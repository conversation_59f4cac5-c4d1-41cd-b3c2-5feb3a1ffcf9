<Location />
    # This is a list of memcache servers which Auth MemCookie
    # should use. 
    # Note that this list must list the same servers as the
    # 'authmemcookie.servers'-option in config.php in the
    # configuration for simpleSAMLphp.
    #
    # The syntax for this option is inherited from: http://docs.libmemcached.org/libmemcached_configuration.html 
    Auth_memCookie_Memcached_Configuration "--SERVER=127.0.0.1:11211"

    # This must be set to 'on' to enable Auth MemCookie for
    # this directory.
    Auth_memCookie_Authoritative on

    # This adjusts the maximum number of data elements in the
    # session data. The default is 10, which can be to low.
    Auth_memCookie_SessionTableSize "40"

    # These two commands are required to enable access control
    # in Apache.
    AuthType Cookie
    AuthName "My Login"

    # This command causes apache to redirect to the given
    # URL when we receive a '401 Authorization Required'
    # error. We redirect to "/simplesaml/module.php/memcookie/auth.php",
    # which initializes a login to the IdP.
    ErrorDocument 401 "/simplesaml/module.php/memcookie/auth.php"
</Location>

<Location /protected>
    # This allows all authenticated users to access the
    # directory. To learn more about the 'Require' command,
    # please look at:
    # http://httpd.apache.org/docs/2.0/mod/core.html#require
    Require valid-user
</Location>

