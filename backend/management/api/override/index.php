<?php
	/** \page api-variables-index Variables de configuration
	 *		- \subpage api-overrides-index-get
	 *		- \subpage api-overrides-index-upd
	 *
	 *	\page api-overrides-index-get Charger la configuration d'un site
	 *	\code
	 *		php index.php --module override --action get
	 *	\endcode
	 *
	 *	Cette fonction retourne la configuration d'un site internet
	 *		\param \-\-tnt_id Obligatoire, identifiant d'un tenant
	 *		\param \-\-wst_id Obligatoire, identifiant d'un site internet
	 *		\param \-\-code Optionnel, code d'une variable de configuration
	 *	
	 *	\return Liste des variables contenant les colonnes :
	 *		- code : Code de la variable
	 *		- value : Valeur de la variable
	 *		- type : Identifiant du type de données (fld_types)
	 *
	 *	\page api-overrides-index-upd Définir une variable pour un site
	 *	\code
	 *		php index.php --module override --action upd --tnt_id={id} --wst_id={id} --code={code} --value={value}
	 *	\endcode
	 *
	 *	Cette fonction permet de définir une variables pour un site
	 *		\param \-\-tnt_id Obligatoire, identifiant d'un tenant
	 *		\param \-\-wst_id Obligatoire, identifiant d'un site internet
	 *		\param \-\-code Optionnel, code d'une variable de configuration
	 *		\param \-\-value Optionnel, nouvelle valeur
	 *	
	 *	\return Liste des variables contenant les colonnes :
	 *		- code : Code de la variable
	 *		- value : Valeur de la variable
	 *		- type : Identifiant du type de données (fld_types)
	 */
	require_once('cfg.variables.inc.php');

	switch($api_action){
		case 'get': {
			if (
				!ria_array_key_exists(array('tnt_id', 'wst_id'), $api_params)
				|| !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0
				|| !is_numeric($api_params['wst_id']) || $api_params['wst_id'] <= 0
			) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			if (!array_key_exists('code', $api_params)) {
				$api_params['code'] = '';
			}

			$r_ovr = cfg_overrides_get($api_params['wst_id'], array(), $api_params['code'], 0, $api_params['tnt_id']);
			if ($r_ovr) {
				while ($ovr = ria_mysql_fetch_assoc($r_ovr)) {
					$api_content[] = $ovr;
				}
			}

			$api_result = true;
			break;
		}
		case 'upd' : {
			if (
				!ria_array_key_exists(array('code', 'value', 'wst_id', 'tnt_id'), $api_params)
				|| !is_numeric($api_params['tnt_id']) || $api_params['tnt_id'] <= 0
				|| !is_numeric($api_params['wst_id']) || $api_params['wst_id'] <= 0
				|| trim($api_params['code']) == ''
			) {
				error_log(__FILE__.':'.__LINE__.' => Paramètres obligatoires omis ou faux');
				die(1);
			}

			if (api_monitoring_var_boolean($api_params['value']) === true) {
				$api_params['value'] = '1';
			}elseif (api_monitoring_var_boolean($api_params['value']) === false) {
				$api_params['value'] = '0';
			}

			if (cfg_overrides_set_value($api_params['code'], $api_params['value'], $api_params['wst_id'], 0, $api_params['tnt_id'])){
				$api_result = true;
			}
			break;
		}
	}