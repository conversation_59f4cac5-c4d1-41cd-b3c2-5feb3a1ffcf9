#!/bin/bash

if [ -z $1 ]; then
	echo "Utilisation :";
	echo "	sh build_new_poc.sh dirname";
	exit 1;
fi

echo "Nom du dossier : $1";
echo "Dossier : /var/www/$1.preprod.riastudio.fr/";
echo "URL : http://$1.preprod.riastudio.fr/";
echo "";

cd /var/www/
echo "copy from beijing (dir)";
rsync -a <EMAIL>:/var/www/$1.maquettes.riastudio.fr .
cd $1.maquettes.riastudio.fr/
unlink tools
ln -s /var/www/riashop.riastudio.fr/tools tools
cd /var/www/$1.maquettes.riastudio.fr/htdocs/ 
unlink sync
unlink admin
ln -s /var/www/riashop.riastudio.fr/htdocs/admin admin
# cd /var/www/$1.maquettes.riastudio.fr/
# echo "import data in MySQL";
# mysql -phNl50sRy -u riashop --default-character-set=utf8 < data-$1.sql
cd /var/www/
mv $1.maquettes.riastudio.fr $1.preprod.riastudio.fr
chown -R www-data:www-data /var/www/$1.preprod.riastudio.fr/tmp
chown -R www-data:www-data /var/www/$1.preprod.riastudio.fr/htdocs/documents
chown -R www-data:www-data /var/www/$1.preprod.riastudio.fr/htdocs/images/products
cd /etc/apache2/vhosts.d/
echo "copy from beijing (vhost)";
rsync -a <EMAIL>:/etc/apache2/vhosts.d/$1.maquettes.riastudio.fr.conf .
mv $1.maquettes.riastudio.fr.conf $1.preprod.riastudio.fr.conf
sed -i 's/maquettes/preprod/g' $1.preprod.riastudio.fr.conf
apache2ctl configtest