<?php
// \cond onlyria

require_once('define.inc.php');
require_once('users.inc.php');
require_once('couchDb.inc.php');
require_once('flow/notifications.inc.php');

/** \defgroup model_notifications Notifications
 *	\ingroup fdv
 *	Ce module comprend les fonctions nécessaires à la gestion des notifications
 *	@{
 */

define( 'NT_TYPE_MANUAL', 1 ); // notification de type manuel
define( 'NT_TYPE_USER_DELETE', 2 ); // notification de suppression utilisateur
define( 'NT_TYPE_CALL_REPORT', 3 ); // notification de rapport d'appel utilisateur
define( 'NT_TYPE_CHECKIN_REPORT', 4 ); // notification de rapport de visite utilisateur
define( 'NT_TYPE_USER_ADD', 5 ); // notification de add utilisateur
define( 'NT_TYPE_USER_UPD', 6 ); // notification de update utilisateur

define( 'NT_STATE_DRAFT', 1 ); // notification en cours d'édition
define( 'NT_STATE_WAITING', 2 ); // notification en cours d'envoi
define( 'NT_STATE_SEND', 3 ); // notification envoyé

/** Cette fonction permet de tester l'existence d'une notification
 *	@param int $id Obligatoire, identifiant de la notifications
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function nt_notification_exists( $id ){

	$results = CouchDB::create()->get(CLS_NOTIFICATIONS, $id);

	if( isset($results['_id']) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}
	return false;
}

/** Cette fonction permet de récupèrer une liste de notifications
 *	@param int $id Facultatif, identifiant de la notification
 *
 *	@return array|bool Les résultats sous forme de tableau en cas de succès ou False en cas d'erreur
 */
function nt_notifications_get( $id ){
	if( !$id ) return false;
	return CouchDB::create()->get(CLS_NOTIFICATIONS, $id);
}

/** Cette fonction permet de récupèrer une liste de notifications
 *	@param int $author_id Facultatif, identifiant de l'auteur
 *	@param array $dates Facultatif, période sur laquelle filtrer le résultat. Le tableau doit contenir une clé start et une clé end
 *	@param string $sort Facultatif, ordre de tri (asc ou desc)
 *	@param int $offset Facultatif, numéro de ligne à partir duquel démarrer le résultat (pagination)
 *	@param int $limit Facultatif, nombre de lignes maximum à retourner (pour la pagination)
 *	@param int $usr_id Facultatif, identifiant de l'utilisateur
 *
 *	@return array|bool Les résultats sous forme de tableau en cas de succès ou False en cas d'erreur
 */
function nt_notifications_get_by_view($author_id=0, $dates=array(), $sort='asc', $offset=0, $limit=0, $usr_id=0) {
	{ // Contrôle des paramètres
		if (!is_numeric($author_id) || $author_id < 0) {
			return false;
		}

		if (!is_array($dates)) {
			return false;
		}

		$p_dates = $dates;
		if (count($dates)) {
			if (!ria_array_key_exists(array('start', 'end'), $dates)) {
				return false;
			}

			$dates['start'] = dateheureparse($dates['start']);
			if (!isdateheure($dates['start'])) {
				return false;
			}

			$dates['end'] = dateheureparse($dates['end']);
			if (!isdateheure($dates['end'])) {
				return false;
			}
		}
	}

	global $config;

	$view_url = 'notifications_by_date';

	if ($usr_id && $author_id) {
		$view_url = 'notifications_by_usr_author_date';
	}else if ($usr_id) {
		$view_url = 'notifications_by_usr_date';
	}else if ($author_id) {
		$view_url = 'notifications_by_date_and_author';
	}

	{ // Prépare les paramètres de requête
		$key_start = $key_end = array($config['tnt_id']);


		if ($usr_id) {
			$key_start[] = $usr_id;
			$key_end[] = $usr_id;
		}

		if ($author_id) {
			$key_start[] = $author_id;
			$key_end[] = $author_id;
		}

		if (count($dates)) {
			if( !isset($dates['start']) || !isdateheure($dates['start']) ){
				$key_start[] = '1000-01-01 00:00:00';
			}else{
				$key_start[] = $dates['start'];
			}

			if( !isset($dates['end']) || !isdateheure($dates['end']) ) {
				$key_end[] = '3000-01-01 00:00:00';
			}else{
				$key_end[] = $dates['end'];
			}
		}

	}

	$call_params = array();
	$call_params['startkey'] = $key_start;
	$call_params['endkey'] = $key_end;
	$call_params['skip'] = $offset;
	$call_params['include_docs'] = true;
	$call_params['descending'] = ($sort == 'asc' ? false : true);

	if ($limit) {
		$call_params['limit'] = $limit;
	}

	$results = CouchDB::create()->getView($view_url, $call_params);
	//On ne récupère que les content et les identifiants de documents dans CouchDb
	$final = array();
	if( $results && sizeof($results) ){
		foreach ($results as $r) {
			$tmp = $r['doc']['content'];
			$tmp['_id'] = $r['doc']['_id'];

			$final[] = $tmp;
		}
	}

	//Puis le nombre de documents
	if (sizeof($final)) {
		$final['total_rows'] = nt_notifications_get_count($author_id, $p_dates);
	} else {
		$final['total_rows'] = 0;
	}

	return $final;
}

function nt_notifications_get_count($author_id=0, $dates=array()) {
	{ // Contrôle des paramètres
		if (!is_numeric($author_id) || $author_id < 0) {
			return false;
		}

		if (!is_array($dates)) {
			return false;
		}

		if (count($dates)) {
			if (!ria_array_key_exists(array('start', 'end'), $dates)) {
				return false;
			}

			$dates['start'] = dateheureparse($dates['start']);
			if (!isdateheure($dates['start'])) {
				return false;
			}

			$dates['end'] = dateheureparse($dates['end']);
			if (!isdateheure($dates['end'])) {
				return false;
			}
		}
	}

	global $config;

	$view = 'notifications_by_date';
	if ($author_id) {
		$view = 'notifications_by_date_and_author';
	}

	{ // Prépare les paramètres de requête
		$key_start = $key_end = array($config['tnt_id']);

		if ($author_id) {
			$key_start[] = $author_id;
			$key_end[] = $author_id;
		}

		if (count($dates)) {
			$key_start[] = $dates['start'];
			$key_end[] = $dates['end'];
		}
	}

	$call_params = array();
	$call_params['startkey'] = $key_start;
	$call_params['endkey'] = $key_end;


	return CouchDB::create()->getViewCount($view, $call_params);
}

/** Cette fonction permet d'ajouter une notification qui sera envoyée à un ou plusieurs devices Yuto.
 * 	L'envoi effectif vers les appareils est réalisée par un worker, de façon asynchrone. C'est ce worker
 *	qui passera la notification de l'état de brouillon (NT_STATE_DRAFT) à l'état "En cours d'envoi" (NT_STATE_WAIT)
 *	puis à l'état Envoyé (NT_STATE_SEND) une fois terminé.
 *	@param int $type_id Obligatoire, type de la notification, manuel, auto ou autre
 *	@param int $author_id Facultatif, auteur de la notification
 *	@param string $title Facultatif, titre de la notification
 *	@param string $desc Facultatif, message de la notification
 *	@param string $date_created Optionnel, date de création de la notification (par défaut il s'agit de maintenant)
 *	@return int l'identifiant de la notification en cas de succès
 *	@return bool False en cas d'échec
 *	@see nt_notifications_send
 */
function nt_notifications_add( $type_id, $author_id=0, $title='', $desc='', $date_created=false){

	if( !is_numeric($type_id) ){
		return false;
	}
	if( $author_id > 0 && !gu_users_exists( $author_id ) ){
		return false;
	}

	$date_created = $date_created && isdateheure($date_created) ? $date_created : dateheureparse(date('Y-m-d H:i:s'));

	$values = array();

	// récupère des informations complémentaires pour les stockers sur le couchdb
	if( $author_id > 0 ){
		$rauthor = gu_users_get($author_id);
		if( !$rauthor ) return false;
		$author = ria_mysql_fetch_assoc($rauthor);

		$values['nt_author_ref'] = $author['ref'];
		$values['nt_author_name'] = trim($author['adr_firstname'].' '.$author['adr_lastname'].' '.$author['society']);
		$values['nt_author_email'] = $author['email'];
	}

	// ajout des datas normales
	$values['nt_state'] = NT_STATE_DRAFT;
	$values['nt_author_id'] = $author_id;
	$values['nt_type_id'] = $type_id;
	$values['nt_title'] = $title;
	$values['nt_desc'] = $desc;
	$values['nt_date_created'] = $date_created;

	$results = CouchDB::create()->add(CLS_NOTIFICATIONS, $values);

	if( isset($results['ok']) ){
		return $results['id'];
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}
	return false;
}

/** Cette fonction permet de mettre à jour une ligne d'une notification
 *	@param int $id : Identifiant de la notification à mettre à jour
 *	@param int $type_id Obligatoire, type de la notification, manuel, auto ou autre
 *	@param int $author_id Facultatif, identifiant de l'auteur
 *	@param string $title Facultatif, titre de la notification
 *	@param string $desc Facultatif, message de la notification
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function nt_notifications_upd( $id, $type_id, $author_id=0, $title='', $desc='' ){
	if( !nt_notification_exists( $id ) ){
		return false;
	}
	if( $author_id > 0 && !gu_users_exists( $author_id ) ){
		return false;
	}

	global $config;

	$values = CouchDB::create()->get(CLS_NOTIFICATIONS, $id);

	if( !isset($values['_id']) ){
		return false;
	}

	if( $values['nt_author_id'] != $author_id ){
		if( $author_id > 0 ){
			$rauthor = gu_users_get($author_id);
			if( !$rauthor ) return false;
			$author = ria_mysql_fetch_assoc($rauthor);

			$values['nt_author_ref'] = $author['ref'];
			$values['nt_author_name'] = trim($author['adr_firstname'].' '.$author['adr_lastname'].' '.$author['society']);
			$values['nt_author_email'] = $author['email'];
		}
	}

	$values['nt_type_id'] = $type_id;
	$values['nt_title'] = $title;
	$values['nt_desc'] = $desc;

	if( CouchDB::create()->update(CLS_NOTIFICATIONS, $id, $values) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' erreur de mise à jour '.print_r($values, true));
	}

	return false;
}

/** Cette fonction permet de supprimer une notification. La suppression est réalisée de façon virtuelle (corbeille)
 * 	@param int $id Obligatoire, identifiant de la notification à supprimer
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function nt_notification_del( $id ){
	if( !nt_notification_exists( $id ) ){
		return false;
	}

	$results = CouchDB::create()->delete(CLS_NOTIFICATIONS, $id);

	if( isset($results['ok']) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}

	return false;
}

/** Cette fonction permet de modifier les destinataires de la notifications
 *	@param int $id : Identifiant de la notification à mettre à jour
 *	@param $usr_ids Facultatif, table de la liste de users pour lequels envoyer la notification
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec, Attention lors qu'une notification est à l'état envoyé ou en cours d'envoi il n'est plus possible de modifier ce paramètre
 */
function nt_notifications_set_users( $id, $usr_ids=array() ){
	if( !nt_notification_exists( $id ) ){
		return false;
	}

	global $config;

	$usr_ids = control_array_integer($usr_ids);

	// récupère le doc actuel
	$values = CouchDB::create()->get(CLS_NOTIFICATIONS, $id);
	$values['users'] = $usr_ids;

	// mise à jour du doc
	if( CouchDB::create()->update(CLS_NOTIFICATIONS, $id, $values) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($values, true));
	}

	return false;
}

/**	Cette fonction permet la suppression d'une notification
 *	@param int $id : Identifiant de la notification à supprimer
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function nt_notifications_del($id){
	if( !nt_notification_exists( $id ) ){
		return false;
	}


	return CouchDB::create()->delete(CLS_NOTIFICATIONS, $id);
}

/** Cette fonction permet de mettre a jour la date de modification
 *	@param int $id Obligatoire : identifiant de la notification
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function nt_notifications_set_date_modified($id){

	$results = CouchDB::create()->get(CLS_NOTIFICATIONS, $id);

	if( !isset($results['_id']) ){
		return false;
	}

	if( CouchDB::create()->update(CLS_NOTIFICATIONS, $id, $results) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}
}

/**	Cette fonction permet l'envoi sur les serveurs Google/Apple des notifications destinées aux appareils Yuto
 *	@param int $id Obligatoire, Identifiant de la notification a envoyer
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function nt_notifications_send( $id ){
	if( !nt_notification_exists( $id ) ){
		return false;
	}

	$notification = CouchDB::create()->get(CLS_NOTIFICATIONS, $id);
	if( !$notification ) {
		return false;
	}

	// changement du status de la notification pour la passer en cours d'envoi
	$notification['nt_state'] = NT_STATE_WAITING;
	if( !CouchDB::create()->update(CLS_NOTIFICATIONS, $id, $notification) ){
		return false;
	}

	if( !isset($notification['users']) || !sizeof($notification['users']) ){
		error_log(__FILE__.':'.__LINE__." Notification sans utilisateurs : ".$id);
		return false;
	}

	$sending = array(); // tableau de donnée pour la liste des envois effectué

	foreach( $notification['users'] as $usr ){
		if( is_numeric($usr) ){

			// récupère l'ensemble des tablettes pour chaque utilisateur
			$rdev = dev_devices_get(0, $usr, '', -1, '=', false, false, true);
			if( $rdev && ria_mysql_num_rows($rdev) ){
				while( $dev = mysql_fetch_assoc($rdev) ){

					$result = dev_send_notifications($dev['id'], '', '', array('nt_id' => $notification['_id'], 'cls_id' => CLS_NOTIFICATIONS));
					if( $result ){

						$sending[] = array(
							'notify_token' => $dev['notify_token'],
							'notify_date' => date('Y-m-d H:m:i'),
							'notify_response' => $result,
							'notify_device_id' =>  $dev['id']
						);

					}


				}
			}

		}
	}

	// mise à jour du document pour l'envoi avec la log
	$notification['sending'] = $sending;
	$notification['nt_state'] = NT_STATE_SEND;
	if( !CouchDB::create()->update(CLS_NOTIFICATIONS, $id, $notification) ){
		return false;
	}

	return true;
}

/** Cette fonction permet récupérer les objets lié à une notifications
 * 	@param $nt_id Obligatoire : identifiant de la notification
 * 	@param int $cls_id Obligatoire : identifiant de classe
 * 	@param int $obj_id_0 Obligatoire : identifiant de l'objet 0
 * 	@param int $obj_id_1 Obligatoire : identifiant de l'objet 1
 * 	@param int $obj_id_2 Obligatoire : identifiant de l'objet 2
 *
 *	@return resource Un résultat MySQL contenant :
 *				- rpr_id : identifiant du rapport
 *				- cls_id : identifiant de classe
 *				- obj_id_0 : identifiant de l'objet 0
 *				- obj_id_1 : identifiant de l'objet 1
 *				- obj_id_2 : identifiant de l'objet 2
 */
function nt_notifications_objects_get( $nt_id=0, $cls_id=0, $obj_id_0=0, $obj_id_1=0, $obj_id_2=0 ){
	if( $nt_id > 0 && !nt_notification_exists( $nt_id ) ){
		return false;
	}
	if( !is_numeric($cls_id) || $cls_id < 0 ){
		return false;
	}
	if( !is_numeric($obj_id_0) || $obj_id_0 < 0 ){
		return false;
	}
	if( !is_numeric($obj_id_1) || $obj_id_1 < 0 ){
		return false;
	}
	if( !is_numeric($obj_id_2) || $obj_id_2 < 0 ){
		return false;
	}

	$values = CouchDB::create()->get(CLS_NOTIFICATIONS, $nt_id);

	if( !isset($values['_id']) ){
		return false;
	}

	if( !isset($values['objects']) || !is_array($values['objects']) ){
		return false;
	}

	$final_obj = array();
	foreach( $values['objects'] as $obj ){
		if( !isset($obj['cls_id']) ) continue;
		if( !isset($obj['obj_id_0']) ) continue;
		if( !isset($obj['obj_id_1']) ) continue;
		if( !isset($obj['obj_id_2']) ) continue;

		if( $cls_id > 0 && $obj['cls_id'] != $cls_id ){
			continue;
		}
		if( $obj_id_0 > 0 && $obj['obj_id_0'] != $obj_id_0 ){
			continue;
		}
		if( $obj_id_1 > 0 && $obj['obj_id_1'] != $obj_id_1 ){
			continue;
		}
		if( $obj_id_2 > 0 && $obj['obj_id_2'] != $obj_id_2 ){
			continue;
		}

		$final_obj[] = $obj;
	}

	return $final_obj;
}

/** Cette fonction permet d'ajouter une ligne d'objet pour une notification
 * 	@param $nt_id Obligatoire : identifiant du rapport
 * 	@param int $cls_id Obligatoire : identifiant de classe
 * 	@param int $obj_id_0 Obligatoire : identifiant de l'objet 0
 * 	@param int $obj_id_1 Obligatoire : identifiant de l'objet 1
 * 	@param int $obj_id_2 Obligatoire : identifiant de l'objet 2
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function nt_notification_objects_add( $nt_id, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2 ){
	if( !is_numeric($cls_id) || $cls_id < 0 ) return false;
	if( !is_numeric($obj_id_0) || $obj_id_0 < 0 ) return false;
	if( !is_numeric($obj_id_1) || $obj_id_1 < 0 ) return false;
	if( !is_numeric($obj_id_2) || $obj_id_2 < 0 ) return false;

	$tmp = nt_notifications_objects_get( $nt_id, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2 );
	if( $tmp && sizeof($tmp) ){
		return true;
	}
	global $config;

	$obj_to_add = array(
		'cls_id' => $cls_id,
		'obj_id_0' => $obj_id_0,
		'obj_id_1' => $obj_id_1,
		'obj_id_2' => $obj_id_2,
	);

	// récupère le doc
	$values = CouchDB::create()->get(CLS_NOTIFICATIONS, $nt_id);

	if( !isset($values['_id']) ){
		return false;
	}

	// si aucun objet n'a été ajouté précédement on créer la clé
	if( !isset($values['objects']) || !is_array($values['objetcs']) ){
		$values['objects'] = array();
	}

	$values['objects'][] = $obj_to_add;

	return CouchDB::create()->update(CLS_NOTIFICATIONS, $nt_id, $values);
}

/** Cette fonction permet de supprimer une ligne d'objet pour une notification
 * 	@param $nt_id Obligatoire : identifiant de la notification
 * 	@param int $cls_id Obligatoire : identifiant de classe
 * 	@param int $obj_id_0 Obligatoire : identifiant de l'objet 0
 * 	@param int $obj_id_1 Obligatoire : identifiant de l'objet 1
 * 	@param int $obj_id_2 Obligatoire : identifiant de l'objet 2
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function nt_notification_objects_del( $nt_id, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2 ){
	if( !is_numeric($cls_id) || $cls_id < 0 ) return false;
	if( !is_numeric($obj_id_0) || $obj_id_0 < 0 ) return false;
	if( !is_numeric($obj_id_1) || $obj_id_1 < 0 ) return false;
	if( !is_numeric($obj_id_2) || $obj_id_2 < 0 ) return false;

	$tmp = nt_notifications_objects_get( $nt_id, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2 );
	if( $tmp && sizeof($tmp) ){
		return true;
	}
	global $config;

	// récupère le doc
	$values = CouchDB::create()->get(CLS_NOTIFICATIONS, $nt_id);

	if( !isset($values['objects']) || !is_array($values['objetcs']) ){
		return false;
	}

	// parcours les objets pour trouver celui à supprimer
	$final_obj = array();
	foreach( $values['objects'] as $key => $obj ){
		if( $obj['cls_id'] == $cls_id
	 		&& $obj['obj_id_0'] == $obj_id_0
	 		&& $obj['obj_id_1'] == $obj_id_1
	 		&& $obj['obj_id_2'] == $obj_id_2
 			){

			unset($values['objects'][$key]);
		}
	}

	// mise à jour du doc
	return CouchDB::create()->update(CLS_NOTIFICATIONS, $nt_id, $values);
}

/** Vérifie le type de notification et envoie un mail aux ADV si nécessaire
 * 	@param int $notification_id Obligatoire, identifiant de la notification
 *	@return bool true en cas de succès, false en cas d'erreur
 */<
function nt_notification_notify( $notification_id ){
    	global $config;
	if( !$notification_id ){
		return false;
	}
	
	$notification = nt_notifications_get($notification_id);
	if( !$notification ){ //si la requete n'a pas fonctionné ou si le vurseur est vide
		return false;
	}
	$http_host_ria = $config['backoffice_url'];
	switch($notification['nt_type_id']){
		case NT_TYPE_USER_DELETE /* Suppression d'un utilisateur */: {

			// Récupère les infos sur l'utilisateur de la tablette
			$rusr_device_owner = gu_users_get($notification['nt_author_id']/* id de l'utilisateur de la tablette */);
			if( !$rusr_device_owner || !ria_mysql_num_rows($rusr_device_owner) ){ //si la requete n'a pas fonctionné ou si le vurseur est vide
				return false;
			}
			$usr_device_owner = ria_mysql_fetch_array($rusr_device_owner);


			// Récupère les infos de l'utilisateur qui a été supprimé
			$rnotification_objects = nt_notifications_objects_get($notification['_id'], CLS_USER);
			if( !$rnotification_objects || !sizeof($rnotification_objects) ){
				return false;
			}
			$notification_objects = $rnotification_objects[0];

			$rusr_deleted = gu_users_get($notification_objects['obj_id_0']/* id de l'utilisateur supprimé */);
			if( !$rusr_deleted ||  !ria_mysql_num_rows($rusr_deleted) ){ //si la requete n'a pas fonctionné ou si le vurseur est vide
				return true; // dans le cas ou l'utilisateur n'ai pas trouvable alors on considére celui ci déjà supprimé
			}
			$usr_deleted = ria_mysql_fetch_array($rusr_deleted);

			// Vérifie qu'une configuration existe pour cette alerte email (adresses expéditrices et destinatrices).
			// Si aucune configuration n'existe, des valeurs par défaut sont utilisées.
			$rcfg = cfg_emails_get('notify-usr-del');

			$cfg = false;
			if( $rcfg && ria_mysql_num_rows($rcfg) ){
				$cfg = ria_mysql_fetch_array($rcfg);
			}

			if( !$cfg || !$cfg['to'] ){
				$cfg[ 'from' ] = '<EMAIL>';
				// S'il n'y a pas de configuration, tous les administrateurs recevront l'alerte
				$rto = gu_users_get( 0, '', '', PRF_ADMIN );
				$emails = array();
				while( $to = mysql_fetch_array($rto) ){
					if( gu_users_is_tenant_linked($to['id']) ){
						$emails[] = $to['email'];
					}
				}
				if( !sizeof($emails) ){ // On ne peut pas envoyer d'alerte si aucun destinataire
					return false;
				}
				$cfg[ 'to' ] = $emails;
				$cfg[ 'cc' ] = '';
				$cfg[ 'bcc' ] = '';
			}

			$tmp = array('to','cc','bcc');
			foreach( $tmp as $t ){
				if( !is_array($cfg[$t]) ){
					$cfg[$t] = array($cfg[$t]);
				}
				$idx = array_search($usr_device_owner['email'], $cfg[$t]);
				if(  $idx !== false ){
					unset($cfg[$t][$idx]);
				}
			}

			$email = new Email();
			$email->setFrom( $cfg['from'] );
			$email->addTo( $cfg['to'] );

			if( $cfg['cc'] ){
				$email->addCc( $cfg['cc'] );
			}

			if( $cfg['bcc'] ){
				$email->addBcc( $cfg['bcc'] );
			}

			if( isset($cfg['reply-to']) && trim($cfg['reply-to']) ){
				$email->setReplyTo( $cfg['reply-to'] );
			}

			$email->setSubject( "Suppression d'un utilisateur" );

			$message = '
			<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
			<html>
			<head>
				<title>Suppression d\'un utilisateur</title>
			</head>
			<body style="margin: 0; padding; 0; text-align: center; font-family: Helvetica;">

			<div style="background-color: #FAFAFA; padding: 5px;">
				<img src="https://'.$http_host_ria.'/admin/images/logo-yuto-email.png" width="300" height="133" />
			</div>
			<div style="color: black; background-color: white;">
				<h1 style="font-size: 28px;font-weight: bold;line-height: 150%;"><a href="https://'.$http_host_ria.'/admin/customers/edit.php?usr='.$usr_device_owner['id'].'">'.htmlspecialchars( $usr_device_owner['adr_firstname'].' '.$usr_device_owner['adr_lastname'] ).'</a> a demandé la suppression du contact <a href="https://'.$http_host_ria.'/admin/customers/edit.php?usr='.$usr_deleted['id'].'">'.htmlspecialchars( trim( $usr_deleted['adr_firstname'].' '.$usr_deleted['adr_lastname'] ) ).'</a>';

			// Précision du compte parent
			$usr_parent = gu_users_get_parent_id( $usr_deleted['id'] );
			if( $usr_parent!==false ){
				$rparent = gu_users_get( $usr_parent );
				if( mysql_num_rows($rparent) ){
					$parent = mysql_fetch_array($rparent);
					$message .= ', du compte ';
					$message .= '<a href="https://'.$http_host_ria.'/admin/customers/edit.php?usr='.$parent['id'].'">';
					if( $parent['society'] ){
						$message .= htmlspecialchars( $parent['society'] );
					}elseif( $parent['adr_firstname'] || $parent['adr_lastname'] ){
						$message .= htmlspecialchars( trim( $parent['adr_firstname'].' '.$parent['adr_lastname'] ) );
					}
					$message .= '</a>';
				}
			}

			$message .= '</h1>';

			if( $notification['nt_desc']!='' ){
				$message .= '<p>En voici la raison :</p>';
				$message .= '<p><i>'.htmlspecialchars( $notification['nt_desc'] ).'</i></p>';
			}

			$message .= '<p style="padding: 20px 0 10px 0;">Pour accepter la demande, vous pouvez procéder à la suppression du contact depuis votre gestion commerciale. Sans action de votre part, cette demande sera ignorée.</p>
			</div>
			<div style="color: white; background-color: #444444; padding: 25px; margin-top: 30px;">
				<p>Notre équipe technique est disponible pour répondre à vos questions par mail sur <a href="mailto:<EMAIL>" style="color: white;"><EMAIL></a> ou par téléphone au <b>05 47 74 81 87</b>.</p>
			</div>

			</body>
			</html>';

			$email->setHtmlMessage($message);
			return $email->send();
		}
	}
	return false;
}

/// @}

// \endcond
