<?xml version="1.0" encoding="utf-8" ?>
<phpunit bootstrap="tests/bootstrap.php">
    <testsuites>
        <testsuite name="The project's test suite">
            <directory>tests/</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./lib</directory>
        </whitelist>
    </filter>
    <logging>
        <log type="coverage-text" target="php://stdout" showUncoveredFiles="true" />
        <log type="coverage-html" target="build/coverage" title="PHP Coveralls" charset="UTF-8" yui="true" highlight="true" lowUpperBound="35" highLowerBound="70" />
        <log type="coverage-clover" target="build/logs/clover.xml" />
    </logging>
</phpunit>

