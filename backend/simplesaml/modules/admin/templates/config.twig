{% set pagetitle = 'SimpleSAMLphp installation page'|trans %}
{% set frontpage_section = 'main' %}
{% extends "base.twig" %}

{% block content %}
    {%- include "@admin/includes/menu.twig" %}

    {%- for key, warning in warnings %}
      {%- if warning is iterable %}

    <div class="message-box warning">{{ warning[0]|trans(warning[1])|raw }}</div>
      {%- else %}

    <div class="message-box warning">{{ warning|trans|raw }}</div>
      {%- endif %}
    {%- endfor %}

    <div class="message-box">
      {% trans %}SimpleSAMLphp is installed in:{% endtrans %}
      <kbd>{{ directory }}</kbd><br/>
      {% trans %}You are running version <kbd>{{ version }}</kbd>.{% endtrans %}
    </div>
    <h2>{% trans %}Configuration{% endtrans %}</h2>
    <div class="enablebox mini">
      <table>
        <tr class="{%- if enablematrix.saml20idp %}enabled{% else %}disabled{% endif -%}">
          <td>SAML 2.0 IdP</td>
          <td><i class="fa fa-{%- if enablematrix.saml20idp %}check{% else %}ban{% endif %}"></i></td>
        </tr>
        <tr class="{%- if enablematrix.shib13idp %}enabled{% else %}disabled{% endif -%}">
          <td>Shib 1.3 IdP</td>
          <td><i class="fa fa-{%- if enablematrix.shib13idp %}check{% else %}ban{% endif %}"></i></td>
        </tr>
      </table>
    </div>
    <ul>
      {%- for key, link in links %}

      <li><a href="{{ link.href }}">{{ link.text|trans }}</a></li>
      {%- endfor %}

    </ul>
    <h2>{% trans %}Checking your PHP installation{% endtrans %}</h2>
    <div class="enablebox">
        <table>
        {%- for key, func in funcmatrix %}

          <tr class="{%- if func.enabled %}enabled{% else %}disabled{% endif -%}">
            <td><i class="fa fa-{%- if func.enabled %}check{% else %}ban{% endif -%}"></i></td>
            <td>
            {%- if func.required == 'required' %}
              {%- trans %}required{% endtrans %}
            {%- else %}
              {%- trans %}optional{% endtrans %}
            {%- endif -%}
            </td>
            <td>
              {%- if func.descr is iterable -%}
                {{ func.descr[0]|trans(func.descr[1]|raw) }}
              {%- else -%}
                {{ func.descr|trans|raw }}
              {%- endif -%}
            </td>
          </tr>
        {%- endfor %}

        </table>
    </div>
{% endblock %}
