{"name": "simplesamlphp/simplesamlphp-module-authtwitter", "description": "A module that is able to perform authentication against Twitter", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "twitter"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload": {"psr-4": {"SimpleSAML\\Module\\authtwitter\\": "lib/"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.5", "simplesamlphp/composer-module-installer": "~1.0", "simplesamlphp/simplesamlphp-module-oauth": "^0.9"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "phpunit/phpunit": "~4.8.35"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-authtwitter/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-authtwitter"}}