
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: lt\n"
"Language-Team: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"(n%100<10 || n%100>=20) ? 1 : 2)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{cron:cron:cron_report_title}"
msgstr "Cron ataskaita"

msgid "{cron:cron:ran_text}"
msgstr "Cron vydytas"

msgid "{cron:cron:cron_suggestion}"
msgstr "Štai pasiūlymas crontab failui:"

msgid "{cron:cron:run_text}"
msgstr "Vykdyti cron:"

msgid "{cron:cron:cron_execution}"
msgstr "Spragtelkite čia pradėti cron funkcijų vykdymą:"

msgid "{cron:cron:cron_info}"
msgstr "Cron tai priemonė reguliariai vykdyti užduotis unix sistemose."

msgid "{cron:cron:cron_result_title}"
msgstr "Štai cron funkcijų vykdymo rezultatas:"

msgid "{cron:cron:cron_header}"
msgstr "Cron rezultatų puslapis"

msgid "Cron is a way to run things regularly on unix systems."
msgstr "Cron tai priemonė reguliariai vykdyti užduotis unix sistemose."

msgid "Run cron:"
msgstr "Vykdyti cron:"

msgid "Cron ran at"
msgstr "Cron vydytas"

msgid "Cron report"
msgstr "Cron ataskaita"

msgid "Here is a suggestion for a crontab file:"
msgstr "Štai pasiūlymas crontab failui:"

msgid "Click here to run the cron jobs:"
msgstr "Spragtelkite čia pradėti cron funkcijų vykdymą:"

msgid "Here are the result for the cron job execution:"
msgstr "Štai cron funkcijų vykdymo rezultatas:"

msgid "Cron result page"
msgstr "Cron rezultatų puslapis"

