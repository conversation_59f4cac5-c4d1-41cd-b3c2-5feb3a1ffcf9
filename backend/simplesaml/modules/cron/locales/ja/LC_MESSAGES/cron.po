
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: ja\n"
"Language-Team: \n"
"Plural-Forms: nplurals=1; plural=0\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{cron:cron:cron_report_title}"
msgstr "Cronレポート"

msgid "{cron:cron:ran_text}"
msgstr "Cronを実行した時刻"

msgid "{cron:cron:cron_suggestion}"
msgstr "crontabファイルの提案:"

msgid "{cron:cron:run_text}"
msgstr "cronを実行"

msgid "{cron:cron:cron_execution}"
msgstr "クリックしてcronジョブを実行:"

msgid "{cron:cron:cron_info}"
msgstr "Cronはunixシステムで定期的に処理を実行します。"

msgid "{cron:cron:cron_result_title}"
msgstr "cronジョブの実行結果:"

msgid "{cron:cron:cron_header}"
msgstr "Cron実行結果ページ"

msgid "Cron is a way to run things regularly on unix systems."
msgstr "Cronはunixシステムで定期的に処理を実行します。"

msgid "Run cron:"
msgstr "cronを実行"

msgid "Cron ran at"
msgstr "Cronを実行した時刻"

msgid "Cron report"
msgstr "Cronレポート"

msgid "Here is a suggestion for a crontab file:"
msgstr "crontabファイルの提案:"

msgid "Click here to run the cron jobs:"
msgstr "クリックしてcronジョブを実行:"

msgid "Here are the result for the cron job execution:"
msgstr "cronジョブの実行結果:"

msgid "Cron result page"
msgstr "Cron実行結果ページ"

