{% set pagetitle = 'Enter your username and password'|trans %}

{% extends "@core/base.twig" %}

{% block postload %}
    <script src="/{{baseurlpath}}module.php/core/assets/js/loginuserpass.js"></script>
{% endblock %}

{% block content %}
    {%- if not isProduction %}

    <div class="message-box warning">
      {% trans %}You are now accessing a pre-production system. This authentication setup
      {#- #} is for testing and pre-production verification only. If someone sent you
      {#- #} a link that pointed you here, and you are not <i>a tester</i> you
      {#- #} probably got the wrong link, and should <b>not be here</b>.{% endtrans %}
    </div>
    {% endif -%}
    {% if errorcode -%}
    <div class="pure-g">
        <div class="pure-u-1">
            <div class="message-box error">

                {% set errtitles = errorcodes['title'] %}
                {% set errtitle = errtitles[errorcode] %}

                <h3>{{ errtitle|trans(errorparams) }}</h3>

                {% set errdescs = errorcodes['descr'] %}
                {% set errdesc = errdescs[errorcode] %}

                <p>{{ errdesc|trans(errorparams) }}</p>

            </div>
        </div>
    </div>
    {%- endif %}

    <h1>{{ 'Enter your username and password'|trans }}</h1>

    <p>{{ 'A service has requested you to authenticate yourself. Please enter your username and password in the form below.'|trans }}</p>
    <br>
    <div class="center">
        <form class="pure-form pure-form-aligned center-form" action="?" method="post" name="f">
            <div class="form-align">

                <div class="pure-control-group">
                    <label for="username">{{ 'Username'|trans }}</label>
                    <input id="username" {{ forceUsername ? 'disabled="disabled"' }} placeholder="{{ username }}" type="text" name="username" class="edge"
            {%- if not forceUsername %} tabindex="1" value="{{ username }}" {% endif %}
            {%- if not forceUsername and not username %} autofocus {% endif %} />
                {% if rememberUsernameEnabled and not forceUsername -%}
                </div>
                <div class="pure-controls pure-form-message">
                    <label for="remember_username" class="pure-checkbox">
                        <input id="remember_username" type="checkbox" tabindex="4"
                                {{ rememberUsernameChecked ? 'checked="checked"' }} name="remember_username" value="Yes"/>
                        <small>{{ 'Remember my username'|trans }}</small>
                    </label>
                {%- endif %}
                </div>

                <div class="pure-control-group">
                    <label for="password">{{ 'Password'|trans}}</label>
                    <input id="password" type="password" tabindex="2" name="password" class="edge" autocomplete="current-password"
                    {%- if forceUsername or username %} autofocus {% endif %} >

                {% if rememberMeEnabled -%}
                </div>
                <div class="pure-controls pure-form-message">
                    <label for="remember_me" class="pure-checkbox">
                    <input id="remember_me" type="checkbox" tabindex="5"
                            {{ rememberMeChecked ? 'checked="checked"' }} name="remember_me" value="Yes"/>
                        <small>{{ 'Remember me'|trans }}</small>
                    </label>
                {%- endif %}
                </div>
                {% if organizations is defined -%}
                <div class="pure-control-group">
                    <label for="organization">{{ 'Organization'|trans }}</label>
                    <div class="pure-select right pure-input-1-2 pure-input-sm-1-1">
                        <select name="organization" class="selectize" id="organization" tabindex="3">
                            {{ selectedOrg ?: null }}
                            {%- for id, orgDesc in organizations -%}
                                {% if id == selectedOrg -%}
                                    {%- set selected = 'selected="selected"' %}
                                {%- else -%}
                                    {% set selected = '' -%}
                                {% endif -%}
                                {% if orgDesc -%}
                                    <option value="{{ id }}" {{ selected }}>{{ orgDesc|trans }}</option>
                                {% endif -%}
                            {% endfor %}
                        </select>
                    </div>
                    {% if rememberOrganizationEnabled is defined -%}
                    <div class="pure-controls pure-form-message">
                        <label for="remember_organization" class="pure-checkbox">
 	                    <input type="checkbox" id="remember_organization" tabindex="5" name="remember_organization" value="Yes"
                                {{ rememberOrganizationChecked ? 'checked="checked"' }} />
                            <small>{{ 'Remember my organization'|trans }}</small>
                        </label>
                    </div>
                    {%- endif %}
                </div> <!--pure-control-group-->
            {%- endif %}

            </div> <!-- form-align-->
            <br><br>
            {%- for name, value in stateparams %}

            <input type="hidden" name="{{ name }}" value="{{ value }}"/>
            {%- endfor %}

            <button class="pure-button pure-button-red pure-input-1-2 pure-input-sm-1-1 right" id="submit_button"
                    type="submit" tabindex="6" data-processing="{% trans %}Processing...{% endtrans %}">
              {% trans %}Log in{% endtrans %}
            </button>
        </form>
    </div><!--center-->
    {% if links -%}
    <ul>
        {% for link in links -%}
        <li><a href="{{ link.href }}">{{ link['text']|trans }}</a></li>
        {% endfor %}
    </ul>
    {%- endif %}
{% endblock %}
