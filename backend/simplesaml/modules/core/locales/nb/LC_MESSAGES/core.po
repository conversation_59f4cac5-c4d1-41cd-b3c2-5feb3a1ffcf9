
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: nb_NO\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{core:frontpage:link_phpinfo}"
msgstr "PHPinfo"

msgid "{core:no_state:report_text}"
msgstr "Hvis problemet vedvarer, kan du rapportere det til systemadministratorene."

msgid "{core:no_state:cause_backforward}"
msgstr "Bruk av \"frem\"- og \"tilbake\"-knappene i nettleseren."

msgid "{core:no_metadata:not_found_for}"
msgstr "Vi fant ikke metadataene for:"

msgid "{core:frontpage:link_shib13example}"
msgstr ""
"Shibboleth 1.3 SP eksempel - test innlogging med Shibboleth 1.3 via din "
"IdP"

msgid "{core:no_state:suggestions}"
msgstr "Forslag for å løse dette problemet:"

msgid "{core:frontpage:login_as_admin}"
msgstr "Login som administrator"

msgid "{core:short_sso_interval:warning}"
msgstr ""
"Vi har detektert at det er bare noen få sekunder siden du sist ble "
"autentisert på denne tjenesten, og derfor antar vi at det er et problem "
"med oppsettet av denne tjenesten."

msgid "{core:frontpage:link_doc_sp}"
msgstr "Bruk SimpleSAMLphp som Service Provider"

msgid "{core:frontpage:link_meta_saml2sphosted}"
msgstr "Hosted SAML 2.0 Service Provider Metadata (automatisk generert)"

msgid "{core:frontpage:link_openidprovider}"
msgstr "OpenID Provider side - Alpha versjon (testkode)"

msgid "{core:frontpage:link_doc_install}"
msgstr "Installerer SimpleSAMLphp"

msgid "{core:frontpage:link_diagnostics}"
msgstr "Diagnostiser hostnavn, port og protokoll"

msgid "{core:no_state:suggestion_goback}"
msgstr "Gå tilbake til forrige side og prøv på nytt."

msgid "{core:no_state:causes}"
msgstr "Denne feilen kan være forårsaket av:"

msgid "{core:frontpage:link_meta_saml2idphosted}"
msgstr "Hosted SAML 2.0 Identity Provider Metadata (automatisk generert)"

msgid "{core:frontpage:optional}"
msgstr "Valgfritt"

msgid "{core:frontpage:link_meta_shib13sphosted}"
msgstr "Hosted Shibboleth 1.3 Service Provider Metadata (automatisk generert)"

msgid "{core:frontpage:doc_header}"
msgstr "Dokumentasjon"

msgid "{core:frontpage:link_doc_advanced}"
msgstr "SimpleSAMLphp avansert funksjonalitet"

msgid "{core:frontpage:required_ldap}"
msgstr "Påkrevd for LDAP"

msgid "{core:frontpage:authtest}"
msgstr "Test konfigurerte autentiseringskilder"

msgid "{core:frontpage:link_meta_overview}"
msgstr ""
"Oversikt over metadata for din installasjon. Diagnostiser metadatafilene "
"her."

msgid "{core:frontpage:configuration}"
msgstr "Konfigurasjon"

msgid "{core:frontpage:welcome}"
msgstr "Velkommen"

msgid "{core:frontpage:link_doc_shibsp}"
msgstr ""
"Konfigurer Shibboleth 1.3 SP for å fungere sammen med SimpleSAMLphp "
"identitetstjeneste"

msgid "{core:no_state:header}"
msgstr "Tilstandsinformasjon tapt"

msgid "{core:frontpage:metadata_header}"
msgstr "Metadata"

msgid "{core:frontpage:link_doc_maintenance}"
msgstr "SimpleSAMLphp vedlikehold og konfigurasjon"

msgid "{core:frontpage:link_configcheck}"
msgstr "Sjekk av SimpleSAMLphp konfigurasjonsfiler"

msgid "{core:frontpage:page_title}"
msgstr "SimpleSAMLphp installasjonsside"

msgid "{core:no_cookie:header}"
msgstr "Mangler informasjonskapsel"

msgid "{core:frontpage:warnings}"
msgstr "Advarsler"

msgid "{core:frontpage:link_xmlconvert}"
msgstr "XML til SimpleSAMLphp metadata-oversetter"

msgid "{core:frontpage:link_cleardiscochoices}"
msgstr "Slett mitt valg av IdP i IdP discovery tjenestene"

msgid "{core:frontpage:loggedin_as_admin}"
msgstr "Du er logget inn som administrator"

msgid "{core:frontpage:auth}"
msgstr "Autentisering"

msgid "{core:no_metadata:suggestion_user_link}"
msgstr ""
"Hvis du er en bruker som fikk denne feilen etter at du fulgte en link på "
"en nettside, så bør du rapportere denne feilen til eieren av den "
"nettsiden."

msgid "{core:no_state:description}"
msgstr "Vi kunne ikke finne tilstandsinformasjonen for denne forespørselen."

msgid "{core:frontpage:show_metadata}"
msgstr "Vis metadata"

msgid "{core:no_state:suggestion_closebrowser}"
msgstr "Lukk nettleseren, og prøv på nytt."

msgid "{core:short_sso_interval:warning_header}"
msgstr "For kort intervall imellom innloggingsforespørsler"

msgid "{core:frontpage:intro}"
msgstr ""
"<strong>Gratulerer</strong>, du har nå installert SimpleSAMLphp. Dette er"
" startsiden til din SimpleSAMLphp installasjon, hvor du vil finne "
"eksempler, diagnostikk, metadata og til og med lenker til relevant "
"dokumentasjon."

msgid "{core:no_metadata:header}"
msgstr "Kunne ikke finne metadata"

msgid "{core:frontpage:link_meta_shib13idphosted}"
msgstr "Hosted Shibboleth 1.3 Identity Provider Metadata (automatisk generert)"

msgid "{core:frontpage:required}"
msgstr "Påkrevd"

msgid "{core:no_metadata:config_problem}"
msgstr ""
"Dette er sannsynligvis et konfigurasjonsproblem hos enten "
"tjenesteleverandøren eller identitetsleverandøren."

msgid "{core:frontpage:warnings_suhosin_url_length}"
msgstr ""
"Lengden på forespørselparametre er begrenset av PHP Suhosin utvidelsen. "
"Vennligst øk suhosin.get.max_value_length konfigurasjonsinnstillingen til"
" minst 2048 tegn."

msgid "{core:frontpage:warnings_https}"
msgstr ""
"<strong>Du bruker ikke HTTPS</strong> - kryptert kommunikasjon med "
"brukeren. HTTP fungerer utmerket til testformål, men  i et "
"produksjonsmiljø anbefales sterkt å skru på sikker kommunikasjon med "
"HTTPS. [ <a href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">Les mer i dokumentet: SimpleSAMLphp maintenance</a> ]"

msgid "{core:frontpage:federation}"
msgstr "Føderasjon"

msgid "{core:frontpage:required_radius}"
msgstr "Påkrevd for Radius"

msgid "{core:no_state:cause_openbrowser}"
msgstr "Starte nettleseren med faner lagret fra forrige gang."

msgid "{core:frontpage:checkphp}"
msgstr "Sjekker din PHP installasjon"

msgid "{core:frontpage:link_doc_idp}"
msgstr "Bruk SimpleSAMLphp som identitetstjeneste"

msgid "{core:no_state:report_header}"
msgstr "Rapporter denne feilen"

msgid "{core:frontpage:link_saml2example}"
msgstr "SAML 2.0 SP eksempel - test innlogging med SAML 2.0 via din IdP"

msgid "{core:no_state:cause_nocookie}"
msgstr "At informasjonskapsler ikke er aktivert i nettleseren."

msgid "{core:frontpage:about_header}"
msgstr "Om SimpleSAMLphp"

msgid "{core:frontpage:about_text}"
msgstr ""
"Yey! SimpleSAMLphp virker jammen kult, hvor kan jeg finne ut mer om det? "
"Du kan lese mer om SimpleSAMLphp på <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp sin "
"hjemmeside</a>."

msgid "{core:no_metadata:suggestion_developer}"
msgstr ""
"Hvis du er en utvikler som setter opp en \"single sign-on\" løsning, så "
"har du et problem med metadataoppsettet. Kontroller at metadata er riktig"
" konfigurert hos både identitetsleverandøren og tjenesteleverandøren."

msgid "{core:no_cookie:retry}"
msgstr "Prøv igjen"

msgid "{core:frontpage:useful_links_header}"
msgstr "Nyttige lenker for denne installasjonen"

msgid "{core:frontpage:metadata}"
msgstr "Metadata"

msgid "{core:frontpage:recommended}"
msgstr "Anbefalt"

msgid "{core:frontpage:link_doc_googleapps}"
msgstr "SimpleSAMLphp som identitetstjeneste for Google Apps for Education"

msgid "{core:frontpage:tools}"
msgstr "Verktøy"

msgid "{core:short_sso_interval:retry}"
msgstr "Forsøk å logge inn på nytt"

msgid "{core:no_cookie:description}"
msgstr ""
"Du ser ut til å ha deaktivert informasjonskapsler. Kontroller "
"innstillingene i nettleseren din og prøv igjen."

msgid "{core:frontpage:deprecated}"
msgstr "Utdatert"

msgid "You are logged in as administrator"
msgstr "Du er logget inn som administrator"

msgid "Go back to the previous page and try again."
msgstr "Gå tilbake til forrige side og prøv på nytt."

msgid "If this problem persists, you can report it to the system administrators."
msgstr "Hvis problemet vedvarer, kan du rapportere det til systemadministratorene."

msgid "Welcome"
msgstr "Velkommen"

msgid "SimpleSAMLphp configuration check"
msgstr "Sjekk av SimpleSAMLphp konfigurasjonsfiler"

msgid "Metadata overview for your installation. Diagnose your metadata files"
msgstr ""
"Oversikt over metadata for din installasjon. Diagnostiser metadatafilene "
"her."

msgid "XML to SimpleSAMLphp metadata converter"
msgstr "XML til SimpleSAMLphp metadata-oversetter"

msgid "Required"
msgstr "Påkrevd"

msgid "Warnings"
msgstr "Advarsler"

msgid "Documentation"
msgstr "Dokumentasjon"

msgid "Hosted Shibboleth 1.3 Service Provider Metadata (automatically generated)"
msgstr "Hosted Shibboleth 1.3 Service Provider Metadata (automatisk generert)"

msgid "PHP info"
msgstr "PHPinfo"

msgid "About SimpleSAMLphp"
msgstr "Om SimpleSAMLphp"

msgid "Hosted SAML 2.0 Service Provider Metadata (automatically generated)"
msgstr "Hosted SAML 2.0 Service Provider Metadata (automatisk generert)"

msgid "Retry login"
msgstr "Forsøk å logge inn på nytt"

msgid "Required for LDAP"
msgstr "Påkrevd for LDAP"

msgid "Close the web browser, and try again."
msgstr "Lukk nettleseren, og prøv på nytt."

msgid "Federation"
msgstr "Føderasjon"

msgid "We were unable to locate the state information for the current request."
msgstr "Vi kunne ikke finne tilstandsinformasjonen for denne forespørselen."

msgid "Delete my choices of IdP in the IdP discovery services"
msgstr "Slett mitt valg av IdP i IdP discovery tjenestene"

msgid ""
"This is most likely a configuration problem on either the service "
"provider or identity provider."
msgstr ""
"Dette er sannsynligvis et konfigurasjonsproblem hos enten "
"tjenesteleverandøren eller identitetsleverandøren."

msgid "Configure Shibboleth 1.3 SP to work with SimpleSAMLphp IdP"
msgstr ""
"Konfigurer Shibboleth 1.3 SP for å fungere sammen med SimpleSAMLphp "
"identitetstjeneste"

msgid "Using the back and forward buttons in the web browser."
msgstr "Bruk av \"frem\"- og \"tilbake\"-knappene i nettleseren."

msgid "Metadata not found"
msgstr "Kunne ikke finne metadata"

msgid "Missing cookie"
msgstr "Mangler informasjonskapsel"

msgid "Cookies may be disabled in the web browser."
msgstr "At informasjonskapsler ikke er aktivert i nettleseren."

msgid "Opened the web browser with tabs saved from the previous session."
msgstr "Starte nettleseren med faner lagret fra forrige gang."

msgid "Tools"
msgstr "Verktøy"

msgid "Test configured authentication sources "
msgstr "Test konfigurerte autentiseringskilder"

msgid ""
"You appear to have disabled cookies in your browser. Please check the "
"settings in your browser, and try again."
msgstr ""
"Du ser ut til å ha deaktivert informasjonskapsler. Kontroller "
"innstillingene i nettleseren din og prøv igjen."

msgid "Installing SimpleSAMLphp"
msgstr "Installerer SimpleSAMLphp"

msgid "Deprecated"
msgstr "Utdatert"

msgid ""
"<strong>Congratulations</strong>, you have successfully installed "
"SimpleSAMLphp. This is the start page of your installation, where you "
"will find links to test examples, diagnostics, metadata and even links to"
" relevant documentation."
msgstr ""
"<strong>Gratulerer</strong>, du har nå installert SimpleSAMLphp. Dette er"
" startsiden til din SimpleSAMLphp installasjon, hvor du vil finne "
"eksempler, diagnostikk, metadata og til og med lenker til relevant "
"dokumentasjon."

msgid "This error may be caused by:"
msgstr "Denne feilen kan være forårsaket av:"

msgid ""
"<strong>You are not using HTTPS</strong> - encrypted communication with "
"the user. HTTP works fine for test purposes, but in a production "
"environment, you should use HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-"
"maintenance\">Read more about SimpleSAMLphp maintenance</a> ]"
msgstr ""
"<strong>Du bruker ikke HTTPS</strong> - kryptert kommunikasjon med "
"brukeren. HTTP fungerer utmerket til testformål, men  i et "
"produksjonsmiljø anbefales sterkt å skru på sikker kommunikasjon med "
"HTTPS. [ <a href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">Les mer i dokumentet: SimpleSAMLphp maintenance</a> ]"

msgid "Metadata"
msgstr "Metadata"

msgid "Retry"
msgstr "Prøv igjen"

msgid "SimpleSAMLphp Maintenance and Configuration"
msgstr "SimpleSAMLphp vedlikehold og konfigurasjon"

msgid "Diagnostics on hostname, port and protocol"
msgstr "Diagnostiser hostnavn, port og protokoll"

msgid ""
"If you are an user who received this error after following a link on a "
"site, you should report this error to the owner of that site."
msgstr ""
"Hvis du er en bruker som fikk denne feilen etter at du fulgte en link på "
"en nettside, så bør du rapportere denne feilen til eieren av den "
"nettsiden."

msgid "Using SimpleSAMLphp as an Identity Provider"
msgstr "Bruk SimpleSAMLphp som identitetstjeneste"

msgid "Optional"
msgstr "Valgfritt"

msgid "Suggestions for resolving this problem:"
msgstr "Forslag for å løse dette problemet:"

msgid ""
"This SimpleSAMLphp thing is pretty cool, where can I read more about it? "
"You can find more information about it at the <a "
"href=\"https://simplesamlphp.org/\">SimpleSAMLphp web page </a> over at "
"<a href=\"http://uninett.no\">UNINETT</a>."
msgstr ""
"Yey! SimpleSAMLphp virker jammen kult, hvor kan jeg finne ut mer om det? "
"Du kan lese mer om SimpleSAMLphp på <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp sin "
"hjemmeside</a>."

msgid "Shibboleth 1.3 SP example - test logging in through your Shib IdP"
msgstr ""
"Shibboleth 1.3 SP eksempel - test innlogging med Shibboleth 1.3 via din "
"IdP"

msgid "Authentication"
msgstr "Autentisering"

msgid "SimpleSAMLphp installation page"
msgstr "SimpleSAMLphp installasjonsside"

msgid "Show metadata"
msgstr "Vis metadata"

msgid "SimpleSAMLphp as an IdP for Google Apps for Education"
msgstr "SimpleSAMLphp som identitetstjeneste for Google Apps for Education"

msgid "State information lost"
msgstr "Tilstandsinformasjon tapt"

msgid "Hosted SAML 2.0 Identity Provider Metadata (automatically generated)"
msgstr "Hosted SAML 2.0 Identity Provider Metadata (automatisk generert)"

msgid "OpenID Provider site - Alpha version (test code)"
msgstr "OpenID Provider side - Alpha versjon (testkode)"

msgid "Required for Radius"
msgstr "Påkrevd for Radius"

msgid "We were unable to locate the metadata for the entity:"
msgstr "Vi fant ikke metadataene for:"

msgid "SAML 2.0 SP example - test logging in through your IdP"
msgstr "SAML 2.0 SP eksempel - test innlogging med SAML 2.0 via din IdP"

msgid "Using SimpleSAMLphp as a Service Provider"
msgstr "Bruk SimpleSAMLphp som Service Provider"

msgid ""
"We have detected that there is only a few seconds since you last "
"authenticated with this service provider, and therefore assume that there"
" is a problem with this SP."
msgstr ""
"Vi har detektert at det er bare noen få sekunder siden du sist ble "
"autentisert på denne tjenesten, og derfor antar vi at det er et problem "
"med oppsettet av denne tjenesten."

msgid "Recommended"
msgstr "Anbefalt"

msgid ""
"If you are a developer who is deploying a single sign-on solution, you "
"have a problem with the metadata configuration. Verify that metadata is "
"configured correctly on both the identity provider and service provider."
msgstr ""
"Hvis du er en utvikler som setter opp en \"single sign-on\" løsning, så "
"har du et problem med metadataoppsettet. Kontroller at metadata er riktig"
" konfigurert hos både identitetsleverandøren og tjenesteleverandøren."

msgid "SimpleSAMLphp Advanced Features"
msgstr "SimpleSAMLphp avansert funksjonalitet"

msgid "Too short interval between single sign on events."
msgstr "For kort intervall imellom innloggingsforespørsler"

msgid "Checking your PHP installation"
msgstr "Sjekker din PHP installasjon"

msgid ""
"The length of query parameters is limited by the PHP Suhosin extension. "
"Please increase the suhosin.get.max_value_length option to at least 2048 "
"bytes."
msgstr ""
"Lengden på forespørselparametre er begrenset av PHP Suhosin utvidelsen. "
"Vennligst øk suhosin.get.max_value_length konfigurasjonsinnstillingen til"
" minst 2048 tegn."

msgid "Useful links for your installation"
msgstr "Nyttige lenker for denne installasjonen"

msgid "Configuration"
msgstr "Konfigurasjon"

msgid "Hosted Shibboleth 1.3 Identity Provider Metadata (automatically generated)"
msgstr "Hosted Shibboleth 1.3 Identity Provider Metadata (automatisk generert)"

msgid "Login as administrator"
msgstr "Login som administrator"

msgid "Report this error"
msgstr "Rapporter denne feilen"

