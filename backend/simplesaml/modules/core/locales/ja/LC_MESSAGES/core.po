
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: ja\n"
"Language-Team: \n"
"Plural-Forms: nplurals=1; plural=0\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{core:frontpage:link_phpinfo}"
msgstr "PHP info"

msgid "{core:no_state:report_text}"
msgstr "この問題が継続して起こる場合、システム管理者に報告してください。"

msgid "{core:no_state:cause_backforward}"
msgstr "WEBブラウザの戻るや次へのボタンを使用します。"

msgid "{core:no_metadata:not_found_for}"
msgstr "メタデータの実体を見つけることが出来ませんでした:"

msgid "{core:frontpage:link_shib13example}"
msgstr "Shibboleth 1.3 SP example - Shib IdP 経由でテストログイン"

msgid "{core:no_state:suggestions}"
msgstr "この問題を解決する為の提案:"

msgid "{core:frontpage:login_as_admin}"
msgstr "管理者でログイン"

msgid "{core:short_sso_interval:warning}"
msgstr "私たちはたった数秒前にあなたがこのサービスプロバイダに認証されている事を検知しています、従って問題はSPに在ると推測できます。"

msgid "{core:frontpage:link_doc_sp}"
msgstr "SimpleSAMLphpをサービスプロバイダとして使用する"

msgid "{core:frontpage:link_meta_saml2sphosted}"
msgstr "ホスト SAML 2.0 サービスプロバイダメタデータ(自動生成)"

msgid "{core:frontpage:link_openidprovider}"
msgstr "OpenID プロバイダサイト - アルファバージョン (テストコード)"

msgid "{core:frontpage:link_doc_install}"
msgstr "SimpleSAMLphpのインストール"

msgid "{core:frontpage:link_diagnostics}"
msgstr "ホストネームやポート、プロトコルを診断"

msgid "{core:no_state:suggestion_goback}"
msgstr "前のページに戻り、再度試してください。"

msgid "{core:no_state:causes}"
msgstr "このエラーの原因:"

msgid "{core:frontpage:link_meta_saml2idphosted}"
msgstr "ホスト SAML 2.0 アイデンティティプロバイダメタデータ(自動生成)"

msgid "{core:frontpage:optional}"
msgstr "任意"

msgid "{core:frontpage:link_meta_shib13sphosted}"
msgstr "ホスト Shibboleth 1.3 サービスプロバイダメタデータ(自動生成)"

msgid "{core:frontpage:doc_header}"
msgstr "ドキュメント"

msgid "{core:frontpage:link_doc_advanced}"
msgstr "SimpleSAMLphp の高度な機能"

msgid "{core:frontpage:required_ldap}"
msgstr "LDAPに必要"

msgid "{core:frontpage:authtest}"
msgstr "設定されている認証元をテスト"

msgid "{core:frontpage:link_meta_overview}"
msgstr "メタデータの概要。メタデータファイルを診断します。"

msgid "{core:frontpage:configuration}"
msgstr "設定"

msgid "{core:frontpage:welcome}"
msgstr "ようこそ"

msgid "{core:frontpage:link_doc_shibsp}"
msgstr "SimpleSAMLphp IdP と 連携する Shibboleth SP 1.3 として設定"

msgid "{core:no_state:header}"
msgstr "状態情報が無くなりました。"

msgid "{core:frontpage:metadata_header}"
msgstr "メタデータ"

msgid "{core:frontpage:link_doc_maintenance}"
msgstr "SimpleSAMLphp のメンテナンスと設定"

msgid "{core:frontpage:link_configcheck}"
msgstr "SimpleSAMLphp 設定確認"

msgid "{core:frontpage:page_title}"
msgstr "SimpleSAMLphp設定ページ"

msgid "{core:no_cookie:header}"
msgstr "クッキーが見つかりません"

msgid "{core:frontpage:warnings}"
msgstr "警告"

msgid "{core:frontpage:link_xmlconvert}"
msgstr "XML を SimpleSAMLphpメタデータに変換"

msgid "{core:frontpage:link_cleardiscochoices}"
msgstr "IdPディスカバリサービス内のIdpの選択を削除する"

msgid "{core:frontpage:loggedin_as_admin}"
msgstr "管理者でログインしています"

msgid "{core:frontpage:auth}"
msgstr "認証"

msgid "{core:no_metadata:suggestion_user_link}"
msgstr "もしあなたがユーザーで以下のリンクのサイトでこのエラーを受け取ったのであれば、あなたはこのエラーをサイトの管理者に報告してください。"

msgid "{core:no_state:description}"
msgstr "現在のリクエストから状態情報を特定することが出来ませんでした。"

msgid "{core:frontpage:show_metadata}"
msgstr "メタデータを表示"

msgid "{core:no_state:suggestion_closebrowser}"
msgstr "WEBブラウザを閉じて、再度試してください。"

msgid "{core:short_sso_interval:warning_header}"
msgstr "シングルサインオンイベントの間隔が短すぎます。"

msgid "{core:frontpage:intro}"
msgstr ""
"<strong>おめでとう</strong>, あなたは SimpleSAMLphp "
"のインストールに成功しました。このページは設定を行うためのスタートページです。テスト、診断、メタデータや関連するドキュメントへのリンクを見つけるでしょう。"

msgid "{core:no_metadata:header}"
msgstr "メタデータが見つかりません"

msgid "{core:frontpage:link_meta_shib13idphosted}"
msgstr "ホスト Shibboleth 1.3 アイデンティティプロバイダメタデータ (自動生成)"

msgid "{core:frontpage:required}"
msgstr "必須"

msgid "{core:no_metadata:config_problem}"
msgstr "これは恐らくサービスプロバイダかアイデンティティプロバイダの設定の問題です。"

msgid "{core:frontpage:warnings_suhosin_url_length}"
msgstr ""
"PHPのSuhosin拡張によってクエリーパラメータの長さが制限されています。suhosin.get.max_value_length を "
"2048以上に増やしてください。"

msgid "{core:frontpage:warnings_https}"
msgstr ""
"<strong>あなたはHTTPS(暗号化通信)を行っていません。</strong>HTTPはテスト環境であれば正常に動作します、しかし製品環境ではHTTPSを使用するべきです。["
" <a href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">詳しくは SimpleSAMLphp メンテナンス情報を読んでください。</a> ]"

msgid "{core:frontpage:federation}"
msgstr "連携"

msgid "{core:frontpage:required_radius}"
msgstr "Radiusに必要"

msgid "{core:no_state:cause_openbrowser}"
msgstr "ブラウザに保存されたタブにより、以前のセッションが開かれました。"

msgid "{core:frontpage:checkphp}"
msgstr "PHPの設定を確認"

msgid "{core:frontpage:link_doc_idp}"
msgstr "SimpleSAMLphpをアイデンティティプロバイダとして使用する"

msgid "{core:no_state:report_header}"
msgstr "このエラーをレポート"

msgid "{core:frontpage:link_saml2example}"
msgstr "SAML 2.0 SP example - IdP 経由でテストログイン"

msgid "{core:no_state:cause_nocookie}"
msgstr "このWEBブラウザではクッキーが無効化されています。"

msgid "{core:frontpage:about_header}"
msgstr "SimpleSAMLphp について"

msgid "{core:frontpage:about_text}"
msgstr ""
"この SimpleSAMLphp は素晴らしいものです。これ以上の説明が在りますか？こちらのリンクで更なる情報を見つけることが出来ます <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp at the Feide RnD"
" blog</a>"

msgid "{core:no_metadata:suggestion_developer}"
msgstr "もしあなたが開発者でシングルサインオンシステムの構築者である場合、メタデータの設定に問題が有ります。アイデンティティプロバイダとサービスプロバイダの両方にメタデータが正しく設定されているか確認してください。"

msgid "{core:no_cookie:retry}"
msgstr "再試行"

msgid "{core:frontpage:useful_links_header}"
msgstr "設定に便利なリンク集"

msgid "{core:frontpage:metadata}"
msgstr "メタデータ"

msgid "{core:frontpage:recommended}"
msgstr "推奨"

msgid "{core:frontpage:link_doc_googleapps}"
msgstr "SimpleSAMLphp を Google Apps for Education の為のIdPとして設定"

msgid "{core:frontpage:tools}"
msgstr "ツール"

msgid "{core:short_sso_interval:retry}"
msgstr "ログインを再試行"

msgid "{core:no_cookie:description}"
msgstr "あなたのブラウザでクッキーが無効化されている様です。ブラウザの設定を確認し、再度試してください。"

msgid "{core:frontpage:deprecated}"
msgstr "古い"

msgid "You are logged in as administrator"
msgstr "管理者でログインしています"

msgid "Go back to the previous page and try again."
msgstr "前のページに戻り、再度試してください。"

msgid "If this problem persists, you can report it to the system administrators."
msgstr "この問題が継続して起こる場合、システム管理者に報告してください。"

msgid "Welcome"
msgstr "ようこそ"

msgid "SimpleSAMLphp configuration check"
msgstr "SimpleSAMLphp 設定確認"

msgid "Metadata overview for your installation. Diagnose your metadata files"
msgstr "メタデータの概要。メタデータファイルを診断します。"

msgid "XML to SimpleSAMLphp metadata converter"
msgstr "XML を SimpleSAMLphpメタデータに変換"

msgid "Required"
msgstr "必須"

msgid "Warnings"
msgstr "警告"

msgid "Documentation"
msgstr "ドキュメント"

msgid "Hosted Shibboleth 1.3 Service Provider Metadata (automatically generated)"
msgstr "ホスト Shibboleth 1.3 サービスプロバイダメタデータ(自動生成)"

msgid "PHP info"
msgstr "PHP info"

msgid "About SimpleSAMLphp"
msgstr "SimpleSAMLphp について"

msgid "Hosted SAML 2.0 Service Provider Metadata (automatically generated)"
msgstr "ホスト SAML 2.0 サービスプロバイダメタデータ(自動生成)"

msgid "Retry login"
msgstr "ログインを再試行"

msgid "Required for LDAP"
msgstr "LDAPに必要"

msgid "Close the web browser, and try again."
msgstr "WEBブラウザを閉じて、再度試してください。"

msgid "Federation"
msgstr "連携"

msgid "We were unable to locate the state information for the current request."
msgstr "現在のリクエストから状態情報を特定することが出来ませんでした。"

msgid "Delete my choices of IdP in the IdP discovery services"
msgstr "IdPディスカバリサービス内のIdpの選択を削除する"

msgid ""
"This is most likely a configuration problem on either the service "
"provider or identity provider."
msgstr "これは恐らくサービスプロバイダかアイデンティティプロバイダの設定の問題です。"

msgid "Configure Shibboleth 1.3 SP to work with SimpleSAMLphp IdP"
msgstr "SimpleSAMLphp IdP と 連携する Shibboleth SP 1.3 として設定"

msgid "Using the back and forward buttons in the web browser."
msgstr "WEBブラウザの戻るや次へのボタンを使用します。"

msgid "Metadata not found"
msgstr "メタデータが見つかりません"

msgid "Missing cookie"
msgstr "クッキーが見つかりません"

msgid "Cookies may be disabled in the web browser."
msgstr "このWEBブラウザではクッキーが無効化されています。"

msgid "Opened the web browser with tabs saved from the previous session."
msgstr "ブラウザに保存されたタブにより、以前のセッションが開かれました。"

msgid "Tools"
msgstr "ツール"

msgid "Test configured authentication sources "
msgstr "設定されている認証元をテスト"

msgid ""
"You appear to have disabled cookies in your browser. Please check the "
"settings in your browser, and try again."
msgstr "あなたのブラウザでクッキーが無効化されている様です。ブラウザの設定を確認し、再度試してください。"

msgid "Installing SimpleSAMLphp"
msgstr "SimpleSAMLphpのインストール"

msgid "Deprecated"
msgstr "古い"

msgid ""
"<strong>Congratulations</strong>, you have successfully installed "
"SimpleSAMLphp. This is the start page of your installation, where you "
"will find links to test examples, diagnostics, metadata and even links to"
" relevant documentation."
msgstr ""
"<strong>おめでとう</strong>, あなたは SimpleSAMLphp "
"のインストールに成功しました。このページは設定を行うためのスタートページです。テスト、診断、メタデータや関連するドキュメントへのリンクを見つけるでしょう。"

msgid "This error may be caused by:"
msgstr "このエラーの原因:"

msgid ""
"<strong>You are not using HTTPS</strong> - encrypted communication with "
"the user. HTTP works fine for test purposes, but in a production "
"environment, you should use HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-"
"maintenance\">Read more about SimpleSAMLphp maintenance</a> ]"
msgstr ""
"<strong>あなたはHTTPS(暗号化通信)を行っていません。</strong>HTTPはテスト環境であれば正常に動作します、しかし製品環境ではHTTPSを使用するべきです。["
" <a href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">詳しくは SimpleSAMLphp メンテナンス情報を読んでください。</a> ]"

msgid "Metadata"
msgstr "メタデータ"

msgid "Retry"
msgstr "再試行"

msgid "SimpleSAMLphp Maintenance and Configuration"
msgstr "SimpleSAMLphp のメンテナンスと設定"

msgid "Diagnostics on hostname, port and protocol"
msgstr "ホストネームやポート、プロトコルを診断"

msgid ""
"If you are an user who received this error after following a link on a "
"site, you should report this error to the owner of that site."
msgstr "もしあなたがユーザーで以下のリンクのサイトでこのエラーを受け取ったのであれば、あなたはこのエラーをサイトの管理者に報告してください。"

msgid "Using SimpleSAMLphp as an Identity Provider"
msgstr "SimpleSAMLphpをアイデンティティプロバイダとして使用する"

msgid "Optional"
msgstr "任意"

msgid "Suggestions for resolving this problem:"
msgstr "この問題を解決する為の提案:"

msgid ""
"This SimpleSAMLphp thing is pretty cool, where can I read more about it? "
"You can find more information about it at the <a "
"href=\"https://simplesamlphp.org/\">SimpleSAMLphp web page </a> over at "
"<a href=\"http://uninett.no\">UNINETT</a>."
msgstr ""
"この SimpleSAMLphp は素晴らしいものです。これ以上の説明が在りますか？こちらのリンクで更なる情報を見つけることが出来ます <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp at the Feide RnD"
" blog</a>"

msgid "Shibboleth 1.3 SP example - test logging in through your Shib IdP"
msgstr "Shibboleth 1.3 SP example - Shib IdP 経由でテストログイン"

msgid "Authentication"
msgstr "認証"

msgid "SimpleSAMLphp installation page"
msgstr "SimpleSAMLphp設定ページ"

msgid "Show metadata"
msgstr "メタデータを表示"

msgid "SimpleSAMLphp as an IdP for Google Apps for Education"
msgstr "SimpleSAMLphp を Google Apps for Education の為のIdPとして設定"

msgid "State information lost"
msgstr "状態情報が無くなりました。"

msgid "Hosted SAML 2.0 Identity Provider Metadata (automatically generated)"
msgstr "ホスト SAML 2.0 アイデンティティプロバイダメタデータ(自動生成)"

msgid "OpenID Provider site - Alpha version (test code)"
msgstr "OpenID プロバイダサイト - アルファバージョン (テストコード)"

msgid "Required for Radius"
msgstr "Radiusに必要"

msgid "We were unable to locate the metadata for the entity:"
msgstr "メタデータの実体を見つけることが出来ませんでした:"

msgid "SAML 2.0 SP example - test logging in through your IdP"
msgstr "SAML 2.0 SP example - IdP 経由でテストログイン"

msgid "Using SimpleSAMLphp as a Service Provider"
msgstr "SimpleSAMLphpをサービスプロバイダとして使用する"

msgid ""
"We have detected that there is only a few seconds since you last "
"authenticated with this service provider, and therefore assume that there"
" is a problem with this SP."
msgstr "私たちはたった数秒前にあなたがこのサービスプロバイダに認証されている事を検知しています、従って問題はSPに在ると推測できます。"

msgid "Recommended"
msgstr "推奨"

msgid ""
"If you are a developer who is deploying a single sign-on solution, you "
"have a problem with the metadata configuration. Verify that metadata is "
"configured correctly on both the identity provider and service provider."
msgstr "もしあなたが開発者でシングルサインオンシステムの構築者である場合、メタデータの設定に問題が有ります。アイデンティティプロバイダとサービスプロバイダの両方にメタデータが正しく設定されているか確認してください。"

msgid "SimpleSAMLphp Advanced Features"
msgstr "SimpleSAMLphp の高度な機能"

msgid "Too short interval between single sign on events."
msgstr "シングルサインオンイベントの間隔が短すぎます。"

msgid "Checking your PHP installation"
msgstr "PHPの設定を確認"

msgid ""
"The length of query parameters is limited by the PHP Suhosin extension. "
"Please increase the suhosin.get.max_value_length option to at least 2048 "
"bytes."
msgstr ""
"PHPのSuhosin拡張によってクエリーパラメータの長さが制限されています。suhosin.get.max_value_length を "
"2048以上に増やしてください。"

msgid "Useful links for your installation"
msgstr "設定に便利なリンク集"

msgid "Configuration"
msgstr "設定"

msgid "Hosted Shibboleth 1.3 Identity Provider Metadata (automatically generated)"
msgstr "ホスト Shibboleth 1.3 アイデンティティプロバイダメタデータ (自動生成)"

msgid "Login as administrator"
msgstr "管理者でログイン"

msgid "Report this error"
msgstr "このエラーをレポート"

