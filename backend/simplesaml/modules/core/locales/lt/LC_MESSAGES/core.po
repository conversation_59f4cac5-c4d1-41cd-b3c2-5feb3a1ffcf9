
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: lt\n"
"Language-Team: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"(n%100<10 || n%100>=20) ? 1 : 2)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{core:frontpage:link_phpinfo}"
msgstr "PHP informacija"

msgid "{core:no_state:report_text}"
msgstr ""
"Jei ši problema i<PERSON>, galite prane<PERSON>ti apie tai sistemos "
"administratoriui."

msgid "{core:no_state:cause_backforward}"
msgstr "Back (Atgal) ir Forward (Pirmyn) mygtukų naudojimas interneto naršyklėje"

msgid "{core:no_metadata:not_found_for}"
msgstr "Nepavyko nustatyti metaduomenų šiems objektams:"

msgid "{core:frontpage:link_shib13example}"
msgstr ""
"Shibboleth 1.3 SP pavyzdys - istorinių duomenų testavimas kartu su Jūsų "
"Shib IdP"

msgid "{core:no_state:suggestions}"
msgstr "Pasiūlymai spręsti šią problemą:"

msgid "{core:frontpage:login_as_admin}"
msgstr "Prisijungti administratoriaus teisėmis"

msgid "{core:short_sso_interval:warning}"
msgstr ""
"Mes nustatėme, kad praėjo tik kelios sekundės nuo Jūsų autentiškumo "
"patvirtimo šiam paslaugos teikėjui, dėl to manome, kad yra nesklandumų su"
" SP."

msgid "{core:frontpage:link_doc_sp}"
msgstr "SimpleSAMLphp naudojimas kaip paslaugos teikėją"

msgid "{core:frontpage:link_meta_saml2sphosted}"
msgstr "Vietinio SAML 2.0 SP metaduomenys (sugeneruoti automatiškai)"

msgid "{core:frontpage:link_openidprovider}"
msgstr "OpenID tiekėjo puslapis - alfa versija (bandomasis kodas)"

msgid "{core:frontpage:link_doc_install}"
msgstr "Diegiamas SimpleSAMLphp"

msgid "{core:frontpage:link_diagnostics}"
msgstr "Serverio vardo, porto ir protokolo diagnostika"

msgid "{core:no_state:suggestion_goback}"
msgstr "Grįžkite į ankstesnį puslapį ir pabandykite dar kartą."

msgid "{core:no_state:causes}"
msgstr "Šią klaidą galėjo sukelti:"

msgid "{core:frontpage:link_meta_saml2idphosted}"
msgstr "Vietinio SAML 2.0 IdP metaduomenys (sugeneruoti automatiškai)"

msgid "{core:frontpage:optional}"
msgstr "Neprivalomas"

msgid "{core:frontpage:link_meta_shib13sphosted}"
msgstr ""
"Vietinio Shibboleth 1.3 paslaugos teikėjo (SP) metaduomenys (sugeneruoti "
"automatiškai)"

msgid "{core:frontpage:doc_header}"
msgstr "Dokumentacija"

msgid "{core:frontpage:link_doc_advanced}"
msgstr "SimpleSAMLphp papildomos galimybės"

msgid "{core:frontpage:required_ldap}"
msgstr "Būtinas LDAP serveriui"

msgid "{core:frontpage:authtest}"
msgstr "Testuoti nustatytus autentifikavimo šaltinius"

msgid "{core:frontpage:link_meta_overview}"
msgstr "Diegimo metaduomenų peržiūra. Galite analizuoti savo metaduomenis"

msgid "{core:frontpage:configuration}"
msgstr "Konfigūracija"

msgid "{core:frontpage:welcome}"
msgstr "Sveiki atvykę"

msgid "{core:frontpage:link_doc_shibsp}"
msgstr "Shibboleth 1.3 SP konfigūravimas darbui su SimpleSAMLphp IdP"

msgid "{core:no_state:header}"
msgstr "Būsenos informacia prarasta"

msgid "{core:frontpage:metadata_header}"
msgstr "Metaduomenys"

msgid "{core:frontpage:link_doc_maintenance}"
msgstr "SimpleSAMLphp valdymas ir konfigūracija"

msgid "{core:frontpage:link_configcheck}"
msgstr "SimpleSAMLphp konfigūracijos patikrinimas"

msgid "{core:frontpage:page_title}"
msgstr "SimpleSAMLphp diegimo puslapis"

msgid "{core:no_cookie:header}"
msgstr "Slapukas nerastas"

msgid "{core:frontpage:warnings}"
msgstr "Įspėjimai"

msgid "{core:frontpage:link_xmlconvert}"
msgstr "XML į SimpleSAMLphp metaduomenų vertimas"

msgid "{core:frontpage:link_cleardiscochoices}"
msgstr "Panaikinti mano IdP parinktis IdP suradimo paslaugoje"

msgid "{core:frontpage:loggedin_as_admin}"
msgstr "Jūs prisijungėte administratoriaus teisėmis"

msgid "{core:frontpage:auth}"
msgstr "Autentifikavimas"

msgid "{core:no_metadata:suggestion_user_link}"
msgstr ""
"Jei Jūs esate naudotojas, kuris gavote šią klaidą spragtelėjęs nuorodą "
"tinklapyje, Jūs turėtumėte informuoti tinklapio administratorių apie šią "
"klaidą."

msgid "{core:no_state:description}"
msgstr "Nepavyko nustatyti būsenos informacijos šiai užklausai."

msgid "{core:frontpage:show_metadata}"
msgstr "Parodyti metaduomenis"

msgid "{core:no_state:suggestion_closebrowser}"
msgstr "Uždarykite interneto naršyklę ir pabandykite dar kartą."

msgid "{core:short_sso_interval:warning_header}"
msgstr "Per trumpas intervalas tarp prisijungimų prie paslaugų."

msgid "{core:frontpage:intro}"
msgstr ""
"<strong>Sveikiname</strong>, Jūs sėkmingai įdiegėte SimpleSAMLphp. Tai "
"pradinis diegimo puslapis, kur Jūs rasite nuorodas į testavimo "
"pavyzdžius, diagnostiką, metaduomenis ir netgi nuorodas į susijusią "
"dokumentaciją."

msgid "{core:no_metadata:header}"
msgstr "Metaduomenys nerasti"

msgid "{core:frontpage:link_meta_shib13idphosted}"
msgstr ""
"Vietinio Shibboleth 1.3 tapatybės teikėjo (IdP) metaduomenys (sugeneruoti"
" automatiškai)"

msgid "{core:frontpage:required}"
msgstr "Būtinas"

msgid "{core:no_metadata:config_problem}"
msgstr ""
"Tai greičiausiai konfigūracijos problema paslaugos teikėjo arba tapatybių"
" teikėjo pusėje."

msgid "{core:frontpage:warnings_suhosin_url_length}"
msgstr ""
"Užklausos parametrų ilgis ribojamas PHP Suhosin plėtinio. Prašome "
"padidinti suhosin.get.max_value_length rekšmę bent iki 2048 baitų."

msgid "{core:frontpage:warnings_https}"
msgstr ""
"<strong>Jūs nenaudojate HTTPS</strong> - šifruotos komunikacijos su "
"vartotoju. HTTP puikiai tinka testavimo reikmėms, tačiau realioje "
"aplinkoje turėtumėte naudoti HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">Skaityti daugiau apie SimpleSAMLphp priežiūrą</a> ]"

msgid "{core:frontpage:federation}"
msgstr "Federacija"

msgid "{core:frontpage:required_radius}"
msgstr "Būtinas Radius serveriui"

msgid "{core:no_state:cause_openbrowser}"
msgstr ""
"Atidaryta interneto naršyklė su kortelėmis, išsaugotomis iš ankstesnės "
"sesijos."

msgid "{core:frontpage:checkphp}"
msgstr "Tikrinamas Jūsų PHP diegimas"

msgid "{core:frontpage:link_doc_idp}"
msgstr "SimpleSAMLphp naudojimas kaip tapatybės teikėją"

msgid "{core:no_state:report_header}"
msgstr "Pranešti apie šią klaidą"

msgid "{core:frontpage:link_saml2example}"
msgstr "SAML 2.0 SP pavyzdys - testinis prisijungimas per Jūsų IdP"

msgid "{core:no_state:cause_nocookie}"
msgstr "Interneto naršyklėje gali būti išjungti slapukai (cookies)."

msgid "{core:frontpage:about_header}"
msgstr "Apie SimpleSAMLphp"

msgid "{core:frontpage:about_text}"
msgstr ""
"Šis SimpleSAMLphp dalykas yra gana puikus, kur galėčiau daugiau apie jį "
"paskaityti? Daugiau informacijos apie <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp galite rasti "
"Feide RnD blog'e</a> bei <a href=\"http://uninett.no\">UNINETT</a> "
"svetainėje."

msgid "{core:no_metadata:suggestion_developer}"
msgstr ""
"Jei Jūs esate kūrėjas, kuris diegiate SSO sprendimą, Jums iškilo problema"
" susijusi su metaduomenų konfigūracija. Patikrinkite, ar metaduomenys "
"teisingai sukonfigūruoti tiek tapatybių teikėjo, tiek paslaugos teikėjo "
"pusėse."

msgid "{core:no_cookie:retry}"
msgstr "Bandyti dar kartą"

msgid "{core:frontpage:useful_links_header}"
msgstr "Naudingos nuorodos diegimui"

msgid "{core:frontpage:metadata}"
msgstr "Metaduomenys"

msgid "{core:frontpage:recommended}"
msgstr "Rekomenduojamas"

msgid "{core:frontpage:link_doc_googleapps}"
msgstr "SimpleSAMLphp kaip IdP skirtas \"Google Apps for Education\""

msgid "{core:frontpage:tools}"
msgstr "Įrankiai"

msgid "{core:short_sso_interval:retry}"
msgstr "Prisijunkite iš naujo"

msgid "{core:no_cookie:description}"
msgstr ""
"Atrodo Jūsų naršyklė nepalaiko slapukų. Patikrinkite naršyklės nustatymus"
" ir bandykite dar kartą."

msgid "{core:frontpage:deprecated}"
msgstr "Nebepalaikoma"

msgid "You are logged in as administrator"
msgstr "Jūs prisijungėte administratoriaus teisėmis"

msgid "Go back to the previous page and try again."
msgstr "Grįžkite į ankstesnį puslapį ir pabandykite dar kartą."

msgid "If this problem persists, you can report it to the system administrators."
msgstr ""
"Jei ši problema išliks, galite pranešti apie tai sistemos "
"administratoriui."

msgid "Welcome"
msgstr "Sveiki atvykę"

msgid "SimpleSAMLphp configuration check"
msgstr "SimpleSAMLphp konfigūracijos patikrinimas"

msgid "Metadata overview for your installation. Diagnose your metadata files"
msgstr "Diegimo metaduomenų peržiūra. Galite analizuoti savo metaduomenis"

msgid "XML to SimpleSAMLphp metadata converter"
msgstr "XML į SimpleSAMLphp metaduomenų vertimas"

msgid "Required"
msgstr "Būtinas"

msgid "Warnings"
msgstr "Įspėjimai"

msgid "Documentation"
msgstr "Dokumentacija"

msgid "Hosted Shibboleth 1.3 Service Provider Metadata (automatically generated)"
msgstr ""
"Vietinio Shibboleth 1.3 paslaugos teikėjo (SP) metaduomenys (sugeneruoti "
"automatiškai)"

msgid "PHP info"
msgstr "PHP informacija"

msgid "About SimpleSAMLphp"
msgstr "Apie SimpleSAMLphp"

msgid "Hosted SAML 2.0 Service Provider Metadata (automatically generated)"
msgstr "Vietinio SAML 2.0 SP metaduomenys (sugeneruoti automatiškai)"

msgid "Retry login"
msgstr "Prisijunkite iš naujo"

msgid "Required for LDAP"
msgstr "Būtinas LDAP serveriui"

msgid "Close the web browser, and try again."
msgstr "Uždarykite interneto naršyklę ir pabandykite dar kartą."

msgid "Federation"
msgstr "Federacija"

msgid "We were unable to locate the state information for the current request."
msgstr "Nepavyko nustatyti būsenos informacijos šiai užklausai."

msgid "Delete my choices of IdP in the IdP discovery services"
msgstr "Panaikinti mano IdP parinktis IdP suradimo paslaugoje"

msgid ""
"This is most likely a configuration problem on either the service "
"provider or identity provider."
msgstr ""
"Tai greičiausiai konfigūracijos problema paslaugos teikėjo arba tapatybių"
" teikėjo pusėje."

msgid "Configure Shibboleth 1.3 SP to work with SimpleSAMLphp IdP"
msgstr "Shibboleth 1.3 SP konfigūravimas darbui su SimpleSAMLphp IdP"

msgid "Using the back and forward buttons in the web browser."
msgstr "Back (Atgal) ir Forward (Pirmyn) mygtukų naudojimas interneto naršyklėje"

msgid "Metadata not found"
msgstr "Metaduomenys nerasti"

msgid "Missing cookie"
msgstr "Slapukas nerastas"

msgid "Cookies may be disabled in the web browser."
msgstr "Interneto naršyklėje gali būti išjungti slapukai (cookies)."

msgid "Opened the web browser with tabs saved from the previous session."
msgstr ""
"Atidaryta interneto naršyklė su kortelėmis, išsaugotomis iš ankstesnės "
"sesijos."

msgid "Tools"
msgstr "Įrankiai"

msgid "Test configured authentication sources "
msgstr "Testuoti nustatytus autentifikavimo šaltinius"

msgid ""
"You appear to have disabled cookies in your browser. Please check the "
"settings in your browser, and try again."
msgstr ""
"Atrodo Jūsų naršyklė nepalaiko slapukų. Patikrinkite naršyklės nustatymus"
" ir bandykite dar kartą."

msgid "Installing SimpleSAMLphp"
msgstr "Diegiamas SimpleSAMLphp"

msgid "Deprecated"
msgstr "Nebepalaikoma"

msgid ""
"<strong>Congratulations</strong>, you have successfully installed "
"SimpleSAMLphp. This is the start page of your installation, where you "
"will find links to test examples, diagnostics, metadata and even links to"
" relevant documentation."
msgstr ""
"<strong>Sveikiname</strong>, Jūs sėkmingai įdiegėte SimpleSAMLphp. Tai "
"pradinis diegimo puslapis, kur Jūs rasite nuorodas į testavimo "
"pavyzdžius, diagnostiką, metaduomenis ir netgi nuorodas į susijusią "
"dokumentaciją."

msgid "This error may be caused by:"
msgstr "Šią klaidą galėjo sukelti:"

msgid ""
"<strong>You are not using HTTPS</strong> - encrypted communication with "
"the user. HTTP works fine for test purposes, but in a production "
"environment, you should use HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-"
"maintenance\">Read more about SimpleSAMLphp maintenance</a> ]"
msgstr ""
"<strong>Jūs nenaudojate HTTPS</strong> - šifruotos komunikacijos su "
"vartotoju. HTTP puikiai tinka testavimo reikmėms, tačiau realioje "
"aplinkoje turėtumėte naudoti HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">Skaityti daugiau apie SimpleSAMLphp priežiūrą</a> ]"

msgid "Metadata"
msgstr "Metaduomenys"

msgid "Retry"
msgstr "Bandyti dar kartą"

msgid "SimpleSAMLphp Maintenance and Configuration"
msgstr "SimpleSAMLphp valdymas ir konfigūracija"

msgid "Diagnostics on hostname, port and protocol"
msgstr "Serverio vardo, porto ir protokolo diagnostika"

msgid ""
"If you are an user who received this error after following a link on a "
"site, you should report this error to the owner of that site."
msgstr ""
"Jei Jūs esate naudotojas, kuris gavote šią klaidą spragtelėjęs nuorodą "
"tinklapyje, Jūs turėtumėte informuoti tinklapio administratorių apie šią "
"klaidą."

msgid "Using SimpleSAMLphp as an Identity Provider"
msgstr "SimpleSAMLphp naudojimas kaip tapatybės teikėją"

msgid "Optional"
msgstr "Neprivalomas"

msgid "Suggestions for resolving this problem:"
msgstr "Pasiūlymai spręsti šią problemą:"

msgid ""
"This SimpleSAMLphp thing is pretty cool, where can I read more about it? "
"You can find more information about it at the <a "
"href=\"https://simplesamlphp.org/\">SimpleSAMLphp web page </a> over at "
"<a href=\"http://uninett.no\">UNINETT</a>."
msgstr ""
"Šis SimpleSAMLphp dalykas yra gana puikus, kur galėčiau daugiau apie jį "
"paskaityti? Daugiau informacijos apie <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp galite rasti "
"Feide RnD blog'e</a> bei <a href=\"http://uninett.no\">UNINETT</a> "
"svetainėje."

msgid "Shibboleth 1.3 SP example - test logging in through your Shib IdP"
msgstr ""
"Shibboleth 1.3 SP pavyzdys - istorinių duomenų testavimas kartu su Jūsų "
"Shib IdP"

msgid "Authentication"
msgstr "Autentifikavimas"

msgid "SimpleSAMLphp installation page"
msgstr "SimpleSAMLphp diegimo puslapis"

msgid "Show metadata"
msgstr "Parodyti metaduomenis"

msgid "SimpleSAMLphp as an IdP for Google Apps for Education"
msgstr "SimpleSAMLphp kaip IdP skirtas \"Google Apps for Education\""

msgid "State information lost"
msgstr "Būsenos informacia prarasta"

msgid "Hosted SAML 2.0 Identity Provider Metadata (automatically generated)"
msgstr "Vietinio SAML 2.0 IdP metaduomenys (sugeneruoti automatiškai)"

msgid "OpenID Provider site - Alpha version (test code)"
msgstr "OpenID tiekėjo puslapis - alfa versija (bandomasis kodas)"

msgid "Required for Radius"
msgstr "Būtinas Radius serveriui"

msgid "We were unable to locate the metadata for the entity:"
msgstr "Nepavyko nustatyti metaduomenų šiems objektams:"

msgid "SAML 2.0 SP example - test logging in through your IdP"
msgstr "SAML 2.0 SP pavyzdys - testinis prisijungimas per Jūsų IdP"

msgid "Using SimpleSAMLphp as a Service Provider"
msgstr "SimpleSAMLphp naudojimas kaip paslaugos teikėją"

msgid ""
"We have detected that there is only a few seconds since you last "
"authenticated with this service provider, and therefore assume that there"
" is a problem with this SP."
msgstr ""
"Mes nustatėme, kad praėjo tik kelios sekundės nuo Jūsų autentiškumo "
"patvirtimo šiam paslaugos teikėjui, dėl to manome, kad yra nesklandumų su"
" SP."

msgid "Recommended"
msgstr "Rekomenduojamas"

msgid ""
"If you are a developer who is deploying a single sign-on solution, you "
"have a problem with the metadata configuration. Verify that metadata is "
"configured correctly on both the identity provider and service provider."
msgstr ""
"Jei Jūs esate kūrėjas, kuris diegiate SSO sprendimą, Jums iškilo problema"
" susijusi su metaduomenų konfigūracija. Patikrinkite, ar metaduomenys "
"teisingai sukonfigūruoti tiek tapatybių teikėjo, tiek paslaugos teikėjo "
"pusėse."

msgid "SimpleSAMLphp Advanced Features"
msgstr "SimpleSAMLphp papildomos galimybės"

msgid "Too short interval between single sign on events."
msgstr "Per trumpas intervalas tarp prisijungimų prie paslaugų."

msgid "Checking your PHP installation"
msgstr "Tikrinamas Jūsų PHP diegimas"

msgid ""
"The length of query parameters is limited by the PHP Suhosin extension. "
"Please increase the suhosin.get.max_value_length option to at least 2048 "
"bytes."
msgstr ""
"Užklausos parametrų ilgis ribojamas PHP Suhosin plėtinio. Prašome "
"padidinti suhosin.get.max_value_length rekšmę bent iki 2048 baitų."

msgid "Useful links for your installation"
msgstr "Naudingos nuorodos diegimui"

msgid "Configuration"
msgstr "Konfigūracija"

msgid "Hosted Shibboleth 1.3 Identity Provider Metadata (automatically generated)"
msgstr ""
"Vietinio Shibboleth 1.3 tapatybės teikėjo (IdP) metaduomenys (sugeneruoti"
" automatiškai)"

msgid "Login as administrator"
msgstr "Prisijungti administratoriaus teisėmis"

msgid "Report this error"
msgstr "Pranešti apie šią klaidą"

