{"name": "simplesamlphp/simplesamlphp-module-authyubikey", "description": "A module that is able to authenticate against YubiKey", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "<PERSON><PERSON><PERSON><PERSON>"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "extra": {"ssp-mixedcase-module-name": "auth<PERSON><PERSON><PERSON>"}, "autoload": {"psr-4": {"SimpleSAML\\modules\\yubikey\\": "lib/"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "webmozart/assert": "~1.4"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "phpunit/phpunit": "~5.7"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-authyubikey/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-authyubikey"}}