
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2019-03-20 15:27+0200\n"
"Last-Translator: \n"
"Language: en\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{discopower:tabs:ukacessfederation}"
msgstr "UK Access Federation"

msgid "{discopower:tabs:switzerland}"
msgstr "Switzerland"

msgid "{discopower:tabs:greece}"
msgstr "Greece"

msgid "{discopower:tabs:southafrica}"
msgstr "South Africa"

msgid "{discopower:tabs:kalmar}"
msgstr "Kalmar"

msgid "{discopower:tabs:netherlands}"
msgstr "The Netherlands"

msgid "{discopower:tabs:denmark}"
msgstr "Denmark"

msgid "{discopower:tabs:sweden}"
msgstr "Sweden"

msgid "{discopower:tabs:edugain}"
msgstr "Europe (eduGAIN)"

msgid "{discopower:tabs:iceland}"
msgstr "Iceland"

msgid "{discopower:tabs:finland}"
msgstr "Finland"

msgid "{discopower:tabs:incommon}"
msgstr "InCommon"

msgid "{discopower:tabs:norway}"
msgstr "Norway"

msgid "{discopower:tabs:misc}"
msgstr "Miscellaneous"

msgid "Sweden"
msgstr "Sweden"

msgid "Denmark"
msgstr "Denmark"

msgid "Iceland"
msgstr "Iceland"

msgid "UK Access Federation"
msgstr "UK Access Federation"

msgid "Kalmar"
msgstr "Kalmar"

msgid "Switzerland"
msgstr "Switzerland"

msgid "Norway"
msgstr "Norway"

msgid "Greece"
msgstr "Greece"

msgid "South Africa"
msgstr "South Africa"

msgid "Miscellaneous"
msgstr "Miscellaneous"

msgid "Finland"
msgstr "Finland"

msgid "Europe (eduGAIN)"
msgstr "Europe (eduGAIN)"

msgid "InCommon"
msgstr "InCommon"

msgid "{discopower:tabs:incremental_search}"
msgstr "Incremental search..."

