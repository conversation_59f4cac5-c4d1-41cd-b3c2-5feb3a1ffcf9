{"name": "simplesamlphp/simplesamlphp-module-cas", "description": "A module that provides CAS authentication", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "cas"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload": {"psr-4": {"SimpleSAML\\Module\\cas\\": "lib/"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "simplesamlphp/simplesamlphp-module-ldap": "^0.9", "webmozart/assert": "~1.4"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "phpunit/phpunit": "~5.7"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-cas/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-cas"}}