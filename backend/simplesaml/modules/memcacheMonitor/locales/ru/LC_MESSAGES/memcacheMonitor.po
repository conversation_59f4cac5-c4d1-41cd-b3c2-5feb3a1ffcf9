
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: ru\n"
"Language-Team: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{memcacheMonitor:memcachestat:total_connections}"
msgstr "Всего соединений"

msgid "{memcacheMonitor:memcachestat:version}"
msgstr "Версия"

msgid "{memcacheMonitor:memcachestat:get_hits}"
msgstr "Всего команд GET (успешных)"

msgid "{memcacheMonitor:memcachestat:curr_items}"
msgstr "Текущее количество элементов"

msgid "{memcacheMonitor:memcachestat:uptime}"
msgstr "Время безотказной работы"

msgid "{memcacheMonitor:memcachestat:limit_maxbytes}"
msgstr "Общий доступный объем системы хранения данных"

msgid "{memcacheMonitor:memcachestat:curr_connections}"
msgstr "Текущие открытые соединения"

msgid "{memcacheMonitor:memcachestat:rusage_system}"
msgstr "Процессорное время в секундах (системы)"

msgid "{memcacheMonitor:memcachestat:rusage_user}"
msgstr "Процессорное время в секундах (пользователя)"

msgid "{memcacheMonitor:memcachestat:pid}"
msgstr "ID процесса"

msgid "{memcacheMonitor:memcachestat:cmd_get}"
msgstr "Всего команд GET"

msgid "{memcacheMonitor:memcachestat:bytes_read}"
msgstr "Байт на сервер"

msgid "{memcacheMonitor:memcachestat:time}"
msgstr "Текущее время"

msgid "{memcacheMonitor:memcachestat:get_misses}"
msgstr "Всего команд GET (с ошибкой)"

msgid "{memcacheMonitor:memcachestat:bytes_written}"
msgstr "Байт записано сервером"

msgid "{memcacheMonitor:memcachestat:connection_structures}"
msgstr "Структуры соединений"

msgid "{memcacheMonitor:memcachestat:cmd_set}"
msgstr "Всего команд SET"

msgid "{memcacheMonitor:memcachestat:total_items}"
msgstr "Общее количество элементов за все время"

msgid "{memcacheMonitor:memcachestat:bytes}"
msgstr "Сейчас используется всего байт"

msgid "Current time"
msgstr "Текущее время"

msgid "Total items ever"
msgstr "Общее количество элементов за все время"

msgid "Bytes written by the server"
msgstr "Байт записано сервером"

msgid "Uptime"
msgstr "Время безотказной работы"

msgid "Current open connections"
msgstr "Текущие открытые соединения"

msgid "Total storage avail"
msgstr "Общий доступный объем системы хранения данных"

msgid "Version"
msgstr "Версия"

msgid "Total GET commands (failed)"
msgstr "Всего команд GET (с ошибкой)"

msgid "Total SET commands"
msgstr "Всего команд SET"

msgid "Connection structures"
msgstr "Структуры соединений"

msgid "Total GET commands (success)"
msgstr "Всего команд GET (успешных)"

msgid "Total bytes in use currently"
msgstr "Сейчас используется всего байт"

msgid "Total GET commands"
msgstr "Всего команд GET"

msgid "Bytes in to the server"
msgstr "Байт на сервер"

msgid "Process ID"
msgstr "ID процесса"

msgid "Currently number of items"
msgstr "Текущее количество элементов"

msgid "CPU Seconds (User)"
msgstr "Процессорное время в секундах (пользователя)"

msgid "CPU Seconds (System)"
msgstr "Процессорное время в секундах (системы)"

msgid "Total connections"
msgstr "Всего соединений"

