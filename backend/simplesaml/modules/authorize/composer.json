{"name": "simplesamlphp/simplesamlphp-module-authorize", "description": "This module provides a user authorization filter based on attribute matching", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "authorize"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload": {"psr-4": {"SimpleSAML\\Module\\authorize\\": "lib/"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Module\\Authorize\\Tests\\Utils\\": "tests/Utils", "SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "phpunit/phpunit": "~5.7"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-authorize/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-authorize"}}