{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./node_modules/jquery/dist/jquery.js-exposed?fe57", "webpack:///(webpack)/buildin/global.js", "webpack:///./node_modules/jquery/dist/jquery.js-exposed", "webpack:///./node_modules/jquery/dist/jquery.js", "webpack:///./src/js/logout/logout.js", "webpack:///./src/js/logout/main.js"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "g", "this", "Function", "e", "window", "global", "factory", "document", "w", "Error", "noGlobal", "arr", "getProto", "getPrototypeOf", "slice", "concat", "push", "indexOf", "class2type", "toString", "hasOwn", "fnToString", "ObjectFunctionString", "support", "isFunction", "obj", "nodeType", "isWindow", "preservedScriptAttributes", "type", "src", "nonce", "noModule", "DOMEval", "code", "node", "doc", "val", "script", "createElement", "text", "getAttribute", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "toType", "j<PERSON><PERSON><PERSON>", "selector", "context", "fn", "init", "rtrim", "isArrayLike", "length", "j<PERSON>y", "constructor", "toArray", "num", "pushStack", "elems", "ret", "merge", "prevObject", "each", "callback", "map", "elem", "apply", "arguments", "first", "eq", "last", "len", "j", "end", "sort", "splice", "extend", "options", "copy", "copyIsArray", "clone", "target", "deep", "isPlainObject", "Array", "isArray", "undefined", "expando", "Math", "random", "replace", "isReady", "error", "msg", "noop", "proto", "Ctor", "isEmptyObject", "globalEval", "trim", "makeArray", "results", "inArray", "second", "grep", "invert", "matches", "callbackExpect", "arg", "guid", "iterator", "split", "toLowerCase", "Sizzle", "Expr", "getText", "isXML", "tokenize", "compile", "select", "outermostContext", "sortInput", "hasDuplicate", "setDocument", "doc<PERSON><PERSON>", "documentIsHTML", "rbuggyQSA", "rbuggyMatches", "contains", "Date", "preferredDoc", "dirruns", "done", "classCache", "createCache", "tokenCache", "compilerCache", "nonnativeSelectorCache", "sortOrder", "a", "b", "pop", "push_native", "list", "booleans", "whitespace", "identifier", "attributes", "pseudos", "rwhitespace", "RegExp", "rcomma", "rcombinators", "rdescend", "r<PERSON>udo", "ridentifier", "matchExpr", "rhtml", "rinputs", "rheader", "rnative", "rquickExpr", "rsibling", "runescape", "funescape", "_", "escaped", "escapedWhitespace", "high", "String", "fromCharCode", "rcssescape", "fcssescape", "ch", "asCodePoint", "charCodeAt", "unload<PERSON><PERSON><PERSON>", "inDisabledFieldset", "addCombinator", "disabled", "nodeName", "dir", "next", "childNodes", "els", "seed", "nid", "match", "groups", "newSelector", "newContext", "ownerDocument", "exec", "getElementById", "id", "getElementsByTagName", "getElementsByClassName", "qsa", "test", "toSelector", "join", "testContext", "querySelectorAll", "qsaError", "removeAttribute", "keys", "cache", "cacheLength", "shift", "markFunction", "assert", "el", "addHandle", "attrs", "handler", "attrHandle", "<PERSON><PERSON><PERSON><PERSON>", "cur", "diff", "sourceIndex", "nextS<PERSON>ling", "createInputPseudo", "createButtonPseudo", "createDisabledPseudo", "isDisabled", "createPositionalPseudo", "argument", "matchIndexes", "namespace", "namespaceURI", "documentElement", "hasCompare", "subWindow", "defaultView", "top", "addEventListener", "attachEvent", "className", "createComment", "getById", "getElementsByName", "filter", "attrId", "find", "getAttributeNode", "tag", "tmp", "innerHTML", "input", "matchesSelector", "webkitMatchesSelector", "mozMatchesSelector", "oMatchesSelector", "msMatchesSelector", "disconnectedMatch", "compareDocumentPosition", "adown", "bup", "compare", "sortDetached", "aup", "ap", "bp", "unshift", "expr", "elements", "attr", "specified", "escape", "sel", "uniqueSort", "duplicates", "detectDuplicates", "sortStable", "textContent", "<PERSON><PERSON><PERSON><PERSON>", "nodeValue", "selectors", "createPseudo", "relative", "preFilter", "excess", "unquoted", "nodeNameSelector", "pattern", "operator", "check", "result", "what", "simple", "forward", "ofType", "xml", "uniqueCache", "outerCache", "nodeIndex", "start", "parent", "useCache", "<PERSON><PERSON><PERSON><PERSON>", "uniqueID", "pseudo", "args", "setFilters", "idx", "matched", "matcher", "unmatched", "lang", "elemLang", "hash", "location", "activeElement", "hasFocus", "href", "tabIndex", "checked", "selected", "selectedIndex", "radio", "checkbox", "file", "password", "image", "submit", "reset", "tokens", "combinator", "base", "skip", "checkNonElements", "doneName", "<PERSON><PERSON><PERSON>", "newCache", "elementMatcher", "matchers", "condense", "newUnmatched", "mapped", "set<PERSON><PERSON><PERSON>", "postFilter", "postFinder", "postSelector", "temp", "preMap", "postMap", "preexisting", "contexts", "multipleContexts", "matcherIn", "matcherOut", "matcherFromTokens", "checkContext", "leadingRelative", "implicitRelative", "matchContext", "matchAnyContext", "filters", "parseOnly", "soFar", "preFilters", "cached", "setMatchers", "elementMatchers", "bySet", "byElement", "superMatcher", "outermost", "matchedCount", "setMatched", "contextBackup", "dirrunsUnique", "matcherFromGroupMatchers", "token", "compiled", "defaultValue", "unique", "isXMLDoc", "escapeSelector", "until", "truncate", "is", "siblings", "rneedsContext", "needsContext", "rsingleTag", "winnow", "qualifier", "not", "self", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "root", "parseHTML", "ready", "rparentsprev", "guaranteedUnique", "children", "contents", "prev", "sibling", "has", "targets", "closest", "index", "prevAll", "add", "addBack", "parents", "parentsUntil", "nextAll", "nextUntil", "prevUntil", "contentDocument", "content", "reverse", "rnothtmlwhite", "Identity", "v", "<PERSON>hrow<PERSON>", "ex", "adoptV<PERSON>ue", "resolve", "reject", "noValue", "method", "promise", "fail", "then", "Callbacks", "flag", "createOptions", "firing", "memory", "fired", "locked", "queue", "firingIndex", "fire", "once", "stopOnFalse", "remove", "empty", "disable", "lock", "fireWith", "Deferred", "func", "tuples", "state", "always", "deferred", "pipe", "fns", "new<PERSON><PERSON><PERSON>", "tuple", "returned", "progress", "notify", "onFulfilled", "onRejected", "onProgress", "max<PERSON><PERSON><PERSON>", "depth", "special", "that", "mightThrow", "TypeError", "notifyWith", "resolveWith", "process", "exceptionHook", "stackTrace", "rejectWith", "getStackHook", "setTimeout", "stateString", "when", "singleValue", "remaining", "resolveContexts", "resolveValues", "master", "updateFunc", "rerror<PERSON><PERSON><PERSON>", "stack", "console", "warn", "message", "readyException", "readyList", "completed", "removeEventListener", "catch", "readyWait", "wait", "readyState", "doScroll", "access", "chainable", "emptyGet", "raw", "bulk", "rmsPrefix", "rdashAlpha", "fcamelCase", "all", "letter", "toUpperCase", "camelCase", "string", "acceptData", "owner", "Data", "uid", "configurable", "set", "data", "prop", "hasData", "dataPriv", "dataUser", "r<PERSON>ce", "rmultiDash", "dataAttr", "JSON", "parse", "getData", "removeData", "_data", "_removeData", "dequeue", "startLength", "hooks", "_queueHooks", "stop", "setter", "clearQueue", "count", "defer", "pnum", "source", "rcssNum", "cssExpand", "isAttached", "composed", "getRootNode", "isHiddenWithinTree", "style", "display", "css", "swap", "old", "adjustCSS", "valueParts", "tween", "adjusted", "scale", "maxIterations", "currentValue", "initial", "unit", "cssNumber", "initialInUnit", "defaultDisplayMap", "getDefaultDisplay", "body", "showHide", "show", "values", "hide", "toggle", "rcheckableType", "rtagName", "rscriptType", "wrapMap", "option", "thead", "col", "tr", "td", "_default", "getAll", "setGlobalEval", "refElements", "optgroup", "tbody", "tfoot", "colgroup", "caption", "th", "div", "buildFragment", "scripts", "selection", "ignored", "wrap", "attached", "fragment", "createDocumentFragment", "nodes", "htmlPrefilter", "createTextNode", "checkClone", "cloneNode", "noCloneChecked", "rkeyEvent", "rmouseEvent", "rtypenamespace", "returnTrue", "returnFalse", "expectSync", "err", "safeActiveElement", "on", "types", "one", "origFn", "event", "off", "leverageNative", "notAsync", "saved", "isTrigger", "delegateType", "stopPropagation", "stopImmediatePropagation", "preventDefault", "trigger", "Event", "handleObjIn", "eventHandle", "events", "handleObj", "handlers", "namespaces", "origType", "elemData", "handle", "triggered", "dispatch", "bindType", "delegateCount", "setup", "mappedTypes", "origCount", "teardown", "removeEvent", "nativeEvent", "handler<PERSON><PERSON>ue", "fix", "<PERSON><PERSON><PERSON><PERSON>", "preDispatch", "isPropagationStopped", "currentTarget", "isImmediatePropagationStopped", "rnamespace", "postDispatch", "matchedHandlers", "matchedSelectors", "button", "addProp", "hook", "originalEvent", "writable", "load", "noBubble", "click", "beforeunload", "returnValue", "props", "isDefaultPrevented", "defaultPrevented", "relatedTarget", "timeStamp", "now", "isSimulated", "altKey", "bubbles", "cancelable", "changedTouches", "ctrl<PERSON>ey", "detail", "eventPhase", "metaKey", "pageX", "pageY", "shift<PERSON>ey", "view", "charCode", "keyCode", "buttons", "clientX", "clientY", "offsetX", "offsetY", "pointerId", "pointerType", "screenX", "screenY", "targetTouches", "toElement", "touches", "which", "focus", "blur", "mouseenter", "mouseleave", "pointerenter", "pointerleave", "orig", "related", "rxhtmlTag", "rnoInnerhtml", "rchecked", "rcleanScript", "<PERSON><PERSON><PERSON><PERSON>", "disableScript", "restoreScript", "cloneCopyEvent", "dest", "pdataOld", "pdataCur", "udataOld", "udataCur", "fixInput", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collection", "hasScripts", "iNoClone", "valueIsFunction", "html", "_evalUrl", "keepData", "cleanData", "dataAndEvents", "deepDataAndEvents", "srcElements", "destElements", "inPage", "detach", "append", "prepend", "insertBefore", "before", "after", "replaceWith", "<PERSON><PERSON><PERSON><PERSON>", "appendTo", "prependTo", "insertAfter", "replaceAll", "original", "insert", "rnumnonpx", "getStyles", "opener", "getComputedStyle", "rboxStyle", "curCSS", "computed", "width", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "getPropertyValue", "pixelBoxStyles", "addGetHookIf", "conditionFn", "hookFn", "computeStyleTests", "container", "cssText", "divStyle", "pixelPositionVal", "reliableMarginLeftVal", "roundPixelMeasures", "marginLeft", "right", "pixelBoxStylesVal", "boxSizingReliableVal", "position", "scrollboxSizeVal", "offsetWidth", "measure", "round", "parseFloat", "backgroundClip", "clearCloneStyle", "boxSizingReliable", "pixelPosition", "reliableMarginLeft", "scrollboxSize", "cssPrefixes", "emptyStyle", "vendorProps", "finalPropName", "final", "cssProps", "capName", "vendorPropName", "rdisplayswap", "rcustomProp", "cssShow", "visibility", "cssNormalTransform", "letterSpacing", "fontWeight", "setPositiveNumber", "subtract", "max", "boxModelAdjustment", "dimension", "box", "isBorderBox", "styles", "computedVal", "extra", "delta", "ceil", "getWidthOrHeight", "valueIsBorderBox", "offsetProp", "getClientRects", "Tween", "easing", "cssHooks", "opacity", "origName", "isCustomProp", "setProperty", "isFinite", "getBoundingClientRect", "scrollboxSizeBuggy", "left", "margin", "padding", "border", "prefix", "suffix", "expand", "expanded", "parts", "propHooks", "run", "percent", "eased", "duration", "pos", "step", "fx", "scrollTop", "scrollLeft", "linear", "swing", "cos", "PI", "fxNow", "inProgress", "rfxtypes", "rrun", "schedule", "hidden", "requestAnimationFrame", "interval", "tick", "createFxNow", "genFx", "includeWidth", "height", "createTween", "animation", "Animation", "tweeners", "properties", "stopped", "prefilters", "currentTime", "startTime", "tweens", "opts", "specialEasing", "originalProperties", "originalOptions", "gotoEnd", "propFilter", "complete", "timer", "anim", "tweener", "oldfire", "propTween", "restoreDisplay", "isBox", "dataShow", "unqueued", "overflow", "overflowX", "overflowY", "prefilter", "speed", "opt", "speeds", "fadeTo", "to", "animate", "optall", "doAnimation", "finish", "stopQueue", "timers", "cssFn", "slideDown", "slideUp", "slideToggle", "fadeIn", "fadeOut", "fadeToggle", "slow", "fast", "delay", "time", "timeout", "clearTimeout", "checkOn", "optSelected", "radioValue", "boolHook", "removeAttr", "nType", "attrHooks", "bool", "attrNames", "lowercaseName", "rfocusable", "rclickable", "stripAndCollapse", "getClass", "classesToArray", "removeProp", "propFix", "tabindex", "parseInt", "addClass", "classes", "curValue", "clazz", "finalValue", "removeClass", "toggleClass", "stateVal", "isValidValue", "classNames", "hasClass", "rreturn", "valHooks", "optionSet", "focusin", "rfocusMorph", "stopPropagationCallback", "onlyHandlers", "bubbleType", "ontype", "lastElement", "eventPath", "parentWindow", "simulate", "<PERSON><PERSON><PERSON><PERSON>", "attaches", "r<PERSON>y", "parseXML", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "rbra<PERSON>", "rCRLF", "rsubmitterTypes", "rsubmittable", "buildParams", "traditional", "param", "valueOrFunction", "encodeURIComponent", "serialize", "serializeArray", "r20", "rhash", "ranti<PERSON><PERSON>", "rheaders", "rno<PERSON><PERSON>nt", "rprotocol", "transports", "allTypes", "originAnchor", "addToPrefiltersOrTransports", "structure", "dataTypeExpression", "dataType", "dataTypes", "inspectPrefiltersOrTransports", "jqXHR", "inspected", "seekingTransport", "inspect", "prefilterOrFactory", "dataTypeOrTransport", "ajaxExtend", "flatOptions", "ajaxSettings", "active", "lastModified", "etag", "url", "isLocal", "protocol", "processData", "async", "contentType", "accepts", "json", "responseFields", "converters", "ajaxSetup", "settings", "ajaxPrefilter", "ajaxTransport", "ajax", "transport", "cacheURL", "responseHeadersString", "responseHeaders", "timeoutTimer", "urlAnchor", "fireGlobals", "uncached", "callbackContext", "globalEventContext", "completeDeferred", "statusCode", "requestHeaders", "requestHeadersNames", "strAbort", "getResponseHeader", "getAllResponseHeaders", "setRequestHeader", "overrideMimeType", "mimeType", "status", "abort", "statusText", "finalText", "crossDomain", "host", "<PERSON><PERSON><PERSON><PERSON>", "ifModified", "headers", "beforeSend", "success", "send", "nativeStatusText", "responses", "isSuccess", "response", "modified", "ct", "finalDataType", "firstDataType", "ajaxHandleResponses", "conv2", "current", "conv", "dataFilter", "throws", "ajaxConvert", "getJSON", "getScript", "wrapAll", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "wrapInner", "htmlIsFunction", "unwrap", "visible", "offsetHeight", "xhr", "XMLHttpRequest", "xhrSuccessStatus", "0", "1223", "xhrSupported", "cors", "<PERSON><PERSON><PERSON><PERSON>", "open", "username", "xhrFields", "onload", "onerror", "<PERSON>ab<PERSON>", "ontimeout", "onreadystatechange", "responseType", "responseText", "binary", "scriptAttrs", "charset", "scriptCharset", "evt", "oldCallbacks", "rjsonp", "jsonp", "jsonpCallback", "originalSettings", "callback<PERSON><PERSON>", "overwritten", "responseContainer", "jsonProp", "createHTMLDocument", "implementation", "keepScripts", "parsed", "params", "animated", "offset", "setOffset", "curPosition", "curL<PERSON>t", "curCSSTop", "curTop", "curOffset", "curCS<PERSON><PERSON><PERSON>", "curE<PERSON>", "using", "rect", "win", "pageYOffset", "pageXOffset", "offsetParent", "parentOffset", "scrollTo", "Height", "<PERSON><PERSON><PERSON>", "defaultExtra", "funcName", "hover", "fnOver", "fnOut", "unbind", "delegate", "undelegate", "proxy", "hold<PERSON><PERSON>y", "hold", "parseJSON", "isNumeric", "isNaN", "_j<PERSON><PERSON>y", "_$", "$", "noConflict", "SimpleSAMLLogout", "page", "populateData", "sps", "btncontinue", "b<PERSON><PERSON>l", "initLogout", "clearAssociation", "spId", "postMessage", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "origin", "failed", "nfailed", "icon", "element", "reason", "errmsg", "errfrm", "actions", "hostname", "port", "btncancel", "for<PERSON>ach", "getTime", "iframe", "initTimeout"], "mappings": "aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,I,mBClFrD,YAAAhC,EAAA,iBAAoC,EAAQ,K,+BCA5C,IAAIiC,EAGJA,EAAI,WACH,OAAOC,KADJ,GAIJ,IAECD,EAAIA,GAAK,IAAIE,SAAS,cAAb,GACR,MAAOC,GAEc,iBAAXC,SAAqBJ,EAAII,QAOrCrC,EAAOD,QAAUkC,G,kBCnBjB,YAAAjC,EAAA,YAA+B,EAAQ,K,iCCAvC;;;;;;;;;;;;;IAaA,SAAYsC,EAAQC,GAEnB,aAE6D,iBAAnBvC,EAAOD,QAShDC,EAAOD,QAAUuC,EAAOE,SACvBD,EAASD,GAAQ,GACjB,SAAUG,GACT,IAAMA,EAAED,SACP,MAAM,IAAIE,MAAO,4CAElB,OAAOH,EAASE,IAGlBF,EAASD,GAtBX,CA0BuB,oBAAXD,OAAyBA,OAASH,MAAM,SAAUG,EAAQM,GAMtE,aAEA,IAAIC,EAAM,GAENJ,EAAWH,EAAOG,SAElBK,EAAWlC,OAAOmC,eAElBC,EAAQH,EAAIG,MAEZC,EAASJ,EAAII,OAEbC,EAAOL,EAAIK,KAEXC,EAAUN,EAAIM,QAEdC,EAAa,GAEbC,EAAWD,EAAWC,SAEtBC,EAASF,EAAWrB,eAEpBwB,EAAaD,EAAOD,SAEpBG,EAAuBD,EAAWlD,KAAMO,QAExC6C,EAAU,GAEVC,EAAa,SAAqBC,GAMhC,MAAsB,mBAARA,GAA8C,iBAAjBA,EAAIC,UAIjDC,EAAW,SAAmBF,GAChC,OAAc,MAAPA,GAAeA,IAAQA,EAAIrB,QAM/BwB,EAA4B,CAC/BC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,UAAU,GAGX,SAASC,EAASC,EAAMC,EAAMC,GAG7B,IAAIpE,EAAGqE,EACNC,GAHDF,EAAMA,GAAO7B,GAGCgC,cAAe,UAG7B,GADAD,EAAOE,KAAON,EACTC,EACJ,IAAMnE,KAAK4D,GAYVS,EAAMF,EAAMnE,IAAOmE,EAAKM,cAAgBN,EAAKM,aAAczE,KAE1DsE,EAAOI,aAAc1E,EAAGqE,GAI3BD,EAAIO,KAAKC,YAAaN,GAASO,WAAWC,YAAaR,GAIzD,SAASS,EAAQtB,GAChB,OAAY,MAAPA,EACGA,EAAM,GAIQ,iBAARA,GAAmC,mBAARA,EACxCP,EAAYC,EAAShD,KAAMsD,KAAW,gBAC/BA,EAQT,IAICuB,EAAS,SAAUC,EAAUC,GAI5B,OAAO,IAAIF,EAAOG,GAAGC,KAAMH,EAAUC,IAKtCG,EAAQ,qCAmVT,SAASC,EAAa7B,GAMrB,IAAI8B,IAAW9B,GAAO,WAAYA,GAAOA,EAAI8B,OAC5C1B,EAAOkB,EAAQtB,GAEhB,OAAKD,EAAYC,KAASE,EAAUF,KAIpB,UAATI,GAA+B,IAAX0B,GACR,iBAAXA,GAAuBA,EAAS,GAAOA,EAAS,KAAO9B,GA/VhEuB,EAAOG,GAAKH,EAAOpD,UAAY,CAG9B4D,OAjBU,QAmBVC,YAAaT,EAGbO,OAAQ,EAERG,QAAS,WACR,OAAO5C,EAAM3C,KAAM8B,OAKpBpB,IAAK,SAAU8E,GAGd,OAAY,MAAPA,EACG7C,EAAM3C,KAAM8B,MAIb0D,EAAM,EAAI1D,KAAM0D,EAAM1D,KAAKsD,QAAWtD,KAAM0D,IAKpDC,UAAW,SAAUC,GAGpB,IAAIC,EAAMd,EAAOe,MAAO9D,KAAKwD,cAAeI,GAM5C,OAHAC,EAAIE,WAAa/D,KAGV6D,GAIRG,KAAM,SAAUC,GACf,OAAOlB,EAAOiB,KAAMhE,KAAMiE,IAG3BC,IAAK,SAAUD,GACd,OAAOjE,KAAK2D,UAAWZ,EAAOmB,IAAKlE,MAAM,SAAUmE,EAAMpG,GACxD,OAAOkG,EAAS/F,KAAMiG,EAAMpG,EAAGoG,QAIjCtD,MAAO,WACN,OAAOb,KAAK2D,UAAW9C,EAAMuD,MAAOpE,KAAMqE,aAG3CC,MAAO,WACN,OAAOtE,KAAKuE,GAAI,IAGjBC,KAAM,WACL,OAAOxE,KAAKuE,IAAK,IAGlBA,GAAI,SAAUxG,GACb,IAAI0G,EAAMzE,KAAKsD,OACdoB,GAAK3G,GAAMA,EAAI,EAAI0G,EAAM,GAC1B,OAAOzE,KAAK2D,UAAWe,GAAK,GAAKA,EAAID,EAAM,CAAEzE,KAAM0E,IAAQ,KAG5DC,IAAK,WACJ,OAAO3E,KAAK+D,YAAc/D,KAAKwD,eAKhCzC,KAAMA,EACN6D,KAAMlE,EAAIkE,KACVC,OAAQnE,EAAImE,QAGb9B,EAAO+B,OAAS/B,EAAOG,GAAG4B,OAAS,WAClC,IAAIC,EAASzG,EAAMuD,EAAKmD,EAAMC,EAAaC,EAC1CC,EAASd,UAAW,IAAO,GAC3BtG,EAAI,EACJuF,EAASe,UAAUf,OACnB8B,GAAO,EAsBR,IAnBuB,kBAAXD,IACXC,EAAOD,EAGPA,EAASd,UAAWtG,IAAO,GAC3BA,KAIsB,iBAAXoH,GAAwB5D,EAAY4D,KAC/CA,EAAS,IAILpH,IAAMuF,IACV6B,EAASnF,KACTjC,KAGOA,EAAIuF,EAAQvF,IAGnB,GAAqC,OAA9BgH,EAAUV,UAAWtG,IAG3B,IAAMO,KAAQyG,EACbC,EAAOD,EAASzG,GAIF,cAATA,GAAwB6G,IAAWH,IAKnCI,GAAQJ,IAAUjC,EAAOsC,cAAeL,KAC1CC,EAAcK,MAAMC,QAASP,MAC/BnD,EAAMsD,EAAQ7G,GAIb4G,EADID,IAAgBK,MAAMC,QAAS1D,GAC3B,GACIoD,GAAgBlC,EAAOsC,cAAexD,GAG1CA,EAFA,GAIToD,GAAc,EAGdE,EAAQ7G,GAASyE,EAAO+B,OAAQM,EAAMF,EAAOF,SAGzBQ,IAATR,IACXG,EAAQ7G,GAAS0G,IAOrB,OAAOG,GAGRpC,EAAO+B,OAAQ,CAGdW,QAAS,UA1KC,QA0KsBC,KAAKC,UAAWC,QAAS,MAAO,IAGhEC,SAAS,EAETC,MAAO,SAAUC,GAChB,MAAM,IAAIvF,MAAOuF,IAGlBC,KAAM,aAENX,cAAe,SAAU7D,GACxB,IAAIyE,EAAOC,EAIX,SAAM1E,GAAgC,oBAAzBN,EAAShD,KAAMsD,QAI5ByE,EAAQtF,EAAUa,KASK,mBADvB0E,EAAO/E,EAAOjD,KAAM+H,EAAO,gBAAmBA,EAAMzC,cACfpC,EAAWlD,KAAMgI,KAAW7E,IAGlE8E,cAAe,SAAU3E,GACxB,IAAIlD,EAEJ,IAAMA,KAAQkD,EACb,OAAO,EAER,OAAO,GAIR4E,WAAY,SAAUnE,EAAM8C,GAC3B/C,EAASC,EAAM,CAAEH,MAAOiD,GAAWA,EAAQjD,SAG5CkC,KAAM,SAAUxC,EAAKyC,GACpB,IAAIX,EAAQvF,EAAI,EAEhB,GAAKsF,EAAa7B,GAEjB,IADA8B,EAAS9B,EAAI8B,OACLvF,EAAIuF,IACqC,IAA3CW,EAAS/F,KAAMsD,EAAKzD,GAAKA,EAAGyD,EAAKzD,IADnBA,UAMpB,IAAMA,KAAKyD,EACV,IAAgD,IAA3CyC,EAAS/F,KAAMsD,EAAKzD,GAAKA,EAAGyD,EAAKzD,IACrC,MAKH,OAAOyD,GAIR6E,KAAM,SAAU9D,GACf,OAAe,MAARA,EACN,IACEA,EAAO,IAAKqD,QAASxC,EAAO,KAIhCkD,UAAW,SAAU5F,EAAK6F,GACzB,IAAI1C,EAAM0C,GAAW,GAarB,OAXY,MAAP7F,IACC2C,EAAa5E,OAAQiC,IACzBqC,EAAOe,MAAOD,EACE,iBAARnD,EACP,CAAEA,GAAQA,GAGXK,EAAK7C,KAAM2F,EAAKnD,IAIXmD,GAGR2C,QAAS,SAAUrC,EAAMzD,EAAK3C,GAC7B,OAAc,MAAP2C,GAAe,EAAIM,EAAQ9C,KAAMwC,EAAKyD,EAAMpG,IAKpD+F,MAAO,SAAUQ,EAAOmC,GAKvB,IAJA,IAAIhC,GAAOgC,EAAOnD,OACjBoB,EAAI,EACJ3G,EAAIuG,EAAMhB,OAEHoB,EAAID,EAAKC,IAChBJ,EAAOvG,KAAQ0I,EAAQ/B,GAKxB,OAFAJ,EAAMhB,OAASvF,EAERuG,GAGRoC,KAAM,SAAU9C,EAAOK,EAAU0C,GAShC,IARA,IACCC,EAAU,GACV7I,EAAI,EACJuF,EAASM,EAAMN,OACfuD,GAAkBF,EAIX5I,EAAIuF,EAAQvF,KACAkG,EAAUL,EAAO7F,GAAKA,KAChB8I,GACxBD,EAAQ7F,KAAM6C,EAAO7F,IAIvB,OAAO6I,GAIR1C,IAAK,SAAUN,EAAOK,EAAU6C,GAC/B,IAAIxD,EAAQtE,EACXjB,EAAI,EACJ8F,EAAM,GAGP,GAAKR,EAAaO,GAEjB,IADAN,EAASM,EAAMN,OACPvF,EAAIuF,EAAQvF,IAGL,OAFdiB,EAAQiF,EAAUL,EAAO7F,GAAKA,EAAG+I,KAGhCjD,EAAI9C,KAAM/B,QAMZ,IAAMjB,KAAK6F,EAGI,OAFd5E,EAAQiF,EAAUL,EAAO7F,GAAKA,EAAG+I,KAGhCjD,EAAI9C,KAAM/B,GAMb,OAAO8B,EAAOsD,MAAO,GAAIP,IAI1BkD,KAAM,EAINzF,QAASA,IAGa,mBAAXxC,SACXiE,EAAOG,GAAIpE,OAAOkI,UAAatG,EAAK5B,OAAOkI,WAI5CjE,EAAOiB,KAAM,uEAAuEiD,MAAO,MAC3F,SAAUlJ,EAAGO,GACZ2C,EAAY,WAAa3C,EAAO,KAAQA,EAAK4I,iBAmB9C,IAAIC;;;;;;;;;;;AAWJ,SAAWhH,GAEX,IAAIpC,EACHuD,EACA8F,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAGAC,EACAvH,EACAwH,EACAC,EACAC,EACAC,EACArB,EACAsB,EAGAzC,EAAU,SAAW,EAAI,IAAI0C,KAC7BC,EAAejI,EAAOG,SACtB+H,EAAU,EACVC,EAAO,EACPC,EAAaC,KACbC,EAAaD,KACbE,EAAgBF,KAChBG,EAAyBH,KACzBI,EAAY,SAAUC,EAAGC,GAIxB,OAHKD,IAAMC,IACVlB,GAAe,GAET,GAIRzG,EAAS,GAAKvB,eACdc,EAAM,GACNqI,EAAMrI,EAAIqI,IACVC,EAActI,EAAIK,KAClBA,EAAOL,EAAIK,KACXF,EAAQH,EAAIG,MAGZG,EAAU,SAAUiI,EAAM9E,GAGzB,IAFA,IAAIpG,EAAI,EACP0G,EAAMwE,EAAK3F,OACJvF,EAAI0G,EAAK1G,IAChB,GAAKkL,EAAKlL,KAAOoG,EAChB,OAAOpG,EAGT,OAAQ,GAGTmL,EAAW,6HAKXC,EAAa,sBAGbC,EAAa,gCAGbC,EAAa,MAAQF,EAAa,KAAOC,EAAa,OAASD,EAE9D,gBAAkBA,EAElB,2DAA6DC,EAAa,OAASD,EACnF,OAEDG,EAAU,KAAOF,EAAa,wFAKAC,EAAa,eAM3CE,EAAc,IAAIC,OAAQL,EAAa,IAAK,KAC5C/F,EAAQ,IAAIoG,OAAQ,IAAML,EAAa,8BAAgCA,EAAa,KAAM,KAE1FM,EAAS,IAAID,OAAQ,IAAML,EAAa,KAAOA,EAAa,KAC5DO,EAAe,IAAIF,OAAQ,IAAML,EAAa,WAAaA,EAAa,IAAMA,EAAa,KAC3FQ,EAAW,IAAIH,OAAQL,EAAa,MAEpCS,EAAU,IAAIJ,OAAQF,GACtBO,EAAc,IAAIL,OAAQ,IAAMJ,EAAa,KAE7CU,EAAY,CACX,GAAM,IAAIN,OAAQ,MAAQJ,EAAa,KACvC,MAAS,IAAII,OAAQ,QAAUJ,EAAa,KAC5C,IAAO,IAAII,OAAQ,KAAOJ,EAAa,SACvC,KAAQ,IAAII,OAAQ,IAAMH,GAC1B,OAAU,IAAIG,OAAQ,IAAMF,GAC5B,MAAS,IAAIE,OAAQ,yDAA2DL,EAC/E,+BAAiCA,EAAa,cAAgBA,EAC9D,aAAeA,EAAa,SAAU,KACvC,KAAQ,IAAIK,OAAQ,OAASN,EAAW,KAAM,KAG9C,aAAgB,IAAIM,OAAQ,IAAML,EAAa,mDAC9CA,EAAa,mBAAqBA,EAAa,mBAAoB,MAGrEY,EAAQ,SACRC,EAAU,sCACVC,EAAU,SAEVC,EAAU,yBAGVC,EAAa,mCAEbC,GAAW,OAIXC,GAAY,IAAIb,OAAQ,qBAAuBL,EAAa,MAAQA,EAAa,OAAQ,MACzFmB,GAAY,SAAUC,EAAGC,EAASC,GACjC,IAAIC,EAAO,KAAOF,EAAU,MAI5B,OAAOE,GAASA,GAAQD,EACvBD,EACAE,EAAO,EAENC,OAAOC,aAAcF,EAAO,OAE5BC,OAAOC,aAAcF,GAAQ,GAAK,MAAe,KAAPA,EAAe,QAK5DG,GAAa,sDACbC,GAAa,SAAUC,EAAIC,GAC1B,OAAKA,EAGQ,OAAPD,EACG,IAIDA,EAAGlK,MAAO,GAAI,GAAM,KAAOkK,EAAGE,WAAYF,EAAGzH,OAAS,GAAIpC,SAAU,IAAO,IAI5E,KAAO6J,GAOfG,GAAgB,WACfrD,KAGDsD,GAAqBC,IACpB,SAAUjH,GACT,OAAyB,IAAlBA,EAAKkH,UAAqD,aAAhClH,EAAKmH,SAASpE,gBAEhD,CAAEqE,IAAK,aAAcC,KAAM,WAI7B,IACCzK,EAAKqD,MACH1D,EAAMG,EAAM3C,KAAMkK,EAAaqD,YAChCrD,EAAaqD,YAId/K,EAAK0H,EAAaqD,WAAWnI,QAAS7B,SACrC,MAAQvB,GACTa,EAAO,CAAEqD,MAAO1D,EAAI4C,OAGnB,SAAU6B,EAAQuG,GACjB1C,EAAY5E,MAAOe,EAAQtE,EAAM3C,KAAKwN,KAKvC,SAAUvG,EAAQuG,GAIjB,IAHA,IAAIhH,EAAIS,EAAO7B,OACdvF,EAAI,EAEIoH,EAAOT,KAAOgH,EAAI3N,OAC3BoH,EAAO7B,OAASoB,EAAI,IAKvB,SAASyC,GAAQnE,EAAUC,EAASsD,EAASoF,GAC5C,IAAIxN,EAAGJ,EAAGoG,EAAMyH,EAAKC,EAAOC,EAAQC,EACnCC,EAAa/I,GAAWA,EAAQgJ,cAGhCxK,EAAWwB,EAAUA,EAAQxB,SAAW,EAKzC,GAHA8E,EAAUA,GAAW,GAGI,iBAAbvD,IAA0BA,GACxB,IAAbvB,GAA+B,IAAbA,GAA+B,KAAbA,EAEpC,OAAO8E,EAIR,IAAMoF,KAEE1I,EAAUA,EAAQgJ,eAAiBhJ,EAAUmF,KAAmB9H,GACtEuH,EAAa5E,GAEdA,EAAUA,GAAW3C,EAEhByH,GAAiB,CAIrB,GAAkB,KAAbtG,IAAoBoK,EAAQ1B,EAAW+B,KAAMlJ,IAGjD,GAAM7E,EAAI0N,EAAM,IAGf,GAAkB,IAAbpK,EAAiB,CACrB,KAAM0C,EAAOlB,EAAQkJ,eAAgBhO,IAUpC,OAAOoI,EALP,GAAKpC,EAAKiI,KAAOjO,EAEhB,OADAoI,EAAQxF,KAAMoD,GACPoC,OAYT,GAAKyF,IAAe7H,EAAO6H,EAAWG,eAAgBhO,KACrD+J,EAAUjF,EAASkB,IACnBA,EAAKiI,KAAOjO,EAGZ,OADAoI,EAAQxF,KAAMoD,GACPoC,MAKH,IAAKsF,EAAM,GAEjB,OADA9K,EAAKqD,MAAOmC,EAAStD,EAAQoJ,qBAAsBrJ,IAC5CuD,EAGD,IAAMpI,EAAI0N,EAAM,KAAOvK,EAAQgL,wBACrCrJ,EAAQqJ,uBAGR,OADAvL,EAAKqD,MAAOmC,EAAStD,EAAQqJ,uBAAwBnO,IAC9CoI,EAKT,GAAKjF,EAAQiL,MACX5D,EAAwB3F,EAAW,QAClCgF,IAAcA,EAAUwE,KAAMxJ,MAIlB,IAAbvB,GAAqD,WAAnCwB,EAAQqI,SAASpE,eAA8B,CAUlE,GARA6E,EAAc/I,EACdgJ,EAAa/I,EAOK,IAAbxB,GAAkBkI,EAAS6C,KAAMxJ,GAAa,CAYlD,KATM4I,EAAM3I,EAAQT,aAAc,OACjCoJ,EAAMA,EAAIhG,QAASiF,GAAYC,IAE/B7H,EAAQR,aAAc,KAAOmJ,EAAMnG,GAKpC1H,GADA+N,EAASvE,EAAUvE,IACRM,OACHvF,KACP+N,EAAO/N,GAAK,IAAM6N,EAAM,IAAMa,GAAYX,EAAO/N,IAElDgO,EAAcD,EAAOY,KAAM,KAG3BV,EAAa5B,GAASoC,KAAMxJ,IAAc2J,GAAa1J,EAAQL,aAC9DK,EAGF,IAIC,OAHAlC,EAAKqD,MAAOmC,EACXyF,EAAWY,iBAAkBb,IAEvBxF,EACN,MAAQsG,GACTlE,EAAwB3F,GAAU,GACjC,QACI4I,IAAQnG,GACZxC,EAAQ6J,gBAAiB,QAQ9B,OAAOrF,EAAQzE,EAAS4C,QAASxC,EAAO,MAAQH,EAASsD,EAASoF,GASnE,SAASnD,KACR,IAAIuE,EAAO,GAUX,OARA,SAASC,EAAO1N,EAAKN,GAMpB,OAJK+N,EAAKhM,KAAMzB,EAAM,KAAQ8H,EAAK6F,oBAE3BD,EAAOD,EAAKG,SAEZF,EAAO1N,EAAM,KAAQN,GAS/B,SAASmO,GAAcjK,GAEtB,OADAA,EAAIuC,IAAY,EACTvC,EAOR,SAASkK,GAAQlK,GAChB,IAAImK,EAAK/M,EAASgC,cAAc,YAEhC,IACC,QAASY,EAAImK,GACZ,MAAOnN,GACR,OAAO,EACN,QAEImN,EAAGzK,YACPyK,EAAGzK,WAAWC,YAAawK,GAG5BA,EAAK,MASP,SAASC,GAAWC,EAAOC,GAI1B,IAHA,IAAI9M,EAAM6M,EAAMtG,MAAM,KACrBlJ,EAAI2C,EAAI4C,OAEDvF,KACPqJ,EAAKqG,WAAY/M,EAAI3C,IAAOyP,EAU9B,SAASE,GAAc7E,EAAGC,GACzB,IAAI6E,EAAM7E,GAAKD,EACd+E,EAAOD,GAAsB,IAAf9E,EAAEpH,UAAiC,IAAfqH,EAAErH,UACnCoH,EAAEgF,YAAc/E,EAAE+E,YAGpB,GAAKD,EACJ,OAAOA,EAIR,GAAKD,EACJ,KAASA,EAAMA,EAAIG,aAClB,GAAKH,IAAQ7E,EACZ,OAAQ,EAKX,OAAOD,EAAI,GAAK,EAOjB,SAASkF,GAAmBnM,GAC3B,OAAO,SAAUuC,GAEhB,MAAgB,UADLA,EAAKmH,SAASpE,eACE/C,EAAKvC,OAASA,GAQ3C,SAASoM,GAAoBpM,GAC5B,OAAO,SAAUuC,GAChB,IAAI7F,EAAO6F,EAAKmH,SAASpE,cACzB,OAAiB,UAAT5I,GAA6B,WAATA,IAAsB6F,EAAKvC,OAASA,GAQlE,SAASqM,GAAsB5C,GAG9B,OAAO,SAAUlH,GAKhB,MAAK,SAAUA,EASTA,EAAKvB,aAAgC,IAAlBuB,EAAKkH,SAGvB,UAAWlH,EACV,UAAWA,EAAKvB,WACbuB,EAAKvB,WAAWyI,WAAaA,EAE7BlH,EAAKkH,WAAaA,EAMpBlH,EAAK+J,aAAe7C,GAI1BlH,EAAK+J,cAAgB7C,GACpBF,GAAoBhH,KAAWkH,EAG3BlH,EAAKkH,WAAaA,EAKd,UAAWlH,GACfA,EAAKkH,WAAaA,GAY5B,SAAS8C,GAAwBjL,GAChC,OAAOiK,IAAa,SAAUiB,GAE7B,OADAA,GAAYA,EACLjB,IAAa,SAAUxB,EAAM/E,GAMnC,IALA,IAAIlC,EACH2J,EAAenL,EAAI,GAAIyI,EAAKrI,OAAQ8K,GACpCrQ,EAAIsQ,EAAa/K,OAGVvF,KACF4N,EAAOjH,EAAI2J,EAAatQ,MAC5B4N,EAAKjH,KAAOkC,EAAQlC,GAAKiH,EAAKjH,WAYnC,SAASiI,GAAa1J,GACrB,OAAOA,QAAmD,IAAjCA,EAAQoJ,sBAAwCpJ,EAujC1E,IAAMlF,KAnjCNuD,EAAU6F,GAAO7F,QAAU,GAO3BgG,EAAQH,GAAOG,MAAQ,SAAUnD,GAChC,IAAImK,EAAYnK,EAAKoK,aACpBzG,GAAW3D,EAAK8H,eAAiB9H,GAAMqK,gBAKxC,OAAQzE,EAAMyC,KAAM8B,GAAaxG,GAAWA,EAAQwD,UAAY,SAQjEzD,EAAcV,GAAOU,YAAc,SAAU3F,GAC5C,IAAIuM,EAAYC,EACfvM,EAAMD,EAAOA,EAAK+J,eAAiB/J,EAAOkG,EAG3C,OAAKjG,IAAQ7B,GAA6B,IAAjB6B,EAAIV,UAAmBU,EAAIqM,iBAMpD1G,GADAxH,EAAW6B,GACQqM,gBACnBzG,GAAkBT,EAAOhH,GAIpB8H,IAAiB9H,IACpBoO,EAAYpO,EAASqO,cAAgBD,EAAUE,MAAQF,IAGnDA,EAAUG,iBACdH,EAAUG,iBAAkB,SAAU3D,IAAe,GAG1CwD,EAAUI,aACrBJ,EAAUI,YAAa,WAAY5D,KAUrC5J,EAAQ+H,WAAa+D,IAAO,SAAUC,GAErC,OADAA,EAAG0B,UAAY,KACP1B,EAAG7K,aAAa,gBAOzBlB,EAAQ+K,qBAAuBe,IAAO,SAAUC,GAE/C,OADAA,EAAG1K,YAAarC,EAAS0O,cAAc,MAC/B3B,EAAGhB,qBAAqB,KAAK/I,UAItChC,EAAQgL,uBAAyBpC,EAAQsC,KAAMlM,EAASgM,wBAMxDhL,EAAQ2N,QAAU7B,IAAO,SAAUC,GAElC,OADAvF,EAAQnF,YAAa0K,GAAKjB,GAAK3G,GACvBnF,EAAS4O,oBAAsB5O,EAAS4O,kBAAmBzJ,GAAUnC,UAIzEhC,EAAQ2N,SACZ7H,EAAK+H,OAAW,GAAI,SAAU/C,GAC7B,IAAIgD,EAAShD,EAAGxG,QAASyE,GAAWC,IACpC,OAAO,SAAUnG,GAChB,OAAOA,EAAK3B,aAAa,QAAU4M,IAGrChI,EAAKiI,KAAS,GAAI,SAAUjD,EAAInJ,GAC/B,QAAuC,IAA3BA,EAAQkJ,gBAAkCpE,EAAiB,CACtE,IAAI5D,EAAOlB,EAAQkJ,eAAgBC,GACnC,OAAOjI,EAAO,CAAEA,GAAS,OAI3BiD,EAAK+H,OAAW,GAAK,SAAU/C,GAC9B,IAAIgD,EAAShD,EAAGxG,QAASyE,GAAWC,IACpC,OAAO,SAAUnG,GAChB,IAAIjC,OAAwC,IAA1BiC,EAAKmL,kBACtBnL,EAAKmL,iBAAiB,MACvB,OAAOpN,GAAQA,EAAKlD,QAAUoQ,IAMhChI,EAAKiI,KAAS,GAAI,SAAUjD,EAAInJ,GAC/B,QAAuC,IAA3BA,EAAQkJ,gBAAkCpE,EAAiB,CACtE,IAAI7F,EAAMnE,EAAG6F,EACZO,EAAOlB,EAAQkJ,eAAgBC,GAEhC,GAAKjI,EAAO,CAIX,IADAjC,EAAOiC,EAAKmL,iBAAiB,QAChBpN,EAAKlD,QAAUoN,EAC3B,MAAO,CAAEjI,GAMV,IAFAP,EAAQX,EAAQiM,kBAAmB9C,GACnCrO,EAAI,EACKoG,EAAOP,EAAM7F,MAErB,IADAmE,EAAOiC,EAAKmL,iBAAiB,QAChBpN,EAAKlD,QAAUoN,EAC3B,MAAO,CAAEjI,GAKZ,MAAO,MAMViD,EAAKiI,KAAU,IAAI/N,EAAQ+K,qBAC1B,SAAUkD,EAAKtM,GACd,YAA6C,IAAjCA,EAAQoJ,qBACZpJ,EAAQoJ,qBAAsBkD,GAG1BjO,EAAQiL,IACZtJ,EAAQ2J,iBAAkB2C,QAD3B,GAKR,SAAUA,EAAKtM,GACd,IAAIkB,EACHqL,EAAM,GACNzR,EAAI,EAEJwI,EAAUtD,EAAQoJ,qBAAsBkD,GAGzC,GAAa,MAARA,EAAc,CAClB,KAASpL,EAAOoC,EAAQxI,MACA,IAAlBoG,EAAK1C,UACT+N,EAAIzO,KAAMoD,GAIZ,OAAOqL,EAER,OAAOjJ,GAITa,EAAKiI,KAAY,MAAI/N,EAAQgL,wBAA0B,SAAUyC,EAAW9L,GAC3E,QAA+C,IAAnCA,EAAQqJ,wBAA0CvE,EAC7D,OAAO9E,EAAQqJ,uBAAwByC,IAUzC9G,EAAgB,GAOhBD,EAAY,IAEN1G,EAAQiL,IAAMrC,EAAQsC,KAAMlM,EAASsM,qBAG1CQ,IAAO,SAAUC,GAMhBvF,EAAQnF,YAAa0K,GAAKoC,UAAY,UAAYhK,EAAU,qBAC1CA,EAAU,kEAOvB4H,EAAGT,iBAAiB,wBAAwBtJ,QAChD0E,EAAUjH,KAAM,SAAWoI,EAAa,gBAKnCkE,EAAGT,iBAAiB,cAActJ,QACvC0E,EAAUjH,KAAM,MAAQoI,EAAa,aAAeD,EAAW,KAI1DmE,EAAGT,iBAAkB,QAAUnH,EAAU,MAAOnC,QACrD0E,EAAUjH,KAAK,MAMVsM,EAAGT,iBAAiB,YAAYtJ,QACrC0E,EAAUjH,KAAK,YAMVsM,EAAGT,iBAAkB,KAAOnH,EAAU,MAAOnC,QAClD0E,EAAUjH,KAAK,eAIjBqM,IAAO,SAAUC,GAChBA,EAAGoC,UAAY,oFAKf,IAAIC,EAAQpP,EAASgC,cAAc,SACnCoN,EAAMjN,aAAc,OAAQ,UAC5B4K,EAAG1K,YAAa+M,GAAQjN,aAAc,OAAQ,KAIzC4K,EAAGT,iBAAiB,YAAYtJ,QACpC0E,EAAUjH,KAAM,OAASoI,EAAa,eAKS,IAA3CkE,EAAGT,iBAAiB,YAAYtJ,QACpC0E,EAAUjH,KAAM,WAAY,aAK7B+G,EAAQnF,YAAa0K,GAAKhC,UAAW,EACY,IAA5CgC,EAAGT,iBAAiB,aAAatJ,QACrC0E,EAAUjH,KAAM,WAAY,aAI7BsM,EAAGT,iBAAiB,QACpB5E,EAAUjH,KAAK,aAIXO,EAAQqO,gBAAkBzF,EAAQsC,KAAO5F,EAAUkB,EAAQlB,SAChEkB,EAAQ8H,uBACR9H,EAAQ+H,oBACR/H,EAAQgI,kBACRhI,EAAQiI,qBAER3C,IAAO,SAAUC,GAGhB/L,EAAQ0O,kBAAoBpJ,EAAQ1I,KAAMmP,EAAI,KAI9CzG,EAAQ1I,KAAMmP,EAAI,aAClBpF,EAAclH,KAAM,KAAMuI,MAI5BtB,EAAYA,EAAU1E,QAAU,IAAIkG,OAAQxB,EAAU0E,KAAK,MAC3DzE,EAAgBA,EAAc3E,QAAU,IAAIkG,OAAQvB,EAAcyE,KAAK,MAIvE+B,EAAavE,EAAQsC,KAAM1E,EAAQmI,yBAKnC/H,EAAWuG,GAAcvE,EAAQsC,KAAM1E,EAAQI,UAC9C,SAAUW,EAAGC,GACZ,IAAIoH,EAAuB,IAAfrH,EAAEpH,SAAiBoH,EAAE2F,gBAAkB3F,EAClDsH,EAAMrH,GAAKA,EAAElG,WACd,OAAOiG,IAAMsH,MAAWA,GAAwB,IAAjBA,EAAI1O,YAClCyO,EAAMhI,SACLgI,EAAMhI,SAAUiI,GAChBtH,EAAEoH,yBAA8D,GAAnCpH,EAAEoH,wBAAyBE,MAG3D,SAAUtH,EAAGC,GACZ,GAAKA,EACJ,KAASA,EAAIA,EAAElG,YACd,GAAKkG,IAAMD,EACV,OAAO,EAIV,OAAO,GAOTD,EAAY6F,EACZ,SAAU5F,EAAGC,GAGZ,GAAKD,IAAMC,EAEV,OADAlB,GAAe,EACR,EAIR,IAAIwI,GAAWvH,EAAEoH,yBAA2BnH,EAAEmH,wBAC9C,OAAKG,IAYU,GAPfA,GAAYvH,EAAEoD,eAAiBpD,MAAUC,EAAEmD,eAAiBnD,GAC3DD,EAAEoH,wBAAyBnH,GAG3B,KAIExH,EAAQ+O,cAAgBvH,EAAEmH,wBAAyBpH,KAAQuH,EAGxDvH,IAAMvI,GAAYuI,EAAEoD,gBAAkB7D,GAAgBF,EAASE,EAAcS,IACzE,EAEJC,IAAMxI,GAAYwI,EAAEmD,gBAAkB7D,GAAgBF,EAASE,EAAcU,GAC1E,EAIDnB,EACJ3G,EAAS2G,EAAWkB,GAAM7H,EAAS2G,EAAWmB,GAChD,EAGe,EAAVsH,GAAe,EAAI,IAE3B,SAAUvH,EAAGC,GAEZ,GAAKD,IAAMC,EAEV,OADAlB,GAAe,EACR,EAGR,IAAI+F,EACH5P,EAAI,EACJuS,EAAMzH,EAAEjG,WACRuN,EAAMrH,EAAElG,WACR2N,EAAK,CAAE1H,GACP2H,EAAK,CAAE1H,GAGR,IAAMwH,IAAQH,EACb,OAAOtH,IAAMvI,GAAY,EACxBwI,IAAMxI,EAAW,EACjBgQ,GAAO,EACPH,EAAM,EACNxI,EACE3G,EAAS2G,EAAWkB,GAAM7H,EAAS2G,EAAWmB,GAChD,EAGK,GAAKwH,IAAQH,EACnB,OAAOzC,GAAc7E,EAAGC,GAKzB,IADA6E,EAAM9E,EACG8E,EAAMA,EAAI/K,YAClB2N,EAAGE,QAAS9C,GAGb,IADAA,EAAM7E,EACG6E,EAAMA,EAAI/K,YAClB4N,EAAGC,QAAS9C,GAIb,KAAQ4C,EAAGxS,KAAOyS,EAAGzS,IACpBA,IAGD,OAAOA,EAEN2P,GAAc6C,EAAGxS,GAAIyS,EAAGzS,IAGxBwS,EAAGxS,KAAOqK,GAAgB,EAC1BoI,EAAGzS,KAAOqK,EAAe,EACzB,GAGK9H,GA3YCA,GA8YT6G,GAAOP,QAAU,SAAU8J,EAAMC,GAChC,OAAOxJ,GAAQuJ,EAAM,KAAM,KAAMC,IAGlCxJ,GAAOwI,gBAAkB,SAAUxL,EAAMuM,GAMxC,IAJOvM,EAAK8H,eAAiB9H,KAAW7D,GACvCuH,EAAa1D,GAGT7C,EAAQqO,iBAAmB5H,IAC9BY,EAAwB+H,EAAO,QAC7BzI,IAAkBA,EAAcuE,KAAMkE,OACtC1I,IAAkBA,EAAUwE,KAAMkE,IAErC,IACC,IAAI7M,EAAM+C,EAAQ1I,KAAMiG,EAAMuM,GAG9B,GAAK7M,GAAOvC,EAAQ0O,mBAGlB7L,EAAK7D,UAAuC,KAA3B6D,EAAK7D,SAASmB,SAChC,OAAOoC,EAEP,MAAO3D,GACRyI,EAAwB+H,GAAM,GAIhC,OAAOvJ,GAAQuJ,EAAMpQ,EAAU,KAAM,CAAE6D,IAASb,OAAS,GAG1D6D,GAAOe,SAAW,SAAUjF,EAASkB,GAKpC,OAHOlB,EAAQgJ,eAAiBhJ,KAAc3C,GAC7CuH,EAAa5E,GAEPiF,EAAUjF,EAASkB,IAG3BgD,GAAOyJ,KAAO,SAAUzM,EAAM7F,IAEtB6F,EAAK8H,eAAiB9H,KAAW7D,GACvCuH,EAAa1D,GAGd,IAAIjB,EAAKkE,EAAKqG,WAAYnP,EAAK4I,eAE9B9E,EAAMc,GAAM/B,EAAOjD,KAAMkJ,EAAKqG,WAAYnP,EAAK4I,eAC9ChE,EAAIiB,EAAM7F,GAAOyJ,QACjBvC,EAEF,YAAeA,IAARpD,EACNA,EACAd,EAAQ+H,aAAetB,EACtB5D,EAAK3B,aAAclE,IAClB8D,EAAM+B,EAAKmL,iBAAiBhR,KAAU8D,EAAIyO,UAC1CzO,EAAIpD,MACJ,MAGJmI,GAAO2J,OAAS,SAAUC,GACzB,OAAQA,EAAM,IAAInL,QAASiF,GAAYC,KAGxC3D,GAAOrB,MAAQ,SAAUC,GACxB,MAAM,IAAIvF,MAAO,0CAA4CuF,IAO9DoB,GAAO6J,WAAa,SAAUzK,GAC7B,IAAIpC,EACH8M,EAAa,GACbvM,EAAI,EACJ3G,EAAI,EAOL,GAJA6J,GAAgBtG,EAAQ4P,iBACxBvJ,GAAarG,EAAQ6P,YAAc5K,EAAQ1F,MAAO,GAClD0F,EAAQ3B,KAAMgE,GAEThB,EAAe,CACnB,KAASzD,EAAOoC,EAAQxI,MAClBoG,IAASoC,EAASxI,KACtB2G,EAAIuM,EAAWlQ,KAAMhD,IAGvB,KAAQ2G,KACP6B,EAAQ1B,OAAQoM,EAAYvM,GAAK,GAQnC,OAFAiD,EAAY,KAELpB,GAORc,EAAUF,GAAOE,QAAU,SAAUlD,GACpC,IAAIjC,EACH2B,EAAM,GACN9F,EAAI,EACJ0D,EAAW0C,EAAK1C,SAEjB,GAAMA,GAMC,GAAkB,IAAbA,GAA+B,IAAbA,GAA+B,KAAbA,EAAkB,CAGjE,GAAiC,iBAArB0C,EAAKiN,YAChB,OAAOjN,EAAKiN,YAGZ,IAAMjN,EAAOA,EAAKkN,WAAYlN,EAAMA,EAAOA,EAAK2J,YAC/CjK,GAAOwD,EAASlD,QAGZ,GAAkB,IAAb1C,GAA+B,IAAbA,EAC7B,OAAO0C,EAAKmN,eAhBZ,KAASpP,EAAOiC,EAAKpG,MAEpB8F,GAAOwD,EAASnF,GAkBlB,OAAO2B,IAGRuD,EAAOD,GAAOoK,UAAY,CAGzBtE,YAAa,GAEbuE,aAAcrE,GAEdtB,MAAO/B,EAEP2D,WAAY,GAEZ4B,KAAM,GAENoC,SAAU,CACT,IAAK,CAAElG,IAAK,aAAcjH,OAAO,GACjC,IAAK,CAAEiH,IAAK,cACZ,IAAK,CAAEA,IAAK,kBAAmBjH,OAAO,GACtC,IAAK,CAAEiH,IAAK,oBAGbmG,UAAW,CACV,KAAQ,SAAU7F,GAUjB,OATAA,EAAM,GAAKA,EAAM,GAAGjG,QAASyE,GAAWC,IAGxCuB,EAAM,IAAOA,EAAM,IAAMA,EAAM,IAAMA,EAAM,IAAM,IAAKjG,QAASyE,GAAWC,IAExD,OAAbuB,EAAM,KACVA,EAAM,GAAK,IAAMA,EAAM,GAAK,KAGtBA,EAAMhL,MAAO,EAAG,IAGxB,MAAS,SAAUgL,GA6BlB,OAlBAA,EAAM,GAAKA,EAAM,GAAG3E,cAEY,QAA3B2E,EAAM,GAAGhL,MAAO,EAAG,IAEjBgL,EAAM,IACX1E,GAAOrB,MAAO+F,EAAM,IAKrBA,EAAM,KAAQA,EAAM,GAAKA,EAAM,IAAMA,EAAM,IAAM,GAAK,GAAmB,SAAbA,EAAM,IAA8B,QAAbA,EAAM,KACzFA,EAAM,KAAUA,EAAM,GAAKA,EAAM,IAAqB,QAAbA,EAAM,KAGpCA,EAAM,IACjB1E,GAAOrB,MAAO+F,EAAM,IAGdA,GAGR,OAAU,SAAUA,GACnB,IAAI8F,EACHC,GAAY/F,EAAM,IAAMA,EAAM,GAE/B,OAAK/B,EAAiB,MAAE0C,KAAMX,EAAM,IAC5B,MAIHA,EAAM,GACVA,EAAM,GAAKA,EAAM,IAAMA,EAAM,IAAM,GAGxB+F,GAAYhI,EAAQ4C,KAAMoF,KAEpCD,EAASpK,EAAUqK,GAAU,MAE7BD,EAASC,EAAS5Q,QAAS,IAAK4Q,EAAStO,OAASqO,GAAWC,EAAStO,UAGvEuI,EAAM,GAAKA,EAAM,GAAGhL,MAAO,EAAG8Q,GAC9B9F,EAAM,GAAK+F,EAAS/Q,MAAO,EAAG8Q,IAIxB9F,EAAMhL,MAAO,EAAG,MAIzBsO,OAAQ,CAEP,IAAO,SAAU0C,GAChB,IAAIvG,EAAWuG,EAAiBjM,QAASyE,GAAWC,IAAYpD,cAChE,MAA4B,MAArB2K,EACN,WAAa,OAAO,GACpB,SAAU1N,GACT,OAAOA,EAAKmH,UAAYnH,EAAKmH,SAASpE,gBAAkBoE,IAI3D,MAAS,SAAUyD,GAClB,IAAI+C,EAAUvJ,EAAYwG,EAAY,KAEtC,OAAO+C,IACLA,EAAU,IAAItI,OAAQ,MAAQL,EAAa,IAAM4F,EAAY,IAAM5F,EAAa,SACjFZ,EAAYwG,GAAW,SAAU5K,GAChC,OAAO2N,EAAQtF,KAAgC,iBAAnBrI,EAAK4K,WAA0B5K,EAAK4K,gBAA0C,IAAtB5K,EAAK3B,cAAgC2B,EAAK3B,aAAa,UAAY,QAI1J,KAAQ,SAAUlE,EAAMyT,EAAUC,GACjC,OAAO,SAAU7N,GAChB,IAAI8N,EAAS9K,GAAOyJ,KAAMzM,EAAM7F,GAEhC,OAAe,MAAV2T,EACgB,OAAbF,GAEFA,IAINE,GAAU,GAEU,MAAbF,EAAmBE,IAAWD,EACvB,OAAbD,EAAoBE,IAAWD,EAClB,OAAbD,EAAoBC,GAAqC,IAA5BC,EAAOjR,QAASgR,GAChC,OAAbD,EAAoBC,GAASC,EAAOjR,QAASgR,IAAW,EAC3C,OAAbD,EAAoBC,GAASC,EAAOpR,OAAQmR,EAAM1O,UAAa0O,EAClD,OAAbD,GAAsB,IAAME,EAAOrM,QAAS2D,EAAa,KAAQ,KAAMvI,QAASgR,IAAW,EAC9E,OAAbD,IAAoBE,IAAWD,GAASC,EAAOpR,MAAO,EAAGmR,EAAM1O,OAAS,KAAQ0O,EAAQ,QAK3F,MAAS,SAAUpQ,EAAMsQ,EAAM9D,EAAU9J,EAAOE,GAC/C,IAAI2N,EAAgC,QAAvBvQ,EAAKf,MAAO,EAAG,GAC3BuR,EAA+B,SAArBxQ,EAAKf,OAAQ,GACvBwR,EAAkB,YAATH,EAEV,OAAiB,IAAV5N,GAAwB,IAATE,EAGrB,SAAUL,GACT,QAASA,EAAKvB,YAGf,SAAUuB,EAAMlB,EAASqP,GACxB,IAAItF,EAAOuF,EAAaC,EAAYtQ,EAAMuQ,EAAWC,EACpDnH,EAAM4G,IAAWC,EAAU,cAAgB,kBAC3CO,EAASxO,EAAKvB,WACdtE,EAAO+T,GAAUlO,EAAKmH,SAASpE,cAC/B0L,GAAYN,IAAQD,EACpBzE,GAAO,EAER,GAAK+E,EAAS,CAGb,GAAKR,EAAS,CACb,KAAQ5G,GAAM,CAEb,IADArJ,EAAOiC,EACEjC,EAAOA,EAAMqJ,IACrB,GAAK8G,EACJnQ,EAAKoJ,SAASpE,gBAAkB5I,EACd,IAAlB4D,EAAKT,SAEL,OAAO,EAITiR,EAAQnH,EAAe,SAAT3J,IAAoB8Q,GAAS,cAE5C,OAAO,EAMR,GAHAA,EAAQ,CAAEN,EAAUO,EAAOtB,WAAasB,EAAOE,WAG1CT,GAAWQ,GAkBf,IAHAhF,GADA6E,GADAzF,GAHAuF,GAJAC,GADAtQ,EAAOyQ,GACYlN,KAAcvD,EAAMuD,GAAY,KAIzBvD,EAAK4Q,YAC7BN,EAAYtQ,EAAK4Q,UAAa,KAEXlR,IAAU,IACZ,KAAQyG,GAAW2E,EAAO,KACzBA,EAAO,GAC3B9K,EAAOuQ,GAAaE,EAAOlH,WAAYgH,GAE9BvQ,IAASuQ,GAAavQ,GAAQA,EAAMqJ,KAG3CqC,EAAO6E,EAAY,IAAMC,EAAM3J,OAGhC,GAAuB,IAAlB7G,EAAKT,YAAoBmM,GAAQ1L,IAASiC,EAAO,CACrDoO,EAAa3Q,GAAS,CAAEyG,EAASoK,EAAW7E,GAC5C,YAuBF,GAjBKgF,IAYJhF,EADA6E,GADAzF,GAHAuF,GAJAC,GADAtQ,EAAOiC,GACYsB,KAAcvD,EAAMuD,GAAY,KAIzBvD,EAAK4Q,YAC7BN,EAAYtQ,EAAK4Q,UAAa,KAEXlR,IAAU,IACZ,KAAQyG,GAAW2E,EAAO,KAMhC,IAATY,EAEJ,MAAS1L,IAASuQ,GAAavQ,GAAQA,EAAMqJ,KAC3CqC,EAAO6E,EAAY,IAAMC,EAAM3J,UAEzBsJ,EACNnQ,EAAKoJ,SAASpE,gBAAkB5I,EACd,IAAlB4D,EAAKT,cACHmM,IAGGgF,KAKJL,GAJAC,EAAatQ,EAAMuD,KAAcvD,EAAMuD,GAAY,KAIzBvD,EAAK4Q,YAC7BN,EAAYtQ,EAAK4Q,UAAa,KAEnBlR,GAAS,CAAEyG,EAASuF,IAG7B1L,IAASiC,MAUlB,OADAyJ,GAAQpJ,KACQF,GAAWsJ,EAAOtJ,GAAU,GAAKsJ,EAAOtJ,GAAS,KAKrE,OAAU,SAAUyO,EAAQ3E,GAK3B,IAAI4E,EACH9P,EAAKkE,EAAKkC,QAASyJ,IAAY3L,EAAK6L,WAAYF,EAAO7L,gBACtDC,GAAOrB,MAAO,uBAAyBiN,GAKzC,OAAK7P,EAAIuC,GACDvC,EAAIkL,GAIPlL,EAAGI,OAAS,GAChB0P,EAAO,CAAED,EAAQA,EAAQ,GAAI3E,GACtBhH,EAAK6L,WAAWrT,eAAgBmT,EAAO7L,eAC7CiG,IAAa,SAAUxB,EAAM/E,GAI5B,IAHA,IAAIsM,EACHC,EAAUjQ,EAAIyI,EAAMyC,GACpBrQ,EAAIoV,EAAQ7P,OACLvF,KAEP4N,EADAuH,EAAMlS,EAAS2K,EAAMwH,EAAQpV,OACZ6I,EAASsM,GAAQC,EAAQpV,OAG5C,SAAUoG,GACT,OAAOjB,EAAIiB,EAAM,EAAG6O,KAIhB9P,IAIToG,QAAS,CAER,IAAO6D,IAAa,SAAUnK,GAI7B,IAAI0M,EAAQ,GACXnJ,EAAU,GACV6M,EAAU5L,EAASxE,EAAS4C,QAASxC,EAAO,OAE7C,OAAOgQ,EAAS3N,GACf0H,IAAa,SAAUxB,EAAM/E,EAAS3D,EAASqP,GAM9C,IALA,IAAInO,EACHkP,EAAYD,EAASzH,EAAM,KAAM2G,EAAK,IACtCvU,EAAI4N,EAAKrI,OAGFvF,MACDoG,EAAOkP,EAAUtV,MACtB4N,EAAK5N,KAAO6I,EAAQ7I,GAAKoG,OAI5B,SAAUA,EAAMlB,EAASqP,GAKxB,OAJA5C,EAAM,GAAKvL,EACXiP,EAAS1D,EAAO,KAAM4C,EAAK/L,GAE3BmJ,EAAM,GAAK,MACHnJ,EAAQwC,UAInB,IAAOoE,IAAa,SAAUnK,GAC7B,OAAO,SAAUmB,GAChB,OAAOgD,GAAQnE,EAAUmB,GAAOb,OAAS,MAI3C,SAAY6J,IAAa,SAAU5K,GAElC,OADAA,EAAOA,EAAKqD,QAASyE,GAAWC,IACzB,SAAUnG,GAChB,OAASA,EAAKiN,aAAe/J,EAASlD,IAASnD,QAASuB,IAAU,MAWpE,KAAQ4K,IAAc,SAAUmG,GAM/B,OAJMzJ,EAAY2C,KAAK8G,GAAQ,KAC9BnM,GAAOrB,MAAO,qBAAuBwN,GAEtCA,EAAOA,EAAK1N,QAASyE,GAAWC,IAAYpD,cACrC,SAAU/C,GAChB,IAAIoP,EACJ,GACC,GAAMA,EAAWxL,EAChB5D,EAAKmP,KACLnP,EAAK3B,aAAa,aAAe2B,EAAK3B,aAAa,QAGnD,OADA+Q,EAAWA,EAASrM,iBACAoM,GAA2C,IAAnCC,EAASvS,QAASsS,EAAO,YAE5CnP,EAAOA,EAAKvB,aAAiC,IAAlBuB,EAAK1C,UAC3C,OAAO,MAKT,OAAU,SAAU0C,GACnB,IAAIqP,EAAOrT,EAAOsT,UAAYtT,EAAOsT,SAASD,KAC9C,OAAOA,GAAQA,EAAK3S,MAAO,KAAQsD,EAAKiI,IAGzC,KAAQ,SAAUjI,GACjB,OAAOA,IAAS2D,GAGjB,MAAS,SAAU3D,GAClB,OAAOA,IAAS7D,EAASoT,iBAAmBpT,EAASqT,UAAYrT,EAASqT,gBAAkBxP,EAAKvC,MAAQuC,EAAKyP,OAASzP,EAAK0P,WAI7H,QAAW5F,IAAsB,GACjC,SAAYA,IAAsB,GAElC,QAAW,SAAU9J,GAGpB,IAAImH,EAAWnH,EAAKmH,SAASpE,cAC7B,MAAqB,UAAboE,KAA0BnH,EAAK2P,SAA0B,WAAbxI,KAA2BnH,EAAK4P,UAGrF,SAAY,SAAU5P,GAOrB,OAJKA,EAAKvB,YACTuB,EAAKvB,WAAWoR,eAGQ,IAAlB7P,EAAK4P,UAIb,MAAS,SAAU5P,GAKlB,IAAMA,EAAOA,EAAKkN,WAAYlN,EAAMA,EAAOA,EAAK2J,YAC/C,GAAK3J,EAAK1C,SAAW,EACpB,OAAO,EAGT,OAAO,GAGR,OAAU,SAAU0C,GACnB,OAAQiD,EAAKkC,QAAe,MAAGnF,IAIhC,OAAU,SAAUA,GACnB,OAAO8F,EAAQuC,KAAMrI,EAAKmH,WAG3B,MAAS,SAAUnH,GAClB,OAAO6F,EAAQwC,KAAMrI,EAAKmH,WAG3B,OAAU,SAAUnH,GACnB,IAAI7F,EAAO6F,EAAKmH,SAASpE,cACzB,MAAgB,UAAT5I,GAAkC,WAAd6F,EAAKvC,MAA8B,WAATtD,GAGtD,KAAQ,SAAU6F,GACjB,IAAIyM,EACJ,MAAuC,UAAhCzM,EAAKmH,SAASpE,eACN,SAAd/C,EAAKvC,OAImC,OAArCgP,EAAOzM,EAAK3B,aAAa,UAA2C,SAAvBoO,EAAK1J,gBAIvD,MAASiH,IAAuB,WAC/B,MAAO,CAAE,MAGV,KAAQA,IAAuB,SAAUE,EAAc/K,GACtD,MAAO,CAAEA,EAAS,MAGnB,GAAM6K,IAAuB,SAAUE,EAAc/K,EAAQ8K,GAC5D,MAAO,CAAEA,EAAW,EAAIA,EAAW9K,EAAS8K,MAG7C,KAAQD,IAAuB,SAAUE,EAAc/K,GAEtD,IADA,IAAIvF,EAAI,EACAA,EAAIuF,EAAQvF,GAAK,EACxBsQ,EAAatN,KAAMhD,GAEpB,OAAOsQ,KAGR,IAAOF,IAAuB,SAAUE,EAAc/K,GAErD,IADA,IAAIvF,EAAI,EACAA,EAAIuF,EAAQvF,GAAK,EACxBsQ,EAAatN,KAAMhD,GAEpB,OAAOsQ,KAGR,GAAMF,IAAuB,SAAUE,EAAc/K,EAAQ8K,GAM5D,IALA,IAAIrQ,EAAIqQ,EAAW,EAClBA,EAAW9K,EACX8K,EAAW9K,EACVA,EACA8K,IACQrQ,GAAK,GACdsQ,EAAatN,KAAMhD,GAEpB,OAAOsQ,KAGR,GAAMF,IAAuB,SAAUE,EAAc/K,EAAQ8K,GAE5D,IADA,IAAIrQ,EAAIqQ,EAAW,EAAIA,EAAW9K,EAAS8K,IACjCrQ,EAAIuF,GACb+K,EAAatN,KAAMhD,GAEpB,OAAOsQ,QAKL/E,QAAa,IAAIlC,EAAKkC,QAAY,GAG5B,CAAE2K,OAAO,EAAMC,UAAU,EAAMC,MAAM,EAAMC,UAAU,EAAMC,OAAO,GAC5EjN,EAAKkC,QAASvL,GAAMgQ,GAAmBhQ,GAExC,IAAMA,IAAK,CAAEuW,QAAQ,EAAMC,OAAO,GACjCnN,EAAKkC,QAASvL,GAAMiQ,GAAoBjQ,GAIzC,SAASkV,MAuET,SAASxG,GAAY+H,GAIpB,IAHA,IAAIzW,EAAI,EACP0G,EAAM+P,EAAOlR,OACbN,EAAW,GACJjF,EAAI0G,EAAK1G,IAChBiF,GAAYwR,EAAOzW,GAAGiB,MAEvB,OAAOgE,EAGR,SAASoI,GAAegI,EAASqB,EAAYC,GAC5C,IAAInJ,EAAMkJ,EAAWlJ,IACpBoJ,EAAOF,EAAWjJ,KAClBlM,EAAMqV,GAAQpJ,EACdqJ,EAAmBF,GAAgB,eAARpV,EAC3BuV,EAAWvM,IAEZ,OAAOmM,EAAWnQ,MAEjB,SAAUH,EAAMlB,EAASqP,GACxB,KAASnO,EAAOA,EAAMoH,IACrB,GAAuB,IAAlBpH,EAAK1C,UAAkBmT,EAC3B,OAAOxB,EAASjP,EAAMlB,EAASqP,GAGjC,OAAO,GAIR,SAAUnO,EAAMlB,EAASqP,GACxB,IAAIwC,EAAUvC,EAAaC,EAC1BuC,EAAW,CAAE1M,EAASwM,GAGvB,GAAKvC,GACJ,KAASnO,EAAOA,EAAMoH,IACrB,IAAuB,IAAlBpH,EAAK1C,UAAkBmT,IACtBxB,EAASjP,EAAMlB,EAASqP,GAC5B,OAAO,OAKV,KAASnO,EAAOA,EAAMoH,IACrB,GAAuB,IAAlBpH,EAAK1C,UAAkBmT,EAO3B,GAFArC,GAJAC,EAAarO,EAAMsB,KAActB,EAAMsB,GAAY,KAIzBtB,EAAK2O,YAAeN,EAAYrO,EAAK2O,UAAa,IAEvE6B,GAAQA,IAASxQ,EAAKmH,SAASpE,cACnC/C,EAAOA,EAAMoH,IAASpH,MAChB,KAAM2Q,EAAWvC,EAAajT,KACpCwV,EAAU,KAAQzM,GAAWyM,EAAU,KAAQD,EAG/C,OAAQE,EAAU,GAAMD,EAAU,GAMlC,GAHAvC,EAAajT,GAAQyV,EAGfA,EAAU,GAAM3B,EAASjP,EAAMlB,EAASqP,GAC7C,OAAO,EAMZ,OAAO,GAIV,SAAS0C,GAAgBC,GACxB,OAAOA,EAAS3R,OAAS,EACxB,SAAUa,EAAMlB,EAASqP,GAExB,IADA,IAAIvU,EAAIkX,EAAS3R,OACTvF,KACP,IAAMkX,EAASlX,GAAIoG,EAAMlB,EAASqP,GACjC,OAAO,EAGT,OAAO,GAER2C,EAAS,GAYX,SAASC,GAAU7B,EAAWnP,EAAKiL,EAAQlM,EAASqP,GAOnD,IANA,IAAInO,EACHgR,EAAe,GACfpX,EAAI,EACJ0G,EAAM4O,EAAU/P,OAChB8R,EAAgB,MAAPlR,EAEFnG,EAAI0G,EAAK1G,KACVoG,EAAOkP,EAAUtV,MAChBoR,IAAUA,EAAQhL,EAAMlB,EAASqP,KACtC6C,EAAapU,KAAMoD,GACdiR,GACJlR,EAAInD,KAAMhD,KAMd,OAAOoX,EAGR,SAASE,GAAY3D,EAAW1O,EAAUoQ,EAASkC,EAAYC,EAAYC,GAO1E,OANKF,IAAeA,EAAY7P,KAC/B6P,EAAaD,GAAYC,IAErBC,IAAeA,EAAY9P,KAC/B8P,EAAaF,GAAYE,EAAYC,IAE/BrI,IAAa,SAAUxB,EAAMpF,EAAStD,EAASqP,GACrD,IAAImD,EAAM1X,EAAGoG,EACZuR,EAAS,GACTC,EAAU,GACVC,EAAcrP,EAAQjD,OAGtBM,EAAQ+H,GA5CX,SAA2B3I,EAAU6S,EAAUtP,GAG9C,IAFA,IAAIxI,EAAI,EACP0G,EAAMoR,EAASvS,OACRvF,EAAI0G,EAAK1G,IAChBoJ,GAAQnE,EAAU6S,EAAS9X,GAAIwI,GAEhC,OAAOA,EAsCWuP,CAAkB9S,GAAY,IAAKC,EAAQxB,SAAW,CAAEwB,GAAYA,EAAS,IAG7F8S,GAAYrE,IAAe/F,GAAS3I,EAEnCY,EADAsR,GAAUtR,EAAO8R,EAAQhE,EAAWzO,EAASqP,GAG9C0D,EAAa5C,EAEZmC,IAAgB5J,EAAO+F,EAAYkE,GAAeN,GAGjD,GAGA/O,EACDwP,EAQF,GALK3C,GACJA,EAAS2C,EAAWC,EAAY/S,EAASqP,GAIrCgD,EAMJ,IALAG,EAAOP,GAAUc,EAAYL,GAC7BL,EAAYG,EAAM,GAAIxS,EAASqP,GAG/BvU,EAAI0X,EAAKnS,OACDvF,MACDoG,EAAOsR,EAAK1X,MACjBiY,EAAYL,EAAQ5X,MAASgY,EAAWJ,EAAQ5X,IAAOoG,IAK1D,GAAKwH,GACJ,GAAK4J,GAAc7D,EAAY,CAC9B,GAAK6D,EAAa,CAIjB,IAFAE,EAAO,GACP1X,EAAIiY,EAAW1S,OACPvF,MACDoG,EAAO6R,EAAWjY,KAEvB0X,EAAK1U,KAAOgV,EAAUhY,GAAKoG,GAG7BoR,EAAY,KAAOS,EAAa,GAAKP,EAAMnD,GAK5C,IADAvU,EAAIiY,EAAW1S,OACPvF,MACDoG,EAAO6R,EAAWjY,MACtB0X,EAAOF,EAAavU,EAAS2K,EAAMxH,GAASuR,EAAO3X,KAAO,IAE3D4N,EAAK8J,KAAUlP,EAAQkP,GAAQtR,UAOlC6R,EAAad,GACZc,IAAezP,EACdyP,EAAWnR,OAAQ+Q,EAAaI,EAAW1S,QAC3C0S,GAEGT,EACJA,EAAY,KAAMhP,EAASyP,EAAY1D,GAEvCvR,EAAKqD,MAAOmC,EAASyP,MAMzB,SAASC,GAAmBzB,GAwB3B,IAvBA,IAAI0B,EAAc9C,EAAS1O,EAC1BD,EAAM+P,EAAOlR,OACb6S,EAAkB/O,EAAKqK,SAAU+C,EAAO,GAAG5S,MAC3CwU,EAAmBD,GAAmB/O,EAAKqK,SAAS,KACpD1T,EAAIoY,EAAkB,EAAI,EAG1BE,EAAejL,IAAe,SAAUjH,GACvC,OAAOA,IAAS+R,IACdE,GAAkB,GACrBE,EAAkBlL,IAAe,SAAUjH,GAC1C,OAAOnD,EAASkV,EAAc/R,IAAU,IACtCiS,GAAkB,GACrBnB,EAAW,CAAE,SAAU9Q,EAAMlB,EAASqP,GACrC,IAAIzO,GAASsS,IAAqB7D,GAAOrP,IAAYyE,MACnDwO,EAAejT,GAASxB,SACxB4U,EAAclS,EAAMlB,EAASqP,GAC7BgE,EAAiBnS,EAAMlB,EAASqP,IAGlC,OADA4D,EAAe,KACRrS,IAGD9F,EAAI0G,EAAK1G,IAChB,GAAMqV,EAAUhM,EAAKqK,SAAU+C,EAAOzW,GAAG6D,MACxCqT,EAAW,CAAE7J,GAAc4J,GAAgBC,GAAY7B,QACjD,CAIN,IAHAA,EAAUhM,EAAK+H,OAAQqF,EAAOzW,GAAG6D,MAAOwC,MAAO,KAAMoQ,EAAOzW,GAAG6I,UAGjDnB,GAAY,CAGzB,IADAf,IAAM3G,EACE2G,EAAID,IACN2C,EAAKqK,SAAU+C,EAAO9P,GAAG9C,MADd8C,KAKjB,OAAO2Q,GACNtX,EAAI,GAAKiX,GAAgBC,GACzBlX,EAAI,GAAK0O,GAER+H,EAAO3T,MAAO,EAAG9C,EAAI,GAAI+C,OAAO,CAAE9B,MAAgC,MAAzBwV,EAAQzW,EAAI,GAAI6D,KAAe,IAAM,MAC7EgE,QAASxC,EAAO,MAClBgQ,EACArV,EAAI2G,GAAKuR,GAAmBzB,EAAO3T,MAAO9C,EAAG2G,IAC7CA,EAAID,GAAOwR,GAAoBzB,EAASA,EAAO3T,MAAO6D,IACtDA,EAAID,GAAOgI,GAAY+H,IAGzBS,EAASlU,KAAMqS,GAIjB,OAAO4B,GAAgBC,GA8RxB,OA9mBAhC,GAAWtT,UAAYyH,EAAKmP,QAAUnP,EAAKkC,QAC3ClC,EAAK6L,WAAa,IAAIA,GAEtB1L,EAAWJ,GAAOI,SAAW,SAAUvE,EAAUwT,GAChD,IAAIrD,EAAStH,EAAO2I,EAAQ5S,EAC3B6U,EAAO3K,EAAQ4K,EACfC,EAASlO,EAAYzF,EAAW,KAEjC,GAAK2T,EACJ,OAAOH,EAAY,EAAIG,EAAO9V,MAAO,GAOtC,IAJA4V,EAAQzT,EACR8I,EAAS,GACT4K,EAAatP,EAAKsK,UAEV+E,GAAQ,CAyBf,IAAM7U,KAtBAuR,KAAYtH,EAAQpC,EAAOyC,KAAMuK,MACjC5K,IAEJ4K,EAAQA,EAAM5V,MAAOgL,EAAM,GAAGvI,SAAYmT,GAE3C3K,EAAO/K,KAAOyT,EAAS,KAGxBrB,GAAU,GAGJtH,EAAQnC,EAAawC,KAAMuK,MAChCtD,EAAUtH,EAAMqB,QAChBsH,EAAOzT,KAAK,CACX/B,MAAOmU,EAEPvR,KAAMiK,EAAM,GAAGjG,QAASxC,EAAO,OAEhCqT,EAAQA,EAAM5V,MAAOsS,EAAQ7P,SAIhB8D,EAAK+H,SACZtD,EAAQ/B,EAAWlI,GAAOsK,KAAMuK,KAAcC,EAAY9U,MAC9DiK,EAAQ6K,EAAY9U,GAAQiK,MAC7BsH,EAAUtH,EAAMqB,QAChBsH,EAAOzT,KAAK,CACX/B,MAAOmU,EACPvR,KAAMA,EACNgF,QAASiF,IAEV4K,EAAQA,EAAM5V,MAAOsS,EAAQ7P,SAI/B,IAAM6P,EACL,MAOF,OAAOqD,EACNC,EAAMnT,OACNmT,EACCtP,GAAOrB,MAAO9C,GAEdyF,EAAYzF,EAAU8I,GAASjL,MAAO,IA+XzC2G,EAAUL,GAAOK,QAAU,SAAUxE,EAAU6I,GAC9C,IAAI9N,EACH6Y,EAAc,GACdC,EAAkB,GAClBF,EAASjO,EAAe1F,EAAW,KAEpC,IAAM2T,EAAS,CAMd,IAJM9K,IACLA,EAAQtE,EAAUvE,IAEnBjF,EAAI8N,EAAMvI,OACFvF,MACP4Y,EAASV,GAAmBpK,EAAM9N,KACrB0H,GACZmR,EAAY7V,KAAM4V,GAElBE,EAAgB9V,KAAM4V,IAKxBA,EAASjO,EAAe1F,EArI1B,SAAmC6T,EAAiBD,GACnD,IAAIE,EAAQF,EAAYtT,OAAS,EAChCyT,EAAYF,EAAgBvT,OAAS,EACrC0T,EAAe,SAAUrL,EAAM1I,EAASqP,EAAK/L,EAAS0Q,GACrD,IAAI9S,EAAMO,EAAG0O,EACZ8D,EAAe,EACfnZ,EAAI,IACJsV,EAAY1H,GAAQ,GACpBwL,EAAa,GACbC,EAAgB1P,EAEhB9D,EAAQ+H,GAAQoL,GAAa3P,EAAKiI,KAAU,IAAG,IAAK4H,GAEpDI,EAAiBhP,GAA4B,MAAjB+O,EAAwB,EAAI1R,KAAKC,UAAY,GACzElB,EAAMb,EAAMN,OASb,IAPK2T,IACJvP,EAAmBzE,IAAY3C,GAAY2C,GAAWgU,GAM/ClZ,IAAM0G,GAA4B,OAApBN,EAAOP,EAAM7F,IAAaA,IAAM,CACrD,GAAKgZ,GAAa5S,EAAO,CAMxB,IALAO,EAAI,EACEzB,GAAWkB,EAAK8H,gBAAkB3L,IACvCuH,EAAa1D,GACbmO,GAAOvK,GAECqL,EAAUyD,EAAgBnS,MAClC,GAAK0O,EAASjP,EAAMlB,GAAW3C,EAAUgS,GAAO,CAC/C/L,EAAQxF,KAAMoD,GACd,MAGG8S,IACJ5O,EAAUgP,GAKPP,KAEE3S,GAAQiP,GAAWjP,IACxB+S,IAIIvL,GACJ0H,EAAUtS,KAAMoD,IAgBnB,GATA+S,GAAgBnZ,EASX+Y,GAAS/Y,IAAMmZ,EAAe,CAElC,IADAxS,EAAI,EACK0O,EAAUwD,EAAYlS,MAC9B0O,EAASC,EAAW8D,EAAYlU,EAASqP,GAG1C,GAAK3G,EAAO,CAEX,GAAKuL,EAAe,EACnB,KAAQnZ,KACAsV,EAAUtV,IAAMoZ,EAAWpZ,KACjCoZ,EAAWpZ,GAAKgL,EAAI7K,KAAMqI,IAM7B4Q,EAAajC,GAAUiC,GAIxBpW,EAAKqD,MAAOmC,EAAS4Q,GAGhBF,IAActL,GAAQwL,EAAW7T,OAAS,GAC5C4T,EAAeN,EAAYtT,OAAW,GAExC6D,GAAO6J,WAAYzK,GAUrB,OALK0Q,IACJ5O,EAAUgP,EACV3P,EAAmB0P,GAGb/D,GAGT,OAAOyD,EACN3J,GAAc6J,GACdA,EAyBkCM,CAA0BT,EAAiBD,KAGtE5T,SAAWA,EAEnB,OAAO2T,GAYRlP,EAASN,GAAOM,OAAS,SAAUzE,EAAUC,EAASsD,EAASoF,GAC9D,IAAI5N,EAAGyW,EAAQ+C,EAAO3V,EAAMyN,EAC3BmI,EAA+B,mBAAbxU,GAA2BA,EAC7C6I,GAASF,GAAQpE,EAAWvE,EAAWwU,EAASxU,UAAYA,GAM7D,GAJAuD,EAAUA,GAAW,GAIC,IAAjBsF,EAAMvI,OAAe,CAIzB,IADAkR,EAAS3I,EAAM,GAAKA,EAAM,GAAGhL,MAAO,IACxByC,OAAS,GAAkC,QAA5BiU,EAAQ/C,EAAO,IAAI5S,MACvB,IAArBqB,EAAQxB,UAAkBsG,GAAkBX,EAAKqK,SAAU+C,EAAO,GAAG5S,MAAS,CAG/E,KADAqB,GAAYmE,EAAKiI,KAAS,GAAGkI,EAAM3Q,QAAQ,GAAGhB,QAAQyE,GAAWC,IAAYrH,IAAa,IAAK,IAE9F,OAAOsD,EAGIiR,IACXvU,EAAUA,EAAQL,YAGnBI,EAAWA,EAASnC,MAAO2T,EAAOtH,QAAQlO,MAAMsE,QAKjD,IADAvF,EAAI+L,EAAwB,aAAE0C,KAAMxJ,GAAa,EAAIwR,EAAOlR,OACpDvF,MACPwZ,EAAQ/C,EAAOzW,IAGVqJ,EAAKqK,SAAW7P,EAAO2V,EAAM3V,QAGlC,IAAMyN,EAAOjI,EAAKiI,KAAMzN,MAEjB+J,EAAO0D,EACZkI,EAAM3Q,QAAQ,GAAGhB,QAASyE,GAAWC,IACrCF,GAASoC,KAAMgI,EAAO,GAAG5S,OAAU+K,GAAa1J,EAAQL,aAAgBK,IACpE,CAKJ,GAFAuR,EAAO3P,OAAQ9G,EAAG,KAClBiF,EAAW2I,EAAKrI,QAAUmJ,GAAY+H,IAGrC,OADAzT,EAAKqD,MAAOmC,EAASoF,GACdpF,EAGR,OAeJ,OAPEiR,GAAYhQ,EAASxE,EAAU6I,IAChCF,EACA1I,GACC8E,EACDxB,GACCtD,GAAWmH,GAASoC,KAAMxJ,IAAc2J,GAAa1J,EAAQL,aAAgBK,GAExEsD,GAMRjF,EAAQ6P,WAAa1L,EAAQwB,MAAM,IAAIrC,KAAMgE,GAAY8D,KAAK,MAAQjH,EAItEnE,EAAQ4P,mBAAqBtJ,EAG7BC,IAIAvG,EAAQ+O,aAAejD,IAAO,SAAUC,GAEvC,OAA0E,EAAnEA,EAAG4C,wBAAyB3P,EAASgC,cAAc,gBAMrD8K,IAAO,SAAUC,GAEtB,OADAA,EAAGoC,UAAY,mBAC+B,MAAvCpC,EAAGgE,WAAW7O,aAAa,YAElC8K,GAAW,0BAA0B,SAAUnJ,EAAM7F,EAAMgJ,GAC1D,IAAMA,EACL,OAAOnD,EAAK3B,aAAclE,EAA6B,SAAvBA,EAAK4I,cAA2B,EAAI,MAOjE5F,EAAQ+H,YAAe+D,IAAO,SAAUC,GAG7C,OAFAA,EAAGoC,UAAY,WACfpC,EAAGgE,WAAW5O,aAAc,QAAS,IACY,KAA1C4K,EAAGgE,WAAW7O,aAAc,aAEnC8K,GAAW,SAAS,SAAUnJ,EAAM7F,EAAMgJ,GACzC,IAAMA,GAAyC,UAAhCnD,EAAKmH,SAASpE,cAC5B,OAAO/C,EAAKsT,gBAOTrK,IAAO,SAAUC,GACtB,OAAsC,MAA/BA,EAAG7K,aAAa,gBAEvB8K,GAAWpE,GAAU,SAAU/E,EAAM7F,EAAMgJ,GAC1C,IAAIlF,EACJ,IAAMkF,EACL,OAAwB,IAAjBnD,EAAM7F,GAAkBA,EAAK4I,eACjC9E,EAAM+B,EAAKmL,iBAAkBhR,KAAW8D,EAAIyO,UAC7CzO,EAAIpD,MACL,QAKGmI,GA1sEP,CA4sEIhH,GAIJ4C,EAAOsM,KAAOlI,EACdpE,EAAO2N,KAAOvJ,EAAOoK,UAGrBxO,EAAO2N,KAAM,KAAQ3N,EAAO2N,KAAKpH,QACjCvG,EAAOiO,WAAajO,EAAO2U,OAASvQ,EAAO6J,WAC3CjO,EAAOR,KAAO4E,EAAOE,QACrBtE,EAAO4U,SAAWxQ,EAAOG,MACzBvE,EAAOmF,SAAWf,EAAOe,SACzBnF,EAAO6U,eAAiBzQ,EAAO2J,OAK/B,IAAIvF,EAAM,SAAUpH,EAAMoH,EAAKsM,GAI9B,IAHA,IAAI1E,EAAU,GACb2E,OAAqBtS,IAAVqS,GAEF1T,EAAOA,EAAMoH,KAA6B,IAAlBpH,EAAK1C,UACtC,GAAuB,IAAlB0C,EAAK1C,SAAiB,CAC1B,GAAKqW,GAAY/U,EAAQoB,GAAO4T,GAAIF,GACnC,MAED1E,EAAQpS,KAAMoD,GAGhB,OAAOgP,GAIJ6E,EAAW,SAAUxY,EAAG2E,GAG3B,IAFA,IAAIgP,EAAU,GAEN3T,EAAGA,EAAIA,EAAEsO,YACI,IAAftO,EAAEiC,UAAkBjC,IAAM2E,GAC9BgP,EAAQpS,KAAMvB,GAIhB,OAAO2T,GAIJ8E,EAAgBlV,EAAO2N,KAAK7E,MAAMqM,aAItC,SAAS5M,EAAUnH,EAAM7F,GAEvB,OAAO6F,EAAKmH,UAAYnH,EAAKmH,SAASpE,gBAAkB5I,EAAK4I,cAG/D,IAAIiR,EAAa,kEAKjB,SAASC,EAAQzH,EAAU0H,EAAWC,GACrC,OAAK/W,EAAY8W,GACTtV,EAAO2D,KAAMiK,GAAU,SAAUxM,EAAMpG,GAC7C,QAASsa,EAAUna,KAAMiG,EAAMpG,EAAGoG,KAAWmU,KAK1CD,EAAU5W,SACPsB,EAAO2D,KAAMiK,GAAU,SAAUxM,GACvC,OAASA,IAASkU,IAAgBC,KAKV,iBAAdD,EACJtV,EAAO2D,KAAMiK,GAAU,SAAUxM,GACvC,OAASnD,EAAQ9C,KAAMma,EAAWlU,IAAU,IAAQmU,KAK/CvV,EAAOoM,OAAQkJ,EAAW1H,EAAU2H,GAG5CvV,EAAOoM,OAAS,SAAUuB,EAAM9M,EAAO0U,GACtC,IAAInU,EAAOP,EAAO,GAMlB,OAJK0U,IACJ5H,EAAO,QAAUA,EAAO,KAGH,IAAjB9M,EAAMN,QAAkC,IAAlBa,EAAK1C,SACxBsB,EAAOsM,KAAKM,gBAAiBxL,EAAMuM,GAAS,CAAEvM,GAAS,GAGxDpB,EAAOsM,KAAKzI,QAAS8J,EAAM3N,EAAO2D,KAAM9C,GAAO,SAAUO,GAC/D,OAAyB,IAAlBA,EAAK1C,cAIdsB,EAAOG,GAAG4B,OAAQ,CACjBuK,KAAM,SAAUrM,GACf,IAAIjF,EAAG8F,EACNY,EAAMzE,KAAKsD,OACXiV,EAAOvY,KAER,GAAyB,iBAAbgD,EACX,OAAOhD,KAAK2D,UAAWZ,EAAQC,GAAWmM,QAAQ,WACjD,IAAMpR,EAAI,EAAGA,EAAI0G,EAAK1G,IACrB,GAAKgF,EAAOmF,SAAUqQ,EAAMxa,GAAKiC,MAChC,OAAO,MAQX,IAFA6D,EAAM7D,KAAK2D,UAAW,IAEhB5F,EAAI,EAAGA,EAAI0G,EAAK1G,IACrBgF,EAAOsM,KAAMrM,EAAUuV,EAAMxa,GAAK8F,GAGnC,OAAOY,EAAM,EAAI1B,EAAOiO,WAAYnN,GAAQA,GAE7CsL,OAAQ,SAAUnM,GACjB,OAAOhD,KAAK2D,UAAWyU,EAAQpY,KAAMgD,GAAY,IAAI,KAEtDsV,IAAK,SAAUtV,GACd,OAAOhD,KAAK2D,UAAWyU,EAAQpY,KAAMgD,GAAY,IAAI,KAEtD+U,GAAI,SAAU/U,GACb,QAASoV,EACRpY,KAIoB,iBAAbgD,GAAyBiV,EAAczL,KAAMxJ,GACnDD,EAAQC,GACRA,GAAY,IACb,GACCM,UASJ,IAAIkV,EAMHrO,EAAa,uCAENpH,EAAOG,GAAGC,KAAO,SAAUH,EAAUC,EAASwV,GACpD,IAAI5M,EAAO1H,EAGX,IAAMnB,EACL,OAAOhD,KAQR,GAHAyY,EAAOA,GAAQD,EAGU,iBAAbxV,EAAwB,CAanC,KAPC6I,EALsB,MAAlB7I,EAAU,IACsB,MAApCA,EAAUA,EAASM,OAAS,IAC5BN,EAASM,QAAU,EAGX,CAAE,KAAMN,EAAU,MAGlBmH,EAAW+B,KAAMlJ,MAIV6I,EAAO,IAAQ5I,EA6CxB,OAAMA,GAAWA,EAAQM,QACtBN,GAAWwV,GAAOpJ,KAAMrM,GAK1BhD,KAAKwD,YAAaP,GAAUoM,KAAMrM,GAhDzC,GAAK6I,EAAO,GAAM,CAYjB,GAXA5I,EAAUA,aAAmBF,EAASE,EAAS,GAAMA,EAIrDF,EAAOe,MAAO9D,KAAM+C,EAAO2V,UAC1B7M,EAAO,GACP5I,GAAWA,EAAQxB,SAAWwB,EAAQgJ,eAAiBhJ,EAAU3C,GACjE,IAII6X,EAAW3L,KAAMX,EAAO,KAAS9I,EAAOsC,cAAepC,GAC3D,IAAM4I,KAAS5I,EAGT1B,EAAYvB,KAAM6L,IACtB7L,KAAM6L,GAAS5I,EAAS4I,IAIxB7L,KAAK4Q,KAAM/E,EAAO5I,EAAS4I,IAK9B,OAAO7L,KAYP,OARAmE,EAAO7D,EAAS6L,eAAgBN,EAAO,OAKtC7L,KAAM,GAAMmE,EACZnE,KAAKsD,OAAS,GAERtD,KAcH,OAAKgD,EAASvB,UACpBzB,KAAM,GAAMgD,EACZhD,KAAKsD,OAAS,EACPtD,MAIIuB,EAAYyB,QACDwC,IAAfiT,EAAKE,MACXF,EAAKE,MAAO3V,GAGZA,EAAUD,GAGLA,EAAOuD,UAAWtD,EAAUhD,QAIhCL,UAAYoD,EAAOG,GAGxBsV,EAAazV,EAAQzC,GAGrB,IAAIsY,EAAe,iCAGlBC,EAAmB,CAClBC,UAAU,EACVC,UAAU,EACVvN,MAAM,EACNwN,MAAM,GAoFR,SAASC,EAAStL,EAAKpC,GACtB,MAAUoC,EAAMA,EAAKpC,KAA4B,IAAjBoC,EAAIlM,WACpC,OAAOkM,EAnFR5K,EAAOG,GAAG4B,OAAQ,CACjBoU,IAAK,SAAU/T,GACd,IAAIgU,EAAUpW,EAAQoC,EAAQnF,MAC7BhC,EAAImb,EAAQ7V,OAEb,OAAOtD,KAAKmP,QAAQ,WAEnB,IADA,IAAIpR,EAAI,EACAA,EAAIC,EAAGD,IACd,GAAKgF,EAAOmF,SAAUlI,KAAMmZ,EAASpb,IACpC,OAAO,MAMXqb,QAAS,SAAU7H,EAAWtO,GAC7B,IAAI0K,EACH5P,EAAI,EACJC,EAAIgC,KAAKsD,OACT6P,EAAU,GACVgG,EAA+B,iBAAd5H,GAA0BxO,EAAQwO,GAGpD,IAAM0G,EAAczL,KAAM+E,GACzB,KAAQxT,EAAIC,EAAGD,IACd,IAAM4P,EAAM3N,KAAMjC,GAAK4P,GAAOA,IAAQ1K,EAAS0K,EAAMA,EAAI/K,WAGxD,GAAK+K,EAAIlM,SAAW,KAAQ0X,EAC3BA,EAAQE,MAAO1L,IAAS,EAGP,IAAjBA,EAAIlM,UACHsB,EAAOsM,KAAKM,gBAAiBhC,EAAK4D,IAAgB,CAEnD4B,EAAQpS,KAAM4M,GACd,MAMJ,OAAO3N,KAAK2D,UAAWwP,EAAQ7P,OAAS,EAAIP,EAAOiO,WAAYmC,GAAYA,IAI5EkG,MAAO,SAAUlV,GAGhB,OAAMA,EAKe,iBAATA,EACJnD,EAAQ9C,KAAM6E,EAAQoB,GAAQnE,KAAM,IAIrCgB,EAAQ9C,KAAM8B,KAGpBmE,EAAKZ,OAASY,EAAM,GAAMA,GAZjBnE,KAAM,IAAOA,KAAM,GAAI4C,WAAe5C,KAAKsE,QAAQgV,UAAUhW,QAAU,GAgBlFiW,IAAK,SAAUvW,EAAUC,GACxB,OAAOjD,KAAK2D,UACXZ,EAAOiO,WACNjO,EAAOe,MAAO9D,KAAKpB,MAAOmE,EAAQC,EAAUC,OAK/CuW,QAAS,SAAUxW,GAClB,OAAOhD,KAAKuZ,IAAiB,MAAZvW,EAChBhD,KAAK+D,WAAa/D,KAAK+D,WAAWoL,OAAQnM,OAU7CD,EAAOiB,KAAM,CACZ2O,OAAQ,SAAUxO,GACjB,IAAIwO,EAASxO,EAAKvB,WAClB,OAAO+P,GAA8B,KAApBA,EAAOlR,SAAkBkR,EAAS,MAEpD8G,QAAS,SAAUtV,GAClB,OAAOoH,EAAKpH,EAAM,eAEnBuV,aAAc,SAAUvV,EAAMpG,EAAG8Z,GAChC,OAAOtM,EAAKpH,EAAM,aAAc0T,IAEjCrM,KAAM,SAAUrH,GACf,OAAO8U,EAAS9U,EAAM,gBAEvB6U,KAAM,SAAU7U,GACf,OAAO8U,EAAS9U,EAAM,oBAEvBwV,QAAS,SAAUxV,GAClB,OAAOoH,EAAKpH,EAAM,gBAEnBmV,QAAS,SAAUnV,GAClB,OAAOoH,EAAKpH,EAAM,oBAEnByV,UAAW,SAAUzV,EAAMpG,EAAG8Z,GAC7B,OAAOtM,EAAKpH,EAAM,cAAe0T,IAElCgC,UAAW,SAAU1V,EAAMpG,EAAG8Z,GAC7B,OAAOtM,EAAKpH,EAAM,kBAAmB0T,IAEtCG,SAAU,SAAU7T,GACnB,OAAO6T,GAAY7T,EAAKvB,YAAc,IAAKyO,WAAYlN,IAExD2U,SAAU,SAAU3U,GACnB,OAAO6T,EAAU7T,EAAKkN,aAEvB0H,SAAU,SAAU5U,GACnB,YAAqC,IAAzBA,EAAK2V,gBACT3V,EAAK2V,iBAMRxO,EAAUnH,EAAM,cACpBA,EAAOA,EAAK4V,SAAW5V,GAGjBpB,EAAOe,MAAO,GAAIK,EAAKsH,gBAE7B,SAAUnN,EAAM4E,GAClBH,EAAOG,GAAI5E,GAAS,SAAUuZ,EAAO7U,GACpC,IAAImQ,EAAUpQ,EAAOmB,IAAKlE,KAAMkD,EAAI2U,GAuBpC,MArB0B,UAArBvZ,EAAKuC,OAAQ,KACjBmC,EAAW6U,GAGP7U,GAAgC,iBAAbA,IACvBmQ,EAAUpQ,EAAOoM,OAAQnM,EAAUmQ,IAG/BnT,KAAKsD,OAAS,IAGZuV,EAAkBva,IACvByE,EAAOiO,WAAYmC,GAIfyF,EAAapM,KAAMlO,IACvB6U,EAAQ6G,WAIHha,KAAK2D,UAAWwP,OAGzB,IAAI8G,EAAgB,oBAsOpB,SAASC,EAAUC,GAClB,OAAOA,EAER,SAASC,EAASC,GACjB,MAAMA,EAGP,SAASC,EAAYtb,EAAOub,EAASC,EAAQC,GAC5C,IAAIC,EAEJ,IAGM1b,GAASuC,EAAcmZ,EAAS1b,EAAM2b,SAC1CD,EAAOxc,KAAMc,GAAQsJ,KAAMiS,GAAUK,KAAMJ,GAGhCxb,GAASuC,EAAcmZ,EAAS1b,EAAM6b,MACjDH,EAAOxc,KAAMc,EAAOub,EAASC,GAQ7BD,EAAQnW,WAAOoB,EAAW,CAAExG,GAAQ6B,MAAO4Z,IAM3C,MAAQzb,GAITwb,EAAOpW,WAAOoB,EAAW,CAAExG,KAvO7B+D,EAAO+X,UAAY,SAAU/V,GAI5BA,EAA6B,iBAAZA,EAlClB,SAAwBA,GACvB,IAAItF,EAAS,GAIb,OAHAsD,EAAOiB,KAAMe,EAAQ8G,MAAOoO,IAAmB,IAAI,SAAU1P,EAAGwQ,GAC/Dtb,EAAQsb,IAAS,KAEXtb,EA8BNub,CAAejW,GACfhC,EAAO+B,OAAQ,GAAIC,GAEpB,IACCkW,EAGAC,EAGAC,EAGAC,EAGAnS,EAAO,GAGPoS,EAAQ,GAGRC,GAAe,EAGfC,EAAO,WAQN,IALAH,EAASA,GAAUrW,EAAQyW,KAI3BL,EAAQF,GAAS,EACTI,EAAM/X,OAAQgY,GAAe,EAEpC,IADAJ,EAASG,EAAMnO,UACLoO,EAAcrS,EAAK3F,SAGmC,IAA1D2F,EAAMqS,GAAclX,MAAO8W,EAAQ,GAAKA,EAAQ,KACpDnW,EAAQ0W,cAGRH,EAAcrS,EAAK3F,OACnB4X,GAAS,GAMNnW,EAAQmW,SACbA,GAAS,GAGVD,GAAS,EAGJG,IAIHnS,EADIiS,EACG,GAIA,KAMV3C,EAAO,CAGNgB,IAAK,WA2BJ,OA1BKtQ,IAGCiS,IAAWD,IACfK,EAAcrS,EAAK3F,OAAS,EAC5B+X,EAAMta,KAAMma,IAGb,SAAW3B,EAAKvG,GACfjQ,EAAOiB,KAAMgP,GAAM,SAAUzI,EAAGzD,GAC1BvF,EAAYuF,GACV/B,EAAQ2S,QAAWa,EAAKW,IAAKpS,IAClCmC,EAAKlI,KAAM+F,GAEDA,GAAOA,EAAIxD,QAA4B,WAAlBR,EAAQgE,IAGxCyS,EAAKzS,MATR,CAYKzC,WAEA6W,IAAWD,GACfM,KAGKvb,MAIR0b,OAAQ,WAYP,OAXA3Y,EAAOiB,KAAMK,WAAW,SAAUkG,EAAGzD,GAEpC,IADA,IAAIuS,GACMA,EAAQtW,EAAOyD,QAASM,EAAKmC,EAAMoQ,KAAa,GACzDpQ,EAAKpE,OAAQwU,EAAO,GAGfA,GAASiC,GACbA,OAIItb,MAKRkZ,IAAK,SAAUhW,GACd,OAAOA,EACNH,EAAOyD,QAAStD,EAAI+F,IAAU,EAC9BA,EAAK3F,OAAS,GAIhBqY,MAAO,WAIN,OAHK1S,IACJA,EAAO,IAEDjJ,MAMR4b,QAAS,WAGR,OAFAR,EAASC,EAAQ,GACjBpS,EAAOiS,EAAS,GACTlb,MAERqL,SAAU,WACT,OAAQpC,GAMT4S,KAAM,WAKL,OAJAT,EAASC,EAAQ,GACXH,GAAWD,IAChBhS,EAAOiS,EAAS,IAEVlb,MAERob,OAAQ,WACP,QAASA,GAIVU,SAAU,SAAU7Y,EAAS+P,GAS5B,OARMoI,IAELpI,EAAO,CAAE/P,GADT+P,EAAOA,GAAQ,IACQnS,MAAQmS,EAAKnS,QAAUmS,GAC9CqI,EAAMta,KAAMiS,GACNiI,GACLM,KAGKvb,MAIRub,KAAM,WAEL,OADAhD,EAAKuD,SAAU9b,KAAMqE,WACdrE,MAIRmb,MAAO,WACN,QAASA,IAIZ,OAAO5C,GA4CRxV,EAAO+B,OAAQ,CAEdiX,SAAU,SAAUC,GACnB,IAAIC,EAAS,CAIX,CAAE,SAAU,WAAYlZ,EAAO+X,UAAW,UACzC/X,EAAO+X,UAAW,UAAY,GAC/B,CAAE,UAAW,OAAQ/X,EAAO+X,UAAW,eACtC/X,EAAO+X,UAAW,eAAiB,EAAG,YACvC,CAAE,SAAU,OAAQ/X,EAAO+X,UAAW,eACrC/X,EAAO+X,UAAW,eAAiB,EAAG,aAExCoB,EAAQ,UACRvB,EAAU,CACTuB,MAAO,WACN,OAAOA,GAERC,OAAQ,WAEP,OADAC,EAAS9T,KAAMjE,WAAYuW,KAAMvW,WAC1BrE,MAER,MAAS,SAAUkD,GAClB,OAAOyX,EAAQE,KAAM,KAAM3X,IAI5BmZ,KAAM,WACL,IAAIC,EAAMjY,UAEV,OAAOtB,EAAOgZ,UAAU,SAAUQ,GACjCxZ,EAAOiB,KAAMiY,GAAQ,SAAUle,EAAGye,GAGjC,IAAItZ,EAAK3B,EAAY+a,EAAKE,EAAO,MAAWF,EAAKE,EAAO,IAKxDJ,EAAUI,EAAO,KAAO,WACvB,IAAIC,EAAWvZ,GAAMA,EAAGkB,MAAOpE,KAAMqE,WAChCoY,GAAYlb,EAAYkb,EAAS9B,SACrC8B,EAAS9B,UACP+B,SAAUH,EAASI,QACnBrU,KAAMiU,EAAShC,SACfK,KAAM2B,EAAS/B,QAEjB+B,EAAUC,EAAO,GAAM,QACtBxc,KACAkD,EAAK,CAAEuZ,GAAapY,iBAKxBiY,EAAM,QACH3B,WAELE,KAAM,SAAU+B,EAAaC,EAAYC,GACxC,IAAIC,EAAW,EACf,SAASxC,EAASyC,EAAOZ,EAAU5O,EAASyP,GAC3C,OAAO,WACN,IAAIC,EAAOld,KACVgT,EAAO3O,UACP8Y,EAAa,WACZ,IAAIV,EAAU5B,EAKd,KAAKmC,EAAQD,GAAb,CAQA,IAJAN,EAAWjP,EAAQpJ,MAAO8Y,EAAMlK,MAIdoJ,EAASzB,UAC1B,MAAM,IAAIyC,UAAW,4BAOtBvC,EAAO4B,IAKgB,iBAAbA,GACY,mBAAbA,IACRA,EAAS5B,KAGLtZ,EAAYsZ,GAGXoC,EACJpC,EAAK3c,KACJue,EACAlC,EAASwC,EAAUX,EAAUlC,EAAU+C,GACvC1C,EAASwC,EAAUX,EAAUhC,EAAS6C,KAOvCF,IAEAlC,EAAK3c,KACJue,EACAlC,EAASwC,EAAUX,EAAUlC,EAAU+C,GACvC1C,EAASwC,EAAUX,EAAUhC,EAAS6C,GACtC1C,EAASwC,EAAUX,EAAUlC,EAC5BkC,EAASiB,eASP7P,IAAY0M,IAChBgD,OAAO1X,EACPwN,EAAO,CAAEyJ,KAKRQ,GAAWb,EAASkB,aAAeJ,EAAMlK,MAK7CuK,EAAUN,EACTE,EACA,WACC,IACCA,IACC,MAAQjd,GAEJ6C,EAAOgZ,SAASyB,eACpBza,EAAOgZ,SAASyB,cAAetd,EAC9Bqd,EAAQE,YAMLT,EAAQ,GAAKD,IAIZvP,IAAY4M,IAChB8C,OAAO1X,EACPwN,EAAO,CAAE9S,IAGVkc,EAASsB,WAAYR,EAAMlK,MAS3BgK,EACJO,KAKKxa,EAAOgZ,SAAS4B,eACpBJ,EAAQE,WAAa1a,EAAOgZ,SAAS4B,gBAEtCxd,EAAOyd,WAAYL,KAKtB,OAAOxa,EAAOgZ,UAAU,SAAUQ,GAGjCN,EAAQ,GAAK,GAAI1C,IAChBgB,EACC,EACAgC,EACAhb,EAAYub,GACXA,EACA5C,EACDqC,EAASc,aAKXpB,EAAQ,GAAK,GAAI1C,IAChBgB,EACC,EACAgC,EACAhb,EAAYqb,GACXA,EACA1C,IAKH+B,EAAQ,GAAK,GAAI1C,IAChBgB,EACC,EACAgC,EACAhb,EAAYsb,GACXA,EACAzC,OAGAO,WAKLA,QAAS,SAAUnZ,GAClB,OAAc,MAAPA,EAAcuB,EAAO+B,OAAQtD,EAAKmZ,GAAYA,IAGvDyB,EAAW,GAkEZ,OA/DArZ,EAAOiB,KAAMiY,GAAQ,SAAUle,EAAGye,GACjC,IAAIvT,EAAOuT,EAAO,GACjBqB,EAAcrB,EAAO,GAKtB7B,EAAS6B,EAAO,IAAQvT,EAAKsQ,IAGxBsE,GACJ5U,EAAKsQ,KACJ,WAIC2C,EAAQ2B,IAKT5B,EAAQ,EAAIle,GAAK,GAAI6d,QAIrBK,EAAQ,EAAIle,GAAK,GAAI6d,QAGrBK,EAAQ,GAAK,GAAIJ,KAGjBI,EAAQ,GAAK,GAAIJ,MAOnB5S,EAAKsQ,IAAKiD,EAAO,GAAIjB,MAKrBa,EAAUI,EAAO,IAAQ,WAExB,OADAJ,EAAUI,EAAO,GAAM,QAAUxc,OAASoc,OAAW5W,EAAYxF,KAAMqE,WAChErE,MAMRoc,EAAUI,EAAO,GAAM,QAAWvT,EAAK6S,YAIxCnB,EAAQA,QAASyB,GAGZJ,GACJA,EAAK9d,KAAMke,EAAUA,GAIfA,GAIR0B,KAAM,SAAUC,GACf,IAGCC,EAAY3Z,UAAUf,OAGtBvF,EAAIigB,EAGJC,EAAkB3Y,MAAOvH,GACzBmgB,EAAgBrd,EAAM3C,KAAMmG,WAG5B8Z,EAASpb,EAAOgZ,WAGhBqC,EAAa,SAAUrgB,GACtB,OAAO,SAAUiB,GAChBif,EAAiBlgB,GAAMiC,KACvBke,EAAengB,GAAMsG,UAAUf,OAAS,EAAIzC,EAAM3C,KAAMmG,WAAcrF,IAC5Dgf,GACTG,EAAOb,YAAaW,EAAiBC,KAMzC,GAAKF,GAAa,IACjB1D,EAAYyD,EAAaI,EAAO7V,KAAM8V,EAAYrgB,IAAMwc,QAAS4D,EAAO3D,QACtEwD,GAGsB,YAAnBG,EAAOjC,SACX3a,EAAY2c,EAAengB,IAAOmgB,EAAengB,GAAI8c,OAErD,OAAOsD,EAAOtD,OAKhB,KAAQ9c,KACPuc,EAAY4D,EAAengB,GAAKqgB,EAAYrgB,GAAKogB,EAAO3D,QAGzD,OAAO2D,EAAOxD,aAOhB,IAAI0D,EAAc,yDAElBtb,EAAOgZ,SAASyB,cAAgB,SAAU1X,EAAOwY,GAI3Cne,EAAOoe,SAAWpe,EAAOoe,QAAQC,MAAQ1Y,GAASuY,EAAY7R,KAAM1G,EAAMxH,OAC9E6B,EAAOoe,QAAQC,KAAM,8BAAgC1Y,EAAM2Y,QAAS3Y,EAAMwY,MAAOA,IAOnFvb,EAAO2b,eAAiB,SAAU5Y,GACjC3F,EAAOyd,YAAY,WAClB,MAAM9X,MAQR,IAAI6Y,EAAY5b,EAAOgZ,WAkDvB,SAAS6C,IACRte,EAASue,oBAAqB,mBAAoBD,GAClDze,EAAO0e,oBAAqB,OAAQD,GACpC7b,EAAO4V,QAnDR5V,EAAOG,GAAGyV,MAAQ,SAAUzV,GAY3B,OAVAyb,EACE9D,KAAM3X,GAKN4b,OAAO,SAAUhZ,GACjB/C,EAAO2b,eAAgB5Y,MAGlB9F,MAGR+C,EAAO+B,OAAQ,CAGde,SAAS,EAITkZ,UAAW,EAGXpG,MAAO,SAAUqG,KAGF,IAATA,IAAkBjc,EAAOgc,UAAYhc,EAAO8C,WAKjD9C,EAAO8C,SAAU,GAGH,IAATmZ,KAAmBjc,EAAOgc,UAAY,GAK3CJ,EAAUrB,YAAahd,EAAU,CAAEyC,QAIrCA,EAAO4V,MAAMkC,KAAO8D,EAAU9D,KAaD,aAAxBva,EAAS2e,YACa,YAAxB3e,EAAS2e,aAA6B3e,EAASkO,gBAAgB0Q,SAGjE/e,EAAOyd,WAAY7a,EAAO4V,QAK1BrY,EAASuO,iBAAkB,mBAAoB+P,GAG/Cze,EAAO0O,iBAAkB,OAAQ+P,IAQlC,IAAIO,EAAS,SAAUvb,EAAOV,EAAI5D,EAAKN,EAAOogB,EAAWC,EAAUC,GAClE,IAAIvhB,EAAI,EACP0G,EAAMb,EAAMN,OACZic,EAAc,MAAPjgB,EAGR,GAAuB,WAAlBwD,EAAQxD,GAEZ,IAAMvB,KADNqhB,GAAY,EACD9f,EACV6f,EAAQvb,EAAOV,EAAInF,EAAGuB,EAAKvB,IAAK,EAAMshB,EAAUC,QAI3C,QAAe9Z,IAAVxG,IACXogB,GAAY,EAEN7d,EAAYvC,KACjBsgB,GAAM,GAGFC,IAGCD,GACJpc,EAAGhF,KAAM0F,EAAO5E,GAChBkE,EAAK,OAILqc,EAAOrc,EACPA,EAAK,SAAUiB,EAAM7E,EAAKN,GACzB,OAAOugB,EAAKrhB,KAAM6E,EAAQoB,GAAQnF,MAKhCkE,GACJ,KAAQnF,EAAI0G,EAAK1G,IAChBmF,EACCU,EAAO7F,GAAKuB,EAAKggB,EACjBtgB,EACAA,EAAMd,KAAM0F,EAAO7F,GAAKA,EAAGmF,EAAIU,EAAO7F,GAAKuB,KAM/C,OAAK8f,EACGxb,EAIH2b,EACGrc,EAAGhF,KAAM0F,GAGVa,EAAMvB,EAAIU,EAAO,GAAKtE,GAAQ+f,GAKlCG,EAAY,QACfC,EAAa,YAGd,SAASC,EAAYC,EAAKC,GACzB,OAAOA,EAAOC,cAMf,SAASC,EAAWC,GACnB,OAAOA,EAAOna,QAAS4Z,EAAW,OAAQ5Z,QAAS6Z,EAAYC,GAEhE,IAAIM,EAAa,SAAUC,GAQ1B,OAA0B,IAAnBA,EAAMxe,UAAqC,IAAnBwe,EAAMxe,YAAsBwe,EAAMxe,UAMlE,SAASye,IACRlgB,KAAKyF,QAAU1C,EAAO0C,QAAUya,EAAKC,MAGtCD,EAAKC,IAAM,EAEXD,EAAKvgB,UAAY,CAEhBqN,MAAO,SAAUiT,GAGhB,IAAIjhB,EAAQihB,EAAOjgB,KAAKyF,SA4BxB,OAzBMzG,IACLA,EAAQ,GAKHghB,EAAYC,KAIXA,EAAMxe,SACVwe,EAAOjgB,KAAKyF,SAAYzG,EAMxBP,OAAOC,eAAgBuhB,EAAOjgB,KAAKyF,QAAS,CAC3CzG,MAAOA,EACPohB,cAAc,MAMXphB,GAERqhB,IAAK,SAAUJ,EAAOK,EAAMthB,GAC3B,IAAIuhB,EACHvT,EAAQhN,KAAKgN,MAAOiT,GAIrB,GAAqB,iBAATK,EACXtT,EAAO8S,EAAWQ,IAAWthB,OAM7B,IAAMuhB,KAAQD,EACbtT,EAAO8S,EAAWS,IAAWD,EAAMC,GAGrC,OAAOvT,GAERpO,IAAK,SAAUqhB,EAAO3gB,GACrB,YAAekG,IAARlG,EACNU,KAAKgN,MAAOiT,GAGZA,EAAOjgB,KAAKyF,UAAawa,EAAOjgB,KAAKyF,SAAWqa,EAAWxgB,KAE7D6f,OAAQ,SAAUc,EAAO3gB,EAAKN,GAa7B,YAAawG,IAARlG,GACCA,GAAsB,iBAARA,QAAgCkG,IAAVxG,EAElCgB,KAAKpB,IAAKqhB,EAAO3gB,IASzBU,KAAKqgB,IAAKJ,EAAO3gB,EAAKN,QAILwG,IAAVxG,EAAsBA,EAAQM,IAEtCoc,OAAQ,SAAUuE,EAAO3gB,GACxB,IAAIvB,EACHiP,EAAQiT,EAAOjgB,KAAKyF,SAErB,QAAeD,IAAVwH,EAAL,CAIA,QAAaxH,IAARlG,EAAoB,CAkBxBvB,GAXCuB,EAJIgG,MAAMC,QAASjG,GAIbA,EAAI4E,IAAK4b,IAEfxgB,EAAMwgB,EAAWxgB,MAIJ0N,EACZ,CAAE1N,GACAA,EAAIuM,MAAOoO,IAAmB,IAG1B3W,OAER,KAAQvF,YACAiP,EAAO1N,EAAKvB,UAKRyH,IAARlG,GAAqByD,EAAOoD,cAAe6G,MAM1CiT,EAAMxe,SACVwe,EAAOjgB,KAAKyF,cAAYD,SAEjBya,EAAOjgB,KAAKyF,YAItB+a,QAAS,SAAUP,GAClB,IAAIjT,EAAQiT,EAAOjgB,KAAKyF,SACxB,YAAiBD,IAAVwH,IAAwBjK,EAAOoD,cAAe6G,KAGvD,IAAIyT,EAAW,IAAIP,EAEfQ,EAAW,IAAIR,EAcfS,GAAS,gCACZC,GAAa,SA2Bd,SAASC,GAAU1c,EAAM7E,EAAKghB,GAC7B,IAAIhiB,EAIJ,QAAckH,IAAT8a,GAAwC,IAAlBnc,EAAK1C,SAI/B,GAHAnD,EAAO,QAAUgB,EAAIsG,QAASgb,GAAY,OAAQ1Z,cAG7B,iBAFrBoZ,EAAOnc,EAAK3B,aAAclE,IAEM,CAC/B,IACCgiB,EApCJ,SAAkBA,GACjB,MAAc,SAATA,GAIS,UAATA,IAIS,SAATA,EACG,KAIHA,KAAUA,EAAO,IACbA,EAGJK,GAAOnU,KAAM8T,GACVQ,KAAKC,MAAOT,GAGbA,GAcGU,CAASV,GACf,MAAQpgB,IAGVwgB,EAASL,IAAKlc,EAAM7E,EAAKghB,QAEzBA,OAAO9a,EAGT,OAAO8a,EAGRvd,EAAO+B,OAAQ,CACd0b,QAAS,SAAUrc,GAClB,OAAOuc,EAASF,QAASrc,IAAUsc,EAASD,QAASrc,IAGtDmc,KAAM,SAAUnc,EAAM7F,EAAMgiB,GAC3B,OAAOI,EAASvB,OAAQhb,EAAM7F,EAAMgiB,IAGrCW,WAAY,SAAU9c,EAAM7F,GAC3BoiB,EAAShF,OAAQvX,EAAM7F,IAKxB4iB,MAAO,SAAU/c,EAAM7F,EAAMgiB,GAC5B,OAAOG,EAAStB,OAAQhb,EAAM7F,EAAMgiB,IAGrCa,YAAa,SAAUhd,EAAM7F,GAC5BmiB,EAAS/E,OAAQvX,EAAM7F,MAIzByE,EAAOG,GAAG4B,OAAQ,CACjBwb,KAAM,SAAUhhB,EAAKN,GACpB,IAAIjB,EAAGO,EAAMgiB,EACZnc,EAAOnE,KAAM,GACbuN,EAAQpJ,GAAQA,EAAKkF,WAGtB,QAAa7D,IAARlG,EAAoB,CACxB,GAAKU,KAAKsD,SACTgd,EAAOI,EAAS9hB,IAAKuF,GAEE,IAAlBA,EAAK1C,WAAmBgf,EAAS7hB,IAAKuF,EAAM,iBAAmB,CAEnE,IADApG,EAAIwP,EAAMjK,OACFvF,KAIFwP,EAAOxP,IAEsB,KADjCO,EAAOiP,EAAOxP,GAAIO,MACR0C,QAAS,WAClB1C,EAAOwhB,EAAWxhB,EAAKuC,MAAO,IAC9BggB,GAAU1c,EAAM7F,EAAMgiB,EAAMhiB,KAI/BmiB,EAASJ,IAAKlc,EAAM,gBAAgB,GAItC,OAAOmc,EAIR,MAAoB,iBAARhhB,EACJU,KAAKgE,MAAM,WACjB0c,EAASL,IAAKrgB,KAAMV,MAIf6f,EAAQnf,MAAM,SAAUhB,GAC9B,IAAIshB,EAOJ,GAAKnc,QAAkBqB,IAAVxG,EAKZ,YAAcwG,KADd8a,EAAOI,EAAS9hB,IAAKuF,EAAM7E,IAEnBghB,OAMM9a,KADd8a,EAAOO,GAAU1c,EAAM7E,IAEfghB,OAIR,EAIDtgB,KAAKgE,MAAM,WAGV0c,EAASL,IAAKrgB,KAAMV,EAAKN,QAExB,KAAMA,EAAOqF,UAAUf,OAAS,EAAG,MAAM,IAG7C2d,WAAY,SAAU3hB,GACrB,OAAOU,KAAKgE,MAAM,WACjB0c,EAAShF,OAAQ1b,KAAMV,SAM1ByD,EAAO+B,OAAQ,CACduW,MAAO,SAAUlX,EAAMvC,EAAM0e,GAC5B,IAAIjF,EAEJ,GAAKlX,EAYJ,OAXAvC,GAASA,GAAQ,MAAS,QAC1ByZ,EAAQoF,EAAS7hB,IAAKuF,EAAMvC,GAGvB0e,KACEjF,GAAS/V,MAAMC,QAAS+a,GAC7BjF,EAAQoF,EAAStB,OAAQhb,EAAMvC,EAAMmB,EAAOuD,UAAWga,IAEvDjF,EAAMta,KAAMuf,IAGPjF,GAAS,IAIlB+F,QAAS,SAAUjd,EAAMvC,GACxBA,EAAOA,GAAQ,KAEf,IAAIyZ,EAAQtY,EAAOsY,MAAOlX,EAAMvC,GAC/Byf,EAAchG,EAAM/X,OACpBJ,EAAKmY,EAAMnO,QACXoU,EAAQve,EAAOwe,YAAapd,EAAMvC,GAMvB,eAAPsB,IACJA,EAAKmY,EAAMnO,QACXmU,KAGIne,IAIU,OAATtB,GACJyZ,EAAM5K,QAAS,qBAIT6Q,EAAME,KACbte,EAAGhF,KAAMiG,GApBF,WACNpB,EAAOqe,QAASjd,EAAMvC,KAmBF0f,KAGhBD,GAAeC,GACpBA,EAAM3F,MAAMJ,QAKdgG,YAAa,SAAUpd,EAAMvC,GAC5B,IAAItC,EAAMsC,EAAO,aACjB,OAAO6e,EAAS7hB,IAAKuF,EAAM7E,IAASmhB,EAAStB,OAAQhb,EAAM7E,EAAK,CAC/Dqc,MAAO5Y,EAAO+X,UAAW,eAAgBvB,KAAK,WAC7CkH,EAAS/E,OAAQvX,EAAM,CAAEvC,EAAO,QAAStC,YAM7CyD,EAAOG,GAAG4B,OAAQ,CACjBuW,MAAO,SAAUzZ,EAAM0e,GACtB,IAAImB,EAAS,EAQb,MANqB,iBAAT7f,IACX0e,EAAO1e,EACPA,EAAO,KACP6f,KAGIpd,UAAUf,OAASme,EAChB1e,EAAOsY,MAAOrb,KAAM,GAAK4B,QAGjB4D,IAAT8a,EACNtgB,KACAA,KAAKgE,MAAM,WACV,IAAIqX,EAAQtY,EAAOsY,MAAOrb,KAAM4B,EAAM0e,GAGtCvd,EAAOwe,YAAavhB,KAAM4B,GAEZ,OAATA,GAAgC,eAAfyZ,EAAO,IAC5BtY,EAAOqe,QAASphB,KAAM4B,OAI1Bwf,QAAS,SAAUxf,GAClB,OAAO5B,KAAKgE,MAAM,WACjBjB,EAAOqe,QAASphB,KAAM4B,OAGxB8f,WAAY,SAAU9f,GACrB,OAAO5B,KAAKqb,MAAOzZ,GAAQ,KAAM,KAKlC+Y,QAAS,SAAU/Y,EAAMJ,GACxB,IAAIgO,EACHmS,EAAQ,EACRC,EAAQ7e,EAAOgZ,WACfpL,EAAW3Q,KACXjC,EAAIiC,KAAKsD,OACTiX,EAAU,aACCoH,GACTC,EAAMtE,YAAa3M,EAAU,CAAEA,KAUlC,IANqB,iBAAT/O,IACXJ,EAAMI,EACNA,OAAO4D,GAER5D,EAAOA,GAAQ,KAEP7D,MACPyR,EAAMiR,EAAS7hB,IAAK+R,EAAU5S,GAAK6D,EAAO,gBAC9B4N,EAAImM,QACfgG,IACAnS,EAAImM,MAAMpC,IAAKgB,IAIjB,OADAA,IACOqH,EAAMjH,QAASnZ,MAGxB,IAAIqgB,GAAO,sCAA0CC,OAEjDC,GAAU,IAAIvY,OAAQ,iBAAmBqY,GAAO,cAAe,KAG/DG,GAAY,CAAE,MAAO,QAAS,SAAU,QAExCxT,GAAkBlO,EAASkO,gBAI1ByT,GAAa,SAAU9d,GACzB,OAAOpB,EAAOmF,SAAU/D,EAAK8H,cAAe9H,IAE7C+d,GAAW,CAAEA,UAAU,GAOnB1T,GAAgB2T,cACpBF,GAAa,SAAU9d,GACtB,OAAOpB,EAAOmF,SAAU/D,EAAK8H,cAAe9H,IAC3CA,EAAKge,YAAaD,MAAe/d,EAAK8H,gBAG1C,IAAImW,GAAqB,SAAUje,EAAMkJ,GAOvC,MAA8B,UAH9BlJ,EAAOkJ,GAAMlJ,GAGDke,MAAMC,SACM,KAAvBne,EAAKke,MAAMC,SAMXL,GAAY9d,IAEsB,SAAlCpB,EAAOwf,IAAKpe,EAAM,YAGjBqe,GAAO,SAAUre,EAAMY,EAASd,EAAU+O,GAC7C,IAAInP,EAAKvF,EACRmkB,EAAM,GAGP,IAAMnkB,KAAQyG,EACb0d,EAAKnkB,GAAS6F,EAAKke,MAAO/jB,GAC1B6F,EAAKke,MAAO/jB,GAASyG,EAASzG,GAM/B,IAAMA,KAHNuF,EAAMI,EAASG,MAAOD,EAAM6O,GAAQ,IAGtBjO,EACbZ,EAAKke,MAAO/jB,GAASmkB,EAAKnkB,GAG3B,OAAOuF,GAMR,SAAS6e,GAAWve,EAAMoc,EAAMoC,EAAYC,GAC3C,IAAIC,EAAUC,EACbC,EAAgB,GAChBC,EAAeJ,EACd,WACC,OAAOA,EAAMjV,OAEd,WACC,OAAO5K,EAAOwf,IAAKpe,EAAMoc,EAAM,KAEjC0C,EAAUD,IACVE,EAAOP,GAAcA,EAAY,KAAS5f,EAAOogB,UAAW5C,GAAS,GAAK,MAG1E6C,EAAgBjf,EAAK1C,WAClBsB,EAAOogB,UAAW5C,IAAmB,OAAT2C,IAAkBD,IAChDlB,GAAQ7V,KAAMnJ,EAAOwf,IAAKpe,EAAMoc,IAElC,GAAK6C,GAAiBA,EAAe,KAAQF,EAAO,CAYnD,IARAD,GAAoB,EAGpBC,EAAOA,GAAQE,EAAe,GAG9BA,GAAiBH,GAAW,EAEpBF,KAIPhgB,EAAOsf,MAAOle,EAAMoc,EAAM6C,EAAgBF,IACnC,EAAIJ,IAAY,GAAMA,EAAQE,IAAiBC,GAAW,MAAW,IAC3EF,EAAgB,GAEjBK,GAAgCN,EAIjCM,GAAgC,EAChCrgB,EAAOsf,MAAOle,EAAMoc,EAAM6C,EAAgBF,GAG1CP,EAAaA,GAAc,GAgB5B,OAbKA,IACJS,GAAiBA,IAAkBH,GAAW,EAG9CJ,EAAWF,EAAY,GACtBS,GAAkBT,EAAY,GAAM,GAAMA,EAAY,IACrDA,EAAY,GACTC,IACJA,EAAMM,KAAOA,EACbN,EAAMlQ,MAAQ0Q,EACdR,EAAMje,IAAMke,IAGPA,EAIR,IAAIQ,GAAoB,GAExB,SAASC,GAAmBnf,GAC3B,IAAIsR,EACHtT,EAAMgC,EAAK8H,cACXX,EAAWnH,EAAKmH,SAChBgX,EAAUe,GAAmB/X,GAE9B,OAAKgX,IAIL7M,EAAOtT,EAAIohB,KAAK5gB,YAAaR,EAAIG,cAAegJ,IAChDgX,EAAUvf,EAAOwf,IAAK9M,EAAM,WAE5BA,EAAK7S,WAAWC,YAAa4S,GAEZ,SAAZ6M,IACJA,EAAU,SAEXe,GAAmB/X,GAAagX,EAEzBA,GAGR,SAASkB,GAAU7S,EAAU8S,GAO5B,IANA,IAAInB,EAASne,EACZuf,EAAS,GACTrK,EAAQ,EACR/V,EAASqN,EAASrN,OAGX+V,EAAQ/V,EAAQ+V,KACvBlV,EAAOwM,EAAU0I,IACNgJ,QAIXC,EAAUne,EAAKke,MAAMC,QAChBmB,GAKa,SAAZnB,IACJoB,EAAQrK,GAAUoH,EAAS7hB,IAAKuF,EAAM,YAAe,KAC/Cuf,EAAQrK,KACblV,EAAKke,MAAMC,QAAU,KAGK,KAAvBne,EAAKke,MAAMC,SAAkBF,GAAoBje,KACrDuf,EAAQrK,GAAUiK,GAAmBnf,KAGrB,SAAZme,IACJoB,EAAQrK,GAAU,OAGlBoH,EAASJ,IAAKlc,EAAM,UAAWme,KAMlC,IAAMjJ,EAAQ,EAAGA,EAAQ/V,EAAQ+V,IACR,MAAnBqK,EAAQrK,KACZ1I,EAAU0I,GAAQgJ,MAAMC,QAAUoB,EAAQrK,IAI5C,OAAO1I,EAGR5N,EAAOG,GAAG4B,OAAQ,CACjB2e,KAAM,WACL,OAAOD,GAAUxjB,MAAM,IAExB2jB,KAAM,WACL,OAAOH,GAAUxjB,OAElB4jB,OAAQ,SAAU1H,GACjB,MAAsB,kBAAVA,EACJA,EAAQlc,KAAKyjB,OAASzjB,KAAK2jB,OAG5B3jB,KAAKgE,MAAM,WACZoe,GAAoBpiB,MACxB+C,EAAQ/C,MAAOyjB,OAEf1gB,EAAQ/C,MAAO2jB,aAKnB,IAAIE,GAAiB,wBAEjBC,GAAW,iCAEXC,GAAc,qCAKdC,GAAU,CAGbC,OAAQ,CAAE,EAAG,+BAAgC,aAK7CC,MAAO,CAAE,EAAG,UAAW,YACvBC,IAAK,CAAE,EAAG,oBAAqB,uBAC/BC,GAAI,CAAE,EAAG,iBAAkB,oBAC3BC,GAAI,CAAE,EAAG,qBAAsB,yBAE/BC,SAAU,CAAE,EAAG,GAAI,KAUpB,SAASC,GAAQthB,EAASsM,GAIzB,IAAI1L,EAYJ,OATCA,OAD4C,IAAjCZ,EAAQoJ,qBACbpJ,EAAQoJ,qBAAsBkD,GAAO,UAEI,IAA7BtM,EAAQ2J,iBACpB3J,EAAQ2J,iBAAkB2C,GAAO,KAGjC,QAGM/J,IAAR+J,GAAqBA,GAAOjE,EAAUrI,EAASsM,GAC5CxM,EAAOe,MAAO,CAAEb,GAAWY,GAG5BA,EAKR,SAAS2gB,GAAe5gB,EAAO6gB,GAI9B,IAHA,IAAI1mB,EAAI,EACPC,EAAI4F,EAAMN,OAEHvF,EAAIC,EAAGD,IACd0iB,EAASJ,IACRzc,EAAO7F,GACP,cACC0mB,GAAehE,EAAS7hB,IAAK6lB,EAAa1mB,GAAK,eAvCnDimB,GAAQU,SAAWV,GAAQC,OAE3BD,GAAQW,MAAQX,GAAQY,MAAQZ,GAAQa,SAAWb,GAAQc,QAAUd,GAAQE,MAC7EF,GAAQe,GAAKf,GAAQK,GA0CrB,IA8FEW,GACAtV,GA/FE3F,GAAQ,YAEZ,SAASkb,GAAerhB,EAAOX,EAASiiB,EAASC,EAAWC,GAO3D,IANA,IAAIjhB,EAAMqL,EAAKD,EAAK8V,EAAMC,EAAU5gB,EACnC6gB,EAAWtiB,EAAQuiB,yBACnBC,EAAQ,GACR1nB,EAAI,EACJC,EAAI4F,EAAMN,OAEHvF,EAAIC,EAAGD,IAGd,IAFAoG,EAAOP,EAAO7F,KAEQ,IAAToG,EAGZ,GAAwB,WAAnBrB,EAAQqB,GAIZpB,EAAOe,MAAO2hB,EAAOthB,EAAK1C,SAAW,CAAE0C,GAASA,QAG1C,GAAM4F,GAAMyC,KAAMrI,GAIlB,CAUN,IATAqL,EAAMA,GAAO+V,EAAS5iB,YAAaM,EAAQX,cAAe,QAG1DiN,GAAQuU,GAAS5X,KAAM/H,IAAU,CAAE,GAAI,KAAQ,GAAI+C,cACnDme,EAAOrB,GAASzU,IAASyU,GAAQM,SACjC9U,EAAIC,UAAY4V,EAAM,GAAMtiB,EAAO2iB,cAAevhB,GAASkhB,EAAM,GAGjE3gB,EAAI2gB,EAAM,GACF3gB,KACP8K,EAAMA,EAAIqD,UAKX9P,EAAOe,MAAO2hB,EAAOjW,EAAI/D,aAGzB+D,EAAM+V,EAASlU,YAGXD,YAAc,QAzBlBqU,EAAM1kB,KAAMkC,EAAQ0iB,eAAgBxhB,IAkCvC,IAHAohB,EAASnU,YAAc,GAEvBrT,EAAI,EACMoG,EAAOshB,EAAO1nB,MAGvB,GAAKonB,GAAapiB,EAAOyD,QAASrC,EAAMghB,IAAe,EACjDC,GACJA,EAAQrkB,KAAMoD,QAgBhB,GAXAmhB,EAAWrD,GAAY9d,GAGvBqL,EAAM+U,GAAQgB,EAAS5iB,YAAawB,GAAQ,UAGvCmhB,GACJd,GAAehV,GAIX0V,EAEJ,IADAxgB,EAAI,EACMP,EAAOqL,EAAK9K,MAChBqf,GAAYvX,KAAMrI,EAAKvC,MAAQ,KACnCsjB,EAAQnkB,KAAMoD,GAMlB,OAAOohB,EAMNP,GADc1kB,EAASklB,yBACR7iB,YAAarC,EAASgC,cAAe,SACpDoN,GAAQpP,EAASgC,cAAe,UAM3BG,aAAc,OAAQ,SAC5BiN,GAAMjN,aAAc,UAAW,WAC/BiN,GAAMjN,aAAc,OAAQ,KAE5BuiB,GAAIriB,YAAa+M,IAIjBpO,EAAQskB,WAAaZ,GAAIa,WAAW,GAAOA,WAAW,GAAOhT,UAAUiB,QAIvEkR,GAAIvV,UAAY,yBAChBnO,EAAQwkB,iBAAmBd,GAAIa,WAAW,GAAOhT,UAAU4E,aAI5D,IACCsO,GAAY,OACZC,GAAc,iDACdC,GAAiB,sBAElB,SAASC,KACR,OAAO,EAGR,SAASC,KACR,OAAO,EASR,SAASC,GAAYjiB,EAAMvC,GAC1B,OAASuC,IAMV,WACC,IACC,OAAO7D,EAASoT,cACf,MAAQ2S,KATQC,KAAqC,UAAT1kB,GAY/C,SAAS2kB,GAAIpiB,EAAMqiB,EAAOxjB,EAAUsd,EAAMpd,EAAIujB,GAC7C,IAAIC,EAAQ9kB,EAGZ,GAAsB,iBAAV4kB,EAAqB,CAShC,IAAM5kB,IANmB,iBAAboB,IAGXsd,EAAOA,GAAQtd,EACfA,OAAWwC,GAEEghB,EACbD,GAAIpiB,EAAMvC,EAAMoB,EAAUsd,EAAMkG,EAAO5kB,GAAQ6kB,GAEhD,OAAOtiB,EAsBR,GAnBa,MAARmc,GAAsB,MAANpd,GAGpBA,EAAKF,EACLsd,EAAOtd,OAAWwC,GACD,MAANtC,IACc,iBAAbF,GAGXE,EAAKod,EACLA,OAAO9a,IAIPtC,EAAKod,EACLA,EAAOtd,EACPA,OAAWwC,KAGD,IAAPtC,EACJA,EAAKijB,QACC,IAAMjjB,EACZ,OAAOiB,EAeR,OAZa,IAARsiB,IACJC,EAASxjB,GACTA,EAAK,SAAUyjB,GAId,OADA5jB,IAAS6jB,IAAKD,GACPD,EAAOtiB,MAAOpE,KAAMqE,aAIzB0C,KAAO2f,EAAO3f,OAAU2f,EAAO3f,KAAOhE,EAAOgE,SAE1C5C,EAAKH,MAAM,WACjBjB,EAAO4jB,MAAMpN,IAAKvZ,KAAMwmB,EAAOtjB,EAAIod,EAAMtd,MA4a3C,SAAS6jB,GAAgBxZ,EAAIzL,EAAMwkB,GAG5BA,GAQN3F,EAASJ,IAAKhT,EAAIzL,GAAM,GACxBmB,EAAO4jB,MAAMpN,IAAKlM,EAAIzL,EAAM,CAC3B0M,WAAW,EACXd,QAAS,SAAUmZ,GAClB,IAAIG,EAAU7U,EACb8U,EAAQtG,EAAS7hB,IAAKoB,KAAM4B,GAE7B,GAAyB,EAAlB+kB,EAAMK,WAAmBhnB,KAAM4B,IAKrC,GAAMmlB,EAAMzjB,QAiCEP,EAAO4jB,MAAM1J,QAASrb,IAAU,IAAKqlB,cAClDN,EAAMO,uBAfN,GAdAH,EAAQlmB,EAAM3C,KAAMmG,WACpBoc,EAASJ,IAAKrgB,KAAM4B,EAAMmlB,GAK1BD,EAAWV,EAAYpmB,KAAM4B,GAC7B5B,KAAM4B,KAEDmlB,KADL9U,EAASwO,EAAS7hB,IAAKoB,KAAM4B,KACJklB,EACxBrG,EAASJ,IAAKrgB,KAAM4B,GAAM,GAE1BqQ,EAAS,GAEL8U,IAAU9U,EAKd,OAFA0U,EAAMQ,2BACNR,EAAMS,iBACCnV,EAAOjT,WAeL+nB,EAAMzjB,SAGjBmd,EAASJ,IAAKrgB,KAAM4B,EAAM,CACzB5C,MAAO+D,EAAO4jB,MAAMU,QAInBtkB,EAAO+B,OAAQiiB,EAAO,GAAKhkB,EAAOukB,MAAM3nB,WACxConB,EAAMlmB,MAAO,GACbb,QAKF2mB,EAAMQ,qCAzE0B3hB,IAA7Bib,EAAS7hB,IAAKyO,EAAIzL,IACtBmB,EAAO4jB,MAAMpN,IAAKlM,EAAIzL,EAAMskB,IAza/BnjB,EAAO4jB,MAAQ,CAEdvmB,OAAQ,GAERmZ,IAAK,SAAUpV,EAAMqiB,EAAOhZ,EAAS8S,EAAMtd,GAE1C,IAAIukB,EAAaC,EAAahY,EAC7BiY,EAAQxoB,EAAGyoB,EACXzK,EAAS0K,EAAU/lB,EAAMgmB,EAAYC,EACrCC,EAAWrH,EAAS7hB,IAAKuF,GAG1B,GAAM2jB,EAuCN,IAlCKta,EAAQA,UAEZA,GADA+Z,EAAc/Z,GACQA,QACtBxK,EAAWukB,EAAYvkB,UAKnBA,GACJD,EAAOsM,KAAKM,gBAAiBnB,GAAiBxL,GAIzCwK,EAAQzG,OACbyG,EAAQzG,KAAOhE,EAAOgE,SAIf0gB,EAASK,EAASL,UACzBA,EAASK,EAASL,OAAS,KAEpBD,EAAcM,EAASC,UAC9BP,EAAcM,EAASC,OAAS,SAAU7nB,GAIzC,YAAyB,IAAX6C,GAA0BA,EAAO4jB,MAAMqB,YAAc9nB,EAAE0B,KACpEmB,EAAO4jB,MAAMsB,SAAS7jB,MAAOD,EAAME,gBAAcmB,IAMpDvG,GADAunB,GAAUA,GAAS,IAAK3a,MAAOoO,IAAmB,CAAE,KAC1C3W,OACFrE,KAEP2C,EAAOimB,GADPrY,EAAMyW,GAAe/Z,KAAMsa,EAAOvnB,KAAS,IACpB,GACvB2oB,GAAepY,EAAK,IAAO,IAAKvI,MAAO,KAAMrC,OAGvChD,IAKNqb,EAAUla,EAAO4jB,MAAM1J,QAASrb,IAAU,GAG1CA,GAASoB,EAAWia,EAAQgK,aAAehK,EAAQiL,WAActmB,EAGjEqb,EAAUla,EAAO4jB,MAAM1J,QAASrb,IAAU,GAG1C8lB,EAAY3kB,EAAO+B,OAAQ,CAC1BlD,KAAMA,EACNimB,SAAUA,EACVvH,KAAMA,EACN9S,QAASA,EACTzG,KAAMyG,EAAQzG,KACd/D,SAAUA,EACVkV,aAAclV,GAAYD,EAAO2N,KAAK7E,MAAMqM,aAAa1L,KAAMxJ,GAC/DsL,UAAWsZ,EAAWlb,KAAM,MAC1B6a,IAGKI,EAAWF,EAAQ7lB,OAC1B+lB,EAAWF,EAAQ7lB,GAAS,IACnBumB,cAAgB,EAGnBlL,EAAQmL,QACiD,IAA9DnL,EAAQmL,MAAMlqB,KAAMiG,EAAMmc,EAAMsH,EAAYJ,IAEvCrjB,EAAK0K,kBACT1K,EAAK0K,iBAAkBjN,EAAM4lB,IAK3BvK,EAAQ1D,MACZ0D,EAAQ1D,IAAIrb,KAAMiG,EAAMujB,GAElBA,EAAUla,QAAQzG,OACvB2gB,EAAUla,QAAQzG,KAAOyG,EAAQzG,OAK9B/D,EACJ2kB,EAAS9iB,OAAQ8iB,EAASQ,gBAAiB,EAAGT,GAE9CC,EAAS5mB,KAAM2mB,GAIhB3kB,EAAO4jB,MAAMvmB,OAAQwB,IAAS,IAMhC8Z,OAAQ,SAAUvX,EAAMqiB,EAAOhZ,EAASxK,EAAUqlB,GAEjD,IAAI3jB,EAAG4jB,EAAW9Y,EACjBiY,EAAQxoB,EAAGyoB,EACXzK,EAAS0K,EAAU/lB,EAAMgmB,EAAYC,EACrCC,EAAWrH,EAASD,QAASrc,IAAUsc,EAAS7hB,IAAKuF,GAEtD,GAAM2jB,IAAeL,EAASK,EAASL,QAAvC,CAOA,IADAxoB,GADAunB,GAAUA,GAAS,IAAK3a,MAAOoO,IAAmB,CAAE,KAC1C3W,OACFrE,KAMP,GAJA2C,EAAOimB,GADPrY,EAAMyW,GAAe/Z,KAAMsa,EAAOvnB,KAAS,IACpB,GACvB2oB,GAAepY,EAAK,IAAO,IAAKvI,MAAO,KAAMrC,OAGvChD,EAAN,CAeA,IARAqb,EAAUla,EAAO4jB,MAAM1J,QAASrb,IAAU,GAE1C+lB,EAAWF,EADX7lB,GAASoB,EAAWia,EAAQgK,aAAehK,EAAQiL,WAActmB,IACpC,GAC7B4N,EAAMA,EAAK,IACV,IAAIhG,OAAQ,UAAYoe,EAAWlb,KAAM,iBAAoB,WAG9D4b,EAAY5jB,EAAIijB,EAASrkB,OACjBoB,KACPgjB,EAAYC,EAAUjjB,IAEf2jB,GAAeR,IAAaH,EAAUG,UACzCra,GAAWA,EAAQzG,OAAS2gB,EAAU3gB,MACtCyI,IAAOA,EAAIhD,KAAMkb,EAAUpZ,YAC3BtL,GAAYA,IAAa0kB,EAAU1kB,WACxB,OAAbA,IAAqB0kB,EAAU1kB,YAChC2kB,EAAS9iB,OAAQH,EAAG,GAEfgjB,EAAU1kB,UACd2kB,EAASQ,gBAELlL,EAAQvB,QACZuB,EAAQvB,OAAOxd,KAAMiG,EAAMujB,IAOzBY,IAAcX,EAASrkB,SACrB2Z,EAAQsL,WACkD,IAA/DtL,EAAQsL,SAASrqB,KAAMiG,EAAMyjB,EAAYE,EAASC,SAElDhlB,EAAOylB,YAAarkB,EAAMvC,EAAMkmB,EAASC,eAGnCN,EAAQ7lB,SA1Cf,IAAMA,KAAQ6lB,EACb1kB,EAAO4jB,MAAMjL,OAAQvX,EAAMvC,EAAO4kB,EAAOvnB,GAAKuO,EAASxK,GAAU,GA8C/DD,EAAOoD,cAAeshB,IAC1BhH,EAAS/E,OAAQvX,EAAM,mBAIzB8jB,SAAU,SAAUQ,GAGnB,IAEI1qB,EAAG2G,EAAGb,EAAKsP,EAASuU,EAAWgB,EAF/B/B,EAAQ5jB,EAAO4jB,MAAMgC,IAAKF,GAG7BzV,EAAO,IAAI1N,MAAOjB,UAAUf,QAC5BqkB,GAAalH,EAAS7hB,IAAKoB,KAAM,WAAc,IAAM2mB,EAAM/kB,OAAU,GACrEqb,EAAUla,EAAO4jB,MAAM1J,QAAS0J,EAAM/kB,OAAU,GAKjD,IAFAoR,EAAM,GAAM2T,EAEN5oB,EAAI,EAAGA,EAAIsG,UAAUf,OAAQvF,IAClCiV,EAAMjV,GAAMsG,UAAWtG,GAMxB,GAHA4oB,EAAMiC,eAAiB5oB,MAGlBid,EAAQ4L,cAA2D,IAA5C5L,EAAQ4L,YAAY3qB,KAAM8B,KAAM2mB,GAA5D,CASA,IAJA+B,EAAe3lB,EAAO4jB,MAAMgB,SAASzpB,KAAM8B,KAAM2mB,EAAOgB,GAGxD5pB,EAAI,GACMoV,EAAUuV,EAAc3qB,QAAY4oB,EAAMmC,wBAInD,IAHAnC,EAAMoC,cAAgB5V,EAAQhP,KAE9BO,EAAI,GACMgjB,EAAYvU,EAAQwU,SAAUjjB,QACtCiiB,EAAMqC,iCAIDrC,EAAMsC,aAAsC,IAAxBvB,EAAUpZ,YACnCqY,EAAMsC,WAAWzc,KAAMkb,EAAUpZ,aAEjCqY,EAAMe,UAAYA,EAClBf,EAAMrG,KAAOoH,EAAUpH,UAKV9a,KAHb3B,IAAUd,EAAO4jB,MAAM1J,QAASyK,EAAUG,WAAc,IAAKE,QAC5DL,EAAUla,SAAUpJ,MAAO+O,EAAQhP,KAAM6O,MAGT,KAAzB2T,EAAM1U,OAASpO,KACrB8iB,EAAMS,iBACNT,EAAMO,oBAYX,OAJKjK,EAAQiM,cACZjM,EAAQiM,aAAahrB,KAAM8B,KAAM2mB,GAG3BA,EAAM1U,SAGd0V,SAAU,SAAUhB,EAAOgB,GAC1B,IAAI5pB,EAAG2pB,EAAW3W,EAAKoY,EAAiBC,EACvCV,EAAe,GACfP,EAAgBR,EAASQ,cACzBxa,EAAMgZ,EAAMxhB,OAGb,GAAKgjB,GAIJxa,EAAIlM,YAOc,UAAfklB,EAAM/kB,MAAoB+kB,EAAM0C,QAAU,GAE7C,KAAQ1b,IAAQ3N,KAAM2N,EAAMA,EAAI/K,YAAc5C,KAI7C,GAAsB,IAAjB2N,EAAIlM,WAAoC,UAAfklB,EAAM/kB,OAAqC,IAAjB+L,EAAItC,UAAsB,CAGjF,IAFA8d,EAAkB,GAClBC,EAAmB,GACbrrB,EAAI,EAAGA,EAAIoqB,EAAepqB,SAMEyH,IAA5B4jB,EAFLrY,GAHA2W,EAAYC,EAAU5pB,IAGNiF,SAAW,OAG1BomB,EAAkBrY,GAAQ2W,EAAUxP,aACnCnV,EAAQgO,EAAK/Q,MAAOqZ,MAAO1L,IAAS,EACpC5K,EAAOsM,KAAM0B,EAAK/Q,KAAM,KAAM,CAAE2N,IAAQrK,QAErC8lB,EAAkBrY,IACtBoY,EAAgBpoB,KAAM2mB,GAGnByB,EAAgB7lB,QACpBolB,EAAa3nB,KAAM,CAAEoD,KAAMwJ,EAAKga,SAAUwB,IAY9C,OALAxb,EAAM3N,KACDmoB,EAAgBR,EAASrkB,QAC7BolB,EAAa3nB,KAAM,CAAEoD,KAAMwJ,EAAKga,SAAUA,EAAS9mB,MAAOsnB,KAGpDO,GAGRY,QAAS,SAAUhrB,EAAMirB,GACxB9qB,OAAOC,eAAgBqE,EAAOukB,MAAM3nB,UAAWrB,EAAM,CACpDK,YAAY,EACZyhB,cAAc,EAEdxhB,IAAK2C,EAAYgoB,GAChB,WACC,GAAKvpB,KAAKwpB,cACR,OAAOD,EAAMvpB,KAAKwpB,gBAGrB,WACC,GAAKxpB,KAAKwpB,cACR,OAAOxpB,KAAKwpB,cAAelrB,IAI/B+hB,IAAK,SAAUrhB,GACdP,OAAOC,eAAgBsB,KAAM1B,EAAM,CAClCK,YAAY,EACZyhB,cAAc,EACdqJ,UAAU,EACVzqB,MAAOA,QAMX2pB,IAAK,SAAUa,GACd,OAAOA,EAAezmB,EAAO0C,SAC5B+jB,EACA,IAAIzmB,EAAOukB,MAAOkC,IAGpBvM,QAAS,CACRyM,KAAM,CAGLC,UAAU,GAEXC,MAAO,CAGNxB,MAAO,SAAU9H,GAIhB,IAAIjT,EAAKrN,MAAQsgB,EAWjB,OARKuD,GAAerX,KAAMa,EAAGzL,OAC5ByL,EAAGuc,OAASte,EAAU+B,EAAI,UAG1BwZ,GAAgBxZ,EAAI,QAAS6Y,KAIvB,GAERmB,QAAS,SAAU/G,GAIlB,IAAIjT,EAAKrN,MAAQsgB,EAUjB,OAPKuD,GAAerX,KAAMa,EAAGzL,OAC5ByL,EAAGuc,OAASte,EAAU+B,EAAI,UAE1BwZ,GAAgBxZ,EAAI,UAId,GAKRiX,SAAU,SAAUqC,GACnB,IAAIxhB,EAASwhB,EAAMxhB,OACnB,OAAO0e,GAAerX,KAAMrH,EAAOvD,OAClCuD,EAAOykB,OAASte,EAAUnG,EAAQ,UAClCsb,EAAS7hB,IAAKuG,EAAQ,UACtBmG,EAAUnG,EAAQ,OAIrB0kB,aAAc,CACbX,aAAc,SAAUvC,QAIDnhB,IAAjBmhB,EAAM1U,QAAwB0U,EAAM6C,gBACxC7C,EAAM6C,cAAcM,YAAcnD,EAAM1U,YA8F7ClP,EAAOylB,YAAc,SAAUrkB,EAAMvC,EAAMmmB,GAGrC5jB,EAAK0a,qBACT1a,EAAK0a,oBAAqBjd,EAAMmmB,IAIlChlB,EAAOukB,MAAQ,SAAUzlB,EAAKkoB,GAG7B,KAAQ/pB,gBAAgB+C,EAAOukB,OAC9B,OAAO,IAAIvkB,EAAOukB,MAAOzlB,EAAKkoB,GAI1BloB,GAAOA,EAAID,MACf5B,KAAKwpB,cAAgB3nB,EACrB7B,KAAK4B,KAAOC,EAAID,KAIhB5B,KAAKgqB,mBAAqBnoB,EAAIooB,uBACHzkB,IAAzB3D,EAAIooB,mBAGgB,IAApBpoB,EAAIioB,YACL5D,GACAC,GAKDnmB,KAAKmF,OAAWtD,EAAIsD,QAAkC,IAAxBtD,EAAIsD,OAAO1D,SACxCI,EAAIsD,OAAOvC,WACXf,EAAIsD,OAELnF,KAAK+oB,cAAgBlnB,EAAIknB,cACzB/oB,KAAKkqB,cAAgBroB,EAAIqoB,eAIzBlqB,KAAK4B,KAAOC,EAIRkoB,GACJhnB,EAAO+B,OAAQ9E,KAAM+pB,GAItB/pB,KAAKmqB,UAAYtoB,GAAOA,EAAIsoB,WAAahiB,KAAKiiB,MAG9CpqB,KAAM+C,EAAO0C,UAAY,GAK1B1C,EAAOukB,MAAM3nB,UAAY,CACxB6D,YAAaT,EAAOukB,MACpB0C,mBAAoB7D,GACpB2C,qBAAsB3C,GACtB6C,8BAA+B7C,GAC/BkE,aAAa,EAEbjD,eAAgB,WACf,IAAIlnB,EAAIF,KAAKwpB,cAEbxpB,KAAKgqB,mBAAqB9D,GAErBhmB,IAAMF,KAAKqqB,aACfnqB,EAAEknB,kBAGJF,gBAAiB,WAChB,IAAIhnB,EAAIF,KAAKwpB,cAEbxpB,KAAK8oB,qBAAuB5C,GAEvBhmB,IAAMF,KAAKqqB,aACfnqB,EAAEgnB,mBAGJC,yBAA0B,WACzB,IAAIjnB,EAAIF,KAAKwpB,cAEbxpB,KAAKgpB,8BAAgC9C,GAEhChmB,IAAMF,KAAKqqB,aACfnqB,EAAEinB,2BAGHnnB,KAAKknB,oBAKPnkB,EAAOiB,KAAM,CACZsmB,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,gBAAgB,EAChBC,SAAS,EACTC,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,OAAO,EACPC,OAAO,EACPC,UAAU,EACVC,MAAM,EACN,MAAQ,EACRhpB,MAAM,EACNipB,UAAU,EACV5rB,KAAK,EACL6rB,SAAS,EACT9B,QAAQ,EACR+B,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,SAAS,EACTC,SAAS,EACTC,eAAe,EACfC,WAAW,EACXC,SAAS,EAETC,MAAO,SAAUrF,GAChB,IAAI0C,EAAS1C,EAAM0C,OAGnB,OAAoB,MAAf1C,EAAMqF,OAAiBjG,GAAUvZ,KAAMma,EAAM/kB,MACxB,MAAlB+kB,EAAMuE,SAAmBvE,EAAMuE,SAAWvE,EAAMwE,SAIlDxE,EAAMqF,YAAoBxmB,IAAX6jB,GAAwBrD,GAAYxZ,KAAMma,EAAM/kB,MACtD,EAATynB,EACG,EAGM,EAATA,EACG,EAGM,EAATA,EACG,EAGD,EAGD1C,EAAMqF,QAEZjpB,EAAO4jB,MAAM2C,SAEhBvmB,EAAOiB,KAAM,CAAEioB,MAAO,UAAWC,KAAM,aAAc,SAAUtqB,EAAMqlB,GACpElkB,EAAO4jB,MAAM1J,QAASrb,GAAS,CAG9BwmB,MAAO,WAQN,OAHAvB,GAAgB7mB,KAAM4B,EAAMwkB,KAGrB,GAERiB,QAAS,WAMR,OAHAR,GAAgB7mB,KAAM4B,IAGf,GAGRqlB,aAAcA,MAYhBlkB,EAAOiB,KAAM,CACZmoB,WAAY,YACZC,WAAY,WACZC,aAAc,cACdC,aAAc,eACZ,SAAUC,EAAM5D,GAClB5lB,EAAO4jB,MAAM1J,QAASsP,GAAS,CAC9BtF,aAAc0B,EACdT,SAAUS,EAEVZ,OAAQ,SAAUpB,GACjB,IAAI9iB,EACHsB,EAASnF,KACTwsB,EAAU7F,EAAMuD,cAChBxC,EAAYf,EAAMe,UASnB,OALM8E,IAAaA,IAAYrnB,GAAWpC,EAAOmF,SAAU/C,EAAQqnB,MAClE7F,EAAM/kB,KAAO8lB,EAAUG,SACvBhkB,EAAM6jB,EAAUla,QAAQpJ,MAAOpE,KAAMqE,WACrCsiB,EAAM/kB,KAAO+mB,GAEP9kB,OAKVd,EAAOG,GAAG4B,OAAQ,CAEjByhB,GAAI,SAAUC,EAAOxjB,EAAUsd,EAAMpd,GACpC,OAAOqjB,GAAIvmB,KAAMwmB,EAAOxjB,EAAUsd,EAAMpd,IAEzCujB,IAAK,SAAUD,EAAOxjB,EAAUsd,EAAMpd,GACrC,OAAOqjB,GAAIvmB,KAAMwmB,EAAOxjB,EAAUsd,EAAMpd,EAAI,IAE7C0jB,IAAK,SAAUJ,EAAOxjB,EAAUE,GAC/B,IAAIwkB,EAAW9lB,EACf,GAAK4kB,GAASA,EAAMY,gBAAkBZ,EAAMkB,UAW3C,OARAA,EAAYlB,EAAMkB,UAClB3kB,EAAQyjB,EAAMoC,gBAAiBhC,IAC9Bc,EAAUpZ,UACToZ,EAAUG,SAAW,IAAMH,EAAUpZ,UACrCoZ,EAAUG,SACXH,EAAU1kB,SACV0kB,EAAUla,SAEJxN,KAER,GAAsB,iBAAVwmB,EAAqB,CAGhC,IAAM5kB,KAAQ4kB,EACbxmB,KAAK4mB,IAAKhlB,EAAMoB,EAAUwjB,EAAO5kB,IAElC,OAAO5B,KAWR,OATkB,IAAbgD,GAA0C,mBAAbA,IAGjCE,EAAKF,EACLA,OAAWwC,IAEA,IAAPtC,IACJA,EAAKijB,IAECnmB,KAAKgE,MAAM,WACjBjB,EAAO4jB,MAAMjL,OAAQ1b,KAAMwmB,EAAOtjB,EAAIF,SAMzC,IAKCypB,GAAY,8FAOZC,GAAe,wBAGfC,GAAW,oCACXC,GAAe,2CAGhB,SAASC,GAAoB1oB,EAAM4V,GAClC,OAAKzO,EAAUnH,EAAM,UACpBmH,EAA+B,KAArByO,EAAQtY,SAAkBsY,EAAUA,EAAQ1I,WAAY,OAE3DtO,EAAQoB,GAAO2U,SAAU,SAAW,IAGrC3U,EAIR,SAAS2oB,GAAe3oB,GAEvB,OADAA,EAAKvC,MAAyC,OAAhCuC,EAAK3B,aAAc,SAAsB,IAAM2B,EAAKvC,KAC3DuC,EAER,SAAS4oB,GAAe5oB,GAOvB,MAN2C,WAApCA,EAAKvC,MAAQ,IAAKf,MAAO,EAAG,GAClCsD,EAAKvC,KAAOuC,EAAKvC,KAAKf,MAAO,GAE7BsD,EAAK2I,gBAAiB,QAGhB3I,EAGR,SAAS6oB,GAAgBnrB,EAAKorB,GAC7B,IAAIlvB,EAAGC,EAAG4D,EAAMsrB,EAAUC,EAAUC,EAAUC,EAAU5F,EAExD,GAAuB,IAAlBwF,EAAKxrB,SAAV,CAKA,GAAKgf,EAASD,QAAS3e,KACtBqrB,EAAWzM,EAAStB,OAAQtd,GAC5BsrB,EAAW1M,EAASJ,IAAK4M,EAAMC,GAC/BzF,EAASyF,EAASzF,QAMjB,IAAM7lB,YAHCurB,EAASpF,OAChBoF,EAAS1F,OAAS,GAEJA,EACb,IAAM1pB,EAAI,EAAGC,EAAIypB,EAAQ7lB,GAAO0B,OAAQvF,EAAIC,EAAGD,IAC9CgF,EAAO4jB,MAAMpN,IAAK0T,EAAMrrB,EAAM6lB,EAAQ7lB,GAAQ7D,IAO7C2iB,EAASF,QAAS3e,KACtBurB,EAAW1M,EAASvB,OAAQtd,GAC5BwrB,EAAWtqB,EAAO+B,OAAQ,GAAIsoB,GAE9B1M,EAASL,IAAK4M,EAAMI,KAKtB,SAASC,GAAUzrB,EAAKorB,GACvB,IAAI3hB,EAAW2hB,EAAK3hB,SAASpE,cAGX,UAAboE,GAAwBuY,GAAerX,KAAM3K,EAAID,MACrDqrB,EAAKnZ,QAAUjS,EAAIiS,QAGK,UAAbxI,GAAqC,aAAbA,IACnC2hB,EAAKxV,aAAe5V,EAAI4V,cAI1B,SAAS8V,GAAUC,EAAYxa,EAAM/O,EAAUmhB,GAG9CpS,EAAOlS,EAAOsD,MAAO,GAAI4O,GAEzB,IAAIuS,EAAUjhB,EAAO4gB,EAASuI,EAAYvrB,EAAMC,EAC/CpE,EAAI,EACJC,EAAIwvB,EAAWlqB,OACfoqB,EAAW1vB,EAAI,EACfgB,EAAQgU,EAAM,GACd2a,EAAkBpsB,EAAYvC,GAG/B,GAAK2uB,GACD3vB,EAAI,GAAsB,iBAAVgB,IAChBsC,EAAQskB,YAAc+G,GAASngB,KAAMxN,GACxC,OAAOwuB,EAAWxpB,MAAM,SAAUqV,GACjC,IAAId,EAAOiV,EAAWjpB,GAAI8U,GACrBsU,IACJ3a,EAAM,GAAMhU,EAAMd,KAAM8B,KAAMqZ,EAAOd,EAAKqV,SAE3CL,GAAUhV,EAAMvF,EAAM/O,EAAUmhB,MAIlC,GAAKpnB,IAEJsG,GADAihB,EAAWN,GAAejS,EAAMwa,EAAY,GAAIvhB,eAAe,EAAOuhB,EAAYpI,IACjE/T,WAEmB,IAA/BkU,EAAS9Z,WAAWnI,SACxBiiB,EAAWjhB,GAIPA,GAAS8gB,GAAU,CAOvB,IALAqI,GADAvI,EAAUniB,EAAOmB,IAAKqgB,GAAQgB,EAAU,UAAYuH,KAC/BxpB,OAKbvF,EAAIC,EAAGD,IACdmE,EAAOqjB,EAEFxnB,IAAM2vB,IACVxrB,EAAOa,EAAOmC,MAAOhD,GAAM,GAAM,GAG5BurB,GAIJ1qB,EAAOe,MAAOohB,EAASX,GAAQriB,EAAM,YAIvC+B,EAAS/F,KAAMsvB,EAAYzvB,GAAKmE,EAAMnE,GAGvC,GAAK0vB,EAOJ,IANAtrB,EAAM+iB,EAASA,EAAQ5hB,OAAS,GAAI2I,cAGpClJ,EAAOmB,IAAKghB,EAAS6H,IAGfhvB,EAAI,EAAGA,EAAI0vB,EAAY1vB,IAC5BmE,EAAOgjB,EAASnnB,GACXgmB,GAAYvX,KAAMtK,EAAKN,MAAQ,MAClC6e,EAAStB,OAAQjd,EAAM,eACxBa,EAAOmF,SAAU/F,EAAKD,KAEjBA,EAAKL,KAA8C,YAArCK,EAAKN,MAAQ,IAAKsF,cAG/BnE,EAAO8qB,WAAa3rB,EAAKH,UAC7BgB,EAAO8qB,SAAU3rB,EAAKL,IAAK,CAC1BC,MAAOI,EAAKJ,OAASI,EAAKM,aAAc,WAI1CR,EAASE,EAAKkP,YAAYxL,QAASgnB,GAAc,IAAM1qB,EAAMC,IAQnE,OAAOqrB,EAGR,SAAS9R,GAAQvX,EAAMnB,EAAU8qB,GAKhC,IAJA,IAAI5rB,EACHujB,EAAQziB,EAAWD,EAAOoM,OAAQnM,EAAUmB,GAASA,EACrDpG,EAAI,EAE4B,OAAvBmE,EAAOujB,EAAO1nB,IAAeA,IAChC+vB,GAA8B,IAAlB5rB,EAAKT,UACtBsB,EAAOgrB,UAAWxJ,GAAQriB,IAGtBA,EAAKU,aACJkrB,GAAY7L,GAAY/f,IAC5BsiB,GAAeD,GAAQriB,EAAM,WAE9BA,EAAKU,WAAWC,YAAaX,IAI/B,OAAOiC,EAGRpB,EAAO+B,OAAQ,CACd4gB,cAAe,SAAUkI,GACxB,OAAOA,EAAKhoB,QAAS6mB,GAAW,cAGjCvnB,MAAO,SAAUf,EAAM6pB,EAAeC,GACrC,IAAIlwB,EAAGC,EAAGkwB,EAAaC,EACtBjpB,EAAQf,EAAK0hB,WAAW,GACxBuI,EAASnM,GAAY9d,GAGtB,KAAM7C,EAAQwkB,gBAAsC,IAAlB3hB,EAAK1C,UAAoC,KAAlB0C,EAAK1C,UAC3DsB,EAAO4U,SAAUxT,IAMnB,IAHAgqB,EAAe5J,GAAQrf,GAGjBnH,EAAI,EAAGC,GAFbkwB,EAAc3J,GAAQpgB,IAEOb,OAAQvF,EAAIC,EAAGD,IAC3CuvB,GAAUY,EAAanwB,GAAKowB,EAAcpwB,IAK5C,GAAKiwB,EACJ,GAAKC,EAIJ,IAHAC,EAAcA,GAAe3J,GAAQpgB,GACrCgqB,EAAeA,GAAgB5J,GAAQrf,GAEjCnH,EAAI,EAAGC,EAAIkwB,EAAY5qB,OAAQvF,EAAIC,EAAGD,IAC3CivB,GAAgBkB,EAAanwB,GAAKowB,EAAcpwB,SAGjDivB,GAAgB7oB,EAAMe,GAWxB,OANAipB,EAAe5J,GAAQrf,EAAO,WACZ5B,OAAS,GAC1BkhB,GAAe2J,GAAeC,GAAU7J,GAAQpgB,EAAM,WAIhDe,GAGR6oB,UAAW,SAAUnqB,GAKpB,IAJA,IAAI0c,EAAMnc,EAAMvC,EACfqb,EAAUla,EAAO4jB,MAAM1J,QACvBlf,EAAI,OAE6ByH,KAAxBrB,EAAOP,EAAO7F,IAAqBA,IAC5C,GAAKiiB,EAAY7b,GAAS,CACzB,GAAOmc,EAAOnc,EAAMsc,EAAShb,SAAc,CAC1C,GAAK6a,EAAKmH,OACT,IAAM7lB,KAAQ0e,EAAKmH,OACbxK,EAASrb,GACbmB,EAAO4jB,MAAMjL,OAAQvX,EAAMvC,GAI3BmB,EAAOylB,YAAarkB,EAAMvC,EAAM0e,EAAKyH,QAOxC5jB,EAAMsc,EAAShb,cAAYD,EAEvBrB,EAAMuc,EAASjb,WAInBtB,EAAMuc,EAASjb,cAAYD,OAOhCzC,EAAOG,GAAG4B,OAAQ,CACjBupB,OAAQ,SAAUrrB,GACjB,OAAO0Y,GAAQ1b,KAAMgD,GAAU,IAGhC0Y,OAAQ,SAAU1Y,GACjB,OAAO0Y,GAAQ1b,KAAMgD,IAGtBT,KAAM,SAAUvD,GACf,OAAOmgB,EAAQnf,MAAM,SAAUhB,GAC9B,YAAiBwG,IAAVxG,EACN+D,EAAOR,KAAMvC,MACbA,KAAK2b,QAAQ3X,MAAM,WACK,IAAlBhE,KAAKyB,UAAoC,KAAlBzB,KAAKyB,UAAqC,IAAlBzB,KAAKyB,WACxDzB,KAAKoR,YAAcpS,QAGpB,KAAMA,EAAOqF,UAAUf,SAG3BgrB,OAAQ,WACP,OAAOf,GAAUvtB,KAAMqE,WAAW,SAAUF,GACpB,IAAlBnE,KAAKyB,UAAoC,KAAlBzB,KAAKyB,UAAqC,IAAlBzB,KAAKyB,UAC3CorB,GAAoB7sB,KAAMmE,GAChCxB,YAAawB,OAKvBoqB,QAAS,WACR,OAAOhB,GAAUvtB,KAAMqE,WAAW,SAAUF,GAC3C,GAAuB,IAAlBnE,KAAKyB,UAAoC,KAAlBzB,KAAKyB,UAAqC,IAAlBzB,KAAKyB,SAAiB,CACzE,IAAI0D,EAAS0nB,GAAoB7sB,KAAMmE,GACvCgB,EAAOqpB,aAAcrqB,EAAMgB,EAAOkM,iBAKrCod,OAAQ,WACP,OAAOlB,GAAUvtB,KAAMqE,WAAW,SAAUF,GACtCnE,KAAK4C,YACT5C,KAAK4C,WAAW4rB,aAAcrqB,EAAMnE,UAKvC0uB,MAAO,WACN,OAAOnB,GAAUvtB,KAAMqE,WAAW,SAAUF,GACtCnE,KAAK4C,YACT5C,KAAK4C,WAAW4rB,aAAcrqB,EAAMnE,KAAK8N,iBAK5C6N,MAAO,WAIN,IAHA,IAAIxX,EACHpG,EAAI,EAE2B,OAAtBoG,EAAOnE,KAAMjC,IAAeA,IACd,IAAlBoG,EAAK1C,WAGTsB,EAAOgrB,UAAWxJ,GAAQpgB,GAAM,IAGhCA,EAAKiN,YAAc,IAIrB,OAAOpR,MAGRkF,MAAO,SAAU8oB,EAAeC,GAI/B,OAHAD,EAAiC,MAAjBA,GAAgCA,EAChDC,EAAyC,MAArBA,EAA4BD,EAAgBC,EAEzDjuB,KAAKkE,KAAK,WAChB,OAAOnB,EAAOmC,MAAOlF,KAAMguB,EAAeC,OAI5CL,KAAM,SAAU5uB,GACf,OAAOmgB,EAAQnf,MAAM,SAAUhB,GAC9B,IAAImF,EAAOnE,KAAM,IAAO,GACvBjC,EAAI,EACJC,EAAIgC,KAAKsD,OAEV,QAAekC,IAAVxG,GAAyC,IAAlBmF,EAAK1C,SAChC,OAAO0C,EAAKsL,UAIb,GAAsB,iBAAVzQ,IAAuB0tB,GAAalgB,KAAMxN,KACpDglB,IAAWF,GAAS5X,KAAMlN,IAAW,CAAE,GAAI,KAAQ,GAAIkI,eAAkB,CAE1ElI,EAAQ+D,EAAO2iB,cAAe1mB,GAE9B,IACC,KAAQjB,EAAIC,EAAGD,IAIS,KAHvBoG,EAAOnE,KAAMjC,IAAO,IAGV0D,WACTsB,EAAOgrB,UAAWxJ,GAAQpgB,GAAM,IAChCA,EAAKsL,UAAYzQ,GAInBmF,EAAO,EAGN,MAAQjE,KAGNiE,GACJnE,KAAK2b,QAAQ2S,OAAQtvB,KAEpB,KAAMA,EAAOqF,UAAUf,SAG3BqrB,YAAa,WACZ,IAAIvJ,EAAU,GAGd,OAAOmI,GAAUvtB,KAAMqE,WAAW,SAAUF,GAC3C,IAAIwO,EAAS3S,KAAK4C,WAEbG,EAAOyD,QAASxG,KAAMolB,GAAY,IACtCriB,EAAOgrB,UAAWxJ,GAAQvkB,OACrB2S,GACJA,EAAOic,aAAczqB,EAAMnE,SAK3BolB,MAILriB,EAAOiB,KAAM,CACZ6qB,SAAU,SACVC,UAAW,UACXN,aAAc,SACdO,YAAa,QACbC,WAAY,gBACV,SAAU1wB,EAAM2wB,GAClBlsB,EAAOG,GAAI5E,GAAS,SAAU0E,GAO7B,IANA,IAAIY,EACHC,EAAM,GACNqrB,EAASnsB,EAAQC,GACjBwB,EAAO0qB,EAAO5rB,OAAS,EACvBvF,EAAI,EAEGA,GAAKyG,EAAMzG,IAClB6F,EAAQ7F,IAAMyG,EAAOxE,KAAOA,KAAKkF,OAAO,GACxCnC,EAAQmsB,EAAQnxB,IAAOkxB,GAAYrrB,GAInC7C,EAAKqD,MAAOP,EAAKD,EAAMhF,OAGxB,OAAOoB,KAAK2D,UAAWE,OAGzB,IAAIsrB,GAAY,IAAI3lB,OAAQ,KAAOqY,GAAO,kBAAmB,KAEzDuN,GAAY,SAAUjrB,GAKxB,IAAI8mB,EAAO9mB,EAAK8H,cAAc0C,YAM9B,OAJMsc,GAASA,EAAKoE,SACnBpE,EAAO9qB,GAGD8qB,EAAKqE,iBAAkBnrB,IAG5BorB,GAAY,IAAI/lB,OAAQwY,GAAUtV,KAAM,KAAO,KAiGnD,SAAS8iB,GAAQrrB,EAAM7F,EAAMmxB,GAC5B,IAAIC,EAAOC,EAAUC,EAAU/rB,EAM9Bwe,EAAQle,EAAKke,MAqCd,OAnCAoN,EAAWA,GAAYL,GAAWjrB,MAQpB,MAFbN,EAAM4rB,EAASI,iBAAkBvxB,IAAUmxB,EAAUnxB,KAEjC2jB,GAAY9d,KAC/BN,EAAMd,EAAOsf,MAAOle,EAAM7F,KAQrBgD,EAAQwuB,kBAAoBX,GAAU3iB,KAAM3I,IAAS0rB,GAAU/iB,KAAMlO,KAG1EoxB,EAAQrN,EAAMqN,MACdC,EAAWtN,EAAMsN,SACjBC,EAAWvN,EAAMuN,SAGjBvN,EAAMsN,SAAWtN,EAAMuN,SAAWvN,EAAMqN,MAAQ7rB,EAChDA,EAAM4rB,EAASC,MAGfrN,EAAMqN,MAAQA,EACdrN,EAAMsN,SAAWA,EACjBtN,EAAMuN,SAAWA,SAIJpqB,IAAR3B,EAINA,EAAM,GACNA,EAIF,SAASksB,GAAcC,EAAaC,GAGnC,MAAO,CACNrxB,IAAK,WACJ,IAAKoxB,IASL,OAAShwB,KAAKpB,IAAMqxB,GAAS7rB,MAAOpE,KAAMqE,kBALlCrE,KAAKpB,OA3JhB,WAIC,SAASsxB,IAGR,GAAMlL,EAAN,CAIAmL,EAAU9N,MAAM+N,QAAU,+EAE1BpL,EAAI3C,MAAM+N,QACT,4HAGD5hB,GAAgB7L,YAAawtB,GAAYxtB,YAAaqiB,GAEtD,IAAIqL,EAAWlwB,EAAOmvB,iBAAkBtK,GACxCsL,EAAoC,OAAjBD,EAASzhB,IAG5B2hB,EAAsE,KAA9CC,EAAoBH,EAASI,YAIrDzL,EAAI3C,MAAMqO,MAAQ,MAClBC,EAA6D,KAAzCH,EAAoBH,EAASK,OAIjDE,EAAgE,KAAzCJ,EAAoBH,EAASX,OAMpD1K,EAAI3C,MAAMwO,SAAW,WACrBC,EAAiE,KAA9CN,EAAoBxL,EAAI+L,YAAc,GAEzDviB,GAAgB3L,YAAastB,GAI7BnL,EAAM,MAGP,SAASwL,EAAoBQ,GAC5B,OAAOtrB,KAAKurB,MAAOC,WAAYF,IAGhC,IAAIV,EAAkBM,EAAsBE,EAAkBH,EAC7DJ,EACAJ,EAAY7vB,EAASgC,cAAe,OACpC0iB,EAAM1kB,EAASgC,cAAe,OAGzB0iB,EAAI3C,QAMV2C,EAAI3C,MAAM8O,eAAiB,cAC3BnM,EAAIa,WAAW,GAAOxD,MAAM8O,eAAiB,GAC7C7vB,EAAQ8vB,gBAA+C,gBAA7BpM,EAAI3C,MAAM8O,eAEpCpuB,EAAO+B,OAAQxD,EAAS,CACvB+vB,kBAAmB,WAElB,OADAnB,IACOU,GAERd,eAAgB,WAEf,OADAI,IACOS,GAERW,cAAe,WAEd,OADApB,IACOI,GAERiB,mBAAoB,WAEnB,OADArB,IACOK,GAERiB,cAAe,WAEd,OADAtB,IACOY,MAvFV,GAsKA,IAAIW,GAAc,CAAE,SAAU,MAAO,MACpCC,GAAapxB,EAASgC,cAAe,OAAQ+f,MAC7CsP,GAAc,GAkBf,SAASC,GAAetzB,GACvB,IAAIuzB,EAAQ9uB,EAAO+uB,SAAUxzB,IAAUqzB,GAAarzB,GAEpD,OAAKuzB,IAGAvzB,KAAQozB,GACLpzB,EAEDqzB,GAAarzB,GAxBrB,SAAyBA,GAMxB,IAHA,IAAIyzB,EAAUzzB,EAAM,GAAIuhB,cAAgBvhB,EAAKuC,MAAO,GACnD9C,EAAI0zB,GAAYnuB,OAETvF,KAEP,IADAO,EAAOmzB,GAAa1zB,GAAMg0B,KACbL,GACZ,OAAOpzB,EAeoB0zB,CAAgB1zB,IAAUA,GAIxD,IAKC2zB,GAAe,4BACfC,GAAc,MACdC,GAAU,CAAEtB,SAAU,WAAYuB,WAAY,SAAU9P,QAAS,SACjE+P,GAAqB,CACpBC,cAAe,IACfC,WAAY,OAGd,SAASC,GAAmBruB,EAAMnF,EAAOyzB,GAIxC,IAAI7rB,EAAUmb,GAAQ7V,KAAMlN,GAC5B,OAAO4H,EAGNlB,KAAKgtB,IAAK,EAAG9rB,EAAS,IAAQ6rB,GAAY,KAAU7rB,EAAS,IAAO,MACpE5H,EAGF,SAAS2zB,GAAoBxuB,EAAMyuB,EAAWC,EAAKC,EAAaC,EAAQC,GACvE,IAAIj1B,EAAkB,UAAd60B,EAAwB,EAAI,EACnCK,EAAQ,EACRC,EAAQ,EAGT,GAAKL,KAAUC,EAAc,SAAW,WACvC,OAAO,EAGR,KAAQ/0B,EAAI,EAAGA,GAAK,EAGN,WAAR80B,IACJK,GAASnwB,EAAOwf,IAAKpe,EAAM0uB,EAAM7Q,GAAWjkB,IAAK,EAAMg1B,IAIlDD,GAmBQ,YAARD,IACJK,GAASnwB,EAAOwf,IAAKpe,EAAM,UAAY6d,GAAWjkB,IAAK,EAAMg1B,IAIjD,WAARF,IACJK,GAASnwB,EAAOwf,IAAKpe,EAAM,SAAW6d,GAAWjkB,GAAM,SAAS,EAAMg1B,MAtBvEG,GAASnwB,EAAOwf,IAAKpe,EAAM,UAAY6d,GAAWjkB,IAAK,EAAMg1B,GAGhD,YAARF,EACJK,GAASnwB,EAAOwf,IAAKpe,EAAM,SAAW6d,GAAWjkB,GAAM,SAAS,EAAMg1B,GAItEE,GAASlwB,EAAOwf,IAAKpe,EAAM,SAAW6d,GAAWjkB,GAAM,SAAS,EAAMg1B,IAoCzE,OAhBMD,GAAeE,GAAe,IAInCE,GAASxtB,KAAKgtB,IAAK,EAAGhtB,KAAKytB,KAC1BhvB,EAAM,SAAWyuB,EAAW,GAAI/S,cAAgB+S,EAAU/xB,MAAO,IACjEmyB,EACAE,EACAD,EACA,MAIM,GAGDC,EAGR,SAASE,GAAkBjvB,EAAMyuB,EAAWK,GAG3C,IAAIF,EAAS3D,GAAWjrB,GAKvB2uB,IADmBxxB,EAAQ+vB,qBAAuB4B,IAEE,eAAnDlwB,EAAOwf,IAAKpe,EAAM,aAAa,EAAO4uB,GACvCM,EAAmBP,EAEnB1wB,EAAMotB,GAAQrrB,EAAMyuB,EAAWG,GAC/BO,EAAa,SAAWV,EAAW,GAAI/S,cAAgB+S,EAAU/xB,MAAO,GAIzE,GAAKsuB,GAAU3iB,KAAMpK,GAAQ,CAC5B,IAAM6wB,EACL,OAAO7wB,EAERA,EAAM,OAgCP,QApBQd,EAAQ+vB,qBAAuByB,GAC9B,SAAR1wB,IACC8uB,WAAY9uB,IAA0D,WAAjDW,EAAOwf,IAAKpe,EAAM,WAAW,EAAO4uB,KAC1D5uB,EAAKovB,iBAAiBjwB,SAEtBwvB,EAAiE,eAAnD/vB,EAAOwf,IAAKpe,EAAM,aAAa,EAAO4uB,IAKpDM,EAAmBC,KAAcnvB,KAEhC/B,EAAM+B,EAAMmvB,MAKdlxB,EAAM8uB,WAAY9uB,IAAS,GAI1BuwB,GACCxuB,EACAyuB,EACAK,IAAWH,EAAc,SAAW,WACpCO,EACAN,EAGA3wB,GAEE,KA+SL,SAASoxB,GAAOrvB,EAAMY,EAASwb,EAAM5b,EAAK8uB,GACzC,OAAO,IAAID,GAAM7zB,UAAUwD,KAAMgB,EAAMY,EAASwb,EAAM5b,EAAK8uB,GA7S5D1wB,EAAO+B,OAAQ,CAId4uB,SAAU,CACTC,QAAS,CACR/0B,IAAK,SAAUuF,EAAMsrB,GACpB,GAAKA,EAAW,CAGf,IAAI5rB,EAAM2rB,GAAQrrB,EAAM,WACxB,MAAe,KAARN,EAAa,IAAMA,MAO9Bsf,UAAW,CACV,yBAA2B,EAC3B,aAAe,EACf,aAAe,EACf,UAAY,EACZ,YAAc,EACd,YAAc,EACd,UAAY,EACZ,YAAc,EACd,eAAiB,EACjB,iBAAmB,EACnB,SAAW,EACX,YAAc,EACd,cAAgB,EAChB,YAAc,EACd,SAAW,EACX,OAAS,EACT,SAAW,EACX,QAAU,EACV,QAAU,EACV,MAAQ,GAKT2O,SAAU,GAGVzP,MAAO,SAAUle,EAAM7F,EAAMU,EAAOi0B,GAGnC,GAAM9uB,GAA0B,IAAlBA,EAAK1C,UAAoC,IAAlB0C,EAAK1C,UAAmB0C,EAAKke,MAAlE,CAKA,IAAIxe,EAAKjC,EAAM0f,EACdsS,EAAW9T,EAAWxhB,GACtBu1B,EAAe3B,GAAY1lB,KAAMlO,GACjC+jB,EAAQle,EAAKke,MAad,GARMwR,IACLv1B,EAAOszB,GAAegC,IAIvBtS,EAAQve,EAAO2wB,SAAUp1B,IAAUyE,EAAO2wB,SAAUE,QAGrCpuB,IAAVxG,EA0CJ,OAAKsiB,GAAS,QAASA,QACwB9b,KAA5C3B,EAAMyd,EAAM1iB,IAAKuF,GAAM,EAAO8uB,IAEzBpvB,EAIDwe,EAAO/jB,GA7CA,YAHdsD,SAAc5C,KAGc6E,EAAMke,GAAQ7V,KAAMlN,KAAa6E,EAAK,KACjE7E,EAAQ0jB,GAAWve,EAAM7F,EAAMuF,GAG/BjC,EAAO,UAIM,MAAT5C,GAAiBA,GAAUA,IAOlB,WAAT4C,GAAsBiyB,IAC1B70B,GAAS6E,GAAOA,EAAK,KAASd,EAAOogB,UAAWyQ,GAAa,GAAK,OAI7DtyB,EAAQ8vB,iBAA6B,KAAVpyB,GAAiD,IAAjCV,EAAK0C,QAAS,gBAC9DqhB,EAAO/jB,GAAS,WAIXgjB,GAAY,QAASA,QACsB9b,KAA9CxG,EAAQsiB,EAAMjB,IAAKlc,EAAMnF,EAAOi0B,MAE7BY,EACJxR,EAAMyR,YAAax1B,EAAMU,GAEzBqjB,EAAO/jB,GAASU,MAkBpBujB,IAAK,SAAUpe,EAAM7F,EAAM20B,EAAOF,GACjC,IAAI3wB,EAAKsB,EAAK4d,EACbsS,EAAW9T,EAAWxhB,GA6BvB,OA5BgB4zB,GAAY1lB,KAAMlO,KAMjCA,EAAOszB,GAAegC,KAIvBtS,EAAQve,EAAO2wB,SAAUp1B,IAAUyE,EAAO2wB,SAAUE,KAGtC,QAAStS,IACtBlf,EAAMkf,EAAM1iB,IAAKuF,GAAM,EAAM8uB,SAIjBztB,IAARpD,IACJA,EAAMotB,GAAQrrB,EAAM7F,EAAMy0B,IAId,WAAR3wB,GAAoB9D,KAAQ+zB,KAChCjwB,EAAMiwB,GAAoB/zB,IAIZ,KAAV20B,GAAgBA,GACpBvvB,EAAMwtB,WAAY9uB,IACD,IAAV6wB,GAAkBc,SAAUrwB,GAAQA,GAAO,EAAItB,GAGhDA,KAITW,EAAOiB,KAAM,CAAE,SAAU,UAAW,SAAUjG,EAAG60B,GAChD7vB,EAAO2wB,SAAUd,GAAc,CAC9Bh0B,IAAK,SAAUuF,EAAMsrB,EAAUwD,GAC9B,GAAKxD,EAIJ,OAAOwC,GAAazlB,KAAMzJ,EAAOwf,IAAKpe,EAAM,aAQxCA,EAAKovB,iBAAiBjwB,QAAWa,EAAK6vB,wBAAwBtE,MAIhE0D,GAAkBjvB,EAAMyuB,EAAWK,GAHnCzQ,GAAMre,EAAMguB,IAAS,WACpB,OAAOiB,GAAkBjvB,EAAMyuB,EAAWK,OAM/C5S,IAAK,SAAUlc,EAAMnF,EAAOi0B,GAC3B,IAAIrsB,EACHmsB,EAAS3D,GAAWjrB,GAIpB8vB,GAAsB3yB,EAAQkwB,iBACT,aAApBuB,EAAOlC,SAIRiC,GADkBmB,GAAsBhB,IAEY,eAAnDlwB,EAAOwf,IAAKpe,EAAM,aAAa,EAAO4uB,GACvCN,EAAWQ,EACVN,GACCxuB,EACAyuB,EACAK,EACAH,EACAC,GAED,EAqBF,OAjBKD,GAAemB,IACnBxB,GAAY/sB,KAAKytB,KAChBhvB,EAAM,SAAWyuB,EAAW,GAAI/S,cAAgB+S,EAAU/xB,MAAO,IACjEqwB,WAAY6B,EAAQH,IACpBD,GAAoBxuB,EAAMyuB,EAAW,UAAU,EAAOG,GACtD,KAKGN,IAAc7rB,EAAUmb,GAAQ7V,KAAMlN,KACb,QAA3B4H,EAAS,IAAO,QAElBzC,EAAKke,MAAOuQ,GAAc5zB,EAC1BA,EAAQ+D,EAAOwf,IAAKpe,EAAMyuB,IAGpBJ,GAAmBruB,EAAMnF,EAAOyzB,QAK1C1vB,EAAO2wB,SAASjD,WAAaV,GAAczuB,EAAQiwB,oBAClD,SAAUptB,EAAMsrB,GACf,GAAKA,EACJ,OAASyB,WAAY1B,GAAQrrB,EAAM,gBAClCA,EAAK6vB,wBAAwBE,KAC5B1R,GAAMre,EAAM,CAAEssB,WAAY,IAAK,WAC9B,OAAOtsB,EAAK6vB,wBAAwBE,SAElC,QAMRnxB,EAAOiB,KAAM,CACZmwB,OAAQ,GACRC,QAAS,GACTC,OAAQ,UACN,SAAUC,EAAQC,GACpBxxB,EAAO2wB,SAAUY,EAASC,GAAW,CACpCC,OAAQ,SAAUx1B,GAOjB,IANA,IAAIjB,EAAI,EACP02B,EAAW,GAGXC,EAAyB,iBAAV11B,EAAqBA,EAAMiI,MAAO,KAAQ,CAAEjI,GAEpDjB,EAAI,EAAGA,IACd02B,EAAUH,EAAStS,GAAWjkB,GAAMw2B,GACnCG,EAAO32B,IAAO22B,EAAO32B,EAAI,IAAO22B,EAAO,GAGzC,OAAOD,IAIO,WAAXH,IACJvxB,EAAO2wB,SAAUY,EAASC,GAASlU,IAAMmS,OAI3CzvB,EAAOG,GAAG4B,OAAQ,CACjByd,IAAK,SAAUjkB,EAAMU,GACpB,OAAOmgB,EAAQnf,MAAM,SAAUmE,EAAM7F,EAAMU,GAC1C,IAAI+zB,EAAQtuB,EACXP,EAAM,GACNnG,EAAI,EAEL,GAAKuH,MAAMC,QAASjH,GAAS,CAI5B,IAHAy0B,EAAS3D,GAAWjrB,GACpBM,EAAMnG,EAAKgF,OAEHvF,EAAI0G,EAAK1G,IAChBmG,EAAK5F,EAAMP,IAAQgF,EAAOwf,IAAKpe,EAAM7F,EAAMP,IAAK,EAAOg1B,GAGxD,OAAO7uB,EAGR,YAAiBsB,IAAVxG,EACN+D,EAAOsf,MAAOle,EAAM7F,EAAMU,GAC1B+D,EAAOwf,IAAKpe,EAAM7F,KACjBA,EAAMU,EAAOqF,UAAUf,OAAS,MAQrCP,EAAOywB,MAAQA,GAEfA,GAAM7zB,UAAY,CACjB6D,YAAagwB,GACbrwB,KAAM,SAAUgB,EAAMY,EAASwb,EAAM5b,EAAK8uB,EAAQvQ,GACjDljB,KAAKmE,KAAOA,EACZnE,KAAKugB,KAAOA,EACZvgB,KAAKyzB,OAASA,GAAU1wB,EAAO0wB,OAAOnP,SACtCtkB,KAAK+E,QAAUA,EACf/E,KAAK0S,MAAQ1S,KAAKoqB,IAAMpqB,KAAK2N,MAC7B3N,KAAK2E,IAAMA,EACX3E,KAAKkjB,KAAOA,IAAUngB,EAAOogB,UAAW5C,GAAS,GAAK,OAEvD5S,IAAK,WACJ,IAAI2T,EAAQkS,GAAMmB,UAAW30B,KAAKugB,MAElC,OAAOe,GAASA,EAAM1iB,IACrB0iB,EAAM1iB,IAAKoB,MACXwzB,GAAMmB,UAAUrQ,SAAS1lB,IAAKoB,OAEhC40B,IAAK,SAAUC,GACd,IAAIC,EACHxT,EAAQkS,GAAMmB,UAAW30B,KAAKugB,MAoB/B,OAlBKvgB,KAAK+E,QAAQgwB,SACjB/0B,KAAKg1B,IAAMF,EAAQ/xB,EAAO0wB,OAAQzzB,KAAKyzB,QACtCoB,EAAS70B,KAAK+E,QAAQgwB,SAAWF,EAAS,EAAG,EAAG70B,KAAK+E,QAAQgwB,UAG9D/0B,KAAKg1B,IAAMF,EAAQD,EAEpB70B,KAAKoqB,KAAQpqB,KAAK2E,IAAM3E,KAAK0S,OAAUoiB,EAAQ90B,KAAK0S,MAE/C1S,KAAK+E,QAAQkwB,MACjBj1B,KAAK+E,QAAQkwB,KAAK/2B,KAAM8B,KAAKmE,KAAMnE,KAAKoqB,IAAKpqB,MAGzCshB,GAASA,EAAMjB,IACnBiB,EAAMjB,IAAKrgB,MAEXwzB,GAAMmB,UAAUrQ,SAASjE,IAAKrgB,MAExBA,OAITwzB,GAAM7zB,UAAUwD,KAAKxD,UAAY6zB,GAAM7zB,UAEvC6zB,GAAMmB,UAAY,CACjBrQ,SAAU,CACT1lB,IAAK,SAAUgkB,GACd,IAAI3Q,EAIJ,OAA6B,IAAxB2Q,EAAMze,KAAK1C,UACa,MAA5BmhB,EAAMze,KAAMye,EAAMrC,OAAoD,MAAlCqC,EAAMze,KAAKke,MAAOO,EAAMrC,MACrDqC,EAAMze,KAAMye,EAAMrC,OAO1BtO,EAASlP,EAAOwf,IAAKK,EAAMze,KAAMye,EAAMrC,KAAM,MAGhB,SAAXtO,EAAwBA,EAAJ,GAEvCoO,IAAK,SAAUuC,GAKT7f,EAAOmyB,GAAGD,KAAMrS,EAAMrC,MAC1Bxd,EAAOmyB,GAAGD,KAAMrS,EAAMrC,MAAQqC,GACK,IAAxBA,EAAMze,KAAK1C,WACrBsB,EAAO2wB,SAAU9Q,EAAMrC,OAC4B,MAAnDqC,EAAMze,KAAKke,MAAOuP,GAAehP,EAAMrC,OAGxCqC,EAAMze,KAAMye,EAAMrC,MAASqC,EAAMwH,IAFjCrnB,EAAOsf,MAAOO,EAAMze,KAAMye,EAAMrC,KAAMqC,EAAMwH,IAAMxH,EAAMM,SAU5DsQ,GAAMmB,UAAUQ,UAAY3B,GAAMmB,UAAUS,WAAa,CACxD/U,IAAK,SAAUuC,GACTA,EAAMze,KAAK1C,UAAYmhB,EAAMze,KAAKvB,aACtCggB,EAAMze,KAAMye,EAAMrC,MAASqC,EAAMwH,OAKpCrnB,EAAO0wB,OAAS,CACf4B,OAAQ,SAAUx1B,GACjB,OAAOA,GAERy1B,MAAO,SAAUz1B,GAChB,MAAO,GAAM6F,KAAK6vB,IAAK11B,EAAI6F,KAAK8vB,IAAO,GAExClR,SAAU,SAGXvhB,EAAOmyB,GAAK1B,GAAM7zB,UAAUwD,KAG5BJ,EAAOmyB,GAAGD,KAAO,GAKjB,IACCQ,GAAOC,GACPC,GAAW,yBACXC,GAAO,cAER,SAASC,KACHH,MACqB,IAApBp1B,EAASw1B,QAAoB31B,EAAO41B,sBACxC51B,EAAO41B,sBAAuBF,IAE9B11B,EAAOyd,WAAYiY,GAAU9yB,EAAOmyB,GAAGc,UAGxCjzB,EAAOmyB,GAAGe,QAKZ,SAASC,KAIR,OAHA/1B,EAAOyd,YAAY,WAClB6X,QAAQjwB,KAEAiwB,GAAQttB,KAAKiiB,MAIvB,SAAS+L,GAAOv0B,EAAMw0B,GACrB,IAAIpK,EACHjuB,EAAI,EACJwP,EAAQ,CAAE8oB,OAAQz0B,GAKnB,IADAw0B,EAAeA,EAAe,EAAI,EAC1Br4B,EAAI,EAAGA,GAAK,EAAIq4B,EAEvB7oB,EAAO,UADPye,EAAQhK,GAAWjkB,KACSwP,EAAO,UAAYye,GAAUpqB,EAO1D,OAJKw0B,IACJ7oB,EAAMomB,QAAUpmB,EAAMmiB,MAAQ9tB,GAGxB2L,EAGR,SAAS+oB,GAAat3B,EAAOuhB,EAAMgW,GAKlC,IAJA,IAAI3T,EACH4K,GAAegJ,GAAUC,SAAUlW,IAAU,IAAKzf,OAAQ01B,GAAUC,SAAU,MAC9Epd,EAAQ,EACR/V,EAASkqB,EAAWlqB,OACb+V,EAAQ/V,EAAQ+V,IACvB,GAAOuJ,EAAQ4K,EAAYnU,GAAQnb,KAAMq4B,EAAWhW,EAAMvhB,GAGzD,OAAO4jB,EAsNV,SAAS4T,GAAWryB,EAAMuyB,EAAY3xB,GACrC,IAAIkN,EACH0kB,EACAtd,EAAQ,EACR/V,EAASkzB,GAAUI,WAAWtzB,OAC9B8Y,EAAWrZ,EAAOgZ,WAAWI,QAAQ,kBAG7B8Z,EAAK9xB,QAEb8xB,EAAO,WACN,GAAKU,EACJ,OAAO,EAYR,IAVA,IAAIE,EAAcpB,IAASS,KAC1BlY,EAAYtY,KAAKgtB,IAAK,EAAG6D,EAAUO,UAAYP,EAAUxB,SAAW8B,GAKpEhC,EAAU,GADH7W,EAAYuY,EAAUxB,UAAY,GAEzC1b,EAAQ,EACR/V,EAASizB,EAAUQ,OAAOzzB,OAEnB+V,EAAQ/V,EAAQ+V,IACvBkd,EAAUQ,OAAQ1d,GAAQub,IAAKC,GAMhC,OAHAzY,EAASiB,WAAYlZ,EAAM,CAAEoyB,EAAW1B,EAAS7W,IAG5C6W,EAAU,GAAKvxB,EACZ0a,GAIF1a,GACL8Y,EAASiB,WAAYlZ,EAAM,CAAEoyB,EAAW,EAAG,IAI5Cna,EAASkB,YAAanZ,EAAM,CAAEoyB,KACvB,IAERA,EAAYna,EAASzB,QAAS,CAC7BxW,KAAMA,EACN4lB,MAAOhnB,EAAO+B,OAAQ,GAAI4xB,GAC1BM,KAAMj0B,EAAO+B,QAAQ,EAAM,CAC1BmyB,cAAe,GACfxD,OAAQ1wB,EAAO0wB,OAAOnP,UACpBvf,GACHmyB,mBAAoBR,EACpBS,gBAAiBpyB,EACjB+xB,UAAWrB,IAASS,KACpBnB,SAAUhwB,EAAQgwB,SAClBgC,OAAQ,GACRT,YAAa,SAAU/V,EAAM5b,GAC5B,IAAIie,EAAQ7f,EAAOywB,MAAOrvB,EAAMoyB,EAAUS,KAAMzW,EAAM5b,EACpD4xB,EAAUS,KAAKC,cAAe1W,IAAUgW,EAAUS,KAAKvD,QAEzD,OADA8C,EAAUQ,OAAOh2B,KAAM6hB,GAChBA,GAERpB,KAAM,SAAU4V,GACf,IAAI/d,EAAQ,EAIX/V,EAAS8zB,EAAUb,EAAUQ,OAAOzzB,OAAS,EAC9C,GAAKqzB,EACJ,OAAO32B,KAGR,IADA22B,GAAU,EACFtd,EAAQ/V,EAAQ+V,IACvBkd,EAAUQ,OAAQ1d,GAAQub,IAAK,GAUhC,OANKwC,GACJhb,EAASiB,WAAYlZ,EAAM,CAAEoyB,EAAW,EAAG,IAC3Cna,EAASkB,YAAanZ,EAAM,CAAEoyB,EAAWa,KAEzChb,EAASsB,WAAYvZ,EAAM,CAAEoyB,EAAWa,IAElCp3B,QAGT+pB,EAAQwM,EAAUxM,MAInB,KA/HD,SAAqBA,EAAOkN,GAC3B,IAAI5d,EAAO/a,EAAMm1B,EAAQz0B,EAAOsiB,EAGhC,IAAMjI,KAAS0Q,EAed,GAbA0J,EAASwD,EADT34B,EAAOwhB,EAAWzG,IAElBra,EAAQ+qB,EAAO1Q,GACV/T,MAAMC,QAASvG,KACnBy0B,EAASz0B,EAAO,GAChBA,EAAQ+qB,EAAO1Q,GAAUra,EAAO,IAG5Bqa,IAAU/a,IACdyrB,EAAOzrB,GAASU,SACT+qB,EAAO1Q,KAGfiI,EAAQve,EAAO2wB,SAAUp1B,KACX,WAAYgjB,EAMzB,IAAMjI,KALNra,EAAQsiB,EAAMkT,OAAQx1B,UACf+qB,EAAOzrB,GAICU,EACNqa,KAAS0Q,IAChBA,EAAO1Q,GAAUra,EAAOqa,GACxB4d,EAAe5d,GAAUoa,QAI3BwD,EAAe34B,GAASm1B,EA6F1B4D,CAAYtN,EAAOwM,EAAUS,KAAKC,eAE1B5d,EAAQ/V,EAAQ+V,IAEvB,GADApH,EAASukB,GAAUI,WAAYvd,GAAQnb,KAAMq4B,EAAWpyB,EAAM4lB,EAAOwM,EAAUS,MAM9E,OAJKz1B,EAAY0Q,EAAOuP,QACvBze,EAAOwe,YAAagV,EAAUpyB,KAAMoyB,EAAUS,KAAK3b,OAAQmG,KAC1DvP,EAAOuP,KAAKjiB,KAAM0S,IAEbA,EAyBT,OArBAlP,EAAOmB,IAAK6lB,EAAOuM,GAAaC,GAE3Bh1B,EAAYg1B,EAAUS,KAAKtkB,QAC/B6jB,EAAUS,KAAKtkB,MAAMxU,KAAMiG,EAAMoyB,GAIlCA,EACE7Z,SAAU6Z,EAAUS,KAAKta,UACzBpU,KAAMiuB,EAAUS,KAAK1uB,KAAMiuB,EAAUS,KAAKM,UAC1C1c,KAAM2b,EAAUS,KAAKpc,MACrBuB,OAAQoa,EAAUS,KAAK7a,QAEzBpZ,EAAOmyB,GAAGqC,MACTx0B,EAAO+B,OAAQmxB,EAAM,CACpB9xB,KAAMA,EACNqzB,KAAMjB,EACNlb,MAAOkb,EAAUS,KAAK3b,SAIjBkb,EAGRxzB,EAAOyzB,UAAYzzB,EAAO+B,OAAQ0xB,GAAW,CAE5CC,SAAU,CACT,IAAK,CAAE,SAAUlW,EAAMvhB,GACtB,IAAI4jB,EAAQ5iB,KAAKs2B,YAAa/V,EAAMvhB,GAEpC,OADA0jB,GAAWE,EAAMze,KAAMoc,EAAMwB,GAAQ7V,KAAMlN,GAAS4jB,GAC7CA,KAIT6U,QAAS,SAAU1N,EAAO9lB,GACpB1C,EAAYwoB,IAChB9lB,EAAW8lB,EACXA,EAAQ,CAAE,MAEVA,EAAQA,EAAMle,MAAOoO,GAOtB,IAJA,IAAIsG,EACHlH,EAAQ,EACR/V,EAASymB,EAAMzmB,OAER+V,EAAQ/V,EAAQ+V,IACvBkH,EAAOwJ,EAAO1Q,GACdmd,GAAUC,SAAUlW,GAASiW,GAAUC,SAAUlW,IAAU,GAC3DiW,GAAUC,SAAUlW,GAAO9P,QAASxM,IAItC2yB,WAAY,CA3Wb,SAA2BzyB,EAAM4lB,EAAOiN,GACvC,IAAIzW,EAAMvhB,EAAO4kB,EAAQtC,EAAOoW,EAASC,EAAWC,EAAgBtV,EACnEuV,EAAQ,UAAW9N,GAAS,WAAYA,EACxCyN,EAAOx3B,KACPusB,EAAO,GACPlK,EAAQle,EAAKke,MACbyT,EAAS3xB,EAAK1C,UAAY2gB,GAAoBje,GAC9C2zB,EAAWrX,EAAS7hB,IAAKuF,EAAM,UA6BhC,IAAMoc,KA1BAyW,EAAK3b,QAEa,OADvBiG,EAAQve,EAAOwe,YAAapd,EAAM,OACvB4zB,WACVzW,EAAMyW,SAAW,EACjBL,EAAUpW,EAAM3F,MAAMJ,KACtB+F,EAAM3F,MAAMJ,KAAO,WACZ+F,EAAMyW,UACXL,MAIHpW,EAAMyW,WAENP,EAAKrb,QAAQ,WAGZqb,EAAKrb,QAAQ,WACZmF,EAAMyW,WACAh1B,EAAOsY,MAAOlX,EAAM,MAAOb,QAChCge,EAAM3F,MAAMJ,cAOFwO,EAEb,GADA/qB,EAAQ+qB,EAAOxJ,GACVoV,GAASnpB,KAAMxN,GAAU,CAG7B,UAFO+qB,EAAOxJ,GACdqD,EAASA,GAAoB,WAAV5kB,EACdA,KAAY82B,EAAS,OAAS,QAAW,CAI7C,GAAe,SAAV92B,IAAoB84B,QAAiCtyB,IAArBsyB,EAAUvX,GAK9C,SAJAuV,GAAS,EAOXvJ,EAAMhM,GAASuX,GAAYA,EAAUvX,IAAUxd,EAAOsf,MAAOle,EAAMoc,GAMrE,IADAoX,GAAa50B,EAAOoD,cAAe4jB,MAChBhnB,EAAOoD,cAAeomB,GA8DzC,IAAMhM,KAzDDsX,GAA2B,IAAlB1zB,EAAK1C,WAMlBu1B,EAAKgB,SAAW,CAAE3V,EAAM2V,SAAU3V,EAAM4V,UAAW5V,EAAM6V,WAIlC,OADvBN,EAAiBE,GAAYA,EAASxV,WAErCsV,EAAiBnX,EAAS7hB,IAAKuF,EAAM,YAGrB,UADjBme,EAAUvf,EAAOwf,IAAKpe,EAAM,cAEtByzB,EACJtV,EAAUsV,GAIVpU,GAAU,CAAErf,IAAQ,GACpByzB,EAAiBzzB,EAAKke,MAAMC,SAAWsV,EACvCtV,EAAUvf,EAAOwf,IAAKpe,EAAM,WAC5Bqf,GAAU,CAAErf,OAKG,WAAZme,GAAoC,iBAAZA,GAAgD,MAAlBsV,IACrB,SAAhC70B,EAAOwf,IAAKpe,EAAM,WAGhBwzB,IACLH,EAAKlvB,MAAM,WACV+Z,EAAMC,QAAUsV,KAEM,MAAlBA,IACJtV,EAAUD,EAAMC,QAChBsV,EAA6B,SAAZtV,EAAqB,GAAKA,IAG7CD,EAAMC,QAAU,iBAKd0U,EAAKgB,WACT3V,EAAM2V,SAAW,SACjBR,EAAKrb,QAAQ,WACZkG,EAAM2V,SAAWhB,EAAKgB,SAAU,GAChC3V,EAAM4V,UAAYjB,EAAKgB,SAAU,GACjC3V,EAAM6V,UAAYlB,EAAKgB,SAAU,OAKnCL,GAAY,EACEpL,EAGPoL,IACAG,EACC,WAAYA,IAChBhC,EAASgC,EAAShC,QAGnBgC,EAAWrX,EAAStB,OAAQhb,EAAM,SAAU,CAAEme,QAASsV,IAInDhU,IACJkU,EAAShC,QAAUA,GAIfA,GACJtS,GAAU,CAAErf,IAAQ,GAKrBqzB,EAAKlvB,MAAM,WASV,IAAMiY,KAJAuV,GACLtS,GAAU,CAAErf,IAEbsc,EAAS/E,OAAQvX,EAAM,UACTooB,EACbxpB,EAAOsf,MAAOle,EAAMoc,EAAMgM,EAAMhM,QAMnCoX,EAAYrB,GAAaR,EAASgC,EAAUvX,GAAS,EAAGA,EAAMiX,GACtDjX,KAAQuX,IACfA,EAAUvX,GAASoX,EAAUjlB,MACxBojB,IACJ6B,EAAUhzB,IAAMgzB,EAAUjlB,MAC1BilB,EAAUjlB,MAAQ,MAuMrBylB,UAAW,SAAUl0B,EAAUsqB,GACzBA,EACJiI,GAAUI,WAAWnmB,QAASxM,GAE9BuyB,GAAUI,WAAW71B,KAAMkD,MAK9BlB,EAAOq1B,MAAQ,SAAUA,EAAO3E,EAAQvwB,GACvC,IAAIm1B,EAAMD,GAA0B,iBAAVA,EAAqBr1B,EAAO+B,OAAQ,GAAIszB,GAAU,CAC3Ed,SAAUp0B,IAAOA,GAAMuwB,GACtBlyB,EAAY62B,IAAWA,EACxBrD,SAAUqD,EACV3E,OAAQvwB,GAAMuwB,GAAUA,IAAWlyB,EAAYkyB,IAAYA,GAoC5D,OAhCK1wB,EAAOmyB,GAAGtO,IACdyR,EAAItD,SAAW,EAGc,iBAAjBsD,EAAItD,WACVsD,EAAItD,YAAYhyB,EAAOmyB,GAAGoD,OAC9BD,EAAItD,SAAWhyB,EAAOmyB,GAAGoD,OAAQD,EAAItD,UAGrCsD,EAAItD,SAAWhyB,EAAOmyB,GAAGoD,OAAOhU,UAMjB,MAAb+T,EAAIhd,QAA+B,IAAdgd,EAAIhd,QAC7Bgd,EAAIhd,MAAQ,MAIbgd,EAAI5V,IAAM4V,EAAIf,SAEde,EAAIf,SAAW,WACT/1B,EAAY82B,EAAI5V,MACpB4V,EAAI5V,IAAIvkB,KAAM8B,MAGVq4B,EAAIhd,OACRtY,EAAOqe,QAASphB,KAAMq4B,EAAIhd,QAIrBgd,GAGRt1B,EAAOG,GAAG4B,OAAQ,CACjByzB,OAAQ,SAAUH,EAAOI,EAAI/E,EAAQxvB,GAGpC,OAAOjE,KAAKmP,OAAQiT,IAAqBG,IAAK,UAAW,GAAIkB,OAG3D9e,MAAM8zB,QAAS,CAAE9E,QAAS6E,GAAMJ,EAAO3E,EAAQxvB,IAElDw0B,QAAS,SAAUlY,EAAM6X,EAAO3E,EAAQxvB,GACvC,IAAI0X,EAAQ5Y,EAAOoD,cAAeoa,GACjCmY,EAAS31B,EAAOq1B,MAAOA,EAAO3E,EAAQxvB,GACtC00B,EAAc,WAGb,IAAInB,EAAOhB,GAAWx2B,KAAM+C,EAAO+B,OAAQ,GAAIyb,GAAQmY,IAGlD/c,GAAS8E,EAAS7hB,IAAKoB,KAAM,YACjCw3B,EAAKhW,MAAM,IAKd,OAFCmX,EAAYC,OAASD,EAEfhd,IAA0B,IAAjB+c,EAAOrd,MACtBrb,KAAKgE,KAAM20B,GACX34B,KAAKqb,MAAOqd,EAAOrd,MAAOsd,IAE5BnX,KAAM,SAAU5f,EAAM8f,EAAY0V,GACjC,IAAIyB,EAAY,SAAUvX,GACzB,IAAIE,EAAOF,EAAME,YACVF,EAAME,KACbA,EAAM4V,IAYP,MATqB,iBAATx1B,IACXw1B,EAAU1V,EACVA,EAAa9f,EACbA,OAAO4D,GAEHkc,IAAuB,IAAT9f,GAClB5B,KAAKqb,MAAOzZ,GAAQ,KAAM,IAGpB5B,KAAKgE,MAAM,WACjB,IAAIod,GAAU,EACb/H,EAAgB,MAARzX,GAAgBA,EAAO,aAC/Bk3B,EAAS/1B,EAAO+1B,OAChBxY,EAAOG,EAAS7hB,IAAKoB,MAEtB,GAAKqZ,EACCiH,EAAMjH,IAAWiH,EAAMjH,GAAQmI,MACnCqX,EAAWvY,EAAMjH,SAGlB,IAAMA,KAASiH,EACTA,EAAMjH,IAAWiH,EAAMjH,GAAQmI,MAAQoU,GAAKppB,KAAM6M,IACtDwf,EAAWvY,EAAMjH,IAKpB,IAAMA,EAAQyf,EAAOx1B,OAAQ+V,KACvByf,EAAQzf,GAAQlV,OAASnE,MACnB,MAAR4B,GAAgBk3B,EAAQzf,GAAQgC,QAAUzZ,IAE5Ck3B,EAAQzf,GAAQme,KAAKhW,KAAM4V,GAC3BhW,GAAU,EACV0X,EAAOj0B,OAAQwU,EAAO,KAOnB+H,GAAYgW,GAChBr0B,EAAOqe,QAASphB,KAAM4B,OAIzBg3B,OAAQ,SAAUh3B,GAIjB,OAHc,IAATA,IACJA,EAAOA,GAAQ,MAET5B,KAAKgE,MAAM,WACjB,IAAIqV,EACHiH,EAAOG,EAAS7hB,IAAKoB,MACrBqb,EAAQiF,EAAM1e,EAAO,SACrB0f,EAAQhB,EAAM1e,EAAO,cACrBk3B,EAAS/1B,EAAO+1B,OAChBx1B,EAAS+X,EAAQA,EAAM/X,OAAS,EAajC,IAVAgd,EAAKsY,QAAS,EAGd71B,EAAOsY,MAAOrb,KAAM4B,EAAM,IAErB0f,GAASA,EAAME,MACnBF,EAAME,KAAKtjB,KAAM8B,MAAM,GAIlBqZ,EAAQyf,EAAOx1B,OAAQ+V,KACvByf,EAAQzf,GAAQlV,OAASnE,MAAQ84B,EAAQzf,GAAQgC,QAAUzZ,IAC/Dk3B,EAAQzf,GAAQme,KAAKhW,MAAM,GAC3BsX,EAAOj0B,OAAQwU,EAAO,IAKxB,IAAMA,EAAQ,EAAGA,EAAQ/V,EAAQ+V,IAC3BgC,EAAOhC,IAAWgC,EAAOhC,GAAQuf,QACrCvd,EAAOhC,GAAQuf,OAAO16B,KAAM8B,aAKvBsgB,EAAKsY,aAKf71B,EAAOiB,KAAM,CAAE,SAAU,OAAQ,SAAU,SAAUjG,EAAGO,GACvD,IAAIy6B,EAAQh2B,EAAOG,GAAI5E,GACvByE,EAAOG,GAAI5E,GAAS,SAAU85B,EAAO3E,EAAQxvB,GAC5C,OAAgB,MAATm0B,GAAkC,kBAAVA,EAC9BW,EAAM30B,MAAOpE,KAAMqE,WACnBrE,KAAKy4B,QAAStC,GAAO73B,GAAM,GAAQ85B,EAAO3E,EAAQxvB,OAKrDlB,EAAOiB,KAAM,CACZg1B,UAAW7C,GAAO,QAClB8C,QAAS9C,GAAO,QAChB+C,YAAa/C,GAAO,UACpBgD,OAAQ,CAAExF,QAAS,QACnByF,QAAS,CAAEzF,QAAS,QACpB0F,WAAY,CAAE1F,QAAS,YACrB,SAAUr1B,EAAMyrB,GAClBhnB,EAAOG,GAAI5E,GAAS,SAAU85B,EAAO3E,EAAQxvB,GAC5C,OAAOjE,KAAKy4B,QAAS1O,EAAOqO,EAAO3E,EAAQxvB,OAI7ClB,EAAO+1B,OAAS,GAChB/1B,EAAOmyB,GAAGe,KAAO,WAChB,IAAIsB,EACHx5B,EAAI,EACJ+6B,EAAS/1B,EAAO+1B,OAIjB,IAFArD,GAAQttB,KAAKiiB,MAELrsB,EAAI+6B,EAAOx1B,OAAQvF,KAC1Bw5B,EAAQuB,EAAQ/6B,OAGC+6B,EAAQ/6B,KAAQw5B,GAChCuB,EAAOj0B,OAAQ9G,IAAK,GAIhB+6B,EAAOx1B,QACZP,EAAOmyB,GAAG1T,OAEXiU,QAAQjwB,GAGTzC,EAAOmyB,GAAGqC,MAAQ,SAAUA,GAC3Bx0B,EAAO+1B,OAAO/3B,KAAMw2B,GACpBx0B,EAAOmyB,GAAGxiB,SAGX3P,EAAOmyB,GAAGc,SAAW,GACrBjzB,EAAOmyB,GAAGxiB,MAAQ,WACZgjB,KAILA,IAAa,EACbG,OAGD9yB,EAAOmyB,GAAG1T,KAAO,WAChBkU,GAAa,MAGd3yB,EAAOmyB,GAAGoD,OAAS,CAClBgB,KAAM,IACNC,KAAM,IAGNjV,SAAU,KAMXvhB,EAAOG,GAAGs2B,MAAQ,SAAUC,EAAM73B,GAIjC,OAHA63B,EAAO12B,EAAOmyB,IAAKnyB,EAAOmyB,GAAGoD,OAAQmB,IAAiBA,EACtD73B,EAAOA,GAAQ,KAER5B,KAAKqb,MAAOzZ,GAAM,SAAU4J,EAAM8V,GACxC,IAAIoY,EAAUv5B,EAAOyd,WAAYpS,EAAMiuB,GACvCnY,EAAME,KAAO,WACZrhB,EAAOw5B,aAAcD,QAMxB,WACC,IAAIhqB,EAAQpP,EAASgC,cAAe,SAEnC+1B,EADS/3B,EAASgC,cAAe,UACpBK,YAAarC,EAASgC,cAAe,WAEnDoN,EAAM9N,KAAO,WAIbN,EAAQs4B,QAA0B,KAAhBlqB,EAAM1Q,MAIxBsC,EAAQu4B,YAAcxB,EAAItkB,UAI1BrE,EAAQpP,EAASgC,cAAe,UAC1BtD,MAAQ,IACd0Q,EAAM9N,KAAO,QACbN,EAAQw4B,WAA6B,MAAhBpqB,EAAM1Q,MApB5B,GAwBA,IAAI+6B,GACHtsB,GAAa1K,EAAO2N,KAAKjD,WAE1B1K,EAAOG,GAAG4B,OAAQ,CACjB8L,KAAM,SAAUtS,EAAMU,GACrB,OAAOmgB,EAAQnf,KAAM+C,EAAO6N,KAAMtS,EAAMU,EAAOqF,UAAUf,OAAS,IAGnE02B,WAAY,SAAU17B,GACrB,OAAO0B,KAAKgE,MAAM,WACjBjB,EAAOi3B,WAAYh6B,KAAM1B,SAK5ByE,EAAO+B,OAAQ,CACd8L,KAAM,SAAUzM,EAAM7F,EAAMU,GAC3B,IAAI6E,EAAKyd,EACR2Y,EAAQ91B,EAAK1C,SAGd,GAAe,IAAVw4B,GAAyB,IAAVA,GAAyB,IAAVA,EAKnC,YAAkC,IAAtB91B,EAAK3B,aACTO,EAAOwd,KAAMpc,EAAM7F,EAAMU,IAKlB,IAAVi7B,GAAgBl3B,EAAO4U,SAAUxT,KACrCmd,EAAQve,EAAOm3B,UAAW57B,EAAK4I,iBAC5BnE,EAAO2N,KAAK7E,MAAMsuB,KAAK3tB,KAAMlO,GAASy7B,QAAWv0B,SAGtCA,IAAVxG,EACW,OAAVA,OACJ+D,EAAOi3B,WAAY71B,EAAM7F,GAIrBgjB,GAAS,QAASA,QACuB9b,KAA3C3B,EAAMyd,EAAMjB,IAAKlc,EAAMnF,EAAOV,IACzBuF,GAGRM,EAAK1B,aAAcnE,EAAMU,EAAQ,IAC1BA,GAGHsiB,GAAS,QAASA,GAA+C,QAApCzd,EAAMyd,EAAM1iB,IAAKuF,EAAM7F,IACjDuF,EAMM,OAHdA,EAAMd,EAAOsM,KAAKuB,KAAMzM,EAAM7F,SAGTkH,EAAY3B,IAGlCq2B,UAAW,CACVt4B,KAAM,CACLye,IAAK,SAAUlc,EAAMnF,GACpB,IAAMsC,EAAQw4B,YAAwB,UAAV96B,GAC3BsM,EAAUnH,EAAM,SAAY,CAC5B,IAAI/B,EAAM+B,EAAKnF,MAKf,OAJAmF,EAAK1B,aAAc,OAAQzD,GACtBoD,IACJ+B,EAAKnF,MAAQoD,GAEPpD,MAMXg7B,WAAY,SAAU71B,EAAMnF,GAC3B,IAAIV,EACHP,EAAI,EAIJq8B,EAAYp7B,GAASA,EAAM6M,MAAOoO,GAEnC,GAAKmgB,GAA+B,IAAlBj2B,EAAK1C,SACtB,KAAUnD,EAAO87B,EAAWr8B,MAC3BoG,EAAK2I,gBAAiBxO,MAO1By7B,GAAW,CACV1Z,IAAK,SAAUlc,EAAMnF,EAAOV,GAQ3B,OAPe,IAAVU,EAGJ+D,EAAOi3B,WAAY71B,EAAM7F,GAEzB6F,EAAK1B,aAAcnE,EAAMA,GAEnBA,IAITyE,EAAOiB,KAAMjB,EAAO2N,KAAK7E,MAAMsuB,KAAKrY,OAAOjW,MAAO,SAAU,SAAU9N,EAAGO,GACxE,IAAIC,EAASkP,GAAYnP,IAAUyE,EAAOsM,KAAKuB,KAE/CnD,GAAYnP,GAAS,SAAU6F,EAAM7F,EAAMgJ,GAC1C,IAAIzD,EAAKkkB,EACRsS,EAAgB/7B,EAAK4I,cAYtB,OAVMI,IAGLygB,EAASta,GAAY4sB,GACrB5sB,GAAY4sB,GAAkBx2B,EAC9BA,EAAqC,MAA/BtF,EAAQ4F,EAAM7F,EAAMgJ,GACzB+yB,EACA,KACD5sB,GAAY4sB,GAAkBtS,GAExBlkB,MAOT,IAAIy2B,GAAa,sCAChBC,GAAa,gBAyIb,SAASC,GAAkBx7B,GAE1B,OADaA,EAAM6M,MAAOoO,IAAmB,IAC/BvN,KAAM,KAItB,SAAS+tB,GAAUt2B,GAClB,OAAOA,EAAK3B,cAAgB2B,EAAK3B,aAAc,UAAa,GAG7D,SAASk4B,GAAgB17B,GACxB,OAAKsG,MAAMC,QAASvG,GACZA,EAEc,iBAAVA,GACJA,EAAM6M,MAAOoO,IAEd,GAxJRlX,EAAOG,GAAG4B,OAAQ,CACjByb,KAAM,SAAUjiB,EAAMU,GACrB,OAAOmgB,EAAQnf,KAAM+C,EAAOwd,KAAMjiB,EAAMU,EAAOqF,UAAUf,OAAS,IAGnEq3B,WAAY,SAAUr8B,GACrB,OAAO0B,KAAKgE,MAAM,kBACVhE,KAAM+C,EAAO63B,QAASt8B,IAAUA,SAK1CyE,EAAO+B,OAAQ,CACdyb,KAAM,SAAUpc,EAAM7F,EAAMU,GAC3B,IAAI6E,EAAKyd,EACR2Y,EAAQ91B,EAAK1C,SAGd,GAAe,IAAVw4B,GAAyB,IAAVA,GAAyB,IAAVA,EAWnC,OAPe,IAAVA,GAAgBl3B,EAAO4U,SAAUxT,KAGrC7F,EAAOyE,EAAO63B,QAASt8B,IAAUA,EACjCgjB,EAAQve,EAAO4xB,UAAWr2B,SAGZkH,IAAVxG,EACCsiB,GAAS,QAASA,QACuB9b,KAA3C3B,EAAMyd,EAAMjB,IAAKlc,EAAMnF,EAAOV,IACzBuF,EAGCM,EAAM7F,GAASU,EAGpBsiB,GAAS,QAASA,GAA+C,QAApCzd,EAAMyd,EAAM1iB,IAAKuF,EAAM7F,IACjDuF,EAGDM,EAAM7F,IAGdq2B,UAAW,CACV9gB,SAAU,CACTjV,IAAK,SAAUuF,GAOd,IAAI02B,EAAW93B,EAAOsM,KAAKuB,KAAMzM,EAAM,YAEvC,OAAK02B,EACGC,SAAUD,EAAU,IAI3BP,GAAW9tB,KAAMrI,EAAKmH,WACtBivB,GAAW/tB,KAAMrI,EAAKmH,WACtBnH,EAAKyP,KAEE,GAGA,KAKXgnB,QAAS,CACR,IAAO,UACP,MAAS,eAYLt5B,EAAQu4B,cACb92B,EAAO4xB,UAAU5gB,SAAW,CAC3BnV,IAAK,SAAUuF,GAId,IAAIwO,EAASxO,EAAKvB,WAIlB,OAHK+P,GAAUA,EAAO/P,YACrB+P,EAAO/P,WAAWoR,cAEZ,MAERqM,IAAK,SAAUlc,GAId,IAAIwO,EAASxO,EAAKvB,WACb+P,IACJA,EAAOqB,cAEFrB,EAAO/P,YACX+P,EAAO/P,WAAWoR,kBAOvBjR,EAAOiB,KAAM,CACZ,WACA,WACA,YACA,cACA,cACA,UACA,UACA,SACA,cACA,oBACE,WACFjB,EAAO63B,QAAS56B,KAAKkH,eAAkBlH,QA4BxC+C,EAAOG,GAAG4B,OAAQ,CACjBi2B,SAAU,SAAU/7B,GACnB,IAAIg8B,EAAS72B,EAAMwJ,EAAKstB,EAAUC,EAAOx2B,EAAGy2B,EAC3Cp9B,EAAI,EAEL,GAAKwD,EAAYvC,GAChB,OAAOgB,KAAKgE,MAAM,SAAUU,GAC3B3B,EAAQ/C,MAAO+6B,SAAU/7B,EAAMd,KAAM8B,KAAM0E,EAAG+1B,GAAUz6B,WAM1D,IAFAg7B,EAAUN,GAAgB17B,IAEbsE,OACZ,KAAUa,EAAOnE,KAAMjC,MAItB,GAHAk9B,EAAWR,GAAUt2B,GACrBwJ,EAAwB,IAAlBxJ,EAAK1C,UAAoB,IAAM+4B,GAAkBS,GAAa,IAEzD,CAEV,IADAv2B,EAAI,EACMw2B,EAAQF,EAASt2B,MACrBiJ,EAAI3M,QAAS,IAAMk6B,EAAQ,KAAQ,IACvCvtB,GAAOutB,EAAQ,KAMZD,KADLE,EAAaX,GAAkB7sB,KAE9BxJ,EAAK1B,aAAc,QAAS04B,GAMhC,OAAOn7B,MAGRo7B,YAAa,SAAUp8B,GACtB,IAAIg8B,EAAS72B,EAAMwJ,EAAKstB,EAAUC,EAAOx2B,EAAGy2B,EAC3Cp9B,EAAI,EAEL,GAAKwD,EAAYvC,GAChB,OAAOgB,KAAKgE,MAAM,SAAUU,GAC3B3B,EAAQ/C,MAAOo7B,YAAap8B,EAAMd,KAAM8B,KAAM0E,EAAG+1B,GAAUz6B,WAI7D,IAAMqE,UAAUf,OACf,OAAOtD,KAAK4Q,KAAM,QAAS,IAK5B,IAFAoqB,EAAUN,GAAgB17B,IAEbsE,OACZ,KAAUa,EAAOnE,KAAMjC,MAMtB,GALAk9B,EAAWR,GAAUt2B,GAGrBwJ,EAAwB,IAAlBxJ,EAAK1C,UAAoB,IAAM+4B,GAAkBS,GAAa,IAEzD,CAEV,IADAv2B,EAAI,EACMw2B,EAAQF,EAASt2B,MAG1B,KAAQiJ,EAAI3M,QAAS,IAAMk6B,EAAQ,MAAS,GAC3CvtB,EAAMA,EAAI/H,QAAS,IAAMs1B,EAAQ,IAAK,KAMnCD,KADLE,EAAaX,GAAkB7sB,KAE9BxJ,EAAK1B,aAAc,QAAS04B,GAMhC,OAAOn7B,MAGRq7B,YAAa,SAAUr8B,EAAOs8B,GAC7B,IAAI15B,SAAc5C,EACjBu8B,EAAwB,WAAT35B,GAAqB0D,MAAMC,QAASvG,GAEpD,MAAyB,kBAAbs8B,GAA0BC,EAC9BD,EAAWt7B,KAAK+6B,SAAU/7B,GAAUgB,KAAKo7B,YAAap8B,GAGzDuC,EAAYvC,GACTgB,KAAKgE,MAAM,SAAUjG,GAC3BgF,EAAQ/C,MAAOq7B,YACdr8B,EAAMd,KAAM8B,KAAMjC,EAAG08B,GAAUz6B,MAAQs7B,GACvCA,MAKIt7B,KAAKgE,MAAM,WACjB,IAAI+K,EAAWhR,EAAGwa,EAAMijB,EAExB,GAAKD,EAOJ,IAJAx9B,EAAI,EACJwa,EAAOxV,EAAQ/C,MACfw7B,EAAad,GAAgB17B,GAEnB+P,EAAYysB,EAAYz9B,MAG5Bwa,EAAKkjB,SAAU1sB,GACnBwJ,EAAK6iB,YAAarsB,GAElBwJ,EAAKwiB,SAAUhsB,aAKIvJ,IAAVxG,GAAgC,YAAT4C,KAClCmN,EAAY0rB,GAAUz6B,QAIrBygB,EAASJ,IAAKrgB,KAAM,gBAAiB+O,GAOjC/O,KAAKyC,cACTzC,KAAKyC,aAAc,QAClBsM,IAAuB,IAAV/P,EACb,GACAyhB,EAAS7hB,IAAKoB,KAAM,kBAAqB,SAO9Cy7B,SAAU,SAAUz4B,GACnB,IAAI+L,EAAW5K,EACdpG,EAAI,EAGL,IADAgR,EAAY,IAAM/L,EAAW,IACnBmB,EAAOnE,KAAMjC,MACtB,GAAuB,IAAlBoG,EAAK1C,WACP,IAAM+4B,GAAkBC,GAAUt2B,IAAW,KAAMnD,QAAS+N,IAAe,EAC5E,OAAO,EAIV,OAAO,KAOT,IAAI2sB,GAAU,MAEd34B,EAAOG,GAAG4B,OAAQ,CACjB1C,IAAK,SAAUpD,GACd,IAAIsiB,EAAOzd,EAAK8pB,EACfxpB,EAAOnE,KAAM,GAEd,OAAMqE,UAAUf,QA0BhBqqB,EAAkBpsB,EAAYvC,GAEvBgB,KAAKgE,MAAM,SAAUjG,GAC3B,IAAIqE,EAEmB,IAAlBpC,KAAKyB,WAWE,OANXW,EADIurB,EACE3uB,EAAMd,KAAM8B,KAAMjC,EAAGgF,EAAQ/C,MAAOoC,OAEpCpD,GAKNoD,EAAM,GAEoB,iBAARA,EAClBA,GAAO,GAEIkD,MAAMC,QAASnD,KAC1BA,EAAMW,EAAOmB,IAAK9B,GAAK,SAAUpD,GAChC,OAAgB,MAATA,EAAgB,GAAKA,EAAQ,QAItCsiB,EAAQve,EAAO44B,SAAU37B,KAAK4B,OAAUmB,EAAO44B,SAAU37B,KAAKsL,SAASpE,iBAGrD,QAASoa,QAA+C9b,IAApC8b,EAAMjB,IAAKrgB,KAAMoC,EAAK,WAC3DpC,KAAKhB,MAAQoD,QAzDT+B,GACJmd,EAAQve,EAAO44B,SAAUx3B,EAAKvC,OAC7BmB,EAAO44B,SAAUx3B,EAAKmH,SAASpE,iBAG/B,QAASoa,QACgC9b,KAAvC3B,EAAMyd,EAAM1iB,IAAKuF,EAAM,UAElBN,EAMY,iBAHpBA,EAAMM,EAAKnF,OAIH6E,EAAI+B,QAAS81B,GAAS,IAIhB,MAAP73B,EAAc,GAAKA,OAG3B,KAyCHd,EAAO+B,OAAQ,CACd62B,SAAU,CACT1X,OAAQ,CACPrlB,IAAK,SAAUuF,GAEd,IAAI/B,EAAMW,EAAOsM,KAAKuB,KAAMzM,EAAM,SAClC,OAAc,MAAP/B,EACNA,EAMAo4B,GAAkBz3B,EAAOR,KAAM4B,MAGlCsD,OAAQ,CACP7I,IAAK,SAAUuF,GACd,IAAInF,EAAOilB,EAAQlmB,EAClBgH,EAAUZ,EAAKY,QACfsU,EAAQlV,EAAK6P,cACbyS,EAAoB,eAAdtiB,EAAKvC,KACX8hB,EAAS+C,EAAM,KAAO,GACtBiM,EAAMjM,EAAMpN,EAAQ,EAAItU,EAAQzB,OAUjC,IAPCvF,EADIsb,EAAQ,EACRqZ,EAGAjM,EAAMpN,EAAQ,EAIXtb,EAAI20B,EAAK30B,IAKhB,KAJAkmB,EAASlf,EAAShH,IAIJgW,UAAYhW,IAAMsb,KAG7B4K,EAAO5Y,YACL4Y,EAAOrhB,WAAWyI,WACnBC,EAAU2Y,EAAOrhB,WAAY,aAAiB,CAMjD,GAHA5D,EAAQ+D,EAAQkhB,GAAS7hB,MAGpBqkB,EACJ,OAAOznB,EAIR0kB,EAAO3iB,KAAM/B,GAIf,OAAO0kB,GAGRrD,IAAK,SAAUlc,EAAMnF,GAMpB,IALA,IAAI48B,EAAW3X,EACdlf,EAAUZ,EAAKY,QACf2e,EAAS3gB,EAAOuD,UAAWtH,GAC3BjB,EAAIgH,EAAQzB,OAELvF,OACPkmB,EAASlf,EAAShH,IAINgW,SACXhR,EAAOyD,QAASzD,EAAO44B,SAAS1X,OAAOrlB,IAAKqlB,GAAUP,IAAY,KAElEkY,GAAY,GAUd,OAHMA,IACLz3B,EAAK6P,eAAiB,GAEhB0P,OAOX3gB,EAAOiB,KAAM,CAAE,QAAS,aAAc,WACrCjB,EAAO44B,SAAU37B,MAAS,CACzBqgB,IAAK,SAAUlc,EAAMnF,GACpB,GAAKsG,MAAMC,QAASvG,GACnB,OAASmF,EAAK2P,QAAU/Q,EAAOyD,QAASzD,EAAQoB,GAAO/B,MAAOpD,IAAW,IAItEsC,EAAQs4B,UACb72B,EAAO44B,SAAU37B,MAAOpB,IAAM,SAAUuF,GACvC,OAAwC,OAAjCA,EAAK3B,aAAc,SAAqB,KAAO2B,EAAKnF,WAW9DsC,EAAQu6B,QAAU,cAAe17B,EAGjC,IAAI27B,GAAc,kCACjBC,GAA0B,SAAU77B,GACnCA,EAAEgnB,mBAGJnkB,EAAO+B,OAAQ/B,EAAO4jB,MAAO,CAE5BU,QAAS,SAAUV,EAAOrG,EAAMnc,EAAM63B,GAErC,IAAIj+B,EAAG4P,EAAK6B,EAAKysB,EAAYC,EAAQnU,EAAQ9K,EAASkf,EACrDC,EAAY,CAAEj4B,GAAQ7D,GACtBsB,EAAOT,EAAOjD,KAAMyoB,EAAO,QAAWA,EAAM/kB,KAAO+kB,EACnDiB,EAAazmB,EAAOjD,KAAMyoB,EAAO,aAAgBA,EAAMrY,UAAUrH,MAAO,KAAQ,GAKjF,GAHA0G,EAAMwuB,EAAc3sB,EAAMrL,EAAOA,GAAQ7D,EAGlB,IAAlB6D,EAAK1C,UAAoC,IAAlB0C,EAAK1C,WAK5Bq6B,GAAYtvB,KAAM5K,EAAOmB,EAAO4jB,MAAMqB,aAItCpmB,EAAKZ,QAAS,MAAS,IAG3B4mB,EAAahmB,EAAKqF,MAAO,KACzBrF,EAAOgmB,EAAW1a,QAClB0a,EAAWhjB,QAEZs3B,EAASt6B,EAAKZ,QAAS,KAAQ,GAAK,KAAOY,GAG3C+kB,EAAQA,EAAO5jB,EAAO0C,SACrBkhB,EACA,IAAI5jB,EAAOukB,MAAO1lB,EAAuB,iBAAV+kB,GAAsBA,IAGhDK,UAAYgV,EAAe,EAAI,EACrCrV,EAAMrY,UAAYsZ,EAAWlb,KAAM,KACnCia,EAAMsC,WAAatC,EAAMrY,UACxB,IAAI9E,OAAQ,UAAYoe,EAAWlb,KAAM,iBAAoB,WAC7D,KAGDia,EAAM1U,YAASzM,EACTmhB,EAAMxhB,SACXwhB,EAAMxhB,OAAShB,GAIhBmc,EAAe,MAARA,EACN,CAAEqG,GACF5jB,EAAOuD,UAAWga,EAAM,CAAEqG,IAG3B1J,EAAUla,EAAO4jB,MAAM1J,QAASrb,IAAU,GACpCo6B,IAAgB/e,EAAQoK,UAAmD,IAAxCpK,EAAQoK,QAAQjjB,MAAOD,EAAMmc,IAAtE,CAMA,IAAM0b,IAAiB/e,EAAQ0M,WAAajoB,EAAUyC,GAAS,CAM9D,IAJA83B,EAAahf,EAAQgK,cAAgBrlB,EAC/Bk6B,GAAYtvB,KAAMyvB,EAAar6B,KACpC+L,EAAMA,EAAI/K,YAEH+K,EAAKA,EAAMA,EAAI/K,WACtBw5B,EAAUr7B,KAAM4M,GAChB6B,EAAM7B,EAIF6B,KAAUrL,EAAK8H,eAAiB3L,IACpC87B,EAAUr7B,KAAMyO,EAAIb,aAAea,EAAI6sB,cAAgBl8B,GAMzD,IADApC,EAAI,GACM4P,EAAMyuB,EAAWr+B,QAAY4oB,EAAMmC,wBAC5CqT,EAAcxuB,EACdgZ,EAAM/kB,KAAO7D,EAAI,EAChBk+B,EACAhf,EAAQiL,UAAYtmB,GAGrBmmB,GAAWtH,EAAS7hB,IAAK+O,EAAK,WAAc,IAAMgZ,EAAM/kB,OACvD6e,EAAS7hB,IAAK+O,EAAK,YAEnBoa,EAAO3jB,MAAOuJ,EAAK2S,IAIpByH,EAASmU,GAAUvuB,EAAKuuB,KACTnU,EAAO3jB,OAAS4b,EAAYrS,KAC1CgZ,EAAM1U,OAAS8V,EAAO3jB,MAAOuJ,EAAK2S,IACZ,IAAjBqG,EAAM1U,QACV0U,EAAMS,kBA8CT,OA1CAT,EAAM/kB,KAAOA,EAGPo6B,GAAiBrV,EAAMqD,sBAEpB/M,EAAQqH,WACqC,IAApDrH,EAAQqH,SAASlgB,MAAOg4B,EAAUrzB,MAAOuX,KACzCN,EAAY7b,IAIP+3B,GAAU36B,EAAY4C,EAAMvC,MAAaF,EAAUyC,MAGvDqL,EAAMrL,EAAM+3B,MAGX/3B,EAAM+3B,GAAW,MAIlBn5B,EAAO4jB,MAAMqB,UAAYpmB,EAEpB+kB,EAAMmC,wBACVqT,EAAYttB,iBAAkBjN,EAAMm6B,IAGrC53B,EAAMvC,KAED+kB,EAAMmC,wBACVqT,EAAYtd,oBAAqBjd,EAAMm6B,IAGxCh5B,EAAO4jB,MAAMqB,eAAYxiB,EAEpBgK,IACJrL,EAAM+3B,GAAW1sB,IAMdmX,EAAM1U,SAKdqqB,SAAU,SAAU16B,EAAMuC,EAAMwiB,GAC/B,IAAIzmB,EAAI6C,EAAO+B,OACd,IAAI/B,EAAOukB,MACXX,EACA,CACC/kB,KAAMA,EACNyoB,aAAa,IAIftnB,EAAO4jB,MAAMU,QAASnnB,EAAG,KAAMiE,MAKjCpB,EAAOG,GAAG4B,OAAQ,CAEjBuiB,QAAS,SAAUzlB,EAAM0e,GACxB,OAAOtgB,KAAKgE,MAAM,WACjBjB,EAAO4jB,MAAMU,QAASzlB,EAAM0e,EAAMtgB,UAGpCu8B,eAAgB,SAAU36B,EAAM0e,GAC/B,IAAInc,EAAOnE,KAAM,GACjB,GAAKmE,EACJ,OAAOpB,EAAO4jB,MAAMU,QAASzlB,EAAM0e,EAAMnc,GAAM,MAc5C7C,EAAQu6B,SACb94B,EAAOiB,KAAM,CAAEioB,MAAO,UAAWC,KAAM,aAAc,SAAUK,EAAM5D,GAGpE,IAAInb,EAAU,SAAUmZ,GACvB5jB,EAAO4jB,MAAM2V,SAAU3T,EAAKhC,EAAMxhB,OAAQpC,EAAO4jB,MAAMgC,IAAKhC,KAG7D5jB,EAAO4jB,MAAM1J,QAAS0L,GAAQ,CAC7BP,MAAO,WACN,IAAIjmB,EAAMnC,KAAKiM,eAAiBjM,KAC/Bw8B,EAAW/b,EAAStB,OAAQhd,EAAKwmB,GAE5B6T,GACLr6B,EAAI0M,iBAAkB0d,EAAM/e,GAAS,GAEtCiT,EAAStB,OAAQhd,EAAKwmB,GAAO6T,GAAY,GAAM,IAEhDjU,SAAU,WACT,IAAIpmB,EAAMnC,KAAKiM,eAAiBjM,KAC/Bw8B,EAAW/b,EAAStB,OAAQhd,EAAKwmB,GAAQ,EAEpC6T,EAKL/b,EAAStB,OAAQhd,EAAKwmB,EAAK6T,IAJ3Br6B,EAAI0c,oBAAqB0N,EAAM/e,GAAS,GACxCiT,EAAS/E,OAAQvZ,EAAKwmB,SAS3B,IAAIlV,GAAWtT,EAAOsT,SAElB3R,GAAQqG,KAAKiiB,MAEbqS,GAAS,KAKb15B,EAAO25B,SAAW,SAAUpc,GAC3B,IAAIhO,EACJ,IAAMgO,GAAwB,iBAATA,EACpB,OAAO,KAKR,IACChO,GAAM,IAAMnS,EAAOw8B,WAAcC,gBAAiBtc,EAAM,YACvD,MAAQpgB,GACToS,OAAM9M,EAMP,OAHM8M,IAAOA,EAAIjG,qBAAsB,eAAgB/I,QACtDP,EAAO+C,MAAO,gBAAkBwa,GAE1BhO,GAIR,IACCuqB,GAAW,QACXC,GAAQ,SACRC,GAAkB,wCAClBC,GAAe,qCAEhB,SAASC,GAAa3I,EAAQ9yB,EAAK07B,EAAa3jB,GAC/C,IAAIjb,EAEJ,GAAKgH,MAAMC,QAAS/D,GAGnBuB,EAAOiB,KAAMxC,GAAK,SAAUzD,EAAGoc,GACzB+iB,GAAeL,GAASrwB,KAAM8nB,GAGlC/a,EAAK+a,EAAQna,GAKb8iB,GACC3I,EAAS,KAAqB,iBAANna,GAAuB,MAALA,EAAYpc,EAAI,IAAO,IACjEoc,EACA+iB,EACA3jB,WAKG,GAAM2jB,GAAiC,WAAlBp6B,EAAQtB,GAUnC+X,EAAK+a,EAAQ9yB,QAPb,IAAMlD,KAAQkD,EACby7B,GAAa3I,EAAS,IAAMh2B,EAAO,IAAKkD,EAAKlD,GAAQ4+B,EAAa3jB,GAYrExW,EAAOo6B,MAAQ,SAAUt0B,EAAGq0B,GAC3B,IAAI5I,EACHx0B,EAAI,GACJyZ,EAAM,SAAUja,EAAK89B,GAGpB,IAAIp+B,EAAQuC,EAAY67B,GACvBA,IACAA,EAEDt9B,EAAGA,EAAEwD,QAAW+5B,mBAAoB/9B,GAAQ,IAC3C+9B,mBAA6B,MAATr+B,EAAgB,GAAKA,IAG5C,GAAU,MAAL6J,EACJ,MAAO,GAIR,GAAKvD,MAAMC,QAASsD,IAASA,EAAEtF,SAAWR,EAAOsC,cAAewD,GAG/D9F,EAAOiB,KAAM6E,GAAG,WACf0Q,EAAKvZ,KAAK1B,KAAM0B,KAAKhB,eAOtB,IAAMs1B,KAAUzrB,EACfo0B,GAAa3I,EAAQzrB,EAAGyrB,GAAU4I,EAAa3jB,GAKjD,OAAOzZ,EAAE4M,KAAM,MAGhB3J,EAAOG,GAAG4B,OAAQ,CACjBw4B,UAAW,WACV,OAAOv6B,EAAOo6B,MAAOn9B,KAAKu9B,mBAE3BA,eAAgB,WACf,OAAOv9B,KAAKkE,KAAK,WAGhB,IAAIyM,EAAW5N,EAAOwd,KAAMvgB,KAAM,YAClC,OAAO2Q,EAAW5N,EAAOuD,UAAWqK,GAAa3Q,QAEjDmP,QAAQ,WACR,IAAIvN,EAAO5B,KAAK4B,KAGhB,OAAO5B,KAAK1B,OAASyE,EAAQ/C,MAAO+X,GAAI,cACvCilB,GAAaxwB,KAAMxM,KAAKsL,YAAeyxB,GAAgBvwB,KAAM5K,KAC3D5B,KAAK8T,UAAY+P,GAAerX,KAAM5K,OAEzCsC,KAAK,SAAUnG,EAAGoG,GAClB,IAAI/B,EAAMW,EAAQ/C,MAAOoC,MAEzB,OAAY,MAAPA,EACG,KAGHkD,MAAMC,QAASnD,GACZW,EAAOmB,IAAK9B,GAAK,SAAUA,GACjC,MAAO,CAAE9D,KAAM6F,EAAK7F,KAAMU,MAAOoD,EAAIwD,QAASk3B,GAAO,YAIhD,CAAEx+B,KAAM6F,EAAK7F,KAAMU,MAAOoD,EAAIwD,QAASk3B,GAAO,YAClDl+B,SAKN,IACC4+B,GAAM,OACNC,GAAQ,OACRC,GAAa,gBACbC,GAAW,6BAIXC,GAAa,iBACbC,GAAY,QAWZjH,GAAa,GAObkH,GAAa,GAGbC,GAAW,KAAKj9B,OAAQ,KAGxBk9B,GAAe19B,EAASgC,cAAe,KAIxC,SAAS27B,GAA6BC,GAGrC,OAAO,SAAUC,EAAoBniB,GAED,iBAAvBmiB,IACXniB,EAAOmiB,EACPA,EAAqB,KAGtB,IAAIC,EACHrgC,EAAI,EACJsgC,EAAYF,EAAmBj3B,cAAc2E,MAAOoO,IAAmB,GAExE,GAAK1Y,EAAYya,GAGhB,KAAUoiB,EAAWC,EAAWtgC,MAGR,MAAlBqgC,EAAU,IACdA,EAAWA,EAASv9B,MAAO,IAAO,KAChCq9B,EAAWE,GAAaF,EAAWE,IAAc,IAAK3tB,QAASuL,KAI/DkiB,EAAWE,GAAaF,EAAWE,IAAc,IAAKr9B,KAAMib,IAQnE,SAASsiB,GAA+BJ,EAAWn5B,EAASoyB,EAAiBoH,GAE5E,IAAIC,EAAY,GACfC,EAAqBP,IAAcJ,GAEpC,SAASY,EAASN,GACjB,IAAIrqB,EAcJ,OAbAyqB,EAAWJ,IAAa,EACxBr7B,EAAOiB,KAAMk6B,EAAWE,IAAc,IAAI,SAAU7zB,EAAGo0B,GACtD,IAAIC,EAAsBD,EAAoB55B,EAASoyB,EAAiBoH,GACxE,MAAoC,iBAAxBK,GACVH,GAAqBD,EAAWI,GAKtBH,IACD1qB,EAAW6qB,QADf,GAHN75B,EAAQs5B,UAAU5tB,QAASmuB,GAC3BF,EAASE,IACF,MAKF7qB,EAGR,OAAO2qB,EAAS35B,EAAQs5B,UAAW,MAAUG,EAAW,MAASE,EAAS,KAM3E,SAASG,GAAY15B,EAAQtD,GAC5B,IAAIvC,EAAK8F,EACR05B,EAAc/7B,EAAOg8B,aAAaD,aAAe,GAElD,IAAMx/B,KAAOuC,OACQ2D,IAAf3D,EAAKvC,MACPw/B,EAAax/B,GAAQ6F,EAAWC,IAAUA,EAAO,KAAU9F,GAAQuC,EAAKvC,IAO5E,OAJK8F,GACJrC,EAAO+B,QAAQ,EAAMK,EAAQC,GAGvBD,EA/EP64B,GAAapqB,KAAOH,GAASG,KAgP9B7Q,EAAO+B,OAAQ,CAGdk6B,OAAQ,EAGRC,aAAc,GACdC,KAAM,GAENH,aAAc,CACbI,IAAK1rB,GAASG,KACdhS,KAAM,MACNw9B,QAvRgB,4DAuRQ5yB,KAAMiH,GAAS4rB,UACvCj/B,QAAQ,EACRk/B,aAAa,EACbC,OAAO,EACPC,YAAa,mDAcbC,QAAS,CACR,IAAK1B,GACLx7B,KAAM,aACNqrB,KAAM,YACNtb,IAAK,4BACLotB,KAAM,qCAGP3mB,SAAU,CACTzG,IAAK,UACLsb,KAAM,SACN8R,KAAM,YAGPC,eAAgB,CACfrtB,IAAK,cACL/P,KAAM,eACNm9B,KAAM,gBAKPE,WAAY,CAGX,SAAUj1B,OAGV,aAAa,EAGb,YAAamW,KAAKC,MAGlB,WAAYhe,EAAO25B,UAOpBoC,YAAa,CACZK,KAAK,EACLl8B,SAAS,IAOX48B,UAAW,SAAU16B,EAAQ26B,GAC5B,OAAOA,EAGNjB,GAAYA,GAAY15B,EAAQpC,EAAOg8B,cAAgBe,GAGvDjB,GAAY97B,EAAOg8B,aAAc55B,IAGnC46B,cAAe9B,GAA6BrH,IAC5CoJ,cAAe/B,GAA6BH,IAG5CmC,KAAM,SAAUd,EAAKp6B,GAGA,iBAARo6B,IACXp6B,EAAUo6B,EACVA,OAAM35B,GAIPT,EAAUA,GAAW,GAErB,IAAIm7B,EAGHC,EAGAC,EACAC,EAGAC,EAGAC,EAGA3hB,EAGA4hB,EAGAziC,EAGA0iC,EAGA3gC,EAAIiD,EAAO88B,UAAW,GAAI96B,GAG1B27B,EAAkB5gC,EAAEmD,SAAWnD,EAG/B6gC,EAAqB7gC,EAAEmD,UACpBy9B,EAAgBj/B,UAAYi/B,EAAgBn9B,QAC7CR,EAAQ29B,GACR39B,EAAO4jB,MAGTvK,EAAWrZ,EAAOgZ,WAClB6kB,EAAmB79B,EAAO+X,UAAW,eAGrC+lB,EAAa/gC,EAAE+gC,YAAc,GAG7BC,EAAiB,GACjBC,EAAsB,GAGtBC,EAAW,WAGXzC,EAAQ,CACPtf,WAAY,EAGZgiB,kBAAmB,SAAU3hC,GAC5B,IAAIuM,EACJ,GAAK+S,EAAY,CAChB,IAAMyhB,EAEL,IADAA,EAAkB,GACRx0B,EAAQ8xB,GAASzxB,KAAMk0B,IAChCC,EAAiBx0B,EAAO,GAAI3E,cAAgB,MACzCm5B,EAAiBx0B,EAAO,GAAI3E,cAAgB,MAAS,IACrDpG,OAAQ+K,EAAO,IAGpBA,EAAQw0B,EAAiB/gC,EAAI4H,cAAgB,KAE9C,OAAgB,MAAT2E,EAAgB,KAAOA,EAAMa,KAAM,OAI3Cw0B,sBAAuB,WACtB,OAAOtiB,EAAYwhB,EAAwB,MAI5Ce,iBAAkB,SAAU7iC,EAAMU,GAMjC,OALkB,MAAb4f,IACJtgB,EAAOyiC,EAAqBziC,EAAK4I,eAChC65B,EAAqBziC,EAAK4I,gBAAmB5I,EAC9CwiC,EAAgBxiC,GAASU,GAEnBgB,MAIRohC,iBAAkB,SAAUx/B,GAI3B,OAHkB,MAAbgd,IACJ9e,EAAEuhC,SAAWz/B,GAEP5B,MAIR6gC,WAAY,SAAU38B,GACrB,IAAIjC,EACJ,GAAKiC,EACJ,GAAK0a,EAGJ2f,EAAMpiB,OAAQjY,EAAKq6B,EAAM+C,cAIzB,IAAMr/B,KAAQiC,EACb28B,EAAY5+B,GAAS,CAAE4+B,EAAY5+B,GAAQiC,EAAKjC,IAInD,OAAOjC,MAIRuhC,MAAO,SAAUC,GAChB,IAAIC,EAAYD,GAAcR,EAK9B,OAJKd,GACJA,EAAUqB,MAAOE,GAElBn5B,EAAM,EAAGm5B,GACFzhC,OAoBV,GAfAoc,EAASzB,QAAS4jB,GAKlBz+B,EAAEq/B,MAAUA,GAAOr/B,EAAEq/B,KAAO1rB,GAASG,MAAS,IAC5ChO,QAASi4B,GAAWpqB,GAAS4rB,SAAW,MAG1Cv/B,EAAE8B,KAAOmD,EAAQ2V,QAAU3V,EAAQnD,MAAQ9B,EAAE4a,QAAU5a,EAAE8B,KAGzD9B,EAAEu+B,WAAcv+B,EAAEs+B,UAAY,KAAMl3B,cAAc2E,MAAOoO,IAAmB,CAAE,IAGxD,MAAjBna,EAAE4hC,YAAsB,CAC5BnB,EAAYjgC,EAASgC,cAAe,KAKpC,IACCi+B,EAAU3sB,KAAO9T,EAAEq/B,IAInBoB,EAAU3sB,KAAO2sB,EAAU3sB,KAC3B9T,EAAE4hC,YAAc1D,GAAaqB,SAAW,KAAOrB,GAAa2D,MAC3DpB,EAAUlB,SAAW,KAAOkB,EAAUoB,KACtC,MAAQzhC,GAITJ,EAAE4hC,aAAc,GAalB,GARK5hC,EAAEwgB,MAAQxgB,EAAEw/B,aAAiC,iBAAXx/B,EAAEwgB,OACxCxgB,EAAEwgB,KAAOvd,EAAOo6B,MAAOr9B,EAAEwgB,KAAMxgB,EAAEo9B,cAIlCoB,GAA+B1H,GAAY92B,EAAGiF,EAASw5B,GAGlD3f,EACJ,OAAO2f,EA6ER,IAAMxgC,KAxENyiC,EAAcz9B,EAAO4jB,OAAS7mB,EAAEM,SAGQ,GAApB2C,EAAOi8B,UAC1Bj8B,EAAO4jB,MAAMU,QAAS,aAIvBvnB,EAAE8B,KAAO9B,EAAE8B,KAAKie,cAGhB/f,EAAE8hC,YAAchE,GAAWpxB,KAAM1M,EAAE8B,MAKnCu+B,EAAWrgC,EAAEq/B,IAAIv5B,QAAS63B,GAAO,IAG3B39B,EAAE8hC,WAuBI9hC,EAAEwgB,MAAQxgB,EAAEw/B,aACoD,KAAzEx/B,EAAE0/B,aAAe,IAAKx+B,QAAS,uCACjClB,EAAEwgB,KAAOxgB,EAAEwgB,KAAK1a,QAAS43B,GAAK,OAtB9BiD,EAAW3gC,EAAEq/B,IAAIt+B,MAAOs/B,EAAS78B,QAG5BxD,EAAEwgB,OAAUxgB,EAAEw/B,aAAiC,iBAAXx/B,EAAEwgB,QAC1C6f,IAAc1D,GAAOjwB,KAAM2zB,GAAa,IAAM,KAAQrgC,EAAEwgB,YAGjDxgB,EAAEwgB,OAIO,IAAZxgB,EAAEkN,QACNmzB,EAAWA,EAASv6B,QAAS83B,GAAY,MACzC+C,GAAahE,GAAOjwB,KAAM2zB,GAAa,IAAM,KAAQ,KAASr+B,KAAY2+B,GAI3E3gC,EAAEq/B,IAAMgB,EAAWM,GASf3gC,EAAE+hC,aACD9+B,EAAOk8B,aAAckB,IACzB5B,EAAM4C,iBAAkB,oBAAqBp+B,EAAOk8B,aAAckB,IAE9Dp9B,EAAOm8B,KAAMiB,IACjB5B,EAAM4C,iBAAkB,gBAAiBp+B,EAAOm8B,KAAMiB,MAKnDrgC,EAAEwgB,MAAQxgB,EAAE8hC,aAAgC,IAAlB9hC,EAAE0/B,aAAyBz6B,EAAQy6B,cACjEjB,EAAM4C,iBAAkB,eAAgBrhC,EAAE0/B,aAI3CjB,EAAM4C,iBACL,SACArhC,EAAEu+B,UAAW,IAAOv+B,EAAE2/B,QAAS3/B,EAAEu+B,UAAW,IAC3Cv+B,EAAE2/B,QAAS3/B,EAAEu+B,UAAW,KACA,MAArBv+B,EAAEu+B,UAAW,GAAc,KAAON,GAAW,WAAa,IAC7Dj+B,EAAE2/B,QAAS,MAIF3/B,EAAEgiC,QACZvD,EAAM4C,iBAAkBpjC,EAAG+B,EAAEgiC,QAAS/jC,IAIvC,GAAK+B,EAAEiiC,cAC+C,IAAnDjiC,EAAEiiC,WAAW7jC,KAAMwiC,EAAiBnC,EAAOz+B,IAAiB8e,GAG9D,OAAO2f,EAAMgD,QAed,GAXAP,EAAW,QAGXJ,EAAiBrnB,IAAKzZ,EAAEw3B,UACxBiH,EAAMj2B,KAAMxI,EAAEkiC,SACdzD,EAAM3jB,KAAM9a,EAAEgG,OAGdo6B,EAAY5B,GAA+BR,GAAYh+B,EAAGiF,EAASw5B,GAK5D,CASN,GARAA,EAAMtf,WAAa,EAGduhB,GACJG,EAAmBtZ,QAAS,WAAY,CAAEkX,EAAOz+B,IAI7C8e,EACJ,OAAO2f,EAIHz+B,EAAEy/B,OAASz/B,EAAE45B,QAAU,IAC3B4G,EAAengC,EAAOyd,YAAY,WACjC2gB,EAAMgD,MAAO,aACXzhC,EAAE45B,UAGN,IACC9a,GAAY,EACZshB,EAAU+B,KAAMnB,EAAgBx4B,GAC/B,MAAQpI,GAGT,GAAK0e,EACJ,MAAM1e,EAIPoI,GAAO,EAAGpI,SAhCXoI,GAAO,EAAG,gBAqCX,SAASA,EAAMg5B,EAAQY,EAAkBC,EAAWL,GACnD,IAAIM,EAAWJ,EAASl8B,EAAOu8B,EAAUC,EACxCd,EAAaU,EAGTtjB,IAILA,GAAY,EAGP0hB,GACJngC,EAAOw5B,aAAc2G,GAKtBJ,OAAY16B,EAGZ46B,EAAwB0B,GAAW,GAGnCvD,EAAMtf,WAAaqiB,EAAS,EAAI,EAAI,EAGpCc,EAAYd,GAAU,KAAOA,EAAS,KAAkB,MAAXA,EAGxCa,IACJE,EA5lBJ,SAA8BviC,EAAGy+B,EAAO4D,GAOvC,IALA,IAAII,EAAI3gC,EAAM4gC,EAAeC,EAC5B1pB,EAAWjZ,EAAEiZ,SACbslB,EAAYv+B,EAAEu+B,UAGY,MAAnBA,EAAW,IAClBA,EAAUnxB,aACE1H,IAAP+8B,IACJA,EAAKziC,EAAEuhC,UAAY9C,EAAM0C,kBAAmB,iBAK9C,GAAKsB,EACJ,IAAM3gC,KAAQmX,EACb,GAAKA,EAAUnX,IAAUmX,EAAUnX,GAAO4K,KAAM+1B,GAAO,CACtDlE,EAAU5tB,QAAS7O,GACnB,MAMH,GAAKy8B,EAAW,KAAO8D,EACtBK,EAAgBnE,EAAW,OACrB,CAGN,IAAMz8B,KAAQugC,EAAY,CACzB,IAAM9D,EAAW,IAAOv+B,EAAE8/B,WAAYh+B,EAAO,IAAMy8B,EAAW,IAAQ,CACrEmE,EAAgB5gC,EAChB,MAEK6gC,IACLA,EAAgB7gC,GAKlB4gC,EAAgBA,GAAiBC,EAMlC,GAAKD,EAIJ,OAHKA,IAAkBnE,EAAW,IACjCA,EAAU5tB,QAAS+xB,GAEbL,EAAWK,GAyiBLE,CAAqB5iC,EAAGy+B,EAAO4D,IAI3CE,EAtiBH,SAAsBviC,EAAGuiC,EAAU9D,EAAO6D,GACzC,IAAIO,EAAOC,EAASC,EAAMrzB,EAAKwJ,EAC9B4mB,EAAa,GAGbvB,EAAYv+B,EAAEu+B,UAAUx9B,QAGzB,GAAKw9B,EAAW,GACf,IAAMwE,KAAQ/iC,EAAE8/B,WACfA,EAAYiD,EAAK37B,eAAkBpH,EAAE8/B,WAAYiD,GAOnD,IAHAD,EAAUvE,EAAUnxB,QAGZ01B,GAcP,GAZK9iC,EAAE6/B,eAAgBiD,KACtBrE,EAAOz+B,EAAE6/B,eAAgBiD,IAAcP,IAIlCrpB,GAAQopB,GAAatiC,EAAEgjC,aAC5BT,EAAWviC,EAAEgjC,WAAYT,EAAUviC,EAAEs+B,WAGtCplB,EAAO4pB,EACPA,EAAUvE,EAAUnxB,QAKnB,GAAiB,MAAZ01B,EAEJA,EAAU5pB,OAGJ,GAAc,MAATA,GAAgBA,IAAS4pB,EAAU,CAM9C,KAHAC,EAAOjD,EAAY5mB,EAAO,IAAM4pB,IAAahD,EAAY,KAAOgD,IAI/D,IAAMD,KAAS/C,EAId,IADApwB,EAAMmzB,EAAM17B,MAAO,MACT,KAAQ27B,IAGjBC,EAAOjD,EAAY5mB,EAAO,IAAMxJ,EAAK,KACpCowB,EAAY,KAAOpwB,EAAK,KACb,EAGG,IAATqzB,EACJA,EAAOjD,EAAY+C,IAGgB,IAAxB/C,EAAY+C,KACvBC,EAAUpzB,EAAK,GACf6uB,EAAU5tB,QAASjB,EAAK,KAEzB,MAOJ,IAAc,IAATqzB,EAGJ,GAAKA,GAAQ/iC,EAAEijC,OACdV,EAAWQ,EAAMR,QAEjB,IACCA,EAAWQ,EAAMR,GAChB,MAAQniC,GACT,MAAO,CACNgc,MAAO,cACPpW,MAAO+8B,EAAO3iC,EAAI,sBAAwB8Y,EAAO,OAAS4pB,IASjE,MAAO,CAAE1mB,MAAO,UAAWoE,KAAM+hB,GAycpBW,CAAaljC,EAAGuiC,EAAU9D,EAAO6D,GAGvCA,GAGCtiC,EAAE+hC,cACNS,EAAW/D,EAAM0C,kBAAmB,oBAEnCl+B,EAAOk8B,aAAckB,GAAamC,IAEnCA,EAAW/D,EAAM0C,kBAAmB,WAEnCl+B,EAAOm8B,KAAMiB,GAAamC,IAKZ,MAAXhB,GAA6B,SAAXxhC,EAAE8B,KACxB4/B,EAAa,YAGS,MAAXF,EACXE,EAAa,eAIbA,EAAaa,EAASnmB,MACtB8lB,EAAUK,EAAS/hB,KAEnB8hB,IADAt8B,EAAQu8B,EAASv8B,UAMlBA,EAAQ07B,GACHF,GAAWE,IACfA,EAAa,QACRF,EAAS,IACbA,EAAS,KAMZ/C,EAAM+C,OAASA,EACf/C,EAAMiD,YAAeU,GAAoBV,GAAe,GAGnDY,EACJhmB,EAASkB,YAAaojB,EAAiB,CAAEsB,EAASR,EAAYjD,IAE9DniB,EAASsB,WAAYgjB,EAAiB,CAAEnC,EAAOiD,EAAY17B,IAI5Dy4B,EAAMsC,WAAYA,GAClBA,OAAar7B,EAERg7B,GACJG,EAAmBtZ,QAAS+a,EAAY,cAAgB,YACvD,CAAE7D,EAAOz+B,EAAGsiC,EAAYJ,EAAUl8B,IAIpC86B,EAAiB9kB,SAAU4kB,EAAiB,CAAEnC,EAAOiD,IAEhDhB,IACJG,EAAmBtZ,QAAS,eAAgB,CAAEkX,EAAOz+B,MAG3CiD,EAAOi8B,QAChBj8B,EAAO4jB,MAAMU,QAAS,cAKzB,OAAOkX,GAGR0E,QAAS,SAAU9D,EAAK7e,EAAMrc,GAC7B,OAAOlB,EAAOnE,IAAKugC,EAAK7e,EAAMrc,EAAU,SAGzCi/B,UAAW,SAAU/D,EAAKl7B,GACzB,OAAOlB,EAAOnE,IAAKugC,OAAK35B,EAAWvB,EAAU,aAI/ClB,EAAOiB,KAAM,CAAE,MAAO,SAAU,SAAUjG,EAAG2c,GAC5C3X,EAAQ2X,GAAW,SAAUykB,EAAK7e,EAAMrc,EAAUrC,GAUjD,OAPKL,EAAY+e,KAChB1e,EAAOA,GAAQqC,EACfA,EAAWqc,EACXA,OAAO9a,GAIDzC,EAAOk9B,KAAMl9B,EAAO+B,OAAQ,CAClCq6B,IAAKA,EACLv9B,KAAM8Y,EACN0jB,SAAUx8B,EACV0e,KAAMA,EACN0hB,QAAS/9B,GACPlB,EAAOsC,cAAe85B,IAASA,QAKpCp8B,EAAO8qB,SAAW,SAAUsR,EAAKp6B,GAChC,OAAOhC,EAAOk9B,KAAM,CACnBd,IAAKA,EAGLv9B,KAAM,MACNw8B,SAAU,SACVpxB,OAAO,EACPuyB,OAAO,EACPn/B,QAAQ,EAKRw/B,WAAY,CACX,cAAe,cAEhBkD,WAAY,SAAUT,GACrBt/B,EAAOqD,WAAYi8B,EAAUt9B,OAMhChC,EAAOG,GAAG4B,OAAQ,CACjBq+B,QAAS,SAAUvV,GAClB,IAAIvI,EAyBJ,OAvBKrlB,KAAM,KACLuB,EAAYqsB,KAChBA,EAAOA,EAAK1vB,KAAM8B,KAAM,KAIzBqlB,EAAOtiB,EAAQ6qB,EAAM5tB,KAAM,GAAIiM,eAAgB1H,GAAI,GAAIW,OAAO,GAEzDlF,KAAM,GAAI4C,YACdyiB,EAAKmJ,aAAcxuB,KAAM,IAG1BqlB,EAAKnhB,KAAK,WAGT,IAFA,IAAIC,EAAOnE,KAEHmE,EAAKi/B,mBACZj/B,EAAOA,EAAKi/B,kBAGb,OAAOj/B,KACJmqB,OAAQtuB,OAGNA,MAGRqjC,UAAW,SAAUzV,GACpB,OAAKrsB,EAAYqsB,GACT5tB,KAAKgE,MAAM,SAAUjG,GAC3BgF,EAAQ/C,MAAOqjC,UAAWzV,EAAK1vB,KAAM8B,KAAMjC,OAItCiC,KAAKgE,MAAM,WACjB,IAAIuU,EAAOxV,EAAQ/C,MAClB+Y,EAAWR,EAAKQ,WAEZA,EAASzV,OACbyV,EAASoqB,QAASvV,GAGlBrV,EAAK+V,OAAQV,OAKhBvI,KAAM,SAAUuI,GACf,IAAI0V,EAAiB/hC,EAAYqsB,GAEjC,OAAO5tB,KAAKgE,MAAM,SAAUjG,GAC3BgF,EAAQ/C,MAAOmjC,QAASG,EAAiB1V,EAAK1vB,KAAM8B,KAAMjC,GAAM6vB,OAIlE2V,OAAQ,SAAUvgC,GAIjB,OAHAhD,KAAK2S,OAAQ3P,GAAWsV,IAAK,QAAStU,MAAM,WAC3CjB,EAAQ/C,MAAO2uB,YAAa3uB,KAAKyL,eAE3BzL,QAKT+C,EAAO2N,KAAKpH,QAAQwsB,OAAS,SAAU3xB,GACtC,OAAQpB,EAAO2N,KAAKpH,QAAQk6B,QAASr/B,IAEtCpB,EAAO2N,KAAKpH,QAAQk6B,QAAU,SAAUr/B,GACvC,SAAWA,EAAK4sB,aAAe5sB,EAAKs/B,cAAgBt/B,EAAKovB,iBAAiBjwB,SAM3EP,EAAOg8B,aAAa2E,IAAM,WACzB,IACC,OAAO,IAAIvjC,EAAOwjC,eACjB,MAAQzjC,MAGX,IAAI0jC,GAAmB,CAGrBC,EAAG,IAIHC,KAAM,KAEPC,GAAehhC,EAAOg8B,aAAa2E,MAEpCpiC,EAAQ0iC,OAASD,IAAkB,oBAAqBA,GACxDziC,EAAQ2+B,KAAO8D,KAAiBA,GAEhChhC,EAAOi9B,eAAe,SAAUj7B,GAC/B,IAAId,EAAUggC,EAGd,GAAK3iC,EAAQ0iC,MAAQD,KAAiBh/B,EAAQ28B,YAC7C,MAAO,CACNO,KAAM,SAAUH,EAASxK,GACxB,IAAIv5B,EACH2lC,EAAM3+B,EAAQ2+B,MAWf,GATAA,EAAIQ,KACHn/B,EAAQnD,KACRmD,EAAQo6B,IACRp6B,EAAQw6B,MACRx6B,EAAQo/B,SACRp/B,EAAQqP,UAIJrP,EAAQq/B,UACZ,IAAMrmC,KAAKgH,EAAQq/B,UAClBV,EAAK3lC,GAAMgH,EAAQq/B,UAAWrmC,GAmBhC,IAAMA,KAdDgH,EAAQs8B,UAAYqC,EAAItC,kBAC5BsC,EAAItC,iBAAkBr8B,EAAQs8B,UAQzBt8B,EAAQ28B,aAAgBI,EAAS,sBACtCA,EAAS,oBAAuB,kBAItBA,EACV4B,EAAIvC,iBAAkBpjC,EAAG+jC,EAAS/jC,IAInCkG,EAAW,SAAUrC,GACpB,OAAO,WACDqC,IACJA,EAAWggC,EAAgBP,EAAIW,OAC9BX,EAAIY,QAAUZ,EAAIa,QAAUb,EAAIc,UAC/Bd,EAAIe,mBAAqB,KAEb,UAAT7iC,EACJ8hC,EAAInC,QACgB,UAAT3/B,EAKgB,iBAAf8hC,EAAIpC,OACfhK,EAAU,EAAG,SAEbA,EAGCoM,EAAIpC,OACJoC,EAAIlC,YAINlK,EACCsM,GAAkBF,EAAIpC,SAAYoC,EAAIpC,OACtCoC,EAAIlC,WAK+B,UAAjCkC,EAAIgB,cAAgB,SACM,iBAArBhB,EAAIiB,aACV,CAAEC,OAAQlB,EAAIrB,UACd,CAAE9/B,KAAMmhC,EAAIiB,cACbjB,EAAIxC,4BAQTwC,EAAIW,OAASpgC,IACbggC,EAAgBP,EAAIY,QAAUZ,EAAIc,UAAYvgC,EAAU,cAKnCuB,IAAhBk+B,EAAIa,QACRb,EAAIa,QAAUN,EAEdP,EAAIe,mBAAqB,WAGA,IAAnBf,EAAIzkB,YAMR9e,EAAOyd,YAAY,WACb3Z,GACJggC,QAQLhgC,EAAWA,EAAU,SAErB,IAGCy/B,EAAIzB,KAAMl9B,EAAQ68B,YAAc78B,EAAQub,MAAQ,MAC/C,MAAQpgB,GAGT,GAAK+D,EACJ,MAAM/D,IAKTqhC,MAAO,WACDt9B,GACJA,SAWLlB,EAAOg9B,eAAe,SAAUjgC,GAC1BA,EAAE4hC,cACN5hC,EAAEiZ,SAAS1W,QAAS,MAKtBU,EAAO88B,UAAW,CACjBJ,QAAS,CACRp9B,OAAQ,6FAGT0W,SAAU,CACT1W,OAAQ,2BAETu9B,WAAY,CACX,cAAe,SAAUr9B,GAExB,OADAQ,EAAOqD,WAAY7D,GACZA,MAMVQ,EAAOg9B,cAAe,UAAU,SAAUjgC,QACxB0F,IAAZ1F,EAAEkN,QACNlN,EAAEkN,OAAQ,GAENlN,EAAE4hC,cACN5hC,EAAE8B,KAAO,UAKXmB,EAAOi9B,cAAe,UAAU,SAAUlgC,GAIxC,IAAIuC,EAAQ4B,EADb,GAAKnE,EAAE4hC,aAAe5hC,EAAE+kC,YAEvB,MAAO,CACN5C,KAAM,SAAU13B,EAAG+sB,GAClBj1B,EAASU,EAAQ,YACf6N,KAAM9Q,EAAE+kC,aAAe,IACvBtkB,KAAM,CAAEukB,QAAShlC,EAAEilC,cAAeljC,IAAK/B,EAAEq/B,MACzC5Y,GAAI,aAActiB,EAAW,SAAU+gC,GACvC3iC,EAAOqZ,SACPzX,EAAW,KACN+gC,GACJ1N,EAAuB,UAAb0N,EAAIpjC,KAAmB,IAAM,IAAKojC,EAAIpjC,QAKnDtB,EAASoC,KAAKC,YAAaN,EAAQ,KAEpCk/B,MAAO,WACDt9B,GACJA,SAUL,IAqGKsf,GArGD0hB,GAAe,GAClBC,GAAS,oBAGVniC,EAAO88B,UAAW,CACjBsF,MAAO,WACPC,cAAe,WACd,IAAInhC,EAAWghC,GAAal8B,OAAWhG,EAAO0C,QAAU,IAAQ3D,KAEhE,OADA9B,KAAMiE,IAAa,EACZA,KAKTlB,EAAOg9B,cAAe,cAAc,SAAUjgC,EAAGulC,EAAkB9G,GAElE,IAAI+G,EAAcC,EAAaC,EAC9BC,GAAuB,IAAZ3lC,EAAEqlC,QAAqBD,GAAO14B,KAAM1M,EAAEq/B,KAChD,MACkB,iBAAXr/B,EAAEwgB,MAE6C,KADnDxgB,EAAE0/B,aAAe,IACjBx+B,QAAS,sCACXkkC,GAAO14B,KAAM1M,EAAEwgB,OAAU,QAI5B,GAAKmlB,GAAiC,UAArB3lC,EAAEu+B,UAAW,GA8D7B,OA3DAiH,EAAexlC,EAAEslC,cAAgB7jC,EAAYzB,EAAEslC,eAC9CtlC,EAAEslC,gBACFtlC,EAAEslC,cAGEK,EACJ3lC,EAAG2lC,GAAa3lC,EAAG2lC,GAAW7/B,QAASs/B,GAAQ,KAAOI,IAC/B,IAAZxlC,EAAEqlC,QACbrlC,EAAEq/B,MAAS1C,GAAOjwB,KAAM1M,EAAEq/B,KAAQ,IAAM,KAAQr/B,EAAEqlC,MAAQ,IAAMG,GAIjExlC,EAAE8/B,WAAY,eAAkB,WAI/B,OAHM4F,GACLziC,EAAO+C,MAAOw/B,EAAe,mBAEvBE,EAAmB,IAI3B1lC,EAAEu+B,UAAW,GAAM,OAGnBkH,EAAcplC,EAAQmlC,GACtBnlC,EAAQmlC,GAAiB,WACxBE,EAAoBnhC,WAIrBk6B,EAAMpiB,QAAQ,gBAGQ3W,IAAhB+/B,EACJxiC,EAAQ5C,GAASw6B,WAAY2K,GAI7BnlC,EAAQmlC,GAAiBC,EAIrBzlC,EAAGwlC,KAGPxlC,EAAEslC,cAAgBC,EAAiBD,cAGnCH,GAAalkC,KAAMukC,IAIfE,GAAqBjkC,EAAYgkC,IACrCA,EAAaC,EAAmB,IAGjCA,EAAoBD,OAAc//B,KAI5B,YAYTlE,EAAQokC,qBACHniB,GAAOjjB,EAASqlC,eAAeD,mBAAoB,IAAKniB,MACvD9T,UAAY,6BACiB,IAA3B8T,GAAK9X,WAAWnI,QAQxBP,EAAO2V,UAAY,SAAU4H,EAAMrd,EAAS2iC,GAC3C,MAAqB,iBAATtlB,EACJ,IAEgB,kBAAZrd,IACX2iC,EAAc3iC,EACdA,GAAU,GAKLA,IAIA3B,EAAQokC,qBAMZhxB,GALAzR,EAAU3C,EAASqlC,eAAeD,mBAAoB,KAKvCpjC,cAAe,SACzBsR,KAAOtT,EAASmT,SAASG,KAC9B3Q,EAAQP,KAAKC,YAAa+R,IAE1BzR,EAAU3C,GAKZ4kB,GAAW0gB,GAAe,IAD1BC,EAAS1tB,EAAWjM,KAAMoU,IAKlB,CAAErd,EAAQX,cAAeujC,EAAQ,MAGzCA,EAAS5gB,GAAe,CAAE3E,GAAQrd,EAASiiB,GAEtCA,GAAWA,EAAQ5hB,QACvBP,EAAQmiB,GAAUxJ,SAGZ3Y,EAAOe,MAAO,GAAI+hC,EAAOp6B,cAlChC,IAAIiJ,EAAMmxB,EAAQ3gB,GAyCnBniB,EAAOG,GAAGwmB,KAAO,SAAUyV,EAAK2G,EAAQ7hC,GACvC,IAAIjB,EAAUpB,EAAMygC,EACnB9pB,EAAOvY,KACP4mB,EAAMuY,EAAIn+B,QAAS,KAsDpB,OApDK4lB,GAAO,IACX5jB,EAAWw3B,GAAkB2E,EAAIt+B,MAAO+lB,IACxCuY,EAAMA,EAAIt+B,MAAO,EAAG+lB,IAIhBrlB,EAAYukC,IAGhB7hC,EAAW6hC,EACXA,OAAStgC,GAGEsgC,GAA4B,iBAAXA,IAC5BlkC,EAAO,QAIH2W,EAAKjV,OAAS,GAClBP,EAAOk9B,KAAM,CACZd,IAAKA,EAKLv9B,KAAMA,GAAQ,MACdw8B,SAAU,OACV9d,KAAMwlB,IACHx9B,MAAM,SAAUq8B,GAGnBtC,EAAWh+B,UAEXkU,EAAKqV,KAAM5qB,EAIVD,EAAQ,SAAUurB,OAAQvrB,EAAO2V,UAAWisB,IAAiBt1B,KAAMrM,GAGnE2hC,MAKExoB,OAAQlY,GAAY,SAAUs6B,EAAO+C,GACxC/oB,EAAKvU,MAAM,WACVC,EAASG,MAAOpE,KAAMqiC,GAAY,CAAE9D,EAAMoG,aAAcrD,EAAQ/C,SAK5Dv+B,MAOR+C,EAAOiB,KAAM,CACZ,YACA,WACA,eACA,YACA,cACA,aACE,SAAUjG,EAAG6D,GACfmB,EAAOG,GAAItB,GAAS,SAAUsB,GAC7B,OAAOlD,KAAKumB,GAAI3kB,EAAMsB,OAOxBH,EAAO2N,KAAKpH,QAAQy8B,SAAW,SAAU5hC,GACxC,OAAOpB,EAAO2D,KAAM3D,EAAO+1B,QAAQ,SAAU51B,GAC5C,OAAOiB,IAASjB,EAAGiB,QAChBb,QAMLP,EAAOijC,OAAS,CACfC,UAAW,SAAU9hC,EAAMY,EAAShH,GACnC,IAAImoC,EAAaC,EAASC,EAAWC,EAAQC,EAAWC,EACvD1V,EAAW9tB,EAAOwf,IAAKpe,EAAM,YAC7BqiC,EAAUzjC,EAAQoB,GAClB4lB,EAAQ,GAGS,WAAb8G,IACJ1sB,EAAKke,MAAMwO,SAAW,YAGvByV,EAAYE,EAAQR,SACpBI,EAAYrjC,EAAOwf,IAAKpe,EAAM,OAC9BoiC,EAAaxjC,EAAOwf,IAAKpe,EAAM,SACI,aAAb0sB,GAAwC,UAAbA,KAC9CuV,EAAYG,GAAavlC,QAAS,SAAY,GAMhDqlC,GADAH,EAAcM,EAAQ3V,YACDjiB,IACrBu3B,EAAUD,EAAYhS,OAGtBmS,EAASnV,WAAYkV,IAAe,EACpCD,EAAUjV,WAAYqV,IAAgB,GAGlChlC,EAAYwD,KAGhBA,EAAUA,EAAQ7G,KAAMiG,EAAMpG,EAAGgF,EAAO+B,OAAQ,GAAIwhC,KAGjC,MAAfvhC,EAAQ6J,MACZmb,EAAMnb,IAAQ7J,EAAQ6J,IAAM03B,EAAU13B,IAAQy3B,GAE1B,MAAhBthC,EAAQmvB,OACZnK,EAAMmK,KAASnvB,EAAQmvB,KAAOoS,EAAUpS,KAASiS,GAG7C,UAAWphC,EACfA,EAAQ0hC,MAAMvoC,KAAMiG,EAAM4lB,GAG1Byc,EAAQjkB,IAAKwH,KAKhBhnB,EAAOG,GAAG4B,OAAQ,CAGjBkhC,OAAQ,SAAUjhC,GAGjB,GAAKV,UAAUf,OACd,YAAmBkC,IAAZT,EACN/E,KACAA,KAAKgE,MAAM,SAAUjG,GACpBgF,EAAOijC,OAAOC,UAAWjmC,KAAM+E,EAAShH,MAI3C,IAAI2oC,EAAMC,EACTxiC,EAAOnE,KAAM,GAEd,OAAMmE,EAQAA,EAAKovB,iBAAiBjwB,QAK5BojC,EAAOviC,EAAK6vB,wBACZ2S,EAAMxiC,EAAK8H,cAAc0C,YAClB,CACNC,IAAK83B,EAAK93B,IAAM+3B,EAAIC,YACpB1S,KAAMwS,EAAKxS,KAAOyS,EAAIE,cARf,CAAEj4B,IAAK,EAAGslB,KAAM,QATxB,GAuBDrD,SAAU,WACT,GAAM7wB,KAAM,GAAZ,CAIA,IAAI8mC,EAAcd,EAAQ7jC,EACzBgC,EAAOnE,KAAM,GACb+mC,EAAe,CAAEn4B,IAAK,EAAGslB,KAAM,GAGhC,GAAwC,UAAnCnxB,EAAOwf,IAAKpe,EAAM,YAGtB6hC,EAAS7hC,EAAK6vB,4BAER,CAON,IANAgS,EAAShmC,KAAKgmC,SAId7jC,EAAMgC,EAAK8H,cACX66B,EAAe3iC,EAAK2iC,cAAgB3kC,EAAIqM,gBAChCs4B,IACLA,IAAiB3kC,EAAIohB,MAAQujB,IAAiB3kC,EAAIqM,kBACT,WAA3CzL,EAAOwf,IAAKukB,EAAc,aAE1BA,EAAeA,EAAalkC,WAExBkkC,GAAgBA,IAAiB3iC,GAAkC,IAA1B2iC,EAAarlC,YAG1DslC,EAAehkC,EAAQ+jC,GAAed,UACzBp3B,KAAO7L,EAAOwf,IAAKukB,EAAc,kBAAkB,GAChEC,EAAa7S,MAAQnxB,EAAOwf,IAAKukB,EAAc,mBAAmB,IAKpE,MAAO,CACNl4B,IAAKo3B,EAAOp3B,IAAMm4B,EAAan4B,IAAM7L,EAAOwf,IAAKpe,EAAM,aAAa,GACpE+vB,KAAM8R,EAAO9R,KAAO6S,EAAa7S,KAAOnxB,EAAOwf,IAAKpe,EAAM,cAAc,MAc1E2iC,aAAc,WACb,OAAO9mC,KAAKkE,KAAK,WAGhB,IAFA,IAAI4iC,EAAe9mC,KAAK8mC,aAEhBA,GAA2D,WAA3C/jC,EAAOwf,IAAKukB,EAAc,aACjDA,EAAeA,EAAaA,aAG7B,OAAOA,GAAgBt4B,SAM1BzL,EAAOiB,KAAM,CAAEoxB,WAAY,cAAeD,UAAW,gBAAiB,SAAUza,EAAQ6F,GACvF,IAAI3R,EAAM,gBAAkB2R,EAE5Bxd,EAAOG,GAAIwX,GAAW,SAAUtY,GAC/B,OAAO+c,EAAQnf,MAAM,SAAUmE,EAAMuW,EAAQtY,GAG5C,IAAIukC,EAOJ,GANKjlC,EAAUyC,GACdwiC,EAAMxiC,EACuB,IAAlBA,EAAK1C,WAChBklC,EAAMxiC,EAAKwK,kBAGCnJ,IAARpD,EACJ,OAAOukC,EAAMA,EAAKpmB,GAASpc,EAAMuW,GAG7BisB,EACJA,EAAIK,SACFp4B,EAAY+3B,EAAIE,YAAVzkC,EACPwM,EAAMxM,EAAMukC,EAAIC,aAIjBziC,EAAMuW,GAAWtY,IAEhBsY,EAAQtY,EAAKiC,UAAUf,YAU5BP,EAAOiB,KAAM,CAAE,MAAO,SAAU,SAAUjG,EAAGwiB,GAC5Cxd,EAAO2wB,SAAUnT,GAASwP,GAAczuB,EAAQgwB,eAC/C,SAAUntB,EAAMsrB,GACf,GAAKA,EAIJ,OAHAA,EAAWD,GAAQrrB,EAAMoc,GAGlB4O,GAAU3iB,KAAMijB,GACtB1sB,EAAQoB,GAAO0sB,WAAYtQ,GAAS,KACpCkP,QAQL1sB,EAAOiB,KAAM,CAAEijC,OAAQ,SAAUC,MAAO,UAAW,SAAU5oC,EAAMsD,GAClEmB,EAAOiB,KAAM,CAAEowB,QAAS,QAAU91B,EAAMyb,QAASnY,EAAM,GAAI,QAAUtD,IACpE,SAAU6oC,EAAcC,GAGxBrkC,EAAOG,GAAIkkC,GAAa,SAAUjT,EAAQn1B,GACzC,IAAIogB,EAAY/a,UAAUf,SAAY6jC,GAAkC,kBAAXhT,GAC5DlB,EAAQkU,KAA6B,IAAXhT,IAA6B,IAAVn1B,EAAiB,SAAW,UAE1E,OAAOmgB,EAAQnf,MAAM,SAAUmE,EAAMvC,EAAM5C,GAC1C,IAAImD,EAEJ,OAAKT,EAAUyC,GAGyB,IAAhCijC,EAASpmC,QAAS,SACxBmD,EAAM,QAAU7F,GAChB6F,EAAK7D,SAASkO,gBAAiB,SAAWlQ,GAIrB,IAAlB6F,EAAK1C,UACTU,EAAMgC,EAAKqK,gBAIJ9I,KAAKgtB,IACXvuB,EAAKof,KAAM,SAAWjlB,GAAQ6D,EAAK,SAAW7D,GAC9C6F,EAAKof,KAAM,SAAWjlB,GAAQ6D,EAAK,SAAW7D,GAC9C6D,EAAK,SAAW7D,UAIDkH,IAAVxG,EAGN+D,EAAOwf,IAAKpe,EAAMvC,EAAMqxB,GAGxBlwB,EAAOsf,MAAOle,EAAMvC,EAAM5C,EAAOi0B,KAChCrxB,EAAMwd,EAAY+U,OAAS3uB,EAAW4Z,UAM5Crc,EAAOiB,KAAM,wLAEgDiD,MAAO,MACnE,SAAUlJ,EAAGO,GAGbyE,EAAOG,GAAI5E,GAAS,SAAUgiB,EAAMpd,GACnC,OAAOmB,UAAUf,OAAS,EACzBtD,KAAKumB,GAAIjoB,EAAM,KAAMgiB,EAAMpd,GAC3BlD,KAAKqnB,QAAS/oB,OAIjByE,EAAOG,GAAG4B,OAAQ,CACjBuiC,MAAO,SAAUC,EAAQC,GACxB,OAAOvnC,KAAKmsB,WAAYmb,GAASlb,WAAYmb,GAASD,MAOxDvkC,EAAOG,GAAG4B,OAAQ,CAEjBvF,KAAM,SAAUinB,EAAOlG,EAAMpd,GAC5B,OAAOlD,KAAKumB,GAAIC,EAAO,KAAMlG,EAAMpd,IAEpCskC,OAAQ,SAAUhhB,EAAOtjB,GACxB,OAAOlD,KAAK4mB,IAAKJ,EAAO,KAAMtjB,IAG/BukC,SAAU,SAAUzkC,EAAUwjB,EAAOlG,EAAMpd,GAC1C,OAAOlD,KAAKumB,GAAIC,EAAOxjB,EAAUsd,EAAMpd,IAExCwkC,WAAY,SAAU1kC,EAAUwjB,EAAOtjB,GAGtC,OAA4B,IAArBmB,UAAUf,OAChBtD,KAAK4mB,IAAK5jB,EAAU,MACpBhD,KAAK4mB,IAAKJ,EAAOxjB,GAAY,KAAME,MAQtCH,EAAO4kC,MAAQ,SAAUzkC,EAAID,GAC5B,IAAIuM,EAAKwD,EAAM20B,EAUf,GARwB,iBAAZ1kC,IACXuM,EAAMtM,EAAID,GACVA,EAAUC,EACVA,EAAKsM,GAKAjO,EAAY2B,GAalB,OARA8P,EAAOnS,EAAM3C,KAAMmG,UAAW,IAC9BsjC,EAAQ,WACP,OAAOzkC,EAAGkB,MAAOnB,GAAWjD,KAAMgT,EAAKlS,OAAQD,EAAM3C,KAAMmG,eAItD0C,KAAO7D,EAAG6D,KAAO7D,EAAG6D,MAAQhE,EAAOgE,OAElC4gC,GAGR5kC,EAAO6kC,UAAY,SAAUC,GACvBA,EACJ9kC,EAAOgc,YAEPhc,EAAO4V,OAAO,IAGhB5V,EAAOwC,QAAUD,MAAMC,QACvBxC,EAAO+kC,UAAYhnB,KAAKC,MACxBhe,EAAOuI,SAAWA,EAClBvI,EAAOxB,WAAaA,EACpBwB,EAAOrB,SAAWA,EAClBqB,EAAO+c,UAAYA,EACnB/c,EAAOnB,KAAOkB,EAEdC,EAAOqnB,IAAMjiB,KAAKiiB,IAElBrnB,EAAOglC,UAAY,SAAUvmC,GAK5B,IAAII,EAAOmB,EAAOnB,KAAMJ,GACxB,OAAkB,WAATI,GAA8B,WAATA,KAK5BomC,MAAOxmC,EAAM0vB,WAAY1vB,UAsB1B,KAFqB,EAAF,WACnB,OAAOuB,GACP,QAFiB,OAEjB,aAMF,IAGCklC,GAAU9nC,EAAO4C,OAGjBmlC,GAAK/nC,EAAOgoC,EAwBb,OAtBAplC,EAAOqlC,WAAa,SAAUhjC,GAS7B,OARKjF,EAAOgoC,IAAMplC,IACjB5C,EAAOgoC,EAAID,IAGP9iC,GAAQjF,EAAO4C,SAAWA,IAC9B5C,EAAO4C,OAASklC,IAGVllC,GAMFtC,IACLN,EAAO4C,OAAS5C,EAAOgoC,EAAIplC,GAMrBA,M,kCCp2UP,YAaA,MAAMslC,EACF7kC,YAAY8kC,GAER,GAAa,uBAATA,EACAtoC,KAAKuoC,eACgC,IAAjC9pC,OAAOsO,KAAK/M,KAAKwoC,KAAKllC,QAEtBtD,KAAKyoC,YAAY7e,QAErB5pB,KAAK0oC,OAAOniB,GAAG,QAASvmB,KAAK2oC,WAAWppC,KAAKS,OAC7CG,OAAO0O,iBAAiB,UAAW7O,KAAK4oC,iBAAiBrpC,KAAKS,OAAO,QAClE,GAAa,wBAATsoC,EAAgC,CACvC,IAAIhoB,EAAO6nB,EAAE,gBACT1pB,EAAU,CACVoqB,KAAMV,EAAE7nB,GAAMA,KAAK,SAEnB6nB,EAAE7nB,GAAMA,KAAK,WACb7B,EAAQ3Y,MAAQqiC,EAAE7nB,GAAMA,KAAK,UAGjCngB,OAAOwS,OAAOm2B,YAAYhoB,KAAKioB,UAAUtqB,GAAU4pB,EAAiBW,cAU5EJ,iBAAiBjiB,GAEb,GAAIA,EAAMsiB,SAAWZ,EAAiBW,YAElC,OAEJ,IAAI1oB,EAAOQ,KAAKC,MAAM4F,EAAMrG,WACF,IAAfA,EAAKxa,MACZ9F,KAAK4e,UAAU0B,EAAKuoB,MAEpB7oC,KAAKkpC,OAAO5oB,EAAKuoB,KAAMvoB,EAAKxa,OAGK,IAAjCrH,OAAOsO,KAAK/M,KAAKwoC,KAAKllC,QACD,IAAjBtD,KAAKmpC,SAELnpC,KAAKyoC,YAAY7e,QAa7BhL,UAAUxS,QAEsB,IAAjBpM,KAAKwoC,IAAIp8B,KAIpBpM,KAAKwoC,IAAIp8B,GAAIg9B,KAAKhO,YAAY,WAC9Bp7B,KAAKwoC,IAAIp8B,GAAIg9B,KAAKhO,YAAY,qBAC9Bp7B,KAAKwoC,IAAIp8B,GAAIg9B,KAAKrO,SAAS,mBAC3B/6B,KAAKwoC,IAAIp8B,GAAIi9B,QAAQzlB,gBACd5jB,KAAKwoC,IAAIp8B,GAChBpM,KAAK44B,UAYTsQ,OAAO98B,EAAIk9B,QAEqB,IAAjBtpC,KAAKwoC,IAAIp8B,KAIpBpM,KAAKwoC,IAAIp8B,GAAIi9B,QAAQtO,SAAS,SAC9BoN,EAAEnoC,KAAKwoC,IAAIp8B,GAAIg9B,MAAMhO,YAAY,6BACjC+M,EAAEnoC,KAAKwoC,IAAIp8B,GAAIg9B,MAAMrO,SAAS,yBAE1B/6B,KAAKupC,OAAO9N,SAAS,WACrBz7B,KAAKupC,OAAOnO,YAAY,UAExBp7B,KAAKwpC,OAAO/N,SAAS,WACrBz7B,KAAKwpC,OAAOpO,YAAY,iBAGrBp7B,KAAKwoC,IAAIp8B,GAChBpM,KAAKmpC,UACLnpC,KAAK44B,UAYTA,SAEQn6B,OAAOsO,KAAK/M,KAAKwoC,KAAKllC,OAAS,SAIP,IAAjBtD,KAAK05B,SACZC,aAAa35B,KAAK05B,SAGlB15B,KAAKmpC,QAAU,GACfnpC,KAAKupC,OAAOnO,YAAY,UACxBp7B,KAAKwpC,OAAOpO,YAAY,UACxBp7B,KAAKypC,QAAQ1O,SAAS,WAEtB/6B,KAAKyoC,YAAY7e,SAQzB,mBAEI,IAAIqf,EAAS9oC,OAAOsT,SAASw1B,OAM7B,OALKA,IAEDA,EAAS9oC,OAAOsT,SAAS4rB,SAAW,KAAOl/B,OAAOsT,SAASi2B,UACtDvpC,OAAOsT,SAASk2B,KAAO,IAAMxpC,OAAOsT,SAASk2B,KAAO,KAEtDV,EASXN,WAAWhiB,GAEPA,EAAMS,iBAENpnB,KAAK0oC,OAAOnoB,KAAK,YAAY,GAC7BvgB,KAAK4pC,UAAUrpB,KAAK,YAAY,GAChC9hB,OAAOsO,KAAK/M,KAAKwoC,KAAKqB,QAAS,SAAUz9B,GACrCpM,KAAKwoC,IAAIp8B,GAAIk1B,OAAS,aACtBthC,KAAKwoC,IAAIp8B,GAAI0qB,WAAa,IAAI3uB,MAAQ2hC,UACtC9pC,KAAKwoC,IAAIp8B,GAAI29B,OAAOn5B,KAAK,MAAO5Q,KAAKwoC,IAAIp8B,GAAI29B,OAAOzpB,KAAK,QACzDtgB,KAAKwoC,IAAIp8B,GAAIg9B,KAAKrO,SAAS,YAC5Bx7B,KAAKS,OACRA,KAAKgqC,cASTA,cAEI,IAAItQ,EAAU,GAEd,IAAK,MAAMttB,KAAMpM,KAAKwoC,IAAK,CACvB,QAAkB,IAAPp8B,EACP,SAEJ,IAAKpM,KAAKwoC,IAAI5oC,eAAewM,GACzB,SAEJ,GAA4B,eAAxBpM,KAAKwoC,IAAIp8B,GAAIk1B,OACb,SAEJ,IAAIlX,IAAQ,IAAIjiB,MAAQ2hC,UAAY9pC,KAAKwoC,IAAIp8B,GAAI0qB,WAAa,IAE1D92B,KAAKwoC,IAAIp8B,GAAIstB,SAAWtP,EACxBpqB,KAAKkpC,OAAO98B,EAAI,YAAajM,OAAOG,UAG/BN,KAAKwoC,IAAIp8B,GAAIstB,QAAUtP,EAAOsP,IAC/BA,EAAU15B,KAAKwoC,IAAIp8B,GAAIstB,QAAUtP,GAKzC3rB,OAAOsO,KAAK/M,KAAKwoC,KAAKllC,OAAS,EAE/BtD,KAAK05B,QAAU9b,WAAW5d,KAAKgqC,YAAYzqC,KAAKS,MAAiB,IAAV05B,GAEvD15B,KAAK44B,SAQb2P,eAEIvoC,KAAKwoC,IAAM,GACXxoC,KAAK0oC,OAASP,EAAE,wBAChBnoC,KAAK4pC,UAAYzB,EAAE,2BACnBnoC,KAAKyoC,YAAcN,EAAE,6BACrBnoC,KAAKypC,QAAUtB,EAAE,8BACjBnoC,KAAKupC,OAASpB,EAAE,2BAChBnoC,KAAKwpC,OAASrB,EAAE,yBAChBnoC,KAAKmpC,QAAU,EACf,IAAIjsB,EAAOld,KAGXmoC,EAAE,iBAAiBnkC,MAAK,WACpB,IAAIoI,EAAK+7B,EAAEnoC,MAAMsgB,KAAK,MAClBypB,EAAS5B,EAAE,qBAAuB/7B,EAAK,MACvCk1B,EAAS6G,EAAEnoC,MAAMsgB,KAAK,UAE1B,OAAQghB,GACJ,IAAK,SACDpkB,EAAKisB,UACT,IAAK,YACD,OAGRjsB,EAAKsrB,IAAIp8B,GAAM,CACXk1B,OAAQA,EACR5H,QAASyO,EAAEnoC,MAAMsgB,KAAK,WACtB+oB,QAASlB,EAAEnoC,MACX+pC,OAAQA,EACRX,KAAMjB,EAAE,cAAgB/7B,EAAK,WAM9Bi8B,Q,oDCrQf,8BAEAF,EAAE7nC,UAAUqY,OAAM,WACd,IAAI0vB,IAAiBF,EAAE,QAAQv3B,KAAK,W", "file": "js/logout.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 16);\n", "module.exports = global[\"jQuery\"] = require(\"-!/tmp/simplesamlphp-1.18.3/node_modules/expose-loader/index.js?$!./jquery.js\");", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "module.exports = global[\"$\"] = require(\"-!./jquery.js\");", "/*!\n * jQuery JavaScript Library v3.4.1\n * https://jquery.com/\n *\n * Includes Sizzle.js\n * https://sizzlejs.com/\n *\n * Copyright JS Foundation and other contributors\n * Released under the MIT license\n * https://jquery.org/license\n *\n * Date: 2019-05-01T21:04Z\n */\n( function( global, factory ) {\n\n\t\"use strict\";\n\n\tif ( typeof module === \"object\" && typeof module.exports === \"object\" ) {\n\n\t\t// For CommonJS and CommonJS-like environments where a proper `window`\n\t\t// is present, execute the factory and get jQuery.\n\t\t// For environments that do not have a `window` with a `document`\n\t\t// (such as Node.js), expose a factory as module.exports.\n\t\t// This accentuates the need for the creation of a real `window`.\n\t\t// e.g. var jQuery = require(\"jquery\")(window);\n\t\t// See ticket #14549 for more info.\n\t\tmodule.exports = global.document ?\n\t\t\tfactory( global, true ) :\n\t\t\tfunction( w ) {\n\t\t\t\tif ( !w.document ) {\n\t\t\t\t\tthrow new Error( \"jQuery requires a window with a document\" );\n\t\t\t\t}\n\t\t\t\treturn factory( w );\n\t\t\t};\n\t} else {\n\t\tfactory( global );\n\t}\n\n// Pass this if window is not defined yet\n} )( typeof window !== \"undefined\" ? window : this, function( window, noGlobal ) {\n\n// Edge <= 12 - 13+, Firefox <=18 - 45+, IE 10 - 11, Safari 5.1 - 9+, iOS 6 - 9.1\n// throw exceptions when non-strict code (e.g., ASP.NET 4.5) accesses strict mode\n// arguments.callee.caller (trac-13335). But as of jQuery 3.0 (2016), strict mode should be common\n// enough that all such attempts are guarded in a try block.\n\"use strict\";\n\nvar arr = [];\n\nvar document = window.document;\n\nvar getProto = Object.getPrototypeOf;\n\nvar slice = arr.slice;\n\nvar concat = arr.concat;\n\nvar push = arr.push;\n\nvar indexOf = arr.indexOf;\n\nvar class2type = {};\n\nvar toString = class2type.toString;\n\nvar hasOwn = class2type.hasOwnProperty;\n\nvar fnToString = hasOwn.toString;\n\nvar ObjectFunctionString = fnToString.call( Object );\n\nvar support = {};\n\nvar isFunction = function isFunction( obj ) {\n\n      // Support: Chrome <=57, Firefox <=52\n      // In some browsers, typeof returns \"function\" for HTML <object> elements\n      // (i.e., `typeof document.createElement( \"object\" ) === \"function\"`).\n      // We don't want to classify *any* DOM node as a function.\n      return typeof obj === \"function\" && typeof obj.nodeType !== \"number\";\n  };\n\n\nvar isWindow = function isWindow( obj ) {\n\t\treturn obj != null && obj === obj.window;\n\t};\n\n\n\n\n\tvar preservedScriptAttributes = {\n\t\ttype: true,\n\t\tsrc: true,\n\t\tnonce: true,\n\t\tnoModule: true\n\t};\n\n\tfunction DOMEval( code, node, doc ) {\n\t\tdoc = doc || document;\n\n\t\tvar i, val,\n\t\t\tscript = doc.createElement( \"script\" );\n\n\t\tscript.text = code;\n\t\tif ( node ) {\n\t\t\tfor ( i in preservedScriptAttributes ) {\n\n\t\t\t\t// Support: Firefox 64+, Edge 18+\n\t\t\t\t// Some browsers don't support the \"nonce\" property on scripts.\n\t\t\t\t// On the other hand, just using `getAttribute` is not enough as\n\t\t\t\t// the `nonce` attribute is reset to an empty string whenever it\n\t\t\t\t// becomes browsing-context connected.\n\t\t\t\t// See https://github.com/whatwg/html/issues/2369\n\t\t\t\t// See https://html.spec.whatwg.org/#nonce-attributes\n\t\t\t\t// The `node.getAttribute` check was added for the sake of\n\t\t\t\t// `jQuery.globalEval` so that it can fake a nonce-containing node\n\t\t\t\t// via an object.\n\t\t\t\tval = node[ i ] || node.getAttribute && node.getAttribute( i );\n\t\t\t\tif ( val ) {\n\t\t\t\t\tscript.setAttribute( i, val );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tdoc.head.appendChild( script ).parentNode.removeChild( script );\n\t}\n\n\nfunction toType( obj ) {\n\tif ( obj == null ) {\n\t\treturn obj + \"\";\n\t}\n\n\t// Support: Android <=2.3 only (functionish RegExp)\n\treturn typeof obj === \"object\" || typeof obj === \"function\" ?\n\t\tclass2type[ toString.call( obj ) ] || \"object\" :\n\t\ttypeof obj;\n}\n/* global Symbol */\n// Defining this global in .eslintrc.json would create a danger of using the global\n// unguarded in another place, it seems safer to define global only for this module\n\n\n\nvar\n\tversion = \"3.4.1\",\n\n\t// Define a local copy of jQuery\n\tjQuery = function( selector, context ) {\n\n\t\t// The jQuery object is actually just the init constructor 'enhanced'\n\t\t// Need init if jQuery is called (just allow error to be thrown if not included)\n\t\treturn new jQuery.fn.init( selector, context );\n\t},\n\n\t// Support: Android <=4.0 only\n\t// Make sure we trim BOM and NBSP\n\trtrim = /^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g;\n\njQuery.fn = jQuery.prototype = {\n\n\t// The current version of jQuery being used\n\tjquery: version,\n\n\tconstructor: jQuery,\n\n\t// The default length of a jQuery object is 0\n\tlength: 0,\n\n\ttoArray: function() {\n\t\treturn slice.call( this );\n\t},\n\n\t// Get the Nth element in the matched element set OR\n\t// Get the whole matched element set as a clean array\n\tget: function( num ) {\n\n\t\t// Return all the elements in a clean array\n\t\tif ( num == null ) {\n\t\t\treturn slice.call( this );\n\t\t}\n\n\t\t// Return just the one element from the set\n\t\treturn num < 0 ? this[ num + this.length ] : this[ num ];\n\t},\n\n\t// Take an array of elements and push it onto the stack\n\t// (returning the new matched element set)\n\tpushStack: function( elems ) {\n\n\t\t// Build a new jQuery matched element set\n\t\tvar ret = jQuery.merge( this.constructor(), elems );\n\n\t\t// Add the old object onto the stack (as a reference)\n\t\tret.prevObject = this;\n\n\t\t// Return the newly-formed element set\n\t\treturn ret;\n\t},\n\n\t// Execute a callback for every element in the matched set.\n\teach: function( callback ) {\n\t\treturn jQuery.each( this, callback );\n\t},\n\n\tmap: function( callback ) {\n\t\treturn this.pushStack( jQuery.map( this, function( elem, i ) {\n\t\t\treturn callback.call( elem, i, elem );\n\t\t} ) );\n\t},\n\n\tslice: function() {\n\t\treturn this.pushStack( slice.apply( this, arguments ) );\n\t},\n\n\tfirst: function() {\n\t\treturn this.eq( 0 );\n\t},\n\n\tlast: function() {\n\t\treturn this.eq( -1 );\n\t},\n\n\teq: function( i ) {\n\t\tvar len = this.length,\n\t\t\tj = +i + ( i < 0 ? len : 0 );\n\t\treturn this.pushStack( j >= 0 && j < len ? [ this[ j ] ] : [] );\n\t},\n\n\tend: function() {\n\t\treturn this.prevObject || this.constructor();\n\t},\n\n\t// For internal use only.\n\t// Behaves like an Array's method, not like a jQuery method.\n\tpush: push,\n\tsort: arr.sort,\n\tsplice: arr.splice\n};\n\njQuery.extend = jQuery.fn.extend = function() {\n\tvar options, name, src, copy, copyIsArray, clone,\n\t\ttarget = arguments[ 0 ] || {},\n\t\ti = 1,\n\t\tlength = arguments.length,\n\t\tdeep = false;\n\n\t// Handle a deep copy situation\n\tif ( typeof target === \"boolean\" ) {\n\t\tdeep = target;\n\n\t\t// Skip the boolean and the target\n\t\ttarget = arguments[ i ] || {};\n\t\ti++;\n\t}\n\n\t// Handle case when target is a string or something (possible in deep copy)\n\tif ( typeof target !== \"object\" && !isFunction( target ) ) {\n\t\ttarget = {};\n\t}\n\n\t// Extend jQuery itself if only one argument is passed\n\tif ( i === length ) {\n\t\ttarget = this;\n\t\ti--;\n\t}\n\n\tfor ( ; i < length; i++ ) {\n\n\t\t// Only deal with non-null/undefined values\n\t\tif ( ( options = arguments[ i ] ) != null ) {\n\n\t\t\t// Extend the base object\n\t\t\tfor ( name in options ) {\n\t\t\t\tcopy = options[ name ];\n\n\t\t\t\t// Prevent Object.prototype pollution\n\t\t\t\t// Prevent never-ending loop\n\t\t\t\tif ( name === \"__proto__\" || target === copy ) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Recurse if we're merging plain objects or arrays\n\t\t\t\tif ( deep && copy && ( jQuery.isPlainObject( copy ) ||\n\t\t\t\t\t( copyIsArray = Array.isArray( copy ) ) ) ) {\n\t\t\t\t\tsrc = target[ name ];\n\n\t\t\t\t\t// Ensure proper type for the source value\n\t\t\t\t\tif ( copyIsArray && !Array.isArray( src ) ) {\n\t\t\t\t\t\tclone = [];\n\t\t\t\t\t} else if ( !copyIsArray && !jQuery.isPlainObject( src ) ) {\n\t\t\t\t\t\tclone = {};\n\t\t\t\t\t} else {\n\t\t\t\t\t\tclone = src;\n\t\t\t\t\t}\n\t\t\t\t\tcopyIsArray = false;\n\n\t\t\t\t\t// Never move original objects, clone them\n\t\t\t\t\ttarget[ name ] = jQuery.extend( deep, clone, copy );\n\n\t\t\t\t// Don't bring in undefined values\n\t\t\t\t} else if ( copy !== undefined ) {\n\t\t\t\t\ttarget[ name ] = copy;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Return the modified object\n\treturn target;\n};\n\njQuery.extend( {\n\n\t// Unique for each copy of jQuery on the page\n\texpando: \"jQuery\" + ( version + Math.random() ).replace( /\\D/g, \"\" ),\n\n\t// Assume jQuery is ready without the ready module\n\tisReady: true,\n\n\terror: function( msg ) {\n\t\tthrow new Error( msg );\n\t},\n\n\tnoop: function() {},\n\n\tisPlainObject: function( obj ) {\n\t\tvar proto, Ctor;\n\n\t\t// Detect obvious negatives\n\t\t// Use toString instead of jQuery.type to catch host objects\n\t\tif ( !obj || toString.call( obj ) !== \"[object Object]\" ) {\n\t\t\treturn false;\n\t\t}\n\n\t\tproto = getProto( obj );\n\n\t\t// Objects with no prototype (e.g., `Object.create( null )`) are plain\n\t\tif ( !proto ) {\n\t\t\treturn true;\n\t\t}\n\n\t\t// Objects with prototype are plain iff they were constructed by a global Object function\n\t\tCtor = hasOwn.call( proto, \"constructor\" ) && proto.constructor;\n\t\treturn typeof Ctor === \"function\" && fnToString.call( Ctor ) === ObjectFunctionString;\n\t},\n\n\tisEmptyObject: function( obj ) {\n\t\tvar name;\n\n\t\tfor ( name in obj ) {\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t},\n\n\t// Evaluates a script in a global context\n\tglobalEval: function( code, options ) {\n\t\tDOMEval( code, { nonce: options && options.nonce } );\n\t},\n\n\teach: function( obj, callback ) {\n\t\tvar length, i = 0;\n\n\t\tif ( isArrayLike( obj ) ) {\n\t\t\tlength = obj.length;\n\t\t\tfor ( ; i < length; i++ ) {\n\t\t\t\tif ( callback.call( obj[ i ], i, obj[ i ] ) === false ) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tfor ( i in obj ) {\n\t\t\t\tif ( callback.call( obj[ i ], i, obj[ i ] ) === false ) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn obj;\n\t},\n\n\t// Support: Android <=4.0 only\n\ttrim: function( text ) {\n\t\treturn text == null ?\n\t\t\t\"\" :\n\t\t\t( text + \"\" ).replace( rtrim, \"\" );\n\t},\n\n\t// results is for internal usage only\n\tmakeArray: function( arr, results ) {\n\t\tvar ret = results || [];\n\n\t\tif ( arr != null ) {\n\t\t\tif ( isArrayLike( Object( arr ) ) ) {\n\t\t\t\tjQuery.merge( ret,\n\t\t\t\t\ttypeof arr === \"string\" ?\n\t\t\t\t\t[ arr ] : arr\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tpush.call( ret, arr );\n\t\t\t}\n\t\t}\n\n\t\treturn ret;\n\t},\n\n\tinArray: function( elem, arr, i ) {\n\t\treturn arr == null ? -1 : indexOf.call( arr, elem, i );\n\t},\n\n\t// Support: Android <=4.0 only, PhantomJS 1 only\n\t// push.apply(_, arraylike) throws on ancient WebKit\n\tmerge: function( first, second ) {\n\t\tvar len = +second.length,\n\t\t\tj = 0,\n\t\t\ti = first.length;\n\n\t\tfor ( ; j < len; j++ ) {\n\t\t\tfirst[ i++ ] = second[ j ];\n\t\t}\n\n\t\tfirst.length = i;\n\n\t\treturn first;\n\t},\n\n\tgrep: function( elems, callback, invert ) {\n\t\tvar callbackInverse,\n\t\t\tmatches = [],\n\t\t\ti = 0,\n\t\t\tlength = elems.length,\n\t\t\tcallbackExpect = !invert;\n\n\t\t// Go through the array, only saving the items\n\t\t// that pass the validator function\n\t\tfor ( ; i < length; i++ ) {\n\t\t\tcallbackInverse = !callback( elems[ i ], i );\n\t\t\tif ( callbackInverse !== callbackExpect ) {\n\t\t\t\tmatches.push( elems[ i ] );\n\t\t\t}\n\t\t}\n\n\t\treturn matches;\n\t},\n\n\t// arg is for internal usage only\n\tmap: function( elems, callback, arg ) {\n\t\tvar length, value,\n\t\t\ti = 0,\n\t\t\tret = [];\n\n\t\t// Go through the array, translating each of the items to their new values\n\t\tif ( isArrayLike( elems ) ) {\n\t\t\tlength = elems.length;\n\t\t\tfor ( ; i < length; i++ ) {\n\t\t\t\tvalue = callback( elems[ i ], i, arg );\n\n\t\t\t\tif ( value != null ) {\n\t\t\t\t\tret.push( value );\n\t\t\t\t}\n\t\t\t}\n\n\t\t// Go through every key on the object,\n\t\t} else {\n\t\t\tfor ( i in elems ) {\n\t\t\t\tvalue = callback( elems[ i ], i, arg );\n\n\t\t\t\tif ( value != null ) {\n\t\t\t\t\tret.push( value );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Flatten any nested arrays\n\t\treturn concat.apply( [], ret );\n\t},\n\n\t// A global GUID counter for objects\n\tguid: 1,\n\n\t// jQuery.support is not used in Core but other projects attach their\n\t// properties to it so it needs to exist.\n\tsupport: support\n} );\n\nif ( typeof Symbol === \"function\" ) {\n\tjQuery.fn[ Symbol.iterator ] = arr[ Symbol.iterator ];\n}\n\n// Populate the class2type map\njQuery.each( \"Boolean Number String Function Array Date RegExp Object Error Symbol\".split( \" \" ),\nfunction( i, name ) {\n\tclass2type[ \"[object \" + name + \"]\" ] = name.toLowerCase();\n} );\n\nfunction isArrayLike( obj ) {\n\n\t// Support: real iOS 8.2 only (not reproducible in simulator)\n\t// `in` check used to prevent JIT error (gh-2145)\n\t// hasOwn isn't used here due to false negatives\n\t// regarding Nodelist length in IE\n\tvar length = !!obj && \"length\" in obj && obj.length,\n\t\ttype = toType( obj );\n\n\tif ( isFunction( obj ) || isWindow( obj ) ) {\n\t\treturn false;\n\t}\n\n\treturn type === \"array\" || length === 0 ||\n\t\ttypeof length === \"number\" && length > 0 && ( length - 1 ) in obj;\n}\nvar Sizzle =\n/*!\n * Sizzle CSS Selector Engine v2.3.4\n * https://sizzlejs.com/\n *\n * Copyright JS Foundation and other contributors\n * Released under the MIT license\n * https://js.foundation/\n *\n * Date: 2019-04-08\n */\n(function( window ) {\n\nvar i,\n\tsupport,\n\tExpr,\n\tgetText,\n\tisXML,\n\ttokenize,\n\tcompile,\n\tselect,\n\toutermostContext,\n\tsortInput,\n\thasDuplicate,\n\n\t// Local document vars\n\tsetDocument,\n\tdocument,\n\tdocElem,\n\tdocumentIsHTML,\n\trbuggyQSA,\n\trbuggyMatches,\n\tmatches,\n\tcontains,\n\n\t// Instance-specific data\n\texpando = \"sizzle\" + 1 * new Date(),\n\tpreferredDoc = window.document,\n\tdirruns = 0,\n\tdone = 0,\n\tclassCache = createCache(),\n\ttokenCache = createCache(),\n\tcompilerCache = createCache(),\n\tnonnativeSelectorCache = createCache(),\n\tsortOrder = function( a, b ) {\n\t\tif ( a === b ) {\n\t\t\thasDuplicate = true;\n\t\t}\n\t\treturn 0;\n\t},\n\n\t// Instance methods\n\thasOwn = ({}).hasOwnProperty,\n\tarr = [],\n\tpop = arr.pop,\n\tpush_native = arr.push,\n\tpush = arr.push,\n\tslice = arr.slice,\n\t// Use a stripped-down indexOf as it's faster than native\n\t// https://jsperf.com/thor-indexof-vs-for/5\n\tindexOf = function( list, elem ) {\n\t\tvar i = 0,\n\t\t\tlen = list.length;\n\t\tfor ( ; i < len; i++ ) {\n\t\t\tif ( list[i] === elem ) {\n\t\t\t\treturn i;\n\t\t\t}\n\t\t}\n\t\treturn -1;\n\t},\n\n\tbooleans = \"checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped\",\n\n\t// Regular expressions\n\n\t// http://www.w3.org/TR/css3-selectors/#whitespace\n\twhitespace = \"[\\\\x20\\\\t\\\\r\\\\n\\\\f]\",\n\n\t// http://www.w3.org/TR/CSS21/syndata.html#value-def-identifier\n\tidentifier = \"(?:\\\\\\\\.|[\\\\w-]|[^\\0-\\\\xa0])+\",\n\n\t// Attribute selectors: http://www.w3.org/TR/selectors/#attribute-selectors\n\tattributes = \"\\\\[\" + whitespace + \"*(\" + identifier + \")(?:\" + whitespace +\n\t\t// Operator (capture 2)\n\t\t\"*([*^$|!~]?=)\" + whitespace +\n\t\t// \"Attribute values must be CSS identifiers [capture 5] or strings [capture 3 or capture 4]\"\n\t\t\"*(?:'((?:\\\\\\\\.|[^\\\\\\\\'])*)'|\\\"((?:\\\\\\\\.|[^\\\\\\\\\\\"])*)\\\"|(\" + identifier + \"))|)\" + whitespace +\n\t\t\"*\\\\]\",\n\n\tpseudos = \":(\" + identifier + \")(?:\\\\((\" +\n\t\t// To reduce the number of selectors needing tokenize in the preFilter, prefer arguments:\n\t\t// 1. quoted (capture 3; capture 4 or capture 5)\n\t\t\"('((?:\\\\\\\\.|[^\\\\\\\\'])*)'|\\\"((?:\\\\\\\\.|[^\\\\\\\\\\\"])*)\\\")|\" +\n\t\t// 2. simple (capture 6)\n\t\t\"((?:\\\\\\\\.|[^\\\\\\\\()[\\\\]]|\" + attributes + \")*)|\" +\n\t\t// 3. anything else (capture 2)\n\t\t\".*\" +\n\t\t\")\\\\)|)\",\n\n\t// Leading and non-escaped trailing whitespace, capturing some non-whitespace characters preceding the latter\n\trwhitespace = new RegExp( whitespace + \"+\", \"g\" ),\n\trtrim = new RegExp( \"^\" + whitespace + \"+|((?:^|[^\\\\\\\\])(?:\\\\\\\\.)*)\" + whitespace + \"+$\", \"g\" ),\n\n\trcomma = new RegExp( \"^\" + whitespace + \"*,\" + whitespace + \"*\" ),\n\trcombinators = new RegExp( \"^\" + whitespace + \"*([>+~]|\" + whitespace + \")\" + whitespace + \"*\" ),\n\trdescend = new RegExp( whitespace + \"|>\" ),\n\n\trpseudo = new RegExp( pseudos ),\n\tridentifier = new RegExp( \"^\" + identifier + \"$\" ),\n\n\tmatchExpr = {\n\t\t\"ID\": new RegExp( \"^#(\" + identifier + \")\" ),\n\t\t\"CLASS\": new RegExp( \"^\\\\.(\" + identifier + \")\" ),\n\t\t\"TAG\": new RegExp( \"^(\" + identifier + \"|[*])\" ),\n\t\t\"ATTR\": new RegExp( \"^\" + attributes ),\n\t\t\"PSEUDO\": new RegExp( \"^\" + pseudos ),\n\t\t\"CHILD\": new RegExp( \"^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\\\(\" + whitespace +\n\t\t\t\"*(even|odd|(([+-]|)(\\\\d*)n|)\" + whitespace + \"*(?:([+-]|)\" + whitespace +\n\t\t\t\"*(\\\\d+)|))\" + whitespace + \"*\\\\)|)\", \"i\" ),\n\t\t\"bool\": new RegExp( \"^(?:\" + booleans + \")$\", \"i\" ),\n\t\t// For use in libraries implementing .is()\n\t\t// We use this for POS matching in `select`\n\t\t\"needsContext\": new RegExp( \"^\" + whitespace + \"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\\\(\" +\n\t\t\twhitespace + \"*((?:-\\\\d)?\\\\d*)\" + whitespace + \"*\\\\)|)(?=[^-]|$)\", \"i\" )\n\t},\n\n\trhtml = /HTML$/i,\n\trinputs = /^(?:input|select|textarea|button)$/i,\n\trheader = /^h\\d$/i,\n\n\trnative = /^[^{]+\\{\\s*\\[native \\w/,\n\n\t// Easily-parseable/retrievable ID or TAG or CLASS selectors\n\trquickExpr = /^(?:#([\\w-]+)|(\\w+)|\\.([\\w-]+))$/,\n\n\trsibling = /[+~]/,\n\n\t// CSS escapes\n\t// http://www.w3.org/TR/CSS21/syndata.html#escaped-characters\n\trunescape = new RegExp( \"\\\\\\\\([\\\\da-f]{1,6}\" + whitespace + \"?|(\" + whitespace + \")|.)\", \"ig\" ),\n\tfunescape = function( _, escaped, escapedWhitespace ) {\n\t\tvar high = \"0x\" + escaped - 0x10000;\n\t\t// NaN means non-codepoint\n\t\t// Support: Firefox<24\n\t\t// Workaround erroneous numeric interpretation of +\"0x\"\n\t\treturn high !== high || escapedWhitespace ?\n\t\t\tescaped :\n\t\t\thigh < 0 ?\n\t\t\t\t// BMP codepoint\n\t\t\t\tString.fromCharCode( high + 0x10000 ) :\n\t\t\t\t// Supplemental Plane codepoint (surrogate pair)\n\t\t\t\tString.fromCharCode( high >> 10 | 0xD800, high & 0x3FF | 0xDC00 );\n\t},\n\n\t// CSS string/identifier serialization\n\t// https://drafts.csswg.org/cssom/#common-serializing-idioms\n\trcssescape = /([\\0-\\x1f\\x7f]|^-?\\d)|^-$|[^\\0-\\x1f\\x7f-\\uFFFF\\w-]/g,\n\tfcssescape = function( ch, asCodePoint ) {\n\t\tif ( asCodePoint ) {\n\n\t\t\t// U+0000 NULL becomes U+FFFD REPLACEMENT CHARACTER\n\t\t\tif ( ch === \"\\0\" ) {\n\t\t\t\treturn \"\\uFFFD\";\n\t\t\t}\n\n\t\t\t// Control characters and (dependent upon position) numbers get escaped as code points\n\t\t\treturn ch.slice( 0, -1 ) + \"\\\\\" + ch.charCodeAt( ch.length - 1 ).toString( 16 ) + \" \";\n\t\t}\n\n\t\t// Other potentially-special ASCII characters get backslash-escaped\n\t\treturn \"\\\\\" + ch;\n\t},\n\n\t// Used for iframes\n\t// See setDocument()\n\t// Removing the function wrapper causes a \"Permission Denied\"\n\t// error in IE\n\tunloadHandler = function() {\n\t\tsetDocument();\n\t},\n\n\tinDisabledFieldset = addCombinator(\n\t\tfunction( elem ) {\n\t\t\treturn elem.disabled === true && elem.nodeName.toLowerCase() === \"fieldset\";\n\t\t},\n\t\t{ dir: \"parentNode\", next: \"legend\" }\n\t);\n\n// Optimize for push.apply( _, NodeList )\ntry {\n\tpush.apply(\n\t\t(arr = slice.call( preferredDoc.childNodes )),\n\t\tpreferredDoc.childNodes\n\t);\n\t// Support: Android<4.0\n\t// Detect silently failing push.apply\n\tarr[ preferredDoc.childNodes.length ].nodeType;\n} catch ( e ) {\n\tpush = { apply: arr.length ?\n\n\t\t// Leverage slice if possible\n\t\tfunction( target, els ) {\n\t\t\tpush_native.apply( target, slice.call(els) );\n\t\t} :\n\n\t\t// Support: IE<9\n\t\t// Otherwise append directly\n\t\tfunction( target, els ) {\n\t\t\tvar j = target.length,\n\t\t\t\ti = 0;\n\t\t\t// Can't trust NodeList.length\n\t\t\twhile ( (target[j++] = els[i++]) ) {}\n\t\t\ttarget.length = j - 1;\n\t\t}\n\t};\n}\n\nfunction Sizzle( selector, context, results, seed ) {\n\tvar m, i, elem, nid, match, groups, newSelector,\n\t\tnewContext = context && context.ownerDocument,\n\n\t\t// nodeType defaults to 9, since context defaults to document\n\t\tnodeType = context ? context.nodeType : 9;\n\n\tresults = results || [];\n\n\t// Return early from calls with invalid selector or context\n\tif ( typeof selector !== \"string\" || !selector ||\n\t\tnodeType !== 1 && nodeType !== 9 && nodeType !== 11 ) {\n\n\t\treturn results;\n\t}\n\n\t// Try to shortcut find operations (as opposed to filters) in HTML documents\n\tif ( !seed ) {\n\n\t\tif ( ( context ? context.ownerDocument || context : preferredDoc ) !== document ) {\n\t\t\tsetDocument( context );\n\t\t}\n\t\tcontext = context || document;\n\n\t\tif ( documentIsHTML ) {\n\n\t\t\t// If the selector is sufficiently simple, try using a \"get*By*\" DOM method\n\t\t\t// (excepting DocumentFragment context, where the methods don't exist)\n\t\t\tif ( nodeType !== 11 && (match = rquickExpr.exec( selector )) ) {\n\n\t\t\t\t// ID selector\n\t\t\t\tif ( (m = match[1]) ) {\n\n\t\t\t\t\t// Document context\n\t\t\t\t\tif ( nodeType === 9 ) {\n\t\t\t\t\t\tif ( (elem = context.getElementById( m )) ) {\n\n\t\t\t\t\t\t\t// Support: IE, Opera, Webkit\n\t\t\t\t\t\t\t// TODO: identify versions\n\t\t\t\t\t\t\t// getElementById can match elements by name instead of ID\n\t\t\t\t\t\t\tif ( elem.id === m ) {\n\t\t\t\t\t\t\t\tresults.push( elem );\n\t\t\t\t\t\t\t\treturn results;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\treturn results;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t// Element context\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\t// Support: IE, Opera, Webkit\n\t\t\t\t\t\t// TODO: identify versions\n\t\t\t\t\t\t// getElementById can match elements by name instead of ID\n\t\t\t\t\t\tif ( newContext && (elem = newContext.getElementById( m )) &&\n\t\t\t\t\t\t\tcontains( context, elem ) &&\n\t\t\t\t\t\t\telem.id === m ) {\n\n\t\t\t\t\t\t\tresults.push( elem );\n\t\t\t\t\t\t\treturn results;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t// Type selector\n\t\t\t\t} else if ( match[2] ) {\n\t\t\t\t\tpush.apply( results, context.getElementsByTagName( selector ) );\n\t\t\t\t\treturn results;\n\n\t\t\t\t// Class selector\n\t\t\t\t} else if ( (m = match[3]) && support.getElementsByClassName &&\n\t\t\t\t\tcontext.getElementsByClassName ) {\n\n\t\t\t\t\tpush.apply( results, context.getElementsByClassName( m ) );\n\t\t\t\t\treturn results;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Take advantage of querySelectorAll\n\t\t\tif ( support.qsa &&\n\t\t\t\t!nonnativeSelectorCache[ selector + \" \" ] &&\n\t\t\t\t(!rbuggyQSA || !rbuggyQSA.test( selector )) &&\n\n\t\t\t\t// Support: IE 8 only\n\t\t\t\t// Exclude object elements\n\t\t\t\t(nodeType !== 1 || context.nodeName.toLowerCase() !== \"object\") ) {\n\n\t\t\t\tnewSelector = selector;\n\t\t\t\tnewContext = context;\n\n\t\t\t\t// qSA considers elements outside a scoping root when evaluating child or\n\t\t\t\t// descendant combinators, which is not what we want.\n\t\t\t\t// In such cases, we work around the behavior by prefixing every selector in the\n\t\t\t\t// list with an ID selector referencing the scope context.\n\t\t\t\t// Thanks to Andrew Dupont for this technique.\n\t\t\t\tif ( nodeType === 1 && rdescend.test( selector ) ) {\n\n\t\t\t\t\t// Capture the context ID, setting it first if necessary\n\t\t\t\t\tif ( (nid = context.getAttribute( \"id\" )) ) {\n\t\t\t\t\t\tnid = nid.replace( rcssescape, fcssescape );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcontext.setAttribute( \"id\", (nid = expando) );\n\t\t\t\t\t}\n\n\t\t\t\t\t// Prefix every selector in the list\n\t\t\t\t\tgroups = tokenize( selector );\n\t\t\t\t\ti = groups.length;\n\t\t\t\t\twhile ( i-- ) {\n\t\t\t\t\t\tgroups[i] = \"#\" + nid + \" \" + toSelector( groups[i] );\n\t\t\t\t\t}\n\t\t\t\t\tnewSelector = groups.join( \",\" );\n\n\t\t\t\t\t// Expand context for sibling selectors\n\t\t\t\t\tnewContext = rsibling.test( selector ) && testContext( context.parentNode ) ||\n\t\t\t\t\t\tcontext;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tpush.apply( results,\n\t\t\t\t\t\tnewContext.querySelectorAll( newSelector )\n\t\t\t\t\t);\n\t\t\t\t\treturn results;\n\t\t\t\t} catch ( qsaError ) {\n\t\t\t\t\tnonnativeSelectorCache( selector, true );\n\t\t\t\t} finally {\n\t\t\t\t\tif ( nid === expando ) {\n\t\t\t\t\t\tcontext.removeAttribute( \"id\" );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// All others\n\treturn select( selector.replace( rtrim, \"$1\" ), context, results, seed );\n}\n\n/**\n * Create key-value caches of limited size\n * @returns {function(string, object)} Returns the Object data after storing it on itself with\n *\tproperty name the (space-suffixed) string and (if the cache is larger than Expr.cacheLength)\n *\tdeleting the oldest entry\n */\nfunction createCache() {\n\tvar keys = [];\n\n\tfunction cache( key, value ) {\n\t\t// Use (key + \" \") to avoid collision with native prototype properties (see Issue #157)\n\t\tif ( keys.push( key + \" \" ) > Expr.cacheLength ) {\n\t\t\t// Only keep the most recent entries\n\t\t\tdelete cache[ keys.shift() ];\n\t\t}\n\t\treturn (cache[ key + \" \" ] = value);\n\t}\n\treturn cache;\n}\n\n/**\n * Mark a function for special use by Sizzle\n * @param {Function} fn The function to mark\n */\nfunction markFunction( fn ) {\n\tfn[ expando ] = true;\n\treturn fn;\n}\n\n/**\n * Support testing using an element\n * @param {Function} fn Passed the created element and returns a boolean result\n */\nfunction assert( fn ) {\n\tvar el = document.createElement(\"fieldset\");\n\n\ttry {\n\t\treturn !!fn( el );\n\t} catch (e) {\n\t\treturn false;\n\t} finally {\n\t\t// Remove from its parent by default\n\t\tif ( el.parentNode ) {\n\t\t\tel.parentNode.removeChild( el );\n\t\t}\n\t\t// release memory in IE\n\t\tel = null;\n\t}\n}\n\n/**\n * Adds the same handler for all of the specified attrs\n * @param {String} attrs Pipe-separated list of attributes\n * @param {Function} handler The method that will be applied\n */\nfunction addHandle( attrs, handler ) {\n\tvar arr = attrs.split(\"|\"),\n\t\ti = arr.length;\n\n\twhile ( i-- ) {\n\t\tExpr.attrHandle[ arr[i] ] = handler;\n\t}\n}\n\n/**\n * Checks document order of two siblings\n * @param {Element} a\n * @param {Element} b\n * @returns {Number} Returns less than 0 if a precedes b, greater than 0 if a follows b\n */\nfunction siblingCheck( a, b ) {\n\tvar cur = b && a,\n\t\tdiff = cur && a.nodeType === 1 && b.nodeType === 1 &&\n\t\t\ta.sourceIndex - b.sourceIndex;\n\n\t// Use IE sourceIndex if available on both nodes\n\tif ( diff ) {\n\t\treturn diff;\n\t}\n\n\t// Check if b follows a\n\tif ( cur ) {\n\t\twhile ( (cur = cur.nextSibling) ) {\n\t\t\tif ( cur === b ) {\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn a ? 1 : -1;\n}\n\n/**\n * Returns a function to use in pseudos for input types\n * @param {String} type\n */\nfunction createInputPseudo( type ) {\n\treturn function( elem ) {\n\t\tvar name = elem.nodeName.toLowerCase();\n\t\treturn name === \"input\" && elem.type === type;\n\t};\n}\n\n/**\n * Returns a function to use in pseudos for buttons\n * @param {String} type\n */\nfunction createButtonPseudo( type ) {\n\treturn function( elem ) {\n\t\tvar name = elem.nodeName.toLowerCase();\n\t\treturn (name === \"input\" || name === \"button\") && elem.type === type;\n\t};\n}\n\n/**\n * Returns a function to use in pseudos for :enabled/:disabled\n * @param {Boolean} disabled true for :disabled; false for :enabled\n */\nfunction createDisabledPseudo( disabled ) {\n\n\t// Known :disabled false positives: fieldset[disabled] > legend:nth-of-type(n+2) :can-disable\n\treturn function( elem ) {\n\n\t\t// Only certain elements can match :enabled or :disabled\n\t\t// https://html.spec.whatwg.org/multipage/scripting.html#selector-enabled\n\t\t// https://html.spec.whatwg.org/multipage/scripting.html#selector-disabled\n\t\tif ( \"form\" in elem ) {\n\n\t\t\t// Check for inherited disabledness on relevant non-disabled elements:\n\t\t\t// * listed form-associated elements in a disabled fieldset\n\t\t\t//   https://html.spec.whatwg.org/multipage/forms.html#category-listed\n\t\t\t//   https://html.spec.whatwg.org/multipage/forms.html#concept-fe-disabled\n\t\t\t// * option elements in a disabled optgroup\n\t\t\t//   https://html.spec.whatwg.org/multipage/forms.html#concept-option-disabled\n\t\t\t// All such elements have a \"form\" property.\n\t\t\tif ( elem.parentNode && elem.disabled === false ) {\n\n\t\t\t\t// Option elements defer to a parent optgroup if present\n\t\t\t\tif ( \"label\" in elem ) {\n\t\t\t\t\tif ( \"label\" in elem.parentNode ) {\n\t\t\t\t\t\treturn elem.parentNode.disabled === disabled;\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn elem.disabled === disabled;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Support: IE 6 - 11\n\t\t\t\t// Use the isDisabled shortcut property to check for disabled fieldset ancestors\n\t\t\t\treturn elem.isDisabled === disabled ||\n\n\t\t\t\t\t// Where there is no isDisabled, check manually\n\t\t\t\t\t/* jshint -W018 */\n\t\t\t\t\telem.isDisabled !== !disabled &&\n\t\t\t\t\t\tinDisabledFieldset( elem ) === disabled;\n\t\t\t}\n\n\t\t\treturn elem.disabled === disabled;\n\n\t\t// Try to winnow out elements that can't be disabled before trusting the disabled property.\n\t\t// Some victims get caught in our net (label, legend, menu, track), but it shouldn't\n\t\t// even exist on them, let alone have a boolean value.\n\t\t} else if ( \"label\" in elem ) {\n\t\t\treturn elem.disabled === disabled;\n\t\t}\n\n\t\t// Remaining elements are neither :enabled nor :disabled\n\t\treturn false;\n\t};\n}\n\n/**\n * Returns a function to use in pseudos for positionals\n * @param {Function} fn\n */\nfunction createPositionalPseudo( fn ) {\n\treturn markFunction(function( argument ) {\n\t\targument = +argument;\n\t\treturn markFunction(function( seed, matches ) {\n\t\t\tvar j,\n\t\t\t\tmatchIndexes = fn( [], seed.length, argument ),\n\t\t\t\ti = matchIndexes.length;\n\n\t\t\t// Match elements found at the specified indexes\n\t\t\twhile ( i-- ) {\n\t\t\t\tif ( seed[ (j = matchIndexes[i]) ] ) {\n\t\t\t\t\tseed[j] = !(matches[j] = seed[j]);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t});\n}\n\n/**\n * Checks a node for validity as a Sizzle context\n * @param {Element|Object=} context\n * @returns {Element|Object|Boolean} The input node if acceptable, otherwise a falsy value\n */\nfunction testContext( context ) {\n\treturn context && typeof context.getElementsByTagName !== \"undefined\" && context;\n}\n\n// Expose support vars for convenience\nsupport = Sizzle.support = {};\n\n/**\n * Detects XML nodes\n * @param {Element|Object} elem An element or a document\n * @returns {Boolean} True iff elem is a non-HTML XML node\n */\nisXML = Sizzle.isXML = function( elem ) {\n\tvar namespace = elem.namespaceURI,\n\t\tdocElem = (elem.ownerDocument || elem).documentElement;\n\n\t// Support: IE <=8\n\t// Assume HTML when documentElement doesn't yet exist, such as inside loading iframes\n\t// https://bugs.jquery.com/ticket/4833\n\treturn !rhtml.test( namespace || docElem && docElem.nodeName || \"HTML\" );\n};\n\n/**\n * Sets document-related variables once based on the current document\n * @param {Element|Object} [doc] An element or document object to use to set the document\n * @returns {Object} Returns the current document\n */\nsetDocument = Sizzle.setDocument = function( node ) {\n\tvar hasCompare, subWindow,\n\t\tdoc = node ? node.ownerDocument || node : preferredDoc;\n\n\t// Return early if doc is invalid or already selected\n\tif ( doc === document || doc.nodeType !== 9 || !doc.documentElement ) {\n\t\treturn document;\n\t}\n\n\t// Update global variables\n\tdocument = doc;\n\tdocElem = document.documentElement;\n\tdocumentIsHTML = !isXML( document );\n\n\t// Support: IE 9-11, Edge\n\t// Accessing iframe documents after unload throws \"permission denied\" errors (jQuery #13936)\n\tif ( preferredDoc !== document &&\n\t\t(subWindow = document.defaultView) && subWindow.top !== subWindow ) {\n\n\t\t// Support: IE 11, Edge\n\t\tif ( subWindow.addEventListener ) {\n\t\t\tsubWindow.addEventListener( \"unload\", unloadHandler, false );\n\n\t\t// Support: IE 9 - 10 only\n\t\t} else if ( subWindow.attachEvent ) {\n\t\t\tsubWindow.attachEvent( \"onunload\", unloadHandler );\n\t\t}\n\t}\n\n\t/* Attributes\n\t---------------------------------------------------------------------- */\n\n\t// Support: IE<8\n\t// Verify that getAttribute really returns attributes and not properties\n\t// (excepting IE8 booleans)\n\tsupport.attributes = assert(function( el ) {\n\t\tel.className = \"i\";\n\t\treturn !el.getAttribute(\"className\");\n\t});\n\n\t/* getElement(s)By*\n\t---------------------------------------------------------------------- */\n\n\t// Check if getElementsByTagName(\"*\") returns only elements\n\tsupport.getElementsByTagName = assert(function( el ) {\n\t\tel.appendChild( document.createComment(\"\") );\n\t\treturn !el.getElementsByTagName(\"*\").length;\n\t});\n\n\t// Support: IE<9\n\tsupport.getElementsByClassName = rnative.test( document.getElementsByClassName );\n\n\t// Support: IE<10\n\t// Check if getElementById returns elements by name\n\t// The broken getElementById methods don't pick up programmatically-set names,\n\t// so use a roundabout getElementsByName test\n\tsupport.getById = assert(function( el ) {\n\t\tdocElem.appendChild( el ).id = expando;\n\t\treturn !document.getElementsByName || !document.getElementsByName( expando ).length;\n\t});\n\n\t// ID filter and find\n\tif ( support.getById ) {\n\t\tExpr.filter[\"ID\"] = function( id ) {\n\t\t\tvar attrId = id.replace( runescape, funescape );\n\t\t\treturn function( elem ) {\n\t\t\t\treturn elem.getAttribute(\"id\") === attrId;\n\t\t\t};\n\t\t};\n\t\tExpr.find[\"ID\"] = function( id, context ) {\n\t\t\tif ( typeof context.getElementById !== \"undefined\" && documentIsHTML ) {\n\t\t\t\tvar elem = context.getElementById( id );\n\t\t\t\treturn elem ? [ elem ] : [];\n\t\t\t}\n\t\t};\n\t} else {\n\t\tExpr.filter[\"ID\"] =  function( id ) {\n\t\t\tvar attrId = id.replace( runescape, funescape );\n\t\t\treturn function( elem ) {\n\t\t\t\tvar node = typeof elem.getAttributeNode !== \"undefined\" &&\n\t\t\t\t\telem.getAttributeNode(\"id\");\n\t\t\t\treturn node && node.value === attrId;\n\t\t\t};\n\t\t};\n\n\t\t// Support: IE 6 - 7 only\n\t\t// getElementById is not reliable as a find shortcut\n\t\tExpr.find[\"ID\"] = function( id, context ) {\n\t\t\tif ( typeof context.getElementById !== \"undefined\" && documentIsHTML ) {\n\t\t\t\tvar node, i, elems,\n\t\t\t\t\telem = context.getElementById( id );\n\n\t\t\t\tif ( elem ) {\n\n\t\t\t\t\t// Verify the id attribute\n\t\t\t\t\tnode = elem.getAttributeNode(\"id\");\n\t\t\t\t\tif ( node && node.value === id ) {\n\t\t\t\t\t\treturn [ elem ];\n\t\t\t\t\t}\n\n\t\t\t\t\t// Fall back on getElementsByName\n\t\t\t\t\telems = context.getElementsByName( id );\n\t\t\t\t\ti = 0;\n\t\t\t\t\twhile ( (elem = elems[i++]) ) {\n\t\t\t\t\t\tnode = elem.getAttributeNode(\"id\");\n\t\t\t\t\t\tif ( node && node.value === id ) {\n\t\t\t\t\t\t\treturn [ elem ];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn [];\n\t\t\t}\n\t\t};\n\t}\n\n\t// Tag\n\tExpr.find[\"TAG\"] = support.getElementsByTagName ?\n\t\tfunction( tag, context ) {\n\t\t\tif ( typeof context.getElementsByTagName !== \"undefined\" ) {\n\t\t\t\treturn context.getElementsByTagName( tag );\n\n\t\t\t// DocumentFragment nodes don't have gEBTN\n\t\t\t} else if ( support.qsa ) {\n\t\t\t\treturn context.querySelectorAll( tag );\n\t\t\t}\n\t\t} :\n\n\t\tfunction( tag, context ) {\n\t\t\tvar elem,\n\t\t\t\ttmp = [],\n\t\t\t\ti = 0,\n\t\t\t\t// By happy coincidence, a (broken) gEBTN appears on DocumentFragment nodes too\n\t\t\t\tresults = context.getElementsByTagName( tag );\n\n\t\t\t// Filter out possible comments\n\t\t\tif ( tag === \"*\" ) {\n\t\t\t\twhile ( (elem = results[i++]) ) {\n\t\t\t\t\tif ( elem.nodeType === 1 ) {\n\t\t\t\t\t\ttmp.push( elem );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn tmp;\n\t\t\t}\n\t\t\treturn results;\n\t\t};\n\n\t// Class\n\tExpr.find[\"CLASS\"] = support.getElementsByClassName && function( className, context ) {\n\t\tif ( typeof context.getElementsByClassName !== \"undefined\" && documentIsHTML ) {\n\t\t\treturn context.getElementsByClassName( className );\n\t\t}\n\t};\n\n\t/* QSA/matchesSelector\n\t---------------------------------------------------------------------- */\n\n\t// QSA and matchesSelector support\n\n\t// matchesSelector(:active) reports false when true (IE9/Opera 11.5)\n\trbuggyMatches = [];\n\n\t// qSa(:focus) reports false when true (Chrome 21)\n\t// We allow this because of a bug in IE8/9 that throws an error\n\t// whenever `document.activeElement` is accessed on an iframe\n\t// So, we allow :focus to pass through QSA all the time to avoid the IE error\n\t// See https://bugs.jquery.com/ticket/13378\n\trbuggyQSA = [];\n\n\tif ( (support.qsa = rnative.test( document.querySelectorAll )) ) {\n\t\t// Build QSA regex\n\t\t// Regex strategy adopted from Diego Perini\n\t\tassert(function( el ) {\n\t\t\t// Select is set to empty string on purpose\n\t\t\t// This is to test IE's treatment of not explicitly\n\t\t\t// setting a boolean content attribute,\n\t\t\t// since its presence should be enough\n\t\t\t// https://bugs.jquery.com/ticket/12359\n\t\t\tdocElem.appendChild( el ).innerHTML = \"<a id='\" + expando + \"'></a>\" +\n\t\t\t\t\"<select id='\" + expando + \"-\\r\\\\' msallowcapture=''>\" +\n\t\t\t\t\"<option selected=''></option></select>\";\n\n\t\t\t// Support: IE8, Opera 11-12.16\n\t\t\t// Nothing should be selected when empty strings follow ^= or $= or *=\n\t\t\t// The test attribute must be unknown in Opera but \"safe\" for WinRT\n\t\t\t// https://msdn.microsoft.com/en-us/library/ie/hh465388.aspx#attribute_section\n\t\t\tif ( el.querySelectorAll(\"[msallowcapture^='']\").length ) {\n\t\t\t\trbuggyQSA.push( \"[*^$]=\" + whitespace + \"*(?:''|\\\"\\\")\" );\n\t\t\t}\n\n\t\t\t// Support: IE8\n\t\t\t// Boolean attributes and \"value\" are not treated correctly\n\t\t\tif ( !el.querySelectorAll(\"[selected]\").length ) {\n\t\t\t\trbuggyQSA.push( \"\\\\[\" + whitespace + \"*(?:value|\" + booleans + \")\" );\n\t\t\t}\n\n\t\t\t// Support: Chrome<29, Android<4.4, Safari<7.0+, iOS<7.0+, PhantomJS<1.9.8+\n\t\t\tif ( !el.querySelectorAll( \"[id~=\" + expando + \"-]\" ).length ) {\n\t\t\t\trbuggyQSA.push(\"~=\");\n\t\t\t}\n\n\t\t\t// Webkit/Opera - :checked should return selected option elements\n\t\t\t// http://www.w3.org/TR/2011/REC-css3-selectors-20110929/#checked\n\t\t\t// IE8 throws error here and will not see later tests\n\t\t\tif ( !el.querySelectorAll(\":checked\").length ) {\n\t\t\t\trbuggyQSA.push(\":checked\");\n\t\t\t}\n\n\t\t\t// Support: Safari 8+, iOS 8+\n\t\t\t// https://bugs.webkit.org/show_bug.cgi?id=136851\n\t\t\t// In-page `selector#id sibling-combinator selector` fails\n\t\t\tif ( !el.querySelectorAll( \"a#\" + expando + \"+*\" ).length ) {\n\t\t\t\trbuggyQSA.push(\".#.+[+~]\");\n\t\t\t}\n\t\t});\n\n\t\tassert(function( el ) {\n\t\t\tel.innerHTML = \"<a href='' disabled='disabled'></a>\" +\n\t\t\t\t\"<select disabled='disabled'><option/></select>\";\n\n\t\t\t// Support: Windows 8 Native Apps\n\t\t\t// The type and name attributes are restricted during .innerHTML assignment\n\t\t\tvar input = document.createElement(\"input\");\n\t\t\tinput.setAttribute( \"type\", \"hidden\" );\n\t\t\tel.appendChild( input ).setAttribute( \"name\", \"D\" );\n\n\t\t\t// Support: IE8\n\t\t\t// Enforce case-sensitivity of name attribute\n\t\t\tif ( el.querySelectorAll(\"[name=d]\").length ) {\n\t\t\t\trbuggyQSA.push( \"name\" + whitespace + \"*[*^$|!~]?=\" );\n\t\t\t}\n\n\t\t\t// FF 3.5 - :enabled/:disabled and hidden elements (hidden elements are still enabled)\n\t\t\t// IE8 throws error here and will not see later tests\n\t\t\tif ( el.querySelectorAll(\":enabled\").length !== 2 ) {\n\t\t\t\trbuggyQSA.push( \":enabled\", \":disabled\" );\n\t\t\t}\n\n\t\t\t// Support: IE9-11+\n\t\t\t// IE's :disabled selector does not pick up the children of disabled fieldsets\n\t\t\tdocElem.appendChild( el ).disabled = true;\n\t\t\tif ( el.querySelectorAll(\":disabled\").length !== 2 ) {\n\t\t\t\trbuggyQSA.push( \":enabled\", \":disabled\" );\n\t\t\t}\n\n\t\t\t// Opera 10-11 does not throw on post-comma invalid pseudos\n\t\t\tel.querySelectorAll(\"*,:x\");\n\t\t\trbuggyQSA.push(\",.*:\");\n\t\t});\n\t}\n\n\tif ( (support.matchesSelector = rnative.test( (matches = docElem.matches ||\n\t\tdocElem.webkitMatchesSelector ||\n\t\tdocElem.mozMatchesSelector ||\n\t\tdocElem.oMatchesSelector ||\n\t\tdocElem.msMatchesSelector) )) ) {\n\n\t\tassert(function( el ) {\n\t\t\t// Check to see if it's possible to do matchesSelector\n\t\t\t// on a disconnected node (IE 9)\n\t\t\tsupport.disconnectedMatch = matches.call( el, \"*\" );\n\n\t\t\t// This should fail with an exception\n\t\t\t// Gecko does not error, returns false instead\n\t\t\tmatches.call( el, \"[s!='']:x\" );\n\t\t\trbuggyMatches.push( \"!=\", pseudos );\n\t\t});\n\t}\n\n\trbuggyQSA = rbuggyQSA.length && new RegExp( rbuggyQSA.join(\"|\") );\n\trbuggyMatches = rbuggyMatches.length && new RegExp( rbuggyMatches.join(\"|\") );\n\n\t/* Contains\n\t---------------------------------------------------------------------- */\n\thasCompare = rnative.test( docElem.compareDocumentPosition );\n\n\t// Element contains another\n\t// Purposefully self-exclusive\n\t// As in, an element does not contain itself\n\tcontains = hasCompare || rnative.test( docElem.contains ) ?\n\t\tfunction( a, b ) {\n\t\t\tvar adown = a.nodeType === 9 ? a.documentElement : a,\n\t\t\t\tbup = b && b.parentNode;\n\t\t\treturn a === bup || !!( bup && bup.nodeType === 1 && (\n\t\t\t\tadown.contains ?\n\t\t\t\t\tadown.contains( bup ) :\n\t\t\t\t\ta.compareDocumentPosition && a.compareDocumentPosition( bup ) & 16\n\t\t\t));\n\t\t} :\n\t\tfunction( a, b ) {\n\t\t\tif ( b ) {\n\t\t\t\twhile ( (b = b.parentNode) ) {\n\t\t\t\t\tif ( b === a ) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t};\n\n\t/* Sorting\n\t---------------------------------------------------------------------- */\n\n\t// Document order sorting\n\tsortOrder = hasCompare ?\n\tfunction( a, b ) {\n\n\t\t// Flag for duplicate removal\n\t\tif ( a === b ) {\n\t\t\thasDuplicate = true;\n\t\t\treturn 0;\n\t\t}\n\n\t\t// Sort on method existence if only one input has compareDocumentPosition\n\t\tvar compare = !a.compareDocumentPosition - !b.compareDocumentPosition;\n\t\tif ( compare ) {\n\t\t\treturn compare;\n\t\t}\n\n\t\t// Calculate position if both inputs belong to the same document\n\t\tcompare = ( a.ownerDocument || a ) === ( b.ownerDocument || b ) ?\n\t\t\ta.compareDocumentPosition( b ) :\n\n\t\t\t// Otherwise we know they are disconnected\n\t\t\t1;\n\n\t\t// Disconnected nodes\n\t\tif ( compare & 1 ||\n\t\t\t(!support.sortDetached && b.compareDocumentPosition( a ) === compare) ) {\n\n\t\t\t// Choose the first element that is related to our preferred document\n\t\t\tif ( a === document || a.ownerDocument === preferredDoc && contains(preferredDoc, a) ) {\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\tif ( b === document || b.ownerDocument === preferredDoc && contains(preferredDoc, b) ) {\n\t\t\t\treturn 1;\n\t\t\t}\n\n\t\t\t// Maintain original order\n\t\t\treturn sortInput ?\n\t\t\t\t( indexOf( sortInput, a ) - indexOf( sortInput, b ) ) :\n\t\t\t\t0;\n\t\t}\n\n\t\treturn compare & 4 ? -1 : 1;\n\t} :\n\tfunction( a, b ) {\n\t\t// Exit early if the nodes are identical\n\t\tif ( a === b ) {\n\t\t\thasDuplicate = true;\n\t\t\treturn 0;\n\t\t}\n\n\t\tvar cur,\n\t\t\ti = 0,\n\t\t\taup = a.parentNode,\n\t\t\tbup = b.parentNode,\n\t\t\tap = [ a ],\n\t\t\tbp = [ b ];\n\n\t\t// Parentless nodes are either documents or disconnected\n\t\tif ( !aup || !bup ) {\n\t\t\treturn a === document ? -1 :\n\t\t\t\tb === document ? 1 :\n\t\t\t\taup ? -1 :\n\t\t\t\tbup ? 1 :\n\t\t\t\tsortInput ?\n\t\t\t\t( indexOf( sortInput, a ) - indexOf( sortInput, b ) ) :\n\t\t\t\t0;\n\n\t\t// If the nodes are siblings, we can do a quick check\n\t\t} else if ( aup === bup ) {\n\t\t\treturn siblingCheck( a, b );\n\t\t}\n\n\t\t// Otherwise we need full lists of their ancestors for comparison\n\t\tcur = a;\n\t\twhile ( (cur = cur.parentNode) ) {\n\t\t\tap.unshift( cur );\n\t\t}\n\t\tcur = b;\n\t\twhile ( (cur = cur.parentNode) ) {\n\t\t\tbp.unshift( cur );\n\t\t}\n\n\t\t// Walk down the tree looking for a discrepancy\n\t\twhile ( ap[i] === bp[i] ) {\n\t\t\ti++;\n\t\t}\n\n\t\treturn i ?\n\t\t\t// Do a sibling check if the nodes have a common ancestor\n\t\t\tsiblingCheck( ap[i], bp[i] ) :\n\n\t\t\t// Otherwise nodes in our document sort first\n\t\t\tap[i] === preferredDoc ? -1 :\n\t\t\tbp[i] === preferredDoc ? 1 :\n\t\t\t0;\n\t};\n\n\treturn document;\n};\n\nSizzle.matches = function( expr, elements ) {\n\treturn Sizzle( expr, null, null, elements );\n};\n\nSizzle.matchesSelector = function( elem, expr ) {\n\t// Set document vars if needed\n\tif ( ( elem.ownerDocument || elem ) !== document ) {\n\t\tsetDocument( elem );\n\t}\n\n\tif ( support.matchesSelector && documentIsHTML &&\n\t\t!nonnativeSelectorCache[ expr + \" \" ] &&\n\t\t( !rbuggyMatches || !rbuggyMatches.test( expr ) ) &&\n\t\t( !rbuggyQSA     || !rbuggyQSA.test( expr ) ) ) {\n\n\t\ttry {\n\t\t\tvar ret = matches.call( elem, expr );\n\n\t\t\t// IE 9's matchesSelector returns false on disconnected nodes\n\t\t\tif ( ret || support.disconnectedMatch ||\n\t\t\t\t\t// As well, disconnected nodes are said to be in a document\n\t\t\t\t\t// fragment in IE 9\n\t\t\t\t\telem.document && elem.document.nodeType !== 11 ) {\n\t\t\t\treturn ret;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tnonnativeSelectorCache( expr, true );\n\t\t}\n\t}\n\n\treturn Sizzle( expr, document, null, [ elem ] ).length > 0;\n};\n\nSizzle.contains = function( context, elem ) {\n\t// Set document vars if needed\n\tif ( ( context.ownerDocument || context ) !== document ) {\n\t\tsetDocument( context );\n\t}\n\treturn contains( context, elem );\n};\n\nSizzle.attr = function( elem, name ) {\n\t// Set document vars if needed\n\tif ( ( elem.ownerDocument || elem ) !== document ) {\n\t\tsetDocument( elem );\n\t}\n\n\tvar fn = Expr.attrHandle[ name.toLowerCase() ],\n\t\t// Don't get fooled by Object.prototype properties (jQuery #13807)\n\t\tval = fn && hasOwn.call( Expr.attrHandle, name.toLowerCase() ) ?\n\t\t\tfn( elem, name, !documentIsHTML ) :\n\t\t\tundefined;\n\n\treturn val !== undefined ?\n\t\tval :\n\t\tsupport.attributes || !documentIsHTML ?\n\t\t\telem.getAttribute( name ) :\n\t\t\t(val = elem.getAttributeNode(name)) && val.specified ?\n\t\t\t\tval.value :\n\t\t\t\tnull;\n};\n\nSizzle.escape = function( sel ) {\n\treturn (sel + \"\").replace( rcssescape, fcssescape );\n};\n\nSizzle.error = function( msg ) {\n\tthrow new Error( \"Syntax error, unrecognized expression: \" + msg );\n};\n\n/**\n * Document sorting and removing duplicates\n * @param {ArrayLike} results\n */\nSizzle.uniqueSort = function( results ) {\n\tvar elem,\n\t\tduplicates = [],\n\t\tj = 0,\n\t\ti = 0;\n\n\t// Unless we *know* we can detect duplicates, assume their presence\n\thasDuplicate = !support.detectDuplicates;\n\tsortInput = !support.sortStable && results.slice( 0 );\n\tresults.sort( sortOrder );\n\n\tif ( hasDuplicate ) {\n\t\twhile ( (elem = results[i++]) ) {\n\t\t\tif ( elem === results[ i ] ) {\n\t\t\t\tj = duplicates.push( i );\n\t\t\t}\n\t\t}\n\t\twhile ( j-- ) {\n\t\t\tresults.splice( duplicates[ j ], 1 );\n\t\t}\n\t}\n\n\t// Clear input after sorting to release objects\n\t// See https://github.com/jquery/sizzle/pull/225\n\tsortInput = null;\n\n\treturn results;\n};\n\n/**\n * Utility function for retrieving the text value of an array of DOM nodes\n * @param {Array|Element} elem\n */\ngetText = Sizzle.getText = function( elem ) {\n\tvar node,\n\t\tret = \"\",\n\t\ti = 0,\n\t\tnodeType = elem.nodeType;\n\n\tif ( !nodeType ) {\n\t\t// If no nodeType, this is expected to be an array\n\t\twhile ( (node = elem[i++]) ) {\n\t\t\t// Do not traverse comment nodes\n\t\t\tret += getText( node );\n\t\t}\n\t} else if ( nodeType === 1 || nodeType === 9 || nodeType === 11 ) {\n\t\t// Use textContent for elements\n\t\t// innerText usage removed for consistency of new lines (jQuery #11153)\n\t\tif ( typeof elem.textContent === \"string\" ) {\n\t\t\treturn elem.textContent;\n\t\t} else {\n\t\t\t// Traverse its children\n\t\t\tfor ( elem = elem.firstChild; elem; elem = elem.nextSibling ) {\n\t\t\t\tret += getText( elem );\n\t\t\t}\n\t\t}\n\t} else if ( nodeType === 3 || nodeType === 4 ) {\n\t\treturn elem.nodeValue;\n\t}\n\t// Do not include comment or processing instruction nodes\n\n\treturn ret;\n};\n\nExpr = Sizzle.selectors = {\n\n\t// Can be adjusted by the user\n\tcacheLength: 50,\n\n\tcreatePseudo: markFunction,\n\n\tmatch: matchExpr,\n\n\tattrHandle: {},\n\n\tfind: {},\n\n\trelative: {\n\t\t\">\": { dir: \"parentNode\", first: true },\n\t\t\" \": { dir: \"parentNode\" },\n\t\t\"+\": { dir: \"previousSibling\", first: true },\n\t\t\"~\": { dir: \"previousSibling\" }\n\t},\n\n\tpreFilter: {\n\t\t\"ATTR\": function( match ) {\n\t\t\tmatch[1] = match[1].replace( runescape, funescape );\n\n\t\t\t// Move the given value to match[3] whether quoted or unquoted\n\t\t\tmatch[3] = ( match[3] || match[4] || match[5] || \"\" ).replace( runescape, funescape );\n\n\t\t\tif ( match[2] === \"~=\" ) {\n\t\t\t\tmatch[3] = \" \" + match[3] + \" \";\n\t\t\t}\n\n\t\t\treturn match.slice( 0, 4 );\n\t\t},\n\n\t\t\"CHILD\": function( match ) {\n\t\t\t/* matches from matchExpr[\"CHILD\"]\n\t\t\t\t1 type (only|nth|...)\n\t\t\t\t2 what (child|of-type)\n\t\t\t\t3 argument (even|odd|\\d*|\\d*n([+-]\\d+)?|...)\n\t\t\t\t4 xn-component of xn+y argument ([+-]?\\d*n|)\n\t\t\t\t5 sign of xn-component\n\t\t\t\t6 x of xn-component\n\t\t\t\t7 sign of y-component\n\t\t\t\t8 y of y-component\n\t\t\t*/\n\t\t\tmatch[1] = match[1].toLowerCase();\n\n\t\t\tif ( match[1].slice( 0, 3 ) === \"nth\" ) {\n\t\t\t\t// nth-* requires argument\n\t\t\t\tif ( !match[3] ) {\n\t\t\t\t\tSizzle.error( match[0] );\n\t\t\t\t}\n\n\t\t\t\t// numeric x and y parameters for Expr.filter.CHILD\n\t\t\t\t// remember that false/true cast respectively to 0/1\n\t\t\t\tmatch[4] = +( match[4] ? match[5] + (match[6] || 1) : 2 * ( match[3] === \"even\" || match[3] === \"odd\" ) );\n\t\t\t\tmatch[5] = +( ( match[7] + match[8] ) || match[3] === \"odd\" );\n\n\t\t\t// other types prohibit arguments\n\t\t\t} else if ( match[3] ) {\n\t\t\t\tSizzle.error( match[0] );\n\t\t\t}\n\n\t\t\treturn match;\n\t\t},\n\n\t\t\"PSEUDO\": function( match ) {\n\t\t\tvar excess,\n\t\t\t\tunquoted = !match[6] && match[2];\n\n\t\t\tif ( matchExpr[\"CHILD\"].test( match[0] ) ) {\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\t// Accept quoted arguments as-is\n\t\t\tif ( match[3] ) {\n\t\t\t\tmatch[2] = match[4] || match[5] || \"\";\n\n\t\t\t// Strip excess characters from unquoted arguments\n\t\t\t} else if ( unquoted && rpseudo.test( unquoted ) &&\n\t\t\t\t// Get excess from tokenize (recursively)\n\t\t\t\t(excess = tokenize( unquoted, true )) &&\n\t\t\t\t// advance to the next closing parenthesis\n\t\t\t\t(excess = unquoted.indexOf( \")\", unquoted.length - excess ) - unquoted.length) ) {\n\n\t\t\t\t// excess is a negative index\n\t\t\t\tmatch[0] = match[0].slice( 0, excess );\n\t\t\t\tmatch[2] = unquoted.slice( 0, excess );\n\t\t\t}\n\n\t\t\t// Return only captures needed by the pseudo filter method (type and argument)\n\t\t\treturn match.slice( 0, 3 );\n\t\t}\n\t},\n\n\tfilter: {\n\n\t\t\"TAG\": function( nodeNameSelector ) {\n\t\t\tvar nodeName = nodeNameSelector.replace( runescape, funescape ).toLowerCase();\n\t\t\treturn nodeNameSelector === \"*\" ?\n\t\t\t\tfunction() { return true; } :\n\t\t\t\tfunction( elem ) {\n\t\t\t\t\treturn elem.nodeName && elem.nodeName.toLowerCase() === nodeName;\n\t\t\t\t};\n\t\t},\n\n\t\t\"CLASS\": function( className ) {\n\t\t\tvar pattern = classCache[ className + \" \" ];\n\n\t\t\treturn pattern ||\n\t\t\t\t(pattern = new RegExp( \"(^|\" + whitespace + \")\" + className + \"(\" + whitespace + \"|$)\" )) &&\n\t\t\t\tclassCache( className, function( elem ) {\n\t\t\t\t\treturn pattern.test( typeof elem.className === \"string\" && elem.className || typeof elem.getAttribute !== \"undefined\" && elem.getAttribute(\"class\") || \"\" );\n\t\t\t\t});\n\t\t},\n\n\t\t\"ATTR\": function( name, operator, check ) {\n\t\t\treturn function( elem ) {\n\t\t\t\tvar result = Sizzle.attr( elem, name );\n\n\t\t\t\tif ( result == null ) {\n\t\t\t\t\treturn operator === \"!=\";\n\t\t\t\t}\n\t\t\t\tif ( !operator ) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tresult += \"\";\n\n\t\t\t\treturn operator === \"=\" ? result === check :\n\t\t\t\t\toperator === \"!=\" ? result !== check :\n\t\t\t\t\toperator === \"^=\" ? check && result.indexOf( check ) === 0 :\n\t\t\t\t\toperator === \"*=\" ? check && result.indexOf( check ) > -1 :\n\t\t\t\t\toperator === \"$=\" ? check && result.slice( -check.length ) === check :\n\t\t\t\t\toperator === \"~=\" ? ( \" \" + result.replace( rwhitespace, \" \" ) + \" \" ).indexOf( check ) > -1 :\n\t\t\t\t\toperator === \"|=\" ? result === check || result.slice( 0, check.length + 1 ) === check + \"-\" :\n\t\t\t\t\tfalse;\n\t\t\t};\n\t\t},\n\n\t\t\"CHILD\": function( type, what, argument, first, last ) {\n\t\t\tvar simple = type.slice( 0, 3 ) !== \"nth\",\n\t\t\t\tforward = type.slice( -4 ) !== \"last\",\n\t\t\t\tofType = what === \"of-type\";\n\n\t\t\treturn first === 1 && last === 0 ?\n\n\t\t\t\t// Shortcut for :nth-*(n)\n\t\t\t\tfunction( elem ) {\n\t\t\t\t\treturn !!elem.parentNode;\n\t\t\t\t} :\n\n\t\t\t\tfunction( elem, context, xml ) {\n\t\t\t\t\tvar cache, uniqueCache, outerCache, node, nodeIndex, start,\n\t\t\t\t\t\tdir = simple !== forward ? \"nextSibling\" : \"previousSibling\",\n\t\t\t\t\t\tparent = elem.parentNode,\n\t\t\t\t\t\tname = ofType && elem.nodeName.toLowerCase(),\n\t\t\t\t\t\tuseCache = !xml && !ofType,\n\t\t\t\t\t\tdiff = false;\n\n\t\t\t\t\tif ( parent ) {\n\n\t\t\t\t\t\t// :(first|last|only)-(child|of-type)\n\t\t\t\t\t\tif ( simple ) {\n\t\t\t\t\t\t\twhile ( dir ) {\n\t\t\t\t\t\t\t\tnode = elem;\n\t\t\t\t\t\t\t\twhile ( (node = node[ dir ]) ) {\n\t\t\t\t\t\t\t\t\tif ( ofType ?\n\t\t\t\t\t\t\t\t\t\tnode.nodeName.toLowerCase() === name :\n\t\t\t\t\t\t\t\t\t\tnode.nodeType === 1 ) {\n\n\t\t\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// Reverse direction for :only-* (if we haven't yet done so)\n\t\t\t\t\t\t\t\tstart = dir = type === \"only\" && !start && \"nextSibling\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tstart = [ forward ? parent.firstChild : parent.lastChild ];\n\n\t\t\t\t\t\t// non-xml :nth-child(...) stores cache data on `parent`\n\t\t\t\t\t\tif ( forward && useCache ) {\n\n\t\t\t\t\t\t\t// Seek `elem` from a previously-cached index\n\n\t\t\t\t\t\t\t// ...in a gzip-friendly way\n\t\t\t\t\t\t\tnode = parent;\n\t\t\t\t\t\t\touterCache = node[ expando ] || (node[ expando ] = {});\n\n\t\t\t\t\t\t\t// Support: IE <9 only\n\t\t\t\t\t\t\t// Defend against cloned attroperties (jQuery gh-1709)\n\t\t\t\t\t\t\tuniqueCache = outerCache[ node.uniqueID ] ||\n\t\t\t\t\t\t\t\t(outerCache[ node.uniqueID ] = {});\n\n\t\t\t\t\t\t\tcache = uniqueCache[ type ] || [];\n\t\t\t\t\t\t\tnodeIndex = cache[ 0 ] === dirruns && cache[ 1 ];\n\t\t\t\t\t\t\tdiff = nodeIndex && cache[ 2 ];\n\t\t\t\t\t\t\tnode = nodeIndex && parent.childNodes[ nodeIndex ];\n\n\t\t\t\t\t\t\twhile ( (node = ++nodeIndex && node && node[ dir ] ||\n\n\t\t\t\t\t\t\t\t// Fallback to seeking `elem` from the start\n\t\t\t\t\t\t\t\t(diff = nodeIndex = 0) || start.pop()) ) {\n\n\t\t\t\t\t\t\t\t// When found, cache indexes on `parent` and break\n\t\t\t\t\t\t\t\tif ( node.nodeType === 1 && ++diff && node === elem ) {\n\t\t\t\t\t\t\t\t\tuniqueCache[ type ] = [ dirruns, nodeIndex, diff ];\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Use previously-cached element index if available\n\t\t\t\t\t\t\tif ( useCache ) {\n\t\t\t\t\t\t\t\t// ...in a gzip-friendly way\n\t\t\t\t\t\t\t\tnode = elem;\n\t\t\t\t\t\t\t\touterCache = node[ expando ] || (node[ expando ] = {});\n\n\t\t\t\t\t\t\t\t// Support: IE <9 only\n\t\t\t\t\t\t\t\t// Defend against cloned attroperties (jQuery gh-1709)\n\t\t\t\t\t\t\t\tuniqueCache = outerCache[ node.uniqueID ] ||\n\t\t\t\t\t\t\t\t\t(outerCache[ node.uniqueID ] = {});\n\n\t\t\t\t\t\t\t\tcache = uniqueCache[ type ] || [];\n\t\t\t\t\t\t\t\tnodeIndex = cache[ 0 ] === dirruns && cache[ 1 ];\n\t\t\t\t\t\t\t\tdiff = nodeIndex;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// xml :nth-child(...)\n\t\t\t\t\t\t\t// or :nth-last-child(...) or :nth(-last)?-of-type(...)\n\t\t\t\t\t\t\tif ( diff === false ) {\n\t\t\t\t\t\t\t\t// Use the same loop as above to seek `elem` from the start\n\t\t\t\t\t\t\t\twhile ( (node = ++nodeIndex && node && node[ dir ] ||\n\t\t\t\t\t\t\t\t\t(diff = nodeIndex = 0) || start.pop()) ) {\n\n\t\t\t\t\t\t\t\t\tif ( ( ofType ?\n\t\t\t\t\t\t\t\t\t\tnode.nodeName.toLowerCase() === name :\n\t\t\t\t\t\t\t\t\t\tnode.nodeType === 1 ) &&\n\t\t\t\t\t\t\t\t\t\t++diff ) {\n\n\t\t\t\t\t\t\t\t\t\t// Cache the index of each encountered element\n\t\t\t\t\t\t\t\t\t\tif ( useCache ) {\n\t\t\t\t\t\t\t\t\t\t\touterCache = node[ expando ] || (node[ expando ] = {});\n\n\t\t\t\t\t\t\t\t\t\t\t// Support: IE <9 only\n\t\t\t\t\t\t\t\t\t\t\t// Defend against cloned attroperties (jQuery gh-1709)\n\t\t\t\t\t\t\t\t\t\t\tuniqueCache = outerCache[ node.uniqueID ] ||\n\t\t\t\t\t\t\t\t\t\t\t\t(outerCache[ node.uniqueID ] = {});\n\n\t\t\t\t\t\t\t\t\t\t\tuniqueCache[ type ] = [ dirruns, diff ];\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tif ( node === elem ) {\n\t\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Incorporate the offset, then check against cycle size\n\t\t\t\t\t\tdiff -= last;\n\t\t\t\t\t\treturn diff === first || ( diff % first === 0 && diff / first >= 0 );\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t},\n\n\t\t\"PSEUDO\": function( pseudo, argument ) {\n\t\t\t// pseudo-class names are case-insensitive\n\t\t\t// http://www.w3.org/TR/selectors/#pseudo-classes\n\t\t\t// Prioritize by case sensitivity in case custom pseudos are added with uppercase letters\n\t\t\t// Remember that setFilters inherits from pseudos\n\t\t\tvar args,\n\t\t\t\tfn = Expr.pseudos[ pseudo ] || Expr.setFilters[ pseudo.toLowerCase() ] ||\n\t\t\t\t\tSizzle.error( \"unsupported pseudo: \" + pseudo );\n\n\t\t\t// The user may use createPseudo to indicate that\n\t\t\t// arguments are needed to create the filter function\n\t\t\t// just as Sizzle does\n\t\t\tif ( fn[ expando ] ) {\n\t\t\t\treturn fn( argument );\n\t\t\t}\n\n\t\t\t// But maintain support for old signatures\n\t\t\tif ( fn.length > 1 ) {\n\t\t\t\targs = [ pseudo, pseudo, \"\", argument ];\n\t\t\t\treturn Expr.setFilters.hasOwnProperty( pseudo.toLowerCase() ) ?\n\t\t\t\t\tmarkFunction(function( seed, matches ) {\n\t\t\t\t\t\tvar idx,\n\t\t\t\t\t\t\tmatched = fn( seed, argument ),\n\t\t\t\t\t\t\ti = matched.length;\n\t\t\t\t\t\twhile ( i-- ) {\n\t\t\t\t\t\t\tidx = indexOf( seed, matched[i] );\n\t\t\t\t\t\t\tseed[ idx ] = !( matches[ idx ] = matched[i] );\n\t\t\t\t\t\t}\n\t\t\t\t\t}) :\n\t\t\t\t\tfunction( elem ) {\n\t\t\t\t\t\treturn fn( elem, 0, args );\n\t\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn fn;\n\t\t}\n\t},\n\n\tpseudos: {\n\t\t// Potentially complex pseudos\n\t\t\"not\": markFunction(function( selector ) {\n\t\t\t// Trim the selector passed to compile\n\t\t\t// to avoid treating leading and trailing\n\t\t\t// spaces as combinators\n\t\t\tvar input = [],\n\t\t\t\tresults = [],\n\t\t\t\tmatcher = compile( selector.replace( rtrim, \"$1\" ) );\n\n\t\t\treturn matcher[ expando ] ?\n\t\t\t\tmarkFunction(function( seed, matches, context, xml ) {\n\t\t\t\t\tvar elem,\n\t\t\t\t\t\tunmatched = matcher( seed, null, xml, [] ),\n\t\t\t\t\t\ti = seed.length;\n\n\t\t\t\t\t// Match elements unmatched by `matcher`\n\t\t\t\t\twhile ( i-- ) {\n\t\t\t\t\t\tif ( (elem = unmatched[i]) ) {\n\t\t\t\t\t\t\tseed[i] = !(matches[i] = elem);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}) :\n\t\t\t\tfunction( elem, context, xml ) {\n\t\t\t\t\tinput[0] = elem;\n\t\t\t\t\tmatcher( input, null, xml, results );\n\t\t\t\t\t// Don't keep the element (issue #299)\n\t\t\t\t\tinput[0] = null;\n\t\t\t\t\treturn !results.pop();\n\t\t\t\t};\n\t\t}),\n\n\t\t\"has\": markFunction(function( selector ) {\n\t\t\treturn function( elem ) {\n\t\t\t\treturn Sizzle( selector, elem ).length > 0;\n\t\t\t};\n\t\t}),\n\n\t\t\"contains\": markFunction(function( text ) {\n\t\t\ttext = text.replace( runescape, funescape );\n\t\t\treturn function( elem ) {\n\t\t\t\treturn ( elem.textContent || getText( elem ) ).indexOf( text ) > -1;\n\t\t\t};\n\t\t}),\n\n\t\t// \"Whether an element is represented by a :lang() selector\n\t\t// is based solely on the element's language value\n\t\t// being equal to the identifier C,\n\t\t// or beginning with the identifier C immediately followed by \"-\".\n\t\t// The matching of C against the element's language value is performed case-insensitively.\n\t\t// The identifier C does not have to be a valid language name.\"\n\t\t// http://www.w3.org/TR/selectors/#lang-pseudo\n\t\t\"lang\": markFunction( function( lang ) {\n\t\t\t// lang value must be a valid identifier\n\t\t\tif ( !ridentifier.test(lang || \"\") ) {\n\t\t\t\tSizzle.error( \"unsupported lang: \" + lang );\n\t\t\t}\n\t\t\tlang = lang.replace( runescape, funescape ).toLowerCase();\n\t\t\treturn function( elem ) {\n\t\t\t\tvar elemLang;\n\t\t\t\tdo {\n\t\t\t\t\tif ( (elemLang = documentIsHTML ?\n\t\t\t\t\t\telem.lang :\n\t\t\t\t\t\telem.getAttribute(\"xml:lang\") || elem.getAttribute(\"lang\")) ) {\n\n\t\t\t\t\t\telemLang = elemLang.toLowerCase();\n\t\t\t\t\t\treturn elemLang === lang || elemLang.indexOf( lang + \"-\" ) === 0;\n\t\t\t\t\t}\n\t\t\t\t} while ( (elem = elem.parentNode) && elem.nodeType === 1 );\n\t\t\t\treturn false;\n\t\t\t};\n\t\t}),\n\n\t\t// Miscellaneous\n\t\t\"target\": function( elem ) {\n\t\t\tvar hash = window.location && window.location.hash;\n\t\t\treturn hash && hash.slice( 1 ) === elem.id;\n\t\t},\n\n\t\t\"root\": function( elem ) {\n\t\t\treturn elem === docElem;\n\t\t},\n\n\t\t\"focus\": function( elem ) {\n\t\t\treturn elem === document.activeElement && (!document.hasFocus || document.hasFocus()) && !!(elem.type || elem.href || ~elem.tabIndex);\n\t\t},\n\n\t\t// Boolean properties\n\t\t\"enabled\": createDisabledPseudo( false ),\n\t\t\"disabled\": createDisabledPseudo( true ),\n\n\t\t\"checked\": function( elem ) {\n\t\t\t// In CSS3, :checked should return both checked and selected elements\n\t\t\t// http://www.w3.org/TR/2011/REC-css3-selectors-20110929/#checked\n\t\t\tvar nodeName = elem.nodeName.toLowerCase();\n\t\t\treturn (nodeName === \"input\" && !!elem.checked) || (nodeName === \"option\" && !!elem.selected);\n\t\t},\n\n\t\t\"selected\": function( elem ) {\n\t\t\t// Accessing this property makes selected-by-default\n\t\t\t// options in Safari work properly\n\t\t\tif ( elem.parentNode ) {\n\t\t\t\telem.parentNode.selectedIndex;\n\t\t\t}\n\n\t\t\treturn elem.selected === true;\n\t\t},\n\n\t\t// Contents\n\t\t\"empty\": function( elem ) {\n\t\t\t// http://www.w3.org/TR/selectors/#empty-pseudo\n\t\t\t// :empty is negated by element (1) or content nodes (text: 3; cdata: 4; entity ref: 5),\n\t\t\t//   but not by others (comment: 8; processing instruction: 7; etc.)\n\t\t\t// nodeType < 6 works because attributes (2) do not appear as children\n\t\t\tfor ( elem = elem.firstChild; elem; elem = elem.nextSibling ) {\n\t\t\t\tif ( elem.nodeType < 6 ) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t},\n\n\t\t\"parent\": function( elem ) {\n\t\t\treturn !Expr.pseudos[\"empty\"]( elem );\n\t\t},\n\n\t\t// Element/input types\n\t\t\"header\": function( elem ) {\n\t\t\treturn rheader.test( elem.nodeName );\n\t\t},\n\n\t\t\"input\": function( elem ) {\n\t\t\treturn rinputs.test( elem.nodeName );\n\t\t},\n\n\t\t\"button\": function( elem ) {\n\t\t\tvar name = elem.nodeName.toLowerCase();\n\t\t\treturn name === \"input\" && elem.type === \"button\" || name === \"button\";\n\t\t},\n\n\t\t\"text\": function( elem ) {\n\t\t\tvar attr;\n\t\t\treturn elem.nodeName.toLowerCase() === \"input\" &&\n\t\t\t\telem.type === \"text\" &&\n\n\t\t\t\t// Support: IE<8\n\t\t\t\t// New HTML5 attribute values (e.g., \"search\") appear with elem.type === \"text\"\n\t\t\t\t( (attr = elem.getAttribute(\"type\")) == null || attr.toLowerCase() === \"text\" );\n\t\t},\n\n\t\t// Position-in-collection\n\t\t\"first\": createPositionalPseudo(function() {\n\t\t\treturn [ 0 ];\n\t\t}),\n\n\t\t\"last\": createPositionalPseudo(function( matchIndexes, length ) {\n\t\t\treturn [ length - 1 ];\n\t\t}),\n\n\t\t\"eq\": createPositionalPseudo(function( matchIndexes, length, argument ) {\n\t\t\treturn [ argument < 0 ? argument + length : argument ];\n\t\t}),\n\n\t\t\"even\": createPositionalPseudo(function( matchIndexes, length ) {\n\t\t\tvar i = 0;\n\t\t\tfor ( ; i < length; i += 2 ) {\n\t\t\t\tmatchIndexes.push( i );\n\t\t\t}\n\t\t\treturn matchIndexes;\n\t\t}),\n\n\t\t\"odd\": createPositionalPseudo(function( matchIndexes, length ) {\n\t\t\tvar i = 1;\n\t\t\tfor ( ; i < length; i += 2 ) {\n\t\t\t\tmatchIndexes.push( i );\n\t\t\t}\n\t\t\treturn matchIndexes;\n\t\t}),\n\n\t\t\"lt\": createPositionalPseudo(function( matchIndexes, length, argument ) {\n\t\t\tvar i = argument < 0 ?\n\t\t\t\targument + length :\n\t\t\t\targument > length ?\n\t\t\t\t\tlength :\n\t\t\t\t\targument;\n\t\t\tfor ( ; --i >= 0; ) {\n\t\t\t\tmatchIndexes.push( i );\n\t\t\t}\n\t\t\treturn matchIndexes;\n\t\t}),\n\n\t\t\"gt\": createPositionalPseudo(function( matchIndexes, length, argument ) {\n\t\t\tvar i = argument < 0 ? argument + length : argument;\n\t\t\tfor ( ; ++i < length; ) {\n\t\t\t\tmatchIndexes.push( i );\n\t\t\t}\n\t\t\treturn matchIndexes;\n\t\t})\n\t}\n};\n\nExpr.pseudos[\"nth\"] = Expr.pseudos[\"eq\"];\n\n// Add button/input type pseudos\nfor ( i in { radio: true, checkbox: true, file: true, password: true, image: true } ) {\n\tExpr.pseudos[ i ] = createInputPseudo( i );\n}\nfor ( i in { submit: true, reset: true } ) {\n\tExpr.pseudos[ i ] = createButtonPseudo( i );\n}\n\n// Easy API for creating new setFilters\nfunction setFilters() {}\nsetFilters.prototype = Expr.filters = Expr.pseudos;\nExpr.setFilters = new setFilters();\n\ntokenize = Sizzle.tokenize = function( selector, parseOnly ) {\n\tvar matched, match, tokens, type,\n\t\tsoFar, groups, preFilters,\n\t\tcached = tokenCache[ selector + \" \" ];\n\n\tif ( cached ) {\n\t\treturn parseOnly ? 0 : cached.slice( 0 );\n\t}\n\n\tsoFar = selector;\n\tgroups = [];\n\tpreFilters = Expr.preFilter;\n\n\twhile ( soFar ) {\n\n\t\t// Comma and first run\n\t\tif ( !matched || (match = rcomma.exec( soFar )) ) {\n\t\t\tif ( match ) {\n\t\t\t\t// Don't consume trailing commas as valid\n\t\t\t\tsoFar = soFar.slice( match[0].length ) || soFar;\n\t\t\t}\n\t\t\tgroups.push( (tokens = []) );\n\t\t}\n\n\t\tmatched = false;\n\n\t\t// Combinators\n\t\tif ( (match = rcombinators.exec( soFar )) ) {\n\t\t\tmatched = match.shift();\n\t\t\ttokens.push({\n\t\t\t\tvalue: matched,\n\t\t\t\t// Cast descendant combinators to space\n\t\t\t\ttype: match[0].replace( rtrim, \" \" )\n\t\t\t});\n\t\t\tsoFar = soFar.slice( matched.length );\n\t\t}\n\n\t\t// Filters\n\t\tfor ( type in Expr.filter ) {\n\t\t\tif ( (match = matchExpr[ type ].exec( soFar )) && (!preFilters[ type ] ||\n\t\t\t\t(match = preFilters[ type ]( match ))) ) {\n\t\t\t\tmatched = match.shift();\n\t\t\t\ttokens.push({\n\t\t\t\t\tvalue: matched,\n\t\t\t\t\ttype: type,\n\t\t\t\t\tmatches: match\n\t\t\t\t});\n\t\t\t\tsoFar = soFar.slice( matched.length );\n\t\t\t}\n\t\t}\n\n\t\tif ( !matched ) {\n\t\t\tbreak;\n\t\t}\n\t}\n\n\t// Return the length of the invalid excess\n\t// if we're just parsing\n\t// Otherwise, throw an error or return tokens\n\treturn parseOnly ?\n\t\tsoFar.length :\n\t\tsoFar ?\n\t\t\tSizzle.error( selector ) :\n\t\t\t// Cache the tokens\n\t\t\ttokenCache( selector, groups ).slice( 0 );\n};\n\nfunction toSelector( tokens ) {\n\tvar i = 0,\n\t\tlen = tokens.length,\n\t\tselector = \"\";\n\tfor ( ; i < len; i++ ) {\n\t\tselector += tokens[i].value;\n\t}\n\treturn selector;\n}\n\nfunction addCombinator( matcher, combinator, base ) {\n\tvar dir = combinator.dir,\n\t\tskip = combinator.next,\n\t\tkey = skip || dir,\n\t\tcheckNonElements = base && key === \"parentNode\",\n\t\tdoneName = done++;\n\n\treturn combinator.first ?\n\t\t// Check against closest ancestor/preceding element\n\t\tfunction( elem, context, xml ) {\n\t\t\twhile ( (elem = elem[ dir ]) ) {\n\t\t\t\tif ( elem.nodeType === 1 || checkNonElements ) {\n\t\t\t\t\treturn matcher( elem, context, xml );\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t} :\n\n\t\t// Check against all ancestor/preceding elements\n\t\tfunction( elem, context, xml ) {\n\t\t\tvar oldCache, uniqueCache, outerCache,\n\t\t\t\tnewCache = [ dirruns, doneName ];\n\n\t\t\t// We can't set arbitrary data on XML nodes, so they don't benefit from combinator caching\n\t\t\tif ( xml ) {\n\t\t\t\twhile ( (elem = elem[ dir ]) ) {\n\t\t\t\t\tif ( elem.nodeType === 1 || checkNonElements ) {\n\t\t\t\t\t\tif ( matcher( elem, context, xml ) ) {\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\twhile ( (elem = elem[ dir ]) ) {\n\t\t\t\t\tif ( elem.nodeType === 1 || checkNonElements ) {\n\t\t\t\t\t\touterCache = elem[ expando ] || (elem[ expando ] = {});\n\n\t\t\t\t\t\t// Support: IE <9 only\n\t\t\t\t\t\t// Defend against cloned attroperties (jQuery gh-1709)\n\t\t\t\t\t\tuniqueCache = outerCache[ elem.uniqueID ] || (outerCache[ elem.uniqueID ] = {});\n\n\t\t\t\t\t\tif ( skip && skip === elem.nodeName.toLowerCase() ) {\n\t\t\t\t\t\t\telem = elem[ dir ] || elem;\n\t\t\t\t\t\t} else if ( (oldCache = uniqueCache[ key ]) &&\n\t\t\t\t\t\t\toldCache[ 0 ] === dirruns && oldCache[ 1 ] === doneName ) {\n\n\t\t\t\t\t\t\t// Assign to newCache so results back-propagate to previous elements\n\t\t\t\t\t\t\treturn (newCache[ 2 ] = oldCache[ 2 ]);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Reuse newcache so results back-propagate to previous elements\n\t\t\t\t\t\t\tuniqueCache[ key ] = newCache;\n\n\t\t\t\t\t\t\t// A match means we're done; a fail means we have to keep checking\n\t\t\t\t\t\t\tif ( (newCache[ 2 ] = matcher( elem, context, xml )) ) {\n\t\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t};\n}\n\nfunction elementMatcher( matchers ) {\n\treturn matchers.length > 1 ?\n\t\tfunction( elem, context, xml ) {\n\t\t\tvar i = matchers.length;\n\t\t\twhile ( i-- ) {\n\t\t\t\tif ( !matchers[i]( elem, context, xml ) ) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t} :\n\t\tmatchers[0];\n}\n\nfunction multipleContexts( selector, contexts, results ) {\n\tvar i = 0,\n\t\tlen = contexts.length;\n\tfor ( ; i < len; i++ ) {\n\t\tSizzle( selector, contexts[i], results );\n\t}\n\treturn results;\n}\n\nfunction condense( unmatched, map, filter, context, xml ) {\n\tvar elem,\n\t\tnewUnmatched = [],\n\t\ti = 0,\n\t\tlen = unmatched.length,\n\t\tmapped = map != null;\n\n\tfor ( ; i < len; i++ ) {\n\t\tif ( (elem = unmatched[i]) ) {\n\t\t\tif ( !filter || filter( elem, context, xml ) ) {\n\t\t\t\tnewUnmatched.push( elem );\n\t\t\t\tif ( mapped ) {\n\t\t\t\t\tmap.push( i );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn newUnmatched;\n}\n\nfunction setMatcher( preFilter, selector, matcher, postFilter, postFinder, postSelector ) {\n\tif ( postFilter && !postFilter[ expando ] ) {\n\t\tpostFilter = setMatcher( postFilter );\n\t}\n\tif ( postFinder && !postFinder[ expando ] ) {\n\t\tpostFinder = setMatcher( postFinder, postSelector );\n\t}\n\treturn markFunction(function( seed, results, context, xml ) {\n\t\tvar temp, i, elem,\n\t\t\tpreMap = [],\n\t\t\tpostMap = [],\n\t\t\tpreexisting = results.length,\n\n\t\t\t// Get initial elements from seed or context\n\t\t\telems = seed || multipleContexts( selector || \"*\", context.nodeType ? [ context ] : context, [] ),\n\n\t\t\t// Prefilter to get matcher input, preserving a map for seed-results synchronization\n\t\t\tmatcherIn = preFilter && ( seed || !selector ) ?\n\t\t\t\tcondense( elems, preMap, preFilter, context, xml ) :\n\t\t\t\telems,\n\n\t\t\tmatcherOut = matcher ?\n\t\t\t\t// If we have a postFinder, or filtered seed, or non-seed postFilter or preexisting results,\n\t\t\t\tpostFinder || ( seed ? preFilter : preexisting || postFilter ) ?\n\n\t\t\t\t\t// ...intermediate processing is necessary\n\t\t\t\t\t[] :\n\n\t\t\t\t\t// ...otherwise use results directly\n\t\t\t\t\tresults :\n\t\t\t\tmatcherIn;\n\n\t\t// Find primary matches\n\t\tif ( matcher ) {\n\t\t\tmatcher( matcherIn, matcherOut, context, xml );\n\t\t}\n\n\t\t// Apply postFilter\n\t\tif ( postFilter ) {\n\t\t\ttemp = condense( matcherOut, postMap );\n\t\t\tpostFilter( temp, [], context, xml );\n\n\t\t\t// Un-match failing elements by moving them back to matcherIn\n\t\t\ti = temp.length;\n\t\t\twhile ( i-- ) {\n\t\t\t\tif ( (elem = temp[i]) ) {\n\t\t\t\t\tmatcherOut[ postMap[i] ] = !(matcherIn[ postMap[i] ] = elem);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif ( seed ) {\n\t\t\tif ( postFinder || preFilter ) {\n\t\t\t\tif ( postFinder ) {\n\t\t\t\t\t// Get the final matcherOut by condensing this intermediate into postFinder contexts\n\t\t\t\t\ttemp = [];\n\t\t\t\t\ti = matcherOut.length;\n\t\t\t\t\twhile ( i-- ) {\n\t\t\t\t\t\tif ( (elem = matcherOut[i]) ) {\n\t\t\t\t\t\t\t// Restore matcherIn since elem is not yet a final match\n\t\t\t\t\t\t\ttemp.push( (matcherIn[i] = elem) );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tpostFinder( null, (matcherOut = []), temp, xml );\n\t\t\t\t}\n\n\t\t\t\t// Move matched elements from seed to results to keep them synchronized\n\t\t\t\ti = matcherOut.length;\n\t\t\t\twhile ( i-- ) {\n\t\t\t\t\tif ( (elem = matcherOut[i]) &&\n\t\t\t\t\t\t(temp = postFinder ? indexOf( seed, elem ) : preMap[i]) > -1 ) {\n\n\t\t\t\t\t\tseed[temp] = !(results[temp] = elem);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t// Add elements to results, through postFinder if defined\n\t\t} else {\n\t\t\tmatcherOut = condense(\n\t\t\t\tmatcherOut === results ?\n\t\t\t\t\tmatcherOut.splice( preexisting, matcherOut.length ) :\n\t\t\t\t\tmatcherOut\n\t\t\t);\n\t\t\tif ( postFinder ) {\n\t\t\t\tpostFinder( null, results, matcherOut, xml );\n\t\t\t} else {\n\t\t\t\tpush.apply( results, matcherOut );\n\t\t\t}\n\t\t}\n\t});\n}\n\nfunction matcherFromTokens( tokens ) {\n\tvar checkContext, matcher, j,\n\t\tlen = tokens.length,\n\t\tleadingRelative = Expr.relative[ tokens[0].type ],\n\t\timplicitRelative = leadingRelative || Expr.relative[\" \"],\n\t\ti = leadingRelative ? 1 : 0,\n\n\t\t// The foundational matcher ensures that elements are reachable from top-level context(s)\n\t\tmatchContext = addCombinator( function( elem ) {\n\t\t\treturn elem === checkContext;\n\t\t}, implicitRelative, true ),\n\t\tmatchAnyContext = addCombinator( function( elem ) {\n\t\t\treturn indexOf( checkContext, elem ) > -1;\n\t\t}, implicitRelative, true ),\n\t\tmatchers = [ function( elem, context, xml ) {\n\t\t\tvar ret = ( !leadingRelative && ( xml || context !== outermostContext ) ) || (\n\t\t\t\t(checkContext = context).nodeType ?\n\t\t\t\t\tmatchContext( elem, context, xml ) :\n\t\t\t\t\tmatchAnyContext( elem, context, xml ) );\n\t\t\t// Avoid hanging onto element (issue #299)\n\t\t\tcheckContext = null;\n\t\t\treturn ret;\n\t\t} ];\n\n\tfor ( ; i < len; i++ ) {\n\t\tif ( (matcher = Expr.relative[ tokens[i].type ]) ) {\n\t\t\tmatchers = [ addCombinator(elementMatcher( matchers ), matcher) ];\n\t\t} else {\n\t\t\tmatcher = Expr.filter[ tokens[i].type ].apply( null, tokens[i].matches );\n\n\t\t\t// Return special upon seeing a positional matcher\n\t\t\tif ( matcher[ expando ] ) {\n\t\t\t\t// Find the next relative operator (if any) for proper handling\n\t\t\t\tj = ++i;\n\t\t\t\tfor ( ; j < len; j++ ) {\n\t\t\t\t\tif ( Expr.relative[ tokens[j].type ] ) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn setMatcher(\n\t\t\t\t\ti > 1 && elementMatcher( matchers ),\n\t\t\t\t\ti > 1 && toSelector(\n\t\t\t\t\t\t// If the preceding token was a descendant combinator, insert an implicit any-element `*`\n\t\t\t\t\t\ttokens.slice( 0, i - 1 ).concat({ value: tokens[ i - 2 ].type === \" \" ? \"*\" : \"\" })\n\t\t\t\t\t).replace( rtrim, \"$1\" ),\n\t\t\t\t\tmatcher,\n\t\t\t\t\ti < j && matcherFromTokens( tokens.slice( i, j ) ),\n\t\t\t\t\tj < len && matcherFromTokens( (tokens = tokens.slice( j )) ),\n\t\t\t\t\tj < len && toSelector( tokens )\n\t\t\t\t);\n\t\t\t}\n\t\t\tmatchers.push( matcher );\n\t\t}\n\t}\n\n\treturn elementMatcher( matchers );\n}\n\nfunction matcherFromGroupMatchers( elementMatchers, setMatchers ) {\n\tvar bySet = setMatchers.length > 0,\n\t\tbyElement = elementMatchers.length > 0,\n\t\tsuperMatcher = function( seed, context, xml, results, outermost ) {\n\t\t\tvar elem, j, matcher,\n\t\t\t\tmatchedCount = 0,\n\t\t\t\ti = \"0\",\n\t\t\t\tunmatched = seed && [],\n\t\t\t\tsetMatched = [],\n\t\t\t\tcontextBackup = outermostContext,\n\t\t\t\t// We must always have either seed elements or outermost context\n\t\t\t\telems = seed || byElement && Expr.find[\"TAG\"]( \"*\", outermost ),\n\t\t\t\t// Use integer dirruns iff this is the outermost matcher\n\t\t\t\tdirrunsUnique = (dirruns += contextBackup == null ? 1 : Math.random() || 0.1),\n\t\t\t\tlen = elems.length;\n\n\t\t\tif ( outermost ) {\n\t\t\t\toutermostContext = context === document || context || outermost;\n\t\t\t}\n\n\t\t\t// Add elements passing elementMatchers directly to results\n\t\t\t// Support: IE<9, Safari\n\t\t\t// Tolerate NodeList properties (IE: \"length\"; Safari: <number>) matching elements by id\n\t\t\tfor ( ; i !== len && (elem = elems[i]) != null; i++ ) {\n\t\t\t\tif ( byElement && elem ) {\n\t\t\t\t\tj = 0;\n\t\t\t\t\tif ( !context && elem.ownerDocument !== document ) {\n\t\t\t\t\t\tsetDocument( elem );\n\t\t\t\t\t\txml = !documentIsHTML;\n\t\t\t\t\t}\n\t\t\t\t\twhile ( (matcher = elementMatchers[j++]) ) {\n\t\t\t\t\t\tif ( matcher( elem, context || document, xml) ) {\n\t\t\t\t\t\t\tresults.push( elem );\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif ( outermost ) {\n\t\t\t\t\t\tdirruns = dirrunsUnique;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Track unmatched elements for set filters\n\t\t\t\tif ( bySet ) {\n\t\t\t\t\t// They will have gone through all possible matchers\n\t\t\t\t\tif ( (elem = !matcher && elem) ) {\n\t\t\t\t\t\tmatchedCount--;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Lengthen the array for every element, matched or not\n\t\t\t\t\tif ( seed ) {\n\t\t\t\t\t\tunmatched.push( elem );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// `i` is now the count of elements visited above, and adding it to `matchedCount`\n\t\t\t// makes the latter nonnegative.\n\t\t\tmatchedCount += i;\n\n\t\t\t// Apply set filters to unmatched elements\n\t\t\t// NOTE: This can be skipped if there are no unmatched elements (i.e., `matchedCount`\n\t\t\t// equals `i`), unless we didn't visit _any_ elements in the above loop because we have\n\t\t\t// no element matchers and no seed.\n\t\t\t// Incrementing an initially-string \"0\" `i` allows `i` to remain a string only in that\n\t\t\t// case, which will result in a \"00\" `matchedCount` that differs from `i` but is also\n\t\t\t// numerically zero.\n\t\t\tif ( bySet && i !== matchedCount ) {\n\t\t\t\tj = 0;\n\t\t\t\twhile ( (matcher = setMatchers[j++]) ) {\n\t\t\t\t\tmatcher( unmatched, setMatched, context, xml );\n\t\t\t\t}\n\n\t\t\t\tif ( seed ) {\n\t\t\t\t\t// Reintegrate element matches to eliminate the need for sorting\n\t\t\t\t\tif ( matchedCount > 0 ) {\n\t\t\t\t\t\twhile ( i-- ) {\n\t\t\t\t\t\t\tif ( !(unmatched[i] || setMatched[i]) ) {\n\t\t\t\t\t\t\t\tsetMatched[i] = pop.call( results );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Discard index placeholder values to get only actual matches\n\t\t\t\t\tsetMatched = condense( setMatched );\n\t\t\t\t}\n\n\t\t\t\t// Add matches to results\n\t\t\t\tpush.apply( results, setMatched );\n\n\t\t\t\t// Seedless set matches succeeding multiple successful matchers stipulate sorting\n\t\t\t\tif ( outermost && !seed && setMatched.length > 0 &&\n\t\t\t\t\t( matchedCount + setMatchers.length ) > 1 ) {\n\n\t\t\t\t\tSizzle.uniqueSort( results );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Override manipulation of globals by nested matchers\n\t\t\tif ( outermost ) {\n\t\t\t\tdirruns = dirrunsUnique;\n\t\t\t\toutermostContext = contextBackup;\n\t\t\t}\n\n\t\t\treturn unmatched;\n\t\t};\n\n\treturn bySet ?\n\t\tmarkFunction( superMatcher ) :\n\t\tsuperMatcher;\n}\n\ncompile = Sizzle.compile = function( selector, match /* Internal Use Only */ ) {\n\tvar i,\n\t\tsetMatchers = [],\n\t\telementMatchers = [],\n\t\tcached = compilerCache[ selector + \" \" ];\n\n\tif ( !cached ) {\n\t\t// Generate a function of recursive functions that can be used to check each element\n\t\tif ( !match ) {\n\t\t\tmatch = tokenize( selector );\n\t\t}\n\t\ti = match.length;\n\t\twhile ( i-- ) {\n\t\t\tcached = matcherFromTokens( match[i] );\n\t\t\tif ( cached[ expando ] ) {\n\t\t\t\tsetMatchers.push( cached );\n\t\t\t} else {\n\t\t\t\telementMatchers.push( cached );\n\t\t\t}\n\t\t}\n\n\t\t// Cache the compiled function\n\t\tcached = compilerCache( selector, matcherFromGroupMatchers( elementMatchers, setMatchers ) );\n\n\t\t// Save selector and tokenization\n\t\tcached.selector = selector;\n\t}\n\treturn cached;\n};\n\n/**\n * A low-level selection function that works with Sizzle's compiled\n *  selector functions\n * @param {String|Function} selector A selector or a pre-compiled\n *  selector function built with Sizzle.compile\n * @param {Element} context\n * @param {Array} [results]\n * @param {Array} [seed] A set of elements to match against\n */\nselect = Sizzle.select = function( selector, context, results, seed ) {\n\tvar i, tokens, token, type, find,\n\t\tcompiled = typeof selector === \"function\" && selector,\n\t\tmatch = !seed && tokenize( (selector = compiled.selector || selector) );\n\n\tresults = results || [];\n\n\t// Try to minimize operations if there is only one selector in the list and no seed\n\t// (the latter of which guarantees us context)\n\tif ( match.length === 1 ) {\n\n\t\t// Reduce context if the leading compound selector is an ID\n\t\ttokens = match[0] = match[0].slice( 0 );\n\t\tif ( tokens.length > 2 && (token = tokens[0]).type === \"ID\" &&\n\t\t\t\tcontext.nodeType === 9 && documentIsHTML && Expr.relative[ tokens[1].type ] ) {\n\n\t\t\tcontext = ( Expr.find[\"ID\"]( token.matches[0].replace(runescape, funescape), context ) || [] )[0];\n\t\t\tif ( !context ) {\n\t\t\t\treturn results;\n\n\t\t\t// Precompiled matchers will still verify ancestry, so step up a level\n\t\t\t} else if ( compiled ) {\n\t\t\t\tcontext = context.parentNode;\n\t\t\t}\n\n\t\t\tselector = selector.slice( tokens.shift().value.length );\n\t\t}\n\n\t\t// Fetch a seed set for right-to-left matching\n\t\ti = matchExpr[\"needsContext\"].test( selector ) ? 0 : tokens.length;\n\t\twhile ( i-- ) {\n\t\t\ttoken = tokens[i];\n\n\t\t\t// Abort if we hit a combinator\n\t\t\tif ( Expr.relative[ (type = token.type) ] ) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif ( (find = Expr.find[ type ]) ) {\n\t\t\t\t// Search, expanding context for leading sibling combinators\n\t\t\t\tif ( (seed = find(\n\t\t\t\t\ttoken.matches[0].replace( runescape, funescape ),\n\t\t\t\t\trsibling.test( tokens[0].type ) && testContext( context.parentNode ) || context\n\t\t\t\t)) ) {\n\n\t\t\t\t\t// If seed is empty or no tokens remain, we can return early\n\t\t\t\t\ttokens.splice( i, 1 );\n\t\t\t\t\tselector = seed.length && toSelector( tokens );\n\t\t\t\t\tif ( !selector ) {\n\t\t\t\t\t\tpush.apply( results, seed );\n\t\t\t\t\t\treturn results;\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Compile and execute a filtering function if one is not provided\n\t// Provide `match` to avoid retokenization if we modified the selector above\n\t( compiled || compile( selector, match ) )(\n\t\tseed,\n\t\tcontext,\n\t\t!documentIsHTML,\n\t\tresults,\n\t\t!context || rsibling.test( selector ) && testContext( context.parentNode ) || context\n\t);\n\treturn results;\n};\n\n// One-time assignments\n\n// Sort stability\nsupport.sortStable = expando.split(\"\").sort( sortOrder ).join(\"\") === expando;\n\n// Support: Chrome 14-35+\n// Always assume duplicates if they aren't passed to the comparison function\nsupport.detectDuplicates = !!hasDuplicate;\n\n// Initialize against the default document\nsetDocument();\n\n// Support: Webkit<537.32 - Safari 6.0.3/Chrome 25 (fixed in Chrome 27)\n// Detached nodes confoundingly follow *each other*\nsupport.sortDetached = assert(function( el ) {\n\t// Should return 1, but returns 4 (following)\n\treturn el.compareDocumentPosition( document.createElement(\"fieldset\") ) & 1;\n});\n\n// Support: IE<8\n// Prevent attribute/property \"interpolation\"\n// https://msdn.microsoft.com/en-us/library/ms536429%28VS.85%29.aspx\nif ( !assert(function( el ) {\n\tel.innerHTML = \"<a href='#'></a>\";\n\treturn el.firstChild.getAttribute(\"href\") === \"#\" ;\n}) ) {\n\taddHandle( \"type|href|height|width\", function( elem, name, isXML ) {\n\t\tif ( !isXML ) {\n\t\t\treturn elem.getAttribute( name, name.toLowerCase() === \"type\" ? 1 : 2 );\n\t\t}\n\t});\n}\n\n// Support: IE<9\n// Use defaultValue in place of getAttribute(\"value\")\nif ( !support.attributes || !assert(function( el ) {\n\tel.innerHTML = \"<input/>\";\n\tel.firstChild.setAttribute( \"value\", \"\" );\n\treturn el.firstChild.getAttribute( \"value\" ) === \"\";\n}) ) {\n\taddHandle( \"value\", function( elem, name, isXML ) {\n\t\tif ( !isXML && elem.nodeName.toLowerCase() === \"input\" ) {\n\t\t\treturn elem.defaultValue;\n\t\t}\n\t});\n}\n\n// Support: IE<9\n// Use getAttributeNode to fetch booleans when getAttribute lies\nif ( !assert(function( el ) {\n\treturn el.getAttribute(\"disabled\") == null;\n}) ) {\n\taddHandle( booleans, function( elem, name, isXML ) {\n\t\tvar val;\n\t\tif ( !isXML ) {\n\t\t\treturn elem[ name ] === true ? name.toLowerCase() :\n\t\t\t\t\t(val = elem.getAttributeNode( name )) && val.specified ?\n\t\t\t\t\tval.value :\n\t\t\t\tnull;\n\t\t}\n\t});\n}\n\nreturn Sizzle;\n\n})( window );\n\n\n\njQuery.find = Sizzle;\njQuery.expr = Sizzle.selectors;\n\n// Deprecated\njQuery.expr[ \":\" ] = jQuery.expr.pseudos;\njQuery.uniqueSort = jQuery.unique = Sizzle.uniqueSort;\njQuery.text = Sizzle.getText;\njQuery.isXMLDoc = Sizzle.isXML;\njQuery.contains = Sizzle.contains;\njQuery.escapeSelector = Sizzle.escape;\n\n\n\n\nvar dir = function( elem, dir, until ) {\n\tvar matched = [],\n\t\ttruncate = until !== undefined;\n\n\twhile ( ( elem = elem[ dir ] ) && elem.nodeType !== 9 ) {\n\t\tif ( elem.nodeType === 1 ) {\n\t\t\tif ( truncate && jQuery( elem ).is( until ) ) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tmatched.push( elem );\n\t\t}\n\t}\n\treturn matched;\n};\n\n\nvar siblings = function( n, elem ) {\n\tvar matched = [];\n\n\tfor ( ; n; n = n.nextSibling ) {\n\t\tif ( n.nodeType === 1 && n !== elem ) {\n\t\t\tmatched.push( n );\n\t\t}\n\t}\n\n\treturn matched;\n};\n\n\nvar rneedsContext = jQuery.expr.match.needsContext;\n\n\n\nfunction nodeName( elem, name ) {\n\n  return elem.nodeName && elem.nodeName.toLowerCase() === name.toLowerCase();\n\n};\nvar rsingleTag = ( /^<([a-z][^\\/\\0>:\\x20\\t\\r\\n\\f]*)[\\x20\\t\\r\\n\\f]*\\/?>(?:<\\/\\1>|)$/i );\n\n\n\n// Implement the identical functionality for filter and not\nfunction winnow( elements, qualifier, not ) {\n\tif ( isFunction( qualifier ) ) {\n\t\treturn jQuery.grep( elements, function( elem, i ) {\n\t\t\treturn !!qualifier.call( elem, i, elem ) !== not;\n\t\t} );\n\t}\n\n\t// Single element\n\tif ( qualifier.nodeType ) {\n\t\treturn jQuery.grep( elements, function( elem ) {\n\t\t\treturn ( elem === qualifier ) !== not;\n\t\t} );\n\t}\n\n\t// Arraylike of elements (jQuery, arguments, Array)\n\tif ( typeof qualifier !== \"string\" ) {\n\t\treturn jQuery.grep( elements, function( elem ) {\n\t\t\treturn ( indexOf.call( qualifier, elem ) > -1 ) !== not;\n\t\t} );\n\t}\n\n\t// Filtered directly for both simple and complex selectors\n\treturn jQuery.filter( qualifier, elements, not );\n}\n\njQuery.filter = function( expr, elems, not ) {\n\tvar elem = elems[ 0 ];\n\n\tif ( not ) {\n\t\texpr = \":not(\" + expr + \")\";\n\t}\n\n\tif ( elems.length === 1 && elem.nodeType === 1 ) {\n\t\treturn jQuery.find.matchesSelector( elem, expr ) ? [ elem ] : [];\n\t}\n\n\treturn jQuery.find.matches( expr, jQuery.grep( elems, function( elem ) {\n\t\treturn elem.nodeType === 1;\n\t} ) );\n};\n\njQuery.fn.extend( {\n\tfind: function( selector ) {\n\t\tvar i, ret,\n\t\t\tlen = this.length,\n\t\t\tself = this;\n\n\t\tif ( typeof selector !== \"string\" ) {\n\t\t\treturn this.pushStack( jQuery( selector ).filter( function() {\n\t\t\t\tfor ( i = 0; i < len; i++ ) {\n\t\t\t\t\tif ( jQuery.contains( self[ i ], this ) ) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} ) );\n\t\t}\n\n\t\tret = this.pushStack( [] );\n\n\t\tfor ( i = 0; i < len; i++ ) {\n\t\t\tjQuery.find( selector, self[ i ], ret );\n\t\t}\n\n\t\treturn len > 1 ? jQuery.uniqueSort( ret ) : ret;\n\t},\n\tfilter: function( selector ) {\n\t\treturn this.pushStack( winnow( this, selector || [], false ) );\n\t},\n\tnot: function( selector ) {\n\t\treturn this.pushStack( winnow( this, selector || [], true ) );\n\t},\n\tis: function( selector ) {\n\t\treturn !!winnow(\n\t\t\tthis,\n\n\t\t\t// If this is a positional/relative selector, check membership in the returned set\n\t\t\t// so $(\"p:first\").is(\"p:last\") won't return true for a doc with two \"p\".\n\t\t\ttypeof selector === \"string\" && rneedsContext.test( selector ) ?\n\t\t\t\tjQuery( selector ) :\n\t\t\t\tselector || [],\n\t\t\tfalse\n\t\t).length;\n\t}\n} );\n\n\n// Initialize a jQuery object\n\n\n// A central reference to the root jQuery(document)\nvar rootjQuery,\n\n\t// A simple way to check for HTML strings\n\t// Prioritize #id over <tag> to avoid XSS via location.hash (#9521)\n\t// Strict HTML recognition (#11290: must start with <)\n\t// Shortcut simple #id case for speed\n\trquickExpr = /^(?:\\s*(<[\\w\\W]+>)[^>]*|#([\\w-]+))$/,\n\n\tinit = jQuery.fn.init = function( selector, context, root ) {\n\t\tvar match, elem;\n\n\t\t// HANDLE: $(\"\"), $(null), $(undefined), $(false)\n\t\tif ( !selector ) {\n\t\t\treturn this;\n\t\t}\n\n\t\t// Method init() accepts an alternate rootjQuery\n\t\t// so migrate can support jQuery.sub (gh-2101)\n\t\troot = root || rootjQuery;\n\n\t\t// Handle HTML strings\n\t\tif ( typeof selector === \"string\" ) {\n\t\t\tif ( selector[ 0 ] === \"<\" &&\n\t\t\t\tselector[ selector.length - 1 ] === \">\" &&\n\t\t\t\tselector.length >= 3 ) {\n\n\t\t\t\t// Assume that strings that start and end with <> are HTML and skip the regex check\n\t\t\t\tmatch = [ null, selector, null ];\n\n\t\t\t} else {\n\t\t\t\tmatch = rquickExpr.exec( selector );\n\t\t\t}\n\n\t\t\t// Match html or make sure no context is specified for #id\n\t\t\tif ( match && ( match[ 1 ] || !context ) ) {\n\n\t\t\t\t// HANDLE: $(html) -> $(array)\n\t\t\t\tif ( match[ 1 ] ) {\n\t\t\t\t\tcontext = context instanceof jQuery ? context[ 0 ] : context;\n\n\t\t\t\t\t// Option to run scripts is true for back-compat\n\t\t\t\t\t// Intentionally let the error be thrown if parseHTML is not present\n\t\t\t\t\tjQuery.merge( this, jQuery.parseHTML(\n\t\t\t\t\t\tmatch[ 1 ],\n\t\t\t\t\t\tcontext && context.nodeType ? context.ownerDocument || context : document,\n\t\t\t\t\t\ttrue\n\t\t\t\t\t) );\n\n\t\t\t\t\t// HANDLE: $(html, props)\n\t\t\t\t\tif ( rsingleTag.test( match[ 1 ] ) && jQuery.isPlainObject( context ) ) {\n\t\t\t\t\t\tfor ( match in context ) {\n\n\t\t\t\t\t\t\t// Properties of context are called as methods if possible\n\t\t\t\t\t\t\tif ( isFunction( this[ match ] ) ) {\n\t\t\t\t\t\t\t\tthis[ match ]( context[ match ] );\n\n\t\t\t\t\t\t\t// ...and otherwise set as attributes\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.attr( match, context[ match ] );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn this;\n\n\t\t\t\t// HANDLE: $(#id)\n\t\t\t\t} else {\n\t\t\t\t\telem = document.getElementById( match[ 2 ] );\n\n\t\t\t\t\tif ( elem ) {\n\n\t\t\t\t\t\t// Inject the element directly into the jQuery object\n\t\t\t\t\t\tthis[ 0 ] = elem;\n\t\t\t\t\t\tthis.length = 1;\n\t\t\t\t\t}\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\n\t\t\t// HANDLE: $(expr, $(...))\n\t\t\t} else if ( !context || context.jquery ) {\n\t\t\t\treturn ( context || root ).find( selector );\n\n\t\t\t// HANDLE: $(expr, context)\n\t\t\t// (which is just equivalent to: $(context).find(expr)\n\t\t\t} else {\n\t\t\t\treturn this.constructor( context ).find( selector );\n\t\t\t}\n\n\t\t// HANDLE: $(DOMElement)\n\t\t} else if ( selector.nodeType ) {\n\t\t\tthis[ 0 ] = selector;\n\t\t\tthis.length = 1;\n\t\t\treturn this;\n\n\t\t// HANDLE: $(function)\n\t\t// Shortcut for document ready\n\t\t} else if ( isFunction( selector ) ) {\n\t\t\treturn root.ready !== undefined ?\n\t\t\t\troot.ready( selector ) :\n\n\t\t\t\t// Execute immediately if ready is not present\n\t\t\t\tselector( jQuery );\n\t\t}\n\n\t\treturn jQuery.makeArray( selector, this );\n\t};\n\n// Give the init function the jQuery prototype for later instantiation\ninit.prototype = jQuery.fn;\n\n// Initialize central reference\nrootjQuery = jQuery( document );\n\n\nvar rparentsprev = /^(?:parents|prev(?:Until|All))/,\n\n\t// Methods guaranteed to produce a unique set when starting from a unique set\n\tguaranteedUnique = {\n\t\tchildren: true,\n\t\tcontents: true,\n\t\tnext: true,\n\t\tprev: true\n\t};\n\njQuery.fn.extend( {\n\thas: function( target ) {\n\t\tvar targets = jQuery( target, this ),\n\t\t\tl = targets.length;\n\n\t\treturn this.filter( function() {\n\t\t\tvar i = 0;\n\t\t\tfor ( ; i < l; i++ ) {\n\t\t\t\tif ( jQuery.contains( this, targets[ i ] ) ) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t} );\n\t},\n\n\tclosest: function( selectors, context ) {\n\t\tvar cur,\n\t\t\ti = 0,\n\t\t\tl = this.length,\n\t\t\tmatched = [],\n\t\t\ttargets = typeof selectors !== \"string\" && jQuery( selectors );\n\n\t\t// Positional selectors never match, since there's no _selection_ context\n\t\tif ( !rneedsContext.test( selectors ) ) {\n\t\t\tfor ( ; i < l; i++ ) {\n\t\t\t\tfor ( cur = this[ i ]; cur && cur !== context; cur = cur.parentNode ) {\n\n\t\t\t\t\t// Always skip document fragments\n\t\t\t\t\tif ( cur.nodeType < 11 && ( targets ?\n\t\t\t\t\t\ttargets.index( cur ) > -1 :\n\n\t\t\t\t\t\t// Don't pass non-elements to Sizzle\n\t\t\t\t\t\tcur.nodeType === 1 &&\n\t\t\t\t\t\t\tjQuery.find.matchesSelector( cur, selectors ) ) ) {\n\n\t\t\t\t\t\tmatched.push( cur );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn this.pushStack( matched.length > 1 ? jQuery.uniqueSort( matched ) : matched );\n\t},\n\n\t// Determine the position of an element within the set\n\tindex: function( elem ) {\n\n\t\t// No argument, return index in parent\n\t\tif ( !elem ) {\n\t\t\treturn ( this[ 0 ] && this[ 0 ].parentNode ) ? this.first().prevAll().length : -1;\n\t\t}\n\n\t\t// Index in selector\n\t\tif ( typeof elem === \"string\" ) {\n\t\t\treturn indexOf.call( jQuery( elem ), this[ 0 ] );\n\t\t}\n\n\t\t// Locate the position of the desired element\n\t\treturn indexOf.call( this,\n\n\t\t\t// If it receives a jQuery object, the first element is used\n\t\t\telem.jquery ? elem[ 0 ] : elem\n\t\t);\n\t},\n\n\tadd: function( selector, context ) {\n\t\treturn this.pushStack(\n\t\t\tjQuery.uniqueSort(\n\t\t\t\tjQuery.merge( this.get(), jQuery( selector, context ) )\n\t\t\t)\n\t\t);\n\t},\n\n\taddBack: function( selector ) {\n\t\treturn this.add( selector == null ?\n\t\t\tthis.prevObject : this.prevObject.filter( selector )\n\t\t);\n\t}\n} );\n\nfunction sibling( cur, dir ) {\n\twhile ( ( cur = cur[ dir ] ) && cur.nodeType !== 1 ) {}\n\treturn cur;\n}\n\njQuery.each( {\n\tparent: function( elem ) {\n\t\tvar parent = elem.parentNode;\n\t\treturn parent && parent.nodeType !== 11 ? parent : null;\n\t},\n\tparents: function( elem ) {\n\t\treturn dir( elem, \"parentNode\" );\n\t},\n\tparentsUntil: function( elem, i, until ) {\n\t\treturn dir( elem, \"parentNode\", until );\n\t},\n\tnext: function( elem ) {\n\t\treturn sibling( elem, \"nextSibling\" );\n\t},\n\tprev: function( elem ) {\n\t\treturn sibling( elem, \"previousSibling\" );\n\t},\n\tnextAll: function( elem ) {\n\t\treturn dir( elem, \"nextSibling\" );\n\t},\n\tprevAll: function( elem ) {\n\t\treturn dir( elem, \"previousSibling\" );\n\t},\n\tnextUntil: function( elem, i, until ) {\n\t\treturn dir( elem, \"nextSibling\", until );\n\t},\n\tprevUntil: function( elem, i, until ) {\n\t\treturn dir( elem, \"previousSibling\", until );\n\t},\n\tsiblings: function( elem ) {\n\t\treturn siblings( ( elem.parentNode || {} ).firstChild, elem );\n\t},\n\tchildren: function( elem ) {\n\t\treturn siblings( elem.firstChild );\n\t},\n\tcontents: function( elem ) {\n\t\tif ( typeof elem.contentDocument !== \"undefined\" ) {\n\t\t\treturn elem.contentDocument;\n\t\t}\n\n\t\t// Support: IE 9 - 11 only, iOS 7 only, Android Browser <=4.3 only\n\t\t// Treat the template element as a regular one in browsers that\n\t\t// don't support it.\n\t\tif ( nodeName( elem, \"template\" ) ) {\n\t\t\telem = elem.content || elem;\n\t\t}\n\n\t\treturn jQuery.merge( [], elem.childNodes );\n\t}\n}, function( name, fn ) {\n\tjQuery.fn[ name ] = function( until, selector ) {\n\t\tvar matched = jQuery.map( this, fn, until );\n\n\t\tif ( name.slice( -5 ) !== \"Until\" ) {\n\t\t\tselector = until;\n\t\t}\n\n\t\tif ( selector && typeof selector === \"string\" ) {\n\t\t\tmatched = jQuery.filter( selector, matched );\n\t\t}\n\n\t\tif ( this.length > 1 ) {\n\n\t\t\t// Remove duplicates\n\t\t\tif ( !guaranteedUnique[ name ] ) {\n\t\t\t\tjQuery.uniqueSort( matched );\n\t\t\t}\n\n\t\t\t// Reverse order for parents* and prev-derivatives\n\t\t\tif ( rparentsprev.test( name ) ) {\n\t\t\t\tmatched.reverse();\n\t\t\t}\n\t\t}\n\n\t\treturn this.pushStack( matched );\n\t};\n} );\nvar rnothtmlwhite = ( /[^\\x20\\t\\r\\n\\f]+/g );\n\n\n\n// Convert String-formatted options into Object-formatted ones\nfunction createOptions( options ) {\n\tvar object = {};\n\tjQuery.each( options.match( rnothtmlwhite ) || [], function( _, flag ) {\n\t\tobject[ flag ] = true;\n\t} );\n\treturn object;\n}\n\n/*\n * Create a callback list using the following parameters:\n *\n *\toptions: an optional list of space-separated options that will change how\n *\t\t\tthe callback list behaves or a more traditional option object\n *\n * By default a callback list will act like an event callback list and can be\n * \"fired\" multiple times.\n *\n * Possible options:\n *\n *\tonce:\t\t\twill ensure the callback list can only be fired once (like a Deferred)\n *\n *\tmemory:\t\t\twill keep track of previous values and will call any callback added\n *\t\t\t\t\tafter the list has been fired right away with the latest \"memorized\"\n *\t\t\t\t\tvalues (like a Deferred)\n *\n *\tunique:\t\t\twill ensure a callback can only be added once (no duplicate in the list)\n *\n *\tstopOnFalse:\tinterrupt callings when a callback returns false\n *\n */\njQuery.Callbacks = function( options ) {\n\n\t// Convert options from String-formatted to Object-formatted if needed\n\t// (we check in cache first)\n\toptions = typeof options === \"string\" ?\n\t\tcreateOptions( options ) :\n\t\tjQuery.extend( {}, options );\n\n\tvar // Flag to know if list is currently firing\n\t\tfiring,\n\n\t\t// Last fire value for non-forgettable lists\n\t\tmemory,\n\n\t\t// Flag to know if list was already fired\n\t\tfired,\n\n\t\t// Flag to prevent firing\n\t\tlocked,\n\n\t\t// Actual callback list\n\t\tlist = [],\n\n\t\t// Queue of execution data for repeatable lists\n\t\tqueue = [],\n\n\t\t// Index of currently firing callback (modified by add/remove as needed)\n\t\tfiringIndex = -1,\n\n\t\t// Fire callbacks\n\t\tfire = function() {\n\n\t\t\t// Enforce single-firing\n\t\t\tlocked = locked || options.once;\n\n\t\t\t// Execute callbacks for all pending executions,\n\t\t\t// respecting firingIndex overrides and runtime changes\n\t\t\tfired = firing = true;\n\t\t\tfor ( ; queue.length; firingIndex = -1 ) {\n\t\t\t\tmemory = queue.shift();\n\t\t\t\twhile ( ++firingIndex < list.length ) {\n\n\t\t\t\t\t// Run callback and check for early termination\n\t\t\t\t\tif ( list[ firingIndex ].apply( memory[ 0 ], memory[ 1 ] ) === false &&\n\t\t\t\t\t\toptions.stopOnFalse ) {\n\n\t\t\t\t\t\t// Jump to end and forget the data so .add doesn't re-fire\n\t\t\t\t\t\tfiringIndex = list.length;\n\t\t\t\t\t\tmemory = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Forget the data if we're done with it\n\t\t\tif ( !options.memory ) {\n\t\t\t\tmemory = false;\n\t\t\t}\n\n\t\t\tfiring = false;\n\n\t\t\t// Clean up if we're done firing for good\n\t\t\tif ( locked ) {\n\n\t\t\t\t// Keep an empty list if we have data for future add calls\n\t\t\t\tif ( memory ) {\n\t\t\t\t\tlist = [];\n\n\t\t\t\t// Otherwise, this object is spent\n\t\t\t\t} else {\n\t\t\t\t\tlist = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// Actual Callbacks object\n\t\tself = {\n\n\t\t\t// Add a callback or a collection of callbacks to the list\n\t\t\tadd: function() {\n\t\t\t\tif ( list ) {\n\n\t\t\t\t\t// If we have memory from a past run, we should fire after adding\n\t\t\t\t\tif ( memory && !firing ) {\n\t\t\t\t\t\tfiringIndex = list.length - 1;\n\t\t\t\t\t\tqueue.push( memory );\n\t\t\t\t\t}\n\n\t\t\t\t\t( function add( args ) {\n\t\t\t\t\t\tjQuery.each( args, function( _, arg ) {\n\t\t\t\t\t\t\tif ( isFunction( arg ) ) {\n\t\t\t\t\t\t\t\tif ( !options.unique || !self.has( arg ) ) {\n\t\t\t\t\t\t\t\t\tlist.push( arg );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else if ( arg && arg.length && toType( arg ) !== \"string\" ) {\n\n\t\t\t\t\t\t\t\t// Inspect recursively\n\t\t\t\t\t\t\t\tadd( arg );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} );\n\t\t\t\t\t} )( arguments );\n\n\t\t\t\t\tif ( memory && !firing ) {\n\t\t\t\t\t\tfire();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t},\n\n\t\t\t// Remove a callback from the list\n\t\t\tremove: function() {\n\t\t\t\tjQuery.each( arguments, function( _, arg ) {\n\t\t\t\t\tvar index;\n\t\t\t\t\twhile ( ( index = jQuery.inArray( arg, list, index ) ) > -1 ) {\n\t\t\t\t\t\tlist.splice( index, 1 );\n\n\t\t\t\t\t\t// Handle firing indexes\n\t\t\t\t\t\tif ( index <= firingIndex ) {\n\t\t\t\t\t\t\tfiringIndex--;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} );\n\t\t\t\treturn this;\n\t\t\t},\n\n\t\t\t// Check if a given callback is in the list.\n\t\t\t// If no argument is given, return whether or not list has callbacks attached.\n\t\t\thas: function( fn ) {\n\t\t\t\treturn fn ?\n\t\t\t\t\tjQuery.inArray( fn, list ) > -1 :\n\t\t\t\t\tlist.length > 0;\n\t\t\t},\n\n\t\t\t// Remove all callbacks from the list\n\t\t\tempty: function() {\n\t\t\t\tif ( list ) {\n\t\t\t\t\tlist = [];\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t},\n\n\t\t\t// Disable .fire and .add\n\t\t\t// Abort any current/pending executions\n\t\t\t// Clear all callbacks and values\n\t\t\tdisable: function() {\n\t\t\t\tlocked = queue = [];\n\t\t\t\tlist = memory = \"\";\n\t\t\t\treturn this;\n\t\t\t},\n\t\t\tdisabled: function() {\n\t\t\t\treturn !list;\n\t\t\t},\n\n\t\t\t// Disable .fire\n\t\t\t// Also disable .add unless we have memory (since it would have no effect)\n\t\t\t// Abort any pending executions\n\t\t\tlock: function() {\n\t\t\t\tlocked = queue = [];\n\t\t\t\tif ( !memory && !firing ) {\n\t\t\t\t\tlist = memory = \"\";\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t},\n\t\t\tlocked: function() {\n\t\t\t\treturn !!locked;\n\t\t\t},\n\n\t\t\t// Call all callbacks with the given context and arguments\n\t\t\tfireWith: function( context, args ) {\n\t\t\t\tif ( !locked ) {\n\t\t\t\t\targs = args || [];\n\t\t\t\t\targs = [ context, args.slice ? args.slice() : args ];\n\t\t\t\t\tqueue.push( args );\n\t\t\t\t\tif ( !firing ) {\n\t\t\t\t\t\tfire();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t},\n\n\t\t\t// Call all the callbacks with the given arguments\n\t\t\tfire: function() {\n\t\t\t\tself.fireWith( this, arguments );\n\t\t\t\treturn this;\n\t\t\t},\n\n\t\t\t// To know if the callbacks have already been called at least once\n\t\t\tfired: function() {\n\t\t\t\treturn !!fired;\n\t\t\t}\n\t\t};\n\n\treturn self;\n};\n\n\nfunction Identity( v ) {\n\treturn v;\n}\nfunction Thrower( ex ) {\n\tthrow ex;\n}\n\nfunction adoptValue( value, resolve, reject, noValue ) {\n\tvar method;\n\n\ttry {\n\n\t\t// Check for promise aspect first to privilege synchronous behavior\n\t\tif ( value && isFunction( ( method = value.promise ) ) ) {\n\t\t\tmethod.call( value ).done( resolve ).fail( reject );\n\n\t\t// Other thenables\n\t\t} else if ( value && isFunction( ( method = value.then ) ) ) {\n\t\t\tmethod.call( value, resolve, reject );\n\n\t\t// Other non-thenables\n\t\t} else {\n\n\t\t\t// Control `resolve` arguments by letting Array#slice cast boolean `noValue` to integer:\n\t\t\t// * false: [ value ].slice( 0 ) => resolve( value )\n\t\t\t// * true: [ value ].slice( 1 ) => resolve()\n\t\t\tresolve.apply( undefined, [ value ].slice( noValue ) );\n\t\t}\n\n\t// For Promises/A+, convert exceptions into rejections\n\t// Since jQuery.when doesn't unwrap thenables, we can skip the extra checks appearing in\n\t// Deferred#then to conditionally suppress rejection.\n\t} catch ( value ) {\n\n\t\t// Support: Android 4.0 only\n\t\t// Strict mode functions invoked without .call/.apply get global-object context\n\t\treject.apply( undefined, [ value ] );\n\t}\n}\n\njQuery.extend( {\n\n\tDeferred: function( func ) {\n\t\tvar tuples = [\n\n\t\t\t\t// action, add listener, callbacks,\n\t\t\t\t// ... .then handlers, argument index, [final state]\n\t\t\t\t[ \"notify\", \"progress\", jQuery.Callbacks( \"memory\" ),\n\t\t\t\t\tjQuery.Callbacks( \"memory\" ), 2 ],\n\t\t\t\t[ \"resolve\", \"done\", jQuery.Callbacks( \"once memory\" ),\n\t\t\t\t\tjQuery.Callbacks( \"once memory\" ), 0, \"resolved\" ],\n\t\t\t\t[ \"reject\", \"fail\", jQuery.Callbacks( \"once memory\" ),\n\t\t\t\t\tjQuery.Callbacks( \"once memory\" ), 1, \"rejected\" ]\n\t\t\t],\n\t\t\tstate = \"pending\",\n\t\t\tpromise = {\n\t\t\t\tstate: function() {\n\t\t\t\t\treturn state;\n\t\t\t\t},\n\t\t\t\talways: function() {\n\t\t\t\t\tdeferred.done( arguments ).fail( arguments );\n\t\t\t\t\treturn this;\n\t\t\t\t},\n\t\t\t\t\"catch\": function( fn ) {\n\t\t\t\t\treturn promise.then( null, fn );\n\t\t\t\t},\n\n\t\t\t\t// Keep pipe for back-compat\n\t\t\t\tpipe: function( /* fnDone, fnFail, fnProgress */ ) {\n\t\t\t\t\tvar fns = arguments;\n\n\t\t\t\t\treturn jQuery.Deferred( function( newDefer ) {\n\t\t\t\t\t\tjQuery.each( tuples, function( i, tuple ) {\n\n\t\t\t\t\t\t\t// Map tuples (progress, done, fail) to arguments (done, fail, progress)\n\t\t\t\t\t\t\tvar fn = isFunction( fns[ tuple[ 4 ] ] ) && fns[ tuple[ 4 ] ];\n\n\t\t\t\t\t\t\t// deferred.progress(function() { bind to newDefer or newDefer.notify })\n\t\t\t\t\t\t\t// deferred.done(function() { bind to newDefer or newDefer.resolve })\n\t\t\t\t\t\t\t// deferred.fail(function() { bind to newDefer or newDefer.reject })\n\t\t\t\t\t\t\tdeferred[ tuple[ 1 ] ]( function() {\n\t\t\t\t\t\t\t\tvar returned = fn && fn.apply( this, arguments );\n\t\t\t\t\t\t\t\tif ( returned && isFunction( returned.promise ) ) {\n\t\t\t\t\t\t\t\t\treturned.promise()\n\t\t\t\t\t\t\t\t\t\t.progress( newDefer.notify )\n\t\t\t\t\t\t\t\t\t\t.done( newDefer.resolve )\n\t\t\t\t\t\t\t\t\t\t.fail( newDefer.reject );\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tnewDefer[ tuple[ 0 ] + \"With\" ](\n\t\t\t\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\t\t\t\tfn ? [ returned ] : arguments\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} );\n\t\t\t\t\t\t} );\n\t\t\t\t\t\tfns = null;\n\t\t\t\t\t} ).promise();\n\t\t\t\t},\n\t\t\t\tthen: function( onFulfilled, onRejected, onProgress ) {\n\t\t\t\t\tvar maxDepth = 0;\n\t\t\t\t\tfunction resolve( depth, deferred, handler, special ) {\n\t\t\t\t\t\treturn function() {\n\t\t\t\t\t\t\tvar that = this,\n\t\t\t\t\t\t\t\targs = arguments,\n\t\t\t\t\t\t\t\tmightThrow = function() {\n\t\t\t\t\t\t\t\t\tvar returned, then;\n\n\t\t\t\t\t\t\t\t\t// Support: Promises/A+ section *******.3\n\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-59\n\t\t\t\t\t\t\t\t\t// Ignore double-resolution attempts\n\t\t\t\t\t\t\t\t\tif ( depth < maxDepth ) {\n\t\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\treturned = handler.apply( that, args );\n\n\t\t\t\t\t\t\t\t\t// Support: Promises/A+ section 2.3.1\n\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-48\n\t\t\t\t\t\t\t\t\tif ( returned === deferred.promise() ) {\n\t\t\t\t\t\t\t\t\t\tthrow new TypeError( \"Thenable self-resolution\" );\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t// Support: Promises/A+ sections 2.3.3.1, 3.5\n\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-54\n\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-75\n\t\t\t\t\t\t\t\t\t// Retrieve `then` only once\n\t\t\t\t\t\t\t\t\tthen = returned &&\n\n\t\t\t\t\t\t\t\t\t\t// Support: Promises/A+ section 2.3.4\n\t\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-64\n\t\t\t\t\t\t\t\t\t\t// Only check objects and functions for thenability\n\t\t\t\t\t\t\t\t\t\t( typeof returned === \"object\" ||\n\t\t\t\t\t\t\t\t\t\t\ttypeof returned === \"function\" ) &&\n\t\t\t\t\t\t\t\t\t\treturned.then;\n\n\t\t\t\t\t\t\t\t\t// Handle a returned thenable\n\t\t\t\t\t\t\t\t\tif ( isFunction( then ) ) {\n\n\t\t\t\t\t\t\t\t\t\t// Special processors (notify) just wait for resolution\n\t\t\t\t\t\t\t\t\t\tif ( special ) {\n\t\t\t\t\t\t\t\t\t\t\tthen.call(\n\t\t\t\t\t\t\t\t\t\t\t\treturned,\n\t\t\t\t\t\t\t\t\t\t\t\tresolve( maxDepth, deferred, Identity, special ),\n\t\t\t\t\t\t\t\t\t\t\t\tresolve( maxDepth, deferred, Thrower, special )\n\t\t\t\t\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\t\t\t\t// Normal processors (resolve) also hook into progress\n\t\t\t\t\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t\t\t\t\t// ...and disregard older resolution values\n\t\t\t\t\t\t\t\t\t\t\tmaxDepth++;\n\n\t\t\t\t\t\t\t\t\t\t\tthen.call(\n\t\t\t\t\t\t\t\t\t\t\t\treturned,\n\t\t\t\t\t\t\t\t\t\t\t\tresolve( maxDepth, deferred, Identity, special ),\n\t\t\t\t\t\t\t\t\t\t\t\tresolve( maxDepth, deferred, Thrower, special ),\n\t\t\t\t\t\t\t\t\t\t\t\tresolve( maxDepth, deferred, Identity,\n\t\t\t\t\t\t\t\t\t\t\t\t\tdeferred.notifyWith )\n\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t// Handle all other returned values\n\t\t\t\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t\t\t\t// Only substitute handlers pass on context\n\t\t\t\t\t\t\t\t\t\t// and multiple values (non-spec behavior)\n\t\t\t\t\t\t\t\t\t\tif ( handler !== Identity ) {\n\t\t\t\t\t\t\t\t\t\t\tthat = undefined;\n\t\t\t\t\t\t\t\t\t\t\targs = [ returned ];\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t// Process the value(s)\n\t\t\t\t\t\t\t\t\t\t// Default process is resolve\n\t\t\t\t\t\t\t\t\t\t( special || deferred.resolveWith )( that, args );\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t},\n\n\t\t\t\t\t\t\t\t// Only normal processors (resolve) catch and reject exceptions\n\t\t\t\t\t\t\t\tprocess = special ?\n\t\t\t\t\t\t\t\t\tmightThrow :\n\t\t\t\t\t\t\t\t\tfunction() {\n\t\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t\tmightThrow();\n\t\t\t\t\t\t\t\t\t\t} catch ( e ) {\n\n\t\t\t\t\t\t\t\t\t\t\tif ( jQuery.Deferred.exceptionHook ) {\n\t\t\t\t\t\t\t\t\t\t\t\tjQuery.Deferred.exceptionHook( e,\n\t\t\t\t\t\t\t\t\t\t\t\t\tprocess.stackTrace );\n\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\t// Support: Promises/A+ section *******.4.1\n\t\t\t\t\t\t\t\t\t\t\t// https://promisesaplus.com/#point-61\n\t\t\t\t\t\t\t\t\t\t\t// Ignore post-resolution exceptions\n\t\t\t\t\t\t\t\t\t\t\tif ( depth + 1 >= maxDepth ) {\n\n\t\t\t\t\t\t\t\t\t\t\t\t// Only substitute handlers pass on context\n\t\t\t\t\t\t\t\t\t\t\t\t// and multiple values (non-spec behavior)\n\t\t\t\t\t\t\t\t\t\t\t\tif ( handler !== Thrower ) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tthat = undefined;\n\t\t\t\t\t\t\t\t\t\t\t\t\targs = [ e ];\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\t\tdeferred.rejectWith( that, args );\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t};\n\n\t\t\t\t\t\t\t// Support: Promises/A+ section *******.1\n\t\t\t\t\t\t\t// https://promisesaplus.com/#point-57\n\t\t\t\t\t\t\t// Re-resolve promises immediately to dodge false rejection from\n\t\t\t\t\t\t\t// subsequent errors\n\t\t\t\t\t\t\tif ( depth ) {\n\t\t\t\t\t\t\t\tprocess();\n\t\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t\t// Call an optional hook to record the stack, in case of exception\n\t\t\t\t\t\t\t\t// since it's otherwise lost when execution goes async\n\t\t\t\t\t\t\t\tif ( jQuery.Deferred.getStackHook ) {\n\t\t\t\t\t\t\t\t\tprocess.stackTrace = jQuery.Deferred.getStackHook();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\twindow.setTimeout( process );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\treturn jQuery.Deferred( function( newDefer ) {\n\n\t\t\t\t\t\t// progress_handlers.add( ... )\n\t\t\t\t\t\ttuples[ 0 ][ 3 ].add(\n\t\t\t\t\t\t\tresolve(\n\t\t\t\t\t\t\t\t0,\n\t\t\t\t\t\t\t\tnewDefer,\n\t\t\t\t\t\t\t\tisFunction( onProgress ) ?\n\t\t\t\t\t\t\t\t\tonProgress :\n\t\t\t\t\t\t\t\t\tIdentity,\n\t\t\t\t\t\t\t\tnewDefer.notifyWith\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// fulfilled_handlers.add( ... )\n\t\t\t\t\t\ttuples[ 1 ][ 3 ].add(\n\t\t\t\t\t\t\tresolve(\n\t\t\t\t\t\t\t\t0,\n\t\t\t\t\t\t\t\tnewDefer,\n\t\t\t\t\t\t\t\tisFunction( onFulfilled ) ?\n\t\t\t\t\t\t\t\t\tonFulfilled :\n\t\t\t\t\t\t\t\t\tIdentity\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// rejected_handlers.add( ... )\n\t\t\t\t\t\ttuples[ 2 ][ 3 ].add(\n\t\t\t\t\t\t\tresolve(\n\t\t\t\t\t\t\t\t0,\n\t\t\t\t\t\t\t\tnewDefer,\n\t\t\t\t\t\t\t\tisFunction( onRejected ) ?\n\t\t\t\t\t\t\t\t\tonRejected :\n\t\t\t\t\t\t\t\t\tThrower\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t);\n\t\t\t\t\t} ).promise();\n\t\t\t\t},\n\n\t\t\t\t// Get a promise for this deferred\n\t\t\t\t// If obj is provided, the promise aspect is added to the object\n\t\t\t\tpromise: function( obj ) {\n\t\t\t\t\treturn obj != null ? jQuery.extend( obj, promise ) : promise;\n\t\t\t\t}\n\t\t\t},\n\t\t\tdeferred = {};\n\n\t\t// Add list-specific methods\n\t\tjQuery.each( tuples, function( i, tuple ) {\n\t\t\tvar list = tuple[ 2 ],\n\t\t\t\tstateString = tuple[ 5 ];\n\n\t\t\t// promise.progress = list.add\n\t\t\t// promise.done = list.add\n\t\t\t// promise.fail = list.add\n\t\t\tpromise[ tuple[ 1 ] ] = list.add;\n\n\t\t\t// Handle state\n\t\t\tif ( stateString ) {\n\t\t\t\tlist.add(\n\t\t\t\t\tfunction() {\n\n\t\t\t\t\t\t// state = \"resolved\" (i.e., fulfilled)\n\t\t\t\t\t\t// state = \"rejected\"\n\t\t\t\t\t\tstate = stateString;\n\t\t\t\t\t},\n\n\t\t\t\t\t// rejected_callbacks.disable\n\t\t\t\t\t// fulfilled_callbacks.disable\n\t\t\t\t\ttuples[ 3 - i ][ 2 ].disable,\n\n\t\t\t\t\t// rejected_handlers.disable\n\t\t\t\t\t// fulfilled_handlers.disable\n\t\t\t\t\ttuples[ 3 - i ][ 3 ].disable,\n\n\t\t\t\t\t// progress_callbacks.lock\n\t\t\t\t\ttuples[ 0 ][ 2 ].lock,\n\n\t\t\t\t\t// progress_handlers.lock\n\t\t\t\t\ttuples[ 0 ][ 3 ].lock\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t// progress_handlers.fire\n\t\t\t// fulfilled_handlers.fire\n\t\t\t// rejected_handlers.fire\n\t\t\tlist.add( tuple[ 3 ].fire );\n\n\t\t\t// deferred.notify = function() { deferred.notifyWith(...) }\n\t\t\t// deferred.resolve = function() { deferred.resolveWith(...) }\n\t\t\t// deferred.reject = function() { deferred.rejectWith(...) }\n\t\t\tdeferred[ tuple[ 0 ] ] = function() {\n\t\t\t\tdeferred[ tuple[ 0 ] + \"With\" ]( this === deferred ? undefined : this, arguments );\n\t\t\t\treturn this;\n\t\t\t};\n\n\t\t\t// deferred.notifyWith = list.fireWith\n\t\t\t// deferred.resolveWith = list.fireWith\n\t\t\t// deferred.rejectWith = list.fireWith\n\t\t\tdeferred[ tuple[ 0 ] + \"With\" ] = list.fireWith;\n\t\t} );\n\n\t\t// Make the deferred a promise\n\t\tpromise.promise( deferred );\n\n\t\t// Call given func if any\n\t\tif ( func ) {\n\t\t\tfunc.call( deferred, deferred );\n\t\t}\n\n\t\t// All done!\n\t\treturn deferred;\n\t},\n\n\t// Deferred helper\n\twhen: function( singleValue ) {\n\t\tvar\n\n\t\t\t// count of uncompleted subordinates\n\t\t\tremaining = arguments.length,\n\n\t\t\t// count of unprocessed arguments\n\t\t\ti = remaining,\n\n\t\t\t// subordinate fulfillment data\n\t\t\tresolveContexts = Array( i ),\n\t\t\tresolveValues = slice.call( arguments ),\n\n\t\t\t// the master Deferred\n\t\t\tmaster = jQuery.Deferred(),\n\n\t\t\t// subordinate callback factory\n\t\t\tupdateFunc = function( i ) {\n\t\t\t\treturn function( value ) {\n\t\t\t\t\tresolveContexts[ i ] = this;\n\t\t\t\t\tresolveValues[ i ] = arguments.length > 1 ? slice.call( arguments ) : value;\n\t\t\t\t\tif ( !( --remaining ) ) {\n\t\t\t\t\t\tmaster.resolveWith( resolveContexts, resolveValues );\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t};\n\n\t\t// Single- and empty arguments are adopted like Promise.resolve\n\t\tif ( remaining <= 1 ) {\n\t\t\tadoptValue( singleValue, master.done( updateFunc( i ) ).resolve, master.reject,\n\t\t\t\t!remaining );\n\n\t\t\t// Use .then() to unwrap secondary thenables (cf. gh-3000)\n\t\t\tif ( master.state() === \"pending\" ||\n\t\t\t\tisFunction( resolveValues[ i ] && resolveValues[ i ].then ) ) {\n\n\t\t\t\treturn master.then();\n\t\t\t}\n\t\t}\n\n\t\t// Multiple arguments are aggregated like Promise.all array elements\n\t\twhile ( i-- ) {\n\t\t\tadoptValue( resolveValues[ i ], updateFunc( i ), master.reject );\n\t\t}\n\n\t\treturn master.promise();\n\t}\n} );\n\n\n// These usually indicate a programmer mistake during development,\n// warn about them ASAP rather than swallowing them by default.\nvar rerrorNames = /^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;\n\njQuery.Deferred.exceptionHook = function( error, stack ) {\n\n\t// Support: IE 8 - 9 only\n\t// Console exists when dev tools are open, which can happen at any time\n\tif ( window.console && window.console.warn && error && rerrorNames.test( error.name ) ) {\n\t\twindow.console.warn( \"jQuery.Deferred exception: \" + error.message, error.stack, stack );\n\t}\n};\n\n\n\n\njQuery.readyException = function( error ) {\n\twindow.setTimeout( function() {\n\t\tthrow error;\n\t} );\n};\n\n\n\n\n// The deferred used on DOM ready\nvar readyList = jQuery.Deferred();\n\njQuery.fn.ready = function( fn ) {\n\n\treadyList\n\t\t.then( fn )\n\n\t\t// Wrap jQuery.readyException in a function so that the lookup\n\t\t// happens at the time of error handling instead of callback\n\t\t// registration.\n\t\t.catch( function( error ) {\n\t\t\tjQuery.readyException( error );\n\t\t} );\n\n\treturn this;\n};\n\njQuery.extend( {\n\n\t// Is the DOM ready to be used? Set to true once it occurs.\n\tisReady: false,\n\n\t// A counter to track how many items to wait for before\n\t// the ready event fires. See #6781\n\treadyWait: 1,\n\n\t// Handle when the DOM is ready\n\tready: function( wait ) {\n\n\t\t// Abort if there are pending holds or we're already ready\n\t\tif ( wait === true ? --jQuery.readyWait : jQuery.isReady ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Remember that the DOM is ready\n\t\tjQuery.isReady = true;\n\n\t\t// If a normal DOM Ready event fired, decrement, and wait if need be\n\t\tif ( wait !== true && --jQuery.readyWait > 0 ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// If there are functions bound, to execute\n\t\treadyList.resolveWith( document, [ jQuery ] );\n\t}\n} );\n\njQuery.ready.then = readyList.then;\n\n// The ready event handler and self cleanup method\nfunction completed() {\n\tdocument.removeEventListener( \"DOMContentLoaded\", completed );\n\twindow.removeEventListener( \"load\", completed );\n\tjQuery.ready();\n}\n\n// Catch cases where $(document).ready() is called\n// after the browser event has already occurred.\n// Support: IE <=9 - 10 only\n// Older IE sometimes signals \"interactive\" too soon\nif ( document.readyState === \"complete\" ||\n\t( document.readyState !== \"loading\" && !document.documentElement.doScroll ) ) {\n\n\t// Handle it asynchronously to allow scripts the opportunity to delay ready\n\twindow.setTimeout( jQuery.ready );\n\n} else {\n\n\t// Use the handy event callback\n\tdocument.addEventListener( \"DOMContentLoaded\", completed );\n\n\t// A fallback to window.onload, that will always work\n\twindow.addEventListener( \"load\", completed );\n}\n\n\n\n\n// Multifunctional method to get and set values of a collection\n// The value/s can optionally be executed if it's a function\nvar access = function( elems, fn, key, value, chainable, emptyGet, raw ) {\n\tvar i = 0,\n\t\tlen = elems.length,\n\t\tbulk = key == null;\n\n\t// Sets many values\n\tif ( toType( key ) === \"object\" ) {\n\t\tchainable = true;\n\t\tfor ( i in key ) {\n\t\t\taccess( elems, fn, i, key[ i ], true, emptyGet, raw );\n\t\t}\n\n\t// Sets one value\n\t} else if ( value !== undefined ) {\n\t\tchainable = true;\n\n\t\tif ( !isFunction( value ) ) {\n\t\t\traw = true;\n\t\t}\n\n\t\tif ( bulk ) {\n\n\t\t\t// Bulk operations run against the entire set\n\t\t\tif ( raw ) {\n\t\t\t\tfn.call( elems, value );\n\t\t\t\tfn = null;\n\n\t\t\t// ...except when executing function values\n\t\t\t} else {\n\t\t\t\tbulk = fn;\n\t\t\t\tfn = function( elem, key, value ) {\n\t\t\t\t\treturn bulk.call( jQuery( elem ), value );\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\tif ( fn ) {\n\t\t\tfor ( ; i < len; i++ ) {\n\t\t\t\tfn(\n\t\t\t\t\telems[ i ], key, raw ?\n\t\t\t\t\tvalue :\n\t\t\t\t\tvalue.call( elems[ i ], i, fn( elems[ i ], key ) )\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif ( chainable ) {\n\t\treturn elems;\n\t}\n\n\t// Gets\n\tif ( bulk ) {\n\t\treturn fn.call( elems );\n\t}\n\n\treturn len ? fn( elems[ 0 ], key ) : emptyGet;\n};\n\n\n// Matches dashed string for camelizing\nvar rmsPrefix = /^-ms-/,\n\trdashAlpha = /-([a-z])/g;\n\n// Used by camelCase as callback to replace()\nfunction fcamelCase( all, letter ) {\n\treturn letter.toUpperCase();\n}\n\n// Convert dashed to camelCase; used by the css and data modules\n// Support: IE <=9 - 11, Edge 12 - 15\n// Microsoft forgot to hump their vendor prefix (#9572)\nfunction camelCase( string ) {\n\treturn string.replace( rmsPrefix, \"ms-\" ).replace( rdashAlpha, fcamelCase );\n}\nvar acceptData = function( owner ) {\n\n\t// Accepts only:\n\t//  - Node\n\t//    - Node.ELEMENT_NODE\n\t//    - Node.DOCUMENT_NODE\n\t//  - Object\n\t//    - Any\n\treturn owner.nodeType === 1 || owner.nodeType === 9 || !( +owner.nodeType );\n};\n\n\n\n\nfunction Data() {\n\tthis.expando = jQuery.expando + Data.uid++;\n}\n\nData.uid = 1;\n\nData.prototype = {\n\n\tcache: function( owner ) {\n\n\t\t// Check if the owner object already has a cache\n\t\tvar value = owner[ this.expando ];\n\n\t\t// If not, create one\n\t\tif ( !value ) {\n\t\t\tvalue = {};\n\n\t\t\t// We can accept data for non-element nodes in modern browsers,\n\t\t\t// but we should not, see #8335.\n\t\t\t// Always return an empty object.\n\t\t\tif ( acceptData( owner ) ) {\n\n\t\t\t\t// If it is a node unlikely to be stringify-ed or looped over\n\t\t\t\t// use plain assignment\n\t\t\t\tif ( owner.nodeType ) {\n\t\t\t\t\towner[ this.expando ] = value;\n\n\t\t\t\t// Otherwise secure it in a non-enumerable property\n\t\t\t\t// configurable must be true to allow the property to be\n\t\t\t\t// deleted when data is removed\n\t\t\t\t} else {\n\t\t\t\t\tObject.defineProperty( owner, this.expando, {\n\t\t\t\t\t\tvalue: value,\n\t\t\t\t\t\tconfigurable: true\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn value;\n\t},\n\tset: function( owner, data, value ) {\n\t\tvar prop,\n\t\t\tcache = this.cache( owner );\n\n\t\t// Handle: [ owner, key, value ] args\n\t\t// Always use camelCase key (gh-2257)\n\t\tif ( typeof data === \"string\" ) {\n\t\t\tcache[ camelCase( data ) ] = value;\n\n\t\t// Handle: [ owner, { properties } ] args\n\t\t} else {\n\n\t\t\t// Copy the properties one-by-one to the cache object\n\t\t\tfor ( prop in data ) {\n\t\t\t\tcache[ camelCase( prop ) ] = data[ prop ];\n\t\t\t}\n\t\t}\n\t\treturn cache;\n\t},\n\tget: function( owner, key ) {\n\t\treturn key === undefined ?\n\t\t\tthis.cache( owner ) :\n\n\t\t\t// Always use camelCase key (gh-2257)\n\t\t\towner[ this.expando ] && owner[ this.expando ][ camelCase( key ) ];\n\t},\n\taccess: function( owner, key, value ) {\n\n\t\t// In cases where either:\n\t\t//\n\t\t//   1. No key was specified\n\t\t//   2. A string key was specified, but no value provided\n\t\t//\n\t\t// Take the \"read\" path and allow the get method to determine\n\t\t// which value to return, respectively either:\n\t\t//\n\t\t//   1. The entire cache object\n\t\t//   2. The data stored at the key\n\t\t//\n\t\tif ( key === undefined ||\n\t\t\t\t( ( key && typeof key === \"string\" ) && value === undefined ) ) {\n\n\t\t\treturn this.get( owner, key );\n\t\t}\n\n\t\t// When the key is not a string, or both a key and value\n\t\t// are specified, set or extend (existing objects) with either:\n\t\t//\n\t\t//   1. An object of properties\n\t\t//   2. A key and value\n\t\t//\n\t\tthis.set( owner, key, value );\n\n\t\t// Since the \"set\" path can have two possible entry points\n\t\t// return the expected data based on which path was taken[*]\n\t\treturn value !== undefined ? value : key;\n\t},\n\tremove: function( owner, key ) {\n\t\tvar i,\n\t\t\tcache = owner[ this.expando ];\n\n\t\tif ( cache === undefined ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( key !== undefined ) {\n\n\t\t\t// Support array or space separated string of keys\n\t\t\tif ( Array.isArray( key ) ) {\n\n\t\t\t\t// If key is an array of keys...\n\t\t\t\t// We always set camelCase keys, so remove that.\n\t\t\t\tkey = key.map( camelCase );\n\t\t\t} else {\n\t\t\t\tkey = camelCase( key );\n\n\t\t\t\t// If a key with the spaces exists, use it.\n\t\t\t\t// Otherwise, create an array by matching non-whitespace\n\t\t\t\tkey = key in cache ?\n\t\t\t\t\t[ key ] :\n\t\t\t\t\t( key.match( rnothtmlwhite ) || [] );\n\t\t\t}\n\n\t\t\ti = key.length;\n\n\t\t\twhile ( i-- ) {\n\t\t\t\tdelete cache[ key[ i ] ];\n\t\t\t}\n\t\t}\n\n\t\t// Remove the expando if there's no more data\n\t\tif ( key === undefined || jQuery.isEmptyObject( cache ) ) {\n\n\t\t\t// Support: Chrome <=35 - 45\n\t\t\t// Webkit & Blink performance suffers when deleting properties\n\t\t\t// from DOM nodes, so set to undefined instead\n\t\t\t// https://bugs.chromium.org/p/chromium/issues/detail?id=378607 (bug restricted)\n\t\t\tif ( owner.nodeType ) {\n\t\t\t\towner[ this.expando ] = undefined;\n\t\t\t} else {\n\t\t\t\tdelete owner[ this.expando ];\n\t\t\t}\n\t\t}\n\t},\n\thasData: function( owner ) {\n\t\tvar cache = owner[ this.expando ];\n\t\treturn cache !== undefined && !jQuery.isEmptyObject( cache );\n\t}\n};\nvar dataPriv = new Data();\n\nvar dataUser = new Data();\n\n\n\n//\tImplementation Summary\n//\n//\t1. Enforce API surface and semantic compatibility with 1.9.x branch\n//\t2. Improve the module's maintainability by reducing the storage\n//\t\tpaths to a single mechanism.\n//\t3. Use the same single mechanism to support \"private\" and \"user\" data.\n//\t4. _Never_ expose \"private\" data to user code (TODO: Drop _data, _removeData)\n//\t5. Avoid exposing implementation details on user objects (eg. expando properties)\n//\t6. Provide a clear path for implementation upgrade to WeakMap in 2014\n\nvar rbrace = /^(?:\\{[\\w\\W]*\\}|\\[[\\w\\W]*\\])$/,\n\trmultiDash = /[A-Z]/g;\n\nfunction getData( data ) {\n\tif ( data === \"true\" ) {\n\t\treturn true;\n\t}\n\n\tif ( data === \"false\" ) {\n\t\treturn false;\n\t}\n\n\tif ( data === \"null\" ) {\n\t\treturn null;\n\t}\n\n\t// Only convert to a number if it doesn't change the string\n\tif ( data === +data + \"\" ) {\n\t\treturn +data;\n\t}\n\n\tif ( rbrace.test( data ) ) {\n\t\treturn JSON.parse( data );\n\t}\n\n\treturn data;\n}\n\nfunction dataAttr( elem, key, data ) {\n\tvar name;\n\n\t// If nothing was found internally, try to fetch any\n\t// data from the HTML5 data-* attribute\n\tif ( data === undefined && elem.nodeType === 1 ) {\n\t\tname = \"data-\" + key.replace( rmultiDash, \"-$&\" ).toLowerCase();\n\t\tdata = elem.getAttribute( name );\n\n\t\tif ( typeof data === \"string\" ) {\n\t\t\ttry {\n\t\t\t\tdata = getData( data );\n\t\t\t} catch ( e ) {}\n\n\t\t\t// Make sure we set the data so it isn't changed later\n\t\t\tdataUser.set( elem, key, data );\n\t\t} else {\n\t\t\tdata = undefined;\n\t\t}\n\t}\n\treturn data;\n}\n\njQuery.extend( {\n\thasData: function( elem ) {\n\t\treturn dataUser.hasData( elem ) || dataPriv.hasData( elem );\n\t},\n\n\tdata: function( elem, name, data ) {\n\t\treturn dataUser.access( elem, name, data );\n\t},\n\n\tremoveData: function( elem, name ) {\n\t\tdataUser.remove( elem, name );\n\t},\n\n\t// TODO: Now that all calls to _data and _removeData have been replaced\n\t// with direct calls to dataPriv methods, these can be deprecated.\n\t_data: function( elem, name, data ) {\n\t\treturn dataPriv.access( elem, name, data );\n\t},\n\n\t_removeData: function( elem, name ) {\n\t\tdataPriv.remove( elem, name );\n\t}\n} );\n\njQuery.fn.extend( {\n\tdata: function( key, value ) {\n\t\tvar i, name, data,\n\t\t\telem = this[ 0 ],\n\t\t\tattrs = elem && elem.attributes;\n\n\t\t// Gets all values\n\t\tif ( key === undefined ) {\n\t\t\tif ( this.length ) {\n\t\t\t\tdata = dataUser.get( elem );\n\n\t\t\t\tif ( elem.nodeType === 1 && !dataPriv.get( elem, \"hasDataAttrs\" ) ) {\n\t\t\t\t\ti = attrs.length;\n\t\t\t\t\twhile ( i-- ) {\n\n\t\t\t\t\t\t// Support: IE 11 only\n\t\t\t\t\t\t// The attrs elements can be null (#14894)\n\t\t\t\t\t\tif ( attrs[ i ] ) {\n\t\t\t\t\t\t\tname = attrs[ i ].name;\n\t\t\t\t\t\t\tif ( name.indexOf( \"data-\" ) === 0 ) {\n\t\t\t\t\t\t\t\tname = camelCase( name.slice( 5 ) );\n\t\t\t\t\t\t\t\tdataAttr( elem, name, data[ name ] );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tdataPriv.set( elem, \"hasDataAttrs\", true );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn data;\n\t\t}\n\n\t\t// Sets multiple values\n\t\tif ( typeof key === \"object\" ) {\n\t\t\treturn this.each( function() {\n\t\t\t\tdataUser.set( this, key );\n\t\t\t} );\n\t\t}\n\n\t\treturn access( this, function( value ) {\n\t\t\tvar data;\n\n\t\t\t// The calling jQuery object (element matches) is not empty\n\t\t\t// (and therefore has an element appears at this[ 0 ]) and the\n\t\t\t// `value` parameter was not undefined. An empty jQuery object\n\t\t\t// will result in `undefined` for elem = this[ 0 ] which will\n\t\t\t// throw an exception if an attempt to read a data cache is made.\n\t\t\tif ( elem && value === undefined ) {\n\n\t\t\t\t// Attempt to get data from the cache\n\t\t\t\t// The key will always be camelCased in Data\n\t\t\t\tdata = dataUser.get( elem, key );\n\t\t\t\tif ( data !== undefined ) {\n\t\t\t\t\treturn data;\n\t\t\t\t}\n\n\t\t\t\t// Attempt to \"discover\" the data in\n\t\t\t\t// HTML5 custom data-* attrs\n\t\t\t\tdata = dataAttr( elem, key );\n\t\t\t\tif ( data !== undefined ) {\n\t\t\t\t\treturn data;\n\t\t\t\t}\n\n\t\t\t\t// We tried really hard, but the data doesn't exist.\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Set the data...\n\t\t\tthis.each( function() {\n\n\t\t\t\t// We always store the camelCased key\n\t\t\t\tdataUser.set( this, key, value );\n\t\t\t} );\n\t\t}, null, value, arguments.length > 1, null, true );\n\t},\n\n\tremoveData: function( key ) {\n\t\treturn this.each( function() {\n\t\t\tdataUser.remove( this, key );\n\t\t} );\n\t}\n} );\n\n\njQuery.extend( {\n\tqueue: function( elem, type, data ) {\n\t\tvar queue;\n\n\t\tif ( elem ) {\n\t\t\ttype = ( type || \"fx\" ) + \"queue\";\n\t\t\tqueue = dataPriv.get( elem, type );\n\n\t\t\t// Speed up dequeue by getting out quickly if this is just a lookup\n\t\t\tif ( data ) {\n\t\t\t\tif ( !queue || Array.isArray( data ) ) {\n\t\t\t\t\tqueue = dataPriv.access( elem, type, jQuery.makeArray( data ) );\n\t\t\t\t} else {\n\t\t\t\t\tqueue.push( data );\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn queue || [];\n\t\t}\n\t},\n\n\tdequeue: function( elem, type ) {\n\t\ttype = type || \"fx\";\n\n\t\tvar queue = jQuery.queue( elem, type ),\n\t\t\tstartLength = queue.length,\n\t\t\tfn = queue.shift(),\n\t\t\thooks = jQuery._queueHooks( elem, type ),\n\t\t\tnext = function() {\n\t\t\t\tjQuery.dequeue( elem, type );\n\t\t\t};\n\n\t\t// If the fx queue is dequeued, always remove the progress sentinel\n\t\tif ( fn === \"inprogress\" ) {\n\t\t\tfn = queue.shift();\n\t\t\tstartLength--;\n\t\t}\n\n\t\tif ( fn ) {\n\n\t\t\t// Add a progress sentinel to prevent the fx queue from being\n\t\t\t// automatically dequeued\n\t\t\tif ( type === \"fx\" ) {\n\t\t\t\tqueue.unshift( \"inprogress\" );\n\t\t\t}\n\n\t\t\t// Clear up the last queue stop function\n\t\t\tdelete hooks.stop;\n\t\t\tfn.call( elem, next, hooks );\n\t\t}\n\n\t\tif ( !startLength && hooks ) {\n\t\t\thooks.empty.fire();\n\t\t}\n\t},\n\n\t// Not public - generate a queueHooks object, or return the current one\n\t_queueHooks: function( elem, type ) {\n\t\tvar key = type + \"queueHooks\";\n\t\treturn dataPriv.get( elem, key ) || dataPriv.access( elem, key, {\n\t\t\tempty: jQuery.Callbacks( \"once memory\" ).add( function() {\n\t\t\t\tdataPriv.remove( elem, [ type + \"queue\", key ] );\n\t\t\t} )\n\t\t} );\n\t}\n} );\n\njQuery.fn.extend( {\n\tqueue: function( type, data ) {\n\t\tvar setter = 2;\n\n\t\tif ( typeof type !== \"string\" ) {\n\t\t\tdata = type;\n\t\t\ttype = \"fx\";\n\t\t\tsetter--;\n\t\t}\n\n\t\tif ( arguments.length < setter ) {\n\t\t\treturn jQuery.queue( this[ 0 ], type );\n\t\t}\n\n\t\treturn data === undefined ?\n\t\t\tthis :\n\t\t\tthis.each( function() {\n\t\t\t\tvar queue = jQuery.queue( this, type, data );\n\n\t\t\t\t// Ensure a hooks for this queue\n\t\t\t\tjQuery._queueHooks( this, type );\n\n\t\t\t\tif ( type === \"fx\" && queue[ 0 ] !== \"inprogress\" ) {\n\t\t\t\t\tjQuery.dequeue( this, type );\n\t\t\t\t}\n\t\t\t} );\n\t},\n\tdequeue: function( type ) {\n\t\treturn this.each( function() {\n\t\t\tjQuery.dequeue( this, type );\n\t\t} );\n\t},\n\tclearQueue: function( type ) {\n\t\treturn this.queue( type || \"fx\", [] );\n\t},\n\n\t// Get a promise resolved when queues of a certain type\n\t// are emptied (fx is the type by default)\n\tpromise: function( type, obj ) {\n\t\tvar tmp,\n\t\t\tcount = 1,\n\t\t\tdefer = jQuery.Deferred(),\n\t\t\telements = this,\n\t\t\ti = this.length,\n\t\t\tresolve = function() {\n\t\t\t\tif ( !( --count ) ) {\n\t\t\t\t\tdefer.resolveWith( elements, [ elements ] );\n\t\t\t\t}\n\t\t\t};\n\n\t\tif ( typeof type !== \"string\" ) {\n\t\t\tobj = type;\n\t\t\ttype = undefined;\n\t\t}\n\t\ttype = type || \"fx\";\n\n\t\twhile ( i-- ) {\n\t\t\ttmp = dataPriv.get( elements[ i ], type + \"queueHooks\" );\n\t\t\tif ( tmp && tmp.empty ) {\n\t\t\t\tcount++;\n\t\t\t\ttmp.empty.add( resolve );\n\t\t\t}\n\t\t}\n\t\tresolve();\n\t\treturn defer.promise( obj );\n\t}\n} );\nvar pnum = ( /[+-]?(?:\\d*\\.|)\\d+(?:[eE][+-]?\\d+|)/ ).source;\n\nvar rcssNum = new RegExp( \"^(?:([+-])=|)(\" + pnum + \")([a-z%]*)$\", \"i\" );\n\n\nvar cssExpand = [ \"Top\", \"Right\", \"Bottom\", \"Left\" ];\n\nvar documentElement = document.documentElement;\n\n\n\n\tvar isAttached = function( elem ) {\n\t\t\treturn jQuery.contains( elem.ownerDocument, elem );\n\t\t},\n\t\tcomposed = { composed: true };\n\n\t// Support: IE 9 - 11+, Edge 12 - 18+, iOS 10.0 - 10.2 only\n\t// Check attachment across shadow DOM boundaries when possible (gh-3504)\n\t// Support: iOS 10.0-10.2 only\n\t// Early iOS 10 versions support `attachShadow` but not `getRootNode`,\n\t// leading to errors. We need to check for `getRootNode`.\n\tif ( documentElement.getRootNode ) {\n\t\tisAttached = function( elem ) {\n\t\t\treturn jQuery.contains( elem.ownerDocument, elem ) ||\n\t\t\t\telem.getRootNode( composed ) === elem.ownerDocument;\n\t\t};\n\t}\nvar isHiddenWithinTree = function( elem, el ) {\n\n\t\t// isHiddenWithinTree might be called from jQuery#filter function;\n\t\t// in that case, element will be second argument\n\t\telem = el || elem;\n\n\t\t// Inline style trumps all\n\t\treturn elem.style.display === \"none\" ||\n\t\t\telem.style.display === \"\" &&\n\n\t\t\t// Otherwise, check computed style\n\t\t\t// Support: Firefox <=43 - 45\n\t\t\t// Disconnected elements can have computed display: none, so first confirm that elem is\n\t\t\t// in the document.\n\t\t\tisAttached( elem ) &&\n\n\t\t\tjQuery.css( elem, \"display\" ) === \"none\";\n\t};\n\nvar swap = function( elem, options, callback, args ) {\n\tvar ret, name,\n\t\told = {};\n\n\t// Remember the old values, and insert the new ones\n\tfor ( name in options ) {\n\t\told[ name ] = elem.style[ name ];\n\t\telem.style[ name ] = options[ name ];\n\t}\n\n\tret = callback.apply( elem, args || [] );\n\n\t// Revert the old values\n\tfor ( name in options ) {\n\t\telem.style[ name ] = old[ name ];\n\t}\n\n\treturn ret;\n};\n\n\n\n\nfunction adjustCSS( elem, prop, valueParts, tween ) {\n\tvar adjusted, scale,\n\t\tmaxIterations = 20,\n\t\tcurrentValue = tween ?\n\t\t\tfunction() {\n\t\t\t\treturn tween.cur();\n\t\t\t} :\n\t\t\tfunction() {\n\t\t\t\treturn jQuery.css( elem, prop, \"\" );\n\t\t\t},\n\t\tinitial = currentValue(),\n\t\tunit = valueParts && valueParts[ 3 ] || ( jQuery.cssNumber[ prop ] ? \"\" : \"px\" ),\n\n\t\t// Starting value computation is required for potential unit mismatches\n\t\tinitialInUnit = elem.nodeType &&\n\t\t\t( jQuery.cssNumber[ prop ] || unit !== \"px\" && +initial ) &&\n\t\t\trcssNum.exec( jQuery.css( elem, prop ) );\n\n\tif ( initialInUnit && initialInUnit[ 3 ] !== unit ) {\n\n\t\t// Support: Firefox <=54\n\t\t// Halve the iteration target value to prevent interference from CSS upper bounds (gh-2144)\n\t\tinitial = initial / 2;\n\n\t\t// Trust units reported by jQuery.css\n\t\tunit = unit || initialInUnit[ 3 ];\n\n\t\t// Iteratively approximate from a nonzero starting point\n\t\tinitialInUnit = +initial || 1;\n\n\t\twhile ( maxIterations-- ) {\n\n\t\t\t// Evaluate and update our best guess (doubling guesses that zero out).\n\t\t\t// Finish if the scale equals or crosses 1 (making the old*new product non-positive).\n\t\t\tjQuery.style( elem, prop, initialInUnit + unit );\n\t\t\tif ( ( 1 - scale ) * ( 1 - ( scale = currentValue() / initial || 0.5 ) ) <= 0 ) {\n\t\t\t\tmaxIterations = 0;\n\t\t\t}\n\t\t\tinitialInUnit = initialInUnit / scale;\n\n\t\t}\n\n\t\tinitialInUnit = initialInUnit * 2;\n\t\tjQuery.style( elem, prop, initialInUnit + unit );\n\n\t\t// Make sure we update the tween properties later on\n\t\tvalueParts = valueParts || [];\n\t}\n\n\tif ( valueParts ) {\n\t\tinitialInUnit = +initialInUnit || +initial || 0;\n\n\t\t// Apply relative offset (+=/-=) if specified\n\t\tadjusted = valueParts[ 1 ] ?\n\t\t\tinitialInUnit + ( valueParts[ 1 ] + 1 ) * valueParts[ 2 ] :\n\t\t\t+valueParts[ 2 ];\n\t\tif ( tween ) {\n\t\t\ttween.unit = unit;\n\t\t\ttween.start = initialInUnit;\n\t\t\ttween.end = adjusted;\n\t\t}\n\t}\n\treturn adjusted;\n}\n\n\nvar defaultDisplayMap = {};\n\nfunction getDefaultDisplay( elem ) {\n\tvar temp,\n\t\tdoc = elem.ownerDocument,\n\t\tnodeName = elem.nodeName,\n\t\tdisplay = defaultDisplayMap[ nodeName ];\n\n\tif ( display ) {\n\t\treturn display;\n\t}\n\n\ttemp = doc.body.appendChild( doc.createElement( nodeName ) );\n\tdisplay = jQuery.css( temp, \"display\" );\n\n\ttemp.parentNode.removeChild( temp );\n\n\tif ( display === \"none\" ) {\n\t\tdisplay = \"block\";\n\t}\n\tdefaultDisplayMap[ nodeName ] = display;\n\n\treturn display;\n}\n\nfunction showHide( elements, show ) {\n\tvar display, elem,\n\t\tvalues = [],\n\t\tindex = 0,\n\t\tlength = elements.length;\n\n\t// Determine new display value for elements that need to change\n\tfor ( ; index < length; index++ ) {\n\t\telem = elements[ index ];\n\t\tif ( !elem.style ) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tdisplay = elem.style.display;\n\t\tif ( show ) {\n\n\t\t\t// Since we force visibility upon cascade-hidden elements, an immediate (and slow)\n\t\t\t// check is required in this first loop unless we have a nonempty display value (either\n\t\t\t// inline or about-to-be-restored)\n\t\t\tif ( display === \"none\" ) {\n\t\t\t\tvalues[ index ] = dataPriv.get( elem, \"display\" ) || null;\n\t\t\t\tif ( !values[ index ] ) {\n\t\t\t\t\telem.style.display = \"\";\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ( elem.style.display === \"\" && isHiddenWithinTree( elem ) ) {\n\t\t\t\tvalues[ index ] = getDefaultDisplay( elem );\n\t\t\t}\n\t\t} else {\n\t\t\tif ( display !== \"none\" ) {\n\t\t\t\tvalues[ index ] = \"none\";\n\n\t\t\t\t// Remember what we're overwriting\n\t\t\t\tdataPriv.set( elem, \"display\", display );\n\t\t\t}\n\t\t}\n\t}\n\n\t// Set the display of the elements in a second loop to avoid constant reflow\n\tfor ( index = 0; index < length; index++ ) {\n\t\tif ( values[ index ] != null ) {\n\t\t\telements[ index ].style.display = values[ index ];\n\t\t}\n\t}\n\n\treturn elements;\n}\n\njQuery.fn.extend( {\n\tshow: function() {\n\t\treturn showHide( this, true );\n\t},\n\thide: function() {\n\t\treturn showHide( this );\n\t},\n\ttoggle: function( state ) {\n\t\tif ( typeof state === \"boolean\" ) {\n\t\t\treturn state ? this.show() : this.hide();\n\t\t}\n\n\t\treturn this.each( function() {\n\t\t\tif ( isHiddenWithinTree( this ) ) {\n\t\t\t\tjQuery( this ).show();\n\t\t\t} else {\n\t\t\t\tjQuery( this ).hide();\n\t\t\t}\n\t\t} );\n\t}\n} );\nvar rcheckableType = ( /^(?:checkbox|radio)$/i );\n\nvar rtagName = ( /<([a-z][^\\/\\0>\\x20\\t\\r\\n\\f]*)/i );\n\nvar rscriptType = ( /^$|^module$|\\/(?:java|ecma)script/i );\n\n\n\n// We have to close these tags to support XHTML (#13200)\nvar wrapMap = {\n\n\t// Support: IE <=9 only\n\toption: [ 1, \"<select multiple='multiple'>\", \"</select>\" ],\n\n\t// XHTML parsers do not magically insert elements in the\n\t// same way that tag soup parsers do. So we cannot shorten\n\t// this by omitting <tbody> or other required elements.\n\tthead: [ 1, \"<table>\", \"</table>\" ],\n\tcol: [ 2, \"<table><colgroup>\", \"</colgroup></table>\" ],\n\ttr: [ 2, \"<table><tbody>\", \"</tbody></table>\" ],\n\ttd: [ 3, \"<table><tbody><tr>\", \"</tr></tbody></table>\" ],\n\n\t_default: [ 0, \"\", \"\" ]\n};\n\n// Support: IE <=9 only\nwrapMap.optgroup = wrapMap.option;\n\nwrapMap.tbody = wrapMap.tfoot = wrapMap.colgroup = wrapMap.caption = wrapMap.thead;\nwrapMap.th = wrapMap.td;\n\n\nfunction getAll( context, tag ) {\n\n\t// Support: IE <=9 - 11 only\n\t// Use typeof to avoid zero-argument method invocation on host objects (#15151)\n\tvar ret;\n\n\tif ( typeof context.getElementsByTagName !== \"undefined\" ) {\n\t\tret = context.getElementsByTagName( tag || \"*\" );\n\n\t} else if ( typeof context.querySelectorAll !== \"undefined\" ) {\n\t\tret = context.querySelectorAll( tag || \"*\" );\n\n\t} else {\n\t\tret = [];\n\t}\n\n\tif ( tag === undefined || tag && nodeName( context, tag ) ) {\n\t\treturn jQuery.merge( [ context ], ret );\n\t}\n\n\treturn ret;\n}\n\n\n// Mark scripts as having already been evaluated\nfunction setGlobalEval( elems, refElements ) {\n\tvar i = 0,\n\t\tl = elems.length;\n\n\tfor ( ; i < l; i++ ) {\n\t\tdataPriv.set(\n\t\t\telems[ i ],\n\t\t\t\"globalEval\",\n\t\t\t!refElements || dataPriv.get( refElements[ i ], \"globalEval\" )\n\t\t);\n\t}\n}\n\n\nvar rhtml = /<|&#?\\w+;/;\n\nfunction buildFragment( elems, context, scripts, selection, ignored ) {\n\tvar elem, tmp, tag, wrap, attached, j,\n\t\tfragment = context.createDocumentFragment(),\n\t\tnodes = [],\n\t\ti = 0,\n\t\tl = elems.length;\n\n\tfor ( ; i < l; i++ ) {\n\t\telem = elems[ i ];\n\n\t\tif ( elem || elem === 0 ) {\n\n\t\t\t// Add nodes directly\n\t\t\tif ( toType( elem ) === \"object\" ) {\n\n\t\t\t\t// Support: Android <=4.0 only, PhantomJS 1 only\n\t\t\t\t// push.apply(_, arraylike) throws on ancient WebKit\n\t\t\t\tjQuery.merge( nodes, elem.nodeType ? [ elem ] : elem );\n\n\t\t\t// Convert non-html into a text node\n\t\t\t} else if ( !rhtml.test( elem ) ) {\n\t\t\t\tnodes.push( context.createTextNode( elem ) );\n\n\t\t\t// Convert html into DOM nodes\n\t\t\t} else {\n\t\t\t\ttmp = tmp || fragment.appendChild( context.createElement( \"div\" ) );\n\n\t\t\t\t// Deserialize a standard representation\n\t\t\t\ttag = ( rtagName.exec( elem ) || [ \"\", \"\" ] )[ 1 ].toLowerCase();\n\t\t\t\twrap = wrapMap[ tag ] || wrapMap._default;\n\t\t\t\ttmp.innerHTML = wrap[ 1 ] + jQuery.htmlPrefilter( elem ) + wrap[ 2 ];\n\n\t\t\t\t// Descend through wrappers to the right content\n\t\t\t\tj = wrap[ 0 ];\n\t\t\t\twhile ( j-- ) {\n\t\t\t\t\ttmp = tmp.lastChild;\n\t\t\t\t}\n\n\t\t\t\t// Support: Android <=4.0 only, PhantomJS 1 only\n\t\t\t\t// push.apply(_, arraylike) throws on ancient WebKit\n\t\t\t\tjQuery.merge( nodes, tmp.childNodes );\n\n\t\t\t\t// Remember the top-level container\n\t\t\t\ttmp = fragment.firstChild;\n\n\t\t\t\t// Ensure the created nodes are orphaned (#12392)\n\t\t\t\ttmp.textContent = \"\";\n\t\t\t}\n\t\t}\n\t}\n\n\t// Remove wrapper from fragment\n\tfragment.textContent = \"\";\n\n\ti = 0;\n\twhile ( ( elem = nodes[ i++ ] ) ) {\n\n\t\t// Skip elements already in the context collection (trac-4087)\n\t\tif ( selection && jQuery.inArray( elem, selection ) > -1 ) {\n\t\t\tif ( ignored ) {\n\t\t\t\tignored.push( elem );\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\n\t\tattached = isAttached( elem );\n\n\t\t// Append to fragment\n\t\ttmp = getAll( fragment.appendChild( elem ), \"script\" );\n\n\t\t// Preserve script evaluation history\n\t\tif ( attached ) {\n\t\t\tsetGlobalEval( tmp );\n\t\t}\n\n\t\t// Capture executables\n\t\tif ( scripts ) {\n\t\t\tj = 0;\n\t\t\twhile ( ( elem = tmp[ j++ ] ) ) {\n\t\t\t\tif ( rscriptType.test( elem.type || \"\" ) ) {\n\t\t\t\t\tscripts.push( elem );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn fragment;\n}\n\n\n( function() {\n\tvar fragment = document.createDocumentFragment(),\n\t\tdiv = fragment.appendChild( document.createElement( \"div\" ) ),\n\t\tinput = document.createElement( \"input\" );\n\n\t// Support: Android 4.0 - 4.3 only\n\t// Check state lost if the name is set (#11217)\n\t// Support: Windows Web Apps (WWA)\n\t// `name` and `type` must use .setAttribute for WWA (#14901)\n\tinput.setAttribute( \"type\", \"radio\" );\n\tinput.setAttribute( \"checked\", \"checked\" );\n\tinput.setAttribute( \"name\", \"t\" );\n\n\tdiv.appendChild( input );\n\n\t// Support: Android <=4.1 only\n\t// Older WebKit doesn't clone checked state correctly in fragments\n\tsupport.checkClone = div.cloneNode( true ).cloneNode( true ).lastChild.checked;\n\n\t// Support: IE <=11 only\n\t// Make sure textarea (and checkbox) defaultValue is properly cloned\n\tdiv.innerHTML = \"<textarea>x</textarea>\";\n\tsupport.noCloneChecked = !!div.cloneNode( true ).lastChild.defaultValue;\n} )();\n\n\nvar\n\trkeyEvent = /^key/,\n\trmouseEvent = /^(?:mouse|pointer|contextmenu|drag|drop)|click/,\n\trtypenamespace = /^([^.]*)(?:\\.(.+)|)/;\n\nfunction returnTrue() {\n\treturn true;\n}\n\nfunction returnFalse() {\n\treturn false;\n}\n\n// Support: IE <=9 - 11+\n// focus() and blur() are asynchronous, except when they are no-op.\n// So expect focus to be synchronous when the element is already active,\n// and blur to be synchronous when the element is not already active.\n// (focus and blur are always synchronous in other supported browsers,\n// this just defines when we can count on it).\nfunction expectSync( elem, type ) {\n\treturn ( elem === safeActiveElement() ) === ( type === \"focus\" );\n}\n\n// Support: IE <=9 only\n// Accessing document.activeElement can throw unexpectedly\n// https://bugs.jquery.com/ticket/13393\nfunction safeActiveElement() {\n\ttry {\n\t\treturn document.activeElement;\n\t} catch ( err ) { }\n}\n\nfunction on( elem, types, selector, data, fn, one ) {\n\tvar origFn, type;\n\n\t// Types can be a map of types/handlers\n\tif ( typeof types === \"object\" ) {\n\n\t\t// ( types-Object, selector, data )\n\t\tif ( typeof selector !== \"string\" ) {\n\n\t\t\t// ( types-Object, data )\n\t\t\tdata = data || selector;\n\t\t\tselector = undefined;\n\t\t}\n\t\tfor ( type in types ) {\n\t\t\ton( elem, type, selector, data, types[ type ], one );\n\t\t}\n\t\treturn elem;\n\t}\n\n\tif ( data == null && fn == null ) {\n\n\t\t// ( types, fn )\n\t\tfn = selector;\n\t\tdata = selector = undefined;\n\t} else if ( fn == null ) {\n\t\tif ( typeof selector === \"string\" ) {\n\n\t\t\t// ( types, selector, fn )\n\t\t\tfn = data;\n\t\t\tdata = undefined;\n\t\t} else {\n\n\t\t\t// ( types, data, fn )\n\t\t\tfn = data;\n\t\t\tdata = selector;\n\t\t\tselector = undefined;\n\t\t}\n\t}\n\tif ( fn === false ) {\n\t\tfn = returnFalse;\n\t} else if ( !fn ) {\n\t\treturn elem;\n\t}\n\n\tif ( one === 1 ) {\n\t\torigFn = fn;\n\t\tfn = function( event ) {\n\n\t\t\t// Can use an empty set, since event contains the info\n\t\t\tjQuery().off( event );\n\t\t\treturn origFn.apply( this, arguments );\n\t\t};\n\n\t\t// Use same guid so caller can remove using origFn\n\t\tfn.guid = origFn.guid || ( origFn.guid = jQuery.guid++ );\n\t}\n\treturn elem.each( function() {\n\t\tjQuery.event.add( this, types, fn, data, selector );\n\t} );\n}\n\n/*\n * Helper functions for managing events -- not part of the public interface.\n * Props to Dean Edwards' addEvent library for many of the ideas.\n */\njQuery.event = {\n\n\tglobal: {},\n\n\tadd: function( elem, types, handler, data, selector ) {\n\n\t\tvar handleObjIn, eventHandle, tmp,\n\t\t\tevents, t, handleObj,\n\t\t\tspecial, handlers, type, namespaces, origType,\n\t\t\telemData = dataPriv.get( elem );\n\n\t\t// Don't attach events to noData or text/comment nodes (but allow plain objects)\n\t\tif ( !elemData ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Caller can pass in an object of custom data in lieu of the handler\n\t\tif ( handler.handler ) {\n\t\t\thandleObjIn = handler;\n\t\t\thandler = handleObjIn.handler;\n\t\t\tselector = handleObjIn.selector;\n\t\t}\n\n\t\t// Ensure that invalid selectors throw exceptions at attach time\n\t\t// Evaluate against documentElement in case elem is a non-element node (e.g., document)\n\t\tif ( selector ) {\n\t\t\tjQuery.find.matchesSelector( documentElement, selector );\n\t\t}\n\n\t\t// Make sure that the handler has a unique ID, used to find/remove it later\n\t\tif ( !handler.guid ) {\n\t\t\thandler.guid = jQuery.guid++;\n\t\t}\n\n\t\t// Init the element's event structure and main handler, if this is the first\n\t\tif ( !( events = elemData.events ) ) {\n\t\t\tevents = elemData.events = {};\n\t\t}\n\t\tif ( !( eventHandle = elemData.handle ) ) {\n\t\t\teventHandle = elemData.handle = function( e ) {\n\n\t\t\t\t// Discard the second event of a jQuery.event.trigger() and\n\t\t\t\t// when an event is called after a page has unloaded\n\t\t\t\treturn typeof jQuery !== \"undefined\" && jQuery.event.triggered !== e.type ?\n\t\t\t\t\tjQuery.event.dispatch.apply( elem, arguments ) : undefined;\n\t\t\t};\n\t\t}\n\n\t\t// Handle multiple events separated by a space\n\t\ttypes = ( types || \"\" ).match( rnothtmlwhite ) || [ \"\" ];\n\t\tt = types.length;\n\t\twhile ( t-- ) {\n\t\t\ttmp = rtypenamespace.exec( types[ t ] ) || [];\n\t\t\ttype = origType = tmp[ 1 ];\n\t\t\tnamespaces = ( tmp[ 2 ] || \"\" ).split( \".\" ).sort();\n\n\t\t\t// There *must* be a type, no attaching namespace-only handlers\n\t\t\tif ( !type ) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// If event changes its type, use the special event handlers for the changed type\n\t\t\tspecial = jQuery.event.special[ type ] || {};\n\n\t\t\t// If selector defined, determine special event api type, otherwise given type\n\t\t\ttype = ( selector ? special.delegateType : special.bindType ) || type;\n\n\t\t\t// Update special based on newly reset type\n\t\t\tspecial = jQuery.event.special[ type ] || {};\n\n\t\t\t// handleObj is passed to all event handlers\n\t\t\thandleObj = jQuery.extend( {\n\t\t\t\ttype: type,\n\t\t\t\torigType: origType,\n\t\t\t\tdata: data,\n\t\t\t\thandler: handler,\n\t\t\t\tguid: handler.guid,\n\t\t\t\tselector: selector,\n\t\t\t\tneedsContext: selector && jQuery.expr.match.needsContext.test( selector ),\n\t\t\t\tnamespace: namespaces.join( \".\" )\n\t\t\t}, handleObjIn );\n\n\t\t\t// Init the event handler queue if we're the first\n\t\t\tif ( !( handlers = events[ type ] ) ) {\n\t\t\t\thandlers = events[ type ] = [];\n\t\t\t\thandlers.delegateCount = 0;\n\n\t\t\t\t// Only use addEventListener if the special events handler returns false\n\t\t\t\tif ( !special.setup ||\n\t\t\t\t\tspecial.setup.call( elem, data, namespaces, eventHandle ) === false ) {\n\n\t\t\t\t\tif ( elem.addEventListener ) {\n\t\t\t\t\t\telem.addEventListener( type, eventHandle );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif ( special.add ) {\n\t\t\t\tspecial.add.call( elem, handleObj );\n\n\t\t\t\tif ( !handleObj.handler.guid ) {\n\t\t\t\t\thandleObj.handler.guid = handler.guid;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Add to the element's handler list, delegates in front\n\t\t\tif ( selector ) {\n\t\t\t\thandlers.splice( handlers.delegateCount++, 0, handleObj );\n\t\t\t} else {\n\t\t\t\thandlers.push( handleObj );\n\t\t\t}\n\n\t\t\t// Keep track of which events have ever been used, for event optimization\n\t\t\tjQuery.event.global[ type ] = true;\n\t\t}\n\n\t},\n\n\t// Detach an event or set of events from an element\n\tremove: function( elem, types, handler, selector, mappedTypes ) {\n\n\t\tvar j, origCount, tmp,\n\t\t\tevents, t, handleObj,\n\t\t\tspecial, handlers, type, namespaces, origType,\n\t\t\telemData = dataPriv.hasData( elem ) && dataPriv.get( elem );\n\n\t\tif ( !elemData || !( events = elemData.events ) ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Once for each type.namespace in types; type may be omitted\n\t\ttypes = ( types || \"\" ).match( rnothtmlwhite ) || [ \"\" ];\n\t\tt = types.length;\n\t\twhile ( t-- ) {\n\t\t\ttmp = rtypenamespace.exec( types[ t ] ) || [];\n\t\t\ttype = origType = tmp[ 1 ];\n\t\t\tnamespaces = ( tmp[ 2 ] || \"\" ).split( \".\" ).sort();\n\n\t\t\t// Unbind all events (on this namespace, if provided) for the element\n\t\t\tif ( !type ) {\n\t\t\t\tfor ( type in events ) {\n\t\t\t\t\tjQuery.event.remove( elem, type + types[ t ], handler, selector, true );\n\t\t\t\t}\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tspecial = jQuery.event.special[ type ] || {};\n\t\t\ttype = ( selector ? special.delegateType : special.bindType ) || type;\n\t\t\thandlers = events[ type ] || [];\n\t\t\ttmp = tmp[ 2 ] &&\n\t\t\t\tnew RegExp( \"(^|\\\\.)\" + namespaces.join( \"\\\\.(?:.*\\\\.|)\" ) + \"(\\\\.|$)\" );\n\n\t\t\t// Remove matching events\n\t\t\torigCount = j = handlers.length;\n\t\t\twhile ( j-- ) {\n\t\t\t\thandleObj = handlers[ j ];\n\n\t\t\t\tif ( ( mappedTypes || origType === handleObj.origType ) &&\n\t\t\t\t\t( !handler || handler.guid === handleObj.guid ) &&\n\t\t\t\t\t( !tmp || tmp.test( handleObj.namespace ) ) &&\n\t\t\t\t\t( !selector || selector === handleObj.selector ||\n\t\t\t\t\t\tselector === \"**\" && handleObj.selector ) ) {\n\t\t\t\t\thandlers.splice( j, 1 );\n\n\t\t\t\t\tif ( handleObj.selector ) {\n\t\t\t\t\t\thandlers.delegateCount--;\n\t\t\t\t\t}\n\t\t\t\t\tif ( special.remove ) {\n\t\t\t\t\t\tspecial.remove.call( elem, handleObj );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Remove generic event handler if we removed something and no more handlers exist\n\t\t\t// (avoids potential for endless recursion during removal of special event handlers)\n\t\t\tif ( origCount && !handlers.length ) {\n\t\t\t\tif ( !special.teardown ||\n\t\t\t\t\tspecial.teardown.call( elem, namespaces, elemData.handle ) === false ) {\n\n\t\t\t\t\tjQuery.removeEvent( elem, type, elemData.handle );\n\t\t\t\t}\n\n\t\t\t\tdelete events[ type ];\n\t\t\t}\n\t\t}\n\n\t\t// Remove data and the expando if it's no longer used\n\t\tif ( jQuery.isEmptyObject( events ) ) {\n\t\t\tdataPriv.remove( elem, \"handle events\" );\n\t\t}\n\t},\n\n\tdispatch: function( nativeEvent ) {\n\n\t\t// Make a writable jQuery.Event from the native event object\n\t\tvar event = jQuery.event.fix( nativeEvent );\n\n\t\tvar i, j, ret, matched, handleObj, handlerQueue,\n\t\t\targs = new Array( arguments.length ),\n\t\t\thandlers = ( dataPriv.get( this, \"events\" ) || {} )[ event.type ] || [],\n\t\t\tspecial = jQuery.event.special[ event.type ] || {};\n\n\t\t// Use the fix-ed jQuery.Event rather than the (read-only) native event\n\t\targs[ 0 ] = event;\n\n\t\tfor ( i = 1; i < arguments.length; i++ ) {\n\t\t\targs[ i ] = arguments[ i ];\n\t\t}\n\n\t\tevent.delegateTarget = this;\n\n\t\t// Call the preDispatch hook for the mapped type, and let it bail if desired\n\t\tif ( special.preDispatch && special.preDispatch.call( this, event ) === false ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Determine handlers\n\t\thandlerQueue = jQuery.event.handlers.call( this, event, handlers );\n\n\t\t// Run delegates first; they may want to stop propagation beneath us\n\t\ti = 0;\n\t\twhile ( ( matched = handlerQueue[ i++ ] ) && !event.isPropagationStopped() ) {\n\t\t\tevent.currentTarget = matched.elem;\n\n\t\t\tj = 0;\n\t\t\twhile ( ( handleObj = matched.handlers[ j++ ] ) &&\n\t\t\t\t!event.isImmediatePropagationStopped() ) {\n\n\t\t\t\t// If the event is namespaced, then each handler is only invoked if it is\n\t\t\t\t// specially universal or its namespaces are a superset of the event's.\n\t\t\t\tif ( !event.rnamespace || handleObj.namespace === false ||\n\t\t\t\t\tevent.rnamespace.test( handleObj.namespace ) ) {\n\n\t\t\t\t\tevent.handleObj = handleObj;\n\t\t\t\t\tevent.data = handleObj.data;\n\n\t\t\t\t\tret = ( ( jQuery.event.special[ handleObj.origType ] || {} ).handle ||\n\t\t\t\t\t\thandleObj.handler ).apply( matched.elem, args );\n\n\t\t\t\t\tif ( ret !== undefined ) {\n\t\t\t\t\t\tif ( ( event.result = ret ) === false ) {\n\t\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Call the postDispatch hook for the mapped type\n\t\tif ( special.postDispatch ) {\n\t\t\tspecial.postDispatch.call( this, event );\n\t\t}\n\n\t\treturn event.result;\n\t},\n\n\thandlers: function( event, handlers ) {\n\t\tvar i, handleObj, sel, matchedHandlers, matchedSelectors,\n\t\t\thandlerQueue = [],\n\t\t\tdelegateCount = handlers.delegateCount,\n\t\t\tcur = event.target;\n\n\t\t// Find delegate handlers\n\t\tif ( delegateCount &&\n\n\t\t\t// Support: IE <=9\n\t\t\t// Black-hole SVG <use> instance trees (trac-13180)\n\t\t\tcur.nodeType &&\n\n\t\t\t// Support: Firefox <=42\n\t\t\t// Suppress spec-violating clicks indicating a non-primary pointer button (trac-3861)\n\t\t\t// https://www.w3.org/TR/DOM-Level-3-Events/#event-type-click\n\t\t\t// Support: IE 11 only\n\t\t\t// ...but not arrow key \"clicks\" of radio inputs, which can have `button` -1 (gh-2343)\n\t\t\t!( event.type === \"click\" && event.button >= 1 ) ) {\n\n\t\t\tfor ( ; cur !== this; cur = cur.parentNode || this ) {\n\n\t\t\t\t// Don't check non-elements (#13208)\n\t\t\t\t// Don't process clicks on disabled elements (#6911, #8165, #11382, #11764)\n\t\t\t\tif ( cur.nodeType === 1 && !( event.type === \"click\" && cur.disabled === true ) ) {\n\t\t\t\t\tmatchedHandlers = [];\n\t\t\t\t\tmatchedSelectors = {};\n\t\t\t\t\tfor ( i = 0; i < delegateCount; i++ ) {\n\t\t\t\t\t\thandleObj = handlers[ i ];\n\n\t\t\t\t\t\t// Don't conflict with Object.prototype properties (#13203)\n\t\t\t\t\t\tsel = handleObj.selector + \" \";\n\n\t\t\t\t\t\tif ( matchedSelectors[ sel ] === undefined ) {\n\t\t\t\t\t\t\tmatchedSelectors[ sel ] = handleObj.needsContext ?\n\t\t\t\t\t\t\t\tjQuery( sel, this ).index( cur ) > -1 :\n\t\t\t\t\t\t\t\tjQuery.find( sel, this, null, [ cur ] ).length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif ( matchedSelectors[ sel ] ) {\n\t\t\t\t\t\t\tmatchedHandlers.push( handleObj );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif ( matchedHandlers.length ) {\n\t\t\t\t\t\thandlerQueue.push( { elem: cur, handlers: matchedHandlers } );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Add the remaining (directly-bound) handlers\n\t\tcur = this;\n\t\tif ( delegateCount < handlers.length ) {\n\t\t\thandlerQueue.push( { elem: cur, handlers: handlers.slice( delegateCount ) } );\n\t\t}\n\n\t\treturn handlerQueue;\n\t},\n\n\taddProp: function( name, hook ) {\n\t\tObject.defineProperty( jQuery.Event.prototype, name, {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\n\t\t\tget: isFunction( hook ) ?\n\t\t\t\tfunction() {\n\t\t\t\t\tif ( this.originalEvent ) {\n\t\t\t\t\t\t\treturn hook( this.originalEvent );\n\t\t\t\t\t}\n\t\t\t\t} :\n\t\t\t\tfunction() {\n\t\t\t\t\tif ( this.originalEvent ) {\n\t\t\t\t\t\t\treturn this.originalEvent[ name ];\n\t\t\t\t\t}\n\t\t\t\t},\n\n\t\t\tset: function( value ) {\n\t\t\t\tObject.defineProperty( this, name, {\n\t\t\t\t\tenumerable: true,\n\t\t\t\t\tconfigurable: true,\n\t\t\t\t\twritable: true,\n\t\t\t\t\tvalue: value\n\t\t\t\t} );\n\t\t\t}\n\t\t} );\n\t},\n\n\tfix: function( originalEvent ) {\n\t\treturn originalEvent[ jQuery.expando ] ?\n\t\t\toriginalEvent :\n\t\t\tnew jQuery.Event( originalEvent );\n\t},\n\n\tspecial: {\n\t\tload: {\n\n\t\t\t// Prevent triggered image.load events from bubbling to window.load\n\t\t\tnoBubble: true\n\t\t},\n\t\tclick: {\n\n\t\t\t// Utilize native event to ensure correct state for checkable inputs\n\t\t\tsetup: function( data ) {\n\n\t\t\t\t// For mutual compressibility with _default, replace `this` access with a local var.\n\t\t\t\t// `|| data` is dead code meant only to preserve the variable through minification.\n\t\t\t\tvar el = this || data;\n\n\t\t\t\t// Claim the first handler\n\t\t\t\tif ( rcheckableType.test( el.type ) &&\n\t\t\t\t\tel.click && nodeName( el, \"input\" ) ) {\n\n\t\t\t\t\t// dataPriv.set( el, \"click\", ... )\n\t\t\t\t\tleverageNative( el, \"click\", returnTrue );\n\t\t\t\t}\n\n\t\t\t\t// Return false to allow normal processing in the caller\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\ttrigger: function( data ) {\n\n\t\t\t\t// For mutual compressibility with _default, replace `this` access with a local var.\n\t\t\t\t// `|| data` is dead code meant only to preserve the variable through minification.\n\t\t\t\tvar el = this || data;\n\n\t\t\t\t// Force setup before triggering a click\n\t\t\t\tif ( rcheckableType.test( el.type ) &&\n\t\t\t\t\tel.click && nodeName( el, \"input\" ) ) {\n\n\t\t\t\t\tleverageNative( el, \"click\" );\n\t\t\t\t}\n\n\t\t\t\t// Return non-false to allow normal event-path propagation\n\t\t\t\treturn true;\n\t\t\t},\n\n\t\t\t// For cross-browser consistency, suppress native .click() on links\n\t\t\t// Also prevent it if we're currently inside a leveraged native-event stack\n\t\t\t_default: function( event ) {\n\t\t\t\tvar target = event.target;\n\t\t\t\treturn rcheckableType.test( target.type ) &&\n\t\t\t\t\ttarget.click && nodeName( target, \"input\" ) &&\n\t\t\t\t\tdataPriv.get( target, \"click\" ) ||\n\t\t\t\t\tnodeName( target, \"a\" );\n\t\t\t}\n\t\t},\n\n\t\tbeforeunload: {\n\t\t\tpostDispatch: function( event ) {\n\n\t\t\t\t// Support: Firefox 20+\n\t\t\t\t// Firefox doesn't alert if the returnValue field is not set.\n\t\t\t\tif ( event.result !== undefined && event.originalEvent ) {\n\t\t\t\t\tevent.originalEvent.returnValue = event.result;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n};\n\n// Ensure the presence of an event listener that handles manually-triggered\n// synthetic events by interrupting progress until reinvoked in response to\n// *native* events that it fires directly, ensuring that state changes have\n// already occurred before other listeners are invoked.\nfunction leverageNative( el, type, expectSync ) {\n\n\t// Missing expectSync indicates a trigger call, which must force setup through jQuery.event.add\n\tif ( !expectSync ) {\n\t\tif ( dataPriv.get( el, type ) === undefined ) {\n\t\t\tjQuery.event.add( el, type, returnTrue );\n\t\t}\n\t\treturn;\n\t}\n\n\t// Register the controller as a special universal handler for all event namespaces\n\tdataPriv.set( el, type, false );\n\tjQuery.event.add( el, type, {\n\t\tnamespace: false,\n\t\thandler: function( event ) {\n\t\t\tvar notAsync, result,\n\t\t\t\tsaved = dataPriv.get( this, type );\n\n\t\t\tif ( ( event.isTrigger & 1 ) && this[ type ] ) {\n\n\t\t\t\t// Interrupt processing of the outer synthetic .trigger()ed event\n\t\t\t\t// Saved data should be false in such cases, but might be a leftover capture object\n\t\t\t\t// from an async native handler (gh-4350)\n\t\t\t\tif ( !saved.length ) {\n\n\t\t\t\t\t// Store arguments for use when handling the inner native event\n\t\t\t\t\t// There will always be at least one argument (an event object), so this array\n\t\t\t\t\t// will not be confused with a leftover capture object.\n\t\t\t\t\tsaved = slice.call( arguments );\n\t\t\t\t\tdataPriv.set( this, type, saved );\n\n\t\t\t\t\t// Trigger the native event and capture its result\n\t\t\t\t\t// Support: IE <=9 - 11+\n\t\t\t\t\t// focus() and blur() are asynchronous\n\t\t\t\t\tnotAsync = expectSync( this, type );\n\t\t\t\t\tthis[ type ]();\n\t\t\t\t\tresult = dataPriv.get( this, type );\n\t\t\t\t\tif ( saved !== result || notAsync ) {\n\t\t\t\t\t\tdataPriv.set( this, type, false );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult = {};\n\t\t\t\t\t}\n\t\t\t\t\tif ( saved !== result ) {\n\n\t\t\t\t\t\t// Cancel the outer synthetic event\n\t\t\t\t\t\tevent.stopImmediatePropagation();\n\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\treturn result.value;\n\t\t\t\t\t}\n\n\t\t\t\t// If this is an inner synthetic event for an event with a bubbling surrogate\n\t\t\t\t// (focus or blur), assume that the surrogate already propagated from triggering the\n\t\t\t\t// native event and prevent that from happening again here.\n\t\t\t\t// This technically gets the ordering wrong w.r.t. to `.trigger()` (in which the\n\t\t\t\t// bubbling surrogate propagates *after* the non-bubbling base), but that seems\n\t\t\t\t// less bad than duplication.\n\t\t\t\t} else if ( ( jQuery.event.special[ type ] || {} ).delegateType ) {\n\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t}\n\n\t\t\t// If this is a native event triggered above, everything is now in order\n\t\t\t// Fire an inner synthetic event with the original arguments\n\t\t\t} else if ( saved.length ) {\n\n\t\t\t\t// ...and capture the result\n\t\t\t\tdataPriv.set( this, type, {\n\t\t\t\t\tvalue: jQuery.event.trigger(\n\n\t\t\t\t\t\t// Support: IE <=9 - 11+\n\t\t\t\t\t\t// Extend with the prototype to reset the above stopImmediatePropagation()\n\t\t\t\t\t\tjQuery.extend( saved[ 0 ], jQuery.Event.prototype ),\n\t\t\t\t\t\tsaved.slice( 1 ),\n\t\t\t\t\t\tthis\n\t\t\t\t\t)\n\t\t\t\t} );\n\n\t\t\t\t// Abort handling of the native event\n\t\t\t\tevent.stopImmediatePropagation();\n\t\t\t}\n\t\t}\n\t} );\n}\n\njQuery.removeEvent = function( elem, type, handle ) {\n\n\t// This \"if\" is needed for plain objects\n\tif ( elem.removeEventListener ) {\n\t\telem.removeEventListener( type, handle );\n\t}\n};\n\njQuery.Event = function( src, props ) {\n\n\t// Allow instantiation without the 'new' keyword\n\tif ( !( this instanceof jQuery.Event ) ) {\n\t\treturn new jQuery.Event( src, props );\n\t}\n\n\t// Event object\n\tif ( src && src.type ) {\n\t\tthis.originalEvent = src;\n\t\tthis.type = src.type;\n\n\t\t// Events bubbling up the document may have been marked as prevented\n\t\t// by a handler lower down the tree; reflect the correct value.\n\t\tthis.isDefaultPrevented = src.defaultPrevented ||\n\t\t\t\tsrc.defaultPrevented === undefined &&\n\n\t\t\t\t// Support: Android <=2.3 only\n\t\t\t\tsrc.returnValue === false ?\n\t\t\treturnTrue :\n\t\t\treturnFalse;\n\n\t\t// Create target properties\n\t\t// Support: Safari <=6 - 7 only\n\t\t// Target should not be a text node (#504, #13143)\n\t\tthis.target = ( src.target && src.target.nodeType === 3 ) ?\n\t\t\tsrc.target.parentNode :\n\t\t\tsrc.target;\n\n\t\tthis.currentTarget = src.currentTarget;\n\t\tthis.relatedTarget = src.relatedTarget;\n\n\t// Event type\n\t} else {\n\t\tthis.type = src;\n\t}\n\n\t// Put explicitly provided properties onto the event object\n\tif ( props ) {\n\t\tjQuery.extend( this, props );\n\t}\n\n\t// Create a timestamp if incoming event doesn't have one\n\tthis.timeStamp = src && src.timeStamp || Date.now();\n\n\t// Mark it as fixed\n\tthis[ jQuery.expando ] = true;\n};\n\n// jQuery.Event is based on DOM3 Events as specified by the ECMAScript Language Binding\n// https://www.w3.org/TR/2003/WD-DOM-Level-3-Events-20030331/ecma-script-binding.html\njQuery.Event.prototype = {\n\tconstructor: jQuery.Event,\n\tisDefaultPrevented: returnFalse,\n\tisPropagationStopped: returnFalse,\n\tisImmediatePropagationStopped: returnFalse,\n\tisSimulated: false,\n\n\tpreventDefault: function() {\n\t\tvar e = this.originalEvent;\n\n\t\tthis.isDefaultPrevented = returnTrue;\n\n\t\tif ( e && !this.isSimulated ) {\n\t\t\te.preventDefault();\n\t\t}\n\t},\n\tstopPropagation: function() {\n\t\tvar e = this.originalEvent;\n\n\t\tthis.isPropagationStopped = returnTrue;\n\n\t\tif ( e && !this.isSimulated ) {\n\t\t\te.stopPropagation();\n\t\t}\n\t},\n\tstopImmediatePropagation: function() {\n\t\tvar e = this.originalEvent;\n\n\t\tthis.isImmediatePropagationStopped = returnTrue;\n\n\t\tif ( e && !this.isSimulated ) {\n\t\t\te.stopImmediatePropagation();\n\t\t}\n\n\t\tthis.stopPropagation();\n\t}\n};\n\n// Includes all common event props including KeyEvent and MouseEvent specific props\njQuery.each( {\n\taltKey: true,\n\tbubbles: true,\n\tcancelable: true,\n\tchangedTouches: true,\n\tctrlKey: true,\n\tdetail: true,\n\teventPhase: true,\n\tmetaKey: true,\n\tpageX: true,\n\tpageY: true,\n\tshiftKey: true,\n\tview: true,\n\t\"char\": true,\n\tcode: true,\n\tcharCode: true,\n\tkey: true,\n\tkeyCode: true,\n\tbutton: true,\n\tbuttons: true,\n\tclientX: true,\n\tclientY: true,\n\toffsetX: true,\n\toffsetY: true,\n\tpointerId: true,\n\tpointerType: true,\n\tscreenX: true,\n\tscreenY: true,\n\ttargetTouches: true,\n\ttoElement: true,\n\ttouches: true,\n\n\twhich: function( event ) {\n\t\tvar button = event.button;\n\n\t\t// Add which for key events\n\t\tif ( event.which == null && rkeyEvent.test( event.type ) ) {\n\t\t\treturn event.charCode != null ? event.charCode : event.keyCode;\n\t\t}\n\n\t\t// Add which for click: 1 === left; 2 === middle; 3 === right\n\t\tif ( !event.which && button !== undefined && rmouseEvent.test( event.type ) ) {\n\t\t\tif ( button & 1 ) {\n\t\t\t\treturn 1;\n\t\t\t}\n\n\t\t\tif ( button & 2 ) {\n\t\t\t\treturn 3;\n\t\t\t}\n\n\t\t\tif ( button & 4 ) {\n\t\t\t\treturn 2;\n\t\t\t}\n\n\t\t\treturn 0;\n\t\t}\n\n\t\treturn event.which;\n\t}\n}, jQuery.event.addProp );\n\njQuery.each( { focus: \"focusin\", blur: \"focusout\" }, function( type, delegateType ) {\n\tjQuery.event.special[ type ] = {\n\n\t\t// Utilize native event if possible so blur/focus sequence is correct\n\t\tsetup: function() {\n\n\t\t\t// Claim the first handler\n\t\t\t// dataPriv.set( this, \"focus\", ... )\n\t\t\t// dataPriv.set( this, \"blur\", ... )\n\t\t\tleverageNative( this, type, expectSync );\n\n\t\t\t// Return false to allow normal processing in the caller\n\t\t\treturn false;\n\t\t},\n\t\ttrigger: function() {\n\n\t\t\t// Force setup before trigger\n\t\t\tleverageNative( this, type );\n\n\t\t\t// Return non-false to allow normal event-path propagation\n\t\t\treturn true;\n\t\t},\n\n\t\tdelegateType: delegateType\n\t};\n} );\n\n// Create mouseenter/leave events using mouseover/out and event-time checks\n// so that event delegation works in jQuery.\n// Do the same for pointerenter/pointerleave and pointerover/pointerout\n//\n// Support: Safari 7 only\n// Safari sends mouseenter too often; see:\n// https://bugs.chromium.org/p/chromium/issues/detail?id=470258\n// for the description of the bug (it existed in older Chrome versions as well).\njQuery.each( {\n\tmouseenter: \"mouseover\",\n\tmouseleave: \"mouseout\",\n\tpointerenter: \"pointerover\",\n\tpointerleave: \"pointerout\"\n}, function( orig, fix ) {\n\tjQuery.event.special[ orig ] = {\n\t\tdelegateType: fix,\n\t\tbindType: fix,\n\n\t\thandle: function( event ) {\n\t\t\tvar ret,\n\t\t\t\ttarget = this,\n\t\t\t\trelated = event.relatedTarget,\n\t\t\t\thandleObj = event.handleObj;\n\n\t\t\t// For mouseenter/leave call the handler if related is outside the target.\n\t\t\t// NB: No relatedTarget if the mouse left/entered the browser window\n\t\t\tif ( !related || ( related !== target && !jQuery.contains( target, related ) ) ) {\n\t\t\t\tevent.type = handleObj.origType;\n\t\t\t\tret = handleObj.handler.apply( this, arguments );\n\t\t\t\tevent.type = fix;\n\t\t\t}\n\t\t\treturn ret;\n\t\t}\n\t};\n} );\n\njQuery.fn.extend( {\n\n\ton: function( types, selector, data, fn ) {\n\t\treturn on( this, types, selector, data, fn );\n\t},\n\tone: function( types, selector, data, fn ) {\n\t\treturn on( this, types, selector, data, fn, 1 );\n\t},\n\toff: function( types, selector, fn ) {\n\t\tvar handleObj, type;\n\t\tif ( types && types.preventDefault && types.handleObj ) {\n\n\t\t\t// ( event )  dispatched jQuery.Event\n\t\t\thandleObj = types.handleObj;\n\t\t\tjQuery( types.delegateTarget ).off(\n\t\t\t\thandleObj.namespace ?\n\t\t\t\t\thandleObj.origType + \".\" + handleObj.namespace :\n\t\t\t\t\thandleObj.origType,\n\t\t\t\thandleObj.selector,\n\t\t\t\thandleObj.handler\n\t\t\t);\n\t\t\treturn this;\n\t\t}\n\t\tif ( typeof types === \"object\" ) {\n\n\t\t\t// ( types-object [, selector] )\n\t\t\tfor ( type in types ) {\n\t\t\t\tthis.off( type, selector, types[ type ] );\n\t\t\t}\n\t\t\treturn this;\n\t\t}\n\t\tif ( selector === false || typeof selector === \"function\" ) {\n\n\t\t\t// ( types [, fn] )\n\t\t\tfn = selector;\n\t\t\tselector = undefined;\n\t\t}\n\t\tif ( fn === false ) {\n\t\t\tfn = returnFalse;\n\t\t}\n\t\treturn this.each( function() {\n\t\t\tjQuery.event.remove( this, types, fn, selector );\n\t\t} );\n\t}\n} );\n\n\nvar\n\n\t/* eslint-disable max-len */\n\n\t// See https://github.com/eslint/eslint/issues/3229\n\trxhtmlTag = /<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\\/\\0>\\x20\\t\\r\\n\\f]*)[^>]*)\\/>/gi,\n\n\t/* eslint-enable */\n\n\t// Support: IE <=10 - 11, Edge 12 - 13 only\n\t// In IE/Edge using regex groups here causes severe slowdowns.\n\t// See https://connect.microsoft.com/IE/feedback/details/1736512/\n\trnoInnerhtml = /<script|<style|<link/i,\n\n\t// checked=\"checked\" or checked\n\trchecked = /checked\\s*(?:[^=]|=\\s*.checked.)/i,\n\trcleanScript = /^\\s*<!(?:\\[CDATA\\[|--)|(?:\\]\\]|--)>\\s*$/g;\n\n// Prefer a tbody over its parent table for containing new rows\nfunction manipulationTarget( elem, content ) {\n\tif ( nodeName( elem, \"table\" ) &&\n\t\tnodeName( content.nodeType !== 11 ? content : content.firstChild, \"tr\" ) ) {\n\n\t\treturn jQuery( elem ).children( \"tbody\" )[ 0 ] || elem;\n\t}\n\n\treturn elem;\n}\n\n// Replace/restore the type attribute of script elements for safe DOM manipulation\nfunction disableScript( elem ) {\n\telem.type = ( elem.getAttribute( \"type\" ) !== null ) + \"/\" + elem.type;\n\treturn elem;\n}\nfunction restoreScript( elem ) {\n\tif ( ( elem.type || \"\" ).slice( 0, 5 ) === \"true/\" ) {\n\t\telem.type = elem.type.slice( 5 );\n\t} else {\n\t\telem.removeAttribute( \"type\" );\n\t}\n\n\treturn elem;\n}\n\nfunction cloneCopyEvent( src, dest ) {\n\tvar i, l, type, pdataOld, pdataCur, udataOld, udataCur, events;\n\n\tif ( dest.nodeType !== 1 ) {\n\t\treturn;\n\t}\n\n\t// 1. Copy private data: events, handlers, etc.\n\tif ( dataPriv.hasData( src ) ) {\n\t\tpdataOld = dataPriv.access( src );\n\t\tpdataCur = dataPriv.set( dest, pdataOld );\n\t\tevents = pdataOld.events;\n\n\t\tif ( events ) {\n\t\t\tdelete pdataCur.handle;\n\t\t\tpdataCur.events = {};\n\n\t\t\tfor ( type in events ) {\n\t\t\t\tfor ( i = 0, l = events[ type ].length; i < l; i++ ) {\n\t\t\t\t\tjQuery.event.add( dest, type, events[ type ][ i ] );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// 2. Copy user data\n\tif ( dataUser.hasData( src ) ) {\n\t\tudataOld = dataUser.access( src );\n\t\tudataCur = jQuery.extend( {}, udataOld );\n\n\t\tdataUser.set( dest, udataCur );\n\t}\n}\n\n// Fix IE bugs, see support tests\nfunction fixInput( src, dest ) {\n\tvar nodeName = dest.nodeName.toLowerCase();\n\n\t// Fails to persist the checked state of a cloned checkbox or radio button.\n\tif ( nodeName === \"input\" && rcheckableType.test( src.type ) ) {\n\t\tdest.checked = src.checked;\n\n\t// Fails to return the selected option to the default selected state when cloning options\n\t} else if ( nodeName === \"input\" || nodeName === \"textarea\" ) {\n\t\tdest.defaultValue = src.defaultValue;\n\t}\n}\n\nfunction domManip( collection, args, callback, ignored ) {\n\n\t// Flatten any nested arrays\n\targs = concat.apply( [], args );\n\n\tvar fragment, first, scripts, hasScripts, node, doc,\n\t\ti = 0,\n\t\tl = collection.length,\n\t\tiNoClone = l - 1,\n\t\tvalue = args[ 0 ],\n\t\tvalueIsFunction = isFunction( value );\n\n\t// We can't cloneNode fragments that contain checked, in WebKit\n\tif ( valueIsFunction ||\n\t\t\t( l > 1 && typeof value === \"string\" &&\n\t\t\t\t!support.checkClone && rchecked.test( value ) ) ) {\n\t\treturn collection.each( function( index ) {\n\t\t\tvar self = collection.eq( index );\n\t\t\tif ( valueIsFunction ) {\n\t\t\t\targs[ 0 ] = value.call( this, index, self.html() );\n\t\t\t}\n\t\t\tdomManip( self, args, callback, ignored );\n\t\t} );\n\t}\n\n\tif ( l ) {\n\t\tfragment = buildFragment( args, collection[ 0 ].ownerDocument, false, collection, ignored );\n\t\tfirst = fragment.firstChild;\n\n\t\tif ( fragment.childNodes.length === 1 ) {\n\t\t\tfragment = first;\n\t\t}\n\n\t\t// Require either new content or an interest in ignored elements to invoke the callback\n\t\tif ( first || ignored ) {\n\t\t\tscripts = jQuery.map( getAll( fragment, \"script\" ), disableScript );\n\t\t\thasScripts = scripts.length;\n\n\t\t\t// Use the original fragment for the last item\n\t\t\t// instead of the first because it can end up\n\t\t\t// being emptied incorrectly in certain situations (#8070).\n\t\t\tfor ( ; i < l; i++ ) {\n\t\t\t\tnode = fragment;\n\n\t\t\t\tif ( i !== iNoClone ) {\n\t\t\t\t\tnode = jQuery.clone( node, true, true );\n\n\t\t\t\t\t// Keep references to cloned scripts for later restoration\n\t\t\t\t\tif ( hasScripts ) {\n\n\t\t\t\t\t\t// Support: Android <=4.0 only, PhantomJS 1 only\n\t\t\t\t\t\t// push.apply(_, arraylike) throws on ancient WebKit\n\t\t\t\t\t\tjQuery.merge( scripts, getAll( node, \"script\" ) );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tcallback.call( collection[ i ], node, i );\n\t\t\t}\n\n\t\t\tif ( hasScripts ) {\n\t\t\t\tdoc = scripts[ scripts.length - 1 ].ownerDocument;\n\n\t\t\t\t// Reenable scripts\n\t\t\t\tjQuery.map( scripts, restoreScript );\n\n\t\t\t\t// Evaluate executable scripts on first document insertion\n\t\t\t\tfor ( i = 0; i < hasScripts; i++ ) {\n\t\t\t\t\tnode = scripts[ i ];\n\t\t\t\t\tif ( rscriptType.test( node.type || \"\" ) &&\n\t\t\t\t\t\t!dataPriv.access( node, \"globalEval\" ) &&\n\t\t\t\t\t\tjQuery.contains( doc, node ) ) {\n\n\t\t\t\t\t\tif ( node.src && ( node.type || \"\" ).toLowerCase()  !== \"module\" ) {\n\n\t\t\t\t\t\t\t// Optional AJAX dependency, but won't run scripts if not present\n\t\t\t\t\t\t\tif ( jQuery._evalUrl && !node.noModule ) {\n\t\t\t\t\t\t\t\tjQuery._evalUrl( node.src, {\n\t\t\t\t\t\t\t\t\tnonce: node.nonce || node.getAttribute( \"nonce\" )\n\t\t\t\t\t\t\t\t} );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tDOMEval( node.textContent.replace( rcleanScript, \"\" ), node, doc );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn collection;\n}\n\nfunction remove( elem, selector, keepData ) {\n\tvar node,\n\t\tnodes = selector ? jQuery.filter( selector, elem ) : elem,\n\t\ti = 0;\n\n\tfor ( ; ( node = nodes[ i ] ) != null; i++ ) {\n\t\tif ( !keepData && node.nodeType === 1 ) {\n\t\t\tjQuery.cleanData( getAll( node ) );\n\t\t}\n\n\t\tif ( node.parentNode ) {\n\t\t\tif ( keepData && isAttached( node ) ) {\n\t\t\t\tsetGlobalEval( getAll( node, \"script\" ) );\n\t\t\t}\n\t\t\tnode.parentNode.removeChild( node );\n\t\t}\n\t}\n\n\treturn elem;\n}\n\njQuery.extend( {\n\thtmlPrefilter: function( html ) {\n\t\treturn html.replace( rxhtmlTag, \"<$1></$2>\" );\n\t},\n\n\tclone: function( elem, dataAndEvents, deepDataAndEvents ) {\n\t\tvar i, l, srcElements, destElements,\n\t\t\tclone = elem.cloneNode( true ),\n\t\t\tinPage = isAttached( elem );\n\n\t\t// Fix IE cloning issues\n\t\tif ( !support.noCloneChecked && ( elem.nodeType === 1 || elem.nodeType === 11 ) &&\n\t\t\t\t!jQuery.isXMLDoc( elem ) ) {\n\n\t\t\t// We eschew Sizzle here for performance reasons: https://jsperf.com/getall-vs-sizzle/2\n\t\t\tdestElements = getAll( clone );\n\t\t\tsrcElements = getAll( elem );\n\n\t\t\tfor ( i = 0, l = srcElements.length; i < l; i++ ) {\n\t\t\t\tfixInput( srcElements[ i ], destElements[ i ] );\n\t\t\t}\n\t\t}\n\n\t\t// Copy the events from the original to the clone\n\t\tif ( dataAndEvents ) {\n\t\t\tif ( deepDataAndEvents ) {\n\t\t\t\tsrcElements = srcElements || getAll( elem );\n\t\t\t\tdestElements = destElements || getAll( clone );\n\n\t\t\t\tfor ( i = 0, l = srcElements.length; i < l; i++ ) {\n\t\t\t\t\tcloneCopyEvent( srcElements[ i ], destElements[ i ] );\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcloneCopyEvent( elem, clone );\n\t\t\t}\n\t\t}\n\n\t\t// Preserve script evaluation history\n\t\tdestElements = getAll( clone, \"script\" );\n\t\tif ( destElements.length > 0 ) {\n\t\t\tsetGlobalEval( destElements, !inPage && getAll( elem, \"script\" ) );\n\t\t}\n\n\t\t// Return the cloned set\n\t\treturn clone;\n\t},\n\n\tcleanData: function( elems ) {\n\t\tvar data, elem, type,\n\t\t\tspecial = jQuery.event.special,\n\t\t\ti = 0;\n\n\t\tfor ( ; ( elem = elems[ i ] ) !== undefined; i++ ) {\n\t\t\tif ( acceptData( elem ) ) {\n\t\t\t\tif ( ( data = elem[ dataPriv.expando ] ) ) {\n\t\t\t\t\tif ( data.events ) {\n\t\t\t\t\t\tfor ( type in data.events ) {\n\t\t\t\t\t\t\tif ( special[ type ] ) {\n\t\t\t\t\t\t\t\tjQuery.event.remove( elem, type );\n\n\t\t\t\t\t\t\t// This is a shortcut to avoid jQuery.event.remove's overhead\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tjQuery.removeEvent( elem, type, data.handle );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Support: Chrome <=35 - 45+\n\t\t\t\t\t// Assign undefined instead of using delete, see Data#remove\n\t\t\t\t\telem[ dataPriv.expando ] = undefined;\n\t\t\t\t}\n\t\t\t\tif ( elem[ dataUser.expando ] ) {\n\n\t\t\t\t\t// Support: Chrome <=35 - 45+\n\t\t\t\t\t// Assign undefined instead of using delete, see Data#remove\n\t\t\t\t\telem[ dataUser.expando ] = undefined;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n} );\n\njQuery.fn.extend( {\n\tdetach: function( selector ) {\n\t\treturn remove( this, selector, true );\n\t},\n\n\tremove: function( selector ) {\n\t\treturn remove( this, selector );\n\t},\n\n\ttext: function( value ) {\n\t\treturn access( this, function( value ) {\n\t\t\treturn value === undefined ?\n\t\t\t\tjQuery.text( this ) :\n\t\t\t\tthis.empty().each( function() {\n\t\t\t\t\tif ( this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9 ) {\n\t\t\t\t\t\tthis.textContent = value;\n\t\t\t\t\t}\n\t\t\t\t} );\n\t\t}, null, value, arguments.length );\n\t},\n\n\tappend: function() {\n\t\treturn domManip( this, arguments, function( elem ) {\n\t\t\tif ( this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9 ) {\n\t\t\t\tvar target = manipulationTarget( this, elem );\n\t\t\t\ttarget.appendChild( elem );\n\t\t\t}\n\t\t} );\n\t},\n\n\tprepend: function() {\n\t\treturn domManip( this, arguments, function( elem ) {\n\t\t\tif ( this.nodeType === 1 || this.nodeType === 11 || this.nodeType === 9 ) {\n\t\t\t\tvar target = manipulationTarget( this, elem );\n\t\t\t\ttarget.insertBefore( elem, target.firstChild );\n\t\t\t}\n\t\t} );\n\t},\n\n\tbefore: function() {\n\t\treturn domManip( this, arguments, function( elem ) {\n\t\t\tif ( this.parentNode ) {\n\t\t\t\tthis.parentNode.insertBefore( elem, this );\n\t\t\t}\n\t\t} );\n\t},\n\n\tafter: function() {\n\t\treturn domManip( this, arguments, function( elem ) {\n\t\t\tif ( this.parentNode ) {\n\t\t\t\tthis.parentNode.insertBefore( elem, this.nextSibling );\n\t\t\t}\n\t\t} );\n\t},\n\n\tempty: function() {\n\t\tvar elem,\n\t\t\ti = 0;\n\n\t\tfor ( ; ( elem = this[ i ] ) != null; i++ ) {\n\t\t\tif ( elem.nodeType === 1 ) {\n\n\t\t\t\t// Prevent memory leaks\n\t\t\t\tjQuery.cleanData( getAll( elem, false ) );\n\n\t\t\t\t// Remove any remaining nodes\n\t\t\t\telem.textContent = \"\";\n\t\t\t}\n\t\t}\n\n\t\treturn this;\n\t},\n\n\tclone: function( dataAndEvents, deepDataAndEvents ) {\n\t\tdataAndEvents = dataAndEvents == null ? false : dataAndEvents;\n\t\tdeepDataAndEvents = deepDataAndEvents == null ? dataAndEvents : deepDataAndEvents;\n\n\t\treturn this.map( function() {\n\t\t\treturn jQuery.clone( this, dataAndEvents, deepDataAndEvents );\n\t\t} );\n\t},\n\n\thtml: function( value ) {\n\t\treturn access( this, function( value ) {\n\t\t\tvar elem = this[ 0 ] || {},\n\t\t\t\ti = 0,\n\t\t\t\tl = this.length;\n\n\t\t\tif ( value === undefined && elem.nodeType === 1 ) {\n\t\t\t\treturn elem.innerHTML;\n\t\t\t}\n\n\t\t\t// See if we can take a shortcut and just use innerHTML\n\t\t\tif ( typeof value === \"string\" && !rnoInnerhtml.test( value ) &&\n\t\t\t\t!wrapMap[ ( rtagName.exec( value ) || [ \"\", \"\" ] )[ 1 ].toLowerCase() ] ) {\n\n\t\t\t\tvalue = jQuery.htmlPrefilter( value );\n\n\t\t\t\ttry {\n\t\t\t\t\tfor ( ; i < l; i++ ) {\n\t\t\t\t\t\telem = this[ i ] || {};\n\n\t\t\t\t\t\t// Remove element nodes and prevent memory leaks\n\t\t\t\t\t\tif ( elem.nodeType === 1 ) {\n\t\t\t\t\t\t\tjQuery.cleanData( getAll( elem, false ) );\n\t\t\t\t\t\t\telem.innerHTML = value;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\telem = 0;\n\n\t\t\t\t// If using innerHTML throws an exception, use the fallback method\n\t\t\t\t} catch ( e ) {}\n\t\t\t}\n\n\t\t\tif ( elem ) {\n\t\t\t\tthis.empty().append( value );\n\t\t\t}\n\t\t}, null, value, arguments.length );\n\t},\n\n\treplaceWith: function() {\n\t\tvar ignored = [];\n\n\t\t// Make the changes, replacing each non-ignored context element with the new content\n\t\treturn domManip( this, arguments, function( elem ) {\n\t\t\tvar parent = this.parentNode;\n\n\t\t\tif ( jQuery.inArray( this, ignored ) < 0 ) {\n\t\t\t\tjQuery.cleanData( getAll( this ) );\n\t\t\t\tif ( parent ) {\n\t\t\t\t\tparent.replaceChild( elem, this );\n\t\t\t\t}\n\t\t\t}\n\n\t\t// Force callback invocation\n\t\t}, ignored );\n\t}\n} );\n\njQuery.each( {\n\tappendTo: \"append\",\n\tprependTo: \"prepend\",\n\tinsertBefore: \"before\",\n\tinsertAfter: \"after\",\n\treplaceAll: \"replaceWith\"\n}, function( name, original ) {\n\tjQuery.fn[ name ] = function( selector ) {\n\t\tvar elems,\n\t\t\tret = [],\n\t\t\tinsert = jQuery( selector ),\n\t\t\tlast = insert.length - 1,\n\t\t\ti = 0;\n\n\t\tfor ( ; i <= last; i++ ) {\n\t\t\telems = i === last ? this : this.clone( true );\n\t\t\tjQuery( insert[ i ] )[ original ]( elems );\n\n\t\t\t// Support: Android <=4.0 only, PhantomJS 1 only\n\t\t\t// .get() because push.apply(_, arraylike) throws on ancient WebKit\n\t\t\tpush.apply( ret, elems.get() );\n\t\t}\n\n\t\treturn this.pushStack( ret );\n\t};\n} );\nvar rnumnonpx = new RegExp( \"^(\" + pnum + \")(?!px)[a-z%]+$\", \"i\" );\n\nvar getStyles = function( elem ) {\n\n\t\t// Support: IE <=11 only, Firefox <=30 (#15098, #14150)\n\t\t// IE throws on elements created in popups\n\t\t// FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n\t\tvar view = elem.ownerDocument.defaultView;\n\n\t\tif ( !view || !view.opener ) {\n\t\t\tview = window;\n\t\t}\n\n\t\treturn view.getComputedStyle( elem );\n\t};\n\nvar rboxStyle = new RegExp( cssExpand.join( \"|\" ), \"i\" );\n\n\n\n( function() {\n\n\t// Executing both pixelPosition & boxSizingReliable tests require only one layout\n\t// so they're executed at the same time to save the second computation.\n\tfunction computeStyleTests() {\n\n\t\t// This is a singleton, we need to execute it only once\n\t\tif ( !div ) {\n\t\t\treturn;\n\t\t}\n\n\t\tcontainer.style.cssText = \"position:absolute;left:-11111px;width:60px;\" +\n\t\t\t\"margin-top:1px;padding:0;border:0\";\n\t\tdiv.style.cssText =\n\t\t\t\"position:relative;display:block;box-sizing:border-box;overflow:scroll;\" +\n\t\t\t\"margin:auto;border:1px;padding:1px;\" +\n\t\t\t\"width:60%;top:1%\";\n\t\tdocumentElement.appendChild( container ).appendChild( div );\n\n\t\tvar divStyle = window.getComputedStyle( div );\n\t\tpixelPositionVal = divStyle.top !== \"1%\";\n\n\t\t// Support: Android 4.0 - 4.3 only, Firefox <=3 - 44\n\t\treliableMarginLeftVal = roundPixelMeasures( divStyle.marginLeft ) === 12;\n\n\t\t// Support: Android 4.0 - 4.3 only, Safari <=9.1 - 10.1, iOS <=7.0 - 9.3\n\t\t// Some styles come back with percentage values, even though they shouldn't\n\t\tdiv.style.right = \"60%\";\n\t\tpixelBoxStylesVal = roundPixelMeasures( divStyle.right ) === 36;\n\n\t\t// Support: IE 9 - 11 only\n\t\t// Detect misreporting of content dimensions for box-sizing:border-box elements\n\t\tboxSizingReliableVal = roundPixelMeasures( divStyle.width ) === 36;\n\n\t\t// Support: IE 9 only\n\t\t// Detect overflow:scroll screwiness (gh-3699)\n\t\t// Support: Chrome <=64\n\t\t// Don't get tricked when zoom affects offsetWidth (gh-4029)\n\t\tdiv.style.position = \"absolute\";\n\t\tscrollboxSizeVal = roundPixelMeasures( div.offsetWidth / 3 ) === 12;\n\n\t\tdocumentElement.removeChild( container );\n\n\t\t// Nullify the div so it wouldn't be stored in the memory and\n\t\t// it will also be a sign that checks already performed\n\t\tdiv = null;\n\t}\n\n\tfunction roundPixelMeasures( measure ) {\n\t\treturn Math.round( parseFloat( measure ) );\n\t}\n\n\tvar pixelPositionVal, boxSizingReliableVal, scrollboxSizeVal, pixelBoxStylesVal,\n\t\treliableMarginLeftVal,\n\t\tcontainer = document.createElement( \"div\" ),\n\t\tdiv = document.createElement( \"div\" );\n\n\t// Finish early in limited (non-browser) environments\n\tif ( !div.style ) {\n\t\treturn;\n\t}\n\n\t// Support: IE <=9 - 11 only\n\t// Style of cloned element affects source element cloned (#8908)\n\tdiv.style.backgroundClip = \"content-box\";\n\tdiv.cloneNode( true ).style.backgroundClip = \"\";\n\tsupport.clearCloneStyle = div.style.backgroundClip === \"content-box\";\n\n\tjQuery.extend( support, {\n\t\tboxSizingReliable: function() {\n\t\t\tcomputeStyleTests();\n\t\t\treturn boxSizingReliableVal;\n\t\t},\n\t\tpixelBoxStyles: function() {\n\t\t\tcomputeStyleTests();\n\t\t\treturn pixelBoxStylesVal;\n\t\t},\n\t\tpixelPosition: function() {\n\t\t\tcomputeStyleTests();\n\t\t\treturn pixelPositionVal;\n\t\t},\n\t\treliableMarginLeft: function() {\n\t\t\tcomputeStyleTests();\n\t\t\treturn reliableMarginLeftVal;\n\t\t},\n\t\tscrollboxSize: function() {\n\t\t\tcomputeStyleTests();\n\t\t\treturn scrollboxSizeVal;\n\t\t}\n\t} );\n} )();\n\n\nfunction curCSS( elem, name, computed ) {\n\tvar width, minWidth, maxWidth, ret,\n\n\t\t// Support: Firefox 51+\n\t\t// Retrieving style before computed somehow\n\t\t// fixes an issue with getting wrong values\n\t\t// on detached elements\n\t\tstyle = elem.style;\n\n\tcomputed = computed || getStyles( elem );\n\n\t// getPropertyValue is needed for:\n\t//   .css('filter') (IE 9 only, #12537)\n\t//   .css('--customProperty) (#3144)\n\tif ( computed ) {\n\t\tret = computed.getPropertyValue( name ) || computed[ name ];\n\n\t\tif ( ret === \"\" && !isAttached( elem ) ) {\n\t\t\tret = jQuery.style( elem, name );\n\t\t}\n\n\t\t// A tribute to the \"awesome hack by Dean Edwards\"\n\t\t// Android Browser returns percentage for some values,\n\t\t// but width seems to be reliably pixels.\n\t\t// This is against the CSSOM draft spec:\n\t\t// https://drafts.csswg.org/cssom/#resolved-values\n\t\tif ( !support.pixelBoxStyles() && rnumnonpx.test( ret ) && rboxStyle.test( name ) ) {\n\n\t\t\t// Remember the original values\n\t\t\twidth = style.width;\n\t\t\tminWidth = style.minWidth;\n\t\t\tmaxWidth = style.maxWidth;\n\n\t\t\t// Put in the new values to get a computed value out\n\t\t\tstyle.minWidth = style.maxWidth = style.width = ret;\n\t\t\tret = computed.width;\n\n\t\t\t// Revert the changed values\n\t\t\tstyle.width = width;\n\t\t\tstyle.minWidth = minWidth;\n\t\t\tstyle.maxWidth = maxWidth;\n\t\t}\n\t}\n\n\treturn ret !== undefined ?\n\n\t\t// Support: IE <=9 - 11 only\n\t\t// IE returns zIndex value as an integer.\n\t\tret + \"\" :\n\t\tret;\n}\n\n\nfunction addGetHookIf( conditionFn, hookFn ) {\n\n\t// Define the hook, we'll check on the first run if it's really needed.\n\treturn {\n\t\tget: function() {\n\t\t\tif ( conditionFn() ) {\n\n\t\t\t\t// Hook not needed (or it's not possible to use it due\n\t\t\t\t// to missing dependency), remove it.\n\t\t\t\tdelete this.get;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Hook needed; redefine it so that the support test is not executed again.\n\t\t\treturn ( this.get = hookFn ).apply( this, arguments );\n\t\t}\n\t};\n}\n\n\nvar cssPrefixes = [ \"Webkit\", \"Moz\", \"ms\" ],\n\temptyStyle = document.createElement( \"div\" ).style,\n\tvendorProps = {};\n\n// Return a vendor-prefixed property or undefined\nfunction vendorPropName( name ) {\n\n\t// Check for vendor prefixed names\n\tvar capName = name[ 0 ].toUpperCase() + name.slice( 1 ),\n\t\ti = cssPrefixes.length;\n\n\twhile ( i-- ) {\n\t\tname = cssPrefixes[ i ] + capName;\n\t\tif ( name in emptyStyle ) {\n\t\t\treturn name;\n\t\t}\n\t}\n}\n\n// Return a potentially-mapped jQuery.cssProps or vendor prefixed property\nfunction finalPropName( name ) {\n\tvar final = jQuery.cssProps[ name ] || vendorProps[ name ];\n\n\tif ( final ) {\n\t\treturn final;\n\t}\n\tif ( name in emptyStyle ) {\n\t\treturn name;\n\t}\n\treturn vendorProps[ name ] = vendorPropName( name ) || name;\n}\n\n\nvar\n\n\t// Swappable if display is none or starts with table\n\t// except \"table\", \"table-cell\", or \"table-caption\"\n\t// See here for display values: https://developer.mozilla.org/en-US/docs/CSS/display\n\trdisplayswap = /^(none|table(?!-c[ea]).+)/,\n\trcustomProp = /^--/,\n\tcssShow = { position: \"absolute\", visibility: \"hidden\", display: \"block\" },\n\tcssNormalTransform = {\n\t\tletterSpacing: \"0\",\n\t\tfontWeight: \"400\"\n\t};\n\nfunction setPositiveNumber( elem, value, subtract ) {\n\n\t// Any relative (+/-) values have already been\n\t// normalized at this point\n\tvar matches = rcssNum.exec( value );\n\treturn matches ?\n\n\t\t// Guard against undefined \"subtract\", e.g., when used as in cssHooks\n\t\tMath.max( 0, matches[ 2 ] - ( subtract || 0 ) ) + ( matches[ 3 ] || \"px\" ) :\n\t\tvalue;\n}\n\nfunction boxModelAdjustment( elem, dimension, box, isBorderBox, styles, computedVal ) {\n\tvar i = dimension === \"width\" ? 1 : 0,\n\t\textra = 0,\n\t\tdelta = 0;\n\n\t// Adjustment may not be necessary\n\tif ( box === ( isBorderBox ? \"border\" : \"content\" ) ) {\n\t\treturn 0;\n\t}\n\n\tfor ( ; i < 4; i += 2 ) {\n\n\t\t// Both box models exclude margin\n\t\tif ( box === \"margin\" ) {\n\t\t\tdelta += jQuery.css( elem, box + cssExpand[ i ], true, styles );\n\t\t}\n\n\t\t// If we get here with a content-box, we're seeking \"padding\" or \"border\" or \"margin\"\n\t\tif ( !isBorderBox ) {\n\n\t\t\t// Add padding\n\t\t\tdelta += jQuery.css( elem, \"padding\" + cssExpand[ i ], true, styles );\n\n\t\t\t// For \"border\" or \"margin\", add border\n\t\t\tif ( box !== \"padding\" ) {\n\t\t\t\tdelta += jQuery.css( elem, \"border\" + cssExpand[ i ] + \"Width\", true, styles );\n\n\t\t\t// But still keep track of it otherwise\n\t\t\t} else {\n\t\t\t\textra += jQuery.css( elem, \"border\" + cssExpand[ i ] + \"Width\", true, styles );\n\t\t\t}\n\n\t\t// If we get here with a border-box (content + padding + border), we're seeking \"content\" or\n\t\t// \"padding\" or \"margin\"\n\t\t} else {\n\n\t\t\t// For \"content\", subtract padding\n\t\t\tif ( box === \"content\" ) {\n\t\t\t\tdelta -= jQuery.css( elem, \"padding\" + cssExpand[ i ], true, styles );\n\t\t\t}\n\n\t\t\t// For \"content\" or \"padding\", subtract border\n\t\t\tif ( box !== \"margin\" ) {\n\t\t\t\tdelta -= jQuery.css( elem, \"border\" + cssExpand[ i ] + \"Width\", true, styles );\n\t\t\t}\n\t\t}\n\t}\n\n\t// Account for positive content-box scroll gutter when requested by providing computedVal\n\tif ( !isBorderBox && computedVal >= 0 ) {\n\n\t\t// offsetWidth/offsetHeight is a rounded sum of content, padding, scroll gutter, and border\n\t\t// Assuming integer scroll gutter, subtract the rest and round down\n\t\tdelta += Math.max( 0, Math.ceil(\n\t\t\telem[ \"offset\" + dimension[ 0 ].toUpperCase() + dimension.slice( 1 ) ] -\n\t\t\tcomputedVal -\n\t\t\tdelta -\n\t\t\textra -\n\t\t\t0.5\n\n\t\t// If offsetWidth/offsetHeight is unknown, then we can't determine content-box scroll gutter\n\t\t// Use an explicit zero to avoid NaN (gh-3964)\n\t\t) ) || 0;\n\t}\n\n\treturn delta;\n}\n\nfunction getWidthOrHeight( elem, dimension, extra ) {\n\n\t// Start with computed style\n\tvar styles = getStyles( elem ),\n\n\t\t// To avoid forcing a reflow, only fetch boxSizing if we need it (gh-4322).\n\t\t// Fake content-box until we know it's needed to know the true value.\n\t\tboxSizingNeeded = !support.boxSizingReliable() || extra,\n\t\tisBorderBox = boxSizingNeeded &&\n\t\t\tjQuery.css( elem, \"boxSizing\", false, styles ) === \"border-box\",\n\t\tvalueIsBorderBox = isBorderBox,\n\n\t\tval = curCSS( elem, dimension, styles ),\n\t\toffsetProp = \"offset\" + dimension[ 0 ].toUpperCase() + dimension.slice( 1 );\n\n\t// Support: Firefox <=54\n\t// Return a confounding non-pixel value or feign ignorance, as appropriate.\n\tif ( rnumnonpx.test( val ) ) {\n\t\tif ( !extra ) {\n\t\t\treturn val;\n\t\t}\n\t\tval = \"auto\";\n\t}\n\n\n\t// Fall back to offsetWidth/offsetHeight when value is \"auto\"\n\t// This happens for inline elements with no explicit setting (gh-3571)\n\t// Support: Android <=4.1 - 4.3 only\n\t// Also use offsetWidth/offsetHeight for misreported inline dimensions (gh-3602)\n\t// Support: IE 9-11 only\n\t// Also use offsetWidth/offsetHeight for when box sizing is unreliable\n\t// We use getClientRects() to check for hidden/disconnected.\n\t// In those cases, the computed value can be trusted to be border-box\n\tif ( ( !support.boxSizingReliable() && isBorderBox ||\n\t\tval === \"auto\" ||\n\t\t!parseFloat( val ) && jQuery.css( elem, \"display\", false, styles ) === \"inline\" ) &&\n\t\telem.getClientRects().length ) {\n\n\t\tisBorderBox = jQuery.css( elem, \"boxSizing\", false, styles ) === \"border-box\";\n\n\t\t// Where available, offsetWidth/offsetHeight approximate border box dimensions.\n\t\t// Where not available (e.g., SVG), assume unreliable box-sizing and interpret the\n\t\t// retrieved value as a content box dimension.\n\t\tvalueIsBorderBox = offsetProp in elem;\n\t\tif ( valueIsBorderBox ) {\n\t\t\tval = elem[ offsetProp ];\n\t\t}\n\t}\n\n\t// Normalize \"\" and auto\n\tval = parseFloat( val ) || 0;\n\n\t// Adjust for the element's box model\n\treturn ( val +\n\t\tboxModelAdjustment(\n\t\t\telem,\n\t\t\tdimension,\n\t\t\textra || ( isBorderBox ? \"border\" : \"content\" ),\n\t\t\tvalueIsBorderBox,\n\t\t\tstyles,\n\n\t\t\t// Provide the current computed size to request scroll gutter calculation (gh-3589)\n\t\t\tval\n\t\t)\n\t) + \"px\";\n}\n\njQuery.extend( {\n\n\t// Add in style property hooks for overriding the default\n\t// behavior of getting and setting a style property\n\tcssHooks: {\n\t\topacity: {\n\t\t\tget: function( elem, computed ) {\n\t\t\t\tif ( computed ) {\n\n\t\t\t\t\t// We should always get a number back from opacity\n\t\t\t\t\tvar ret = curCSS( elem, \"opacity\" );\n\t\t\t\t\treturn ret === \"\" ? \"1\" : ret;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\n\t// Don't automatically add \"px\" to these possibly-unitless properties\n\tcssNumber: {\n\t\t\"animationIterationCount\": true,\n\t\t\"columnCount\": true,\n\t\t\"fillOpacity\": true,\n\t\t\"flexGrow\": true,\n\t\t\"flexShrink\": true,\n\t\t\"fontWeight\": true,\n\t\t\"gridArea\": true,\n\t\t\"gridColumn\": true,\n\t\t\"gridColumnEnd\": true,\n\t\t\"gridColumnStart\": true,\n\t\t\"gridRow\": true,\n\t\t\"gridRowEnd\": true,\n\t\t\"gridRowStart\": true,\n\t\t\"lineHeight\": true,\n\t\t\"opacity\": true,\n\t\t\"order\": true,\n\t\t\"orphans\": true,\n\t\t\"widows\": true,\n\t\t\"zIndex\": true,\n\t\t\"zoom\": true\n\t},\n\n\t// Add in properties whose names you wish to fix before\n\t// setting or getting the value\n\tcssProps: {},\n\n\t// Get and set the style property on a DOM Node\n\tstyle: function( elem, name, value, extra ) {\n\n\t\t// Don't set styles on text and comment nodes\n\t\tif ( !elem || elem.nodeType === 3 || elem.nodeType === 8 || !elem.style ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Make sure that we're working with the right name\n\t\tvar ret, type, hooks,\n\t\t\torigName = camelCase( name ),\n\t\t\tisCustomProp = rcustomProp.test( name ),\n\t\t\tstyle = elem.style;\n\n\t\t// Make sure that we're working with the right name. We don't\n\t\t// want to query the value if it is a CSS custom property\n\t\t// since they are user-defined.\n\t\tif ( !isCustomProp ) {\n\t\t\tname = finalPropName( origName );\n\t\t}\n\n\t\t// Gets hook for the prefixed version, then unprefixed version\n\t\thooks = jQuery.cssHooks[ name ] || jQuery.cssHooks[ origName ];\n\n\t\t// Check if we're setting a value\n\t\tif ( value !== undefined ) {\n\t\t\ttype = typeof value;\n\n\t\t\t// Convert \"+=\" or \"-=\" to relative numbers (#7345)\n\t\t\tif ( type === \"string\" && ( ret = rcssNum.exec( value ) ) && ret[ 1 ] ) {\n\t\t\t\tvalue = adjustCSS( elem, name, ret );\n\n\t\t\t\t// Fixes bug #9237\n\t\t\t\ttype = \"number\";\n\t\t\t}\n\n\t\t\t// Make sure that null and NaN values aren't set (#7116)\n\t\t\tif ( value == null || value !== value ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// If a number was passed in, add the unit (except for certain CSS properties)\n\t\t\t// The isCustomProp check can be removed in jQuery 4.0 when we only auto-append\n\t\t\t// \"px\" to a few hardcoded values.\n\t\t\tif ( type === \"number\" && !isCustomProp ) {\n\t\t\t\tvalue += ret && ret[ 3 ] || ( jQuery.cssNumber[ origName ] ? \"\" : \"px\" );\n\t\t\t}\n\n\t\t\t// background-* props affect original clone's values\n\t\t\tif ( !support.clearCloneStyle && value === \"\" && name.indexOf( \"background\" ) === 0 ) {\n\t\t\t\tstyle[ name ] = \"inherit\";\n\t\t\t}\n\n\t\t\t// If a hook was provided, use that value, otherwise just set the specified value\n\t\t\tif ( !hooks || !( \"set\" in hooks ) ||\n\t\t\t\t( value = hooks.set( elem, value, extra ) ) !== undefined ) {\n\n\t\t\t\tif ( isCustomProp ) {\n\t\t\t\t\tstyle.setProperty( name, value );\n\t\t\t\t} else {\n\t\t\t\t\tstyle[ name ] = value;\n\t\t\t\t}\n\t\t\t}\n\n\t\t} else {\n\n\t\t\t// If a hook was provided get the non-computed value from there\n\t\t\tif ( hooks && \"get\" in hooks &&\n\t\t\t\t( ret = hooks.get( elem, false, extra ) ) !== undefined ) {\n\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\t// Otherwise just get the value from the style object\n\t\t\treturn style[ name ];\n\t\t}\n\t},\n\n\tcss: function( elem, name, extra, styles ) {\n\t\tvar val, num, hooks,\n\t\t\torigName = camelCase( name ),\n\t\t\tisCustomProp = rcustomProp.test( name );\n\n\t\t// Make sure that we're working with the right name. We don't\n\t\t// want to modify the value if it is a CSS custom property\n\t\t// since they are user-defined.\n\t\tif ( !isCustomProp ) {\n\t\t\tname = finalPropName( origName );\n\t\t}\n\n\t\t// Try prefixed name followed by the unprefixed name\n\t\thooks = jQuery.cssHooks[ name ] || jQuery.cssHooks[ origName ];\n\n\t\t// If a hook was provided get the computed value from there\n\t\tif ( hooks && \"get\" in hooks ) {\n\t\t\tval = hooks.get( elem, true, extra );\n\t\t}\n\n\t\t// Otherwise, if a way to get the computed value exists, use that\n\t\tif ( val === undefined ) {\n\t\t\tval = curCSS( elem, name, styles );\n\t\t}\n\n\t\t// Convert \"normal\" to computed value\n\t\tif ( val === \"normal\" && name in cssNormalTransform ) {\n\t\t\tval = cssNormalTransform[ name ];\n\t\t}\n\n\t\t// Make numeric if forced or a qualifier was provided and val looks numeric\n\t\tif ( extra === \"\" || extra ) {\n\t\t\tnum = parseFloat( val );\n\t\t\treturn extra === true || isFinite( num ) ? num || 0 : val;\n\t\t}\n\n\t\treturn val;\n\t}\n} );\n\njQuery.each( [ \"height\", \"width\" ], function( i, dimension ) {\n\tjQuery.cssHooks[ dimension ] = {\n\t\tget: function( elem, computed, extra ) {\n\t\t\tif ( computed ) {\n\n\t\t\t\t// Certain elements can have dimension info if we invisibly show them\n\t\t\t\t// but it must have a current display style that would benefit\n\t\t\t\treturn rdisplayswap.test( jQuery.css( elem, \"display\" ) ) &&\n\n\t\t\t\t\t// Support: Safari 8+\n\t\t\t\t\t// Table columns in Safari have non-zero offsetWidth & zero\n\t\t\t\t\t// getBoundingClientRect().width unless display is changed.\n\t\t\t\t\t// Support: IE <=11 only\n\t\t\t\t\t// Running getBoundingClientRect on a disconnected node\n\t\t\t\t\t// in IE throws an error.\n\t\t\t\t\t( !elem.getClientRects().length || !elem.getBoundingClientRect().width ) ?\n\t\t\t\t\t\tswap( elem, cssShow, function() {\n\t\t\t\t\t\t\treturn getWidthOrHeight( elem, dimension, extra );\n\t\t\t\t\t\t} ) :\n\t\t\t\t\t\tgetWidthOrHeight( elem, dimension, extra );\n\t\t\t}\n\t\t},\n\n\t\tset: function( elem, value, extra ) {\n\t\t\tvar matches,\n\t\t\t\tstyles = getStyles( elem ),\n\n\t\t\t\t// Only read styles.position if the test has a chance to fail\n\t\t\t\t// to avoid forcing a reflow.\n\t\t\t\tscrollboxSizeBuggy = !support.scrollboxSize() &&\n\t\t\t\t\tstyles.position === \"absolute\",\n\n\t\t\t\t// To avoid forcing a reflow, only fetch boxSizing if we need it (gh-3991)\n\t\t\t\tboxSizingNeeded = scrollboxSizeBuggy || extra,\n\t\t\t\tisBorderBox = boxSizingNeeded &&\n\t\t\t\t\tjQuery.css( elem, \"boxSizing\", false, styles ) === \"border-box\",\n\t\t\t\tsubtract = extra ?\n\t\t\t\t\tboxModelAdjustment(\n\t\t\t\t\t\telem,\n\t\t\t\t\t\tdimension,\n\t\t\t\t\t\textra,\n\t\t\t\t\t\tisBorderBox,\n\t\t\t\t\t\tstyles\n\t\t\t\t\t) :\n\t\t\t\t\t0;\n\n\t\t\t// Account for unreliable border-box dimensions by comparing offset* to computed and\n\t\t\t// faking a content-box to get border and padding (gh-3699)\n\t\t\tif ( isBorderBox && scrollboxSizeBuggy ) {\n\t\t\t\tsubtract -= Math.ceil(\n\t\t\t\t\telem[ \"offset\" + dimension[ 0 ].toUpperCase() + dimension.slice( 1 ) ] -\n\t\t\t\t\tparseFloat( styles[ dimension ] ) -\n\t\t\t\t\tboxModelAdjustment( elem, dimension, \"border\", false, styles ) -\n\t\t\t\t\t0.5\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t// Convert to pixels if value adjustment is needed\n\t\t\tif ( subtract && ( matches = rcssNum.exec( value ) ) &&\n\t\t\t\t( matches[ 3 ] || \"px\" ) !== \"px\" ) {\n\n\t\t\t\telem.style[ dimension ] = value;\n\t\t\t\tvalue = jQuery.css( elem, dimension );\n\t\t\t}\n\n\t\t\treturn setPositiveNumber( elem, value, subtract );\n\t\t}\n\t};\n} );\n\njQuery.cssHooks.marginLeft = addGetHookIf( support.reliableMarginLeft,\n\tfunction( elem, computed ) {\n\t\tif ( computed ) {\n\t\t\treturn ( parseFloat( curCSS( elem, \"marginLeft\" ) ) ||\n\t\t\t\telem.getBoundingClientRect().left -\n\t\t\t\t\tswap( elem, { marginLeft: 0 }, function() {\n\t\t\t\t\t\treturn elem.getBoundingClientRect().left;\n\t\t\t\t\t} )\n\t\t\t\t) + \"px\";\n\t\t}\n\t}\n);\n\n// These hooks are used by animate to expand properties\njQuery.each( {\n\tmargin: \"\",\n\tpadding: \"\",\n\tborder: \"Width\"\n}, function( prefix, suffix ) {\n\tjQuery.cssHooks[ prefix + suffix ] = {\n\t\texpand: function( value ) {\n\t\t\tvar i = 0,\n\t\t\t\texpanded = {},\n\n\t\t\t\t// Assumes a single number if not a string\n\t\t\t\tparts = typeof value === \"string\" ? value.split( \" \" ) : [ value ];\n\n\t\t\tfor ( ; i < 4; i++ ) {\n\t\t\t\texpanded[ prefix + cssExpand[ i ] + suffix ] =\n\t\t\t\t\tparts[ i ] || parts[ i - 2 ] || parts[ 0 ];\n\t\t\t}\n\n\t\t\treturn expanded;\n\t\t}\n\t};\n\n\tif ( prefix !== \"margin\" ) {\n\t\tjQuery.cssHooks[ prefix + suffix ].set = setPositiveNumber;\n\t}\n} );\n\njQuery.fn.extend( {\n\tcss: function( name, value ) {\n\t\treturn access( this, function( elem, name, value ) {\n\t\t\tvar styles, len,\n\t\t\t\tmap = {},\n\t\t\t\ti = 0;\n\n\t\t\tif ( Array.isArray( name ) ) {\n\t\t\t\tstyles = getStyles( elem );\n\t\t\t\tlen = name.length;\n\n\t\t\t\tfor ( ; i < len; i++ ) {\n\t\t\t\t\tmap[ name[ i ] ] = jQuery.css( elem, name[ i ], false, styles );\n\t\t\t\t}\n\n\t\t\t\treturn map;\n\t\t\t}\n\n\t\t\treturn value !== undefined ?\n\t\t\t\tjQuery.style( elem, name, value ) :\n\t\t\t\tjQuery.css( elem, name );\n\t\t}, name, value, arguments.length > 1 );\n\t}\n} );\n\n\nfunction Tween( elem, options, prop, end, easing ) {\n\treturn new Tween.prototype.init( elem, options, prop, end, easing );\n}\njQuery.Tween = Tween;\n\nTween.prototype = {\n\tconstructor: Tween,\n\tinit: function( elem, options, prop, end, easing, unit ) {\n\t\tthis.elem = elem;\n\t\tthis.prop = prop;\n\t\tthis.easing = easing || jQuery.easing._default;\n\t\tthis.options = options;\n\t\tthis.start = this.now = this.cur();\n\t\tthis.end = end;\n\t\tthis.unit = unit || ( jQuery.cssNumber[ prop ] ? \"\" : \"px\" );\n\t},\n\tcur: function() {\n\t\tvar hooks = Tween.propHooks[ this.prop ];\n\n\t\treturn hooks && hooks.get ?\n\t\t\thooks.get( this ) :\n\t\t\tTween.propHooks._default.get( this );\n\t},\n\trun: function( percent ) {\n\t\tvar eased,\n\t\t\thooks = Tween.propHooks[ this.prop ];\n\n\t\tif ( this.options.duration ) {\n\t\t\tthis.pos = eased = jQuery.easing[ this.easing ](\n\t\t\t\tpercent, this.options.duration * percent, 0, 1, this.options.duration\n\t\t\t);\n\t\t} else {\n\t\t\tthis.pos = eased = percent;\n\t\t}\n\t\tthis.now = ( this.end - this.start ) * eased + this.start;\n\n\t\tif ( this.options.step ) {\n\t\t\tthis.options.step.call( this.elem, this.now, this );\n\t\t}\n\n\t\tif ( hooks && hooks.set ) {\n\t\t\thooks.set( this );\n\t\t} else {\n\t\t\tTween.propHooks._default.set( this );\n\t\t}\n\t\treturn this;\n\t}\n};\n\nTween.prototype.init.prototype = Tween.prototype;\n\nTween.propHooks = {\n\t_default: {\n\t\tget: function( tween ) {\n\t\t\tvar result;\n\n\t\t\t// Use a property on the element directly when it is not a DOM element,\n\t\t\t// or when there is no matching style property that exists.\n\t\t\tif ( tween.elem.nodeType !== 1 ||\n\t\t\t\ttween.elem[ tween.prop ] != null && tween.elem.style[ tween.prop ] == null ) {\n\t\t\t\treturn tween.elem[ tween.prop ];\n\t\t\t}\n\n\t\t\t// Passing an empty string as a 3rd parameter to .css will automatically\n\t\t\t// attempt a parseFloat and fallback to a string if the parse fails.\n\t\t\t// Simple values such as \"10px\" are parsed to Float;\n\t\t\t// complex values such as \"rotate(1rad)\" are returned as-is.\n\t\t\tresult = jQuery.css( tween.elem, tween.prop, \"\" );\n\n\t\t\t// Empty strings, null, undefined and \"auto\" are converted to 0.\n\t\t\treturn !result || result === \"auto\" ? 0 : result;\n\t\t},\n\t\tset: function( tween ) {\n\n\t\t\t// Use step hook for back compat.\n\t\t\t// Use cssHook if its there.\n\t\t\t// Use .style if available and use plain properties where available.\n\t\t\tif ( jQuery.fx.step[ tween.prop ] ) {\n\t\t\t\tjQuery.fx.step[ tween.prop ]( tween );\n\t\t\t} else if ( tween.elem.nodeType === 1 && (\n\t\t\t\t\tjQuery.cssHooks[ tween.prop ] ||\n\t\t\t\t\ttween.elem.style[ finalPropName( tween.prop ) ] != null ) ) {\n\t\t\t\tjQuery.style( tween.elem, tween.prop, tween.now + tween.unit );\n\t\t\t} else {\n\t\t\t\ttween.elem[ tween.prop ] = tween.now;\n\t\t\t}\n\t\t}\n\t}\n};\n\n// Support: IE <=9 only\n// Panic based approach to setting things on disconnected nodes\nTween.propHooks.scrollTop = Tween.propHooks.scrollLeft = {\n\tset: function( tween ) {\n\t\tif ( tween.elem.nodeType && tween.elem.parentNode ) {\n\t\t\ttween.elem[ tween.prop ] = tween.now;\n\t\t}\n\t}\n};\n\njQuery.easing = {\n\tlinear: function( p ) {\n\t\treturn p;\n\t},\n\tswing: function( p ) {\n\t\treturn 0.5 - Math.cos( p * Math.PI ) / 2;\n\t},\n\t_default: \"swing\"\n};\n\njQuery.fx = Tween.prototype.init;\n\n// Back compat <1.8 extension point\njQuery.fx.step = {};\n\n\n\n\nvar\n\tfxNow, inProgress,\n\trfxtypes = /^(?:toggle|show|hide)$/,\n\trrun = /queueHooks$/;\n\nfunction schedule() {\n\tif ( inProgress ) {\n\t\tif ( document.hidden === false && window.requestAnimationFrame ) {\n\t\t\twindow.requestAnimationFrame( schedule );\n\t\t} else {\n\t\t\twindow.setTimeout( schedule, jQuery.fx.interval );\n\t\t}\n\n\t\tjQuery.fx.tick();\n\t}\n}\n\n// Animations created synchronously will run synchronously\nfunction createFxNow() {\n\twindow.setTimeout( function() {\n\t\tfxNow = undefined;\n\t} );\n\treturn ( fxNow = Date.now() );\n}\n\n// Generate parameters to create a standard animation\nfunction genFx( type, includeWidth ) {\n\tvar which,\n\t\ti = 0,\n\t\tattrs = { height: type };\n\n\t// If we include width, step value is 1 to do all cssExpand values,\n\t// otherwise step value is 2 to skip over Left and Right\n\tincludeWidth = includeWidth ? 1 : 0;\n\tfor ( ; i < 4; i += 2 - includeWidth ) {\n\t\twhich = cssExpand[ i ];\n\t\tattrs[ \"margin\" + which ] = attrs[ \"padding\" + which ] = type;\n\t}\n\n\tif ( includeWidth ) {\n\t\tattrs.opacity = attrs.width = type;\n\t}\n\n\treturn attrs;\n}\n\nfunction createTween( value, prop, animation ) {\n\tvar tween,\n\t\tcollection = ( Animation.tweeners[ prop ] || [] ).concat( Animation.tweeners[ \"*\" ] ),\n\t\tindex = 0,\n\t\tlength = collection.length;\n\tfor ( ; index < length; index++ ) {\n\t\tif ( ( tween = collection[ index ].call( animation, prop, value ) ) ) {\n\n\t\t\t// We're done with this property\n\t\t\treturn tween;\n\t\t}\n\t}\n}\n\nfunction defaultPrefilter( elem, props, opts ) {\n\tvar prop, value, toggle, hooks, oldfire, propTween, restoreDisplay, display,\n\t\tisBox = \"width\" in props || \"height\" in props,\n\t\tanim = this,\n\t\torig = {},\n\t\tstyle = elem.style,\n\t\thidden = elem.nodeType && isHiddenWithinTree( elem ),\n\t\tdataShow = dataPriv.get( elem, \"fxshow\" );\n\n\t// Queue-skipping animations hijack the fx hooks\n\tif ( !opts.queue ) {\n\t\thooks = jQuery._queueHooks( elem, \"fx\" );\n\t\tif ( hooks.unqueued == null ) {\n\t\t\thooks.unqueued = 0;\n\t\t\toldfire = hooks.empty.fire;\n\t\t\thooks.empty.fire = function() {\n\t\t\t\tif ( !hooks.unqueued ) {\n\t\t\t\t\toldfire();\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\t\thooks.unqueued++;\n\n\t\tanim.always( function() {\n\n\t\t\t// Ensure the complete handler is called before this completes\n\t\t\tanim.always( function() {\n\t\t\t\thooks.unqueued--;\n\t\t\t\tif ( !jQuery.queue( elem, \"fx\" ).length ) {\n\t\t\t\t\thooks.empty.fire();\n\t\t\t\t}\n\t\t\t} );\n\t\t} );\n\t}\n\n\t// Detect show/hide animations\n\tfor ( prop in props ) {\n\t\tvalue = props[ prop ];\n\t\tif ( rfxtypes.test( value ) ) {\n\t\t\tdelete props[ prop ];\n\t\t\ttoggle = toggle || value === \"toggle\";\n\t\t\tif ( value === ( hidden ? \"hide\" : \"show\" ) ) {\n\n\t\t\t\t// Pretend to be hidden if this is a \"show\" and\n\t\t\t\t// there is still data from a stopped show/hide\n\t\t\t\tif ( value === \"show\" && dataShow && dataShow[ prop ] !== undefined ) {\n\t\t\t\t\thidden = true;\n\n\t\t\t\t// Ignore all other no-op show/hide data\n\t\t\t\t} else {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t}\n\t\t\torig[ prop ] = dataShow && dataShow[ prop ] || jQuery.style( elem, prop );\n\t\t}\n\t}\n\n\t// Bail out if this is a no-op like .hide().hide()\n\tpropTween = !jQuery.isEmptyObject( props );\n\tif ( !propTween && jQuery.isEmptyObject( orig ) ) {\n\t\treturn;\n\t}\n\n\t// Restrict \"overflow\" and \"display\" styles during box animations\n\tif ( isBox && elem.nodeType === 1 ) {\n\n\t\t// Support: IE <=9 - 11, Edge 12 - 15\n\t\t// Record all 3 overflow attributes because IE does not infer the shorthand\n\t\t// from identically-valued overflowX and overflowY and Edge just mirrors\n\t\t// the overflowX value there.\n\t\topts.overflow = [ style.overflow, style.overflowX, style.overflowY ];\n\n\t\t// Identify a display type, preferring old show/hide data over the CSS cascade\n\t\trestoreDisplay = dataShow && dataShow.display;\n\t\tif ( restoreDisplay == null ) {\n\t\t\trestoreDisplay = dataPriv.get( elem, \"display\" );\n\t\t}\n\t\tdisplay = jQuery.css( elem, \"display\" );\n\t\tif ( display === \"none\" ) {\n\t\t\tif ( restoreDisplay ) {\n\t\t\t\tdisplay = restoreDisplay;\n\t\t\t} else {\n\n\t\t\t\t// Get nonempty value(s) by temporarily forcing visibility\n\t\t\t\tshowHide( [ elem ], true );\n\t\t\t\trestoreDisplay = elem.style.display || restoreDisplay;\n\t\t\t\tdisplay = jQuery.css( elem, \"display\" );\n\t\t\t\tshowHide( [ elem ] );\n\t\t\t}\n\t\t}\n\n\t\t// Animate inline elements as inline-block\n\t\tif ( display === \"inline\" || display === \"inline-block\" && restoreDisplay != null ) {\n\t\t\tif ( jQuery.css( elem, \"float\" ) === \"none\" ) {\n\n\t\t\t\t// Restore the original display value at the end of pure show/hide animations\n\t\t\t\tif ( !propTween ) {\n\t\t\t\t\tanim.done( function() {\n\t\t\t\t\t\tstyle.display = restoreDisplay;\n\t\t\t\t\t} );\n\t\t\t\t\tif ( restoreDisplay == null ) {\n\t\t\t\t\t\tdisplay = style.display;\n\t\t\t\t\t\trestoreDisplay = display === \"none\" ? \"\" : display;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tstyle.display = \"inline-block\";\n\t\t\t}\n\t\t}\n\t}\n\n\tif ( opts.overflow ) {\n\t\tstyle.overflow = \"hidden\";\n\t\tanim.always( function() {\n\t\t\tstyle.overflow = opts.overflow[ 0 ];\n\t\t\tstyle.overflowX = opts.overflow[ 1 ];\n\t\t\tstyle.overflowY = opts.overflow[ 2 ];\n\t\t} );\n\t}\n\n\t// Implement show/hide animations\n\tpropTween = false;\n\tfor ( prop in orig ) {\n\n\t\t// General show/hide setup for this element animation\n\t\tif ( !propTween ) {\n\t\t\tif ( dataShow ) {\n\t\t\t\tif ( \"hidden\" in dataShow ) {\n\t\t\t\t\thidden = dataShow.hidden;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tdataShow = dataPriv.access( elem, \"fxshow\", { display: restoreDisplay } );\n\t\t\t}\n\n\t\t\t// Store hidden/visible for toggle so `.stop().toggle()` \"reverses\"\n\t\t\tif ( toggle ) {\n\t\t\t\tdataShow.hidden = !hidden;\n\t\t\t}\n\n\t\t\t// Show elements before animating them\n\t\t\tif ( hidden ) {\n\t\t\t\tshowHide( [ elem ], true );\n\t\t\t}\n\n\t\t\t/* eslint-disable no-loop-func */\n\n\t\t\tanim.done( function() {\n\n\t\t\t/* eslint-enable no-loop-func */\n\n\t\t\t\t// The final step of a \"hide\" animation is actually hiding the element\n\t\t\t\tif ( !hidden ) {\n\t\t\t\t\tshowHide( [ elem ] );\n\t\t\t\t}\n\t\t\t\tdataPriv.remove( elem, \"fxshow\" );\n\t\t\t\tfor ( prop in orig ) {\n\t\t\t\t\tjQuery.style( elem, prop, orig[ prop ] );\n\t\t\t\t}\n\t\t\t} );\n\t\t}\n\n\t\t// Per-property setup\n\t\tpropTween = createTween( hidden ? dataShow[ prop ] : 0, prop, anim );\n\t\tif ( !( prop in dataShow ) ) {\n\t\t\tdataShow[ prop ] = propTween.start;\n\t\t\tif ( hidden ) {\n\t\t\t\tpropTween.end = propTween.start;\n\t\t\t\tpropTween.start = 0;\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction propFilter( props, specialEasing ) {\n\tvar index, name, easing, value, hooks;\n\n\t// camelCase, specialEasing and expand cssHook pass\n\tfor ( index in props ) {\n\t\tname = camelCase( index );\n\t\teasing = specialEasing[ name ];\n\t\tvalue = props[ index ];\n\t\tif ( Array.isArray( value ) ) {\n\t\t\teasing = value[ 1 ];\n\t\t\tvalue = props[ index ] = value[ 0 ];\n\t\t}\n\n\t\tif ( index !== name ) {\n\t\t\tprops[ name ] = value;\n\t\t\tdelete props[ index ];\n\t\t}\n\n\t\thooks = jQuery.cssHooks[ name ];\n\t\tif ( hooks && \"expand\" in hooks ) {\n\t\t\tvalue = hooks.expand( value );\n\t\t\tdelete props[ name ];\n\n\t\t\t// Not quite $.extend, this won't overwrite existing keys.\n\t\t\t// Reusing 'index' because we have the correct \"name\"\n\t\t\tfor ( index in value ) {\n\t\t\t\tif ( !( index in props ) ) {\n\t\t\t\t\tprops[ index ] = value[ index ];\n\t\t\t\t\tspecialEasing[ index ] = easing;\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tspecialEasing[ name ] = easing;\n\t\t}\n\t}\n}\n\nfunction Animation( elem, properties, options ) {\n\tvar result,\n\t\tstopped,\n\t\tindex = 0,\n\t\tlength = Animation.prefilters.length,\n\t\tdeferred = jQuery.Deferred().always( function() {\n\n\t\t\t// Don't match elem in the :animated selector\n\t\t\tdelete tick.elem;\n\t\t} ),\n\t\ttick = function() {\n\t\t\tif ( stopped ) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tvar currentTime = fxNow || createFxNow(),\n\t\t\t\tremaining = Math.max( 0, animation.startTime + animation.duration - currentTime ),\n\n\t\t\t\t// Support: Android 2.3 only\n\t\t\t\t// Archaic crash bug won't allow us to use `1 - ( 0.5 || 0 )` (#12497)\n\t\t\t\ttemp = remaining / animation.duration || 0,\n\t\t\t\tpercent = 1 - temp,\n\t\t\t\tindex = 0,\n\t\t\t\tlength = animation.tweens.length;\n\n\t\t\tfor ( ; index < length; index++ ) {\n\t\t\t\tanimation.tweens[ index ].run( percent );\n\t\t\t}\n\n\t\t\tdeferred.notifyWith( elem, [ animation, percent, remaining ] );\n\n\t\t\t// If there's more to do, yield\n\t\t\tif ( percent < 1 && length ) {\n\t\t\t\treturn remaining;\n\t\t\t}\n\n\t\t\t// If this was an empty animation, synthesize a final progress notification\n\t\t\tif ( !length ) {\n\t\t\t\tdeferred.notifyWith( elem, [ animation, 1, 0 ] );\n\t\t\t}\n\n\t\t\t// Resolve the animation and report its conclusion\n\t\t\tdeferred.resolveWith( elem, [ animation ] );\n\t\t\treturn false;\n\t\t},\n\t\tanimation = deferred.promise( {\n\t\t\telem: elem,\n\t\t\tprops: jQuery.extend( {}, properties ),\n\t\t\topts: jQuery.extend( true, {\n\t\t\t\tspecialEasing: {},\n\t\t\t\teasing: jQuery.easing._default\n\t\t\t}, options ),\n\t\t\toriginalProperties: properties,\n\t\t\toriginalOptions: options,\n\t\t\tstartTime: fxNow || createFxNow(),\n\t\t\tduration: options.duration,\n\t\t\ttweens: [],\n\t\t\tcreateTween: function( prop, end ) {\n\t\t\t\tvar tween = jQuery.Tween( elem, animation.opts, prop, end,\n\t\t\t\t\t\tanimation.opts.specialEasing[ prop ] || animation.opts.easing );\n\t\t\t\tanimation.tweens.push( tween );\n\t\t\t\treturn tween;\n\t\t\t},\n\t\t\tstop: function( gotoEnd ) {\n\t\t\t\tvar index = 0,\n\n\t\t\t\t\t// If we are going to the end, we want to run all the tweens\n\t\t\t\t\t// otherwise we skip this part\n\t\t\t\t\tlength = gotoEnd ? animation.tweens.length : 0;\n\t\t\t\tif ( stopped ) {\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t\tstopped = true;\n\t\t\t\tfor ( ; index < length; index++ ) {\n\t\t\t\t\tanimation.tweens[ index ].run( 1 );\n\t\t\t\t}\n\n\t\t\t\t// Resolve when we played the last frame; otherwise, reject\n\t\t\t\tif ( gotoEnd ) {\n\t\t\t\t\tdeferred.notifyWith( elem, [ animation, 1, 0 ] );\n\t\t\t\t\tdeferred.resolveWith( elem, [ animation, gotoEnd ] );\n\t\t\t\t} else {\n\t\t\t\t\tdeferred.rejectWith( elem, [ animation, gotoEnd ] );\n\t\t\t\t}\n\t\t\t\treturn this;\n\t\t\t}\n\t\t} ),\n\t\tprops = animation.props;\n\n\tpropFilter( props, animation.opts.specialEasing );\n\n\tfor ( ; index < length; index++ ) {\n\t\tresult = Animation.prefilters[ index ].call( animation, elem, props, animation.opts );\n\t\tif ( result ) {\n\t\t\tif ( isFunction( result.stop ) ) {\n\t\t\t\tjQuery._queueHooks( animation.elem, animation.opts.queue ).stop =\n\t\t\t\t\tresult.stop.bind( result );\n\t\t\t}\n\t\t\treturn result;\n\t\t}\n\t}\n\n\tjQuery.map( props, createTween, animation );\n\n\tif ( isFunction( animation.opts.start ) ) {\n\t\tanimation.opts.start.call( elem, animation );\n\t}\n\n\t// Attach callbacks from options\n\tanimation\n\t\t.progress( animation.opts.progress )\n\t\t.done( animation.opts.done, animation.opts.complete )\n\t\t.fail( animation.opts.fail )\n\t\t.always( animation.opts.always );\n\n\tjQuery.fx.timer(\n\t\tjQuery.extend( tick, {\n\t\t\telem: elem,\n\t\t\tanim: animation,\n\t\t\tqueue: animation.opts.queue\n\t\t} )\n\t);\n\n\treturn animation;\n}\n\njQuery.Animation = jQuery.extend( Animation, {\n\n\ttweeners: {\n\t\t\"*\": [ function( prop, value ) {\n\t\t\tvar tween = this.createTween( prop, value );\n\t\t\tadjustCSS( tween.elem, prop, rcssNum.exec( value ), tween );\n\t\t\treturn tween;\n\t\t} ]\n\t},\n\n\ttweener: function( props, callback ) {\n\t\tif ( isFunction( props ) ) {\n\t\t\tcallback = props;\n\t\t\tprops = [ \"*\" ];\n\t\t} else {\n\t\t\tprops = props.match( rnothtmlwhite );\n\t\t}\n\n\t\tvar prop,\n\t\t\tindex = 0,\n\t\t\tlength = props.length;\n\n\t\tfor ( ; index < length; index++ ) {\n\t\t\tprop = props[ index ];\n\t\t\tAnimation.tweeners[ prop ] = Animation.tweeners[ prop ] || [];\n\t\t\tAnimation.tweeners[ prop ].unshift( callback );\n\t\t}\n\t},\n\n\tprefilters: [ defaultPrefilter ],\n\n\tprefilter: function( callback, prepend ) {\n\t\tif ( prepend ) {\n\t\t\tAnimation.prefilters.unshift( callback );\n\t\t} else {\n\t\t\tAnimation.prefilters.push( callback );\n\t\t}\n\t}\n} );\n\njQuery.speed = function( speed, easing, fn ) {\n\tvar opt = speed && typeof speed === \"object\" ? jQuery.extend( {}, speed ) : {\n\t\tcomplete: fn || !fn && easing ||\n\t\t\tisFunction( speed ) && speed,\n\t\tduration: speed,\n\t\teasing: fn && easing || easing && !isFunction( easing ) && easing\n\t};\n\n\t// Go to the end state if fx are off\n\tif ( jQuery.fx.off ) {\n\t\topt.duration = 0;\n\n\t} else {\n\t\tif ( typeof opt.duration !== \"number\" ) {\n\t\t\tif ( opt.duration in jQuery.fx.speeds ) {\n\t\t\t\topt.duration = jQuery.fx.speeds[ opt.duration ];\n\n\t\t\t} else {\n\t\t\t\topt.duration = jQuery.fx.speeds._default;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Normalize opt.queue - true/undefined/null -> \"fx\"\n\tif ( opt.queue == null || opt.queue === true ) {\n\t\topt.queue = \"fx\";\n\t}\n\n\t// Queueing\n\topt.old = opt.complete;\n\n\topt.complete = function() {\n\t\tif ( isFunction( opt.old ) ) {\n\t\t\topt.old.call( this );\n\t\t}\n\n\t\tif ( opt.queue ) {\n\t\t\tjQuery.dequeue( this, opt.queue );\n\t\t}\n\t};\n\n\treturn opt;\n};\n\njQuery.fn.extend( {\n\tfadeTo: function( speed, to, easing, callback ) {\n\n\t\t// Show any hidden elements after setting opacity to 0\n\t\treturn this.filter( isHiddenWithinTree ).css( \"opacity\", 0 ).show()\n\n\t\t\t// Animate to the value specified\n\t\t\t.end().animate( { opacity: to }, speed, easing, callback );\n\t},\n\tanimate: function( prop, speed, easing, callback ) {\n\t\tvar empty = jQuery.isEmptyObject( prop ),\n\t\t\toptall = jQuery.speed( speed, easing, callback ),\n\t\t\tdoAnimation = function() {\n\n\t\t\t\t// Operate on a copy of prop so per-property easing won't be lost\n\t\t\t\tvar anim = Animation( this, jQuery.extend( {}, prop ), optall );\n\n\t\t\t\t// Empty animations, or finishing resolves immediately\n\t\t\t\tif ( empty || dataPriv.get( this, \"finish\" ) ) {\n\t\t\t\t\tanim.stop( true );\n\t\t\t\t}\n\t\t\t};\n\t\t\tdoAnimation.finish = doAnimation;\n\n\t\treturn empty || optall.queue === false ?\n\t\t\tthis.each( doAnimation ) :\n\t\t\tthis.queue( optall.queue, doAnimation );\n\t},\n\tstop: function( type, clearQueue, gotoEnd ) {\n\t\tvar stopQueue = function( hooks ) {\n\t\t\tvar stop = hooks.stop;\n\t\t\tdelete hooks.stop;\n\t\t\tstop( gotoEnd );\n\t\t};\n\n\t\tif ( typeof type !== \"string\" ) {\n\t\t\tgotoEnd = clearQueue;\n\t\t\tclearQueue = type;\n\t\t\ttype = undefined;\n\t\t}\n\t\tif ( clearQueue && type !== false ) {\n\t\t\tthis.queue( type || \"fx\", [] );\n\t\t}\n\n\t\treturn this.each( function() {\n\t\t\tvar dequeue = true,\n\t\t\t\tindex = type != null && type + \"queueHooks\",\n\t\t\t\ttimers = jQuery.timers,\n\t\t\t\tdata = dataPriv.get( this );\n\n\t\t\tif ( index ) {\n\t\t\t\tif ( data[ index ] && data[ index ].stop ) {\n\t\t\t\t\tstopQueue( data[ index ] );\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tfor ( index in data ) {\n\t\t\t\t\tif ( data[ index ] && data[ index ].stop && rrun.test( index ) ) {\n\t\t\t\t\t\tstopQueue( data[ index ] );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tfor ( index = timers.length; index--; ) {\n\t\t\t\tif ( timers[ index ].elem === this &&\n\t\t\t\t\t( type == null || timers[ index ].queue === type ) ) {\n\n\t\t\t\t\ttimers[ index ].anim.stop( gotoEnd );\n\t\t\t\t\tdequeue = false;\n\t\t\t\t\ttimers.splice( index, 1 );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Start the next in the queue if the last step wasn't forced.\n\t\t\t// Timers currently will call their complete callbacks, which\n\t\t\t// will dequeue but only if they were gotoEnd.\n\t\t\tif ( dequeue || !gotoEnd ) {\n\t\t\t\tjQuery.dequeue( this, type );\n\t\t\t}\n\t\t} );\n\t},\n\tfinish: function( type ) {\n\t\tif ( type !== false ) {\n\t\t\ttype = type || \"fx\";\n\t\t}\n\t\treturn this.each( function() {\n\t\t\tvar index,\n\t\t\t\tdata = dataPriv.get( this ),\n\t\t\t\tqueue = data[ type + \"queue\" ],\n\t\t\t\thooks = data[ type + \"queueHooks\" ],\n\t\t\t\ttimers = jQuery.timers,\n\t\t\t\tlength = queue ? queue.length : 0;\n\n\t\t\t// Enable finishing flag on private data\n\t\t\tdata.finish = true;\n\n\t\t\t// Empty the queue first\n\t\t\tjQuery.queue( this, type, [] );\n\n\t\t\tif ( hooks && hooks.stop ) {\n\t\t\t\thooks.stop.call( this, true );\n\t\t\t}\n\n\t\t\t// Look for any active animations, and finish them\n\t\t\tfor ( index = timers.length; index--; ) {\n\t\t\t\tif ( timers[ index ].elem === this && timers[ index ].queue === type ) {\n\t\t\t\t\ttimers[ index ].anim.stop( true );\n\t\t\t\t\ttimers.splice( index, 1 );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Look for any animations in the old queue and finish them\n\t\t\tfor ( index = 0; index < length; index++ ) {\n\t\t\t\tif ( queue[ index ] && queue[ index ].finish ) {\n\t\t\t\t\tqueue[ index ].finish.call( this );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Turn off finishing flag\n\t\t\tdelete data.finish;\n\t\t} );\n\t}\n} );\n\njQuery.each( [ \"toggle\", \"show\", \"hide\" ], function( i, name ) {\n\tvar cssFn = jQuery.fn[ name ];\n\tjQuery.fn[ name ] = function( speed, easing, callback ) {\n\t\treturn speed == null || typeof speed === \"boolean\" ?\n\t\t\tcssFn.apply( this, arguments ) :\n\t\t\tthis.animate( genFx( name, true ), speed, easing, callback );\n\t};\n} );\n\n// Generate shortcuts for custom animations\njQuery.each( {\n\tslideDown: genFx( \"show\" ),\n\tslideUp: genFx( \"hide\" ),\n\tslideToggle: genFx( \"toggle\" ),\n\tfadeIn: { opacity: \"show\" },\n\tfadeOut: { opacity: \"hide\" },\n\tfadeToggle: { opacity: \"toggle\" }\n}, function( name, props ) {\n\tjQuery.fn[ name ] = function( speed, easing, callback ) {\n\t\treturn this.animate( props, speed, easing, callback );\n\t};\n} );\n\njQuery.timers = [];\njQuery.fx.tick = function() {\n\tvar timer,\n\t\ti = 0,\n\t\ttimers = jQuery.timers;\n\n\tfxNow = Date.now();\n\n\tfor ( ; i < timers.length; i++ ) {\n\t\ttimer = timers[ i ];\n\n\t\t// Run the timer and safely remove it when done (allowing for external removal)\n\t\tif ( !timer() && timers[ i ] === timer ) {\n\t\t\ttimers.splice( i--, 1 );\n\t\t}\n\t}\n\n\tif ( !timers.length ) {\n\t\tjQuery.fx.stop();\n\t}\n\tfxNow = undefined;\n};\n\njQuery.fx.timer = function( timer ) {\n\tjQuery.timers.push( timer );\n\tjQuery.fx.start();\n};\n\njQuery.fx.interval = 13;\njQuery.fx.start = function() {\n\tif ( inProgress ) {\n\t\treturn;\n\t}\n\n\tinProgress = true;\n\tschedule();\n};\n\njQuery.fx.stop = function() {\n\tinProgress = null;\n};\n\njQuery.fx.speeds = {\n\tslow: 600,\n\tfast: 200,\n\n\t// Default speed\n\t_default: 400\n};\n\n\n// Based off of the plugin by Clint Helfers, with permission.\n// https://web.archive.org/web/20100324014747/http://blindsignals.com/index.php/2009/07/jquery-delay/\njQuery.fn.delay = function( time, type ) {\n\ttime = jQuery.fx ? jQuery.fx.speeds[ time ] || time : time;\n\ttype = type || \"fx\";\n\n\treturn this.queue( type, function( next, hooks ) {\n\t\tvar timeout = window.setTimeout( next, time );\n\t\thooks.stop = function() {\n\t\t\twindow.clearTimeout( timeout );\n\t\t};\n\t} );\n};\n\n\n( function() {\n\tvar input = document.createElement( \"input\" ),\n\t\tselect = document.createElement( \"select\" ),\n\t\topt = select.appendChild( document.createElement( \"option\" ) );\n\n\tinput.type = \"checkbox\";\n\n\t// Support: Android <=4.3 only\n\t// Default value for a checkbox should be \"on\"\n\tsupport.checkOn = input.value !== \"\";\n\n\t// Support: IE <=11 only\n\t// Must access selectedIndex to make default options select\n\tsupport.optSelected = opt.selected;\n\n\t// Support: IE <=11 only\n\t// An input loses its value after becoming a radio\n\tinput = document.createElement( \"input\" );\n\tinput.value = \"t\";\n\tinput.type = \"radio\";\n\tsupport.radioValue = input.value === \"t\";\n} )();\n\n\nvar boolHook,\n\tattrHandle = jQuery.expr.attrHandle;\n\njQuery.fn.extend( {\n\tattr: function( name, value ) {\n\t\treturn access( this, jQuery.attr, name, value, arguments.length > 1 );\n\t},\n\n\tremoveAttr: function( name ) {\n\t\treturn this.each( function() {\n\t\t\tjQuery.removeAttr( this, name );\n\t\t} );\n\t}\n} );\n\njQuery.extend( {\n\tattr: function( elem, name, value ) {\n\t\tvar ret, hooks,\n\t\t\tnType = elem.nodeType;\n\n\t\t// Don't get/set attributes on text, comment and attribute nodes\n\t\tif ( nType === 3 || nType === 8 || nType === 2 ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Fallback to prop when attributes are not supported\n\t\tif ( typeof elem.getAttribute === \"undefined\" ) {\n\t\t\treturn jQuery.prop( elem, name, value );\n\t\t}\n\n\t\t// Attribute hooks are determined by the lowercase version\n\t\t// Grab necessary hook if one is defined\n\t\tif ( nType !== 1 || !jQuery.isXMLDoc( elem ) ) {\n\t\t\thooks = jQuery.attrHooks[ name.toLowerCase() ] ||\n\t\t\t\t( jQuery.expr.match.bool.test( name ) ? boolHook : undefined );\n\t\t}\n\n\t\tif ( value !== undefined ) {\n\t\t\tif ( value === null ) {\n\t\t\t\tjQuery.removeAttr( elem, name );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ( hooks && \"set\" in hooks &&\n\t\t\t\t( ret = hooks.set( elem, value, name ) ) !== undefined ) {\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\telem.setAttribute( name, value + \"\" );\n\t\t\treturn value;\n\t\t}\n\n\t\tif ( hooks && \"get\" in hooks && ( ret = hooks.get( elem, name ) ) !== null ) {\n\t\t\treturn ret;\n\t\t}\n\n\t\tret = jQuery.find.attr( elem, name );\n\n\t\t// Non-existent attributes return null, we normalize to undefined\n\t\treturn ret == null ? undefined : ret;\n\t},\n\n\tattrHooks: {\n\t\ttype: {\n\t\t\tset: function( elem, value ) {\n\t\t\t\tif ( !support.radioValue && value === \"radio\" &&\n\t\t\t\t\tnodeName( elem, \"input\" ) ) {\n\t\t\t\t\tvar val = elem.value;\n\t\t\t\t\telem.setAttribute( \"type\", value );\n\t\t\t\t\tif ( val ) {\n\t\t\t\t\t\telem.value = val;\n\t\t\t\t\t}\n\t\t\t\t\treturn value;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\n\tremoveAttr: function( elem, value ) {\n\t\tvar name,\n\t\t\ti = 0,\n\n\t\t\t// Attribute names can contain non-HTML whitespace characters\n\t\t\t// https://html.spec.whatwg.org/multipage/syntax.html#attributes-2\n\t\t\tattrNames = value && value.match( rnothtmlwhite );\n\n\t\tif ( attrNames && elem.nodeType === 1 ) {\n\t\t\twhile ( ( name = attrNames[ i++ ] ) ) {\n\t\t\t\telem.removeAttribute( name );\n\t\t\t}\n\t\t}\n\t}\n} );\n\n// Hooks for boolean attributes\nboolHook = {\n\tset: function( elem, value, name ) {\n\t\tif ( value === false ) {\n\n\t\t\t// Remove boolean attributes when set to false\n\t\t\tjQuery.removeAttr( elem, name );\n\t\t} else {\n\t\t\telem.setAttribute( name, name );\n\t\t}\n\t\treturn name;\n\t}\n};\n\njQuery.each( jQuery.expr.match.bool.source.match( /\\w+/g ), function( i, name ) {\n\tvar getter = attrHandle[ name ] || jQuery.find.attr;\n\n\tattrHandle[ name ] = function( elem, name, isXML ) {\n\t\tvar ret, handle,\n\t\t\tlowercaseName = name.toLowerCase();\n\n\t\tif ( !isXML ) {\n\n\t\t\t// Avoid an infinite loop by temporarily removing this function from the getter\n\t\t\thandle = attrHandle[ lowercaseName ];\n\t\t\tattrHandle[ lowercaseName ] = ret;\n\t\t\tret = getter( elem, name, isXML ) != null ?\n\t\t\t\tlowercaseName :\n\t\t\t\tnull;\n\t\t\tattrHandle[ lowercaseName ] = handle;\n\t\t}\n\t\treturn ret;\n\t};\n} );\n\n\n\n\nvar rfocusable = /^(?:input|select|textarea|button)$/i,\n\trclickable = /^(?:a|area)$/i;\n\njQuery.fn.extend( {\n\tprop: function( name, value ) {\n\t\treturn access( this, jQuery.prop, name, value, arguments.length > 1 );\n\t},\n\n\tremoveProp: function( name ) {\n\t\treturn this.each( function() {\n\t\t\tdelete this[ jQuery.propFix[ name ] || name ];\n\t\t} );\n\t}\n} );\n\njQuery.extend( {\n\tprop: function( elem, name, value ) {\n\t\tvar ret, hooks,\n\t\t\tnType = elem.nodeType;\n\n\t\t// Don't get/set properties on text, comment and attribute nodes\n\t\tif ( nType === 3 || nType === 8 || nType === 2 ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( nType !== 1 || !jQuery.isXMLDoc( elem ) ) {\n\n\t\t\t// Fix name and attach hooks\n\t\t\tname = jQuery.propFix[ name ] || name;\n\t\t\thooks = jQuery.propHooks[ name ];\n\t\t}\n\n\t\tif ( value !== undefined ) {\n\t\t\tif ( hooks && \"set\" in hooks &&\n\t\t\t\t( ret = hooks.set( elem, value, name ) ) !== undefined ) {\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\treturn ( elem[ name ] = value );\n\t\t}\n\n\t\tif ( hooks && \"get\" in hooks && ( ret = hooks.get( elem, name ) ) !== null ) {\n\t\t\treturn ret;\n\t\t}\n\n\t\treturn elem[ name ];\n\t},\n\n\tpropHooks: {\n\t\ttabIndex: {\n\t\t\tget: function( elem ) {\n\n\t\t\t\t// Support: IE <=9 - 11 only\n\t\t\t\t// elem.tabIndex doesn't always return the\n\t\t\t\t// correct value when it hasn't been explicitly set\n\t\t\t\t// https://web.archive.org/web/20141116233347/http://fluidproject.org/blog/2008/01/09/getting-setting-and-removing-tabindex-values-with-javascript/\n\t\t\t\t// Use proper attribute retrieval(#12072)\n\t\t\t\tvar tabindex = jQuery.find.attr( elem, \"tabindex\" );\n\n\t\t\t\tif ( tabindex ) {\n\t\t\t\t\treturn parseInt( tabindex, 10 );\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\trfocusable.test( elem.nodeName ) ||\n\t\t\t\t\trclickable.test( elem.nodeName ) &&\n\t\t\t\t\telem.href\n\t\t\t\t) {\n\t\t\t\t\treturn 0;\n\t\t\t\t}\n\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t}\n\t},\n\n\tpropFix: {\n\t\t\"for\": \"htmlFor\",\n\t\t\"class\": \"className\"\n\t}\n} );\n\n// Support: IE <=11 only\n// Accessing the selectedIndex property\n// forces the browser to respect setting selected\n// on the option\n// The getter ensures a default option is selected\n// when in an optgroup\n// eslint rule \"no-unused-expressions\" is disabled for this code\n// since it considers such accessions noop\nif ( !support.optSelected ) {\n\tjQuery.propHooks.selected = {\n\t\tget: function( elem ) {\n\n\t\t\t/* eslint no-unused-expressions: \"off\" */\n\n\t\t\tvar parent = elem.parentNode;\n\t\t\tif ( parent && parent.parentNode ) {\n\t\t\t\tparent.parentNode.selectedIndex;\n\t\t\t}\n\t\t\treturn null;\n\t\t},\n\t\tset: function( elem ) {\n\n\t\t\t/* eslint no-unused-expressions: \"off\" */\n\n\t\t\tvar parent = elem.parentNode;\n\t\t\tif ( parent ) {\n\t\t\t\tparent.selectedIndex;\n\n\t\t\t\tif ( parent.parentNode ) {\n\t\t\t\t\tparent.parentNode.selectedIndex;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n}\n\njQuery.each( [\n\t\"tabIndex\",\n\t\"readOnly\",\n\t\"maxLength\",\n\t\"cellSpacing\",\n\t\"cellPadding\",\n\t\"rowSpan\",\n\t\"colSpan\",\n\t\"useMap\",\n\t\"frameBorder\",\n\t\"contentEditable\"\n], function() {\n\tjQuery.propFix[ this.toLowerCase() ] = this;\n} );\n\n\n\n\n\t// Strip and collapse whitespace according to HTML spec\n\t// https://infra.spec.whatwg.org/#strip-and-collapse-ascii-whitespace\n\tfunction stripAndCollapse( value ) {\n\t\tvar tokens = value.match( rnothtmlwhite ) || [];\n\t\treturn tokens.join( \" \" );\n\t}\n\n\nfunction getClass( elem ) {\n\treturn elem.getAttribute && elem.getAttribute( \"class\" ) || \"\";\n}\n\nfunction classesToArray( value ) {\n\tif ( Array.isArray( value ) ) {\n\t\treturn value;\n\t}\n\tif ( typeof value === \"string\" ) {\n\t\treturn value.match( rnothtmlwhite ) || [];\n\t}\n\treturn [];\n}\n\njQuery.fn.extend( {\n\taddClass: function( value ) {\n\t\tvar classes, elem, cur, curValue, clazz, j, finalValue,\n\t\t\ti = 0;\n\n\t\tif ( isFunction( value ) ) {\n\t\t\treturn this.each( function( j ) {\n\t\t\t\tjQuery( this ).addClass( value.call( this, j, getClass( this ) ) );\n\t\t\t} );\n\t\t}\n\n\t\tclasses = classesToArray( value );\n\n\t\tif ( classes.length ) {\n\t\t\twhile ( ( elem = this[ i++ ] ) ) {\n\t\t\t\tcurValue = getClass( elem );\n\t\t\t\tcur = elem.nodeType === 1 && ( \" \" + stripAndCollapse( curValue ) + \" \" );\n\n\t\t\t\tif ( cur ) {\n\t\t\t\t\tj = 0;\n\t\t\t\t\twhile ( ( clazz = classes[ j++ ] ) ) {\n\t\t\t\t\t\tif ( cur.indexOf( \" \" + clazz + \" \" ) < 0 ) {\n\t\t\t\t\t\t\tcur += clazz + \" \";\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Only assign if different to avoid unneeded rendering.\n\t\t\t\t\tfinalValue = stripAndCollapse( cur );\n\t\t\t\t\tif ( curValue !== finalValue ) {\n\t\t\t\t\t\telem.setAttribute( \"class\", finalValue );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn this;\n\t},\n\n\tremoveClass: function( value ) {\n\t\tvar classes, elem, cur, curValue, clazz, j, finalValue,\n\t\t\ti = 0;\n\n\t\tif ( isFunction( value ) ) {\n\t\t\treturn this.each( function( j ) {\n\t\t\t\tjQuery( this ).removeClass( value.call( this, j, getClass( this ) ) );\n\t\t\t} );\n\t\t}\n\n\t\tif ( !arguments.length ) {\n\t\t\treturn this.attr( \"class\", \"\" );\n\t\t}\n\n\t\tclasses = classesToArray( value );\n\n\t\tif ( classes.length ) {\n\t\t\twhile ( ( elem = this[ i++ ] ) ) {\n\t\t\t\tcurValue = getClass( elem );\n\n\t\t\t\t// This expression is here for better compressibility (see addClass)\n\t\t\t\tcur = elem.nodeType === 1 && ( \" \" + stripAndCollapse( curValue ) + \" \" );\n\n\t\t\t\tif ( cur ) {\n\t\t\t\t\tj = 0;\n\t\t\t\t\twhile ( ( clazz = classes[ j++ ] ) ) {\n\n\t\t\t\t\t\t// Remove *all* instances\n\t\t\t\t\t\twhile ( cur.indexOf( \" \" + clazz + \" \" ) > -1 ) {\n\t\t\t\t\t\t\tcur = cur.replace( \" \" + clazz + \" \", \" \" );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Only assign if different to avoid unneeded rendering.\n\t\t\t\t\tfinalValue = stripAndCollapse( cur );\n\t\t\t\t\tif ( curValue !== finalValue ) {\n\t\t\t\t\t\telem.setAttribute( \"class\", finalValue );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn this;\n\t},\n\n\ttoggleClass: function( value, stateVal ) {\n\t\tvar type = typeof value,\n\t\t\tisValidValue = type === \"string\" || Array.isArray( value );\n\n\t\tif ( typeof stateVal === \"boolean\" && isValidValue ) {\n\t\t\treturn stateVal ? this.addClass( value ) : this.removeClass( value );\n\t\t}\n\n\t\tif ( isFunction( value ) ) {\n\t\t\treturn this.each( function( i ) {\n\t\t\t\tjQuery( this ).toggleClass(\n\t\t\t\t\tvalue.call( this, i, getClass( this ), stateVal ),\n\t\t\t\t\tstateVal\n\t\t\t\t);\n\t\t\t} );\n\t\t}\n\n\t\treturn this.each( function() {\n\t\t\tvar className, i, self, classNames;\n\n\t\t\tif ( isValidValue ) {\n\n\t\t\t\t// Toggle individual class names\n\t\t\t\ti = 0;\n\t\t\t\tself = jQuery( this );\n\t\t\t\tclassNames = classesToArray( value );\n\n\t\t\t\twhile ( ( className = classNames[ i++ ] ) ) {\n\n\t\t\t\t\t// Check each className given, space separated list\n\t\t\t\t\tif ( self.hasClass( className ) ) {\n\t\t\t\t\t\tself.removeClass( className );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.addClass( className );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t// Toggle whole class name\n\t\t\t} else if ( value === undefined || type === \"boolean\" ) {\n\t\t\t\tclassName = getClass( this );\n\t\t\t\tif ( className ) {\n\n\t\t\t\t\t// Store className if set\n\t\t\t\t\tdataPriv.set( this, \"__className__\", className );\n\t\t\t\t}\n\n\t\t\t\t// If the element has a class name or if we're passed `false`,\n\t\t\t\t// then remove the whole classname (if there was one, the above saved it).\n\t\t\t\t// Otherwise bring back whatever was previously saved (if anything),\n\t\t\t\t// falling back to the empty string if nothing was stored.\n\t\t\t\tif ( this.setAttribute ) {\n\t\t\t\t\tthis.setAttribute( \"class\",\n\t\t\t\t\t\tclassName || value === false ?\n\t\t\t\t\t\t\"\" :\n\t\t\t\t\t\tdataPriv.get( this, \"__className__\" ) || \"\"\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t} );\n\t},\n\n\thasClass: function( selector ) {\n\t\tvar className, elem,\n\t\t\ti = 0;\n\n\t\tclassName = \" \" + selector + \" \";\n\t\twhile ( ( elem = this[ i++ ] ) ) {\n\t\t\tif ( elem.nodeType === 1 &&\n\t\t\t\t( \" \" + stripAndCollapse( getClass( elem ) ) + \" \" ).indexOf( className ) > -1 ) {\n\t\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n} );\n\n\n\n\nvar rreturn = /\\r/g;\n\njQuery.fn.extend( {\n\tval: function( value ) {\n\t\tvar hooks, ret, valueIsFunction,\n\t\t\telem = this[ 0 ];\n\n\t\tif ( !arguments.length ) {\n\t\t\tif ( elem ) {\n\t\t\t\thooks = jQuery.valHooks[ elem.type ] ||\n\t\t\t\t\tjQuery.valHooks[ elem.nodeName.toLowerCase() ];\n\n\t\t\t\tif ( hooks &&\n\t\t\t\t\t\"get\" in hooks &&\n\t\t\t\t\t( ret = hooks.get( elem, \"value\" ) ) !== undefined\n\t\t\t\t) {\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\n\t\t\t\tret = elem.value;\n\n\t\t\t\t// Handle most common string cases\n\t\t\t\tif ( typeof ret === \"string\" ) {\n\t\t\t\t\treturn ret.replace( rreturn, \"\" );\n\t\t\t\t}\n\n\t\t\t\t// Handle cases where value is null/undef or number\n\t\t\t\treturn ret == null ? \"\" : ret;\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\tvalueIsFunction = isFunction( value );\n\n\t\treturn this.each( function( i ) {\n\t\t\tvar val;\n\n\t\t\tif ( this.nodeType !== 1 ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ( valueIsFunction ) {\n\t\t\t\tval = value.call( this, i, jQuery( this ).val() );\n\t\t\t} else {\n\t\t\t\tval = value;\n\t\t\t}\n\n\t\t\t// Treat null/undefined as \"\"; convert numbers to string\n\t\t\tif ( val == null ) {\n\t\t\t\tval = \"\";\n\n\t\t\t} else if ( typeof val === \"number\" ) {\n\t\t\t\tval += \"\";\n\n\t\t\t} else if ( Array.isArray( val ) ) {\n\t\t\t\tval = jQuery.map( val, function( value ) {\n\t\t\t\t\treturn value == null ? \"\" : value + \"\";\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\thooks = jQuery.valHooks[ this.type ] || jQuery.valHooks[ this.nodeName.toLowerCase() ];\n\n\t\t\t// If set returns undefined, fall back to normal setting\n\t\t\tif ( !hooks || !( \"set\" in hooks ) || hooks.set( this, val, \"value\" ) === undefined ) {\n\t\t\t\tthis.value = val;\n\t\t\t}\n\t\t} );\n\t}\n} );\n\njQuery.extend( {\n\tvalHooks: {\n\t\toption: {\n\t\t\tget: function( elem ) {\n\n\t\t\t\tvar val = jQuery.find.attr( elem, \"value\" );\n\t\t\t\treturn val != null ?\n\t\t\t\t\tval :\n\n\t\t\t\t\t// Support: IE <=10 - 11 only\n\t\t\t\t\t// option.text throws exceptions (#14686, #14858)\n\t\t\t\t\t// Strip and collapse whitespace\n\t\t\t\t\t// https://html.spec.whatwg.org/#strip-and-collapse-whitespace\n\t\t\t\t\tstripAndCollapse( jQuery.text( elem ) );\n\t\t\t}\n\t\t},\n\t\tselect: {\n\t\t\tget: function( elem ) {\n\t\t\t\tvar value, option, i,\n\t\t\t\t\toptions = elem.options,\n\t\t\t\t\tindex = elem.selectedIndex,\n\t\t\t\t\tone = elem.type === \"select-one\",\n\t\t\t\t\tvalues = one ? null : [],\n\t\t\t\t\tmax = one ? index + 1 : options.length;\n\n\t\t\t\tif ( index < 0 ) {\n\t\t\t\t\ti = max;\n\n\t\t\t\t} else {\n\t\t\t\t\ti = one ? index : 0;\n\t\t\t\t}\n\n\t\t\t\t// Loop through all the selected options\n\t\t\t\tfor ( ; i < max; i++ ) {\n\t\t\t\t\toption = options[ i ];\n\n\t\t\t\t\t// Support: IE <=9 only\n\t\t\t\t\t// IE8-9 doesn't update selected after form reset (#2551)\n\t\t\t\t\tif ( ( option.selected || i === index ) &&\n\n\t\t\t\t\t\t\t// Don't return options that are disabled or in a disabled optgroup\n\t\t\t\t\t\t\t!option.disabled &&\n\t\t\t\t\t\t\t( !option.parentNode.disabled ||\n\t\t\t\t\t\t\t\t!nodeName( option.parentNode, \"optgroup\" ) ) ) {\n\n\t\t\t\t\t\t// Get the specific value for the option\n\t\t\t\t\t\tvalue = jQuery( option ).val();\n\n\t\t\t\t\t\t// We don't need an array for one selects\n\t\t\t\t\t\tif ( one ) {\n\t\t\t\t\t\t\treturn value;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Multi-Selects return an array\n\t\t\t\t\t\tvalues.push( value );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn values;\n\t\t\t},\n\n\t\t\tset: function( elem, value ) {\n\t\t\t\tvar optionSet, option,\n\t\t\t\t\toptions = elem.options,\n\t\t\t\t\tvalues = jQuery.makeArray( value ),\n\t\t\t\t\ti = options.length;\n\n\t\t\t\twhile ( i-- ) {\n\t\t\t\t\toption = options[ i ];\n\n\t\t\t\t\t/* eslint-disable no-cond-assign */\n\n\t\t\t\t\tif ( option.selected =\n\t\t\t\t\t\tjQuery.inArray( jQuery.valHooks.option.get( option ), values ) > -1\n\t\t\t\t\t) {\n\t\t\t\t\t\toptionSet = true;\n\t\t\t\t\t}\n\n\t\t\t\t\t/* eslint-enable no-cond-assign */\n\t\t\t\t}\n\n\t\t\t\t// Force browsers to behave consistently when non-matching value is set\n\t\t\t\tif ( !optionSet ) {\n\t\t\t\t\telem.selectedIndex = -1;\n\t\t\t\t}\n\t\t\t\treturn values;\n\t\t\t}\n\t\t}\n\t}\n} );\n\n// Radios and checkboxes getter/setter\njQuery.each( [ \"radio\", \"checkbox\" ], function() {\n\tjQuery.valHooks[ this ] = {\n\t\tset: function( elem, value ) {\n\t\t\tif ( Array.isArray( value ) ) {\n\t\t\t\treturn ( elem.checked = jQuery.inArray( jQuery( elem ).val(), value ) > -1 );\n\t\t\t}\n\t\t}\n\t};\n\tif ( !support.checkOn ) {\n\t\tjQuery.valHooks[ this ].get = function( elem ) {\n\t\t\treturn elem.getAttribute( \"value\" ) === null ? \"on\" : elem.value;\n\t\t};\n\t}\n} );\n\n\n\n\n// Return jQuery for attributes-only inclusion\n\n\nsupport.focusin = \"onfocusin\" in window;\n\n\nvar rfocusMorph = /^(?:focusinfocus|focusoutblur)$/,\n\tstopPropagationCallback = function( e ) {\n\t\te.stopPropagation();\n\t};\n\njQuery.extend( jQuery.event, {\n\n\ttrigger: function( event, data, elem, onlyHandlers ) {\n\n\t\tvar i, cur, tmp, bubbleType, ontype, handle, special, lastElement,\n\t\t\teventPath = [ elem || document ],\n\t\t\ttype = hasOwn.call( event, \"type\" ) ? event.type : event,\n\t\t\tnamespaces = hasOwn.call( event, \"namespace\" ) ? event.namespace.split( \".\" ) : [];\n\n\t\tcur = lastElement = tmp = elem = elem || document;\n\n\t\t// Don't do events on text and comment nodes\n\t\tif ( elem.nodeType === 3 || elem.nodeType === 8 ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// focus/blur morphs to focusin/out; ensure we're not firing them right now\n\t\tif ( rfocusMorph.test( type + jQuery.event.triggered ) ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( type.indexOf( \".\" ) > -1 ) {\n\n\t\t\t// Namespaced trigger; create a regexp to match event type in handle()\n\t\t\tnamespaces = type.split( \".\" );\n\t\t\ttype = namespaces.shift();\n\t\t\tnamespaces.sort();\n\t\t}\n\t\tontype = type.indexOf( \":\" ) < 0 && \"on\" + type;\n\n\t\t// Caller can pass in a jQuery.Event object, Object, or just an event type string\n\t\tevent = event[ jQuery.expando ] ?\n\t\t\tevent :\n\t\t\tnew jQuery.Event( type, typeof event === \"object\" && event );\n\n\t\t// Trigger bitmask: & 1 for native handlers; & 2 for jQuery (always true)\n\t\tevent.isTrigger = onlyHandlers ? 2 : 3;\n\t\tevent.namespace = namespaces.join( \".\" );\n\t\tevent.rnamespace = event.namespace ?\n\t\t\tnew RegExp( \"(^|\\\\.)\" + namespaces.join( \"\\\\.(?:.*\\\\.|)\" ) + \"(\\\\.|$)\" ) :\n\t\t\tnull;\n\n\t\t// Clean up the event in case it is being reused\n\t\tevent.result = undefined;\n\t\tif ( !event.target ) {\n\t\t\tevent.target = elem;\n\t\t}\n\n\t\t// Clone any incoming data and prepend the event, creating the handler arg list\n\t\tdata = data == null ?\n\t\t\t[ event ] :\n\t\t\tjQuery.makeArray( data, [ event ] );\n\n\t\t// Allow special events to draw outside the lines\n\t\tspecial = jQuery.event.special[ type ] || {};\n\t\tif ( !onlyHandlers && special.trigger && special.trigger.apply( elem, data ) === false ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Determine event propagation path in advance, per W3C events spec (#9951)\n\t\t// Bubble up to document, then to window; watch for a global ownerDocument var (#9724)\n\t\tif ( !onlyHandlers && !special.noBubble && !isWindow( elem ) ) {\n\n\t\t\tbubbleType = special.delegateType || type;\n\t\t\tif ( !rfocusMorph.test( bubbleType + type ) ) {\n\t\t\t\tcur = cur.parentNode;\n\t\t\t}\n\t\t\tfor ( ; cur; cur = cur.parentNode ) {\n\t\t\t\teventPath.push( cur );\n\t\t\t\ttmp = cur;\n\t\t\t}\n\n\t\t\t// Only add window if we got to document (e.g., not plain obj or detached DOM)\n\t\t\tif ( tmp === ( elem.ownerDocument || document ) ) {\n\t\t\t\teventPath.push( tmp.defaultView || tmp.parentWindow || window );\n\t\t\t}\n\t\t}\n\n\t\t// Fire handlers on the event path\n\t\ti = 0;\n\t\twhile ( ( cur = eventPath[ i++ ] ) && !event.isPropagationStopped() ) {\n\t\t\tlastElement = cur;\n\t\t\tevent.type = i > 1 ?\n\t\t\t\tbubbleType :\n\t\t\t\tspecial.bindType || type;\n\n\t\t\t// jQuery handler\n\t\t\thandle = ( dataPriv.get( cur, \"events\" ) || {} )[ event.type ] &&\n\t\t\t\tdataPriv.get( cur, \"handle\" );\n\t\t\tif ( handle ) {\n\t\t\t\thandle.apply( cur, data );\n\t\t\t}\n\n\t\t\t// Native handler\n\t\t\thandle = ontype && cur[ ontype ];\n\t\t\tif ( handle && handle.apply && acceptData( cur ) ) {\n\t\t\t\tevent.result = handle.apply( cur, data );\n\t\t\t\tif ( event.result === false ) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tevent.type = type;\n\n\t\t// If nobody prevented the default action, do it now\n\t\tif ( !onlyHandlers && !event.isDefaultPrevented() ) {\n\n\t\t\tif ( ( !special._default ||\n\t\t\t\tspecial._default.apply( eventPath.pop(), data ) === false ) &&\n\t\t\t\tacceptData( elem ) ) {\n\n\t\t\t\t// Call a native DOM method on the target with the same name as the event.\n\t\t\t\t// Don't do default actions on window, that's where global variables be (#6170)\n\t\t\t\tif ( ontype && isFunction( elem[ type ] ) && !isWindow( elem ) ) {\n\n\t\t\t\t\t// Don't re-trigger an onFOO event when we call its FOO() method\n\t\t\t\t\ttmp = elem[ ontype ];\n\n\t\t\t\t\tif ( tmp ) {\n\t\t\t\t\t\telem[ ontype ] = null;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Prevent re-triggering of the same event, since we already bubbled it above\n\t\t\t\t\tjQuery.event.triggered = type;\n\n\t\t\t\t\tif ( event.isPropagationStopped() ) {\n\t\t\t\t\t\tlastElement.addEventListener( type, stopPropagationCallback );\n\t\t\t\t\t}\n\n\t\t\t\t\telem[ type ]();\n\n\t\t\t\t\tif ( event.isPropagationStopped() ) {\n\t\t\t\t\t\tlastElement.removeEventListener( type, stopPropagationCallback );\n\t\t\t\t\t}\n\n\t\t\t\t\tjQuery.event.triggered = undefined;\n\n\t\t\t\t\tif ( tmp ) {\n\t\t\t\t\t\telem[ ontype ] = tmp;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn event.result;\n\t},\n\n\t// Piggyback on a donor event to simulate a different one\n\t// Used only for `focus(in | out)` events\n\tsimulate: function( type, elem, event ) {\n\t\tvar e = jQuery.extend(\n\t\t\tnew jQuery.Event(),\n\t\t\tevent,\n\t\t\t{\n\t\t\t\ttype: type,\n\t\t\t\tisSimulated: true\n\t\t\t}\n\t\t);\n\n\t\tjQuery.event.trigger( e, null, elem );\n\t}\n\n} );\n\njQuery.fn.extend( {\n\n\ttrigger: function( type, data ) {\n\t\treturn this.each( function() {\n\t\t\tjQuery.event.trigger( type, data, this );\n\t\t} );\n\t},\n\ttriggerHandler: function( type, data ) {\n\t\tvar elem = this[ 0 ];\n\t\tif ( elem ) {\n\t\t\treturn jQuery.event.trigger( type, data, elem, true );\n\t\t}\n\t}\n} );\n\n\n// Support: Firefox <=44\n// Firefox doesn't have focus(in | out) events\n// Related ticket - https://bugzilla.mozilla.org/show_bug.cgi?id=687787\n//\n// Support: Chrome <=48 - 49, Safari <=9.0 - 9.1\n// focus(in | out) events fire after focus & blur events,\n// which is spec violation - http://www.w3.org/TR/DOM-Level-3-Events/#events-focusevent-event-order\n// Related ticket - https://bugs.chromium.org/p/chromium/issues/detail?id=449857\nif ( !support.focusin ) {\n\tjQuery.each( { focus: \"focusin\", blur: \"focusout\" }, function( orig, fix ) {\n\n\t\t// Attach a single capturing handler on the document while someone wants focusin/focusout\n\t\tvar handler = function( event ) {\n\t\t\tjQuery.event.simulate( fix, event.target, jQuery.event.fix( event ) );\n\t\t};\n\n\t\tjQuery.event.special[ fix ] = {\n\t\t\tsetup: function() {\n\t\t\t\tvar doc = this.ownerDocument || this,\n\t\t\t\t\tattaches = dataPriv.access( doc, fix );\n\n\t\t\t\tif ( !attaches ) {\n\t\t\t\t\tdoc.addEventListener( orig, handler, true );\n\t\t\t\t}\n\t\t\t\tdataPriv.access( doc, fix, ( attaches || 0 ) + 1 );\n\t\t\t},\n\t\t\tteardown: function() {\n\t\t\t\tvar doc = this.ownerDocument || this,\n\t\t\t\t\tattaches = dataPriv.access( doc, fix ) - 1;\n\n\t\t\t\tif ( !attaches ) {\n\t\t\t\t\tdoc.removeEventListener( orig, handler, true );\n\t\t\t\t\tdataPriv.remove( doc, fix );\n\n\t\t\t\t} else {\n\t\t\t\t\tdataPriv.access( doc, fix, attaches );\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t} );\n}\nvar location = window.location;\n\nvar nonce = Date.now();\n\nvar rquery = ( /\\?/ );\n\n\n\n// Cross-browser xml parsing\njQuery.parseXML = function( data ) {\n\tvar xml;\n\tif ( !data || typeof data !== \"string\" ) {\n\t\treturn null;\n\t}\n\n\t// Support: IE 9 - 11 only\n\t// IE throws on parseFromString with invalid input.\n\ttry {\n\t\txml = ( new window.DOMParser() ).parseFromString( data, \"text/xml\" );\n\t} catch ( e ) {\n\t\txml = undefined;\n\t}\n\n\tif ( !xml || xml.getElementsByTagName( \"parsererror\" ).length ) {\n\t\tjQuery.error( \"Invalid XML: \" + data );\n\t}\n\treturn xml;\n};\n\n\nvar\n\trbracket = /\\[\\]$/,\n\trCRLF = /\\r?\\n/g,\n\trsubmitterTypes = /^(?:submit|button|image|reset|file)$/i,\n\trsubmittable = /^(?:input|select|textarea|keygen)/i;\n\nfunction buildParams( prefix, obj, traditional, add ) {\n\tvar name;\n\n\tif ( Array.isArray( obj ) ) {\n\n\t\t// Serialize array item.\n\t\tjQuery.each( obj, function( i, v ) {\n\t\t\tif ( traditional || rbracket.test( prefix ) ) {\n\n\t\t\t\t// Treat each array item as a scalar.\n\t\t\t\tadd( prefix, v );\n\n\t\t\t} else {\n\n\t\t\t\t// Item is non-scalar (array or object), encode its numeric index.\n\t\t\t\tbuildParams(\n\t\t\t\t\tprefix + \"[\" + ( typeof v === \"object\" && v != null ? i : \"\" ) + \"]\",\n\t\t\t\t\tv,\n\t\t\t\t\ttraditional,\n\t\t\t\t\tadd\n\t\t\t\t);\n\t\t\t}\n\t\t} );\n\n\t} else if ( !traditional && toType( obj ) === \"object\" ) {\n\n\t\t// Serialize object item.\n\t\tfor ( name in obj ) {\n\t\t\tbuildParams( prefix + \"[\" + name + \"]\", obj[ name ], traditional, add );\n\t\t}\n\n\t} else {\n\n\t\t// Serialize scalar item.\n\t\tadd( prefix, obj );\n\t}\n}\n\n// Serialize an array of form elements or a set of\n// key/values into a query string\njQuery.param = function( a, traditional ) {\n\tvar prefix,\n\t\ts = [],\n\t\tadd = function( key, valueOrFunction ) {\n\n\t\t\t// If value is a function, invoke it and use its return value\n\t\t\tvar value = isFunction( valueOrFunction ) ?\n\t\t\t\tvalueOrFunction() :\n\t\t\t\tvalueOrFunction;\n\n\t\t\ts[ s.length ] = encodeURIComponent( key ) + \"=\" +\n\t\t\t\tencodeURIComponent( value == null ? \"\" : value );\n\t\t};\n\n\tif ( a == null ) {\n\t\treturn \"\";\n\t}\n\n\t// If an array was passed in, assume that it is an array of form elements.\n\tif ( Array.isArray( a ) || ( a.jquery && !jQuery.isPlainObject( a ) ) ) {\n\n\t\t// Serialize the form elements\n\t\tjQuery.each( a, function() {\n\t\t\tadd( this.name, this.value );\n\t\t} );\n\n\t} else {\n\n\t\t// If traditional, encode the \"old\" way (the way 1.3.2 or older\n\t\t// did it), otherwise encode params recursively.\n\t\tfor ( prefix in a ) {\n\t\t\tbuildParams( prefix, a[ prefix ], traditional, add );\n\t\t}\n\t}\n\n\t// Return the resulting serialization\n\treturn s.join( \"&\" );\n};\n\njQuery.fn.extend( {\n\tserialize: function() {\n\t\treturn jQuery.param( this.serializeArray() );\n\t},\n\tserializeArray: function() {\n\t\treturn this.map( function() {\n\n\t\t\t// Can add propHook for \"elements\" to filter or add form elements\n\t\t\tvar elements = jQuery.prop( this, \"elements\" );\n\t\t\treturn elements ? jQuery.makeArray( elements ) : this;\n\t\t} )\n\t\t.filter( function() {\n\t\t\tvar type = this.type;\n\n\t\t\t// Use .is( \":disabled\" ) so that fieldset[disabled] works\n\t\t\treturn this.name && !jQuery( this ).is( \":disabled\" ) &&\n\t\t\t\trsubmittable.test( this.nodeName ) && !rsubmitterTypes.test( type ) &&\n\t\t\t\t( this.checked || !rcheckableType.test( type ) );\n\t\t} )\n\t\t.map( function( i, elem ) {\n\t\t\tvar val = jQuery( this ).val();\n\n\t\t\tif ( val == null ) {\n\t\t\t\treturn null;\n\t\t\t}\n\n\t\t\tif ( Array.isArray( val ) ) {\n\t\t\t\treturn jQuery.map( val, function( val ) {\n\t\t\t\t\treturn { name: elem.name, value: val.replace( rCRLF, \"\\r\\n\" ) };\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\treturn { name: elem.name, value: val.replace( rCRLF, \"\\r\\n\" ) };\n\t\t} ).get();\n\t}\n} );\n\n\nvar\n\tr20 = /%20/g,\n\trhash = /#.*$/,\n\trantiCache = /([?&])_=[^&]*/,\n\trheaders = /^(.*?):[ \\t]*([^\\r\\n]*)$/mg,\n\n\t// #7653, #8125, #8152: local protocol detection\n\trlocalProtocol = /^(?:about|app|app-storage|.+-extension|file|res|widget):$/,\n\trnoContent = /^(?:GET|HEAD)$/,\n\trprotocol = /^\\/\\//,\n\n\t/* Prefilters\n\t * 1) They are useful to introduce custom dataTypes (see ajax/jsonp.js for an example)\n\t * 2) These are called:\n\t *    - BEFORE asking for a transport\n\t *    - AFTER param serialization (s.data is a string if s.processData is true)\n\t * 3) key is the dataType\n\t * 4) the catchall symbol \"*\" can be used\n\t * 5) execution will start with transport dataType and THEN continue down to \"*\" if needed\n\t */\n\tprefilters = {},\n\n\t/* Transports bindings\n\t * 1) key is the dataType\n\t * 2) the catchall symbol \"*\" can be used\n\t * 3) selection will start with transport dataType and THEN go to \"*\" if needed\n\t */\n\ttransports = {},\n\n\t// Avoid comment-prolog char sequence (#10098); must appease lint and evade compression\n\tallTypes = \"*/\".concat( \"*\" ),\n\n\t// Anchor tag for parsing the document origin\n\toriginAnchor = document.createElement( \"a\" );\n\toriginAnchor.href = location.href;\n\n// Base \"constructor\" for jQuery.ajaxPrefilter and jQuery.ajaxTransport\nfunction addToPrefiltersOrTransports( structure ) {\n\n\t// dataTypeExpression is optional and defaults to \"*\"\n\treturn function( dataTypeExpression, func ) {\n\n\t\tif ( typeof dataTypeExpression !== \"string\" ) {\n\t\t\tfunc = dataTypeExpression;\n\t\t\tdataTypeExpression = \"*\";\n\t\t}\n\n\t\tvar dataType,\n\t\t\ti = 0,\n\t\t\tdataTypes = dataTypeExpression.toLowerCase().match( rnothtmlwhite ) || [];\n\n\t\tif ( isFunction( func ) ) {\n\n\t\t\t// For each dataType in the dataTypeExpression\n\t\t\twhile ( ( dataType = dataTypes[ i++ ] ) ) {\n\n\t\t\t\t// Prepend if requested\n\t\t\t\tif ( dataType[ 0 ] === \"+\" ) {\n\t\t\t\t\tdataType = dataType.slice( 1 ) || \"*\";\n\t\t\t\t\t( structure[ dataType ] = structure[ dataType ] || [] ).unshift( func );\n\n\t\t\t\t// Otherwise append\n\t\t\t\t} else {\n\t\t\t\t\t( structure[ dataType ] = structure[ dataType ] || [] ).push( func );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n}\n\n// Base inspection function for prefilters and transports\nfunction inspectPrefiltersOrTransports( structure, options, originalOptions, jqXHR ) {\n\n\tvar inspected = {},\n\t\tseekingTransport = ( structure === transports );\n\n\tfunction inspect( dataType ) {\n\t\tvar selected;\n\t\tinspected[ dataType ] = true;\n\t\tjQuery.each( structure[ dataType ] || [], function( _, prefilterOrFactory ) {\n\t\t\tvar dataTypeOrTransport = prefilterOrFactory( options, originalOptions, jqXHR );\n\t\t\tif ( typeof dataTypeOrTransport === \"string\" &&\n\t\t\t\t!seekingTransport && !inspected[ dataTypeOrTransport ] ) {\n\n\t\t\t\toptions.dataTypes.unshift( dataTypeOrTransport );\n\t\t\t\tinspect( dataTypeOrTransport );\n\t\t\t\treturn false;\n\t\t\t} else if ( seekingTransport ) {\n\t\t\t\treturn !( selected = dataTypeOrTransport );\n\t\t\t}\n\t\t} );\n\t\treturn selected;\n\t}\n\n\treturn inspect( options.dataTypes[ 0 ] ) || !inspected[ \"*\" ] && inspect( \"*\" );\n}\n\n// A special extend for ajax options\n// that takes \"flat\" options (not to be deep extended)\n// Fixes #9887\nfunction ajaxExtend( target, src ) {\n\tvar key, deep,\n\t\tflatOptions = jQuery.ajaxSettings.flatOptions || {};\n\n\tfor ( key in src ) {\n\t\tif ( src[ key ] !== undefined ) {\n\t\t\t( flatOptions[ key ] ? target : ( deep || ( deep = {} ) ) )[ key ] = src[ key ];\n\t\t}\n\t}\n\tif ( deep ) {\n\t\tjQuery.extend( true, target, deep );\n\t}\n\n\treturn target;\n}\n\n/* Handles responses to an ajax request:\n * - finds the right dataType (mediates between content-type and expected dataType)\n * - returns the corresponding response\n */\nfunction ajaxHandleResponses( s, jqXHR, responses ) {\n\n\tvar ct, type, finalDataType, firstDataType,\n\t\tcontents = s.contents,\n\t\tdataTypes = s.dataTypes;\n\n\t// Remove auto dataType and get content-type in the process\n\twhile ( dataTypes[ 0 ] === \"*\" ) {\n\t\tdataTypes.shift();\n\t\tif ( ct === undefined ) {\n\t\t\tct = s.mimeType || jqXHR.getResponseHeader( \"Content-Type\" );\n\t\t}\n\t}\n\n\t// Check if we're dealing with a known content-type\n\tif ( ct ) {\n\t\tfor ( type in contents ) {\n\t\t\tif ( contents[ type ] && contents[ type ].test( ct ) ) {\n\t\t\t\tdataTypes.unshift( type );\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Check to see if we have a response for the expected dataType\n\tif ( dataTypes[ 0 ] in responses ) {\n\t\tfinalDataType = dataTypes[ 0 ];\n\t} else {\n\n\t\t// Try convertible dataTypes\n\t\tfor ( type in responses ) {\n\t\t\tif ( !dataTypes[ 0 ] || s.converters[ type + \" \" + dataTypes[ 0 ] ] ) {\n\t\t\t\tfinalDataType = type;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif ( !firstDataType ) {\n\t\t\t\tfirstDataType = type;\n\t\t\t}\n\t\t}\n\n\t\t// Or just use first one\n\t\tfinalDataType = finalDataType || firstDataType;\n\t}\n\n\t// If we found a dataType\n\t// We add the dataType to the list if needed\n\t// and return the corresponding response\n\tif ( finalDataType ) {\n\t\tif ( finalDataType !== dataTypes[ 0 ] ) {\n\t\t\tdataTypes.unshift( finalDataType );\n\t\t}\n\t\treturn responses[ finalDataType ];\n\t}\n}\n\n/* Chain conversions given the request and the original response\n * Also sets the responseXXX fields on the jqXHR instance\n */\nfunction ajaxConvert( s, response, jqXHR, isSuccess ) {\n\tvar conv2, current, conv, tmp, prev,\n\t\tconverters = {},\n\n\t\t// Work with a copy of dataTypes in case we need to modify it for conversion\n\t\tdataTypes = s.dataTypes.slice();\n\n\t// Create converters map with lowercased keys\n\tif ( dataTypes[ 1 ] ) {\n\t\tfor ( conv in s.converters ) {\n\t\t\tconverters[ conv.toLowerCase() ] = s.converters[ conv ];\n\t\t}\n\t}\n\n\tcurrent = dataTypes.shift();\n\n\t// Convert to each sequential dataType\n\twhile ( current ) {\n\n\t\tif ( s.responseFields[ current ] ) {\n\t\t\tjqXHR[ s.responseFields[ current ] ] = response;\n\t\t}\n\n\t\t// Apply the dataFilter if provided\n\t\tif ( !prev && isSuccess && s.dataFilter ) {\n\t\t\tresponse = s.dataFilter( response, s.dataType );\n\t\t}\n\n\t\tprev = current;\n\t\tcurrent = dataTypes.shift();\n\n\t\tif ( current ) {\n\n\t\t\t// There's only work to do if current dataType is non-auto\n\t\t\tif ( current === \"*\" ) {\n\n\t\t\t\tcurrent = prev;\n\n\t\t\t// Convert response if prev dataType is non-auto and differs from current\n\t\t\t} else if ( prev !== \"*\" && prev !== current ) {\n\n\t\t\t\t// Seek a direct converter\n\t\t\t\tconv = converters[ prev + \" \" + current ] || converters[ \"* \" + current ];\n\n\t\t\t\t// If none found, seek a pair\n\t\t\t\tif ( !conv ) {\n\t\t\t\t\tfor ( conv2 in converters ) {\n\n\t\t\t\t\t\t// If conv2 outputs current\n\t\t\t\t\t\ttmp = conv2.split( \" \" );\n\t\t\t\t\t\tif ( tmp[ 1 ] === current ) {\n\n\t\t\t\t\t\t\t// If prev can be converted to accepted input\n\t\t\t\t\t\t\tconv = converters[ prev + \" \" + tmp[ 0 ] ] ||\n\t\t\t\t\t\t\t\tconverters[ \"* \" + tmp[ 0 ] ];\n\t\t\t\t\t\t\tif ( conv ) {\n\n\t\t\t\t\t\t\t\t// Condense equivalence converters\n\t\t\t\t\t\t\t\tif ( conv === true ) {\n\t\t\t\t\t\t\t\t\tconv = converters[ conv2 ];\n\n\t\t\t\t\t\t\t\t// Otherwise, insert the intermediate dataType\n\t\t\t\t\t\t\t\t} else if ( converters[ conv2 ] !== true ) {\n\t\t\t\t\t\t\t\t\tcurrent = tmp[ 0 ];\n\t\t\t\t\t\t\t\t\tdataTypes.unshift( tmp[ 1 ] );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Apply converter (if not an equivalence)\n\t\t\t\tif ( conv !== true ) {\n\n\t\t\t\t\t// Unless errors are allowed to bubble, catch and return them\n\t\t\t\t\tif ( conv && s.throws ) {\n\t\t\t\t\t\tresponse = conv( response );\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tresponse = conv( response );\n\t\t\t\t\t\t} catch ( e ) {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tstate: \"parsererror\",\n\t\t\t\t\t\t\t\terror: conv ? e : \"No conversion from \" + prev + \" to \" + current\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn { state: \"success\", data: response };\n}\n\njQuery.extend( {\n\n\t// Counter for holding the number of active queries\n\tactive: 0,\n\n\t// Last-Modified header cache for next request\n\tlastModified: {},\n\tetag: {},\n\n\tajaxSettings: {\n\t\turl: location.href,\n\t\ttype: \"GET\",\n\t\tisLocal: rlocalProtocol.test( location.protocol ),\n\t\tglobal: true,\n\t\tprocessData: true,\n\t\tasync: true,\n\t\tcontentType: \"application/x-www-form-urlencoded; charset=UTF-8\",\n\n\t\t/*\n\t\ttimeout: 0,\n\t\tdata: null,\n\t\tdataType: null,\n\t\tusername: null,\n\t\tpassword: null,\n\t\tcache: null,\n\t\tthrows: false,\n\t\ttraditional: false,\n\t\theaders: {},\n\t\t*/\n\n\t\taccepts: {\n\t\t\t\"*\": allTypes,\n\t\t\ttext: \"text/plain\",\n\t\t\thtml: \"text/html\",\n\t\t\txml: \"application/xml, text/xml\",\n\t\t\tjson: \"application/json, text/javascript\"\n\t\t},\n\n\t\tcontents: {\n\t\t\txml: /\\bxml\\b/,\n\t\t\thtml: /\\bhtml/,\n\t\t\tjson: /\\bjson\\b/\n\t\t},\n\n\t\tresponseFields: {\n\t\t\txml: \"responseXML\",\n\t\t\ttext: \"responseText\",\n\t\t\tjson: \"responseJSON\"\n\t\t},\n\n\t\t// Data converters\n\t\t// Keys separate source (or catchall \"*\") and destination types with a single space\n\t\tconverters: {\n\n\t\t\t// Convert anything to text\n\t\t\t\"* text\": String,\n\n\t\t\t// Text to html (true = no transformation)\n\t\t\t\"text html\": true,\n\n\t\t\t// Evaluate text as a json expression\n\t\t\t\"text json\": JSON.parse,\n\n\t\t\t// Parse text as xml\n\t\t\t\"text xml\": jQuery.parseXML\n\t\t},\n\n\t\t// For options that shouldn't be deep extended:\n\t\t// you can add your own custom options here if\n\t\t// and when you create one that shouldn't be\n\t\t// deep extended (see ajaxExtend)\n\t\tflatOptions: {\n\t\t\turl: true,\n\t\t\tcontext: true\n\t\t}\n\t},\n\n\t// Creates a full fledged settings object into target\n\t// with both ajaxSettings and settings fields.\n\t// If target is omitted, writes into ajaxSettings.\n\tajaxSetup: function( target, settings ) {\n\t\treturn settings ?\n\n\t\t\t// Building a settings object\n\t\t\tajaxExtend( ajaxExtend( target, jQuery.ajaxSettings ), settings ) :\n\n\t\t\t// Extending ajaxSettings\n\t\t\tajaxExtend( jQuery.ajaxSettings, target );\n\t},\n\n\tajaxPrefilter: addToPrefiltersOrTransports( prefilters ),\n\tajaxTransport: addToPrefiltersOrTransports( transports ),\n\n\t// Main method\n\tajax: function( url, options ) {\n\n\t\t// If url is an object, simulate pre-1.5 signature\n\t\tif ( typeof url === \"object\" ) {\n\t\t\toptions = url;\n\t\t\turl = undefined;\n\t\t}\n\n\t\t// Force options to be an object\n\t\toptions = options || {};\n\n\t\tvar transport,\n\n\t\t\t// URL without anti-cache param\n\t\t\tcacheURL,\n\n\t\t\t// Response headers\n\t\t\tresponseHeadersString,\n\t\t\tresponseHeaders,\n\n\t\t\t// timeout handle\n\t\t\ttimeoutTimer,\n\n\t\t\t// Url cleanup var\n\t\t\turlAnchor,\n\n\t\t\t// Request state (becomes false upon send and true upon completion)\n\t\t\tcompleted,\n\n\t\t\t// To know if global events are to be dispatched\n\t\t\tfireGlobals,\n\n\t\t\t// Loop variable\n\t\t\ti,\n\n\t\t\t// uncached part of the url\n\t\t\tuncached,\n\n\t\t\t// Create the final options object\n\t\t\ts = jQuery.ajaxSetup( {}, options ),\n\n\t\t\t// Callbacks context\n\t\t\tcallbackContext = s.context || s,\n\n\t\t\t// Context for global events is callbackContext if it is a DOM node or jQuery collection\n\t\t\tglobalEventContext = s.context &&\n\t\t\t\t( callbackContext.nodeType || callbackContext.jquery ) ?\n\t\t\t\t\tjQuery( callbackContext ) :\n\t\t\t\t\tjQuery.event,\n\n\t\t\t// Deferreds\n\t\t\tdeferred = jQuery.Deferred(),\n\t\t\tcompleteDeferred = jQuery.Callbacks( \"once memory\" ),\n\n\t\t\t// Status-dependent callbacks\n\t\t\tstatusCode = s.statusCode || {},\n\n\t\t\t// Headers (they are sent all at once)\n\t\t\trequestHeaders = {},\n\t\t\trequestHeadersNames = {},\n\n\t\t\t// Default abort message\n\t\t\tstrAbort = \"canceled\",\n\n\t\t\t// Fake xhr\n\t\t\tjqXHR = {\n\t\t\t\treadyState: 0,\n\n\t\t\t\t// Builds headers hashtable if needed\n\t\t\t\tgetResponseHeader: function( key ) {\n\t\t\t\t\tvar match;\n\t\t\t\t\tif ( completed ) {\n\t\t\t\t\t\tif ( !responseHeaders ) {\n\t\t\t\t\t\t\tresponseHeaders = {};\n\t\t\t\t\t\t\twhile ( ( match = rheaders.exec( responseHeadersString ) ) ) {\n\t\t\t\t\t\t\t\tresponseHeaders[ match[ 1 ].toLowerCase() + \" \" ] =\n\t\t\t\t\t\t\t\t\t( responseHeaders[ match[ 1 ].toLowerCase() + \" \" ] || [] )\n\t\t\t\t\t\t\t\t\t\t.concat( match[ 2 ] );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tmatch = responseHeaders[ key.toLowerCase() + \" \" ];\n\t\t\t\t\t}\n\t\t\t\t\treturn match == null ? null : match.join( \", \" );\n\t\t\t\t},\n\n\t\t\t\t// Raw string\n\t\t\t\tgetAllResponseHeaders: function() {\n\t\t\t\t\treturn completed ? responseHeadersString : null;\n\t\t\t\t},\n\n\t\t\t\t// Caches the header\n\t\t\t\tsetRequestHeader: function( name, value ) {\n\t\t\t\t\tif ( completed == null ) {\n\t\t\t\t\t\tname = requestHeadersNames[ name.toLowerCase() ] =\n\t\t\t\t\t\t\trequestHeadersNames[ name.toLowerCase() ] || name;\n\t\t\t\t\t\trequestHeaders[ name ] = value;\n\t\t\t\t\t}\n\t\t\t\t\treturn this;\n\t\t\t\t},\n\n\t\t\t\t// Overrides response content-type header\n\t\t\t\toverrideMimeType: function( type ) {\n\t\t\t\t\tif ( completed == null ) {\n\t\t\t\t\t\ts.mimeType = type;\n\t\t\t\t\t}\n\t\t\t\t\treturn this;\n\t\t\t\t},\n\n\t\t\t\t// Status-dependent callbacks\n\t\t\t\tstatusCode: function( map ) {\n\t\t\t\t\tvar code;\n\t\t\t\t\tif ( map ) {\n\t\t\t\t\t\tif ( completed ) {\n\n\t\t\t\t\t\t\t// Execute the appropriate callbacks\n\t\t\t\t\t\t\tjqXHR.always( map[ jqXHR.status ] );\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t// Lazy-add the new callbacks in a way that preserves old ones\n\t\t\t\t\t\t\tfor ( code in map ) {\n\t\t\t\t\t\t\t\tstatusCode[ code ] = [ statusCode[ code ], map[ code ] ];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn this;\n\t\t\t\t},\n\n\t\t\t\t// Cancel the request\n\t\t\t\tabort: function( statusText ) {\n\t\t\t\t\tvar finalText = statusText || strAbort;\n\t\t\t\t\tif ( transport ) {\n\t\t\t\t\t\ttransport.abort( finalText );\n\t\t\t\t\t}\n\t\t\t\t\tdone( 0, finalText );\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t};\n\n\t\t// Attach deferreds\n\t\tdeferred.promise( jqXHR );\n\n\t\t// Add protocol if not provided (prefilters might expect it)\n\t\t// Handle falsy url in the settings object (#10093: consistency with old signature)\n\t\t// We also use the url parameter if available\n\t\ts.url = ( ( url || s.url || location.href ) + \"\" )\n\t\t\t.replace( rprotocol, location.protocol + \"//\" );\n\n\t\t// Alias method option to type as per ticket #12004\n\t\ts.type = options.method || options.type || s.method || s.type;\n\n\t\t// Extract dataTypes list\n\t\ts.dataTypes = ( s.dataType || \"*\" ).toLowerCase().match( rnothtmlwhite ) || [ \"\" ];\n\n\t\t// A cross-domain request is in order when the origin doesn't match the current origin.\n\t\tif ( s.crossDomain == null ) {\n\t\t\turlAnchor = document.createElement( \"a\" );\n\n\t\t\t// Support: IE <=8 - 11, Edge 12 - 15\n\t\t\t// IE throws exception on accessing the href property if url is malformed,\n\t\t\t// e.g. http://example.com:80x/\n\t\t\ttry {\n\t\t\t\turlAnchor.href = s.url;\n\n\t\t\t\t// Support: IE <=8 - 11 only\n\t\t\t\t// Anchor's host property isn't correctly set when s.url is relative\n\t\t\t\turlAnchor.href = urlAnchor.href;\n\t\t\t\ts.crossDomain = originAnchor.protocol + \"//\" + originAnchor.host !==\n\t\t\t\t\turlAnchor.protocol + \"//\" + urlAnchor.host;\n\t\t\t} catch ( e ) {\n\n\t\t\t\t// If there is an error parsing the URL, assume it is crossDomain,\n\t\t\t\t// it can be rejected by the transport if it is invalid\n\t\t\t\ts.crossDomain = true;\n\t\t\t}\n\t\t}\n\n\t\t// Convert data if not already a string\n\t\tif ( s.data && s.processData && typeof s.data !== \"string\" ) {\n\t\t\ts.data = jQuery.param( s.data, s.traditional );\n\t\t}\n\n\t\t// Apply prefilters\n\t\tinspectPrefiltersOrTransports( prefilters, s, options, jqXHR );\n\n\t\t// If request was aborted inside a prefilter, stop there\n\t\tif ( completed ) {\n\t\t\treturn jqXHR;\n\t\t}\n\n\t\t// We can fire global events as of now if asked to\n\t\t// Don't fire events if jQuery.event is undefined in an AMD-usage scenario (#15118)\n\t\tfireGlobals = jQuery.event && s.global;\n\n\t\t// Watch for a new set of requests\n\t\tif ( fireGlobals && jQuery.active++ === 0 ) {\n\t\t\tjQuery.event.trigger( \"ajaxStart\" );\n\t\t}\n\n\t\t// Uppercase the type\n\t\ts.type = s.type.toUpperCase();\n\n\t\t// Determine if request has content\n\t\ts.hasContent = !rnoContent.test( s.type );\n\n\t\t// Save the URL in case we're toying with the If-Modified-Since\n\t\t// and/or If-None-Match header later on\n\t\t// Remove hash to simplify url manipulation\n\t\tcacheURL = s.url.replace( rhash, \"\" );\n\n\t\t// More options handling for requests with no content\n\t\tif ( !s.hasContent ) {\n\n\t\t\t// Remember the hash so we can put it back\n\t\t\tuncached = s.url.slice( cacheURL.length );\n\n\t\t\t// If data is available and should be processed, append data to url\n\t\t\tif ( s.data && ( s.processData || typeof s.data === \"string\" ) ) {\n\t\t\t\tcacheURL += ( rquery.test( cacheURL ) ? \"&\" : \"?\" ) + s.data;\n\n\t\t\t\t// #9682: remove data so that it's not used in an eventual retry\n\t\t\t\tdelete s.data;\n\t\t\t}\n\n\t\t\t// Add or update anti-cache param if needed\n\t\t\tif ( s.cache === false ) {\n\t\t\t\tcacheURL = cacheURL.replace( rantiCache, \"$1\" );\n\t\t\t\tuncached = ( rquery.test( cacheURL ) ? \"&\" : \"?\" ) + \"_=\" + ( nonce++ ) + uncached;\n\t\t\t}\n\n\t\t\t// Put hash and anti-cache on the URL that will be requested (gh-1732)\n\t\t\ts.url = cacheURL + uncached;\n\n\t\t// Change '%20' to '+' if this is encoded form body content (gh-2658)\n\t\t} else if ( s.data && s.processData &&\n\t\t\t( s.contentType || \"\" ).indexOf( \"application/x-www-form-urlencoded\" ) === 0 ) {\n\t\t\ts.data = s.data.replace( r20, \"+\" );\n\t\t}\n\n\t\t// Set the If-Modified-Since and/or If-None-Match header, if in ifModified mode.\n\t\tif ( s.ifModified ) {\n\t\t\tif ( jQuery.lastModified[ cacheURL ] ) {\n\t\t\t\tjqXHR.setRequestHeader( \"If-Modified-Since\", jQuery.lastModified[ cacheURL ] );\n\t\t\t}\n\t\t\tif ( jQuery.etag[ cacheURL ] ) {\n\t\t\t\tjqXHR.setRequestHeader( \"If-None-Match\", jQuery.etag[ cacheURL ] );\n\t\t\t}\n\t\t}\n\n\t\t// Set the correct header, if data is being sent\n\t\tif ( s.data && s.hasContent && s.contentType !== false || options.contentType ) {\n\t\t\tjqXHR.setRequestHeader( \"Content-Type\", s.contentType );\n\t\t}\n\n\t\t// Set the Accepts header for the server, depending on the dataType\n\t\tjqXHR.setRequestHeader(\n\t\t\t\"Accept\",\n\t\t\ts.dataTypes[ 0 ] && s.accepts[ s.dataTypes[ 0 ] ] ?\n\t\t\t\ts.accepts[ s.dataTypes[ 0 ] ] +\n\t\t\t\t\t( s.dataTypes[ 0 ] !== \"*\" ? \", \" + allTypes + \"; q=0.01\" : \"\" ) :\n\t\t\t\ts.accepts[ \"*\" ]\n\t\t);\n\n\t\t// Check for headers option\n\t\tfor ( i in s.headers ) {\n\t\t\tjqXHR.setRequestHeader( i, s.headers[ i ] );\n\t\t}\n\n\t\t// Allow custom headers/mimetypes and early abort\n\t\tif ( s.beforeSend &&\n\t\t\t( s.beforeSend.call( callbackContext, jqXHR, s ) === false || completed ) ) {\n\n\t\t\t// Abort if not done already and return\n\t\t\treturn jqXHR.abort();\n\t\t}\n\n\t\t// Aborting is no longer a cancellation\n\t\tstrAbort = \"abort\";\n\n\t\t// Install callbacks on deferreds\n\t\tcompleteDeferred.add( s.complete );\n\t\tjqXHR.done( s.success );\n\t\tjqXHR.fail( s.error );\n\n\t\t// Get transport\n\t\ttransport = inspectPrefiltersOrTransports( transports, s, options, jqXHR );\n\n\t\t// If no transport, we auto-abort\n\t\tif ( !transport ) {\n\t\t\tdone( -1, \"No Transport\" );\n\t\t} else {\n\t\t\tjqXHR.readyState = 1;\n\n\t\t\t// Send global event\n\t\t\tif ( fireGlobals ) {\n\t\t\t\tglobalEventContext.trigger( \"ajaxSend\", [ jqXHR, s ] );\n\t\t\t}\n\n\t\t\t// If request was aborted inside ajaxSend, stop there\n\t\t\tif ( completed ) {\n\t\t\t\treturn jqXHR;\n\t\t\t}\n\n\t\t\t// Timeout\n\t\t\tif ( s.async && s.timeout > 0 ) {\n\t\t\t\ttimeoutTimer = window.setTimeout( function() {\n\t\t\t\t\tjqXHR.abort( \"timeout\" );\n\t\t\t\t}, s.timeout );\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\tcompleted = false;\n\t\t\t\ttransport.send( requestHeaders, done );\n\t\t\t} catch ( e ) {\n\n\t\t\t\t// Rethrow post-completion exceptions\n\t\t\t\tif ( completed ) {\n\t\t\t\t\tthrow e;\n\t\t\t\t}\n\n\t\t\t\t// Propagate others as results\n\t\t\t\tdone( -1, e );\n\t\t\t}\n\t\t}\n\n\t\t// Callback for when everything is done\n\t\tfunction done( status, nativeStatusText, responses, headers ) {\n\t\t\tvar isSuccess, success, error, response, modified,\n\t\t\t\tstatusText = nativeStatusText;\n\n\t\t\t// Ignore repeat invocations\n\t\t\tif ( completed ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tcompleted = true;\n\n\t\t\t// Clear timeout if it exists\n\t\t\tif ( timeoutTimer ) {\n\t\t\t\twindow.clearTimeout( timeoutTimer );\n\t\t\t}\n\n\t\t\t// Dereference transport for early garbage collection\n\t\t\t// (no matter how long the jqXHR object will be used)\n\t\t\ttransport = undefined;\n\n\t\t\t// Cache response headers\n\t\t\tresponseHeadersString = headers || \"\";\n\n\t\t\t// Set readyState\n\t\t\tjqXHR.readyState = status > 0 ? 4 : 0;\n\n\t\t\t// Determine if successful\n\t\t\tisSuccess = status >= 200 && status < 300 || status === 304;\n\n\t\t\t// Get response data\n\t\t\tif ( responses ) {\n\t\t\t\tresponse = ajaxHandleResponses( s, jqXHR, responses );\n\t\t\t}\n\n\t\t\t// Convert no matter what (that way responseXXX fields are always set)\n\t\t\tresponse = ajaxConvert( s, response, jqXHR, isSuccess );\n\n\t\t\t// If successful, handle type chaining\n\t\t\tif ( isSuccess ) {\n\n\t\t\t\t// Set the If-Modified-Since and/or If-None-Match header, if in ifModified mode.\n\t\t\t\tif ( s.ifModified ) {\n\t\t\t\t\tmodified = jqXHR.getResponseHeader( \"Last-Modified\" );\n\t\t\t\t\tif ( modified ) {\n\t\t\t\t\t\tjQuery.lastModified[ cacheURL ] = modified;\n\t\t\t\t\t}\n\t\t\t\t\tmodified = jqXHR.getResponseHeader( \"etag\" );\n\t\t\t\t\tif ( modified ) {\n\t\t\t\t\t\tjQuery.etag[ cacheURL ] = modified;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// if no content\n\t\t\t\tif ( status === 204 || s.type === \"HEAD\" ) {\n\t\t\t\t\tstatusText = \"nocontent\";\n\n\t\t\t\t// if not modified\n\t\t\t\t} else if ( status === 304 ) {\n\t\t\t\t\tstatusText = \"notmodified\";\n\n\t\t\t\t// If we have data, let's convert it\n\t\t\t\t} else {\n\t\t\t\t\tstatusText = response.state;\n\t\t\t\t\tsuccess = response.data;\n\t\t\t\t\terror = response.error;\n\t\t\t\t\tisSuccess = !error;\n\t\t\t\t}\n\t\t\t} else {\n\n\t\t\t\t// Extract error from statusText and normalize for non-aborts\n\t\t\t\terror = statusText;\n\t\t\t\tif ( status || !statusText ) {\n\t\t\t\t\tstatusText = \"error\";\n\t\t\t\t\tif ( status < 0 ) {\n\t\t\t\t\t\tstatus = 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Set data for the fake xhr object\n\t\t\tjqXHR.status = status;\n\t\t\tjqXHR.statusText = ( nativeStatusText || statusText ) + \"\";\n\n\t\t\t// Success/Error\n\t\t\tif ( isSuccess ) {\n\t\t\t\tdeferred.resolveWith( callbackContext, [ success, statusText, jqXHR ] );\n\t\t\t} else {\n\t\t\t\tdeferred.rejectWith( callbackContext, [ jqXHR, statusText, error ] );\n\t\t\t}\n\n\t\t\t// Status-dependent callbacks\n\t\t\tjqXHR.statusCode( statusCode );\n\t\t\tstatusCode = undefined;\n\n\t\t\tif ( fireGlobals ) {\n\t\t\t\tglobalEventContext.trigger( isSuccess ? \"ajaxSuccess\" : \"ajaxError\",\n\t\t\t\t\t[ jqXHR, s, isSuccess ? success : error ] );\n\t\t\t}\n\n\t\t\t// Complete\n\t\t\tcompleteDeferred.fireWith( callbackContext, [ jqXHR, statusText ] );\n\n\t\t\tif ( fireGlobals ) {\n\t\t\t\tglobalEventContext.trigger( \"ajaxComplete\", [ jqXHR, s ] );\n\n\t\t\t\t// Handle the global AJAX counter\n\t\t\t\tif ( !( --jQuery.active ) ) {\n\t\t\t\t\tjQuery.event.trigger( \"ajaxStop\" );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn jqXHR;\n\t},\n\n\tgetJSON: function( url, data, callback ) {\n\t\treturn jQuery.get( url, data, callback, \"json\" );\n\t},\n\n\tgetScript: function( url, callback ) {\n\t\treturn jQuery.get( url, undefined, callback, \"script\" );\n\t}\n} );\n\njQuery.each( [ \"get\", \"post\" ], function( i, method ) {\n\tjQuery[ method ] = function( url, data, callback, type ) {\n\n\t\t// Shift arguments if data argument was omitted\n\t\tif ( isFunction( data ) ) {\n\t\t\ttype = type || callback;\n\t\t\tcallback = data;\n\t\t\tdata = undefined;\n\t\t}\n\n\t\t// The url can be an options object (which then must have .url)\n\t\treturn jQuery.ajax( jQuery.extend( {\n\t\t\turl: url,\n\t\t\ttype: method,\n\t\t\tdataType: type,\n\t\t\tdata: data,\n\t\t\tsuccess: callback\n\t\t}, jQuery.isPlainObject( url ) && url ) );\n\t};\n} );\n\n\njQuery._evalUrl = function( url, options ) {\n\treturn jQuery.ajax( {\n\t\turl: url,\n\n\t\t// Make this explicit, since user can override this through ajaxSetup (#11264)\n\t\ttype: \"GET\",\n\t\tdataType: \"script\",\n\t\tcache: true,\n\t\tasync: false,\n\t\tglobal: false,\n\n\t\t// Only evaluate the response if it is successful (gh-4126)\n\t\t// dataFilter is not invoked for failure responses, so using it instead\n\t\t// of the default converter is kludgy but it works.\n\t\tconverters: {\n\t\t\t\"text script\": function() {}\n\t\t},\n\t\tdataFilter: function( response ) {\n\t\t\tjQuery.globalEval( response, options );\n\t\t}\n\t} );\n};\n\n\njQuery.fn.extend( {\n\twrapAll: function( html ) {\n\t\tvar wrap;\n\n\t\tif ( this[ 0 ] ) {\n\t\t\tif ( isFunction( html ) ) {\n\t\t\t\thtml = html.call( this[ 0 ] );\n\t\t\t}\n\n\t\t\t// The elements to wrap the target around\n\t\t\twrap = jQuery( html, this[ 0 ].ownerDocument ).eq( 0 ).clone( true );\n\n\t\t\tif ( this[ 0 ].parentNode ) {\n\t\t\t\twrap.insertBefore( this[ 0 ] );\n\t\t\t}\n\n\t\t\twrap.map( function() {\n\t\t\t\tvar elem = this;\n\n\t\t\t\twhile ( elem.firstElementChild ) {\n\t\t\t\t\telem = elem.firstElementChild;\n\t\t\t\t}\n\n\t\t\t\treturn elem;\n\t\t\t} ).append( this );\n\t\t}\n\n\t\treturn this;\n\t},\n\n\twrapInner: function( html ) {\n\t\tif ( isFunction( html ) ) {\n\t\t\treturn this.each( function( i ) {\n\t\t\t\tjQuery( this ).wrapInner( html.call( this, i ) );\n\t\t\t} );\n\t\t}\n\n\t\treturn this.each( function() {\n\t\t\tvar self = jQuery( this ),\n\t\t\t\tcontents = self.contents();\n\n\t\t\tif ( contents.length ) {\n\t\t\t\tcontents.wrapAll( html );\n\n\t\t\t} else {\n\t\t\t\tself.append( html );\n\t\t\t}\n\t\t} );\n\t},\n\n\twrap: function( html ) {\n\t\tvar htmlIsFunction = isFunction( html );\n\n\t\treturn this.each( function( i ) {\n\t\t\tjQuery( this ).wrapAll( htmlIsFunction ? html.call( this, i ) : html );\n\t\t} );\n\t},\n\n\tunwrap: function( selector ) {\n\t\tthis.parent( selector ).not( \"body\" ).each( function() {\n\t\t\tjQuery( this ).replaceWith( this.childNodes );\n\t\t} );\n\t\treturn this;\n\t}\n} );\n\n\njQuery.expr.pseudos.hidden = function( elem ) {\n\treturn !jQuery.expr.pseudos.visible( elem );\n};\njQuery.expr.pseudos.visible = function( elem ) {\n\treturn !!( elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length );\n};\n\n\n\n\njQuery.ajaxSettings.xhr = function() {\n\ttry {\n\t\treturn new window.XMLHttpRequest();\n\t} catch ( e ) {}\n};\n\nvar xhrSuccessStatus = {\n\n\t\t// File protocol always yields status code 0, assume 200\n\t\t0: 200,\n\n\t\t// Support: IE <=9 only\n\t\t// #1450: sometimes IE returns 1223 when it should be 204\n\t\t1223: 204\n\t},\n\txhrSupported = jQuery.ajaxSettings.xhr();\n\nsupport.cors = !!xhrSupported && ( \"withCredentials\" in xhrSupported );\nsupport.ajax = xhrSupported = !!xhrSupported;\n\njQuery.ajaxTransport( function( options ) {\n\tvar callback, errorCallback;\n\n\t// Cross domain only allowed if supported through XMLHttpRequest\n\tif ( support.cors || xhrSupported && !options.crossDomain ) {\n\t\treturn {\n\t\t\tsend: function( headers, complete ) {\n\t\t\t\tvar i,\n\t\t\t\t\txhr = options.xhr();\n\n\t\t\t\txhr.open(\n\t\t\t\t\toptions.type,\n\t\t\t\t\toptions.url,\n\t\t\t\t\toptions.async,\n\t\t\t\t\toptions.username,\n\t\t\t\t\toptions.password\n\t\t\t\t);\n\n\t\t\t\t// Apply custom fields if provided\n\t\t\t\tif ( options.xhrFields ) {\n\t\t\t\t\tfor ( i in options.xhrFields ) {\n\t\t\t\t\t\txhr[ i ] = options.xhrFields[ i ];\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Override mime type if needed\n\t\t\t\tif ( options.mimeType && xhr.overrideMimeType ) {\n\t\t\t\t\txhr.overrideMimeType( options.mimeType );\n\t\t\t\t}\n\n\t\t\t\t// X-Requested-With header\n\t\t\t\t// For cross-domain requests, seeing as conditions for a preflight are\n\t\t\t\t// akin to a jigsaw puzzle, we simply never set it to be sure.\n\t\t\t\t// (it can always be set on a per-request basis or even using ajaxSetup)\n\t\t\t\t// For same-domain requests, won't change header if already provided.\n\t\t\t\tif ( !options.crossDomain && !headers[ \"X-Requested-With\" ] ) {\n\t\t\t\t\theaders[ \"X-Requested-With\" ] = \"XMLHttpRequest\";\n\t\t\t\t}\n\n\t\t\t\t// Set headers\n\t\t\t\tfor ( i in headers ) {\n\t\t\t\t\txhr.setRequestHeader( i, headers[ i ] );\n\t\t\t\t}\n\n\t\t\t\t// Callback\n\t\t\t\tcallback = function( type ) {\n\t\t\t\t\treturn function() {\n\t\t\t\t\t\tif ( callback ) {\n\t\t\t\t\t\t\tcallback = errorCallback = xhr.onload =\n\t\t\t\t\t\t\t\txhr.onerror = xhr.onabort = xhr.ontimeout =\n\t\t\t\t\t\t\t\t\txhr.onreadystatechange = null;\n\n\t\t\t\t\t\t\tif ( type === \"abort\" ) {\n\t\t\t\t\t\t\t\txhr.abort();\n\t\t\t\t\t\t\t} else if ( type === \"error\" ) {\n\n\t\t\t\t\t\t\t\t// Support: IE <=9 only\n\t\t\t\t\t\t\t\t// On a manual native abort, IE9 throws\n\t\t\t\t\t\t\t\t// errors on any property access that is not readyState\n\t\t\t\t\t\t\t\tif ( typeof xhr.status !== \"number\" ) {\n\t\t\t\t\t\t\t\t\tcomplete( 0, \"error\" );\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tcomplete(\n\n\t\t\t\t\t\t\t\t\t\t// File: protocol always yields status 0; see #8605, #14207\n\t\t\t\t\t\t\t\t\t\txhr.status,\n\t\t\t\t\t\t\t\t\t\txhr.statusText\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tcomplete(\n\t\t\t\t\t\t\t\t\txhrSuccessStatus[ xhr.status ] || xhr.status,\n\t\t\t\t\t\t\t\t\txhr.statusText,\n\n\t\t\t\t\t\t\t\t\t// Support: IE <=9 only\n\t\t\t\t\t\t\t\t\t// IE9 has no XHR2 but throws on binary (trac-11426)\n\t\t\t\t\t\t\t\t\t// For XHR2 non-text, let the caller handle it (gh-2498)\n\t\t\t\t\t\t\t\t\t( xhr.responseType || \"text\" ) !== \"text\"  ||\n\t\t\t\t\t\t\t\t\ttypeof xhr.responseText !== \"string\" ?\n\t\t\t\t\t\t\t\t\t\t{ binary: xhr.response } :\n\t\t\t\t\t\t\t\t\t\t{ text: xhr.responseText },\n\t\t\t\t\t\t\t\t\txhr.getAllResponseHeaders()\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t};\n\n\t\t\t\t// Listen to events\n\t\t\t\txhr.onload = callback();\n\t\t\t\terrorCallback = xhr.onerror = xhr.ontimeout = callback( \"error\" );\n\n\t\t\t\t// Support: IE 9 only\n\t\t\t\t// Use onreadystatechange to replace onabort\n\t\t\t\t// to handle uncaught aborts\n\t\t\t\tif ( xhr.onabort !== undefined ) {\n\t\t\t\t\txhr.onabort = errorCallback;\n\t\t\t\t} else {\n\t\t\t\t\txhr.onreadystatechange = function() {\n\n\t\t\t\t\t\t// Check readyState before timeout as it changes\n\t\t\t\t\t\tif ( xhr.readyState === 4 ) {\n\n\t\t\t\t\t\t\t// Allow onerror to be called first,\n\t\t\t\t\t\t\t// but that will not handle a native abort\n\t\t\t\t\t\t\t// Also, save errorCallback to a variable\n\t\t\t\t\t\t\t// as xhr.onerror cannot be accessed\n\t\t\t\t\t\t\twindow.setTimeout( function() {\n\t\t\t\t\t\t\t\tif ( callback ) {\n\t\t\t\t\t\t\t\t\terrorCallback();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} );\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\t// Create the abort callback\n\t\t\t\tcallback = callback( \"abort\" );\n\n\t\t\t\ttry {\n\n\t\t\t\t\t// Do send the request (this may raise an exception)\n\t\t\t\t\txhr.send( options.hasContent && options.data || null );\n\t\t\t\t} catch ( e ) {\n\n\t\t\t\t\t// #14683: Only rethrow if this hasn't been notified as an error yet\n\t\t\t\t\tif ( callback ) {\n\t\t\t\t\t\tthrow e;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tabort: function() {\n\t\t\t\tif ( callback ) {\n\t\t\t\t\tcallback();\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t}\n} );\n\n\n\n\n// Prevent auto-execution of scripts when no explicit dataType was provided (See gh-2432)\njQuery.ajaxPrefilter( function( s ) {\n\tif ( s.crossDomain ) {\n\t\ts.contents.script = false;\n\t}\n} );\n\n// Install script dataType\njQuery.ajaxSetup( {\n\taccepts: {\n\t\tscript: \"text/javascript, application/javascript, \" +\n\t\t\t\"application/ecmascript, application/x-ecmascript\"\n\t},\n\tcontents: {\n\t\tscript: /\\b(?:java|ecma)script\\b/\n\t},\n\tconverters: {\n\t\t\"text script\": function( text ) {\n\t\t\tjQuery.globalEval( text );\n\t\t\treturn text;\n\t\t}\n\t}\n} );\n\n// Handle cache's special case and crossDomain\njQuery.ajaxPrefilter( \"script\", function( s ) {\n\tif ( s.cache === undefined ) {\n\t\ts.cache = false;\n\t}\n\tif ( s.crossDomain ) {\n\t\ts.type = \"GET\";\n\t}\n} );\n\n// Bind script tag hack transport\njQuery.ajaxTransport( \"script\", function( s ) {\n\n\t// This transport only deals with cross domain or forced-by-attrs requests\n\tif ( s.crossDomain || s.scriptAttrs ) {\n\t\tvar script, callback;\n\t\treturn {\n\t\t\tsend: function( _, complete ) {\n\t\t\t\tscript = jQuery( \"<script>\" )\n\t\t\t\t\t.attr( s.scriptAttrs || {} )\n\t\t\t\t\t.prop( { charset: s.scriptCharset, src: s.url } )\n\t\t\t\t\t.on( \"load error\", callback = function( evt ) {\n\t\t\t\t\t\tscript.remove();\n\t\t\t\t\t\tcallback = null;\n\t\t\t\t\t\tif ( evt ) {\n\t\t\t\t\t\t\tcomplete( evt.type === \"error\" ? 404 : 200, evt.type );\n\t\t\t\t\t\t}\n\t\t\t\t\t} );\n\n\t\t\t\t// Use native DOM manipulation to avoid our domManip AJAX trickery\n\t\t\t\tdocument.head.appendChild( script[ 0 ] );\n\t\t\t},\n\t\t\tabort: function() {\n\t\t\t\tif ( callback ) {\n\t\t\t\t\tcallback();\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t}\n} );\n\n\n\n\nvar oldCallbacks = [],\n\trjsonp = /(=)\\?(?=&|$)|\\?\\?/;\n\n// Default jsonp settings\njQuery.ajaxSetup( {\n\tjsonp: \"callback\",\n\tjsonpCallback: function() {\n\t\tvar callback = oldCallbacks.pop() || ( jQuery.expando + \"_\" + ( nonce++ ) );\n\t\tthis[ callback ] = true;\n\t\treturn callback;\n\t}\n} );\n\n// Detect, normalize options and install callbacks for jsonp requests\njQuery.ajaxPrefilter( \"json jsonp\", function( s, originalSettings, jqXHR ) {\n\n\tvar callbackName, overwritten, responseContainer,\n\t\tjsonProp = s.jsonp !== false && ( rjsonp.test( s.url ) ?\n\t\t\t\"url\" :\n\t\t\ttypeof s.data === \"string\" &&\n\t\t\t\t( s.contentType || \"\" )\n\t\t\t\t\t.indexOf( \"application/x-www-form-urlencoded\" ) === 0 &&\n\t\t\t\trjsonp.test( s.data ) && \"data\"\n\t\t);\n\n\t// Handle iff the expected data type is \"jsonp\" or we have a parameter to set\n\tif ( jsonProp || s.dataTypes[ 0 ] === \"jsonp\" ) {\n\n\t\t// Get callback name, remembering preexisting value associated with it\n\t\tcallbackName = s.jsonpCallback = isFunction( s.jsonpCallback ) ?\n\t\t\ts.jsonpCallback() :\n\t\t\ts.jsonpCallback;\n\n\t\t// Insert callback into url or form data\n\t\tif ( jsonProp ) {\n\t\t\ts[ jsonProp ] = s[ jsonProp ].replace( rjsonp, \"$1\" + callbackName );\n\t\t} else if ( s.jsonp !== false ) {\n\t\t\ts.url += ( rquery.test( s.url ) ? \"&\" : \"?\" ) + s.jsonp + \"=\" + callbackName;\n\t\t}\n\n\t\t// Use data converter to retrieve json after script execution\n\t\ts.converters[ \"script json\" ] = function() {\n\t\t\tif ( !responseContainer ) {\n\t\t\t\tjQuery.error( callbackName + \" was not called\" );\n\t\t\t}\n\t\t\treturn responseContainer[ 0 ];\n\t\t};\n\n\t\t// Force json dataType\n\t\ts.dataTypes[ 0 ] = \"json\";\n\n\t\t// Install callback\n\t\toverwritten = window[ callbackName ];\n\t\twindow[ callbackName ] = function() {\n\t\t\tresponseContainer = arguments;\n\t\t};\n\n\t\t// Clean-up function (fires after converters)\n\t\tjqXHR.always( function() {\n\n\t\t\t// If previous value didn't exist - remove it\n\t\t\tif ( overwritten === undefined ) {\n\t\t\t\tjQuery( window ).removeProp( callbackName );\n\n\t\t\t// Otherwise restore preexisting value\n\t\t\t} else {\n\t\t\t\twindow[ callbackName ] = overwritten;\n\t\t\t}\n\n\t\t\t// Save back as free\n\t\t\tif ( s[ callbackName ] ) {\n\n\t\t\t\t// Make sure that re-using the options doesn't screw things around\n\t\t\t\ts.jsonpCallback = originalSettings.jsonpCallback;\n\n\t\t\t\t// Save the callback name for future use\n\t\t\t\toldCallbacks.push( callbackName );\n\t\t\t}\n\n\t\t\t// Call if it was a function and we have a response\n\t\t\tif ( responseContainer && isFunction( overwritten ) ) {\n\t\t\t\toverwritten( responseContainer[ 0 ] );\n\t\t\t}\n\n\t\t\tresponseContainer = overwritten = undefined;\n\t\t} );\n\n\t\t// Delegate to script\n\t\treturn \"script\";\n\t}\n} );\n\n\n\n\n// Support: Safari 8 only\n// In Safari 8 documents created via document.implementation.createHTMLDocument\n// collapse sibling forms: the second one becomes a child of the first one.\n// Because of that, this security measure has to be disabled in Safari 8.\n// https://bugs.webkit.org/show_bug.cgi?id=137337\nsupport.createHTMLDocument = ( function() {\n\tvar body = document.implementation.createHTMLDocument( \"\" ).body;\n\tbody.innerHTML = \"<form></form><form></form>\";\n\treturn body.childNodes.length === 2;\n} )();\n\n\n// Argument \"data\" should be string of html\n// context (optional): If specified, the fragment will be created in this context,\n// defaults to document\n// keepScripts (optional): If true, will include scripts passed in the html string\njQuery.parseHTML = function( data, context, keepScripts ) {\n\tif ( typeof data !== \"string\" ) {\n\t\treturn [];\n\t}\n\tif ( typeof context === \"boolean\" ) {\n\t\tkeepScripts = context;\n\t\tcontext = false;\n\t}\n\n\tvar base, parsed, scripts;\n\n\tif ( !context ) {\n\n\t\t// Stop scripts or inline event handlers from being executed immediately\n\t\t// by using document.implementation\n\t\tif ( support.createHTMLDocument ) {\n\t\t\tcontext = document.implementation.createHTMLDocument( \"\" );\n\n\t\t\t// Set the base href for the created document\n\t\t\t// so any parsed elements with URLs\n\t\t\t// are based on the document's URL (gh-2965)\n\t\t\tbase = context.createElement( \"base\" );\n\t\t\tbase.href = document.location.href;\n\t\t\tcontext.head.appendChild( base );\n\t\t} else {\n\t\t\tcontext = document;\n\t\t}\n\t}\n\n\tparsed = rsingleTag.exec( data );\n\tscripts = !keepScripts && [];\n\n\t// Single tag\n\tif ( parsed ) {\n\t\treturn [ context.createElement( parsed[ 1 ] ) ];\n\t}\n\n\tparsed = buildFragment( [ data ], context, scripts );\n\n\tif ( scripts && scripts.length ) {\n\t\tjQuery( scripts ).remove();\n\t}\n\n\treturn jQuery.merge( [], parsed.childNodes );\n};\n\n\n/**\n * Load a url into a page\n */\njQuery.fn.load = function( url, params, callback ) {\n\tvar selector, type, response,\n\t\tself = this,\n\t\toff = url.indexOf( \" \" );\n\n\tif ( off > -1 ) {\n\t\tselector = stripAndCollapse( url.slice( off ) );\n\t\turl = url.slice( 0, off );\n\t}\n\n\t// If it's a function\n\tif ( isFunction( params ) ) {\n\n\t\t// We assume that it's the callback\n\t\tcallback = params;\n\t\tparams = undefined;\n\n\t// Otherwise, build a param string\n\t} else if ( params && typeof params === \"object\" ) {\n\t\ttype = \"POST\";\n\t}\n\n\t// If we have elements to modify, make the request\n\tif ( self.length > 0 ) {\n\t\tjQuery.ajax( {\n\t\t\turl: url,\n\n\t\t\t// If \"type\" variable is undefined, then \"GET\" method will be used.\n\t\t\t// Make value of this field explicit since\n\t\t\t// user can override it through ajaxSetup method\n\t\t\ttype: type || \"GET\",\n\t\t\tdataType: \"html\",\n\t\t\tdata: params\n\t\t} ).done( function( responseText ) {\n\n\t\t\t// Save response for use in complete callback\n\t\t\tresponse = arguments;\n\n\t\t\tself.html( selector ?\n\n\t\t\t\t// If a selector was specified, locate the right elements in a dummy div\n\t\t\t\t// Exclude scripts to avoid IE 'Permission Denied' errors\n\t\t\t\tjQuery( \"<div>\" ).append( jQuery.parseHTML( responseText ) ).find( selector ) :\n\n\t\t\t\t// Otherwise use the full result\n\t\t\t\tresponseText );\n\n\t\t// If the request succeeds, this function gets \"data\", \"status\", \"jqXHR\"\n\t\t// but they are ignored because response was set above.\n\t\t// If it fails, this function gets \"jqXHR\", \"status\", \"error\"\n\t\t} ).always( callback && function( jqXHR, status ) {\n\t\t\tself.each( function() {\n\t\t\t\tcallback.apply( this, response || [ jqXHR.responseText, status, jqXHR ] );\n\t\t\t} );\n\t\t} );\n\t}\n\n\treturn this;\n};\n\n\n\n\n// Attach a bunch of functions for handling common AJAX events\njQuery.each( [\n\t\"ajaxStart\",\n\t\"ajaxStop\",\n\t\"ajaxComplete\",\n\t\"ajaxError\",\n\t\"ajaxSuccess\",\n\t\"ajaxSend\"\n], function( i, type ) {\n\tjQuery.fn[ type ] = function( fn ) {\n\t\treturn this.on( type, fn );\n\t};\n} );\n\n\n\n\njQuery.expr.pseudos.animated = function( elem ) {\n\treturn jQuery.grep( jQuery.timers, function( fn ) {\n\t\treturn elem === fn.elem;\n\t} ).length;\n};\n\n\n\n\njQuery.offset = {\n\tsetOffset: function( elem, options, i ) {\n\t\tvar curPosition, curLeft, curCSSTop, curTop, curOffset, curCSSLeft, calculatePosition,\n\t\t\tposition = jQuery.css( elem, \"position\" ),\n\t\t\tcurElem = jQuery( elem ),\n\t\t\tprops = {};\n\n\t\t// Set position first, in-case top/left are set even on static elem\n\t\tif ( position === \"static\" ) {\n\t\t\telem.style.position = \"relative\";\n\t\t}\n\n\t\tcurOffset = curElem.offset();\n\t\tcurCSSTop = jQuery.css( elem, \"top\" );\n\t\tcurCSSLeft = jQuery.css( elem, \"left\" );\n\t\tcalculatePosition = ( position === \"absolute\" || position === \"fixed\" ) &&\n\t\t\t( curCSSTop + curCSSLeft ).indexOf( \"auto\" ) > -1;\n\n\t\t// Need to be able to calculate position if either\n\t\t// top or left is auto and position is either absolute or fixed\n\t\tif ( calculatePosition ) {\n\t\t\tcurPosition = curElem.position();\n\t\t\tcurTop = curPosition.top;\n\t\t\tcurLeft = curPosition.left;\n\n\t\t} else {\n\t\t\tcurTop = parseFloat( curCSSTop ) || 0;\n\t\t\tcurLeft = parseFloat( curCSSLeft ) || 0;\n\t\t}\n\n\t\tif ( isFunction( options ) ) {\n\n\t\t\t// Use jQuery.extend here to allow modification of coordinates argument (gh-1848)\n\t\t\toptions = options.call( elem, i, jQuery.extend( {}, curOffset ) );\n\t\t}\n\n\t\tif ( options.top != null ) {\n\t\t\tprops.top = ( options.top - curOffset.top ) + curTop;\n\t\t}\n\t\tif ( options.left != null ) {\n\t\t\tprops.left = ( options.left - curOffset.left ) + curLeft;\n\t\t}\n\n\t\tif ( \"using\" in options ) {\n\t\t\toptions.using.call( elem, props );\n\n\t\t} else {\n\t\t\tcurElem.css( props );\n\t\t}\n\t}\n};\n\njQuery.fn.extend( {\n\n\t// offset() relates an element's border box to the document origin\n\toffset: function( options ) {\n\n\t\t// Preserve chaining for setter\n\t\tif ( arguments.length ) {\n\t\t\treturn options === undefined ?\n\t\t\t\tthis :\n\t\t\t\tthis.each( function( i ) {\n\t\t\t\t\tjQuery.offset.setOffset( this, options, i );\n\t\t\t\t} );\n\t\t}\n\n\t\tvar rect, win,\n\t\t\telem = this[ 0 ];\n\n\t\tif ( !elem ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Return zeros for disconnected and hidden (display: none) elements (gh-2310)\n\t\t// Support: IE <=11 only\n\t\t// Running getBoundingClientRect on a\n\t\t// disconnected node in IE throws an error\n\t\tif ( !elem.getClientRects().length ) {\n\t\t\treturn { top: 0, left: 0 };\n\t\t}\n\n\t\t// Get document-relative position by adding viewport scroll to viewport-relative gBCR\n\t\trect = elem.getBoundingClientRect();\n\t\twin = elem.ownerDocument.defaultView;\n\t\treturn {\n\t\t\ttop: rect.top + win.pageYOffset,\n\t\t\tleft: rect.left + win.pageXOffset\n\t\t};\n\t},\n\n\t// position() relates an element's margin box to its offset parent's padding box\n\t// This corresponds to the behavior of CSS absolute positioning\n\tposition: function() {\n\t\tif ( !this[ 0 ] ) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar offsetParent, offset, doc,\n\t\t\telem = this[ 0 ],\n\t\t\tparentOffset = { top: 0, left: 0 };\n\n\t\t// position:fixed elements are offset from the viewport, which itself always has zero offset\n\t\tif ( jQuery.css( elem, \"position\" ) === \"fixed\" ) {\n\n\t\t\t// Assume position:fixed implies availability of getBoundingClientRect\n\t\t\toffset = elem.getBoundingClientRect();\n\n\t\t} else {\n\t\t\toffset = this.offset();\n\n\t\t\t// Account for the *real* offset parent, which can be the document or its root element\n\t\t\t// when a statically positioned element is identified\n\t\t\tdoc = elem.ownerDocument;\n\t\t\toffsetParent = elem.offsetParent || doc.documentElement;\n\t\t\twhile ( offsetParent &&\n\t\t\t\t( offsetParent === doc.body || offsetParent === doc.documentElement ) &&\n\t\t\t\tjQuery.css( offsetParent, \"position\" ) === \"static\" ) {\n\n\t\t\t\toffsetParent = offsetParent.parentNode;\n\t\t\t}\n\t\t\tif ( offsetParent && offsetParent !== elem && offsetParent.nodeType === 1 ) {\n\n\t\t\t\t// Incorporate borders into its offset, since they are outside its content origin\n\t\t\t\tparentOffset = jQuery( offsetParent ).offset();\n\t\t\t\tparentOffset.top += jQuery.css( offsetParent, \"borderTopWidth\", true );\n\t\t\t\tparentOffset.left += jQuery.css( offsetParent, \"borderLeftWidth\", true );\n\t\t\t}\n\t\t}\n\n\t\t// Subtract parent offsets and element margins\n\t\treturn {\n\t\t\ttop: offset.top - parentOffset.top - jQuery.css( elem, \"marginTop\", true ),\n\t\t\tleft: offset.left - parentOffset.left - jQuery.css( elem, \"marginLeft\", true )\n\t\t};\n\t},\n\n\t// This method will return documentElement in the following cases:\n\t// 1) For the element inside the iframe without offsetParent, this method will return\n\t//    documentElement of the parent window\n\t// 2) For the hidden or detached element\n\t// 3) For body or html element, i.e. in case of the html node - it will return itself\n\t//\n\t// but those exceptions were never presented as a real life use-cases\n\t// and might be considered as more preferable results.\n\t//\n\t// This logic, however, is not guaranteed and can change at any point in the future\n\toffsetParent: function() {\n\t\treturn this.map( function() {\n\t\t\tvar offsetParent = this.offsetParent;\n\n\t\t\twhile ( offsetParent && jQuery.css( offsetParent, \"position\" ) === \"static\" ) {\n\t\t\t\toffsetParent = offsetParent.offsetParent;\n\t\t\t}\n\n\t\t\treturn offsetParent || documentElement;\n\t\t} );\n\t}\n} );\n\n// Create scrollLeft and scrollTop methods\njQuery.each( { scrollLeft: \"pageXOffset\", scrollTop: \"pageYOffset\" }, function( method, prop ) {\n\tvar top = \"pageYOffset\" === prop;\n\n\tjQuery.fn[ method ] = function( val ) {\n\t\treturn access( this, function( elem, method, val ) {\n\n\t\t\t// Coalesce documents and windows\n\t\t\tvar win;\n\t\t\tif ( isWindow( elem ) ) {\n\t\t\t\twin = elem;\n\t\t\t} else if ( elem.nodeType === 9 ) {\n\t\t\t\twin = elem.defaultView;\n\t\t\t}\n\n\t\t\tif ( val === undefined ) {\n\t\t\t\treturn win ? win[ prop ] : elem[ method ];\n\t\t\t}\n\n\t\t\tif ( win ) {\n\t\t\t\twin.scrollTo(\n\t\t\t\t\t!top ? val : win.pageXOffset,\n\t\t\t\t\ttop ? val : win.pageYOffset\n\t\t\t\t);\n\n\t\t\t} else {\n\t\t\t\telem[ method ] = val;\n\t\t\t}\n\t\t}, method, val, arguments.length );\n\t};\n} );\n\n// Support: Safari <=7 - 9.1, Chrome <=37 - 49\n// Add the top/left cssHooks using jQuery.fn.position\n// Webkit bug: https://bugs.webkit.org/show_bug.cgi?id=29084\n// Blink bug: https://bugs.chromium.org/p/chromium/issues/detail?id=589347\n// getComputedStyle returns percent when specified for top/left/bottom/right;\n// rather than make the css module depend on the offset module, just check for it here\njQuery.each( [ \"top\", \"left\" ], function( i, prop ) {\n\tjQuery.cssHooks[ prop ] = addGetHookIf( support.pixelPosition,\n\t\tfunction( elem, computed ) {\n\t\t\tif ( computed ) {\n\t\t\t\tcomputed = curCSS( elem, prop );\n\n\t\t\t\t// If curCSS returns percentage, fallback to offset\n\t\t\t\treturn rnumnonpx.test( computed ) ?\n\t\t\t\t\tjQuery( elem ).position()[ prop ] + \"px\" :\n\t\t\t\t\tcomputed;\n\t\t\t}\n\t\t}\n\t);\n} );\n\n\n// Create innerHeight, innerWidth, height, width, outerHeight and outerWidth methods\njQuery.each( { Height: \"height\", Width: \"width\" }, function( name, type ) {\n\tjQuery.each( { padding: \"inner\" + name, content: type, \"\": \"outer\" + name },\n\t\tfunction( defaultExtra, funcName ) {\n\n\t\t// Margin is only for outerHeight, outerWidth\n\t\tjQuery.fn[ funcName ] = function( margin, value ) {\n\t\t\tvar chainable = arguments.length && ( defaultExtra || typeof margin !== \"boolean\" ),\n\t\t\t\textra = defaultExtra || ( margin === true || value === true ? \"margin\" : \"border\" );\n\n\t\t\treturn access( this, function( elem, type, value ) {\n\t\t\t\tvar doc;\n\n\t\t\t\tif ( isWindow( elem ) ) {\n\n\t\t\t\t\t// $( window ).outerWidth/Height return w/h including scrollbars (gh-1729)\n\t\t\t\t\treturn funcName.indexOf( \"outer\" ) === 0 ?\n\t\t\t\t\t\telem[ \"inner\" + name ] :\n\t\t\t\t\t\telem.document.documentElement[ \"client\" + name ];\n\t\t\t\t}\n\n\t\t\t\t// Get document width or height\n\t\t\t\tif ( elem.nodeType === 9 ) {\n\t\t\t\t\tdoc = elem.documentElement;\n\n\t\t\t\t\t// Either scroll[Width/Height] or offset[Width/Height] or client[Width/Height],\n\t\t\t\t\t// whichever is greatest\n\t\t\t\t\treturn Math.max(\n\t\t\t\t\t\telem.body[ \"scroll\" + name ], doc[ \"scroll\" + name ],\n\t\t\t\t\t\telem.body[ \"offset\" + name ], doc[ \"offset\" + name ],\n\t\t\t\t\t\tdoc[ \"client\" + name ]\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\treturn value === undefined ?\n\n\t\t\t\t\t// Get width or height on the element, requesting but not forcing parseFloat\n\t\t\t\t\tjQuery.css( elem, type, extra ) :\n\n\t\t\t\t\t// Set width or height on the element\n\t\t\t\t\tjQuery.style( elem, type, value, extra );\n\t\t\t}, type, chainable ? margin : undefined, chainable );\n\t\t};\n\t} );\n} );\n\n\njQuery.each( ( \"blur focus focusin focusout resize scroll click dblclick \" +\n\t\"mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave \" +\n\t\"change select submit keydown keypress keyup contextmenu\" ).split( \" \" ),\n\tfunction( i, name ) {\n\n\t// Handle event binding\n\tjQuery.fn[ name ] = function( data, fn ) {\n\t\treturn arguments.length > 0 ?\n\t\t\tthis.on( name, null, data, fn ) :\n\t\t\tthis.trigger( name );\n\t};\n} );\n\njQuery.fn.extend( {\n\thover: function( fnOver, fnOut ) {\n\t\treturn this.mouseenter( fnOver ).mouseleave( fnOut || fnOver );\n\t}\n} );\n\n\n\n\njQuery.fn.extend( {\n\n\tbind: function( types, data, fn ) {\n\t\treturn this.on( types, null, data, fn );\n\t},\n\tunbind: function( types, fn ) {\n\t\treturn this.off( types, null, fn );\n\t},\n\n\tdelegate: function( selector, types, data, fn ) {\n\t\treturn this.on( types, selector, data, fn );\n\t},\n\tundelegate: function( selector, types, fn ) {\n\n\t\t// ( namespace ) or ( selector, types [, fn] )\n\t\treturn arguments.length === 1 ?\n\t\t\tthis.off( selector, \"**\" ) :\n\t\t\tthis.off( types, selector || \"**\", fn );\n\t}\n} );\n\n// Bind a function to a context, optionally partially applying any\n// arguments.\n// jQuery.proxy is deprecated to promote standards (specifically Function#bind)\n// However, it is not slated for removal any time soon\njQuery.proxy = function( fn, context ) {\n\tvar tmp, args, proxy;\n\n\tif ( typeof context === \"string\" ) {\n\t\ttmp = fn[ context ];\n\t\tcontext = fn;\n\t\tfn = tmp;\n\t}\n\n\t// Quick check to determine if target is callable, in the spec\n\t// this throws a TypeError, but we will just return undefined.\n\tif ( !isFunction( fn ) ) {\n\t\treturn undefined;\n\t}\n\n\t// Simulated bind\n\targs = slice.call( arguments, 2 );\n\tproxy = function() {\n\t\treturn fn.apply( context || this, args.concat( slice.call( arguments ) ) );\n\t};\n\n\t// Set the guid of unique handler to the same of original handler, so it can be removed\n\tproxy.guid = fn.guid = fn.guid || jQuery.guid++;\n\n\treturn proxy;\n};\n\njQuery.holdReady = function( hold ) {\n\tif ( hold ) {\n\t\tjQuery.readyWait++;\n\t} else {\n\t\tjQuery.ready( true );\n\t}\n};\njQuery.isArray = Array.isArray;\njQuery.parseJSON = JSON.parse;\njQuery.nodeName = nodeName;\njQuery.isFunction = isFunction;\njQuery.isWindow = isWindow;\njQuery.camelCase = camelCase;\njQuery.type = toType;\n\njQuery.now = Date.now;\n\njQuery.isNumeric = function( obj ) {\n\n\t// As of jQuery 3.0, isNumeric is limited to\n\t// strings and numbers (primitives or objects)\n\t// that can be coerced to finite numbers (gh-2662)\n\tvar type = jQuery.type( obj );\n\treturn ( type === \"number\" || type === \"string\" ) &&\n\n\t\t// parseFloat NaNs numeric-cast false positives (\"\")\n\t\t// ...but misinterprets leading-number strings, particularly hex literals (\"0x...\")\n\t\t// subtraction forces infinities to NaN\n\t\t!isNaN( obj - parseFloat( obj ) );\n};\n\n\n\n\n// Register as a named AMD module, since jQuery can be concatenated with other\n// files that may use define, but not via a proper concatenation script that\n// understands anonymous AMD modules. A named AMD is safest and most robust\n// way to register. Lowercase jquery is used because AMD module names are\n// derived from file names, and jQuery is normally delivered in a lowercase\n// file name. Do this after creating the global so that if an AMD module wants\n// to call noConflict to hide this version of jQuery, it will work.\n\n// Note that for maximum portability, libraries that are not jQuery should\n// declare themselves as anonymous modules, and avoid setting a global if an\n// AMD loader is present. jQuery is a special case. For more information, see\n// https://github.com/jrburke/requirejs/wiki/Updating-existing-libraries#wiki-anon\n\nif ( typeof define === \"function\" && define.amd ) {\n\tdefine( \"jquery\", [], function() {\n\t\treturn jQuery;\n\t} );\n}\n\n\n\n\nvar\n\n\t// Map over jQuery in case of overwrite\n\t_jQuery = window.jQuery,\n\n\t// Map over the $ in case of overwrite\n\t_$ = window.$;\n\njQuery.noConflict = function( deep ) {\n\tif ( window.$ === jQuery ) {\n\t\twindow.$ = _$;\n\t}\n\n\tif ( deep && window.jQuery === jQuery ) {\n\t\twindow.jQuery = _jQuery;\n\t}\n\n\treturn jQuery;\n};\n\n// Expose jQuery and $ identifiers, even in AMD\n// (#7102#comment:10, https://github.com/jquery/jquery/pull/557)\n// and CommonJS for browser emulators (#13566)\nif ( !noGlobal ) {\n\twindow.jQuery = window.$ = jQuery;\n}\n\n\n\n\nreturn jQuery;\n} );\n", "/**\n * This class is used for the logout page.\n *\n * It allows the user to start logout from all the services where a session exists (if any). Logout will be\n * triggered by loading an iframe where we send a SAML logout request to the SingleLogoutService endpoint of the\n * given SP. After successful response back from the SP, we will load a small template in the iframe that loads\n * this class again (IFrameLogoutHandler branch of the constructor), and sends a message to the main page\n * (core:logout-iframe branch).\n *\n * The iframes communicate the logout status for their corresponding association via an event message, for which the\n * main page is listening (the clearAssociation() method). Upon reception of a message, we'll check if there was an\n * error or not, and call the appropriate method (either completed() or failed()).\n */\nclass SimpleSAMLLogout {\n    constructor(page)\n    {\n        if (page === 'core:logout-iframe') { // main page\n            this.populateData();\n            if (Object.keys(this.sps).length === 0) {\n                // all SPs completed logout, this was a reload\n                this.btncontinue.click();\n            }\n            this.btnall.on('click', this.initLogout.bind(this));\n            window.addEventListener('message', this.clearAssociation.bind(this), false);\n        } else if (page === 'IFrameLogoutHandler') { // iframe\n            let data = $('i[id=\"data\"]');\n            let message = {\n                spId: $(data).data('spid')\n            };\n            if ($(data).data('error')) {\n                message.error = $(data).data('error');\n            }\n\n            window.parent.postMessage(JSON.stringify(message), SimpleSAMLLogout.getOrigin());\n        }\n    }\n\n\n    /**\n     * Clear an association when it is signaled from an iframe (either failed or completed).\n     *\n     * @param event The event containing the message from the iframe.\n     */\n    clearAssociation(event)\n    {\n        if (event.origin !== SimpleSAMLLogout.getOrigin()) {\n            // we don't accept events from other origins\n            return;\n        }\n        let data = JSON.parse(event.data);\n        if (typeof data.error === 'undefined') {\n            this.completed(data.spId);\n        } else {\n            this.failed(data.spId, data.error);\n        }\n\n        if (Object.keys(this.sps).length === 0) {\n            if (this.nfailed === 0) {\n                // all SPs successfully logged out, continue w/o user interaction\n                this.btncontinue.click();\n            }\n        }\n    }\n\n\n    /**\n     * Mark logout as completed for a given SP.\n     *\n     * This method will be called by the SimpleSAML\\IdP\\IFrameLogoutHandler class upon successful logout from the SP.\n     *\n     * @param id The ID of the SP that completed logout successfully.\n     */\n    completed(id)\n    {\n        if (typeof this.sps[id] === 'undefined') {\n            return;\n        }\n\n        this.sps[id].icon.removeClass('fa-spin');\n        this.sps[id].icon.removeClass('fa-circle-o-notch');\n        this.sps[id].icon.addClass('fa-check-circle');\n        this.sps[id].element.toggle();\n        delete this.sps[id];\n        this.finish();\n    }\n\n\n    /**\n     * Mark logout as failed for a given SP.\n     *\n     * This method will be called by the SimpleSAML\\IdP\\IFrameLogoutHandler class upon logout failure from the SP.\n     *\n     * @param id The ID of the SP that failed to complete logout.\n     * @param reason The reason why logout failed.\n     */\n    failed(id, reason)\n    {\n        if (typeof this.sps[id] === 'undefined') {\n            return;\n        }\n\n        this.sps[id].element.addClass('error');\n        $(this.sps[id].icon).removeClass('fa-spin fa-circle-o-notch');\n        $(this.sps[id].icon).addClass('fa-exclamation-circle');\n\n        if (this.errmsg.hasClass('hidden')) {\n            this.errmsg.removeClass('hidden');\n        }\n        if (this.errfrm.hasClass('hidden')) {\n            this.errfrm.removeClass('hidden');\n        }\n\n        delete this.sps[id];\n        this.nfailed++;\n        this.finish();\n    }\n\n\n    /**\n     * Finish the logout process, acting according to the current situation:\n     *\n     * - If there were failures, an error message is shown telling the user to close the browser.\n     * - If everything went ok, then we just continue back to the service that started logout.\n     *\n     * Note: this method won't do anything if there are SPs pending logout (e.g. waiting for the timeout).\n     */\n    finish()\n    {\n        if (Object.keys(this.sps).length > 0) { // pending services\n            return;\n        }\n\n        if (typeof this.timeout !== 'undefined') {\n            clearTimeout(this.timeout);\n        }\n\n        if (this.nfailed > 0) { // some services failed to log out\n            this.errmsg.removeClass('hidden');\n            this.errfrm.removeClass('hidden');\n            this.actions.addClass('hidden');\n        } else { // all services done\n            this.btncontinue.click();\n        }\n    }\n\n\n    /**\n     * Get the origin of the current page.\n     */\n    static getOrigin()\n    {\n        let origin = window.location.origin;\n        if (!origin) {\n            // IE < 11 does not support window.location.origin\n            origin = window.location.protocol + \"//\" + window.location.hostname +\n                (window.location.port ? ':' + window.location.port : '');\n        }\n        return origin;\n    }\n\n\n    /**\n     * This method starts logout on all SPs where we are currently logged in.\n     *\n     * @param event The click event on the \"Yes, all services\" button.\n     */\n    initLogout(event)\n    {\n        event.preventDefault();\n\n        this.btnall.prop('disabled', true);\n        this.btncancel.prop('disabled', true);\n        Object.keys(this.sps).forEach((function (id) {\n            this.sps[id].status = 'inprogress';\n            this.sps[id].startTime = (new Date()).getTime();\n            this.sps[id].iframe.attr('src', this.sps[id].iframe.data('url'));\n            this.sps[id].icon.addClass('fa-spin');\n        }).bind(this));\n        this.initTimeout();\n    }\n\n\n    /**\n     * Set timeouts for all logout operations.\n     *\n     * If an SP didn't reply by the timeout, we'll mark it as failed.\n     */\n    initTimeout()\n    {\n        let timeout = 10;\n\n        for (const id in this.sps) {\n            if (typeof id === 'undefined') {\n                continue;\n            }\n            if (!this.sps.hasOwnProperty(id)) {\n                continue;\n            }\n            if (this.sps[id].status !== 'inprogress') {\n                continue;\n            }\n            let now = ((new Date()).getTime() - this.sps[id].startTime) / 1000;\n\n            if (this.sps[id].timeout <= now) {\n                this.failed(id, 'Timed out', window.document);\n            } else {\n                // get the lowest timeout we have\n                if ((this.sps[id].timeout - now) < timeout) {\n                    timeout = this.sps[id].timeout - now;\n                }\n            }\n        }\n\n        if (Object.keys(this.sps).length > 0) {\n            // we have associations left, check them again as soon as one expires\n            this.timeout = setTimeout(this.initTimeout.bind(this), timeout * 1000);\n        } else {\n            this.finish();\n        }\n    }\n\n\n    /**\n     * This method populates the data we need from data-* properties in the page.\n     */\n    populateData()\n    {\n        this.sps = {};\n        this.btnall = $('button[id=\"btn-all\"]');\n        this.btncancel = $('button[id=\"btn-cancel\"]');\n        this.btncontinue = $('button[id=\"btn-continue\"]');\n        this.actions = $('div[id=\"original-actions\"]');\n        this.errmsg = $('div[id=\"error-message\"]');\n        this.errfrm = $('form[id=\"error-form\"]');\n        this.nfailed = 0;\n        let that = this;\n\n        // initialise SP status and timeout arrays\n        $('li[id^=\"sp-\"]').each(function () {\n            let id = $(this).data('id');\n            let iframe = $('iframe[id=\"iframe-' + id + '\"]');\n            let status = $(this).data('status');\n\n            switch (status) {\n                case 'failed':\n                    that.nfailed++;\n                case 'completed':\n                    return;\n            }\n\n            that.sps[id] = {\n                status: status,\n                timeout: $(this).data('timeout'),\n                element: $(this),\n                iframe: iframe,\n                icon: $('i[id=\"icon-' + id + '\"]'),\n            };\n        });\n    }\n}\n\nexport default SimpleSAMLLogout;\n", "import SimpleSAMLLogout from './logout.js';\n\n$(document).ready(function () {\n    new SimpleSAMLLogout($('body').attr('id'));\n});"], "sourceRoot": ""}