<?php

/*
 * This file defines "named" access control lists, which can
 * be reused in several places.
 */
$config = [
    'adminlist' => [
        //['allow', 'equals', 'mail', '<EMAIL>'],
        //['allow', 'has', 'groups', 'admin'],
        // The default action is to deny access.
    ],

    'example-simple' => [
        ['allow', 'equals', 'mail', '<EMAIL>'],
        ['allow', 'equals', 'mail', '<EMAIL>'],
        // The default action is to deny access.
    ],

    'example-deny-some' => [
        ['deny', 'equals', 'mail', '<EMAIL>'],
        ['allow'], // Allow everybody else.
    ],

    'example-maildomain' => [
        ['allow', 'equals-preg', 'mail', '/@example\.org$/'],
        // The default action is to deny access.
    ],

    'example-allow-employees' => [
        ['allow', 'has', 'eduPersonAffiliation', 'employee'],
        // The default action is to deny access.
    ],

    'example-allow-employees-not-students' => [
        ['deny', 'has', 'eduPersonAffiliation', 'student'],
        ['allow', 'has', 'eduPersonAffiliation', 'employee'],
        // The default action is to deny access.
    ],

    'example-deny-student-except-one' => [
        ['deny', 'and',
            ['has', 'eduPersonAffiliation', 'student'],
            ['not', 'equals', 'mail', '<EMAIL>'],
        ],
        ['allow'],
    ],

    'example-allow-or' => [
        ['allow', 'or',
            ['equals', 'eduPersonAffiliation', 'student', 'member'],
            ['equals', 'mail', '<EMAIL>'],
        ],
    ],

    'example-allow-all' => [
        ['allow'],
    ],
];
