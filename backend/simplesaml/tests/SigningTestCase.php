<?php

namespace SimpleSAML\Test;

use org\bovigo\vfs\vfsStream;
use PHPUnit\Framework\TestCase;
use SimpleSAML\Configuration;

/**
 * A test case that provides a certificate directory with public and private
 * keys.
 *
 * @package SimpleSAMLphp
 */
class SigningTestCase extends TestCase
{
    // openssl genrsa -out ca.key.pem 2048
    /** @var string $ca_private_key */
    protected $ca_private_key = <<<'NOWDOC'
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************NOWDOC;

    // openssl req -key ca.key.pem -new -x509 -days 3650 -out ca.cert.pem
    /** @var string $ca_certificate */
    private $ca_certificate = <<<'NOWDOC'
-----BEGIN CERTIFICATE-----
MIIDtjCCAp6gAwIBAgIJAII4rW68Q+IsMA0GCSqGSIb3DQEBCwUAMHAxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQxKTAnBgNVBAMMIEludGVybmV0IFdpZGdpdHMgUHR5IEx0
ZCBSb290IENBMB4XDTE3MTAxMTIxMjIzOFoXDTI3MTAwOTIxMjIzOFowcDELMAkG
A1UEBhMCQVUxEzARBgNVBAgMClNvbWUtU3RhdGUxITAfBgNVBAoMGEludGVybmV0
IFdpZGdpdHMgUHR5IEx0ZDEpMCcGA1UEAwwgSW50ZXJuZXQgV2lkZ2l0cyBQdHkg
THRkIFJvb3QgQ0EwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC2Pka6
+cLloKDxuGrZjLiKyn+5e1FFYqFsHlpg/v/UPDDBDVku0GiwEADlIwPizgv3D9ts
lVVUDOFz1yl7a8Ngt7V9pRmW87Mm/8m4f+7Hk1GSCbHaoGJzXMQS7Yq4JBG82C7i
QBtZFLpvHFBYm6EnLlkmldENk55vy3wkCfosvqt8x9KvAY/YhNoraWuq/M5qiS0e
Sy0ZU6DfKfkrXmUIpJIEjeqaVi1xTlWcv+RI9jYZ1rp4XeANIB+XKY2EeZoTHotk
zodY20l1SMatyK+PKekh2jsMx8vc5NdXy8zQB3gDnl7HTTbQKf+BcJKHAiz2VSua
NTNf8MnRFzi9XMZXAgMBAAGjUzBRMB0GA1UdDgQWBBQjqR1+FXBhfbKUUMfdjHp/
9fMvPTAfBgNVHSMEGDAWgBQjqR1+FXBhfbKUUMfdjHp/9fMvPTAPBgNVHRMBAf8E
BTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQAuUyMn7wz8RUAjW5cbOTvLejYmaPKf
EzWMYhcRmCQcmqZJ3Sxy+VEBCZsHG+a5R0rXsQ1Iwrgpo7H4d5+CRS6rJcrKAKC+
1Izaolodnfbz1sQlmHxwkSwDqdb4pWujw7L0YBfvsUc5FGoKfdPUoa6qL/eP1pVH
0d9JC1ucX+0EmTX9a+3LH0t3evPP2yx53SjQiMoRf/ty7NwfIVxlqWyKFJnUYSF5
c2jGmls/F+PBVeW51bfK00DpdXLgbgWmNDdePf2fPvpkADGfo/DxLZOTtiY6ngtO
BdyrA5DmvSuL/Yfq03J9btXX4NnANQFVvfSbun7ts5F1qTkSe/vHCoke
-----END CERTIFICATE-----
NOWDOC;

    // openssl genrsa -out good.key.pem 2048
    /** @var string $good_private_key */
    protected $good_private_key = <<<'NOWDOC'
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************NOWDOC;

    // openssl req -key good.key.pem -new -out good.csr.pem
    // openssl x509 \
    //      -req \
    //      -CA ca.cert.pem \
    //      -CAkey ca.key.pem \
    //      -CAcreateserial \
    //      -days 3650 \
    //      -in good.csr.pem \
    //      -out good.cert.pem
    /** @var string $good_certificate */
    protected $good_certificate = <<<'NOWDOC'
-----BEGIN CERTIFICATE-----
MIIDZTCCAk0CCQC+sxqJmyko6TANBgkqhkiG9w0BAQsFADBwMQswCQYDVQQGEwJB
VTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50ZXJuZXQgV2lkZ2l0
cyBQdHkgTHRkMSkwJwYDVQQDDCBJbnRlcm5ldCBXaWRnaXRzIFB0eSBMdGQgUm9v
dCBDQTAeFw0xNzEwMTEyMTIzMTRaFw0yNzEwMDkyMTIzMTRaMHkxCzAJBgNVBAYT
AkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBXaWRn
aXRzIFB0eSBMdGQxMjAwBgNVBAMMKUludGVybmV0IFdpZGdpdHMgUHR5IEx0ZCBU
ZXN0IENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA
qmNn4bt/jrMHgoWtwXLc2ok17BHh1O5ETbn9rK3KFjk3BXp53aGveill+KbW7Sgr
iGZSa1KBE2uaQy2mZpiBQqFrLcgKhtzaCNLyBvKOozQhn/XN6m2kN8EDZaGIGxtM
/6ypUAnytscGo2bKzyHtOjYOPwEeALiq7+YrR1Bc2X05OyVudV8Wju8QUCm7No85
/TOjxD6SrWUXuEPJm0RiyVMeZhuKmtxm0kB2ZtQ0lKViOxaLiBRiW9TldY94NaHf
gaZSpCmrikoiS4QJ4hTo4nEVpjx+1BDJIar3bfxH+vwuLlOoZg3KI9BYcWm5n+XK
wTxnhaBWM8MH3PtmLNbrRwIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAyWgO1+gyu
3ao9Om0/TaAgJzsb2dnrb91P4eLo285bPToOGekaJyP5up6xP6DsOnvPCkXIglld
PR8LyCWjHhIFL7bZod7cmXvBhedX7yxP9nwDwOvz9e9M117cVXfUQqZVktLiDxmg
FxNHi6lMlYtvvnHnjnjYtA2w7c0u0SBeqhXfctZxrzqP97BzUAQkk75ElDJM6lNw
FTVvRw8z7um+jeruCa6FcUVBxkKcUNvo3p6C2m+bntkqmMZji1YZ7j0kC/tnjr95
hQc0xnrLQ255SjMn+nQtMkVSuKwAUqaAP1ByyiVbN1cBlHnMiJCjvBI58bSTdlVK
0ZppWlc39T6m
-----END CERTIFICATE-----
NOWDOC;

    /** @var string */
    protected $good_private_key_file;

    /** @var string */
    protected $good_certificate_file;

    /** @var string */
    protected $certdir;

    /** @var \org\bovigo\vfs\vfsStreamDirectory */
    protected $root;

    /** @var string */
    protected $root_directory;

    /** @var string */
    protected $ca_private_key_file;

    /** @var string */
    protected $ca_certificate_file;

    /** @var \SimpleSAML\Configuration */
    protected $config;

    const ROOTDIRNAME = 'testdir';
    const DEFAULTCERTDIR = 'certdir';
    const CA_PRIVATE_KEY = 'ca.key.pem';
    const CA_CERTIFICATE = 'ca.cert.pem';
    const GOOD_PRIVATE_KEY = 'good.key.pem';
    const GOOD_CERTIFICATE = 'good.cert.pem';


    /**
     * @return array
     */
    public function getCertDirContent()
    {
        return [
            self::CA_PRIVATE_KEY => $this->ca_private_key,
            self::CA_CERTIFICATE => $this->ca_certificate,
            self::GOOD_PRIVATE_KEY => $this->good_private_key,
            self::GOOD_CERTIFICATE => $this->good_certificate,
        ];
    }


    /**
     * @return void
     */
    public function setUp()
    {
        $this->root = vfsStream::setup(
            self::ROOTDIRNAME,
            null,
            [
                self::DEFAULTCERTDIR => $this->getCertDirContent(),
            ]
        );
        $this->root_directory = vfsStream::url(self::ROOTDIRNAME);

        $this->certdir = $this->root_directory . DIRECTORY_SEPARATOR . self::DEFAULTCERTDIR;
        $this->ca_private_key_file = $this->certdir . DIRECTORY_SEPARATOR . self::CA_PRIVATE_KEY;
        $this->ca_certificate_file = $this->certdir . DIRECTORY_SEPARATOR . self::CA_CERTIFICATE;
        $this->good_private_key_file = $this->certdir . DIRECTORY_SEPARATOR . self::GOOD_PRIVATE_KEY;
        $this->good_certificate_file = $this->certdir . DIRECTORY_SEPARATOR . self::GOOD_CERTIFICATE;

        $this->config = \SimpleSAML\Configuration::loadFromArray([
            'certdir' => $this->certdir,
        ], '[ARRAY]', 'simplesaml');
    }


    /**
     * @return void
     */
    public function tearDown()
    {
        $this->clearInstance($this->config, '\SimpleSAML\Configuration', []);
    }


    /**
     * @param \SimpleSAML\Configuration $service
     * @param string $className
     * @param mixed|null $value
     * @return void
     */
    protected function clearInstance(Configuration $service, $className, $value = null)
    {
        $reflectedClass = new \ReflectionClass($className);
        $reflectedInstance = $reflectedClass->getProperty('instance');
        $reflectedInstance->setAccessible(true);
        $reflectedInstance->setValue($service, $value);
        $reflectedInstance->setAccessible(false);
    }
}
