a:5:{s:8:"entityid";s:19:"expiredInSrc1InSrc2";s:6:"expire";i:3659688740;s:4:"name";a:1:{s:2:"en";s:35:"expiredInSrc1InSrc2 SP from source2";}s:12:"metadata-set";s:16:"saml20-sp-remote";s:24:"AssertionConsumerService";a:1:{i:0;a:4:{s:7:"Binding";s:46:"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST";s:8:"Location";s:65:"https://expiredInSrc1InSrc2.example.org/Shibboleth.sso/SAML2/POST";s:5:"index";i:1;s:9:"isDefault";b:1;}}}
