<?php

namespace SimpleSAML\Test\Module\core\Auth\Process;

use PHPUnit\Framework\TestCase;

/**
 * Test for the core:ScopeAttribute filter.
 */
class ScopeAttributeTest extends TestCase
{
    /**
     * Helper function to run the filter with a given configuration.
     *
     * @param array $config  The filter configuration.
     * @param array $request  The request state.
     * @return array  The state array after processing.
     */
    private static function processFilter(array $config, array $request)
    {
        $filter = new \SimpleSAML\Module\core\Auth\Process\ScopeAttribute($config, null);
        $filter->process($request);
        return $request;
    }


    /**
     * Test the most basic functionality.
     * @return void
     */
    public function testBasic()
    {
        $config = [
            'scopeAttribute' => 'eduPersonPrincipalName',
            'sourceAttribute' => 'eduPersonAffiliation',
            'targetAttribute' => 'eduPersonScopedAffiliation',
        ];
        $request = [
            'Attributes' => [
                'eduPersonPrincipalName' => ['<EMAIL>'],
                'eduPersonAffiliation' => ['member'],
            ]
        ];
        $result = self::processFilter($config, $request);
        $attributes = $result['Attributes'];
        $this->assertArrayHasKey('eduPersonScopedAffiliation', $attributes);
        $this->assertEquals($attributes['eduPersonScopedAffiliation'], ['<EMAIL>']);
    }


    /**
     * If target attribute already set, module must add, not overwrite.
     * @return void
     */
    public function testNoOverwrite()
    {
        $config = [
            'scopeAttribute' => 'eduPersonPrincipalName',
            'sourceAttribute' => 'eduPersonAffiliation',
            'targetAttribute' => 'eduPersonScopedAffiliation',
        ];
        $request = [
            'Attributes' => [
                'eduPersonPrincipalName' => ['<EMAIL>'],
                'eduPersonAffiliation' => ['member'],
                'eduPersonScopedAffiliation' => ['<EMAIL>'],
            ]
        ];
        $result = self::processFilter($config, $request);
        $attributes = $result['Attributes'];
        $this->assertEquals(
            $attributes['eduPersonScopedAffiliation'],
            ['<EMAIL>', '<EMAIL>']
        );
    }


    /**
     * If same scope already set, module must do nothing, not duplicate value.
     * @return void
     */
    public function testNoDuplication()
    {
        $config = [
            'scopeAttribute' => 'eduPersonPrincipalName',
            'sourceAttribute' => 'eduPersonAffiliation',
            'targetAttribute' => 'eduPersonScopedAffiliation',
        ];
        $request = [
            'Attributes' => [
                'eduPersonPrincipalName' => ['<EMAIL>'],
                'eduPersonAffiliation' => ['member'],
                'eduPersonScopedAffiliation' => ['<EMAIL>'],
            ]
        ];
        $result = self::processFilter($config, $request);
        $attributes = $result['Attributes'];
        $this->assertEquals($attributes['eduPersonScopedAffiliation'], ['<EMAIL>']);
    }


    /**
     * If source attribute not set, nothing happens
     * @return void
     */
    public function testNoSourceAttribute()
    {
        $config = [
            'scopeAttribute' => 'eduPersonPrincipalName',
            'sourceAttribute' => 'eduPersonAffiliation',
            'targetAttribute' => 'eduPersonScopedAffiliation',
        ];
        $request = [
            'Attributes' => [
                'mail' => ['<EMAIL>', '<EMAIL>'],
                'eduPersonAffiliation' => ['member'],
                'eduPersonScopedAffiliation' => ['<EMAIL>'],
            ]
        ];
        $result = self::processFilter($config, $request);
        $this->assertEquals($request['Attributes'], $result['Attributes']);
    }


    /**
     * If scope attribute not set, nothing happens
     * @return void
     */
    public function testNoScopeAttribute()
    {
        $config = [
            'scopeAttribute' => 'eduPersonPrincipalName',
            'sourceAttribute' => 'eduPersonAffiliation',
            'targetAttribute' => 'eduPersonScopedAffiliation',
        ];
        $request = [
            'Attributes' => [
                'mail' => ['<EMAIL>', '<EMAIL>'],
                'eduPersonScopedAffiliation' => ['<EMAIL>'],
                'eduPersonPrincipalName' => ['<EMAIL>'],
            ]
        ];
        $result = self::processFilter($config, $request);
        $this->assertEquals($request['Attributes'], $result['Attributes']);
    }


    /**
     * When multiple @ signs in attribute, will use the first one.
     * @return void
     */
    public function testMultiAt()
    {
        $config = [
            'scopeAttribute' => 'eduPersonPrincipalName',
            'sourceAttribute' => 'eduPersonAffiliation',
            'targetAttribute' => 'eduPersonScopedAffiliation',
        ];
        $request = [
            'Attributes' => [
                'eduPersonPrincipalName' => ['john@<EMAIL>'],
                'eduPersonAffiliation' => ['member'],
            ]
        ];
        $result = self::processFilter($config, $request);
        $attributes = $result['Attributes'];
        $this->assertEquals($attributes['eduPersonScopedAffiliation'], ['member@<EMAIL>']);
    }


    /**
     * When multiple values in source attribute, should render multiple targets.
     * @return void
     */
    public function testMultivaluedSource()
    {
        $config = [
            'scopeAttribute' => 'eduPersonPrincipalName',
            'sourceAttribute' => 'eduPersonAffiliation',
            'targetAttribute' => 'eduPersonScopedAffiliation',
        ];
        $request = [
            'Attributes' => [
                'eduPersonPrincipalName' => ['<EMAIL>'],
                'eduPersonAffiliation' => ['member', 'staff', 'faculty'],
            ]
        ];
        $result = self::processFilter($config, $request);
        $attributes = $result['Attributes'];
        $this->assertEquals(
            $attributes['eduPersonScopedAffiliation'],
            ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        );
    }


    /**
     * When the source attribute doesn't have a scope, the entire value is used.
     * @return void
     */
    public function testNoAt()
    {
        $config = [
            'scopeAttribute' => 'schacHomeOrganization',
            'sourceAttribute' => 'eduPersonAffiliation',
            'targetAttribute' => 'eduPersonScopedAffiliation',
        ];
        $request = [
            'Attributes' => [
                'schacHomeOrganization' => ['example.org'],
                'eduPersonAffiliation' => ['student'],
            ]
        ];
        $result = self::processFilter($config, $request);
        $attributes = $result['Attributes'];
        $this->assertEquals($attributes['eduPersonScopedAffiliation'], ['<EMAIL>']);
    }


    /**
     * When the target attribute exists and onlyIfEmpty is set
     * @return void
     */
    public function testOnlyIfEmpty()
    {
        $config = [
            'scopeAttribute' => 'schacHomeOrganization',
            'sourceAttribute' => 'eduPersonAffiliation',
            'targetAttribute' => 'eduPersonScopedAffiliation',
            'onlyIfEmpty' => true,
        ];
        $request = [
            'Attributes' => [
                'schacHomeOrganization' => ['example.org'],
                'eduPersonAffiliation' => ['student'],
                'eduPersonScopedAffiliation' => ['<EMAIL>', '<EMAIL>'],
            ]
        ];
        $result = self::processFilter($config, $request);
        $attributes = $result['Attributes'];
        $this->assertEquals($attributes['eduPersonScopedAffiliation'], ['<EMAIL>', '<EMAIL>']);
    }
}
