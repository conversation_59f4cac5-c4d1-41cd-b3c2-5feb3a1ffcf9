
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:31+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: sl\n"
"Language-Team: \n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 "
"|| n%100==4 ? 2 : 3)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{admin:metadata_saml20-idp}"
msgstr "SAML 2.0 IdP Metapodatki"

msgid "{errors:descr_WRONGUSERPASS}"
msgstr ""
"Uporabnika s tem uporabniškim imenom ni bilo mogoče najti ali pa je "
"vpisano geslo napačno. Preverite svoje uporabniško ime in poskusite "
"znova."

msgid "{logout:failed}"
msgstr "Odjava je spodletela."

msgid "{status:attributes_header}"
msgstr "Vaši atributi"

msgid "{admin:metaover_group_metadata.saml20-sp-remote}"
msgstr "SAML 2.0 SP (Oddaljeni)"

msgid "{errors:descr_NOCERT}"
msgstr ""
"Avtentikacija je spodletela: vaš spletni brskalnik ni posredoval "
"digitalnega potrdila!"

msgid "{errors:title_PROCESSASSERTION}"
msgstr "Pri obdelavi odgovora IdP-ja je prišlo do napake"

msgid "{errors:title_NOSTATE}"
msgstr "Podatki o stanju so izgubljeni"

msgid "{login:username}"
msgstr "Uporabniško ime"

msgid "{errors:title_METADATA}"
msgstr "Napaka pri nalaganju metapodatkov"

msgid "{admin:metaconv_title}"
msgstr "Metapodatkovna sintaktična analiza (parser)"

msgid "{admin:cfg_check_noerrors}"
msgstr "Ni napak"

msgid "{errors:descr_LOGOUTINFOLOST}"
msgstr ""
"Odjavni (Logout) parametri niso na voljo. Vrnite se na storitev, ki ste "
"jo pravkar uporabljali in se ponovno poskusite odjaviti. Napaka je "
"posledica poteka veljavnosti seje."

msgid "{disco:previous_auth}"
msgstr "Predhodnje ste se prijavljali že pri"

msgid "{admin:cfg_check_back}"
msgstr "Vrnite se na seznam datotek"

msgid "{errors:report_trackid}"
msgstr ""
"Če boste prijavili to napako, priložite tudi ID seje, preko katere bo "
"lažje najti vaše zapise v dnevniških datotekah, ki so na voljo skrbniku "
"sistema."

msgid "{login:change_home_org_title}"
msgstr "Izberite vašo domačo organizacijo."

msgid "{errors:descr_METADATANOTFOUND}"
msgstr "Metapodatkov za %ENTITYID% ni bilo moč najti"

msgid "{admin:metadata_metadata}"
msgstr "Metapodatki"

msgid "{errors:report_text}"
msgstr ""
"Če želite, vnesite elektronski naslov, na katerem boste dosegljivi v "
"primeru dodatnih vprašanj za skrbnika sistema :"

msgid "{errors:report_header}"
msgstr "Prijavi napake"

msgid "{login:change_home_org_text}"
msgstr ""
"Izbrali ste <b>%HOMEORG%</b> kot vašo domačo organizacijo. V primeru da "
"je izbor napačen, izberite drugo."

msgid "{errors:title_PROCESSAUTHNREQUEST}"
msgstr "Napaka pri obdelavi zahteve SP"

msgid "{errors:descr_PROCESSASSERTION}"
msgstr "Odgovor, poslan od IdP-ja, ni bil sprejet."

msgid "{errors:debuginfo_header}"
msgstr "Pomoč pri odpravljanju napak (debug)"

msgid "{admin:debug_sending_message_msg_text}"
msgstr "Ste v debug načinu, lahko si ogledate vsebino sporočila, ki ga pošiljate"

msgid "{errors:descr_RESPONSESTATUSNOSUCCESS}"
msgstr "Odziv IdP vsebuje napako (\"SAML-Response\" ni uspel)! "

msgid "{admin:metadata_shib13-idp}"
msgstr "Shib 1.3 IdP Metapodatki"

msgid "{login:help_text}"
msgstr ""
"Žal se brez uporabniškega imena in gesla ne morete prijaviti in "
"uporabljati storitev."

msgid "{logout:default_link_text}"
msgstr "Nazaj na namestitveno stran SimpleSAMLphp"

msgid "{errors:error_header}"
msgstr "SimpleSAMLphp napaka"

msgid "{login:help_header}"
msgstr "Na pomoč! Pozabil sem svoje geslo."

msgid "{errors:descr_LDAPERROR}"
msgstr ""
"LDAP je zbirka uporabnikov. Ko se želite prijaviti, je potrebno prijavo "
"preveriti v LDAPu. Pri trenutnem preverjanju je prišlo do napake."

msgid "{errors:descr_METADATA}"
msgstr ""
"V konfiguraciji SimpleSAMLphp-ja je napaka. Če ste skrbnik te storitve, "
"preverite, da je konfiguracija metapodatkov pravilna."

msgid "{errors:title_BADREQUEST}"
msgstr "Napaka v prejetem zahtevku."

msgid "{status:sessionsize}"
msgstr "Velikost seje: %SIZE% bajtov"

msgid "{logout:title}"
msgstr "Odjavljen"

msgid "{admin:metaconv_xmlmetadata}"
msgstr "XML metapodatki"

msgid "{admin:metaover_unknown_found}"
msgstr "Nepoznana polja"

msgid "{errors:title_AUTHSOURCEERROR}"
msgstr "Napaka v avtentikacijskem viru"

msgid "{login:select_home_org}"
msgstr "Izberite vašo domačo organizacijo"

msgid "{logout:hold}"
msgstr "V teku"

msgid "{admin:cfg_check_header}"
msgstr "Preverjanje konfiguracije"

msgid "{admin:debug_sending_message_send}"
msgstr "Pošlji sporočilo"

msgid "{status:logout}"
msgstr "Odjava"

msgid "{errors:descr_DISCOPARAMS}"
msgstr ""
"Parametri, ki so bili poslani \"Discovery service-u\", ne ustrezajo "
"specifikaciji."

msgid "{errors:descr_CREATEREQUEST}"
msgstr "Pri ustvarjanju SAML zahteve je prišlo do napake."

msgid "{admin:metaover_optional_found}"
msgstr "Neobvezna polja"

msgid "{logout:return}"
msgstr "Vrni se nazaj na storitev."

msgid "{admin:metadata_xmlurl}"
msgstr "XML metapodatki se nahajajo na <a href=\"%METAURL%\">tem naslovu</a>:"

msgid "{logout:logout_all}"
msgstr "Da, odjavi me z vseh storitev"

msgid "{admin:debug_disable_debug_mode}"
msgstr ""
"Debug način lahko izklopite v globalni SimpleSAMLphp konfiguracijski "
"datoteki <tt>config/config.php</tt>."

msgid "{disco:select}"
msgstr "Izberite"

msgid "{logout:also_from}"
msgstr "Prijavljeni ste v naslednje storitve:"

msgid "{login:login_button}"
msgstr "Prijava"

msgid "{logout:progress}"
msgstr "Odjavljanje..."

msgid "{login:error_wrongpassword}"
msgstr "Napačno uporabniško ime ali geslo!"

msgid "{admin:metaover_group_metadata.shib13-sp-remote}"
msgstr "Shib 1.3 SP (Oddaljeni)"

msgid "{errors:descr_PROCESSAUTHNREQUEST}"
msgstr ""
"IdP je prejel avtenticirano zahtevo SP-ja, vendar je prišlo do napake pri"
" obdelavi te zahteve."

msgid "{logout:logout_all_question}"
msgstr "Ali se želite odjaviti z vseh naštetih storitev?"

msgid "{errors:title_NOACCESS}"
msgstr "Ni dostopa"

msgid "{login:error_nopassword}"
msgstr "Prišlo je do napake, poskusite znova."

msgid "{errors:title_NORELAYSTATE}"
msgstr "RelayState parameter ne obstaja"

msgid "{errors:descr_NOSTATE}"
msgstr ""
"Podatki o stanju so izgubljeni, zato zahteve ni mogoče obnoviti/ponovno "
"zagnati."

msgid "{login:password}"
msgstr "Geslo"

msgid "{errors:debuginfo_text}"
msgstr "Podatki o odpravljanju napak bodo zanimali skrbnika/helpdesk:"

msgid "{admin:cfg_check_missing}"
msgstr "V konfiguracijski datoteki manjkajo nastavitve"

msgid "{errors:descr_UNHANDLEDEXCEPTION}"
msgstr "Zagnana je bila nedefinirana izjema."

msgid "{general:yes}"
msgstr "Da"

msgid "{errors:title_CONFIG}"
msgstr "Napaka v nastavitvah"

msgid "{errors:title_LOGOUTREQUEST}"
msgstr "Napaka pri obdelavi zahteve za odjavo"

msgid "{admin:metaover_errorentry}"
msgstr "Napaka pri vnosu metapodatkov"

msgid "{errors:title_METADATANOTFOUND}"
msgstr "Metapodatkov ni bilo moč najti"

msgid "{login:contact_info}"
msgstr "Kontakt"

msgid "{errors:title_UNHANDLEDEXCEPTION}"
msgstr "Nedefinirana izjema."

msgid "{status:header_saml20_sp}"
msgstr "SAML 2.0 SP Demo primer"

msgid "{login:error_header}"
msgstr "Napaka"

msgid "{errors:title_USERABORTED}"
msgstr "Avtentikacija prekinjena"

msgid "{logout:incapablesps}"
msgstr ""
"Ena ali več storitev, v katere ste prijavljeni, <i>ne omogoča odjave</i>."
" Odjavo iz teh storitev izvedete tako, da <i>zaprete spletni "
"brskalnik</i>."

msgid "{admin:metadata_xmlformat}"
msgstr "V SAML 2.0 Metapodatkovni XML format:"

msgid "{admin:metaover_group_metadata.saml20-idp-remote}"
msgstr "SAML 2.0 IdP (Oddaljeni)"

msgid "{admin:metaover_group_metadata.saml20-idp-hosted}"
msgstr "SAML 2.0 IdP (Lokalni)"

msgid "{admin:metaover_required_found}"
msgstr "Zahtevana polja"

msgid "{admin:cfg_check_select_file}"
msgstr "Izberite konfiguracijsko datoteko, ki jo želite preveriti"

msgid "{errors:descr_UNKNOWNCERT}"
msgstr ""
"Avtentikacija je spodletela: vaš spletni brskalnik je posredoval "
"nepoznano digitalno potrdilo"

msgid "{logout:logging_out_from}"
msgstr "Odjava iz naslednjih storitev:"

msgid "{logout:loggedoutfrom}"
msgstr "Uspešno ste se odjavili s ponudnika storitev: %SP%"

msgid "{errors:errorreport_text}"
msgstr "Poročilo o napaki je bilo poslano skrbnikom sistema."

msgid "{errors:descr_LOGOUTREQUEST}"
msgstr "Pri obdelavi zahteve za odjavo je prišlo do napake."

msgid "{logout:success}"
msgstr "Uspešno ste se odjavili z vseh naštetih storitev."

msgid "{admin:cfg_check_notices}"
msgstr "Obvestila"

msgid "{errors:descr_USERABORTED}"
msgstr "Avtentikacija prekinjena na zahtevo uporabnika"

msgid "{errors:descr_CASERROR}"
msgstr "Napaka pri komunikaciji s CAS strežnikom."

msgid "{general:no}"
msgstr "Ne"

msgid "{admin:metadata_saml20-sp}"
msgstr "SAML 2.0 SP Metapodatki"

msgid "{admin:metaconv_converted}"
msgstr "Pretvorjeni metapodatki"

msgid "{logout:completed}"
msgstr "Dokončano"

msgid "{errors:descr_NOTSET}"
msgstr ""
"V nastavitvah je geslo skrbnika (auth.adminpassword) še vedno nastavljeno"
" na začetno vrednost. Spremenite ga!"

msgid "{general:service_provider}"
msgstr "Ponudnik storitev"

msgid "{errors:descr_BADREQUEST}"
msgstr "Prišlo je do napake pri prejetem zahtevku. Razlog: %REASON%"

msgid "{logout:no}"
msgstr "Ne"

msgid "{disco:icon_prefered_idp}"
msgstr "Prioritetna izbira"

msgid "{general:no_cancel}"
msgstr "Ne, prekliči"

msgid "{login:user_pass_header}"
msgstr "Vnesite svoje uporabniško ime in geslo"

msgid "{errors:report_explain}"
msgstr "Opišite, kako je prišlo do napake..."

msgid "{errors:title_ACSPARAMS}"
msgstr "Nobenega odgovora za SAML ni na voljo"

msgid "{errors:descr_SLOSERVICEPARAMS}"
msgstr ""
"Dostopili ste do SingleLogoutService vmesnika, ampak niste zagotovili "
"SAML LogoutRequest ali LogoutResponse."

msgid "{login:organization}"
msgstr "Organizacija"

msgid "{errors:title_WRONGUSERPASS}"
msgstr "Napačno uporabniško ime ali geslo"

msgid "{admin:metaover_required_not_found}"
msgstr "Naslednjih zahtevanih polj ni bilo mogoče najti"

msgid "{errors:descr_NOACCESS}"
msgstr ""
"Ta končna točka ni omogočena. Preverite možnost omogočanja storitve v "
"konfiguraciji SimpleSAMLphp-ja."

msgid "{errors:title_SLOSERVICEPARAMS}"
msgstr "SAML sporočilo ni na voljo"

msgid "{errors:descr_ACSPARAMS}"
msgstr ""
"Dostopili ste do \"Assertion Consumer Service\" vmesnika, ampak niste "
"zagotovili \"SAML Authentication Responsa\"."

msgid "{admin:debug_sending_message_text_link}"
msgstr "Sporočilo boste poslali s klikom na gumb za pošiljanje."

msgid "{errors:descr_AUTHSOURCEERROR}"
msgstr "Napaka v avtentikacijskem viru: %AUTHSOURCE%. Opis napake: %REASON%"

msgid "{status:some_error_occurred}"
msgstr "Prišlo je do napake!"

msgid "{login:change_home_org_button}"
msgstr "Izberite domačo organizacijo."

msgid "{admin:cfg_check_superfluous}"
msgstr "Odvečne nastavitve v konfiguracijski datoteki"

msgid "{errors:report_email}"
msgstr "Elektronski naslov:"

msgid "{errors:howto_header}"
msgstr "Kje lahko najdem pomoč?"

msgid "{errors:title_NOTSET}"
msgstr "Geslo ni nastavljeno"

msgid "{errors:descr_NORELAYSTATE}"
msgstr "Pobudnik te zahteve ni posredoval RelayState parametra."

msgid "{status:header_diagnostics}"
msgstr "SimpleSAMLphp diagnostika"

msgid "{status:intro}"
msgstr ""
"Živjo! To je statusna stran SimpleSAMLphp, ki omogoča pregled nad "
"trajanjem vaše trenutne seje in atributi, ki so povezani z njo."

msgid "{errors:title_NOTFOUNDREASON}"
msgstr "Strani ni bilo mogoče najti."

msgid "{admin:debug_sending_message_title}"
msgstr "Pošiljanje sporočila"

msgid "{errors:title_RESPONSESTATUSNOSUCCESS}"
msgstr "Napaka na IdP"

msgid "{admin:metadata_shib13-sp}"
msgstr "Shib 1.3 SP Metapodatki"

msgid "{admin:metaover_intro}"
msgstr "Za pregled podrobnosti SAML entitete, kliknite na glavo te entitete"

msgid "{errors:title_NOTVALIDCERT}"
msgstr "Napačen certifikat"

msgid "{general:remember}"
msgstr "Zapomni si privolitev."

msgid "{disco:selectidp}"
msgstr "Izberite IdP domače organizacije"

msgid "{login:help_desk_email}"
msgstr "Pošlji sporočilo tehnični podpori."

msgid "{login:help_desk_link}"
msgstr "Spletna stran tehnične podpore uporabnikom."

msgid "{errors:title_CASERROR}"
msgstr "CAS napaka"

msgid "{login:user_pass_text}"
msgstr ""
"Storitev zahteva, da se prijavite. To pomeni, da je potreben vnos "
"uporabniškega imena in gesla v spodnji polji."

msgid "{errors:title_DISCOPARAMS}"
msgstr "Zahteva, ki je bila poslana \"Discovery service-u\" je napačna."

msgid "{general:yes_continue}"
msgstr "Da, nadaljuj"

msgid "{disco:remember}"
msgstr "Shrani kot privzeto izbiro"

msgid "{admin:metaover_group_metadata.saml20-sp-hosted}"
msgstr "SAML 2.0 SP (Lokalni)"

msgid "{admin:metadata_simplesamlformat}"
msgstr ""
"V SimpleSAMLphp \"flat file\" formatu - ta format uporabite, če "
"uporabljate SimpleSAMLphp entiteto na drugi strani:"

msgid "{disco:login_at}"
msgstr "Prijavi se pri"

msgid "{errors:title_GENERATEAUTHNRESPONSE}"
msgstr "Odgovora za odjavo ni bilo mogoče ustvariti"

msgid "{errors:errorreport_header}"
msgstr "Poročilo o napaki je bilo poslano"

msgid "{errors:title_CREATEREQUEST}"
msgstr "Napaka pri ustvarjanju zahteve"

msgid "{admin:metaover_header}"
msgstr "Pregled metapodatkov"

msgid "{errors:report_submit}"
msgstr "Pošlji poročilo o napaki"

msgid "{errors:title_INVALIDCERT}"
msgstr "Neveljavno digitalno potrdilo"

msgid "{errors:title_NOTFOUND}"
msgstr "Strani ni bilo mogoče najti."

msgid "{logout:logged_out_text}"
msgstr "Odjava je bila uspešna. Hvala, ker uporabljate to storitev."

msgid "{admin:metaover_group_metadata.shib13-sp-hosted}"
msgstr "Shib 1.3 SP (Lokalni)"

msgid "{admin:metadata_cert_intro}"
msgstr "Prenesi X509 digitalno potrdilo v PEM datoteki."

msgid "{admin:debug_sending_message_msg_title}"
msgstr "Sporočilo"

msgid "{errors:title_UNKNOWNCERT}"
msgstr "Nepoznano digitalno potrdilo"

msgid "{errors:title_LDAPERROR}"
msgstr "Napaka LDAP-a"

msgid "{logout:failedsps}"
msgstr ""
"Odjava z ene ali več storitev ni uspela. Odjavo dokončajte tako, da "
"<i>zaprete spletni brskalnik</i>."

msgid "{errors:descr_NOTFOUND}"
msgstr "Strani ni bilo mogoče najti. Naveden URL strani je bil: %URL%"

msgid "{errors:howto_text}"
msgstr ""
"Ta napaka je verjetno posledica nepravilne konfiguracije SimpleSAMLphp-"
"ja. Obrnite se na skrbnika in mu posredujte to napako."

msgid "{admin:metaover_group_metadata.shib13-idp-hosted}"
msgstr "Shib 1.3 SP (Lokalni)"

msgid "{errors:descr_NOTVALIDCERT}"
msgstr "Posredovan certifikat je neveljaven"

msgid "{admin:debug_sending_message_text_button}"
msgstr "Sporočilo boste poslali s klikom na gumb za pošiljanje."

msgid "{admin:metaover_optional_not_found}"
msgstr "Naslednjih neobveznih polj ni bilo mogoče najti"

msgid "{logout:logout_only}"
msgstr "Ne, odjavi me samo z naslednjega %SP%"

msgid "{login:next}"
msgstr "Naprej"

msgid "{errors:descr_GENERATEAUTHNRESPONSE}"
msgstr "Ko je IdP želel ustvariti odgovor o prijavi, je prišlo do napake."

msgid "{disco:selectidp_full}"
msgstr "Izberite IdP, na katerem se boste avtenticirali:"

msgid "{errors:descr_NOTFOUNDREASON}"
msgstr ""
"Strani ni bilo mogoče najti. Razlog: %REASON%. Naveden URL strani je bil:"
" %URL%"

msgid "{errors:title_NOCERT}"
msgstr "Ni digitalnega potrdila"

msgid "{errors:title_LOGOUTINFOLOST}"
msgstr "Odjavni (Logout) parametri niso na voljo."

msgid "{admin:metaover_group_metadata.shib13-idp-remote}"
msgstr "Shib 1.3 SP (Oddaljeni)"

msgid "{errors:descr_CONFIG}"
msgstr "Nastavitve SimpleSAMLphp so napačne ali se med seboj izključujejo."

msgid "{admin:metadata_intro}"
msgstr ""
"Tu so metapodatki, ki jih je generiral SimpleSAMLphp. Dokument lahko "
"pošljete zaupanja vrednim partnerjem, s katerimi boste ustvarili "
"federacijo."

msgid "{admin:metadata_cert}"
msgstr "Digitalna potrdila"

msgid "{errors:descr_INVALIDCERT}"
msgstr ""
"Avtentikacija je spodletela: vaš spletni brskalnik je posredoval "
"neveljavno digitalno potrdilo ali pa ga ni moč prebrati!"

msgid "{status:header_shib}"
msgstr "Shibboleth demo primer"

msgid "{admin:metaconv_parse}"
msgstr "Sintaktična analiza (parse)"

msgid "Person's principal name at home organization"
msgstr "ID uporabnika na domači organizaciji"

msgid "Superfluous options in config file"
msgstr "Odvečne nastavitve v konfiguracijski datoteki"

msgid "Mobile"
msgstr "Mobilni telefon"

msgid "Shib 1.3 Service Provider (Hosted)"
msgstr "Shib 1.3 SP (Lokalni)"

msgid ""
"LDAP is the user database, and when you try to login, we need to contact "
"an LDAP database. An error occurred when we tried it this time."
msgstr ""
"LDAP je zbirka uporabnikov. Ko se želite prijaviti, je potrebno prijavo "
"preveriti v LDAPu. Pri trenutnem preverjanju je prišlo do napake."

msgid ""
"Optionally enter your email address, for the administrators to be able "
"contact you for further questions about your issue:"
msgstr ""
"Če želite, vnesite elektronski naslov, na katerem boste dosegljivi v "
"primeru dodatnih vprašanj za skrbnika sistema :"

msgid "Display name"
msgstr "Prikazno ime"

msgid "Remember my choice"
msgstr "Shrani kot privzeto izbiro"

msgid "SAML 2.0 SP Metadata"
msgstr "SAML 2.0 SP Metapodatki"

msgid "Notices"
msgstr "Obvestila"

msgid "Home telephone"
msgstr "Domača telefonska številka"

msgid ""
"Hi, this is the status page of SimpleSAMLphp. Here you can see if your "
"session is timed out, how long it lasts until it times out and all the "
"attributes that are attached to your session."
msgstr ""
"Živjo! To je statusna stran SimpleSAMLphp, ki omogoča pregled nad "
"trajanjem vaše trenutne seje in atributi, ki so povezani z njo."

msgid "Explain what you did when this error occurred..."
msgstr "Opišite, kako je prišlo do napake..."

msgid "An unhandled exception was thrown."
msgstr "Zagnana je bila nedefinirana izjema."

msgid "Invalid certificate"
msgstr "Napačen certifikat"

msgid "Service Provider"
msgstr "Ponudnik storitev"

msgid "Incorrect username or password."
msgstr "Napačno uporabniško ime ali geslo!"

msgid "There is an error in the request to this page. The reason was: %REASON%"
msgstr "Prišlo je do napake pri prejetem zahtevku. Razlog: %REASON%"

msgid "E-mail address:"
msgstr "Elektronski naslov:"

msgid "Submit message"
msgstr "Pošlji sporočilo"

msgid "No RelayState"
msgstr "RelayState parameter ne obstaja"

msgid "Error creating request"
msgstr "Napaka pri ustvarjanju zahteve"

msgid "Locality"
msgstr "Kraj"

msgid "Unhandled exception"
msgstr "Nedefinirana izjema."

msgid "The following required fields was not found"
msgstr "Naslednjih zahtevanih polj ni bilo mogoče najti"

msgid "Download the X509 certificates as PEM-encoded files."
msgstr "Prenesi X509 digitalno potrdilo v PEM datoteki."

#, python-format
msgid "Unable to locate metadata for %ENTITYID%"
msgstr "Metapodatkov za %ENTITYID% ni bilo moč najti"

msgid "Organizational number"
msgstr "Organizacijska številka"

msgid "Password not set"
msgstr "Geslo ni nastavljeno"

msgid "SAML 2.0 IdP Metadata"
msgstr "SAML 2.0 IdP Metapodatki"

msgid "Post office box"
msgstr "Poštni predal"

msgid ""
"A service has requested you to authenticate yourself. Please enter your "
"username and password in the form below."
msgstr ""
"Storitev zahteva, da se prijavite. To pomeni, da je potreben vnos "
"uporabniškega imena in gesla v spodnji polji."

msgid "CAS Error"
msgstr "CAS napaka"

msgid ""
"The debug information below may be of interest to the administrator / "
"help desk:"
msgstr "Podatki o odpravljanju napak bodo zanimali skrbnika/helpdesk:"

msgid ""
"Either no user with the given username could be found, or the password "
"you gave was wrong. Please check the username and try again."
msgstr ""
"Uporabnika s tem uporabniškim imenom ni bilo mogoče najti ali pa je "
"vpisano geslo napačno. Preverite svoje uporabniško ime in poskusite "
"znova."

msgid "Error"
msgstr "Napaka"

msgid "Next"
msgstr "Naprej"

msgid "Distinguished name (DN) of the person's home organizational unit"
msgstr "Ime oddelka domače organizacije (DN)"

msgid "State information lost"
msgstr "Podatki o stanju so izgubljeni"

msgid ""
"The password in the configuration (auth.adminpassword) is not changed "
"from the default value. Please edit the configuration file."
msgstr ""
"V nastavitvah je geslo skrbnika (auth.adminpassword) še vedno nastavljeno"
" na začetno vrednost. Spremenite ga!"

msgid "Converted metadata"
msgstr "Pretvorjeni metapodatki"

msgid "Mail"
msgstr "Elektronski naslov"

msgid "No, cancel"
msgstr "Ne"

msgid ""
"You have chosen <b>%HOMEORG%</b> as your home organization. If this is "
"wrong you may choose another one."
msgstr ""
"Izbrali ste <b>%HOMEORG%</b> kot vašo domačo organizacijo. V primeru da "
"je izbor napačen, izberite drugo."

msgid "Error processing request from Service Provider"
msgstr "Napaka pri obdelavi zahteve SP"

msgid "Distinguished name (DN) of person's primary Organizational Unit"
msgstr "Ime (DN) oddelka v domači organizaciji"

msgid ""
"To look at the details for an SAML entity, click on the SAML entity "
"header."
msgstr "Za pregled podrobnosti SAML entitete, kliknite na glavo te entitete"

msgid "Enter your username and password"
msgstr "Vnesite svoje uporabniško ime in geslo"

msgid "Login at"
msgstr "Prijavi se pri"

msgid "No"
msgstr "Ne"

msgid "Home postal address"
msgstr "Domači naslov"

msgid "WS-Fed SP Demo Example"
msgstr "WS-Fed SP demo primer"

msgid "SAML 2.0 Identity Provider (Remote)"
msgstr "SAML 2.0 IdP (Oddaljeni)"

msgid "Error processing the Logout Request"
msgstr "Napaka pri obdelavi zahteve za odjavo"

msgid "Do you want to logout from all the services above?"
msgstr "Ali se želite odjaviti z vseh naštetih storitev?"

msgid "Select"
msgstr "Izberite"

msgid "The authentication was aborted by the user"
msgstr "Avtentikacija prekinjena na zahtevo uporabnika"

msgid "Your attributes"
msgstr "Vaši atributi"

msgid "Given name"
msgstr "Ime"

msgid "Identity assurance profile"
msgstr "Stopnja zanesljivosti"

msgid "SAML 2.0 SP Demo Example"
msgstr "SAML 2.0 SP Demo primer"

msgid "Logout information lost"
msgstr "Odjavni (Logout) parametri niso na voljo."

msgid "Organization name"
msgstr "Ime organizacije"

msgid "Authentication failed: the certificate your browser sent is unknown"
msgstr ""
"Avtentikacija je spodletela: vaš spletni brskalnik je posredoval "
"nepoznano digitalno potrdilo"

msgid ""
"You are about to send a message. Hit the submit message button to "
"continue."
msgstr "Sporočilo boste poslali s klikom na gumb za pošiljanje."

msgid "Home organization domain name"
msgstr "ID domače organizacije"

msgid "Go back to the file list"
msgstr "Vrnite se na seznam datotek"

msgid "Error report sent"
msgstr "Poročilo o napaki je bilo poslano"

msgid "Common name"
msgstr "Ime in priimek"

msgid "Please select the identity provider where you want to authenticate:"
msgstr "Izberite IdP, na katerem se boste avtenticirali:"

msgid "Logout failed"
msgstr "Odjava je spodletela."

msgid "Identity number assigned by public authorities"
msgstr "Matična številka"

msgid "WS-Federation Identity Provider (Remote)"
msgstr "WS-Federation Idp (Oddaljni)"

msgid "Error received from Identity Provider"
msgstr "Napaka na IdP"

msgid "LDAP Error"
msgstr "Napaka LDAP-a"

msgid ""
"The information about the current logout operation has been lost. You "
"should return to the service you were trying to log out from and try to "
"log out again. This error can be caused by the logout information "
"expiring. The logout information is stored for a limited amout of time - "
"usually a number of hours. This is longer than any normal logout "
"operation should take, so this error may indicate some other error with "
"the configuration. If the problem persists, contact your service "
"provider."
msgstr ""
"Odjavni (Logout) parametri niso na voljo. Vrnite se na storitev, ki ste "
"jo pravkar uporabljali in se ponovno poskusite odjaviti. Napaka je "
"posledica poteka veljavnosti seje."

msgid "Some error occurred"
msgstr "Prišlo je do napake!"

msgid "Organization"
msgstr "Organizacija"

msgid "No certificate"
msgstr "Ni digitalnega potrdila"

msgid "Choose home organization"
msgstr "Izberite domačo organizacijo."

msgid "Persistent pseudonymous ID"
msgstr "Trajni anonimni ID"

msgid "No SAML response provided"
msgstr "Nobenega odgovora za SAML ni na voljo"

msgid "No errors found."
msgstr "Ni napak"

msgid "SAML 2.0 Service Provider (Hosted)"
msgstr "SAML 2.0 SP (Lokalni)"

msgid "The given page was not found. The URL was: %URL%"
msgstr "Strani ni bilo mogoče najti. Naveden URL strani je bil: %URL%"

msgid "Configuration error"
msgstr "Napaka v nastavitvah"

msgid "Required fields"
msgstr "Zahtevana polja"

msgid "An error occurred when trying to create the SAML request."
msgstr "Pri ustvarjanju SAML zahteve je prišlo do napake."

msgid ""
"This error probably is due to some unexpected behaviour or to "
"misconfiguration of SimpleSAMLphp. Contact the administrator of this "
"login service, and send them the error message above."
msgstr ""
"Ta napaka je verjetno posledica nepravilne konfiguracije SimpleSAMLphp-"
"ja. Obrnite se na skrbnika in mu posredujte to napako."

#, python-format
msgid "Your session is valid for %remaining% seconds from now."
msgstr "Vaša trenutna seja je veljavna še %remaining% sekund."

msgid "Domain component (DC)"
msgstr "Domenska komponenta (DC)"

msgid "Shib 1.3 Service Provider (Remote)"
msgstr "Shib 1.3 SP (Oddaljeni)"

msgid "Password"
msgstr "Geslo"

msgid "Nickname"
msgstr "Vzdevek"

msgid "Send error report"
msgstr "Pošlji poročilo o napaki"

msgid ""
"Authentication failed: the certificate your browser sent is invalid or "
"cannot be read"
msgstr ""
"Avtentikacija je spodletela: vaš spletni brskalnik je posredoval "
"neveljavno digitalno potrdilo ali pa ga ni moč prebrati!"

msgid "The error report has been sent to the administrators."
msgstr "Poročilo o napaki je bilo poslano skrbnikom sistema."

msgid "Date of birth"
msgstr "Datum rojstva"

msgid "Private information elements"
msgstr "Zasebni informacijski elementi"

msgid "You are also logged in on these services:"
msgstr "Prijavljeni ste v naslednje storitve:"

msgid "SimpleSAMLphp Diagnostics"
msgstr "SimpleSAMLphp diagnostika"

msgid "Debug information"
msgstr "Pomoč pri odpravljanju napak (debug)"

msgid "No, only %SP%"
msgstr "Ne, odjavi me samo z naslednjega %SP%"

msgid "Username"
msgstr "Uporabniško ime"

msgid "Go back to SimpleSAMLphp installation page"
msgstr "Nazaj na namestitveno stran SimpleSAMLphp"

msgid "You have successfully logged out from all services listed above."
msgstr "Uspešno ste se odjavili z vseh naštetih storitev."

msgid "You are now successfully logged out from %SP%."
msgstr "Uspešno ste se odjavili s ponudnika storitev: %SP%"

msgid "Affiliation"
msgstr "Vloga uporabnika"

msgid "You have been logged out."
msgstr "Odjava je bila uspešna. Hvala, ker uporabljate to storitev."

msgid "Return to service"
msgstr "Vrni se nazaj na storitev."

msgid "Logout"
msgstr "Odjava"

msgid "State information lost, and no way to restart the request"
msgstr ""
"Podatki o stanju so izgubljeni, zato zahteve ni mogoče obnoviti/ponovno "
"zagnati."

msgid "Error processing response from Identity Provider"
msgstr "Pri obdelavi odgovora IdP-ja je prišlo do napake"

msgid "WS-Federation Service Provider (Hosted)"
msgstr "WS-Fedration SP (Lokalni)"

msgid "Preferred language"
msgstr "Želen jezik"

msgid "SAML 2.0 Service Provider (Remote)"
msgstr "SAML 2.0 SP (Oddaljeni)"

msgid "Surname"
msgstr "Priimek"

msgid "No access"
msgstr "Ni dostopa"

msgid "The following fields was not recognized"
msgstr "Nepoznana polja"

msgid "Authentication error in source %AUTHSOURCE%. The reason was: %REASON%"
msgstr "Napaka v avtentikacijskem viru: %AUTHSOURCE%. Opis napake: %REASON%"

msgid "Bad request received"
msgstr "Napaka v prejetem zahtevku."

msgid "User ID"
msgstr "Uporabniško ime"

msgid "JPEG Photo"
msgstr "JPEG Slika"

msgid "Postal address"
msgstr "Poštni naslov"

msgid "An error occurred when trying to process the Logout Request."
msgstr "Pri obdelavi zahteve za odjavo je prišlo do napake."

msgid "Sending message"
msgstr "Pošiljanje sporočila"

msgid "In SAML 2.0 Metadata XML format:"
msgstr "V SAML 2.0 Metapodatkovni XML format:"

msgid "Logging out of the following services:"
msgstr "Odjava iz naslednjih storitev:"

msgid ""
"When this identity provider tried to create an authentication response, "
"an error occurred."
msgstr "Ko je IdP želel ustvariti odgovor o prijavi, je prišlo do napake."

msgid "Could not create authentication response"
msgstr "Odgovora za odjavo ni bilo mogoče ustvariti"

msgid "Labeled URI"
msgstr "Označen URI"

msgid "SimpleSAMLphp appears to be misconfigured."
msgstr "Nastavitve SimpleSAMLphp so napačne ali se med seboj izključujejo."

msgid "Shib 1.3 Identity Provider (Hosted)"
msgstr "Shib 1.3 SP (Lokalni)"

msgid "Metadata"
msgstr "Metapodatki"

msgid "Login"
msgstr "Prijava"

msgid ""
"This Identity Provider received an Authentication Request from a Service "
"Provider, but an error occurred when trying to process the request."
msgstr ""
"IdP je prejel avtenticirano zahtevo SP-ja, vendar je prišlo do napake pri"
" obdelavi te zahteve."

msgid "Yes, all services"
msgstr "Da, odjavi me z vseh storitev"

msgid "Logged out"
msgstr "Odjavljen"

msgid "Postal code"
msgstr "Poštna številka"

msgid "Logging out..."
msgstr "Odjavljanje..."

msgid "Metadata not found"
msgstr "Metapodatkov ni bilo moč najti"

msgid "SAML 2.0 Identity Provider (Hosted)"
msgstr "SAML 2.0 IdP (Lokalni)"

msgid "Primary affiliation"
msgstr "Primarna vloga"

msgid ""
"If you report this error, please also report this tracking number which "
"makes it possible to locate your session in the logs available to the "
"system administrator:"
msgstr ""
"Če boste prijavili to napako, priložite tudi ID seje, preko katere bo "
"lažje najti vaše zapise v dnevniških datotekah, ki so na voljo skrbniku "
"sistema."

msgid "XML metadata"
msgstr "XML metapodatki"

msgid ""
"The parameters sent to the discovery service were not according to "
"specifications."
msgstr ""
"Parametri, ki so bili poslani \"Discovery service-u\", ne ustrezajo "
"specifikaciji."

msgid "Telephone number"
msgstr "Telefonska številka"

msgid ""
"Unable to log out of one or more services. To ensure that all your "
"sessions are closed, you are encouraged to <i>close your webbrowser</i>."
msgstr ""
"Odjava z ene ali več storitev ni uspela. Odjavo dokončajte tako, da "
"<i>zaprete spletni brskalnik</i>."

msgid "Bad request to discovery service"
msgstr "Zahteva, ki je bila poslana \"Discovery service-u\" je napačna."

msgid "Select your identity provider"
msgstr "Izberite IdP domače organizacije"

msgid "Entitlement regarding the service"
msgstr "Upravičenost do storitve"

msgid "Shib 1.3 SP Metadata"
msgstr "Shib 1.3 SP Metapodatki"

msgid ""
"As you are in debug mode, you get to see the content of the message you "
"are sending:"
msgstr "Ste v debug načinu, lahko si ogledate vsebino sporočila, ki ga pošiljate"

msgid "Certificates"
msgstr "Digitalna potrdila"

msgid "Remember"
msgstr "Zapomni si privolitev."

msgid "Distinguished name (DN) of person's home organization"
msgstr "Ime domače organizacije (DN)"

msgid "You are about to send a message. Hit the submit message link to continue."
msgstr "Sporočilo boste poslali s klikom na gumb za pošiljanje."

msgid "Organizational unit"
msgstr "Oddelek"

msgid "Authentication aborted"
msgstr "Avtentikacija prekinjena"

msgid "Local identity number"
msgstr "Vpisna številka"

msgid "Report errors"
msgstr "Prijavi napake"

msgid "Page not found"
msgstr "Strani ni bilo mogoče najti."

msgid "Shib 1.3 IdP Metadata"
msgstr "Shib 1.3 IdP Metapodatki"

msgid "Change your home organization"
msgstr "Izberite vašo domačo organizacijo."

msgid "User's password hash"
msgstr "Uporabnikovo zgoščeno geslo"

msgid ""
"In SimpleSAMLphp flat file format - use this if you are using a "
"SimpleSAMLphp entity on the other side:"
msgstr ""
"V SimpleSAMLphp \"flat file\" formatu - ta format uporabite, če "
"uporabljate SimpleSAMLphp entiteto na drugi strani:"

msgid "Yes, continue"
msgstr "Da, nadaljuj"

msgid "Completed"
msgstr "Dokončano"

msgid ""
"The Identity Provider responded with an error. (The status code in the "
"SAML Response was not success)"
msgstr "Odziv IdP vsebuje napako (\"SAML-Response\" ni uspel)! "

msgid "Error loading metadata"
msgstr "Napaka pri nalaganju metapodatkov"

msgid "Select configuration file to check:"
msgstr "Izberite konfiguracijsko datoteko, ki jo želite preveriti"

msgid "On hold"
msgstr "V teku"

msgid "Error when communicating with the CAS server."
msgstr "Napaka pri komunikaciji s CAS strežnikom."

msgid "No SAML message provided"
msgstr "SAML sporočilo ni na voljo"

msgid "Help! I don't remember my password."
msgstr "Na pomoč! Pozabil sem svoje geslo."

msgid ""
"You can turn off debug mode in the global SimpleSAMLphp configuration "
"file <tt>config/config.php</tt>."
msgstr ""
"Debug način lahko izklopite v globalni SimpleSAMLphp konfiguracijski "
"datoteki <tt>config/config.php</tt>."

msgid "How to get help"
msgstr "Kje lahko najdem pomoč?"

msgid ""
"You accessed the SingleLogoutService interface, but did not provide a "
"SAML LogoutRequest or LogoutResponse. Please note that this endpoint is "
"not intended to be accessed directly."
msgstr ""
"Dostopili ste do SingleLogoutService vmesnika, ampak niste zagotovili "
"SAML LogoutRequest ali LogoutResponse."

msgid "SimpleSAMLphp error"
msgstr "SimpleSAMLphp napaka"

msgid ""
"One or more of the services you are logged into <i>do not support "
"logout</i>. To ensure that all your sessions are closed, you are "
"encouraged to <i>close your webbrowser</i>."
msgstr ""
"Ena ali več storitev, v katere ste prijavljeni, <i>ne omogoča odjave</i>."
" Odjavo iz teh storitev izvedete tako, da <i>zaprete spletni "
"brskalnik</i>."

msgid "Organization's legal name"
msgstr "Naziv organizacije"

msgid "Options missing from config file"
msgstr "V konfiguracijski datoteki manjkajo nastavitve"

msgid "The following optional fields was not found"
msgstr "Naslednjih neobveznih polj ni bilo mogoče najti"

msgid "Authentication failed: your browser did not send any certificate"
msgstr ""
"Avtentikacija je spodletela: vaš spletni brskalnik ni posredoval "
"digitalnega potrdila!"

msgid ""
"This endpoint is not enabled. Check the enable options in your "
"configuration of SimpleSAMLphp."
msgstr ""
"Ta končna točka ni omogočena. Preverite možnost omogočanja storitve v "
"konfiguraciji SimpleSAMLphp-ja."

msgid "You can <a href=\"%METAURL%\">get the metadata xml on a dedicated URL</a>:"
msgstr "XML metapodatki se nahajajo na <a href=\"%METAURL%\">tem naslovu</a>:"

msgid "Street"
msgstr "Ulica"

msgid ""
"There is some misconfiguration of your SimpleSAMLphp installation. If you"
" are the administrator of this service, you should make sure your "
"metadata configuration is correctly setup."
msgstr ""
"V konfiguraciji SimpleSAMLphp-ja je napaka. Če ste skrbnik te storitve, "
"preverite, da je konfiguracija metapodatkov pravilna."

msgid "Incorrect username or password"
msgstr "Napačno uporabniško ime ali geslo"

msgid "Message"
msgstr "Sporočilo"

msgid "Contact information:"
msgstr "Kontakt"

msgid "Unknown certificate"
msgstr "Nepoznano digitalno potrdilo"

msgid "Legal name"
msgstr "Uradno ime"

msgid "Optional fields"
msgstr "Neobvezna polja"

msgid ""
"The initiator of this request did not provide a RelayState parameter "
"indicating where to go next."
msgstr "Pobudnik te zahteve ni posredoval RelayState parametra."

msgid "You have previously chosen to authenticate at"
msgstr "Predhodnje ste se prijavljali že pri"

msgid ""
"You sent something to the login page, but for some reason the password "
"was not sent. Try again please."
msgstr "Prišlo je do napake, poskusite znova."

msgid "Fax number"
msgstr "Fax"

msgid "Shibboleth demo"
msgstr "Shibboleth demo primer"

msgid "Error in this metadata entry"
msgstr "Napaka pri vnosu metapodatkov"

msgid "Session size: %SIZE%"
msgstr "Velikost seje: %SIZE% bajtov"

msgid "Parse"
msgstr "Sintaktična analiza (parse)"

msgid ""
"Without your username and password you cannot authenticate "
"yourself for access to the service. There may be someone that can help "
"you. Consult the help desk at your organization!"
msgstr ""
"Žal se brez uporabniškega imena in gesla ne morete prijaviti in "
"uporabljati storitev."

msgid "Metadata parser"
msgstr "Metapodatkovna sintaktična analiza (parser)"

msgid "Choose your home organization"
msgstr "Izberite vašo domačo organizacijo"

msgid "Send e-mail to help desk"
msgstr "Pošlji sporočilo tehnični podpori."

msgid "Metadata overview"
msgstr "Pregled metapodatkov"

msgid "Title"
msgstr "Naziv"

msgid "Manager"
msgstr "Menedžer"

msgid "You did not present a valid certificate."
msgstr "Posredovan certifikat je neveljaven"

msgid "Authentication source error"
msgstr "Napaka v avtentikacijskem viru"

msgid "Affiliation at home organization"
msgstr "Vloga v organizaciji"

msgid "Help desk homepage"
msgstr "Spletna stran tehnične podpore uporabnikom."

msgid "Configuration check"
msgstr "Preverjanje konfiguracije"

msgid "We did not accept the response sent from the Identity Provider."
msgstr "Odgovor, poslan od IdP-ja, ni bil sprejet."

msgid "The given page was not found. The reason was: %REASON%  The URL was: %URL%"
msgstr ""
"Strani ni bilo mogoče najti. Razlog: %REASON%. Naveden URL strani je bil:"
" %URL%"

msgid "Shib 1.3 Identity Provider (Remote)"
msgstr "Shib 1.3 SP (Oddaljeni)"

msgid ""
"Here is the metadata that SimpleSAMLphp has generated for you. You may "
"send this metadata document to trusted partners to setup a trusted "
"federation."
msgstr ""
"Tu so metapodatki, ki jih je generiral SimpleSAMLphp. Dokument lahko "
"pošljete zaupanja vrednim partnerjem, s katerimi boste ustvarili "
"federacijo."

msgid "[Preferred choice]"
msgstr "Prioritetna izbira"

msgid "Organizational homepage"
msgstr "Spletna stran organizacije"

msgid ""
"You accessed the Assertion Consumer Service interface, but did not "
"provide a SAML Authentication Response. Please note that this endpoint is"
" not intended to be accessed directly."
msgstr ""
"Dostopili ste do \"Assertion Consumer Service\" vmesnika, ampak niste "
"zagotovili \"SAML Authentication Responsa\"."


msgid ""
"You are now accessing a pre-production system. This authentication setup "
"is for testing and pre-production verification only. If someone sent you "
"a link that pointed you here, and you are not <i>a tester</i> you "
"probably got the wrong link, and should <b>not be here</b>."
msgstr ""
"Dostopate do predprodukcijskega sistema, ki je namenjen izključno "
"preizkušanju. V primeru da ste pristali na tej strani med postopkom "
"prijave v produkcijsko storitev, je storitev verjetno napačno "
"nastavljena."
