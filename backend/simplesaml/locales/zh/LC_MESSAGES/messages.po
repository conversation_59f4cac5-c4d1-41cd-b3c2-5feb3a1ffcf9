
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:31+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: zh\n"
"Language-Team: \n"
"Plural-Forms: nplurals=1; plural=0\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{admin:metadata_saml20-idp}"
msgstr "SAML 2.0 IdP 元信息"

msgid "{errors:descr_WRONGUSERPASS}"
msgstr "如果不是给定的用户名没有找到就是给定的密码错误，请再次检查用户名和密码"

msgid "{logout:failed}"
msgstr "退出失败"

msgid "{status:attributes_header}"
msgstr "你的属性"

msgid "{admin:metaover_group_metadata.saml20-sp-remote}"
msgstr "SAML 2.0 服务提供者 (远程)"

msgid "{errors:descr_NOCERT}"
msgstr "认证失败，你的浏览器没有发送任何证书"

msgid "{errors:title_PROCESSASSERTION}"
msgstr "处理来自身份提供者的应答时出错"

msgid "{errors:title_NOSTATE}"
msgstr "状态信息丢失"

msgid "{login:username}"
msgstr "用户名"

msgid "{errors:title_METADATA}"
msgstr "载入元信息时发生错误"

msgid "{admin:metaconv_title}"
msgstr "元信息分析器"

msgid "{admin:cfg_check_noerrors}"
msgstr "没有发现错误"

msgid "{errors:descr_LOGOUTINFOLOST}"
msgstr "关于当前退出操作的相关信息丢失了，你应该返回服务中，重新尝试退出，这个错误可能是退出信息超时引起的。退出消息在有限的时间内存储，通常是几个小时，这比任何常规的退出时间所需的时间要长多了，所以这种错误可能是配置错误，如果问题依旧存在，联系你的服务提供商"

msgid "{disco:previous_auth}"
msgstr "你先前选择的认证"

msgid "{admin:cfg_check_back}"
msgstr "返回至文件列表"

msgid "{errors:report_trackid}"
msgstr "如果你报告了这个错误，那么请你也报告这个追踪号码，系统管理员有可能根据这个号码在日志中定位你的SESSION"

msgid "{login:change_home_org_title}"
msgstr "改变你的家庭组织"

msgid "{errors:descr_METADATANOTFOUND}"
msgstr "无法为%ENTITYID%定位元信息"

msgid "{admin:metadata_metadata}"
msgstr "元信息"

msgid "{errors:report_text}"
msgstr "你可以填入你的Email地址（当然你也可以选择不填），这样管理员就能够通过联系您来进一步的了解你的问题了"

msgid "{errors:report_header}"
msgstr "报告错误"

msgid "{login:change_home_org_text}"
msgstr "你选择了<b>%HOMEORG%</b>作为你的家庭组织。如果错了请选择其他的"

msgid "{errors:title_PROCESSAUTHNREQUEST}"
msgstr "处理来自服务提供者的请求时出错"

msgid "{errors:descr_PROCESSASSERTION}"
msgstr "我们不接受来自身份提供者的应答"

msgid "{errors:debuginfo_header}"
msgstr "调试信息"

msgid "{admin:debug_sending_message_msg_text}"
msgstr "当你处在调试模式中时，你将看到你正在发送的消息的内容"

msgid "{errors:descr_RESPONSESTATUSNOSUCCESS}"
msgstr "身份提供者的应答存在错误（SAML应答状态码并没有成功）"

msgid "{admin:metadata_shib13-idp}"
msgstr "Shib 1.3 IdP 元信息"

msgid "{login:help_text}"
msgstr "太糟糕了！-没有你的用户名和密码你将不能访问该服务，也许有人能够帮助你，请咨询你所在大学的服务台"

msgid "{logout:default_link_text}"
msgstr "返回SimpleSAMLphp安装页面"

msgid "{errors:error_header}"
msgstr "SimpleSAMLphp错误"

msgid "{login:help_header}"
msgstr "帮助！我忘记我的密码了！"

msgid "{errors:descr_LDAPERROR}"
msgstr "LDAP是一个用户数据库，当你试图登录时，我们需要连接到LDAP数据库，然而这次我们试图链接时发生了一个错误"

msgid "{errors:descr_METADATA}"
msgstr "你的SimpleSAMLphp配置错误，如果你是该服务的管理员，那么请确认你的配置是正确的"

msgid "{errors:title_BADREQUEST}"
msgstr "收到了错误的请求"

msgid "{status:sessionsize}"
msgstr "Session 大小: %SIZE%"

msgid "{logout:title}"
msgstr "退出"

msgid "{admin:metaconv_xmlmetadata}"
msgstr "XML元信息"

msgid "{admin:metaover_unknown_found}"
msgstr "下列区域无法识别"

msgid "{errors:title_AUTHSOURCEERROR}"
msgstr "认证源错误"

msgid "{login:select_home_org}"
msgstr "选择你的组织"

msgid "{logout:hold}"
msgstr "保持"

msgid "{admin:cfg_check_header}"
msgstr "配置检查"

msgid "{admin:debug_sending_message_send}"
msgstr "提交信息"

msgid "{status:logout}"
msgstr "退出"

msgid "{errors:descr_DISCOPARAMS}"
msgstr "发送给搜寻服务的参数不符合规范"

msgid "{errors:descr_CREATEREQUEST}"
msgstr "在创建SAML请求中发生了一个错误"

msgid "{admin:metaover_optional_found}"
msgstr "选项区域"

msgid "{logout:return}"
msgstr "返回至服务"

msgid "{admin:metadata_xmlurl}"
msgstr "你可以在 <a href=\"%METAURL%\">获取元信息XML</a>"

msgid "{logout:logout_all}"
msgstr "是的，所有的服务"

msgid "{admin:debug_disable_debug_mode}"
msgstr "你可以关闭调试模式，在SimpleSAMLphp全局配置文件<tt>config/config.php</tt>中"

msgid "{disco:select}"
msgstr "选择"

msgid "{logout:also_from}"
msgstr "你同时登录这以下这些服务"

msgid "{login:login_button}"
msgstr "登录"

msgid "{logout:progress}"
msgstr "正在退出"

msgid "{login:error_wrongpassword}"
msgstr "错误的用户名或者密码"

msgid "{admin:metaover_group_metadata.shib13-sp-remote}"
msgstr "Shib 1.3 服务提供者（远程）"

msgid "{errors:descr_PROCESSAUTHNREQUEST}"
msgstr "该身份提供者接受到了一个来自服务提供者的认证请求，但是在试图处理该请求的过程中发生了错误"

msgid "{logout:logout_all_question}"
msgstr "你想同时从上面的这些服务中退出吗？"

msgid "{errors:title_NOACCESS}"
msgstr "禁止访问"

msgid "{login:error_nopassword}"
msgstr "你确实发送了一些信息给登录页面，但由于某些原因，你没有发送密码，请再试一次"

msgid "{errors:title_NORELAYSTATE}"
msgstr "无依赖状态"

msgid "{errors:descr_NOSTATE}"
msgstr "状态信息丢失，并且无法重新请求"

msgid "{login:password}"
msgstr "密码"

msgid "{errors:debuginfo_text}"
msgstr "管理员或者服务台可能对下面的调试信息很感兴趣"

msgid "{admin:cfg_check_missing}"
msgstr "配置文件中选项缺失"

msgid "{errors:descr_UNHANDLEDEXCEPTION}"
msgstr "抛出一个未处理的异常"

msgid "{general:yes}"
msgstr "是"

msgid "{errors:title_CONFIG}"
msgstr "配置错误"

msgid "{errors:title_LOGOUTREQUEST}"
msgstr "处理退出请求时发生错误"

msgid "{admin:metaover_errorentry}"
msgstr "该元信息实体存在错误"

msgid "{errors:title_METADATANOTFOUND}"
msgstr "没有找到元信息"

msgid "{login:contact_info}"
msgstr "联系方式"

msgid "{errors:title_UNHANDLEDEXCEPTION}"
msgstr "未处理的异常"

msgid "{status:header_saml20_sp}"
msgstr "SAML 2.0 SP演示案例"

msgid "{login:error_header}"
msgstr "错误"

msgid "{errors:title_USERABORTED}"
msgstr "认证中止"

msgid "{logout:incapablesps}"
msgstr "一个或多个你已登录的服务<i>不支持退出</i>，请确认你所有sessions已关闭，我们鼓励你 <i>关闭浏览器</i>"

msgid "{admin:metadata_xmlformat}"
msgstr "在SAML 2.0 XML 元信息格式中："

msgid "{admin:metaover_group_metadata.saml20-idp-remote}"
msgstr "SAML 2.0 身份提供者（远程）"

msgid "{admin:metaover_group_metadata.saml20-idp-hosted}"
msgstr "SAML 2.0 身份提供者（本地）"

msgid "{admin:metaover_required_found}"
msgstr "必需的区域"

msgid "{admin:cfg_check_select_file}"
msgstr "选择一个配置文件用于检测"

msgid "{errors:descr_UNKNOWNCERT}"
msgstr "认证失败：你的浏览器发送的是未知的证书"

msgid "{logout:logging_out_from}"
msgstr "从下列服务中退出"

msgid "{logout:loggedoutfrom}"
msgstr "你已成功从%SP%退出"

msgid "{errors:errorreport_text}"
msgstr "错误报告已经发送给管理员"

msgid "{errors:descr_LOGOUTREQUEST}"
msgstr "试图处理退出请求时发生了一个错误"

msgid "{logout:success}"
msgstr "你成功的退出了上面列表中的服务"

msgid "{admin:cfg_check_notices}"
msgstr "通告"

msgid "{errors:descr_USERABORTED}"
msgstr "认证被用户中止"

msgid "{errors:descr_CASERROR}"
msgstr "在和CAS服务器的通讯中发生了错误"

msgid "{general:no}"
msgstr "没有"

msgid "{admin:metadata_saml20-sp}"
msgstr "SAML 2.0 SP 元信息"

msgid "{admin:metaconv_converted}"
msgstr "转换过的元信息"

msgid "{logout:completed}"
msgstr "完成"

msgid "{errors:descr_NOTSET}"
msgstr "你没有修改配置文件中的默认密码，请修改该密码"

msgid "{general:service_provider}"
msgstr "服务提供者"

msgid "{errors:descr_BADREQUEST}"
msgstr "请求该页的请求存在错误，原因：%REASON%"

msgid "{logout:no}"
msgstr "不"

msgid "{disco:icon_prefered_idp}"
msgstr "首选选项"

msgid "{general:no_cancel}"
msgstr "不，取消"

msgid "{login:user_pass_header}"
msgstr "输入你的用户名和密码"

msgid "{errors:report_explain}"
msgstr "说明一下，你正在做什么的时候发生了这个错误"

msgid "{errors:title_ACSPARAMS}"
msgstr "没有提供SAML应答"

msgid "{errors:descr_SLOSERVICEPARAMS}"
msgstr "你访问了SingleLogoutService接口，但是并没有提供一个SAML的LogoutRequest或者LogoutResponse"

msgid "{login:organization}"
msgstr "组织"

msgid "{errors:title_WRONGUSERPASS}"
msgstr "不正确的用户名或密码"

msgid "{admin:metaover_required_not_found}"
msgstr "下列必需的区域没有找到"

msgid "{errors:descr_NOACCESS}"
msgstr "这个端点没有启用，请在SimpleSAMLphp的配置选项中选中启用"

msgid "{errors:title_SLOSERVICEPARAMS}"
msgstr "没有提供SAML消息"

msgid "{errors:descr_ACSPARAMS}"
msgstr "你访问了Assertion Consumer Service接口，但是并没有提供一个SAML认证应答"

msgid "{admin:debug_sending_message_text_link}"
msgstr "你准备发送一个消息，请点击提交链接以继续"

msgid "{errors:descr_AUTHSOURCEERROR}"
msgstr "认证源%AUTHSOURCE%存在错误， 原因: %REASON%"

msgid "{status:some_error_occurred}"
msgstr "某些错误发生了"

msgid "{login:change_home_org_button}"
msgstr "选择你的家庭组织"

msgid "{admin:cfg_check_superfluous}"
msgstr "配置文件中拥有过多的选项"

msgid "{errors:report_email}"
msgstr "E-mail地址"

msgid "{errors:howto_header}"
msgstr "如何获取帮助"

msgid "{errors:title_NOTSET}"
msgstr "没有设置密码"

msgid "{errors:descr_NORELAYSTATE}"
msgstr "这个请求的发起人没有提供RelayState参数，以说明下一步处理"

msgid "{status:header_diagnostics}"
msgstr "SimpleSAMLphp 诊断"

msgid "{status:intro}"
msgstr "嗨，这是SimpleSAMLphp状态页。这里你可以看到，如果您的会话超时，它持续多久，直到超时和连接到您的会话的所有属性。"

msgid "{errors:title_NOTFOUNDREASON}"
msgstr "页面没有找到"

msgid "{admin:debug_sending_message_title}"
msgstr "正在发送消息"

msgid "{errors:title_RESPONSESTATUSNOSUCCESS}"
msgstr "从身份提供者收到一个错误"

msgid "{admin:metadata_shib13-sp}"
msgstr "Shib 1.3 SP 元信息"

msgid "{admin:metaover_intro}"
msgstr "想要查看SAML实体的详细情况，请点击SAML实体载入器"

msgid "{errors:title_NOTVALIDCERT}"
msgstr "无效的证书"

msgid "{general:remember}"
msgstr "记住"

msgid "{disco:selectidp}"
msgstr "选择你的身份提供者"

msgid "{login:help_desk_email}"
msgstr "发送Email给服务台"

msgid "{login:help_desk_link}"
msgstr "服务台的主页"

msgid "{errors:title_CASERROR}"
msgstr "CAS错误"

msgid "{login:user_pass_text}"
msgstr "一个服务需要你的认证，请在下面输入你的用户名和密码"

msgid "{errors:title_DISCOPARAMS}"
msgstr "错误的搜寻服务请求"

msgid "{general:yes_continue}"
msgstr "是的，继续"

msgid "{disco:remember}"
msgstr "记住我的选择"

msgid "{admin:metaover_group_metadata.saml20-sp-hosted}"
msgstr "SAML 2.0 服务提供者（本地）"

msgid "{admin:metadata_simplesamlformat}"
msgstr "如果你想在其他网站使用的SimpleSAMLphp，那么你应该使用SimpleSAMLphp扁平的文件格式"

msgid "{disco:login_at}"
msgstr "登录于"

msgid "{errors:title_GENERATEAUTHNRESPONSE}"
msgstr "无法创建认证应答"

msgid "{errors:errorreport_header}"
msgstr "发送错误报告"

msgid "{errors:title_CREATEREQUEST}"
msgstr "创建请求出错"

msgid "{admin:metaover_header}"
msgstr "元信息浏览"

msgid "{errors:report_submit}"
msgstr "发送错误报告"

msgid "{errors:title_INVALIDCERT}"
msgstr "无效的证书"

msgid "{errors:title_NOTFOUND}"
msgstr "页面没有找到"

msgid "{logout:logged_out_text}"
msgstr "你已经退出了"

msgid "{admin:metaover_group_metadata.shib13-sp-hosted}"
msgstr "Shib 1.3 服务提供者（本地）"

msgid "{admin:metadata_cert_intro}"
msgstr "下载X509证书作为PEM编码的文件"

msgid "{admin:debug_sending_message_msg_title}"
msgstr "信息"

msgid "{errors:title_UNKNOWNCERT}"
msgstr "未知的证书"

msgid "{errors:title_LDAPERROR}"
msgstr "LDAP错误"

msgid "{logout:failedsps}"
msgstr "无法从一个或者多个服务中退出，请确认你所有sessions已关闭，我们鼓励你 <i>关闭浏览器</i>"

msgid "{errors:descr_NOTFOUND}"
msgstr "没有找到给定的URL：%URL%"

msgid "{errors:howto_text}"
msgstr "这个错误可能是由于一些意想不到的行为或者是SimpleSAMLphp的配置错误导致的，请联系这个登录服务器的管理员并把上面的错误消息发送给他们"

msgid "{admin:metaover_group_metadata.shib13-idp-hosted}"
msgstr "Shib 1.3 认证提供者（本地）"

msgid "{errors:descr_NOTVALIDCERT}"
msgstr "你没有提交一个有效的证书"

msgid "{admin:debug_sending_message_text_button}"
msgstr "你准备发送一个消息，请点击提交按钮以继续"

msgid "{admin:metaover_optional_not_found}"
msgstr "下列必需的选项区域没有找到"

msgid "{logout:logout_only}"
msgstr "不，仅%SP%"

msgid "{login:next}"
msgstr "下一步"

msgid "{errors:descr_GENERATEAUTHNRESPONSE}"
msgstr "在这个身份提供者创建认证应答的时候发生了一个错误"

msgid "{disco:selectidp_full}"
msgstr "选择你要认证的身份提供者"

msgid "{errors:descr_NOTFOUNDREASON}"
msgstr "给定的页面没有找到，原因: %REASON%; URL: %URL%"

msgid "{errors:title_NOCERT}"
msgstr "无证书"

msgid "{errors:title_LOGOUTINFOLOST}"
msgstr "丢失了退出消息"

msgid "{admin:metaover_group_metadata.shib13-idp-remote}"
msgstr "Shib 1.3 认证提供者（远程）"

msgid "{errors:descr_CONFIG}"
msgstr "SimpleSAMLphp出现配置错误"

msgid "{admin:metadata_intro}"
msgstr "这里是SimpleSAMLphp为你生成的元信息，你应该发送这个元信息文档给你的信任的合作伙伴以建立信任的联盟"

msgid "{admin:metadata_cert}"
msgstr "证书"

msgid "{errors:descr_INVALIDCERT}"
msgstr "认证失败：你的浏览器发送的证书无效或者不能读取"

msgid "{status:header_shib}"
msgstr "Shibboleth演示"

msgid "{admin:metaconv_parse}"
msgstr "分析器"

msgid "Person's principal name at home organization"
msgstr "人的主要在家中组织的名称"

msgid "Superfluous options in config file"
msgstr "配置文件中拥有过多的选项"

msgid "Mobile"
msgstr "手机"

msgid "Shib 1.3 Service Provider (Hosted)"
msgstr "Shib 1.3 服务提供者（本地）"

msgid ""
"LDAP is the user database, and when you try to login, we need to contact "
"an LDAP database. An error occurred when we tried it this time."
msgstr "LDAP是一个用户数据库，当你试图登录时，我们需要连接到LDAP数据库，然而这次我们试图链接时发生了一个错误"

msgid ""
"Optionally enter your email address, for the administrators to be able "
"contact you for further questions about your issue:"
msgstr "你可以填入你的Email地址（当然你也可以选择不填），这样管理员就能够通过联系您来进一步的了解你的问题了"

msgid "Display name"
msgstr "显示名称"

msgid "Remember my choice"
msgstr "记住我的选择"

msgid "SAML 2.0 SP Metadata"
msgstr "SAML 2.0 SP 元信息"

msgid "Notices"
msgstr "通告"

msgid "Home telephone"
msgstr "家庭电话"

msgid ""
"Hi, this is the status page of SimpleSAMLphp. Here you can see if your "
"session is timed out, how long it lasts until it times out and all the "
"attributes that are attached to your session."
msgstr "嗨，这是SimpleSAMLphp状态页。这里你可以看到，如果您的会话超时，它持续多久，直到超时和连接到您的会话的所有属性。"

msgid "Explain what you did when this error occurred..."
msgstr "说明一下，你正在做什么的时候发生了这个错误"

msgid "An unhandled exception was thrown."
msgstr "抛出一个未处理的异常"

msgid "Invalid certificate"
msgstr "无效的证书"

msgid "Service Provider"
msgstr "服务提供者"

msgid "Incorrect username or password."
msgstr "错误的用户名或者密码"

msgid "There is an error in the request to this page. The reason was: %REASON%"
msgstr "请求该页的请求存在错误，原因：%REASON%"

msgid "E-mail address:"
msgstr "E-mail地址"

msgid "Submit message"
msgstr "提交信息"

msgid "No RelayState"
msgstr "无依赖状态"

msgid "Error creating request"
msgstr "创建请求出错"

msgid "Locality"
msgstr "位置"

msgid "Unhandled exception"
msgstr "未处理的异常"

msgid "The following required fields was not found"
msgstr "下列必需的区域没有找到"

msgid "Download the X509 certificates as PEM-encoded files."
msgstr "下载X509证书作为PEM编码的文件"

#, python-format
msgid "Unable to locate metadata for %ENTITYID%"
msgstr "无法为%ENTITYID%定位元信息"

msgid "Organizational number"
msgstr "组织号码"

msgid "Password not set"
msgstr "没有设置密码"

msgid "SAML 2.0 IdP Metadata"
msgstr "SAML 2.0 IdP 元信息"

msgid "Post office box"
msgstr "邮政信箱"

msgid ""
"A service has requested you to authenticate yourself. Please enter your "
"username and password in the form below."
msgstr "一个服务需要你的认证，请在下面输入你的用户名和密码"

msgid "CAS Error"
msgstr "CAS错误"

msgid ""
"The debug information below may be of interest to the administrator / "
"help desk:"
msgstr "管理员或者服务台可能对下面的调试信息很感兴趣"

msgid ""
"Either no user with the given username could be found, or the password "
"you gave was wrong. Please check the username and try again."
msgstr "如果不是给定的用户名没有找到就是给定的密码错误，请再次检查用户名和密码"

msgid "Error"
msgstr "错误"

msgid "Next"
msgstr "下一步"

msgid "Distinguished name (DN) of the person's home organizational unit"
msgstr "人的家组织单位的辨别名称（DN）"

msgid "State information lost"
msgstr "状态信息丢失"

msgid ""
"The password in the configuration (auth.adminpassword) is not changed "
"from the default value. Please edit the configuration file."
msgstr "你没有修改配置文件中的默认密码，请修改该密码"

msgid "Converted metadata"
msgstr "转换过的元信息"

msgid "Mail"
msgstr "邮箱"

msgid "No, cancel"
msgstr "没有"

msgid ""
"You have chosen <b>%HOMEORG%</b> as your home organization. If this is "
"wrong you may choose another one."
msgstr "你选择了<b>%HOMEORG%</b>作为你的家庭组织。如果错了请选择其他的"

msgid "Error processing request from Service Provider"
msgstr "处理来自服务提供者的请求时出错"

msgid "Distinguished name (DN) of person's primary Organizational Unit"
msgstr "人的主要组织单位的辨别名称（DN）"

msgid ""
"To look at the details for an SAML entity, click on the SAML entity "
"header."
msgstr "想要查看SAML实体的详细情况，请点击SAML实体载入器"

msgid "Enter your username and password"
msgstr "输入你的用户名和密码"

msgid "Login at"
msgstr "登录于"

msgid "No"
msgstr "不"

msgid "Home postal address"
msgstr "家庭邮政地址"

msgid "WS-Fed SP Demo Example"
msgstr "WS-Fed SP 演示案例"

msgid "SAML 2.0 Identity Provider (Remote)"
msgstr "SAML 2.0 身份提供者（远程）"

msgid "Error processing the Logout Request"
msgstr "处理退出请求时发生错误"

msgid "Do you want to logout from all the services above?"
msgstr "你想同时从上面的这些服务中退出吗？"

msgid "Select"
msgstr "选择"

msgid "The authentication was aborted by the user"
msgstr "认证被用户中止"

msgid "Your attributes"
msgstr "你的属性"

msgid "Given name"
msgstr "名"

msgid "Identity assurance profile"
msgstr "可靠验证配置文件"

msgid "SAML 2.0 SP Demo Example"
msgstr "SAML 2.0 SP演示案例"

msgid "Logout information lost"
msgstr "丢失了退出消息"

msgid "Organization name"
msgstr "组织名称"

msgid "Authentication failed: the certificate your browser sent is unknown"
msgstr "认证失败：你的浏览器发送的是未知的证书"

msgid ""
"You are about to send a message. Hit the submit message button to "
"continue."
msgstr "你准备发送一个消息，请点击提交按钮以继续"

msgid "Home organization domain name"
msgstr "首页组织的域名"

msgid "Go back to the file list"
msgstr "返回至文件列表"

msgid "Error report sent"
msgstr "发送错误报告"

msgid "Common name"
msgstr "常用名字"

msgid "Please select the identity provider where you want to authenticate:"
msgstr "选择你要认证的身份提供者"

msgid "Logout failed"
msgstr "退出失败"

msgid "Identity number assigned by public authorities"
msgstr "身份证号码"

msgid "WS-Federation Identity Provider (Remote)"
msgstr "WS-Federation 身份提供者（远程）"

msgid "Error received from Identity Provider"
msgstr "从身份提供者收到一个错误"

msgid "LDAP Error"
msgstr "LDAP错误"

msgid ""
"The information about the current logout operation has been lost. You "
"should return to the service you were trying to log out from and try to "
"log out again. This error can be caused by the logout information "
"expiring. The logout information is stored for a limited amout of time - "
"usually a number of hours. This is longer than any normal logout "
"operation should take, so this error may indicate some other error with "
"the configuration. If the problem persists, contact your service "
"provider."
msgstr "关于当前退出操作的相关信息丢失了，你应该返回服务中，重新尝试退出，这个错误可能是退出信息超时引起的。退出消息在有限的时间内存储，通常是几个小时，这比任何常规的退出时间所需的时间要长多了，所以这种错误可能是配置错误，如果问题依旧存在，联系你的服务提供商"

msgid "Some error occurred"
msgstr "某些错误发生了"

msgid "Organization"
msgstr "组织"

msgid "No certificate"
msgstr "无证书"

msgid "Choose home organization"
msgstr "选择你的家庭组织"

msgid "Persistent pseudonymous ID"
msgstr "持续的匿名ID"

msgid "No SAML response provided"
msgstr "没有提供SAML应答"

msgid "No errors found."
msgstr "没有发现错误"

msgid "SAML 2.0 Service Provider (Hosted)"
msgstr "SAML 2.0 服务提供者（本地）"

msgid "The given page was not found. The URL was: %URL%"
msgstr "没有找到给定的URL：%URL%"

msgid "Configuration error"
msgstr "配置错误"

msgid "Required fields"
msgstr "必需的区域"

msgid "An error occurred when trying to create the SAML request."
msgstr "在创建SAML请求中发生了一个错误"

msgid ""
"This error probably is due to some unexpected behaviour or to "
"misconfiguration of SimpleSAMLphp. Contact the administrator of this "
"login service, and send them the error message above."
msgstr "这个错误可能是由于一些意想不到的行为或者是SimpleSAMLphp的配置错误导致的，请联系这个登录服务器的管理员并把上面的错误消息发送给他们"

#, python-format
msgid "Your session is valid for %remaining% seconds from now."
msgstr "你的会话在%remaining%秒内有效"

msgid "Domain component (DC)"
msgstr "Opened the web browser with tabs saved from the previous session.域组件（DC）"

msgid "Shib 1.3 Service Provider (Remote)"
msgstr "Shib 1.3 服务提供者（远程）"

msgid "Password"
msgstr "密码"

msgid "Nickname"
msgstr "昵称"

msgid "Send error report"
msgstr "发送错误报告"

msgid ""
"Authentication failed: the certificate your browser sent is invalid or "
"cannot be read"
msgstr "认证失败：你的浏览器发送的证书无效或者不能读取"

msgid "The error report has been sent to the administrators."
msgstr "错误报告已经发送给管理员"

msgid "Date of birth"
msgstr "生日"

msgid "Private information elements"
msgstr "个人资料"

msgid "You are also logged in on these services:"
msgstr "你同时登录这以下这些服务"

msgid "SimpleSAMLphp Diagnostics"
msgstr "SimpleSAMLphp 诊断"

msgid "Debug information"
msgstr "调试信息"

msgid "No, only %SP%"
msgstr "不，仅%SP%"

msgid "Username"
msgstr "用户名"

msgid "Go back to SimpleSAMLphp installation page"
msgstr "返回SimpleSAMLphp安装页面"

msgid "You have successfully logged out from all services listed above."
msgstr "你成功的退出了上面列表中的服务"

msgid "You are now successfully logged out from %SP%."
msgstr "你已成功从%SP%退出"

msgid "Affiliation"
msgstr "联络方式"

msgid "You have been logged out."
msgstr "你已经退出了"

msgid "Return to service"
msgstr "返回至服务"

msgid "Logout"
msgstr "退出"

msgid "State information lost, and no way to restart the request"
msgstr "状态信息丢失，并且无法重新请求"

msgid "Error processing response from Identity Provider"
msgstr "处理来自身份提供者的应答时出错"

msgid "WS-Federation Service Provider (Hosted)"
msgstr "WS-Federation 服务提供者（本地）"

msgid "Preferred language"
msgstr "首选语言"

msgid "SAML 2.0 Service Provider (Remote)"
msgstr "SAML 2.0 服务提供者 (远程)"

msgid "Surname"
msgstr "姓"

msgid "No access"
msgstr "禁止访问"

msgid "The following fields was not recognized"
msgstr "下列区域无法识别"

msgid "Authentication error in source %AUTHSOURCE%. The reason was: %REASON%"
msgstr "认证源%AUTHSOURCE%存在错误， 原因: %REASON%"

msgid "Bad request received"
msgstr "收到了错误的请求"

msgid "User ID"
msgstr "用户ID"

msgid "JPEG Photo"
msgstr "JPEG图片"

msgid "Postal address"
msgstr "邮政地址"

msgid "An error occurred when trying to process the Logout Request."
msgstr "试图处理退出请求时发生了一个错误"

msgid "Sending message"
msgstr "正在发送消息"

msgid "In SAML 2.0 Metadata XML format:"
msgstr "在SAML 2.0 XML 元信息格式中："

msgid "Logging out of the following services:"
msgstr "从下列服务中退出"

msgid ""
"When this identity provider tried to create an authentication response, "
"an error occurred."
msgstr "在这个身份提供者创建认证应答的时候发生了一个错误"

msgid "Could not create authentication response"
msgstr "无法创建认证应答"

msgid "Labeled URI"
msgstr "标签URI"

msgid "SimpleSAMLphp appears to be misconfigured."
msgstr "SimpleSAMLphp出现配置错误"

msgid "Shib 1.3 Identity Provider (Hosted)"
msgstr "Shib 1.3 认证提供者（本地）"

msgid "Metadata"
msgstr "元信息"

msgid "Login"
msgstr "登录"

msgid ""
"This Identity Provider received an Authentication Request from a Service "
"Provider, but an error occurred when trying to process the request."
msgstr "该身份提供者接受到了一个来自服务提供者的认证请求，但是在试图处理该请求的过程中发生了错误"

msgid "Yes, all services"
msgstr "是的，所有的服务"

msgid "Logged out"
msgstr "退出"

msgid "Postal code"
msgstr "邮政编码"

msgid "Logging out..."
msgstr "正在退出"

msgid "Metadata not found"
msgstr "没有找到元信息"

msgid "SAML 2.0 Identity Provider (Hosted)"
msgstr "SAML 2.0 身份提供者（本地）"

msgid "Primary affiliation"
msgstr "主要的联系方式"

msgid ""
"If you report this error, please also report this tracking number which "
"makes it possible to locate your session in the logs available to the "
"system administrator:"
msgstr "如果你报告了这个错误，那么请你也报告这个追踪号码，系统管理员有可能根据这个号码在日志中定位你的SESSION"

msgid "XML metadata"
msgstr "XML元信息"

msgid ""
"The parameters sent to the discovery service were not according to "
"specifications."
msgstr "发送给搜寻服务的参数不符合规范"

msgid "Telephone number"
msgstr "电话号码"

msgid ""
"Unable to log out of one or more services. To ensure that all your "
"sessions are closed, you are encouraged to <i>close your webbrowser</i>."
msgstr "无法从一个或者多个服务中退出，请确认你所有sessions已关闭，我们鼓励你 <i>关闭浏览器</i>"

msgid "Bad request to discovery service"
msgstr "错误的搜寻服务请求"

msgid "Select your identity provider"
msgstr "选择你的身份提供者"

msgid "Entitlement regarding the service"
msgstr "关于服务的权利"

msgid "Shib 1.3 SP Metadata"
msgstr "Shib 1.3 SP 元信息"

msgid ""
"As you are in debug mode, you get to see the content of the message you "
"are sending:"
msgstr "当你处在调试模式中时，你将看到你正在发送的消息的内容"

msgid "Certificates"
msgstr "证书"

msgid "Remember"
msgstr "记住"

msgid "Distinguished name (DN) of person's home organization"
msgstr "人的家庭组织的分辨名称（DN）"

msgid "You are about to send a message. Hit the submit message link to continue."
msgstr "你准备发送一个消息，请点击提交链接以继续"

msgid "Organizational unit"
msgstr "组织单位"

msgid "Authentication aborted"
msgstr "认证中止"

msgid "Local identity number"
msgstr "本地身份号码"

msgid "Report errors"
msgstr "报告错误"

msgid "Page not found"
msgstr "页面没有找到"

msgid "Shib 1.3 IdP Metadata"
msgstr "Shib 1.3 IdP 元信息"

msgid "Change your home organization"
msgstr "改变你的家庭组织"

msgid "User's password hash"
msgstr "用户密码的HASH值"

msgid ""
"In SimpleSAMLphp flat file format - use this if you are using a "
"SimpleSAMLphp entity on the other side:"
msgstr "如果你想在其他网站使用的SimpleSAMLphp，那么你应该使用SimpleSAMLphp扁平的文件格式"

msgid "Yes, continue"
msgstr "是的，继续"

msgid "Completed"
msgstr "完成"

msgid ""
"The Identity Provider responded with an error. (The status code in the "
"SAML Response was not success)"
msgstr "身份提供者的应答存在错误（SAML应答状态码并没有成功）"

msgid "Error loading metadata"
msgstr "载入元信息时发生错误"

msgid "Select configuration file to check:"
msgstr "选择一个配置文件用于检测"

msgid "On hold"
msgstr "保持"

msgid "Error when communicating with the CAS server."
msgstr "在和CAS服务器的通讯中发生了错误"

msgid "No SAML message provided"
msgstr "没有提供SAML消息"

msgid "Help! I don't remember my password."
msgstr "帮助！我忘记我的密码了！"

msgid ""
"You can turn off debug mode in the global SimpleSAMLphp configuration "
"file <tt>config/config.php</tt>."
msgstr "你可以关闭调试模式，在SimpleSAMLphp全局配置文件<tt>config/config.php</tt>中"

msgid "How to get help"
msgstr "如何获取帮助"

msgid ""
"You accessed the SingleLogoutService interface, but did not provide a "
"SAML LogoutRequest or LogoutResponse. Please note that this endpoint is "
"not intended to be accessed directly."
msgstr "你访问了SingleLogoutService接口，但是并没有提供一个SAML的LogoutRequest或者LogoutResponse"

msgid "SimpleSAMLphp error"
msgstr "SimpleSAMLphp错误"

msgid ""
"One or more of the services you are logged into <i>do not support "
"logout</i>. To ensure that all your sessions are closed, you are "
"encouraged to <i>close your webbrowser</i>."
msgstr "一个或多个你已登录的服务<i>不支持退出</i>，请确认你所有sessions已关闭，我们鼓励你 <i>关闭浏览器</i>"

msgid "Organization's legal name"
msgstr "组织的法定名称"

msgid "Options missing from config file"
msgstr "配置文件中选项缺失"

msgid "The following optional fields was not found"
msgstr "下列必需的选项区域没有找到"

msgid "Authentication failed: your browser did not send any certificate"
msgstr "认证失败，你的浏览器没有发送任何证书"

msgid ""
"This endpoint is not enabled. Check the enable options in your "
"configuration of SimpleSAMLphp."
msgstr "这个端点没有启用，请在SimpleSAMLphp的配置选项中选中启用"

msgid "You can <a href=\"%METAURL%\">get the metadata xml on a dedicated URL</a>:"
msgstr "你可以在 <a href=\"%METAURL%\">获取元信息XML</a>"

msgid "Street"
msgstr "街道"

msgid ""
"There is some misconfiguration of your SimpleSAMLphp installation. If you"
" are the administrator of this service, you should make sure your "
"metadata configuration is correctly setup."
msgstr "你的SimpleSAMLphp配置错误，如果你是该服务的管理员，那么请确认你的配置是正确的"

msgid "Incorrect username or password"
msgstr "不正确的用户名或密码"

msgid "Message"
msgstr "信息"

msgid "Contact information:"
msgstr "联系方式"

msgid "Unknown certificate"
msgstr "未知的证书"

msgid "Legal name"
msgstr "正式名称"

msgid "Optional fields"
msgstr "选项区域"

msgid ""
"The initiator of this request did not provide a RelayState parameter "
"indicating where to go next."
msgstr "这个请求的发起人没有提供RelayState参数，以说明下一步处理"

msgid "You have previously chosen to authenticate at"
msgstr "你先前选择的认证"

msgid ""
"You sent something to the login page, but for some reason the password "
"was not sent. Try again please."
msgstr "你确实发送了一些信息给登录页面，但由于某些原因，你没有发送密码，请再试一次"

msgid "Fax number"
msgstr "传真号码"

msgid "Shibboleth demo"
msgstr "Shibboleth演示"

msgid "Error in this metadata entry"
msgstr "该元信息实体存在错误"

msgid "Session size: %SIZE%"
msgstr "Session 大小: %SIZE%"

msgid "Parse"
msgstr "分析器"

msgid ""
"Without your username and password you cannot authenticate "
"yourself for access to the service. There may be someone that can help "
"you. Consult the help desk at your organization!"
msgstr "太糟糕了！-没有你的用户名和密码你将不能访问该服务，也许有人能够帮助你，请咨询你所在大学的服务台"

msgid "Metadata parser"
msgstr "元信息分析器"

msgid "Choose your home organization"
msgstr "选择你的组织"

msgid "Send e-mail to help desk"
msgstr "发送Email给服务台"

msgid "Metadata overview"
msgstr "元信息浏览"

msgid "Title"
msgstr "标题"

msgid "Manager"
msgstr "管理员"

msgid "You did not present a valid certificate."
msgstr "你没有提交一个有效的证书"

msgid "Authentication source error"
msgstr "认证源错误"

msgid "Affiliation at home organization"
msgstr "家庭联络地址"

msgid "Help desk homepage"
msgstr "服务台的主页"

msgid "Configuration check"
msgstr "配置检查"

msgid "We did not accept the response sent from the Identity Provider."
msgstr "我们不接受来自身份提供者的应答"

msgid "The given page was not found. The reason was: %REASON%  The URL was: %URL%"
msgstr "给定的页面没有找到，原因: %REASON%; URL: %URL%"

msgid "Shib 1.3 Identity Provider (Remote)"
msgstr "Shib 1.3 认证提供者（远程）"

msgid ""
"Here is the metadata that SimpleSAMLphp has generated for you. You may "
"send this metadata document to trusted partners to setup a trusted "
"federation."
msgstr "这里是SimpleSAMLphp为你生成的元信息，你应该发送这个元信息文档给你的信任的合作伙伴以建立信任的联盟"

msgid "[Preferred choice]"
msgstr "首选选项"

msgid "Organizational homepage"
msgstr "组织的首页"

msgid ""
"You accessed the Assertion Consumer Service interface, but did not "
"provide a SAML Authentication Response. Please note that this endpoint is"
" not intended to be accessed directly."
msgstr "你访问了Assertion Consumer Service接口，但是并没有提供一个SAML认证应答"


msgid ""
"You are now accessing a pre-production system. This authentication setup "
"is for testing and pre-production verification only. If someone sent you "
"a link that pointed you here, and you are not <i>a tester</i> you "
"probably got the wrong link, and should <b>not be here</b>."
msgstr "你现在访问的是一个预安装系统，这个认证设置是用来测试和预发布校验之用。如果有人发送一个连接让你访问到这里，并且你又不是<i>测试人员</i>，那么你获得了一个错误连接，并且你<b>不应该在这里</b>"
