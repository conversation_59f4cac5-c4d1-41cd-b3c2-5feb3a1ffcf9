<?php
$attributemap = [
    'aRecord'                       => 'urn:oid:0.9.2342.********.100.1.26',
    'aliasedEntryName'              => 'urn:oid:2.5.4.1',
    'aliasedObjectName'             => 'urn:oid:2.5.4.1',
    'associatedDomain'              => 'urn:oid:0.9.2342.********.100.1.37',
    'associatedName'                => 'urn:oid:0.9.2342.********.100.1.38',
    'audio'                         => 'urn:oid:0.9.2342.********.100.1.55',
    'authorityRevocationList'       => 'urn:oid:*******8',
    'buildingName'                  => 'urn:oid:0.9.2342.********.100.1.48',
    'businessCategory'              => 'urn:oid:2.5.4.15',
    'c'                             => 'urn:oid:*******',
    'cACertificate'                 => 'urn:oid:*******7',
    'cNAMERecord'                   => 'urn:oid:0.9.2342.********.100.1.31',
    'carLicense'                    => 'urn:oid:2.16.840.1.113730.3.1.1',
    'certificateRevocationList'     => 'urn:oid:*******9',
    'cn'                            => 'urn:oid:*******',
    'co'                            => 'urn:oid:0.9.2342.********.100.1.43',
    'commonName'                    => 'urn:oid:*******',
    'countryName'                   => 'urn:oid:*******',
    'crossCertificatePair'          => 'urn:oid:********',
    'dITRedirect'                   => 'urn:oid:0.9.2342.********.100.1.54',
    'dSAQuality'                    => 'urn:oid:0.9.2342.********.100.1.49',
    'dc'                            => 'urn:oid:0.9.2342.********.100.1.25',
    'deltaRevocationList'           => 'urn:oid:********',
    'departmentNumber'              => 'urn:oid:2.16.840.1.113730.3.1.2',
    'description'                   => 'urn:oid:********',
    'destinationIndicator'          => 'urn:oid:********',
    'displayName'                   => 'urn:oid:2.16.840.1.113730.3.1.241',
    'distinguishedName'             => 'urn:oid:********',
    'dmdName'                       => 'urn:oid:********',
    'dnQualifier'                   => 'urn:oid:********',
    'documentAuthor'                => 'urn:oid:0.9.2342.********.100.1.14',
    'documentIdentifier'            => 'urn:oid:0.9.2342.********.100.1.11',
    'documentLocation'              => 'urn:oid:0.9.2342.********.100.1.15',
    'documentPublisher'             => 'urn:oid:0.9.2342.********.100.1.56',
    'documentTitle'                 => 'urn:oid:0.9.2342.********.100.1.12',
    'documentVersion'               => 'urn:oid:0.9.2342.********.100.1.13',
    'domainComponent'               => 'urn:oid:0.9.2342.********.100.1.25',
    'drink'                         => 'urn:oid:0.9.2342.********.100.1.5',
    'eduOrgHomePageURI'             => 'urn:oid:*******.4.1.5923.1.2.1.2',
    'eduOrgIdentityAuthNPolicyURI'  => 'urn:oid:*******.4.1.5923.1.2.1.3',
    'eduOrgLegalName'               => 'urn:oid:*******.4.1.5923.1.2.1.4',
    'eduOrgSuperiorURI'             => 'urn:oid:*******.4.1.5923.1.2.1.5',
    'eduOrgWhitePagesURI'           => 'urn:oid:*******.4.1.5923.1.2.1.6',
    'eduPersonAffiliation'          => 'urn:oid:*******.4.1.5923.1.1.1.1',
    'eduPersonAssurance'            => 'urn:oid:*******.4.1.5923.********',
    'eduPersonEntitlement'          => 'urn:oid:*******.4.1.5923.*******',
    'eduPersonNickname'             => 'urn:oid:*******.4.1.5923.*******',
    'eduPersonOrgDN'                => 'urn:oid:*******.4.1.5923.*******',
    'eduPersonOrgUnitDN'            => 'urn:oid:*******.4.1.5923.*******',
    'eduPersonPrimaryAffiliation'   => 'urn:oid:*******.4.1.5923.*******',
    'eduPersonPrimaryOrgUnitDN'     => 'urn:oid:*******.4.1.5923.*******',
    'eduPersonPrincipalName'        => 'urn:oid:*******.4.1.5923.*******',
    'eduPersonScopedAffiliation'    => 'urn:oid:*******.4.1.5923.*******',
    'eduPersonTargetedID'           => 'urn:oid:*******.4.1.5923.********',
    'eduPersonUniqueId'             => 'urn:oid:*******.4.1.5923.********',
    'eduPersonOrcid'                => 'urn:oid:*******.4.1.5923.********',
    'email'                         => 'urn:oid:1.2.840.113549.1.9.1',
    'emailAddress'                  => 'urn:oid:1.2.840.113549.1.9.1',
    'employeeNumber'                => 'urn:oid:2.16.840.1.113730.3.1.3',
    'employeeType'                  => 'urn:oid:2.16.840.1.113730.3.1.4',
    'enhancedSearchGuide'           => 'urn:oid:********',
    'facsimileTelephoneNumber'      => 'urn:oid:*******3',
    'favouriteDrink'                => 'urn:oid:0.9.2342.********.100.1.5',
    'fax'                           => 'urn:oid:*******3',
    'federationFeideSchemaVersion'  => 'urn:oid:*******.4.1.2428.90.1.9',
    'friendlyCountryName'           => 'urn:oid:0.9.2342.********.100.1.43',
    'generationQualifier'           => 'urn:oid:********',
    'givenName'                     => 'urn:oid:********',
    'gn'                            => 'urn:oid:********',
    'homePhone'                     => 'urn:oid:0.9.2342.********.100.1.20',
    'homePostalAddress'             => 'urn:oid:0.9.2342.********.100.1.39',
    'homeTelephoneNumber'           => 'urn:oid:0.9.2342.********.100.1.20',
    'host'                          => 'urn:oid:0.9.2342.********.100.1.9',
    'houseIdentifier'               => 'urn:oid:********',
    'info'                          => 'urn:oid:0.9.2342.********.100.1.4',
    'initials'                      => 'urn:oid:********',
    'internationaliSDNNumber'       => 'urn:oid:********',
    'isMemberOf'                    => 'urn:oid:*******.4.1.5923.*******',
    'janetMailbox'                  => 'urn:oid:0.9.2342.********.100.1.46',
    'jpegPhoto'                     => 'urn:oid:0.9.2342.********.100.1.60',
    'knowledgeInformation'          => 'urn:oid:*******',
    'l'                             => 'urn:oid:*******',
    'labeledURI'                    => 'urn:oid:*******.*********.57',
    'localityName'                  => 'urn:oid:*******',
    'mDRecord'                      => 'urn:oid:0.9.2342.********.100.1.27',
    'mXRecord'                      => 'urn:oid:0.9.2342.********.100.1.28',
    'mail'                          => 'urn:oid:0.9.2342.********.100.1.3',
    'mailPreferenceOption'          => 'urn:oid:0.9.2342.********.100.1.47',
    'manager'                       => 'urn:oid:0.9.2342.********.100.1.10',
    'member'                        => 'urn:oid:*******1',
    'mobile'                        => 'urn:oid:0.9.2342.********.100.1.41',
    'mobileTelephoneNumber'         => 'urn:oid:0.9.2342.********.100.1.41',
    'nSRecord'                      => 'urn:oid:0.9.2342.********.100.1.29',
    'name'                          => 'urn:oid:*******1',
    'norEduOrgAcronym'              => 'urn:oid:*******.4.1.2428.90.1.6',
    'norEduOrgNIN'                  => 'urn:oid:*******.4.1.2428.90.1.12',
    'norEduOrgSchemaVersion'        => 'urn:oid:*******.4.1.2428.90.1.11',
    'norEduOrgUniqueIdentifier'     => 'urn:oid:*******.4.1.2428.90.1.7',
    'norEduOrgUniqueNumber'         => 'urn:oid:*******.4.1.2428.90.1.1',
    'norEduOrgUnitUniqueIdentifier' => 'urn:oid:*******.4.1.2428.90.1.8',
    'norEduOrgUnitUniqueNumber'     => 'urn:oid:*******.4.1.2428.90.1.2',
    'norEduPersonBirthDate'         => 'urn:oid:*******.4.1.2428.90.1.3',
    'norEduPersonLIN'               => 'urn:oid:*******.4.1.2428.90.1.4',
    'norEduPersonNIN'               => 'urn:oid:*******.4.1.2428.90.1.5',
    'o'                             => 'urn:oid:2.5.4.10',
    'objectClass'                   => 'urn:oid:2.5.4.0',
    'organizationName'              => 'urn:oid:2.5.4.10',
    'organizationalStatus'          => 'urn:oid:0.9.2342.********.100.1.45',
    'organizationalUnitName'        => 'urn:oid:2.5.4.11',
    'otherMailbox'                  => 'urn:oid:0.9.2342.********.100.1.22',
    'ou'                            => 'urn:oid:2.5.4.11',
    'owner'                         => 'urn:oid:*******2',
    'pager'                         => 'urn:oid:0.9.2342.********.100.1.42',
    'pagerTelephoneNumber'          => 'urn:oid:0.9.2342.********.100.1.42',
    'personalSignature'             => 'urn:oid:0.9.2342.********.100.1.53',
    'personalTitle'                 => 'urn:oid:0.9.2342.********.100.1.40',
    'photo'                         => 'urn:oid:0.9.2342.********.100.1.7',
    'physicalDeliveryOfficeName'    => 'urn:oid:********',
    'pkcs9email'                    => 'urn:oid:1.2.840.113549.1.9.1',
    'postOfficeBox'                 => 'urn:oid:********',
    'postalAddress'                 => 'urn:oid:********',
    'postalCode'                    => 'urn:oid:********',
    'preferredDeliveryMethod'       => 'urn:oid:*******8',
    'preferredLanguage'             => 'urn:oid:2.16.840.1.113730.3.1.39',
    'presentationAddress'           => 'urn:oid:*******9',
    'protocolInformation'           => 'urn:oid:********',
    'pseudonym'                     => 'urn:oid:*******5',
    'registeredAddress'             => 'urn:oid:*******6',
    'rfc822Mailbox'                 => 'urn:oid:0.9.2342.********.100.1.3',
    'roleOccupant'                  => 'urn:oid:*******3',
    'roomNumber'                    => 'urn:oid:0.9.2342.********.100.1.6',
    'sOARecord'                     => 'urn:oid:0.9.2342.********.100.1.30',
    'schacCountryOfCitizenship'     => 'urn:oid:*******.4.1.25178.1.2.5',
    'schacCountryOfResidence'       => 'urn:oid:*******.4.1.25178.1.2.11',
    'schacDateOfBirth'              => 'urn:oid:*******.4.1.25178.1.2.3',
    'schacExpiryDate'               => 'urn:oid:*******.4.1.25178.1.2.17',
    'schacGender'                   => 'urn:oid:*******.4.1.25178.1.2.2',
    'schacHomeOrganization'         => 'urn:oid:*******.4.1.25178.1.2.9',
    'schacHomeOrganizationType'     => 'urn:oid:*******.4.1.25178.1.2.10',
    'schacMotherTongue'             => 'urn:oid:*******.4.1.25178.1.2.1',
    'schacPersonalPosition'         => 'urn:oid:*******.4.1.25178.1.2.13',
    'schacPersonalTitle'            => 'urn:oid:*******.4.1.25178.1.2.8',
    'schacPersonalUniqueCode'       => 'urn:oid:*******.4.1.25178.1.2.14',
    'schacPersonalUniqueID'         => 'urn:oid:*******.4.1.25178.1.2.15',
    'schacPlaceOfBirth'             => 'urn:oid:*******.4.1.25178.1.2.4',
    'schacProjectMembership'        => 'urn:oid:*******.4.1.25178.1.2.20',
    'schacProjectSpecificRole'      => 'urn:oid:*******.4.1.25178.1.2.21',
    'schacSn1'                      => 'urn:oid:*******.4.1.25178.1.2.6',
    'schacSn2'                      => 'urn:oid:*******.4.1.25178.1.2.7',
    'schacUserPresenceID'           => 'urn:oid:*******.4.1.25178.1.2.12',
    'schacUserPrivateAttribute'     => 'urn:oid:*******.4.1.25178.1.2.18',
    'schacUserStatus'               => 'urn:oid:*******.4.1.25178.1.2.19',
    'schacYearOfBirth'              => 'urn:oid:*******.4.1.25178.*******',
    'searchGuide'                   => 'urn:oid:********',
    'secretary'                     => 'urn:oid:0.9.2342.********.100.1.21',
    'seeAlso'                       => 'urn:oid:*******4',
    'serialNumber'                  => 'urn:oid:*******',
    'singleLevelQuality'            => 'urn:oid:0.9.2342.********.100.1.50',
    'sisSchoolGrade'                => 'urn:oid:1.2.752.**********',
    'sisLegalGuardianFor'           => 'urn:oid:1.2.752.**********',
    'sisOrgDepartment'              => 'urn:oid:1.2.752.194.10.3',
    'sisSchoolUnitCode'             => 'urn:oid:1.2.752.**********',
    'sn'                            => 'urn:oid:*******',
    'sshPublicKey'                  => 'urn:oid:*******.4.1.24552.500.********',
    'st'                            => 'urn:oid:*******',
    'stateOrProvinceName'           => 'urn:oid:*******',
    'street'                        => 'urn:oid:*******',
    'streetAddress'                 => 'urn:oid:*******',
    'subtreeMaximumQuality'         => 'urn:oid:0.9.2342.********.100.1.52',
    'subtreeMinimumQuality'         => 'urn:oid:0.9.2342.********.100.1.51',
    'supportedAlgorithms'           => 'urn:oid:********',
    'supportedApplicationContext'   => 'urn:oid:*******0',
    'surname'                       => 'urn:oid:*******',
    'telephoneNumber'               => 'urn:oid:*******0',
    'teletexTerminalIdentifier'     => 'urn:oid:*******2',
    'telexNumber'                   => 'urn:oid:*******1',
    'textEncodedORAddress'          => 'urn:oid:0.9.2342.********.100.1.2',
    'title'                         => 'urn:oid:********',
    'uid'                           => 'urn:oid:0.9.2342.********.100.1.1',
    'uniqueIdentifier'              => 'urn:oid:0.9.2342.********.100.1.44',
    'uniqueMember'                  => 'urn:oid:********',
    'userCertificate'               => 'urn:oid:*******6',
    'userClass'                     => 'urn:oid:0.9.2342.********.100.1.8',
    'userPKCS12'                    => 'urn:oid:2.16.840.1.113730.3.1.216',
    'userPassword'                  => 'urn:oid:*******5',
    'userSMIMECertificate'          => 'urn:oid:2.16.840.1.113730.3.1.40',
    'userid'                        => 'urn:oid:0.9.2342.********.100.1.1',
    'x121Address'                   => 'urn:oid:*******4',
    'x500UniqueIdentifier'          => 'urn:oid:********',
];
