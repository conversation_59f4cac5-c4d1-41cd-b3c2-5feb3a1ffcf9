<?php
$attributemap = [
    'urn:oid:0.9.2342.19200300.100.1.1'  => 'uid',
    'urn:oid:0.9.2342.19200300.100.1.10' => 'manager',
    'urn:oid:0.9.2342.19200300.100.1.11' => 'documentIdentifier',
    'urn:oid:0.9.2342.19200300.100.1.12' => 'documentTitle',
    'urn:oid:0.9.2342.19200300.100.1.13' => 'documentVersion',
    'urn:oid:0.9.2342.19200300.100.1.14' => 'documentAuthor',
    'urn:oid:0.9.2342.19200300.100.1.15' => 'documentLocation',
    'urn:oid:0.9.2342.19200300.100.1.2'  => 'textEncodedORAddress',
    'urn:oid:0.9.2342.19200300.100.1.20' => 'homePhone',
    'urn:oid:0.9.2342.19200300.100.1.21' => 'secretary',
    'urn:oid:0.9.2342.19200300.100.1.22' => 'otherMailbox',
    'urn:oid:0.9.2342.19200300.100.1.25' => 'dc',
    'urn:oid:0.9.2342.19200300.100.1.26' => 'aRecord',
    'urn:oid:0.9.2342.19200300.100.1.27' => 'mDRecord',
    'urn:oid:0.9.2342.19200300.100.1.28' => 'mXRecord',
    'urn:oid:0.9.2342.19200300.100.1.29' => 'nSRecord',
    'urn:oid:0.9.2342.19200300.100.1.3'  => 'mail',
    'urn:oid:0.9.2342.19200300.100.1.30' => 'sOARecord',
    'urn:oid:0.9.2342.19200300.100.1.31' => 'cNAMERecord',
    'urn:oid:0.9.2342.19200300.100.1.37' => 'associatedDomain',
    'urn:oid:0.9.2342.19200300.100.1.38' => 'associatedName',
    'urn:oid:0.9.2342.19200300.100.1.39' => 'homePostalAddress',
    'urn:oid:0.9.2342.19200300.100.1.4'  => 'info',
    'urn:oid:0.9.2342.19200300.100.1.40' => 'personalTitle',
    'urn:oid:0.9.2342.19200300.100.1.41' => 'mobile',
    'urn:oid:0.9.2342.19200300.100.1.42' => 'pager',
    'urn:oid:0.9.2342.19200300.100.1.43' => 'co',
    'urn:oid:0.9.2342.19200300.100.1.44' => 'uniqueIdentifier',
    'urn:oid:0.9.2342.19200300.100.1.45' => 'organizationalStatus',
    'urn:oid:0.9.2342.19200300.100.1.46' => 'janetMailbox',
    'urn:oid:0.9.2342.19200300.100.1.47' => 'mailPreferenceOption',
    'urn:oid:0.9.2342.19200300.100.1.48' => 'buildingName',
    'urn:oid:0.9.2342.19200300.100.1.49' => 'dSAQuality',
    'urn:oid:0.9.2342.19200300.100.1.5'  => 'drink',
    'urn:oid:0.9.2342.19200300.100.1.50' => 'singleLevelQuality',
    'urn:oid:0.9.2342.19200300.100.1.51' => 'subtreeMinimumQuality',
    'urn:oid:0.9.2342.19200300.100.1.52' => 'subtreeMaximumQuality',
    'urn:oid:0.9.2342.19200300.100.1.53' => 'personalSignature',
    'urn:oid:0.9.2342.19200300.100.1.54' => 'dITRedirect',
    'urn:oid:0.9.2342.19200300.100.1.55' => 'audio',
    'urn:oid:0.9.2342.19200300.100.1.56' => 'documentPublisher',
    'urn:oid:0.9.2342.19200300.100.1.6'  => 'roomNumber',
    'urn:oid:0.9.2342.19200300.100.1.60' => 'jpegPhoto',
    'urn:oid:0.9.2342.19200300.100.1.7'  => 'photo',
    'urn:oid:0.9.2342.19200300.100.1.8'  => 'userClass',
    'urn:oid:0.9.2342.19200300.100.1.9'  => 'host',
    'urn:oid:1.2.840.113549.1.9.1'       => 'email',
    'urn:oid:1.2.752.**********'         => 'sisSchoolGrade',
    'urn:oid:1.2.752.**********'         => 'sisLegalGuardianFor',
    'urn:oid:1.2.752.194.10.3'           => 'sisOrgDepartment',
    'urn:oid:1.2.752.**********'         => 'sisSchoolUnitCode',
    'urn:oid:*******.4.1.2428.90.1.1'    => 'norEduOrgUniqueNumber',
    'urn:oid:*******.4.1.2428.90.1.11'   => 'norEduOrgSchemaVersion',
    'urn:oid:*******.4.1.2428.90.1.12'   => 'norEduOrgNIN',
    'urn:oid:*******.4.1.2428.90.1.2'    => 'norEduOrgUnitUniqueNumber',
    'urn:oid:*******.4.1.2428.90.1.3'    => 'norEduPersonBirthDate',
    'urn:oid:*******.4.1.2428.90.1.4'    => 'norEduPersonLIN',
    'urn:oid:*******.4.1.2428.90.1.5'    => 'norEduPersonNIN',
    'urn:oid:*******.4.1.2428.90.1.6'    => 'norEduOrgAcronym',
    'urn:oid:*******.4.1.2428.90.1.7'    => 'norEduOrgUniqueIdentifier',
    'urn:oid:*******.4.1.2428.90.1.8'    => 'norEduOrgUnitUniqueIdentifier',
    'urn:oid:*******.4.1.2428.90.1.9'    => 'federationFeideSchemaVersion',
    'urn:oid:*******.4.1.24552.500.********' => 'sshPublicKey',
    'urn:oid:*******.4.1.250.1.57'       => 'labeledURI',
    'urn:oid:*******.4.1.5923.1.1.1.1'   => 'eduPersonAffiliation',
    'urn:oid:*******.4.1.5923.1.1.1.11'  => 'eduPersonAssurance',
    'urn:oid:*******.4.1.5923.********'  => 'eduPersonTargetedID',
    'urn:oid:*******.4.1.5923.********'  => 'eduPersonUniqueId',
    'urn:oid:*******.4.1.5923.********'  => 'eduPersonOrcid',
    'urn:oid:*******.4.1.5923.*******'   => 'eduPersonNickname',
    'urn:oid:*******.4.1.5923.*******'   => 'eduPersonOrgDN',
    'urn:oid:*******.4.1.5923.*******'   => 'eduPersonOrgUnitDN',
    'urn:oid:*******.4.1.5923.*******'   => 'eduPersonPrimaryAffiliation',
    'urn:oid:*******.4.1.5923.*******'   => 'eduPersonPrincipalName',
    'urn:oid:*******.4.1.5923.*******'   => 'eduPersonEntitlement',
    'urn:oid:*******.4.1.5923.*******'   => 'eduPersonPrimaryOrgUnitDN',
    'urn:oid:*******.4.1.5923.*******'   => 'eduPersonScopedAffiliation',
    'urn:oid:*******.4.1.5923.*******'   => 'eduOrgHomePageURI',
    'urn:oid:*******.4.1.5923.*******'   => 'eduOrgIdentityAuthNPolicyURI',
    'urn:oid:*******.4.1.5923.*******'   => 'eduOrgLegalName',
    'urn:oid:*******.4.1.5923.*******'   => 'eduOrgSuperiorURI',
    'urn:oid:*******.4.1.5923.*******'   => 'eduOrgWhitePagesURI',
    'urn:oid:*******.4.1.5923.*******'   => 'isMemberOf',
    'urn:oid:*******.4.1.25178.*******'  => 'schacYearOfBirth',
    'urn:oid:*******.4.1.25178.1.2.1'    => 'schacMotherTongue',
    'urn:oid:*******.4.1.25178.1.2.2'    => 'schacGender',
    'urn:oid:*******.4.1.25178.1.2.3'    => 'schacDateOfBirth',
    'urn:oid:*******.4.1.25178.1.2.4'    => 'schacPlaceOfBirth',
    'urn:oid:*******.4.1.25178.1.2.5'    => 'schacCountryOfCitizenship',
    'urn:oid:*******.4.1.25178.1.2.6'    => 'schacSn1',
    'urn:oid:*******.4.1.25178.1.2.7'    => 'schacSn2',
    'urn:oid:*******.4.1.25178.1.2.8'    => 'schacPersonalTitle',
    'urn:oid:*******.4.1.25178.1.2.9'    => 'schacHomeOrganization',
    'urn:oid:*******.4.1.25178.1.2.10'   => 'schacHomeOrganizationType',
    'urn:oid:*******.4.1.25178.1.2.11'   => 'schacCountryOfResidence',
    'urn:oid:*******.4.1.25178.1.2.12'   => 'schacUserPresenceID',
    'urn:oid:*******.4.1.25178.1.2.13'   => 'schacPersonalPosition',
    'urn:oid:*******.4.1.25178.1.2.14'   => 'schacPersonalUniqueCode',
    'urn:oid:*******.4.1.25178.1.2.15'   => 'schacPersonalUniqueID',
    'urn:oid:*******.4.1.25178.1.2.17'   => 'schacExpiryDate',
    'urn:oid:*******.4.1.25178.1.2.18'   => 'schacUserPrivateAttribute',
    'urn:oid:*******.4.1.25178.1.2.19'   => 'schacUserStatus',
    'urn:oid:*******.4.1.25178.1.2.20'   => 'schacProjectMembership',
    'urn:oid:*******.4.1.25178.1.2.21'   => 'schacProjectSpecificRole',
    'urn:oid:2.16.840.1.113730.3.1.1'    => 'carLicense',
    'urn:oid:2.16.840.1.113730.3.1.2'    => 'departmentNumber',
    'urn:oid:2.16.840.1.113730.3.1.216'  => 'userPKCS12',
    'urn:oid:2.16.840.1.113730.3.1.241'  => 'displayName',
    'urn:oid:2.16.840.1.113730.3.1.3'    => 'employeeNumber',
    'urn:oid:2.16.840.1.113730.3.1.39'   => 'preferredLanguage',
    'urn:oid:2.16.840.1.113730.3.1.4'    => 'employeeType',
    'urn:oid:2.16.840.1.113730.3.1.40'   => 'userSMIMECertificate',
    'urn:oid:*******'                    => 'objectClass',
    'urn:oid:*******'                    => 'aliasedObjectName',
    'urn:oid:*******0'                   => 'o',
    'urn:oid:*******1'                   => 'ou',
    'urn:oid:*******2'                   => 'title',
    'urn:oid:*******3'                   => 'description',
    'urn:oid:*******4'                   => 'searchGuide',
    'urn:oid:*******5'                   => 'businessCategory',
    'urn:oid:*******6'                   => 'postalAddress',
    'urn:oid:*******7'                   => 'postalCode',
    'urn:oid:*******8'                   => 'postOfficeBox',
    'urn:oid:*******9'                   => 'physicalDeliveryOfficeName',
    'urn:oid:*******'                    => 'knowledgeInformation',
    'urn:oid:*******0'                   => 'telephoneNumber',
    'urn:oid:*******1'                   => 'telexNumber',
    'urn:oid:*******2'                   => 'teletexTerminalIdentifier',
    'urn:oid:*******3'                   => 'facsimileTelephoneNumber',
    'urn:oid:*******4'                   => 'x121Address',
    'urn:oid:*******5'                   => 'internationaliSDNNumber',
    'urn:oid:*******6'                   => 'registeredAddress',
    'urn:oid:*******7'                   => 'destinationIndicator',
    'urn:oid:*******8'                   => 'preferredDeliveryMethod',
    'urn:oid:*******9'                   => 'presentationAddress',
    'urn:oid:*******'                    => 'cn',
    'urn:oid:*******0'                   => 'supportedApplicationContext',
    'urn:oid:*******1'                   => 'member',
    'urn:oid:*******2'                   => 'owner',
    'urn:oid:*******3'                   => 'roleOccupant',
    'urn:oid:*******4'                   => 'seeAlso',
    'urn:oid:*******5'                   => 'userPassword',
    'urn:oid:*******6'                   => 'userCertificate',
    'urn:oid:*******7'                   => 'cACertificate',
    'urn:oid:*******8'                   => 'authorityRevocationList',
    'urn:oid:*******9'                   => 'certificateRevocationList',
    'urn:oid:*******'                    => 'sn',
    'urn:oid:*******0'                   => 'crossCertificatePair',
    'urn:oid:*******1'                   => 'name',
    'urn:oid:*******2'                   => 'givenName',
    'urn:oid:*******3'                   => 'initials',
    'urn:oid:********'                   => 'generationQualifier',
    'urn:oid:*******5'                   => 'x500UniqueIdentifier',
    'urn:oid:*******6'                   => 'dnQualifier',
    'urn:oid:*******7'                   => 'enhancedSearchGuide',
    'urn:oid:*******8'                   => 'protocolInformation',
    'urn:oid:*******9'                   => 'distinguishedName',
    'urn:oid:2.5.4.5'                    => 'serialNumber',
    'urn:oid:2.5.4.50'                   => 'uniqueMember',
    'urn:oid:2.5.4.51'                   => 'houseIdentifier',
    'urn:oid:2.5.4.52'                   => 'supportedAlgorithms',
    'urn:oid:2.5.4.53'                   => 'deltaRevocationList',
    'urn:oid:2.5.4.54'                   => 'dmdName',
    'urn:oid:2.5.4.6'                    => 'c',
    'urn:oid:2.5.4.65'                   => 'pseudonym',
    'urn:oid:2.5.4.7'                    => 'l',
    'urn:oid:2.5.4.8'                    => 'st',
    'urn:oid:2.5.4.9'                    => 'street',
];
