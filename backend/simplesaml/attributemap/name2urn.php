<?php
$attributemap = [
    'aRecord'                       => 'urn:mace:dir:attribute-def:aRecord',
    'aliasedEntryName'              => 'urn:mace:dir:attribute-def:aliasedEntryName',
    'aliasedObjectName'             => 'urn:mace:dir:attribute-def:aliasedObjectName',
    'associatedDomain'              => 'urn:mace:dir:attribute-def:associatedDomain',
    'associatedName'                => 'urn:mace:dir:attribute-def:associatedName',
    'audio'                         => 'urn:mace:dir:attribute-def:audio',
    'authorityRevocationList'       => 'urn:mace:dir:attribute-def:authorityRevocationList',
    'buildingName'                  => 'urn:mace:dir:attribute-def:buildingName',
    'businessCategory'              => 'urn:mace:dir:attribute-def:businessCategory',
    'c'                             => 'urn:mace:dir:attribute-def:c',
    'cACertificate'                 => 'urn:mace:dir:attribute-def:cACertificate',
    'cNAMERecord'                   => 'urn:mace:dir:attribute-def:cNAMERecord',
    'carLicense'                    => 'urn:mace:dir:attribute-def:carLicense',
    'certificateRevocationList'     => 'urn:mace:dir:attribute-def:certificateRevocationList',
    'cn'                            => 'urn:mace:dir:attribute-def:cn',
    'co'                            => 'urn:mace:dir:attribute-def:co',
    'commonName'                    => 'urn:mace:dir:attribute-def:commonName',
    'countryName'                   => 'urn:mace:dir:attribute-def:countryName',
    'crossCertificatePair'          => 'urn:mace:dir:attribute-def:crossCertificatePair',
    'dITRedirect'                   => 'urn:mace:dir:attribute-def:dITRedirect',
    'dSAQuality'                    => 'urn:mace:dir:attribute-def:dSAQuality',
    'dc'                            => 'urn:mace:dir:attribute-def:dc',
    'deltaRevocationList'           => 'urn:mace:dir:attribute-def:deltaRevocationList',
    'departmentNumber'              => 'urn:mace:dir:attribute-def:departmentNumber',
    'description'                   => 'urn:mace:dir:attribute-def:description',
    'destinationIndicator'          => 'urn:mace:dir:attribute-def:destinationIndicator',
    'displayName'                   => 'urn:mace:dir:attribute-def:displayName',
    'distinguishedName'             => 'urn:mace:dir:attribute-def:distinguishedName',
    'dmdName'                       => 'urn:mace:dir:attribute-def:dmdName',
    'dnQualifier'                   => 'urn:mace:dir:attribute-def:dnQualifier',
    'documentAuthor'                => 'urn:mace:dir:attribute-def:documentAuthor',
    'documentIdentifier'            => 'urn:mace:dir:attribute-def:documentIdentifier',
    'documentLocation'              => 'urn:mace:dir:attribute-def:documentLocation',
    'documentPublisher'             => 'urn:mace:dir:attribute-def:documentPublisher',
    'documentTitle'                 => 'urn:mace:dir:attribute-def:documentTitle',
    'documentVersion'               => 'urn:mace:dir:attribute-def:documentVersion',
    'domainComponent'               => 'urn:mace:dir:attribute-def:domainComponent',
    'drink'                         => 'urn:mace:dir:attribute-def:drink',
    'eduOrgHomePageURI'             => 'urn:mace:dir:attribute-def:eduOrgHomePageURI',
    'eduOrgIdentityAuthNPolicyURI'  => 'urn:mace:dir:attribute-def:eduOrgIdentityAuthNPolicyURI',
    'eduOrgLegalName'               => 'urn:mace:dir:attribute-def:eduOrgLegalName',
    'eduOrgSuperiorURI'             => 'urn:mace:dir:attribute-def:eduOrgSuperiorURI',
    'eduOrgWhitePagesURI'           => 'urn:mace:dir:attribute-def:eduOrgWhitePagesURI',
    'eduPersonAffiliation'          => 'urn:mace:dir:attribute-def:eduPersonAffiliation',
    'eduPersonAssurance'            => 'urn:mace:dir:attribute-def:eduPersonAssurance',
    'eduPersonEntitlement'          => 'urn:mace:dir:attribute-def:eduPersonEntitlement',
    'eduPersonNickname'             => 'urn:mace:dir:attribute-def:eduPersonNickname',
    'eduPersonOrgDN'                => 'urn:mace:dir:attribute-def:eduPersonOrgDN',
    'eduPersonOrgUnitDN'            => 'urn:mace:dir:attribute-def:eduPersonOrgUnitDN',
    'eduPersonPrimaryAffiliation'   => 'urn:mace:dir:attribute-def:eduPersonPrimaryAffiliation',
    'eduPersonPrimaryOrgUnitDN'     => 'urn:mace:dir:attribute-def:eduPersonPrimaryOrgUnitDN',
    'eduPersonPrincipalName'        => 'urn:mace:dir:attribute-def:eduPersonPrincipalName',
    'eduPersonScopedAffiliation'    => 'urn:mace:dir:attribute-def:eduPersonScopedAffiliation',
    'eduPersonTargetedID'           => 'urn:mace:dir:attribute-def:eduPersonTargetedID',
    'eduPersonUniqueId'             => 'urn:mace:dir:attribute-def:eduPersonUniqueId',
    'email'                         => 'urn:mace:dir:attribute-def:email',
    'emailAddress'                  => 'urn:mace:dir:attribute-def:emailAddress',
    'employeeNumber'                => 'urn:mace:dir:attribute-def:employeeNumber',
    'employeeType'                  => 'urn:mace:dir:attribute-def:employeeType',
    'enhancedSearchGuide'           => 'urn:mace:dir:attribute-def:enhancedSearchGuide',
    'facsimileTelephoneNumber'      => 'urn:mace:dir:attribute-def:facsimileTelephoneNumber',
    'favouriteDrink'                => 'urn:mace:dir:attribute-def:favouriteDrink',
    'fax'                           => 'urn:mace:dir:attribute-def:fax',
    'federationFeideSchemaVersion'  => 'urn:mace:dir:attribute-def:federationFeideSchemaVersion',
    'friendlyCountryName'           => 'urn:mace:dir:attribute-def:friendlyCountryName',
    'generationQualifier'           => 'urn:mace:dir:attribute-def:generationQualifier',
    'givenName'                     => 'urn:mace:dir:attribute-def:givenName',
    'gn'                            => 'urn:mace:dir:attribute-def:gn',
    'homePhone'                     => 'urn:mace:dir:attribute-def:homePhone',
    'homePostalAddress'             => 'urn:mace:dir:attribute-def:homePostalAddress',
    'homeTelephoneNumber'           => 'urn:mace:dir:attribute-def:homeTelephoneNumber',
    'host'                          => 'urn:mace:dir:attribute-def:host',
    'houseIdentifier'               => 'urn:mace:dir:attribute-def:houseIdentifier',
    'info'                          => 'urn:mace:dir:attribute-def:info',
    'initials'                      => 'urn:mace:dir:attribute-def:initials',
    'internationaliSDNNumber'       => 'urn:mace:dir:attribute-def:internationaliSDNNumber',
    'janetMailbox'                  => 'urn:mace:dir:attribute-def:janetMailbox',
    'jpegPhoto'                     => 'urn:mace:dir:attribute-def:jpegPhoto',
    'knowledgeInformation'          => 'urn:mace:dir:attribute-def:knowledgeInformation',
    'l'                             => 'urn:mace:dir:attribute-def:l',
    'labeledURI'                    => 'urn:mace:dir:attribute-def:labeledURI',
    'localityName'                  => 'urn:mace:dir:attribute-def:localityName',
    'mDRecord'                      => 'urn:mace:dir:attribute-def:mDRecord',
    'mXRecord'                      => 'urn:mace:dir:attribute-def:mXRecord',
    'mail'                          => 'urn:mace:dir:attribute-def:mail',
    'mailPreferenceOption'          => 'urn:mace:dir:attribute-def:mailPreferenceOption',
    'manager'                       => 'urn:mace:dir:attribute-def:manager',
    'member'                        => 'urn:mace:dir:attribute-def:member',
    'mobile'                        => 'urn:mace:dir:attribute-def:mobile',
    'mobileTelephoneNumber'         => 'urn:mace:dir:attribute-def:mobileTelephoneNumber',
    'nSRecord'                      => 'urn:mace:dir:attribute-def:nSRecord',
    'name'                          => 'urn:mace:dir:attribute-def:name',
    'norEduOrgAcronym'              => 'urn:mace:dir:attribute-def:norEduOrgAcronym',
    'norEduOrgNIN'                  => 'urn:mace:dir:attribute-def:norEduOrgNIN',
    'norEduOrgSchemaVersion'        => 'urn:mace:dir:attribute-def:norEduOrgSchemaVersion',
    'norEduOrgUniqueIdentifier'     => 'urn:mace:dir:attribute-def:norEduOrgUniqueIdentifier',
    'norEduOrgUniqueNumber'         => 'urn:mace:dir:attribute-def:norEduOrgUniqueNumber',
    'norEduOrgUnitUniqueIdentifier' => 'urn:mace:dir:attribute-def:norEduOrgUnitUniqueIdentifier',
    'norEduOrgUnitUniqueNumber'     => 'urn:mace:dir:attribute-def:norEduOrgUnitUniqueNumber',
    'norEduPersonBirthDate'         => 'urn:mace:dir:attribute-def:norEduPersonBirthDate',
    'norEduPersonLIN'               => 'urn:mace:dir:attribute-def:norEduPersonLIN',
    'norEduPersonNIN'               => 'urn:mace:dir:attribute-def:norEduPersonNIN',
    'o'                             => 'urn:mace:dir:attribute-def:o',
    'objectClass'                   => 'urn:mace:dir:attribute-def:objectClass',
    'organizationName'              => 'urn:mace:dir:attribute-def:organizationName',
    'organizationalStatus'          => 'urn:mace:dir:attribute-def:organizationalStatus',
    'organizationalUnitName'        => 'urn:mace:dir:attribute-def:organizationalUnitName',
    'otherMailbox'                  => 'urn:mace:dir:attribute-def:otherMailbox',
    'ou'                            => 'urn:mace:dir:attribute-def:ou',
    'owner'                         => 'urn:mace:dir:attribute-def:owner',
    'pager'                         => 'urn:mace:dir:attribute-def:pager',
    'pagerTelephoneNumber'          => 'urn:mace:dir:attribute-def:pagerTelephoneNumber',
    'pairwise-id'                   => 'urn:oasis:names:tc:SAML:attribute:pairwise-id',
    'personalSignature'             => 'urn:mace:dir:attribute-def:personalSignature',
    'personalTitle'                 => 'urn:mace:dir:attribute-def:personalTitle',
    'photo'                         => 'urn:mace:dir:attribute-def:photo',
    'physicalDeliveryOfficeName'    => 'urn:mace:dir:attribute-def:physicalDeliveryOfficeName',
    'pkcs9email'                    => 'urn:mace:dir:attribute-def:pkcs9email',
    'postOfficeBox'                 => 'urn:mace:dir:attribute-def:postOfficeBox',
    'postalAddress'                 => 'urn:mace:dir:attribute-def:postalAddress',
    'postalCode'                    => 'urn:mace:dir:attribute-def:postalCode',
    'preferredDeliveryMethod'       => 'urn:mace:dir:attribute-def:preferredDeliveryMethod',
    'preferredLanguage'             => 'urn:mace:dir:attribute-def:preferredLanguage',
    'presentationAddress'           => 'urn:mace:dir:attribute-def:presentationAddress',
    'protocolInformation'           => 'urn:mace:dir:attribute-def:protocolInformation',
    'pseudonym'                     => 'urn:mace:dir:attribute-def:pseudonym',
    'registeredAddress'             => 'urn:mace:dir:attribute-def:registeredAddress',
    'rfc822Mailbox'                 => 'urn:mace:dir:attribute-def:rfc822Mailbox',
    'roleOccupant'                  => 'urn:mace:dir:attribute-def:roleOccupant',
    'roomNumber'                    => 'urn:mace:dir:attribute-def:roomNumber',
    'sOARecord'                     => 'urn:mace:dir:attribute-def:sOARecord',
    'schacCountryOfCitizenship'     => 'urn:mace:terena.org:attribute-def:schacCountryOfCitizenship',
    'schacCountryOfResidence'       => 'urn:mace:terena.org:attribute-def:schacCountryOfResidence',
    'schacDateOfBirth'              => 'urn:mace:terena.org:attribute-def:schacDateOfBirth',
    'schacExpiryDate'               => 'urn:mace:terena.org:attribute-def:schacExpiryDate',
    'schacGender'                   => 'urn:mace:terena.org:attribute-def:schacGender',
    'schacHomeOrganization'         => 'urn:mace:terena.org:attribute-def:schacHomeOrganization',
    'schacHomeOrganizationType'     => 'urn:mace:terena.org:attribute-def:schacHomeOrganizationType',
    'schacMotherTongue'             => 'urn:mace:terena.org:attribute-def:schacMotherTongue',
    'schacPersonalPosition'         => 'urn:mace:terena.org:attribute-def:schacPersonalPosition',
    'schacPersonalTitle'            => 'urn:mace:terena.org:attribute-def:schacPersonalTitle',
    'schacPersonalUniqueCode'       => 'urn:mace:terena.org:attribute-def:schacPersonalUniqueCode',
    'schacPersonalUniqueID'         => 'urn:mace:terena.org:attribute-def:schacPersonalUniqueID',
    'schacPlaceOfBirth'             => 'urn:mace:terena.org:attribute-def:schacPlaceOfBirth',
    'schacProjectMembership'        => 'urn:mace:terena.org:attribute-def:schacProjectMembership',
    'schacProjectSpecificRole'      => 'urn:mace:terena.org:attribute-def:schacProjectSpecificRole',
    'schacSn1'                      => 'urn:mace:terena.org:attribute-def:schacSn1',
    'schacSn2'                      => 'urn:mace:terena.org:attribute-def:schacSn2',
    'schacUserPresenceID'           => 'urn:mace:terena.org:attribute-def:schacUserPresenceID',
    'schacUserPrivateAttribute'     => 'urn:mace:terena.org:attribute-def:schacUserPrivateAttribute',
    'schacUserStatus'               => 'urn:mace:terena.org:attribute-def:schacUserStatus',
    'schacYearOfBirth'              => 'urn:mace:terena.org:attribute-def:schacYearOfBirth',
    'searchGuide'                   => 'urn:mace:dir:attribute-def:searchGuide',
    'secretary'                     => 'urn:mace:dir:attribute-def:secretary',
    'seeAlso'                       => 'urn:mace:dir:attribute-def:seeAlso',
    'serialNumber'                  => 'urn:mace:dir:attribute-def:serialNumber',
    'singleLevelQuality'            => 'urn:mace:dir:attribute-def:singleLevelQuality',
    'sisSchoolGrade'                => 'urn:mace:dir:attribute-def:sisSchoolGrade',
    'sisLegalGuardianFor'           => 'urn:mace:dir:attribute-def:sisLegalGuardianFor',
    'sn'                            => 'urn:mace:dir:attribute-def:sn',
    'st'                            => 'urn:mace:dir:attribute-def:st',
    'stateOrProvinceName'           => 'urn:mace:dir:attribute-def:stateOrProvinceName',
    'street'                        => 'urn:mace:dir:attribute-def:street',
    'streetAddress'                 => 'urn:mace:dir:attribute-def:streetAddress',
    'subject-id'                    => 'urn:oasis:names:tc:SAML:attribute:subject-id',
    'subtreeMaximumQuality'         => 'urn:mace:dir:attribute-def:subtreeMaximumQuality',
    'subtreeMinimumQuality'         => 'urn:mace:dir:attribute-def:subtreeMinimumQuality',
    'supportedAlgorithms'           => 'urn:mace:dir:attribute-def:supportedAlgorithms',
    'supportedApplicationContext'   => 'urn:mace:dir:attribute-def:supportedApplicationContext',
    'surname'                       => 'urn:mace:dir:attribute-def:surname',
    'telephoneNumber'               => 'urn:mace:dir:attribute-def:telephoneNumber',
    'teletexTerminalIdentifier'     => 'urn:mace:dir:attribute-def:teletexTerminalIdentifier',
    'telexNumber'                   => 'urn:mace:dir:attribute-def:telexNumber',
    'textEncodedORAddress'          => 'urn:mace:dir:attribute-def:textEncodedORAddress',
    'title'                         => 'urn:mace:dir:attribute-def:title',
    'uid'                           => 'urn:mace:dir:attribute-def:uid',
    'uniqueIdentifier'              => 'urn:mace:dir:attribute-def:uniqueIdentifier',
    'uniqueMember'                  => 'urn:mace:dir:attribute-def:uniqueMember',
    'userCertificate'               => 'urn:mace:dir:attribute-def:userCertificate',
    'userClass'                     => 'urn:mace:dir:attribute-def:userClass',
    'userPKCS12'                    => 'urn:mace:dir:attribute-def:userPKCS12',
    'userPassword'                  => 'urn:mace:dir:attribute-def:userPassword',
    'userSMIMECertificate'          => 'urn:mace:dir:attribute-def:userSMIMECertificate',
    'userid'                        => 'urn:mace:dir:attribute-def:userid',
    'x121Address'                   => 'urn:mace:dir:attribute-def:x121Address',
    'x500UniqueIdentifier'          => 'urn:mace:dir:attribute-def:x500UniqueIdentifier',
];
