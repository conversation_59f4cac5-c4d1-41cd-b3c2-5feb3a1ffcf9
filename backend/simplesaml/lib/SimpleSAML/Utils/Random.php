<?php

namespace SimpleSAML\Utils;

/**
 * Utility class for random data generation and manipulation.
 *
 * @package SimpleSAMLphp
 */
class Random
{
    /**
     * The fixed length of random identifiers.
     */
    const ID_LENGTH = 43;

    /**
     * Generate a random identifier, ID_LENGTH bytes long.
     *
     * @return string A ID_LENGTH-bytes long string with a random, hex-encoded string.
     *
     * <AUTHOR> UNINETT AS <<EMAIL>>
     * <AUTHOR> UNINETT AS <<EMAIL>>
     * <AUTHOR> UNINETT AS <<EMAIL>>
     */
    public static function generateID()
    {
        return '_' . bin2hex(openssl_random_pseudo_bytes((int) ((self::ID_LENGTH - 1) / 2)));
    }
}
