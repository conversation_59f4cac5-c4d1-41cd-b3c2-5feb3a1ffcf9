#!/bin/bash
tnt_id=
crontabs_dir=

# On se place dans le dossier qui contient toutes les tâches planifiées
cd $crontabs_dir

# Envoi des rapports sur la recherche des revendeurs
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script send-stats-search-stores --mode 1

# Ménage sur les paniers de plus de 12 mois
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script clean-carts-orders --mode 0

# Ménage sur les caches de recherche non utilisé depuis plus de 120 jours
nice /usr/bin/php execute-script.php --tnt_id $tnt_id --script clean-search-caches --mode 0 --other="days=120"

# Mise à jours des zones de france (région, département, code postaux et commune)
nice /usr/bin/php update-zones-france.php

# Envoi du rapport de stats Yuto (spé GROUPAUTO)
nice /usr/bin/php groupauto-export-stats.php

# Mise à jour des familles Amazon.fr
nice /usr/bin/php update-taxonomy-amazon.php

# Rapport VA Equipe Boplan
#nice /usr/bin/php execute-script.php --tnt_id 352 --script boplan/run-export --mode 0