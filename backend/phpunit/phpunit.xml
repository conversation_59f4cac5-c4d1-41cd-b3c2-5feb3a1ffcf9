<?xml version="1.0" encoding="UTF-8"?>
<phpunit colors="true" bootstrap="./config.php" verbose="true">
	<php />
	<testsuites>
		<testsuite name="datesTest">
			<file>unit/dates/findNearestDateInArrayTest.php</file>
		</testsuite>
		<testsuite name="adressesTest">
			<file>unit/adresses/adressesGetTest.php</file>
			<file>unit/adresses/adressesAddTest.php</file>
			<file>unit/adresses/adressesUpdateTest.php</file>
			<file>unit/adresses/adressesDelTest.php</file>
		</testsuite>
		<testsuite name="usersTest">
			<file>unit/users/usersGetTest.php</file>
			<file>unit/users/usersAddTest.php</file>
			<file>unit/users/usersUpdateTest.php</file>
			<file>unit/users/usersDelTest.php</file>
			<file>unit/users/usersValidEmailTest.php</file>
		</testsuite>
		<testsuite name="brandsTest">
			<file>unit/brands/brandsGetTest.php</file>
			<file>unit/brands/brandsAddTest.php</file>
			<file>unit/brands/brandsUpdateTest.php</file>
			<file>unit/brands/brandsDelTest.php</file>
		</testsuite>
		<testsuite name="productTest">
			<file>unit/products/productsGetTest.php</file>
			<file>unit/products/productsAddTest.php</file>
			<file>unit/products/productsUpdateTest.php</file>
			<file>unit/products/productsDelTest.php</file>
			<testsuite name="colisageTest">
				<file>unit/products/colisage/colisageGetTest.php</file>
				<file>unit/products/colisage/colisageAddTest.php</file>
				<file>unit/products/colisage/colisageUpdateTest.php</file>
				<file>unit/products/colisage/colisageDelTest.php</file>
			</testsuite>
		</testsuite>
		<testsuite name="advertisingTest">
			<file>unit/advertising/bannersGetTest.php</file>
			<file>unit/advertising/bannersAddTest.php</file>
			<file>unit/advertising/bannersDelTest.php</file>
		</testsuite>
		<testsuite name="newsletterTest">
			<file>unit/newsletter/subscribersExistsTest.php</file>
			<file>unit/newsletter/subscribersAddTest.php</file>
			<file>unit/newsletter/subscribersDelTest.php</file>
			<file>unit/newsletter/newsletterInscriptTest.php</file>
			<file>unit/newsletter/newsletterUninscriptTest.php</file>
		</testsuite>
		<testsuite name="ordersTest">
			<file>unit/orders/ordersGetProductsTest.php</file>
			<file>unit/orders/ordersAddProductsTest.php</file>
			<file>unit/orders/ordersUpdateTotalTest.php</file>
			<file>unit/orders/ordersDelProductTest.php</file>
			<file>unit/orders/ordersGetTest.php</file>
			<file>unit/orders/ordersSetParentIdTest.php</file>
			<file>unit/orders/ordersSetProductIdTest.php</file>
			<file>unit/orders/ordersSetRefTest.php</file>
		</testsuite>
		<testsuite name="categoriesTest">
			<file>unit/categories/categoriesGetTest.php</file>
			<file>unit/categories/categoriesExistsTest.php</file>
			<file>unit/categories/categoriesAddTest.php</file>
			<file>unit/categories/categoriesUpdateTest.php</file>
			<file>unit/categories/categoriesDelTest.php</file>
		</testsuite>
		<testsuite name="reviewsTest">
			<file>unit/reviews/reviewsGetTest.php</file>
			<file>unit/reviews/reviewsAddTest.php</file>
		</testsuite>
		<testsuite name="deliveryTest">
			<testsuite name="servicesTest">
				<file>unit/delivery/services/servicesGetTest.php</file>
				<file>unit/delivery/services/servicesAddTest.php</file>
				<file>unit/delivery/services/servicesUpdateTest.php</file>
				<file>unit/delivery/services/servicesDelTest.php</file>
			</testsuite>
		</testsuite>
		<testsuite name="RGPDTest">
			<file>unit/RGPD/TarteaucitronTest.php</file>
		</testsuite>
		<testsuite name="fieldsTest">
			<file>unit/fields/fieldsCheckObjectIdentifiantTest.php</file>
		</testsuite>
		<testsuite name="pricewatchingTest">
			<testsuite name="followedListsTest">
				<file>unit/pricewatching/followedLists/followedListsCRUDTest.php</file>
				<file>unit/pricewatching/followedLists/followedListsFeaturesTest.php</file>
			</testsuite>
			<testsuite name="followedUsersTest">
				<file>unit/pricewatching/followedUsers/followedUsersCRUDTest.php</file>
			</testsuite>
			<testsuite name="followedProductsTest">
				<file>unit/pricewatching/followedProducts/followedProductsCRUDTest.php</file>
			</testsuite>
			<testsuite name="linearRaisedTest">
				<file>unit/pricewatching/linearRaised/linearRaisedCRUDTest.php</file>
			</testsuite>
			<testsuite name="offersTest">
				<file>unit/pricewatching/offers/offersCRUDTest.php</file>
			</testsuite>
			<testsuite name="statesTest">
				<file>unit/pricewatching/states/statesCRUDTest.php</file>
			</testsuite>
			<testsuite name="offersStatesTest">
				<file>unit/pricewatching/offersStates/offersStatesCRUDTest.php</file>
			</testsuite>
			<testsuite name="listSectionTest">
				<file>unit/pricewatching/listSection/listSectionCRUDTest.php</file>
			</testsuite>
		</testsuite>
	</testsuites>
	<logging>
		<log type="coverage-html" target="build/coverage" title="RiaShop" charset="UTF-8" yui="true" highlight="true" lowUpperBound="35" highLowerBound="70" />
		<log type="coverage-clover" target="build/logs/clover.xml" />
		<log type="junit" target="build/logs/junit.xml" logIncompleteSkipped="false" />
	</logging>
</phpunit>