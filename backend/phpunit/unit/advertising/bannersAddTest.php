<?php
	require_once('advertising.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class bannersAddTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester l'ajout d'une nouvelle bannière
		 * @dataProvider validBanners
		 */
		public function testValidBannersAdd($plc, $name, $alt, $url, $from, $to, $prd) {

			$bnr_id = adv_banners_add($plc, $name, $alt, $url, $from, $to, $prd);
			
			// Vérifie que l'id est un integer
			$this->assertThat($bnr_id, $this->logicalAnd(
				$this->isType('int'), 
				$this->greaterThan(0)
			), "Erreur: adv_banners_add renvoie un id invalide");
			
			$rbnr = adv_banners_get(1, $bnr_id, false);
			$this->assertTrue($rbnr || ria_mysql_num_rows($rbnr), 'Erreur lors de la vérification des champs de la bannière ajoutée');
			$bnr = ria_mysql_fetch_assoc($rbnr);

			$this->assertEquals($plc, $bnr['plc_id'], 'Erreur: identifiant de l\'emplacement de la bannière non conforme à la valeur lors de l\'ajout');

			$this->assertEquals(ucFirst($name), $bnr['name'], 'Erreur: désignation de la bannière non conforme à la valeur lors de l\'ajout');

			$this->assertEquals(ucFirst($alt), $bnr['alt'], 'Erreur: text alternatif de la bannière non conforme à la valeur lors de l\'ajout');

			if($url != ''){
				$this->assertEquals($url, $bnr['url'], 'Erreur: url de la bannière non conforme à la valeur lors de l\'ajout');
			}else if($prd == 2){
				$this->assertEquals('/catalogue/parent/fille-1/produit-2', $bnr['url'], 'Erreur: url de la bannière non conforme à la valeur lors de l\'ajout');
			}

			if($from != ''){
				$this->assertEquals($from, $bnr['date_from'].' '.$bnr['hour_from'], 'Erreur: date de début d\'affichage de la bannière non conforme à la valeur lors de l\'ajout');
			}else{
				$this->assertEquals('00/00/0000 00:00', $bnr['date_from'].' '.$bnr['hour_from'], 'Erreur: date de début d\'affichage de la bannière non conforme à la valeur lors de l\'ajout');
			}

			if($to != ''){
				$this->assertEquals($to, $bnr['date_to'].' '.$bnr['hour_to'], 'Erreur: date de fin d\affichage de la bannière non conforme à la valeur lors de l\'ajout');
			}else{
				$this->assertEquals(' ', $bnr['date_to'].' '.$bnr['hour_to'], 'Erreur: date de fin d\affichage de la bannière non conforme à la valeur lors de l\'ajout');
			}

			$this->assertEquals($prd, $bnr['prd_id'], 'Erreur: produit de destination de la bannière non conforme à la valeur lors de l\'ajout');
		}

		/** Fonction permettant de tester l'insertion d'une bannière invalide
		 * @dataProvider invalidBanners
		 */
		public function testInvalidBannnersAdd($plc, $name, $alt, $url, $from, $to, $prd, $error){

			$this->assertFalse( adv_banners_add($plc, $name, $alt, $url, $from, $to, $prd), $error);
		}

		public function validBanners(){
			return array(
				//	 plc  	  name   		  alt      		 url               from      		     to       prd 
				array(1,	 'nom',  		  '', 		     '', 		         '', 		  	     '', 		  0),
				array(1,	 'nom', 'alternatif', 'www.url.com', '04/05/2018 11:36', '05/05/2018 11:36', 		  0),
				array(1,	 'nom', 		  '', 		     '', '01/04/2017 15:45', '01/05/2017 20:00', 		  0),
				array(1,	 'nom', 'alternatif', 		     '', '09/11/2015 08:45', 		   	     '', 		  2),
			);
		}

		public function invalidBanners(){
			return array(
				//	 plc  	  name   		  alt      		 url               from      		     to        prd               message d'erreur
				array(2,	 'nom',  		  '', 		     '', 		         '', 		  	     '', 		  0, 'Erreur: ajout d\'une bannière avec un emplacement invalide'),
				array(1,	 '',  		      '', 		     '', 		         '', 		  	     '', 		  0, 'Erreur: ajout d\'une bannière sans nom'),
				array(1,	 'nom',  		  '', 		     '',    'date invalide', 		  	     '', 		  0, 'Erreur: ajout d\'une bannière avec une date de début d\'affichage incorrecte'),
				array(1,	 'nom',  		  '', 		     '', 		         '', 		 '11/11/11', 		  0, 'Erreur: ajout d\'une baniière avec une date de fin d\'affichage incorrecte'),
			);
		}
	}
