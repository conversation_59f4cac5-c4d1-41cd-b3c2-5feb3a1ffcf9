<?php
	require_once('delivery.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class servicesUpdateTest extends PHPUnit_Framework_TestCase {

        /** Fonction permettant de tester la mise à jour d'un service avec des paramètre valide 
         * @dataProvider validServices
         */
        public function testServicesValidUpdate($id, $name, $desc, $url_site, $url_colis, $msg, $active, $zones, $price_ttc, $dealer_price_ht, $dealer_free_ht, $weight_min, $weight_max, $ord_amount_min, $ord_amount_max, $accept_consign){
           
            $this->assertTrue( dlv_services_update($id, $name, $desc, $url_site, $url_colis, $msg, $active, $zones, $price_ttc, $dealer_price_ht, $dealer_free_ht, $weight_min, $weight_max, $ord_amount_min, $ord_amount_max, $accept_consign), 'Erreur lors de la mise à jour d\'un service');
            
            // Vérifie que les champs ont bien été mis à jour
            $rsrv = dlv_services_get($id);
            $this->assertTrue( $rsrv && ria_mysql_num_rows($rsrv) == 1, 'Erreur lors de la vérification des champs du service mis à jour');
            $srv = ria_mysql_fetch_assoc($rsrv);

            $this->assertEquals( $name, $srv['name'], 'Erreur: nom du service non mis à jour' );

            $this->assertEquals( $desc, $srv['desc'], 'Erreur: description du service non mis à jour' );

            $this->assertEquals( $url_site, $srv['url-site'], 'Erreur: Url du site Internet du transporteur du service non mise à jour' );

            $this->assertEquals( $url_colis, $srv['url-colis'], 'Erreur: Url de suivi des colis du service non mise à jour' );

            $this->assertEquals( $msg, $srv['alert-msg'], 'Erreur: message de notification du service non mis à jour' );

            $this->assertTrue( $active == $srv['is_active'], 'Erreur: propriété is_active du service non mise à jour' );
   
            $this->assertEquals( $price_ttc, $srv['price-ttc'], 'Erreur: Tarif forfaitaire TTC pour l\'utilisation du service non mis à jour' );
            
            $this->assertEquals( $dealer_price_ht, $srv['dealer-price-ht'], 'Erreur: Tarif forfaitaire pour les revendeurs du service non mis à jour' );
            
            $this->assertEquals( $dealer_free_ht, $srv['dealer-free-ht'], 'Erreur: Franco de port revendeur du service non mis à jour' );
            
            $this->assertEquals( $weight_min, $srv['weight_min'], 'Erreur: poids minimum du service non mis à jour' );
            
            $this->assertEquals( $weight_max, $srv['weight_max'], 'Erreur: poids maximum du service non mis à jour' );
            
            $this->assertEquals( $ord_amount_min, $srv['ord_amount_min'], 'Erreur: montant minimum de commande du service non mis à jour' );
            
            $this->assertEquals( $ord_amount_max, $srv['ord_amount_max'], 'Erreur: montant maximum de commande du service non mis à jour' );
            
            $this->assertTrue( $accept_consign == $srv['accept_consign'], 'Erreur: propriété accept_consign du service non mise à jour' );
        }

        /** Fonction permttant de tester la mise à jour d'un service avec des paramètre invalide
         * @dataProvider invalidServices
         */
        public function testServicesInvalideUpdate($id, $name, $desc, $url_site, $url_colis, $msg, $active, $zones, $price_ttc, $dealer_price_ht, $dealer_free_ht, $weight_min, $weight_max, $ord_amount_min, $ord_amount_max, $accept_consign, $error){

            $this->assertFalse( dlv_services_update($id, $name, $desc, $url_site, $url_colis, $msg, $active, $zones, $price_ttc, $dealer_price_ht, $dealer_free_ht, $weight_min, $weight_max, $ord_amount_min, $ord_amount_max, $accept_consign), $error);
        }

        public static function validServices(){
            return array(
                //     id        name             desc          url_site          url_colis           msg        active      zones     price_ttc dealer_price_ht dealer_free_ht weight_min weight_max ord_amount_min ord_amount_max accept_consign
                array(100, 'Nouveau nom', 'Nouvelle desc', 'www.nouvelleurl.fr', 'www.colis.fr', 'nouveau message', 1,      array(),        null,        null,        null,          null,     null,            null,        null,         true),
                array(100, 'Nouveau nom', 'Nouvelle desc', 'www.nouvelleurl.fr', 'www.colis.fr', 'nouveau message', 0,      array( array('id' =>2)),           12,          15,         150,            10,      100,               0,          50,        false),
                array(100, 'Nouveau nom', 'Nouvelle desc', 'www.nouvelleurl.fr', 'www.colis.fr', 'nouveau message', 0,      array( array('id' =>2)),           12,          15,         150,            10,      100,               0,          50,        false,   1),
                array(100, 'Nouveau nom', 'Nouvelle desc', 'www.nouvelleurl.fr', 'www.colis.fr', 'nouveau message', 0,      array( array('id' =>2)),           12,          15,         150,            10,      100,               0,          50,        false,   false),
                array(100, 'Nouveau nom', 'Nouvelle desc', 'www.nouvelleurl.fr', 'www.colis.fr', 'nouveau message', 0,      array( array('id' =>2)),           12,          15,         150,            10,      100,               0,          50,        false,   null),
            );
        }

        public static function invalidServices(){
            return array(
                //     id        name             desc          url_site          url_colis           msg        active      zones     price_ttc dealer_price_ht dealer_free_ht weight_min weight_max ord_amount_min ord_amount_max accept_consign     message d'erreur
                array(1000, 'Nouveau nom', 'Nouvelle desc', 'www.nouvelleurl.fr', 'www.colis.fr', 'nouveau message', 1,      array(),        null,        null,        null,          null,     null,            null,        null,         true,        false,      'Erreur: mise à jour d\'un service inéxistant'),
                array( 100,            '', 'Nouvelle desc', 'www.nouvelleurl.fr', 'www.colis.fr', 'nouveau message', 1,      array(),        null,        null,        null,          null,     null,            null,        null,         true,        false,      'Erreur: mise à jour d\'un service sans nom'),
                array( 100,            '', 'Nouvelle desc', 'www.nouvelleurl.fr', 'www.colis.fr', 'nouveau message', 1,      array(),        null,        null,        null,          null,     null,            null,        null,         true,        -12,        'Erreur: mise à jour d\'un service avec un type non existant'),
                
            );
        }
    }
