<?php
	require_once('delivery.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class servicesGetTest extends PHPUnit_Framework_TestCase {

        /** Fonction permettant de tester la récupération des données d'un service
         */
        public function testServicesGetById(){

           $rsrv = dlv_services_get(1);
           $this->assertTrue( $rsrv && ria_mysql_num_rows($rsrv) == 1, 'Erreur lors de la récupération d\'un service par son id');
           $srv = ria_mysql_fetch_assoc($rsrv);

           $this->assertEquals( 1, $srv['id'], 'Erreur lors de la récupération d\'un service par son id');
        }

        /** Fonction permettant de tester la récupération des service disponible dans une certaine zone
         */
        public function testServicesGetByZone(){

            $rsrv = dlv_services_get(0, false, 1);
            $this->assertTrue( $rsrv && ria_mysql_num_rows($rsrv) == 1, 'Erreur lors de la récupération d\'un service par zone');
            $srv = ria_mysql_fetch_assoc($rsrv);
 
            $this->assertEquals( 2, $srv['id'], 'Erreur lors de la récupération d\'un service par zone');
        }

        /** Fonction permettant de tester la récupération du prix ttc d'un service
         */
        public function testServicesPriceTTCGet(){

            $this->assertEquals( 12, dlv_services_price_ttc_get(1), 'Erreur: prix ttc du service non conforme à la valeur dans la base de donnée');
        }

        /** Fonction permettant de tester la récupération du nom d'un service
         */
        public function testServiceNameGet(){

            $this->assertEquals( 'service de livraison', dlv_services_get_name(1), 'Erreur: nom du service non conforme à la valeur dans la base de donnée');
        }

        /** Fonction permettant de tester la récupération de l'information accept_consign d'un service
         */
        public function testServiceAcceptConsign(){

            $this->assertEquals( 1, dlv_services_accept_consign(1), 'Erreur: information accept_consign du service non conforme à la valeur dans la base de donnée');

            $this->assertEquals( 0, dlv_services_accept_consign(2), 'Erreur: information accept_consign du service non conforme à la valeur dans la base de donnée');
        }

        /** Fonction permettant de tester l'éxistance d'un service
         */
        public function testServicesExists(){

            $this->assertTrue(dlv_services_exists(1), 'Erreur: dlv_services_exists retourne faux avec un service éxistante');

            $this->assertFalse(dlv_services_exists(1000), 'Erreur: dlv_services_exists retourne vrai avec un service inéxistant');
        }
    }

