<?php
	require_once('fields.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class fieldsCheckObjectIdentifiantTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la validation d'identifiant d'objet valide
		 * @dataProvider validObjectIdentifiants
		 */
		public function testCheckValidObjectIdentifiant($obj) {
			$isValid = fld_check_object_id(obj);
			$this->assertTrue($isValid);
		}

		/** Fonction permettant de tester la validation d'identifiant d'objet invalide
		 * @dataProvider invalidObjectIdentifiants
		 */
		public function testCheckInValidObjectIdentifiant($ref, $name, $desc, $brand, $publish, $weight, $length, $width, $height, $keywords, $is_sync, $taxcode, $error) {
			$isValid = fld_check_object_id(obj);
			$this->assertFalse($isValid);
		}

		public static function validObjectIdentifiants() {
			return array(
				1, 2, 42,
				array(42, 0, 0),
				array(42, 42, 0),
				array(42, 42, 42)
			);
		}

		public static function invalidObjectIdentifiants(){
			return array(
				0, -1,
				array(0, 0, 0),
				array(-1, 0, 0),
				array(42, -1, 0),
				array(42, 0, -1)
			);
		}
	}
