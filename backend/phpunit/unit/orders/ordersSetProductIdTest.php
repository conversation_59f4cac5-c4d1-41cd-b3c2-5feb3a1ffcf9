<?php

require_once('orders.inc.php');

/**
 * @backupGlobals disabled
 */
class ordersSetProductIdTest extends PHPUnit_Framework_TestCase
{
	/**
	 * Test les contrôles de paramètres pour la fonction "ord_products_set_product_id".
	 *
	 * @dataProvider invalidParameters
	 *
	 * @param  int $orderId
	 * @param  int $currentProductId
	 * @param  int $lineId
	 * @param  int $newProductId
	 * @return void
	 */
	public function testInvalidParametersWillReturnFalse($orderId, $currentProductId, $lineId, $newProductId)
	{
		$this->assertFalse(
			ord_products_set_product_id($orderId, $currentProductId, $lineId, $newProductId)
		);
	}

	/**
	 * Test la fonction "ord_products_set_product_id".
	 *
	 * @dataProvider validParameters
	 *
	 * @param  int $orderId
	 * @param  int $currentProductId
	 * @param  int $lineId
	 * @param  int $newProductId
	 * @return void
	 */
	public function testOrderSetRef($orderId, $currentProductId, $lineId, $newProductId)
	{
		$this->assertTrue(
			ord_products_set_product_id($orderId, $currentProductId, $lineId, $newProductId)
		);
	}

	/**
	 * Fournit des paramètres valides pour tester la fonction "ord_products_set_product_id".
	 *
	 * @return array
	 */
	public function validParameters()
	{
		return array(
			array(1, 1, 0, 2),
		);
	}

	/**
	 * Fournit des paramètres invalides pour tester la fonction "ord_products_set_product_id".
	 * 		- 1ère ligne : Mauvais identifiant de commande
	 * 		- 2ème ligne : Mauvais identifiant produit (actuel)
	 * 		- 3ème ligne : Mauvais identifiant ligne
	 * 		- 4ème ligne : Mauvaise identifiant produit (nouveau)
	 *
	 * @return array
	 */
	public function invalidParameters()
	{
		return array(
			array(0, 1, 0, 2),
			array(1, 0, 0, 2),
			array(1, 1, 'WRONG_LINE_ID', 2),
			array(1, 1, 0, 'WRONG_PRODUCT_ID'),
		);
	}
}