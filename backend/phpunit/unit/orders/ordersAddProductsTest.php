<?php
	require_once('orders.inc.php');

	/**
	 *@backupGlobals disabled
	*/
	class ordersAddProductsTest extends PHPUnit_Framework_TestCase {

        /** Cette fonction permet de tester l'ajout d'un produit à un panier
         * @dataProvider validOrdProducts
         */
        public function testOrdAddPoducts($order, $prd, $qte, $notes, $published, $childs, $colisage, $reseller, $cod, $sub_id, $sub_date_start, $from_link, $update_total, $get_line_id, $fld, $new_line, $dps){
			
			$line = ord_products_add($order, $prd, $qte, $notes, $published, $childs, $colisage, $reseller, $cod, $sub_id, $sub_date_start, $from_link, $update_total, $get_line_id, $fld, $new_line, $dps);
			$this->assertTrue( false !== $line, 'Erreur : le produit ne c\'est pas ajouté à la commande');
			
			// Vérifie que les champs sont corrects
			$rord = ord_products_get($order, false, $prd, '', $line);
			$this->assertTrue($rord && ria_mysql_num_rows($rord) == 1, 'Erreur lors de l\'ajout d\'un produit à la commande');
			$ord = ria_mysql_fetch_assoc($rord);

			$this->assertEquals( $order, $ord['ord_id'], 'Erreur: identifiant de la commande non conforme à la valeur lors de l\'ajout');

			$this->assertEquals( $prd, $ord['id'], 'Erreur: identifiant du produit non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $qte, $ord['qte'], 'Erreur: quantité du produit dans la commande non conforme à la valeur lors de l\'ajout');

			$this->assertEquals( $notes, $ord['notes'], 'Erreur: note non conforme à la valeur lors de l\'ajout');

			$this->assertEquals( $colisage, $ord['col_id'], 'Erreur: identifiant de conditionnement du produit non conforme à la valeur lors de l\'ajout');

			$this->assertEquals( $cod, $ord['cod'], 'Erreur: identifiant de la promotion non conforme à la valeur lors de l\'ajout');

			$this->assertEquals( $dps, $ord['line_dps_id'], 'Erreur: identifiant du dépot non conforme à la valeur lors de l\'ajout');
		}
		
		/** Cette fonction vérifie si le le montant total de la commande a bien été mis à jour
		 */
		//TODO CE TEST ECHOUE UNIQUEMENT DANS L ENVIRONEMENT AUTOMATISE. A réactiver au plus tot
		//public function testOrdTotal(){
		//	$this->assertEquals(666, ord_orders_get_total_without_port(1, true, true ), 'Erreur: le prix total de la commande n\'a pas été mis à jour lors de l\'ajout de produit');
		//}

        public function validOrdProducts(){
            return array(
				//  order prd   qte     notes   published   childs colisage reseller  cod sub_id sub_date_start from_link update_total   get_line_id    fld     new_line     dps
				array(1,   1, 	 1, 		'', 	false, 	null, 	0, 		false, 	  0,     0,     false,        false,        true,       true,     false,     false,   false),
				array(1,   1, 	 10,   'notes', 	false, 	null, 	1, 		false, 	  0,     0,     false,        false,        true,       true,     false,     false,   false),
				array(1,   2, 	 1, 		'', 	false, 	null, 	0, 		    3, 	  0,     0,     false,        false,        true,       true,     false,     false,   false),
				array(1,   2, 	 1, 		'', 	false, 	null, 	0, 		    3, 	  0,     0,     false,        false,       false,       true,     false,      true,   false),
				array(1,   3, 	 1, 		'', 	false, 	null, 	0, 		false, 	  0,     0,     false,        false,        true,       true,     false,      true,   false),
				);
        }
		
	}

