<?php
	require_once('orders.inc.php');

	/**
	 *@backupGlobals disabled
	*/
	class ordersGetTest extends PHPUnit_Framework_TestCase {
        public static $order_id = 3;
        public static $order;
        /** Cette fonction permet de tester la récupéraction des données d'une commande
         */
        public function testOrdersGet(){

            $rord = ord_orders_get(0, self::$order_id);
            $this->assertTrue($rord || ria_mysql_num_rows($rord), 'Erreur: Aucun résultat retourné par ord_orders_get');
            $ord = ria_mysql_fetch_assoc($rord);

            $this->assertEquals(1, $ord['user'], 'Erreur: identifiant du client ayant passé la commande non conforme à la valeur dans la base de donnée');

            $this->assertEquals(self::$order_id, $ord['id'], 'Erreur: Identifiant de la commande non conforme à la valeur dans la base de donnée');
        }

        /** Cette fonction permet de tester la récupéraction des données d'une commande avec ord_orders_get_simple
         */
        public function testOrdersGetSimple(){

            $rord = ord_orders_get_simple(array('id' => self::$order_id));
            $this->assertTrue($rord || ria_mysql_num_rows($rord), 'Erreur: Aucun résultat retourné par ord_orders_get');
            self::$order = ria_mysql_fetch_assoc($rord);

            $this->assertEquals(1, self::$order['usr_id'], 'Erreur: identifiant du client ayant passé la commande non conforme à la valeur dans la base de donnée');

            $this->assertEquals(3, self::$order['id'], 'Erreur: Identifiant de la commande non conforme à la valeur dans la base de donnée');
        }

        /** Cette fonction permet de tester la récupéraction des adresses lié à la commande
         */
        public function testLoadAdresses(){
            $adresses = ord_orders_address_load(self::$order);
            $this->assertArrayHasKey('type' , $adresses, "Erreur: La commande n'a pas de type adresse de livraison.");
            $this->assertArrayHasKey('invoice' , $adresses, "Erreur: La commande n'a pas d'adresse de facturation.");
            $this->assertArrayHasKey('delivery' , $adresses, "Erreur: La commande n'a pas d'adresse de livraison.");
            $this->assertEquals(3, count($adresses), 'Erreur: Nombre d\'élément dans le tableau de la commande incorrecte');
            $this->assertEquals('store', $adresses['type'], 'Erreur: Type d\'adresse de livraison non conforme à celle de la commande');
        }
	}
