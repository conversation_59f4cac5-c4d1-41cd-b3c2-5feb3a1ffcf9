<?php

require_once('orders.inc.php');

/**
 * @backupGlobals disabled
 */
class ordersSetParentIdTest extends PHPUnit_Framework_TestCase
{
	/**
	 * Test les contrôles de paramètres pour la fonction "ord_products_set_parent_id".
	 *
	 * @dataProvider invalidParameters
	 *
	 * @param  int $orderId
	 * @param  int $currentParentId
	 * @param  int $newParentId
	 * @return void
	 */
	public function testInvalidParametersWillReturnFalse($orderId, $currentParentId, $newParentId)
	{
		$this->assertFalse(
			ord_products_set_parent_id($orderId, $currentParentId, $newParentId)
		);
	}

	/**
	 * Test la fonction "ord_products_set_parent_id".
	 *
	 * @dataProvider validParameters
	 *
	 * @param  int $orderId
	 * @param  int $currentParentId
	 * @param  int $newParentId
	 * @return void
	 */
	public function testOrderSetRef($orderId, $currentParentId, $newParentId)
	{
		$this->assertTrue(
			ord_products_set_parent_id($orderId, $currentParentId, $newParentId)
		);
	}

	/**
	 * Fournit des paramètres valides pour tester la fonction "ord_products_set_parent_id".
	 *
	 * @return array
	 */
	public function validParameters()
	{
		return array(
			array(1, 1, 2),
		);
	}

	/**
	 * Fournit des paramètres invalides pour tester la fonction "ord_products_set_parent_id".
	 * 		- 1ère ligne : Mauvais identifiant de commande
	 * 		- 2ème ligne : Mauvais identifiant parent (actuel)
	 * 		- 4ème ligne : Mauvaise identifiant parent (nouveau)
	 *
	 * @return array
	 */
	public function invalidParameters()
	{
		return array(
			array(0, 1, 2),
			array(1, 0, 2),
			array(1, 1, 0),
		);
	}
}