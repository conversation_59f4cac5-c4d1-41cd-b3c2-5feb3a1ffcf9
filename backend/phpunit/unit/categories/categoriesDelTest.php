<?php
	require_once('categories.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class categoriesDelTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la suppression d'une catégorie
		 */
		public function testCategoriesDel(){

            $this->assertTrue(prd_categories_del(5), 'Erreur lors de la suppression d\'une catégorie');
            
            $this->assertTrue(prd_categories_del(2, true), 'Erreur lors de la suppression d\'une catégorie et de ses produits');
		}
		
		/** Fonction permettant de vérifier la suppression des catégories
		 */
		public function testCategoriesVerifyDel(){

			$this->assertFalse(prd_categories_exists(2) || prd_categories_exists(5) , 'Erreur lors de la vérification de la suppression de la catégorie');

			$this->assertFalse(prd_categories_exists(4), 'Erreur: les sous catégories non pas été supprimée');
		}

    }

