<?php
	require_once('products.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class productsUpdateTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la mise à jour d'un produit
         * @dataProvider validProducts
		 */
		public function testValidProductsUpdate($id, $ref, $name, $desc, $brand, $publish, $weight, $length, $width, $height, $keywords) {

            $this->assertTrue(prd_products_update($id, $ref, $name, $desc, $brand, $publish, $weight, $length, $width, $height, $keywords), 'Erreur lors de la mise à jour d\'un produit');

            // Vérifie que les champs ont bien été mis à jour
            $rprd = prd_products_get(100);
            $this->assertTrue( $rprd && ria_mysql_num_rows($rprd), 'Erreur lors de la vérification des champs du produit mis à jour');
			$prd = ria_mysql_fetch_assoc($rprd);

            $this->assertEquals( $ref, $prd['ref'], 'Erreur: référence du produit non mise à jour' );

            $this->assertEquals( $name, $prd['name'], 'Erreur: nom du produit non mis à jour' );

            $this->assertEquals( $desc, $prd['desc'], 'Erreur: description du produit non mise à jour' );

            $this->assertEquals( $brand, $prd['brd_id'], 'Erreur: marque du produit non mise à jour' );

            $this->assertTrue( $publish == $prd['publish'], 'Erreur: propriété publish du produit non mise à jour');

            $this->assertEquals( $weight, $prd['weight'], 'Erreur: poids du produit non mis à jour' );

            $this->assertEquals( $length, $prd['length'], 'Erreur: longueur du produit non mise à jour' );

            $this->assertEquals( $width, $prd['width'], 'Erreur: largeur du produit non mise à jour' );

            $this->assertEquals( $height, $prd['height'], 'Erreur: hauteur du produit non mise à jour' );

            $this->assertEquals( $keywords, $prd['keywords'], 'Erreur: mot clé du produit non mis à jour' );
        }        

        /** Fonction permettant de tester la fonction d'update d'un produit avec des paramètres invalides
         * @dataProvider invalidProducts
         */
        public function testInvalidProductsUpdate($id, $ref, $name, $desc, $brand, $publish, $weight, $length, $width, $height, $keywords, $error){

            $this->assertFalse(prd_products_update($id, $ref, $name, $desc, $brand, $publish, $weight, $length, $width, $height, $keywords), $error);
        }

        public static function validProducts(){
            return array(
                //     id       ref       name        desc  brand publish weight length width height     keywords
                array(100, 'NEWREF', 'New name', 'New desc',    2,     1,   10,     20,     2,    7, 'New keyword'),
            );
        }
        
        public static function invalidProducts(){
            return array(
                //     id       ref       name        desc  brand publish weight length width height     keywords                       message d'erreur
                array(100,       '', 'New name', 'New desc', 2355,      1,  10,     20,     2,  7,  'New keyword', 'Erreur : Mise à jour du produits sans référence'),
                array(100, 'NEWREF',         '', 'New desc', 2355,      1,  10,     20,     2,  7,  'New keyword', 'Erreur : Mise à jour du produits sans nom'),
                array(100, 'NEWREF', 'New name', 'New desc', 1000,      1,  10,     20,     2,  7,  'New keyword', 'Erreur : Mise à jour du produits avec une marque invalide'),
            );
        }
	}
