<?php
	require_once('products.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class productsGetTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la récupération des données d'un produit
		 */
		public function testProductsGetById() {

            $rprd = prd_products_get_simple(1);
			$this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 1, 'Erreur lors de la récupération d\'un produit par son id');
            $prd = ria_mysql_fetch_assoc($rprd);            
            $this->assertEquals( 1, $prd['id'], 'Erreur lors de la récupération d\'un produit par son id');

            $rprd = prd_products_get_simple(1000);
            $this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 0, 'Erreur: prd_products_get_simple retourne des informations avec un id de produit invalide');
        }
        
        /** Fonction permettant de tester la récupération d'un produit par sa référence
         */
        public function testProductsGetByRef(){

            $rprd = prd_products_get_simple(0, 'REF2');
            $this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 1, 'Erreur lors de la récupération d\'un produit par ca référence');
            $prd = ria_mysql_fetch_assoc($rprd);
            $this->assertEquals( 2, $prd['id'], 'Erreur lors de la récupération d\'un produit par ca référence');

            $rprd = prd_products_get_simple(0, 'REFinvalide');
            $this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 0, 'Erreur: prd_products_get_simple retourne des informations avec une référence invalide');
        }

        /** Fonction permettant de tester la récupération de produits par leur catégorie
         */
        public function testProductsGetByCat(){

            $rprd = prd_products_get_simple(0, '', false, 1);
            $this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 2, 'Erreur lors de la récupération de produits avec un filtre sur la catégorie');
            while($prd = ria_mysql_fetch_assoc($rprd)){
                $this->assertTrue( in_array($prd['id'], array(1,2)), 'Erreur lors de la récupération de produits avec un filtre sur la catégorie' );
            }

            $rprd = prd_products_get_simple(0, '', false, array(1,4));
            $this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 3, 'Erreur lors de la récupération de produits avec un filtre sur la catégorie');
            while($prd = ria_mysql_fetch_assoc($rprd)){
                $this->assertTrue( in_array($prd['id'], array(1,2,4)), 'Erreur lors de la récupération de produits avec un filtre sur la catégorie');
            }

            $rprd = prd_products_get_simple(0, '', false, 3, false);
            $this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 0, 'Erreur lors de la récupération de produits avec un filtre sur la catégorie');

            $rprd = prd_products_get_simple(0, '', false, 3, true);
            $this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 4, 'Erreur lors de la récupération de produits avec un filtre sur la catégorie et catchilds=true'); 
            while($prd = ria_mysql_fetch_assoc($rprd)){
                $this->assertTrue( in_array($prd['id'], array(1,2,3,4)), 'Erreur lors de la récupération de produits avec un filtre sur la catégorie et catchilds=true');
            }

            $rprd = prd_products_get_simple(0, '', false, 1000);
            $this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 0, 'Erreur: prd_products_get_simple retourne des informations avec un id de catégorie invalide');
        }

        /** Fonction permettant de tester la récupération de produits par l'id de leur marque
         */
        public function testProductsGetByBrand(){

            $rprd = prd_products_get_simple(0, '', false, 0, false, false, false, false, array('brand' => 1));
			$this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 3, 'Erreur lors de la récupération de produits avec un filtre sur la marque');
            while( $prd = ria_mysql_fetch_assoc($rprd)){
                $this->assertTrue( in_array($prd['id'], array(1,2,3)));
            }

            $rprd = prd_products_get_simple(0, '', false, 0, false, false, false, false, array('brand' => array(1, 2)));
			$this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 4, 'Erreur lors de la récupération de produits avec un filtre sur la marque');
            while( $prd = ria_mysql_fetch_assoc($rprd)){
                $this->assertTrue( in_array($prd['id'], array(1,2,3,4)));
            }

            $rprd = prd_products_get_simple(0, '', false, 0, false, false, false, false, array('brand' => 1000));
            $this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 0, 'Erreur: prd_products_get_simple retourne des informations avec un id de marque invalide');
        }

        /** Fonction permettant de tester la récupération de produits avec un filtre sur la marque et la catégorie
         */
        public function testProductsGetByBrandAndCat(){
        
            $rprd = prd_products_get_simple(0, '', false, 1, false, false, false, false, array('brand' => 1));
            $this->assertTrue($rprd && ria_mysql_num_rows($rprd) == 2, 'Erreur lors de la récupération de produits avec un filtre sur la catégorie et la marque');
            while( $prd = ria_mysql_fetch_assoc($rprd)){
                $this->assertTrue( in_array($prd['id'], array(1,2)));
            }
        }

        /** Fonction permettant de tester la récupération du code bare d'un produit
         */
        public function testProductsBarcodeGet(){

            $this->assertEquals( 'barcode', prd_products_get_barcode(1), 'Erreur: code bare du produit non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de tester la récupération de la marque d'un produit
         */
        public function testProductsBrandGet(){

            $this->assertEquals( 1, prd_products_get_brand(1), 'Erreur: identifiant de la marque du produit non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de tester la récupération de la description d'un produit
         */
        public function testProductsDescGet(){

            $this->assertEquals( 'description du produit 1', prd_products_get_desc(1), 'Erreur: description du produit non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de tester la récupération de la description longue d'un produit
         */
        public function testProductsDescLongGet(){

            $this->assertEquals( 'une longue description du produit 1', prd_products_get_desc_long(1), 'Erreur: description longue du produit non conforme à la valeur dans la base de donnée' );        }

        /** Fonction permettant de tester la récupération de l'ecotaxe d'un produit *
         */
        public function testProductsEcotaxeGet(){
            
            $this->assertEquals( 0.0000, prd_products_get_ecotaxe(1), 'Erreur: ecotaxe du produit non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de tester la récupération de l'information follow_stock d'un produit
         */
        public function testProductsFollowStockGet(){
            
            $this->assertEquals( 1, prd_products_get_follow_stock(1), 'Erreur: information follow_stock du produit non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de tester la récupération de la durée de garantie d'un produit
         */
        public function testProductsGarantieGet(){

            $this->assertEquals( 12, prd_products_get_garantie(1), 'Erreur: durée de garantie du produit non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de tester la récupération de l'id d'un produit
         */
        public function testProductsIdGet(){

            $this->assertEquals( 1, prd_products_get_id('REF1'), 'Erreur: id du produit non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de tester si un produit est nouveau
         */
        public function testProductsIsNew(){

            $this->assertEquals( 0, prd_products_get_is_new(1), 'Erreur: prd_products_get_is_new retourne vrai pour un produit ancien' );

            $this->assertEquals( 1, prd_products_get_is_new(2), 'Erreur: prd_products_get_is_new retourne faux pour un produit nouveau' );
        }

        /** Fonction permettant de tester la récupération du tarif d'un produit
         */
        public function testProductsPriceGet(){
            
            $rprc = prd_products_get_price(1);
            $this->assertTrue( $rprc && ria_mysql_num_rows($rprc), 'Erreur lors de la récupération du tarif d\'un produit' );
            $prc = ria_mysql_fetch_assoc($rprc);

            $this->assertEquals( 5, $prc['price_ht'], 'Erreur: prix ht du produit non conforme à la valeur dans la base de donnée' );
            $this->assertEquals( 1.2, $prc['tva_rate'], 'Erreur: taux de tva du produit non conforme à la valeur dans la base de donnée' );
            $this->assertEquals( 6, $prc['price_ttc'], 'Erreur: prix ttc du produit non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de tester la récupération de la référence d'un produit
         */
        public function testProductsRefGet(){

            $this->assertEquals( 'REF1', prd_products_get_ref(1), 'Erreur: référence du produit non conforme à la valeur dans la base de donnée' );
        }    

        /** Fonction permettant de tester la récupération de l'url d'un produit
         */
        public function testProductsUrlGet(){

            $this->assertEquals( '/catalogue/parent/fille-1/produit-1' ,prd_products_get_url(1), 'Erreur: url du produit non conforme à la valeur dans la base de donnée' );
        }

        /** Fonction permettant de tester la récupération du poids d'un produit
         */
        public function testProductsWeightGet(){

            $this->assertEquals( 8, prd_products_get_weight(1), 'Erreur: poids du produit non conforme à la valeur dans la base de donnée' );        
        }

        /** Fonction permettant de tester la récupération des catégories d'un produits
         */
        public function testProductsCategoriesGet(){

            $cat = prd_products_categories_get_array(1);
            $this->assertTrue( is_array( $cat ) && $cat == array(1), 'Erreur: catégorie du produit non conforme à la valeur dans la base de donnée');
        }

        /** Fonction permettant de tester si un produit est suivi en stock 
         */
        public function testProductsIsFollowStock(){

            $this->assertTrue(prd_products_is_follow_stock('', 1), 'Erreur: prd_products_is_follow_stock retourne faux pour un produit suivit en stock');

            $this->assertTrue(prd_products_is_follow_stock('REF1'), 'Erreur: prd_products_is_follow_stock retourne faux pour un produit suivit en stock');

            $this->assertFalse(prd_products_is_follow_stock('', 2), 'Erreur: prd_products_is_follow_stock retourne vrai pour un produit non suivit en stock');

            $this->assertFalse(prd_products_is_follow_stock('REF2'), 'Erreur: prd_products_is_follow_stock retourne vrai pour un produit non suivit en stock');
        }

        /** Fonction permettant de tester si un produit éxiste
         */
        public function testProductsExists(){

            $this->assertTrue( true == prd_products_exists(1), 'Erreur: prd_products_exists retourne faux avec un id de produit éxistant');

            $this->assertFalse( prd_products_exists(1000), 'Erreur: prd_products_exists retourne vrai avec un id de produit inéxistant');
        }

        /** Fonction permettant de tester si un produit éxiste
         */
        public function testProductsExistsRef(){

            $this->assertTrue( true == prd_products_exists_ref('REF1'), 'Erreur: prd_products_exists_ref retourne faux avec une référence produit éxistante');

            $this->assertFalse( prd_products_exists_ref('REFinexistante'), 'Erreur: prd_products_exists_ref retourne vrai avec une référence produit inexistante');
        }
	}
