<?php
	require_once('prd/colisage.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class colisageGetTest extends PHPUnit_Framework_TestCase {

        /** Fonction permettant de tester la récupération d'un conditionnement par son id
         */
        public function testColisageGetById(){

            $rcol = prd_colisage_types_get(1);
            $this->assertTrue($rcol && ria_mysql_num_rows($rcol) == 1, 'Erreur lors de la récupération du conditionnement pas son id');
            $col = ria_mysql_fetch_assoc($rcol);

            $this->assertEquals( 1, $col['id'], 'Erreur lors de la récupération du conditionnement par son id');
        }

        /** Fonction permettant de tester la récupération d'un conditionnement par sa quantité
         */
        public function testColisageGetByQte(){

            $rcol = prd_colisage_types_get(0, 15);
            $this->assertTrue($rcol && ria_mysql_num_rows($rcol) == 1, 'Erreur lors de la récupération du conditionnement pas sa quantité');
            $col = ria_mysql_fetch_assoc($rcol);

            $this->assertEquals( 2, $col['id'], 'Erreur lors de la récupération du conditionnement par sa quantité');
        }

        /** Fonction permettant de tester la récupération d'un conditionnement par son nom
         */
        public function testColisageGetByName(){

            $rcol = prd_colisage_types_get(0, false, false, 'Colisage test');
            $this->assertTrue($rcol && ria_mysql_num_rows($rcol) == 1, 'Erreur lors de la récupération du conditionnement pas son nom');
            $col = ria_mysql_fetch_assoc($rcol);

            $this->assertEquals( 1, $col['id'], 'Erreur lors de la récupération du conditionnement par son nom');
        }

		/** Fonction permettant de tester la récupération du libellé associé à un conditionnement
		 */
		public function testColisageGetName(){

            $this->assertEquals( 'Colisage test', prd_colisage_types_get_name( 1, false, ' ' ), 'Erreur: nom du conditionnement non conforme à la valeur dans la base de donnée');

            $this->assertEquals( 'Colisage test 10.0000', prd_colisage_types_get_name( 1, true, ' ' ), 'Erreur: nom du conditionnement non conforme à la valeur dans la base de donnée');

            $this->assertEquals( 'Colisage test qte:10.0000', prd_colisage_types_get_name( 1, true, ' qte:' ), 'Erreur: nom du conditionnement non conforme à la valeur dans la base de donnée');

            $this->assertFalse( prd_colisage_types_get_name( 1000, true, ' qte:' ), 'Erreur: prd_colisage_type_get_name retourne un nom pour un identifiant de conditionnement invalide');
        }  

        /** Fonction permettant de tester la récupération de la quantité d'un conditionnement
         */
        public function testColisageGetQte(){
            
            $this->assertEquals( 10, prd_colisage_types_get_qte(1), 'Erreur: quantité du conditionnement non conforme à la valeur dans la base de donnée');

            $this->assertFalse(prd_colisage_types_get_qte(1000), 'Erreur: prd_colisage_type_get_qte retourne une quantité pour un identifiant de conditionnement invalide');
        }

        /** Fonction permettant de tester la récupération des conditionnements effectifs de produits
         */
        public function testColisageClasifyGet(){

            // récupération par id du conditionnement
            $rcol = prd_colisage_classify_get(1);
            $this->assertTrue($rcol && ria_mysql_num_rows($rcol) == 1, 'Erreur lors de la récupération des conditionnements effectifs de produits');
            $col = ria_mysql_fetch_assoc($rcol);

            $this->assertEquals( 1, $col['col_id'], 'Erreur lors de la récupération des conditionnements effectifs de produits');
            $this->assertEquals( 1, $col['prd_id'], 'Erreur lors de la récupération des conditionnements effectifs de produits');

            // récupération par id du produit
            $rcol = prd_colisage_classify_get(0, 1);
            $this->assertTrue($rcol && ria_mysql_num_rows($rcol) == 1, 'Erreur lors de la récupération des conditionnements effectifs de produits');
            $col = ria_mysql_fetch_assoc($rcol);

            $this->assertEquals( 1, $col['col_id'], 'Erreur lors de la récupération des conditionnements effectifs de produits');
            $this->assertEquals( 1, $col['prd_id'], 'Erreur lors de la récupération des conditionnements effectifs de produits');

            // récupération par quantité du conditionnement
            $rcol = prd_colisage_classify_get(0, 0, 15);
            $this->assertTrue($rcol && ria_mysql_num_rows($rcol) == 1, 'Erreur lors de la récupération des conditionnements effectifs de produits');
            $col = ria_mysql_fetch_assoc($rcol);

            $this->assertEquals( 2, $col['col_id'], 'Erreur lors de la récupération des conditionnements effectifs de produits');
            $this->assertEquals( 5, $col['prd_id'], 'Erreur lors de la récupération des conditionnements effectifs de produits');
        }

        /** Fonction permettant de tester l'éxistance d'un type de conditionnement
         */
        public function testColisageExists(){

            $this->assertTrue( true == prd_colisage_types_exists(1), 'Erreur: prd_colisage_type_exists retourne faux avec un identifiant de conditionnement éxistant');

            $this->assertTrue( false == prd_colisage_types_exists(1000), 'Erreur: prd_colisage_types_exists retourne vrai avec un identifiant de conditionnement inexistant');
        }
    }
