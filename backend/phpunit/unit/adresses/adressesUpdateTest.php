<?php
	require_once('users.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class adressesUpdateTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la mise à jour d'une adresse
		 * @dataProvider validAdresses
		 */
		public function testValidAdressesUpdate($user, $id, $type, $title, $firstname, $lastname, $society, $siret, $address1, $address2, $postal_code, $city, $country, $phone, $fax, $mobile, $work, $from_sync, $adr_name, $email, $cnt_code, $address3, $ref_gescom, $country_state) {

            $this->assertTrue(gu_adresses_update($user, $id, $type, $title, $firstname, $lastname, $society, $siret, $address1, $address2, $postal_code, $city, $country, $phone, $fax, $mobile, $work, $from_sync, $adr_name, $email, $cnt_code, $address3, $ref_gescom, $country_state), 'Erreur lors de la mise à jour de l\'adresse');

			// Vérifie que les paramètres on bien été mis à jour
			$radr = gu_adresses_get(100,100);
			$this->assertTrue($radr || ria_mysql_num_rows($radr), 'Erreur: adresse mise à jour non trouvé');
			$adr = ria_mysql_fetch_assoc($radr);

			if($type == 1){//particulier
				$this->assertEquals( $title, $adr['title_id'], 'Erreur: civilité de l\'adresse non mise à jour');
				$this->assertEquals( $firstname, $adr['firstname'], 'Erreur: prénom de l\'adresse non mis à jour');
				$this->assertEquals( $lastname, $adr['lastname'], 'Erreur: nom de l\'adresse non mis à jour');
				$this->assertEquals( '', $adr['society'], 'Erreur: société de l\'adresse non mis à jour');
				$this->assertEquals( '', $adr['siret'], 'Erreur: siret de l\'adresse non mis à jour');
			}else if($type == 2){//société
				$this->assertEquals( '', $adr['title_id'], 'Erreur: civilité de l\'adresse non mise à jour');
				$this->assertEquals( '', $adr['firstname'], 'Erreur: prénom de l\'adresse non mis à jour');
				$this->assertEquals( '', $adr['lastname'], 'Erreur: nom de l\'adresse non mis à jour');
				$this->assertEquals( $society, $adr['society'], 'Erreur : société de l\'adresse non mis à jour');
				$this->assertEquals( $siret, $adr['siret'], 'Erreur: siret de l\'adresse non mis à jour');
			}else{//professionnel
				$this->assertEquals( $title, $adr['title_id'], 'Erreur: civilité de l\'adresse non mise à jour');
				$this->assertEquals( $firstname, $adr['firstname'], 'Erreur: prénom de l\'adresse non mis à jour');
				$this->assertEquals( $lastname, $adr['lastname'], 'Erreur: nom de l\'adresse non mis à jour');
				$this->assertEquals( $society, $adr['society'], 'Erreur : société de l\'adresse non mis à jour');			
				$this->assertEquals( $siret, $adr['siret'], 'Erreur: siret de l\'adresse non mis à jour');
			}

			$this->assertEquals( $address1, $adr['address1'], 'Erreur: la première partie de l\'adresse n\'a pas été mise à jour' );

			$this->assertEquals( $address2, $adr['address2'], 'Erreur: la deuxième partie de l\'adresse n\'a pas été mise à jour' );

			$this->assertEquals( $address3, $adr['address3'], 'Erreur: la troisième partie de l\'adresse n\'a pas été mise à jour' );

			$this->assertEquals( $postal_code, $adr['postal_code'], 'Erreur: le code postal de l\'adresse n\'a pas été mis à jour' );

			$this->assertEquals( $city, $adr['city'], 'Erreur: la ville de l\'adresse n\'a pas été mise à jour' );

			$this->assertEquals( $country, $adr['country'], 'Erreur: le pays de l\'adresse n\'a pas été mis à jour' );

			$this->assertEquals( $phone, $adr['phone'], 'Erreur: le téléphone de l\'adresse n\'a pas été mis à jour' );

			$this->assertEquals( $fax, $adr['fax'], 'Erreur: le fax de l\'adresse n\'a pas été mis à jour' );

			$this->assertEquals( $mobile, $adr['mobile'], 'Erreur: le téléphone portable de l\'adresse n\'a pas été mis à jour' );

			$this->assertEquals( $work, $adr['phone_work'], 'Erreur: le téléphone en journée de l\'adresse n\'a pas été mis à jour' );

			$this->assertEquals( $adr_name, $adr['description'], 'Erreur: la description de l\'adresse de l\'adresse n\'a pas été mise à jour' );

			$this->assertEquals( $email, $adr['email'], 'Erreur: l\'email de l\'adresse n\'a pas été mis à jour' );

			$this->assertEquals( $cnt_code, $adr['country_code'], 'Erreur: le code du pays de l\'adresse n\'a pas été mis à jour' );

			$this->assertEquals( $ref_gescom, $adr['ref_gescom'], 'Erreur: la référence de l\'adresse dans la gescom n\'a pas été mise à jour' );

			$this->assertEquals( $country_state, $adr['country_state'], 'Erreur:  l\'état / la province de l\'adresse n\'a pas été mise à jour' );
        }
        
        /** Fonction permettant de tester la mise à jour d'une adresse avec des paramètres invalide
		 * @dataProvider invalidAdresses
		 */
		public function testInvalideAdressesUpdate($user, $id, $type, $title, $firstname, $lastname, $society, $siret, $address1, $address2, $postal_code, $city, $country, $phone, $fax, $mobile, $work, $from_sync, $adr_name, $email, $cnt_code, $address3, $ref_gescom, $country_state, $error) {

            $this->assertFalse(gu_adresses_update($user, $id, $type, $title, $firstname, $lastname, $society, $siret, $address1, $address2, $postal_code, $city, $country, $phone, $fax, $mobile, $work, $from_sync, $adr_name, $email, $cnt_code, $address3, $ref_gescom, $country_state), $error);
		}


		public function validAdresses(){
			return array(
                //   user  id type title   firstname lastname    society                siret      address1        address2 postal_code  city   country          phone            fax          mobile            work   from_sync   adr_name             email    cnt_code    address3   ref_gescom  country_state 
                array(100, 100,   1,    1,    'Prenom',   'Nom',        '',               '',           '',             '',      '',      '',       '',           '',             '',             '',             '',  false,           '',                '',      null,          '',      null,      '' ),
                array(100, 100,   2,    1,    'Prenom',   'Nom', 'Société', '50539719000024',  'Adresse 1',             '', '79000', 'Niort', 'FRANCE',           '',             '',             '',             '',  false,           '',                '',      'EN',          '',      null,      '' ),
				array(100, 100,   3,    1,    'Prenom',   'Nom',        '',               '',  'Adresse 1',    'Adresse 2', '79000', 'Niort', 'FRANCE', '0549050505',   '0549050505',   '0606060606',   '0549050505',  false,       'Desc', '<EMAIL>',      'FR', 'Adresse 3',      null,      '' ),
				array(100, 100,   1,    1,    'Prenom',   'Nom',        '',               '',  'Adresse 1',    'Adresse 2', '79000', 'Niort', 'FRANCE', '0549050505',   '0549050505',   '0606060606',   '0549050505',  false,       'Desc', '<EMAIL>',      'FR', 'Adresse 3',      null,      '' ),
			);
		}

		public function invalidAdresses(){
			return array(
                //   user    id   type title    firstname  lastname  society     siret      address1    address2 postal_code   city   country   phone     fax   mobile   work   from_sync adr_name   email  cnt_code address3 ref_gescom country_state message d'erreur
                array(10000, 100,   1,    1,    'Prenom',   'Nom',      '',       '',           '',          '',       '',       '',       '', 		'', 	'', 	'', 	'', 	false, 		'', 	'', 	null, 	  '', 		null, 	  '', 'Erreur: mise à jour de l\'adresse avec un id utilisateur invalide'),
                array(100,   100,  -1,    1,    'Prenom',   'Nom',      '',       '',           '',          '',       '',       '',       '', 		'', 	'', 	'', 	'', 	false, 		'', 	'', 	null, 	  '', 		null, 	  '', 'Erreur: mise à jour de l\'adresse avec un type invalide'),
                array(100,   100,   2,    1,    'Prenom',   'Nom',      '',       '',           '',          '',       '',       '',       '', 		'', 	'', 	'', 	'', 	false, 		'', 	'', 	null, 	  '', 		null, 	  '', 'Erreur: mise à jour d\'une adresse professionelle sans renseigner de société'),
                array(100,   100,   1,   -1,    'Prenom',   'Nom',      '',       '',           '',          '',       '',       '',       '', 		'', 	'', 	'', 	'', 	false, 		'', 	'', 	null, 	  '', 		null, 	  '', 'Erreur: mise à jour de l\'adresse avec un title invalide'),
			);
		}

		/** Fonction permettant de tester le masquage d'une adresse
		 */
		public function testAdresseSetMasked(){
			
			$this->assertFalse(gu_adresses_is_masked(100), 'Erreur: gu_adresses_is_masked retourne vrai pour une adresse non masqué');

			$this->assertTrue(gu_adresses_set_masked(100), 'Erreur lors du masquage de l\'adresse');

			$this->assertTrue(gu_adresses_is_masked(100), 'Erreur: gu_adresses_is_masked retourne faux pour une adresse masqué');
		}
	}
