<?php
	require_once('users.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class adressesGetTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la récupération d'une adresse par l'id
        */
		public function testAdressesGetById() {

            $radr = gu_adresses_get(1, 3);
			$this->assertTrue($radr && ria_mysql_num_rows($radr) == 1, 'Erreur: lors de la récupération d\'une adresse par son id');
            $adr = ria_mysql_fetch_assoc($radr);

            $this->assertEquals(1 ,$adr['usr_id'], 'Erreur lors de la récupération par id d\'une adresse');

            $this->assertEquals(3 ,$adr['id'], 'Erreur lors de la récupération par id d\'une adresse');
        }

        /** Fonction permettant de tester l'éxistance d'une adresse
         */
        public function testAdressesExists(){

            $this->assertTrue(gu_adresses_exists(100), 'Erreur: gu_adresses_exists retourne faux pour une adresse éxistante');

            $this->assertFalse(gu_adresses_exists(10000), 'Erreur: gu_adresses_exists retourne vrai avec un identifiant d\'adresse inexistant');
        }

        /** Fonction permettant de vérifier qu'une adresse n'existe pas déja
         */
        public function testAdressesGetDoublon(){

            $this->assertTrue(gu_adresses_get_doublon( array('adr_address1' => 'adresse 1', 'adr_address2' => 'adresse 2', 'adr_address3' => '','adr_city' => 'niort', 'adr_postal_code' => '79000', 'adr_country' => 'france')), 'Erreur: gu_adresses_get_doublon retourne faux pour une adresse doublon');

            $this->assertTrue(gu_adresses_get_doublon( array('adr_address1' => 'adresse 1')), 'Erreur: gu_adresses_get_doublon retourne faux pour une adresse doublon');

            $this->assertFalse(gu_adresses_get_doublon( array('adr_address1' => 'adresse inexistante')), 'Erreur: gu_adresses_get_doublon retourne vrai pour une adresse non doublon');

            $this->assertFalse(gu_adresses_get_doublon( array('adr_address1' => 'adresse inexistante', 'adr_address2' => 'adresse 2', 'adr_address3' => '','adr_city' => 'niort', 'adr_postal_code' => '79000', 'adr_country' => 'france')), 'Erreur: gu_adresses_get_doublon retourne vrai pour une adresse non doublon');
        }

	}