<?php
	require_once('users.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class adressesAddTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester l'ajout d'une nouvelle adresse
		 * @dataProvider validAdresses
		 */
		public function testValidAdressesAdd($user, $type, $title, $firstname, $lastname, $society, $siret, $address1, $address2, $postal_code, $city, $country, $phone, $fax, $mobile, $work, $adr_name, $email, $cnt_code, $address3, $ref_gescom, $country_state ) {

            $adr_id = gu_adresses_add($user, $type, $title, $firstname, $lastname, $society, $siret, $address1, $address2, $postal_code, $city, $country, $phone, $fax, $mobile, $work, $adr_name, $email, $cnt_code, $address3, $ref_gescom, $country_state);
			
			//Vérifie que l'id est un integer
			$this->assertThat($adr_id, $this->logicalAnd(
				$this->isType('int'), 
				$this->greaterThan(0)
			), "Erreur: l'id retourné par gu_adresses_add n'est pas valide");

			//Vérifie que les champs sont corrects
			$radr = gu_adresses_get($user, $adr_id);
			$this->assertTrue($radr && ria_mysql_num_rows($radr) == 1, 'Erreur lors de la vérification des champs de l\'adresse ajoutée');
			$adr = ria_mysql_fetch_assoc($radr);
		
			$this->assertEquals( $user, $adr['usr_id'], 'Erreur: id du client associé à l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $type, $adr['type_id'], 'Erreur: type de l\'adresse non conforme à la valeur lors de l\'ajout');

			if( $type==1 ){ // Particulier 
				$this->assertEquals( $title, $adr['title_id'], 'Erreur: identifiant de la civilité de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( $firstname, $adr['firstname'], 'Erreur: prénom de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( $lastname, $adr['lastname'], 'Erreur: nom de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( '', $adr['society'], 'Erreur: société de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( '', $adr['siret'], 'Erreur: siret de l\'adresse non conforme à la valeur lors de l\'ajout');
			}elseif( $type==2 ){ // Société
				$this->assertEquals( 0, $adr['title_id'], 'Erreur: identifiant de la civilité de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( '', $adr['firstname'], 'Erreur: prénom de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( '', $adr['lastname'], 'Erreur: nom de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( $society, $adr['society'], 'Erreur: société de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( $siret, $adr['siret'], 'Erreur: siret de l\'adresse non conforme à la valeur lors de l\'ajout');
			}else{ // Professionnel
				$this->assertEquals( $title, $adr['title_id'], 'Erreur: identifiant de la civilité de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( $firstname, $adr['firstname'], 'Erreur: prénom de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( $lastname, $adr['lastname'], 'Erreur: nom de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( $society, $adr['society'], 'Erreur: société de l\'adresse non conforme à la valeur lors de l\'ajout');
				$this->assertEquals( $siret, $adr['siret'], 'Erreur: siret de l\'adresse non conforme à la valeur lors de l\'ajout');
			}
			
			$this->assertEquals( $address1, $adr['address1'], 'Erreur: première partie de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $address2, $adr['address2'], 'Erreur: deuxième partie de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $postal_code, $adr['zipcode'], 'Erreur: code postal de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $city, $adr['city'], 'Erreur: ville de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $country, $adr['country'], 'Erreur: pays de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $phone, $adr['phone'], 'Erreur: numéro de téléphone de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $fax, $adr['fax'], 'Erreur: fax de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $mobile, $adr['mobile'], 'Erreur: téléphone portable de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $work, $adr['phone_work'], 'Erreur: téléphone en journée de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $adr_name, $adr['description'], 'Erreur: description de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $email, $adr['email'], 'Erreur: email de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $cnt_code, $adr['country_code'], 'Erreur: code du pays de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $address3, $adr['address3'], 'Erreur: troisième partie de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $ref_gescom, $adr['ref_gescom'], 'Erreur: référence dans la gescom de l\'adresse non conforme à la valeur lors de l\'ajout');
			
			$this->assertEquals( $country_state, $adr['country_state'], 'Erreur: état / province de l\'adresse non conforme à la valeur lors de l\'ajout');
		}

		/** Fonction permettant de tester l'insertion d'une adresse invalide
		 * @dataProvider invalidAdresses
		 */
		public function testInvalidAdressesAdd($user, $type, $title, $firstname, $lastname, $society, $siret, $address1, $address2, $postal_code, $city, $country, $phone, $fax, $mobile, $work, $adr_name, $email, $cnt_code, $address3, $ref_gescom, $country_state, $error ){

            $this->assertFalse(gu_adresses_add($user, $type, $title, $firstname, $lastname, $society, $siret, $address1, $address2, $postal_code, $city, $country, $phone, $fax, $mobile, $work, $adr_name, $email, $cnt_code, $address3, $ref_gescom, $country_state), 'Erreur: une adresses à été enregistré avec des paramètre invalide');
		}

		public static function validAdresses(){
			return array(
                //   user type title   firstname lastname    society     	     siret      address1        address2 postal_code  city   country          phone            fax          mobile            work         adr_name             email    cnt_code    address3   ref_gescom  country_state 
                array(100,  1,    1,    'Prenom',   'Nom',        '', 	            '',           '',             '',      '',      '',       '',           '',             '',             '',             '',             '',                '',      null,          '',      null,      '' ),
                array(100,  2,    1,    'Prenom',   'Nom', 'Société', '50539719000024',  'Adresse 1',             '', '79000', 'Niort', 'FRANCE',           '',             '',             '',             '',             '',                '',      'FR',          '',      null,      '' ),
				array(100,  1,    1,    'Prenom',   'Nom', 'Société',         	    '',  'Adresse 1',    'Adresse 2', '79000', 'Niort', 'FRANCE', '0549050505',   '0549050505',   '0606060606',   '0549050505',         'Name', '<EMAIL>',      'FR', 'Adresse 3',      null,      '' ),
				array(100,  3,    1,    'Prenom',   'Nom', 'Société',         	    '',  'Adresse 1',    'Adresse 2', '79000', 'Niort', 'FRANCE', '0549050505',   '0549050505',   '0606060606',   '0549050505',         'Name', '<EMAIL>',      'FR', 'Adresse 3',      null,      '' ),
			);
		}

		public static function invalidAdresses(){
			return array(
                //   user type title    firstname  lastname  society     siret      address1        address2 postal_code city country phone fax mobile work adr_name email cnt_code address3 ref_gescom country_state   message d'erreur
                array(10000,  1,    1,    'prenom',   'nom',      '',       '',           '',             '',     '',     '',       '', 	'', 	'', 	'', 	'', 	'', 	'', 	null, 	'', 	null, 	'', 'Erreur: une adresse associé à un utilisateur inéxistant à été ajouté' ),
                array(100,   -1,    1,    'prenom',   'nom',      '',       '',           '',             '',     '',     '',       '', 	'', 	'', 	'', 	'', 	'', 	'', 	null, 	'', 	null, 	'', 'Erreur: une adresse avec un type invalide à été ajouté' ),
                array(100,    2,    1,    'prenom',   'nom',      '',       '',           '',             '',     '',     '',       '', 	'', 	'', 	'', 	'', 	'', 	'', 	null, 	'', 	null, 	'', 'Erreur: une adresse professionnelle sans société à été ajouté' ),
                array(100,    1,   -1,    'prenom',   'nom',      '',       '',           '',             '',     '',     '',       '', 	'', 	'', 	'', 	'', 	'', 	'', 	null, 	'', 	null, 	'', 'Erreur: une adresse avec un identifiant de civilité invalide à été ajouté' ),
			);
		}
	}