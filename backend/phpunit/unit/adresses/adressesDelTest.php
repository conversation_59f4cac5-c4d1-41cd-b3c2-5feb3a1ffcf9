<?php
	require_once('users.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class adressesDelTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la suppression d'une adresse
		 */
		public function testAdressesDel() {

            $this->assertTrue(gu_adresses_del(100), 'Erreur lors de la suppression de l\'adresse');
		}
		
		/** Fonction permettant de vérifier la suppression d'une adresse
		 */
		public function testAdressesVerifyDel(){

			$radr = gu_adresses_get(100,100);
			$this->assertTrue($radr && ria_mysql_num_rows($radr) == 0, 'Erreur lors de la vérification de la suppression de l\'adresse');
		}
	}