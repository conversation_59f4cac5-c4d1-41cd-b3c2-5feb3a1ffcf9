<?php
	require_once('site.reviews.inc.php');
	require_once('prd/reviews.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class reviewsGetTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la récupération d'un avis consommateur par son id
		 */
		public function testReviewsGetById() {

            $rrvw = rvw_reviews_get(1);
            $this->assertTrue($rrvw && ria_mysql_num_rows($rrvw) == 1, 'Erreur lors de la récupération d\'un avis consommateur par son id');
			$rvw = ria_mysql_fetch_assoc($rrvw);
			
			$this->assertEquals( 1, $rvw['id'], 'Erreur lors de la récupération d\'un avis consommateur par son id');
		}  

		/** Fonction permettant de tester la récupération d'avis consommateur avec un filtre sur l'utilisateur 
		 */
		public function testReviewsGetByUsr(){
			
			$rrvw = rvw_reviews_get(0, null, 1);
            $this->assertTrue($rrvw && ria_mysql_num_rows($rrvw) == 2, 'Erreur lors de la récupération d\'avis consommateur avec un filtre sur l\'utilisateur');
			while( $rvw = ria_mysql_fetch_assoc($rrvw) ){
				$this->assertTrue( in_array($rvw['id'], array(1,2)), 'Erreur lors de la récupération d\'avis consommateur avec un filtre sur l\'utilisateur');
			}
		}
		
		/** Fonction permettant de tester la récupération d'avis consommateur avec un filtre sur le produit
		 */
		public function testPrdReviewsGet(){

			$rrvw = prd_reviews_get(0,1);
            $this->assertTrue($rrvw && ria_mysql_num_rows($rrvw) == 2, 'Erreur lors de la récupération d\'avis consommateur avec un filtre sur le produit');
			while( $rvw = ria_mysql_fetch_assoc($rrvw) ){
				$this->assertTrue( in_array($rvw['id'], array(1,2)), 'Erreur lors de la récupération d\'avis consommateur avec un filtre sur le produit');
			}
		}

		/** Fonction permettant de tester la récupération des statistiques sur les avis consommateurs
		 */
		public function testPrdReviewsStatsGet(){

			$stats = prd_reviews_get_stats(1, null, 12);

			$this->assertEquals( 3.5,$stats['avg'], 'Erreur: moyenne des notes obtenues pour un produit non conforme à la valeur dans la base de donnée');
			$this->assertEquals( 3,$stats['avg_package'], 'Erreur: moyenne des notes obtenues (colis) pour un produit non conforme à la valeur dans la base de donnée');
			$this->assertEquals( 3.5,$stats['avg_delivery'], 'Erreur: moyenne des notes obtenues (livraison) pour un produit non conforme à la valeur dans la base de donnée');
			$this->assertEquals( 2,$stats['count'], 'Erreur: nombre d\'avis pour un produit non conforme à la valeur dans la base de donnée');		
		}
    }

