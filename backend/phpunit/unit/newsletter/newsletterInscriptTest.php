<?php
	require_once('newsletter.inc.php');

	/**
	 *@backupGlobals disabled
	*/
	class newsletterInscriptTest extends PHPUnit_Framework_TestCase {

		public static $id;
		public static $cat_id = 1;
		public static $email = '<EMAIL>'; 
		
		/** Fonction permettant de tester l'entré d'une adresse mail dans le processus d'inscription
		 */
		public function testNewsletterValidInscript(){

			self::$id = newsletter_request_inscript( self::$email, self::$cat_id, false);
			$this->assertTrue( false != self::$id , 'Erreur lors de l\'entré de l\'adresse mail dans le processus d\'inscription');			
		}

		/** Fonction permettant de tester la confirmation d'une inscription à la newsletter  
		 */
		public function testNewsletterValidConfirmInscript(){
			$token = nlr_sub_tokens_get_token(self::$email, self::$cat_id);
			$this->assertTrue(newsletter_confirm_inscript($token), 'Erreur lors de la confirmation de l\'inscription à la newsletter');
		}

		/** Fonction permettant de tester l'entré d'une adresse mail invalide dans le processus d'inscription
		 */
		public function testNewsletterInvalidInscript(){

			$this->assertFalse( false != newsletter_request_inscript( self::$email, self::$cat_id, false) , 'Erreur une adresse déja inscrite est entré dans le processus d\'inscription à la newsletter');
		}

		/** Fonction permettant de tester la confirmation d'une inscription à la newsletter avec un code invalide
		 */
		public function testNewsletterInvalidConfirmInscript(){

			$this->assertFalse(newsletter_confirm_inscript('code invalide'), 'Erreur l\'inscription a été confirmé avec un code invalide');
		}

		/** Fonction permettant de tester si une adresse mail est inscrite à la newsletter 
		 */
		public function testNewsletterIsInscript(){

			$this->assertTrue( true == newsletter_is_inscripted(self::$email), 'Erreur ');

			$this->assertTrue( false == newsletter_is_inscripted('<EMAIL>'), 'Erreur ');
		}
		
	}
