<?php
	require_once('newsletter.inc.php');

	/**
	 *@backupGlobals disabled
	*/
	class newsletterUninscriptTest extends PHPUnit_Framework_TestCase {

		public static $email = '<EMAIL>';
		public static $cat_id = 1;
        
		/** Fonction permettant de tester l'entré d'une adresse mail dans le processus de désinscription
		 */
		public function testNewsletterValidUninscript(){

			
			$this->assertTrue( newsletter_request_uninscript( self::$email, self::$cat_id), 'Erreur lors de l\'entré de l\'adresse mail dans le processus de désinscription');			
		}

		/** Fonction permettant de tester la confirmation d'une désinscription à la newsletter  
		 */
        public function testNewsletterValidConfirmUninscript(){

            $token = nlr_sub_tokens_get_token(self::$email, self::$cat_id, true);
			$this->assertTrue(newsletter_confirm_uninscript($token), 'Erreur lors de la confirmation de la désinscription à la newsletter');
		}

		/** Fonction permettant de tester l'entré d'une adresse mail invalide dans le processus de désinscription
		 */
        public function testNewsletterInvalidUninscript(){

			$this->assertFalse( false != newsletter_request_uninscript( self::$email, 1, false) , 'Erreur une adresse non inscrit est entré dans le processus de désinscription à la newsletter');
		}

		/** Fonction permettant de tester la confirmation d'une désinscription à la newsletter avec un code invalide
		 */
        public function testNewsletterInvalidConfirmUninscript(){

			$this->assertFalse(newsletter_confirm_uninscript('code invalide'), 'Erreur la désinscription à la newsletter a été confirmé avec un code invalide');
		}
		
	}
