<?php
	require_once('brands.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class brandsUpdateTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la mise à jour d'une marque
		 */
		public function testBrandsUpdate() {

            $this->assertTrue(prd_brands_update( 3, 'nouveau nom', 'nouveau titre', 'nouvelle url' ), 'Erreur lors de la mise à jour des informations de la marque');       
            
            // Vérifie que les champs ont bien été mis à jour
            $rbrd = prd_brands_get(3);
			$this->assertTrue($rbrd || ria_mysql_num_rows($rbrd), 'Erreur lors de la vérification de la mise à jour des champs de la marque');
            $brd = ria_mysql_fetch_assoc($rbrd);
            
            $this->assertEquals( 'Nouveau nom', $brd['name'], 'Erreur: nom de la marque non mis à jour');
            
            $this->assertEquals( 'Nouveau titre', $brd['title'], 'Erreur: titre de la marque non mis à jour');
            
            $this->assertEquals( 'nouvelle url', $brd['url'], 'Erreur: url de la marque non mise à jour');

            
            $this->assertTrue(prd_brands_set_name(3, 'nom'), 'Erreur lors de la mise à jour du nom de la marque');

            $this->assertTrue(prd_brands_update_desc(3, 'desc'), 'Erreur lors de la mise à jour de la description de la marque');

            $this->assertTrue(prd_brands_update_products(3), 'Erreur lors de la mise à jour du nombre de produit de la marque');

            $rbrd = prd_brands_get(3);
			$this->assertTrue($rbrd || ria_mysql_num_rows($rbrd), 'Erreur lors de la vérification de la mise à jour des champs de la marque');
            $brd = ria_mysql_fetch_assoc($rbrd);

            $this->assertEquals( 'Nom', $brd['name'], 'Erreur: nom de la marque non mis à jour');

            $this->assertEquals( 'Desc', $brd['desc'], 'Erreur: description de la marque non mise à jour');

            $this->assertEquals( 1, $brd['products'], 'Erreur: nombre de produit de la marque non mis à jour');    
        }

        /** Fonction permettant de tester la mise à jour de la partie référencement d'une marque
         */
        public function testBrandsReferencingUpdate(){

            $this->assertTrue(prd_brands_update_referencing(3, 'titre référencement', 'desc référencement', 'mot clé'), 'Erreur lors de la mise à jour de la partie référencement d\'une marque');
            
            // Vérifie que les champs ont bien été mis à jour
            $rbrd = prd_brands_get(3);
			$this->assertTrue($rbrd || ria_mysql_num_rows($rbrd), 'Erreur lors de la vérification de la mise à jour des champs de référencement de la marque');
            $brd = ria_mysql_fetch_assoc($rbrd);
            
            $this->assertEquals( 'titre référencement', $brd['tag_title'], 'Erreur: titre de la marque utilisé pour le référencement non mis à jour');

            $this->assertEquals( 'desc référencement' , $brd['tag_desc'], 'Erreur: decription de la marque utilisé pour le référencement non mis à jour');

            $this->assertEquals( 'mot clé', $brd['keywords'], 'Erreur: mots clé de la marque non mis à jour');  
        }
	}
