<?php

require_once('dlv_store_plage.inc.php');
require_once('ria.mysql.inc.php');

/**
 * @backupGlobals disabled
 */
class plagesGetAvailablePlages extends PHPUnit_Framework_TestCase
{
	const DATE_FORMAT = 'Y-m-d H:i:s';

	const MONDAY = 1;
	const TUESDAY = 2;
	const WEDNESDAY = 3;
	const THURSDAY = 4;

	protected static $storeId;

	public static function setUpBeforeClass()
	{
		global $config;

		self::$storeId = dlv_stores_add('Riastudio', '', '', '103 Avenue de Paris', '', '79000', 'Niort', 'FRANCE', '', '0101010101', '', '<EMAIL>', '', true);

		ria_mysql_query("
			insert into dlv_store_plage (dsp_debut, dsp_fin, dsp_disponibilite, dsp_tnt_id, dsp_store_id, dsp_day)
			values
				('09:00:00', '09:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('09:30:00', '10:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('10:00:00', '10:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('10:30:00', '11:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('11:00:00', '11:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('11:30:00', '12:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('14:00:00', '14:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('14:30:00', '15:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('15:00:00', '15:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('15:30:00', '16:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('16:00:00', '16:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('16:30:00', '17:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('17:00:00', '17:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('17:30:00', '18:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::MONDAY."),
				('09:00:00', '09:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('09:30:00', '10:00:00', 2, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('10:00:00', '10:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('10:30:00', '11:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('11:00:00', '11:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('11:30:00', '12:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('14:00:00', '14:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('14:30:00', '15:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('15:00:00', '15:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('15:30:00', '16:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('16:00:00', '16:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('16:30:00', '17:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('17:00:00', '17:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('17:30:00', '18:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::TUESDAY."),
				('09:00:00', '09:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('09:30:00', '10:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('10:00:00', '10:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('10:30:00', '11:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('11:00:00', '11:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('11:30:00', '12:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('14:00:00', '14:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('14:30:00', '15:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('15:00:00', '15:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('15:30:00', '16:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('16:00:00', '16:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('16:30:00', '17:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('17:00:00', '17:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('17:30:00', '18:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::WEDNESDAY."),
				('09:00:00', '09:30:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::THURSDAY.")
				('09:30:00', '10:00:00', 1, ".$config['tnt_id'].", ".self::$storeId.", ".self::THURSDAY.")
		");
	}

	public static function tearDownAfterClass()
	{
		self::$storeId = null;
	}

	/**
	 * Test les contrôles de paramètres pour la fonction "dsp_plage_get_available_plages".
	 *
	 * @dataProvider invalidParameters
	 *
	 * @param  int    $storeId
	 * @param  string $date
	 * @return void
	 */
	public function testInvalidParametersWillReturnFalse($storeId, $date)
	{
		$this->assertFalse(
			dsp_plage_get_available_plages($storeId, $date)
		);
	}

	/**
	 * Test la fonction "dsp_plage_get_available_plages" quand aucune plage n'est disponible pour cette journée.
	 *
	 * @expected array(), car aucun "slot" n'est disponible et donc la fonction doit retourner un tableau vide
	 *
	 * @return void
	 */
	public function testNoAvailablePlages()
	{
		$this->assertSame(
			array(),
			dsp_plage_get_available_plages(self::$storeId, date(self::DATE_FORMAT, strtotime('next sunday')))
		);
	}

	/**
	 * Test la fonction "dsp_plage_get_available_plages" quand toutes les plages de la journée sont disponibles.
	 *
	 * @expected 14, car aucun "slot" n'est réservé
	 *
	 * @return void
	 */
	public function testAllPlagesAvailable()
	{
		$this->assertCount(
			14,
			dsp_plage_get_available_plages(self::$storeId, date(self::DATE_FORMAT, strtotime('next monday')))
		);
	}

	/**
	 * Test la fonction "dsp_plage_get_available_plages" quand une plage est réservée mais des "slots"
	 * sont toujours libres et la plage est donc disponible.
	 *
	 * @expected 14, car même si une commande réserve un "slot", il y en a un autre disponible
	 *
	 * @return void
	 */
	public function testPlageWithFreeSlotsAvailable()
	{
		global $config;

		$orderId = 1000;

		$plage = ria_mysql_fetch_assoc(
			ria_mysql_query('
				select dsp_id as id
				from dlv_store_plage
				where dsp_tnt_id = '.$config['tnt_id'].'
					and dsp_day = '.self::TUESDAY.'
					and dsp_store_id = '.self::$storeId.'
					and dsp_disponibilite = 2
			')
		);

		dsp_plage_book_plage($plage['id'], $orderId, date(self::DATE_FORMAT, strtotime('next tuesday')));

		$this->assertCount(
			14,
			dsp_plage_get_available_plages(self::$storeId, date(self::DATE_FORMAT, strtotime('next tuesday')))
		);
	}

	/**
	 * Test la fonction "dsp_plage_get_available_plages" quand une plage est réservée et aucun "slot" n'est libre.
	 *
	 * @expected 13, car un slot n'est plus disponible
	 *
	 * @return void
	 */
	public function testPlageWithoutFreeSlotsAvailable()
	{
		global $config;

		$orderId = 1001;

		$plage = ria_mysql_fetch_assoc(
			ria_mysql_query('
				select dsp_id as id
				from dlv_store_plage
				where dsp_tnt_id = '.$config['tnt_id'].'
					and dsp_store_id = '.self::$storeId.'
					and dsp_day = '.self::WEDNESDAY.'
				limit 1
			')
		);

		dsp_plage_book_plage($plage['id'], $orderId, date(self::DATE_FORMAT, strtotime('next wednesday')));

		$this->assertCount(
			13,
			dsp_plage_get_available_plages(self::$storeId, date(self::DATE_FORMAT, strtotime('next wednesday')))
		);
	}

	/**
	 * Test la fonction "dsp_plage_get_available_plages" quand on passe la commande en paramètre.
	 *
	 * @expected 1, car on inclut le "slot" réservé par la commande quand celle-ci est passé en paramètre
	 *
	 * @return void
	 */
	public function testA()
	{
		global $config;

		$orderId = 1002;

		$plage = ria_mysql_fetch_assoc(
			ria_mysql_query('
				select dsp_id as id
				from dlv_store_plage
				where dsp_tnt_id = '.$config['tnt_id'].'
					and dsp_store_id = '.self::$storeId.'
					and dsp_day = '.self::THURSDAY.'
				limit 1
			')
		);

		dsp_plage_book_plage($plage['id'], $orderId, date(self::DATE_FORMAT, strtotime('next thursday')));

		$this->assertCount(
			1,
			dsp_plage_get_available_plages(self::$storeId, date(self::DATE_FORMAT, strtotime('next thursday')), $orderId)
		);
	}

	/**
	 * Test la fonction "dsp_plage_get_available_plages" quand une commande a été crée il y a plus de 15 minutes mais qu'elle est confirmée.
	 *
	 * @expected 13, car la commande doit réservée ce "slot" qu'importe sa date de dernière modification
	 *
	 * @return void
	 */
	public function testPlageWithConfirmedOrder()
	{
		global $config;

		$orderId = 1003;

		$plage = ria_mysql_fetch_assoc(
			ria_mysql_query('
				select dsp_id as id
				from dlv_store_plage
				where dsp_tnt_id = '.$config['tnt_id'].'
					and dsp_store_id = '.self::$storeId.'
					and dsp_day = '.self::TUESDAY.'
					and dsp_disponibilite = 1
				limit 1
			')
		);

		dsp_plage_book_plage($plage['id'], $orderId, date(self::DATE_FORMAT, strtotime('next tuesday')));

		ria_mysql_query('
			update ord_orders_plage
			set oop_date_modified = '.date('Y-m-d', strtotime('last monday')).'
			where dsp_tnt_id = '.$config['tnt_id'].'
				and oop_order_id = '.$orderId
		);

		dsp_plage_confirm_booking($orderId);

		$this->assertCount(
			13,
			dsp_plage_get_available_plages(self::$storeId, date(self::DATE_FORMAT, strtotime('next tuesday')))
		);
	}

	/**
	 * Fournit des paramètres invalides pour tester la fonction "dsp_plage_get_available_plages".
	 * 		- 1ère ligne : Mauvais identifiant de magasin
	 * 		- 2ème ligne : Mauvais identifiant de magasin
	 * 		- 3ème ligne : Date non valide
	 * 		- 4ème ligne : Format de date non valide ("Y-m-d" au lieu de "d/m/Y")
	 *
	 * @return array
	 */
	public function invalidParameters()
	{
		return array(
			array('WRONG_STORE_ID', date('Y-m-d H:i:s')),
			array(0, date('Y-m-d H:i:s')),
			array(1, 'WRONG_DATE'),
			array(1, date('Y-m-d H:i:s')),
		);
	}
}