<?php

use Riashop\PriceWatching\models\LinearRaised\prw_states;

/**
 * @group statesTest
 * @backupGlobals disabled
 */
class StatesCRUDTest extends PHPUnit_Framework_TestCase {
	/**
	 * @dataProvider validOffersProvider
	 */
	public function testAddOffersStates($name) {
		$id = prw_states::add($name);
		$this->assertTrue(is_numeric($id) && $id > 0, "Erreur lors de l'ajout d'un statut");
	}

	/**
	 * @dataProvider invalidOffersProvider
	 */
	public function testFailAddOffersStates($name) {
		$this->setExpectedException('InvalidArgumentException');
		$id = prw_states::add($name);
	}

	public function testGetOffersStates() {
		$id = prw_states::add('Absent');
		$this->assertTrue(is_numeric($id) && $id > 0, "Erreur lors de l'ajout d'un statut");
		$result = prw_states::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat n'est pas une ressource mysql");
		$this->assertEquals(1, ria_mysql_num_rows($result), "Erreur le résultat devrais être 1");
		$one = ria_mysql_fetch_assoc($result);
		$this->assertArrayHasKey('id', $one);
		$this->assertArrayHasKey('name', $one);
		$this->assertArrayHasKey('date_created', $one);
		$this->assertArrayHasKey('date_modified', $one);

		$this->assertEquals('Absent', $one['name']);
	}

	public function testFailGetOffersStatesNoResult() {
		$result = prw_states::get(**********);
		$this->assertNotTrue(ria_mysql_control_ressource($result), "Erreur le résultat n'est pas une ressource mysql");
	}

	public function testFailGetOffersStatesInvalidId() {
		$this->setExpectedException('InvalidArgumentException');
		$result = prw_states::get(array('test' , null));
	}

	public function testGetAllOffersStates() {
		$result = prw_states::getAll();
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat n'est pas une ressource mysql");
		$this->assertEquals(4, ria_mysql_num_rows($result), "Erreur le résultat devrais être 4");
		$one = ria_mysql_fetch_assoc($result);
		$this->assertArrayHasKey('id', $one);
		$this->assertArrayHasKey('name', $one);
		$this->assertArrayHasKey('date_created', $one);
		$this->assertArrayHasKey('date_modified', $one);
	}

	public function testUpdateOfferStates() {
		$id = prw_states::add('Absent');
		$this->assertTrue(is_numeric($id) && $id > 0, "Erreur lors de l'ajout d'un statut");

		$result = prw_states::update($id, 'Rupture');
		$this->assertTrue($result);

		$result = prw_states::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat n'est pas une ressource mysql");
		$one = ria_mysql_fetch_assoc($result);
		$this->assertEquals('Rupture', $one['name']);
	}

	public function testFailUpdateOffersStatesinvalidName() {
		$id = prw_states::add('Absent');
		$this->assertTrue(is_numeric($id) && $id > 0, "Erreur lors de l'ajout d'un statut");
		$this->setExpectedException('InvalidArgumentException');
		$result = prw_states::update($id, 846524);
	}

	public function testFailUpdateOffersStatesInvalidId() {
		$this->setExpectedException('InvalidArgumentException');
		$result = prw_states::update('tests', 846524);
	}

	public function testDeleteOffersStates() {
		$id = prw_states::add('Absent');
		$this->assertTrue(is_numeric($id) && $id > 0, "Erreur lors de l'ajout d'un statut");
		$result = prw_states::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat n'est pas une ressource mysql");

		$result = prw_states::delete($id);
		$this->assertTrue($result, "Erreur de la suppression");
		$result = prw_states::get($id);
		$this->assertNotTrue(ria_mysql_control_ressource($result), "Erreur le résultat n'est pas une ressource mysql");
	}

	public function testFailDeleteOffersStates() {

		$this->setExpectedException('InvalidArgumentException');
		$result = prw_states::delete('esrsr');
	}

	public function validOffersProvider() {
		return array(
			array('Absent'),
			array('Rupture'),
			array('Promo'),
		);
	}

	public function invalidOffersProvider() {
		return array(
			array(5),
			array(array('test')),
			array(null),
		);
	}
}