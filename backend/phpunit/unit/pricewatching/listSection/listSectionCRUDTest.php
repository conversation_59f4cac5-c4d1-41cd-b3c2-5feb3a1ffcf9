<?php

use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_list_sections;

/**
 * @group listSectionTest
 * @backupGlobals disabled
 */
class listSectionCRUDTest extends PHPUnit_Framework_TestCase {
	/**
	 * @dataProvider validSectionProvider
	 */
	public function testAddSection($title) {
		$id = prw_followed_list_sections::add($title);
		$this->assertTrue(is_numeric($id) && $id > 0);
	}

	/**
	 * @dataProvider invalidSectionProvider
	 */
	public function testFailAddSection($title) {
		$this->setExpectedException('InvalidArgumentException');
		prw_followed_list_sections::add($title);
	}

	public function testGetSection() {
		$id = prw_followed_list_sections::add('TEST');
		$this->assertTrue(is_numeric($id) && $id > 0);

		$result = prw_followed_list_sections::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result));
		$this->assertEquals(1, ria_mysql_num_rows($result));
		$one = ria_mysql_fetch_assoc($result);
		$this->assertArrayHasKey('id', $one);
		$this->assertArrayHasKey('title', $one);
		$this->assertArrayHasKey('date_created', $one);
	}

	public function testGetAllSection() {
		$result = prw_followed_list_sections::get();
		$this->assertTrue(ria_mysql_control_ressource($result));
		$this->assertEquals(4, ria_mysql_num_rows($result));
	}

	public function testGetSomeSection() {
		$id = prw_followed_list_sections::add('TEST');
		$id2 = prw_followed_list_sections::add('TEST');
		$id3 = prw_followed_list_sections::add('TEST');
		$result = prw_followed_list_sections::get(array($id,$id2,$id3));
		$this->assertTrue(ria_mysql_control_ressource($result));
		$this->assertEquals(3, ria_mysql_num_rows($result));
	}

	public function testFailGetSection() {
		$this->setExpectedException('InvalidArgumentException');
		$result = prw_followed_list_sections::get('test');
	}

	public function testDeleteSection() {
		$id = prw_followed_list_sections::add('TEST');
		$this->assertTrue(is_numeric($id) && $id > 0);
		$result = prw_followed_list_sections::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result));
		$this->assertEquals(1, ria_mysql_num_rows($result));

		$result = prw_followed_list_sections::delete($id);
		$this->assertTrue($result);

		$result = prw_followed_list_sections::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result));
		$this->assertEquals(0, ria_mysql_num_rows($result));
	}

	public function testUpdateSection() {
		$id = prw_followed_list_sections::add('TEST');
		$this->assertTrue(is_numeric($id) && $id > 0);
		$result = prw_followed_list_sections::update($id, 'new');
		$this->assertTrue($result);
		$result = prw_followed_list_sections::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result));
		$this->assertEquals(1, ria_mysql_num_rows($result));
		$one = ria_mysql_fetch_assoc($result);
		$this->assertEquals('new', $one['title']);
	}

	public function testCountLinkedFollowedLists()
	{
		$id = prw_followed_list_sections::add('TEST');
		prw_followed_lists::add('Episserie', prw_followed_lists::TYPE_YUTO, false, $id);
		prw_followed_lists::add('Episserie', prw_followed_lists::TYPE_YUTO, false, $id);
		prw_followed_lists::add('Episserie', prw_followed_lists::TYPE_YUTO, false, 5);
		prw_followed_lists::add('Episserie', prw_followed_lists::TYPE_YUTO, false, $id);


		$count = prw_followed_list_sections::countLinkedFollowedLists($id);
		$this->assertEquals(3, $count);
	}

	function validSectionProvider(){
		return array(
			array('Rayon frais'),
			array('Boissons'),
			array('Légumes'),
		);
	}

	function invalidSectionProvider(){
		return array(
			array(false),
			array(null),
			array(array()),
			array(''),
		);
	}
}