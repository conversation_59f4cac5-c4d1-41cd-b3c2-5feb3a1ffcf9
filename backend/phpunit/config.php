<?php

set_include_path(__DIR__.'/../include');

global $ria_db_connect, $ria_db_selected, $memcached, $config;

require_once('debug.inc.php');
require_once('db.inc.php');
require_once('define.inc.php');
require_once('cfg.variables.inc.php');
require_once('strings.inc.php');

$memcached = new Memcached;

// Identifiant du locataire et du site boutique
$config['tnt_id'] = 7;
$config['wst_id'] = 12;

// Chargement des autres variables.
cfg_variables_load($config);

// Configuration des images produits.
cfg_images_load($config);

// Pages internes composant la vue du produit.
cfg_products_load($config);

$config['prd_reviews'] = true;
$config['prd_new_days'] = 1;

// Nettoie la base de donnée pour le tenant de test.
$current_DB = $ria_db_connect[_DB_RIASHOP];

exec('mysql -h '.$current_DB['server'].' -p'.$current_DB['password'].' -u '.$current_DB['user'].' < '.__DIR__.'/delete-data.sql');