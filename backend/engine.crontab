SHELL=/bin/bash
PATH=/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=root
HOME=/

CRONTABS_DIR=

#################
##### Cron ######
#################

# Traitement riashop pour les places de marché
20,50 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script confirm-orders-amazon --mode 0 2> /dev/null
23,53 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script import-orders-amazon --mode 0
26,56 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script update-priceandquantity-amazon --mode 0 2> /dev/null
29,59 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script workqueue-amazon --mode 0 2> /dev/null
59 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script workqueue-ebay --mode 0

# TODO voir pour lancer le script par base mysql plutôt que par tenant
20 5 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script clean-workqueue-marketplace --mode 0

# Traitement des mises à jour des tarifs pour les places de marché
29,59 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script update-priceandquantity-ebay --mode 0

# Traitement des commandes provenant des places de marché
*/15 6-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script beezup/import-orders --mode 1
25,55 6-23,0-1 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script import-orders-priceminister --mode 0
26,56 6-23,0-1 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script import-orders-cdiscount --mode 0
27,57 6-23,0-1 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script import-orders-rueducommerce --mode 0
28,58 6-23,0-1 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script import-orders-ebay --mode 0
29,59 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script import-orders-pourdebon --mode 0

# Confirmation des commandes provenant des places de marché
*/15 6-23 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script beezup/notify-bl --mode 1
45 8,13,18 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script confirm-orders-cdiscount --mode 0
46 8,13,18 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script confirm-orders-rueducommerce --mode 0
47 8,13,18 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script confirm-orders-priceminister --mode 0
48 8,13,18 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script confirm-orders-ebay --mode 0

# Traitement Workqueue rue du commerce
29,59 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-mirakl-offers --mode 0
19,49 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-pourdebon-offers --mode 0

# Envoi des alertes de produits à nouveau disponibles
14 6 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script gu-livr-alerts-notify --mode 1

# Gestion des campagnes Email
0 18 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-boost-order --mode 1

# Gestion de la synchronisation Sineres
47 8,13 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script sineres-import --mode 0

# Gestion de la synchronisation Salesforce
*/10 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script salesforce-import --mode 0
*/10 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script salesforce-export --mode 0

# Gestion de la synchronisation ios
*/15 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script devices-send-notifications --mode 0

# Gestion des imports
7 * * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script import-execution --mode 0

# controles des tablettes actives
4 9 * * 1-5 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script devices-check

# Import data HML
# SEULEMENT POUR TNT ID 168 
#30 1 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script "harmonia/import-prd" --mode 1
#40 1 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script "harmonia/import-users" --mode 1
#50 1 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script "harmonia/import-model_orders" --mode 1
#10 2 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script "harmonia/import-orders" --mode 1
#20 2 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script "harmonia/import-cod_list" --mode 1

# Export commandes HML
# SEULEMENT POUR TNT ID 168
#0 2 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script export-orders-harmonia --mode 1
#0 9 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script export-orders-harmonia --mode 1
#30 13 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script export-orders-harmonia --mode 1
#30 15 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script export-orders-harmonia --mode 1
#30 19 * * 7 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script export-orders-harmonia --mode 1

# Export rapports de visite HML
# SEULEMENT POUR TNT ID 168
#0 3 * * 1-6 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script export-reports-harmonia --mode 1

# Reconstruction du cache de mercuriale toutes les 30 minutes pour Benjamin
# SEULEMENT POUR TNT ID 135
#*/30 8-19 * * 1-5 infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script rebuild-restrictions-cache --mode 1

#################
## Cron hourly ##
#################

# Gestion des campagnes SMS
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-marketing-campaigns --mode 0

# Workqueue d'indexation
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/workqueue-search.php 0 30
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/workqueue-search.php 0 2 3 4 5 6 7 11 12 13 14 16 17 62

# Workqueue des imports de fichier
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script workqueue-imports --mode 0

# Mise à jour des caches de recherche
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/update-search-caches.php

# Gestion de priceminsiter
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script update-priceandquantity-priceminister --mode 0
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script workqueue-priceminister --mode 0

# #Gestion de CDiscount
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script update-priceandquantity-cdiscount --mode 0
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script workqueue-cdiscount --mode 0

# envoi des relances panier
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-cart-notify --mode 1

# envoi des relances panier aux représentants
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-cart-notify-seller --mode 1

# envoi des relances de paiement par YesByCash
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-yesbycash-notify --mode 1

# envoi des relances de paiement par chèque
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-cheque-notify --mode 1

# #envoi notification demandes d'avis consommateur sur les produits livres
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-product-review-notify --mode 1

# #envoi des commandes sur sineres
17 7-23 * * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script sineres-export --mode 0

# génère le cache des droits d'accès au catalogue
# SEULEMENT POUR TNT ID 40, 105
#17 7-23 * * * infra /usr/bin/php execute-script.php --script rebuild-restrictions-cache --mode 0

# Export du catalogue pour Facebook
17 7-23 * * * infra /usr/bin/php execute-script.php --script export-facebook-catalog --mode 0

# Export des articles sur BeezUP
17 7-23 * * * infra /usr/bin/php execute-script.php --script beezup/export-catalog --mode 1


#################
## Cron daily ###
#################

# Mise à jour du nombre de fois que les produits ont été commandé
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script ord-related-refresh --mode 0
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-sitemaps --mode 1
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script check-cat-hierarchy --mode 0

# Mise à jout des exports vers les comparateurs de prix
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script export-google-shopping --mode 1
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script export-prixan --mode 1
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script export-netaffiliation --mode 1
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script export-mastertag --mode 1

# Envoi du catalogue produit sur Mirakl - Rue du Commerce
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script create-mirakl-catalog --mode 0

25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script import-media-youtube --mode 1

# Mise à jour des meilleures ventes.
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-best-sellers --mode 0

# Mise à jour des statistiques sur les commandes
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-stats-orders --mode 0

# Archivage des commandes
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script ord-archiving --mode 0

# Envoi des alertes de relance des points de fidélité
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-alert-rewards --mode 1
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-birthday-promo --mode 1

# Mise à jour du nombre d'objets retournés par chaque segment
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script update-segments-count-objects --mode 0

# Mise à jour des inscriptions à la Newsletter sur MailJet
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-newsletters-mailjet --mode 0

# Lancement des tâches pour la veille tarifiaire
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script check-price-watching --mode 0 --other="cpt=Amazon"
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script check-price-watching --mode 0 --other="cpt=Client"

# Mise à jour de l'information "cat_products" des catégories utilisées dans les arborescenses présentes sur les sites
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-cat-products-count --mode 0

# Mise à jour de la position des catégories
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-prd-cat-pos --mode 1

# Mise à jour des entêtes de commandes
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-header-orders --mode 0

# Mise à jour du nombre de produits publiés dans une marque
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-products-publish-brand --mode 0

# génère le cache des droits d'accès au catalogue
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script rebuild-restrictions-cache --mode 0

# Retire le commercial rattaché à un prospect si aucun devis depuis X jours
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script reset_seller_when_no_devis --mode 0

# Ajoute le commercial commissionaire d'une commande pour les stats de commande directe et indirect
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-orders-seller-comisionned --mode 0

# Mise à jour des statistiques d'objectifs
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-goals-stats --mode 0

# Avis Vérifiés
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script avis-verifie-get-notice --mode 1
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script avis-verifie-send-orders --mode 1

# Yuto VEL
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script yuto_to_pay_subscription --mode 0
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script yuto-to-alert-subscribtion --mode 0
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script yuto-to-alert-help --mode 0
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script yuto-to-alert-endtry --mode 0

# Import de document depuis un FTP
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script import-documents-ftp --mode 1

# Création des prévisualisation de documents
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script rebuild-documents-preview --mode 1

# Alerte les clients ayant leur syncrhonisation de coupée
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-sync-alert --mode 0

# Dépublication des articles parents en fonction de ses articles enfants (actvier à la demande uniquement)
# Chadog / Naturanimo - Les articles enfants sont considérés comme étant dans l'arborescense principale du site
# SEULEMENT POUR TNT ID 171
#25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-products-publish-parent --mode 1

# Pre-calcul des stats pour les factures
# SEULEMENT POUR TNT ID 268, 1053, 118
#25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script refresh-stats-pre-caculated --mode 0

# Envoi quotidienne des article en rupture
25 2 * * *  infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-out-of-stock --mode 1


#################
# Cron monthly  #
#################

# Envoi des rapports sur la recherche des revendeurs
52 5 1 * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script send-stats-search-stores --mode 1

# Ménage sur les paniers de plus de 12 mois
52 5 1 * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script clean-carts-orders --mode 0

# Ménage sur les caches de recherche non utilisé depuis plus de 120 jours
52 5 1 * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script clean-search-caches --mode 0 --other="days=120"

# Mise à jours des zones de france (région, département, code postaux et commune)
52 5 1 * * infra /usr/bin/php $CRONTABS_DIR/update-zones-france.php

# Envoi du rapport de stats Yuto (spé GROUPAUTO)
# SEULEMENT POUR TNT ID 11, 61, 64, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 92, 116
#52 5 1 * * infra /usr/bin/php $CRONTABS_DIR/groupauto-export-stats.php

# Mise à jour des familles Amazon.fr
52 5 1 * * infra /usr/bin/php $CRONTABS_DIR/update-taxonomy-amazon.php

# Rapport VA Equipe Boplan
# SEULEMENT POUR TNT ID 352
#52 5 1 * * infra /usr/bin/php $CRONTABS_DIR/execute-script.php --script boplan/run-export --mode 0

# Attention ne rien ajouter en dernière ligne, celle ci n'est pas prise en compte par le démon vixie-cron.
# Toujours ajouter vos lignes actives au dessus de la présente notice
