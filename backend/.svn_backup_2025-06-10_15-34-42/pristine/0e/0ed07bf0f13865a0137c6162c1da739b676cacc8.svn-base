<?php

require_once('products.inc.php');
require_once('users.inc.php');
require_once('fields.inc.php');
require_once('strings.inc.php');
require_once('categories.inc.php');
require_once('orders.inc.php');
require_once('prd/colisage.inc.php');
require_once('brands.inc.php');

/** \defgroup model_prices Gestion de la tarification
 *	\ingroup pim_products
 *
 *	Ce module est chargé d'assurer la tarification des produits vendus sur la boutique.
 *	La gestion du calcul final d'un prix est externalisée dans une fonction utilisateur MySQL dont la documentation n'est pas présente ici
 *	Le module permet notamment :
 *		- De créer / supprimer / mettre à jour des tarifs HT ou des remises conditionnels :
 *			- Sur un produit
 *			- Sur une catégorie
 *			- Sur l'ensemble du catalogue ou des sous-groupes
 *		- De créer / supprimer / mettre à jour des taux de TVA conditionnels :
 *			- Sur un produit
 *			- Sur une catégorie
 *			- Sur l'ensemble du catalogue ou des sous-groupes
 *		- De créer / supprimer / modifier des groupes de conditions permettant l'exonération de TVA
 *		- De générer du code HTML mutualisé, propre à notre back-office
 *
 *	Les conditions tarifaires sont potentiellement extensibles à l'infinie, mais nécessiteront des ajustements au fur et à mesure de leur utilisation (bloc "switch" suivant le champ concerné)
 * @{
 */

/// Constante pour le type de remise "nouveau prix fixe"
define( 'NEW_PRICE',1 );
/// Constante pour le type de remise "remise en %"
define( 'DISCOUNT_PERCENT',2 );
/// Constante pour le type de remise "remise en valeur"
define( 'DISCOUNT_VALUE',3 );

// \cond onlyria
/// Le champ libre spécifié n'existe pas
define( 'FLD_NOT_EXISTS',-1 );
/// Le symbole n'est pas autorisé pour ce type de champ libre
define( 'INVALID_SYMBOL_FOR_FIELD',-2 );
/// Pour un comparateur "est compris entre", la valeur spécifié n'est pas composée de deux valeurs séparés par un point-virgule
define( 'WRONG_SIZE_OF_VALUE',-3 );
/// Le type du champ libre est numérique mais la valeur spécifié ne l'est pas
define( 'VALUE_NOT_NUMERIC',-4 );
/// La valeur n'est pas un entier
define( 'VALUE_NOT_INTEGER',-5 );
/// La valeur n'est pas un booléen oui / non
define( 'VALUE_NOT_BOOLEAN',-6 );
/// La valeur n'existe pas dans les valeurs de restriction
define( 'SELECT_VALUE_NOT_EXISTS',-7 );
/// La valeur n'est pas un identifiant de période, ou une date, ou deux dates séparés par un point-virgule
define( 'VALUE_NOT_DATE_OR_PERIOD',-8 );
/// Le symbole n'est pas applicable à la date ou à l'identifiant de date spécifié
define( 'WRONG_SYMBOL_FOR_DATE_VALUE',-9 );
/// Le champ libre n'est pas utilisable dans ce contexte de tarification
define( 'FLD_NOT_USABLE',-10 );

/// Identifiant à sauvegarder en tant que valeur de condition pour la comparaison d'une date avec le mois et le jour actuel ( peu importe l'année, ex : anniversaire )
define( 'DAY_AND_MONTH_IS_NOW',1 );
/// Identifiant à sauvegarder en tant que valeur de condition pour la comparaison d'une date avec la semaine actuelle ( peu importe l'année )
define( 'WEEK_IS_NOW',2 );
/// Identifiant à sauvegarder en tant que valeur de condition pour la comparaison d'une date avec le mois actuel ( peu importe l'année )
define( 'MONTH_IS_NOW',3 );
/// Identifiant d'une date personnalisé
define( 'CUSTOM_DATE',4 );
/// Identifiant d'une période personnalisé
define( 'CUSTOM_PERIOD',5 );

/// Les promotions sont calculées à partir du tarif de base
define( 'PMT_BASE_PRICE', 0 );
/// Les promotions sont calculées à partir du tarif de base dégressif sur la quantité
define( 'PMT_BASE_PRICE_QTE', 1 );
/// Les promotions sont calculées à partir du tarif spécifique
define( 'PMT_USR_PRICE', 2 );
/// Les promotions sont calculées à partir du tarif spécifique dégressif sur la quantité
define( 'PMT_USR_PRICE_QTE', 3 );
// \endcond

// \cond onlyria
/** Teste l'existence d'un symbôle d'opération
 *	@param string $symb symbôle à tester
 *	@param int $fld_type Type de données avec lequel le symbole est autorisé
 *	@return bool true si le symbole existe, false sinon
 */
function prc_symbols_exists( $symb, $fld_type=0 ){
	$symb = trim( $symb );

	if( $symb=='' ) return false;
	if( !is_numeric($fld_type) || $fld_type<0 ) return false;

	$sql = '
		select psy_symbol
		from prc_symbols
		where psy_symbol=\''.addslashes($symb).'\'
	';

	if( $fld_type>0 ){
		$sql .= ' and exists ( select 1 from prc_symbol_types where pst_symbol=psy_symbol and pst_type_id='.$fld_type.' )';
	}

	$res = ria_mysql_query( $sql );
	if( !$res ) return false;

	return ria_mysql_num_rows( $res )>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère la description en texte associée à un symbole de comparaison
 *	@param string $symb Symbôle dont on souhaite connaitre la description
 *	@return bool False en cas d'échec
 *	@return string Description textuelle du symbôle
 */
function prc_symbols_get_desc( $symb ){
	$sql = '
		select psy_desc from prc_symbols where psy_symbol="'.addslashes(trim($symb)).'"
	';
	$r = ria_mysql_query( $sql );
	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return _( ria_mysql_result( $r, 0, 0 ) );
}
// \endcond

// \cond onlyria
/** Recherche une liste de symbole selon dex critères facultatifs
 *	@param int $fld_type Identifiant d'un type de données (table fld_types) pour lequel le symbole est autorisé
 *	@param string $date_type Pour un type de champ libre date, Identifiant du type de comparaison de date (voir les constantes en haut du fichier)
 *	@param bool|array $exclude Optionnel, permet d'exclure des symbôles
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- symbol : Identifiant du symbole en chaîne de caractères
 *		- desc : Description du symbole
 */
function prc_symbols_get( $fld_type=0, $date_type=1, $exclude=false ){

	if( !is_numeric($fld_type) || $fld_type<0 ) return false;

	$sql = '
		select psy_symbol as symbol, psy_desc as "desc"
		from prc_symbols
		where 1
	';

	if( $fld_type>0 ){
		$sql .= ' and exists ( select 1 from prc_symbol_types where pst_symbol=psy_symbol and pst_type_id='.$fld_type.' )';
	}
	if( $fld_type==FLD_TYPE_DATE ){
		if( !is_numeric($date_type) || $date_type<1 || $date_type>5 ) return false;
		switch( $date_type ){
			case DAY_AND_MONTH_IS_NOW:
			case WEEK_IS_NOW:
			case MONTH_IS_NOW:
				$sql .= ' and psy_symbol in ( \'=\', \'!=\' )';
				break;
			case CUSTOM_DATE:
				$sql .= ' and psy_symbol in ( \'=\', \'!=\', \'<\', \'<=\', \'>\', \'>=\' )';
				break;
			case CUSTOM_PERIOD:
				$sql .= ' and psy_symbol in ( \'><\' )';
				break;
		}
	}
	if( is_array($exclude) && sizeof($exclude) ){
		$formated = array();
		foreach($exclude as $s) $formated[] = addslashes($s);
		$sql .= ' and psy_symbol not in ("'.implode('", "', $formated).'")';
	}

	$sql .= ' order by psy_pos';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Détermine si une condition est valide dans un contexte donné
 *	@param int $fld Identifiant du champ libre
 *	@param string $symbol Symbôle de comparaison
 *	@param string $value Valeur de comparaison
 *	@param bool $is_price Détermine s'il s'agit d'une condition tarifaire (true) ou d'une condition d'exonération (false)
 *	@return bool True si la condition est valide, code d'erreur numérique sinon
 */
function prc_conditions_is_valid( $fld, $symbol, $value, $is_price=true ){

	$rfield = fld_fields_get($fld);
	if( $rfield===false || !ria_mysql_num_rows($rfield) ){
		return FLD_NOT_EXISTS;
	}
	$field = ria_mysql_fetch_array( $rfield );

	if( $is_price ){
		if( !fld_fields_is_prices_apply($fld) ){
			return FLD_NOT_USABLE;
		}
	}else{
		if( !fld_fields_is_tvas_apply($fld) ){
			return FLD_NOT_USABLE;
		}
	}

	$value = trim($value);

	if( !prc_symbols_exists($symbol,$field['type_id']) ){
		return INVALID_SYMBOL_FOR_FIELD;
	}

	switch( $field['type_id'] ){

		case FLD_TYPE_REFERENCES_ID:
		case FLD_TYPE_INT:
		case FLD_TYPE_FLOAT:
			if( $symbol=='><' ){
				$vals = explode( ';',$value );
				if( sizeof($vals)<2 ) return WRONG_SIZE_OF_VALUE;
				$val1 = str_replace( array(' ',','),array('','.'),$vals[0] );
				$val2 = str_replace( array(' ',','),array('','.'),$vals[1] );
				if( !is_numeric($val1) ) return VALUE_NOT_NUMERIC;
				if( !is_numeric($val2) ) return VALUE_NOT_NUMERIC;
				if( $field['type_id']!=FLD_TYPE_FLOAT ){
					if( strpos( $val1,'.' ) ) return VALUE_NOT_INTEGER;
					if( strpos( $val2,'.' ) ) return VALUE_NOT_INTEGER;
				}
			}else{
				$value = str_replace( array(' ',','),array('','.'),$value );
				if( !is_numeric($value) ) return VALUE_NOT_NUMERIC;
				if( $field['type_id']!=FLD_TYPE_FLOAT ){
					if( strpos( $value,'.' ) ) return VALUE_NOT_INTEGER;
				}
			}
			break;
		case FLD_TYPE_SELECT_MULTIPLE:
		case FLD_TYPE_SELECT:
			if( !fld_restricted_values_get_id( $fld,$value ) ) return SELECT_VALUE_NOT_EXISTS;
			break;
		case FLD_TYPE_BOOLEAN_YES_NO:
			if( $value!='oui' && $value!='non' ) return VALUE_NOT_BOOLEAN;
			break;
		case FLD_TYPE_SELECT_HIERARCHY:
			$rows = fld_restricted_values_get( $value, $fld );
			if( !$rows || !ria_mysql_num_rows($rows) ) return SELECT_VALUE_NOT_EXISTS;
			break;
		case FLD_TYPE_DATE:
			if( is_numeric($value) ){
				if( $value<1 || $value>3 ) return VALUE_NOT_DATE_OR_PERIOD;
				if( $symbol!='=' && $symbol!='!=' ) return WRONG_SYMBOL_FOR_DATE_VALUE;
			}elseif( isdate($value) ){
				if( $symbol=='><' ) return WRONG_SYMBOL_FOR_DATE_VALUE;
			}else{
				$vals = explode( ';',$value );
				if( sizeof($vals)<2 ) return VALUE_NOT_DATE_OR_PERIOD;
				if( !isdate($vals[0]) || !isdate($vals[1]) ) return VALUE_NOT_DATE_OR_PERIOD;
				if( $symbol!='><' ) return WRONG_SYMBOL_FOR_DATE_VALUE;
			}
			break;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Détermine l'existence d'un type de tarif
 *	@param int $type Obligatoire, type de tarif
 *
 *	@return bool True si le type existe, False sinon
 */
function prc_types_exists( $type ){

	if( !is_numeric($type) || $type<=0 ) return false;

	$res = ria_mysql_query('
		select type_id
		from prc_types
		where type_id='.$type.'
	');

	if( !$res ) return false;

	return ria_mysql_num_rows( $res )>0;
}
// \endcond

// \cond onlyria
/** Retourne tout ou partie des types de tarifs et remises
 *	@param int $id Facultatif, Identifiant ou tableau d'identifiants de type(s)
 *	@param string $name_like Facultatif, Tout ou partie du nom du type
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant du type
 *		- name : Nom du type
 */
function prc_types_get( $id=0, $name_like=false ){

	if( is_array($id) ){
		if( !sizeof($id) ){
			$id = 0;
		}else{
			foreach( $id as $unique_id ){
				if( !is_numeric($unique_id) || $unique_id<0 ) return false;
			}
		}
	}elseif( !is_numeric($id) || $id<0 ){
		return false;
	}
	$name_like = !$name_like ? false : trim( $name_like );

	$sql = '
		select
			type_id as id,
			type_name as name
		from prc_types
		where 1
	';
	if( is_array($id) ){
		$sql .= ' and type_id in ('.implode( ',',$id ).')';
	}elseif( $id>0 ){
		$sql .= ' and type_id='.$id;
	}
	if( $name_like ){
		$sql .= ' and type_name like \'%'.addslashes($name_like).'%\'';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Crée un nouveau tarif.
 *	\remarks Les promotions ne sont pas cumulables
 *	@param int $type Identifiant du type de tarif. Se réferer à la table prc_types.
 *	@param $value Valeur du tarif. S'il s'agit d'un pourcentage, il ne peut être inférieur à 0 ni supérieur à 100.
 *	@param string $date_start Date de début d'application du tarif, mettre null pour une période indéterminée
 *	@param string $date_end Date de fin d'application du tarif, mettre null pour une période indéterminée
 *	@param $qte Facultatif, quantité minimale d'achat pour laquelle le tarif peut s'appliquer. Si le tarif n'est pas rattaché à un produit, cette quantité doit forcément être égale à  1 (valeur par défaut).
 *	@param int $prd Facultatif, identifiant d'article auquel est rattaché le tarif. Mettre 0 (par défaut) pour n'attacher à aucun article.
 *	@param int $cat Facultatif, identifiant de catégorie auquel est rattaché le tarif. Mettre 0 (par défaut) pour n'attacher à aucune catégorie.
 *	@param $is_cumuled Facultatif, détermine si le tarif peut se cumuler avec ceux de priorité supérieur. True par défaut
 *	@param bool $is_sync Facultatif, détermine si le tarif est crée depuis la synchronisation. La valeur par défaut est False.
 *	@param string $name Facultatif, permet de définir un nom sémantique sur la remise ou le prix (ex: "Remise pour les clients exports"). La valeur par défaut est une chaîne vide.
 *	@param $conditions Facultatif, conditions d'application sous la forme d'un tableau associatif champ libre => array( 'symbol','value' )
 *	@param $qte_max Facultatif, détermine la quantité maximale d'achat. False (par défaut) pour ne pas définir ce paramètre. Si le tarif n'est pas rattaché à un produit, ce paramètre est ignoré
 *	@param $is_promotion Facultatif, détermine s'il s'agit d'une promotion spécifique au site (ex-pmt_promotions).
 *	@param $group Facultatif, détermine le groupe auquel appartient la promotion. Si $is_promotion n'est pas spécifié, ce paramètre est ignoré.
 *	@param string $ref_gescom Facultatif, identifiant de la gestion commerciale
 *	@param string $kind Facultatif, identifiant du type de tarifs 1 normal, 2 taxes
 *	@param string $priority Facultatif, Priorité du tarifs, utilisé par certaine gescom
 *	@param int $dps_id Optionnel, par défaut à 0, identifiant du dépôt pour lequel le tarif s'appliquera
 *	@param string $currency Optionnel, par défaut "EUR", devise utilisé pour ce tarif
 *
 *	@return int|bool Identifiant du tarif créé, ou False en cas d'erreur.
 */
function prc_prices_add( $type, $value, $date_start, $date_end, $qte=1, $prd=0, $cat=0, $is_cumuled=true, $is_sync=false, $name='', $conditions=array(), $qte_max=false, $is_promotion=false, $group=0, $ref_gescom=null, $kind=null, $priority=null, $dps_id=0, $currency=null ){
	global $config;

	// formatages
	$value = str_replace( array(' ',','),array('','.'),$value );
	if( $prd!=0 ) $cat = 0;
	$name = trim( $name );
	$is_cumuled = $is_promotion ? 0 : ($is_cumuled ? 1 : 0);
	$is_sync = $is_sync ? 1 : 0;
	$date_start = isdate($date_start) || isdateheure($date_start)  ? $date_start : null;
	$date_end = isdate($date_end) || isdateheure($date_end) ? $date_end : null;

	// contrôles
	if( !prc_types_exists($type) ) {
		return false;
	}
	if( !is_numeric($value) ) {
		return false;
	}
	if( $type==DISCOUNT_PERCENT && $value>100 ) {
		return false;
	}
	if( !is_numeric($qte) || $qte<1 ) {
		return false;
	}
	if( $qte_max!=false && ( !is_numeric($qte_max) || $qte_max<$qte ) ) {
		return false;
	}
	if( $prd!=0 && !prd_products_exists($prd) ) {
		return false;
	}
	if( $cat!=0 && !prd_categories_exists($cat) ) {
		return false;
	}
	if( $prd==0 && $is_promotion ) {
		return false;
	}
	if( $date_start!=null && !isdateheure($date_start) ) {
		return false;
	}
	if( $date_end!=null && !isdateheure($date_end) ) {
		return false;
	}
	if( !is_array($conditions) ) {
		return false;
	}
	if( $ref_gescom!==null && prc_prices_get_by_ref_gescom($ref_gescom) ) {
		return false;
	}
	if( $priority!==null && !is_numeric($priority) ) {
		return false;
	}

	if( $kind!==null && !is_numeric($kind) ) {
		return false;
	}
	if( $kind===null ){
		$kind = PRC_KIND_DEFAULT;
	}

	// Avant de créer le tarifs, on vérifié que les conditions appliquées dessus (quand il y en a) sont des conditions valides
	if( count($conditions) ){
		foreach( $conditions as $cnd_fld=>$cnd_data ){
			if( prc_conditions_is_valid($cnd_fld, $cnd_data['symbol'], $cnd_data['value']) !== true ){
				return false;
			}
		}
	}

	if( $date_end!=null && $date_start!=null ){
		$delta = strtotime(dateheureparse($date_end)) - strtotime(dateheureparse($date_start));
		if ($delta<0) {
			return false;
		}
	}elseif( $is_promotion ){
		return false;
	}

	if( $dps_id != 0 && !prd_deposits_exists($dps_id) ){
		return false;
	}

	$fields = array( 'prc_tnt_id', 'prc_name', 'prc_is_cumuled', 'prc_is_sync', 'prc_type_id', 'prc_value', 'prc_qte_min' );
	$values = array( $config['tnt_id'], '\''.addslashes($name).'\'', $is_cumuled, $is_sync, $type, $value, $qte );

	$fields[] = 'prc_prd_id';
	$values[] = $prd;

	$fields[] = 'prc_cat_id';
	$values[] = $cat;

	$fields[] = 'prc_date_start';
	$values[] = $date_start === null ? '\'1000-01-01 00:00:00\'' : '\''.dateheureparse($date_start).'\'';

	$fields[] = 'prc_date_end';
	$values[] = $date_end === null ? '\'9999-12-31 23:59:59\'' : '\''.dateheureparse($date_end).'\'';

	// Par défaut le tarif est supprimé
	$fields[] = 'prc_is_deleted';
	$values[] = '1';

	$fields[] = 'prc_date_deleted';
	$values[] = 'now()';

	$fields[] = 'prc_kind';
	$values[] = $kind;

	if( $qte_max ){
		$fields[] = 'prc_qte_max';
		$values[] = $qte_max;
	}

	if( $is_promotion ){
		$fields[] = 'prc_is_promotion';
		$values[] = 1;
	}

	if( $is_promotion && is_numeric($group) && $group>0 ){
		$fields[] = 'prc_grp_id';
		$values[] = $group;
	}

	if( $ref_gescom!==null ){
		$fields[] = 'prc_ref_gescom';
		$values[] = '\''.addslashes($ref_gescom).'\'';
	}

	if( $priority!==null ){
		$fields[] = 'prc_priority';
		$values[] = $priority;
	}

	$fields[] = 'prc_dps_id';
	$values[] = $dps_id;

	if( $currency !== null ){
		$fields[] = 'prc_currency';
		$values[] = '"'.addslashes( $currency ).'"';
	}


	$res = ria_mysql_query('
		insert into prc_prices
			('.implode( ',',$fields ).')
		values
			('.implode( ',', $values ).')
	');
	if( !$res ){
		return false;
	}

	$id = ria_mysql_insert_id();

	$stop = false;
	foreach( $conditions as $fld=>$val ){
		if( !$stop ){
			if( !is_array($val) || !isset($val['symbol'],$val['value']) || !prc_price_conditions_add( $id,$fld,$val['value'],$val['symbol'] ) ){
				$stop = true;
			}
		}
	}

	if( $stop ) return false;

	if( ria_mysql_query( 'update prc_prices set prc_is_deleted=0, prc_date_deleted = null where prc_tnt_id='.$config['tnt_id'].' and prc_id='.$id ) ){
		if( $prd ){
			prd_products_set_date_modified( $prd );
		}

		return $id;
	}

	return false;
}
// \endcond

// \cond onlyria
/** Permet de déterminer si un tarif est cumulable avec les remises et tarifs de priorité supérieure
 *	@param int $id identifiant du tarif
 *	@param bool $is_cumuled Valeur booléenne indiquant si le cumul est possible
 *	@return bool true ou False suivant le succès ou l'échec de l'opération
 */
function prc_prices_set_is_cumuled( $id, $is_cumuled ){
	global $config;

	if( !prc_prices_exists($id) ) return false;
	$is_cumuled = $is_cumuled ? 1 : 0;

	$sql = '
		update prc_prices
		set prc_is_cumuled='.$is_cumuled.'
		where prc_id='.$id.' and prc_tnt_id='.$config['tnt_id'].'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Met à jour la description d'un tarif
 *	@param int $id Identifiant du tarif
 *	@param string $name Description du tarif
 *
 *	@return bool True en cas de succès, False sinon
 */
function prc_prices_set_name( $id, $name ){
	global $config;

	if( !prc_prices_exists($id) ) return false;
	$name = trim( $name );

	$sql = '
		update prc_prices
		set prc_name=\''.$name.'\'
		where prc_id='.$id.' and prc_tnt_id='.$config['tnt_id'].'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction supprime un ou plusieurs tarifs suivant les paramètres fournis.
 *	Le dernier paramètre seul n'est pas autorisé.
 *	@param int $id Optionnel, identifiant d'un tarif
 *	@param int $grp Optionnel, identifiant d'un groupe de promotion
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@param int $cat Optionnel, identifiant d'une catégorie
 *	@param null|string $date_modified Optionnel, permet de supprimer tous les tarifs n'ayant pas été mis à jour après cette date (inclus)
 *	@param bool $is_promotion Optionnel, filtre uniquement les promotions ou au contraire les non-promotions
 *  @param array $excluded_ids Optionnel, identifiants de tarifs à ne pas supprimer
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prc_prices_del( $id=0, $grp=0, $prd=0, $cat=0, $is_promotion=null, $excluded_ids=0, $date_modified=null ){

	if( !is_numeric($id) || $id < 0 ){
		return false;
	}
	if( !is_numeric($grp) || $grp < 0 ){
		return false;
	}
	if( !is_numeric($prd) || $prd < 0 ){
		return false;
	}
	if( !is_numeric($cat) || $cat < 0 ){
		return false;
	}

	if( $date_modified !== null ){
		$date_modified = dateheureparse( $date_modified );
		if( !isdateheure($date_modified) ){
			return false;
		}
	}

	if( !$id && !$grp && !$prd && !$cat && $date_modified === null ){
		error_log(__FILE__.':'.__LINE__.' Aucun paramètre n\'a été spécifié pour la suppression de tarifs.');
		return false;
	}

	$excluded_ids = control_array_integer($excluded_ids, false);
	if( $excluded_ids === false ){
		return false;
	}

	global $config;

	$sql = '
		update
			prc_prices
		set
			prc_is_deleted = 1, prc_date_deleted = now()
		where
			prc_tnt_id = '.$config['tnt_id'].'
			and prc_is_deleted = 0
	';

	if( $id > 0 ){
		$sql .= ' and prc_id = '.$id;
	}

	if( count($excluded_ids) ){
		$sql .= ' and prc_id not in ('.implode(',', $excluded_ids) . ')';
	}

	if( $grp > 0 ){
		$sql .= ' and prc_is_promotion = 1 and prc_grp_id = '.$grp;
	}
	if( $prd > 0 ){
		$sql .= ' and prc_prd_id = '.$prd;
	}
	if( $cat > 0 ){
		$sql .= ' and prc_cat_id = '.$cat;
	}
	if( $is_promotion !== null ){
		if( $is_promotion ){
			$sql .= ' and prc_is_promotion = 1';
		}else{
			$sql .= ' and prc_is_promotion = 0';
		}
	}
	if( $date_modified !== null ){
		$sql .= ' and prc_date_modified < "'.htmlspecialchars( $date_modified ).'"';
	}

	$r = ria_mysql_query($sql);

	if( $r && $prd > 0 ){
		prd_products_set_date_modified( $prd );
	}

	return $r;

}
// \endcond

// \cond onlyria
/** Met à jour la plupart des informations sur un tarif, à l'exception des conditions et du paramètre is-sync
 *	\remarks Un tarif n'est pas reconvertible en promotion site et vice-versa. Les promotions ne sont pas cumulables
 *	@param int $id Identifiant du tarif
 *	@param $type Identifiant du type de tarif. Se réferer à la table prc_types.
 *	@param $value Valeur du tarif. S'il s'agit d'un pourcentage, il ne peut être supérieur à 100.
 *	@param string $date_start Date de début d'application du tarif, mettre null pour une période indéterminée
 *	@param string $date_end Date de fin d'application du tarif, mettre null pour une période indéterminée
 *	@param $qte Facultatif, quantité minimale d'achat pour laquelle le tarif peut s'appliquer. Si le tarif n'est pas rattaché à un produit, cette quantité doit forcément être égale à  1 (valeur par défaut).
 *	@param int $prd Facultatif, identifiant d'article auquel est rattaché le tarif. Mettre 0 (par défaut) pour n'attacher à aucun article.
 *	@param int $cat Facultatif, identifiant de catégorie auquel est rattaché le tarif. Mettre 0 (par défaut) pour n'attacher à aucune catégorie.
 *	@param $is_cumuled Facultatif, Détermine si le tarif est cumulable avec les offres de priorité supérieure
 *	@param string $name Facultatif, permet de définir un nom sémantique sur la remise ou le prix (ex: "Remise pour les clients exports"). La valeur par défaut est une chaîne vide.
 *	@param $qte_max Facultatif, détermine la quantité maximale de commande. Est ignoré si égal à False ou si $prd n'est pas défini
 *	@param string $ref_gescom Facultatif, identifiant unique du tarifs sur la gestion commerciale
 *	@param string $kind Facultatif, identifiant du type de tarifs 1 normal, 2 taxes
 *	@param string $priority Facultatif, Priorité du tarifs, utilisé par certaine gescom
 *	@param int $dps_id Optionnel, par défaut à null - aucune action, identifiant du dépôt pour lequel le tarif s'appliquera
 *	@param string $currency Optionnel, par défaut à null - aucune action, devise utilisé pour ce tarif
 *
 *	@return bool True si succès, False sinon. Si le tarif est synchronisé, la fonction retournera False.
 */
function prc_prices_update( $id, $type, $value, $date_start, $date_end, $qte=1, $prd=0, $cat=0, $is_cumuled=true, $name='', $qte_max=false, $ref_gescom=null, $kind=null, $priority=null, $dps_id=null, $currency=null  ){
	global $config;

	if( !prc_prices_exists($id) ) return false;

	$is_promotion = prc_prices_get_is_promotion( $id );
	$qte_max = $qte_max=='∞' ? false : $qte_max;
	// formatages
	$value = str_replace( array(' ',','),array('','.'),$value );
	if( $prd!=0 ) $cat = 0;
	if( $prd==0 ) $qte_max = false;
	$name = trim( $name );
	$is_cumuled = $is_promotion ? 0 : ($is_cumuled ? 1 : 0);
	$date_start = isdate($date_start) || isdateheure($date_start)  ? $date_start : null;
	$date_end = isdate($date_end) || isdateheure($date_end) ? $date_end : null;

	// contrôles
	if( !prc_types_exists($type) ) return false;
	if( !is_numeric($value) ) return false;
	if( $type==DISCOUNT_PERCENT && $value>100 ) return false;
	if( !is_numeric($qte) || $qte<1 ) return false;
	if( $qte_max!=false && ( !is_numeric($qte_max) || $qte_max<$qte ) ) return false;
	if( $prd!=0 && !prd_products_exists($prd) ) return false;
	if( $cat!=0 && !prd_categories_exists($cat) ) return false;
	if( $prd==0 && $is_promotion ) return false;
	if( $date_start!=null && !isdateheure($date_start) ) return false;
	if( $date_end!=null && !isdateheure($date_end) ) return false;

	if( $date_end!=null && $date_start!=null ){

		$delta = strtotime(dateheureparse($date_end)) - strtotime(dateheureparse($date_start));
		if ($delta<0) return false;
	}elseif( $is_promotion ){
		return false;
	}

	// Si la gestion des baisses de tarifs est activée, alors on sauvegarde l'ancien tarif (duplication de tarifs en mode suppression)
	if( isset($config['prices_drop_actived']) && $config['prices_drop_actived'] ){
		if( !prc_prices_duplicate($id, true) ){
			return false;
		}
	}

	if( !empty($dps_id) && !prd_deposits_exists($dps_id) ){
		return false;
	}

	$sql = '
		update prc_prices
		set
			prc_name=\''.addslashes($name).'\',
			prc_is_cumuled='.$is_cumuled.',
			prc_type_id='.$type.',
			prc_value='.$value.',
			prc_qte_min='.$qte.',
			prc_qte_max='.( $qte_max == false ? '2147483647' : $qte_max ).',
			prc_prd_id='.$prd.',
			prc_cat_id='.$cat.',
			prc_date_start='.( $date_start === null ? '\'1000-01-01 00:00:00\'' : '\''.dateheureparse($date_start).'\'' ).',
			prc_date_end='.( $date_end === null ? '\'9999-12-31 23:59:59\'' : '\''.dateheureparse($date_end).'\'' ).'
			';

	if( $ref_gescom !=null ){
		$sql .= ' , prc_ref_gescom=\''.addslashes($ref_gescom).'\' ';
	}

	if( $kind==null && is_numeric($kind) ) {
		$sql .= ' , prc_kind='.$kind.' ';
	}

	if( $priority!==null && is_numeric($priority) ) {
		$sql .= ' , prc_priority='.$priority.' ';
	}

	if( $dps_id !== null ){
		$sql .= ' , prc_dps_id = '.$dps_id;
	}

	if( $currency !== null ){
		$sql .= ' , prc_currency = "'.addslashes( $currency ).'"';
	}

	$sql .= '
		where prc_id='.$id.' and
		prc_tnt_id='.$config['tnt_id'].'
	';

	if( !ria_mysql_query($sql) )
		return false;

	if( $prd ){
		prd_products_set_date_modified( $prd );
	}

	return $id;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de dupliquer un tarif.
 *	@param $prc_id Obligatoire, identifiant d'un tarif
 *	@param $delete Optionnel, si la copie est automatiquement supprimée ou non
 *	@return bool True si la copie s'est correctement déroulée, False dans le cas contraire
 */
function prc_prices_duplicate( $prc_id, $delete=false ){
	if( !is_numeric($prc_id) || $prc_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		insert into prc_prices
			( prc_tnt_id, prc_prd_id, prc_cat_id, prc_type_id, prc_date_start, prc_date_end, prc_is_deleted, prc_is_cumuled, prc_is_sync, prc_name, prc_value, prc_qte_min, prc_qte_max, prc_is_promotion, prc_grp_id, prc_date_deleted, prc_date_modified )
		select prc_tnt_id, prc_prd_id, prc_cat_id, prc_type_id, prc_date_start, prc_date_end, '.( $delete ? '1' : 'prc_is_deleted' ).', prc_is_cumuled, prc_is_sync, prc_name, prc_value, prc_qte_min, prc_qte_max, prc_is_promotion, prc_grp_id, '.( $delete ? 'now()' : 'prc_date_deleted' ).', now()
		from prc_prices
		where prc_tnt_id = '.$config['tnt_id'].'
			and prc_id = '.$prc_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	$new_prc_id = ria_mysql_insert_id();

	$sql = '
		insert into prc_price_conditions
			( ppc_tnt_id, ppc_fld_id, ppc_prc_id, ppc_value, ppc_symbol, ppc_date_modified )
		select ppc_tnt_id, ppc_fld_id, '.$new_prc_id.', ppc_value, ppc_symbol, now()
		from prc_price_conditions
		where ppc_tnt_id = '.$config['tnt_id'].'
			and ppc_prc_id = '.$prc_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		ria_mysql_query('delete from prc_prices where prc_tnt_id = '.$config['tnt_id'].' and prc_id = '.$new_prc_id);
		return false;
	}

	return $new_prc_id;
}
// \endcond

// \cond onlyria
/** Met à jour les dates de début et de fin d'activation d'un tarif
 *
 *	@param int $id Obligatoire, identifiant du tarif à mettre à jour
 *	@param string $date_start Facultatif, date de début d'application du tarif. Null, par défaut, pour une date indéterminée
 *	@param string $date_end Facultatif, date de fin d'application du tarif. Null, par défaut, pour une date indéterminée
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prc_prices_set_date_activation( $id, $date_start=null, $date_end=null ){
	global $config;

	if( $date_end!=null && !isdateheure($date_end) ) return false;
	if( $date_start!=null && !isdateheure($date_start) ) return false;
	if( !is_numeric($id) || $id<=0 ) return false;

	$is_promotion = prc_prices_get_is_promotion( $id );

	if( $date_end!=null && $date_start!=null ){
		$delta = strtotime(dateheureparse($date_end)) - strtotime(dateheureparse($date_start));
		if ($delta<0) return false;
	}elseif( $is_promotion ){
		return false;
	}

	$sql = '
		update prc_prices
			set prc_date_end='.( $date_end===null ? '\'9999-12-31 23:59:59\'' : '\''.dateheureparse($date_end).'\'' ).',
			prc_date_start='.( $date_start===null ? '\'1000-01-01 00:00:00\'' : '\''.dateheureparse($date_start).'\'' ).'
		where prc_tnt_id='.$config['tnt_id'].' and
			prc_id='.$id
	;

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour la valeur d'un tarif ou d'une remise
 *	Il n'est pas autorisé d'appliquer un tarif sur une catégorie ou sur le catalogue (remise uniquement)
 *	@param int $id Identifiant du tarif
 *	@param $value Nouvelle valeur
 *	@param $type Facultatif, type de la valeur. Se référer à la table prc_types. La valeur par défaut False ne change pas le type existant.
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prc_prices_set_value( $id, $value, $type=false ){
	global $config;

	$value = str_replace( array(' ',','),array('','.'),$value );

	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_numeric($value) ) return false;

	if( $type==false ){
		$rtype = ria_mysql_query( 'select prc_type_id from prc_prices where prc_id='.$id.' and prc_tnt_id='.$config['tnt_id'] );
		if( !$rtype || !ria_mysql_num_rows($rtype) ) return false;
		$type = ria_mysql_result( $rtype,0,0 );
	}

	if( !prc_types_exists($type) ) return false;

	if( $type==DISCOUNT_PERCENT ){
		if( $value>100 ) return false;
	}

	// Si la gestion des baisses de tarifs est activée, alors on sauvegarde l'ancien tarif (duplication de tarifs en mode suppression)
	if( isset($config['prices_drop_actived']) && $config['prices_drop_actived'] ){
		if( !prc_prices_duplicate($id, true) ){
			return false;
		}
	}

	$sql = '
		update prc_prices
		set prc_value='.$value.',
		prc_type_id='.$type.'
		where prc_id='.$id.' and
		prc_tnt_id='.$config['tnt_id'].'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Met à jour la quantité minimale d'achat pour laquelle un tarif est applicable
 *	Si le tarif fait référence au catalogue ou à une catégorie de produit, la valeur doit être forcément 1
 *	@param int $id Obligatoire, identifiant du tarif
 *	@param int $qte_min Obligatoire, quantité minimale
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prc_prices_set_qte_min( $id, $qte_min ){
	global $config;

	if( !is_numeric($qte_min) || $qte_min<1 ) return false;
	if( !prc_prices_exists($id) ) return false;

	/*$rprc = prc_prices_get($id);
	if( !$rprc || !ria_mysql_num_rows($rprc) ) return false;
	$prc = ria_mysql_fetch_array($rprc);*/

	//if( ( $prc['prd']==0 || $prc['prd']=='' ) && $qte_min!=1 ) return false;

	$sql = '
		update prc_prices
		set prc_qte_min='.$qte_min.'
		where prc_id='.$id.' and
		prc_tnt_id='.$config['tnt_id'].'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Met à jour la quantité maximale pour laquelle le tarif spécifié est appliqué
 *	Si le tarif ne pointe pas sur un article précis, False sera retourné
 *	@param int $id Obligatoire, Identifiant de l'article
 *	@param int $qte_max Obligatoire, Quantité maximale, False pour définir une valeur nulle
 *
 *	@return bool True en cas de succès, False sinon
 */
function prc_prices_set_qte_max( $id, $qte_max ){
	global $config;

	if( $qte_max!=false && ( !is_numeric($qte_max) || $qte_max<1 ) ) return false;

	$rprc = prc_prices_get($id);
	if( !$rprc || !ria_mysql_num_rows($rprc) ) return false;
	$prc = ria_mysql_fetch_array($rprc);

	if( ( $prc['prd']==0 || $prc['prd']=='' ) && $qte_max!=1 && $qte_max!=false ) return false;
	if( $qte_max!=false && $qte_max<$prc['qte-min'] ) return false;

	$sql = '
		update prc_prices
		set prc_qte_max='.( $qte_max==false ? '2147483647' : $qte_max ).'
		where prc_id='.$id.' and
		prc_tnt_id='.$config['tnt_id'].'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Détermine si un tarif existe dans la base de données et n'a pas été supprimé
 *	@param int $id Obligatoire, Identifiant du tarif
 *
 *	@return bool True si le tarif existe, False sinon
 */
function prc_prices_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select prc_id
		from prc_prices
		where prc_id='.$id.' and
		prc_tnt_id='.$config['tnt_id'].' and
		prc_is_deleted=0
	';

	$res = ria_mysql_query( $sql );
	if( !$res ) return false;

	return ria_mysql_num_rows($res)>0;
}

/** Détermine si un tarif existe dans la base de données en fonction de la réf gescom
 *	@param string $ref Obligatoire, référence gescom du tarifs
 *
 *	@return int|bool l'id du le tarif si il existe, False sinon
 */
function prc_prices_get_by_ref_gescom( $ref ){
	global $config;

	if( $ref==null || trim($ref)=='' ) return false;

	$sql = '
		select prc_id
		from prc_prices
		where prc_ref_gescom=\''.addslashes($ref).'\' and
		prc_tnt_id='.$config['tnt_id'].' and
		prc_is_deleted=0
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res, 0, 0 );
}
// \endcond

// \cond onlyria
/** Détermine si un tarif est en cours de validité à une date donnée
 *	@param int $id Obligatoire, identifiant du tarif
 *	@param $date Facultatif, date du test de validité. Null (par défaut) pour tester la date courante
 *
 *	@return bool True si le tarif est actif, False sinon
 */
function prc_prices_is_active( $id, $date=null ){
	global $config;

	if( $date!=null && !isdateheure($date) ) return false;
	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select prc_id
		from prc_prices
		where prc_id='.$id.' and
		prc_tnt_id='.$config['tnt_id'].' and
		prc_is_deleted=0 and (
			prc_date_start<='.( $date===null ? 'now()' : '\''.dateheureparse($date).'\'' ).'
		) and (
			prc_date_end>'.( $date===null ? 'now()' : '\''.dateheureparse($date).'\'' ).'
		)
	';

	$res = ria_mysql_query( $sql );
	if( !$res ) return false;

	return ria_mysql_num_rows($res)>0;
}
// \endcond

// \cond onlyria
/** Récupère un ou plusieurs prix selon des paramètres optionnels. Tous les paramètres sont facultatifs, la valeur par défaut de chaque paramètre retire le filtre.
 *	@warning Préciser conjointement le paramètre $prd et $cat peut provoquer des résultats inattendus
 *
 * 	Pour charger le prix applicable à une catégorie tarifaire, il faut utiliser le paramètre $conditions comme suit :
 * 	<code>
 *	<?php
 *		$conditions = array( 'fld' => _FLD_USR_PRC, 'symbol' => '=', 'value' => $prc['id'] )
 *	?>
 * 	</code>
 *
 *	@param int|array $id Facultatif, Identifiant du prix (ou tableau d'identifiants)
 *	@param int $type Facultatif, Identifiant de type de prix parmi les valeurs suivantes : 0 => Nouveau tarif, 1 => Remise en valeur, 2 => Remise en %
 *	@param float $value Facultatif, Prix
 *	@param string $date_start Facultatif, Date de début minimale. Null pour filtrer les prix sans date de début.
 *	@param string $date_end Date de fin maximale. Null pour filtrer les prix sans date de fin.
 *	@param int $prd Identifiant de produit auquel est rattaché le prix, ou tableau d'identifiants. Null pour filtrer les prix non rattachés à un produit.
 *	@param bool $prd_recursive Si True et prd précisé et non null, retourne également les prix liés à la hiérarchie supérieure (classement du ou des produits dans une ou des catégories, jusqu'à la racine)
 *	@param int $cat Identifiant de catégorie à laquelle est rattaché le prix, ou tableau d'identifiants. Null pour filtrer les prix non rattachés à une catégorie.
 *	@param bool $cat_recursive Si True et $cat précisé et non null, retourne également les prix de la hiérarchie inférieure à $cat (ou de chaque élément si $cat est un tableau)
 *	@param bool $is_cumuled Détermine si le prix est cumulable avec les offres supérieures (null par défaut, pas de filtre)
 *	@param bool $is_sync Détermine si ce prix provient de la synchronisation
 *	@param int $qte_min Quantité minimale d'achat
 *	@param int $current_activated N'est pas pris en compte si $date_start et $date_end sont précisés. Toute valeur non valide sera remplacée par 0. Détermine une période logique :
 *		- 0 : pas de filtres
 *		- 1 : uniquement les prix actuels
 *		- 2 : uniquement les prix passés
 *		- 3 : uniquement les prix futures
 *		- 4 : uniquement les prix passés ou actuels
 *		- 5 : uniquement les prix actuels ou futurs
 *	@param string $name Tout ou partie de la description du prix. Note : la valeur passé en paramètre n'est pas trimée. Peut être un tableau sous la forme array('symbol'=>'', value=>'')
 *	@param bool $have_conditions Détermine si le prix est conditionné par des champs libres ou non (ou via une quantité minimale > 1) "none" pour récupérer les tarif sans condition
 *	@param array $conditions Liste de critères conditionnant l'application du prix. Chaque élément du tableau doit avoir la structure suivante : array( 'fld'=>xxx, 'symbol'=>'xxx', 'value'=>'xxx' )[fld est obligatoire, symbol et value sont mutuellement obligatoires].
 *	@param array $exclude Tableau d'identifiants à exclure du résultat
 *	@param int $qte_max Quantité maximale pour laquelle le prix s'applique. Null (par défaut) pour ignoré ce paramètre
 *	@param bool $is_promotion Détermine si les prix retournés sont les promotions spécifiques au site. False par défaut (sauf si un identifiant est précisé).
 *	@param int $group Optionnel, identifiant d'un groupe de promotion
 *	@param string $order_date Optionnel, si 'asc' ou 'desc', trie le résultat selon la date d'activation (asc : plus ancienne à la plus récente, desc : l'inverse)
 *	@param float $min_price Optionnel, valeur minimale pour un prix
 *	@param float $max_price Optionnel, valeur maximale pour un prix
 *	@param bool $include_deleted Optionnel, par défaut les prix supprimés sont exclus du résultat (mettre true pour les inclure)
 *	@param int $prices_drop Optionnel, par défaut ce paramètre n'est pas pris en compte, mettre un nombre de jours pour récupérer les tarifs incluent dans les baisses de prix
 *	@param bool $period Optionnel, ce paramètre en cas de date_start et date_false filtrer par periode
 *	@param bool $is_between Optionnel, ce parametre en cas de date_start et date_false de vérifier si les date saisies sont inclu dans la promotion
 *	@param bool $qty_exactly Optionnel, par défaut à false, mettre true pour récupérer les prix donc la quantité min et / max correspondent à la valeur données en paramètre (et non plus à >= ou <=)
 *	@param bool $conditions_exactly Optionnel, par défaut à false, mettre true pour récupérer les tarifs donc les contitions  correspondent à la valeur donnéses en paramètre
 *	@param int $dps_id Optionnel, identifiant du dépôt pour lequel le tarif s'appliquera
 *	@param string $currency Optionnel, devise ou tableau de devises utilisées pour ce tarif
 *	@param array $sort Optionnel permet d'appliquer un tri personnalisé, valeurs acceptées : 'type', 'value'
 *
 *	@return bool False en cas d'erreur.
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : identifiant du prix
 *		- type : identifiant du type de prix
 *		- value : valeur du prix
 *		- date-start : date de début d'application du prix
 *		- date-end : date de fin d'application du prix
 *		- name : description du prix
 *		- qte-min : quantité minimale d'achat pour l'application du prix
 *		- qte-max : quantité maximale d'achat pour l'application du prix
 *		- is-cumuled : détermine si le prix est cumulé
 *		- is-sync : détermine si le prix est synchronisé
 *		- prd : identifiant du produit sur lequel le prix est applicable
 *		- cat : identifiant de la catégorie sur lequel le prix est applicable
 *		- date-start-fr : date de début au format jj/mm/aaaa hh:mm:ss
 *		- date-end-fr : date de fin au format jj/mm/aaaa hh:mm:ss
 *		- is-promotion : détermine s'il s'agit d'un prix promotionnel spécifique au site
 *		- grp_id : Identifiant du groupe de promotions auquel appartient le prix
 *		- date_modified : date de dernière modification
 *		- date_modified_en : date de dernière modification au format EN
 *		- date_deleted : date de dernière modification
 *		- date_deleted_en : date de dernière modification au format EN
 *		- kind : permet de diférencier les règles tarifaires sur les prix des taxes
 *		- priority : ordre de priorité pour les prix (utilisé par certaines gestions commerciales)
 *		- dps_id : identifiant du dépôt sur lequel le tarif est applicable
 *		- currency : devise du tarif
 */
function prc_prices_get(
	$id=0, $type=0, $value=false, $date_start=false, $date_end=false, $prd=false, $prd_recursive=false, $cat=false, $cat_recursive=false,
	$is_cumuled=null, $is_sync=null, $qte_min=1, $current_activated=0, $name=false, $have_conditions=null, $conditions=array(),
	$exclude=array(), $qte_max=null, $is_promotion=false, $group=0, $order_date=null, $min_price=0, $max_price=0, $include_deleted=false,
	$prices_drop=0, $period=false, $is_between=false, $qty_exactly=false, $conditions_exactly=false, $dps_id=null, $currency=null,
	$sort=[]
){
	global $config;

	// formatages
	$is_cumuled = $is_cumuled===null ? null : $is_cumuled ? 1 : 0;
	$is_sync = $is_sync===null ? null : $is_sync ? 1 : 0;
	if( $have_conditions !== 'none' ){
		$have_conditions = $have_conditions===null ? null : $have_conditions ? 1 : 0;
	}
	$value = $value==false ? false : str_replace( array( ' ',',' ),array( '','.' ),$value );
	$current_activated = !is_numeric($current_activated) || $current_activated<0 || $current_activated>8 ? 0 : $current_activated;

	// contrôles
	if( is_array($id) ){
		if( !sizeof($id) ){
			$id = 0;
		}else{
			foreach( $id as $single_id ){
				if( !is_numeric($single_id) || $single_id<=0 ){
					return false;
				}
			}
		}
	}
	elseif( !is_numeric($id) || $id<0 ) return false;

	if( $type!=0 && !prc_types_exists($type) ) return false;
	if( $value!=false && !is_numeric($value) ) return false;
	if( $date_start!=false && $date_start!=null && !isdateheure($date_start) ){ return false; }
	if( $date_end!=false && $date_end!=null && !isdateheure($date_end) ) return false;
	if( $prd!=false && $prd!=null ){
		if( !is_array($prd) ){
			if( !is_numeric($prd) || $prd<=0 ) return false;
		}else{
			if( !sizeof($prd) ){
				$prd = false;
			}else{
				foreach( $prd as $p ){
					if( !is_numeric($p) || $p<=0 ) return false;
				}
			}
		}
	}

	if( $cat!=false && $cat!=null ){
		if( !is_array($cat) ){
			if( !is_numeric($cat) || $cat<=0 ) return false;
		}else{
			if( !sizeof($cat) ){
				$cat = false;
			}else{
				foreach( $cat as $c ){
					if( !is_numeric($c) || $c<=0 ) return false;
				}
			}
		}
	}
	if( !is_numeric($qte_min) || $qte_min<1 ) return false;
	if( $qte_max!=null && ( !is_numeric($qte_max) || $qte_max<1 ) ) return false;
	if( !is_array($conditions) ) return false;
	if( !is_array($exclude) ) return false;

	if( $dps_id !== null ){
		if( !is_numeric($dps_id) || $dps_id < 0 ){
			return false;
		}
	}

	if( $currency !== null && !is_array($currency) ){
		$currency = [ $currency ];
	}

	// partie commune de la requête
	$sql = '
		select
			prc_id as id,
			prc_type_id as type,
			prc_value as value,
			prc_date_start as "date-start",
			prc_date_end as "date-end",
			date_format(prc_date_start,\'%d/%m/%Y\') as "date-start-fr",
			date_format(prc_date_end,\'%d/%m/%Y\') as "date-end-fr",
			time_format(prc_date_start,"%H:%i") as "hour-start",
			time_format(prc_date_end,"%H:%i") as "hour-stop",
			prc_name as name,
			prc_qte_min as "qte-min",
			prc_qte_max as "qte-max",
			prc_is_cumuled as "is-cumuled",
			prc_is_sync as "is-sync",
			prc_prd_id as prd,
			prc_cat_id as cat,
			prc_is_promotion as "is-promotion",
			prc_grp_id as grp_id,
			date_format(prc_date_modified, "%d/%m/%Y à %H:%i") as date_modified,
			prc_date_modified as date_modified_en,
			date_format(prc_date_deleted, "%d/%m/%Y à %H:%i") as date_deleted,
			prc_date_deleted as date_deleted_en,
			prc_kind as kind,
			prc_priority as priority,
			prc_dps_id as dps_id,
			prc_currency as currency
		from
			prc_prices
		where
			'.( $include_deleted ? '1' : 'prc_is_deleted=0' ).' and
			prc_tnt_id='.$config['tnt_id'].'
	';

	// filtres
	if( is_array($id) ){
		$sql .= ' and prc_id in ('.implode( ',',$id ).')';
	}elseif( $id>0 ){
		$sql .= ' and prc_id='.$id;
	}
	if( $type!=0 ){
		$sql .= ' and prc_type_id='.$type;
	}
	if( $value!=false ){
		$sql .= ' and prc_value='.$value;
	}

	if( $date_start!=false ){


		if($is_between){
			$sql .= ' and '.( $date_start===null ? '\'1000-01-01 00:00:00\'' : '\''.dateheureparse($date_start).'\'  between date(prc_date_start) and date(prc_date_end)'  );
		}else{
			$sql .= ' and prc_date_start '.($period ? ">=" : "=") .( $date_start===null ? '\'1000-01-01 00:00:00\'' : '\''.dateheureparse($date_start).'\'' );
		}
	}
	if( $date_end!=false ){

		if($is_between){
			$sql .= ' and '.( $date_end===null ? '\'9999-12-31 23:59:59\'' : '\''.dateheureparse($date_end).'\'  between date(prc_date_start) and date(prc_date_end)' );
		}else{
				$sql .= ' and prc_date_end'.($period ? "<=" : "=") .( $date_end===null ? '\'9999-12-31 23:59:59\'' : '\''.dateheureparse($date_end).'\'' );
		}

	}

	if( $current_activated!=0 && $date_end==false && $date_start==false ){
		switch( $current_activated ){
			case 1: // actuels
				$sql .= ' and ( prc_date_start<=now() ) and ( prc_date_end>now() )';
				break;
			case 2: // passés
				$sql .= ' and prc_date_end<now()';
				break;
			case 3: // futurs
				$sql .= ' and prc_date_start>now()';
				break;
			case 4: // passés ou actuels
				$sql .= ' and ((( prc_date_start<=now() ) and ( prc_date_end>now() )) or ( prc_date_end<now() ))';
				break;
			case 5: // actuels ou futurs
				$sql .= ' and ((( prc_date_start<=now() ) and ( prc_date_end>now() )) or ( prc_date_start>now() ))';
				break;
			case 6:	//	actuels ou futurs ou récemment terminés
				if(!isset($config['recent_interval'])){
					$config['recent_interval'] = 60;
				}
				$interval = intval($config['recent_interval']);
				$sql .= ' and (
					(
						( prc_date_start<=now()  and  prc_date_end>now() )
						or  prc_date_start>now()
					)
					or ( ( prc_date_end<=now() and prc_date_end is not null )
						and prc_date_end>DATE_SUB(now(), INTERVAL '.$interval.' day )
					)
				)';
				break;
			case 7:	// récemment terminés
				if(!isset($config['recent_interval'])){
					$config['recent_interval'] = 60;
				}
				$interval = intval($config['recent_interval']);
				$sql .= ' and (
					( prc_date_end<=now() and prc_date_end is not null )
					and prc_date_end>=DATE_SUB(now(), INTERVAL '.$interval.' day )
				)';
				break;
		}
	}
	if( $prd===null ){
		$sql .= ' and prc_prd_id=0';
	}elseif( $prd!=false ){
		$sub_prd_join = is_array($prd) ? ' in ('.implode( ',',$prd ).')' : '='.$prd;
		if( $prd_recursive ){
			$sql .= '
				and (
					prc_prd_id'.$sub_prd_join.' or (
						prc_prd_id=0 and (
							prc_cat_id=0 or prc_cat_id in (
								select cat_id from prd_categories
								where cat_date_deleted is null and cat_tnt_id=prc_tnt_id and cat_id in (
									select cly_cat_id from prd_classify
									where cly_tnt_id=prc_tnt_id and cly_prd_id'.$sub_prd_join.'
									union all
									select cat_parent_id from prd_cat_hierarchy, prd_classify
									where cat_tnt_id=prc_tnt_id and cly_tnt_id=prc_tnt_id and cly_prd_id'.$sub_prd_join.'
									and cly_cat_id=cat_child_id
								)
							)
						)
					)
				)
			';
		}else{
			$sql .= ' and prc_prd_id'.$sub_prd_join;
		}
	}
	if( $cat===null ){
		$sql .= ' and prc_cat_id=0';
	}elseif( $cat!=false ){
		$sub_cat_join = is_array($cat) ? ' in ('.implode( ',',$cat ).')' : '='.$cat;
		if( $cat_recursive ){
			$sql .= '
				and prc_cat_id'.$sub_cat_join.' or prc_cat_id in (
					select cat_id from prd_categories
					where cat_tnt_id=prc_tnt_id and cat_date_deleted is null and cat_id in (
						select cat_child_id
						from prd_cat_hierarchy
						where cat_tnt_id=prc_tnt_id and cat_parent_id'.$sub_cat_join.'
					)
				)
			';
		}else{
			$sql .= ' and prc_cat_id'.$sub_cat_join;
		}
	}
	if( $is_cumuled!=null ){
		$sql .= ' and prc_is_cumuled='.$is_cumuled;
	}
	if( $is_sync!=null ){
		$sql .= ' and prc_is_sync='.$is_sync;
	}

	if( !$qty_exactly ){
		$sql .= ' and prc_qte_min >= '.$qte_min;
	}else{
		$sql .= ' and prc_qte_min = '.$qte_min;
	}

	if( $name!==false ){
		if( is_array($name) ){
			if(isset($name['symbol'], $name['value']) && in_array($name['symbol'], array('=', '!=', 'like')) ){
				$sql .= ' and prc_name '.$name['symbol'].' \''.addslashes($name['value']).'\'';
			}
		}else{
			$sql .= ' and prc_name like \'%'.addslashes($name).'%\'';
		}
	}

	if( $have_conditions === 'none' ){
		$sql .= ' and not exists (
			select 1 from prc_price_conditions where ppc_prc_id=prc_id and ppc_tnt_id='.$config['tnt_id'].'
		) and prc_qte_min=1';
	}elseif( $have_conditions!=null ){
		$sql .= ' and'.($have_conditions ? '' : ' not').' exists (
			select 1 from prc_price_conditions where ppc_prc_id=prc_id and ppc_tnt_id='.$config['tnt_id'].'
		) and prc_qte_min'.($have_conditions ? '!' : '').'=1';
	}
	foreach( $conditions as $cnd ){
		if( !is_array($cnd) ) return false;
		$fld_present = false;
		foreach( $cnd as $k=>$v ){
			if( $k=='fld' ){
				$fld_present = true;
				if( !is_numeric($v) || $v<=0 ) return false;
			}
		}
		if( !$fld_present ) return false;

		$sql .= ' and exists (
			select 1 from prc_price_conditions where ppc_prc_id=prc_id and ppc_tnt_id='.$config['tnt_id'].' and ppc_fld_id='.$cnd['fld'].( isset( $cnd['symbol'],$cnd['value'] ) ? ' and ppc_value=\''.addslashes($cnd['value']).'\' and ppc_symbol=\''.addslashes($cnd['symbol']).'\'' : '' ).'
		)';
	}
	if( sizeof($exclude)>0 ){
		foreach( $exclude as $ex_id ){
			if( !is_numeric($ex_id) || $ex_id<=0 ) return false;
		}
		$sql .= ' and prc_id not in ('.implode( ',',$exclude ).')';
	}
	if( $qte_max!==null ){
		if( !$qty_exactly ){
			$sql .= ' and ( prc_qte_max<='.$qte_max.' )';
		}else{
			$sql .= ' and prc_qte_max = '.$qte_max;
		}
	}
	if( $id==0 ){
		$sql .= ' and prc_is_promotion='.( $is_promotion ? '1' : '0' );
	}
	if( $is_promotion && $group>0 ){
		$sql .= ' and prc_grp_id='.$group;
	}
	if( is_numeric($min_price) && $min_price > 0 ){
		$sql .= ' and prc_value >= '.$min_price;
	}
	if( is_numeric($max_price) && $max_price > 0 ){
		$sql .= ' and prc_value <= '.$max_price;
	}

	if( is_numeric($prices_drop) && $prices_drop > 0 ){
		$sql .= '
			and (
				(datediff(now(), prc_date_end) > 0 and datediff(now(), prc_date_end) < '.$prices_drop.')
				or
				(datediff(now(), prc_date_deleted) > 0 and datediff(now(), prc_date_deleted) < '.$prices_drop.')
			)
		';
	}

	if( $dps_id !== null && $dps_id >= 0 ){
		$sql .= ' and prc_dps_id = '.$dps_id;
	}

	if( is_array($currency) && count($currency) ){
		$sql .= ' and prc_currency in ("'.implode( '", "', $currency ).'")';
	}

	// paramètre de tri
	if( $order_date!==null ){
		$order_date = strtolower(trim($order_date));
		if( $order_date!=='asc' && $order_date!=='desc' ){
			$order_date = null;
		}
	}

	if( $conditions_exactly ){
		$sql .= '
			having (select count(*) from prc_price_conditions where ppc_prc_id=prc_id and ppc_tnt_id='.$config['tnt_id'].') = '.sizeof($conditions)
		;
	}


	if( is_array($sort) && count($sort) ){
		$sort_final = [];

		foreach( $sort as $col=>$dir ){
			if( !in_array($dir, ['asc', 'desc']) ){
				$dir = 'asc';
			}

			switch( $col ){
				case 'type':
					array_push( $sort_final, 'prc_type_id '.$dir );
					break;
				case 'value':
					array_push( $sort_final, 'prc_value '.$dir );
			}
		}

		if( count($sort_final) ){
			$sql .= ' order by '.implode( ', ', $sort_final );
		}
	}elseif( $order_date ){
		$sql .= '
			order by prc_date_start '.$order_date.', prc_date_end '.$order_date.', prc_date_deleted '.$order_date.'
		';
	}else{
		$sql .= '
			order by prc_type_id asc, prc_qte_min asc, prc_value desc, prc_date_start asc, prc_date_end asc, prc_date_deleted asc
		';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Récupération des prix minimaux par produit parent pour chaque condition de prix appliquées aux produits enfants
 * 	@return Une ressource MySQL et pour chaque ligne les informations suivantes :
 * 		- prd_parent_id : Identifiant du produit parent
 * 		- prd_id : Identifiant du produit enfant
 * 		- cat_id : Identifiant de la catégorie tarifaire
 * 		- id : Identifiant du tarif
 * 		- type : Identifiant du type de tarif
 * 		- date_start : Date de début de validité du prix
 * 		- date_end : Date de fin de validité du prix
 * 		- is_cumuled : Tarif cumulable ?
 * 		- is_sync : Tarif synchronisé ?
 * 		- name : Nom du tarif
 * 		- min_value : Tarif minimal parmis les tarifs des produits enfants pour la condition tarifaire
 * 		- qte_min : Quantité minimale de produits à partir de laquelle le tarif est appliqué
 * 		- qte_max : Quantité minimale de produits jusqu'à laquelle le tarif est appliqué
 * 		- is_promotion : Ce tarif est-il une promotion ?
 * 		- grp_id : Groupe de tarif
 * 		- date_modified : Date de dernière modification
 * 		- date_deleted : Date de suppression
 * 		- ppc_tnt_id : Tenant de la condition tarifaire
 * 		- ppc_fld_id : Champs avancé concerné par la condition tarifaire
 * 		- ppc_value : Valeur cible du champs avancé de la condition tarifaire
 * 		- ppc_symbol : Opérateur de la condition tarifaire
 * 		- ppc_date_modified : Date de dernière modification de la condition tarifaire
 */
function prc_prices_get_prd_parent_min_prices(){
	global $config;

	$sql = '
		select prd_parent_id, prc_prd_id as prd_id, prc_cat_id as cat_id, prc_id as id,
			prc_type_id as type, prc_date_start as date_start, prc_date_end as date_end, prc_is_cumuled as is_cumuled,
			prc_is_sync as is_sync, prc_name as name, min(prc_value) as min_value, prc_qte_min as qte_min, prc_qte_max as qte_max,
			prc_is_promotion as is_promotion, prc_grp_id as grp_id, prc_date_modified as date_modified,prc_date_deleted as date_deleted,
			ppc_tnt_id, ppc_fld_id, ppc_prc_id, ppc_value, ppc_symbol, ppc_date_modified
		from prc_prices
			inner join prd_hierarchy on (prd_tnt_id = '.$config['tnt_id'].' and prd_child_id = prc_prd_id)
			left join prc_price_conditions on (ppc_tnt_id = '.$config['tnt_id'].' and ppc_prc_id = prc_id)
		where prc_tnt_id = '.$config['tnt_id'].'
			and prc_is_deleted != 1
			and prc_date_deleted is null
			and (prc_type_id != 2 or prc_value != 0)
		group by prd_parent_id, ppc_fld_id, ppc_value, ppc_symbol, prc_type_id, prc_is_promotion
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction est una lias pour prc_prices_get ne retournant que les tarifs dont is_promotion est vrai
 *	@param int $prd Identifiant d'un article sur lequel filtrer le résultat
 *	@param $qte_min Facultatif, quantité maximale
 *	@param $current_activated Facultatif, identifiant de la période d'activation
 *	@param string $name Nom ou partie du nom de la promotion
 *	@param $qte_max Facultatif, quantité maximale
 *	@param $group Facultatif, deitnfiaitn d'un groupe de promotions
 *	@param int $prc Facultatif, identifiant de la promotion (si ce paramètre est précisé, tous les autres peuvent être à leur valeur par défaut)
 *	@param $order_date Facultatif, paramètre de tri, voir la fonction prc_prices_get()
 *  @param string $date_start Optionnel, permet de retourner les prix a partir de la date de commencement
 *  @param string $date_end Optionnel, permet de retourner les prix jusqu'à la date de fin
 *  @param $period Optionnel, determine si l'ont trie par periode comprise entre 2 dates
 *  @param $is_between Optionnel, ce parametre en cas de date_start et date_false de vérifier si les date saisies sont inclu dans la promotion
 *
 *	@see Voir prc_prices_get()
 */
function prc_prices_get_promotions( $prd=false, $qte_min=1, $current_activated=0, $name=false, $qte_max=null, $group=0, $prc=0, $order_date=null, $date_start=false, $date_end=false, $period=false, $is_between=false ){
	return prc_prices_get( $prc, 0, false, $date_start, $date_end, $prd, false, false, false, null, null, $qte_min, $current_activated, $name, null, array(), array(), $qte_max, true, $group, $order_date, 0, 0, false, 0, $period, $is_between);
}
// \endcond

// \cond onlyria
/** Détermine si un tarif est une promotion spécifique au site (ex-pmt_promotions)
 *	@param int $id Identifiant de la promotion
 *	@return bool true s'il s'agit d'une promotion, False sinon
 */
function prc_prices_get_is_promotion( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('select prc_is_promotion from prc_prices where prc_tnt_id='.$config['tnt_id'].' and prc_id='.$id);
	if( $res===false || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res, 0, 0 );
}
// \endcond

// \cond onlyria
/** Recherche des informations au choix sur un tarif
 *	@param int $id Obligatoire, identifiant de tarif
 *	@param $params Obligatoire, Tableau d'informations à retourner, les valeurs autorisées sont : date-start, date-end, date-start-fr, date-end-fr, type, value, is-sync, is-cumuled,  qte-min, prd, cat, name, qte-max, is-promotion.
 *	@return bool False en cas d'erreur
 *	@return array Un tableau associatif comprenant en clés les champs transmis et en valeur le contenu de la base pour le champ
 */
function prc_prices_get_params( $id, $params ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_array($params) || sizeof($params)==0 ) return false;

	$allowed_fields = array( 'date-start','date-end','date-start-fr','date-end-fr','type','value','is-sync','is-cumuled','qte-min','prd','cat','name','qte-max','is-promotion' );

	$sql = 'select ';
	$i = 0;
	foreach( $params as $p ){
		if( !in_array( $p,$allowed_fields ) ) return false;
		if( $i>0 ) $sql .= ', ';
		switch( $p ){
			case 'date-start': 			$sql .= 'prc_date_start as "'.$p.'" '; 	break;
			case 'date-end': 			$sql .= 'prc_date_end as "'.$p.'" '; 	break;
			case 'date-start-fr': 		$sql .= 'date_format(prc_date_start,\'%d/%M/%Y %H:%m:%s\') as "'.$p.'" '; 	break;
			case 'date-end-fr': 		$sql .= 'date_format(prc_date_end,\'%d/%M/%Y %H:%m:%s\') as "'.$p.'" '; 	break;
			case 'type': 				$sql .= 'prc_type as "'.$p.'" '; 		break;
			case 'value': 				$sql .= 'prc_value as "'.$p.'" '; 		break;
			case 'is-sync': 			$sql .= 'prc_is_sync as "'.$p.'" '; 	break;
			case 'is-cumuled': 			$sql .= 'prc_is_cumuled as "'.$p.'" '; 	break;
			case 'qte-min': 			$sql .= 'prc_qte_min as "'.$p.'" '; 	break;
			case 'prd': 				$sql .= 'prc_prd_id as "'.$p.'" '; 		break;
			case 'cat': 				$sql .= 'prc_cat_id as "'.$p.'" '; 		break;
			case 'name': 				$sql .= 'prc_name as "'.$p.'" '; 		break;
			case 'qte-max': 			$sql .= 'prc_qte_max as "'.$p.'" '; 	break;
			case 'is-promotion':		$sql .= 'prc_is_promotion as "'.$p.'" '; break;
		}
		$i++;
	}
	$sql .= '
		from prc_prices
		where prc_id='.$id.' and prc_is_deleted=0 and prc_tnt_id='.$config['tnt_id'].'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_fetch_array( $res );
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si un tarif s'applique dans un contexte donné. L'UDF "price_is_apply" est utilisée.
 *	@param $price_id Obligatoire, identifiant du tarif à tester.
 *	@param int $prd_id Optionnel, identifiant du produit.
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur.
 *	@param $col_id Optionnel, identifiant du conditionnement du produit.
 *	@param $prc_cat_id Optionnel, identifiant de catégorie tarifaire (si utilisateur non spécifié).
 *
 *	@return bool True si le tarif est applicable dans ce contexte, False sinon.
 */
function prc_prices_is_apply( $price_id, $prd_id=0, $usr_id=0, $col_id=0, $prc_cat_id=0 ){

	if( !is_numeric($price_id) || $price_id <= 0 ){
		return false;
	}
	if( !is_numeric($prd_id) || $prd_id < 0 ){
		return false;
	}
	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}
	if( !is_numeric($col_id) || $col_id < 0 ){
		return false;
	}
	if( !is_numeric($prc_cat_id) || $prc_cat_id < 0 ){
		return false;
	}

	$usr_holder = gu_users_get_prices_holder( $usr_id );

	// si client spécifié
	// ou catégorie tarifaire non forcée
	if( $usr_holder || !$prc_cat_id ){
		$prc_cat_id = gu_users_get_prc( $usr_holder, true );
	}
	$usr_ref = gu_users_get_ref( $usr_holder, true );

	$lng = wst_websites_languages_default();

	global $config;

	$sql = '
		select price_is_apply( '.$config['tnt_id'].', '.$prd_id.', '.$usr_holder.', '.$col_id.', '.$price_id.', '.$prc_cat_id.', "'.$lng.'", "'.addslashes($usr_ref).'", -1, NULL ) as "apply"
	';

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res, 0, 'apply');

}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un tarif.
 *	@param int $id Obligatoire, Identifiant du tarif.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prc_prices_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update prc_prices
		set prc_date_modified = now()
		where prc_tnt_id = '.$config['tnt_id'].' and prc_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Crée une condition d'application d'un tarif
 *	@param $prc_id Obligatoire, Identifiant du tarif
 *	@param int $fld_id Obligatoire, Identifiant du champ libre
 *	@param $value Obligatoire, Valeur de la condition
 *	@param $symbol Obligatoire, Détermine l'opérateur à appliquer sur la condition.
 *	@return bool true en cas de succès, False sinon
 */
function prc_price_conditions_add( $prc_id, $fld_id, $value, $symbol ){

	if( prc_price_conditions_exists( $prc_id, $fld_id ) ){
		return prc_price_conditions_upd( $prc_id, $fld_id, $value, $symbol );
	}

	// Ne pas utiliser prc_prices_get ou prc_prices_exists car le prix en question peut avoir une date de suppression temporaire
	if( !is_numeric($prc_id) || $prc_id <= 0 ){
		return false;
	}

	if( prc_conditions_is_valid( $fld_id, $symbol, $value ) !== true ){
		return false;
	}

	global $config;

	$sql = '
		insert into prc_price_conditions
			(ppc_tnt_id, ppc_fld_id, ppc_prc_id, ppc_value, ppc_symbol)
		values
			('.$config['tnt_id'].', '.$fld_id.', '.$prc_id.', "'.addslashes(trim($value)).'", "'.addslashes($symbol).'")
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		prc_prices_set_date_modified( $prc_id );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Supprime une condition sur un tarif
 *	@param $prc_id Obligatoire, Identifiant du tarif
 *	@param int $fld_id Optionnel, identifiant du champ libre, si cet argument n'est pas précisé, toutes les conditions du tarif seront supprimées
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prc_price_conditions_del( $prc_id, $fld_id=0 ){

	if( !is_numeric($prc_id) || $prc_id <= 0 ){
		return false;
	}
	if( !is_numeric($fld_id) || $fld_id < 0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from prc_price_conditions
		where ppc_prc_id = '.$prc_id.'
			and ppc_tnt_id = '.$config['tnt_id'].'
	';

	if( $fld_id ){
		$sql .= ' and ppc_fld_id = '.$fld_id;
	}

	$res = ria_mysql_query($sql);

	if( $res ){
		prc_prices_set_date_modified( $prc_id );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Met à jour la valeur et le symbole d'une condition sur un tarif
 *	@param $prc_id Obligatoire, Identifiant du tarif
 *	@param int $fld_id Obligatoire, Identifiant du champ libre
 *	@param $value Obligatoire, Nouvelle valeur
 *	@param $symbol Obligatoire, Nouveau symbôle
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prc_price_conditions_upd( $prc_id, $fld_id, $value, $symbol ){

	if( !is_numeric($prc_id) || $prc_id <= 0 ){
		return false;
	}
	if( !is_numeric($fld_id) || $fld_id <= 0 ){
		return false;
	}

	// Ne pas utiliser prc_prices_get ou prc_prices_exists car le prix en question peut avoir une date de suppression temporaire

	if( prc_conditions_is_valid( $fld_id, $symbol, $value ) !== true ){
		return false;
	}

	global $config;

	// Si la gestion des baisses de tarifs est activée, alors on sauvegarde l'ancien tarif (duplication de tarifs en mode suppression)
	if( isset($config['prices_drop_actived']) && $config['prices_drop_actived'] ){
		if( !prc_prices_duplicate($prc_id, true) ){
			return false;
		}
	}

	$sql = '
		update prc_price_conditions
		set
			ppc_value = "'.addslashes(trim($value)).'",
			ppc_symbol = "'.addslashes($symbol).'"
		where
			ppc_prc_id = '.$prc_id.'
			and ppc_fld_id = '.$fld_id.'
			and ppc_tnt_id = '.$config['tnt_id'].'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		prc_prices_set_date_modified( $prc_id );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Récupère les conditions d'un ou plusieurs tarifs
 *	@param int $prc Optionnel, identifiant d'un tarif, ou tableau d'identifiants de tarifs
 *	@param int $cls_id Optionnel, permet de ne filtrer que les conditions qui concernent des champs avancés d'une classe spécifique
 *	@param $fld Optionnel, identifiant d'un champ avancé, ou tableau d'identifiants
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- price : identifiant du tarif
 *		- fld : identifiant du champ avancé
 *		- fld-cls : identifiant de la classe du champ avancé
 *		- type-id : identifiant du type du champ avancé
 *		- value : valeur de la condition
 *		- symbol : symbole de la condition
 *		- date_modified : date de dernière modification
 *		- date_modified_en : date de dernière modification au format EN
 */
function prc_price_conditions_get( $prc=0, $cls_id=null, $fld=0 ){

	$prc = control_array_integer( $prc, false );
	if( $prc === false ){
		return false;
	}

	$fld = control_array_integer( $fld, false );
	if( $fld === false ){
		return false;
	}

	if( $cls_id !== null && !fld_classes_exists( $cls_id ) ){
		return false;
	}

	global $config;

	$sql = '
		select
			ppc_prc_id as price,
			fld_id as fld,
			fld_cls_id as "fld-cls",
			fld_type_id as "type-id",
			ppc_value as value,
			ppc_symbol as symbol,
			date_format(ppc_date_modified, "%d/%m/%Y à %H:%i") as date_modified,
			ppc_date_modified as date_modified_en
		from
			prc_price_conditions
			join fld_fields on ppc_fld_id = fld_id and  ppc_tnt_id = if(fld_tnt_id = 0, '.$config['tnt_id'].', fld_tnt_id)
		where
			ppc_tnt_id='.$config['tnt_id'].'
			and fld_date_deleted is null
			and exists (
				select 1 from prc_price_fields where ppf_tnt_id = '.$config['tnt_id'].' and (
						ppf_fld_id = ppc_fld_id
						or ppf_fld_id_1 = ppc_fld_id
						or ppf_fld_id_2 = ppc_fld_id
						or ppf_fld_id_3 = ppc_fld_id
					)
			)
	';

	if( sizeof($prc) ){
		$sql .= ' and ppc_prc_id in ('.implode(', ', $prc).')';
	}

	if( $cls_id !== null ){
		$sql .= ' and fld_cls_id = '.$cls_id;
	}

	if( sizeof($fld) ){
		$sql .= ' and ppc_fld_id in ('.implode(', ', $fld).')';
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Détermine l'existence d'une condition sur un tarif
 *	@param int $prc Obligatoire, Identifiant du tarif
 *	@param $fld Obligatoire, Identifiant du champ libre
 *
 *	@return bool True si la condition existe, False sinon
 */
function prc_price_conditions_exists( $prc, $fld ){
	global $config;

	if( !is_numeric($prc) || $prc<=0 ) return false;
	if( !is_numeric($fld) || $fld<=0 ) return false;

	$sql = '
		select ppc_value
		from prc_price_conditions
		where ppc_prc_id='.$prc.' and
		ppc_fld_id='.$fld.' and
		ppc_tnt_id='.$config['tnt_id'].'
	';

	$res = ria_mysql_query( $sql );
	if( $res===false ) return false;

	return ria_mysql_num_rows($res)>0;
}
// \endcond

// \cond onlyria
/** Crée un nouveau taux de TVA conditionnel
 *	@param float $rate Obligatoire, Taux de tva
 *	@param int $prd Facultatif, identifiant de produit sur lequel s'applique la TVA
 *	@param int $cat Non utilisé
 *	@param bool $is_sync Facultatif, Détermine si le taux de TVA provient de la synchronisation
 *	@param int $cac Facultatif, identifiant de la catégorie comptable concernée par le taux de TVA
 *	@param string $cnt_code Facultatif, code du pays pour lequel ce taux de TVA s'applique
 *
 *	@return int L'identifiant du taux de TVA, False en cas d'échec
 */
function prc_tvas_add( $rate, $prd=0, $cat=0, $is_sync=false, $cac=null, $cnt_code=null ){
	global $config;

	if( $cac!==null && !gu_accouting_categories_exists($cac) ) return false;
	if( !prd_products_exists($prd) ){
		return false;
	}
	if( !is_null($cnt_code) && !sys_countries_exists_code( $cnt_code ) ){
		return false;
	}

	$exist = prc_tvas_exists_prd( $prd, $cac, $cnt_code );
	if( $exist ){
		return prc_tvas_update( $prd, $rate, $cac, $cnt_code );
	}

	$rate = str_replace( array(' ',','),array('','.'),$rate );

	$values = array(
		$config['tnt_id'],
		$is_sync ? 1 : 0,
		$rate,
		$prd==0 ? 'NULL' : $prd,
		$cac===null ? 'NULL' : $cac,
		is_null($cnt_code) ? 'NULL' : '"'.$cnt_code.'"'
	);

	$sql = '
		insert into prc_tvas (
			ptv_tnt_id, ptv_is_sync, ptv_tva_rate, ptv_prd_id, ptv_cac_id, ptv_cnt_code
		) values (
			'.implode( ',', $values ).'
		)
	';

	$result = ria_mysql_query( $sql );
	if( !$result ) return false;

	$id = ria_mysql_insert_id();

	if( $prd ){
		prd_products_set_date_modified( $prd );
	}

	return $id;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le taux de tva d'un produit.
 * 	@param int $prd_id Obligatoire, identifiant d'un produit
 * 	@param float $tva_rate Obligatoire, taux de TVA
 * 	@param int $cac Optionnel, identifiant d'une catégorie tarifaire
 * 	@param string $cnt_code Optionnel, code d'un pays
 * 	@return bool true si la mise à jour s'est correctement déroulée, false dans le cas contraire
 */
function prc_tvas_update( $prd_id, $tva_rate, $cac_id=null, $cnt_code=null ){
	global $config;
	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	if( $cac_id !== null ){
		if( !is_numeric($cac_id) || $cac_id <= 0 ){
			return false;
		}
	}

	$tva_rate = (float) str_replace( [',', ' '], ['.', ''], $tva_rate );
	if( $tva_rate <= 0 ){
		return false;
	}

	$sql = '
		update prc_tvas
		set ptv_tva_rate = '.$tva_rate.'
		where ptv_tnt_id = '.$config['tnt_id'].'
			and ptv_prd_id = '.$prd_id.'
			and ptv_cac_id '.( $cac_id === null ? ' is null' : '= '. $cac_id );

	$cnt_code = trim($cnt_code);
	if( !is_null($cnt_code) && sys_countries_exists_code($cnt_code)){
		$sql .= ' and ptv_cnt_code="'.addslashes($cnt_code).'" ';
	}else{
		$sql .= ' and ptv_cnt_code is null';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Détermine si une TVA existe dans la base de données et n'a pas été supprimé
 *	@param int $id Obligatoire, Identifiant de TVA
 *	@return bool True si la TVA existe, False sinon
 */
function prc_tvas_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select ptv_id
		from prc_tvas
		where ptv_id='.$id.' and
		ptv_tnt_id='.$config['tnt_id'].' and
		ptv_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res ) return false;

	return ria_mysql_num_rows($res)>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si un taux de TVA existe pour un produit et une catégorie tarifaire donnés
 *	@param int $prd Obligatoire, Identifiant du produit
 *	@param $cac Obligatoire, Identifiant de la catégorie, ou NULL
 *	@param $cnt_code Facultatif, code du pays pour lequel cette TVA doit exister
 *	@return int L'identifiant de la ligne s'il en existe une, False sinon
 */
function prc_tvas_exists_prd( $prd, $cac, $cnt_code=null ){
	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}
	if( $cac !== null && ( !is_numeric($cac) || $cac < 0 ) ){
		return false;
	}

	global $config;

	$sql = '
		select ptv_id as id
		from prc_tvas
		where ptv_tnt_id = '.$config['tnt_id'].'
		and ptv_date_deleted is null
		and ptv_prd_id = '.$prd.'
		and ptv_cac_id'.( $cac === null ? ' is null' : ' = '.$cac ).'
	';

	if( !is_null($cnt_code) && sys_countries_exists_code($cnt_code)){
		$sql .= ' and ptv_cnt_code="'.$cnt_code.'" ';
	}else{
		$sql .= ' and ptv_cnt_code is null';
	}

	$r = ria_mysql_query($sql);

	if( !$r ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
		}
		return false;
	}

	if( !ria_mysql_num_rows($r) ){
		return false;
	}

	$r = ria_mysql_fetch_array($r);

	return $r['id'];
}
// \endcond

// \cond onlyria
/** Met à jour le taux d'une TVA conditionnelle
 *	@param int $id Obligatoire, Identifiant de TVA
 *	@param $rate Obligatoire, Taux de TVA
 *	@return bool True en cas de succès, False sinon
 */
function prc_tvas_set_rate( $id, $rate ){
	global $config;

	$rate = str_replace(array(' ',','), array('','.'), $rate);
	// if( !prd_tvas_exists($rate) ) return false;

	$tva = prc_tvas_get_params( $id, array('value', 'prd', 'cac') );
	if( !is_array($tva) || !sizeof($tva) ) return false;
	$tva['value'] = str_replace(array(' ',','), array('','.'), $tva['value']);

	if( $tva['value'] == $rate ) return true;

	$tva['cac'] = !is_numeric($tva['cac']) ? $config['default_cac_id'] : $tva['cac'];

	// rafraichissement des paniers contenant le produit
	// la catégorie comptable doit correspondre
	$sql_where = '
		where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$tva['prd'].'
		and prd_tva_rate = '.$tva['value'].' and prd_ord_id in (
			select ord_id from ord_orders
			left join gu_users as u1 on ord_usr_id = u1.usr_id and ( ord_tnt_id = u1.usr_tnt_id or 0 = u1.usr_tnt_id )
			left join gu_users as u2 on '.( $config['parent_is_order_holder'] ? 'ifnull(u1.usr_parent_id, u1.usr_id)' : 'u1.usr_id' ).' = u2.usr_id and ( ord_tnt_id = u2.usr_tnt_id or 0 = u2.usr_tnt_id )
			where ord_tnt_id = prd_tnt_id and ord_state_id in ('.implode(', ', ord_states_get_uncompleted()).')
			and u2.usr_date_deleted is null and ifnull(u2.usr_cac_id, '.$config['default_cac_id'].') = '.$tva['cac'].'
		)
	';

	$rget_ord = ria_mysql_query('
		select distinct prd_ord_id as "id"
		from ord_products
		'.$sql_where.'
	');

	$res = ria_mysql_query('
		update ord_products
		set prd_tva_rate = '.$rate.'
		'.$sql_where.'
	');

	if( $res && $rget_ord ){
		while( $ord_fetch = ria_mysql_fetch_assoc($rget_ord) ){
			ord_orders_set_date_modified( $ord_fetch['id'] );
		}
	}

	$sql = '
		update prc_tvas
		set ptv_tva_rate = '.$rate.'
		where ptv_tnt_id = '.$config['tnt_id'].'
		and ptv_id = '.$id.'
		and ptv_date_deleted is null
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la catégorie comptable pour laquelle un taux de TVA est applicable
 *	@param int $id Obligatoire, Identifiant du taux de TVA
 *	@param $cac_id Obligatoire, Identifiant de la catégorie comptable (NULL pour tout autoriser)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prc_tvas_set_accounting_category( $id, $cac_id ){
	if( !prc_tvas_exists( $id ) ) return false;
	if( $cac_id!==null && !gu_accouting_categories_exists($cac_id) ) return false;

	global $config;

	$sql = '
		update prc_tvas
		set ptv_cac_id='.( $cac_id===null ? 'NULL' : $cac_id ).'
		where ptv_id='.$id.' and ptv_tnt_id='.$config['tnt_id'].'
		and ptv_date_deleted is null
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction supprime une ou des lignes de TVA
 *	Les trois arguements ne peuvent être à leur valeur par défaut en même temps
 *	@param int $id Optionnel, identifiant de ligne
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@param $cac Optionnel, identifiant d'une catégorie comptable (attention, NULL différe de 0)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prc_tvas_del( $id=0, $prd=0, $cac=null ){
	global $config;

	if( !is_numeric($id) || $id<0 ) return false;
	if( !is_numeric($prd) || $prd<0 ) return false;
	if( $cac!==null && ( !is_numeric($cac) || $cac<0 ) ) return false;
	if( !$id && !$prd && $cac===null ) return false;

	$sql = '
		update prc_tvas
		set ptv_date_deleted=now()
		where ptv_tnt_id='.$config['tnt_id'].'
	';

	if( $id )
		$sql .= ' and ptv_id='.$id;
	if( $prd )
		$sql .= ' and ptv_prd_id='.$prd;
	if( $cac!==null )
		$sql .= ' and ptv_cac_id='.$cac;

	if( $prd ){
		prd_products_set_date_modified( $prd );
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Récupère une ou plusieurs TVA conditionnelles suivant des paramètres optionnels. Tous les paramètres sont optionnels et leur valeur par défaut
 * 	retire le filtre.
 *	Définir à la fois $prd et $cat peut provoquer des résultats inattendus
 *	@param int $id Identifiant de TVA (ou tableau)
 *	@param float $rate Taux de TVA
 *	@param int $prd Identifiant de produit auquel est rattaché la TVA (ou tableau, ou null pour les TVA explicitement non rattachées)
 *	@param int $cat Non utilisé
 *	@param bool $is_sync Détermine si la TVA est synchronisée
 *	@param array $exclude Tableau d'identifiants à exclure du résultat
 *	@param bool $prd_recursive Si True, retourne les TVA concernant la hiérarchie parente (catégories, jusqu'à la racine)
 *	@param bool $cat_recursive Si true, retourne les TVA concernant les catégories enfantes. Déprécié.
 *	@param int $cac_id Facultatif, identifiant de catégorie comptable sur lequel filtrer le résultat (attention à bien distinguer False, Null et 0)
 *	@param null|string|bool $cnt_code Facultatif, code du pays associé à la tva, default a null pour avoir le comportement standard,
 *		passer un code iso de pays pour filtrer les résultats, ou true pour avoir que ce avec des gestions de pays
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : identifiant de TVA
 *		- rate : taux de TVA
 *		- is-sync : détermine si la TVA est synchronisée
 *		- prd : identifiant du produit sur lequel la TVA est applicable
 *		- cat : Toujours NULL
 *		- cac : identifiant de la catégorie comptable sur laquelle la TVA est applicable
 *		- cnt_code : code du pays associé à la tva
 *		- date_modified : date de dernière modification
 *		- date_modified_en : date de dernière modification au format EN
 */
function prc_tvas_get(
	$id=0, $rate=false, $prd=false, $cat=false, $is_sync=null, $exclude=array(), $prd_recursive=false, $cat_recursive=false,
	$cac_id=false, $cnt_code=null
){
	global $config;

	// formatages
	$rate = str_replace( array(' ',','),array('','.'),$rate );
	$is_sync = $is_sync!=null ? ($is_sync ? 1 : 0) : $is_sync;

	// contrôles
	if( is_array($id) ){
		if( !sizeof($id) ) $id = 0;
		else{
			foreach( $id as $single_id ){
				if( !is_numeric($single_id) || $single_id<=0 ){
					return false;
				}
			}
		}
	}elseif( !is_numeric($id) || $id<0 ){
		return false;
	}

	if( $rate!=false && !prd_tvas_exists($rate) ){
		return false;
	}

	if( $prd!=false && $prd!=null ){
		if( !is_array($prd) ){
			if( !is_numeric($prd) || $prd<=0 ){
				return false;
			}
		}else{
			if( !sizeof($prd) ){
				$prd = false;
			}else{
				foreach( $prd as $p ){
					if( !is_numeric($p) || $p<=0 ){
						return false;
					}
				}
			}
		}
	}
	/*if( $cat!=false && $cat!=null ){
		if( !is_array($cat) ){
			if( !is_numeric($cat) || $cat<=0 ) return false;
		}else{
			if( !sizeof($cat) )
				$cat = false;
			else{
				foreach( $cat as $c ){
					if( !is_numeric($c) || $c<=0 ) return false;
				}
			}
		}
	}*/
	if( !is_array($exclude) ) return false;
	if( $cac_id!==false && $cac_id!==null && !gu_accouting_categories_exists($cac_id) ) return false;

	$sql = '
		select
			ptv_id as id,
			ptv_tva_rate as rate,
			ptv_is_sync as "is-sync",
			ptv_prd_id as prd,
			NULL as cat,
			ptv_cac_id as cac,
			ptv_cnt_code as cnt_code,
			date_format(ptv_date_modified, "%d/%m/%Y à %H:%i") as date_modified,
			ptv_date_modified as date_modified_en
		from
			prc_tvas
		where
			ptv_date_deleted is null and
			ptv_tnt_id='.$config['tnt_id'].'
	';

	// filtres
	if( is_array($id) ){
		$sql .= ' and ptv_id in ('.implode( ',',$id ).')';
	}elseif( $id>0 ){
		$sql .= ' and ptv_id='.$id;
	}
	if( $rate!=false ){
		$sql .= ' and ptv_tva_rate='.$rate;
	}
	if( $prd===null ){
		$sql .= ' and ptv_prd_id is null';
	}elseif( $prd!=false ){
		$sub_prd_join = is_array($prd) ? ' in ('.implode( ',',$prd ).')' : '='.$prd;
		if( $prd_recursive ){
			$sql .= '
				and ( ptv_prd_id'.$sub_prd_join.' or ptv_prd_id is null )
			';
		}else{
			$sql .= ' and ptv_prd_id'.$sub_prd_join;
		}
	}

	if( $is_sync!=null ){
		$sql .= ' and ptv_is_sync='.$is_sync;
	}
	if( sizeof($exclude)>0 ){
		foreach( $exclude as $ex_id ){
			if( !is_numeric($ex_id) || $ex_id<=0 ) return false;
		}
		$sql .= ' and ptv_id not in ('.implode( ',',$exclude ).')';
	}
	if( $cac_id!==false ){
		if( $cac_id===null ){
			$sql .= ' and ptv_cac_id is null';
		}else{
			$sql .= ' and ptv_cac_id='.$cac_id;
		}
	}

	if( !is_null($cnt_code) ){
		if(is_bool($cnt_code) && $cnt_code){
			$sql .= ' and ptv_cnt_code is not null ';
		}elseif( is_string($cnt_code) && strlen($cnt_code) == 2){
			$sql .= ' and ptv_cnt_code = "'.htmlspecialchars($cnt_code).'" ';
		}
	} else {
		$sql .= ' and ptv_cnt_code is null';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Recherche des informations au choix sur une TVA
 *	@param int $id Obligatoire, identifiant de TVA
 *	@param $params Obligatoire, Tableau d'informations à retourner, les valeurs autorisées sont : value, is-sync, prd, cat (toujours NULL, ne plus utiliser), cac, cnt_code.
 *	@return bool False en cas d'erreur
 *	@return array Un tableau associatif comprenant en clés les champs transmis et en valeur le contenu de la base pour le champ
 */
function prc_tvas_get_params( $id, $params ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_array($params) || sizeof($params)==0 ) return false;

	$allowed_fields = array( 'value', 'is-sync', 'prd', 'cat', 'cac', 'cnt_code' );

	$sql = 'select ';
	$i = 0;
	foreach( $params as $p ){
		if( !in_array( $p,$allowed_fields ) ) return false;
		if( $i>0 ) $sql .= ', ';
		switch( $p ){
			case 'value': 			$sql .= 'ptv_tva_rate as "'.$p.'" '; 	break;
			case 'is-sync': 		$sql .= 'ptv_is_sync as "'.$p.'" '; 	break;
			case 'prd': 			$sql .= 'ptv_prd_id as "'.$p.'" '; 		break;
			case 'cat': 			$sql .= 'NULL as "'.$p.'" '; 			break;
			case 'cac':				$sql .= 'ptv_cac_id as "'.$p.'" ';		break;
			case 'cnt_code':		$sql .= 'ptv_cnt_code as "'.$p.'" ';	break;
		}
		$i++;
	}
	$sql .= '
		from prc_tvas
		where ptv_id='.$id.' and ptv_date_deleted is null and ptv_tnt_id='.$config['tnt_id'].'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_fetch_array( $res );
}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un taux de TVA.
 *	@param int $id Obligatoire, Identifiant du taux.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prc_tvas_set_date_modified( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	return ria_mysql_query('
		update prc_tvas
		set ptv_date_modified = now()
		where ptv_tnt_id = '.$config['tnt_id'].'
			and ptv_id = '.$id.'
	');

}
// \endcond

// \cond onlyria
/** Crée un nouveau groupe d'exonération de TVA
 *	@param string $name Facultatif, nom du groupe
 *	@param $conditions Facultatif, tableau associatif de conditions. La clé est l'identifiant du champ libre, la valeur est un autre tableau de type ( 'symbol'=>symbole, 'value'=>valeur )
 *	@return int L'identifiant du groupe crée, ou False en cas d'échec
 */
function prc_tva_exempt_groups_add( $name=null, $conditions=array() ){
	global $config;

	// Contrôle les conditions
	if( !is_array($conditions) ) return false;
	foreach( $conditions as $fld=>$cnd ){
		if( !is_array($cnd) ) return false;
		$symbol = $value = null;
		foreach( $cnd as $k=>$v ){
			if( $k=='symbol' ) $symbol = $v;
			if( $k=='value' ) $value = $v;
		}
		if( $value===null || $symbol===null ) return false;
		if( !prc_conditions_is_valid( $fld,$symbol,$value,false ) ) return false;
	}

	$result = ria_mysql_query( 'insert into prc_tva_exempt_groups ( ptg_tnt_id,ptg_name ) values ( '.$config['tnt_id'].','.( $name===null ? 'NULL' : '\''.addslashes( trim($name) ).'\'' ).' )' );
	if( !$result ) return false;

	$id = ria_mysql_insert_id();

	// Crée les conditions
	foreach( $conditions as $fld=>$cnd ){
		if( !prc_tva_exempt_conditions_add( $id,$fld,$cnd['symbol'],$cnd['value'] ) ){
			$result = false;
			continue;
		}
	}

	// Supprime le groupe et retourne False
	if( $result===false ){
		prc_tva_exempt_groups_del($id);
		return false;
	}

	return $id;
}
// \endcond

// \cond onlyria
/** Met à jour le nom d'un groupe d'exonération de TVA
 *	@param int $id Obligatoire, Identifiant du groupe
 *	@param string $name Obligatoire, Nom du groupe. Utiliser NULL pour retirer le nom
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prc_tva_exempt_groups_set_name( $id, $name ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	return ria_mysql_query('
		update prc_tva_exempt_groups
		set
			ptg_name='.( $name===null ? 'NULL' : '\''.addslashes(trim($name)).'\'' ).'
		where
			ptg_id='.$id.' and
			ptg_date_deleted is null and
			ptg_tnt_id='.$config['tnt_id'].'
	');
}
// \endcond

// \cond onlyria
/** Supprime un groupe d'éxonération de TVA
 *	@param int $id Obligatoire, Identifiant du groupe à supprimer
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prc_tva_exempt_groups_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	return ria_mysql_query( 'update prc_tva_exempt_groups set ptg_date_deleted=now() where ptg_id='.$id.' and ptg_tnt_id='.$config['tnt_id'] );
}
// \endcond

// \cond onlyria
/** Récupère les informations disponibles sur un ou plusieurs groupes d'éxonération de TVA
 *	@param int $id Facultatif, identifiant du groupe (ou tableau)
 *	@param string $name Facultatif, nom du groupe. Note : la comparaison est effectuée avec l'opérateur LIKE
 *	@param $conditions Facultatif, conditions sur lesquels filtrer le résultat. Voir prc_tva_exempt_groups_add pour le format du paramètre
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant du groupe
 *		- name : Nom du groupe
 *		- cnd-count : Nombre de conditions pour ce groupe
 */
function prc_tva_exempt_groups_get( $id=0, $name='', $conditions=array() ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	if( !is_array($conditions) ){
		$conditions = array();
	}

	global $config;

	$sql = '
		select
			ptg_id as id,
			ptg_name as name, (
				select count(*) from prc_tva_exempt_conditions
				where ptc_ptg_id = ptg_id and ptc_tnt_id = '.$config['tnt_id'].'
			) as "cnd-count"
		from
			prc_tva_exempt_groups
		where
			ptg_tnt_id = '.$config['tnt_id'].' and
			ptg_date_deleted is null
	';

	if( sizeof($id) ){
		$sql .= ' and ptg_id in ('.implode(', ', $id).')';
	}

	if( trim($name) ){
		$sql .= ' and ptg_name like "%'.addslashes(trim($name)).'%"';
	}

	foreach( $conditions as $fld => $cnd ){
		if( !is_numeric($fld) || $fld <= 0 ){
			return false;
		}
		$sql .= '
			and exists (
				select 1 from prc_tva_exempt_conditions
				where ptc_ptg_id = ptg_id and ptc_tnt_id = '.$config['tnt_id'].' and ptc_fld_id = '.$fld.'
		';
		if( is_array($cnd) ){
			if( isset($cnd['symbol']) ){
				$sql .= ' and ptc_symbol = "'.addslashes($cnd['symbol']).'"';
			}
			if( isset($cnd['value']) ){
				$sql .= ' and ptc_value = "'.addslashes($cnd['value']).'"';
			}
		}
		$sql .= '
			)
		';
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un groupe d'exonération de TVA.
 *	@param int $id Obligatoire, Identifiant du groupe.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prc_tva_exempt_groups_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update prc_tva_exempt_groups
		set ptg_date_modified = now()
		where ptg_tnt_id = '.$config['tnt_id'].'
			and ptg_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Crée une condition d'exo. de TVA
 *	@param $group Obligatoire, Identifiant de groupe d'éxo. de TVA
 *	@param $fld Obligatoire, Identifiant du champ libre
 *	@param $val Obligatoire, Valeur de comparaison
 *	@param $symb Obligatoire, Symbole de comparaison
 *	@return bool True si succès, False sinon
 */
function prc_tva_exempt_conditions_add( $group, $fld, $val, $symb ){

	if( prc_tva_exempt_conditions_exists( $group, $fld ) ){
		return prc_tva_exempt_conditions_upd( $group, $fld, $val, $symb );
	}

	if( !is_numeric($group) || $group <= 0 ){
		return false;
	}

	if( !fld_fields_is_tvas_apply( $fld ) ){
		return false;
	}

	if( !prc_conditions_is_valid( $fld, $symb, $val, false ) ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		insert into prc_tva_exempt_conditions
			(ptc_tnt_id, ptc_ptg_id, ptc_fld_id, ptc_symbol, ptc_value)
		values
			('.$config['tnt_id'].', '.$group.', '.$fld.', "'.addslashes($symb).'", "'.addslashes(trim($val)).'")
	');

	if( $res ){
		prc_tva_exempt_groups_set_date_modified( $group );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Met à jour une condition d'éxo. TVA
 *	@param $group Obligatoire, Groupe d'éxo. de TVA
 *	@param $fld Obligatoire, identifiant de champ libre
 *	@param $val Obligatoire, Nouvelle valeur
 *	@param $symb Obligatoire, Nouveau symbole de comparaison
 *	@return bool true si succès, False sinon
 */
function prc_tva_exempt_conditions_upd( $group, $fld, $val, $symb ){

	if( !is_numeric($group) || $group <= 0 ){
		return false;
	}
	if( !is_numeric($fld) || $fld <= 0 ){
		return false;
	}

	if( prc_conditions_is_valid( $fld, $symb, $val, false ) !== true ){
		return false;
	}

	global $config;

	$sql = '
		update prc_tva_exempt_conditions
		set ptc_value = "'.addslashes(trim($val)).'",
			ptc_symbol = "'.addslashes($symb).'"
		where ptc_ptg_id = '.$group.'
			and ptc_fld_id = '.$fld.'
			and ptc_tnt_id = '.$config['tnt_id'].'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		prc_tva_exempt_groups_set_date_modified( $group );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Supprime une condition d'exo. de TVA
 *	@param $group Obligatoire, identifiant de groupe d'exo. TVA
 *	@param $fld Facultatif, identifiant de champ libre (ou tableau d'identifiants)
 *	@param $exclude_fields Facultatif. Si true, $fld représente le ou les champs libres à ne pas supprimer
 *	@return bool True si succès, False sinon
 */
function prc_tva_exempt_conditions_del( $group, $fld=0, $exclude_fields=false ){

	if( !is_numeric($group) || $group <= 0 ){
		return false;
	}

	$fld = control_array_integer( $fld, false );
	if( $fld === false ){
		return false;
	}

	global $config;

	$sql = '
		delete from prc_tva_exempt_conditions
		where ptc_ptg_id = '.$group.'
			and ptc_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($fld) ){
		$sql .= ' and ptc_fld_id '.( $exclude_fields ? 'not in' : 'in' ).' ('.implode(', ', $fld).')';
	}

	$res = ria_mysql_query($sql);

	if( $res ){
		prc_tva_exempt_groups_set_date_modified( $group );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Récupère les conditions attachées à groupe d'exonération de TVA
 *	@param $group Optionnel, identifiant du groupe d'exonération de TVA (ou tableau d'identifiants)
 *	@param $class Optionnel, identifiant de classe de champs libres
 *	@param $fld Optionnel, identifiant de champ libre (ou tableau d'identifiants)
 *
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- fld : Identifiant du champ libre
 *		- fld-cls : Identifiant de classe du champ libre
 *		- type-id : Type du champ libre
 *		- value : Valeur de la condition
 *		- symbol : Symbole de la condition
 *		- group : Identifiant de groupe de TVA
 *		- date_modified : date de dernière modification
 *		- date_modified_en : date de dernière modification au format EN
 */
function prc_tva_exempt_conditions_get( $group=0, $class=0, $fld=0 ){

	$group = control_array_integer( $group, false );
	if( $group === false ){
		return false;
	}

	$fld = control_array_integer( $fld, false );
	if( $fld === false ){
		return false;
	}

	if( !is_numeric($class) || $class < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select
			ptg_id as "group",
			fld_id as fld,
			fld_cls_id as "fld-cls",
			fld_type_id as "type-id",
			ptc_value as value,
			ptc_symbol as symbol,
			date_format(ptc_date_modified, "%d/%m/%Y à %H:%i") as date_modified,
			ptc_date_modified as date_modified_en
		from
			prc_tva_exempt_conditions join
			fld_fields on
				ptc_fld_id = fld_id and ( ptc_tnt_id = fld_tnt_id or fld_tnt_id = 0 )
			join prc_tva_exempt_groups on
				ptc_ptg_id = ptg_id and ptc_tnt_id = ptg_tnt_id
			join prc_tva_fields on
				( ( ptc_tnt_id=ptf_tnt_id and ptc_fld_id=ptf_fld_id ) or ( ptf_tnt_id=0 and ptc_fld_id=ptf_fld_id ) )
		where
			ptc_tnt_id = '.$config['tnt_id'].'
			and fld_date_deleted is null
			and ptg_date_deleted is null
	';

	if( sizeof($group) ){
		$sql .= ' and ptc_ptg_id in ('.implode(', ', $group).')';
	}

	if( $class ){
		$sql .= ' and fld_cls_id = '.$class;
	}

	if( sizeof($fld) ){
		$sql .= ' and ptc_fld_id in ('.implode(', ', $fld).')';
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Détermine l'existence d'une condition d'exonération de TVA
 *	@param $group Obligatoire, Identifiant du groupe d'éxonération de TVA
 *	@param $fld Obligatoire, Identifiant de champ libre
 *	@return bool True si la condition existe, False sinon
 */
function prc_tva_exempt_conditions_exists( $group, $fld ){
	global $config;

	if( !is_numeric($group) || $group<=0 ) return false;
	if( !is_numeric($fld) || $fld<=0 ) return false;

	$sql = '
		select ptc_value
		from prc_tva_exempt_conditions
		where ptc_ptg_id='.$group.' and
		ptc_fld_id='.$fld.' and
		ptc_tnt_id='.$config['tnt_id'].'
	';

	$res = ria_mysql_query( $sql );
	if( !$res ) return false;

	return ria_mysql_num_rows($res)>0;
}
// \endcond

/** Cette fonction récupère une promotion ( ligne de prc_prices dont l'attribut prc_is_promotion est vrai ) appliquée dans un contexte donné. Remplace les appels à pmt_promotions_get()
 *	@param int $prd Identifiant de l'article
 *	@param int $usr Facultatif, Identifiant de client ( -1 pour récupérer celui en cours )
 *	@param int $prc Facultatif, identifiant de la catégorie tarifaire à prendre en compte si le client n'est pas spécifié.
 *	@param $qte Facultatif, quantité commandée pour laquelle la remise peut s'appliquer
 *	@param $col Facultatif, identifiant d'un conditionnement pour lequel la remise peut s'appliquer
 *	@param $price_ar Facultatif, permet de spécifier les valeurs "price_ht" et "tva_rate" de base (tableau associatif).
 *	@param $include_remise Facultatif, permet d'inclure aux promotions les remises spéciales (REMISES et SOLDES)
 *	@param $remise_applicable Facultatif, permet de tenir compte que des remises applicables (ignoré si $include_remise est à false)
 *	@param $date Optionnel, permet de récupérer un tarif active à cette date là
 *	@param int $pmt_id_to_exclude Optionnel, identifiant d'une promotion à ignorer souvent la promotion en cours d'application
 *	@param int $dps Facultatif, identifiant d'un dépôt pour lequel la remise peut s'appliquer
 * 	@param bool $pmt_field Optionnel, permet de limiter les promotions en fonction de champ avancé
 *
 *	@return bool|array False en cas d'erreur sinon
 *					   un tableau associatif comprenant les colonnes suivantes :
 *							- id : Identifiant de la promotion
 *							- type : Si promotion sur produit 	: 1 => Nouveau tarif, 2 => Remise en pourcentage, 3 => Remise en valeur
 * 							- type : Si remise sur produit 		: 2 => Nouveau tarif, 1 => Remise en pourcentage, 0 => Remise en valeur
 *							- price_ht : Montant HT du produit, promotion incluse
 *							- price_ttc : Montant TTC du produit, promotion incluse
 *							- date-start : Date de début d'application de la promotion (format FR)
 *							- date-end : Date de fin d'application de la promotion (format FR)
 *							- datehour-start : Date et heure de début d'application de la promotion (format FR)
 *							- datehour-end : Date et heure de fin d'application de la promotion (format FR)
 *							- date-start-en : Date de début d'application de la promotion (format EN)
 *							- date-end-en : Date de fin d'application de la promotion (format EN)
 *							- datehour-start-en : Date et heure de début d'application de la promotion (format EN)
 *							- datehour-end-en : Date et heure de fin d'application de la promotion (format EN)
 *							- grp_id : Identifiant du groupe de promotion
 *							- value: Valeur de réduction, en pourcentage ou en valeur
 */
function prc_promotions_get( $prd, $usr=0, $prc=0, $qte=1, $col=0, $price_ar=null, $include_remise=false, $remise_applicable=false, $date=false, $pmt_id_to_exclude=null, $dps=0, $batch=[], $pmt_field=false ){
	global $config, $memcached;

	$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':prc_promotions_get:'.$prd.':'.$usr.':'.$prd.':'.$qte.':'.$col;
	$key_memcached .= ( is_array($price_ar) ? md5(json_encode($price_ar)) : ':null' );
	$key_memcached .= ':'.( $include_remise ? 'include_remise' : 'none' );
	$key_memcached .= ':'.( $remise_applicable ? 'remise_applicable' : 'none' );
	$key_memcached .= ':'.$date;

	if( !isset($_GET['force_cache']) && ($get = $memcached->get($key_memcached)) ){
		return ($get === 'none' ? false : $get);
	}

	// Contrôle des paramètres
	if( !is_numeric($prd) || $prd <= 0  ){
		return false;
	}
	if( !is_numeric($usr) || $usr < -1 ){
		return false;
	}
	if( !is_numeric($prc) || $prc < 0 ){
		return false;
	}
	if( !is_numeric($qte) || $qte < 0 ){
		return false;
	}
	if( !is_numeric($col) || $col < 0 ){
		return false;
	}
	if( !is_numeric($dps) || $dps < 0 ){
		return false;
	}

	if( $price_ar !== null ){
		if( !is_array($price_ar) ){
			return false;
		}elseif( !isset($price_ar['price_ht'], $price_ar['tva_rate']) ){
			return false;
		}else{
			$price_ar['price_ht'] = str_replace(array(',', ' '), array('.', ''), $price_ar['price_ht']);
			$price_ar['tva_rate'] = str_replace(array(',', ' '), array('.', ''), $price_ar['tva_rate']);
			if( !is_numeric($price_ar['price_ht']) || !is_numeric($price_ar['tva_rate']) ){
				return false;
			}
		}
	}

	if( $usr == -1 ){
		$usr = isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0;
		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) ){
			$usr = $_SESSION['admin_view_user'];
		}
	}

	if( $date !== false && !isdate($date) ){
		return false;
	}

	$usr_holder = gu_users_get_prices_holder( (isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id'] > 0 ? $usr : 0) );

	// si client spécifié
	// ou catégorie tarifaire non forcée
	if( $usr_holder || !$prc ){
		$prc = gu_users_get_prc( $usr_holder, true );
	}
	$usr_ref = gu_users_get_ref( $usr_holder, true );

	if( $price_ar === null ){
		// récupère le tarif de base suivant le type de calcul
		$price_info = false;
		switch( $config['calc_promotions_type'] ){
			case PMT_BASE_PRICE_QTE:
				$price_info = prd_products_get_price_array( $prd, 0, 0, 0, $qte, $col );
				break;
			case PMT_USR_PRICE:
				$price_info = prd_products_get_price_array( $prd, $usr, $prc, 0, 1, $col );
				break;
			case PMT_USR_PRICE_QTE:
				$price_info = prd_products_get_price_array( $prd, $usr, $prc, 0, $qte, $col );
				break;
			// case PMT_BASE_PRICE:
			default:
				$price_info = prd_products_get_price_array( $prd, 0, 0, 0, 1, $col );
				break;
		}

		if( !ria_array_key_exists(['price_ht', 'tva_rate', 'price_ttc'], $price_info) ){
			$memcached->set( $key_memcached, 'none', 60 );
			return false;
		}
	}else{
		$price_info = $price_ar;
	}

	$sql = '
		select
			prc_id as id, prc_type_id as type, prc_value as value, prc_grp_id as grp_id,
			date_format(prc_date_start, "%d/%m/%Y") as "date-start",
			date_format(prc_date_end, "%d/%m/%Y") as "date-end",
			date_format(prc_date_start, "%d/%m/%Y %H:%i") as "datehour-start",
			date_format(prc_date_end, "%d/%m/%Y %H:%i") as "datehour-end",
			date_format(prc_date_start, "%Y-%m-%d") as "date-start-en",
			date_format(prc_date_end, "%Y-%m-%d") as "date-end-en",
			date_format(prc_date_start, "%Y-%m-%d %H:%i") as "datehour-start-en",
			date_format(prc_date_end, "%Y-%m-%d %H:%i") as "datehour-end-en"
		from
			prc_prices
		where
			prc_tnt_id = '.$config['tnt_id'].'
	';

	// Si une date est donnée en paramètre, on ne tient pas compte de la suppression des tarifs
	if( $date === false ){
		$sql .= '
			and prc_is_deleted = 0
			and prc_date_end > now() and prc_date_start <= now()
		';
	}

	$sql .= '
			and prc_qte_min <= '.$qte.' and prc_qte_max >= '.$qte.'
			and prc_is_promotion = 1 and prc_prd_id='.$prd.'
			and price_is_apply( prc_tnt_id, prc_prd_id, '.$usr_holder.', '.$col.', prc_id, '.$prc.', "'.wst_websites_languages_default().'", "'.addslashes($usr_ref).'", -1, NULL )
	';

	// Si une date est donnée en paramètre, on récupèrera les tarifs non supprimé à ce moment et que la date soit dans la période de validité du tarif
	if( $date !== false ){
		$sql .= '
			and (prc_date_deleted is null or date(prc_date_deleted) > "'.addslashes($date).'")
			and prc_date_end > "'.addslashes($date).'" and prc_date_start <= "'.addslashes($date).'"
		';
	}

	// Pour Proloisirs, les promotions sur produits sont exclus sauf sur l'Extranet
	if ($config['tnt_id'] == 4 && $config['wst_id'] == 30) {
		$rpromos = false;
	}elseif( $config['tnt_id'] == 16 && $config['wst_id'] == 367 ){
		$rpromos = false;
	}else{
		$rpromos = ria_mysql_query($sql);
	}
	if( !$rpromos || !ria_mysql_num_rows($rpromos) ){
		if ($include_remise) {
			$spe_promo = pmt_promotions_get($prd, $price_info, $usr, false, $remise_applicable, $pmt_id_to_exclude, $dps, $batch, $pmt_field);

			if( is_array($spe_promo) ){
				$memcached->set( $key_memcached, ($spe_promo === false ? 'none' : $spe_promo), 60 );
				return $spe_promo;
			}
		}

		$memcached->set( $key_memcached, 'none', 60 );
		return false;
	}

	// convertit en tableau
	$promos_ar = array();
	while( $promo = ria_mysql_fetch_assoc($rpromos) ){
		$promos_ar[] = $promo;
	}

	// ne conserve que la meilleure (les promotions ne sont pas cumulables)
	$best_i = 0;
	for( $i = 1; $i < sizeof($promos_ar); $i++ ){

		$sql_compare = '
			select compare_prices( '.$config['tnt_id'].', '.$promos_ar[ $best_i ]['id'].', '.$promos_ar[ $i ]['id'].' )
		';

		if( $r_compare = ria_mysql_query($sql_compare) ){
			if( ria_mysql_num_rows($r_compare) ){
				if( ria_mysql_result($r_compare, 0, 0) == $promos_ar[ $i ]['id'] ){
					$best_i = $i;
				}
			}
		}

	}

	$promo = $promos_ar[ $best_i ];

	if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
		$ecotaxe = prd_products_get_ecotaxe( $prd );
	}

	// ajoute les informations issues du tarif de base
	$promo['price_ht'] = $price_info['price_ht'];
	$promo['price_ttc'] = $price_info['price_ht'] * $price_info['tva_rate'];
	$promo['origin_price_ht'] = $promo['price_ht'];
	$promo['origin_price_ttc'] = $promo['price_ttc'];

	switch( $promo['type'] ){
		case NEW_PRICE:
			$promo['price_ht'] = $promo['value'];
			$promo['price_ttc'] = $promo['value'] * $price_info['tva_rate'];
			break;
		case DISCOUNT_PERCENT:
			if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
				$promo['price_ht'] = $promo['price_ht'] - $ecotaxe;
				$promo['price_ht'] = ( $promo['price_ht'] * ( 100 - $promo['value'] ) / 100 ) + $ecotaxe;
			}else{
				$promo['price_ht'] *= ( 100 - $promo['value'] ) / 100;
			}
			$promo['price_ttc'] = $promo['price_ht'] * $price_info['tva_rate'];
			break;
		case DISCOUNT_VALUE:
			$promo['price_ht'] -= $promo['value'];
			$promo['price_ttc'] = $promo['price_ht'] * $price_info['tva_rate'];
			break;
	}

	if( $promo['price_ht'] === null ){
		if ($include_remise) {
			$spe_promo = pmt_promotions_get($promo, $price_info, $usr, false, $remise_applicable, $pmt_id_to_exclude, $dps, $batch, $pmt_field);
			if( is_array($spe_promo) ){
				$memcached->set( $key_memcached, ($spe_promo === false ? 'none' : $spe_promo), 60 );
				return $spe_promo;
			}
		}

		$memcached->set( $key_memcached, 'none', 60 );
		return false;
	}

	if( $include_remise ){
		$spe_promo = pmt_promotions_get($promo, $price_info, $usr, false, $remise_applicable, $pmt_id_to_exclude, $dps, $batch, $pmt_field);

		if( is_array($spe_promo) ){
			$promo = $spe_promo;
		}
	}

	$memcached->set( $key_memcached, ($promo === false ? 'none' : $promo), 60 );
	return $promo;

}

// \cond onlyria
/**	Cette fonction récupère les champs avancés sur lesquels il est possible de conditionner un tarif (ou une éxonération de taxes).
 *	@param int $id Optionnel, identifiant ou tableau d'identifiants .
 *	@param $sort Optionnel, paramètre de tri, sous la forme d'un tableau associatif colonne => direction. Par défaut le tri est par priorité croissante.
 *	@param $tva Optionnel, si activé, récupère les champs avancés permettant une exonération.
 *	@param int $fld_id Optionnel, id de champs avancé
 *	@param int $fld_id_1 Optionnel, id 2 de champs avancé
 *	@param int $fld_id_2 Optionnel, id 3 de champs avancé
 *	@param int $fld_id_3 Optionnel, id 4 de champs avancé
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- fld_id : identifiant du champ avancé.
 *		- fld_id_1 : identifiant du champ avancé.
 *		- fld_id_2 : identifiant du champ avancé.
 *		- fld_id_3 : identifiant du champ avancé.
 *		- priority : ordre de priorité (si $tva FALSE).
 *		- date_modified : date de dernière modification (si $tva FALSE).
 *		- date_modified_en : date de dernière modification au format EN (si $tva FALSE).
 */
function prc_fields_get( $id=0, $sort=false, $tva=false, $fld_id=0, $fld_id_1=0, $fld_id_2=0, $fld_id_3=0 ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}
	if( $fld_id !== 0 && !is_numeric($fld_id) ) return false;
	if( $fld_id_1 !== 0 && !is_numeric($fld_id_1) ) return false;
	if( $fld_id_2 !== 0 && !is_numeric($fld_id_2) ) return false;
	if( $fld_id_3 !== 0 && !is_numeric($fld_id_3) ) return false;

	global $config;

	if( $tva ){
		$sql = '
			select ptf_fld_id as fld_id
			from prc_tva_fields
			where ( ptf_tnt_id = '.$config['tnt_id'].' or ptf_tnt_id=0 )
		';

		if( $fld_id > 0 ){
			$sql .= ' and ptf_fld_id = '.$fld_id;
		}

		return ria_mysql_query($sql);
	}

	$sql = '
		select
			ppf_id as id, ppf_fld_id as fld_id, ppf_fld_id_1 as fld_id_1, ppf_fld_id_2 as fld_id_2, ppf_fld_id_3 as fld_id_3, ppf_priority as "priority", ppf_date_modified as date_modified_en,
			date_format(ppf_date_modified, "%d/%m/%Y à %H:%i") as date_modified
		from prc_price_fields
		where ppf_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($id) ){
		$sql .= ' and ppf_id in ('.implode(', ', $id).')';
	}

	if( $fld_id > 0 ){

	}

	$sort_final = array();

	if( is_array($sort) ){
		foreach( $sort as $col => $dir ){
			$dir = strtolower(trim($dir)) == 'asc' ? 'asc' : 'desc';
			switch( strtolower(trim($col)) ){
				case 'fld_id':
					$sort_final[] = 'ppf_fld_id '.$dir;
					break;
				case 'priority':
					$sort_final[] = 'ppf_priority '.$dir;
					break;
			}
		}
	}

	// Tri par défaut
	if( !sizeof($sort_final) ){
		$sort_final[] = 'ppf_priority asc';
	}

	$sql .= '
		order by '.implode(', ', $sort_final).'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Détermine quel sera la priorité du prochain champ applicable aux tarifications
 *	@return bool False en cas d'erreur
 *	@return int Indice de priorité dans le cas contraire
 */
function prc_fields_get_lower_priority(){
	global $config;

	$r_ppf = ria_mysql_query('
		select ppf_priority as priority
		from prc_price_fields
		where ppf_tnt_id='.$config['tnt_id'].'
		order by ppf_priority asc
	');

	$break = false; $rank = 1;

	while( $ppf = ria_mysql_fetch_array( $r_ppf ) ){
		if( $ppf['priority']>$rank )
			$break = true;
		$rank = $ppf['priority'] + 1;
	}

	if( !$break ) $rank++;

	return $rank;
}
// \endcond

// \cond onlyria
/** Met à jour la priorité tarifaire d'un champ libre
 *	@param int $id Obligatoire, Identifiant de la priorité
 *	@param $priority Facultatif, Ordre de priorité. 1 est la valeur la plus forte, 0 permet de définir l'absence de priorité (quand prc_apply=0)
 *	@return bool True si succès, False sinon
 */
function prc_fields_set_priority( $id, $priority=0 ){
	global $config;

	if( !is_numeric($id) ) return false;
	if( !is_numeric($priority) || $priority<0 ) return false;

	$sql = '
		update prc_price_fields
		set ppf_priority='.($priority==0 ? 'null' : $priority).'
		where ppf_id='.$id.'
			and ppf_tnt_id='.$config['tnt_id'].'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Permet de baisser le niveau de priorité tarifaire d'un champ libre
 *	@param int $id Obligatoire, identifiant de la priorité
 *	@param $annexe Facultatif, identifiant d'une seconde priorité
 *	@return bool True si la mise à jour s'est correctement déroulée
 *	@return bool False dans le cas contraire
 */
function prc_fields_set_priority_down( $id=0, $annexe=0 ){
	if( $id!==0 && !is_numeric($id) ) return false;
	if( $annexe!==0 && !is_numeric($annexe) ) return false;
	if( $id==0 && $annexe==0 ) return false;
	global $config;

	if( $id>0 ){
		$priority = prc_fields_get_priority($id);
		$annexe_priority = $annexe>0 ? prc_fields_get_priority($annexe) : 'ppf_priority+1';
		$sql = '
			update prc_price_fields
			set ppf_priority=('.$annexe_priority.')
			where ppf_id='.$id.'
				and ppf_tnt_id='.$config['tnt_id'].'
		';
		if( !ria_mysql_query($sql) )
			return false;
		if( $annexe>0 ){
				$sql = '
				update prc_price_fields
				set ppf_priority='.$priority.'
				where ppf_id='.$annexe.'
					and ppf_tnt_id='.$config['tnt_id'].'
			';

		   return ria_mysql_query( $sql );
		}
		return true;
	} else {
		return ria_mysql_query('
			update prc_price_fields
			set ppf_priority=ppf_priority-1
			where ppf_id='.$annexe.'
				and ppf_tnt_id='.$config['tnt_id'].'
		');
	}
}
// \endcond

// \cond onlyria
/** Permet de baisser le niveau de priorité tarifaire d'un champ libre
 *	@param int $id Obligatoire, identifiant d'une priorité
 *	@param $annexe Facultatif, identifiant d'une seconde priorité
 *	@return bool True si la mise à jour s'est correctement déroulée
 *	@return bool False dans le cas contraire
 */
function prc_fields_set_priority_up( $id, $annexe=0 ){
	if( $id!==0 && !is_numeric($id) ) return false;
	if( $annexe!==0 && !is_numeric($annexe) ) return false;
	if( $id==0 && $annexe==0 ) return false;
	global $config;

	if( $id>0 ){
		$priority = prc_fields_get_priority($id);
		$annexe_priority = $annexe>0 ? prc_fields_get_priority($annexe) : 'ppf_priority-1';
		$sql = '
			update prc_price_fields
			set ppf_priority=('.$annexe_priority.')
			where ppf_id='.$id.'
				and ppf_tnt_id='.$config['tnt_id'].'
		';
		if( !ria_mysql_query($sql) )
			return false;
		if( $annexe>0 ){
			$sql = '
				update prc_price_fields
				set ppf_priority='.$priority.'
				where ppf_id='.$annexe.'
					and ppf_tnt_id='.$config['tnt_id'].'
			';

		   return ria_mysql_query( $sql );
		}
		return true;
	} else {
		return ria_mysql_query('
			update prc_price_fields
			set ppf_priority=ppf_priority+1
			where ppf_id='.$annexe.'
				and ppf_tnt_id='.$config['tnt_id'].'
		');
	}
}
// \endcond

// \cond onlyria
/** Récupère la priorité de tarification d'un champ libre
 *	@param int $id Facultatif, Identifiant de la priorité Si 0, retourne la priorité du prochain champ. Si -1, retourne la priorité de la quantité minimale de commande.
 *	@return Indice de priorité, ou False en cas d'erreur
 */
function prc_fields_get_priority( $id=0 ){
	global $config;

	if( !is_numeric($id) || $id<-1 ) return false;

	if( $id>0 ){
		$res = ria_mysql_query( 'select ppf_priority from prc_price_fields where ppf_fld_id='.$id.' and ppf_tnt_id='.$config['tnt_id'] );
		if( !$res || !ria_mysql_num_rows($res) ) return false;
		return ria_mysql_result( $res,0,0 );
	}elseif( $id<0 ){
		$fields = fld_fields_get( 0,0,0,0,0,0,null,array('prc-priority'),true,null,null,null,true,null );
		if( !$fields ) return false;
		$next = 1;
		while( $field = ria_mysql_fetch_array($fields) ){
			if( $fields['prc-priority']!=$next )
				return $next;
			$next++;
		}
		return $next;
	}else{
		return prc_fields_get_lower_priority();
	}
}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'une promotion groupée.
 *
 *	@param string $name Obligatoire, Nom de la promotion
 *	@param $datestart Obligatoire, Date et heure de début de la promotion
 *	@param $datestop Obligatoire, Date et heure de fin de la promotion
 *	@param $qte_min Optionnel, Quantité minimale commandable pour bénéficier de la promotion
 *	@param $qte_max Optionnel, Quantité maximale commandable pour bénéficier de la promotion
 *	@param string $desc Optionnel, Description de la promotion
 *	@param $priceminister_key Optionnel, identifiant de la campagne promotionnelle sur Priceminister
 *
 *	@return bool false en cas d'erreur
 *	@return int l'identifiant attribué au groupe en cas de succès
 *
 */
function prc_promotion_groups_add( $name, $datestart, $datestop, $qte_min=1, $qte_max=false, $desc=false, $priceminister_key=0 ){
	global $config;

	if( !trim($name) ) return false;
	if( !isdateheure($datestart) ) return false;
	if( !isdateheure($datestop) ) return false;
	if( !is_numeric($qte_min) || $qte_min<1 ) return false;
	if( $qte_max!==false && ( !is_numeric($qte_max) || $qte_max<$qte_min ) ) return false;

	if( !is_numeric($priceminister_key) || $priceminister_key < 0 ){
		return false;
	}

	$datestart = dateheureparse($datestart);
	$datestop = dateheureparse($datestop);
	$name = ucfirst(trim($name));
	$desc = $desc ? ucfirst(trim($desc)) : false;

	$sql = '
		insert into prc_promotion_groups
			( grp_tnt_id, grp_name, grp_desc, grp_date_start, grp_date_stop, grp_qte_min, grp_qte_max, grp_priceminister_key )
		values
			( '.$config['tnt_id'].', \''.addslashes($name).'\', '.( $desc ? '\''.addslashes($desc).'\'' : 'NULL' ).', \''.$datestart.'\', \''.$datestop.'\', '.$qte_min.', '.( $qte_max ? $qte_max : 'NULL' ).', '.( $priceminister_key > 0 ? $priceminister_key : 'null' ).' )
	';

	$res = ria_mysql_query($sql);

	if( $res )
		return ria_mysql_insert_id();

	return false;
}
// \endcond

// \cond onlyria
/**	Permet la modification d'un groupe de promotions.
 *	Cette fonction n'est plus utilsé par la nouvelle interface d'administration
 *
 *	@param int $id Obligatoire, Identifiant du groupe de promotions à mettre à jour
 *	@param string $name Obligatoire, Nom de la promotion
 *	@param $datestart Obligatoire, Date et heure de début de la promotion
 *	@param $datestop Obligatoire, Date et heure de fin de la promotion
 *	@param $qte_min Optionnel, Quantité minimale commandable pour bénéficier de la promotion
 *	@param $qte_max Optionnel, Quantité maximale commandable pour bénéficier de la promotion
 *	@param string $desc Optionnel, Description de la promotion
 *	@param $priceminister_key Optionnel, identifiant de la campagne promotionnelle sur Priceminister
 *
 *	@return bool false en cas d'erreur
 *	@return bool true en cas de succès
 *
 */
function prc_promotion_groups_update( $id, $name, $datestart, $datestop, $qte_min=1, $qte_max=false, $desc=false, $priceminister_key=0 ){
	global $config;

	if( !prc_promotion_groups_exists($id) ) return false;

	if( !trim($name) ) return false;
	if( !isdateheure($datestart) ) return false;
	if( !isdateheure($datestop) ) return false;
	if( !is_numeric($qte_min) || $qte_min<1 ) return false;
	if( $qte_max!==false && ( !is_numeric($qte_max) || $qte_max<$qte_min ) ) return false;

	if( !is_numeric($priceminister_key) || $priceminister_key < 0 ){
		return false;
	}

	$datestart = dateheureparse($datestart);
	$datestop = dateheureparse($datestop);
	$name = ucfirst(trim($name));
	$desc = $desc ? ucfirst(trim($desc)) : false;

	$sql = '
		update prc_promotion_groups set
			grp_desc='.( $desc ? '\''.addslashes($desc).'\'' : 'NULL' ).',
			grp_name=\''.addslashes($name).'\',
			grp_date_start=\''.$datestart.'\',
			grp_date_stop=\''.$datestop.'\',
			grp_qte_min='.$qte_min.',
			grp_qte_max='.( $qte_max ? $qte_max : 'NULL' ).',
			grp_priceminister_key = '.( $priceminister_key > 0 ? $priceminister_key : 'null' ).'
		where
			grp_tnt_id='.$config['tnt_id'].' and
			grp_id='.$id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Vérifie la validité d'un identifiant de groupe de promotions.
 *
 *	@param int $id Obligatoire, Identifiant du groupe de promotions à tester
 *
 *	@return bool true si l'identifiant correspond à un groupe de promotions enregistré dans la base de données
 *	@return bool false si l'identifiant est invalide ou ne correspond à aucun groupe de promotions enregistré
 *
 */
function prc_promotion_groups_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('select grp_id from prc_promotion_groups where grp_tnt_id='.$config['tnt_id'].' and grp_id='.$id.' limit 0,1');
	if( $res===false ) return false;

	return ria_mysql_num_rows($res)>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'un groupe de promotions.
 *
 *	@param int $id Obligatoire, Identifiant du groupe de promotions à supprimer.
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function prc_promotion_groups_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	ria_mysql_query('update prc_prices set prc_grp_id=NULL where prc_grp_id='.$id.' and prc_tnt_id='.$config['tnt_id']);
	ria_mysql_query('delete from prc_group_conditions where pgc_tnt_id='.$config['tnt_id'].' and pgc_grp_id='.$id);
	return ria_mysql_query('delete from prc_promotion_groups where grp_tnt_id='.$config['tnt_id'].' and grp_id='.$id);
}
// \endcond

// \cond onlyria
/** Cette fonction récupère des groupes de promotions filtrés selon des paramètres optionnels
 *	@param int $id Facultatif, Identifiant du groupe. Si le paramètre est spécifié, les autres paramètres sont ignorés
 *	@param $date Facultatif, Détermine une date comprise dans l'interval de la promotion. Si ce parmaètre est spécifié, le paramètre $current_activated n'est pas pris en compte
 *	@param $current_activated Facultatif, Détermine la période d'activation
 *	@param $qte Facultatif, Quantité pour laquelle les promotions du groupe peuvent s'appliquer
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant du groupe de promotion
 *		- name : Nom du groupe de promotion
 *		- desc : Description facultative du groupe de promotion
 *		- date-start : Date de début pour les promotions du groupe (note : peut être surchargé par chaque promotion)
 *		- date-stop : Date de fin pour les promotions du groupe (note : peut être surchargé par chaque promotion)
 *		- hour-start : Heure de début pour les promotions du groupe (note : peut être surchargé par chaque promotion)
 *		- hour-stop : Heure de fin pour les promotions du groupe (note : peut être surchargé par chaque promotion)
 *		- qte-min : Quantité minimale commandable pour que les promotions du groupe s'appliquent (surchargeable)
 *		- qte-max : Quantité maximale commandable pour que les promotions du groupe s'appliquent (surchargeable)
 *		- priceminister_key : identifiant de la campagne promotionnelle sur Priceminister
 */
function prc_promotion_groups_get( $id=0, $date=false, $current_activated=null, $qte=null ){
	global $config;

	$sql = '
		select
			grp_id as id, grp_name as name, grp_desc as "desc",
			date_format(grp_date_start,"%d/%m/%Y") as "date-start", time_format(grp_date_start,"%H:%m") as "hour-start",
			date_format(grp_date_stop,"%d/%m/%Y") as "date-stop", time_format(grp_date_stop,"%H:%m") as "hour-stop",
			grp_qte_min as "qte-min", grp_qte_max as "qte-max", grp_priceminister_key as priceminister_key
		from prc_promotion_groups
		where grp_tnt_id='.$config['tnt_id'].'
	';

	if( is_numeric($id) && $id>0 )
		$sql .= ' and grp_id='.$id;
	else{
		if( isdateheure($date) )
			$sql .= ' and grp_date_start<=\''.$date.'\' and grp_date_stop>'.$date;
		elseif( $current_activated!==null && is_numeric($current_activated) && $current_activated>0 ){
				switch( $current_activated ){
				case 1: // actuels
					$sql .= ' and grp_date_start<=now() and grp_date_stop>now()';
					break;
				case 2: // passés
					$sql .= ' and grp_date_stop<now()';
					break;
				case 3: // futurs
					$sql .= ' and grp_date_start>now()';
					break;
				case 4: // passés ou actuels
					$sql .= ' and ( ( grp_date_start<=now() and grp_date_stop>now() ) or grp_date_stop<now() )';
					break;
				case 5: // actuels ou futurs
					$sql .= ' and ( ( grp_date_start<=now()  and  grp_date_stop>now() ) or  grp_date_start>now() )';
					break;
				case 6:	//	actuels ou futurs ou récemment terminés
					if(!isset($config['recent_interval'])){
						$config['recent_interval'] = 60;
					}
					$interval = intval($config['recent_interval']);
					$sql .= ' and (
						(
							( grp_date_start<=now()  and  grp_date_stop>now() )
							or  grp_date_start>now()
						)
						or ( grp_date_stop<=now() and grp_date_stop is not null
							and grp_date_stop>DATE_SUB(now(), INTERVAL '.$interval.' day ) )
					)';
					break;
				case 7:	//	Récemment terminés
					if(!isset($config['recent_interval'])){
						$config['recent_interval'] = 60;
					}
					$interval = intval($config['recent_interval']);
					$sql .= ' and
						(
							(
								grp_date_stop<=now() and grp_date_stop is not null
								and grp_date_stop>=DATE_SUB(now(), INTERVAL '.$interval.' day )

							)
					 	)';
					break;
			}
		}
		if( $qte!==null && is_numeric($qte) && $qte>0 )
			$sql .= ' and grp_qte_min<='.$qte.' and ( grp_qte_max is null or grp_qte_min>='.$qte.' )';

	}

	$all_in = false;

	$sql .= ' and (grp_id in (
			select prc_grp_id
			from prc_prices
			where prc_tnt_id='.$config['tnt_id'].'
				and prc_is_deleted=0
	';
	if( isdateheure($date) )
		$sql .= ' and prc_date_start<=\''.$date.'\' and prc_date_end>'.$date;
	elseif( $current_activated!==null && is_numeric($current_activated) && $current_activated>0 ){
		switch( $current_activated ){
			case 1: // actuels
				$sql .= ' and prc_date_start<=now() and prc_date_end>now()';
				break;
			case 2: // passés
				$sql .= ' and prc_date_end<now()';
				break;
			case 3: // futurs
				$sql .= ' and prc_date_start>now()';
				break;
			case 4: // passés ou actuels
				$sql .= ' and ( ( prc_date_start<=now() and prc_date_end>now() ) or prc_date_end<now() )';
				break;
			case 5: // actuels ou futurs
				$sql .= ' and ( ( prc_date_start<=now()  and  prc_date_end>now() ) or  prc_date_start>now() )';
				break;
		}

	}elseif( !($qte!==null && is_numeric($qte) && $qte>0) ){
		$all_in = true;
	}

	if( $qte!==null && is_numeric($qte) && $qte>0 ){
		$sql .= ' and prc_qte_min<='.$qte.' and ( prc_qte_min>='.$qte.' )';
	}

	if( $all_in ){
		$sql .= ') or not exists ( select 1 from prc_prices where prc_tnt_id='.$config['tnt_id'].' and prc_is_deleted=0 and prc_grp_id=grp_id ) )';
	}else{
		$sql .= ') )';
	}

	$sql .= '
		order by grp_date_start desc, grp_date_stop desc
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant de la campagne promotionnelle sur Priceminister d'un groupe de promotion
 *	@param $grp_id Obligatoire, identifiant d'un groupe
 *	@return int L'identifiant de la campagne sur Priceminister
 */
function prc_promotion_groups_get_priceminister_key( $grp_id ){
	if( !is_numeric($grp_id) || $grp_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select grp_priceminister_key
		from prc_promotion_groups
		where grp_tnt_id = '.$config['tnt_id'].'
			and grp_id = '.$grp_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['grp_priceminister_key'];
}
// \endcond

// \cond onlyria
/** Crée une condition d'application d'un groupe de promotion
 *	@param $grp_id Obligatoire, Identifiant d'un groupe de promotion
 *	@param int $fld_id Obligatoire, Identifiant du champ libre
 *	@param $value Obligatoire, Valeur de la condition
 *	@param $symbol Obligatoire, Détermine l'opérateur à appliquer sur la condition.
 *	@return bool True en cas de succès
 *	@return bool False dans le cas contraire
 */
function prc_group_conditions_add( $grp_id, $fld_id, $value, $symbol ){

	global $config;

	if( prc_group_conditions_exists($grp_id,$fld_id) ) return prc_group_conditions_upd( $grp_id,$fld_id,$value,$symbol );

	if( !is_numeric($grp_id) || $grp_id<=0 ) return false;

	// Ne pas utiliser prc_prices_get ou prc_prices_exists car le prix en question peut avoir une date de suppression temporaire

	// déplacer ci dessous
	// if( !fld_fields_is_prices_apply($fld_id) ) return false;
	if( prc_conditions_is_valid( $fld_id,$symbol,$value )!==true ) return false;

	$value = trim( $value );

	$sql = '
		insert into prc_group_conditions (
			pgc_tnt_id, pgc_fld_id, pgc_grp_id, pgc_value, pgc_symbol
		) values (
			'.$config['tnt_id'].', '.$fld_id.', '.$grp_id.', \''.addslashes($value).'\', \''.addslashes($symbol).'\'
		)
	';

	return ria_mysql_query( $sql );// or die (mysql_error());
}
// \endcond

// \cond onlyria
/** Supprime une condition sur un groupe de promotion
 *	@param $grp_id Obligatoire, Identifiant d'un groupe de promotion
 *	@param int $fld_id Optionnel, identifiant du champ libre, si cet argument n'est pas précisé, toutes les conditions d'un groupe de promotion seront supprimées
 *
 *	@return bool Retourne true en cas de succès
 *	@return bool Retourne false dans le cas contraire
 */
function prc_group_conditions_del( $grp_id, $fld_id=0 ){
	global $config;

	if( !is_numeric($grp_id) || $grp_id<=0 ) return false;
	if( !is_numeric($fld_id) || $fld_id<0 ) return false;

	$sql = '
		delete from prc_group_conditions
		where pgc_grp_id='.$grp_id.' and
		pgc_tnt_id='.$config['tnt_id'].'
	';

	if( $fld_id>0 )
		$sql .= ' and pgc_fld_id='.$fld_id;
	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Met à jour la valeur et le symbole d'une condition sur un groupe de promotion
 *	@param $grp_id Obligatoire, Identifiant d'un groupe de promotion
 *	@param int $fld_id Obligatoire, Identifiant du champ libre
 *	@param $value Obligatoire, Nouvelle valeur
 *	@param $symbol Obligatoire, Nouveau symbole
 *
 *	@return bool Retourne true en cas de succès
 *	@return bool Retourne false dans le cas contraire
 */
function prc_group_conditions_upd( $grp_id, $fld_id, $value, $symbol ){
	global $config;

	if( !is_numeric($grp_id) || $grp_id<=0 ) return false;
	if( !is_numeric($fld_id) || $fld_id<=0 ) return false;

	// Ne pas utiliser prc_prices_get ou prc_prices_exists car le prix en question peut avoir une date de suppression temporaire

	if( prc_conditions_is_valid( $fld_id,$symbol,$value )!==true ) return false;

	$value = trim( $value );

	$sql = '
		update prc_group_conditions
		set pgc_value=\''.addslashes($value).'\',
		pgc_symbol=\''.addslashes($symbol).'\'
		where pgc_prc_id='.$grp_id.' and
		pgc_fld_id='.$fld_id.' and
		pgc_tnt_id='.$config['tnt_id'].'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Récupère les conditions attachées à un groupe de promotion
 *	@param int $id Obligatoire, Identifiant d'un groupe de promotion
 *	@param int $cls_id Facultatif, classe de champs libres sur laquelle filtrer le résultat
 *
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- group : Identifiant d'un groupe de promotion
 *		- fld : Identifiant du champ libre
 *		- fld-cls : Identifiant de classe du champ libre
 *		- type-id : Type du champ libre
 *		- value : Valeur de la condition
 *		- symbol : Symbole de la condition
 */
function prc_group_conditions_get( $id, $cls_id=null ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	if( $cls_id!==null && !fld_classes_exists($cls_id) ) return false;

	$sql = '
		select
			pgc_grp_id as "group",
			fld_id as fld,
			fld_cls_id as "fld-cls",
			fld_type_id as "type-id",
			pgc_value as value,
			pgc_symbol as symbol
		from
			prc_group_conditions join
			fld_fields on (
				pgc_fld_id=fld_id and (pgc_tnt_id=fld_tnt_id or fld_tnt_id=0)
			)
		where
			pgc_grp_id='.$id.' and
			pgc_tnt_id='.$config['tnt_id'].' and
			fld_date_deleted is null
			and exists (
				select 1 from prc_price_fields where ppf_tnt_id = '.$config['tnt_id'].' and (
						ppf_fld_id = fld_id
						or ppf_fld_id_1 = fld_id
						or ppf_fld_id_2 = fld_id
						or ppf_fld_id_3 = fld_id
					)
			)
	';

	if( $cls_id!==null )
		$sql .= ' and fld_cls_id='.$cls_id;

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Détermine l'existence d'une condition sur un groupe de promotion
 *	@param $grp Obligatoire, Identifiant d'un groupe de promotion
 *	@param $fld Obligatoire, Identifiant du champ libre
 *
 *	@return bool True si la condition existe, False sinon
 */
function prc_group_conditions_exists( $grp, $fld ){
	global $config;

	if( !is_numeric($grp) || $grp<=0 ) return false;
	if( !is_numeric($fld) || $fld<=0 ) return false;

	$sql = '
		select pgc_value
		from prc_price_conditions
		where pgc_prc_id='.$grp.' and
		pgc_fld_id='.$fld.' and
		pgc_tnt_id='.$config['tnt_id'].'
	';

	$res = ria_mysql_query( $sql );
	if( $res===false ) return false;

	return ria_mysql_num_rows($res)>0;
}
// \endcond

// \cond onlyria
/// @}
/** \defgroup model_prices_drop Gestion des inscriptions aux baisses de prix
 *  \ingroup cpq
 *	Ce module permet de gérer les inscriptions aux baisses de prix
 *	@{
 */
// \endcond

/** Cette fonction permet d'ajouter une inscription au baisse de prix
 *	@param string $email Obligatoire, adresse e-mail
 *	@param int $prd_id Optionnel, identifiant d'un article parent (0 revient à recevoir toutes les baisses de prix, si un identifiant est donné, la règle "toutes les baisses" est supprimée)
 *	@param $once Optionnel, si une seule notification est faite ou bien à chaque fois que le prix baisse (par défaut : à chaque fois)
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function prc_prices_drop_add( $email, $prd_id=0, $once=false ){
	if( !isemail($email) ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id < 0 ){
		return false;
	}

	global $config;

	if( $prd_id == 0 ){
		if( !prc_prices_drop_del($email, 0) ){
			return false;
		}
	}

	if( prc_prices_drop_exists($email, $prd_id) ){
		return true;
	}

	$r_user = ria_mysql_query('
		select usr_id
		from gu_users
		where (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].')
			and usr_email = "'.addslashes( $email ).'"
	');

	$usr_id = 0;
	if( $r_user && ria_mysql_num_rows($r_user) ){
		$user = ria_mysql_fetch_assoc( $r_user );
		$usr_id = $user['usr_id'];
	}
	$sql = '
		insert into prc_prices_drop
			( pcd_tnt_id, pcd_wst_id, pcd_email, pcd_prd_id, pcd_usr_id, pcd_date_subscribed, pcd_notify_once )
		value
			( '.$config['tnt_id'].', '.$config['wst_id'].', "'.addslashes($email).'", '.$prd_id.', '.$usr_id.', now(), '.($once ? '1' : '0').' )
	';
	$res = ria_mysql_query($sql);
	return $res;
}

/**
 * Mat a jour la date de dernière notification
 *
 * @param integer $pcd_id
 * @return bool
 */
function prc_prices_drop_notified($pcd_id){
	if( !is_numeric($pcd_id) || $pcd_id <= 0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prc_prices_drop
		set pcd_date_last_notified = now()
		where pcd_tnt_id = '.$config['tnt_id'].'
			and pcd_id = '.$pcd_id.'
			and pcd_date_deleted is null
	');

	return $res;
}

/** Cette fonction permet de vérifier qu'une adresse e-mail est inscrite aux notifications de baisse de prix.
 *	@param string $email Obligatoire, adresse e-mail
 *	@param int $prd_id Optionnel, identifiant d'un article
 *	@return bool True si l'adresse e-mail est inscrite, False dans le cas contraire
 */
function prc_prices_drop_exists( $email, $prd_id=0 ){
	if( !isemail($email) ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1 from prc_prices_drop
		where pcd_tnt_id = '.$config['tnt_id'].'
			and pcd_email = "'.addslashes( $email ).'"
			and pcd_prd_id = '.$prd_id.'
			and pcd_date_deleted is null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

// \cond onlyria
/** Cette fonction permet de supprimer une inscription aux notifications de baisse de prix
 *	@param string $email Obligatoire, adresse e-mail
 *	@param int $prd_id Optionnel, identifiant d'un article
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function prc_prices_drop_del( $email, $prd_id=0 ){
	if( !isemail($email) ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id < 0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prc_prices_drop
		set pcd_date_deleted = now()
		where pcd_tnt_id = '.$config['tnt_id'].'
			and pcd_email = "'.addslashes( $email ).'"
			and pcd_prd_id = '.$prd_id.'
			and pcd_date_deleted is null
	');

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les inscriptions aux notifications de baisse de prix
 *	@param string $email Optionnel, adresse e-mail
 *	@param int $prd_id Optionnel, identifiant d'un article
 *	@param int $usr_id Optionnel, identifiant d'un compte client
 *	@param int $wst_id Optionnel, identifiant à partir duquel l'inscription a été faite
 *	@return resource Un résultat MySQL contenant :
 *					- wst_id : identifiant du site sur lequel la personne s'est inscrite
 *					- id : identifiant de l'insciption
 *					- email  : adresse e-mail inscite
 *					- prd_id : identifiant du produit (0 = toutes les baisses de prix)
 *					- usr_id : identifiant d'un compte client
 *					- uninscript : code de désinscription
 *					- date_last_notified : date de dernière notification
 *					- date_subscribed : date d'inscription à la notification
 *					- notify_once : si l'on doit notifié une fois ou à chaque fois
 */
function prc_prices_drop_get( $email='', $prd_id=0, $usr_id=0, $wst_id=0 ){
	if( trim($email) != '' && !isemail($email) ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id < 0 ){
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select pcd_wst_id as wst_id,
			pcd_id as id,
			pcd_email as email,
			pcd_prd_id as prd_id,
			pcd_usr_id as usr_id,
			md5(concat("prices_drop", pcd_email, pcd_id)) as uninscript,
			pcd_date_last_notified as date_last_notified,
			pcd_date_subscribed as date_subscribed,
			pcd_notify_once as notify_once
		from prc_prices_drop
		where pcd_tnt_id = '.$config['tnt_id'].'
			and pcd_date_deleted is null
	';

	if( trim($email) != '' ){
		$sql .= ' and pcd_email = "'.addslashes( $email ).'"';
	}

	if( $prd_id > 0 ){
		$sql .= ' and pcd_prd_id = '.$prd_id;
	}

	if( $usr_id > 0 ){
		$sql .= ' and pcd_usr_id = '.$usr_id;
	}

	if( $wst_id > 0 ){
		$sql .= ' and pcd_wst_id = '.$wst_id;
	}

	$res = ria_mysql_query( $sql );

	return $res;
}
// \endcond

/** Cette fonction permet de désinscrire un compte à une notification de baisse de prix
 *	@param string $email Obligatoire, adresse email
 *	@param $code Obligatoire, code de désinscription
 *	@return bool True si la désinscription s'est correctement déroulée, -1 si le code est faux, False dans tous les autres cas
 */
function prc_prices_drop_uninscripted( $email, $code ){
	if( !isemail($email) || trim($code) == '' ){
		return false;
	}

	global $config;

	$r_exists = '
		select pcd_id from prc_prices_drop
		where pcd_tnt_id = '.$config['tnt_id'].'
			and pcd_email = "'.addslashes( $email ).'"
			and (
				md5(concat("prices_drop", pcd_email, pcd_id)) = "'.addslashes($code).'"
				or
				md5(concat("prices_drop", pcd_email)) = "'.addslashes($code).'"
			)
	';

	if( !$r_exists || !ria_mysql_num_rows($r_exists) ){
		return -1;
	}

	while($exists = ria_mysql_fetch_assoc($r_exists) ){
		$res = ria_mysql_query('
			update prc_prices_drop
			set pcd_date_deleted = now()
			where pcd_tnt_id = '.$config['tnt_id'].'
				and pcd_id = '.$exists['pcd_id'].'
		');

		if( !$res ){
			return false;
		}
	}

	return true;
}

/** Cette fonction permet de récupérer une liste d'identifiant de produits pour lequels un compte client à souhaité recevoir la notification de baisse de prix
 *	Le tableau contiendra "all" si l'a souhaité reçevoir toutes les baisses de prix
 *	@param string $email Optionnel, adresse mail de l'inscrit
 *	@param int $wst_id Optionnel, identifiant à partir duquel l'inscription a été faite
 *	@return array Un tableau simple si une adresse email est donnée, sinon un tableau deux dimensions avec comme clé l'adresse mail.
 */
function prc_prices_drop_get_products( $email='', $wst_id=0 ){
	if( trim($email) != '' && !isemail($email) ){
		return false;
	}

	global $config;

	$ar_products = array();

	$r_prices_drop = prc_prices_drop_get( $email, 0, 0, $wst_id );
	if( $r_prices_drop && ria_mysql_num_rows($r_prices_drop) ){
		while( $prices_drop = ria_mysql_fetch_assoc($r_prices_drop) ){
			if ($prices_drop['notify_once'] === '1' && !is_null($prices_drop['date_last_notified'])) {
				continue;
			}
			if( !array_key_exists($prices_drop['email'], $ar_products) ){
				$ar_products[ $prices_drop['email'] ] = array();
			}

			$ar_products[ $prices_drop['email'] ][$prices_drop['id']] = ( is_numeric($prices_drop['prd_id']) && $prices_drop['prd_id'] > 0 ? $prices_drop['prd_id'] : 'all' );
		}

		if( trim($email) != '' ){
			$ar_products = $ar_products[ $email ];
		}
	}

	return $ar_products;
}

// \cond onlyria
/**	Cette fonction envoie une notification sur les baisses de prix
 *	@param string $usr_email Obligatoire, adresse e-mail du destinataire
 *	@param array $ar_products Obligatoire, tableau contenant les informations sur les produits ayant subient une baisse de prix
 *	@param int $wst_id Obligatoire, identifiant du site à partir duquel la notification a été enregistrée
 *	@return bool True si l'e-mail a bien été envoyé, False dans le cas contraire
 *
 */
function prc_prices_drop_notify( $usr_email, $ar_products, $wst_id ){
	if( !isemail($usr_email) ){
		return false;
	}

	if( !is_array($ar_products) || !sizeof($ar_products) ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id <= 0 ){
		return false;
	}

	$rcfg = cfg_emails_get( 'prices-drop', $wst_id );
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	global $config;

	// Récupère les informations sur l'utilisateur correspondant à cette adresse mail
	$user = array( 'title' => '', 'adr_firstname' => '', 'adr_lastname' => '' );
	$r_user = gu_users_get( 0, $usr_email );
	if( $r_user && ria_mysql_num_rows($r_user) ){
		$user = ria_mysql_fetch_assoc( $r_user );

		if($user['title_id']) {
			$titles = gu_titles_get($user['title_id']);
			if($titles && ria_mysql_num_rows($titles)) {
				$title = ria_mysql_fetch_array($titles);
				$user['title'] = $title['name'];
			}
		}
	}

	// Proprietaire de la boutique
	$owner = site_owner_get( $wst_id );

	$email = new Email();
	$cfg = ria_mysql_fetch_array( $rcfg );
	$email->setFrom( $cfg['from'] );
	$email->addBcc( $cfg['bcc'] );
	$email->setReplyTo( $cfg['reply-to'] );
	$email->addTo( $usr_email );

	// Gestion des notifications personnalisées
	{
		$file_notify_exists = false;

		$file_emails_perso = $config['site_dir'].'/include/view.emails.inc.php';
		if (file_exists($file_emails_perso)) {
			$file_notify_exists = true;
		} else {
			$file_emails_perso = $config['site_dir'].'/../include/view.emails.inc.php';

			if (file_exists($file_emails_perso)) {
				$file_notify_exists = true;
			}
		}

		if ($file_notify_exists) {
			require_once($file_emails_perso);

			if (function_exists('riashop_prices_drop_notify')) {
				$notify_invoice = riashop_prices_drop_notify($email, $cfg, $user, $ar_products, $owner);
				return $notify_invoice;
			}
		}
	}

	switch( $config['tnt_id'] ){
		case 14: {
			require_once( $config['site_dir'].'/../include/view.emails.inc.php' );
			$terredeviande_notify = terredeviande_prices_drop_notify($email, $user, $ar_products);
			return $terredeviande_notify;
		}
		default : {
			$prd = false;
			if( is_array($ar_products) && sizeof($ar_products) ){
				$prd = $ar_products[0];
			}

			$email->setSubject('Variation de tarifs depuis le '.date('d/m/Y', strtotime('-1 month')).' sur '.$config['site_name']);

			$email->addHtml( $config['email_html_header'] );
			$email->addParagraph('Bonjour '.trim( $user['title'].' '.$user['adr_firstname'].' '.$user['adr_lastname'] ).',');

			$html_table = '
				<p>Nous tenons à vous informer des variations de tarif public HT concernant les produits suivants :<br /><a href="'.$config['site_url'].'?utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=prices_drop&amp;utm_content=link_show_prices">Voir mes tarifs d’achat</a></p>
				<center style="display:block;padding:0;">
					<table style="border-collapse: collapse;font-family:Arial;" cellspacing="0" cellpadding="0">
			';

			$type_price = $config['prices_drop_ht'] == 'ttc' ? '<abbr title"Toutes Taxes Comprises">TTC</abbr>' : '<abbr title"Hors Taxes">HT</abbr>';
			foreach( $ar_products as $one_prd ){
				$one_prd['url_alias'] = prd_products_get_url( $one_prd['id'], true, $config['cat_root'] );

				$price = $one_prd['price_ht'];
				$old_price = $one_prd['price_max'];
				// if( $config['prices_drop_ht'] == 'ttc' ){
				// 	$price = $price * $one_prd['tva_rate'];
				// 	$old_price = $old_price * $one_prd['tva_rate'];
				// }

				$html_table .= '
						<tr>
							<td style="border: 1px solid #e1dad3;width:80px;text-align:center;">
								<a href="'.$config['site_url'].$one_prd['url_alias'].'?utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=prices_drop&amp;utm_content=produit"><center>
									<img style="border-bottom-width: 0; border-left-width: 0; border-right-width: 0; border-top-width: 0; display: block; line-height: 1; padding-bottom: 0px; padding-left: 0px; padding-right: 0px; padding-top: 0px" border="0" src="'.$config['site_url'].'/images/products/80x80/'.$one_prd['img_id'].'.jpg" width="80" height="80" alt="'.htmlspecialchars( $one_prd['title'] ).'"/>
								</center></a>
							</td>
							<td style="border: 1px solid #e1dad3;width:363px;padding:10px;">
								<div style="padding: 0 0 12px 0; font-size: 16px;">
									<a style="color:#59493f;" href="'.$config['site_url'].$one_prd['url_alias'].'?utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=prices_drop&amp;utm_content=produit"><strong>'.htmlspecialchars( $one_prd['title'] ).'</strong></a>
								</div>
								<div>'.strcut( html_revert_wysiwyg($one_prd['desc'].' '.$one_prd['desc-long']), 150 ).'</div>
							</td>
							<td style="border: 1px solid #e1dad3;width:145px;padding:10px;">
								<div style="color:#59493f;font-size: 17px;text-decoration: line-through;">'.number_format($old_price, 2 , ',', ' ').' &euro; '.$type_price.'</div>
								<div style="color:#59493f;font-size: 17px;"><strong>'.number_format($price, 2 , ',', ' ').' &euro; '.$type_price.'</strong></div>
							</td>
						</tr>
				';
			}

    		$html_table .= '
					</table>
				</center>
			';

			$email->addHtml( $html_table );
			$email->addParagraph('Si vous avez la moindre question sur '.( sizeof($ar_products) > 1 ? 'les produits' : 'le produit' ).' ou le fonctionnement du site, n’hésitez pas à nous contacter'.($owner && $owner['phone'] ? ' au <b>'.$owner['phone'].'</b>' : '').($owner && $owner['email'] ? ' ou par email : <a href="mailto:'.$owner['email'].'"><b>'.$owner['email'].'</b></a>' : '').', nous nous ferons une joie de vous répondre.');
			$email->addHtml('<span>Vous pouvez vous désinscire en cliquant sur le lien suivant : <a href="'.$config['site_url'].'/desinscription-baisse-prix/?p='.md5('prices_drop'.$usr_email).'">Me désinscire.</a></span>');
			$email->addHtml( $config['email_html_footer'] );
			break;
		}
	}

	return $email->send();
}
// \endcond

/// @}
/// @}

?>
