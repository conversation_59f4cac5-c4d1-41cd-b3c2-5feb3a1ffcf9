<?php
/**
 * \defgroup Import Import
 * \ingroup sync
 * @{
 * \page api-imports-index-add Ajout
 *
 * Cette fonction permet de réaliser un import
 *
 *   \code
 *       POST /imports/index/
 *   \endcode
 *
 * @param $imp_id Obligatoire, Identifiant de l'import à éxécuter (table "ipt_imports")
 * @param $is_backup Obligatoire, Booléen indiquant si l'import est un backup ou non
 *
 * @return true si l'ajout s'est déroulé avec succés
 * @}
*/

require_once("imports.inc.php");

switch( $method ){
	case 'add':
		if( !isset($_REQUEST['imp_id']) ){
			throw new BadFunctionCallException('L\'identifiant de l\'import est manquant.');
		}

		if( !isset($_REQUEST['is_backup']) ){
			throw new BadFunctionCallException('Certains paramètres sont manquants.');
		}

		try{
			if( !ipt_imports_exec($_REQUEST['imp_id'], $_REQUEST['is_backup']) ){
				throw new RuntimeException('Erreur lors de l\'éxécution de l\'import '.$_REQUEST['imp_id']);
			}
		}catch( Exception $e ){
			error_log('Erreur import : '.$e->getMessage()."\n Contexte : ".print_r($_REQUEST, true));
			throw new RuntimeException('Erreur lors de l\'éxécution de l\'import '.$_REQUEST['imp_id']);
		}

		$result = true;
	break;
}