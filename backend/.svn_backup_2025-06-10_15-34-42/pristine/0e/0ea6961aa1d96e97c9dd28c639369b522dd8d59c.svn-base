<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/resources.proto

namespace Google\Cloud\ServiceManagement\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generic specification of a source configuration file
 *
 * Generated from protobuf message <code>google.api.servicemanagement.v1.ConfigFile</code>
 */
class ConfigFile extends \Google\Protobuf\Internal\Message
{
    /**
     * The file name of the configuration file (full or relative path).
     *
     * Generated from protobuf field <code>string file_path = 1;</code>
     */
    private $file_path = '';
    /**
     * The bytes that constitute the file.
     *
     * Generated from protobuf field <code>bytes file_contents = 3;</code>
     */
    private $file_contents = '';
    /**
     * The type of configuration file this represents.
     *
     * Generated from protobuf field <code>.google.api.servicemanagement.v1.ConfigFile.FileType file_type = 4;</code>
     */
    private $file_type = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $file_path
     *           The file name of the configuration file (full or relative path).
     *     @type string $file_contents
     *           The bytes that constitute the file.
     *     @type int $file_type
     *           The type of configuration file this represents.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Servicemanagement\V1\Resources::initOnce();
        parent::__construct($data);
    }

    /**
     * The file name of the configuration file (full or relative path).
     *
     * Generated from protobuf field <code>string file_path = 1;</code>
     * @return string
     */
    public function getFilePath()
    {
        return $this->file_path;
    }

    /**
     * The file name of the configuration file (full or relative path).
     *
     * Generated from protobuf field <code>string file_path = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setFilePath($var)
    {
        GPBUtil::checkString($var, True);
        $this->file_path = $var;

        return $this;
    }

    /**
     * The bytes that constitute the file.
     *
     * Generated from protobuf field <code>bytes file_contents = 3;</code>
     * @return string
     */
    public function getFileContents()
    {
        return $this->file_contents;
    }

    /**
     * The bytes that constitute the file.
     *
     * Generated from protobuf field <code>bytes file_contents = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setFileContents($var)
    {
        GPBUtil::checkString($var, False);
        $this->file_contents = $var;

        return $this;
    }

    /**
     * The type of configuration file this represents.
     *
     * Generated from protobuf field <code>.google.api.servicemanagement.v1.ConfigFile.FileType file_type = 4;</code>
     * @return int
     */
    public function getFileType()
    {
        return $this->file_type;
    }

    /**
     * The type of configuration file this represents.
     *
     * Generated from protobuf field <code>.google.api.servicemanagement.v1.ConfigFile.FileType file_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setFileType($var)
    {
        GPBUtil::checkEnum($var, \Google\Cloud\ServiceManagement\V1\ConfigFile_FileType::class);
        $this->file_type = $var;

        return $this;
    }

}

