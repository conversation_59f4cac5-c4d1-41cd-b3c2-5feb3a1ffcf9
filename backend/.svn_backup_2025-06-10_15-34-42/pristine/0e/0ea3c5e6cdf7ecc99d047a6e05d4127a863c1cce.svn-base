<?php

// La fonction gu_valid_email est déclarée dans le fichier users.inc.php
require(dirname(__FILE__)."/../../include/users.inc.php");

/** Test de la fonction gu_valid_email
 *	Aujourd'hui, elle ne répond ok que si l'adresse est correctement formatée et le domaine possède un MX valide
 *	Les noms de domaines accentués ne sont pas acceptés.
 */
class userValidEmailTest extends PHPUnit_Framework_TestCase {

	/**
	 * @dataProvider bad_emails_provider
	 */
	public function testGuValidEmailBad($email) {
		$this->assertFalse(gu_valid_email($email));
	}

	/**
	 * @dataProvider good_emails_provider
	 */
	public function testGuValidEmailGood($email) {
		$this->assertTrue(gu_valid_email($email));
	}

	/**	Cette fonction fourni une liste d'adresses emails invalides
	 *	@return array liste d'adresses emails invalides, sous forme de tableaux imbriqués
	 */
	public static function bad_emails_provider() {
		return array(
			array("toto@toto"), // Domaine invalide
			array("ta@ta@ta"), // @ en excès
			array("tititi"), // Domaine manquant
			array("@"), // Arobase uniquement
			array("@riastudio.fr"), // Utilisateur manquant
			array("@google.com"), // Utilisateur manquant
			array("@orang.fr"), // Domaine invalide et utilisateur manquant
			array("@riastudio.fr"), // Utilisateur manquant
			array("jean-pau;<EMAIL>"), // Utilisateur mal formé
			array("<EMAIL>"), // Nom de domaine invalide
			array("fdqsdfqsdfqsdfqsdfototggdsfgt.fr"), // Utilisateur et @ manquants
			array("<EMAIL>"), // Domaine invalide
			array("<EMAIL>"), // Utilisateur mal formé
			array("contact@télévision.fr"), // A passer dans good quand compatibilité IDN voire #
			array("sébastien.mahé@riastudio.fr"), //A passer dans good lors de l'implémentation de la compatibilité voire #
		);
	}

	/**	Cette fonction fourni une liste d'adresses emails valides
	 *	@return array liste d'adresses emails valides, sous forme de tableaux imbriqués
	 */
	public static function good_emails_provider() {
		return array(
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>"),
			array("<EMAIL>")
		);
	}

}
