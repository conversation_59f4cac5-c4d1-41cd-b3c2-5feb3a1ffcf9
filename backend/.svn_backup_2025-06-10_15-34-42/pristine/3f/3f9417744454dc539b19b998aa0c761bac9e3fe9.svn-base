<?php
	require_once('products.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class productsAddTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester l'insertion de produits valide
		 * @dataProvider validProducts
		 */
		public function testValidProductsAdd($ref, $name, $desc, $brand, $publish, $weight, $length, $width, $height, $keywords, $is_sync, $taxcode) {
            
            $prd_id = prd_products_add($ref, $name, $desc, $brand, $publish, $weight, $length, $width, $height, $keywords, $is_sync, $taxcode);

            // Vérifie que l'id est un integer
			$this->assertThat($prd_id, $this->logicalAnd(
				$this->isType('int'), 
				$this->greaterThan(0)
            ), "Erreur: prd_products_add retourne un identifiant invalide");
			
			// Vérifie que les champs sont corrects
            $rprd = prd_products_get($prd_id);
			$this->assertTrue($rprd || ria_mysql_num_rows($rprd), 'Erreur: lors de la vérification des champs du produits ajoutée');
            $prd = ria_mysql_fetch_assoc($rprd);
            
            $this->assertEquals(strtoupper2($ref), $prd['ref'], 'Erreur: référence du produits non conforme à la valeur lors de l\'ajout');

            $this->assertEquals(ucFirst($name), $prd['name'], 'Erreur: nom du produits non conforme à la valeur lors de l\'ajout');            

            $this->assertEquals(ucFirst($desc), $prd['desc'], 'Erreur: description du produits non conforme à la valeur lors de l\'ajout');

            $this->assertEquals($brand, $prd['brd_id'], 'Erreur: marque du produits non conforme à la valeur lors de l\'ajout');

            $this->assertTrue($publish == $prd['publish'], 'Erreur: propriété publish du produits non conforme à la valeur lors de l\'ajout');

            $this->assertEquals($weight, $prd['weight'], 'Erreur: poids du produits non conforme à la valeur lors de l\'ajout');

            $this->assertEquals($length, $prd['length'], 'Erreur: longueur du produits non conforme à la valeur lors de l\'ajout');

            $this->assertEquals($width, $prd['width'] , 'Erreur: largeur du produits non conforme à la valeur lors de l\'ajout');

            $this->assertEquals($height, $prd['height'], 'Erreur: hauteur du produits non conforme à la valeur lors de l\'ajout');

            $this->assertEquals(ucFirst($keywords), $prd['keywords'] , 'Erreur: mot clé du produits non conforme à la valeur lors de l\'ajout');

            $this->assertTrue($is_sync == $prd['is_sync'] , 'Erreur: propriété is_sync du produits non conforme à la valeur lors de l\'ajout');

            $this->assertEquals($taxcode, $prd['taxcode'] , 'Erreur: code douanier du produits non conforme à la valeur lors de l\'ajout');
		}
		
		
		/** Fonction permettant de tester l'insertion de produits invalide
		 * @dataProvider invalidProducts
		 */
		public function testInvalidProductsAdd($ref, $name, $desc, $brand, $publish, $weight, $length, $width, $height, $keywords, $is_sync, $taxcode, $error) {

            $this->assertFalse( prd_products_add($ref, $name, $desc, $brand, $publish, $weight, $length, $width, $height, $keywords, $is_sync, $taxcode), $error);            
		}

		public static function validProducts() {
			return array(
                //      ref             name          desc   brand      publish  weight   length   width   height   keywords     is_sync     taxcode
                array('ref1',    'produitTest1',          '',      0,      false,      0,       0,      0,      0,        '',      false,         ''),
                array('ref2',    'produitTest2',      'desc',      1,       true,   2300,       0,      0,      0, 'mot clé',      false,         ''),
                array('ref3',    'produitTest3',          '',      2,       true,   1200,      12,      3,     26,        '',       true, '90148000'),
                array('ref4',    'produitTest4',  'desc prd',      0,      false,      0,       6,      0,      0,        '',       true,         ''),
			);
		}

		public static function invalidProducts(){
			return array(
                 //      ref             name          desc   brand     publish  weight   length   width   height   keywords     is_sync     taxcode        message d'erreur
                array(01238,    'produitTest5',          '',  10000,      false,      0,       0,      0,      0,        '',      false,         '', 'Erreur: ajout du produit avec un id de marque invalide'),
			);
		}
	}
