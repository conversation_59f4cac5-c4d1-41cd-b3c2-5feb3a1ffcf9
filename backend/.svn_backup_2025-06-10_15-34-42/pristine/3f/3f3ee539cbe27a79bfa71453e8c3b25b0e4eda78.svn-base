<?php

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('products.inc.php');


/** \defgroup model_colisage Colisage
 *	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion du colisage des produits.
 *	Le colisage permet de créer des lots de produits.
 *
 *	@{
 */

// \cond onlyria
/**	Permet l'ajout d'un type de colisage.
 *	Le nom du type de colisage est automatiquement mis en forme par cette fonction :
 *	suppression des espaces de début et de fin de chaîne.
 *
 *	Tout nom vide ou contenant des guillemets sera refusé.
 *
 *	@param string $name Optionnel Nom du type de colisage.
 *	@param $qte Quantité pour ce colisage.
 *	@param bool $is_sync Optionnel Conditionnement synchronisé oui / non
 *	@param int $dps_id Identifiant du dépôt disposant de ce collisage
 *	@param $pkg_id Facultatif, identifiant du package
 *
 *	@return bool false en cas d'erreur.
 *	@return int l'identifiant du type de colisage en cas de succès.
 *
 */
function prd_colisage_types_add( $name, $qte, $is_sync=false, $dps_id=null, $pkg_id=null ){
	global $config;
	// Contrôles et formatage
	if( !trim( $name ) ) return false;
	$name = trim( $name );
	if( strstr( $name, '"' ) ) return false;
	$name = addslashes( $name );

	$qte = str_replace(array(' ', ','), array('', '.'), $qte);

	if( !is_numeric($qte) || $qte<=0 ) return false;
	if( $dps_id!==null && !is_numeric($dps_id)) return false;
	if( $pkg_id!==null && !is_numeric($pkg_id)) return false;

	$is_sync = $is_sync ? "1" : "0";

	// Procède à l'insertion
	$res = ria_mysql_query( '
		insert into prd_colisage_types (
			col_tnt_id, col_name, col_qte, col_is_sync, col_dps_id, col_pkg_id
		) values (
			'.$config['tnt_id'].', \''.$name.'\', '.$qte.', '.$is_sync.', '.($dps_id === null ? 'NULL' : $dps_id).', '.($pkg_id === null ? 'NULL' : $pkg_id).'
		)
	' );

	if( $res===false ) return false;

	return ria_mysql_insert_id();
}
// \endcond

// \cond onlyria
/**	Permet la mise à jour d'un type de colisage.
 *
 *	Les modifications apportées sur le nom du type sont les mêmes que lors d'un ajout
 *	(suppression des caractères de début et de fin de chaîne).
 *
 *	Tout nom vide ou contenant des guillemets sera refusé.
 *
 *	@param int $id Identifiant du type de colisage à mettre à jour.
 *	@param string $name Facultatif, nouveau nom du type de colisage. Si False, la valeur précédente n'est pas modifiée.
 *	@param $qte Facultatif, nouvelle quantité colisée. Si False, la valeur précédente n'est pas modifiée.
 *	@param int $dps_id Facultatif, Identifiant du dépôt disposant de ce collisage. Si False, la valeur précédente n'est pas modifiée.
 *	@param $pkg_id Facultatif, identifiant du package. Si False, la valeur précédente n'est pas modifiée.
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 *
 */
function prd_colisage_types_update( $id, $name=false, $qte=false, $dps_id=false, $pkg_id=false ){

	if( !prd_colisage_types_exists( $id ) ){
		return false;
	}

	if( $name === false && $qte === false && $dps_id === false && $pkg_id===false ){
		// pas de modifications
		return true;
	}

	if( $name !== false ){
		$name = trim($name);
		if( !$name || strstr($name, '"') ){
			return false;
		}
	}

	if( $qte !== false && ( !is_numeric($qte) || $qte <= 0 ) ){
		return false;
	}

	$up_cols = array();
	if( $name !== false ){
		$up_cols[] = 'col_name = "'.addslashes($name).'"';
	}
	if( $qte !== false ){
		$up_cols[] = 'col_qte = '.$qte;
	}
	if( $dps_id !== false ){
		if( $dps_id!==null && !is_numeric($dps_id)) return false;
		$up_cols[] = 'col_dps_id = '.($dps_id === null ? 'NULL' : $dps_id).'';
	}
	if( $pkg_id !== false ){
		if( $pkg_id!==null && !is_numeric($pkg_id)) return false;
		$up_cols[] = 'col_pkg_id = '.($pkg_id === null ? 'NULL' : $pkg_id).'';
	}
	global $config;

	$sql = '
		update prd_colisage_types
		set '.implode(', ', $up_cols).'
		where col_tnt_id = '.$config['tnt_id'].' and col_id = '.$id.'
	';
	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Permet de supprimer un ou plusieurs types de colisage.
 *
 *	@param int $id Obligatoire, Identifiant du type de colisage à supprimer (ou tableau d'identifiants).
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 *
 */
function prd_colisage_types_del( $id ){

	if( !is_array($id) ){
		$id = array($id);
	}

	if( !sizeof($id) ){
		return false;
	}

	foreach( $id as $one_id ){
		if( !is_numeric($one_id) || $one_id <= 0 ){
			return false;
		}
		if( prd_colisage_types_get_is_sync( $one_id ) ){
			return false;
		}
	}

	global $config;

	$sql = '
		delete from prd_colisage_classify
		where cly_tnt_id = '.$config['tnt_id'].'
			and cly_col_id in ('.implode(', ', $id).')
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$sql = '
		update prd_colisage_types
		set col_is_deleted = 1
		where col_tnt_id = '.$config['tnt_id'].'
			and col_id in ('.implode(', ', $id).')
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction supprime un type de conditionnement sans vérifier s'il est synchronisé ou non
 *	@param int $id Identifiant du conditionnement
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_colisage_types_del_sage( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !prd_colisage_types_exists( $id ) ){
		return true;
	}

	global $config;

	$sql = '
		delete from prd_colisage_classify
		where cly_tnt_id = '.$config['tnt_id'].' and cly_col_id = '.$id.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$sql = '
		update prd_colisage_types
		set col_is_deleted = 1
		where col_tnt_id = '.$config['tnt_id'].'
			and col_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Permet le chargement d'un ou plusieurs types de colisage, éventuellement filtrés en fonction des arguments optionnels fournis.
 *
 *	@param int $id Optionnel, identifiant d'un type de colisage sur lequel filtrer le résultat (ou tableau).
 *	@param int $qte Optionnel, détermine une quantité sur laquelle filtrer le résultat
 *	@param array $sort Optionnel, détermine les conditions de tri, sous la forme d'un tableau associatif ( nom de colonne => sens de tri ). Les valeurs autorisées sont id, name, qte et "count-prd" ( double-quotes obligatoires )
 *	@param string $name Optionnel, nom d'un type de colisage sur lequel filtrer le résultat.
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du type de colisage
 *			- name : désignation du type de colisage
 *			- qte : Quantité colisée
 *			- is_sync : Conditionnement synchronisé oui / non
 *			- count-prd : Nombre de produits conditionnés avec ce colisage
 *			- date_modified : date de dernière modification
 *			- dps_id : identifiant du dépôt
 *			- pkg_id : identifiant du package
 */
function prd_colisage_types_get( $id=0, $qte=false, $sort=false, $name='' ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	if( $qte !== false && ( !is_numeric($qte) || $qte <= 0 ) ){
		return false;
	}

	$name = trim($name);

	global $config;

	$sql = '
		select
			col_id as id, col_name as name, col_dps_id as dps_id, col_pkg_id as pkg_id,
			col_qte as qte, col_is_sync as is_sync, (
				select
					count(*)
				from
					prd_colisage_classify join
					prd_products on ( cly_prd_id=prd_id and prd_tnt_id=cly_tnt_id )
				where
					cly_tnt_id='.$config['tnt_id'].' and
					cly_col_id=col_id and
					prd_date_deleted is null
			) as "count-prd", col_date_modified as date_modified
		from
			prd_colisage_types
		where
			col_tnt_id = '.$config['tnt_id'].'
			and col_is_deleted = 0
	';

	if( sizeof($id) ){
		$sql .= ' and col_id in ('.implode(', ', $id).')';
	}

	if( $qte ){
		$sql .= ' and col_qte = '.$qte;
	}

	if( $name ){
		$sql .= ' and col_name = "'.addslashes( $name ).'"';
	}

	$real_order = array();
	$allowed_cols = array('id', 'name', 'rname', 'qte', 'count-prd');

	if( is_array($sort) && sizeof($sort) ){
		foreach( $sort as $col => $dir ){
			$col = strtolower(trim($col));
			if( in_array($col, $allowed_cols) ){
				if( $col == 'rname' ){
					$real_order[] = 'replace(CONVERT(col_name using utf8) collate utf8_general_ci, col_qte, "") '.( strtolower(trim($dir)) == 'desc' ? 'desc' : 'asc' );
				}else{
					$real_order[] = $col.( strtolower(trim($dir)) == 'desc' ? ' desc' : ' asc' );
				}
			}
		}
	}

	if( !sizeof($real_order) ){
		$real_order[] = 'col_id asc';
	}

	$sql .= ' order by '.implode(', ', $real_order);

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Détermine si un conditionnement est synchronisé
 *	@param int $id Identifiant du conditionnement
 *	@return bool True si le conditionnement est synchronisé, False sinon
 */
function prd_colisage_types_get_is_sync( $id ){

	if( !prd_colisage_types_exists( $id ) ){
		return false;
	}

	global $config;

	$sql = '
		select col_is_sync
		from prd_colisage_types
		where col_tnt_id = '.$config['tnt_id'].'
			and col_id = '.$id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	ria_mysql_result($res, 0, 0);

}
// \endcond

/**	Retourne le libellé associé à un conditionnement
 *	@param int $id Obligatoire, identifiant du conditionnement
 *	@param $with_qte Facultatif, si True, quantité conditionnée est rajoutée au libellé
 *	@param $pattern_qte Facultatif, si $with_qte est à True, détermine le séparateur entre le nom et la quantité
 *	@return Le libellé du conditionnement, False en cas d'échec
 */
function prd_colisage_types_get_name( $id, $with_qte=false, $pattern_qte=' ' ){

	if( !prd_colisage_types_exists( $id ) ){
		return false;
	}

	global $config;

	$sql = '
		select concat(col_name'.( $with_qte ? ', "'.addslashes($pattern_qte).'", col_qte' : '' ).')
		from prd_colisage_types
		where col_tnt_id = '.$config['tnt_id'].'
			and col_id = '.$id.'
	';

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res, 0, 0);

}

// \cond onlyria
/**	Permet le test de l'existance d'un type de colisage dans la base de données.
 *
 *	@param int $id Identifiant du type de colisage à tester.
 *
 *	@return bool true si le type de colisage existe dans la base, false s'il n'existe pas.
 *
 */
function prd_colisage_types_exists( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select col_id from prd_colisage_types
		where col_tnt_id = '.$config['tnt_id'].'
			and col_id = '.$id.'
			and col_is_deleted = 0
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}
// \endcond

/** Détermine la quantité pour un type de colisage
 *
 *	@param int $id Identifiant du type de colisage
 *
 *	@return La quantité pour ce colisage, False en cas d'erreur
 */
function prd_colisage_types_get_qte( $id ){
	global $config;

	if( !prd_colisage_types_exists( $id ) ) return false;

	$sql = 'select col_qte from prd_colisage_types where col_id='.$id.' and col_tnt_id='.$config['tnt_id'];

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res,0,0 );
}

// \cond onlyria
/** Permet l'assignation d'une nouvelle quantité à un colisage
 *	@param int $id identifiant du colisage
 *	@param $qte Quantité conditionnée
 *	@return bool True ou False suivant succès ou échec
 */
function prd_colisage_types_set_qte( $id, $qte ){
	global $config;

	if( !prd_colisage_types_exists($id) ) return false;
	if( !is_numeric($qte) || $qte<=0 ) return false;

	$sql = 'update prd_colisage_types set col_qte='.$qte.' where col_id='.$id.' and col_tnt_id='.$config['tnt_id'];

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un conditionnement.
 *	@param int $id Identifiant du conditionnement.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_colisage_types_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update prd_colisage_types
		set col_date_modified = now()
		where col_id = '.$id.' and col_tnt_id = '.$config['tnt_id'].'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction permet d'associer un ou plusieurs produits à un ou plusieurs conditionnements.
 *
 *	@param $col Obligatoire, identifiant de conditionnement ou tableau d'identifiants de conditionnements.
 *	@param int $prd Obligatoire, identifiant de produit ou tableau d'identifiants de produits.
 *	@param string $ref Optionnel, référence du produit spécifique au conditionnement (est annulé si $col ou $prd est un tableau).
 *	@param string $barcode Optionnel, code-barre du produit spécifique au conditionnement (est annulé si $col ou $prd est un tableau).
 *	@param $is_default Optionnel, détermine s'il s'agit du conditionnement par défaut (est annulé si $col ou $prd est un tableau).
 *
 *	@return bool True si toutes les opérations ont réussies, False sinon.
 */
function prd_colisage_classify_add( $col, $prd, $ref=null, $barcode=null, $is_default=false ){

	$col_ar = control_array_integer( $col );
	if( $col_ar === false ){
		return false;
	}

	$prd_ar = control_array_integer( $prd );
	if( $prd_ar === false ){
		return false;
	}

	global $config;

	// s'il y a plus de un produit ou code-barre, les autres paramètres doivent être ignorés
	if( sizeof($prd_ar) > 1 || sizeof($col_ar) > 1 ){
		$ref = null;
		$barcode = null;
		$is_default = false;
	}

	// parsing SQL
	$ref = $ref === null ? 'NULL' : '"'.addslashes(trim($ref)).'"';
	$barcode = $barcode === null ? 'NULL' : '"'.addslashes(trim($barcode)).'"';
	$is_default = $is_default ? 1 : 0;

	$results = array();
	$prd_to_up = array();

	$fields = array('cly_tnt_id', 'cly_col_id', 'cly_prd_id', 'cly_prd_ref', 'cly_prd_barcode', 'cly_is_default');

	foreach( $prd_ar as $prd_id ){
		foreach( $col_ar as $col_id ){

			if( prd_colisage_classify_exists( $prd_id, $col_id ) ){
				continue;
			}

			$values = array($config['tnt_id'], $col_id, $prd_id, $ref, $barcode, $is_default);

			$sql = '
				insert into prd_colisage_classify
					('.implode(', ', $fields).')
				values
					('.implode(', ', $values).')
			';

			$res = ria_mysql_query($sql);

			if( $res ){
				$prd_to_up[] = $prd_id;
			}
			$results[] = $res;

		}
	}

	if( sizeof($prd_to_up) ){
		prd_products_set_date_modified( $prd_to_up );
	}

	return !in_array(false, $results);

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour des informations concernant un conditionnement produit
 *	@param $col Obligatoire, identifiant du conditionnement
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param string $ref Optionnel, référence du produit spécifiquement pour le conditionnement
 *	@param string $barcode Optionnel, code-barre spécifique au conditionnement
 *	@param $is_default Optionnel, détermine s'il s'agit du conditionnement par défaut
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_colisage_classify_upd( $col, $prd, $ref=null, $barcode=null, $is_default=false ){
	if( !is_numeric($col) || $col<=0 ) return false;
	if( !is_numeric($prd) || $prd<=0 ) return false;
	$ref = $ref===null ? 'NULL' : '\''.addslashes(trim($ref)).'\'';
	$barcode = $barcode===null ? 'NULL' : '\''.addslashes(trim($barcode)).'\'';
	$is_default = $is_default ? '1' : '0';

	global $config;

	$sql = '
		update
			prd_colisage_classify
		set
			cly_prd_ref='.$ref.',
			cly_prd_barcode='.$barcode.',
			cly_is_default='.$is_default.'
		where
			cly_tnt_id='.$config['tnt_id'].'
			and cly_col_id='.$col.'
			and cly_prd_id='.$prd.'
	';

	prd_products_set_date_modified( $prd );
	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Supprime la relation entre un conditionnement et un ou plusieurs produits.
 *	@param $col_id Identifiant de conditionnement.
 *	@param int|array $prd_id Identifiant de produit ou tableau d'identifiants de produits.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_colisage_classify_del( $col_id, $prd_id ){

	if( !is_numeric($col_id) || $col_id <= 0 ){
		return false;
	}

	$prd_id = control_array_integer( $prd_id );
	if( $prd_id === false ){
		return false;
	}

	global $config;

	$sql = '
		delete from prd_colisage_classify
		where cly_tnt_id = '.$config['tnt_id'].'
			and cly_col_id = '.$col_id.'
			and cly_prd_id in ('.implode(', ', $prd_id).')
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	prd_products_set_date_modified( $prd_id );

	return true;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de supprimer tous les conditionnements lié à un produit
 *	@param int|array $prd_id Identifiant de produit ou tableau d'identifiants de produits.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_colisage_classify_del_all( $prd_id ){
	$prd_id = control_array_integer( $prd_id );
	if( $prd_id === false ){
		return false;
	}

	global $config;

	$sql = '
		delete from prd_colisage_classify
		where cly_tnt_id = '.$config['tnt_id'].'
			and cly_prd_id in ('.implode(', ', $prd_id).')
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	prd_products_set_date_modified( $prd_id );
	return true;
}
// \endcond

/**	Cette fonction permet de charger des conditionnements effectifs de produits - et non pas des conditionnements possibles, voir prd_colisage_types_get() pour cela - selon des paramètres optionnels.
 *	Par défaut, les résultats sont triés par ID de conditionnement, ID de produit et quantité croissante.
 *
 *	@param $col Optionnel, identifiant d'un conditionnement ou tableau d'identifiants
 *	@param int $prd Optionnel, identifiant d'un produit ou tableau d'identifiants
 *	@param $qte Optionnel, quantité conditionnée
 *	@param $sort Optionnel, paramètre de tri (colonne => ordre). La liste des colonnes possibles est la même que celle retournée par la fonction
 *	@param bool $correled Optionnel, si activé et $col et $prd des tableaux de même taille, contrôle un à un les couples col_id / prd_id
 *	@param int $dps_id Optionnel, liste d'identifiant de dépôt
 *	@param $pkg_id Optionnel, liste d'identifiant de type de paquet
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- col_id : identifiant du conditionnement
 *		- col_name : nom du conditionnement
 *		- prd_id : identifiant du produit
 *		- qte : quantité conditionnée
 *		- cly_prd_ref : référence du conditionnement
 *		- prd_ref : référence du conditionnement (référence du produit le cas échéant)
 *		- cly_prd_barcode : code-barre du conditionnement
 *		- prd_barcode : code-barre du conditionnement (celui par défaut du produit le cas échéant)
 *		- is_default : détermine s'il s'agit du conditionnement par défaut
 *		- dps_id : identifiant du dépôt rattaché au conditionnement
 */
function prd_colisage_classify_get( $col=0, $prd=0, $qte=0, $sort=false, $correled=false, $dps_id=null, $pkg_id=null ){

	$col = control_array_integer( $col, false );
	if( $col === false ){
		return false;
	}

	$prd = control_array_integer( $prd, false );
	if( $prd === false ){
		return false;
	}

	if( $correled && sizeof($col) != sizeof($prd) ){
		return false;
	}

	if( !is_numeric($qte) || $qte < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select
			cly_col_id as col_id, col_name, prd_id, col_qte as qte, cly_prd_ref, ifnull(cly_prd_ref, prd_ref) as prd_ref,
			cly_prd_barcode, ifnull(cly_prd_barcode, prd_barcode) as prd_barcode, cly_is_default as is_default, col_dps_id as dps_id
		from
			prd_colisage_classify
			join prd_colisage_types on cly_tnt_id = col_tnt_id and cly_col_id = col_id
			join prd_products on cly_tnt_id = prd_tnt_id and cly_prd_id = prd_id
		where
			cly_tnt_id = '.$config['tnt_id'].' and prd_date_deleted is null and col_is_deleted = 0
	';

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($col); $i++ ){
			$cnds[] = 'col_id = '.$col[ $i ].' and prd_id = '.$prd[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($col) ){
			$sql .= ' and col_id in ('.implode(', ', $col).')';
		}
		if( sizeof($prd) ){
			$sql .= ' and prd_id in ('.implode(', ', $prd).')';
		}
	}

	if( $qte ){
		$sql .= ' and col_qte = '.$qte;
	}

	if( $dps_id !== null ){
		$dps_ids = control_array_integer($dps_id);
		$sql .= '
			and col_dps_id in ('.implode(',', $dps_ids).')
		';
	}

	if( $pkg_id !== null ){
		$pkg_ids = control_array_integer($pkg_id);
		$sql .= '
			and col_pkg_id in ('.implode(',', $pkg_ids).')
		';
	}

	$array_sort = array();
	if( is_array($sort) ){
		$allow_cols = array('col_id', 'prd_id', 'qte', 'col_name', 'rcol_name', 'prd_ref', 'prd_barcode', 'is_default');
		foreach( $sort as $k => $v ){
			if( in_array(strtolower(trim($k)), $allow_cols) ){
				if( $k == 'rcol_name' ){
					$array_sort[] = 'replace(CONVERT(col_name using utf8) collate utf8_general_ci, col_qte, "") '.( strtolower(trim($v)) == 'desc' ? 'desc' : 'asc' );
				}else{
					$array_sort[] = $k.' '.( strtolower(trim($v)) == 'desc' ? 'desc' : 'asc' );
				}
			}
		}
	}

	if( !sizeof($array_sort) ){
		$array_sort[] = 'col_id asc';
		$array_sort[] = 'prd_id asc';
		$array_sort[] = 'qte asc';
	}

	$sql .= ' order by '.implode(', ', $array_sort);

	return ria_mysql_query($sql);

}

// \cond onlyria
/**	Récupère l'identifiant d'un produit à partir d'une référence spécifique à un conditionnement.
 *	La référence classique ne retournera pas forcément de résultat, "cly_prd_ref" n'étant généralement renseigné que si le conditionnement est différent de l'unité
 *	@param $cly_prd_ref Référence du produit dans un conditionnement donné
 *	@return int L'identifiant du produit, ou False en cas d'échec
 */
function prd_colisage_classify_get_product( $cly_prd_ref ){
	$cly_prd_ref = trim( $cly_prd_ref );
	if( $cly_prd_ref==='' ) return false;
	$cly_prd_ref = '\''.addslashes($cly_prd_ref).'\'';

	global $config;

	$r = ria_mysql_query( '
		select prd_id
		from prd_products
			join prd_colisage_classify on prd_id=cly_prd_id and prd_tnt_id=cly_tnt_id
		where
			prd_date_deleted is null
			and cly_tnt_id='.$config['tnt_id'].'
			and cly_prd_ref='.$cly_prd_ref.'
	' );

	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}
// \endcond

// \cond onlyria
/**	Permet le test de l'existance d'un colisage pour un produit dans la base de données.
 *
 *	@param int $prd_id Obligatoire, identifiant du produit.
 *	@param $col_id Optionnel, identifiant du type de colisage.
 *
 *	@return bool true si le produit est associé à un colisage/au colisage donné, false s'il n'existe pas.
 */
function prd_colisage_classify_exists( $prd_id, $col_id=0 ){
	global $config;
	// Contrôles
	if( !is_numeric( $prd_id ) || $prd_id<=0 ) return false;
	if( !is_numeric( $col_id ) || $col_id<0 ) return false;

	// Procède au test
	$sql = '
		select cly_col_id, cly_prd_id
		from prd_colisage_classify
		where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$prd_id
	;

	if( $col_id>0 ) $sql .= ' and cly_col_id='.$col_id;

	$res = ria_mysql_query( $sql );
	if( !$res ) return false;

	return ria_mysql_num_rows( $res )>0;
}
// \endcond

// \cond onlyria
/**	Permet de savoir si la quantité du produit est un multiple du type ou de l'un des types de colisage associés.
 *
 *	@param $qte Quantité à tester.
 *	@param int $prd_id Identifiant du produit.
 *
 *	@return int L'identifiant du colisage le plus intéréssant si un multiple existe, False sinon
 *
 */
function prd_colisage_classify_is_multiple_of( $qte, $prd_id ){
	global $config;
	// Contrôles
	if( !is_numeric( $qte ) || $qte<=0 ) return false;
	if( !prd_products_exists($prd_id) ) return false;

	// Procède à la vérification
	$sql = '
		SELECT col_id
		FROM prd_colisage_classify
		JOIN prd_colisage_types ON ( col_tnt_id=cly_tnt_id AND col_id=cly_col_id )
		WHERE cly_tnt_id='.$config['tnt_id'].' AND cly_prd_id = '.$prd_id.'
		AND col_is_deleted = 0
		AND '.$qte.' MOD col_qte = 0
		ORDER BY col_qte DESC
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res, 0, 0 );
}
// \endcond

/// @}

/** \defgroup model_sell_unit Unités de vente
 *	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion de l'unité de vente des produits.
 *	Les unités de vente définissent sous quelle unité de mesure le produit est vendu (grammes, mètres, mètres², boite, ...).
 *
 *	@{
 */

 // \cond onlyria
/**	Cette fonction crée une nouvelle unité de vente.
 *	@param string $name Obligatoire, nom de l'unité de vente. Ne peut pas être vide et doit être unique, y compris en prenant en compte les unités globales.
 *	@param string $name_pl Optionnel, nom de l'unité au pluriel.
 *	@param bool $is_sync Optionnel, détermine si l'unité est synchronisée.
 *	@param $get_exist_id Optionnel, si activé et si $name n'est pas unique, l'identifiant existant sera retourné.
 *
 *	@return int L'identifiant généré ou récupéré en cas de succès, False en cas d'échec.
 *
 */
function prd_sell_units_add( $name, $name_pl=null, $is_sync=false, $get_exist_id=false ){

	// détecte un doublon sur le nom
	$rdata = prd_sell_units_get( 0, $name );
	if( !$rdata ){
		return false;
	}
	if( ria_mysql_num_rows($rdata) ){
		if( $get_exist_id ){
			$data = ria_mysql_fetch_assoc($name);
			return $data['id'];
		}else{
			return false;
		}
	}

	$name = trim($name);
	if( !$name ){
		return false;
	}

	// le pluriel est optionnel
	if( $name_pl !== null ){
		$name_pl = trim($name_pl);
		if( !$name_pl ){
			$name_pl = null;
		}
	}

	$is_sync = $is_sync ? 1 : 0;

	global $config;

	$fields = array( 'sun_tnt_id, sun_name, sun_date_created', 'sun_is_sync' );
	$values = array( $config['tnt_id'], '"'.addslashes($name).'"', 'now()', $is_sync );

	if( $name_pl !== null ){
		$fields[] = 'sun_name_pl';
		$values[] = '"'.addslashes($name_pl).'"';
	}

	$sql = 'insert into prd_sell_units ('.implode(', ', $fields).') values ('.implode(', ', $values).')';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour une unité de vente existante, et spécifique au locataire courant.
 *	@param int $id Obligatoire, identifiant de l'unité de vente.
 *	@param string $name Obligatoire, nom de l'unité de vente. Ne peut pas être vide et doit être unique, y compris en prenant en compte les unités globales.
 *	@param string $name_pl Obligatoire, nom de l'unité au pluriel. Null est une valeur autorisée.
 *
 *	@return bool True en cas de succès, False en d'échec.
 *
 */
function prd_sell_units_upd( $id, $name, $name_pl ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	// détecte l'existence de l'unité
	$rdata = prd_sell_units_get( $id );
	if( !$rdata || !ria_mysql_num_rows($rdata) ){
		return false;
	}
	$data = ria_mysql_fetch_assoc($rdata);

	// détecte si l'unité est bien spécifique au locataire courant
	if( !$data['is_tenant'] ){
		return false;
	}

	$name = trim($name);
	if( !$name ){
		return false;
	}

	// détecte un doublon sur le nom
	$rdata = prd_sell_units_get( 0, $name );
	if( !$rdata ){
		return false;
	}
	if( ria_mysql_num_rows($rdata) ){
		$data = ria_mysql_fetch_assoc($rdata);
		// ne sort à False que s'il s'agit d'une autre unité
		if( $data['id'] != $id ){
			return false;
		}
	}

	// le nom pluriel est optionnel
	if( $name_pl !== null ){
		$name_pl = trim($name_pl);
		if( !$name_pl ){
			$name_pl = null;
		}
	}

	global $config;

	$sql = '
		update prd_sell_units
		set
			sun_name = "'.addslashes($name).'",
			sun_name_pl = '.( $name_pl !== null ? '"'.addslashes($name_pl).'"' : 'NULL' ).'
		where
			sun_tnt_id = '.$config['tnt_id'].' and sun_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction supprime une unité de vente. La suppression est virtuelle.
 *
 *	@param int $id Obligatoire, identifiant de l'unité de vente.
 *	@param $set_prd_null Optionnel, détermine le comportement si des produits sont rattachés à l'unité :
 *		- False empêchera la suppression.
 *		- True retirera l'unité sur les produits avant de la supprimer.
 *	@param $no_error_tnt Optionnel, détermine le comportement si l'unité de vente n'est pas spécifique au locataire :
 *		- False empêchera la suppression et retournera "False" quoiqu'il arrive.
 *		- True retournera "True" sans supprimer réellement (si aucun produit rattaché pour le locataire courant ou si $set_prd_null activé).
 *	@param bool $from_sync Optionnel, indique si la demande provient ou non de la synchronisation. Cette valeur doit correspondre au champ "is_sync" de l'unité à supprimer.
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 *
 */
function prd_sell_units_del( $id, $set_prd_null=false, $no_error_tnt=false, $from_sync=false ){

	if( !is_numeric($id) || $id <= 0){
		return false;
	}

	// détecte l'existence de l'unité
	$rdata = prd_sell_units_get( $id );
	if( !$rdata || !ria_mysql_num_rows($rdata) ){
		return false;
	}
	$data = ria_mysql_fetch_assoc($rdata);

	// égalité du marqueur de synchro
	$from_sync = $from_sync ? 1 : 0;
	if( $from_sync != $data['is_sync'] ){
		return false;
	}

	// détecte si l'unité à supprimer est bien spécifique au locataire courant
	if( !$no_error_tnt && !$data['is_tenant'] ){
		return false;
	}

	// détecte la présence de produits toujours rattachés à l'unité
	$rprd = prd_products_get_simple( 0, '', false, 0, false, false, false, false, array('childs' => true, 'sell_unit' => $id) );
	if( !$rprd ){
		return false;
	}

	if( ria_mysql_num_rows($rprd) ){
		if( $set_prd_null ){
			// met à jour l'unité de vente des produits concernés
			prd_products_set_sell_unit( 0, null, $id );
		}else{
			return false;
		}
	}

	// détecte si l'unité à supprimer est bien spécifique au locataire courant
	if( $no_error_tnt && !$data['is_tenant'] ){
		return true;
	}

	global $config;

	$sql = '
		update prd_sell_units
		set
			sun_is_deleted = 1, sun_date_deleted = now()
		where
			sun_tnt_id = '.$config['tnt_id'].' and sun_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction teste l'existence d'une unité de vente.
 *	@param int $id Obligatoire, identifiant de l'unité à tester.
 *	@param $is_tenant Optionnel, détermine la relation avec le locataire : spécifique (True), générique (False) ou indifférent (Null).
 *
 *	@return bool True si l'unité existe, False sinon.
 */
function prd_sell_units_exists( $id, $is_tenant=null ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$ar_tnt = array();
	if( $is_tenant === null ){
		$ar_tnt[] = 0;
		$ar_tnt[] = $config['tnt_id'];
	}elseif( $is_tenant ){
		$ar_tnt[] = $config['tnt_id'];
	}else{
		$ar_tnt[] = 0;
	}

	$sql = '
		select sun_id
		from prd_sell_units
		where
			sun_tnt_id in ('.implode(', ', $ar_tnt).')
			and sun_id = '.$id.'
			and sun_is_deleted = 0
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}
// \endcond

/**	Cette fonction récupère des unités de vente selon des paramètres optionnels.
 *	@param int $id Optionnel, identifiant de l'unité de vente, ou tableau d'identifiants.
 *	@param string $name Optionnel, nom de l'unité de vente (comparaison insensible à la casse).
 *	@param $is_tenant Optionnel, détermine la relation avec le locataire : spécifique (True), générique (False) ou indifférent (Null).
 *	@param $get_count_prd Optionnel, détermine si un colonne contenant le nombre de produits liés à l'unité doit être retournée.
 *	@param int $prd Optionnel, permet de récupérer des unités rattachées à des produits spécifiques.
 *	@param $sync Optionnel, permet de récupérer les unité synchronisées, ou l'inverse, ou les deux.
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivante :
 *		- is_tenant : détermine si l'unité est spécifique au locataire.
 *		- id : identifiant de l'unité.
 *		- name : nom de l'unité.
 *		- name_pl : nom au pluriel de l'unité.
 *		- date_created : date de création de l'unité.
 *		- date_created_en : date de création au format EN.
 *		- date_modified : date de dernière modification de l'unité.
 *		- date_modified_en : date de dernière modification au format EN.
 *		- count_prd : Nombre de produits liés à l'unité (si $get_count_prd est activé uniquement).
 *		- is_sync : marqueur de synchronisation (1 / 0).
 */
function prd_sell_units_get( $id=0, $name='', $is_tenant=null, $get_count_prd=false, $prd=0, $sync=null ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	$prd = control_array_integer( $prd, false );
	if( $prd === false ){
		return false;
	}

	$name = trim($name);

	global $config;

	$tnt_ar = array();
	if( $is_tenant === null ){
		$tnt_ar[] = 0;
		$tnt_ar[] = $config['tnt_id'];
	}elseif( $is_tenant ){
		$tnt_ar[] = $config['tnt_id'];
	}else{
		$tnt_ar[] = 0;
	}

	$sql = '
		select
			if(sun_tnt_id = 0, 0, 1) as is_tenant, sun_id as "id", sun_name as "name", sun_name_pl as name_pl,
			date_format(sun_date_created, "%d/%m/%Y à %H:%i") as date_created, sun_date_created as date_created_en,
			date_format(sun_date_modified, "%d/%m/%Y à %H:%i") as date_modified, sun_date_modified as date_modified_en,
			sun_is_sync as is_sync
	';

	if( $get_count_prd ){
		$sql .= ', (
			select count(*) from prd_products
			where prd_tnt_id = '.$config['tnt_id'].' and prd_sun_id = sun_id and prd_date_deleted is null
		) as count_prd';
	}

	$sql .= '
		from
			prd_sell_units
		where
			sun_tnt_id in ('.implode(', ', $tnt_ar).')
			and sun_is_deleted = 0
	';

	if( sizeof($id) ){
		$sql .= ' and sun_id in ('.implode(', ', $id).')';
	}

	if( trim($name) ){
		$sql .= ' and lower(sun_name) = lower("'.addslashes($name).'")';
	}

	if( sizeof($prd) ){
		$sql .= ' and sun_id in (
			select prd_sun_id from prd_products
			where prd_tnt_id = '.$config['tnt_id'].' and prd_id in ('.implode(', ', $prd).')
		)';
	}

	if( $sync !== null ){
		if( $sync ){
			$sql .= ' and sun_is_sync = 1';
		}else{
			$sql .= ' and sun_is_sync = 0';
		}
	}

	return ria_mysql_query($sql);

}

// \cond onlyria
/**	Cette fonction récupère l'unité de vente la plus représentée d'une catégorie donnée.
 *	@param int $cat_id Identifiant de la catégorie.
 *	@param bool $catchilds Optionnel, calcul sur les catégories enfants oui/non.
 *	@return bool False en cas d'échec.
 *	@return array Un tableau associatif comprenant les clés suivantes :
 *		- id : identifiant de l'unité.
 *		- name : nom de l'unité.
 *		- name_pl : nom au pluriel de l'unité.
 */
function prd_sell_units_get_from_category( $cat_id, $catchilds=false ){

	if( !prd_categories_exists( $cat_id ) ){
		return false;
	}

	global $config;

	$sql = '
		select sun_id as "id", sun_name as "name", sun_name_pl as name_pl
		from prd_sell_units
			join prd_products on '.$config['tnt_id'].' = prd_tnt_id and sun_id = prd_sun_id
			join prd_classify on prd_tnt_id = cly_tnt_id and prd_id = cly_prd_id
	';
	if( $catchilds ){
		$sql .= '
			left join prd_cat_hierarchy on cly_tnt_id = cat_tnt_id and cly_cat_id = cat_child_id
		';
	}
	$sql .= '
		where sun_tnt_id in (0, '.$config['tnt_id'].')
			and sun_is_deleted = 0
			and prd_date_deleted is null
			and (
				cly_cat_id = '.$cat_id.'
	';
	if( $catchilds ){
		$sql .= '
				or cat_parent_id = '.$cat_id.'
		';
	}
	$sql .= '
			)
		group by sun_id, sun_name, sun_name_pl
		order by count(*) desc
	';

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_fetch_assoc($res);

}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si une unité de vente donnée est spécifique au locataire ou générique.
 *	@param int $id Obligatoire, identifiant de l'unité à tester.
 *	@return bool True si elle est spécifique au locataire, False sinon.
 */
function prd_sell_units_is_tenant_linked( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$rdata = prd_sell_units_get( $id );
	if( !$rdata || !ria_mysql_num_rows($rdata) ){
		return false;
	}
	$data = ria_mysql_fetch_assoc($rdata);

	return $data['is_tenant'];

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la date de dernière modification d'une unité de vente.
 *	@param int $id Identifiant de l'unité de vente.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_sell_units_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !prd_sell_units_is_tenant_linked( $id ) ){
		return false;
	}

	global $config;

	$sql = '
		update prd_sell_units
		set sun_date_modified = now()
		where sun_tnt_id = '.$config['tnt_id'].' and sun_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

/** Cette fonction permet de récupérer l'intitulé d'une unité de vente
 *	@param $sun_id Identifiant d'une unité de vente
 *	@return L'intitulé si cette unité de vente existe, False dans le cas contraire
 */
function prd_sell_units_get_name( $sun_id ){
	if (!is_numeric($sun_id) || $sun_id <= 0) {
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select sun_name
		from prd_sell_units
		where sun_tnt_id = '.$config['tnt_id'].'
			and sun_id = '.$sun_id.'
			and sun_is_deleted = 0
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['sun_name'];
}

/// @}


