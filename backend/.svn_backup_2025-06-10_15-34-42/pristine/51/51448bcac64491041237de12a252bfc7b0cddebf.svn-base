<?php

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	require_once('products.inc.php');
	require_once('comparators.inc.php');
	require_once('view.ctr.inc.php');

	if( !isset($_GET['ctr'], $_GET['prd']) || !ctr_comparators_exists($_GET['ctr']) || !prd_products_exists($_GET['prd']) ){
		$g_error = _("Une erreur inattendue s'est produite lors du chargement des informations.");
	}

	if( isset($g_error) ){ 
		print '
			<div class="error">'.nl2br( $g_error ).'</div>
		';
	}else{
		$ctr = ria_mysql_fetch_array( ctr_comparators_get($_GET['ctr'], false, false, null) );
		$prd = ria_mysql_fetch_array( prd_products_get_simple($_GET['prd']) );
		
		$is_market = $ctr['marketplace'] ? true : false;
		
		if( isset($_POST['save']) ){
			if( !isset($_POST['prd_title'], $_POST['prd_desc']) ){
				$error = _("Une ou plusieurs informations sont manquantes.");
			}else{
				$rmdl = ctr_models_get( 0, $prd['id'], $is_market );

				$mdl_title = $mdl_desc = '';
				if( $rmdl && ria_mysql_num_rows($rmdl) ){
					$mdl = ria_mysql_fetch_array( $rmdl );

					$mdl_title = $mdl['prd_title'];
					$mdl_desc  = $mdl['prd_desc'];
				}

				$_POST['prd_title'] = $_POST['prd_title']==$mdl_title ? '' : $_POST['prd_title'];
				$_POST['prd_desc']  =  $_POST['prd_desc']==$mdl_desc ? '' : $_POST['prd_desc'];

				if( $ctr['max_title']>0 && strlen($_POST['prd_title'])>$ctr['max_title']){
					$error = sprintf(_("Le nombre de caractère dans le titre est limité à %d."), number_format( $ctr['max_title'], 0, '', ' ' ));
				}elseif( $ctr['max_desc']>0 && strlen($_POST['prd_desc'])>$ctr['max_desc'] ){
					$error = sprintf(_("Le nombre de caractère dans la description est limité à %d."), number_format( $ctr['max_desc'], 0, '', ' '));
				}elseif( !ctr_catalogs_set_prd_title($_GET['ctr'], $_GET['prd'], $_POST['prd_title']) || !ctr_catalogs_set_prd_desc($_GET['ctr'], $_GET['prd'], $_POST['prd_desc']) ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la surcharge des informations.")."\n"._("Merci de prendre contact pour nous signaler le problème");
				}else{
					$success = true;
				}
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Surcharge des informations exportées') . ' - ' . _('Catalogue'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){ 
		print '
			<div class="error">'.nl2br( $g_error ).'</div>
		';
	}else{
		$prd_title = ctr_catalogs_get_prd_title( $_GET['ctr'], $_GET['prd'] );
		$prd_desc  = ctr_catalogs_get_prd_desc( $_GET['ctr'], $_GET['prd'] );

		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}elseif(isset($success) ){
			print '<div class="success">'._('Les informations ont bien été sauvegardées.').'</div>';
			
			$data = view_prd_export_data( $_GET['ctr'], $_GET['prd'] );
			print '
				<script><!--
					parent.changeInfosData( '.$_GET['ctr'].', '.$_GET['prd'].', "'.str_replace( '"', '\"', trim(preg_replace('/[\r\n\t]+/i', ' ', $data)) ).'");
				--></script>
			';
		}

		print '
			<p class="notice">'.sprintf(_('Vous pouvez à partir d\'ici surcharger les informations qui seront exportées vers %s'), $is_market ? _('la place de marché') : _('le comparateur de prix') ).' '.htmlspecialchars( $ctr['name'] ).'.</p>

			<form action="/admin/catalog/popup-edit-ctrmarket.php?ctr='.$_GET['ctr'].'&amp;prd='.$_GET['prd'].'" method="post">
				<table class="tb-popup-edit-ctrmarket">
					<caption>'._('Surcharge').'</caption>
					<tbody>
						<tr>
							<td class="col300px">
								<label for="prd_title">'._('Titre utilisé lors de l\'export').' :</label>
								'.( $ctr['max_title']>0 ? '<br /><sub>'.number_format( $ctr['max_title'], 0, '', ' ' ).' '._('caractères maximum').'</sub>' : '' ).'
							</td>
							<td>
								<input class="text" type="text" name="prd_title" id="prd_title" '.( $ctr['max_title']>0 ? 'maxlength="'.$ctr['max_title'].'"' : '' ).' value="'.htmlspecialchars( $prd_title ).'" />
							</td>
						</tr>
						<tr>
							<td>
								<label for="prd_desc">'._('Description utilisée lors de l\'export').' :</label>
								'.( $ctr['max_desc']>0 ? '<br /><sub>'.number_format( $ctr['max_desc'], 0, '', ' ' ).' '._('caractères maximum').'</sub>' : '' ).'
							</td>
							<td>
								<textarea rows="10" cols="50" id="prd_desc" name="prd_desc" '.( $ctr['max_desc']>0 ? 'maxlength="'.$ctr['max_desc'].'"' : '' ).'>'.htmlspecialchars( $prd_desc ).'</textarea>
							</td>
						</tr>
						'.view_admin_img_table( CLS_CTR_MKT, array($_GET['prd'], $_GET['ctr']), false, false, true ).'
					</tbody>
					<tfoot>
						<tr>
							<td colspan="2" class="right">
								<input type="submit" name="pop-ctr-addimg" value="'._('Ajouter des images').'" data-ctr-id="'.$_GET['ctr'].'" data-prd-id="'.$_GET['prd'].'" />
								<input type="submit" name="pop-ctr-delimg" value="'._('Supprimer les images').'" title="'._('Supprimer les images sélectionnées').'" />
								<input type="submit" value="'._('Enregistrer').'" name="save" />
							</td>
						</tr>
					</tfoot>
				</table>
			</form>
		';
	}

	require_once('admin/skin/footer.inc.php');

