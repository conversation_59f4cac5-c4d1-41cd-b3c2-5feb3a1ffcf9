/**	\file metas.js
 *	Permet d'ajouter des alertes à l'utilisateur lors de la saisie dans les champs dédiés au référencement naturel :
 *	- textarea#tag_title : balise Title de la page
 *	- textarea#tag_desc : balise Meta Description de la page
 */
(function($) {
	/**
	 *	Constructeur de l'outil
	 */
	$.fn.riametas = function( options )
	{
		var tmp = this;
		if( tmp.length > 0 || $(tmp).attr('readonly') || (typeof tmp.params != 'undefined' && tmp.params.force)  ){
			tmp.params = $.extend(this, $.fn.riametas.defaults, options);

			if( tmp.params.padding )
				$(tmp).wrapAll('<div class="riametasBox_wrap padding"/>');
			else
				$(tmp).wrapAll('<div class="riametasBox_wrap"/>');
			
			tmp.refreshMetaBoxInfo( );
			
			this.keyup(function(){
				tmp.refreshMetaBoxInfo( );
			});
		}
	};
	
	/**
	*	Permet l'affichage à l'utilisateur de l'infobulle
	*/
	$.fn.refreshMetaBoxInfo = function( ){
		var element = this;
		var message = element.getMetaMessage(); 
		var longueur = element.getMetaLength();
				
		$('#riametasBox_'+ $(element).attr('id') ).remove();
		
		if( longueur >= 0 || $(element).attr('readonly') || element.params.force ){
			$(element).after('<div id="riametasBox_'+ $(element).attr('id') +'" class="riametasbox '+message.css_class+'">'
						+ '		<div class="box-arrow"></div>'
						+ '		<div class="box-content">'
						+ '			<div class="box-content-open">'
						+ '				<p><strong>' + metasNbCaracteres + ' : </strong> ' + longueur + '/' + element.params[ element.params.type ].normal.max+ '</p>'
						+ '				<p><strong>' + metasNbMots + ' : </strong> ' + element.getMetaWords() + '</p>'
						+ '				<p><strong>' + metasInformations + ' : </strong>' + message.msg + '</p>'
						+ '			</div>'
						+ '			<div class="box-content-close">'
						+ '				<p>...</p>'
						+ '			</div>'
						+ '		</div>'
						+'</div>');
						
			
			element.displayBox();
			
			$('#riametasBox_'+ $(element).attr('id')+' .box-content' ).click(function(){
				element.params.open = !element.params.open;
				element.displayBox();
			});

			if( element.params.showFocus ){
				$('#'+ $(element).attr('id')).focus(
					function(){
						element.params.open = true;
						element.displayBox();
					}
				).blur(
					function(){
						element.params.open = false;
						element.displayBox();
					}
				);
			}
		}
	}
	
	/**
	*	Compte le nombre de caractère dans le champs
	*/
	$.fn.getMetaLength = function(){
		
		return trim($(this).val()).length;
	
	}
	
	/**
	*	Affiche ou cache le contenu
	*/
	$.fn.displayBox = function(){
		
		if( this.params.open ){
			$('#riametasBox_'+ $(this).attr('id')).addClass('open');
			$('#riametasBox_'+ $(this).attr('id')+' .box-content-open' ).show();
			$('#riametasBox_'+ $(this).attr('id')+' .box-content-close' ).hide();
		 }else{
			$('#riametasBox_'+ $(this).attr('id')).removeClass('open');
			$('#riametasBox_'+ $(this).attr('id')+' .box-content-open' ).hide();
			$('#riametasBox_'+ $(this).attr('id')+' .box-content-close' ).show();
		}
		
	}
	
	/**
	*	Compte le nombre de mots dans le champ
	*/
	$.fn.getMetaWords = function(){
		
		var exp = new RegExp("[a-zA-Z0-9éèêëàáâäóòôöíìîïçÉÈÊËÀÁÂÄÒÓÔÖÌÍÎÏÇ-]+","g");
		var match = trim( $(this).val() ).match(exp);
		if( match )
			return match.length;
		else return 0;
		
	}

	/**
	*	Affiche le message pour la description en fonction du nombre de caractère
	*/
	$.fn.getMetaMessage = function(){
		var current_length = this.getMetaLength();
		var retourne = { msg : "", css_class : "meta-note"};
		
		if(  current_length < this.params[ this.params.type ].short.max ){
			retourne.msg = this.params[ this.params.type ].short.msg;
			retourne.css_class = "meta-note";
		}
		else if(  current_length <= this.params[ this.params.type ].normal.max ){
			retourne.msg = this.params[ this.params.type ].normal.msg;
			retourne.css_class = "meta-success";
		}
		else{
			retourne.msg = this.params[ this.params.type ].long.msg;
			retourne.css_class = "meta-note";
		}
		
		return retourne;
	}
	
	function trim( string ){
		return string.replace(/^\s+/g,'').replace(/\s+$/g,'')
	} 
	
	/**
	 *	variable par défault
	 */
	$.fn.riametas.defaults = {
		type 	   : "title",
		open 	   : false,
		padding    : false,
		force      : false,
		showFocus  : false,
		title	   : { 
						short  :{ msg : metasTitreCourt, 				max : 30  		}, 
						normal :{ msg : metasTitreLongeurCorrect,    	max : 70  		}, 
						long   :{ msg : metasTitreLong,      		    				} 
					 },
		desc	   : { 
						short  :{ msg : metasDescriptionCourt, 				max : 70      	}, 
						normal :{ msg : metasDescriptionLongeurCorrect,   	max : 156     	}, 
						long   :{ msg : metasDescriptionLong,    							} 
					 },
		keywords   : { 
						short  :{ msg : metasMotCleCourt, 								max : 50  		}, 
						normal :{ msg : metasMotCleLongeurCorrect,    					max : 1000  	}, 
						long   :{ msg : metasMotCleLong,     		 		    						} 
					 }
	};
	

})(jQuery);

$(document).ready(
	function(){

		// Onglet Référencement, balise Title
		$('textarea#tag_title').bind('input propertychange', function() {
			const value = $('select#select_title').val();
			if (value > 0){
				$('select#select_title option').each(function(){
					$(this).prop('selected', false);
				});
				$('select#select_title option#no_title').prop('selected', true);
				if ($('input#previous_title_text').length){
					$('input#previous_title_text').remove();
				}
			}
		});

		// Onglet Référencement, balise Meta Description
		$('textarea#tag_desc').bind('input propertychange', function() {
			const value = $('select#select_desc').val();
			if (value > 0){
				$('select#select_desc option').each(function(){
					$(this).prop('selected', false);
				});
				$('select#select_desc option#no_desc').prop('selected', true);
				if ($('input#previous_desc_text').length){
					$('input#previous_desc_text').remove();
				}
			}
		});

	}
).delegate( // Balises Meta : Title
	'select#select_title', 'change', function(){
		const value = $('select#select_title').val();
		if (value > 0){
			const previous_text = $("textarea#tag_title").val();
			if (!$('input#previous_title_text').length){
				$("select#select_title").before('<input type="hidden" id="previous_title_text" name="previous_title_text" value="'+previous_text+'"/>');
			}
			$("textarea#tag_title").val($('select#select_title option#title-'+value).text()).trigger('keyup');
		}
		if (value == 0 && $('input#previous_title_text').length){
			$("textarea#tag_title").val($('input#previous_title_text').val()).trigger('keyup');
			$('input#previous_title_text').remove();
		}
		if (value == -1 && $('input#original_title_text').length){
			$("textarea#tag_title").val($('input#original_title_text').val()).trigger('keyup');
			if ($('input#previous_title_text').length) {
				$('input#previous_title_text').remove();
			}
		}
	}
).delegate( // Balises Meta : Description
	'select#select_desc', 'change', function(){
		const value = $('select#select_desc').val();
		if (value > 0){
			const previous_text = $("textarea#tag_desc").val();
			if (!$('input#previous_desc_text').length){
				$("select#select_desc").before('<input type="hidden" id="previous_desc_text" name="previous_desc_text" value="'+previous_text+'"/>');
			}
			$("textarea#tag_desc").val($('select#select_desc option#desc-'+value).text()).trigger('keyup');
		}
		if (value == 0 && $('input#previous_desc_text').length){
			$("textarea#tag_desc").val($('input#previous_desc_text').val()).trigger('keyup');
			$('input#previous_desc_text').remove();
		}
		if (value == -1 && $('input#original_desc_text').length){
			$("textarea#tag_desc").val($('input#original_desc_text').val()).trigger('keyup');
			if ($('input#previous_desc_text').length) {
				$('input#previous_desc_text').remove();
			}
		}
	}
);
