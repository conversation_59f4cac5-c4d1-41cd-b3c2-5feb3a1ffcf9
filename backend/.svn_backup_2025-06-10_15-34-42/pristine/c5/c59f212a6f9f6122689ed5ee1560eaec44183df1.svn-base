<?php
require_once 'Services/Service.class.php';

/**	\brief Cette classe permet de construire un tableau de champs de formulaire, classés suivant des rangées
 */
final class FormService extends Service {

	/**
	 * Tableau de tous les champs du formulaire (non classés)
	 */
	protected $fields = [];

	/**
	 * Tableau de tous les identifiants des champs (non classés)
	 */
	protected $field_ids = [];

	/**
	 * Nombre de champs
	 */
	protected $fields_length = 0;

	/**
	 * Tableau trié final contenant les champs du formulaire classés dans des rangées
	 */
	protected $rows = [];

	/**
	 * Nombre de rangées
	 */
	protected $rows_length = 0;


	/**
	 * Ajoute un champ au formulaire
	 * @param	string		$name	Obligatoire, Nom du champ
	 * @param	int|string	$row	Obligatoire, Identifiant de la rangée dans laquelle classer le champ
	 * @param	array		$data	Obligatoire, Tableau des données du champ
	 * @return 	object		L'objet en cours
	 */
	public function addField($name, $row, $data=[]){

		if( !$this->isValidFieldName($name) ){
			throw new Exception('Le nom du champ n\'est pas un nom valide.');
		}

		if( array_key_exists($name, $this->fields) ){
			throw new Exception('Le nom du champ existe déjà.');
		}

		if( !$this->isValidRowName($row) ){
			throw new Exception('L\'identifiant de la rangée n\'est pas un identifiant valide.');
		}

		if( !is_array($data) ){
			$data = [];
		}

		if( !array_key_exists($row, $this->rows) ){
			$this->rows[$row] = [
				'title'		=> false,
				'desc'		=> false,
				'fields'	=> []
			];
			$this->rows_length++;
		}

		$types = [
			'hidden',
			'text',
			'radio',
			'tel',
			'number',
			'checkbox',
			'select',
			'textarea',
			'file',
			'password',
			'image',
			'color',
			'url',
			'search',
			'email',
			'countries',

			'reset',
			'gender',

			'date',
			'month',
			'week',

		];

		$field = [
			'type'			=> $this->arrayGet($data, 'type', $types, 'text'),
			'label'			=> $this->arrayGet($data, 'label', false, ''),
			'desc'			=> $this->arrayGet($data, 'desc', false, ''),
			'disabled'		=> $this->arrayGet($data, 'disabled', false, false),
			'required'		=> $this->arrayGet($data, 'required', false, false),
			'placeholder'	=> $this->arrayGet($data, 'placeholder', false, ''),
			'value'			=> $this->arrayGet($data, 'value', false, ''),
			'class'			=> $this->arrayGet($data, 'class', false, ''),
			'related'		=> $this->arrayGet($data, 'related', false, ''),
		];

		if( isset($data['hide_label']) && is_bool($data['hide_label']) ){
			$field['hide_label'] = $data['hide_label'];
		}

		switch($field['type']){

			case 'gender':
				$options = [];
				$titles = gu_titles_get();

				while($title = ria_mysql_fetch_assoc($titles)){
					$options[$title['id']] = [
						'id'	=> $title['id'],
						'label'	=> $title['name'],
						'abr'	=> $title['abr']
					];
				}
				$field['options'] = $options;
				break;

			case 'select':
			case 'checkbox':
			case 'radio':
			case 'countries':
				$options = $this->arrayGet($data, 'options', false, []);
				$field['options'] = !is_array($options) ? [] : $options;

				if( $field['type'] === 'countries' && !count($options) ){
					$field['options'] = Template::countries();
				}
				break;
		}

		$this->rows[$row]['fields'][$name] = $field;
		$this->fields[$name] = $field;
		$this->field_ids[] = $name;
		$this->fields_length++;

		return $this;

	}

	/**
	 * Retourne des données d'un champ du formulaire
	 * @param	string	$name	Obligatoire, Nom du champ
	 * @return	mixed	Tableau du champ, false si le champ n'existe pas
	 */
	public function getField($name){

		return isset($this->fields[$name]) ? $this->fields[$name] : false;

	}

	/**
	 * Vérifie si le nom du champ est valide
	 * @param	string	$name	Obligatoire, chaine de caractères à tester
	 * @return	bool	True si la chaine de caractères est un nom de champ valide, false sinon
	 */
	private function isValidFieldName($name){
		return $this->isValidName($name);

	}

	/**
	 * Vérifie si le nom de la rangée est valide
	 * @param	string	$name	Obligatoire, chaine de caractères à tester
	 * @return	bool	True si la chaine de caractères est un nom de rangée valide, false sinon
	 */
	private function isValidRowName($name){
		return $this->isValidName($name);

	}

	/**
	 * Vérifie qu'une chaine de caractères est au bon format
	 * @param	string	$str	Obligatoire, chaine de caractères à tester
	 * @return	bool	True si la chaine de caractères est au bon format, false sinon
	 */
	private function isValidName($str){
		return is_string($str) && preg_match('/^[a-z]+[a-z0-9\-_]*/i', $str);

	}

	/**
	 * Vérifie et retourne la valeur dans un tableau
	 * @param	array	$data		Obligatoire, Tableau clé => valeur
	 * @param	string	$key		Obligatoire, clé à tester
	 * @param	mixed	$value		Optionnel, tableau de valeurs acceptées
	 * @param	mixed	$default	optionnel, Valeur par défaut
	 * @return	mixed	Retourne la valeur du tableau ou la valeur par defaut
	 */
	private function arrayGet($data, $key, $values=false, $default=''){

		if( !is_string($key) && !is_integer($key) ){
			return $default;
		}

		if( !array_key_exists($key, $data) ){
			return $default;
		}
		$value = $data[$key];

		if( !is_array($values) || !count($values) ){
			return $value;
		}

		foreach($values as $test){

			if( $test == $value ){
				return $test;
			}

		}
		return $default;

	}

	/**
	 * Définit le titre d'une rangée
	 * @param	string	$row	Obligatoire, Identifiant de la rangée
	 * @param	string 	$title	Obligatoire, Titre de la rangée
	 * @return	object	L'objet en cours
	 */
	public function defineRowTitle($row, $title){

		if( !is_string($row) || !array_key_exists($row, $this->rows) ){
			throw new Exception('L\'identifiant de la rangée n\'existe pas ou plus.');
		}

		if( !is_string($title) ){
			throw new Exception('Le titre de la rangée doit être une chaîne de caractère.');
		}
		$this->rows[$row]['title'] = strip_tags($title, '<br>');
		return $this;

	}

	/**
	 * Définit la description d'une rangée
	 * @param	string	$row	Obligatoire, Identifiant de la rangée
	 * @param	string 	$desc	Obligatoire, Description de la rangée
	 * @return	object	L'objet en cours
	 */
	public function defineRowDescription($row, $desc){

		if( !is_string($row) || !array_key_exists($row, $this->rows) ){
			throw new Exception('L\'identifiant de la rangée n\'existe pas ou plus.');
		}

		if( !is_string($desc) ){
			throw new Exception('La description de la rangée doit être une chaîne de caractère.');
		}
		$this->rows[$row]['desc'] = strip_tags($desc, '<br>');
		return $this;

	}

	/**
	 * Définit la valeur d'un champ
	 * @param	string	$name	Obligatoire, Nom du champ
	 * @param	mixed	$value	Obligatoire, Valeur du champ
	 * @return	object	L'objet en cours
	 */
	public function setValue($name, $value){

		if( !$this->isValidFieldName($name) || !array_key_exists($name, $this->fields) ){
			return $this;
		}
		$this->fields[$name]['value'] = $value;

		foreach($this->rows as $krow => $row){

			if( !isset($row['fields']) || !is_array($row['fields']) || !isset($row['fields'][$name]) ){
				continue;
			}
			$this->rows[$krow]['fields'][$name]['value'] = $value;
			break;
		}
		return $this;

	}

	/**
	 * Définit les valeurs des champs
	 * @param	array	$data	Obligatoire, Tableau des données nom_du_champ => valeur
	 */
	public function setValues($data){

		if( !is_array($data) || count($data) < 1 ){
			return;
		}
		foreach($data as $name => $value){
			$this->setValue($name, $value);
		}
	}

	/**
	 * Méthode permettant la vérification/ validation valeurs saisies ($_POST) dans le formulaire par rapport aux champs ($this->fields) du formulaire
	 * @param	array	$data	Obligatoire, Tableau des valeurs saisies dans le formulaire
	 * @return	array	Une exceptionne sera levée en cas d'erreur, tableau des données en cas de succès
	 */
	public function validate($data){
		$fields = $this->fields;
		$ar_data = [];

		if( !is_array($data) || !count($data) ){
			throw new Exception(i18n::get('Une erreur est survenue lors de la vérification du formulaire.', 'ERROR'), 1);
		}

		foreach($fields as $id => $field){
			$required = false;
			if( !is_array($field) || !isset($field['label']) || !isset($field['type']) ){
				continue;
			}

			if( $this->isFieldRequired($field) ){
				$required = true;

				if( !isset($data[$id]) ){
					$error = sprintf( i18n::get('Le champ "%s" est obligatoire. Veuillez vérifier votre saisie.', 'ERROR'), $field['label'] );
					throw new Exception( $error, 2 );
				}

			}elseif( !isset($data[$id]) ){
				continue;

			}

			switch($field['type']){
				case 'text':
				case 'textarea':
					$value = is_string($data[$id]) ? trim(strip_tags($data[$id])) : '';

					if( $value == '' ){
						unset($value);

						if( $required ){
							$error = sprintf( i18n::get('L\'information du champ "%s" est manquante. Veuillez vérifier votre saisie.', 'ERROR'), $field['label'] );
							throw new Exception( $error, 3 );
						}
					}
					break;
				case 'radio':
				case 'select':
					$value = is_numeric($data[$id]) ? (float)$data[$id] :(is_string($data[$id]) ? trim(strip_tags($data[$id])) : '');

					if( !isset($field['options']) && !is_array($field['options']) && !array_key_exists($value, $field['options']) ){
						unset($value);

						if( $required ){
							$error = sprintf( i18n::get('La saisie du champ "%s" n\'est pas valide. Veuillez vérifier.', 'ERROR'), $field['label'] );
							throw new Exception( $error, 4 );
						}
					}
					break;
				case 'tel':
					$value = $data[$id];

					if( !isphone($value) ){
						unset($value);

						if( $required ){
							$error = sprintf( i18n::get('Le numéro de téléphone du champ "%s" n\'est pas valide. Veuillez vérifier votre saisie.', 'ERROR'), $field['label'] );
							throw new Exception( $error, 5 );
						}
					}
					break;
				case 'number':
					$value = $data[$id];

					if( !is_numeric($value) ){
						unset($value);

						if( $required ){
							$error = sprintf( i18n::get('La saisie du champ "%s" n\'est pas valide. Veuillez vérifier.', 'ERROR'), $field['label'] );
							throw new Exception( $error, 4 );

						}
						break;
					}
					$value = (float)$value;
					break;
				case 'email':
					$value = $data[$id];

					if( !gu_valid_email($value) ){
						unset($value);

						if( $required ){
							$error = sprintf( i18n::get('L\'adresse email saisie dans le champ "%s" n\'est pas valide. Veuillez vérifier.', 'ERROR'), $field['label'] );
							throw new Exception( $error, 6 );
						}
					}
					break;
				case 'checkbox':
					$values = $data[$id];
					$value = [];

					if( is_array($values) && count($values) && isset($field['options']) && is_array($field['options']) && count($field['options']) ){

						foreach($values as $key => $val){

							if( array_key_exists($key, $field['options']) ){
								$value[$key] = $val;
							}
						}
					}

					if( !count($value) ){

						if( $required ){
							$error = sprintf( i18n::get('Veuillez vérifier la sélection "%s".', 'ERROR'), $field['label'] );
							throw new Exception( $error, 7 );
						}
						unset($value);
					}
					break;

				case 'password':
					$value = $data[$id];

					if( !gu_valid_password($value) ){
						unset($value);

						if( $required ){
							global $config;
							$error = sprintf( i18n::get($config['password_error_message'], 'ERROR'), $field['label'] );
							throw new Exception( $error, 8 );
						}
					}
					break;
				case 'file':
					// TODO
				case 'countries':
					// TODO
				case 'date':
					// TODO
			}

			if( !isset($value) ){
				continue;
			}
			$ar_data[$id] = $value;
			unset($value);
		}
		return $ar_data;

	}

	/**
	 * Méthode permettant de vérifier si un champ d'un formulaire est requis ou non
	 * @param	array	$field	Obligatoire, Tableau des données du champ
	 * @return	bool	True si le champ est requis, false sinon
	 */
	private function isFieldRequired($field){

		if( !is_array($field) || !isset($field['required']) || !is_bool($field['required']) ){
			return false;
		}
		return $field['required'];

	}

}