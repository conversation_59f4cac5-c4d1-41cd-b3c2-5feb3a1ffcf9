<?php
/** \file update-zones-irlande.php
 *	\ingroup system crontabs
 * 	Ce script est destiné à mettre à jour les zones (Régions, province, Communes, Codes postaux) pour l'irlande'.
 * 	Description du code postal de l'irlande
 * 	1 lettre 2 chiffres 
 */
set_include_path(dirname(__FILE__) . '/../include/');

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('sys.zones.inc.php');
require_once('geo-api.inc.php');

$file_text = api_geoname_get_zones('IE');
/* Identifiants de types de zones :
	*		- Code postal : 52
	*		- Ville : 39
	*/

if($file_text != ''){
	$cpt = 1;
	foreach ($file_text as $line) {
		$line = explode('	', $line);
		$ville       = $line[2];
		$zip_code    = $line[1];

		$ville = strtoupper2($ville);
		print 'Commune : '. $ville .PHP_EOL;
		$zipcode_zone = api_geogouv_add_zone(_ZONE_ZIPCODE_IRLANDE, $zip_code, $zip_code, 0,'IE');
		if (!$zipcode_zone) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération du code postal : "' . htmlspecialchars($zip_code) . '"');
			continue;
		}
		// Recherche la commune et l'a créée si elle n'existe pas
		$commune_zone = api_geogouv_add_zone(_ZONE_VILLE_IRLANDE, $ville, $ville, $zipcode_zone);
		if (!$commune_zone) {
			$error = true;
			error_log(__FILE__.':'.__LINE__.'Erreur lors de la création / récupération de la commune : "'.htmlspecialchars($zip_code.' - '.$ville).'"');
			continue;
		}
		print 'Code postal : ' . $zip_code . PHP_EOL;
		$ar_codepostal_ids[] = $zipcode_zone;
		$ar_commune_ids[] = $commune_zone;
	}

	// Deprecated des zones plus utilisées
	if (!isset($error)) {
		sys_zones_set_deprecated_out_ids(_ZONE_VILLE_IRLANDE, $ar_commune_ids);
		sys_zones_set_deprecated_out_ids(_ZONE_ZIPCODE_IRLANDE, $ar_codepostal_ids);
		// Reconstruction de la hiérarchie
		sys_zones_hierarchy_rebuild(0, "IE");
	}

}