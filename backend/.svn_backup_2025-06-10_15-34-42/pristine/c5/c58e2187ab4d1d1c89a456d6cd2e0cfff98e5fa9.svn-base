<?php
require_once 'Services/Service.class.php';

/**	\brief Cette classe permet de charger les informations sur une/des marque.s
 */
class BrandsService extends Service
{

	/**	Liste des marques
	 * @var	array|null
	 */
	protected $brands = null;

	/**	Nombre de marques
	 * @var	int
	 */
	protected $count = 0;

	/**	Tableau des attributs de requête
	 * @var	array
	 */
	private $attributes = [
		'brds'				=> 0, //<<<< Identifiant ou tableau d'identifiants de marques
		'publish'			=> true, //<<<< Par défaut toutes les marques sont récupérées, mettre true pour ne récupérer que celles qui sont publiées et false pour les autres
		'with_img'			=> false, //<<<< Si vrai, récupére les images des marques
		'cfg_img'			=> 'high', //<<<< Nom de la config image à utiliser
		'fld'				=> false, //<<<< Champ avancé sur lequel filtrer le résultat. Ce paramètre peut être un identifiant, un tableau d'identifiants ou un tableau associatif identifiant => valeur
		'or_between_val'	=> false, //<<<< Dans un contexte où $fld est un tableau associatif, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque valeur possible d'un même champ (si non spécifié, la valeur logique est ET)
		'or_between_fld'	=> false, //<<<< Dans un contexte où $fld est un tableau, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque champs (si non spécifié, la valeur logique est ET)
		'offset'			=> 0, //<<<< Ligne de départ du résultat (pour la pagination). Valeur par défaut : 0.
		'limit'				=> 0, //<<<< Nombre maximum de lignes à retourner (pour la pagination). Valeur par défaut : 0, pas de limite.

	];

	/**	Constructeur de la classe.
	 * Récupère les marques suivant les arguments passés à la méthode
	 * @param	int|array	$brds	Optionnel, Identifiant de marques ou tableau d'identifiants de marques
	 */
	public function __construct($brds = 0)
	{
		$this->setAttribute('brds', $brds);
	}
	/**	Permet de définir une valeur à un attribut
	 * @param	string	$key	Obligatoire, Nom de l'attribut
	 * @param	mixed	$value	Obligatoire, Valeur de l'attribut
	 * @return	object	L'objet en cours
	 */
	public function setAttribute($key, $value)
	{
		$this->__sanitizeAttribute($key, $value);
		return $this;
	}

	/**	La valeur d'un attribut
	 * @param	string		$key	Obligatoire, Nom de l'attribut
	 * @return	mixed|null	Valeur de l'attribut, null en cas d'erreur
	 */
	public function getAttribute($key)
	{
		if (!is_string($key)) {
			return null;
		}
		$key = trim(strtolower($key));

		if (!$key) {
			return null;
		}
		return array_key_exists($key, $this->attributes) ? $this->attributes[$key] : null;
	}

	/**	Retourne tous les attributs
	 * @return	array	Tableau attribut => valeur
	 */
	public function getAttributes()
	{
		return $this->attributes;
	}

	/**	Retourne les marques
	 * @param	bool		$force	Optionnel, True pour recharger la liste des marques
	 * @return	array|bool	Tableau des marques, false sinon
	 * @todo	fld
	 * @todo	sort
	 */
	public function getBrands($force = false)
	{

		$force = is_bool($force) ? $force : false;

		if (is_array($this->brands) && count($this->brands) && !$force) {
			return $this->brands;
		}
		$brds = control_array_integer($this->attributes['brds'], false);

		if ($brds === false) {
			return false;
		}
		$fld = $this->attributes['fld'];
		$or_between_val = $this->attributes['or_between_val'];
		$or_between_fld = $this->attributes['or_between_fld'];
		$publish = $this->attributes['publish'];
		$offset = $this->attributes['offset'];
		$limit = $this->attributes['limit'];

		$r_brands = prd_brands_get($brds, true, '', '', false, $publish, $fld, $or_between_val, $or_between_fld, false, false, false, false, false, $offset, $limit);

		if (!ria_mysql_num_rows($r_brands)) {
			return false;
		}
		$this->brands = [];
		$this->count = 0;

		while ($brd = ria_mysql_fetch_assoc($r_brands)) {
			$this->brands[] = $brd;
			$this->count++;
		}

		// Charge les images des marques
		$this->__getImages();

		return $this->brands;
	}

	/**	Charge les images des marques si nécessaire
	 * @return	object	L'objet en cours
	 */
	private function __getImages()
	{

		if (!$this->attributes['with_img'] || is_null($this->brands)) {
			return $this;
		}
		global $config;

		$thumb = $config['img_sizes'][$this->attributes['cfg_img']];
		$img_default = isset($config['default_image']) ? $config['default_image'] : 0;
		$ar_tmp = [];

		foreach ($this->brands as $brd) {
			$brd['images'] = [];

			if( isset($brd['img_id']) && is_numeric($brd['img_id']) && $brd['img_id']){
				$brd['images'][] = [
					'id'		=> $brd['img_id'],
					'alt'		=> $brd['title'],
					'url'		=> $config['img_url'] . '/' . $thumb['dir'] . '/' . $brd['img_id'] . '.' . $thumb['format'],
					'width'		=> $thumb['width'],
					'height'	=> $thumb['height']
				];
			} elseif ($img_default) {
				$brd['images'][] = [
					'id'		=> $img_default,
					'alt'		=> $brd['title'],
					'url'		=> $config['img_url'] . '/' . $thumb['dir'] . '/' . $img_default . '.' . $thumb['format'],
					'width'		=> $thumb['width'],
					'height'	=> $thumb['height']
				];
			}
			$ar_tmp[] = $brd;
		}
		$this->brands = $ar_tmp;

		return $this;
	}

	/**	Permet de controler la valeur d'un attribut et de le mettre à jour
	 * @param	string	$key	Obligatoire, Nom de l'attribut
	 * @param	mixed	$value	Obligatoire, Valeur de l'attribut
	 * @return	bool	True en cas de succes, false sinon
	 */
	private function __sanitizeAttribute($key, $value)
	{

		if (!is_string($key)) {
			return false;
		}
		global $config;

		$key = trim(strtolower($key));

		switch ($key) {

			case 'brds':
				$this->attributes[$key] = control_array_integer($value, false);
				break;

			case 'or_between_fld':
			case 'or_between_fld':
			case 'with_img':
			case 'publish':
				$this->attributes[$key] = is_bool($value) ? $value : false;
				break;

			case 'offset':
				$this->attributes[$key] = is_numeric($value) && $value > 0 ? $value : 0;
				break;

			case 'limit':
				$this->attributes[$key] = is_numeric($value) && $value > 0 ? $value : 0;
				break;

			case 'cfg_img':
				$this->attributes[$key] = is_string($value) && trim($value) != '' && array_key_exists($value, $config['img_sizes']) ? $value : 'high';
				break;

			default:
				return false;
		}
		return true;
	}
}
