# StoreLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**self** | [**\Swagger\Client\Model\LinksGetStoreLink**](LinksGetStoreLink.md) |  | [optional] 
**delete_store** | [**\Swagger\Client\Model\LinksDeleteStoreLink**](LinksDeleteStoreLink.md) |  | [optional] 
**update_store** | [**\Swagger\Client\Model\LinksUpdateStoreLink**](LinksUpdateStoreLink.md) |  | [optional] 
**shares** | [**\Swagger\Client\Model\LinksGetStoreSharesLink**](LinksGetStoreSharesLink.md) |  | [optional] 
**share** | [**\Swagger\Client\Model\LinksShareStoreLink**](LinksShareStoreLink.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


