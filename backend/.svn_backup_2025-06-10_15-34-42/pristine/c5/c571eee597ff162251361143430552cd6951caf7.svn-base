<?php
// \cond onlyria

/** \defgroup model_ria_queues Gestion des Queues de processus
 *  \ingroup system
 *	@{
 */

require_once(dirname(__FILE__).'/../vendor/autoload.php');

require_once('tenants.inc.php');
require_once('RegisterGCP.inc.php');

use Pheanstalk\Pheanstalk;

/**
 * \class RiaQueue
 * \brief Cette classe permet la gestion des queues, elle doit être utilisée partout et faire abstraction du logiciel de queue utilisé derrière.
 * 	Actuellement nous utilisons Beanstalkd.
 */
class RiaQueue
{
	// Liste des priorités possibles.
	const LOW = 'low';
	const HIGH = 'high';
	const HIGHER = 'higher';
	const STATS = 'stats';

	// Liste des types de jobs disponibles, correspond à une action de l'api module/action/method.
	const WORKER_DEVICE_NOTIFICATION_SEND = 'notifications/send/upd';
	const WORKER_DEVICE_REQUEST_SYNC = 'devices/request_sync/upd';
	const WORKER_SEARCH_HIGH = 'search/index/add';
	const WORKER_SEARCH_LOW = 'search/index/upd';
	const WORKER_SEARCH_REBUILD = 'search/rebuild/add';
	const WORKER_SYNC_SALESFORCE_ORDERS_SEND = 'sync/salesforce_orders_send/add';
	const WORKER_SYNC_SALESFORCE_LINEARS_SEND = 'sync/salesforce_linears_send/add';
	const WORKER_SYNC_SALESFORCE_SAVE_ROW = 'sync/salesforce_save_row/add';
	const WORKER_SYNC_SALESFORCE_IMG_SEND = 'sync/salesforce_img_send/add';
	const WORKER_SYNC_SALESFORCE_USERS_SEND = 'sync/salesforce_users_send/add';
	const WORKER_INVOICE_STATS_PUT = 'invoices/stats/upd'; // Statistique des facturations (UTILISER QUE POUR ZOLUX, FRANCODEX et SAINT BERNARD)
	const WORKER_USR_GEOLOC = 'addresses/geoloc/upd';
	const WORKER_IMPORT_EXEC = 'imports/index/add';
	const WORKER_EXPORT_GENERATE = 'exports/index/add';
	const WORKER_TENANT_CREATE = 'tenants/create/add'; // Création d'un nouveau tenant
	const WORKER_YUTO_ACTIVE = 'tenants/yuto/add'; // Activation de Yuto pour un tenant déjà existant
	const WORKER_BTOB_ACTIVE = 'tenants/websites/add'; // Activation d'un BtoB pour un tenant déjà existant
	const WORKER_CLASSIFY_ADD = 'products/classify/add'; // Ajout d'un ou plusieurs classements de produit
	const WORKER_CLASSIFY_DEL = 'products/classify/del'; // Suppression d'un ou plusieurs classements de produit

	private $workers_priority = array(
		self::WORKER_DEVICE_NOTIFICATION_SEND => self::HIGHER,
		self::WORKER_DEVICE_REQUEST_SYNC => self::HIGH,
		self::WORKER_SEARCH_HIGH => self::HIGH,
		self::WORKER_SEARCH_LOW => self::LOW,
		self::WORKER_SEARCH_REBUILD => self::LOW,
		self::WORKER_SYNC_SALESFORCE_ORDERS_SEND => self::HIGHER,
		self::WORKER_SYNC_SALESFORCE_USERS_SEND => self::HIGHER,
		self::WORKER_SYNC_SALESFORCE_LINEARS_SEND => self::HIGHER,
		self::WORKER_SYNC_SALESFORCE_SAVE_ROW => self::HIGH,
		self::WORKER_SYNC_SALESFORCE_IMG_SEND => self::HIGHER,
		self::WORKER_USR_GEOLOC => self::LOW,
		self::WORKER_IMPORT_EXEC => self::HIGHER,
		self::WORKER_EXPORT_GENERATE => self::HIGHER,
		self::WORKER_TENANT_CREATE  => self::HIGH,
		self::WORKER_YUTO_ACTIVE  => self::HIGH,
		self::WORKER_BTOB_ACTIVE  => self::HIGH,
		self::WORKER_CLASSIFY_ADD => self::HIGH,
		self::WORKER_CLASSIFY_DEL => self::HIGH,
		self::WORKER_INVOICE_STATS_PUT => self::STATS,
	);

	private $pheanstalk;
	private static $_instance = array();
	private $tube_server = 'localhost'; ///< serveur MySQL
	private $tube_tenant_id = 0; ///< identifiant du locataire

	/** Permet de récupére le singleton de la classe.
	 * 	@param $prefix Optionnel, serveur MySQL de données (cf. marai-xxxxxx)
	 * 	@param int $tnt_id Optionnel, identifiant du locataire (présent sur ce $prefix)
	 * 	@return object L'instance RiaQueue
	 */
	public static function getInstance($prefix=null, $tnt_id=null)
	{
		if( !isset(self::$_instance[$prefix.$tnt_id]) ){
			self::$_instance[$prefix.$tnt_id] = new RiaQueue($prefix, $tnt_id);
		}

		return self::$_instance[$prefix.$tnt_id];
	}

	/** Cette fonction est le constructeur de la classe RiaQueue. Elle peut prendre deux paramètres optionnels.
	 * 	@param $prefix Optionnel, serveur MySQL de données (cf. maria-xxxxxx)
	 * 	@param int $tnt_id Optionnel, identifiant du locataire (présent sur ce $prefix)
	 * 	@return Aucun retour n'est fait par cette fonction
	 */
	private function __construct($prefix=null, $tnt_id=null)
	{
		global $config;
		global $worker_queue;

		//
		if(isset($worker_queue))
		{
			$this->pheanstalk = new Pheanstalk( $worker_queue['server'] );
		}
		else
		{
			$this->pheanstalk = new Pheanstalk( getenv('ENVRIA_QUEUE_SERVER') );
		}

		//


		// Vérification que le service est up sur la machine.
		if( !$this->pheanstalk->getConnection()->isServiceListening() ){
			throw new Exception('Erreur de connexion au Beanstalkd.');
		}

		// Récupére l'instance sql pour créer des tubes / serveur sql.
		if( $prefix === null ){
			if(isset($worker_queue)) {
				$this->tube_server = $worker_queue['prefix'];
			}
			else{
				$this->tube_server = getenv('ENVRIA_QUEUE_PREFIX');
			}
		}else{
			$this->tube_server = $prefix;
		}

		if( is_numeric($tnt_id) && $tnt_id > 0 ){
			$this->tube_tenant_id = $tnt_id;
		}
	}

	/** Ajoute un job dans le tube.
	 *
	 * @param  $worker_type  Identifiant du worker concerné
	 * @param  $params       Tableau avec tous les paramètres à passer au worker
	 * @return void
	 */
	public function addJob($worker_type, $params = array())
	{
		if( !isset($this->workers_priority[$worker_type]) ){
			throw new Exception('La priorité pour le job '.$worker_type.' n\'est pas définie.');
		}

		global $config;

		if( !is_array($params) ){
			$params = array();
		}

		$tnt_id = $config['tnt_id'];
		if( is_numeric($this->tube_tenant_id) && $this->tube_tenant_id > 0 ){
			$tnt_id = $this->tube_tenant_id;
		}

		if( $worker_type == self::WORKER_INVOICE_STATS_PUT && !in_array($tnt_id, [1118, 1053, 268]) ){
			return;
		}

		$params["engine_directory"] = getenv('ENVRIA_ENGINE_DIRECTORY');

		$worker_params = array(
			'worker_type' => str_replace('/_(high|low)/', '', $worker_type),
			'worker_logtoken' => tnt_tenants_get_logtoken($tnt_id),
			'worker_params' => $params,
		);

		// Détermine le temps limite pour le traitement de la tâche
		$ttk = 3600; // Par défaut 1h
		if( $worker_type == self::WORKER_IMPORT_EXEC ){
			// Pour les tâches d'import cette limite passe à 3h
			$ttk = 10800;
		}

		// Ajout du job dans la queue suivant son serveur sql et sa priorité.
		$this->pheanstalk
			->useTube( $this->tube_server.'_'.$this->workers_priority[$worker_type] )
			->put( json_encode($worker_params), 1024, 0, $ttk);
	}

	/** Permet de lancer un job
	 *
	 * @param  $debug_mode Facultatif, permet d'afficher ou non les logs en sortie
	 * @return void
	 */
	private function launchWork($debug_mode = false)
	{
		// Tant que j'ai des jobs je continue.
		while( $job = $this->pheanstalk->reserve() ){
			$data = json_decode($job->getData(), true);

			$worker = new WorkerLauncher($data, $debug_mode);

			if( $worker->exec() ){
				$this->pheanstalk->delete($job);
			}else{
				$this->pheanstalk->bury($job);

				error_log('[worker erreur job en bury] :'.print_r($data, true).print_r($worker->getErrors(), true));
			}
		}
	}

	/** Permet de lancer une boucle while sur l'éxécution des jobs.
	 *	@param $priority Obligatoire, niveau de priorité de la tâche. Trois niveaux de priorité sont acceptés : RiaQueue::LOW, RiaQueue::HIGH et RiaQueue::HIGHER
	 *	@param $debug_mode Facultatif, permet d'afficher ou non les logs en sortie
	 *	@return void
	 */
	public function launchWorks($priority, $debug_mode = false)
	{
		if( !in_array($priority, array(self::LOW, self::HIGH, self::HIGHER, self::STATS)) ){
			throw new Exception('La priorité donnée n\'est pas correcte : '.$priority);
		}

		// "Watch" une queue.
		$this->pheanstalk->watch($this->tube_server.'_'.$priority);

		// Boucle inifie permettant de checker la queue en permanence.
		while( 1 ){
			try{
				$this->launchWork($debug_mode);
			}catch(Exception $e){
				if( $debug_mode ){
					print 'Crash relaunch : '.$e->getMessage().PHP_EOL;
				}
			}

			usleep(500);
		}
	}

	public function getStats($tubeName = null) {
		
		if(empty($tubeName)) {
			$tubeName = 'default';
		}

		$stats = $this->pheanstalk->statsTube($tubeName);
		$serverStats = $this->pheanstalk->stats();

		$retour = '';
		$retour .= "Jobs prêts: " . $stats['current-jobs-ready'] . "\n";
		$retour .= "Jobs réservés: " . $stats['current-jobs-reserved'] . "\n";
		$retour .= "Jobs retardés: " . $stats['current-jobs-delayed'] . "\n";
		$retour .= "Jobs enterrés: " . $stats['current-jobs-buried'] . "\n";
		$retour .= "Jobs total: " . $stats['total-jobs'] . "\n";
		$retour .= "Nombre de connexions: " . $serverStats['current-connections'] . "\n";
		$retour .= "\n";
		
		if ($stats['current-jobs-reserved'] > 0) {
		$retour .= "Il y a des jobs en cours de traitement.\n";
		} else {
		$retour .= "Aucun job en cours de traitement.\n";
		}
		
		return $retour;
	}
}

/**
 * \class WorkerLauncher
 * \brief Classe permettant le lancement des workers.
 * Elle lance les scripts dans des processus séparers pour éviter les crash ou augmentation de mémoire.
 */
class WorkerLauncher
{
	private $pheanstalk;
	private $datas = null;
	private $debug_mode = false;
	private $errors = array();

	/** Permet d'initialiser l'exec du worker.
	 *
	 * @param $datas Obligatoire, tableau des paramètres du job
	 * @param $debug_mode Facultatif, permet d'afficher ou non les logs en sortie
	 */
	public function __construct($datas, $debug_mode=false)
	{
		$this->datas = $datas;
		$this->debug_mode = $debug_mode;
	}

	/** Permet de retourner toutes les erreurs ayant au lieu dans de l'éxécution.
	 *
	 */
	public function getErrors()
	{
		return $this->errors;
	}

	/** Permet de lancer un exec du script php.
	 *
	 * @return bool True si l'execution a fini, False dans le cas contraite (les erreurs sont disponible via la fonctions getErrors())
	 */
	public function exec()
	{
		global $config;
		try{
			if( !isset($this->datas['worker_type']) ){
				throw new Exception('Job sans worker type');
			}

			if( !isset($this->datas['worker_logtoken']) ){
				throw new Exception('Job sans tnt');
			}

			$params = explode('/', $this->datas['worker_type']);

			if( count($params) !== 3 ){
				throw new Exception('Job avec une const incorrecte.');
			}

			$cli_params = '--module='.$params[0].' --action='.$params[1].' --method='.$params[2].' --logtoken='.$this->datas['worker_logtoken'].' --data='.escapeshellarg(json_encode($this->datas['worker_params']));

			if( $this->debug_mode ){
				print 'Execute : '.$cli_params.PHP_EOL ;
			}

			$api_url =  getenv('ENVRIA_ENGINE_DIRECTORY') . 'htdocs/api/index.php ';
			
			$return = shell_exec('php '.$api_url.$cli_params);

			if( $this->debug_mode ){
				print ' --- return= '.$return.PHP_EOL;
			}

			if( !$return ){
				throw new Exception("Erreur d'appel, retour vide");
			}

			$return_obj = json_decode($return);

			if(json_last_error() != JSON_ERROR_NONE) {
				throw new Exception("Erreur d'appel parse json failed : ".$return);
			}

			if( !$return_obj->result ){
				throw new Exception("Erreur d'appel : ".$return_obj->message);
			}

			return true;
		}catch(Exception $e){
			$this->errors[] = $e->getMessage().print_r($this->datas, true);

			if( $this->debug_mode ){
				print ' --- exception= '. $e->getMessage().PHP_EOL;
			}
		}

		return false;
	}
}

/// @}

// \endcond
