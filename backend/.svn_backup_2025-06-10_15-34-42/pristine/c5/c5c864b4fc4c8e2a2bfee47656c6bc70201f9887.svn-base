<?php
	/**	\defgroup paypal PayPal
	 *	\ingroup payment_external
	 *	@{
	 */

	/**
	 * \brief Cette classe gère les paiments avec PayPal.
	 *
	 * 	@method static PayPal doPayment()
	 *
	 *	Variables de config utilisées :
	 *	logo_src				:	Logo du site à afficher sur PayPal
	 *	paypal_payment_type_id	:	Identifiant de type de paiement à utiliser. (créer la ligne dans ord_payment_types, l'id et la position devraient toujours être 5)
	 *	paypal_url_callback		:	Url de retour PayPal lorsque l'utilisateur a validé son paiement (créer le fichier /cart/paypal-success.php)
	 *	paypal_version			:	Version de l'API
	 *	url_payment				:	Url de l'étape paiement du panier
	 *
	 *	Variables de config à personnaliser (pour la prod uniquement) :
	 *	paypal_user			:	Utilisateur PayPal
	 *	paypal_pwd			:	Mot de Passe PayPal
	 *	paypal_signature	:	Signature PayPal (Voir dernier point IMPORTANT pour récupérer la signature)
	 *
	 *	Lorsque l'utilisateur souhaite payer avec PayPal, il suffit d'appeler PayPal::doPayment();
	 *	L'utilisateur est alors redirigé sur le site de PayPal pour valider son paiement.
	 *	Si l'utilisateur souhaite annuler, il est redirigé sur $config[url_payment]
	 *	Lorsque le paiement est validé, l'utilisateur est redirigé sur $config[paypal_url_callback]
	 *	Sur cette page, on valide le paiement : PayPal::getPaymentResult().
	 *	Cette méthode valide définitivement la transaction et met la commande à jour.
	 *
	 *	Note : Les méthodes PayPal::doPayment et PayPal::getPaymentResult peuvent échouer et lancer une exception si une erreur se produit
	 *	Il est donc important de les englober dans un try/catch :
	 *	\code{.php}
	 *	try {
	 *		PayPal::doPayment();
	 *	}
	 *	catch (exception $e) {
	 *		// On log l'erreur
	 *		error_log('PayPal : ' . $e->getMessage());
	 *	}
	 *	\endcode
	 *
	 *	Pour simuler une commande virtuelle :
	 *	Pour acheter, vous pouvez utiliser ce compte : <EMAIL> (Mot de passe : 336572809)
	 *	Pour vous connecter à la sandbox, utiliser ce compte : <EMAIL> (Mot de passe : ria123456)
	 *	La class utilisera la sandbox ou les données réelles du client selon le contexte de développement (PaymentExternal::CONTEXT_DEV ou PaymentExternal::CONTEXT_PROD).
	 *	Surchargez $config[context] pour forcer le contexte. Sinon, la class se base sur le domaine pour décider si elle doit utiliser les données dev ou prod.
	 *
	 *	Procédure pour créer un compte test :
	 *	Pour faire des tests en dev, il faut utiliser la SandBox de PayPal : paypal.com > Développeurs > SandBox (https://developer.paypal.com/cgi-bin/devscr?cmd=_login-done&login_access=0)
	 *
	 *	On ne va pas utiliser le compte du client mais créer un compte virtuel (> Create a preconfigured account).
	 *	Choisir Website Payments Pro. PayPal nous fournit alors un compte virutel (Ex : <EMAIL>)
	 *	Dans API and Payment Card Credentials, on peut connaître les informations du compte
	 *
	 *	IMPORTANT : Inutile de modifier les données tests mais vous devez définir les bonnes variables de config pour la prod (paypal_user, paypal_pwd, paypal_signature)
	 *	Pour récupérer les données pour la prod, se connecter au compte PayPal du client
	 *	Mon compte > Plus d'options > Mes ventes > Accès à l'API Mettre à jour > Demander une signature API (à droite)
	 */

	require_once('PaymentExternal.inc.php');
	require_once('NetAffiliation.inc.php');

	class PayPal extends PaymentExternal {

		private static $Instance;

		/**
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @return Rien car il y a une redirection à la fin
		*/
		public static function doPayment(){
			return self::getInstance()->_doPayment();
		}

		/**
		 * Renvoie le singleton
		 * @return object Le singleton
		 */
		private static function getInstance(){
			if( !self::$Instance ){
				self::$Instance = new PayPal();
			}
			return self::$Instance;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	\param $nounset Optionnel, par défaut $_SESSION['ord_id'] est supprimé, mettre false pour ne pas le faire au moment de la validation du paiement
		 *	@param $control_session Optionnel, permet de contrôler des variables de session liés au paiement (désactivé sur les nouvelles installation)
		 *	@return object L'instance
		 */
		public static function getPaymentResult( $nounset=false, $control_session=true ){
			return self::getInstance()->_getPaymentResult( $nounset, $control_session );
		}

		/**
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @return Rien car il y a une redirection à la fin
		*/
		public function _doPayment(){
			$orderId = $this->getOrderId();
			$amount = $this->getOrderAmount();

			global $config;

			$domain = 'http://' . $_SERVER['HTTP_HOST'];

			$logoSearch = $domain . $config['logo_src'];
			$urlCancel = $domain . $config['url_payment'];
			$urlCallBack = $domain . $config['paypal_url_callback'].'?o='.$orderId;

			// Requête
			$params = array(
				'METHOD'		=>	'SetExpressCheckout',
				'CANCELURL'		=>	$urlCancel,
				'RETURNURL'		=>	$urlCallBack,
				'AMT'			=>	$amount,
				'CURRENCYCODE'	=>	'EUR',
				'DESC'			=>	'Commande ' . $GLOBALS['config']['site_name'] . ' (' . $orderId . ') pour un montant de ' . number_format($amount, 2, ',', ' ') . '€',
				'LOCALECODE'	=>	'FR',
				'HDRIMG'		=>	$logoSearch,
				'PAYERID'		=>	$orderId
			);

			$query = array_merge($this->getConfig(), $params);

			// Prépare la requête
			$query = $this->encodeParams($query);

			// Envoie la requête
			if ($this->getContext() === PaymentExternal::CONTEXT_DEV) {
				ob_start();
				$ch = curl_init();

				curl_setopt($ch,CURLOPT_URL, $this->getUrlAPI());
				curl_setopt($ch,CURLOPT_POSTFIELDS, $query);

				$response = curl_exec($ch);
				curl_close($ch);
				$content = ob_get_contents();
				ob_end_clean();

				$t = explode('&', $content);
				$response = array();
				foreach ($t as $value) {
					$split = explode('=', $value);
					if (count($split) == 2) {
						$response[ $split[0] ] = urldecode($split[1]);
					}
				}
			}else{
				$response = $this->send($this->getUrlAPI() . $query);

				// Récupère la réponse
				$response = $this->decodeParams($response);
			}

			// Traitement de la réponse
			$this->validResponse($response);
			if( !isset($response['TOKEN']) ){
				throw new exception('La réponse ne contient pas l\'information TOKEN ! ' . print_r($response, true));
			}

			// Mémorisation en session pour garantir l'authenticité plus tard
			$_SESSION['PayPal']['ord_id'] = $orderId;
			$_SESSION['PayPal']['TOKEN'] = $response['TOKEN'];
			$_SESSION['PayPal']['amount'] = $amount;

			header('location: ' . $this->getUrlPayment() . 'webscr&cmd=_express-checkout&token=' . $response['TOKEN']);
			exit;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	\param $nounset Optionnel, par défaut $_SESSION['ord_id'] est supprimé, mettre false pour ne pas le faire au moment de la validation du paiement
		 *	@param $control_session Optionnel, permet de contrôler des variables de session liés au paiement (désactivé sur les nouvelles installation)
		 *	@return object L'instance
		 */
		public function _getPaymentResult( $nounset=false, $control_session=true ){
			$orderId = $this->getOrderId();

			// Véfifie l'état de la commande
			$state = ord_orders_get_state($orderId);
			if( $state===false || $state >= 3 ){
				throw new exception("PayPal : La commande $orderId semble déjà avoir été traitée ! (state = $state)");
			}

			if( !isset($_GET['token']) ){
				throw new exception('$_GET[token] n\'est pas défini !');
			}
			if( !isset($_GET['PayerID']) ){
				throw new exception('$_GET[PayerID] n\'est pas défini !');
			}

			$amount = round(ord_orders_get_total_to_pay($orderId, true), 2);

			if( $control_session ){
				// Authenticité de la réponse
				if( !(isset($_SESSION['PayPal']['ord_id']) && $_SESSION['PayPal']['ord_id'] == $orderId) ){
					throw new exception('La commande ne correspond pas !');
				}
				if( !(isset($_SESSION['PayPal']['TOKEN']) && $_SESSION['PayPal']['TOKEN'] == $_GET['token']) ){
					throw new exception('Le token n\'est pas bon !');
				}

				// Vérifie que la commande n'a pas été modifiée entre temps
				if( !(isset($_SESSION['PayPal']['amount']) && $_SESSION['PayPal']['amount'] == $amount) ){
					throw new exception('Le montant de la commande a changé entre temps !');
				}
			}

			// Requête
			$params = array(
				'METHOD'		=>	'DoExpressCheckoutPayment',
				'TOKEN'			=>	htmlentities($_GET['token'], ENT_QUOTES),
				'AMT'			=>	$amount,
				'CURRENCYCODE'	=>	'EUR',
				'PAYERID'		=>	htmlentities($_GET['PayerID'], ENT_QUOTES),
				'PAYMENTACTION'	=>	'sale'
			);
			$query = array_merge($this->getConfig(), $params);

			// Prépare la requête
			$query = $this->encodeParams($query);

			// Envoie la requête
			if ($this->getContext() === PaymentExternal::CONTEXT_DEV) {
				ob_start();
				$ch = curl_init();

				curl_setopt($ch,CURLOPT_URL, $this->getUrlAPI());
				curl_setopt($ch,CURLOPT_POSTFIELDS, $query);

				$response = curl_exec($ch);
				curl_close($ch);
				$content = ob_get_contents();
				ob_end_clean();

				$t = explode('&', $content);
				$response = array();
				foreach ($t as $value) {
					$split = explode('=', $value);
					if (count($split) == 2) {
						$response[ $split[0] ] = urldecode($split[1]);
					}
				}
			}else{
				$response = $this->send($this->getUrlAPI() . $query);

				// Récupère la réponse
				$response = $this->decodeParams($response);
			}

			// Traitement de la réponse
			$this->validResponse($response);

			// Le paiement a été validé
			ord_orders_pay_type_set($orderId, $GLOBALS['config']['paypal_payment_type_id']);
			if ($state < 3) ord_orders_update_status($orderId, 3, '');

			if( $response['PAYMENTSTATUS'] == 'Completed' ){
				ord_orders_update_status($orderId, 4, '');
			}

			// Confirmation de la commande à NetAffiliation
			$affi = new NetAffiliation();
			$affi->getOnlinePaymentTag( $orderId );

			if( $nounset ){
				unset($_SESSION['PayPal']);
			}else{
				unset($_SESSION['ord_id'], $_SESSION['PayPal']);
			}

			return $this;
		}

		/**
		 * Renvoie la config de base en fonction du contexte
		 * @return array Tableau de paramètres
		 *		-	VERSION		:	La version API
		 *		-	USER		:	L'identifiant API
		 *		-	PSWD		:	Le mot de passe API
		 *		-	SIGNATURE	:	La signature API
		 */
		protected function getConfig(){
			return $this->{$this->getContext() === PaymentExternal::CONTEXT_DEV ? 'getConfigDev' : 'getConfigProd'}();
		}

		/**
		 * Renvoie la config de base pour le dev
		 * @return array Tableau de paramètres
		 *		-	VERSION		:	La version API
		 *		-	USER		:	L'identifiant API
		 *		-	PSWD		:	Le mot de passe API
		 *		-	SIGNATURE	:	La signature API
		 */
		protected function getConfigDev(){
			$res = cfg_variables_get(array('paypal_version', 'paypal_user', 'paypal_pwd', 'paypal_signature'));
			if (! ($res && ria_mysql_num_rows($res))) throw new exception('Erreur cfg_variables_get !');
			$vars = array();
			while ($dat = ria_mysql_fetch_assoc($res)) $vars[$dat['code']] = $dat;
			return array(
				'VERSION'	=>	$vars['paypal_version']['default'],
				'USER'		=>	$vars['paypal_user']['default'],
				'PWD'		=>	$vars['paypal_pwd']['default'],
				'SIGNATURE'	=>	$vars['paypal_signature']['default']
			);
		}

		/**
		 * Renvoie la config de base pour la prod
		 * @return array Tableau de paramètres
		 *		-	VERSION		:	La version API
		 *		-	USER		:	L'identifiant API
		 *		-	PSWD		:	Le mot de passe API
		 *		-	SIGNATURE	:	La signature API
		 */
		protected function getConfigProd(){
			global $config;
			return array(
				'VERSION'	=>	$config['paypal_version'],
				'USER'		=>	$config['paypal_user'],
				'PWD'		=>	$config['paypal_pwd'],
				'SIGNATURE'	=>	$config['paypal_signature']
			);
		}

		/**
		 * Renvoie l'url de l'API à utiliser, en fonction du contexte
		 * @return string L'URL
		 */
		protected function getUrlAPI(){
			return ($this->getContext() === PaymentExternal::CONTEXT_DEV) ? 'https://api-3t.sandbox.paypal.com/nvp?' : 'https://api-3t.paypal.com/nvp?';
		}

		/**
		 * Renvoie l'url de paiement en ligne à utiliser, en fonction du contexte
		 * @return string L'URL
		*/
		protected function getUrlPayment(){
			return ($this->getContext() === PaymentExternal::CONTEXT_DEV) ? 'https://www.sandbox.paypal.com/' : 'https://www.paypal.com/';
		}

		/**
		 * Valide la réponse fournie par PayPal
		 * Si la réponse n'est pas valide ou a échoué, la méthode lance une exception contenant une description de l'erreur
		 * @param $response Tableau contenant la réponse serveur
		 * @return object L'instance
		 */
		protected function validResponse( $response ){
			global $config;

			if( !isset($response['ACK']) ){
				throw new exception('[tnt-'.$config['tnt_id'].'] La réponse ne contient pas l\'information ACK !' . print_r($response, true));
			}

			if ($response['ACK'] != 'Success') {
				$e = new exception('[tnt-'.$config['tnt_id'].'] La valeur de ACK ne vaut pas Success (' . print_r($response, true) . ') !');

				if( isset($response['L_ERRORCODE0']) && $response['L_ERRORCODE0'] == 10417 ){
					$e->nolog = true;
				}

				throw $e;
			}
			return $this;
		}
	}

/// @}

