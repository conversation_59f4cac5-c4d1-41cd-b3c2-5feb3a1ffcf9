<?php

class ColissimoHttpClient {
    const MESSAGE_TYPE_ERROR = 'ERROR';

    protected static $parameters = array(
        'contractNumber' => array(
            /* Il s'agit du numéro de client sur 6 caractères fourni par La Poste
            - Colissimo à l'ouverture de votre compte client.
            */
            'type' => 'number',
            'length' => array(
                'min' => 6,
                'max' => 6
            ),
            'mandatory' => true,
        ),
        'password' => array(
            /* Mot de passe associé au numéro de client saisi.
                * Il doit être identique à celui de votre espace client.
                */
            'type' => 'string',
            'length' => array(
                'min' => 6,
                'max' => 15
            ),
            'mandatory' => true
        ),
        'outputFormat' => array(
            /* Ce bloc contient les balises liées au format des éditions renvoyées par le Web Service. */
            'children' => array(
                'x' => array(
                    /*
                    Permet d’ajuster le décalage horizontal de l’impression
                    sur l’étiquette (exprimé en points) :
                     si x <0 : l'étiquette est décalée à droite.
                     si x >0 : l'étiquette est décalée à gauche.
                    Par défaut, renseigner "false" (ou "0").
                    */
                    'type' => 'number',
                    'min' => -9999,
                    'max' => 9999,
                    'mandatory' => true,
                    'default' => 0
                ),
                'y' => array(
                    /*
                    Permet d’ajuster le décalage vertical de l’impression sur
                    l’étiquette (exprimé en points) :
                     si y <0 : l'étiquette est décalée en haut.
                     si y >0 : l'étiquette est décalée en bas.
                    Par défaut, renseigner "false" (ou "0").
                    */
                    'type' => 'number',
                    'min' => -120,
                    'max' => 120,
                    'mandatory' => true,
                    'default' => 0
                ),
                'outputPrintingType' => array(
                    /* Format d'impression des étiquettes. */
                    'type' => 'list',
                    'choices' => array(
                        'ZPL_10x15_203dpi' => 'impression thermique en ZPL, de dimension 10cm par 15cm, et de résolution 203dpi',
                        'ZPL_10x15_300dpi' => 'impression thermique en ZPL, de dimension 10cm par 15cm, et de résolution 300dpi',
                        'DPL_10x15_203dpi' => 'impression thermique en DPL, de dimension 10cm par 15cm, et de résolution 203dpi',
                        'DPL_10x15_300dpi' => 'impression thermique en DPL, de dimension 10cm par 15cm, et de résolution 300dpi',
                        'PDF_10x15_300dpi' => 'impression bureautique en PDF, de dimension 10cm par 15cm, et de résolution 300dpi',
                        'PDF_A4_300dpi' => 'impression bureautique en PDF, de dimension A4, et de résolution 300dpi'
                    ),
                    'mandatory' => true,
                    'default' => 'PDF_A4_300dpi'
                ),
                'returnType' => array(
                    /* Définit le mode de transmission de l’étiquette.
                    * Utilisé pour le Colissimo Retour uniquement
                    */
                    'type' => 'list',
                    'choices' => array(
                        'SendPDFByMail' => 'L\'étiquette est également envoyée par e-mail en tant que pièce jointe', // à sender
                        'SendPDFLinkByMail' => 'L\'étiquette est également envoyée par e-mail sous forme de lien hypertexte' // à sender
                    ),
                    'mandatory' => false
                )
            ),
            'mandatory' => true
        ),
        'letter' => array(
            /* Ce bloc contient des balises et blocs liés à l’expédition 
             * (options, colis, expéditeur, destinataire, douanes).
             */
            'children' => array( 
                'service' => array(
                    'mandatory' => true,
                    'children' => array(
                        'productCode' => array(
                            /* Code désignant l’offre produit souhaitée.
                            * Exemple : code CORE pour Colissimo Retour.
                            * Si la balise vaut :
                            *  A2P, BPR, ACP, CDI, CMT, BDP, ou PCS, la
                            * balise <pickupLocationId> doit être renseignée,
                            *  DOM, DOS, BOS ou BOM, la balise
                            * <pickupLocationId> ne doit pas être renseignée.
                            */
                            'type' => 'list',
                            'choices' => array(
                                'DOM' => 'Colissimo Domicile - sans signature', 
                                'COLD' => 'Colissimo Domicile - sans signature - contrat antérieur à 2016', 
                                'DOS' => 'Colissimo Domicile - avec signature',
                                'COL' => 'Colissimo Domicile - avec signature - contrat antérieur à 2016', 
                                'BPR' => 'France Colissimo - Point Retrait – en Bureau de Poste',
                                'A2P' => 'France Colissimo - Point Retrait – en relais Pickup ou en consigne Pickup Station',
                                'CORE' => 'France Colissimo Retour France',
                                'COLR' => 'France Colissimo Next-Day - sans signature',
                                'J+1' => 'France Colissimo Next-Day – avec signature',
                                'CORI' => 'International Colissimo Retour International, Outre-Mer Colissimo Retour OM',
                                'COM' => 'Outre-Mer Colissimo Domicile - sans signature',
                                'CDS' => 'Outre-Mer Colissimo Domicile - avec signature',
                                'ECO' => 'Outre-Mer Colissimo Eco OM',
                                'COLI' => 'International Colissimo Expert International',
                                'ACCI' => 'International Offre Economique Grand Export',
                                'CMT' => 'International(Europe) Colissimo - Point Retrait – en relais',
                                'PCS' => 'International(Europe) Colissimo - Point Retrait – Consigne Pickup Station – Sauf France et Belgique',
                                'BDP' => 'International(Europe) Colissimo Point Retrait – en bureau de poste'
                            ),
                            'mandatory' => true
                        ),
                        'depositDate' => array(
                            /* Date de dépôt prévisionnelle dans le réseau La Poste.
                            * Exemple : « 2015-12-23 ».
                            */
                            'type' => 'date',
                            'format' => 'YYYY-mm-dd',
                            'mandatory' => true
                        ),
                        'mailBoxPicking' => array(
                            /* Demander la collecte du colis retourné à partir d'une boîte aux
                            * lettres (retour BAL).
                            * Utilisé pour autoriser le retour des colis en boîte aux lettres,
                            * code CORE (8R).
                            * Utilisé pour le code CORE (8R).
                            * Par défaut, renseigner "false" (ou "0").
                            * Doit valoir "true" (ou "1") pour que le colis retourné par son
                            * ancien destinataire puisse être collecté par un agent de
                            * Colissimo à partir de la boîte aux lettres de l’ancien
                            * destinataire.
                            */
                            'type' => 'boolean',
                        ),
                        'mailBoxPickingDate' => array(
                            'mailBoxPickingDate' => array(
                                /*
                                * Date d’emport en boîte aux lettres demandée.
                                * Utilisé pour le code CORE (8R).
                                * Obligatoire (et pris en compte) seulement si
                                * <mailBoxPicking> vaut "true" (ou "1").
                                * Exemple : 2015-12-23
                                * Pour connaître la prochaine date possible, appeler la méthode
                                * @see getListMailBoxPickingDates
                                */
                                'type' => 'date',
                                'format' => 'YYYY-mm-dd'
                            )
                        ),
                        'transportationAmount' => array(
                            /* Prix du transport de base (frais de port) sans les options
                            * éventuelles.
                            * En centièmes d’euro.
                            * Exemple : 4530 pour 45,30€.
                            * Par défaut, passer à "0".
                            */
                            'type' => 'number',
                            'default' => 0
                        ),
                        'totalAmount' => array(
                            /*
                            * Prix du transport de base augmenté du prix des options de l’envoi.
                            * Obligatoire pour les colis nécessitant une déclaration CN23.
                            * En centièmes d’euro.
                            * Exemple : 4530 pour 45,30€.
                            * Apparaît dans le formulaire CN23 dans le champ « Frais de
                            * port ».
                            */
                            'type' => 'number'
                        ),
                        'orderNumber' => array(
                            /*
                            * Référence de commande du client.
                            * N’apparait pas sur l’étiquette mais est intégré dans le SI
                            * Colissimo.
                            * Renvoyée dans les fichiers de suivi de colis (EDI-Retour).
                            * Peut être utile pour rechercher des colis selon ce champ dans
                            * le suivi ColiView (apparaît dans le champ « réf. client »).
                            */
                            'type' => 'string',
                            'length' => array(
                                'min' => 1,
                                'max' => 30
                            )
                        ),
                        'commercialName' => array(
                            /*
                            * Nom commercial du chargeur.
                            * Cette balise :
                            *  Est obligatoire si <productCode> vaut A2P, BPR,
                            * BDP, CMT
                            *  Facultative si <productCode> vaut DOM, DOS,
                            * Ce nom sera affiché dans les notifications par e-mail faites aux
                            * destinataires des colis.
                            */
                            'type' => 'string',
                        ),
                        'returnTypeChoice' => array(
                            /* Indique si le colis doit être retourné à l’expéditeur en cas de
                            * non distribution du colis.
                            * Obligatoire pour certains colis à l’international selon les zones
                            * tarifaires applicables.
                            */
                            'type' => 'list',
                            'choices' => array(
                                '2' => 'Retour payant en prioritaire (Hors Union Européenne seulement)',
                                '3' => 'Ne pas retourner (Toutes les destinations)'
                            )
                        ),
                    )
                ),
                'parcel' => array(
                    'mandatory' => true,
                    'children' => array(
                        'insuranceValue' => array(
                            /* Valeur assurée.
                            * Par défaut, renseigner "false" (ou "0").
                            * Maximum= 1500€.
                            * Passer 1230 pour 12,30€.
                            * Cette valeur sera arrondie à l’entier le plus proche
                            * Exemple : 12€ si 1232 est envoyé.
                            * Option non cumulable avec le niveau de
                            * recommandation.
                            */
                            'type' => 'number'
                        ),
                        'recommendationLevel' => array(
                            /* Niveau de recommandation.
                            * Option non cumulable avec la valeur assurée.
                            */
                            'type' => 'list',
                            'choices' => array(
                                'R1' => 'Jusqu\'à 31€',
                                'R2' => 'Jusqu\'à 153€',
                                'R3' => 'Jusqu\'à 458€'
                            )
                        ),
                        'weight' => array(
                            /* Poids du colis en kg.
                            * Exemple : « 5.50 ».
                            */
                            'type' => 'number',
                            'unit' => 'kg',
                            'mandatory' => true
                        ),
                        'nonMachinable' => array(
                            /* Format du colis. 
                            * Si la balise <productCode> a le codes BPR, A2P, BDP
                            * ou CMT renseigner "false" (ou "0") par défaut.
                            * Passer à "true" (ou "1") pour indiquer que le format du
                            * colis est non standard (non mécanisable).
                            */
                            'type' => 'boolean'
                        ),
                        'COD' => array(
                            /* Indique si la livraison doit se faire contre remboursement.
                            * Par défaut, renseigner "0" (zéro).
                            * Passer à "true" (ou "1") si la livraison doit se faire contre
                            * remboursement.
                            */
                            'type' => 'boolean',
                            'default' => 0
                        ),
                        'CODAmount' => array(
                            /* Montant attendu pour une livraison contre
                            * remboursement.
                            * Par défaut, renseigner "false" (ou "0").
                            * Obligatoire si la valeur de la balise <COD> est "true" (ou "1").
                            */
                            'type' => 'number',
                            'default' => 0
                        ),
                        'returnReceipt' => array(
                            /* Avis de réception.
                            * Par défaut, renseigner "false " (ou "0").
                            */
                            'type' => 'boolean',
                            'default' => 0
                        ),
                        'instructions' => array(
                            /* Permet d’afficher sur l’étiquette :
                            *  Des indications complémentaires pour la
                            * livraison,
                            *  Le motif du retour.
                            * Exemple pour une livraison : « à gauche au portail
                            * intérieur ».
                            */
                            'type' => 'string',
                            'length' => array(
                                'min' => 1,
                                'max' => 35
                            )
                        ),
                        'pickupLocationId' => array(
                            /* Identifiant du point de retrait pour une livraison
                            * Colissimo hors domicile.
                            * Cette balise :
                            *  Doit être renseignée si <productCode> vaut
                            * A2P, BPR, ACP, CDI, CMT, BDP, ou PCS,
                            *  Doit être vide si <productCode> vaut DOM,
                            * DOS, BOS ou BOM.
                            * Si le code réseau est "X00", il sera obligatoire de
                            * renseigner le bloc <fields>.
                            */
                            'type' => 'number',
                            'length' => array(
                                'min' => 6,
                                'max' => 6
                            )
                        ),
                        'ftb' => array(
                            /* Indique si le colis est franc de taxes et de droits.
                            * Par défaut, renseigner "false " (ou "0").
                            * Obligatoire, à renseigner par "true " (ou "1"), pour les
                            * envois vers l’Outre-Mer uniquement.
                            */
                            'type' => 'boolean',
                            'default' => 0
                        ),
                    ),
                ),
                'customsDeclarations' => array(
                    'children' => array(
                        'includeCustomsDeclarations' => array(
                            /* Permet d’inclure, dans la réponse du web service, la
                            * génération d’un document CN23 au format PDF
                            * portant sur les articles déclarés dans la requête web service.
                            * Par défaut vaut "true" (ou "1") pour inclure le
                            * document CN23 dans la réponse du web service.
                            * Indiquez "false" (ou "0") pour ne pas inclure le
                            * document CN23 dans la réponse du web service
                            */
                            'type' => 'boolean',
                            'default' => 1
                        ),
                        'contents' => array(
                            /* Ce bloc représente la nature de l’envoi et liste des articles à déclarer.
                            * Obligatoire si les envois sont éligibles à la CN23
                            */
                            'children' => array(
                                'article' => array(
                                    'children' => array(
                                        'description' => array(
                                            'type' => 'string',
                                            'length' => array(
                                                'min' => 1,
                                                'max' => 64
                                            ),
                                            'mandatory' => true
                                        ),
                                        'quantity' => array(
                                            'type' => 'number',
                                            'mandatory' => true
                                        ),
                                        'weight' => array(
                                            'type' => 'number',
                                            'unit' => 'kg',
                                            'mandatory' => true
                                        ),
                                        'value' => array(
                                            /* Valeur unitaire des articles en EURO 
                                             * Seulement 2 décimales différentes de 0 sont acceptées
                                             */
                                            'type' => 'number',
                                            'scale' => 2,
                                            'unit' => 'EURO',
                                            'mandatory' => true
                                        ),
                                        'hsCode' => array(
                                            /**
                                             * Numéro tarifaire correspondant à l’article.
                                             * Il faudra indiquer 6, 8 ou 10 chiffres pour les envois à
                                             * l’international nécessitant une déclaration douanière.
                                             * Un code erreur 30519 apparaîtra dans la réponse du
                                             * Web Service si la balise est mal renseignée.
                                             * 
                                             * Obligatoire si <category> vaut 3 (envoi commercial).
                                             * Obligatoire pour les produits retours 7R et 5R. Il doit
                                             * être de taille 6 ou 11
                                             * 
                                             * @see prd_products.prd_tax_code
                                             * ex: 90148000
                                             * 
                                             * @link https://pro.douane.gouv.fr/prodouane.asp
                                             * @link https://fr.wikipedia.org/wiki/Syst%C3%A8me_harmonis%C3%A9
                                             */
                                            'type' => 'number',
                                            'length' => array(
                                                'min' => 6,
                                                'max' => 10
                                            )
                                        ),
                                        'originCountry' => array(
                                            /**
                                             * Code ISO du pays d’origine de l’article.
                                             * Obligatoire pour les produits retours 7R et 5R. Il doit être
                                             * identique pour tous les articles déclarés
                                             * Obligatoire si category=3 (envoi commercial)
                                             * Le nom du pays apparait en entier sur le fichier CN23
                                             */
                                            'type' => 'string',
                                            'length' => array(
                                                'min' => 2,
                                                'max' => 2
                                            ),
                                        ),
                                        'currency' => array(
                                            /**
                                             * Devise.
                                             * Obligatoire pour les produits retours inter (7R) hors UE
                                             * et 5R (OM).
                                             * La devise doit être identique pour tous les articles
                                             * déclarés
                                             */
                                            'type' => 'string',
                                            'length' => array(
                                                'min' => 3,
                                                'max' => 3
                                            ),
                                            'default' => 'EUR'
                                        ),
                                        'artref' => array(
                                            /**
                                             * Référence de l’article, numéro de série.
                                             * Obligatoire pour les produits retours inter (7R) hors UE
                                             * et 5R (OM).
                                             * 
                                             * @see prd_products.prd_ref
                                             */
                                            'type' => 'string',
                                            'length' => array(
                                                'min' => 1,
                                                'max' => 4
                                            ),
                                        ),
                                        'originalIdent' => array(
                                            /**
                                             * Clé permettant d’identifier la facture aller et le colis lié
                                             * à l’article.
                                             * Obligatoire pour les produits retours inter (7R) hors UE
                                             * et 5R(OM).
                                             * Appairer le numéro de colis avec son ordonnancement :
                                             *  si l’article appartient au 1er colis, indiquer A ;
                                             *  si l’article appartient au 3ème colis, indiquer C ;
                                             *  etc …
                                             */
                                            'type' => 'string',
                                            'length' => array(
                                                'min' => 1,
                                                'max' => 1
                                            ),
                                        ),
                                    ),
                                    'length' => array(
                                        'max' => 100
                                    )
                                ),
                                'category' => array(
                                    'children' => array(
                                        'value' => array(
                                            'type' => 'list',
                                            'choices' => array(
                                                '1' => 'Cadeau',
                                                '2' => 'Echantillon commercial',
                                                '3' => 'Envoi commercial',
                                                '4' => 'Document',
                                                '5' => 'Autre',
                                                '6' => 'Retour de marchandise'
                                            ),
                                            'mandatory' => true
                                        )
                                    ),
                                    'mandatory' => true
                                ),
                                'original' => array(
                                    'children' => array(
                                        'originalIdent' => array(
                                            /**
                                             * Clé permettant d’identifier la facture aller et le colis lié
                                             * à l’article.
                                             * Obligatoire pour les produits retours inter (7R) hors UE
                                             * et 5R(OM).
                                             * Appairer le numéro de colis avec son ordonnancement :
                                             *  si l’article appartient au 1er colis, indiquer A ;
                                             *  si l’article appartient au 3ème colis, indiquer C ;
                                             *  etc …
                                             */
                                            'type' => 'string',
                                            'length' => array(
                                                'min' => 1,
                                                'max' => 1
                                            ),
                                        ),
                                        'originalInvoiceNumber' => array(
                                            /**
                                             * Numéro de la facture d’origine du colis.
                                             * Obligatoire pour un colis Retour
                                             */
                                            'type' => 'string',
                                            'length' => array(
                                                'min' => 1,
                                                'max' => 35
                                            ),
                                        ),
                                        'originalInvoiceDate' => array(
                                            'type' => 'date',
                                            'format' => 'YYYY-mm-dd'
                                        ),
                                        'originalParcelNumber' => array(
                                            /**
                                             * Numéro de colis d’origine.
                                             * Obligatoire pour un colis Retour
                                             */
                                            'type' => 'string',
                                            'length' => array(
                                                'min' => 1,
                                                'max' => 35
                                            ),
                                        )
                                    )
                                ),
                            )
                        ),
                        'importersReference' => array(
                            'type' => 'string',
                            'length' => array(
                                'min' => 1,
                                'max' => 35
                            )
                        ),
                        'flowTransport' => array(
                            'type' => 'string',
                            'length' => array(
                                'max' => 6
                            )
                        ),
                        'importersContact' => array(
                            'type' => 'string',
                            'length' => array(
                                'min' => 35,
                                'max' => 35
                            )
                        ),
                        'officeOrigin' => array(
                            'type' => 'string',
                            'length' => array(
                                'min' => 35,
                                'max' => 35
                            )
                        ),
                        'comments' => array(
                            'type' => 'string',
                            'length' => array(
                                'max' => 35
                            )
                        ),
                        'invoiceNumber' => array(
                            'type' => 'string',
                            'length' => array(
                                'min' => 1,
                                'max' => 35
                            )
                        ),
                        'licenceNumber' => array(
                            'type' => 'string',
                            'length' => array(
                                'min' => 1,
                                'max' => 35
                            )
                        ),
                        'certificatNumber' => array(
                            'type' => 'string',
                            'length' => array(
                                'min' => 1,
                                'max' => 35
                            )
                        ),
                        'importerAddress' => array(
                            'children' => array(
                                'companyName' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 35
                                    )
                                ),
                                'lastName' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 35
                                    )
                                ),
                                'firstName' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 29
                                    )
                                ),
                                'line0' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 35
                                    )
                                ),
                                'line1' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 35
                                    )
                                ),
                                'line2' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 35
                                    )
                                ),
                                'line3' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 35
                                    )
                                ),
                                'countryCode' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 2,
                                        'max' => 2
                                    ),
                                    'mandatory' => true
                                ),
                                'city' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 35
                                    )
                                ),
                                'zipCode' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 5,
                                        'max' => 5
                                    ),
                                    'mandatory' => true
                                ),
                                'phoneNumber' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 15
                                    )
                                ),
                                'mobileNumber' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 10
                                    )
                                ),
                                'doorCode1' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 8
                                    )
                                ),
                                'doorCode2' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 8
                                    )
                                ),
                                'email' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 5,
                                        'max' => 80
                                    )
                                ),
                                'intercom' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 30
                                    )
                                ),
                                'Language' => array(
                                    'type' => 'list',
                                    'choices' => array(
                                        'ES' => 'Espagnol', 
                                        'IT' => 'Italien', 
                                        'NL' => 'Néerlandais', 
                                        'EN' => 'Anglais', 
                                        'DE' => 'Allemand'
                                    ),
                                    'default' => 'FR'
                                ),
                            )
                        )
                    )
                ),
                'sender' => array(
                    'mandatory' => true,
                    'children' => array(
                        'senderParcelRef' => array(
                            /**
                             * Référence de commande de l’expéditeur.
                             * Elle permet d’afficher la référence client seulement sur
                             * l’étiquette.
                             * Cette donnée n’est pas remontée dans le SI Colissimo et ne
                             * sera pas visible dans ColiView.
                             */
                            'type' => 'string',
                            'length' => array(
                                'min' => 1,
                                'max' => 17
                            )
                        ),
                        'address' => array(
                            'children' => array(
                                'companyName' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'lastName' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'firstName' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 29
                                    )
                                ),
                                'line0' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'line1' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'line2' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'line3' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'countryCode' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 2,
                                        'max' => 2
                                    ),
                                    'mandatory' => true
                                ),
                                'city' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'zipCode' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 5,
                                        'max' => 5
                                    ),
                                    'mandatory' => true
                                ),
                                'phoneNumber' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 15
                                    )
                                ),
                                'mobileNumber' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 10,
                                        'max' => 12
                                    )
                                ),
                                'doorCode1' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 8
                                    )
                                ),
                                'doorCode2' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 8
                                    )
                                ),
                                'email' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 5,
                                        'max' => 80
                                    )
                                ),
                                'intercom' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 30
                                    )
                                ),
                                'Language' => array(
                                    'type' => 'list',
                                    'choices' => array(
                                        'ES' => 'Espagnol', 
                                        'IT' => 'Italien', 
                                        'NL' => 'Néerlandais', 
                                        'EN' => 'Anglais', 
                                        'DE' => 'Allemand'
                                    )
                                )
                            )
                        )
                    )
                ),
                'addressee' => array(
                    'mandatory' => true,
                    'children' => array(
                        'addresseeParcelRef' => array(
                            'type' => 'string',
                            'length' => array(
                                'min' => 1,
                                'max' => 17
                            )
                        ),
                        'codeBarForReference' => array(
                            'type' => 'boolean',
                            'default' => 0
                        ),
                        'serviceInfo' => array(
                            'type' => 'string'
                        ),
                        'address' => array(
                            'children' => array(
                                'companyName' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'lastName' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'firstName' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 29
                                    )
                                ),
                                'line0' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'line1' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'line2' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'line3' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'countryCode' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 2,
                                        'max' => 2
                                    ),
                                    'mandatory' => true
                                ),
                                'city' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 35
                                    )
                                ),
                                'zipCode' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 5,
                                        'max' => 5
                                    ),
                                    'mandatory' => true
                                ),
                                'phoneNumber' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'max' => 15
                                    )
                                ),
                                'mobileNumber' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 10,
                                        'max' => 12
                                    )
                                ),
                                'doorCode1' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 8
                                    )
                                ),
                                'doorCode2' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 8
                                    )
                                ),
                                'email' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 5,
                                        'max' => 80
                                    )
                                ),
                                'intercom' => array(
                                    'type' => 'string',
                                    'length' => array(
                                        'min' => 1,
                                        'max' => 30
                                    )
                                ),
                                'Language' => array(
                                    'type' => 'list',
                                    'choices' => array(
                                        'ES' => 'Espagnol', 
                                        'IT' => 'Italien', 
                                        'NL' => 'Néerlandais', 
                                        'EN' => 'Anglais', 
                                        'DE' => 'Allemand'
                                    )
                                )
                            )
                        )
                    )
                )
            ),
            'mandatory' => true
        ),
        'fields' => array(
            /* Ce bloc peut être utilisé pour renseigner l’identifiant
             * (options, colis, expéditeur, destinataire, douanes).
             */
            'children' => array(),
            'mandatory' => true
        ),
    );

    protected static $resourceParameters = array(
        '/generateLabel' => array(
            'contractNumber',
            'password',
            'outputFormat',
            'letter'
        )
    );

    protected $contractNumber;

    protected function getContractNumber()
    {
        return $this->contractNumber;
    }

    public function setContractNumber($contractNumber)
    {
        $this->contractNumber = $contractNumber;

        return $this;
    }
    
    protected $password;

    protected function getPassword()
    {
        return $this->password;
    }

    public function setPassword($password)
    {
        $this->password = $password;

        return $this;
    }

    protected function getUrl()
    {
        return "https://ws.colissimo.fr/sls-ws/SlsServiceWSRest";
    }

    protected $options;

    protected function getOptions()
    {
        return $this->options;
    }

    public function setOptions(array $options = null)
    {
        $this->options = $options;

        return $this;
    }

    public function __construct($contractNumber = null, $password = null)
    {
        $this->contractNumber = $contractNumber;
        $this->password = $password;
    }

    protected function call($resource, array & $parameters = array(), $validate = true, $throwsException = true)
    {
        $this->addDefaultParameters($resource, $parameters, $throwsException);

        if ($validate) {
            if (!$this->validateParameters($resource, $parameters, $throwsException)) {
                return false;
            }
        }

        try {
            $ch = curl_init();

            $url = $this->getUrl() . $resource;

            $json = json_encode($parameters, JSON_FORCE_OBJECT);

            $options = array(
                CURLOPT_URL => $url,
                CURLOPT_POST => 1,
                CURLOPT_POSTFIELDS => $json,
                CURLOPT_RETURNTRANSFER => 1,
                CURLOPT_TIMEOUT => 2,
                CURLOPT_CONNECTTIMEOUT => 1,
                CURLOPT_FOLLOWLOCATION => 1
            );

            $moreOptions = $this->getOptions();
            if (is_array($moreOptions)) {
                $options = array_merge_recursive($options, $moreOptions);
            }

            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Content-Length: ' . mb_strlen($json)));
    
            curl_setopt_array($ch, $options);

            error_log(sprintf('Appel au webservice colissimo %s avec les paramètres %s', $url, $json) , E_USER_NOTICE);

            $result = curl_exec($ch);

            file_put_contents('/var/www/test.txt', $result);
            
            if (false === $result) {
                throw new Exception(sprintf('Erreur lors de l\'appel au webservice Colissimo : %s', curl_error($ch)));
            }

            $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            error_log(
                sprintf(
                    'Réponse du webservice colissimo %s avec les paramètres %s, statut: %d', 
                    $url, 
                    json_encode($parameters), 
                    intval($http_status)
                ), 
                E_USER_NOTICE
            );
    
            curl_close($ch);
        } catch (Exception $e) {
            if ($throwsException) {
                throw $e;
            } else {
                error_log($e->getMessage(), E_USER_ERROR);
            }

            return false;
        }

        return $this->handleResponse($result, $http_status, $throwsException);
    }

    protected function handleResponse($response, $http_status, $throwsException = true)
    {
        $file_content = null;
        if (substr($response, 0, 7) == '--uuid:') { // multipart response
            $ar_result = explode('--uuid:', $response);
            foreach($ar_result as $result) {
                if ($result == '') {
                    continue;
                }

                $arrayResult = explode("\r\n\r\n", $result);

                if (!isset($arrayResult[1])) {
                    continue;
                }

                $headers = $arrayResult[0];
                $body = $arrayResult[1];

                if (false !== strpos($headers, 'application/json')) {
                    $data = json_decode($body, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        if ($throwsException) {
                            throw new Exception('Réponse non valide du webservice Colissimo');
                        }

                        return false;
                    }
                } else {
                    $file_content = $body;
                    break;
                }
            }
        } else {
            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                if ($throwsException) {
                    throw new Exception('Réponse non valide du webservice Colissimo');
                }
    
                return false;
            }
        }

        if (isset($data['messages']) && is_array($data['messages'])) {
            foreach ($data['messages'] as $message) {
                if (isset($message['type']) && $message['type'] == static::MESSAGE_TYPE_ERROR) {
                    if ($throwsException) {
                        throw new Exception(
                            isset($message['messageContent']) ? $message['messageContent'] : 'Erreur inconnue', 
                            isset($message['code']) ? $message['code'] : 0
                        );
                    }

                    return false;
                }
            }
        }

        $data['file'] = $file_content;

        return $data;
    }

    protected function validateParameters($resource, array & $parameters, $throwsException = true)
    {
        if (!isset(static::$resourceParameters[$resource]) || !is_array(static::$resourceParameters[$resource])) {
            if ($throwsException) {
                throw new Exception('Définition du webservice non trouvée');
            }

            return false;
        }

        $isValid = true;

        foreach (static::$resourceParameters[$resource] as $parameterName) {
            if (!isset(static::$parameters[$parameterName]) || !is_array(static::$parameters[$parameterName])) {
                if ($throwsException) {
                    throw new Exception(sprintf('Définition du webservice non valide : %s', $parameterName));
                }

                return false;
            }

            if (!$this->validateParameter($parameterName, static::$parameters[$parameterName], $parameters, $throwsException)) {
                return false;
            }
        }

        return $isValid;
    }

    protected function validateParameter($name, array & $definition, array & $parameters, $throwsException = true, $parentPath = '')
    {
        $currentPath = $path . ('' == $path ? '' : '.') . $name;
        if (isset($definition['mandatory']) && !isset($parameters[$name])) {
            throw new Exception(sprintf('Paramètre manquant : %s', $currentPath));
        }

        if ($definition['type'] == 'number') {
            $val = doubleval($parameters[$name]);
            if (((string) $val) !== $parameters[$name]) {
                if ($throwsException) {
                    throw new Exception(sprintf('La valeur du paramètre %s n\'est pas un nombre', $currentPath));
                }

                return false;
            }

            if (isset($definition['min']) && $val < $definition['min']) {
                if ($throwsException) {
                    throw new Exception(sprintf('La valeur minimale du paramètre %s n\'est pas respectée', $currentPath));
                }

                return false;
            }

            if (isset($definition['max']) && $val > $definition['max']) {
                if ($throwsException) {
                    throw new Exception(sprintf('La valeur maximale du paramètre %s n\'est pas respectée', $currentPath));
                }

                return false;
            }
        }

        if (isset($definition['length']) && is_array($definition['length'])) {
            if (isset($definition['length']['min']) && strlen((string) $parameters[$name]) < $definition['length']['min']) {
                if ($throwsException) {
                    throw new Exception(sprintf('La longueur minimale du paramètre %s n\'est pas respectée', $currentPath));
                }

                return false;
            }

            if (isset($definition['max']) && $val > $definition['max']) {
                if ($throwsException) {
                    throw new Exception(sprintf('La longueur maximale du paramètre %s n\'est pas respectée', $currentPath));
                }

                return false;
            }
        }

        if (isset($definition['type']) && $definition['type'] == 'string') {
            if (!preg_match('/\w+/i', $parameters[$name])) {
                if ($throwsException) {
                    throw new Exception(sprintf('La valeur du paramètre %s n\'est pas alphanumérique', $currentPath));
                }

                return false;
            }
        }

        if (isset($definition['type']) && $definition['type'] == 'list' && isset($definition['choices']) && is_array($definition['choices'])) {
            if (!isset($definition['choices'][$parameters[$name]])) {
                if ($throwsException) {
                    throw new Exception(sprintf('La valeur du paramètre %s n\'est pas autorisée parmis les choix possibles', $currentPath));
                }

                return false;
            }
        }

        $isValid = true;
        if (isset($definition['children'])) {
            foreach ($definition['children'] as $childName => $childDefinition) {
                if (!$this->validateParameter($childName, $childDefinition, $parameters[$name], $throwsException, $currentPath)) {
                    return false;
                }
            }
        }

        return $isValid;
    }

    protected function addDefaultParameters($resource, array & $parameters = array(), $throwsException = true)
    {
        if (!isset(static::$resourceParameters[$resource]) || !is_array(static::$resourceParameters[$resource])) {
            if ($throwsException) {
                throw new Exception('Définition du webservice non trouvée');
            }

            return $this;
        }

        foreach (static::$resourceParameters[$resource] as $parameterName) {
            if (!isset(static::$parameters[$parameterName]) || !is_array(static::$parameters[$parameterName])) {
                if ($throwsException) {
                    throw new Exception(sprintf('Définition du webservice non valide : %s', $parameterName));
                }

                return false;
            }

            if (!$this->addDefaultParameter($parameterName, static::$parameters[$parameterName], $parameters, null, $throwsException)) {
                return $this;
            }
        }

        return $this;
    }

    protected function addDefaultParameter($name, array & $definition, array & $parameters, $path = null, $throwsException = true)
    {
        $currentPath = $path . ('' == $path ? '' : '.') . $name;

        if (!isset($parameters[$name]) && isset($definition['mandatory']) && $definition['mandatory'] && isset($definition['default'])) {
            $parameters[$name] = $definition['default'];
        }

        if (isset($definition['children']) && isset($definition['mandatory']) && $definition['mandatory']) {
            if (!isset($parameters[$name]) || !is_array($parameters[$name])) {
                $parameters[$name] = array();
            }

            foreach ($definition['children'] as $childName => $childDefinition) {
                $this->addDefaultParameter($childName, $childDefinition, $parameters[$name], $currentPath, $throwsException);
            }
        }

        return $this;
    }

    public function generateLabel(array & $parameters = array(), $validate = true, $throwsException = true)
    {
        if (!isset($parameters['contractNumber'])) {
            $parameters['contractNumber'] = $this->getContractNumber();
        }

        if (!isset($parameters['password'])) {
            $parameters['password'] = $this->getPassword();
        }

        return $this->call('/generateLabel', $parameters, $validate, $throwsException);
    }
}