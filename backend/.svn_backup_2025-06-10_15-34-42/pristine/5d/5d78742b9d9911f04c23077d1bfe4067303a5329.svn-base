<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/field_behavior.proto

namespace GPBMetadata\Google\Api;

class FieldBehavior
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0ac6020a1f676f6f676c652f6170692f6669656c645f6265686176696f72" .
            "2e70726f746f120a676f6f676c652e6170691a20676f6f676c652f70726f" .
            "746f6275662f64657363726970746f722e70726f746f2a7b0a0d4669656c" .
            "644265686176696f72121e0a1a4649454c445f4245484156494f525f554e" .
            "5350454349464945441000120c0a084f5054494f4e414c1001120c0a0852" .
            "455155495245441002120f0a0b4f55545055545f4f4e4c591003120e0a0a" .
            "494e5055545f4f4e4c591004120d0a09494d4d555441424c45100542700a" .
            "0e636f6d2e676f6f676c652e61706942124669656c644265686176696f72" .
            "50726f746f50015a41676f6f676c652e676f6c616e672e6f72672f67656e" .
            "70726f746f2f676f6f676c65617069732f6170692f616e6e6f746174696f" .
            "6e733b616e6e6f746174696f6e73a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

