<?php

require_once('db.inc.php');
require_once('define.inc.php');
require_once('obj_position.inc.php');
require_once('products.inc.php');
require_once('users.inc.php');
require_once('medias.inc.php');
require_once('ria.queue.inc.php');

/** \defgroup model_documents Bibliothèque de documents
 *	\ingroup dam
 *
 *  Ce module comprend les fonctions nécessaires à la gestion d'une bibliothèque de documents.
 *  Elle n'est pas liée à l'utilisation qui en est faite par la suite (produits, pages de contenu, etc...).
 *
 *  Le comportement de ces fonctions est régi par les directives de configuration suivantes :
 *	- doc_dir : chemin absolu vers le répertoire de stockage des documents
 *
 * @{
*/

/** \defgroup model_documents_types Types de documents
 *	Ce module comprend les fonctions nécessaires à la gestion des types de documents.
 *	Les types aident au classement des documents.
 * @{
 */

/**	Permet l'ajout d'un type de document.
 *	@param string $name Désignation du type de document (ne peut être vide)
 *	@param string $desc Description du type de document.
 *	@param int $parent Optionnel, identifiant d'un type parent (par défaut à la racine)
 *	@return int l'identifiant attribué au type en cas de succès, false en cas d'échec
 */
function doc_types_add( $name, $desc, $parent=0, $ref_gescom='' ){
	if( !trim($name) ){
		return false;
	}

	if( !is_numeric($parent) || $parent < 0 || ( $parent > 0 && !doc_types_exists( $parent ) ) ){
		return false;
	}

	global $config;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	$sql = '
		insert into doc_types
		(type_tnt_id, type_name, type_desc, type_parent_id, type_date_created, type_ref_gescom)
		values
		(
			'.$config['tnt_id'].', "'.addslashes($name).'", "'.addslashes($desc).'", '.( $parent ? $parent : 'NULL' ).', now(),
			'.( trim($ref_gescom) != '' ? '"'.addslashes($ref_gescom).'"' : 'NULL' ).'
		)
	';

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}

	$id = ria_mysql_insert_id();

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) ){
		return false;
	}

	// Crée les alias
	$alias = rew_rewritemap_generated( array($id), CLS_TYPE_DOCUMENT );
	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_TYPE_DOCUMENT);
		if( $prd_pages ){
			while( $page = ria_mysql_fetch_array($prd_pages) ){
				rew_rewritemap_add_specify_class( CLS_TYPE_DOCUMENT, $alias.$page['key'], $page['url'].'?t='.$id, 200, $wst['id'], false, null, $id );
			}
		}
	}

	ria_mysql_query('
		update doc_types
		set type_url_alias="'.$alias.'"
		where type_id='.$id
	);

	try{
		// Index le type de document dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_TYPE_DOCUMENT,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return $id;
}

/**	Cette fonction permet la mise à jour d'un type de document.
 *	@param int $id Identifiant du type de document
 *	@param string $name Désignation du type de document (ne peut être vide)
 *	@param string $desc Description du type de document
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_types_update( $id, $name, $desc ){
	if( !doc_types_exists($id) ) return false;
	global $config;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	$res = ria_mysql_query('update doc_types set type_name=\''.addslashes($name).'\', type_desc=\''.addslashes($desc).'\' where type_tnt_id ='.$config['tnt_id'].' and type_id='.$id);
	if( $res ){
		$url = ria_mysql_result(ria_mysql_query('select type_url_alias from doc_types where type_tnt_id ='.$config['tnt_id'].' and type_id='.$id),0,0);
		if( !trim($url) ){
			// Récupère les sites
			$rwst = wst_websites_get();
			if( !$rwst || !ria_mysql_num_rows($rwst) )
				return false;

			// Crée les alias
			$alias = rew_rewritemap_generated( array($id), CLS_TYPE_DOCUMENT );
			while( $wst = ria_mysql_fetch_array($rwst) ){
				$prd_pages = cfg_urls_get( $wst['id'], CLS_TYPE_DOCUMENT);
				if( $prd_pages ){
					while( $page = ria_mysql_fetch_array($prd_pages) ){
						rew_rewritemap_add_specify_class( CLS_TYPE_DOCUMENT, $alias.$page['key'], $page['url'].'?t='.$id, 200, $wst['id'], false, null, $id );
					}
				}
			}
			$res = ria_mysql_query('update doc_types set type_url_alias=\''.$alias.'\' where type_tnt_id ='.$config['tnt_id'].' and type_id='.$id);
		}
	}

	if( $res ){
		try{
			// Réindex le type de document dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_TYPE_DOCUMENT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $res;
}

/**	Cette fonction met à jour le type parent d'un type existant
 *	@param int $id Identifiant du type à mettre à jour
 *	@param int $parent_id Identifiant du type parent (0 pour mettre à la racine)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function doc_types_parent_set( $id, $parent_id ){
	if( !doc_types_exists( $id ) ){
		return false;
	}
	if( !is_numeric($parent_id) || $parent_id < 0 ){
		return false;
	}

	if( $parent_id > 0 ){
		// parent inexistant ou équivalent au type
		if( !doc_types_exists( $parent_id ) || $parent_id == $id ){
			return false;
		}
		// type appartenant à la parenté du nouveau parent (référence circulaire)
		$parents_ar = doc_types_get_parents_array( $parent_id );
		if( in_array($id, $parents_ar) ){
			return false;
		}
	}

	global $config;

	$sql = '
		update doc_types
		set type_parent_id = '.( $parent_id ? $parent_id : 'NULL' ).'
		where type_tnt_id = '.$config['tnt_id'].' and type_id = '.$id.'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction récupérer les identifiants des types de documents enfants à un type (récursivement)
 *	@param int $type_id Obligatoire, identifiant d'un type
 *	@return array Un tableau contenant les identifiants des types enfants
 */
function doc_types_get_childs_array( $type_id ){
	if( !is_numeric($type_id) || $type_id<=0 ){
		return false;
	}

	global $config;

	$ar_child_id = array();

	$r_child = doc_types_get( 0, false, true, false, false, false, $type_id );
	if( $r_child ){
		while( $child = ria_mysql_fetch_assoc($r_child) ){
			$ar_child_id[] = $child['id'];

			$r_temp = doc_types_get_childs_array( $child['id'] );
			if( is_array($r_temp) && sizeof($r_temp) ){
				$ar_child_id = array_merge( $ar_child_id, $r_temp );
			}
		}
	}

	return array_unique( $ar_child_id );
}

/**	Cette fonction retourne la parenté d'un type donné, triée par profondeur décroissante
 *	@param int $id Identifiant du type
 *	@param string $dir Optionnel, par défaut triée par profondeur décroissante, mettre "desc" pour triée par profondeur croissante
 *	@return array Un tableau contenant les identifiants des types parents
 */
function doc_types_get_parents_array( $id, $dir='asc' ){
	$parents = array();

	if( is_numeric( $id ) && $id>0 ){
		while( $id ){
			$rtype = doc_types_get( $id );
			if( $rtype && ria_mysql_num_rows($rtype) ){
				$id = ria_mysql_result($rtype, 0, 'parent_id');
				if( $id ){
					$parents[] = $id;
				}
			}else{
				$id = false;
			}
		}
	}

	if( $dir=='desc' ){
		asort( $parents );
	}

	return $parents;
}

/**	Cette fonction permet la suppression "logique" d'un type de document.
 *	Lors de la suppression d'un type, les types enfants sont "remontés" d'un niveau
 *	@param int $id Identifiant du type de document à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_types_del( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	// Suppression des urls
	rew_rewritemap_del_multilingue( _FLD_DOC_TYPE_URL, array($id) );
	$url = doc_types_get_url( $id );
	if( trim($url)!='' )
		rew_rewritemap_del( $url );

	$res = ria_mysql_query( 'update doc_types set type_date_deleted=now() where type_tnt_id ='.$config['tnt_id'].' and type_id='.$id );
	if( !$res ){
		return false;
	}

	// récupère l'identifiant du type parent
	$p_id = null;
	$parent = ria_mysql_query('select type_parent_id from doc_types where type_tnt_id = '.$config['tnt_id'].' and type_id = '.$id);
	if( $parent && ria_mysql_num_rows($parent) ){
		$p_id = ria_mysql_result($parent, 0, 0);
	}

	// met le type parent comme parent direct sur les enfants du type supprimé
	ria_mysql_query('update doc_types set type_parent_id = '.( $p_id ? $p_id : 'NULL' ).' where type_tnt_id = '.$config['tnt_id'].' and type_parent_id = '.$id);

	// supprime le type de documents du moteur de recherche
	$cid = ria_mysql_query('select type_cnt_id as cnt from doc_types where type_tnt_id='.$config['tnt_id'].' and type_id='.$id);
	if( $cid && ria_mysql_num_rows($cid) ){
		$cid = ria_mysql_result( $cid, 0, 'cnt' );
		if( is_numeric($cid) && $cid>=0 ){
			search_index_clean( 'doc-type', $cid );
		}
	}

	return true;
}

/**	Cette fonction permet la suppression récursive d'un type de document et de ses sous-types.
 *
 *	@param int $id 			Obligatoire, Identifiant du type de document à supprimer
 *	@param bool $with_docs 	Optionnel, permet de supprimer les documents du type également
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_types_del_recursively($id, $with_docs=false){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	// Permet de supprimer les documents si le paramètre est fourni
	if( $with_docs ){
		$r_docs = doc_documents_get(0, $id);
		if( $r_docs && ria_mysql_num_rows($r_docs) ){
			while( $one_doc = ria_mysql_fetch_assoc($r_docs) ){
				if( !doc_documents_del($one_doc['id']) ){
					return false;
				}
			}
		}
	}

	// Suppression des urls
	rew_rewritemap_del_multilingue(_FLD_DOC_TYPE_URL, array($id));
	$url = doc_types_get_url($id);
	if( trim($url) != '' ){
		rew_rewritemap_del($url);
	}

	$sql = '
		update doc_types
		set type_date_deleted = now()
		where type_tnt_id = '.$config['tnt_id'].'
		and type_id = '.$id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	// supprime le type de documents du moteur de recherche
	$cid = ria_mysql_query('select type_cnt_id as cnt from doc_types where type_tnt_id='.$config['tnt_id'].' and type_id='.$id);
	if( $cid && ria_mysql_num_rows($cid) ){
		$cid = ria_mysql_result($cid, 0, 'cnt');
		if( is_numeric($cid) && $cid>=0 ){
			search_index_clean('doc-type', $cid);
		}
	}

	// Supprime les types de documents enfants
	$ar_childs = doc_types_get_childs_array($id);
	if( is_array($ar_childs) && count($ar_childs) ){
		foreach( $ar_childs as $child_id ){
			doc_types_del_recursively($child_id, $with_docs);
		}
	}

	return true;
}

/**	Cette fonction permet la validation d'un identifiant de type de document.
 *	@param int $id Identifiant du type de document à vérifier.
 *	@return bool true si l'identifiant est valide et correspond à un type de document enregistré dans la base de données.
 *	@return bool false dans le cas contraire.
 */
function doc_types_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$sql = 'select type_id from doc_types where type_tnt_id ='.$config['tnt_id'].' and type_id='.$id.' and type_date_deleted is null';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);
}

/**	Cette fonction permet le chargement d'un ou plusieurs types de documents
 *	@param int $id Facultatif, identifiant ou tableau d'identifiants de types de documents sur lequel filtrer le résultat.
 *	@param $allwebsites Facultatif, si true alors on compte les doc pour tout les sites du locataire, si false on ne prend que le site en cours
 *	@param $empty facultatif, permet de recuperer les types de documents qui ne sont pas vide
 *	@param $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par nom. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : id, name, desc, docs. Les valeurs autorisées pour la direction sont : asc, desc. Le tri sur price doit être le dernier du tableau ( dans le cas contraire, les critères suivants ne seront pas pris en compte ).
 *	@param int|array $fld Facultatif, identifiant d'un champ libre sur lequel filtrer le résultat (ou tableau multiple clé => valeur)
 *	@param bool $fld_or Facultatif, détermine si les valeurs du tableau $fld sont comparées entre elles par des "ou" ou des "et"
 *	@param int $parent Facultatif, identifiant d'un type parent ou d'un tableau de types parents. -1 pour récupérer tous les types, par défaut seuls les types à la racine sont retournés.
 *	@param int|array $exclude Facultatif, identifiant ou tableau d'identifiants de types à exclure du résultat
 *	@param string $name Optionnel, nom d'un document
 *	@param bool $case_insensitive Optionnel, permet de lancer la récupération d'un type de document par le nom insensible à la casse (va de paire avec $name)
 *
 *	@return bool False en cas d'échec
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du type de document
 *			- name : désignation du type de document
 *			- desc : description du type de document
 *			- url_alias : url simplifiée du type de document
 *			- docs : nombre de documents de ce type
 *			- pos : position lors d'un tri personnalisé
 *			- parent_id : identifiant du type de document parent
 *			- ref_gescom : référence externe du type de documents (en cas de synchronisé)
 */
function doc_types_get( $id=0, $allwebsites = false, $empty = true, $sort=false, $fld=false, $fld_or=false, $parent=null, $exclude=false, $name=false, $case_insensitive=false ){
	global $config;

	if( is_array($id) ){
		foreach( $id as $k ){
			if( !is_numeric($k) || $k<=0 ){
				return false;
			}
		}
	}else{
		if( is_numeric($id) && $id > 0 ){
			$id = array($id);
		}else{
			$id = array();
		}
	}

	// si parent non spécifié et id unique spécifié
	if( sizeof($id) == 1 && !$parent ){
		$parent = -1;
	}

	if( is_array($exclude) ){
		foreach( $exclude as $exc ){
			if( !is_numeric($exc) || $exc <= 0 ){
				return false;
			}
		}
	}else{
		if( is_numeric($exclude) && $exclude > 0 ){
			$exclude = array($exclude);
		}else{
			$exclude = array();
		}
	}

	// Si l'on recherche les types de documents non vides, on récupère tous les types utilisés par les documents
	// Les identifiants des types parents sont inclus
	$ar_type_not_empty = [ -1 ];
	if( !$empty ){
		$r_used = ria_mysql_query('
			select distinct doc_type_id
			from doc_documents, doc_types
			where doc_type_id=type_id
				and doc_tnt_id = '.$config['tnt_id'].'
				and doc_is_deleted = 0
				and type_date_deleted is null
		');

		if( $r_used ){
			while( $used = ria_mysql_fetch_assoc($r_used) ){
				$ar_type_not_empty[] = $used['doc_type_id'];

				$parents = doc_types_get_parents_array( $used['doc_type_id'] );
				foreach( $parents as $p ){
					$ar_type_not_empty[] = $p;
				}

			}
		}
	}

	$sql = '
		select
			type_id as id, type_name as name, type_desc as "desc", type_url_alias as url_alias, type_ref_gescom as ref_gescom,
			count(doc_id) as docs, type_pos as pos, type_parent_id as parent_id, type_date_created as date_created
		from doc_types
		left join (
			select doc_type_id, doc_tnt_id, doc_id
			from doc_documents
			where doc_tnt_id = '.$config['tnt_id'].'
				and doc_is_deleted=0
	';

	if( !$allwebsites ){
		$sql .= '
			and doc_id in (
				select dw_doc_id from doc_websites
				where dw_tnt_id = '.$config['tnt_id'].' and dw_wst_id = '.$config['wst_id'].'
			)
		';
	}

	$sql .= '
			group by doc_type_id, doc_tnt_id, doc_id
		) as tmp on type_id = tmp.doc_type_id and type_tnt_id = tmp.doc_tnt_id
		where
			type_tnt_id = '.$config['tnt_id'].' and
			type_date_deleted is null
	';

	if( sizeof($id) ){
		$sql .= ' and type_id in ('.implode(', ', $id).')';
	}

	if( !$empty ){
		$sql .= ' and type_id in ('.implode(', ', $ar_type_not_empty).')';
	}

	// filtrage par parent (par défaut "is null")
	if( is_array($parent) ){
		if( !sizeof($parent) ){
			return false;
		}
		foreach( $parent as $p ){
			if( !is_numeric($p) || $p <= 0 ){
				return false;
			}
		}
		$sql .= ' and type_parent_id in ('.implode(', ', $parent).')';
	}elseif( is_numeric($parent) && $parent > 0 ){
		$sql .= ' and type_parent_id = '.$parent;
	}elseif( $parent !== -1 ){
		$sql .= ' and type_parent_id is null';
	}

	// exclusions
	if( sizeof($exclude) ){
		$sql .= ' and type_id not in ('.implode(', ', $exclude).')';
	}

	if( $name !== false ){
		if( $case_insensitive ){
			$sql .= ' and lower(type_name) = lower("'.addslashes( $name ).'")';
		}else{
			$sql .= ' and type_name = "'.addslashes( $name ).'"';
		}
	}

	$sql .= fld_classes_sql_get( CLS_TYPE_DOCUMENT, $fld, $fld_or );

	$sql .= '
		group by type_id, type_name, type_desc, type_url_alias
	';

	// Tri du résultat (valeurs par défaut)
	if( $sort==false || !is_array($sort) || sizeof($sort)==0 ){
		$sort = array( 'type_name'=>'asc' );
	}

	// Converti le paramètre de tri en SQL
	$sort_final = array();
	foreach( $sort as $col=>$dir ){
		$dir = $dir=='asc' ? 'asc' : 'desc';
		switch( $col ){
			case 'id' :
				array_push( $sort_final, 'type_id '.$dir );
				break;
			case 'name' :
				array_push( $sort_final, 'type_name '.$dir );
				break;
			case 'desc' :
				array_push( $sort_final, 'type_desc '.$dir );
				break;
			case 'docs' :
				array_push( $sort_final, 'count(doc_id) '.$dir );
				break;
		}
	}

	// Ajoute la clause de tri
	if( sizeof($sort_final)==0 ){

		$perso_sort = false;
		if( is_numeric($parent) && $parent > 0 ){
			// type de tri pour le parent spécifié
			$perso_sort = doc_types_order_get( $parent );
		}elseif( !is_array($parent) && $parent !== -1 ){
			// type de tri pour la racine
			$perso_sort = doc_types_order_get();
		}

		$sort_final = $perso_sort ? array('type_pos asc') : array( 'type_name asc' );
	}

	$sql .= ' order by '.implode( ', ', $sort_final ).' ';
	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
	}

	return $r;
}

/** Cette fonction permet de récupérer l'url du type de document
 *	@param int $id Obligatoire, identifiant du type de document
 *	@return string Retourne l'url du type de document
 *	@return bool Retourne false si le paramètre est omis ou bien si le type de document n'existe pas
 */
function doc_types_get_url( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select type_url_alias as alias
		from doc_types
		where type_tnt_id='.$config['tnt_id'].' and type_id='.$id.'
		and type_date_deleted is null
	';

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'alias' );
}

/** Cette fonction permet de récupérer le libellé d'un type de document
 *	@param int $id Obligatoire, identifiant d'un type de document
 *	@return string Retourne le nom du type de document
 *	@return bool Retourne false si le paramètre est omis ou bien si le document n'existe pas
 */
function doc_types_get_name( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select type_name as name
		from doc_types
		where type_tnt_id='.$config['tnt_id'].' and type_id='.$id.'
		and type_date_deleted is null
	';

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'name' );
}

/**	Cette fonction permet la modification de la méthode de tri utilisée pour un ensemble de types de documents.
 *
 *	@param int $type Type de documents dont on souhaite modifier l'ordre d'apparition des enfants. 0 pour la racine du catalogue.
 *	@param bool $order Mode de tri. 0/false pour un tri alphabétique, 1/true pour un tri numérique défini par l'utilisateur
 *	@param int $parent Optionnel, identifiant du type parent
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_types_order_update( $type, $order, $parent = null ){
	if( $order && ( !is_numeric($type) || $type < 0 ) ){
		return false;
	}elseif( !$order ){
		$type = 0;
	}

	if( !( $types = doc_types_get( $type, false, true, false, false, false, $parent ) ) ){
		return false;
	}

	global $config;

	$pos = 0;
	while( $t = ria_mysql_fetch_array($types) ){
		ria_mysql_query('update doc_types set type_pos='.( $order ? $pos : 'NULL' ).' where type_tnt_id='.$config['tnt_id'].' and type_id='.$t['id']);
		$pos++;
	}

	return true;
}

/**	Cette fonction retourne le mode utilisé pour trier les types de documents à un niveau donné.
 *
 *	@param int $parent_id Optionnel, identifiant du type parent
 *
 *	@return bool false si la méthode de tri est alphabétique, true si la méthode de tri est personnalisée
 */
function doc_types_order_get( $parent_id = null ){
	global $config;

	$sql = '
		select type_id
		from doc_types
		where type_tnt_id='.$config['tnt_id'].'
			and type_pos is not null
	';
	if( is_numeric($parent_id) && $parent_id > 0 ){
		$sql .= ' and type_parent_id = '.$parent_id;
	}else{
		$sql .= ' and type_parent_id is null';
	}

	$res = ria_mysql_query($sql);

	return ria_mysql_num_rows($res)>0;
}

/**	Permet le déplacement vers le haut d'un type de documents.
 *
 *	@param int $id Type à déplacer vers le haut
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_types_move_up( $id ){
	global $config;
	if( !is_numeric($id) ) return false;

	// Charge la catégorie
	$rtype = doc_types_get($id);
	if( !ria_mysql_num_rows($rtype) ) return false;
	$type = ria_mysql_fetch_array($rtype);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( $type['pos']==='' ) return false;

	// S'assure que la catégorie n'est pas déjà la première de la liste
	if( $type['pos']===0 ) return false;

	// Permute la catégorie avec celle qui se trouve juste au dessus
	ria_mysql_query('update doc_types set type_pos='.($type['pos']-1).' where type_tnt_id='.$config['tnt_id'].' and type_id='.$id);
	ria_mysql_query('update doc_types set type_pos='.($type['pos']).' where type_tnt_id='.$config['tnt_id'].' and type_parent_id'.( $type['parent_id'] ? ' = '.$type['parent_id'] : ' is null' ).' and type_pos='.($type['pos']-1).' and type_id!='.$id);

	return true;
}

/**	Permet le déplacement vers le bas d'un type de documents.
 *
 *	@param int $id Type à déplacer vers le bas
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_types_move_down( $id ){
	global $config;
	if( !is_numeric($id) ) return false;

	// Charge la catégorie
	$rtype = doc_types_get($id);
	if( !ria_mysql_num_rows($rtype) ) return false;
	$type = ria_mysql_fetch_array($rtype);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( $type['pos']==='' ) return false;

	// S'assure que la catégorie n'est pas déjà en fin de la liste
	$rnext = ria_mysql_query('select type_id from doc_types where type_tnt_id='.$config['tnt_id'].' and type_parent_id'.( $type['parent_id'] ? ' = '.$type['parent_id'] : ' is null' ).' and type_pos='.($type['pos']+1));
	if( !ria_mysql_num_rows($rnext) ) return false;

	// Permute la catégorie avec celle qui se trouve juste au dessous
	ria_mysql_query('update doc_types set type_pos='.($type['pos']+1).' where type_tnt_id='.$config['tnt_id'].' and type_id='.$id);
	ria_mysql_query('update doc_types set type_pos='.($type['pos']).' where type_tnt_id='.$config['tnt_id'].' and type_parent_id'.( $type['parent_id'] ? ' = '.$type['parent_id'] : ' is null' ).' and type_pos='.($type['pos']+1).' and type_id!='.$id);

	return true;
}

/**	Déplace le document avant ou après un autre document
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *	L'utilisateur doit s'assurer que les 2 documents appartiennent au même parent (sinon ça n'a pas de sens)
 *
 *	@param int $source Identifiant du document source
 *	@param int $target Identifiant du document cible
 *	@param string $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
 */
function doc_types_position_update( $source, $target, $where ){
	return obj_position_update( DD_TYPE_DOCUMENT, $source, $target, $where );
}

/** Cette fonction permet d'indexé un type de documents.
 *	@param int $type Optionnel, identifiant d'un type de documents
 *	@return bool True si l'indexation s'est correctement déroulée, False dans le cas contraire
 */
function doc_types_add_index( $type=0 ){
	if( !is_numeric($type) || $type < 0 ){
		return false;
	}

	global $config;

	$rtype = doc_types_get($type);

	if( !$rtype || !ria_mysql_num_rows($rtype) ){
		return false;
	}

	while( $type = ria_mysql_fetch_array($rtype) ){
		// Indexation du type de document
		$cid = search_index_content($type['url_alias'], 'doc-type', $type['name'], $type['desc'], $type['name'].' '.$type['desc'], '/admin/documents/types/edit.php?type='.$type['id'], 1, $type['id']);

		if( !$cid ){
			return false;
		}

		ria_mysql_query('
			update doc_types
			set type_cnt_id='.$cid.'
			where type_tnt_id='.$config['tnt_id'].'
			and type_id='.$type['id']
		);
	}

	return true;
}

/**	Cette fonction met à jour la position absolue d'un type de document. En conséquence, les autres types (du même niveau de parent) sont décalés avant et après la position donnée.
 *	@param int $id Identifiant du type de document
 *	@param int $pos Numéro de la nouvelle position (base 0)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function doc_types_set_pos( $id, $pos ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}
	if( !is_numeric($pos) || $pos < 0 ){
		return false;
	}

	$rtype = doc_types_get( $id );
	if( !$rtype || !ria_mysql_num_rows($rtype) ){
		return false;
	}

	// charge les autres types du même niveau
	$rtypes = doc_types_get( 0, false, true, false, false, false, ria_mysql_result($rtype, 0, 'parent_id'), $id );

	if( !$rtypes ){
		return false;
	}

	global $config;

	$i = 0;
	$pass_current = false;
	while( $t = ria_mysql_fetch_array($rtypes) ){
		if( $i == $pos ){
			ria_mysql_query('update doc_types set type_pos = '.$i.' where type_tnt_id = '.$config['tnt_id'].' and type_id = '.$id);
			$pass_current = true;
			$i++;
		}

		// on fait la MAJ uniquement si différence avec l'ancienne position
		if( $i != $t['pos'] ){
			ria_mysql_query('update doc_types set type_pos = '.$i.' where type_tnt_id = '.$config['tnt_id'].' and type_id = '.$t['id']);
		}

		$i++;
	}

	// si $pos était trop grand p/r au nombre d'éléments
	if( !$pass_current ){
		ria_mysql_query('update doc_types set type_pos = '.$i.' where type_tnt_id = '.$config['tnt_id'].' and type_id = '.$id);
	}

	return true;
}

/**	Cette fonction détermine le nombre de documents rattachés à un type.
 *	Elle diffère de la colonne du même nom dans le sens où les types enfants sont également pris en compte dans le calcul.
 *	@param int $id Identifiant du type de document
 *	@return int Le nombre de documents pour ce type et ses enfants
 *	@return bool False en cas d'échec
 */
function doc_types_get_docs_count( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$rtype = doc_types_get( $id );
	if( !$rtype || !ria_mysql_num_rows($rtype) ){
		return false;
	}
	$type = ria_mysql_fetch_array($rtype);

	// count standard sur le type
	$total_count = $type['docs'];

	// récupération des types enfants et appels récursifs sur chaque
	if( $rchild = doc_types_get( 0, false, true, false, false, false, $id ) ){
		while( $ch = ria_mysql_fetch_array($rchild) ){
			$total_count += doc_types_get_docs_count( $ch['id'] );
		}
	}

	return $total_count;
}

/**	Cette fonction force la mise à jour de la date de dernière modification d'un type de document.
 *	@param int $id Identifiant du type de document.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function doc_types_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update doc_types
		set type_date_modified = now()
		where type_id = '.$id.' and type_tnt_id = '.$config['tnt_id'].'
	';

	return ria_mysql_query($sql);

}

function doc_types_url_alias_add( $type_id ){
	if( !doc_types_exists($type_id) ){
		return false;
	}

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) )
		return false;

	// Crée les alias
	$alias = rew_rewritemap_generated( array($type_id), CLS_TYPE_DOCUMENT );
	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_TYPE_DOCUMENT);
		if( $prd_pages ){
			while( $page = ria_mysql_fetch_array($prd_pages) )
				rew_rewritemap_add_specify_class( CLS_TYPE_DOCUMENT, $alias.$page['key'], $page['url'].'?t='.$type_id, 200, $wst['id'], false, null, $type_id );
		}
	}

	if( ria_mysql_query('update doc_types set type_url_alias="'.$alias.'" where type_id='.$type_id) ){
		return false;
	}

	return $alias;
}

/// @}

/** \defgroup model_documents_documents Documents
 *	Ce module comprend les fonctions nécessaires à la gestion des documents.
 *	Il est dépendant des fonctions contenus dans le module Types de documents.
 * @{
 */

/** Permet l'upload d'un document qui sera ensuite ajoutée à la bibliothèque
 *
 *	@param int $type Obligatoire, Identifiant du type de document
 *  @param string $fieldname Obligatoire, Nom du champ de formulaire contenant le document à uploader.
 *	@param string $name Obligatoire, La désignation du document
 *	@param string $desc Obligatoire, La description du document
 *	@param array $websites Facultatif, Liste des sites pour lequel le document sera créé
 *	@param array $languages Facultatif, tableau de code langues pour lesquels le document est rattaché
 *
 *  @return int L'identifiant attribué au document, False en cas d'échec.
 */
function doc_documents_upload( $type, $fieldname, $name, $desc, $websites=array(), $languages=array() ){

	if( !isset($_FILES[$fieldname]) ) return false;
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return doc_documents_add( $type, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'], $name, $desc, $websites, $languages );

}

/**	Cette fonction permet la mise à jour du fichier attaché au document, par upload depuis le poste client.
 *	@param int $id Obligatoire, Identifiant du document à actualiser
 *	@param string $fieldname Obligatoire, Nom du champ contenant le nouveau fichier
 *	@param string $lng Facultatif, code langue du document (utile uniquement dans des contextes multi-lingues)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_documents_update_file( $id, $fieldname, $lng=false ){
	global $config;

	$lng = $lng!==false && in_array($lng, $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];

	if( !doc_documents_exists($id) ) return false;
	if( !isset($_FILES[$fieldname]) ) return false;
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;

	$file = $config['doc_dir'].'/'.( $lng!=$config['i18n_lng'] ? $lng.'-' : '' ).$id;
	@unlink( $file );
	$res = rename( $_FILES[$fieldname]['tmp_name'], $file );

	if( $lng==$config['i18n_lng'] ){
		if( $res ){
			$md5_content = md5(file_get_contents($file));
			ria_mysql_query('
				update doc_documents set doc_size='.filesize( $file ).', doc_filename=\''.addslashes($_FILES[$fieldname]['name']).'\' , doc_preview_rebuild = 1, doc_md5_content = \''.$md5_content.'\' where doc_tnt_id="'.$config['tnt_id'].'" and doc_id='.$id.'
			');
		}
	} else {
		// @unlink( $config['doc_dir'].'/'.$lng.'-'.$id );
		fld_object_values_set( $id, _FLD_DOC_FILENAME, $_FILES[$fieldname]['name'], $lng );
		fld_object_values_set( $id, _FLD_DOC_SIZE, filesize( $file ), $lng );
	}

	return $res;
}

/**	Cette fonction met à jour le fichier lié à un document RiaShop
 *	Contrairement à la fonction doc_documents_update_file(), elle n'impose pas que le fichier provienne d'un formulaire d'upload
 *	@param int $id Identifiant du document à mettre à jour
 *	@param string $path Chemin d'accès vers le nouveau fichier
 *	@param string $usr_filename Nom du fichier, pour l'utilisateur (si vide alors on utilisera $path)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function doc_documents_update_file_sage( $id, $path, $usr_filename='' ){
	global $config;

	if( !doc_documents_exists($id) ) return false;
	if( !file_exists($path) ) return false;

	@unlink( $config['doc_dir'].'/'.$id );
	$res = rename( $path, $config['doc_dir'].'/'.$id );
	if( $res ){
		$md5_content = md5(file_get_contents($config['doc_dir'].'/'.$id));
		$file_name = trim($usr_filename) != '' ? $usr_filename : $path;
		ria_mysql_query('update doc_documents set doc_size='.filesize($config['doc_dir'].'/'.$id).', doc_filename=\''.addslashes($file_name).'\', doc_preview_rebuild = 1, doc_md5_content = \''.$md5_content.'\' where doc_tnt_id="'.$config['tnt_id'].'" and doc_id='.$id);
	}
	return $res;
}

/** Permet l'ajout d'un document. Cette fonction est similaire à doc_documents_upload,
 *  à l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile
 *  lors d'importation.
 *	@param int $type Identifiant du type de document
 *	@param string $srv_filename Nom du fichier à ajouter (le fichier doit être local, et le chemin absolu)
 *	@param string $usr_filename Nom du fichier, pour l'utilisateur (ne peut pas être vide)
 *	@param string $name La désignation du document (ne peut pas être vide)
 *	@param string $desc La description du document
 *	@param array $websites Liste des sites pour lequel on creer le document
 *	@param array $languages Facultatif, tableau de code langues pour lesquels le document est rattaché
 *	@param string $ref_gescom Optionnel, référence externe du document
 *
 *  @return bool false en cas d'erreur
 *  @return int l'identifiant du nouveau document, en cas de succès
 */
function doc_documents_add( $type, $srv_filename, $usr_filename, $name, $desc, $websites = array(), $languages=array(), $ref_gescom='' ){
	global $config;
	// Validation des paramètres d'entrée
	if( !doc_types_exists($type) ) return false;
	if( !file_exists($srv_filename) ) return false;
	if( !trim($usr_filename) ) return false;

	$usr_filename = trim($usr_filename);
	if( $config['tnt_id']==8 ){
		$name = trim($name);
	}else{
		$name = ucfirst(trim($name));
	}
	$desc = ucfirst(trim($desc));
	$size = filesize($srv_filename);

	if( !is_array($languages) || !sizeof($languages) ){
		$languages = $config['i18n_lng_used'];
	}

	$md5_content = md5(file_get_contents($srv_filename));

	if( !ria_mysql_query('
		insert into doc_documents
			(doc_tnt_id, doc_type_id,doc_filename,doc_name,doc_desc,doc_size,doc_date_created, doc_md5_content, doc_ref_gescom)
		values
			('.$config['tnt_id'].','.$type.',\''.addslashes($usr_filename).'\',\''.addslashes($name).'\',\''.addslashes($desc).'\','.$size.',now(),
			\''.$md5_content.'\', '.( trim($ref_gescom) != '' ? '"'.addslashes($ref_gescom).'"' : 'null').')' )  ){
		return false;
	}

	$id = ria_mysql_insert_id();
	if( !$id ){
		return false;
	}

	// Ajoute le document pour les websites sélectionnés
	$wsts = wst_websites_get();
	if(ria_mysql_num_rows($wsts)){
		while($wst = ria_mysql_fetch_array($wsts)){
			if( !is_array($websites) || !sizeof($websites) || in_array($wst['id'], $websites) ){
				foreach( $config['i18n_lng_used'] as $lng ){
					if( in_array($lng, $languages) ){
						doc_websites_add( $id, $wst['id'], $lng );
					}
				}
			}
		}
	}

	try{
		// Indexe le document dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
			'cls_id' => CLS_DOCUMENT,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	// Déplace le fichier
	rename($srv_filename, $config['doc_dir'].'/'.$id);

	return $id;
}

/**	Permet la mise à jour des métas informations associées à un document de la base de données.
 *	@param int $id Obligatoire, Identifiant du document à mettre à jour
 *	@param int $type Obligatoire, Identifiant du type de document
 *	@param string $usr_filename Obligatoire, Nom du fichier, pour l'utilisateur (ne peut pas être vide)
 *	@param string $name Obligatoire, Nouvelle désignation du document (ne peut pas être vide)
 *	@param string $desc Obligatoire, Nouvelle description du document
 *	@param array $websites Facultatif, tableau d'identifiants de sites pour lesquels le document est rattaché
 *	@param array $languages Facultatif, tableau de code langues pour lesquels le document est rattaché
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function doc_documents_update( $id, $type, $usr_filename, $name, $desc , $websites=false, $languages=array() ){

	if( !doc_documents_exists($id) ) return false;
	if( !doc_types_exists($type) ) return false;

	global $config;

	$usr_filename = trim($usr_filename);
	if( $config['tnt_id']==8 )
		$name = trim($name);
	else
		$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	// supprime les liens entre le document et les sites et leurs versions
	doc_websites_del( $id );

	// par défaut, ajout de toutes les langues
	if( !is_array($languages) || !sizeof($languages) ){
		$languages = $config['i18n_lng_used'];
	}

	//on ajoute le document pour les websites selectionnés
	if( is_array($websites) && sizeof($websites) ){
		$wsts = wst_websites_get();

		if(ria_mysql_num_rows($wsts)){
			while($wst = ria_mysql_fetch_array($wsts)){
				if( in_array($wst['id'], $websites) ){
					foreach( $config['i18n_lng_used'] as $lng ){
						if( in_array($lng, $languages) ){
							doc_websites_add( $id, $wst['id'], $lng );
						}
					}
				}
			}
		}
	}

	$res = ria_mysql_query('
		update doc_documents
		set doc_type_id='.$type.',
			doc_filename=\''.addslashes($usr_filename).'\',
			doc_name=\''.addslashes($name).'\',
			doc_desc=\''.addslashes($desc).'\'
		where doc_id='.$id.'
			and doc_tnt_id = '.$config['tnt_id']
	);

	if( $res ){
		try{
			// Réindex le document dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_DOCUMENT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $res;
}

/** Cette fonction permet de mettre à jour la désignation du document
 *
 * @param	int		$doc_id	Obligatoire, Identifiant du document
 * @param	string	$name	Obligatoire, Nouvelle désignation
 *
 * @return	bool	True en cas de succès, false sinon
 */
function doc_documents_update_name($doc_id, $name){

	if( !doc_documents_exists($doc_id) || !is_string($name) || trim($name) == '' ){
		return false;
	}
	global $config;

	$name = trim($name);

	if( $config['tnt_id']!=8 ){
		$name = ucfirst($name);
	}

	$res = ria_mysql_query('
		update doc_documents
		set doc_name=\''.addslashes($name).'\'
		where doc_id='.$doc_id.'
			and doc_tnt_id = '.$config['tnt_id']
	);

	if( $res ){
		try{
			// Réindex le document dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_DOCUMENT,
				'obj_id_0' => $doc_id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $res;

}

/** Cette fonction permet de déplacer un ou plusieurs documents dans un nouvean type de document.
 *	@param int $doc_id Obligatoire, identifiant ou tableau d'identifiant
 *	@param int $type_id Obligatoire, nouvel identifiant de type de documents
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function doc_documents_move( $doc_id, $type_id ){
	$doc_id = control_array_integer( $doc_id );
	if( !$doc_id ){
		return false;
	}

	if( !doc_types_exists($type_id) ){
		return false;
	}

	global $config;

	$sql = '
		update doc_documents
		set doc_type_id = '.$type_id.'
		where doc_tnt_id='.$config['tnt_id'].'
			and doc_id in ('.implode( ', ', $doc_id ).')
			and doc_is_deleted=0
	';

	return ria_mysql_query( $sql );
}

/** Vérifie un identifiant de document.
 *  @param int $id Obligatoire, Identifiant du document à vérifier
 *	@param int $website Facultatif, identifiant d'un site du locataire
 *  @return bool True si le docuement existe, False sinon
 */
function doc_documents_exists( $id, $website=0 ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( $website != 0 && (!is_numeric($website) || !wst_websites_exists($website)) ) return false;
	global $config;

	$sql = 'select doc_id from doc_documents where doc_id='.$id.' and doc_is_deleted=0 and doc_tnt_id = '.$config['tnt_id'];

	if($website != 0){
		$sql .= ' and doc_id in (select dw_doc_id from doc_websites where dw_tnt_id = '.$config['tnt_id'].' and dw_wst_id = '.$website.')' ;
	}

	return ria_mysql_num_rows(ria_mysql_query($sql))>0;
}

/** Permet la suppression d'un ou plusieurs documents.
 *	Un des deux paramètres suivant doit être fourni : $id ou $type_id
 *
 *  @param int $doc_id Optionnel, identifiant ou tableau d'identifiants de documents à supprimer.
 * 	@param int $type_id Optionnel, identifiant ou tableau d'identifiants de types de documents dont les documents doivent être supprimés
 * 	@param bool $recursive_type Optionnel, détermine si la suppressione est récursive lorsque $type_id est fournie (ignoré dans tous les autres cas)
 * 	@param bool $is_excluded Optionnel, par défaut à false, mettre true pour signaler que $doc_id est le tableau des documents à ne pas supprimer (ne fonctionne que si $type_id est fourni)
 *  @return bool true en cas de succès
 *  @return bool false en cas d'échec
 *
 */
function doc_documents_del( $doc_id=0, $type_id=0, $recursive_type=false, $is_excluded=false ){
	global $config;

	$doc_id = control_array_integer( $doc_id, false );
	if( $doc_id === false ){
		return false;
	}

	$type_id = control_array_integer( $type_id, false );
	if( $type_id === false ){
		return false;
	}

	// Il faut au minimum fournir un identifiant de document ou un identifiant de type de documents
	if( !count($doc_id) && !count($type_id) ){
		return false;
	}

	if( count($type_id) && $recursive_type ){
		// Récupère les types de documents de la hiérarchie enfante
		foreach( $type_id as $one_type ){
			$childs = doc_types_get_childs_array( $one_type );
			if( is_array($childs) && count($childs) > 0 ){
				$type_id = array_merge( $type_id, $childs );
			}
		}
	}

	$where = '
		where doc_tnt_id = '.$config['tnt_id'].'
			and doc_is_deleted = 0
	';

	if( count($doc_id) > 0 ){
		if( count($type_id) > 0 && $is_excluded ){
			$where .= ' and doc_id not in ('.implode( ', ', $doc_id ).')';
		}else{
			$where .= ' and doc_id in ('.implode( ', ', $doc_id ).')';
		}
	}

	if( count($type_id) > 0 ){
		$where .= ' and doc_type_id in ('.implode( ', ', $type_id ).')';
	}


	$cid = ria_mysql_query('
		select doc_cnt_id as cnt from doc_documents
		'.$where
	);

	$sql = '
		update doc_documents
		set doc_is_deleted=1,
			doc_date_deleted=now()
	 '.$where;


	$r = ria_mysql_query( $sql );

	if( !$r ){
		return false;
	}

	@unlink( $config['doc_dir'].'/'.$doc_id );

	// supprime le document du moteur de recherche
	if( $cid && ria_mysql_num_rows($cid) ){
		$cid = ria_mysql_result( $cid, 0, 'cnt' );
		if( is_numeric($cid) && $cid>0 ){
			search_index_clean( 'doc', $cid );
		}
	}

	return true;
}

/**	Cette fonction permet le chargement d'un ou plusieurs documents, filtrés en fonction des paramètres
 *	optionnels fournis.
 *	@param int $id Facultatif, identifiant ou tableau d'identifiants d'un document sur lequel filtrer le résultat
 *	@param int $type Facultatif, identifiant d'un type de document sur lequel filtrer le résultat
 *	@param int|null|bool $website Facultatif, identifiant d'un site (autre que celui en config). Avec NULL, pas de filtre sur le website. Avec false (valeur par défaut), le site en cours est utilisé comme filtre.
 *	@param string $name Facultatif, permet de filtrer le résultat sur le nom du document,par défaut on recherche par le début, passer un tableau pour paramétrer ce paramètre array('name'=>'titre', 'like' => 'start' | 'end' | 'contains')
 *	@param bool $check_segments Facultatif, vérifie si le document est accessible dans le contexte de segmentation. Ignoré si $d est spécifié (Pour le moment ce paramètre n'est pas utilisé)
 *	@param string $lng Facultatif, code de la langue afin de récupérer les documents présents que dans une langue précise du site
 *	@param bool $recursive_type Optionnel, mettre à true pour récupérer les document des sous-types de celui passé en paramètre $type (par défaut à false)
 *	@param bool $preview_rebuild Optionnel, par défaut ignoré, mettre True pour avoir que les documents dont la preview doit être reconstruit, False pour tous les autres
 *	@param int|null $limit Optionnel, Limite le nombre de résultat de recherche, null pour tout avoir
 *	@param int|null $start Optionnel, place le début du curseur dans le résultat (indisociable du paramètre $limit)
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du document
 *			- filename : nom du fichier (virtuel)
 *			- size : poids du fichier, en octets
 *			- name : désignation du fichier
 *			- desc : description du fichier
 *			- date_created : date et heure de création du fichier
 *			- date_created_en : date et heure de création du fichier au format d'origine
 *			- type_id : identifiant du type du document
 *			- type_name : désignation du type de document
 *			- doc_ext : extension du fichier
 *			- preview_rebuild : si oui ou non la preview doit être reconstruit
 *			- ref_gescom : référence externe du document
 */
function doc_documents_get( $id=0, $type=0, $website=false, $name='', $check_segments=true, $lng=false, $recursive_type=false, $preview_rebuild=null, $limit=null, $start=null ){
	global $config;

	if( is_array($id) ){
		foreach( $id as $one_id ){
			if( !is_numeric($one_id) || $one_id<=0 ){
				return false;
			}
		}
	}elseif( is_numeric($id) && $id>0 ){
		$id = array($id);
	}else{
		$id = array();
	}

	$ar_type = array();

	// Transforme le paramètre type pour qu'il soit toujours un tableau
	if( is_numeric($type) && $type > 0 ){
		$type = [ $type ];
	}

	// Si le paramètre type est bien de type array, on créé alors un tableau des types (avec une récursivité si demandé)
	// pour la recherche des documents
	if( is_array($type) ){
		foreach( $type as $one_type ){
			if( is_numeric($one_type) && $one_type > 0 ){
				$ar_type[] = $one_type;

				if( $recursive_type ){
					$temp = doc_types_get_childs_array( $one_type );
					if( is_array($temp) && sizeof($temp) ){
						$ar_type = array_merge( $ar_type, $temp );
					}
				}
			}
		}
	}

	$lng = $lng!==false && in_array($lng, $config['i18n_lng_used']) ? $lng : false;

	$search_doc = array();
	if( !is_array($name) ){
		if( trim($name) != '' ){
			$search_doc = array( 'name' => $name, 'like' => 'contains', 'ci' => false );
		}
	}elseif( sizeof($name) ){
		if( !ria_array_key_exists(array('name', 'like', 'ci'), $name) ){
			return false;
		}

		$search_doc = $name;
	}

	$sql = '
		select
			doc_id as id, doc_filename as filename, doc_size as size,
			doc_name as name, doc_desc as "desc",
			doc_preview_rebuild as preview_rebuild,
			date_format(doc_date_created,"%d/%m/%Y à %H:%i") as date_created, doc_date_created as date_created_en,
			type_id, type_name, doc_pos as pos,
			doc_md5_content as md5_content,
			if( substring_index(doc_filename, ".", -1) != doc_filename, substring_index(doc_filename, ".", -1), "") as doc_ext,
			doc_ref_gescom as ref_gescom
		from doc_documents, doc_types
	';
	//if( !$website )
	//	$sql .= ' inner join doc_websites on ( dw_tnt_id=doc_tnt_id and dw_wst_id='.$config['wst_id'].' and dw_doc_id=doc_id )';

	$sql .= '
		where doc_type_id=type_id
		and doc_tnt_id = type_tnt_id
		and doc_tnt_id = '.$config['tnt_id'].'
		and doc_is_deleted=0
			and type_date_deleted is null
	';

	if( sizeof($search_doc) ){
		if( !$search_doc['ci'] ){
			$search_doc['name'] = strtoupper( $search_doc['name'] );
		}

		switch( $search_doc['like'] ){
			case 'contains': {
				$like_sql = ' like "%'.addslashes( $search_doc['name'] ).'%"';
				break;
			}
			case 'end': {
				$like_sql = ' like "%'.addslashes( $search_doc['name'] ).'"';
				break;
			}
			default: {
				$like_sql = ' like "'.addslashes( $search_doc['name'] ).'%"';
				break;
			}
		}

		if( $search_doc['ci'] ){
			$sql .= ' and doc_name '.$like_sql;
		}else{
			$sql .= ' and upper(doc_name) '.$like_sql;
		}
	}

	if( $website!==null ){
		$sql .= '
			and exists (
				select 1
				from doc_websites
				where dw_tnt_id='.$config['tnt_id'].'
					and dw_doc_id=doc_id
					and dw_wst_id='.( is_numeric($website) ? $website : $config['wst_id'] ).'
					'.( $lng ? ' and dw_lng_code=\''.$lng.'\'' : '' ).'
			)
		';
	}

	if( sizeof($id) ){
		$sql .= ' and doc_id in ('.implode(', ', $id).')';
	}

	if( sizeof($ar_type) ){
		$sql .= ' and doc_type_id in ('.implode( ', ', $ar_type ).')';
	}

	if ($preview_rebuild !== null) {
		if ($preview_rebuild) {
			$sql .= ' and doc_preview_rebuild = 1';
		}else{
			$sql .= ' and doc_preview_rebuild = 0';
		}
	}

	$sql .= '
		group by doc_id';

	// Ajoute la clause de tri
	if( is_array($type) && count($type) == 1 && doc_documents_order_get($type[0]) ) {
		$sort_final = array('doc_pos asc');
	} else  {
		$sort_final = array('doc_name asc');
	}
	$sql .= ' order by '.implode( ', ', $sort_final ).' ';
	if( is_numeric($limit) ){
		$sql .= '
			limit '.( is_numeric($start) && $start >= 0 ? $start.', ' : '').$limit.'
		';
	}
	$r = ria_mysql_query($sql);

	if( $r && ria_mysql_num_rows($r) && $check_segments ){
		$id_ar = array();

		$usr_id = 0; // non connecté
		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] )
			$usr_id = $_SESSION['admin_view_user'];
		elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] )
			$usr_id = $_SESSION['usr_id'];

		while( $d = ria_mysql_fetch_array($r) ){
			if( seg_objects_check_segment( CLS_DOCUMENT, $d['id'], CLS_USER, $usr_id ) ){
				$id_ar[] = $d['id'];
			}
		}

		if( !sizeof($id_ar) ) return false;

		return doc_documents_get( $id_ar, $type, null, '', false, $lng, $recursive_type, null, $limit);
	}

	return $r;
}

/**	Cette fonction permet la modification de la méthode de tri utilisée pour un ensemble de documents.
 *
 *	@param int $type Type de documents dont on souhaite modifier l'ordre d'apparition des documents enfants.
 *	@param bool $order Mode de tri. 0/false pour un tri alphabétique, 1/true pour un tri numérique défini par l'utilisateur
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_documents_order_update( $type, $order ){
	global $config;
	if( !is_numeric($type) ) return false;
	if( $type!=0 && !doc_types_exists($type) ) return false;

	if( $order ){
		$documents = doc_documents_get(0, $type, null, '', false );
		$pos = 0;
		while( $d = ria_mysql_fetch_array($documents) ){
			ria_mysql_query('update doc_documents set doc_pos='.$pos.' where doc_tnt_id='.$config['tnt_id'].' and doc_id='.$d['id']);
			$pos++;
		}
		return true;
	}else{
		ria_mysql_query('update doc_documents set doc_pos=null where doc_tnt_id='.$config['tnt_id'].' and doc_is_deleted=0 and doc_type_id='.$type);
		return true;
	}
}

/**	Cette fonction retourne le mode utilisé pour trier les types de documents.
 *	@param int $type Obligatoire, identifiant du type de document
 *	@return bool false si la méthode de tri est alphabétique, true si la méthode de tri est personnalisée
 */
function doc_documents_order_get( $type ){
	global $config;
	if( !is_numeric($type) ) return false;
	if( $type!=0 && !doc_types_exists($type) ) return false;

	$res = ria_mysql_query( 'select doc_id
		from doc_documents
		where doc_tnt_id='.$config['tnt_id'].'
			and doc_pos is not null
			and doc_type_id='.$type.'
			and doc_is_deleted=0
	' );

	return ria_mysql_num_rows($res)>0;
}

/**	Permet le déplacement vers le haut d'un document.
 *
 *	@param int $id Document à déplacer vers le haut
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_documents_move_up( $id ){
	global $config;
	if( !is_numeric($id) ) return false;

	// Charge le document
	$rdoc = doc_documents_get($id, 0, false, '', false);
	if( !ria_mysql_num_rows($rdoc) ) return false;
	$doc = ria_mysql_fetch_array($rdoc);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( !is_numeric($doc['pos']) ) return false;

	// S'assure que la catégorie n'est pas déjà la première de la liste
	if( $doc['pos']===0 ) return false;

	// Permute la catégorie avec celle qui se trouve juste au dessus
	ria_mysql_query('update doc_documents set doc_pos='.($doc['pos']-1).' where doc_tnt_id='.$config['tnt_id'].' and doc_id='.$id.' and doc_type_id='.$doc['type_id']);
	ria_mysql_query('update doc_documents set doc_pos='.($doc['pos']).' where doc_tnt_id='.$config['tnt_id'].' and doc_pos='.($doc['pos']-1).' and doc_id!='.$id.' and doc_type_id='.$doc['type_id']);

	return true;
}

/**	Permet le déplacement vers le bas d'un document.
 *
 *	@param int $id Document à déplacer vers le bas
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_documents_move_down( $id ){
	global $config;
	if( !is_numeric($id) ) return false;

	// Charge le document
	$rdoc = doc_documents_get($id, 0, false, '', false);
	if( !ria_mysql_num_rows($rdoc) ) return false;
	$doc = ria_mysql_fetch_array($rdoc);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( !is_numeric($doc['pos']) ) return false;

	// S'assure que le document n'est pas déjà en fin de la liste
	$rnext = ria_mysql_query('select doc_id from doc_documents where doc_tnt_id='.$config['tnt_id'].' and doc_pos='.($doc['pos']+1).' and doc_type_id='.$doc['type_id']);
	if( !ria_mysql_num_rows($rnext) ) return false;

	// Permute le document avec celui qui se trouve juste au dessous
	ria_mysql_query('update doc_documents set doc_pos='.($doc['pos']+1).' where doc_tnt_id='.$config['tnt_id'].' and doc_id='.$id.' and doc_type_id='.$doc['type_id']);
	ria_mysql_query('update doc_documents set doc_pos='.($doc['pos']).' where doc_tnt_id='.$config['tnt_id'].' and doc_pos='.($doc['pos']+1).' and doc_id!='.$id.' and doc_type_id='.$doc['type_id']);

	return true;
}

/**	Permet le déplacement vers le bas d'un type de document.
 *
 *	@param int $id Document à déplacer vers le bas
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_type_documents_move_down( $id ){
	global $config;
	if( !is_numeric($id) ) return false;

	// Charge le document
	$rdoc = doc_types_get($id);
	if( !ria_mysql_num_rows($rdoc) ) return false;
	$doc = ria_mysql_fetch_array($rdoc);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( !is_numeric($doc['pos']) ) return false;

	// S'assure que le document n'est pas déjà en fin de la liste
	$rnext = ria_mysql_query('select type_id from doc_types where type_tnt_id='.$config['tnt_id'].' and type_pos='.($doc['pos']+1));
	if( !ria_mysql_num_rows($rnext) ) return false;

	// Permute le document avec celui qui se trouve juste au dessous
	ria_mysql_query('update doc_types set type_pos='.($doc['pos']+1).' where type_tnt_id='.$config['tnt_id'].' and type_id='.$id);
	ria_mysql_query('update doc_types set type_pos='.($doc['pos']).' where type_tnt_id='.$config['tnt_id'].' and type_pos='.($doc['pos']+1).' and type_id!='.$id);

	return true;
}

/**	Permet le déplacement vers le haut d'un type de document.
 *
 *	@param int $id type de document à déplacer vers le haut
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_type_documents_move_up( $id ){
	global $config;
	if( !is_numeric($id) ) return false;

	// Charge le document
	$rdoc = doc_types_get($id);
	if( !ria_mysql_num_rows($rdoc) ) return false;
	$doc = ria_mysql_fetch_array($rdoc);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( !is_numeric($doc['pos']) ) return false;

	// S'assure que la catégorie n'est pas déjà la première de la liste
	if( $doc['pos']===0 ) return false;

	// Permute la catégorie avec celle qui se trouve juste au dessus
	ria_mysql_query('update doc_types set type_pos='.($doc['pos']-1).' where type_tnt_id='.$config['tnt_id'].' and type_id='.$id);
	ria_mysql_query('update doc_types set type_pos='.($doc['pos']).' where type_tnt_id='.$config['tnt_id'].' and type_pos='.($doc['pos']-1).' and type_id!='.$id);

	return true;
}

/** Cette fonction permet d'indexer un document.
 *	@param int $doc Optionnel, identifiant d'un document
 *	@return bool True si l'indexation s'est correctement déroulée, False dans le cas contraire
 */
function doc_documents_add_index( $doc=0 ){
	global $config;

	if( !is_numeric($doc) || $doc < 0 ){
		return false;
	}

	$rdoc = doc_documents_get($doc, 0, null, '', false);
	if( !$rdoc || !ria_mysql_num_rows($rdoc) ){
		return false;
	}

	while( $doc = ria_mysql_fetch_array($rdoc) ){
		// Indexation du type de document
		$cid = search_index_content(
			'/download/dl.php?doc='.$doc['id'], 'doc',
			$doc['name'].' - '.$doc['type_name'], $doc['desc'], $doc['name'].' '.$doc['desc'].' '.$doc['filename'],
			'/admin/documents/edit.php?doc='.$doc['id'].'&type='.$doc['type_id'], 1, $doc['id'], $doc['type_id']
		);
		if( !$cid ){
			return false;
		}

		ria_mysql_query('
			update doc_documents
			set doc_cnt_id='.$cid.'
			where doc_tnt_id='.$config['tnt_id'].'
				and doc_id='.$doc['id']
		);
	}

	return true;
}

/**	Cette fonction met à jour la date de dernière modification d'un document.
 *	@param int $id Identifiant du document.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function doc_documents_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update doc_documents
		set doc_date_modified = now()
		where doc_tnt_id = '.$config['tnt_id'].' and doc_id = '.$id.'
	';

	return ria_mysql_query($sql);

}

/** Cette fonction met a jour l'information pour savoir si la preview d'un document doit être ou non reconstruit
 *	@param int $id Identifiant du document.
 *	@param bool $rebuild Obligatoire, par défaut la preview devra être recontruit, mettre False pour que ce ne soit pas le cas
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function doc_document_set_rebuild_preview($id, $rebuild=true){
	if( !is_numeric($id) || $id <= 0){
		return false;
	}

	global $config;

	$sql ='
		update doc_documents
		set doc_preview_rebuild = '.( $rebuild ? '1' : '0' ).'
		where doc_tnt_id = '.$config['tnt_id'].'
			and doc_id = '.$id.'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet la récupération du md5 par défaut du document
 * @param int $doc_id Obligatoire, identifiant du document
 * @return string|bool le md5 en cas de succès, false sinon
 */
function doc_documents_get_md5($doc_id){
	if (!is_numeric($doc_id) && $doc_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select doc_md5_content
		from doc_documents
		where doc_tnt_id = '.$config['tnt_id'].'
			and doc_id = '.$doc_id.'
	';

	$result = ria_mysql_query($sql);

	if (!$result){
		return false;
	}

	return ria_mysql_result($result, 0);
}

/** Cette fonction permet l'affectation du md5 par défaut du document
 * @param int $doc_id Obligatoire, identifiant du document
 * @param string $md5 Obligatoire, md5 du document
 * @return bool true en cas de succès, false sinon
 */
function doc_documents_set_md5($doc_id, $md5){
	if (!is_numeric($doc_id) && $doc_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update doc_documents
		set doc_md5_content = "'.$md5.'", doc_date_modified = now()
		where doc_id = '.$doc_id.'
			and doc_tnt_id = '.$config['tnt_id'].'
	';

	return ria_mysql_query($sql);
}

/** Permet l'ajout d'un site à un document
 *
 *	@param int $doc Obligatoire, identifiant du document
 *	@param int $wst Obligatoire, identifiant du site
 *	@param string $lng Facultatif, code langue du document
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_websites_add( $doc, $wst, $lng=false ){
	global $config;
	if( doc_websites_exists($doc, $wst) ) return true;

	$lng = !$lng || !in_array($lng, $config['i18n_lng_used']) ? $config['i18n_lng'] : $lng;

	if ($lng == $config['i18n_lng']){
		$doc_url = $config['doc_dir'].'/'.$doc;
	} else {
		$doc_url = $config['doc_dir'].'/'.$lng.'-'.$doc;
		if (!file_exists($doc_url)){
			$doc_url = $config['doc_dir'].'/'.$doc;
		}
	}

	$md5_code = 'NULL';
	if (file_exists($doc_url)){
		$content = file_get_contents($doc_url);
		$md5_code = '"'.md5($content).'"';
	}

	$sql = '
		insert into doc_websites
			( dw_tnt_id, dw_doc_id, dw_wst_id, dw_lng_code, dw_md5_content )
		values
			( '.$config['tnt_id'].', '.$doc.', '.$wst.', \''.$lng.'\', '.$md5_code.' )
	';

	return ria_mysql_query($sql);
}

/** Permet de récupérer les sites associés à un document
 *	@param $doc Obligatoire, identifiant du document
 *
 *	@return resource un résultat de requête MySql en cas de succès, false en cas d'échec
 */
function doc_websites_get( $doc ){
	global $config;

	$sql = 'select dw_doc_id as doc, dw_wst_id as wst from doc_websites where dw_tnt_id='.$config['tnt_id'].' and dw_doc_id = '.$doc;

	return ria_mysql_query($sql);
}

/** Permet la suppression d'un document relier a un site web
 *	@param int $doc Obligatoire, identifiant du document
 *	@param int $wst Optionnel, identifiant du site
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_websites_del( $doc, $wst=0 ){
	if( !is_numeric($wst) || $wst<0 ) return false;

	global $config;

	$sql = '
		delete from doc_websites
		where dw_tnt_id='.$config['tnt_id'].'
			and	dw_doc_id = '.$doc.'
			'.( $wst>0 ? 'and dw_wst_id = '.$wst : '' ).'
	';

	return ria_mysql_query($sql);
}

/** cette fonction permet de savoir si un document existe pour un site
 *	@param int $website Obligatoire, identifiant du site
 *	@param int $doc Obligatoire, identifiant du document
 *	@param string $lng Facultatif, code ISO d'une langue
 *
 *	@return bool True si le document existe pour ce site, False s'il n'existe pas
 */
function doc_websites_exists( $website, $doc, $lng=false ){
	if( !is_numeric($website) || $website<0 ){
		return false;
	}

	if( !is_numeric($doc) || $doc<=0 ){
		return false;
	}

	global $config;
	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : false;

	$sql = '
		select dw_doc_id
		from doc_websites
		where dw_tnt_id = '.$config['tnt_id'].'
			and dw_doc_id = '.$doc.'
			'.( $website ? ' and dw_wst_id = '.$website : '' ).'
			'.( $lng	 ? ' and dw_lng_code = \''.$lng.'\'' : '' ).'
	';

	$result = ria_mysql_query($sql);

	return ria_mysql_num_rows($result)>0;
}

/** Permet la récupération du md5 associé au document pour un site particulier
 *	@param int $wst_id Obligatoire, identifiant du site
 *	@param int $doc_id Obligatoire, identifiant du document
 *	@param string $lng_code Obligatoire, code de la langue
 *	@return string|bool le md5 en cas de succès, false en cas d'échec
 */
function doc_websites_get_md5($wst_id, $doc_id, $lng_code=false){
	if (!is_numeric($doc_id) || $doc_id <= 0 ){
		return false;
	}
	if (!is_numeric($wst_id) || $wst_id <= 0 ){
		return false;
	}
	if ($lng_code && !i18n_languages_exists($lng_code)){
		return false;
	}

	global $config;

	if (!$lng_code){
		$lng_code = $config['i18n_lng'];
	}

	$sql = '
		select dw_md5_content
		from doc_websites
		where dw_tnt_id = '.$config['tnt_id'].'
			and dw_wst_id = '.$wst_id.'
			and dw_doc_id = '.$doc_id.'
			and dw_lng_code = "'.$lng_code.'"
	';

	$result = ria_mysql_query($sql);

	if (!$result || !ria_mysql_num_rows($result)){
		return false;
	}

	return ria_mysql_result($result, 0);
}

/** Cette fonction permet l'affectation du md5 du document en fonction de sa langue
 * @param int $wst_id Obligatoire, identifiant du site
 * @param int $doc_id Obligatoire, identifiant du document
 * @param string $lng_code Obligatoire, code de la langue
 * @param string $md5 Obligatoire, clé md5 du document
 * @return bool true en cas de succès, false sinon
 */
function doc_websites_set_md5($wst_id, $doc_id, $lng_code, $md5){
	if (!is_numeric($doc_id) && $doc_id <= 0 ){
		return false;
	}
	if (!is_numeric($wst_id) && $wst_id <= 0 ){
		return false;
	}
	if (!i18n_languages_exists($lng_code)){
		return false;
	}

	global $config;

	$sql = '
		update doc_websites
		set dw_md5_content = "'.$md5.'"
		where dw_doc_id = '.$doc_id.'
			and dw_tnt_id = '.$config['tnt_id'].'
			and dw_wst_id = '.$wst_id.'
			and dw_lng_code = "'.$lng_code.'"
	';

	return ria_mysql_query($sql);
}

/// @}

/** \defgroup model_documents_objects Liens entre les documents et les instances de classes
 *	Ce module comprend les fonctions nécessaires à la gestion des liaisons entre les documents
 *	et les objets (instances) de classes.
 *	A terme, ce module a vocation à remplacer "doc_products", "doc_prd_categories" et "doc_erratums"
 * @{
 */

/**	Cette fonction crée un lien entre un document et une instance de classe
 *	L'existence de l'instance pour la classe donnée n'est pas vérifiée (cela nécessiterait un contrôle spécifique à chaque classe)
 *	@param int $doc_id Obligatoire, identifiant du document
 *	@param int $cls_id Obligatoire, identifiant de la classe de l'instance
 *	@param int|array $obj_id Obligatoire, identifiant de l'instance (tableau pour les clés multiples, comme "ord_products")
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function doc_objects_add( $doc_id, $cls_id, $obj_id ){
	if( !doc_documents_exists( $doc_id ) ){
		return false;
	}

	if( !fld_classes_exists( $cls_id ) ){
		return false;
	}

	if( is_array( $obj_id ) ){
		if( !sizeof( $obj_id ) || sizeof( $obj_id ) > COUNT_OBJ_ID ){
			return false;
		}
		foreach( $obj_id as $one_id ){
			if( !is_numeric( $one_id ) || $one_id <= 0 ){
				return false;
			}
		}
	}elseif( !is_numeric( $obj_id ) || $obj_id <= 0 ){
		return false;
	}else{
		$obj_id = array( $obj_id );
	}

	if( doc_objects_exists($doc_id, $cls_id, $obj_id) ){
		return true;
	}

	global $config;

	$fields = array();
	$fields[ 'dob_tnt_id' ] = $config['tnt_id'];
	$fields[ 'dob_doc_id' ] = $doc_id;
	$fields[ 'dob_cls_id' ] = $cls_id;
	for( $i = 0; $i < sizeof( $obj_id ); $i++ ){
		$fields[ 'dob_obj_id_'.$i ] = $obj_id[ $i ];
	}

	$sql = 'insert into doc_objects ('.implode( ', ', array_keys( $fields ) ).') values ('.implode( ', ', array_values( $fields ) ).')';

	$r = ria_mysql_query( $sql );
	if( !$r ){
		return false;
	}

	fld_objects_set_date_modified( $cls_id, $obj_id );

	return true;
}

/**	Cette fonction vérifie l'existence d'un lien entre un document et une instance de classe, ou la présence de liens pour un document
 *	Le paramètre $obj_id est obligatoire si $cls_id est spécifié. Si $cls_id n'est pas spécifié, le paramètre $obj_id n'est pas pris en compte.
 *	@param int $doc_id Obligatoire, identifiant du document
 *	@param int $cls_id Optionnel, identifiant de la classe de l'instance
 *	@param int|array $obj_id Optionnel, identifiant de l'instance (tableau pour les clés multiples, comme "ord_products")
 *
 *	@return bool True si le (ou les) lien(s) existe(nt), false sinon
 */
function doc_objects_exists( $doc_id, $cls_id=0, $obj_id=0 ){
	if( !is_numeric( $doc_id ) || $doc_id <= 0 ){
		return false;
	}
	if( is_numeric( $cls_id ) && $cls_id > 0 ){
		if( is_array( $obj_id ) ){
			if( !sizeof( $obj_id ) || sizeof( $obj_id ) > COUNT_OBJ_ID ){
				return false;
			}
			foreach( $obj_id as $one_id ){
				if( !is_numeric( $one_id ) || $one_id <= 0 ){
					return false;
				}
			}
		}elseif( !is_numeric( $obj_id ) || $obj_id <= 0 ){
			return false;
		}else{
			$obj_id = array( $obj_id );
		}
	}else{
		$cls_id = 0;
	}

	global $config;

	$sql = '
		select 1 from doc_objects
		where dob_tnt_id = '.$config['tnt_id'].'
		and dob_doc_id = '.$doc_id.'
	';

	if( $cls_id ){
		$sql .= ' and dob_cls_id = '.$cls_id;
		for( $i = 0; $i < sizeof( $obj_id ); $i++ ){
			$sql .= ' and dob_obj_id_'.$i.' = '.$obj_id[ $i ];
		}
	}

	$r = ria_mysql_query( $sql );
	if( !$r ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql );
		}
		return false;
	}

	return ria_mysql_num_rows( $r ) > 0;
}

/**	Cette fonction supprime un lien entre un document et une instance de classe, ou tous les liens d'un document, ou tous les liens d'une instance
 *	Le paramètre $obj_id est obligatoire si $cls_id est spécifié. Si $cls_id n'est pas spécifié, le paramètre $obj_id n'est pas pris en compte.
 *	L'appel sans arguments n'est pas autorisé (cela reviendrait à une vidage de la table)
 *	@param int $doc_id Optionnel, identifiant du document
 *	@param int $cls_id Optionnel, identifiant de la classe de l'instance
 *	@param int|array $obj_id Optionnel, identifiant de l'instance (tableau pour les clés multiples, comme "ord_products")
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function doc_objects_del( $doc_id=0, $cls_id=0, $obj_id=0 ){

	$no_doc = $no_object = false;

	if( is_numeric( $doc_id ) && $doc_id > 0 ){
		if( !doc_documents_exists( $doc_id ) ){
			return false;
		}
	}else{
		$no_doc = true;
	}

	if( is_numeric( $cls_id ) && $cls_id > 0 ){
		if( !fld_classes_exists( $cls_id ) ){
			return false;
		}
		// autorise 0 comme identifiant (pas parfait)
		$obj_id = control_array_integer( $obj_id, true, true, true );
		if( $obj_id === false ){
			return false;
		}
	}else{
		$no_object = true;
	}

	if( $no_doc && $no_object ){
		return false;
	}

	global $config;

	$sql = '
		delete from doc_objects where dob_tnt_id = '.$config['tnt_id'].'
	';
	if( $doc_id ){
		$sql .= ' and dob_doc_id = '.$doc_id;
	}
	if( $cls_id ){
		$sql .= ' and dob_cls_id = '.$cls_id;
		for( $i = 0; $i < sizeof( $obj_id ); $i++ ){
			$sql .= ' and dob_obj_id_'.$i.' = '.$obj_id[ $i ];
		}
	}

	// récupère les objets impactés par cette suppression
	$sql_get = '
		select distinct dob_cls_id as "cls", dob_obj_id_0 as "id", dob_obj_id_1 as "id1", dob_obj_id_2 as "id2"
		from doc_objects
		where dob_tnt_id = '.$config['tnt_id'].' and dob_doc_id = '.$doc_id.'
	';
	for( $i = 0; $i < sizeof( $obj_id ); $i++ ){
		$sql_get .= ' and dob_obj_id_'.$i.' = '.$obj_id[ $i ];
	}

	$rget = ria_mysql_query($sql_get);

	$r = ria_mysql_query( $sql );
	if( !$r ){
		return false;
	}

	if( $rget ){
		while( $get = ria_mysql_fetch_assoc($rget) ){
			fld_objects_set_date_modified( $get['cls'], array($get['id'],$get['id1'],$get['id2']) );
		}
	}

	return true;

}

/**	Cette fonction récupère les liens entre des documents et des objets
 *	Le paramètre $obj_id n'est pas pris en compte si $cls_id n'est pas spécifié.
 *	@param int $doc_id Optionnel, identifiant d'un document ou tableau d'identifiants de documents
 *	@param int $cls_id Optionnel, identifiant d'une classe
 *	@param int|array $obj_id Optionnel, identifiant d'un objet, ou tableau d'identifiants pour les clés composites (ex : ord_products)
 *	@param int $type_id Optionnel, identifiant d'un type de document
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- doc_id : Identifiant du document
 *		- cls_id : Identifiant de la classe
 *		- obj_id_X : identifiant de l'objet, ou "X" est l'indice de base 0
 *		- cls_tnt_id : identifiant du tenant de la classe
 *		- cls_name : nom de la classe
 *		- doc_filename : nom du document (fichier)
 *		- doc_size : taille du document
 *		- doc_name : nom du document (intitulé)
 *		- doc_desc : description du document
 *		- type_id : identifiant du type de document
 */
function doc_objects_get( $doc_id=0, $cls_id=0, $obj_id=0, $type_id=0 ){

	$doc_id = control_array_integer( $doc_id, false );
	if( $doc_id === false ){
		return false;
	}

	if( !is_numeric( $cls_id ) || $cls_id < 0 ){
		return false;
	}elseif( $cls_id ){
		$obj_id = control_array_integer( $obj_id, false, true, true );
		if( $obj_id === false ){
			return false;
		}
	}

	if( !is_numeric($type_id) || $type_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select doc_id, dob_cls_id as cls_id, dob_obj_id_0 as obj_id_0, dob_obj_id_1 as obj_id_1, dob_obj_id_2 as obj_id_2,
		cls_tnt_id, cls_name, doc_filename, doc_size, doc_name, doc_desc, doc_type_id as type_id
		from doc_objects
			join fld_classes on (dob_cls_id=cls_id and dob_tnt_id=if(cls_tnt_id=0,'.$config['tnt_id'].',cls_tnt_id))
			join doc_documents on (dob_tnt_id=doc_tnt_id and dob_doc_id=doc_id and doc_is_deleted=0)
		where dob_tnt_id = '.$config['tnt_id'].'
	';
	if( sizeof( $doc_id ) ){
		$sql .= ' and doc_id in ('.implode( ', ', $doc_id ).')';
	}
	if( $cls_id ){
		$sql .= ' and dob_cls_id = '.$cls_id;
		for( $i = 0; $i < sizeof( $obj_id ); $i++ ){
			$sql .= ' and dob_obj_id_'.$i.' = '.$obj_id[ $i ];
		}
	}
	if( $type_id ){
		$sql .= ' and doc_type_id='.$type_id;
	}

	$r = ria_mysql_query( $sql );
	if( !$r ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql );
		}
		return false;
	}

	return $r;
}

/** Cette fonction permet de récupérer la liste des notes pour un ou plusieurs objets
 *	@param int $cls Obligatoire, identifiant de la classe conserné
 *	@param int|array $obj Obligatoire, identifiant de l'objet ou tableau d'identifiants pour les clés composées ou tableau de tableau d'identifiants pour les clés composées
 *	@param bool $multi_key Facultatif, permet de dire si le paramètre obj est un tableau de tableau de clé ou seulement un tableau d'id
 *	@return bool false en cas d'échec ou un résultat de ria_mysql_query avec les colonnes suivantes :
 *		- doc_id : identifiant du document
 *		- cls_id : identifiant de la classe de l'objet
 *		- obj_id_0 : identifiant de l'objet
 *		- obj_id_1 : identifiant de l'objet (clé composée - 0 sinon)
 *		- obj_id_2 : identifiant de l'objet (clé composée - 0 sinon)
 */
function doc_objects_get_all($cls, $obj, $multi_key=false){
	if( !is_numeric($cls) || $cls <= 0 ){
		return false;
	}

	if( $multi_key ){
		if( !is_array($obj) ){
			return false;
		}
		foreach( $obj as $k => $o ){
			$obj[$k] = control_array_integer( $o, true );
			if( $obj[$k] === false ){
				return false;
			}elseif( !$obj[$k][0] ){
				return false;
			}
		}
	}else{
		if( is_numeric($obj) ){
			$obj = array($obj);
		}

		$obj = control_array_integer( $obj, true );
		if( $obj === false ){
			return false;
		}elseif( !$obj[0] ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			dob_doc_id as doc_id, dob_cls_id as cls_id, dob_obj_id_0 as obj_id,
			dob_obj_id_0 as obj_id_0, dob_obj_id_1 as obj_id_1, dob_obj_id_2 as obj_id_2
		from
			doc_objects
		join fld_classes on (dob_cls_id=cls_id and dob_tnt_id=if(cls_tnt_id=0,'.$config['tnt_id'].',cls_tnt_id))
		join doc_documents on (dob_tnt_id=doc_tnt_id and dob_doc_id=doc_id and doc_is_deleted=0)
		where dob_tnt_id = '.$config['tnt_id'].' and dob_cls_id = '.$cls.'
	';

	if( $multi_key ){
		$wheres = array();
		$wheres_single = array();
		foreach( $obj as $o ){
			if( sizeof( $o ) == 1 ){
				$wheres_single[] = $o[0];
			}else{
				$where = ' dob_obj_id_0 = '.$o[0];
				if( isset($o[1]) ){
					$where .= ' and dob_obj_id_1 = '.$o[1];
				}
				if( isset($o[2]) ){
					$where .= ' and dob_obj_id_2 = '.$o[2];
				}
				$wheres[] = $where;
			}
		}
		if( sizeof( $wheres_single ) ){
			$sql .= ' and dob_obj_id_0 in ('.implode(',', $wheres_single).')';
		}else{
			$sql .= ' and (('.implode(') or (', $wheres).'))';
		}
	}else{
		$sql .= ' and dob_obj_id_0 = '.$obj[0];
		if( isset($obj[1]) ){
			$sql .= ' and dob_obj_id_1 = '.$obj[1];
		}
		if( isset($obj[2]) ){
			$sql .= ' and dob_obj_id_2 = '.$obj[2];
		}
	}

	return ria_mysql_query($sql);
}

/// @}

/** \defgroup model_documents_products Liens entre les documents et les produits
 *	Ce module comprend les fonctions nécessaires à la gestion des liaisons entre les documents
 *	et les produits du catalogue.
 * @{
 */

/**	Cette fonction permet l'ajout d'un lien entre un document et un produit.
 *	@param int $doc Identifiant du document à relier au produit
 *	@param int $prd Identifiant du produit à relier au document
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_products_add( $doc, $prd ){
	return doc_objects_add( $doc, CLS_PRODUCT, $prd );
}

/**	Cette fonction permet la vérification de la présence d'un lien entre un document et un produit donné.
 *	@param int $doc Identifiant du document
 *	@param int $prd Identifiant du produit
 *	@return bool true si le lien existe, false si le lien n'existe pas
 */
function doc_products_exists( $doc, $prd ){
	return doc_objects_exists( $doc, CLS_PRODUCT, $prd );
}

/**	Cette fonction est une variante de \c doc_products_add, elle permet l'ajout d'une relation entre
 *	un document et un produit, mais par la référence du produit.
 *	@param int $doc Identifiant du document à relier au produit
 *	@param int $prd Référence du produit à relier au document
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_products_add_ref( $doc, $prd ){
	if( !doc_documents_exists($doc) ){
		return false;
	}
	$prd = trim($prd);
	if( $prd == '' ){
		return false;
	}

	$rp = prd_products_get_simple( 0, $prd );
	if( !$rp || !ria_mysql_num_rows($rp) ){
		return false;
	}

	$p = ria_mysql_fetch_array( $rp );
	return doc_objects_add( $doc, CLS_PRODUCT, $p['id'] );
}

/**	Cette fonction permet la suppression d'un lien entre un document et un produit donné.
 *	@param int $doc Identifiant du document
 *	@param int $prd Identifiant du produit
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_products_del( $doc, $prd ){
	return doc_objects_del( $doc, CLS_PRODUCT, $prd );
}

/**	Cette fonction permet le chargement des relations entre des documents et des produits.
 *	@param int $doc Optionnel, identifiant du document sur lequel filtrer le résultat
 *	@param int $prd Optionnel, identifiant du produit sur lequel filtrer le résultat
 *	@param $type Optionnel, type de document sur lequel filtrer le résultat
 *	@param $sort Optionnel, tableau de trie
 *	@param $website Optionnel, identifiant d'un site web
 *	@param string $lng Optionnel, permet de filtrer sur une langue (via son code ISO)
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- doc_id : identifiant du document
 *			- doc_name : désignation du document
 *			- doc_filename : nom virtuel du fichier
 *			- doc_size : taille du fichier, en octet
 *			- doc_type_id : identifiant du type de document
 *			- doc_type_name : désignation du type de document
 *			- prd_id : identifiant du produit
 *			- prd_ref : référence du produit
 *			- prd_name : désignation du produit
 *			- prd_is_sync : booléen indiquant si le produit est synchronisé avec la gestion commerciale
 *			- doc_ext : extension du fichier source
 */
function doc_products_get( $doc=0, $prd=0, $type=0, $sort=array(), $website=false, $lng=false ){
	global $config;

	$sql = '
		select doc_id, doc_name, doc_filename, doc_size, doc_desc,
			doc_type_id, type_name as doc_type_name,
			prd_id, prd_ref, prd_name, prd_is_sync,
			if( substring_index(doc_filename, ".", -1) != doc_filename, substring_index(doc_filename, ".", -1), "") as doc_ext
		from doc_objects
			join doc_documents on ( dob_tnt_id = doc_tnt_id and dob_doc_id = doc_id and doc_is_deleted=0 )
			join doc_types on ( doc_tnt_id = type_tnt_id and doc_type_id = type_id )
			join prd_products on ( dob_tnt_id = prd_tnt_id and dob_obj_id_0 = prd_id )
		where dob_tnt_id = '.$config['tnt_id'].' and dob_cls_id='.CLS_PRODUCT.'
			and prd_date_deleted is null
	';

	if( is_numeric($doc) && $doc>0 ){
		$sql .=' and dob_doc_id='.$doc;
	}

	if( is_numeric($prd) && $prd>0 ){
		$sql .=' and dob_obj_id_0='.$prd;
	}

	if( is_numeric($type) && $type>0 ){
		$sql .=' and doc_type_id='.$type;
	}

	// filtres optionnels par langue et website
	if( ( is_numeric($website) && $website>0 ) || ( $lng!==false && in_array(strtolower(trim($lng)), $config['i18n_lng_used']) ) ){
		$sql .= ' and exists (
			select 1 from doc_websites
			where dw_tnt_id = '.$config['tnt_id'].' and dw_doc_id = dob_doc_id
		';
		if( is_numeric($website) && $website>0 ){
			$sql .= ' and dw_wst_id = '.$website;
		}
		if( $lng!==false && in_array(strtolower(trim($lng)), $config['i18n_lng_used']) ){
			$sql .= ' and dw_lng_code = "'.addslashes(strtolower(trim($lng))).'"';
		}
		$sql .= ' )';
	}

	if( is_numeric($prd) && $prd>0 ){
		$sql .= ' group by doc_id ';
	}

	// Tri du résultat (valeurs par défaut)
	if( $sort==false || !is_array($sort) || sizeof($sort)==0 ){
		$sort = array( 'type_name'=>'asc', 'doc_name'=>'asc', 'prd_ref'=>'asc' );
	}

	// Converti le paramètre de tri en SQL
	$sort_final = array();

	// Récupère un éventuel tri par prix
	foreach( $sort as $col=>$dir ){
		switch( $col ){
			case 'type_pos' :
				array_push ($sort_final, 'type_pos '.$dir );
				break;
			case 'type_name' :
				array_push ($sort_final, 'type_name '.$dir );
				break;
			case 'doc_name':
				array_push( $sort_final, 'doc_name '.$dir );
				break;
		}
	}

	// Ajoute la clause de tri
	if( sizeof($sort_final)==0 ){
		$sort_final = array( 'prd_name asc' );
	}

	$sql .= ' order by '.implode( ', ', $sort_final ).' ';

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
	}

	return $r;
}

/// @}

/** \defgroup model_documents_prd_categories Liens entre les documents et les catégories de produits
 *	Ce module comprend les fonctions nécessaires à la gestion des liaisons entre les documents
 *	et les catégories de produits du catalogue.
 * @{
 */

/**	Cette fonction permet l'ajout d'un lien entre un document et une catégorie de produits.
 *	@param int $doc Identifiant du document
 *	@param int $cat Identifiant de la catégorie de produits
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_prd_categories_add( $doc, $cat ){
	return doc_objects_add( $doc, CLS_CATEGORY, $cat );
}

/** Cette fonction permet la vérification de la présence d'un lien entre un document et une catégorie
 *	de produits donnée.
 *	@param int $doc Identifiant du document
 *	@param int $cat Identifiant de la catégorie de produits
 *	@return bool true si un lien existe, false dans le cas contraire
 */
function doc_prd_categories_exists( $doc, $cat ){
	return doc_objects_exists( $doc, CLS_CATEGORY, $cat );
}

/**	Cette fonction permet la suppression d'un lien entre un document et une catégorie de produits donnée.
 *	@param int $doc Identifiant du document
 *	@param int $cat Identifiant de la catégorie de produits
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_prd_categories_del( $doc, $cat ){
	return doc_objects_del( $doc, CLS_CATEGORY, $cat );
}

/**	Cette fonction permet le chargement d'un ou plusieurs liens entre des documents et des catégories de
 *	produits. Le résultat est filtré avec les paramètres optionnels fournis.
 *	@param int $doc Optionnel, Identifiant d'un document sur lequel filtrer le résultat
 *	@param int $cat Optionnel, Identifiant d'une catégorie de produits sur laquelle filtrer le résultat
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- doc_id : identifiant du document
 *			- doc_name : désignation du document
 *			- cat_id : identifiant de la catégorie
 *			- cat_title : désignation de la catégorie
 *			- cat_publish : booléen indiquant si la catégorie est publiée ou non
 */
function doc_prd_categories_get( $doc=0, $cat=0 ){
	global $config;

	$sql = '
		select doc_id, doc_name, cat_id, if(cat_title!="",cat_title,cat_name) as cat_title, cat_publish
		from doc_objects
			join doc_documents on (dob_tnt_id=doc_tnt_id and dob_doc_id=doc_id and doc_is_deleted=0)
			join prd_categories on (dob_tnt_id=cat_tnt_id and dob_obj_id_0=cat_id)
		where dob_tnt_id = '.$config['tnt_id'].' and dob_cls_id='.CLS_CATEGORY.'
	';

	if( is_numeric($doc) && $doc>0 ){
		$sql .= ' and dob_doc_id='.$doc;
	}

	if( is_numeric($cat) && $cat>0 ){
		$sql .= ' and dob_obj_id_0='.$cat;
	}

	$sql .= '
		group by doc_id
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction est chargée de retourner tous les documents rencontrés dans une catégorie de produits donnée.
 *	@param int $cat Obligatoire, Identifiant de la catégorie
 *	@param int $type Optionnel, identifiant d'un type de fichier sur lequel filtrer le résultat
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du document
 *			- filename : nom du fichier (virtuel)
 *			- size : poids du fichier, en octets
 *			- name : désignation du fichier
 *			- desc : description du fichier
 *			- type_id : identifiant du type du document
 *			- type_name : désignation du type de document
 */
function doc_prd_categories_documents_get( $cat, $type=0 ){
	global $config;

	$sql = '
		select doc_id as id, doc_filename as filename, doc_size as size, doc_name as name, doc_desc as "desc", type_id, type_name
		from doc_documents
			join doc_types on (doc_tnt_id=type_tnt_id and doc_type_id=type_id)
		where doc_tnt_id='.$config['tnt_id'].' and doc_is_deleted=0
			and (
				exists (
					select 1 from doc_objects
						join prd_classify on (dob_tnt_id=cly_tnt_id and dob_obj_id_0=cly_prd_id)
						join prd_cat_hierarchy on (cly_tnt_id=cat_tnt_id and cly_cat_id=cat_child_id)
					where dob_tnt_id='.$config['tnt_id'].' and dob_cls_id='.CLS_PRODUCT.'
						and cat_parent_id='.$cat.'
						and dob_doc_id=doc_id
				) or exists (
					select 1 from doc_objects
						join prd_classify on (dob_tnt_id=cly_tnt_id and dob_obj_id_0=cly_prd_id)
					where dob_tnt_id='.$config['tnt_id'].' and dob_cls_id='.CLS_PRODUCT.'
						and cly_cat_id='.$cat.'
						and dob_doc_id=doc_id
				) or exists (
					select 1 from doc_objects
					where dob_tnt_id='.$config['tnt_id'].' and dob_cls_id='.CLS_CATEGORY.'
						and dob_doc_id=doc_id
						and dob_obj_id_0='.$cat.'
				)
			)
	';

	if( is_numeric($type) && $type>0 ){
		$sql .= ' and doc_type_id='.$type;
	}

	$sql .= '
		order by doc_name
	';

	return ria_mysql_query($sql);
}

/// @}

/** \defgroup model_documents_erratums Liens entre les documents et les erratums
 *	Ce module comprend les fonctions nécessaires à la gestion des liaisons entre les documents
 *	et les erratums.
 * @{
 */

/**	Cette fonction permet l'ajout d'un lien entre un document et un erratum.
 *	@param int $doc Identifiant du document à relier a l'erratum
 *	@param int $err Identifiant de l'erratum à relier au document
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_erratums_add( $doc, $err ){
	return doc_objects_add( $doc, CLS_ERRATUM, $err);
}

/**	Cette fonction permet la vérification de la présence d'un lien entre un document et un erratum donné.
 *	@param int $doc Identifiant du document
 *	@param int $err Identifiant de l'erratum
 *	@return bool true si le lien existe, false si le lien n'existe pas
 */
function doc_erratums_exists( $doc, $err ){
	return doc_objects_exists( $doc, CLS_ERRATUM, $err );
}

/**	Cette fonction permet la suppression d'un lien entre un document et un erratum donné.
 *	@param int $doc Identifiant du document
 *	@param int $err Identifiant de l'erratum
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_erratums_del( $doc, $err ){
	return doc_objects_del( $doc, CLS_ERRATUM, $err );
}

/**	Cette fonction permet le chargement des relations entre des documents et des erratums.
 *	@param int $doc Facultatif, identifiant du document sur lequel filtrer le résultat
 *	@param int $err Facultatif, identifiant d'un erratum sur lequel filtrer le résultat
 *	@param int $type Facultatif, type de document sur lequel filtrer le résultat
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- doc_id : identifiant du document
 *			- doc_name : désignation du document
 *			- doc_filename : nom virtuel du fichier
 *			- doc_size : taille du fichier, en octet
 *			- doc_type_id : identifiant du type de document
 *			- doc_type_name : désignation du type de document
 *			- err_id : identifiant de l'erratum
 *			- prd_id : identifiant du produit
 *			- prd_ref : référence du produit
 *			- prd_name : désignation du produit
 *			- prd_is_sync : booléen indiquant si le produit est synchronisé avec la gestion commerciale
 */
function doc_erratums_get( $doc=0, $err=0, $type=0 ){
	global $config;

	$sql = '
		select
			doc_id, doc_name, doc_filename, doc_size,
			doc_type_id, type_name as doc_type_name,
			err_id, err_desc, prd_id, prd_ref, prd_name, prd_is_sync
		from doc_objects
			join doc_documents on (dob_tnt_id=doc_tnt_id and dob_doc_id=doc_id and doc_is_deleted=0)
			join doc_types on (doc_tnt_id=type_tnt_id and doc_type_id=type_id)
			join cat_erratums on (dob_tnt_id=err_tnt_id and dob_obj_id_0=err_id)
			join prd_products on (err_tnt_id=prd_tnt_id and err_prd_id=prd_id)
		where dob_tnt_id = '.$config['tnt_id'].' and dob_cls_id='.CLS_ERRATUM.'
			and prd_date_deleted is null
	';

	if( is_numeric($doc) && $doc>0 ){
		$sql .=' and dob_doc_id='.$doc;
	}

	if( is_numeric($err) && $err>0 ){
		$sql .=' and dob_obj_id_0='.$err;
	}

	if( is_numeric($type) && $type>0 ){
		$sql .=' and doc_type_id='.$type;
	}

	$sql .= '
		group by doc_id
		order by type_name, doc_name, prd_ref
	';

	return ria_mysql_query( $sql );
}
/// @}

/** \defgroup doc_downloads Téléchargement
 *	Ce module comprend les fonctions nécessaires à la suivi des téléchargements de documents.
 *	Il est dépendant des fonctions contenus dans le module Types de documents.
 * @{
 */

/** Cette fonction permet d'enregistrer un téléchargement de document par un compte client.
 *	L'une des deux informations suivantes doit être fournie, sinon la fonction retourne False sans enregistrer la demande de téléchargement
 *
 *	@param int $doc_id Obligatoire, identifiant d'un document
 *	@param int $usr_id Optionnel, identifiant d'un compte client
 *	@param string $email Optionnel, adresse mail de la personne ayant demandée le téléchargement du document
 *	@param string $firstname Optionnel, prénom de la personne ayant demandée le téléchargement du document
 *	@param string $lastname Optionnel, nom de la personne ayant demandée le téléchargement du document
 *	@param string $phone Optionnel, numéro de téléphone de la personne ayant demandée le téléchargement du document
 *
 *	@return bool True si l'enregistrement s'est correctement déroulée, False dans le cas contraire
 */
function doc_downloads_add( $doc_id, $usr_id=0, $email='', $firstname='', $lastname='', $phone='' ){
	if( !doc_documents_exists($doc_id) ){
		return false;
	}

	if( !is_numeric($usr_id) && $usr_id <= 0 && trim($email) == '' ){
		return false;
	}

	if( trim($email) != '' ){
		// On essaye de récupérer le compte client rattaché à cette adresse mail
		$r_user = gu_users_get( 0, $email );
		if( $r_user && ria_mysql_num_rows($r_user) != '' ){
			$user = ria_mysql_fetch_assoc( $r_user );
			$usr_id = $user['id'];
		}
	}else{
		if( !gu_users_exists($usr_id) ){
			return false;
		}

		$email = '';
	}

	global $config;

	$email 		= trim($email) != '' 	 ? '"'.addslashes( $email ).'"' 	: 'null';
	$firstname 	= trim($firstname) != '' ? '"'.addslashes( $firstname ).'"' : 'null';
	$lastname 	= trim($lastname) != ''  ? '"'.addslashes( $lastname ).'"' 	: 'null';
	$phone 		= trim($phone) != '' 	 ? '"'.addslashes( $phone ).'"' 	: 'null';

	$sql = '
		insert into doc_downloads
			( dwl_tnt_id, dwl_doc_id, dwl_usr_id, dwl_date, dwl_email, dwl_fistname, dwl_lastname, dwl_phone )
		values
			( '.$config['tnt_id'].', '.$doc_id.', '.$usr_id.', now(), '.$email.', '.$firstname.', '.$lastname.', '.$phone.' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet la création d'un document dans couchDb lorsqu'un utilisateur télécharge un document
 * @param int $doc_id Obligatoire, identifiant du document téléchargé
 * @return bool true en cas de succès, false en cas d'échec
 */
function doc_downloads_add_couchDb_document($doc_id){
	if (!is_numeric($doc_id) && $doc_id <= 0){
		return false;
	}

	global $config;

	$doc = ria_mysql_fetch_assoc(doc_documents_get($doc_id,0,null,'',false));
	$doc_md5_version = doc_websites_get_md5($config['wst_id'], $doc_id);

	$usr_id = '';
	$usr_firstname = 'Anonyme';
	$usr_lastname = '';
	$usr_society = '';
	$usr_email = '';

	if (gu_users_is_connected()) {
		$usr_id = $_SESSION['usr_id'];
		$usr_firstname = isset($_SESSION['usr_firstname']) ? $_SESSION['usr_firstname'] : '';
		$usr_lastname = isset($_SESSION['usr_lastname']) ? $_SESSION['usr_lastname'] : '';
		$usr_society = isset($_SESSION['usr_society']) ? $_SESSION['usr_society'] : '' ;
		$usr_email = $_SESSION['usr_email'];
	}

	$ar_couchDB = array(
		'doc_id' => $doc_id,
		'doc_name' => $doc['name'],
		'doc_md5_default' => $doc['md5_content'],
		'doc_md5_version' => $doc_md5_version,
		'doc_website' => $config['wst_id'],
		'doc_lng' => i18n::getLang(),
		'user_id' => $usr_id,
		'user_firstname' => $usr_firstname,
		'user_lastname' => $usr_lastname,
		'user_society' => $usr_society,
		'user_email' => $usr_email,
		'date_download' => time()
	);

	$couchDB = CouchDB::create(_COUCHDB_DOWNLOAD_DB_NAME);

	$reponse = $couchDB::add(CLS_DOCUMENTS_HISTO, $ar_couchDB);

	return $reponse['ok'];
}

/** Cette fonction permet la récupération via CouchDB des téléchargements
 * 	@param int $doc_id Obligatoire, identifiant du document pour lequel on veut récupérer l'historique
 * 	@param int $start Optionnel, ligne a laquelle débute la recherche (pour la pagination)
 * 	@param int $limit Optionnel, nombre d'éléments à renvoyer (pour la pagination)
 * 	@param $user_filter Optionnel, filtre pour définir le type d'utilisateur recherché. Trois valeurs sont possibles : all (tout les clients), anonymous (clients anonymes), identified (clients connectés)
 * 	@param $lng_filter Optionnel, filtre pour définir la langue dans lequel le document a été téléchargé
 * 	@return en cas de succès, le tableau des données renvoyées par CouchDB selon les critères fournis ayant les colonnes suivantes :
 * 		- doc_id : identifiant du document dans RiaShop
 * 		- doc_name : libellé du document
 * 		- doc_md5_default : md5 du contenu du document original
 * 		- doc_md5_version : md5 du contenu du document téléchargé (différent du md5 par défaut si langue différente de l'original)
 * 		- doc_website : identifiant du site (wst_id)
 * 		- doc_lng : identifiant de la langue
 * 		- user_id : identifiant de l'auteur du téléchargement. Si anonyme, alors cette valeure sera ""
 * 		- user_firstname : prénom de l'utilisateur
 * 		- user_lastname : nom de l'utilisateur
 * 		- user_society : nom de la société de l'utilisateur
 * 		- user_email : email de l'utilisateur
 * 		- date_download : date du téléchargement, au format timestamp
 * 		- _id : identifiant du document dans CouchDB
 * 	@return bool false en cas d'échec
 */
function doc_downloads_couchdb_get($doc_id, $start=0, $limit=25, $user_filter="all", $lng_filter='fr'){
	if (!is_numeric($doc_id) || $doc_id < 0){
		return false;
	}

	if (!is_numeric($start) || $start < 0){
		return false;
	}

	if (!is_numeric($limit) || $limit <= 0){
		return false;
	}

	global $config;


	{ //On compose le startkey et l'endkey dans l'ordre des clés dans la vue CouchDb. Il ne faut pas de valeur nulle pour une clé située entre deux autres.
		$params = array();

		$params['startkey'] = [$config['tnt_id'], $lng_filter, 0];
		$params['endkey'] = [$config['tnt_id'], $lng_filter, (time()+1)];

		$params['skip'] = $start;
		$params['include_docs'] = true;
		$params['descending'] = true;
		$params['reduce'] = false;

		switch($user_filter){
			case 'all' : {
				$view = 'all_downloads';
				break;
			}
			case 'anonymous' : {
				$view = 'anonymous_downloads';
				break;
			}
			case 'named' : {
				$view = 'authenticated_downloads';
				break;
			}
		}
	}

	if (!isset($view)){
		return false;
	}

	/* Construction de l'URL pour la récupération des données de la vue
	* Startkey et endkey pour le filtrage des données (entre xxx et yyy)
	* reduce et include_docs pour la récupération des documents, sans ces paramètres on n'aura que le nombre de documents
	* descending pour l'ordre des données (tri par ordre croissant : on ne met pas ce paramètre, tri par ordre décroissant : on met le paramètre et on inverse de place startkey et endkey)
	* skip pour la pagination
	* limit pour le nombre d'éléments
	*/

	if ($limit) {
		$params['limit'] = $limit;
	}

	$couchDB = CouchDB::create(_COUCHDB_DOWNLOAD_DB_NAME);

	$results = $couchDB::getView($view, $params);

	//On ne récupère que les content et les identifiants de documents dans CouchDb
	$final = array();
	foreach( $results as $r ){
		$tmp = $r['doc']['content'];
		$tmp['_id'] = $r['doc']['_id'];

		$final[] = $tmp;
	}

	return $final;
}

/** Cette fonction permet de récupérer le nombre de téléchargements pour un document en fonction des paramètres fournis
 * 	@param int $doc_id Obligatoire, identifiant du document dans RiaShop
 * 	@param string $user_filter Optionnel, filtre pour définir le type d'utilisateur recherché. Trois valeurs sont possibles : all (tout les clients), anonymous (clients anonymes), identified (clients connectés)
 * 	@param string $lng_filter Optionnel, filtre pour définir la langue dans lequel le document a été téléchargé
 * 	@return int le nombre de téléchargements en cas de succès, false en cas d'échec
 */
function doc_downloads_couchdb_get_count($doc_id, $user_filter="all", $lng_filter='fr'){
	if (!is_numeric($doc_id) || $doc_id < 0){
		return false;
	}

	global $config;

	$params = array();
	$params['startkey'] = [$config['tnt_id'], $lng_filter, 0];
	$params['endkey'] = [$config['tnt_id'], $lng_filter, (time()+1)];

	switch($user_filter){
		case 'all' : {
			$view = 'all_downloads';
			break;
		}
		case 'anonymous' : {
			$view = 'anonymous_downloads';
			break;
		}
		case 'named' : {
			$view = 'authenticated_downloads';
			break;
		}
	}

	$couchDB = CouchDB::create(_COUCHDB_DOWNLOAD_DB_NAME);

	return $couchDB::getViewCount($view, $params);
}

/// @}
/// @}


