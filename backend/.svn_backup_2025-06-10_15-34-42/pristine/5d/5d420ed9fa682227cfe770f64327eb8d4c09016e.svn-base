<?php

	/**	\file ajax-naf.php
	 * 
	 * 	Ce fichier est utilisé comme outil d'auto-complétion pour aider à la saisie d'un code NAF. Il n'est actuellement utilisé
	 *  que pour compléter la fiche du propriétaire de l'instance (d'où le droit d'accès associé).
	 * 
	 * 	Un seul paramètre est attendu :
	 *  - term : Obligatoire, début de recherche à auto-compléter
	 * 
	 * 	Le résultat est fourni au format json.
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_OWNER');

	require_once('sys.naf.inc.php');
	
	$result = array();
	if( isset($_GET['term']) && trim($_GET['term'])!='' ){
		$rnaf = sys_naf_codes_get( '', $_GET['term'] );
		if( $rnaf && ria_mysql_num_rows($rnaf) ){
			while( $naf = ria_mysql_fetch_array($rnaf) ){
				$result[] = $naf['code'].' - '.htmlspecialchars($naf['name']);
			}
		}
	}	

	print json_encode($result);
