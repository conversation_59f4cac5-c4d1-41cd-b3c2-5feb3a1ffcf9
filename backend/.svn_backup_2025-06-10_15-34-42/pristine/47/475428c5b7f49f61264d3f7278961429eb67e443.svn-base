{"interfaces": {"google.pubsub.v1.SchemaService": {"retry_codes": {"retry_policy_1_codes": ["UNAVAILABLE"], "retry_policy_4_codes": ["DEADLINE_EXCEEDED", "RESOURCE_EXHAUSTED", "ABORTED", "INTERNAL", "UNAVAILABLE"], "retry_policy_6_codes": ["UNAVAILABLE"], "no_retry_codes": [], "retry_policy_3_codes": ["UNKNOWN", "ABORTED", "UNAVAILABLE"], "retry_policy_2_codes": ["ABORTED", "CANCELLED", "INTERNAL", "RESOURCE_EXHAUSTED", "UNKNOWN", "UNAVAILABLE", "DEADLINE_EXCEEDED"], "retry_policy_5_codes": ["UNKNOWN", "ABORTED", "UNAVAILABLE"]}, "retry_params": {"retry_policy_1_params": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 60000}, "retry_policy_3_params": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 60000}, "retry_policy_6_params": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 60000}, "retry_policy_2_params": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 60000}, "retry_policy_4_params": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 900000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 900000, "total_timeout_millis": 900000}, "retry_policy_5_params": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 60000}, "no_retry_params": {"initial_retry_delay_millis": 0, "retry_delay_multiplier": 0.0, "max_retry_delay_millis": 0, "initial_rpc_timeout_millis": 0, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 0, "total_timeout_millis": 0}}, "methods": {"CreateSchema": {"timeout_millis": 60000, "retry_codes_name": "no_retry_codes", "retry_params_name": "no_retry_params"}, "GetSchema": {"timeout_millis": 60000, "retry_codes_name": "no_retry_codes", "retry_params_name": "no_retry_params"}, "ListSchemas": {"timeout_millis": 60000, "retry_codes_name": "no_retry_codes", "retry_params_name": "no_retry_params"}, "DeleteSchema": {"timeout_millis": 60000, "retry_codes_name": "no_retry_codes", "retry_params_name": "no_retry_params"}, "ValidateSchema": {"timeout_millis": 60000, "retry_codes_name": "no_retry_codes", "retry_params_name": "no_retry_params"}, "ValidateMessage": {"timeout_millis": 60000, "retry_codes_name": "no_retry_codes", "retry_params_name": "no_retry_params"}}}}}