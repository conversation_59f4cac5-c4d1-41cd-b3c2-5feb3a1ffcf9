<?php
require_once('documents.inc.php');
require_once('glossary.inc.php');

/// \ingroup view_admin
/// @{

/**	Cette fonction est responsable de la mise en forme d'un contenu multiligne.
 *	Les adresses email reconnues dans le corps du texte sont automatiquement activées,
 *	les différents paragraphes distingués par la balise p, et les listes transformées en listes html.
 *	Si le terme "nous contacter" est trouvé dans le corps du texte, il est remplacé par un lien vers
 *	le formulaire de contact, etc.
 *
 *	@param string $desc Description de l'article, à mettre en forme par la fonction.
 *	@param bool $glossary Optionnel, si true les termes du glossaire seront ajoutés.
 *	@return string La description mise en forme au format html.
 *
 */
function view_site_format_description( $desc, $glossary=true ){
	global $config;
	
	
	$desc = html_entity_decode2( html_strip_tags($desc) );
	if( !trim($desc) )
		return $desc;
	
	// Extrait les éventuelles balises code, pour empêcher leur échappement par htmlspecialchars
	$code_count = 0;
	if( preg_match_all( '/\<code\>(.*)\<\/code\>/sU', $desc, $codes, PREG_SET_ORDER ) ){
		foreach( $codes as $code ){
			$desc = str_replace( '<code>'.$code[1].'</code>', '<code'.$code_count.'>', $desc );
			$code_count++;
		}
	}

	// Echappement xhtml
	$desc = htmlspecialchars( $desc );
	$desc = nl2br( $desc );

	// Remet les balises code
	$code_count = 0;
	foreach( $codes as $code ){
		$desc = str_replace( '&lt;code'.$code_count.'&gt;', $code[1], $desc );
		$code_count++;
	}

	// Crée les paragraphes
	$desc = str_replace( "\r\n", "\n", $desc );
	$desc = '<p>'. str_replace( "\n\n", "</p>\n<p>", $desc ) .'</p>';
	// Active les adresses email
	$desc = preg_replace( '/([a-z0-9\.\-\_]+@[a-z0-9\-\_]+(\.[a-z0-9\-\_]+)+)/i', '<a href="mailto:\1">\1</a>', $desc );
	// Active les listes
	$desc = preg_replace( "/\n&middot;/", "\n-", $desc );
	$desc = preg_replace( "/<p>&middot;/", "<p>\n-", $desc );
	$desc = preg_replace( "/\n\-/", "\n- ", $desc );
	$desc = preg_replace( '/<p>\- ([^\n]+)\n/i', "\n<li>\\1</li>\n", $desc );
	$desc = preg_replace( '/\n\- ([^\n]+)<\/p>/i', "\n<li>\\1</li>\n", $desc );
	while( ($res = preg_replace( '/\n\- ([^\n]+)\n/i', "\n<li>\\1</li>\n", $desc ))!=$desc )
		$desc = $res;
	$desc = preg_replace( '/(<li>.+<\/li>)/', '</p><ul>\1</ul><p>', $desc );
	$desc = preg_replace( '/<\/ul>\n<ul>/', '', $desc );
	$desc = preg_replace( '/<p>\s+<\/p>/i', '', $desc );
	$desc = preg_replace( '/^<\/p>/i', '', trim($desc) );
	$desc = preg_replace( '/<p>$/i', '', $desc );
	$desc = preg_replace( '/<\/ul><ul>/', '', $desc );
	// Mettre en gras
	$desc = preg_replace( '/\*(.*)\*/U', '<strong>\1</strong>', $desc );
	// Mettre en italic
	$desc = preg_replace( '/_(.*)_/', '<em>\1</em>', $desc );
	// Remplace les occurences de "nous contacter" par un lien vers le formulaire de contact.
	$desc = preg_replace( '/(nous contacter)/i', '<a href="/contact">\1</a>', $desc );
	$desc = preg_replace( '/(prendre contact avec nous)/i', '<a href="/contact">\1</a>', $desc );
	// Remplace les occurences de "FAQ" par un lien vers la foire aux questions
	$desc = preg_replace( '/(\s|\.|,)(FAQ)(\s|\.|,)/', '\1<a href="/faq">\2</a>\3', $desc );
	// Remplace les liens sans http par un lien direct
	$desc = preg_replace( '/(www\.)([a-z0-9\-\_]+)(\.com)(\s|\.|,)/i', '<a href="http://\1\2\3">\1\2\3</a>\4', $desc );
	$desc = preg_replace( '/(www\.)([a-z0-9\-\_]+)(\.fr)(\s|\.|,)/i', '<a href="http://\1\2\3">\1\2\3</a>\4', $desc );
	//Remplace les headers
	$desc = preg_replace('/h1\.(.*)/','<h1>\1</h1>',$desc);
	$desc = preg_replace('/h2\.(.*)/','<h2>\1</h2>',$desc);
	$desc = preg_replace('/h3\.(.*)/','<h3>\1</h3>',$desc);
	$desc = preg_replace('/h4\.(.*)/','<h4>\1</h4>',$desc);
	$desc = preg_replace('/h5\.(.*)/','<h5>\1</h5>',$desc);
	//Remplace l'alignement a droite
	$desc = preg_replace('/right\.(.*)/','<div style="float:right">\1</div>', $desc);
	
	// Remplace les "sep."
	$desc = preg_replace( '/sep\./', '<div style="clear:both"></div><div style="margin-top:20px; height: 1px; width: 100%;	font-size:0px;	background-color: #D6D6D6;	display:block;"> </div>', $desc );


	// Traite les liens vers des produits
	if( preg_match_all( '/&lt;produit\:([^:]+):([^&]+)&gt;/', $desc, $links, PREG_SET_ORDER ) ){
		foreach( $links as $lnk ){
			$rprd = prd_products_get( 0, $lnk[1], 0, true );
			if( @ria_mysql_num_rows($rprd) ){
				$prd = ria_mysql_fetch_array($rprd);
				$url = prd_products_get_url( $prd['id'], true );
				if( $url ){
					$desc = str_replace( '&lt;produit:'.$lnk[1].':'.$lnk[2].'&gt;', '<a href="'.$url.'">'.$lnk[2].'</a>', $desc );
				}else{
					$desc = str_replace( '&lt;produit:'.$lnk[1].':'.$lnk[2].'&gt;', $lnk[2], $desc );
				}
			}else{
				$desc = str_replace( '&lt;produit:'.$lnk[1].':'.$lnk[2].'&gt;', $lnk[2], $desc );
			}
		}
	}
	// Traite les liens vers des catégories
	if( preg_match_all( '/&lt;categorie\:([^:]+):([^&]+)&gt;/', $desc, $links, PREG_SET_ORDER ) ){
		foreach( $links as $lnk ){
			$rcat = prd_categories_get( $lnk[1], true );
			if( @ria_mysql_num_rows($rcat) ){
				$cat = ria_mysql_fetch_array($rcat);
				//$url = prd_products_get_url( $prd['id'], true );
				if( $cat['url_alias']){
					$desc = str_replace( '&lt;categorie:'.$lnk[1].':'.$lnk[2].'&gt;', '<a href="'.$cat['url_alias'].'">'.$lnk[2].'</a>', $desc );
				}else{
					$desc = str_replace( '&lt;categorie:'.$lnk[1].':'.$lnk[2].'&gt;', $lnk[2], $desc );
				}
			}else{
				$desc = str_replace( '&lt;categorie:'.$lnk[1].':'.$lnk[2].'&gt;', $lnk[2], $desc );
			}
		}
	}
	// Traite les liens vers des cms
	if( preg_match_all( '/&lt;cms\:([^:]+):([^&]+)&gt;/', $desc, $links, PREG_SET_ORDER ) ){
		foreach( $links as $lnk ){
			$rcms = cms_categories_get($lnk[1], false, false, -1, false, false, true, null, false, null, false);
			if( @ria_mysql_num_rows($rcms) ){
				$cms = ria_mysql_fetch_array($rcms);
				$url = $cms['url'];
				if( $url ){
					$desc = str_replace( '&lt;cms:'.$lnk[1].':'.$lnk[2].'&gt;', '<a href="'.$url.'">'.$lnk[2].'</a>', $desc );
				}else{
					$desc = str_replace( '&lt;cms:'.$lnk[1].':'.$lnk[2].'&gt;', $lnk[2], $desc );
				}
			}else{
				$desc = str_replace( '&lt;cms:'.$lnk[1].':'.$lnk[2].'&gt;', $lnk[2], $desc );
			}
		}
	}
	// Traite les liens externes
	if( preg_match_all( '/&lt;url\:(https?:[^:]+):(.+?)&gt;/', $desc, $links, PREG_SET_ORDER ) ){
		foreach( $links as $lnk ){
			$desc = str_replace( '&lt;url:'.$lnk[1].':'.$lnk[2].'&gt;', '<a href="'.$lnk[1].'" target="_blank">'.$lnk[2].'</a>', $desc );
		}
	}
	// Traite les liens externes
	if( preg_match_all( '/&lt;url\:(ftp?:[^:]+):(.+?)&gt;/', $desc, $links, PREG_SET_ORDER ) ){
		foreach( $links as $lnk ){
			$desc = str_replace( '&lt;url:'.$lnk[1].':'.$lnk[2].'&gt;', '<a href="'.$lnk[1].'" target="_blank">'.$lnk[2].'</a>', $desc );
		}
	}
	// Traite les liens vers des documents
	//$desc = str_replace( array('&laquo;','&raquo;','&eacute;'), array('<','>','é'), $desc );
	if( preg_match_all( '/&lt;document\:([^:]+):(.+?)&gt;/', $desc, $links, PREG_SET_ORDER ) ){
		foreach( $links as $lnk ){
			$rdoc = doc_documents_get( $lnk[1], 0, false, '', false );
			if( @ria_mysql_num_rows($rdoc) ){
				$doc = ria_mysql_fetch_array($rdoc);
				$desc = str_replace( '&lt;document:'.$lnk[1].':'.$lnk[2].'&gt;', '<a href="/telechargements/download?doc='.$doc['id'].'">'.$lnk[2].'</a>', $desc );
			}
		}
	}

	if ($glossary) {
		$desc = gsr_insert_def_in_desc($desc);
	}

	return $desc;
}

/** Cette fonction permet de modifier tous les types de lien href riaShop par un lien HTML.
 *	@param string $desc Obligatoire, texte d'origine
 *	@param int $cat_cms Optionnel, identifiant d'une catégorie de CMS
 *	@param bool $publish Optionnel, si le contenu doit être publié ou non, par défault à true
 *	@param bool $remove_href Optionnel, par défaut ignoré, mettre true pour ne pas afficher les liens
 *	@return string Retourne le texte après avoir modifier tous les liens
 */
function view_site_replace_href( $desc, $cat_cms=false, $publish=true, $remove_href=false ){
	global $config;
	
	if( preg_match_all('/<a\s([a-z]+="[^"]*"[^>]*)>(.*)<\/a>/siU', $desc, $links, PREG_SET_ORDER) ){
		foreach( $links as $l ){
			if( preg_match_all('/href="(product|category|categorie|cms|doc|store):([0-9]*)"/iU', $l[1], $href, PREG_SET_ORDER) ){
				foreach( $href as $h ){
					$h[1] = str_replace( array('/','(',')'), array('\/','\(','\)'), $h[1] );
					$h[2] = str_replace( array('/','(',')'), array('\/','\(','\)'), $h[2] );
					
					if( is_numeric($h[2]) && $h[2]>=0 ){
						$url = view_site_href_load($h[1], $h[2], $publish, $cat_cms);
						
						$replace = false;
						if (!$remove_href) {
							if( trim($url) ){
								$desc = preg_replace( '/href="'.$h[1].':'.$h[2].'"/iU', 'href="'.rew_strip($url).'"', $desc, 1 );
								$replace = true;
							}
						}
						
						if( !$replace ){
							$desc = str_replace( $l[0], $l[2], $desc );
						}
					}
				}
			}
		}
	}

	// Gestion des urls HTTPS
	if (isset($config['site_ssl_url']) && trim($config['site_ssl_url']) != '' && $config['site_ssl_url'] != $config['site_url']) {
		if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS']) {
			$desc = str_replace($config['site_url'], $config['site_ssl_url'], $desc);
		}
	}

	return $desc;
}

/** Cette fonction permet de charger l'url dans les contenus dynamiques..
 * 	@param int $cls_id Obligatoire, identifiant d'une classe d'objet
 * 	@param int|array $obj_ids identifiant de l'objet (peut être un tableau d'identifiants de l'objet - ex. classement de produit)
 * 	@param bool $publish Par défaut seule l'url publiée est récupérer, mettre False pour ne pas tenir compte de la publication
 *	@param int $cat_cms Optionnel, identifiant d'une catégorie de CMS
 * 	
 * 	@return string L'url trouvée, vide si aucune url
 */
function view_site_href_load($cls_id, $obj_ids, $publish=true, $cat_cms=false){
	$obj_ids = control_array_integer( $obj_ids, true, true, true );
	if ($obj_ids === false) {
		return '';
	}
	
	$url = '';

	global $config;

	$cat = isset($config['default_category']) ? $config['default_category'] : 0;
	$catchilds = $cat>0 ? true : false;
	
	switch( $cls_id ){
		case 'product' :
			$rprd = prd_products_get_simple( $obj_ids[0], '', $publish, $cat, $catchilds );
			if( ria_mysql_num_rows($rprd) ){
				$prd = ria_mysql_fetch_array($rprd);
				if( !isset($prd['url_alias']) ){
					$url = prd_products_get_url( $prd['id'], $publish );
				}else{
					$url = $prd['url_alias'];
				}
			}
			break;
		case 'categorie' :
		case 'category' :
			if( !$publish || prd_categories_is_published($obj_ids[0]) ){
				$rcat = prd_categories_get( $obj_ids[0], $publish );
				if( $rcat && ria_mysql_num_rows($rcat) ){
					$url = ria_mysql_result( $rcat, 0, 'url_alias' );
				}
			}
			break;
		case 'doc' :
			$rdoc = doc_documents_get( $obj_ids[0], 0, $publish, '', false );
			if( $rdoc && ria_mysql_num_rows($rdoc) ){
				$url = '/downloads/dl.php?doc='.ria_mysql_result( $rdoc, 0, 'id' );
				$url .= $cat_cms ? '&amp;cat='.$cat_cms : '';
			}
			break;
		case 'cms' :
			$rcms = cms_categories_get( $obj_ids[0], $config['wst_id'], $publish, -1, false, false, true, null, false, null, false );
			if( $rcms && ria_mysql_num_rows($rcms) ){
				$url = ria_mysql_result( $rcms, 0, 'url' );
			}	
			break;
		case 'store' :
			$rstr = dlv_stores_get( $obj_ids[0] );
			if( $rstr && ria_mysql_num_rows($rstr) ){
				$url = ria_mysql_result( $rstr, 0, 'url_alias' );
			}
	}

	return $url;
}

/**	Cette fonction est responsable de la mise en forme d'un contenu multiligne ou le tinyMce est activé
 *	@param string $desc Obligatoire, description de l'article, à mettre en forme par la fonction.
 *	@param int $cat_cms Optionnel, identifiant d'une catégorie de CMS
 *	@param bool $show_img Optionnel, si true les images seront affichées
 *	@param bool $publish Optionnel, lors du traitement des liens vers des contenus RiaShop, le lien n'existera que si la cible est publiée
 *	@param $glossary Optionnel, si true les termes du glossaire seront ajoutés.
 *	@param $remove_href Optionnel, par défaut ignoré, mettre true pour ne pas afficher les liens
 *	@return string La description mise en forme au format html.
 *
 */
function view_site_format_riawysiwyg( $desc, $cat_cms=false, $show_img=true, $publish=true, $glossary=true, $remove_href=false ){
	global $config;
	
	/*
		Exemple de lien : <a href="http://www.exemple.com" title="exemple" target="_blank">Exemple</a>
		Avec les attributs title et target optionnel et pas forcément toujours à la même place.
			$desc = '<a target="_blank" href="category:8150" title="Ecoproduits &gt;&gt; Cuisine Nature">exemple cat 37</a><br />'.$desc;
			$desc = '<a href="category:37" title="Ecoproduits &gt;&gt; Cuisine Nature" target="_blank">exemple cat 37 target="_blank"</a><br />'.$desc;
			$desc = '<a title="Ecoproduits &gt;&gt; Cuisine Nature" target="_blank" href="category:37">exemple cat 37  href="category:37"</a><br />'.$desc;
	*/

	if( !ria_is_HTML($desc) ){
		$desc = nl2br( $desc );
	}

	// Traite les liens vers des contenus RiaShop
	if( $publish ){
		$desc = view_site_replace_href( $desc, $cat_cms, $publish, $remove_href );
	}
	
	// propre aux tableau
	$desc = str_replace( '<h3 style="background-color: rgb(255, 255, 255);"><br></h3>', '', $desc );
	$desc = str_replace( '<p style=" background-color: rgb(255, 255, 255);"><br></p>', '', $desc );
	
	$desc = preg_replace('/<div style="text-align: right;">(<img.*)<\/div>/sU','<span style="float:right">\1</span>',$desc);
	$desc = preg_replace('/<div style="text-align: left;">(<img.*)<\/div>/sU','<span style="float:left">\1</span>',$desc);
	$desc = preg_replace('/<div style="(.*)" alt="" \/>/sU', '<div style="$1">', $desc);
	$desc = preg_replace('/<div style="(.*)" alt="" alt="" \/>/sU', '<div style="$1">', $desc);
	$desc = preg_replace('/<p(style=(.*)|)>[ ]*<\/p>/sU', '', $desc);
	$desc = preg_replace('/<span(style=(.*)|)>[ ]*<\/span>/sU', '', $desc);
	
	// if( preg_match_all( '/\*([^\*]*)\*/i', $desc, $bolds, PREG_SET_ORDER ) ){
		// foreach( $bolds as $bold ){
			// $desc = str_replace( $bold[0], '<b>'.$bold[1].'</b>', $desc );
		// }
	// }
	
	$desc = str_replace( "\r\n", "\n", $desc );
	$desc = str_replace('<h2><li>', '<ul><li>', $desc);
	$desc = str_replace('</h2></li>', '</li></ul>', $desc);
	
	// $desc = '<p>'. str_replace( "\n\n", "\n</p><p>", $desc ) .'</p>';

	$desc = preg_replace( "/\n•/", "\n-", $desc );
	// Active les listes
	$desc = preg_replace( "/\n&middot;/", "\n-", $desc );
	$desc = preg_replace( "/<p>&middot;/", "<p>\n-", $desc );
	$desc = preg_replace( "/\n\-/", "\n- ", $desc );
	$desc = preg_replace( '/<p>\- ([^\n]+)\n/i', "\n<li>\\1</li>\n", $desc );
	$desc = preg_replace( '/\n\- ([^\n]+)<\/p>/i', "\n<li>\\1</li>\n", $desc );
	
	while( ($res = preg_replace( '/\n\- ([^\n]+)(\n)*/i', "\n<li>\\1</li>\n", $desc ))!=$desc )
		$desc = $res;
	
	$desc = preg_replace( '/<\/ul>\n<ul>/', '', $desc );
	$desc = preg_replace( '/<p>\s+<\/p>/i', '', $desc );
	$desc = preg_replace( '/^<\/p>/i', '', trim($desc) );
	$desc = preg_replace( '/<p>$/i', '', $desc );
	$desc = preg_replace( '/<\/ul><ul>/', '', $desc );

	$desc = str_replace('<br>','<br />',$desc);
	$desc = str_replace('<BR>','<br />',$desc);
	$desc = str_replace('<UL>','<ul>',$desc);
	$desc = str_replace('</UL>','</ul>',$desc);
	$desc = str_replace('<LI>','<li>',$desc);
	$desc = str_replace('</LI>','</li>',$desc);
	$desc = str_replace('<EM>','<em>',$desc);
	$desc = str_replace('</EM>','</em>',$desc);
	$desc = str_replace('<STRONG>','<strong>',$desc);
	$desc = str_replace('</STRONG>','</strong>',$desc);
	$desc = str_replace('<P>','<p>',$desc);
	$desc = str_replace('</P>','</p>',$desc);
	$desc = str_replace('<IMG ','<img ',$desc);
	$desc = str_replace('<A ','<a ',$desc);
	$desc = str_replace('</A>','</a>',$desc);
	$desc = str_replace('<SPAN ','<span ',$desc);
	$desc = str_replace('</SPAN>','</span>',$desc);
	$desc = preg_replace('/<style.*<\/style>/sU','',$desc);
	$desc = preg_replace('/<xml.*<\/xml>/sU','',$desc);
	$desc = preg_replace('/<span style="(.*)".*>/sU', strtolower('<span style="\1">'),$desc);
	$desc = str_replace('&amp;nbsp;','&nbsp;', $desc);
	
	while (($r = strpos($desc, '<br /><br /><br />')) !== false) $desc = str_replace('<br /><br /><br />', '<br /><br />', $desc);
	
	// suppression de cette balise pouvant venir d'un copier/coller de Word et qui pose des problèmes d'affichage
	$desc = str_replace('<!--[if gte mso 9]>', '', $desc);
	$desc = str_replace('<!--[if gte mso 10]>', '', $desc);
	$desc = str_replace('<![endif]-->', '', $desc);

	// Supprime la présence des balises glossaires pouvant avoir été copier / coller dans une description
	$desc = preg_replace( '/<dfn title="(.*)">(.*)<\/dfn>/sU', "$2", $desc );

	// Ajout des balises glossaires si demandé
	if( $glossary ){
		$desc = gsr_insert_def_in_desc(html_entity_decode($desc, ENT_COMPAT, 'UTF-8'));
	}

	// Correction erreur W3C pouvant survenir
	$desc = preg_replace( '/frameborder="[0-9]+"/i', '', $desc );
	$desc = preg_replace( '/marginwidth="[0-9]+"/i', '', $desc );
	$desc = preg_replace( '/marginheight="[0-9]+"/i', '', $desc );
	$desc = str_replace( '<p align="LEFT"', '<p', $desc );
	$desc = str_replace( 'http://www.youtube.com/embed/', 'https://www.youtube.com/embed/', $desc );

	return $desc;
}

/// @}
/// @}


