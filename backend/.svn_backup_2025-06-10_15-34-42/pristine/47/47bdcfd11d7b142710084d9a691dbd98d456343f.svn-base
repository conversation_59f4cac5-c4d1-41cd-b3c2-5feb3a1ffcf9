<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AC' => 'ꗻꗡ ꕒꕡꕌ ꗏ ꔳꘋꗣ',
  'AD' => 'ꕉꖆꕟ',
  'AE' => 'ꖳꕯꔤꗳ ꕉꕟꔬ ꗡꕆꔓꔻ',
  'AF' => 'ꕉꔱꕭꔕꔻꕚꘋ',
  'AG' => 'ꕉꘋꔳꖶꕎ ꗪ ꕑꖜꕜ',
  'AI' => 'ꕉꕄꕞ',
  'AL' => 'ꕉꔷꕑꕇꕩ',
  'AM' => 'ꕉꕆꕯ',
  'AO' => 'ꕉꖐꕞ',
  'AQ' => 'ꕉꘋꕚꔳꕪ',
  'AR' => 'ꕉꘀꘋꔳꕯ',
  'AS' => 'ꕶꕱ ꕢꕹꕎ',
  'AT' => 'ꖺꔻꖤꕎ',
  'AU' => 'ꖺꖬꖤꔃꔷꕩ',
  'AW' => 'ꕉꖩꕑ',
  'AX' => 'ꕉꕞꔺ',
  'AZ' => 'ꕉꕤꕑꔤꕧꘋ',
  'BA' => 'ꕷꔻꕇꕰ ꗪ ꗥꕤꖑꔲꕯ',
  'BB' => 'ꕑꔆꖁꔻ',
  'BD' => 'ꕑꕅꕞꗵꔼ',
  'BE' => 'ꗩꕀꗚꘋ',
  'BF' => 'ꕷꕃꕯ ꕘꖇ',
  'BG' => 'ꗂꔠꔸꕩ',
  'BH' => 'ꕑꗸꘋ',
  'BI' => 'ꖜꖩꔺ',
  'BJ' => 'ꗩꕇꘋ',
  'BL' => 'ꕪꘋꕓ ꗞꗢ ꕒꕚꕞꕆ',
  'BM' => 'ꗩꖷꕜ',
  'BN' => 'ꖜꖩꘉꔧ',
  'BO' => 'ꕷꔷꔲꕩ',
  'BQ' => 'ꕪꔓꔬꘂꘋ ꖨꕮ ꗨꗳꗣ',
  'BR' => 'ꖜꕟꔘꔀ',
  'BS' => 'ꕑꕌꕮꔻ',
  'BT' => 'ꖜꕚꘋ',
  'BW' => 'ꕷꖬꕎꕯ',
  'BY' => 'ꗩꕞꖩꔻ',
  'BZ' => 'ꔆꔷꔘ',
  'CA' => 'ꕪꕯꕜ',
  'CC' => 'ꖏꖏꔻ (ꔞꔀꔷꘋ) ꔳꘋꗣ',
  'CD' => 'ꖏꖐ ꗵꗞꖴꕟꔎ ꕸꖃꔀ',
  'CF' => 'ꕉꔱꔸꕪ ꗳ ꗳ ꕸꖃꔀ',
  'CG' => 'ꖏꖐ',
  'CH' => 'ꖬꔃꕤ ꖨꕮꕊ',
  'CI' => 'ꖏꔳ ꕾꕎ',
  'CK' => 'ꖏꕃ ꔳꘋꗣ',
  'CL' => 'ꔚꔷ',
  'CM' => 'ꕪꔈꖩꘋ',
  'CN' => 'ꕦꔤꕯ',
  'CO' => 'ꗛꗏꔭꕩ',
  'CR' => 'ꖏꔻꕚ ꔸꕪ',
  'CU' => 'ꕃꖳꕑ',
  'CV' => 'ꔞꔪ ꗲꔵ ꔳꘋꗣ',
  'CW' => 'ꖴꕟꖇꕱ',
  'CX' => 'ꔞꔻꕮꔻ ꔳꘋꗣ',
  'CY' => 'ꕢꗡꖛꗐꔻ',
  'CZ' => 'ꗿꕃ ꕸꖃꔀ',
  'DE' => 'ꕧꕮꔧ',
  'DG' => 'ꔵꔀꖑ ꔳꘋꗣ',
  'DJ' => 'ꕀꖜꔳ',
  'DK' => 'ꕜꕇꕮꕃ',
  'DM' => 'ꖁꕆꕇꕪ',
  'DO' => 'ꖁꕆꕇꕪꘋ ꕸꕱꔀ',
  'DZ' => 'ꕉꔷꔠꔸꕩ',
  'EA' => 'ꗻꕚ ꗪ ꔡꔷꕞ',
  'EC' => 'ꗡꖴꔃꗍ',
  'EE' => 'ꗡꔻꕿꕇꕰ',
  'EG' => 'ꕆꔖꕞ',
  'EH' => 'ꕢꕌꕟ ꔎꔒ ꕀꔤ',
  'ER' => 'ꔀꔸꔳꕟ',
  'ES' => 'ꕐꘊꔧ',
  'ET' => 'ꔤꔳꖎꔪꕩ',
  'FI' => 'ꔱꘋ ꖨꕮꕊ',
  'FJ' => 'ꔱꔤꕀ',
  'FK' => 'ꕘꔷꕃ ꖨꕮ ꔳꘋꗣ',
  'FM' => 'ꕆꖏꕇꔻꕩ',
  'FO' => 'ꕘꖄ ꔳꘋꗣ',
  'FR' => 'ꖢꕟꘋꔻ',
  'GA' => 'ꕭꕷꘋ',
  'GB' => 'ꖕꕯꔤꗳ',
  'GD' => 'ꖶꕟꕯꕜ',
  'GE' => 'ꗘꖺꕀꕩ',
  'GF' => 'ꗱꘋꔻ ꖶꕎꕯ',
  'GG' => 'ꖶꗦꘋꔻ',
  'GH' => 'ꕭꕌꕯ',
  'GI' => 'ꕀꖜꕟꕚ',
  'GL' => 'ꕧꕓ ꖴꕎ ꖨꕮꕊ',
  'GM' => 'ꕭꔭꕩ',
  'GN' => 'ꕅꔤꕇ',
  'GP' => 'ꖶꕎꔐꖨꔅ',
  'GQ' => 'ꖦꕰꕊ ꗳ ꕅꔤꕇ',
  'GR' => 'ꗥꗷꘋ',
  'GS' => 'ꗘꖺꕀꕩ ꗛꔤ ꔒꘋꗣ ꗏ ꗪ ꗇꖢ ꔳꘋꗣ ꗛꔤ ꔒꘋꗣ ꗏ',
  'GT' => 'ꖶꕎꔎꕮꕞ',
  'GU' => 'ꖶꕎꕆ',
  'GW' => 'ꕅꔤꕇ ꔫꕢꕴ',
  'GY' => 'ꖶꕩꕯ',
  'HN' => 'ꖽꖫꕟ',
  'HR' => 'ꖏꔓꔻꕩ',
  'HT' => 'ꕌꔤꔳ',
  'HU' => 'ꖽꘋꕭꔓ',
  'IC' => 'ꗛꖺꔻꕩ ꔳꘋꗣ',
  'ID' => 'ꔤꖆꕇꔻꕩ',
  'IE' => 'ꕉꔓ ꖨꕮꕊ',
  'IL' => 'ꕑꕇꔻꕞꔤꕞ',
  'IM' => 'ꕮꘋ ꔳꘋꗣ',
  'IN' => 'ꔤꔺꕩ',
  'IO' => 'ꔛꔟꔻ ꔤꔺꕩ ꗛꔤꘂ ꕗꕴꔀ ꕮ',
  'IQ' => 'ꔤꕟꕃ',
  'IR' => 'ꔤꕟꘋ',
  'IS' => 'ꕉꔤꔻ ꖨꕮꕊ',
  'IT' => 'ꔤꕚꔷ',
  'JE' => 'ꘀꗡꔘ',
  'JM' => 'ꕧꕮꔧꕪ',
  'JO' => 'ꗘꖺꗵꘋ',
  'JP' => 'ꔛꗨꗢ',
  'KE' => 'ꔞꕰ',
  'KG' => 'ꕃꕅꔻꕚꘋ',
  'KH' => 'ꕪꕹꔵꕩ',
  'KI' => 'ꕃꔸꕑꔳ',
  'KM' => 'ꖏꕹꖄꔻ',
  'KN' => 'ꔻꘋ ꕃꔳꔻ ꗪ ꔕꔲꔻ',
  'KP' => 'ꖏꔸꕩ ꗛꔤ ꕪꘋꗒ',
  'KR' => 'ꖏꔸꕩ ꗛꔤ ꔒꘋꗣ ꗏ',
  'KW' => 'ꖴꔃꔳ',
  'KY' => 'ꔞꔀꕮꘋ ꔳꘋꗣ',
  'KZ' => 'ꕪꕤꔻꕚꘋ',
  'LA' => 'ꕞꕴꔻ',
  'LB' => 'ꔒꕑꗟꘋ',
  'LC' => 'ꔻꘋ ꖨꔻꕩ',
  'LI' => 'ꔷꗿꘋꔻꗳꘋ',
  'LK' => 'ꖬꔸ ꕞꘋꕪ',
  'LR' => 'ꕞꔤꔫꕩ',
  'LS' => 'ꔷꖇꕿ',
  'LT' => 'ꔷꖤꔃꕇꕰ',
  'LU' => 'ꗏꔻꘋꗂꖺ',
  'LV' => 'ꕞꔳꔲꕩ',
  'LY' => 'ꔒꔫꕩ',
  'MA' => 'ꗞꕟꖏ',
  'MC' => 'ꗞꕯꖏ',
  'MD' => 'ꖒꔷꖁꕙ',
  'ME' => 'ꗞꔳꕇꖶꖄ',
  'MF' => 'ꕪꘋꕓ ꗞꗢ ꕮꕊꔳꘋ',
  'MG' => 'ꕮꕜꕭꔻꕪ',
  'MH' => 'ꕮꕊꕣ ꔳꘋꗣ',
  'MK' => 'ꕮꔖꖁꕇꕰ',
  'ML' => 'ꕮꔷ',
  'MM' => 'ꕆꕩꘋꕮ',
  'MN' => 'ꗞꖐꔷꕩ',
  'MP' => 'ꗛꔤ ꕪꘋꗒ ꕮꔸꕩꕯ ꔳꘋꗣ',
  'MQ' => 'ꕮꔳꕇꕃ',
  'MR' => 'ꗞꔓꔎꕇꕰ',
  'MS' => 'ꗞꘋꔖꕟꔳ',
  'MT' => 'ꕮꕊꕚ',
  'MU' => 'ꗞꔓꗔ',
  'MV' => 'ꕮꔷꕜꔍ',
  'MW' => 'ꕮꕞꕌꔨ',
  'MX' => 'ꘈꔻꖏ',
  'MY' => 'ꕮꔒꔻꕩ',
  'MZ' => 'ꕹꕤꔭꕃ',
  'NA' => 'ꕯꕆꔫꕩ',
  'NC' => 'ꕪꔷꖁꕇꕰ ꕯꕮꕊ',
  'NE' => 'ꕯꔤꕧ',
  'NF' => 'ꗟꖺꗉ ꔳꘋꗣ',
  'NG' => 'ꕯꔤꕀꔸꕩ',
  'NI' => 'ꕇꕪꕟꖶꕎ',
  'NL' => 'ꘉꕜ ꖨꕮꕊ',
  'NO' => 'ꗟꖺꔃ',
  'NP' => 'ꕇꕐꔷ',
  'NR' => 'ꖆꖩ',
  'NU' => 'ꖸꔃꔤ',
  'NZ' => 'ꔽꔤ ꖨꕮ ꕯꕮꕊ',
  'OM' => 'ꕱꕮꘋ',
  'PA' => 'ꕐꕯꕮ',
  'PE' => 'ꗨꗡꖩ',
  'PF' => 'ꗱꘋꔻ ꕶꔷꕇꔻꕩ',
  'PG' => 'ꕐꖛꕎ ꕅꔤꕇ ꕯꕮꕊ',
  'PH' => 'ꔱꔒꔪꘋ',
  'PK' => 'ꕐꕃꔻꕚꘋ',
  'PL' => 'ꕶꗷꘋ',
  'PM' => 'ꔻꘋ ꔪꘂ ꗪ ꕆꔞꗏꘋ',
  'PN' => 'ꔪꔳꕪꕆ',
  'PR' => 'ꔪꖳꕿ ꔸꖏ',
  'PS' => 'ꕐꔒꔻꔳꕯ ꔎꔒ ꕀꔤ ꗛꔤ ꕞ ꗱ ꗪ ꕭꕌꕤ',
  'PT' => 'ꕶꕿꕃꔤ ꕸꖃꔀ',
  'PW' => 'ꕐꖃ',
  'PY' => 'ꕐꕟꗝꔀ',
  'QA' => 'ꕪꕚꕌ',
  'RE' => 'ꔓꗠꖻ',
  'RO' => 'ꖄꕆꕇꕰ',
  'RS' => 'ꗻꗡꔬꕩ',
  'RU' => 'ꗐꖺꔻꕩ',
  'RW' => 'ꕟꖙꕡ',
  'SA' => 'ꕞꕌꖝ ꕸꖃꔀ',
  'SB' => 'ꖬꕞꔤꕮꕊꕯ ꔳꘋꗣ',
  'SC' => 'ꔖꗼꔷ',
  'SD' => 'ꖬꗵꘋ',
  'SE' => 'ꖬꔨꗵꘋ',
  'SG' => 'ꔻꕬꕶꕱ',
  'SH' => 'ꔻꘋ ꗥꔷꕯ',
  'SI' => 'ꔻꖃꔍꕇꕰ',
  'SJ' => 'ꔻꕙꕒꔵ ꗪ ꕧꘋ ꕮꘂꘋ',
  'SK' => 'ꔻꖃꕙꕃꕩ',
  'SL' => 'ꔋꕩ ꕒꕌꖺ ꕸꖃꔀ',
  'SM' => 'ꕮꔸꖆ ꕢꘋ',
  'SN' => 'ꔻꕇꕭꕌ',
  'SO' => 'ꖇꕮꔷꕩ',
  'SR' => 'ꖬꔸꕯꔈ',
  'SS' => 'ꖬꕜꘋ ꗛꔤ ꔒꘋꗣ ꗏ',
  'ST' => 'ꕢꕴ ꕿꔈ ꗪ ꕉ ꕮꔧ ꕗꕴꔀ',
  'SV' => 'ꗡꗷ ꕢꔍꗍꖺ',
  'SX' => 'ꔻꘋꔳ ꕮꕊꗳꘋ',
  'SY' => 'ꔻꕩꘋ',
  'SZ' => 'ꖬꕎꔽ ꖨꕮꕊ',
  'TA' => 'ꔳꔻꕚꘋ ꕜ ꖴꕯ',
  'TC' => 'ꗋꖺꕃꔻ ꗪ ꕪꔤꖏꔻ ꔳꘋꗣ',
  'TD' => 'ꕦꔵ',
  'TF' => 'ꔱꗷꘋꔻ ꗛꔤ ꔒꘋꗣ ꗏ ꕸꖃꔀ ꖸ',
  'TG' => 'ꕿꖑ',
  'TH' => 'ꕚꔤ ꖨꕮꕊ',
  'TJ' => 'ꕚꕀꕃꔻꕚꘋ',
  'TK' => 'ꕿꔞꖃ',
  'TL' => 'ꔎꔒ ꗃ ꔳꗞꖻ',
  'TM' => 'ꗋꖺꕃꕮꕇꔻꕚꘋ',
  'TN' => 'ꖤꕇꔻꕩ',
  'TO' => 'ꗋꕬ',
  'TR' => 'ꗋꖺꕃ',
  'TT' => 'ꖤꔸꔕꕜ ꗪ ꕿꔆꖑ',
  'TV' => 'ꕚꖣꖨ',
  'TW' => 'ꕚꔤꕎꘋ',
  'TZ' => 'ꕚꘋꕤꕇꕰ',
  'UA' => 'ꖳꖴꔓꘋ',
  'UG' => 'ꖳꕭꕡ',
  'UM' => 'ꕶꕱ ꕪꘋ ꗅꘋ ꔳꘋꗣ ꖸ',
  'US' => 'ꕶꕱ',
  'UY' => 'ꖳꔓꗝꔀ',
  'UZ' => 'ꖳꗩꕃꔻꕚꘋ',
  'VA' => 'ꕙꔳꕪꘋ ꕢꕨꕌ',
  'VC' => 'ꔻꘋ ꔲꘋꔻꘋ ꗪ ꖶꔓꕯꔵꘋ ꖸ',
  'VE' => 'ꕙꔳꕪꘋ ꕸꖃꔀ',
  'VG' => 'ꔛꔟꔻ ꗩꗡ ꗏ ꖷꖬ ꔳꘋꗣ',
  'VI' => 'ꕶꕱ ꗩꗡ ꗏ ꖷꖬ ꔳꘋꗣ',
  'VN' => 'ꗲꕇꖮꔃꕞ',
  'VU' => 'ꕙꖸꕎꖤ',
  'WF' => 'ꕎꔷꔻ ꗪ ꖢꖤꕯ',
  'WS' => 'ꕢꕹꖙꕉ',
  'XK' => 'ꖏꖇꕾ',
  'YE' => 'ꔝꘈꘋ',
  'YT' => 'ꕮꗚꔎ',
  'ZA' => 'ꕉꔱꔸꕪ ꗛꔤ ꔒꘋꗣ ꗏ ꕸꖃꔀ',
  'ZM' => 'ꕤꔭꕩ',
  'ZW' => 'ꔽꕓꖜꔃ',
);
