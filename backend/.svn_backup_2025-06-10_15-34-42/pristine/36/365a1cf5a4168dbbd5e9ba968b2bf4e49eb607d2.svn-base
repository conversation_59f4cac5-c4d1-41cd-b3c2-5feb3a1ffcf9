{"name": "php-coveralls/php-coveralls", "description": "PHP client library for Coveralls API", "keywords": ["coverage", "github", "test", "ci"], "homepage": "https://github.com/php-coveralls/php-coveralls", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.facebook.com/satooshi.jp", "role": "Original creator"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Google Inc"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/keradus"}, {"name": "Contributors", "homepage": "https://github.com/php-coveralls/php-coveralls/graphs/contributors"}], "require": {"php": "^5.5 || ^7.0", "ext-json": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.0", "psr/log": "^1.0", "symfony/config": "^2.1 || ^3.0 || ^4.0 || ^5.0", "symfony/console": "^2.1 || ^3.0 || ^4.0 || ^5.0", "symfony/stopwatch": "^2.0 || ^3.0 || ^4.0 || ^5.0", "symfony/yaml": "^2.0.5 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.4.3 || ^6.0"}, "suggest": {"symfony/http-kernel": "Allows Symfony integration"}, "autoload": {"psr-4": {"PhpCoveralls\\": "src/"}}, "autoload-dev": {"psr-4": {"PhpCoveralls\\Tests\\": "tests/"}}, "bin": ["bin/php-coveralls"], "scripts": {"install-dev-tools": ["cd dev-tools && composer update --no-interaction"], "sca": ["php dev-tools/vendor/bin/php-cs-fixer fix --dry-run -vv", "php dev-tools/vendor/bin/phpmd src text build/config/phpmd.xml"]}, "config": {"optimize-autoloader": true, "process-timeout": 0, "sort-packages": true}, "extra": {"branch-alias": {"dev-master": "2.2-dev"}}}