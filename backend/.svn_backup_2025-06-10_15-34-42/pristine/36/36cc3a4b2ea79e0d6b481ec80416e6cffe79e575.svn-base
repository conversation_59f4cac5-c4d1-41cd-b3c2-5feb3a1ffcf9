<?php
/**
 *  \defgroup api-prices-tvas Taux de TVA
 *  \ingroup cpq
 *	@{
 *
 * 	\page api-prices-tvas-get Chargement 
 *
 * 	Cet appel permet la récupération de l'ensemble des catégories tarifaires. Il n'est pas paginé.
 * 
 *		\code
 *			GET /prices/tvas/
 *		\endcode
 *
 * @return Json Un tableau au format Json comprenant pour chaque item les clés suivantes :
 *	\code{.json}
 *       {
 *			"rate" : taux de TVA (supérieur à 1),
 *			"name" : dénomination du taux de TVA (ex : 19,6)    
 *       },
 *	\endcode
 * @}
*/

require_once('prd/tvas.inc.php');

switch( $method ){
	case 'get':

		$array = array();
		$rcat = prd_tvas_get();
		while( $cat = ria_mysql_fetch_assoc($rcat) ){
			$array[] = $cat;
		}

		$content = $array;
		$result = true;
		break;
}
