// Ajout un parser pour gérer les nombres a virgules
$.tablesorter.addParser({
  id: "commaDigit",
  is: function(s, table) {
	var c = table.config;
	return $.tablesorter.isDigit(s.replace(/,/g, ""), c);
  },
  format: function(s) {
	return $.tablesorter.formatFloat(s.replace(/,/g, ""));
  },
  type: "numeric"
});

var exportAction = '';
var sortList =  [];

function calcHref() {
	var reg=new RegExp('[,]+', 'g');
	var date = getRiaDate().toString().split(reg);
	var date1 = dateparse(date[0]);
	var date2 = dateparse(date[1]);
	var wst = $('#wst').val();

	if( wst=='all' || !wst ) wst = 0;
	wst = wst.toString().replace('w-','');
	var get = extractUrlParams();
	var tri = '';
	if( get['tri'] ){
		for( var i=0; i<get['tri'].length; i++ ){
			tri += '&tri['+get['tri'][i][0]+']='+get['tri'][i][1];
		}
	}

	var wosubstitut = '';
	if($('input#wosubstitut').is(':checked'))
		wosubstitut = '&wosubstitut=1';
	var fct = last_function == '' ? (get['last'] ? get['last'] : prdConversion ) : last_function;
	return window.location.pathname+'?'+'wst=' + wst + '&date1=' + date1 + '&date2=' + date2 + wosubstitut + '&last=' + fct+tri;
}

// Au clic sur le th de l'une des colonnes
$(document).on('click', '#w-ref a, #w-name a, #w-hits a, #w-sells a, #w-conversion a, #w-price_sell a, #w-price_purchase a, #w-margin a, #w-margin_rate a', function(e){
	e.preventDefault();

	var href = $(this).attr('href');
	getAjaxData(href);

	// Changer l'URL sans recharger la page
	history.pushState({ path: this.path }, '', href);
})

/**	Assure le rechargement des données du tableau en Ajax
 * 
 * @param {*} href 
 */
function getAjaxData(href) {

	// Active l'indicateur de chargement
	$('#ajax-indicator').show();

	$.ajax({
		type: 'GET',
		url: href,
		success: function(data){
			// Affiche les données une fois chargées
			$('#site-content-main').html(data);
			// Désactive l'indicateur de chargement
			$('#ajax-indicator').hide();
		},
		error:function(){
		}
	});
}

$(document).ready(function(){
	$(window).bind('popstate', function() {
		$.get(location.href, function(data) {
			$('#site-content-main').html(data);
		});
	});
	
	exportAction = $('#form-export').attr('action');

	$('.tooltip-handle').tooltip({ 
		delay: 0, 
		showURL: false, 
		track: true, 
		bodyHandler: function() { 
			return $('#tooltip-'+$(this).attr('id')).html(); 
		} 
	});
	getAjaxData(calcHref());
});