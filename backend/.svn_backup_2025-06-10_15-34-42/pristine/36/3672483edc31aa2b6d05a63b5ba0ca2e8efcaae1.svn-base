<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/logging.proto

namespace GPBMetadata\Google\Api;

class Logging
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0af8020a18676f6f676c652f6170692f6c6f6767696e672e70726f746f12" .
            "0a676f6f676c652e61706922d7010a074c6f6767696e6712450a1570726f" .
            "64756365725f64657374696e6174696f6e7318012003280b32262e676f6f" .
            "676c652e6170692e4c6f6767696e672e4c6f6767696e6744657374696e61" .
            "74696f6e12450a15636f6e73756d65725f64657374696e6174696f6e7318" .
            "022003280b32262e676f6f676c652e6170692e4c6f6767696e672e4c6f67" .
            "67696e6744657374696e6174696f6e1a3e0a124c6f6767696e6744657374" .
            "696e6174696f6e121a0a126d6f6e69746f7265645f7265736f7572636518" .
            "0320012809120c0a046c6f6773180120032809426e0a0e636f6d2e676f6f" .
            "676c652e617069420c4c6f6767696e6750726f746f50015a45676f6f676c" .
            "652e676f6c616e672e6f72672f67656e70726f746f2f676f6f676c656170" .
            "69732f6170692f73657276696365636f6e6669673b73657276696365636f" .
            "6e666967a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

