<?php

require_once('db.inc.php');
require_once('websites.inc.php');
require_once('cfg.variables.inc.php');
require_once('rewrite.inc.php');
require_once('i18n.inc.php');

/** \defgroup field_constants_error_404 Erreur de redirection
 *	\ingroup model_constants
 *	@{
 */
// Codes d'erreur renvoyés si l'url de substitution ne se trouve pas dans rew_rewritemap
define( 'ERROR_REW', -1 );		/// Non utilisé
define( 'ERROR_REW_404', -2 );	/// L'url provoque une erreur 404
/// @}

/** \defgroup err_404 Erreurs 404
 *	\ingroup rewriting
 *	Ce module comprend les fonctions nécessaires à la gestion des erreurs 404 et leur redirection.
 *	@{
 */

/** Cette fonction permet d'ajouter une erreur 404 dans la base de données.
 *	Si une erreur 404 sur l'url passée en paramètre existe déjà, alors nous incrementerons le nombre d'occurences.
 *	@param string $url Obligatoire, URL retournant une erreur 404
 *	@param $http Optionnel, ce paramètre permet de sauvegarder la valeur de la variable $_SERVER['HTTP_REFERER']
 *	@param int $user_id Optionnnel, permet de vérifier que l'erreur n'est pas provoquée par une restriction d'accès à un contenu
 *
 *	@return bool Retourne true si l'erreur 404 à bien été sauvegardée
 *	@return bool Retourne false dans le cas contraire
 */
function err_errors_add( $url, $http='', $user_id=0 ){
	if( stats_is_bot() ){
		return true;
	}

	// Les erreurs générées par les administrateurs ne sont pas logguées
	if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_ADMIN ){
		return true;
	}

	// Si l'url est manquante, l'erreur n'est pas logguée
	if( trim($url)=='' ){
		return false;
	}

	global $config;

	if( isset($config['rew_strip']) && trim($config['rew_strip']) ){
		$url = rew_strip( $url );
	}

	// Bloque l'enregistrement de toutes les erreurs 404 provenant de l'espace d'administration
	if( trim($http) && strstr($http, $config['site_url'].'/admin') ){
		return true;
	}
	if( trim($config['site_dir']) && strstr($url, $config['site_dir']) ){
		return true;
	}

	// Les erreurs liées au plugin complitly ne sont pas logguées (#6352)
	if( preg_match( '/\/undefined$/', $url ) || preg_match( '/\/\[LINK\]$/', $url ) ){
		return true;
	}

	$save_error = true;
	if( is_numeric($user_id) && $user_id>0 ){
		if( defined('ERROR_CLS_ID') ){
			switch( ERROR_CLS_ID ){
				case CLS_CATEGORY: {
					if( isset($_GET['cat']) ){
						if( prd_categories_exists($_GET['cat'], false) && !prd_restrictions_reach_object( CLS_CATEGORY, $_GET['cat'], $user_id) ){
							$save_error = false;
						}
					}
					break;
				}
				case CLS_PRODUCT: {
					if( isset($_GET['cat'], $_GET['prd']) ){
						if( prd_classify_exists($_GET['cat'], $_GET['prd']) && !prd_restrictions_reach_object( CLS_PRODUCT, $_GET['prd'], $user_id) ){
							$save_error = false;
						}
					}
				}
			}
		}
	}

	// Si la 404 est provoqué par une restriction, alors on ne sauvegarde pas l'erreur
	if( !$save_error ){
		error_log( __FILE__.':'.__LINE__.' => err_errors_add(), access denied' );
		return true;
	}

	// Sinon on ajoute l'url à la base de données
	return ria_mysql_query('
		insert into err_errors
			( err_tnt_id, err_wst_id, err_lng_code, err_url, err_date, err_http_referer )
		values
			( '.$config['tnt_id'].', '.$config['wst_id'].', \''.strtolower(i18n::getLang()).'\', \''.addslashes($url).'\', now(), '.(trim($http) ? '\''.addslashes($http).'\'' : 'null').')
	');
}

// \cond onlyria
/** Cette fonction permet de vérifié l'existance, dans la base de données, d'une url retournant une erreur 404.
 *	@param int $wst Optionnel, identifiant du site où s'est produite l'erreur 404, si ce paramètre est omis, alors l'identifiant du site dans les configs est pris
 *	@param string $url Obligatoire, URL devant retourner une erreur 404
 *	@return bool Retourne true si l'URL est trouvée
 *	@return bool Retourne false dans le cas contraire
 */
function err_errors_exists( $wst=0, $url ){
	if( $wst>0 && !wst_websites_exists($wst) ) return false;
	if( trim($url)=='' ) return false;
	global $config;

	$wst = $wst>0 ? $wst : $config['wst_id'];

	return ria_mysql_num_rows( ria_mysql_query('select 1 from err_errors where err_tnt_id='.$config['tnt_id'].' and err_wst_id='.$wst.' and err_url=\''.addslashes($url).'\'') )>0;
}
// \endcond

// \cond onlyria
/**	Renvoie les filtres sur le type de document
 *	@return array Tableau de filtres id_filtre/options
 *		-	name	:	Nom du filtre
 *		-	regExp	:	Expression régulière à appliquer sur le nom du fichier
 *		-	not		:	Optionnel, le résultat sera retourné si la regExp échoue
 */
function err_errors_filters_types_get() {
	return array(
		'all'		=>	array(
							'name'		=>	'Toutes les erreurs 404',
							'regExp'	=>	'.*'
						),
		'pages'		=>	array(
							'name'		=>	'Pages',
							'regExp'	=>	'(\.css|\.scss|\.gif|\.js|\.jpg|\.png|\.xml)$',
							'not'		=>	true
						),
		'images'	=>	array(
							'name'		=>	'Images',
							'regExp'	=>	'(\.gif|\.jpg|\.png)$'
						),
		'others'	=>	array(
							'name'		=>	'Autres',
							'regExp'	=>	'(\.css|\.scss|\.js|\.xml)$'
						)
	);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer toutes les urls ayant retournées une erreur 404
 *	@param int $wst Optionnel, identifiant d'un site web
 *	@param string $url Optionnel, URL retournant une erreur 404
 *	@param $no_redirection Optionnel, par défaut toutes les erreurs sont retournées, mettre à true pour n'avoir que celles qui n'ont pas de redirection
 *	@param $like Optionnel, permet de filtrer sur le début d'une url
 *	@param $filterType Optionnel, permet de filtrer sur le type de document (all, pages, images, others)
 *	@param $resolved Optionnel, par défaut seules les erreurs 404 non résolues sont retournées, mettre à true pour avoir que celles qui ont été notées comme résolues et null pour toutes les avoir
 *	@param string $date_start Optionnel, date de début de période
 *	@param string $date_end Optionnel, date de fin de période
 *	@param $sort Optionnel, paramètre de tri (actuellement disponible : "date" pour un tri par date de dernière occurence, "count" pour un tri par occurence)
 *	@param string $lng Optionnel, langue de l'erreur
 *	@return bool Retourne false si une erreur est survenu à cause d'un paramètre faux
 *	@return array Retourne un tableau MySQL contenant les résultats sous la forme suivante :
 *				- wst : identifiant du site
 *				- url : url retournant une erreur 404
 *				- lng : langue du site retournant une erreur 404
 *				- count : nombre de fois que cette erreur s'est produite
 *				- first : date de la première apparition de cette erreur
 *				- last : date de la dernière apparition de cette erreur
 *				- referer : nombre de source de cette erreur
 *				- first_fr : date de la première apparition de cette erreur au format JJ/MM/YYYY
 *				- last_fr : date de la dernière apparition de cette erreur au format JJ/MM/YYYY
 *				- resolved : si oui ou non l'erreur 404 est notée comme résolu
 */
function err_errors_get( $wst=0, $url='', $no_redirection=false, $like='', $filterType=null, $resolved=false, $date_start=false, $date_end=false, $sort=false, $lng=false ){
	if( $wst>0 && !wst_websites_exists($wst) ) return false;
	if( $url!='' && !err_errors_exists($wst, $url) ) return false;
	if ($filterType !== null) {
		$filterTypes = err_errors_filters_types_get();
		if (! isset($filterTypes[$filterType])) return false;
		$filterType = $filterTypes[$filterType];
	}

	if( $date_start !== false && !isdateheure($date_start) ){
		return false;
	}

	if( $date_end !== false && !isdateheure($date_end) ){
		return false;
	}

	global $config;

	$sql = '
		select err_wst_id as wst, err_id as id, err_url as url, count(*) as "count", min(err_date) as "first", max(err_date) as "last", err_lng_code as lng, err_resolved as resolved,
		sum( if(err_http_referer != \'\', 1, 0) ) as referer, date_format(min(err_date),"%d/%m/%Y") as "first_fr", date_format(max(err_date),"%d/%m/%Y") as "last_fr"
		from err_errors
	';

	$sql .= '
		where err_tnt_id='.$config['tnt_id'].'
	';

	if( $wst>0 )
		$sql .= ' and err_wst_id='.$wst;

	if( $url!='' )
		$sql .= ' and err_url=\''.addslashes($url).'\'';

	if( $date_start !== false ){
		if( isdate($date_start) ){
			$sql .= ' and date(err_date) >= "'.dateparse($date_start).'"';
		}else{
			$sql .= ' and err_date >= "'.dateheureparse($date_start).'"';
		}
	}

	if( $date_end !== false ){
		if( isdate($date_end) ){
			$sql .= ' and date(err_date) <= "'.dateparse($date_end).'"';
		}else{
			$sql .= ' and err_date <= "'.dateheureparse($date_end).'"';
		}
	}

	if( $resolved!==null ){
		if( $resolved ){
			$sql .= ' and err_resolved=1';
		} else {
			$sql .= ' and err_resolved=0';
		}
	}

	if( trim($like) )
		$sql .= ' and lower(err_url) like \'%'.strtolower(addslashes($like)).'%\'';

	if ($filterType) {
		$sql .= ' and lower(err_url)' . (isset($filterType['not']) && $filterType['not'] ? ' not' : '') . ' regexp \'' . addslashes($filterType['regExp']) . '\'';
	}

	if ($lng !== false){
		$sql .= ' and err_lng_code="'.$lng.'"';
	}

	if( $no_redirection ){
		$sql .= '
			and not exists(
				select 1
				from rew_rewritemap
				where url_tnt_id='.$config['tnt_id'].'
					and url_wst_id=err_wst_id
					and url_lng_code=err_lng_code
					and url_extern=err_url
					and url_code=301
			)
		';
	}

	$sql .= '
		group by err_url, err_wst_id
	';

	// Tri du résultat (valeurs par défaut)
	if( $sort==false || !is_array($sort) || sizeof($sort)==0 )
		$sort = array();

	// Converti le paramètre de tri en SQL
	$sort_final = array();

	foreach( $sort as $col=>$dir ){
		$col = strtolower(trim($col));
		$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';
		switch( $col ){
			case 'count' :
				array_push( $sort_final, 'count(*) '. $dir );
				break;
			case 'date' :
				array_push( $sort_final, 'err_date '.$dir );
				break;
		}
	}

	// Ajoute la clause de tri
	if( sizeof($sort_final)==0 ) $sort_final = array( 'count(*) desc' );

	if( is_array($sort_final) && sizeof($sort_final) )
		$sql .= ' order by '.implode( ', ', $sort_final ).' ';
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les urls sources d'une erreur 404
 *	@param string $url Obligatoire, url provoquant l'erreur 404
 *	@param int $wst Facultatif, identifiant d'un site sur lequel filtrer le résultat. Si cet argument n'est pas précisé, les urls ne sont filtrées que sur le locataire (tous sites confondus)
 *	@return resource Retourne un résultat MySQL des urls sources contenants
 *				- source : url source de l'erreur
 *				- occurence : nombre de fois que cette url est utilisée
 *	@return bool false en cas d'erreur sur l'un des paramètres
 */
function err_errors_get_sources( $url, $wst=0 ){
	if( !trim($url) ) return false;
	if( !is_numeric($wst) || $wst<0 ) return false;
	global $config;

	$sql = '
		select err_http_referer as source, count(*) as occurence
		from err_errors
		where err_tnt_id='.$config['tnt_id'].'
			'.( $wst>0 ? ' and err_wst_id='.$wst : '' ).'
			and err_url = \''.addslashes( $url ).'\'
			and err_http_referer is not null
		group by err_http_referer
		order by count(*) desc
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une erreur 404 (cette fonction ne supprime pas les redirections pouvant avoir été mise en place).
 *	@param int $wst_id Obligatoire, identifiant d'un site
 *	@param string $lng Obligatoire, code de la langue
 *	@param string $url Obligatoire, url à supprimer
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function err_errors_del( $wst_id, $lng, $url ){
	if( !is_numeric($wst_id) || $wst_id <= 0 ){
		return false;
	}

	if( trim($lng) == '' ){
		return false;
	}

	if( trim($url) == '' ){
		return false;
	}

	global $config;

	$sql = '
		delete from err_errors
		where err_tnt_id = '.$config['tnt_id'].'
			and err_wst_id = '.$wst_id.'
			and err_lng_code = "'.addslashes( $lng ).'"
			and err_url = "'.addslashes( $url ).'"
	';

	return ria_mysql_query( $sql ) or die($sql. ' => '.mysql_error());
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information de résolue d'une erreur 404.
 *	@param int $wst Obligatoire, identifiant du site sur lequel l'erreur 404 s'est prouite
 *	@param string $url Obligatoire, url ayant renvoyée une erreur 404
 *	@param string $lng Obligatoire, code de la langue
 *	@param bool $resolved Optionnel, si oui ou non l'erreur 404 est résolué par défaut à Oui
 *	@return bool True si l'enregistrement s'est correctement déroulé, False dans le cas contraire
 */
function err_errors_set_resolved( $wst, $url, $lng, $resolved=true ){
	if( !err_errors_exists($wst, $url) ) return false;
	global $config;

	if( trim($lng)=='' || !in_array($lng, $config['i18n_lng_used']) ){
		return false;
	}

	$sql = '
		update err_errors set err_resolved='.( $resolved ? 1 : 0 ).'
		where err_tnt_id='.$config['tnt_id'].'
			and err_wst_id='.$wst.'
			and err_lng_code=\''.strtolower($lng).'\'
			and err_url=\''.addslashes($url).'\'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter une redirection à une erreur 404
 *	@param int $wst Obligatoire, identifiant du site sur lequel l'erreur 404 s'est produite
 *	@param string $url Obligatoire, url ayant renvoyée une erreur 404
 *	@param string $lng Obligatoire, code de la langue
 *	@param string $redirection Obligatoire, destination de la redirection
 *	@return bool Retourne true si la mise à jour s'est correctement passée
 *	@return bool Retourne false en cas d'erreur ou -2 si l'url de destination retourne une erreur 404
 */
function err_errors_redirection_add( $wst, $url, $lng, $redirection ){
	if( !err_errors_exists($wst, $url) ) return false;
	if( !trim($redirection) ) return false;
	if( $url==$redirection ) return false;
	global $config;

	// récupère l'url du site
	$url_site = cfg_overrides_get( $wst, array(), 'site_url' );
	if( !$url_site || !ria_mysql_num_rows($url_site) )
		return false;

	$url_site = ria_mysql_result( $url_site, 0, 'value' );
	if( $lng!=$config['i18n_lng'] ){
		$rurl = wst_websites_languages_get( $wst, $lng );
		if( $rurl && ria_mysql_num_rows($rurl) )
			$url_site = ria_mysql_result( $rurl, 0, 'url' );
	}


	// L'url complete a tester
    $full_url=$url_site.$redirection.(preg_match('/\?.+/', $url_site.$redirection) ? '&testadminerror404=1' : '?testadminerror404=1') ;

	// simule le comportement du proxy, car le code peut faire la requette directement au serveur, qui ne gÃ¨re pas le https
        $curl_extra_header=false;
        if (preg_match("/^https:/",$full_url)) {
                $full_url=preg_replace("/^https:/","http:",$full_url);
		$curl_extra_header = array( "X-Forwarded-Proto: https", "X-Forwarded-By: pound" );
        }


	// test si l'adresse de destination ne retourne pas une erreur 404
	if( !($c = curl_init()) ) return ERROR_REW_404;
	curl_setopt($c, CURLOPT_URL, $full_url );
	curl_setopt($c, CURLOPT_FOLLOWLOCATION, 1);
	curl_setopt($c, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($c, CURLOPT_CONNECTTIMEOUT, 5);
	if (is_array($curl_extra_header)) {
	                 curl_setopt($c, CURLOPT_HTTPHEADER, $curl_extra_header);
	}

	if (curl_exec($c) === false) {error_log('Curl error: ' . curl_error($c));return ERROR_REW_404;}
	$infos = curl_getinfo($c);
	curl_close($c);

	if( $infos['http_code']!='200' )
		return ERROR_REW_404;

	// supprime l'ancienne redirection
	if( err_errors_redirection_exists($wst, $url, $lng) ){
		if( !err_errors_url_redirection_delete($wst, $url, $lng) )
			return false;
	}

	// on insère la redirection dans la langue où l'erreur à été détectée
	$sql = '
		replace into rew_rewritemap
			( url_tnt_id, url_wst_id, url_lng_code, url_extern, url_intern, url_code, url_public )
		values
			( '.$config['tnt_id'].', '.$wst.', \''.$lng.'\', \''.addslashes($url).'\', \''.addslashes($redirection).'\', 301, 0 )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	rew_rewritemap_clean_cache( array($url, $redirection) );
	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer une redirection
 *	@param int $wst Obligatoire, identifiant d'un site
 *	@param string $url Obligatoire, url provoquant l'erreur 404
 *	@param string $lng Obligatoire, identifiant d'une langue
 *	@return bool false en cas de problème sinon l'url de redirection si celle-ci existe
 */
function err_errors_redirection_get( $wst, $url, $lng ){
	if( !is_numeric($wst) || $wst<=0 ) return false;
	if( !trim($url) ) return false;
	global $config;

	$res = ria_mysql_query('
		select url_intern as redirection
		from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
			and url_wst_id='.$wst.'
			and url_extern=\''.addslashes($url).'\'
			and url_code=301
			and url_lng_code=\''.strtolower($lng).'\'
		limit 1
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'redirection' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de tester si une redirection est déjà en place pour une erreur 404
 *	@param int $wst Obligatoire, identifiant d'un site
 *	@param string $url Obligatoire, url provoquant l'erreur 404
 *	@param string $lng optionnel, code de la langue
 *	@return bool true si elle existe, false dans le cas contraire
 */
function err_errors_redirection_exists( $wst, $url, $lng='' ){
	if( !is_numeric($wst) || $wst<=0 ) return false;
	if( !trim($url) ) return false;
	global $config;

	$res = ria_mysql_query('
		select 1
		from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
			and url_wst_id='.$wst.'
			and url_extern=\''.addslashes($url).'\'
			'.( trim($lng) != '' ? ' and url_lng_code = "'.addslashes( $lng ).'"' : '' ).'
   ');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une redirection déjà en place pour une erreur 404
 *	@param int $wst Obligatoire, identifiant d'un site
 *	@param string $url Obligatoire, url provoquant l'erreur 404
 *	@param string $lng Obligatoire, code de la langue
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function err_errors_url_redirection_delete( $wst, $url=array(), $lng='' ){
	if( !err_errors_exists($wst, $url) ) return false;
	global $config;

	return ria_mysql_query('
		delete from rew_rewritemap
		where url_tnt_id='.$config['tnt_id'].'
			and url_wst_id='.$wst.'
			and url_extern=\''.addslashes($url).'\'
			and url_code=301
			'.( trim($lng) != '' ? ' and url_lng_code = "'.addslashes( $lng ).'"' : '' ).'
	');
}
// \endcond

/// @}


