# Change Log

All notable changes to `sebastianbergmann/object-enumerator` are documented in this file using the [Keep a CHANGELOG](http://keepachangelog.com/) principles.

## [2.0.1] - 2017-02-18

### Fixed

* Fixed [#2](https://github.com/sebastianbergmann/phpunit/pull/2): Exceptions in `ReflectionProperty::getValue()` are not handled

## [2.0.0] - 2016-11-19

### Changed

* This component is now compatible with `sebastian/recursion-context: ~1.0.4`

## 1.0.0 - 2016-02-04

### Added

* Initial release

[2.0.1]: https://github.com/sebastianbergmann/object-enumerator/compare/2.0.0...2.0.1
[2.0.0]: https://github.com/sebastianbergmann/object-enumerator/compare/1.0...2.0.0

