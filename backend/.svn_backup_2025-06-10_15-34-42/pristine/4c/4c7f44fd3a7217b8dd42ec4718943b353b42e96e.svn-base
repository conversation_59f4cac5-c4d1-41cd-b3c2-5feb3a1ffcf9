<?php

/** Cette fonction ramène toutes les lignes de classement,
 *	éventuellement filtrées selon des paramètres comme l'état de synchronisation ou la publication.
 *
 *	@param bool $is_sync Facultatif, si true, seul les classements synchronisés seront retournés
 *	@param int $prd Facultatif, identifiant d'un article en particulier (ou tableau)
 *	@param int|array $cat Facultatif, identifiant d'une catégorie en particulier (ou tableau)
 *	@param int $cnt Facultatif, identifiant d'un contenu dans le moteur de recherches
 *	@param bool|array $sort Optionnel, paramètre de tri. Tableau contenant la colonne sur laquelle trier et l'ordre de tri.
 *	@param bool $publish Optionnel, par défaut tous les classements sont retournés, mettre True pour ne récupérer que ceux publiés
 *	@param bool $correled Optionnel, si activé, sont retournés les lignes correspondantes aux couples $prd / $cat (les deux arguments doivent avoir la même taille)
 *
 *	@return bool False en cas d'échec
 *	@return resource Retourne le résultat sous la forme d'une requête MySQL comprenant les champs suivants:
 *			- prd : identifiant du produit
 *			- cat : identifiant de la catégorie
 *			- pos : position dans le tri
 *			- cnt : identifiant du contenu pour le lien prd-cat
 *			- url : url du produit pour le lien prd-cat (priorité : cly_url_perso > cly_url_alias)
 *			- url_perso : url personnalisée du produit
 *			- cly_url_alias : url du produit générée automatiquement par le système
 *			- tag_title : référencement pour la balise title propre au lien produit-catégorie
 *			- tag_desc : référencement pour la balise meta-description propre au lien produit-catégorie
 *			- keywords : référencement pour la balise meta-keywords propre au lien produit-catégorie
 *			- date_created : date de création du classement
 *			- date_created_en : date de création du classement au format EN
 *			- is_canonical : si oui ou non l'url est canonique
*/
function prd_classify_get( $is_sync=false, $prd=0, $cat=0, $cnt=0, $sort=false, $publish=false, $correled=false ){
	global $config;

	$prd = control_array_integer( $prd, false );
	if( $prd === false ){
		return false;
	}

	$cat = control_array_integer( $cat, false );
	if( $cat === false ){
		return false;
	}

	if( $correled && sizeof($prd) != sizeof($cat) ){
		return false;
	}

	$sql = '
		select
			cly_prd_id as prd, cly_cat_id as cat, cly_prd_pos as pos, cly_cnt_id as cnt,
			if(ifnull(cly_url_perso, "") = "", cly_url_alias, cly_url_perso) as url, cly_url_perso as url_perso, cly_url_alias,
				cly_tag_title as tag_title, cly_tag_desc as tag_desc, cly_keywords as keywords,
			date_format(cly_date_created, "%d/%m/%Y à %H:%i") as date_created, cly_date_created as date_created_en,
			cly_url_is_canonical as is_canonical
		from
			prd_classify
			join prd_products on cly_tnt_id = prd_tnt_id and cly_prd_id = prd_id
			join prd_categories on cly_tnt_id = cat_tnt_id and cly_cat_id = cat_id
		where
			cly_tnt_id='.$config['tnt_id'].'
			and prd_date_deleted is null
			and cat_date_deleted is null
	';

	if( $is_sync ){
		$sql .= ' and cly_is_sync = 1';
	}

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($cat); $i++ ){
			$cnds[] = 'cly_cat_id = '.$cat[ $i ].' and cly_prd_id = '.$prd[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($prd) ){
			$sql .= ' and cly_prd_id in ('.implode(', ', $prd).')';
		}
		if( sizeof($cat) ){
			$sql .= ' and cly_cat_id in ('.implode(', ', $cat).')';
		}
	}

	if( is_numeric($cnt) && $cnt > 0 ){
		$sql .= ' and cly_cnt_id = '.$cnt;
	}

	if( $publish ){
		$sql .= '
			and cat_publish = 1 and cat_products > 0
			and cly_cat_id not in (
				select cat_child_id
				from prd_cat_hierarchy as h
				join prd_categories as c on h.cat_tnt_id = c.cat_tnt_id and h.cat_parent_id = c.cat_id
				where h.cat_tnt_id = '.$config['tnt_id'].'
				and c.cat_publish = 0
			)
		';
	}

	// Utilise le tri par défaut
	if( $sort===false ){
		$sort = array(
			'cly_url_is_canonical'=>'desc', // Url canonique en premier
			'cat_publish'=>'desc' // Catégories publiées en premier
		);
	}

	if( is_array($sort) ){
		$sort_ar = array();
		foreach( $sort as $col => $dir ){
			$sort_ar[] = addslashes($col).' '.( strtolower(trim($dir)) == 'desc' ? 'desc' : 'asc' );
		}
		if( sizeof($sort_ar) ){
			$sql .= ' order by '.implode(', ', $sort_ar);
		}
	}

	return ria_mysql_query($sql);

}

// \cond onlyria
/** Cette fonction permet de charger le référencement d'un produit dans une catégorie donnée.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $cat Optionnel, identifiant d'une catégorie
 *	@return bool false en cas de problème, sinon un tableau contenant :
 *				- tag_title : titre utilisé pour le référencement
 *				- tag_desc : description utilisé pour le référencement
 *				- keywords : mots clés utilisés pour le référencement
 */
function prd_classify_get_referencing( $prd, $cat=0 ){
	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($cat) || $cat<0 ) return false;
	global $config;

	$sql = '
		select cly_tag_title as tag_title, cly_tag_desc as tag_desc, cly_keywords as keywords
		from prd_classify
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_prd_id='.$prd.'
	';

	if( $cat>0 ){
		$sql .= ' and cly_cat_id='.$cat;
	}
	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}
	return ria_mysql_fetch_array( $res );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le titre utilisé dans le référencement d'un produit.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@return bool false si le référencement n'existe pas , sinon le titre utilisé
 */
function prd_classify_get_tagtitle( $prd, $cat ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;

	$res = ria_mysql_query('
		select ifnull(cly_tag_title, \'\') as tag_title
		from prd_classify
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_prd_id='.$prd.'
			and cly_cat_id='.$cat.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'tag_title' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la description utilisée dans le référencement d'un produit.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@return bool false si le référencement n'existe pas , sinon la description utilisée
 */
function prd_classify_get_tagdesc( $prd, $cat ){
	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select ifnull(cly_tag_desc, \'\') as tag_desc
		from prd_classify
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_prd_id='.$prd.'
			and cly_cat_id='.$cat.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'tag_desc' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les mots clés utilisés dans le référencement d'un produit.
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@return bool false si le référencement n'existe pas , sinon les mots clés utilisés
 */
function prd_classify_get_keywords( $prd, $cat ){
	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select ifnull(cly_keywords, \'\') as keywords
		from prd_classify
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_prd_id='.$prd.'
			and cly_cat_id='.$cat.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'keywords' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier si un produit existe bien dans une catégorie.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $prd Obligatoire, identifiant d'un produit ou tableau d'identifiant
 *	@return bool Retourne true si le produit est trouvé dans la catégorie (ou bien au moins un produit si $prd est un tableau)
 *	@return bool Retourne false dans le cas contraire
 */
function prd_classify_exists( $cat, $prd ){
	if( !is_numeric($cat) || $cat<=0 ) return false;

	$prd = control_array_integer($prd, true);
	if( $prd === false ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select 1
		from prd_classify
			join prd_categories on (cat_tnt_id=cly_tnt_id and cat_id=cly_cat_id)
			join prd_products on (prd_tnt_id=cat_tnt_id and prd_id=cly_prd_id)
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_cat_id='.$cat.' and cly_prd_id in ('.implode(', ', $prd).')
			and cat_date_deleted is null
			and prd_date_deleted is null
	');

	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows( $res )>0;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'url canonique d'un produit
 *	@param int $prd Obligatoire, identifiant d'un produit
 *
 *	@return string Retourne l'url canonique pour un produit
 */
function prd_classify_get_canonical_url( $prd ){
	if( !is_numeric($prd) || $prd<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select prd_canonical_id
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$prd.'
			and ifnull(prd_canonical_id, 0) > 0
			and prd_id != prd_canonical_id
	');

	if( $res && ria_mysql_num_rows($res) ){
		$r = ria_mysql_fetch_assoc($res);
		if( is_numeric($r['prd_canonical_id']) && $r['prd_canonical_id'] > 0){
			return prd_classify_get_canonical_url($r['prd_canonical_id']);
		}
	}

	$rcat = prd_products_categories_get( $prd, true, true, true );
	if( !$rcat || !ria_mysql_num_rows($rcat) ) return false;
	$cat = ria_mysql_fetch_array( $rcat );
	return $cat['url_alias'];
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retourner la catégorie portant l'url canonique de celui ci
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@return int|bool Retourne l'id de la catégorie canonique du produit, ou false si aucune n'est définie
 */
function prd_classify_get_canonical_cat( $prd ){
	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$rcat = ria_mysql_query('
		select cly_cat_id
		from prd_classify
		where cly_url_is_canonical = 1
			and cly_tnt_id = '.$config['tnt_id'].'
			and cly_prd_id = '.$prd.'
	');

	if( !$rcat || !ria_mysql_num_rows($rcat) ) return false;

	if( $c = ria_mysql_fetch_array( $rcat ) ){
		return $c["cly_cat_id"];
	}

	return false;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de définir une url canonique pour un produit
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $cat Optionnel, identifiant d'une catégorie
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_classify_set_canonical( $prd, $cat=0 ){
	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($cat) || $cat<0 ) return false;
	global $config;

	//s'assure que la catégorie spécifier existe pour ce produit et fait partie des catégories publiés
	if($cat != 0){
		$rcat = prd_products_categories_get( $prd, true, true );
		if( !$rcat || !ria_mysql_num_rows($rcat) ){
			return false;
		}

		$is_publish = false;
		while( $c = ria_mysql_fetch_array( $rcat ) ){
			if( $c['cat'] == $cat ){
				$is_publish = true;
				break;
			}
		}

		if( !$is_publish ){
			return false;
		}
	}


	// met à jour les classifications
	if( !ria_mysql_query('update prd_classify set cly_url_is_canonical=0 where cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = '.$prd ) ) return false;
	if($cat != 0){
		if( !ria_mysql_query('update prd_classify set cly_url_is_canonical=1 where cly_tnt_id = '.$config['tnt_id'].' and cly_cat_id = '.$cat.' and cly_prd_id = '.$prd ) ) return false;
	}

	prd_products_set_date_modified( $prd );
	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'url d'un classement.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param string $url Obligatoire, nouvelle url du classement
 *	@return bool True si l'enregistrement s'est correctement déroulé, False dans le cas contraire
 */
function prd_classify_set_url( $cat, $prd, $url ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( trim($url)=='' ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_classify
		set cly_url_alias="'.addslashes( $url ).'"
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_cat_id='.$cat.'
			and cly_prd_id='.$prd.'
	');

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'url personnalisation d'une classification de produit
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param string $url Obligatoire, nouvelle url
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_classify_set_url_perso( $cat, $prd, $url ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( trim($url)=='' ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_classify
		set cly_url_perso="'.addslashes( $url ).'"
		where cly_tnt_id='.$config['tnt_id'].'
		and cly_cat_id = '.$cat.'
		and cly_prd_id = '.$prd.'
		');

	if( !$res ){
		return false;
	}

	$rcnt = ria_mysql_query('
		select cly_cnt_id as cnt_id
		from prd_classify
		where cly_tnt_id='.$config['tnt_id'].'
		and cly_cat_id = '.$cat.'
		and cly_prd_id = '.$prd.'
		');

	if( $rcnt && ria_mysql_num_rows($rcnt) ){
		$cnt = ria_mysql_fetch_array( $rcnt );
		if (isset($cnt) && is_numeric($cnt) && $cnt > 0) {
			$res = ria_mysql_query('
				update search_contents
				set cnt_url="'.addslashes( $url ).'"
				where cnt_tnt_id='.$config['tnt_id'].'
				and cnt_id='.$cnt['cnt_id'].'
				and cnt_type_id=2
				');

			if( !$res ){
				return false;
			}
		}
	}

	return true;
}
// \endcond


// \cond onlyria
/** Cette fonction permet de supprimer l'url personnalisation d'une classification de produit tout en remettant l'url de base pour la recherche de contenu
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param string $base_url Obligatoire, nouvelle url
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_classify_del_and_set_base_url_perso( $cat, $prd, $base_url ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( trim($base_url)=='' ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_classify
		set cly_url_perso=""
		where cly_tnt_id='.$config['tnt_id'].'
		and cly_cat_id = '.$cat.'
		and cly_prd_id = '.$prd.'
		');

	if( !$res ){
		return false;
	}

	$rcnt = ria_mysql_query('
		select cly_cnt_id as cnt_id
		from prd_classify
		where cly_tnt_id='.$config['tnt_id'].'
		and cly_cat_id = '.$cat.'
		and cly_prd_id = '.$prd.'
		');

	if( $rcnt && ria_mysql_num_rows($rcnt) ){
		$cnt = ria_mysql_fetch_array( $rcnt );
		if (isset($cnt) && is_numeric($cnt) && $cnt > 0) {
			$res = ria_mysql_query('
				update search_contents
				set cnt_url="'.addslashes( $base_url ).'"
				where cnt_tnt_id='.$config['tnt_id'].'
				and cnt_id='.$cnt['cnt_id'].'
				and cnt_type_id=2
				');

			if( !$res ){
				return false;
			}
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Permet le déplacement vers le haut d'un produit dans une catégorie donnée. S'applique aux catégories dont le tri des produits sont triés de manière personnalisée.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_classify_move_up( $cat, $prd ){
	if( !prd_classify_exists($cat, $prd) ) return false;
	global $config;

	// Charge la catégorie
	$rcly = prd_classify_get( false, $prd, $cat );
	if( !$rcly || !ria_mysql_num_rows($rcly) ) return false;
	$cly = ria_mysql_fetch_array($rcly);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( $cly['pos']==='' ) return false;

	// S'assure que la catégorie n'est pas déjà la première de la liste
	if( $cly['pos']===null ) return false;

	// Permute la catégorie avec celle qui se trouve juste au dessus
	if( !ria_mysql_query('update prd_classify set cly_prd_pos='.($cly['pos']).' where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$cat.' and cly_prd_pos='.($cly['pos']-1)) )
		return false;
	if( !ria_mysql_query('update prd_classify set cly_prd_pos='.($cly['pos']-1).' where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$cat.' and cly_prd_id='.$prd) )
		return false;

	prd_products_set_date_modified( $prd );
	return true;
}
// \endcond

/** Cette fonction permet de récupérer les optimisations de titre, de descritpion et de description longue pour un produit
 * @param int $cat_id  Identifiant de la catégorie
 * @param array $product Objet produit
 * @return array Retourne le produit avec ou sans les surcharge d'optimisation
 */
function prd_classify_optimise_product( $cat_id,array $product ){
	if( !is_numeric($cat_id) || $cat_id<=0 ){
		throw new InvalidArgumentException("L'identifaint de catégorie doit être un numérique supérieur a 0");
	}

	$fields =  array(_FLD_CLY_PRD_TITLE=>'title', _FLD_CLY_PRD_DESC=>'desc', _FLD_CLY_PRD_DESC_LONG => 'desc-long');

	if( !ria_array_key_exists(array_merge($fields, array('id')), $product) ){
		throw new InvalidArgumentException("Le produit doit avoir les champs suivant (id, title, desc, desc-long)");
	}
	global $config;

	if( i18n::getLang() == $config['i18n_lng'] ){
		$res = ria_mysql_query('
			select cly_prd_title as title, cly_prd_desc as "desc", cly_prd_desc_long as "desc-long"
			from prd_classify
			where cly_tnt_id='.$config['tnt_id'].'
			and cly_cat_id='.$cat_id.'
			and cly_prd_id='.$product['id'].'
		');

		if(!$res || !ria_mysql_num_rows($res)){
			return $product;
		}

		$overide_optimisation = ria_mysql_fetch_assoc($res);
	}else{
		$overide_optimisation = fld_translates_get( CLS_PRODUCT, array($cat_id, $product['id']), i18n::getLang(), $product, $fields );
	}

	foreach($fields as $field){
		if( trim($overide_optimisation[$field]) == '' ){
			continue;
		}
		$prd_field = $field;
		if( isset($product['is_sync']) && $product['is_sync'] == 0 && $field === 'title' ){
			$prd_field = 'name';
		}
		$product[$prd_field] = $overide_optimisation[$field];
	}

	return $product;
}

// \cond onlyria
/** Cette fonction retourne le titre du produit pour un classement
 * @param int $cat_id Identifiant de la catégorie
 * @param int $prd_id Identifiant du produit
 * @return string Le titre du produit
 */
function prd_classify_prd_title_get( $cat_id, $prd_id ){
	global $config;

	if( !is_numeric($cat_id) || $cat_id<=0 ){
		return false;
	}
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select cly_prd_title as prd_title
		from prd_classify
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_cat_id='.$cat_id.'
			and cly_prd_id='.$prd_id.'
	');

	if(!$res || !ria_mysql_num_rows($res)){
		return '';
	}

	$prd = ria_mysql_fetch_assoc($res);
	return $prd['prd_title'];
}
// \endcond

// \cond onlyria
/** Cette fonction retourne la description courte du produit pour un classement
 * @param int $cat_id Identifiant de la catégorie
 * @param int $prd_id Identifiant du produit
 * @return string La description courte du produit
 */
function prd_classify_prd_desc_get( $cat_id, $prd_id ){
	global $config;

	if( !is_numeric($cat_id) || $cat_id<=0 ){
		return false;
	}
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select cly_prd_desc as prd_desc
		from prd_classify
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_cat_id='.$cat_id.'
			and cly_prd_id='.$prd_id.'
	');

	if(!$res || !ria_mysql_num_rows($res)){
		return '';
	}

	$prd = ria_mysql_fetch_assoc($res);
	return $prd['prd_desc'];
}
// \endcond

// \cond onlyria
/** Cette fonction retourne la description longue du produit pour un classement
 * @param int $cat_id Identifiant de la catégorie
 * @param int $prd_id Identifiant du produit
 * @return string La description longue du produit
 */
function prd_classify_prd_desc_long_get( $cat_id, $prd_id ){
	global $config;

	if( !is_numeric($cat_id) || $cat_id<=0 ){
		return false;
	}
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select cly_prd_desc_long as prd_desc_long
		from prd_classify
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_cat_id='.$cat_id.'
			and cly_prd_id='.$prd_id.'
	');

	if(!$res || !ria_mysql_num_rows($res)){
		return '';
	}

	$prd = ria_mysql_fetch_assoc($res);
	return $prd['prd_desc_long'];
}
// \endcond

// \cond onlyria
/** Cette fonction permet la mise à jour des informations du produit en fonction de son classement pour le référencement
 * @param int $cat_id        Identifiant de la catégorie
 * @param int $prd_id        Identifiant du produit
 * @param string $prd_title     Facultatif, titre de surcharge pour le produit
 * @param string $prd_desc      Facultatif, description courte de surcharge pour le produit
 * @param string $prd_desc_long Facultatif, description longue de surcharge pour le produit
 * @return bool True si succès, false si erreur
 */
function prd_classify_update_optimisation( $cat_id, $prd_id, $prd_title='', $prd_desc='', $prd_desc_long='' ){
	if( !is_numeric($cat_id) || $cat_id <= 0 || !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_classify
		set cly_prd_title = '.(trim($prd_title) ? '\''.addslashes($prd_title).'\'' : 'null').',
		cly_prd_desc = '.(trim($prd_desc) ? '\''.addslashes($prd_desc).'\'' : 'null').',
		cly_prd_desc_long = '.(trim($prd_desc_long) ? '\''.addslashes($prd_desc_long).'\'' : 'null').'
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_cat_id='.$cat_id.'
			and cly_prd_id='.$prd_id.'
	');

	if( $res ){
		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $prd_id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	prd_products_set_date_modified($prd_id);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour les informations de référencement propre au lien produit/catégorie.
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param string $title Optionnel, titre utilisé dans le référencement
 *	@param string $desc Optionnel, description utilisé dans le référencement
 *	@param string $keywords Optionnel, mots clés utilisés dans le référencement
 *	@return bool true si la mise à jour s'est correctement passée, false dans le cas contraire
 */
function prd_classify_update_referencing( $cat, $prd, $title='', $desc='', $keywords='' ){
	global $config;

	if( !is_numeric($cat) || $cat <= 0 || !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	$keywords = str_replace(array("\r", "\n"), array(' ', ' '), $keywords);

	$res = ria_mysql_query('
		update prd_classify
		set cly_tag_title = '.(trim($title) ? '\''.addslashes($title).'\'' : 'null').',
		cly_tag_desc = '.(trim($desc) ? '\''.addslashes($desc).'\'' : 'null').',
		cly_keywords = '.(trim($keywords) ? '\''.addslashes($keywords).'\'' : 'null').'
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_cat_id='.$cat.'
			and cly_prd_id='.$prd.'
	');

	if( $res ){
		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $prd,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	prd_products_set_date_modified($prd);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise TITLE propre au lien Produit / Catégorie.
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param string $tag_title Optionnel, titre utilisé dans le référencement
 *	@return bool true si la mise à jour s'est correctement passée, false dans le cas contraire
 */
function prd_classify_update_referencing_tag_title( $cat, $prd, $tag_title='' ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_classify
		set cly_tag_title = '.( trim($tag_title) ? '\''.addslashes($tag_title).'\'' : 'null' ).'
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_cat_id='.$cat.'
			and cly_prd_id='.$prd.'
	');

	if( $res ){
		prd_products_set_date_modified( $prd );
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise META DESCRIPTION propre au lien Produit / Catégorie.
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param string $tag_desc Optionnel, description utilisée dans le référencement
 *	@return bool true si la mise à jour s'est correctement passée, false dans le cas contraire
 */
function prd_classify_update_referencing_tag_desc( $cat, $prd, $tag_desc='' ){
	global $config;

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}
	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		update prd_classify
		set cly_tag_desc = '.( trim($tag_desc) ? '\''.addslashes($tag_desc).'\'' : 'null' ).'
		where cly_tnt_id='.$config['tnt_id'].'
			and cly_cat_id='.$cat.'
			and cly_prd_id='.$prd.'
	');

	if( $res ){
		prd_products_set_date_modified( $prd );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Permet le déplacement vers le bas d'une catégorie. S'applique aux catégories dont les enfants
 *	sont triés de manière personnalisée.
 *	@param int $cat Catégorie à déplacer vers le bas
 *	@param int $prd Identifiant d'un produit qui est classé dans la catégorie $cat
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_classify_move_down( $cat, $prd ){
	if( !prd_classify_exists($cat, $prd) ) return false;
	global $config;

	// Charge la catégorie
	$rcly = prd_classify_get( false, $prd, $cat );
	if( !$rcly || !ria_mysql_num_rows($rcly) ) return false;
	$cly = ria_mysql_fetch_array($rcly);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( $cly['pos']==='' ) return false;

	// S'assure que la catégorie n'est pas déjà la première de la liste
	if( $cly['pos']===null ) return false;

	// Permute la catégorie avec celle qui se trouve juste au dessus
	if( !ria_mysql_query('update prd_classify set cly_prd_pos='.($cly['pos']).' where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$cat.' and cly_prd_pos='.($cly['pos']+1)) )
		return false;
	if( !ria_mysql_query('update prd_classify set cly_prd_pos='.($cly['pos']+1).' where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$cat.' and cly_prd_id='.$prd) )
		return false;

	prd_products_set_date_modified( $prd );
	return true;
}
// \endcond

