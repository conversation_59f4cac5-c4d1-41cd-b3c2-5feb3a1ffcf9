# AnalyticsProductColumnFilter

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**operator_name** | [**\Swagger\Client\Model\AnalyticsProductColumnFilterOperatorName**](AnalyticsProductColumnFilterOperatorName.md) |  | 
**values** | **string[]** | Must be null if the operator is \&quot;IsNull\&quot; or \&quot;IsNotNull\&quot;. Can contains multiple value in case of \&quot;InList\&quot; operator. Otherwise a single value is expected. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


