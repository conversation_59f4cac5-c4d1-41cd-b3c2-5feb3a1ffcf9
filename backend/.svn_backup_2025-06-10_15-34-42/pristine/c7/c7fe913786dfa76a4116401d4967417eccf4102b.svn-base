var currentAjaxRequest = ajaxCurrent = directory = false;
var limit = 0; var pageResults = 1;
var haveResults = activeScroll = true;
var scrollNow = 0;
var imageID = 0;
var timer = false;
var timer2 = false;

var ImageDialog = {
	preInit : function() {
		var url;

		tinyMCEPopup.requireLangPack();

		if (url = tinyMCEPopup.getParam("external_image_list_url"))
			document.write('<script language="javascript" src="' + tinyMCEPopup.editor.documentBaseURI.toAbsolute(url) + '"></script>');
	},

	actionScroll : function(){
		$(window).scroll(function(){
			if( !activeScroll ) return false;
			limit = ($('#select-img').innerHeight()-500);
			scrollNow = $(window).scrollTop()
			
			if( scrollNow==0 ){
				$('#back-top-search').hide();
				$('#back-top-search2').hide();
			} else {
				var tmpWidth = $('#search-img').width()-155;
				
				$('#back-top-search').show();
				$('#back-top-search').css( 'width', tmpWidth+'px' );
				$('#back-top-search2').css( 'width', tmpWidth+'px' );
				
				$('#back-top-search2').show()
				$('#back-top-search2').css( 'margin-top', ($(window).height()-43)+'px' );
			}
			
			if( haveResults && !ajaxCurrent && limit < scrollNow ){
				ajaxCurrent = true;
				pageResults++;
				
				ImageDialog.showImageLibrary( pageResults );
				$('#select-img .load-img').remove();
			}
		});
	},
	
	myOnScroll : function( actived ){
		activeScroll = actived;
	},
	
	init : function(ed) {
		var f = document.forms[0], nl = f.elements, ed = tinyMCEPopup.editor, dom = ed.dom, n = ed.selection.getNode(), fl = tinyMCEPopup.getParam('external_image_list', 'tinyMCEImageList');
		
		ImageDialog.actionScroll();
		
		tinyMCEPopup.resizeToInnerSize();
		this.fillClassList('class_list');
		this.fillFileList('src_list', fl);
		this.fillFileList('over_list', fl);
		this.fillFileList('out_list', fl);
		TinyMCE_EditableSelects.init();

		if (n.nodeName == 'IMG') {
			nl.src.value = dom.getAttrib(n, 'src');
			nl.width.value = dom.getAttrib(n, 'width');
			nl.height.value = dom.getAttrib(n, 'height');
			nl.alt.value = dom.getAttrib(n, 'alt');
			nl.title.value = dom.getAttrib(n, 'title');
			nl.vspace.value = this.getAttrib(n, 'vspace');
			nl.hspace.value = this.getAttrib(n, 'hspace');
			nl.border.value = this.getAttrib(n, 'border');
			selectByValue(f, 'align', this.getAttrib(n, 'align'));
			selectByValue(f, 'class_list', dom.getAttrib(n, 'class'), true, true);
			nl.style.value = dom.getAttrib(n, 'style');
			nl.id.value = dom.getAttrib(n, 'id');
			nl.dir.value = dom.getAttrib(n, 'dir');
			nl.lang.value = dom.getAttrib(n, 'lang');
			nl.usemap.value = dom.getAttrib(n, 'usemap');
			nl.longdesc.value = dom.getAttrib(n, 'longdesc');
			nl.insert.value = ed.getLang('update');

			if (/^\s*this.src\s*=\s*\'([^\']+)\';?\s*$/.test(dom.getAttrib(n, 'onmouseover')))
				nl.onmouseoversrc.value = dom.getAttrib(n, 'onmouseover').replace(/^\s*this.src\s*=\s*\'([^\']+)\';?\s*$/, '$1');

			if (/^\s*this.src\s*=\s*\'([^\']+)\';?\s*$/.test(dom.getAttrib(n, 'onmouseout')))
				nl.onmouseoutsrc.value = dom.getAttrib(n, 'onmouseout').replace(/^\s*this.src\s*=\s*\'([^\']+)\';?\s*$/, '$1');

			var edit = false;
			if( nl.width.value && nl.height.value ){
				var selectImageID = false;
				if( typeof $('#imgid') != 'undefined' && $('#imgid').length ){
					selectImageID = $('#imgid').val();
				}

				if( !isNaN(parseInt(selectImageID)) ){
					imageID = selectImageID;
					// ImageDialog.selectImage( imageID );
					ImageDialog.changeDimensions();
				}else{
					var temp = $('#src').val();

					temp = temp.substring( temp.lastIndexOf('/') + 1 );
					temp = temp.substring( 0, temp.indexOf('.') );
					
					if( temp.match('-') ){
						temp = temp.substring( temp.indexOf('-') + 1 );
					}

					imageID = temp;
				}
				
				// Taille pré-déterminer
				if( typeof $('#' + nl.width.value + 'x' + nl.height.value) != 'undefined' && $('#' + nl.width.value + 'x' + nl.height.value).length ){
					edit = true;
					$('#dimensions #' + nl.width.value + 'x' + nl.height.value).attr('selected', 'selected');
					this.initDimensionsPerso();
				}

				// Taille personnalisée
				if( nl.src.value.match(window.parent.ui_config.img_url+'/customized/') ){
					edit = true;
					
					$('#persowidth').val( nl.width.value );
					$('#persoheight').val( nl.height.value );
					$('#dimensions #customized').attr('selected', 'selected');
					
					this.showDimensionsPerso();
				}
			}

			if( !edit ){
				// var tdHtml = '';
				// tdHtml += '<span style="display:none" id="width_voiceLabel">{#advimage_dlg.width}</span>';
				// tdHtml += '<input name="width" type="text" id="width" value="" size="5" maxlength="5" class="size" onchange="ImageDialog.changeHeight();" aria-labelledby="width_voiceLabel" /> x ';
				// tdHtml += '<span style="display:none" id="height_voiceLabel">{#advimage_dlg.height}</span>';
				// tdHtml += '<input name="height" type="text" id="height" value="" size="5" maxlength="5" class="size" onchange="ImageDialog.changeWidth();" aria-labelledby="height_voiceLabel" /> px';
				
				// $('#dimensions').parent().addClass('nowrap').html( tdHtml );
				$('#constrain, #constrainlabel').show();
				$('#cancel').attr('onclick', 'tinyMCEPopup.close();');
			}
			
			if (ed.settings.inline_styles) {
				// Move attribs to styles
				if (dom.getAttrib(n, 'align'))
					this.updateStyle('align');

				if (dom.getAttrib(n, 'hspace'))
					this.updateStyle('hspace');

				if (dom.getAttrib(n, 'border'))
					this.updateStyle('border');

				if (dom.getAttrib(n, 'vspace'))
					this.updateStyle('vspace');
			}
			
			$('#edit-img').show();
			$('#media-images').hide();
			$('#source').hide();
		} else if( !$('#imgid').length || $('#imgid').val()==0 ){
			this.showImageLibrary(1);
		}


		// Setup browse button
		document.getElementById('srcbrowsercontainer').innerHTML = getBrowserHTML('srcbrowser','src','image','theme_advanced_image');
		if (isVisible('srcbrowser'))
			document.getElementById('src').style.width = '260px';

		// Setup browse button
		document.getElementById('onmouseoversrccontainer').innerHTML = getBrowserHTML('overbrowser','onmouseoversrc','image','theme_advanced_image');
		if (isVisible('overbrowser'))
			document.getElementById('onmouseoversrc').style.width = '260px';

		// Setup browse button
		document.getElementById('onmouseoutsrccontainer').innerHTML = getBrowserHTML('outbrowser','onmouseoutsrc','image','theme_advanced_image');
		if (isVisible('outbrowser'))
			document.getElementById('onmouseoutsrc').style.width = '260px';

		// Check swap image if valid data
		if (nl.onmouseoversrc.value || nl.onmouseoutsrc.value)
			this.setSwapImage(true);
		else
			this.setSwapImage(false);

		this.changeAppearance();
		this.showPreviewImage(nl.src.value, 1);
	},

	insert : function(file, title) {
		if( !ImageDialog.changeOrCreatePersoImage() ){
			console.log( 'error insert : changeOrCreatePersoImage');
			return false;
		}

		var ed = tinyMCEPopup.editor, t = this, f = document.forms[0];

		if (f.src.value === '') {
			if (ed.selection.getNode().nodeName == 'IMG') {
				ed.dom.remove(ed.selection.getNode());
				ed.execCommand('mceRepaint');
			}

			tinyMCEPopup.close();
			return;
		}

		if (tinyMCEPopup.getParam("accessibility_warnings", 1)) {
			if (!f.alt.value) {
				tinyMCEPopup.confirm(tinyMCEPopup.getLang('advimage_dlg.missing_alt'), function(s) {
					if (s)
						t.insertAndClose();
				});

				return;
			}
		}

		t.insertAndClose();
	},

	insertAndClose : function() {
		var ed = tinyMCEPopup.editor, f = document.forms[0], nl = f.elements, v, args = {}, el;

		tinyMCEPopup.restoreSelection();

		// Fixes crash in Safari
		if (tinymce.isWebKit)
			ed.getWin().focus();

		if (!ed.settings.inline_styles) {
			args = {
				vspace : nl.vspace.value,
				hspace : nl.hspace.value,
				border : nl.border.value,
				align : getSelectValue(f, 'align')
			};
		} else {
			// Remove deprecated values
			args = {
				vspace : '',
				hspace : '',
				border : '',
				align : ''
			};
		}

		tinymce.extend(args, {
			src : nl.src.value.replace(/ /g, '%20'),
			alt : nl.alt.value,
			title : nl.title.value,
			'class' : getSelectValue(f, 'class_list'),
			style : nl.style.value,
			id : nl.id.value,
			dir : nl.dir.value,
			lang : nl.lang.value,
			usemap : nl.usemap.value,
			longdesc : nl.longdesc.value
		});
		if( nl.width.value != 0 ){
			args.width = nl.width.value;
		}
		if( nl.height.value != 0 ){
			args.height = nl.height.value;
		}

		args.onmouseover = args.onmouseout = '';

		if (f.onmousemovecheck.checked) {
			if (nl.onmouseoversrc.value)
				args.onmouseover = "this.src='" + nl.onmouseoversrc.value + "';";

			if (nl.onmouseoutsrc.value)
				args.onmouseout = "this.src='" + nl.onmouseoutsrc.value + "';";
		}

		el = ed.selection.getNode();

		if (el && el.nodeName == 'IMG') {
			ed.dom.setAttribs(el, args);
		} else {
			tinymce.each(args, function(value, name) {
				if (value === "") {
					delete args[name];
				}
			});

			ed.execCommand('mceInsertContent', false, tinyMCEPopup.editor.dom.createHTML('img', args), {skip_undo : 1});
			ed.undoManager.add();
		}

		tinyMCEPopup.editor.execCommand('mceRepaint');
		tinyMCEPopup.editor.focus();
		tinyMCEPopup.close();
	},

	getAttrib : function(e, at) {
		var ed = tinyMCEPopup.editor, dom = ed.dom, v, v2;

		if (ed.settings.inline_styles) {
			switch (at) {
				case 'align':
					if (v = dom.getStyle(e, 'float'))
						return v;

					if (v = dom.getStyle(e, 'vertical-align'))
						return v;

					break;

				case 'hspace':
					v = dom.getStyle(e, 'margin-left')
					v2 = dom.getStyle(e, 'margin-right');

					if (v && v == v2)
						return parseInt(v.replace(/[^0-9]/g, ''));

					break;

				case 'vspace':
					v = dom.getStyle(e, 'margin-top')
					v2 = dom.getStyle(e, 'margin-bottom');
					if (v && v == v2)
						return parseInt(v.replace(/[^0-9]/g, ''));

					break;

				case 'border':
					v = 0;

					tinymce.each(['top', 'right', 'bottom', 'left'], function(sv) {
						sv = dom.getStyle(e, 'border-' + sv + '-width');

						// False or not the same as prev
						if (!sv || (sv != v && v !== 0)) {
							v = 0;
							return false;
						}

						if (sv)
							v = sv;
					});

					if (v)
						return parseInt(v.replace(/[^0-9]/g, ''));

					break;
			}
		}

		if (v = dom.getAttrib(e, at))
			return v;

		return '';
	},

	setSwapImage : function(st) {
		var f = document.forms[0];

		f.onmousemovecheck.checked = st;
		setBrowserDisabled('overbrowser', !st);
		setBrowserDisabled('outbrowser', !st);

		if (f.over_list)
			f.over_list.disabled = !st;

		if (f.out_list)
			f.out_list.disabled = !st;

		f.onmouseoversrc.disabled = !st;
		f.onmouseoutsrc.disabled  = !st;
	},

	fillClassList : function(id) {
		var dom = tinyMCEPopup.dom, lst = dom.get(id), v, cl;

		if (v = tinyMCEPopup.getParam('theme_advanced_styles')) {
			cl = [];

			tinymce.each(v.split(';'), function(v) {
				var p = v.split('=');

				cl.push({'title' : p[0], 'class' : p[1]});
			});
		} else
			cl = tinyMCEPopup.editor.dom.getClasses();

		if (cl.length > 0) {
			lst.options.length = 0;
			lst.options[lst.options.length] = new Option(tinyMCEPopup.getLang('not_set'), '');

			tinymce.each(cl, function(o) {
				lst.options[lst.options.length] = new Option(o.title || o['class'], o['class']);
			});
		} else
			dom.remove(dom.getParent(id, 'tr'));
	},

	fillFileList : function(id, l) {
		var dom = tinyMCEPopup.dom, lst = dom.get(id), v, cl;

		l = typeof(l) === 'function' ? l() : window[l];
		lst.options.length = 0;

		if (l && l.length > 0) {
			lst.options[lst.options.length] = new Option('', '');

			tinymce.each(l, function(o) {
				lst.options[lst.options.length] = new Option(o[0], o[1]);
			});
		} else
			dom.remove(dom.getParent(id, 'tr'));
	},

	resetImageData : function() {
		var f = document.forms[0];

		f.elements.width.value = f.elements.height.value = '';
	},

	updateImageData : function(img, st) {
		var f = document.forms[0];

		if (!st) {
			f.elements.width.value = img.width;
			f.elements.height.value = img.height;
		}

		this.preloadImg = img;
	},

	changeAppearance : function() {
		var ed = tinyMCEPopup.editor, f = document.forms[0], img = document.getElementById('alignSampleImg');

		if (img) {
			if (ed.getParam('inline_styles')) {
				ed.dom.setAttrib(img, 'style', f.style.value);
			} else {
				img.align = f.align.value;
				img.border = f.border.value;
				img.hspace = f.hspace.value;
				img.vspace = f.vspace.value;
			}
		}
	},

	changeHeight : function() {
		var f = document.forms[0], tp, t = this;

		if (!f.constrain.checked || !t.preloadImg) {
			return;
		}

		if (f.width.value == "" || f.height.value == "")
			return;

		tp = (parseInt(f.width.value) / parseInt(t.preloadImg.width)) * t.preloadImg.height;
		f.height.value = tp.toFixed(0);
	},

	changeWidth : function() {
		var f = document.forms[0], tp, t = this;

		if (!f.constrain.checked || !t.preloadImg) {
			return;
		}

		if (f.width.value == "" || f.height.value == "")
			return;

		tp = (parseInt(f.height.value) / parseInt(t.preloadImg.height)) * t.preloadImg.width;
		f.width.value = tp.toFixed(0);
	},

	updateStyle : function(ty) {
		var dom = tinyMCEPopup.dom, b, bStyle, bColor, v, isIE = tinymce.isIE, f = document.forms[0], img = dom.create('img', {style : dom.get('style').value});

		if (tinyMCEPopup.editor.settings.inline_styles) {
			// Handle align
			if (ty == 'align') {
				dom.setStyle(img, 'float', '');
				dom.setStyle(img, 'vertical-align', '');

				v = getSelectValue(f, 'align');
				if (v) {
					if (v == 'left' || v == 'right')
						dom.setStyle(img, 'float', v);
					else
						img.style.verticalAlign = v;
				}
			}

			// Handle border
			if (ty == 'border') {
				b = img.style.border ? img.style.border.split(' ') : [];
				bStyle = dom.getStyle(img, 'border-style');
				bColor = dom.getStyle(img, 'border-color');

				dom.setStyle(img, 'border', '');

				v = f.border.value;
				if (v || v == '0') {
					if (v == '0')
						img.style.border = isIE ? '0' : '0 none none';
					else {
						var isOldIE = tinymce.isIE && (!document.documentMode || document.documentMode < 9);

						if (b.length == 3 && b[isOldIE ? 2 : 1])
							bStyle = b[isOldIE ? 2 : 1];
						else if (!bStyle || bStyle == 'none')
							bStyle = 'solid';
						if (b.length == 3 && b[isIE ? 0 : 2])
							bColor = b[isOldIE ? 0 : 2];
						else if (!bColor || bColor == 'none')
							bColor = 'black';
						img.style.border = v + 'px ' + bStyle + ' ' + bColor;
					}
				}
			}

			// Handle hspace
			if (ty == 'hspace') {
				dom.setStyle(img, 'marginLeft', '');
				dom.setStyle(img, 'marginRight', '');

				v = f.hspace.value;
				if (v) {
					img.style.marginLeft = v + 'px';
					img.style.marginRight = v + 'px';
				}
			}

			// Handle vspace
			if (ty == 'vspace') {
				dom.setStyle(img, 'marginTop', '');
				dom.setStyle(img, 'marginBottom', '');

				v = f.vspace.value;
				if (v) {
					img.style.marginTop = v + 'px';
					img.style.marginBottom = v + 'px';
				}
			}

			// Merge
			dom.get('style').value = dom.serializeStyle(dom.parseStyle(img.style.cssText), 'img');
		}
	},

	changeMouseMove : function() {
	},

	showPreviewImage : function(u, st) {
		if (!u) {
			tinyMCEPopup.dom.setHTML('prev', '');
			return;
		}

		if (!st && tinyMCEPopup.getParam("advimage_update_dimensions_onchange", true))
			this.resetImageData();

		u = tinyMCEPopup.editor.documentBaseURI.toAbsolute(u);

		if (!st)
			tinyMCEPopup.dom.setHTML('prev', '<img id="previewImg" src="' + u + '" border="0" onload="ImageDialog.updateImageData(this);" onerror="ImageDialog.resetImageData();" />');
		else
			tinyMCEPopup.dom.setHTML('prev', '<img id="previewImg" src="' + u + '" border="0" onload="ImageDialog.updateImageData(this, 1);" />');
	},
	
	getImageThumbs : function( size ){
		var thumbs = { 'width' : '80','height' : '80','background' : '#FFFFFF','format' : 'jpg','clip' : '1','transparent' : '0' };
		if( currentAjaxRequest )
			currentAjaxRequest.abort();
		
		currentAjaxRequest = $.ajax({
			url: '/admin/ajax/tinymce/ajax-images.php?size=' + encodeURIComponent(size),
			type: 'get',
			dataType: 'json',
			async: false,
			success: function(data){ thumbs = data; },
			complete: function(){ currentAjaxRequest = false; }
		});

		return thumbs;
	},
	
	showImageLibrary : function( page ){
		this.cancelNewImage();
		var e = this;
		var q = $('#q-img').val();
		
		var dataUrl = 'images=1';
		dataUrl += '&page=' + ( page!=undefined ? page : 1 );
		dataUrl += '&q=' + encodeURIComponent(q);
		dataUrl += '&type=' + directory;
		
		$('#select-img a').each(function(){
			var imgID = $(this).attr('href').substring( 14 );
			dataUrl += '&imgs[]=' + imgID;
		});
		
		if( currentAjaxRequest )
			currentAjaxRequest.abort();
		
		currentAjaxRequest = $.ajax({
			url: '/admin/ajax/tinymce/ajax-images.php',
			data: dataUrl,
			type: 'post',
			dataType: 'json',
			async: false,
			success: function(data){
				var d = html = false;
				var t = e.getImageThumbs( 'small' );
				
				if( data['images'] != undefined ){
					html = '';
					for( var i=0 ; i<data['images'].length ; i++ ){
						d = data['images'][ i ];
						d.name = d.name==null ? '' : d.name;
						html += '<a onclick="return ImageDialog.selectImage(' + d.id + ');" href="image.php?img=' + d.id + '" class="img">';
						html += '	<img width="' + t.width + '" height="' + t.height + '" src="'+window.parent.ui_config.img_url + '/' + t.width + 'x' + t.height + '/' + d.id + '.' + t.format + '" alt="' + d.name + '" />';
						html += '</a>';
					}
				} else {
					haveResults = false;
					/* if( !$('#select-img a').length ){
						$('#select-img').html( '<div>Aucune image</div>' );
					} */
				}
				
				$('#select-img').append( html ).show();
				$('#search-img').show();
				ajaxCurrent = false;
			},
			complete: function(){ currentAjaxRequest = false; }
		});
		
		return false;
	},
	
	newImage : function(){
		$('#media-images').hide();
		$('#add-image').show();
		$('#search-img').hide();
		$('#source').hide();
		ImageDialog.myOnScroll( false );
		return false;
	},
	
	cancelNewImage : function(){
		$('#media-images').show();
		$('#add-image').hide();
		$('#search-img').show();
		$('#source').show();
		
		ImageDialog.myOnScroll( true );
		$(window).scrollTop(scrollNow);
		
		return false;
	},
	
	addNewImage : function(){
		$('#advimage form').submit();
		return false;
	},
	
	changeDimensions : function(){
		var dimensions = $('#dimensions').val();
		var h = dimensions.substring( 0, dimensions.indexOf('x') );
		var w = dimensions.substring( dimensions.indexOf('x')+1, dimensions.indexOf('|') );
		var format = dimensions.substring( dimensions.indexOf('|')+1 );
		
		if( dimensions == 'customized' ){
			this.showDimensionsPerso();
		}else{
			$('#persosize').slideUp();
		
			$('#width').val( h );
			$('#height').val( w );
			
			var url = window.parent.ui_config.img_url + '/' + h + 'x' + w + '/' + imageID + '.' + format;
			$('#src').val( url );
			
			this.showPreviewImage( url, 1 );
		}
		return false;
	},

	initDimensionsPerso : function() {
		currentAjaxRequest = $.ajax({
			url: '/admin/ajax/tinymce/ajax-images.php',
			data: 'get-source=1&img_id=' + imageID,
			type: 'post',
			dataType: 'json',
			async: false,
			success: function(data){
				$('#persowidth').val( data.width );
				$('#persoheight').val( data.height );
			},
			complete: function(){ currentAjaxRequest = false; }
		});
	},

	showDimensionsPerso : function(){
		$('#persosize').slideDown();
		
		if( $.trim($('#persowidth').val()) == '' ){
			this.initDimensionsPerso();
		}
		
		return false;
	},

	changeOrCreatePersoImage : function(){
		$('.error').remove();

		if( typeof $('#dimensions #customized') =='undefined' || !$('#dimensions #customized').is(':selected') ){console.log('passe');
			return true;
		}

		var error  = false;
		var width  = parseInt( $('#persowidth').val() );
		var height = parseInt( $('#persoheight').val() );
		
		if( isNaN(width) || isNaN(height) || width<=0 || height<=0 ){
			error = "Les dimensions personnalisées ne sont pas valide, merci de renseigner une longeur et largeur supérieur à zéro.";
		}else if( typeof $('#' + width + 'x' + height) != 'undefined' && $('#' + width + 'x' + height).length ){
			$('.error').remove();
			error = "Cette dimension fait partie de celles pré-définies et ne peut donc pas être utilisée. <br />Vous pouvez toutefois la sélectionnée dans la liste déroulante \"Dimensions\".</div>";
			
		}

		if( error !== false ){
			$('#persowidth').parents('fieldset').before( '<div class="error">' + error + '</div>' );
			return false;
		}else{
			currentAjaxRequest = $.ajax({
				url: '/admin/ajax/tinymce/ajax-images.php',
				data: 'create-perso=' + imageID + '&width=' + width + '&height=' + height,
				type: 'post',
				dataType: 'json',
				async: false,
				success: function(data){
					if( data.result == '1' ){
						var urlImage = window.parent.ui_config.img_url+'/customized/' + width + 'x' + height + '-' + imageID + '.jpg';
						
						$('#src').val( urlImage );
						$('#width').val( width );
						$('#height').val( height );
						
						ImageDialog.showPreviewImage( urlImage, 1 );
					}
				},
				complete: function(){ currentAjaxRequest = false; }
			});
		}

		return true;
	},
	
	selectDirectory : function( type ){
		// ré-initialise les variables et l'interface
		haveResults = true;
		$('#select-img').html( '' );
		$('#q-img').val('');
		
		// active le dossier
		$('#source .selected').removeClass('selected');
		$('#source .' + type).addClass('selected');

		// sauvegarde le dossier choisi dans une variable
		directory = type!='all' ? type : false;
		$('#select-img a').remove();
		
		page = 1;
		
		// recherche des images
		this.showImageLibrary(1);
		return false;
	},
	
	searchImages : function(){
		haveResults = true;
		$('#select-img').html('');
		
		this.showImageLibrary(1);
		return false;
	},
	
	selectImage : function( imgID ){
		imageID = imgID;

		currentAjaxRequest = $.ajax({
			url: '/admin/ajax/tinymce/ajax-images.php?selectImage=' + imgID,
			type: 'get',
			dataType: 'json',
			async: false,
			success: function(data){
				ImageDialog.myOnScroll( false );
				$('#src').val( data.url );
				if( data.name!=null )
					$('#title').val( data.name );
				
				$('#dimensions option').remove('selected');
				$('#source, #media-images, #search-img').hide();
				$('#dimensions #150x150').attr('selected', 'selected');
				$('#constrain, #constrainlabel').hide();
				
				$('#persosize').slideUp();
		
				$('#width, #height').val( 150 );
				$('#height').val( 150 );

				if( $.trim(data.width)=='' ){
					$('#dimensions option[value=customized]').remove();
				}else{
					if( !$('#dimensions option[value=customized]').length ){
						$('#dimensions').append('<option value="perso">Personnalisées</option>');
					}

					$('#persowidth').val( data.width );
					$('#persoheight').val( data.height );
				}
				
				ImageDialog.showPreviewImage( data.url, 1 );
				$('#edit-img').show();
			},
			complete: function(){ currentAjaxRequest = false; }
		});
		
		return false;
	},
	
	cancelSelectImage : function(){
		$('#source, #search-img, #media-images').show();
		$('#edit-img').hide();
		
		ImageDialog.myOnScroll( true );
		$(window).scrollTop(scrollNow);
		return false;
	}
	
};

ImageDialog.preInit();
tinyMCEPopup.onInit.add(ImageDialog.init, ImageDialog);
