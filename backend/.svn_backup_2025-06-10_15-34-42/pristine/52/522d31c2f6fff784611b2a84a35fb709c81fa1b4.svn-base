/**
 * Général
 */

* {
    margin: 0px;
    padding: 0px;
}
@include media('<medium'){
	html,body{
		-webkit-overflow-scrolling : touch !important;
		overflow: auto !important;
		height: 100% !important;
	}
}

body {
	@include media('>=large'){
		display: flex;
		flex-direction: column;
	}
    min-height: 100vh;
    font-family: 'Montserrat', sans-serif;
    font-size: 12px;
	color: $black;
    font-weight: 500;
    min-height: 100%;
	height: auto;
}

html {
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	font-smoothing: antialiased;
	font-weight: 400;
}

a{
	cursor:pointer;
	color: $medium-dark-color;
	text-decoration: none;
	&:hover {
		text-decoration: underline;
	}
}
a[target="_blank"] {
  position: relative;
  margin-right: 15px;
  &:after {
    content:"";
    background-image: url('/admin/images/imports/lien-externe.svg');
    background-repeat: no-repeat;
    background-size: contain;
    position: absolute;
    width: 10px;
    height: 10px;
    right: -14px;
    top: calc(50% - 5px);
	}

	&.button {
		padding-right: 35px;
		&:after {
			right: 20px;
		}
	}
}

#site-accessibility-menu {
	display: none !important;
}

#site-content-w {
	flex: 1 1 auto;
	overflow: hidden;
    display: flex;
}

/**
 * Firefox specific rule
 */

@-moz-document url-prefix() {
	body {
		font-weight: lighter !important;
	}
}

* {
	box-sizing: border-box;
}


/* Surcharge Police sur Google Maps */
.gm-style {
  font-family: 'Montserrat', sans-serif !important;
}

/* image de synchro */
img.sync {
    width: 16px;
    height: 16px;
    border: 0 !important;
    vertical-align: middle;
    margin-right: 2px;
}
h2 {
    img.sync {
      border : 0 !important;
      width: 28px;
      height: 28px;
    }
}

/* = Section En tête = */
.ria-admin-ui-header {
	display: flex;
	align-items: center;
	background-color: $dark-color;
	color: $white;
	padding: 18px 8px;
	@include media('>=medium') {
		justify-content: space-between;
		padding: 18px 14px;
	}
}
/* == Colonne Bouton Menu == */
.ria-admin-ui-toggle-menu {
	@include media('>=large') {
		display: none;
	}
	button {
		&:hover {
			background-color: transparent
		}
		&::before, &::after {
			display: none;
		}
		margin: 0;
		display: inline-block;
		border: 0 none;
		padding: 0;
		width: 32px;
		height: 32px;
		line-height: 32px;
		text-align: center;
		color: $white;
		cursor: pointer;
		outline: none;
		background-color: transparent;
		border-radius: 5px;
		img, svg {
			width: 32px;
			height: 32px;
		}
		&:focus {
			box-shadow: none;
		}
	}
}
/* == Colonne Logo == */
.ria-admin-ui-logo-moto {
	padding-left: 8px;
	@include media('>=medium') {
		padding-left: 15px;
		padding-right: 110px;
	}
}
/* === Widget Logo === */
.ria-admin-ui-logo {
	width: 92px;
	img {
		height: 23px;
	}
}
.ria-admin-ui-moto {
	font-size: 12px;
	font-weight: 400;
	a, a:visited {
		color: $ghostwhite;
		text-decoration: none;
	}
}
/* == Colonne Barre de recherche == */
.ria-admin-ui-searchbar {
	flex: 1 1 auto;
	.ria-admin-ui-header & {
		display: none;
		@include media('>=medium') {
			display: block;
		}
	}

	.mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) {
		height: 30px;
		background-color: $white;
		border-radius: 20px;
		.mdc-text-field__input {
			padding-left: 24px;
			width: 100%;
			height: 100%;
			border: 0 none;
			max-width: none;
			font-size: 14px;
			&:hover {
				border: 0 none;
			}
			// Pour retirer le jaune forcé par Chrome en autocomplete
			&:-webkit-autofill {
				-webkit-box-shadow: 0 0 0px 1000px $white inset;
			}
		}
		.mdc-text-field__icon {
			background-color: transparent;
			border: 0 none;
			max-width: none;
			bottom: 3px;
		}
	}
	.mdc-text-field--border {
		border: 1px solid rgba(0, 0, 0, 0.12);

		&.dark_border {
			border-color: $grey-medium-color;
		}
	}

	.mdc-text-field__icon {
		padding: 0;
		margin: 0;
	}
	.mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input {
		border-bottom: 0 none;
	}
	.mdc-text-field:not(.mdc-text-field--disabled):not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mdc-text-field__input:hover {
		border-bottom: 0 none;
	}
}

/* == Colonne actions Mes options, Déconnexion == */
.ria-admin-ui-headactions {
	display: none;
	@include media('>=large') {
		display: flex;
	}
	.mdc-button {
		height: 30px;
		font-size: 12px;
		font-weight: 400;
		padding: 0 20px;
		font-family: 'Montserrat', sans-serif;
		letter-spacing: unset;
		position: relative;
		&.ria-button--active {
			// cursor: default;
			color: $white;
			&::before, &::after {
				position: absolute;
				width: auto;
				height: 100%;
				top: 0;
				right: 10px;
				left: 10px;
				bottom: 0;
				background-color: $medium-color;
				opacity: 1;
				border-radius: 5px;
				z-index: -1;
			}
			&:hover{
				text-decoration: none;
			}
		}
	}
}

/* Fil d'ariane, breadcrumbs emplacement en cours */
#site-location {
	font-size: 11px;
	margin-bottom: 15px;
	list-style-type: none !important;
}

/* Cache le h1 Riashop, et le timer */
h1, .page_timer {
	display: none !important;
}


.mdc-button:not(:disabled) {
	&.ria-button--light {
		color: $white;
	}
	&.ria-button--outline-light {
		color: $white;
		&:hover {
			background-color: $white;
			color: $dark-color;
			text-decoration: none;
		}
	}
	&.ria-button--outline-dark {
		&:hover {
			background-color: $dark-color;
			color: $white;
			text-decoration: none;
		}
	}
	&.ria-button--tti {
		text-transform: none;
	}
}

.ria-button--outline-dark {
	border: 1px solid $dark-color;

}
.ria-button--outline-light {
	border: 1px solid $white;
}
.ria-button--bordered {
	border: 1px solid $white;
}

/* Choix de la société / locataire */
.accesstenant {
	text-align: center;
	padding: 10px 0 !important;
	margin: 0;
	margin-bottom: 0 !important;
    strong {
        font-size: 1.2em;
    }
}

/* Zone de contenu */
#site-content {
	margin-right: 10px;
	padding-top: 10px;
	margin-bottom: 20px;
	padding-bottom: 10px;
	min-width: 0;
	min-height:  500px;
	flex: 1 1 auto;
	@include media('<medium') {
		margin-left: 10px !important;
		height: auto !important;
	}
	@include media('>=medium', '<large') {
		margin-left: 20px !important; // pour corriger bug lorsque le sous-menu est fermé et qu'on réduit l'écran
		margin-right: 20px;
		padding-top: 20px;
		padding-bottom: 20px;
	}
	@include media('>=large') {
		margin-left: 200px;
		padding-left: 30px;
		margin-right: 30px;
		padding-top: 30px;
		padding-bottom: 30px;
		min-width: 726px;
		min-height: auto;
		border-left: 1px solid $grey-medium-color;
	}
	.mceToolbar .toolBarGroup, .defaultSkin .mceIframeContainer {
		box-shadow: none;
	}
}

/* Copyright */
#site-footer {
	display: none;
}
#site-footer {
	@include media('<large') {
		display: none;
	}
	display: block;
	font-size: 10px;
	padding: 2px;
	background-color: $light-color;
	color: $dark-color;
	bottom: 0px;
	left: 0px;
	right: 0px;
	border-top: 1px solid $grey-medium-color;
	z-index: 2;
}

.ria-admin-ui-filters {
	display: flex;
	flex-wrap: wrap;
}
.ria-admin-ui-overview {
	margin: 0;
	clear: both;
}


.ria-admin-ui-actions {
	margin-top: 10px;
}

img {
	border-style: none;
	vertical-align: text-top;
}

/* images flèches */
.fleche {
    width: 16px;
    height: 8px;
}

.fleche-stats {
    width: 16px;
    height: 8px;
}

.fleche-move {
    width: 16px;
    height: 16px;
    border-style: medium none !important;
}

/* Lecture seule */
.readonly, input:read-only {
    color: $grey-medium-color;
}

/* Boutons de suppression */
img.icon-del-cat {
    border : 0 !important;
}

a.del-link, .del-link a, #pmt-special input.cdt-grp-del:not(.button-del-group), input.input-icon-del {
    background-image: url("/admin/images/cut_inactive.svg");
    background-repeat: no-repeat;
    background-color: transparent !important;
    color: transparent !important;
    display: block;
    height: 23px;
    width: 23px;
    padding: 0;
	border: 0 !important;
	cursor: pointer;
    &:hover {
      background-image: url("/admin/images/cut_rollover.svg");
	  height: 23px;
	  width: 23px;
    }
}

/* Sélecteurs */

.riapicker .selectorview {
	position: relative;
	.left {
		width: 250px;
  		white-space: nowrap;
  		overflow: hidden;
 		text-overflow: ellipsis !important;
	}
    a.btn {
		position: absolute;
		right: 5px;
		img {
			width: 16px;
			height: 8px;
		}
	}
}

/* Zone de contenu */
h2 {
	margin-bottom: 10px;
	font-size: 22px;
	padding-bottom: 4px;
	color: $dark-color;
	word-wrap: break-word;
}

/**
 * Règles CSS générales pour la version imprimable
 */
 @media screen {
	 // Visible seulement en print
	.print-only{
		display: none !important;
	}
 }

 @media print {
	@page {
		margin: 15mm 5mm;
	}
	html, body {
		// width: 210mm;
		// height: 297mm;
		background: $white;
		overflow: visible !important;
	}
	.print-none {
		display: none !important;
	}

	// garde la forme initiale d'un tableau et n'applique pas le responsive
	.print-table {
		display: table !important;
		thead {
			display: table-header-group !important;
		}
		tbody {
			display: table-row-group !important;
		}
		tfoot {
			display: table-footer-group !important;
		}
		tr {
			display: table-row !important;
		}
		td, th, th.thead-none {
			display: table-cell !important;

			&.print-none {
				display: none !important;
			}
		}
		td, th{
			&[data-label]::before {
				content: '' !important;
			}
		}

	}

	body {
		font-size: 11px;
	}
	#site-content #tabpanel {
		padding: 0;
		border-style: none;
	}
	#site-content {
		margin: 0; padding: 0;
	}
	#site-content table thead th {
		border-bottom: 1px solid $black;
	}
	#site-content table tbody th {
		border-bottom: 1px solid $grey-medium-color;
	}
	a {
		color: $black;
		text-decoration: none;
	}

	// suppression des boutons d'action
	[type="button"], [type="submit"], .button {
		display: none !important;
	}

	// suppression du border
	select, [type="text"], textarea {
		border: none !important;
		vertical-align: middle !important;
	}

	input, select, textarea, .input-edit-success {
		&, &:focus {
			box-shadow : none !important;
			border: none;
		}
	}

	// Pour que l'icone de redimentionnement ne s'affiche pas
	textarea{
		resize: none !important;
		height: auto;
	}

}