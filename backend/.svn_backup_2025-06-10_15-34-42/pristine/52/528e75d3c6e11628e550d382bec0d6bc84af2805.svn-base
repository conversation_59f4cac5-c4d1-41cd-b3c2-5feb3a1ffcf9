<?php
/**
 * \defgroup api-promotions-specials Promotions Spéciales
 * \ingroup cpq
 * @{
 *
 * \page api-promotions-specials-add Ajout/Mise à jour 
 *
 * Cette fonction permet l'ajout ou la mise à jour d'un code promotion de type Produit offert 
 *
 *		\code
 *			POST /promotions/specials/
 *		 ou
 *			PUT /promotions/specials/
 *		\endcode
 *
 * @param raw_data Obligatoire, Donnée en json_decode
 *		\code{.json}
 *			{
 *					"type" : (obligatoire) Type de la promotion ( pour le moment seul le type = 3 est accepté produit offerts 
 *					"label" : (obligatoire) Nom de la promotion
 *					"cod_id" : (obligatoire en mise à jour) Identifiant de la promotion
 *					"offers" : (obligatoire) Tableau des produits a inclure dans la promo sous la forme : 
 *								[{
 *									"all_customers" : (facultatif) Si true permet de donner la promotion à tous les clients
 *									"date_start" : (facultatif) Date de début de la promo
 *									"date_end" : (facultatif) Date de fin de la promo
 *									"list_prd_offers" : [{
 *											prd_id : {
 												qty : qty,
 												price_ht : price_ht
 											}
 *										}]
 *								}]
 *					"list_prd_included" : (obligatoire) Tableau des produits a inclure dans la promo sous la forme : 
 *								[{
 *									"id" : (obligatoire) Identifiant du produit
 *								}]
 *					"list_usr_included" : (obligatoire) Tableau des clients a inclure dans la promo sous la forme : 
 *								[{
 *									"id" : (obligatoire) Identifiant du client
 *								}]
 *					"conditions" : (obligatoire) Tableau des clients a inclure dans la promo sous la forme : 
 *								[{
 *									"cdt" : (obligatoire) identifiant d'une condition
 *									"psy" : (obligatoire) symbole utilisé ( =, != )
 *									"grp" : (obligatoire) identifiant d'un groupe de condition
 *									"val" : (obligatoire) Valeur acceptée pour cette condition
 *									"apply_on" : (obligatoire) sur quel partie de la commande la conditions est appliquée (toutes la commande : order, respect des règles d'inclu/exclu : rules)
 *									"same_qty" : (obligatoire) par défaut à False, mettre True pour avoir la même quantité sur chaque ligne
 *								}]
 *			}
 *		\endcode
 * 
 * @return json, une liste avec les éléments traité sous la forme suivante :
 *		\code{.json}
 *			{
 *					"type" : Type de la promotion
 *					"label" : Nom de la promotion
 *					"cod_id" : Identifiant de la promotion créé ou mise à jour
 *					"all_customers" : Si true permet de donner la promotion à tous les clients
 *					"date_start" : Date de début de la promo
 *					"date_end" : Date de fin de la promo
 *					"list_prd_included" : Tableau des produits inclus dans la promo sous la forme : 
 *								[{
 *									"id" : Identifiant du produit
 *								}]
 *					"list_usr_included" : Tableau des clients inclus dans la promo sous la forme : 
 *								[{
 *									"id" : Identifiant du client
 *								}]
 *			}
 *		\endcode
 * @}
*/

switch( $method ){

	case 'upd': 
	case 'add': 
		if( !is_array($objs) ){
			throw new Exception("Paramètres invalide");
		}

		$content = array();
		foreach($objs as $obj){

			$gescom_ref = isset($obj['gescom_ref']) ? $obj['gescom_ref'] : "";
			$type = isset($obj['type']) && is_numeric($obj['type']) ? $obj['type'] : 0;
			$cod_id = isset($obj['cod_id']) && is_numeric($obj['cod_id']) ? $obj['cod_id'] : 0;
			$label = isset($obj['label']) ? $obj['label'] : "";
			$list_prd_included = isset($obj['list_prd_included']) && is_array($obj['list_prd_included']) ? $obj['list_prd_included'] : array();
			$list_usr_included = isset($obj['list_usr_included']) && is_array($obj['list_usr_included']) ? $obj['list_usr_included'] : array();
			$offers = isset($obj['offers']) && is_array($obj['offers']) ? $obj['offers'] : array();
			$conditions = isset($obj['conditions']) && is_array($obj['conditions']) ? $obj['conditions'] : array();


			if( !in_array($type, array(_PMT_TYPE_PRD)) ){
				throw new Exception("Le type de promotion est incorrecte.");				
			}

			$data_for_response = array(
					"gescom_ref" => $gescom_ref,
					"type" => $type,
					"label" => $label,
					"cod_id" => $cod_id,
					"offers" => $offers,
					"list_prd_included" => $list_prd_included,
					"list_usr_included" => $list_usr_included,
					"conditions" => $conditions,
				);


			foreach($offers as $key => $off ){
				$offers[$key]['date_start'] = isset($off['date_start']) && isdateheure($off['date_start']) ? dateheureparse($off['date_start']) : null;
				$offers[$key]['date_end'] = isset($off['date_end']) && isdateheure($off['date_end']) ? dateheureparse($off['date_end']) : null;
				$offers[$key]['all_catalog'] = isset($off['all_catalog']) && is_numeric($off['all_catalog']) ? $off['all_catalog']==1 : false;
				$offers[$key]['all_customers'] = isset($off['all_customers']) && is_numeric($off['all_customers']) ? $off['all_customers']==1 : false;
				$offers[$key]['list_prd_offers'] = isset($off['list_prd_offers']) && is_array($off['list_prd_offers']) ? $off['list_prd_offers'] : array();
			}

			foreach($list_prd_included as $prd ){
				if( !isset($prd['id']) || !is_numeric($prd['id']) ){
					throw new Exception('Les paramètres d\'inclusion du produit ne sont pas valide.'.print_r($data_for_response, true));
				}
			}

			foreach($list_usr_included as $usr ){
				if( !isset($usr['id']) || !is_numeric($usr['id']) ){
					throw new Exception('Les paramètres d\'inclusion du client ne sont pas valide.'.print_r($data_for_response, true));
				}
			}

			foreach($conditions as $cnd ){
				if( !isset($cnd['cdt'])
				 || !isset($cnd['psy'])
				 || !isset($cnd['grp'])
				 || !isset($cnd['val'])
				 || !isset($cnd['apply_on'])
				 || !isset($cnd['same_qty']) ){
					throw new Exception('Les paramètres des conditions ne sont pas valide.'.print_r($data_for_response, true));
				}
			}

			// controles des datas en fonction du types de la promo
			switch ($type) {
				case _PMT_TYPE_PRD:

					if( !$label ){
						throw new Exception("Le label ne peut être vide dans le cas de cette promotion.");				
					}

					foreach($obj['offers'] as $offer ){
						if( !sizeof($offer['list_prd_offers']) ){
							throw new Exception('Dans le cadre d\'une promotion de type produit offert, la liste des produits est obligatoire');							
						}
						foreach($offer['list_prd_offers'] as $prd_id => $data ){
							if( !prd_products_exists($prd_id) ){
								throw new Exception('Le produit '.$prd_id.' n\'existe pas.'.print_r($data_for_response, true));
							}
							if( !is_array($data) || !is_numeric($data['qty']) ){
								throw new Exception('La quantité '.$data['qty'].' du produit n\'est pas valide.'.print_r($data_for_response, true));
							}
						}
					}
					break;
			}


			// cas de la mise à jours
			if( $cod_id > 0 ){
				if( !pmt_codes_exists($cod_id) ){
					throw new Exception("La promotion cod_id=".$cod_id.' n\'existe pas : '.print_r($data_for_response, true));					
				}

				// suppression des offres précédentes 
				if( !pmt_offers_del($cod_id) ){
					throw new Exception("Les détails précédents de la promotion promotion cod_id=".$cod_id.' n\'ont pas été supprimé : '.print_r($data_for_response, true));	
				}

				// suppression des inclusions de produits
				if( !pmt_products_del_by_id($cod_id) ){
					throw new Exception("Les inclusions produits précédentes de la promotion promotion cod_id=".$cod_id.' n\'ont pas été supprimé : '.print_r($data_for_response, true));	
				}
				// suppression des inclusions de produits
				if( !pmt_code_conditions_del($cod_id) ){
					throw new Exception("Les conditions précédentes de la promotion promotion cod_id=".$cod_id.' n\'ont pas été supprimé : '.print_r($data_for_response, true));	
				}
				// suppression des inclusions de clients
				$rusr = pmt_users_get( $cod_id );
				if( $rusr && ria_mysql_num_rows($rusr) ){
					while( $usr = ria_mysql_fetch_array($rusr) ){
						if( !pmt_users_del($cod_id, $usr['id']) ){
							throw new Exception("Les inclusions clients précédentes de la promotion promotion cod_id=".$cod_id.' n\'ont pas été supprimé : '.print_r($data_for_response, true));
						}
					}
				}

			}
			// cas de la création 
			else{
				$cod_id = pmt_codes_add( $label, $type );

				if( !$cod_id ){
					throw new Exception('La promotion n\'a pas pu être ajouté : '.print_r($data_for_response, true));		
				}

				$data_for_response['cod_id'] = $cod_id;
			}

			// ajout des offres
			switch ($type) {
				case _PMT_TYPE_PRD:

					foreach( $offers as $off ){

						if( !pmt_offers_add( $cod_id, $type, 0, 0, 1, 0, 0, 0, null, $off['date_start'], $off['date_end'], 0, true, false, true, false, false, $off['list_prd_offers'], false, false, false, _TVA_RATE_DEFAULT ) ){
								throw new Exception('Le détail de la promotion n\'a pas pu être ajouté : '.print_r($data_for_response, true));
						}
					}

					break;
			}

			// ajout des inclusions de produits
			if( sizeof($list_prd_included) ){
				if( $off['all_catalog'] ){
					pmt_codes_set_all_catalog($cod_id, true);
				}else{
					pmt_codes_set_all_catalog($cod_id, false);
				}
				foreach($list_prd_included as $prd ){
					if( !pmt_products_add( $cod_id, $prd['id'], true, 0, null, null ) ){
						throw new Exception('L\'inclusion du produit '.$prd['id'].' n\'a pas pu être faite.'.print_r($data_for_response, true));
					}
				}
			}

			// ajout des inclusions de clients
			foreach( $offers as $off ){
				if( $off['all_customers'] ){
					pmt_codes_set_all_customers($cod_id, true);
				}else{
					pmt_codes_set_all_customers($cod_id, false);

					foreach($list_usr_included as $usr ){
						if( !pmt_users_add( $cod_id, $usr['id'], true ) ){
							throw new Exception('L\'inclusion du client '.$usr['id'].' n\'a pas pu être faite.'.print_r($data_for_response, true));
						}
					}
				}
				break;
			}

			// ajout des conditions 
			if( sizeof($conditions) ){
				// supression est création d'un cgroupe de cnd a voir dans le temps pour que l'api accepte ca dans les paramètres
				pmt_code_groups_del_all($cod_id);
				$grp = pmt_code_groups_add($cod_id); 

				foreach($conditions as $cnd ){
					if( !pmt_code_conditions_add( $cod_id, $cnd['cdt'], $cnd['psy'], $grp, $cnd['val'], $cnd['apply_on'], $cnd['same_qty']) ){
						throw new Exception('La condition '.print_r($cnd, true).' n\'a pas pu être créé.'.print_r($data_for_response, true));
					}
				}
			}

			// mise à jour pour l'affectation des sites 
			pmt_websites_del($cod_id);
			pmt_websites_add($cod_id, wst_websites_get_array());


			$content[] = $data_for_response;

		}
		$result = true;
		break;
	case 'del': 
		if( !isset($_REQUEST['id']) || !is_numeric($_REQUEST['id']) ){
			throw new Exception("Les identifiants fournis en arguments sont incorrects");
		}
		pmt_codes_del($_REQUEST['id']); 
		$result = true;
		break;
}
