// Gestion du sélectionneur de période
$(document).ready(function(){
	$('#closingpicker .selectorview').click(function(){
		if($('#closingpicker .selector').css('display')=='none'){
			$('#closingpicker .selector').show();
		}else{
			$('#closingpicker .selector').hide();
		}
	});
	$("#closingpicker .selector a").click(function(){
		const periode = $(this).attr('name');
		const p = periode.substring(periode.indexOf('-')+1, periode.length);
		window.location.href = 'index.php?period='+p+'#form-closing';
	});
	$('#expeditionspicker .selectorview').click(function(){
		if($('#expeditionspicker .selector').css('display')=='none'){
			$('#expeditionspicker .selector').show();
		}else{
			$('#expeditionspicker .selector').hide();
		}
	});
	$("#expeditionspicker .selector a").click(function(){
		const periode = $(this).attr('name');
		const p = periode.substring(periode.indexOf('-')+1, periode.length);
		window.location.href = 'index.php?website='+p;
	});

	if( typeof $("input[name=exp-website]") != 'undefined' ){
		$("input[name=exp-website]").change(function(){
			if( $("#exp-website-1").attr('checked') ){
				$("#site-content div.exp-menu").show();
			}else{
				$("#site-content div.exp-menu").hide();
			}
		});
	}
});

var lastDay = '';
var lastHr = -1;
var check = false;
var action = false;

/**
	Cette fonction permet de gérer la sélection des heures pendant lesquelles les expéditions sont effectuées.
*/
function selectedHourly(day, hourly){
	
	// Gestoin d'une heure en particulier
	if( $("#hr-"+day+"-"+hourly).parent().attr('class')=='hr-active' ){
		$("#hr-"+day+"-"+hourly).parent().attr('class', 'hr-inactive');
		$("#hr-"+day+"-"+hourly).removeAttr('checked');
		check = false;
	} else {
		$("#hr-"+day+"-"+hourly).parent().attr('class', 'hr-active');
		$("#hr-"+day+"-"+hourly).attr('checked', 'checked');
		check = true;
	}
	
	// Gestion d'une période d'horaires
	if( lastDay==day && lastHr>=0 ){
		if( check && $("#hr-"+day+"-"+lastHr).parent().attr('class')=='hr-active' ){
			action = true;
		}else if( !check && $("#hr-"+day+"-"+lastHr).parent().attr('class')=='hr-inactive' ){
			action = true;
		}
		
		if(	action ){
			if( lastHr>hourly ){
				// Si la première heure est supérieur à la seconde, on sélection en arrière
				for( var i=lastHr ; i>=hourly ; i-- ){
					check ? $("#hr-"+day+"-"+i).parent().attr('class', 'hr-active') : $("#hr-"+day+"-"+i).parent().attr('class', 'hr-inactive');
					check ? $("#hr-"+day+"-"+i).attr('checked', 'checked') : $("#hr-"+day+"-"+i).removeAttr('checked');
				}
			} else {
				// Si la première heure est inférieure à la seconde, on sélection en avant
				for( var i=lastHr ; i<=hourly ; i++ ){
					check ? $("#hr-"+day+"-"+i).parent().attr('class', 'hr-active') : $("#hr-"+day+"-"+i).parent().attr('class', 'hr-inactive');
					check ? $("#hr-"+day+"-"+i).attr('checked', 'checked') : $("#hr-"+day+"-"+i).removeAttr('checked');
				}
			}
		}
		
		// Réinitialise les variables d'historique
		lastDay = '';
		lastHr = -1;
		check = action = false;
	} else {
		
		// Initialise les variables d'historique
		lastDay = day;
		lastHr = hourly;
		
	}
	
	// Gestion du libellé des périodes
	var period = '';
	var start = 0,
		end = 0,
		count = 0;
	var first = true;

	for( var i=0; i<25 ; i++ ){
		if( $("#hr-"+day+'-'+i).parent().attr('class')=='hr-active' ){
			if( first ){
				start = i;
				first = false;
			}
			end = i;
		} else {
			if( $("#hr-"+day+'-'+(i-1)).parent().attr('class')=='hr-active' && ( $("#hr-"+day+'-'+i).parent().attr('class')=='hr-inactive' || i==24 ) ){
				count++;
				// Mise en forme de l'heure
				start = start<10 ? '0'+start+':00' : start+':00';
				end = (end+1)<10 ? '0'+(end+1)+':00' : (end+1)+':00';
				
				// Création du libellé des périodes
				if( start!=end )
					period += start+' à '+end+' ';
				else{
					end = (start+1)<10 ? '0'+(start+1)+':00' : (start+1)+':00';
					period += start+' à '+end+' ';
				}
				if( count==2 ){
					period += '<br />';
					count = 0;
				}
			}
			
			first = true;
		}
	}
	
	// Affiche le libellé des périodes
	period = period!='' ? period : expeditionsAucuneExp;
	period = start=='00:00' && end=='24:00' ? expeditionsTouteJournee : period;
	$("#period-"+day).html(period);
}

/**
	Cette fonction permet de réinitialisé les heures à 0 pendant lesquelles les expéditions ont lieu.
*/
function reset(day){
	// Réinitialise la colonne période
	$("#period-"+day).html(expeditionsAucuneExp);
	for( var i=0 ; i<24 ; i++ ){
		$("#hr-"+day+"-"+i).parent().attr('class', 'hr-inactive');
		$("#hr-"+day+"-"+i).removeAttr('checked');
	}
	
	// Réinitialise les variables d'historique
	lastDay = '';
	lastHr = -1;
	lastcheck = false;
}

/**
	Cette fonction permet de recharger le tableau des jours fériés.
*/
function holidays(year){
	if( year<=0 ) return false;
	
	// Requête AJAX pour affiché le tableau selon l'année
	$.ajax({
		type: "POST",
		url: '/admin/config/expeditions/ajax-holidays.php',
		data: 'year='+year,
		dataType: 'xml',
		success: function(xml) {
			// Affiche le nouvel entête de tableau
			$("#th-year").html( $(xml).find('head-year').text() );
			
			// Affiche le nouveau corps du tableau
			$("#tb-holidays tbody").html( $(xml).find('body-year').text() );
		}
	});
}

/**
	Cette fonction permet de sauvegarder les jours fériés pour savoir si oui ou non les expéditions auront lieu.
*/
function holidays_save(){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();
	
	// Requête AJAX pour la sauvegarde des jours fériés avec ou sans expéditions
	$.ajax({
		type: 'POST',      
		url: '/admin/config/expeditions/ajax-holidays.php',
		data: 'saveHolidays=1&'+$("#form-holidays").serialize(),     
		dataType: 'xml',
		success: function(xml) {
			// Affichge du message d'erreur ou de succès
			if( $(xml).find('result').attr('type')==0 )
				$("#tb-holidays").before( '<div class="error">'+$(xml).find('error').text()+'</div>' );
			else
				$("#tb-holidays").before( '<div class="error-success">'+$(xml).find('success').text()+'</div>' );
		}
	});
}

/**
	Cette fonction permet de charger ou de recharger le sélectionneur de date pour les champs de type date
*/
function loadDatePicker(){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();
	
	// Parcours tous les champs de type date
	$('input.datepicker').each(function(){
		var temp = this ;
		
		// Implémente le sélecteur de date sur chacun d'entre eux.
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				var date = $(temp).val();
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					if( formated!=date )
						$(temp).DatePickerHide();
				}
			}
		});
		
	});	
}

/**
	Cette fonction permet de mettre en place le formulaire d'ajout d'une fermeture exceptionnelle
*/
function closing_add(){
	if( $("#new-closing").length==1 ){
		
		closing_del(0);
	}
		
	$("#none-closing").hide();
	var html = '';
	
	// Création du formulaire d'ajout
	html += '<tr id="new-closing"><td headers="clg-name" class="td-info"><input type="text" name="name-period-0" id="name-period-0" value="" /></td>';
	html += '<td headers="clg-start" class="td-date"><input class="datepicker" type="text" name="start-period-0" id="start-period-0" value="" /></td>';
	html += '<td headers="clg-end" class="td-date"><input class="datepicker" type="text" name="end-period-0" id="end-period-0" value="" /></td>';
	html += '<td headers="clg-action" class="td-action"><input class="action" type="button" name="save-period" id="save-period-0" value="' + expeditionsEnregistrer + '" onclick="lavascript:closing_save(0);" />';
	html += '<div id="save-load-0" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>';
	html += '<br /><a class="del button" onclick="javascript:closing_del(0);">' + expeditionsAnnuler + '</a></td></tr>';
	
	// Affiche ce formulaire et initialise les champs date
	$("#tb-closing tbody").append(html);
	loadDatePicker();
}

/**
	Cette fonction permet de faire apparaître le formulaire de modification des informations sur une fermeture exceptionnelle
*/
function closing_edit(id, name, start, end){

	// Création HTML du formulaire d'édition d'une fermeture exceptionnelle
	var html = '<td headers="clg-name" class="td-info"><input type="text" name="name-period-'+id+'" id="name-period-'+id+'" value="'+name+'" /></td>';
	html += '<td headers="clg-start" class="td-date"><input class="datepicker" type="text" name="start-period-'+id+'" id="start-period-'+id+'" value="'+start+'" /></td>';
	html += '<td headers="clg-end" class="td-date"><input class="datepicker" type="text" name="end-period-'+id+'" id="end-period-'+id+'" value="'+end+'" /></td>';
	html += '<td headers="clg-action" class="td-action"><input class="action" type="button" name="save-period" id="save-period-'+id+'" value="' + expeditionsEnregistrer + '" title="' + expeditionsEnregistrerModif + '" onclick="lavascript:closing_save('+id+');" />';
	html += '<div id="save-load-'+id+'" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>';
	html += '<br /><a class="del button" title="' + expeditionsAnnulerModif + '" onclick="javascript:closing_edit_cancel('+id+', \''+name+'\', \''+start+'\', \''+end+'\');">' + expeditionsAnnuler + '</a></td>';
	
	// Affiche le formulaire et initalise les champs date
	$("#closing-"+id).html(html);
	loadDatePicker();
}

/**
	Cette fonction permet de sauvegarder une fermeture exceptionnelle
*/
function closing_save(id){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages(); 

	// Affiche l'image pour montrer que l'action a bien lieu
	$("#save-period-"+id).hide(); $("#save-period-"+id).parent().find('a.del').hide(); $("#save-load-"+id).show();
	var idElem = id;
	// Requête AJAX pour la sauvegarde
	$.ajax({
		type: 'POST',
		url: '/admin/config/expeditions/ajax-closing.php',
		data: 'addclosing=1&closing='+id+'&'+$("#form-closing").serialize(),
		dataType: 'xml',
		success: function(xml) {
			if( $(xml).find('result').attr('type')==0 ){
				$("#tb-closing").before( '<div class="error">'+$(xml).find('error').text()+'</div>' );
				
				$('#save-load-' + idElem).parents('tr').find('input, a').show();
				$('#save-load-' + idElem).hide();
			} else {
				// Affiche le message de succès
				$("#tb-closing").before( '<div class="error-success">'+$(xml).find('success').text()+'</div>' );
				
				// Récupère les informations sur la fermeture
				var id = $(xml).find('result').attr('id');
				var name = $(xml).find('result').attr('name');
				var start = $(xml).find('result').attr('start');
				var end = $(xml).find('result').attr('end');
				
				// Affiche ces informations
				if( $(xml).find('result').attr('add')==1 ){ // il s'agit d'une nouvelle fermeture
					var html = '<tr id="closing-'+id+'">';
					html += '<td headers="clg-name" class="td-info">'+name+'</td>';
					html += '<td headers="clg-start" class="td-date">'+start+'</td>';
					html += '<td headers="clg-end" class="td-date">'+end+'</td>';
					html += '<td headers="clg-action" class="td-action"><img class="edit-closing" src="/admin/images/expeditions/edit.png" alt="Editer" title="' + expeditionsEditerFermeture + '" onclick="javascript:closing_edit('+id+', \''+addslashes(name)+'\', \''+start+'\', \''+end+'\');" />';
					html += '<div id="save-load-'+id+'" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>';
					html += '<br /><a class="del button" title="' + expeditionsSupprimerFermeture + '" onclick="javascript:closing_del('+id+');">' + expeditionsSupprimer + '</a></td>';
					html += '</tr>';
					$("#tb-closing").append(html);
					$("#new-closing").remove();
				} else {
					// Il s'agit d'une mise à jour
					closing_edit_cancel(id, name, start, end);
				}
			}
		}
	});
}

/**
	Cette fonction permet d'annuler les modifications apportées lors d'une édition
*/
function closing_edit_cancel(id, name, start, end){
	// Constuit la ligne sans les input
	html = '<td headers="clg-name" class="td-info">'+name+'</td>';
	html += '<td headers="clg-start" class="td-date">'+start+'</td>';
	html += '<td headers="clg-end" class="td-date">'+end+'</td>';
	html += '<td headers="clg-action" class="td-action">';
	html += '<img class="edit-closing" src="/admin/images/expeditions/edit.png" alt="Editer" title="' + expeditionsEditerFermeture + '" onclick="javascript:closing_edit('+id+', \''+addslashes(name)+'\', \''+start+'\', \''+end+'\');" />';
	html += '<div id="save-load-'+id+'" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>';
	html += '<br /><a class="del button" title="' + expeditionsSupprimerFermeture + '" onclick="javascript:closing_del('+id+');">' + Supprimer + '</a></td>';
	
	// Affecte le nouveau HTML à la ligne du tableau
	$("#closing-"+id).html(html);
}

/**
	Cette fonction permet de supprimer une fermeture exceptionnelle
*/
function closing_del(id){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();
	
	if( id==0 ){
		// Suppression du formulaire d'ajout
		$("#new-closing").remove();
		
		// S'il n'y a plus de fermeture, on affiche le message "Aucune fermeture ... "
		if( $("#tb-closing tbody tr").length==1 )
			$("#none-closing").show();
	} else {
		
		// Affiche l'image pour montrer que l'action a bien lieu
		$("#closing-"+id).find('img.edit-closing').hide();
		$("#closing-"+id).find('a.del').hide();
		$("#save-load-"+id).show();
		
		// Demande confirmation avant la suppression d'une fermeture exceptionnelle
		if( window.confirm(expeditionsConfirmSuppressionFermeture) ){
			// Suppression d'une fermeture enregistrée dans la base
			$.ajax({
				type: 'POST',
				url: '/admin/config/expeditions/ajax-closing.php',
				data: 'delclosing=1&closing='+id,
				dataType: 'xml',
				success: function(xml) {
					if( $(xml).find('result').attr('type')==0 )
						$("#tb-closing").before( '<div class="error">'+$(xml).find('error').text()+'</div>' );
					else {
						$("#tb-closing").before( '<div class="error-success">'+$(xml).find('success').text()+'</div>' );
						$("#closing-"+id).remove();
						
						// S'il n'y a plus de fermeture, on affiche le message "Aucune fermeture ... "
						if( $("#tb-closing tbody tr").length==1 )
							$("#none-closing").show();
					}
				}
			});
		} else {
			// Réaffiche les éléments pour les actions
			$("#closing-"+id).find('img.edit-closing').show();
			$("#closing-"+id).find('a.del').show();
			$("#save-load-"+id).hide();
		}
	}
	
}
