<?php
/**
 * \ingroup PriceWatching
 */
/**
 * Fonction pour inclure automatiquement les class nécessaire pour les requêtes au web service d'Amazon
 * @param $className
 */
function __autoload($className)
{
    $filePath = 'comparators/' . str_replace('_', DIRECTORY_SEPARATOR, $className) . '.php';
    print 'file : '.$filePath.PHP_EOL;
    $includePaths = explode(PATH_SEPARATOR, get_include_path());
    foreach ($includePaths as $includePath) {
        if (file_exists($includePath . DIRECTORY_SEPARATOR . $filePath)) {
            require_once $filePath;
            return;
        }
    }
}
