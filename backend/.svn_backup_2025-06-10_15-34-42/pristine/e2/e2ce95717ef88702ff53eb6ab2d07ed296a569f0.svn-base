{"name": "sebastian/comparator", "description": "Provides the functionality to compare PHP values for equality", "keywords": ["comparator", "compare", "equality"], "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}}