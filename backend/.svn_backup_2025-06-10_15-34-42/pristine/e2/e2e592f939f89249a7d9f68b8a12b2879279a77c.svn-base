<?php

// \cond onlyria
/**	Cette fonction déclenche l'envoi d'une alerte email au propriétaire de la commande.
 *	\ingroup oms_ord_alerts
 *	Il est nécessaire d'appeller cette fonction lorsque le statut d'une commande est modifiée.
 *
 *	En plus de l'envoi, cette fonction va se charger de vérifier la configuration de l'utilisateur
 *	pour déterminer si l'email doit être envoyé ou non. En effet, l'utilisateur peut avoir choisi
 *	de ne pas recevoir la notification correspondant au statut actuel de sa commande.
 *
 *	@param int $order Identifiant de la commande
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *
 *	@todo Un grand nombre de status sont désormais notifiés par les pl et les bl. A nettoyer.
 *
 */
require_once("Barcode.inc.php");

function ord_orders_notify( $order ){
	global $config;

	// Spécifier true pour activer la trace dans le log.
	$logger = in_array($config['tnt_id'], array(4, 8, 17, 39, 40, 89));

	if( !is_numeric($order) || $order <= 0 ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$order.' argument order invalide'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		return false;
	}

	ord_orders_refresh($order);
	$rorder = ord_orders_get_with_adresses( 0, $order );
	if( !$rorder ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$order.' echec de ord_orders_get_with_adresses()'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		return false;
	}elseif( !ria_mysql_num_rows($rorder) ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$order.' aucune commande à charger'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		return false;
	}
	$r = ria_mysql_fetch_assoc($rorder);

	// Utilisé par Boissinot pour déterminer si des produit sont en rupture.
	$products_count = $products_in_stock = 0;

	// copie préalable de la configuration de base (elle doit être rechargée avant la sortie de la fonction)
	$config_copy = $config;

	if( !is_numeric($r['wst_id']) || $r['wst_id'] <= 0 ){
		// le "wst_id" sur la commande n'est pas correcte
		$r['wst_id'] = $config['wst_id'];
	}else{
		// chargement du website de la commande
		$wst_get = wst_websites_get( $r['wst_id'], false, $config['tnt_id'] );

		if( !$wst_get || !ria_mysql_num_rows($wst_get) ){

			// le "wst_id" n'appartient pas au tenant ou n'existe pas ou plus
			$r['wst_id'] = $config['wst_id'];

		}elseif( $r['wst_id'] != $config['wst_id'] ){

			// toutes les variables de configuration utilisées dans cette fonction
			$ar_to_reload = array(
				'email_alerts_enabled', 'days_order_notify', 'allow_orders_update_state',
				'notify_state_wait_pay_for_cb', 'notify-seller', 'notify-seller-state', 'site_name', 'email_html_header',
				'site_dir', 'email_html_header_order', 'site_have_user_space',
				'adr_pay_cheque', 'site_url', 'contact_page_url', 'email_html_footer', 'ord_notify_only_web',
				'show_barcode_in_order_notify', 'weight_col_calc_lines', 'assurance_ref', 'prd_ref_cbmulti',
				'show_fields_in_notify', 'show_price_in_weight_unit', 'show_port_in_order_confirmation',
				'show_notify_code_discount', 'show_original_port_name', 'site_have_user_histo', 'notify_ord_childs', 'active_email_perso',
			);

			// rechargement de ces variables avec le nouveau site
			if( $rconf_from_ord = cfg_overrides_get( $r['wst_id'], array(), $ar_to_reload ) ){
				while( $conf_from_ord = ria_mysql_fetch_assoc($rconf_from_ord) ){
					$config[ $conf_from_ord['code'] ] = $conf_from_ord['value'];
				}
			}
			$config['wst_id'] = $r['wst_id'];
		}
		if($wst_get && ria_mysql_num_rows($wst_get)) {
			$wst = ria_mysql_fetch_assoc($wst_get);
		}
	}

	// email client non activé ou email fournisseur non activé et statut _STATE_SUPP_WAIT_CONFIRM pour la commande (BCF confirmé)
	if( !$config['email_alerts_enabled'] && $r['state_id'] != _STATE_SUPP_WAIT_CONFIRM ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' config non activée'."\n", 3, '/var/log/php/error_ord_notify.log');
		}

		$config = $config_copy;
		return true;
	}

	if( $r['date_archived'] ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' commande archivée'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		$config = $config_copy;
		return true;
	}

	if( isset($config['days_order_notify']) && $config['days_order_notify']>-1 && $r['age']>$config['days_order_notify'] ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' délai dépassé'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		$config = $config_copy;
		return true;
	}

	// Pas de notifications sur les paniers
	if( in_array($r['state_id'], ord_states_get_uncompleted(false, false, false)) || $r['state_id'] == _STATE_MODEL ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' statut panier ou non terminé'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		$config = $config_copy;
		return true;
	}

	// si sodipec on retire la ref de la commande pour la notification
	if( $config['tnt_id'] == 21 ){
		$r['ref'] = '';
	}

	// Pas de notifications fournisseurs (sauf l'état _STATE_SUPP_WAIT_CONFIRM qui est redirigé un peu plus loin)
	if( in_array($r['state_id'], ord_states_get_supplier( true )) ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' statut fournisseur'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		$config = $config_copy;
		return true;
	}

	// Etat gérés par la synchronisation (transformation en d'autres documents, eux-mêmes notifiés)
	if( in_array( $r['state_id'], array(_STATE_PREPARATION, _STATE_BL_READY, _STATE_BL_PARTIEL_EXP, _STATE_BL_EXP, _STATE_INVOICE, _STATE_ARCHIVE) ) ){
		if( !$config['allow_orders_update_state'] ){
			// le document de type "bon de préparation" Bigship n'existe plus
			if( $r['state_id'] == _STATE_PREPARATION && $config['tnt_id'] == 1 ){
				return ord_pl_notify( $order, true );
			}
			if( $logger ){
				error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' statut géré autre part'."\n", 3, '/var/log/php/error_ord_notify.log');
			}
			$config = $config_copy;
			return true;
		}
	}

	// Pour la plupart des boutiques, l'état 3 n'est pas notifié pour les paiements type CB
	// Sauf mention contraire via "notify_state_wait_pay_for_cb"
	if( $r['state_id'] == _STATE_WAIT_PAY && in_array($r['pay_id'], array(_PAY_CB, _PAY_PAYPAL, _PAY_SOFINCO)) && !$config['notify_state_wait_pay_for_cb'] ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' statut 3 paiement CB'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		$config = $config_copy;
		return true;
	}

	// notifications web uniquement
	if( $r['state_id'] != _STATE_WAIT_VALIDATION ){
		if( $config['ord_notify_only_web'] && ( !is_numeric($r['pay_id']) || $r['pay_id']<=0 ) ){
			if( $logger ){
				error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' commande non web'."\n", 3, '/var/log/php/error_ord_notify.log');
			}
			$config = $config_copy;
			return true;
		}
	}

	// Seul les locataires désynchronisées envoient des notifications d'annulation (à configurer également)
	if( in_array($r['state_id'], ord_states_get_canceled( true )) ){
		if( !$config['allow_orders_update_state'] ){
			if( $logger ){
				error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' commande annulée'."\n", 3, '/var/log/php/error_ord_notify.log');
			}
			$config = $config_copy;
			return true;
		}
	}

	// Guery : la catégorie client doit être Poubelle-Pro ou Mobilier de ville
	if( $config['tnt_id'] == 27 ){
		$cat_cli = strtoupper(trim(fld_object_values_get( $r['user'], 2922, '', false, true )));
		if( !in_array($cat_cli, array('POUBELLE PRO', 'MOBILIER DE VILLE')) ){
			if( $logger ){
				error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' client non pp ou mdv'."\n", 3, '/var/log/php/error_ord_notify.log');
			}
			$config = $config_copy;
			return true;
		}
	}

	// pas de notification pour "Commande retirée au magasin"
	if( $r['state_id'] == _STATE_INV_STORE ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' statut retirée au magasin'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		$config = $config_copy;
		return false;
	}

	// pas de notification pour "Devis"
	if( $r['state_id'] == _STATE_DEVIS ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' statut devis'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		$config = $config_copy;
		return false;
	}

	// Si la commande est vide, n'envoie pas la notification
	$products = ord_products_get($r['id']);
	if( !$products || !ria_mysql_num_rows( $products ) ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' pas de produits dans la commande'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		$config = $config_copy;
		return false;
	}

	// Chargement du compte
	$rusr = gu_users_get( $r['user'] );
	if( !$rusr || !ria_mysql_num_rows( $rusr ) ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' chargement du client impossible'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		$config = $config_copy;
		return false;
	}
	$usr = ria_mysql_fetch_array( $rusr );

	if (isset($config['ctr_order_notify']) && !$config['ctr_order_notify']) {
		$mkt_users = ctr_marketplaces_get_users();
		if (is_array($mkt_users)) {
			if (in_array($usr['id'], $mkt_users)) {
				return true;
			}
		}
	}

	// Contrôle du client (email, n'est pas un fournisseur)
	if( !$usr['email'] ){
		if( $logger ){
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' client sans adresse email'."\n", 3, '/var/log/php/error_ord_notify.log');
		}
		if( $logger ) error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' utilisateur est un fournisseur'."\n", 3, '/var/log/php/error_ord_notify.log');
		$config = $config_copy;
		return false;
	}

	// Si l'utilisateur à choisi de ne pas recevoir d'alerte pour cet état de commande,
	// arrête l'exécution mais retourne tout de même true
	$control_notif = $r['state_id'] == _STATE_CLICK_N_COLLECT ? _STATE_PAY_CONFIRM : $r['state_id'];

	if ($r['pay_id'] != _PAY_SOFINCO || $r['state_id'] != _STATE_PAY_WAIT_CONFIRM) {
		if(
			!in_array($config['tnt_id'], array(26, 40))
			&& !in_array($r['state_id'], array(_STATE_PRJ_WAIT_VALIDATION, _STATE_PRJ_REFUSED, _STATE_WAIT_VALIDATION, _STATE_REFUSED))
			&& !gu_ord_alerts_exists( $r['user'], $control_notif )
		){
			if( $logger ){
				error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' client pas abonné à la notification'."\n", 3, '/var/log/php/error_ord_notify.log');
			}
			$config = $config_copy;
			return true;
		}
	}

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = false;
	if( $r['pay_id'] ){
		switch( $r['state_id'] ){
			case _STATE_WAIT_PAY :
			case _STATE_CLICK_N_COLLECT:
			case _STATE_PAY_CONFIRM :
				$rcfg = cfg_emails_get('ord-confirm', $r['wst_id']);
				break;
			case _STATE_BL_PARTIEL_EXP :
			case _STATE_BL_EXP :
			case _STATE_BL_STORE :
				$rcfg = cfg_emails_get('ord-shipped', $r['wst_id']);
				break;
			//case _STATE_INV_STORE
		}
	}

	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		$cfg_email_name = 'ord-alert';
		if( $config['tnt_id']==8 && substr(gu_users_get_parent_ref($usr['id']),0,2)=='US' ){
			$cfg_email_name = 'ord-alert-store';
		}
		$rcfg = cfg_emails_get($cfg_email_name, $r['wst_id']);
	}

	if( !ria_mysql_num_rows($rcfg) ){
		if( $logger ) {
			error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').' - tnt - '.$config['tnt_id'].'] ordID = '.$r['id'].', ordState = '.$r['state_id'].' config de mail non chargée');
		}
		$config = $config_copy;
		return false;
	}
	$cfg = ria_mysql_fetch_array($rcfg);

	$mkt_users = ctr_marketplaces_get_users();
	if( is_array($mkt_users) && in_array($usr['id'], $mkt_users) ) {
		$cfg['bcc'] = preg_replace("/(.*)@invite.trustpilot.com/", "", $cfg['bcc']);
	}

	// Crée le message
	$email = new Email();
	if( !in_array($r['state_id'], array(_STATE_WAIT_VALIDATION,_STATE_PRJ_WAIT_VALIDATION)) ){
		$email->setFrom( $cfg['from'] );
	}else{
		$email->setFrom( $usr['adr_firstname'].' '.$usr['adr_lastname'].' <'.$usr['email'].'>' );
	}

	// ajout d'un pdf pour nipahut
	if ($config['tnt_id'] == 89) {
		require_once($config['site_dir'].'/include/'.'view.pdf.inc.php');

		$file = $config['site_dir'].'/../pdf/'.$r['id'].'.pdf';
		if ($usr['prf_id'] == PRF_CUST_PRO) {
			$cgv = $config['site_dir'].'/../pdf/CGV-pro.pdf';
		}else{
			$cgv = $config['site_dir'].'/../pdf/CGV.pdf';
		}

		$error = false;
		$error = !generate_pdf($r, $file );

		if ($error) {
			if( $logger ){
				error_log('[ord_orders_notify - '.date('Y-m-d H:i:s').'] ordID = '.$r['id'].' erreur génération pdf'."\n", 3, '/var/log/php/error_ord_notify.log');
			}
			return false;
		}

		$email->addAttachment( $file, 'Commande_'.$r['id'].'.pdf' );
		$email->addAttachment( $cgv, 'cgv.pdf' );
	}

	$view_price = gu_users_rights_used( '_RGH_DWL_PRC', $usr['id'] );

	// récupère la hiérarchie parente de l'utilisateur
	$parents_ids = array();
	$rparents = rel_relations_get_parents(REL_USR_HIERARCHY, $usr['id']);
	if( $rparents && sizeof($rparents) ){
		foreach( $rparents as $p ){
			$parents_ids[] = $p['obj'][0];
		}
	}

	if( $usr['parent_id'] ){
		$parents_ids[] = $usr['parent_id'];
	}

	if($config['tnt_id'] == 36 && $config['wst_id'] == 81){
		//	Sardéco, si la commande est passée par un user, on notifie son parent
		//	Un vendeur est un sous compte
		$email->addTo( $usr['email'] );
		if(isset($config['user']['prf_id']) && $config['user']['prf_id'] == 5){
			/*$owner_data = site_owner_get($config['wst_id']);
			if(isset($owner_data['email']) ){
				$email->addBcc( $owner_data['email'] );
			}*/
		}
		elseif(isset($config['user']['prf_id']) && $config['user']['prf_id'] == PRF_USER){
			//	Si c'est un compte enfant, on notifie son parent
			if($parent_id = gu_users_get_parent_id( $config['user_id']) ){
				if($parent = gu_users_get( $parent_id ) ){
					$parent = ria_mysql_fetch_assoc($parent);
					if(isset($parent['email']) && $parent['email'] != ""){
						$email->addBcc( $parent['email'] );
						$is_child_user = true;
					}
				}
			}
		}
	}
	elseif( ($config['tnt_id'] != 39 || in_array($r['state_id'], array(_STATE_WAIT_VALIDATION,_STATE_PRJ_WAIT_VALIDATION))) && ( in_array($config['tnt_id'], array(11,53,45)) || !$usr['is_sync'] ) && sizeof($parents_ids) ){
		// créer un second mail pour les comptes utilisateur gérer par d'autre compte
		if( !in_array($r['state_id'], array(_STATE_WAIT_VALIDATION,_STATE_PRJ_WAIT_VALIDATION)) ){
			$email2 = new Email();
			$email2->setFrom( $cfg['from'] );
			$email2->addTo( $usr['email'] );
		}

		// envoi du mail principal au compte administrateur
		if( $config['tnt_id'] == 45 ){ // fch
			$rusr = gu_users_get( $parents_ids, '', '', false, '', 0, '', false, false, false, false, '', false, 0, '');
			if( $rusr && ria_mysql_num_rows($rusr) ){
				while( $to = ria_mysql_fetch_array($rusr) ){
					unset($parents_ids[array_search($to['id'], $parents_ids)]);

					if( $r['state_id'] == _STATE_PRJ_WAIT_VALIDATION && gu_users_rights_used('_RGH_ORD_PRJ', $to['id']) ){
						$email->addTo( $to['email'] );
					}
					if( $r['state_id'] == _STATE_WAIT_VALIDATION && gu_users_rights_used('_RGH_ORD_VLD', $to['id']) ){
						$email->addTo( $to['email'] );
					}
				}
			}
		}else{
			if( !$usr['parent_id'] ){
				$email->addTo( $usr['email'] );
			}else{
				$rusr = gu_users_get( 0, '', '', 0, '', 0, '', false, false, false, false, '', false, 0, '', $usr['parent_id'] );
				if( $rusr && ria_mysql_num_rows($rusr) ){
					while( $to = ria_mysql_fetch_array($rusr) ){
						if( in_array($config['tnt_id'], array(11,53)) || $to['is_sync'] ){
							$email->addTo( $to['email'] );
						}
					}
				}
			}
		}
	}
	elseif ( in_array($config['tnt_id'], [588, 1279]) && $r['state_id'] == _STATE_PREPARATION && in_array($usr['ref'], ['W9990', 'W9999']) ) {
		// Spé BERTON
		// Récupération du mail du client d'origine qui a passer commande au lieu du compte client divers
		$temp_mail = fld_object_values_get($r['id'], 102542);
		if ($temp_mail !== false) {
			$email->addTo( $temp_mail );
			$r_temp_usr = gu_users_get(0, $temp_mail);
			if ($r_temp_usr && ria_mysql_num_rows($r_temp_usr)) {
				$berton_usr = ria_mysql_fetch_assoc($r_temp_usr);
			}
		}
	}
	else { /* if( !$usr['parent_id'] || ($usr['is_sync'] && $usr['parent_id']) ) */
		$email->addTo( $usr['email'] );
	}

	if( $config['tnt_id'] == 40 ){
		// Ce morceau serait peut être a passer en générique à l'avenir
		// Récupération de toutes les personnes qui sont intervenu sur la commande
		$rstate = ord_orders_states_get( $r['id'] );
		if( $rstate && ria_mysql_num_rows($rstate) ){
			$copy_usr_id = array();
			while( $st = ria_mysql_fetch_assoc($rstate) ){
				$copy_usr_id[] = $st['usr_id'];
			}

			if( sizeof($copy_usr_id) > 0 ){
				$rusr = gu_users_get( $copy_usr_id );
				if( $rusr && ria_mysql_num_rows($rusr) ){
					while( $u = ria_mysql_fetch_array( $rusr ) ){
						$email->addCc( $u['email'] );
					}
				}
			}
		}

		// Si c'est une demande de validation de commande
		if (in_array($r['state_id'], array(_STATE_WAIT_VALIDATION, _STATE_PRJ_WAIT_VALIDATION))) {
			// Le compte lié a la commande est le compte parent synchronisé SOFI
			$ar_users = gu_users_get_family( $usr['id'], true );

			// on récupère la famille de ce compte pour ajouter que les valideurs en copie
			$rparent = rel_relations_get(0, null, $config['user_id'], REL_USR_HIERARCHY);
			if( $rparent && ria_mysql_num_rows($rparent) == 1 ){
				$parent = ria_mysql_fetch_assoc($rparent);

				$rchilds = rel_relations_hierarchy_childs_get_ids(REL_USR_HIERARCHY, $parent['src_0']);
				if( $rchilds && sizeof($rchilds) ){
					$ar_users = array_merge( $ar_users, $rchilds );
				}
			}

			$prf = array(21); // valideur de commande
			if( $r['state_id'] == _STATE_PRJ_WAIT_VALIDATION ){
				$prf[] = 23; // Demandeur de commande
			}

			// sélectionne les valideurs de l'arbo
			if (!empty($ar_users)) {
				$rusr = gu_users_get( $ar_users, '', '', $prf, '', 0, '', false, false, false, false, '', false, 0, '');
				if( $rusr && ria_mysql_num_rows($rusr) ){
					while( $to = ria_mysql_fetch_array($rusr) ){
						$email->addTo( $to['email'] );
					}
				}
			}
		}
	}

	if(sizeof($parents_ids)){
		$rusr = gu_users_get( $parents_ids );
		if( $rusr && ria_mysql_num_rows($rusr) ){
			while( $parent = ria_mysql_fetch_array( $rusr ) ){
				// dans le cas de gb les demandeurs de plus haut niveau de doivent pas avoir de notification
				if( $config['tnt_id'] == 40 && !in_array($parent['prf_id'], array(PRF_CUSTOMER, PRF_CUST_PRO, 21))) {
					continue;
				}
				$email->addCc( $parent['email'] );
			}
		}
	}elseif( $usr['alert_cc'] ){
		$email->addCc( $usr['alert_cc'] );
	}

	if( $cfg['bcc'] ){
		$email->addBcc( $cfg['bcc'] );
	}

	if( $cfg['reply-to'] ){
		$email->setReplyTo( $cfg['reply-to'] );
	}

	// Notifie également le représentant (si activé)
	if( isset( $config['notify-seller'] ) && isset( $config['notify-seller-state'] ) ){
		// par défaut : $config['notify-seller-state'] = array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM)
		if( $config['notify-seller'] && is_array( $config['notify-seller-state'] ) && in_array( $r['state_id'], $config['notify-seller-state'] ) ){
			$seller_id = false;
			if (in_array($config['tnt_id'], [588, 1279])) {
				$seller_id = isset($r['seller_id']) && is_numeric($r['seller_id']) && $r['seller_id'] > 0 ? $r['seller_id'] : false;
			}else{
				$seller_id = $usr['seller_id'];

				if (!is_numeric($seller_id) || $seller_id <= 0) {
					if (is_numeric($usr['parent_id']) && $usr['parent_id']) {
						$seller_id = gu_users_get_seller_id( $usr['parent_id'] );
					}
				}
			}

			if (is_numeric($seller_id) && $seller_id) {
				$rseller = gu_users_get( 0, '', '', PRF_SELLER, '', 0, '', false, false, $seller_id );

				if (ria_mysql_num_rows($rseller)) {
					$seller = ria_mysql_fetch_assoc($rseller);

					if( $seller['email'] ){
						$email->addBcc( $seller['email'] );
					}

					if( trim($seller['alert_cc'])!='' ){
						$email->addBcc($seller['alert_cc']);
					}
				}
			}
		}
	}

	// Gestion des notifications personnalisées
	if (isset($config['active_email_perso']) && $config['active_email_perso']) {
		$file_notify_exists = false;

		$file_emails_perso = $config['site_dir'].'/include/view.emails.inc.php';
		if (file_exists($file_emails_perso)) {
			$file_notify_exists = true;
		} else {
			$file_emails_perso = $config['site_dir'].'/../include/view.emails.inc.php';

			if (file_exists($file_emails_perso)) {
				$file_notify_exists = true;
			}
		}

		if ($file_notify_exists) {
			require_once($file_emails_perso);

			if (function_exists('riashop_order_notify')) {
				$notify_invoice = riashop_order_notify($email, $cfg, $r, $usr, $products);
				$config = $config_copy;
				return $notify_invoice;
			}
		}
	}

	// Notification spécifique à chaque client
	switch( $config['tnt_id'] ){
		case 1: {
			if($config['wst_id'] == 1  && (isset($config['bigship_prod']) && $config['bigship_prod'] || isset($config['bigship_emails_test']) && in_array($usr['email'], $config['bigship_emails_test']))) {
				require_once( $config['site_dir'].'/include/view.emails.inc.php' );
				$bigship_notify_order = bigship_notify_order( $email, $r, $usr, $products );
				$config = $config_copy;
				return $bigship_notify_order;
			}
			break;
		}
		case 4: {
			if ($r['wst_id'] == 30) {
				if (file_exists($config['site_dir'] . '/include/view.emails.inc.php')) {
					require_once($config['site_dir'] . '/include/view.emails.inc.php');
					if (function_exists('proloisirs_custom_order_notify')) {
						$custom_order_notify = proloisirs_custom_order_notify($email, $r, $usr, $products);
						$config = $config_copy;
						return $custom_order_notify;
					}
				}

				$config = $config_copy;
				return true;
			}elseif( $r['wst_id'] == 26 ){
				// Pas de notification pour les commandes borne proloisirs
				return true;
			}
		}
		case 13: {
			if( in_array($r['state_id'], array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM)) ){
				// notification spécifique Pierre Oteiza
				require_once( $config['site_dir'].'/include/view.emails.inc.php' );

				if( pierreoteiza_notify_order( $email, $r, $usr ) ){
					$config = $config_copy;
					return true;
				}
			}
			break;
		}
		case 39: {
			if( !in_array($r['state_id'], array(_STATE_WAIT_VALIDATION, _STATE_REFUSED)) ){
				require_once( $config['site_dir'].'/include/view.email.inc.php');
				if($r['wst_id'] == 69){
					$extranet_freevox_notify_send = freevox_notify_order( $email, $r, $usr, $products );
				}else{
					$extranet_freevox_notify_send = extranet_freevox_notify_order( $email, $r, $usr, $products );
				}

				$config = $config_copy;
				return $extranet_freevox_notify_send;
			}

			break;
		}
		case 40: {
			require_once( $config['site_dir'].'/include/view.emails.inc.php');
			$extranet_graphicbiz_notify_send = extranet_graphicbiz_notify_order( $email, $r, $usr, $products );
			$config = $config_copy;
			return $extranet_graphicbiz_notify_send;
		}
		case 45: {
			// ajout du pdf de la commande
			require_once 'Export/order.inc.php';

			$filename = 'commande-'.$r['id'].'.php';
			$tmpname = tempnam(sys_get_temp_dir(), $filename);

			// génération du PDF
			$options = [
				'check_status'	=> false,
				'filename' 		=> $tmpname,
				'output'		=> 'F'
			];

			export_order( $r['id'], 'pdf', $options );

			if( file_exists($tmpname) ){
				$email->addAttachment( $tmpname, 'Commande_'.$r['id'].'.pdf' );
				$email_has_attachment = true;
			}
			break;

		}
		/* case 47: {
			require_once( $config['site_dir'].'/include/view.emails.inc.php');
			$lightmeup_order_notify = false;
			 if( in_array($r['state_id'], array(_STATE_BL_EXP)) ){
				$lightmeup_order_notify = lightmeup_order_BL_EXP_notify( $email, $r, $usr, $products );
			}elseif(in_array($r['state_id'], array(_STATE_PAY_CONFIRM))){
				$lightmeup_order_notify = lightmeup_order_notify( $email, $r, $usr, $products );
			}

			$config = $config_copy;
			return $lightmeup_order_notify;
		} */
		case 51: {
			if ( file_exists($config['site_dir'].'/include/view.emails.inc.php') ) {
				require_once($config['site_dir'].'/include/view.emails.inc.php');
				if ( function_exists('idmat_custom_order_notify') ) {
					$custom_order_notify = idmat_custom_order_notify($email, $r, $usr, $products);
					$config = $config_copy;
					return $custom_order_notify;
				}
			}
			break;
		}
	}

	// notification spéciale extranet Chazelles
	if( !in_array($r['state_id'], array(_STATE_PRJ_WAIT_VALIDATION, _STATE_PRJ_REFUSED, _STATE_WAIT_VALIDATION, _STATE_REFUSED)) && $config['tnt_id']==8 && $config['wst_id'] == 22 ){
		$site_dir = ria_mysql_fetch_array( cfg_overrides_get( 22, array(), 'site_dir' ) );
		$config['site_dir'] = $site_dir['value'];
		require_once($config['site_dir'].'/include/view.email.inc.php');
		$send_confirmation_mail = send_confirmation_mail( $r, $usr, $products, $email, isset($email2) ? $email2 : null, $view_price );
		$config = $config_copy;
		return $send_confirmation_mail;
	}

	// notification spécifique Bobby
	if( $config['tnt_id'] == 19 ){
		require_once( $config['site_dir'].'/include/view.emails.inc.php' );
		$send_order_confirmation = false;
		if( $r['wst_id'] == 43 ){
			$send_order_confirmation = notify_order( $r );
		}else{
			$send_order_confirmation = send_order_confirmation( $email, $usr, $r, $products );
		}
		$config = $config_copy;
		return $send_order_confirmation;
	}

	// Défini le sujet du message
	$subject = null;
	switch( $r['state_id'] ){
		case _STATE_IN_PROCESS :
			$email->setSubject( 'Votre commande '.$config['site_name'].' '.$r['ref'].' est en cours de traitement' );
			if( isset($email2) ) $email2->setSubject( 'Votre commande '.$config['site_name'].' '.$r['ref'].' est en cours de traitement' );
			break;
		case _STATE_PREPARATION :
			$email->setSubject( 'Avis de préparation de votre commande '.$config['site_name'].' '.$r['ref'] );
			if( isset($email2) ) $email2->setSubject( 'Avis de préparation de votre commande '.$config['site_name'].' '.$r['ref'] );
			break;
		case _STATE_BL_READY :
			$email->setSubject( 'Votre commande '.$config['site_name'].' '.$r['ref'].' est prête à être expédiée' );
			if( isset($email2) ) $email2->setSubject( 'Votre commande '.$config['site_name'].' '.$r['ref'].' est prête à être expédiée' );
			break;
		case _STATE_BL_PARTIEL_EXP :
		case _STATE_BL_EXP :
			$email->setSubject( 'Avis d\'expédition de votre commande '.$config['site_name'].' '.$r['ref'] );
			if( isset($email2) ) $email2->setSubject( 'Avis d\'expédition de votre commande '.$config['site_name'].' '.$r['ref'] );
			break;
		case _STATE_PRJ_WAIT_VALIDATION :
			$email->setSubject( 'Demande de validation de projet '.$r['id'] );
			if( isset($email2) ) $email2->setSubject( 'Demande de validation de projet '.$r['id'] );
			break;
		case _STATE_WAIT_VALIDATION :
			$email->setSubject( 'Demande de validation du panier '.$r['id'] );
			if( isset($email2) ) $email2->setSubject( 'Demande de validation du panier '.$r['id'] );
			break;
		case _STATE_PRJ_REFUSED :
			$email->setSubject( 'Validation du projet '.$r['id'].' refusée' );
			if( isset($email2) ) $email2->setSubject( 'Validation du panier '.$r['id'].' refusée' );
			break;
		case _STATE_REFUSED :
			$email->setSubject( 'Validation du panier '.$r['id'].' refusée' );
			if( isset($email2) ) $email2->setSubject( 'Validation du panier '.$r['id'].' refusée' );
			break;
		case _STATE_BL_STORE :
			$email->setSubject( 'Votre commande '.$r['id'].' est disponible en magasin' );
			if( isset($email2) ) $email2->setSubject( 'Votre commande '.$r['id'].' est disponible en magasin' );
			break;
		//case _STATE_INV_STORE
		default:
			$email->setSubject( 'Votre commande '.$config['site_name'].' '.$r['ref'] );
			if( isset($email2) ) $email2->setSubject( 'Votre commande '.$config['site_name'].' '.$r['ref'] );

			if( $config['tnt_id'] == 23 && ( $r['state_id'] == _STATE_WAIT_PAY || $r['state_id'] == _STATE_PAY_CONFIRM ) ){
				$email->setSubject('Confirmation de commande '.$r['ref']);
			}
			break;
	}

	// notification spécifique kitabri
	if( $config['tnt_id'] == 23 ){
		// require_once( '/var/www/alexis_couturas/dinamik/htdocs/include/view.emails.inc.php' );
		require_once( $config['site_dir'].'/include/view.emails.inc.php' );
		if( kiabri_notify_order( $email, $r, $usr ) ){
			$config = $config_copy;
			return true;
		}
	}


	// notification spécifique Sardéco
	if( $config['tnt_id'] == 33 ){
		require_once( $config['site_dir'].'/include/view.emails.inc.php' );
		$send_order_confirmation = send_order_confirmation( $email, $usr, $r, $products );
		$config = $config_copy;
		return $send_order_confirmation;
	}

	// Composition
	$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );
	if( isset($email2) ){
		$email2->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );
	}

	// Notification spéciale Sardéco si la commande est passée par un vendeur
	if($config['tnt_id'] == 36 && $config['wst_id'] == 81){

		if(isset($config['user']['prf_id']) && $config['user']['prf_id'] == 5){
			/*$email->addParagraph('La commande suivante a été passée par '.$config['user']['adr_firstname'].' '.$config['user']['adr_lastname']);*/
		}
		elseif( isset($config['user']['prf_id']) && $config['user']['prf_id'] == PRF_USER && isset($is_child_user) ) {
			$email->addParagraph('La commande suivante a été passée par '.$config['user']['adr_firstname'].' '.$config['user']['adr_lastname']);
		}
	}

	if( !in_array($r['state_id'], array(_STATE_PRJ_WAIT_VALIDATION, _STATE_PRJ_REFUSED, _STATE_WAIT_VALIDATION, _STATE_REFUSED)) ){
		$email->addParagraph('Cher client, chère cliente,');
	}else{
		$email->addParagraph('Bonjour,');
	}

	if( isset($email2) ){
		if( !in_array($r['state_id'], array(_STATE_PRJ_WAIT_VALIDATION, _STATE_PRJ_REFUSED, _STATE_WAIT_VALIDATION, _STATE_REFUSED)) ){
			$email2->addParagraph('Cher client, chère cliente,');
		}else{
			$email2->addParagraph('Bonjour,');
		}
	}

	$email->addBlankTextLine();
	if( isset($email2) ){
		$email2->addBlankTextLine();
	}

	if( in_array( $config['tnt_id'], array(5, 20) ) && ( $r['state_id'] == _STATE_WAIT_PAY || $r['state_id'] == _STATE_PAY_CONFIRM ) ){
		$email->addParagraph('Votre commande No '.( $r['ref'] ? $r['ref'] : $r['id'] ).' du '.$r['date'].' a bien été prise en compte par nos services, et son traitement débutera dans les meilleurs délais. Vous trouverez ci-dessous le détail des articles commandés.');
		if( isset($email2) ){
			$email2->addParagraph('Votre commande No '.( $r['ref'] ? $r['ref'] : $r['id'] ).' du '.$r['date'].' a bien été prise en compte par nos services, et son traitement débutera dans les meilleurs délais. Vous trouverez ci-dessous le détail des articles commandés.');
		}
	}elseif( !in_array($r['state_id'], array(_STATE_PRJ_WAIT_VALIDATION, _STATE_PRJ_REFUSED, _STATE_WAIT_VALIDATION, _STATE_REFUSED)) ){
		if( $config['tnt_id']== 29 ){
			$msg = ' a été '.( in_array($r['state_id'], array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM, _STATE_CLICK_N_COLLECT)) ? 'validé' : 'modifié' ).' par notre service client';
			if( is_null($r['dlv_country']) || trim($r['dlv_country']) == '' ){
				$r['dlv_country'] = 'FR';
			}
			if( !in_array(trim($r['dlv_country']), array('FR','FRANCE')) || !sys_postal_code_is_france_metropol($r['dlv_postal_code']) ){
				$msg = ' sera validé par notre service client sous réserve d’acceptation des frais de transport';
			}
			$email->addParagraph('Votre commande No '.( $r['ref'] ? $r['ref'] : $r['id'] ).', enregistrée le '.$r['date'].','. $msg .' Veuillez trouver ci-dessous le détail de l\'opération :');
			if( isset($email2) ){
				$email2->addParagraph('Votre commande No '.( $r['ref'] ? $r['ref'] : $r['id'] ).', enregistrée le '.$r['date'].', a été '.( in_array($r['state_id'], array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM, _STATE_CLICK_N_COLLECT)) ? 'validé' : 'modifié' ).' par notre service client. Veuillez trouver ci-dessous le détail de l\'opération :');
			}
		}else{
			if( in_array($r['state_id'], [_STATE_WAIT_PAY, _STATE_PAY_CONFIRM]) ){
				$email->addParagraph('Votre commande No '.( $r['ref'] ? $r['ref'] : $r['id'] ).', enregistrée le '.$r['date'].', a bien été prise en compte par notre service client. Veuillez trouver ci-dessous le détail de l\'opération :');
				if( isset($email2) ){
					$email2->addParagraph('Votre commande No '.( $r['ref'] ? $r['ref'] : $r['id'] ).', enregistrée le '.$r['date'].', a bien été prise en compte par notre service client. Veuillez trouver ci-dessous le détail de l\'opération :');
				}
			}else{
				$email->addParagraph('Votre commande No '.( $r['ref'] ? $r['ref'] : $r['id'] ).', enregistrée le '.$r['date'].', a été modifié par notre service client. Veuillez trouver ci-dessous le détail de l\'opération :');
				if( isset($email2) ){
					$email2->addParagraph('Votre commande No '.( $r['ref'] ? $r['ref'] : $r['id'] ).', enregistrée le '.$r['date'].', a été modifié par notre service client. Veuillez trouver ci-dessous le détail de l\'opération :');
				}
			}
		}
	}

	// NAVICOM
	if( isset( $_SESSION['usr_prf_id'], $_SESSION['admin_view_user'], $_SESSION['usr_id'], $config['tnt_id'] ) && $config['tnt_id'] == 3 && ( $_SESSION['usr_prf_id'] == PRF_ADMIN || $_SESSION['usr_prf_id'] == PRF_SELLER ) && $_SESSION['admin_view_user'] != $_SESSION['usr_id'] && $_SESSION['admin_view_user'] > 0 ){
		$email->addParagraph('Cette commande a été saisie par: '.$_SESSION['usr_firstname'].' '.$_SESSION['usr_lastname'].'.');

	}

	$email->addBlankTextLine();
	if( isset($email2) ){
		$email2->addBlankTextLine();
	}

	$updater = $usr;
	if( in_array($r['state_id'], array(_STATE_WAIT_VALIDATION, _STATE_PRJ_WAIT_VALIDATION)) ){
		// Récupération de la personne qui à émis la demande de validation
		$rstate = ord_orders_states_get( $r['id'], _STATE_WAIT_VALIDATION );
		if( $rstate && ria_mysql_num_rows($rstate) ){
			$state = ria_mysql_fetch_assoc($rstate);
			if( $state['usr_id'] != $usr['id'] ){
				$rupdater = gu_users_get($state['usr_id']);
				if( $rupdater && ria_mysql_num_rows($rupdater) ){
					$updater = ria_mysql_fetch_assoc($rupdater);
				}
			}
		}
	}

	switch( $r['state_id'] ){
		case _STATE_WAIT_PAY: {
			if( $r['comments'] ){
				$email->addParagraph( $r['comments'] );
				if( isset($email2) ){
					$email2->addParagraph( $r['comments'] );
				}
			}
			switch( $r['pay_id'] ){
				case _PAY_CB: {
					// Règlement par carte
					$email->addParagraph('Votre règlement par carte bleue est en attente de confirmation par votre banque. Conformément à nos conditions générales de vente, nous vous rappellons que votre compte ne sera débité qu\'à l\'expédition de votre commande.');
					if( isset($email2) ){
						$email2->addParagraph('Votre règlement par carte bleue est en attente de confirmation par votre banque. Conformément à nos conditions générales de vente, nous vous rappellons que votre compte ne sera débité qu\'à l\'expédition de votre commande.');
					}
					break;
				}
				case _PAY_CHEQUE: {
					if( $config['tnt_id'] == 25 ){
						$email->addParagraph('Vos conditions de règlement à la société '.$config['site_name'].' sont :');
						// Règlement par chèque détaillé
						$sub_payment_string = '';
						if( $rcompte = gu_users_payment_types_get( $usr['id'], 2 ) ){
							if( $compte = ria_mysql_fetch_array($rcompte) ){
								if( $compte['days']!='' && $compte['day_stop']!='' ){
									$sub_payment_string = ' à '.$compte['days'].' le '.$compte['day_stop'];
								}
							}
						}
						$email->addParagraph('Règlement par chèque'.$sub_payment_string.'.');

						if( isset($email2) ){
							$email2->addParagraph('Vos conditions de règlement à la société '.$config['site_name'].' sont :');
							$email2->addParagraph('Règlement par chèque'.$sub_payment_string.'.');
						}
					}else{
						// Règlement par chèque

						// Spé OZ ticket #23202
						if ($config['tnt_id'] == 29) {
							$email->addParagraph('Votre commande a bien été enregistrée. Vous avez choisi de payer par chèque, veuillez adresser votre règlement à l\'adresse suivante :');
						}else{
							$email->addParagraph('Votre commande a bien été enregistrée, nous sommes cependant en attente de votre règlement pour lancer son traitement. Vous avez choisi de payer par chèque, veuillez adresser votre règlement à l\'adresse suivante :');
						}

						$email->addBlankTextLine();
						if(isset($config['adr_pay_cheque']) && trim($config['adr_pay_cheque'])!='' ){
							$email->addAddress( $config['adr_pay_cheque'] );
						} else {
							$owner = site_owner_get( $r['wst_id'] );
							$email->addAddress(
								$owner['name']."\n".
								$owner['address1']."\n".
								( $owner['address2'] ? $owner['address2']."\n" : '' ).
								$owner['zipcode'].' '.$owner['city']
							);
						}
						if( isset($email2) ){

							$email2->addParagraph('Votre commande a bien été enregistrée, nous sommes cependant en attente de votre règlement pour lancer son traitement. Vous avez choisi de payer par chèque, veuillez adresser votre règlement à l\'adresse suivante :');
							$email2->addBlankTextLine();
							if(isset($config['adr_pay_cheque']) && trim($config['adr_pay_cheque'])!='' ){
								$email2->addAddress( $config['adr_pay_cheque'] );
							} else {
								$owner = site_owner_get( $r['wst_id'] );
								$email2->addAddress(
									$owner['name']."\n".
									$owner['address1']."\n".
									( $owner['address2'] ? $owner['address2']."\n" : '' ).
									$owner['zipcode'].' '.$owner['city']
								);
							}
						}

						$email->addParagraph('Merci d’indiquer votre Nom, votre Prénom et le Numéro de commande '.$r['id'].', au dos du chèque.');

						if( isset($email2) ){
							$email2->addParagraph('Merci d’indiquer votre Nom, votre Prénom et le Numéro de commande '.$r['id'].', au dos du chèque.');
						}
					}

					break;
				}
				case _PAY_COMPTE: {
					// Règlement par LCR
					$email->addParagraph('Vos conditions de règlement à la société '.$config['site_name'].' sont :');
					$sub_payment_string = '';
					if( $rcompte = gu_users_payment_types_get($usr['id'],_PAY_COMPTE) ){
						if( $compte = ria_mysql_fetch_array($rcompte) ){
							if( $compte['days']!='' && $compte['day_stop']!='' ){
								$sub_payment_string = ' à '.$compte['days'].' le '.$compte['day_stop'];
								if ($compte['days'] == '0' || $compte['day_stop'] == '0') {
									$sub_payment_string = '';
								}
							}
						}
					}
					$email->addParagraph('Règlement par LCR'.$sub_payment_string.'.');

					if( isset($email2) ){
						$email2->addParagraph('Vos conditions de règlement à la société '.$config['site_name'].' sont :');
						$email2->addParagraph('Règlement par LCR'.$sub_payment_string.'.');
					}

					break;
				}
				case _PAY_VIREMENT: {
					// Règlement par virement
					if ($config['tnt_id'] == 29) {
						$email->addParagraph('Votre commande a bien été enregistrée, vous avez choisi de payer par virement.');
					}else{
						$email->addParagraph('Votre commande a bien été enregistrée, nous sommes cependant en attente de votre règlement pour lancer son traitement. Vous avez choisi de payer par virement, veuillez effectuer votre règlement sur le compte suivant :');
					}
					$bank = site_bank_details_get( 0, $usr['prf_id'] );
					if( $bank && ria_mysql_num_rows($bank) ){
						$email->addhtml( site_bank_details_describe( ria_mysql_fetch_array($bank), false ) );
					}
					else{
						if ($config['tnt_id'] == 29) {
							$email->addParagraph('N’hésitez pas à nous contacter si vous souhaitez plus de détails.');
						}else{
							$email->addParagraph('Veuillez nous contacter pour obtenir les détails.');
						}
					}

					break;
				}
				case 14: {
					// Règlement par mandat administratif
					$email->addParagraph('Votre commande a bien été enregistrée, nous sommes cependant en attente de votre règlement pour lancer son traitement. Vous avez choisi de payer par mandat administratif, votre commande sera préparée dès validation par notre service administratif qui vous contactera si nécessaire.');
					$email->addParagraph('Sans contact de notre part, veuillez considérer votre commande comme acceptée.');

					break;
				}
				case _PAY_YESBYCASH: {
					$email->addParagraph('Vous avez choisi le règlement de votre commande '.$r['id'].' par YesByCash.');
					$email->addHtml('<p>Vous avez la possibilité de régler votre commande dans n\'importe quel point de paiement YesByCash accessible sur : <a href="https://www.myemoneypurse.com/outlets">https://www.myemoneypurse.com/outlets</a>. Pour cela munissez-vous du document que vous recevrez dans quelques instants par e-mail pour régler votre commande.</p>');
					break;
				}
			}
			$email->addBlankTextLine();
			if( isset($email2) ){
				$email2->addBlankTextLine();
			}
			break;
		}
		case _STATE_CLICK_N_COLLECT:
		case _STATE_PAY_CONFIRM: {
			if( $config['tnt_id'] != 5 ) {
				$email->addParagraph('Votre commande a bien été prise en compte par nos services, et son traitement débutera dans les meilleurs délais. Vous trouverez ci-dessous le détail des articles commandés.');
				if( isset($email2) ){
					$email2->addParagraph('Votre commande a bien été prise en compte par nos services, et son traitement débutera dans les meilleurs délais. Vous trouverez ci-dessous le détail des articles commandés.');
				}
			}
			break;
		}
		case _STATE_IN_PROCESS: {
			$email->addParagraph('Le traitement de votre commande a commencé.'."\n".'Nous sommes en train de procéder aux vérifications d\'usage (disponibilité des produits commandés, etc.).');
			if( isset($email2) ){
				$email2->addParagraph('Le traitement de votre commande a commencé.'."\n".'Nous sommes en train de procéder aux vérifications d\'usage (disponibilité des produits commandés, etc.).');
			}
			break;
		}
		case _STATE_PREPARATION: {
			$email->addParagraph('Les vérifications d\'usage sur votre commande sont terminées.'."\n".'Nous sommes en train de réunir les produits et de les conditionner.');
			if( isset($email2) ){
				$email2->addParagraph('Les vérifications d\'usage sur votre commande sont terminées.'."\n".'Nous sommes en train de réunir les produits et de les conditionner.');
			}
			break;
		}
		case _STATE_BL_READY: {
			$email->addParagraph('Le traitement de votre commande est terminé.'."\n".'Les articles que vous avez commandé sont désormais prêts à vous être expédiés.');
			if( isset($email2) ){
				$email2->addParagraph('Le traitement de votre commande est terminé.'."\n".'Les articles que vous avez commandé sont désormais prêts à vous être expédiés.');
			}
			break;
		}
		case _STATE_INVOICE: {
			if( $config['tnt_id']==5 ){
				$invoices = ord_ord_invoices_get($r['id']);
				if( ria_mysql_num_rows($invoices)==1 ){
					$inv = ria_mysql_fetch_array($invoices);
					$email->addParagraph('La facture correspondante est disponible dans <a href="'.$config['site_url'].'/mon-compte/factures/">votre espace client</a>. Elle porte le numéro de pièce <a href="'.$config['site_url'].'/mon-compte/factures/facture?inv='.$inv['id'].'">'.$inv['piece'].'</a>. Merci de bien vouloir noter qu\'aucune facture ne vous sera envoyée par courrier, dans un souci de développement durable. En cas de problème pour télécharger votre facture, n\'hésitez-pas à contacter notre <a href="'.$config['site_url'].$config['contact_page_url'].'">service client</a>.');
					if( isset($email2) ){
						$email2->addParagraph('La facture correspondante est disponible dans <a href="'.$config['site_url'].'/mon-compte/factures/">votre espace client</a>. Elle porte le numéro de pièce <a href="'.$config['site_url'].'/mon-compte/factures/facture?inv='.$inv['id'].'">'.$inv['piece'].'</a>. Merci de bien vouloir noter qu\'aucune facture ne vous sera envoyée par courrier, dans un souci de développement durable. En cas de problème pour télécharger votre facture, n\'hésitez-pas à contacter notre <a href="'.$config['site_url'].$config['contact_page_url'].'">service client</a>.');
					}
				}elseif( ria_mysql_num_rows($invoices)>1 ){
					$ar_invoices = array();
					while( $inv = ria_mysql_fetch_array($invoices) ){
						$ar_invoices[] = '<a href="'.$config['site_url'].'/mon-compte/factures/facture?inv='.$inv['id'].'">'.$inv['piece'].'</a>';
					}
					$email->addParagraph('Les factures établies pour cette commande sont disponibles dans votre <a href="'.$config['site_url'].'/mon-compte/factures/">votre espace client</a>. Elles portent les numéros de pièce : '.implode(', ',$ar_invoices).' Merci de bien vouloir noter qu\'aucune facture ne vous sera envoyée par courrier, dans un souci de développement durable. En cas de problème pour télécharger votre facture, n\'hésitez-pas à contacter notre <a href="'.$config['site_url'].$config['contact_page_url'].'">service client</a>.');
					if( isset($email2) ){
						$email2->addParagraph('Les factures établies pour cette commande sont disponibles dans votre <a href="'.$config['site_url'].'/mon-compte/factures/">votre espace client</a>. Elles portent les numéros de pièce : '.implode(', ',$ar_invoices).' Merci de bien vouloir noter qu\'aucune facture ne vous sera envoyée par courrier, dans un souci de développement durable. En cas de problème pour télécharger votre facture, n\'hésitez-pas à contacter notre <a href="'.$config['site_url'].$config['contact_page_url'].'">service client</a>.');
					}
				}
			}else{
				$email->addParagraph('Nous avons été informés par notre transporteur que votre commande vous a bien été livrée.');
				if( isset($email2) ){
					$email2->addParagraph('Nous avons été informés par notre transporteur que votre commande vous a bien été livrée.');
				}
			}
			break;
		}
		case _STATE_CANCEL_USER: {
			$email->addParagraph('Nous avons bien pris en compte votre demande d\'annulation de cette commande et l\'annulation s\'est correctement déroulée.');
			if( isset($email2) ){
				$email2->addParagraph('Nous avons bien pris en compte votre demande d\'annulation de cette commande et l\'annulation s\'est correctement déroulée.');
			}
			break;
		}
		case _STATE_CANCEL_MERCHAND: {
			$email->addParagraph('Votre commande vient d\'être annulée par notre service commercial. Si vous souhaitez plus d\'informations sur la raison de cette annulation, veuillez nous contacter. Vous trouverez les informations nécessaires à l\'adresse suivante : '.$config['site_url'].$config['contact_page_url']);
			if( isset($email2) ){
				$email2->addParagraph('Votre commande vient d\'être annulée par notre service commercial. Si vous souhaitez plus d\'informations sur la raison de cette annulation, veuillez nous contacter. Vous trouverez les informations nécessaires à l\'adresse suivante : '.$config['site_url'].$config['contact_page_url']);
			}
			break;
		}
		case _STATE_WAIT_VALIDATION: {

			$email->addParagraph( 'Une demande de validation a été émise par '.$updater['adr_firstname'].' '.$updater['adr_lastname'].' pour le panier n°'.$r['id'].'.' );
			if( isset($email2) ){
				$email2->addParagraph( 'Une demande de validation a été émise par '.$updater['adr_firstname'].' '.$updater['adr_lastname'].' pour le panier n°'.$r['id'].'.' );
			}
			break;
		}
		case _STATE_PRJ_WAIT_VALIDATION: {

			$email->addParagraph( 'Une demande de validation a été émise par '.$updater['adr_firstname'].' '.$updater['adr_lastname'].' pour le projet n°'.$r['id'].'.' );
			if( isset($email2) ){
				$email2->addParagraph( 'Une demande de validation a été émise par '.$updater['adr_firstname'].' '.$updater['adr_lastname'].' pour le projet n°'.$r['id'].'.' );
			}
			break;
		}
		case _STATE_REFUSED:
		case _STATE_PRJ_REFUSED: {
			$txt = 'Votre demande de validation du panier n°'.$r['id'].' a été refusée';
			// vérifier la valeur du champs avancé (Par qui)
			$who = fld_object_values_get( $r['id'], _FLD_ORD_WHO );
			if( $who!='' ){
				$rusr_who = gu_users_get( $who );
				if( $rusr_who && ria_mysql_num_rows($rusr_who) ){
					$usr_who = ria_mysql_fetch_array($rusr_who);
					$txt .= ' par '.$usr_who['adr_firstname'].' '.$usr_who['adr_lastname'];
					// vérifier le motif de refus
					$why = fld_object_values_get( $r['id'], _FLD_ORD_WHY );
					if( $why!='' ){
						$txt .= ' pour le motif suivant : '.$why;
					}else{
						$txt .= '.';
					}
				}
			}
			$email->addParagraph( $txt );
			if( isset($email2) ){
				$email2->addParagraph( $txt );
			}
			break;
		}
		case _STATE_BL_STORE: {
			$txt = 'Votre commande est disponible au magasin :'."\n";
			if( $r['str_id'] ){

				$str = ria_mysql_fetch_array(dlv_stores_get( $r['str_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ));
				$txt .= $str['name']."\n";
				$txt .= $str['address1']."\n";
				if( trim($str['address2']) ) $txt .= $str['address2']."\n";
				$txt .= $str['zipcode'].' '.$str['city']."\n";
				$txt .= $str['country']."\n";
				if( trim($str['phone']) ) $txt .= 'Tél. : '.$str['phone']."\n";

			}
			$email->addParagraph( $txt );
			if( isset($email2) ){
				$email2->addParagraph( $txt );
			}
			break;
		}
		case _STATE_PAY_WAIT_CONFIRM: {
			$txt = 'Nous avons recu le paiement de votre commande, cependant celui-ci est en attente de validation.'."\n";

			if( $r['pay_id'] == _PAY_SOFINCO ){

				$credits = ord_payment_credits_get( 0, $r['id']);
				if( $credits && ria_mysql_num_rows($credits) ){
					$credit = ria_mysql_fetch_array($credits);
					$txt .= 'Le numéro de dossier relatif à votre demande de financement Sofinco est le '.htmlspecialchars($credit['ref']).'.'."\n";
				}

			}

			$email->addParagraph( $txt );
			if( isset($email2) )
				$email2->addParagraph( $txt );

			if( $r['pay_id'] == _PAY_SOFINCO ){
				$email->addHtml('<p style="font-size:1.1em;text-transform: uppercase;"><strong>Un crédit vous engage et doit être remboursé.<br />Vérifiez vos capacités de remboursement avant de vous engager.</strong></p>');
				if( isset($email2) ){
					$email2->addHtml('<p><strong>Un crédit vous engage et doit être remboursé.<br />Vérifiez vos capacités de remboursement avant de vous engager.</strong></p>');
				}
			}
			break;
		}
		//case _STATE_INV_STORE :
		case _STATE_BL_EXP: {
			if (isset($config['ord_notify_show_bl']) && $config['ord_notify_show_bl']) {
				$bl_list = ord_orders_get_bl_list($r['id']);
				if (is_array($bl_list) && count($bl_list)) {
					$bl = $bl_list[0];

					$email->addHtml('
						<!--
							<script type="application/json+trustpilot">
								{
									"recipientEmail":"'.$usr['email'].'",
									"recipientName":"'.$usr['adr_lastname'].' '.$usr['adr_firstname'].'",
									"referenceId":"'.$bl['piece'].'"
								}
							</script>
						-->
					');

					if( $bl['srv_id'] ){
						$srv = ria_mysql_fetch_array(dlv_services_get($bl['srv_id']));
						if( $srv['url-site'] ){
							$srv_name = '<a href="'.$srv['url-site'].'">'.htmlspecialchars($srv['name']).'</a>';
						}else{
							$srv_name = htmlspecialchars($srv['name']);
						}
						$email->addParagraph( str_replace( '%nom%', $srv_name, $srv['alert-msg'] ) );
					}else{
						$email->addParagraph('Votre commande vient d\'être expédiée.'."\n".'Elle a été prise en charge par notre transporteur.');
					}

					// Numéros de colis
					$colis = ord_bl_colis_get($bl['id']);
					$have_links = false;

					for( $i=0; $i<sizeof($colis); $i++ ){
						if( $bl['srv_id'] && $srv['url-colis'] ){
							$url_suivi = '';

							if( strpos($srv['url-colis'], '#param[colis]#')!==false ){
								if( $rly_id ){
									$rrly = dlv_relays_get_simple( $rly_id );
									if( $rrly && ria_mysql_num_rows($rrly) ){
										$rly = ria_mysql_fetch_assoc( $rrly );

										$tmp = str_replace(
											array('#param[colis]#', '#param[zipcode]#'),
											array($colis[$i], $rly['zipcode']),
											$srv['url-colis']
										);

										$url_suivi = '<a href="'.$tmp.'">'.$colis[$i].'</a>';
									}
								}
							}else{
								$url_suivi = '<a href="'.$srv['url-colis'].$colis[$i].'">'.$colis[$i].'</a>';
							}

							if( trim($url_suivi) ){
								$colis[$i] = $url_suivi;
								$have_links = true;
							}
						}
					}

					switch( sizeof($colis) ){
						case 0:
							break;
						case 1:
							$email->addParagraph('Votre commande porte le numéro de colis suivant chez notre transporteur : '.$colis[0]);
							break;
						default:
							$email->addParagraph('Votre commande à été expédiée en plusieurs colis.'."\n".'Voici leur références chez notre transporteur : '.implode(', ',$colis));
							break;
					}
				}
			}
			break;
		}
	}

	// charge l'adresse de livraison si :
	// statut validé ou
	// statut non validé mais activation forcée (20, 23, 25, 29)
	if( in_array($r['state_id'], array(_STATE_CLICK_N_COLLECT, _STATE_PAY_CONFIRM)) || ( isset($config['notify_include_adr_delivery']) && $config['notify_include_adr_delivery'] && $r['state_id'] == _STATE_WAIT_PAY ) ){
		$show_address_livr = true;

		if( isset($config['notify_ord_childs']) && $config['notify_ord_childs'] ){
			$r_ochild = ord_orders_get_childs( $order, true);

			if( $r_ochild && ria_mysql_num_rows($r_ochild) > 1 ){
				$show_address_livr = false;
			}
		}

		if( $show_address_livr ){
			$email->addBlankTextLine();
			if( isset($email2) ){
				$email2->addBlankTextLine();
			}

			$email->addParagraph('Conformément à votre souhait, votre commande vous sera livrée à l\'adresse suivante :');
			if( isset($email2) ){
				$email2->addParagraph('Conformément à votre souhait, votre commande vous sera livrée à l\'adresse suivante :');
			}

			$dlv_address = '';
			$find_adr = $adr_given = false;
			if( is_numeric($r['rly_id']) ){
				$rrly = dlv_relays_get_simple( $r['rly_id'] );
				if( $rrly && ria_mysql_num_rows( $rrly ) ){
					$rly = ria_mysql_fetch_assoc( $rrly );
					$dlv_address = $rly['name']."\n";
					$dlv_address .= $rly['address1']."\n";
					if( trim($rly['address2']) ) $dlv_address .= $rly['address2']."\n";
					$dlv_address .= $rly['zipcode'].' '.$rly['city']."\n";
					$dlv_address .= $rly['country']."\n";
					$find_adr = true;
				}else{
					error_log(__FILE__.':'.__LINE__.' - Impossible de charger le point-relais N° '.$r['rly_id'].'.');
				}
			}elseif( is_numeric($r['str_id']) ){
				$rstr = dlv_stores_get( $r['str_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
				if( $rstr && ria_mysql_num_rows( $rstr ) ){
					$str = ria_mysql_fetch_assoc( $rstr );
					$dlv_address = $str['name']."\n";
					$dlv_address .= $str['address1']."\n";
					if( trim($str['address2']) ) $dlv_address .= $str['address2']."\n";
					$dlv_address .= $str['zipcode'].' '.$str['city']."\n";
					$dlv_address .= $str['country']."\n";
					$find_adr = true;
				}else{
					error_log(__FILE__.':'.__LINE__.' - Impossible de charger le magasin N° '.$r['str_id'].'.');
				}
			}elseif( $config['tnt_id']==13 && trim($r['dlv-notes'])!='' ){
				$dlv_address = str_replace('<br />', "\n", $r['dlv-notes']);
				$find_adr = true;
			}

			if(($config["tnt_id"]==26) && !$find_adr){
				if($r_ochild && ria_mysql_num_rows($r_ochild)){
					$ochild = ria_mysql_fetch_assoc($r_ochild);

					if( trim($ochild['dlv_firstname']) || trim($ochild['dlv_lastname']) ){
						$dlv_address .= $ochild['dlv_firstname'].' '.$ochild['dlv_lastname']."\n";
					}
					if( trim($ochild['dlv_society']) ){
						$dlv_address .= $ochild['dlv_society']."\n";
					}
					$dlv_address .= $ochild['dlv_address1']."\n";
					if( trim($ochild['dlv_address2']) ) $dlv_address .= $ochild['dlv_address2']."\n";
					$dlv_address .= $ochild['dlv_postal_code'].' '.$ochild['dlv_city']."\n";
					$dlv_address .= $ochild['dlv_country'];
					$find_adr = true;
					$adr_given = true;
				}
			}

			if( !$find_adr ){
				if( trim($r['dlv_firstname']) || trim($r['dlv_lastname']) ){
					$dlv_address .= $r['dlv_firstname'].' '.$r['dlv_lastname']."\n";
				}
				if( trim($r['dlv_society']) ){
					$dlv_address .= $r['dlv_society']."\n";
				}
				$dlv_address .= $r['dlv_address1']."\n";
				if( trim($r['dlv_address2']) ) $dlv_address .= $r['dlv_address2']."\n";
				$dlv_address .= $r['dlv_postal_code'].' '.$r['dlv_city']."\n";
				$dlv_address .= $r['dlv_country'];
			}

			$email->addAddress($dlv_address);
			if( isset($email2) ){
				$email2->addAddress($dlv_address);
			}

			$email->addBlankTextLine();
			if( isset($email2) ){
				$email2->addBlankTextLine();
			}
		}
	}

	if( isset($config['notify_include_adr_invoice']) && is_array($config['notify_include_adr_invoice']) && sizeof($config['notify_include_adr_invoice']) ){
		if( in_array($r['state_id'], $config['notify_include_adr_invoice']) ){
			$email->addBlankTextLine();
			$email->addParagraph('Retrouvez ci-dessous votre adresse de facturation :');

			$inv_address = '';
			if( trim($r['inv_firstname']) || trim($r['inv_lastname']) ){
				$inv_address .= $r['inv_firstname'].' '.$r['inv_lastname']."\n";
			}
			if( trim($r['inv_society']) ){
				$inv_address .= $r['inv_society']."\n";
			}
			$inv_address .= $r['inv_address1']."\n";
			if( trim($r['inv_address2']) ) $inv_address .= $r['inv_address2']."\n";
			if( trim($r['inv_address3']) ) $inv_address .= $r['inv_address3']."\n";
			$inv_address .= $r['inv_postal_code'].' '.$r['inv_city']."\n";
			$inv_address .= $r['inv_country'];

			$email->addAddress($inv_address);
			$email->addBlankTextLine();
		}
	}

	if( isset($config['notify_include_dlvnote']) && $config['notify_include_dlvnote'] && trim($r['dlv-notes'])!='' ){
		$email->addBlankTextLine();
		$email->addParagraph('Retrouvez ci-dessous le commentaire :');
		$email->addParagraph($r['dlv-notes']);
		$email->addBlankTextLine();
	}

	if( isset($r['date_livr_en']) && wst_websites_is_fdv_app($config['tnt_id'], $r['wst_id']) ){
		$date_livr = new DateTime($r['date_livr_en']);
		$now = new DateTime();
		if( $date_livr->getTimestamp() > $now->getTimestamp() ){
			$email->addParagraph('Votre commande sera livrée à partir du '.$date_livr->format('d/m/Y').'. Cette date de livraison est indicative.');
		}
	}

	// Rappel du contenu de la commande
	$email->addBlankTextLine();
	switch( $r['state_id'] ){
		case _STATE_PREPARATION:
			$email->addParagraph('Veuillez trouver ci-dessous le détail des articles en cours de préparation :');
			if( isset($email2) )
				$email2->addParagraph('Veuillez trouver ci-dessous le détail des articles en cours de préparation :');
			break;
		case _STATE_BL_READY:
			$email->addParagraph('Veuillez trouver ci-dessous le détail des articles prêts à vous être expédiés :');
			if( isset($email2) )
				$email2->addParagraph('Veuillez trouver ci-dessous le détail des articles prêts à vous être expédiés :');
			break;
		case _STATE_BL_PARTIEL_EXP:
		case _STATE_BL_EXP:
			$email->addParagraph('Veuillez trouver ci-dessous le détail des produits expédiés :');
			if( isset($email2) )
				$email2->addParagraph('Veuillez trouver ci-dessous le détail des produits expédiés :');
			break;
		case _STATE_WAIT_VALIDATION:
			$email->addParagraph( 'Veuillez trouver ci-dessous le contenu du panier : ' );
			if( isset($email2) )
				$email2->addParagraph( 'Veuillez trouver ci-dessous le contenu du panier : ' );
			break;
		case _STATE_PRJ_WAIT_VALIDATION:
			$email->addParagraph( 'Veuillez trouver ci-dessous le contenu du projet : ' );
			if( isset($email2) )
				$email2->addParagraph( 'Veuillez trouver ci-dessous le contenu du projet : ' );
			break;
		case _STATE_REFUSED:
			$email->addParagraph( 'Veuillez trouver ci-dessous le contenu de votre panier : ' );
			if( isset($email2) )
				$email2->addParagraph( 'Veuillez trouver ci-dessous le contenu de votre panier : ' );
			break;
		case _STATE_PRJ_REFUSED:
			$email->addParagraph( 'Veuillez trouver ci-dessous le contenu de votre projet : ' );
			if( isset($email2) )
				$email2->addParagraph( 'Veuillez trouver ci-dessous le contenu de votre projet : ' );
			break;
		case _STATE_BL_STORE:
			$email->addParagraph( 'Veuillez trouver ci-dessous le détail des articles disponibles : ' );
			if( isset($email2) )
				$email2->addParagraph( 'Veuillez trouver ci-dessous le détail des articles disponibles : ' );
			break;
		// case _STATE_INV_STORE
		default:
			if( !in_array($r['pay_id'], array(_PAY_CHEQUE_X, _PAY_SOFINCO)) ){
				$email->addParagraph('Veuillez trouver ci-dessous le rappel du contenu de votre commande :');

				if( isset($email2) ){
					$email2->addParagraph('Veuillez trouver ci-dessous le rappel du contenu de votre commande :');
				}
			}
			break;
	}

	$show_ean = isset($config['show_barcode_in_order_notify']) && $config['show_barcode_in_order_notify'];

	// demande spé de boero si la commande n'est pas livrable sur 1 seul dépot alors afficher un message pour avertir
	if( $config["tnt_id"] == 2 ){
		global $main_dps;
		foreach( $main_dps as $dps ){
			$is_livrable[$dps] = true;
		}

		while( $prd = ria_mysql_fetch_assoc($products) ){
			foreach ( $main_dps as $dps ){
				$rstock = prd_dps_stocks_get($prd["id"], $dps);
				while( $stock = ria_mysql_fetch_assoc($rstock) ){
					if( $prd['qte'] > $stock['qte'] ){
						$is_livrable[$dps] = false;
					}
				}
			}
		}
		ria_mysql_data_seek($products, 0);

		$is_livrable_once = false;
		foreach( $main_dps as $dps ){
			if( $is_livrable[$dps] ){
				$is_livrable_once = true;
			}
		}

		if( !$is_livrable_once ){
			$email->addParagraph('Attention pour cette commande, vous serez livré en plusieurs fois.');
		}
	}

	// Tableau récapitulatif
	$email->openTable();
	$email->openTableRow();
	if($config['tnt_id'] == 29)
	{
		$email->addCell( 'Code Barre' );
		$email->addCell( 'Code EAN' );
	}
	$email->addCell( 'Ref' );
	$email->addCell( 'Désignation' );
	if( $show_ean ){
		$email->addCell( 'Code EAN' );
	}
	if( $usr['prf_id']==PRF_CUSTOMER ){
		$email->addCell( 'Prix TTC' );
		$email->addCell( 'Qté' );
		$email->addCell( 'Total TTC' );
	}else{
		$email->addCell( 'Prix HT' );
		$email->addCell( 'Qté' );
		$email->addCell( 'Total HT' );
	}
	$email->closeTableRow();
	if( isset($email2) ) {
		$email2->openTable();
		$email2->openTableRow();
		$email2->addCell( 'Ref' );
		$email2->addCell( 'Désignation' );

		if( $show_ean ){
			$email2->addCell( 'Code-barres' );
		}
		if( $view_price && $usr['prf_id']==PRF_CUSTOMER ){
			$email2->addCell( 'Prix TTC' );
			$email2->addCell( 'Qté' );
			$email2->addCell( 'Total TTC' );
		}elseif( $view_price ){
			$email2->addCell( 'Prix HT' );
			$email2->addCell( 'Qté' );
			$email2->addCell( 'Total HT' );
		}
		$email2->closeTableRow();
	}
	$ratio = isset( $config['weight_col_calc_lines'] ) && is_numeric( $config['weight_col_calc_lines'] ) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;
	$unit = 'Gr';
	if( $ratio==1000 )
		$unit = 'Kg';
	elseif( $ratio==100000 )
		$unit = 'Qt';
	elseif( $ratio==1000000 )
		$unit = 'Tn';

	$suffixe = $usr['prf_id']==PRF_CUSTOMER ? 'ttc' : 'ht';

	$port_ht = $port_ttc = 0;
	$source_port_name = '';
	$prd_calc_disc_array = array(); // tableau associatif produit / total TTC
	$reappro = false;
	$total_ecotaxe = isset($config['show_ecotaxe_in_order_notify']) && $config['show_ecotaxe_in_order_notify'] ? 0:null;

	if( $config['tnt_id'] == 29 ){
		require_once $config['site_dir'].'/include/view.product.inc.php';
		$total_available = $total_unavailable = 0;
	}

	while( $p = ria_mysql_fetch_array($products) ){
		if( prd_products_is_port($p['ref']) ){
			$port_ttc += $p['price_ttc'];
			$port_ht += $p['price_ht'];
			$source_port_name = $p['name'];
		}else{
			$prd_name = $p['name'];

			// Pour Boissinot, vérifie si le produit est en rupture.
			if( $config['tnt_id'] == 54 ){
				$r_product = prd_products_get_simple($p['id']);
				if( $r_product && ria_mysql_num_rows($r_product) ){
					$follow_stock = ria_mysql_result($r_product, 0, 'follow_stock');
					if( !$follow_stock || ($follow_stock && ria_mysql_result($r_product, 0, 'stock') >= $p['qte']) ){
						$products_in_stock++;
					}
				}

				$products_count++;
			}

			if( $config['tnt_id']==1 && !$r['piece'] && !$p['sell_weight'] && $p['col_id'] && $p['col_qte'] ){
				$rcol = prd_colisage_types_get( $p['col_id'] );
				if( $rcol && ria_mysql_num_rows($rcol) ){
					$prd_name .= ' ('.ria_mysql_result( $rcol, 0, 'name' ).')';
				}
			}

			$email->openTableRow();

			// Dev Spé pour Oz - International
			if($config['tnt_id'] == 29)
			{
				$barcode = prd_products_get_barcode( $p['id'] );
				$barcode = !$barcode ? '' : $barcode;
				
				$bc_image = 'barcode.png';
				$bc = new Barcode();

				$bc->setCode( (string) $barcode );
				$bc->setType('EAN');
				$bc->setSize(29, 115, 10);
				$bc->setText('');
				$bc->hideCodeType();
				$bc->setFiletype('PNG');
				@$bc->writeBarcodeFile($bc_image);

				ob_start (); 
				imagejpeg ($bc->IH);
				$image_data = ob_get_contents (); 
				ob_end_clean (); 

				$email->addCell("<img src=\"data:image/jpeg;charset=utf-8;base64,". base64_encode($image_data) ."\"/>");
				$email->addCell($barcode);
			}
			$email->addCell( $p['ref'], 'right' );
			if( isset($email2) ) {
				$email2->openTableRow();
				$email2->addCell( $p['ref'], 'right' );
			}
			if( isset($config['show_fields_in_notify']) && $config['show_fields_in_notify'] ){
				$prd_models = fld_models_get(0,$p['id'],CLS_PRODUCT);
				$html = '<p style="font-size: 0.9em;">';
				if( ria_mysql_num_rows($prd_models) ){
					while( $m = ria_mysql_fetch_array($prd_models) ){

						$fields = fld_fields_get(0,0,$m['id'],0,0,$p['id'],null,array(),false,array(),null,CLS_PRODUCT);
						while( $f = ria_mysql_fetch_array($fields) ){
							$html .= '<strong>'.$f['name'].'</strong> : '.$f['obj_value'].'<br/>';
						}
					}
				}
				$html .= '</p>';
				$email->addCell( $prd_name .'<br/>'.$html);
				if( isset($email2) ){
					$email2->addCell( $prd_name .'<br/>'.$html);
				}
			}else{
				$email->addCell( $prd_name );
				if( isset($email2) ){
					$email2->addCell( $prd_name );
				}
			}

			if( $config['tnt_id'] == 29 ){
				$stock_info = view_stock_info($p['id'], $p['qte']);
				if( strstr($stock_info, 'availability yes') ){
					$total_available += ($p['price_ht'] * $p['qte']);
				}else{
					$total_unavailable += ($p['price_ht'] * $p['qte']);
				}
			}

			if( $show_ean ){
				$barcode = prd_products_get_barcode( $p['id'] );
				$barcode = !$barcode ? '' : $barcode;
				$email->addCell( $barcode );
				if( isset($email2) ){
					$email2->addCell( $barcode );
				}
			}

			$qte_poids = null;
			if( $p['sell_weight'] && $p['weight_net_total'] && $config['show_price_in_weight_unit'] )
				$qte_poids = $p['weight_net_total'] / $ratio;

			// spécifique Bigship code promo vente privée
			if( $config['tnt_id'] == 1 && $r['pmt_id'] && !$r['piece'] ){
				$code_prom = ria_mysql_fetch_array( pmt_codes_get( $r['pmt_id'] ) );
				if( in_array( substr( strtoupper( $code_prom['code'] ), 0, 9 ), array('HEOBALISE', 'HEOSURVIE') ) ){
					$discount = $code_prom['discount'];
					$promo_single = fld_object_values_get( $p['id'], 2532, '', false, true );
					$promo_single = str_replace( array(',', ' '), array('.', ''), $promo_single );
					if( is_numeric($promo_single) && $promo_single <= 100 ){
						$discount = $promo_single;
					}
					$p['total_'.$suffixe] *= ( 100 - $discount ) / 100;
					$p['price_'.$suffixe] *= ( 100 - $discount ) / 100;
				}
			}
			$price = $p['price_'.$suffixe];
			if ($skip_port && $config['tnt_id'] == 17 && $config['wst_id'] == 40 && $majoration_admin) {
				$price = $price * 1.25;
			}
			if( $qte_poids!==null )
				$email->addCell( number_format($p['total_'.$suffixe] / $qte_poids,2,',',' '), 'right' );
			elseif( $r['piece'] && $p['sell_weight'] )
				$email->addCell( number_format($price * $ratio,2,',',' '), 'right' );
			elseif( $config['tnt_id']==1 && !$r['piece'] && !$p['sell_weight'] && $p['col_id'] && $p['col_qte'] )
				$email->addCell( number_format($price * $p['col_qte'],2,',',' '), 'right' );
			else
				$email->addCell( number_format($price,2,',',' '), 'right' );

			if( $view_price && isset($email2) ){
				if( $qte_poids!==null )
					$email2->addCell( number_format($p['total_'.$suffixe] / $qte_poids,2,',',' '), 'right' );
				elseif( $r['piece'] && $p['sell_weight'] )
					$email2->addCell( number_format($p['price_'.$suffixe] * $ratio,2,',',' '), 'right' );
				elseif( $config['tnt_id']==1 && !$r['piece'] && !$p['sell_weight'] && $p['col_id'] && $p['col_qte'] )
					$email2->addCell( number_format($p['price_'.$suffixe] * $p['col_qte'],2,',',' '), 'right' );
				else
					$email2->addCell( number_format($p['price_'.$suffixe],2,',',' '), 'right' );
				}

			if( $p['sell_weight'] ){
				if( $qte_poids!==null )
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($qte_poids,3,',',' ') ).'&nbsp;'.$unit, 'right' );
				elseif( !$r['piece'] && $p['col_id'] && $p['col_qte'] )
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'] * ($p['col_qte'] / $ratio),3,',',' ') ).'&nbsp;'.$unit, 'right' );
				elseif( !$r['piece'] && $p['weight_net_total'] )
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['weight_net_total'] / $ratio,3,',',' ') ).'&nbsp;'.$unit, 'right' );
				elseif( $r['piece'] )
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'] / $ratio,3,',',' ') ).'&nbsp;'.$unit, 'right' );
				else
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'],0,',',' ') ), 'right' );
				if( $view_price && isset($email2) ){
					if( $qte_poids!==null )
						$email2->addCell( str_replace( ' ', '&nbsp;', number_format($qte_poids,3,',',' ') ).'&nbsp;'.$unit, 'right' );
					elseif( !$r['piece'] && $p['col_id'] && $p['col_qte'] )
						$email2->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'] * ($p['col_qte'] / $ratio),3,',',' ') ).'&nbsp;'.$unit, 'right' );
					elseif( !$r['piece'] && $p['weight_net_total'] )
						$email2->addCell( str_replace( ' ', '&nbsp;', number_format($p['weight_net_total'] / $ratio,3,',',' ') ).'&nbsp;'.$unit, 'right' );
					elseif( $r['piece'] )
						$email2->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'] / $ratio,3,',',' ') ).'&nbsp;'.$unit, 'right' );
					else
						$email2->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'],0,',',' ') ), 'right' );
					}
			}else{
				$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'],0,',',' ') ), 'right' );
				if( $view_price && isset($email2) )
					$email2->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'],0,',',' ') ), 'right' );
				}
			$total = $p['total_'.$suffixe];
			$email->addCell( number_format($total,2,',',' '), 'right' );
			$email->closeTableRow();
			if( isset($email2) ){
				if( $view_price )
					$email2->addCell( number_format($total,2,',',' '), 'right' );
				$email2->closeTableRow();
			}

			// ajout de la ligne parcourue au tableau (il peut, théoriquement, y avoir plusieurs fois le même produit)
			if( array_key_exists( $p['id'], $prd_calc_disc_array ) ){
				$prd_calc_disc_array[$p['id']] += $p['total_ttc'];
			}else{
				$prd_calc_disc_array[$p['id']] = $p['total_ttc'];
			}

			// Ajout des notes
			if( !isset($config['notify_ord_prd_notes']) || $config['notify_ord_prd_notes'] ){
				if( (trim($p['notes']) && $p['notes']!=$r['ref']) || trim($p['date-livr']) ){
					$prd_more = '';
					if( trim($p['notes']) && $p['notes']!=$r['ref'] )
						$prd_more = 'Notes : '.htmlspecialchars($p['notes']);
					if( trim($p['date-livr']) ){
						if( $prd_more ) $prd_more .= ', ';
						if( $config['tnt_id'] == 3 ){
							$prd_more .= 'Livraison prévue pour le : '.$p['date-livr'];
						}else{
							$prd_more .= 'Livraison différée le : '.$p['date-livr'];
						}
					}
					$email->openTableRow();
					$email->addCell( $prd_more, 'left', 5 );
					$email->closeTableRow();
					if( isset($email2) ){
						$email2->openTableRow();
						$email2->addCell( $prd_more, 'left', 5 );
						$email2->closeTableRow();
					}
				}
			}

			//ajout du total de l'eco-taxe
			if( isset($config['show_ecotaxe_in_order_notify']) && $config['show_ecotaxe_in_order_notify'] ){
				$total_ecotaxe += $p['ecotaxe']*$p['qte'];
			}
		}
	}

	$colspan = 4;
	if( $show_ean )
		$colspan += 1;

	if( $config[ 'show_port_in_order_confirmation' ] ){
		// Exception Frais de port offerts
		$free_shipping = false;
		$order_promotions = ord_orders_promotions_get($r['id']);

		if( !empty($order_promotions) ){
			// calcule la remise
			$remise = $remise_reward = 0;

			$rem_texte = '';
			$rem_discount = 0;
			foreach( $order_promotions as $order_promo ){

				$pmt = ria_mysql_fetch_assoc(pmt_codes_get($order_promo['pmt_id']));
				if( $pmt['free_shipping'] ){
					$services = pmt_codes_get_services($pmt['parent'] ? $pmt['parent'] : $pmt['id']);
					if( !$r['str_id'] && !$r['srv_id'] ){
						$free_shipping = true;
					}elseif( in_array($r['srv_id'], $services) ){
						$free_shipping = true;
					}elseif( $r['str_id'] && in_array(-1, $services) ){
						$free_shipping = true;
					}
				}

				// Affichage code promotion
				$email->openTableRow();
				$email->addCell( 'Code promotion :', 'right', $colspan );
				$email->addCell( $pmt['code'], 'right', 1, true );
				$email->closeTableRow();
				if( isset($email2) ){
					$email2->openTableRow();
					$email2->addCell( 'Code promotion :', 'right', $colspan );
					$email2->addCell( $pmt['code'], 'right', 1, true );
					$email2->closeTableRow();
				}
				// Affichage du montant de la promotion [TTC], si activé
				if( !$config['show_notify_code_discount'] ){
					continue;
				}

				if( $pmt['discount_type']==0 ){
					$remise += $pmt['discount']; // remise en montant
				}else{
					// remise en pourcentage
					// on calcule d'abord le montant brut sur lequel la remise s'applique
					$total_brut_ttc = 0;
					foreach( $prd_calc_disc_array as $pid=>$totttc ){
						if( pmt_products_is_included( $pmt, $pid ) ){
							$total_brut_ttc += $totttc;
						}
					}
					$remise += $total_brut_ttc * ($pmt['discount'] / 100);
				}
				if( $pmt['discount_type']==1 ){
					$rem_discount += $pmt['discount'];
				}
			}

			$remise_reward = fld_object_values_get( $r['id'], _FLD_ORD_RWD_OPT4_HT );

			if ($remise) {
				$rem_texte = number_format($remise,2,',',' ');

				if( $rem_discount ){
					$rem_texte = number_format($rem_discount,2,',',' ').' %, soit '.$rem_texte; // remise en pourcentage, le texte change légèrement
				}

				$email->openTableRow();
				$email->addCell( 'Remise TTC :', 'right', $colspan );
				$email->addCell( '&nbsp;'.$rem_texte, 'right', 1, true );
				$email->closeTableRow();
			}

			if ($remise_reward) {
				$email->openTableRow();
				$email->addCell( 'Remise Points de fidélité :', 'right', $colspan );
				$email->addCell( number_format( ($remise_reward * _TVA_RATE_DEFAULT), 2, ',', ' '), 'right', 1, true );
				$email->closeTableRow();
			}

			$email->openTableRow();
			$email->addCell( 'Total net TTC :', 'right', $colspan );
			$email->addCell( number_format($r['total_ttc']-($free_shipping ? 0 : $port_ttc),2,',',' '), 'right', 1, true );
			$email->closeTableRow();

			if( $view_price && isset($email2) ){
				$email2->openTableRow();
				$email2->addCell( 'Remise TTC :', 'right', $colspan );
				$email2->addCell( '&nbsp;'.$rem_texte, 'right', 1, true );
				$email2->closeTableRow();

				$email2->openTableRow();
				$email2->addCell( 'Total net TTC :', 'right', $colspan );
				$email2->addCell( number_format($r['total_ttc']-($free_shipping ? 0 : $port_ttc),2,',',' '), 'right', 1, true );
				$email2->closeTableRow();
			}
		}

		$name_port = 'Frais de port';
		if( $config['show_original_port_name'] && trim($source_port_name)!='' )
			$name_port = $source_port_name;

		// Frais de port
		$email->openTableRow();
		$email->addCell( $name_port.' :', 'right', $colspan );
		if( $free_shipping )
			$email->addCell( 'Offerts', 'right', 1, true );
		else
			$email->addCell( number_format($usr['prf_id']==PRF_CUSTOMER ? $port_ttc : $port_ht,2,',',' '), 'right', 1, true );
		$email->closeTableRow();
		if( $view_price && isset($email2) ){
			$email2->openTableRow();
			$email2->addCell( $name_port.' :', 'right', $colspan );
			if( $free_shipping )
				$email2->addCell( 'Offerts', 'right', 1, true );
			else
				$email2->addCell( number_format($usr['prf_id']==PRF_CUSTOMER ? $port_ttc : $port_ht,2,',',' '), 'right', 1, true );
			$email2->closeTableRow();
		}
	}

	if( $usr['prf_id']!=PRF_CUSTOMER ){
		// Total HT
		if( $config['tnt_id'] == 29 ){
			$email->openTableRow();
			$email->addCell( 'Total ht produits disponible :', 'right', $colspan );
			$email->addCell( number_format($total_available,2,',',' '), 'right', 1, true );
			$email->closeTableRow();

			$email->openTableRow();
			$email->addCell( 'Total ht produits indisponible :', 'right', $colspan );
			$email->addCell( number_format($total_unavailable,2,',',' '), 'right', 1, true );
			$email->closeTableRow();
		}
		$email->openTableRow();
		$email->addCell( 'Total HT :', 'right', $colspan );
		$email->addCell( number_format($r['total_ht'],2,',',' '), 'right', 1, true );
		$email->closeTableRow();
		if( $view_price && isset($email2) ){
			$email2->openTableRow();
			$email2->addCell( 'Total HT :', 'right', $colspan );
			$email2->addCell( number_format($r['total_ht'],2,',',' '), 'right', 1, true );
			$email2->closeTableRow();
		}

		//Eco-taxe
		if(isset($config['show_ecotaxe_in_order_notify'], $total_ecotaxe) && $config['show_ecotaxe_in_order_notify']){
			$email->openTableRow();
			$email->addCell( 'Eco-participation', 'right', $colspan);
			$email->addCell(number_format($total_ecotaxe,2,',',' '),'right',1,true);
			$email->closeTableRow();
		}

		// Tva
		$email->openTableRow();
		$email->addCell( 'TVA :', 'right', $colspan );
		$email->addCell( number_format($r['total_ttc']-$r['total_ht'],2,',',' '), 'right', 1, true );
		$email->closeTableRow();
		if( $view_price && isset($email2) ){
			$email2->openTableRow();
			$email2->addCell( 'TVA :', 'right', $colspan );
			$email2->addCell( number_format($r['total_ttc']-$r['total_ht'],2,',',' '), 'right', 1, true );
			$email2->closeTableRow();
		}
	}

	if( $config['tnt_id'] == 23 ){
		// acompte versé
		$email->openTableRow();
		$email->addCell( 'Acompte :', 'right', $colspan );
		$email->addCell( number_format(ord_orders_get_total_to_pay($r['id'], true),2,',',' '), 'right', 1, true );
		$email->closeTableRow();
	}

	// Total TTC
	$email->openTableRow();
	$email->addCell( 'Total TTC :', 'right', $colspan );
	$email->addCell( number_format($r['total_ttc'],2,',',' '), 'right', 1, true );
	$email->closeTableRow();
	$email->closeTable();
	$email->addBlankTextLine();
	if( isset($email2) ){
		if( $view_price ){
			$email2->openTableRow();
			$email2->addCell( 'Total TTC :', 'right', $colspan );
			$email2->addCell( number_format($r['total_ttc'],2,',',' '), 'right', 1, true );
			$email2->closeTableRow();
		}
		$email2->closeTable();
		$email2->addBlankTextLine();
	}

	if( $config['tnt_id'] == 15 ){
		$email->addParagraph('Le transporteur prendra contact avec vous dans un délai compris entre 2 et 4 jours afin de convenir de la livraison. <a href="http://www.somelys.fr/conditions-generales-de-vente/#Article6" target="_blank">En savoir plus sur la livraison</a>');
		$email->addBlankTextLine();
	}

	if( $config['tnt_id'] == 29 ){
		$email->addParagraph('FRAIS DE PORT : les frais de port s’appliquent sur le total HT des produits disponibles');
		$email->addBlankTextLine();
	}

	if( !in_array($r['state_id'], array(_STATE_PRJ_WAIT_VALIDATION,_STATE_PRJ_REFUSED,_STATE_WAIT_VALIDATION, _STATE_REFUSED)) ){
		// Numéro de pièce interne
		if ($config['tnt_id'] == 4 && $r['wst_id'] == 8) {
			$is_ldd = fld_object_values_get($r['id'], 496, '', false, true);
			if ($is_ldd == 'Non') {
				$email->addBlankTextLine();
				$email->addHtml('<p><b>Attention, si la livraison de cette commande se fait hors franco de port, un montant de frais de frais de port sera ajouté à la commande.</b></p>');

				if( isset($email2) ){
					$email2->addBlankTextLine();
					$email->addHtml('<p><b>Attention, si la livraison de cette commande se fait hors franco de port, un montant de frais de frais de port sera ajouté à la commande.</b></p>');
				}
			}
		}

		$email->addBlankTextLine();

		if( in_array($config['tnt_id'], [588, 1279]) && isset($berton_usr) && isset($berton_usr['ref']) && $berton_usr['ref'] != '' ){
			$temp_ref = $usr['ref'];
			$usr['ref'] = $berton_usr['ref'];
		}
		$email->addParagraph(
			'Votre commande porte le numéro '.$r['id'].(isset($wst['type_id']) && $wst['type_id'] != _WST_TYPE_FDV ? ' dans notre boutique en ligne' : '').'.'."\n"
			.( $r['piece'] && $config['tnt_id']!=16 ? 'Cette alerte email porte le numéro de pièce '.$r['piece'].' dans notre gestion commerciale.'."\n" : '' )
			.( $usr['ref'] && $config['tnt_id']!=54 ? 'Votre compte client porte le numéro '. $usr['ref'] .'.'."\n" : '' )
			.' Merci d\'y faire référence si vous souhaitez nous interroger sur le contenu de cet email.'
		);
		if( isset($email2) ){
			$email2->addBlankTextLine();
			$email2->addParagraph(
				'Votre commande porte le numéro '.$r['id'].(isset($wst['type_id']) && $wst['type_id'] != _WST_TYPE_FDV ? ' dans notre boutique en ligne' : '').'.'."\n"
				.( $r['piece'] && $config['tnt_id']!=16 ? 'Cette alerte email porte le numéro de pièce '.$r['piece'].' dans notre gestion commerciale.'."\n" : '' )
				.( $usr['ref'] && $config['tnt_id']!=54 ? 'Votre compte client porte le numéro '.$usr['ref'].'.'."\n" : '' )
				.' Merci d\'y faire référence si vous souhaitez nous interroger sur le contenu de cet email.'
			);
		}
		if( isset($temp_ref) ){
			$usr['ref'] = $temp_ref;
		}

		if( isset($config['notify_ord_childs']) && $config['notify_ord_childs'] ){
			$limit_show = 1;
			$childs = true;

			if( $config['tnt_id'] == 26 ){
				if( !isset($r_ochild) ){
					$r_ochild = ord_orders_get_childs($r['id'] );
				}

				if( !$r_ochild || !ria_mysql_num_rows($r_ochild) ){
					$r_ochild = ord_orders_get( $r['user'], $r['id'] );
					$limit_show = 0;
					$childs = false;
				}

				if(isset($ochild)){
					ria_mysql_data_seek($r_ochild, 0);
					$limit_show = 0;
				}
			}

			if( $r_ochild && ria_mysql_num_rows($r_ochild) > $limit_show ){

				$cpt=0; $html = '';
				while( $ochild = ria_mysql_fetch_assoc($r_ochild) ){
					$cpt++;

					if( $childs ){
						$roprd = ord_products_get_from_child($order, $ochild['id']);
					}else{
						$roprd = ord_products_get($ochild['id']);
					}

					$ar_prds = array();
					if( $roprd ){
						while( $oprd = ria_mysql_fetch_assoc($roprd) ){
							if( !prd_products_is_port($oprd['ref']) ){
								$ar_prds[] = $oprd;
							}
						}
					}

					$email->addBlankTextLine();
					$email->addHTML('<p><b>Colis n°'.$cpt.'</b><br /><i>Rèf. '.$ochild['id'].'</i></p>');

					$srv = false;
					$rsrv = dlv_services_get($ochild['srv_id']);
					if( $rsrv && ria_mysql_num_rows($rsrv) ){
						$srv = ria_mysql_fetch_assoc($rsrv);

						if( !isset($ochild['ord_livr']) || trim($ochild['ord_livr']) == '' ){
							$ochild['ord_livr'] = $ochild['date_livr_en'];
						}

						$date_livr = date( 'd/m/Y', strtotime($ochild['ord_livr']) );

						$email->addParagraph('Les produits ci-dessous vous seront livrés par '.( $srv ? htmlspecialchars( $srv['name'] ) : 'Inconnue' ).' à partir du '.$date_livr.' : ');

						$html = '
								<ul>
						';

						if( $roprd && ria_mysql_num_rows($roprd) ){
							ria_mysql_data_seek( $roprd, 0 );
							while( $oprd = ria_mysql_fetch_assoc($roprd) ){
								if( !prd_products_is_port($oprd['ref']) ){
									$html .= '
										<li>'.$oprd['name'].'</li>
									';
								}
							}
						}

						$html .= '
								</ul>
						';

						if(!$adr_given){
							if( $ochild['str_id'] ){
								$adresse = '';

								$rstr = dlv_stores_get( $ochild['str_id'] );
								if( $rstr && ria_mysql_num_rows($rstr) ){
									$str = ria_mysql_fetch_assoc( $rstr );

									if( $str['address1'] ){
										$adresse .= htmlspecialchars( $str['address1'] );
									}

									if( $str['address2'] ){
										$adresse .= ' '.htmlspecialchars( $str['address2'] );
									}

									$adresse .= ' '.htmlspecialchars( $str['zipcode'].' '.$str['city'] );
								}

								$html .= '
									<p>À notre relais magasin : '.$adresse.'</p>
								';
							}elseif( $ochild['rly_id'] ){
								$adresse = '';

								$rrly = dlv_relays_get( $ochild['rly_id'] );
								if( $rrly && ria_mysql_num_rows($rrly) ){
									$rly = ria_mysql_fetch_assoc( $rrly );

									$adresse .= htmlspecialchars( $rly['name'] );

									if( $rly['address1'] ){
										$adresse .= ' '.htmlspecialchars( $rly['address1'] );
									}

									if( $rly['address2'] ){
										$adresse .= ' '.htmlspecialchars( $rly['address2'] );
									}

									$adresse .= ' '.htmlspecialchars( $rly['zipcode'].' '.$rly['city'].' '.$rly['country'] );
								}

								$html .= '
									<p>Au point relais : '.$adresse.'</p>
								';
							}else{
								$adresse = '';

								$radresse = gu_adresses_get( $r['user'], $ochild['adr_delivery'] );
								if( $radresse && ria_mysql_num_rows($radresse) ){
									while( $adr = ria_mysql_fetch_assoc($radresse) ){
										if( $adr['firstname'] || $adr['lastname'] ){
											$adresse .= '
												<span class="name">
													<span>'.htmlspecialchars( $adr['firstname'].' '.$adr['lastname'] ).'</span>
											';
										}

										$adresse .= '
													<span>'.htmlspecialchars( $adr['address1'].' '.$adr['address2'].' '.$adr['zipcode'].' '.$adr['city'].' '.$adr['country'] ).'</span>
												</span>
										';
									}

								}
								$html .= '
									<p>À cette adresse : '.$adresse.'</p>
								';
							}
						}
					}

					$email->addHtml( $html );
				}
			}
		}

		if( $r['state_id']==_STATE_INVOICE && $config['tnt_id']!=5 ){
			$invoices = ord_ord_invoices_get($r['id']);
			if( ria_mysql_num_rows($invoices)==1 ){
				$inv = ria_mysql_fetch_array($invoices);
				$email->addParagraph('La facture correspondante est disponible dans votre espace client. Elle porte le numéro de pièce <a href="'.$config['site_url'].'/mon-compte/facture?inv='.$inv['id'].'">'.$inv['piece'].'</a>');
				if( isset($email2) ){
					$email2->addParagraph('La facture correspondante est disponible dans votre espace client. Elle porte le numéro de pièce <a href="'.$config['site_url'].'/mon-compte/facture?inv='.$inv['id'].'">'.$inv['piece'].'</a>');
				}
			}elseif( ria_mysql_num_rows($invoices)>1 ){
				$ar_invoices = array();
				while( $inv = ria_mysql_fetch_array($invoices) ){
					$ar_invoices[] = '<a href="'.$config['site_url'].'/mon-compte/facture?inv='.$inv['id'].'">'.$inv['piece'].'</a>';
				}
				$email->addParagraph('Les factures établies pour cette commande sont disponibles dans votre espace client. Elles portent les numéros de pièce : '.implode(', ',$ar_invoices));
				if( isset($email2) )
					$email2->addParagraph('Les factures établies pour cette commande sont disponibles dans votre espace client. Elles portent les numéros de pièce : '.implode(', ',$ar_invoices));
			}
		}

		if( $config['site_have_user_histo'] && $config['tnt_id']!=3 && $config['wst_id']!=6 ){
			// Rappel suivi possible
			$email->addBlankTextLine();
			$email->addParagraph( 'Vous pouvez également suivre l\'avancement de votre commande depuis votre espace client à l\'adresse suivante : <a href="'.$config['site_url'].'/mon-compte/commandes">'.$config['site_url'].'/mon-compte/commandes</a>.');
			if( isset($email2) ){
				$email2->addBlankTextLine();
				$email2->addParagraph( 'Vous pouvez également suivre l\'avancement de votre commande depuis votre espace client à l\'adresse suivante : <a href="'.$config['site_url'].'/mon-compte/commandes">'.$config['site_url'].'/mon-compte/commandes</a>.');
			}
		}

		$email->addBlankTextLine();
		$email->addParagraph(
			"Cordialement,\n".
			"L'équipe ".$config['site_name'].'.'
		);
		if( isset($email2) ){
			$email2->addBlankTextLine();
			$email2->addParagraph(
				"Cordialement,\n".
				"L'équipe ".$config['site_name'].'.'
			);
		}

		// Rappel configuration possible
		if( $config['site_have_user_space'] && $config['tnt_id']!=3 && $config['wst_id']!=6 ){
			$email->addHorizontalRule();
			$txt_options = 'Mes options';
			$email->addParagraph('Cet email vous a été envoyé pour vous tenir informé(e) de l\'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction \''.$txt_options.'\' de votre espace client : <a href="'.$config['site_url'].'/mon-compte/mes-options">'.$config['site_url'].'/mon-compte/mes-options</a>.');
			if( isset($email2) ){
				$email2->addHorizontalRule();
				$email2->addParagraph('Cet email vous a été envoyé pour vous tenir informé(e) de l\'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction \''.$txt_options.'\' de votre espace client : <a href="'.$config['site_url'].'/mon-compte/mes-options">'.$config['site_url'].'/mon-compte/mes-options</a>.');
				$email2->addBlankTextLine();
			}
			$email->addBlankTextLine();
		}

		// Rappel contact possible
		$email->addHorizontalRule();
		$email->addBlankTextLine();
		if ($config['tnt_id'] == 89){
			$email->addParagraph('Nous sommes en permanence à l\'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n\'hésitez pas à nous contacter : <a href= "mailto:<EMAIL>"><EMAIL></a>');
		}else{
			$contact_url = $config['site_url'].$config['contact_page_url'];
			if(isset($wst['type_id']) && $wst['type_id'] == _WST_TYPE_FDV) {
				$contact_url = $config['contact_page_url'];
			}
			$email->addParagraph('Nous sommes en permanence à l\'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n\'hésitez pas à nous contacter : <a href="'.$contact_url.'">'.$contact_url.'</a>');
		}
		if( isset($email2) ){
			$email2->addHorizontalRule();
			$email2->addBlankTextLine();
			$email2->addParagraph('Nous sommes en permanence à l\'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n\'hésitez pas à nous contacter : <a href="'.$contact_url.'">'.$contact_url.'</a>');
		}
	} elseif( in_array($r['state_id'], array(_STATE_PRJ_WAIT_VALIDATION,_STATE_PRJ_REFUSED,_STATE_WAIT_VALIDATION,_STATE_REFUSED)) ) {
		if( $config['tnt_id']==8 ){
			// récupération de l'URL de l'extranet (nous sommes dans la configuration du site public)
			$site_url = ria_mysql_fetch_array( cfg_overrides_get( 22, array(), 'site_url' ) );
			$config['site_url'] = $site_url['value'];
		}

		if( $r['state_id']!=_STATE_REFUSED ){
			$email->addParagraph('Vous pouvez valider ou refuser le panier en suivant ce lien : <a href="'.$config['site_url'].'/valider-commandes/?ord='.$r['id'].'">Validation du panier n°: '.$r['id'].'</a>');
			$email->addBlankTextLine();
			if( isset($email2) ){
				$email2->addParagraph('Vous pouvez valider ou refuser le panier en suivant ce lien : <a href="'.$config['site_url'].'/valider-commandes/?ord='.$r['id'].'">Validation du panier n°: '.$r['id'].'</a>');
				$email2->addBlankTextLine();
			}
		}

		$email->addParagraph(
			"Cordialement,\n".
			"L'équipe ".$config['site_name'].'.'
		);
		if( isset($email2) ){
			$email2->addParagraph(
				"Cordialement,\n".
				"L'équipe ".$config['site_name'].'.'
			);
		}
	}

	if( in_array($r['state_id'], array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM, _STATE_CLICK_N_COLLECT)) && $config['notify_ord_docs'] ){
		foreach($config['notify_ord_docs'] as $doc_id) {
			$documents = doc_documents_get($doc_id);
			if( $documents && ria_mysql_num_rows($documents) ){
				$doc = ria_mysql_fetch_assoc($documents);
				$email->addAttachment( $config['doc_dir'].'/'.$doc['id'], $doc['filename'] );
			}

		}
	}

	$email->addHtml( $config['email_html_footer'] );
	if( isset($email2) )
		$email2->addHtml( $config['email_html_footer'] );

	if( isset($email2) )
		$email2->send();

	$email_send = $email->send();

	if( isset($email_has_attachment, $tmpname) && $email_has_attachment ){
		@unlink($tmpname);
	}

	$config = $config_copy;
	return $email_send;

}
// \endcond

