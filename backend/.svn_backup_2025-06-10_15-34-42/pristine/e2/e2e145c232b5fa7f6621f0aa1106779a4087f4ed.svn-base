<?php
$attributemap = [
    'urn:mace:dir:attribute-def:aRecord'                          => 'aRecord',
    'urn:mace:dir:attribute-def:aliasedEntryName'                 => 'aliasedEntryName',
    'urn:mace:dir:attribute-def:aliasedObjectName'                => 'aliasedObjectName',
    'urn:mace:dir:attribute-def:associatedDomain'                 => 'associatedDomain',
    'urn:mace:dir:attribute-def:associatedName'                   => 'associatedName',
    'urn:mace:dir:attribute-def:audio'                            => 'audio',
    'urn:mace:dir:attribute-def:authorityRevocationList'          => 'authorityRevocationList',
    'urn:mace:dir:attribute-def:buildingName'                     => 'buildingName',
    'urn:mace:dir:attribute-def:businessCategory'                 => 'businessCategory',
    'urn:mace:dir:attribute-def:c'                                => 'c',
    'urn:mace:dir:attribute-def:cACertificate'                    => 'cACertificate',
    'urn:mace:dir:attribute-def:cNAMERecord'                      => 'cNAMERecord',
    'urn:mace:dir:attribute-def:carLicense'                       => 'carLicense',
    'urn:mace:dir:attribute-def:certificateRevocationList'        => 'certificateRevocationList',
    'urn:mace:dir:attribute-def:cn'                               => 'cn',
    'urn:mace:dir:attribute-def:co'                               => 'co',
    'urn:mace:dir:attribute-def:commonName'                       => 'commonName',
    'urn:mace:dir:attribute-def:countryName'                      => 'countryName',
    'urn:mace:dir:attribute-def:crossCertificatePair'             => 'crossCertificatePair',
    'urn:mace:dir:attribute-def:dITRedirect'                      => 'dITRedirect',
    'urn:mace:dir:attribute-def:dSAQuality'                       => 'dSAQuality',
    'urn:mace:dir:attribute-def:dc'                               => 'dc',
    'urn:mace:dir:attribute-def:deltaRevocationList'              => 'deltaRevocationList',
    'urn:mace:dir:attribute-def:departmentNumber'                 => 'departmentNumber',
    'urn:mace:dir:attribute-def:description'                      => 'description',
    'urn:mace:dir:attribute-def:destinationIndicator'             => 'destinationIndicator',
    'urn:mace:dir:attribute-def:displayName'                      => 'displayName',
    'urn:mace:dir:attribute-def:distinguishedName'                => 'distinguishedName',
    'urn:mace:dir:attribute-def:dmdName'                          => 'dmdName',
    'urn:mace:dir:attribute-def:dnQualifier'                      => 'dnQualifier',
    'urn:mace:dir:attribute-def:documentAuthor'                   => 'documentAuthor',
    'urn:mace:dir:attribute-def:documentIdentifier'               => 'documentIdentifier',
    'urn:mace:dir:attribute-def:documentLocation'                 => 'documentLocation',
    'urn:mace:dir:attribute-def:documentPublisher'                => 'documentPublisher',
    'urn:mace:dir:attribute-def:documentTitle'                    => 'documentTitle',
    'urn:mace:dir:attribute-def:documentVersion'                  => 'documentVersion',
    'urn:mace:dir:attribute-def:domainComponent'                  => 'domainComponent',
    'urn:mace:dir:attribute-def:drink'                            => 'drink',
    'urn:mace:dir:attribute-def:eduOrgHomePageURI'                => 'eduOrgHomePageURI',
    'urn:mace:dir:attribute-def:eduOrgIdentityAuthNPolicyURI'     => 'eduOrgIdentityAuthNPolicyURI',
    'urn:mace:dir:attribute-def:eduOrgLegalName'                  => 'eduOrgLegalName',
    'urn:mace:dir:attribute-def:eduOrgSuperiorURI'                => 'eduOrgSuperiorURI',
    'urn:mace:dir:attribute-def:eduOrgWhitePagesURI'              => 'eduOrgWhitePagesURI',
    'urn:mace:dir:attribute-def:eduPersonAffiliation'             => 'eduPersonAffiliation',
    'urn:mace:dir:attribute-def:eduPersonAssurance'               => 'eduPersonAssurance',
    'urn:mace:dir:attribute-def:eduPersonEntitlement'             => 'eduPersonEntitlement',
    'urn:mace:dir:attribute-def:eduPersonNickname'                => 'eduPersonNickname',
    'urn:mace:dir:attribute-def:eduPersonOrgDN'                   => 'eduPersonOrgDN',
    'urn:mace:dir:attribute-def:eduPersonOrgUnitDN'               => 'eduPersonOrgUnitDN',
    'urn:mace:dir:attribute-def:eduPersonPrimaryAffiliation'      => 'eduPersonPrimaryAffiliation',
    'urn:mace:dir:attribute-def:eduPersonPrimaryOrgUnitDN'        => 'eduPersonPrimaryOrgUnitDN',
    'urn:mace:dir:attribute-def:eduPersonPrincipalName'           => 'eduPersonPrincipalName',
    'urn:mace:dir:attribute-def:eduPersonScopedAffiliation'       => 'eduPersonScopedAffiliation',
    'urn:mace:dir:attribute-def:eduPersonTargetedID'              => 'eduPersonTargetedID',
    'urn:mace:dir:attribute-def:eduPersonUniqueId'                => 'eduPersonUniqueId',
    'urn:mace:dir:attribute-def:email'                            => 'email',
    'urn:mace:dir:attribute-def:emailAddress'                     => 'emailAddress',
    'urn:mace:dir:attribute-def:employeeNumber'                   => 'employeeNumber',
    'urn:mace:dir:attribute-def:employeeType'                     => 'employeeType',
    'urn:mace:dir:attribute-def:enhancedSearchGuide'              => 'enhancedSearchGuide',
    'urn:mace:dir:attribute-def:facsimileTelephoneNumber'         => 'facsimileTelephoneNumber',
    'urn:mace:dir:attribute-def:favouriteDrink'                   => 'favouriteDrink',
    'urn:mace:dir:attribute-def:fax'                              => 'fax',
    'urn:mace:dir:attribute-def:federationFeideSchemaVersion'     => 'federationFeideSchemaVersion',
    'urn:mace:dir:attribute-def:friendlyCountryName'              => 'friendlyCountryName',
    'urn:mace:dir:attribute-def:generationQualifier'              => 'generationQualifier',
    'urn:mace:dir:attribute-def:givenName'                        => 'givenName',
    'urn:mace:dir:attribute-def:gn'                               => 'gn',
    'urn:mace:dir:attribute-def:homePhone'                        => 'homePhone',
    'urn:mace:dir:attribute-def:homePostalAddress'                => 'homePostalAddress',
    'urn:mace:dir:attribute-def:homeTelephoneNumber'              => 'homeTelephoneNumber',
    'urn:mace:dir:attribute-def:host'                             => 'host',
    'urn:mace:dir:attribute-def:houseIdentifier'                  => 'houseIdentifier',
    'urn:mace:dir:attribute-def:info'                             => 'info',
    'urn:mace:dir:attribute-def:initials'                         => 'initials',
    'urn:mace:dir:attribute-def:internationaliSDNNumber'          => 'internationaliSDNNumber',
    'urn:mace:dir:attribute-def:janetMailbox'                     => 'janetMailbox',
    'urn:mace:dir:attribute-def:jpegPhoto'                        => 'jpegPhoto',
    'urn:mace:dir:attribute-def:knowledgeInformation'             => 'knowledgeInformation',
    'urn:mace:dir:attribute-def:l'                                => 'l',
    'urn:mace:dir:attribute-def:labeledURI'                       => 'labeledURI',
    'urn:mace:dir:attribute-def:localityName'                     => 'localityName',
    'urn:mace:dir:attribute-def:mDRecord'                         => 'mDRecord',
    'urn:mace:dir:attribute-def:mXRecord'                         => 'mXRecord',
    'urn:mace:dir:attribute-def:mail'                             => 'mail',
    'urn:mace:dir:attribute-def:mailPreferenceOption'             => 'mailPreferenceOption',
    'urn:mace:dir:attribute-def:manager'                          => 'manager',
    'urn:mace:dir:attribute-def:member'                           => 'member',
    'urn:mace:dir:attribute-def:mobile'                           => 'mobile',
    'urn:mace:dir:attribute-def:mobileTelephoneNumber'            => 'mobileTelephoneNumber',
    'urn:mace:dir:attribute-def:nSRecord'                         => 'nSRecord',
    'urn:mace:dir:attribute-def:name'                             => 'name',
    'urn:mace:dir:attribute-def:norEduOrgAcronym'                 => 'norEduOrgAcronym',
    'urn:mace:dir:attribute-def:norEduOrgNIN'                     => 'norEduOrgNIN',
    'urn:mace:dir:attribute-def:norEduOrgSchemaVersion'           => 'norEduOrgSchemaVersion',
    'urn:mace:dir:attribute-def:norEduOrgUniqueIdentifier'        => 'norEduOrgUniqueIdentifier',
    'urn:mace:dir:attribute-def:norEduOrgUniqueNumber'            => 'norEduOrgUniqueNumber',
    'urn:mace:dir:attribute-def:norEduOrgUnitUniqueIdentifier'    => 'norEduOrgUnitUniqueIdentifier',
    'urn:mace:dir:attribute-def:norEduOrgUnitUniqueNumber'        => 'norEduOrgUnitUniqueNumber',
    'urn:mace:dir:attribute-def:norEduPersonBirthDate'            => 'norEduPersonBirthDate',
    'urn:mace:dir:attribute-def:norEduPersonLIN'                  => 'norEduPersonLIN',
    'urn:mace:dir:attribute-def:norEduPersonNIN'                  => 'norEduPersonNIN',
    'urn:mace:dir:attribute-def:o'                                => 'o',
    'urn:mace:dir:attribute-def:objectClass'                      => 'objectClass',
    'urn:mace:dir:attribute-def:organizationName'                 => 'organizationName',
    'urn:mace:dir:attribute-def:organizationalStatus'             => 'organizationalStatus',
    'urn:mace:dir:attribute-def:organizationalUnitName'           => 'organizationalUnitName',
    'urn:mace:dir:attribute-def:otherMailbox'                     => 'otherMailbox',
    'urn:mace:dir:attribute-def:ou'                               => 'ou',
    'urn:mace:dir:attribute-def:owner'                            => 'owner',
    'urn:mace:dir:attribute-def:pager'                            => 'pager',
    'urn:mace:dir:attribute-def:pagerTelephoneNumber'             => 'pagerTelephoneNumber',
    'urn:mace:dir:attribute-def:personalSignature'                => 'personalSignature',
    'urn:mace:dir:attribute-def:personalTitle'                    => 'personalTitle',
    'urn:mace:dir:attribute-def:photo'                            => 'photo',
    'urn:mace:dir:attribute-def:physicalDeliveryOfficeName'       => 'physicalDeliveryOfficeName',
    'urn:mace:dir:attribute-def:pkcs9email'                       => 'pkcs9email',
    'urn:mace:dir:attribute-def:postOfficeBox'                    => 'postOfficeBox',
    'urn:mace:dir:attribute-def:postalAddress'                    => 'postalAddress',
    'urn:mace:dir:attribute-def:postalCode'                       => 'postalCode',
    'urn:mace:dir:attribute-def:preferredDeliveryMethod'          => 'preferredDeliveryMethod',
    'urn:mace:dir:attribute-def:preferredLanguage'                => 'preferredLanguage',
    'urn:mace:dir:attribute-def:presentationAddress'              => 'presentationAddress',
    'urn:mace:dir:attribute-def:protocolInformation'              => 'protocolInformation',
    'urn:mace:dir:attribute-def:pseudonym'                        => 'pseudonym',
    'urn:mace:dir:attribute-def:registeredAddress'                => 'registeredAddress',
    'urn:mace:dir:attribute-def:rfc822Mailbox'                    => 'rfc822Mailbox',
    'urn:mace:dir:attribute-def:roleOccupant'                     => 'roleOccupant',
    'urn:mace:dir:attribute-def:roomNumber'                       => 'roomNumber',
    'urn:mace:dir:attribute-def:sOARecord'                        => 'sOARecord',
    'urn:mace:dir:attribute-def:schacGender'                      => 'schacGender',
    'urn:mace:dir:attribute-def:searchGuide'                      => 'searchGuide',
    'urn:mace:dir:attribute-def:secretary'                        => 'secretary',
    'urn:mace:dir:attribute-def:seeAlso'                          => 'seeAlso',
    'urn:mace:dir:attribute-def:serialNumber'                     => 'serialNumber',
    'urn:mace:dir:attribute-def:singleLevelQuality'               => 'singleLevelQuality',
    'urn:mace:dir:attribute-def:sisSchoolGrade'                   => 'sisSchoolGrade',
    'urn:mace:dir:attribute-def:sisLegalGuardianFor'              => 'sisLegalGuardianFor',
    'urn:mace:dir:attribute-def:sn'                               => 'sn',
    'urn:mace:dir:attribute-def:st'                               => 'st',
    'urn:mace:dir:attribute-def:stateOrProvinceName'              => 'stateOrProvinceName',
    'urn:mace:dir:attribute-def:street'                           => 'street',
    'urn:mace:dir:attribute-def:streetAddress'                    => 'streetAddress',
    'urn:mace:dir:attribute-def:subtreeMaximumQuality'            => 'subtreeMaximumQuality',
    'urn:mace:dir:attribute-def:subtreeMinimumQuality'            => 'subtreeMinimumQuality',
    'urn:mace:dir:attribute-def:supportedAlgorithms'              => 'supportedAlgorithms',
    'urn:mace:dir:attribute-def:supportedApplicationContext'      => 'supportedApplicationContext',
    'urn:mace:dir:attribute-def:surname'                          => 'surname',
    'urn:mace:dir:attribute-def:telephoneNumber'                  => 'telephoneNumber',
    'urn:mace:dir:attribute-def:teletexTerminalIdentifier'        => 'teletexTerminalIdentifier',
    'urn:mace:dir:attribute-def:telexNumber'                      => 'telexNumber',
    'urn:mace:dir:attribute-def:textEncodedORAddress'             => 'textEncodedORAddress',
    'urn:mace:dir:attribute-def:title'                            => 'title',
    'urn:mace:dir:attribute-def:uid'                              => 'uid',
    'urn:mace:dir:attribute-def:uniqueIdentifier'                 => 'uniqueIdentifier',
    'urn:mace:dir:attribute-def:uniqueMember'                     => 'uniqueMember',
    'urn:mace:dir:attribute-def:userCertificate'                  => 'userCertificate',
    'urn:mace:dir:attribute-def:userClass'                        => 'userClass',
    'urn:mace:dir:attribute-def:userPKCS12'                       => 'userPKCS12',
    'urn:mace:dir:attribute-def:userPassword'                     => 'userPassword',
    'urn:mace:dir:attribute-def:userSMIMECertificate'             => 'userSMIMECertificate',
    'urn:mace:dir:attribute-def:userid'                           => 'userid',
    'urn:mace:dir:attribute-def:x121Address'                      => 'x121Address',
    'urn:mace:dir:attribute-def:x500UniqueIdentifier'             => 'x500UniqueIdentifier',
    'urn:mace:terena.org:attribute-def:schacCountryOfCitizenship' => 'schacCountryOfCitizenship',
    'urn:mace:terena.org:attribute-def:schacCountryOfResidence'   => 'schacCountryOfResidence',
    'urn:mace:terena.org:attribute-def:schacDateOfBirth'          => 'schacDateOfBirth',
    'urn:mace:terena.org:attribute-def:schacExpiryDate'           => 'schacExpiryDate',
    'urn:mace:terena.org:attribute-def:schacGender'               => 'schacGender',
    'urn:mace:terena.org:attribute-def:schacHomeOrganization'     => 'schacHomeOrganization',
    'urn:mace:terena.org:attribute-def:schacHomeOrganizationType' => 'schacHomeOrganizationType',
    'urn:mace:terena.org:attribute-def:schacMotherTongue'         => 'schacMotherTongue',
    'urn:mace:terena.org:attribute-def:schacPersonalPosition'     => 'schacPersonalPosition',
    'urn:mace:terena.org:attribute-def:schacPersonalTitle'        => 'schacPersonalTitle',
    'urn:mace:terena.org:attribute-def:schacPersonalUniqueCode'   => 'schacPersonalUniqueCode',
    'urn:mace:terena.org:attribute-def:schacPersonalUniqueID'     => 'schacPersonalUniqueID',
    'urn:mace:terena.org:attribute-def:schacPlaceOfBirth'         => 'schacPlaceOfBirth',
    'urn:mace:terena.org:attribute-def:schacProjectMembership'    => 'schacProjectMembership',
    'urn:mace:terena.org:attribute-def:schacProjectSpecificRole'  => 'schacProjectSpecificRole',
    'urn:mace:terena.org:attribute-def:schacSn1'                  => 'schacSn1',
    'urn:mace:terena.org:attribute-def:schacSn2'                  => 'schacSn2',
    'urn:mace:terena.org:attribute-def:schacUserPresenceID'       => 'schacUserPresenceID',
    'urn:mace:terena.org:attribute-def:schacUserPrivateAttribute' => 'schacUserPrivateAttribute',
    'urn:mace:terena.org:attribute-def:schacUserStatus'           => 'schacUserStatus',
    'urn:oasis:names:tc:SAML:attribute:pairwise-id'               => 'pairwise-id',
    'urn:oasis:names:tc:SAML:attribute:subject-id'                => 'subject-id',
];
