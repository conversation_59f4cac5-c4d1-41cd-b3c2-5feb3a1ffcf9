<?php

/** \defgroup model_users_alerts Alertes email
 *	\ingroup model_users
 *	Ce module comprend les fonctions nécessaires à la gestion des alertes emails sur le statut des
 *	commandes.
 *	@{
 */

/**	Retourne tous les états de commande pour lesquels une alerte email est définie pour un utilisateur
 *	donné.
 *
 *	@param int $usr Obligatoire, identifiant de l'utilisateur pour lequel on souhaite connaître les alertes
 *	enregistrées.
 *
 *	@return bool false en cas d'erreur
 *	@return resource Cette fonction retourne un résultat de requête MySQL comprenant une seule colonne :
 *			- state_id : identifiant de l'état pour lequel une alerte à été définie pour cet utilisateur
 *
 */
function gu_ord_alerts_get( $usr ){
	global $config;

	if( !gu_users_exists($usr) ){
		return false;
	}

	return ria_mysql_query('
		select alert_state_id as state_id from gu_ord_alerts
		where alert_tnt_id='.$config['tnt_id'].' and alert_usr_id='.$usr.' order by alert_state_id
	');
}

// \cond onlyria
/**	Cette fonction teste si un utilisateur donné souhaite ou non une alerte email
 *	pour un état de commande.
 *	@param int $usr Identifiant de l'utilisateur
 *	@param int $state Identifiant du statut de commande
 *	@return bool true si l'utilisateur souhaite une alerte email
 *	@return bool false si l'utilisateur ne souhaite pas d'alerte email ou si une erreur s'est produite.
 */
function gu_ord_alerts_exists( $usr, $state ){
	global $config;

	if( !gu_users_exists($usr) ) return false;
	if( !ord_states_exists($state) ) return false;
	return ria_mysql_num_rows(ria_mysql_query('
		select alert_usr_id from gu_ord_alerts
		where alert_tnt_id='.$config['tnt_id'].' and alert_usr_id='.$usr.' and alert_state_id='.$state
	))>0;
}
// \endcond

/**	Cette fonction permet l'ajout d'une alerte email pour un état de commande et un utilisateur donné.
 *	Si une alerte était déjà définie pour ce statut, aucune erreur n'est générée.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur pour lequel on définie les préférences concernant les alertes emails
 *	@param int $state Obligatoire, Etat pour lequel l'utilisateur souhaite recevoir une notification
 *	@return bool true si l'enregistrement à réussi, false en cas d'échec.
 */
function gu_ord_alerts_add( $usr, $state ){
	global $config;

	if( !gu_users_exists($usr) ) return false;
	if( !ord_states_exists($state) ) return false;

	$rexists = ria_mysql_query('
		select alert_usr_id, alert_state_id from gu_ord_alerts
		where alert_tnt_id='.$config['tnt_id'].' and alert_usr_id='.$usr.' and alert_state_id='.$state
	);

	if( ria_mysql_num_rows($rexists)>0 ){
		return true;
	}

	return ria_mysql_query('
		insert into gu_ord_alerts (alert_tnt_id,alert_usr_id,alert_state_id)
		values ('.$config['tnt_id'].','.$usr.','.$state.')
	');

}

/**	Cette fonction permet la suppression d'une alerte email pour un état de commande et un utilisateur donné.
 *	Si aucune alerte n'était définie pour cet état de commande, aucune erreur n'est générée.
 *
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur pour lequel on définie les préférences concernant les alertes emails
 *	@param int $state Obligatoire, Etat pour lequel l'utilisateur ne souhaite pas recevoir de notification
 *	@return bool true si l'enregistrement à réussi.
 *	@return bool false en cas d'échec.
 *
 */
function gu_ord_alerts_del( $usr, $state ){
	global $config;

	if( !gu_users_exists($usr) ) return false;
	if( !ord_states_exists($state) ) return false;

	return ria_mysql_query('
		delete from gu_ord_alerts
		where alert_tnt_id='.$config['tnt_id'].'
			and alert_usr_id='.$usr.'
			and alert_state_id='.$state
	);
}

/** Cette fonction réalise une mise à jour des alertes emails choisi.
 * 	Supprime et remplace.
 * 	@param int $usr_id Obligatoire, identifiant d'un compte client
 * 	@param array $ar_states Obligatoire, tableau des status (peut être vide)
 * 	@return true en cas de succès, false dans le cas contraire
 */
function gu_ord_alerts_update( $usr_id, $ar_states ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	if( !is_array($ar_states) ){
		return false;
	}

	if( count($ar_states) ){
		$ar_states = control_array_integer( $ar_states, true );
		if( $ar_states === false ){
			return false;
		}
	}

	$res = ria_mysql_query('
		delete from gu_ord_alerts
			where alert_tnt_id='.$config['tnt_id'].'
				and alert_usr_id='.$usr_id.'
	');

	if( !$res ){
		return false;
	}

	if( count($ar_states) ){
		$sql = '
			insert into gu_ord_alerts
				( alert_tnt_id, alert_usr_id, alert_state_id )
			values
		';

		$first = true;
		foreach( $ar_states as $state ){
			if( !$first ){
				$sql .= ', ';
			}

			$sql .= '( '.$config['tnt_id'].', '.$usr_id.', '.$state.' )';

			$first = false;
		}

		if( !ria_mysql_query($sql) ){
			return false;
		}
	}

	return true;
}

/**	Restaure les paramètres de notification par défaut pour un utilisateur donné.
 *
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur pour lequel il faut restaurer le paramétrage
 *	@return bool true en cas de succès
 *	@return bool false en cas d'erreur
 *
 */
function gu_ord_alerts_set_default( $usr ){
	global $config;

	if( !gu_users_exists($usr) ) return false;
	if( !isset($config['default_alerts']) || !is_array($config['default_alerts']) ) return false;

	foreach( $config['default_alerts'] as $state ){
		@ria_mysql_query('
			insert into gu_ord_alerts (alert_tnt_id,alert_usr_id,alert_state_id)
			values ('.$config['tnt_id'].','.$usr.','.$state.')
		');
	}

	return true;

}

/// @}

