<?php

set_include_path('/var/www/riashop-4095/include/');

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('sys.zones.inc.php');

/* Identifiants de types de zones :
	*		- Région : 16
	*		- Province : 17
	*		- Communauté : 18
	*		- Ville : 19
	*		- Code postal : 5

	*/
$geoname_id_country = country_informations_get('IT');
$list_regions = get_children_zone($geoname_id_country);

foreach ($list_regions as $region) {
	$list_provinces = get_children_zone($region['geonameId']);
	foreach ($list_provinces as $key=>$province) {
		print_r($key);
		$list_communautes = get_children_zone($province['geonameId']);
		//foreach ($list_communautes as $communaute) {
		//	$list_villes = get_children_zone($communaute['geonameId']);
		//	//foreach ($list_villes as $ville) {
		//	//	$list_zipcode = get_ville_information($region['countryCode'],$ville['toponymName']);
		//	//	foreach ($list_zipcode as $zipcode) {
		//	//	}
		//	//}
		//}
	}
}

/**
 * cette fonction permet la récupération les informations d'un pays via l'API geonames
 * @param code du pays (FR,IT...)
 * @return retourne le geoname_id du pays
*/
function country_informations_get($code_pays){
	$url_api = 'http://api.geonames.org/countryInfoJSON?username=kevinria&country=' . $code_pays;
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_URL, $url_api);
	$result = curl_exec($ch);
	curl_close($ch);
	$result = json_decode($result,true);

	return $result['geonames'][0]['geonameId'];
}

/**
 * permet de recuperer tous les enfant d'une zone voulu
 * @param  id de la zone (geonameId)
 * @return list des zone enfant
 */
function get_children_zone($zone_id){
	$url_api = 'http://api.geonames.org/childrenJSON?geonameId=' . $zone_id . '&username=kevinria';
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_URL, $url_api);
	$result = curl_exec($ch);
	curl_close($ch);
	$result = json_decode($result,true);

	if(!isset($result['geonames'])){
		ria_dump($result);
	}
	return $result['geonames'];
}