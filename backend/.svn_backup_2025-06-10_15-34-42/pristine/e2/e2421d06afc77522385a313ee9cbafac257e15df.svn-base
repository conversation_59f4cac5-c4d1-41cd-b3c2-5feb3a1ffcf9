# ReportByCategory

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**catalog_category_id** | [**\Swagger\Client\Model\BeezUPCommonCatalogCategoryId**](BeezUPCommonCatalogCategoryId.md) |  | 
**catalog_category_path** | [**\Swagger\Client\Model\BeezUPCommonCatalogCategoryPath**](BeezUPCommonCatalogCategoryPath.md) |  | [optional] 
**all_product_count** | **int** | The product count for this category. This includes all the products that have been imported and can still have associated clicks and orders. | 
**catalog_product_count** | **int** | The catalog product count for this category. This includes all products that are still present in your imported catalog. | 
**enabled_product_count** | **int** | The enabled product count for this category. This includes all products that are still present in your imported catalog and have not been disabled via the optimisation feature. | 
**click_count** | **int** | The click count for this category | 
**cost** | **float** | The cost for this category | 
**order_count** | **int** | The order count for this category | 
**roi** | **float** | The Return On Investment for this category | [optional] 
**margin** | **float** | The margin for this category | [optional] 
**performance_indicator** | **float** | The performance indicator based on the performance indicator formula indicated in the request for this category | 
**total_sales** | **float** | The total sales for this category | 
**sold_product_count** | **int** | The product sold count count for this category | 
**links** | [**\Swagger\Client\Model\ReportByCategoryLinks**](ReportByCategoryLinks.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


