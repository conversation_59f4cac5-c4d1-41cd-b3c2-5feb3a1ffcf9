<?php

require_once( 'PriceWatching/models/prw_followed_products.inc.php' );
require_once( 'PriceWatching/models/prw_offers.inc.php' );

/**
 *	\ingroup PriceWatching
 *	\class WatchingAssistant
 *
 *    \brief Cette class permet un pont entre les modèles du module PriceWatching et les vues de l'interface d'administration.
 *    Cette class offre des méthodes pour récupérer les informations des produits, cela inclus l'identifiant et le code
 *    barre. En plus la class donne accès à des méthodes outils qui sont util pour faire du tri d'informations, comme :
 *      - getDisable
 *      - followedProductTableData
 *      - getChildrens
 *      - hasChildrens
 *
 *    L'utilisation principale de cette class est d'activer ou désactiver la veille tarifaire pour certain produit et
 *    certain concurrent Voici deux exemples d'activation l'un avec un identifiant de catégorie et le second avec un
 *    tableau d'identifiants produits.
 *
 *    Exemple : Surveillance des produits d'une catégorie
 *    \code{.php}
 *        $pwa = new WatchingAssistant();
 *        $pwa->setProductCategorieInfo( $categ_id); // -> Obligatoire, identifiant de la catégorie
 *        $pwa->watchSelectedProducts( array $cpts_id); // -> Obligatoire, Tableau d'identifiant concurrents
 *    \endcode
 *
 *    Exemple : Surveillance des produits grâce à un tableau d'identifiants produits
 *    \code{.php}
 *        $pwa = new WatchingAssistant();
 *        $pwa->setProductsInfo( array $products_id ); // -> Obligatoire, tableau d'identifiants de produit
 *      $pwa->watchSelectedProducts( array $cpts_id ); // -> Obligatoire, Tableau d'identifiant concurrents
 *    \endcode
 *
 *    @{
 */
class WatchingAssistant
{

	/**
	 * @var $prw_followed_products
	 */
	private $prw_followed_products;
	/**
	 * @var $prw_offers
	 */
	private $prw_offers;
	/**
	 * @var array $product_childrens
	 */
	private $product_childrens = array();
	/**
	 * @var array $products_info
	 */
	private $products_info = array();

	/**	Constructeur
	 *	initialise les instances des modèles prw_followed_products et prw_offers
	 */
	public function __construct()
	{
		$this->prw_followed_products = new prw_followed_products();
		$this->prw_offers = new prw_offers();
	}

	/**
	 * Cette fonction permet de récuper les informations d'un produit necessaire à partir d'une catégorie donné
	 *
	 *	@param   $categs Tableau d'identifiant de catégories
	 *
	 * @return          retourne un tableau associatif avec tous les produits des catégories en paramètre
	 *                                   avec le jeux de valeur suivant
	 *    -id
	 *    -ref
	 *    -name
	 *    -barcode
	 */
	public function setProductCategorieInfo( $categs )
	{
		$prds = array();
		foreach( $categs as $c ){
			$r = prd_products_get_simple( 0, '', true, $c, true, false, false, false, array( 'childs' => true ) );
			$rows = array();
			while( $row = ria_mysql_fetch_assoc( $r ) ) {
//                si un produit ne possède pas de code barre c'est un produit parent sur riashop donc on cherche les produits enfants
				if( $row['barcode'] !== "" ){
					$prds[] = array( 'id' => $row['id'], 'barcode' => $row['barcode'] );
				} else{
					$this->setChildrens( $row['id'] );
				}
			}
		}
		$this->products_info = array_merge( $prds, $this->getChildrens() );

		return $prds;
	}

	/** Cette fonction retourne la liste des produits à surveiller
	 *
	 * @return retourne un tableau avec tous les produits sélectionner pour la veille tarifaire avec le jeux de valeur
	 * suivant
	 *    -id
	 *    -barcode
	 */
	public function getProductsInfo()
	{
		return $this->products_info;
	}


	/** Cette fonction récupère le champ disable de prw_followed_products à travers le modèle
	 * @param $prd_id
	 * @param $cpt
	 *
	 * @return bool|null    retourne false si erreur, sinon la valeur de disable 1 ou 0, si pas d'info retourne null
	 */
	public function getDisable( $prd_id, $cpt )
	{
		return $this->prw_followed_products->prw_followed_products_get_disable( $prd_id, $cpt );
	}

	/**
	 * Cette fonction est un alias de get_product_info qui prend un tableau de produit
	 *
	 * @param array $prd_id
	 *
	 * @return array
	 */
	public function setProductsInfo( $prd_id = array() )
	{
		$prd = array();
		foreach( $prd_id as $item ){
			if( $this->setProductInfo( $item ) ){
				$this->setProductInfo( $item );
			}
		}
//		$this->products_info=array_merge($prd,$this->getChildrens());
	}

	/**
	 * Permet de récupérer les informations nécessaires surveiller un produit
	 *\* @param $prd_id
	 *
	 * @return array|bool
	 */
	public function setProductInfo( $prd_id )
	{
		$childs = array();
		$r = prd_products_get_simple( $prd_id, '', false, 0, true, false, false, false, array( 'childs' => true ) );
		if( !ria_mysql_num_rows( $r ) ){
			return $this->products_info = false;
		}

		while( $row = ria_mysql_fetch_assoc( $r ) ) {
			if( $row['barcode'] !== "" ){
				$this->products_info[] = array( 'id' => $row['id'], 'barcode' => $row['barcode'] );
			} else{
				$childs = $this->setChildrens( $row['id'] );
			}
		}
		$this->products_info = array_merge( $this->products_info, $childs );
	}


	/** Cete fonction retourne la liste des produits enfant d'un produit parent
	 * à utilisé sur la page produit pour afficher les enfants si le produit est un produit parent.
	 *
	 * @return mixed retourne un tableau avec tous les produits enfants avec le jeux de valeur suivant
	 *    -id
	 *    -ref
	 *    -name
	 *    -barcode
	 */
	public function getChildrens()
	{
		return $this->product_childrens;
	}

	/** Fonction outil qui permet de tester si un produit possède des enfants ou pas
	 *
	 *    @param $prd_id Obligatoire, identifiant d'un produit.
	 *
	 * @return bool
	 */
	public function hasChildrens( $prd_id )
	{
		$r = prd_products_get_simple( 0, '', false, 0, true, false, false, false, array( 'parent' => $prd_id, 'childs' => true ) );
		if( ria_mysql_num_rows( $r ) ){
			$this->setChildrens( $prd_id );

			return true;
		} else{
			return false;
		}
	}


	/** Cette fonction active la veille tarifaire pour pour les produit selectionnés
	 *  @param array $cpt_ids Obligatoire, tableau d'identifiants concurrents
	 *
	 *	@return bool retourne false si erreur
	 */
	public function watchSelectedProducts( array $cpt_ids )
	{
		if( !isset( $cpt_ids ) || !is_array( $cpt_ids ) || !is_array( $this->products_info ) || empty( $this->products_info ) ){
			return false;
		}
		foreach( $this->products_info as $prd ){
			if( $prd ){
				foreach( $cpt_ids as $cpt_id ){
					$disable = $this->getDisable( $prd['id'], $cpt_id );
					switch( $disable ) {
						case "1" :
							$this->enableProductFollowing( $prd['id'], $cpt_id );
							break;
						case "0":
							break;
						case null:
							$this->prw_followed_products->prw_followed_products_add( $prd['id'], $cpt_id, '', 0 );
							break;
					}
				}
			}
		}
	}

	/** Cette fonction désactive la veille tarifaire pour pour les produit selectionnés
	 *  @param array $cpts_id Obligatoire, tableau d'identifiants concurrent.
	 *
	 *	@return bool retourne false si erreur
	 */
	public function cancelSelectedProducts( $cpts_id )
	{
		if( !isset( $cpts_id ) || !is_array( $cpts_id ) || !is_array( $this->products_info ) || empty( $this->products_info ) ){
			return false;
		}
		foreach( $this->products_info as $prd ){
			foreach( $cpts_id as $cpt ){
				$disable = $this->getDisable( $prd['id'], $cpt );
				switch( $disable ) {
					case "1" :
						break;
					case "0" :
						$this->disableProductFollowing( $prd['id'], $cpt );
						break;
					case null :
						break;
				}
			}
		}
	}

	/** Cette fonction permet d'ajouter les produit ou non a la selection personaliser
	 *	@param bool $is_selection Facultatif, indique si l'on ajoute le produit a la sélection ou non
	 *
	 *	@return bool true si l'ajout c'est bien réalisé false si non
	 */
	public function setProductsInSelection($is_selection=false){
		$no_error = true;
		foreach( $this->products_info as $prd ){
			if( !$this->prw_followed_products->prw_followed_products_set_is_selection( $prd['id'], $is_selection) ){
				$no_error = false;
			}
		}
		return $no_error;
	}

	/**
	 * Cette fonction retourner les information du produit
	 * chez le client et les concurrents pour réaliser un tableau récapitulatif.
	 *
	 *    @param $prd_id Obligatoire, identifiant produit.
	 *    @param $cpt    Obligatoire, tableau de concurrent avec une clé 'id'.
	 *
	 * @return array un tableau avec les informations du produit client dans un autre tableau avec les clées :
	 *  - followed : info du produit suivit pour le client
	 *  - lastOffer : dernière offre client pour ce produit
	 * un autre tableau avec les dernière offres relevé pour chaque concurrent activé avec les clées :
	 *  - followed : info du produit suivit pour un concurrent
	 *  - lastOffer :  dernière offre pour ce concurrent
	 */
	public function followedProductTableData( $prd_id, array $cpt )
	{
		$comp = array();
		$client = array();
		foreach( $cpt as $key => $c ){
			if( $c['id'] == PRW_CLIENT ){
				$followed = $this->prw_followed_products->prw_followed_products_get( $prd_id, $c['id'] );
				$client['followed'] = $followed[0];
				$client['lastOffer'] = $this->prw_offers->get_offer_getLast( $prd_id, $c['id'] );
			} else{
				$followed = $this->prw_followed_products->prw_followed_products_get( $prd_id, $c['id'] );
				$comp[$c['name']] = array(
					'followed'  => $followed[0],
					'lastOffer' => $this->prw_offers->get_offer_getLast( $prd_id, $c['id'] ) );
			}
		}

		return array( $client, $comp );
	}

	/** Cette fonction mais en place les produits enfants d'un produit parent
	 *
	 * @param $prd_id Obligatoire, identifiant d'un produit parent
	 *
	 * @return array|bool retourne false en cas d'erreur, sinon un tableau avec les enfants du produit parent avec le
	 * jeux de valeur suivant
	 *    -id
	 *    -ref
	 *    -name
	 *    -barcode
	 */
	private function setChildrens( $prd_id )
	{
		$r = prd_products_get_simple( 0, '', false, 0, true, false, false, false, array( 'parent' => $prd_id, 'childs' => true ) );
		$tmp = array();
		if( !ria_mysql_num_rows( $r ) ){
			return $tmp;
		}
		while( $row = ria_mysql_fetch_assoc( $r ) ) {
			if( $row['barcode'] !== "" ){
				$this->product_childrens[] = $tmp[] = array( 'id' => $row['id'], 'ref' => $row['ref'], 'name' => $row['name'], 'barcode' => $row['barcode'] );
			} else{
				return false;
			}
		}

		return $tmp;
	}

	/**
	 * Cette fonction permet de d'activer la surveillance pour un produit.
	 *
	 * @param $prd_id Obligatoire, identifiant d'un produit.
	 * @param $cpt    Obligatoire, identifiant d'un concurrent.
	 */
	private function enableProductFollowing( $prd_id, $cpt_id )
	{
		$this->prw_followed_products->prw_followed_products_update_disable( $prd_id, $cpt_id, 0 );
	}

	/**
	 * Cette fonction permet de désactiver la surveillance pour un produit.
	 *
	 * @param $prd_id Obligatoire, identifiant d'un produit.
	 * @param $cpt    Obligatoire, identifiant d'un concurrent.
	 */
	private function disableProductFollowing( $prd_id, $cpt_id )
	{
		$this->prw_followed_products->prw_followed_products_update_disable( $prd_id, $cpt_id, 1 );
	}
}

/// @}
