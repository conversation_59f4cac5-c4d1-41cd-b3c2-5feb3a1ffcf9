<?php
    /** \file index.php
     * Ce fichier est la page qui permet de visualiser les différents exports en cours de préparation
     * ainsi que l'historique des exports réalisés
     */

    require_once('exports.inc.php');

    // Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_EXPORT');
    
    
    // Téléchargement de l'export
    if( isset($_GET['download']) && isset($_GET['exp']) ){
        $r_export = exp_exports_get( $_GET['exp'] );
        if( $r_export && ria_mysql_num_rows($r_export) ){
            $export = ria_mysql_fetch_assoc( $r_export );       
            if( file_exists($export['file_path']) ){
                $extension = pathinfo( $export['file_path'] , PATHINFO_EXTENSION);
                if (in_array($extension, array('xls', 'xlsx'))) {
                    header('Content-Type: application/vnd.ms-excel');
                }else{
                    header('Content-Type: application/x-force-download');
                }
                header('Content-Disposition: attachment; filename="'.$export['name'].'"');
                ob_clean();
			    flush();
                readfile( $export['file_path'] );
                exit;
            }
        }
    }

    // Suppression des exports
    if( isset($_POST['del'], $_POST['exp']) ){
        foreach( $_POST['exp'] as $exp_id ){
            exp_exports_del( $exp_id );
        }
    }
    
    // Défini le titre de la page
    define('ADMIN_PAGE_TITLE', _('Exports') . ' - ' . _('Outils'));
    require_once('admin/skin/header.inc.php');

    $r_export_processing = exp_exports_get( 0, array('pending', 'processing') );
    if( $r_export_processing ){
        $row_total_processing = ria_mysql_num_rows($r_export_processing);
    }else{
        $row_total_processing = 0;
    }
    
    $r_export = exp_exports_get( 0, array( 'finished', 'error') );
    if( $r_export ){
        $row_total = ria_mysql_num_rows($r_export);
    }else{
        $row_total = 0;
    }

?>

    <h2><?php print _('Liste des exports en cours'); ?></h2>

    <table class="checklist table-export" id="table-unfinished-export"> 
        <thead>
            <th><?php print _('Exports'); ?></th>
            <th><?php print _('Etat'); ?></th>
        </thead>
        <tbody>
            <tr id="empty" <?php echo ($row_total_processing)? 'style="display:none"': ''?>>
                <td colspan="2"><?php print _('Aucun export en cours de traitement pour le moment'); ?></td>
            </tr>
            <?php 
            if( $row_total_processing ){
                while( $export_processing = ria_mysql_fetch_assoc($r_export_processing) ) { 
                    if( $export_processing['state'] == 'pending' ){
                        $progression_txt = '0/...';
                    }else{
                        $progression_txt = $export_processing['line_processed']."/".$export_processing['line_count'];
                    }?>
                    <tr id="exp-<?php echo $export_processing['id']; ?>">
                        <td>
                            <strong><?php print _('Contenu :'); ?></strong> <?php echo fld_classes_get_name( $export_processing['cls_id'] ) ?><br>
                            <strong><?php print _('Date d\'ajout :'); ?></strong> <?php echo ria_date_format( $export_processing['date_created'] ) ?> <br>
                        </td>
                        <td class="<?php echo exp_exports_state_css_class($export_processing['state']) ?>" id="state-<?php echo $export_processing['id'] ?>" data-id="<?php echo $export_processing['id'] ?>">
                            <?php echo exp_state_display($export_processing['state']);?>
                            <div class="progress-bar" >
                                <div class="bar-progression" data-progress=<?php echo $export_processing['line_processed']; ?> data-progress-max=<?php echo $export_processing['line_count'] ?>></div>
                                <div class="span-progression"><?php print $progression_txt; ?></div>
                            </div>
                        </td>    
                    </tr><?php 
                }
            } ?>
        </tbody>
    </table>


    <h2><?php print _('Liste des exports'); ?></h2>

    <form method="post">
        <table class="checklist" id="table-finished-export"> 
            <thead>
                <th class="col-check" data-label="<?php print _('Tout cocher :'); ?> ">
                    <input type="checkbox" onclick="checkAllClick(this)">
                </th>
                <th class="thead-none"><?php print _('Fichier'); ?></th>
                <th class="thead-none"><?php print _('Contenu'); ?></th>
                <th class="thead-none"><?php print _('Nbr de lignes'); ?></th>
                <th class="thead-none"><?php print _('Date d\'ajout'); ?></th>
                <th class="thead-none"><?php print _('Etat'); ?></th>
            </thead>
            <tbody>
                <?php if( $row_total == 0 ){ ?>
                <tr>
                    <td colspan="6"><?php print _('Aucun export pour le moment'); ?></td>
                </tr>
                <?php }else{ 
                    while( $export = ria_mysql_fetch_assoc($r_export) ) { ?>
                    <tr>
                        <td class="align-center">
                            <input type="checkbox" name="exp[]" value="<?php echo $export['id'] ?>" >
                        </td>
                        <td data-label="<?php print _('Fichier :'); ?> "><a href="/admin/tools/exports/index.php?download=1&exp=<?php echo $export['id']; ?>"><?php echo htmlspecialchars( $export['name'] ); ?></a></td>
                        <td data-label="<?php print _('Contenu :'); ?> "><?php echo fld_classes_get_name( $export['cls_id'] ) ?></td>
                        <td class="number" data-label="<?php print _('Nbr de lignes :'); ?> "><?php print number_format( $export['line_count'], 0, ',', ' ' ); ?></td>
                        <td data-label="<?php print _('Date d\'ajout :'); ?> "><?php echo ria_date_format( $export['date_created'] ) ?></td>
                        <td class="<?php echo exp_exports_state_css_class($export['state']) ?>" data-label="<?php print _('Etat :'); ?> ">
                            <?php echo exp_state_display($export['state']);?>
                        </td>
                    </tr>
                    <?php }
                } ?>
            </tbody>
            <tfoot>
                    <tr>
                        <td colspan="6">
                            <input type="submit" name="del" value="<?php print _('Supprimer'); ?>" class="float-left" />
                        </td>
                    </tr>
                </tfoot>
        </table>
    </form>


