# ChannelCatalogList

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**links** | [**\Swagger\Client\Model\ChannelCatalogListLinks**](ChannelCatalogListLinks.md) |  | [optional] 
**lov_links** | [**\Swagger\Client\Model\ChannelCatalogListLovLinks**](ChannelCatalogListLovLinks.md) |  | [optional] 
**channel_catalogs** | [**map[string,\Swagger\Client\Model\ChannelCatalog]**](ChannelCatalog.md) | The channel catalog list. The key is the channel catalog identifier | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


