<?php
namespace SchemaDotOrg\Tags;
require_once('SchemaDotOrg/Tags/TagOffer.php');
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag AggregateOffer qui complémente TagProduct
 */
class TagAggregateOffer extends TagOffer {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	protected $type = 'AggregateOffer';

	/**
	 * Permet d'ajouter le prix minimum du listing
	 *
	 * @param float $price Prix minimum du listing
	 * @return self retourne l'instance
	 */
	public function setLowPrice($price){
		$this->addField('lowPrice', $price);

		return $this;
	}

	/**
	 * Permet d'ajouter le prix maximum du listing
	 *
	 * @param float $price Prix maximum du listing
	 * @return self retourne l'instance
	 */
	public function setHighPrice($price){
		$this->addField('highPrice', $price);

		return $this;
	}
}
// @}