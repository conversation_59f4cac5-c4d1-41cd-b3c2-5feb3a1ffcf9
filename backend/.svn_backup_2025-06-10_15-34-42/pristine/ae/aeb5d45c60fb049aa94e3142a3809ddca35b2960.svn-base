<?php
	/** \file send-cheque-notify.php
	 *
	 * 	Ce script est chargé de relancer les clients pour les paiements par cheque non finalisé.
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('orders.inc.php');
	
	$fld_cheque_notify = 3206;
	
	foreach( $configs as $config ){
		if( !isset($config['sys_cheque_notify_active']) || !$config['sys_cheque_notify_active'] ){
			continue;
		}

		// récupère les comptes clients utilisés pour les places de marché
		$mkt_users = ctr_marketplaces_get_users();
		if( !is_array($mkt_users) ){
			$mkt_users = array();
		}

		$hours = ( isset($config['sys_cheque_notify_days']) && is_numeric($config['sys_cheque_notify_days']) && $config['sys_cheque_notify_days'] > 0 ? $config['sys_cheque_notify_days'] : 7 ) * 24;
		$date_start = date( 'Y-m-d H:00:00', strtotime('-'.( $hours + 10 ).' hours') );
		$date_end = date( 'Y-m-d H:00:00', strtotime('-'.$hours.' hours') );

		// Récupère les commandes en attente de paiement avec comme moyen de paiement : Chèque
		$rorder = ord_orders_get_with_adresses( 0, 0, array(_STATE_WAIT_PAY), '', $date_start, $date_end, false, false, null, false, false, false, false, _PAY_CHEQUE );
		if( $rorder && ria_mysql_num_rows($rorder) ){
			while( $order = ria_mysql_fetch_assoc($rorder) ){
				// Les commandes liés au places de marché ne sont pas notifié
				if( in_array($order['user'], $mkt_users) ){
					continue;
				}

				// controle si la commande n'a pas été déjà notifiée
				$notified = fld_object_values_get($order['id'], $fld_cheque_notify);
				if( $notified ){
					continue;
				}

				if( ord_orders_pay_cheque_notify($order) ){
					if( !fld_object_values_set($order['id'], $fld_cheque_notify, 'Oui') ){
						error_log('Erreur dans send-cheque-notify : '.$order['id']);
					}
				}
			}
		}
	}