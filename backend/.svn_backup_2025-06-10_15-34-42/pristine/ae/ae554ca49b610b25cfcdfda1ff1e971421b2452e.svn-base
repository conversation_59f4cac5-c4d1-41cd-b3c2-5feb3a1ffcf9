<?php
/**
 * Order
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * Order Class Doc Comment
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class Order implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'order';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'marketplace_technical_code' => '\Swagger\Client\Model\BeezUPCommonMarketplaceTechnicalCode',
        'account_id' => '\Swagger\Client\Model\AccountId',
        'beez_up_order_id' => '\Swagger\Client\Model\BeezUPOrderId',
        'beez_up_order_url' => '\Swagger\Client\Model\BeezUPCommonHttpUrl',
        'marketplace_business_code' => '\Swagger\Client\Model\BeezUPCommonMarketplaceBusinessCode',
        'order_marketplace_order_id' => '\Swagger\Client\Model\MarketplaceOrderId',
        'order_status_beez_up_order_status' => '\Swagger\Client\Model\BeezUPOrderStatus',
        'order_status_marketplace_order_status' => '\Swagger\Client\Model\MarketplaceOrderStatus',
        'order_merchant_order_id' => '\Swagger\Client\Model\OrderMerchantOrderId',
        'order_merchant_e_commerce_software_name' => '\Swagger\Client\Model\OrderMerchantECommerceSoftwareName',
        'order_merchant_e_commerce_software_version' => '\Swagger\Client\Model\OrderMerchantECommerceSoftwareVersion',
        'order_purchase_utc_date' => '\DateTime',
        'order_last_modification_utc_date' => '\DateTime',
        'order_marketplace_last_modification_utc_date' => '\DateTime',
        'order_buyer_name' => '\Swagger\Client\Model\OrderBuyerName',
        'order_total_price' => 'float',
        'order_currency_code' => '\Swagger\Client\Model\BeezUPCommonCurrencyCode',
        'processing' => '\Swagger\Client\Model\Processing',
        'etag' => '\Swagger\Client\Model\Etag',
        'links' => '\Swagger\Client\Model\OrderLinks',
        'order_market_place_channel' => 'string',
        'order_total_tax' => 'float',
        'order_total_commission' => 'float',
        'order_payment_method' => 'string',
        'order_paying_utc_date' => '\DateTime',
        'order_comment' => 'string',
        'order_shipping_civility' => 'string',
        'order_shipping_company_name' => 'string',
        'order_shipping_address_name' => 'string',
        'order_shipping_email' => 'string',
        'order_shipping_address_line1' => 'string',
        'order_shipping_address_line2' => 'string',
        'order_shipping_address_line3' => 'string',
        'order_shipping_address_postal_code' => 'string',
        'order_shipping_address_city' => 'string',
        'order_shipping_address_state_or_region' => 'string',
        'order_shipping_address_country_name' => 'string',
        'order_shipping_address_country_iso_code_alpha2' => 'string',
        'order_shipping_phone' => 'string',
        'order_shipping_mobile_phone' => 'string',
        'order_shipping_price' => 'float',
        'order_shipping_method' => 'string',
        'order_shipping_shipping_tax' => 'float',
        'order_shipping_earliest_ship_utc_date' => '\DateTime',
        'order_shipping_latest_ship_utc_date' => '\DateTime',
        'order_buyer_identifier' => 'string',
        'order_buyer_civility' => 'string',
        'order_buyer_company_name' => 'string',
        'order_buyer_email' => 'string',
        'order_buyer_address_line1' => 'string',
        'order_buyer_address_line2' => 'string',
        'order_buyer_address_line3' => 'string',
        'order_buyer_address_postal_code' => 'string',
        'order_buyer_address_city' => 'string',
        'order_buyer_address_state_or_region' => 'string',
        'order_buyer_address_country_name' => 'string',
        'order_buyer_address_country_iso_code_alpha2' => 'string',
        'order_buyer_phone' => 'string',
        'order_buyer_mobile_phone' => 'string',
        'order_fulfilled_by' => 'string',
        'order_order_source_uri' => 'string',
        'order_order_items_source_uri' => 'string',
        'order_items' => '\Swagger\Client\Model\OrderItem[]',
        'transition_links' => '\Swagger\Client\Model\OrderTransitionLinks'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'marketplace_technical_code' => null,
        'account_id' => null,
        'beez_up_order_id' => null,
        'beez_up_order_url' => null,
        'marketplace_business_code' => null,
        'order_marketplace_order_id' => null,
        'order_status_beez_up_order_status' => null,
        'order_status_marketplace_order_status' => null,
        'order_merchant_order_id' => null,
        'order_merchant_e_commerce_software_name' => null,
        'order_merchant_e_commerce_software_version' => null,
        'order_purchase_utc_date' => 'date-time',
        'order_last_modification_utc_date' => 'date-time',
        'order_marketplace_last_modification_utc_date' => 'date-time',
        'order_buyer_name' => null,
        'order_total_price' => 'decimal',
        'order_currency_code' => null,
        'processing' => null,
        'etag' => null,
        'links' => null,
        'order_market_place_channel' => null,
        'order_total_tax' => 'decimal',
        'order_total_commission' => 'decimal',
        'order_payment_method' => null,
        'order_paying_utc_date' => 'date-time',
        'order_comment' => null,
        'order_shipping_civility' => null,
        'order_shipping_company_name' => null,
        'order_shipping_address_name' => null,
        'order_shipping_email' => null,
        'order_shipping_address_line1' => null,
        'order_shipping_address_line2' => null,
        'order_shipping_address_line3' => null,
        'order_shipping_address_postal_code' => null,
        'order_shipping_address_city' => null,
        'order_shipping_address_state_or_region' => null,
        'order_shipping_address_country_name' => null,
        'order_shipping_address_country_iso_code_alpha2' => null,
        'order_shipping_phone' => null,
        'order_shipping_mobile_phone' => null,
        'order_shipping_price' => 'decimal',
        'order_shipping_method' => null,
        'order_shipping_shipping_tax' => 'decimal',
        'order_shipping_earliest_ship_utc_date' => 'date-time',
        'order_shipping_latest_ship_utc_date' => 'date-time',
        'order_buyer_identifier' => null,
        'order_buyer_civility' => null,
        'order_buyer_company_name' => null,
        'order_buyer_email' => null,
        'order_buyer_address_line1' => null,
        'order_buyer_address_line2' => null,
        'order_buyer_address_line3' => null,
        'order_buyer_address_postal_code' => null,
        'order_buyer_address_city' => null,
        'order_buyer_address_state_or_region' => null,
        'order_buyer_address_country_name' => null,
        'order_buyer_address_country_iso_code_alpha2' => null,
        'order_buyer_phone' => null,
        'order_buyer_mobile_phone' => null,
        'order_fulfilled_by' => null,
        'order_order_source_uri' => 'uri',
        'order_order_items_source_uri' => 'uri',
        'order_items' => null,
        'transition_links' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'marketplace_technical_code' => 'marketplaceTechnicalCode',
        'account_id' => 'accountId',
        'beez_up_order_id' => 'beezUPOrderId',
        'beez_up_order_url' => 'beezUPOrderUrl',
        'marketplace_business_code' => 'marketplaceBusinessCode',
        'order_marketplace_order_id' => 'order_MarketplaceOrderId',
        'order_status_beez_up_order_status' => 'order_Status_BeezUPOrderStatus',
        'order_status_marketplace_order_status' => 'order_Status_MarketplaceOrderStatus',
        'order_merchant_order_id' => 'order_MerchantOrderId',
        'order_merchant_e_commerce_software_name' => 'order_MerchantECommerceSoftwareName',
        'order_merchant_e_commerce_software_version' => 'order_MerchantECommerceSoftwareVersion',
        'order_purchase_utc_date' => 'order_PurchaseUtcDate',
        'order_last_modification_utc_date' => 'order_LastModificationUtcDate',
        'order_marketplace_last_modification_utc_date' => 'order_MarketplaceLastModificationUtcDate',
        'order_buyer_name' => 'order_Buyer_Name',
        'order_total_price' => 'order_TotalPrice',
        'order_currency_code' => 'order_CurrencyCode',
        'processing' => 'processing',
        'etag' => 'etag',
        'links' => 'links',
        'order_market_place_channel' => 'order_MarketPlaceChannel',
        'order_total_tax' => 'order_TotalTax',
        'order_total_commission' => 'order_TotalCommission',
        'order_payment_method' => 'order_PaymentMethod',
        'order_paying_utc_date' => 'order_PayingUtcDate',
        'order_comment' => 'order_Comment',
        'order_shipping_civility' => 'order_Shipping_Civility',
        'order_shipping_company_name' => 'order_Shipping_CompanyName',
        'order_shipping_address_name' => 'order_Shipping_AddressName',
        'order_shipping_email' => 'order_Shipping_Email',
        'order_shipping_address_line1' => 'order_Shipping_AddressLine1',
        'order_shipping_address_line2' => 'order_Shipping_AddressLine2',
        'order_shipping_address_line3' => 'order_Shipping_AddressLine3',
        'order_shipping_address_postal_code' => 'order_Shipping_AddressPostalCode',
        'order_shipping_address_city' => 'order_Shipping_AddressCity',
        'order_shipping_address_state_or_region' => 'order_Shipping_AddressStateOrRegion',
        'order_shipping_address_country_name' => 'order_Shipping_AddressCountryName',
        'order_shipping_address_country_iso_code_alpha2' => 'order_Shipping_AddressCountryIsoCodeAlpha2',
        'order_shipping_phone' => 'order_Shipping_Phone',
        'order_shipping_mobile_phone' => 'order_Shipping_MobilePhone',
        'order_shipping_price' => 'order_Shipping_Price',
        'order_shipping_method' => 'order_Shipping_Method',
        'order_shipping_shipping_tax' => 'order_Shipping_ShippingTax',
        'order_shipping_earliest_ship_utc_date' => 'order_Shipping_EarliestShipUtcDate',
        'order_shipping_latest_ship_utc_date' => 'order_Shipping_LatestShipUtcDate',
        'order_buyer_identifier' => 'order_Buyer_Identifier',
        'order_buyer_civility' => 'order_Buyer_Civility',
        'order_buyer_company_name' => 'order_Buyer_CompanyName',
        'order_buyer_email' => 'order_Buyer_Email',
        'order_buyer_address_line1' => 'order_Buyer_AddressLine1',
        'order_buyer_address_line2' => 'order_Buyer_AddressLine2',
        'order_buyer_address_line3' => 'order_Buyer_AddressLine3',
        'order_buyer_address_postal_code' => 'order_Buyer_AddressPostalCode',
        'order_buyer_address_city' => 'order_Buyer_AddressCity',
        'order_buyer_address_state_or_region' => 'order_Buyer_AddressStateOrRegion',
        'order_buyer_address_country_name' => 'order_Buyer_AddressCountryName',
        'order_buyer_address_country_iso_code_alpha2' => 'order_Buyer_AddressCountryIsoCodeAlpha2',
        'order_buyer_phone' => 'order_Buyer_Phone',
        'order_buyer_mobile_phone' => 'order_Buyer_MobilePhone',
        'order_order_source_uri' => 'order_OrderSourceUri',
        'order_fulfilled_by' => 'order_FulfilledBy',
        'order_order_items_source_uri' => 'order_OrderItemsSourceUri',
        'order_items' => 'orderItems',
        'transition_links' => 'transitionLinks'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'marketplace_technical_code' => 'setMarketplaceTechnicalCode',
        'account_id' => 'setAccountId',
        'beez_up_order_id' => 'setBeezUpOrderId',
        'beez_up_order_url' => 'setBeezUpOrderUrl',
        'marketplace_business_code' => 'setMarketplaceBusinessCode',
        'order_marketplace_order_id' => 'setOrderMarketplaceOrderId',
        'order_status_beez_up_order_status' => 'setOrderStatusBeezUpOrderStatus',
        'order_status_marketplace_order_status' => 'setOrderStatusMarketplaceOrderStatus',
        'order_merchant_order_id' => 'setOrderMerchantOrderId',
        'order_merchant_e_commerce_software_name' => 'setOrderMerchantECommerceSoftwareName',
        'order_merchant_e_commerce_software_version' => 'setOrderMerchantECommerceSoftwareVersion',
        'order_purchase_utc_date' => 'setOrderPurchaseUtcDate',
        'order_last_modification_utc_date' => 'setOrderLastModificationUtcDate',
        'order_marketplace_last_modification_utc_date' => 'setOrderMarketplaceLastModificationUtcDate',
        'order_buyer_name' => 'setOrderBuyerName',
        'order_total_price' => 'setOrderTotalPrice',
        'order_currency_code' => 'setOrderCurrencyCode',
        'processing' => 'setProcessing',
        'etag' => 'setEtag',
        'links' => 'setLinks',
        'order_market_place_channel' => 'setOrderMarketPlaceChannel',
        'order_total_tax' => 'setOrderTotalTax',
        'order_total_commission' => 'setOrderTotalCommission',
        'order_payment_method' => 'setOrderPaymentMethod',
        'order_paying_utc_date' => 'setOrderPayingUtcDate',
        'order_comment' => 'setOrderComment',
        'order_shipping_civility' => 'setOrderShippingCivility',
        'order_shipping_company_name' => 'setOrderShippingCompanyName',
        'order_shipping_address_name' => 'setOrderShippingAddressName',
        'order_shipping_email' => 'setOrderShippingEmail',
        'order_shipping_address_line1' => 'setOrderShippingAddressLine1',
        'order_shipping_address_line2' => 'setOrderShippingAddressLine2',
        'order_shipping_address_line3' => 'setOrderShippingAddressLine3',
        'order_shipping_address_postal_code' => 'setOrderShippingAddressPostalCode',
        'order_shipping_address_city' => 'setOrderShippingAddressCity',
        'order_shipping_address_state_or_region' => 'setOrderShippingAddressStateOrRegion',
        'order_shipping_address_country_name' => 'setOrderShippingAddressCountryName',
        'order_shipping_address_country_iso_code_alpha2' => 'setOrderShippingAddressCountryIsoCodeAlpha2',
        'order_shipping_phone' => 'setOrderShippingPhone',
        'order_shipping_mobile_phone' => 'setOrderShippingMobilePhone',
        'order_shipping_price' => 'setOrderShippingPrice',
        'order_shipping_method' => 'setOrderShippingMethod',
        'order_shipping_shipping_tax' => 'setOrderShippingShippingTax',
        'order_shipping_earliest_ship_utc_date' => 'setOrderShippingEarliestShipUtcDate',
        'order_shipping_latest_ship_utc_date' => 'setOrderShippingLatestShipUtcDate',
        'order_buyer_identifier' => 'setOrderBuyerIdentifier',
        'order_buyer_civility' => 'setOrderBuyerCivility',
        'order_buyer_company_name' => 'setOrderBuyerCompanyName',
        'order_buyer_email' => 'setOrderBuyerEmail',
        'order_buyer_address_line1' => 'setOrderBuyerAddressLine1',
        'order_buyer_address_line2' => 'setOrderBuyerAddressLine2',
        'order_buyer_address_line3' => 'setOrderBuyerAddressLine3',
        'order_buyer_address_postal_code' => 'setOrderBuyerAddressPostalCode',
        'order_buyer_address_city' => 'setOrderBuyerAddressCity',
        'order_buyer_address_state_or_region' => 'setOrderBuyerAddressStateOrRegion',
        'order_buyer_address_country_name' => 'setOrderBuyerAddressCountryName',
        'order_buyer_address_country_iso_code_alpha2' => 'setOrderBuyerAddressCountryIsoCodeAlpha2',
        'order_buyer_phone' => 'setOrderBuyerPhone',
        'order_buyer_mobile_phone' => 'setOrderBuyerMobilePhone',
        'order_order_source_uri' => 'setOrderOrderSourceUri',
        'order_order_items_source_uri' => 'setOrderOrderItemsSourceUri',
        'order_items' => 'setOrderItems',
        'transition_links' => 'setTransitionLinks',
        'order_fulfilled_by' => 'setOrderFulfilledBy',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'marketplace_technical_code' => 'getMarketplaceTechnicalCode',
        'account_id' => 'getAccountId',
        'beez_up_order_id' => 'getBeezUpOrderId',
        'beez_up_order_url' => 'getBeezUpOrderUrl',
        'marketplace_business_code' => 'getMarketplaceBusinessCode',
        'order_marketplace_order_id' => 'getOrderMarketplaceOrderId',
        'order_status_beez_up_order_status' => 'getOrderStatusBeezUpOrderStatus',
        'order_status_marketplace_order_status' => 'getOrderStatusMarketplaceOrderStatus',
        'order_merchant_order_id' => 'getOrderMerchantOrderId',
        'order_merchant_e_commerce_software_name' => 'getOrderMerchantECommerceSoftwareName',
        'order_merchant_e_commerce_software_version' => 'getOrderMerchantECommerceSoftwareVersion',
        'order_purchase_utc_date' => 'getOrderPurchaseUtcDate',
        'order_last_modification_utc_date' => 'getOrderLastModificationUtcDate',
        'order_marketplace_last_modification_utc_date' => 'getOrderMarketplaceLastModificationUtcDate',
        'order_buyer_name' => 'getOrderBuyerName',
        'order_total_price' => 'getOrderTotalPrice',
        'order_currency_code' => 'getOrderCurrencyCode',
        'processing' => 'getProcessing',
        'etag' => 'getEtag',
        'links' => 'getLinks',
        'order_market_place_channel' => 'getOrderMarketPlaceChannel',
        'order_total_tax' => 'getOrderTotalTax',
        'order_total_commission' => 'getOrderTotalCommission',
        'order_payment_method' => 'getOrderPaymentMethod',
        'order_paying_utc_date' => 'getOrderPayingUtcDate',
        'order_comment' => 'getOrderComment',
        'order_shipping_civility' => 'getOrderShippingCivility',
        'order_shipping_company_name' => 'getOrderShippingCompanyName',
        'order_shipping_address_name' => 'getOrderShippingAddressName',
        'order_shipping_email' => 'getOrderShippingEmail',
        'order_shipping_address_line1' => 'getOrderShippingAddressLine1',
        'order_shipping_address_line2' => 'getOrderShippingAddressLine2',
        'order_shipping_address_line3' => 'getOrderShippingAddressLine3',
        'order_shipping_address_postal_code' => 'getOrderShippingAddressPostalCode',
        'order_shipping_address_city' => 'getOrderShippingAddressCity',
        'order_shipping_address_state_or_region' => 'getOrderShippingAddressStateOrRegion',
        'order_shipping_address_country_name' => 'getOrderShippingAddressCountryName',
        'order_shipping_address_country_iso_code_alpha2' => 'getOrderShippingAddressCountryIsoCodeAlpha2',
        'order_shipping_phone' => 'getOrderShippingPhone',
        'order_shipping_mobile_phone' => 'getOrderShippingMobilePhone',
        'order_shipping_price' => 'getOrderShippingPrice',
        'order_shipping_method' => 'getOrderShippingMethod',
        'order_shipping_shipping_tax' => 'getOrderShippingShippingTax',
        'order_shipping_earliest_ship_utc_date' => 'getOrderShippingEarliestShipUtcDate',
        'order_shipping_latest_ship_utc_date' => 'getOrderShippingLatestShipUtcDate',
        'order_buyer_identifier' => 'getOrderBuyerIdentifier',
        'order_buyer_civility' => 'getOrderBuyerCivility',
        'order_buyer_company_name' => 'getOrderBuyerCompanyName',
        'order_buyer_email' => 'getOrderBuyerEmail',
        'order_buyer_address_line1' => 'getOrderBuyerAddressLine1',
        'order_buyer_address_line2' => 'getOrderBuyerAddressLine2',
        'order_buyer_address_line3' => 'getOrderBuyerAddressLine3',
        'order_buyer_address_postal_code' => 'getOrderBuyerAddressPostalCode',
        'order_buyer_address_city' => 'getOrderBuyerAddressCity',
        'order_buyer_address_state_or_region' => 'getOrderBuyerAddressStateOrRegion',
        'order_buyer_address_country_name' => 'getOrderBuyerAddressCountryName',
        'order_buyer_address_country_iso_code_alpha2' => 'getOrderBuyerAddressCountryIsoCodeAlpha2',
        'order_buyer_phone' => 'getOrderBuyerPhone',
        'order_buyer_mobile_phone' => 'getOrderBuyerMobilePhone',
        'order_order_source_uri' => 'getOrderOrderSourceUri',
        'order_order_items_source_uri' => 'getOrderOrderItemsSourceUri',
        'order_items' => 'getOrderItems',
        'transition_links' => 'getTransitionLinks',
        'order_fulfilled_by' => 'getOrderFulfilledBy',
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['marketplace_technical_code'] = isset($data['marketplace_technical_code']) ? $data['marketplace_technical_code'] : null;
        $this->container['account_id'] = isset($data['account_id']) ? $data['account_id'] : null;
        $this->container['beez_up_order_id'] = isset($data['beez_up_order_id']) ? $data['beez_up_order_id'] : null;
        $this->container['beez_up_order_url'] = isset($data['beez_up_order_url']) ? $data['beez_up_order_url'] : null;
        $this->container['marketplace_business_code'] = isset($data['marketplace_business_code']) ? $data['marketplace_business_code'] : null;
        $this->container['order_marketplace_order_id'] = isset($data['order_marketplace_order_id']) ? $data['order_marketplace_order_id'] : null;
        $this->container['order_status_beez_up_order_status'] = isset($data['order_status_beez_up_order_status']) ? $data['order_status_beez_up_order_status'] : null;
        $this->container['order_status_marketplace_order_status'] = isset($data['order_status_marketplace_order_status']) ? $data['order_status_marketplace_order_status'] : null;
        $this->container['order_merchant_order_id'] = isset($data['order_merchant_order_id']) ? $data['order_merchant_order_id'] : null;
        $this->container['order_merchant_e_commerce_software_name'] = isset($data['order_merchant_e_commerce_software_name']) ? $data['order_merchant_e_commerce_software_name'] : null;
        $this->container['order_merchant_e_commerce_software_version'] = isset($data['order_merchant_e_commerce_software_version']) ? $data['order_merchant_e_commerce_software_version'] : null;
        $this->container['order_purchase_utc_date'] = isset($data['order_purchase_utc_date']) ? $data['order_purchase_utc_date'] : null;
        $this->container['order_last_modification_utc_date'] = isset($data['order_last_modification_utc_date']) ? $data['order_last_modification_utc_date'] : null;
        $this->container['order_marketplace_last_modification_utc_date'] = isset($data['order_marketplace_last_modification_utc_date']) ? $data['order_marketplace_last_modification_utc_date'] : null;
        $this->container['order_buyer_name'] = isset($data['order_buyer_name']) ? $data['order_buyer_name'] : null;
        $this->container['order_total_price'] = isset($data['order_total_price']) ? $data['order_total_price'] : null;
        $this->container['order_currency_code'] = isset($data['order_currency_code']) ? $data['order_currency_code'] : null;
        $this->container['processing'] = isset($data['processing']) ? $data['processing'] : null;
        $this->container['etag'] = isset($data['etag']) ? $data['etag'] : null;
        $this->container['links'] = isset($data['links']) ? $data['links'] : null;
        $this->container['order_market_place_channel'] = isset($data['order_market_place_channel']) ? $data['order_market_place_channel'] : null;
        $this->container['order_total_tax'] = isset($data['order_total_tax']) ? $data['order_total_tax'] : null;
        $this->container['order_total_commission'] = isset($data['order_total_commission']) ? $data['order_total_commission'] : null;
        $this->container['order_payment_method'] = isset($data['order_payment_method']) ? $data['order_payment_method'] : null;
        $this->container['order_paying_utc_date'] = isset($data['order_paying_utc_date']) ? $data['order_paying_utc_date'] : null;
        $this->container['order_comment'] = isset($data['order_comment']) ? $data['order_comment'] : null;
        $this->container['order_shipping_civility'] = isset($data['order_shipping_civility']) ? $data['order_shipping_civility'] : null;
        $this->container['order_shipping_company_name'] = isset($data['order_shipping_company_name']) ? $data['order_shipping_company_name'] : null;
        $this->container['order_shipping_address_name'] = isset($data['order_shipping_address_name']) ? $data['order_shipping_address_name'] : null;
        $this->container['order_shipping_email'] = isset($data['order_shipping_email']) ? $data['order_shipping_email'] : null;
        $this->container['order_shipping_address_line1'] = isset($data['order_shipping_address_line1']) ? $data['order_shipping_address_line1'] : null;
        $this->container['order_shipping_address_line2'] = isset($data['order_shipping_address_line2']) ? $data['order_shipping_address_line2'] : null;
        $this->container['order_shipping_address_line3'] = isset($data['order_shipping_address_line3']) ? $data['order_shipping_address_line3'] : null;
        $this->container['order_shipping_address_postal_code'] = isset($data['order_shipping_address_postal_code']) ? $data['order_shipping_address_postal_code'] : null;
        $this->container['order_shipping_address_city'] = isset($data['order_shipping_address_city']) ? $data['order_shipping_address_city'] : null;
        $this->container['order_shipping_address_state_or_region'] = isset($data['order_shipping_address_state_or_region']) ? $data['order_shipping_address_state_or_region'] : null;
        $this->container['order_shipping_address_country_name'] = isset($data['order_shipping_address_country_name']) ? $data['order_shipping_address_country_name'] : null;
        $this->container['order_shipping_address_country_iso_code_alpha2'] = isset($data['order_shipping_address_country_iso_code_alpha2']) ? $data['order_shipping_address_country_iso_code_alpha2'] : null;
        $this->container['order_shipping_phone'] = isset($data['order_shipping_phone']) ? $data['order_shipping_phone'] : null;
        $this->container['order_shipping_mobile_phone'] = isset($data['order_shipping_mobile_phone']) ? $data['order_shipping_mobile_phone'] : null;
        $this->container['order_shipping_price'] = isset($data['order_shipping_price']) ? $data['order_shipping_price'] : null;
        $this->container['order_shipping_method'] = isset($data['order_shipping_method']) ? $data['order_shipping_method'] : null;
        $this->container['order_shipping_shipping_tax'] = isset($data['order_shipping_shipping_tax']) ? $data['order_shipping_shipping_tax'] : null;
        $this->container['order_shipping_earliest_ship_utc_date'] = isset($data['order_shipping_earliest_ship_utc_date']) ? $data['order_shipping_earliest_ship_utc_date'] : null;
        $this->container['order_shipping_latest_ship_utc_date'] = isset($data['order_shipping_latest_ship_utc_date']) ? $data['order_shipping_latest_ship_utc_date'] : null;
        $this->container['order_buyer_identifier'] = isset($data['order_buyer_identifier']) ? $data['order_buyer_identifier'] : null;
        $this->container['order_buyer_civility'] = isset($data['order_buyer_civility']) ? $data['order_buyer_civility'] : null;
        $this->container['order_buyer_company_name'] = isset($data['order_buyer_company_name']) ? $data['order_buyer_company_name'] : null;
        $this->container['order_buyer_email'] = isset($data['order_buyer_email']) ? $data['order_buyer_email'] : null;
        $this->container['order_buyer_address_line1'] = isset($data['order_buyer_address_line1']) ? $data['order_buyer_address_line1'] : null;
        $this->container['order_buyer_address_line2'] = isset($data['order_buyer_address_line2']) ? $data['order_buyer_address_line2'] : null;
        $this->container['order_buyer_address_line3'] = isset($data['order_buyer_address_line3']) ? $data['order_buyer_address_line3'] : null;
        $this->container['order_buyer_address_postal_code'] = isset($data['order_buyer_address_postal_code']) ? $data['order_buyer_address_postal_code'] : null;
        $this->container['order_buyer_address_city'] = isset($data['order_buyer_address_city']) ? $data['order_buyer_address_city'] : null;
        $this->container['order_buyer_address_state_or_region'] = isset($data['order_buyer_address_state_or_region']) ? $data['order_buyer_address_state_or_region'] : null;
        $this->container['order_buyer_address_country_name'] = isset($data['order_buyer_address_country_name']) ? $data['order_buyer_address_country_name'] : null;
        $this->container['order_buyer_address_country_iso_code_alpha2'] = isset($data['order_buyer_address_country_iso_code_alpha2']) ? $data['order_buyer_address_country_iso_code_alpha2'] : null;
        $this->container['order_buyer_phone'] = isset($data['order_buyer_phone']) ? $data['order_buyer_phone'] : null;
        $this->container['order_buyer_mobile_phone'] = isset($data['order_buyer_mobile_phone']) ? $data['order_buyer_mobile_phone'] : null;
        $this->container['order_order_source_uri'] = isset($data['order_order_source_uri']) ? $data['order_order_source_uri'] : null;
        $this->container['order_order_items_source_uri'] = isset($data['order_order_items_source_uri']) ? $data['order_order_items_source_uri'] : null;
        $this->container['order_items'] = isset($data['order_items']) ? $data['order_items'] : null;
        $this->container['transition_links'] = isset($data['transition_links']) ? $data['transition_links'] : null;
        $this->container['order_fulfilled_by'] = isset($data['order_fulfilled_by']) ? $data['order_fulfilled_by'] : null;

    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['marketplace_technical_code'] === null) {
            $invalidProperties[] = "'marketplace_technical_code' can't be null";
        }
        if ($this->container['account_id'] === null) {
            $invalidProperties[] = "'account_id' can't be null";
        }
        if ($this->container['beez_up_order_id'] === null) {
            $invalidProperties[] = "'beez_up_order_id' can't be null";
        }
        if ($this->container['marketplace_business_code'] === null) {
            $invalidProperties[] = "'marketplace_business_code' can't be null";
        }
        if ($this->container['order_marketplace_order_id'] === null) {
            $invalidProperties[] = "'order_marketplace_order_id' can't be null";
        }
        if ($this->container['order_status_beez_up_order_status'] === null) {
            $invalidProperties[] = "'order_status_beez_up_order_status' can't be null";
        }
        if ($this->container['order_purchase_utc_date'] === null) {
            $invalidProperties[] = "'order_purchase_utc_date' can't be null";
        }
        if ($this->container['order_last_modification_utc_date'] === null) {
            $invalidProperties[] = "'order_last_modification_utc_date' can't be null";
        }
        if ($this->container['order_marketplace_last_modification_utc_date'] === null) {
            $invalidProperties[] = "'order_marketplace_last_modification_utc_date' can't be null";
        }
        if ($this->container['processing'] === null) {
            $invalidProperties[] = "'processing' can't be null";
        }
        if ($this->container['etag'] === null) {
            $invalidProperties[] = "'etag' can't be null";
        }
        if ($this->container['links'] === null) {
            $invalidProperties[] = "'links' can't be null";
        }
        if ($this->container['order_items'] === null) {
            $invalidProperties[] = "'order_items' can't be null";
        }
        if ($this->container['transition_links'] === null) {
            $invalidProperties[] = "'transition_links' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['marketplace_technical_code'] === null) {
            return false;
        }
        if ($this->container['account_id'] === null) {
            return false;
        }
        if ($this->container['beez_up_order_id'] === null) {
            return false;
        }
        if ($this->container['marketplace_business_code'] === null) {
            return false;
        }
        if ($this->container['order_marketplace_order_id'] === null) {
            return false;
        }
        if ($this->container['order_status_beez_up_order_status'] === null) {
            return false;
        }
        if ($this->container['order_purchase_utc_date'] === null) {
            return false;
        }
        if ($this->container['order_last_modification_utc_date'] === null) {
            return false;
        }
        if ($this->container['order_marketplace_last_modification_utc_date'] === null) {
            return false;
        }
        if ($this->container['processing'] === null) {
            return false;
        }
        if ($this->container['etag'] === null) {
            return false;
        }
        if ($this->container['links'] === null) {
            return false;
        }
        if ($this->container['order_items'] === null) {
            return false;
        }
        if ($this->container['transition_links'] === null) {
            return false;
        }
        return true;
    }






    /**
     * Sets order_fulfilled_by
     *
     * @param string $order_fulfilled_by
     *
     * @return $this
     */
    public function setOrderFulfilledBy($order_fulfilled_by)
    {
        $this->container['order_fulfilled_by'] = $order_fulfilled_by;

        return $this;
    }

    /**
     * Gets order_fulfilled_by
     *
     * @return string
     */
    public function getOrderFulfilledBy()
    {
        return $this->container['order_fulfilled_by'];
    }


    /**
     * Gets marketplace_technical_code
     *
     * @return \Swagger\Client\Model\BeezUPCommonMarketplaceTechnicalCode
     */
    public function getMarketplaceTechnicalCode()
    {
        return $this->container['marketplace_technical_code'];
    }

    /**
     * Sets marketplace_technical_code
     *
     * @param \Swagger\Client\Model\BeezUPCommonMarketplaceTechnicalCode $marketplace_technical_code marketplace_technical_code
     *
     * @return $this
     */
    public function setMarketplaceTechnicalCode($marketplace_technical_code)
    {
        $this->container['marketplace_technical_code'] = $marketplace_technical_code;

        return $this;
    }

    /**
     * Gets account_id
     *
     * @return \Swagger\Client\Model\AccountId
     */
    public function getAccountId()
    {
        return $this->container['account_id'];
    }

    /**
     * Sets account_id
     *
     * @param \Swagger\Client\Model\AccountId $account_id account_id
     *
     * @return $this
     */
    public function setAccountId($account_id)
    {
        $this->container['account_id'] = $account_id;

        return $this;
    }

    /**
     * Gets beez_up_order_id
     *
     * @return \Swagger\Client\Model\BeezUPOrderId
     */
    public function getBeezUpOrderId()
    {
        return $this->container['beez_up_order_id'];
    }

    /**
     * Sets beez_up_order_id
     *
     * @param \Swagger\Client\Model\BeezUPOrderId $beez_up_order_id beez_up_order_id
     *
     * @return $this
     */
    public function setBeezUpOrderId($beez_up_order_id)
    {
        $this->container['beez_up_order_id'] = $beez_up_order_id;

        return $this;
    }

    /**
     * Gets beez_up_order_url
     *
     * @return \Swagger\Client\Model\BeezUPCommonHttpUrl
     */
    public function getBeezUpOrderUrl()
    {
        return $this->container['beez_up_order_url'];
    }

    /**
     * Sets beez_up_order_url
     *
     * @param \Swagger\Client\Model\BeezUPCommonHttpUrl $beez_up_order_url beez_up_order_url
     *
     * @return $this
     */
    public function setBeezUpOrderUrl($beez_up_order_url)
    {
        $this->container['beez_up_order_url'] = $beez_up_order_url;

        return $this;
    }

    /**
     * Gets marketplace_business_code
     *
     * @return \Swagger\Client\Model\BeezUPCommonMarketplaceBusinessCode
     */
    public function getMarketplaceBusinessCode()
    {
        return $this->container['marketplace_business_code'];
    }

    /**
     * Sets marketplace_business_code
     *
     * @param \Swagger\Client\Model\BeezUPCommonMarketplaceBusinessCode $marketplace_business_code marketplace_business_code
     *
     * @return $this
     */
    public function setMarketplaceBusinessCode($marketplace_business_code)
    {
        $this->container['marketplace_business_code'] = $marketplace_business_code;

        return $this;
    }

    /**
     * Gets order_marketplace_order_id
     *
     * @return \Swagger\Client\Model\MarketplaceOrderId
     */
    public function getOrderMarketplaceOrderId()
    {
        return $this->container['order_marketplace_order_id'];
    }

    /**
     * Sets order_marketplace_order_id
     *
     * @param \Swagger\Client\Model\MarketplaceOrderId $order_marketplace_order_id order_marketplace_order_id
     *
     * @return $this
     */
    public function setOrderMarketplaceOrderId($order_marketplace_order_id)
    {
        $this->container['order_marketplace_order_id'] = $order_marketplace_order_id;

        return $this;
    }

    /**
     * Gets order_status_beez_up_order_status
     *
     * @return \Swagger\Client\Model\BeezUPOrderStatus
     */
    public function getOrderStatusBeezUpOrderStatus()
    {
        return $this->container['order_status_beez_up_order_status'];
    }

    /**
     * Sets order_status_beez_up_order_status
     *
     * @param \Swagger\Client\Model\BeezUPOrderStatus $order_status_beez_up_order_status order_status_beez_up_order_status
     *
     * @return $this
     */
    public function setOrderStatusBeezUpOrderStatus($order_status_beez_up_order_status)
    {
        $this->container['order_status_beez_up_order_status'] = $order_status_beez_up_order_status;

        return $this;
    }

    /**
     * Gets order_status_marketplace_order_status
     *
     * @return \Swagger\Client\Model\MarketplaceOrderStatus
     */
    public function getOrderStatusMarketplaceOrderStatus()
    {
        return $this->container['order_status_marketplace_order_status'];
    }

    /**
     * Sets order_status_marketplace_order_status
     *
     * @param \Swagger\Client\Model\MarketplaceOrderStatus $order_status_marketplace_order_status order_status_marketplace_order_status
     *
     * @return $this
     */
    public function setOrderStatusMarketplaceOrderStatus($order_status_marketplace_order_status)
    {
        $this->container['order_status_marketplace_order_status'] = $order_status_marketplace_order_status;

        return $this;
    }

    /**
     * Gets order_merchant_order_id
     *
     * @return \Swagger\Client\Model\OrderMerchantOrderId
     */
    public function getOrderMerchantOrderId()
    {
        return $this->container['order_merchant_order_id'];
    }

    /**
     * Sets order_merchant_order_id
     *
     * @param \Swagger\Client\Model\OrderMerchantOrderId $order_merchant_order_id order_merchant_order_id
     *
     * @return $this
     */
    public function setOrderMerchantOrderId($order_merchant_order_id)
    {
        $this->container['order_merchant_order_id'] = $order_merchant_order_id;

        return $this;
    }

    /**
     * Gets order_merchant_e_commerce_software_name
     *
     * @return \Swagger\Client\Model\OrderMerchantECommerceSoftwareName
     */
    public function getOrderMerchantECommerceSoftwareName()
    {
        return $this->container['order_merchant_e_commerce_software_name'];
    }

    /**
     * Sets order_merchant_e_commerce_software_name
     *
     * @param \Swagger\Client\Model\OrderMerchantECommerceSoftwareName $order_merchant_e_commerce_software_name order_merchant_e_commerce_software_name
     *
     * @return $this
     */
    public function setOrderMerchantECommerceSoftwareName($order_merchant_e_commerce_software_name)
    {
        $this->container['order_merchant_e_commerce_software_name'] = $order_merchant_e_commerce_software_name;

        return $this;
    }

    /**
     * Gets order_merchant_e_commerce_software_version
     *
     * @return \Swagger\Client\Model\OrderMerchantECommerceSoftwareVersion
     */
    public function getOrderMerchantECommerceSoftwareVersion()
    {
        return $this->container['order_merchant_e_commerce_software_version'];
    }

    /**
     * Sets order_merchant_e_commerce_software_version
     *
     * @param \Swagger\Client\Model\OrderMerchantECommerceSoftwareVersion $order_merchant_e_commerce_software_version order_merchant_e_commerce_software_version
     *
     * @return $this
     */
    public function setOrderMerchantECommerceSoftwareVersion($order_merchant_e_commerce_software_version)
    {
        $this->container['order_merchant_e_commerce_software_version'] = $order_merchant_e_commerce_software_version;

        return $this;
    }

    /**
     * Gets order_purchase_utc_date
     *
     * @return \DateTime
     */
    public function getOrderPurchaseUtcDate()
    {
        return $this->container['order_purchase_utc_date'];
    }

    /**
     * Sets order_purchase_utc_date
     *
     * @param \DateTime $order_purchase_utc_date The purchase date of this order
     *
     * @return $this
     */
    public function setOrderPurchaseUtcDate($order_purchase_utc_date)
    {
        $this->container['order_purchase_utc_date'] = $order_purchase_utc_date;

        return $this;
    }

    /**
     * Gets order_last_modification_utc_date
     *
     * @return \DateTime
     */
    public function getOrderLastModificationUtcDate()
    {
        return $this->container['order_last_modification_utc_date'];
    }

    /**
     * Sets order_last_modification_utc_date
     *
     * @param \DateTime $order_last_modification_utc_date The last modification UTC date done by BeezUP of this order
     *
     * @return $this
     */
    public function setOrderLastModificationUtcDate($order_last_modification_utc_date)
    {
        $this->container['order_last_modification_utc_date'] = $order_last_modification_utc_date;

        return $this;
    }

    /**
     * Gets order_marketplace_last_modification_utc_date
     *
     * @return \DateTime
     */
    public function getOrderMarketplaceLastModificationUtcDate()
    {
        return $this->container['order_marketplace_last_modification_utc_date'];
    }

    /**
     * Sets order_marketplace_last_modification_utc_date
     *
     * @param \DateTime $order_marketplace_last_modification_utc_date The last modification UTC date done by the marketplace on this order
     *
     * @return $this
     */
    public function setOrderMarketplaceLastModificationUtcDate($order_marketplace_last_modification_utc_date)
    {
        $this->container['order_marketplace_last_modification_utc_date'] = $order_marketplace_last_modification_utc_date;

        return $this;
    }

    /**
     * Gets order_buyer_name
     *
     * @return \Swagger\Client\Model\OrderBuyerName
     */
    public function getOrderBuyerName()
    {
        return $this->container['order_buyer_name'];
    }

    /**
     * Sets order_buyer_name
     *
     * @param \Swagger\Client\Model\OrderBuyerName $order_buyer_name order_buyer_name
     *
     * @return $this
     */
    public function setOrderBuyerName($order_buyer_name)
    {
        $this->container['order_buyer_name'] = $order_buyer_name;

        return $this;
    }

    /**
     * Gets order_total_price
     *
     * @return float
     */
    public function getOrderTotalPrice()
    {
        return $this->container['order_total_price'];
    }

    /**
     * Sets order_total_price
     *
     * @param float $order_total_price The total price of this order (corresponding to the amount paid by the customer)
     *
     * @return $this
     */
    public function setOrderTotalPrice($order_total_price)
    {
        $this->container['order_total_price'] = $order_total_price;

        return $this;
    }

    /**
     * Gets order_currency_code
     *
     * @return \Swagger\Client\Model\BeezUPCommonCurrencyCode
     */
    public function getOrderCurrencyCode()
    {
        return $this->container['order_currency_code'];
    }

    /**
     * Sets order_currency_code
     *
     * @param \Swagger\Client\Model\BeezUPCommonCurrencyCode $order_currency_code order_currency_code
     *
     * @return $this
     */
    public function setOrderCurrencyCode($order_currency_code)
    {
        $this->container['order_currency_code'] = $order_currency_code;

        return $this;
    }

    /**
     * Gets processing
     *
     * @return \Swagger\Client\Model\Processing
     */
    public function getProcessing()
    {
        return $this->container['processing'];
    }

    /**
     * Sets processing
     *
     * @param \Swagger\Client\Model\Processing $processing processing
     *
     * @return $this
     */
    public function setProcessing($processing)
    {
        $this->container['processing'] = $processing;

        return $this;
    }

    /**
     * Gets etag
     *
     * @return \Swagger\Client\Model\Etag
     */
    public function getEtag()
    {
        return $this->container['etag'];
    }

    /**
     * Sets etag
     *
     * @param \Swagger\Client\Model\Etag $etag etag
     *
     * @return $this
     */
    public function setEtag($etag)
    {
        $this->container['etag'] = $etag;

        return $this;
    }

    /**
     * Gets links
     *
     * @return \Swagger\Client\Model\OrderLinks
     */
    public function getLinks()
    {
        return $this->container['links'];
    }

    /**
     * Sets links
     *
     * @param \Swagger\Client\Model\OrderLinks $links links
     *
     * @return $this
     */
    public function setLinks($links)
    {
        $this->container['links'] = $links;

        return $this;
    }

    /**
     * Gets order_market_place_channel
     *
     * @return string
     */
    public function getOrderMarketPlaceChannel()
    {
        return $this->container['order_market_place_channel'];
    }

    /**
     * Sets order_market_place_channel
     *
     * @param string $order_market_place_channel Useful to identify the origin of the order. For example in Amazon.
     *
     * @return $this
     */
    public function setOrderMarketPlaceChannel($order_market_place_channel)
    {
        $this->container['order_market_place_channel'] = $order_market_place_channel;

        return $this;
    }

    /**
     * Gets order_total_tax
     *
     * @return float
     */
    public function getOrderTotalTax()
    {
        return $this->container['order_total_tax'];
    }

    /**
     * Sets order_total_tax
     *
     * @param float $order_total_tax The total tax of this order
     *
     * @return $this
     */
    public function setOrderTotalTax($order_total_tax)
    {
        $this->container['order_total_tax'] = $order_total_tax;

        return $this;
    }

    /**
     * Gets order_total_commission
     *
     * @return float
     */
    public function getOrderTotalCommission()
    {
        return $this->container['order_total_commission'];
    }

    /**
     * Sets order_total_commission
     *
     * @param float $order_total_commission The total commission of this order
     *
     * @return $this
     */
    public function setOrderTotalCommission($order_total_commission)
    {
        $this->container['order_total_commission'] = $order_total_commission;

        return $this;
    }

    /**
     * Gets order_payment_method
     *
     * @return string
     */
    public function getOrderPaymentMethod()
    {
        return $this->container['order_payment_method'];
    }

    /**
     * Sets order_payment_method
     *
     * @param string $order_payment_method The payment method of this order
     *
     * @return $this
     */
    public function setOrderPaymentMethod($order_payment_method)
    {
        $this->container['order_payment_method'] = $order_payment_method;

        return $this;
    }

    /**
     * Gets order_paying_utc_date
     *
     * @return \DateTime
     */
    public function getOrderPayingUtcDate()
    {
        return $this->container['order_paying_utc_date'];
    }

    /**
     * Sets order_paying_utc_date
     *
     * @param \DateTime $order_paying_utc_date The UTC date of the payment of this order
     *
     * @return $this
     */
    public function setOrderPayingUtcDate($order_paying_utc_date)
    {
        $this->container['order_paying_utc_date'] = $order_paying_utc_date;

        return $this;
    }

    /**
     * Gets order_comment
     *
     * @return string
     */
    public function getOrderComment()
    {
        return $this->container['order_comment'];
    }

    /**
     * Sets order_comment
     *
     * @param string $order_comment The comment associated to this order
     *
     * @return $this
     */
    public function setOrderComment($order_comment)
    {
        $this->container['order_comment'] = $order_comment;

        return $this;
    }

    /**
     * Gets order_shipping_civility
     *
     * @return string
     */
    public function getOrderShippingCivility()
    {
        return $this->container['order_shipping_civility'];
    }

    /**
     * Sets order_shipping_civility
     *
     * @param string $order_shipping_civility The civility of the person in the shipping address for this order
     *
     * @return $this
     */
    public function setOrderShippingCivility($order_shipping_civility)
    {
        $this->container['order_shipping_civility'] = $order_shipping_civility;

        return $this;
    }

    /**
     * Gets order_shipping_company_name
     *
     * @return string
     */
    public function getOrderShippingCompanyName()
    {
        return $this->container['order_shipping_company_name'];
    }

    /**
     * Sets order_shipping_company_name
     *
     * @param string $order_shipping_company_name The company name of the shipping address for this order
     *
     * @return $this
     */
    public function setOrderShippingCompanyName($order_shipping_company_name)
    {
        $this->container['order_shipping_company_name'] = $order_shipping_company_name;

        return $this;
    }

    /**
     * Gets order_shipping_address_name
     *
     * @return string
     */
    public function getOrderShippingAddressName()
    {
        return $this->container['order_shipping_address_name'];
    }

    /**
     * Sets order_shipping_address_name
     *
     * @param string $order_shipping_address_name The name of the person in the shipping address for this order
     *
     * @return $this
     */
    public function setOrderShippingAddressName($order_shipping_address_name)
    {
        $this->container['order_shipping_address_name'] = $order_shipping_address_name;

        return $this;
    }

    /**
     * Gets order_shipping_email
     *
     * @return string
     */
    public function getOrderShippingEmail()
    {
        return $this->container['order_shipping_email'];
    }

    /**
     * Sets order_shipping_email
     *
     * @param string $order_shipping_email The email of the person in the shipping address for this order
     *
     * @return $this
     */
    public function setOrderShippingEmail($order_shipping_email)
    {
        $this->container['order_shipping_email'] = $order_shipping_email;

        return $this;
    }

    /**
     * Gets order_shipping_address_line1
     *
     * @return string
     */
    public function getOrderShippingAddressLine1()
    {
        return $this->container['order_shipping_address_line1'];
    }

    /**
     * Sets order_shipping_address_line1
     *
     * @param string $order_shipping_address_line1 The shipping address line 1 of this order
     *
     * @return $this
     */
    public function setOrderShippingAddressLine1($order_shipping_address_line1)
    {
        $this->container['order_shipping_address_line1'] = $order_shipping_address_line1;

        return $this;
    }

    /**
     * Gets order_shipping_address_line2
     *
     * @return string
     */
    public function getOrderShippingAddressLine2()
    {
        return $this->container['order_shipping_address_line2'];
    }

    /**
     * Sets order_shipping_address_line2
     *
     * @param string $order_shipping_address_line2 The shipping address line 2 of this order
     *
     * @return $this
     */
    public function setOrderShippingAddressLine2($order_shipping_address_line2)
    {
        $this->container['order_shipping_address_line2'] = $order_shipping_address_line2;

        return $this;
    }

    /**
     * Gets order_shipping_address_line3
     *
     * @return string
     */
    public function getOrderShippingAddressLine3()
    {
        return $this->container['order_shipping_address_line3'];
    }

    /**
     * Sets order_shipping_address_line3
     *
     * @param string $order_shipping_address_line3 The shipping address line 3 of this order
     *
     * @return $this
     */
    public function setOrderShippingAddressLine3($order_shipping_address_line3)
    {
        $this->container['order_shipping_address_line3'] = $order_shipping_address_line3;

        return $this;
    }

    /**
     * Gets order_shipping_address_postal_code
     *
     * @return string
     */
    public function getOrderShippingAddressPostalCode()
    {
        return $this->container['order_shipping_address_postal_code'];
    }

    /**
     * Sets order_shipping_address_postal_code
     *
     * @param string $order_shipping_address_postal_code The shipping address postal code of this order
     *
     * @return $this
     */
    public function setOrderShippingAddressPostalCode($order_shipping_address_postal_code)
    {
        $this->container['order_shipping_address_postal_code'] = $order_shipping_address_postal_code;

        return $this;
    }

    /**
     * Gets order_shipping_address_city
     *
     * @return string
     */
    public function getOrderShippingAddressCity()
    {
        return $this->container['order_shipping_address_city'];
    }

    /**
     * Sets order_shipping_address_city
     *
     * @param string $order_shipping_address_city The shipping address city of this order
     *
     * @return $this
     */
    public function setOrderShippingAddressCity($order_shipping_address_city)
    {
        $this->container['order_shipping_address_city'] = $order_shipping_address_city;

        return $this;
    }

    /**
     * Gets order_shipping_address_state_or_region
     *
     * @return string
     */
    public function getOrderShippingAddressStateOrRegion()
    {
        return $this->container['order_shipping_address_state_or_region'];
    }

    /**
     * Sets order_shipping_address_state_or_region
     *
     * @param string $order_shipping_address_state_or_region The shipping address state or region of this order
     *
     * @return $this
     */
    public function setOrderShippingAddressStateOrRegion($order_shipping_address_state_or_region)
    {
        $this->container['order_shipping_address_state_or_region'] = $order_shipping_address_state_or_region;

        return $this;
    }

    /**
     * Gets order_shipping_address_country_name
     *
     * @return string
     */
    public function getOrderShippingAddressCountryName()
    {
        return $this->container['order_shipping_address_country_name'];
    }

    /**
     * Sets order_shipping_address_country_name
     *
     * @param string $order_shipping_address_country_name The shipping address country name
     *
     * @return $this
     */
    public function setOrderShippingAddressCountryName($order_shipping_address_country_name)
    {
        $this->container['order_shipping_address_country_name'] = $order_shipping_address_country_name;

        return $this;
    }

    /**
     * Gets order_shipping_address_country_iso_code_alpha2
     *
     * @return string
     */
    public function getOrderShippingAddressCountryIsoCodeAlpha2()
    {
        return $this->container['order_shipping_address_country_iso_code_alpha2'];
    }

    /**
     * Sets order_shipping_address_country_iso_code_alpha2
     *
     * @param string $order_shipping_address_country_iso_code_alpha2 The shipping address country iso code alpha 2 (see http://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#/decoding_table for more details)
     *
     * @return $this
     */
    public function setOrderShippingAddressCountryIsoCodeAlpha2($order_shipping_address_country_iso_code_alpha2)
    {
        $this->container['order_shipping_address_country_iso_code_alpha2'] = $order_shipping_address_country_iso_code_alpha2;

        return $this;
    }

    /**
     * Gets order_shipping_phone
     *
     * @return string
     */
    public function getOrderShippingPhone()
    {
        return $this->container['order_shipping_phone'];
    }

    /**
     * Sets order_shipping_phone
     *
     * @param string $order_shipping_phone The phone number of the person in the shipping address for this order
     *
     * @return $this
     */
    public function setOrderShippingPhone($order_shipping_phone)
    {
        $this->container['order_shipping_phone'] = $order_shipping_phone;

        return $this;
    }

    /**
     * Gets order_shipping_mobile_phone
     *
     * @return string
     */
    public function getOrderShippingMobilePhone()
    {
        return $this->container['order_shipping_mobile_phone'];
    }

    /**
     * Sets order_shipping_mobile_phone
     *
     * @param string $order_shipping_mobile_phone The mobile phone number of the person in the shipping address for this order
     *
     * @return $this
     */
    public function setOrderShippingMobilePhone($order_shipping_mobile_phone)
    {
        $this->container['order_shipping_mobile_phone'] = $order_shipping_mobile_phone;

        return $this;
    }

    /**
     * Gets order_shipping_price
     *
     * @return float
     */
    public function getOrderShippingPrice()
    {
        return $this->container['order_shipping_price'];
    }

    /**
     * Sets order_shipping_price
     *
     * @param float $order_shipping_price The shipping price of this order
     *
     * @return $this
     */
    public function setOrderShippingPrice($order_shipping_price)
    {
        $this->container['order_shipping_price'] = $order_shipping_price;

        return $this;
    }

    /**
     * Gets order_shipping_method
     *
     * @return string
     */
    public function getOrderShippingMethod()
    {
        return $this->container['order_shipping_method'];
    }

    /**
     * Sets order_shipping_method
     *
     * @param string $order_shipping_method The shipping method of this order
     *
     * @return $this
     */
    public function setOrderShippingMethod($order_shipping_method)
    {
        $this->container['order_shipping_method'] = $order_shipping_method;

        return $this;
    }

    /**
     * Gets order_shipping_shipping_tax
     *
     * @return float
     */
    public function getOrderShippingShippingTax()
    {
        return $this->container['order_shipping_shipping_tax'];
    }

    /**
     * Sets order_shipping_shipping_tax
     *
     * @param float $order_shipping_shipping_tax The shipping tax for this order
     *
     * @return $this
     */
    public function setOrderShippingShippingTax($order_shipping_shipping_tax)
    {
        $this->container['order_shipping_shipping_tax'] = $order_shipping_shipping_tax;

        return $this;
    }

    /**
     * Gets order_shipping_earliest_ship_utc_date
     *
     * @return \DateTime
     */
    public function getOrderShippingEarliestShipUtcDate()
    {
        return $this->container['order_shipping_earliest_ship_utc_date'];
    }

    /**
     * Sets order_shipping_earliest_ship_utc_date
     *
     * @param \DateTime $order_shipping_earliest_ship_utc_date The UTC date of the earliest ship for this order
     *
     * @return $this
     */
    public function setOrderShippingEarliestShipUtcDate($order_shipping_earliest_ship_utc_date)
    {
        $this->container['order_shipping_earliest_ship_utc_date'] = $order_shipping_earliest_ship_utc_date;

        return $this;
    }

    /**
     * Gets order_shipping_latest_ship_utc_date
     *
     * @return \DateTime
     */
    public function getOrderShippingLatestShipUtcDate()
    {
        return $this->container['order_shipping_latest_ship_utc_date'];
    }

    /**
     * Sets order_shipping_latest_ship_utc_date
     *
     * @param \DateTime $order_shipping_latest_ship_utc_date The UTC date of the latest ship for this order
     *
     * @return $this
     */
    public function setOrderShippingLatestShipUtcDate($order_shipping_latest_ship_utc_date)
    {
        $this->container['order_shipping_latest_ship_utc_date'] = $order_shipping_latest_ship_utc_date;

        return $this;
    }

    /**
     * Gets order_buyer_identifier
     *
     * @return string
     */
    public function getOrderBuyerIdentifier()
    {
        return $this->container['order_buyer_identifier'];
    }

    /**
     * Sets order_buyer_identifier
     *
     * @param string $order_buyer_identifier The buyer identifier for this order
     *
     * @return $this
     */
    public function setOrderBuyerIdentifier($order_buyer_identifier)
    {
        $this->container['order_buyer_identifier'] = $order_buyer_identifier;

        return $this;
    }

    /**
     * Gets order_buyer_civility
     *
     * @return string
     */
    public function getOrderBuyerCivility()
    {
        return $this->container['order_buyer_civility'];
    }

    /**
     * Sets order_buyer_civility
     *
     * @param string $order_buyer_civility The buyer civility for this order
     *
     * @return $this
     */
    public function setOrderBuyerCivility($order_buyer_civility)
    {
        $this->container['order_buyer_civility'] = $order_buyer_civility;

        return $this;
    }

    /**
     * Gets order_buyer_company_name
     *
     * @return string
     */
    public function getOrderBuyerCompanyName()
    {
        return $this->container['order_buyer_company_name'];
    }

    /**
     * Sets order_buyer_company_name
     *
     * @param string $order_buyer_company_name The buyer company name for this order
     *
     * @return $this
     */
    public function setOrderBuyerCompanyName($order_buyer_company_name)
    {
        $this->container['order_buyer_company_name'] = $order_buyer_company_name;

        return $this;
    }

    /**
     * Gets order_buyer_email
     *
     * @return string
     */
    public function getOrderBuyerEmail()
    {
        return $this->container['order_buyer_email'];
    }

    /**
     * Sets order_buyer_email
     *
     * @param string $order_buyer_email The email of the buyer for this order
     *
     * @return $this
     */
    public function setOrderBuyerEmail($order_buyer_email)
    {
        $this->container['order_buyer_email'] = $order_buyer_email;

        return $this;
    }

    /**
     * Gets order_buyer_address_line1
     *
     * @return string
     */
    public function getOrderBuyerAddressLine1()
    {
        return $this->container['order_buyer_address_line1'];
    }

    /**
     * Sets order_buyer_address_line1
     *
     * @param string $order_buyer_address_line1 The Buyer address line 1 of this order
     *
     * @return $this
     */
    public function setOrderBuyerAddressLine1($order_buyer_address_line1)
    {
        $this->container['order_buyer_address_line1'] = $order_buyer_address_line1;

        return $this;
    }

    /**
     * Gets order_buyer_address_line2
     *
     * @return string
     */
    public function getOrderBuyerAddressLine2()
    {
        return $this->container['order_buyer_address_line2'];
    }

    /**
     * Sets order_buyer_address_line2
     *
     * @param string $order_buyer_address_line2 The Buyer address line 2 of this order
     *
     * @return $this
     */
    public function setOrderBuyerAddressLine2($order_buyer_address_line2)
    {
        $this->container['order_buyer_address_line2'] = $order_buyer_address_line2;

        return $this;
    }

    /**
     * Gets order_buyer_address_line3
     *
     * @return string
     */
    public function getOrderBuyerAddressLine3()
    {
        return $this->container['order_buyer_address_line3'];
    }

    /**
     * Sets order_buyer_address_line3
     *
     * @param string $order_buyer_address_line3 The Buyer address line 3 of this order
     *
     * @return $this
     */
    public function setOrderBuyerAddressLine3($order_buyer_address_line3)
    {
        $this->container['order_buyer_address_line3'] = $order_buyer_address_line3;

        return $this;
    }

    /**
     * Gets order_buyer_address_postal_code
     *
     * @return string
     */
    public function getOrderBuyerAddressPostalCode()
    {
        return $this->container['order_buyer_address_postal_code'];
    }

    /**
     * Sets order_buyer_address_postal_code
     *
     * @param string $order_buyer_address_postal_code The Buyer address postal code of this order
     *
     * @return $this
     */
    public function setOrderBuyerAddressPostalCode($order_buyer_address_postal_code)
    {
        $this->container['order_buyer_address_postal_code'] = $order_buyer_address_postal_code;

        return $this;
    }

    /**
     * Gets order_buyer_address_city
     *
     * @return string
     */
    public function getOrderBuyerAddressCity()
    {
        return $this->container['order_buyer_address_city'];
    }

    /**
     * Sets order_buyer_address_city
     *
     * @param string $order_buyer_address_city The Buyer address city of this order
     *
     * @return $this
     */
    public function setOrderBuyerAddressCity($order_buyer_address_city)
    {
        $this->container['order_buyer_address_city'] = $order_buyer_address_city;

        return $this;
    }

    /**
     * Gets order_buyer_address_state_or_region
     *
     * @return string
     */
    public function getOrderBuyerAddressStateOrRegion()
    {
        return $this->container['order_buyer_address_state_or_region'];
    }

    /**
     * Sets order_buyer_address_state_or_region
     *
     * @param string $order_buyer_address_state_or_region The Buyer address state or region of this order
     *
     * @return $this
     */
    public function setOrderBuyerAddressStateOrRegion($order_buyer_address_state_or_region)
    {
        $this->container['order_buyer_address_state_or_region'] = $order_buyer_address_state_or_region;

        return $this;
    }

    /**
     * Gets order_buyer_address_country_name
     *
     * @return string
     */
    public function getOrderBuyerAddressCountryName()
    {
        return $this->container['order_buyer_address_country_name'];
    }

    /**
     * Sets order_buyer_address_country_name
     *
     * @param string $order_buyer_address_country_name The Buyer address country name
     *
     * @return $this
     */
    public function setOrderBuyerAddressCountryName($order_buyer_address_country_name)
    {
        $this->container['order_buyer_address_country_name'] = $order_buyer_address_country_name;

        return $this;
    }

    /**
     * Gets order_buyer_address_country_iso_code_alpha2
     *
     * @return string
     */
    public function getOrderBuyerAddressCountryIsoCodeAlpha2()
    {
        return $this->container['order_buyer_address_country_iso_code_alpha2'];
    }

    /**
     * Sets order_buyer_address_country_iso_code_alpha2
     *
     * @param string $order_buyer_address_country_iso_code_alpha2 The Buyer address country iso code alpha 2 (see http://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#/decoding_table for more details)
     *
     * @return $this
     */
    public function setOrderBuyerAddressCountryIsoCodeAlpha2($order_buyer_address_country_iso_code_alpha2)
    {
        $this->container['order_buyer_address_country_iso_code_alpha2'] = $order_buyer_address_country_iso_code_alpha2;

        return $this;
    }

    /**
     * Gets order_buyer_phone
     *
     * @return string
     */
    public function getOrderBuyerPhone()
    {
        return $this->container['order_buyer_phone'];
    }

    /**
     * Sets order_buyer_phone
     *
     * @param string $order_buyer_phone The phone number of the buyer for this order
     *
     * @return $this
     */
    public function setOrderBuyerPhone($order_buyer_phone)
    {
        $this->container['order_buyer_phone'] = $order_buyer_phone;

        return $this;
    }

    /**
     * Gets order_buyer_mobile_phone
     *
     * @return string
     */
    public function getOrderBuyerMobilePhone()
    {
        return $this->container['order_buyer_mobile_phone'];
    }

    /**
     * Sets order_buyer_mobile_phone
     *
     * @param string $order_buyer_mobile_phone The mobile phone number of the buyer for this order
     *
     * @return $this
     */
    public function setOrderBuyerMobilePhone($order_buyer_mobile_phone)
    {
        $this->container['order_buyer_mobile_phone'] = $order_buyer_mobile_phone;

        return $this;
    }

    /**
     * Gets order_order_source_uri
     *
     * @return string
     */
    public function getOrderOrderSourceUri()
    {
        return $this->container['order_order_source_uri'];
    }

    /**
     * Sets order_order_source_uri
     *
     * @param string $order_order_source_uri Technical information: The url to the source of this order. We received this information from the marketplace.
     *
     * @return $this
     */
    public function setOrderOrderSourceUri($order_order_source_uri)
    {
        $this->container['order_order_source_uri'] = $order_order_source_uri;

        return $this;
    }

    /**
     * Gets order_order_items_source_uri
     *
     * @return string
     */
    public function getOrderOrderItemsSourceUri()
    {
        return $this->container['order_order_items_source_uri'];
    }

    /**
     * Sets order_order_items_source_uri
     *
     * @param string $order_order_items_source_uri Technical information: The url to the source of this order items. We received this information from the marketplace.
     *
     * @return $this
     */
    public function setOrderOrderItemsSourceUri($order_order_items_source_uri)
    {
        $this->container['order_order_items_source_uri'] = $order_order_items_source_uri;

        return $this;
    }

    /**
     * Gets order_items
     *
     * @return \Swagger\Client\Model\OrderItem[]
     */
    public function getOrderItems()
    {
        return $this->container['order_items'];
    }

    /**
     * Sets order_items
     *
     * @param \Swagger\Client\Model\OrderItem[] $order_items order_items
     *
     * @return $this
     */
    public function setOrderItems($order_items)
    {
        $this->container['order_items'] = $order_items;

        return $this;
    }

    /**
     * Gets transition_links
     *
     * @return \Swagger\Client\Model\OrderTransitionLinks
     */
    public function getTransitionLinks()
    {
        return $this->container['transition_links'];
    }

    /**
     * Sets transition_links
     *
     * @param \Swagger\Client\Model\OrderTransitionLinks $transition_links transition_links
     *
     * @return $this
     */
    public function setTransitionLinks($transition_links)
    {
        $this->container['transition_links'] = $transition_links;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


