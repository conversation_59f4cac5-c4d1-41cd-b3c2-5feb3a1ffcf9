<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/resources.proto

namespace Google\Cloud\ServiceManagement\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Represents a source file which is used to generate the service configuration
 * defined by `google.api.Service`.
 *
 * Generated from protobuf message <code>google.api.servicemanagement.v1.ConfigSource</code>
 */
class ConfigSource extends \Google\Protobuf\Internal\Message
{
    /**
     * A unique ID for a specific instance of this message, typically assigned
     * by the client for tracking purpose. If empty, the server may choose to
     * generate one instead.
     *
     * Generated from protobuf field <code>string id = 5;</code>
     */
    private $id = '';
    /**
     * Set of source configuration files that are used to generate a service
     * configuration (`google.api.Service`).
     *
     * Generated from protobuf field <code>repeated .google.api.servicemanagement.v1.ConfigFile files = 2;</code>
     */
    private $files;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $id
     *           A unique ID for a specific instance of this message, typically assigned
     *           by the client for tracking purpose. If empty, the server may choose to
     *           generate one instead.
     *     @type \Google\Cloud\ServiceManagement\V1\ConfigFile[]|\Google\Protobuf\Internal\RepeatedField $files
     *           Set of source configuration files that are used to generate a service
     *           configuration (`google.api.Service`).
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Servicemanagement\V1\Resources::initOnce();
        parent::__construct($data);
    }

    /**
     * A unique ID for a specific instance of this message, typically assigned
     * by the client for tracking purpose. If empty, the server may choose to
     * generate one instead.
     *
     * Generated from protobuf field <code>string id = 5;</code>
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * A unique ID for a specific instance of this message, typically assigned
     * by the client for tracking purpose. If empty, the server may choose to
     * generate one instead.
     *
     * Generated from protobuf field <code>string id = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkString($var, True);
        $this->id = $var;

        return $this;
    }

    /**
     * Set of source configuration files that are used to generate a service
     * configuration (`google.api.Service`).
     *
     * Generated from protobuf field <code>repeated .google.api.servicemanagement.v1.ConfigFile files = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getFiles()
    {
        return $this->files;
    }

    /**
     * Set of source configuration files that are used to generate a service
     * configuration (`google.api.Service`).
     *
     * Generated from protobuf field <code>repeated .google.api.servicemanagement.v1.ConfigFile files = 2;</code>
     * @param \Google\Cloud\ServiceManagement\V1\ConfigFile[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setFiles($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\ServiceManagement\V1\ConfigFile::class);
        $this->files = $arr;

        return $this;
    }

}

