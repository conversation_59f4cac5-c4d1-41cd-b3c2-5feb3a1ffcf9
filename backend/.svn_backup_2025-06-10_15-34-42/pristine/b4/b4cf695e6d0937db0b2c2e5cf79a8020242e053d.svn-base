<?php
	require_once('admin/get-filters.php');
	require_once('stats.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_ORIGIN');

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Provenances des ventes') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Provenances des ventes').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Statistiques de provenances des ventes'); ?></h2>

	<div class="stats-menu">
		<div id="riadatepicker"></div>
		<?php print view_websites_selector( (isset($wst_id) ? $wst_id : 0), true, 'riapicker', true, 'Tous les sites', false, true ); ?>
		<div class="clear"></div>
	</div>

	<?php view_import_highcharts(); ?>

	<?php
		require_once( 'admin/highcharts/graph-origins.php' );
	?>

	<script><!--
		var urlHighcharts = '/admin/stats/origins.php';
		<?php view_date_initialized( 0, '', false, array('autoload'=>true) ); ?>
		$(document).ready(function(){
			$('.selector').mouseup(function(){
				setTimeout(function(){
				date1 = $('#date1').val();
				date2 = $('#date2').val();
				date1 = date1.substr(6,4) + '-' + date1.substr(3,2) + '-' + date1.substr(0,2);
				date2 = date2.substr(6,4) + '-' + date2.substr(3,2) + '-' + date2.substr(0,2);
				window.location='origins.php?date1='+date1+'&date2='+date2;
				},50);
			});
			$('#btn_submit').mouseup(function(){
				setTimeout(function(){
				date1 = $('#date1').val();
				date2 = $('#date2').val();
				date1 = date1.substr(6,4) + '-' + date1.substr(3,2) + '-' + date1.substr(0,2);
				date2 = date2.substr(6,4) + '-' + date2.substr(3,2) + '-' + date2.substr(0,2);
				window.location='origins.php?date1='+date1+'&date2='+date2;
				},50);
			});
		});
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
