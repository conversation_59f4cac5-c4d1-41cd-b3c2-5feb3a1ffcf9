<?php
require_once('fpdf/fpdf.php');
require_once('reports.inc.php');
require_once('cfg.variables.inc.php');
define('EURO', chr(128));

/** @class ReportPdf
 * Classe générant les rapports au format PDF.
 *
 * @see http://www.fpdf.org/
 *
 * @property int    $id       L'identifiant du rapport
 * @property string $filename Le nom de fichier du rapport
 * @property string $zip_filename Le nom de fichier zip du rapport
 * @property array  $report   Les informations concernant le rapport
 * @property array  $config   La configuration globale de l'application
 */
class ReportPdf extends FPDF
{
    const PAGE_HEIGHT = 260;

    const PAGE_WIDTH = 200;

    protected $id; ///< Identifiant du rapport

    protected $filename;

    protected $zip_filename;

    protected $report;

    protected $config;

    protected $signatureSeller = ''; ///< Chemin vers l'image contenant la signature du représentant
    protected $signatureUser = ''; ///< Chemin vers l'image contenant la signature du client

    private $noheader = false;


    /**
     * Créer une nouvelle instance de la classe ReportPdf.
     *
     * @param  int $id
     * @return void
     */
    public function __construct($id)
    {
        parent::__construct();

        $this->id = $id;

        $this->createSignature();

        $this->setConfig()
            ->setFileName('rapport-de-visite-'.$this->id.'.pdf')
            ->setZipFileName('rapport-de-visite-'.$this->id.'.zip')
            ->resetFont()
            ->retrieveReport();

        $this->SetAutoPageBreak(false, 0);
    }

    /**
     * Récupère la configuration globale de l'application.
     *
     * @return $this
     */
    protected function setConfig()
    {
        global $config;

        $this->config = $config;

        return $this;
    }

    /**
     * Défini le nom du fichier pour ce rapport.
     * Utilisé lors du téléchargement de celui-ci.
     *
     * @param  string $filename
     * @return $this
     */
    public function setFileName($filename)
    {
        $filename = trim($filename);

        if( !$filename || strpos($filename, '.pdf') === false ){
            throw new BadMethodCallException('Le nom de fichier est invalide.');
        }

        $this->filename = $filename;

        return $this;
    }

    /**
     * Défini le nom du fichier zip pour ce rapport.
     * Utilisé lors du téléchargement de celui-ci en format zip.
     *
     * @param  string $zip_filename
     * @return $this
     */
    public function setZipFileName($zip_filename)
    {
        $zip_filename = trim($zip_filename);

        if( !$zip_filename || strpos($zip_filename, '.zip') === false ){
            throw new BadMethodCallException('Le nom de fichier zip est invalide.');
        }

        $this->zip_filename = $zip_filename;

        return $this;
    }

    /**
     * Utilise la police par défaut.
     *
     * @return $this
     */
    public function resetFont()
    {
        $this->SetFont('Arial', '', 10);
        $this->SetTextColor(0, 0, 0);

        return $this;
    }

    /**
     * Construit l'entête de la facture.
     *
     * @return void
     */
    public function Header()
    {
        if( !$this->noheader ){
            $this->blocOwner();
            $this->noheader = false;
            return;
        }

        $this->head( $this->pxToPts(150) );
    }

    private function head( $top ){
        $this->SetFont('Arial', 'B', 20);
        $this->SetXY( 10, $top);
        $this->MultiCell( self::PAGE_WIDTH-10, 7, $this->report['type_name'], 0, 'C');

        $this->SetFont('Arial', '', 12);
        $this->SetXY($this->pxToPts(0), $this->GetY()+3);
        $this->Cell(self::PAGE_WIDTH, 7, 'Rapport n°'.$this->report['id'], 0, 0, 'C');
    }

    /**
     * Génère le header d'une pièce de vente
     *
     * \return void
     */
    public function blocOwner(){
        //Récupère les informations sur le client
        $data['owner'] = site_owner_get();

        if(is_null($data['owner'])){
            return;
        }


        global $config;

        if( isset($config['pdf_generation_reports_logo']) && trim($config['pdf_generation_reports_logo']) != '' ){
            $size = $config['img_sizes']['high'];
            $image = $config['img_dir']."/".$size['dir']."/".$config['pdf_generation_reports_logo'].".jpg";
            $this->Image( $image, 200 - $config['pdf_generation_reports_logo_size_x'], 4.5, $config['pdf_generation_reports_logo_size_x'], $config['pdf_generation_reports_logo_size_y'] );
        }
        $this->SetFont('Arial', 'B', 12);
        $this->Cell(50, 4.5, $data['owner']['name'], 0, 1, '');
        $this->Cell(50, 4, '', 0, 1);

        $this->SetFont('Arial', '', 9);
        $this->Cell(50, 4.5, $data['owner']['address1'], 0, 1, '');
        $this->Cell(50, 4.5, $data['owner']['zipcode'] . ' ' . $data['owner']['city'], 0, 1, '');
        $this->Cell(50, 3, '', 0, 1);

        $this->Cell(15, 4.5, 'N° Siret : ', 0, 0, '');
        $this->Cell(50, 4.5, $data['owner']['inscription'], 0, 1, '');

        if ($data['owner']['capital'] != ""){
            $this->Cell(15, 4.5, 'Capital : ', 0, 0, '');
            $this->Cell(50, 4.5, number_format($data['owner']['capital'], 0, ',', ' ').' '.utf8_encode(EURO), 0, 1, '');
        }
        if ($data['owner']['naf'] != ""){
            $this->Cell(15, 4.5, 'N.A.F. : ', 0, 0, '');
            $this->Cell(50, 4.5, $data['owner']['naf'], 0, 1, '');
        }

        $this->Cell(50, 4.5, 'N° intracommunautaire : ' . $data['owner']['taxcode'], 0, 0, '');
        $this->Cell(50, 4.5, '', 0, 1);

        $this->Cell(25, 4.5, 'Téléphone : ', 0, 0, '');
        $this->Cell(50, 4.5, $data['owner']['phone'], 0, 1, '');
        if ($data['owner']['fax'] != ""){
            $this->Cell(25, 4.5, 'Télécopie : ', 0, 0, '');
            $this->Cell(50, 4.5, $data['owner']['fax'], 0, 1, '');
        }
        $this->Cell(25, 4.5, 'Email : ', 0, 0, '');
        $this->Cell(50, 4.5, $data['owner']['email'], 0, 1, '');

        $this->Cell(50, 3, '', 0, 1);

        $this->Ln(10);
        $this->head( $this->GetY() );

    }

    /**
     * Construit le PDF.
     * L'entête est automatiquement rajoutée par FPDF.
     *
     * @see Header
     *
     * @return void
     */
    public function build()
    {
        global $config;

        $this->AddPage();

        $this->SetFont('Arial', 'B', 10);
        $this->SetXY($this->pxToPts(100), $this->GetY()+15);
        $this->Cell(self::PAGE_WIDTH, 5, 'Informations générales :', 0, 1);

        $this->resetFont();

        $this->SetXY($this->pxToPts(100), $this->GetY());
        $this->Cell(self::PAGE_WIDTH, 5, 'Auteur :   '.mb_strtoupper($this->report['author_name']), 0, 1);

        $this->SetXY($this->pxToPts(100), $this->GetY());
        $this->Cell(self::PAGE_WIDTH, 5, 'Client :    '.mb_strtoupper($this->report['usr_name']), 0, 1);

        $this->SetXY($this->pxToPts(100), $this->GetY());
        $this->Cell(self::PAGE_WIDTH, 5, 'Durée :    '.$this->getReportDuration(), 0, 1);

        $this->SetXY($this->pxToPts(100), $this->GetY());
        $this->Cell(self::PAGE_WIDTH, 5, 'Crée le  : '.$this->report['date_created'], 0, 1);

        $this->SetFont('Arial', 'B', 10);
        $this->SetXY($this->pxToPts(100), $this->GetY() + $this->pxToPts(80));
        $this->Cell(self::PAGE_WIDTH, 5, 'Commentaires :', 0, 1);

        $this->resetFont();
        $this->SetXY($this->pxToPts(100), $this->GetY());
        $this->MultiCell((self::PAGE_WIDTH - 10), 5, $this->report['comments'], 0, 'L', 0, true);

        if( $this->GetY() >= self::PAGE_HEIGHT ){
            $this->noheader = true;
            $this->AddPage();

            $y = $this->pxToPts(1000);
        }

        if( !empty($this->report['categories']) && is_array($this->report['categories']) ){
            $this->SetFont('Arial', 'B', 10);
            $this->SetXY($this->pxToPts(100), ($this->GetY() + $this->pxToPts(150)));
            $this->Cell(self::PAGE_WIDTH, 0, _('Catégories de produits :'));
            $this->resetFont();

            foreach( $this->report['categories'] as $category ){
                $this->SetXY($this->pxToPts(100), ($this->GetY() + $this->pxToPts(50)));
                $this->Cell(self::PAGE_WIDTH, 0, $category['name']);
            }
        }

        if( !empty($this->report['products']) && is_array($this->report['products']) ){
            $this->SetFont('Arial', 'B', 10);
            $this->SetXY($this->pxToPts(100), ($this->GetY() + $this->pxToPts(150)));
            $this->Cell(self::PAGE_WIDTH, 0, _('Produits :'));
            $this->resetFont();

            foreach( $this->report['products'] as $product ){
                $this->SetXY($this->pxToPts(100), ($this->GetY() + $this->pxToPts(50)));
                $this->Cell(self::PAGE_WIDTH, 0, $product['ref'].' - '.$product['name']);
            }
        }

        if( isset($this->report['fields']) && count($this->report['fields']) ){
            $this->SetFont('Arial', 'B', 10);
            $this->SetXY($this->pxToPts(100), $this->GetY() + $this->pxToPts(150));
            $this->Cell(self::PAGE_WIDTH, 0, 'Informations complémentaires :');
        }

        $this->resetFont();

        $y = $this->GetY() + $this->pxToPts(70);

        // Gestion des retour chariot si le champs est de type long texte
        if( isset($this->report['fields']) ){
            foreach( $this->report['fields'] as $field ){
                if( $y >= self::PAGE_HEIGHT ){
                    $this->noheader = true;
                    $this->AddPage();

                    $y = $this->GetY() + 10;
                }

                if( $field['type_id'] == FLD_TYPE_TEXTAREA ){
                    $this->SetXY($this->pxToPts(100), $y);
                    $this->Cell(self::PAGE_WIDTH, 0, $field['name'].' :');

                    $y += $this->pxToPts(30);

                    $this->SetXY($this->pxToPts(100), $y);
                    $this->Multicell((self::PAGE_WIDTH - $this->pxToPts(100)), 5, $field['obj_value']);

                    $y = $this->GetY();
                }elseif( $field['type_id'] == FLD_TYPE_IMAGE ){
                    $this->SetXY($this->pxToPts(100), $y);
                    $this->Cell(self::PAGE_WIDTH, 0, $field['name'].' : ');

                    if( is_numeric($field['obj_value']) && $field['obj_value'] > 0 ){
                        $y += $this->pxToPts(30);
                        $this->SetXY($this->pxToPts(100), $y);
                        if(!isset($config['img_sizes'])) cfg_images_load($config);
                        $path = $config['img_dir'].'/'.$config['img_sizes']['medium']['dir'].'/'.$field['obj_value'].'.'.$config['img_sizes']['medium']['format'];

                        if( file_exists($path) ){
                            $this->Image( $path, 10, $y, $this->pxToPts(350), $this->pxToPts(350) );

                            $y += $this->pxToPts(350);
                            $this->SetXY($this->pxToPts(100), $y);
                        }
                    }
                }elseif( $field['type_id'] == FLD_TYPE_SELECT_MULTIPLE ){
                    $this->SetXY($this->pxToPts(100), $y);
                    $this->Cell(self::PAGE_WIDTH, 0, $field['name'].' : ');

                    $obj_values = explode(', ', $field['obj_value']);
                    // print_r($obj_values);
                    foreach($obj_values as $one_val ){
                        $y = $y + 5;

                        $this->SetXY($this->pxToPts(150), $y);
                        $this->Cell(self::PAGE_WIDTH, 0, $one_val);
                    }
                }else{
                    $this->SetXY($this->pxToPts(100), $y);
                    $this->Cell(self::PAGE_WIDTH, 0, $field['name'].' : '.$field['obj_value']);
                }

                $y = $this->GetY() + $this->pxToPts(50);
            }
        }

        if( trim($this->signatureSeller) != '' || $this->signatureUser != '' ){
            if( $y >= 259 ){
                $this->noheader = true;
                $this->AddPage();

                $y = $this->pxToPts(1000);
            }

            if( trim($this->signatureSeller) != '' ){
                $this->SetXY( 10, 259 );
                $this->Cell( (self::PAGE_WIDTH / 2 - 10), 10, 'Signature du représentant', 0, 0, 'C' );
                $this->Image( $this->signatureSeller, 0 + 37.5, 269, $this->pxToPts(400), $this->pxToPts(200) );
            }

            if( trim($this->signatureUser) != '' ){
                $this->SetXY( (self::PAGE_WIDTH / 2), 259 );
                $this->Cell( (self::PAGE_WIDTH / 2 - 10), 10, 'Signature du client', 0, 0, 'C' );
                $this->Image( $this->signatureUser, (self::PAGE_WIDTH / 2) + 27.5, 269, $this->pxToPts(400), $this->pxToPts(200) );
            }
        }

        // Supprime les signatures une fois la création terminée
        $this->deleteSignature();
    }

    /**
     * Force le navigateur a télécharger la facture.
     *
     * @return void
     */
    public function download()
    {
        if($this->hasImages()){
            //If the repport has images we download a zip with the pdf and all the images
            $this->downloadAsZip();
        }else{
            $this->Output($this->filename, 'I');
        }
    }

    /** Cette fonction permet de sauvegarder le rapport.
     * 	@return string Le chemin vers le fichier sauvegardé (peut être un .zip ou .pdf - selon s'il y a des images ou non)
     */
    public function save(){
        if($this->hasImages()){
            //If the repport has images we download a zip with the pdf and all the images
            $filename = $this->downloadAsZip( true );
        }else{
            $filename = sys_get_temp_dir().'/'.$this->filename;
            $this->Output($filename, 'F');
        }

        return $filename;
    }

    /**
     * Récupère le rapport correspondant à l'identifiant passé lors de la construction de l'objet.
     *
     * @return $this
     * @throws Exception Si échec lors de la récupération du rapport
     */
    protected function retrieveReport()
    {
        $r_report = rp_reports_get($this->id);

        if( !$r_report || !ria_mysql_num_rows($r_report) ){
            throw new Exception('Une erreur est survenue lors de la récupération du rapport.');
        }

        $this->report = ria_mysql_fetch_assoc($r_report);
        $this->report['fields'] = array();

        // Récupération des "objets" associés au rapport.
        if( $r_objects = rp_report_objects_get($this->report['id']) ){
            while( $object = ria_mysql_fetch_assoc($r_objects) ){
                switch( $object['cls_id'] ){
                    case CLS_CATEGORY:
                        $r_category = prd_categories_get($object['obj_id_0']);
                        if( $r_category && ria_mysql_num_rows($r_category) ){
                            $this->report['categories'][] = ria_mysql_fetch_assoc($r_category);
                        }
                        break;
                    case CLS_PRODUCT:
                        $r_product = prd_products_get_simple($object['obj_id_0']);
                        if( $r_product && ria_mysql_num_rows($r_product) ){
                            $this->report['products'][] = ria_mysql_fetch_assoc($r_product);
                        }
                        break;
                }
            }
        }

        // Récupération des modèles de saisies et des champs avancés associés.
        if( $r_models = rp_types_models_get($this->report['type_id']) ){
            while( $model = ria_mysql_fetch_assoc($r_models) ){
                $this->report['models'][] = $model;

                $r_fields = fld_fields_get(0, 0, $model['mdl_id'], 0, 0, $this->report['id'], null, array(), false, array(), null, CLS_REPORT);
                if( !$r_fields || !ria_mysql_num_rows($r_fields) ){
                    continue;
                }

                while( $field = ria_mysql_fetch_assoc($r_fields) ){
                    if( !$field['obj_value'] ){
                        continue;
                    }

                    $this->report['fields'][$field['id']] = $field;
                }
            }
        }

        // Récupération des champs avancés orphelins uniquements.
        $r_fields = fld_fields_get(0, 0, -2, 0, 0, $this->report['id'], null, array(), false, array(), null, CLS_REPORT);
        if( $r_fields && ria_mysql_num_rows($r_fields) ){
            while( $field = ria_mysql_fetch_assoc($r_fields) ){
                if( array_key_exists($field['id'], $this->report['fields']) ){
                    continue;
                }

                if( $field['type_id'] == FLD_TYPE_DATE ){
                    $field['obj_value'] = ria_date_format($field['obj_value']);
                }

                $this->report['fields'][] = $field;
            }
        }

        return $this;
    }

    /**
     * Calcule la durée du rapport.
     *
     * @return void
     */
    protected function getReportDuration()
    {
        $r_obj = rp_report_objects_get($this->report['id'], CLS_CHECKIN);

        if( !$r_obj || !ria_mysql_num_rows($r_obj) ){
            return 'Aucune durée';
        }

        $obj = ria_mysql_fetch_assoc($r_obj);

        $duration = ria_mysql_fetch_assoc(
            rp_checkin_get($obj['obj_id_0'])
        );

        return convert_second_to_readable_delay(strtotime($duration['date_end_en']) - strtotime($duration['date_start_en']));
    }

    /**
     * Returns true if the report has any image, false otherwise
     *
     * @return bool
     */
    protected function hasImages()
    {
        if( isset($this->report['fields']) ){
            foreach( $this->report['fields'] as $field ){
                if( $field['type_id'] == FLD_TYPE_IMAGE && is_numeric($field['obj_value']) && $field['obj_value'] > 0){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Force le navigateur a télécharger le rapport en format zip.
     *	@param $get_filename Optionnel, par défaut à false, mettre true pour récupérer l'emplacement du fichier
     * @return string Retourne l'emplacement du fichier si $get_filename est à true, sinon le téléchargement est lancé
     */
    public function downloadAsZip( $filename=false ){
        global $config;

        //Create zip path
        $zip_url = sys_get_temp_dir().'/'.$this->zip_filename;

        //Create zip
        $zip = new ZipArchive();
        if($zip->open($zip_url, ZIPARCHIVE::CREATE) == TRUE){
            //Add files to zip

            //Add pdf
            $zip->addFromString($this->filename,  $this->Output($this->filename, 'S'));

            //Add images
            if( isset($this->report['fields']) ){
                foreach( $this->report['fields'] as $field ){
                    if( $field['type_id'] == FLD_TYPE_IMAGE && is_numeric($field['obj_value']) && $field['obj_value'] > 0){

                        //Get source file
                        $img_source = img_images_get_filesource( $field['obj_value'] );
                        if($img_source){
                            $img_path = $config['img_dir'].'/source/'.$img_source;

                            if( file_exists($img_path) && !is_dir($img_path) ) {
                                //Create file name
                                $img = ria_mysql_fetch_array( img_images_get( $field['obj_value'] ) );
                                if( $img ){
                                    $img_name = ( $img['src_name'] ? $img['src_name'] : $img['id'] ).'.'.($img['type']?$img['type']:'jpg');
                                }else{
                                    $img_name = $field['obj_value'].'.jpg';
                                }

                                $zip->addFile($img_path, $img_name);
                            }
                        }
                    }
                }
            }

            //Close zip
            $zip->close();

            if( $filename ){
                return $zip_url;
            }

            if( file_exists($zip_url) ){

                //Send zip
                header("Content-type: application/octet-stream");
                header("Content-disposition: attachment; filename=".$this->zip_filename);
                readfile($zip_url);

                //Delete zip
                @unlink($zip_url);
            }
        }
    }

    /**
     * Surchage la méthode "Cell" pour forcer l'utilisation de l'encodage UTF-8.
     *
     * @return void
     */
    public function Cell($w, $h = 0, $txt = '', $border = 0, $ln = 0, $align = '', $fill = 0, $link = '')
    {
        parent::Cell($w, $h, utf8_decode($txt), $border, $ln, $align, $fill, $link);
    }

    /**
     * Surchage la méthode "MultiCell" pour forcer l'utilisation de l'encodage UTF-8.
     *
     * @return void
     */
    public function MultiCell($w, $h, $txt, $border = 0, $align = 'L', $fill = 0, $multipage=false)
    {
        if( $multipage ){
            // Découpe le texte en phrase
            $ar_lines = preg_split('/[\n]/i', $txt );

            $last = $ar_lines[ count($ar_lines) - 1 ];
            if( trim($last) == '' ){
                unset( $ar_lines[ count($ar_lines) - 1 ] );
            }

            if( count($ar_lines) > 0 ){
                foreach( $ar_lines as $line ){
                    $this->SetXY($this->pxToPts(100), $this->GetY());
                    parent::MultiCell($w, $h, trim($line), $border, $align, $fill );

                    if( $this->GetY() >= self::PAGE_HEIGHT ){
                        $this->noheader = true;
                        $this->AddPage();

                        $this->SetXY($this->pxToPts(100), ($this->GetY() + $this->pxToPts(150)));
                    }
                }
            }
        }else{
            parent::MultiCell($w, $h, $txt, $border, $align, $fill);
        }
    }

    /**
     * Convertit des pixels en points.
     *
     * @param  float $pixels
     * @return float
     */
    protected function pxToPts($pixels)
    {
        return $pixels * 0.0875;
    }

    /** Cette fonction permet de créer des fichiers images temporaires contenant les signatures (représentant/client).
     *	Les images seront supprimées à la fin de la création du PDF
     */
    private function createSignature(){
        global $config;

        // Charge la signature du représentant et du compte client
        $r_sign_seller = obj_signature_get( CLS_REPORT, $this->id, false, 1 );
        $r_sign_user = obj_signature_get( CLS_REPORT, $this->id, false, 2 );

        // Affichage de la signature du représentant
        if( $r_sign_seller && ria_mysql_num_rows($r_sign_seller) ){
            $sign_seller = ria_mysql_fetch_assoc( $r_sign_seller );

            // Création de l'image temporaire
            $create_is_ok = img_images_create_from_canvas(
                $sign_seller['signature'],
                $config['doc_dir'].'/signture_seller.jpg',
                ['width' => 200, 'height' => 100, 'margin' => 50, 'ext' => 'jpg']
            );

            if( $create_is_ok ){
                $this->signatureSeller = $config['doc_dir'].'/signture_seller.jpg';
            }
        }

        // Affichage de la signature du compte client
        if( $r_sign_user && ria_mysql_num_rows($r_sign_user) ){
            $sign_user = ria_mysql_fetch_assoc( $r_sign_user );

            // Création de l'image temporaire
            $create_is_ok = img_images_create_from_canvas(
                $sign_user['signature'],
                $config['doc_dir'].'/signture_user.jpg',
                ['width' => 200, 'height' => 100, 'margin' => 50, 'ext' => 'jpg']
            );

            if( $create_is_ok ){
                $this->signatureUser = $config['doc_dir'].'/signture_user.jpg';
            }
        }
    }

    /** Cette fonction permet de supprimer les signatures temporaires.
     *
     */
    private function deleteSignature(){
        if( trim($this->signatureSeller) != '' ){
            unlink($this->signatureSeller);
        }

        if( trim($this->signatureUser) != '' ){
            unlink($this->signatureUser);
        }
    }
}
?>