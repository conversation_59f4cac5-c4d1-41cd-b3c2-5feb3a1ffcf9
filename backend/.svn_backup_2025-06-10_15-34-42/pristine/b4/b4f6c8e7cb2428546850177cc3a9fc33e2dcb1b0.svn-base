<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/servicemanager.proto

namespace Google\Cloud\ServiceManagement\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Response message for `ListServices` method.
 *
 * Generated from protobuf message <code>google.api.servicemanagement.v1.ListServicesResponse</code>
 */
class ListServicesResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * The returned services will only have the name field set.
     *
     * Generated from protobuf field <code>repeated .google.api.servicemanagement.v1.ManagedService services = 1;</code>
     */
    private $services;
    /**
     * Token that can be passed to `ListServices` to resume a paginated query.
     *
     * Generated from protobuf field <code>string next_page_token = 2;</code>
     */
    private $next_page_token = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Cloud\ServiceManagement\V1\ManagedService[]|\Google\Protobuf\Internal\RepeatedField $services
     *           The returned services will only have the name field set.
     *     @type string $next_page_token
     *           Token that can be passed to `ListServices` to resume a paginated query.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Servicemanagement\V1\Servicemanager::initOnce();
        parent::__construct($data);
    }

    /**
     * The returned services will only have the name field set.
     *
     * Generated from protobuf field <code>repeated .google.api.servicemanagement.v1.ManagedService services = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getServices()
    {
        return $this->services;
    }

    /**
     * The returned services will only have the name field set.
     *
     * Generated from protobuf field <code>repeated .google.api.servicemanagement.v1.ManagedService services = 1;</code>
     * @param \Google\Cloud\ServiceManagement\V1\ManagedService[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setServices($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\ServiceManagement\V1\ManagedService::class);
        $this->services = $arr;

        return $this;
    }

    /**
     * Token that can be passed to `ListServices` to resume a paginated query.
     *
     * Generated from protobuf field <code>string next_page_token = 2;</code>
     * @return string
     */
    public function getNextPageToken()
    {
        return $this->next_page_token;
    }

    /**
     * Token that can be passed to `ListServices` to resume a paginated query.
     *
     * Generated from protobuf field <code>string next_page_token = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setNextPageToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->next_page_token = $var;

        return $this;
    }

}

