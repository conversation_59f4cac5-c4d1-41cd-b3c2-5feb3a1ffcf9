<?php
namespace Riashop\Salesforce\Tasks\LinearRaised;

use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_products;
use Riashop\Salesforce\Task;
/**
 * \ingroup salesforce_tasks
 * @class CollectionProduct Classe gestion des produits assortiments
 * @{
 */
class CollectionProduct extends Task
{
	const CAT_ROOT = 235176; ///< integer Identifiant de la catégorie qui contient les relevé linéaire chez legrand
	/**
	 * @copydoc Task::saveRow
	 */
	public function saveRow(array $record)
	{
		if( !prd_categories_exists(self::CAT_ROOT) ){
			throw new \Exception("Category ".self::CAT_ROOT." must exist for record ".$record['Id']);
		}
		$prd_id = prd_products_get_id($record['Id'], self::CAT_ROOT);
		if( !$prd_id ){
			$prd_id = prd_products_add($record['Id'], $record['Name'], '', 0, true, 0, 0, 0, 0, '', true);
			prd_products_set_ref_gescom($prd_id, $record['Id'], true);
			if( is_numeric($prd_id) ){
				prd_products_add_to_cat($prd_id, self::CAT_ROOT, true);
			}else{
				throw new \Exception("CollectionProduct impossible d'ajouter le linéaire ".$record['Id']);
			}
		}

		prd_products_update_desc($prd_id, 'Référence emblématique : '.$record['Ref_Emblematique__c']);

		$r_list = prw_followed_lists::getByRefGescom($record['FR_Collection__c']);

		if( !ria_mysql_num_rows($r_list) ){
			throw new \Exception("L'assortiment : {$record['FR_Collection__c']} n'existe pas");
		}

		$list = ria_mysql_fetch_assoc($r_list);

		prw_followed_products::delete($list['id'], $prd_id);

		if( !$record['IsDeleted'] ){
			prw_followed_products::add($prd_id, $list['id'], 1, null, $record['Marque__c'] != 'Legrand');
		}

		prw_followed_lists::setDateModified($list['id']);

		return true;
	}
	/**
	 * @copydoc Task::add
	 */
	public function add($param){

	}
}
/// @}