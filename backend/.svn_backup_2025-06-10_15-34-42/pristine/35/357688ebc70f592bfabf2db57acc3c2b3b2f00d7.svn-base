<?php
    /** \file export-mastertag.php
     * 	Ce script est destiné à mettre à jour le flux contenant le catalogue pour Mastertag.
     */

    if (!isset($ar_params)) {
        die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
    }

    foreach ($configs as $config) {
        // lien utilise pour la création du fichier d'export du catalogue
        $dirname = $config['ctr_dir'].'/'.md5($config['tnt_id'].$config['date-created']).'/';
        $file = $dirname.'tmp-mastertag.xml';
        $url_file = $config['site_url'].'/shopbots/'.md5($config['tnt_id'].$config['date-created']).'/mastertag.xml';
            
        // Vérifie que le comparateur Google Shopping est bien activé pour ce client.
        if (!isset($config['mastertag_active_export']) || !$config['mastertag_active_export']) {
            if (file_exists($file)) {
                unlink($file);
            }

            continue;
        }

        // si le dossier contenant les fichiers n'existe pas, on le créé avec les droits apache
        if (!file_exists($dirname)) {
            mkdir($dirname, 0755);
            chgrp($dirname, 'apache');
            chown($dirname, 'apache');
        }
            
        // création du fichier
        $f = fopen($file, 'w');
            
        // écriture de l'entête
        fwrite($f, '<?xml version="1.0"?>'."\n");
        fwrite($f, '<rss xmlns:g="http://base.google.com/ns/1.0" version="2.0">'."\n");
        fwrite($f, '	<channel>'."\n");
        fwrite($f, '		<title>'.htmlspecialchars(strtolower($config['site_name'])).'</title>'."\n");
        fwrite($f, '		<link>'.$config['site_url'].'</link>'."\n");
        fwrite($f, '		<description>'.htmlspecialchars($config['site_desc']).'</description>'."\n");
            
        // récupère le catalogue exporté sur Google Shopping
        $r_product = prd_products_get_simple(0, '', true, $config['cat_root'], true);
        
        if ($r_product && ria_mysql_num_rows($r_product)) {
            $ar_ids = array();

            while ($p = ria_mysql_fetch_assoc($r_product)) {
                // Identifiant d'un article parent
                $parent = prd_products_get_parent($p['id'], true);
                $tmp_prd_id = is_numeric($parent) && $parent > 0 ? $parent : $p['id'];
                $child_for_parent = false;

                // vérifier s'il existe des produits enfants
                $childs = prd_childs_get($tmp_prd_id, false, false);
                if ($childs && ria_mysql_num_rows($childs)) {
                    while ($child = ria_mysql_fetch_array($childs)) {
                        if (!in_array($child['id'], $ar_ids)) {
                            $ar_ids[] = $child['id'];

                            $res = add_product($f, $tmp_prd_id, $child);
                            if ($res) {
                                $child_for_parent = $child;
                            }
                        }
                    }
                }

                if (!in_array($p['id'], $ar_ids)) {
                    if ($child_for_parent) {
                        add_product($f, $p['id'], false);
                    }else{
                        add_product($f, $p['id']);
                    }

                    $ar_ids[] = $p['id'];
                }
            }

        }

        // écriture de pied de page
        fwrite($f, '	</channel>'."\n");
        fwrite($f, '</rss>'."\n");
            
        // fermeture du fichier
        fclose($f);
            
        rename($dirname.'tmp-mastertag.xml', $dirname.'mastertag.xml');
        @unlink($dirname.'tmp-mastertag.xml');
    }

    function add_product( $file, $prd, $child=false ){
        global $config;

        // information sur le produit
        $product = ria_mysql_fetch_assoc(prd_products_get_simple($prd));
        
        // Seul les produits commandable sont exportés
        if ($child !== false) {
            if (!$child['orderable']) {
                return false;
            }
        } else {
            if (!$product['orderable']) {
                return false;
            }
        }

        $sell_weight = $product['sell_weight'];
        $url = prd_products_get_url($product['id'], true);
            
        // intégre le produit seulement s'il existe une url publiée
        if (trim($url)) {
            $url = rew_strip($url);

            $info = false;
            // information sur le produit
            if ($child !== false) {
                $info = array(
                    'id' => $child['id'],
                    'ref' => $child['ref'],
                    'name' => $child['name'],
                    'title' => $child['title'],
                    'desc' => $child['desc-long'],
                    'stock' => $child['stock'],
                    'countermark' => $child['countermark'],
                    'stock_com' => $child['stock_com'],
                    'img_id' => $child['img_id'] > 0 ? $child['img_id'] : $product['img_id'],
                    'brand' => $child['brd_title'],
                    'barcode' => $child['barcode'],
                    'weight' => $child['weight_net'] != '' ? $child['weight_net'] : $child['weight'],
                    'follow_stock' => prd_products_is_follow_stock($child['ref']),
                    'parent' => $product['id']
                );
            } else {
                $info = array(
                    'id' => $product['id'],
                    'ref' => $product['ref'],
                    'name' => $product['name'],
                    'title' => $product['title'],
                    'desc' => $product['desc-long'],
                    'stock' => $product['stock'],
                    'countermark' => $product['countermark'],
                    'stock_com' => $product['stock_com'],
                    'img_id' => $product['img_id'],
                    'brand' => $product['brd_title'],
                    'barcode' => $product['barcode'],
                    'weight' => $product['weight_net'] != '' ? $product['weight_net'] : $product['weight'],
                    'follow_stock' => prd_products_is_follow_stock($product['ref']),
                    'parent' => '0'
                );

                if (trim($info['desc']) == '') {
                    $info['desc'] = $product['desc'];
                }
            }

            $info['price_port'] = false;

            // Traitement spécifique à certaines boutique
            switch ($config['tnt_id']) {
                case 4: { // Proloisirs
                    $tmp_arg = $config['get_prd_params'];

                    if (isset($tmp_arg['prs_wst_id'])) {
                        unset($tmp_arg['prs_wst_id']);
                    }
                    if (isset($tmp_arg['exclude'])) {
                        unset($tmp_arg['exclude']);
                    }
                    if (isset($tmp_arg['reseller_id'])) {
                        unset($tmp_arg['reseller_id']);
                    }

                    $rp = prd_products_get_simple($product['id'], '', false, 0, false, false, false, false, $tmp_arg);
                    if (!$rp || !ria_mysql_num_rows($rp)) {
                        return false;
                    }

                    $p = ria_mysql_fetch_assoc($rp);

                    $product['nomenclature_type'] = $p['nomenclature_type'];
                    $product['is_parent'] = prd_products_is_parent($product['id']);

                    // Récupère la TVA
                    $tva_rate = _TVA_RATE_DEFAULT;

                    $rtva = prd_products_get_price($info['id'], 0, 0, 0, 1, 0, true);
                    if ($rtva && ria_mysql_num_rows($rtva)) {
                        $tva = ria_mysql_fetch_assoc($rtva);

                        if (is_numeric($tva['tva_rate']) && $tva['tva_rate'] > 0) {
                            $tva_rate = $tva['tva_rate'];
                        }
                    }

                    $info['tva_rate'] = $tva_rate;
                    $info['price'] = fld_object_values_get($info['id'], 770, i18n::getLang());
                    $info['price_ht'] = round(($info['price'] / $tva_rate), 2);
                    $info['price_port'] = 0;

                    if ($rpldd = prd_products_get_price($info['id'], 0, 3255)) {
                        if ($p_ldd = ria_mysql_fetch_array($rpldd)) {
                            if ($p_ldd['price_ht'] > $info['price_ht'])
                                $info['price_port'] = $p_ldd['price_ht'] - $info['price_ht'] * $info['tva_rate'];
                            else
                                $info['price_port'] = $p_ldd['price_ht'] * $info['tva_rate'];
                        }
                    }

                    // montant de port obligatoire
                    if ($info['price_port'] <= 0) {
                        return false;
                    }

                    // Disponible en stock
                    require_once($config['site_dir'].'/include/view.product.inc.php');
                    $res_stock = view_product_stock($product);
                    if ($res_stock === 1) {
                        $info['stock'] = 10;
                    } else {
                        $info['stock'] = 0;
                    }

                    break;
                }
                case 26: { // Ma Maison est Magnifique
                    // récupère le montant du port 
                    $tmp = get_service_for_products(array($info), false);
                    if (is_array($tmp)) {
                        $tmp = array_shift(array_shift($tmp));
                        $info['price_port'] = $tmp['cost'];
                    }
                    
                    // Tarif du produit
                    $price = prd_products_get_price($info['id']);
                    if (!$price || !ria_mysql_num_rows($price)) {
                        return false;
                    }
                    $info['price'] = ria_mysql_result($price, 0, 'price_ttc');
                    break;
                }
                case 43: { // Purebike
                    // Si pas de stock, on récupère le stock fournisseurs
                    if (!is_numeric($info['stock']) || $info['stock'] <= 0) {
                        $stock_fourn = fld_object_values_get($info['id'], 3590);
                        if (is_numeric($stock_fourn) && $stock_fourn > 0) {
                            $info['stock'] = $stock_fourn;
                        }
                    }

                    // Tarif du produit
                    $price = prd_products_get_price($info['id']);
                    if (!$price || !ria_mysql_num_rows($price)) {
                        return false;
                    }
                    $info['price'] = ria_mysql_result($price, 0, 'price_ttc');
                    break;
                }
                case 16 : { // Animal & Co
                    // Tarif du produit
                    $price = prd_products_get_price($info['id']);
                    if (!$price || !ria_mysql_num_rows($price)) {
                        return false;
                    }
                    
                    $info['price'] = ria_mysql_result($price, 0, 'price_ttc');
                    
                    $r_service = dlv_services_get(array(130, 132), true, 0, false, $info['id']);
                    if ($r_service) {
                        while ($service = ria_mysql_fetch_assoc($r_service)) {
                            if ($info['price_port'] === false || $info['price_port'] > $service['price-ttc']) {
                                $info['price_port'] = $service['price-ttc'];

                                if ($service['dealer-free-ht'] && $service['dealer-free-ht'] <= $info['price'] ) {
                                    $info['price_port'] = 0;
                                }
                            }
                        }
                    }

                    if ($info['price_port'] === false) {
                        $info['price_port'] = 0;
                    }
                    break;
                }
                default: {
                    // Tarif du produit
                    $price = prd_products_get_price($info['id']);
                    if (!$price || !ria_mysql_num_rows($price)) {
                        return false;
                    }
                    $info['price'] = ria_mysql_result($price, 0, 'price_ttc');
                    break;
                }
            }
                
            // Description du produit
                /* Note Google Shopping : Nous vous recommandons de mentionner le terme "multipack" et la quantité ( $info['qte_pack'] ) dans la description. */
            $info['desc'] = trim($info['desc']) == '' ? $info['title'] : html_revert_wysiwyg($info['desc']);

            // Limite de caractère sur différents champs
            $info['title'] = strcut($info['title'], 150);
            $info['desc'] = strcut($info['desc'], 5000);

            // Vérifie que l'url du produit est fonctionnelle (en réalisant une requête HTTP)
            if ($config['tnt_id'] != 1) { // désactivation bigship temporaire
                // Seules les urls valides seront inclues
                $infos = Sitemap::checkUrl($config['site_url'].$url, false, '', true);
                if (!$infos) {
                    return false;
                }

                // Récupère l'url directe du produit (sans redirection)
                $url = str_replace(array(str_replace('https', 'http', $config['site_url']), $config['site_url'], '?testadminerror404=1'), '', $infos['url']);
                if (!trim($url) || $url == '/') {
                    return false;
                }

                if ($config['tnt_id'] == 27 && $config['wst_id'] == 45) {
                    if (strstr($url, '/catalogue/mobilierdeville/')) {
                        return false;
                    }
                }
            }
            // Conditionnement du produit
            //
            // on applique le fonctionnement Animal & Co (qui n'est pas le standard, mais ce sont les seuls qui ont besoin des multipacks pour l'instant)
            // à savoir :
            //	- si un produit est vendu dans un conditionnement, il n'est pas vendu à l'unité
            //	- Il n'y a qu'un seul conditionnement possible
            $info['qte_col'] = 0;
            $info['qte_pack'] = 1;
            if (isset($config['ctr_unity_qty_pack']) && !$config['ctr_unity_qty_pack']) {
                if (!$sell_weight) {
                    $rcol = prd_colisage_classify_get(0, $info['id'], 0, array('qte' => 'asc'));
                    if ($rcol && ria_mysql_num_rows($rcol)) {
                        $info['qte_col'] = ria_mysql_result($rcol, 0, 'col_id');
                        $info['qte_pack'] = ria_mysql_result($rcol, 0, 'qte');
                    }

                    if ($rcol && ria_mysql_num_rows($rcol) > 1) {
                        error_log('Export Mastertag : il existe plusieurs conditionnements pour le produit '.$info['id'].'. Un seul va être exporté.');
                    }
                }
            }

            if ($sell_weight) { // Le prix de vente est exprimé au kilo
                $info['price'] = $info['price'] * ($info['weight'] / 1000);
            }
                            
            // url des images des produits
            // utilisation de la nouvelle configuration 800x800 "google-shopping" si existante
            $thumbs = isset($config['img_sizes']['google-shopping']) ? $config['img_sizes']['google-shopping'] : $config['img_sizes']['high'];
            $url_img = $config['img_url'].'/'.$thumbs['width'].'x'.$thumbs['height'];
                
            // Contrôle que le prix du produit est bien supérieur à 0. Le produit ne pourra pas être exporté
            // si cette condition n'est pas remplie.
            if ($info['price'] <= 0) {
                return false;
            }
                
            // intègre le produit à Google Shopping
            fwrite($file, '		<item>'."\n");

            // informations générales du produit
            fwrite($file, '			<title><![CDATA['.htmlspecialchars(ucfirst2($info['title'])).']]></title>'."\n");
            fwrite($file, '			<link><![CDATA['.$config['site_url'].$url.']]></link>'."\n");
            fwrite($file, '			<description><![CDATA['.$info['desc'].']]></description>'."\n");
            fwrite($file, '			<g:id>'.$info['id'].'</g:id>'."\n");
            fwrite($file, '			<g:condition>new</g:condition>'."\n");
                
            // tarif avec promotion (important : multiplié par la quantité conditionnée)
            switch ($config['tnt_id']) {
                case 4: { // Proloisirs
                    // on ne récupère pas les promotions sur produits pour Proloisirs (réservé à l'extranet).
                    break;
                }
                case 43: { // Purebike
                    // Récupère le prix avant Soldes
                    $p_solde = pmt_soldes_is_active();
                    $y_solde = pmt_soldes_get_periods(date('Y'));

                    if ($p_solde !== false) {
                        $price_solde = fld_object_values_get($info['id'], 3553);

                        if (is_numeric($price_solde) && $price_solde > $info['price']) {
                            fwrite($file, '            <g:sale_price>'.number_format($info['price'], 2, '.', '').' EUR</g:sale_price>'."\n");
                            fwrite($file, '            <g:sale_price_effective_date>'.date('Y-m-d', $y_solde[$p_solde]['start']['time']).'T00:00-0800/'.date('Y-m-d', $y_solde[$p_solde]['stop']['time']).'T23:59-0800</g:sale_price_effective_date>'."\n");

                            $info['price'] = $price_solde;
                            break;
                        }
                    }
                }
                default: {
                    // ATTENTION : si pas de solde en champ avancé, le code dans le default est exécuté pour Purebike (à conserver)
                    $pmt = prc_promotions_get($info['id'], 0, 0, 1, $info['qte_col']);

                    if (is_array($pmt) && sizeof($pmt)) {
                        fwrite($file, '            <g:sale_price>'.number_format($pmt['price_ttc'] * $info['qte_pack'], 2, '.', '').' EUR</g:sale_price>'."\n");
                        if (trim($pmt['date-start']) && trim($pmt['date-end'])) {
                            $datestart = dateparse($pmt['date-start']);
                            $dateend = dateparse($pmt['date-end']);
                            fwrite($file, '            <g:sale_price_effective_date>'.$datestart.'T00:00-0800/'.$dateend.'T23:59-0800</g:sale_price_effective_date>'."\n");
                        }
                    }
                }
            }

            // prix du produit (important : multiplié par la quantité conditionnée)
            fwrite($file, '			<g:price>'.number_format($info['price'] * $info['qte_pack'], 2, '.', '').' EUR</g:price>'."\n");

            // Information de stock, deux valeurs prédéfinies sont possibles :
            //	- in stock : en stock
            //	- out of stock : non disponible
            //
            $stock = 'in stock';
            if ($info['follow_stock']) {
                if ($info['stock'] > 0 || $info['countermark']) {
                    $stock = 'in stock';
                } else {
                    $stock = 'out of stock';
                }
            }
            
            fwrite($file, '			<g:availability>'.$stock.'</g:availability>'."\n");
                
            // Image principale du produit
            //
            // La documentation de Google Shopping indique (https://support.google.com/merchants/answer/188494?hl=fr) :
            //	1. Utilisez l'image la plus grande, en taille réelle, de votre produit (jusqu'à 4 Mo).
            //	2. Nous recommandons l'utilisation d'images d'au moins 800 x 800 pixels.
            //	3. N'agrandissez pas les images. N'envoyez pas non plus de vignettes.
            //	4. Le format minimal obligatoire pour les images de vêtements est 250 x 250 pixels.
            //	5. L'image doit représenter le produit. N'indiquez pas d'espace réservé, tel que "Image non disponible", ou le logo de la marque ou de votre magasin.
            //	6. Les images des produits ne doivent pas comporter de logos ou d'autres formes de promotion.
            //	7. Les formats d'image acceptés sont GIF (.gif) non animés, JPEG (.jpg/.jpeg), PNG (.png), BMP (.bmp) et TIFF (.tif/.tiff). Les extensions des fichiers d'image doivent correspondre au format utilisé.
            //	8. L'URL doit commencer par "http://" ou "https://"
            //
            // Images secondaires
            //
            // La documentation de Google Shopping indique (https://support.google.com/merchants/answer/188494?hl=fr) :
            // 	1. Vous pouvez insérer jusqu'à 10 images supplémentaires par article en utilisant cet attribut plusieurs fois.
            //	2. Pour les fichiers XML, ajoutez chaque URL en tant qu'attribut <additional_image_link> distinct.
            //	3. Cet attribut est soumis aux mêmes règles que l'attribut 'lien image' [image link].
            //
            $ar_imgs = prd_images_get_all($info['id']);
            if (!count($ar_imgs) && $info['parent']) {
                $ar_imgs = prd_images_get_all($info['id']);
            }

            $first = true;
            foreach ($ar_imgs as $img) {
                if ($first) {
                    fwrite($file, '			<g:image_link>'.$url_img.'/'.$info['img_id'].'.'.$thumbs['format'].'</g:image_link>'."\n");
                    $first = false;
                    continue;
                }

                fwrite($file, '			<g:additional_image_link>'.$url_img.'/'.$img.'.'.$thumbs['format'].'</g:additional_image_link>'."\n");
            }
                
            // liste des paramètres qui permettent de caractériser un produit "avec variantes"
            // dans quel cas, on définit un attribut "item_group_id" qui contiendra l'identifiant du produit parent
            // note : ce fonctionnement est facultatif de dehors des vètements
            // ce système oblige par ailleurs à avoir un produit cartésien de variantes qui soit unique (exemple : pas deux produits différents avec taille et couleur du premier = taille et couleur du deuxième)
            $is_var = false;
            
            $var_codes = array(
                'size'      => 0, 
                'color'     => 0, 
                'pattern'   => 0, 
                'material'  => 0
            );

            switch ($config['tnt_id']) {
                case 16: {
                    $var_codes = array(
                        'size' => 731,
                        'color' => 678
                    );
                    break;
                }
            }

            foreach ($var_codes as $code => $fld_id) {
                $value = str_replace(' / ', '&#47;', fld_object_values_get($info['id'], $fld_id));
                if (trim($value) == '') {
                    continue;
                }
                
                $is_var = true;
                fwrite($file, '			<g:'.$code.'><![CDATA['.htmlspecialchars($value).']]></g:'.$code.'>'."\n");
            }
        
            // on est sur un produit enfant qui est une variante
            if ($is_var && is_numeric($info['parent']) && $info['parent']) {
                fwrite($file, '			<g:item_group_id>'.$info['parent'].'</g:item_group_id>'."\n");
            }
                
            // code barre 'EAN'
            $gtin = false;
            if (trim($info['barcode'])) {
                fwrite($file, '			<g:gtin>'.$info['barcode'].'</g:gtin>'."\n");
                $gtin = true;
            }
                
            // Le couple de variables ci-dessous représente un identifiant unique pour le fabriquant
            // 2 cas :
            //     - on connait la marque (brd_name) et la référence de son produit (prd_suppliers -> ps_ref)
            //     - le vendeur fabrique ses produits (Pierre Oteiza, Terre de Viande) : le nom est stocké dans une variable de niveau tenant est le prd_ref est la référence constructeur
            // Important : à partir du moment où une boutique gère des marques, il ne faut pas renseigner "comparators_default_brand" (sauf si l'on est CERTAIN que tous les produits sans marque sont les produits fabriqués par le vendeur)
            $ok_ref_constructeur = false;
            $ok_name_constructeur = false;
                
            // référence fabriquant
            $sup = prd_suppliers_get($info['id'], 0, '', true);
            if ($sup && ria_mysql_num_rows($sup)) {
                $ref_sup = ria_mysql_result($sup, 0, 'ref');
                if (trim($ref_sup)) {
                    fwrite($file, '		    <g:mpn>'.$ref_sup.'</g:mpn>'."\n");
                    $ok_ref_constructeur = true;
                }
            }
                
            // Marque
            if (trim($info['brand'])) {
                fwrite($file, '			<g:brand><![CDATA['.htmlspecialchars($info['brand']).']]></g:brand>'."\n");
                $ok_name_constructeur = true;
            }
                
            // le vendeur est le fabriquant
            if (!$ok_ref_constructeur && !$ok_name_constructeur && trim($config['comparators_default_brand'])) {
                fwrite($file, '			<g:mpn>'.$info['ref'].'</g:mpn>'."\n");
                fwrite($file, '			<g:brand><![CDATA['.htmlspecialchars($config['comparators_default_brand']).']]></g:brand>'."\n");
                $ok_ref_constructeur = true;
                $ok_name_constructeur = true;
            }
                
            // pas de code unique (ni EAN, ni ISBN, ni constructeur)
            if (!$gtin && !($ok_ref_constructeur && $ok_name_constructeur)) {
                fwrite($file, '			<g:identifier_exists>FALSE</g:identifier_exists>'."\n");
            }
                
            // poids du produit
            if (is_numeric($info['weight']) && $info['weight'] > 0) {
                fwrite($file, '			<g:shipping_weight>'.$info['weight'].' g</g:shipping_weight>'."\n");
                // poids net du produit pour la vente au poids
                if ($sell_weight) {
                    fwrite($file, '		<g:unit_pricing_measure>'.$info['weight'].' g</g:unit_pricing_measure>'."\n");
                    // l'attribut ci-dessous indique l'unité de l'expression "Soit xx € le [unité]"
                    fwrite($file, '		<g:unit_pricing_base_measure>1 kg</g:unit_pricing_base_measure>'."\n");
                }
            }

            if ($info['price_port'] !== false) {
                // Calcul des frais de port spécifique à Proloisirs
                fwrite($file, '			<g:shipping>'."\n");
                fwrite($file, '				<g:country>FR</g:country>'."\n");
                fwrite($file, '				<g:service>Standard</g:service>'."\n");
                fwrite($file, '				<g:price>'.number_format($info['price_port'], 2, '.', '').' EUR</g:price>'."\n");
                fwrite($file, '			</g:shipping>'."\n");
            }

            // gestion du conditionnement "multipack"
            if ($info['qte_pack'] > 1) {
                fwrite($file, '			<g:multipack>'.$info['qte_pack'].'</g:multipack>'."\n");
            }
                
            // classification interne du produit
            // Cet attribut indique également la catégorie du produit proposé, mais d'après votre propre classification. 
            // Contrairement à l'attribut 'catégorie de produits Google' [google product category], vous pouvez inclure plusieurs valeurs d'attribut 
            // 'catégorie' [product type] si les produits appartiennent à plusieurs catégories. Incluez la chaîne de catégorie complète. 
            // Par exemple, si vos produits appartiennent à la catégorie Réfrigérateurs, indiquez la chaîne complète : 
            //     Maison et jardin > Cuisine et table > Petit électroménager > Réfrigérateurs. 
            // Vous pouvez utiliser le séparateur > ou /.
            $rhy = prd_products_categories_get($info['id'], true);
            if ($rhy && ria_mysql_num_rows($rhy)) {

                $i = 1;
                while ($hy = ria_mysql_fetch_array($rhy)) {
                    if ($i > 10) {
                        break;
                    }

                    if (!prd_categories_is_published($hy['cat'])) {
                        continue;
                    }
                        
                    // récupère la hiérarchie complète
                    $rparent = prd_categories_parents_get($hy['cat']);
                    if ($rparent && ria_mysql_num_rows($rparent)) {
                        $tmp_hy = '';
                        while ($parent = ria_mysql_fetch_array($rparent)) {
                            $tmp_hy .= ($tmp_hy != '' ? ' > ' : '').$parent['title'];
                        }

                        $tmp_hy .= ($tmp_hy != '' ? ' > ' : '').$hy['title'];
                        if ($tmp_hy != '') {
                            fwrite($file, '			<g:product_type><![CDATA['.$tmp_hy.']]></g:product_type>'."\n");
                            $i++;
                        }
                    } else {
                        fwrite($file, '			<g:product_type><![CDATA['.$hy['title'].']]></g:product_type>'."\n");
                        $i++;
                    }

                }
            }
                
            // si aucune clasification publié pour le produit enfant, on prends celle du produit parent.
            if (!isset($tmp_hy) && $child !== false) {
                $rhy = prd_products_categories_get($product['id'], true);
                if ($rhy && ria_mysql_num_rows($rhy)) {

                    while ($hy = ria_mysql_fetch_array($rhy)) {

                        if (!prd_categories_is_published($hy['cat'])) {
                            continue;
                        }
                            
                        // récupère la hiérarchie complète
                        $rparent = prd_categories_parents_get($hy['cat']);
                        if ($rparent && ria_mysql_num_rows($rparent)) {
                            $tmp_hy = '';
                            while ($parent = ria_mysql_fetch_array($rparent)) {
                                $tmp_hy .= ($tmp_hy != '' ? ' > ' : '').$parent['title'];
                            }

                            $tmp_hy .= ($tmp_hy != '' ? ' > ' : '').$hy['title'];
                            if ($tmp_hy != '') {
                                fwrite($file, '			<g:product_type><![CDATA['.$tmp_hy.']]></g:product_type>'."\n");
                            }
                        }

                    }
                }
            }

            fwrite($file, '		</item>'."\n");
            return true;
        }
    }