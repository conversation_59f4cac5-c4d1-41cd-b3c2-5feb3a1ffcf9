<?php

require_once 'Services/Abstract.class.php';
require_once 'Services/Contacts/Contact.class.php';

class ContactTypeService extends AbstractService
{
	/**	Identifiant du type de contact
	 * @var	int
	 */
	protected $id = 0;

	/**	Titre du type de contact
	 * @var	string|null
	 */
	protected $name = null;

	/**	Description du type de contact
	 * @var	string|null
	 */
	protected $desc = null;

	/**	Tableau des contacts associés type
	 * @var	null|array
	 */
	protected $contacts = null;

	/**	Nombre de contacts associés au type
	 * @var	int
	 */
	protected $count = 0;


	/**	Tableau des attributs de requête
	 * @var	array
	 */
	protected $attributes = [];

	/**	Constructeur de la classe
	 * @param	int		$type	Obligatoire, Identifiant d'un type de contact
	 * @param	bool	$load	Optionnel, True pour charger les données
	 * @return	void
	 */
	public function __construct($type, $load = true)
	{
		$this->id = is_numeric($type) && $type > 0 ? (int)$type : 0;

		if (is_bool($load) && $load) {
			$this->__loadType();
		}
	}

	/**	Charge les informations de base du type de contact
	 * @return	ContactTypeService	L'instance en cours
	 */
	private function __loadType()
	{
		if ($this->id < 1) {
			return $this;
		}
		global $config;

		$sql = '
			select
				ct.type_id as id,
				ct.type_name as name,
				ct.type_desc as "desc"
			from
				cnt_types ct
			where
					ct.type_tnt_id = ' . $config['tnt_id'] . '
				and ct.type_id = ' . $this->id . '
				and ct.type_date_deleted is null
		';

		$rtype = ria_mysql_query($sql);

		if (!ria_mysql_num_rows($rtype)) {
			return $this;
		}
		$type = ria_mysql_fetch_assoc($rtype);

		$this->__set('name', $type['name']);
		$this->__set('desc', $type['desc']);

		return $this;
	}

	/**	Charge les contacts associés au type
	 * @return	ContactTypeService	L'instance en cours
	 */
	public function contacts()
	{
		if ($this->id < 1) {
			return $this;
		}
		global $config;

		$sql = '
			select
				cnt.cnt_id as cnt
			from
				cnt_contacts cnt
			inner join
				cnt_contacts_types ctt
			on
					ctt.ctt_tnt_id = ' . $config['tnt_id'] . '
				and ctt.ctt_type_id = ' . $this->id . '
				and ctt.ctt_cnt_id = cnt.cnt_id
			where
					cnt.cnt_tnt_id = ' . $config['tnt_id'] . '
			group by cnt.cnt_id
		';

		$rcnts = ria_mysql_query($sql);

		if (!ria_mysql_num_rows($rcnts)) {
			return $this;
		}
		$this->contacts = [];
		$this->count = 0;

		while ($cnt = ria_mysql_fetch_assoc($rcnts)) {
			$Cnt = new ContactService($cnt['cnt'], $this->id);

			$this->contacts[] = $Cnt;
			$this->count++;
		}

		return $this;
	}

	/** Retourne un tableau des contacts associés au type
	 * @param	bool	$force	Optionnel, Force le chargement des contacts
	 * @return	bool|array	Tableau des contacts associés au type, false sinon
	 */
	public function getContacts($force = false)
	{
		if (is_null($this->contacts) || (is_bool($force) && $force)) {
			$this->contacts();
		}

		if (!is_array($this->contacts) || count($this->contacts) < 1) {
			return false;
		}
		$ar_cnts = [];

		foreach ($this->contacts as $Cnt) {
			$ar_cnts[] = $Cnt->getData();
		}
		return $ar_cnts;
	}

	/**	Retourne un tableau de toutes les informations du type de contact
	 * @return	array	Tableau contenant toutes les informations du type de contact
	 */
	public function getFullData()
	{
		$ar_type = $this->getData(false);

		$ar_type['contacts'] = $this->getContacts();

		return $ar_type;
	}

	/**	Permet de mettre à jour la valeur d'une propriété
	 * @param	string	$property	Obligatoire, Nom de la propriété
	 * @param	mixed	$value		Obligatoire, Valeur de la propriété
	 * @return	ContactTypeService
	 */
	public function __set($property, $value)
	{
		if(!property_exists($this, $property)){
			return $this;
		}
		$this->{$property} = $this->__sanitizeProperty($property, $value);
		return $this;
	}

	/**	Permet de controler la valeur d'une propriété
	 * @param	string	$property	Obligatoire, Nom de la propriété
	 * @param	mixed	$value	Obligatoire, Valeur de la propriété
	 * @return	mixed	La valeur
	 */
	protected function __sanitizeProperty($property, $value)
	{
		switch ($property) {
			case 'name':
			case 'desc':
				return is_string($value) && trim($value) != '' ? $value : null;

			default:
				return false;
		}
		return true;
	}
}
