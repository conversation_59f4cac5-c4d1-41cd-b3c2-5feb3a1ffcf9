<?php

require_once('db.inc.php');
require_once('users.inc.php'); // Pour gu_titles_exists
require_once('strings.inc.php');
require_once('cnt.types.inc.php');

/** \defgroup model_contacts Contacts
 *	\ingroup crm
 *	Ce module comprend les fonctions nécessaires à la gestion des contacts société.
 * @{
 */

// \cond onlyria
/**	Cette fonction permet l'ajout d'un contact société.
 *	@param int $title identifiant de la civilité
 *	@param string $firstname Prénom du contact
 *	@param string $lastname Nom du contact
 *	@param string $email Adresse email du contact
 *	@param string $phone Numéro de téléphone du contact
 *	@param string $fax Numéro de fax du contact
 *	@param bool $fct Facultatif, nom de la fonction du contact
 *
 *	@return int|bool l'identifiant attribué au contact en cas de succès, false en cas d'échec
 */
function cnt_contacts_add( $title, $firstname, $lastname, $email, $phone, $fax, $fct = false ){
	global $config;
	if( !gu_titles_exists($title) ) return false;
	if( !trim($firstname) ) return false;
	if( !trim($lastname) ) return false;

	$firstname = ucfirst(trim($firstname));
	$lastname = ucfirst(trim($lastname));
	$email = trim(strtolower2($email));
	$phone = trim($phone);
	$fax = trim($fax);
	$fct_id = 'NULL';
	if($fct) {
		$fct_result = cnt_contacts_function_get($fct);
		if($fct_result)
			$fct_id = $fct_result['id'];
		else
			$fct_id = cnt_contacts_function_add($fct);
		if(!$fct_id)
			$fct_id = 'NULL';
	}
	$res = ria_mysql_query('
		insert into cnt_contacts
			(cnt_tnt_id, cnt_title_id,cnt_firstname,cnt_lastname,cnt_email,cnt_phone,cnt_fax,cnt_fct_id)
		values
			('.$config['tnt_id'].','.$title.',\''.addslashes($firstname).'\',\''.addslashes($lastname).'\',\''.addslashes($email).'\',\''.addslashes($phone).'\',\''.addslashes($fax).'\','.$fct_id.')
	');
	if( $res ){
		return ria_mysql_insert_id();
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour d'un contact société.
 *	@param int $id Identifiant du contact
 *	@param int $title identifiant de la civilité
 *	@param string $firstname Prénom du contact
 *	@param string $lastname Nom du contact
 *	@param string $email Adresse email du contact
 *	@param string $phone Numéro de téléphone du contact
 *	@param string $fax Numéro de fax du contact
 *	@param string $fct Facultatif, nom de la fonction du contact
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cnt_contacts_update( $id, $title, $firstname, $lastname, $email, $phone, $fax, $fct = false ){
	global $config;
	if( !cnt_contacts_exists($id) ) return false;
	if( !gu_titles_exists($title) ) return false;
	if( !trim($firstname) ) return false;
	if( !trim($lastname) ) return false;

	$firstname = ucfirst(trim($firstname));
	$lastname = ucfirst(trim($lastname));
	$email = trim(strtolower2($email));
	$phone = trim($phone);
	$fax = trim($fax);

	$fct_id = 'NULL';
	if($fct) {
		$fct_result = cnt_contacts_function_get($fct);
		if($fct_result){
			$fct_id = $fct_result['id'];
		}else{
			$fct_id = cnt_contacts_function_add($fct);
		}
		if(!$fct_id){
			$fct_id = 'NULL';
		}
	}

	return ria_mysql_query('
		update cnt_contacts set
			cnt_title_id='.$title.',
			cnt_firstname=\''.addslashes($firstname).'\',
			cnt_lastname=\''.addslashes($lastname).'\',
			cnt_email=\''.addslashes($email).'\',
			cnt_phone=\''.addslashes($phone).'\',
			cnt_fax=\''.addslashes($fax).'\',
			cnt_fct_id='.$fct_id .'
		where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le contrôle d'un identifiant de contact
 *	@param int $id Identifiant de contact à vérifier
 *	@return bool true si l'identifiant existe, false dans le cas contraire
 */
function cnt_contacts_exists( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select cnt_id from cnt_contacts where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id))>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'un contact
 *	@param int $id Identifiant du contact à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cnt_contacts_del( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	ria_mysql_query('delete from cnt_contacts_types where ctt_tnt_id='.$config['tnt_id']. ' and ctt_cnt_id='.$id);
	return ria_mysql_query('delete from cnt_contacts where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$id);
}
// \endcond

/**	Cette fonction permet le chargement d'un ou plusieurs contacts, éventuellement filtrés
 *	en fonction des paramètres optionnels fournis
 *	@param int $id Optionnel, identifiant d'un contact sur lequel filtrer le résultat
 *	@param int $type Optionnel, identifiant d'un type de contact
 *	@return resource un résultat de requête comprenant les colonnes suivantes
 *			- id : identifiant du contact
 *			- title_id : identifiant de la civilité
 *			- title_name : désignation de la civilité
 *			- firstname : prénom du contact
 *			- lastname : nom de famille du contact
 *			- email : adresse email du contact
 *			- phone : numéro de téléphone du contact
 *			- fax : numéro de fax du contact
 *			- img_id : identifiant de l'image rattachée au contact
 *			- func : fonction du contact
 */
function cnt_contacts_get( $id=0, $type=0 ){
	if( !is_numeric($id) || $id<0 ) return false;
	if( $type>0 && !cnt_types_exists($type) ) return false;
	global $config;

	$sql = '
		select cnt_id as id, title_id, title_name, cnt_firstname as firstname, cnt_lastname as lastname, cnt_email as email, cnt_phone as phone, cnt_fax as fax, cnt_img_id as img_id,
		fct_name as func
		from  gu_titles, cnt_contacts
		left join cnt_functions on cnt_functions.fct_id=cnt_contacts.cnt_fct_id
	';
	if( $type>0 )
		$sql .= ' inner join cnt_contacts_types on (ctt_tnt_id=cnt_tnt_id and ctt_cnt_id=cnt_id)';
	$sql .= '
		where cnt_tnt_id='.$config['tnt_id'].'
		and cnt_title_id=title_id
	';
	if( $id>0 )
		$sql .= ' and cnt_id='.$id;
	if( $type>0 )
		$sql .= ' and ctt_type_id='.$type;

	return ria_mysql_query($sql);
}

// \cond onlyria
/** Permet l'ajout d'un fichier image à un contact. Cette fonction est similaire à \c cnt_contacts_image_upload,
 *	à l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile
 *	lors d'importations.
 *
 *	@param int $cnt Obligatoire, Identifiant du contact.
 *	@param string $filename Obligatoire, Nom du fichier image.
 *	@param string $srcname Facultatif, Nom de l'image source
 *
 *	@return int L'identifiant attribué à l'image.
 *	@return bool false en cas d'erreur
 *
 */
function cnt_contacts_image_add( $cnt, $filename, $srcname='' ){
	global $config;

	if( $id = img_images_add( $filename, $srcname ) )
		ria_mysql_query('update cnt_contacts set cnt_img_id='.$id.' where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$cnt);
	return $id;
}
// \endcond

// \cond onlyria
/** Permet l'association d'une image à un contact.
 *
 *	@param int $cnt Obligatoire, Identifiant de la marque.
 *	@param string $fieldname Obligatoire, Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *	@return bool false en cas d'erreur
 *
 */
function cnt_contacts_image_upload( $cnt, $fieldname ){

	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return cnt_contacts_image_add( $cnt, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );

}
// \endcond

// \cond onlyria
/** Permet la suppression de l'image associée à un contact.
 *  @param int $cnt Identifiant du contact dont on souhaite supprimer l'image
 *  @return bool true en cas de succès
 *  @return bool false en cas d'erreur
 */
function cnt_contacts_image_del( $cnt ){
	global $config;

	if( !prd_brands_exists($cnt) ) return false;
	$contact = ria_mysql_fetch_array(cnt_contacts_get($cnt));

	$r = ria_mysql_query('update cnt_contacts set cnt_img_id=null where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$cnt);

	if( $r && $contact['img_id'] ){
		img_images_count_update( $contact['img_id'] );
	}

	return $r;
}
// \endcond

/**	Cette fonction permet le chargement d'un ou plusieurs contacts, regroupés par type de contact
 *	@param int $type Facultatif, identifiant d'un type de contact sur lequel filtrer le résultat
 *	@param int $cnt Facultatif, identifiant d'un contact sur lequel filtrer le résultat
 *	@return resource un résultat de requête comprenant les colonnes suivantes
 *			- type_id : identifiant du type de contact
 *			- type_name : désignation du type de contact
 *			- cnt_id : identifiant du contact
 *			- cnt_title_id : identifiant de la civilité
 *			- cnt_title_name : désignation de la civilité
 *			- cnt_firstname : prénom du contact
 *			- cnt_lastname : nom de famille du contact
 *			- cnt_email : adresse email du contact
 *			- cnt_phone : numéro de téléphone du contact
 *			- cnt_fax : numéro de fax du contact
 *			- img_id : identifiant de d'image associée au contact
 *			- func : fonction du contact
 */
function cnt_types_contacts_get( $type=0, $cnt=0 ){
	global $config;
	$sql = '
		select type_id, type_name,
			cnt_id, title_id as cnt_title_id, title_name as cnt_title_name, cnt_firstname, cnt_lastname, cnt_email, cnt_phone, cnt_fax, cnt_img_id as img_id , fct_name as func
		from cnt_types, cnt_contacts_types, gu_titles, cnt_contacts
		left join cnt_functions on cnt_contacts.cnt_fct_id=cnt_functions.fct_id
		where cnt_tnt_id='.$config['tnt_id'].' and ctt_tnt_id='.$config['tnt_id'].' and type_tnt_id='.$config['tnt_id'].' and type_id=ctt_type_id and ctt_cnt_id=cnt_id and cnt_title_id=title_id
	';
	if( $type>0 )
		$sql .= ' and ctt_type_id='.$type;
	if( $cnt>0 )
		$sql .= ' and ctt_cnt_id='.$cnt;
	$sql .= '
		order by type_id, cnt_id
	';

	return ria_mysql_query($sql);
}

// \cond onlyria
/**	Cette fonction permet la définition de l'assocation contact/types de contacts
 *	@param int $cnt Identifiant du contact
 *	@param array $types Tableau des identifiants de types à associer à ce contact
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cnt_contacts_types_set( $cnt, $types ){
	global $config;
	if( !cnt_contacts_exists($cnt) ) return false;
	if( !is_array($types) ) return false;
	foreach( $types as $t )
		if( !cnt_types_exists($t) )
			return false;

	ria_mysql_query('delete from cnt_contacts_types where ctt_tnt_id='.$config['tnt_id'].' and ctt_cnt_id='.$cnt);
	foreach( $types as $t )
		ria_mysql_query('insert into cnt_contacts_types (ctt_tnt_id, ctt_cnt_id,ctt_type_id) values ('.$config['tnt_id'].','.$cnt.','.$t.')');

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement des types associés à un contact
 *	@param int $cnt Identifiant du contact pour lequel on souhaite charger les types associés
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du type de contact
 *			- name : désignation du type de contact
 *			- desc : description du type de contact
 */
function cnt_contacts_types_get( $cnt ){
	global $config;
	if( !is_numeric($cnt) || $cnt<=0 ) return false;
	return ria_mysql_query('
		select type_id as id, type_name as name, type_desc as "desc"
		from cnt_contacts_types, cnt_types
		where ctt_tnt_id='.$config['tnt_id'].' and type_tnt_id='.$config['tnt_id'].' and ctt_type_id=type_id
			and ctt_cnt_id='.$cnt.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'ajout d'une nouvelle fonction contact
 *	@param string $fct Obligatoire, Nom de la fonction
 *	@return int l'identifiant attribué à la nouvelle fonction en cas de succès, false en cas d'échec
 */
function cnt_contacts_function_add( $fct ){
	global $config;
	if( !$fct ) return false;
	$res = ria_mysql_query('insert into cnt_functions(fct_tnt_id, fct_name) values('.$config['tnt_id'].',\''.addslashes($fct).'\')');
	if( $res ){
		return ria_mysql_insert_id();
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de vérifier l'existence d'une function
 *	@param string $fct Obligaoire, Nom de la fonction
 *	@return resource l'identifiant et le nom de la fonction, sous forme de résultat de requête MySQL, false en cas d'échec
 */
function cnt_contacts_function_get( $fct ){
	global $config;
	if( !$fct ) return false;
	$res = ria_mysql_query('select fct_id as id, fct_name as name from cnt_functions where fct_tnt_id='.$config['tnt_id'].' and fct_name=\''.addslashes($fct).'\'');
	if($res && ria_mysql_num_rows($res))
		return ria_mysql_fetch_array($res);
	else
		return false;
}
// \endcond

/// @}