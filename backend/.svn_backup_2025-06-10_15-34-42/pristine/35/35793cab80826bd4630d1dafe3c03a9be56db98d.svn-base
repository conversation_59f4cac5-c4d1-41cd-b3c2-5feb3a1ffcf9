<?xml version="1.0" encoding="utf-8"?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Symfony\Component\DependencyInjection\Tests\Fixtures\NamedArgumentsDummy">
            <argument key="$apiKey">ABCD</argument>
            <argument key="Symfony\Component\DependencyInjection\Tests\Fixtures\CaseSensitiveClass">null</argument>
            <call method="setApiKey">
                <argument key="$apiKey">123</argument>
            </call>
        </service>
    </services>
</container>
