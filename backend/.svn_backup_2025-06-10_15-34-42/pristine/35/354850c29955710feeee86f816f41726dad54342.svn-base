<?php
	/** \file users.php
	 *  Ce script est destiné à supprimer les comptes, et leurs données liées, lorsque ces derniers sont supprimés
	 */

  $date_del_users = new DateTime();
  $date_del_users->modify( '-'.$cfg_days['users']['days'].' days' );

  $date_del_users_histo_login = new DateTime();
  $date_del_users_histo_login->modify( '-'.$cfg_days['histo_login']['days'].' days' );

  $date_del_users_sell_unit = new DateTime();
  $date_del_users_sell_unit->modify( '-'.$cfg_days['sell_unit']['days'].' days' );

  $date_del_users_livr_alert = new DateTime();
  $date_del_users_livr_alert->modify( '-'.$cfg_days['livr_alert']['days'].' days' );

  { // Suppression de tous les comptes liés supprimés depuis plus de $cfg_days['users']['days']
    $sql_del = '
      delete from gu_users
      where usr_tnt_id in (0, '.$config['tnt_id'].')
        and usr_date_deleted is not null
        and date(usr_date_deleted) <= "'.$date_del_users->format('Y-m-d').'"
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des adresses postals liées à un compte qui n'existe plus
    // Seules les adresses liées à aucune commande validée seront supprimées
    $sql_del = '
      delete from gu_adresses
      where adr_tnt_id in (0, '.$config['tnt_id'].')
        and not exists (
          select 1
          from gu_users
          where adr_tnt_id in (0, '.$config['tnt_id'].')
          and adr_usr_id = usr_id
        )
        and not exists (
          select 1 from ord_orders
          where ord_tnt_id = '.$config['tnt_id'].' and ord_usr_id = adr_usr_id and ord_adr_invoices = adr_id
          and ord_state_id not in ('.implode( ', ', ord_states_get_ord_valid() ).')
        )
        and not exists (
          select 1 from ord_orders
          where ord_tnt_id = '.$config['tnt_id'].' and ord_usr_id = adr_usr_id and ord_adr_delivery = adr_id
          and ord_state_id not in ('.implode( ', ', ord_states_get_ord_valid() ).')
        )
        and not exists (
          select 1 from ord_orders
          where ord_tnt_id = '.$config['tnt_id'].' and ord_usr_id = adr_usr_id and ord_adr_final = adr_id
          and ord_state_id not in ('.implode( ', ', ord_states_get_ord_valid() ).')
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des adresses liées aux comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des adresses liées aux comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des droits liés aux comptes supprimés
    $sql_del = '
      delete from gu_users_rights
      where urg_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from gu_users
          where usr_tnt_id in (0, '.$config['tnt_id'].')
            and urg_usr_id = usr_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des droits liés aux comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des droits liés aux comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des CGV appliquées des CGV appliquées aux comptes supprimés
    $sql_del = '
      delete from gu_users_cgv
      where usg_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from gu_users
          where usr_tnt_id in (0, '.$config['tnt_id'].')
            and usg_usr_id = usr_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des CGV appliquées aux comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des CGV appliquées aux comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression de l'historique de connexion depuis plus de $cfg_days['histo_login']['days']
    $sql_del = '
      delete from gu_users_logins
      where usl_tnt_id = '.$config['tnt_id'].'
        and date(usl_date_login) <= "'.$date_del_users_histo_login->format('Y-m-d').'"
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression de l\'historique de connexion. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression de l\'historique de connexion : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression de l'historique de connexion des comptes supprimés
    $sql_del = '
      delete from gu_users_logins
      where usl_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from gu_users
          where usr_tnt_id in (0, '.$config['tnt_id'].')
            and usl_usr_id = usr_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de l\'historique de connexion des comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression de l\'historique de connexion des comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des CB enregistrées des comptes supprimés
    $sql_del = '
      delete from gu_users_payment_credentials
      where upc_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from gu_users
          where usr_tnt_id in (0, '.$config['tnt_id'].')
            and upc_usr_id = usr_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des CB enregistrées liées aux comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des CB enregistrées des comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des moyens de paiements des comptes supprimés
    $sql_del = '
      delete from gu_users_payment_types
      where upt_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from gu_users
          where usr_tnt_id in (0, '.$config['tnt_id'].')
            and upt_usr_id = usr_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des moyens de paiements liés aux comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des moyens de paiements liés aux comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des contacts des contacts liés aux comptes supprimés
    $sql_del = '
      delete from gu_contacts
      where cnt_tnt_id = '.$config['tnt_id'].'
        and cnt_usr_id is not null
        and not exists (
          select 1 from gu_users
          where usr_tnt_id in (0, '.$config['tnt_id'].')
            and cnt_usr_id = usr_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des contacts liés aux comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des contacts liés aux comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des alerts de disponibilité depuis plus de $cfg_days['sell_unit']['days']
    $sql_del = '
      delete from gu_livr_alerts
      where alert_tnt_id = '.$config['tnt_id'].'
        and date(alert_date_created) <= "'.$date_del_users_livr_alert->format('Y-m-d').'"
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des alerts de disponibilité. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des alerts de disponibilité : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des alerts de disponibilité des comptes supprimés
    $sql_del = '
      delete from gu_livr_alerts
      where alert_tnt_id = '.$config['tnt_id'].'
        and alert_usr_id is not null
        and not exists (
          select 1 from gu_users
          where usr_tnt_id in (0, '.$config['tnt_id'].')
            and alert_usr_id = usr_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des alerts de disponibilité liées aux comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des alerts de disponibilité liées aux comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des unités de vente personnalisées supprimées depuis plus de $cfg_days['livr_alert']['days']
    $sql_del = '
      delete from gu_sell_units_rules
      where sur_tnt_id = '.$config['tnt_id'].'
        and sur_date_deleted is not null
        and date(sur_date_deleted) <= "'.$date_del_users_sell_unit->format('Y-m-d').'"
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des unités de vente personnalisées supprimées. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des unités de vente personnalisées supprimées : '.ria_mysql_affected_rows().PHP_EOL;
    }
  }

  { // Suppression des unités de vente personnalisées des comptes supprimés
    $sql_del = '
      delete from gu_sell_units_rules
      where sur_tnt_id = '.$config['tnt_id'].'
        and not exists (
          select 1 from gu_users
          where usr_tnt_id in (0, '.$config['tnt_id'].')
            and sur_usr_id = usr_id
        )
    ';

    $res_del = ria_mysql_query( $sql_del );
    if( !$res_del ){
      throw new Exception( 'Erreur lors de la suppression des unités de vente personnalisées liés aux comptes supprimés. => '.ria_mysql_error().' | '.$sql_del );
    }

    if( $debug ){
      print ' Suppression des unités de vente personnalisées liés aux comptes supprimés : '.ria_mysql_affected_rows().PHP_EOL;
    }

  }
