Shell execution
-----
<?php
``;
`test`;
`test $A`;
`test \``;
`test \"`;
-----
array(
    0: Expr_ShellExec(
        parts: array(
        )
    )
    1: Expr_ShellExec(
        parts: array(
            0: Scalar_EncapsedStringPart(
                value: test
            )
        )
    )
    2: Expr_ShellExec(
        parts: array(
            0: Scalar_EncapsedStringPart(
                value: test
            )
            1: Expr_Variable(
                name: A
            )
        )
    )
    3: Expr_ShellExec(
        parts: array(
            0: Scalar_EncapsedStringPart(
                value: test `
            )
        )
    )
    4: Expr_ShellExec(
        parts: array(
            0: Scalar_EncapsedStringPart(
                value: test \"
            )
        )
    )
)