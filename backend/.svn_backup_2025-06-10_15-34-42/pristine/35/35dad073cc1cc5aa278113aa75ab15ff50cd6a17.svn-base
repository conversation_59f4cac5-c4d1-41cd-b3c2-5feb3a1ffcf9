<?php
/**
 * PublicationFeedReporting
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * PublicationFeedReporting Class Doc Comment
 *
 * @category Class
 * @description Publication feed reporting
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class PublicationFeedReporting implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'publicationFeedReporting';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'feed_type' => '\Swagger\Client\Model\FeedType',
        'start_utc_date' => '\DateTime',
        'end_utc_date' => '\DateTime',
        'processing_status' => 'string',
        'exported_products' => 'int',
        'transmitted_items' => 'int',
        'published_items' => 'int',
        'published_items_with_warning' => 'int',
        'failed_items' => 'int',
        'error_message' => 'string',
        'html_report_url' => 'string',
        'html_report_generation_error_message' => 'string',
        'completed' => 'bool'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'feed_type' => null,
        'start_utc_date' => 'date-time',
        'end_utc_date' => 'date-time',
        'processing_status' => null,
        'exported_products' => 'int32',
        'transmitted_items' => 'int32',
        'published_items' => 'int32',
        'published_items_with_warning' => 'int32',
        'failed_items' => 'int32',
        'error_message' => null,
        'html_report_url' => 'uri',
        'html_report_generation_error_message' => null,
        'completed' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'feed_type' => 'feedType',
        'start_utc_date' => 'startUtcDate',
        'end_utc_date' => 'endUtcDate',
        'processing_status' => 'processingStatus',
        'exported_products' => 'exportedProducts',
        'transmitted_items' => 'transmittedItems',
        'published_items' => 'publishedItems',
        'published_items_with_warning' => 'publishedItemsWithWarning',
        'failed_items' => 'failedItems',
        'error_message' => 'errorMessage',
        'html_report_url' => 'htmlReportUrl',
        'html_report_generation_error_message' => 'htmlReportGenerationErrorMessage',
        'completed' => 'completed'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'feed_type' => 'setFeedType',
        'start_utc_date' => 'setStartUtcDate',
        'end_utc_date' => 'setEndUtcDate',
        'processing_status' => 'setProcessingStatus',
        'exported_products' => 'setExportedProducts',
        'transmitted_items' => 'setTransmittedItems',
        'published_items' => 'setPublishedItems',
        'published_items_with_warning' => 'setPublishedItemsWithWarning',
        'failed_items' => 'setFailedItems',
        'error_message' => 'setErrorMessage',
        'html_report_url' => 'setHtmlReportUrl',
        'html_report_generation_error_message' => 'setHtmlReportGenerationErrorMessage',
        'completed' => 'setCompleted'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'feed_type' => 'getFeedType',
        'start_utc_date' => 'getStartUtcDate',
        'end_utc_date' => 'getEndUtcDate',
        'processing_status' => 'getProcessingStatus',
        'exported_products' => 'getExportedProducts',
        'transmitted_items' => 'getTransmittedItems',
        'published_items' => 'getPublishedItems',
        'published_items_with_warning' => 'getPublishedItemsWithWarning',
        'failed_items' => 'getFailedItems',
        'error_message' => 'getErrorMessage',
        'html_report_url' => 'getHtmlReportUrl',
        'html_report_generation_error_message' => 'getHtmlReportGenerationErrorMessage',
        'completed' => 'getCompleted'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['feed_type'] = isset($data['feed_type']) ? $data['feed_type'] : null;
        $this->container['start_utc_date'] = isset($data['start_utc_date']) ? $data['start_utc_date'] : null;
        $this->container['end_utc_date'] = isset($data['end_utc_date']) ? $data['end_utc_date'] : null;
        $this->container['processing_status'] = isset($data['processing_status']) ? $data['processing_status'] : null;
        $this->container['exported_products'] = isset($data['exported_products']) ? $data['exported_products'] : null;
        $this->container['transmitted_items'] = isset($data['transmitted_items']) ? $data['transmitted_items'] : null;
        $this->container['published_items'] = isset($data['published_items']) ? $data['published_items'] : null;
        $this->container['published_items_with_warning'] = isset($data['published_items_with_warning']) ? $data['published_items_with_warning'] : null;
        $this->container['failed_items'] = isset($data['failed_items']) ? $data['failed_items'] : null;
        $this->container['error_message'] = isset($data['error_message']) ? $data['error_message'] : null;
        $this->container['html_report_url'] = isset($data['html_report_url']) ? $data['html_report_url'] : null;
        $this->container['html_report_generation_error_message'] = isset($data['html_report_generation_error_message']) ? $data['html_report_generation_error_message'] : null;
        $this->container['completed'] = isset($data['completed']) ? $data['completed'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['feed_type'] === null) {
            $invalidProperties[] = "'feed_type' can't be null";
        }
        if ($this->container['start_utc_date'] === null) {
            $invalidProperties[] = "'start_utc_date' can't be null";
        }
        if ($this->container['processing_status'] === null) {
            $invalidProperties[] = "'processing_status' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['feed_type'] === null) {
            return false;
        }
        if ($this->container['start_utc_date'] === null) {
            return false;
        }
        if ($this->container['processing_status'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets feed_type
     *
     * @return \Swagger\Client\Model\FeedType
     */
    public function getFeedType()
    {
        return $this->container['feed_type'];
    }

    /**
     * Sets feed_type
     *
     * @param \Swagger\Client\Model\FeedType $feed_type feed_type
     *
     * @return $this
     */
    public function setFeedType($feed_type)
    {
        $this->container['feed_type'] = $feed_type;

        return $this;
    }

    /**
     * Gets start_utc_date
     *
     * @return \DateTime
     */
    public function getStartUtcDate()
    {
        return $this->container['start_utc_date'];
    }

    /**
     * Sets start_utc_date
     *
     * @param \DateTime $start_utc_date The feed publication start time (UTC timezone)
     *
     * @return $this
     */
    public function setStartUtcDate($start_utc_date)
    {
        $this->container['start_utc_date'] = $start_utc_date;

        return $this;
    }

    /**
     * Gets end_utc_date
     *
     * @return \DateTime
     */
    public function getEndUtcDate()
    {
        return $this->container['end_utc_date'];
    }

    /**
     * Sets end_utc_date
     *
     * @param \DateTime $end_utc_date The feed publication end time (UTC timezone)
     *
     * @return $this
     */
    public function setEndUtcDate($end_utc_date)
    {
        $this->container['end_utc_date'] = $end_utc_date;

        return $this;
    }

    /**
     * Gets processing_status
     *
     * @return string
     */
    public function getProcessingStatus()
    {
        return $this->container['processing_status'];
    }

    /**
     * Sets processing_status
     *
     * @param string $processing_status The processing status
     *
     * @return $this
     */
    public function setProcessingStatus($processing_status)
    {
        $this->container['processing_status'] = $processing_status;

        return $this;
    }

    /**
     * Gets exported_products
     *
     * @return int
     */
    public function getExportedProducts()
    {
        return $this->container['exported_products'];
    }

    /**
     * Sets exported_products
     *
     * @param int $exported_products The product count downloaded from BeezUP Export
     *
     * @return $this
     */
    public function setExportedProducts($exported_products)
    {
        $this->container['exported_products'] = $exported_products;

        return $this;
    }

    /**
     * Gets transmitted_items
     *
     * @return int
     */
    public function getTransmittedItems()
    {
        return $this->container['transmitted_items'];
    }

    /**
     * Sets transmitted_items
     *
     * @param int $transmitted_items The item count (products or offers) sent to the marketplace
     *
     * @return $this
     */
    public function setTransmittedItems($transmitted_items)
    {
        $this->container['transmitted_items'] = $transmitted_items;

        return $this;
    }

    /**
     * Gets published_items
     *
     * @return int
     */
    public function getPublishedItems()
    {
        return $this->container['published_items'];
    }

    /**
     * Sets published_items
     *
     * @param int $published_items The item count (products or offers) the marketplace flagged as successful
     *
     * @return $this
     */
    public function setPublishedItems($published_items)
    {
        $this->container['published_items'] = $published_items;

        return $this;
    }

    /**
     * Gets published_items_with_warning
     *
     * @return int
     */
    public function getPublishedItemsWithWarning()
    {
        return $this->container['published_items_with_warning'];
    }

    /**
     * Sets published_items_with_warning
     *
     * @param int $published_items_with_warning The item count (products or offers) the marketplace flagged as successful with warnings
     *
     * @return $this
     */
    public function setPublishedItemsWithWarning($published_items_with_warning)
    {
        $this->container['published_items_with_warning'] = $published_items_with_warning;

        return $this;
    }

    /**
     * Gets failed_items
     *
     * @return int
     */
    public function getFailedItems()
    {
        return $this->container['failed_items'];
    }

    /**
     * Sets failed_items
     *
     * @param int $failed_items The item count (products or offers) the marketplace flagged as failed
     *
     * @return $this
     */
    public function setFailedItems($failed_items)
    {
        $this->container['failed_items'] = $failed_items;

        return $this;
    }

    /**
     * Gets error_message
     *
     * @return string
     */
    public function getErrorMessage()
    {
        return $this->container['error_message'];
    }

    /**
     * Sets error_message
     *
     * @param string $error_message The error message
     *
     * @return $this
     */
    public function setErrorMessage($error_message)
    {
        $this->container['error_message'] = $error_message;

        return $this;
    }

    /**
     * Gets html_report_url
     *
     * @return string
     */
    public function getHtmlReportUrl()
    {
        return $this->container['html_report_url'];
    }

    /**
     * Sets html_report_url
     *
     * @param string $html_report_url The Url for the Html Report generated
     *
     * @return $this
     */
    public function setHtmlReportUrl($html_report_url)
    {
        $this->container['html_report_url'] = $html_report_url;

        return $this;
    }

    /**
     * Gets html_report_generation_error_message
     *
     * @return string
     */
    public function getHtmlReportGenerationErrorMessage()
    {
        return $this->container['html_report_generation_error_message'];
    }

    /**
     * Sets html_report_generation_error_message
     *
     * @param string $html_report_generation_error_message The error message if the Html Report generation failed
     *
     * @return $this
     */
    public function setHtmlReportGenerationErrorMessage($html_report_generation_error_message)
    {
        $this->container['html_report_generation_error_message'] = $html_report_generation_error_message;

        return $this;
    }

    /**
     * Gets completed
     *
     * @return bool
     */
    public function getCompleted()
    {
        return $this->container['completed'];
    }

    /**
     * Sets completed
     *
     * @param bool $completed Indicates if the publication is completed or not
     *
     * @return $this
     */
    public function setCompleted($completed)
    {
        $this->container['completed'] = $completed;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


