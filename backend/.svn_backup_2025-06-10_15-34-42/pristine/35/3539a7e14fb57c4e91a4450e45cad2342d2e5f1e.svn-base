<?php
/**
 *  \defgroup api-stocks-index Stocks 
 *  \ingroup scm
 *  @{
*/
switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-stocks-index-upd Mise à jour
	 *	
	 * Cette fonction permet la mise à jour des stocks
	 *
	 *		\code
	 *			PUT /stocks/
	 *		\endcode
	 *
	 * @param raw_data Obligatoire, Donnée en json_decode :
	 *		\code{.json}
	 *			{
	 *				"prd"			Obligatoire	: Identifiant du produit
	 *				"dps"			Obligatoire	: Identifiant du dépôt
	 *				"qty | qte"		Obligatoire	: Stock disponible
	 *				"ord | com"		Obligatoire	: Stock commandé
	 *				"res"			Obligatoire	: Stock réservé
	 *				"pre"			Obligatoire : Stock en préparation
	 *				"min"			Obligatoire	: Seuil d'alerte du stock minimun
	 *				"max"			Obligatoire	: Seuil d'alerte du stock maximum
	 *			}
	 *		\endcode
	 *
	 * @return true si la mise à jour est effectuée avec succès 
	 *	@}
	*/
	case 'upd':
		global $method, $config;
		$obj = json_decode($raw_data, true);
		if( !is_array($obj) ){
			throw new Exception("Paramètres invalide");
		}

		foreach($obj as $stock){
			if(!isset($stock["prd"], $stock["dps"], $stock["res"], $stock["pre"], $stock["min"], $stock["max"])){
				throw new Exception("Paramètres invalide");
			}
			if(isset($stock["qty"])){
				$qty = $stock["qty"];
			}
			else if(isset($stock["qte"])){
				$qty = $stock["qte"];
			}
			else{
				throw new Exception("Paramètres invalide");
			}
			if(isset($stock["ord"])){
				$ord = $stock["ord"];
			}
			else if(isset($stock["com"])){
				$ord = $stock["com"];
			}
			else{
				throw new Exception("Paramètres invalide");
			}
			if(!prd_dps_stocks_update($stock["prd"], $stock["dps"], $qty, $stock["res"], $ord, $stock["pre"], $stock["min"], $stock["max"])){
				throw new Exception("La mise à jour des stocks a échoué.");
			}
		}
		$result = true;
		break;
	/** @{@}
 	 *	@{
	 *	\page api-stocks-index-del Suppression 
	 *
	 *	Cette fonction permet la suppression des stocks
	 *
	 *		\code
	 *			DELETE /stocks/
	 *		\endcode
	 *
	 * @param raw_data Obligatoire, Donnée en json_decode:
	 *		\code{.json}
	 *			{
	 *				"prd"	Obligatoire	: Identifiant du produit
	 *				"dps"	Obligatoire	: Identifiant du dépôt
	 *			}
	 *		\endcode	 
	 * @return true si la suppression s'est déroulée avec succès 
	 *	@}
	*/
	case 'del':
		global $method, $config;
		$obj = json_decode($raw_data, true);
		if( !is_array($obj) ){
			throw new Exception("Paramètres invalide");
		}

		foreach($obj as $stock){
			if(!isset($stock["prd"], $stock["dps"])){
				throw new Exception("Paramètres invalide");
			}
			if(!prd_dps_stocks_del($stock["prd"], $stock["dps"])){
				throw new Exception("La suppression des stocks a échoué.");
			}
		}
		$result = true;
		break;
}
///@}