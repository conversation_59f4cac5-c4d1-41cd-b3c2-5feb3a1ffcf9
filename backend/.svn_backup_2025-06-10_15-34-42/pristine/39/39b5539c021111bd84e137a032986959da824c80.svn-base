<?php

// \cond onlyria

/**
 *\defgroup api-riashopsync-status Permet de connaitre le status de la synchro comme les version des softs
 *\ingroup sync
 *@{
 *	 \page api-riashopsync-status-get
 *
 *	 cette fonction
 *
 *		\code
 *			GET /riashopsync/status/
 *		\endcode
 *
 *	 @return Un tableau avec les données suivantes :
 * 			{
 *				branche : nom de la branche sur laquel est le tenant
 *				manager_version : dernier numéro de version pour le manager disponible sur cette branche
 *				updater_version : dernier numéro de version pour l'updater disponible sur cette branche
 * 			}
 *
 */

switch( $method ){
    case 'get':


		$branch_code = $config["sync_updater_branch"].'-standard';

    	$content = array(
    		'running' => tnt_tenants_get_sync_running(),
    		'branche' => $config["sync_updater_branch"],
    		"manager_version" => '0.0.0.0',
    		"updater_version" => '0.0.0.0',
    	);


		$folder_path = _RIASHOPSYNC_DEPLOY.$branch_code.'/';

		if( is_dir($folder_path) ){

			$files = array_diff(scandir($folder_path), array('.', '..'));

			foreach( $files as $f ){
				$regex_manager = "/^RiaShopSync_".$branch_code."_(.*)_\.zip/";
				$regex_updater = "/^RiaShopSyncUpdater_".$branch_code."_(.*)_\.zip/";
				if( preg_match($regex_manager, $f) ){
					$content['manager_version'] = str_replace('-','.',preg_replace($regex_manager, "$1", $f));
				}
				else if( preg_match($regex_updater, $f) ){
					$content['updater_version'] = str_replace('-','.',preg_replace($regex_updater, "$1", $f));
				}
			}
		}

    	$result = true;
		break;

	case 'upd':

		if( !isset($_REQUEST['type']) ){
			throw new Exception("Erreur dans le type du status");
		}

		$state_available = array( _RIASHOPSYNC_STATE_RUNNING, _RIASHOPSYNC_STATE_STOPING, _RIASHOPSYNC_STATE_STARTING, _RIASHOPSYNC_STATE_STOP);
		if( !in_array($_REQUEST['type'], $state_available) ){
			throw new Exception("Erreur dans le type du status");
		}

		if( !tnt_tenants_set_sync_running($_REQUEST['type']) ){
			throw new Exception("Erreur dans la mise à jour du status de la synchronisation.");
		}

		$result = true;
		break;
}

///@}

// \endcond