#popup_riawysiwyg{
	background-color: white;
	top:0px;
	left: 0px;
	z-index:99999;
}
iframe{
	border: none;
	width:100%;
}
.clear{
	clear: both;
}
*{
	padding: 0;
	margin: 0;
}
#riashtml{
	width: 600px !important;
	height: 400px !important;
	margin-bottom: 5px;
}
#wys{
	border: 1px solid #A9A9A9 !important;
	margin-bottom: 5px;
	background-color: white;
	width: 99% !important;
	height: 600px !important;
}
#shadows{
	position: absolute;
	top:0px;
	left: 0px;
	height:100%;
	width: 100%;
	background-color: black;
}
#popup_riawysiwyg .onclick{
	cursor: pointer;
}
#popup_riawysiwyg table{
	border:1px solid #C4C4C4;
	border-collapse:collapse;
	width:100%;
}
#popup_riawysiwyg table th, #popup_riawysiwyg table td{
	border:1px solid #C4C4C4;
	padding:5px;
}
#cms_add_img #lstimg .input{
	float:left;
}
#cms_add_img #lstimg .submit{
	margin-left:20px;
}
#popup_riawysiwyg form, #cms_add_img #lstimg{
	background-color:#CFCFCF;
	color:#232E63;
	padding-bottom:10px;
	padding-left:20px;
	padding-top:10px;
}
#popup_riawysiwyg #load{
	display:none;
	position: relative;
	margin-top:-13px;
	height: 20px;
	width: 20px;
	background-image:url(../images/loader.gif);
	background-repeat:no-repeat;
}
#popup_riawysiwyg #res{
	padding-bottom:50px;
}
#popup_riawysiwyg #riacontent{
	padding: 0px 20px 0px 20px;
	background-color: white;
}
#popup_riawysiwyg #tabs ul{
	margin-left: 20px;
	margin-top: 0px;
	padding-top:10px;
}
#popup_riawysiwyg #tabs ul li{
	display:inline;
	list-style-type: none;
	color: white;
}
#popup_riawysiwyg #tabs ul li a{
	background-image:url(../images/tabstrip/default.gif);
	background-repeat:no-repeat;
	border-bottom:1px solid #8F8F8F;
	color:black;
	display:block;
	float:left;
	height:15px;
	padding-top:3px;
	text-align:center;
	width:100px;
}
#popup_riawysiwyg #res .lst_img{
	border: solid 1px #DFDFDF;
	margin: 7px;
	padding: 5px 0px 5px 0px;
	width: 170px;
	float: left;
	text-align: center;
}
#popup_riawysiwyg #res .lst_att img{
	border: none;
}
#popup_riawysiwyg #res .lst_att a{
	float: right;
	margin-right: 10px;
}
#popup_riawysiwyg #res .lst_att img.thumb{
	border: solid 1px #DFDFDF;
	margin-bottom: 5px;
}
#popup_riawysiwyg #res .lst_att select{
	width: 70px;
	float: left;
	margin-left: 10px;
}
#popup_riawysiwyg #res .img{
	display:none;
}
#popup_riawysiwyg #tabs ul li a:hover{
	background-image:url(../images/tabstrip/selected.gif);
}
#popup_riawysiwyg #tabs ul li a.select{
	background-image:url(../images/tabstrip/selected.gif);
	border-bottom: 1px solid white;
}
#site-content table tbody  #wymenu input, #site-content table tbody  #wysshtmlmenu input{
	border: none;
	height: 16px;
	width: 16px !important;
	margin:3px;
	background-color: white;
	background-repeat: no-repeat;
	cursor: pointer;
}/*
#site-content table tbody  #wymenu .h1{
	background-image: url(../images/cms/text_heading_1.png);
}
#site-content table tbody  #wymenu .h2{
	background-image: url(../images/cms/text_heading_2.png);
}
#site-content table tbody  #wymenu .h3{
	background-image: url(../images/cms/text_heading_3.png);
}
#site-content table tbody  #wymenu .h4{
	background-image: url(../images/cms/text_heading_4.png);
}
#site-content table tbody  #wymenu .h5{
	background-image: url(../images/cms/text_heading_5.png);
}*/
#site-content table tbody  #wymenu .souligne{
	background-image: url(../images/cms/text_underline.png);
}
#site-content table tbody  #wymenu .gras{
	background-image: url(../images/cms/text_bold.png);
}
#site-content table tbody  #wymenu .italic{
	background-image: url(../images/cms/text_italic.png);
}
#site-content table tbody  #wymenu .link{
	background-image: url(../images/cms/link.png);
}
#site-content table tbody  #wymenu .unlink{
	background-image: url(../images/cms/link_break.png);
}
#site-content table tbody  #wymenu .listeanum{
	background-image: url(../images/cms/text_list_numbers.png);
}
#site-content table tbody  #wymenu .listeapuce{
	background-image: url(../images/cms/text_list_bullets.png);
}
#site-content table tbody  #wymenu .alignright{
	background-image: url(../images/cms/text_align_right.png);
}
#site-content table tbody  #wymenu .alignleft{
	background-image: url(../images/cms/text_align_left.png);
}
/*
#site-content table tbody  #wymenu .p{
	background-image: url(../images/cms/text_dropcaps.png);
}*/
#site-content table tbody  #wymenu .img{
	background-image: url(../images/cms/picture.png);
}
#site-content table tbody  #wymenu .removeformat{
	background-image: url(../images/cms/cancel.png);
}
#site-content table tbody  #wymenu .code{
	background-image: url(../images/cms/script_code_red.png);
}
#site-content table tbody  #wymenu .html{
	background-image: url(../images/cms/html.png);
}
#site-content table tbody  #wysshtmlmenu .assistant{
	background-image: url(../images/cms/xhtml_go.png);
}
#site-content table tbody  #wymenu #format{
	width: 170px;
	height: 18px;
}
#site-content table tbody  #wymenu #format option.h1{
	font-size: 26px;
	font-weight: bold;
}
#site-content table tbody  #wymenu #format option.h2{
	font-size: 22px;
	font-weight: bold;
}
#site-content table tbody  #wymenu #format option.h3{
	font-size: 18px;
	font-weight: bold;
}
#site-content table tbody  #wymenu #format option.h4{
	font-size: 15px;
	font-weight: bold;
}
#site-content table tbody  #wymenu #format option.h5{
	font-size: 12px;
	font-weight: bold;
}
#site-content table tbody  #wymenu #format option.format{
	background-color: #F3F3F3;
	
}
#site-content table tbody  #wymenu img{
	border: none;
	
	margin-bottom: 5px;
}
.doc-tab-ligne{
	cursor: pointer;
}
.doc-tab-ligne:hover{
	background: #E3E3E3;
}