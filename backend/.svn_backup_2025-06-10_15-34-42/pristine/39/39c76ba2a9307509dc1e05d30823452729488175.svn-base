<?php

/** 
 * \defgroup api-i18n-languages Langages 
 * \ingroup i18n
 * @{	
 * \page api-i18n-languages-get Chargement
 *
 * Cette fonction récupère la liste des langages du monde 
 *
 * \code
 *		GET /i18n/Languages/
 * \endcode
 *	
 * @return Json Liste des languages sous la forme suivante :
 * \code{.json}
 *	{
 *		"code": Code norme ISO 639-1 du languages (2 caractères),
 *		"nameFR": nom du language en français,
 *		"nameEN": nom du language en anglais
 *	}
 * \endcode
 * @}
*/

switch ($method) {
	case 'get':
		$result = true;
		$string = file_get_contents(dirname(__FILE__)."/languages.json");
		$content = json_decode($string, true);
		break;
}
