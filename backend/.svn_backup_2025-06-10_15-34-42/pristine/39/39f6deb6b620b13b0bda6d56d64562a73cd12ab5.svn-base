<?php

    /** \file order-product-update.php
     *  Ce fichier permet de gérer les actions de mise à jour des lignes de commandes lors de l'édition de commande.
     *  Il est possible de mettre à jour :
     *      le nom : $_POST['content-type'] = 'name'
     *      le commentaire : $_POST['content-type'] = 'comment'
     *  
     *  Toujours passer en paramètre le $_POST['prd_id'] et $_POST['ord_id'] ainsi que $_POST['content'] qui contiendra la valeur de remplacement.
     */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

    $product_name_maximum_length = 69;

    if( !isset($_POST['prd_id']) || !is_numeric($_POST['prd_id']) || $_POST['prd_id'] < 0 ){
        print json_encode(array('code' => '400', 'response' => _('L\'identifiant du produit est invalide ou manquant.')));
    } elseif( !isset($_POST['ord_id']) || !is_numeric($_POST['ord_id']) || $_POST['ord_id'] <= 0 ){
        print $_POST['ord_id'];
        print json_encode(array('code' => '400', 'response' => _('L\'identifiant de la commande est invalide ou manquant.')));
    } elseif( !isset($_POST['line_id']) || !is_numeric($_POST['line_id']) || $_POST['line_id'] < 0 ){
        print json_encode(array('code' => '400', 'response' => _('L\'identifiant de la ligne du produit est invalide ou manquant.')));
    } elseif( !isset($_POST['content']) ){
        print json_encode(array('code' => '400', 'response' => _('Le contenu est manquant')));
    }elseif( !isset($_POST['content_type']) || trim($_POST['content_type']) === '' ){
        print json_encode(array('code' => '400', 'response' => _('Le type de contenu est invalide ou manquant.')));
    }elseif( trim($_POST['content_type']) === 'name' && mb_strlen($_POST['content']) > $product_name_maximum_length ){
        print json_encode(array('code' => '400', 'response' => _('Le contenu doit être de maximum 69 caractères.')));
    }else{
        $content_type = $_POST['content_type'];

        switch( $content_type ){
            case 'name':
                if( !ord_products_update_name( $_POST['ord_id'], $_POST['prd_id'], $_POST['line_id'], $_POST['content']) ){
                    $error = _('Une erreur est survenue lors de la modification de la désignation du produit.');
                }

                break;
            case 'comment':
                if( $_POST['prd_id'] ){
                    if( !ord_products_notes_update( $_POST['ord_id'], $_POST['prd_id'], $_POST['content'], null, $_POST['line_id']) ){
                        $error = _('Une erreur est survenue lors de la modification de du commentaire du produit.');
                    }
                } else {
                    if( !ord_products_set_spacing($_POST['ord_id'], $_POST['line_id'], $_POST['content']) ){
                        $error = _('Une erreur est survenue lors de la modification de l\'interligne.');
                    }
                }
                break;
            default:
                $error = sprintf(_('Le type de mofification %s n\'est pas pris en charge.'), $content_type);
        }

        if( isset($error) ){
            print json_encode(array('code' => '400', 'response' => $error));
        } else {
            print json_encode(array('code' => '100', 'response' => $_POST['prd_id'] ? _('La modification du produit s\'est correctement déroulé.') : _('La modification de l\'interligne s\'est correctement déroulé.')));
        }
    }
