{"name": "webmozart/assert", "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "autoload-dev": {"psr-4": {"Webmozart\\Assert\\Tests\\": "tests/"}}}