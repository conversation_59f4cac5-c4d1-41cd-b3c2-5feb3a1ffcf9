<?php
	/** \file delayed.php
	 *  Ce fichier contient l'affichage du tableau "Articles commandés en attente de livraison (reliquats)" présent dans l'onglet "Commandes" d'un compte client.
	 * 	Afin d'afficher ce table, il faut que la variable "$usr" soit défini en amont de l'include de ce fichier.
	 */
	if( !isset($usr) ){
		header('Location: /admin/customers/index.php');
		exit;
	}
	
	// Charge la liste des reliquats
	$delayed = ord_products_get_delayed($_GET['usr']);
	
	// Le tableau n'apparaît que s'il y a des reliquats
	if( $delayed && ria_mysql_num_rows($delayed) ){
?>
<table class="tb-remainders">
	<caption><?php print _('Articles commandés en attente de livraison (reliquats)')?></caption>
	<thead>
		<tr class="thead-none">
			<th id="prd-ref" class="col145px"><?php print _('Référence')?></th>
			<th id="prd-name" class="col335"><?php print _('Désignation')?></th>
			<th id="prd-qte" class="col90 align-right"><?php print _('Qté')?></th>
			<!--<th id="prd-date-livr">Livraison souhaitée</th>-->
			<th id="prd-avail" class="col170px align-right"><?php print _('Date de disponibilité')?></th>
		</tr>
	</thead>
	<tbody><?php
		$last_ord = '';
		if( !$delayed || !ria_mysql_num_rows($delayed) ){
			print '<tr><td colspan="4">'._('Aucun article commandé en attente de livraison').'</td></tr>';
		} else {
			while( $del = ria_mysql_fetch_assoc($delayed) ){
				if( trim($del['prd_date_livr']) == '' && trim($del['prd_stock_livr']) == '' ){
					continue;
				}
				$date_livr = '';
				// On recherche une date de livraison possible (dans le futur)
				if (isdateheure($del['prd_date_livr']) && strtotime($del['prd_date_livr']) > time()) {
					$date_livr = $del['prd_date_livr'];
				}elseif (isdateheure($del['prd_stock_livr']) && strtotime(dateparse($del['prd_stock_livr'])) > time()) {
					$date_livr = $del['prd_stock_livr'];
				}
				
				if( $del['ord_id']!=$last_ord ){
					$last_ord = $del['ord_id'];
					print '<tr class="head-second"><th colspan="4" class="align-left"><a href="../orders/order.php?ord='.$del['ord_id'].'">'.htmlspecialchars(ord_orders_name($del['ord_ref'],$del['ord_piece'],$del['ord_id'])).'</a>, le '.$del['ord_date'].'</th></tr>';
				}

				?>
				<tr>
					<td headers="prd-ref" data-label="<?php print _('Référence :'); ?> ">
						<a href="../catalog/product.php?prd=<?php print $del['prd_id']; ?>&cat=0"><?php print $del['prd_ref']; ?></a>
					</td>
					<td headers="prd-name" data-label="<?php print _('Désignation :'); ?> "><?php print htmlspecialchars($del['prd_name']); ?></td>
					<td headers="prd-qte" class="align-right" data-label="<?php print _('Qté :'); ?> "><?php print $del['prd_qte']; ?></td>
					<td headers="prd-avail" class="align-right" data-label="<?php print _('Date de disponibilité :'); ?> "><?php print (trim($date_livr) == '' ? 'Non-renseignée' : date('d/m/Y', strtotime($date_livr))); ?></td>
				</tr><?php
			}
		}
	?></tbody>
</table>
<?php
	}