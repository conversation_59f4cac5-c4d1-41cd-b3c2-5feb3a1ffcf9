<?php

/**	\file index.php
 * 
 *	Cette page liste les relevés de linéaires ayant été réalisés et permet les actions suivantes :
 *	- Ajout
 *	- Modification
 *	- Suppression
 * 	
 */

use Riashop\PriceWatching\models\LinearRaised\prw_offers;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_users;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_products;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_list_sections;

// Vérifie que l'utilisateur en cours peut accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_LINEAR_RAISED_CONFIG');

$error = null;
if (isset($_POST['add'])) {
	if (!isset($_POST['name']) || !trim($_POST['name'])) {
		$error = _("Vous devez saisir un libellé");
	}
	if (!isset($_POST['type']) || !is_numeric($_POST['type'])) {
		$error = _("Vous devez choisir un type d'assortiment");
	}

	if (isset($_POST['name'], $_POST['type'])) {
		$list['name'] = $_POST['name'];
		$list['type'] = $_POST['type'];
	}

	if (is_null($error)) {
		try{
			$id = prw_followed_lists::add($list['name'], $list['type']);
			if (!$id) {
				throw new Exception(_("Une erreur est survenue lors de la création de votre assortiment. Veuillez prendre contacte avec nous."));
			}else{
				$_SESSION['flash-success'] = str_replace("#param[nom_assortiment]#", "<strong>".htmlspecialchars($list['name'])."</strong>", _("L'assortiment #param[nom_assortiment]# a été créé avec succès. Vous pouvez commencer à paramétrer votre catalogue produits." ));
				header('Location: /admin/catalog/linear-raised/edit.php?id='.$id);
				exit;
			}
		}catch(Exception $e) {
			$error = $e->getMessage();
		}
	}

}

if (isset($_POST['del'], $_POST['list'])) {
	if (!empty($_POST['list'])) {
		foreach ($_POST['list'] as $id) {
			if(!prw_followed_lists::delete($id)) {
				$error = _('Une erreur est survenue lors de la suppression de l\'assortiment.');
			}else{
				prw_followed_products::delete($id);
				prw_followed_users::delete($id);
			}
		}
	}
}

if (isset($_POST['add-section'])) {
	if (!isset($_POST['name']) || !trim($_POST['name'])) {
		$error = _("Vous devez saisir un nom de section");
	}

	if (is_null($error)) {
		try{
			$id = prw_followed_list_sections::add($_POST['name']);
			if (!$id) {
				throw new Exception(_("Une erreur est survenue lors de la création de votre section. Veuillez prendre contacte avec nous."));
			}else{
				header('Location: /admin/catalog/linear-raised/section.php?id='.$id);
				exit;
			}
		}catch(Exception $e) {
			$error = $e->getMessage();
		}
	}
}

if (isset($_POST['del-section'], $_POST['list-section'])) {
	if (!empty($_POST['list-section'])) {
		foreach ($_POST['list-section'] as $id) {
			if(!prw_followed_list_sections::delete($id)) {
				$error = _('Une erreur est survenue lors de la suppression de l\'assortiment.');
			}
		}
	}
}
$types = array_flip(prw_followed_lists::$types);
$r_followed_lists = prw_followed_lists::get();
$r_list_sections = prw_followed_list_sections::get();
define('ADMIN_PAGE_TITLE', _('Gestion des assortiments'));
require_once('admin/skin/header.inc.php');
?>
<h2><?php echo _('Gestion des assortiments');?></h2>
<?php if (!is_null($error)) { ?>
	<div class="error"><?php echo $error?></div>
<?php } ?>
<?php if (isset($_SESSION['flash-success'])) { ?>
	<div class="success">
		<?php
			echo $_SESSION['flash-success'];
			unset($_SESSION['flash-success']);
		?>
	</div>
<?php } ?>
<div class="forms-wrapper">
	<form method="post">
		<table class="checklist" id="table-sections-assortiments">
			<caption>
				<?php echo _('Listes des sections d\'assortiments') ?>
			</caption>

			<thead>
				<tr>
					<th id="th-sections-1">
						<input type="checkbox" onclick="checkAllClick(this)">
					</th>
					<th id="th-sections-2">
						<?php echo _('Nom de la section') ?>
					</th>
					<th>
						<?php echo _('Assortiments') ?>
					</th>
				</tr>
			</thead>
			<tbody>
				<?php if( !$r_list_sections || !ria_mysql_num_rows($r_list_sections) ) {?>
					<tr>
						<td colspan="3"><?php echo _('Aucune section assortiment pour le moment')?></td>
					</tr>
				<?php }else{ 
					while( $list = ria_mysql_fetch_assoc($r_list_sections) ){ ?>
					<tr>
						<td>
							<input type="checkbox" name="list-section[]" value="<?php echo $list['id'] ?>" class="checkbox">
						</td>
						<td>
							<a href="/admin/catalog/linear-raised/section.php?id=<?php echo $list['id']?>">
								<?php echo htmlspecialchars($list['title']) ?>
							</a>
						</td>
						<td><?php echo prw_followed_list_sections::countLinkedFollowedLists($list['id']);?></td>
					</tr>
				<?php } 
			} ?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="3">
					
						<input type="submit" name="del-section" value="<?php echo _('Supprimer') ?>" class="js-input-disabled none float-left" />
					
						<div class="float-right">
							<label for="name">Ajouter une section :</label>
							<input type="text" name="name" id="name" maxlength="75">
							<input type="submit" name="add-section" value="<?php echo _('Ajouter') ?>" />
						</div>
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
	<form method="post">
		<table class="checklist" id="table-assortiments-produits">
			<caption>
				<?php echo _('Liste d\'assortiments produits') ?>
			</caption>
			<thead>
				<tr>
					<th id="th-assortiments-1"><input type="checkbox" onclick="checkAllClick(this)" class="checkbox" /></th>
					<th id="th-assortiments-2"><?php echo _('Nom de l\'assortiment'); ?></th>
					<th id="th-assortiments-3"><?php echo _('Type d\'assortiment'); ?></th>
					<th id="th-assortiments-4"><?php echo _('Activer'); ?></th>
					<th id="th-assortiments-5"><?php echo _('Date d\'ajout'); ?></th>
				</tr>
			</thead>
			<tbody>
				<?php if( !$r_followed_lists ) {?>
					<tr>
						<td colspan="5"><?php echo _('Aucun assortiment pour le moment'); ?></td>
					</tr>
				<?php }else{
					while( $list = ria_mysql_fetch_assoc($r_followed_lists) ){ ?>
					<tr>
						<td>
							<input type="checkbox" name="list[]" value="<?php echo $list['id'] ?>" class="checkbox" />
						</td>
						<td>
							<a href="/admin/catalog/linear-raised/edit.php?id=<?php echo $list['id']?>">
								<?php echo htmlspecialchars($list['name']) ?>
							</a>
						</td>
						<td>
							<?php echo ucfirst($types[$list['type']]) ?>
						</td>
						<td>
							<?php echo $list['is_published'] ? _('Oui') : _('Non'); ?>
						</td>
						<td>
							<?php echo ria_date_format($list['date_created']) ?>
						</td>
					</tr>
				<?php }
			} ?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="5">
						<input type="submit" name="del" value="<?php echo _('Supprimer'); ?>" class="js-input-disabled none float-left" />
						<div class="float-right">
							<label for="name"><?php print _('Ajouter un assortiment :'); ?></label>
							<input type="text" name="name" id="name" maxlength="75" />
							<select name="type" id="type">
								<?php foreach($types as $i => $type) {?>
									<option value="<?php echo $i?>">
										<?php echo ucfirst($type)?>
									</option>
								<?php } ?>
							</select>
							<input type="submit" name="add" value="<?php echo _('Ajouter') ?>" />
						</div>
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>
