<?php

class T_VoidResponse
{

    /**
     * @var XCHANGE_TR $T_VoidResult
     */
    protected $T_VoidResult = null;

    /**
     * @param XCHANGE_TR $T_VoidResult
     */
    public function __construct($T_VoidResult)
    {
      $this->T_VoidResult = $T_VoidResult;
    }

    /**
     * @return XCHANGE_TR
     */
    public function getT_VoidResult()
    {
      return $this->T_VoidResult;
    }

    /**
     * @param XCHANGE_TR $T_VoidResult
     * @return T_VoidResponse
     */
    public function setT_VoidResult($T_VoidResult)
    {
      $this->T_VoidResult = $T_VoidResult;
      return $this;
    }

}
