value = (one + two);
value = one + two;

value = (one - two);
value = (one - two);

value = (one * two);
value = (one * two);

value = (one / two);
value = (one / two);

value = (one % two);
value = (one % two);

value = (one + two + three);
value = one + two + three;
value = (one + (two + three));
value = one + (two + three);

value++;
value--;
value = -1;
value = - 1;

value = (1 + 2);
value = 1 + 2;

value = (1 - 2);
value = (1 - 2);

value = (1 * 2);
value = (1 * 2);

value = (1 / 2);
value = (1 / 2);

value = (1 % 2);
value = (1 % 2);

value = (1 + 2 + 3);
value = 1 + 2 + 3;
value = (1 + (2 + 3));
value = 1 + (2 + 3);

value = one + 2 + (3 - (four * five * (6 + 7))) + nine + 2;
value = myFunction(tokens[(stackPtr - 1)]);

for (i = 1 + 2; i < 4 + 5; i++) {
}

function myFunction()
{
    value = (one + 1) + (two + 2) + (myFunction() + 2);
    value = myFunction() + 2;
    value = (myFunction(mvar) + myFunction2(mvar));
    return -1;
}

params['mode'] = id.replace(/WidgetType/, '');

if (index < -1) index = 0;
if (index < - 1) index = 0;

var classN = prvId.replace(/\./g, '-');

three = myFunction(one / two);
three = myFunction((one / two) / four);
three = myFunction(one / (two / four));

four = -0.25;

id = id.replace(/row\/:/gi, '');
return /MSIE/.test(navigator.userAgent);

var re = new RegExp(/<\/?(\w+)((\s+\w+(\s*=\s*(?:".*?"|'.*?'|[^'">\s]+))?)+\s*|\s*)\/?>/gim);

var options = {
    minVal: -1,
    maxVal: -1
};

stepWidth = Math.round(this.width / 5);

date.setMonth(d[2] - 1);

switch (number % 10) {
    case -1:
        suffix = 'st';
    break;
}

var pathSplit = ipt.value.split(/\/|\\/);

if (pairs[i].search(/=/) !== -1) {
}

if (urlValue.search(/[a-zA-z]+:\/\//) !== 0) {
}

if (urlValue.search(/[a-zA-z]+:\/\/*/) !== 0) {
}

if (!value || /^\s*$/.test(value)) {
    return true;
}

(parseInt(dfx.attr(selectors[idx], 'elemOffsetTop'), 10) - scrollCoords.y) + 'px'

if (something === true
    ^ somethingElse === true
) {
    return false;
}

if (true === /^\d*\.?\d*$/.test(input)) return true;

if ( ! /^(?:a|select)$/i.test( element.tagName ) ) return true;
