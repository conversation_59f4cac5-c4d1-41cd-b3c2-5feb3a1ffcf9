--TEST--
Test symfony_debug_backtrace in case of non fatal error
--SKIPIF--
<?php if (!extension_loaded('symfony_debug')) {
    echo 'skip';
} ?>
--FILE--
<?php

function bar()
{
    bt();
}

function bt()
{
    print_r(symfony_debug_backtrace());
}

bar();

?>
--EXPECTF--
Array
(
    [0] => Array
        (
            [file] => %s
            [line] => %d
            [function] => bt
            [args] => Array
                (
                )

        )

    [1] => Array
        (
            [file] => %s
            [line] => %d
            [function] => bar
            [args] => Array
                (
                )

        )

)
