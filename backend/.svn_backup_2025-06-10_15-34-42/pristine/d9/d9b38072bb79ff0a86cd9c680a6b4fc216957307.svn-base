<?php

require_once('orders.inc.php');
require_once('Services/Customer/Order.class.php');
require_once('Services/Cart/Delivery.class.php');

/**	\brief Cette classe permet de charger les informations sur le panier en cours.
 *  La classe s'appuit sur la variable de session "ord_id" pour savoir de quel panier il s'agit.
 */
class CartService extends OrderService {
	private static $instance = null;

	protected $onlygiftcard = false; ///< Détermine si le panier contient uniquement des cartes cadeaux
	protected $onegiftcard = false; ///< Détermine si le panier contient au moins une carte cadeau

	protected $delivery 	= null; ///< Informations sur les dates de livraison estimées

	/** Cette fonction permet d'initialiser un objet panier.
	 * 	@return object L'instance nouvellement créée
	 */
	public static function getInstance(){
		if( is_null(self::$instance) || self::$instance->id == 0 ){
			self::$instance = new CartService();
		}

		return self::$instance;
	}

	/** Cette fonction permet de mettre à jour le panier.
	 * 	@return object L'objet courant
	 */
	public function reload(){
		PromotionsService::getInstance()->reload();

		// Mise à jour des promotions spéciales
		if( is_numeric($this->id) && $this->id > 0 ){
			ord_orders_promotions_verified($this->id);
		}

		// Mise à jour de l'objet panier
		self::$instance = new CartService();
		return self::$instance;
	}

	/** Cette fonction charge les informations de livraison d'une commande.
	 * 	@return object L'instance courant
	 */
	public function delivery(){
		if( $this->prds === null ){
			return $this;
		}
		$ar_prds = new Collection();
		$net_weight = 0;

		// Récupération des informations de frais de port
		$delivery = DeliveryService::getInstance()->getPort()->controlCart()->getDates()->getData();
		$this->delivery['services'] = $delivery;

		// On parcours tous les articles du panier
		foreach( $this->prds->getAll() as $one_prd ){
			$prd = $one_prd['prd']->getData();
			$line = $one_prd['line'];

			$one_prd['prd']->delivery();
			$ar_prds->addItem($one_prd);

			if( is_numeric($prd['weightnet']) && $prd['weightnet'] > 0 ){
				$net_weight += $prd['weightnet'] * ( isset($line['qty']) ? (int) $line['qty'] : 1 );
			}
		}

		if( $net_weight > 0 && is_array($delivery) && count($delivery) ){
			$ar_services = [];

			foreach($delivery as $key => $dlv){
				$is_numeric_wmin = is_numeric($dlv['weight_min']);
				$is_numeric_wmax = is_numeric($dlv['weight_max']);

				if( $is_numeric_wmin && $is_numeric_wmax && (float)$dlv['weight_min'] > (float)$dlv['weight_max'] ){
					continue;
				}

				if( $is_numeric_wmin ){
					if( (float)$dlv['weight_min'] > (float)$net_weight ){
						continue;
					}
				}

				if( $is_numeric_wmax ){
					if( (float)$dlv['weight_max'] < (float)$net_weight ){
						continue;
					}
				}
				$ar_services[$key] = $dlv;
			}
			$this->delivery['services'] = count($ar_services) ? $ar_services : null;
		}

		$this->prds = $ar_prds;

		return $this;
	}

	/** Cette fonction permet de détruire le singleton.
	 * 	@return emtpy
	 */
	public function destroy(){
		self::$instance = null;
	}

	/** Cette fonction permet de charger les informations principales du panier en cours.
	 * 	@return empty
	 */
	public function __construct(){
		global $config;

		if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] > 0 ){
			// Olivier : Ce code est désactivé car inutile à mon sens lors du chargement d'un panier
			// Si tout de fois il s'avère que ce n'est pas le cas, il pourra être réactivé
			// mais il faudra en parlé car si les totaux ou les promotions ne sont pas à jour
			// c'est qu'il manque un appel à ces fonctions après une action.
			// if( $refresh ){
				// Rafraichit le contenu du panier
				// ord_orders_refresh( $_SESSION['ord_id'], false, true, false );
				// ord_orders_promotions_verified( $_SESSION['ord_id'] );
			// }

			// On créé un objet panier avec le constructeur hérité en précisant le statut et l'identifiant en session
			parent::__construct( ['ord' => $_SESSION['ord_id'], 'status' =>[_STATE_BASKET, _STATE_BASKET_SAVE]] );
			$this->products();

			// Si les cartes cadeaux sont activées, on renseigne des informations supplémentaire
			if( isset($config['gifts_actived']) && $config['gifts_actived'] ){
				$this->onegiftcard = ord_orders_have_gift( $_SESSION['ord_id'] );
				$this->onlygiftcard = ord_orders_not_only_gifts( $_SESSION['ord_id'] ) === false;
			}
		}
	}

	/** Cette fonction permet de récupérer l'information pour savoir si le panier contient que des cartes cadeaux.
	 * 	@return bool true si c'est le cas, false dans le cas contraire
	 */
	public function getOnlyGiftCard(){
		return $this->onlygiftcard;
	}

	/** Cette fonction permet de récupérer l'information pour savoir si le panier contient au moins un article carte cadeau.
	 * 	@return bool true si c'est le cas, false dans le cas contraire
	 */
	public function getOneGiftCard(){
		return $this->onegiftcard;
	}
}