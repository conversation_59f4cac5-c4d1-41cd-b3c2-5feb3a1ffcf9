<?php

/**	Ce script est chargé de modifier les taux de TVA de nos boutiques non synchronisées
 *	Il se compose de trois étapes
 *	- Modification des prix, si les prix sont impactés par le changement de taux (cela dépend du client) : le prix TTC reste le même, le prix HT change.
 *	- Modification des taux
 *	- Rafraichissement des paniers
 *	Avant son exécution, il faut renseigner les variables "$rates" et "$tenants"
 */

set_include_path(dirname(__FILE__) . '/../include/');

require_once( 'db.inc.php' );
require_once( 'cfg.variables.inc.php' );
require_once( 'orders.inc.php' );

$print_log = isset($argv[1]) && $argv[1];

// liste des taux modifiés
$rates = array(
	array(1.196, 1.2),
	array(1.07, 1.1)
);

// locataires à modifier + indicateur si le prix de vente absorbe la différence ou non + website pour le chargement de la configuration
$tenants = array(
	array(23, false, 24),
	array(17, true, 31),
	array(20, false, 36)
);

foreach( $tenants as $tenant ){
	
	foreach( $rates as $rate ){
		
		if( $tenant[1] ){
			
			$sql = '
				update prc_prices
				set prc_value = ( ( prc_value * '.$rate[0].' ) / '.$rate[1].' )
				where prc_is_deleted = 0
				and prc_tnt_id = '.$tenant[0].'
				and prc_date_end >= NOW()
				and prc_type_id = 1
				and prc_is_sync = 0
				and prc_prd_id in (
					select ptv_prd_id from prc_tvas
					where ptv_tnt_id = '.$tenant[0].' and ptv_date_deleted is null
					and ptv_is_sync = 0 and ptv_tva_rate = '.$rate[0].'
				)
			';
			
			ria_mysql_query($sql);
			
			if( ria_mysql_errno() ){
				if( $print_log ){
					print "Erreur SQL : ".mysql_error()." - ".$sql."\n";
				}else{
					error_log( __FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql );
				}
				exit;
			}
			
		}
		
		$sql = '
			update prc_tvas
			set ptv_tva_rate = '.$rate[1].'
			where ptv_tnt_id = '.$tenant[0].' and ptv_date_deleted is null
			and ptv_is_sync = 0 and ptv_tva_rate = '.$rate[0].'
		';
		
		ria_mysql_query($sql);
		
		if( ria_mysql_errno() ){
			if( $print_log ){
				print "Erreur SQL : ".mysql_error()." - ".$sql."\n";
			}else{
				error_log( __FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql );
			}
			exit;
		}
		
	}
	
	// commande à l'état de panier, de moins de 3 mois ou client connu
	$sql = '
		select ord_id as id from ord_orders
		where ord_tnt_id = '.$tenant[0].' and ord_state_id in ('.implode(', ', ord_states_get_uncompleted()).') and (
			ord_usr_id is not null or DATEDIFF(now(), ord_date) < 92
		)
	';
	
	$r = ria_mysql_query($sql);
	
	if( $r ){
		
		unset($config);
		$config['tnt_id'] = $tenant[0];
		$config['wst_id'] = $tenant[2];
		
		cfg_variables_load($config);
		
		while( $ord = ria_mysql_fetch_array($r) ){
			ord_orders_refresh($ord['id']);
		}
		
	}else{
		
		if( ria_mysql_errno() ){
			if( $print_log ){
				print "Erreur SQL : ".mysql_error()." - ".$sql."\n";
			}else{
				error_log( __FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql );
			}
			exit;
		}
		
	}
}

if( $print_log ){
	print "Fin de procédure\n";
}

