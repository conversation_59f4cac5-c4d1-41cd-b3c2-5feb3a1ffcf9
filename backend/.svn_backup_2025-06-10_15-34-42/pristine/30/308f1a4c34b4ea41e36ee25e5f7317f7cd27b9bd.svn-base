<?php

	/**	\file json-stores.php
	 * 
	 *	Ce fichier retourne une liste de magasins, éventuellement filtrée en fonction des paramètres fournis. Les paramètres acceptés sont
	 *	les suivants :
	 *	- mdl : identifiant d'un modèle de saisie
	 *	- fld : identifiant d'un champ avancé
	 *	- c : identifiant d'un pays
	 *	- pub : statut de publication (true ou false)
	 *	- term : terme de recherche
	 *	- seg : identifiant d'un segment
	 *	- reg : identifiant d'une zone géographique
	 *	- sales_types : tableau d'identifiants de types de vente (dlv_sales_types), mettre 'none' pour avoir les magasins liés à aucun type de ventes
	 *
	 *	L'ordre dans lequel les résultats seront retournés est modifiable grâce aux arguments suivants :
	 *	- sort : colonne sur laquelle le résultat sera trié
	 *	- dir : direction dans laquelle la colonne sera triée (asc ou desc)
	 *
	 * 	Il est utiliser pour gérer la pagination du tableau des magasin.
	 */

	// Vérifie que l'utilisateur en cours à le droit de charger les magasins
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE');

	require_once('delivery.inc.php');
	require_once('fields.inc.php');
	
	// détermine si le chargement "dlv_stores_get" a été effectué
	$store_get = false;
	
	// Récupère les magasins contenus dans la base de données
	$have_fld_filter = $have_mdl_filter = false;
	if( isset($_POST['mdl']) && is_numeric($_POST['mdl']) && $_POST['mdl']>0 ){
		if( $rm = fld_models_get($_POST['mdl']) ){
			$m = ria_mysql_fetch_array($rm);
			if( $m['cls_id']==CLS_STORE ){
				$have_mdl_filter = true;
				$rstr = fld_models_get_objects($_POST['mdl']);
			}
		}
	}

	if( !$have_mdl_filter ){
		if( isset($_POST['fld']) && is_numeric($_POST['fld']) && $_POST['fld']>0 ){
			if( $rf = fld_fields_get($_POST['fld']) ){
				$f = ria_mysql_fetch_array($rf);
				if( $f['cls_id']==CLS_STORE ){
					$have_fld_filter = true;
					$rstr = fld_fields_get_objects($_POST['fld']);
				}
			}
		}
	}
	
	$c = isset($_REQUEST['c']) && sys_countries_exists_code($_REQUEST['c']) ? sys_countries_get_name($_REQUEST['c']) : '';
	$pub = null;
	if( isset($_POST['pub']) ){
		if( strtolower(trim($_POST['pub']))=='true' ) $pub = true;
		elseif( strtolower(trim($_POST['pub']))=='false' ) $pub = false;
	}
	$search = isset($_GET['term']) ? $_GET['term'] : false;
	$seg = isset($_REQUEST['seg']) && is_numeric($_REQUEST['seg']) && $_REQUEST['seg'] > 0 && seg_segments_exists( $_REQUEST['seg'], CLS_STORE ) ? $_REQUEST['seg'] : 0;
	$reg = isset($_REQUEST['reg']) && is_numeric($_REQUEST['reg']) ? $_REQUEST['reg'] : false;
	$sort = isset($_REQUEST['sort']) ? $_REQUEST['sort'] : 'name';
	$dir = isset($_REQUEST['dir']) ? $_REQUEST['dir'] : 'asc';

	$sales_types = 0;
	if( isset($_REQUEST['sales_types']) ){
		if( is_numeric($_REQUEST['sales_types']) && $_REQUEST['sales_types'] ){
			$sales_types = $_REQUEST['sales_types'];
		}elseif( $_REQUEST['sales_types'] === 'none' ){
			$sales_types = 'none';
		}
	}
	
	if( $sort === 'address' ){
		$sort = 'zipcode';
	}

	if( !$have_mdl_filter && !$have_fld_filter ){
		$s = is_numeric( $sales_types ) && $sales_types > 0 ? array($sales_types) : ( $sales_types === 'none' ? 'none' : array() );
		$rstr = dlv_stores_get( 0, null, array($sort => $dir), 0, 0, false, 0, $c, 0, 0, false, false, false, $s, $search, true, $pub, '', $seg, '', array(), $reg ? array(_ZONE_RGN_FRANCE => $reg) : false );
		//$rstr = dlv_stores_get( 0, null, array($_REQUEST['sort'] => $_REQUEST['dir']), 0, 0,  false, 0, $c, 0, 0, false, false, false, array(), $search, true, $pub );
		$store_get = true;
	}
	
	  
	// Défini la page
	$page = isset($_POST['p']) && $_POST['p']>0 ? $_POST['p'] : 1;
	
	// Détermine le nombre limite de magasin retourné
	$limit = isset($_REQUEST['limit']) && is_numeric($_REQUEST['limit']) && $_REQUEST['limit']>0 ? $_REQUEST['limit'] : 18446744073709551615;
	
	// Tableau contenant tous les magasins
	$stores = array();
	
	if( $rstr && ria_mysql_num_rows($rstr) ){
		
		// On se place sur la position de départ de lecture des résultats
		if( $page>0 )
			ria_mysql_data_seek( $rstr, ($page-1)*$limit );
		
		$count = 0;
		while( $store = ria_mysql_fetch_array($rstr) ){
			
			// charge réellement le magasin si ce n'est pas le cas jusqu'ici
			$str = $store;
			if( !$store_get ){
				$str = ria_mysql_fetch_array(dlv_stores_get($store['obj_id'],null,false,0,0,false,0,'',0,0,false,false,false,array(),false,false,null));
			}
			
			// Intégration d'un tableau contenant les informations sur un magasin
			$stores[] = array(
				'id' 				=> $str['id'],
				'name' 				=> $str['name'],
				'value' 			=> $str['id'],
				'label' 			=> $str['name'],
				'desc' 				=> $str['desc'],
				'address1' 			=> $str['address1'], 
				'address2' 			=> $str['address2'],
				'zipcode' 			=> $str['zipcode'],
				'city' 				=> $str['city'],
				'country' 			=> $str['country'],
				'manager' 			=> $str['manager'],
				'phone' 			=> $str['phone'],
				'fax' 				=> $str['fax'],
				'email' 			=> $str['email'],
				'website' 			=> $str['website'],
				'allow_delivery' 	=> $str['allow_delivery'],
				'cnt_id' 			=> $str['cnt_id'],
				'longitude' 		=> $str['longitude'],
				'latitude' 			=> $str['latitude'],
				'sct_id' 			=> $str['sct_id'],
				'url_alias' 		=> $str['url_alias'],
				'is_sync'			=> $str['is_sync']
			);
			
			// Comptabilise le nombre de magasin ajouté dans la liste
			$count++;
			
			// Si le nombre de magasin retourné est fixé et atteint, on arrête la lecture des résultats MySQL
			if( $count>=$limit )
				break;
		}
		
	}
	
	// Encode le tableau sous format JSON
	print json_encode( $stores );

