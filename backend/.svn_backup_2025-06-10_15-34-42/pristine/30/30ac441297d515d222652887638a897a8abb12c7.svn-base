<?php

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_NEGOTIATION_ADD');

if (isset($_POST["add"])) {
	header('Location: /admin/index.php');
	exit;
}

require_once('devices.inc.php');
require_once('./functions.php');
require_once('prd/restrictions.admin.inc.php');

if( IS_AJAX ){

	$fld_id 		= isset($_GET["fld_id"]) ? $_GET["fld_id"] : null;
	$usr_id 		= isset($_GET["usr_id"]) ? $_GET["usr_id"] : null ;
	$gtr_id 		= isset($_GET["gtr_id"]) ? $_GET["gtr_id"] : null ;
	$price_fld_id 	= isset($_GET["price_fld_id"]) ? $_GET["price_fld_id"] : null ;
	$price_value 	= isset($_GET["price_value"]) ? $_GET["price_value"] : null ;
	$price_type 	= isset($_GET["price_type"]) ? $_GET["price_type"] : null ;

	if ( isset( $fld_id, $usr_id, $gtr_id) ) {
		print gu_trading_rules_form_view( $fld_id, $usr_id, $gtr_id, $price_fld_id, $price_value, $price_type );
		exit;
	}
}

Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
	->push( $is_yuto_essentiel ? _('Comptes') : _('Comptes clients'), '/admin/customers/index.php' )
	->push( _('Gestion des négociations'), '/admin/customers/negotiations/index.php' )
	->push( _('Nouvelle règle de négociation') );


define('ADMIN_PAGE_TITLE', _('Nouvelle règle de négociation') . ' - ' . _('Comptes clients'));
require_once('admin/skin/header.inc.php');

?>
<h2><?php print _('Nouvelle règle de négociation')?></h2>
<form action="edit.php?action=add_gtr" method="POST">
	<table class="nocaption large">
		<tbody>
			<tr class="valign-center">
				<td id="td-contexte"><?php print _('Contexte :'); ?></td>
				<td>
					<select name="usr_fld_id">
						<option id="rec-all" value="-1"><?php print _('Tous les utilisateurs')?></option>
						<option id="rec-usr" value="<?php print _FLD_USR_ID; ?>" <?php print isset($_GET['usr_id']) && $_GET['usr_id'] > 0 ? 'selected="selected"':'' ?>><?php print _('Utilisateur')?></option>
					</select>
					<span class="rec-lbl"><?php print _('égal à')?></span>
					<!-- client spécifique (moteur de recherche avec autocomplétion sur un champ texte) (si _FLD_USR_ID) -->
					<?php
					$email = '';
					$usr_id = '';
					if ( isset($_GET['usr_id']) && gu_users_exists($_GET['usr_id']) ) {
						$email = gu_users_get_email($_GET['usr_id']);
						$usr_id = $_GET['usr_id'];
					}
					 ?>
					<input class="rec-value value-rec-usr" type="text" name="usr_value_txt" value="<?php print $email; ?>"/>
					<input class="rec-action action-rec-usr" type="button" name="choose-rec-usr" id="choose-rec-usr" value="<?php print _('Choisir')?>" />
					<input class="val-autocomplete-usr" type="hidden" name="usr_id" value="<?php print $usr_id; ?>"/>
				</td>
			</tr>
		</tbody>
	</table>
	<table class="authorizations large">
		<thead>
			<tr class="thead-none">
				<th><?php print _('Contexte')?></th>
				<th colspan="2"><?php print _('Règle')?></th>
			</tr>
		</thead>
		<tbody>
			<?php print gu_trading_rules_form_view(); ?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="3"><input type="submit" name="action_gtr" value="<?php print _('Enregistrer')?>"></td>
			</tr>
		</tfoot>
	</table>
</form>


<?php
	require_once('admin/skin/footer.inc.php');
?>