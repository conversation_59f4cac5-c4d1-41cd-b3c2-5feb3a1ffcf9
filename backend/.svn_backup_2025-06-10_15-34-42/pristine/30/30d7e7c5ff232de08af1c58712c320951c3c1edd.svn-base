<?php
/** \defgroup model_prd_relations Relations entre les produits
 *	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion des relations entre les produits
 *	@{
 */

require_once('products.inc.php');
require_once('prd.stocks.inc.php');

// \cond onlyria
/**	Cette fonction permet l'ajout d'un type de relation entre deux produits
 *	@param string $name Obligatoire, désignation du type de relation, au singulier
 *	@param $plural Obligatoire, désignation du type de relation, au pluriel
 *	@return int l'identifiant attribué au type de relation en cas de succès, false en cas d'échec
 */
function prd_relations_types_add( $name, $plural ){

	$name = ucfirst(trim($name));
	if( !$name ){
		return false;
	}

	$plural = ucfirst(trim($plural));
	if( !$plural ){
		return false;
	}

	global $config;

	$sql = '
		insert into prd_relations_types
			(type_tnt_id, type_name, type_name_plural)
		values
			('.$config['tnt_id'].', "'.addslashes($name).'", "'.addslashes($plural).'")
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour d'un type de relation entre deux produits
 *	@param int $id Obligatoire, identifiant du type de relation
 *	@param string $name Obligatoire, désignation du type de relation, au singulier
 *	@param $plural Obligatoire, désignation du type de relation, au pluriel
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_relations_types_update( $id, $name, $plural ){

	if( !prd_relations_types_exists( $id ) ){
		return false;
	}

	$name = ucfirst(trim($name));
	if( !$name ){
		return false;
	}

	$plural = ucfirst(trim($plural));
	if( !$plural ){
		return false;
	}

	global $config;

	$sql = '
		update prd_relations_types
		set type_name = "'.addslashes($name).'", type_name_plural = "'.addslashes($plural).'"
		where type_tnt_id = '.$config['tnt_id'].' and type_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un type de relation.
 *	@param int $id Identifiant du type de relation.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_relations_types_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update prd_relations_types
		set type_date_modified = now()
		where type_tnt_id = '.$config['tnt_id'].' and type_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction supprime un type de relation.
 *	@param int $id Obligatoire, identifiant du type à supprimer.
 *	@param $del_rel Optionnel, détermine si les relations existantes sont supprimées (sinon, la suppression est refusée si des relations existent).
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_relations_types_del( $id, $del_rel=false ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}
	if( !prd_relations_types_exists( $id ) ){
		return true;
	}

	// charge les relations existantes
	$rrel = prd_relations_get( null, null, $id );
	if( !$rrel ){
		return false;
	}
	if( ria_mysql_num_rows($rrel) ){
		if( $del_rel ){
			// supprime chaque relation
			while( $rel = ria_mysql_fetch_assoc($rrel) ){
				prd_relations_del( $rel['src_id'], $rel['dst_id'], $rel['type_id'] );
			}
		}else{
			return false;
		}
	}

	global $config;

	$sql = '
		update prd_relations_types
		set type_is_deleted = 1
		where type_tnt_id = '.$config['tnt_id'].' and type_id = '.$id.'
	';
	//print_r($sql);
	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification d'un identifiant de type de relation
 *	@param int $id Obligatoire, identifiant du type de relation
 *	@return bool true si l'identifiant est valide, false dans le cas contraire
 */
function prd_relations_types_exists( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select type_id from prd_relations_types
		where type_tnt_id = '.$config['tnt_id'].' and type_id = '.$id.'
		and type_is_deleted = 0
	';

	$r_exists = ria_mysql_query($sql);
	if( !$r_exists ){
		return false;
	}

	return ria_mysql_num_rows($r_exists);

}
// \endcond

/**	Cette fonction permet le chargement d'un ou plusieurs types de relations.
 *	Les relations sont triées par leur nom.
 *	@param int $id Facultatif, identifiant d'un type de relation sur lequel filtrer le résultat (ou tableau)
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du type de relation
 *			- name : désignation du type de relation
 *			- name_plural : désignation du type de relation, au pluriel
 *			- date_modified : date de dernière modification
 */
function prd_relations_types_get( $id=null ){

	if( $id === null ){
		$id = 0;
	}

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		select type_id as id, type_name as name, type_name_plural as name_plural, type_date_modified as date_modified
		from prd_relations_types
		where type_tnt_id = '.$config['tnt_id'].'
			and type_is_deleted = 0
	';

	if( sizeof($id) ){
		$sql .= ' and type_id in ('.implode(', ', $id).')';
	}

	$sql .= '
		order by type_name
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction permet le chargement d'un ou plusieurs types de relations.
 *	Les relations sont triées par leur nom.
 *	@param string $name Obligatoire, désignation du type de relation, au singulier
 *	@param $plural Obligatoire, désignation du type de relation, au pluriel
 *	@return int l'identifiant du type de la relation en cas de succès, false en cas d'échec, 0 si le type n'existe pas
 */
function prd_relations_types_get_by_name( $name='', $plural='' ){
	// Contrôle des paramètres
	if( trim($name) == '' && trim($plural) == '' ){
		return false;
	}

	global $config;

	$sql = '
		select type_id as id
		from prd_relations_types
		where type_tnt_id = '.$config['tnt_id'].'
			and type_is_deleted = 0
	';

	// Ajout des filtres en fonction des paramètres fournis
	if( trim($name) != '' ){
		$sql .= ' and type_name = "'.trim($name).'"';
	}

	if( trim($plural) != '' ){
		$sql .= ' and type_name_plural = "'.trim($plural).'"';
	}

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	if( !ria_mysql_num_rows($res) ){
		return 0;
	}

	return ria_mysql_result($res, 0, 0);
}

// \cond onlyria
/**	Cette fonction permet l'ajout d'une relation entre deux produits
 *	@param int $src Obligatoire, identifiant du produit source
 *	@param int $dst Obligatoire, identifiant du produit destination
 *	@param int $type Obligatoire, identifiant du type de relation
 *	@return bool true en cas de succès, false en cas d'échec
 *	@todo Il manque un contrôle sur le type de relation
 */
function prd_relations_add( $src, $dst, $type ){
	global $config;

	if( !is_numeric($src) || $src<=0 ) return false;
	if( !is_numeric($dst) || $dst<=0 ) return false;
	if( !is_numeric($type) || $type<=0 ) return false;
	if( $src==$dst ) return false;

	$pos = 0;

	$rpos = ria_mysql_query('
		select ifnull(max(rel_pos) + 1, 0) as max_pos
		from prd_relations
		where rel_tnt_id='.$config['tnt_id'].' and rel_src_id='.$src.' and rel_type_id='.$type.' and rel_is_deleted=0
	');

	if( $rpos && ria_mysql_num_rows($rpos) ){
		$pos = ria_mysql_result($rpos, 0, 'max_pos');
	}

	return ria_mysql_query('
		replace into prd_relations
			(rel_tnt_id,rel_src_id,rel_dst_id,rel_type_id,rel_pos,rel_is_deleted)
		values
			('.$config['tnt_id'].','.$src.','.$dst.','.$type.','.$pos.',0)
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'une relation entre deux produits
 *	@param int $src Obligatoire, identifiant du produit source
 *	@param int $dst Optionnel, identifiant du produit destination
 *	@param int $type Optionnel, identifiant du type de relation
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_relations_del( $src, $dst=0, $type=0 ){
	global $config;

	if( !is_numeric($src) || $src <= 0 ) return false;
	if( !is_numeric($dst) || $dst < 0 ) return false;
	if( !is_numeric($type) || $type < 0 ) return false;

	$sql = '
		update prd_relations set rel_is_deleted=1
		where rel_tnt_id='.$config['tnt_id'].' and rel_src_id='.$src.' and rel_is_deleted=0
	';
	if( $dst > 0 ){
		$sql .= ' and rel_dst_id='.$dst;
	}

	if( $type > 0 ){
		$sql .= ' and rel_type_id='.$type;
	}

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	// Pour chaque type de relation en jeu, on repositionne les produits liés entre eux (pour éviter les trous)
	if( $type > 0 && $dst > 0 ){
		$types = array($type);
	}else{
		$types = array();

		if( $dst > 0 ){
			$rtypes = ria_mysql_query('
				select rel_type_id from prd_relations
				where rel_tnt_id='.$config['tnt_id'].' and rel_src_id='.$src.' and rel_dst_id='.$dst.' and rel_is_deleted=0
			');

			if( $rtypes && ria_mysql_num_rows($rtypes) ){
				while( $t = ria_mysql_fetch_assoc($rtypes) ){
					$types[] = $t['rel_type_id'];
				}
			}
		}
	}
	foreach( $types as $t ){
		$rchildren = ria_mysql_query('
			select rel_dst_id from prd_relations
			where rel_tnt_id='.$config['tnt_id'].' and rel_src_id='.$src.' and rel_type_id='.$t.' and rel_is_deleted=0
			order by rel_pos
		');

		if( $rchildren && ria_mysql_num_rows($rchildren) ){
			$pos = 0;
			while( $c = ria_mysql_fetch_assoc($rchildren) ){

				ria_mysql_query('
					update prd_relations
					set rel_pos='.$pos.'
					where rel_tnt_id='.$config['tnt_id'].'
						and rel_src_id='.$src.'
						and rel_type_id='.$t.'
						and rel_dst_id='.$c['rel_dst_id'].'
						and rel_is_deleted=0
				');

				++$pos;
			}
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la recopie des articles liés sur les articles enfants
 *	@param int $prd Obligatoire, produit parent à utiliser comme modèle
 *	@param int $type Facultatif, type de relation à recopier
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_relations_copy_to_childs( $prd, $type=null ){

	if( !prd_products_exists( $prd ) ){
		return false;
	}

	// Si aucun produit enfant, inutile de continuer
	$rchilds = prd_products_get_simple( 0, '', false, 0, false, false, false, false, array('childs' => true, 'parent' => $prd) );
	if( !$rchilds || !ria_mysql_num_rows($rchilds) ){
		return false;
	}

	$error = false;
	if( $relations = prd_relations_get( $prd, null, $type ) ){
		while( $rel = ria_mysql_fetch_assoc($relations) ){
			while( $child = ria_mysql_fetch_assoc($rchilds) ){
				if( !prd_relations_add( $child['id'], $rel['dst_id'], $rel['type_id'] ) ){
					$error = true;
				}
			}
			ria_mysql_data_seek($rchilds, 0);
		}
	}

	return !$error;

}
// \endcond

/**	Cette fonction permet le chargement d'une ou plusieurs relations entre les produits
 *	@param $src Facultatif, identifiant d'un produit source sur lequel filtrer le résultat (ou tableau d'identifiants)
 *	@param $dst Facultatif, identifiant d'un produit destination sur lequel filtrer le résultat (ou tableau d'identifiants)
 *	@param $type Facultatif, identifiant ou tableau d'identifiants de types de relation sur lequel filtrer le résultat (ou tableau d'identifiants)
 *	@param bool $published  Facultatif, limiter le résultat aux produits de destination publiés ? (ignoré si $correled = true)
 *	@param int $limit Facultatif, nombre de lignes auxquels limiter le résultat
 *	@param bool $correled Facultatif, détermine si les relations à récupérer sont des triplets des éléments de même rang $src / $dst / $type (qui doivent être de même taille)
 *	@param $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par référence. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : pos.
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- src_id : identifiant du produit source
 *			- src_ref : référence du produit source
 *			- src_name : désignation du produit source
 *			- src_title : titre du produit source
 *			- src_desc : description courte du produit source
 *			- src_is_sync : produit source synchronisé ou non
 *			- dst_id : identifiant du produit destination
 *			- dst_ref : référence du produit destination
 *			- dst_name : désignation du produit destination
 *			- dst_title : titre du produit destination
 *			- dst_desc : description courte du produit destination
 *			- dst_is_sync : produit destination synchronisé ou non
 *			- dst_publish : produit destination publié ou non
 *			- dst_publish_cat : produit destination publié ou non
 *			- type_id : identifiant du type de relation
 *			- type_name : désignation du type de relation
 *			- type_name_plural : désignation du type de relation, au pluriel
 *			- pos : position du produit destination dans la liste des produits destinations
 *	@return bool False en cas d'échec.
 */
function prd_relations_get( $src=null, $dst=null, $type=null, $published=false, $limit=0, $correled=false, $sort=false ){

	$src = $src === null ? 0 : $src;
	$src = control_array_integer( $src, false );
	if( $src === false ){
		return false;
	}

	$dst = $dst === null ? 0 : $dst;
	$dst = control_array_integer( $dst, false );
	if( $dst === false ){
		return false;
	}

	$type = $type === null ? 0 : $type;
	$type = control_array_integer( $type, false );
	if( $type === false ){
		return false;
	}

	if( $correled && ( sizeof($src) != sizeof($dst) || sizeof($src) != sizeof($type) ) ){
		return false;
	}

	global $config;

	$dps = prd_deposits_get_main();
	if( $config['prd_deposits'] == 'use-customer' && isset($_SESSION['usr_dps_id']) ){
		$dps = $_SESSION['usr_dps_id'];
	}

	if( !$dps ) $dps = 0;

	$sql = '
		select
			src.prd_id as src_id, src.prd_ref as src_ref, src.prd_name as src_name, if(src.prd_title!="",src.prd_title,src.prd_name) as src_title, src.prd_img_id as src_img_id, src.prd_desc as src_desc, src.prd_desc_long as src_desc_long, src.prd_orderable as src_orderable, src.prd_is_sync as src_is_sync,
			dst.prd_id as dst_id, dst.prd_ref as dst_ref, dst.prd_name as dst_name, if(dst.prd_title!="",dst.prd_title,dst.prd_name) as dst_title, dst.prd_img_id as dst_img_id, dst.prd_desc as dst_desc, dst.prd_desc_long as dst_desc_long, dst.prd_orderable as dst_orderable, dst.prd_is_sync as dst_is_sync, dst.prd_publish as dst_publish, dst.prd_publish_cat as dst_publish_cat,
			type_id, type_name, type_name_plural, rel_pos as pos
		from
			prd_relations
			join prd_relations_types on rel_tnt_id = type_tnt_id and rel_type_id = type_id
			join prd_products as src on rel_tnt_id = src.prd_tnt_id and rel_src_id = src.prd_id
			join prd_products as dst on rel_tnt_id = dst.prd_tnt_id and rel_dst_id = dst.prd_id
			left join prd_stocks on dst.prd_tnt_id = sto_tnt_id and dst.prd_id = sto_prd_id and sto_dps_id = '.$dps.' and sto_is_deleted=0
		where
			rel_tnt_id = '.$config['tnt_id'].'
			and dst.prd_date_deleted is null
			and rel_is_deleted=0
	';

	if( !$correled && $published ){
		if( ( isset($config['catalog_publish_lng']) && !$config['catalog_publish_lng'] ) || i18n::getLang() == $config['i18n_lng'] ){
			$sql .= ' and dst.prd_publish = 1 and dst.prd_publish_cat = 1 ';
		}else{
			$sql .= ' and exists (
				select 1 from fld_object_values
				where pv_tnt_id = '.$config['tnt_id'].' and pv_fld_id = '._FLD_PRD_PUBLISH.' and pv_obj_id_0 = dst.prd_id
				and pv_value in ("Oui", "oui", "1") and pv_lng_code="'.addslashes(i18n::getLang()).'"
			)';
		}
		$sql .= ' and ( dst.prd_sleep = 0 or ( ' . prd_stocks_get_sql() .' - ifnull(sto_prepa, 0) ) > 0 ) ';
	}

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($src); $i++ ){
			$cnds[] = 'rel_src_id = '.$src[ $i ].' and rel_dst_id = '.$dst[ $i ].' and rel_type_id = '.$type[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($src) ){
			$sql .= ' and rel_src_id in ('.implode(', ', $src).')';
		}
		if( sizeof($dst) ){
			$sql .= ' and rel_dst_id in ('.implode(', ', $dst).')';
			if( $published ){
				$sql .= '
					and src.prd_publish = 1 and src.prd_publish_cat = 1 and ( src.prd_sleep = 0 or ifnull((
						select ' . prd_stocks_get_sql('s2') . ' - s2.sto_prepa from prd_stocks as s2
						where s2.sto_tnt_id = src.prd_tnt_id and s2.sto_prd_id = src.prd_id and s2.sto_dps_id = '.$dps.' and s2.sto_is_deleted=0
					), 0) > 0 )
				';
			}
		}
		if( sizeof($type) ){
			$sql .= ' and rel_type_id in ('.implode(', ', $type).')';
		}
	}

	if( $sort==false || !is_array($sort) || !sizeof($sort) ){
		$sort = array();
	}

	$sort_final = array();

	foreach( $sort as $col=>$dir ){
		$col = strtolower(trim($col));
		$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';

		switch( $col ){
			case 'pos' :
				$sort_final[] = 'rel_pos '.$dir;
				break;
		}
	}

	if( !sizeof($sort_final) ){
		if( is_array($type) && sizeof($type) == 1 ){
			if( prd_relations_order_get( $type[0] ) ){
				$sort_final[] = 'rel_pos';
			}
		}
		$sort_final[] = 'src.prd_ref';
		$sort_final[] = 'dst.prd_ref';
	}

	$sql .= '
		order by '.implode(', ', $sort_final).'
	';

	if( is_numeric($limit) && $limit > 0 ){
		$sql .= ' limit '.$limit;
	}

	return ria_mysql_query($sql);

}

// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier les articles liés.
 *	@param $rel_type Obligatoire, identifiant du type de relation à retourner
 *	@return bool False si la méthode de tri est alphabétique, True si la méthode de tri est personnalisée
 */
function prd_relations_order_get( $rel_type ){
	global $config;

	$rorderby = cfg_overrides_get(0, array(), 'orderby_relations');
	if( $rorderby && ria_mysql_num_rows($rorderby) ){
		$orderby = ria_mysql_fetch_assoc($rorderby);
		$orderby = $orderby['value'];

		if( strpos($orderby, 'rel='.$rel_type) !== FALSE ){
			return true;
		}
	}

	return false;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de la méthode de tri utilisée pour les articles liés
 *	@param int $prd_id Obligatoire, identifiant du produit source
 *	@param $rel_type Obligatoire, identifiant du type de relation
 *	@param $order Optionnel, mode de tri - false pour un tri alphabétique, true pour un tri numérique défini par l'utilisateur
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_relations_order_update( $prd_id, $rel_type, $order=false ){

	if( !is_numeric($prd_id) || $prd_id <= 0 ) {
		return false;
	}

	global $config;

	// La variable existe-t-elle déjà rel=id
	// Si oui
	$rorderby = cfg_overrides_get(0, array(), 'orderby_relations');
	$orderby = array();
	if( $rorderby && ria_mysql_num_rows($rorderby) ){
		$orderby = ria_mysql_fetch_assoc($rorderby);

		if( $orderby['value'] != '' ){
			$orderby = explode(',', $orderby['value']);
		}else{
			$orderby = array();
		}
	}

	if( $order ){
		//$products = prd_relations_get( null, null, $rel_type, $ord_id, array('pos' => 'asc') );
		//$relations = prd_relations_get($prd_id,null,$rel_type);
		if (!in_array('rel='.$rel_type, $orderby)){
			$orderby[] = 'rel='.$rel_type;
		}
	}else{
		if( ($key = array_search('rel='.$rel_type, $orderby)) !== FALSE ) {
			unset($orderby[$key]);
		}
	}

	$res = cfg_overrides_set_value( 'orderby_relations', implode(',', $orderby) );

	return $res;
}
// \endcond

/// @}

/** Calcule le nombre de produits liés à un type de relation (Accessoires, Pièces de rechange, etc...).
 * 	@param int $type_id Obligatoire, identifiant du type de relation voulu
 * 	@return int le nombre de relations d'un type donné
 */
function prd_relation_type_count($type_id){
	if(!is_numeric($type_id) || $type_id <= 0){
		return false;
	}

	global $config;

	$query = '
		select count(*) nb_relation
		from prd_relations
		where rel_tnt_id = ' . $config['tnt_id'] . '
			and rel_type_id = ' . $type_id .'
	';

	$res = ria_mysql_query($query);

	$nb_relation = 0;
	if ($res && ria_mysql_num_rows($res)) {
		$r = ria_mysql_fetch_assoc($res);
		$nb_relation = $r['nb_relation'];
	}

	return $nb_relation;
}

