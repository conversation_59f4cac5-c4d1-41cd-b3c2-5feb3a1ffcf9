<?php

require_once('Services/Service.class.php');
require_once('advertising.inc.php');

/**	\brief Cette classe permet de charger les données d'une page de contenu
 */
class BannerService extends Service{
	protected $title = ''; ///< Titre de la bannière
	protected $url = ''; ///< URL accessible via la bannière
	protected $img = ''; ///< URL de l'image de la bannière
	protected $desc = ''; ///< Description de la bannière
	protected $btnlabel = ''; ///< Label du bouton (seulement si une URL est donnée)
	protected $pos = 0; ///< Ordre d'affichage
	protected $plcid = 0; ///< Identifiant de la zone d'affichage
	protected $alt = ''; ///< Identifiant de la zone d'affichage
	protected $product = null; ///< Informations sur le produit lié à la bannière
	protected $fields = null; ///< Champs avancés liés à la bannière

	private $id = 0;

	/** Cette fonction permet de créer un objet en préchargeant les informations.
	 * 	@param array $data Optionnel, permet de précharger les données (clés acceptées : 'bnr', 'title', 'url', 'img', 'desc', 'btnlabel')
	 */
	public function __construct( $data=[] ){
		$this->id = ria_array_get($data, 'bnr', 0);

		$this->title = ria_array_get($data, 'title', 0);
		$this->url = ria_array_get($data, 'url', 0);
		$this->img = ria_array_get($data, 'img', 0);
		$this->desc = ria_array_get($data, 'desc', '');
		$this->btnlabel = ria_array_get($data, 'btnlabel', '');
		$this->pos = ria_array_get($data, 'pos', '');
		$this->plcid = ria_array_get($data, 'plcid', '');
		$this->alt = ria_array_get($data, 'alt', '');

		$this->fields = new Collection();
	}

	/** Cette fonction permet de charger les informations sur une bannière.
	 * 	@return object L'instance en cours
	 */
	public function load(){
		global $config;

		// Récupère les informations sur une bannière
		$r_banner = adv_banners_get( 0, $this->id, true, i18n::getLang() );

		if( $r_banner && ria_mysql_num_rows($r_banner) ){
			$banner = ria_mysql_fetch_assoc($r_banner);

			$this->title = $banner['name'];
			$this->url = $banner['url'];
			$this->img = $config['banners_url'].'/'.$banner['image'];
			$this->desc = $banner['desc'];
			$this->btnlabel = (trim($banner['btn_label']) != '' ? $banner['btn_label'] : i18n::get('En savoir plus', 'BANNER'));
			$this->plcid = $banner['plc_id'];
			$this->alt = $banner['alt'];
			$this->pos = intval( $banner['pos'] );

			if( is_numeric($banner['prd_id']) && $banner['prd_id'] ){
				try{
					$this->product = new ProductService( ['prd' => $banner['prd_id']] );
					$this->product->card();
				}catch(Exception $e){
					$this->product = null;
				}
			}

			$r_fields = fld_fields_get( 0, 0, -2, 0, 0, $this->id, null, [], false, [], null, CLS_BANNER );

			if( $r_fields ){
				while( $field = ria_mysql_fetch_assoc($r_fields) ){
					// Transforme la valeur en tableau pour certains types de champs avancés
					// Liste de choix unique, Liste de choix multiple ou Liste de choix multiple hiérarchique
					if( in_array( $field['type_id'], [FLD_TYPE_SELECT, FLD_TYPE_SELECT_MULTIPLE, FLD_TYPE_SELECT_HIERARCHY]) ){
						$field['obj_value'] = explode( ', ', $field['obj_value'] );
					}

					$this->fields->addItem([
							'id' => $field['id'],
							'value' => $field['obj_value'],
						], 'field' . $field['id']
					);
				}
			}
		}

		return $this;
	}

	/** Cette fonction permet de charger les bannières présentes dans la visionneuse présentes sur la page d'accueil.
	 * 	@return array Un tableau contenant les bannières de la visionneuse de la page d'accueil
	 */
	public function home(){
		$r_place = adv_places_get(0, _ADV_PLC_VISIO, 'home');

		if( !$r_place || !ria_mysql_num_rows($r_place) ){
			return false;
		}

		$ar_banners = [];
		while( $place = ria_mysql_fetch_assoc($r_place) ){
			$temp = $this->forOneZone( $place['id'] );

			if( is_array($temp) && count($temp) ){
				$ar_banners = array_merge( $ar_banners, $temp );
			}
		}

		// Tri les bannières selon l'ordre d'affichage
		$ar_banners = array_msort( $ar_banners, ['pos' => SORT_ASC] );

		return $ar_banners;
	}

	/** Cette fonction permet de charger toutes les bannières d'un emplacement.
	 * 	@param $plc_id Obligatoire, identifiant d'un emplacement
	 * 	@return array Un tableau contenant les bannières de la zone
	 */
	public function forOneZone( $plc_id ){
		global $config;

		if( !is_numeric($plc_id) || $plc_id <= 0 ){
			return false;
		}

		// Charge les bannières liés à cet emplacement
		$r_banner = adv_banners_get( $plc_id, 0, true, i18n::getLang(), $config['wst_id'], true, false, true);
		if( !$r_banner ){
			return false;
		}

		// Charge un tableau de bannières
		$visio = new Collection();

		while( $banner = ria_mysql_fetch_assoc($r_banner) ){
			$obj_banner = new BannerService( ['bnr' => $banner['id']] );
			$obj_banner->load();

			$visio->addItem( $obj_banner );
		}

		return $this->transformObjectToArray( $visio->getAll() );
	}
}