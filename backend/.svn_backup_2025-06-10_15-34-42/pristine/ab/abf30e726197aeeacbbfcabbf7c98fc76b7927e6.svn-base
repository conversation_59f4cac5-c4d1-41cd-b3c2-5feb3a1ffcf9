
/* Transforme le menu principal en barre d'outils */
#site-menu-search {
	width: 0;
	height: 0;
	padding: 0;
	margin: 0;
	flex-basis: 0;
	#site-submenu{
		top: 140px;
	}
}
/* Champs spécifiques à la liste des commandes */
.ria-admin-ui-menu #ord-ref, .ria-admin-ui-menu #ret-ref {
	max-width: 148px;
}

/* = Menu = */
/* Menu principal de l'admin */
.ria-admin-ui-menu {
	list-style-type: none;
	font-size: 12px;
	margin-bottom: 0;
	margin-top: 0;
	li, ul {
		margin: 0;
		padding: 0;
	}
	@include media('>=large') {
		border-bottom: 1px solid $grey-medium-color;
		height: 66px;
		display: flex;
	}
	> li {
		flex: 0 1 150px;
		> .site-menu-btn {
			color: #222842;
			text-decoration: none;
			line-height: 44px;
			padding: 0 5px;
			display: block;
			font-size: 12px;
			&:hover,
			&.tab-menu-active {
				background-color: $medium-color;
				color: $white;
			}
			@include media('<large') {
				&:not(.tab-menu-active) {
					border-color: $white;
					// &:hover {
						// border-top: 1px solid $grey-medium-color;
						// border-bottom: 1px solid $grey-medium-color;
					// }
				}
			}
			@include media('>=large') {
				white-space: nowrap;
				padding-top: 42px;
				padding-bottom: 11px;
				position: relative;
				line-height: 13px;
				text-align: center;
				&:not(.tab-menu-active) {
					border-color: $white;
					&:hover {
						border-left: 1px solid $grey-medium-color;
						border-right: 1px solid $grey-medium-color;
					}
				}
			}
		}
	}
	label {
		margin-bottom: 8px;
		display: block;
	}
	/* Sous menu (Contextuel), Submenu */
	.ria-admin-ui-submenu {
		color: $dark-color;
		display: block;
		list-style-type: circle;
		list-style-position: outside;
		@include media('>=large') {
			position: absolute;
			left: 0px;
			width: 200px;
			margin-top: 1px;
			padding-bottom: 18px;
		}
		ul {
			margin-bottom: 0;
			li {
				position: relative;
				border-top: 1px solid $white;
				// &::after {
				// 	content: '';
				// 	top: -1px;
				// 	height: 1px;
				// 	display: block;
				// 	position: absolute; 
				// 	background-color: $grey-medium-color;
				// 	z-index: 1;
				// 	width: 85%;
				// 	left: 50%;
				// 	transform: translateX(-50%);
				// }
				a, div, span {
					&::before {
						content: '\002022';
						font-size: 28px;
						line-height: 10px;
						vertical-align: top;
						margin-right: 4px;
						display: inline-block;
					}
				}

			}
		}
		> li {
			text-align: left;
			list-style-type: none;
			border-bottom: 1px solid $grey-medium-color;
		}
		li > a, 
		li > div,
		li > span.disabled {
			border-style: none;
			display: block;
			color: $dark-color;
			padding: 10px 12px 6px;
			text-decoration: none;
			position: relative;
			&.ria-admin-ui-searchbar {
				margin: 0;
			}
			&:hover {
				text-decoration: none;
				background-color: $light-color;
			}
			&.ria-admin-ui-submenu--active {
				background-color: $medium-light-color;
				//font-weight: 600;
				color: $black;
			}
		}

		li > span.disabled {
			cursor: default;
			display: block;
		}

		li > ul > li > span.disabled {
		    display: block;
		    color: $dark-color;
		    padding: 10px 12px 6px;
		}
		
	}
}

#site-menu-home {
	display: none;
}

.site-menu-disabled{
	opacity: 0.5;
}

.site-menu-btn-disabled{
	opacity: 0.5;
	cursor : default;
}


.ria-admin-ui-menubar {
	background-color: $white;
	@include media('<large') {
		position: absolute;
		z-index: 10000;
		width: 100%;
		width: 100vw;
		max-width: 260px;
		box-shadow: 0 3px 8px rgba(0,0,0,0.2);
		background-image: none;
		height: auto;
		transform: translateX(-100%);
		transition: transform 0.2s ease-out;
		max-height: calc(100% - 74px);
		overflow-y: auto;
		padding-bottom: 10px;
		&.is-open {
			transform: translateX(0);
		}
	}
	.ria-admin-ui-searchbar {
		margin: 10px 12px;
		.mdc-text-field--fullwidth:not(.mdc-text-field--textarea):not(.mdc-text-field--disabled) {
			border: 1px solid $grey-medium-color;
			.mdc-text-field__input {
				padding-left: 8px;
			}
		}
	}
	.ria-admin-ui-headactions {
		display: none;
		@include media('<large') {
			display: block;
		}
		.mdc-button {
			padding: 0 12px;
			margin: 3px 0;
		}
		.ria-button--outline-dark {
			margin-left: 12px;
		}
	}
}
.ria-admin-ui-searchbar--top-menu {
	display: block;
	@include media('>=large') {
		display: none;
	}
}

.ria-admin-ui-legals {
	font-size: 10px;
	color: $dark-color;
	margin-top: 13px;
	padding: 0 12px;
	@include media('>=large') {
		display: none;
	}
}

.ria-sprite {
  @include media('<large') {
    position: static;
    display: inline-block;
    vertical-align: middle;
    transform: scale(0.7);
  }
	background-repeat: no-repeat;
	display: block;
	position: absolute;
	top: 6px;
	left: 0;
	right: 0;
	margin: 0 auto;
	width: 28px;
	height: 28px;
}

$sprites: (
	catalog: catalogue,
	config: configuration,
	customers: clients,
	media: mediatheque,
	moderation: moderation,
	order: commandes,
	pda: yuto,
	yuto: yuto,
	promotions: promotions,
	stats: statistiques,
	tools: outils,
	comparators: comparateurs,
);

@each $class, $picto in $sprites {
	.ria-sprite-#{$class} {
		background-image: url('/admin/dist/images/' + $picto + '_off.svg');
		.site-menu-btn:hover &, .tab-menu-active & {
			background-image: url('/admin/dist/images/' + $picto + '.svg');
		}
	}
}
.ria-admin-ui-menu #site-menu-options {
	flex-basis: 1px;
	.site-menu-btn {
		width: 1px;
		visibility: hidden;
		overflow: hidden;
		padding-left: 0;
		padding-right: 0;
		text-indent: -9999px;
	}
}

.block-submenu {
	position: absolute;
	cursor: pointer;
	width: 24px;
	height: 24px;
	left: 188px;
	border-radius: 50%;
	margin-top: 20px;
	z-index: 100;
	background: url('/admin/images/fold.svg') no-repeat center center;
	&:hover {
		background: url('/admin/images/fold_hover.svg') no-repeat center center;
	}
	&.animate-is-close {
		transform: rotate(180deg);
	// 	&:hover {
	// 		transform: rotate(0deg);
	// 	}
	}
	&.animate-is-open {
		transform: rotate(0deg);
	// 	&:hover {
	// 		transform: rotate(180deg);
	// 	}
	}
	@include media('<large') {
		display: none;
	}
}

#site-submenu{
	// max-height: 100vh;
	margin-bottom: 18px;
	// overflow-y: hidden;
	// custom de la scrollbar
	&::-webkit-scrollbar {
		width: 0;
	}
	
	&::-webkit-scrollbar-thumb {
		// padding-top: 10px !important;
		padding-bottom: 18px !important;
		background-color: $light-grey-blue;
		border-radius: 3px;
		&:hover {
			background-color: $blue-grey;
		}
	}
	// &:hover{
		
	// }
}

@include media('>large') {
	.fixedFold {
		position: fixed !important;
		overflow-y: auto;
		top: 0px;
	}
	.fixedTop {
		position: fixed !important;
		top: 0px;
	}
	.fixedBottom {
		position: fixed !important;
		overflow-y: auto;
		bottom: -18px;
	}
}