# Changes in PHP_CodeCoverage 3.2

All notable changes of the PHP_CodeCoverage 3.2 release series are documented in this file using the [Keep a CHANGELOG](http://keepachangelog.com/) principles.

## [3.2.1] - 2016-02-18

### Changed

* Updated dependency information in `composer.json`

## [3.2.0] - 2016-02-13

### Added

* Added optional check for missing `@covers` annotation when the usage of `@covers` annotations is forced

### Changed

* Improved `PHP_CodeCoverage_UnintentionallyCoveredCodeException` message

[3.2.1]: https://github.com/sebastianbergmann/php-code-coverage/compare/3.2.0...3.2.1
[3.2.0]: https://github.com/sebastian<PERSON>mann/php-code-coverage/compare/3.1...3.2.0

