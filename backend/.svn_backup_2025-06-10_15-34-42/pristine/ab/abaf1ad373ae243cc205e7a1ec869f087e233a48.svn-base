<?php

require_once('Export/Exception/BlExportException.php');

use \Export\Exception\BlExportException;

/**
 * Export d'un bon de livraison
 *
 * @param int Identifiant du bon de livraison
 * @param string Format d'export. Par défaut: pdf
 * @param array|null $options Options d'export
 * @param array|null $bl Données du bon de livraison (au format ord_bl_get) pour optimisation
 *
 * @return mixed Commande exportée dans le format demandé
 */
function export_bl( $bl_id, $output_format='pdf', array &$options=null, array &$bl=null ){
	global $config;

	$data['bl'] = $bl;
	// Charge la commande si elle n'a pas déjà été fournie en paramètre
	if( is_null($data['bl'])) {
		$r_bl = ord_bl_get( $bl_id );
		if( !$r_bl || !ria_mysql_num_rows($r_bl)){
			throw new BlExportException($bl_id, 'Bon de livraison non trouvé');
		}
		$data['bl'] = ria_mysql_fetch_assoc($r_bl);
	}

	$r_contact = ria_mysql_query('
		select adr_firstname, adr_lastname
		from gu_adresses
			join gu_users on (usr_tnt_id = '.$config['tnt_id'].' and adr_id = usr_adr_invoices)
				join ord_bl on (bl_tnt_id = '.$config['tnt_id'].' and bl_contact_id = usr_id)
		where adr_tnt_id = '.$config['tnt_id'].'
			and bl_id = '.$bl_id.'
	');

	if( $r_contact && ria_mysql_num_rows($r_contact) ){
		$contact = ria_mysql_fetch_assoc( $r_contact );
		$data['bl']['contact'] = [
			'firstname' => $contact['adr_firstname'],
			'lastname' => $contact['adr_lastname'],
		];
	}

	if (is_null($options)) {
		$options = array();
	}

	$options = array_merge(
		array(
			'name' => $config['pdf_generation_bl_name'],
			'logo' => $config['pdf_generation_bl_logo'],
			'logo_disposition' => $config['pdf_generation_bl_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_bl_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_bl_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_bl_display_dlv_address'],
			'prd_reduce' => $config['pdf_generation_bl_prd_reduce'],
			'display_payment' => isset($config['pdf_generation_bl_display_payment']) ? $config['pdf_generation_bl_display_payment'] : null,
			'header' => $config['pdf_generation_bl_header'],
			'header_content' => $config['pdf_generation_bl_header_content'],
			'footer' => $config['pdf_generation_bl_footer'],
			'footer_content' => $config['pdf_generation_bl_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_bl_prd_reftruncated'],
			'prd_ecotaxe' => $config['pdf_generation_bl_prd_ecotaxe'],
			'prd_barcode' => $config['pdf_generation_bl_prd_barcode'],
			'font_size' => $config['pdf_generation_bl_font_size'],
			'ref' => ''
		),
		$options
	);

	// extrait les produits du bon de livraison et leurs taxes
  $r_bl_prd = ord_bl_products_get( $data['bl']['id'], 0, false, ['group' => 'asc', 'group_parent' => 'asc', 'line_pos'=>'asc'] );
	$data['bl_products'] = array();
	$data['ecotaxe'] = array('base' => 0, 'amount' => 0);
	$data['tva'] = array();
	if (false !== $r_bl_prd && ria_mysql_num_rows($r_bl_prd) > 0) {
		while ($bl_prd = ria_mysql_fetch_assoc($r_bl_prd)) {
			$data['bl_products'][] = $bl_prd;

			$eco_total_ht = $eco_total_ttc = 0;

			if ($bl_prd['ecotaxe'] > 0) {
				$eco_total_ht = $bl_prd['ecotaxe'] * $bl_prd['qte'];
				$eco_total_ttc = $eco_total_ht * _TVA_RATE_DEFAULT;

				$data['ecotaxe']['base'] += $eco_total_ht;
				$data['ecotaxe']['amount'] += $eco_total_ttc - $eco_total_ht;
			}


			$total_ht = $bl_prd['total_ht'];
			$total_ttc = $total_ht *  $bl_prd['tva_rate'];
			if (isset($bl_prd['discount_type']) && isset($bl_prd['discount'])){
				if ($bl_prd['discount_type'] === "0"){ // Euros
					$total_ht = $bl_prd['total_ht'] - $bl_prd['discount'];
					$total_ttc = ($bl_prd['total_ht'] * $bl_prd['tva_rate']) - ($bl_prd['discount'] * $bl_prd['tva_rate']);
				} else { // %
					$total_ht = $bl_prd['total_ht'] * (1-($bl_prd['discount']/100));
					$total_ttc = ($bl_prd['total_ht'] * $bl_prd['tva_rate']) * (1-($bl_prd['discount']/100));
				}
			}

			if (!isset($data['tva'][$bl_prd['tva_rate']])) {
				$data['tva'][$bl_prd['tva_rate']] = array('base' => 0, 'amount' => 0);
			}

			$total_ht = $total_ht - $eco_total_ht;
			$total_ttc = $total_ttc - $eco_total_ttc;

			$data['tva'][$bl_prd['tva_rate']]['base'] += $total_ht;
			$data['tva'][$bl_prd['tva_rate']]['amount'] += $total_ttc - $total_ht;
		}
  }

	if (!isset($data['bl']['usr_id'])) {
		throw new BlExportException($data['bl']['id'], 'Aucun utilisateur lié à la commande');
	}

	$r_user = gu_users_get( $data['bl']['usr_id'] );
	$data['user'] = null;
	if( $r_user===false || ria_mysql_num_rows($r_user) <= 0) {
		//throw new BlExportException($data['bl']['id'], 'Utilisateur non trouvé');
	}else{
		$data['user'] = ria_mysql_fetch_assoc($r_user);
  }

	$data['addresses'] = ord_orders_address_load([
    'inv_id' => $data['bl']['adr_invoices'],
    'dlv_id' => $data['bl']['adr_dlv_id'],
    'usr_id' => $data['bl']['usr_id'],
    'str_id' => $data['bl']['str_id'],
    'rly_id' => 0,
  ]);

	$data['addresses']['invoice']['type'] = 'facturation';
	$data['addresses']['delivery']['type'] = 'livraison';

	$data['pay_name'] = '';
	$data['installments'] = array();


	// extrait le moyen de paiement
	if (trim($data['pay_name']) == '') {
		$r_pay = false;
		if ($data['bl']['pay_id'] <= 0) {
			$r_pay = gu_users_payment_types_get($data['user']['id']);
		} else {
			$r_pay = gu_users_payment_types_get(0, $data['bl']['pay_id']);
		}

		if ($r_pay && ria_mysql_num_rows($r_pay)) {
			$pay = ria_mysql_fetch_assoc($r_pay);
			$data['pay_name'] = gu_users_payment_types_view($pay);
		}
	}

	// extrait les infos du propriétaire du site
	$data['owner'] = site_owner_get();

	// extrait les infos bancaire du site
	$r_bankinfo = site_bank_details_get(0);
	$data['bank_details'] = null;
	if ($r_bankinfo && ria_mysql_num_rows($r_bankinfo)) {
		$data['bank_details'] = ria_mysql_fetch_assoc($r_bankinfo);
	}


	// On regarde s'il existe une promotion pour la commande
	// On part du pédicat qu'il n'y en a pas et on cherche à en trouver une
	if( $config["pdf_generation_bl_prd_reduce"] && $config["pdf_generation_bl_prd_reduce_hide_if_empty"] ) {
		$discountExists = false;
		$i = 0;
		while (!$discountExists && $i < sizeof($data["bl_products"])) {
			$product = $data["bl_products"][$i];

			$fld_discount = fld_object_values_get(array($product['bl_id'], $product['id'], $product['line']), _FLD_BL_LINE_DISCOUNT, '', false, true);
			if (is_numeric($fld_discount) && $fld_discount > 0) {
				$discountExists = true;
			}

			$i += 1;
		}

		$options["prd_reduce"] = $discountExists;
	}

  return export_bl_pdf($data, $options);
}

/**
 * Export PDF du bon de livraison
 * @see export_order
 *
 * @param array $data Données du bon de livraison
 * @param array|null & $options Options
 *
 * @return mixed Export PDF (dépend de l'option ouput)
 */
function export_bl_pdf( array & $data, array & $options = null ){
  global $config;

	switch($config['tnt_id']){
    default:
      // hack causé par les héritages
      $data['ord'] = $data['bl'];
      unset( $data['bl'] );

			require_once('Pdf/BlPdf.php');
			$pdf = new \Pdf\BlPdf($data['ord']);

			if(!is_null($options)){
				$pdf->setOptions($options);
			}

			$pdf->setData($data);

			if($data['ord']['pay_id'] == _PAY_VIREMENT){
				$pdf->setDisplayBankInfo();
			}

			$pdf->table()->withBody($data['bl_products']);
			$pdf->defaultProductTable();

			$filename = !isset($options['filename']) || trim($options['filename']) == ''
				? $data['ord']['id'] . '.pdf'
				: $options['filename'];

			$output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

			$pdf->headerOnAllPages(false)
				->showTaxcode(false)
				->addTotalPage()
				->generate($filename, $output);
	}
}