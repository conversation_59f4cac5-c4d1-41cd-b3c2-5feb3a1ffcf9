<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/pubsub.proto

namespace Google\Cloud\PubSub\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A message that is published by publishers and consumed by subscribers. The
 * message must contain either a non-empty data field or at least one attribute.
 * Note that client libraries represent this object differently
 * depending on the language. See the corresponding [client library
 * documentation](https://cloud.google.com/pubsub/docs/reference/libraries) for
 * more information. See [quotas and limits]
 * (https://cloud.google.com/pubsub/quotas) for more information about message
 * limits.
 *
 * Generated from protobuf message <code>google.pubsub.v1.PubsubMessage</code>
 */
class PubsubMessage extends \Google\Protobuf\Internal\Message
{
    /**
     * The message data field. If this field is empty, the message must contain
     * at least one attribute.
     *
     * Generated from protobuf field <code>bytes data = 1;</code>
     */
    private $data = '';
    /**
     * Attributes for this message. If this field is empty, the message must
     * contain non-empty data. This can be used to filter messages on the
     * subscription.
     *
     * Generated from protobuf field <code>map<string, string> attributes = 2;</code>
     */
    private $attributes;
    /**
     * ID of this message, assigned by the server when the message is published.
     * Guaranteed to be unique within the topic. This value may be read by a
     * subscriber that receives a `PubsubMessage` via a `Pull` call or a push
     * delivery. It must not be populated by the publisher in a `Publish` call.
     *
     * Generated from protobuf field <code>string message_id = 3;</code>
     */
    private $message_id = '';
    /**
     * The time at which the message was published, populated by the server when
     * it receives the `Publish` call. It must not be populated by the
     * publisher in a `Publish` call.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp publish_time = 4;</code>
     */
    private $publish_time = null;
    /**
     * If non-empty, identifies related messages for which publish order should be
     * respected. If a `Subscription` has `enable_message_ordering` set to `true`,
     * messages published with the same non-empty `ordering_key` value will be
     * delivered to subscribers in the order in which they are received by the
     * Pub/Sub system. All `PubsubMessage`s published in a given `PublishRequest`
     * must specify the same `ordering_key` value.
     *
     * Generated from protobuf field <code>string ordering_key = 5;</code>
     */
    private $ordering_key = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $data
     *           The message data field. If this field is empty, the message must contain
     *           at least one attribute.
     *     @type array|\Google\Protobuf\Internal\MapField $attributes
     *           Attributes for this message. If this field is empty, the message must
     *           contain non-empty data. This can be used to filter messages on the
     *           subscription.
     *     @type string $message_id
     *           ID of this message, assigned by the server when the message is published.
     *           Guaranteed to be unique within the topic. This value may be read by a
     *           subscriber that receives a `PubsubMessage` via a `Pull` call or a push
     *           delivery. It must not be populated by the publisher in a `Publish` call.
     *     @type \Google\Protobuf\Timestamp $publish_time
     *           The time at which the message was published, populated by the server when
     *           it receives the `Publish` call. It must not be populated by the
     *           publisher in a `Publish` call.
     *     @type string $ordering_key
     *           If non-empty, identifies related messages for which publish order should be
     *           respected. If a `Subscription` has `enable_message_ordering` set to `true`,
     *           messages published with the same non-empty `ordering_key` value will be
     *           delivered to subscribers in the order in which they are received by the
     *           Pub/Sub system. All `PubsubMessage`s published in a given `PublishRequest`
     *           must specify the same `ordering_key` value.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Pubsub\V1\Pubsub::initOnce();
        parent::__construct($data);
    }

    /**
     * The message data field. If this field is empty, the message must contain
     * at least one attribute.
     *
     * Generated from protobuf field <code>bytes data = 1;</code>
     * @return string
     */
    public function getData()
    {
        return $this->data;
    }

    /**
     * The message data field. If this field is empty, the message must contain
     * at least one attribute.
     *
     * Generated from protobuf field <code>bytes data = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setData($var)
    {
        GPBUtil::checkString($var, False);
        $this->data = $var;

        return $this;
    }

    /**
     * Attributes for this message. If this field is empty, the message must
     * contain non-empty data. This can be used to filter messages on the
     * subscription.
     *
     * Generated from protobuf field <code>map<string, string> attributes = 2;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getAttributes()
    {
        return $this->attributes;
    }

    /**
     * Attributes for this message. If this field is empty, the message must
     * contain non-empty data. This can be used to filter messages on the
     * subscription.
     *
     * Generated from protobuf field <code>map<string, string> attributes = 2;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setAttributes($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->attributes = $arr;

        return $this;
    }

    /**
     * ID of this message, assigned by the server when the message is published.
     * Guaranteed to be unique within the topic. This value may be read by a
     * subscriber that receives a `PubsubMessage` via a `Pull` call or a push
     * delivery. It must not be populated by the publisher in a `Publish` call.
     *
     * Generated from protobuf field <code>string message_id = 3;</code>
     * @return string
     */
    public function getMessageId()
    {
        return $this->message_id;
    }

    /**
     * ID of this message, assigned by the server when the message is published.
     * Guaranteed to be unique within the topic. This value may be read by a
     * subscriber that receives a `PubsubMessage` via a `Pull` call or a push
     * delivery. It must not be populated by the publisher in a `Publish` call.
     *
     * Generated from protobuf field <code>string message_id = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setMessageId($var)
    {
        GPBUtil::checkString($var, True);
        $this->message_id = $var;

        return $this;
    }

    /**
     * The time at which the message was published, populated by the server when
     * it receives the `Publish` call. It must not be populated by the
     * publisher in a `Publish` call.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp publish_time = 4;</code>
     * @return \Google\Protobuf\Timestamp|null
     */
    public function getPublishTime()
    {
        return isset($this->publish_time) ? $this->publish_time : null;
    }

    public function hasPublishTime()
    {
        return isset($this->publish_time);
    }

    public function clearPublishTime()
    {
        unset($this->publish_time);
    }

    /**
     * The time at which the message was published, populated by the server when
     * it receives the `Publish` call. It must not be populated by the
     * publisher in a `Publish` call.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp publish_time = 4;</code>
     * @param \Google\Protobuf\Timestamp $var
     * @return $this
     */
    public function setPublishTime($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Timestamp::class);
        $this->publish_time = $var;

        return $this;
    }

    /**
     * If non-empty, identifies related messages for which publish order should be
     * respected. If a `Subscription` has `enable_message_ordering` set to `true`,
     * messages published with the same non-empty `ordering_key` value will be
     * delivered to subscribers in the order in which they are received by the
     * Pub/Sub system. All `PubsubMessage`s published in a given `PublishRequest`
     * must specify the same `ordering_key` value.
     *
     * Generated from protobuf field <code>string ordering_key = 5;</code>
     * @return string
     */
    public function getOrderingKey()
    {
        return $this->ordering_key;
    }

    /**
     * If non-empty, identifies related messages for which publish order should be
     * respected. If a `Subscription` has `enable_message_ordering` set to `true`,
     * messages published with the same non-empty `ordering_key` value will be
     * delivered to subscribers in the order in which they are received by the
     * Pub/Sub system. All `PubsubMessage`s published in a given `PublishRequest`
     * must specify the same `ordering_key` value.
     *
     * Generated from protobuf field <code>string ordering_key = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setOrderingKey($var)
    {
        GPBUtil::checkString($var, True);
        $this->ordering_key = $var;

        return $this;
    }

}

