<?php

/**	\file products.php
 *	Cet écran est utilisé en popup pour sélectionner une promotion dont les règles "produits" seront dupliquées sur une promotion en cours.
 *	Il est par exemple appelé par le fichier /admin/promotions/specials/edit.php pour dupliquer une promotion.
 *	Les paramètres suivants sont attendus en argument :
 *	- type : identifiant du type de promotion
 *	- id : identifiant du code promotion qui va recevoir les données
 */
 
require_once('promotions.inc.php');

// Vérifie que l'utilisateur à bien accès à la page et qu'il à le droit de modifier une promotion
if( $_GET['type'] == _PMT_TYPE_SOLDES ){
    if( $_GET['id'] == 0 ){
        gu_if_authorized_else_403('_RGH_ADMIN_PROMO_SOLDE_ADD');
    }elseif( $_GET['id'] != 0 ){
        gu_if_authorized_else_403('_RGH_ADMIN_PROMO_SOLDE_VIEW');
    }
}elseif( $_GET['type'] == _PMT_TYPE_REWARD ){
    if( $_GET['id'] == 0 ){
        gu_if_authorized_else_403('_RGH_ADMIN_PROMO_REWARD_ADD');
    }elseif( $_GET['id'] != 0 ){
        gu_if_authorized_else_403('_RGH_ADMIN_PROMO_REWARD_VIEW');
    }
}else{
    if( $_GET['id'] == 0 ){
        gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD_ADD');
    }elseif( $_GET['id'] != 0 ){
        gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD_VIEW');
    }
}

$pmt_id = isset($_GET['id']) ? $_GET['id'] : 0;
if (!pmt_codes_exists($pmt_id)) {
    exit;
}

$rtype = null;
$type = null;
if (isset($_GET['type']) && is_numeric($_GET['type']) && $_GET['type'] > 0) {
    $rtype = pmt_types_get($_GET['type'], false);
    if (!$rtype || !ria_mysql_num_rows($rtype)) {
        exit;
    }
    $type = ria_mysql_fetch_assoc($rtype);
    if (!$type) {
        exit;
    }
}

$r_promotions = pmt_codes_get( null, null, false, $type['id'], false, null );

if (isset($_POST['pmt']) && is_array($_POST['pmt'])) {
    foreach($_POST['pmt'] as $other_pmt_id) {
        if ($pmt_id == $other_pmt_id) {
            continue;
        }

        $r_prd = pmt_products_get($other_pmt_id);
        if ($r_prd && ria_mysql_num_rows($r_prd)) {
            while ($prd = ria_mysql_fetch_assoc($r_prd)) {
                if (!pmt_products_add($_GET['id'], $prd['id'], intval($prd['include']), !$prd['discount'] ? 0 : $prd['discount'], $prd['discount_type'])) {
                    $prd_error = _('Erreur lors de l\'ajout d\'un produit');
                }
            }    
        }

        $r_prd = pmt_products_sets_get($other_pmt_id);
        if ($r_prd && ria_mysql_num_rows($r_prd)) {
            while ($prd = ria_mysql_fetch_assoc($r_prd)) {
                if (!pmt_products_sets_add($_GET['id'], $prd['ref_start'], $prd['ref_stop'], intval($prd['include']))) {
                    $prd_set_error = _('Erreur lors de l\'ajout d\'une plage produits');
                }
                
                if (!pmt_products_sets_set_discount($_GET['id'], $prd['ref_start'], $prd['ref_stop'], !$prd['discount'] ? 0 : $prd['discount'], $prd['discount_type'])) {
                    $prd_set_discount_error = _('Erreur lors de la modification des réductions d\'une plage produits');
                }
            }    
        }

        $r_cat = pmt_categories_get($other_pmt_id);
        if ($r_cat && ria_mysql_num_rows($r_cat)) {
            while ($cat = ria_mysql_fetch_assoc($r_cat)) {
                if (!pmt_categories_add($_GET['id'], $cat['id'], intval($cat['include']), !$cat['discount'] ? 0 : $cat['discount'], $cat['discount_type'])) {
                    $cat_error = _('Erreur lors de l\'ajout d\'une catégorie');
                }
            }
        }

        $r_brd = pmt_brands_get($other_pmt_id);
        if ($r_brd && ria_mysql_num_rows($r_brd)) {
            while ($brd = ria_mysql_fetch_assoc($r_brd)) {
                if (!pmt_brands_add($_GET['id'], $brd['id'], $brd['include'], !$brd['discount'] ? 0 : $brd['discount'], $brd['discount_type'])) {
                    $brd_error = _('Erreur lors de l\'ajout d\'une marque');
                }
            }
        }
    }

    if (isset($prd_error)) {
        $errors[] = $prd_error;
    }

    if (isset($prd_set_error)) {
        $errors[] = $prd_set_error;
    }

    if (isset($prd_set_discount_error)) {
        $errors[] = $prd_set_discount_error;
    }

    if (isset($cat_error)) {
        $errors[] = $cat_error;
    }

    if (isset($brd_error)) {
        $errors[] = $brd_error;
    }
}

// pmt_products_add( $cod, $rule['id'], $rule['include'] )

define('ADMIN_PAGE_TITLE', _('Import de produits') . ' - ' . _('Promotions'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');

require_once('admin/skin/header.inc.php');

if (isset($errors) && is_array($errors)) {
    foreach ($errors as $error) {
        print '<div class="error">' . htmlspecialchars($error) . '</div>';
    }
} elseif (!empty($_POST)) {
    print '<script>
        window.parent.loadRulesProducts();
        window.parent.hidePopup();
        </script>';
    exit;
}
?>

<form method="post">
    <table id="pmt-specials-product-import" class="test">
        <caption><?php print _('Promotions') ?></caption>
        <thead>
            <tr>
                <th id="pmt-check" class="col20px">
                    <input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
                </th>
                <th id="pmt-name"><?php print _('Désignation') ?></th>
                <th class="col80px"><?php print _('Déplacer') ?></th>
            </tr>
        </thead>
        <tbody><?php
while( $pmt = ria_mysql_fetch_assoc($r_promotions) ){
	
	// On ne peut pas importer une promotion sur elle-même
    if( intval($pmt['id'])==intval($_GET['id']) ){
        continue;
    }

    ?>
    <tr class="ria-row-orderable" id="line-<?php print intval($pmt['id']) ?>">
        <td>
            <input type="checkbox" name="pmt[]" id="pmt-<?php print $pmt['id']; ?>" value="<?php print intval($pmt['id']) ?>" />
        </td>
        <td><label for="pmt-<?php print $pmt['id']; ?>"><?php print htmlspecialchars( $pmt['code'] ? $pmt['code'] : $pmt['name'] ); ?></label></td>
        <td class="ria-cell-move"></td>
    </tr>
    <?php
}
        ?>
        </tbody>
        <tfoot>
            <tr>
                <td colspan="3" class="align-right">
                    <input type="submit" name="save" value="<?php print _('Importer les produits'); ?>" />
                </td>
            </tr>
        </tfoot>
    </table>
    <br />
    <div class="notice"><?php echo _("Vous pouvez importer les règles d'inclusions / exclusions depuis une ou plusieurs autres promotions. Si plusieurs promotions disposent de règles s'appliquant sur les mêmes contenus (produits, catégories, marques...), vous pouvez, via le glisser / déposer, gérer l'ordre d'import des règles."); ?></div>
</form>
<?php
require_once('admin/skin/footer.inc.php');
?>