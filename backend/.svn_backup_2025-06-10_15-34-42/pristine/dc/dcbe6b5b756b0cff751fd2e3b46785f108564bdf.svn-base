services:
    _defaults: {public: true}

    foo: {class: stdClass, public: false}

    bar:
        class: stdClass
        arguments: [ '@private_not_inlined' ]

    private: {class: stdClass, public: false}
    decorated_private: {class: stdClass}
    decorated_private_alias: '@foo'
    alias_to_private: '@private'
    private_alias: {alias: foo, public: false}

    private_decorator:
        class: stdClass
        decorates: 'decorated_private'

    private_alias_decorator:
        class: stdClass
        decorates: 'decorated_private_alias'

    private_not_inlined: {class: stdClass, public: false}
    private_not_removed: {class: stdClass, public: false}

    public_child: {parent: private, public: true}
