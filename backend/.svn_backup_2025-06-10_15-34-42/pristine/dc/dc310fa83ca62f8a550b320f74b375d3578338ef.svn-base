<?php
	require_once('calls.inc.php');
	require_once('stats.inc.php');

	print '
		<div class="graph-calls-semi-circle" id="graph-calls-representative-answered"></div>
		<div class="graph-calls-semi-circle" id="graph-calls-representative-all"></div>
	';
    
    $author = 0;
    if( isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] ){
        $author = $_GET['author'];
    }
	// Récupération des données
    $filtre = array("author" => $author);

    $date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	if( isset($_GET["date1"], $_GET["date2"]) ){
		$date1 = dateheureparse($_GET["date1"]);
        $date2 = dateheureparse( $_GET["date2"]);
	} 
    
	$calls_representative = stats_graphs_get_datas( 'calls-representative', $date1, $date2, $filtre );
	$colors[1] = "#434348";
	$colors[2] = "#90ed7d";
	$colors[3] = "#f7a35c";
	$colors[4] = "#7cb5ec";
	$colors[5] = "#8085e9";
	$colors[6] = "#f15c80";
	$colors[7] = "#e4d354";
	$colors[8] = "#2b908f";
	$colors[9] = "#f45b5b";
	$colors[10] = "#91e8e1";
	$colors[11] = "#ff8172";

	$all_brut 	= array(); // Appels totaux (brut)
	$answered_brut = array(); // Appels réussis (brut)
	$all 	= array(); // Appels totaux
    $answered = array(); // Appels réussis

	if( $calls_representative && sizeof($calls_representative) > 0 ){
		$others_all = array();
		$others_answered = array();
		foreach( $calls_representative as $xVal => $yVals ){
			$name = gu_users_get_name($xVal);
			if( $yVals['all'] !== 0 ){
				$all_brut[] = array("name" => ($name != '' ? $name : $xVal), "percent" => isset($yVals["all"]) ? $yVals["all"] : 0, "number" => isset($yVals["all_number"]) ? $yVals["all_number"] : 0) ;
				if( $yVals['answered'] !== 0 ){
					$answered_brut[] = array("name" => ($name != '' ? $name : $xVal), "percent" => isset($yVals["answered"]) ? $yVals["answered"] : 0, "number" => isset($yVals["answered_number"]) ? $yVals["answered_number"] : 0) ;
				}
			}
		}

		$all_brut = array_msort( $all_brut, array("percent" => SORT_DESC) );
		$answered_brut = array_msort( $answered_brut, array("percent" => SORT_DESC) );

		$i = 1;
		foreach( $all_brut as $key => $value ){
			if( $i < 11 ){
				$all[] = array("name" => $value['name'], "percent" => $value['percent'], "color" => $colors[$i], "number" => $value['number']) ;
			}else{
				if( !sizeof($others_all) ){
					$others_all = array("name" => _("Autres"), "percent" => $value['percent'], "color" => $colors[11], "number" => $value['number']) ;
				}else{
					$others_all["percent"] += $value['percent'];
					$others_all["number"] += $value['number'];
				}
			}
			$i++;			
		}
		$i = 1;
		foreach( $answered_brut as $key => $value ){
			if( $i < 11 ){
				$answered[] = array("name" => $value['name'], "percent" => $value['percent'], "color" => $colors[$i], "number" => $value['number']);
			}else{
				if( !sizeof($others_answered) ){
					$others_answered = array("name" => _("Autres"), "percent" => $value['percent'], "color" => $colors[11], "number" => $value['number']) ;
				}else{
					$others_answered["percent"] += $value['percent'];
					$others_answered["number"] += $value['number'];
				}
			}
			$i++;			
		}	
		if( sizeof($others_all) ){
			$all[11] = $others_all;			
			$all = array_msort( $all, array("percent" => SORT_DESC) );
		}
		if( sizeof($others_answered) ){
			$answered[11] = $others_answered;
			$answered = array_msort( $answered, array("percent" => SORT_DESC) );
		}
	?>


	<script>
		$(function () {
			$('#graph-calls-representative-all').highcharts({
				chart: {
					plotBackgroundColor: null,
					plotBorderWidth: 1,
					plotShadow: false
				},
				title: {
					text: '<?php print _('Proportion des appels par représentant'); ?>',
					align: 'center',
					verticalAlign: 'top',
					y: 25
				},
				tooltip: {
					pointFormat: '{series.name} <b>{point.percentage:.1f}%</b><br/>Nombre d\'appels : <b>{point.number}</b>'
				},
				credits: {
					enabled: false
				},
				exporting: {
					filename: 'all-representative-calls'
				},
				plotOptions: {
					pie: {
						dataLabels: {
							enabled: true,
							style: {
								fontWeight: 'bold',
								color: 'black'
							}
						},
						startAngle: -90,
						endAngle: 90,
						center: ['50%', '75%']
					}
				},
				series: [{
					type: 'pie',
					name: '<?php print _('Pourcentage des appels :'); ?>',
					innerSize: '50%',
					data: [<?php 
						foreach( $all as $val ){
			            	print '
			            	{
			            		name:"'.sprintf( ( ( is_numeric($val['name']) ? '<span class=\"barre\">compte n° %s</span>' : '%s') ),htmlspecialchars($val['name']) ).'",
								y:'.round( $val['percent'], 1).',
								color:\''.( $val['color'] ).'\',
								number:'.($val['number']).'

				            },';
		            	} ?> ]
				}]
			});

			$('#graph-calls-representative-answered').highcharts({
				chart: {
					plotBackgroundColor: null,
					plotBorderWidth: 1,
					plotShadow: false
				},
				title: {
					text: '<?php print _('Proportion des appels réussis par représentant'); ?>',
					align: 'center',
					verticalAlign: 'top',
					y: 25
				},
				credits: {
					enabled: false
				},
				exporting: {
					filename: 'answered-representative-calls'
				},
				tooltip: {
					pointFormat: '{series.name} <b>{point.percentage:.1f}%</b><br/>Nombre d\'appels : <b>{point.number}</b>'
				},
				plotOptions: {
					pie: {
						dataLabels: {
							enabled: true,
							style: {
								fontWeight: 'bold',
								color: 'black'
							}
						},
						startAngle: -90,
						endAngle: 90,
						center: ['50%', '75%']
					}
				},
				series: [{
					type: 'pie',
					name: '<?php print _('Pourcentage des appels :'); ?>',
					innerSize: '50%',
					data: [<?php 
						foreach( $answered as $val ){
			            	print '
			            	{
			            		name:"'.sprintf( ( ( is_numeric($val['name']) ? '<span class=\"barre\">compte n° %s</span>' : '%s') ),htmlspecialchars($val['name']) ).'",
								y:'.round( $val['percent'], 1).',
								color:\''.( $val['color'] ).'\',
								number:'.($val['number']).'
				            },';
		            	} ?> ]
				}]
			});
		});
	</script>

	<?php } ?>