<?php

	/** \file confirm-orders-ebay.php
	 *
	 * 	Ce script est destiné à confirmer les nouvelles commandes réalisées sur la place de marché eBay.
	 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
	 *
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('orders.inc.php');
	require_once('comparators/ctr.ebay.inc.php');


	// Active ou non le mode test
	$mode_test = isset($ar_params['test']) && $ar_params['test'] == 'test';

	// Traitement
	foreach( $configs as $config ){
		// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, on passe au client suivant.			
		if( !ctr_comparators_actived(CTR_EBAY) ){
			continue;
		}
		$states = array( 6, 7, 8 );
		
		$ebay = new EBay( $mode_test );
		if( !gu_users_exists($ebay->user_id) ){
			error_log('[EBay - Tenant '.$config['tnt_id'].'] Le compte client "'.$ebay->user_id.'" n\'existe pas (confirmation d\'expédition des commandes).');
			continue;
		}
		
		// Récupère les commandes pouvant faire l'objet d'une expédition
		$rord = ord_orders_get( $ebay->user_id, 0, $states, 0, null, false, false, false, false, false, false, '', true, array(_FLD_ORD_CTR_SHIPPED=>'Non') );
		if( !$rord || !ria_mysql_num_rows($rord) ){
			if( !$rord ){
				error_log('[EBay - Tenant '.$config['tnt_id'].'] Une erreur est survenue lors de la récupération des commandes expédiées.');
			}
			
			continue;
		}
		
		while( $ord = ria_mysql_fetch_array($rord) ){
			$ebay->confirmDeliveryOrder( $ord );
		}
	}
