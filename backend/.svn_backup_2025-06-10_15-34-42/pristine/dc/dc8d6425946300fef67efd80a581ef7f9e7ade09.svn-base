<?php

/**
 * Define a custom exception class
 */
class RiaShopException extends Exception
{
    // The string error code, null by default.
    protected $_riaCode;

    // The name of the invalid parameter, null by default.
    protected $_riaParam;

    // Redefine the exception so add text error code and invalid parameter name.
    public function __construct($message, $riaCode = null, $riaParam = null, $code=0, Throwable $previous = null) {
        
        // Save the riashop error code.
        $this->_riaCode = $riaCode;
        // Save the riashop error parameter name.
        $this->_riaParam = $riaParam;
    
        // make sure everything is assigned properly
        parent::__construct($message, $code, $previous);
    }

    // custom string representation of object
    public function __toString() {
        return __CLASS__ . ": [{$this->_riaCode}]: {$this->message}\n";
    }
    
    public function getRiaCode() {
        return $this->_riaCode;
    }

    public function getRiaParam() {
        return $this->_riaParam;
    }
}