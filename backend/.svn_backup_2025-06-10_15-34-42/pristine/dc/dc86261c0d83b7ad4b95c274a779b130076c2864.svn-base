# GULP

## Prérequis
- node v10.15.x
- gulp v3.9.x  // ATTENTION! Ne pas utiliser gulp 4.x

## Installation du projet
1. Aller dans le répertoire `htdocs/admin/src` dans le dossier root de la copie de travail
```bash
cd /var/www/<copy_de_travail>/htdocs/admin/src
```
2. Lancer l'installation des dépendances de développement `npm install`
```bash
npm install
```

## gulpfile.js
Le fichier contient les taches et les packages utilisés.
Le fichier gulpfile ne doit pas être commit sauf si des répertoires de destinations ou des nouveaux packages ont été ajoutés.

## Commandes gulp
Dans htdocs/admin/src:

- `gulp watch`
Permet de lancer gulp et de compiler les fichiers scss au fur et à mesure du développements.


- `gulp` : Lance en oneShot une compilation uniquement.

Gulp ne touche qu'aux fichiers présents dans htdocs/admin/src