Static calls
-----
<?php

// method name variations
A::b();
A::{'b'}();
A::$b();
A::$b['c']();
A::$b['c']['d']();

// array dereferencing
A::b()['c'];

// class name variations
static::b();
$a::b();
${'a'}::b();
$a['b']::c();
-----
!!php5
array(
    0: Expr_StaticCall(
        class: Name(
            parts: array(
                0: A
            )
            comments: array(
                0: // method name variations
            )
        )
        name: b
        args: array(
        )
        comments: array(
            0: // method name variations
        )
    )
    1: Expr_StaticCall(
        class: Name(
            parts: array(
                0: A
            )
        )
        name: Scalar_String(
            value: b
        )
        args: array(
        )
    )
    2: Expr_StaticCall(
        class: Name(
            parts: array(
                0: A
            )
        )
        name: Expr_Variable(
            name: b
        )
        args: array(
        )
    )
    3: Expr_StaticCall(
        class: Name(
            parts: array(
                0: A
            )
        )
        name: Expr_ArrayDimFetch(
            var: Expr_Variable(
                name: b
            )
            dim: Scalar_String(
                value: c
            )
        )
        args: array(
        )
    )
    4: Expr_StaticCall(
        class: Name(
            parts: array(
                0: A
            )
        )
        name: Expr_ArrayDimFetch(
            var: Expr_ArrayDimFetch(
                var: Expr_Variable(
                    name: b
                )
                dim: Scalar_String(
                    value: c
                )
            )
            dim: Scalar_String(
                value: d
            )
        )
        args: array(
        )
    )
    5: Expr_ArrayDimFetch(
        var: Expr_StaticCall(
            class: Name(
                parts: array(
                    0: A
                )
                comments: array(
                    0: // array dereferencing
                )
            )
            name: b
            args: array(
            )
            comments: array(
                0: // array dereferencing
            )
        )
        dim: Scalar_String(
            value: c
        )
        comments: array(
            0: // array dereferencing
        )
    )
    6: Expr_StaticCall(
        class: Name(
            parts: array(
                0: static
            )
            comments: array(
                0: // class name variations
            )
        )
        name: b
        args: array(
        )
        comments: array(
            0: // class name variations
        )
    )
    7: Expr_StaticCall(
        class: Expr_Variable(
            name: a
        )
        name: b
        args: array(
        )
    )
    8: Expr_StaticCall(
        class: Expr_Variable(
            name: Scalar_String(
                value: a
            )
        )
        name: b
        args: array(
        )
    )
    9: Expr_StaticCall(
        class: Expr_ArrayDimFetch(
            var: Expr_Variable(
                name: a
            )
            dim: Scalar_String(
                value: b
            )
        )
        name: c
        args: array(
        )
    )
)