Aliases (use)
-----
<?php

use A\B;
use C\D as E;
use F\G as H, J;

// evil alias notation - Do Not Use!
use \A;
use \A as B;

// function and constant aliases
use function foo\bar;
use function foo\bar as baz;
use const foo\BAR;
use const foo\BAR as BAZ;
-----
array(
    0: Stmt_Use(
        type: TYPE_NORMAL (1)
        uses: array(
            0: Stmt_UseUse(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    parts: array(
                        0: A
                        1: B
                    )
                )
                alias: B
            )
        )
    )
    1: Stmt_Use(
        type: TYPE_NORMAL (1)
        uses: array(
            0: Stmt_UseUse(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    parts: array(
                        0: C
                        1: D
                    )
                )
                alias: E
            )
        )
    )
    2: Stmt_Use(
        type: TYPE_NORMAL (1)
        uses: array(
            0: Stmt_UseUse(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    parts: array(
                        0: F
                        1: G
                    )
                )
                alias: H
            )
            1: Stmt_UseUse(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    parts: array(
                        0: J
                    )
                )
                alias: J
            )
        )
    )
    3: Stmt_Use(
        type: TYPE_NORMAL (1)
        uses: array(
            0: Stmt_UseUse(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    parts: array(
                        0: A
                    )
                )
                alias: A
            )
        )
        comments: array(
            0: // evil alias notation - Do Not Use!
        )
    )
    4: Stmt_Use(
        type: TYPE_NORMAL (1)
        uses: array(
            0: Stmt_UseUse(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    parts: array(
                        0: A
                    )
                )
                alias: B
            )
        )
    )
    5: Stmt_Use(
        type: TYPE_FUNCTION (2)
        uses: array(
            0: Stmt_UseUse(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    parts: array(
                        0: foo
                        1: bar
                    )
                )
                alias: bar
            )
        )
        comments: array(
            0: // function and constant aliases
        )
    )
    6: Stmt_Use(
        type: TYPE_FUNCTION (2)
        uses: array(
            0: Stmt_UseUse(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    parts: array(
                        0: foo
                        1: bar
                    )
                )
                alias: baz
            )
        )
    )
    7: Stmt_Use(
        type: TYPE_CONSTANT (3)
        uses: array(
            0: Stmt_UseUse(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    parts: array(
                        0: foo
                        1: BAR
                    )
                )
                alias: BAR
            )
        )
    )
    8: Stmt_Use(
        type: TYPE_CONSTANT (3)
        uses: array(
            0: Stmt_UseUse(
                type: TYPE_UNKNOWN (0)
                name: Name(
                    parts: array(
                        0: foo
                        1: BAR
                    )
                )
                alias: BAZ
            )
        )
    )
)