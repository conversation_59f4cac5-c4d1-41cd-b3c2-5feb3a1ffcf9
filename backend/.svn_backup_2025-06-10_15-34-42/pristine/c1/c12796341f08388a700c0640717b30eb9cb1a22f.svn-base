<?php
namespace Riashop\Salesforce;
/**
 * \ingroup salesforce_tasks
 * @class TaskFactory Gestion de la création de tache en fonction de l'identifiant de tache
 * @{
 */
class TaskFactory
{
	private $client = null;///< Gestion du client Salesforce

	private $task_list = array(
		TSK_COLLECTION => '\Riashop\Salesforce\Tasks\LinearRaised\Collection',
		TSK_COLLECTION_PRODUCTS => '\Riashop\Salesforce\Tasks\LinearRaised\CollectionProduct',
		TSK_LINEAR_RAISED_ADD => '\Riashop\Salesforce\Tasks\LinearRaised\LinearRaised',
		TSK_IMAGES_OBJECTS_ADD => '\Riashop\Salesforce\Tasks\Images\ImagesObjects', 
	);///< array Tableau des listes de tache prise en compte
	/**
	 * Constructeur
	 *
	 * @param SforceEnterpriseClient $SforceEnterpriseClient Client Salesforce
	 */
	public function __construct(\SforceEnterpriseClient $SforceEnterpriseClient=null)
	{
		$this->client = $SforceEnterpriseClient;
	}
	/**
	 * Fonction de création de l'instance de la tâche
	 *
	 * @param integer $task_id Identifiant d'une tâche
	 * @return Task retourne une instance d'une tâche
	 */
	public function make($task_id)
	{
		if( !$this->taskIsSupported($task_id) ){
			throw new \RunTimeException("Task $task_id is not supported");
		}

		return new $this->task_list[$task_id]($this->client);
	}
	/**
	 * Vérifie si la tâche est prise en compte
	 *
	 * @param integer $task_id Identifiant de la tâche
	 * @return boolean Retourn true si la tâche est prise en compte, sinon false
	 */
	private function taskIsSupported($task_id)
	{
		return is_numeric($task_id) && array_key_exists($task_id, $this->task_list);
	}
}
/// @}