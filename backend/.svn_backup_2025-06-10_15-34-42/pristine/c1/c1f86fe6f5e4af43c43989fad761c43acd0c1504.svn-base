<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AD' => 'Andɔ́ra',
  'AE' => 'Minlambɔ́ Nsaŋ́nsa mí Arabia',
  'AF' => 'Afganistaŋ',
  'AG' => 'Antíga bá Barbúda',
  'AI' => 'Anguílla',
  'AL' => 'Albania',
  'AM' => 'Arménia',
  'AO' => 'Angola',
  'AR' => 'Argentína',
  'AS' => 'Samoa m ́Amɛ́rka',
  'AT' => 'Ötrish',
  'AU' => 'Östraliá',
  'AW' => 'Árúba',
  'AZ' => 'Azerbaïjaŋ',
  'BA' => 'Bosnia na Ɛrzegovina',
  'BB' => 'Barbado',
  'BD' => 'Bɛŋgladɛsh',
  'BE' => 'Bɛlgik',
  'BF' => 'Burkina Faso',
  'BG' => 'Bulgaria',
  'BH' => 'Bahrain',
  'BI' => 'Burundi',
  'BJ' => 'Benin',
  'BM' => 'Bɛrmuda',
  'BN' => 'Brunɛi',
  'BO' => 'Bolivia',
  'BR' => 'Brésil',
  'BS' => 'Bahamas',
  'BT' => 'Butaŋ',
  'BW' => 'Botswana',
  'BY' => 'Belarus',
  'BZ' => 'Bɛliz',
  'CA' => 'Kanada',
  'CD' => 'Kongó Zaïre',
  'CF' => 'Sentrafríka',
  'CG' => 'Kongo',
  'CH' => 'Switzɛrland',
  'CI' => 'Kote d´Ivoire',
  'CK' => 'Maŋ́ má Kook',
  'CL' => 'Tshili',
  'CM' => 'Kamerun',
  'CN' => 'Shine',
  'CO' => 'Kɔlɔ́mbia',
  'CR' => 'Kosta Ríka',
  'CU' => 'Kuba',
  'CV' => 'Maŋ́ má Kapvɛr',
  'CY' => 'Sipria',
  'CZ' => 'Nlambɔ́ bó tschɛk',
  'DE' => 'Jaman',
  'DJ' => 'Jibúti',
  'DK' => 'Danemark',
  'DM' => 'Dominíka',
  'DO' => 'Nlambɔ́ Dominíka',
  'DZ' => 'Algeria',
  'EC' => 'Ekuateur',
  'EE' => 'Ɛstonia',
  'EG' => 'Ägyptɛn',
  'ER' => 'Erytrea',
  'ES' => 'Paŋá',
  'ET' => 'Ethiopiá',
  'FI' => 'Finlande',
  'FJ' => 'Fijiá',
  'FK' => 'Maŋ má Falkland',
  'FM' => 'Mikronesia',
  'FR' => 'Fala',
  'GA' => 'Gabɔŋ',
  'GB' => 'Nlambɔ́ Ngɛlɛn',
  'GD' => 'Grenada',
  'GE' => 'Jɔrgia',
  'GF' => 'Guyane Fala',
  'GH' => 'Gána',
  'GI' => 'Gilbratar',
  'GL' => 'Greenland',
  'GM' => 'Gambia',
  'GN' => 'Guine',
  'GP' => 'Guadeloup',
  'GQ' => 'Guine Ekuatorial',
  'GR' => 'Grɛce',
  'GT' => 'Guatemala',
  'GU' => 'Guam',
  'GW' => 'Guine Bisso',
  'GY' => 'Guyana',
  'HN' => 'Ɔndúras',
  'HR' => 'Kroasia',
  'HT' => 'Haïti',
  'HU' => 'Ɔngría',
  'ID' => 'Indonesia',
  'IE' => 'Irland',
  'IL' => 'Äsrɛl',
  'IN' => 'India',
  'IO' => 'Nlambɔ́ ngɛlɛn ma yí maŋ ntsiɛh',
  'IQ' => 'Irak',
  'IR' => 'Iran',
  'IS' => 'Island',
  'IT' => 'Italia',
  'JM' => 'Jamaika',
  'JO' => 'Jɔrdania',
  'JP' => 'Japɔn',
  'KE' => 'Kɛnya',
  'KG' => 'Kyrgystaŋ',
  'KH' => 'Kambodia',
  'KI' => 'Kiribati',
  'KM' => 'Kɔmɔr',
  'KN' => 'Saint Kitts na Nevis',
  'KP' => 'Koré yí bvuɔ',
  'KR' => 'Koré yí sí',
  'KW' => 'Kowɛit',
  'KY' => 'Maŋ́ má kumbi',
  'KZ' => 'Kazakstaŋ',
  'LA' => 'Laos',
  'LB' => 'Libaŋ',
  'LC' => 'Saint Lucia',
  'LI' => 'Lishenstein',
  'LK' => 'Sri Lanka',
  'LR' => 'Liberia',
  'LS' => 'Lesoto',
  'LT' => 'Lituaniá',
  'LU' => 'Luxembourg',
  'LV' => 'Latvia',
  'LY' => 'Libya',
  'MA' => 'Marɔk',
  'MC' => 'Monako',
  'MD' => 'Mɔldavia',
  'MG' => 'Madagaskar',
  'MH' => 'Maŋ́ má Marshall',
  'MK' => 'Macedonia',
  'ML' => 'Mali',
  'MM' => 'Myanmar',
  'MN' => 'Mɔngolia',
  'MP' => 'Maŋ́ Mariá',
  'MQ' => 'Martinika',
  'MR' => 'Moritania',
  'MS' => 'Mɔnserrat',
  'MT' => 'Malta',
  'MU' => 'Morisse',
  'MV' => 'Maldivia',
  'MW' => 'Malawi',
  'MX' => 'Mɛxik',
  'MY' => 'Malaysia',
  'MZ' => 'Mozambik',
  'NA' => 'Namibia',
  'NC' => 'Kaledoni nwanah',
  'NE' => 'Niger',
  'NF' => 'Maŋ́ má Nɔrfɔrk',
  'NG' => 'Nigeria',
  'NI' => 'Nikaragua',
  'NL' => 'Nedɛrland',
  'NO' => 'Nɔrvɛg',
  'NP' => 'Nepal',
  'NR' => 'Noru',
  'NU' => 'Niuɛ',
  'NZ' => 'Zeland nwanah',
  'OM' => 'Oman',
  'PA' => 'Panama',
  'PE' => 'Peru',
  'PF' => 'Polynesia Fala',
  'PG' => 'Guine Papuasi',
  'PH' => 'Filipin',
  'PK' => 'Pakistan',
  'PL' => 'Pɔlɔŋ',
  'PM' => 'Saint Peter ba Mikelɔn',
  'PN' => 'Pitkairn',
  'PR' => 'Puɛrto Riko',
  'PS' => 'Palɛstin',
  'PT' => 'Pɔrtugal',
  'PW' => 'Palo',
  'PY' => 'Paraguay',
  'QA' => 'Katar',
  'RE' => 'Réuniɔn',
  'RO' => 'Roumania',
  'RU' => 'Russi',
  'RW' => 'Rwanda',
  'SA' => 'Saudi Arabia',
  'SB' => 'Maŋ́ má Salomɔn',
  'SC' => 'Seychɛlle',
  'SD' => 'Sudaŋ',
  'SE' => 'Suɛd',
  'SG' => 'Singapur',
  'SH' => 'Saint Lina',
  'SI' => 'Slovenia',
  'SK' => 'Slovakia',
  'SL' => 'Sierra Leɔn',
  'SM' => 'San Marino',
  'SN' => 'Senegal',
  'SO' => 'Somália',
  'SR' => 'Surinam',
  'ST' => 'Sao Tomé ba Prinship',
  'SV' => 'Salvadɔr',
  'SY' => 'Syria',
  'SZ' => 'Swaziland',
  'TC' => 'Maŋ́ má Turk na Kaiko',
  'TD' => 'Tshad',
  'TG' => 'Togo',
  'TH' => 'Taïland',
  'TJ' => 'Tajikistaŋ',
  'TK' => 'Tokelo',
  'TL' => 'Timɔr tsindikēh',
  'TM' => 'Turkmɛnistaŋ',
  'TN' => 'Tunisiá',
  'TO' => 'Tɔnga',
  'TR' => 'Turki',
  'TT' => 'Trinidad ba Tobágó',
  'TV' => 'Tuvalú',
  'TW' => 'Taïwan',
  'TZ' => 'Tanzánía',
  'UA' => 'Ukrɛn',
  'UG' => 'Uganda',
  'US' => 'Amɛŕka',
  'UY' => 'Uruguay',
  'UZ' => 'Usbǝkistaŋ',
  'VA' => 'Vatikaŋ',
  'VC' => 'Saint Vincent ba Grenadines',
  'VE' => 'Vǝnǝzuela',
  'VG' => 'Minsilɛ́ mímaŋ mí ngɛ̄lɛ̄n',
  'VI' => 'Minsilɛ mí maŋ́ m´Amɛrka',
  'VN' => 'Viɛtnam',
  'VU' => 'Vanuatu',
  'WF' => 'Wallis ba Futuna',
  'WS' => 'Samoa',
  'YE' => 'Yǝmɛn',
  'YT' => 'Mayɔt',
  'ZA' => 'Afríka yí sí',
  'ZM' => 'Zambia',
  'ZW' => 'Zimbabwǝ',
);
