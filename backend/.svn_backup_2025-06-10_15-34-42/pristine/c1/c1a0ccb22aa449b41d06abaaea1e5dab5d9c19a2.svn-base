<?php
	/**	\file popup-quick-order.php
     *
	 *	Cette popup est utilisée dans le processus de commande rapide,
     *	elle permet de charger un textarea dans lequel on indique des couples reference//quantité
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_CREATE');

    $is_ajax = false;
    if (!isset($_GET['ord_id'])){
        $error = _('Il manque des paramètres');
    }
    if (!isset($_GET['user'])){
        $_GET['user'] = 0;
    }

    // Si requête ajax
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}

	if( !$is_ajax ) {
        define('ADMIN_PAGE_TITLE', _('Commande rapide'));
        define('ADMIN_HEAD_POPUP', true);
        define('ADMIN_ID_BODY', 'popup-content');
        require_once('admin/skin/header.inc.php');
    }
    if (isset($error)) {
        print '<div class="error">'.nl2br($error).'</div>';
        exit;
    }
?>

<div id="quick-order-instructions" class="notice">
    <p>Indiquez sur des lignes différentes les références des articles suivies d'un (ou de plusieurs) espace(s) puis de la quantité souhaitée. Par exemple :</p>
    <ul>
        <li>788600 2</li>
		<li>793500 5</li>
    </ul>
</div>

<form method="post" action="popup-quick-order.php">
    <?php
        print '<div class="quick-order-div" id="quick-order-ref">';
            print '<label class="quick-order-label" for="quick-order-references">'._('Liste des références :').'</label>';
            print '<textarea name="references" id="quick-order-references" col="80" rows="10" style="min-height: 360px"></textarea>';
            print '<input type="hidden" name="quick-order-product-ids" id="quick-order-product-ids" />';
        print '</div>';

        // Actions : analyser (etape 1), puis valider ou annnuler (etape 2)
        print '<div class="quick-order-step quick-order-step-1" style="display:block">
            <button type="button" id="quick-order-analyse" name="quick-order-analyse">'._('Analyser').'</button>&nbsp;
            <span class="loader" style="display:none;"><img src="/admin/images/loader2.gif" title="'._('Chargement en cours, veuillez patienter.').'" /> Veuillez patienter</span>
        </div>';
        print '<div class="quick-order-analyse-result quick-order-step quick-order-step-1"></div>';
        print '<div class="ord-actions quick-order-step quick-order-step-2" style="display:none">
            <input type="button" id="quick-order-validate" name="quick-order-validate" value="'._('Valider').'"/>
        </div>';
        print '<div class="ord-history align-right quick-order-step quick-order-step-2" style="display:none"><input type="button" id="quick-order-cancel" name="quick-order-cancel" value="'._('Annuler').'"/></div>';
        print '<div class="quick-order-add-success success quick-order-step quick-order-step-2 clear-both" style="display:none;"></div>';
    ?>
</form>

<?php
    if( !$is_ajax ){
?>
    <script>
        var ord_id = <?php print $_GET['ord_id']; ?>;
        var order_data = [];
        $(document).ready(function(){
            $('.page_timer').hide();
            // Etape 1 - Analyse du champ de commande rapide : verification de la conformité des lignes puis recherche des produits à partir des références si OK
            $("#quick-order-analyse").click(function(e){
                $('#quick-order-product-ids').val('');
                if (!$.trim($("#quick-order-references").val())) { // Textarea vide
                    return false;
                }
                $('.quick-order-analyse-result').hide(0).text('').removeClass('success error notice-default');
                $('.quick-order-step-1 .loader').fadeIn('fast');
                var lines = $("#quick-order-references").val().split('\n');
                var analyse_errors = [];
                lines.forEach(function(line, index){

					// Les espaces de début de ligne sont supprimés
					line = line.replace( /^\s+/g, '' );
					// Les espaces de fin de ligne sont supprimés
					line = line.replace( /\s+$/g, '' );
					// Remplace les espaces consécutifs par un seul (espaces et tabulations)
					line = line.replace( /\s+/g, ' ' );

                    var line_data = line.split(' ');

                    if (line_data.length !== 2) { // Chaque ligne ne doit avoir que 2 données : reference et quantité
                        analyse_errors.push(index+1);
                    } else {
                        if (isNaN(parseInt(line_data[1]))) { // La 2nde donnee doit etre un chiffre (quantité)
                            analyse_errors.push(index+1);
                        } else {
                            order_data.push({'reference' : line_data[0], 'quantity' : line_data[1]});
                        }
                    }
                });
                if (analyse_errors.length) { // Erreurs
                    if (analyse_errors.length == 1) {
                        var analyse_error_msg = 'Erreur détectée à la ligne '+analyse_errors[0];
                    } else if( analyse_errors.length==2 ){
                        var analyse_error_msg = 'Erreur détectée aux lignes '+analyse_errors.join(" et ");
                    }else{
						var analyse_error_msg = 'Erreur détectée aux lignes '+analyse_errors.join(", ");
					}
                    $('.quick-order-analyse-result').text(analyse_error_msg+'. Veuillez corriger.').addClass('error').fadeIn('fast');
                    $('.quick-order-step .loader').fadeOut('fast');
                } else if (order_data.length) { // Lignes conformes -> recherche par reference
                    $.ajax({
                        method : 'POST',
                        url : '/admin/ajax/orders/ajax-order-quick-order.php',
                        dataType: 'json',
                        data : {'action' : 'search', 'ord_id' : ord_id, 'order_data' : order_data},
                        success : function(resp) {
                            if (resp.result) {
                                order_data = resp.data.order_data;
                                $('.quick-order-step-1').fadeOut('fast', function(){
                                    $('.quick-order-step-2:not(.success)').fadeIn('fast');
                                });
                                if (resp.data.not_found != '') { // References non-trouvees, on ne bloque pas l'ajout mais on informe
                                    $('.quick-order-analyse-result').text('Les références suivantes n\'ont pas été trouvées :'+resp.data.not_found.join(", ")).addClass('notice-default').show('fast');
                                }
                            } else {
                                var search_error_msg = (resp.msg != '') ? resp.msg: 'Erreur lors de la recherche des produits. Veuillez recharger cette page et réessayer';
                                $('.quick-order-analyse-result').text('Impossible de rechercher les produits à partir des références').addClass('error').show('fast');
                            }
                        },
                        error : function() {
                            $('.quick-order-analyse-result').text('Impossible de rechercher les produits à partir des références').addClass('error').show('fast');
                        },
                        complete : function() {
                            $('.quick-order-step .loader').fadeOut('fast');
                        }
                    });
                }
                e.preventDefault();
            });
            // Etape 2 - Ajout des produits à la commande
            $('#quick-order-validate').click(function(e){
                $('.quick-order-add-success').text('').hide(0);
                var validate_btn  = $(this);
                validate_btn.prop("disabled", true);
                if (order_data.length) {
                    $.ajax({
                        method : 'POST',
                        url : '/admin/ajax/orders/ajax-order-quick-order.php',
                        dataType: 'json',
                        data : {'action' : 'add', 'ord_id' : ord_id, 'order_data' : order_data},
                        success : function(resp) {
                            if (resp.result) {
                                $('.quick-order-add-success').text(resp.msg).fadeIn('fast');
                                setTimeout(function(){
                                    window.parent.hidePopup(true);
                                }, 3000);
                            } else {
                                alert(resp.msg);
                            }
                        },
                        error : function() {
                            validate_btn.removeAttr("disabled");
                            alert('Erreur lors de l\'ajout des produits à la commande');
                        },
                    });
                } else {
                    validate_btn.removeAttr("disabled");
                    alert('Ajout à la commande impossible. Données manquantes');
                }
                e.preventDefault();
            });
            // Fermeture de la pop avec bouton Annuler
            $('#quick-order-cancel').click(function(e){
                window.parent.hidePopup();
            });
            // Edition du textarea : reaffichage des elements de la 1ere etape
            $('#quick-order-references').on('change keyup paste', function(){
                $('.quick-order-step-2').hide(0, function(){
                    $('.quick-order-step-1').show(0);
                });
            });
        });
    </script>

	<style>
		#quick-order-references {
			min-height: 330px;
		}
	</style>

<?php
        require_once('admin/skin/footer.inc.php');
    }
