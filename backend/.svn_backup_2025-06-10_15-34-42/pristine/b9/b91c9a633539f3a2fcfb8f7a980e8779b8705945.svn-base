<?php

require_once('documents.inc.php');

require_once('Services/Service.class.php');
require_once('Services/News/News.class.php');
require_once('Services/Documents/Document.class.php');

/**	\brief Cette classe permet de charger les informations sur un type de documents
 *
 */
class TypeDocument extends Service {
	protected $id; ///< Identifiant du type de documents
	protected $name; ///< Nom du type de documents
	protected $desc; ///< Description du type de documents
	protected $count; ///< Nombre de documents publiés dans ce type de documents
	protected $url; ///< URL donnant accès à ce type de documents
	protected $documents; ///< Liste des documents liés à ce type (chargée à la demande)
	protected $childs; ///< Liste des types de documents enfant
	protected $parentid; ///< Identifiant du type de documents parent
	protected $images; ///< Tableau d'identifiant des images liées à ce type de document
	protected $breadcrumbs = null; ///< Fil d'Ariane

	/** Cette fonction créé un objet permettant de charger les informations sur un type.
	 * 	@param array $data Optionnel, permet de transmettre les informations suivantes :
	 * 			- type : identifiant du type de documents
	 */
	public function __construct( $data=[] ){
		$this->id = ria_array_get( $data, 'type', 0 );
		$this->documents = new Collection();
		$this->childs = new Collection();
	}

	/** Cette fonction permet de charger les informations générales du type de documents.
	 * 	@return TypeDocument L'objet courant
	 */
	public function general(){
		if( !is_numeric($this->id) || $this->id <= 0 ){
			throw new Exception('Le type de documents n\'est pas identifié.');
		}

		$r_type = doc_types_get( $this->id, false, true, false, false, false, $parent=null);
		if( !$r_type || !ria_mysql_num_rows($r_type) ){
			throw new Exception('Le type de documents n\'existe pas ou plus.');
		}

		$type = i18n::getTranslation( CLS_TYPE_DOCUMENT, ria_mysql_fetch_assoc($r_type) );

		$this->name = $type['name'];
		$this->desc = view_site_format_description( $type['desc'] );
		$this->count = doc_types_get_docs_count( $this->id );
		$this->url = $type['url_alias'];
		$this->parentid = $type['parent_id'];

		$this->images = new Collection();

		$r_img = doc_types_images_get( $this->id );
		if( $r_img ){
			while( $img = ria_mysql_fetch_assoc($r_img) ){
				$this->images->addItem( $img['id'] );
			}
		}

		return $this;
	}

	/** Cette fonction permet de charger les documents présents dans ce type.
	 *
	 * 	@param int $start Optionnel, permet de préciser à partir de quel curseur on récupère les actualités
	 * 	@param bool|int $limit Optionnel, permet de préciser combien d'actualités sont récupérées (par défaut 20)
	 *	@param bool $recursive Optionnel, active la récupération récursive des documents (contenu dans les sous catégorie)
	 * 	@return TypeDocument L'object courant
	 */
	public function documents( $start=false, $limit=false, $recursive=false ){
		global $config;

		if( $this->id <= 0 ){
			throw new Exception('Le type de documents n\'est pas identifié, impossible de charger ses documents.');
		}

		if( $start !== false ){
			if( !is_numeric($start) || $start < 0 ){
				return false;
			}
		}

		if( $limit !== false ){
			if( !is_numeric($limit) || $limit <= 0 ){
				return false;
			}
		}

		// Récupère les documents présents dans ce type
		$r_doc = doc_documents_get( 0, $this->id, $config['wst_id'], '', true, i18n::getLang(), $recursive, null, $limit, $start);
		if( $r_doc ){
			while( $doc = ria_mysql_fetch_assoc($r_doc) ){
				$obj_doc = new Document( ['doc' => $doc['id']] );
				$this->documents->addItem( $obj_doc->general() );
			}
		}

		return $this;
	}

	/** Cette fonction permet de charger les types de documents enfants
	 * 	@param int $depth Optionnel, permet de préciser un niveau de profondeur de récupération
	 * 	@return TypeDocument L'objet courant
	 */
	public function childs( $depth=0 ){
		if( $this->id <= 0 ){
			throw new Exception('Le type de documents n\'est pas identifié, impossible de charger les types enfants.');
		}

		// Charge les types de documents enfants
		$r_child = doc_types_get( 0, false, true, false, false, false, $this->id );

		if( $r_child ){
			$depth--;

			while( $child = ria_mysql_fetch_assoc($r_child) ){
				$obj_child = new TypeDocument( ['type' => $child['id']] );
				$obj_child->general();

				if( $depth > 0 ){
					$obj_child->childs( $depth );
				}

				$this->childs->addItem( $obj_child );
			}
		}

		return $this;
	}

	/** Cette fonction permet de récupérer le premier niveau des types de documents.
	 * 	@return array Un tableau contenant le premier niveau
	 */
	public static function firstlevel(){
		$first_level = new Collection();

		// Charge les types de documents exclus
		$exclude = control_array_integer( Template::get('media-doc-exclude'), true );

		$r_type = doc_types_get( 0, false, true, false, false, false, null, $exclude );
		if( $r_type ){
			while( $type = ria_mysql_fetch_assoc($r_type) ){
				$obj_type = new TypeDocument( ['type' => $type['id']] );
				$first_level->addItem( $obj_type->general()->documents()->childs() );
			}
		}

		return self::transformObjectToArray( $first_level );
	}

	/** Cette foncton permet de charger un menu de type de document.
	 * 	@return array Un tableau contenant le menu
	 */
	public static function arbo(){
		$arbo = new Collection();

		// Charge les types de documents exclus
		$exclude = control_array_integer( Template::get('media-doc-exclude'), true );

		$r_type = doc_types_get( 0, false, true, false, false, false, null, $exclude );
		if( $r_type ){
			while( $type = ria_mysql_fetch_assoc($r_type) ){
				$obj_type = new TypeDocument( ['type' => $type['id']] );
				$arbo->addItem( $obj_type->general()->childs( Template::get('downloads-depth') ) );
			}
		}

		return self::transformObjectToArray( $arbo );
	}

	/** Cette fonction permet de charger le fil d'Ariane pour un type de documents.
	 * 	@return TypeDocument L'objet courant
	 */
	public function breadcrumbs(){
		global $config;

		$this->breadcrumbs = new Collection();

		if( is_numeric($this->id) && $this->id >0 ){

			$parent = $this->parentid;

			$i = 0;
			while( $parent ){
				if( ($i++)>20 ){
					// Sécurité
					break;
				}

				// Info sur le parent
				$r_type_parent = ria_mysql_query('
					select type_name, type_url_alias, type_parent_id
					from doc_types
					where type_tnt_id = '.$config['tnt_id'].'
						and type_id = '.$parent.'
				');

				$parent = 0;
				if( $r_type_parent && ria_mysql_num_rows($r_type_parent) ){
					$type_parent = i18n::getTranslation( CLS_TYPE_DOCUMENT, ria_mysql_fetch_assoc($r_type_parent) );
					$this->breadcrumbs->addItem([
						'url' => $type_parent['type_url_alias'],
						'text' => $type_parent['type_name'],
					]);

					if( is_numeric($type_parent['type_parent_id']) && $type_parent['type_parent_id'] > 0 ){
						$parent = $type_parent['type_parent_id'];
					}
				}
			}

			$this->breadcrumbs->reverse();
		}

		return $this;
	}
}