# OrderIndexLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**self** | [**\Swagger\Client\Model\LinksGetOrderIndexLink**](LinksGetOrderIndexLink.md) |  | 
**status** | [**\Swagger\Client\Model\LinksGetMarketplaceAccountsSynchronizationLink**](LinksGetMarketplaceAccountsSynchronizationLink.md) |  | 
**harvest** | [**\Swagger\Client\Model\LinksHarvestAllLink**](LinksHarvestAllLink.md) |  | 
**set_merchant_infos** | [**\Swagger\Client\Model\LinksSetMerchantOrderInfoListLink**](LinksSetMerchantOrderInfoListLink.md) |  | 
**clear_merchant_infos** | [**\Swagger\Client\Model\LinksClearMerchantOrderInfoListLink**](LinksClearMerchantOrderInfoListLink.md) |  | 
**export** | [**\Swagger\Client\Model\LinksExportOrdersLink**](LinksExportOrdersLink.md) |  | 
**auto_transitions** | [**\Swagger\Client\Model\LinksGetAutomaticTransitionsLink**](LinksGetAutomaticTransitionsLink.md) |  | 
**exportations** | [**\Swagger\Client\Model\LinksGetOrderExportationsLink**](LinksGetOrderExportationsLink.md) |  | [optional] 
**orders** | [**\Swagger\Client\Model\LinksGetOrderListFullLink**](LinksGetOrderListFullLink.md) |  | 
**light_orders** | [**\Swagger\Client\Model\LinksGetOrderListLightLink**](LinksGetOrderListLightLink.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


