{"name": "simplesamlphp/simplesamlphp-module-ldap", "description": "A module that provides authentication against LDAP stores", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "ldap"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload": {"psr-4": {"SimpleSAML\\Module\\ldap\\": "lib/"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "phpunit/phpunit": "~5.7"}, "suggest": {"ext-ldap": "Needed when using LDAP authentication in SimpleSAMLphp"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-ldap/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-ldap"}}