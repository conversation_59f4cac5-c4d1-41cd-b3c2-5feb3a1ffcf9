<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'libphonenumber\\' => array($vendorDir . '/giggsey/libphonenumber-for-php/src'),
    'Symfony\\Polyfill\\Php72\\' => array($vendorDir . '/symfony/polyfill-php72'),
    'Symfony\\Polyfill\\Php70\\' => array($vendorDir . '/symfony/polyfill-php70'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Idn\\' => array($vendorDir . '/symfony/polyfill-intl-idn'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Swagger\\Client\\' => array($vendorDir . '/beezup/api-php-client/lib'),
    'Rize\\' => array($vendorDir . '/rize/uri-template/src/Rize'),
    'Ramsey\\Uuid\\' => array($vendorDir . '/ramsey/uuid/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-message/src'),
    'Psr\\Cache\\' => array($vendorDir . '/psr/cache/src'),
    'Pheanstalk\\' => array($vendorDir . '/pda/pheanstalk/src'),
    'PHPMailer\\PHPMailer\\' => array($vendorDir . '/phpmailer/phpmailer/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'Grpc\\Gcp\\' => array($vendorDir . '/google/grpc-gcp/src'),
    'Grpc\\' => array($vendorDir . '/grpc/grpc/src/lib'),
    'Google\\Protobuf\\' => array($vendorDir . '/google/protobuf/src/Google/Protobuf'),
    'Google\\Cloud\\PubSub\\' => array($vendorDir . '/google/cloud-pubsub/src'),
    'Google\\Cloud\\Datastore\\' => array($vendorDir . '/google/cloud-datastore/src'),
    'Google\\Cloud\\Core\\' => array($vendorDir . '/google/cloud-core/src'),
    'Google\\Cloud\\BigQuery\\' => array($vendorDir . '/google/cloud-bigquery/src'),
    'Google\\Auth\\' => array($vendorDir . '/google/auth/src'),
    'Google\\ApiCore\\' => array($vendorDir . '/google/gax/src'),
    'Google\\' => array($vendorDir . '/google/common-protos/src'),
    'Giggsey\\Locale\\' => array($vendorDir . '/giggsey/locale/src'),
    'GPBMetadata\\Google\\Pubsub\\' => array($vendorDir . '/google/cloud-pubsub/metadata'),
    'GPBMetadata\\Google\\Protobuf\\' => array($vendorDir . '/google/protobuf/src/GPBMetadata/Google/Protobuf'),
    'GPBMetadata\\Google\\Datastore\\' => array($vendorDir . '/google/cloud-datastore/metadata'),
    'GPBMetadata\\Google\\' => array($vendorDir . '/google/common-protos/metadata'),
    'GPBMetadata\\ApiCore\\' => array($vendorDir . '/google/gax/metadata/ApiCore'),
    'Firebase\\JWT\\' => array($vendorDir . '/firebase/php-jwt/src'),
    '' => array($vendorDir . '/lyracom/rest-php-sdk/src'),
);
