<?php

	/**	\file export.php
	 * 
	 * 	Ce fichier exporte les adresses des personnes inscrites aux alertes de disponibilité
	 * 
	 */

	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ALERT');

	$alphabet = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');
	
	$fields = array(
		_('Référence') => 'ref',
		_('Désignation') => 'name',
		_('Marque') => 'brand',
		_('Adresse email') => 'usr_email',
		_('En attente depuis le') => 'date_created'
	);
	
	require_once( 'excel/PHPExcel.php' );

	// Période
	// Variable pour la mise en place des périodes
	$date1 = false;
	$date2 = false; 
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d H:i:s');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	if ( isset($_GET["date1"], $_GET["date2"]) ) {
		$date1 = ria_mysql_escape_string(dateheureparse($_GET["date1"]));
		$date2 = ria_mysql_escape_string(dateheureparse($_GET["date2"]));
	}

	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();

	// Déterminé les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
								 ->setLastModifiedBy("riaStudio")
								 ->setTitle(_("Export des alertes de disponibilité"))
								 ->setSubject(_("Export des alertes de disponibilité"))
								 ->setDescription(_("Export des alertes de disponibilité"))
								 ->setKeywords(_("export alertes disponibilite"))
								 ->setCategory("");
	$objWorksheet = $objPHPExcel->getActiveSheet();
	$objWorksheet->setTitle(_('Alertes de disponibilités'));
	$ligne=2;	
	
	// Impression de l'entâªte
	$i = 0;
	foreach( $fields as $name => $field ){
		$objWorksheet->setCellValue($alphabet[$i].'1', $name );
		$i++;
	}
		
	// Police de l'entâªte du tableau
	$objWorksheet->getStyle('A1:'.$alphabet[$i-1].'1')->getFont()->setBold(true);
	$objWorksheet->getStyle('A1:'.$alphabet[$i-1].'1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet->getStyle('A1:'.$alphabet[$i-1].'1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
	
	// Chargement des adresses
	$alerts = gu_livr_alerts_get(0, 0, 0, true, $date1, $date2);
	
	while( $r = ria_mysql_fetch_array($alerts) ){
		$r['brand'] = '';
		$rp = prd_products_get_simple( $r['id'] );
		if( $rp && ria_mysql_num_rows($rp) ){
			$p = ria_mysql_fetch_assoc( $rp );
			$r['brand'] = $p['brd_title'];
		}

		$i = 0;
		foreach( $fields as $name=>$field ){
			$objWorksheet->setCellValueExplicit($alphabet[$i].$ligne, iconv("utf-8", "UTF-8//IGNORE", $r[$field]),PHPExcel_Cell_DataType::TYPE_STRING);
			
			$i++;
		}
		$objWorksheet->getStyle('A1:'.$alphabet[$i-1].$ligne)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
		$ligne++;
	}
	
	// Fixe la largeur des colonnes
	$objWorksheet->getColumnDimension('A')->setWidth(15);
	$objWorksheet->getColumnDimension('B')->setWidth(58);
	$objWorksheet->getColumnDimension('C')->setWidth(37);
	$objWorksheet->getColumnDimension('D')->setWidth(25);
	
	// Met des bordures en place
	$objWorksheet->getStyle('A1:'.$alphabet[$i-1].($ligne-1))->getBorders()->applyFromArray(
		array(
			'allborders' => array(
				'style' => PHPExcel_Style_Border::BORDER_THIN,
			)
		)
	);

	// Redirect output to a clientâs web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="'._('alertes-de-disponibilite').'-'.date('Y-m-d').'.xls"');
	header('Cache-Control: max-age=0');

	// Ecrit le fichier et le sauvegarde
	$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
	$objWriter->save('php://output');
	exit;
