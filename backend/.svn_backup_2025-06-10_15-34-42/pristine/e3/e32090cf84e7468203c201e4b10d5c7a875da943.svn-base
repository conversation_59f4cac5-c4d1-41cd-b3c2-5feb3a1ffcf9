<?php

	/**	Cet écran constitue la fiche d'une image
	 *	Il affiche les relations de cette image avec les contenus (Catégories, Produits, Clients, etc...)
	 */

	require_once('images.inc.php');
	require_once('view.translate.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');
	
	/* Gestion des onglets */
	if( !isset($_GET['tab']) ){
		$_GET['tab'] = 'general';
	}
	$tab = $_GET['tab'];
	if( isset($_POST['tabGeneral']) ){
		$tab = 'general';
	}elseif( isset($_POST['tabFields']) ){
		$tab= 'fields';
	}

	if( !isset($_GET['image']) || !img_images_exists($_GET['image']) ){
		header('Location: /admin/documents/images/index.php');
		exit;
	}
	
	// Charge l'image demandée
	$image = ria_mysql_fetch_array(img_images_get( $_GET['image'] ));
	
	// Suppression
	if( isset($_GET['image'], $_GET['deleted']) ){
		if( !img_images_del($_GET['image'], true) )
			$error = "Une erreur inattendue s'est produite lors de la suppression de l'image.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.";
		
		if( !isset($error) ){
			header('Location: /admin/documents/images/edit.php?image'.$_GET['image']);
			exit;
		}
	}

	// Enregistrer une nouvelle version
	if( isset($_POST['save-new-file']) && isset($_FILES) && !$image['is_sync'] ){
		if( !$id = img_images_replace($_GET['image'], 'new-file-media') )
			$error = "Une erreur inattendue s'est produite lors de l'enregistrement de la nouvelle version.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.";
		
		if( !isset($error) ){
			header('Location: /admin/documents/images/edit.php?image='.$id);
			exit;
		}
	}

	$lng = view_selected_language();

	// Action sur l'onglet "Avancés"
	view_admin_tab_fields_actions( CLS_IMAGE, $_GET['image'], $lng );
	
	// dimensions des images
	$size = $config['img_sizes']['high'];
	$sizeM = $config['img_sizes']['medium'];
	
	// recherche les contenus rattachés à l'image
	/*$ar_prd = img_images_get_by_class( $image['id'], CLS_PRODUCT );		// produit
	$ar_cat = img_images_get_by_class( $image['id'], CLS_CATEGORY );	// catégorie de produit
	$ar_brd = img_images_get_by_class( $image['id'], CLS_BRAND ); 		// marques
	$ar_str = img_images_get_by_class( $image['id'], CLS_STORE );		// magasins
	$ar_cms = img_images_get_by_class( $image['id'], CLS_CMS );			// gestion de contenu
	$ar_nws = img_images_get_by_class( $image['id'], CLS_NEWS );		// actualité*/

	// Gestion des contenus en fonction des droits accessible
	{
		$contenu = array();
		
		// Prodtuis
		if( gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_PRD') ){
			$contenu[] = CLS_PRODUCT;
		}
		
		// Catégories de produit
		if( gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_CAT') ){
			$contenu[] = CLS_CATEGORY;
		}
		
		// Marques
		if( gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_BRD') ){
			$contenu[] = CLS_BRAND;
		}
		
		// Magasins
		if( gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_STR') ){
			$contenu[] = CLS_STORE;
		}
		
		// Pages de contenu
		if( gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_CMS') ){
			$contenu[] = CLS_CMS;
		}
		
		// Actualités
		if( gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_NEWS') ){
			$contenu[] = CLS_NEWS;
		}
		
		// Comptes
		if( gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_USR') ){
			$contenu[] = CLS_USER;
		}
	}

	$unused = isset($_GET['unused']) && $_GET['unused'];

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php' )
		->push( _('Images'), '/admin/documents/images/index.php' );

	if( $unused ){
		Breadcrumbs::add( _('Non utilisées'), '/admin/documents/images/index.php?unused=1' );
	}

	if( $image = ria_mysql_fetch_array(img_images_get( $_GET['image'] )) ){
		$image_name = _('Image').' '.( $image['src_name']!='' ? $image['src_name'] : $image['id'] );
		Breadcrumbs::add( _('Image').' '.$image_name );
	}else{
		Breadcrumbs::add( _('Image') );
	}


	define('ADMIN_PAGE_TITLE', _('Images') . ' - ' . _('Médiathèque'));
	define('ADMIN_CLASS_BODY', 'img-detail');
	require_once('admin/skin/header.inc.php');
?>
	<input type="hidden" name="id-img" id="id-img" value="<?php print $image['id']; ?>" />
	
	<h2><?php print view_image_is_sync( $image ); ?> <?php print $image['src_name']!='' ? htmlspecialchars( $image['src_name'].'.'.$image['type'] ) : _('Nom de l\'image : Non disponible'); ?></h2>

	<form action="edit.php?image=<?php print $_GET['image']; ?>&amp;lng=<?php print $lng; ?>" method="post" enctype="multipart/form-data">
	<input type="hidden" name="lng" id="lng" value="<?php print $lng; ?>" />
	
	<?php if( view_admin_show_tab_fields( CLS_IMAGE, $_GET['image'] ) ){ ?>
	<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="<?php echo _("Général"); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
		<?php if( view_admin_show_tab_fields( CLS_IMAGE, $_GET['image'] ) ){ ?>
		<li><input type="submit" name="tabFields" value="<?php echo _("Avancé"); ?>" <?php if( $tab=='fields' ) print 'class="selected"'; ?> /></li>
		<?php } ?>
	</ul>
	<div id="tabpanel">
	<?php }
	
		if( $tab=='general' ){ ?>
			<div id="img-infos">
				<div id="media">
					<img src="<?php print $config['img_url'].'/'.$size['dir'].'/'.$image['id'].'.'.$size['format']; ?>" width="<?php print $size['width']; ?>" height="<?php print $size['height']; ?>" alt="" />
					<br /><a href="download.php?image=<?php print $image['id']; ?>" class="button"><?php print _("Télécharger l'original"); ?></a>
					<?php if( !$image['is_sync'] ) { ?>
		            	<br /><a href="#" onclick="return displayNewMedia();" class="button"><?php echo _('Envoyer une nouvelle version'); ?></a>
					<?php } ?>
					<br />
					<?php if( ( !isset($_GET['unused']) && gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_DEL')) || ( isset($_GET['unused']) && $_GET['unused']) && gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_UNUSED_DEL') ){ ?>
					<a class="button" onclick="return window.confirm('<?php 
						print _('La suppression sera irréversible et toutes les utilisations de cette image seront aussi supprimées. Êtes-vous sur de vouloir supprimer cette image ?'); 
						?>');" 
							href="edit.php?image=<?php print $image['id']; ?>&amp;deleted=1" 
							title="<?php print _('Supprimer définitivement cette image de la médiathèque') ?>"
						><?php 
							print _('Supprimer cette image') 
						?></a>
					<br />
					<?php }
					if (isset($config['img_sizes']['very_high'])) { ?>
					<a class="button" onclick="displayPopup('<?php 
						print _('Définir des zones cliquables') ?>', '', '/admin/documents/images/zones.php?image=<?php print intval($image['id']) ?>', null, 756, 598);" 
						href="#" 
					><?php 
						print _('Zones cliquables'); 
					?></a>
					<?php } ?>
					<div id="new-media">
						<input type="file" name="new-file-media" id="new-file-media" />
						<input type="submit" name="save-new-file" id="save-new-file" value="<?php print _('Confirmer'); ?>" />
						<input onclick="return hideNewMedia();" type="button" name="cancel-new-file" id="cancel-new-file" value="<?php print _('Annuler'); ?>" />
					</div>
				</div><div class="elem-links">
				<?php
					// Affiche les contenus auxquels cette image est liée
					foreach( $contenu as $c ){
						print img_images_view_linked( $image['id'], $c );
					}
				?></div>
			</div>
			<div class="clear"></div>
			<div class="maxipopup" id="popup_ria"><div class="popup_ria_drag"></div><div class="content"></div></div>

	<?php }elseif( $tab=='fields' ){
		print view_admin_tab_fields( CLS_IMAGE, $image['id'], $lng, 'edit.php?image='.$_GET['image'].'&amp;tab=fields' );
	}
	
	if( view_admin_show_tab_fields( CLS_IMAGE, $_GET['image'] ) ){ ?>
	</div>
	<?php } ?>
	
			<input type="hidden" name="unused" id="unused" value="<?php print $unused; ?>" />

			<div id="list-img"><?php
				// récupère les images
				$rimg = img_images_get( 0, '', '', '', '', $unused ? 0 : null );
				$ar_img = array();
				if( $rimg ){
					while( $img = ria_mysql_fetch_array($rimg) )
						$ar_img[] = $img['id'];
				}
				
				if( sizeof($ar_img) ){
					$pos_img = array_search( $image['id'], $ar_img );
					
					$min = $pos_img-2>=0 ? $pos_img-2 : 0; 
					$max = $pos_img+2<sizeof($ar_img) ? $pos_img+2 : sizeof($ar_img);
					
					for( $i=$min ; $i<=$max ; $i++ ){
						if( !isset($ar_img[$i]) )
							continue;
						if( $i!=$pos_img ){
							print '	<div class="img-images">
										<a href="edit.php?image='.$ar_img[ $i ].'" >
											<img src="'.$config['img_url'].'/'.$sizeM['dir'].'/'.$ar_img[ $i ].'.'.$sizeM['format'].'" alt="" width="'.$sizeM['width'].'" height="'.$sizeM['height'].'" />
										</a>
									</div>';
						}
					}
				}
			?>
			<div id="infos-img"><div>&nbsp;</div></div>
		</div>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
