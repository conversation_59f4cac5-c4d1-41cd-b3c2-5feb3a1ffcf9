<?php

	/**	\file index.php
	 *	Cette page affiche l'ensemble des notifications générées par Yuto
	 *	Elle permet également d'envoyer un message à l'ensemble des utilisateurs.
	 */

	require_once('notifications.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_FDV_NOTIFICATION');

	// Création d'une notification
	if( isset($_REQUEST['new']) ){
		header('Location: edit.php');
		exit;
	}

	$_GET['page'] = !isset($_GET['page']) ? 1: $_GET['page'];
	$author = isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] > 0 ? $_GET['author'] : 0;
	
	$nb_per_page = 25;
	$start = $nb_per_page*($_GET['page']-1);
	$date = array();

	if( isset($_GET['date1']) && isset($_GET['date2']) ){
		$date['start'] = $_GET['date1'];
		$date['end'] = $_GET['date2'];
	}

	$rnotify = nt_notifications_get_by_view( 0, $date, 'desc', $start, $nb_per_page);
	$count_notifications = $rnotify ? $rnotify['total_rows'] : 0;


	// Calcule le nombre de pages
	$pages = ceil($rnotify['total_rows'] / $nb_per_page);

	// Titre de la page
	define('ADMIN_PAGE_TITLE', _('Notifications').' - Yuto');
	require_once('admin/skin/header.inc.php');

	$colspan = $author ? 3 : 4;

	// charge la liste des tablettes active
	$rdev = dev_devices_get(0, 0, '', -1, '=', false, false, true, false, 0, false, true);
	if( !ria_mysql_num_rows($rdev) ){
		$error[] = _('Pour utiliser cette fonctionnalité, vous devez installer Yuto sur un ou plusieurs appareils.');
	}
?>

<h2><?php print (_("Notifications") . ' (' . ria_number_format($count_notifications) .')'); ?></h2>

<?php

// Affiche les messages de succès ou d'erreur
if( isset($error) && sizeof($error) ){
	print '<div class="error">'.nl2br( implode('<br/>', $error) ).'</div>';
}elseif( isset($success) ){
	print '<div class="success">'.$success.'</div>';
}

?>

<form action="index.php" method="post">
	<table id="yuto-notifications" class="checklist ui-sortable large">
		<thead><tr>
			<th id="notifications-id"><?php echo _("Titre"); ?></th>
			<th id="notifications-author"><?php echo _("Auteur"); ?></th>
			<th id="notifications-created"><?php echo _("Créé le"); ?> </th>
			<th id="notifications-state"><?php echo _("Statut"); ?></th>
		</tr></thead>
		<?php

			// Détermine la page en cours de consultation
			$page = 1;
			if( isset($_GET['page']) && is_numeric($_GET['page']) ){
				if( $_GET['page']>0 && $_GET['page']<=$pages )
					$page = $_GET['page'];
			}

			// Détermine les limites inférieures et supérieures pour l'affichage des pages
			$pmin = $page-5;
			if( $pmin<1 )
				$pmin = 1;
			$pmax = $pmin+9;
			if( $pmax>$pages )
				$pmax = $pages;
		?>
		<tbody><?php

			if( !$count_notifications ){
				print '
					<tr><td colspan="'.$colspan.'">' . _('Aucune notification n\'a encore été envoyée').( $author ? _(' par cette personne') : '' ).'.</td></tr>
				';
			}else{
				
				foreach( $rnotify as $notification ){
					if( !isset($notification['_id'])) continue;
				
						print '
						<tr>
							<td headers="notifications-id" data-label="'._('Titre :').' ">
								<a href="/admin/fdv/notifications/edit.php?nt_id='.$notification['_id'].'">'.htmlspecialchars( $notification['nt_title'] ).'</a>
							</td>
							<td headers="notifications-author" data-label="'._('Auteur :').' ">
								'.( $notification['nt_author_id'] > 0 ? '<a href="/admin/customers/edit.php?usr='.$notification['nt_author_id'].'">' : '' ).'
								'.( isset($notification['nt_author_name']) ? htmlspecialchars( trim($notification['nt_author_name']) ) : '' ).'
								'.( $notification['_id'] > 0 ? '</a>' : '' ).'
							</td>
							<td headers="notifications-created" data-label="'._('Créé le :').' ">'.ria_date_format($notification['nt_date_created'], true).'</td>
							<td headers="notifications-state" data-label="'._('Status :').' ">';

							if(! isset($notification['nt_state']) ){
								$notification['nt_state'] = NT_STATE_DRAFT;
							}
							switch ($notification['nt_state']) {
								case NT_STATE_WAITING:
									print _('En cours d\'envoi');
									break;
								case NT_STATE_SEND:
									print _('Envoyé');
									break;
								default:
									print _('Brouillon');
									break;
							}

					print '</td>
						</tr>
					';
				}
			}
		?></tbody>
		<tfoot>
			<?php if( $pages>1 ){ ?>
				<tr id="pagination">
					<td colspan="1" class="page align-left">Page <?php print $page ?>/<?php print $pages; ?></td>
					<td colspan="<?php print $colspan-1; ?>" class="pages">
						<?php
							if( $pages>1 ){
								if( $page>1 )
									print '<a href="index.php?page='.($page-1).'">&laquo; '. _('Page précédente') . '</a> | ';
								for( $i=$pmin; $i<=$pmax; $i++ ){
									if( $i==$page )
										print '<b>'.$page.'</b>';
									else
										print '<a href="index.php?page='.$i.'">'.$i.'</a>';
									if( $i<$pmax )
										print ' | ';
								}
								if( $page<$pages )
									print ' | <a href="index.php?page='.($page+1).'">'. _('Page suivante &raquo;') . '</a>';
							}
						?>
					</td>
				</tr>
			<?php } ?>
			<tr>
				<td colspan="<?php print $colspan; ?>" class="align-right">
					<input type="submit" name="new" id="new" value="<?php print _('Ajouter'); ?>" />
				</td>
			</tr>
		</tfoot>
	</table>

</form>

<script>
<!--
	function load_reports(){
		window.location.href = 'index.php?date1=' + $('[name=date1]').val() + '&author=<?php print $author; ?>&date2=' + $('[name=date2]').val();
	}
	<?php print view_date_initialized( 0, '', false, array('callback'=>'load_reports') ); ?>
-->
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
