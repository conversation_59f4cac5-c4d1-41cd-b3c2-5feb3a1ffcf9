/**
 *	Gestion des rapports émis depuis l'application
 */
$(document).ready(function(){
	loadDatePicker();
	$('.check-all').click(function(){
		$($(this).parent().get(0)).find('input[type=checkbox]').attr('checked','checked');
		return false;
	});
	$('.uncheck-all').click(function(){
		$($(this).parent().get(0)).find('input[type=checkbox]').removeAttr('checked');
		return false;
	});


}).delegate(
	'#select-fdv-report-author .selectorview', 'click', function(){
		if($('#select-fdv-report-author .selector').css('display')=='none'){
			$('#select-fdv-report-author .selector').show();
		}else{
			$('#select-fdv-report-author .selector').hide();
		}
	}
).delegate(
	'#select-fdv-report-author .selector a', 'click', function(){
		var typeID = $('#type-id').val();
		var attrAuthor = $(this).attr('name');
		var date1 = $('#date1').val();
		var date2 = $('#date2').val();
		var author = attrAuthor.replace( 'author-', '' );
		if (date1 != undefined && date2 != undefined) {
			window.location.href = '/admin/fdv/reports/index.php?type=' + typeID + '&author=' + author + '&date1=' + date1 + '&date2=' + date2;
		}else{
			window.location.href = '/admin/fdv/reports/index.php?type=' + typeID + '&author=' + author;
		}
	}
).delegate(
	'#select-fdv-calls-report-author .selectorview', 'click', function () {
		if ($('#select-fdv-calls-report-author .selector').css('display') == 'none') {
			$('#select-fdv-calls-report-author .selector').show();
		} else {
			$('#select-fdv-calls-report-author .selector').hide();
		}
	}
).delegate(
	'#select-fdv-calls-report-author .selector a', 'click', function () {
		var attrAuthor = $(this).attr('name');
		var date1 = $('#date1').val();
		var date2 = $('#date2').val();
		var author = attrAuthor.replace('author-', '');
		if (date1 != undefined && date2 != undefined) {
			window.location.href = '/admin/fdv/reports/calls/index.php?author=' + author + '&date1=' + date1 + '&date2=' + date2;
		} else {
			window.location.href = '/admin/fdv/reports/calls/index.php?author=' + author;
		}
	}
).delegate(
	'#export-calls', 'click', function() {
		var date1 = $('#date1').val();
		var date2 = $('#date2').val();
		var author = $('#author_id').val();
		displayPopup(fdvDisplayPopupExportRapport, '', '/admin/fdv/reports/calls/popup-export-calls.php?author=' + author + '&date1=' + date1 + '&date2=' + date2);
		return false;
	}
).delegate(
	'#select-fdv-report-visite-author .selectorview', 'click', function () {
		if ($('#select-fdv-report-visite-author .selector').css('display') == 'none') {
			$('#select-fdv-report-visite-author .selector').show();
		} else {
			$('#select-fdv-report-visite-author .selector').hide();
		}
	}
).delegate(
	'#select-fdv-report-visite-author .selector a', 'click', function () {
		var attrAuthor = $(this).attr('name');
		var date1 = $('#date1').val();
		var date2 = $('#date2').val();
		var author = attrAuthor.replace('author-', '');
		if (date1 != undefined && date2 != undefined) {
			window.location.href = '/admin/fdv/reports/types/index.php?author=' + author + '&date1=' + date1 + '&date2=' + date2;
		} else {
			window.location.href = '/admin/fdv/reports/types/index.php?author=' + author;
		}
	}
).delegate(
	'#select-fdv-devices-user .selectorview', 'click', function(){
		if($('#select-fdv-devices-user .selector').css('display')=='none'){
			$('#select-fdv-devices-user .selector').show();
		}else{
			$('#select-fdv-devices-user .selector').hide();
		}
	}
).delegate(
	'#select-fdv-devices-user .selector a', 'click', function(){
		var attrAuthor = $(this).attr('name');
		var author = attrAuthor.replace( 'author-', '' );
		window.location.href = '/admin/fdv/devices/index.php?author=' + author;

	}
).delegate(
	'#exportParc', 'click', function() {
		var author = $('#author_id').val();
		window.open('/admin/fdv/devices/export.php?exportParc=1&author=' + author, '_blank');		
	}
).delegate( // Ouverture de la popup de choix de client
	'#upd-ord-usr', 'click', function() {
		displayPopup(fdvDisplayPopupChoixComptClient, '', '/admin/ajax/orders/ncmd-customers-change.php?callback=getUserReport', '', 800, 600 );
		return false;
	}
).delegate(
	'#select-doublon-devices-user .selectorview', 'click', function(){
		if($('#select-doublon-devices-user .selector').css('display')=='none'){
			$('#select-doublon-devices-user .selector').show();
		}else{
			$('#select-doublon-devices-user .selector').hide();
		}
	}
).delegate(
	'#select-doublon-devices-user .selector a', 'click', function(){
		var attrDoublon = $(this).attr('name');
		window.location.href = '/admin/fdv/devices/index.php?doublon=' +attrDoublon;
	}
).delegate(
	'#select-status-user .selectorview', 'click', function(){
		if($('#select-status-user .selector').css('display')=='none'){
			$('#select-status-user .selector').show();
		}else{
			$('#select-status-user .selector').hide();
		}
	}
).delegate(
	'#select-status-user .selector a', 'click', function(){
		var attrStatus = $(this).attr('name');
		window.location.href = '/admin/fdv/devices/index.php?status=' + attrStatus;
	}
);

$(document).ready(function(){
	var href = 'index.php';
	var usr_id = 0;
	$('#closingpicker .selectorview').click(function(){
		if ($('#closingpicker').data('href') != null) {
			href = $('#closingpicker').data('href');
		}
		if ($('#closingpicker').data('usr-id') != null) {
			usr_id = $('#closingpicker').data('usr-id');
		}
		$('#closingpicker .selector').toggle();
	});
	$("#closingpicker .selector a").click(function(){
		const periode = $(this).attr('name');
		const p = periode.substring(periode.indexOf('-')+1, periode.length);
		window.location.href = href+'?seller_id='+usr_id+'&period='+p+'#form-closing';
	});

	$("#expeditionspicker .selector a").click(function(){
		const periode = $(this).attr('name');
		const p = periode.substring(periode.indexOf('-')+1, periode.length);
		window.location.href = href+'?website='+p;
	});

	if( typeof $("input[name=exp-website]") != 'undefined' ){
		$("input[name=exp-website]").change(function(){
				$("#site-content div.exp-menu").toggle();
		});
	}

	$('#selectseller .selectorview').click(function(){
		if($('#selectseller .selector').css('display')=='none'){
			$('#selectseller .selector').show();
		}else{
			$('#selectseller .selector').hide();
		}
	});		
}).delegate('#selectseller a', 'click', function(){
		const seller_id = 'seller_id='+$(this).attr('name').replace('seller-','');
		window.location = 'index.php?'+seller_id;
	}
).delegate('#only_seller_orders', 'change', function(){
		window.location = 'index.php?'+seller_id;
	}
);


/**	Cette fonction gère le lien "Voir la localisation" qui permet d'afficher sur une carte Google Maps le dernier emplacement connnu d'un appareil sur lequel Yuto est installé.
 *	@param latitude Latitude à laquelle l'appareil est situé
 *	@param longitude Longitude à laquelle l'appareil est situé
 *	@param locRadius Périmètre de localisation. Plus la localisation est précise est plus ce périmètre sera petit.
 */
var showDeviceLocation = function( latitude, longitude, locRadius, last_call ){
	displayPopup(fdvDisplayPopuplocalisation, '<div id="dev-location"></div>', '', '', 90, 90, true );
	
	var myLatlng = new google.maps.LatLng( latitude, longitude );
	var mapOptions = {
		zoom: 15,
		maxZoom: 15,
		center: myLatlng,

		draggable: true,
		mapTypeControl: false,
		panControl: false,
		scaleControl: false,
		streetViewControl: false
	}
	
	var googleMap = new google.maps.Map(document.getElementById('dev-location'), mapOptions);

	var marker = new google.maps.Marker({
		position: myLatlng,
		map: googleMap
	});
	
	// La couleur du marqueur utilisé dépend de la date de dernier contact avec l'appareil
	var aliveDelay = new Date(new Date().getTime());
	var lostDelay = new Date(new Date().getTime() - (6 * 60 * 60 * 1000));
	var deadDelay = new Date(new Date().getTime() - (24 * 60 * 60 * 1000));
	
	var device_last_call = new Date(last_call);
	if( device_last_call<aliveDelay && device_last_call>lostDelay ){
		marker.setIcon('/admin/images/fdv/icon_alive.png');
	}else if( device_last_call<lostDelay && device_last_call>deadDelay ) {
		marker.setIcon('/admin/images/fdv/icon_lost.png');
	}else{
		marker.setIcon('/admin/images/fdv/icon_dead.png');
	}

	if( typeof locRadius && locRadius != undefined ){

		var populationOptions = {
			strokeColor: '#6BAC00',
			strokeOpacity: 0.8,
			strokeWeight: 1,
			fillColor: '#BFE768',
			fillOpacity: 0.35,
			map: googleMap,
			center: new google.maps.LatLng( latitude, longitude ),
			radius: locRadius
		};
		
		const cityCircle = new google.maps.Circle(populationOptions);
		googleMap.fitBounds(cityCircle.getBounds());
	}

    $('#popup_ria').css('overflow', 'hidden');
	return false;
}

/**	Evite un warning avec Google Maps
 */
function initMap(){
	;
}

var confirmDeleteMultiDevice = function(){
	return window.confirm(fdvConfirmValideSuppression);
}

var confirmDeleteDevice = function(){
	return window.confirm(fdvConfirmSuppression);
}

var selectDeviceUser = function(){ // Ouverture de la popup choix de client
	displayPopup(fdvDisplayPopupChoixComptClient, '', '/admin/ajax/orders/ncmd-customers-change.php?callback=getUserReport', '', 800, 600 );
	return false;
}

// Permet de récupéré le choix du compte client
var getUserReport = function( newUserID, email, allname ){
	
	var newHtml = '<img class="sync" src="/admin/images/sync/0.svg?1" title="Ce compte client n\'existe que dans votre boutique en ligne" alt="Ce compte client n\'existe que dans votre boutique en ligne" />'
		+ '<input name="user-id" id="user-id" value="' + newUserID + '" type="hidden">'
		+ allname + ' - <a href="#" onclick="return selectDeviceUser();">Modifier</a>';

	$('#user-id-report').html( newHtml );

	return false;
}

function loadDatePicker(){
	// removeMessages();
	
	// Parcours tous les champs de type date
	$('input.datepicker').each(function(){
		var temp = this ;
		
		// Implémente le sélecteur de date sur chacun d'entre eux.
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					$(temp).DatePickerHide();
				}
				if( $('#date_fin').length > 0 && $(temp).attr('name') == 'date_deb' ){
					$('#date_fin').val( $('#date_deb').val() );
				}
			}
		});
		
	});	
}