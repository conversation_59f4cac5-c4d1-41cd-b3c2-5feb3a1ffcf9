<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_SEGMENT');

	global $config;

	if( !isset($rseg) ){
		exit;
	}
	
	require_once('excel/PHPExcel.php');
	$alphabet = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');
	
	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();

	// Déterminé les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
								 ->setLastModifiedBy("riaStudio")
								 ->setTitle(_("Export des segments ").$cls_name)
								 ->setSubject(_("Export des segments ").$cls_name)
								 ->setDescription(_("Export des segments ").$cls_name)
								 ->setKeywords(_("export segments ").$cls_name)
								 ->setCategory("");
	
	// Création du fichier
	$objWorksheet = $objPHPExcel->getActiveSheet();
	
	// 1° feuille : Contient l'entête des segments
	$objWorksheet->setTitle('Segments '.$cls_name);
	
	$letter = 'A';

	// 1° feuille : Nom des colonnes
	$objWorksheet->setCellValue($letter.'1', _('Segment'));
	$objWorksheet->getColumnDimension($letter++)->setWidth(40);
	
	$objWorksheet->setCellValue($letter.'1', _('Description'));
	$objWorksheet->getColumnDimension($letter++)->setWidth(40);

	$objWorksheet->setCellValue($letter.'1', $cls_name);
	$objWorksheet->getColumnDimension($letter++)->setWidth(10);

	switch( $_GET['cls'] ){
		case CLS_USER: {
			$objWorksheet->setCellValue($letter.'1', _('Commandes par compte client'));
			$objWorksheet->getColumnDimension($letter++)->setWidth(12);

			$objWorksheet->setCellValue($letter.'1', _('Produits par commande'));
			$objWorksheet->getColumnDimension($letter++)->setWidth(12);

			$objWorksheet->setCellValue($letter.'1', _('CA par compte client HT'));
			$objWorksheet->getColumnDimension($letter++)->setWidth(12);

			$objWorksheet->setCellValue($letter.'1', _('CA par compte client TTC'));
			$objWorksheet->getColumnDimension($letter++)->setWidth(12);

			$objWorksheet->setCellValue($letter.'1', _('Marge brute par compte client HT'));
			$objWorksheet->getColumnDimension($letter++)->setWidth(12);

			$objWorksheet->setCellValue($letter.'1', _('CA total HT'));
			$objWorksheet->getColumnDimension($letter++)->setWidth(12);

			$objWorksheet->setCellValue($letter.'1', _('CA total TTC'));
			$objWorksheet->getColumnDimension($letter++)->setWidth(12);
		}
	}

	$letter = chr(ord($letter) - 1);
	
	// Police sur l'entête des colonnes
	$objWorksheet->getStyle('A1:'.$letter.'1')->getFont()->setBold(true);
	$objWorksheet->getStyle('A1:'.$letter.'1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet->getStyle('A1:'.$letter.'1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
	$objWorksheet->getStyle('A1:'.$letter.'1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objWorksheet->getStyle('A1:'.$letter.'1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
	$objWorksheet->getStyle('A1:'.$letter.'1')->getAlignment()->setWrapText(true);
	$objWorksheet->freezePane('B2');
	
	
	
	// Enregistre les entêtes de commandes
	$line = 2; 
	while( $seg = ria_mysql_fetch_array($rseg) ){
		$letter = 'A';
		$objWorksheet->setCellValue($letter++.$line, $seg['name'] );
		$objWorksheet->setCellValue($letter++.$line, $seg['desc'] );
		$objWorksheet->setCellValue($letter++.$line, $seg['objects'] );
		

		switch( $_GET['cls'] ){
			case CLS_USER: {
				if( $seg['objects'] > 0 ){
					$ar_user_ids = gu_users_get_by_segment( $seg['id'] );
					
					if( is_array($ar_user_ids) && sizeof($ar_user_ids) ){
						$stat = array('count' => 0, 'total_ht' => 0, 'total_ttc' => 0, 'marge' => 0, 'nb_products' => 0 );
						
						$rstat = ord_orders_totals_get( ord_states_get_ord_valid(), $ar_user_ids, 0, 0, true );
						if( $rstat && ria_mysql_num_rows($rstat) ){
							$stat = ria_mysql_fetch_assoc( $rstat );
							$stat['nb_products'] = 0;

							$prd_stats = ord_products_totals_get( $ar_user_ids );
							if( $prd_stats && ria_mysql_num_rows($prd_stats) ){
								$prd_stats = ria_mysql_fetch_assoc( $prd_stats );

								$stat['nb_products'] = $prd_stats['count_prd'];
							}
						}

						$objWorksheet->setCellValue($letter++.$line, number_format( $stat['count'] / $seg['objects'], 2, ',', ' ' ) );
						$objWorksheet->setCellValue($letter++.$line, number_format( ($stat['count'] ? $stat['nb_products'] / $stat['count'] : 0), 2, ',', ' ' ) );
						$objWorksheet->setCellValue($letter++.$line, number_format( $stat['total_ht'] / $seg['objects'], 2, ',', ' ' ) );
						$objWorksheet->setCellValue($letter++.$line, number_format( $stat['total_ttc'] / $seg['objects'], 2, ',', ' ' ) );
						$objWorksheet->setCellValue($letter++.$line, number_format( $stat['marge'] / $seg['objects'], 2, ',', ' ' ) );
						$objWorksheet->setCellValue($letter++.$line, number_format( $stat['total_ht'], 2, ',', ' ' ) );
						$objWorksheet->setCellValue($letter++.$line, number_format( $stat['total_ttc'], 2, ',', ' ' ) );
					}
				}

				break;
			}
			case CLS_STORE: {

			}
		}
		$line++;
	}
	$letter = chr(ord($letter) - 1);

	$line--;

	// Gestion des alignements
	$objWorksheet->getStyle('A2:'.$letter.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
	$objWorksheet->getStyle('A2:B'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
	$objWorksheet->getStyle('C2:'.$letter.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
		
	// Bordure
	$objWorksheet->getStyle('A1:'.$letter.$line)->getBorders()->applyFromArray(
		array(
			'allborders' => array(
				'style' => PHPExcel_Style_Border::BORDER_THIN,
			)
		)
	);

	$objPHPExcel->setActiveSheetIndex(0);

	// Redirect output to a clientâ??s web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="segments-'.$cls_name.'-'.date('Y-m-d').'.xls"');
	header('Cache-Control: max-age=0');

	// Ecrit le fichier et le sauvegarde
	$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
	$objWriter->save('php://output');
	exit;


