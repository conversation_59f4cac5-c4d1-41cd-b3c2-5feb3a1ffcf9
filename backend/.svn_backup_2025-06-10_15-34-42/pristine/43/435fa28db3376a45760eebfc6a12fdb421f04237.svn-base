<?php

	/**	\file index.php
	 *	Cet écran affiche la liste des catégories de champs avancés
	 *	Sur cette liste, les fonctionnalités suivantes sont disponibles :
	 *	- Ajout d'une catégorie
	 *	- Modification de l'ordre d'affichage
	 *	- Suppression
	 */

	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_CATEG');

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	unset($error);

	// Déplacement vers le haut
	if( isset($_GET['up']) ){
		fld_categories_move_up( $_GET['up'] );
		header('Location: index.php');
		exit;
	}

	// Déplacement vers le bas
	if( isset($_GET['dw']) ){
		fld_categories_move_down( $_GET['dw'] );
		header('Location: index.php');
		exit;
	}

	// Suppression dans la liste
	if( isset($_POST['del']) && isset($_POST['cat']) ){
		foreach( $_POST['cat'] as $z )
			if( !fld_categories_del($z) ){
				$count = fld_categories_get_fields_count($z);
				if( $count ){
					$error = str_replace("#param[nom_categorie]#", fld_categories_get_name($z), str_replace("#param[nb_champs]#", $count, _("La catégorie #param[nom_categorie]# contient #param[nb_champs]# champ(s).\nVeuillez déplacer ou supprimer les champs qu'elle contient avant de la supprimer."))) ;
				}else{
					$error = _("Une erreur inattendue s'est produite lors de la suppression d'un des champs.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}
				break;
			}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	$can_move = gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CATEG_MOVE');
	$colspan = $can_move ? 3 : 2;

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Catégories de champs') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	$categories = fld_categories_get( 0, false, null );
	$count = ria_mysql_num_rows( $categories );

?>
	<h2><?php echo _('Catégories de champs'); ?> (<?php print ria_number_format($count) ?>)</h2>

	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<form action="index.php" method="post">
	<table id="categories" class="checklist">
		<thead>
			<tr>
				<th id="select"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<th id="name"><?php echo _("Nom de la catégorie"); ?></th>
				<?php if( $can_move ){ ?>
				<th id="pos"><?php echo _("Déplacer"); ?></th>
				<?php } ?>
			</tr>
		</thead>
		<tbody>
			<?php
				$categories_count = 0;
				$classes = fld_classes_get( 0, false, true, true, null, true, true );
				if( $classes!==false && ria_mysql_num_rows($classes) ){
					while( $cls = ria_mysql_fetch_array($classes) ){

						if( $cls['categories']>0 ){ // La classe n'apparaît que si des catégories sont présentes
							print '<tr><th colspan="'.$colspan.'">'.htmlspecialchars($cls['name']).' ('.ria_number_format($cls['categories']).')</th></tr>';

							$categories = fld_categories_get( 0, false, $cls['id'] );
							$count = ria_mysql_num_rows($categories);

							while( $r = ria_mysql_fetch_array($categories) ){
								$count = $count - 1;
								$categories_count++;
								print '<tr id="line-' . $r['id'] . '" class="ria-row-orderable cls-' . $cls['id'] . '">
									<td headers="select"><input type="checkbox" class="checkbox" name="cat[]" value="'.$r['id'].'" /></td>
									<td headers="name">';
								if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CATEG_EDIT') ){
									print '<a href="edit.php?cat='.$r['id'].'">'.htmlspecialchars($r['name']).'</a>';
								}else{
									print htmlspecialchars($r['name']);
								}
								print '</td>';
								if( $can_move ){
									print '<td headers="pos" class="align-center ria-cell-move">';
									print '	<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
									print '</td>';
								}
								print '</tr>';
							}
						}
					}
				}
				if( !$categories_count ){
					print '<tr><td colspan="'.$colspan.'">'._('Aucune catégorie de champs personnalisés.').'</td></tr>';
				}
			?>
		</tbody>
		<tfoot>
			<tr><td colspan="<?php print $colspan; ?>">
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CATEG_DEL') && $categories_count>0 ){ ?>
				<input type="submit" name="del" class="btn-del float-left" value="<?php print _('Supprimer'); ?>" title="<?php echo _("Supprimer les catégories sélectionnées"); ?>" onclick="return fldCatConfirmDelList()" />
				<?php } ?>
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CATEG_ADD') ){ ?>
				<input type="submit" name="add" class="btn-add" value="<?php print _('Ajouter'); ?>" title="<?php echo _("Ajouter une catégorie"); ?>" onclick="return fldCatAdd()" />
				<?php } ?>
			</td></tr>
		</tfoot>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>