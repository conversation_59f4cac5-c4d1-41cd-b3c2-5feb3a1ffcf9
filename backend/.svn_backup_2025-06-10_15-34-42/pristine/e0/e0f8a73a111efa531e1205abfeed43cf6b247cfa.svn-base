<?php
/**	\defgroup https_transition Transition https
 * 	\ingroup system
 * 	@deprecated Ces fonctions ont été utilisées de façon transitoire pour réaliser des migrations http vers https et ne sont plus utilisées
 * 	à ce jour.
 *	@{
 */


/**
 * Permet la récupération des objets avec un contenu avec un lien vers le site en http
 *
 * @param Helpers::Https::HttpObject $object
 * @return resource Résultat mysql avec les colonnes d'identification de l'objet et les colonnes de contenu
 */
function query_objects_with_http_urls_in_content(Helpers\Https\HttpObject $object)
{
	global $config;

	$cols = array_map(function($item) use ($config) {
		$sql  = '('.$item . ' like "%'.str_replace('https://','http://', $config['site_url']).'%"';
		$sql .= ' or '.$item.' like "%src=\"http://%")';
		return $sql;
	},$object->httpCols());

	$sql = '
		select '.implode(', ',$object->objColIds()).', '.implode(', ',$object->httpCols()).'
		from '.$object->table().'
		where '.$object->tntCol().'='.$config['tnt_id'].'
		and ('.implode(' or ', $cols).')
	';

	return ria_mysql_query($sql);
}

/**
 * Permet de remplacé le contenu des object d'une class voulue.
 *
 * @param Helpers::Https::HttpObject $object
 * @return true
 */
function replace_http_urls_in_objects_content(Helpers\Https\HttpObject $object)
{
	global $config;

	$res = query_objects_with_http_urls_in_content($object);

	if (!$res || !ria_mysql_num_rows($res)) {
		return true;
	}
	$site_ssl = str_replace('http://','https://', $config['site_url']);
	$site_no_ssl = str_replace('https://','http://', $config['site_url']);

	while ($content = ria_mysql_fetch_assoc($res)) {

		$ids = array_map(function($item) use ($content){
			return $item . ' = '.$content[$item];
		},$object->objColIds());

		$cols = array_map(function($item) use ($site_no_ssl, $site_ssl, $content){
			return $item . ' = "'.addslashes(str_replace(array($site_no_ssl, 'src="http://'), array($site_ssl, 'src="https://'), $content[$item])).'"';
		},$object->httpCols());

		$sql = '
			update '.$object->table().'
			set '.implode(', ', $cols).'
			where '.$object->tntCol().'='.$config['tnt_id'].'
			and '.implode(' and ', $ids).'
		';

		ria_mysql_query($sql);
	}

	return true;
}
// @}