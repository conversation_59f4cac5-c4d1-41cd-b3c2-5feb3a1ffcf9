<?php
// \cond onlyria

/**	\brief Cette classe encapsule la table des mots clés de l'index du moteur de recherche.
 *
 *	Par rapport à la version procédurale, elle apporte une mise en cache dans une mémoire partagée (memcached),
 *	et ne nécessite pas un chargement complet de l'index des mots clés en mémoire pour fonctionner.
 *	Seul les mots clés utilisés sont chargés en mémoire et seronts éliminés automatiquement par memcached en fonction
 *	de la mémoire qui lui est attribuée.
 *	\ingroup searchengine
 */
class SearchKeywords {

	/** Permet l'ajout d'un mot clé à la base de données.
	 *	Il est très important que le mot clé n'existe pas déjé dans la base de données,
	 *	car cette fonction ne vérifie pas son existance.
	 *
	 *	@param string $keyword Obligatoire, Mot clé à ajouter
	 *
	 *	@return int|bool Identifiant attribué au mot clé, ou false en cas d'erreur.
	 *
	 */
	public static function addKeyword( $keyword ){
		global $memcached, $config;

		$r = ria_mysql_query("
			insert into search_keywords (kwd_name) values ('".addslashes($keyword)."')
		");

		if( !$r ){
			return SearchKeywords::getKeywordId( $keyword );
		}

		$kid = ria_mysql_insert_id();
		$memcached->set( 'search:'.$config['tnt_id'].':keyword:'.$keyword, $kid );

		return $kid;
	}

	/**	Retourne l'identifiant d'un mot clé, à partir de son libellé.
	 *	@param string $keyword Mot clé à rechercher dans l'index, en majuscule et sans accents
	 *	@return int l'identifiant du mot clé dans l'index, ou false si le mot clé ne fait pas partie de l'index
	 */
	public static function getKeywordId( $keyword ){
		global $memcached, $config;

		$kid = $memcached->get( 'search:'.$config['tnt_id'].':keyword:'.$keyword );
		if( $kid!==false ){
			return $kid;
		}

		$r_keyword = ria_mysql_query("select kwd_id from search_keywords where kwd_name like '".addslashes( $keyword )."'");
		if( ria_mysql_num_rows($r_keyword) ){
			if( $r = ria_mysql_fetch_array($r_keyword) ){
				$memcached->set( 'search:'.$config['tnt_id'].':keyword:'.$keyword, $r['kwd_id'] );
				return $r['kwd_id'];
			}
		}

		return false;
	}

	/**	Retourne les identifiants d'un mot clé ou de ses variations, à partir de son libellé.
	 *	@param $keyword Obligatoire, Mot clé à rechercher dans l'index, en majuscule et sans accents
	 *	@param $exact Optionnel, si true, le mot-clé doit être exact (sinon, il s'agit d'un like % à droite)
	 *	@param $limit Optionnel, permet de limiter le nombre de mot clé retourné
	 *	@return les identifiants de mots clés trouvés dans l'index, ou false si le mot clé ne fait pas partie de l'index
	 */
	public static function getKeywordIds( $keyword, $exact=false, $limit=0 ){

		$ids = array();

		if( $exact === true ){
			return self::getKeywordId( $keyword );
		}else{
			// Le mot clé exacte est toujours inclu
			$temp = self::getKeywordId( $keyword );
			if( is_numeric($temp) && $temp > 0 ){
				$ids[] = $temp;
			}

			// Initialise la recherche des mots clé
			$sql = '
				select kwd_id
				from search_keywords
				where kwd_name like "'.( $exact === 'contains' ? '%' : '' ).addslashes( $keyword ).($exact === 'exact' ? '"' : '%"').'
			';

			// Applique la limite si celle-ci est demandé
			if( is_numeric($limit) && $limit > 0 ){
				$sql .= ' limit 0, '.$limit;
			}

			$r_keywords = ria_mysql_query( $sql );
			while( $r = ria_mysql_fetch_array($r_keywords) ){
				$ids[] = $r['kwd_id'];
			}
		}

		// Retourne un tableau d'identifiant de mot clé unique
		return array_unique( $ids );
	}

	/**	Cette fonction nettoie l'index des mots clés qui ne sont plus utilisés.
	 *
	 *	@return bool true en cas de succès
	 *	@return bool false en cas d'échec
	 *
	 */
	public static function delUnused(){
		global $memcached, $config;

		$unused_keywords = ria_mysql_query('
			select kwd_id as id, kwd_name as name
			from search_keywords left join search_contains on (kwd_id=cnt_kwd_id) where cnt_kwd_id is null
		');
		while( $r = ria_mysql_fetch_array($unused_keywords) ){
			$memcached->del( 'search:'.$config['tnt_id'].':keyword:'.$r['name'] );
			ria_mysql_query('delete from search_keywords where kwd_id='.$r['id']);
		}

		return true;
	}

	/**	Compte le nombre de mots clés contenus dans l'index.
	 *	@return int le nombre de mots clés de l'index
	 *	@return bool false en cas d'échec
	 */
	public static function count(){
		return ria_mysql_result(ria_mysql_query('select count(*) from search_keywords'),0,0);
	}

}
// \endcond