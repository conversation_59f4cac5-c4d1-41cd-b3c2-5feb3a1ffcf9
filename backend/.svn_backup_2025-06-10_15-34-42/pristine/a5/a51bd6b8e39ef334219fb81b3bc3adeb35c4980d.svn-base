<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/pubsub.proto

namespace Google\Cloud\PubSub\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Request for the UpdateSubscription method.
 *
 * Generated from protobuf message <code>google.pubsub.v1.UpdateSubscriptionRequest</code>
 */
class UpdateSubscriptionRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. The updated subscription object.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Subscription subscription = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    private $subscription = null;
    /**
     * Required. Indicates which fields in the provided subscription to update.
     * Must be specified and non-empty.
     *
     * Generated from protobuf field <code>.google.protobuf.FieldMask update_mask = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    private $update_mask = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Cloud\PubSub\V1\Subscription $subscription
     *           Required. The updated subscription object.
     *     @type \Google\Protobuf\FieldMask $update_mask
     *           Required. Indicates which fields in the provided subscription to update.
     *           Must be specified and non-empty.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Pubsub\V1\Pubsub::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. The updated subscription object.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Subscription subscription = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return \Google\Cloud\PubSub\V1\Subscription|null
     */
    public function getSubscription()
    {
        return isset($this->subscription) ? $this->subscription : null;
    }

    public function hasSubscription()
    {
        return isset($this->subscription);
    }

    public function clearSubscription()
    {
        unset($this->subscription);
    }

    /**
     * Required. The updated subscription object.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Subscription subscription = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param \Google\Cloud\PubSub\V1\Subscription $var
     * @return $this
     */
    public function setSubscription($var)
    {
        GPBUtil::checkMessage($var, \Google\Cloud\PubSub\V1\Subscription::class);
        $this->subscription = $var;

        return $this;
    }

    /**
     * Required. Indicates which fields in the provided subscription to update.
     * Must be specified and non-empty.
     *
     * Generated from protobuf field <code>.google.protobuf.FieldMask update_mask = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return \Google\Protobuf\FieldMask|null
     */
    public function getUpdateMask()
    {
        return isset($this->update_mask) ? $this->update_mask : null;
    }

    public function hasUpdateMask()
    {
        return isset($this->update_mask);
    }

    public function clearUpdateMask()
    {
        unset($this->update_mask);
    }

    /**
     * Required. Indicates which fields in the provided subscription to update.
     * Must be specified and non-empty.
     *
     * Generated from protobuf field <code>.google.protobuf.FieldMask update_mask = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param \Google\Protobuf\FieldMask $var
     * @return $this
     */
    public function setUpdateMask($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\FieldMask::class);
        $this->update_mask = $var;

        return $this;
    }

}

