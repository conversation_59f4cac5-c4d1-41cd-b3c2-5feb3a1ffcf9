<?php

// \cond onlyria

/**
 * \defgroup api-sync-salesforce_img_send Export des images
 * \ingroup sync
 * @{
 * \page api-sync-salesforce_img_send-add Ajout
 *
 * Cette fonction permet l'ajout des images
 *
 *		\code
 *			PUT /sync/salesforce_img_send/
 *		\endcode
 *
 * @param $images_objects Obligatoire, Identifiant du relevé linéaire à envoyer
 *
*/

use Riashop\Salesforce\Tasks\Images\ImagesObjects;
require_once( 'salesforce.inc.php' );
require_once ('salesforce/soapclient/SforceHeaderOptions.php');
require_once ('salesforce/soapclient/SforceEnterpriseClient.php');

cfg_images_load($config);

switch( $method ){
	case 'add':

		if( !isset($_REQUEST['images_objects']) ){
			throw new Exception("Il manque le paramètre objet image.");
		}

		try {
			sf_login('write');
			$ImagesObjects = new ImagesObjects($config['sf_connexion']);
			$result = $ImagesObjects->add($_REQUEST['images_objects']);
			sf_logout();
		} catch (Exception $e) {
			mail('<EMAIL>', 'err SF '.$config['tnt_id'], $e->getMessage());
			throw new Exception("Erreur de ImagesObjects ".$_REQUEST['id']." : ".$e->getMessage());
		}

		break;
}

///@}

// \endcond