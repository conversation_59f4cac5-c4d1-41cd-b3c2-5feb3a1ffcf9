<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/eval.proto

namespace Google\Api\Expr\V1beta1\EvalState;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A single evaluation result.
 *
 * Generated from protobuf message <code>google.api.expr.v1beta1.EvalState.Result</code>
 */
class Result extends \Google\Protobuf\Internal\Message
{
    /**
     * The expression this result is for.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.IdRef expr = 1;</code>
     */
    private $expr = null;
    /**
     * The index in `values` of the resulting value.
     *
     * Generated from protobuf field <code>int32 value = 2;</code>
     */
    private $value = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Api\Expr\V1beta1\IdRef $expr
     *           The expression this result is for.
     *     @type int $value
     *           The index in `values` of the resulting value.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Expr\V1Beta1\PBEval::initOnce();
        parent::__construct($data);
    }

    /**
     * The expression this result is for.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.IdRef expr = 1;</code>
     * @return \Google\Api\Expr\V1beta1\IdRef
     */
    public function getExpr()
    {
        return $this->expr;
    }

    /**
     * The expression this result is for.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.IdRef expr = 1;</code>
     * @param \Google\Api\Expr\V1beta1\IdRef $var
     * @return $this
     */
    public function setExpr($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Expr\V1beta1\IdRef::class);
        $this->expr = $var;

        return $this;
    }

    /**
     * The index in `values` of the resulting value.
     *
     * Generated from protobuf field <code>int32 value = 2;</code>
     * @return int
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * The index in `values` of the resulting value.
     *
     * Generated from protobuf field <code>int32 value = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkInt32($var);
        $this->value = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Result::class, \Google\Api\Expr\V1beta1\EvalState_Result::class);

