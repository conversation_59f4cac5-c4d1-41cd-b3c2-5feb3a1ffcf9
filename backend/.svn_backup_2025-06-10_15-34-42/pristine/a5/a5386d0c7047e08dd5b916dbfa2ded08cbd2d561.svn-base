<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  37060 => 'Tele 2',
  37061 => 'Omnitel',
  37062 => 'Omnitel',
  370640 => 'BITĖ',
  370641 => 'BITĖ',
  370642 => 'BITĖ',
  370643 => 'BITĖ',
  370644 => 'BITĖ',
  370645 => 'Tele 2',
  370646 => 'Tele 2',
  370647 => 'Tele 2',
  370648 => 'Tele 2',
  370649 => 'BITĖ',
  37065 => 'BITĖ',
  370660 => 'BITĖ',
  370662 => 'Omnitel',
  37067 => 'Tele 2',
  370680 => 'Omnitel',
  370681 => 'BITĖ',
  370682 => 'Omnitel',
  370683 => 'Tele 2',
  370684 => 'Tele 2',
  370685 => 'BITĖ',
  370686 => 'Omnitel',
  370687 => 'Omnitel',
  370688 => 'Omnitel',
  370689 => 'BITĖ',
  370690 => 'BITĖ',
  370692 => 'Omnitel',
  370693 => 'Omnitel',
  370694 => 'Omnitel',
  370695 => 'Omnitel',
  370696 => 'Omnitel',
  370697 => 'Omnitel',
  370698 => 'Omnitel',
  370699 => 'BITĖ',
);
