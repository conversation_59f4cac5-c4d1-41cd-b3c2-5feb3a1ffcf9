<?php
/**
 * \defgroup facture_complete Facture complète
 * \ingroup facture_client
 * @{
*/

// \cond onlyria
function update_invoices($raw_data){
	global $method, $config;
	$obj = json_decode($raw_data);
	$obj = json_decode(json_encode($obj), true);

	if( !isset($obj['head']) || !is_array($obj['head'])
		|| !isset($obj['lines']) || !is_array($obj['lines'])
		){
		throw new Exception("Paramètres invalide");
	}

	$head = $obj['head'];
	$lines = $obj['lines'];

	$serials = isset($obj['serials']) && is_array($obj['serials']) ? $obj['serials'] : false;

	// controles des paramètres pour le invoices
	if( !isset($head['usr'],$head['date'],$head['piece'],$head['ref']) ){
		throw new Exception("Paramètres facture invalide");
	}

	// la synchro va parfois envoyer le real_usr en plus de l'autre, cas des écritures des commandes sous le meme code client
	if( isset($head['real_usr']) && $head['real_usr'] > 0 ){
		$head['usr'] = $head['real_usr'];
	}


	// controles des paramètres pour les lignes
	foreach( $lines as $line ){
		if( !isset($line['prd'],$line['line'],$line['ref'],$line['name'],$line['qte'],$line['price_ht'],$line['tva'],$line['ord']) ){
			throw new Exception('Paramètres lignes invalide');
		}
	}

	// création / mise à jour de l'entete de invoices
	if( $method == "add" ){

		// on check que la facture n'existerais pas deja dnas la DB avec ce numéro de pièce , si c'est le cas on ne le récré pas !
		$rinv = ord_invoices_get( 0, 0, 0, false, false, false, $head['piece'] );
		if( $rinv && ria_mysql_num_rows($rinv) ){
			$inv = ria_mysql_fetch_assoc($rinv);
			$head_id = $inv['id'];
		}else{
			$head_id = ord_invoices_add_sage($head['usr'],$head['piece'],$head['ref'],$head['date']);

			if( !$head_id ){
				throw new Exception("Une erreur est survenue lors de la création de l'entete de la facture");
			}
		}
	}else{
		$head_id = $head['id'];
	}

	// mise à jour de la référence du invoices
	if( !ord_invoices_update_ref($head_id, $head['ref']) ){
		throw new Exception("Erreur dans la mise à jour de la référence de la facture");
	}

	// mise à jour de la date du invoices
	if( !ord_invoices_update_date($head_id, $head['date']) ){
		throw new Exception("Erreur dans la mise à jour de la date de la facture");
	}

	// mise à jour de l'utilisateur
	if( $head['usr'] > 0 ){
		if( !ord_invoices_update_user($head_id, $head['usr']) ){
			throw new Exception("Erreur dans la mise à jour de l'utilisateur de la facture");
		}
	}

	// Ajout / mise à jour de la date d'échéance
	if( isset($head['date_deadline']) ){
		if( !ord_invoices_update_date_deadline($head_id, $head['date_deadline']) ){
			throw new Exception("Erreur dans la mise à jour de la date d'échéance de la facture");
		}
	}

	// Mise à jour de la remise pieds de page d'une facture
	if( isset($head['discount']) ){
		$discount = 0;
		if( is_numeric($head['discount']) && $head['discount'] > 0 ){
			$discount = $head['discount'];
		}

		ord_invoices_set_discount( $head_id, $discount );
	}

	// controles de lignes
	$lines_in = array();
	foreach( $lines as $line ){
		$lines_in[] = $line['prd'].'-'.$line['line'];

		$price_ttc = false;
		if( isset($line['price_ttc']) && is_numeric($line['price_ttc']) ){
			$price_ttc = $line['price_ttc'];
		}

		$ecotaxe = 0;
		if( isset($line['ecotaxe']) && is_numeric($line['ecotaxe']) ){
			$ecotaxe = $line['ecotaxe'];
		}

		$purchase_avg = false;
		if( isset($line['purchase_avg']) && is_numeric($line['purchase_avg']) ){
			$purchase_avg = $line['purchase_avg'];
		}

		$rprd = ord_inv_products_get($head_id, false, false, null, null, null, null, $line['prd'], $line['line']);
		if( $rprd && ria_mysql_num_rows($rprd) ){
			$res = ord_inv_products_update_sage( $head_id,$line['prd'],$line['line'],$line['ref'],$line['name'],$line['qte'],$line['price_ht'],$line['tva'],$line['ord'],$price_ttc,$ecotaxe,$purchase_avg,false);
		}else{
			$res = ord_inv_products_add_sage( $head_id,$line['prd'],$line['line'],$line['ref'],$line['name'],$line['price_ht'],$line['qte'],$line['tva'],$line['ord'],$price_ttc,$ecotaxe,$purchase_avg,false);
		}

		if( $res && isset($line['bl']) && is_numeric($line['bl']) && $line['bl']>0 ){
			ord_inv_products_set_bl_id($head_id, $line['prd'], $line['line'], $line['bl']);
		}

		if( !$res ){
			throw new Exception("Erreur lors de l'enregistrement de la ligne produit : ".$line['prd']);
		}

		// insertion du group_id
		if(isset($line['group_id'])){
			$group_parent_id = false;
			if( isset($line['group_parent_id']) && is_numeric($line['group_parent_id']) ){
				$group_parent_id = $line['group_parent_id'];
			}
			if(!ord_inv_products_set_group_id($head_id,  $line['prd'], $line['line'], $line['group_id'], $group_parent_id)){
				throw new Exception("Erreur lors de l'enregistrement de la ligne produit : ".$line['prd']);
			}
		}

		// insertion du seller_id
		if (isset($line['seller_id'])){
			ord_inv_products_set_seller($head_id, $line['prd'], $line['line'], $line['seller_id']);
		}

		// ajout des champs avancés
		if(isset($line['fields'])) {
			$fields_delete_missing = (isset($line['fields_delete_missing']) && !$line['fields_delete_missing']) ? false : true;
			fields_sync(CLS_INV_PRODUCT, $head_id, $line['prd'], $line['line'], $line['fields'], $fields_delete_missing);
		}
	}

	// retire les lignes produits absente du invoices
	$rprd = ord_inv_products_get($head_id);
	while( $prd = ria_mysql_fetch_assoc($rprd) ){
		$key = $prd['id'].'-'.$prd['line'];
		if( !in_array($key, $lines_in) ){
			if( !ord_inv_products_del($head_id,$prd['id'],$prd['line']) ){
				throw new Exception("Erreur lors de la suppression du produit : ".$prd['id']);
			}
		}
	}

	if(isset($head['fields'])) {
		$fields_delete_missing = (isset($head['fields_delete_missing']) && !$head['fields_delete_missing']) ? false : true;
		fields_sync(CLS_INVOICE, $head_id, 0, 0, $head['fields'], $fields_delete_missing);
	}

	// ajout des modeles de saisie
	if(isset($head['models'])) {
		$models_delete_missing = (isset($head['models_delete_missing']) && !$head['models_delete_missing']) ? false : true;
		models_sync(CLS_INVOICE, $head_id, 0, 0, $head['models'], $models_delete_missing);
	}

	ord_invoices_update_totals($head_id);

	// ajout de les serials
	if ($serials) {
		serials_sync(CLS_INV_PRODUCT, $head_id, false, false, $serials);
	}

	// démaske la facture
	ord_invoices_unmask($head_id);

	return $head_id;
}
// \endcond

switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-invoices-complete-get Chargement
	 *
	 * Cette fonction permet de récupérer une facture complète d'un client
	 *
	 *	\code
	 *		GET /invoices/complete/
	 *	\endcode
	 *
	 * @param int id : identifiant de la facture
	 *
 	 * @return Json Les données à synchroniser pour la classe.
 	 *		Ces données sont données sous forme d'un tableau (une ligne par élément), chaque élément contient les clés suivantes :
 	 *			- xxx : liste de colonne de l'objet en fonction de la classe
	 *			- related : liste des datas liées
	 *
	 *	\code{.json}
	 *			"related":
	 *			{
     *        		"products": []
	 *		    },
	 *			{
	 *				"id" : identifiant de la facture
	 *				"usr_id" : identifiant du compte utilisateur
	 *				"year" : Année de la facture
	 *				"total_ht" : montant total hors taxe de la facture
	 *				"total_ttc" : montant total ttc de la facture
	 *				"piece" : code de la pièce sage correspondante
	 *				"ref" : référence externe de la facture
	 *				"date" : date de la facture au format jj/mm/aaaa à hh:mm
	 *				"datenotime" : date de la facture au format jj/mm/aaaa
	 *				"date_en" : date de la facture au format anglais
	 *				"age" : nombre de jours depuis la création de la facture
	 *				"discount" : pourcentage d'escompte sur la facture (ce pourcentage est inclut dans les montants HT et TTC)
	 *				"date_modified" : date de dernière modification
	 *				"date_modified_en" : date de dernière modification au format EN
	 *				"masked" : détermine si la facture est masquée
	 *				"date_deadline" : date d'échéance de la facture
	 *				"date_deadline_en" : date d'échéance de la facture au format EN
	 *       	}
	 *	\endcode
	 * @}
	*/
	case 'get' :
		$rinv = ord_invoices_get($_REQUEST['id']);

		if( $rinv && ria_mysql_num_rows($rinv) ) {
			$content = array();
			$cpt = 0;
			while ($inv = ria_mysql_fetch_assoc($rinv)) {
				if( ++$cpt > 500 ) break; // limite la récupération des données

				$content[] = dev_devices_get_object_simplified(CLS_INVOICE, array($inv['id']));
			}
		}
		$result = true;
		break;

	/** @{
	 * \page api-invoices-complete-add Ajout
	 *
	 * cette fonction permet l'ajout d'une nouvelle facture
	 *
	 *		\code
	 *			POST /invoices/complete/
	 *		\endcode
	 *
	 * @param raw_data Obligatoire, Donnée en json_decode
	 *	\code{.json}
	 *     {
	 *				"head" Obligatoire : Entête de la facture
	 *					"usr" 						Obligatoire	: Identifiant de l'utilisateur
	 *					"date"						Obligatoire	: Date
	 *					"piece"						Obligatoire	: N° de pièce
	 *					"ref"							Obligatoire	: Référence
	 *					"date_deadline"		Facultatif 	: Date d'échéance
	 *					"discount"				Optionnel : Remise en pourcentage de pieds de page
	 *				"lines" Obligatoire : Tableau de lignes de la facture
	 *					"prd" : Identifiant du produit
	 *					"line" : N° de line
	 *					"qte" : Quantité
	 *					"ref" : Référence du produit
	 *					"name" : Nom du produit
	 *					"price_ht" : Prix HT unitaire
	 *					"tva" : tva en coeficient
	 *					"price_ttc" : prix TTC unitaire
	 *					"ord" : identifiant de la commande d'origine
	 *					"ecotaxe" : Ecotaxe
	 *					"purchase_avg" : prix d'achat unitaire de la ligne
	 *					"fields_delete_missing"	Facultatif : Permet de ne pas supprimer les champs non données par le bundle
	 *					"fields"	Facultatif : Tableau de champ avancé
	 *						"fld_id"	Obligatoire	: Identifiant du champ avancé
	 *						"value"		Obligatoire	: Valeur
	 *				"fields_delete_missing"	Facultatif : Permet de ne pas supprimer les champs non données par le bundle
	 *				"fields" Facultatif : Tableau de champ avancé de la facture
	 *					"fld_id"	Obligatoire	: Identifiant du champ avancé
	 *					"value"		Obligatoire	: Valeur
	 *     }
	 *	\endcode

	 *
	 *	 @return Identifiant de la facture
	 *	@}
	*/
	case 'add':
		$inv_id = update_invoices($raw_data);
		$result = true;
		$content = array('inv_id' => $inv_id);
		break;

	/** @{
	 * \page api-invoices-complete-upd Mise à jour
	 *
	 * cette fonction permet la mise à jour d'une nouvelle facture complète
	 *
	 *		\code
	 *			PUT /invoices/complete/
	 *		\endcode
	 *
	 * @see Ajout
	 *
	 * @return bool true si la facture est modifiée sans erreur
	 *
	 *	@}
	*/
	case 'upd':
		$inv_id = update_invoices($raw_data);
		$result = true;
		break;
}

///@}