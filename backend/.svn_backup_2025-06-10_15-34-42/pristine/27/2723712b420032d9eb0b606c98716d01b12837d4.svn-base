<?php

set_include_path(dirname(__FILE__) . '/../include/');
	require_once(str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php');
	require_once('users.inc.php');

	$rusr = gu_users_get();
	if( $rusr && ria_mysql_num_rows($rusr) ){
		while( $usr = ria_mysql_fetch_array($rusr) ){
			// mise à jour du nombre de commande
			gu_users_update_orders( $usr['id'] );
			gu_users_update_orders_web( $usr['id'] );
			gu_users_update_orders_canceled( $usr['id'] );
			gu_users_update_orders_canceled_web( $usr['id'] );
		}
	}

