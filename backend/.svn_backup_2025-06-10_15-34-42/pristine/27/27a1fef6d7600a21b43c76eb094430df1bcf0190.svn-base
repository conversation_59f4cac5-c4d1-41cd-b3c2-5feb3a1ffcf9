<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  4021 => 'Bucharest and Ilfov County',
  40230 => 'Suceava',
  40231 => 'Botoșani',
  40232 => 'Iași',
  40233 => 'Neamț',
  40234 => 'Bacău',
  40235 => 'Vaslui',
  40236 => 'Galați',
  40237 => 'Vrancea',
  40238 => 'Buzău',
  40239 => 'Brăila',
  40240 => 'Tulcea',
  40241 => 'Constanța',
  40242 => 'Călărași',
  40243 => '<PERSON>alomiț<PERSON>',
  40244 => '<PERSON><PERSON><PERSON>',
  40245 => '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  40246 => '<PERSON><PERSON><PERSON><PERSON>',
  40247 => 'Teleorman',
  40248 => '<PERSON>rge<PERSON>',
  40249 => 'Olt',
  40250 => 'Vâlcea',
  40251 => 'Dolj',
  40252 => 'Mehedinți',
  40253 => 'Gorj',
  40254 => 'Hunedoara',
  40255 => 'Caraș-Severin',
  40256 => 'Timiș',
  40257 => 'Arad',
  40258 => 'Alba',
  40259 => 'Bihor',
  40260 => 'Sălaj',
  40261 => 'Satu Mare',
  40262 => 'Maramureș',
  40263 => 'Bistrița-Năsăud',
  40264 => 'Cluj',
  40265 => 'Mureș',
  40266 => 'Harghita',
  40267 => 'Covasna',
  40268 => 'Brașov',
  40269 => 'Sibiu',
  4031 => 'Bucharest and Ilfov County',
  40330 => 'Suceava',
  40331 => 'Botoșani',
  40332 => 'Iași',
  40333 => 'Neamț',
  40334 => 'Bacău',
  40335 => 'Vaslui',
  40336 => 'Galați',
  40337 => 'Vrancea',
  40338 => 'Buzău',
  40339 => 'Brăila',
  40340 => 'Tulcea',
  40341 => 'Constanța',
  40342 => 'Călărași',
  40343 => 'Ialomița',
  40344 => 'Prahova',
  40345 => 'Dâmbovița',
  40346 => 'Giurgiu',
  40347 => 'Teleorman',
  40348 => 'Argeș',
  40349 => 'Olt',
  40350 => 'Vâlcea',
  40351 => 'Dolj',
  40352 => 'Mehedinți',
  40353 => 'Gorj',
  40354 => 'Hunedoara',
  40355 => 'Caraș-Severin',
  40356 => 'Timiș',
  40357 => 'Arad',
  40358 => 'Alba',
  40359 => 'Bihor',
  40360 => 'Sălaj',
  40361 => 'Satu Mare',
  40362 => 'Maramureș',
  40363 => 'Bistrița-Năsăud',
  40364 => 'Cluj',
  40365 => 'Mureș',
  40366 => 'Harghita',
  40367 => 'Covasna',
  40368 => 'Brașov',
  40369 => 'Sibiu',
);
