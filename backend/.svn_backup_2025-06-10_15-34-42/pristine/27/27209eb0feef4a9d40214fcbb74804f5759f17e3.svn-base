#dlv-add-rule label.inline {
    text-align: left !important;
    width: 175px !important;
}
#dlv-add-rule-set { float: left; }
#dlv-set1 {
	float: left;
	margin-left: 5px;
}
#dlv-label-ref2 {
	width: 10px !important;
}
#dlv-add-rule input.text {
    width: 300px;
}
.dlv-rules-buttons {
    text-align: right;
}
#site-content table tbody #dlv-list-rules-prd {
	padding-bottom: 10px;
}
#dlv-list-rules-prd .notice {
	margin: 10px;
}
.dlv-list-choose {
	padding: 3px 20px !important;
}
#dlv-special {
    width: 665px;
}
#site-content table.dlv-special .readonly {
    background-color: #F0F0F0;
    border: 1px solid #AFAFAF;
    color: #6D6D6D;
    padding: 1px;
}
#site-content table tbody td.dlv-spe-rules {
	border: none;
	padding: 5px;
}
#site-content table tbody td.dlv-spe-rules:hover {
	background-color: white;
}
#site-content #tb-tabProducts fieldset legend {
	padding: 0 5px;
}
table.dlv-rules {
	width: 100%;
}
table.dlv-rules .col1 {
	width: 3%;
}
table.dlv-rules .col2 {
	width: 63.5%;
}
table.dlv-rules .col3 {
	width: 33.5%;
}
table.dlv-rules .col4 {
	width: 97%;
}