<?php

/**	\file devices-location.php
 *
 *	Ce fichier est chargé d'afficher sur une carte l'emplacement des appareils équipés de Yuto
 *
 */

 
// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_FDV_DEVICE_LOCATION');

require_once('devices.inc.php');

define('ADMIN_PAGE_TITLE', _('Localisation des appareils') . ' - Yuto');
require_once('admin/skin/header.inc.php');

// Charge la liste des appareils depuis la base de données et l'enregistre dans un tableau associatif
$rdevice = dev_devices_get( 0, 0, '', -1, '=', false, false, null, true );
$devices = array();
while( $device = ria_mysql_fetch_assoc($rdevice) ){
	
	// Charge l'utilisateur associé à cet appareil
	$ruser = gu_users_get( $device['usr_id'] );
	if( $ruser && ria_mysql_num_rows($ruser) ){
		$user = ria_mysql_fetch_assoc( $ruser );
		$device['user_name'] = trim( htmlspecialchars($user['adr_firstname'].' '.$user['adr_lastname'].' '.$user['society']) );
	}else{
		$device['user_name'] = 'Utilisateur inconnu';
	}

	$device['brand'] = ucfirst($device['brand']);
	$devices[] = $device;
}

 ?>
	<style type="text/css">
	<!--
		#site-content {
			position: relative;
		}
		#map {
			right: 0;
			top: 30px;
			bottom: 0;
			left: 0;
			position: absolute;
		}
	//-->
	</style>
	<h2><?php print _('Localisation des appareils'); ?></h2>
    <div id="map"></div>
    <script>
	<!--
    	var devices = <?php print json_encode( $devices, JSON_NUMERIC_CHECK ); ?>;
		
		// Initialise la carte
		function initMap() {
			
			var googleMap = new google.maps.Map(document.getElementById('map'));

			var zoneMarqueurs = new google.maps.LatLngBounds();
			
			var aliveDelay = new Date(new Date().getTime());
			var lostDelay = new Date(new Date().getTime() - (6 * 60 * 60 * 1000));
			var deadDelay = new Date(new Date().getTime() - (24 * 60 * 60 * 1000));
			var infowindow = new google.maps.InfoWindow({
				content: 	'Chargement...'
			});
			
			for( var i = 0, I = devices.length; i < I; i++ ){

				var device = devices[i];
				var latitude = device['loc_lat'],								
					longitude = device['loc_lng'];
				
				if( longitude!=null && latitude!=null ){

					// La couleur du marqueur dépend de la date/heure à laquelle nous avons reçu un signal pour la dernière fois 
					var device_last_call = new Date(device['date_last_call']);
					var marker_icon, signal_alert_class;					
					if( device_last_call<aliveDelay && device_last_call>lostDelay ){
						marker_icon = '/admin/images/fdv/icon_alive.png';
						signal_alert_class = 'alive';
					}else if( device_last_call<lostDelay && device_last_call>deadDelay ) {
						marker_icon = '/admin/images/fdv/icon_lost.png';
						signal_alert_class = 'lost';
					}else{
						marker_icon = '/admin/images/fdv/icon_dead.png';
						signal_alert_class = 'dead';
					}

					// Crée un marqueur sur la carte pour l'appareil
					var marqueur = new google.maps.Marker({
						map: googleMap,
						position: new google.maps.LatLng( latitude, longitude ),
						icon: marker_icon,
					});
					marqueur.html = '<div id="content">'+
										'<a href="/admin/customers/edit.php?usr='+device['usr_id']+'"><h2>'+device["user_name"]+'</h2></a>'+
										'<ul style="list-style-type:none;">'+
											'<li>'+device["brand"]+' '+device["model"]+'</li>'+
											'<li>Dernier signal:<span class="signal-alert '+signal_alert_class+'"> '+device["date_last_call"]+'</span></li>'+
											'<li><a href="/admin/fdv/devices/edit.php?id='+device['id']+'"><?php print _('Plus...'); ?></a></li>'+
										'</ul>'+
							      	'</div>';

					google.maps.event.addListener(marqueur, 'click', function () {
						infowindow.setContent(this.html);
						infowindow.open(map, this);
					});

					zoneMarqueurs.extend( marqueur.getPosition() );
				}
			}

			googleMap.fitBounds( zoneMarqueurs );
	 	}
	//-->
    </script>

 	<?php
 		require_once('admin/skin/footer.inc.php');
 	?>