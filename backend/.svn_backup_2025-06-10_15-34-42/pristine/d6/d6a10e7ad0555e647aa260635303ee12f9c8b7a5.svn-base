<?php

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_SMS');

if( !isset($config['marketing_is_active']) || !$config['marketing_is_active'] ){
	header( 'location: /admin/index.php' );
	exit;
}
require_once( 'Marketing/CampaignsManager.inc.php' );
require_once( 'Marketing/TriggersManager.inc.php' );
require_once( 'Marketing/Channels.inc.php' );


if( isset( $_POST['save-main'], $_POST['channel'] ) ){
	//sanitize
	$errors = array();
	$success = false;
	$title = htmlspecialchars( $_POST['title'] );
	$desc = htmlspecialchars( $_POST['desc'] );
	$content = htmlspecialchars( $_POST['channel']['content'] );
	
	
	if( trim( $_POST['date-start'] ) == '' ){
		$_POST['date-start'] = date('Y-m-d H:i:s');
	}else{
		$_POST['date-start'] = date('Y-m-d H:i:s',strtotime(dateheureparse($_POST['date-start'])));
	}
	
	if( trim( $_POST['date-end'] ) == '' ){
		$_POST['date-end'] = date('Y-m-d H:i:s',strtotime('2037-12-31'));
	}else{
		$_POST['date-end'] = date('Y-m-d H:i:s',strtotime(dateheureparse($_POST['date-end'])));
	}

	if( !isdateheure( $_POST['date-start'] ) || !isdateheure( $_POST['date-end'] ) ){
		$errors[] = _('Les périodes ne sont pas valident.');
	}else{
		if( strtotime($_POST['date-end']) < strtotime($_POST['date-start']) ){
			$errors[] = _('La date de fin ne peux pas être inférieur à la date de début de campagne.');
		}
	}
	
	//validation
	if( trim( $title ) == '' ){
		$errors[] = _('Il faut un titre pour votre campagne.');
	}
	if(!isset($_POST['trigger'])){
		$errors[] = _('Vous devez sélectionner un déclencheur');
	}else{
		$trigger = $_POST['trigger'];
	}
	if( !is_numeric( $trigger ) || $trigger <= 0 ){
		$errors[] = _('L\'identifiant du trigger est iccorect.');
	}
	if( !in_array( $_POST['type'], array( 'DIFF', 'NOW' ) ) ){
		$errors[] = _('Le type de campagne est incorrect');
	}
	$type = $_POST['type'];
	
	if( !in_array( $_POST['channel']['type'], array( 'SMS', 'EMAIL' ) ) ){
		$errors[] = _('Le type de moyen de transmition est incorrect');
	}
	$channel_type = $_POST['channel']['type'];
	
	if( !count( $errors ) ){
		$id = CampaignsManager::addCampaigns( $title, $desc, $type, $_POST['date-start'], $_POST['date-end'] );
		
		if( !$id ){
			$errors[] = _('Une erreur inattendue est survenue lors de la sauvegarde de votre campagne, veuillez réessayer ou contacter l\'administrateur');
		}else{
			$channel_id = Channels::addChannels( $config['wst_id'], $channel_type, $id, false, false, 1, $config['i18n_lng'] );
			Channels::addChannelContent($channel_id, $content);
			$group_id = TriggersManager::addTriggersGroups( $id, 'and', 'all' );
			TriggersManager::addTriggerCondition( $group_id, $trigger, $id);
			$success = true;
		}
		if($success){
			header('location: index.php?success=true');
			exit;
		}
	}
}

define( 'ADMIN_PAGE_TITLE', _('Nouvelle campagne SMS').' - '._('Campagnes Marketing') );
require_once( 'admin/skin/header.inc.php' );
?>
	<h2><?php print _('Campagne marketing'); ?></h2>

<?php
if( isset( $errors ) && count( $errors ) ){
	echo '<div class="notice error">';
	foreach( $errors as $error ){
		echo '<p>'.htmlspecialchars( $error ).'</p>';
	}
	echo '</div>';
}
if( isset( $success ) ){
	echo '<div class="notice success"><p>'.htmlspecialchars( $success ).'</p></div>';
}
?>
	<form action="" method="post">
		
		<input type="hidden" name="type" value="NOW">
		<input type="hidden" name="channel[type]" value="SMS">
		
		<table>
			<caption><?php print _('Ajouter une campagne marketing'); ?></caption>
			<colgroup>
				<col width="140">
				<col width="*">
			</colgroup>
			<tfoot>
			<tr>
				<td></td>
				<td><input type="submit" name="save-main" value="<?php print _('Enregistrer'); ?>"></td>
			</tr>
			</tfoot>
			<tbody>
			<tr>
				<td><label for="title"><?php print _('Titre :'); ?></label></td>
				<td><input type="text" id="title" name="title"></td>
			</tr>
			<tr>
				<td><label for="desc"><?php print _('Description :'); ?></label></td>
				<td><textarea id="desc" name="desc" cols="50" rows="10"></textarea></td>
			</tr>
			<tr>
				<td><label for="date-start"><?php print _('Date de début :'); ?></label></td>
				<td><input class="date datepicker date-start" type="text" name="date-start" id="date-start"></td>
			</tr>
			<tr>
				<td><label for="date-end"><?php print _('Date de fin :'); ?></label></td>
				<td><input class="date datepicker date-end" type="text" name="date-end" id="date-end"></td>
			</tr>
			<tr>
				<td><label for="trigger"><?php print _('Sélectionner un déclencheur :'); ?></label>
				</td>
				<td>
					<select name="trigger" id="trigger">
						<?php $res = TriggersManager::getTriggers();
						while( $trigger = ria_mysql_fetch_assoc( $res ) ){
							echo '<option value="'.$trigger['id'].'">'.htmlspecialchars( TriggersManager::getTriggerName( $trigger['id'] ) ).'</option>';
						} ?>
					</select>
				</td>
			</tr>
			<tr>
				<th colspan="2"><?php print _('Saisir le message du sms'); ?></th>
			</tr>
			<tr>
				<td colspan="2">
					<textarea id="desc" name="channel[content]" cols="60" rows="10"></textarea>
				</td>
			</tr>
			</tbody>
		</table>
	</form>
<?php
require_once( 'admin/skin/footer.inc.php' );
?>