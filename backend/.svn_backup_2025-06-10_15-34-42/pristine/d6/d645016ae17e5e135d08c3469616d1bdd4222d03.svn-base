<?xml version="1.0" encoding="utf-8"?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults>
            <bind key="NonExistent">null</bind>
            <bind key="$quz">quz</bind>
            <bind key="$factory">factory</bind>
        </defaults>

        <service id="bar" class="Symfony\Component\DependencyInjection\Tests\Fixtures\Bar" autowire="true">
            <bind key="Symfony\Component\DependencyInjection\Tests\Fixtures\BarInterface" type="service" id="Symfony\Component\DependencyInjection\Tests\Fixtures\Bar" />
            <bind key="$foo" type="collection">
                <bind>null</bind>
            </bind>
        </service>

        <service id="Symfony\Component\DependencyInjection\Tests\Fixtures\Bar">
            <factory method="create" />
        </service>
    </services>
</container>
