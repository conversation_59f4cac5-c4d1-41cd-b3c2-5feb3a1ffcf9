<?php
	/** \file avisVerifie.php
 	 * 	\ingroup marketplace
	 *	Classe avis vérifié
	 *	Cette classe permet la gestion de l'API avis certifié
	 *	Client utilisant cette API : proloisirs
	 *	Attention penser à configurer les variable de configuration pour le client voulu
	 *	Liste des varibales
	 *		- avis_verifie_secret_key
	 *		- avis_verifie_website_id
	 *		- avis_verifie_url_list_products
	 *		- avis_verifie_url_avis_produit
	 *		- avis_verifie_url_avis_site
	 *	Ne pas oublier de configurer la génération des fichier de récupération du coté du compte client sur www.avis-verifies.com
	 *	Le secret key et le website id sont trouvable sur le site dans Mon compte => Codes d'Intégrations
	 *	Les url et l'activation des fichiers se trouve  dans Intégration => Récupérer vos avis => API site et API produit*
	 *
	 * 	Exemple 1 : Envoi de commande
	 * 	$avis = new AvisVerifie();
	 * 	$avis_verifie->set_order($order['id']);
	 * 	$avis_verifie->calcul_signature();
	 * 	$send = $avis_verifie->send_order();
	 * 	$update = ord_order_set_verifie($order['id'],1);
	 *
	 * 	Exemple 2 :  récupération d'avis
	 * 	$avis_verifie = new AvisVerifie();
	 * 	$list_products = $avis_verifie->get_list_product_have_notice();
	 * 	foreach ($list_products as $value) {
	 * 		$notices = $avis_verifie->get_notice_product($value['0']);
	 * 	}
	 * 	$list_notice_website = $avis_verifie->get_notice_website();
	 */

	require_once('orders.inc.php');
	require_once('users.inc.php');
	
	class AvisVerifie {
		private $secret_key = ''; ///< Clé secrète qui identifie le compte client
		private $website_id = ''; ///< Identifiant client (clé publique)
		private $url_api = 'www.avis-verifies.com/index.php'; ///< Url appel api
		private $url_get_products = ''; ///< Url permettant la recupération de la liste des produits ayant un avis
		private $url_get_notice_products = ''; ///< Url permettant de récupérer les avis d'un produit
		private $url_get_product_rating = ''; ///< URL permettant de récupérer la note moyenne + le nombre d'avis
		private $url_get_notice_website = ''; ///< Url permettant de récupérer les avis du site
		private $cmd = array();

		/** Constructeur de l'instance AvisVerifie
		 */
		public function __construct(){
			global $config;
		
			$this->secret_key = $config['avis_verifie_secret_key'];
			$this->website_id = $config['avis_verifie_website_id'];
			$this->url_get_products = $config['avis_verifie_url_list_products'];
			$this->url_get_notice_products = $config['avis_verifie_url_avis_produit'];
			$this->url_get_product_rating = $config['avis_verifie_url_product_rating'];
			$this->url_get_notice_website = $config['avis_verifie_url_avis_site'];

			$this->cmd = array(
		            'query'            => 'pushCommandeSHA1',   //Required
		            'order_ref'        => '',        			//Required - Reference order 
		            'email'            => '',        			//Required - Client email
		            'lastname'         => '',                 	//Required -  Client lastname
		            'firstname'        => '',                	//Required -  Client firstname
		            'order_date'       => '', 					//Required - Format YYYY-MM-JJ HH:MM:SS
		            'delay'            => '',                   //0=Immediately / ‘n’ days between 1 and 30 days
		            'PRODUCTS'         => '',
		            'sign'             => '',
		    	);
		}

		/** Calcul et initialisation de la signature pour l'envoi de la commande 
		 */
		public function calcul_signature(){
			$order = $this->cmd;
			// on encode la signature en SHA1 avec les données de la commande et la clé secrète
			$signature = SHA1($order['query'].$order['order_ref'].$order['email'].$order['lastname'].$order['firstname'].$order['order_date'].$order['delay'].$this->secret_key);
			$this->cmd['sign'] = $signature;
		}

		/** Initialise la commande à envoyer
		 * @param int $idOrder identifiant de la commande à initialiser avant envoi
		 */
		public function set_order($idOrder){
			global $config;
			// Récupération sur les informations de la commande
			$orders = ord_orders_get_simple(array("id"=>$idOrder));
			$order_info = ria_mysql_fetch_assoc($orders);
			// Récupération des informations sur le client
			$user = gu_users_get($order_info['usr_id']);
			$usr_info = ria_mysql_fetch_assoc($user);
			$this->cmd['order_ref'] = $order_info['id'];
			$this->cmd['email'] = $usr_info['email'];
			$this->cmd['lastname'] = $usr_info['adr_lastname'];
			$this->cmd['firstname'] = $usr_info['adr_firstname'];
			$this->cmd['order_date'] = $order_info['date'];

			// Spé Berton, on a pas besoin d'allez plus loin, on ne récupère que la commande et pas ses produits
			if ($config['tnt_id'] == 588){
				if( $this->cmd['lastname'] == "" ){
					$this->cmd['lastname'] = ".";
				}

				if( $this->cmd['firstname'] == "" ){
					$this->cmd['firstname'] = ".";
				}

				unset($this->cmd['PRODUCTS']);
				return;
			}

			// Récupération des produits de la commande
			$liste_products = ord_products_get($idOrder);
			$products = [];
			while ($product = ria_mysql_fetch_assoc($liste_products)){
				$rprd = prd_products_get_simple($product['id'], '', false,  $config['cat_root'], true);

				if( !ria_mysql_num_rows($rprd) ){
					continue;
				}
				$prd = ria_mysql_fetch_assoc($rprd);
				$url = isset($prd['url_alias']) ? $prd['url_alias'] : '';

				if( trim($url) == '' ){
					$url = prd_products_get_url($prd['id'], true);
				}
				$url_img = '';

				if( is_numeric($prd['img_id']) && $prd['img_id'] > 0 && isset($config['img_sizes']['high']) ){
					$rimg = prd_images_get($prd['id'], $prd['img_id']);

					if( ria_mysql_num_rows($rimg) ){
						$img = ria_mysql_fetch_assoc($rimg);
						$size = $config['img_sizes']['high'];
						$url_img = $config['img_url'].'/'.$size['width'].'x'.$size['height'].'/'.$img['id'].'.'.$size['format'];

						if( isset($config['is_https']) && $config['is_https'] ){
							$url_img = str_replace( 'http://', 'https://', $url_img );
						}
					}
				}
				$products[] = [
					"id_product"		=> $prd['ref'],
					"name_product"		=> $prd['name'],
					"url_image_product"	=> $url_img,
					"url_product"		=> $config['site_url'].'/'.$url,
					"gtin_ean"			=> $prd['barcode'],
					"brand_name"		=> $prd['brd_title'],
				];
				// array_push($products, $prd);
			}
			$this->cmd['PRODUCTS'] = $products;
		}

		/** Fonction permettant l'envoie de la commande via l'API
		 *	@return string retourne les jsons de réponse
		 *	liste des codes d'erreur pour le retour
		 *		-1 : une ou plusieurs informations obligatoires sont manquantes sur les commandes.
		 *		1 : Votre action a été effectuée avec succès
		 *		2 : La calcul de la signature ne donne pas le même résultat
		 *		3 : Le compte client (website id) n'a pas été identifié
		 *		4 : Les paramètres sont incorrects
		 */
		public function send_order(){
			// Le nom et le prénom sont des informations obligatoires
			if( !isset($this->cmd['lastname'], $this->cmd['firstname']) || trim($this->cmd['lastname']) == '' || trim($this->cmd['firstname']) == '' ){
				return ['return' => -1];
			}

			// Création de la requête
			$encrypted_notification=http_build_query(
				array(
					'idWebsite' => $this->website_id,
					'message' => json_encode($this->cmd)
				)
			);

			// Création du POST à envoyer
			$post_notification = array('http' =>
				array(
					'method'  => 'POST',
					'header'  => 'Content-type: application/x-www-form-urlencoded',
					'content' => $encrypted_notification
				)
			);
			// Envoi de la commande via l'API
			$context_notification = stream_context_create($post_notification);

			$result_notification = file_get_contents('https://' .$this->url_api.'?action=act_api_notification_sha1&type=json2', false, $context_notification);

			// Récupéaration du message de retour après envoi
			// $result array avec deux entrée
			// code : code d'erreur
			// debug : message lié au code
			$result_notification = json_decode($result_notification,true);

		    return $result_notification;
		}

		/** Fonction qui récupère les produit ayant un avis
		 * @return array liste des produits ayant un avis
		 */
		function get_list_product_have_notice(){
			global $config;

			if( !trim($this->url_get_products) ){
				error_log( 'Avis Verifies : la variable de configuration avis_verifie_url_list_products n\'est pas definie pour le tenant '.$config['tnt_id'] );
				return array();
			}

			$result = file_get_contents('https:' . $this->url_get_products,true);
			$content = explode(PHP_EOL, $result);
			$return = array();
			foreach ($content as $line) {
				array_push($return, explode(";", $line));
			}

			return $return;
		}

		/** Méthode qui récupère les avis d'un produit
		 * @param	string|int				$id_prd Obligatoire, Référence d'un produit
		 * @param	bool					$array	Optionnel, S'il faut retourner un tableau ou un objet
		 * @return	object|array|bool		Objet/ Tableau des avis produit, false en cas d'erreur ou aucun avis produit
		 */
		public function get_notice_product($id_prd, $array=false){
			$array = is_bool($array) ? $array : false;
			$ch = curl_init();

			curl_setopt($ch, CURLOPT_HEADER, 0);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_URL, 'https:' . $this->url_get_notice_products . $id_prd . '.json');

			$data = curl_exec($ch);

			curl_close($ch);

			$data_decoded = json_decode($data, $array);

			return json_last_error() == JSON_ERROR_NONE ? $data_decoded : false;

			// $result = file_get_contents('https:' . $this->url_get_notice_products . $id_prd . '.json');
			// return json_decode($result);
		}

		/**	Méthode permettant de récupérer la note moyenne + nombre d'avis d'un produit
		 * @param	string|int				$id_prd Obligatoire, Référence d'un produit
		 * @param	bool					$array	Optionnel, S'il faut retourner un tableau ou un objet
		 * @return	object|array|bool		Objet/ Tabeau de la note + nombre d'avis produit, false en cas d'erreur
		 */
		public function get_product_rating($id_prd, $array=false){
			$array = is_bool($array) ? $array : false;
			$ch = curl_init();

			curl_setopt($ch, CURLOPT_HEADER, 0);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_URL, 'https:' . $this->url_get_product_rating . $id_prd . '.json');

			$data = curl_exec($ch);

			curl_close($ch);

			$data_decoded = json_decode($data, $array);

			return json_last_error() == JSON_ERROR_NONE ? $data_decoded : false;
		}

		/** Fonction qui récupère les avis du site
		 * @return string json de la liste avis du site
		 */
		function get_notice_website(){
			global $config;

			$result = file_get_contents('https:' . $this->url_get_notice_website . '.json');
			if( $result===false ){
				error_log( 'Avis Verifies : la variable de configuration avis_verifie_url_avis_site n\'est pas definie pour le tenant '.$config['tnt_id'] );
				return array();
			}
			return json_decode($result);
		}

		/** fonction getter du champs commande
		 *	@return retourne la commande de l'objet
		 */
		function get_commande(){
			return $this->cmd;
		}

		/** fonction getter du champs commande
		 *	@return retourne la commande de l'objet
		 */
		function get_website_id(){
			return $this->website_id;
		}
	}
