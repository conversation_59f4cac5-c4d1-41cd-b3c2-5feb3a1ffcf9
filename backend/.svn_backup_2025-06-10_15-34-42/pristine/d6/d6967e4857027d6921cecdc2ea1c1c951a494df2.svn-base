<?php
// \cond onlyria

require_once('prices.inc.php');

/** \defgroup model_prices_admin Gestion de l'interface d'administration des tarifs
 *	\ingroup view_admin
 *
 *	Ce module est chargé de générer l'interface d'administration des prix dans le back-office
 *
 * @{
 */

/// Identifiant d'une condition sur un tarif
define( 'TERMS_PRICE', 1 );
/// Identifiant d'une condition sur une TVA
define( 'TERMS_TVA', 2 );
/// Identifiant d'une condition sur une eco-taxe
define( 'TERMS_ECO', 3 );
/// Identifiant d'une condition sur une promotion
define( 'TERMS_PROMO', 4 );

/** Génère un contenu HTML pour l'affichage des conditions de TVA ou de tarif
 *	@param int $id Obligatoire, Identifiant de la TVA ou du tarif
 *	@param $type_terms Obligatoire, identifiant du type de données
 *	@param $form Facultatif, Formulaire sur lequel se trouveront les conditions
 *	@return string Le code HTML généré
 */
function prc_conditions_view( $id, $type_terms, $form='product' ){
	global $config;

	$name = '';
	switch( $type_terms ){
		case TERMS_PRICE :
			$res = prc_prices_get_params( $id,array('is-sync') );
			$r_cdt = prc_price_conditions_get( $id );
			$is_price = true;
			break;
		case TERMS_TVA :
			$res['is-sync'] = false;
			$r_cdt = prc_tva_exempt_conditions_get( $id );
			$is_price = false;
			$name .= '-tva';
			break;
		case TERMS_ECO :
			$res['is-sync'] = false;
			$r_cdt = prc_ecotaxe_exempt_conditions_get($id);
			$is_price = false;
			$name .= '-eco';
			break;
		case TERMS_PROMO :
			$res['is-sync'] = false;
			$r_cdt = prc_group_conditions_get( $id );
			$is_price = true;
			$name .= '-new';
			break;
	}

	if( $res===false || !is_array($res) || sizeof($res)==0 ) return false;
	$is_sync = $res['is-sync'];

	if( $r_cdt===false ) return false;

	if( $type_terms==TERMS_ECO && ria_mysql_num_rows($r_cdt)==0 ) return false;

	// Récupère les conditions possibles
	if( $type_terms==TERMS_TVA )
		$r_fld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array("prc_priority"), true, array(), null, null, false, null, true);
	else
		$r_fld = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array("prc_priority"), true, array(), null, null, true, null);

	if( $r_fld===false ) return false;

	// Détermine un préfixe en plus pour les conditions de TVA
	$tva = !$is_price && $type_terms!=TERMS_ECO ? '-tva' : '';

	// Détermine les balises name et id des composants des conditions
	$bname = $type_terms==TERMS_PROMO ? $name : $name.'-'.$id;
	$html = '';

	$count = 0;
	$nbCdt = ria_mysql_num_rows($r_cdt)-1;
	while( $cdt = ria_mysql_fetch_array($r_cdt) ){
		$bid = $bname.'-'.$count;
		// Construit les option des select pour l'ajout d'une condition, le select sera construit lors de l'appel pour avoir l'identifiant du tarif
		$html .= '<div class="display-flex" id="js-new-condition-'.$bid.'">
		<div class="display-flex" id="js-new-condition2-'.$bid.'">
			<select class="fld" '.( $is_sync ? 'disabled="disabled"' :'' ).' name="cdt'.$bname.'['.$count.']" id="cdt'.$bid.'" onchange="cdtForm('.$id.', '.$count.', false, '.( $type_terms==TERMS_TVA ? 'true' : 'false' ).', '.( $type_terms==TERMS_ECO ? 'true' : 'false' ).');">';
		while( $fld = ria_mysql_fetch_array($r_fld) ){
			$html .= '<option value="'.$fld['id'].'" '.( $fld['id']==$cdt['fld'] ? 'selected="selected"' : '' ).'>'.$fld['name'].' ('.$fld['cls_name'].')</option>';
		}
		$html .= '</select>';

		$html .= '<div '.( $is_sync && $count==$nbCdt ? 'class="conditions"' : 'class="conditions-next"' ).' id="condition'.$bid.'">';
		if( $cdt['type-id']<>FLD_TYPE_BOOLEAN_YES_NO && $cdt['type-id']<>FLD_TYPE_DATE){
			// On récupère les types de symboles
			$fld_type = fld_fields_get_type($cdt['fld']);
				$r_symbols = prc_symbols_get( $fld_type, 1, array('%LIKE', 'LIKE%') );
				if( $r_symbols!==false ){
				$html .= '<select class="sbl-select" '.( $is_sync ? 'disabled="disabled"' :'' ).' name="sbl'.$bname.'['.$count.']" id="sbl'.$bid.'" onchange="chargInputVal('.( $type_terms==TERMS_ECO ? '0' : $id).', '.$count.', false, '.( $is_price ? 'false' : 'true' ).', '.( $type_terms==TERMS_ECO ? 'true' : 'false' ).')">';
				while( $symbol = ria_mysql_fetch_array($r_symbols) )
					$html .= '<option value="'.htmlspecialchars($symbol['symbol']).'" '.( $symbol['symbol']==$cdt['symbol'] ? 'selected="selected"' : '' ).'>'.$symbol['desc'].'</option>';
				$html .= '</select>';
			}
		}

		switch( $cdt['type-id'] ){
			case FLD_TYPE_REFERENCES_ID :
				if( fld_fields_get_physical_name($cdt['fld']) == 'usr_prc_id' ){
					// Il s'agit d'une condition sur la catégorie tarifiare d'un utilisateur
					$cat_price = prd_prices_categories_get();
					if( $cat_price!=false ){
						$html .= '	<select  '.( $is_sync ? 'disabled="disabled"' :'' ).' name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'">';
						while( $cat = ria_mysql_fetch_array($cat_price) )
							$html .= '		<option value="'.$cat['id'].'" '.( $cat['id']==$cdt['value'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $cat['name'] ).'</option>';
						$html .= '	</select>';
					} else {
						$html .= '<input '.( $is_sync ? 'disabled="disabled"' :'' ).' type="text" name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'" value="'.$cdt['value'].'" />';
					}
				} elseif( fld_fields_get_physical_name($cdt['fld']) == 'usr_cac_id' ){
					// condition sur la catégorie comptable du client
					$racc = gu_accounting_categories_get();
					if( $racc!=false ){
						$html .= '	<select  '.( $is_sync ? 'disabled="disabled"' :'' ).' name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'">';
						while( $acc = ria_mysql_fetch_array($racc) )
							$html .= '		<option value="'.$acc['id'].'" '.( $acc['id']==$cdt['value'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $acc['name'] ).'</option>';
						$html .= '	</select>';
					} else {
						$html .= '<input '.( $is_sync ? 'disabled="disabled"' :'' ).' type="text" name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'" value="'.$cdt['value'].'" />';
					}
				} elseif( fld_fields_get_physical_name($cdt['fld']) == 'cly_col_id' ){
					// Il s'agit d'une condition sur le conditionnement
					$rcol = prd_colisage_types_get();
					if( $rcol!=false ){
						$html .= '	<select  '.( $is_sync ? 'disabled="disabled"' :'' ).' name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'">';
						while( $col = ria_mysql_fetch_array($rcol) )
							$html .= '		<option value="'.$col['id'].'" '.( $col['id']==$cdt['value'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $col['name'] ).'</option>';
						$html .= '	</select>';									}
				} elseif( fld_fields_get_physical_name($cdt['fld']) == 'prd_brd_id' ){
					// condition sur la marque du produit
					if( $rbrands = prd_brands_get() ){
						$html .= '	<select  '.( $is_sync ? 'disabled="disabled"' :'' ).' name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'">';
						while( $brd = ria_mysql_fetch_array($rbrands) )
							$html .= '		<option value="'.$brd['id'].'" '.( $brd['id']==$cdt['value'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $brd['title'] ).'</option>';
						$html .= '	</select>';
					}
				} else {
					$html .= '<input '.( $is_sync ? 'disabled="disabled"' :'' ).' type="text" name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'" value="'.$cdt['value'].'" />';
				}
				break;
			case FLD_TYPE_TEXT : // Type acceptant les chaines de caractères
				$html .= '<input '.( $is_sync ? 'disabled="disabled"' :'' ).' type="text" name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'" value="'.$cdt['value'].'" />';
				break;
			case FLD_TYPE_TEXTAREA : // Type pour les zones de texte
				$html .= '<textarea '.( $is_sync ? 'disabled="disabled"' :'' ).' name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'" rows="5" cols="50">'.htmlspecialchars( $cdt['value'] ).'</textarea>';
				break;
			case FLD_TYPE_INT : // Type n'acceptant que les entiers
				$values = explode(';', $cdt['value']);
				$html .= '<input '.( $is_sync ? 'disabled="disabled"' :'' ).' type="text" name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'" value="'.$values[0].'" />';
				if( $cdt['symbol']=="><" )
					$html .= '&nbsp;et&nbsp;<input '.( $is_sync ? 'disabled="disabled"' :'' ).' type="text" name="val-cdt'.$bname.'-2['.$count.']" id="val-cdt'.$bid.'-2" value="'.$values[1].'" />';
				$html .= $is_sync ? '' : '<div class="clear"></div><span class="info-fld">'._('Seuls les nombres entiers sont acceptés pour ce type de condition.').'</span>';
				break;
			case FLD_TYPE_FLOAT : // Type n'acceptant que des valeurs numériques
				$values = explode(';', $cdt['value']);
				$html .= '<input '.( $is_sync ? 'disabled="disabled"' :'' ).' type="text" name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'" value="'.$values[0].'" />';
				if( $cdt['symbol']=="><" )
					$html .= '&nbsp;et&nbsp;<input '.( $is_sync ? 'disabled="disabled"' :'' ).' type="text" name="val-cdt'.$bname.'-2['.$count.']" id="val-cdt'.$bid.'-2" value="'.$values[1].'" />';
				$html .= $is_sync ? '' : '<div class="clear"></div><span class="info-fld">'._('Seules les valeurs numériques sont acceptées pour ce type de condition.').'</span>';
				break;
			case FLD_TYPE_SELECT :
				$values = fld_restricted_values_get( 0, $cdt['fld'] );
				$html .= '<select '.( $is_sync ? 'disabled="disabled"' :'' ).' name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'">';
				while( $val = ria_mysql_fetch_array($values) )
					$html .= '<option value="'.$val['name'].'" title="'.$val['name'].'" '.( $val['name']==$cdt['value'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $val['name'] ).'</option>';
				$html .= '</select>';
				break;
			case FLD_TYPE_SELECT_HIERARCHY :
				// On récupère les valeurs pour le select hiérarchique
				$values = fld_restricted_values_get(0, $cdt['fld']);
				if( $values!==false ){
					while( $r = ria_mysql_fetch_array($values) ){
						$name_val = $r['name'];
						$rid = $r['id'];
						while( $vals2 = fld_restricted_values_get( $r['parent'] ) ){
							if( $r = ria_mysql_fetch_array($vals2) )
								$name_val = $r['name'].' >> '.$name_val;
						}
						$all_vals[] = array( 'id' => $rid, 'name' => htmlspecialchars($name_val) );
					}
					$all_vals = array_msort( $all_vals, array( 'name'=>SORT_ASC ) );
					// On affiche ce select
					$html .= '<select '.( $is_sync ? 'disabled="disabled"' :'' ).' name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'">';
					foreach( $all_vals as $val )
						$html .= '<option value="'.$val['id'].'" title="'.$val['name'].'" '.( $val['id']==$cdt['value'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $val['name'] ).'</option>';
					$html .= '</select>';
				}
				break;
			case FLD_TYPE_BOOLEAN_YES_NO :
				$html .= '<input '.( $is_sync ? 'disabled="disabled"' :'' ).' class="bl-cdt" type="radio" name="val-cdt'.$bname.'['.$count.']" id="oui'.$bid.'" value="oui" '.( strtolower($cdt['value'])=='oui' || $cdt['value'] ? 'checked="checked"' : '').' /><label for="oui'.$bid.'">Oui</label><br />';
				$html .= '<input '.( $is_sync ? 'disabled="disabled"' :'' ).' class="bl-cdt" type="radio" name="val-cdt'.$bname.'['.$count.']" id="non'.$bid.'" value="non" '.( strtolower($cdt['value'])=='non' || !$cdt['value'] ? 'checked="checked"' : '').' /><label for="non'.$bid.'">Non</label>';
				break;
			case FLD_TYPE_DATE :
				$values = explode(';', $cdt['value']);
				if( !$is_sync || ($is_sync && ($values[0]==DAY_AND_MONTH_IS_NOW || $values[0]==WEEK_IS_NOW || $values[0]==MONTH_IS_NOW)) ){
					$html .= '<div class="val-fld-date">';
					$html .= '<input type="radio" name="choose-date'.$bname.'" id="choose-date'.$bid.'-1" value="1" '.( $values[0]==DAY_AND_MONTH_IS_NOW || $values[0]==WEEK_IS_NOW || $values[0]==MONTH_IS_NOW ? 'checked="checked"' : '' ).' />';
					$html .= '<select name="sbl-date'.$bname.'-1" id="sbl'.$bid.'-date-1" onclick="changeChooseDate(\'choose-date'.$bid.'-1\')">';
					$html .= '<option value="=" '.( $cdt['symbol']=="=" ? 'selected="selected"' : '' ).'>égal à </option><option value="!=" '.( $cdt['symbol']=="!=" ? 'selected="selected"' : '' ).'>'._('Différent de').'</option>';
					$html .= '</select>';
					$html .= '<select name="val-cdt-date'.$bname.'-1" id="val-cdt'.$bid.'-date-1" onclick="changeChooseDate(\'choose-date'.$bid.'-1\')">';
					$html .= '<option value="-1"></option>';
					$html .= '<option value="'.DAY_AND_MONTH_IS_NOW.'" '.( $values[0]==DAY_AND_MONTH_IS_NOW ? 'selected="selected"' : '' ).'>'._('Aujourdh\'hui').'</option>';
					$html .= '<option value="'.WEEK_IS_NOW.'" '.( $values[0]==WEEK_IS_NOW ? 'selected="selected"' : '' ).'>'._('Cette semaine').'</option>';
					$html .= '<option value="'.MONTH_IS_NOW.'" '.( $values[0]==MONTH_IS_NOW ? 'selected="selected"' : '' ).'>'._('Ce mois-ci').'</option>';
					$html .= '</select>';
					$html .= '</div>';
				}
				if( !$is_sync || ($is_sync && isdate($values[0])) ){
					$html .= '<div class="val-fld-date">';
					$html .= '<input type="radio" name="choose-date'.$bname.'" id="choose-date'.$bid.'-2" value="2" '.( isdate($values[0]) && sizeof($values)==1 ? 'checked="checked"' : '' ).' />';
					$html .= '<select name="sbl-date'.$bname.'-2" id="sbl'.$bid.'-date-2" onclick="changeChooseDate(\'choose-date'.$bid.'-2\')">';
					$html .= '<option value="=">égal à </option><option value="!=">'._('Différent de').'</option>';
					$html .= '</select>';
					$html .= '<input class="datepicker date" type="text" name="val-cdt-date'.$bname.'-2" id="val-cdt'.$bid.'-date-2" value="'.( isdate($values[0]) && sizeof($values)==1 ? $values[0] : '').'" onfocus="changeChooseDate(\'choose-date'.$bid.'-2\')" />';
					$html .= '<span class="info-fld">'._('(Date personnalisée)').'</span><br />';
					$html .= '</div>';
				}
				if( !$is_sync || ($is_sync && sizeof($values)==2) ){
					$html .= '<div class="val-fld-date">';
					$html .= '<input type="radio" name="choose-date'.$bname.'" id="choose-date'.$bid.'-3" value="3" '.( sizeof($values)==2 ? 'checked="checked"' : '' ).' />';
					$html .= '&nbsp;Est compris entre&nbsp;';
					$html .= '<input class="datepicker date" type="text" name="val-cdt-date'.$bname.'-3" id="val-cdt'.$bid.'date-3" value="'.( sizeof($values)==2 && isdate($values[0]) ? $values[0] : '').'" onfocus="changeChooseDate(\'choose-date'.$bid.'-3\')" />';
					$html .= '&nbsp;et&nbsp;<input class="datepicker date" type="text" name="val-cdt-date'.$bname.'-3-2" id="val-cdt'.$bid.'-date-3-2" value="'.( sizeof($values)==2 && isdate($values[1]) ? $values[1] : '').'" onfocus="changeChooseDate(\'choose-date'.$bid.'-3\')" />';
					$html .= '<span class="info-fld">'._('(Période personnalisée)').'</span><br />';
					$html .= '</div>';
				}
				break;
			default :
				$html .= '<input '.( $is_sync ? 'disabled="disabled"' :'' ).' type="text" name="val-cdt'.$bname.'['.$count.']" id="val-cdt'.$bid.'" value="'.$cdt['value'].'" />';
				break;
		}
		$html .= '	</div>';

		$html .= '	</div>';
		$html .= $is_sync ? '' : '<img class="del-cdt" src="/admin/images/del.svg" alt="'._('Supprimer').'" title="'._('Supprimer cette condition').'" name="del" id="del-cdt'.$bid.'" onclick="delCdt('.( $is_price ? 'true' : 'false' ).', '.$id.', '.$cdt['fld'].', false, false, '.( $type_terms==TERMS_ECO ? 'true' : 'false' ).', '.$count.', '.( $type_terms==TERMS_PROMO ? 'true' : 'false' ).');" />';

		$html .= '	</div>';

		// Retourne au premier index dans les tableaux MySQL
		ria_mysql_data_seek( $r_fld, 0 );
		$count++;
	}

	$html .= '<input type="hidden" name="nb-cdt'.$bname.'" id="nb-cdt'.$bname.'" value="'.$count.'" />';
	$html .= $is_sync || $type_terms==TERMS_PROMO ? '' : '<br /><input class="action" type="button"  title="Ajouter une condition à ce tarif" name="add-cdt" id="add-cdt'.$bname.'" value="Ajouter une condition" onclick="addCdt('.$id.', false, '.( $type_terms==TERMS_TVA ? 'true' : 'false' ).', '.( $type_terms==TERMS_ECO ? 'true' : 'false' ).');" />';
	return $html;
}

/** Cette fonction génère le HTML de la liste des tarifs
 *	@param int $prd Optionnel, identifiant d'un produit
 *	@param int $cat Optionnel, identifiant d'une catégorie de produit
 *	@param $periode Optionnel, période d'affichage des tarifs
 *	@param int $id Optionnel, identifiant d'un tarif
 *	@param $promo Optionnel, détermine s'il s'agit d'une promotion, par défaut est à false
 *	@param $group Optionnel, identifiant d'un groupe de promotion
 *	@param $page Optionnel, permet de gérer la pagination
 *	@param int $limit Optionnel, nombre de résultat pouvant être présent dans une page
 *	@param $order_date Optionnel, par défaut les tarifs sont triés par montant décroissant. Indiquer 'asc' pour trier par date croissante ou 'desc' pour trier par date décroissante.
 *  @param string $ref Optionnel, permet de trier par référence. Pour les promotions uniquement
 *  @param $ref_like Optionnel, permet d'indiquer le mode de tri. Valeurs possibles: equal, start, end, in. Pour les promotions uniquement
 *  @param string $date_start Optionnel, permet de retourner les prix a partir de la date de commencement
 *  @param string $date_end Optionnel, permet de retourner les prix jusqu'à la date de fin
 *  @param $return_only_count Optionnel, retourne uniquement le nombre de lignes. Utile pour la pagination.
 *	@return bool False en cas d'échec
 *	@return Le contenu HTML listant les tarifs pour le back-office
 */
function prc_prices_list_view( $prd=0, $cat=0, $periode=0, $id=0, $promo=false, $group=0, $page=1, $limit=0, $order_date=null, $ref=null, $ref_like='start', $return_only_count=false, $date_start=false, $date_end=false){
	if( $prd>0 && !prd_products_exists($prd) ) return false;
	if( $cat>0 && !prd_categories_exists($cat) ) return false;
	// global $config;

	// On récupère les types de tarifs
	$r_types = prc_types_get();

	//si on recherche par référence de produit
	if(isset($ref, $ref_like, $promo) && $promo && $ref_like == 'equal'){
		$prd_name = prd_products_get_id($ref);
	}

	// Récupère les tarifs pour un produit donné
	if( $prd>0 || $cat>0 ){
		$r_prices = prc_prices_get( $id, 0, false, $date_start, $date_end, $prd, false, $cat, false, null, null, 1, $periode, false);
	}elseif( $promo ){
		if($date_start || $date_end ){
			$period = true;
		}else{
			$period = false;
		}
		$r_prices = prc_prices_get_promotions(isset($prd_name) ? $prd_name:false, 1, $periode, false, null, $group, $id, $order_date, $date_start, $date_end, $period, true);
	}else{
		$r_prices = prc_prices_get( $id, 0, false, $date_start, $date_end, null, false,null, false, null, null, 1, $periode, false);
	}
	if( $page>1 && $limit>0 ){
		ria_mysql_data_seek( $r_prices, ($page-1)*$limit );
	}
	$html = '';
	// Affiche les informations sur les tarifs
	if( !$promo && $id==0 ){
		$html .= '	<tr>';
		$html .= '		<td colspan="4" class="none-cdt" id="none-price" '.( $r_prices==false || ria_mysql_num_rows($r_prices)==0 ? '' : 'style="display:none;"' ).'>';
		$html .= _('Aucun tarif conditionnel enregistré');
		$html .= '	</td></tr>';
	}elseif( $promo && $id==0 ){
		$html .= '	<tr>';
		$html .= '		<td colspan="3" class="none-cdt" id="none-price" '.( $r_prices==false || ria_mysql_num_rows($r_prices)==0 ? '' : 'style="display:none;"' ).'>';
		$html .= _('Aucune promotion enregistrée');
		$html .= '	</td></tr>';
	}

	// Charge une fois les dépôts
	$r_dps = prd_deposits_get();

	if( $r_prices!=false ){
		$count = 0;
		while($price = ria_mysql_fetch_array($r_prices) ){
			//si on recherche par référence de produit
			if( isset($ref, $promo, $ref_like) && $promo){
				$product = prd_products_get_ref( $price['prd'] );
				if( $ref_like == 'equal' ){
					if( strtolower2($product) != strtolower2($ref) ){
						continue;
					}
				}else{
					$reg = '/'.( $ref_like == 'start' ? '^' : '' ).$ref.( $ref_like == 'end' ? '$' : '' ).'/i';

					if( !preg_match($reg, $product) ){
						continue;
					}
				}
			}
			if( $limit>0 && $count>=$limit )
				break;
			if($return_only_count){
				$count ++;
				continue;
			}
			if( $id==0 )
				$html .= '	<tr id="prc-line-'.$price['id'].'">';
			if( !$promo )
				$html .= '		<td class="cdt-center"  headers="sync">'.view_prc_prd_is_sync($price).'</td>';
			$html .= '		<td headers="information" class="information" data-label="Informations : ">';
			$html .= '			<div class="info">';
			$html .= '				<label for="type-'.$price['id'].'">'._('Valeur :').'</label>';
			if( $r_types!==false && ria_mysql_num_rows($r_types)>0 ){
				$html .= '			<select '.( $price['is-sync'] ? 'disabled="disabled"' :'' ).' name="type-'.$price['id'].'" id="type-'.$price['id'].'" onchange="displayCumulate('.$price['id'].')">';
					while( $type = ria_mysql_fetch_array($r_types) ){
						if( $type['id']==NEW_PRICE && $price['is-sync'] ){
							$html .= '		<option value="'.$type['id'].'" '.( $price['type']==$type['id'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $type['name'] ).'</option>';
						} elseif( $cat>0 && $type['id']<>NEW_PRICE ){
							$html .= '		<option value="'.$type['id'].'" '.( $price['type']==$type['id'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $type['name'] ).'</option>';
						} elseif( $prd>0 || $promo ) {
							$html .= '		<option value="'.$type['id'].'" '.( $price['type']==$type['id'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $type['name'] ).'</option>';
						} elseif( $type['id']<>NEW_PRICE ){
							$html .= '		<option value="'.$type['id'].'" '.( $price['type']==$type['id'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $type['name'] ).'</option>';
						}
					}
				$html .= '			</select>';
			}
			$html .= '				<select '.( $price['is-sync'] || ($type['id'] !== null && $type['id'] !== NEW_PRICE) ? 'disabled="disabled"' :'' ).' class="valeur-select" name="valeur-type-'.$price['id'].'" id="valeur-type-'.$price['id'].'">';
			$html .= '				<option value="HT" selected="selected">'._('HT').'</option>';
			$html .= ' 				<option value="TTC">'._('TTC').'</option>';
			$html .= '				</select>';
			$html .= '				<input '.( $price['is-sync'] ? 'disabled="disabled"' :'' ).' class="val-select" type="text" name="val-type-'.$price['id'].'" id="val-type-'.$price['id'].'" value="'.number_format($price['value'], 6, '.', ' ').'" />';
			$html .= ' 				<span class="info-bulle color-red" title="'._('Ce champ est obligatoire. Pour un nouveau tarif, la valeur doit être supérieure à 0, pour une remise en pourcentage, la valeur doit être inférieure ou égale à 100.').'">*</span>';
			$html .= '			</div>';
			if( !$promo ){
				$html .= '			<div class="info" '.( $price['type']==NEW_PRICE ? 'style="display:none"' : '' ).'>';
				$html .= '				<label for="unique-'.$price['id'].'" title="'._('En décochant cette option, la remise ne sera pas prise en compte s\'il existe des tarifs de priorité supérieure.').'">'._('Cumuler :').'</label>';
				$html .= '				<input '.( $price['is-sync'] ? 'disabled="disabled"' :'' ).' type="checkbox" name="unique-'.$price['id'].'" id="unique-'.$price['id'].'" value="'.$price['id'].'" '.( $price['is-cumuled'] ? 'checked="checked"' : '' ).' />';
				$html .= ' 				<span class="info-bulle color-red" title="'._('En décochant cette option, la remise ne sera pas prise en compte s\'il existe des tarifs de priorité supérieure.').'">**</span>';
				$html .= '			</div>';
			} else {
				$product = ria_mysql_fetch_array( prd_products_get( $price['prd'] ) );
				$html .= '			<div class="info">';
				$html .= '				<label for="prd-'.$price['id'].'">Article :</label>';
				$html .= '				<input class="prd-ref" type="text" name="prd-'.$price['id'].'" id="prd-'.$price['id'].'" onkeyup="search_references(\'prd-'.$price['id'].'\');" value="'.$product['ref'].'" />';
				$html .= '			</div>';
			}
			if( $price['is-sync'] && $price['name']=='' ){}
			else {
				$html .= '			<div class="info">';
				$html .= '				<input type="hidden" name="is-sync-price-'.$price['id'].'" id="is-sync-price-'.$price['id'].'" value="'.$price['is-sync'].'" />';
				$html .= '				<label for="desc-'.$price['id'].'">'._('Nom :').'</label>';
				$html .= '				<input '.( $price['is-sync'] ? 'class="prc-name" disabled="disabled"' :'' ).' type="text" name="desc-'.$price['id'].'" id="desc-'.$price['id'].'" value="'.htmlspecialchars( $price['name'] ).'" />'.(!$price['is-sync'] ? '<span class="info-fld">'._('(Optionnel)').'</span>' : '');
				$html .= '			</div>';
			}
			if( $promo ){
				$price_bare_ht = '0';
				if( $rprc = prd_products_get_price($product['id']) ){
					if( $prc = ria_mysql_fetch_array($rprc) )
						$price_bare_ht = $prc['price_ht'];
				}
				$html .= ' 			<div class="info">';
				$html .= ' 				<span class="bold">'._('Tarif de base HT :').'</span> '.number_format($price_bare_ht, 2, ',', ' ');
				$html .= ' 			</div>';
			}
			$html .= '		</td>';
			$html .= '		<td headers="conditions-prc" '.( $price['is-sync'] ? 'colspan="2"' : '' ).' class="conditions" id="cdts-'.$price['id'].'" data-label="Conditions : ">  ';

			// Les condition
			$max_unvalid = $price['qte-max']<=1 || $price['qte-max']=='' || $price['qte-max']==2147483647;
			if( ($max_unvalid) && $price['qte-min']<=1 ){
			}elseif( ($cat>0 && $price['is-sync']) || $prd>0 || ($prd==0 && $cat==0)){
				$qteMax = $max_unvalid ? '&#8734;' : $price['qte-max'];
				$qteMin = $price['qte-min']>0 ? $price['qte-min'] : 1;
				$html .= '	<div>';
				$html .= '		<label for="qte-min-'.$price['id'].'">Quantité comprise entre</label>&nbsp;:&nbsp;';
				$html .= '		<input '.( $price['is-sync'] ? 'disabled="disabled"' :'' ).' type="text" name="qte-min-'.$price['id'].'" id="qte-min-'.$price['id'].'" value="'.$qteMin.'" />&nbsp;';
				$html .= '		<label class="lbl-info-cdt" for="qte-max-'.$price['id'].'">et</label>&nbsp;';
				$html .= '		<input '.( $price['is-sync'] ? 'disabled="disabled"' :'' ).' type="text" name="qte-max-'.$price['id'].'" id="qte-max-'.$price['id'].'" value="'.$qteMax.'" />';
				$html .= '		<span class="info-bulle color-red" title="'._('Si la quantité maximum n\'est pas précisée alors aucun maximum ne sera fixé pour ce tarif.').'">***</span>';
				$html .= '	</div>';
			}
			$price['date-start-fr'] = $price['date-start-fr']=='01/01/1000' ? '' : $price['date-start-fr'];
			$price['date-end-fr'] = $price['date-end-fr']=='31/12/9999' ? '' : $price['date-end-fr'];
			if( $price['is-sync'] && $price['date-start-fr']=='' && $price['date-end-fr']=='' ){
			} else {
				$html .= '		<div class="info">';
				$html .= '			<label for="date-price-'.$price['id'].'">'._('Date de début :').' </label>';
				$html .= '			<input class="datepicker date" '.( $price['is-sync'] ? 'disabled="disabled"' :'' ).' type="text" name="date-start-'.$price['id'].'" id="date-price-'.$price['id'].'" value="'.$price['date-start-fr'].'" />';
				$html .= '			&nbsp;<label class="lbl-info-cdt" for="hour-start-'.$price['id'].'">'._('À :').' </label>&nbsp;<input class="hour hourpicker" '.( $price['is-sync'] ? 'disabled="disabled"' :'' ).' type="text" name="hour-start-'.$price['id'].'" id="hour-start-'.$price['id'].'" value="'.$price['hour-start'].'" />';
				if( $promo )
					$html .= '		<span class="info-bulle color-red" title="'._('Ce champ est obligatoire.').'">**</span>';
				$html .= '		</div>';
				$html .= '		<div class="info">';
				$html .= '			<label for="date-end-'.$price['id'].'">'._('Date de fin :').'</label>';
				$html .= '			<input class="datepicker date" '.( $price['is-sync'] ? 'disabled="disabled"' :'' ).' type="text" name="date-end-'.$price['id'].'" id="date-end-'.$price['id'].'" value="'.$price['date-end-fr'].'" />';
				$html .= '			&nbsp;<label class="lbl-info-cdt" for="hour-stop-'.$price['id'].'">'._('À :').' </label>&nbsp;<input class="hour hourpicker" '.( $price['is-sync'] ? 'disabled="disabled"' :'' ).' type="text" name="hour-stop-'.$price['id'].'" id="hour-stop-'.$price['id'].'" value="'.$price['hour-stop'].'" />';
				if( $promo )
					$html .= '		<span class="info-bulle color-red" title="'._('Ce champ est obligatoire.').'">**</span>';
				$html .= '		</div>';
				$html .= '		<div class="bold"></div>';
			}

			if( !$promo && $r_dps && ria_mysql_num_rows($r_dps) ){
				ria_mysql_data_seek( $r_dps, 0 );

				$html .= '<div class="bold">';
				$html .= '	<label for="dps-'.$price['id'].'">'._('Dépôt :').'</label>';
				$html .= '	<select '.( $price['is-sync'] ? 'disabled="disabled"' :'' ).' name="dps-'.$price['id'].'" id="dps-'.$price['id'].'">';
				$html .= '		<option value="0">'._('Aucune restriction').'</div>';
				while( $dps = ria_mysql_fetch_assoc($r_dps) ){
					$selected = '';
					if( $dps['id'] == $price['dps_id'] ){
						$selected = 'selected="selected"';
					}

					$html .= '<option value="'.$dps['id'].'" '.$selected.'>'.htmlspecialchars( $dps['name'] ).'</div>';
				}
				$html .= '	</select>';
				$html .= '</div>';
			}

			if( $prd>0 ) {
				$cdt = prc_conditions_view( $price['id'], TERMS_PRICE, 'product' );
				$price_submit = 'priceSubmit(\'product\', '.$price['id'].', 0, '.$prd.')';
			} elseif( $cat>0 ) {
				$cdt = prc_conditions_view( $price['id'], TERMS_PRICE, 'cat' );
				$price_submit = 'priceSubmit(\'cat\', '.$price['id'].', '.$cat.', 0)';
			} elseif( $promo ) {
				$cdt = prc_conditions_view( $price['id'], TERMS_PRICE, 'prc-cat' );
				$price_submit = 'promoSubmit(\'pmt-cdt\', '.$price['id'].', '.$product['id'].')';
			} else {
				$cdt = prc_conditions_view( $price['id'], TERMS_PRICE, 'prc-cat' );
				$price_submit = 'priceSubmit(\'prc-cat\', '.$price['id'].', 0, 0)';
			}
			if( $cdt!== false )
				$html .= $cdt;
			$html .= '		</td>';
			// Action
			if( !$price['is-sync'] ){
				$html .= '		<td class="action" headers="action">';
				if( !$price['is-sync'] ){
					$html .= '		<input class="action btn-main" type="button" name="prices-save" id="prices-save-'.$price['id'].'" value="'._('Enregistrer').'" onclick="'.$price_submit.';" /><br />';
					$html .= '		<div class="save-load" id="save-load-'.$price['id'].'">';
					$html .= ' 			<img src="/admin/images/loader2.gif" alt="" />';
					$html .= '		</div>';
					if( gu_user_is_authorized('_RGH_ADMIN_PROMO_PRD_DEL') ){
						$html .= '		<a href="#lst-prices" id="prices-del-'.$price['id'].'" class="del edit button button-del" onclick="return delPrice(false, '.$price['id'].', '.$cat.', '.$prd.');">'._('Supprimer').'</a>';
					}
				}
				$html .= '		</td>';
				if( $id==0 )
					$html .= '	</tr>';
			}
			// On retourne au premier résultat MySQL de $r_types
			ria_mysql_data_seek($r_types, 0);
			$count++;
		}
	}
	return $return_only_count ? $count : $html;
}

/// @}
/// @}

// \endcond
