<?php
/**
 * Copyright 2016 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

namespace Google\Cloud\BigQuery;

use GuzzleHttp\Psr7;
use Psr\Http\Message\StreamInterface;

/**
 * Represents a value with a data type of
 * [bytes](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-types#bytes_type).
 *
 * Example:
 * ```
 * use Google\Cloud\BigQuery\BigQueryClient;
 *
 * $bigQuery = new BigQueryClient();
 *
 * $bytes = $bigQuery->bytes('hello world');
 * ```
 */
class Bytes implements ValueInterface
{
    /**
     * @var string|resource|StreamInterface
     */
    private $value;

    /**
     * @param string|resource|StreamInterface $value The bytes value.
     */
    public function __construct($value)
    {
        $this->value = Psr7\stream_for($value);
    }

    /**
     * Get the bytes as a stream.
     *
     * @return StreamInterface
     */
    public function get()
    {
        return $this->value;
    }

    /**
     * Get the type.
     *
     * @return string
     */
    public function type()
    {
        return ValueMapper::TYPE_BYTES;
    }

    /**
     * Format the value as a string.
     *
     * @return string
     */
    public function formatAsString()
    {
        return base64_encode((string) $this->value);
    }

    /**
     * Format the value as a string.
     *
     * @return string
     */
    public function __toString()
    {
        return $this->formatAsString();
    }
}
