{"version": 3, "sources": ["src/header.js", "node_modules/browser-pack/_prelude.js", "index.js", "src/Eventable.js", "src/InteractEvent.js", "src/Interactable.js", "src/Interaction.js", "src/actions/base.js", "src/actions/drag.js", "src/actions/drop.js", "src/actions/gesture.js", "src/actions/resize.js", "src/autoScroll.js", "src/autoStart/InteractableMethods.js", "src/autoStart/base.js", "src/autoStart/drag.js", "src/autoStart/gesture.js", "src/autoStart/hold.js", "src/autoStart/resize.js", "src/defaultOptions.js", "src/index.js", "src/inertia.js", "src/interact.js", "src/interactablePreventDefault.js", "src/modifiers/base.js", "src/modifiers/restrict.js", "src/modifiers/restrictEdges.js", "src/modifiers/restrictSize.js", "src/modifiers/snap.js", "src/modifiers/snapSize.js", "src/pointerEvents/PointerEvent.js", "src/pointerEvents/base.js", "src/pointerEvents/holdRepeat.js", "src/pointerEvents/interactableTargets.js", "src/scope.js", "src/utils/Signals.js", "src/utils/arr.js", "src/utils/browser.js", "src/utils/clone.js", "src/utils/domObjects.js", "src/utils/domUtils.js", "src/utils/events.js", "src/utils/extend.js", "src/utils/getOriginXY.js", "src/utils/hypot.js", "src/utils/index.js", "src/utils/interactionFinder.js", "src/utils/is.js", "src/utils/isWindow.js", "src/utils/pointerExtend.js", "src/utils/pointerUtils.js", "src/utils/raf.js", "src/utils/rect.js", "src/utils/window.js"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;ACLA,ADMA;AACA;;AEPA;;;;;;;;AAQA,IAAI,OAAO,MAAP,KAAkB,WAAtB,EAAmC;AACjC,SAAO,OAAP,GAAiB,UAAU,MAAV,EAAkB;AACjC,YAAQ,oBAAR,EAA8B,IAA9B,CAAmC,MAAnC;;AAEA,WAAO,QAAQ,aAAR,CAAP;AACD,GAJD;AAKD,CAND,MAOK;AACH,SAAO,OAAP,GAAiB,QAAQ,aAAR,CAAjB;AACD;;;;;;;ACjBD,IAAM,SAAS,QAAQ,mBAAR,CAAf;;AAEA,SAAS,yBAAT,CAAoC,KAApC,EAA2C,SAA3C,EAAsD;AACpD,wBAAuB,SAAvB,eAAkC;AAAA;;AAAA,WAAX,SAAW;AAAA,QAAvB,QAAuB;;AAChC,QAAI,MAAM,2BAAV,EAAuC;AAAE;AAAQ;;AAEjD,aAAS,KAAT;AACD;AACF;;IAEK,S;AAEJ,qBAAa,OAAb,EAAsB;AAAA;;AACpB,SAAK,OAAL,GAAe,OAAO,EAAP,EAAW,WAAW,EAAtB,CAAf;AACD;;sBAED,I,iBAAM,K,EAAO;AACX,QAAI,kBAAJ;AACA,QAAM,UAAU,OAAO,MAAM,IAA7B;AACA,QAAM,SAAS,KAAK,MAApB;;AAEA;AACA,QAAK,YAAY,KAAK,MAAM,IAAX,CAAjB,EAAoC;AAClC,gCAA0B,KAA1B,EAAiC,SAAjC;AACD;;AAED;AACA,QAAI,KAAK,OAAL,CAAJ,EAAmB;AACjB,WAAK,OAAL,EAAc,KAAd;AACD;;AAED;AACA,QAAI,CAAC,MAAM,kBAAP,IAA6B,MAA7B,KAAwC,YAAY,OAAO,MAAM,IAAb,CAApD,CAAJ,EAA8E;AAC5E,gCAA0B,KAA1B,EAAiC,SAAjC;AACD;AACF,G;;sBAED,E,eAAI,S,EAAW,Q,EAAU;AACvB;AACA,QAAI,KAAK,SAAL,CAAJ,EAAqB;AACnB,WAAK,SAAL,EAAgB,IAAhB,CAAqB,QAArB;AACD,KAFD,MAGK;AACH,WAAK,SAAL,IAAkB,CAAC,QAAD,CAAlB;AACD;AACF,G;;sBAED,G,gBAAK,S,EAAW,Q,EAAU;AACxB;AACA,QAAM,YAAY,KAAK,SAAL,CAAlB;AACA,QAAM,QAAY,YAAW,UAAU,OAAV,CAAkB,QAAlB,CAAX,GAAyC,CAAC,CAA5D;;AAEA,QAAI,UAAU,CAAC,CAAf,EAAkB;AAChB,gBAAU,MAAV,CAAiB,KAAjB,EAAwB,CAAxB;AACD;;AAED,QAAI,aAAa,UAAU,MAAV,KAAqB,CAAlC,IAAuC,CAAC,QAA5C,EAAsD;AACpD,WAAK,SAAL,IAAkB,SAAlB;AACD;AACF,G;;;;;AAGH,OAAO,OAAP,GAAiB,SAAjB;;;;;;;AC9DA,IAAM,SAAc,QAAQ,gBAAR,CAApB;AACA,IAAM,cAAc,QAAQ,qBAAR,CAApB;AACA,IAAM,WAAc,QAAQ,kBAAR,CAApB;AACA,IAAM,UAAc,QAAQ,iBAAR,EAA2B,GAA3B,EAApB;;IAEM,a;AACJ;AACA,yBAAa,WAAb,EAA0B,KAA1B,EAAiC,MAAjC,EAAyC,KAAzC,EAAgD,OAAhD,EAAyD,OAAzD,EAAkF;AAAA,QAAhB,MAAgB,uEAAP,KAAO;;AAAA;;AAChF,QAAM,SAAc,YAAY,MAAhC;AACA,QAAM,cAAc,CAAC,UAAU,OAAO,OAAjB,IAA4B,QAA7B,EAAuC,WAA3D;AACA,QAAM,SAAc,YAAY,MAAZ,EAAoB,OAApB,EAA6B,MAA7B,CAApB;AACA,QAAM,WAAc,UAAU,OAA9B;AACA,QAAM,SAAc,UAAU,KAA9B;AACA,QAAM,SAAc,WAAU,YAAY,WAAtB,GAAoC,YAAY,SAApE;AACA,QAAM,YAAc,YAAY,SAAhC;;AAEA,cAAU,WAAW,YAAY,OAAjC;;AAEA,QAAM,OAAS,OAAO,EAAP,EAAW,OAAO,IAAlB,CAAf;AACA,QAAM,SAAS,OAAO,EAAP,EAAW,OAAO,MAAlB,CAAf;;AAEA,SAAK,CAAL,IAAU,OAAO,CAAjB;AACA,SAAK,CAAL,IAAU,OAAO,CAAjB;;AAEA,WAAO,CAAP,IAAY,OAAO,CAAnB;AACA,WAAO,CAAP,IAAY,OAAO,CAAnB;;AAEA,SAAK,OAAL,GAAqB,MAAM,OAA3B;AACA,SAAK,MAAL,GAAqB,MAAM,MAA3B;AACA,SAAK,QAAL,GAAqB,MAAM,QAA3B;AACA,SAAK,OAAL,GAAqB,MAAM,OAA3B;AACA,SAAK,MAAL,GAAqB,MAAM,MAA3B;AACA,SAAK,OAAL,GAAqB,MAAM,OAA3B;AACA,SAAK,MAAL,GAAqB,OAArB;AACA,SAAK,aAAL,GAAqB,OAArB;AACA,SAAK,aAAL,GAAqB,WAAW,IAAhC;AACA,SAAK,MAAL,GAAqB,MAArB;AACA,SAAK,IAAL,GAAqB,UAAU,SAAS,EAAnB,CAArB;AACA,SAAK,WAAL,GAAqB,WAArB;AACA,SAAK,YAAL,GAAqB,MAArB;;AAEA,SAAK,EAAL,GAAU,WAAW,YAAY,SAAZ,CAAsB,YAAY,SAAZ,CAAsB,MAAtB,GAA+B,CAArD,CAAX,GACW,UAAU,EAD/B;;AAGA,QAAM,YAAY;AAChB,8BADgB;AAEhB,kBAFgB;AAGhB,oBAHgB;AAIhB,kBAJgB;AAKhB,sBALgB;AAMhB,sBANgB;AAOhB,gBAPgB;AAQhB,oBARgB;AAShB,oBATgB;AAUhB,wBAVgB;AAWhB,oBAXgB;AAYhB,8BAZgB;AAahB,cAAQ;AAbQ,KAAlB;;AAgBA,YAAQ,IAAR,CAAa,QAAb,EAAuB,SAAvB;;AAEA,QAAI,MAAJ,EAAY;AACV;AACA,WAAK,KAAL,GAAa,UAAU,KAAvB;AACA,WAAK,KAAL,GAAa,UAAU,KAAvB;AACA,WAAK,OAAL,GAAe,UAAU,OAAzB;AACA,WAAK,OAAL,GAAe,UAAU,OAAzB;AACD,KAND,MAOK;AACH,WAAK,KAAL,GAAiB,KAAK,CAAtB;AACA,WAAK,KAAL,GAAiB,KAAK,CAAtB;AACA,WAAK,OAAL,GAAiB,OAAO,CAAxB;AACA,WAAK,OAAL,GAAiB,OAAO,CAAxB;AACD;;AAED,SAAK,EAAL,GAAiB,YAAY,WAAZ,CAAwB,IAAxB,CAA6B,CAA7B,GAAiC,OAAO,CAAzD;AACA,SAAK,EAAL,GAAiB,YAAY,WAAZ,CAAwB,IAAxB,CAA6B,CAA7B,GAAiC,OAAO,CAAzD;AACA,SAAK,QAAL,GAAiB,YAAY,WAAZ,CAAwB,MAAxB,CAA+B,CAA/B,GAAmC,OAAO,CAA3D;AACA,SAAK,QAAL,GAAiB,YAAY,WAAZ,CAAwB,MAAxB,CAA+B,CAA/B,GAAmC,OAAO,CAA3D;;AAEA,YAAQ,IAAR,CAAa,WAAb,EAA0B,SAA1B;;AAEA,SAAK,SAAL,GAAiB,OAAO,SAAxB;AACA,SAAK,EAAL,GAAiB,YAAY,YAAZ,CAAyB,SAA1C;AACA,SAAK,QAAL,GAAiB,KAAK,SAAL,GAAiB,KAAK,EAAvC;;AAEA;AACA,SAAK,KAAL,GAAa,YAAY,YAAZ,CAAyB,WAAzB,EAAsC,KAAnD;AACA,SAAK,SAAL,GAAiB,YAAY,YAAZ,CAAyB,WAAzB,EAAsC,EAAvD;AACA,SAAK,SAAL,GAAiB,YAAY,YAAZ,CAAyB,WAAzB,EAAsC,EAAvD;;AAEA,SAAK,KAAL,GAAc,UAAU,UAAU,cAArB,GAAsC,KAAK,QAAL,EAAtC,GAAwD,IAArE;;AAEA,YAAQ,IAAR,CAAa,KAAb,EAAoB,SAApB;AACD;;0BAED,Q,uBAAY;AACV,QAAM,cAAc,KAAK,WAAzB;;AAEA,QAAI,YAAY,SAAZ,CAAsB,KAAtB,GAA8B,GAA9B,IACG,KAAK,SAAL,GAAiB,YAAY,SAAZ,CAAsB,SAAvC,GAAmD,GAD1D,EAC+D;AAC7D,aAAO,IAAP;AACD;;AAED,QAAI,QAAQ,MAAM,KAAK,KAAL,CAAW,YAAY,SAAZ,CAAsB,SAAjC,EAA4C,YAAY,SAAZ,CAAsB,SAAlE,CAAN,GAAqF,KAAK,EAAtG;AACA,QAAM,UAAU,IAAhB;;AAEA,QAAI,QAAQ,CAAZ,EAAe;AACb,eAAS,GAAT;AACD;;AAED,QAAM,OAAO,MAAM,OAAN,IAAiB,KAAjB,IAA0B,QAAQ,MAAM,OAArD;AACA,QAAM,KAAO,MAAM,OAAN,IAAiB,KAAjB,IAA0B,QAAQ,MAAM,OAArD;;AAEA,QAAM,QAAQ,CAAC,IAAD,KAAU,MAAM,OAAN,IAAiB,KAAjB,IAA0B,QAAS,KAAK,OAAlD,CAAd;AACA,QAAM,OAAQ,CAAC,EAAD,IAAW,KAAK,OAAL,IAAgB,KAA3B,IAAoC,QAAQ,MAAM,OAAhE;;AAEA,WAAO;AACL,YADK;AAEL,gBAFK;AAGL,gBAHK;AAIL,kBAJK;AAKL,kBALK;AAML,aAAO,YAAY,SAAZ,CAAsB,KANxB;AAOL,gBAAU;AACR,WAAG,YAAY,SAAZ,CAAsB,SADjB;AAER,WAAG,YAAY,SAAZ,CAAsB;AAFjB;AAPL,KAAP;AAYD,G;;0BAED,c,6BAAkB,CAAE,C;;AAEpB;;;0BACA,wB,uCAA4B;AAC1B,SAAK,2BAAL,GAAmC,KAAK,kBAAL,GAA0B,IAA7D;AACD,G;;AAED;;;0BACA,e,8BAAmB;AACjB,SAAK,kBAAL,GAA0B,IAA1B;AACD,G;;;;;AAGH,QAAQ,EAAR,CAAW,WAAX,EAAwB,gBAA0D;AAAA,MAA9C,MAA8C,QAA9C,MAA8C;AAAA,MAAtC,WAAsC,QAAtC,WAAsC;AAAA,MAAzB,QAAyB,QAAzB,QAAyB;AAAA,MAAf,WAAe,QAAf,WAAe;;AAChF,MAAM,YAAY,WAAU,MAAV,GAAmB,YAAY,SAAjD;;AAEA,MAAI,gBAAgB,QAApB,EAA8B;AAC5B,WAAO,EAAP,GAAY,OAAO,OAAP,GAAiB,UAAU,OAAvC;AACA,WAAO,EAAP,GAAY,OAAO,OAAP,GAAiB,UAAU,OAAvC;AACD,GAHD,MAIK;AACH,WAAO,EAAP,GAAY,OAAO,KAAP,GAAe,UAAU,KAArC;AACA,WAAO,EAAP,GAAY,OAAO,KAAP,GAAe,UAAU,KAArC;AACD;AACF,CAXD;;AAaA,cAAc,OAAd,GAAwB,OAAxB;;AAEA,OAAO,OAAP,GAAiB,aAAjB;;;;;;;AChKA,IAAM,QAAY,QAAQ,eAAR,CAAlB;AACA,IAAM,KAAY,QAAQ,YAAR,CAAlB;AACA,IAAM,SAAY,QAAQ,gBAAR,CAAlB;AACA,IAAM,SAAY,QAAQ,gBAAR,CAAlB;AACA,IAAM,UAAY,QAAQ,gBAAR,CAAlB;AACA,IAAM,QAAY,QAAQ,SAAR,CAAlB;AACA,IAAM,YAAY,QAAQ,aAAR,CAAlB;AACA,IAAM,WAAY,QAAQ,kBAAR,CAAlB;AACA,IAAM,UAAY,QAAQ,iBAAR,EAA2B,GAA3B,EAAlB;;eAOuB,QAAQ,kBAAR,C;IAJrB,c,YAAA,c;IACA,Y,YAAA,Y;IACA,W,YAAA,W;IACA,e,YAAA,e;;gBAEqB,QAAQ,gBAAR,C;IAAf,S,aAAA,S;;gBACe,QAAQ,aAAR,C;IAAf,Q,aAAA,Q;;gBACe,QAAQ,iBAAR,C;IAAf,U,aAAA,U;;AAER;;;AACA,MAAM,aAAN,GAAsB,EAAtB;;IAEM,Y;AACJ;AACA,wBAAa,MAAb,EAAqB,OAArB,EAA8B;AAAA;;AAC5B,cAAU,WAAW,EAArB;;AAEA,SAAK,MAAL,GAAgB,MAAhB;AACA,SAAK,MAAL,GAAgB,IAAI,SAAJ,EAAhB;AACA,SAAK,QAAL,GAAgB,QAAQ,OAAR,IAAmB,MAAM,QAAzC;AACA,SAAK,IAAL,GAAgB,UAAU,YAAY,MAAZ,IAAqB,KAAK,QAA1B,GAAqC,MAA/C,CAAhB;AACA,SAAK,IAAL,GAAgB,KAAK,IAAL,CAAU,QAA1B;;AAEA,YAAQ,IAAR,CAAa,KAAb,EAAoB;AAClB,oBADkB;AAElB,sBAFkB;AAGlB,oBAAc,IAHI;AAIlB,WAAK,KAAK;AAJQ,KAApB;;AAOA,UAAM,WAAN,CAAmB,KAAK,IAAxB,EAA8B,KAAK,IAAnC;;AAEA,UAAM,aAAN,CAAoB,IAApB,CAAyB,IAAzB;;AAEA,SAAK,GAAL,CAAS,OAAT;AACD;;yBAED,W,wBAAa,M,EAAQ,M,EAAQ;AAC3B,QAAM,WAAW,OAAO,MAAxB;;AAEA,QAAI,GAAG,QAAH,CAAY,OAAO,OAAnB,CAAJ,EAAwC;AAAE,WAAK,MAAL,CAAY,WAAW,OAAvB,IAA0C,OAAO,OAAjD;AAAoE;AAC9G,QAAI,GAAG,QAAH,CAAY,OAAO,MAAnB,CAAJ,EAAwC;AAAE,WAAK,MAAL,CAAY,WAAW,MAAvB,IAA0C,OAAO,MAAjD;AAAoE;AAC9G,QAAI,GAAG,QAAH,CAAY,OAAO,KAAnB,CAAJ,EAAwC;AAAE,WAAK,MAAL,CAAY,WAAW,KAAvB,IAA0C,OAAO,KAAjD;AAAoE;AAC9G,QAAI,GAAG,QAAH,CAAY,OAAO,cAAnB,CAAJ,EAAwC;AAAE,WAAK,MAAL,CAAY,WAAW,cAAvB,IAA0C,OAAO,cAAjD;AAAoE;;AAE9G,WAAO,IAAP;AACD,G;;yBAED,Y,yBAAc,M,EAAQ,O,EAAS;AAC7B;AACA,SAAK,IAAM,MAAX,IAAqB,OAArB,EAA8B;AAC5B;AACA,UAAI,UAAU,SAAS,MAAT,CAAd,EAAgC;AAC9B;AACA,YAAI,GAAG,MAAH,CAAU,QAAQ,MAAR,CAAV,CAAJ,EAAgC;AAC9B;AACA,eAAK,OAAL,CAAa,MAAb,EAAqB,MAArB,IAA+B,MAAM,KAAK,OAAL,CAAa,MAAb,EAAqB,MAArB,KAAgC,EAAtC,CAA/B;AACA,iBAAO,KAAK,OAAL,CAAa,MAAb,EAAqB,MAArB,CAAP,EAAqC,QAAQ,MAAR,CAArC;;AAEA,cAAI,GAAG,MAAH,CAAU,SAAS,SAAT,CAAmB,MAAnB,CAAV,KAAyC,aAAa,SAAS,SAAT,CAAmB,MAAnB,CAA1D,EAAsF;AACpF,iBAAK,OAAL,CAAa,MAAb,EAAqB,MAArB,EAA6B,OAA7B,GAAuC,QAAQ,MAAR,EAAgB,OAAhB,KAA4B,KAA5B,GAAmC,KAAnC,GAA2C,IAAlF;AACD;AACF,SARD,MASK,IAAI,GAAG,IAAH,CAAQ,QAAQ,MAAR,CAAR,KAA4B,GAAG,MAAH,CAAU,SAAS,SAAT,CAAmB,MAAnB,CAAV,CAAhC,EAAuE;AAC1E,eAAK,OAAL,CAAa,MAAb,EAAqB,MAArB,EAA6B,OAA7B,GAAuC,QAAQ,MAAR,CAAvC;AACD,SAFI,MAGA,IAAI,QAAQ,MAAR,MAAoB,SAAxB,EAAmC;AACtC;AACA,eAAK,OAAL,CAAa,MAAb,EAAqB,MAArB,IAA+B,QAAQ,MAAR,CAA/B;AACD;AACF;AACF;AACF,G;;AAED;;;;;;;;;yBAOA,O,oBAAS,O,EAAS;AAChB,cAAU,WAAW,KAAK,MAA1B;;AAEA,QAAI,GAAG,MAAH,CAAU,KAAK,MAAf,KAA0B,CAAE,GAAG,OAAH,CAAW,OAAX,CAAhC,EAAsD;AACpD,gBAAU,KAAK,QAAL,CAAc,aAAd,CAA4B,KAAK,MAAjC,CAAV;AACD;;AAED,WAAO,eAAe,OAAf,CAAP;AACD,G;;AAED;;;;;;;;;;yBAQA,W,wBAAa,O,EAAS;AACpB,QAAI,GAAG,QAAH,CAAY,OAAZ,CAAJ,EAA0B;AACxB,WAAK,OAAL,GAAe,OAAf;;AAEA,aAAO,IAAP;AACD;;AAED,QAAI,YAAY,IAAhB,EAAsB;AACpB,aAAO,KAAK,OAAL,CAAa,OAApB;;AAEA,aAAO,IAAP;AACD;;AAED,WAAO,KAAK,OAAZ;AACD,G;;yBAED,iB,8BAAmB,U,EAAY,Q,EAAU;AACvC,QAAI,YAAY,QAAZ,KAAyB,GAAG,MAAH,CAAU,QAAV,CAA7B,EAAkD;AAChD,WAAK,OAAL,CAAa,UAAb,IAA2B,QAA3B;;AAEA,4BAAqB,QAAQ,KAA7B,eAAoC;AAAA;;AAAA,eAAf,QAAQ,KAAO;AAAA,YAAzB,MAAyB;;AAClC,aAAK,OAAL,CAAa,MAAb,EAAqB,UAArB,IAAmC,QAAnC;AACD;;AAED,aAAO,IAAP;AACD;;AAED,WAAO,KAAK,OAAL,CAAa,UAAb,CAAP;AACD,G;;AAED;;;;;;;;;;;;yBAUA,M,mBAAQ,Q,EAAU;AAChB,WAAO,KAAK,iBAAL,CAAuB,QAAvB,EAAiC,QAAjC,CAAP;AACD,G;;AAED;;;;;;;;;;yBAQA,W,wBAAa,Q,EAAU;AACrB,QAAI,aAAa,MAAb,IAAuB,aAAa,QAAxC,EAAkD;AAChD,WAAK,OAAL,CAAa,WAAb,GAA2B,QAA3B;;AAEA,aAAO,IAAP;AACD;;AAED,WAAO,KAAK,OAAL,CAAa,WAApB;AACD,G;;AAED;;;;;;;;yBAMA,O,sBAAW;AACT,WAAO,KAAK,QAAZ;AACD,G;;yBAED,S,sBAAW,O,EAAS;AAClB,WAAQ,KAAK,QAAL,KAAkB,QAAQ,aAA1B,IACG,aAAa,KAAK,QAAlB,EAA4B,OAA5B,CADX;AAED,G;;AAED;;;;;;;;;;yBAQA,I,iBAAM,M,EAAQ;AACZ,SAAK,MAAL,CAAY,IAAZ,CAAiB,MAAjB;;AAEA,WAAO,IAAP;AACD,G;;yBAED,c,2BAAgB,M,EAAQ,S,EAAW,Q,EAAU,O,EAAS;AACpD,QAAI,GAAG,MAAH,CAAU,SAAV,KAAwB,UAAU,MAAV,CAAiB,GAAjB,MAA0B,CAAC,CAAvD,EAA0D;AACxD,kBAAY,UAAU,IAAV,GAAiB,KAAjB,CAAuB,IAAvB,CAAZ;AACD;;AAED,QAAI,GAAG,KAAH,CAAS,SAAT,CAAJ,EAAyB;AACvB,8BAAmB,SAAnB,gBAA8B;AAAA;;AAAA,gBAAX,SAAW;AAAA,YAAnB,IAAmB;;AAC5B,aAAK,MAAL,EAAa,IAAb,EAAmB,QAAnB,EAA6B,OAA7B;AACD;;AAED,aAAO,IAAP;AACD;;AAED,QAAI,GAAG,MAAH,CAAU,SAAV,CAAJ,EAA0B;AACxB,WAAK,IAAM,IAAX,IAAmB,SAAnB,EAA8B;AAC5B,aAAK,MAAL,EAAa,IAAb,EAAmB,UAAU,IAAV,CAAnB,EAAoC,QAApC;AACD;;AAED,aAAO,IAAP;AACD;AACF,G;;AAED;;;;;;;;;;;;yBAUA,E,eAAI,S,EAAW,Q,EAAU,O,EAAS;AAChC,QAAI,KAAK,cAAL,CAAoB,IAApB,EAA0B,SAA1B,EAAqC,QAArC,EAA+C,OAA/C,CAAJ,EAA6D;AAC3D,aAAO,IAAP;AACD;;AAED,QAAI,cAAc,OAAlB,EAA2B;AAAE,kBAAY,UAAZ;AAAyB;;AAEtD,QAAI,SAAS,aAAa,UAAtB,EAAkC,SAAlC,CAAJ,EAAkD;AAChD,WAAK,MAAL,CAAY,EAAZ,CAAe,SAAf,EAA0B,QAA1B;AACD;AACD;AAHA,SAIK,IAAI,GAAG,MAAH,CAAU,KAAK,MAAf,CAAJ,EAA4B;AAC/B,eAAO,WAAP,CAAmB,KAAK,MAAxB,EAAgC,KAAK,QAArC,EAA+C,SAA/C,EAA0D,QAA1D,EAAoE,OAApE;AACD,OAFI,MAGA;AACH,eAAO,GAAP,CAAW,KAAK,MAAhB,EAAwB,SAAxB,EAAmC,QAAnC,EAA6C,OAA7C;AACD;;AAED,WAAO,IAAP;AACD,G;;AAED;;;;;;;;;;;;yBAUA,G,gBAAK,S,EAAW,Q,EAAU,O,EAAS;AACjC,QAAI,KAAK,cAAL,CAAoB,KAApB,EAA2B,SAA3B,EAAsC,QAAtC,EAAgD,OAAhD,CAAJ,EAA8D;AAC5D,aAAO,IAAP;AACD;;AAED,QAAI,cAAc,OAAlB,EAA2B;AAAE,kBAAY,UAAZ;AAAyB;;AAEtD;AACA,QAAI,SAAS,aAAa,UAAtB,EAAkC,SAAlC,CAAJ,EAAkD;AAChD,WAAK,MAAL,CAAY,GAAZ,CAAgB,SAAhB,EAA2B,QAA3B;AACD;AACD;AAHA,SAIK,IAAI,GAAG,MAAH,CAAU,KAAK,MAAf,CAAJ,EAA4B;AAC/B,eAAO,cAAP,CAAsB,KAAK,MAA3B,EAAmC,KAAK,QAAxC,EAAkD,SAAlD,EAA6D,QAA7D,EAAuE,OAAvE;AACD;AACD;AAHK,WAIA;AACH,iBAAO,MAAP,CAAc,KAAK,MAAnB,EAA2B,SAA3B,EAAsC,QAAtC,EAAgD,OAAhD;AACD;;AAED,WAAO,IAAP;AACD,G;;AAED;;;;;;;;yBAMA,G,gBAAK,O,EAAS;AACZ,QAAI,CAAC,GAAG,MAAH,CAAU,OAAV,CAAL,EAAyB;AACvB,gBAAU,EAAV;AACD;;AAED,SAAK,OAAL,GAAe,MAAM,SAAS,IAAf,CAAf;;AAEA,QAAM,aAAa,MAAM,SAAS,SAAf,CAAnB;;AAEA,SAAK,IAAM,UAAX,IAAyB,QAAQ,UAAjC,EAA6C;AAC3C,UAAM,aAAa,QAAQ,UAAR,CAAmB,UAAnB,CAAnB;;AAEA,WAAK,OAAL,CAAa,UAAb,IAA2B,MAAM,SAAS,UAAT,CAAN,CAA3B;;AAEA,WAAK,YAAL,CAAkB,UAAlB,EAA8B,UAA9B;;AAEA,WAAK,UAAL,EAAiB,QAAQ,UAAR,CAAjB;AACD;;AAED,4BAAsB,aAAa,eAAnC,gBAAoD;AAAA;;AAAA,cAA9B,aAAa,eAAiB;AAAA,UAAzC,OAAyC;;AAClD,WAAK,OAAL,CAAa,OAAb,IAAwB,SAAS,IAAT,CAAc,OAAd,CAAxB;;AAEA,UAAI,WAAW,OAAf,EAAwB;AACtB,aAAK,OAAL,EAAc,QAAQ,OAAR,CAAd;AACD;AACF;;AAED,YAAQ,IAAR,CAAa,KAAb,EAAoB;AAClB,sBADkB;AAElB,oBAAc;AAFI,KAApB;;AAKA,WAAO,IAAP;AACD,G;;AAED;;;;;;;;yBAMA,K,oBAAS;AACP,WAAO,MAAP,CAAc,KAAK,MAAnB,EAA2B,KAA3B;;AAEA,QAAI,GAAG,MAAH,CAAU,KAAK,MAAf,CAAJ,EAA4B;AAC1B;AACA,WAAK,IAAM,IAAX,IAAmB,OAAO,eAA1B,EAA2C;AACzC,YAAM,YAAY,OAAO,eAAP,CAAuB,IAAvB,CAAlB;;AAEA,YAAI,UAAU,SAAV,CAAoB,CAApB,MAA2B,KAAK,MAAhC,IACG,UAAU,QAAV,CAAmB,CAAnB,MAA0B,KAAK,QADtC,EACgD;;AAE9C,oBAAU,SAAV,CAAoB,MAApB,CAA2B,CAA3B,EAA8B,CAA9B;AACA,oBAAU,QAAV,CAAoB,MAApB,CAA2B,CAA3B,EAA8B,CAA9B;AACA,oBAAU,SAAV,CAAoB,MAApB,CAA2B,CAA3B,EAA8B,CAA9B;;AAEA;AACA,cAAI,CAAC,UAAU,SAAV,CAAoB,MAAzB,EAAiC;AAC/B,sBAAU,IAAV,IAAkB,IAAlB;AACD;AACF;;AAED,eAAO,MAAP,CAAc,KAAK,QAAnB,EAA6B,IAA7B,EAAmC,OAAO,gBAA1C;AACA,eAAO,MAAP,CAAc,KAAK,QAAnB,EAA6B,IAA7B,EAAmC,OAAO,kBAA1C,EAA8D,IAA9D;AACD;AACF,KArBD,MAsBK;AACH,aAAO,MAAP,CAAc,IAAd,EAAoB,KAApB;AACD;;AAED,YAAQ,IAAR,CAAa,OAAb,EAAsB,EAAE,cAAc,IAAhB,EAAtB;;AAEA,UAAM,aAAN,CAAoB,MAApB,CAA2B,MAAM,aAAN,CAAoB,OAApB,CAA4B,IAA5B,CAA3B,EAA8D,CAA9D;;AAEA;AACA,6BAA0B,MAAM,YAAN,IAAsB,EAAhD,iBAAoD;AAAA;;AAAA,eAA1B,MAAM,YAAN,IAAsB,EAAI;AAAA,UAAzC,WAAyC;;AAClD,UAAI,YAAY,MAAZ,KAAuB,IAAvB,IAA+B,YAAY,WAAZ,EAA/B,IAA4D,CAAC,YAAY,OAA7E,EAAsF;AACpF,oBAAY,IAAZ;AACD;AACF;;AAED,WAAO,MAAM,QAAb;AACD,G;;;;;AAGH,MAAM,aAAN,CAAoB,cAApB,GAAqC,SAAS,cAAT,CAAyB,MAAzB,EAAiC,OAAjC,EAA0C;AAC7E,YAAU,WAAW,MAAM,QAA3B;;AAEA,OAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,KAAK,MAAzB,EAAiC,GAAjC,EAAsC;AACpC,QAAM,eAAe,KAAK,CAAL,CAArB;;AAEA,QAAI,aAAa,MAAb,KAAwB,MAAxB,IAAkC,aAAa,QAAb,KAA0B,OAAhE,EAAyE;AACvE,aAAO,CAAP;AACD;AACF;AACD,SAAO,CAAC,CAAR;AACD,CAXD;;AAaA,MAAM,aAAN,CAAoB,GAApB,GAA0B,SAAS,eAAT,CAA0B,OAA1B,EAAmC,OAAnC,EAA4C,kBAA5C,EAAgE;AACxF,MAAM,MAAM,KAAK,KAAK,cAAL,CAAoB,OAApB,EAA6B,WAAW,QAAQ,OAAhD,CAAL,CAAZ;;AAEA,SAAO,QAAQ,GAAG,MAAH,CAAU,OAAV,KAAsB,kBAAtB,IAA4C,IAAI,SAAJ,CAAc,OAAd,CAApD,IAA6E,GAA7E,GAAmF,IAA1F;AACD,CAJD;;AAMA,MAAM,aAAN,CAAoB,YAApB,GAAmC,UAAU,OAAV,EAAmB,QAAnB,EAA6B;AAC9D,0BAA2B,IAA3B,gBAAiC;AAAA;;AAAA,YAAN,IAAM;AAAA,QAAtB,YAAsB;;AAC/B,QAAI,YAAJ;;AAEA,QAAI,CAAC,GAAG,MAAH,CAAU,aAAa,MAAvB;AACD;AADC,MAEE,GAAG,OAAH,CAAW,OAAX,KAAuB,gBAAgB,OAAhB,EAAyB,aAAa,MAAtC,CAFzB;AAGD;AACE,gBAAY,aAAa,MAJ3B;AAKA;AACE,iBAAa,SAAb,CAAuB,OAAvB,CANN,EAMwC;AACtC,YAAM,SAAS,YAAT,CAAN;AACD;;AAED,QAAI,QAAQ,SAAZ,EAAuB;AACrB,aAAO,GAAP;AACD;AACF;AACF,CAlBD;;AAoBA;AACA,aAAa,UAAb,GAA0B,MAAM,UAAN,GAAmB,EAA7C;;AAEA,aAAa,OAAb,GAAuB,OAAvB;;AAEA,aAAa,eAAb,GAA+B,CAAE,aAAF,EAAiB,QAAjB,EAA2B,gBAA3B,EAA6C,aAA7C,CAA/B;;AAEA,OAAO,OAAP,GAAiB,YAAjB;;;;;;;ACxaA,IAAM,QAAa,QAAQ,SAAR,CAAnB;AACA,IAAM,QAAa,QAAQ,SAAR,CAAnB;AACA,IAAM,SAAa,QAAQ,gBAAR,CAAnB;AACA,IAAM,UAAa,QAAQ,iBAAR,CAAnB;AACA,IAAM,aAAa,QAAQ,oBAAR,CAAnB;AACA,IAAM,SAAa,QAAQ,2BAAR,CAAnB;AACA,IAAM,UAAa,QAAQ,iBAAR,EAA2B,GAA3B,EAAnB;;AAEA,IAAM,YAAc,EAApB;AACA,IAAM,cAAc,CAClB,aADkB,EACH,aADG,EACY,WADZ,EAElB,eAFkB,EAED,eAFC,CAApB;;AAKA;AACA,IAAI,gBAAgB,CAApB;;AAEA;AACA,MAAM,YAAN,GAAqB,EAArB;;IAEM,W;AACJ;AACA,6BAA8B;AAAA,QAAf,WAAe,QAAf,WAAe;;AAAA;;AAC5B,SAAK,MAAL,GAAqB,IAArB,CAD4B,CACD;AAC3B,SAAK,OAAL,GAAqB,IAArB,CAF4B,CAED;;AAE3B,SAAK,QAAL,GAAqB,EAAM;AACzB,YAAO,IADY;AAEnB,YAAO,IAFY;AAGnB,aAAO;AAHY,KAArB;;AAMA;AACA,SAAK,QAAL,GAAmB,EAAnB;AACA,SAAK,UAAL,GAAmB,EAAnB;AACA,SAAK,WAAL,GAAmB,EAAnB;AACA,SAAK,SAAL,GAAmB,EAAnB;;AAEA;AACA,SAAK,UAAL,GAAkB;AAChB,YAAW,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EADK;AAEhB,cAAW,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EAFK;AAGhB,iBAAW;AAHK,KAAlB;AAKA;AACA,SAAK,SAAL,GAAiB;AACf,YAAW,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EADI;AAEf,cAAW,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EAFI;AAGf,iBAAW;AAHI,KAAjB;;AAMA;AACA,SAAK,WAAL,GAAmB;AACjB,YAAW,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EADM;AAEjB,cAAW,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EAFM;AAGjB,iBAAW;AAHM,KAAnB;;AAMA;AACA,SAAK,YAAL,GAAoB;AAClB,YAAW,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EAAc,IAAI,CAAlB,EAAqB,IAAI,CAAzB,EAA4B,OAAO,CAAnC,EADO;AAElB,cAAW,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EAAc,IAAI,CAAlB,EAAqB,IAAI,CAAzB,EAA4B,OAAO,CAAnC,EAFO;AAGlB,iBAAW;AAHO,KAApB;;AAMA,SAAK,SAAL,GAAmB,IAAnB,CA3C4B,CA2CA;AAC5B,SAAK,WAAL,GAAmB,EAAnB;;AAEA,SAAK,YAAL,GAAuB,IAAvB;AACA,SAAK,eAAL,GAAuB,IAAvB;;AAEA,SAAK,SAAL,GAAiB,IAAjB,CAjD4B,CAiDA;;AAE5B,SAAK,aAAL,GAAuB,KAAvB;AACA,SAAK,eAAL,GAAuB,KAAvB;AACA,SAAK,YAAL,GAAuB,KAAvB;AACA,SAAK,OAAL,GAAuB,KAAvB;;AAEA,SAAK,WAAL,GAAmB,WAAnB;;AAEA,YAAQ,IAAR,CAAa,KAAb,EAAoB,IAApB;;AAEA,UAAM,YAAN,CAAmB,IAAnB,CAAwB,IAAxB;AACD;;wBAED,W,wBAAa,O,EAAS,K,EAAO,W,EAAa;AACxC,QAAM,eAAe,KAAK,aAAL,CAAmB,OAAnB,EAA4B,KAA5B,EAAmC,IAAnC,CAArB;;AAEA,YAAQ,IAAR,CAAa,MAAb,EAAqB;AACnB,sBADmB;AAEnB,kBAFmB;AAGnB,8BAHmB;AAInB,gCAJmB;AAKnB,mBAAa;AALM,KAArB;AAOD,G;;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBA+BA,K,kBAAO,M,EAAQ,M,EAAQ,O,EAAS;AAC9B,QAAI,KAAK,WAAL,MACG,CAAC,KAAK,aADT,IAEG,KAAK,UAAL,CAAgB,MAAhB,IAA0B,OAAO,IAAP,KAAgB,SAAhB,GAA2B,CAA3B,GAA+B,CAAzD,CAFP,EAEoE;AAClE;AACD;;AAED;AACA;AACA,QAAI,MAAM,YAAN,CAAmB,OAAnB,CAA2B,IAA3B,MAAqC,CAAC,CAA1C,EAA6C;AAC3C,YAAM,YAAN,CAAmB,IAAnB,CAAwB,IAAxB;AACD;;AAED,UAAM,UAAN,CAAiB,KAAK,QAAtB,EAAgC,MAAhC;AACA,SAAK,MAAL,GAAsB,MAAtB;AACA,SAAK,OAAL,GAAsB,OAAtB;;AAEA,YAAQ,IAAR,CAAa,cAAb,EAA6B;AAC3B,mBAAa,IADc;AAE3B,aAAO,KAAK;AAFe,KAA7B;AAID,G;;wBAED,W,wBAAa,O,EAAS,K,EAAO,W,EAAa;AACxC,QAAI,CAAC,KAAK,UAAV,EAAsB;AACpB,WAAK,aAAL,CAAmB,OAAnB;AACA,YAAM,SAAN,CAAgB,KAAK,SAArB,EAAgC,KAAK,QAArC;AACD;;AAED,QAAM,gBAAiB,KAAK,SAAL,CAAe,IAAf,CAAoB,CAApB,KAA0B,KAAK,UAAL,CAAgB,IAAhB,CAAqB,CAA/C,IACG,KAAK,SAAL,CAAe,IAAf,CAAoB,CAApB,KAA0B,KAAK,UAAL,CAAgB,IAAhB,CAAqB,CADlD,IAEG,KAAK,SAAL,CAAe,MAAf,CAAsB,CAAtB,KAA4B,KAAK,UAAL,CAAgB,MAAhB,CAAuB,CAFtD,IAGG,KAAK,SAAL,CAAe,MAAf,CAAsB,CAAtB,KAA4B,KAAK,UAAL,CAAgB,MAAhB,CAAuB,CAH7E;;AAKA,QAAI,WAAJ;AACA,QAAI,WAAJ;;AAEA;AACA,QAAI,KAAK,aAAL,IAAsB,CAAC,KAAK,eAAhC,EAAiD;AAC/C,WAAK,KAAK,SAAL,CAAe,MAAf,CAAsB,CAAtB,GAA0B,KAAK,WAAL,CAAiB,MAAjB,CAAwB,CAAvD;AACA,WAAK,KAAK,SAAL,CAAe,MAAf,CAAsB,CAAtB,GAA0B,KAAK,WAAL,CAAiB,MAAjB,CAAwB,CAAvD;;AAEA,WAAK,eAAL,GAAuB,MAAM,KAAN,CAAY,EAAZ,EAAgB,EAAhB,IAAsB,YAAY,oBAAzD;AACD;;AAED,QAAM,YAAY;AAChB,sBADgB;AAEhB,oBAAc,KAAK,eAAL,CAAqB,OAArB,CAFE;AAGhB,kBAHgB;AAIhB,8BAJgB;AAKhB,YALgB;AAMhB,YANgB;AAOhB,iBAAW,aAPK;AAQhB,mBAAa,IARG;AAShB,6BAAuB,KAAK,WAAL;AATP,KAAlB;;AAYA,QAAI,CAAC,aAAL,EAAoB;AAClB;AACA,YAAM,cAAN,CAAqB,KAAK,YAA1B,EAAwC,KAAK,UAA7C,EAAyD,KAAK,SAA9D;AACD;;AAED,YAAQ,IAAR,CAAa,MAAb,EAAqB,SAArB;;AAEA,QAAI,CAAC,aAAL,EAAoB;AAClB;AACA,UAAI,KAAK,WAAL,EAAJ,EAAwB;AACtB,aAAK,MAAL,CAAY,SAAZ;AACD;;AAED,UAAI,KAAK,eAAT,EAA0B;AACxB,cAAM,UAAN,CAAiB,KAAK,UAAtB,EAAkC,KAAK,SAAvC;AACD;AACF;AACF,G;;AAED;;;;;;;;;;;;;;;;;;;;wBAkBA,M,mBAAQ,S,EAAW;AACjB,gBAAY,MAAM,MAAN,CAAa;AACvB,eAAS,KAAK,QAAL,CAAc,CAAd,CADc;AAEvB,aAAO,KAAK,SAFW;AAGvB,mBAAa,KAAK,YAHK;AAIvB,mBAAa;AAJU,KAAb,EAKT,aAAa,EALJ,CAAZ;;AAOA,YAAQ,IAAR,CAAa,oBAAb,EAAmC,SAAnC;;AAEA,QAAI,CAAC,KAAK,aAAV,EAAyB;AACvB,cAAQ,IAAR,CAAa,aAAb,EAA4B,SAA5B;AACD;;AAED,SAAK,aAAL,GAAqB,KAArB;AACD,G;;AAED;;;wBACA,S,sBAAW,O,EAAS,K,EAAO,W,EAAa,c,EAAgB;AACtD,QAAM,eAAe,KAAK,eAAL,CAAqB,OAArB,CAArB;;AAEA,YAAQ,IAAR,CAAa,WAAW,IAAX,CAAgB,MAAM,IAAtB,IAA6B,QAA7B,GAAwC,IAArD,EAA2D;AACzD,sBADyD;AAEzD,gCAFyD;AAGzD,kBAHyD;AAIzD,8BAJyD;AAKzD,oCALyD;AAMzD,mBAAa;AAN4C,KAA3D;;AASA,QAAI,CAAC,KAAK,UAAV,EAAsB;AACpB,WAAK,GAAL,CAAS,KAAT;AACD;;AAED,SAAK,aAAL,GAAqB,KAArB;AACA,SAAK,aAAL,CAAmB,OAAnB,EAA4B,KAA5B;AACD,G;;AAED;;;;;;;;;;;;;;;;;;;;;wBAmBA,G,gBAAK,K,EAAO;AACV,SAAK,OAAL,GAAe,IAAf;;AAEA,YAAQ,SAAS,KAAK,SAAtB;;AAEA,QAAI,KAAK,WAAL,EAAJ,EAAwB;AACtB,cAAQ,IAAR,CAAa,YAAb,EAA2B;AACzB,oBADyB;AAEzB,qBAAa;AAFY,OAA3B;AAID;;AAED,SAAK,IAAL;AACA,SAAK,OAAL,GAAe,KAAf;AACD,G;;wBAED,a,4BAAiB;AACf,WAAO,KAAK,YAAL,GAAmB,KAAK,QAAL,CAAc,IAAjC,GAAuC,IAA9C;AACD,G;;wBAED,W,0BAAe;AACb,WAAO,KAAK,YAAZ;AACD,G;;AAED;;;wBACA,I,mBAAQ;AACN,YAAQ,IAAR,CAAa,MAAb,EAAqB,EAAE,aAAa,IAAf,EAArB;;AAEA,QAAI,KAAK,YAAT,EAAuB;AACrB,cAAQ,IAAR,CAAa,aAAb,EAA4B,EAAE,aAAa,IAAf,EAA5B;AACA,cAAQ,IAAR,CAAa,UAAU,KAAK,QAAL,CAAc,IAArC,EAA2C,EAAE,aAAa,IAAf,EAA3C;AACD;;AAED,SAAK,MAAL,GAAc,KAAK,OAAL,GAAe,IAA7B;;AAEA,SAAK,YAAL,GAAoB,KAApB;AACA,SAAK,QAAL,CAAc,IAAd,GAAqB,KAAK,SAAL,GAAiB,IAAtC;AACD,G;;wBAED,e,4BAAiB,O,EAAS;AACxB;AACA,QAAI,KAAK,WAAL,KAAqB,OAArB,IAAgC,KAAK,WAAL,KAAqB,KAAzD,EAAgE;AAC9D,aAAO,CAAP;AACD;;AAED,WAAO,KAAK,UAAL,CAAgB,OAAhB,CAAwB,MAAM,YAAN,CAAmB,OAAnB,CAAxB,CAAP;AACD,G;;wBAED,a,0BAAe,O,EAAS,K,EAA0D;AAAA,QAAnD,IAAmD,uEAA5C,SAAS,iBAAiB,IAAjB,CAAsB,MAAM,IAA5B,CAAmC;;AAChF,QAAM,KAAK,MAAM,YAAN,CAAmB,OAAnB,CAAX;AACA,QAAI,QAAQ,KAAK,eAAL,CAAqB,OAArB,CAAZ;;AAEA,QAAI,UAAU,CAAC,CAAf,EAAkB;AAChB,cAAQ,KAAK,UAAL,CAAgB,MAAxB;AACA,WAAK,UAAL,CAAgB,KAAhB,IAAyB,EAAzB;AACD;;AAED,QAAI,IAAJ,EAAU;AACR,cAAQ,IAAR,CAAa,qBAAb,EAAoC;AAClC,wBADkC;AAElC,oBAFkC;AAGlC,kBAHkC;AAIlC,mBAAW,EAJuB;AAKlC,sBAAc,KALoB;AAMlC,qBAAa;AANqB,OAApC;AAQD;;AAED,SAAK,QAAL,CAAc,KAAd,IAAuB,OAAvB;;AAEA,WAAO,KAAP;AACD,G;;wBAED,a,0BAAe,O,EAAS,K,EAAO;AAC7B,QAAM,QAAQ,KAAK,eAAL,CAAqB,OAArB,CAAd;;AAEA,QAAI,UAAU,CAAC,CAAf,EAAkB;AAAE;AAAS;;AAE7B,YAAQ,IAAR,CAAa,gBAAb,EAA+B;AAC7B,sBAD6B;AAE7B,kBAF6B;AAG7B,oBAAc,KAHe;AAI7B,mBAAa;AAJgB,KAA/B;;AAOA,SAAK,QAAL,CAAiB,MAAjB,CAAwB,KAAxB,EAA+B,CAA/B;AACA,SAAK,UAAL,CAAiB,MAAjB,CAAwB,KAAxB,EAA+B,CAA/B;AACA,SAAK,WAAL,CAAiB,MAAjB,CAAwB,KAAxB,EAA+B,CAA/B;AACA,SAAK,SAAL,CAAiB,MAAjB,CAAwB,KAAxB,EAA+B,CAA/B;AACD,G;;wBAED,mB,gCAAqB,M,EAAQ,a,EAAe;AAC1C,SAAK,YAAL,GAAuB,MAAvB;AACA,SAAK,eAAL,GAAuB,aAAvB;AACD,G;;;;;AAGH,sBAAqB,WAArB,eAAkC;AAA7B,MAAM,SAAU,WAAV,IAAN;AACH,YAAU,MAAV,IAAoB,iBAAiB,MAAjB,CAApB;AACD;;AAED,SAAS,gBAAT,CAA2B,MAA3B,EAAmC;AACjC,SAAQ,UAAU,KAAV,EAAiB;AACvB,QAAM,cAAc,MAAM,cAAN,CAAqB,KAArB,CAApB;;AADuB,gCAEe,MAAM,eAAN,CAAsB,KAAtB,CAFf;AAAA,QAEhB,WAFgB;AAAA,QAEH,cAFG;;AAGvB,QAAM,UAAU,EAAhB,CAHuB,CAGH;;AAEpB,QAAI,QAAQ,aAAR,IAAyB,QAAQ,IAAR,CAAa,MAAM,IAAnB,CAA7B,EAAuD;AACrD,sBAAgB,IAAI,IAAJ,GAAW,OAAX,EAAhB;;AAEA,8BAA2B,MAAM,cAAjC,gBAAiD;AAAA;;AAAA,gBAAtB,MAAM,cAAgB;AAAA,YAAtC,YAAsC;;AAC/C,YAAM,UAAU,YAAhB;AACA,YAAM,cAAc,OAAO,MAAP,CAAc,OAAd,EAAuB,MAAM,IAA7B,EAAmC,WAAnC,CAApB;;AAEA,gBAAQ,IAAR,CAAa,CAAC,OAAD,EAAU,eAAe,IAAI,WAAJ,CAAgB,EAAE,wBAAF,EAAhB,CAAzB,CAAb;AACD;AACF,KATD,MAUK;AACH,UAAI,iBAAiB,KAArB;;AAEA,UAAI,CAAC,QAAQ,oBAAT,IAAiC,QAAQ,IAAR,CAAa,MAAM,IAAnB,CAArC,EAA+D;AAC7D;AACA,aAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,MAAM,YAAN,CAAmB,MAAvB,IAAiC,CAAC,cAAlD,EAAkE,GAAlE,EAAuE;AACrE,2BAAiB,MAAM,YAAN,CAAmB,CAAnB,EAAsB,WAAtB,KAAsC,OAAtC,IAAiD,MAAM,YAAN,CAAmB,CAAnB,EAAsB,aAAxF;AACD;;AAED;AACA;AACA,yBAAiB,kBACX,IAAI,IAAJ,GAAW,OAAX,KAAuB,aAAvB,GAAuC;AAC3C;AAFe,WAGZ,MAAM,SAAN,KAAoB,CAHzB;AAID;;AAED,UAAI,CAAC,cAAL,EAAqB;AACnB,YAAI,eAAc,OAAO,MAAP,CAAc,KAAd,EAAqB,MAAM,IAA3B,EAAiC,WAAjC,CAAlB;;AAEA,YAAI,CAAC,YAAL,EAAkB;AAChB,yBAAc,IAAI,WAAJ,CAAgB,EAAE,wBAAF,EAAhB,CAAd;AACD;;AAED,gBAAQ,IAAR,CAAa,CAAC,KAAD,EAAQ,YAAR,CAAb;AACD;AACF;;AAED,4BAAqC,OAArC,gBAA8C;AAAA,kBAAT,OAAS;AAAA,UAAlC,QAAkC;AAAA,UAAzB,aAAyB;;AAC5C,oBAAY,mBAAZ,CAAgC,WAAhC,EAA6C,cAA7C;AACA,oBAAY,MAAZ,EAAoB,QAApB,EAA6B,KAA7B,EAAoC,WAApC,EAAiD,cAAjD;AACD;AACF,GA/CD;AAgDD;;AAED,SAAS,MAAT,CAAiB,KAAjB,EAAwB;AACtB,0BAA0B,MAAM,YAAhC,gBAA8C;AAAA;;AAAA,YAApB,MAAM,YAAc;AAAA,QAAnC,WAAmC;;AAC5C,gBAAY,GAAZ,CAAgB,KAAhB;AACA,YAAQ,IAAR,CAAa,QAAb,EAAuB,EAAE,YAAF,EAAS,wBAAT,EAAvB;AACD;AACF;;AAED,IAAM,YAAY,CAAE,+BAAF,CAAlB;AACA,IAAM,cAAc,QAAQ,WAA5B;;AAEA,IAAI,WAAW,YAAf,EAA6B;AAC3B,YAAU,YAAY,IAAtB,IAAgC,UAAU,WAA1C;AACA,YAAU,YAAY,IAAtB,IAAgC,UAAU,WAA1C;AACA,YAAU,YAAY,EAAtB,IAAgC,UAAU,SAA1C;AACA,YAAU,YAAY,MAAtB,IAAgC,UAAU,SAA1C;AACD,CALD,MAMK;AACH,YAAU,SAAV,GAAwB,UAAU,WAAlC;AACA,YAAU,SAAV,GAAwB,UAAU,WAAlC;AACA,YAAU,OAAV,GAAwB,UAAU,SAAlC;;AAEA,YAAU,UAAV,GAAwB,UAAU,WAAlC;AACA,YAAU,SAAV,GAAwB,UAAU,WAAlC;AACA,YAAU,QAAV,GAAwB,UAAU,SAAlC;AACA,YAAU,WAAV,GAAwB,UAAU,SAAlC;AACD;;AAED,UAAU,IAAV,GAAiB,MAAjB;;AAEA,SAAS,WAAT,QAA+B,UAA/B,EAA2C;AAAA,MAAnB,GAAmB,SAAnB,GAAmB;;AACzC,MAAM,cAAc,WAAW,OAAX,CAAmB,KAAnB,MAA8B,CAA9B,GAChB,OAAO,GADS,GACH,OAAO,MADxB;;AAGA;AACA,OAAK,IAAM,SAAX,IAAwB,MAAM,eAA9B,EAA+C;AAC7C,gBAAY,GAAZ,EAAiB,SAAjB,EAA4B,OAAO,gBAAnC;AACA,gBAAY,GAAZ,EAAiB,SAAjB,EAA4B,OAAO,kBAAnC,EAAuD,IAAvD;AACD;;AAED,OAAK,IAAM,UAAX,IAAwB,SAAxB,EAAmC;AACjC,gBAAY,GAAZ,EAAiB,UAAjB,EAA4B,UAAU,UAAV,CAA5B,EAAkD,QAAQ,KAAR,GAAgB,EAAE,SAAS,KAAX,EAAhB,GAAqC,SAAvF;AACD;AACF;;AAED,QAAQ,EAAR,CAAW,qBAAX,EAAkC,iBAAiF;AAAA,MAA9E,WAA8E,SAA9E,WAA8E;AAAA,MAAjE,OAAiE,SAAjE,OAAiE;AAAA,MAAxD,SAAwD,SAAxD,SAAwD;AAAA,MAA7C,YAA6C,SAA7C,YAA6C;AAAA,MAA/B,KAA+B,SAA/B,KAA+B;AAAA,MAAxB,WAAwB,SAAxB,WAAwB;AAAA,MAAX,IAAW,SAAX,IAAW;;AACjH,cAAY,UAAZ,CAAuB,YAAvB,IAAuC,SAAvC;AACA,cAAY,QAAZ,CAAqB,YAArB,IAAqC,OAArC;;AAEA,MAAI,IAAJ,EAAU;AACR,gBAAY,aAAZ,GAA4B,IAA5B;AACD;;AAED,MAAI,CAAC,YAAY,WAAZ,EAAL,EAAgC;AAC9B,UAAM,SAAN,CAAgB,YAAY,WAA5B,EAAyC,YAAY,QAArD;;AAEA,UAAM,UAAN,CAAiB,YAAY,SAA7B,EAAyC,YAAY,WAArD;AACA,UAAM,UAAN,CAAiB,YAAY,UAA7B,EAAyC,YAAY,WAArD;;AAEA,gBAAY,SAAZ,GAAwC,KAAxC;AACA,gBAAY,SAAZ,CAAsB,YAAtB,IAAwC,YAAY,SAAZ,CAAsB,SAA9D;AACA,gBAAY,WAAZ,CAAwB,YAAxB,IAAwC,eAAe,SAAS,MAAM,eAAN,CAAsB,KAAtB,EAA6B,CAA7B,CAAhE;AACA,gBAAY,eAAZ,GAAwC,KAAxC;;AAEA,UAAM,aAAN,CAAoB,YAAY,WAAhC,EAA6C,OAA7C;AACD;AACF,CArBD;;AAuBA,MAAM,OAAN,CAAc,EAAd,CAAiB,cAAjB,EAAoC,WAApC;AACA,MAAM,OAAN,CAAc,EAAd,CAAiB,iBAAjB,EAAoC,WAApC;;AAEA,YAAY,oBAAZ,GAAmC,CAAnC;AACA,YAAY,gBAAZ,GAA+B,gBAA/B;AACA,YAAY,MAAZ,GAAqB,MAArB;AACA,YAAY,OAAZ,GAAsB,OAAtB;AACA,YAAY,SAAZ,GAAwB,SAAxB;;AAEA,MAAM,kBAAN,GAA2B,MAA3B;;AAEA,OAAO,OAAP,GAAiB,WAAjB;;;;;AC7fA,IAAM,cAAgB,QAAQ,gBAAR,CAAtB;AACA,IAAM,gBAAgB,QAAQ,kBAAR,CAAtB;;AAEA,IAAM,UAAU;AACd,4BADc;AAEd,SAAO,EAFO;AAGd,cAAY;AAHE,CAAhB;;AAMA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,cAAvB,EAAuC,gBAAkC;AAAA,MAAtB,WAAsB,QAAtB,WAAsB;AAAA,MAAT,KAAS,QAAT,KAAS;;AACvE,cAAY,YAAZ,GAA2B,IAA3B;AACA,eAAa,WAAb,EAA0B,KAA1B,EAAiC,OAAjC;AACD,CAHD;;AAKA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,aAAvB,EAAsC,iBAA0C;AAAA,MAA9B,WAA8B,SAA9B,WAA8B;AAAA,MAAjB,KAAiB,SAAjB,KAAiB;AAAA,MAAV,MAAU,SAAV,MAAU;;AAC9E,eAAa,WAAb,EAA0B,KAA1B,EAAiC,MAAjC,EAAyC,MAAzC;;AAEA;AACA,MAAI,CAAC,YAAY,WAAZ,EAAL,EAAgC;AAAE,WAAO,KAAP;AAAe;AAClD,CALD;;AAOA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,YAAvB,EAAqC,iBAAkC;AAAA,MAAtB,WAAsB,SAAtB,WAAsB;AAAA,MAAT,KAAS,SAAT,KAAS;;AACrE,eAAa,WAAb,EAA0B,KAA1B,EAAiC,KAAjC;AACD,CAFD;;AAIA,SAAS,YAAT,CAAuB,WAAvB,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,MAAlD,EAA0D;AACxD,MAAM,aAAa,YAAY,QAAZ,CAAqB,IAAxC;;AAEA,MAAM,WAAW,IAAI,aAAJ,CAAkB,WAAlB,EAA+B,KAA/B,EAAsC,UAAtC,EAAkD,KAAlD,EAAyD,YAAY,OAArE,EAA8E,IAA9E,EAAoF,MAApF,CAAjB;;AAEA,cAAY,MAAZ,CAAmB,IAAnB,CAAwB,QAAxB;AACA,cAAY,SAAZ,GAAwB,QAAxB;AACD;;AAED,OAAO,OAAP,GAAiB,OAAjB;;;;;AClCA,IAAM,UAAiB,QAAQ,QAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA,IAAM,gBAAiB,QAAQ,kBAAR,CAAvB;AACA;AACA,IAAM,eAAiB,QAAQ,iBAAR,CAAvB;AACA,IAAM,cAAiB,QAAQ,gBAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,mBAAR,CAAvB;;AAEA,IAAM,OAAO;AACX,YAAU;AACR,aAAc,KADN;AAER,kBAAc,IAFN;;AAIR,YAAY,IAJJ;AAKR,UAAY,IALJ;AAMR,cAAY,IANJ;AAOR,aAAY,IAPJ;AAQR,gBAAY,IARJ;;AAUR,eAAY,IAVJ;AAWR,cAAY;AAXJ,GADC;;AAeX,WAAS,iBAAU,OAAV,EAAmB,KAAnB,EAA0B,YAA1B,EAAwC;AAC/C,QAAM,cAAc,aAAa,OAAb,CAAqB,IAAzC;;AAEA,WAAO,YAAY,OAAZ,GACH,EAAE,MAAM,MAAR,EAAgB,MAAO,YAAY,QAAZ,KAAyB,OAAzB,GACE,YAAY,SADd,GAEE,YAAY,QAFrC,EADG,GAIH,IAJJ;AAKD,GAvBU;;AAyBX,aAAW,qBAAY;AACrB,WAAO,MAAP;AACD;AA3BU,CAAb;;AA8BA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,oBAAvB,EAA6C,gBAA2B;AAAA,MAAf,WAAe,QAAf,WAAe;;AACtE,MAAI,YAAY,QAAZ,CAAqB,IAArB,KAA8B,MAAlC,EAA0C;AAAE;AAAS;;AAErD,MAAM,OAAO,YAAY,QAAZ,CAAqB,IAAlC;;AAEA,MAAI,SAAS,GAAb,EAAkB;AAChB,gBAAY,SAAZ,CAAsB,IAAtB,CAA2B,CAA3B,GAAiC,YAAY,WAAZ,CAAwB,IAAxB,CAA6B,CAA9D;AACA,gBAAY,SAAZ,CAAsB,MAAtB,CAA6B,CAA7B,GAAiC,YAAY,WAAZ,CAAwB,MAAxB,CAA+B,CAAhE;;AAEA,gBAAY,YAAZ,CAAyB,IAAzB,CAA8B,KAA9B,GAAwC,KAAK,GAAL,CAAS,YAAY,YAAZ,CAAyB,IAAzB,CAA8B,EAAvC,CAAxC;AACA,gBAAY,YAAZ,CAAyB,MAAzB,CAAgC,KAAhC,GAAwC,KAAK,GAAL,CAAS,YAAY,YAAZ,CAAyB,MAAzB,CAAgC,EAAzC,CAAxC;AACA,gBAAY,YAAZ,CAAyB,MAAzB,CAAgC,EAAhC,GAAqC,CAArC;AACA,gBAAY,YAAZ,CAAyB,IAAzB,CAA8B,EAA9B,GAAqC,CAArC;AACD,GARD,MASK,IAAI,SAAS,GAAb,EAAkB;AACrB,gBAAY,SAAZ,CAAsB,IAAtB,CAA2B,CAA3B,GAAiC,YAAY,WAAZ,CAAwB,IAAxB,CAA6B,CAA9D;AACA,gBAAY,SAAZ,CAAsB,MAAtB,CAA6B,CAA7B,GAAiC,YAAY,WAAZ,CAAwB,MAAxB,CAA+B,CAAhE;;AAEA,gBAAY,YAAZ,CAAyB,IAAzB,CAA8B,KAA9B,GAAwC,KAAK,GAAL,CAAS,YAAY,YAAZ,CAAyB,IAAzB,CAA8B,EAAvC,CAAxC;AACA,gBAAY,YAAZ,CAAyB,MAAzB,CAAgC,KAAhC,GAAwC,KAAK,GAAL,CAAS,YAAY,YAAZ,CAAyB,MAAzB,CAAgC,EAAzC,CAAxC;AACA,gBAAY,YAAZ,CAAyB,MAAzB,CAAgC,EAAhC,GAAqC,CAArC;AACA,gBAAY,YAAZ,CAAyB,IAAzB,CAA8B,EAA9B,GAAqC,CAArC;AACD;AACF,CAvBD;;AAyBA;AACA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,KAAzB,EAAgC,iBAAmC;AAAA,MAAvB,MAAuB,SAAvB,MAAuB;AAAA,MAAf,WAAe,SAAf,WAAe;;AACjE,MAAI,OAAO,IAAP,KAAgB,UAApB,EAAgC;AAAE;AAAS;;AAE3C,MAAM,OAAO,YAAY,QAAZ,CAAqB,IAAlC;;AAEA,MAAI,SAAS,GAAb,EAAkB;AAChB,WAAO,KAAP,GAAiB,YAAY,WAAZ,CAAwB,IAAxB,CAA6B,CAA9C;AACA,WAAO,OAAP,GAAiB,YAAY,WAAZ,CAAwB,MAAxB,CAA+B,CAAhD;AACA,WAAO,EAAP,GAAY,CAAZ;AACD,GAJD,MAKK,IAAI,SAAS,GAAb,EAAkB;AACrB,WAAO,KAAP,GAAiB,YAAY,WAAZ,CAAwB,IAAxB,CAA6B,CAA9C;AACA,WAAO,OAAP,GAAiB,YAAY,WAAZ,CAAwB,MAAxB,CAA+B,CAAhD;AACA,WAAO,EAAP,GAAY,CAAZ;AACD;AACF,CAfD;;AAiBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,aAAa,SAAb,CAAuB,SAAvB,GAAmC,UAAU,OAAV,EAAmB;AACpD,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,OAAhB,CAAJ,EAA8B;AAC5B,SAAK,OAAL,CAAa,IAAb,CAAkB,OAAlB,GAA4B,QAAQ,OAAR,KAAoB,KAApB,GAA2B,KAA3B,GAAkC,IAA9D;AACA,SAAK,YAAL,CAAkB,MAAlB,EAA0B,OAA1B;AACA,SAAK,WAAL,CAAiB,MAAjB,EAAyB,OAAzB;;AAEA,QAAI,mBAAmB,IAAnB,CAAwB,QAAQ,QAAhC,CAAJ,EAA+C;AAC7C,WAAK,OAAL,CAAa,IAAb,CAAkB,QAAlB,GAA6B,QAAQ,QAArC;AACD;AACD,QAAI,aAAa,IAAb,CAAkB,QAAQ,SAA1B,CAAJ,EAA0C;AACxC,WAAK,OAAL,CAAa,IAAb,CAAkB,SAAlB,GAA8B,QAAQ,SAAtC;AACD;;AAED,WAAO,IAAP;AACD;;AAED,MAAI,MAAM,EAAN,CAAS,IAAT,CAAc,OAAd,CAAJ,EAA4B;AAC1B,SAAK,OAAL,CAAa,IAAb,CAAkB,OAAlB,GAA4B,OAA5B;;AAEA,QAAI,CAAC,OAAL,EAAc;AACZ,WAAK,WAAL,GAAmB,KAAK,WAAL,GAAmB,KAAK,SAAL,GAAiB,IAAvD;AACD;;AAED,WAAO,IAAP;AACD;;AAED,SAAO,KAAK,OAAL,CAAa,IAApB;AACD,CA3BD;;AA6BA,QAAQ,IAAR,GAAe,IAAf;AACA,QAAQ,KAAR,CAAc,IAAd,CAAmB,MAAnB;AACA,MAAM,KAAN,CAAY,aAAa,UAAzB,EAAqC,CACnC,WADmC,EAEnC,UAFmC,EAGnC,kBAHmC,EAInC,mBAJmC,EAKnC,SALmC,CAArC;AAOA,QAAQ,UAAR,CAAmB,IAAnB,GAA0B,WAA1B;;AAEA,eAAe,IAAf,GAAsB,KAAK,QAA3B;;AAEA,OAAO,OAAP,GAAiB,IAAjB;;;;;AChKA,IAAM,UAAiB,QAAQ,QAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA;AACA,IAAM,WAAiB,QAAQ,aAAR,CAAvB;AACA,IAAM,gBAAiB,QAAQ,kBAAR,CAAvB;AACA;AACA,IAAM,eAAiB,QAAQ,iBAAR,CAAvB;AACA,IAAM,cAAiB,QAAQ,gBAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,mBAAR,CAAvB;;AAEA,IAAM,OAAO;AACX,YAAU;AACR,aAAS,KADD;AAER,YAAS,IAFD;AAGR,aAAS;AAHD;AADC,CAAb;;AAQA,IAAI,cAAc,KAAlB;;AAEA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,cAAvB,EAAuC,gBAAkC;AAAA,MAAtB,WAAsB,QAAtB,WAAsB;AAAA,MAAT,KAAS,QAAT,KAAS;;AACvE,MAAI,YAAY,QAAZ,CAAqB,IAArB,KAA8B,MAAlC,EAA0C;AAAE;AAAS;;AAErD;AACA,cAAY,WAAZ,CAAwB,SAAxB,GAAoC,EAApC;AACA,cAAY,WAAZ,CAAwB,QAAxB,GAAoC,EAApC;AACA,cAAY,WAAZ,CAAwB,KAAxB,GAAoC,EAApC;;AAEA,cAAY,UAAZ,GAAyB,IAAzB;;AAEA,MAAI,CAAC,YAAY,WAAjB,EAA8B;AAC5B,mBAAe,YAAY,WAA3B,EAAwC,YAAY,OAApD;AACD;;AAED,MAAM,YAAY,YAAY,SAA9B;AACA,MAAM,aAAa,cAAc,WAAd,EAA2B,KAA3B,EAAkC,SAAlC,CAAnB;;AAEA,MAAI,WAAW,QAAf,EAAyB;AACvB,oBAAgB,YAAY,WAA5B,EAAyC,WAAW,QAApD;AACD;AACF,CApBD;;AAsBA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,KAAzB,EAAgC,iBAA0C;AAAA,MAA9B,WAA8B,SAA9B,WAA8B;AAAA,MAAjB,MAAiB,SAAjB,MAAiB;AAAA,MAAT,KAAS,SAAT,KAAS;;AACxE,MAAI,OAAO,IAAP,KAAgB,UAAhB,IAA8B,OAAO,IAAP,KAAgB,SAAlD,EAA6D;AAAE;AAAS;;AAExE,MAAM,mBAAmB,YAAY,OAArC;AACA,MAAM,YAAY,MAAlB;AACA,MAAM,aAAa,QAAQ,SAAR,EAAmB,KAAnB,EAA0B,gBAA1B,CAAnB;;AAEA,cAAY,UAAZ,GAA0B,WAAW,QAArC;AACA,cAAY,WAAZ,GAA0B,WAAW,OAArC;;AAEA,cAAY,UAAZ,GAAyB,cAAc,WAAd,EAA2B,KAA3B,EAAkC,SAAlC,CAAzB;AACD,CAXD;;AAaA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,aAAvB,EAAsC,iBAA2B;AAAA,MAAf,WAAe,SAAf,WAAe;;AAC/D,MAAI,YAAY,QAAZ,CAAqB,IAArB,KAA8B,MAAlC,EAA0C;AAAE;AAAS;;AAErD,iBAAe,WAAf,EAA4B,YAAY,UAAxC;AACD,CAJD;;AAMA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,YAAvB,EAAqC,iBAA2B;AAAA,MAAf,WAAe,SAAf,WAAe;;AAC9D,MAAI,YAAY,QAAZ,CAAqB,IAArB,KAA8B,MAAlC,EAA0C;AACxC,mBAAe,WAAf,EAA4B,YAAY,UAAxC;AACD;AACF,CAJD;;AAMA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,WAAvB,EAAoC,iBAA2B;AAAA,MAAf,WAAe,SAAf,WAAe;;AAC7D,cAAY,WAAZ,GAA0B;AACxB,eAAW,IADa;AAExB,cAAU,IAFc;AAGxB,WAAO;AAHiB,GAA1B;;AAMA,cAAY,UAAZ,GAAyB,IAAzB;AACD,CARD;;AAUA,SAAS,YAAT,CAAuB,WAAvB,EAAoC,OAApC,EAA6C;AAC3C,MAAM,QAAQ,EAAd;AACA,MAAM,WAAW,EAAjB;;AAEA;AACA,wBAAsB,MAAM,aAA5B,eAA2C;AAAA;;AAAA,YAArB,MAAM,aAAe;AAAA,QAAhC,OAAgC;;AACzC,QAAI,CAAC,QAAQ,OAAR,CAAgB,IAAhB,CAAqB,OAA1B,EAAmC;AAAE;AAAW;;AAEhD,QAAM,SAAS,QAAQ,OAAR,CAAgB,IAAhB,CAAqB,MAApC;;AAEA;AACA,QAAK,MAAM,EAAN,CAAS,OAAT,CAAiB,MAAjB,KAA4B,WAAW,OAAxC,IACI,MAAM,EAAN,CAAS,MAAT,CAAgB,MAAhB,KACD,CAAC,MAAM,eAAN,CAAsB,OAAtB,EAA+B,MAA/B,CAFR,EAEiD;;AAE/C;AACD;;AAED;AACA,QAAM,eAAe,MAAM,EAAN,CAAS,MAAT,CAAgB,QAAQ,MAAxB,IACjB,QAAQ,QAAR,CAAiB,gBAAjB,CAAkC,QAAQ,MAA1C,CADiB,GAEjB,CAAC,QAAQ,MAAT,CAFJ;;AAIA,4BAA6B,YAA7B,gBAA2C;AAAA;;AAAA,cAAd,YAAc;AAAA,UAAhC,cAAgC;;AACzC,UAAI,mBAAmB,OAAvB,EAAgC;AAC9B,cAAM,IAAN,CAAW,OAAX;AACA,iBAAS,IAAT,CAAc,cAAd;AACD;AACF;AACF;;AAED,SAAO;AACL,sBADK;AAEL,eAAW;AAFN,GAAP;AAID;;AAED,SAAS,eAAT,CAA0B,WAA1B,EAAuC,KAAvC,EAA8C;AAC5C,MAAI,oBAAJ;;AAEA;AACA,OAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,YAAY,SAAZ,CAAsB,MAA1C,EAAkD,GAAlD,EAAuD;AACrD,QAAM,UAAU,YAAY,SAAZ,CAAsB,CAAtB,CAAhB;AACA,QAAM,iBAAiB,YAAY,QAAZ,CAAsB,CAAtB,CAAvB;;AAEA;AACA,QAAI,mBAAmB,WAAvB,EAAoC;AAClC;AACA,YAAM,MAAN,GAAe,cAAf;AACA,cAAQ,IAAR,CAAa,KAAb;AACD;AACD,kBAAc,cAAd;AACD;AACF;;AAED;AACA;AACA;AACA,SAAS,cAAT,CAAyB,WAAzB,EAAsC,WAAtC,EAAmD;AACjD;AACA,MAAM,gBAAgB,aAAa,WAAb,EAA0B,WAA1B,CAAtB;;AAEA,cAAY,SAAZ,GAAwB,cAAc,SAAtC;AACA,cAAY,QAAZ,GAAwB,cAAc,QAAtC;AACA,cAAY,KAAZ,GAAwB,EAAxB;;AAEA,OAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,YAAY,SAAZ,CAAsB,MAA1C,EAAkD,GAAlD,EAAuD;AACrD,gBAAY,KAAZ,CAAkB,CAAlB,IAAuB,YAAY,SAAZ,CAAsB,CAAtB,EAAyB,OAAzB,CAAiC,YAAY,QAAZ,CAAqB,CAArB,CAAjC,CAAvB;AACD;AACF;;AAED,SAAS,OAAT,CAAkB,SAAlB,EAA6B,KAA7B,EAAoC,WAApC,EAAiD;AAC/C,MAAM,cAAc,UAAU,WAA9B;AACA,MAAM,aAAa,EAAnB;;AAEA,MAAI,WAAJ,EAAiB;AACf,mBAAe,YAAY,WAA3B,EAAwC,WAAxC;AACD;;AAED;AACA,OAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,YAAY,WAAZ,CAAwB,SAAxB,CAAkC,MAAtD,EAA8D,GAA9D,EAAmE;AACjE,QAAM,UAAiB,YAAY,WAAZ,CAAwB,SAAxB,CAAkC,CAAlC,CAAvB;AACA,QAAM,iBAAiB,YAAY,WAAZ,CAAwB,QAAxB,CAAkC,CAAlC,CAAvB;AACA,QAAM,OAAiB,YAAY,WAAZ,CAAwB,KAAxB,CAAkC,CAAlC,CAAvB;;AAEA,eAAW,IAAX,CAAgB,QAAQ,SAAR,CAAkB,SAAlB,EAA6B,KAA7B,EAAoC,YAAY,MAAhD,EAAwD,WAAxD,EAAqE,cAArE,EAAqF,IAArF,IACZ,cADY,GAEZ,IAFJ;AAGD;;AAED;AACA,MAAM,YAAY,MAAM,qBAAN,CAA4B,UAA5B,CAAlB;;AAEA,SAAO;AACL,cAAU,YAAY,WAAZ,CAAwB,SAAxB,CAAkC,SAAlC,KAAgD,IADrD;AAEL,aAAU,YAAY,WAAZ,CAAwB,QAAxB,CAAkC,SAAlC,KAAgD;AAFrD,GAAP;AAID;;AAED,SAAS,aAAT,CAAwB,WAAxB,EAAqC,YAArC,EAAmD,SAAnD,EAA8D;AAC5D,MAAM,aAAa;AACjB,WAAY,IADK;AAEjB,WAAY,IAFK;AAGjB,cAAY,IAHK;AAIjB,gBAAY,IAJK;AAKjB,UAAY,IALK;AAMjB,UAAY;AANK,GAAnB;;AASA,MAAM,OAAO;AACX,wBADW;AAEX,4BAFW;AAGX,YAAe,YAAY,WAHhB;AAIX,cAAe,YAAY,UAJhB;AAKX,mBAAe,UAAU,MALd;AAMX,eAAe,UAAU,YANd;AAOX,eAAe,UAAU;AAPd,GAAb;;AAUA,MAAI,YAAY,WAAZ,KAA4B,YAAY,eAA5C,EAA6D;AAC3D;AACA,QAAI,YAAY,cAAhB,EAAgC;AAC9B,iBAAW,KAAX,GAAmB,MAAM,MAAN,CAAa,EAAE,MAAM,WAAR,EAAb,EAAoC,IAApC,CAAnB;;AAEA,gBAAU,SAAV,GAAyB,WAAW,KAAX,CAAiB,MAAjB,GAA4B,YAAY,eAAjE;AACA,gBAAU,YAAV,GAAyB,WAAW,KAAX,CAAiB,QAAjB,GAA4B,YAAY,cAAjE;AACD;AACD;AACA,QAAI,YAAY,UAAhB,EAA4B;AAC1B,iBAAW,KAAX,GAAmB;AACjB,4BADiB;AAEjB,gCAFiB;AAGjB,gBAAe,YAAY,WAHV;AAIjB,kBAAe,YAAY,UAJV;AAKjB,uBAAe,UAAU,MALR;AAMjB,mBAAe,UAAU,YANR;AAOjB,mBAAe,UAAU,SAPR;AAQjB,cAAe;AARE,OAAnB;;AAWA,gBAAU,SAAV,GAAsB,YAAY,WAAlC;AACA,gBAAU,QAAV,GAAqB,YAAY,UAAjC;AACD;AACF;;AAED,MAAI,UAAU,IAAV,KAAmB,SAAnB,IAAgC,YAAY,UAAhD,EAA4D;AAC1D,eAAW,IAAX,GAAkB,MAAM,MAAN,CAAa,EAAE,MAAM,MAAR,EAAb,EAA+B,IAA/B,CAAlB;;AAEA,cAAU,QAAV,GAAqB,YAAY,UAAjC;AACA,cAAU,aAAV,GAA0B,YAAY,WAAtC;AACD;AACD,MAAI,UAAU,IAAV,KAAmB,WAAvB,EAAoC;AAClC,eAAW,QAAX,GAAsB,MAAM,MAAN,CAAa,EAAE,MAAM,cAAR,EAAb,EAAuC,IAAvC,CAAtB;;AAEA,eAAW,QAAX,CAAoB,MAApB,GAA+B,IAA/B;AACA,eAAW,QAAX,CAAoB,QAApB,GAA+B,IAA/B;AACD;AACD,MAAI,UAAU,IAAV,KAAmB,SAAvB,EAAkC;AAChC,eAAW,UAAX,GAAwB,MAAM,MAAN,CAAa,EAAE,MAAM,gBAAR,EAAb,EAAyC,IAAzC,CAAxB;;AAEA,eAAW,UAAX,CAAsB,MAAtB,GAAiC,IAAjC;AACA,eAAW,UAAX,CAAsB,QAAtB,GAAiC,IAAjC;AACD;AACD,MAAI,UAAU,IAAV,KAAmB,UAAnB,IAAiC,YAAY,UAAjD,EAA6D;AAC3D,eAAW,IAAX,GAAkB,MAAM,MAAN,CAAa;AAC7B,gBAAe,SADc;AAE7B,YAAe;AAFc,KAAb,EAGf,IAHe,CAAlB;;AAKA,cAAU,QAAV,GAAqB,YAAY,UAAjC;AACD;;AAED,SAAO,UAAP;AACD;;AAED,SAAS,cAAT,CAAyB,WAAzB,EAAsC,UAAtC,EAAkD;AAAA,MAE9C,WAF8C,GAM5C,WAN4C,CAE9C,WAF8C;AAAA,MAG9C,cAH8C,GAM5C,WAN4C,CAG9C,cAH8C;AAAA,MAI9C,UAJ8C,GAM5C,WAN4C,CAI9C,UAJ8C;AAAA,MAK9C,WAL8C,GAM5C,WAN4C,CAK9C,WAL8C;;;AAQhD,MAAI,WAAW,KAAf,EAAsB;AAAE,mBAAe,IAAf,CAAoB,WAAW,KAA/B;AAAwC;AAChE,MAAI,WAAW,IAAf,EAAsB;AAAM,eAAW,IAAX,CAAgB,WAAW,IAA3B;AAAoC;AAChE,MAAI,WAAW,KAAf,EAAsB;AAAM,eAAW,IAAX,CAAgB,WAAW,KAA3B;AAAoC;AAChE,MAAI,WAAW,IAAf,EAAsB;AAAM,eAAW,IAAX,CAAgB,WAAW,IAA3B;AAAoC;AAChE,MAAI,WAAW,UAAf,EAA2B;AACzB,oBAAgB,WAAhB,EAA6B,WAAW,UAAxC;AACD;;AAED,cAAY,cAAZ,GAA8B,UAA9B;AACA,cAAY,eAAZ,GAA8B,WAA9B;AACD;;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA,aAAa,SAAb,CAAuB,QAAvB,GAAkC,UAAU,OAAV,EAAmB;AACnD,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,OAAhB,CAAJ,EAA8B;AAC5B,SAAK,OAAL,CAAa,IAAb,CAAkB,OAAlB,GAA4B,QAAQ,OAAR,KAAoB,KAApB,GAA2B,KAA3B,GAAkC,IAA9D;;AAEA,QAAI,MAAM,EAAN,CAAS,QAAT,CAAkB,QAAQ,MAA1B,CAAJ,EAAiD;AAAE,WAAK,MAAL,CAAY,MAAZ,GAA+B,QAAQ,MAAvC;AAA0D;AAC7G,QAAI,MAAM,EAAN,CAAS,QAAT,CAAkB,QAAQ,cAA1B,CAAJ,EAAiD;AAAE,WAAK,MAAL,CAAY,cAAZ,GAA+B,QAAQ,cAAvC;AAA0D;AAC7G,QAAI,MAAM,EAAN,CAAS,QAAT,CAAkB,QAAQ,gBAA1B,CAAJ,EAAiD;AAAE,WAAK,MAAL,CAAY,gBAAZ,GAA+B,QAAQ,gBAAvC;AAA0D;AAC7G,QAAI,MAAM,EAAN,CAAS,QAAT,CAAkB,QAAQ,WAA1B,CAAJ,EAAiD;AAAE,WAAK,MAAL,CAAY,WAAZ,GAA+B,QAAQ,WAAvC;AAA0D;AAC7G,QAAI,MAAM,EAAN,CAAS,QAAT,CAAkB,QAAQ,WAA1B,CAAJ,EAAiD;AAAE,WAAK,MAAL,CAAY,WAAZ,GAA+B,QAAQ,WAAvC;AAA0D;AAC7G,QAAI,MAAM,EAAN,CAAS,QAAT,CAAkB,QAAQ,UAA1B,CAAJ,EAAiD;AAAE,WAAK,MAAL,CAAY,UAAZ,GAA+B,QAAQ,UAAvC;AAA0D;;AAE7G,QAAI,qBAAqB,IAArB,CAA0B,QAAQ,OAAlC,CAAJ,EAAgD;AAC9C,WAAK,OAAL,CAAa,IAAb,CAAkB,OAAlB,GAA4B,QAAQ,OAApC;AACD,KAFD,MAGK,IAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,QAAQ,OAAxB,CAAJ,EAAsC;AACzC,WAAK,OAAL,CAAa,IAAb,CAAkB,OAAlB,GAA4B,KAAK,GAAL,CAAS,KAAK,GAAL,CAAS,CAAT,EAAY,QAAQ,OAApB,CAAT,EAAuC,CAAvC,CAA5B;AACD;AACD,QAAI,YAAY,OAAhB,EAAyB;AACvB,WAAK,OAAL,CAAa,IAAb,CAAkB,MAAlB,GAA2B,QAAQ,MAAnC;AACD;AACD,QAAI,aAAa,OAAjB,EAA0B;AACxB,WAAK,OAAL,CAAa,IAAb,CAAkB,OAAlB,GAA4B,QAAQ,OAApC;AACD;;AAGD,WAAO,IAAP;AACD;;AAED,MAAI,MAAM,EAAN,CAAS,IAAT,CAAc,OAAd,CAAJ,EAA4B;AAC1B,SAAK,OAAL,CAAa,IAAb,CAAkB,OAAlB,GAA4B,OAA5B;;AAEA,QAAI,CAAC,OAAL,EAAc;AACZ,WAAK,WAAL,GAAmB,KAAK,WAAL,GAAmB,KAAK,MAAL,GAClC,KAAK,cAAL,GAAsB,KAAK,gBAAL,GAAwB,IADlD;AAED;;AAED,WAAO,IAAP;AACD;;AAED,SAAO,KAAK,OAAL,CAAa,IAApB;AACD,CAxCD;;AA0CA,aAAa,SAAb,CAAuB,SAAvB,GAAmC,UAAU,SAAV,EAAqB,KAArB,EAA4B,SAA5B,EAAuC,gBAAvC,EAAyD,WAAzD,EAAsE,IAAtE,EAA4E;AAC7G,MAAI,UAAU,KAAd;;AAEA;AACA;AACA,MAAI,EAAE,OAAO,QAAQ,KAAK,OAAL,CAAa,WAAb,CAAjB,CAAJ,EAAiD;AAC/C,WAAQ,KAAK,OAAL,CAAa,IAAb,CAAkB,OAAlB,GACJ,KAAK,OAAL,CAAa,IAAb,CAAkB,OAAlB,CAA0B,SAA1B,EAAqC,KAArC,EAA4C,OAA5C,EAAqD,IAArD,EAA2D,WAA3D,EAAwE,SAAxE,EAAmF,gBAAnF,CADI,GAEJ,KAFJ;AAGD;;AAED,MAAM,cAAc,KAAK,OAAL,CAAa,IAAb,CAAkB,OAAtC;;AAEA,MAAI,gBAAgB,SAApB,EAA+B;AAC7B,QAAM,SAAS,MAAM,WAAN,CAAkB,SAAlB,EAA6B,gBAA7B,EAA+C,MAA/C,CAAf;AACA,QAAM,OAAO,MAAM,SAAN,CAAgB,SAAhB,CAAb;;AAEA,SAAK,CAAL,IAAU,OAAO,CAAjB;AACA,SAAK,CAAL,IAAU,OAAO,CAAjB;;AAEA,QAAM,aAAc,KAAK,CAAL,GAAS,KAAK,IAAf,IAAyB,KAAK,CAAL,GAAS,KAAK,KAA1D;AACA,QAAM,WAAc,KAAK,CAAL,GAAS,KAAK,GAAf,IAAyB,KAAK,CAAL,GAAS,KAAK,MAA1D;;AAEA,cAAU,cAAc,QAAxB;AACD;;AAED,MAAM,WAAW,UAAU,OAAV,CAAkB,gBAAlB,CAAjB;;AAEA,MAAI,YAAY,gBAAgB,QAAhC,EAA0C;AACxC,QAAM,KAAK,SAAS,IAAT,GAAgB,SAAS,KAAT,GAAkB,CAA7C;AACA,QAAM,KAAK,SAAS,GAAT,GAAgB,SAAS,MAAT,GAAkB,CAA7C;;AAEA,cAAU,MAAM,KAAK,IAAX,IAAmB,MAAM,KAAK,KAA9B,IAAuC,MAAM,KAAK,GAAlD,IAAyD,MAAM,KAAK,MAA9E;AACD;;AAED,MAAI,YAAY,MAAM,EAAN,CAAS,MAAT,CAAgB,WAAhB,CAAhB,EAA8C;AAC5C,QAAM,cAAgB,KAAK,GAAL,CAAS,CAAT,EAAY,KAAK,GAAL,CAAS,KAAK,KAAd,EAAsB,SAAS,KAA/B,IAAyC,KAAK,GAAL,CAAS,KAAK,IAAd,EAAoB,SAAS,IAA7B,CAArD,IACE,KAAK,GAAL,CAAS,CAAT,EAAY,KAAK,GAAL,CAAS,KAAK,MAAd,EAAsB,SAAS,MAA/B,IAAyC,KAAK,GAAL,CAAS,KAAK,GAAd,EAAoB,SAAS,GAA7B,CAArD,CADxB;;AAGA,QAAM,eAAe,eAAe,SAAS,KAAT,GAAiB,SAAS,MAAzC,CAArB;;AAEA,cAAU,gBAAgB,WAA1B;AACD;;AAED,MAAI,KAAK,OAAL,CAAa,IAAb,CAAkB,OAAtB,EAA+B;AAC7B,cAAU,KAAK,OAAL,CAAa,IAAb,CAAkB,OAAlB,CAA0B,SAA1B,EAAqC,KAArC,EAA4C,OAA5C,EAAqD,IAArD,EAA2D,WAA3D,EAAwE,SAAxE,EAAmF,gBAAnF,CAAV;AACD;;AAED,SAAO,OAAP;AACD,CAjDD;;AAmDA,aAAa,OAAb,CAAqB,EAArB,CAAwB,OAAxB,EAAiC,iBAA4B;AAAA,MAAhB,YAAgB,SAAhB,YAAgB;;AAC3D,eAAa,QAAb,CAAsB,KAAtB;AACD,CAFD;;AAIA,aAAa,eAAb,CAA6B,IAA7B,CAAkC,aAAlC;;AAEA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,KAAvB,EAA8B,UAAU,WAAV,EAAuB;AACnD,cAAY,UAAZ,GAA8B,IAA9B,CADmD,CACf;AACpC,cAAY,WAAZ,GAA8B,IAA9B,CAFmD,CAEf;AACpC,cAAY,cAAZ,GAA8B,IAA9B,CAHmD,CAGf;AACpC,cAAY,eAAZ,GAA8B,IAA9B,CAJmD,CAIf;AACpC,cAAY,UAAZ,GAA8B,IAA9B,CALmD,CAKf;;AAEpC,cAAY,WAAZ,GAA0B;AACxB,eAAW,EADa,EACJ;AACpB,cAAW,EAFa,EAEJ;AACpB,WAAW,EAHa,CAGJ;AAHI,GAA1B;AAMD,CAbD;;AAeA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,MAAvB,EAA+B,iBAA2B;AAAA,MAAf,WAAe,SAAf,WAAe;;AACxD,cAAY,UAAZ,GAAyB,YAAY,WAAZ,GACvB,YAAY,cAAZ,GAA6B,YAAY,eAAZ,GAA8B,IAD7D;AAED,CAHD;;AAKA;;;;;;;;AAQA,SAAS,WAAT,GAAuB,UAAU,QAAV,EAAoB;AACzC,MAAI,MAAM,EAAN,CAAS,IAAT,CAAc,QAAd,CAAJ,EAA6B;AAC3B;AACE;AACF;;AAEA,kBAAc,QAAd;;AAEA,WAAO,QAAP;AACD;AACD,SAAO,WAAP;AACD,CAXD;;AAaA,MAAM,KAAN,CAAY,aAAa,UAAzB,EAAqC,CACnC,WADmC,EAEnC,WAFmC,EAGnC,cAHmC,EAInC,gBAJmC,EAKnC,UALmC,EAMnC,MANmC,CAArC;AAQA,QAAQ,UAAR,CAAmB,IAAnB,GAA0B,UAA1B;;AAEA,eAAe,IAAf,GAAsB,KAAK,QAA3B;;AAEA,OAAO,OAAP,GAAiB,IAAjB;;;;;AC9dA,IAAM,UAAiB,QAAQ,QAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA,IAAM,gBAAiB,QAAQ,kBAAR,CAAvB;AACA,IAAM,eAAiB,QAAQ,iBAAR,CAAvB;AACA,IAAM,cAAiB,QAAQ,gBAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,mBAAR,CAAvB;;AAEA,IAAM,UAAU;AACd,YAAU;AACR,aAAU,KADF;AAER,YAAU,IAFF;AAGR,cAAU;AAHF,GADI;;AAOd,WAAS,iBAAU,OAAV,EAAmB,KAAnB,EAA0B,YAA1B,EAAwC,OAAxC,EAAiD,WAAjD,EAA8D;AACrE,QAAI,YAAY,UAAZ,CAAuB,MAAvB,IAAiC,CAArC,EAAwC;AACtC,aAAO,EAAE,MAAM,SAAR,EAAP;AACD;;AAED,WAAO,IAAP;AACD,GAba;;AAed,aAAW,qBAAY;AACrB,WAAO,EAAP;AACD;AAjBa,CAAhB;;AAoBA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,KAAzB,EAAgC,gBAAmC;AAAA,MAAvB,MAAuB,QAAvB,MAAuB;AAAA,MAAf,WAAe,QAAf,WAAe;;AACjE,MAAI,OAAO,IAAP,KAAgB,cAApB,EAAoC;AAAE;AAAS;AAC/C,SAAO,EAAP,GAAY,CAAZ;;AAEA,cAAY,OAAZ,CAAoB,aAApB,GAAoC,YAAY,OAAZ,CAAoB,YAApB,GAAmC,OAAO,QAA9E;AACA,cAAY,OAAZ,CAAoB,UAApB,GAAiC,YAAY,OAAZ,CAAoB,SAApB,GAAgC,OAAO,KAAxE;AACA,cAAY,OAAZ,CAAoB,KAApB,GAA4B,CAA5B;AACD,CAPD;;AASA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,KAAzB,EAAgC,iBAAmC;AAAA,MAAvB,MAAuB,SAAvB,MAAuB;AAAA,MAAf,WAAe,SAAf,WAAe;;AACjE,MAAI,OAAO,IAAP,KAAgB,aAApB,EAAmC;AAAE;AAAS;;AAE9C,SAAO,EAAP,GAAY,OAAO,KAAP,GAAe,YAAY,OAAZ,CAAoB,KAA/C;;AAEA,cAAY,MAAZ,CAAmB,IAAnB,CAAwB,MAAxB;;AAEA,cAAY,OAAZ,CAAoB,SAApB,GAAgC,OAAO,KAAvC;AACA,cAAY,OAAZ,CAAoB,YAApB,GAAmC,OAAO,QAA1C;;AAEA,MAAI,OAAO,KAAP,KAAiB,QAAjB,IACG,OAAO,KAAP,KAAiB,IADpB,IAEG,OAAO,KAAP,KAAiB,SAFpB,IAGG,CAAC,MAAM,OAAO,KAAb,CAHR,EAG6B;;AAE3B,gBAAY,OAAZ,CAAoB,KAApB,GAA4B,OAAO,KAAnC;AACD;AACF,CAjBD;;AAmBA;;;;;;;;;;;;;;;;;;;;;;;AAuBA,aAAa,SAAb,CAAuB,UAAvB,GAAoC,UAAU,OAAV,EAAmB;AACrD,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,OAAhB,CAAJ,EAA8B;AAC5B,SAAK,OAAL,CAAa,OAAb,CAAqB,OAArB,GAA+B,QAAQ,OAAR,KAAoB,KAApB,GAA2B,KAA3B,GAAkC,IAAjE;AACA,SAAK,YAAL,CAAkB,SAAlB,EAA6B,OAA7B;AACA,SAAK,WAAL,CAAiB,SAAjB,EAA4B,OAA5B;;AAEA,WAAO,IAAP;AACD;;AAED,MAAI,MAAM,EAAN,CAAS,IAAT,CAAc,OAAd,CAAJ,EAA4B;AAC1B,SAAK,OAAL,CAAa,OAAb,CAAqB,OAArB,GAA+B,OAA/B;;AAEA,QAAI,CAAC,OAAL,EAAc;AACZ,WAAK,cAAL,GAAsB,KAAK,cAAL,GAAsB,KAAK,YAAL,GAAoB,IAAhE;AACD;;AAED,WAAO,IAAP;AACD;;AAED,SAAO,KAAK,OAAL,CAAa,OAApB;AACD,CApBD;;AAsBA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,WAAzB,EAAsC,iBAAiF;AAAA,MAArE,WAAqE,SAArE,WAAqE;AAAA,MAAxD,MAAwD,SAAxD,MAAwD;AAAA,MAAhD,MAAgD,SAAhD,MAAgD;AAAA,MAAxC,KAAwC,SAAxC,KAAwC;AAAA,MAAjC,QAAiC,SAAjC,QAAiC;AAAA,MAAvB,MAAuB,SAAvB,MAAuB;AAAA,MAAf,WAAe,SAAf,WAAe;;AACrH,MAAI,WAAW,SAAf,EAA0B;AAAE;AAAS;;AAErC,MAAM,WAAW,YAAY,QAA7B;;AAEA,SAAO,OAAP,GAAiB,CAAC,SAAS,CAAT,CAAD,EAAc,SAAS,CAAT,CAAd,CAAjB;;AAEA,MAAI,QAAJ,EAAc;AACZ,WAAO,QAAP,GAAkB,MAAM,aAAN,CAAoB,QAApB,EAA8B,WAA9B,CAAlB;AACA,WAAO,GAAP,GAAkB,MAAM,SAAN,CAAgB,QAAhB,CAAlB;AACA,WAAO,KAAP,GAAkB,CAAlB;AACA,WAAO,EAAP,GAAkB,CAAlB;AACA,WAAO,KAAP,GAAkB,MAAM,UAAN,CAAiB,QAAjB,EAA2B,SAA3B,EAAsC,WAAtC,CAAlB;AACA,WAAO,EAAP,GAAkB,CAAlB;AACD,GAPD,MAQK,IAAI,UAAU,iBAAiB,aAA/B,EAA8C;AACjD,WAAO,QAAP,GAAkB,YAAY,SAAZ,CAAsB,QAAxC;AACA,WAAO,GAAP,GAAkB,YAAY,SAAZ,CAAsB,GAAxC;AACA,WAAO,KAAP,GAAkB,YAAY,SAAZ,CAAsB,KAAxC;AACA,WAAO,EAAP,GAAkB,OAAO,KAAP,GAAe,CAAjC;AACA,WAAO,KAAP,GAAkB,YAAY,SAAZ,CAAsB,KAAxC;AACA,WAAO,EAAP,GAAkB,OAAO,KAAP,GAAe,YAAY,OAAZ,CAAoB,UAArD;AACD,GAPI,MAQA;AACH,WAAO,QAAP,GAAkB,MAAM,aAAN,CAAoB,QAApB,EAA8B,WAA9B,CAAlB;AACA,WAAO,GAAP,GAAkB,MAAM,SAAN,CAAgB,QAAhB,CAAlB;AACA,WAAO,KAAP,GAAkB,OAAO,QAAP,GAAkB,YAAY,OAAZ,CAAoB,aAAxD;AACA,WAAO,KAAP,GAAkB,MAAM,UAAN,CAAiB,QAAjB,EAA2B,YAAY,OAAZ,CAAoB,SAA/C,EAA0D,WAA1D,CAAlB;;AAEA,WAAO,EAAP,GAAY,OAAO,KAAP,GAAe,YAAY,OAAZ,CAAoB,SAA/C;AACA,WAAO,EAAP,GAAY,OAAO,KAAP,GAAe,YAAY,OAAZ,CAAoB,SAA/C;AACD;AACF,CAhCD;;AAkCA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,KAAvB,EAA8B,UAAU,WAAV,EAAuB;AACnD,cAAY,OAAZ,GAAsB;AACpB,WAAO,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EADa;;AAGpB,mBAAe,CAHK,EAGA;AACpB,kBAAe,CAJK;AAKpB,cAAe,CALK;;AAOpB,WAAO,CAPa,EAOA;;AAEpB,gBAAY,CATQ,EASA;AACpB,eAAY,CAVQ,CAUA;AAVA,GAAtB;AAYD,CAbD;;AAeA,QAAQ,OAAR,GAAkB,OAAlB;AACA,QAAQ,KAAR,CAAc,IAAd,CAAmB,SAAnB;AACA,MAAM,KAAN,CAAY,aAAa,UAAzB,EAAqC,CACnC,cADmC,EAEnC,aAFmC,EAGnC,YAHmC,CAArC;AAKA,QAAQ,UAAR,CAAmB,OAAnB,GAA6B,YAA7B;;AAEA,eAAe,OAAf,GAAyB,QAAQ,QAAjC;;AAEA,OAAO,OAAP,GAAiB,OAAjB;;;;;AChKA,IAAM,UAAiB,QAAQ,QAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA,IAAM,UAAiB,QAAQ,kBAAR,CAAvB;AACA,IAAM,gBAAiB,QAAQ,kBAAR,CAAvB;AACA;AACA,IAAM,eAAiB,QAAQ,iBAAR,CAAvB;AACA,IAAM,cAAiB,QAAQ,gBAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,mBAAR,CAAvB;;AAEA;AACA,IAAM,gBAAgB,QAAQ,aAAR,IAAyB,QAAQ,oBAAjC,GAAuD,EAAvD,GAA2D,EAAjF;;AAEA,IAAM,SAAS;AACb,YAAU;AACR,aAAc,KADN;AAER,kBAAc,IAFN;;AAIR,YAAY,IAJJ;AAKR,UAAY,IALJ;AAMR,cAAY,IANJ;AAOR,aAAY,IAPJ;AAQR,gBAAY,IARJ;;AAUR,YAAQ,KAVA;AAWR,yBAAqB,KAXb;AAYR,UAAM,IAZE;;AAcR;AACA,YAAQ,GAfA;;AAiBR;AACA;AACA;AACA;AACA,WAAO,IArBC;;AAuBR;AACA;AACA;AACA;AACA,YAAQ;AA3BA,GADG;;AA+Bb,WAAS,iBAAU,OAAV,EAAmB,KAAnB,EAA0B,YAA1B,EAAwC,OAAxC,EAAiD,WAAjD,EAA8D,IAA9D,EAAoE;AAC3E,QAAI,CAAC,IAAL,EAAW;AAAE,aAAO,IAAP;AAAc;;AAE3B,QAAM,OAAO,MAAM,MAAN,CAAa,EAAb,EAAiB,YAAY,SAAZ,CAAsB,IAAvC,CAAb;AACA,QAAM,UAAU,aAAa,OAA7B;;AAEA,QAAI,QAAQ,MAAR,CAAe,OAAnB,EAA4B;AAC1B,UAAM,gBAAgB,QAAQ,MAA9B;AACA,UAAM,cAAc,EAAE,MAAM,KAAR,EAAe,OAAO,KAAtB,EAA6B,KAAK,KAAlC,EAAyC,QAAQ,KAAjD,EAApB;;AAEA;AACA,UAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,cAAc,KAA9B,CAAJ,EAA0C;AACxC,aAAK,IAAM,IAAX,IAAmB,WAAnB,EAAgC;AAC9B,sBAAY,IAAZ,IAAoB,gBAAgB,IAAhB,EACgB,cAAc,KAAd,CAAoB,IAApB,CADhB,EAEgB,IAFhB,EAGgB,YAAY,YAH5B,EAIgB,OAJhB,EAKgB,IALhB,EAMgB,cAAc,MAAd,IAAwB,aANxC,CAApB;AAOD;;AAED,oBAAY,IAAZ,GAAmB,YAAY,IAAZ,IAAoB,CAAC,YAAY,KAApD;AACA,oBAAY,GAAZ,GAAmB,YAAY,GAAZ,IAAoB,CAAC,YAAY,MAApD;;AAEA,YAAI,YAAY,IAAZ,IAAoB,YAAY,KAAhC,IAAyC,YAAY,GAArD,IAA4D,YAAY,MAA5E,EAAoF;AAClF,iBAAO;AACL,kBAAM,QADD;AAEL,mBAAO;AAFF,WAAP;AAID;AACF,OApBD,MAqBK;AACH,YAAM,QAAS,QAAQ,MAAR,CAAe,IAAf,KAAwB,GAAxB,IAA+B,KAAK,CAAL,GAAU,KAAK,KAAL,GAAc,aAAtE;AACA,YAAM,SAAS,QAAQ,MAAR,CAAe,IAAf,KAAwB,GAAxB,IAA+B,KAAK,CAAL,GAAU,KAAK,MAAL,GAAc,aAAtE;;AAEA,YAAI,SAAS,MAAb,EAAqB;AACnB,iBAAO;AACL,kBAAM,QADD;AAEL,kBAAM,CAAC,QAAO,GAAP,GAAa,EAAd,KAAqB,SAAQ,GAAR,GAAc,EAAnC;AAFD,WAAP;AAID;AACF;AACF;;AAED,WAAO,IAAP;AACD,GA7EY;;AA+Eb,WAAU,QAAQ,KAAR,GAAgB;AACxB,OAAI,UADoB;AAExB,OAAI,UAFoB;AAGxB,QAAI,WAHoB;;AAKxB,SAAa,UALW;AAMxB,UAAa,UANW;AAOxB,YAAa,UAPW;AAQxB,WAAa,UARW;AASxB,aAAa,WATW;AAUxB,iBAAa,WAVW;AAWxB,cAAa,WAXW;AAYxB,gBAAa;AAZW,GAAhB,GAaN;AACF,OAAI,WADF;AAEF,OAAI,WAFF;AAGF,QAAI,aAHF;;AAKF,SAAa,WALX;AAMF,UAAa,WANX;AAOF,YAAa,WAPX;AAQF,WAAa,WARX;AASF,aAAa,aATX;AAUF,iBAAa,aAVX;AAWF,cAAa,aAXX;AAYF,gBAAa;AAZX,GA5FS;;AA2Gb,aAAW,mBAAU,MAAV,EAAkB;AAC3B,QAAI,OAAO,IAAX,EAAiB;AACf,aAAO,OAAO,OAAP,CAAe,OAAO,IAAP,GAAc,OAAO,IAApC,CAAP;AACD,KAFD,MAGK,IAAI,OAAO,KAAX,EAAkB;AACrB,UAAI,YAAY,EAAhB;AACA,UAAM,YAAY,CAAC,KAAD,EAAQ,QAAR,EAAkB,MAAlB,EAA0B,OAA1B,CAAlB;;AAEA,WAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,CAApB,EAAuB,GAAvB,EAA4B;AAC1B,YAAI,OAAO,KAAP,CAAa,UAAU,CAAV,CAAb,CAAJ,EAAgC;AAC9B,uBAAa,UAAU,CAAV,CAAb;AACD;AACF;;AAED,aAAO,OAAO,OAAP,CAAe,SAAf,CAAP;AACD;AACF;AA3HY,CAAf;;AA8HA;AACA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,KAAzB,EAAgC,gBAAmC;AAAA,MAAvB,MAAuB,QAAvB,MAAuB;AAAA,MAAf,WAAe,QAAf,WAAe;;AACjE,MAAI,OAAO,IAAP,KAAgB,aAAhB,IAAiC,CAAC,YAAY,QAAZ,CAAqB,KAA3D,EAAkE;AAChE;AACD;;AAED,MAAM,YAAY,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,YAAY,OAAvC,CAAlB;AACA,MAAM,gBAAgB,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,MAAjD;;AAEA;;;;;;AAMA,MAAI,cAAc,MAAd,IAAwB,cAAc,mBAA1C,EAA+D;AAC7D,QAAM,cAAc,MAAM,MAAN,CAAa,EAAb,EAAiB,YAAY,QAAZ,CAAqB,KAAtC,CAApB;;AAEA,gBAAY,GAAZ,GAAqB,YAAY,GAAZ,IAAuB,YAAY,IAAZ,IAAsB,CAAC,YAAY,MAA/E;AACA,gBAAY,IAAZ,GAAqB,YAAY,IAAZ,IAAuB,YAAY,GAAZ,IAAsB,CAAC,YAAY,KAA/E;AACA,gBAAY,MAAZ,GAAqB,YAAY,MAAZ,IAAuB,YAAY,KAAZ,IAAsB,CAAC,YAAY,GAA/E;AACA,gBAAY,KAAZ,GAAqB,YAAY,KAAZ,IAAuB,YAAY,MAAZ,IAAsB,CAAC,YAAY,IAA/E;;AAEA,gBAAY,QAAZ,CAAqB,YAArB,GAAoC,WAApC;AACD,GATD,MAUK;AACH,gBAAY,QAAZ,CAAqB,YAArB,GAAoC,IAApC;AACD;;AAED;AACA,MAAI,cAAc,mBAAlB,EAAuC;AACrC,gBAAY,sBAAZ,GAAqC,UAAU,KAAV,GAAkB,UAAU,MAAjE;AACD;;AAED,cAAY,WAAZ,GAA0B;AACxB,WAAY,SADY;AAExB,aAAY,MAAM,MAAN,CAAa,EAAb,EAAiB,SAAjB,CAFY;AAGxB,cAAY,MAAM,MAAN,CAAa,EAAb,EAAiB,SAAjB,CAHY;AAIxB,cAAY,MAAM,MAAN,CAAa,EAAb,EAAiB,SAAjB,CAJY;AAKxB,WAAY;AACV,YAAM,CADI,EACD,OAAQ,CADP,EACU,OAAQ,CADlB;AAEV,WAAM,CAFI,EAED,QAAQ,CAFP,EAEU,QAAQ;AAFlB;AALY,GAA1B;;AAWA,SAAO,IAAP,GAAc,YAAY,WAAZ,CAAwB,QAAtC;AACA,SAAO,SAAP,GAAmB,YAAY,WAAZ,CAAwB,KAA3C;AACD,CA9CD;;AAgDA;AACA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,KAAzB,EAAgC,iBAA0C;AAAA,MAA9B,MAA8B,SAA9B,MAA8B;AAAA,MAAtB,KAAsB,SAAtB,KAAsB;AAAA,MAAf,WAAe,SAAf,WAAe;;AACxE,MAAI,UAAU,MAAV,IAAoB,CAAC,YAAY,QAAZ,CAAqB,KAA9C,EAAqD;AAAE;AAAS;;AAEhE,MAAM,gBAAgB,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,MAAjD;AACA,MAAM,SAAS,cAAc,MAA7B;AACA,MAAM,aAAa,WAAW,YAAX,IAA2B,WAAW,QAAzD;;AAEA,MAAI,QAAQ,YAAY,QAAZ,CAAqB,KAAjC;;AAEA,MAAM,QAAa,YAAY,WAAZ,CAAwB,KAA3C;AACA,MAAM,UAAa,YAAY,WAAZ,CAAwB,OAA3C;AACA,MAAM,WAAa,YAAY,WAAZ,CAAwB,QAA3C;AACA,MAAM,QAAa,YAAY,WAAZ,CAAwB,KAA3C;AACA,MAAM,WAAa,MAAM,MAAN,CAAa,YAAY,WAAZ,CAAwB,QAArC,EAA+C,QAA/C,CAAnB;AACA,MAAM,gBAAgB,KAAtB;;AAEA,MAAI,KAAK,OAAO,EAAhB;AACA,MAAI,KAAK,OAAO,EAAhB;;AAEA,MAAI,cAAc,mBAAd,IAAqC,cAAc,MAAvD,EAA+D;AAC7D;AACA,QAAM,mBAAmB,cAAc,mBAAd,GACrB,YAAY,sBADS,GAErB,CAFJ;;AAIA,YAAQ,YAAY,QAAZ,CAAqB,YAA7B;;AAEA,QAAK,cAAc,IAAd,IAAsB,cAAc,MAArC,IACI,cAAc,KAAd,IAAuB,cAAc,GAD7C,EACmD;AACjD,WAAK,CAAC,EAAD,GAAM,gBAAX;AACD,KAHD,MAIK,IAAI,cAAc,IAAd,IAAsB,cAAc,KAAxC,EAAgD;AAAE,WAAK,KAAK,gBAAV;AAA6B,KAA/E,MACA,IAAI,cAAc,GAAd,IAAsB,cAAc,MAAxC,EAAgD;AAAE,WAAK,KAAK,gBAAV;AAA6B;AACrF;;AAED;AACA,MAAI,MAAM,GAAV,EAAkB;AAAE,YAAQ,GAAR,IAAkB,EAAlB;AAAuB;AAC3C,MAAI,MAAM,MAAV,EAAkB;AAAE,YAAQ,MAAR,IAAkB,EAAlB;AAAuB;AAC3C,MAAI,MAAM,IAAV,EAAkB;AAAE,YAAQ,IAAR,IAAkB,EAAlB;AAAuB;AAC3C,MAAI,MAAM,KAAV,EAAkB;AAAE,YAAQ,KAAR,IAAkB,EAAlB;AAAuB;;AAE3C,MAAI,UAAJ,EAAgB;AACd;AACA,UAAM,MAAN,CAAa,QAAb,EAAuB,OAAvB;;AAEA,QAAI,WAAW,YAAf,EAA6B;AAC3B;AACA,UAAI,aAAJ;;AAEA,UAAI,SAAS,GAAT,GAAe,SAAS,MAA5B,EAAoC;AAClC,eAAO,SAAS,GAAhB;;AAEA,iBAAS,GAAT,GAAe,SAAS,MAAxB;AACA,iBAAS,MAAT,GAAkB,IAAlB;AACD;AACD,UAAI,SAAS,IAAT,GAAgB,SAAS,KAA7B,EAAoC;AAClC,eAAO,SAAS,IAAhB;;AAEA,iBAAS,IAAT,GAAgB,SAAS,KAAzB;AACA,iBAAS,KAAT,GAAiB,IAAjB;AACD;AACF;AACF,GArBD,MAsBK;AACH;AACA,aAAS,GAAT,GAAkB,KAAK,GAAL,CAAS,QAAQ,GAAjB,EAAsB,MAAM,MAA5B,CAAlB;AACA,aAAS,MAAT,GAAkB,KAAK,GAAL,CAAS,QAAQ,MAAjB,EAAyB,MAAM,GAA/B,CAAlB;AACA,aAAS,IAAT,GAAkB,KAAK,GAAL,CAAS,QAAQ,IAAjB,EAAuB,MAAM,KAA7B,CAAlB;AACA,aAAS,KAAT,GAAkB,KAAK,GAAL,CAAS,QAAQ,KAAjB,EAAwB,MAAM,IAA9B,CAAlB;AACD;;AAED,WAAS,KAAT,GAAkB,SAAS,KAAT,GAAkB,SAAS,IAA7C;AACA,WAAS,MAAT,GAAkB,SAAS,MAAT,GAAkB,SAAS,GAA7C;;AAEA,OAAK,IAAM,IAAX,IAAmB,QAAnB,EAA6B;AAC3B,UAAM,IAAN,IAAc,SAAS,IAAT,IAAiB,SAAS,IAAT,CAA/B;AACD;;AAED,SAAO,KAAP,GAAe,YAAY,QAAZ,CAAqB,KAApC;AACA,SAAO,IAAP,GAAc,QAAd;AACA,SAAO,SAAP,GAAmB,KAAnB;AACD,CAjFD;;AAmFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,aAAa,SAAb,CAAuB,SAAvB,GAAmC,UAAU,OAAV,EAAmB;AACpD,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,OAAhB,CAAJ,EAA8B;AAC5B,SAAK,OAAL,CAAa,MAAb,CAAoB,OAApB,GAA8B,QAAQ,OAAR,KAAoB,KAApB,GAA2B,KAA3B,GAAkC,IAAhE;AACA,SAAK,YAAL,CAAkB,QAAlB,EAA4B,OAA5B;AACA,SAAK,WAAL,CAAiB,QAAjB,EAA2B,OAA3B;;AAEA,QAAI,eAAe,IAAf,CAAoB,QAAQ,IAA5B,CAAJ,EAAuC;AACrC,WAAK,OAAL,CAAa,MAAb,CAAoB,IAApB,GAA2B,QAAQ,IAAnC;AACD,KAFD,MAGK,IAAI,QAAQ,IAAR,KAAiB,IAArB,EAA2B;AAC9B,WAAK,OAAL,CAAa,MAAb,CAAoB,IAApB,GAA2B,eAAe,MAAf,CAAsB,IAAjD;AACD;;AAED,QAAI,MAAM,EAAN,CAAS,IAAT,CAAc,QAAQ,mBAAtB,CAAJ,EAAgD;AAC9C,WAAK,OAAL,CAAa,MAAb,CAAoB,mBAApB,GAA0C,QAAQ,mBAAlD;AACD,KAFD,MAGK,IAAI,MAAM,EAAN,CAAS,IAAT,CAAc,QAAQ,MAAtB,CAAJ,EAAmC;AACtC,WAAK,OAAL,CAAa,MAAb,CAAoB,MAApB,GAA6B,QAAQ,MAArC;AACD;;AAED,WAAO,IAAP;AACD;AACD,MAAI,MAAM,EAAN,CAAS,IAAT,CAAc,OAAd,CAAJ,EAA4B;AAC1B,SAAK,OAAL,CAAa,MAAb,CAAoB,OAApB,GAA8B,OAA9B;;AAEA,QAAI,CAAC,OAAL,EAAc;AACZ,WAAK,aAAL,GAAqB,KAAK,aAAL,GAAqB,KAAK,WAAL,GAAmB,IAA7D;AACD;;AAED,WAAO,IAAP;AACD;AACD,SAAO,KAAK,OAAL,CAAa,MAApB;AACD,CAhCD;;AAkCA,SAAS,eAAT,CAA0B,IAA1B,EAAgC,KAAhC,EAAuC,IAAvC,EAA6C,OAA7C,EAAsD,mBAAtD,EAA2E,IAA3E,EAAiF,MAAjF,EAAyF;AACvF;AACA,MAAI,CAAC,KAAL,EAAY;AAAE,WAAO,KAAP;AAAe;;AAE7B;AACA,MAAI,UAAU,IAAd,EAAoB;AAClB;AACA,QAAM,QAAS,MAAM,EAAN,CAAS,MAAT,CAAgB,KAAK,KAArB,IAA8B,KAAK,KAAnC,GAA4C,KAAK,KAAL,GAAc,KAAK,IAA9E;AACA,QAAM,SAAS,MAAM,EAAN,CAAS,MAAT,CAAgB,KAAK,MAArB,IAA8B,KAAK,MAAnC,GAA4C,KAAK,MAAL,GAAc,KAAK,GAA9E;;AAEA,QAAI,QAAQ,CAAZ,EAAe;AACb,UAAS,SAAS,MAAlB,EAA2B;AAAE,eAAO,OAAP;AAAiB,OAA9C,MACK,IAAI,SAAS,OAAb,EAAsB;AAAE,eAAO,MAAP;AAAiB;AAC/C;AACD,QAAI,SAAS,CAAb,EAAgB;AACd,UAAS,SAAS,KAAlB,EAA4B;AAAE,eAAO,QAAP;AAAkB,OAAhD,MACK,IAAI,SAAS,QAAb,EAAuB;AAAE,eAAO,KAAP;AAAkB;AACjD;;AAED,QAAI,SAAS,MAAb,EAAuB;AAAE,aAAO,KAAK,CAAL,GAAU,CAAC,SAAU,CAAV,GAAa,KAAK,IAAlB,GAAwB,KAAK,KAA9B,IAAwC,MAAzD;AAAmE;AAC5F,QAAI,SAAS,KAAb,EAAuB;AAAE,aAAO,KAAK,CAAL,GAAU,CAAC,UAAU,CAAV,GAAa,KAAK,GAAlB,GAAwB,KAAK,MAA9B,IAAwC,MAAzD;AAAmE;;AAE5F,QAAI,SAAS,OAAb,EAAuB;AAAE,aAAO,KAAK,CAAL,GAAU,CAAC,SAAU,CAAV,GAAa,KAAK,KAAlB,GAA0B,KAAK,IAAhC,IAAwC,MAAzD;AAAmE;AAC5F,QAAI,SAAS,QAAb,EAAuB;AAAE,aAAO,KAAK,CAAL,GAAU,CAAC,UAAU,CAAV,GAAa,KAAK,MAAlB,GAA0B,KAAK,GAAhC,IAAwC,MAAzD;AAAmE;AAC7F;;AAED;AACA,MAAI,CAAC,MAAM,EAAN,CAAS,OAAT,CAAiB,OAAjB,CAAL,EAAgC;AAAE,WAAO,KAAP;AAAe;;AAEjD,SAAO,MAAM,EAAN,CAAS,OAAT,CAAiB,KAAjB;AACP;AADO,IAEH,UAAU;AACZ;AAHK,IAIH,MAAM,WAAN,CAAkB,OAAlB,EAA2B,KAA3B,EAAkC,mBAAlC,CAJJ;AAKD;;AAED,YAAY,OAAZ,CAAoB,EAApB,CAAuB,KAAvB,EAA8B,UAAU,WAAV,EAAuB;AACnD,cAAY,UAAZ,GAAyB,IAAzB;AACD,CAFD;;AAIA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,WAAzB,EAAsC,iBAA2C;AAAA,MAA/B,WAA+B,SAA/B,WAA+B;AAAA,MAAlB,MAAkB,SAAlB,MAAkB;AAAA,MAAV,MAAU,SAAV,MAAU;;AAC/E,MAAI,WAAW,QAAX,IAAuB,CAAC,YAAY,UAAxC,EAAoD;AAAE;AAAS;;AAE/D,MAAM,UAAU,YAAY,MAAZ,CAAmB,OAAnC;;AAEA,MAAI,QAAQ,MAAR,CAAe,MAAnB,EAA2B;AACzB,QAAI,YAAY,UAAZ,KAA2B,GAA/B,EAAoC;AAClC,aAAO,EAAP,GAAY,OAAO,EAAnB;AACD,KAFD,MAGK;AACH,aAAO,EAAP,GAAY,OAAO,EAAnB;AACD;AACD,WAAO,IAAP,GAAc,IAAd;AACD,GARD,MASK;AACH,WAAO,IAAP,GAAc,YAAY,UAA1B;;AAEA,QAAI,YAAY,UAAZ,KAA2B,GAA/B,EAAoC;AAClC,aAAO,EAAP,GAAY,CAAZ;AACD,KAFD,MAGK,IAAI,YAAY,UAAZ,KAA2B,GAA/B,EAAoC;AACvC,aAAO,EAAP,GAAY,CAAZ;AACD;AACF;AACF,CAxBD;;AA0BA,QAAQ,MAAR,GAAiB,MAAjB;AACA,QAAQ,KAAR,CAAc,IAAd,CAAmB,QAAnB;AACA,MAAM,KAAN,CAAY,aAAa,UAAzB,EAAqC,CACnC,aADmC,EAEnC,YAFmC,EAGnC,oBAHmC,EAInC,qBAJmC,EAKnC,WALmC,CAArC;AAOA,QAAQ,UAAR,CAAmB,MAAnB,GAA4B,WAA5B;;AAEA,eAAe,MAAf,GAAwB,OAAO,QAA/B;;AAEA,OAAO,OAAP,GAAiB,MAAjB;;;;;AC7aA,IAAM,MAAiB,QAAQ,aAAR,CAAvB;AACA,IAAM,YAAiB,QAAQ,gBAAR,EAA0B,SAAjD;AACA,IAAM,KAAiB,QAAQ,YAAR,CAAvB;AACA,IAAM,WAAiB,QAAQ,kBAAR,CAAvB;AACA,IAAM,cAAiB,QAAQ,eAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,kBAAR,CAAvB;;AAEA,IAAM,aAAa;AACjB,YAAU;AACR,aAAW,KADH;AAER,eAAW,IAFH,EAEa;AACrB,YAAW,EAHH;AAIR,WAAW,GAJH,CAIa;AAJb,GADO;;AAQjB,eAAa,IARI;AASjB,KAAG,IATc,EASL;AACZ,KAAG,CAVc,EAUX,GAAG,CAVQ,EAUL;;AAEZ,eAAa,KAZI;AAajB,YAAU,CAbO;;AAejB,SAAO,eAAU,WAAV,EAAuB;AAC5B,eAAW,WAAX,GAAyB,IAAzB;AACA,QAAI,MAAJ,CAAW,WAAW,CAAtB;;AAEA,eAAW,WAAX,GAAyB,WAAzB;AACA,eAAW,QAAX,GAAsB,IAAI,IAAJ,GAAW,OAAX,EAAtB;AACA,eAAW,CAAX,GAAe,IAAI,OAAJ,CAAY,WAAW,MAAvB,CAAf;AACD,GAtBgB;;AAwBjB,QAAM,gBAAY;AAChB,eAAW,WAAX,GAAyB,KAAzB;AACA,QAAI,MAAJ,CAAW,WAAW,CAAtB;AACD,GA3BgB;;AA6BjB;AACA,UAAQ,kBAAY;AAClB,QAAM,UAAU,WAAW,WAAX,CAAuB,MAAvB,CAA8B,OAA9B,CAAsC,WAAW,WAAX,CAAuB,QAAvB,CAAgC,IAAtE,EAA4E,UAA5F;AACA,QAAM,YAAY,QAAQ,SAAR,IAAqB,UAAU,WAAW,WAAX,CAAuB,OAAjC,CAAvC;AACA,QAAM,MAAM,IAAI,IAAJ,GAAW,OAAX,EAAZ;AACA;AACA,QAAM,KAAK,CAAC,MAAM,WAAW,QAAlB,IAA8B,IAAzC;AACA;AACA,QAAM,IAAI,QAAQ,KAAR,GAAgB,EAA1B;;AAEA,QAAI,KAAK,CAAT,EAAY;AACV,UAAI,GAAG,MAAH,CAAU,SAAV,CAAJ,EAA0B;AACxB,kBAAU,QAAV,CAAmB,WAAW,CAAX,GAAe,CAAlC,EAAqC,WAAW,CAAX,GAAe,CAApD;AACD,OAFD,MAGK,IAAI,SAAJ,EAAe;AAClB,kBAAU,UAAV,IAAwB,WAAW,CAAX,GAAe,CAAvC;AACA,kBAAU,SAAV,IAAwB,WAAW,CAAX,GAAe,CAAvC;AACD;;AAED,iBAAW,QAAX,GAAsB,GAAtB;AACD;;AAED,QAAI,WAAW,WAAf,EAA4B;AAC1B,UAAI,MAAJ,CAAW,WAAW,CAAtB;AACA,iBAAW,CAAX,GAAe,IAAI,OAAJ,CAAY,WAAW,MAAvB,CAAf;AACD;AACF,GAvDgB;AAwDjB,SAAO,eAAU,YAAV,EAAwB,UAAxB,EAAoC;AACzC,QAAM,UAAU,aAAa,OAA7B;;AAEA,WAAO,QAAQ,UAAR,EAAoB,UAApB,IAAkC,QAAQ,UAAR,EAAoB,UAApB,CAA+B,OAAxE;AACD,GA5DgB;AA6DjB,qBAAmB,iCAAoC;AAAA,QAAxB,WAAwB,QAAxB,WAAwB;AAAA,QAAX,OAAW,QAAX,OAAW;;AACrD,QAAI,EAAE,YAAY,WAAZ,MACG,WAAW,KAAX,CAAiB,YAAY,MAA7B,EAAqC,YAAY,QAAZ,CAAqB,IAA1D,CADL,CAAJ,EAC2E;AACzE;AACD;;AAED,QAAI,YAAY,UAAhB,EAA4B;AAC1B,iBAAW,CAAX,GAAe,WAAW,CAAX,GAAe,CAA9B;AACA;AACD;;AAED,QAAI,YAAJ;AACA,QAAI,cAAJ;AACA,QAAI,eAAJ;AACA,QAAI,aAAJ;;AAEA,QAAM,UAAU,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,YAAY,QAAZ,CAAqB,IAAhD,EAAsD,UAAtE;AACA,QAAM,YAAY,QAAQ,SAAR,IAAqB,UAAU,YAAY,OAAtB,CAAvC;;AAEA,QAAI,GAAG,MAAH,CAAU,SAAV,CAAJ,EAA0B;AACxB,aAAS,QAAQ,OAAR,GAAkB,WAAW,MAAtC;AACA,YAAS,QAAQ,OAAR,GAAkB,WAAW,MAAtC;AACA,cAAS,QAAQ,OAAR,GAAkB,UAAU,UAAV,GAAwB,WAAW,MAA9D;AACA,eAAS,QAAQ,OAAR,GAAkB,UAAU,WAAV,GAAwB,WAAW,MAA9D;AACD,KALD,MAMK;AACH,UAAM,OAAO,SAAS,oBAAT,CAA8B,SAA9B,CAAb;;AAEA,aAAS,QAAQ,OAAR,GAAkB,KAAK,IAAL,GAAc,WAAW,MAApD;AACA,YAAS,QAAQ,OAAR,GAAkB,KAAK,GAAL,GAAc,WAAW,MAApD;AACA,cAAS,QAAQ,OAAR,GAAkB,KAAK,KAAL,GAAc,WAAW,MAApD;AACA,eAAS,QAAQ,OAAR,GAAkB,KAAK,MAAL,GAAc,WAAW,MAApD;AACD;;AAED,eAAW,CAAX,GAAgB,QAAQ,CAAR,GAAW,OAAM,CAAC,CAAP,GAAU,CAArC;AACA,eAAW,CAAX,GAAgB,SAAQ,CAAR,GAAY,MAAK,CAAC,CAAN,GAAS,CAArC;;AAEA,QAAI,CAAC,WAAW,WAAhB,EAA6B;AAC3B;AACA,iBAAW,MAAX,GAAoB,QAAQ,MAA5B;AACA,iBAAW,KAAX,GAAoB,QAAQ,KAA5B;;AAEA,iBAAW,KAAX,CAAiB,WAAjB;AACD;AACF;AAzGgB,CAAnB;;AA4GA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,aAAvB,EAAsC,YAAY;AAChD,aAAW,IAAX;AACD,CAFD;;AAIA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,aAAvB,EAAsC,WAAW,iBAAjD;;AAEA,eAAe,SAAf,CAAyB,UAAzB,GAAsC,WAAW,QAAjD;;AAEA,OAAO,OAAP,GAAiB,UAAjB;;;;;AC3HA;AACA,IAAM,eAAe,QAAQ,iBAAR,CAArB;AACA,IAAM,UAAe,QAAQ,iBAAR,CAArB;AACA,IAAM,KAAe,QAAQ,aAAR,CAArB;AACA,IAAM,WAAe,QAAQ,mBAAR,CAArB;;eAEqB,QAAQ,UAAR,C;IAAb,Q,YAAA,Q;;AAER,aAAa,SAAb,CAAuB,SAAvB,GAAmC,UAAU,OAAV,EAAmB,KAAnB,EAA0B,WAA1B,EAAuC,OAAvC,EAAgD;AACjF,MAAM,SAAS,KAAK,oBAAL,CAA0B,OAA1B,EAAmC,KAAnC,EAA0C,WAA1C,EAAuD,OAAvD,CAAf;;AAEA,MAAI,KAAK,OAAL,CAAa,aAAjB,EAAgC;AAC9B,WAAO,KAAK,OAAL,CAAa,aAAb,CAA2B,OAA3B,EAAoC,KAApC,EAA2C,MAA3C,EAAmD,IAAnD,EAAyD,OAAzD,EAAkE,WAAlE,CAAP;AACD;;AAED,SAAO,MAAP;AACD,CARD;;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,aAAa,SAAb,CAAuB,UAAvB,GAAoC,SAAS,UAAU,QAAV,EAAoB;AAC/D,SAAO,KAAK,iBAAL,CAAuB,YAAvB,EAAqC,QAArC,CAAP;AACD,CAFmC,EAEjC,mGAFiC,CAApC;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,aAAa,SAAb,CAAuB,SAAvB,GAAmC,SAAS,UAAU,QAAV,EAAoB;AAC9D,SAAO,KAAK,iBAAL,CAAuB,WAAvB,EAAoC,QAApC,CAAP;AACD,CAFkC,EAEhC,iGAFgC,CAAnC;;AAIA,aAAa,SAAb,CAAuB,UAAvB,GAAoC,UAAU,UAAV,EAAsB,mBAAtB,EAA2C,OAA3C,EAAoD;AACtF,MAAI,CAAC,UAAD,IAAe,CAAC,GAAG,OAAH,CAAW,OAAX,CAApB,EAAyC;AAAE,WAAO,KAAP;AAAe;;AAE1D,MAAI,GAAG,MAAH,CAAU,UAAV,CAAJ,EAA2B;AACzB,WAAO,SAAS,WAAT,CAAqB,OAArB,EAA8B,UAA9B,EAA0C,mBAA1C,CAAP;AACD,GAFD,MAGK,IAAI,GAAG,OAAH,CAAW,UAAX,CAAJ,EAA4B;AAC/B,WAAO,SAAS,YAAT,CAAsB,UAAtB,EAAkC,OAAlC,CAAP;AACD;;AAED,SAAO,KAAP;AACD,CAXD;;AAaA,aAAa,SAAb,CAAuB,SAAvB,GAAmC,UAAU,SAAV,EAAqB,mBAArB,EAA0C,OAA1C,EAAmD;AACpF,MAAI,CAAC,SAAL,EAAgB;AAAE,WAAO,IAAP;AAAc;;AAEhC,MAAI,CAAC,GAAG,OAAH,CAAW,OAAX,CAAL,EAA0B;AAAE,WAAO,KAAP;AAAe;;AAE3C,MAAI,GAAG,MAAH,CAAU,SAAV,CAAJ,EAA0B;AACxB,WAAO,SAAS,WAAT,CAAqB,OAArB,EAA8B,SAA9B,EAAyC,mBAAzC,CAAP;AACD,GAFD,MAGK,IAAI,GAAG,OAAH,CAAW,SAAX,CAAJ,EAA2B;AAC9B,WAAO,SAAS,YAAT,CAAsB,SAAtB,EAAiC,OAAjC,CAAP;AACD;;AAED,SAAO,KAAP;AACD,CAbD;;AAeA,aAAa,SAAb,CAAuB,eAAvB,GAAyC,UAAU,OAAV,EAAmB,mBAAnB,EAAwC,WAAxC,EAAqD;AAC5F,SAAQ,CAAC,KAAK,UAAL,CAAgB,QAAQ,UAAxB,EAAoC,mBAApC,EAAyD,WAAzD,CAAD,IACH,KAAK,SAAL,CAAe,QAAQ,SAAvB,EAAkC,mBAAlC,EAAuD,WAAvD,CADL;AAED,CAHD;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,aAAa,SAAb,CAAuB,aAAvB,GAAuC,UAAU,OAAV,EAAmB;AACxD,MAAI,GAAG,QAAH,CAAY,OAAZ,CAAJ,EAA0B;AACxB,SAAK,OAAL,CAAa,aAAb,GAA6B,OAA7B;;AAEA,WAAO,IAAP;AACD;;AAED,MAAI,YAAY,IAAhB,EAAsB;AACpB,WAAO,KAAK,OAAL,CAAa,aAApB;;AAEA,WAAO,IAAP;AACD;;AAED,SAAO,KAAK,OAAL,CAAa,aAApB;AACD,CAdD;;AAgBA;;;;;;;AAOA,aAAa,SAAb,CAAuB,WAAvB,GAAqC,UAAU,QAAV,EAAoB;AACvD,MAAI,GAAG,IAAH,CAAQ,QAAR,CAAJ,EAAuB;AACrB,SAAK,OAAL,CAAa,WAAb,GAA2B,QAA3B;;AAEA,WAAO,IAAP;AACD;;AAED,MAAI,aAAa,IAAjB,EAAuB;AACrB,WAAO,KAAK,OAAL,CAAa,WAApB;;AAEA,WAAO,IAAP;AACD;;AAED,SAAO,KAAK,OAAL,CAAa,WAApB;AACD,CAdD;;AAgBA,aAAa,SAAb,CAAuB,oBAAvB,GAA8C,UAAU,OAAV,EAAmB,KAAnB,EAA0B,WAA1B,EAAuC,OAAvC,EAAgD;AAC5F,MAAM,OAAO,KAAK,OAAL,CAAa,OAAb,CAAb;AACA,MAAM,UAAU,MAAM,OAAN,IAAkB;AAChC,OAAG,CAD6B;AAEhC,OAAG,CAF6B;AAGhC,OAAG,CAH6B;AAIhC,OAAG;AAJ6B,GAAD,CAK9B,MAAM,MALwB,CAAjC;AAMA,MAAI,SAAS,IAAb;;AAEA,wBAAyB,QAAQ,KAAjC,eAAwC;AAAA;;AAAA,WAAf,QAAQ,KAAO;AAAA,QAA7B,UAA6B;;AACtC;AACA,QAAI,YAAY,aAAZ,IACG,gBAAgB,IAAhB,CAAqB,YAAY,WAAjC,CADH,IAEG,CAAC,UAAU,KAAK,OAAL,CAAa,UAAb,EAAyB,YAApC,MAAsD,CAF7D,EAEgE;AAC9D;AACD;;AAED,aAAS,QAAQ,UAAR,EAAoB,OAApB,CAA4B,OAA5B,EAAqC,KAArC,EAA4C,IAA5C,EAAkD,OAAlD,EAA2D,WAA3D,EAAwE,IAAxE,CAAT;;AAEA,QAAI,MAAJ,EAAY;AACV,aAAO,MAAP;AACD;AACF;AACF,CAxBD;;;;;ACrLA,IAAM,WAAiB,QAAQ,aAAR,CAAvB;AACA,IAAM,eAAiB,QAAQ,iBAAR,CAAvB;AACA,IAAM,cAAiB,QAAQ,gBAAR,CAAvB;AACA,IAAM,UAAiB,QAAQ,iBAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,mBAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA,IAAM,UAAiB,QAAQ,kBAAR,EAA4B,GAA5B,EAAvB;;AAEA,QAAQ,uBAAR;;AAEA,IAAM,YAAY;AAChB,kBADgB;AAEhB,gDAFgB;AAGhB;AACA,mBAAiB,QAJD;AAKhB,YAAU;AACR,eAAW;AACT,mBAAa,KADJ;AAET,WAAK,QAFI;AAGT,qBAAe,CAHN;AAIT,iBAAY,IAJH;AAKT,kBAAY,IALH;;AAOT;AACA;AACA,oBAAc;AATL;AADH,GALM;AAkBhB,qBAAmB,2BAAU,MAAV,EAAkB;AACnC,UAAM,MAAN,CAAa,OAAO,QAApB,EAA8B,UAAU,QAAV,CAAmB,SAAjD;AACD,GApBe;AAqBhB;AArBgB,CAAlB;;AAwBA;AACA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,MAAvB,EAA+B,gBAAwD;AAAA,MAA5C,WAA4C,QAA5C,WAA4C;AAAA,MAA/B,OAA+B,QAA/B,OAA+B;AAAA,MAAtB,KAAsB,QAAtB,KAAsB;AAAA,MAAf,WAAe,QAAf,WAAe;;AACrF,MAAI,YAAY,WAAZ,EAAJ,EAA+B;AAAE;AAAS;;AAE1C,MAAM,aAAa,cAAc,WAAd,EAA2B,OAA3B,EAAoC,KAApC,EAA2C,WAA3C,CAAnB;AACA,UAAQ,WAAR,EAAqB,UAArB;AACD,CALD;;AAOA;AACA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,MAAvB,EAA+B,iBAAwD;AAAA,MAA5C,WAA4C,SAA5C,WAA4C;AAAA,MAA/B,OAA+B,SAA/B,OAA+B;AAAA,MAAtB,KAAsB,SAAtB,KAAsB;AAAA,MAAf,WAAe,SAAf,WAAe;;AACrF,MAAI,YAAY,WAAZ,KAA4B,OAA5B,IACG,YAAY,aADf,IAEG,YAAY,WAAZ,EAFP,EAEkC;AAAE;AAAS;;AAE7C,MAAM,aAAa,cAAc,WAAd,EAA2B,OAA3B,EAAoC,KAApC,EAA2C,WAA3C,CAAnB;AACA,UAAQ,WAAR,EAAqB,UAArB;AACD,CAPD;;AASA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,MAAvB,EAA+B,UAAU,GAAV,EAAe;AAAA,MACpC,WADoC,GACb,GADa,CACpC,WADoC;AAAA,MACvB,KADuB,GACb,GADa,CACvB,KADuB;;;AAG5C,MAAI,CAAC,YAAY,aAAb,IACG,YAAY,WAAZ,EADH,IAEG,CAAC,YAAY,eAFhB,IAGG,CAAC,YAAY,QAAZ,CAAqB,IAH7B,EAGmC;AACjC;AACD;;AAED,UAAQ,IAAR,CAAa,cAAb,EAA6B,GAA7B;;AAEA,MAAM,SAAS,YAAY,MAA3B;;AAEA,MAAI,YAAY,QAAZ,CAAqB,IAArB,IAA6B,MAAjC,EAAyC;AACvC;AACA,QAAI,OAAO,OAAP,CAAe,YAAY,QAAZ,CAAqB,IAApC,EAA0C,WAA1C,IACG,CAAC,uBAAuB,MAAvB,EAA+B,YAAY,OAA3C,EAAoD,YAAY,QAAhE,CADR,EACmF;AACjF,kBAAY,IAAZ,CAAiB,KAAjB;AACD,KAHD,MAIK;AACH,kBAAY,KAAZ,CAAkB,YAAY,QAA9B,EAAwC,MAAxC,EAAgD,YAAY,OAA5D;AACD;AACF;AACF,CAxBD;;AA0BA;AACA;AACA,SAAS,cAAT,CAAyB,MAAzB,EAAiC,YAAjC,EAA+C,OAA/C,EAAwD,WAAxD,EAAqE;AACnE,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,MAAhB,KACG,aAAa,eAAb,CAA6B,aAAa,OAAb,CAAqB,OAAO,IAA5B,CAA7B,EAAgE,OAAhE,EAAyE,WAAzE,CADH,IAEG,aAAa,OAAb,CAAqB,OAAO,IAA5B,EAAkC,OAFrC,IAGG,uBAAuB,YAAvB,EAAqC,OAArC,EAA8C,MAA9C,CAHP,EAG8D;AAC5D,WAAO,MAAP;AACD;;AAED,SAAO,IAAP;AACD;;AAED,SAAS,gBAAT,CAA2B,WAA3B,EAAwC,OAAxC,EAAiD,KAAjD,EAAwD,OAAxD,EAAiE,aAAjE,EAAgF,WAAhF,EAA6F;AAC3F,OAAK,IAAI,IAAI,CAAR,EAAW,MAAM,QAAQ,MAA9B,EAAsC,IAAI,GAA1C,EAA+C,GAA/C,EAAoD;AAClD,QAAM,QAAQ,QAAQ,CAAR,CAAd;AACA,QAAM,eAAe,cAAc,CAAd,CAArB;AACA,QAAM,SAAS,eAAe,MAAM,SAAN,CAAgB,OAAhB,EAAyB,KAAzB,EAAgC,WAAhC,EAA6C,YAA7C,CAAf,EACe,KADf,EAEe,YAFf,EAGe,WAHf,CAAf;;AAKA,QAAI,MAAJ,EAAY;AACV,aAAO;AACL,sBADK;AAEL,gBAAQ,KAFH;AAGL,iBAAS;AAHJ,OAAP;AAKD;AACF;;AAED,SAAO,EAAP;AACD;;AAED,SAAS,aAAT,CAAwB,WAAxB,EAAqC,OAArC,EAA8C,KAA9C,EAAqD,WAArD,EAAkE;AAChE,MAAI,UAAU,EAAd;AACA,MAAI,gBAAgB,EAApB;;AAEA,MAAI,UAAU,WAAd;;AAEA,WAAS,WAAT,CAAsB,YAAtB,EAAoC;AAClC,YAAQ,IAAR,CAAa,YAAb;AACA,kBAAc,IAAd,CAAmB,OAAnB;AACD;;AAED,SAAO,MAAM,EAAN,CAAS,OAAT,CAAiB,OAAjB,CAAP,EAAkC;AAChC,cAAU,EAAV;AACA,oBAAgB,EAAhB;;AAEA,UAAM,aAAN,CAAoB,YAApB,CAAiC,OAAjC,EAA0C,WAA1C;;AAEA,QAAM,aAAa,iBAAiB,WAAjB,EAA8B,OAA9B,EAAuC,KAAvC,EAA8C,OAA9C,EAAuD,aAAvD,EAAsE,WAAtE,CAAnB;;AAEA,QAAI,WAAW,MAAX,IACC,CAAC,WAAW,MAAX,CAAkB,OAAlB,CAA0B,WAAW,MAAX,CAAkB,IAA5C,EAAkD,WADxD,EACqE;AACnE,aAAO,UAAP;AACD;;AAED,cAAU,MAAM,UAAN,CAAiB,OAAjB,CAAV;AACD;;AAED,SAAO,EAAP;AACD;;AAED,SAAS,OAAT,CAAkB,WAAlB,SAA4D;AAAA,MAA3B,MAA2B,SAA3B,MAA2B;AAAA,MAAnB,MAAmB,SAAnB,MAAmB;AAAA,MAAX,OAAW,SAAX,OAAW;;AAC1D,WAAS,UAAU,EAAnB;;AAEA,MAAI,YAAY,MAAZ,IAAsB,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,WAArD,EAAkE;AAChE,gBAAY,MAAZ,CAAmB,IAAnB,CAAwB,eAAxB,CAAwC,KAAxC,CAA8C,MAA9C,GAAuD,EAAvD;AACD;;AAED,cAAY,MAAZ,GAAqB,MAArB;AACA,cAAY,OAAZ,GAAsB,OAAtB;AACA,QAAM,UAAN,CAAiB,YAAY,QAA7B,EAAuC,MAAvC;;AAEA,MAAI,UAAU,OAAO,OAAP,CAAe,WAA7B,EAA0C;AACxC,QAAM,SAAS,SAAQ,QAAQ,OAAO,IAAf,EAAqB,SAArB,CAA+B,MAA/B,CAAR,GAAiD,EAAhE;AACA,gBAAY,MAAZ,CAAmB,IAAnB,CAAwB,eAAxB,CAAwC,KAAxC,CAA8C,MAA9C,GAAuD,MAAvD;AACD;;AAED,UAAQ,IAAR,CAAa,UAAb,EAAyB,EAAE,aAAa,WAAf,EAAzB;AACD;;AAED,YAAY,OAAZ,CAAoB,EAApB,CAAuB,MAAvB,EAA+B,iBAA2B;AAAA,MAAf,WAAe,SAAf,WAAe;;AACxD,MAAM,SAAS,YAAY,MAA3B;;AAEA,MAAI,UAAU,OAAO,OAAP,CAAe,WAA7B,EAA0C;AACxC,WAAO,IAAP,CAAY,eAAZ,CAA4B,KAA5B,CAAkC,MAAlC,GAA2C,EAA3C;AACD;AACF,CAND;;AAQA,SAAS,sBAAT,CAAiC,YAAjC,EAA+C,OAA/C,EAAwD,MAAxD,EAAgE;AAC9D,MAAM,UAAU,aAAa,OAA7B;AACA,MAAM,aAAa,QAAQ,OAAO,IAAf,EAAqB,GAAxC;AACA,MAAM,gBAAgB,QAAQ,OAAO,IAAf,EAAqB,aAA3C;AACA,MAAI,qBAAqB,CAAzB;AACA,MAAI,cAAc,CAAlB;AACA,MAAI,qBAAqB,CAAzB;;AAEA;AACA,MAAI,EAAE,cAAc,aAAd,IAA+B,UAAU,eAA3C,CAAJ,EAAiE;AAAE;AAAS;;AAE5E,wBAA0B,MAAM,YAAhC,eAA8C;AAAA;;AAAA,YAApB,MAAM,YAAc;AAAA,QAAnC,WAAmC;;AAC5C,QAAM,cAAc,YAAY,QAAZ,CAAqB,IAAzC;;AAEA,QAAI,CAAC,YAAY,WAAZ,EAAL,EAAgC;AAAE;AAAW;;AAE7C;;AAEA,QAAI,sBAAsB,UAAU,eAApC,EAAqD;AACnD,aAAO,KAAP;AACD;;AAED,QAAI,YAAY,MAAZ,KAAuB,YAA3B,EAAyC;AAAE;AAAW;;AAEtD,mBAAgB,gBAAgB,OAAO,IAAxB,GAA8B,CAA7C;;AAEA,QAAI,eAAe,UAAnB,EAA+B;AAC7B,aAAO,KAAP;AACD;;AAED,QAAI,YAAY,OAAZ,KAAwB,OAA5B,EAAqC;AACnC;;AAEA,UAAI,gBAAgB,OAAO,IAAvB,IAA+B,sBAAsB,aAAzD,EAAwE;AACtE,eAAO,KAAP;AACD;AACF;AACF;;AAED,SAAO,UAAU,eAAV,GAA4B,CAAnC;AACD;;AAED;;;;;;;;;;;AAWA,SAAS,eAAT,GAA2B,UAAU,QAAV,EAAoB;AAC7C,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,QAAhB,CAAJ,EAA+B;AAC7B,cAAU,eAAV,GAA4B,QAA5B;;AAEA,WAAO,QAAP;AACD;;AAED,SAAO,UAAU,eAAjB;AACD,CARD;;AAUA,aAAa,eAAb,CAA6B,IAA7B,CAAkC,aAAlC;AACA,aAAa,eAAb,CAA6B,IAA7B,CAAkC,eAAlC;AACA,aAAa,eAAb,CAA6B,IAA7B,CAAkC,YAAlC;AACA,aAAa,eAAb,CAA6B,IAA7B,CAAkC,WAAlC;;AAEA,eAAe,IAAf,CAAoB,aAApB,GAAoC,IAApC;AACA,eAAe,IAAf,CAAoB,WAApB,GAAkC,IAAlC;;AAEA,MAAM,MAAN,CAAa,eAAe,SAA5B,EAAuC,UAAU,QAAV,CAAmB,SAA1D;;AAEA,OAAO,OAAP,GAAiB,SAAjB;;;;;ACnPA,IAAM,YAAY,QAAQ,QAAR,CAAlB;AACA,IAAM,QAAY,QAAQ,UAAR,CAAlB;AACA,IAAM,KAAY,QAAQ,aAAR,CAAlB;;eAEuB,QAAQ,mBAAR,C;IAAf,U,YAAA,U;;AAER,UAAU,iBAAV,CAA4B,QAAQ,iBAAR,CAA5B;;AAEA,UAAU,OAAV,CAAkB,EAAlB,CAAqB,cAArB,EAAsC,gBAAgD;AAAA,MAApC,WAAoC,QAApC,WAAoC;AAAA,MAAvB,WAAuB,QAAvB,WAAuB;AAAA,MAAV,EAAU,QAAV,EAAU;AAAA,MAAN,EAAM,QAAN,EAAM;;AACpF,MAAI,YAAY,QAAZ,CAAqB,IAArB,KAA8B,MAAlC,EAA0C;AAAE;AAAS;;AAErD;AACA,MAAM,OAAO,KAAK,GAAL,CAAS,EAAT,CAAb;AACA,MAAM,OAAO,KAAK,GAAL,CAAS,EAAT,CAAb;AACA,MAAM,gBAAgB,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,IAAjD;AACA,MAAM,YAAY,cAAc,SAAhC;AACA,MAAM,cAAe,OAAO,IAAP,GAAc,GAAd,GAAoB,OAAO,IAAP,GAAc,GAAd,GAAoB,IAA7D;;AAEA,cAAY,QAAZ,CAAqB,IAArB,GAA4B,cAAc,QAAd,KAA2B,OAA3B,GACxB,YAAY,CAAZ,CADwB,CACT;AADS,IAExB,cAAc,QAFlB;;AAIA;AACA,MAAI,gBAAgB,IAAhB,IAAwB,cAAc,IAAtC,IAA8C,cAAc,WAAhE,EAA6E;AAC3E;AACA,gBAAY,QAAZ,CAAqB,IAArB,GAA4B,IAA5B;;AAEA;AACA,QAAI,UAAU,WAAd;;AAEA,QAAM,eAAe,SAAf,YAAe,CAAU,YAAV,EAAwB;AAC3C,UAAI,iBAAiB,YAAY,MAAjC,EAAyC;AAAE;AAAS;;AAEpD,UAAM,UAAU,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,IAA3C;;AAEA,UAAI,CAAC,QAAQ,WAAT,IACG,aAAa,eAAb,CAA6B,OAA7B,EAAsC,OAAtC,EAA+C,WAA/C,CADP,EACoE;;AAElE,YAAM,SAAS,aAAa,SAAb,CACb,YAAY,WADC,EACY,YAAY,SADxB,EACmC,WADnC,EACgD,OADhD,CAAf;;AAGA,YAAI,UACG,OAAO,IAAP,KAAgB,MADnB,IAEG,eAAe,WAAf,EAA4B,YAA5B,CAFH,IAGG,UAAU,cAAV,CAAyB,MAAzB,EAAiC,YAAjC,EAA+C,OAA/C,EAAwD,WAAxD,CAHP,EAG6E;;AAE3E,iBAAO,YAAP;AACD;AACF;AACF,KAnBD;;AAqBA;AACA,WAAO,GAAG,OAAH,CAAW,OAAX,CAAP,EAA4B;AAC1B,UAAM,eAAe,MAAM,aAAN,CAAoB,YAApB,CAAiC,OAAjC,EAA0C,YAA1C,CAArB;;AAEA,UAAI,YAAJ,EAAkB;AAChB,oBAAY,QAAZ,CAAqB,IAArB,GAA4B,MAA5B;AACA,oBAAY,MAAZ,GAAqB,YAArB;AACA,oBAAY,OAAZ,GAAsB,OAAtB;AACA;AACD;;AAED,gBAAU,WAAW,OAAX,CAAV;AACD;AACF;AACF,CAzDD;;AA2DA,SAAS,cAAT,CAAyB,SAAzB,EAAoC,YAApC,EAAkD;AAChD,MAAI,CAAC,YAAL,EAAmB;AAAE,WAAO,KAAP;AAAe;;AAEpC,MAAM,WAAW,aAAa,OAAb,CAAqB,IAArB,CAA0B,SAA3C;;AAEA,SAAQ,cAAc,IAAd,IAAsB,aAAa,IAAnC,IAA2C,aAAa,SAAhE;AACD;;;;;ACzED,QAAQ,QAAR,EAAkB,iBAAlB,CAAoC,QAAQ,oBAAR,CAApC;;;;;ACAA,IAAM,YAAc,QAAQ,QAAR,CAApB;AACA,IAAM,cAAc,QAAQ,gBAAR,CAApB;;AAEA,UAAU,QAAV,CAAmB,SAAnB,CAA6B,IAA7B,GAAoC,CAApC;AACA,UAAU,QAAV,CAAmB,SAAnB,CAA6B,KAA7B,GAAqC,CAArC;;AAEA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,KAAvB,EAA8B,UAAU,WAAV,EAAuB;AACnD,cAAY,kBAAZ,GAAiC,IAAjC;AACD,CAFD;;AAIA,UAAU,OAAV,CAAkB,EAAlB,CAAqB,UAArB,EAAiC,gBAA2B;AAAA,MAAf,WAAe,QAAf,WAAe;;AAC1D,MAAM,OAAO,gBAAgB,WAAhB,CAAb;;AAEA,MAAI,OAAO,CAAX,EAAc;AACZ,gBAAY,kBAAZ,GAAiC,WAAW,YAAM;AAChD,kBAAY,KAAZ,CAAkB,YAAY,QAA9B,EAAwC,YAAY,MAApD,EAA4D,YAAY,OAAxE;AACD,KAFgC,EAE9B,IAF8B,CAAjC;AAGD;AACF,CARD;;AAUA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,MAAvB,EAA+B,iBAAsC;AAAA,MAA1B,WAA0B,SAA1B,WAA0B;AAAA,MAAb,SAAa,SAAb,SAAa;;AACnE,MAAI,YAAY,eAAZ,IAA+B,CAAC,SAApC,EAA+C;AAC7C,iBAAa,YAAY,kBAAzB;AACD;AACF,CAJD;;AAMA;AACA,UAAU,OAAV,CAAkB,EAAlB,CAAqB,cAArB,EAAqC,iBAA2B;AAAA,MAAf,WAAe,SAAf,WAAe;;AAC9D,MAAM,OAAO,gBAAgB,WAAhB,CAAb;;AAEA,MAAI,OAAO,CAAX,EAAc;AACZ,gBAAY,QAAZ,CAAqB,IAArB,GAA4B,IAA5B;AACD;AACF,CAND;;AAQA,SAAS,eAAT,CAA0B,WAA1B,EAAuC;AACrC,MAAM,aAAa,YAAY,QAAZ,IAAwB,YAAY,QAAZ,CAAqB,IAAhE;;AAEA,MAAI,CAAC,UAAL,EAAiB;AAAE,WAAO,IAAP;AAAc;;AAEjC,MAAM,UAAU,YAAY,MAAZ,CAAmB,OAAnC;;AAEA,SAAO,QAAQ,UAAR,EAAoB,IAApB,IAA4B,QAAQ,UAAR,EAAoB,KAAvD;AACD;;AAED,OAAO,OAAP,GAAiB;AACf;AADe,CAAjB;;;;;AC7CA,QAAQ,QAAR,EAAkB,iBAAlB,CAAoC,QAAQ,mBAAR,CAApC;;;;;ACAA,OAAO,OAAP,GAAiB;AACf,QAAM;AACJ,YAAgB,IADZ;AAEJ,oBAAgB,MAFZ;AAGJ,iBAAgB;AAHZ,GADS;;AAOf,aAAW;AACT,YAAQ,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EADC;;AAGT,aAAS;AACP,eAAmB,KADZ;AAEP,kBAAmB,EAFZ,EAEmB;AAC1B,gBAAmB,GAHZ,EAGmB;AAC1B,gBAAmB,EAJZ,EAImB;AAC1B,mBAAmB,IALZ,EAKmB;AAC1B,yBAAmB,GANZ,CAMmB;AANnB;AAHA;AAPI,CAAjB;;;;;ACAA;;AAEA;AACA,QAAQ,WAAR;;AAEA;AACA,QAAQ,kBAAR;AACA,QAAQ,sBAAR;;AAEA;AACA,QAAQ,sBAAR;AACA,QAAQ,4BAAR;AACA,QAAQ,qCAAR;;AAEA;AACA,QAAQ,kBAAR;;AAEA;AACA,QAAQ,mBAAR;AACA,QAAQ,kBAAR;AACA,QAAQ,gBAAR;AACA,QAAQ,gBAAR;;AAEA;AACA,QAAQ,sBAAR;AACA,QAAQ,2BAAR;AACA,QAAQ,0BAAR;;AAEA;AACA,QAAQ,qBAAR;AACA,QAAQ,oBAAR;AACA,QAAQ,kBAAR;;AAEA;AACA,QAAQ,iCAAR;;AAEA;AACA,QAAQ,cAAR;;AAEA;AACA,OAAO,OAAP,GAAiB,QAAQ,YAAR,CAAjB;;;;;ACxCA,IAAM,gBAAiB,QAAQ,iBAAR,CAAvB;AACA,IAAM,cAAiB,QAAQ,eAAR,CAAvB;AACA,IAAM,YAAiB,QAAQ,kBAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,SAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,aAAR,CAAvB;;AAEA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,KAAvB,EAA8B,UAAU,WAAV,EAAuB;AACnD,cAAY,aAAZ,GAA4B;AAC1B,YAAa,KADa;AAE1B,eAAa,KAFa;AAG1B,iBAAa,KAHa;;AAK1B,gBAAY,IALc;AAM1B,cAAY,EANc;;AAQ1B,QAAI,CARsB,EAQnB,IAAI,CARe;AAS1B,QAAI,CATsB,EASnB,IAAI,CATe;;AAW1B,QAAI,CAXsB;AAY1B,SAAK,CAZqB,EAYlB,KAAK,CAZa;AAa1B,cAAU,CAbgB;;AAe1B,eAAW,CAfe;AAgB1B,eAAW,CAhBe;AAiB1B,OAAK;AAjBqB,GAA5B;;AAoBA,cAAY,iBAAZ,GAAkC;AAAA,WAAM,aAAe,KAAf,CAAqB,WAArB,CAAN;AAAA,GAAlC;AACA,cAAY,mBAAZ,GAAkC;AAAA,WAAM,eAAe,KAAf,CAAqB,WAArB,CAAN;AAAA,GAAlC;AACD,CAvBD;;AAyBA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,MAAvB,EAA+B,gBAAwD;AAAA,MAA5C,WAA4C,QAA5C,WAA4C;AAAA,MAA/B,KAA+B,QAA/B,KAA+B;AAAA,MAAxB,OAAwB,QAAxB,OAAwB;AAAA,MAAf,WAAe,QAAf,WAAe;;AACrF,MAAM,SAAS,YAAY,aAA3B;;AAEA;AACA,MAAI,OAAO,MAAX,EAAmB;AACjB,QAAI,UAAU,WAAd;;AAEA;AACA,WAAO,MAAM,EAAN,CAAS,OAAT,CAAiB,OAAjB,CAAP,EAAkC;;AAEhC;AACA,UAAI,YAAY,YAAY,OAA5B,EAAqC;AACnC;AACA,uBAAe,MAAf,CAAsB,OAAO,CAA7B;AACA,eAAO,MAAP,GAAgB,KAAhB;AACA,oBAAY,UAAZ,GAAyB,IAAzB;;AAEA;AACA,oBAAY,aAAZ,CAA0B,OAA1B;AACA,cAAM,SAAN,CAAgB,YAAY,SAA5B,EAAuC,YAAY,QAAnD;;AAEA;AACA,YAAM,YAAY,EAAE,wBAAF,EAAlB;AACA,oBAAY,OAAZ,CAAoB,IAApB,CAAyB,oBAAzB,EAA+C,SAA/C;AACA,oBAAY,OAAZ,CAAoB,IAApB,CAAyB,eAAzB,EAA+C,SAA/C;;AAEA;AACA,YAAM,cAAc,IAAI,aAAJ,CAAkB,WAAlB,EACkB,KADlB,EAEkB,YAAY,QAAZ,CAAqB,IAFvC,EAGkB,eAHlB,EAIkB,YAAY,OAJ9B,CAApB;;AAMA,oBAAY,MAAZ,CAAmB,IAAnB,CAAwB,WAAxB;AACA,oBAAY,SAAZ,GAAwB,WAAxB;AACA,kBAAU,aAAV,CAAwB,YAAY,gBAApC;;AAEA,cAAM,UAAN,CAAiB,YAAY,UAA7B,EAAyC,YAAY,SAArD;AACA;AACD;;AAED,gBAAU,MAAM,UAAN,CAAiB,OAAjB,CAAV;AACD;AACF;AACF,CA5CD;;AA8CA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,IAAvB,EAA6B,iBAAkC;AAAA,MAAtB,WAAsB,SAAtB,WAAsB;AAAA,MAAT,KAAS,SAAT,KAAS;;AAC7D,MAAM,SAAS,YAAY,aAA3B;;AAEA,MAAI,CAAC,YAAY,WAAZ,EAAD,IAA8B,OAAO,MAAzC,EAAiD;AAAE;AAAS;;AAE5D,MAAM,SAAS,YAAY,MAA3B;AACA,MAAM,UAAU,UAAU,OAAO,OAAjC;AACA,MAAM,iBAAiB,WAAW,YAAY,QAAZ,CAAqB,IAAhC,IAAwC,QAAQ,YAAY,QAAZ,CAAqB,IAA7B,EAAmC,OAAlG;;AAEA,MAAM,MAAM,IAAI,IAAJ,GAAW,OAAX,EAAZ;AACA,MAAM,WAAW,EAAjB;AACA,MAAM,OAAO,MAAM,MAAN,CAAa,EAAb,EAAiB,YAAY,SAAZ,CAAsB,IAAvC,CAAb;AACA,MAAM,eAAe,YAAY,YAAZ,CAAyB,MAAzB,CAAgC,KAArD;;AAEA,MAAI,YAAY,KAAhB;AACA,MAAI,uBAAJ;;AAEA;AACA,MAAM,kBAAmB,kBAAkB,eAAe,OAAjC,IACH,YAAY,QAAZ,CAAqB,IAArB,KAA8B,SAD3B,IAEH,UAAU,OAAO,UAFvC;;AAIA,MAAM,UAAW,mBACX,MAAM,YAAY,SAAZ,CAAsB,SAA7B,GAA0C,EAD9B,IAEZ,eAAe,eAAe,QAFlB,IAGZ,eAAe,eAAe,QAHnC;;AAKA,MAAM,cAAc;AAClB,4BADkB;AAElB,gBAAY,IAFM;AAGlB,sBAHkB;AAIlB,YAAQ,IAJU;AAKlB,oBAAgB;AALE,GAApB;;AAQA;AACA,MAAI,mBAAmB,CAAC,OAAxB,EAAiC;AAC/B,cAAU,aAAV,CAAwB,QAAxB;;AAEA,qBAAiB,UAAU,MAAV,CAAiB,WAAjB,CAAjB;;AAEA,QAAI,eAAe,UAAf,IAA6B,eAAe,MAAhD,EAAwD;AACtD,kBAAY,IAAZ;AACD;AACF;;AAED,MAAI,EAAE,WAAW,SAAb,CAAJ,EAA6B;AAAE;AAAS;;AAExC,QAAM,UAAN,CAAiB,OAAO,QAAxB,EAAkC,YAAY,SAA9C;;AAEA,cAAY,QAAZ,CAAqB,CAArB,IAA0B,OAAO,UAAP,GACxB,IAAI,aAAJ,CAAkB,WAAlB,EAA+B,KAA/B,EAAsC,YAAY,QAAZ,CAAqB,IAA3D,EAAiE,cAAjE,EAAiF,YAAY,OAA7F,CADF;;AAGA,SAAO,EAAP,GAAY,GAAZ;;AAEA,SAAO,MAAP,GAAgB,IAAhB;AACA,SAAO,WAAP,GAAqB,eAAe,WAApC;AACA,cAAY,UAAZ,GAAyB,MAAzB;;AAEA,SAAO,IAAP,CAAY,OAAO,UAAnB;;AAEA,MAAI,OAAJ,EAAa;AACX,WAAO,GAAP,GAAa,YAAY,YAAZ,CAAyB,MAAzB,CAAgC,EAA7C;AACA,WAAO,GAAP,GAAa,YAAY,YAAZ,CAAyB,MAAzB,CAAgC,EAA7C;AACA,WAAO,EAAP,GAAY,YAAZ;;AAEA,gBAAY,WAAZ,EAAyB,MAAzB;;AAEA,UAAM,MAAN,CAAa,IAAb,EAAmB,YAAY,SAAZ,CAAsB,IAAzC;;AAEA,SAAK,CAAL,IAAU,OAAO,EAAjB;AACA,SAAK,CAAL,IAAU,OAAO,EAAjB;;AAEA,cAAU,aAAV,CAAwB,QAAxB;;AAEA,qBAAiB,UAAU,MAAV,CAAiB,WAAjB,CAAjB;;AAEA,WAAO,UAAP,IAAqB,eAAe,EAApC;AACA,WAAO,UAAP,IAAqB,eAAe,EAApC;;AAEA,WAAO,CAAP,GAAW,eAAe,OAAf,CAAuB,YAAY,iBAAnC,CAAX;AACD,GApBD,MAqBK;AACH,WAAO,SAAP,GAAmB,IAAnB;AACA,WAAO,EAAP,GAAY,eAAe,EAA3B;AACA,WAAO,EAAP,GAAY,eAAe,EAA3B;;AAEA,WAAO,EAAP,GAAY,OAAO,EAAP,GAAY,CAAxB;;AAEA,WAAO,CAAP,GAAW,eAAe,OAAf,CAAuB,YAAY,mBAAnC,CAAX;AACD;AACF,CA3FD;;AA6FA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,aAAvB,EAAsC,iBAA2B;AAAA,MAAf,WAAe,SAAf,WAAe;;AAC/D,MAAM,SAAS,YAAY,aAA3B;;AAEA,MAAI,OAAO,MAAX,EAAmB;AACjB,mBAAe,MAAf,CAAsB,OAAO,CAA7B;AACA,WAAO,MAAP,GAAgB,KAAhB;AACA,gBAAY,UAAZ,GAAyB,IAAzB;AACD;AACF,CARD;;AAUA,SAAS,WAAT,CAAsB,WAAtB,EAAmC,MAAnC,EAA2C;AACzC,MAAM,iBAAiB,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,YAAY,QAAZ,CAAqB,IAAhD,EAAsD,OAA7E;AACA,MAAM,SAAS,eAAe,UAA9B;AACA,MAAM,aAAa,CAAC,KAAK,GAAL,CAAS,eAAe,QAAf,GAA0B,OAAO,EAA1C,CAAD,GAAiD,MAApE;;AAEA,SAAO,EAAP,GAAY,YAAY,SAAZ,CAAsB,KAAlC;AACA,SAAO,EAAP,GAAY,YAAY,SAAZ,CAAsB,KAAlC;AACA,SAAO,EAAP,GAAY,OAAO,UAAP,CAAkB,SAAlB,GAA8B,IAA1C;AACA,SAAO,EAAP,GAAY,OAAO,EAAP,GAAY,CAAxB;;AAEA,SAAO,UAAP,GAAoB,OAAO,EAAP,GAAY,CAAC,OAAO,GAAP,GAAa,UAAd,IAA4B,MAA5D;AACA,SAAO,UAAP,GAAoB,OAAO,EAAP,GAAY,CAAC,OAAO,GAAP,GAAa,UAAd,IAA4B,MAA5D;AACA,SAAO,EAAP,GAAY,UAAZ;;AAEA,SAAO,SAAP,GAAmB,SAAS,OAAO,EAAnC;AACA,SAAO,SAAP,GAAmB,IAAI,eAAe,QAAf,GAA0B,OAAO,EAAxD;AACD;;AAED,SAAS,YAAT,GAAyB;AACvB,sBAAoB,IAApB;AACA,QAAM,cAAN,CAAqB,KAAK,YAA1B,EAAwC,KAAK,UAA7C,EAAyD,KAAK,SAA9D;;AAEA,MAAM,SAAS,KAAK,aAApB;AACA,MAAM,UAAU,KAAK,MAAL,CAAY,OAAZ,CAAoB,KAAK,QAAL,CAAc,IAAlC,EAAwC,OAAxD;AACA,MAAM,SAAS,QAAQ,UAAvB;AACA,MAAM,IAAI,IAAI,IAAJ,GAAW,OAAX,KAAuB,IAAvB,GAA8B,OAAO,EAA/C;;AAEA,MAAI,IAAI,OAAO,EAAf,EAAmB;;AAEjB,QAAM,WAAY,IAAI,CAAC,KAAK,GAAL,CAAS,CAAC,MAAD,GAAU,CAAnB,IAAwB,OAAO,SAAhC,IAA6C,OAAO,SAA1E;;AAEA,QAAI,OAAO,UAAP,KAAsB,OAAO,EAA7B,IAAmC,OAAO,UAAP,KAAsB,OAAO,EAApE,EAAwE;AACtE,aAAO,EAAP,GAAY,OAAO,EAAP,GAAY,QAAxB;AACA,aAAO,EAAP,GAAY,OAAO,EAAP,GAAY,QAAxB;AACD,KAHD,MAIK;AACH,UAAM,YAAY,MAAM,sBAAN,CAA6B,CAA7B,EAAgC,CAAhC,EAC6B,OAAO,EADpC,EAE6B,OAAO,EAFpC,EAG6B,OAAO,UAHpC,EAI6B,OAAO,UAJpC,EAK6B,QAL7B,CAAlB;;AAOA,aAAO,EAAP,GAAY,UAAU,CAAtB;AACA,aAAO,EAAP,GAAY,UAAU,CAAtB;AACD;;AAED,SAAK,MAAL;;AAEA,WAAO,CAAP,GAAW,eAAe,OAAf,CAAuB,KAAK,iBAA5B,CAAX;AACD,GAvBD,MAwBK;AACH,WAAO,EAAP,GAAY,OAAO,UAAnB;AACA,WAAO,EAAP,GAAY,OAAO,UAAnB;;AAEA,SAAK,MAAL;AACA,SAAK,GAAL,CAAS,OAAO,UAAhB;AACA,WAAO,MAAP,GAAgB,KAAhB;AACA,SAAK,UAAL,GAAkB,IAAlB;AACD;;AAED,QAAM,UAAN,CAAiB,KAAK,UAAtB,EAAkC,KAAK,SAAvC;AACD;;AAED,SAAS,cAAT,GAA2B;AACzB,sBAAoB,IAApB;;AAEA,MAAM,SAAS,KAAK,aAApB;AACA,MAAM,IAAI,IAAI,IAAJ,GAAW,OAAX,KAAuB,OAAO,EAAxC;AACA,MAAM,WAAW,KAAK,MAAL,CAAY,OAAZ,CAAoB,KAAK,QAAL,CAAc,IAAlC,EAAwC,OAAxC,CAAgD,iBAAjE;;AAEA,MAAI,IAAI,QAAR,EAAkB;AAChB,WAAO,EAAP,GAAY,MAAM,WAAN,CAAkB,CAAlB,EAAqB,CAArB,EAAwB,OAAO,EAA/B,EAAmC,QAAnC,CAAZ;AACA,WAAO,EAAP,GAAY,MAAM,WAAN,CAAkB,CAAlB,EAAqB,CAArB,EAAwB,OAAO,EAA/B,EAAmC,QAAnC,CAAZ;;AAEA,SAAK,WAAL,CAAiB,OAAO,UAAxB,EAAoC,OAAO,UAA3C;;AAEA,WAAO,CAAP,GAAW,eAAe,OAAf,CAAuB,KAAK,mBAA5B,CAAX;AACD,GAPD,MAQK;AACH,WAAO,EAAP,GAAY,OAAO,EAAnB;AACA,WAAO,EAAP,GAAY,OAAO,EAAnB;;AAEA,SAAK,WAAL,CAAiB,OAAO,UAAxB,EAAoC,OAAO,UAA3C;AACA,SAAK,GAAL,CAAS,OAAO,UAAhB;;AAEA,WAAO,SAAP,GACE,OAAO,MAAP,GAAgB,KADlB;AAEA,SAAK,UAAL,GAAkB,IAAlB;AACD;AACF;;AAED,SAAS,mBAAT,CAA8B,WAA9B,EAA2C;AACzC,MAAM,SAAS,YAAY,aAA3B;;AAEA;AACA,MAAI,CAAC,OAAO,MAAZ,EAAoB;AAAE;AAAS;;AAE/B,MAAM,SAAW,OAAO,QAAP,CAAgB,IAAjC;AACA,MAAM,WAAW,OAAO,QAAP,CAAgB,MAAjC;;AAEA,QAAM,SAAN,CAAgB,YAAY,SAA5B,EAAuC,CAAE;AACvC,WAAS,OAAO,CAAP,GAAa,OAAO,EADU;AAEvC,WAAS,OAAO,CAAP,GAAa,OAAO,EAFU;AAGvC,aAAS,SAAS,CAAT,GAAa,OAAO,EAHU;AAIvC,aAAS,SAAS,CAAT,GAAa,OAAO;AAJU,GAAF,CAAvC;AAMD;;;;;AC/RD;;AAEA,IAAM,UAAe,QAAQ,iBAAR,CAArB;AACA,IAAM,SAAe,QAAQ,gBAAR,CAArB;AACA,IAAM,QAAe,QAAQ,SAAR,CAArB;AACA,IAAM,QAAe,QAAQ,SAAR,CAArB;AACA,IAAM,eAAe,QAAQ,gBAAR,CAArB;AACA,IAAM,cAAe,QAAQ,eAAR,CAArB;;AAEA,IAAM,eAAe,EAArB;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,SAAS,QAAT,CAAmB,OAAnB,EAA4B,OAA5B,EAAqC;AACnC,MAAI,eAAe,MAAM,aAAN,CAAoB,GAApB,CAAwB,OAAxB,EAAiC,OAAjC,CAAnB;;AAEA,MAAI,CAAC,YAAL,EAAmB;AACjB,mBAAe,IAAI,YAAJ,CAAiB,OAAjB,EAA0B,OAA1B,CAAf;AACA,iBAAa,MAAb,CAAoB,MAApB,GAA6B,YAA7B;AACD;;AAED,SAAO,YAAP;AACD;;AAED;;;;;;;;;;AAUA,SAAS,KAAT,GAAiB,UAAU,OAAV,EAAmB,OAAnB,EAA4B;AAC3C,SAAO,MAAM,aAAN,CAAoB,cAApB,CAAmC,OAAnC,EAA4C,WAAW,QAAQ,OAA/D,MAA4E,CAAC,CAApF;AACD,CAFD;;AAIA;;;;;;;;;;;AAWA,SAAS,EAAT,GAAc,UAAU,IAAV,EAAgB,QAAhB,EAA0B,OAA1B,EAAmC;AAC/C,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,IAAhB,KAAyB,KAAK,MAAL,CAAY,GAAZ,MAAqB,CAAC,CAAnD,EAAsD;AACpD,WAAO,KAAK,IAAL,GAAY,KAAZ,CAAkB,IAAlB,CAAP;AACD;;AAED,MAAI,MAAM,EAAN,CAAS,KAAT,CAAe,IAAf,CAAJ,EAA0B;AACxB,0BAAwB,IAAxB,eAA8B;AAAA;;AAAA,aAAN,IAAM;AAAA,UAAnB,SAAmB;;AAC5B,eAAS,EAAT,CAAY,SAAZ,EAAuB,QAAvB,EAAiC,OAAjC;AACD;;AAED,WAAO,QAAP;AACD;;AAED,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,IAAhB,CAAJ,EAA2B;AACzB,SAAK,IAAM,IAAX,IAAmB,IAAnB,EAAyB;AACvB,eAAS,EAAT,CAAY,IAAZ,EAAkB,KAAK,IAAL,CAAlB,EAA8B,QAA9B;AACD;;AAED,WAAO,QAAP;AACD;;AAED;AACA,MAAI,MAAM,QAAN,CAAe,aAAa,UAA5B,EAAwC,IAAxC,CAAJ,EAAmD;AACjD;AACA,QAAI,CAAC,aAAa,IAAb,CAAL,EAAyB;AACvB,mBAAa,IAAb,IAAqB,CAAC,QAAD,CAArB;AACD,KAFD,MAGK;AACH,mBAAa,IAAb,EAAmB,IAAnB,CAAwB,QAAxB;AACD;AACF;AACD;AATA,OAUK;AACH,aAAO,GAAP,CAAW,MAAM,QAAjB,EAA2B,IAA3B,EAAiC,QAAjC,EAA2C,EAAE,gBAAF,EAA3C;AACD;;AAED,SAAO,QAAP;AACD,CArCD;;AAuCA;;;;;;;;;;;;AAYA,SAAS,GAAT,GAAe,UAAU,IAAV,EAAgB,QAAhB,EAA0B,OAA1B,EAAmC;AAChD,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,IAAhB,KAAyB,KAAK,MAAL,CAAY,GAAZ,MAAqB,CAAC,CAAnD,EAAsD;AACpD,WAAO,KAAK,IAAL,GAAY,KAAZ,CAAkB,IAAlB,CAAP;AACD;;AAED,MAAI,MAAM,EAAN,CAAS,KAAT,CAAe,IAAf,CAAJ,EAA0B;AACxB,4BAAwB,IAAxB,gBAA8B;AAAA;;AAAA,cAAN,IAAM;AAAA,UAAnB,SAAmB;;AAC5B,eAAS,GAAT,CAAa,SAAb,EAAwB,QAAxB,EAAkC,OAAlC;AACD;;AAED,WAAO,QAAP;AACD;;AAED,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,IAAhB,CAAJ,EAA2B;AACzB,SAAK,IAAM,IAAX,IAAmB,IAAnB,EAAyB;AACvB,eAAS,GAAT,CAAa,IAAb,EAAmB,KAAK,IAAL,CAAnB,EAA+B,QAA/B;AACD;;AAED,WAAO,QAAP;AACD;;AAED,MAAI,CAAC,MAAM,QAAN,CAAe,aAAa,UAA5B,EAAwC,IAAxC,CAAL,EAAoD;AAClD,WAAO,MAAP,CAAc,MAAM,QAApB,EAA8B,IAA9B,EAAoC,QAApC,EAA8C,OAA9C;AACD,GAFD,MAGK;AACH,QAAI,cAAJ;;AAEA,QAAI,QAAQ,YAAR,IACG,CAAC,QAAQ,aAAa,IAAb,EAAmB,OAAnB,CAA2B,QAA3B,CAAT,MAAmD,CAAC,CAD3D,EAC8D;AAC5D,mBAAa,IAAb,EAAmB,MAAnB,CAA0B,KAA1B,EAAiC,CAAjC;AACD;AACF;;AAED,SAAO,QAAP;AACD,CAlCD;;AAoCA;;;;;;;;AAQA,SAAS,KAAT,GAAiB,YAAY;AAC3B,SAAO,KAAP;AACD,CAFD;;AAIA;AACA,SAAS,iBAAT,GAA8B,MAAM,cAApC;AACA,SAAS,YAAT,GAA8B,MAAM,SAApC;AACA,SAAS,gBAAT,GAA8B,MAAM,aAApC;AACA,SAAS,aAAT,GAA8B,MAAM,UAApC;;AAEA,SAAS,cAAT,GAAgC,MAAM,cAAtC;AACA,SAAS,oBAAT,GAAgC,MAAM,oBAAtC;AACA,SAAS,eAAT,GAAgC,MAAM,eAAtC;AACA,SAAS,OAAT,GAAgC,MAAM,OAAtC;;AAEA;;;;;AAKA,SAAS,aAAT,GAAyB,YAAY;AACnC,SAAO,QAAQ,aAAf;AACD,CAFD;;AAIA;;;;;AAKA,SAAS,oBAAT,GAAgC,YAAY;AAC1C,SAAO,QAAQ,oBAAf;AACD,CAFD;;AAIA;;;;;;;;AAQA,SAAS,IAAT,GAAgB,UAAU,KAAV,EAAiB;AAC/B,OAAK,IAAI,IAAI,MAAM,YAAN,CAAmB,MAAnB,GAA4B,CAAzC,EAA4C,KAAK,CAAjD,EAAoD,GAApD,EAAyD;AACvD,UAAM,YAAN,CAAmB,CAAnB,EAAsB,IAAtB,CAA2B,KAA3B;AACD;;AAED,SAAO,QAAP;AACD,CAND;;AAQA;;;;;;;;;AASA,SAAS,oBAAT,GAAgC,UAAU,QAAV,EAAoB;AAClD,MAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,QAAhB,CAAJ,EAA+B;AAC7B,gBAAY,oBAAZ,GAAmC,QAAnC;;AAEA,WAAO,QAAP;AACD;;AAED,SAAO,YAAY,oBAAnB;AACD,CARD;;AAUA,SAAS,WAAT,GAA0B,MAAM,WAAhC;AACA,SAAS,cAAT,GAA0B,MAAM,cAAhC;;AAEA,MAAM,QAAN,GAAiB,QAAjB;;AAEA,OAAO,OAAP,GAAiB,QAAjB;;;;;AChPA,IAAM,eAAe,QAAQ,gBAAR,CAArB;AACA,IAAM,cAAe,QAAQ,eAAR,CAArB;AACA,IAAM,QAAe,QAAQ,SAAR,CAArB;AACA,IAAM,KAAe,QAAQ,YAAR,CAArB;AACA,IAAM,SAAe,QAAQ,gBAAR,CAArB;AACA,IAAM,UAAe,QAAQ,iBAAR,CAArB;;eAE0C,QAAQ,kBAAR,C;IAAlC,Y,YAAA,Y;IAAc,e,YAAA,e;;AAEtB;;;;;;;;;;;;AAUA,aAAa,SAAb,CAAuB,cAAvB,GAAwC,UAAU,QAAV,EAAoB;AAC1D,MAAI,wBAAwB,IAAxB,CAA6B,QAA7B,CAAJ,EAA4C;AAC1C,SAAK,OAAL,CAAa,cAAb,GAA8B,QAA9B;AACA,WAAO,IAAP;AACD;;AAED,MAAI,GAAG,IAAH,CAAQ,QAAR,CAAJ,EAAuB;AACrB,SAAK,OAAL,CAAa,cAAb,GAA8B,WAAU,QAAV,GAAqB,OAAnD;AACA,WAAO,IAAP;AACD;;AAED,SAAO,KAAK,OAAL,CAAa,cAApB;AACD,CAZD;;AAcA,aAAa,SAAb,CAAuB,sBAAvB,GAAgD,UAAU,KAAV,EAAiB;AAC/D,MAAM,UAAU,KAAK,OAAL,CAAa,cAA7B;;AAEA,MAAI,YAAY,OAAhB,EAAyB;AAAE;AAAS;;AAEpC,MAAI,YAAY,QAAhB,EAA0B;AACxB,UAAM,cAAN;AACA;AACD;;AAED;;AAEA;AACA;AACA,MAAI,OAAO,eAAP,IACC,sBAAsB,IAAtB,CAA2B,MAAM,IAAjC,CADD,IAEC,CAAC,QAAQ,KAFd,EAEqB;AACnB;AACD;;AAED;AACA,MAAI,uCAAuC,IAAvC,CAA4C,MAAM,IAAlD,CAAJ,EAA6D;AAC3D;AACD;;AAED;AACA,MAAI,GAAG,OAAH,CAAW,MAAM,MAAjB,KACG,gBAAgB,MAAM,MAAtB,EAA8B,uEAA9B,CADP,EAC+G;AAC7G;AACD;;AAED,QAAM,cAAN;AACD,CAhCD;;AAkCA,SAAS,kBAAT,OAAqD;AAAA,MAAtB,WAAsB,QAAtB,WAAsB;AAAA,MAAT,KAAS,QAAT,KAAS;;AACnD,MAAI,YAAY,MAAhB,EAAwB;AACtB,gBAAY,MAAZ,CAAmB,sBAAnB,CAA0C,KAA1C;AACD;AACF;;WAEyB,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,QAAvB,C;AAA1B,yCAA4D;AAAvD,MAAM,sBAAN;AACH,cAAY,OAAZ,CAAoB,EAApB,CAAuB,WAAvB,EAAoC,kBAApC;AACD;;AAED;AACA,YAAY,SAAZ,CAAsB,SAAtB,GAAkC,SAAS,iBAAT,CAA4B,KAA5B,EAAmC;AACnE,0BAA0B,MAAM,YAAhC,gBAA8C;AAAA;;AAAA,YAApB,MAAM,YAAc;AAAA,QAAnC,WAAmC;;;AAE5C,QAAI,YAAY,OAAZ,KACI,YAAY,OAAZ,KAAwB,MAAM,MAA9B,IACG,aAAa,YAAY,OAAzB,EAAkC,MAAM,MAAxC,CAFP,CAAJ,EAE6D;;AAE3D,kBAAY,MAAZ,CAAmB,sBAAnB,CAA0C,KAA1C;AACA;AACD;AACF;AACF,CAXD;;;;;AC9EA,IAAM,gBAAgB,QAAQ,kBAAR,CAAtB;AACA,IAAM,cAAgB,QAAQ,gBAAR,CAAtB;AACA,IAAM,SAAgB,QAAQ,iBAAR,CAAtB;;AAEA,IAAM,YAAY;AAChB,SAAO,EADS;;AAGhB,cAAY,oBAAU,GAAV,EAAe;AAAA,QACjB,WADiB,GACiB,GADjB,CACjB,WADiB;AAAA,QACQ,IADR,GACiB,GADjB,CACJ,UADI;AAAA,QAEjB,MAFiB,GAEgB,WAFhB,CAEjB,MAFiB;AAAA,QAET,OAFS,GAEgB,WAFhB,CAET,OAFS;AAAA,QAEA,WAFA,GAEgB,WAFhB,CAEA,WAFA;;AAGzB,QAAM,OAAO,OAAO,OAAP,CAAe,OAAf,CAAb;;AAEA,QAAI,IAAJ,EAAU;AACR,kBAAY,IAAZ,GAAmB,KAAK,CAAL,GAAS,KAAK,IAAjC;AACA,kBAAY,GAAZ,GAAmB,KAAK,CAAL,GAAS,KAAK,GAAjC;;AAEA,kBAAY,KAAZ,GAAqB,KAAK,KAAL,GAAc,KAAK,CAAxC;AACA,kBAAY,MAAZ,GAAqB,KAAK,MAAL,GAAc,KAAK,CAAxC;;AAEA,UAAI,EAAE,WAAY,IAAd,CAAJ,EAAyB;AAAE,aAAK,KAAL,GAAc,KAAK,KAAL,GAAc,KAAK,IAAjC;AAAwC;AACnE,UAAI,EAAE,YAAY,IAAd,CAAJ,EAAyB;AAAE,aAAK,MAAL,GAAc,KAAK,MAAL,GAAc,KAAK,GAAjC;AAAwC;AACpE,KATD,MAUK;AACH,kBAAY,IAAZ,GAAmB,YAAY,GAAZ,GAAkB,YAAY,KAAZ,GAAoB,YAAY,MAAZ,GAAqB,CAA9E;AACD;;AAED,QAAI,IAAJ,GAAW,IAAX;AACA,QAAI,YAAJ,GAAmB,MAAnB;AACA,QAAI,OAAJ,GAAc,OAAd;;AAEA,0BAA2B,UAAU,KAArC,eAA4C;AAAA;;AAAA,aAAjB,UAAU,KAAO;AAAA,UAAjC,YAAiC;;AAC1C,UAAI,OAAJ,GAAc,OAAO,OAAP,CAAe,YAAY,QAAZ,CAAqB,IAApC,EAA0C,YAA1C,CAAd;;AAEA,UAAI,CAAC,IAAI,OAAT,EAAkB;AAChB;AACD;;AAED,kBAAY,eAAZ,CAA4B,YAA5B,IAA4C,UAAU,YAAV,EAAwB,SAAxB,CAAkC,GAAlC,CAA5C;AACD;AACF,GAnCe;;AAqChB,UAAQ,gBAAU,GAAV,EAAe;AAAA,QACb,WADa,GACqC,GADrC,CACb,WADa;AAAA,QACA,QADA,GACqC,GADrC,CACA,QADA;AAAA,QACU,MADV,GACqC,GADrC,CACU,MADV;AAAA,QACkB,cADlB,GACqC,GADrC,CACkB,cADlB;;AAErB,QAAM,SAAS;AACb,UAAI,CADS;AAEb,UAAI,CAFS;AAGb,eAAS,KAHI;AAIb,cAAQ,KAJK;AAKb,kBAAY;AALC,KAAf;;AAQA,QAAI,cAAJ,GAAqB,OAAO,EAAP,EAAW,IAAI,UAAf,CAArB;;AAEA,4BAA2B,UAAU,KAArC,gBAA4C;AAAA;;AAAA,cAAjB,UAAU,KAAO;AAAA,UAAjC,YAAiC;;AAC1C,UAAM,WAAW,UAAU,YAAV,CAAjB;AACA,UAAM,UAAU,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,YAAY,QAAZ,CAAqB,IAAhD,EAAsD,YAAtD,CAAhB;;AAEA,UAAI,CAAC,SAAS,OAAT,EAAkB,MAAlB,EAA0B,cAA1B,CAAL,EAAgD;AAAE;AAAW;;AAE7D,UAAI,MAAJ,GAAa,IAAI,MAAJ,GAAa,SAAS,YAAT,CAA1B;AACA,UAAI,OAAJ,GAAc,OAAd;AACA,UAAI,MAAJ,GAAa,IAAI,WAAJ,CAAgB,eAAhB,CAAgC,YAAhC,CAAb;;AAEA,eAAS,GAAT,CAAa,GAAb;;AAEA,UAAI,IAAI,MAAJ,CAAW,MAAf,EAAuB;AACrB,YAAI,cAAJ,CAAmB,CAAnB,IAAwB,IAAI,MAAJ,CAAW,EAAnC;AACA,YAAI,cAAJ,CAAmB,CAAnB,IAAwB,IAAI,MAAJ,CAAW,EAAnC;;AAEA,eAAO,EAAP,IAAa,IAAI,MAAJ,CAAW,EAAxB;AACA,eAAO,EAAP,IAAa,IAAI,MAAJ,CAAW,EAAxB;;AAEA,eAAO,MAAP,GAAgB,IAAhB;AACD;AACF;;AAED;AACA;AACA;AACA;AACA,WAAO,UAAP,GAAoB,CAAC,IAAI,MAAL,IAAe,CAAC,OAAO,MAAvB,IAAiC,IAAI,MAAJ,CAAW,OAAhE;;AAEA,WAAO,MAAP;AACD,GA/Ee;;AAiFhB,iBAAe,uBAAU,QAAV,EAAoB;AACjC,4BAA2B,UAAU,KAArC,gBAA4C;AAAA;;AAAA,cAAjB,UAAU,KAAO;AAAA,UAAjC,YAAiC;;AAC1C,UAAM,SAAS,SAAS,YAAT,KAA0B,EAAzC;;AAEA,aAAO,EAAP,GAAY,OAAO,EAAP,GAAY,CAAxB;AACA,aAAO,SAAP,GAAmB,OAAO,SAAP,GAAmB,GAAtC;AACA,aAAO,MAAP,GAAgB,KAAhB;AACA,aAAO,OAAP,GAAiB,IAAjB;;AAEA,eAAS,YAAT,IAAyB,MAAzB;AACD;;AAED,WAAO,QAAP;AACD,GA9Fe;;AAgGhB,SAAO,sBAA2B,UAA3B,EAAuC;AAAA,QAA3B,WAA2B,SAA3B,WAA2B;;AAC5C,QAAM,MAAM;AACV,8BADU;AAEV,kBAAY,CAAC,eAAe,eAAf,GACA,YAAY,SADZ,GACwB,YAAY,WADrC,EACkD,IAHpD;AAIV,mBAAa,YAAY,WAJf;AAKV,gBAAU,YAAY,gBALZ;AAMV,cAAQ,KANE;AAOV,sBAAgB;AAPN,KAAZ;;AAUA,cAAU,UAAV,CAAqB,GAArB;AACA,cAAU,aAAV,CAAwB,IAAI,QAA5B;;AAEA,QAAI,UAAJ,GAAiB,OAAO,EAAP,EAAW,YAAY,WAAZ,CAAwB,IAAnC,CAAjB;AACA,gBAAY,cAAZ,GAA6B,UAAU,MAAV,CAAiB,GAAjB,CAA7B;AACD,GAhHe;;AAkHhB,cAAY,2BAA0D;AAAA,QAA9C,WAA8C,SAA9C,WAA8C;AAAA,QAAjC,MAAiC,SAAjC,MAAiC;AAAA,QAAzB,qBAAyB,SAAzB,qBAAyB;;AACpE,QAAM,iBAAiB,UAAU,MAAV,CAAiB;AACtC,8BADsC;AAEtC,oBAFsC;AAGtC,kBAAY,YAAY,SAAZ,CAAsB,IAHI;AAItC,gBAAU,YAAY,gBAJgB;AAKtC,sBAAgB;AALsB,KAAjB,CAAvB;;AAQA;AACA;AACA,QAAI,CAAC,eAAe,UAAhB,IAA8B,qBAAlC,EAAyD;AACvD,kBAAY,aAAZ,GAA4B,IAA5B;AACD;;AAED,gBAAY,cAAZ,GAA6B,cAA7B;AACD,GAlIe;;AAoIhB,OAAK,oBAAkC;AAAA,QAAtB,WAAsB,SAAtB,WAAsB;AAAA,QAAT,KAAS,SAAT,KAAS;;AACrC,4BAA2B,UAAU,KAArC,gBAA4C;AAAA;;AAAA,cAAjB,UAAU,KAAO;AAAA,UAAjC,YAAiC;;AAC1C,UAAM,UAAU,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,YAAY,QAAZ,CAAqB,IAAhD,EAAsD,YAAtD,CAAhB;;AAEA;AACA,UAAI,SAAS,OAAT,EAAkB,IAAlB,EAAwB,IAAxB,CAAJ,EAAmC;AACjC;AACA,oBAAY,MAAZ,CAAmB,EAAE,YAAF,EAAS,QAAQ,IAAjB,EAAnB;AACA;AACD;AACF;AACF,GA/Ie;;AAiJhB,SAAO,eAAU,GAAV,EAAe;AAAA,QACZ,MADY,GACY,GADZ,CACZ,MADY;AAAA,QACJ,WADI,GACY,GADZ,CACJ,WADI;;AAEpB,QAAM,cAAc,OAAO,EAAP,EAAW,GAAX,CAApB;;AAEA,SAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,UAAU,KAAV,CAAgB,MAApC,EAA4C,GAA5C,EAAiD;AAC/C,UAAM,eAAe,UAAU,KAAV,CAAgB,CAAhB,CAArB;AACA,kBAAY,OAAZ,GAAsB,YAAY,MAAZ,CAAmB,OAAnB,CAA2B,YAAY,QAAZ,CAAqB,IAAhD,EAAsD,YAAtD,CAAtB;;AAEA,UAAI,CAAC,YAAY,OAAjB,EAA0B;AACxB;AACD;;AAED,UAAM,WAAW,UAAU,YAAV,CAAjB;;AAEA,kBAAY,MAAZ,GAAqB,YAAY,gBAAZ,CAA6B,YAA7B,CAArB;;AAEA,aAAO,YAAP,IAAuB,SAAS,YAAT,CAAsB,WAAtB,CAAvB;AACD;AACF;AAnKe,CAAlB;;AAsKA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,KAAvB,EAA8B,UAAU,WAAV,EAAuB;AACnD,cAAY,WAAZ,GAA+B,EAAE,MAAM,CAAR,EAAW,OAAO,CAAlB,EAAqB,KAAK,CAA1B,EAA6B,QAAQ,CAArC,EAA/B;AACA,cAAY,eAAZ,GAA+B,EAA/B;AACA,cAAY,gBAAZ,GAA+B,UAAU,aAAV,CAAwB,EAAxB,CAA/B;AACA,cAAY,cAAZ,GAA+B,IAA/B;AACD,CALD;;AAOA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,cAAvB,EAAwC,UAAU,KAAlD;AACA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,eAAvB,EAAwC,UAAU,KAAlD;AACA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,oBAAvB,EAA6C,UAAU,UAAvD;AACA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,YAAvB,EAAqC,UAAU,GAA/C;;AAEA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,QAAzB,EAAmC,UAAU,KAA7C;;AAEA,SAAS,QAAT,CAAmB,OAAnB,EAA4B,MAA5B,EAAoC,cAApC,EAAoD;AAClD,SAAQ,WAAW,QAAQ,OAAnB,KACI,UAAU,CAAC,QAAQ,OADvB,MAEI,CAAC,cAAD,IAAmB,QAAQ,OAF/B,CAAR;AAGD;;AAED,OAAO,OAAP,GAAiB,SAAjB;;;;;AC9LA,IAAM,YAAiB,QAAQ,QAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,mBAAR,CAAvB;;AAEA,IAAM,WAAW;AACf,YAAU;AACR,aAAa,KADL;AAER,aAAa,KAFL;AAGR,iBAAa,IAHL;AAIR,iBAAa;AAJL,GADK;;AAQf,aAAW,yBAA0C;AAAA,QAA9B,IAA8B,QAA9B,IAA8B;AAAA,QAAxB,WAAwB,QAAxB,WAAwB;AAAA,QAAX,OAAW,QAAX,OAAW;;AACnD,QAAM,cAAc,WAAW,QAAQ,WAAvC;AACA,QAAM,SAAS,EAAf;;AAEA,QAAI,QAAQ,WAAZ,EAAyB;AACvB,aAAO,IAAP,GAAc,YAAY,IAAZ,GAAoB,KAAK,KAAL,GAAc,YAAY,IAA5D;AACA,aAAO,GAAP,GAAc,YAAY,GAAZ,GAAoB,KAAK,MAAL,GAAc,YAAY,GAA5D;;AAEA,aAAO,KAAP,GAAgB,YAAY,KAAZ,GAAsB,KAAK,KAAL,IAAe,IAAI,YAAY,KAA/B,CAAtC;AACA,aAAO,MAAP,GAAgB,YAAY,MAAZ,GAAsB,KAAK,MAAL,IAAe,IAAI,YAAY,MAA/B,CAAtC;AACD,KAND,MAOK;AACH,aAAO,IAAP,GAAc,OAAO,GAAP,GAAa,OAAO,KAAP,GAAe,OAAO,MAAP,GAAgB,CAA1D;AACD;;AAED,WAAO,MAAP;AACD,GAxBc;;AA0Bf,OAAK,oBAA4D;AAAA,QAAhD,cAAgD,SAAhD,cAAgD;AAAA,QAAhC,WAAgC,SAAhC,WAAgC;AAAA,QAAnB,MAAmB,SAAnB,MAAmB;AAAA,QAAX,OAAW,SAAX,OAAW;;AAC/D,QAAI,CAAC,OAAL,EAAc;AAAE,aAAO,MAAP;AAAgB;;AAEhC,QAAM,OAAO,OAAO,WAAP,GACT,EAAE,GAAG,OAAO,CAAZ,EAAe,GAAG,OAAO,CAAzB,EADS,GAET,MAAM,MAAN,CAAa,EAAb,EAAiB,cAAjB,CAFJ;;AAIA,QAAM,cAAc,mBAAmB,QAAQ,WAA3B,EAAwC,WAAxC,EAAqD,IAArD,CAApB;;AAEA,QAAI,CAAC,WAAL,EAAkB;AAAE,aAAO,MAAP;AAAgB;;AAEpC,WAAO,EAAP,GAAY,CAAZ;AACA,WAAO,EAAP,GAAY,CAAZ;AACA,WAAO,MAAP,GAAgB,KAAhB;;AAEA,QAAM,OAAO,WAAb;AACA,QAAI,YAAY,KAAK,CAArB;AACA,QAAI,YAAY,KAAK,CAArB;;AAEA,QAAM,SAAS,YAAY,eAAZ,CAA4B,QAA3C;;AAEA;AACA;AACA;AACA,QAAI,OAAO,WAAP,IAAsB,OAAO,WAAjC,EAA8C;AAC5C,kBAAY,KAAK,GAAL,CAAS,KAAK,GAAL,CAAS,KAAK,CAAL,GAAS,KAAK,KAAd,GAAuB,OAAO,KAAvC,EAA+C,KAAK,CAApD,CAAT,EAAiE,KAAK,CAAL,GAAS,OAAO,IAAjF,CAAZ;AACA,kBAAY,KAAK,GAAL,CAAS,KAAK,GAAL,CAAS,KAAK,CAAL,GAAS,KAAK,MAAd,GAAuB,OAAO,MAAvC,EAA+C,KAAK,CAApD,CAAT,EAAiE,KAAK,CAAL,GAAS,OAAO,GAAjF,CAAZ;AACD,KAHD,MAIK;AACH,kBAAY,KAAK,GAAL,CAAS,KAAK,GAAL,CAAS,KAAK,KAAL,GAAc,OAAO,KAA9B,EAAsC,KAAK,CAA3C,CAAT,EAAwD,KAAK,IAAL,GAAY,OAAO,IAA3E,CAAZ;AACA,kBAAY,KAAK,GAAL,CAAS,KAAK,GAAL,CAAS,KAAK,MAAL,GAAc,OAAO,MAA9B,EAAsC,KAAK,CAA3C,CAAT,EAAwD,KAAK,GAAL,GAAY,OAAO,GAA3E,CAAZ;AACD;;AAED,WAAO,EAAP,GAAY,YAAY,KAAK,CAA7B;AACA,WAAO,EAAP,GAAY,YAAY,KAAK,CAA7B;;AAEA,WAAO,OAAP,GAAiB,OAAO,SAAP,KAAqB,SAArB,IAAkC,OAAO,SAAP,KAAqB,SAAxE;AACA,WAAO,MAAP,GAAgB,CAAC,EAAE,OAAO,EAAP,IAAa,OAAO,EAAtB,CAAjB;;AAEA,WAAO,SAAP,GAAmB,SAAnB;AACA,WAAO,SAAP,GAAmB,SAAnB;AACD,GAnEc;;AAqEf,gBAAc,6BAAoD;AAAA,QAAxC,IAAwC,SAAxC,IAAwC;AAAA,QAAlC,MAAkC,SAAlC,MAAkC;AAAA,QAA1B,MAA0B,SAA1B,MAA0B;AAAA,QAAlB,KAAkB,SAAlB,KAAkB;AAAA,QAAX,OAAW,SAAX,OAAW;;AAChE,QAAM,cAAc,WAAW,QAAQ,WAAvC;;AAEA,QAAI,WAAW,QAAQ,OAAnB,IACG,EAAE,UAAU,OAAV,IAAqB,WAArB,IAAoC,OAAO,MAA7C,CADP,EAC6D;;AAE3D,UAAI,OAAO,MAAX,EAAmB;AACjB,aAAK,CAAL,IAAU,OAAO,EAAjB;AACA,aAAK,CAAL,IAAU,OAAO,EAAjB;AACA,eAAO,CAAP,IAAY,OAAO,EAAnB;AACA,eAAO,CAAP,IAAY,OAAO,EAAnB;;AAEA,eAAO;AACL,cAAI,OAAO,EADN;AAEL,cAAI,OAAO;AAFN,SAAP;AAID;AACF;AACF,GAvFc;;AAyFf;AAzFe,CAAjB;;AA4FA,SAAS,kBAAT,CAA6B,KAA7B,EAAoC,WAApC,EAAiD,IAAjD,EAAuD;AACrD,MAAI,MAAM,EAAN,CAAS,QAAT,CAAkB,KAAlB,CAAJ,EAA8B;AAC5B,WAAO,MAAM,eAAN,CAAsB,KAAtB,EAA6B,YAAY,MAAzC,EAAiD,YAAY,OAA7D,EAAsE,CAAC,KAAK,CAAN,EAAS,KAAK,CAAd,EAAiB,WAAjB,CAAtE,CAAP;AACD,GAFD,MAEO;AACL,WAAO,MAAM,eAAN,CAAsB,KAAtB,EAA6B,YAAY,MAAzC,EAAiD,YAAY,OAA7D,CAAP;AACD;AACF;;AAED,UAAU,QAAV,GAAqB,QAArB;AACA,UAAU,KAAV,CAAgB,IAAhB,CAAqB,UAArB;;AAEA,eAAe,SAAf,CAAyB,QAAzB,GAAoC,SAAS,QAA7C;;AAEA,OAAO,OAAP,GAAiB,QAAjB;;;;;AC7GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAM,YAAiB,QAAQ,QAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA,IAAM,YAAiB,QAAQ,eAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,mBAAR,CAAvB;AACA,IAAM,SAAiB,QAAQ,mBAAR,CAAvB;;eAE+B,QAAQ,YAAR,C;IAAvB,kB,YAAA,kB;;AAER,IAAM,UAAU,EAAE,KAAK,CAAC,QAAR,EAAkB,MAAM,CAAC,QAAzB,EAAmC,QAAQ,CAAC,QAA5C,EAAsD,OAAO,CAAC,QAA9D,EAAhB;AACA,IAAM,UAAU,EAAE,KAAK,CAAC,QAAR,EAAkB,MAAM,CAAC,QAAzB,EAAmC,QAAQ,CAAC,QAA5C,EAAsD,OAAO,CAAC,QAA9D,EAAhB;;AAEA,IAAM,gBAAgB;AACpB,YAAU;AACR,aAAS,KADD;AAER,aAAS,KAFD;AAGR,SAAK,IAHG;AAIR,SAAK,IAJG;AAKR,YAAQ;AALA,GADU;;AASpB,aAAW,yBAAiD;AAAA,QAArC,WAAqC,QAArC,WAAqC;AAAA,QAAxB,WAAwB,QAAxB,WAAwB;AAAA,QAAX,OAAW,QAAX,OAAW;;AAC1D,QAAI,CAAC,OAAL,EAAc;AACZ,aAAO,MAAM,MAAN,CAAa,EAAb,EAAiB,WAAjB,CAAP;AACD;;AAED,QAAM,SAAS,mBAAmB,QAAQ,MAA3B,EAAmC,WAAnC,EAAgD,YAAY,WAAZ,CAAwB,IAAxE,CAAf;;AAEA,QAAI,MAAJ,EAAY;AACV,aAAO;AACL,aAAQ,YAAY,GAAZ,GAAqB,OAAO,CAD/B;AAEL,cAAQ,YAAY,IAAZ,GAAqB,OAAO,CAF/B;AAGL,gBAAQ,YAAY,MAAZ,GAAqB,OAAO,CAH/B;AAIL,eAAQ,YAAY,KAAZ,GAAqB,OAAO;AAJ/B,OAAP;AAMD;;AAED,WAAO,WAAP;AACD,GA1BmB;;AA4BpB,OAAK,oBAAoE;AAAA,QAAxD,cAAwD,SAAxD,cAAwD;AAAA,QAAxC,WAAwC,SAAxC,WAAwC;AAAA,QAA3B,MAA2B,SAA3B,MAA2B;AAAA,QAAnB,MAAmB,SAAnB,MAAmB;AAAA,QAAX,OAAW,SAAX,OAAW;;AACvE,QAAM,QAAQ,YAAY,QAAZ,CAAqB,WAArB,IAAoC,YAAY,QAAZ,CAAqB,KAAvE;;AAEA,QAAI,CAAC,YAAY,WAAZ,EAAD,IAA8B,CAAC,KAAnC,EAA0C;AACxC;AACD;;AAED,QAAM,OAAO,OAAO,WAAP,GACT,EAAE,GAAG,OAAO,CAAZ,EAAe,GAAG,OAAO,CAAzB,EADS,GAET,MAAM,MAAN,CAAa,EAAb,EAAiB,cAAjB,CAFJ;AAGA,QAAM,QAAQ,UAAU,UAAV,CAAqB,mBAAmB,QAAQ,KAA3B,EAAkC,WAAlC,EAA+C,IAA/C,CAArB,KAA8E,OAA5F;AACA,QAAM,QAAQ,UAAU,UAAV,CAAqB,mBAAmB,QAAQ,KAA3B,EAAkC,WAAlC,EAA+C,IAA/C,CAArB,KAA8E,OAA5F;;AAEA,QAAI,YAAY,KAAK,CAArB;AACA,QAAI,YAAY,KAAK,CAArB;;AAEA,WAAO,EAAP,GAAY,CAAZ;AACA,WAAO,EAAP,GAAY,CAAZ;AACA,WAAO,MAAP,GAAgB,KAAhB;;AAEA,QAAI,MAAM,GAAV,EAAe;AACb,kBAAY,KAAK,GAAL,CAAS,KAAK,GAAL,CAAS,MAAM,GAAN,GAAe,OAAO,GAA/B,EAAuC,KAAK,CAA5C,CAAT,EAAyD,MAAM,GAAN,GAAe,OAAO,GAA/E,CAAZ;AACD,KAFD,MAGK,IAAI,MAAM,MAAV,EAAkB;AACrB,kBAAY,KAAK,GAAL,CAAS,KAAK,GAAL,CAAS,MAAM,MAAN,GAAe,OAAO,MAA/B,EAAuC,KAAK,CAA5C,CAAT,EAAyD,MAAM,MAAN,GAAe,OAAO,MAA/E,CAAZ;AACD;AACD,QAAI,MAAM,IAAV,EAAgB;AACd,kBAAY,KAAK,GAAL,CAAS,KAAK,GAAL,CAAS,MAAM,IAAN,GAAe,OAAO,IAA/B,EAAuC,KAAK,CAA5C,CAAT,EAAyD,MAAM,IAAN,GAAe,OAAO,IAA/E,CAAZ;AACD,KAFD,MAGK,IAAI,MAAM,KAAV,EAAiB;AACpB,kBAAY,KAAK,GAAL,CAAS,KAAK,GAAL,CAAS,MAAM,KAAN,GAAe,OAAO,KAA/B,EAAuC,KAAK,CAA5C,CAAT,EAAyD,MAAM,KAAN,GAAe,OAAO,KAA/E,CAAZ;AACD;;AAED,WAAO,EAAP,GAAY,YAAY,KAAK,CAA7B;AACA,WAAO,EAAP,GAAY,YAAY,KAAK,CAA7B;;AAEA,WAAO,OAAP,GAAiB,OAAO,SAAP,KAAqB,SAArB,IAAkC,OAAO,SAAP,KAAqB,SAAxE;AACA,WAAO,MAAP,GAAgB,CAAC,EAAE,OAAO,EAAP,IAAa,OAAO,EAAtB,CAAjB;;AAEA,WAAO,SAAP,GAAmB,SAAnB;AACA,WAAO,SAAP,GAAmB,SAAnB;AACD,GArEmB;;AAuEpB,gBAAc,6BAAoD;AAAA,QAAxC,IAAwC,SAAxC,IAAwC;AAAA,QAAlC,MAAkC,SAAlC,MAAkC;AAAA,QAA1B,MAA0B,SAA1B,MAA0B;AAAA,QAAlB,KAAkB,SAAlB,KAAkB;AAAA,QAAX,OAAW,SAAX,OAAW;;AAChE,QAAI,WAAW,QAAQ,OAAnB,IACG,EAAE,UAAU,OAAV,IAAqB,OAAO,MAA9B,CADP,EAC8C;;AAE5C,UAAI,OAAO,MAAX,EAAmB;AACjB,aAAK,CAAL,IAAU,OAAO,EAAjB;AACA,aAAK,CAAL,IAAU,OAAO,EAAjB;AACA,eAAO,CAAP,IAAY,OAAO,EAAnB;AACA,eAAO,CAAP,IAAY,OAAO,EAAnB;;AAEA,eAAO;AACL,cAAI,OAAO,EADN;AAEL,cAAI,OAAO;AAFN,SAAP;AAID;AACF;AACF,GAvFmB;;AAyFpB,kBAzFoB;AA0FpB,kBA1FoB;AA2FpB;AA3FoB,CAAtB;;AA8FA,UAAU,aAAV,GAA0B,aAA1B;AACA,UAAU,KAAV,CAAgB,IAAhB,CAAqB,eAArB;;AAEA,eAAe,SAAf,CAAyB,aAAzB,GAAyC,cAAc,QAAvD;AACA,OAAO,QAAP,CAAgB,aAAhB,GAAyC,cAAc,QAAvD;;AAEA,OAAO,OAAP,GAAiB,aAAjB;;;;;AC1HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAM,YAAiB,QAAQ,QAAR,CAAvB;AACA,IAAM,gBAAiB,QAAQ,iBAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA,IAAM,YAAiB,QAAQ,eAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,mBAAR,CAAvB;AACA,IAAM,SAAiB,QAAQ,mBAAR,CAAvB;;AAEA,IAAM,QAAQ,EAAE,OAAO,CAAC,QAAV,EAAoB,QAAQ,CAAC,QAA7B,EAAd;AACA,IAAM,QAAQ,EAAE,OAAO,CAAC,QAAV,EAAoB,QAAQ,CAAC,QAA7B,EAAd;;AAEA,IAAM,eAAe;AACnB,YAAU;AACR,aAAS,KADD;AAER,aAAS,KAFD;AAGR,SAAK,IAHG;AAIR,SAAK;AAJG,GADS;;AAQnB,aAAW,yBAA2B;AAAA,QAAf,WAAe,QAAf,WAAe;;AACpC,WAAO,YAAY,WAAnB;AACD,GAVkB;;AAYnB,OAAK,aAAU,GAAV,EAAe;AAAA,QACV,WADU,GACe,GADf,CACV,WADU;AAAA,QACG,OADH,GACe,GADf,CACG,OADH;;AAElB,QAAM,QAAQ,YAAY,QAAZ,CAAqB,WAArB,IAAoC,YAAY,QAAZ,CAAqB,KAAvE;;AAEA,QAAI,CAAC,YAAY,WAAZ,EAAD,IAA8B,CAAC,KAAnC,EAA0C;AACxC;AACD;;AAED,QAAM,OAAO,UAAU,UAAV,CAAqB,YAAY,WAAZ,CAAwB,QAA7C,CAAb;;AAEA,QAAM,UAAU,UAAU,UAAV,CAAqB,cAAc,kBAAd,CAAiC,QAAQ,GAAzC,EAA8C,WAA9C,CAArB,KAAoF,KAApG;AACA,QAAM,UAAU,UAAU,UAAV,CAAqB,cAAc,kBAAd,CAAiC,QAAQ,GAAzC,EAA8C,WAA9C,CAArB,KAAoF,KAApG;;AAEA,QAAI,OAAJ,GAAc;AACZ,eAAS,QAAQ,OADL;AAEZ,eAAS,QAAQ,OAFL;AAGZ,aAAO,MAAM,MAAN,CAAa,EAAb,EAAiB,cAAc,OAA/B,CAHK;AAIZ,aAAO,MAAM,MAAN,CAAa,EAAb,EAAiB,cAAc,OAA/B;AAJK,KAAd;;AAOA,QAAI,MAAM,GAAV,EAAe;AACb,UAAI,OAAJ,CAAY,KAAZ,CAAkB,GAAlB,GAAwB,KAAK,MAAL,GAAc,QAAQ,MAA9C;AACA,UAAI,OAAJ,CAAY,KAAZ,CAAkB,GAAlB,GAAwB,KAAK,MAAL,GAAc,QAAQ,MAA9C;AACD,KAHD,MAIK,IAAI,MAAM,MAAV,EAAkB;AACrB,UAAI,OAAJ,CAAY,KAAZ,CAAkB,MAAlB,GAA2B,KAAK,GAAL,GAAW,QAAQ,MAA9C;AACA,UAAI,OAAJ,CAAY,KAAZ,CAAkB,MAAlB,GAA2B,KAAK,GAAL,GAAW,QAAQ,MAA9C;AACD;AACD,QAAI,MAAM,IAAV,EAAgB;AACd,UAAI,OAAJ,CAAY,KAAZ,CAAkB,IAAlB,GAAyB,KAAK,KAAL,GAAa,QAAQ,KAA9C;AACA,UAAI,OAAJ,CAAY,KAAZ,CAAkB,IAAlB,GAAyB,KAAK,KAAL,GAAa,QAAQ,KAA9C;AACD,KAHD,MAIK,IAAI,MAAM,KAAV,EAAiB;AACpB,UAAI,OAAJ,CAAY,KAAZ,CAAkB,KAAlB,GAA0B,KAAK,IAAL,GAAY,QAAQ,KAA9C;AACA,UAAI,OAAJ,CAAY,KAAZ,CAAkB,KAAlB,GAA0B,KAAK,IAAL,GAAY,QAAQ,KAA9C;AACD;;AAED,kBAAc,GAAd,CAAkB,GAAlB;AACD,GAlDkB;;AAoDnB,gBAAc,cAAc;AApDT,CAArB;;AAuDA,UAAU,YAAV,GAAyB,YAAzB;AACA,UAAU,KAAV,CAAgB,IAAhB,CAAqB,cAArB;;AAEA,eAAe,SAAf,CAAyB,YAAzB,GAAwC,aAAa,QAArD;AACA,OAAO,QAAP,CAAgB,YAAhB,GAAwC,aAAa,QAArD;;AAEA,OAAO,OAAP,GAAiB,YAAjB;;;;;AClFA,IAAM,YAAiB,QAAQ,QAAR,CAAvB;AACA,IAAM,WAAiB,QAAQ,aAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,UAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,mBAAR,CAAvB;;AAEA,IAAM,OAAO;AACX,YAAU;AACR,aAAS,KADD;AAER,aAAS,KAFD;AAGR,WAAS,QAHD;AAIR,aAAS,IAJD;AAKR,aAAS,IALD;;AAOR,oBAAgB;AAPR,GADC;;AAWX,aAAW,yBAA8E;AAAA,QAAlE,WAAkE,QAAlE,WAAkE;AAAA,QAArD,YAAqD,QAArD,YAAqD;AAAA,QAAvC,OAAuC,QAAvC,OAAuC;AAAA,QAA9B,IAA8B,QAA9B,IAA8B;AAAA,QAAxB,WAAwB,QAAxB,WAAwB;AAAA,QAAX,OAAW,QAAX,OAAW;;AACvF,QAAM,UAAU,EAAhB;AACA,QAAM,gBAAgB,MAAM,QAAN,CAAe,MAAM,eAAN,CAAsB,QAAQ,MAA9B,CAAf,CAAtB;AACA,QAAM,SAAS,iBAAiB,MAAM,WAAN,CAAkB,YAAlB,EAAgC,OAAhC,EAAyC,YAAY,QAAZ,CAAqB,IAA9D,CAAhC;AACA,cAAU,WAAW,aAAa,OAAb,CAAqB,YAAY,QAAZ,CAAqB,IAA1C,EAAgD,IAA3D,IAAmE,EAA7E;;AAEA,QAAI,mBAAJ;;AAEA,QAAI,QAAQ,MAAR,KAAmB,aAAvB,EAAsC;AACpC,mBAAa;AACX,WAAG,YAAY,WAAZ,CAAwB,IAAxB,CAA6B,CAA7B,GAAiC,OAAO,CADhC;AAEX,WAAG,YAAY,WAAZ,CAAwB,IAAxB,CAA6B,CAA7B,GAAiC,OAAO;AAFhC,OAAb;AAID,KALD,MAMM;AACJ,UAAM,aAAa,MAAM,eAAN,CAAsB,QAAQ,MAA9B,EAAsC,YAAtC,EAAoD,OAApD,EAA6D,CAAC,WAAD,CAA7D,CAAnB;;AAEA,mBAAa,MAAM,QAAN,CAAe,UAAf,KAA8B,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EAA3C;AACD;;AAED,QAAI,QAAQ,QAAQ,cAAhB,IAAkC,QAAQ,cAAR,CAAuB,MAA7D,EAAqE;AACnE,4BAA6C,QAAQ,cAArD,eAAqE;AAAA;;AAAA,gBAAxB,QAAQ,cAAgB;AAAA;AAAA,YAArD,SAAqD,SAAxD,CAAwD;AAAA,YAAvC,SAAuC,SAA1C,CAA0C;;AACnE,gBAAQ,IAAR,CAAa;AACX,aAAG,YAAY,IAAZ,GAAoB,KAAK,KAAL,GAAc,SAAlC,GAA+C,WAAW,CADlD;AAEX,aAAG,YAAY,GAAZ,GAAoB,KAAK,MAAL,GAAc,SAAlC,GAA+C,WAAW;AAFlD,SAAb;AAID;AACF,KAPD,MAQK;AACH,cAAQ,IAAR,CAAa,UAAb;AACD;;AAED,WAAO,OAAP;AACD,GA5CU;;AA8CX,OAAK,oBAA6E;AAAA,QAAjE,WAAiE,SAAjE,WAAiE;AAAA,QAApD,cAAoD,SAApD,cAAoD;AAAA,QAApC,MAAoC,SAApC,MAAoC;AAAA,QAA5B,OAA4B,SAA5B,OAA4B;AAAA,QAAX,OAAW,SAAnB,MAAmB;;AAChF,QAAM,UAAU,EAAhB;AACA,QAAI,eAAJ;AACA,QAAI,aAAJ;AACA,QAAI,UAAJ;;AAEA,QAAI,OAAO,WAAX,EAAwB;AACtB,aAAO,EAAE,GAAG,OAAO,CAAZ,EAAe,GAAG,OAAO,CAAzB,EAAP;AACD,KAFD,MAGK;AACH,UAAM,SAAS,MAAM,WAAN,CAAkB,YAAY,MAA9B,EAAsC,YAAY,OAAlD,EAA2D,YAAY,QAAZ,CAAqB,IAAhF,CAAf;;AAEA,aAAO,MAAM,MAAN,CAAa,EAAb,EAAiB,cAAjB,CAAP;;AAEA,WAAK,CAAL,IAAU,OAAO,CAAjB;AACA,WAAK,CAAL,IAAU,OAAO,CAAjB;AACD;;AAED,WAAO,KAAP,GAAe,KAAK,CAApB;AACA,WAAO,KAAP,GAAe,KAAK,CAApB;;AAEA,QAAI,MAAM,QAAQ,OAAR,GAAiB,QAAQ,OAAR,CAAgB,MAAjC,GAA0C,CAApD;;AAEA,4BAAyC,OAAzC,gBAAkD;AAAA;;AAAA,cAAT,OAAS;AAAA;AAAA,UAAlC,OAAkC,SAArC,CAAqC;AAAA,UAAtB,OAAsB,SAAzB,CAAyB;;AAChD,UAAM,YAAY,KAAK,CAAL,GAAS,OAA3B;AACA,UAAM,YAAY,KAAK,CAAL,GAAS,OAA3B;;AAEA,+BAA0B,QAAQ,OAAR,IAAmB,EAA7C,iBAAkD;AAAA;;AAAA,iBAAxB,QAAQ,OAAR,IAAmB,EAAK;AAAA,YAAvC,UAAuC;;AAChD,YAAI,MAAM,EAAN,CAAS,QAAT,CAAkB,UAAlB,CAAJ,EAAmC;AACjC,mBAAS,WAAW,SAAX,EAAsB,SAAtB,EAAiC,WAAjC,CAAT;AACD,SAFD,MAGK;AACH,mBAAS,UAAT;AACD;;AAED,YAAI,CAAC,MAAL,EAAa;AAAE;AAAW;;AAE1B,gBAAQ,IAAR,CAAa;AACX,aAAG,MAAM,EAAN,CAAS,MAAT,CAAgB,OAAO,CAAvB,IAA6B,OAAO,CAAP,GAAW,OAAxC,GAAmD,SAD3C;AAEX,aAAG,MAAM,EAAN,CAAS,MAAT,CAAgB,OAAO,CAAvB,IAA6B,OAAO,CAAP,GAAW,OAAxC,GAAmD,SAF3C;;AAIX,iBAAO,MAAM,EAAN,CAAS,MAAT,CAAgB,OAAO,KAAvB,IAA+B,OAAO,KAAtC,GAA6C,QAAQ;AAJjD,SAAb;AAMD;AACF;;AAED,QAAM,UAAU;AACd,cAAQ,IADM;AAEd,eAAS,KAFK;AAGd,gBAAU,CAHI;AAId,aAAO,CAJO;AAKd,UAAI,CALU;AAMd,UAAI;AANU,KAAhB;;AASA,SAAK,IAAI,CAAJ,EAAO,MAAM,QAAQ,MAA1B,EAAkC,IAAI,GAAtC,EAA2C,GAA3C,EAAgD;AAC9C,eAAS,QAAQ,CAAR,CAAT;;AAEA,UAAM,QAAQ,OAAO,KAArB;AACA,UAAM,KAAK,OAAO,CAAP,GAAW,KAAK,CAA3B;AACA,UAAM,KAAK,OAAO,CAAP,GAAW,KAAK,CAA3B;AACA,UAAM,WAAW,MAAM,KAAN,CAAY,EAAZ,EAAgB,EAAhB,CAAjB;AACA,UAAI,UAAU,YAAY,KAA1B;;AAEA;AACA;AACA,UAAI,UAAU,QAAV,IAAsB,QAAQ,OAA9B,IAAyC,QAAQ,KAAR,KAAkB,QAA/D,EAAyE;AACvE,kBAAU,KAAV;AACD;;AAED,UAAI,CAAC,QAAQ,MAAT,KAAoB;AACpB;AADoB,QAEjB,QAAQ,OAAR,IAAmB,UAAU;AAChC;AADG,QAED,WAAW,KAAX,GAAmB,QAAQ,QAAR,GAAmB,QAAQ;AAChD;AAHG,QAIA,UAAU,QAAV,IAAsB,QAAQ,KAAR,KAAkB,QAAzC;AACF;AACC,iBAAW,QAAQ,QARA;AASpB;AACG,OAAC,QAAQ,OAAT,IAAoB,WAAW,QAAQ,QAV1C,CAAJ,EAU0D;;AAExD,gBAAQ,MAAR,GAAiB,MAAjB;AACA,gBAAQ,QAAR,GAAmB,QAAnB;AACA,gBAAQ,KAAR,GAAgB,KAAhB;AACA,gBAAQ,OAAR,GAAkB,OAAlB;AACA,gBAAQ,EAAR,GAAa,EAAb;AACA,gBAAQ,EAAR,GAAa,EAAb;;AAEA,eAAO,KAAP,GAAe,KAAf;AACD;AACF;;AAED,QAAI,oBAAJ;;AAEA,QAAI,QAAQ,MAAZ,EAAoB;AAClB,oBAAe,OAAO,SAAP,KAAqB,QAAQ,MAAR,CAAe,CAApC,IAAyC,OAAO,SAAP,KAAqB,QAAQ,MAAR,CAAe,CAA5F;;AAEA,aAAO,SAAP,GAAmB,QAAQ,MAAR,CAAe,CAAlC;AACA,aAAO,SAAP,GAAmB,QAAQ,MAAR,CAAe,CAAlC;AACD,KALD,MAMK;AACH,oBAAc,IAAd;;AAEA,aAAO,SAAP,GAAmB,GAAnB;AACA,aAAO,SAAP,GAAmB,GAAnB;AACD;;AAED,WAAO,EAAP,GAAY,QAAQ,EAApB;AACA,WAAO,EAAP,GAAY,QAAQ,EAApB;;AAEA,WAAO,OAAP,GAAkB,eAAgB,QAAQ,OAAR,IAAmB,CAAC,OAAO,MAA7D;AACA,WAAO,MAAP,GAAgB,QAAQ,OAAxB;AACD,GA/JU;;AAiKX,gBAAc,6BAAoD;AAAA,QAAxC,IAAwC,SAAxC,IAAwC;AAAA,QAAlC,MAAkC,SAAlC,MAAkC;AAAA,QAA1B,MAA0B,SAA1B,MAA0B;AAAA,QAAlB,KAAkB,SAAlB,KAAkB;AAAA,QAAX,OAAW,SAAX,OAAW;;AAChE,QAAM,iBAAiB,WAAW,QAAQ,cAA1C;;AAEA,QAAI,WAAW,QAAQ,OAAnB,IACG,EAAE,UAAU,OAAV,IAAqB,cAArB,IAAuC,eAAe,MAAxD,CADP,EACwE;;AAEtE,UAAI,OAAO,MAAX,EAAmB;AACjB,aAAK,CAAL,IAAU,OAAO,EAAjB;AACA,aAAK,CAAL,IAAU,OAAO,EAAjB;AACA,eAAO,CAAP,IAAY,OAAO,EAAnB;AACA,eAAO,CAAP,IAAY,OAAO,EAAnB;AACD;;AAED,aAAO;AACL,eAAS,OAAO,KADX;AAEL,gBAAS,OAAO,MAFX;AAGL,WAAS,OAAO,SAHX;AAIL,WAAS,OAAO,SAJX;AAKL,eAAS,OAAO,KALX;AAML,eAAS,OAAO,KANX;AAOL,YAAS,OAAO,EAPX;AAQL,YAAS,OAAO;AARX,OAAP;AAUD;AACF;AAzLU,CAAb;;AA4LA,SAAS,cAAT,GAA0B,UAAU,IAAV,EAAgB;AACxC,SAAO,UAAU,CAAV,EAAa,CAAb,EAAgB;AACrB,QAAM,SAAS,KAAK,MAAL,IAAe;AAC5B,YAAQ,CAAC,QADmB;AAE5B,aAAS,QAFmB;AAG5B,WAAQ,CAAC,QAHmB;AAI5B,cAAS;AAJmB,KAA9B;AAMA,QAAI,UAAU,CAAd;AACA,QAAI,UAAU,CAAd;;AAEA,QAAI,MAAM,EAAN,CAAS,MAAT,CAAgB,KAAK,MAArB,CAAJ,EAAkC;AAChC,gBAAU,KAAK,MAAL,CAAY,CAAtB;AACA,gBAAU,KAAK,MAAL,CAAY,CAAtB;AACD;;AAED,QAAM,QAAQ,KAAK,KAAL,CAAW,CAAC,IAAI,OAAL,IAAgB,KAAK,CAAhC,CAAd;AACA,QAAM,QAAQ,KAAK,KAAL,CAAW,CAAC,IAAI,OAAL,IAAgB,KAAK,CAAhC,CAAd;;AAEA,QAAM,OAAO,KAAK,GAAL,CAAS,OAAO,IAAhB,EAAsB,KAAK,GAAL,CAAS,OAAO,KAAhB,EAAwB,QAAQ,KAAK,CAAb,GAAiB,OAAzC,CAAtB,CAAb;AACA,QAAM,OAAO,KAAK,GAAL,CAAS,OAAO,GAAhB,EAAsB,KAAK,GAAL,CAAS,OAAO,MAAhB,EAAwB,QAAQ,KAAK,CAAb,GAAiB,OAAzC,CAAtB,CAAb;;AAEA,WAAO;AACL,SAAG,IADE;AAEL,SAAG,IAFE;AAGL,aAAO,KAAK;AAHP,KAAP;AAKD,GA1BD;AA2BD,CA5BD;;AA8BA,UAAU,IAAV,GAAiB,IAAjB;AACA,UAAU,KAAV,CAAgB,IAAhB,CAAqB,MAArB;;AAEA,eAAe,SAAf,CAAyB,IAAzB,GAAgC,KAAK,QAArC;;AAEA,OAAO,OAAP,GAAiB,IAAjB;;;;;ACpOA;AACA;;AAEA,IAAM,YAAiB,QAAQ,QAAR,CAAvB;AACA,IAAM,OAAiB,QAAQ,QAAR,CAAvB;AACA,IAAM,iBAAiB,QAAQ,mBAAR,CAAvB;AACA,IAAM,SAAiB,QAAQ,mBAAR,CAAvB;AACA,IAAM,QAAiB,QAAQ,WAAR,CAAvB;;AAEA,IAAM,WAAW;AACf,YAAU;AACR,aAAS,KADD;AAER,aAAS,KAFD;AAGR,WAAS,QAHD;AAIR,aAAS,IAJD;AAKR,aAAS;AALD,GADK;;AASf,aAAW,mBAAU,GAAV,EAAe;AAAA,QAChB,WADgB,GACS,GADT,CAChB,WADgB;AAAA,QACH,OADG,GACS,GADT,CACH,OADG;;AAExB,QAAM,QAAQ,YAAY,QAAZ,CAAqB,KAAnC;;AAEA,QAAI,CAAC,KAAL,EAAY;AAAE;AAAS;;AAEvB,QAAI,OAAJ,GAAc;AACZ,sBAAgB,CAAC;AACf,WAAG,MAAM,IAAN,GAAY,CAAZ,GAAgB,CADJ;AAEf,WAAG,MAAM,GAAN,GAAY,CAAZ,GAAgB;AAFJ,OAAD,CADJ;AAKZ,cAAQ,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EALI;AAMZ,cAAQ,MANI;AAOZ,aAAO,QAAQ;AAPH,KAAd;;AAUA,QAAM,UAAU,KAAK,SAAL,CAAe,GAAf,CAAhB;AACA,QAAI,OAAJ,GAAc,OAAd;;AAEA,WAAO,OAAP;AACD,GA7Bc;;AA+Bf,OAAK,aAAU,GAAV,EAAe;AAAA,QACV,WADU,GACuC,GADvC,CACV,WADU;AAAA,QACG,OADH,GACuC,GADvC,CACG,OADH;AAAA,QACY,MADZ,GACuC,GADvC,CACY,MADZ;AAAA,QACoB,cADpB,GACuC,GADvC,CACoB,cADpB;;AAElB,QAAM,OAAO,MAAM,MAAN,CAAa,EAAb,EAAiB,cAAjB,CAAb;AACA,QAAM,YAAY,KAAK,CAAL,GAAS,OAAO,CAAP,EAAU,CAArC;AACA,QAAM,YAAY,KAAK,CAAL,GAAS,OAAO,CAAP,EAAU,CAArC;;AAEA,QAAI,OAAJ,GAAc,MAAM,MAAN,CAAa,EAAb,EAAiB,OAAjB,CAAd;AACA,QAAI,OAAJ,CAAY,OAAZ,GAAsB,EAAtB;;AAEA,2BAA0B,QAAQ,OAAR,IAAmB,EAA7C,gBAAkD;AAAA;;AAAA,cAAxB,QAAQ,OAAR,IAAmB,EAAK;AAAA,UAAvC,UAAuC;;AAChD,UAAI,eAAJ;;AAEA,UAAI,MAAM,EAAN,CAAS,QAAT,CAAkB,UAAlB,CAAJ,EAAmC;AACjC,iBAAS,WAAW,SAAX,EAAsB,SAAtB,EAAiC,WAAjC,CAAT;AACD,OAFD,MAGK;AACH,iBAAS,UAAT;AACD;;AAED,UAAI,CAAC,MAAL,EAAa;AAAE;AAAW;;AAE1B,UAAI,WAAW,MAAX,IAAqB,YAAY,MAArC,EAA6C;AAC3C,eAAO,CAAP,GAAW,OAAO,KAAlB;AACA,eAAO,CAAP,GAAW,OAAO,MAAlB;AACD;;AAED,UAAI,OAAJ,CAAY,OAAZ,CAAoB,IAApB,CAAyB,MAAzB;AACD;;AAED,SAAK,GAAL,CAAS,GAAT;AACD,GA7Dc;;AA+Df,gBAAc,sBAAU,GAAV,EAAe;AAAA,QACnB,OADmB,GACP,GADO,CACnB,OADmB;;;AAG3B,QAAI,OAAJ,GAAc,MAAM,MAAN,CAAa,EAAb,EAAiB,OAAjB,CAAd;AACA,QAAI,OAAJ,CAAY,OAAZ,GAAsB,QAAQ,OAA9B;AACA,QAAI,OAAJ,CAAY,cAAZ,GAA6B,CAAC,IAAD,CAA7B;;AAEA,SAAK,YAAL,CAAkB,GAAlB;AACD;AAvEc,CAAjB;;AA0EA,UAAU,QAAV,GAAqB,QAArB;AACA,UAAU,KAAV,CAAgB,IAAhB,CAAqB,UAArB;;AAEA,eAAe,SAAf,CAAyB,QAAzB,GAAoC,SAAS,QAA7C;AACA,OAAO,QAAP,CAAgB,QAAhB,GAAoC,SAAS,QAA7C;;AAEA,OAAO,OAAP,GAAiB,QAAjB;;;;;;;ACzFA,IAAM,eAAe,QAAQ,uBAAR,CAArB;;AAEA,OAAO,OAAP;AACE;AACA,wBAAa,IAAb,EAAmB,OAAnB,EAA4B,KAA5B,EAAmC,WAAnC,EAAgD,WAAhD,EAA6D;AAAA;;AAC3D,iBAAa,aAAb,CAA2B,IAA3B,EAAiC,KAAjC;;AAEA,QAAI,UAAU,OAAd,EAAuB;AACrB,mBAAa,aAAb,CAA2B,IAA3B,EAAiC,OAAjC;AACD;;AAED,SAAK,WAAL,GAAmB,WAAnB;;AAEA,SAAK,SAAL,GAAqB,IAAI,IAAJ,GAAW,OAAX,EAArB;AACA,SAAK,aAAL,GAAqB,KAArB;AACA,SAAK,IAAL,GAAqB,IAArB;AACA,SAAK,SAAL,GAAqB,aAAa,YAAb,CAA0B,OAA1B,CAArB;AACA,SAAK,WAAL,GAAqB,aAAa,cAAb,CAA4B,OAA5B,CAArB;AACA,SAAK,MAAL,GAAqB,WAArB;AACA,SAAK,aAAL,GAAqB,IAArB;;AAEA,QAAI,SAAS,KAAb,EAAoB;AAClB,UAAM,eAAe,YAAY,eAAZ,CAA4B,OAA5B,CAArB;AACA,WAAK,EAAL,GAAU,KAAK,SAAL,GAAiB,YAAY,SAAZ,CAAsB,YAAtB,CAA3B;;AAEA,UAAM,WAAW,KAAK,SAAL,GAAiB,YAAY,OAA9C;;AAEA,WAAK,MAAL,GAAc,CAAC,EAAE,YAAY,OAAZ,IACZ,YAAY,OAAZ,CAAoB,IAApB,KAA6B,WADjB,IAEZ,YAAY,OAAZ,CAAoB,MAApB,KAA+B,KAAK,MAFxB,IAGZ,WAAW,GAHD,CAAf;AAID,KAVD,MAWK,IAAI,SAAS,WAAb,EAA0B;AAC7B,WAAK,EAAL,GAAU,QAAQ,SAAR,GAAoB,YAAY,OAA1C;AACD;AACF;;AAjCH,yBAmCE,cAnCF,iCAmC8C;AAAA,QAAvB,OAAuB,QAA1B,CAA0B;AAAA,QAAX,OAAW,QAAd,CAAc;;AAC1C,SAAK,KAAL,IAAgB,OAAhB;AACA,SAAK,KAAL,IAAgB,OAAhB;AACA,SAAK,OAAL,IAAgB,OAAhB;AACA,SAAK,OAAL,IAAgB,OAAhB;;AAEA,WAAO,IAAP;AACD,GA1CH;;AAAA,yBA4CE,SA5CF,6BA4CyC;AAAA,QAAvB,OAAuB,SAA1B,CAA0B;AAAA,QAAX,OAAW,SAAd,CAAc;;AACrC,SAAK,KAAL,IAAgB,OAAhB;AACA,SAAK,KAAL,IAAgB,OAAhB;AACA,SAAK,OAAL,IAAgB,OAAhB;AACA,SAAK,OAAL,IAAgB,OAAhB;;AAEA,WAAO,IAAP;AACD,GAnDH;;AAqDE;;;AArDF,yBAsDE,cAtDF,6BAsDoB;AAChB,SAAK,aAAL,CAAmB,cAAnB;AACD,GAxDH;;AA0DE;;;AA1DF,yBA2DE,eA3DF,8BA2DqB;AACjB,SAAK,kBAAL,GAA0B,IAA1B;AACD,GA7DH;;AA+DE;;;AA/DF,yBAgEE,wBAhEF,uCAgE8B;AAC1B,SAAK,2BAAL,GAAmC,KAAK,kBAAL,GAA0B,IAA7D;AACD,GAlEH;;AAAA;AAAA;;;;;ACFA,IAAM,eAAe,QAAQ,gBAAR,CAArB;AACA,IAAM,cAAe,QAAQ,gBAAR,CAArB;AACA,IAAM,QAAe,QAAQ,UAAR,CAArB;AACA,IAAM,WAAe,QAAQ,mBAAR,CAArB;AACA,IAAM,UAAe,QAAQ,kBAAR,EAA4B,GAA5B,EAArB;;AAEA,IAAM,gBAAgB,CAAE,MAAF,EAAU,IAAV,EAAgB,QAAhB,CAAtB;AACA,IAAM,eAAgB,CAAE,MAAF,EAAU,IAAV,EAAgB,QAAhB,CAAtB;;AAEA,IAAM,gBAAgB;AACpB,4BADoB;AAEpB,YAFoB;AAGpB,0CAHoB;AAIpB,kBAJoB;AAKpB,YAAU;AACR,kBAAc,GADN;AAER,gBAAc,IAFN;AAGR,eAAc,IAHN;AAIR,YAAc,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX;AAJN,GALU;AAWpB,SAAO,CACL,MADK,EAEL,MAFK,EAGL,IAHK,EAIL,QAJK,EAKL,KALK,EAML,WANK,EAOL,MAPK;AAXa,CAAtB;;AAsBA,SAAS,IAAT,CAAe,GAAf,EAAoB;AAAA,MAEhB,WAFgB,GAMd,GANc,CAEhB,WAFgB;AAAA,MAEH,OAFG,GAMd,GANc,CAEH,OAFG;AAAA,MAEM,KAFN,GAMd,GANc,CAEM,KAFN;AAAA,MAEa,WAFb,GAMd,GANc,CAEa,WAFb;AAAA,kBAMd,GANc,CAGhB,IAHgB;AAAA,MAGhB,IAHgB,6BAGT,IAAI,YAAJ,CAAiB,IAHR;AAAA,qBAMd,GANc,CAIhB,OAJgB;AAAA,MAIhB,OAJgB,gCAIN,oBAAoB,GAApB,CAJM;AAAA,0BAMd,GANc,CAKhB,YALgB;AAAA,MAKhB,YALgB,qCAKD,IAAI,YAAJ,CAAiB,IAAjB,EAAuB,OAAvB,EAAgC,KAAhC,EAAuC,WAAvC,EAAoD,WAApD,CALC;;;AAQlB,MAAM,YAAY;AAChB,4BADgB;AAEhB,oBAFgB;AAGhB,gBAHgB;AAIhB,4BAJgB;AAKhB,oBALgB;AAMhB,cANgB;AAOhB;AAPgB,GAAlB;;AAUA,OAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,QAAQ,MAA5B,EAAoC,GAApC,EAAyC;AACvC,QAAM,SAAS,QAAQ,CAAR,CAAf;;AAEA,SAAK,IAAM,IAAX,IAAmB,OAAO,KAAP,IAAgB,EAAnC,EAAuC;AACrC,mBAAa,IAAb,IAAqB,OAAO,KAAP,CAAa,IAAb,CAArB;AACD;;AAED,QAAM,SAAS,MAAM,WAAN,CAAkB,OAAO,SAAzB,EAAoC,OAAO,OAA3C,CAAf;;AAEA,iBAAa,cAAb,CAA4B,MAA5B;AACA,iBAAa,SAAb,GAAyB,OAAO,SAAhC;AACA,iBAAa,aAAb,GAA6B,OAAO,OAApC;;AAEA,WAAO,SAAP,CAAiB,IAAjB,CAAsB,YAAtB;;AAEA,iBAAa,SAAb,CAAuB,MAAvB;;AAEA,QAAI,aAAa,2BAAb,IACI,aAAa,kBAAb,IACI,IAAI,CAAL,GAAU,QAAQ,MADrB,IAC+B,QAAQ,IAAI,CAAZ,EAAe,OAAf,KAA2B,aAAa,aAF/E,EAE+F;AAC7F;AACD;AACF;;AAED,UAAQ,IAAR,CAAa,OAAb,EAAsB,SAAtB;;AAEA,MAAI,SAAS,KAAb,EAAoB;AAClB;AACA;AACA,QAAM,UAAU,aAAa,MAAb,GACZ,KAAK;AACL,8BADK,EACQ,gBADR,EACiB,YADjB,EACwB,wBADxB;AAEL,YAAM;AAFD,KAAL,CADY,GAKZ,YALJ;;AAOA,gBAAY,OAAZ,GAAsB,OAAtB;AACA,gBAAY,OAAZ,GAAsB,QAAQ,SAA9B;AACD;;AAED,SAAO,YAAP;AACD;;AAED,SAAS,mBAAT,OAAkF;AAAA,MAAlD,WAAkD,QAAlD,WAAkD;AAAA,MAArC,OAAqC,QAArC,OAAqC;AAAA,MAA5B,KAA4B,QAA5B,KAA4B;AAAA,MAArB,WAAqB,QAArB,WAAqB;AAAA,MAAR,IAAQ,QAAR,IAAQ;;AAChF,MAAM,eAAe,YAAY,eAAZ,CAA4B,OAA5B,CAArB;;AAEA;AACA,MAAI,SAAS,KAAT,KAAmB,YAAY;AAC/B;AADmB,KAEhB,EAAE,YAAY,WAAZ,CAAwB,YAAxB,KAAyC,YAAY,WAAZ,CAAwB,YAAxB,MAA0C,WAArF,CAFH,CAAJ,EAE2G;AACzG,WAAO,EAAP;AACD;;AAED,MAAM,OAAO,MAAM,OAAN,CAAc,WAAd,CAAb;AACA,MAAM,YAAY;AAChB,4BADgB;AAEhB,oBAFgB;AAGhB,gBAHgB;AAIhB,4BAJgB;AAKhB,cALgB;AAMhB,cANgB;AAOhB,aAAS,EAPO;AAQhB,aAAS;AARO,GAAlB;;AAWA,wBAAsB,IAAtB,eAA4B;AAAA;;AAAA,YAAN,IAAM;AAAA,QAAjB,OAAiB;;AAC1B,cAAU,OAAV,GAAoB,OAApB;;AAEA,YAAQ,IAAR,CAAa,iBAAb,EAAgC,SAAhC;AACD;;AAED,MAAI,SAAS,MAAb,EAAqB;AACnB,cAAU,OAAV,GAAoB,UAAU,OAAV,CAAkB,MAAlB,CAAyB;AAAA,aAC3C,OAAO,SAAP,CAAiB,OAAjB,CAAyB,YAAzB,KAA0C,YAAY,UAAZ,CAAuB,YAAvB,EAAqC,QADpC;AAAA,KAAzB,CAApB;AAED;;AAED,SAAO,UAAU,OAAjB;AACD;;AAED,YAAY,OAAZ,CAAoB,EAApB,CAAuB,qBAAvB,EAA8C,iBAAyC;AAAA,MAA7B,WAA6B,SAA7B,WAA6B;AAAA,MAAhB,YAAgB,SAAhB,YAAgB;;AACrF,cAAY,UAAZ,CAAuB,YAAvB,IAAuC,EAAE,UAAU,QAAZ,EAAsB,SAAS,IAA/B,EAAvC;AACD,CAFD;;AAIA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,gBAAvB,EAAyC,iBAAyC;AAAA,MAA7B,WAA6B,SAA7B,WAA6B;AAAA,MAAhB,YAAgB,SAAhB,YAAgB;;AAChF,cAAY,UAAZ,CAAuB,MAAvB,CAA8B,YAA9B,EAA4C,CAA5C;AACD,CAFD;;AAIA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,MAAvB,EAA+B,iBAAuE;AAAA,MAA3D,WAA2D,SAA3D,WAA2D;AAAA,MAA9C,OAA8C,SAA9C,OAA8C;AAAA,MAArC,KAAqC,SAArC,KAAqC;AAAA,MAA9B,WAA8B,SAA9B,WAA8B;AAAA,MAAjB,aAAiB,SAAjB,aAAiB;;AACpG,MAAM,eAAe,YAAY,eAAZ,CAA4B,OAA5B,CAArB;;AAEA,MAAI,CAAC,aAAD,KAAmB,CAAC,YAAY,aAAb,IAA8B,YAAY,eAA7D,CAAJ,EAAmF;AACjF,QAAI,YAAY,aAAhB,EAA+B;AAC7B,mBAAa,YAAY,UAAZ,CAAuB,YAAvB,EAAqC,OAAlD;AACD;;AAED,SAAK;AACH,8BADG,EACU,gBADV,EACmB,YADnB,EAC0B,wBAD1B;AAEH,YAAM;AAFH,KAAL;AAID;AACF,CAbD;;AAeA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,MAAvB,EAA+B,iBAAsE;AAAA,MAA1D,WAA0D,SAA1D,WAA0D;AAAA,MAA7C,OAA6C,SAA7C,OAA6C;AAAA,MAApC,KAAoC,SAApC,KAAoC;AAAA,MAA7B,WAA6B,SAA7B,WAA6B;AAAA,MAAhB,YAAgB,SAAhB,YAAgB;;AACnG,MAAM,QAAQ,YAAY,UAAZ,CAAuB,YAAvB,CAAd;AACA,MAAM,OAAO,MAAM,OAAN,CAAc,WAAd,CAAb;AACA,MAAM,YAAY;AAChB,4BADgB;AAEhB,oBAFgB;AAGhB,gBAHgB;AAIhB,4BAJgB;AAKhB,UAAM,MALU;AAMhB,aAAS,EANO;AAOhB,cAPgB;AAQhB,aAAS;AARO,GAAlB;;AAWA,0BAAsB,IAAtB,gBAA4B;AAAA;;AAAA,YAAN,IAAM;AAAA,QAAjB,OAAiB;;AAC1B,cAAU,OAAV,GAAoB,OAApB;;AAEA,YAAQ,IAAR,CAAa,iBAAb,EAAgC,SAAhC;AACD;;AAED,MAAI,CAAC,UAAU,OAAV,CAAkB,MAAvB,EAA+B;AAAE;AAAS;;AAE1C,MAAI,cAAc,QAAlB;;AAEA,0BAAqB,UAAU,OAA/B,gBAAwC;AAAA;;AAAA,YAAnB,UAAU,OAAS;AAAA,QAA7B,MAA6B;;AACtC,QAAM,eAAe,OAAO,SAAP,CAAiB,OAAjB,CAAyB,YAA9C;;AAEA,QAAI,eAAe,WAAnB,EAAgC;AAC9B,oBAAc,YAAd;AACD;AACF;;AAED,QAAM,QAAN,GAAiB,WAAjB;AACA,QAAM,OAAN,GAAgB,WAAW,YAAY;AACrC,SAAK;AACH,8BADG;AAEH,8BAFG;AAGH,sBAHG;AAIH,kBAJG;AAKH,YAAM;AALH,KAAL;AAOD,GARe,EAQb,WARa,CAAhB;AASD,CA1CD;;AA4CA,YAAY,OAAZ,CAAoB,EAApB,CAAuB,IAAvB,EAA6B,iBAAkD;AAAA,MAA/C,WAA+C,SAA/C,WAA+C;AAAA,MAAlC,OAAkC,SAAlC,OAAkC;AAAA,MAAzB,KAAyB,SAAzB,KAAyB;AAAA,MAAlB,WAAkB,SAAlB,WAAkB;;AAC7E,MAAI,CAAC,YAAY,eAAjB,EAAkC;AAChC,SAAK,EAAE,wBAAF,EAAe,wBAAf,EAA4B,gBAA5B,EAAqC,YAArC,EAA4C,MAAM,KAAlD,EAAL;AACD;AACF,CAJD;;WAMyB,CAAC,IAAD,EAAO,QAAP,C;AAAzB,4CAA2C;AAAtC,MAAM,sBAAN;AACH,cAAY,OAAZ,CAAoB,EAApB,CAAuB,UAAvB,EAAmC,kBAAyC;AAAA,QAA7B,WAA6B,UAA7B,WAA6B;AAAA,QAAhB,YAAgB,UAAhB,YAAgB;;AAC1E,QAAI,YAAY,UAAZ,CAAuB,YAAvB,CAAJ,EAA0C;AACxC,mBAAa,YAAY,UAAZ,CAAuB,YAAvB,EAAqC,OAAlD;AACD;AACF,GAJD;AAKD;;AAED,SAAS,oBAAT,CAA+B,IAA/B,EAAqC;AACnC,SAAO,kBAAwD;AAAA,QAA5C,WAA4C,UAA5C,WAA4C;AAAA,QAA/B,OAA+B,UAA/B,OAA+B;AAAA,QAAtB,KAAsB,UAAtB,KAAsB;AAAA,QAAf,WAAe,UAAf,WAAe;;AAC7D,SAAK,EAAE,wBAAF,EAAe,wBAAf,EAA4B,gBAA5B,EAAqC,YAArC,EAA4C,UAA5C,EAAL;AACD,GAFD;AAGD;;AAED,KAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,cAAc,MAAlC,EAA0C,GAA1C,EAA+C;AAC7C,cAAY,OAAZ,CAAoB,EAApB,CAAuB,cAAc,CAAd,CAAvB,EAAyC,qBAAqB,aAAa,CAAb,CAArB,CAAzC;AACD;;AAED,YAAY,OAAZ,CAAoB,EAApB,CAAuB,KAAvB,EAA8B,UAAU,WAAV,EAAuB;AACnD,cAAY,OAAZ,GAAyB,IAAzB,CADmD,CACnB;AAChC,cAAY,OAAZ,GAAyB,CAAzB,CAFmD,CAEnB;AAChC,cAAY,UAAZ,GAAyB,EAAzB,CAHmD,CAGnB;AACjC,CAJD;;AAMA,SAAS,aAAT,GAAyB,cAAc,QAAvC;AACA,OAAO,OAAP,GAAiB,aAAjB;;;;;AClOA,IAAM,gBAAgB,QAAQ,QAAR,CAAtB;AACA,IAAM,cAAgB,QAAQ,gBAAR,CAAtB;;AAEA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,KAAzB,EAAgC,KAAhC;AACA,cAAc,OAAd,CAAsB,EAAtB,CAAyB,OAAzB,EAAkC,OAAlC;;WAEqB,CAAC,MAAD,EAAS,IAAT,EAAe,QAAf,EAAyB,QAAzB,C;AAArB,yCAAyD;AAApD,MAAM,iBAAN;AACH,cAAY,OAAZ,CAAoB,EAApB,CAAuB,MAAvB,EAA+B,aAA/B;AACD;;AAED,SAAS,KAAT,OAAkC;AAAA,MAAhB,YAAgB,QAAhB,YAAgB;;AAChC,MAAI,aAAa,IAAb,KAAsB,MAA1B,EAAkC;AAAE;AAAS;;AAE7C,eAAa,KAAb,GAAqB,CAAC,aAAa,KAAb,IAAsB,CAAvB,IAA4B,CAAjD;AACD;;AAED,SAAS,OAAT,QAAuE;AAAA,MAAnD,WAAmD,SAAnD,WAAmD;AAAA,MAAtC,YAAsC,SAAtC,YAAsC;AAAA,MAAxB,WAAwB,SAAxB,WAAwB;AAAA,MAAX,OAAW,SAAX,OAAW;;AACrE,MAAI,aAAa,IAAb,KAAsB,MAAtB,IAAgC,CAAC,QAAQ,MAA7C,EAAqD;AAAE;AAAS;;AAEhE;AACA,MAAM,WAAW,QAAQ,CAAR,EAAW,SAAX,CAAqB,OAArB,CAA6B,kBAA9C;;AAEA;AACA,MAAI,YAAY,CAAhB,EAAmB;AAAE;AAAS;;AAE9B;AACA,cAAY,kBAAZ,GAAiC,WAAW,YAAY;AACtD,kBAAc,IAAd,CAAmB;AACjB,8BADiB;AAEjB,8BAFiB;AAGjB,YAAM,MAHW;AAIjB,eAAS,YAJQ;AAKjB,aAAO;AALU,KAAnB;AAOD,GARgC,EAQ9B,QAR8B,CAAjC;AASD;;AAED,SAAS,aAAT,QAAyC;AAAA,MAAf,WAAe,SAAf,WAAe;;AACvC;AACA;AACA,MAAI,YAAY,kBAAhB,EAAoC;AAClC,kBAAc,YAAY,kBAA1B;AACA,gBAAY,kBAAZ,GAAiC,IAAjC;AACD;AACF;;AAED;AACA,cAAc,QAAd,CAAuB,kBAAvB,GAA4C,CAA5C;AACA,cAAc,KAAd,CAAoB,IAApB,CAAyB,YAAzB;;AAEA,OAAO,OAAP,GAAiB;AACf,cADe;AAEf,kBAFe;AAGf;AAHe,CAAjB;;;;;AClDA,IAAM,gBAAgB,QAAQ,QAAR,CAAtB;AACA,IAAM,eAAgB,QAAQ,iBAAR,CAAtB;AACA,IAAM,KAAgB,QAAQ,aAAR,CAAtB;AACA,IAAM,QAAgB,QAAQ,UAAR,CAAtB;AACA,IAAM,SAAgB,QAAQ,iBAAR,CAAtB;;eACsB,QAAQ,cAAR,C;IAAd,K,YAAA,K;;AAER,cAAc,OAAd,CAAsB,EAAtB,CAAyB,iBAAzB,EAA4C,gBAAmD;AAAA,MAAvC,OAAuC,QAAvC,OAAuC;AAAA,MAA9B,OAA8B,QAA9B,OAA8B;AAAA,MAArB,IAAqB,QAArB,IAAqB;AAAA,MAAf,WAAe,QAAf,WAAe;;AAC7F,QAAM,aAAN,CAAoB,YAApB,CAAiC,OAAjC,EAA0C,wBAAgB;AACxD,QAAM,YAAY,aAAa,MAA/B;AACA,QAAM,UAAU,UAAU,OAA1B;;AAEA,QAAI,UAAU,IAAV,KACC,GAAG,OAAH,CAAW,OAAX,CADD,IAEC,aAAa,eAAb,CAA6B,OAA7B,EAAsC,OAAtC,EAA+C,WAA/C,CAFL,EAEkE;;AAEhE,cAAQ,IAAR,CAAa;AACX,wBADW;AAEX,4BAFW;AAGX,eAAO,EAAE,0BAAF;AAHI,OAAb;AAKD;AACF,GAdD;AAeD,CAhBD;;AAkBA,aAAa,OAAb,CAAqB,EAArB,CAAwB,KAAxB,EAA+B,iBAA4B;AAAA,MAAhB,YAAgB,SAAhB,YAAgB;;AACzD,eAAa,MAAb,CAAoB,OAApB,GAA8B,UAAU,OAAV,EAAmB;AAC/C,WAAO,aAAa,OAAb,CAAqB,OAArB,CAAP;AACD,GAFD;AAGD,CAJD;;AAMA,aAAa,OAAb,CAAqB,EAArB,CAAwB,KAAxB,EAA+B,iBAAqC;AAAA,MAAzB,YAAyB,SAAzB,YAAyB;AAAA,MAAX,OAAW,SAAX,OAAW;;AAClE,SAAO,aAAa,MAAb,CAAoB,OAA3B,EAAoC,cAAc,QAAlD;AACA,SAAO,aAAa,MAAb,CAAoB,OAA3B,EAAoC,OAApC;AACD,CAHD;;AAKA,MAAM,aAAa,UAAnB,EAA+B,cAAc,KAA7C;;AAEA,aAAa,SAAb,CAAuB,aAAvB,GAAuC,UAAU,OAAV,EAAmB;AACxD,SAAO,KAAK,MAAL,CAAY,OAAnB,EAA4B,OAA5B;;AAEA,SAAO,IAAP;AACD,CAJD;;AAMA,IAAM,qBAAqB,aAAa,SAAb,CAAuB,iBAAlD;;AAEA,aAAa,SAAb,CAAuB,iBAAvB,GAA2C,UAAU,UAAV,EAAsB,QAAtB,EAAgC;AACzE,MAAM,MAAM,mBAAmB,IAAnB,CAAwB,IAAxB,EAA8B,UAA9B,EAA0C,QAA1C,CAAZ;;AAEA,MAAI,QAAQ,IAAZ,EAAkB;AAChB,SAAK,MAAL,CAAY,OAAZ,CAAoB,UAApB,IAAkC,QAAlC;AACD;;AAED,SAAO,GAAP;AACD,CARD;;AAUA,aAAa,eAAb,CAA6B,IAA7B,CAAkC,eAAlC;;;;;ACxDA,IAAM,QAAU,QAAQ,SAAR,CAAhB;AACA,IAAM,SAAU,QAAQ,gBAAR,CAAhB;AACA,IAAM,UAAU,QAAQ,iBAAR,EAA2B,GAA3B,EAAhB;;eAEsB,QAAQ,gBAAR,C;IAAd,S,YAAA,S;;AAER,IAAM,QAAQ;AACZ,kBADY;AAEZ,gBAFY;AAGZ,cAHY;;AAKZ;AACA,YAAU,QAAQ,oBAAR,EAA8B,QAN5B;AAOZ;AACA,aAAW,EARC;;AAUZ,eAAa,qBAAU,GAAV,EAAe,GAAf,EAAoB;AAC/B;AACA,QAAI,MAAM,QAAN,CAAe,MAAM,SAArB,EAAgC,GAAhC,CAAJ,EAA0C;AAAE,aAAO,KAAP;AAAe;;AAE3D,UAAM,OAAO,UAAU,GAAV,CAAb;;AAEA,UAAM,SAAN,CAAgB,IAAhB,CAAqB,GAArB;AACA,WAAO,SAAP,CAAiB,IAAjB,CAAsB,GAAtB;;AAEA;AACA;AACA,QAAI,QAAQ,MAAM,QAAlB,EAA4B;AAC1B,aAAO,GAAP,CAAW,GAAX,EAAgB,QAAhB,EAA0B,MAAM,cAAhC;AACD;;AAED,YAAQ,IAAR,CAAa,cAAb,EAA6B,EAAE,QAAF,EAAO,QAAP,EAA7B;AACD,GA1BW;;AA4BZ,kBAAgB,wBAAU,GAAV,EAAe,GAAf,EAAoB;AAClC,QAAM,QAAQ,MAAM,SAAN,CAAgB,OAAhB,CAAwB,GAAxB,CAAd;;AAEA,UAAM,OAAO,UAAU,GAAV,CAAb;;AAEA,WAAO,MAAP,CAAc,GAAd,EAAmB,QAAnB,EAA6B,MAAM,cAAnC;;AAEA,UAAM,SAAN,CAAgB,MAAhB,CAAuB,KAAvB,EAA8B,CAA9B;AACA,WAAO,SAAP,CAAiB,MAAjB,CAAwB,KAAxB,EAA+B,CAA/B;;AAEA,YAAQ,IAAR,CAAa,iBAAb,EAAgC,EAAE,QAAF,EAAO,QAAP,EAAhC;AACD,GAvCW;;AAyCZ,kBAAgB,0BAAY;AAC1B,UAAM,cAAN,CAAqB,KAAK,QAA1B,EAAoC,IAApC;AACD;AA3CW,CAAd;;AA8CA,OAAO,OAAP,GAAiB,KAAjB;;;;;;;ICpDM,O;AACJ,qBAAe;AAAA;;AACb,SAAK,SAAL,GAAiB;AACf;AADe,KAAjB;AAGD;;oBAED,E,eAAI,I,EAAM,Q,EAAU;AAClB,QAAI,CAAC,KAAK,SAAL,CAAe,IAAf,CAAL,EAA2B;AACzB,WAAK,SAAL,CAAe,IAAf,IAAuB,CAAC,QAAD,CAAvB;AACA;AACD;;AAED,SAAK,SAAL,CAAe,IAAf,EAAqB,IAArB,CAA0B,QAA1B;AACD,G;;oBAED,G,gBAAK,I,EAAM,Q,EAAU;AACnB,QAAI,CAAC,KAAK,SAAL,CAAe,IAAf,CAAL,EAA2B;AAAE;AAAS;;AAEtC,QAAM,QAAQ,KAAK,SAAL,CAAe,IAAf,EAAqB,OAArB,CAA6B,QAA7B,CAAd;;AAEA,QAAI,UAAU,CAAC,CAAf,EAAkB;AAChB,WAAK,SAAL,CAAe,IAAf,EAAqB,MAArB,CAA4B,KAA5B,EAAmC,CAAnC;AACD;AACF,G;;oBAED,I,iBAAM,I,EAAM,G,EAAK;AACf,QAAM,kBAAkB,KAAK,SAAL,CAAe,IAAf,CAAxB;;AAEA,QAAI,CAAC,eAAL,EAAsB;AAAE;AAAS;;AAEjC,0BAAuB,eAAvB,eAAwC;AAAA;;AAAA,aAAjB,eAAiB;AAAA,UAA7B,QAA6B;;AACtC,UAAI,SAAS,GAAT,EAAc,IAAd,MAAwB,KAA5B,EAAmC;AACjC;AACD;AACF;AACF,G;;;;;AAGH,QAAQ,GAAR,GAAc,YAAY;AACxB,SAAO,IAAI,OAAJ,EAAP;AACD,CAFD;;AAIA,OAAO,OAAP,GAAiB,OAAjB;;;;;AC3CA,SAAS,QAAT,CAAmB,KAAnB,EAA0B,MAA1B,EAAkC;AAChC,SAAO,MAAM,OAAN,CAAc,MAAd,MAA0B,CAAC,CAAlC;AACD;;AAED,SAAS,KAAT,CAAgB,MAAhB,EAAwB,MAAxB,EAAgC;AAC9B,wBAAmB,MAAnB,eAA2B;AAAA;;AAAA,WAAR,MAAQ;AAAA,QAAhB,IAAgB;;AACzB,WAAO,IAAP,CAAY,IAAZ;AACD;;AAED,SAAO,MAAP;AACD;;AAED,OAAO,OAAP,GAAiB;AACf,oBADe;AAEf;AAFe,CAAjB;;;;;eCZmB,QAAQ,UAAR,C;IAAX,M,YAAA,M;;AACR,IAAM,KAAS,QAAQ,MAAR,CAAf;AACA,IAAM,aAAa,QAAQ,cAAR,CAAnB;;AAEA,IAAM,UAAU,WAAW,OAA3B;AACA,IAAM,YAAa,OAAO,SAA1B;;AAEA,IAAM,UAAU;AACd;AACA,iBAAe,CAAC,EAAG,kBAAkB,MAAnB,IAA8B,GAAG,QAAH,CAAY,OAAO,aAAnB,KAC1B,WAAW,QAAX,YAA+B,OAAO,aAD5C,CAFF;;AAKd;AACA,wBAAsB,CAAC,CAAC,WAAW,YANrB;;AAQd,SAAQ,iBAAiB,IAAjB,CAAsB,UAAU,QAAhC,CARM;;AAUd;AACA,UAAS,iBAAiB,IAAjB,CAAsB,UAAU,QAAhC,KACG,YAAY,IAAZ,CAAiB,UAAU,UAA3B,CAZE;;AAcd,SAAO,SAAS,IAAT,CAAc,UAAU,SAAxB,CAdO;;AAgBd;AACA,2BAAyB,aAAa,QAAQ,SAArB,GACrB,SADqB,GACV,2BAA2B,QAAQ,SAAnC,GACX,uBADW,GACc,wBAAwB,QAAQ,SAAhC,GACzB,oBADyB,GACH,sBAAsB,QAAQ,SAA9B,GACtB,kBADsB,GACF,mBArBV;;AAuBd,eAAc,WAAW,YAAX,GACT,WAAW,YAAX,KAA4B,OAAO,cAAnC,GACC;AACA,QAAQ,aADR;AAEA,UAAQ,eAFR;AAGA,UAAQ,WAHR;AAIA,SAAQ,UAJR;AAKA,UAAQ,eALR;AAMA,YAAQ;AANR,GADD,GASC;AACA,QAAQ,WADR;AAEA,UAAQ,aAFR;AAGA,UAAQ,aAHR;AAIA,SAAQ,YAJR;AAKA,UAAQ,aALR;AAMA,YAAQ;AANR,GAVQ,GAkBV,IAzCU;;AA2Cd;AACA,cAAY,kBAAkB,WAAW,QAA7B,GAAuC,YAAvC,GAAqD;;AA5CnD,CAAhB;;AAgDA;AACA,QAAQ,aAAR,GAAyB,UAAU,OAAV,KAAsB,OAAtB,IACpB,QAAQ,aADY,IAEpB,UAAU,SAAV,CAAoB,KAApB,CAA0B,QAA1B,CAFL;;AAIA,OAAO,OAAP,GAAiB,OAAjB;;;;;AC5DA,IAAM,KAAK,QAAQ,MAAR,CAAX;;AAEA,OAAO,OAAP,GAAiB,SAAS,KAAT,CAAgB,MAAhB,EAAwB;AACvC,MAAM,OAAO,EAAb;AACA,OAAK,IAAM,IAAX,IAAmB,MAAnB,EAA2B;AACzB,QAAI,GAAG,WAAH,CAAe,OAAO,IAAP,CAAf,CAAJ,EAAkC;AAChC,WAAK,IAAL,IAAa,MAAM,OAAO,IAAP,CAAN,CAAb;AACD,KAFD,MAEO;AACL,WAAK,IAAL,IAAa,OAAO,IAAP,CAAb;AACD;AACF;AACD,SAAO,IAAP;AACD,CAVD;;;;;ACFA,IAAM,aAAa,EAAnB;AACA,IAAM,MAAM,QAAQ,UAAR,EAAoB,MAAhC;;AAEA,SAAS,KAAT,GAAkB,CAAE;;AAEpB,WAAW,QAAX,GAAgC,IAAI,QAApC;AACA,WAAW,gBAAX,GAAgC,IAAI,gBAAJ,IAA0B,KAA1D;AACA,WAAW,UAAX,GAAgC,IAAI,UAAJ,IAA0B,KAA1D;AACA,WAAW,aAAX,GAAgC,IAAI,aAAJ,IAA0B,KAA1D;AACA,WAAW,kBAAX,GAAgC,IAAI,kBAAJ,IAA0B,KAA1D;AACA,WAAW,OAAX,GAAgC,IAAI,OAAJ,IAA0B,KAA1D;AACA,WAAW,WAAX,GAAgC,IAAI,WAAJ,IAA0B,WAAW,OAArE;;AAEA,WAAW,KAAX,GAA0B,IAAI,KAA9B;AACA,WAAW,KAAX,GAA0B,IAAI,KAAJ,IAAa,KAAvC;AACA,WAAW,YAAX,GAA2B,IAAI,YAAJ,IAAoB,IAAI,cAAnD;;AAEA,OAAO,OAAP,GAAiB,UAAjB;;;;;ACjBA,IAAM,MAAa,QAAQ,UAAR,CAAnB;AACA,IAAM,UAAa,QAAQ,WAAR,CAAnB;AACA,IAAM,KAAa,QAAQ,MAAR,CAAnB;AACA,IAAM,aAAa,QAAQ,cAAR,CAAnB;;AAEA,IAAM,WAAW;AACf,gBAAc,sBAAU,MAAV,EAAkB,KAAlB,EAAyB;AACrC,WAAO,KAAP,EAAc;AACZ,UAAI,UAAU,MAAd,EAAsB;AACpB,eAAO,IAAP;AACD;;AAED,cAAQ,MAAM,UAAd;AACD;;AAED,WAAO,KAAP;AACD,GAXc;;AAaf,WAAS,iBAAU,OAAV,EAAmB,QAAnB,EAA6B;AACpC,WAAO,GAAG,OAAH,CAAW,OAAX,CAAP,EAA4B;AAC1B,UAAI,SAAS,eAAT,CAAyB,OAAzB,EAAkC,QAAlC,CAAJ,EAAiD;AAAE,eAAO,OAAP;AAAiB;;AAEpE,gBAAU,SAAS,UAAT,CAAoB,OAApB,CAAV;AACD;;AAED,WAAO,IAAP;AACD,GArBc;;AAuBf,cAAY,oBAAU,IAAV,EAAgB;AAC1B,QAAI,SAAS,KAAK,UAAlB;;AAEA,QAAI,GAAG,OAAH,CAAW,MAAX,CAAJ,EAAwB;AACtB;AACA,aAAO,CAAC,SAAS,OAAO,IAAjB,KAA0B,GAAG,OAAH,CAAW,MAAX,CAAjC,EAAqD;AACnD;AACD;;AAED,aAAO,MAAP;AACD;;AAED,WAAO,MAAP;AACD,GApCc;;AAsCf,mBAAiB,yBAAU,OAAV,EAAmB,QAAnB,EAA6B;AAC5C;AACA,QAAI,IAAI,MAAJ,KAAe,IAAI,UAAvB,EAAmC;AACjC,iBAAW,SAAS,OAAT,CAAiB,WAAjB,EAA8B,GAA9B,CAAX;AACD;;AAED,WAAO,QAAQ,QAAQ,uBAAhB,EAAyC,QAAzC,CAAP;AACD,GA7Cc;;AA+Cf;AACA,yBAAuB,+BAAU,QAAV,EAAoB;AACzC,QAAI,qBAAqB,EAAzB;AACA,QAAI,kBAAkB,EAAtB;AACA,QAAI,iBAAJ;AACA,QAAI,cAAc,SAAS,CAAT,CAAlB;AACA,QAAI,QAAQ,cAAa,CAAb,GAAgB,CAAC,CAA7B;AACA,QAAI,eAAJ;AACA,QAAI,cAAJ;AACA,QAAI,UAAJ;AACA,QAAI,UAAJ;;AAEA,SAAK,IAAI,CAAT,EAAY,IAAI,SAAS,MAAzB,EAAiC,GAAjC,EAAsC;AACpC,iBAAW,SAAS,CAAT,CAAX;;AAEA;AACA,UAAI,CAAC,QAAD,IAAa,aAAa,WAA9B,EAA2C;AACzC;AACD;;AAED,UAAI,CAAC,WAAL,EAAkB;AAChB,sBAAc,QAAd;AACA,gBAAQ,CAAR;AACA;AACD;;AAED;AACA;AACA,UAAI,SAAS,UAAT,KAAwB,SAAS,aAArC,EAAoD;AAClD;AACD;AACD;AAHA,WAIK,IAAI,YAAY,UAAZ,KAA2B,SAAS,aAAxC,EAAuD;AAC1D,wBAAc,QAAd;AACA,kBAAQ,CAAR;AACA;AACD;;AAED,UAAI,CAAC,mBAAmB,MAAxB,EAAgC;AAC9B,iBAAS,WAAT;AACA,eAAO,OAAO,UAAP,IAAqB,OAAO,UAAP,KAAsB,OAAO,aAAzD,EAAwE;AACtE,6BAAmB,OAAnB,CAA2B,MAA3B;AACA,mBAAS,OAAO,UAAhB;AACD;AACF;;AAED;AACA;AACA,UAAI,uBAAuB,WAAW,WAAlC,IACG,oBAAoB,WAAW,UADlC,IAEG,EAAE,oBAAoB,WAAW,aAAjC,CAFP,EAEwD;;AAEtD,YAAI,aAAa,YAAY,UAA7B,EAAyC;AACvC;AACD;;AAED,iBAAS,SAAS,eAAlB;AACD,OATD,MAUK;AACH,iBAAS,QAAT;AACD;;AAED,wBAAkB,EAAlB;;AAEA,aAAO,OAAO,UAAP,KAAsB,OAAO,aAApC,EAAmD;AACjD,wBAAgB,OAAhB,CAAwB,MAAxB;AACA,iBAAS,OAAO,UAAhB;AACD;;AAED,UAAI,CAAJ;;AAEA;AACA,aAAO,gBAAgB,CAAhB,KAAsB,gBAAgB,CAAhB,MAAuB,mBAAmB,CAAnB,CAApD,EAA2E;AACzE;AACD;;AAED,UAAM,UAAU,CACd,gBAAgB,IAAI,CAApB,CADc,EAEd,gBAAgB,CAAhB,CAFc,EAGd,mBAAmB,CAAnB,CAHc,CAAhB;;AAMA,cAAQ,QAAQ,CAAR,EAAW,SAAnB;;AAEA,aAAO,KAAP,EAAc;AACZ,YAAI,UAAU,QAAQ,CAAR,CAAd,EAA0B;AACxB,wBAAc,QAAd;AACA,kBAAQ,CAAR;AACA,+BAAqB,EAArB;;AAEA;AACD,SAND,MAOK,IAAI,UAAU,QAAQ,CAAR,CAAd,EAA0B;AAC7B;AACD;;AAED,gBAAQ,MAAM,eAAd;AACD;AACF;;AAED,WAAO,KAAP;AACD,GApJc;;AAsJf,eAAa,qBAAU,OAAV,EAAmB,QAAnB,EAA6B,KAA7B,EAAoC;AAC/C,WAAO,GAAG,OAAH,CAAW,OAAX,CAAP,EAA4B;AAC1B,UAAI,SAAS,eAAT,CAAyB,OAAzB,EAAkC,QAAlC,CAAJ,EAAiD;AAC/C,eAAO,IAAP;AACD;;AAED,gBAAU,SAAS,UAAT,CAAoB,OAApB,CAAV;;AAEA,UAAI,YAAY,KAAhB,EAAuB;AACrB,eAAO,SAAS,eAAT,CAAyB,OAAzB,EAAkC,QAAlC,CAAP;AACD;AACF;;AAED,WAAO,KAAP;AACD,GApKc;;AAsKf,oBAAkB,0BAAU,OAAV,EAAmB;AACnC,WAAQ,mBAAmB,WAAW,kBAA9B,GACJ,QAAQ,uBADJ,GAEJ,OAFJ;AAGD,GA1Kc;;AA4Kf,eAAa,qBAAU,cAAV,EAA0B;AACrC,qBAAiB,kBAAkB,IAAI,MAAvC;AACA,WAAO;AACL,SAAG,eAAe,OAAf,IAA0B,eAAe,QAAf,CAAwB,eAAxB,CAAwC,UADhE;AAEL,SAAG,eAAe,OAAf,IAA0B,eAAe,QAAf,CAAwB,eAAxB,CAAwC;AAFhE,KAAP;AAID,GAlLc;;AAoLf,wBAAsB,8BAAU,OAAV,EAAmB;AACvC,QAAM,aAAc,mBAAmB,WAAW,UAA9B,GAChB,QAAQ,qBAAR,EADgB,GAEhB,QAAQ,cAAR,GAAyB,CAAzB,CAFJ;;AAIA,WAAO,cAAc;AACnB,YAAQ,WAAW,IADA;AAEnB,aAAQ,WAAW,KAFA;AAGnB,WAAQ,WAAW,GAHA;AAInB,cAAQ,WAAW,MAJA;AAKnB,aAAQ,WAAW,KAAX,IAAqB,WAAW,KAAX,GAAoB,WAAW,IALzC;AAMnB,cAAQ,WAAW,MAAX,IAAqB,WAAW,MAAX,GAAoB,WAAW;AANzC,KAArB;AAQD,GAjMc;;AAmMf,kBAAgB,wBAAU,OAAV,EAAmB;AACjC,QAAM,aAAa,SAAS,oBAAT,CAA8B,OAA9B,CAAnB;;AAEA,QAAI,CAAC,QAAQ,MAAT,IAAmB,UAAvB,EAAmC;AACjC,UAAM,SAAS,SAAS,WAAT,CAAqB,IAAI,SAAJ,CAAc,OAAd,CAArB,CAAf;;AAEA,iBAAW,IAAX,IAAqB,OAAO,CAA5B;AACA,iBAAW,KAAX,IAAqB,OAAO,CAA5B;AACA,iBAAW,GAAX,IAAqB,OAAO,CAA5B;AACA,iBAAW,MAAX,IAAqB,OAAO,CAA5B;AACD;;AAED,WAAO,UAAP;AACD,GAhNc;;AAkNf,WAAS,iBAAU,OAAV,EAAmB;AAC1B,QAAM,OAAO,EAAb;;AAEA,WAAO,OAAP,EAAgB;AACd,WAAK,IAAL,CAAU,OAAV;AACA,gBAAU,SAAS,UAAT,CAAoB,OAApB,CAAV;AACD;;AAED,WAAO,IAAP;AACD,GA3Nc;;AA6Nf,eAAa,4BAAS;AACpB,QAAI,CAAC,GAAG,MAAH,CAAU,KAAV,CAAL,EAAuB;AAAE,aAAO,KAAP;AAAe;;AAExC;AACA,eAAW,QAAX,CAAoB,aAApB,CAAkC,KAAlC;AACA,WAAO,IAAP;AACD;AAnOc,CAAjB;;AAsOA,OAAO,OAAP,GAAiB,QAAjB;;;;;AC3OA,IAAM,KAAe,QAAQ,MAAR,CAArB;AACA,IAAM,WAAe,QAAQ,YAAR,CAArB;AACA,IAAM,eAAe,QAAQ,gBAAR,CAArB;AACA,IAAM,UAAe,QAAQ,iBAAR,CAArB;;eAEqB,QAAQ,UAAR,C;IAAb,M,YAAA,M;;gBACa,QAAQ,OAAR,C;IAAb,Q,aAAA,Q;;AAER,IAAM,WAAW,EAAjB;AACA,IAAM,UAAW,EAAjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM,kBAAkB,EAAxB;AACA,IAAM,YAAkB,EAAxB;;AAEA,IAAM,kBAAmB,YAAM;AAC7B,MAAI,YAAY,KAAhB;;AAEA,SAAO,QAAP,CAAgB,aAAhB,CAA8B,KAA9B,EAAqC,gBAArC,CAAsD,MAAtD,EAA8D,IAA9D,EAAoE;AAClE,QAAI,OAAJ,GAAe;AAAE,kBAAY,IAAZ;AAAmB;AAD8B,GAApE;;AAIA,SAAO,SAAP;AACD,CARuB,EAAxB;;AAUA,SAAS,GAAT,CAAc,OAAd,EAAuB,IAAvB,EAA6B,QAA7B,EAAuC,WAAvC,EAAoD;AAClD,MAAM,UAAU,WAAW,WAAX,CAAhB;AACA,MAAI,eAAe,SAAS,OAAT,CAAiB,OAAjB,CAAnB;AACA,MAAI,SAAS,QAAQ,YAAR,CAAb;;AAEA,MAAI,CAAC,MAAL,EAAa;AACX,aAAS;AACP,cAAQ,EADD;AAEP,iBAAW;AAFJ,KAAT;;AAKA,mBAAe,SAAS,IAAT,CAAc,OAAd,IAAyB,CAAxC;AACA,YAAQ,IAAR,CAAa,MAAb;AACD;;AAED,MAAI,CAAC,OAAO,MAAP,CAAc,IAAd,CAAL,EAA0B;AACxB,WAAO,MAAP,CAAc,IAAd,IAAsB,EAAtB;AACA,WAAO,SAAP;AACD;;AAED,MAAI,CAAC,SAAS,OAAO,MAAP,CAAc,IAAd,CAAT,EAA8B,QAA9B,CAAL,EAA8C;AAC5C,YAAQ,gBAAR,CAAyB,IAAzB,EAA+B,QAA/B,EAAyC,kBAAiB,OAAjB,GAA2B,CAAC,CAAC,QAAQ,OAA9E;AACA,WAAO,MAAP,CAAc,IAAd,EAAoB,IAApB,CAAyB,QAAzB;AACD;AACF;;AAED,SAAS,MAAT,CAAiB,OAAjB,EAA0B,IAA1B,EAAgC,QAAhC,EAA0C,WAA1C,EAAuD;AACrD,MAAM,UAAU,WAAW,WAAX,CAAhB;AACA,MAAM,eAAe,SAAS,OAAT,CAAiB,OAAjB,CAArB;AACA,MAAM,SAAS,QAAQ,YAAR,CAAf;;AAEA,MAAI,CAAC,MAAD,IAAW,CAAC,OAAO,MAAvB,EAA+B;AAC7B;AACD;;AAED,MAAI,SAAS,KAAb,EAAoB;AAClB,SAAK,IAAL,IAAa,OAAO,MAApB,EAA4B;AAC1B,UAAI,OAAO,MAAP,CAAc,cAAd,CAA6B,IAA7B,CAAJ,EAAwC;AACtC,eAAO,OAAP,EAAgB,IAAhB,EAAsB,KAAtB;AACD;AACF;AACD;AACD;;AAED,MAAI,OAAO,MAAP,CAAc,IAAd,CAAJ,EAAyB;AACvB,QAAM,MAAM,OAAO,MAAP,CAAc,IAAd,EAAoB,MAAhC;;AAEA,QAAI,aAAa,KAAjB,EAAwB;AACtB,WAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,GAApB,EAAyB,GAAzB,EAA8B;AAC5B,eAAO,OAAP,EAAgB,IAAhB,EAAsB,OAAO,MAAP,CAAc,IAAd,EAAoB,CAApB,CAAtB,EAA8C,OAA9C;AACD;AACD;AACD,KALD,MAMK;AACH,WAAK,IAAI,KAAI,CAAb,EAAgB,KAAI,GAApB,EAAyB,IAAzB,EAA8B;AAC5B,YAAI,OAAO,MAAP,CAAc,IAAd,EAAoB,EAApB,MAA2B,QAA/B,EAAyC;AACvC,kBAAQ,mBAAR,QAAiC,IAAjC,EAAyC,QAAzC,EAAmD,kBAAiB,OAAjB,GAA2B,CAAC,CAAC,QAAQ,OAAxF;AACA,iBAAO,MAAP,CAAc,IAAd,EAAoB,MAApB,CAA2B,EAA3B,EAA8B,CAA9B;;AAEA;AACD;AACF;AACF;;AAED,QAAI,OAAO,MAAP,CAAc,IAAd,KAAuB,OAAO,MAAP,CAAc,IAAd,EAAoB,MAApB,KAA+B,CAA1D,EAA6D;AAC3D,aAAO,MAAP,CAAc,IAAd,IAAsB,IAAtB;AACA,aAAO,SAAP;AACD;AACF;;AAED,MAAI,CAAC,OAAO,SAAZ,EAAuB;AACrB,YAAQ,MAAR,CAAe,YAAf,EAA6B,CAA7B;AACA,aAAS,MAAT,CAAgB,YAAhB,EAA8B,CAA9B;AACD;AACF;;AAED,SAAS,WAAT,CAAsB,QAAtB,EAAgC,OAAhC,EAAyC,IAAzC,EAA+C,QAA/C,EAAyD,WAAzD,EAAsE;AACpE,MAAM,UAAU,WAAW,WAAX,CAAhB;AACA,MAAI,CAAC,gBAAgB,IAAhB,CAAL,EAA4B;AAC1B,oBAAgB,IAAhB,IAAwB;AACtB,iBAAW,EADW;AAEtB,gBAAW,EAFW;AAGtB,iBAAW;AAHW,KAAxB;;AAMA;AACA,4BAAkB,SAAlB,gBAA6B;AAAxB,UAAM,MAAO,SAAP,KAAN;AACH,UAAI,GAAJ,EAAS,IAAT,EAAe,gBAAf;AACA,UAAI,GAAJ,EAAS,IAAT,EAAe,kBAAf,EAAmC,IAAnC;AACD;AACF;;AAED,MAAM,YAAY,gBAAgB,IAAhB,CAAlB;AACA,MAAI,cAAJ;;AAEA,OAAK,QAAQ,UAAU,SAAV,CAAoB,MAApB,GAA6B,CAA1C,EAA6C,SAAS,CAAtD,EAAyD,OAAzD,EAAkE;AAChE,QAAI,UAAU,SAAV,CAAoB,KAApB,MAA+B,QAA/B,IACG,UAAU,QAAV,CAAmB,KAAnB,MAA8B,OADrC,EAC8C;AAC5C;AACD;AACF;;AAED,MAAI,UAAU,CAAC,CAAf,EAAkB;AAChB,YAAQ,UAAU,SAAV,CAAoB,MAA5B;;AAEA,cAAU,SAAV,CAAoB,IAApB,CAAyB,QAAzB;AACA,cAAU,QAAV,CAAoB,IAApB,CAAyB,OAAzB;AACA,cAAU,SAAV,CAAoB,IAApB,CAAyB,EAAzB;AACD;;AAED;AACA,YAAU,SAAV,CAAoB,KAApB,EAA2B,IAA3B,CAAgC,CAAC,QAAD,EAAW,CAAC,CAAC,QAAQ,OAArB,EAA8B,QAAQ,OAAtC,CAAhC;AACD;;AAED,SAAS,cAAT,CAAyB,QAAzB,EAAmC,OAAnC,EAA4C,IAA5C,EAAkD,QAAlD,EAA4D,WAA5D,EAAyE;AACvE,MAAM,UAAU,WAAW,WAAX,CAAhB;AACA,MAAM,YAAY,gBAAgB,IAAhB,CAAlB;AACA,MAAI,aAAa,KAAjB;AACA,MAAI,cAAJ;;AAEA,MAAI,CAAC,SAAL,EAAgB;AAAE;AAAS;;AAE3B;AACA,OAAK,QAAQ,UAAU,SAAV,CAAoB,MAApB,GAA6B,CAA1C,EAA6C,SAAS,CAAtD,EAAyD,OAAzD,EAAkE;AAChE;AACA,QAAI,UAAU,SAAV,CAAoB,KAApB,MAA+B,QAA/B,IACG,UAAU,QAAV,CAAmB,KAAnB,MAA8B,OADrC,EAC8C;;AAE5C,UAAM,YAAY,UAAU,SAAV,CAAoB,KAApB,CAAlB;;AAEA;AACA,WAAK,IAAI,IAAI,UAAU,MAAV,GAAmB,CAAhC,EAAmC,KAAK,CAAxC,EAA2C,GAA3C,EAAgD;AAAA,2BACf,UAAU,CAAV,CADe;AAAA,YACvC,EADuC;AAAA,YACnC,OADmC;AAAA,YAC1B,OAD0B;;AAG9C;;AACA,YAAI,OAAO,QAAP,IAAmB,YAAY,CAAC,CAAC,QAAQ,OAAzC,IAAoD,YAAY,QAAQ,OAA5E,EAAqF;AACnF;AACA,oBAAU,MAAV,CAAiB,CAAjB,EAAoB,CAApB;;AAEA;AACA;AACA,cAAI,CAAC,UAAU,MAAf,EAAuB;AACrB,sBAAU,SAAV,CAAoB,MAApB,CAA2B,KAA3B,EAAkC,CAAlC;AACA,sBAAU,QAAV,CAAoB,MAApB,CAA2B,KAA3B,EAAkC,CAAlC;AACA,sBAAU,SAAV,CAAoB,MAApB,CAA2B,KAA3B,EAAkC,CAAlC;;AAEA;AACA,mBAAO,OAAP,EAAgB,IAAhB,EAAsB,gBAAtB;AACA,mBAAO,OAAP,EAAgB,IAAhB,EAAsB,kBAAtB,EAA0C,IAA1C;;AAEA;AACA,gBAAI,CAAC,UAAU,SAAV,CAAoB,MAAzB,EAAiC;AAC/B,8BAAgB,IAAhB,IAAwB,IAAxB;AACD;AACF;;AAED;AACA,uBAAa,IAAb;AACA;AACD;AACF;;AAED,UAAI,UAAJ,EAAgB;AAAE;AAAQ;AAC3B;AACF;AACF;;AAED;AACA;AACA,SAAS,gBAAT,CAA2B,KAA3B,EAAkC,WAAlC,EAA+C;AAC7C,MAAM,UAAU,WAAW,WAAX,CAAhB;AACA,MAAM,YAAY,EAAlB;AACA,MAAM,YAAY,gBAAgB,MAAM,IAAtB,CAAlB;;AAH6C,8BAItB,aAAa,eAAb,CAA6B,KAA7B,CAJsB;AAAA,MAItC,WAJsC;;AAK7C,MAAI,UAAU,WAAd;;AAEA;AACA,UAAQ,SAAR,EAAmB,KAAnB;;AAEA,YAAU,aAAV,GAA0B,KAA1B;AACA,YAAU,cAAV,GAA2B,sBAA3B;;AAEA;AACA,SAAO,GAAG,OAAH,CAAW,OAAX,CAAP,EAA4B;AAC1B,SAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,UAAU,SAAV,CAAoB,MAAxC,EAAgD,GAAhD,EAAqD;AACnD,UAAM,WAAW,UAAU,SAAV,CAAoB,CAApB,CAAjB;AACA,UAAM,UAAU,UAAU,QAAV,CAAmB,CAAnB,CAAhB;;AAEA,UAAI,SAAS,eAAT,CAAyB,OAAzB,EAAkC,QAAlC,KACG,SAAS,YAAT,CAAsB,OAAtB,EAA+B,WAA/B,CADH,IAEG,SAAS,YAAT,CAAsB,OAAtB,EAA+B,OAA/B,CAFP,EAEgD;;AAE9C,YAAM,YAAY,UAAU,SAAV,CAAoB,CAApB,CAAlB;;AAEA,kBAAU,aAAV,GAA0B,OAA1B;;AAEA,aAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,UAAU,MAA9B,EAAsC,GAAtC,EAA2C;AAAA,6BACV,UAAU,CAAV,CADU;AAAA,cAClC,EADkC;AAAA,cAC9B,OAD8B;AAAA,cACrB,OADqB;;;AAGzC,cAAI,YAAY,CAAC,CAAC,QAAQ,OAAtB,IAAiC,YAAY,QAAQ,OAAzD,EAAkE;AAChE,eAAG,SAAH;AACD;AACF;AACF;AACF;;AAED,cAAU,SAAS,UAAT,CAAoB,OAApB,CAAV;AACD;AACF;;AAED,SAAS,kBAAT,CAA6B,KAA7B,EAAoC;AAClC,SAAO,iBAAiB,IAAjB,CAAsB,IAAtB,EAA4B,KAA5B,EAAmC,IAAnC,CAAP;AACD;;AAED,SAAS,sBAAT,GAAmC;AACjC,OAAK,aAAL,CAAmB,cAAnB;AACD;;AAED,SAAS,UAAT,CAAqB,KAArB,EAA4B;AAC1B,SAAO,GAAG,MAAH,CAAU,KAAV,IAAkB,KAAlB,GAA0B,EAAE,SAAS,KAAX,EAAjC;AACD;;AAED,OAAO,OAAP,GAAiB;AACf,UADe;AAEf,gBAFe;;AAIf,0BAJe;AAKf,gCALe;;AAOf,oCAPe;AAQf,wCARe;AASf,kCATe;AAUf,sBAVe;;AAYf,kCAZe;;AAcf,aAAW,QAdI;AAef,YAAU;AAfK,CAAjB;;;;;AC7PA,OAAO,OAAP,GAAiB,SAAS,MAAT,CAAiB,IAAjB,EAAuB,MAAvB,EAA+B;AAC9C,OAAK,IAAM,IAAX,IAAmB,MAAnB,EAA2B;AACzB,SAAK,IAAL,IAAa,OAAO,IAAP,CAAb;AACD;AACD,SAAO,IAAP;AACD,CALD;;;;;eCGI,QAAQ,QAAR,C;IAFF,e,YAAA,e;IACA,Q,YAAA,Q;;AAGF,OAAO,OAAP,GAAiB,UAAU,MAAV,EAAkB,OAAlB,EAA2B,MAA3B,EAAmC;AAClD,MAAM,gBAAgB,OAAO,OAAP,CAAe,MAAf,CAAtB;AACA,MAAM,eAAe,iBAAiB,cAAc,MAApD;AACA,MAAM,SAAS,gBAAgB,OAAO,OAAP,CAAe,MAA9C;;AAEA,MAAM,aAAa,gBAAgB,MAAhB,EAAwB,MAAxB,EAAgC,OAAhC,EAAyC,CAAC,UAAU,OAAX,CAAzC,CAAnB;;AAEA,SAAO,SAAS,UAAT,KAAwB,EAAE,GAAG,CAAL,EAAQ,GAAG,CAAX,EAA/B;AACD,CARD;;;;;ACLA,OAAO,OAAP,GAAiB,UAAC,CAAD,EAAI,CAAJ;AAAA,SAAW,KAAK,IAAL,CAAU,IAAI,CAAJ,GAAQ,IAAI,CAAtB,CAAX;AAAA,CAAjB;;;;;ACAA,IAAM,SAAS,QAAQ,UAAR,CAAf;AACA,IAAM,MAAS,QAAQ,UAAR,CAAf;;AAEA,IAAM,QAAQ;AACZ,YAAU,kBAAU,MAAV,EAAkB,OAAlB,EAA2B;AACnC,QAAI,SAAS,KAAb;;AAEA,WAAO,YAAY;AACjB,UAAI,CAAC,MAAL,EAAa;AACX,YAAI,MAAJ,CAAW,OAAX,CAAmB,IAAnB,CAAwB,OAAxB;AACA,iBAAS,IAAT;AACD;;AAED,aAAO,OAAO,KAAP,CAAa,IAAb,EAAmB,SAAnB,CAAP;AACD,KAPD;AAQD,GAZW;;AAcZ;AACA,oBAAkB,0BAAU,CAAV,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB;AACzC,QAAM,KAAK,IAAI,CAAf;AACA,WAAO,KAAK,EAAL,GAAU,EAAV,GAAe,IAAI,EAAJ,GAAS,CAAT,GAAa,EAA5B,GAAiC,IAAI,CAAJ,GAAQ,EAAhD;AACD,GAlBW;;AAoBZ,0BAAwB,gCAAU,MAAV,EAAkB,MAAlB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,IAApC,EAA0C,IAA1C,EAAgD,QAAhD,EAA0D;AAChF,WAAO;AACL,SAAI,MAAM,gBAAN,CAAuB,QAAvB,EAAiC,MAAjC,EAAyC,GAAzC,EAA8C,IAA9C,CADC;AAEL,SAAI,MAAM,gBAAN,CAAuB,QAAvB,EAAiC,MAAjC,EAAyC,GAAzC,EAA8C,IAA9C;AAFC,KAAP;AAID,GAzBW;;AA2BZ;AACA,eAAa,qBAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB;AACjC,SAAK,CAAL;AACA,WAAO,CAAC,CAAD,GAAK,CAAL,IAAQ,IAAE,CAAV,IAAe,CAAtB;AACD,GA/BW;;AAiCZ,cAAY,oBAAU,IAAV,EAAgB,GAAhB,EAAqB;AAC/B,SAAK,IAAL,GAAa,IAAI,IAAjB;AACA,SAAK,IAAL,GAAa,IAAI,IAAjB;AACA,SAAK,KAAL,GAAa,IAAI,KAAjB;;AAEA,WAAO,IAAP;AACD,GAvCW;;AAyCZ,MAAa,QAAQ,MAAR,CAzCD;AA0CZ,UAAa,MA1CD;AA2CZ,SAAa,QAAQ,SAAR,CA3CD;AA4CZ,eAAa,QAAQ,eAAR;AA5CD,CAAd;;AA+CA,OAAO,KAAP,EAAc,QAAQ,OAAR,CAAd;AACA,OAAO,KAAP,EAAc,QAAQ,YAAR,CAAd;AACA,OAAO,KAAP,EAAc,QAAQ,gBAAR,CAAd;AACA,OAAO,KAAP,EAAc,QAAQ,QAAR,CAAd;;AAEA,OAAO,OAAP,GAAiB,KAAjB;;;;;ACvDA,IAAM,QAAU,QAAQ,UAAR,CAAhB;AACA,IAAM,QAAU,QAAQ,SAAR,CAAhB;;AAEA,IAAM,SAAS;AACb,eAAa,CAAE,kBAAF,EAAsB,YAAtB,EAAoC,YAApC,EAAkD,MAAlD,CADA;;AAGb,UAAQ,gBAAU,OAAV,EAAmB,SAAnB,EAA8B,WAA9B,EAA2C;AACjD,QAAM,cAAc,MAAM,cAAN,CAAqB,OAArB,CAApB;AACA,QAAM,YAAY,MAAM,YAAN,CAAmB,OAAnB,CAAlB;AACA,QAAM,UAAU,EAAE,gBAAF,EAAW,oBAAX,EAAsB,wBAAtB,EAAmC,oBAAnC,EAA8C,wBAA9C,EAAhB;;AAEA,0BAAqB,OAAO,WAA5B,eAAyC;AAAA;;AAAA,aAApB,OAAO,WAAa;AAAA,UAA9B,MAA8B;;AACvC,UAAM,cAAc,OAAO,MAAP,EAAe,OAAf,CAApB;;AAEA,UAAI,WAAJ,EAAiB;AACf,eAAO,WAAP;AACD;AACF;AACF,GAfY;;AAiBb;AACA,oBAAkB,iCAAmD;AAAA,QAAvC,WAAuC,SAAvC,WAAuC;AAAA,QAA1B,SAA0B,SAA1B,SAA0B;AAAA,QAAf,WAAe,SAAf,WAAe;;AACnE,QAAI,CAAC,cAAc,IAAd,CAAmB,SAAnB,CAAL,EAAoC;AAClC,aAAO,IAAP;AACD;;AAED,4BAA0B,MAAM,YAAhC,gBAA8C;AAAA;;AAAA,cAApB,MAAM,YAAc;AAAA,UAAnC,WAAmC;;AAC5C,UAAI,UAAU,WAAd;;AAEA,UAAI,YAAY,UAAZ,IAA0B,YAAY,UAAZ,CAAuB,WAAjD,IACI,YAAY,WAAZ,KAA4B,WADpC,EACkD;AAChD,eAAO,OAAP,EAAgB;AACd;AACA,cAAI,YAAY,YAAY,OAA5B,EAAqC;AACnC,mBAAO,WAAP;AACD;AACD,oBAAU,MAAM,UAAN,CAAiB,OAAjB,CAAV;AACD;AACF;AACF;;AAED,WAAO,IAAP;AACD,GAvCY;;AAyCb;AACA,cAAY,2BAAiD;AAAA,QAArC,SAAqC,SAArC,SAAqC;AAAA,QAA1B,WAA0B,SAA1B,WAA0B;AAAA,QAAb,SAAa,SAAb,SAAa;;AAC3D,QAAI,gBAAgB,OAAhB,IAA2B,gBAAgB,KAA/C,EAAsD;AACpD,aAAO,IAAP;AACD;;AAED,QAAI,uBAAJ;;AAEA,4BAA0B,MAAM,YAAhC,gBAA8C;AAAA;;AAAA,cAApB,MAAM,YAAc;AAAA,UAAnC,WAAmC;;AAC5C,UAAI,YAAY,WAAZ,KAA4B,WAAhC,EAA6C;AAC3C;AACA,YAAI,YAAY,UAAZ,IAA0B,CAAC,MAAM,QAAN,CAAe,YAAY,UAA3B,EAAuC,SAAvC,CAA/B,EAAkF;AAAE;AAAW;;AAE/F;AACA,YAAI,YAAY,WAAZ,EAAJ,EAA+B;AAC7B,iBAAO,WAAP;AACD;AACD;AAHA,aAIK,IAAI,CAAC,cAAL,EAAqB;AACxB,6BAAiB,WAAjB;AACD;AACF;AACF;;AAED;AACA;AACA,QAAI,cAAJ,EAAoB;AAClB,aAAO,cAAP;AACD;;AAED;AACA;AACA;AACA,4BAA0B,MAAM,YAAhC,gBAA8C;AAAA;;AAAA,cAApB,MAAM,YAAc;AAAA,UAAnC,YAAmC;;AAC5C,UAAI,aAAY,WAAZ,KAA4B,WAA5B,IAA2C,EAAE,QAAQ,IAAR,CAAa,SAAb,KAA2B,aAAY,UAAzC,CAA/C,EAAqG;AACnG,eAAO,YAAP;AACD;AACF;;AAED,WAAO,IAAP;AACD,GAjFY;;AAmFb;AACA,cAAY,2BAAyB;AAAA,QAAb,SAAa,SAAb,SAAa;;AACnC,4BAA0B,MAAM,YAAhC,gBAA8C;AAAA;;AAAA,cAApB,MAAM,YAAc;AAAA,UAAnC,WAAmC;;AAC5C,UAAI,MAAM,QAAN,CAAe,YAAY,UAA3B,EAAuC,SAAvC,CAAJ,EAAuD;AACrD,eAAO,WAAP;AACD;AACF;AACF,GA1FY;;AA4Fb;AACA,QAAM,qBAA2B;AAAA,QAAf,WAAe,SAAf,WAAe;;AAC/B,4BAA0B,MAAM,YAAhC,gBAA8C;AAAA;;AAAA,eAApB,MAAM,YAAc;AAAA,UAAnC,WAAmC;;AAC5C;AACA,UAAI,YAAY,UAAZ,CAAuB,MAAvB,KAAkC,CAAtC,EAAyC;AACvC,YAAM,SAAS,YAAY,MAA3B;AACA;AACA;AACA,YAAI,UAAU,CAAC,OAAO,OAAP,CAAe,OAAf,CAAuB,OAAtC,EAA+C;AAC7C;AACD;AACF;AACD;AARA,WASK,IAAI,YAAY,UAAZ,CAAuB,MAAvB,IAAiC,CAArC,EAAwC;AAC3C;AACD;;AAED,UAAI,CAAC,YAAY,WAAZ,EAAD,IAA+B,gBAAgB,YAAY,WAA/D,EAA6E;AAC3E,eAAO,WAAP;AACD;AACF;;AAED,WAAO,IAAP;AACD;AAnHY,CAAf;;AAsHA,OAAO,OAAP,GAAiB,MAAjB;;;;;;;ACzHA,IAAM,MAAa,QAAQ,UAAR,CAAnB;AACA,IAAM,WAAa,QAAQ,YAAR,CAAnB;;AAEA,IAAM,KAAK;AACT,SAAU,iBAAM,CAAE,CADT;;AAGT,UAAU;AAAA,WAAS,UAAU,IAAI,MAAd,IAAwB,SAAS,KAAT,CAAjC;AAAA,GAHD;;AAKT,WAAU;AAAA,WAAS,GAAG,MAAH,CAAU,KAAV,KAAoB,MAAM,QAAN,KAAmB,EAAhD;AAAA,GALD;;AAOT,UAAU;AAAA,WAAS,CAAC,CAAC,KAAF,IAAY,QAAO,KAAP,yCAAO,KAAP,OAAiB,QAAtC;AAAA,GAPD;;AAST,YAAU;AAAA,WAAS,OAAO,KAAP,KAAiB,UAA1B;AAAA,GATD;;AAWT,UAAU;AAAA,WAAS,OAAO,KAAP,KAAiB,QAA1B;AAAA,GAXD;;AAaT,QAAU;AAAA,WAAS,OAAO,KAAP,KAAiB,SAA1B;AAAA,GAbD;;AAeT,UAAU;AAAA,WAAS,OAAO,KAAP,KAAiB,QAA1B;AAAA,GAfD;;AAiBT,WAAS,wBAAS;AAChB,QAAI,CAAC,KAAD,IAAW,QAAO,KAAP,yCAAO,KAAP,OAAiB,QAAhC,EAA2C;AAAE,aAAO,KAAP;AAAe;;AAE5D,QAAM,UAAU,IAAI,SAAJ,CAAc,KAAd,KAAwB,IAAI,MAA5C;;AAEA,WAAQ,mBAAkB,IAAlB,SAA8B,QAAQ,OAAtC,KACJ,iBAAiB,QAAQ,OADrB,CAC6B;AAD7B,QAEJ,MAAM,QAAN,KAAmB,CAAnB,IAAwB,OAAO,MAAM,QAAb,KAA0B;AAFtD;AAGD,GAzBQ;;AA2BT,eAAa;AAAA,WAAS,GAAG,MAAH,CAAU,KAAV,KAAoB,MAAM,WAAN,CAAkB,IAAlB,KAA2B,QAAxD;AAAA;AA3BJ,CAAX;;AA8BA,GAAG,KAAH,GAAW;AAAA,SAAU,GAAG,MAAH,CAAU,KAAV,KACf,OAAO,MAAM,MAAb,KAAwB,WADT,IAEhB,GAAG,QAAH,CAAY,MAAM,MAAlB,CAFM;AAAA,CAAX;;AAIA,OAAO,OAAP,GAAiB,EAAjB;;;;;ACrCA,OAAO,OAAP,GAAiB,UAAC,KAAD;AAAA,SAAW,CAAC,EAAE,SAAS,MAAM,MAAjB,CAAD,IAA8B,iBAAiB,MAAM,MAAhE;AAAA,CAAjB;;;;;ACAA,SAAS,aAAT,CAAwB,IAAxB,EAA8B,MAA9B,EAAsC;AACpC,OAAK,IAAM,IAAX,IAAmB,MAAnB,EAA2B;AACzB,QAAM,kBAAkB,OAAO,OAAP,CAAe,eAAvC;AACA,QAAI,aAAa,KAAjB;;AAEA;AACA,SAAK,IAAM,MAAX,IAAqB,eAArB,EAAsC;AACpC,UAAI,KAAK,OAAL,CAAa,MAAb,MAAyB,CAAzB,IAA8B,gBAAgB,MAAhB,EAAwB,IAAxB,CAA6B,IAA7B,CAAlC,EAAsE;AACpE,qBAAa,IAAb;AACA;AACD;AACF;;AAED,QAAI,CAAC,UAAD,IAAe,OAAO,OAAO,IAAP,CAAP,KAAwB,UAA3C,EAAuD;AACrD,WAAK,IAAL,IAAa,OAAO,IAAP,CAAb;AACD;AACF;AACD,SAAO,IAAP;AACD;;AAED,cAAc,eAAd,GAAgC;AAC9B,UAAQ;AADsB,CAAhC;;AAIA,OAAO,OAAP,GAAiB,aAAjB;;;;;ACxBA,IAAM,QAAgB,QAAQ,SAAR,CAAtB;AACA,IAAM,UAAgB,QAAQ,WAAR,CAAtB;AACA,IAAM,MAAgB,QAAQ,cAAR,CAAtB;AACA,IAAM,WAAgB,QAAQ,YAAR,CAAtB;AACA,IAAM,aAAgB,QAAQ,cAAR,CAAtB;AACA,IAAM,KAAgB,QAAQ,MAAR,CAAtB;AACA,IAAM,gBAAgB,QAAQ,iBAAR,CAAtB;;AAEA,IAAM,eAAe;AACnB,cAAY,oBAAU,IAAV,EAAgB,GAAhB,EAAqB;AAC/B,SAAK,IAAL,GAAY,KAAK,IAAL,IAAa,EAAzB;AACA,SAAK,IAAL,CAAU,CAAV,GAAc,IAAI,IAAJ,CAAS,CAAvB;AACA,SAAK,IAAL,CAAU,CAAV,GAAc,IAAI,IAAJ,CAAS,CAAvB;;AAEA,SAAK,MAAL,GAAc,KAAK,MAAL,IAAe,EAA7B;AACA,SAAK,MAAL,CAAY,CAAZ,GAAgB,IAAI,MAAJ,CAAW,CAA3B;AACA,SAAK,MAAL,CAAY,CAAZ,GAAgB,IAAI,MAAJ,CAAW,CAA3B;;AAEA,SAAK,SAAL,GAAiB,IAAI,SAArB;AACD,GAXkB;;AAanB,kBAAgB,wBAAU,SAAV,EAAqB,IAArB,EAA2B,GAA3B,EAAgC;AAC9C,cAAU,IAAV,CAAe,CAAf,GAAsB,IAAI,IAAJ,CAAS,CAAT,GAAgB,KAAK,IAAL,CAAU,CAAhD;AACA,cAAU,IAAV,CAAe,CAAf,GAAsB,IAAI,IAAJ,CAAS,CAAT,GAAgB,KAAK,IAAL,CAAU,CAAhD;AACA,cAAU,MAAV,CAAiB,CAAjB,GAAsB,IAAI,MAAJ,CAAW,CAAX,GAAgB,KAAK,MAAL,CAAY,CAAlD;AACA,cAAU,MAAV,CAAiB,CAAjB,GAAsB,IAAI,MAAJ,CAAW,CAAX,GAAgB,KAAK,MAAL,CAAY,CAAlD;AACA,cAAU,SAAV,GAAsB,IAAI,SAAJ,GAAgB,KAAK,SAA3C;;AAEA;AACA,QAAM,KAAK,KAAK,GAAL,CAAS,UAAU,SAAV,GAAsB,IAA/B,EAAqC,KAArC,CAAX;;AAEA,cAAU,IAAV,CAAe,KAAf,GAAyB,MAAM,UAAU,IAAV,CAAe,CAArB,EAAwB,UAAU,IAAV,CAAe,CAAvC,IAA4C,EAArE;AACA,cAAU,IAAV,CAAe,EAAf,GAAyB,UAAU,IAAV,CAAe,CAAf,GAAmB,EAA5C;AACA,cAAU,IAAV,CAAe,EAAf,GAAyB,UAAU,IAAV,CAAe,CAAf,GAAmB,EAA5C;;AAEA,cAAU,MAAV,CAAiB,KAAjB,GAAyB,MAAM,UAAU,MAAV,CAAiB,CAAvB,EAA0B,UAAU,IAAV,CAAe,CAAzC,IAA8C,EAAvE;AACA,cAAU,MAAV,CAAiB,EAAjB,GAAyB,UAAU,MAAV,CAAiB,CAAjB,GAAqB,EAA9C;AACA,cAAU,MAAV,CAAiB,EAAjB,GAAyB,UAAU,MAAV,CAAiB,CAAjB,GAAqB,EAA9C;AACD,GA9BkB;;AAgCnB,mBAAiB,yBAAW,OAAX,EAAoB;AACnC,WAAQ,mBAAmB,IAAI,KAAvB,IAAgC,mBAAmB,IAAI,KAA/D;AACD,GAlCkB;;AAoCnB;AACA,SAAO,eAAU,IAAV,EAAgB,OAAhB,EAAyB,EAAzB,EAA6B;AAClC,SAAK,MAAM,EAAX;AACA,WAAO,QAAQ,MAAf;;AAEA,OAAG,CAAH,GAAO,QAAQ,OAAO,GAAf,CAAP;AACA,OAAG,CAAH,GAAO,QAAQ,OAAO,GAAf,CAAP;;AAEA,WAAO,EAAP;AACD,GA7CkB;;AA+CnB,aAAW,mBAAU,OAAV,EAAmB,IAAnB,EAAyB;AAClC,WAAO,QAAQ,EAAf;;AAEA;AACA,QAAI,QAAQ,aAAR,IAAyB,aAAa,eAAb,CAA6B,OAA7B,CAA7B,EAAoE;AAClE,mBAAa,KAAb,CAAmB,QAAnB,EAA6B,OAA7B,EAAsC,IAAtC;;AAEA,WAAK,CAAL,IAAU,OAAO,OAAjB;AACA,WAAK,CAAL,IAAU,OAAO,OAAjB;AACD,KALD,MAMK;AACH,mBAAa,KAAb,CAAmB,MAAnB,EAA2B,OAA3B,EAAoC,IAApC;AACD;;AAED,WAAO,IAAP;AACD,GA9DkB;;AAgEnB,eAAa,qBAAU,OAAV,EAAmB,MAAnB,EAA2B;AACtC,aAAS,UAAU,EAAnB;;AAEA,QAAI,QAAQ,aAAR,IAAyB,aAAa,eAAb,CAA6B,OAA7B,CAA7B,EAAoE;AAClE;AACA,mBAAa,KAAb,CAAmB,QAAnB,EAA6B,OAA7B,EAAsC,MAAtC;AACD,KAHD,MAIK;AACH,mBAAa,KAAb,CAAmB,QAAnB,EAA6B,OAA7B,EAAsC,MAAtC;AACD;;AAED,WAAO,MAAP;AACD,GA5EkB;;AA8EnB,gBAAc,sBAAU,OAAV,EAAmB;AAC/B,WAAO,GAAG,MAAH,CAAU,QAAQ,SAAlB,IAA8B,QAAQ,SAAtC,GAAkD,QAAQ,UAAjE;AACD,GAhFkB;;AAkFnB,aAAW,mBAAU,SAAV,EAAqB,QAArB,EAA+B,SAA/B,EAA0C;AACnD,QAAM,UAAW,SAAS,MAAT,GAAkB,CAAlB,GACE,aAAa,cAAb,CAA4B,QAA5B,CADF,GAEE,SAAS,CAAT,CAFnB;;AAIA,QAAM,QAAQ,EAAd;;AAEA,iBAAa,SAAb,CAAuB,OAAvB,EAAgC,KAAhC;AACA,cAAU,IAAV,CAAe,CAAf,GAAmB,MAAM,CAAzB;AACA,cAAU,IAAV,CAAe,CAAf,GAAmB,MAAM,CAAzB;;AAEA,iBAAa,WAAb,CAAyB,OAAzB,EAAkC,KAAlC;AACA,cAAU,MAAV,CAAiB,CAAjB,GAAqB,MAAM,CAA3B;AACA,cAAU,MAAV,CAAiB,CAAjB,GAAqB,MAAM,CAA3B;;AAEA,cAAU,SAAV,GAAsB,GAAG,MAAH,CAAU,SAAV,IAAuB,SAAvB,GAAkC,IAAI,IAAJ,GAAW,OAAX,EAAxD;AACD,GAlGkB;;AAoGnB,iBAAe,aApGI;;AAsGnB,gBAAc,sBAAU,KAAV,EAAiB;AAC7B,QAAM,UAAU,EAAhB;;AAEA;AACA,QAAI,GAAG,KAAH,CAAS,KAAT,CAAJ,EAAqB;AACnB,cAAQ,CAAR,IAAa,MAAM,CAAN,CAAb;AACA,cAAQ,CAAR,IAAa,MAAM,CAAN,CAAb;AACD;AACD;AAJA,SAKK;AACH,YAAI,MAAM,IAAN,KAAe,UAAnB,EAA+B;AAC7B,cAAI,MAAM,OAAN,CAAc,MAAd,KAAyB,CAA7B,EAAgC;AAC9B,oBAAQ,CAAR,IAAa,MAAM,OAAN,CAAc,CAAd,CAAb;AACA,oBAAQ,CAAR,IAAa,MAAM,cAAN,CAAqB,CAArB,CAAb;AACD,WAHD,MAIK,IAAI,MAAM,OAAN,CAAc,MAAd,KAAyB,CAA7B,EAAgC;AACnC,oBAAQ,CAAR,IAAa,MAAM,cAAN,CAAqB,CAArB,CAAb;AACA,oBAAQ,CAAR,IAAa,MAAM,cAAN,CAAqB,CAArB,CAAb;AACD;AACF,SATD,MAUK;AACH,kBAAQ,CAAR,IAAa,MAAM,OAAN,CAAc,CAAd,CAAb;AACA,kBAAQ,CAAR,IAAa,MAAM,OAAN,CAAc,CAAd,CAAb;AACD;AACF;;AAED,WAAO,OAAP;AACD,GAjIkB;;AAmInB,kBAAgB,wBAAU,QAAV,EAAoB;AAClC,QAAM,UAAU;AACd,aAAS,CADK;AAEd,aAAS,CAFK;AAGd,eAAS,CAHK;AAId,eAAS,CAJK;AAKd,eAAS,CALK;AAMd,eAAS;AANK,KAAhB;;AASA,0BAAsB,QAAtB,eAAgC;AAAA;;AAAA,aAAV,QAAU;AAAA,UAArB,OAAqB;;AAC9B,WAAK,IAAM,KAAX,IAAmB,OAAnB,EAA4B;AAC1B,gBAAQ,KAAR,KAAiB,QAAQ,KAAR,CAAjB;AACD;AACF;AACD,SAAK,IAAM,IAAX,IAAmB,OAAnB,EAA4B;AAC1B,cAAQ,IAAR,KAAiB,SAAS,MAA1B;AACD;;AAED,WAAO,OAAP;AACD,GAvJkB;;AAyJnB,aAAW,mBAAU,KAAV,EAAiB;AAC1B,QAAI,CAAC,MAAM,MAAP,IAAiB,EAAE,MAAM,OAAN,IAAiB,MAAM,OAAN,CAAc,MAAd,GAAuB,CAA1C,CAArB,EAAmE;AACjE;AACD;;AAED,QAAM,UAAU,aAAa,YAAb,CAA0B,KAA1B,CAAhB;AACA,QAAM,OAAO,KAAK,GAAL,CAAS,QAAQ,CAAR,EAAW,KAApB,EAA2B,QAAQ,CAAR,EAAW,KAAtC,CAAb;AACA,QAAM,OAAO,KAAK,GAAL,CAAS,QAAQ,CAAR,EAAW,KAApB,EAA2B,QAAQ,CAAR,EAAW,KAAtC,CAAb;AACA,QAAM,OAAO,KAAK,GAAL,CAAS,QAAQ,CAAR,EAAW,KAApB,EAA2B,QAAQ,CAAR,EAAW,KAAtC,CAAb;AACA,QAAM,OAAO,KAAK,GAAL,CAAS,QAAQ,CAAR,EAAW,KAApB,EAA2B,QAAQ,CAAR,EAAW,KAAtC,CAAb;;AAEA,WAAO;AACL,SAAG,IADE;AAEL,SAAG,IAFE;AAGL,YAAM,IAHD;AAIL,WAAK,IAJA;AAKL,aAAO,OAAO,IALT;AAML,cAAQ,OAAO;AANV,KAAP;AAQD,GA5KkB;;AA8KnB,iBAAe,uBAAU,KAAV,EAAiB,WAAjB,EAA8B;AAC3C,QAAM,UAAU,cAAc,GAA9B;AACA,QAAM,UAAU,cAAc,GAA9B;AACA,QAAM,UAAU,aAAa,YAAb,CAA0B,KAA1B,CAAhB;;AAGA,QAAM,KAAK,QAAQ,CAAR,EAAW,OAAX,IAAsB,QAAQ,CAAR,EAAW,OAAX,CAAjC;AACA,QAAM,KAAK,QAAQ,CAAR,EAAW,OAAX,IAAsB,QAAQ,CAAR,EAAW,OAAX,CAAjC;;AAEA,WAAO,MAAM,EAAN,EAAU,EAAV,CAAP;AACD,GAxLkB;;AA0LnB,cAAY,oBAAU,KAAV,EAAiB,SAAjB,EAA4B,WAA5B,EAAyC;AACnD,QAAM,UAAU,cAAc,GAA9B;AACA,QAAM,UAAU,cAAc,GAA9B;AACA,QAAM,UAAU,aAAa,YAAb,CAA0B,KAA1B,CAAhB;AACA,QAAM,KAAK,QAAQ,CAAR,EAAW,OAAX,IAAsB,QAAQ,CAAR,EAAW,OAAX,CAAjC;AACA,QAAM,KAAK,QAAQ,CAAR,EAAW,OAAX,IAAsB,QAAQ,CAAR,EAAW,OAAX,CAAjC;AACA,QAAM,QAAQ,MAAM,KAAK,KAAL,CAAW,EAAX,EAAgB,EAAhB,CAAN,GAA4B,KAAK,EAA/C;;AAEA,WAAQ,KAAR;AACD,GAnMkB;;AAqMnB,kBAAgB,wBAAU,OAAV,EAAmB;AACjC,WAAO,GAAG,MAAH,CAAU,QAAQ,WAAlB,IACH,QAAQ,WADL,GAEH,GAAG,MAAH,CAAU,QAAQ,WAAlB,IACE,CAAC,SAAD,EAAY,SAAZ,EAAsB,OAAtB,EAA+B,KAA/B,EAAsC,OAAtC,EAA+C,QAAQ,WAAvD;AACA;AACA;AAHF,MAII,QAAQ,IAAR,CAAa,QAAQ,IAArB,KAA8B,mBAAmB,WAAW,KAA5D,GACE,OADF,GAEE,OARV;AASD,GA/MkB;;AAiNnB;AACA,mBAAiB,yBAAU,KAAV,EAAiB;AAChC,QAAM,OAAO,GAAG,QAAH,CAAY,MAAM,YAAlB,IAAkC,MAAM,YAAN,EAAlC,GAAyD,MAAM,IAA5E;;AAEA,WAAO,CACL,SAAS,gBAAT,CAA0B,OAAO,KAAK,CAAL,CAAP,GAAiB,MAAM,MAAjD,CADK,EAEL,SAAS,gBAAT,CAA0B,MAAM,aAAhC,CAFK,CAAP;AAID;AAzNkB,CAArB;;AA4NA,OAAO,OAAP,GAAiB,YAAjB;;;;;eCpOmB,QAAQ,UAAR,C;IAAX,M,YAAA,M;;AAER,IAAM,UAAU,CAAC,IAAD,EAAO,KAAP,EAAc,QAAd,EAAwB,GAAxB,CAAhB;AACA,IAAI,WAAW,CAAf;AACA,IAAI,gBAAJ;AACA,IAAI,eAAJ;;AAEA,KAAK,IAAI,IAAI,CAAb,EAAgB,IAAI,QAAQ,MAAZ,IAAsB,CAAC,OAAO,qBAA9C,EAAqE,GAArE,EAA0E;AACxE,YAAU,OAAO,QAAQ,CAAR,IAAa,uBAApB,CAAV;AACA,WAAS,OAAO,QAAQ,CAAR,IAAY,sBAAnB,KAA8C,OAAO,QAAQ,CAAR,IAAa,6BAApB,CAAvD;AACD;;AAED,IAAI,CAAC,OAAL,EAAc;AACZ,YAAU,iBAAU,QAAV,EAAoB;AAC5B,QAAM,WAAW,IAAI,IAAJ,GAAW,OAAX,EAAjB;AACA,QAAM,aAAa,KAAK,GAAL,CAAS,CAAT,EAAY,MAAM,WAAW,QAAjB,CAAZ,CAAnB;AACA,QAAM,KAAK,WAAW,YAAY;AAAE,eAAS,WAAW,UAApB;AAAkC,KAA3D,EACW,UADX,CAAX;;AAGA,eAAW,WAAW,UAAtB;AACA,WAAO,EAAP;AACD,GARD;AASD;;AAED,IAAI,CAAC,MAAL,EAAa;AACX,WAAS,gBAAU,EAAV,EAAc;AACrB,iBAAa,EAAb;AACD,GAFD;AAGD;;AAED,OAAO,OAAP,GAAiB;AACf,kBADe;AAEf;AAFe,CAAjB;;;;;AC9BA,IAAM,SAAS,QAAQ,UAAR,CAAf;AACA,IAAM,KAAK,QAAQ,MAAR,CAAX;;eAKI,QAAQ,YAAR,C;IAHF,O,YAAA,O;IACA,U,YAAA,U;IACA,c,YAAA,c;;AAGF,IAAM,YAAY;AAChB,yBAAuB,+BAAU,KAAV,EAAiB,YAAjB,EAA+B,OAA/B,EAAwC;AAC7D,QAAI,CAAC,GAAG,MAAH,CAAU,KAAV,CAAL,EAAuB;AACrB,aAAO,IAAP;AACD;;AAED,QAAI,UAAU,QAAd,EAAwB;AACtB,cAAQ,WAAW,OAAX,CAAR;AACD,KAFD,MAGK,IAAI,UAAU,MAAd,EAAsB;AACzB,cAAQ,aAAa,OAAb,CAAqB,OAArB,CAAR;AACD,KAFI,MAGA;AACH,cAAQ,QAAQ,OAAR,EAAiB,KAAjB,CAAR;AACD;;AAED,WAAO,KAAP;AACD,GAjBe;;AAmBhB,mBAAiB,yBAAU,KAAV,EAAiB,YAAjB,EAA+B,OAA/B,EAAwC,YAAxC,EAAsD;AACrE,YAAQ,UAAU,qBAAV,CAAgC,KAAhC,EAAuC,YAAvC,EAAqD,OAArD,KAAiE,KAAzE;;AAEA,QAAI,GAAG,QAAH,CAAY,KAAZ,CAAJ,EAAwB;AACtB,cAAQ,MAAM,KAAN,CAAY,IAAZ,EAAkB,YAAlB,CAAR;AACD;;AAED,QAAI,GAAG,OAAH,CAAW,KAAX,CAAJ,EAAuB;AACrB,cAAQ,eAAe,KAAf,CAAR;AACD;;AAED,WAAO,KAAP;AACD,GA/Be;;AAiChB,YAAU,kBAAU,IAAV,EAAgB;AACxB,WAAQ,QAAQ;AACd,SAAG,OAAO,IAAP,GAAc,KAAK,CAAnB,GAAuB,KAAK,IADjB;AAEd,SAAG,OAAO,IAAP,GAAc,KAAK,CAAnB,GAAuB,KAAK;AAFjB,KAAhB;AAID,GAtCe;;AAwChB,cAAY,oBAAU,IAAV,EAAgB;AAC1B,QAAI,QAAQ,EAAE,UAAU,IAAV,IAAkB,SAAS,IAA7B,CAAZ,EAAgD;AAC9C,aAAO,OAAO,EAAP,EAAW,IAAX,CAAP;;AAEA,WAAK,IAAL,GAAc,KAAK,CAAL,IAAU,CAAxB;AACA,WAAK,GAAL,GAAc,KAAK,CAAL,IAAU,CAAxB;AACA,WAAK,KAAL,GAAc,KAAK,KAAL,IAAiB,KAAK,IAAL,GAAY,KAAK,KAAhD;AACA,WAAK,MAAL,GAAc,KAAK,MAAL,IAAiB,KAAK,GAAL,GAAW,KAAK,MAA/C;AACD;;AAED,WAAO,IAAP;AACD,GAnDe;;AAqDhB,cAAY,oBAAU,IAAV,EAAgB;AAC1B,QAAI,QAAQ,EAAE,OAAO,IAAP,IAAe,OAAO,IAAxB,CAAZ,EAA2C;AACzC,aAAO,OAAO,EAAP,EAAW,IAAX,CAAP;;AAEA,WAAK,CAAL,GAAc,KAAK,IAAL,IAAa,CAA3B;AACA,WAAK,GAAL,GAAc,KAAK,GAAL,IAAa,CAA3B;AACA,WAAK,KAAL,GAAc,KAAK,KAAL,IAAgB,KAAK,KAAL,GAAc,KAAK,CAAjD;AACA,WAAK,MAAL,GAAc,KAAK,MAAL,IAAgB,KAAK,MAAL,GAAc,KAAK,CAAjD;AACD;;AAED,WAAO,IAAP;AACD;AAhEe,CAAlB;;AAmEA,OAAO,OAAP,GAAiB,SAAjB;;;;;AC3EA,IAAM,MAAM,OAAO,OAAnB;AACA,IAAM,WAAW,QAAQ,YAAR,CAAjB;;AAEA,SAAS,IAAT,CAAe,MAAf,EAAuB;AACrB;;AAEA,MAAI,UAAJ,GAAiB,MAAjB;;AAEA;AACA,MAAM,KAAK,OAAO,QAAP,CAAgB,cAAhB,CAA+B,EAA/B,CAAX;;AAEA;AACA,MAAI,GAAG,aAAH,KAAqB,OAAO,QAA5B,IACG,OAAO,OAAO,IAAd,KAAuB,UAD1B,IAEC,OAAO,IAAP,CAAY,EAAZ,MAAoB,EAFzB,EAE6B;AAC3B;AACA,aAAS,OAAO,IAAP,CAAY,MAAZ,CAAT;AACD;;AAED,MAAI,MAAJ,GAAa,MAAb;AACD;;AAED,IAAI,OAAO,MAAP,KAAkB,WAAtB,EAAmC;AACjC,MAAI,MAAJ,GAAiB,SAAjB;AACA,MAAI,UAAJ,GAAiB,SAAjB;AACD,CAHD,MAIK;AACH,OAAK,MAAL;AACD;;AAED,IAAI,SAAJ,GAAgB,SAAS,SAAT,CAAoB,IAApB,EAA0B;AACxC,MAAI,SAAS,IAAT,CAAJ,EAAoB;AAClB,WAAO,IAAP;AACD;;AAED,MAAM,WAAY,KAAK,aAAL,IAAsB,IAAxC;;AAEA,SAAO,SAAS,WAAT,IAAwB,SAAS,YAAjC,IAAiD,IAAI,MAA5D;AACD,CARD;;AAUA,IAAI,IAAJ,GAAW,IAAX", "file": "interact.js", "sourceRoot": "", "sourcesContent": ["/**\n * interact.js v1.3.4\n *\n * Copyright (c) 2012-2018 <PERSON><PERSON> <<EMAIL>>\n * Released under the MIT License.\n * https://raw.github.com/taye/interact.js/master/LICENSE\n */\n", "(function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require==\"function\"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error(\"Cannot find module '\"+o+\"'\");throw f.code=\"MODULE_NOT_FOUND\",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require==\"function\"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})", "/*\n * In a (windowless) server environment this file exports a factory function\n * that takes the window to use.\n *\n *     var interact = require('interact.js')(windowObject);\n *\n * See https://github.com/taye/interact.js/issues/187\n */\nif (typeof window === 'undefined') {\n  module.exports = function (window) {\n    require('./src/utils/window').init(window);\n\n    return require('./src/index');\n  };\n}\nelse {\n  module.exports = require('./src/index');\n}\n", "const extend = require('./utils/extend.js');\n\nfunction fireUntilImmediateStopped (event, listeners) {\n  for (const listener of listeners) {\n    if (event.immediatePropagationStopped) { break; }\n\n    listener(event);\n  }\n}\n\nclass Eventable {\n\n  constructor (options) {\n    this.options = extend({}, options || {});\n  }\n\n  fire (event) {\n    let listeners;\n    const onEvent = 'on' + event.type;\n    const global = this.global;\n\n    // Interactable#on() listeners\n    if ((listeners = this[event.type])) {\n      fireUntilImmediateStopped(event, listeners);\n    }\n\n    // interactable.onevent listener\n    if (this[onEvent]) {\n      this[onEvent](event);\n    }\n\n    // interact.on() listeners\n    if (!event.propagationStopped && global && (listeners = global[event.type]))  {\n      fireUntilImmediateStopped(event, listeners);\n    }\n  }\n\n  on (eventType, listener) {\n    // if this type of event was never bound\n    if (this[eventType]) {\n      this[eventType].push(listener);\n    }\n    else {\n      this[eventType] = [listener];\n    }\n  }\n\n  off (eventType, listener) {\n    // if it is an action event type\n    const eventList = this[eventType];\n    const index     = eventList? eventList.indexOf(listener) : -1;\n\n    if (index !== -1) {\n      eventList.splice(index, 1);\n    }\n\n    if (eventList && eventList.length === 0 || !listener) {\n      this[eventType] = undefined;\n    }\n  }\n}\n\nmodule.exports = Eventable;\n", "const extend      = require('./utils/extend');\nconst getOriginXY = require('./utils/getOriginXY');\nconst defaults    = require('./defaultOptions');\nconst signals     = require('./utils/Signals').new();\n\nclass InteractEvent {\n  /** */\n  constructor (interaction, event, action, phase, element, related, preEnd = false) {\n    const target      = interaction.target;\n    const deltaSource = (target && target.options || defaults).deltaSource;\n    const origin      = getOriginXY(target, element, action);\n    const starting    = phase === 'start';\n    const ending      = phase === 'end';\n    const coords      = starting? interaction.startCoords : interaction.curCoords;\n    const prevEvent   = interaction.prevEvent;\n\n    element = element || interaction.element;\n\n    const page   = extend({}, coords.page);\n    const client = extend({}, coords.client);\n\n    page.x -= origin.x;\n    page.y -= origin.y;\n\n    client.x -= origin.x;\n    client.y -= origin.y;\n\n    this.ctrlKey       = event.ctrlKey;\n    this.altKey        = event.altKey;\n    this.shiftKey      = event.shiftKey;\n    this.metaKey       = event.metaKey;\n    this.button        = event.button;\n    this.buttons       = event.buttons;\n    this.target        = element;\n    this.currentTarget = element;\n    this.relatedTarget = related || null;\n    this.preEnd        = preEnd;\n    this.type          = action + (phase || '');\n    this.interaction   = interaction;\n    this.interactable  = target;\n\n    this.t0 = starting ? interaction.downTimes[interaction.downTimes.length - 1]\n                       : prevEvent.t0;\n\n    const signalArg = {\n      interaction,\n      event,\n      action,\n      phase,\n      element,\n      related,\n      page,\n      client,\n      coords,\n      starting,\n      ending,\n      deltaSource,\n      iEvent: this,\n    };\n\n    signals.fire('set-xy', signalArg);\n\n    if (ending) {\n      // use previous coords when ending\n      this.pageX = prevEvent.pageX;\n      this.pageY = prevEvent.pageY;\n      this.clientX = prevEvent.clientX;\n      this.clientY = prevEvent.clientY;\n    }\n    else {\n      this.pageX     = page.x;\n      this.pageY     = page.y;\n      this.clientX   = client.x;\n      this.clientY   = client.y;\n    }\n\n    this.x0        = interaction.startCoords.page.x - origin.x;\n    this.y0        = interaction.startCoords.page.y - origin.y;\n    this.clientX0  = interaction.startCoords.client.x - origin.x;\n    this.clientY0  = interaction.startCoords.client.y - origin.y;\n\n    signals.fire('set-delta', signalArg);\n\n    this.timeStamp = coords.timeStamp;\n    this.dt        = interaction.pointerDelta.timeStamp;\n    this.duration  = this.timeStamp - this.t0;\n\n    // speed and velocity in pixels per second\n    this.speed = interaction.pointerDelta[deltaSource].speed;\n    this.velocityX = interaction.pointerDelta[deltaSource].vx;\n    this.velocityY = interaction.pointerDelta[deltaSource].vy;\n\n    this.swipe = (ending || phase === 'inertiastart')? this.getSwipe() : null;\n\n    signals.fire('new', signalArg);\n  }\n\n  getSwipe () {\n    const interaction = this.interaction;\n\n    if (interaction.prevEvent.speed < 600\n        || this.timeStamp - interaction.prevEvent.timeStamp > 150) {\n      return null;\n    }\n\n    let angle = 180 * Math.atan2(interaction.prevEvent.velocityY, interaction.prevEvent.velocityX) / Math.PI;\n    const overlap = 22.5;\n\n    if (angle < 0) {\n      angle += 360;\n    }\n\n    const left = 135 - overlap <= angle && angle < 225 + overlap;\n    const up   = 225 - overlap <= angle && angle < 315 + overlap;\n\n    const right = !left && (315 - overlap <= angle || angle <  45 + overlap);\n    const down  = !up   &&   45 - overlap <= angle && angle < 135 + overlap;\n\n    return {\n      up,\n      down,\n      left,\n      right,\n      angle,\n      speed: interaction.prevEvent.speed,\n      velocity: {\n        x: interaction.prevEvent.velocityX,\n        y: interaction.prevEvent.velocityY,\n      },\n    };\n  }\n\n  preventDefault () {}\n\n  /** */\n  stopImmediatePropagation () {\n    this.immediatePropagationStopped = this.propagationStopped = true;\n  }\n\n  /** */\n  stopPropagation () {\n    this.propagationStopped = true;\n  }\n}\n\nsignals.on('set-delta', function ({ iEvent, interaction, starting, deltaSource }) {\n  const prevEvent = starting? iEvent : interaction.prevEvent;\n\n  if (deltaSource === 'client') {\n    iEvent.dx = iEvent.clientX - prevEvent.clientX;\n    iEvent.dy = iEvent.clientY - prevEvent.clientY;\n  }\n  else {\n    iEvent.dx = iEvent.pageX - prevEvent.pageX;\n    iEvent.dy = iEvent.pageY - prevEvent.pageY;\n  }\n});\n\nInteractEvent.signals = signals;\n\nmodule.exports = InteractEvent;\n", "const clone     = require('./utils/clone');\nconst is        = require('./utils/is');\nconst events    = require('./utils/events');\nconst extend    = require('./utils/extend');\nconst actions   = require('./actions/base');\nconst scope     = require('./scope');\nconst Eventable = require('./Eventable');\nconst defaults  = require('./defaultOptions');\nconst signals   = require('./utils/Signals').new();\n\nconst {\n  getElementRect,\n  nodeContains,\n  trySelector,\n  matchesSelector,\n}                    = require('./utils/domUtils');\nconst { getWindow }  = require('./utils/window');\nconst { contains }   = require('./utils/arr');\nconst { wheelEvent } = require('./utils/browser');\n\n// all set interactables\nscope.interactables = [];\n\nclass Interactable {\n  /** */\n  constructor (target, options) {\n    options = options || {};\n\n    this.target   = target;\n    this.events   = new Eventable();\n    this._context = options.context || scope.document;\n    this._win     = getWindow(trySelector(target)? this._context : target);\n    this._doc     = this._win.document;\n\n    signals.fire('new', {\n      target,\n      options,\n      interactable: this,\n      win: this._win,\n    });\n\n    scope.addDocument( this._doc, this._win );\n\n    scope.interactables.push(this);\n\n    this.set(options);\n  }\n\n  setOnEvents (action, phases) {\n    const onAction = 'on' + action;\n\n    if (is.function(phases.onstart)       ) { this.events[onAction + 'start'        ] = phases.onstart         ; }\n    if (is.function(phases.onmove)        ) { this.events[onAction + 'move'         ] = phases.onmove          ; }\n    if (is.function(phases.onend)         ) { this.events[onAction + 'end'          ] = phases.onend           ; }\n    if (is.function(phases.oninertiastart)) { this.events[onAction + 'inertiastart' ] = phases.oninertiastart  ; }\n\n    return this;\n  }\n\n  setPerAction (action, options) {\n    // for all the default per-action options\n    for (const option in options) {\n      // if this option exists for this action\n      if (option in defaults[action]) {\n        // if the option in the options arg is an object value\n        if (is.object(options[option])) {\n          // duplicate the object and merge\n          this.options[action][option] = clone(this.options[action][option] || {});\n          extend(this.options[action][option], options[option]);\n\n          if (is.object(defaults.perAction[option]) && 'enabled' in defaults.perAction[option]) {\n            this.options[action][option].enabled = options[option].enabled === false? false : true;\n          }\n        }\n        else if (is.bool(options[option]) && is.object(defaults.perAction[option])) {\n          this.options[action][option].enabled = options[option];\n        }\n        else if (options[option] !== undefined) {\n          // or if it's not undefined, do a plain assignment\n          this.options[action][option] = options[option];\n        }\n      }\n    }\n  }\n\n  /**\n   * The default function to get an Interactables bounding rect. Can be\n   * overridden using {@link Interactable.rectChecker}.\n   *\n   * @param {Element} [element] The element to measure.\n   * @return {object} The object's bounding rectangle.\n   */\n  getRect (element) {\n    element = element || this.target;\n\n    if (is.string(this.target) && !(is.element(element))) {\n      element = this._context.querySelector(this.target);\n    }\n\n    return getElementRect(element);\n  }\n\n  /**\n   * Returns or sets the function used to calculate the interactable's\n   * element's rectangle\n   *\n   * @param {function} [checker] A function which returns this Interactable's\n   * bounding rectangle. See {@link Interactable.getRect}\n   * @return {function | object} The checker function or this Interactable\n   */\n  rectChecker (checker) {\n    if (is.function(checker)) {\n      this.getRect = checker;\n\n      return this;\n    }\n\n    if (checker === null) {\n      delete this.options.getRect;\n\n      return this;\n    }\n\n    return this.getRect;\n  }\n\n  _backCompatOption (optionName, newValue) {\n    if (trySelector(newValue) || is.object(newValue)) {\n      this.options[optionName] = newValue;\n\n      for (const action of actions.names) {\n        this.options[action][optionName] = newValue;\n      }\n\n      return this;\n    }\n\n    return this.options[optionName];\n  }\n\n  /**\n   * Gets or sets the origin of the Interactable's element.  The x and y\n   * of the origin will be subtracted from action event coordinates.\n   *\n   * @param {Element | object | string} [origin] An HTML or SVG Element whose\n   * rect will be used, an object eg. { x: 0, y: 0 } or string 'parent', 'self'\n   * or any CSS selector\n   *\n   * @return {object} The current origin or this Interactable\n   */\n  origin (newValue) {\n    return this._backCompatOption('origin', newValue);\n  }\n\n  /**\n   * Returns or sets the mouse coordinate types used to calculate the\n   * movement of the pointer.\n   *\n   * @param {string} [newValue] Use 'client' if you will be scrolling while\n   * interacting; Use 'page' if you want autoScroll to work\n   * @return {string | object} The current deltaSource or this Interactable\n   */\n  deltaSource (newValue) {\n    if (newValue === 'page' || newValue === 'client') {\n      this.options.deltaSource = newValue;\n\n      return this;\n    }\n\n    return this.options.deltaSource;\n  }\n\n  /**\n   * Gets the selector context Node of the Interactable. The default is\n   * `window.document`.\n   *\n   * @return {Node} The context Node of this Interactable\n   */\n  context () {\n    return this._context;\n  }\n\n  inContext (element) {\n    return (this._context === element.ownerDocument\n            || nodeContains(this._context, element));\n  }\n\n  /**\n   * Calls listeners for the given InteractEvent type bound globally\n   * and directly to this Interactable\n   *\n   * @param {InteractEvent} iEvent The InteractEvent object to be fired on this\n   * Interactable\n   * @return {Interactable} this Interactable\n   */\n  fire (iEvent) {\n    this.events.fire(iEvent);\n\n    return this;\n  }\n\n  _onOffMultiple (method, eventType, listener, options) {\n    if (is.string(eventType) && eventType.search(' ') !== -1) {\n      eventType = eventType.trim().split(/ +/);\n    }\n\n    if (is.array(eventType)) {\n      for (const type of eventType) {\n        this[method](type, listener, options);\n      }\n\n      return true;\n    }\n\n    if (is.object(eventType)) {\n      for (const prop in eventType) {\n        this[method](prop, eventType[prop], listener);\n      }\n\n      return true;\n    }\n  }\n\n  /**\n   * Binds a listener for an InteractEvent, pointerEvent or DOM event.\n   *\n   * @param {string | array | object} eventType  The types of events to listen\n   * for\n   * @param {function} listener   The function event (s)\n   * @param {object | boolean} [options]    options object or useCapture flag\n   * for addEventListener\n   * @return {object} This Interactable\n   */\n  on (eventType, listener, options) {\n    if (this._onOffMultiple('on', eventType, listener, options)) {\n      return this;\n    }\n\n    if (eventType === 'wheel') { eventType = wheelEvent; }\n\n    if (contains(Interactable.eventTypes, eventType)) {\n      this.events.on(eventType, listener);\n    }\n    // delegated event for selector\n    else if (is.string(this.target)) {\n      events.addDelegate(this.target, this._context, eventType, listener, options);\n    }\n    else {\n      events.add(this.target, eventType, listener, options);\n    }\n\n    return this;\n  }\n\n  /**\n   * Removes an InteractEvent, pointerEvent or DOM event listener\n   *\n   * @param {string | array | object} eventType The types of events that were\n   * listened for\n   * @param {function} listener The listener function to be removed\n   * @param {object | boolean} [options] options object or useCapture flag for\n   * removeEventListener\n   * @return {object} This Interactable\n   */\n  off (eventType, listener, options) {\n    if (this._onOffMultiple('off', eventType, listener, options)) {\n      return this;\n    }\n\n    if (eventType === 'wheel') { eventType = wheelEvent; }\n\n    // if it is an action event type\n    if (contains(Interactable.eventTypes, eventType)) {\n      this.events.off(eventType, listener);\n    }\n    // delegated event\n    else if (is.string(this.target)) {\n      events.removeDelegate(this.target, this._context, eventType, listener, options);\n    }\n    // remove listener from this Interatable's element\n    else {\n      events.remove(this.target, eventType, listener, options);\n    }\n\n    return this;\n  }\n\n  /**\n   * Reset the options of this Interactable\n   *\n   * @param {object} options The new settings to apply\n   * @return {object} This Interactable\n   */\n  set (options) {\n    if (!is.object(options)) {\n      options = {};\n    }\n\n    this.options = clone(defaults.base);\n\n    const perActions = clone(defaults.perAction);\n\n    for (const actionName in actions.methodDict) {\n      const methodName = actions.methodDict[actionName];\n\n      this.options[actionName] = clone(defaults[actionName]);\n\n      this.setPerAction(actionName, perActions);\n\n      this[methodName](options[actionName]);\n    }\n\n    for (const setting of Interactable.settingsMethods) {\n      this.options[setting] = defaults.base[setting];\n\n      if (setting in options) {\n        this[setting](options[setting]);\n      }\n    }\n\n    signals.fire('set', {\n      options,\n      interactable: this,\n    });\n\n    return this;\n  }\n\n  /**\n   * Remove this interactable from the list of interactables and remove it's\n   * action capabilities and event listeners\n   *\n   * @return {interact}\n   */\n  unset () {\n    events.remove(this.target, 'all');\n\n    if (is.string(this.target)) {\n      // remove delegated events\n      for (const type in events.delegatedEvents) {\n        const delegated = events.delegatedEvents[type];\n\n        if (delegated.selectors[0] === this.target\n            && delegated.contexts[0] === this._context) {\n\n          delegated.selectors.splice(0, 1);\n          delegated.contexts .splice(0, 1);\n          delegated.listeners.splice(0, 1);\n\n          // remove the arrays if they are empty\n          if (!delegated.selectors.length) {\n            delegated[type] = null;\n          }\n        }\n\n        events.remove(this._context, type, events.delegateListener);\n        events.remove(this._context, type, events.delegateUseCapture, true);\n      }\n    }\n    else {\n      events.remove(this, 'all');\n    }\n\n    signals.fire('unset', { interactable: this });\n\n    scope.interactables.splice(scope.interactables.indexOf(this), 1);\n\n    // Stop related interactions when an Interactable is unset\n    for (const interaction of scope.interactions || []) {\n      if (interaction.target === this && interaction.interacting() && !interaction._ending) {\n        interaction.stop();\n      }\n    }\n\n    return scope.interact;\n  }\n}\n\nscope.interactables.indexOfElement = function indexOfElement (target, context) {\n  context = context || scope.document;\n\n  for (let i = 0; i < this.length; i++) {\n    const interactable = this[i];\n\n    if (interactable.target === target && interactable._context === context) {\n      return i;\n    }\n  }\n  return -1;\n};\n\nscope.interactables.get = function interactableGet (element, options, dontCheckInContext) {\n  const ret = this[this.indexOfElement(element, options && options.context)];\n\n  return ret && (is.string(element) || dontCheckInContext || ret.inContext(element))? ret : null;\n};\n\nscope.interactables.forEachMatch = function (element, callback) {\n  for (const interactable of this) {\n    let ret;\n\n    if ((is.string(interactable.target)\n        // target is a selector and the element matches\n        ? (is.element(element) && matchesSelector(element, interactable.target))\n        // target is the element\n        : element === interactable.target)\n        // the element is in context\n      && (interactable.inContext(element))) {\n      ret = callback(interactable);\n    }\n\n    if (ret !== undefined) {\n      return ret;\n    }\n  }\n};\n\n// all interact.js eventTypes\nInteractable.eventTypes = scope.eventTypes = [];\n\nInteractable.signals = signals;\n\nInteractable.settingsMethods = [ 'deltaSource', 'origin', 'preventDefault', 'rectChecker' ];\n\nmodule.exports = Interactable;\n", "const scope      = require('./scope');\nconst utils      = require('./utils');\nconst events     = require('./utils/events');\nconst browser    = require('./utils/browser');\nconst domObjects = require('./utils/domObjects');\nconst finder     = require('./utils/interactionFinder');\nconst signals    = require('./utils/Signals').new();\n\nconst listeners   = {};\nconst methodNames = [\n  'pointerDown', 'pointerMove', 'pointerUp',\n  'updatePointer', 'removePointer',\n];\n\n// for ignoring browser's simulated mouse events\nlet prevTouchTime = 0;\n\n// all active and idle interactions\nscope.interactions = [];\n\nclass Interaction {\n  /** */\n  constructor ({ pointerType }) {\n    this.target        = null; // current interactable being interacted with\n    this.element       = null; // the target element of the interactable\n\n    this.prepared      = {     // action that's ready to be fired on next move event\n      name : null,\n      axis : null,\n      edges: null,\n    };\n\n    // keep track of added pointers\n    this.pointers    = [];\n    this.pointerIds  = [];\n    this.downTargets = [];\n    this.downTimes   = [];\n\n    // Previous native pointer move event coordinates\n    this.prevCoords = {\n      page     : { x: 0, y: 0 },\n      client   : { x: 0, y: 0 },\n      timeStamp: 0,\n    };\n    // current native pointer move event coordinates\n    this.curCoords = {\n      page     : { x: 0, y: 0 },\n      client   : { x: 0, y: 0 },\n      timeStamp: 0,\n    };\n\n    // Starting InteractEvent pointer coordinates\n    this.startCoords = {\n      page     : { x: 0, y: 0 },\n      client   : { x: 0, y: 0 },\n      timeStamp: 0,\n    };\n\n    // Change in coordinates and time of the pointer\n    this.pointerDelta = {\n      page     : { x: 0, y: 0, vx: 0, vy: 0, speed: 0 },\n      client   : { x: 0, y: 0, vx: 0, vy: 0, speed: 0 },\n      timeStamp: 0,\n    };\n\n    this.downEvent   = null;    // pointerdown/mousedown/touchstart event\n    this.downPointer = {};\n\n    this._eventTarget    = null;\n    this._curEventTarget = null;\n\n    this.prevEvent = null;      // previous action event\n\n    this.pointerIsDown   = false;\n    this.pointerWasMoved = false;\n    this._interacting    = false;\n    this._ending         = false;\n\n    this.pointerType = pointerType;\n\n    signals.fire('new', this);\n\n    scope.interactions.push(this);\n  }\n\n  pointerDown (pointer, event, eventTarget) {\n    const pointerIndex = this.updatePointer(pointer, event, true);\n\n    signals.fire('down', {\n      pointer,\n      event,\n      eventTarget,\n      pointerIndex,\n      interaction: this,\n    });\n  }\n\n  /**\n   * ```js\n   * interact(target)\n   *   .draggable({\n   *     // disable the default drag start by down->move\n   *     manualStart: true\n   *   })\n   *   // start dragging after the user holds the pointer down\n   *   .on('hold', function (event) {\n   *     var interaction = event.interaction;\n   *\n   *     if (!interaction.interacting()) {\n   *       interaction.start({ name: 'drag' },\n   *                         event.interactable,\n   *                         event.currentTarget);\n   *     }\n   * });\n   * ```\n   *\n   * Start an action with the given Interactable and Element as tartgets. The\n   * action must be enabled for the target Interactable and an appropriate\n   * number of pointers must be held down - 1 for drag/resize, 2 for gesture.\n   *\n   * Use it with `interactable.<action>able({ manualStart: false })` to always\n   * [start actions manually](https://github.com/taye/interact.js/issues/114)\n   *\n   * @param {object} action   The action to be performed - drag, resize, etc.\n   * @param {Interactable} target  The Interactable to target\n   * @param {Element} element The DOM Element to target\n   * @return {object} interact\n   */\n  start (action, target, element) {\n    if (this.interacting()\n        || !this.pointerIsDown\n        || this.pointerIds.length < (action.name === 'gesture'? 2 : 1)) {\n      return;\n    }\n\n    // if this interaction had been removed after stopping\n    // add it back\n    if (scope.interactions.indexOf(this) === -1) {\n      scope.interactions.push(this);\n    }\n\n    utils.copyAction(this.prepared, action);\n    this.target         = target;\n    this.element        = element;\n\n    signals.fire('action-start', {\n      interaction: this,\n      event: this.downEvent,\n    });\n  }\n\n  pointerMove (pointer, event, eventTarget) {\n    if (!this.simulation) {\n      this.updatePointer(pointer);\n      utils.setCoords(this.curCoords, this.pointers);\n    }\n\n    const duplicateMove = (this.curCoords.page.x === this.prevCoords.page.x\n                           && this.curCoords.page.y === this.prevCoords.page.y\n                           && this.curCoords.client.x === this.prevCoords.client.x\n                           && this.curCoords.client.y === this.prevCoords.client.y);\n\n    let dx;\n    let dy;\n\n    // register movement greater than pointerMoveTolerance\n    if (this.pointerIsDown && !this.pointerWasMoved) {\n      dx = this.curCoords.client.x - this.startCoords.client.x;\n      dy = this.curCoords.client.y - this.startCoords.client.y;\n\n      this.pointerWasMoved = utils.hypot(dx, dy) > Interaction.pointerMoveTolerance;\n    }\n\n    const signalArg = {\n      pointer,\n      pointerIndex: this.getPointerIndex(pointer),\n      event,\n      eventTarget,\n      dx,\n      dy,\n      duplicate: duplicateMove,\n      interaction: this,\n      interactingBeforeMove: this.interacting(),\n    };\n\n    if (!duplicateMove) {\n      // set pointer coordinate, time changes and speeds\n      utils.setCoordDeltas(this.pointerDelta, this.prevCoords, this.curCoords);\n    }\n\n    signals.fire('move', signalArg);\n\n    if (!duplicateMove) {\n      // if interacting, fire an 'action-move' signal etc\n      if (this.interacting()) {\n        this.doMove(signalArg);\n      }\n\n      if (this.pointerWasMoved) {\n        utils.copyCoords(this.prevCoords, this.curCoords);\n      }\n    }\n  }\n\n  /**\n   * ```js\n   * interact(target)\n   *   .draggable(true)\n   *   .on('dragmove', function (event) {\n   *     if (someCondition) {\n   *       // change the snap settings\n   *       event.interactable.draggable({ snap: { targets: [] }});\n   *       // fire another move event with re-calculated snap\n   *       event.interaction.doMove();\n   *     }\n   *   });\n   * ```\n   *\n   * Force a move of the current action at the same coordinates. Useful if\n   * snap/restrict has been changed and you want a movement with the new\n   * settings.\n   */\n  doMove (signalArg) {\n    signalArg = utils.extend({\n      pointer: this.pointers[0],\n      event: this.prevEvent,\n      eventTarget: this._eventTarget,\n      interaction: this,\n    }, signalArg || {});\n\n    signals.fire('before-action-move', signalArg);\n\n    if (!this._dontFireMove) {\n      signals.fire('action-move', signalArg);\n    }\n\n    this._dontFireMove = false;\n  }\n\n  // End interact move events and stop auto-scroll unless simulation is running\n  pointerUp (pointer, event, eventTarget, curEventTarget) {\n    const pointerIndex = this.getPointerIndex(pointer);\n\n    signals.fire(/cancel$/i.test(event.type)? 'cancel' : 'up', {\n      pointer,\n      pointerIndex,\n      event,\n      eventTarget,\n      curEventTarget,\n      interaction: this,\n    });\n\n    if (!this.simulation) {\n      this.end(event);\n    }\n\n    this.pointerIsDown = false;\n    this.removePointer(pointer, event);\n  }\n\n  /**\n   * ```js\n   * interact(target)\n   *   .draggable(true)\n   *   .on('move', function (event) {\n   *     if (event.pageX > 1000) {\n   *       // end the current action\n   *       event.interaction.end();\n   *       // stop all further listeners from being called\n   *       event.stopImmediatePropagation();\n   *     }\n   *   });\n   * ```\n   *\n   * Stop the current action and fire an end event. Inertial movement does\n   * not happen.\n   *\n   * @param {PointerEvent} [event]\n   */\n  end (event) {\n    this._ending = true;\n\n    event = event || this.prevEvent;\n\n    if (this.interacting()) {\n      signals.fire('action-end', {\n        event,\n        interaction: this,\n      });\n    }\n\n    this.stop();\n    this._ending = false;\n  }\n\n  currentAction () {\n    return this._interacting? this.prepared.name: null;\n  }\n\n  interacting () {\n    return this._interacting;\n  }\n\n  /** */\n  stop () {\n    signals.fire('stop', { interaction: this });\n\n    if (this._interacting) {\n      signals.fire('stop-active', { interaction: this });\n      signals.fire('stop-' + this.prepared.name, { interaction: this });\n    }\n\n    this.target = this.element = null;\n\n    this._interacting = false;\n    this.prepared.name = this.prevEvent = null;\n  }\n\n  getPointerIndex (pointer) {\n    // mouse and pen interactions may have only one pointer\n    if (this.pointerType === 'mouse' || this.pointerType === 'pen') {\n      return 0;\n    }\n\n    return this.pointerIds.indexOf(utils.getPointerId(pointer));\n  }\n\n  updatePointer (pointer, event, down = event && /(down|start)$/i.test(event.type)) {\n    const id = utils.getPointerId(pointer);\n    let index = this.getPointerIndex(pointer);\n\n    if (index === -1) {\n      index = this.pointerIds.length;\n      this.pointerIds[index] = id;\n    }\n\n    if (down) {\n      signals.fire('update-pointer-down', {\n        pointer,\n        event,\n        down,\n        pointerId: id,\n        pointerIndex: index,\n        interaction: this,\n      });\n    }\n\n    this.pointers[index] = pointer;\n\n    return index;\n  }\n\n  removePointer (pointer, event) {\n    const index = this.getPointerIndex(pointer);\n\n    if (index === -1) { return; }\n\n    signals.fire('remove-pointer', {\n      pointer,\n      event,\n      pointerIndex: index,\n      interaction: this,\n    });\n\n    this.pointers   .splice(index, 1);\n    this.pointerIds .splice(index, 1);\n    this.downTargets.splice(index, 1);\n    this.downTimes  .splice(index, 1);\n  }\n\n  _updateEventTargets (target, currentTarget) {\n    this._eventTarget    = target;\n    this._curEventTarget = currentTarget;\n  }\n}\n\nfor (const method of methodNames) {\n  listeners[method] = doOnInteractions(method);\n}\n\nfunction doOnInteractions (method) {\n  return (function (event) {\n    const pointerType = utils.getPointerType(event);\n    const [eventTarget, curEventTarget] = utils.getEventTargets(event);\n    const matches = []; // [ [pointer, interaction], ...]\n\n    if (browser.supportsTouch && /touch/.test(event.type)) {\n      prevTouchTime = new Date().getTime();\n\n      for (const changedTouch of event.changedTouches) {\n        const pointer = changedTouch;\n        const interaction = finder.search(pointer, event.type, eventTarget);\n\n        matches.push([pointer, interaction || new Interaction({ pointerType })]);\n      }\n    }\n    else {\n      let invalidPointer = false;\n\n      if (!browser.supportsPointerEvent && /mouse/.test(event.type)) {\n        // ignore mouse events while touch interactions are active\n        for (let i = 0; i < scope.interactions.length && !invalidPointer; i++) {\n          invalidPointer = scope.interactions[i].pointerType !== 'mouse' && scope.interactions[i].pointerIsDown;\n        }\n\n        // try to ignore mouse events that are simulated by the browser\n        // after a touch event\n        invalidPointer = invalidPointer\n          || (new Date().getTime() - prevTouchTime < 500)\n          // on iOS and Firefox Mobile, MouseEvent.timeStamp is zero if simulated\n          || event.timeStamp === 0;\n      }\n\n      if (!invalidPointer) {\n        let interaction = finder.search(event, event.type, eventTarget);\n\n        if (!interaction) {\n          interaction = new Interaction({ pointerType });\n        }\n\n        matches.push([event, interaction]);\n      }\n    }\n\n    for (const [pointer, interaction] of matches) {\n      interaction._updateEventTargets(eventTarget, curEventTarget);\n      interaction[method](pointer, event, eventTarget, curEventTarget);\n    }\n  });\n}\n\nfunction endAll (event) {\n  for (const interaction of scope.interactions) {\n    interaction.end(event);\n    signals.fire('endall', { event, interaction });\n  }\n}\n\nconst docEvents = { /* 'eventType': listenerFunc */ };\nconst pEventTypes = browser.pEventTypes;\n\nif (domObjects.PointerEvent) {\n  docEvents[pEventTypes.down  ] = listeners.pointerDown;\n  docEvents[pEventTypes.move  ] = listeners.pointerMove;\n  docEvents[pEventTypes.up    ] = listeners.pointerUp;\n  docEvents[pEventTypes.cancel] = listeners.pointerUp;\n}\nelse {\n  docEvents.mousedown   = listeners.pointerDown;\n  docEvents.mousemove   = listeners.pointerMove;\n  docEvents.mouseup     = listeners.pointerUp;\n\n  docEvents.touchstart  = listeners.pointerDown;\n  docEvents.touchmove   = listeners.pointerMove;\n  docEvents.touchend    = listeners.pointerUp;\n  docEvents.touchcancel = listeners.pointerUp;\n}\n\ndocEvents.blur = endAll;\n\nfunction onDocSignal ({ doc }, signalName) {\n  const eventMethod = signalName.indexOf('add') === 0\n    ? events.add : events.remove;\n\n  // delegate event listener\n  for (const eventType in scope.delegatedEvents) {\n    eventMethod(doc, eventType, events.delegateListener);\n    eventMethod(doc, eventType, events.delegateUseCapture, true);\n  }\n\n  for (const eventType in docEvents) {\n    eventMethod(doc, eventType, docEvents[eventType], browser.isIOS ? { passive: false } : undefined);\n  }\n}\n\nsignals.on('update-pointer-down', ({ interaction, pointer, pointerId, pointerIndex, event, eventTarget, down }) => {\n  interaction.pointerIds[pointerIndex] = pointerId;\n  interaction.pointers[pointerIndex] = pointer;\n\n  if (down) {\n    interaction.pointerIsDown = true;\n  }\n\n  if (!interaction.interacting()) {\n    utils.setCoords(interaction.startCoords, interaction.pointers);\n\n    utils.copyCoords(interaction.curCoords , interaction.startCoords);\n    utils.copyCoords(interaction.prevCoords, interaction.startCoords);\n\n    interaction.downEvent                 = event;\n    interaction.downTimes[pointerIndex]   = interaction.curCoords.timeStamp;\n    interaction.downTargets[pointerIndex] = eventTarget || event && utils.getEventTargets(event)[0];\n    interaction.pointerWasMoved           = false;\n\n    utils.pointerExtend(interaction.downPointer, pointer);\n  }\n});\n\nscope.signals.on('add-document'   , onDocSignal);\nscope.signals.on('remove-document', onDocSignal);\n\nInteraction.pointerMoveTolerance = 1;\nInteraction.doOnInteractions = doOnInteractions;\nInteraction.endAll = endAll;\nInteraction.signals = signals;\nInteraction.docEvents = docEvents;\n\nscope.endAllInteractions = endAll;\n\nmodule.exports = Interaction;\n", "const Interaction   = require('../Interaction');\nconst InteractEvent = require('../InteractEvent');\n\nconst actions = {\n  firePrepared,\n  names: [],\n  methodDict: {},\n};\n\nInteraction.signals.on('action-start', function ({ interaction, event }) {\n  interaction._interacting = true;\n  firePrepared(interaction, event, 'start');\n});\n\nInteraction.signals.on('action-move', function ({ interaction, event, preEnd }) {\n  firePrepared(interaction, event, 'move', preEnd);\n\n  // if the action was ended in a listener\n  if (!interaction.interacting()) { return false; }\n});\n\nInteraction.signals.on('action-end', function ({ interaction, event }) {\n  firePrepared(interaction, event, 'end');\n});\n\nfunction firePrepared (interaction, event, phase, preEnd) {\n  const actionName = interaction.prepared.name;\n\n  const newEvent = new InteractEvent(interaction, event, actionName, phase, interaction.element, null, preEnd);\n\n  interaction.target.fire(newEvent);\n  interaction.prevEvent = newEvent;\n}\n\nmodule.exports = actions;\n", "const actions        = require('./base');\nconst utils          = require('../utils');\nconst InteractEvent  = require('../InteractEvent');\n/** @lends Interactable */\nconst Interactable   = require('../Interactable');\nconst Interaction    = require('../Interaction');\nconst defaultOptions = require('../defaultOptions');\n\nconst drag = {\n  defaults: {\n    enabled     : false,\n    mouseButtons: null,\n\n    origin    : null,\n    snap      : null,\n    restrict  : null,\n    inertia   : null,\n    autoScroll: null,\n\n    startAxis : 'xy',\n    lockAxis  : 'xy',\n  },\n\n  checker: function (pointer, event, interactable) {\n    const dragOptions = interactable.options.drag;\n\n    return dragOptions.enabled\n      ? { name: 'drag', axis: (dragOptions.lockAxis === 'start'\n                               ? dragOptions.startAxis\n                               : dragOptions.lockAxis)}\n      : null;\n  },\n\n  getCursor: function () {\n    return 'move';\n  },\n};\n\nInteraction.signals.on('before-action-move', function ({ interaction }) {\n  if (interaction.prepared.name !== 'drag') { return; }\n\n  const axis = interaction.prepared.axis;\n\n  if (axis === 'x') {\n    interaction.curCoords.page.y   = interaction.startCoords.page.y;\n    interaction.curCoords.client.y = interaction.startCoords.client.y;\n\n    interaction.pointerDelta.page.speed   = Math.abs(interaction.pointerDelta.page.vx);\n    interaction.pointerDelta.client.speed = Math.abs(interaction.pointerDelta.client.vx);\n    interaction.pointerDelta.client.vy = 0;\n    interaction.pointerDelta.page.vy   = 0;\n  }\n  else if (axis === 'y') {\n    interaction.curCoords.page.x   = interaction.startCoords.page.x;\n    interaction.curCoords.client.x = interaction.startCoords.client.x;\n\n    interaction.pointerDelta.page.speed   = Math.abs(interaction.pointerDelta.page.vy);\n    interaction.pointerDelta.client.speed = Math.abs(interaction.pointerDelta.client.vy);\n    interaction.pointerDelta.client.vx = 0;\n    interaction.pointerDelta.page.vx   = 0;\n  }\n});\n\n// dragmove\nInteractEvent.signals.on('new', function ({ iEvent, interaction }) {\n  if (iEvent.type !== 'dragmove') { return; }\n\n  const axis = interaction.prepared.axis;\n\n  if (axis === 'x') {\n    iEvent.pageY   = interaction.startCoords.page.y;\n    iEvent.clientY = interaction.startCoords.client.y;\n    iEvent.dy = 0;\n  }\n  else if (axis === 'y') {\n    iEvent.pageX   = interaction.startCoords.page.x;\n    iEvent.clientX = interaction.startCoords.client.x;\n    iEvent.dx = 0;\n  }\n});\n\n/**\n * ```js\n * interact(element).draggable({\n *     onstart: function (event) {},\n *     onmove : function (event) {},\n *     onend  : function (event) {},\n *\n *     // the axis in which the first movement must be\n *     // for the drag sequence to start\n *     // 'xy' by default - any direction\n *     startAxis: 'x' || 'y' || 'xy',\n *\n *     // 'xy' by default - don't restrict to one axis (move in any direction)\n *     // 'x' or 'y' to restrict movement to either axis\n *     // 'start' to restrict movement to the axis the drag started in\n *     lockAxis: 'x' || 'y' || 'xy' || 'start',\n *\n *     // max number of drags that can happen concurrently\n *     // with elements of this Interactable. Infinity by default\n *     max: Infinity,\n *\n *     // max number of drags that can target the same element+Interactable\n *     // 1 by default\n *     maxPerElement: 2\n * });\n *\n * var isDraggable = interact('element').draggable(); // true\n * ```\n *\n * Get or set whether drag actions can be performed on the target\n *\n * @param {boolean | object} [options] true/false or An object with event\n * listeners to be fired on drag events (object makes the Interactable\n * draggable)\n * @return {boolean | Interactable} boolean indicating if this can be the\n * target of drag events, or this Interctable\n */\nInteractable.prototype.draggable = function (options) {\n  if (utils.is.object(options)) {\n    this.options.drag.enabled = options.enabled === false? false: true;\n    this.setPerAction('drag', options);\n    this.setOnEvents('drag', options);\n\n    if (/^(xy|x|y|start)$/.test(options.lockAxis)) {\n      this.options.drag.lockAxis = options.lockAxis;\n    }\n    if (/^(xy|x|y)$/.test(options.startAxis)) {\n      this.options.drag.startAxis = options.startAxis;\n    }\n\n    return this;\n  }\n\n  if (utils.is.bool(options)) {\n    this.options.drag.enabled = options;\n\n    if (!options) {\n      this.ondragstart = this.ondragstart = this.ondragend = null;\n    }\n\n    return this;\n  }\n\n  return this.options.drag;\n};\n\nactions.drag = drag;\nactions.names.push('drag');\nutils.merge(Interactable.eventTypes, [\n  'dragstart',\n  'dragmove',\n  'draginertiastart',\n  'draginertiaresume',\n  'dragend',\n]);\nactions.methodDict.drag = 'draggable';\n\ndefaultOptions.drag = drag.defaults;\n\nmodule.exports = drag;\n", "const actions        = require('./base');\nconst utils          = require('../utils');\nconst scope          = require('../scope');\n/** @lends module:interact */\nconst interact       = require('../interact');\nconst InteractEvent  = require('../InteractEvent');\n/** @lends Interactable */\nconst Interactable   = require('../Interactable');\nconst Interaction    = require('../Interaction');\nconst defaultOptions = require('../defaultOptions');\n\nconst drop = {\n  defaults: {\n    enabled: false,\n    accept : null,\n    overlap: 'pointer',\n  },\n};\n\nlet dynamicDrop = false;\n\nInteraction.signals.on('action-start', function ({ interaction, event }) {\n  if (interaction.prepared.name !== 'drag') { return; }\n\n  // reset active dropzones\n  interaction.activeDrops.dropzones = [];\n  interaction.activeDrops.elements  = [];\n  interaction.activeDrops.rects     = [];\n\n  interaction.dropEvents = null;\n\n  if (!interaction.dynamicDrop) {\n    setActiveDrops(interaction.activeDrops, interaction.element);\n  }\n\n  const dragEvent = interaction.prevEvent;\n  const dropEvents = getDropEvents(interaction, event, dragEvent);\n\n  if (dropEvents.activate) {\n    fireActiveDrops(interaction.activeDrops, dropEvents.activate);\n  }\n});\n\nInteractEvent.signals.on('new', function ({ interaction, iEvent, event }) {\n  if (iEvent.type !== 'dragmove' && iEvent.type !== 'dragend') { return; }\n\n  const draggableElement = interaction.element;\n  const dragEvent = iEvent;\n  const dropResult = getDrop(dragEvent, event, draggableElement);\n\n  interaction.dropTarget  = dropResult.dropzone;\n  interaction.dropElement = dropResult.element;\n\n  interaction.dropEvents = getDropEvents(interaction, event, dragEvent);\n});\n\nInteraction.signals.on('action-move', function ({ interaction }) {\n  if (interaction.prepared.name !== 'drag') { return; }\n\n  fireDropEvents(interaction, interaction.dropEvents);\n});\n\nInteraction.signals.on('action-end', function ({ interaction }) {\n  if (interaction.prepared.name === 'drag') {\n    fireDropEvents(interaction, interaction.dropEvents);\n  }\n});\n\nInteraction.signals.on('stop-drag', function ({ interaction }) {\n  interaction.activeDrops = {\n    dropzones: null,\n    elements: null,\n    rects: null,\n  };\n\n  interaction.dropEvents = null;\n});\n\nfunction collectDrops (activeDrops, element) {\n  const drops = [];\n  const elements = [];\n\n  // collect all dropzones and their elements which qualify for a drop\n  for (const current of scope.interactables) {\n    if (!current.options.drop.enabled) { continue; }\n\n    const accept = current.options.drop.accept;\n\n    // test the draggable element against the dropzone's accept setting\n    if ((utils.is.element(accept) && accept !== element)\n        || (utils.is.string(accept)\n        && !utils.matchesSelector(element, accept))) {\n\n      continue;\n    }\n\n    // query for new elements if necessary\n    const dropElements = utils.is.string(current.target)\n      ? current._context.querySelectorAll(current.target)\n      : [current.target];\n\n    for (const currentElement of dropElements) {\n      if (currentElement !== element) {\n        drops.push(current);\n        elements.push(currentElement);\n      }\n    }\n  }\n\n  return {\n    elements,\n    dropzones: drops,\n  };\n}\n\nfunction fireActiveDrops (activeDrops, event) {\n  let prevElement;\n\n  // loop through all active dropzones and trigger event\n  for (let i = 0; i < activeDrops.dropzones.length; i++) {\n    const current = activeDrops.dropzones[i];\n    const currentElement = activeDrops.elements [i];\n\n    // prevent trigger of duplicate events on same element\n    if (currentElement !== prevElement) {\n      // set current element as event target\n      event.target = currentElement;\n      current.fire(event);\n    }\n    prevElement = currentElement;\n  }\n}\n\n// Collect a new set of possible drops and save them in activeDrops.\n// setActiveDrops should always be called when a drag has just started or a\n// drag event happens while dynamicDrop is true\nfunction setActiveDrops (activeDrops, dragElement) {\n  // get dropzones and their elements that could receive the draggable\n  const possibleDrops = collectDrops(activeDrops, dragElement);\n\n  activeDrops.dropzones = possibleDrops.dropzones;\n  activeDrops.elements  = possibleDrops.elements;\n  activeDrops.rects     = [];\n\n  for (let i = 0; i < activeDrops.dropzones.length; i++) {\n    activeDrops.rects[i] = activeDrops.dropzones[i].getRect(activeDrops.elements[i]);\n  }\n}\n\nfunction getDrop (dragEvent, event, dragElement) {\n  const interaction = dragEvent.interaction;\n  const validDrops = [];\n\n  if (dynamicDrop) {\n    setActiveDrops(interaction.activeDrops, dragElement);\n  }\n\n  // collect all dropzones and their elements which qualify for a drop\n  for (let j = 0; j < interaction.activeDrops.dropzones.length; j++) {\n    const current        = interaction.activeDrops.dropzones[j];\n    const currentElement = interaction.activeDrops.elements [j];\n    const rect           = interaction.activeDrops.rects    [j];\n\n    validDrops.push(current.dropCheck(dragEvent, event, interaction.target, dragElement, currentElement, rect)\n      ? currentElement\n      : null);\n  }\n\n  // get the most appropriate dropzone based on DOM depth and order\n  const dropIndex = utils.indexOfDeepestElement(validDrops);\n\n  return {\n    dropzone: interaction.activeDrops.dropzones[dropIndex] || null,\n    element : interaction.activeDrops.elements [dropIndex] || null,\n  };\n}\n\nfunction getDropEvents (interaction, pointerEvent, dragEvent) {\n  const dropEvents = {\n    enter     : null,\n    leave     : null,\n    activate  : null,\n    deactivate: null,\n    move      : null,\n    drop      : null,\n  };\n\n  const tmpl = {\n    dragEvent,\n    interaction,\n    target       : interaction.dropElement,\n    dropzone     : interaction.dropTarget,\n    relatedTarget: dragEvent.target,\n    draggable    : dragEvent.interactable,\n    timeStamp    : dragEvent.timeStamp,\n  };\n\n  if (interaction.dropElement !== interaction.prevDropElement) {\n    // if there was a prevDropTarget, create a dragleave event\n    if (interaction.prevDropTarget) {\n      dropEvents.leave = utils.extend({ type: 'dragleave' }, tmpl);\n\n      dragEvent.dragLeave    = dropEvents.leave.target   = interaction.prevDropElement;\n      dragEvent.prevDropzone = dropEvents.leave.dropzone = interaction.prevDropTarget;\n    }\n    // if the dropTarget is not null, create a dragenter event\n    if (interaction.dropTarget) {\n      dropEvents.enter = {\n        dragEvent,\n        interaction,\n        target       : interaction.dropElement,\n        dropzone     : interaction.dropTarget,\n        relatedTarget: dragEvent.target,\n        draggable    : dragEvent.interactable,\n        timeStamp    : dragEvent.timeStamp,\n        type         : 'dragenter',\n      };\n\n      dragEvent.dragEnter = interaction.dropElement;\n      dragEvent.dropzone = interaction.dropTarget;\n    }\n  }\n\n  if (dragEvent.type === 'dragend' && interaction.dropTarget) {\n    dropEvents.drop = utils.extend({ type: 'drop' }, tmpl);\n\n    dragEvent.dropzone = interaction.dropTarget;\n    dragEvent.relatedTarget = interaction.dropElement;\n  }\n  if (dragEvent.type === 'dragstart') {\n    dropEvents.activate = utils.extend({ type: 'dropactivate' }, tmpl);\n\n    dropEvents.activate.target   = null;\n    dropEvents.activate.dropzone = null;\n  }\n  if (dragEvent.type === 'dragend') {\n    dropEvents.deactivate = utils.extend({ type: 'dropdeactivate' }, tmpl);\n\n    dropEvents.deactivate.target   = null;\n    dropEvents.deactivate.dropzone = null;\n  }\n  if (dragEvent.type === 'dragmove' && interaction.dropTarget) {\n    dropEvents.move = utils.extend({\n      dragmove     : dragEvent,\n      type         : 'dropmove',\n    }, tmpl);\n\n    dragEvent.dropzone = interaction.dropTarget;\n  }\n\n  return dropEvents;\n}\n\nfunction fireDropEvents (interaction, dropEvents) {\n  const {\n    activeDrops,\n    prevDropTarget,\n    dropTarget,\n    dropElement,\n  } = interaction;\n\n  if (dropEvents.leave) { prevDropTarget.fire(dropEvents.leave); }\n  if (dropEvents.move ) {     dropTarget.fire(dropEvents.move ); }\n  if (dropEvents.enter) {     dropTarget.fire(dropEvents.enter); }\n  if (dropEvents.drop ) {     dropTarget.fire(dropEvents.drop ); }\n  if (dropEvents.deactivate) {\n    fireActiveDrops(activeDrops, dropEvents.deactivate);\n  }\n\n  interaction.prevDropTarget  = dropTarget;\n  interaction.prevDropElement = dropElement;\n}\n\n/**\n * ```js\n * interact(target)\n * .dropChecker(function(dragEvent,         // related dragmove or dragend event\n *                       event,             // TouchEvent/PointerEvent/MouseEvent\n *                       dropped,           // bool result of the default checker\n *                       dropzone,          // dropzone Interactable\n *                       dropElement,       // dropzone elemnt\n *                       draggable,         // draggable Interactable\n *                       draggableElement) {// draggable element\n *\n *   return dropped && event.target.hasAttribute('allow-drop');\n * }\n * ```\n *\n * ```js\n * interact('.drop').dropzone({\n *   accept: '.can-drop' || document.getElementById('single-drop'),\n *   overlap: 'pointer' || 'center' || zeroToOne\n * }\n * ```\n *\n * Returns or sets whether draggables can be dropped onto this target to\n * trigger drop events\n *\n * Dropzones can receive the following events:\n *  - `dropactivate` and `dropdeactivate` when an acceptable drag starts and ends\n *  - `dragenter` and `dragleave` when a draggable enters and leaves the dropzone\n *  - `dragmove` when a draggable that has entered the dropzone is moved\n *  - `drop` when a draggable is dropped into this dropzone\n *\n * Use the `accept` option to allow only elements that match the given CSS\n * selector or element. The value can be:\n *\n *  - **an Element** - only that element can be dropped into this dropzone.\n *  - **a string**, - the element being dragged must match it as a CSS selector.\n *  - **`null`** - accept options is cleared - it accepts any element.\n *\n * Use the `overlap` option to set how drops are checked for. The allowed\n * values are:\n *\n *   - `'pointer'`, the pointer must be over the dropzone (default)\n *   - `'center'`, the draggable element's center must be over the dropzone\n *   - a number from 0-1 which is the `(intersection area) / (draggable area)`.\n *   e.g. `0.5` for drop to happen when half of the area of the draggable is\n *   over the dropzone\n *\n * Use the `checker` option to specify a function to check if a dragged element\n * is over this Interactable.\n *\n * @param {boolean | object | null} [options] The new options to be set.\n * @return {boolean | Interactable} The current setting or this Interactable\n */\nInteractable.prototype.dropzone = function (options) {\n  if (utils.is.object(options)) {\n    this.options.drop.enabled = options.enabled === false? false: true;\n\n    if (utils.is.function(options.ondrop)          ) { this.events.ondrop           = options.ondrop          ; }\n    if (utils.is.function(options.ondropactivate)  ) { this.events.ondropactivate   = options.ondropactivate  ; }\n    if (utils.is.function(options.ondropdeactivate)) { this.events.ondropdeactivate = options.ondropdeactivate; }\n    if (utils.is.function(options.ondragenter)     ) { this.events.ondragenter      = options.ondragenter     ; }\n    if (utils.is.function(options.ondragleave)     ) { this.events.ondragleave      = options.ondragleave     ; }\n    if (utils.is.function(options.ondropmove)      ) { this.events.ondropmove       = options.ondropmove      ; }\n\n    if (/^(pointer|center)$/.test(options.overlap)) {\n      this.options.drop.overlap = options.overlap;\n    }\n    else if (utils.is.number(options.overlap)) {\n      this.options.drop.overlap = Math.max(Math.min(1, options.overlap), 0);\n    }\n    if ('accept' in options) {\n      this.options.drop.accept = options.accept;\n    }\n    if ('checker' in options) {\n      this.options.drop.checker = options.checker;\n    }\n\n\n    return this;\n  }\n\n  if (utils.is.bool(options)) {\n    this.options.drop.enabled = options;\n\n    if (!options) {\n      this.ondragenter = this.ondragleave = this.ondrop\n        = this.ondropactivate = this.ondropdeactivate = null;\n    }\n\n    return this;\n  }\n\n  return this.options.drop;\n};\n\nInteractable.prototype.dropCheck = function (dragEvent, event, draggable, draggableElement, dropElement, rect) {\n  let dropped = false;\n\n  // if the dropzone has no rect (eg. display: none)\n  // call the custom dropChecker or just return false\n  if (!(rect = rect || this.getRect(dropElement))) {\n    return (this.options.drop.checker\n      ? this.options.drop.checker(dragEvent, event, dropped, this, dropElement, draggable, draggableElement)\n      : false);\n  }\n\n  const dropOverlap = this.options.drop.overlap;\n\n  if (dropOverlap === 'pointer') {\n    const origin = utils.getOriginXY(draggable, draggableElement, 'drag');\n    const page = utils.getPageXY(dragEvent);\n\n    page.x += origin.x;\n    page.y += origin.y;\n\n    const horizontal = (page.x > rect.left) && (page.x < rect.right);\n    const vertical   = (page.y > rect.top ) && (page.y < rect.bottom);\n\n    dropped = horizontal && vertical;\n  }\n\n  const dragRect = draggable.getRect(draggableElement);\n\n  if (dragRect && dropOverlap === 'center') {\n    const cx = dragRect.left + dragRect.width  / 2;\n    const cy = dragRect.top  + dragRect.height / 2;\n\n    dropped = cx >= rect.left && cx <= rect.right && cy >= rect.top && cy <= rect.bottom;\n  }\n\n  if (dragRect && utils.is.number(dropOverlap)) {\n    const overlapArea  = (Math.max(0, Math.min(rect.right , dragRect.right ) - Math.max(rect.left, dragRect.left))\n                          * Math.max(0, Math.min(rect.bottom, dragRect.bottom) - Math.max(rect.top , dragRect.top )));\n\n    const overlapRatio = overlapArea / (dragRect.width * dragRect.height);\n\n    dropped = overlapRatio >= dropOverlap;\n  }\n\n  if (this.options.drop.checker) {\n    dropped = this.options.drop.checker(dragEvent, event, dropped, this, dropElement, draggable, draggableElement);\n  }\n\n  return dropped;\n};\n\nInteractable.signals.on('unset', function ({ interactable }) {\n  interactable.dropzone(false);\n});\n\nInteractable.settingsMethods.push('dropChecker');\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.dropTarget      = null; // the dropzone a drag target might be dropped into\n  interaction.dropElement     = null; // the element at the time of checking\n  interaction.prevDropTarget  = null; // the dropzone that was recently dragged away from\n  interaction.prevDropElement = null; // the element at the time of checking\n  interaction.dropEvents      = null; // the dropEvents related to the current drag event\n\n  interaction.activeDrops = {\n    dropzones: [],      // the dropzones that are mentioned below\n    elements : [],      // elements of dropzones that accept the target draggable\n    rects    : [],      // the rects of the elements mentioned above\n  };\n\n});\n\nInteraction.signals.on('stop', function ({ interaction }) {\n  interaction.dropTarget = interaction.dropElement =\n    interaction.prevDropTarget = interaction.prevDropElement = null;\n});\n\n/**\n * Returns or sets whether the dimensions of dropzone elements are calculated\n * on every dragmove or only on dragstart for the default dropChecker\n *\n * @param {boolean} [newValue] True to check on each move. False to check only\n * before start\n * @return {boolean | interact} The current setting or interact\n */\ninteract.dynamicDrop = function (newValue) {\n  if (utils.is.bool(newValue)) {\n    //if (dragging && dynamicDrop !== newValue && !newValue) {\n      //calcRects(dropzones);\n    //}\n\n    dynamicDrop = newValue;\n\n    return interact;\n  }\n  return dynamicDrop;\n};\n\nutils.merge(Interactable.eventTypes, [\n  'dragenter',\n  'dragleave',\n  'dropactivate',\n  'dropdeactivate',\n  'dropmove',\n  'drop',\n]);\nactions.methodDict.drop = 'dropzone';\n\ndefaultOptions.drop = drop.defaults;\n\nmodule.exports = drop;\n", "const actions        = require('./base');\nconst utils          = require('../utils');\nconst InteractEvent  = require('../InteractEvent');\nconst Interactable   = require('../Interactable');\nconst Interaction    = require('../Interaction');\nconst defaultOptions = require('../defaultOptions');\n\nconst gesture = {\n  defaults: {\n    enabled : false,\n    origin  : null,\n    restrict: null,\n  },\n\n  checker: function (pointer, event, interactable, element, interaction) {\n    if (interaction.pointerIds.length >= 2) {\n      return { name: 'gesture' };\n    }\n\n    return null;\n  },\n\n  getCursor: function () {\n    return '';\n  },\n};\n\nInteractEvent.signals.on('new', function ({ iEvent, interaction }) {\n  if (iEvent.type !== 'gesturestart') { return; }\n  iEvent.ds = 0;\n\n  interaction.gesture.startDistance = interaction.gesture.prevDistance = iEvent.distance;\n  interaction.gesture.startAngle = interaction.gesture.prevAngle = iEvent.angle;\n  interaction.gesture.scale = 1;\n});\n\nInteractEvent.signals.on('new', function ({ iEvent, interaction }) {\n  if (iEvent.type !== 'gesturemove') { return; }\n\n  iEvent.ds = iEvent.scale - interaction.gesture.scale;\n\n  interaction.target.fire(iEvent);\n\n  interaction.gesture.prevAngle = iEvent.angle;\n  interaction.gesture.prevDistance = iEvent.distance;\n\n  if (iEvent.scale !== Infinity\n      && iEvent.scale !== null\n      && iEvent.scale !== undefined\n      && !isNaN(iEvent.scale)) {\n\n    interaction.gesture.scale = iEvent.scale;\n  }\n});\n\n/**\n * ```js\n * interact(element).gesturable({\n *     onstart: function (event) {},\n *     onmove : function (event) {},\n *     onend  : function (event) {},\n *\n *     // limit multiple gestures.\n *     // See the explanation in {@link Interactable.draggable} example\n *     max: Infinity,\n *     maxPerElement: 1,\n * });\n *\n * var isGestureable = interact(element).gesturable();\n * ```\n *\n * Gets or sets whether multitouch gestures can be performed on the target\n *\n * @param {boolean | object} [options] true/false or An object with event\n * listeners to be fired on gesture events (makes the Interactable gesturable)\n * @return {boolean | Interactable} A boolean indicating if this can be the\n * target of gesture events, or this Interactable\n */\nInteractable.prototype.gesturable = function (options) {\n  if (utils.is.object(options)) {\n    this.options.gesture.enabled = options.enabled === false? false: true;\n    this.setPerAction('gesture', options);\n    this.setOnEvents('gesture', options);\n\n    return this;\n  }\n\n  if (utils.is.bool(options)) {\n    this.options.gesture.enabled = options;\n\n    if (!options) {\n      this.ongesturestart = this.ongesturestart = this.ongestureend = null;\n    }\n\n    return this;\n  }\n\n  return this.options.gesture;\n};\n\nInteractEvent.signals.on('set-delta', function ({ interaction, iEvent, action, event, starting, ending, deltaSource }) {\n  if (action !== 'gesture') { return; }\n\n  const pointers = interaction.pointers;\n\n  iEvent.touches = [pointers[0], pointers[1]];\n\n  if (starting) {\n    iEvent.distance = utils.touchDistance(pointers, deltaSource);\n    iEvent.box      = utils.touchBBox(pointers);\n    iEvent.scale    = 1;\n    iEvent.ds       = 0;\n    iEvent.angle    = utils.touchAngle(pointers, undefined, deltaSource);\n    iEvent.da       = 0;\n  }\n  else if (ending || event instanceof InteractEvent) {\n    iEvent.distance = interaction.prevEvent.distance;\n    iEvent.box      = interaction.prevEvent.box;\n    iEvent.scale    = interaction.prevEvent.scale;\n    iEvent.ds       = iEvent.scale - 1;\n    iEvent.angle    = interaction.prevEvent.angle;\n    iEvent.da       = iEvent.angle - interaction.gesture.startAngle;\n  }\n  else {\n    iEvent.distance = utils.touchDistance(pointers, deltaSource);\n    iEvent.box      = utils.touchBBox(pointers);\n    iEvent.scale    = iEvent.distance / interaction.gesture.startDistance;\n    iEvent.angle    = utils.touchAngle(pointers, interaction.gesture.prevAngle, deltaSource);\n\n    iEvent.ds = iEvent.scale - interaction.gesture.prevScale;\n    iEvent.da = iEvent.angle - interaction.gesture.prevAngle;\n  }\n});\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.gesture = {\n    start: { x: 0, y: 0 },\n\n    startDistance: 0,   // distance between two touches of touchStart\n    prevDistance : 0,\n    distance     : 0,\n\n    scale: 1,           // gesture.distance / gesture.startDistance\n\n    startAngle: 0,      // angle of line joining two touches\n    prevAngle : 0,      // angle of the previous gesture event\n  };\n});\n\nactions.gesture = gesture;\nactions.names.push('gesture');\nutils.merge(Interactable.eventTypes, [\n  'gesturestart',\n  'gesturemove',\n  'gestureend',\n]);\nactions.methodDict.gesture = 'gesturable';\n\ndefaultOptions.gesture = gesture.defaults;\n\nmodule.exports = gesture;\n", "const actions        = require('./base');\nconst utils          = require('../utils');\nconst browser        = require('../utils/browser');\nconst InteractEvent  = require('../InteractEvent');\n/** @lends Interactable */\nconst Interactable   = require('../Interactable');\nconst Interaction    = require('../Interaction');\nconst defaultOptions = require('../defaultOptions');\n\n// Less Precision with touch input\nconst defaultMargin = browser.supportsTouch || browser.supportsPointerEvent? 20: 10;\n\nconst resize = {\n  defaults: {\n    enabled     : false,\n    mouseButtons: null,\n\n    origin    : null,\n    snap      : null,\n    restrict  : null,\n    inertia   : null,\n    autoScroll: null,\n\n    square: false,\n    preserveAspectRatio: false,\n    axis: 'xy',\n\n    // use default margin\n    margin: NaN,\n\n    // object with props left, right, top, bottom which are\n    // true/false values to resize when the pointer is over that edge,\n    // CSS selectors to match the handles for each direction\n    // or the Elements for each handle\n    edges: null,\n\n    // a value of 'none' will limit the resize rect to a minimum of 0x0\n    // 'negate' will alow the rect to have negative width/height\n    // 'reposition' will keep the width/height positive by swapping\n    // the top and bottom edges and/or swapping the left and right edges\n    invert: 'none',\n  },\n\n  checker: function (pointer, event, interactable, element, interaction, rect) {\n    if (!rect) { return null; }\n\n    const page = utils.extend({}, interaction.curCoords.page);\n    const options = interactable.options;\n\n    if (options.resize.enabled) {\n      const resizeOptions = options.resize;\n      const resizeEdges = { left: false, right: false, top: false, bottom: false };\n\n      // if using resize.edges\n      if (utils.is.object(resizeOptions.edges)) {\n        for (const edge in resizeEdges) {\n          resizeEdges[edge] = checkResizeEdge(edge,\n                                              resizeOptions.edges[edge],\n                                              page,\n                                              interaction._eventTarget,\n                                              element,\n                                              rect,\n                                              resizeOptions.margin || defaultMargin);\n        }\n\n        resizeEdges.left = resizeEdges.left && !resizeEdges.right;\n        resizeEdges.top  = resizeEdges.top  && !resizeEdges.bottom;\n\n        if (resizeEdges.left || resizeEdges.right || resizeEdges.top || resizeEdges.bottom) {\n          return {\n            name: 'resize',\n            edges: resizeEdges,\n          };\n        }\n      }\n      else {\n        const right  = options.resize.axis !== 'y' && page.x > (rect.right  - defaultMargin);\n        const bottom = options.resize.axis !== 'x' && page.y > (rect.bottom - defaultMargin);\n\n        if (right || bottom) {\n          return {\n            name: 'resize',\n            axes: (right? 'x' : '') + (bottom? 'y' : ''),\n          };\n        }\n      }\n    }\n\n    return null;\n  },\n\n  cursors: (browser.isIe9 ? {\n    x : 'e-resize',\n    y : 's-resize',\n    xy: 'se-resize',\n\n    top        : 'n-resize',\n    left       : 'w-resize',\n    bottom     : 's-resize',\n    right      : 'e-resize',\n    topleft    : 'se-resize',\n    bottomright: 'se-resize',\n    topright   : 'ne-resize',\n    bottomleft : 'ne-resize',\n  } : {\n    x : 'ew-resize',\n    y : 'ns-resize',\n    xy: 'nwse-resize',\n\n    top        : 'ns-resize',\n    left       : 'ew-resize',\n    bottom     : 'ns-resize',\n    right      : 'ew-resize',\n    topleft    : 'nwse-resize',\n    bottomright: 'nwse-resize',\n    topright   : 'nesw-resize',\n    bottomleft : 'nesw-resize',\n  }),\n\n  getCursor: function (action) {\n    if (action.axis) {\n      return resize.cursors[action.name + action.axis];\n    }\n    else if (action.edges) {\n      let cursorKey = '';\n      const edgeNames = ['top', 'bottom', 'left', 'right'];\n\n      for (let i = 0; i < 4; i++) {\n        if (action.edges[edgeNames[i]]) {\n          cursorKey += edgeNames[i];\n        }\n      }\n\n      return resize.cursors[cursorKey];\n    }\n  },\n};\n\n// resizestart\nInteractEvent.signals.on('new', function ({ iEvent, interaction }) {\n  if (iEvent.type !== 'resizestart' || !interaction.prepared.edges) {\n    return;\n  }\n\n  const startRect = interaction.target.getRect(interaction.element);\n  const resizeOptions = interaction.target.options.resize;\n\n  /*\n   * When using the `resizable.square` or `resizable.preserveAspectRatio` options, resizing from one edge\n   * will affect another. E.g. with `resizable.square`, resizing to make the right edge larger will make\n   * the bottom edge larger by the same amount. We call these 'linked' edges. Any linked edges will depend\n   * on the active edges and the edge being interacted with.\n   */\n  if (resizeOptions.square || resizeOptions.preserveAspectRatio) {\n    const linkedEdges = utils.extend({}, interaction.prepared.edges);\n\n    linkedEdges.top    = linkedEdges.top    || (linkedEdges.left   && !linkedEdges.bottom);\n    linkedEdges.left   = linkedEdges.left   || (linkedEdges.top    && !linkedEdges.right );\n    linkedEdges.bottom = linkedEdges.bottom || (linkedEdges.right  && !linkedEdges.top   );\n    linkedEdges.right  = linkedEdges.right  || (linkedEdges.bottom && !linkedEdges.left  );\n\n    interaction.prepared._linkedEdges = linkedEdges;\n  }\n  else {\n    interaction.prepared._linkedEdges = null;\n  }\n\n  // if using `resizable.preserveAspectRatio` option, record aspect ratio at the start of the resize\n  if (resizeOptions.preserveAspectRatio) {\n    interaction.resizeStartAspectRatio = startRect.width / startRect.height;\n  }\n\n  interaction.resizeRects = {\n    start     : startRect,\n    current   : utils.extend({}, startRect),\n    inverted  : utils.extend({}, startRect),\n    previous  : utils.extend({}, startRect),\n    delta     : {\n      left: 0, right : 0, width : 0,\n      top : 0, bottom: 0, height: 0,\n    },\n  };\n\n  iEvent.rect = interaction.resizeRects.inverted;\n  iEvent.deltaRect = interaction.resizeRects.delta;\n});\n\n// resizemove\nInteractEvent.signals.on('new', function ({ iEvent, phase, interaction }) {\n  if (phase !== 'move' || !interaction.prepared.edges) { return; }\n\n  const resizeOptions = interaction.target.options.resize;\n  const invert = resizeOptions.invert;\n  const invertible = invert === 'reposition' || invert === 'negate';\n\n  let edges = interaction.prepared.edges;\n\n  const start      = interaction.resizeRects.start;\n  const current    = interaction.resizeRects.current;\n  const inverted   = interaction.resizeRects.inverted;\n  const delta      = interaction.resizeRects.delta;\n  const previous   = utils.extend(interaction.resizeRects.previous, inverted);\n  const originalEdges = edges;\n\n  let dx = iEvent.dx;\n  let dy = iEvent.dy;\n\n  if (resizeOptions.preserveAspectRatio || resizeOptions.square) {\n    // `resize.preserveAspectRatio` takes precedence over `resize.square`\n    const startAspectRatio = resizeOptions.preserveAspectRatio\n      ? interaction.resizeStartAspectRatio\n      : 1;\n\n    edges = interaction.prepared._linkedEdges;\n\n    if ((originalEdges.left && originalEdges.bottom)\n        || (originalEdges.right && originalEdges.top)) {\n      dy = -dx / startAspectRatio;\n    }\n    else if (originalEdges.left || originalEdges.right ) { dy = dx / startAspectRatio; }\n    else if (originalEdges.top  || originalEdges.bottom) { dx = dy * startAspectRatio; }\n  }\n\n  // update the 'current' rect without modifications\n  if (edges.top   ) { current.top    += dy; }\n  if (edges.bottom) { current.bottom += dy; }\n  if (edges.left  ) { current.left   += dx; }\n  if (edges.right ) { current.right  += dx; }\n\n  if (invertible) {\n    // if invertible, copy the current rect\n    utils.extend(inverted, current);\n\n    if (invert === 'reposition') {\n      // swap edge values if necessary to keep width/height positive\n      let swap;\n\n      if (inverted.top > inverted.bottom) {\n        swap = inverted.top;\n\n        inverted.top = inverted.bottom;\n        inverted.bottom = swap;\n      }\n      if (inverted.left > inverted.right) {\n        swap = inverted.left;\n\n        inverted.left = inverted.right;\n        inverted.right = swap;\n      }\n    }\n  }\n  else {\n    // if not invertible, restrict to minimum of 0x0 rect\n    inverted.top    = Math.min(current.top, start.bottom);\n    inverted.bottom = Math.max(current.bottom, start.top);\n    inverted.left   = Math.min(current.left, start.right);\n    inverted.right  = Math.max(current.right, start.left);\n  }\n\n  inverted.width  = inverted.right  - inverted.left;\n  inverted.height = inverted.bottom - inverted.top ;\n\n  for (const edge in inverted) {\n    delta[edge] = inverted[edge] - previous[edge];\n  }\n\n  iEvent.edges = interaction.prepared.edges;\n  iEvent.rect = inverted;\n  iEvent.deltaRect = delta;\n});\n\n/**\n * ```js\n * interact(element).resizable({\n *   onstart: function (event) {},\n *   onmove : function (event) {},\n *   onend  : function (event) {},\n *\n *   edges: {\n *     top   : true,       // Use pointer coords to check for resize.\n *     left  : false,      // Disable resizing from left edge.\n *     bottom: '.resize-s',// Resize if pointer target matches selector\n *     right : handleEl    // Resize if pointer target is the given Element\n *   },\n *\n *     // Width and height can be adjusted independently. When `true`, width and\n *     // height are adjusted at a 1:1 ratio.\n *     square: false,\n *\n *     // Width and height can be adjusted independently. When `true`, width and\n *     // height maintain the aspect ratio they had when resizing started.\n *     preserveAspectRatio: false,\n *\n *   // a value of 'none' will limit the resize rect to a minimum of 0x0\n *   // 'negate' will allow the rect to have negative width/height\n *   // 'reposition' will keep the width/height positive by swapping\n *   // the top and bottom edges and/or swapping the left and right edges\n *   invert: 'none' || 'negate' || 'reposition'\n *\n *   // limit multiple resizes.\n *   // See the explanation in the {@link Interactable.draggable} example\n *   max: Infinity,\n *   maxPerElement: 1,\n * });\n *\n * var isResizeable = interact(element).resizable();\n * ```\n *\n * Gets or sets whether resize actions can be performed on the target\n *\n * @param {boolean | object} [options] true/false or An object with event\n * listeners to be fired on resize events (object makes the Interactable\n * resizable)\n * @return {boolean | Interactable} A boolean indicating if this can be the\n * target of resize elements, or this Interactable\n */\nInteractable.prototype.resizable = function (options) {\n  if (utils.is.object(options)) {\n    this.options.resize.enabled = options.enabled === false? false: true;\n    this.setPerAction('resize', options);\n    this.setOnEvents('resize', options);\n\n    if (/^x$|^y$|^xy$/.test(options.axis)) {\n      this.options.resize.axis = options.axis;\n    }\n    else if (options.axis === null) {\n      this.options.resize.axis = defaultOptions.resize.axis;\n    }\n\n    if (utils.is.bool(options.preserveAspectRatio)) {\n      this.options.resize.preserveAspectRatio = options.preserveAspectRatio;\n    }\n    else if (utils.is.bool(options.square)) {\n      this.options.resize.square = options.square;\n    }\n\n    return this;\n  }\n  if (utils.is.bool(options)) {\n    this.options.resize.enabled = options;\n\n    if (!options) {\n      this.onresizestart = this.onresizestart = this.onresizeend = null;\n    }\n\n    return this;\n  }\n  return this.options.resize;\n};\n\nfunction checkResizeEdge (name, value, page, element, interactableElement, rect, margin) {\n  // false, '', undefined, null\n  if (!value) { return false; }\n\n  // true value, use pointer coords and element rect\n  if (value === true) {\n    // if dimensions are negative, \"switch\" edges\n    const width  = utils.is.number(rect.width )? rect.width  : rect.right  - rect.left;\n    const height = utils.is.number(rect.height)? rect.height : rect.bottom - rect.top ;\n\n    if (width < 0) {\n      if      (name === 'left' ) { name = 'right'; }\n      else if (name === 'right') { name = 'left' ; }\n    }\n    if (height < 0) {\n      if      (name === 'top'   ) { name = 'bottom'; }\n      else if (name === 'bottom') { name = 'top'   ; }\n    }\n\n    if (name === 'left'  ) { return page.x < ((width  >= 0? rect.left: rect.right ) + margin); }\n    if (name === 'top'   ) { return page.y < ((height >= 0? rect.top : rect.bottom) + margin); }\n\n    if (name === 'right' ) { return page.x > ((width  >= 0? rect.right : rect.left) - margin); }\n    if (name === 'bottom') { return page.y > ((height >= 0? rect.bottom: rect.top ) - margin); }\n  }\n\n  // the remaining checks require an element\n  if (!utils.is.element(element)) { return false; }\n\n  return utils.is.element(value)\n  // the value is an element to use as a resize handle\n    ? value === element\n    // otherwise check if element matches value as selector\n    : utils.matchesUpTo(element, value, interactableElement);\n}\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.resizeAxes = 'xy';\n});\n\nInteractEvent.signals.on('set-delta', function ({ interaction, iEvent, action }) {\n  if (action !== 'resize' || !interaction.resizeAxes) { return; }\n\n  const options = interaction.target.options;\n\n  if (options.resize.square) {\n    if (interaction.resizeAxes === 'y') {\n      iEvent.dx = iEvent.dy;\n    }\n    else {\n      iEvent.dy = iEvent.dx;\n    }\n    iEvent.axes = 'xy';\n  }\n  else {\n    iEvent.axes = interaction.resizeAxes;\n\n    if (interaction.resizeAxes === 'x') {\n      iEvent.dy = 0;\n    }\n    else if (interaction.resizeAxes === 'y') {\n      iEvent.dx = 0;\n    }\n  }\n});\n\nactions.resize = resize;\nactions.names.push('resize');\nutils.merge(Interactable.eventTypes, [\n  'resizestart',\n  'resizemove',\n  'resizeinertiastart',\n  'resizeinertiaresume',\n  'resizeend',\n]);\nactions.methodDict.resize = 'resizable';\n\ndefaultOptions.resize = resize.defaults;\n\nmodule.exports = resize;\n", "const raf            = require('./utils/raf');\nconst getWindow      = require('./utils/window').getWindow;\nconst is             = require('./utils/is');\nconst domUtils       = require('./utils/domUtils');\nconst Interaction    = require('./Interaction');\nconst defaultOptions = require('./defaultOptions');\n\nconst autoScroll = {\n  defaults: {\n    enabled  : false,\n    container: null,     // the item that is scrolled (Window or HTMLElement)\n    margin   : 60,\n    speed    : 300,      // the scroll speed in pixels per second\n  },\n\n  interaction: null,\n  i: null,    // the handle returned by window.setInterval\n  x: 0, y: 0, // Direction each pulse is to scroll in\n\n  isScrolling: false,\n  prevTime: 0,\n\n  start: function (interaction) {\n    autoScroll.isScrolling = true;\n    raf.cancel(autoScroll.i);\n\n    autoScroll.interaction = interaction;\n    autoScroll.prevTime = new Date().getTime();\n    autoScroll.i = raf.request(autoScroll.scroll);\n  },\n\n  stop: function () {\n    autoScroll.isScrolling = false;\n    raf.cancel(autoScroll.i);\n  },\n\n  // scroll the window by the values in scroll.x/y\n  scroll: function () {\n    const options = autoScroll.interaction.target.options[autoScroll.interaction.prepared.name].autoScroll;\n    const container = options.container || getWindow(autoScroll.interaction.element);\n    const now = new Date().getTime();\n    // change in time in seconds\n    const dt = (now - autoScroll.prevTime) / 1000;\n    // displacement\n    const s = options.speed * dt;\n\n    if (s >= 1) {\n      if (is.window(container)) {\n        container.scrollBy(autoScroll.x * s, autoScroll.y * s);\n      }\n      else if (container) {\n        container.scrollLeft += autoScroll.x * s;\n        container.scrollTop  += autoScroll.y * s;\n      }\n\n      autoScroll.prevTime = now;\n    }\n\n    if (autoScroll.isScrolling) {\n      raf.cancel(autoScroll.i);\n      autoScroll.i = raf.request(autoScroll.scroll);\n    }\n  },\n  check: function (interactable, actionName) {\n    const options = interactable.options;\n\n    return options[actionName].autoScroll && options[actionName].autoScroll.enabled;\n  },\n  onInteractionMove: function ({ interaction, pointer }) {\n    if (!(interaction.interacting()\n          && autoScroll.check(interaction.target, interaction.prepared.name))) {\n      return;\n    }\n\n    if (interaction.simulation) {\n      autoScroll.x = autoScroll.y = 0;\n      return;\n    }\n\n    let top;\n    let right;\n    let bottom;\n    let left;\n\n    const options = interaction.target.options[interaction.prepared.name].autoScroll;\n    const container = options.container || getWindow(interaction.element);\n\n    if (is.window(container)) {\n      left   = pointer.clientX < autoScroll.margin;\n      top    = pointer.clientY < autoScroll.margin;\n      right  = pointer.clientX > container.innerWidth  - autoScroll.margin;\n      bottom = pointer.clientY > container.innerHeight - autoScroll.margin;\n    }\n    else {\n      const rect = domUtils.getElementClientRect(container);\n\n      left   = pointer.clientX < rect.left   + autoScroll.margin;\n      top    = pointer.clientY < rect.top    + autoScroll.margin;\n      right  = pointer.clientX > rect.right  - autoScroll.margin;\n      bottom = pointer.clientY > rect.bottom - autoScroll.margin;\n    }\n\n    autoScroll.x = (right ? 1: left? -1: 0);\n    autoScroll.y = (bottom? 1:  top? -1: 0);\n\n    if (!autoScroll.isScrolling) {\n      // set the autoScroll properties to those of the target\n      autoScroll.margin = options.margin;\n      autoScroll.speed  = options.speed;\n\n      autoScroll.start(interaction);\n    }\n  },\n};\n\nInteraction.signals.on('stop-active', function () {\n  autoScroll.stop();\n});\n\nInteraction.signals.on('action-move', autoScroll.onInteractionMove);\n\ndefaultOptions.perAction.autoScroll = autoScroll.defaults;\n\nmodule.exports = autoScroll;\n", "/** @lends Interactable */\nconst Interactable = require('../Interactable');\nconst actions      = require('../actions/base');\nconst is           = require('../utils/is');\nconst domUtils     = require('../utils/domUtils');\n\nconst { warnOnce } = require('../utils');\n\nInteractable.prototype.getAction = function (pointer, event, interaction, element) {\n  const action = this.defaultActionChecker(pointer, event, interaction, element);\n\n  if (this.options.actionChecker) {\n    return this.options.actionChecker(pointer, event, action, this, element, interaction);\n  }\n\n  return action;\n};\n\n/**\n * ```js\n * interact(element, { ignoreFrom: document.getElementById('no-action') });\n * // or\n * interact(element).ignoreFrom('input, textarea, a');\n * ```\n * @deprecated\n * If the target of the `mousedown`, `pointerdown` or `touchstart` event or any\n * of it's parents match the given CSS selector or Element, no\n * drag/resize/gesture is started.\n *\n * Don't use this method. Instead set the `ignoreFrom` option for each action\n * or for `pointerEvents`\n *\n * @example\n * interact(targett)\n *   .draggable({\n *     ignoreFrom: 'input, textarea, a[href]'',\n *   })\n *   .pointerEvents({\n *     ignoreFrom: '[no-pointer]',\n *   });\n *\n * @param {string | Element | null} [newValue] a CSS selector string, an\n * Element or `null` to not ignore any elements\n * @return {string | Element | object} The current ignoreFrom value or this\n * Interactable\n */\nInteractable.prototype.ignoreFrom = warnOnce(function (newValue) {\n  return this._backCompatOption('ignoreFrom', newValue);\n}, 'Interactable.ignoreForm() has been deprecated. Use Interactble.draggable({ignoreFrom: newValue}).');\n\n/**\n * ```js\n *\n * @deprecated\n * A drag/resize/gesture is started only If the target of the `mousedown`,\n * `pointerdown` or `touchstart` event or any of it's parents match the given\n * CSS selector or Element.\n *\n * Don't use this method. Instead set the `allowFrom` option for each action\n * or for `pointerEvents`\n *\n * @example\n * interact(targett)\n *   .resizable({\n *     allowFrom: '.resize-handle',\n *   .pointerEvents({\n *     allowFrom: '.handle',,\n *   });\n *\n * @param {string | Element | null} [newValue] a CSS selector string, an\n * Element or `null` to allow from any element\n * @return {string | Element | object} The current allowFrom value or this\n * Interactable\n */\nInteractable.prototype.allowFrom = warnOnce(function (newValue) {\n  return this._backCompatOption('allowFrom', newValue);\n}, 'Interactable.allowForm() has been deprecated. Use Interactble.draggable({allowFrom: newValue}).');\n\nInteractable.prototype.testIgnore = function (ignoreFrom, interactableElement, element) {\n  if (!ignoreFrom || !is.element(element)) { return false; }\n\n  if (is.string(ignoreFrom)) {\n    return domUtils.matchesUpTo(element, ignoreFrom, interactableElement);\n  }\n  else if (is.element(ignoreFrom)) {\n    return domUtils.nodeContains(ignoreFrom, element);\n  }\n\n  return false;\n};\n\nInteractable.prototype.testAllow = function (allowFrom, interactableElement, element) {\n  if (!allowFrom) { return true; }\n\n  if (!is.element(element)) { return false; }\n\n  if (is.string(allowFrom)) {\n    return domUtils.matchesUpTo(element, allowFrom, interactableElement);\n  }\n  else if (is.element(allowFrom)) {\n    return domUtils.nodeContains(allowFrom, element);\n  }\n\n  return false;\n};\n\nInteractable.prototype.testIgnoreAllow = function (options, interactableElement, eventTarget) {\n  return (!this.testIgnore(options.ignoreFrom, interactableElement, eventTarget)\n    && this.testAllow(options.allowFrom, interactableElement, eventTarget));\n};\n\n/**\n * ```js\n * interact('.resize-drag')\n *   .resizable(true)\n *   .draggable(true)\n *   .actionChecker(function (pointer, event, action, interactable, element, interaction) {\n *\n *   if (interact.matchesSelector(event.target, '.drag-handle') {\n *     // force drag with handle target\n *     action.name = drag;\n *   }\n *   else {\n *     // resize from the top and right edges\n *     action.name  = 'resize';\n *     action.edges = { top: true, right: true };\n *   }\n *\n *   return action;\n * });\n * ```\n *\n * Gets or sets the function used to check action to be performed on\n * pointerDown\n *\n * @param {function | null} [checker] A function which takes a pointer event,\n * defaultAction string, interactable, element and interaction as parameters\n * and returns an object with name property 'drag' 'resize' or 'gesture' and\n * optionally an `edges` object with boolean 'top', 'left', 'bottom' and right\n * props.\n * @return {Function | Interactable} The checker function or this Interactable\n */\nInteractable.prototype.actionChecker = function (checker) {\n  if (is.function(checker)) {\n    this.options.actionChecker = checker;\n\n    return this;\n  }\n\n  if (checker === null) {\n    delete this.options.actionChecker;\n\n    return this;\n  }\n\n  return this.options.actionChecker;\n};\n\n/**\n * Returns or sets whether the the cursor should be changed depending on the\n * action that would be performed if the mouse were pressed and dragged.\n *\n * @param {boolean} [newValue]\n * @return {boolean | Interactable} The current setting or this Interactable\n */\nInteractable.prototype.styleCursor = function (newValue) {\n  if (is.bool(newValue)) {\n    this.options.styleCursor = newValue;\n\n    return this;\n  }\n\n  if (newValue === null) {\n    delete this.options.styleCursor;\n\n    return this;\n  }\n\n  return this.options.styleCursor;\n};\n\nInteractable.prototype.defaultActionChecker = function (pointer, event, interaction, element) {\n  const rect = this.getRect(element);\n  const buttons = event.buttons || ({\n    0: 1,\n    1: 4,\n    3: 8,\n    4: 16,\n  })[event.button];\n  let action = null;\n\n  for (const actionName of actions.names) {\n    // check mouseButton setting if the pointer is down\n    if (interaction.pointerIsDown\n        && /mouse|pointer/.test(interaction.pointerType)\n        && (buttons & this.options[actionName].mouseButtons) === 0) {\n      continue;\n    }\n\n    action = actions[actionName].checker(pointer, event, this, element, interaction, rect);\n\n    if (action) {\n      return action;\n    }\n  }\n};\n\n", "const interact       = require('../interact');\nconst Interactable   = require('../Interactable');\nconst Interaction    = require('../Interaction');\nconst actions        = require('../actions/base');\nconst defaultOptions = require('../defaultOptions');\nconst scope          = require('../scope');\nconst utils          = require('../utils');\nconst signals        = require('../utils/Signals').new();\n\nrequire('./InteractableMethods');\n\nconst autoStart = {\n  signals,\n  withinInteractionLimit,\n  // Allow this many interactions to happen simultaneously\n  maxInteractions: Infinity,\n  defaults: {\n    perAction: {\n      manualStart: false,\n      max: Infinity,\n      maxPerElement: 1,\n      allowFrom:  null,\n      ignoreFrom: null,\n\n      // only allow left button by default\n      // see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons#Return_value\n      mouseButtons: 1,\n    },\n  },\n  setActionDefaults: function (action) {\n    utils.extend(action.defaults, autoStart.defaults.perAction);\n  },\n  validateAction,\n};\n\n// set cursor style on mousedown\nInteraction.signals.on('down', function ({ interaction, pointer, event, eventTarget }) {\n  if (interaction.interacting()) { return; }\n\n  const actionInfo = getActionInfo(interaction, pointer, event, eventTarget);\n  prepare(interaction, actionInfo);\n});\n\n// set cursor style on mousemove\nInteraction.signals.on('move', function ({ interaction, pointer, event, eventTarget }) {\n  if (interaction.pointerType !== 'mouse'\n      || interaction.pointerIsDown\n      || interaction.interacting()) { return; }\n\n  const actionInfo = getActionInfo(interaction, pointer, event, eventTarget);\n  prepare(interaction, actionInfo);\n});\n\nInteraction.signals.on('move', function (arg) {\n  const { interaction, event } = arg;\n\n  if (!interaction.pointerIsDown\n      || interaction.interacting()\n      || !interaction.pointerWasMoved\n      || !interaction.prepared.name) {\n    return;\n  }\n\n  signals.fire('before-start', arg);\n\n  const target = interaction.target;\n\n  if (interaction.prepared.name && target) {\n    // check manualStart and interaction limit\n    if (target.options[interaction.prepared.name].manualStart\n        || !withinInteractionLimit(target, interaction.element, interaction.prepared)) {\n      interaction.stop(event);\n    }\n    else {\n      interaction.start(interaction.prepared, target, interaction.element);\n    }\n  }\n});\n\n// Check if the current target supports the action.\n// If so, return the validated action. Otherwise, return null\nfunction validateAction (action, interactable, element, eventTarget) {\n  if (utils.is.object(action)\n      && interactable.testIgnoreAllow(interactable.options[action.name], element, eventTarget)\n      && interactable.options[action.name].enabled\n      && withinInteractionLimit(interactable, element, action)) {\n    return action;\n  }\n\n  return null;\n}\n\nfunction validateSelector (interaction, pointer, event, matches, matchElements, eventTarget) {\n  for (let i = 0, len = matches.length; i < len; i++) {\n    const match = matches[i];\n    const matchElement = matchElements[i];\n    const action = validateAction(match.getAction(pointer, event, interaction, matchElement),\n                                  match,\n                                  matchElement,\n                                  eventTarget);\n\n    if (action) {\n      return {\n        action,\n        target: match,\n        element: matchElement,\n      };\n    }\n  }\n\n  return {};\n}\n\nfunction getActionInfo (interaction, pointer, event, eventTarget) {\n  let matches = [];\n  let matchElements = [];\n\n  let element = eventTarget;\n\n  function pushMatches (interactable) {\n    matches.push(interactable);\n    matchElements.push(element);\n  }\n\n  while (utils.is.element(element)) {\n    matches = [];\n    matchElements = [];\n\n    scope.interactables.forEachMatch(element, pushMatches);\n\n    const actionInfo = validateSelector(interaction, pointer, event, matches, matchElements, eventTarget);\n\n    if (actionInfo.action\n      && !actionInfo.target.options[actionInfo.action.name].manualStart) {\n      return actionInfo;\n    }\n\n    element = utils.parentNode(element);\n  }\n\n  return {};\n}\n\nfunction prepare (interaction, { action, target, element }) {\n  action = action || {};\n\n  if (interaction.target && interaction.target.options.styleCursor) {\n    interaction.target._doc.documentElement.style.cursor = '';\n  }\n\n  interaction.target = target;\n  interaction.element = element;\n  utils.copyAction(interaction.prepared, action);\n\n  if (target && target.options.styleCursor) {\n    const cursor = action? actions[action.name].getCursor(action) : '';\n    interaction.target._doc.documentElement.style.cursor = cursor;\n  }\n\n  signals.fire('prepared', { interaction: interaction });\n}\n\nInteraction.signals.on('stop', function ({ interaction }) {\n  const target = interaction.target;\n\n  if (target && target.options.styleCursor) {\n    target._doc.documentElement.style.cursor = '';\n  }\n});\n\nfunction withinInteractionLimit (interactable, element, action) {\n  const options = interactable.options;\n  const maxActions = options[action.name].max;\n  const maxPerElement = options[action.name].maxPerElement;\n  let activeInteractions = 0;\n  let targetCount = 0;\n  let targetElementCount = 0;\n\n  // no actions if any of these values == 0\n  if (!(maxActions && maxPerElement && autoStart.maxInteractions)) { return; }\n\n  for (const interaction of scope.interactions) {\n    const otherAction = interaction.prepared.name;\n\n    if (!interaction.interacting()) { continue; }\n\n    activeInteractions++;\n\n    if (activeInteractions >= autoStart.maxInteractions) {\n      return false;\n    }\n\n    if (interaction.target !== interactable) { continue; }\n\n    targetCount += (otherAction === action.name)|0;\n\n    if (targetCount >= maxActions) {\n      return false;\n    }\n\n    if (interaction.element === element) {\n      targetElementCount++;\n\n      if (otherAction !== action.name || targetElementCount >= maxPerElement) {\n        return false;\n      }\n    }\n  }\n\n  return autoStart.maxInteractions > 0;\n}\n\n/**\n * Returns or sets the maximum number of concurrent interactions allowed.  By\n * default only 1 interaction is allowed at a time (for backwards\n * compatibility). To allow multiple interactions on the same Interactables and\n * elements, you need to enable it in the draggable, resizable and gesturable\n * `'max'` and `'maxPerElement'` options.\n *\n * @alias module:interact.maxInteractions\n *\n * @param {number} [newValue] Any number. newValue <= 0 means no interactions.\n */\ninteract.maxInteractions = function (newValue) {\n  if (utils.is.number(newValue)) {\n    autoStart.maxInteractions = newValue;\n\n    return interact;\n  }\n\n  return autoStart.maxInteractions;\n};\n\nInteractable.settingsMethods.push('styleCursor');\nInteractable.settingsMethods.push('actionChecker');\nInteractable.settingsMethods.push('ignoreFrom');\nInteractable.settingsMethods.push('allowFrom');\n\ndefaultOptions.base.actionChecker = null;\ndefaultOptions.base.styleCursor = true;\n\nutils.extend(defaultOptions.perAction, autoStart.defaults.perAction);\n\nmodule.exports = autoStart;\n", "const autoStart = require('./base');\nconst scope     = require('../scope');\nconst is        = require('../utils/is');\n\nconst { parentNode } = require('../utils/domUtils');\n\nautoStart.setActionDefaults(require('../actions/drag'));\n\nautoStart.signals.on('before-start',  function ({ interaction, eventTarget, dx, dy }) {\n  if (interaction.prepared.name !== 'drag') { return; }\n\n  // check if a drag is in the correct axis\n  const absX = Math.abs(dx);\n  const absY = Math.abs(dy);\n  const targetOptions = interaction.target.options.drag;\n  const startAxis = targetOptions.startAxis;\n  const currentAxis = (absX > absY ? 'x' : absX < absY ? 'y' : 'xy');\n\n  interaction.prepared.axis = targetOptions.lockAxis === 'start'\n    ? currentAxis[0] // always lock to one axis even if currentAxis === 'xy'\n    : targetOptions.lockAxis;\n\n  // if the movement isn't in the startAxis of the interactable\n  if (currentAxis !== 'xy' && startAxis !== 'xy' && startAxis !== currentAxis) {\n    // cancel the prepared action\n    interaction.prepared.name = null;\n\n    // then try to get a drag from another ineractable\n    let element = eventTarget;\n\n    const getDraggable = function (interactable) {\n      if (interactable === interaction.target) { return; }\n\n      const options = interaction.target.options.drag;\n\n      if (!options.manualStart\n          && interactable.testIgnoreAllow(options, element, eventTarget)) {\n\n        const action = interactable.getAction(\n          interaction.downPointer, interaction.downEvent, interaction, element);\n\n        if (action\n            && action.name === 'drag'\n            && checkStartAxis(currentAxis, interactable)\n            && autoStart.validateAction(action, interactable, element, eventTarget)) {\n\n          return interactable;\n        }\n      }\n    };\n\n    // check all interactables\n    while (is.element(element)) {\n      const interactable = scope.interactables.forEachMatch(element, getDraggable);\n\n      if (interactable) {\n        interaction.prepared.name = 'drag';\n        interaction.target = interactable;\n        interaction.element = element;\n        break;\n      }\n\n      element = parentNode(element);\n    }\n  }\n});\n\nfunction checkStartAxis (startAxis, interactable) {\n  if (!interactable) { return false; }\n\n  const thisAxis = interactable.options.drag.startAxis;\n\n  return (startAxis === 'xy' || thisAxis === 'xy' || thisAxis === startAxis);\n}\n", "require('./base').setActionDefaults(require('../actions/gesture'));\n", "const autoStart   = require('./base');\nconst Interaction = require('../Interaction');\n\nautoStart.defaults.perAction.hold = 0;\nautoStart.defaults.perAction.delay = 0;\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.autoStartHoldTimer = null;\n});\n\nautoStart.signals.on('prepared', function ({ interaction }) {\n  const hold = getHoldDuration(interaction);\n\n  if (hold > 0) {\n    interaction.autoStartHoldTimer = setTimeout(() => {\n      interaction.start(interaction.prepared, interaction.target, interaction.element);\n    }, hold);\n  }\n});\n\nInteraction.signals.on('move', function ({ interaction, duplicate }) {\n  if (interaction.pointerWasMoved && !duplicate) {\n    clearTimeout(interaction.autoStartHoldTimer);\n  }\n});\n\n// prevent regular down->move autoStart\nautoStart.signals.on('before-start', function ({ interaction }) {\n  const hold = getHoldDuration(interaction);\n\n  if (hold > 0) {\n    interaction.prepared.name = null;\n  }\n});\n\nfunction getHoldDuration (interaction) {\n  const actionName = interaction.prepared && interaction.prepared.name;\n\n  if (!actionName) { return null; }\n\n  const options = interaction.target.options;\n\n  return options[actionName].hold || options[actionName].delay;\n}\n\nmodule.exports = {\n  getHoldDuration,\n};\n", "require('./base').setActionDefaults(require('../actions/resize'));\n", "module.exports = {\n  base: {\n    accept        : null,\n    preventDefault: 'auto',\n    deltaSource   : 'page',\n  },\n\n  perAction: {\n    origin: { x: 0, y: 0 },\n\n    inertia: {\n      enabled          : false,\n      resistance       : 10,    // the lambda in exponential decay\n      minSpeed         : 100,   // target speed must be above this for inertia to start\n      endSpeed         : 10,    // the speed at which inertia is slow enough to stop\n      allowResume      : true,  // allow resuming an action in inertia phase\n      smoothEndDuration: 300,   // animate to snap/restrict endOnly if there's no inertia\n    },\n  },\n};\n", "/* browser entry point */\n\n// inertia\nrequire('./inertia');\n\n// modifiers\nrequire('./modifiers/snap');\nrequire('./modifiers/restrict');\n\n// pointerEvents\nrequire('./pointerEvents/base');\nrequire('./pointerEvents/holdRepeat');\nrequire('./pointerEvents/interactableTargets');\n\n// autoStart hold\nrequire('./autoStart/hold');\n\n// actions\nrequire('./actions/gesture');\nrequire('./actions/resize');\nrequire('./actions/drag');\nrequire('./actions/drop');\n\n// load these modifiers after resize is loaded\nrequire('./modifiers/snapSize');\nrequire('./modifiers/restrictEdges');\nrequire('./modifiers/restrictSize');\n\n// autoStart actions\nrequire('./autoStart/gesture');\nrequire('./autoStart/resize');\nrequire('./autoStart/drag');\n\n// Interactable preventDefault setting\nrequire('./interactablePreventDefault.js');\n\n// autoScroll\nrequire('./autoScroll');\n\n// export interact\nmodule.exports = require('./interact');\n", "const InteractEvent  = require('./InteractEvent');\nconst Interaction    = require('./Interaction');\nconst modifiers      = require('./modifiers/base');\nconst utils          = require('./utils');\nconst animationFrame = require('./utils/raf');\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.inertiaStatus = {\n    active     : false,\n    smoothEnd  : false,\n    allowResume: false,\n\n    startEvent: null,\n    upCoords  : {},\n\n    xe: 0, ye: 0,\n    sx: 0, sy: 0,\n\n    t0: 0,\n    vx0: 0, vys: 0,\n    duration: 0,\n\n    lambda_v0: 0,\n    one_ve_v0: 0,\n    i  : null,\n  };\n\n  interaction.boundInertiaFrame   = () => inertiaFrame  .apply(interaction);\n  interaction.boundSmoothEndFrame = () => smoothEndFrame.apply(interaction);\n});\n\nInteraction.signals.on('down', function ({ interaction, event, pointer, eventTarget }) {\n  const status = interaction.inertiaStatus;\n\n  // Check if the down event hits the current inertia target\n  if (status.active) {\n    let element = eventTarget;\n\n    // climb up the DOM tree from the event target\n    while (utils.is.element(element)) {\n\n      // if interaction element is the current inertia target element\n      if (element === interaction.element) {\n        // stop inertia\n        animationFrame.cancel(status.i);\n        status.active = false;\n        interaction.simulation = null;\n\n        // update pointers to the down event's coordinates\n        interaction.updatePointer(pointer);\n        utils.setCoords(interaction.curCoords, interaction.pointers);\n\n        // fire appropriate signals\n        const signalArg = { interaction };\n        Interaction.signals.fire('before-action-move', signalArg);\n        Interaction.signals.fire('action-resume'     , signalArg);\n\n        // fire a reume event\n        const resumeEvent = new InteractEvent(interaction,\n                                              event,\n                                              interaction.prepared.name,\n                                              'inertiaresume',\n                                              interaction.element);\n\n        interaction.target.fire(resumeEvent);\n        interaction.prevEvent = resumeEvent;\n        modifiers.resetStatuses(interaction.modifierStatuses);\n\n        utils.copyCoords(interaction.prevCoords, interaction.curCoords);\n        break;\n      }\n\n      element = utils.parentNode(element);\n    }\n  }\n});\n\nInteraction.signals.on('up', function ({ interaction, event }) {\n  const status = interaction.inertiaStatus;\n\n  if (!interaction.interacting() || status.active) { return; }\n\n  const target = interaction.target;\n  const options = target && target.options;\n  const inertiaOptions = options && interaction.prepared.name && options[interaction.prepared.name].inertia;\n\n  const now = new Date().getTime();\n  const statuses = {};\n  const page = utils.extend({}, interaction.curCoords.page);\n  const pointerSpeed = interaction.pointerDelta.client.speed;\n\n  let smoothEnd = false;\n  let modifierResult;\n\n  // check if inertia should be started\n  const inertiaPossible = (inertiaOptions && inertiaOptions.enabled\n                     && interaction.prepared.name !== 'gesture'\n                     && event !== status.startEvent);\n\n  const inertia = (inertiaPossible\n    && (now - interaction.curCoords.timeStamp) < 50\n    && pointerSpeed > inertiaOptions.minSpeed\n    && pointerSpeed > inertiaOptions.endSpeed);\n\n  const modifierArg = {\n    interaction,\n    pageCoords: page,\n    statuses,\n    preEnd: true,\n    requireEndOnly: true,\n  };\n\n  // smoothEnd\n  if (inertiaPossible && !inertia) {\n    modifiers.resetStatuses(statuses);\n\n    modifierResult = modifiers.setAll(modifierArg);\n\n    if (modifierResult.shouldMove && modifierResult.locked) {\n      smoothEnd = true;\n    }\n  }\n\n  if (!(inertia || smoothEnd)) { return; }\n\n  utils.copyCoords(status.upCoords, interaction.curCoords);\n\n  interaction.pointers[0] = status.startEvent =\n    new InteractEvent(interaction, event, interaction.prepared.name, 'inertiastart', interaction.element);\n\n  status.t0 = now;\n\n  status.active = true;\n  status.allowResume = inertiaOptions.allowResume;\n  interaction.simulation = status;\n\n  target.fire(status.startEvent);\n\n  if (inertia) {\n    status.vx0 = interaction.pointerDelta.client.vx;\n    status.vy0 = interaction.pointerDelta.client.vy;\n    status.v0 = pointerSpeed;\n\n    calcInertia(interaction, status);\n\n    utils.extend(page, interaction.curCoords.page);\n\n    page.x += status.xe;\n    page.y += status.ye;\n\n    modifiers.resetStatuses(statuses);\n\n    modifierResult = modifiers.setAll(modifierArg);\n\n    status.modifiedXe += modifierResult.dx;\n    status.modifiedYe += modifierResult.dy;\n\n    status.i = animationFrame.request(interaction.boundInertiaFrame);\n  }\n  else {\n    status.smoothEnd = true;\n    status.xe = modifierResult.dx;\n    status.ye = modifierResult.dy;\n\n    status.sx = status.sy = 0;\n\n    status.i = animationFrame.request(interaction.boundSmoothEndFrame);\n  }\n});\n\nInteraction.signals.on('stop-active', function ({ interaction }) {\n  const status = interaction.inertiaStatus;\n\n  if (status.active) {\n    animationFrame.cancel(status.i);\n    status.active = false;\n    interaction.simulation = null;\n  }\n});\n\nfunction calcInertia (interaction, status) {\n  const inertiaOptions = interaction.target.options[interaction.prepared.name].inertia;\n  const lambda = inertiaOptions.resistance;\n  const inertiaDur = -Math.log(inertiaOptions.endSpeed / status.v0) / lambda;\n\n  status.x0 = interaction.prevEvent.pageX;\n  status.y0 = interaction.prevEvent.pageY;\n  status.t0 = status.startEvent.timeStamp / 1000;\n  status.sx = status.sy = 0;\n\n  status.modifiedXe = status.xe = (status.vx0 - inertiaDur) / lambda;\n  status.modifiedYe = status.ye = (status.vy0 - inertiaDur) / lambda;\n  status.te = inertiaDur;\n\n  status.lambda_v0 = lambda / status.v0;\n  status.one_ve_v0 = 1 - inertiaOptions.endSpeed / status.v0;\n}\n\nfunction inertiaFrame () {\n  updateInertiaCoords(this);\n  utils.setCoordDeltas(this.pointerDelta, this.prevCoords, this.curCoords);\n\n  const status = this.inertiaStatus;\n  const options = this.target.options[this.prepared.name].inertia;\n  const lambda = options.resistance;\n  const t = new Date().getTime() / 1000 - status.t0;\n\n  if (t < status.te) {\n\n    const progress =  1 - (Math.exp(-lambda * t) - status.lambda_v0) / status.one_ve_v0;\n\n    if (status.modifiedXe === status.xe && status.modifiedYe === status.ye) {\n      status.sx = status.xe * progress;\n      status.sy = status.ye * progress;\n    }\n    else {\n      const quadPoint = utils.getQuadraticCurvePoint(0, 0,\n                                                     status.xe,\n                                                     status.ye,\n                                                     status.modifiedXe,\n                                                     status.modifiedYe,\n                                                     progress);\n\n      status.sx = quadPoint.x;\n      status.sy = quadPoint.y;\n    }\n\n    this.doMove();\n\n    status.i = animationFrame.request(this.boundInertiaFrame);\n  }\n  else {\n    status.sx = status.modifiedXe;\n    status.sy = status.modifiedYe;\n\n    this.doMove();\n    this.end(status.startEvent);\n    status.active = false;\n    this.simulation = null;\n  }\n\n  utils.copyCoords(this.prevCoords, this.curCoords);\n}\n\nfunction smoothEndFrame () {\n  updateInertiaCoords(this);\n\n  const status = this.inertiaStatus;\n  const t = new Date().getTime() - status.t0;\n  const duration = this.target.options[this.prepared.name].inertia.smoothEndDuration;\n\n  if (t < duration) {\n    status.sx = utils.easeOutQuad(t, 0, status.xe, duration);\n    status.sy = utils.easeOutQuad(t, 0, status.ye, duration);\n\n    this.pointerMove(status.startEvent, status.startEvent);\n\n    status.i = animationFrame.request(this.boundSmoothEndFrame);\n  }\n  else {\n    status.sx = status.xe;\n    status.sy = status.ye;\n\n    this.pointerMove(status.startEvent, status.startEvent);\n    this.end(status.startEvent);\n\n    status.smoothEnd =\n      status.active = false;\n    this.simulation = null;\n  }\n}\n\nfunction updateInertiaCoords (interaction) {\n  const status = interaction.inertiaStatus;\n\n  // return if inertia isn't running\n  if (!status.active) { return; }\n\n  const pageUp   = status.upCoords.page;\n  const clientUp = status.upCoords.client;\n\n  utils.setCoords(interaction.curCoords, [ {\n    pageX  : pageUp.x   + status.sx,\n    pageY  : pageUp.y   + status.sy,\n    clientX: clientUp.x + status.sx,\n    clientY: clientUp.y + status.sy,\n  } ]);\n}\n", "/** @module interact */\n\nconst browser      = require('./utils/browser');\nconst events       = require('./utils/events');\nconst utils        = require('./utils');\nconst scope        = require('./scope');\nconst Interactable = require('./Interactable');\nconst Interaction  = require('./Interaction');\n\nconst globalEvents = {};\n\n/**\n * ```js\n * interact('#draggable').draggable(true);\n *\n * var rectables = interact('rect');\n * rectables\n *   .gesturable(true)\n *   .on('gesturemove', function (event) {\n *       // ...\n *   });\n * ```\n *\n * The methods of this variable can be used to set elements as interactables\n * and also to change various default settings.\n *\n * Calling it as a function and passing an element or a valid CSS selector\n * string returns an Interactable object which has various methods to configure\n * it.\n *\n * @global\n *\n * @param {Element | string} element The HTML or SVG Element to interact with\n * or CSS selector\n * @return {Interactable}\n */\nfunction interact (element, options) {\n  let interactable = scope.interactables.get(element, options);\n\n  if (!interactable) {\n    interactable = new Interactable(element, options);\n    interactable.events.global = globalEvents;\n  }\n\n  return interactable;\n}\n\n/**\n * Check if an element or selector has been set with the {@link interact}\n * function\n *\n * @alias module:interact.isSet\n *\n * @param {Element} element The Element being searched for\n * @return {boolean} Indicates if the element or CSS selector was previously\n * passed to interact\n*/\ninteract.isSet = function (element, options) {\n  return scope.interactables.indexOfElement(element, options && options.context) !== -1;\n};\n\n/**\n * Add a global listener for an InteractEvent or adds a DOM event to `document`\n *\n * @alias module:interact.on\n *\n * @param {string | array | object} type The types of events to listen for\n * @param {function} listener The function event (s)\n * @param {object | boolean} [options] object or useCapture flag for\n * addEventListener\n * @return {object} interact\n */\ninteract.on = function (type, listener, options) {\n  if (utils.is.string(type) && type.search(' ') !== -1) {\n    type = type.trim().split(/ +/);\n  }\n\n  if (utils.is.array(type)) {\n    for (const eventType of type) {\n      interact.on(eventType, listener, options);\n    }\n\n    return interact;\n  }\n\n  if (utils.is.object(type)) {\n    for (const prop in type) {\n      interact.on(prop, type[prop], listener);\n    }\n\n    return interact;\n  }\n\n  // if it is an InteractEvent type, add listener to globalEvents\n  if (utils.contains(Interactable.eventTypes, type)) {\n    // if this type of event was never bound\n    if (!globalEvents[type]) {\n      globalEvents[type] = [listener];\n    }\n    else {\n      globalEvents[type].push(listener);\n    }\n  }\n  // If non InteractEvent type, addEventListener to document\n  else {\n    events.add(scope.document, type, listener, { options });\n  }\n\n  return interact;\n};\n\n/**\n * Removes a global InteractEvent listener or DOM event from `document`\n *\n * @alias module:interact.off\n *\n * @param {string | array | object} type The types of events that were listened\n * for\n * @param {function} listener The listener function to be removed\n * @param {object | boolean} options [options] object or useCapture flag for\n * removeEventListener\n * @return {object} interact\n */\ninteract.off = function (type, listener, options) {\n  if (utils.is.string(type) && type.search(' ') !== -1) {\n    type = type.trim().split(/ +/);\n  }\n\n  if (utils.is.array(type)) {\n    for (const eventType of type) {\n      interact.off(eventType, listener, options);\n    }\n\n    return interact;\n  }\n\n  if (utils.is.object(type)) {\n    for (const prop in type) {\n      interact.off(prop, type[prop], listener);\n    }\n\n    return interact;\n  }\n\n  if (!utils.contains(Interactable.eventTypes, type)) {\n    events.remove(scope.document, type, listener, options);\n  }\n  else {\n    let index;\n\n    if (type in globalEvents\n        && (index = globalEvents[type].indexOf(listener)) !== -1) {\n      globalEvents[type].splice(index, 1);\n    }\n  }\n\n  return interact;\n};\n\n/**\n * Returns an object which exposes internal data\n\n * @alias module:interact.debug\n *\n * @return {object} An object with properties that outline the current state\n * and expose internal functions and variables\n */\ninteract.debug = function () {\n  return scope;\n};\n\n// expose the functions used to calculate multi-touch properties\ninteract.getPointerAverage  = utils.pointerAverage;\ninteract.getTouchBBox       = utils.touchBBox;\ninteract.getTouchDistance   = utils.touchDistance;\ninteract.getTouchAngle      = utils.touchAngle;\n\ninteract.getElementRect       = utils.getElementRect;\ninteract.getElementClientRect = utils.getElementClientRect;\ninteract.matchesSelector      = utils.matchesSelector;\ninteract.closest              = utils.closest;\n\n/**\n * @alias module:interact.supportsTouch\n *\n * @return {boolean} Whether or not the browser supports touch input\n */\ninteract.supportsTouch = function () {\n  return browser.supportsTouch;\n};\n\n/**\n * @alias module:interact.supportsPointerEvent\n *\n * @return {boolean} Whether or not the browser supports PointerEvents\n */\ninteract.supportsPointerEvent = function () {\n  return browser.supportsPointerEvent;\n};\n\n/**\n * Cancels all interactions (end events are not fired)\n *\n * @alias module:interact.stop\n *\n * @param {Event} event An event on which to call preventDefault()\n * @return {object} interact\n */\ninteract.stop = function (event) {\n  for (let i = scope.interactions.length - 1; i >= 0; i--) {\n    scope.interactions[i].stop(event);\n  }\n\n  return interact;\n};\n\n/**\n * Returns or sets the distance the pointer must be moved before an action\n * sequence occurs. This also affects tolerance for tap events.\n *\n * @alias module:interact.pointerMoveTolerance\n *\n * @param {number} [newValue] The movement from the start position must be greater than this value\n * @return {interact | number}\n */\ninteract.pointerMoveTolerance = function (newValue) {\n  if (utils.is.number(newValue)) {\n    Interaction.pointerMoveTolerance = newValue;\n\n    return interact;\n  }\n\n  return Interaction.pointerMoveTolerance;\n};\n\ninteract.addDocument    = scope.addDocument;\ninteract.removeDocument = scope.removeDocument;\n\nscope.interact = interact;\n\nmodule.exports = interact;\n", "const Interactable = require('./Interactable');\nconst Interaction  = require('./Interaction');\nconst scope        = require('./scope');\nconst is           = require('./utils/is');\nconst events       = require('./utils/events');\nconst browser      = require('./utils/browser');\n\nconst { nodeContains, matchesSelector } = require('./utils/domUtils');\n\n/**\n * Returns or sets whether to prevent the browser's default behaviour in\n * response to pointer events. Can be set to:\n *  - `'always'` to always prevent\n *  - `'never'` to never prevent\n *  - `'auto'` to let interact.js try to determine what would be best\n *\n * @param {string} [newValue] `true`, `false` or `'auto'`\n * @return {string | Interactable} The current setting or this Interactable\n */\nInteractable.prototype.preventDefault = function (newValue) {\n  if (/^(always|never|auto)$/.test(newValue)) {\n    this.options.preventDefault = newValue;\n    return this;\n  }\n\n  if (is.bool(newValue)) {\n    this.options.preventDefault = newValue? 'always' : 'never';\n    return this;\n  }\n\n  return this.options.preventDefault;\n};\n\nInteractable.prototype.checkAndPreventDefault = function (event) {\n  const setting = this.options.preventDefault;\n\n  if (setting === 'never') { return; }\n\n  if (setting === 'always') {\n    event.preventDefault();\n    return;\n  }\n\n  // setting === 'auto'\n\n  // don't preventDefault of touch{start,move} events if the browser supports passive\n  // events listeners. CSS touch-action and user-selecct should be used instead\n  if (events.supportsPassive\n    && /^touch(start|move)$/.test(event.type)\n    && !browser.isIOS) {\n    return;\n  }\n\n  // don't preventDefault of pointerdown events\n  if (/^(mouse|pointer|touch)*(down|start)/i.test(event.type)) {\n    return;\n  }\n\n  // don't preventDefault on editable elements\n  if (is.element(event.target)\n      && matchesSelector(event.target, 'input,select,textarea,[contenteditable=true],[contenteditable=true] *')) {\n    return;\n  }\n\n  event.preventDefault();\n};\n\nfunction onInteractionEvent ({ interaction, event }) {\n  if (interaction.target) {\n    interaction.target.checkAndPreventDefault(event);\n  }\n}\n\nfor (const eventSignal of ['down', 'move', 'up', 'cancel']) {\n  Interaction.signals.on(eventSignal, onInteractionEvent);\n}\n\n// prevent native HTML5 drag on interact.js target elements\nInteraction.docEvents.dragstart = function preventNativeDrag (event) {\n  for (const interaction of scope.interactions) {\n\n    if (interaction.element\n        && (interaction.element === event.target\n            || nodeContains(interaction.element, event.target))) {\n\n      interaction.target.checkAndPreventDefault(event);\n      return;\n    }\n  }\n};\n", "const InteractEvent = require('../InteractEvent');\nconst Interaction   = require('../Interaction');\nconst extend        = require('../utils/extend');\n\nconst modifiers = {\n  names: [],\n\n  setOffsets: function (arg) {\n    const { interaction, pageCoords: page } = arg;\n    const { target, element, startOffset } = interaction;\n    const rect = target.getRect(element);\n\n    if (rect) {\n      startOffset.left = page.x - rect.left;\n      startOffset.top  = page.y - rect.top;\n\n      startOffset.right  = rect.right  - page.x;\n      startOffset.bottom = rect.bottom - page.y;\n\n      if (!('width'  in rect)) { rect.width  = rect.right  - rect.left; }\n      if (!('height' in rect)) { rect.height = rect.bottom - rect.top ; }\n    }\n    else {\n      startOffset.left = startOffset.top = startOffset.right = startOffset.bottom = 0;\n    }\n\n    arg.rect = rect;\n    arg.interactable = target;\n    arg.element = element;\n\n    for (const modifierName of modifiers.names) {\n      arg.options = target.options[interaction.prepared.name][modifierName];\n\n      if (!arg.options) {\n        continue;\n      }\n\n      interaction.modifierOffsets[modifierName] = modifiers[modifierName].setOffset(arg);\n    }\n  },\n\n  setAll: function (arg) {\n    const { interaction, statuses, preEnd, requireEndOnly } = arg;\n    const result = {\n      dx: 0,\n      dy: 0,\n      changed: false,\n      locked: false,\n      shouldMove: true,\n    };\n\n    arg.modifiedCoords = extend({}, arg.pageCoords);\n\n    for (const modifierName of modifiers.names) {\n      const modifier = modifiers[modifierName];\n      const options = interaction.target.options[interaction.prepared.name][modifierName];\n\n      if (!shouldDo(options, preEnd, requireEndOnly)) { continue; }\n\n      arg.status = arg.status = statuses[modifierName];\n      arg.options = options;\n      arg.offset = arg.interaction.modifierOffsets[modifierName];\n\n      modifier.set(arg);\n\n      if (arg.status.locked) {\n        arg.modifiedCoords.x += arg.status.dx;\n        arg.modifiedCoords.y += arg.status.dy;\n\n        result.dx += arg.status.dx;\n        result.dy += arg.status.dy;\n\n        result.locked = true;\n      }\n    }\n\n    // a move should be fired if:\n    //  - there are no modifiers enabled,\n    //  - no modifiers are \"locked\" i.e. have changed the pointer's coordinates, or\n    //  - the locked coords have changed since the last pointer move\n    result.shouldMove = !arg.status || !result.locked || arg.status.changed;\n\n    return result;\n  },\n\n  resetStatuses: function (statuses) {\n    for (const modifierName of modifiers.names) {\n      const status = statuses[modifierName] || {};\n\n      status.dx = status.dy = 0;\n      status.modifiedX = status.modifiedY = NaN;\n      status.locked = false;\n      status.changed = true;\n\n      statuses[modifierName] = status;\n    }\n\n    return statuses;\n  },\n\n  start: function ({ interaction }, signalName) {\n    const arg = {\n      interaction,\n      pageCoords: (signalName === 'action-resume' ?\n                   interaction.curCoords : interaction.startCoords).page,\n      startOffset: interaction.startOffset,\n      statuses: interaction.modifierStatuses,\n      preEnd: false,\n      requireEndOnly: false,\n    };\n\n    modifiers.setOffsets(arg);\n    modifiers.resetStatuses(arg.statuses);\n\n    arg.pageCoords = extend({}, interaction.startCoords.page);\n    interaction.modifierResult = modifiers.setAll(arg);\n  },\n\n  beforeMove: function ({ interaction, preEnd, interactingBeforeMove }) {\n    const modifierResult = modifiers.setAll({\n      interaction,\n      preEnd,\n      pageCoords: interaction.curCoords.page,\n      statuses: interaction.modifierStatuses,\n      requireEndOnly: false,\n    });\n\n    // don't fire an action move if a modifier would keep the event in the same\n    // cordinates as before\n    if (!modifierResult.shouldMove && interactingBeforeMove) {\n      interaction._dontFireMove = true;\n    }\n\n    interaction.modifierResult = modifierResult;\n  },\n\n  end: function ({ interaction, event }) {\n    for (const modifierName of modifiers.names) {\n      const options = interaction.target.options[interaction.prepared.name][modifierName];\n\n      // if the endOnly option is true for any modifier\n      if (shouldDo(options, true, true)) {\n        // fire a move event at the modified coordinates\n        interaction.doMove({ event, preEnd: true });\n        break;\n      }\n    }\n  },\n\n  setXY: function (arg) {\n    const { iEvent, interaction } = arg;\n    const modifierArg = extend({}, arg);\n\n    for (let i = 0; i < modifiers.names.length; i++) {\n      const modifierName = modifiers.names[i];\n      modifierArg.options = interaction.target.options[interaction.prepared.name][modifierName];\n\n      if (!modifierArg.options) {\n        continue;\n      }\n\n      const modifier = modifiers[modifierName];\n\n      modifierArg.status = interaction.modifierStatuses[modifierName];\n\n      iEvent[modifierName] = modifier.modifyCoords(modifierArg);\n    }\n  },\n};\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.startOffset      = { left: 0, right: 0, top: 0, bottom: 0 };\n  interaction.modifierOffsets  = {};\n  interaction.modifierStatuses = modifiers.resetStatuses({});\n  interaction.modifierResult   = null;\n});\n\nInteraction.signals.on('action-start' , modifiers.start);\nInteraction.signals.on('action-resume', modifiers.start);\nInteraction.signals.on('before-action-move', modifiers.beforeMove);\nInteraction.signals.on('action-end', modifiers.end);\n\nInteractEvent.signals.on('set-xy', modifiers.setXY);\n\nfunction shouldDo (options, preEnd, requireEndOnly) {\n  return (options && options.enabled\n          && (preEnd || !options.endOnly)\n          && (!requireEndOnly || options.endOnly));\n}\n\nmodule.exports = modifiers;\n", "const modifiers      = require('./base');\nconst utils          = require('../utils');\nconst defaultOptions = require('../defaultOptions');\n\nconst restrict = {\n  defaults: {\n    enabled    : false,\n    endOnly    : false,\n    restriction: null,\n    elementRect: null,\n  },\n\n  setOffset: function ({ rect, startOffset, options }) {\n    const elementRect = options && options.elementRect;\n    const offset = {};\n\n    if (rect && elementRect) {\n      offset.left = startOffset.left - (rect.width  * elementRect.left);\n      offset.top  = startOffset.top  - (rect.height * elementRect.top);\n\n      offset.right  = startOffset.right  - (rect.width  * (1 - elementRect.right));\n      offset.bottom = startOffset.bottom - (rect.height * (1 - elementRect.bottom));\n    }\n    else {\n      offset.left = offset.top = offset.right = offset.bottom = 0;\n    }\n\n    return offset;\n  },\n\n  set: function ({ modifiedCoords, interaction, status, options }) {\n    if (!options) { return status; }\n\n    const page = status.useStatusXY\n      ? { x: status.x, y: status.y }\n      : utils.extend({}, modifiedCoords);\n\n    const restriction = getRestrictionRect(options.restriction, interaction, page);\n\n    if (!restriction) { return status; }\n\n    status.dx = 0;\n    status.dy = 0;\n    status.locked = false;\n\n    const rect = restriction;\n    let modifiedX = page.x;\n    let modifiedY = page.y;\n\n    const offset = interaction.modifierOffsets.restrict;\n\n    // object is assumed to have\n    // x, y, width, height or\n    // left, top, right, bottom\n    if ('x' in restriction && 'y' in restriction) {\n      modifiedX = Math.max(Math.min(rect.x + rect.width  - offset.right , page.x), rect.x + offset.left);\n      modifiedY = Math.max(Math.min(rect.y + rect.height - offset.bottom, page.y), rect.y + offset.top );\n    }\n    else {\n      modifiedX = Math.max(Math.min(rect.right  - offset.right , page.x), rect.left + offset.left);\n      modifiedY = Math.max(Math.min(rect.bottom - offset.bottom, page.y), rect.top  + offset.top );\n    }\n\n    status.dx = modifiedX - page.x;\n    status.dy = modifiedY - page.y;\n\n    status.changed = status.modifiedX !== modifiedX || status.modifiedY !== modifiedY;\n    status.locked = !!(status.dx || status.dy);\n\n    status.modifiedX = modifiedX;\n    status.modifiedY = modifiedY;\n  },\n\n  modifyCoords: function ({ page, client, status, phase, options }) {\n    const elementRect = options && options.elementRect;\n\n    if (options && options.enabled\n        && !(phase === 'start' && elementRect && status.locked)) {\n\n      if (status.locked) {\n        page.x += status.dx;\n        page.y += status.dy;\n        client.x += status.dx;\n        client.y += status.dy;\n\n        return {\n          dx: status.dx,\n          dy: status.dy,\n        };\n      }\n    }\n  },\n\n  getRestrictionRect,\n};\n\nfunction getRestrictionRect (value, interaction, page) {\n  if (utils.is.function(value)) {\n    return utils.resolveRectLike(value, interaction.target, interaction.element, [page.x, page.y, interaction]);\n  } else {\n    return utils.resolveRectLike(value, interaction.target, interaction.element);\n  }\n}\n\nmodifiers.restrict = restrict;\nmodifiers.names.push('restrict');\n\ndefaultOptions.perAction.restrict = restrict.defaults;\n\nmodule.exports = restrict;\n", "// This module adds the options.resize.restrictEdges setting which sets min and\n// max for the top, left, bottom and right edges of the target being resized.\n//\n// interact(target).resize({\n//   edges: { top: true, left: true },\n//   restrictEdges: {\n//     inner: { top: 200, left: 200, right: 400, bottom: 400 },\n//     outer: { top:   0, left:   0, right: 600, bottom: 600 },\n//   },\n// });\n\nconst modifiers      = require('./base');\nconst utils          = require('../utils');\nconst rectUtils      = require('../utils/rect');\nconst defaultOptions = require('../defaultOptions');\nconst resize         = require('../actions/resize');\n\nconst { getRestrictionRect } = require('./restrict');\n\nconst noInner = { top: +Infinity, left: +Infinity, bottom: -Infinity, right: -Infinity };\nconst noOuter = { top: -Infinity, left: -Infinity, bottom: +Infinity, right: +Infinity };\n\nconst restrictEdges = {\n  defaults: {\n    enabled: false,\n    endOnly: false,\n    min: null,\n    max: null,\n    offset: null,\n  },\n\n  setOffset: function ({ interaction, startOffset, options }) {\n    if (!options) {\n      return utils.extend({}, startOffset);\n    }\n\n    const offset = getRestrictionRect(options.offset, interaction, interaction.startCoords.page);\n\n    if (offset) {\n      return {\n        top:    startOffset.top    + offset.y,\n        left:   startOffset.left   + offset.x,\n        bottom: startOffset.bottom + offset.y,\n        right:  startOffset.right  + offset.x,\n      };\n    }\n\n    return startOffset;\n  },\n\n  set: function ({ modifiedCoords, interaction, status, offset, options }) {\n    const edges = interaction.prepared.linkedEdges || interaction.prepared.edges;\n\n    if (!interaction.interacting() || !edges) {\n      return;\n    }\n\n    const page = status.useStatusXY\n      ? { x: status.x, y: status.y }\n      : utils.extend({}, modifiedCoords);\n    const inner = rectUtils.xywhToTlbr(getRestrictionRect(options.inner, interaction, page)) || noInner;\n    const outer = rectUtils.xywhToTlbr(getRestrictionRect(options.outer, interaction, page)) || noOuter;\n\n    let modifiedX = page.x;\n    let modifiedY = page.y;\n\n    status.dx = 0;\n    status.dy = 0;\n    status.locked = false;\n\n    if (edges.top) {\n      modifiedY = Math.min(Math.max(outer.top    + offset.top,    page.y), inner.top    + offset.top);\n    }\n    else if (edges.bottom) {\n      modifiedY = Math.max(Math.min(outer.bottom - offset.bottom, page.y), inner.bottom - offset.bottom);\n    }\n    if (edges.left) {\n      modifiedX = Math.min(Math.max(outer.left   + offset.left,   page.x), inner.left   + offset.left);\n    }\n    else if (edges.right) {\n      modifiedX = Math.max(Math.min(outer.right  - offset.right,  page.x), inner.right  - offset.right);\n    }\n\n    status.dx = modifiedX - page.x;\n    status.dy = modifiedY - page.y;\n\n    status.changed = status.modifiedX !== modifiedX || status.modifiedY !== modifiedY;\n    status.locked = !!(status.dx || status.dy);\n\n    status.modifiedX = modifiedX;\n    status.modifiedY = modifiedY;\n  },\n\n  modifyCoords: function ({ page, client, status, phase, options }) {\n    if (options && options.enabled\n        && !(phase === 'start' && status.locked)) {\n\n      if (status.locked) {\n        page.x += status.dx;\n        page.y += status.dy;\n        client.x += status.dx;\n        client.y += status.dy;\n\n        return {\n          dx: status.dx,\n          dy: status.dy,\n        };\n      }\n    }\n  },\n\n  noInner,\n  noOuter,\n  getRestrictionRect,\n};\n\nmodifiers.restrictEdges = restrictEdges;\nmodifiers.names.push('restrictEdges');\n\ndefaultOptions.perAction.restrictEdges = restrictEdges.defaults;\nresize.defaults.restrictEdges          = restrictEdges.defaults;\n\nmodule.exports = restrictEdges;\n", "// This module adds the options.resize.restrictSize setting which sets min and\n// max width and height for the target being resized.\n//\n// interact(target).resize({\n//   edges: { top: true, left: true },\n//   restrictSize: {\n//     min: { width: -600, height: -600 },\n//     max: { width:  600, height:  600 },\n//   },\n// });\n\nconst modifiers      = require('./base');\nconst restrictEdges  = require('./restrictEdges');\nconst utils          = require('../utils');\nconst rectUtils      = require('../utils/rect');\nconst defaultOptions = require('../defaultOptions');\nconst resize         = require('../actions/resize');\n\nconst noMin = { width: -Infinity, height: -Infinity };\nconst noMax = { width: +Infinity, height: +Infinity };\n\nconst restrictSize = {\n  defaults: {\n    enabled: false,\n    endOnly: false,\n    min: null,\n    max: null,\n  },\n\n  setOffset: function ({ interaction }) {\n    return interaction.startOffset;\n  },\n\n  set: function (arg) {\n    const { interaction, options } = arg;\n    const edges = interaction.prepared.linkedEdges || interaction.prepared.edges;\n\n    if (!interaction.interacting() || !edges) {\n      return;\n    }\n\n    const rect = rectUtils.xywhToTlbr(interaction.resizeRects.inverted);\n\n    const minSize = rectUtils.tlbrToXywh(restrictEdges.getRestrictionRect(options.min, interaction)) || noMin;\n    const maxSize = rectUtils.tlbrToXywh(restrictEdges.getRestrictionRect(options.max, interaction)) || noMax;\n\n    arg.options = {\n      enabled: options.enabled,\n      endOnly: options.endOnly,\n      inner: utils.extend({}, restrictEdges.noInner),\n      outer: utils.extend({}, restrictEdges.noOuter),\n    };\n\n    if (edges.top) {\n      arg.options.inner.top = rect.bottom - minSize.height;\n      arg.options.outer.top = rect.bottom - maxSize.height;\n    }\n    else if (edges.bottom) {\n      arg.options.inner.bottom = rect.top + minSize.height;\n      arg.options.outer.bottom = rect.top + maxSize.height;\n    }\n    if (edges.left) {\n      arg.options.inner.left = rect.right - minSize.width;\n      arg.options.outer.left = rect.right - maxSize.width;\n    }\n    else if (edges.right) {\n      arg.options.inner.right = rect.left + minSize.width;\n      arg.options.outer.right = rect.left + maxSize.width;\n    }\n\n    restrictEdges.set(arg);\n  },\n\n  modifyCoords: restrictEdges.modifyCoords,\n};\n\nmodifiers.restrictSize = restrictSize;\nmodifiers.names.push('restrictSize');\n\ndefaultOptions.perAction.restrictSize = restrictSize.defaults;\nresize.defaults.restrictSize          = restrictSize.defaults;\n\nmodule.exports = restrictSize;\n", "const modifiers      = require('./base');\nconst interact       = require('../interact');\nconst utils          = require('../utils');\nconst defaultOptions = require('../defaultOptions');\n\nconst snap = {\n  defaults: {\n    enabled: false,\n    endOnly: false,\n    range  : Infinity,\n    targets: null,\n    offsets: null,\n\n    relativePoints: null,\n  },\n\n  setOffset: function ({ interaction, interactable, element, rect, startOffset, options }) {\n    const offsets = [];\n    const optionsOrigin = utils.rectToXY(utils.resolveRectLike(options.origin));\n    const origin = optionsOrigin || utils.getOriginXY(interactable, element, interaction.prepared.name);\n    options = options || interactable.options[interaction.prepared.name].snap || {};\n\n    let snapOffset;\n\n    if (options.offset === 'startCoords') {\n      snapOffset = {\n        x: interaction.startCoords.page.x - origin.x,\n        y: interaction.startCoords.page.y - origin.y,\n      };\n    }\n    else  {\n      const offsetRect = utils.resolveRectLike(options.offset, interactable, element, [interaction]);\n\n      snapOffset = utils.rectToXY(offsetRect) || { x: 0, y: 0 };\n    }\n\n    if (rect && options.relativePoints && options.relativePoints.length) {\n      for (const { x: relativeX, y: relativeY } of options.relativePoints) {\n        offsets.push({\n          x: startOffset.left - (rect.width  * relativeX) + snapOffset.x,\n          y: startOffset.top  - (rect.height * relativeY) + snapOffset.y,\n        });\n      }\n    }\n    else {\n      offsets.push(snapOffset);\n    }\n\n    return offsets;\n  },\n\n  set: function ({ interaction, modifiedCoords, status, options, offset: offsets }) {\n    const targets = [];\n    let target;\n    let page;\n    let i;\n\n    if (status.useStatusXY) {\n      page = { x: status.x, y: status.y };\n    }\n    else {\n      const origin = utils.getOriginXY(interaction.target, interaction.element, interaction.prepared.name);\n\n      page = utils.extend({}, modifiedCoords);\n\n      page.x -= origin.x;\n      page.y -= origin.y;\n    }\n\n    status.realX = page.x;\n    status.realY = page.y;\n\n    let len = options.targets? options.targets.length : 0;\n\n    for (const { x: offsetX, y: offsetY } of offsets) {\n      const relativeX = page.x - offsetX;\n      const relativeY = page.y - offsetY;\n\n      for (const snapTarget of (options.targets || [])) {\n        if (utils.is.function(snapTarget)) {\n          target = snapTarget(relativeX, relativeY, interaction);\n        }\n        else {\n          target = snapTarget;\n        }\n\n        if (!target) { continue; }\n\n        targets.push({\n          x: utils.is.number(target.x) ? (target.x + offsetX) : relativeX,\n          y: utils.is.number(target.y) ? (target.y + offsetY) : relativeY,\n\n          range: utils.is.number(target.range)? target.range: options.range,\n        });\n      }\n    }\n\n    const closest = {\n      target: null,\n      inRange: false,\n      distance: 0,\n      range: 0,\n      dx: 0,\n      dy: 0,\n    };\n\n    for (i = 0, len = targets.length; i < len; i++) {\n      target = targets[i];\n\n      const range = target.range;\n      const dx = target.x - page.x;\n      const dy = target.y - page.y;\n      const distance = utils.hypot(dx, dy);\n      let inRange = distance <= range;\n\n      // Infinite targets count as being out of range\n      // compared to non infinite ones that are in range\n      if (range === Infinity && closest.inRange && closest.range !== Infinity) {\n        inRange = false;\n      }\n\n      if (!closest.target || (inRange\n          // is the closest target in range?\n          ? (closest.inRange && range !== Infinity\n          // the pointer is relatively deeper in this target\n          ? distance / range < closest.distance / closest.range\n          // this target has Infinite range and the closest doesn't\n          : (range === Infinity && closest.range !== Infinity)\n          // OR this target is closer that the previous closest\n        || distance < closest.distance)\n          // The other is not in range and the pointer is closer to this target\n          : (!closest.inRange && distance < closest.distance))) {\n\n        closest.target = target;\n        closest.distance = distance;\n        closest.range = range;\n        closest.inRange = inRange;\n        closest.dx = dx;\n        closest.dy = dy;\n\n        status.range = range;\n      }\n    }\n\n    let snapChanged;\n\n    if (closest.target) {\n      snapChanged = (status.modifiedX !== closest.target.x || status.modifiedY !== closest.target.y);\n\n      status.modifiedX = closest.target.x;\n      status.modifiedY = closest.target.y;\n    }\n    else {\n      snapChanged = true;\n\n      status.modifiedX = NaN;\n      status.modifiedY = NaN;\n    }\n\n    status.dx = closest.dx;\n    status.dy = closest.dy;\n\n    status.changed = (snapChanged || (closest.inRange && !status.locked));\n    status.locked = closest.inRange;\n  },\n\n  modifyCoords: function ({ page, client, status, phase, options }) {\n    const relativePoints = options && options.relativePoints;\n\n    if (options && options.enabled\n        && !(phase === 'start' && relativePoints && relativePoints.length)) {\n\n      if (status.locked) {\n        page.x += status.dx;\n        page.y += status.dy;\n        client.x += status.dx;\n        client.y += status.dy;\n      }\n\n      return {\n        range  : status.range,\n        locked : status.locked,\n        x      : status.modifiedX,\n        y      : status.modifiedY,\n        realX  : status.realX,\n        realY  : status.realY,\n        dx     : status.dx,\n        dy     : status.dy,\n      };\n    }\n  },\n};\n\ninteract.createSnapGrid = function (grid) {\n  return function (x, y) {\n    const limits = grid.limits || {\n      left  : -Infinity,\n      right :  Infinity,\n      top   : -Infinity,\n      bottom:  Infinity,\n    };\n    let offsetX = 0;\n    let offsetY = 0;\n\n    if (utils.is.object(grid.offset)) {\n      offsetX = grid.offset.x;\n      offsetY = grid.offset.y;\n    }\n\n    const gridx = Math.round((x - offsetX) / grid.x);\n    const gridy = Math.round((y - offsetY) / grid.y);\n\n    const newX = Math.max(limits.left, Math.min(limits.right , gridx * grid.x + offsetX));\n    const newY = Math.max(limits.top , Math.min(limits.bottom, gridy * grid.y + offsetY));\n\n    return {\n      x: newX,\n      y: newY,\n      range: grid.range,\n    };\n  };\n};\n\nmodifiers.snap = snap;\nmodifiers.names.push('snap');\n\ndefaultOptions.perAction.snap = snap.defaults;\n\nmodule.exports = snap;\n", "// This module allows snapping of the size of targets during resize\n// interactions.\n\nconst modifiers      = require('./base');\nconst snap           = require('./snap');\nconst defaultOptions = require('../defaultOptions');\nconst resize         = require('../actions/resize');\nconst utils          = require('../utils/');\n\nconst snapSize = {\n  defaults: {\n    enabled: false,\n    endOnly: false,\n    range  : Infinity,\n    targets: null,\n    offsets: null,\n  },\n\n  setOffset: function (arg) {\n    const { interaction, options } = arg;\n    const edges = interaction.prepared.edges;\n\n    if (!edges) { return; }\n\n    arg.options = {\n      relativePoints: [{\n        x: edges.left? 0 : 1,\n        y: edges.top ? 0 : 1,\n      }],\n      origin: { x: 0, y: 0 },\n      offset: 'self',\n      range: options.range,\n    };\n\n    const offsets = snap.setOffset(arg);\n    arg.options = options;\n\n    return offsets;\n  },\n\n  set: function (arg) {\n    const { interaction, options, offset, modifiedCoords } = arg;\n    const page = utils.extend({}, modifiedCoords);\n    const relativeX = page.x - offset[0].x;\n    const relativeY = page.y - offset[0].y;\n\n    arg.options = utils.extend({}, options);\n    arg.options.targets = [];\n\n    for (const snapTarget of (options.targets || [])) {\n      let target;\n\n      if (utils.is.function(snapTarget)) {\n        target = snapTarget(relativeX, relativeY, interaction);\n      }\n      else {\n        target = snapTarget;\n      }\n\n      if (!target) { continue; }\n\n      if ('width' in target && 'height' in target) {\n        target.x = target.width;\n        target.y = target.height;\n      }\n\n      arg.options.targets.push(target);\n    }\n\n    snap.set(arg);\n  },\n\n  modifyCoords: function (arg) {\n    const { options } = arg;\n\n    arg.options = utils.extend({}, options);\n    arg.options.enabled = options.enabled;\n    arg.options.relativePoints = [null];\n\n    snap.modifyCoords(arg);\n  },\n};\n\nmodifiers.snapSize = snapSize;\nmodifiers.names.push('snapSize');\n\ndefaultOptions.perAction.snapSize = snapSize.defaults;\nresize.defaults.snapSize          = snapSize.defaults;\n\nmodule.exports = snapSize;\n", "const pointerUtils = require('../utils/pointerUtils');\n\nmodule.exports = class PointerEvent {\n  /** */\n  constructor (type, pointer, event, eventTarget, interaction) {\n    pointerUtils.pointerExtend(this, event);\n\n    if (event !== pointer) {\n      pointerUtils.pointerExtend(this, pointer);\n    }\n\n    this.interaction = interaction;\n\n    this.timeStamp     = new Date().getTime();\n    this.originalEvent = event;\n    this.type          = type;\n    this.pointerId     = pointerUtils.getPointerId(pointer);\n    this.pointerType   = pointerUtils.getPointerType(pointer);\n    this.target        = eventTarget;\n    this.currentTarget = null;\n\n    if (type === 'tap') {\n      const pointerIndex = interaction.getPointerIndex(pointer);\n      this.dt = this.timeStamp - interaction.downTimes[pointerIndex];\n\n      const interval = this.timeStamp - interaction.tapTime;\n\n      this.double = !!(interaction.prevTap\n        && interaction.prevTap.type !== 'doubletap'\n        && interaction.prevTap.target === this.target\n        && interval < 500);\n    }\n    else if (type === 'doubletap') {\n      this.dt = pointer.timeStamp - interaction.tapTime;\n    }\n  }\n\n  subtractOrigin ({ x: originX, y: originY }) {\n    this.pageX   -= originX;\n    this.pageY   -= originY;\n    this.clientX -= originX;\n    this.clientY -= originY;\n\n    return this;\n  }\n\n  addOrigin ({ x: originX, y: originY }) {\n    this.pageX   += originX;\n    this.pageY   += originY;\n    this.clientX += originX;\n    this.clientY += originY;\n\n    return this;\n  }\n\n  /** */\n  preventDefault () {\n    this.originalEvent.preventDefault();\n  }\n\n  /** */\n  stopPropagation () {\n    this.propagationStopped = true;\n  }\n\n  /** */\n  stopImmediatePropagation () {\n    this.immediatePropagationStopped = this.propagationStopped = true;\n  }\n};\n", "const PointerEvent = require('./PointerEvent');\nconst Interaction  = require('../Interaction');\nconst utils        = require('../utils');\nconst defaults     = require('../defaultOptions');\nconst signals      = require('../utils/Signals').new();\n\nconst simpleSignals = [ 'down', 'up', 'cancel' ];\nconst simpleEvents  = [ 'down', 'up', 'cancel' ];\n\nconst pointerEvents = {\n  PointerEvent,\n  fire,\n  collectEventTargets,\n  signals,\n  defaults: {\n    holdDuration: 600,\n    ignoreFrom  : null,\n    allowFrom   : null,\n    origin      : { x: 0, y: 0 },\n  },\n  types: [\n    'down',\n    'move',\n    'up',\n    'cancel',\n    'tap',\n    'doubletap',\n    'hold',\n  ],\n};\n\nfunction fire (arg) {\n  const {\n    interaction, pointer, event, eventTarget,\n    type = arg.pointerEvent.type,\n    targets = collectEventTargets(arg),\n    pointerEvent = new PointerEvent(type, pointer, event, eventTarget, interaction),\n  } = arg;\n\n  const signalArg = {\n    interaction,\n    pointer,\n    event,\n    eventTarget,\n    targets,\n    type,\n    pointerEvent,\n  };\n\n  for (let i = 0; i < targets.length; i++) {\n    const target = targets[i];\n\n    for (const prop in target.props || {}) {\n      pointerEvent[prop] = target.props[prop];\n    }\n\n    const origin = utils.getOriginXY(target.eventable, target.element);\n\n    pointerEvent.subtractOrigin(origin);\n    pointerEvent.eventable = target.eventable;\n    pointerEvent.currentTarget = target.element;\n\n    target.eventable.fire(pointerEvent);\n\n    pointerEvent.addOrigin(origin);\n\n    if (pointerEvent.immediatePropagationStopped\n        || (pointerEvent.propagationStopped\n            && (i + 1) < targets.length && targets[i + 1].element !== pointerEvent.currentTarget)) {\n      break;\n    }\n  }\n\n  signals.fire('fired', signalArg);\n\n  if (type === 'tap') {\n    // if pointerEvent should make a double tap, create and fire a doubletap\n    // PointerEvent and use that as the prevTap\n    const prevTap = pointerEvent.double\n      ? fire({\n        interaction, pointer, event, eventTarget,\n        type: 'doubletap',\n      })\n      : pointerEvent;\n\n    interaction.prevTap = prevTap;\n    interaction.tapTime = prevTap.timeStamp;\n  }\n\n  return pointerEvent;\n}\n\nfunction collectEventTargets ({ interaction, pointer, event, eventTarget, type }) {\n  const pointerIndex = interaction.getPointerIndex(pointer);\n\n  // do not fire a tap event if the pointer was moved before being lifted\n  if (type === 'tap' && (interaction.pointerWasMoved\n      // or if the pointerup target is different to the pointerdown target\n      || !(interaction.downTargets[pointerIndex] && interaction.downTargets[pointerIndex] === eventTarget))) {\n    return [];\n  }\n\n  const path = utils.getPath(eventTarget);\n  const signalArg = {\n    interaction,\n    pointer,\n    event,\n    eventTarget,\n    type,\n    path,\n    targets: [],\n    element: null,\n  };\n\n  for (const element of path) {\n    signalArg.element = element;\n\n    signals.fire('collect-targets', signalArg);\n  }\n\n  if (type === 'hold') {\n    signalArg.targets = signalArg.targets.filter(target =>\n      target.eventable.options.holdDuration === interaction.holdTimers[pointerIndex].duration);\n  }\n\n  return signalArg.targets;\n}\n\nInteraction.signals.on('update-pointer-down', function ({ interaction, pointerIndex }) {\n  interaction.holdTimers[pointerIndex] = { duration: Infinity, timeout: null };\n});\n\nInteraction.signals.on('remove-pointer', function ({ interaction, pointerIndex }) {\n  interaction.holdTimers.splice(pointerIndex, 1);\n});\n\nInteraction.signals.on('move', function ({ interaction, pointer, event, eventTarget, duplicateMove }) {\n  const pointerIndex = interaction.getPointerIndex(pointer);\n\n  if (!duplicateMove && (!interaction.pointerIsDown || interaction.pointerWasMoved)) {\n    if (interaction.pointerIsDown) {\n      clearTimeout(interaction.holdTimers[pointerIndex].timeout);\n    }\n\n    fire({\n      interaction, pointer, event, eventTarget,\n      type: 'move',\n    });\n  }\n});\n\nInteraction.signals.on('down', function ({ interaction, pointer, event, eventTarget, pointerIndex }) {\n  const timer = interaction.holdTimers[pointerIndex];\n  const path = utils.getPath(eventTarget);\n  const signalArg = {\n    interaction,\n    pointer,\n    event,\n    eventTarget,\n    type: 'hold',\n    targets: [],\n    path,\n    element: null,\n  };\n\n  for (const element of path) {\n    signalArg.element = element;\n\n    signals.fire('collect-targets', signalArg);\n  }\n\n  if (!signalArg.targets.length) { return; }\n\n  let minDuration = Infinity;\n\n  for (const target of signalArg.targets) {\n    const holdDuration = target.eventable.options.holdDuration;\n\n    if (holdDuration < minDuration) {\n      minDuration = holdDuration;\n    }\n  }\n\n  timer.duration = minDuration;\n  timer.timeout = setTimeout(function () {\n    fire({\n      interaction,\n      eventTarget,\n      pointer,\n      event,\n      type: 'hold',\n    });\n  }, minDuration);\n});\n\nInteraction.signals.on('up', ({ interaction, pointer, event, eventTarget }) => {\n  if (!interaction.pointerWasMoved) {\n    fire({ interaction, eventTarget, pointer, event, type: 'tap' });\n  }\n});\n\nfor (const signalName of ['up', 'cancel']) {\n  Interaction.signals.on(signalName, function ({ interaction, pointerIndex }) {\n    if (interaction.holdTimers[pointerIndex]) {\n      clearTimeout(interaction.holdTimers[pointerIndex].timeout);\n    }\n  });\n}\n\nfunction createSignalListener (type) {\n  return function ({ interaction, pointer, event, eventTarget }) {\n    fire({ interaction, eventTarget, pointer, event, type });\n  };\n}\n\nfor (let i = 0; i < simpleSignals.length; i++) {\n  Interaction.signals.on(simpleSignals[i], createSignalListener(simpleEvents[i]));\n}\n\nInteraction.signals.on('new', function (interaction) {\n  interaction.prevTap    = null;  // the most recent tap event on this interaction\n  interaction.tapTime    = 0;     // time of the most recent tap event\n  interaction.holdTimers = [];    // [{ duration, timeout }]\n});\n\ndefaults.pointerEvents = pointerEvents.defaults;\nmodule.exports = pointerEvents;\n", "const pointerEvents = require('./base');\nconst Interaction   = require('../Interaction');\n\npointerEvents.signals.on('new', onNew);\npointerEvents.signals.on('fired', onFired);\n\nfor (const signal of ['move', 'up', 'cancel', 'endall']) {\n  Interaction.signals.on(signal, endHoldRepeat);\n}\n\nfunction onNew ({ pointerEvent }) {\n  if (pointerEvent.type !== 'hold') { return; }\n\n  pointerEvent.count = (pointerEvent.count || 0) + 1;\n}\n\nfunction onFired ({ interaction, pointerEvent, eventTarget, targets }) {\n  if (pointerEvent.type !== 'hold' || !targets.length) { return; }\n\n  // get the repeat interval from the first eventable\n  const interval = targets[0].eventable.options.holdRepeatInterval;\n\n  // don't repeat if the interval is 0 or less\n  if (interval <= 0) { return; }\n\n  // set a timeout to fire the holdrepeat event\n  interaction.holdIntervalHandle = setTimeout(function () {\n    pointerEvents.fire({\n      interaction,\n      eventTarget,\n      type: 'hold',\n      pointer: pointerEvent,\n      event: pointerEvent,\n    });\n  }, interval);\n}\n\nfunction endHoldRepeat ({ interaction }) {\n  // set the interaction's holdStopTime property\n  // to stop further holdRepeat events\n  if (interaction.holdIntervalHandle) {\n    clearInterval(interaction.holdIntervalHandle);\n    interaction.holdIntervalHandle = null;\n  }\n}\n\n// don't repeat by default\npointerEvents.defaults.holdRepeatInterval = 0;\npointerEvents.types.push('holdrepeat');\n\nmodule.exports = {\n  onNew,\n  onFired,\n  endHoldRepeat,\n};\n", "const pointerEvents = require('./base');\nconst Interactable  = require('../Interactable');\nconst is            = require('../utils/is');\nconst scope         = require('../scope');\nconst extend        = require('../utils/extend');\nconst { merge }     = require('../utils/arr');\n\npointerEvents.signals.on('collect-targets', function ({ targets, element, type, eventTarget }) {\n  scope.interactables.forEachMatch(element, interactable => {\n    const eventable = interactable.events;\n    const options = eventable.options;\n\n    if (eventable[type]\n      && is.element(element)\n      && interactable.testIgnoreAllow(options, element, eventTarget)) {\n\n      targets.push({\n        element,\n        eventable,\n        props: { interactable },\n      });\n    }\n  });\n});\n\nInteractable.signals.on('new', function ({ interactable }) {\n  interactable.events.getRect = function (element) {\n    return interactable.getRect(element);\n  };\n});\n\nInteractable.signals.on('set', function ({ interactable, options }) {\n  extend(interactable.events.options, pointerEvents.defaults);\n  extend(interactable.events.options, options);\n});\n\nmerge(Interactable.eventTypes, pointerEvents.types);\n\nInteractable.prototype.pointerEvents = function (options) {\n  extend(this.events.options, options);\n\n  return this;\n};\n\nconst __backCompatOption = Interactable.prototype._backCompatOption;\n\nInteractable.prototype._backCompatOption = function (optionName, newValue) {\n  const ret = __backCompatOption.call(this, optionName, newValue);\n\n  if (ret === this) {\n    this.events.options[optionName] = newValue;\n  }\n\n  return ret;\n};\n\nInteractable.settingsMethods.push('pointerEvents');\n", "const utils   = require('./utils');\nconst events  = require('./utils/events');\nconst signals = require('./utils/Signals').new();\n\nconst { getWindow } = require('./utils/window');\n\nconst scope = {\n  signals,\n  events,\n  utils,\n\n  // main document\n  document: require('./utils/domObjects').document,\n  // all documents being listened to\n  documents: [],\n\n  addDocument: function (doc, win) {\n    // do nothing if document is already known\n    if (utils.contains(scope.documents, doc)) { return false; }\n\n    win = win || getWindow(doc);\n\n    scope.documents.push(doc);\n    events.documents.push(doc);\n\n    // don't add an unload event for the main document\n    // so that the page may be cached in browser history\n    if (doc !== scope.document) {\n      events.add(win, 'unload', scope.onWindowUnload);\n    }\n\n    signals.fire('add-document', { doc, win });\n  },\n\n  removeDocument: function (doc, win) {\n    const index = scope.documents.indexOf(doc);\n\n    win = win || getWindow(doc);\n\n    events.remove(win, 'unload', scope.onWindowUnload);\n\n    scope.documents.splice(index, 1);\n    events.documents.splice(index, 1);\n\n    signals.fire('remove-document', { win, doc });\n  },\n\n  onWindowUnload: function () {\n    scope.removeDocument(this.document, this);\n  },\n};\n\nmodule.exports = scope;\n", "class Signals {\n  constructor () {\n    this.listeners = {\n      // signalName: [listeners],\n    };\n  }\n\n  on (name, listener) {\n    if (!this.listeners[name]) {\n      this.listeners[name] = [listener];\n      return;\n    }\n\n    this.listeners[name].push(listener);\n  }\n\n  off (name, listener) {\n    if (!this.listeners[name]) { return; }\n\n    const index = this.listeners[name].indexOf(listener);\n\n    if (index !== -1) {\n      this.listeners[name].splice(index, 1);\n    }\n  }\n\n  fire (name, arg) {\n    const targetListeners = this.listeners[name];\n\n    if (!targetListeners) { return; }\n\n    for (const listener of targetListeners) {\n      if (listener(arg, name) === false) {\n        return;\n      }\n    }\n  }\n}\n\nSignals.new = function () {\n  return new Signals();\n};\n\nmodule.exports = Signals;\n", "function contains (array, target) {\n  return array.indexOf(target) !== -1;\n}\n\nfunction merge (target, source) {\n  for (const item of source) {\n    target.push(item);\n  }\n\n  return target;\n}\n\nmodule.exports = {\n  contains,\n  merge,\n};\n", "const { window } = require('./window');\nconst is     = require('./is');\nconst domObjects = require('./domObjects');\n\nconst Element = domObjects.Element;\nconst navigator  = window.navigator;\n\nconst browser = {\n  // Does the browser support touch input?\n  supportsTouch: !!(('ontouchstart' in window) || is.function(window.DocumentTouch)\n                     && domObjects.document instanceof window.DocumentTouch),\n\n  // Does the browser support PointerEvents\n  supportsPointerEvent: !!domObjects.PointerEvent,\n\n  isIOS: (/iP(hone|od|ad)/.test(navigator.platform)),\n\n  // scrolling doesn't change the result of getClientRects on iOS 7\n  isIOS7: (/iP(hone|od|ad)/.test(navigator.platform)\n           && /OS 7[^\\d]/.test(navigator.appVersion)),\n\n  isIe9: /MSIE 9/.test(navigator.userAgent),\n\n  // prefix matchesSelector\n  prefixedMatchesSelector: 'matches' in Element.prototype\n    ? 'matches': 'webkitMatchesSelector' in Element.prototype\n    ? 'webkitMatchesSelector': 'mozMatchesSelector' in Element.prototype\n    ? 'mozMatchesSelector': 'oMatchesSelector' in Element.prototype\n    ? 'oMatchesSelector': 'msMatchesSelector',\n\n  pEventTypes: (domObjects.PointerEvent\n    ? (domObjects.PointerEvent === window.MSPointerEvent\n      ? {\n        up:     'MSPointerUp',\n        down:   'MSPointerDown',\n        over:   'mouseover',\n        out:    'mouseout',\n        move:   'MSPointerMove',\n        cancel: 'MSPointerCancel',\n      }\n      : {\n        up:     'pointerup',\n        down:   'pointerdown',\n        over:   'pointerover',\n        out:    'pointerout',\n        move:   'pointermove',\n        cancel: 'pointercancel',\n      })\n    : null),\n\n  // because Webkit and Opera still use 'mousewheel' event type\n  wheelEvent: 'onmousewheel' in domObjects.document? 'mousewheel': 'wheel',\n\n};\n\n// Opera Mobile must be handled differently\nbrowser.isOperaMobile = (navigator.appName === 'Opera'\n  && browser.supportsTouch\n  && navigator.userAgent.match('Presto'));\n\nmodule.exports = browser;\n", "const is = require('./is');\n\nmodule.exports = function clone (source) {\n  const dest = {};\n  for (const prop in source) {\n    if (is.plainObject(source[prop])) {\n      dest[prop] = clone(source[prop]);\n    } else {\n      dest[prop] = source[prop];\n    }\n  }\n  return dest;\n};\n", "const domObjects = {};\nconst win = require('./window').window;\n\nfunction blank () {}\n\ndomObjects.document           = win.document;\ndomObjects.DocumentFragment   = win.DocumentFragment   || blank;\ndomObjects.SVGElement         = win.SVGElement         || blank;\ndomObjects.SVGSVGElement      = win.SVGSVGElement      || blank;\ndomObjects.SVGElementInstance = win.SVGElementInstance || blank;\ndomObjects.Element            = win.Element            || blank;\ndomObjects.HTMLElement        = win.HTMLElement        || domObjects.Element;\n\ndomObjects.Event        = win.Event;\ndomObjects.Touch        = win.Touch || blank;\ndomObjects.PointerEvent = (win.PointerEvent || win.MSPointerEvent);\n\nmodule.exports = domObjects;\n", "const win        = require('./window');\nconst browser    = require('./browser');\nconst is         = require('./is');\nconst domObjects = require('./domObjects');\n\nconst domUtils = {\n  nodeContains: function (parent, child) {\n    while (child) {\n      if (child === parent) {\n        return true;\n      }\n\n      child = child.parentNode;\n    }\n\n    return false;\n  },\n\n  closest: function (element, selector) {\n    while (is.element(element)) {\n      if (domUtils.matchesSelector(element, selector)) { return element; }\n\n      element = domUtils.parentNode(element);\n    }\n\n    return null;\n  },\n\n  parentNode: function (node) {\n    let parent = node.parentNode;\n\n    if (is.docFrag(parent)) {\n      // skip past #shado-root fragments\n      while ((parent = parent.host) && is.docFrag(parent)) {\n        continue;\n      }\n\n      return parent;\n    }\n\n    return parent;\n  },\n\n  matchesSelector: function (element, selector) {\n    // remove /deep/ from selectors if shadowDOM polyfill is used\n    if (win.window !== win.realWindow) {\n      selector = selector.replace(/\\/deep\\//g, ' ');\n    }\n\n    return element[browser.prefixedMatchesSelector](selector);\n  },\n\n  // Test for the element that's \"above\" all other qualifiers\n  indexOfDeepestElement: function (elements) {\n    let deepestZoneParents = [];\n    let dropzoneParents = [];\n    let dropzone;\n    let deepestZone = elements[0];\n    let index = deepestZone? 0: -1;\n    let parent;\n    let child;\n    let i;\n    let n;\n\n    for (i = 1; i < elements.length; i++) {\n      dropzone = elements[i];\n\n      // an element might belong to multiple selector dropzones\n      if (!dropzone || dropzone === deepestZone) {\n        continue;\n      }\n\n      if (!deepestZone) {\n        deepestZone = dropzone;\n        index = i;\n        continue;\n      }\n\n      // check if the deepest or current are document.documentElement or document.rootElement\n      // - if the current dropzone is, do nothing and continue\n      if (dropzone.parentNode === dropzone.ownerDocument) {\n        continue;\n      }\n      // - if deepest is, update with the current dropzone and continue to next\n      else if (deepestZone.parentNode === dropzone.ownerDocument) {\n        deepestZone = dropzone;\n        index = i;\n        continue;\n      }\n\n      if (!deepestZoneParents.length) {\n        parent = deepestZone;\n        while (parent.parentNode && parent.parentNode !== parent.ownerDocument) {\n          deepestZoneParents.unshift(parent);\n          parent = parent.parentNode;\n        }\n      }\n\n      // if this element is an svg element and the current deepest is\n      // an HTMLElement\n      if (deepestZone instanceof domObjects.HTMLElement\n          && dropzone instanceof domObjects.SVGElement\n          && !(dropzone instanceof domObjects.SVGSVGElement)) {\n\n        if (dropzone === deepestZone.parentNode) {\n          continue;\n        }\n\n        parent = dropzone.ownerSVGElement;\n      }\n      else {\n        parent = dropzone;\n      }\n\n      dropzoneParents = [];\n\n      while (parent.parentNode !== parent.ownerDocument) {\n        dropzoneParents.unshift(parent);\n        parent = parent.parentNode;\n      }\n\n      n = 0;\n\n      // get (position of last common ancestor) + 1\n      while (dropzoneParents[n] && dropzoneParents[n] === deepestZoneParents[n]) {\n        n++;\n      }\n\n      const parents = [\n        dropzoneParents[n - 1],\n        dropzoneParents[n],\n        deepestZoneParents[n],\n      ];\n\n      child = parents[0].lastChild;\n\n      while (child) {\n        if (child === parents[1]) {\n          deepestZone = dropzone;\n          index = i;\n          deepestZoneParents = [];\n\n          break;\n        }\n        else if (child === parents[2]) {\n          break;\n        }\n\n        child = child.previousSibling;\n      }\n    }\n\n    return index;\n  },\n\n  matchesUpTo: function (element, selector, limit) {\n    while (is.element(element)) {\n      if (domUtils.matchesSelector(element, selector)) {\n        return true;\n      }\n\n      element = domUtils.parentNode(element);\n\n      if (element === limit) {\n        return domUtils.matchesSelector(element, selector);\n      }\n    }\n\n    return false;\n  },\n\n  getActualElement: function (element) {\n    return (element instanceof domObjects.SVGElementInstance\n      ? element.correspondingUseElement\n      : element);\n  },\n\n  getScrollXY: function (relevantWindow) {\n    relevantWindow = relevantWindow || win.window;\n    return {\n      x: relevantWindow.scrollX || relevantWindow.document.documentElement.scrollLeft,\n      y: relevantWindow.scrollY || relevantWindow.document.documentElement.scrollTop,\n    };\n  },\n\n  getElementClientRect: function (element) {\n    const clientRect = (element instanceof domObjects.SVGElement\n      ? element.getBoundingClientRect()\n      : element.getClientRects()[0]);\n\n    return clientRect && {\n      left  : clientRect.left,\n      right : clientRect.right,\n      top   : clientRect.top,\n      bottom: clientRect.bottom,\n      width : clientRect.width  || clientRect.right  - clientRect.left,\n      height: clientRect.height || clientRect.bottom - clientRect.top,\n    };\n  },\n\n  getElementRect: function (element) {\n    const clientRect = domUtils.getElementClientRect(element);\n\n    if (!browser.isIOS7 && clientRect) {\n      const scroll = domUtils.getScrollXY(win.getWindow(element));\n\n      clientRect.left   += scroll.x;\n      clientRect.right  += scroll.x;\n      clientRect.top    += scroll.y;\n      clientRect.bottom += scroll.y;\n    }\n\n    return clientRect;\n  },\n\n  getPath: function (element) {\n    const path = [];\n\n    while (element) {\n      path.push(element);\n      element = domUtils.parentNode(element);\n    }\n\n    return path;\n  },\n\n  trySelector: value => {\n    if (!is.string(value)) { return false; }\n\n    // an exception will be raised if it is invalid\n    domObjects.document.querySelector(value);\n    return true;\n  },\n};\n\nmodule.exports = domUtils;\n", "const is           = require('./is');\nconst domUtils     = require('./domUtils');\nconst pointerUtils = require('./pointerUtils');\nconst pExtend      = require('./pointerExtend');\n\nconst { window }   = require('./window');\nconst { contains } = require('./arr');\n\nconst elements = [];\nconst targets  = [];\n\n// {\n//   type: {\n//     selectors: ['selector', ...],\n//     contexts : [document, ...],\n//     listeners: [[listener, capture, passive], ...]\n//   }\n//  }\nconst delegatedEvents = {};\nconst documents       = [];\n\nconst supportsOptions = (() => {\n  let supported = false;\n\n  window.document.createElement('div').addEventListener('test', null, {\n    get capture () { supported = true; },\n  });\n\n  return supported;\n})();\n\nfunction add (element, type, listener, optionalArg) {\n  const options = getOptions(optionalArg);\n  let elementIndex = elements.indexOf(element);\n  let target = targets[elementIndex];\n\n  if (!target) {\n    target = {\n      events: {},\n      typeCount: 0,\n    };\n\n    elementIndex = elements.push(element) - 1;\n    targets.push(target);\n  }\n\n  if (!target.events[type]) {\n    target.events[type] = [];\n    target.typeCount++;\n  }\n\n  if (!contains(target.events[type], listener)) {\n    element.addEventListener(type, listener, supportsOptions? options : !!options.capture);\n    target.events[type].push(listener);\n  }\n}\n\nfunction remove (element, type, listener, optionalArg) {\n  const options = getOptions(optionalArg);\n  const elementIndex = elements.indexOf(element);\n  const target = targets[elementIndex];\n\n  if (!target || !target.events) {\n    return;\n  }\n\n  if (type === 'all') {\n    for (type in target.events) {\n      if (target.events.hasOwnProperty(type)) {\n        remove(element, type, 'all');\n      }\n    }\n    return;\n  }\n\n  if (target.events[type]) {\n    const len = target.events[type].length;\n\n    if (listener === 'all') {\n      for (let i = 0; i < len; i++) {\n        remove(element, type, target.events[type][i], options);\n      }\n      return;\n    }\n    else {\n      for (let i = 0; i < len; i++) {\n        if (target.events[type][i] === listener) {\n          element.removeEventListener(`on${type}`, listener, supportsOptions? options : !!options.capture);\n          target.events[type].splice(i, 1);\n\n          break;\n        }\n      }\n    }\n\n    if (target.events[type] && target.events[type].length === 0) {\n      target.events[type] = null;\n      target.typeCount--;\n    }\n  }\n\n  if (!target.typeCount) {\n    targets.splice(elementIndex, 1);\n    elements.splice(elementIndex, 1);\n  }\n}\n\nfunction addDelegate (selector, context, type, listener, optionalArg) {\n  const options = getOptions(optionalArg);\n  if (!delegatedEvents[type]) {\n    delegatedEvents[type] = {\n      selectors: [],\n      contexts : [],\n      listeners: [],\n    };\n\n    // add delegate listener functions\n    for (const doc of documents) {\n      add(doc, type, delegateListener);\n      add(doc, type, delegateUseCapture, true);\n    }\n  }\n\n  const delegated = delegatedEvents[type];\n  let index;\n\n  for (index = delegated.selectors.length - 1; index >= 0; index--) {\n    if (delegated.selectors[index] === selector\n        && delegated.contexts[index] === context) {\n      break;\n    }\n  }\n\n  if (index === -1) {\n    index = delegated.selectors.length;\n\n    delegated.selectors.push(selector);\n    delegated.contexts .push(context);\n    delegated.listeners.push([]);\n  }\n\n  // keep listener and capture and passive flags\n  delegated.listeners[index].push([listener, !!options.capture, options.passive]);\n}\n\nfunction removeDelegate (selector, context, type, listener, optionalArg) {\n  const options = getOptions(optionalArg);\n  const delegated = delegatedEvents[type];\n  let matchFound = false;\n  let index;\n\n  if (!delegated) { return; }\n\n  // count from last index of delegated to 0\n  for (index = delegated.selectors.length - 1; index >= 0; index--) {\n    // look for matching selector and context Node\n    if (delegated.selectors[index] === selector\n        && delegated.contexts[index] === context) {\n\n      const listeners = delegated.listeners[index];\n\n      // each item of the listeners array is an array: [function, capture, passive]\n      for (let i = listeners.length - 1; i >= 0; i--) {\n        const [fn, capture, passive] = listeners[i];\n\n        // check if the listener functions and capture and passive flags match\n        if (fn === listener && capture === !!options.capture && passive === options.passive) {\n          // remove the listener from the array of listeners\n          listeners.splice(i, 1);\n\n          // if all listeners for this interactable have been removed\n          // remove the interactable from the delegated arrays\n          if (!listeners.length) {\n            delegated.selectors.splice(index, 1);\n            delegated.contexts .splice(index, 1);\n            delegated.listeners.splice(index, 1);\n\n            // remove delegate function from context\n            remove(context, type, delegateListener);\n            remove(context, type, delegateUseCapture, true);\n\n            // remove the arrays if they are empty\n            if (!delegated.selectors.length) {\n              delegatedEvents[type] = null;\n            }\n          }\n\n          // only remove one listener\n          matchFound = true;\n          break;\n        }\n      }\n\n      if (matchFound) { break; }\n    }\n  }\n}\n\n// bound to the interactable context when a DOM event\n// listener is added to a selector interactable\nfunction delegateListener (event, optionalArg) {\n  const options = getOptions(optionalArg);\n  const fakeEvent = {};\n  const delegated = delegatedEvents[event.type];\n  const [eventTarget] = (pointerUtils.getEventTargets(event));\n  let element = eventTarget;\n\n  // duplicate the event so that currentTarget can be changed\n  pExtend(fakeEvent, event);\n\n  fakeEvent.originalEvent = event;\n  fakeEvent.preventDefault = preventOriginalDefault;\n\n  // climb up document tree looking for selector matches\n  while (is.element(element)) {\n    for (let i = 0; i < delegated.selectors.length; i++) {\n      const selector = delegated.selectors[i];\n      const context = delegated.contexts[i];\n\n      if (domUtils.matchesSelector(element, selector)\n          && domUtils.nodeContains(context, eventTarget)\n          && domUtils.nodeContains(context, element)) {\n\n        const listeners = delegated.listeners[i];\n\n        fakeEvent.currentTarget = element;\n\n        for (let j = 0; j < listeners.length; j++) {\n          const [fn, capture, passive] = listeners[j];\n\n          if (capture === !!options.capture && passive === options.passive) {\n            fn(fakeEvent);\n          }\n        }\n      }\n    }\n\n    element = domUtils.parentNode(element);\n  }\n}\n\nfunction delegateUseCapture (event) {\n  return delegateListener.call(this, event, true);\n}\n\nfunction preventOriginalDefault () {\n  this.originalEvent.preventDefault();\n}\n\nfunction getOptions (param) {\n  return is.object(param)? param : { capture: param };\n}\n\nmodule.exports = {\n  add,\n  remove,\n\n  addDelegate,\n  removeDelegate,\n\n  delegateListener,\n  delegateUseCapture,\n  delegatedEvents,\n  documents,\n\n  supportsOptions,\n\n  _elements: elements,\n  _targets: targets,\n};\n", "module.exports = function extend (dest, source) {\n  for (const prop in source) {\n    dest[prop] = source[prop];\n  }\n  return dest;\n};\n", "const {\n  resolveRectLike,\n  rectToXY,\n} = require('./rect');\n\nmodule.exports = function (target, element, action) {\n  const actionOptions = target.options[action];\n  const actionOrigin = actionOptions && actionOptions.origin;\n  const origin = actionOrigin || target.options.origin;\n\n  const originRect = resolveRectLike(origin, target, element, [target && element]);\n\n  return rectToXY(originRect) || { x: 0, y: 0 };\n};\n", "module.exports = (x, y) =>  Math.sqrt(x * x + y * y);\n", "const extend = require('./extend');\nconst win    = require('./window');\n\nconst utils = {\n  warnOnce: function (method, message) {\n    let warned = false;\n\n    return function () {\n      if (!warned) {\n        win.window.console.warn(message);\n        warned = true;\n      }\n\n      return method.apply(this, arguments);\n    };\n  },\n\n  // http://stackoverflow.com/a/5634528/2280888\n  _getQBezierValue: function (t, p1, p2, p3) {\n    const iT = 1 - t;\n    return iT * iT * p1 + 2 * iT * t * p2 + t * t * p3;\n  },\n\n  getQuadraticCurvePoint: function (startX, startY, cpX, cpY, endX, endY, position) {\n    return {\n      x:  utils._getQBezierValue(position, startX, cpX, endX),\n      y:  utils._getQBezierValue(position, startY, cpY, endY),\n    };\n  },\n\n  // http://gizma.com/easing/\n  easeOutQuad: function (t, b, c, d) {\n    t /= d;\n    return -c * t*(t-2) + b;\n  },\n\n  copyAction: function (dest, src) {\n    dest.name  = src.name;\n    dest.axis  = src.axis;\n    dest.edges = src.edges;\n\n    return dest;\n  },\n\n  is         : require('./is'),\n  extend     : extend,\n  hypot      : require('./hypot'),\n  getOriginXY: require('./getOriginXY'),\n};\n\nextend(utils, require('./arr'));\nextend(utils, require('./domUtils'));\nextend(utils, require('./pointerUtils'));\nextend(utils, require('./rect'));\n\nmodule.exports = utils;\n", "const scope   = require('../scope');\nconst utils   = require('./index');\n\nconst finder = {\n  methodOrder: [ 'simulationResume', 'mouseOrPen', 'hasPointer', 'idle' ],\n\n  search: function (pointer, eventType, eventTarget) {\n    const pointerType = utils.getPointerType(pointer);\n    const pointerId = utils.getPointerId(pointer);\n    const details = { pointer, pointerId, pointerType, eventType, eventTarget };\n\n    for (const method of finder.methodOrder) {\n      const interaction = finder[method](details);\n\n      if (interaction) {\n        return interaction;\n      }\n    }\n  },\n\n  // try to resume simulation with a new pointer\n  simulationResume: function ({ pointerType, eventType, eventTarget }) {\n    if (!/down|start/i.test(eventType)) {\n      return null;\n    }\n\n    for (const interaction of scope.interactions) {\n      let element = eventTarget;\n\n      if (interaction.simulation && interaction.simulation.allowResume\n          && (interaction.pointerType === pointerType)) {\n        while (element) {\n          // if the element is the interaction element\n          if (element === interaction.element) {\n            return interaction;\n          }\n          element = utils.parentNode(element);\n        }\n      }\n    }\n\n    return null;\n  },\n\n  // if it's a mouse or pen interaction\n  mouseOrPen: function ({ pointerId, pointerType, eventType }) {\n    if (pointerType !== 'mouse' && pointerType !== 'pen') {\n      return null;\n    }\n\n    let firstNonActive;\n\n    for (const interaction of scope.interactions) {\n      if (interaction.pointerType === pointerType) {\n        // if it's a down event, skip interactions with running simulations\n        if (interaction.simulation && !utils.contains(interaction.pointerIds, pointerId)) { continue; }\n\n        // if the interaction is active, return it immediately\n        if (interaction.interacting()) {\n          return interaction;\n        }\n        // otherwise save it and look for another active interaction\n        else if (!firstNonActive) {\n          firstNonActive = interaction;\n        }\n      }\n    }\n\n    // if no active mouse interaction was found use the first inactive mouse\n    // interaction\n    if (firstNonActive) {\n      return firstNonActive;\n    }\n\n    // find any mouse or pen interaction.\n    // ignore the interaction if the eventType is a *down, and a simulation\n    // is active\n    for (const interaction of scope.interactions) {\n      if (interaction.pointerType === pointerType && !(/down/i.test(eventType) && interaction.simulation)) {\n        return interaction;\n      }\n    }\n\n    return null;\n  },\n\n  // get interaction that has this pointer\n  hasPointer: function ({ pointerId }) {\n    for (const interaction of scope.interactions) {\n      if (utils.contains(interaction.pointerIds, pointerId)) {\n        return interaction;\n      }\n    }\n  },\n\n  // get first idle interaction with a matching pointerType\n  idle: function ({ pointerType }) {\n    for (const interaction of scope.interactions) {\n      // if there's already a pointer held down\n      if (interaction.pointerIds.length === 1) {\n        const target = interaction.target;\n        // don't add this pointer if there is a target interactable and it\n        // isn't gesturable\n        if (target && !target.options.gesture.enabled) {\n          continue;\n        }\n      }\n      // maximum of 2 pointers per interaction\n      else if (interaction.pointerIds.length >= 2) {\n        continue;\n      }\n\n      if (!interaction.interacting() && (pointerType === interaction.pointerType)) {\n        return interaction;\n      }\n    }\n\n    return null;\n  },\n};\n\nmodule.exports = finder;\n", "const win        = require('./window');\nconst isWindow   = require('./isWindow');\n\nconst is = {\n  array   : () => {},\n\n  window  : thing => thing === win.window || isWindow(thing),\n\n  docFrag : thing => is.object(thing) && thing.nodeType === 11,\n\n  object  : thing => !!thing && (typeof thing === 'object'),\n\n  function: thing => typeof thing === 'function',\n\n  number  : thing => typeof thing === 'number'  ,\n\n  bool    : thing => typeof thing === 'boolean' ,\n\n  string  : thing => typeof thing === 'string'  ,\n\n  element: thing => {\n    if (!thing || (typeof thing !== 'object')) { return false; }\n\n    const _window = win.getWindow(thing) || win.window;\n\n    return (/object|function/.test(typeof _window.Element)\n      ? thing instanceof _window.Element //DOM2\n      : thing.nodeType === 1 && typeof thing.nodeName === 'string');\n  },\n\n  plainObject: thing => is.object(thing) && thing.constructor.name === 'Object',\n};\n\nis.array = thing => (is.object(thing)\n  && (typeof thing.length !== 'undefined')\n  && is.function(thing.splice));\n\nmodule.exports = is;\n", "module.exports = (thing) => !!(thing && thing.Window) && (thing instanceof thing.Window);\n", "function pointerExtend (dest, source) {\n  for (const prop in source) {\n    const prefixedPropREs = module.exports.prefixedPropREs;\n    let deprecated = false;\n\n    // skip deprecated prefixed properties\n    for (const vendor in prefixedPropREs) {\n      if (prop.indexOf(vendor) === 0 && prefixedPropREs[vendor].test(prop)) {\n        deprecated = true;\n        break;\n      }\n    }\n\n    if (!deprecated && typeof source[prop] !== 'function') {\n      dest[prop] = source[prop];\n    }\n  }\n  return dest;\n}\n\npointerExtend.prefixedPropREs = {\n  webkit: /(Movement[XY]|Radius[XY]|RotationAngle|Force)$/,\n};\n\nmodule.exports = pointerExtend;\n", "const hypot         = require('./hypot');\nconst browser       = require('./browser');\nconst dom           = require('./domObjects');\nconst domUtils      = require('./domUtils');\nconst domObjects    = require('./domObjects');\nconst is            = require('./is');\nconst pointerExtend = require('./pointerExtend');\n\nconst pointerUtils = {\n  copyCoords: function (dest, src) {\n    dest.page = dest.page || {};\n    dest.page.x = src.page.x;\n    dest.page.y = src.page.y;\n\n    dest.client = dest.client || {};\n    dest.client.x = src.client.x;\n    dest.client.y = src.client.y;\n\n    dest.timeStamp = src.timeStamp;\n  },\n\n  setCoordDeltas: function (targetObj, prev, cur) {\n    targetObj.page.x    = cur.page.x    - prev.page.x;\n    targetObj.page.y    = cur.page.y    - prev.page.y;\n    targetObj.client.x  = cur.client.x  - prev.client.x;\n    targetObj.client.y  = cur.client.y  - prev.client.y;\n    targetObj.timeStamp = cur.timeStamp - prev.timeStamp;\n\n    // set pointer velocity\n    const dt = Math.max(targetObj.timeStamp / 1000, 0.001);\n\n    targetObj.page.speed   = hypot(targetObj.page.x, targetObj.page.y) / dt;\n    targetObj.page.vx      = targetObj.page.x / dt;\n    targetObj.page.vy      = targetObj.page.y / dt;\n\n    targetObj.client.speed = hypot(targetObj.client.x, targetObj.page.y) / dt;\n    targetObj.client.vx    = targetObj.client.x / dt;\n    targetObj.client.vy    = targetObj.client.y / dt;\n  },\n\n  isNativePointer: function  (pointer) {\n    return (pointer instanceof dom.Event || pointer instanceof dom.Touch);\n  },\n\n  // Get specified X/Y coords for mouse or event.touches[0]\n  getXY: function (type, pointer, xy) {\n    xy = xy || {};\n    type = type || 'page';\n\n    xy.x = pointer[type + 'X'];\n    xy.y = pointer[type + 'Y'];\n\n    return xy;\n  },\n\n  getPageXY: function (pointer, page) {\n    page = page || {};\n\n    // Opera Mobile handles the viewport and scrolling oddly\n    if (browser.isOperaMobile && pointerUtils.isNativePointer(pointer)) {\n      pointerUtils.getXY('screen', pointer, page);\n\n      page.x += window.scrollX;\n      page.y += window.scrollY;\n    }\n    else {\n      pointerUtils.getXY('page', pointer, page);\n    }\n\n    return page;\n  },\n\n  getClientXY: function (pointer, client) {\n    client = client || {};\n\n    if (browser.isOperaMobile && pointerUtils.isNativePointer(pointer)) {\n      // Opera Mobile handles the viewport and scrolling oddly\n      pointerUtils.getXY('screen', pointer, client);\n    }\n    else {\n      pointerUtils.getXY('client', pointer, client);\n    }\n\n    return client;\n  },\n\n  getPointerId: function (pointer) {\n    return is.number(pointer.pointerId)? pointer.pointerId : pointer.identifier;\n  },\n\n  setCoords: function (targetObj, pointers, timeStamp) {\n    const pointer = (pointers.length > 1\n                     ? pointerUtils.pointerAverage(pointers)\n                     : pointers[0]);\n\n    const tmpXY = {};\n\n    pointerUtils.getPageXY(pointer, tmpXY);\n    targetObj.page.x = tmpXY.x;\n    targetObj.page.y = tmpXY.y;\n\n    pointerUtils.getClientXY(pointer, tmpXY);\n    targetObj.client.x = tmpXY.x;\n    targetObj.client.y = tmpXY.y;\n\n    targetObj.timeStamp = is.number(timeStamp) ? timeStamp :new Date().getTime();\n  },\n\n  pointerExtend: pointerExtend,\n\n  getTouchPair: function (event) {\n    const touches = [];\n\n    // array of touches is supplied\n    if (is.array(event)) {\n      touches[0] = event[0];\n      touches[1] = event[1];\n    }\n    // an event\n    else {\n      if (event.type === 'touchend') {\n        if (event.touches.length === 1) {\n          touches[0] = event.touches[0];\n          touches[1] = event.changedTouches[0];\n        }\n        else if (event.touches.length === 0) {\n          touches[0] = event.changedTouches[0];\n          touches[1] = event.changedTouches[1];\n        }\n      }\n      else {\n        touches[0] = event.touches[0];\n        touches[1] = event.touches[1];\n      }\n    }\n\n    return touches;\n  },\n\n  pointerAverage: function (pointers) {\n    const average = {\n      pageX  : 0,\n      pageY  : 0,\n      clientX: 0,\n      clientY: 0,\n      screenX: 0,\n      screenY: 0,\n    };\n\n    for (const pointer of pointers) {\n      for (const prop in average) {\n        average[prop] += pointer[prop];\n      }\n    }\n    for (const prop in average) {\n      average[prop] /= pointers.length;\n    }\n\n    return average;\n  },\n\n  touchBBox: function (event) {\n    if (!event.length && !(event.touches && event.touches.length > 1)) {\n      return;\n    }\n\n    const touches = pointerUtils.getTouchPair(event);\n    const minX = Math.min(touches[0].pageX, touches[1].pageX);\n    const minY = Math.min(touches[0].pageY, touches[1].pageY);\n    const maxX = Math.max(touches[0].pageX, touches[1].pageX);\n    const maxY = Math.max(touches[0].pageY, touches[1].pageY);\n\n    return {\n      x: minX,\n      y: minY,\n      left: minX,\n      top: minY,\n      width: maxX - minX,\n      height: maxY - minY,\n    };\n  },\n\n  touchDistance: function (event, deltaSource) {\n    const sourceX = deltaSource + 'X';\n    const sourceY = deltaSource + 'Y';\n    const touches = pointerUtils.getTouchPair(event);\n\n\n    const dx = touches[0][sourceX] - touches[1][sourceX];\n    const dy = touches[0][sourceY] - touches[1][sourceY];\n\n    return hypot(dx, dy);\n  },\n\n  touchAngle: function (event, prevAngle, deltaSource) {\n    const sourceX = deltaSource + 'X';\n    const sourceY = deltaSource + 'Y';\n    const touches = pointerUtils.getTouchPair(event);\n    const dx = touches[1][sourceX] - touches[0][sourceX];\n    const dy = touches[1][sourceY] - touches[0][sourceY];\n    const angle = 180 * Math.atan2(dy , dx) / Math.PI;\n\n    return  angle;\n  },\n\n  getPointerType: function (pointer) {\n    return is.string(pointer.pointerType)\n      ? pointer.pointerType\n      : is.number(pointer.pointerType)\n        ? [undefined, undefined,'touch', 'pen', 'mouse'][pointer.pointerType]\n          // if the PointerEvent API isn't available, then the \"pointer\" must\n          // be either a MouseEvent, TouchEvent, or Touch object\n          : /touch/.test(pointer.type) || pointer instanceof domObjects.Touch\n            ? 'touch'\n            : 'mouse';\n  },\n\n  // [ event.target, event.currentTarget ]\n  getEventTargets: function (event) {\n    const path = is.function(event.composedPath) ? event.composedPath() : event.path;\n\n    return [\n      domUtils.getActualElement(path ? path[0] : event.target),\n      domUtils.getActualElement(event.currentTarget),\n    ];\n  },\n};\n\nmodule.exports = pointerUtils;\n", "const { window } = require('./window');\n\nconst vendors = ['ms', 'moz', 'webkit', 'o'];\nlet lastTime = 0;\nlet request;\nlet cancel;\n\nfor (let x = 0; x < vendors.length && !window.requestAnimationFrame; x++) {\n  request = window[vendors[x] + 'RequestAnimationFrame'];\n  cancel = window[vendors[x] +'CancelAnimationFrame'] || window[vendors[x] + 'CancelRequestAnimationFrame'];\n}\n\nif (!request) {\n  request = function (callback) {\n    const currTime = new Date().getTime();\n    const timeToCall = Math.max(0, 16 - (currTime - lastTime));\n    const id = setTimeout(function () { callback(currTime + timeToCall); },\n                          timeToCall);\n\n    lastTime = currTime + timeToCall;\n    return id;\n  };\n}\n\nif (!cancel) {\n  cancel = function (id) {\n    clearTimeout(id);\n  };\n}\n\nmodule.exports = {\n  request,\n  cancel,\n};\n", "const extend = require('./extend');\nconst is = require('./is');\nconst {\n  closest,\n  parentNode,\n  getElementRect,\n} = require('./domUtils');\n\nconst rectUtils = {\n  getStringOptionResult: function (value, interactable, element) {\n    if (!is.string(value)) {\n      return null;\n    }\n\n    if (value === 'parent') {\n      value = parentNode(element);\n    }\n    else if (value === 'self') {\n      value = interactable.getRect(element);\n    }\n    else {\n      value = closest(element, value);\n    }\n\n    return value;\n  },\n\n  resolveRectLike: function (value, interactable, element, functionArgs) {\n    value = rectUtils.getStringOptionResult(value, interactable, element) || value;\n\n    if (is.function(value)) {\n      value = value.apply(null, functionArgs);\n    }\n\n    if (is.element(value)) {\n      value = getElementRect(value);\n    }\n\n    return value;\n  },\n\n  rectToXY: function (rect) {\n    return  rect && {\n      x: 'x' in rect ? rect.x : rect.left,\n      y: 'y' in rect ? rect.y : rect.top,\n    };\n  },\n\n  xywhToTlbr: function (rect) {\n    if (rect && !('left' in rect && 'top' in rect)) {\n      rect = extend({}, rect);\n\n      rect.left   = rect.x || 0;\n      rect.top    = rect.y || 0;\n      rect.right  = rect.right   || (rect.left + rect.width);\n      rect.bottom = rect.bottom  || (rect.top + rect.height);\n    }\n\n    return rect;\n  },\n\n  tlbrToXywh: function (rect) {\n    if (rect && !('x' in rect && 'y' in rect)) {\n      rect = extend({}, rect);\n\n      rect.x      = rect.left || 0;\n      rect.top    = rect.top  || 0;\n      rect.width  = rect.width  || (rect.right  - rect.x);\n      rect.height = rect.height || (rect.bottom - rect.y);\n    }\n\n    return rect;\n  },\n};\n\nmodule.exports = rectUtils;\n", "const win = module.exports;\nconst isWindow = require('./isWindow');\n\nfunction init (window) {\n  // get wrapped window if using Shadow DOM polyfill\n\n  win.realWindow = window;\n\n  // create a TextNode\n  const el = window.document.createTextNode('');\n\n  // check if it's wrapped by a polyfill\n  if (el.ownerDocument !== window.document\n      && typeof window.wrap === 'function'\n    && window.wrap(el) === el) {\n    // use wrapped window\n    window = window.wrap(window);\n  }\n\n  win.window = window;\n}\n\nif (typeof window === 'undefined') {\n  win.window     = undefined;\n  win.realWindow = undefined;\n}\nelse {\n  init(window);\n}\n\nwin.getWindow = function getWindow (node) {\n  if (isWindow(node)) {\n    return node;\n  }\n\n  const rootNode = (node.ownerDocument || node);\n\n  return rootNode.defaultView || rootNode.parentWindow || win.window;\n};\n\nwin.init = init;\n"]}