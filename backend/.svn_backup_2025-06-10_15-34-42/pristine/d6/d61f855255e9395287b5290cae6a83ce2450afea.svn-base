<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

$error = false;
if( isset($_POST['add_credit']) ){
    if( is_numeric($_POST['discount']) && $_POST['discount'] > 0 ){
        $comment = sprintf(_("Avoir pour le client n°%s."),$_GET['usr']);
        $code =	 strtoupper(pmt_codes_generated());
        $label = "Avoir ".$code;
        $cod = pmt_codes_add( $label, _PMT_TYPE_CREDIT, $code, $comment );
        if( $cod ){
            $r = pmt_offers_add($cod, _PMT_TYPE_CREDIT, $_POST['discount'], $_POST['discount-type'], '', 0, 0, 0, 'order', date('d/m/Y H:i:s'), null, 0, false );
            $r = $r && pmt_codes_set_include_pmt( $cod, true );
            $r = $r && pmt_codes_set_all_customers( $cod, false );
            $r = $r && pmt_users_add( $cod, $_GET['usr'], 1 );
            $r = $r && pmt_codes_set_all_catalog( $cod, true );
            if( !$r ){
                $error = true;
                pmt_codes_del( $cod );
                pmt_offers_del( $cod );
            }
        }else{
            $error = true;
        }
    }else{
        $error = true; 
    }

    if( $error ){
        $error = _("Une erreur est survenue lors de la création de l'avoir.");
    }else{
        $_SESSION['riashop']['avoir'] = _('L\'avoir à bien été créé.' );
        print '
            <script>
                window.parent.hidePopup();
                window.parent.location.reload();
                window.parent.scrollTo(0, 0);
            </script>';
    }
}



define('ADMIN_PAGE_TITLE', 'Création d\'un avoir');
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');
require_once('admin/skin/header.inc.php');

?>
<?php
    if( $error ){
        print '<div class="error">'.$error.'</div>';
    }
?>
<h2><?php print _('Créer un avoir pour ').gu_users_get_name($_GET['usr']) ?></h2>

<form action="/admin/customers/popup-add-promo.php?usr=<?php print $_GET['usr']; ?>" method="POST">
    <label for="discount">Appliquer une réduction de : </label>
    <input type="text" maxlength="9" class="price" id="discount" name="discount">
    <select name="discount-type" id="select-discount-type">
        <option value="0">€ HT</option>
        <option selected="selected" value="1">%</option>	
    </select>
    <input type="submit" name="add_credit" id="add_credit" value="<?php print _('Créer l\'avoir'); ?>" />
</form>