<?php

    /** \file ajax-progress-update.php
     *  Permet d'actualiser l'affichage de la barre de progression pour les imports et les exports
     *
     *  Ce fichier est appelé dans :
     *      -/admin/tools/imports/index.php et /admin/tools/imports/mapping.php par la fonction reloadProgressBar dans le fichier tools/imports.js
     *      -/admin/tools/exports/index.php par la fonction reloadProgressBar dans le fichier tools/exports.js
	 *
	 * 	Les paramètres acceptés sont les suivants (utiliser l'un ou l'autre mais pas les deux ensemble) :
	 * 		- imp : Identifiant de l'import dont on souhaite interroger l'état. 0 est accepté.
	 * 		- exp : Identifiant de l'export dont on souhaite interroger l'état
     */

	// Vérifie que l'utilisateur en cours à le droit de consulter les imports
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT');

    require_once('imports.inc.php');
    require_once('exports.inc.php');

	// Import
    if( isset($_GET['imp']) ){

        $is_system = null;
        $is_sync = null;

        if( $_GET['imp'] > 0 ){

            $r_import = ipt_imports_get( $_GET['imp'], false, false, '', 0, 0, false, false, '', '', array(), $is_system, 0, $is_sync );
            if( !$r_import || !ria_mysql_num_rows($r_import) ){
                exit;
			}

        }else{
            // Récupère l'import en cours de traitement
            $r_import = ipt_imports_get( 0, false, false, '', 0, 0, 'processing', false, '', '', array(), $is_system, 0, $is_sync );
            if( !$r_import || !ria_mysql_num_rows($r_import) ){
                exit;
            }
        }

        $import = ria_mysql_fetch_assoc($r_import);

        // Si l'import est terminé, ne plus afficher la barre de progression mais seulement son état
        if( $import['state'] != 'processing' ){

			// Résume l'état de l'import
			$title = '';
            switch ($import['state']) {
                case 'finished':
                    $title = _('Import réussi !');
                    break;
                case 'error':
                    $title = _('Erreur, l\'import n\'a pu être finalisé');
                    break;
                case 'warning':
                    $title = _('Attention! Import effectué partiellement');
                    break;
			}

			// Recherche l'identifiant du rapport
			$report_id = 0;
			$reports = ipt_reports_get( 0, $import['id'] );
			if( ria_mysql_num_rows($reports) ){
				$report_id = ria_mysql_result( $reports, 0, 0 );
			}

			$data['title'] = $title;
            $data['html'] = ipt_state_display( $import['state'] );
            $data['updated_state'] = $import['state'];
			$data['report'] = $report_id;
            echo json_encode( $data );
            exit;
        }

        ob_start();
        // Construction de l'html permetant d'afficher une barre de progression
        if( $import['state']=='processing' ){

            $r_report = ipt_reports_get( 0, $import['id'] );

            if( $r_report && ria_mysql_num_rows($r_report) ){
                $report = ria_mysql_fetch_assoc( $r_report );

                $line_processed = ipt_import_nb_line_processed( $report['id'] );
                if( $line_processed != $import['line_count'] ){
                    $progression_txt = $line_processed."/".$import['line_count'];
                }else{
                    $progression_txt = _("Finalisation");
                }

                echo ipt_state_display($import['state']);?>
                <div class="progress-bar">
                    <div class="bar-progression" data-progress=<?php echo $line_processed ?> data-progress-max=<?php echo $import['line_count'] ?>></div>
                    <div class="span-progression"><?php echo $progression_txt ?></div>
                </div><?php

            }
        }

        $data['title'] =  _('Import en cours');
        $data['imp'] = $import['id'];
        $data['html'] = ob_get_contents();
        ob_end_clean();

        echo json_encode($data);
        exit;
    }


    if( isset($_GET['exp']) ){
        $res = array();
        $exp = json_decode( $_GET['exp'] );

        $r_export = exp_exports_get( 0, array('processing', 'pending') );
        if( $r_export ){
            while( $export = ria_mysql_fetch_assoc($r_export) ){
                ob_start();

                if( $export['state'] == 'pending' ){
                    $progression_txt = "0/...";
                }else{
                    $progression_txt = $export['line_processed']."/".$export['line_count'];
                }

                echo exp_state_display($export['state']);?>
                <div class="progress-bar">
                    <div class="bar-progression" data-progress=<?php echo $export['line_processed']; ?> data-progress-max=<?php echo $export['line_count'] ?>></div>
                    <div class="span-progression"><?php echo $progression_txt ?></div>
                </div><?php

                $content = ob_get_contents();
                $res[$export['id']]['class'] = exp_exports_state_css_class( $export['state']);
                $res[$export['id']]['html'] = $content;
                ob_end_clean();
            }
        }

        // Si un export qui était en état processing n'est pas présent dans $res, c'est qu'il est maintenant terminé
        if( count($exp) ){
            foreach( $exp as $e ){
                if( !array_key_exists($e, $res) ){
                    $r_export = exp_exports_get( $e );
                    if( $r_export && ria_mysql_num_rows($r_export) ){
                        $export = ria_mysql_fetch_assoc($r_export);

                        ob_start();?>

                        <tr>
                            <td class="align-center">
                                <input type="checkbox" name="exp[]" value="<?php echo $export['id'] ?>" >
                            </td>
                            <td><a href="/admin/tools/exports/index.php?download=1&exp=<?php echo $export['id']; ?>"><?php echo htmlspecialchars( $export['name'] ); ?></a></td>
							<td><?php echo fld_classes_get_name( $export['cls_id'] ) ?></td>
							<td class="number" data-label="<?php print _('Nbr de lignes :'); ?> "><?php print number_format( $export['line_count'], 0, ',', ' ' ); ?></td>
                            <td><?php echo ria_date_format( $export['date_created'] ) ?></td>
                            <td class="<?php echo exp_exports_state_css_class($export['state']) ?>">
                                <?php echo exp_state_display($export['state']);?>
                            </td>
                        </tr><?php
                        $contents = ob_get_contents();
                        ob_end_clean();

                        $res[$export['id']]['class'] = exp_exports_state_css_class($export['state']);
                        $res[$export['id']]['html'] = $contents;
                    }
                }
            }
        }

        echo json_encode( json_encode($res) );
        exit;
    }