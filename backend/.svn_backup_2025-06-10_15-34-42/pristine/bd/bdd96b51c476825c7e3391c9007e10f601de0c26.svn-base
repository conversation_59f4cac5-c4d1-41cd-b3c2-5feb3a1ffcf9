<?xml version="1.0" encoding="UTF-8"?>
<GetOrderResponse xmlns="https://mws.amazonservices.com/Orders/2011-01-01">
  <GetOrderResult>
    <Orders>
      <!--1 or more repetitions:-->
      <Order>
        <AmazonOrderId>string</AmazonOrderId>
        <!--Optional:-->
        <SellerOrderId>string</SellerOrderId>
        <PurchaseDate>2008-09-29T01:49:45</PurchaseDate>
        <LastUpdateDate>2014-09-18T23:18:33</LastUpdateDate>
        <OrderStatus>Pending</OrderStatus>
        <!--Optional:-->
        <FulfillmentChannel>AFN</FulfillmentChannel>
        <!--Optional:-->
        <SalesChannel>string</SalesChannel>
        <!--Optional:-->
        <OrderChannel>string</OrderChannel>
        <!--Optional:-->
        <ShipServiceLevel>string</ShipServiceLevel>
        <!--Optional:-->
        <ShippingAddress>
          <Name>string</Name>
          <!--Optional:-->
          <AddressLine1>string</AddressLine1>
          <!--Optional:-->
          <AddressLine2>string</AddressLine2>
          <!--Optional:-->
          <AddressLine3>string</AddressLine3>
          <!--Optional:-->
          <City>string</City>
          <!--Optional:-->
          <County>string</County>
          <!--Optional:-->
          <District>string</District>
          <!--Optional:-->
          <StateOrRegion>string</StateOrRegion>
          <!--Optional:-->
          <PostalCode>string</PostalCode>
          <!--Optional:-->
          <CountryCode>string</CountryCode>
          <!--Optional:-->
          <Phone>string</Phone>
        </ShippingAddress>
        <!--Optional:-->
        <OrderTotal>
          <!--Optional:-->
          <CurrencyCode>string</CurrencyCode>
          <!--Optional:-->
          <Amount>string</Amount>
        </OrderTotal>
        <!--Optional:-->
        <NumberOfItemsShipped>100</NumberOfItemsShipped>
        <!--Optional:-->
        <NumberOfItemsUnshipped>100</NumberOfItemsUnshipped>
        <!--Optional:-->
        <PaymentExecutionDetail>
          <!--1 or more repetitions:-->
          <PaymentExecutionDetailItem>
            <Payment>
              <!--Optional:-->
              <CurrencyCode>string</CurrencyCode>
              <!--Optional:-->
              <Amount>string</Amount>
            </Payment>
            <PaymentMethod>string</PaymentMethod>
          </PaymentExecutionDetailItem>
        </PaymentExecutionDetail>
        <!--Optional:-->
        <PaymentMethod>CVS</PaymentMethod>
        <!--Optional:-->
        <MarketplaceId>string</MarketplaceId>
        <!--Optional:-->
        <BuyerEmail>string</BuyerEmail>
        <!--Optional:-->
        <BuyerName>string</BuyerName>
        <!--Optional:-->
        <ShipmentServiceLevelCategory>string</ShipmentServiceLevelCategory>
        <!--Optional:-->
        <ShippedByAmazonTFM>false</ShippedByAmazonTFM>
        <!--Optional:-->
        <TFMShipmentStatus>string</TFMShipmentStatus>
      </Order>
    </Orders>
  </GetOrderResult>
  <ResponseMetadata>
    <RequestId>string</RequestId>
  </ResponseMetadata>
</GetOrderResponse>