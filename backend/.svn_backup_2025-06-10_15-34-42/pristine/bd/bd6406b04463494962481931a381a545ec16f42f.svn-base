//#################################################################//
//						  Gestion des dates		     			   //
//#################################################################//
	$(document).ready(function(){
		// Permet d'afficher le sélectionneur de date pour les champs de type date
		loadDatePicker();
		
		$('select').each(function() {
			var id = (new RegExp('^type-([0-9]+)$')).exec($(this).attr('id'));
			if (id && id[1] !== undefined) displayCumulate(id[1]);
		});

		if( $('#all-price-prd').length ){
			getAllPricesPrd( $('#all-price-prd').val() );
		}
	});
	
	/**
	 *	Cette fonction charge le contenu du tableau contenant tous les tarifs par catégories tarifaire d'un produit
	 */
	function getAllPricesPrd( prd ){
		$.get('/admin/catalog/ajax-price-conditions.php?get-all-price=1&prd=' + prd, function( data ){
			$('#all-prices').find('tbody').html( data );
		});
	}

	/**
	 *	Cette fonction permet d'afficher ou de masquer l'info-bulle sur les actions
	 */
	function displayRefresh(save, button){
		if( $("#"+save).css('display') != 'none' ){
			$("#"+save).hide(); $("#"+button).show();
		}else{
			$("#"+save).show(); $("#"+button).hide();
		}
	}
	
	/**
	 *	Cette fonction permet d'afficher ou de masquer la fonction cumulé
	 */
	function displayCumulate(id){
		$("#unique-"+id).parent()[$("#type-"+id).val()==1 ? 'hide' : 'show']();
		var sel = $('#type-'+id);
		if (sel.length) $('#valeur-type-'+id)[sel.val() == 1 ? 'show' : 'hide']();
	}
	
	/**
	 * Cette fonction permet de charger ou de recharger le sélectionneur de date pour les champs de type date
	 */
	function loadDatePicker(){
		// removeMessage();
		
		// Parcours tous les champs de type date
		$('input.datepicker').each(function(){
			var temp = this ;
			
			// Implémente le sélecteur de date sur chacun d'entre eux.
			$(this).DatePicker({
				format:'d/m/Y',
				date: $(this).val(),
				current: $(this).val(),
				starts: 1,
				onChange: function(formated, dates){
					var date = $(temp).val();
					if(dates != 'Invalid Date'){
						$(temp).val(formated);
						if( formated!=date )
							$(temp).DatePickerHide();
					}
				}
			});
			
		});	
		
		// Parcours tous les champs de type heure
		$("input.hourpicker").each(function(){
			$(this).autocomplete({
				source:["00:00", "00:30", "01:00","01:30", "02:00", "02:30", "03:00", "03:30","04:00", "04:30", "05:00", "05:30", "06:00","06:30", "07:00", "07:30", "08:00", "08:30","09:00", "09:30", "10:00", "10:30", "11:00","11:30", "12:00", "12:30", "13:00", "13:30","14:00", "14:30", "15:00", "15:30", "16:00","16:30", "17:00", "17:30", "18:00", "18:30","19:00", "19:30", "20:00", "20:30", "21:00","21:30", "22:00", "22:30", "23:00", "23:30"],
				minChars: 0,
				delay:200,
				max:15,
				width:75
			});
		});
	}
	
//#################################################################//
//				Gestion des conditions de tarification			   //
//#################################################################//

	/** Cette fonction permet de gérer les composants de condition selon le champ libre choisi
	 *	@param id Identifiant d'un tarif ou d'une TVA
	 *	@param nb Nombre affecté à cette condition
	 *	@param newCdt True si il s'agit d'un formulaire d'ajout de tarif ou de TVA, False dans le cas contraire
	 *	@param tva True s'il s'agit de gérer les conditions d'une TVA, False pour celles des tarifs
	 */
	function cdtForm(id, nb,  newCdt, tva, ecotaxe, addCdt){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();
		
		// Détermine les identifiants des éléments graphique
		var idFld = '';
		if( tva ) idFld = idFld+'-tva';
		if( ecotaxe ) idFld = idFld+'-eco';
		
		// Récupère la champ ligne
		var fld = newCdt ? $("#cdt-new-"+nb).val() : $("#cdt"+idFld+"-"+id+"-"+nb).val();
			
		// Requête AJAX pour afficher le formulaire d'ajout
		$.ajax({
			type: "POST",
			url: '/admin/catalog/ajax-price-conditions.php',
			data: 'fld='+fld+'&id='+id+'&nb='+nb+'&new='+newCdt+'&tva='+tva+'&ecotaxe='+ecotaxe+'&addCdt='+addCdt,
			async:false,
			success: function(msg){
				
				// On affiche les nouveaux composants correspondant au type de champ libre sélectionné
				if( newCdt ){
					$("#condition-new-"+nb).html(msg);
				}else{
					$("#condition"+idFld+"-"+id+"-"+nb).html(msg);
				}
			}
		});
		loadDatePicker();
	}
	
	/** Cette fonction permet de gérer le changement de symbole
	 *	@param id Identifiant d'un tarif ou d'une TVA
	 *	@param nb Nombre affecté à cette condition
	 *	@param newCdt True s'il s'agit d'un formulaire d'ajout de tarif ou de TVA, False dans le cas contraire
	 *	@param tva True s'il s'agit de gérer les conditions d'une TVA, False pour celles des tarifs
	 *	@param selectDate Par défault à false, correspond au champ libre de type date
	 */
	function chargInputVal(id, nb, newCdt, tva, ecotaxe, addCdt){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();
		
		// Récupère le champ libre
		var fld;
		if( ecotaxe ){
			fld = $("#cdt-eco-"+nb).val();
		}else{
			fld = newCdt ? $("#cdt-new-"+nb).val() : $("#cdt"+( tva==1 ? '-tva' : '' )+"-"+id+"-"+nb).val();
		}
			
		// Récupère le symbole
		var sb;
		if( ecotaxe ){
			sb = $("#sbl-eco-"+id+"-"+nb).val();
		}else if( newCdt ){
			sb = $("#sbl"+( tva==1 ? '-tva' : '' )+"-new-"+nb).val();
		}else{
			sb = $("#sbl"+( tva==1 ? '-tva' : '' )+"-"+id+"-"+nb).val();
		}
			
		// Lance le rechargement des composants de la condition
		$.ajax({
			type: "POST",
			url: '/admin/catalog/ajax-price-conditions.php',
			data: 'fld='+fld+'&id='+id+'&nb='+nb+'&new='+newCdt+'&sbl='+sb+'&tva='+tva+'&ecotaxe='+ecotaxe+'&addCdt='+addCdt,
			async:false,
			success: function(msg){
				// On affiche les nouveaux composants correspondant au type de champ libre sélectionné
				if( ecotaxe )
					$("#condition-eco-"+nb).html(msg);
				else if( newCdt )
					$("#condition-new-"+nb).html(msg);
				else
					$("#condition"+( tva==1 ? '-tva' : '' )+"-"+id+"-"+nb).html(msg);
			}
		});
		return true;
	}
	
	/** Cette fonction permet de recharger les composants des conditions quand on choisi un champ de type date
	 *	@param price True s'il s'agit d'un formulaire de tarif, False s'il s'agit celui d'une TVA
	 *	@param newCdt True s'il s'agit d'un formulaire d'ajout d'un tarif ou d'une TVA, False dans le cas contraire
	 *	@param id Identifiant de la condition
	 *	@param nb Nombre affecté à cette condition
	 *	@param sbl Symnole déjà sélectionné, si False alors on récupère le symbole choisi
	 */
	function addInputDate(price, newCdt, id, nb, ecotaxe){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();

		price = price ? 1 : 0;
		newCdt = newCdt ? 1 : 0;
		ecotaxe = ecotaxe ? 1 : 0;
		var tva = !price && !ecotaxe ? 1 : 0;
		
		// Récupère le champ libre
		var fld;
		if( ecotaxe ){
			fld = $("#cdt-eco-"+nb).val();
		}else{
			fld = newCdt ? $("#cdt-new-"+nb).val() : $("#cdt"+( price ? '' : '-tva' )+"-"+id+"-"+nb).val();
		}
			
		// Récupère le symbole
		var sb;
		if( ecotaxe ){
			sb = $("#sbl-eco-"+id+"-"+nb).val();
		}else if( newCdt ){
			sb = $("#sbl"+( tva==1 ? '-tva' : '' )+"-new-"+nb).val();
		}else{
			sb = $("#sbl"+( tva==1 ? '-tva' : '' )+"-"+id+"-"+nb).val();
		}
			
		// Récupère le choisi pour le champ libre de date
		var selectDate = '';
		if( ecotaxe ){
			selectDate = $("#selectDate-eco-"+nb).val();
		}else if( newCdt ){
			selectDate = $("#selectDate"+( price ? '' : '-tva' )+"-new-"+nb).val();
		}else{
			selectDate = $("#selectDate"+( price ? '' : '-tva' )+"-"+id+"-"+nb).val();
		}
		
		// Rafraichit les composants de la condition
		$.ajax({
			type: "POST",
			url: '/admin/catalog/ajax-price-conditions.php',
			data: 'fld='+fld+'&id='+id+'&nb='+nb+'&new='+newCdt+'&sbl='+sb+'&tva='+tva+'&selectDate='+selectDate+'&ecotaxe='+ecotaxe,
			async:false,
			success: function(msg){
				// On affiche les nouveaux composants correspondant au type de champ libre sélectionné
				if( ecotaxe ){
					$("#condition-eco-"+nb).html(msg);
				}else if( newCdt ){
					$("#condition-new-"+nb).html(msg);
				}else{
					$("#condition"+( price ? '' : '-tva' )+"-"+id+"-"+nb).html(msg);
				}
			}
		});
		loadDatePicker();
		return true;
	}
	
	/** Cette fonction permet la suppression d'une condition
	 *	@param price True s'il s'agit d'un tarif, False si il s'agit d'une TVA
	 *	@param id Identifiant de la TVA ou du tarif
	 *	@param cdt Identifiant de la condition
	 *	@param newForm True s'il s'agit d'un formulaire d'ajout d'un tarif ou d'une TVA, False dans le cas contraire
	 *	@param newCdt True s'il s'agit d'une nouvelle condition, False s'il s'agit d'une condition enregistrée dans la base
	 */
	function delCdt( price, id, cdt, newForm, newCdt, ecotaxe, nb, group ){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();
		
		// Détermine l'identifiant des composants à supprimer
		var idFld = '';
		if( !price && !ecotaxe ) idFld = idFld+'-tva';
		if( ecotaxe ) idFld = idFld+'-eco';
		
		if( !newForm ){ // Il ne s'agit pas d'un formulaire d'ajout
			if( newCdt ){
				// Suppression graphique seule quand il s'agit d'une nouvelle condition
				$("#js-new-condition"+idFld+"-"+id+"-"+cdt).remove(); 
				$("#js-new-condition2"+idFld+"-"+id+"-"+cdt).remove(); 
				$("#cdt"+idFld+"-"+id+"-"+cdt).remove(); 
				$("#condition"+idFld+"-"+id+"-"+cdt).remove(); 
				$("#del-cdt"+idFld+"-"+id+"-"+cdt).remove();
				return true;
			}
			
			// Demande de confirmation
			if( price ) {
				
				// Il s'agit d'une condition d'un tarif
				confirmDel = window.confirm(priceTvaConditionConfirmSuppressionTarif);
				
			} else if( !price && $("#cdts"+idFld+"-"+id+" img.del-cdt").length==1 ) {
				
				// S'il ne reste qu'une condition dans la liste, on renvoie vers la suppression d'une liste
				if( ecotaxe ) delEco(id); else delTva(id);
				return true;
				
			} else {
				
				// Il s'agit d'une condition dans une liste
				confirmDel = window.confirm(priceTvaConditionConfirmSuppressionListe);
				
			}
			
			// Si la confirmation est donné par l'utilisateur
			if( confirmDel ){
				
				// Gestion des paramètres
				var datas;
				if( group ){
					datas = 'delGrpPrice=1&grp='+id+'&fld='+cdt;
				}else if( ecotaxe ){
					datas = 'delCdtEco=1&grp='+id+'&cdt='+cdt;
				}else{
					datas = price ? 'delCdtPrice=1&price='+id+'&fld='+cdt : 'delCdtTva=1&tva='+id+'&fld='+cdt;
				}
				
				// Requête AJAX de suppression d'une condition
				$.ajax({
					type: "POST",
					url: '/admin/catalog/ajax-new-price-tva.php',
					data: datas,
					async:false,
					success: function(xml){
						if( $(xml).find('result').attr('type') == '1' ){
							
							// Si la suppression dans la base à fonctionnée, on supprime les éléments graphique
							if( group ){
								$("#condition-new-"+nb).remove(); $("#cdt-new-"+nb).remove(); $("#del-cdt-new-"+nb).remove();
							} else {
								$("#condition"+idFld+"-"+id+"-"+nb).remove(); $("#cdt"+idFld+"-"+id+"-"+nb).remove(); $("#del-cdt"+idFld+"-"+id+"-"+nb).remove();
							}
							// Gestion du message de réussite
							$("#cdts"+idFld+"-"+id).prepend("<div class=\"error-success\">La suppression de la condition a fonctionnée.");
							
						} else {
							// Gestion des messages en cas d'erreur de suppressio
							if( price ){
								$("#cdts-"+id).prepend("<div class=\"error\">" + priceTvaConditionErreurSuppressionCondition + "</div>");
							}else{
								$("#cdts-tva-"+id).prepend("<div class=\"error\">" + priceTvaConditionErreurSuppressionCondition + "</div>");
							}
						}
					}
				});
				
			}
		} else {
			// Il s'agit d'un formulaire d'ajout
			$("#cdt-new-"+cdt).remove(); $("#condition-new-"+cdt).remove(); $("#del-cdt-new-"+cdt).remove();
		}
		if( $("#cdt-header").length ){
			if( $("#cdts select").length<=0 ){
				$("#cdt-header").hide();
				$("#cdts").hide();
			}
		}
		return true;
	}
	/** Cette fonction permet de gérer l'autosélection de l'input type=radio
	 *	@param choose Choix réalisé (identifiant de l'input type=radio)
	 */
	function changeChooseDate(choose){
		$("#"+choose).attr("checked","checked");
	}
//#################################################################//
//					Action possible pour les tarifs			       //
//#################################################################//

	/** Cette fonction permet de supprimer le formulaire d'ajout de tarif
	 *	@param newForm True s'il s'agit d'un formulaire d'ajout de tarif, False s'il s'agit d'un tarif déjà enregistré dans la base (dans ce cas, il sera supprimé de celle-ci)
	 *	@param idForm Identifiant du formulaire
	 *	@param id Identifiant du tarif
	 */
	function delPrice(newForm, id, cat, prd, isPromo){
		
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();

		displayRefresh("prices-del-"+id, "save-load-"+id);
		if( newForm ){
			
			// Il s'agit d'un formulaire d'ajout
			$("#price-new").remove();
			
		} else {
			
			// Demande de confirmation
			var confirmDel = true;
			if( !isPromo )
				confirmDel = window.confirm(priceTvaConditionConfirmSuppressionTarifConditionnel);
			
			// La confirmation est donné par l'utilisateur
			if( confirmDel ){
			
				// Gestion des identifiants produit et catégorie
				var cat = cat ? cat : 0;
				var prd = prd ? prd : 0;
				
				// Requête AJAX de suppression d'un tarif
				$.ajax({
					type: "POST",
					url: '/admin/catalog/ajax-new-price-tva.php',
					data: 'del=1&price='+id+'&cat='+cat+'&prd='+prd,
					dataType: 'xml',
					success: function(xml) {
						
						// Si la suppression a réussie
						if( $(xml).find('result').attr('type') == '1' ){
							// Supprime l'élément graphique
							$("#cdts-"+id).parent().remove();
							
							// Gestion du message d'erreur
							$("#lst-prices").before("<div class=\"error-success\">" + priceTvaConditionSuccesSuppressionTarif + "</div>");
							
							// S'il n'y a plus de tarif conditionnel, on affiche le message Aucun tarif trouvé
							if( $("#lst-prices tbody tr").length == 2 ){
								$("#none-price").show();
							}
							
							if( $("#lst-pmt tbody tr").length == 1 ){
								$("#none-price").show();
							}
						} else{
							// Gestion des messages d'erreur
							$("#lst-prices").before("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
							$("#lst-pmt").before("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
						}

						getAllPricesPrd( prd );
					}
				});
			} else {
				return false;
			}
			displayRefresh("prices-del-"+id, "save-load-"+id);
			return true;
		}
	}
	/** Cette fonction permet de lancer l'enregistrement du tarif
	 *	@param idForm identifiant du formulaire
	 *	@param id Identifiant du tarif
	 */
	function priceSubmit(idForm, id, cat, prd){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();

		displayRefresh("prices-save-"+id, "save-load-"+id);
		
		if( $("#val-type-"+id).val()=='' ){
			$("#cdts-"+id).prepend("<div class=\"error\">" + priceTvaConditionErreruValeurSaveTarif + "</div>");
			displayRefresh("prices-save-"+id, "save-load-"+id);
		} else {
			$("#id-price").val(id);
			var cat = cat ? cat : 0;
			var prd = prd ? prd : 0;
			var datas = $("#"+idForm).serialize();     
			$.ajax({
				type: 'POST',      
				url: '/admin/catalog/ajax-new-price-tva.php',
				data: 'addPrice=1&formulaire='+idForm+'&cat='+cat+'&prd='+prd+'&'+datas,     
				dataType: 'xml',
				success: function(xml) {
					var idPrc = $(xml).find('result').attr('idPrc');
					if( $(xml).find('result').attr('type') == '1' ){
						if( id>0 ){
							$("#prc-line-"+id).html($(xml).find('cdt-line').text());
							displayCumulate(id);
						}else{
							$("#price-new").before('<tr id="prc-line-'+idPrc+'">'+$(xml).find('cdt-line').text()+'</tr>');
							$("#cdts-0").parent().html('');
						}
						
						loadDatePicker();
						$("#cdts-"+idPrc).prepend("<div class=\"error-success\">" + priceTvaConditionSuccesEnregistrementTarif + "</div>");
					}else{ 
						$("#cdts-"+idPrc).prepend("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
						displayRefresh("prices-save-"+id, "save-load-"+id);
					}

					getAllPricesPrd( prd );
				}
			});
		}
	}
//#################################################################//
//					Action possible pour les TVAs			       //
//#################################################################//

	/** Cette fonction permet de supprimer le formulaire d'ajout de tva
	 *	@param newForm S'il s'agit d'un nouveau formulaire
	 *	@param id  Identifiant de la TVA
	 */
	function delTva(id){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();

		displayRefresh("grp-tva-del-"+id, "save-load-tva-"+id);
		if( $("#cdts-tva-"+id+" img.del-cdt").length==1 )
			confirmDel = window.confirm(priceTvaConditionConfirmSuppressionOnlyConditionListe);
		else
			confirmDel = window.confirm(priceTvaConditionConfirmSuppressionListeCondition);
		if( confirmDel ){
			$.ajax({
				type: "POST",
				url: '/admin/catalog/ajax-new-price-tva.php',
				data: 'del=1&tva='+id,
				dataType: 'xml',
				success: function(xml) {
					if( $(xml).find('result').attr('type') == '1' ){
						$("#orTva-"+id).remove();
						$("#grpTva-"+id).remove();
						$("#grps-tva").before("<div class=\"error-success\">" + priceTvaConditionSuccesSuppressionListeCondition + "</div>");
						if( $("#grps-tva tbody tr").length == 2 ){
							$("#none-grp-tva").show();
						}
						if( $("#grps-tva td.conditions").length==1 ){
							$("#grps-tva td.or-cdt").each(function(){
								$(this).remove();
							});
						}
					} else{
						$("#grps-tva").before("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
					}
					
					// Supprime les séparateurs "OU"
					$("#grps-tva td.or-cdt").parent().each(function(){
						$(this).remove();
					});
					
					// Recréer les séparateurs "OU" entre deux listes de conditions
					var nbGrpEco = $("#grps-tva td.conditions").length - 1;
					$("#grps-tva td.conditions").parent().each(function(){
						if( nbGrpEco>0 )
							$(this).after('<tr><td class="or-cdt" colspan="2">OU</td></tr>');
						nbGrpEco--;
					});
					
					displayRefresh("grp-tva-del-"+id, "save-load-tva-"+id);
				}
			});
		}
		displayRefresh("grp-tva-del-"+id, "save-load-tva-"+id);
	}
	/** Cette fonction permet de lancer l'enregistrement d'une tva
	 *	@param idForm Identifiant du formulaire
	 *	@param id Identifiant de la TVA
	 */
	function tvaSubmit(idForm, id){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();

		var datas = $("#"+idForm).serialize();     

		displayRefresh("grp-tva-save-"+id, "save-load-tva-"+id);
		$.ajax({
			type: 'POST',      
			url: '/admin/catalog/ajax-new-price-tva.php',
			data: 'addTva=1&formulaire='+idForm+'&id-tva='+id+'&'+datas,     
			dataType: 'xml',
			success: function(xml) {
				if( $(xml).find('result').attr('type') == '1' ){
					$("#grps-tva-new").before($(xml).find('cdt-line').text());
					$("#or-cdt-new").remove();
					$("#grps-tva-new").html('<td></td>');
					loadDatePicker();
					$("#cdts-tva-"+$(xml).find('result').attr('idTva')).prepend("<div class=\"error-success\">" + priceTvaConditionSuccesEnregistrementListeCondition + "</div>");
				} else if( $(xml).find('result').attr('type') == '2' ) {
					$("#cdts-tva-"+id).html($(xml).find('cdt-line').text());
					loadDatePicker();
					$("#cdts-tva-"+id).prepend("<div class=\"error-success\">" + priceTvaConditionSuccesMajListeCondition + "</div>");
				} else {
					$("#cdts-tva-"+id).prepend("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
				}
				displayRefresh("grp-tva-save-"+id, "save-load-tva-"+id);
			}
		});
	}
	
//#################################################################//
//				   Action sur les ecotaxes      	  			   //
//#################################################################//
	/** Cette fonction permet de supprimer un groupe de condition pour l'exonération de l'écotaxe
	 *	@param newForm S'il s'agit d'un nouveau formulaire
	 *	@param id  Identifiant de la TVA
	 */
	function delEco(id){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();

		displayRefresh("grp-eco-del-"+id, "save-load-eco-"+id);

		// Affichage du message de confirmation
		if( $("#cdts-eco-"+id+" img.del-cdt").length==1 )
			confirmDel = window.confirm(priceTvaConditionConfirmSuppressionOnlyConditionListe);
		else
			confirmDel = window.confirm(priceTvaConditionConfirmSuppressionOnlyConditionEcotaxe);
		
		// Si l'utilisateur a donné sa confirmation
		if( confirmDel ){
			$.ajax({
				type: "POST",
				url: '/admin/catalog/ajax-new-price-tva.php',
				data: 'del=1&eco='+id,
				dataType: 'xml',
				success: function(xml) {
					if( $(xml).find('result').attr('type') == '1' ){
						$("#grpEco-"+id).remove();
						$("#grps-eco").before("<div class=\"error-success\">" + priceTvaConditionSuccesSuppressionListeCondition + "</div>");
						if( $("#grps-eco tbody tr").length == 2 ){
							$("#none-grp-eco").show();
						}
						
						// Supprime les séparateurs "OU"
						$("#grps-eco td.or-cdt").parent().each(function(){
							$(this).remove();
						});
						
						// Recréer les séparateurs "OU" entre deux listes de conditions
						var nbGrpEco = $("#grps-eco td.conditions").length - 1;
						$("#grps-eco td.conditions").parent().each(function(){
							if( nbGrpEco>0 )
								$(this).after('<tr><td class="or-cdt" colspan="2">OU</td></tr>');
							nbGrpEco--;
						});
					} else{
						$("#grps-eco").before("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
					}
					displayRefresh("grp-eco-del-"+id, "save-load-eco-"+id);
				}
			});
		}
		displayRefresh("grp-eco-del-"+id, "save-load-eco-"+id);
	}

	/** Cette fonction permet de lancer l'enregistrement d'une liste de condition pour l'exonération de l'écotaxe
	 *	@param idForm identifiant du formulaire
	 *	@param id identifiant du groupe, 0 pour un nouveau
	 */
	function ecoSubmit(idForm, id){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();

		var datas = $("#"+idForm).serialize();
		
		$.ajax({
			type: 'POST',
			url: '/admin/catalog/ajax-new-price-tva.php',
			data: 'addEco=1&formulaire='+idForm+'&id-eco='+id+'&'+datas,
			dataType: 'xml',
			success: function( xml ){
				displayRefresh("grp-eco-save-"+id, "save-load-eco-"+id);
				if( $(xml).find('result').attr('type') == '1' ){
					$("#grps-eco-new").before($(xml).find('cdt-line').text());
					$("#or-cdt-new").remove();
					$("#grps-eco-new").html('<td></td>');
					loadDatePicker();
					$("#cdts-eco-"+$(xml).find('result').attr('idTva')).prepend("<div class=\"error-success\">" + priceTvaConditionSuccesSuppressionListeEcotaxe + "</div>");
				} else if( $(xml).find('result').attr('type') == '2' ) {
					$("#cdts-eco-"+id).html($(xml).find('cdt-line').text());
					loadDatePicker();
					$("#cdts-eco-"+id).prepend("<div class=\"error-success\">" + priceTvaConditionSuccesEnregistrementListeEcotaxe + "</div>");
				} else {
					$("#cdts-eco-"+id).prepend("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
				}
			}
		});
		displayRefresh("grp-eco-save-"+id, "save-load-eco-"+id);
	}

//#################################################################//
//				   Action sur les ecotaxes      	  			   //
//#################################################################//
	/** Cette fonction permet de lancer l'enregistrement du tarif
	 *	@param idForm identifiant du formulaire
	 *	@param id Identifiant du tarif
	 */
	function promoSubmit(idForm, id, prd){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();

		displayRefresh("prices-save-"+id, "save-load-"+id);
		
		if( $("#val-type-"+id).val()=='' ){
			$("#cdts-"+id).prepend("<div class=\"error\">" + priceTvaConditionErreruValeurSaveTarif + "</div>");
			displayRefresh("prices-save-"+id, "save-load-"+id);
		} else {
			$("#id-price").val(id);
			var prd = prd ? prd : 0;
			var datas = $("#"+idForm).serialize();     
			$.ajax({
				type: 'POST',      
				url: '/admin/catalog/ajax-new-price-tva.php',
				data: 'addPromo=1&formulaire='+idForm+'&id-pmt='+id+'&prd='+prd+'&'+datas,     
				dataType: 'xml',
				success: function(xml) {
					var idPrc = $(xml).find('result').attr('idPrc');
					if( $(xml).find('result').attr('type') == '1' ){
						if( id>0 ){
							$("#prc-line-"+id).html($(xml).find('cdt-line').text());
							displayCumulate(id);
						}else{
							$("#price-new").before('<tr id="prc-line-'+idPrc+'">'+$(xml).find('cdt-line').text()+'</tr>');
							$("#cdts-0").parent().html('');
						}
						
						// Modification le total dans la phare au dessus du tableau
						loadDatePicker();
						$("#cdts-"+idPrc).prepend("<div class=\"error-success\">" + priceTvaConditionSuccesEnregistrementPromo + "</div>");
					}else{
						$("#cdts-"+idPrc).prepend("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
						displayRefresh("prices-save-"+id, "save-load-"+id);
					}
				}
			});
		}
	}

//#################################################################//
//			   Action sur les formulaires d'ajout	  			   //
//#################################################################//
	/** Cette fonction permet d'afficher le formulaire d'ajout d'un tarif ou d'une tva conditionnelle.
	 *	@param type s'il s'agit d'un price 'price', ou  d'une tva 'tva'
	 *	@param form Le formulaire auquel ajouter le groupe de champs
	 *	@param cat identifiant de la catégorie sur laquelle va s'appliquer la condition. Indiquer 0 si produit ou condition d'exonération de TVA
	 *	@param prd identifiant du produit sur lequel va s'appliquer le tarif d'exception. Indiquer 0 catégorie ou condition d'exonération de TVA
	 */
	function newForm(type, form, cat, prd){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();

		var cat = cat ? cat : 0;
		var prd = prd ? prd : 0;

		if( type!='price' )
			$("#or-cdt-new").remove();
		$.ajax({
			type: "POST",
			url: '/admin/catalog/ajax-new-price-tva.php',
			data: 'type='+type+'&form='+form+'&cat='+cat+'&prd='+prd,
			dataType: 'xml',
			success: function(xml) {
				if( type=='price' ){ // Nouveau tarif
					// Affiche le formulaire
					$("#price-new").html( $(xml).find('cdt-line').text() );
					$("#price-new").show();
					displayCumulate(0);
				}else if( type=='tva' ){ // Nouvelle liste de condition pour tva
					// Affiche le formulaire
					$("#grps-tva-new").html( $(xml).find('cdt-line').text() );
					$("#grps-tva-new").show();
					// Affiche le séparateur "OU"
					if( $("#grps-tva tbody tr").length > 2 ){
						$("#grps-tva-new").before('<tr id="or-cdt-new"><td class="or-cdt" colspan="2">' + priceTvaConditionOu + '</td></tr>');
					}
				} else if( type=='eco' ){ // Nouvelle liste de condition pour ecotaxe
					// Affiche le formulaire
					$("#grps-eco-new").html( $(xml).find('cdt-line').text() );
					$("#grps-eco-new").show();
					// Affiche le séparateur
					if( $("#grps-eco tbody tr").length > 2 )
						$("#grps-eco-new").before('<tr id="or-cdt-new"><td class="or-cdt" colspan="2">' + priceTvaConditionOu + '</td></tr>');
				}
				// Masque les autres formulaire d'ajout (tva et ecotaxe)
				cancelNewForm(type);

				// Initialise les champs date
				loadDatePicker();
			}
		});
		loadDatePicker();
	}
	/**
	 * 	Cette fonction retirer les formulaires d'ajout
	 *	@param type type de formulaire. Les valeurs possibles sont 'price', 'tva', 'eco'
	 */
	function cancelNewForm(type){
		// Retire les messages d'erreur et de succès actuellement affichés
		removeMessages();
		
		// Masque l'information qu'aucune donnée n'a été trouvée
		$("#none-price").hide();
		$("#none-grp-tva").hide();
		$("#none-grp-eco").hide();
		
		// Affiche qu'aucun tarif est enregistré (sauf lors d'ajout d'un tarif)
		if( type!='price' && $("#lst-prices tbody tr").length == 2 )
			$("#none-price").show();
		
		// Affiche qu'aucune liste de condition pour les tva n'est enregistrée (sauf lors de l'ajout d'une liste)
		if( type!='tva' && $("#grps-tva tbody tr").length == 2 )
			$("#none-grp-tva").show();
		
		// Affiche qu'une liste de condition pour les ecotaxe n'est enregistrée (sauf lors de l'ajout d'une liste)
		if( type!='eco' && $("#grps-eco tbody tr").length == 2 )
			$("#none-grp-eco").show();
			
		// Supprime le formulaire d'ajout d'un tarif
		if( type!='price' ){
			$("#price-new").html('<td colspan="4"></td>');
			$("#price-new").hide();
		}
		
		// Supprime le formulaire d'ajout d'une liste de condition pour la tva
		if( type!='tva' ){
			$("#grps-tva-new").html('<td colspan="2"></td>');
			$("#grps-tva-new").hide();
		}
		
		// Supprime le formulaire d'ajout d'une liste de condition pour l'ecotaxe
		if( type!='eco' ){
			$("#grps-eco-new").html('<td colspan="2"></td>');
			$("#grps-eco-new").hide();
		}
		
		// Suprime le séparateur "OU"
		if( type!='eco' && type!='tva' ){
			$("#or-cdt-new").remove();
		}
	}