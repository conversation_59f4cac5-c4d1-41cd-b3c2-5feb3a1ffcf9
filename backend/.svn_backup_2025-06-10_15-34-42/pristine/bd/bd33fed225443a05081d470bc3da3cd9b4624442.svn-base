$(document).ready(function() {
	if( typeof $('#tbl-dispo-fld') != 'undefined' && $('#tbl-dispo-fld').length ){
		riaSortable.create({
			'table'			:	$('#tbl-dispo-fld'),
			'rowPrefix'		:	'tr-fld-',
			'url'			:	'/admin/ajax/config/ajax-price-position-update.php'
		});
	}

	// script pour afficher les options lorsqu'on sélectionne un paramètre dans le tableau "Catégorie tarifaire suivant le site"
	$('.prc-default-options').hide();
	$('.prc-selector').change(
		function(){
			var options = $(this).parent().find('.prc-default-options');

			if( $(this).val()==$(this).data('prc') ){
				options.hide();
			}else{
				options.show();
			}
		}
	);
});

function prcValidForm(form){
	var name = form.name.value;

	if( name ){
		return true;
	}

	alert(priceCategoriesAlertChampNom);

	form.name.focus();

	return false;
}

function prcConfirmDelList(){
	return window.confirm(priceWatchingConfirmSuppressionCategories);
}

function prdConfirmDelList(){
	return window.confirm(priceWatchingConfirmSuppressionCategorie);
}

function parent_select_prd( id, name, ref ){
	$('#ref').val( ref );
	hidePopup();
	return false;
}