<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd">
  <imports>
    <import resource="services2.xml" />
    <import resource="services3.xml" />
    <import resource="../ini/parameters.ini" />
    <import resource="../ini/parameters2.ini" />
    <import resource="../yaml/services13.yml" />
    <import resource="../yaml/yaml_with_wrong_ext.ini" type="yaml"/>
  </imports>
</container>
