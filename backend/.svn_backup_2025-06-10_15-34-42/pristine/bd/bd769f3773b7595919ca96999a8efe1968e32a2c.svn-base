<?php

	/**	\file upload-img.php
	 *	Cette page est appelée en Ajax et permet l'upload d'un fichier image destiné à être attaché à un produit.
	 *	Les paramètres suivants sont obligatoires :
	 *	- prd : identifiant du produit
	 *	- img : fichier image (champ input file)
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_EDIT');

$json = array(); 

if( !isset( $_POST['prd'] ) || !is_numeric( $_POST['prd'] ) ){
	$json['error']  = _('Paramètres manquants');
}else{

	// permet l'upload d'une image sur un produit
	if( isset($_FILES['img']) && $_FILES['img']['error']!=UPLOAD_ERR_NO_FILE ){
		if( !($id=prd_images_upload( $_POST['prd'], 'img' )) ){
			$json['error'] = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'image secondaire.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
		}
		else{
			$size = $config['img_sizes']['medium'];
			$json['html'] = '<img src="'.$config['img_url'].'/'.$size['dir'].'/'.$id.'.jpg" width="'.$size['width'].'" height="'.$size['height'].'" />';
		}
	}	

}

if( isset($_POST['cpt']) ) $json['cpt'] = $_POST['cpt'];

print json_encode( $json );