<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AC' => 'Ascension ƒudomekpo nutome',
  'AD' => 'Andorra nutome',
  'AE' => 'United Arab Emirates nutome',
  'AF' => 'Afghanistan nutome',
  'AG' => '́Antigua kple Barbuda nutome',
  'AI' => 'Anguilla nutome',
  'AL' => 'Albania nutome',
  'AM' => 'Armenia nutome',
  'AO' => 'Angola nutome',
  'AQ' => 'Antartica nutome',
  'AR' => 'Argentina nutome',
  'AS' => 'Amerika Samoa nutome',
  'AT' => 'Austria nutome',
  'AU' => 'Australia nutome',
  'AW' => 'Aruba nutome',
  'AX' => 'Åland ƒudomekpo nutome',
  'AZ' => 'Azerbaijan nutome',
  'BA' => 'Bosnia kple Herzergovina nutome',
  'BB' => 'Barbados nutome',
  'BD' => 'Bangladesh nutome',
  'BE' => 'Belgium nutome',
  'BF' => 'Burkina Faso nutome',
  'BG' => 'Bulgaria nutome',
  'BH' => 'Bahrain nutome',
  'BI' => 'Burundi nutome',
  'BJ' => 'Benin nutome',
  'BL' => 'Saint Barthélemy nutome',
  'BM' => 'Bermuda nutome',
  'BN' => 'Brunei nutome',
  'BO' => 'Bolivia nutome',
  'BR' => 'Brazil nutome',
  'BS' => 'Bahamas nutome',
  'BT' => 'Bhutan nutome',
  'BW' => 'Botswana nutome',
  'BY' => 'Belarus nutome',
  'BZ' => 'Belize nutome',
  'CA' => 'Canada nutome',
  'CC' => 'Kokos (Kiling) fudomekpo nutome',
  'CD' => 'Kongo Kinshasa nutome',
  'CF' => 'Titina Afrika repɔblik nutome',
  'CG' => 'Kongo Brazzaville nutome',
  'CH' => 'Switzerland nutome',
  'CI' => 'Kote d’Ivoire nutome',
  'CK' => 'Kook ƒudomekpo nutome',
  'CL' => 'Tsile nutome',
  'CM' => 'Kamerun nutome',
  'CN' => 'Tsaina nutome',
  'CO' => 'Kolombia nutome',
  'CR' => 'Kosta Rika nutome',
  'CU' => 'Kuba nutome',
  'CV' => 'Kape Verde nutome',
  'CX' => 'Kristmas ƒudomekpo nutome',
  'CY' => 'Saiprus nutome',
  'CZ' => 'Tsɛk repɔblik nutome',
  'DE' => 'Germania nutome',
  'DG' => 'Diego Garsia nutome',
  'DJ' => 'Dzibuti nutome',
  'DK' => 'Denmark nutome',
  'DM' => 'Dominika nutome',
  'DO' => 'Dominika repɔblik nutome',
  'DZ' => 'Algeria nutome',
  'EA' => 'Keuta and Melilla nutome',
  'EC' => 'Ekuadɔ nutome',
  'EE' => 'Estonia nutome',
  'EG' => 'Egypte nutome',
  'EH' => 'Ɣetoɖoƒe Sahara nutome',
  'ER' => 'Eritrea nutome',
  'ES' => 'Spain nutome',
  'ET' => 'Etiopia nutome',
  'FI' => 'Finland nutome',
  'FJ' => 'Fidzi nutome',
  'FK' => 'Falkland ƒudomekpowo nutome',
  'FM' => 'Mikronesia nutome',
  'FO' => 'Faroe ƒudomekpowo nutome',
  'FR' => 'France nutome',
  'GA' => 'Gabɔn nutome',
  'GB' => 'United Kingdom nutome',
  'GD' => 'Grenada nutome',
  'GE' => 'Georgia nutome',
  'GF' => 'Frentsi Gayana nutome',
  'GG' => 'Guernse nutome',
  'GH' => 'Ghana nutome',
  'GI' => 'Gibraltar nutome',
  'GL' => 'Grinland nutome',
  'GM' => 'Gambia nutome',
  'GN' => 'Guini nutome',
  'GP' => 'Guadelupe nutome',
  'GQ' => 'Ekuatorial Guini nutome',
  'GR' => 'Greece nutome',
  'GS' => 'Anyiehe Georgia kple Anyiehe Sandwich ƒudomekpowo nutome',
  'GT' => 'Guatemala nutome',
  'GU' => 'Guam nutome',
  'GW' => 'Gini-Bisao nutome',
  'GY' => 'Guyanadu',
  'HK' => 'Hɔng Kɔng SAR Tsaina nutome',
  'HN' => 'Hondurasdu',
  'HR' => 'Kroatsia nutome',
  'HT' => 'Haiti nutome',
  'HU' => 'Hungari nutome',
  'IC' => 'Kanari ƒudomekpowo nutome',
  'ID' => 'Indonesia nutome',
  'IE' => 'Ireland nutome',
  'IL' => 'Israel nutome',
  'IM' => 'Aisle of Man nutome',
  'IN' => 'India nutome',
  'IO' => 'Britaintɔwo ƒe india ƒudome nutome',
  'IQ' => 'iraqdukɔ',
  'IR' => 'Iran nutome',
  'IS' => 'Aiseland nutome',
  'IT' => 'Italia nutome',
  'JE' => 'Dzɛse nutome',
  'JM' => 'Dzamaika nutome',
  'JO' => 'Yordan nutome',
  'JP' => 'Dzapan nutome',
  'KE' => 'Kenya nutome',
  'KG' => 'Kirgizstan nutome',
  'KH' => 'Kambodia nutome',
  'KI' => 'Kiribati nutome',
  'KM' => 'Komoros nutome',
  'KN' => 'Saint Kitis kple Nevis nutome',
  'KP' => 'Dziehe Korea nutome',
  'KR' => 'Anyiehe Korea nutome',
  'KW' => 'Kuwait nutome',
  'KY' => 'Kayman ƒudomekpowo nutome',
  'KZ' => 'Kazakstan nutome',
  'LA' => 'Laos nutome',
  'LB' => 'Lebanɔn nutome',
  'LC' => 'Saint Lusia nutome',
  'LI' => 'Litsenstein nutome',
  'LK' => 'Sri Lanka nutome',
  'LR' => 'Liberia nutome',
  'LS' => 'Lɛsoto nutome',
  'LT' => 'Lituania nutome',
  'LU' => 'Lazembɔg nutome',
  'LV' => 'Latvia nutome',
  'LY' => 'Libya nutome',
  'MA' => 'Moroko nutome',
  'MC' => 'Monako nutome',
  'MD' => 'Moldova nutome',
  'ME' => 'Montenegro nutome',
  'MF' => 'Saint Martin nutome',
  'MG' => 'Madagaska nutome',
  'MH' => 'Marshal ƒudomekpowo nutome',
  'MK' => 'Makedonia nutome',
  'ML' => 'Mali nutome',
  'MM' => 'Myanmar (Burma) nutome',
  'MN' => 'Mongolia nutome',
  'MO' => 'Macau SAR Tsaina nutome',
  'MP' => 'Dziehe Marina ƒudomekpowo nutome',
  'MQ' => 'Martiniki nutome',
  'MR' => 'Mauritania nutome',
  'MS' => 'Montserrat nutome',
  'MT' => 'Malta nutome',
  'MU' => 'mauritiusdukɔ',
  'MV' => 'maldivesdukɔ',
  'MW' => 'Malawi nutome',
  'MX' => 'Mexico nutome',
  'MY' => 'Malaysia nutome',
  'MZ' => 'Mozambiki nutome',
  'NA' => 'Namibia nutome',
  'NC' => 'New Kaledonia nutome',
  'NE' => 'Niger nutome',
  'NF' => 'Norfolk ƒudomekpo nutome',
  'NG' => 'Nigeria nutome',
  'NI' => 'Nicaraguadukɔ',
  'NL' => 'Netherlands nutome',
  'NO' => 'Norway nutome',
  'NP' => 'Nepal nutome',
  'NR' => 'Nauru nutome',
  'NU' => 'Niue nutome',
  'NZ' => 'New Zealand nutome',
  'OM' => 'Oman nutome',
  'PA' => 'Panama nutome',
  'PE' => 'Peru nutome',
  'PF' => 'Frentsi Pɔlinesia nutome',
  'PG' => 'Papua New Gini nutome',
  'PH' => 'Filipini nutome',
  'PK' => 'Pakistan nutome',
  'PL' => 'Poland nutome',
  'PM' => 'Saint Pierre kple Mikelɔn nutome',
  'PN' => 'Pitkairn ƒudomekpo nutome',
  'PR' => 'Puerto Riko nutome',
  'PS' => 'Palestinia nutome',
  'PT' => 'Portugal nutome',
  'PW' => 'Palau nutome',
  'PY' => 'Paragua nutome',
  'QA' => 'Katar nutome',
  'RE' => 'Réunion nutome',
  'RO' => 'Romania nutome',
  'RU' => 'Russia nutome',
  'RW' => 'Rwanda nutome',
  'SA' => 'Saudi Arabia nutome',
  'SB' => 'Solomon ƒudomekpowo nutome',
  'SC' => 'Seshɛls nutome',
  'SD' => 'Sudan nutome',
  'SE' => 'Sweden nutome',
  'SG' => 'Singapɔr nutome',
  'SH' => 'Saint Helena nutome',
  'SI' => 'Slovenia nutome',
  'SJ' => 'Svalbard kple Yan Mayen nutome',
  'SK' => 'Slovakia nutome',
  'SL' => 'Sierra Leone nutome',
  'SM' => 'San Marino nutome',
  'SN' => 'Senegal nutome',
  'SO' => 'Somalia nutome',
  'SR' => 'Suriname nutome',
  'ST' => 'São Tomé kple Príncipe nutome',
  'SV' => 'El Salvadɔ nutome',
  'SY' => 'Siria nutome',
  'SZ' => 'Swaziland nutome',
  'TA' => 'Tristan da Kunha nutome',
  'TC' => 'Tɛks kple Kaikos ƒudomekpowo nutome',
  'TD' => 'Tsad nutome',
  'TF' => 'Anyiehe Franseme nutome',
  'TG' => 'Togo nutome',
  'TH' => 'Thailand nutome',
  'TJ' => 'Tajikistan nutome',
  'TK' => 'Tokelau nutome',
  'TL' => 'Timor-Leste nutome',
  'TM' => 'Tɛkmenistan nutome',
  'TN' => 'Tunisia nutome',
  'TO' => 'Tonga nutome',
  'TR' => 'Tɛki nutome',
  'TT' => 'Trinidad kple Tobago nutome',
  'TV' => 'Tuvalu nutome',
  'TW' => 'Taiwan nutome',
  'TZ' => 'Tanzania nutome',
  'UA' => 'Ukraine nutome',
  'UG' => 'Uganda nutome',
  'UM' => 'U.S. Minor Outlaying ƒudomekpowo nutome',
  'US' => 'USA nutome',
  'UY' => 'uruguaydukɔ',
  'UZ' => 'Uzbekistan nutome',
  'VA' => 'Vatikandu nutome',
  'VC' => 'Saint Vincent kple Grenadine nutome',
  'VE' => 'Venezuela nutome',
  'VG' => 'Britaintɔwo ƒe Virgin ƒudomekpowo nutome',
  'VI' => 'U.S. Vɛrgin ƒudomekpowo nutome',
  'VN' => 'Vietnam nutome',
  'VU' => 'Vanuatu nutome',
  'WF' => 'Wallis kple Futuna nutome',
  'WS' => 'Samoa nutome',
  'YE' => 'Yemen nutome',
  'YT' => 'Mayotte nutome',
  'ZA' => 'Anyiehe Africa nutome',
  'ZM' => 'Zambia nutome',
  'ZW' => 'Zimbabwe nutome',
);
