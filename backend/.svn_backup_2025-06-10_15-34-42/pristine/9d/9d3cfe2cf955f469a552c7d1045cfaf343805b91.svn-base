<?php

require_once('orders.inc.php');
require_once('Services/Customer/Customer.class.php');

/**	\brief Cette classe permet de réaliser les actions liés à l'espace "Mon Compte".
 * 	Toutes les actions lèveront des exceptions avec un message personnalisé et un code unique.
 * 	Les codes suivants sont réservés (et doivent être utilisé dans le cas de nouvelle action) :
 * 			- 97 : Le compte client donné n'existe pas
 * 			- 98 : Aucun client n'est connecté
 * 			- 99 : Une erreur inattendue (quelque soit l'action)
 */
class RightActions {
	/** Cette fonction permet de créer un nouveau utilisateur
	 * @param array $data Obligatoire, information sur le client
	 *        - firstname : obligatoire pour les particuliers, prénom
	 *        - lastname : obligatoire pour les particuliers, nom de famille
	 *        - address1 : obligatoire, adresse postal de facturation
	 *        - address2 : Optionnel, complément d'adresse de facturation
	 *        - zipcode : Obligatoire, code postal de l'adresse de facturation
	 *        - city : Obligatoire, ville de l'adresse de facturation
	 *        - country : Obligatoire, pays de facturation sous ce format (CODE_NOMDUPAYS, exemple FR_FRANCE)
	 *        - email : Obligatoire, adresse mail du client
	 * 		    - password : Obligatoire, mot de passe du compte
	 *        - phone : Optionnel, numéro de téléphone fixe (fournir au moins 1 numéro de téléphone)
	 *        - mobile : Optionnel, numéro de téléphone mobile (fournir au moins 1 numéro de téléphone)
	 * @param	bool|array		$add_required Optionnel, Tableau des champs optionnels à vérifier (voir liste des champs optionnels sur le paramètre $data)
	 * @return	int|Exception	Une exception sera levée en cas d'erreur, l'identifiant du compte en cas de succès
	 */
	public static function createUser( $data, $add_required=false ){
		global $config;

		// Champs requis
		$fields_required = [
			'firstname',
      'lastname',
			'address1',
			'zipcode',
			'city',
			'country',
			'email',
			'password'
		];

		if( !is_array($data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1 );
		}

		// Ajout des champs requis
		if( is_array($add_required) ){
			$fields_required = array_unique( array_merge($fields_required, $add_required) );
		}

		$type = 4;
		$prf = PRF_USER;

		if( !isset($data['society']) ){
			$data['society'] = '';
		}

		foreach($fields_required as $field){
			if( !isset($data[$field]) ){
				throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1 );
			}
		}

		// Vérification du genre
		if( in_array('gender', $fields_required) ){
			if( !gu_titles_exists($data['gender']) ){
				throw new Exception(i18n::get('L\'information de civilité n\'est pas correct.', 'ERROR'), 2 );
			}
		}else{
			$data['gender'] = ria_array_get( $data, 'gender', '');
		}

		// Vérification du prénom
		if( in_array('firstname', $fields_required) ){
			if( !trim($data['firstname']) ){
				throw new Exception(i18n::get('Veuillez rentrer votre prénom.', 'ERROR'), 3 );
			}
		}else{
			$data['firstname'] = ria_array_get( $data, 'firstname', '');
		}

		// Vérification du nom
		if( in_array('lastname', $fields_required) ){
			if( !trim($data['lastname']) ){
				throw new Exception(i18n::get('Veuillez rentrer votre nom de famille.', 'ERROR'), 4 );
			}
		}else{
			$data['lastname'] = ria_array_get( $data, 'lastname', '');
		}

		// Vérification du n° de téléphone
		if( in_array('phone', $fields_required) ){
			if( !isphone($data['phone']) ){
				throw new Exception( i18n::get('Le numéro de téléphone ne semble pas valide.', 'ERROR'), 7 );
			}
		}else{
			$data['phone'] = ria_array_get( $data, 'phone', '');
		}

		// Vérification du n° de téléphone mobile
		if( in_array('mobile', $fields_required) ){
			if( !isphone($data['mobile']) || !in_array(substr($data['mobile'], 0, 2), ['06', '07']) ){
				throw new Exception( i18n::get('Le numéro de téléphone mobile ne semble pas valide.', 'ERROR'), 8 );
			}
		}else{
			$data['mobile'] = ria_array_get( $data, 'mobile', '');
		}

		if( !trim($data['address1']) || !trim($data['zipcode']) || !trim($data['city']) || !trim($data['country']) || !trim($data['email']) || !gu_valid_email($data['email']) ){
			throw new Exception(i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.', 'ERROR'), 9 );
		}

		if( !gu_users_check_email($data['email']) ){
			throw new Exception( i18n::get('Cette adresse mail est déjà utilisée.', 'ERROR'), 10 );
		}

		if( !gu_valid_password($data['password']) ){
			throw new Exception( i18n::get('Le mot de passe que vous avez choisi ne satisfait pas les conditions requises.', 'ERROR'), 13 );
		}

		if( !iszipcode($data['zipcode']) ){
			throw new Exception( i18n::get('Le code postal saisi ne semble pas correct.', 'ERROR'), 11 );
		}

		if( !($usr = gu_users_add($data['email'], $data['password'], $prf)) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
		}
		$res_country = explode("_", $data['country']);
		$data['address2'] = !isset($data['address2']) ? '' : $data['address2'];

		if( !($adr = gu_adresses_add($usr, $type, $data['gender'], ucwords($data['firstname']), ucwords($data['lastname']), $data['society'], '', $data['address1'], $data['address2'], $data['zipcode'], $data['city'], $res_country[1], $data['phone'], '', $data['mobile'], '', '', $data['email'], $res_country[0])) ){
			gu_users_del($usr);
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
		}

		if( !gu_users_address_set($usr, $adr) ){
			gu_users_del($usr);
			gu_adresses_del($adr);
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
		}

    // Définie le lien avec le compte parent => celui qui est connecté
		// (ou bien son parent s'il s'agit d'un compte enfant ayant le droit de gérer les utilisateur)
    $user = CustomerService::getInstance();
		$parent_id = gu_users_get_parent_id( $user->getID() );

    if( !gu_users_set_parent_id($usr, ($parent_id ? $parent_id : $user->getID())) ){
			gu_adresses_del($adr);
			gu_users_del($usr);
      throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
    }

		return $usr;
	}

	/** Cette fonction permet de mettre à jour un utilisateur
	 * @param int $account_id Obligatoire, identifiant du compte utilisateur
	 * @param array $data Obligatoire, information sur le client
	 *        - firstname : obligatoire pour les particuliers, prénom
	 *        - lastname : obligatoire pour les particuliers, nom de famille
	 *        - address1 : obligatoire, adresse postal de facturation
	 *        - address2 : Optionnel, complément d'adresse de facturation
	 *        - zipcode : Obligatoire, code postal de l'adresse de facturation
	 *        - city : Obligatoire, ville de l'adresse de facturation
	 *        - country : Obligatoire, pays de facturation sous ce format (CODE_NOMDUPAYS, exemple FR_FRANCE)
	 *        - email : Obligatoire, adresse mail du client
	 * 		    - password : Obligatoire, mot de passe du compte
	 *        - phone : Optionnel, numéro de téléphone fixe (fournir au moins 1 numéro de téléphone)
	 *        - mobile : Optionnel, numéro de téléphone mobile (fournir au moins 1 numéro de téléphone)
	 * @param	bool|array		$add_required Optionnel, Tableau des champs optionnels à vérifier (voir liste des champs optionnels sur le paramètre $data)
	 * @return	true|Exception	Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function editUser( $account_id, $data, $add_required=false ){
		global $config;

		if( !gu_users_exists($account_id) ){
			throw new Exception( i18n::get('L\'utilisateur n\'a pas été identifié.'), 97 );
		}

		// Champs requis
		$fields_required = [
			'firstname',
      'lastname',
			'address1',
			'zipcode',
			'city',
			'country',
			'email',
		];

		if( !is_array($data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1 );
		}

		// Ajout des champs requis
		if( is_array($add_required) ){
			$fields_required = array_unique( array_merge($fields_required, $add_required) );
		}

		$type = 4;
		if( !isset($data['password']) ){
			$data['password'] = ''; // pas de mise à jour du mot de passe
		}

		foreach($fields_required as $field){
			if( !isset($data[$field]) ){
				throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1 );
			}
		}

		// Vérification du genre
		if( in_array('gender', $fields_required) ){
			if( !gu_titles_exists($data['gender']) ){
				throw new Exception(i18n::get('L\'information de civilité n\'est pas correct.', 'ERROR'), 2 );
			}
		}else{
			$data['gender'] = ria_array_get( $data, 'gender', '');
		}

		// Vérification du prénom
		if( in_array('firstname', $fields_required) ){
			if( !trim($data['firstname']) ){
				throw new Exception(i18n::get('Veuillez rentrer votre prénom.', 'ERROR'), 3 );
			}
		}else{
			$data['firstname'] = ria_array_get( $data, 'firstname', '');
		}

		// Vérification du nom
		if( in_array('lastname', $fields_required) ){
			if( !trim($data['lastname']) ){
				throw new Exception(i18n::get('Veuillez rentrer votre nom de famille.', 'ERROR'), 4 );
			}
		}else{
			$data['lastname'] = ria_array_get( $data, 'lastname', '');
		}

		// Vérification du n° de téléphone
		if( in_array('phone', $fields_required) ){
			if( !isphone($data['phone']) ){
				throw new Exception( i18n::get('Le numéro de téléphone ne semble pas valide.', 'ERROR'), 7 );
			}
		}else{
			$data['phone'] = ria_array_get( $data, 'phone', '');
		}

		// Vérification du n° de téléphone mobile
		if( in_array('mobile', $fields_required) ){
			if( !isphone($data['mobile']) || !in_array(substr($data['mobile'], 0, 2), ['06', '07']) ){
				throw new Exception( i18n::get('Le numéro de téléphone mobile ne semble pas valide.', 'ERROR'), 8 );
			}
		}else{
			$data['mobile'] = ria_array_get( $data, 'mobile', '');
		}

		if( !trim($data['address1']) || !trim($data['zipcode']) || !trim($data['city']) || !trim($data['country']) || !trim($data['email']) || !gu_valid_email($data['email']) ){
			throw new Exception(i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.', 'ERROR'), 9 );
		}

		if( !gu_users_check_email($data['email'], $account_id) ){
			throw new Exception( i18n::get('Cette adresse mail est déjà utilisée.', 'ERROR'), 10 );
		}

		if( trim($data['password']) != '' && !gu_valid_password($data['password']) ){
			throw new Exception( i18n::get('Le mot de passe que vous avez choisi ne satisfait pas les conditions requises.', 'ERROR'), 13 );
		}

		if( !iszipcode($data['zipcode']) ){
			throw new Exception( i18n::get('Le code postal saisi ne semble pas correct.', 'ERROR'), 11 );
		}

		$res_country = explode("_", $data['country']);
		$data['address2'] = !isset($data['address2']) ? '' : $data['address2'];

		$adr_id = gu_users_get_adr_invoices( $account_id );
		if( !is_numeric($adr_id) || $adr_id <= 0 ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour de l\'utilisateur. Veuillez réessayer.', 'ERROR'), 16 );
		}

		// Mise à jour des informations personnelles
		if( !gu_adresses_update( $account_id, $adr_id, $type, $data['gender'], ucwords($data['firstname']), ucwords($data['lastname']), '', '', $data['address1'], $data['address2'], $data['zipcode'], $data['city'], $res_country[1], $data['phone'], '', $data['mobile'], null, false, null, $data['email'], $res_country[0]) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour de l\'utilisateur. Veuillez réessayer.', 'ERROR'), 16 );
		}

		// Mise à jour du mot de passe (si demande donc si $data['password'] n\'est pas vide et valide)
		if( trim($data['password']) != '' ){
			if( !gu_users_update_password($account_id, $data['password']) ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour du mot de passe. Veuillez réessayer.', 'ERROR'), 16 );
			}
		}

		return true;
	}

  /** Cette fonction permet de mettre à jour les droits d'un utilisateur.
   *  @param int $usr_id Obligatoire, identifiant de l'utilisateur
   *  @param array $rights Obligatoire, tableau des droits actives (peut-être vide)
   *  @return	bool|Exception	Une exception sera levée en cas d'erreur, true en cas de succès
   */
  public static function updateRights( $usr_id, $rights ){
    if( !is_numeric($usr_id) || $usr_id <= 0 ){
      throw new Exception( i18n::get('L\'utilisateur n\'est pas identifié.', 'ERROR'), 1);
    }

    // Suppression de tous les droits
    if( !gu_users_rights_del($usr_id) ){
      throw new Exception( __LINE__.i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour des droits.', 'ERROR'), 2 );
    }

    $ar_rights = [];
    foreach( $rights as $one_right ){
      $ar_rights[ $one_right ] = 1;
    }

    if( count($ar_rights) ){
      if( !gu_users_rights_add($usr_id, $ar_rights) ){
        throw new Exception( __LINE__.i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour des droits.', 'ERROR'), 2 );
      }
    }

    return true;
  }

	/** Cette fonction permet de supprimer un utilisateur.
	 * 	@param int $usr_id Obligatoire, identifiant d'un utilisateur
	 * 	@return bool|Exception Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function deleteUser( $usr_id ){
		if( !is_numeric($usr_id) || $usr_id <= 0 ){
      throw new Exception( i18n::get('L\'utilisateur n\'est pas identifié.', 'ERROR'), 1);
    }

		// La suppression d'un compte ne peut être fait que par un responsable
		$user = CustomerService::getInstance();
		$parent_id = gu_users_get_parent_id( $usr_id );

		if( $user->getID() != $parent_id ){
			throw new Exception( i18n::get('Vous n\'êtes pas authorisé à supprimer ce compte.', 17) );
		}

		if( !gu_users_del($usr_id) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la suppression de l\'utilisateur.', 99) );
		}

		return true;
	}
}