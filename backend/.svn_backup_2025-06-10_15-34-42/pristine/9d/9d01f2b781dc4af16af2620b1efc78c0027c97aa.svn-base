<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd">
  <parameters>
    <parameter key="foo">foo</parameter>
    <parameter key="values" type="collection">
      <parameter>true</parameter>
      <parameter>false</parameter>
    </parameter>
  </parameters>
</container>
