<?xml version="1.0" encoding="utf-8"?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd">
  <services>
    <service id="foo" class="stdClass">
      <argument type="service">
        <service class="Symfony\Component\DependencyInjection\Tests\Compiler\D"/>
      </argument>
    </service>
    <service id="autowired" class="Symfony\Component\DependencyInjection\Tests\Compiler\E" autowire="true"/>
  </services>
</container>
