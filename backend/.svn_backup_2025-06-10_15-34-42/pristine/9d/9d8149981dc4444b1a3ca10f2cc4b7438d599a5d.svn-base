<?php

/**	\defgroup model_fields_restricted_values Valeurs possibles pour une liste de choix
 * 	\ingroup model_fields
 *	Ce module comprend les fonctions nécessaires à la gestion des listes de choix
 *	@{
 */

 /// \cond onlyria
/**	Cette fonction permet l'ajout d'une valeur de restriction pour les listes de choix.
 *	Pour des questions de stockage, le caractère "," n'est pas accepté dans les valeurs (sauf pour le type hiérarchique)
 *	@param int $fld Obligatoire, identifiant du champ auquel la valeur est ajoutée
 *	@param string $name Obligatoire, désignation de la valeur (les valeurs vides sont refusées)
 *	@param int $val_parent_id Facultatif, Identifiant d'une valeur de restriction déjà existante servant de parent. Les 2 valeurs de restriction doivent être associée au même champ.
 *
 *	@return int l'identifiant de la valeur en cas de succès
 *	@return bool false en cas d'échec
 */
function fld_restricted_values_add( $fld, $name, $val_parent_id=false ){
	global $config;

	if( !is_numeric($fld) ) return false;

	if( trim($name)==='' ) return false;

	// Les virgules sont autorisées pour le type hiérarchique seulement, puisque ce sont les ID qui seront stockés
	if( fld_fields_get_type($fld)!=FLD_TYPE_SELECT_HIERARCHY ){
		$val_parent_id = false;
		if( strpos($name,',')!==false ) return false;
	}

	$name = ucfirst(trim($name));

	$fields = array();
	$values = array();

	$fields[] = 'val_tnt_id'; $values[] = $config['tnt_id'];
	$fields[] = 'val_fld_id'; $values[] = $fld;
	$fields[] = 'val_name'; $values[] = '\''.addslashes($name).'\'';
	$fields[] = 'val_alias'; $values[] = '\''.addslashes(urlalias($name)).'\'';

	if( $val_parent_id!=false ){
		if( !is_numeric($val_parent_id) ) return false;
		if( !fld_restricted_values_exists($val_parent_id) ) return false;

		$rparent = fld_restricted_values_get( $val_parent_id );

		if( $rparent==false || !ria_mysql_num_rows($rparent) ) return false;
		$parent = ria_mysql_fetch_array($rparent);

		// L'identifiant du champ de la valeur parent doit correspondre au parent du champ de la valeur enfant
		if( $parent['fld_id']!=$fld ) return false;

		$fields[] = 'val_parent_id';
		$values[] = $val_parent_id;
	}

	$res = ria_mysql_query('
		insert into fld_restricted_values
			('.implode( ',',$fields ).')
		values
			('.implode( ',',$values ).')
	');

	if( $res ){
		$id = ria_mysql_insert_id();

		// met à jour la position des champs
		fld_restricted_values_order_update( $fld, fld_restricted_values_order_get( $fld ) );

		// force la mise à jour des configs sur les applications
		dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

		return $id;
	}else{
		return false;
	}
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet la vérification d'un identifiant de valeur de liste de choix.
 *	@param int $id Obligatoire, identifiant de la valeur
 *	@return bool true si l'identifiant est valide
 *	@return bool false si l'identifiant est invalide
 */
function fld_restricted_values_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select val_id from fld_restricted_values where val_tnt_id='.$config['tnt_id'].' and val_id='.$id))==1;
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet la mise à jour d'une valeur de restriction pour les listes de choix.
 *	@param int $id Obligatoire, identifiant de la valeur
 *	@param string $name Obligatoire, désignation de la valeur (les valeurs vides sont refusées)
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_restricted_values_update( $id, $name ){
	global $config;

	$rval = fld_restricted_values_get($id);
	if( $rval==false || !ria_mysql_num_rows($rval) ) return false;
	$val = ria_mysql_fetch_array( $rval );

	if( !$val['tenant'] ) return false;

	if( trim($name)==='' ) return false;

	// les virgules sont autorisees pour le type hiérarchique, puisque ce sont les ID qui seront stockés
	if( fld_fields_get_type($val['fld_id'])!=FLD_TYPE_SELECT_HIERARCHY ){
		if( strpos($name,',')!==false ) return false;
	}

	$name = ucfirst(trim($name));

	$res = ria_mysql_query('
		update fld_restricted_values set
			val_name=\''.addslashes($name).'\'
		where val_tnt_id='.$config['tnt_id'].' and val_id='.$id.'
	');

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
/// \endcond

/**	Cette fonction permet le chargement d'une ou plusieurs valeurs de listes de choix.
 *	Le résultat peut être filtré en fonction du contenu des paramètres optionnels fournis.
 *	@param int $id Facultatif, identifiant d'une valeur sur laquelle filtrer le résultat
 *	@param int $fld Facultatif, identifiant d'un champ sur lequel filtrer le résultat
 *	@param string $alias Facultatif, alias du libellé sur lequel filtrer le résultat
 *	@param int $parent_id Facultatif, Identifiant de la valeur parent sur laquelle filtrer le résultat (-1 pour racine uniquement)
 *	@param string $name Facultatif, nom sur lequel filtrer le résultat
 *	@param string $lng Facultatif, langue sur laquelle filtrer le résultat
 *	@param bool $sort_by_parent Facultatif, détermine l'ordre de tri : si True, les valeurs parentes sont prioritaires (impacte uniquement les valeurs hiérarchisées)
 *	@param int $val_restrict Facultatif, identifiant d'une valeur de restriction (ou tableau de valeurs) permettant un filtrage selon les groupes de contraintes existants (n'est pas pris en compte si $id est spécifié). Ce n'est pas obligatoire, mais ce paramètre devrait toujours être combiné avec $fld
 *	@param bool $restrict_inline Facultatif, si activé et $val_restrict spécifié, le filtrage n'est pas effectué mais une colonne "restric" est ajouté au résultat, indiquant si la valeur est possible ou non
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- tenant : identifiant du locataire
 *			- id : identifiant de la valeur
 *			- name : désignation de la valeur
 *			- fld_id : identifiant du champ de rattachement
 *			- alias : alias de la valeur (suppression des accents par exemple)
 *			- parent : identifiant de la valeur parent
 *			- pos : Position de tri personnalisé
 *			- parent_pos : Position de tri personnalisé du parent (si $sort_by_parent spécifié)
 *			- in_restrict : uniquement si $val_restrict et $restrict_inline, détermine si la valeur est incluse via le moteur de restrictions
 */
function fld_restricted_values_get( $id=0, $fld=0, $alias='', $parent_id=0, $name='', $lng=false, $sort_by_parent=false, $val_restrict=false, $restrict_inline=false ){
	global $config;

	if( !is_numeric($id) || $id<0 ) return false;
	if( !is_numeric($parent_id) || $parent_id<-1 ) return false;
	if( $fld!=0 && !fld_fields_exists($fld) ) return false;
	if( $lng!==false && !in_array(strtolower($lng), $config['i18n_lng_used']) ) return false;

	{ // restrictions
		if( $val_restrict!==false && !$id ){
			if( is_array($val_restrict) ){
				foreach( $val_restrict as $vr ){
					if( !is_numeric($vr) || $vr<=0 ) return false;
				}
			}else{
				if( !is_numeric($val_restrict) || $val_restrict<=0 ) return false;
				$val_restrict = array($val_restrict);
			}
		}else{
			$val_restrict = array();
		}

		$allow_id = array();
		if( sizeof($val_restrict) ){
			// on charge tous les groupes concernés par la ou les valeurs spécifiées
			$groups = fld_restricted_values_constraints_get_groups( $val_restrict );
			if( !is_array($groups) ) return false;
			foreach( $groups as $g ){
				// on charge toutes les valeurs de tous les groupes (pour le $fld spécifié le cas échéant)
				$rval = fld_restricted_values_constraints_get( $g, 0, $fld );
				if( !$rval ) return false;
				if( $fld && !ria_mysql_num_rows($rval) ){
					// pour ce groupe de contraintes, il n'y a pas de valeurs qui concerne ce champ (UNIQUMENT si $fld spécifié)
					// dans ce cas, allow_id autorise tout (il faut stopper la boucle)
					$allow_id = array();
					break;
				}else{
					while( $rv = ria_mysql_fetch_array($rval) ){
						// la valeur n'est pas encore dans le tableau, et n'est pas dans le critère d'origine
						if( !in_array($rv['val_id'], $allow_id) && !in_array($rv['val_id'], $val_restrict) )
							$allow_id[] = $rv['val_id'];
					}
				}
			}
		}
	}

	$sql_tenant = '( v.val_tnt_id=0 or v.val_tnt_id='.$config['tnt_id'].' )';

	$sql = '
		select
			v.val_tnt_id as tenant, v.val_id as id, '.( !$lng ? 'v.val_name as name' : 'ifnull(vlg_name,v.val_name) as name').',
			v.val_fld_id as fld_id, v.val_alias as alias, v.val_parent_id as parent, v.val_pos as pos
	';
	if( $restrict_inline ){
		$sql .= ', '.( !sizeof($allow_id) ? '1' : 'v.val_id in ('.implode(', ', $allow_id).')' ).' as in_restrict ';
	}

	if( $sort_by_parent ){
		$sql .= ', (select pv.val_pos from fld_restricted_values as pv where pv.val_tnt_id=v.val_tnt_id and pv.val_id=v.val_parent_id) as parent_pos ';
	}
	$sql .= '
		from
			fld_restricted_values as v
	';

	if( $lng!==false )
		$sql .= ' left join fld_restricted_values_translates on ( v.val_tnt_id=vlg_tnt_id and v.val_id=vlg_val_id and \''.strtolower($lng).'\'=vlg_lng_code )';

	$sql .= '
		where
			'.$sql_tenant.'
	';

	if( $id>0 ) $sql .= ' and v.val_id='.$id;
	if( $fld>0 ) $sql .= ' and v.val_fld_id='.$fld;
	if( $parent_id>0 ) $sql .= ' and v.val_parent_id='.$parent_id;
	elseif( $parent_id==-1 ) $sql .= ' and v.val_parent_id is null';
	if( trim($alias) ) $sql .= ' and v.val_alias=\''.addslashes($alias).'\'';
	if( trim($name) ) $sql .= ' and v.val_name=\''.addslashes($name).'\'';
	if( sizeof($allow_id) && !$restrict_inline ) $sql .= ' and v.val_id in ('.implode(', ', $allow_id).')';

	$sql .= ' order by ';
	if( $sort_by_parent ){
		$sql .= 'ifnull((select pv.val_pos from fld_restricted_values as pv where pv.val_tnt_id=v.val_tnt_id and pv.val_id=v.val_parent_id), -1) asc, ';
	}
	$sql .= 'ifnull(v.val_pos, -1) asc, v.val_name asc';

	$r = ria_mysql_query($sql);

	if( !$r ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	return $r;
}

/**	Cette fonction permet le chargement d'un libellé de valeur de liste de choix,
 *	sans charger les autres informations sur la valeur.
 *	@param int $id Obligatoire, identifiant de la valeur à charger
 *	@return string|bool le libellé de la valeur en cas de succès, false en cas d'échec
 */
function fld_restricted_values_get_name( $id ){
	global $config;

	$res = fld_restricted_values_get( $id );
	if( $res===false || !ria_mysql_num_rows($res) ){ return false; }
	$r = ria_mysql_fetch_array($res);

	return $r['name'];
}

/**	Cette fonction permet le chargement d'un identifiant de valeur de liste de choix
 *	à partir du libellé de la valeur. La récupération par la traduction est gérée en standard
 *	@param int $fld Obligatoire, identifiant du champ auquel la valeur appartient
 *	@param string $name Obligatoire, désignation de la valeur
 *	@param string $lng Optionnel, langue de $name (attention, par défault i18n::getLang() et non pas $config['i18n_lng'])
 *	@return int l'identifiant de la valeur en cas de succès, false en cas d'échec
 */
function fld_restricted_values_get_id( $fld, $name, $lng=false ){
	global $config;

	if( !is_numeric($fld) || $fld<=0 ) return false;
	$name = trim($name);
	if( !$name && $name!=='0' ) return false;

	if( !in_array(strtolower(trim($lng)), $config['i18n_lng_used']) )
		$lng = i18n::getLang();

	if( $lng!=$config['i18n_lng'] && in_array($lng, $config['i18n_lng_used']) ){
		$res = fld_restricted_values_translates_get( 0, $lng, $name );
		if( $res===false || !ria_mysql_num_rows($res) ) return false;
		$r = ria_mysql_fetch_array($res);
	}else{
		$name = ucfirst($name);

		$res = fld_restricted_values_get( 0, $fld, '', 0, $name );
		if( $res===false || !ria_mysql_num_rows($res) ) return false;
		$r = ria_mysql_fetch_array($res);
	}

	return $r['id'];
}

/// \cond onlyria
/**	Cette fonction permet la suppression de valeur de liste de choix.
 *	Si la valeur est utilisée dans un ou plusieurs champs enfants, la suppression sera refusée.
 *	Il n'est pas possible de supprimer les valeurs d'un champ avancé générique (non spécifique à un locataire).
 *
 *	@param int $id Obligatoire, identifiant de la valeur à supprimer.
 *	@param bool $del_obj Optionnel, détermine si les relations avec les objets sont supprimées (activé par défaut), ou au contraire si la valeur ne doit pas être utilisée pour pouvoir être supprimée.
 *	@param string $child_action Optionnel, action à effectuer sur les valeurs enfants : restrict empêche la suppression, set_null les place à la racine, set_parent les place au niveau du parent de $id, force_del les supprime. Ce paramètre est insensible à la casse.
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function fld_restricted_values_del( $id, $del_obj = true, $child_action = 'restrict' ){

	$rval = fld_restricted_values_get( $id );
	if( !$rval || !ria_mysql_num_rows($rval) ){
		return false;
	}
	$val = ria_mysql_fetch_assoc($rval);

	// champ avancé générique
	if( !$val['tenant'] ){
		return false;
	}

	$child_action = strtolower(trim($child_action));

	if( !in_array($child_action, array('restrict', 'set_null', 'set_parent', 'force_del')) ){
		$child_action = 'restrict';
	}

	global $config;

	switch( $child_action ){
		case 'restrict': {
			// détecte la présence d'une hiérarchie descendante
			$rchilds = ria_mysql_query('
				select 1
				from fld_restricted_values
				where val_tnt_id = '.$config['tnt_id'].'
					and val_parent_id = '.$val['id'].'
			');
			if( $rchilds && ria_mysql_num_rows($rchilds) ){
				return false;
			}
			break;
		}
		case 'set_null':
		case 'set_parent': {
			// remplace le parent par le parent courant ou NULL
			ria_mysql_query('
				update fld_restricted_values
					set val_parent_id = '.( $child_action == 'set_null' || !$val['parent'] ? 'NULL' : $val['parent'] ).'
				where val_tnt_id = '.$config['tnt_id'].'
					and val_parent_id = '.$val['id'].'
			');
			break;
		}
		case 'force_del': {
			// suppression en cascade
			$sql = '
				select val_id as id
				from fld_restricted_values
				where val_tnt_id = '.$config['tnt_id'].'
					and val_parent_id = '.$val['id'].'
			';
			if( $r = ria_mysql_query($sql) ){
				while( $v = ria_mysql_fetch_assoc($r) ){
					fld_restricted_values_del( $v['id'], $del_obj, $child_action );
				}
			}
			break;
		}
	}

	// détection du type hiérarchique : pour celui-ci, les identifiants sont utilisés (sinon, ce sont les noms)
	$val_sql = addslashes($val['name']);
	if( fld_fields_get_type( $val['fld_id'] ) == FLD_TYPE_SELECT_HIERARCHY ){
		$val_sql = $val['id'];
	}

	// partie commune de toutes les requêtes
	$sql_commun = '
		pv_tnt_id = '.$config['tnt_id'].' and
		pv_fld_id = '.$val['fld_id'].' and
		pv_lng_code = "'.addslashes($config['i18n_lng']).'"
	';

	if( $del_obj ){

		// met à jour les objets rattachés à la valeur

		// valeur exacte (= suppression)
		ria_mysql_query('
			delete from fld_object_values
			where
				'.$sql_commun.' and
				pv_value = "'.$val_sql.'"
		');

		// contient la valeur (fin de chaine)
		ria_mysql_query('
			update fld_object_values
			set pv_value = replace(pv_value, ", '.$val_sql.'", "")
			where
				'.$sql_commun.' and
				pv_value like "%, '.$val_sql.'"
		');

		// contient la valeur (début de chaine)
		ria_mysql_query('
			update fld_object_values
			set pv_value = replace(pv_value, "'.$val_sql.', ", "")
			where
				'.$sql_commun.' and
				pv_value like "'.$val_sql.', %"
		');

		// contient la valeur (milieu de chaine)
		ria_mysql_query('
			update fld_object_values
			set pv_value = replace(pv_value, ", '.$val_sql.', ", ", ")
			where
				'.$sql_commun.' and
				pv_value like "%, '.$val_sql.', %"
		');

	}else{

		// aucun objet ne doit être rattaché à la valeur
		$robjs = ria_mysql_query('
			select 1
			from fld_object_values
			where
				'.$sql_commun.' and (
					pv_value = "'.$val_sql.'" or
					pv_value like "%, '.$val_sql.'" or
					pv_value like "'.$val_sql.', %" or
					pv_value like "%, '.$val_sql.', %"
				)
		');

		if( $robjs && ria_mysql_num_rows($robjs) ){
			return false;
		}

	}

	// Supprime les traductions
	fld_restricted_values_translates_del( $id, false, $del_obj );

	$sql = '
		delete from fld_restricted_values
		where val_tnt_id = '.$config['tnt_id'].' and val_id = '.$id.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	fld_restricted_values_order_update( $val['fld_id'], fld_restricted_values_order_get($val['fld_id']) );

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return true;

}
/// \endcond

/// \cond onlyria
/**	Cette fonction détermine si une valeur de restriction de champ libre est utilisée
 *	Son utilisation est spécifique au type de champ 12 (hiérarchique multiple) et au type d'objet 1 (produit)
 *	@param int $fld Obligatoire, identifiant du champ libre
 *	@param int $val Obligatoire, identifiant de la valeur de restriction à tester
 *	@param array $val_filters Facultatif, tableau associatif dont la clé est un identifiant de champ libre et la valeur un identifiant de valeur de restriction (ou un sous-tableau de valeurs de restriction). Par défaut ,ce paramètre est omis
 *	@param bool $fld_filter_operator Facultatif, détermine l'opérateur à appliquer entre chaque champ du filtre (and ou or). La valeur par défaut est or
 *	@param string $val_filter_operator Facultatif, détermine l'opérateur à appliquer entre chaque valeur d'un même champ de filtre (and ou or). La valeur par défaut est or
 *	@param array $cats Facultatif, tableau d'identifiants de catégorie de produits sur lesquels filtrer le résultat
 *
 *	@return bool False en cas d'erreur, sinon le nombre d'utilisations de valeur de restriction
 */
function fld_restricted_values_is_used( $fld, $val, $val_filters=array(), $fld_filter_operator='or', $val_filter_operator='or', $cats=array() ){
	global $config;

	if( !is_numeric($val) || $val<=0 ) return false;
	if( !is_numeric($fld) || $fld<=0 ) return false;
	if( fld_fields_get_class($fld)!=CLS_PRODUCT ) return false;
	if( fld_fields_get_type($fld)!=FLD_TYPE_SELECT_HIERARCHY ) return false;
	if( is_array($cats) ){
		foreach( $cats as $cat ){
			if( !is_numeric($cat) ) return false;
		}
	}elseif( is_numeric($cats) ){
		$cats = array( $cats );
	}

	$sql = '
		select distinct pv_obj_id_0 as obj
		from fld_object_values
		where pv_tnt_id='.$config['tnt_id'].' and (
			pv_value like \'%, '.$val.',%\'
			or pv_value like \''.$val.', %\'
			or pv_value like \'%, '.$val.'\'
			or pv_value=\''.$val.'\'
		) and pv_fld_id='.$fld.'
	';

	if( is_array($cats) && sizeof($cats)>0 ){
		$sql .= '
			 and pv_obj_id_0 in (
				select cly_prd_id
				from prd_classify
				where cly_tnt_id='.$config['tnt_id'].' and
				cly_cat_id in ('.implode( ',',$cats ).')
			)
		';
	}

	$result = ria_mysql_query( $sql );
	if( $result==false ) return false;
	if( !ria_mysql_num_rows($result) ) return 0;
	if( !is_array($val_filters) || sizeof($val_filters)==0 ) return ria_mysql_num_rows($result);

	$count = 0;

	while( $r=ria_mysql_fetch_array($result) ){

		$sub_count = array();
		foreach( $val_filters as $key=>$value ){

			if( !is_numeric($key) || $key<=0 ) return false;

			$sql = '
				select count(distinct pv_obj_id_0)
				from fld_object_values, fld_fields
				where (fld_tnt_id=0 or fld_tnt_id=pv_tnt_id) and pv_tnt_id='.$config['tnt_id'].' and
				fld_cls_id='.CLS_PRODUCT.' and fld_id=pv_fld_id and pv_obj_id_0='.$r['obj'].'
				and pv_fld_id='.$key.'
			';

			if( $value!='' ){
				$sql .= ' and (';
				if( is_array($value) || sizeof($value)>0 ){
					$first = true;
					foreach( $value as $v ){
						if( $first )
							$first = false;
						else
							$sql .= $val_filter_operator;
						$sql .= ' (
							pv_value like \'%, '.addslashes($v).',%\'
							or pv_value like \''.addslashes($v).', %\'
							or pv_value like \'%, '.addslashes($v).'\'
							or pv_value=\''.addslashes($v).'\'
						) ';
					}
				}else{
					$sql .= '
						pv_value like \'%, '.addslashes($value).',%\'
						or pv_value like \''.addslashes($value).', %\'
						or pv_value like \'%, '.addslashes($value).'\'
						or pv_value=\''.addslashes($value).'\'
					';
				}
				$sql .= ')';
			}

			$sub_result = ria_mysql_query($sql);
			if( $sub_result==false ) return false;

			$sub_count[] = ria_mysql_result($sub_result,0,0);
		}

		// traitement suivant opérateur
		$sub_total = 0;
		foreach( $sub_count as $sc ){
			$sub_total += $sc;
			if( $fld_filter_operator=='and' && $sc==0 ){
				$sub_total = 0;
				break;
			}
		}

		if( $sub_total>0 )
			$count++;
	}

	return $count;
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet la regénération des alias des valeurs de champs personnalisés restreints
 */
function fld_restricted_regenere_alias(){
	global $config;

	$rfldresval = fld_restricted_values_get();

	while($val = ria_mysql_fetch_array($rfldresval)){
		if( $val['tenant'] ){
			 ria_mysql_query('
				update fld_restricted_values set
					val_alias=\''.addslashes(urlalias($val['name'])).'\'
				where val_tnt_id='.$config['tnt_id'].' and val_id='.$val['id'].'
			');

			// Met à jour ses traduction
			fld_restricted_translates_regenere_alias($val['id']);
		}
	}
}
/// \endcond

/// \cond onlyria
/** Cette fonction met à jour l'identifaint parent d'une valeur de restriction
 *	@param int $val_id Identifiant de la valeur à mettre à jour
 *	@param int $val_parent_id Identifiant du nouveau parent (0 pour racine)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_restricted_values_update_parent( $val_id, $val_parent_id ){
	global $config;

	$rval = fld_restricted_values_get( $val_id );
	if( !$rval || !ria_mysql_num_rows($rval) ) return false;
	$val = ria_mysql_fetch_array($rval);

	$rfield = fld_fields_get( $val['fld_id'] );
	if( !$rfield || !ria_mysql_num_rows($rfield) ) return false;
	$field = ria_mysql_fetch_array($rfield);

	if( $field['type_id']!=FLD_TYPE_SELECT_HIERARCHY ) return false;

	if( !is_numeric($val_parent_id) || $val_parent_id<0 ) return false;

	if( $val_id==$val_parent_id ) return false;
	if( $val['parent_id']==$val_parent_id ) return true;

	$childs = fld_restricted_values_get_childs( $val_id );
	if( is_array($childs) && in_array( $val_parent_id, $childs ) ) return false;

	$res = ria_mysql_query( 'update fld_restricted_values set val_parent_id='.$val_parent_id.' where val_id='.$val_id.' and val_tnt_id='.$config['tnt_id'] );
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
/// \endcond

/// \cond onlyria
/** Récupère toute la parenté enfant d'une valeur de restriction
 *	@param int $val Identifiant de la valeur de restriction
 *	@return array Un tableau des identifiants d'enfants, ou False en cas d'échec
 */
function fld_restricted_values_get_childs( $val ){
	return fld_restricted_values_get_childs_array( $val );
}
/// \endcond

/// \cond onlyria
/** Cette fonction permet de sauvegarder une traduction d'une valeur de restriction. Si une traduction est déjà présente, on lancera une mise à jour.
 *	@param int $id Obligatoire, identifiant d'une valeur
 *	@param string $lng Obligatoire, code ISO de la langue
 *	@param string $name Obligatoire, libellé de la valeur traduite
 *	@return bool Retourne true si l'ajout s'est correctement déroulée
 *	@return bool Retourne false dans la cas contraire
 */
function fld_restricted_values_translates_add( $id, $lng, $name ){
	if( !fld_restricted_values_exists($id) ) return false;
	if( !trim($name) ) return false;
	global $config;

	if( fld_restricted_values_translates_exists($id, $lng) )
		return fld_restricted_values_translates_update( $id, $lng, $name );

	if( !in_array(strtolower($lng), $config['i18n_lng_used']) ) return false;

	$alias = urlalias( $name );

	return ria_mysql_query('
		insert into fld_restricted_values_translates
			( vlg_tnt_id, vlg_lng_code, vlg_val_id, vlg_name, vlg_alias )
		values
			( '.$config['tnt_id'].', \''.strtolower($lng).'\', '.$id.', \''.addslashes($name).'\', \''.$alias.'\' )
	');
}
/// \endcond

/// \cond onlyria
/** Cette fonction permet de mettre à jour une traduction d'une valeur de restriction.
 *	@param int $id Obligatoire, identifiant d'une valeur
 *	@param string $lng code ISO de la langue
 *	@param string $name Obligatoire, nouveau libellé de la valeur traduite
 *	@return bool Retourne true si la mise à jour s'est correctement déroulée
 *	@return bool Retourne false dans le cas contraire
 */
function fld_restricted_values_translates_update( $id, $lng, $name ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !trim($name) ) return false;
	global $config;

	if( !in_array(strtolower($lng), $config['i18n_lng_used']) ) return false;

	return ria_mysql_query('
		update fld_restricted_values_translates
		set vlg_name=\''.addslashes($name).'\', vlg_alias=\''.urlalias($name).'\'
		where vlg_tnt_id='.$config['tnt_id'].'
			and vlg_lng_code=\''.strtolower($lng).'\'
			and vlg_val_id='.$id.'
	');

}
/// \endcond

/// \cond onlyria
/** Cette fonction permet de tester l'existance d'une traduction d'une valeur de restriction.
 *	@param int $id Obligatoire, identifiant d'une valeur de restriction
 *	@param string $lng Obligatoire, code de la langue de la traduction recherchée
 *	@return bool Retourne true si la traduction existe
 *	@return bool Retourne false dans le cas contraire
 */
function fld_restricted_values_translates_exists( $id, $lng ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	if( !in_array(strtolower($lng), $config['i18n_lng_used']) ) return false;

	return ria_mysql_num_rows( ria_mysql_query( 'select 1 from fld_restricted_values_translates where vlg_tnt_id='.$config['tnt_id'].' and vlg_lng_code=\''.strtolower($lng).'\' and vlg_val_id='.$id) )>0;
}
/// \endcond

/** Cette fonction permet de récupérer la ou les traduction d'une valeur de restriction
 *	@param int $id Facultatif, identifiant d'une valeur de restriction
 *	@param string $lng Facultatif, code ISO d'une langue
 *	@param string $name Facultatif, nom de la valeur de restriction
 *	@param string $name_src Facultatif, nom de la valeur de restriction dans la langue par défaut
 *	@param $fld Facultatif, permet de coupler $name ou $name_src avec un champ avancé
 *	@return bool Retourne false si l'un des paramètres est faux
 *	@return resource Retourne un résultat MySQL contenant :
 *				- id : identifiant de la valeur
 *				- lng : code ISO de la langue utilisée
 *				- name : valeur traduite
 *				- alias : alias de la traduction
 */
function fld_restricted_values_translates_get( $id=0, $lng=false, $name=false, $name_src=false, $fld=false ){
	if( !is_numeric($id) || $id<0 ) return false;
	if( $fld!==false && (!is_numeric($fld) || $fld<=0) ) return false;
	$name = $name!==false ? trim($name) : false;
	$name_src = $name_src!==false ? trim($name_src) : false;
	global $config;

	if( $lng!==false && !in_array(strtolower($lng), $config['i18n_lng_used']) ) return false;

	$sql = '
		select vlg_lng_code as lng, vlg_val_id as id, ifnull(vlg_name, val_name) as name, ifnull(vlg_alias, val_alias) as alias
		from fld_restricted_values_translates
			join fld_restricted_values on ( val_tnt_id=vlg_tnt_id and val_id=vlg_val_id )
		where vlg_tnt_id='.$config['tnt_id'].'
	';

	if( $id>0 )
		$sql .= ' and vlg_val_id='.$id;
	if( $lng!==false )
		$sql .= ' and vlg_lng_code=\''.strtolower($lng).'\'';
	if( $name!==false )
		$sql .= ' and vlg_name=\''.addslashes($name).'\'';
	if( $fld!==false )
		$sql .= ' and val_fld_id='.$fld;
	if( $name_src!==false )
		$sql .= ' and val_name=\''.addslashes($name_src).'\'';

	return ria_mysql_query( $sql );
}

/// \cond onlyria
/** Cette fonction permet de supprimer une ou toutes les traductions d'une valeur de restriction.
 *	@param int $id Obligatoire, identifiant d'une valeur de restriction.
 *	@param string $lng Optionnel, code ISO de la langue.
 *	@param bool $del_obj Optionnel, détermine si les relations avec les objets sont supprimées (activé par défaut), ou au contraire si la valeur ne doit pas être utilisée pour pouvoir être supprimée.
 *
 *	@return bool Retourne true si la suppression s'est correctement déroulée
 *	@return bool Retourne false dans le cas contraire
 */
function fld_restricted_values_translates_del( $id, $lng = false, $del_obj = true ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	// charge le détail de la valeur
	$rval = fld_restricted_values_get( $id );
	if( !$rval || !ria_mysql_num_rows($rval) ){
		return false;
	}
	$val = ria_mysql_fetch_assoc($rval);

	// crée un tableau des langues à traiter
	if( $lng !== false && !in_array(strtolower(trim($lng)), $config['i18n_lng_used']) ){
		return false;
	}
	$lng_used_tmp = $lng !== false ? array( strtolower(trim($lng)) ) : $config['i18n_lng_used'];

	// exclusion de la langue par défaut
	$lng_used = array();
	foreach( $lng_used_tmp as $l ){
		if( $l != $config['i18n_lng'] ){
			$lng_used[] = $l;
		}
	}

	if( !sizeof($lng_used) ){
		return true;
	}

	$hierarchique = fld_fields_get_type( $val['fld_id'] ) == FLD_TYPE_SELECT_HIERARCHY;

	foreach( $lng_used as $l ){

		$sqlval = $id;
		if( $hierarchique ){
			// charge la valeur traduite dans la langue en cours
			$rval_tsl = fld_restricted_values_translates_get( $id, $l );
			if( !$rval_tsl || !ria_mysql_num_rows($rval_tsl) ){
				return false;
			}
			$val_tsl = ria_mysql_fetch_assoc($rval_tsl);

			$sqlval = addslashes($val_tsl['name']);
		}

		$commun_sql = '
			pv_tnt_id = '.$config['tnt_id'].'
			and pv_lng_code = "'.addslashes(strtolower(trim($l))).'"
			and pv_fld_id = '.$val['fld_id'].'
		';

		if( $del_obj ){

			// valeur exacte
			ria_mysql_query('
				delete from fld_object_values
				where '.$commun_sql.'
				and pv_value = "'.$sqlval.'"
			');

			// valeur approchée fin
			ria_mysql_query('
				update fld_object_values
				set pv_value = replace(pv_value, ", '.$sqlval.'", "")
				where '.$commun_sql.'
				and pv_value like "%, '.$sqlval.'"
			');

			// valeur approchée début
			ria_mysql_query('
				update fld_object_values
				set pv_value = replace(pv_value, "'.$sqlval.', ", "")
				where '.$commun_sql.'
				and pv_value like "'.$sqlval.', %"
			');

			// valeur approchée centre
			ria_mysql_query('
				update fld_object_values
				set pv_value = replace(pv_value, ", '.$sqlval.', ", ", ")
				where '.$commun_sql.'
				and pv_value like "%, '.$sqlval.', %"
			');

		}else{

			$objects = ria_mysql_query('
				select 1 from fld_object_values
				where '.$commun_sql.'
				and (
					pv_value = "'.$sqlval.'"
					or pv_value like "%, '.$sqlval.'"
					or pv_value like "'.$sqlval.', %"
					or pv_value like "%, '.$sqlval.', %"
				)
			');

			// pour chaque langue, vérifie l'absence d'objets associés, et retourne False s'il y en a
			if( $objects && ria_mysql_num_rows($objects) ){
				return false;
			}

		}

	}

	return ria_mysql_query('
		delete from fld_restricted_values_translates
		where
			vlg_tnt_id = '.$config['tnt_id'].'
			and vlg_val_id = '.$id.'
			and vlg_lng_code in ("'.implode('", "', array_slashes($lng_used)).'")
	');

}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet la regénération des alias des traduction de valeurs de champs personnalisés restreints
 *	@param int $id Facultatif, identifiant d'une valeur de restriction
 */
function fld_restricted_translates_regenere_alias( $id=0 ){
	global $config;

	$rfldresval = fld_restricted_values_translates_get($id);

	while($val = ria_mysql_fetch_array($rfldresval)){
		 ria_mysql_query('
			update fld_restricted_values_translates set
				vlg_alias=\''.addslashes(urlalias($val['name'])).'\'
			where vlg_tnt_id='.$config['tnt_id'].'
				and vlg_id='.$val['id'].'
				and vlg_lng_code=\''.strtolower($val['lng']).'\'
		');
	}
}
/// \endcond

/// \cond onlyria
/**	Cette fonction récupère tous les enfants, directs et indirects, d'une valeur de restriction. Il faut que le champ avancé original soit de type hiérarchique
 *	@param int $id Identifiant de la valeur de restriction
 *	@return bool False en cas d'échec
 *	@return array Un tableau des identifiants enfants
 */
function fld_restricted_values_get_childs_array( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	// valeur introuvable
	$rval = fld_restricted_values_get( $id );
	if( !$rval || !ria_mysql_num_rows($rval) ){
		return false;
	}
	$val = ria_mysql_fetch_array($rval);

	// pas de type hiérarchique
	if( fld_fields_get_type($val['fld_id']) != FLD_TYPE_SELECT_HIERARCHY ){
		return false;
	}

	$ar_childs = array(); // tableau des IDs
	$ar_traitement = array(); // tableau des IDs dont on va aller chercher les enfants (la valeur est l'indicateur si les enfants ont déjà été cherchés)

	$current = $id;
	$i_cur = 0;
	$I_MAX = 2048; // valeur arbitraire pour sortie de boucle infinie

	// tant qu'il existe des valeurs non traitées
	while( $current && $i_cur < $I_MAX ){
		$i_cur++;

		if( $rchilds = fld_restricted_values_get( 0, $val['fld_id'], '', $current ) ){
			while( $child = ria_mysql_fetch_array($rchilds) ){
				// ajout au tableau final
				$ar_childs[] = $child['id'];
				// ajout au tableau des valeurs à traiter
				$ar_traitement[ $child['id'] ] = false;
			}
		}

		// marque la valeur courante comme traitée
		$ar_traitement[ $current ] = true;

		// calcule la prochaine valeur à traiter
		$current = false;
		foreach( $ar_traitement as $ch_id=>$is_traite ){
			if( !$is_traite ){
				$current = $ch_id;
				break;
			}
		}
	}

	return $ar_childs;
}
/// \endcond

/// \cond onlyria
/**	Détermine le nombre de valeurs de restrcition pour un champ avancé à partir de l'identifiant d'une de ces valeurs
 *	@param int $id Identifiant de la valeur de restriction
 *	@return bool False en cas d'échec
 *	@return int Le nombre de valeurs de restrictions pour le champ avancé
 */
function fld_restricted_values_get_count( $id ){
	global $config;

	if( !$id || !is_numeric( $id ) ) return false;

	$rcount = ria_mysql_query('
		select count(*) from fld_restricted_values as v1
		where v1.val_tnt_id='.$config['tnt_id'].' and v1.val_fld_id in (
			select v2.val_fld_id  from fld_restricted_values as v2
			where v2.val_tnt_id='.$config['tnt_id'].' and v2.val_id ='.$id.'
		)
	');

	if( !$rcount || !ria_mysql_num_rows($rcount) ){
		return 0;
	}

	return ria_mysql_result($rcount,0,0);
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet le chargement de la position d'affichage d'une valeur
 *	@param int $id Obligatoire, Identifiant de la valeur
 *	@return int la position d'affichage de la valeur (>=0)
 *	@return bool false en cas d'échec
 */
function fld_restricted_values_get_pos( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$rpos = ria_mysql_query('
		select val_pos from fld_restricted_values
		where val_tnt_id='.$config['tnt_id'].' and val_id='.$id.'
	');
	if( !$rpos || !ria_mysql_num_rows($rpos) ) return false;
	return ria_mysql_result($rpos,0,0);
}
/// \endcond

/// \cond onlyria
/**	Permet le remplacement des NULL dans les positions pour affecté un trie personnnel, la fonction met 0, 1, 2, 3 .. sur le trie par défault
 *  $fld ou $value doit etre renseigné sinon la fonction retournera false
 *	@param int $fld Facultatif, Identifiant du champ
 *	@param int $value Facultatif, Identifiant de la valeur
 *	@param bool $force Facultatif, force la modification des positions utilisé dans move_up et move_down
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_restricted_values_set_pos( $fld=false, $value=false, $force=false ){

	if( $fld!==false && (!is_numeric( $fld ) || $fld<=0) ) return false;
	if( $value!==false && (!is_numeric( $value ) || $value<=0) ) return false;
	if( !$fld && !$value ) return false;

	if( $value ){

		// on récupère la valeur pour avoir l'identifiant du champ
		$rvalue = fld_restricted_values_get( $value );
		if( !$rvalue || !ria_mysql_num_rows( $rvalue ) ) return false;

		$val = ria_mysql_fetch_array( $rvalue );

		$fld = $val['fld_id'];

	}

	$res = fld_restricted_values_order_update( $fld, $force );
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
/// \endcond

/// \cond onlyria
/**	Déplace une valeur vers le haut dans l'ordre d'affichage.
 *	@param int $id Obligatoire, Identifiant de la valeur à déplacer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_restricted_values_move_up( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	fld_restricted_values_set_pos( false, $id, true );

	$pos = fld_restricted_values_get_pos( $id );
	if( $pos===false ) return false;

	global $config;

	$r1 = $r2 = true;
	if( $pos>0 ){
		$r1 = ria_mysql_query('update fld_restricted_values set val_pos=val_pos-1 where val_tnt_id='.$config['tnt_id'].' and val_id='.$id);
		$r2 = ria_mysql_query('update fld_restricted_values set val_pos=val_pos+1 where val_tnt_id='.$config['tnt_id'].' and val_pos='.($pos-1).' and val_id!='.$id);
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $r1 && $r2;
}
/// \endcond

/// \cond onlyria
/**	Déplace une valeur vers le bas dans l'ordre d'affichage.
 *	@param int $id Obligatoire, identifiant de la valeur à déplacer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_restricted_values_move_down( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	fld_restricted_values_set_pos( false, $id, true );

	$pos = fld_restricted_values_get_pos($id);
	$count = fld_restricted_values_get_count($id);

	if( $pos===false ) return false;

	global $config;

	$r1 = $r2 = true;
	if( $pos<$count-1 ){
		$r1 = ria_mysql_query('update fld_restricted_values set val_pos=val_pos+1 where val_tnt_id='.$config['tnt_id'].' and val_id='.$id);
		$r2 = ria_mysql_query('update fld_restricted_values set val_pos=val_pos-1 where val_tnt_id='.$config['tnt_id'].' and val_pos='.($pos+1).' and val_id!='.$id);
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $r1 && $r2;
}
/// \endcond

/// \cond onlyria
/**	Déplace la valeur avant ou après une autre valeur
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *	L'utilisateur doit s'assurer que les 2 catégories appartiennent au même parent (sinon ça n'a pas de sens)
 *
 *	@param int $source Identifiant de la valeur source
 *	@param int $target Identifiant de la valeur cible
 *	@param string $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
*/
function fld_restricted_values_position_update( $source, $target, $where ){
	$res = obj_position_update( DD_FLD_RESTRICTED_VALUE, $source, $target, $where );
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
/// \endcond

/// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier les valeurs
 *	@param int $fld Obligatoire, identifiant du champ
 *
 *	@return bool false si la méthode de tri est alphabétique, true si la méthode de tri est personnalisée
 */
function fld_restricted_values_order_get( $fld ){
	if( !is_numeric( $fld ) || $fld<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select val_id
		from fld_restricted_values
		where val_tnt_id='.$config['tnt_id'].'
			and val_fld_id = '.$fld.'
			and val_pos is not null
	' );

	return ria_mysql_num_rows($res);
}
/// \endcond

/// \cond onlyria
/**	Cette fonction permet la modification de la méthode de tri utilisée pour un ensemble des valeurs d'un champ.
 *
 *	@param int $fld Obligatoire, Identifiant du champ dont on souhaite modifier l'ordre d'apparition des enfants. 0 pour la racine du catalogue.
 *	@param bool $order Obligatoire, Mode de tri. 0/false pour un tri alphabétique, 1/true pour un tri numérique défini par l'utilisateur
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_restricted_values_order_update( $fld, $order ){
	if( !fld_fields_exists($fld) ) return false;

	global $config;

	if( $order ){
		$rvalues = fld_restricted_values_get(0, $fld );
		$pos = 0;
		while( $v = ria_mysql_fetch_array($rvalues) ){
			ria_mysql_query('update fld_restricted_values set val_pos='.$pos.' where val_tnt_id='.$config['tnt_id'].' and val_id='.$v['id']);
			$pos++;
		}
	}else{
		$rvalues = fld_restricted_values_get(0, $fld );
		while( $v = ria_mysql_fetch_array($rvalues) ){
			ria_mysql_query('update fld_restricted_values set val_pos=null where val_tnt_id='.$config['tnt_id'].' and val_id='.$v['id']);
		}
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return true;
}
/// \endcond

/// \cond onlyria
/** récupère une liste distinctes de valeur de restriction pour un champ libre de la clase produit, en fonction de paramètre optionnels
 *	Il est obligatoire de préciser un des paramètres ($cat, $brd ou $prd_p + $opt_id).
 *	Si plusieurs de ces paramètres sont précisés, leur priorité de prise en compte est la même que leur ordre dans l'appel de la fonction.
 *	Le paramètre $val_parent_id est toujours pris en compte
 *
 *	@param int $fld Obligatoire, identifiant du champ libre
 *	@param int $cat Facultatif, identifiant d'une catégorie
 *	@param bool $recursive Facultatif, détermine si les sous niveaux de catégories sont pris en compte (uniquement pris en compte si $cat est spécifié)
 *	@param int $opt_id Facultatif, identifiant d'une option de nomenclature (le paramètre $prd_p est obligatoire si ce paramètre est spécifié)
 *	@param int $val_parent_id Facultatif, identifiant d'une valeur de restriction parent (-1 pour racine uniquement, c'est le paramètre par défaut). 0 pour ne pas filtrer
 *	@param bool $recursive_val Facultatif, par défaut, récupère toute la hiérarchie enfante pour le type FLD_TYPE_SELECT_HIERARCHY
 *	@param int $brd Facultatif, identifiant d'une marque
 *	@param string $lng Facultatif, identifiant de la langue pour laquelle on souhaite récupérer les valeurs de restriction
 *
 *	@return bool False en cas d'échec
 *	@return array un tableau associatif contenant les mêmes colonnes que la fonction fld_restricted_values_get
 */
function fld_restricted_values_get_distinct( $fld, $cat=0, $recursive=false, $opt_id=0, $val_parent_id=-1, $recursive_val=true, $brd=0, $lng=false ){

	$rfld = fld_fields_get( $fld );
	if( !$rfld || !ria_mysql_num_rows($rfld) ) return false;

	$field = ria_mysql_fetch_array( $rfld );
	if( $field['cls_id'] !=CLS_PRODUCT ) return false;

	if( $val_parent_id != 0 && $val_parent_id != -1 ){
		if( !($rvalp = fld_restricted_values_get( $val_parent_id, $fld )) ) return false;
		if( !ria_mysql_num_rows($rvalp) ) return false;
	}
	if( $cat!=0 && !prd_categories_exists( $cat ) ) return false;
	if( $opt_id!=0 && !prd_options_exists( $opt_id ) ) return false;
	if( $brd!=0 && !prd_brands_exists($brd) ) return false;
	if( $cat==0 && $opt_id==0 && $brd==0 ) return false;

	$rvals = fld_restricted_values_get( 0, $fld, '', $val_parent_id );
	if( !$rvals || !ria_mysql_num_rows($rvals) ) return false;

	global $config;

	if( $lng!=false && !in_array( strtolower($lng), $config['i18n_lng_used'] ) ) return false;
	if( $lng==false ) $lng = $config['i18n_lng'];

	$array_vals = array();
	while( $v = ria_mysql_fetch_array($rvals) ){
		if( strtolower($lng)!=$config['i18n_lng'] ){
			if( $rval = fld_restricted_values_translates_get( $v['id'], $lng ) ){
				$array_vals[] = ria_mysql_fetch_array( $rval );
			}
		}else{
			$array_vals[] = $v;
		}
	}

	$ids = array();
	$distinct_val = array();

	$select_from = '
		select
			distinct(pv_value) as val
		from
			prd_products as p
			join fld_object_values on ( p.prd_tnt_id=pv_tnt_id and p.prd_id=pv_obj_id_0 )
	';

	$where = '
		where
			pv_fld_id='.$fld.'
			and pv_lng_code=\''.strtolower($lng).'\'
			and p.prd_date_deleted is null
			and p.prd_tnt_id='.$config['tnt_id'].'
	';

	if( $cat!=0 ){

		$sql = '
			'.$select_from.'
				join prd_classify on ( p.prd_tnt_id=cly_tnt_id and p.prd_id=cly_prd_id )
			'.$where.'
				and cly_cat_id='.$cat.'
		';
		if( $recursive ){
			$sql .= '
				union
				'.$select_from.'
					join prd_cat_hierarchy as h on ( p.prd_tnt_id = h.cat_tnt_id )
					join prd_categories as c on ( h.cat_tnt_id = c.cat_tnt_id AND h.cat_child_id = c.cat_id )
					join prd_classify on ( p.prd_tnt_id = cly_tnt_id AND p.prd_id = cly_prd_id and c.cat_id=cly_cat_id )
				'.$where.'
					and h.cat_parent_id='.$cat.'
					and c.cat_date_deleted is null
			';
		}

	}elseif( $opt_id ){

		$sql = '
			'.$select_from.'
				join prd_options_products as op on ( p.prd_tnt_id=opt_tnt_id and op.prd_id=p.prd_id )
			'.$where.'
				and op.opt_id='.$opt_id.'
		';

	}elseif( $brd!=0 ){

		$sql = '
			'.$select_from.'
			'.$where.'
				and p.prd_brd_id='.$brd.'
		';

	}else{
		$sql = $select_from.$where;
	}

	$result = ria_mysql_query( $sql );
	if( !$result || !ria_mysql_num_rows($result) ) return false;

	while( $r = ria_mysql_fetch_array($result) ){
		if( sizeof($distinct_val)<sizeof($array_vals) ){
			$values = explode( ', ', $r['val'] );
			if( $field['type_id']==FLD_TYPE_SELECT_HIERARCHY ){

				foreach( $values as $v ){

					$v_array = array($v);
					if( $recursive_val ){
						while( $v ){
							if( $rvv = fld_restricted_values_get( $v, $fld ) ){
								if( $vv = ria_mysql_fetch_array( $rvv ) ){
									if( $vv['parent'] ){
										$v_array[] = $vv['parent'];
										$v = $vv['parent'];
									} else {
										$v = false;
									}
								} else {
									$v = false;
								}
							} else {
								$v = false;
							}
						}
					}

					foreach( $array_vals as $av ){
						if( in_array( $av['id'], $v_array )  && !in_array( $av['id'], $ids ) ){
							$ids[] = $av['id'];
							$distinct_val[] = $av;
						}
					}

				}

			}else{

				foreach( $values as $v ){
					foreach( $array_vals as $av ){
						if( $v == $av['name'] && !in_array( $av['id'], $ids ) ){
							$ids[] = $av['id'];
							$distinct_val[] = $av;
						}
					}
				}

			}
		}
	}

	return $distinct_val;
}
/// \endcond

/// @}
