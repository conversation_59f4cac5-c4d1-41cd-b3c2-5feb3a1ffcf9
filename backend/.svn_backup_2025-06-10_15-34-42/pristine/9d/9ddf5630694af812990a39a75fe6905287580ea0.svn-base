<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AC' => 'Isola Ascensione',
  'AD' => 'Andorra',
  'AE' => 'Emirati Arabi Uniti',
  'AF' => 'Afghanistan',
  'AG' => 'Antigua e Barbuda',
  'AI' => 'Anguilla',
  'AL' => 'Albania',
  'AM' => 'Armenia',
  'AO' => 'Angola',
  'AQ' => 'Antartide',
  'AR' => 'Argentina',
  'AS' => 'Samoa americane',
  'AT' => 'Austria',
  'AU' => 'Australia',
  'AW' => 'Aruba',
  'AX' => 'Isole Åland',
  'AZ' => 'Azerbaigian',
  'BA' => 'Bosnia ed Erzegovina',
  'BB' => 'Barbados',
  'BD' => 'Bangladesh',
  'BE' => 'Belgio',
  'BF' => 'Burkina Faso',
  'BG' => 'Bulgaria',
  'BH' => 'Bahrein',
  'BI' => 'Burundi',
  'BJ' => 'Benin',
  'BL' => 'Saint-Barthélemy',
  'BM' => 'Bermuda',
  'BN' => 'Brunei',
  'BO' => 'Bolivia',
  'BQ' => 'Caraibi olandesi',
  'BR' => 'Brasile',
  'BS' => 'Bahamas',
  'BT' => 'Bhutan',
  'BW' => 'Botswana',
  'BY' => 'Bielorussia',
  'BZ' => 'Belize',
  'CA' => 'Canada',
  'CC' => 'Isole Cocos (Keeling)',
  'CD' => 'Congo - Kinshasa',
  'CF' => 'Repubblica Centrafricana',
  'CG' => 'Congo-Brazzaville',
  'CH' => 'Svizzera',
  'CI' => 'Costa d’Avorio',
  'CK' => 'Isole Cook',
  'CL' => 'Cile',
  'CM' => 'Camerun',
  'CN' => 'Cina',
  'CO' => 'Colombia',
  'CR' => 'Costa Rica',
  'CU' => 'Cuba',
  'CV' => 'Capo Verde',
  'CW' => 'Curaçao',
  'CX' => 'Isola Christmas',
  'CY' => 'Cipro',
  'CZ' => 'Cechia',
  'DE' => 'Germania',
  'DG' => 'Diego Garcia',
  'DJ' => 'Gibuti',
  'DK' => 'Danimarca',
  'DM' => 'Dominica',
  'DO' => 'Repubblica Dominicana',
  'DZ' => 'Algeria',
  'EA' => 'Ceuta e Melilla',
  'EC' => 'Ecuador',
  'EE' => 'Estonia',
  'EG' => 'Egitto',
  'EH' => 'Sahara occidentale',
  'ER' => 'Eritrea',
  'ES' => 'Spagna',
  'ET' => 'Etiopia',
  'FI' => 'Finlandia',
  'FJ' => 'Figi',
  'FK' => 'Isole Falkland',
  'FM' => 'Micronesia',
  'FO' => 'Isole Fær Øer',
  'FR' => 'Francia',
  'GA' => 'Gabon',
  'GB' => 'Regno Unito',
  'GD' => 'Grenada',
  'GE' => 'Georgia',
  'GF' => 'Guyana francese',
  'GG' => 'Guernsey',
  'GH' => 'Ghana',
  'GI' => 'Gibilterra',
  'GL' => 'Groenlandia',
  'GM' => 'Gambia',
  'GN' => 'Guinea',
  'GP' => 'Guadalupa',
  'GQ' => 'Guinea Equatoriale',
  'GR' => 'Grecia',
  'GS' => 'Georgia del Sud e Sandwich australi',
  'GT' => 'Guatemala',
  'GU' => 'Guam',
  'GW' => 'Guinea-Bissau',
  'GY' => 'Guyana',
  'HK' => 'RAS di Hong Kong',
  'HN' => 'Honduras',
  'HR' => 'Croazia',
  'HT' => 'Haiti',
  'HU' => 'Ungheria',
  'IC' => 'Isole Canarie',
  'ID' => 'Indonesia',
  'IE' => 'Irlanda',
  'IL' => 'Israele',
  'IM' => 'Isola di Man',
  'IN' => 'India',
  'IO' => 'Territorio britannico dell’Oceano Indiano',
  'IQ' => 'Iraq',
  'IR' => 'Iran',
  'IS' => 'Islanda',
  'IT' => 'Italia',
  'JE' => 'Jersey',
  'JM' => 'Giamaica',
  'JO' => 'Giordania',
  'JP' => 'Giappone',
  'KE' => 'Kenya',
  'KG' => 'Kirghizistan',
  'KH' => 'Cambogia',
  'KI' => 'Kiribati',
  'KM' => 'Comore',
  'KN' => 'Saint Kitts e Nevis',
  'KP' => 'Corea del Nord',
  'KR' => 'Corea del Sud',
  'KW' => 'Kuwait',
  'KY' => 'Isole Cayman',
  'KZ' => 'Kazakistan',
  'LA' => 'Laos',
  'LB' => 'Libano',
  'LC' => 'Saint Lucia',
  'LI' => 'Liechtenstein',
  'LK' => 'Sri Lanka',
  'LR' => 'Liberia',
  'LS' => 'Lesotho',
  'LT' => 'Lituania',
  'LU' => 'Lussemburgo',
  'LV' => 'Lettonia',
  'LY' => 'Libia',
  'MA' => 'Marocco',
  'MC' => 'Monaco',
  'MD' => 'Moldavia',
  'ME' => 'Montenegro',
  'MF' => 'Saint Martin',
  'MG' => 'Madagascar',
  'MH' => 'Isole Marshall',
  'MK' => 'Repubblica di Macedonia',
  'ML' => 'Mali',
  'MM' => 'Myanmar (Birmania)',
  'MN' => 'Mongolia',
  'MO' => 'RAS di Macao',
  'MP' => 'Isole Marianne settentrionali',
  'MQ' => 'Martinica',
  'MR' => 'Mauritania',
  'MS' => 'Montserrat',
  'MT' => 'Malta',
  'MU' => 'Mauritius',
  'MV' => 'Maldive',
  'MW' => 'Malawi',
  'MX' => 'Messico',
  'MY' => 'Malaysia',
  'MZ' => 'Mozambico',
  'NA' => 'Namibia',
  'NC' => 'Nuova Caledonia',
  'NE' => 'Niger',
  'NF' => 'Isola Norfolk',
  'NG' => 'Nigeria',
  'NI' => 'Nicaragua',
  'NL' => 'Paesi Bassi',
  'NO' => 'Norvegia',
  'NP' => 'Nepal',
  'NR' => 'Nauru',
  'NU' => 'Niue',
  'NZ' => 'Nuova Zelanda',
  'OM' => 'Oman',
  'PA' => 'Panamá',
  'PE' => 'Perù',
  'PF' => 'Polinesia francese',
  'PG' => 'Papua Nuova Guinea',
  'PH' => 'Filippine',
  'PK' => 'Pakistan',
  'PL' => 'Polonia',
  'PM' => 'Saint-Pierre e Miquelon',
  'PN' => 'Isole Pitcairn',
  'PR' => 'Portorico',
  'PS' => 'Territori palestinesi',
  'PT' => 'Portogallo',
  'PW' => 'Palau',
  'PY' => 'Paraguay',
  'QA' => 'Qatar',
  'RE' => 'Riunione',
  'RO' => 'Romania',
  'RS' => 'Serbia',
  'RU' => 'Russia',
  'RW' => 'Ruanda',
  'SA' => 'Arabia Saudita',
  'SB' => 'Isole Salomone',
  'SC' => 'Seychelles',
  'SD' => 'Sudan',
  'SE' => 'Svezia',
  'SG' => 'Singapore',
  'SH' => 'Sant’Elena',
  'SI' => 'Slovenia',
  'SJ' => 'Svalbard e Jan Mayen',
  'SK' => 'Slovacchia',
  'SL' => 'Sierra Leone',
  'SM' => 'San Marino',
  'SN' => 'Senegal',
  'SO' => 'Somalia',
  'SR' => 'Suriname',
  'SS' => 'Sud Sudan',
  'ST' => 'São Tomé e Príncipe',
  'SV' => 'El Salvador',
  'SX' => 'Sint Maarten',
  'SY' => 'Siria',
  'SZ' => 'Swaziland',
  'TA' => 'Tristan da Cunha',
  'TC' => 'Isole Turks e Caicos',
  'TD' => 'Ciad',
  'TF' => 'Terre australi francesi',
  'TG' => 'Togo',
  'TH' => 'Thailandia',
  'TJ' => 'Tagikistan',
  'TK' => 'Tokelau',
  'TL' => 'Timor Est',
  'TM' => 'Turkmenistan',
  'TN' => 'Tunisia',
  'TO' => 'Tonga',
  'TR' => 'Turchia',
  'TT' => 'Trinidad e Tobago',
  'TV' => 'Tuvalu',
  'TW' => 'Taiwan',
  'TZ' => 'Tanzania',
  'UA' => 'Ucraina',
  'UG' => 'Uganda',
  'UM' => 'Altre isole americane del Pacifico',
  'US' => 'Stati Uniti',
  'UY' => 'Uruguay',
  'UZ' => 'Uzbekistan',
  'VA' => 'Città del Vaticano',
  'VC' => 'Saint Vincent e Grenadine',
  'VE' => 'Venezuela',
  'VG' => 'Isole Vergini Britanniche',
  'VI' => 'Isole Vergini Americane',
  'VN' => 'Vietnam',
  'VU' => 'Vanuatu',
  'WF' => 'Wallis e Futuna',
  'WS' => 'Samoa',
  'XK' => 'Kosovo',
  'YE' => 'Yemen',
  'YT' => 'Mayotte',
  'ZA' => 'Sudafrica',
  'ZM' => 'Zambia',
  'ZW' => 'Zimbabwe',
);
