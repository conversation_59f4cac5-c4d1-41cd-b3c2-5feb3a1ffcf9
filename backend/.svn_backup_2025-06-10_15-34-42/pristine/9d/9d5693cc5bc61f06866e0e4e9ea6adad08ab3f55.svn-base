Include and eval
-----
<?php
include 'A.php';
include_once 'A.php';
require 'A.php';
require_once 'A.php';
eval('A');
-----
array(
    0: Expr_Include(
        expr: Scalar_String(
            value: A.php
        )
        type: TYPE_INCLUDE (1)
    )
    1: Expr_Include(
        expr: Scalar_String(
            value: A.php
        )
        type: TYPE_INCLUDE_ONCE (2)
    )
    2: Expr_Include(
        expr: Scalar_String(
            value: A.php
        )
        type: TYPE_REQUIRE (3)
    )
    3: Expr_Include(
        expr: Scalar_String(
            value: A.php
        )
        type: TYPE_REQURE_ONCE (4)
    )
    4: Expr_Eval(
        expr: Scalar_String(
            value: A
        )
    )
)