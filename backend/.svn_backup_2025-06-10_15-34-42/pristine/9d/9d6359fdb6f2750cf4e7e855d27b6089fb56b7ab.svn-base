<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/pubsub.proto

namespace Google\Cloud\PubSub\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\PubSub\V1\PushConfig\OidcToken instead.
     * @deprecated
     */
    class PushConfig_OidcToken {}
}
class_exists(PushConfig\OidcToken::class);
@trigger_error('Google\Cloud\PubSub\V1\PushConfig_OidcToken is deprecated and will be removed in the next major release. Use Google\Cloud\PubSub\V1\PushConfig\OidcToken instead', E_USER_DEPRECATED);

