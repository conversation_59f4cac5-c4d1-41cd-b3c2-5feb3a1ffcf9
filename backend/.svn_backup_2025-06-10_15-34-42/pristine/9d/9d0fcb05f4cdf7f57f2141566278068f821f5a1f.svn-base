var prdID = 0;
var auctions = 0;
var auctionsDirect = false;
var title = '';
var desc = '';

$(document).ready(
	function(){
		prdID = $('#prd').val();
		
		$('.mapping input[type=radio]').click(function(){
			if( $(this).is(':checked') ){
				var attrID = $(this).attr('id');
				
				var action = attrID.substring( 0, attrID.indexOf('-') );
				var ctrID = attrID.substring( attrID.indexOf('-')+1 );
				var catID = $('#prdcat').length ? $('#prdcat').val() : 0;
				
				if( action=='expnew' ){
					var url = '/admin/catalog/popup-export-comparators.php?ctr=' + ctrID;
					if( catID>0 )
						url += '&prdcat=' + catID;
					
					displayPopup("Choisissez une famille du comparateur", '', url, 'hidePopupChoose(' + ctrID + ')',950,450 );
				} else {
					hidePopup();
				}
			}
		});
		
		if( $('#typeproduct').length ){
			loadTemplatePrice();
			$('#typeproduct').change(function(){
				loadTemplatePrice();
			});
		}
	}
).delegate(
	'#templatectr-15 select', 'change', function(){
		var show = false;
		
		if( $(this).attr('multiple')!='undefined' ){
			$(this).find('option:selected').each(function(){
				if( $(this).val()=='other' ){
					show = true;
				}
			});
		}else{
			if( $(this).val() == 'other' ){
				show = true;
			}
		}
		
		if( show ){
			$(this).parent().find('.other').show();
			$(this).parents('.elem').find('.opt-other').show();
		}else{
			$(this).parent().find('.other').hide().val('');
			$(this).parents('.elem').find('.opt-other').hide();
		}
	}
).delegate(
	'#add-option', 'change', function(){
		var val = $(this).val();
		var name = $('#add-option option[value="' + val + '"]').html()
		
		var ctrID = $(this).parents('.add-option').find('[name=add-opt-ctr]').val();
		
		$.get('/admin/ajax/comparators/ajax-comparators.php?add-opt-group=1&ctr=' + ctrID + '&prd=' + prdID + '&code=' + val + '&name=' + name, function(result){
			if( result=='ok' ){
				$('#add-option option[value="' + val + '"]').remove();
				var option = '<li><input type="image" src="/admin/images/del-cat.png" class="del-option" width="16" height="13" title="' + catalogComparatorRemoveOption + '" name="del-' + val + '" /> ' + name + '</li>';
				if( $('.ul-opt ul').length ){
					$('.ul-opt ul').append( option );
				}else{
					$('.ul-opt').html( '<ul>' + option + '</ul>' );
				}
			}
		});
	}
).delegate(
	'.del-option', 'click', function(){
		var code = $(this).attr('name').replace('del-', '');
		
		var ctrID = $('.add-option').find('[name=add-opt-ctr]').val();
		
		$.get('/admin/ajax/comparators/ajax-comparators.php?del-opt-group=1&ctr=' + ctrID + '&prd=' + prdID + '&code=' + code, function( listOptions ){
			if( listOptions!='' ){
				$('#add-option').html( listOptions );
			}
		});
		
		$(this).parent().remove();
		return false;
	}
).delegate(
	'#riawebsitepicker.prd-catalog .selector a', 'click', function(){
		var wst = $(this).attr('name').replace('w-', '');
		var url = window.location.href.replace(new RegExp(/[\?&]tab=[a-z]+/g), '');
		url += ( url.match(/\?/g) ? '&' : '?' ) + 'wst=' + wst + '&tab=comparators';

		window.location.href = url;
	}
);

function loadTemplateMarketPlace( ctrID, catID, prdID ){
	$.get('/admin/ajax/comparators/ajax-comparators.php?ctr=' + ctrID + '&cat=' + catID + '&prd=' + prdID, function(data){
		$('#templatectr-' + ctrID).html( data );
		if( $.trim(data)=='' ){
			$('#templatectr-' + ctrID).parents('tr').hide();
		} else {
			$('#templatectr-' + ctrID).parents('tr').show();
		}
		
		if( $('input[name=ebay-multisku]').length ){
			if( $('input[name=ebay-multisku]').val()=='false' ){
				if( !$('#templatectr-15').next().find('.notice').length ){
					$('#templatectr-15').next().find('hr:first').before('<div class="notice" style="margin-top: 5px;">' +  + '</div>')
				}
			}else{
				$('#templatectr-15').next().find('.notice').remove();
			}
		}
	});
	hidePopup();
}

function loadTemplatePrice(){
	var alias = $('#typeproduct').length ? $('#typeproduct').val() : '';
	$('#templatepriceminister').hide();
	
	$.get('/admin/ajax/comparators/ajax-priceminister.php?prd=' + prdID + '&alias=' + alias + '&' + $('#templatepriceminister input').serialize(), function( data ){
		
		if( $.trim(data)!='' ){
			$('#templatepriceminister').html( data ).show();
		}
		
	});
}

/** Cette fonction permet d'activer un produit enfant dans l'export.
 *	@param inPut Obligatoire, lien sur lequel on clique
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 */
function activatedProductChild( inPut, ctrID ){
	var childID = inPut.attr('name').replace('prd-','');
	
	$.ajax({
		type: 'post',
		url: '/admin/ajax/comparators/ajax-actions.php',
		data: 'ctr=' + ctrID + '&prd=' + childID + '&activated=1',
		dataType: 'json',
		success: function(res){
			if( res.type!='0' ){
				inPut.before('<a class="cancel" name="prd-' + childID + '" href="#" onclick="return unactivatedProductChild( $(this), ' + ctrID + ' );">' + catalogComparatorDesactiverexport + '</a>').remove();
			}
		}
	});
	
	return false;
}

/** Cette fonction permet d'activer un produit enfant dans l'export.
 *	@param inPut Obligatoire, lien sur lequel on clique
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 */
function unactivatedProductChild( inPut, ctrID ){
	var childID = inPut.attr('name').replace('prd-','');
	
	$.ajax({
		type: 'post',
		url: '/admin/ajax/comparators/ajax-actions.php',
		data: 'ctr=' + ctrID + '&prd=' + childID + '&unactivated=1',
		dataType: 'json',
		success: function(res){
			if( res.type!='0' ){
				inPut.before('<a class="active" href="#" name="prd-' + childID + '" onclick="return activatedProductChild( $(this), ' + ctrID + ' );">' + catalogComparatorActiverexport + '</a>').remove();
			}
		}
	});
	
	return false;
}

/**
 *	Cette fonction permet de désactiver un produit dans l'export.
 *	@param inPut Obligatoire, lien sur lequel on clique
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 */
function unactivatedProduct( inPut, ctrID, inline ){
	$('.message-return').html('')
	const prd_id = inPut.attr('name').replace('prd-',''); 
	
	$.ajax({
		type: 'post',
		url: '/admin/ajax/comparators/ajax-actions.php',
		data: 'ctr=' + ctrID + '&prd=' + prd_id + '&unactivated=1',
		dataType: 'json',
		success: function(res){
			if( res.type=='0' ){
				$('#msg-' + ctrID).html( '<div class="error">' + res.message + '</div>' );
			} else {
				if( !inline ){
					inPut.parent().html( 'Non - <a onclick="return activatedProduct( $(this),' + ctrID +', false );" href="#" name="prd-'+prd_id+'" class="active">' + catalogComparatorActiverexport + '</a>' );
				}else{
					inPut.parent().html( '<a onclick="return activatedProduct( $(this),' + ctrID +', true );" href="#" name="prd-'+prd_id+'" class="cancel">' + catalogComparatorNon + '</a>' );
				}
				$('#typeproduct').parent().siblings().find('.mandatory').remove();
				if( window.parent != window )
					window.parent.reloadList(window.parent.location.href.replace('/admin/comparators/stats/index.php','/admin/ajax/comparators/json-stats.php'));
			}
		}
	});
	
	return false;
}

/** 
 *	Cette fonction permet d'activer un produit dans l'export.
 *	@param inPut Obligatoire, lien sur lequel on clique
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 */
function activatedProduct( inPut, ctrID, inline ){
	$('.message-return').html('')
	const prd_id = inPut.attr('name').replace('prd-',''); 
	
	var data = '';
	if( $('#marketplace').length ){
		data += '&marketplace=' + ($('#marketplace').val() == 1 ? 1 : 0);
	}
	
	$.ajax({
		type: 'post',
		url: '/admin/ajax/comparators/ajax-actions.php',
		data: 'ctr=' + ctrID + '&prd=' + prd_id + '&activated=1' + data,
		dataType: 'json',
		success: function(res){
			if( res.type=='0' ){
				$('#msg-' + ctrID).html( '<div class="error">' + res.message + '</div>' );
			} else {
				if( !inline ){
					inPut.parent().html( 'Oui - <a onclick="return unactivatedProduct( $(this),' + ctrID +', false );" href="#" name="prd-'+prd_id+'" class="cancel">' + catalogComparatorDesactiverexport + '</a>' );
				}else{
					inPut.parent().html( '<a onclick="return unactivatedProduct( $(this),' + ctrID +', true );" href="#" name="prd-'+prd_id+'" class="active">Oui</a>' );
				}
				if( ctrID==8 ){
					$('#typeproduct').parent().siblings().html( $('#typeproduct').parent().siblings().html().replace(':', '<span class="mandatory">*</span> :') );
				}
				if( window.parent != window )
					window.parent.reloadList(window.parent.location.href.replace('/admin/comparators/stats/index.php','/admin/ajax/comparators/json-stats.php'));
			}
		}
	});
	
	return false;
}

/** 
 *	Cette fonction permet de sauvegarder un titre et une description personnalisée pour l'export d'un produit.
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 */
function saveInfosPerso( ctrID ){
	$('.success, .error').remove();
	
	if( ctrID ){
		var title = $.trim( $('#title-' + ctrID).val() );
		var desc = $.trim( $('#desc-' + ctrID).val() );
		var auction = $('#auctions-' + ctrID).length ? $.trim( $('#auctions-' +ctrID).val() ).replace(',', '.') : '';
		var alias = $('#typeproduct').length ? $('#typeproduct').val() : '';
		
		var othersData = '';
		if( typeof $('.marketplace-mdl :input') != 'undefined' && $('.marketplace-mdl :input').length ){
			othersData += '&' + $('.marketplace-mdl :input').serialize();
		}

		$.ajax({
			type: 'post',
			url: '/admin/ajax/comparators/ajax-actions.php',
			data: 'save-info=1&ctr=' + ctrID + '&prd=' + prdID + '&title=' + encodeURIComponent(title) + '&desc=' + encodeURIComponent(desc) + '&auctions=' + auction + '&alias=' + alias + '&' + $('#templatepriceminister input, #templatepriceminister select').serialize() + othersData,
			dataType: 'json',
			success: function(res){
				if( res.type=='0' ){
					$('.export').before( '<div class="error">' + res.message + '</div>' );
				} else {
					$('.export').before( '<div class="success">' + res.message + '</div>' );
					$('#auctions-' +ctrID).val( res.amount );
				}
				$(window).scrollTop(0)
			}
		});
	
	} else {
		return true;
	}
	
	return false;
}

/**
 *	Cette fonction permet d'afficher le choix de l'export pour une catégorie produit
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 */
function modifiedCtrCat( ctrID ){
	$('#diffctr-' + ctrID).show();
	return false;
}

/** 
 *	Cette fonction permet d'afficher le formulaire pour modifier l'enchère directement sur un produit.
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 */
function modifyCatAuctions( ctrID ){
	$('.message-return').html('')
	
	var html = '';
	auctions = $('#amount-' + ctrID).val();
	auctionsDirect = $('#auctions-direct-' + ctrID).val()=='1';
	
	html += '	<input class="text" type="text" name="auctions" id="auctions-' + ctrID + '" value="' + ( $.trim(auctions) ? auctions : '' ) + '" /><br />';
	html += '	<input class="save" type="submit" name="save-auctions" id="save-auctions" value="' + catalogComparatorEnregistrer + '" onclick="return saveCatAuctions(' + ctrID +');" />';
	html += '	<a href="#" class="cancel" onclick="return cancelModifyCatAuctions(' + ctrID +');">' + catalogComparatorAnnuler + '</a>';
	
	$('#td-auctions-' + ctrID).html( html );
	return false;
}

/** 
 *	Cette fonction permet de sauvegarder une enchère personnalisée pour un produit
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 */
function saveCatAuctions( ctrID ){
	$('.message-return').html('')

	var auctionsPerso = $('#auctions-' + ctrID).val();
	var catID = $('#prdcat').val();
	
	$.ajax({
		type: 'post',
		url: '/admin/ajax/comparators/ajax-actions.php',
		data: 'ctr=' + ctrID + '&cat=' + catID + '&auctionsCat=' + encodeURIComponent( auctionsPerso ),
		dataType: 'json',
		success: function(res){
			if( res.type=='0' ){
				$('#msg-' + ctrID).html( '<div class="error">' + res.message + '</div>' );
			} else {
				auctions = $.trim( auctionsPerso ) ? auctionsPerso : res.amount;
				auctionsDirect = $.trim(auctionsPerso)!='';
				$('#auctions-' + ctrID).val( auctions );
				cancelModifyAuctions( ctrID );
			}
		}
	});
	
	return false;
}

/**
 *	Cette fonction permet d'annuler la modification d'une enchère sur un produit
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 */
function cancelModifyCatAuctions( ctrID ){
	$('.message-return').html('')
	
	var tmp = auctions.replace( '.', ',' );
	var html = '';
	
	if( auctionsDirect ){
		html = tmp + ' €';
	} else {
		html = catalogComparatorHtmlEnchere.replace('#param[tarif]#',$tmp); 
	}
	
	html += '<input type="hidden" value="' + auctions + '" id="amount-6" name="amount-6">';
	html += '<input type="hidden" name="auctions-direct-' + ctrID + '" id="auctions-direct-' + ctrID + '" value="' + ( auctionsDirect ? '1' : '0' ) + '" />';
	
	$('#td-auctions-' + ctrID).html( html );
	return false;
}

/** 
 *	Cette fonction permet de masquer la popup de choix de famille.
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 *	@param action Obligatoire, mettre save pour réaliser une sauvegarde en plus de la fermeture de la popup
 *	@param content Obligatoire, objet de l'export 'cat->catégorie de produit, 'prd'->produit
 */
function hidePopupChoose( ctrID, action, content ){
	if( action=='save' ){
		if( content=='cat' ){
			var catID = $('#prdcat').val();
			window.location.href = '/admin/catalog/edit.php?cat=' + catID + '&tab=comparators';
		}else{
			var catID = $('#cat').val();
			var prdID = $('#prd').val();
			window.location.href = '/admin/catalog/product.php?cat=' + catID + '&prd=' + prdID + '&tab=comparators';
		}
	}
	
	hidePopup();
	$('#noexp-' + ctrID).parent().find('input').removeAttr('checked');
}

/**
 *	Cette fonction permet d'afficher une popup pour choisir la famille d'export pour un produit.
 *	@param ctrID Obligatoire, identifiant d'un comparateur de prix
 *	@param prdID Obligatoire, identifiant d'un produit
 *	@param catID Obligatoire, identifiant de la famille du comparateur actuellement active
 *	@param tab Optionnel, si oui ou non on se trouve sur la fiche d'un produit > onglet comparateur
 */
function showChooseCategory( ctrID, prdID, catID, tab ){
	var url = '/admin/comparators/stats/js_product.php';
	url += '?ctr=' + ctrID + '&prd=' + prdID + '&cat=' + catID;
	if( tab )
		url += '&tab=1';
	
	displayPopup(catalogComparatorDisplayPopupChoixFamille, '', url, 'hidePopupChoose(' + ctrID + ')', 950, 550 );
	return false; 
}

/**
 *	Cette fonction ferme la popup lorsqu'on est sur une fiche produit.
 */
function hidePopupPrd( ctrID, cat_ctr, cat_id ){
	if( cat_id ){
		var prdID = $('#prd').val();
		var onclick = 'onclick="return showChooseCategory(' + ctrID + ', ' + prdID + ', 0, ' + cat_id + ', true);"';
		
		$('#cat-ctr-' + ctrID).html( cat_ctr );
		$('#cat-ctr-' + ctrID).append( '<br /><a href="?ctr=11&prd=137345&cat=30415" ' + onclick + '>' + catalogComparatorModifier + '</a>');
		
		loadTemplateMarketPlace( ctrID, cat_id, prdID );
	}
	
	$('#popup_ria').hide();
}
function editInfosCtrMarket( ctrID, prdID ){
	displayPopup(catalogComparatorDisplayPopupSurchargeInfos, '', '/admin/catalog/popup-edit-ctrmarket.php?ctr=' + ctrID + '&prd=' + prdID, '', 785, 580 );
	return false;
}
function changeInfosData( ctrID, prdID, data ){
	$('.ctr-export-info-' + ctrID + '-' + prdID).html( data );
}
function useOnlyMdlInCtr( ctrID, prdID, mdlID ){
	if( !window.confirm(catalogComparatorConfirmSuppressionSurcharge) ){
		return false;
	}
	
	$.ajax({
		url: '/admin/ajax/comparators/ajax-actions.php',
		data: 'useonlymdl=1&ctr=' + ctrID + '&prd=' + prdID + '&mdl=' + mdlID,
		dataType: 'json',
		method: 'post',
		success: function( result ){
			changeInfosData( ctrID, prdID, result.data.replace(/\\"/g, '"') )
		}
	});

	return false;
}
function showPopupEditAttr( ctrID, attrID ){
	displayPopup(catalogComparatorDisplayPopupInfoProduit, '', '/admin/comparators/popup-edit-prd-attr.php?ctr=' + ctrID + '&attr=' + attrID, 'reloadMappingAttr()',950,450 );
}
var canReloadMapping = false;
function reloadMappingAttr(){
	if( canReloadMapping ){
		window.location.reload();
	}else{
		hidePopup();
	}
}

$(document).on('change', '#list-attributs select', function(){
	var select = $(this);

	var ctr  = $('#ctr').val();
	var attr = $('#attr').val();
	var data = '&' + select.attr('name') + '=' + select.find('option:selected').val();

	$.ajax({
		url: '/admin/comparators/popup-edit-prd-attr.php?ctr=' + ctr + '&attr=' + attr, 
		data: data,
		type: 'post',
		success: function(){
			select.after('<img style="border:none;float:right;" src="/admin/images/add-search.png" />');
		},
		completed: function(){

		},
		error: function(){

		}
	});
});

$(document).on('blur', '#list-attributs input[type=text]', function(){
	var select = $(this);

	var ctr  = $('#ctr').val();
	var attr = $('#attr').val();
	var data = '&' + select.attr('name') + '=' + select.val();

	$.ajax({
		url: '/admin/comparators/popup-edit-prd-attr.php?ctr=' + ctr + '&attr=' + attr, 
		data: data,
		type: 'post',
		success: function(){
			select.after('<img style="border:none;float:right;" src="/admin/images/add-search.png" />');
		},
		completed: function(){

		},
		error: function(){

		}
	});
});