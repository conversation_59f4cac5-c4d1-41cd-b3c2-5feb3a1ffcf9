<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  31610 => 'KPN',
  31611 => 'Vodafone Libertel B.V.',
  31612 => 'KPN',
  31613 => 'KPN',
  31614 => 'T-Mobile',
  31615 => 'Vodafone Libertel B.V.',
  31616 => 'Telfort',
  31617 => 'Telfort',
  31620 => 'KPN',
  31621 => 'Vodafone Libertel B.V.',
  31622 => 'KPN',
  31623 => 'KPN',
  31624 => 'T-Mobile',
  31625 => 'Vodafone Libertel B.V.',
  31626 => 'Telfort',
  31627 => 'Vodafone Libertel B.V.',
  31629 => 'Vodafone Libertel B.V.',
  31630 => 'KPN',
  31631 => 'Vodafone Libertel B.V.',
  31633 => 'Telfort',
  31634 => 'T-Mobile',
  316351 => 'Intercity Zakelijk',
  316359 => 'ASPIDER Solutions Nederland B.V.',
  31636 => 'Tele2',
  31637 => 'Teleena (MVNE)',
  31640 => 'Tele2',
  31641 => 'T-Mobile',
  31642 => 'T-Mobile',
  31643 => 'T-Mobile',
  31644 => 'Telfort',
  31645 => 'Telfort',
  31646 => 'Vodafone Libertel B.V.',
  31647 => 'Telfort',
  31649 => 'Telfort',
  31650 => 'Vodafone Libertel B.V.',
  31651 => 'KPN',
  31652 => 'Vodafone Libertel B.V.',
  31653 => 'KPN',
  31654 => 'Vodafone Libertel B.V.',
  31655 => 'Vodafone Libertel B.V.',
  31656 => 'T-Mobile',
  31657 => 'KPN',
  31658 => 'Telfort',
  31659 => 'Vectone Mobile/Delight Mobile',
  31680 => 'Vodafone Libertel B.V.',
  31681 => 'T-Mobile',
  31683 => 'KPN',
  31684 => 'Lycamobile',
  31685 => 'Lycamobile',
  31686 => 'Lycamobile',
  31687 => 'Lycamobile',
);
