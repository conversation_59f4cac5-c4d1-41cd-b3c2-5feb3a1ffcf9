<?php
require_once( 'Oauth2.0/OauthProvider.inc.php' );

/**
 * Class Facebook
 */
class Facebook extends OauthProvider {

	/**
	 * Constante de l'identifiant du champ avancé qui représente ce provider
	 * 
	 * @var int $provider
	 */
	protected $provider = _FLD_USR_FACEBOOK;
	
	/**
	 * Identifiant du provider
	 * 
	 * @var int $provider_id
	 */
	protected $provider_id = _PVD_FACEBOOK;

	/**
	 * Nom du provider
	 * 
	 * @var string $name
	 */
	public $name = 'Facebook';

	/** Cette fonction permet configurer l'url de de redirection vers la page de login du provider
	 * @return l'url vers la page de login du provider
	 */
	public function getAuthorizeUrl(){
		return 'https://www.facebook.com/dialog/oauth?client_id='.$this->options['client_id'].'&redirect_uri='.$this->options['redirect_uri'].'&scope=email,public_profile&response_type=code&display=popup';
	}

	/**
	 * @param $code
	 *
	 * @return mixed
	 */
	public function getUserByCode( $code ){
		$token = $this->getAccessTokenFromCode( $code );

		return $this->getUserByToken( $token );
	}

	/**
	 * @param $token
	 *
	 * @return mixed
	 */
	public function getUserByToken( $token ){
		$this->options['fields'] = 'first_name,last_name,gender,email';
		$this->options['access_token'] = $token;

		return $this->request( 'GET', 'https://graph.facebook.com/me', $this->options );
	}

	/**
	 * @param $code
	 *
	 * @return mixed
	 */
	public function getAccessTokenFromCode( $code ){
		$this->options['code'] = $code;
		$response = $this->request( 'GET', 'https://graph.facebook.com/v2.4/oauth/access_token', $this->options );
		return $response['access_token'];
	}
}
