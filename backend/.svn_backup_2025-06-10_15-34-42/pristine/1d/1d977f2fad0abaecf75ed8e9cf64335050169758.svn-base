Different float syntaxes
-----
<?php

0.0;
0.;
.0;
0e0;
0E0;
0e+0;
0e-0;
30.20e10;
300.200e100;
1e10000;

// various integer -> float overflows
// (all are actually the same number, just in different representations)
18446744073709551615;
0xFFFFFFFFFFFFFFFF;
01777777777777777777777;
0177777777777777777777787;
0b1111111111111111111111111111111111111111111111111111111111111111;
-----
array(
    0: Scala<PERSON>_DNumber(
        value: 0
    )
    1: Scala<PERSON>_DNumber(
        value: 0
    )
    2: <PERSON><PERSON><PERSON>_DNumber(
        value: 0
    )
    3: Sc<PERSON><PERSON>_DNumber(
        value: 0
    )
    4: Sc<PERSON><PERSON>_DNumber(
        value: 0
    )
    5: Scala<PERSON>_DNumber(
        value: 0
    )
    6: <PERSON>alar_DNumber(
        value: 0
    )
    7: <PERSON><PERSON>r_DNumber(
        value: 302000000000
    )
    8: <PERSON><PERSON><PERSON>_DNumber(
        value: 3.002E+102
    )
    9: <PERSON><PERSON><PERSON>_DNumber(
        value: INF
    )
    10: <PERSON><PERSON><PERSON>_DNumber(
        value: 1.844674407371E+19
        comments: array(
            0: // various integer -> float overflows
            1: // (all are actually the same number, just in different representations)
        )
    )
    11: Scalar_DNumber(
        value: 1.844674407371E+19
    )
    12: Scalar_DNumber(
        value: 1.844674407371E+19
    )
    13: Scalar_DNumber(
        value: 1.844674407371E+19
    )
    14: Scalar_DNumber(
        value: 1.844674407371E+19
    )
)