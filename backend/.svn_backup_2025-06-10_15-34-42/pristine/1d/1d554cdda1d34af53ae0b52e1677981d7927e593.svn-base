<?php

// \cond onlyria

/** 
 * \defgroup api-devices-States Status des commandes A DEPLACER (VOIR AVEC SEB D) 
 * \ingroup Yuto 
 * @{		
 * \page api-devices-states-get Chargement 
 *
 * Cette fonction récupère les éléments de statut 
 *
 *	\code
 *		GET /devices/states/
 *	\endcode
 *	
 * @return json avec les colonnes :
 *		\code{.json}
 *       {
 *           "id": Identifiant de l'état de commande,
 *           "name": Libellé de l'état de commande,
 *           "name_plural": libellé de l'état de commande, au pluriel,
 *           "is_custom": détermine si l'état de commande a été personnalisé,
 *           "pos": position de l'état de commande
 *       },
 *		\endcode
 * @}
*/

switch( $method ){
	case 'get':

		$result = true;

		$rstates = ord_states_get();
		if( $rstates && ria_mysql_num_rows($rstates) ){
			while($state = ria_mysql_fetch_assoc($rstates)){
				$content[] = $state;
			}
		}

		break;
}


// \endcond