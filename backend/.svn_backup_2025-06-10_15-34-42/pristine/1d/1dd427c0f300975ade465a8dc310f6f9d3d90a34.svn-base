<?php
	if (!isset($usr)) {
		header('Location: /admin/customers/index.php');
		exit;
	}
?>
<table class="checklist">
		<caption><?php print _('Utilisations de la fonction Recommander à un ami')?></caption>
		<col width="100" /><col width="300" /><col width="150" /><col width="60" /><col width="100" /><col width="60" />
	<thead>
		<tr>
			<th><?php print _('Type')?></th>
			<th><?php print _('Message')?></th>
			<th><?php print _('Date')?></th>
			<th><?php print _('Destinataire')?></th>
			<th><?php print _('Produit')?></th>
			<th><?php print _('Publié')?></th>
		</tr>
	</thead>
	<tfoot>
		<tr><td colspan="6">
			<input type="submit" name="cancel-ord" value="<?php print _('Annuler')?>" />
		</td></tr>
	</tfoot>
	<tbody>
		<?php
		$friend = messages_get( $_GET[ 'usr' ] , array("SITE","PRODUCT"));
		if( ria_mysql_num_rows($friend) == 0 )
		{
			?>
			<tr>
				<td colspan="6"><?php print _('Aucune recommandation')?></td>
			</tr>
			<?php
		}
		else
		{
			while( $f = ria_mysql_fetch_array($friend) )
			{
			?>
				<tr>
					<td align="center"><?php $f[ 'type' ] == 'SITE' ? print _("Site") : print _("Produit") ?></td>
					<td>
						
						<?php 
							if( strlen( $f[ 'body' ] ) > 100 )
								print '<span class="tool-body" title="'. nl2br(htmlspecialchars($f[ 'body' ])) .'">'. nl2br(htmlspecialchars( substr( $f[ 'body' ], 0, 100) ) ) .'...</span>';
							else
								print nl2br(htmlspecialchars( substr( $f[ 'body' ], 0, 100) ) );
						?>
					</td>
					<td align="center"><?php print ria_date_format($f[ 'date' ]); ?></td>
					<td align="center"><?php print htmlspecialchars( $f[ 'receiver_email' ] ); ?></td>
					<td align="center">
					<?php
						if( $f[ 'type' ] == "PRODUCT" )
						{
							$prd = prd_products_get( $f[ 'prd_id' ] );
							$cat = prd_products_categories_get( $f[ 'prd_id' ] );
							$p = ria_mysql_fetch_array( $prd );
							$c = ria_mysql_fetch_array( $cat );
							print '<a href="../catalog/product.php?cat='. $c[ 'cat' ] .'&prd='. $f[ 'prd_id' ] .'">'. $p[ 'name' ] .'</a>';
						}
					?>
					</td>
					<td align="center"><?php $f[ 'publish' ] ? print _("Oui") : print _("Non") ; ?></td>
				</tr>
			<?php
			}
		}
		?>
	</tbody>
</table>