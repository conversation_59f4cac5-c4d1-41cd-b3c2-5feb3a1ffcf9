<?php

	/**	\file birthday.php
	 *
	 * 	Cette page permet la configuration des campagnes Anniversaire
	 *
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_CAMPAGNE_BIRTHDAY');

	//---------- Nouvel configuration ----------

	//  Json Exemple : dob_promo_config = {"days":"1234","min-ttc":"10","type":"1","config":{"code":{"value":"200","type":"0","on":"min-line"},"srv":["-1","274","275","286","129","128"]}}

	if(isset($_POST["save-dob"])){
		$error = false;

		if(isset($_POST["active-bod"]) && $_POST["active-bod"] == 1){

			cfg_overrides_set_value("dob_promo_activated",1,$config["wst_id"]);

			$arrConfig = array();
			$arrConfig["days"] = (isset($_POST["remise-day-valid"]) && is_numeric($_POST["remise-day-valid"]) ? $_POST["remise-day-valid"] : 0);
			$arrConfig["min-ttc"] = (isset($_POST["min-ttc"]) && is_numeric($_POST["min-ttc"]) ? $_POST["min-ttc"] : 0);

			if(isset($_POST["sp-system"])){
				if($_POST["sp-system"] == _PMT_TYPE_CODE ){

					$arrConfig["type"] = $_POST["sp-system"];

					$cfg = array();

					if(isset($_POST["discount"]) && is_numeric($_POST["discount"]) && isset($_POST["discount-type"]) && isset($_POST["remise-on"])){
						$cfg["code"]["value"] = $_POST["discount"];
						$cfg["code"]["type"] = $_POST["discount-type"];
						$cfg["code"]["on"] = $_POST["remise-on"];
					}

					if(isset($_POST["chk_srv"]) && count($_POST["chk_srv"]) > 0){

						$cfg["srv"] = $_POST["chk_srv"];
					}
					// else{
					// 	$error = "Veuillez choisir un système de livraison";
					// }

					if(!$error || $error == ""){
						$arrConfig["config"] = $cfg;
					}

					$json = json_encode($arrConfig);
					cfg_overrides_set_value("dob_promo_config",$json,$config["wst_id"]);

				}else if($_POST["sp-system"] == _PMT_TYPE_PRD ){

					$arrConfig["type"] = $_POST["sp-system"];

					$cfg = array();

					if(isset($_POST["prd-offer-id"]) && is_numeric($_POST["prd-offer-id"]) && isset($_POST["prd-offer-ref"]) && $_POST["prd-offer-ref"] != "" && isset($_POST["prd-offer-ref"]) && $_POST["prd-offer-ref"] != "") {

						$cfg["product"]["id"] = $_POST["prd-offer-id"];
						$cfg["product"]["ref"] = $_POST["prd-offer-ref"];
						$cfg["product"]["name"] = $_POST["prd-offer-name"];

					}else{
						$error = _("Veuillez choisir un produit à inclure dans la promotion");
					}

					if(isset($_POST["chk_srv"]) && count($_POST["chk_srv"]) > 0){

						$cfg["srv"] = $_POST["chk_srv"];
					}

					if(!$error || $error == ""){
						$arrConfig["config"] = $cfg;
					}

					$json =json_encode($arrConfig);
					cfg_overrides_set_value("dob_promo_config",$json,$config["wst_id"]);


				}else if($_POST["sp-system"] == "port" ){

					$arrConfig["type"] = $_POST["sp-system"];

					$cfg = array();

					$cfg["code"]["value"] = 0;
					$cfg["code"]["type"] = 0;
					$cfg["code"]["on"] = null;

					if(isset($_POST["chk_srv"]) && count($_POST["chk_srv"]) > 0){
						$cfg["srv"] = $_POST["chk_srv"];
					}else{
						$error = _("Veuillez choisir un système de livraison");
					}

					if(!$error || $error == ""){
						$arrConfig["config"] = $cfg;
					}

					$json = json_encode($arrConfig);
					cfg_overrides_set_value("dob_promo_config",$json,$config["wst_id"]);

				}
			}
		}else if($_POST["active-bod"] == 0){
			//Supprimer les variables
			cfg_overrides_del_value("dob_promo_activated",$config["wst_id"]);
			cfg_overrides_del_value("dob_promo_config",$config["wst_id"]);
		}

		if( !isset($error) || !$error){
			$_SESSION["success_dob_reward"] = 1;
			header('Location: /admin/config/campagnes/birthday.php');
			exit;
		}
	}

	//---------- Charger la configuration actuel ----------

	$jsonConfig = cfg_overrides_get_value("dob_promo_config",$config["wst_id"]);
	$promoActivated = cfg_overrides_get_value("dob_promo_activated",$config["wst_id"]);

	if($promoActivated && $jsonConfig){
		$currentConfig = json_decode($jsonConfig,true);
	}else{
		$currentConfig = array("days"=>0,"min-ttc"=>0,"type"=>_PMT_TYPE_CODE,"config"=>false);
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Configuration'), '/admin/config/index.php' )
		->push( _('Campagnes'), '/admin/config/campagnes/index.php' )
		->push( _('Anniversaire') );

	require_once('promotions.inc.php');
	define('ADMIN_PAGE_TITLE', _('Anniversaire') . ' - ' . _('Campagnes') . ' - ' . _('Configuration') );
	require_once('admin/skin/header.inc.php');

?>

<h2><?php print _('Anniversaire'); ?></h2>
<div class="notice">
	<?php print _('Permet de faire bénéficier un utilisateur d\'une offre particulière lors de son anniversaire. Trois types d\'offres sont disponibles :'); ?>
	<ul>
		<li><?php print _('Code promotion'); ?></li>
		<li><?php print _('Produit offert'); ?></li>
		<li><?php print _('Frais de port offerts'); ?></li>
	</ul>
</div>
<?php
	if( isset($error) && $error){
		print '<div class="error">'.htmlspecialchars( $error ).'</div>';
	}
?>
<?php
	if( isset($_SESSION["success_dob_reward"]) && $_SESSION["success_dob_reward"] == 1){
		print '<div class="success"> '._('Votre configuration a bien été prise en compte').' </div>';
		unset($_SESSION["success_dob_reward"]);
	}
?>
<?php if( !$promoActivated ){ ?>
<style>
	.details{ display: none; }
</style>
<?php } ?>
<form method="post">
	<table id="table-dob" class="table-dob">
		<tbody>
			<tr>
				<th colspan="2"><?php print _('Général'); ?></th>
			</tr>
			<tr>
				<td class="col230px">
					<label for="active-sponsor-y"><span class="mandatory">*</span> <?php print _('Activer ce système :'); ?></label>
				</td>
				<td>
					<input class="active-radio" <?php echo ( $promoActivated == 1 ? "checked='checked'" : "" ) ?>  type="radio" name="active-bod" id="active-bod-y" value="1" />
					<label for="active-bod-y"><?php print _('Oui'); ?></label>
					<input class="active-radio" <?php echo ( !$promoActivated ? "checked='checked'" : "" ) ?> type="radio" name="active-bod" id="active-bod-n" value="0" />
					<label for="active-bod-n"><?php print _('Non'); ?></label>
				</td>
			</tr>
			<tr class="details">
				<td>
					<label for="sp-system"><span class="mandatory">*</span> <?php print _('Type :'); ?></label>
				</td>
				<td>
					<select name="sp-system" id="sp-system">
					<option <?php echo (isset($currentConfig['type']) && $currentConfig['type'] == _PMT_TYPE_CODE ? 'selected="selected"' : '') ?> value="<?php echo _PMT_TYPE_CODE ?>"><?php print _('Code promotion'); ?></option>
					<option <?php echo (isset($currentConfig['type']) && $currentConfig['type'] == _PMT_TYPE_PRD  ? 'selected="selected"' : '') ?> value="<?php echo _PMT_TYPE_PRD ?>"><?php print _('Produit offert'); ?></option>
					<option <?php echo (isset($currentConfig['type']) && $currentConfig['type'] == "port" ? 'selected="selected"' : '') ?> value="port"><?php print _('Frais de port offert'); ?></option>
				</select>
				</td>
			</tr>
			<tr class="details">
				<td>
					<span class="mandatory">*</span>
					<label for="remise-day-valid"><?php print _('Valable pendant :'); ?></label>
				</td>
				<td>
					<input type="text" name="remise-day-valid" id="remise-day-valid" class="price" maxlength="8" value="<?php echo (isset($currentConfig['days']) ? $currentConfig['days'] : '') ?>" />&nbsp; <?php print _('jours(s)'); ?>
				</td>
			</tr>
			<tr class="details">
				<td>
					<label title="<?php print _('Montant TTC minimal de la commande pour bénéficier de l’offre'); ?>" for="remise-day-valid"><?php print _('Montant minimum de commande :'); ?></label>
				</td>
				<td>
					<input type="text" name="min-ttc" id="min-ttc" class="price" maxlength="8" value="<?php echo (isset($currentConfig['min-ttc']) ? $currentConfig['min-ttc'] : '') ?>" />&nbsp; <?php print _('€ (TTC)'); ?>
				</td>
			</tr>
		<?php

			if(isset($currentConfig['config'])){

				$cfg = $currentConfig['config'];
			}

		 ?>
			<tr class="details">
				<th colspan="2"><?php print _('Bénéfices apportés par l\'offre'); ?></th>
			</tr>
			<tr class="details">
				<td <?php echo ($currentConfig["type"] == _PMT_TYPE_CODE ? '' : 'class="none"'); ?> id="bod-code-offers"  colspan="2">
					<label for="discount"><?php print _('Appliquer une réduction de'); ?></label>
					<input value="<?php echo (isset($cfg['code']['value']) ? $cfg['code']['value'] : 0) ?>" maxlength="9" class="price" id="discount" name="discount" type="text">
					<select name="discount-type" id="select-discount-type">
						<option <?php echo (isset($cfg['code']['type']) && $cfg['code']['type'] == 1 ? 'selected="selected"' : '') ?> value="1">%</option>
						<option <?php echo (isset($cfg['code']['type']) && $cfg['code']['type'] == 0  ? 'selected="selected"' : '') ?> value="0"><?php print _('€ HT'); ?></option>
						<option <?php echo (isset($cfg['code']['type']) && $cfg['code']['type'] == 2  ? 'selected="selected"' : '') ?> value="2"><?php print _('Nouveau tarif'); ?></option>
					</select>
					<div class="pmt-list-discount">
						<input <?php echo ((isset($cfg['code']['on']) && $cfg['code']['on'] == "order") || !isset($cfg['code']['on'])  ? 'checked="checked"' : '') ?> name="remise-on" id="remise-on-order" value="order" type="radio">
						<label for="remise-on-discount"><?php print _('Sur toute la commande'); ?></label>
					</div><div class="pmt-list-discount">
						<input <?php echo (isset($cfg['code']['on']) && $cfg['code']['on'] == "min-prd" ? 'checked="checked"' : '') ?> name="remise-on" id="remise-on-min-prd" value="min-prd" type="radio">
						<label for="remise-on-min-prd"><?php print _('Sur le produit le moins cher'); ?></label>
					</div><div class="pmt-list-discount">
						<input <?php echo (isset($cfg['code']['on']) && $cfg['code']['on'] == "max-prd" ? 'checked="checked"' : '') ?> name="remise-on" id="remise-on-max-prd" value="max-prd" type="radio">
						<label for="remise-on-max-prd"><?php print _('Sur le produit le plus cher'); ?></label>
					</div><div class="pmt-list-discount">
						<input <?php echo (isset($cfg['code']['on']) && $cfg['code']['on'] == "min-line" ? 'checked="checked"' : '') ?> name="remise-on" id="remise-on-min-line" value="min-line" type="radio">
						<label for="remise-on-min-line"><?php print _('Sur la ligne de commande la moins chère'); ?></label>
					</div><div class="pmt-list-discount">
						<input <?php echo (isset($cfg['code']['on']) && $cfg['code']['on'] == "max-line" ? 'checked="checked"' : '') ?> name="remise-on" id="remise-on-max-line" value="max-line" type="radio">
						<label for="remise-on-max-line"><?php print _('Sur la ligne de commande la plus chère'); ?></label>
					</div>
				</td>
			</tr>
			<tr>
				<td <?php echo ($currentConfig["type"] == _PMT_TYPE_PRD ? '' : 'class="none"') ?> id="bod-prd-offers" colspan="2">

					<input name="prd-offer-id" id="prd-offer-id" value="<?php echo (isset($cfg['product']['id']) ? $cfg['product']['id'] : '') ?>" type="hidden">
					<input name="prd-offer-name" id="prd-offer-name" value="<?php echo (isset($cfg['product']['name']) ? $cfg['product']['name'] : '') ?>" type="hidden">
					<input name="prd-offer-ref" id="prd-offer-ref" value="<?php echo (isset($cfg['product']['ref']) ? $cfg['product']['ref'] : '') ?>" type="hidden">

					<fieldset>
						<legend><?php print _('Produit offert'); ?></legend>
						<p><?php print _('Ajouter un produit :'); ?></p>
						<div>
							<label for="pop-ref"><?php print _('Référence :'); ?></label>
							<input class="text" name="pop-ref" id="pop-ref" value="<?php echo (isset($cfg['product']['ref']) ? $cfg['product']['ref'] : '') ?>" type="text">
							<input value="Choisir" class="button" id="pop-ref-select" name="pmt-ref-select" type="button">
						</div>
					</fieldset>
				</td>
			</tr>
			<tr class="srv-zone details">
				<td colspan="2" class="pmt-off-srv"><?php print _('Frais de ports offerts pour'); ?> <span class="<?php echo ($currentConfig["type"] == "port" ? '' : 'none ') ?>port-required mandatory">*</span> : <a href="#" class="check-all"><?php print _('Cocher tout'); ?></a> | <a href="#" class="uncheck-all"><?php print _('Décocher tout'); ?></a></td>
			</tr>
			<tr class="details">
				<td colspan="2" class="pmt-list-choose">
					<ul class="pmt-list-service">
					<?php
						if (isset($cfg["srv"]) && count($cfg["srv"]) > 0) {
							$cod_service = $cfg["srv"];
						}else{
							$cod_service = array();
						}

						$rsrv = dlv_services_get( 0, true );

						print '	<li>
									<label><input '.( in_array(-1, $cod_service) ? 'checked="checked"' : '' ).' type="checkbox" name="chk_srv[]" value="-1" /> '._('Livraison en magasin (si activé)').'</label>
								</li>';

						if( $rsrv && ria_mysql_num_rows($rsrv) ){
							while( $srv = ria_mysql_fetch_array($rsrv) ){
								print '	<li>
											<label>
												<input '.( in_array($srv['id'], $cod_service) ? 'checked="checked"' : '' ).' type="checkbox" name="chk_srv[]" id="chk_srv_'.$srv['id'].'" value="'.$srv['id'].'" />
												'.htmlspecialchars($srv['name']).'
											</label>
										</li>';
							}
						}
					?></ul>
				</td>
			</tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" value="<?php print _('Enregistrer'); ?>" name="save-dob" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>
<script><!--
$(document).ready( function(){

	if($("#sp-system option:selected").data("special") == "port"){
		$("#bod-code-offers").hide();
		$("#bod-prd-offers").hide();
	}

	$("#sp-system").change(function(e){

		var code = <?php echo _PMT_TYPE_CODE ?>;
		var prd = <?php echo _PMT_TYPE_PRD ?>;
		var port = "port";

		if($(this).val() == code){
			$("#bod-code-offers").show();
			$("#bod-prd-offers").hide();
			$(".port-required").hide();
		}else if($(this).val() == prd){
			$("#bod-prd-offers").show();
			$("#bod-code-offers").hide();
			$(".port-required").hide();
		}else if($(this).val() == port){
			$(".port-required").show();
			$("#bod-code-offers").hide();
			$("#bod-prd-offers").hide();
		}
	});

	$(".active-radio").change(function(e){
		if($(this).val() === 0 ){
			$(".details").toggle("hide");
		}else{
			$(".details").toggle("show");
		}
		return false;
	});

	$(".check-all").click(function(){
		$('.pmt-list-service input').attr('checked', 'checked');
		return false;
	});
	$(".uncheck-all").click(function(){
		$('.pmt-list-service input').removeAttr('checked');
		return false;
	});

});

/**
 *	Cette fonction permet de sélectionner le produit offert.
 */
function parent_select_prd( id, name, ref, cat, input_id, input_name, input_ref ){
	currentCat = cat;
	if( $.trim(input_id)!='' ){
		$('#' + input_id).val(id);
		$('#' + input_name).val(name);
		$('#' + input_ref).val(ref);
		$('#pop-ref').val( ref );
	}else{
		return chooseElemProducts( id, ref + ' - ' + name, 'pmt-prd-name' );
	}
}

/**
 *	Cette fonction permet d'ajouter un produit
 */
function addPrdOffers(){
	$('.error').remove();

	var prdID = $('#prd-offer-id').val();
	var prdRef = $('#prd-offer-ref').val();
	var prdName = $('#prd-offer-name').val();

	if( $.trim(prdID)=='' || $.trim(prdRef)=='' || $.trim(prdName)=='' ){
		var searchRef = $('#pop-ref').val();

		if( $.trim($(searchRef)!='') ){
			$.ajax({
				url: '/admin/ajax/promotions/ajax-pmt-specials.php',
				data: 'getinforef=1&ref=' + searchRef,
				type: 'get',
				dataType: 'json',
				async: false,
				success: function( infoPrd ){
					if( $.trim(infoPrd.ref)!='' ){
						prdID = infoPrd.id;
						prdRef = infoPrd.ref;
						prdName = infoPrd.name;
					}
				}
			});
		}
	}

	if( $.trim(prdID)!='' && $.trim(prdRef)!='' && $.trim(prdName)!='' ){
		if( !$('#prd-pop-' + prdID).length ){
			var html = ''
				+	'<div id="prd-pop-' + prdID + '" class="prd-pop">'
				+		'<label for="pop-ref-' + prdID + '"><?php print _('Référence'); ?></label>'
				+		'<input class="text ref" type="text" name="list-pop-ref[]" value="' + addslashes( prdRef ) + '" />'
				+		'<label for="pop-qty-' + prdID + '"><?php print _('Quantité offerte'); ?></label>'
				+		'<input class="text qty" type="text" name="list-pop-qty[]" value="1" />'
				+		'<input type="button" value="x" name="del-pop" class="del-pop input-icon-del" />'
				+	'</div>';

			$('#list-prd-offers .no-pop').remove();
			$('#list-prd-offers').append( html );
		}

		$('#pop-ref').val('');
		$('#prd-offer-id').val('');
		$('#prd-offer-ref').val('');
		$('#prd-offer-name').val('');
	}else if( $.trim(searchRef)!='' ){
		$('<div class="error"></div>')
			.appendTo('#pmt-list-prd-offers fieldset')
			.text(
				<?php print json_encode(_('La référence "#param[variable]" ne fait plus ou pas partie de vos références.')); ?>
					.replace('#param[variable]',addslashes( searchRef ))
			);
	}

	return false;
}

$(document).delegate(
	'#pop-ref-select', 'click', function(){
		displayPopup( '<?php print _('Rechercher un produit'); ?>', '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0&input_id_prd_id=prd-offer-id&input_id_name=prd-offer-name&input_id_ref=prd-offer-ref' );
	}
).delegate(
	'.del-pop', 'click', function(){
		$(this).parent().remove();

		if( !$('#list-prd-offers div').length ){
			$('#list-prd-offers').html( '<div class="no-pop prd-pop"><?php print _('Aucun produit offert pour le moment'); ?></div>' );
		}

		return false;
	}
);
//-->
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>