/**	\file tools/alerts.js
 *	Ce fichier est destiné à gérer le rechargement Ajax des alertes de disponibilité.
 *	Il est actuellement appelé ici :
 *	- /tools/livr-alerts/index.php
 */
$(document).ready(function() {
	
	loadDatePicker();

	// Active le tri sur le tableau des Alertes de disponibilité
	if (typeof tablesorter != undefined && $('#tb-alert-dispo').length) {
		$('#tb-alert-dispo').tablesorter({
			headers: {
				6: { sorter: 'shortDate' }
			}
		});
	}
});

/** Change la page de données affichée dans le tableau des alertes de disponibilité
 * @param {int} page Page en cours de consultation 
 * @param {int} pages Nombre de pages
 */
function alertSwitchTablePage( page, pages ){
	// Place le scroll en haut à gauche de l'écran
	swichPageLoad(6);
	
	// Requête AJAX pour le changement de page
	$.ajax({
		type: "GET",
		url: '/admin/ajax/delivery-alerts/json-delivery-alerts.php',
		data: 'p='+page+'&limit=50',
		dataType: 'json',
		async:true,
		success: function(msg){
			var html = '';
			
			// Créé le contenu du tableau
			for( var i=0 ; i<msg.length ; i++ ){
				html += '	<tr>';
				html += '		<td headers="sel"><input type="checkbox" class="checkbox" name="sub[]" value="'+msg[i].id+'" /></td>';
				html += '		<td headers="prd-ref"><a href="/admin/catalog/product.php?cat=0&amp;prd='+msg[i].id+'">'+htmlspecialchars(msg[i].ref)+'</a></td>';
				html += '		<td headers="prd-name">'+htmlspecialchars(msg[i].name)+'</td>';
				html += '		<td headers="brd-name">'+htmlspecialchars(msg[i].brdName)+'</td>';
				html += '		<td headers="email"><a href="mailto:'+htmlspecialchars(msg[i].usr_email)+'">'+htmlspecialchars(msg[i].usr_email)+'</a></td>';
				html += '		<td headers="account">'+( msg[i].usr_id ? '<a href="../../customers/edit.php?usr='+msg[i].usr_id+'">Oui</a>' : 'Non' )+'</td>';
				html += '		<td headers="date">'+msg[i].date_created+'</td>';
				html += '	</tr>';
			}
			
			// Affiche le contenu
			$("#site-content table tbody").html(html);
			
			// Créé la pagination
			$("#pagination").html(switchPage(page, pages, 3, 3, 2, 5, 'alertSwitchTablePage', '', 'index.php', ''));
		},
		error: function(){
			return true;
		}
	});
	return false;
}

// fonction pour le sélecteur de dates
function loadDatePicker(){
	
	// Parcours tous les champs de type date
	$('input.datepicker').each(function(){
		var temp = this ;
		
		// Implémente le sélecteur de date sur chacun d'entre eux.
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					$(temp).DatePickerHide();
				}
				if( $('#date_fin').length > 0 && $(temp).attr('name') == 'date_deb' ){
					$('#date_fin').val( $('#date_deb').val() );
				}
			}
		});
		
	});	
}