<?php

	/**	\file step.php
	 *	Ce fichier fait partie d'un ensemble destiné à la gestion des cycles, développement spécifique réalisé pour la société Boero.
	 *	Merci de ne pas traduire ces fichiers / ne pas inclure les fonctions de traduction, car nous n'en aurons pas l'utilité.
	 */

	require_once('boero.cycles.inc.php');
	
	unset( $error );
	
	if( !isset($_GET['prd']) )
		$_GET['prd'] = 0;
	if( !isset($_GET['pos']) )
		$_GET['pos'] = 0;
		
	if( isset($_POST['del']) ){
		if( boero_cycle_products_del( $_GET['ccl'], $_GET['pos'] ) ){
			$success = 'La suppression de l\'étape à réussi';
			
			
			$d=boero_cycle_products_get($_GET['ccl']);
			while($e = ria_mysql_fetch_array($d) ){
				if($e['pos'] > $_GET['pos']) {
					$pos = $e['pos']-1;
					boero_cycle_products_update($e['ccl_id'] ,$e['id'],$e['pos'], $e['layers'], $e['mode'],$pos);
				}
			}
		}else{
			$error = "Une erreur inattendue s'est produite lors de la suppression de l'étape";
		}
	}
	
	if( isset($_POST['ref']) && !isset($_POST['del'])){
		if( !trim($_POST['ref']) ){
			$error = "Veuillez indiquer la référence du produit";
		}else{
			$rproduct = prd_products_get( 0, $_POST['ref'] );
			if( !ria_mysql_num_rows($rproduct) ){
				$error = "La référence demandée n'a pas été trouvée";
			/*}elseif( !isset($_POST['layers']) || !is_numeric($_POST['layers']) ){
				$error = "Veuillez indiquer le nombre de couches";*/
			}else{
				if( !isset($_POST['layers']) ) $_POST['layers'] = '';
				$product = ria_mysql_fetch_array($rproduct);
				
				if( $_GET['pos']>0 ){
					if( !boero_cycle_products_update( $_GET['ccl'], $product['id'], $_GET['pos'],$_POST['layers'], $_POST['mode'] ) ){
						$error = "Une erreur inattendue s'est produite lors de la mise à jour du produit";
					}else{
						$success = "L'étape a été mise à jour avec succès";
					}
				}else{
					if( !boero_cycle_products_add( $_GET['ccl'], $product['id'], $_POST['layers'], $_POST['mode'] ) ){
						$error = "Une erreur inattendue s'est produite lors de l'ajout du produit";
					}else{
						$success = "L'étape a été ajoutée avec succès";
					}
				}
			}
		}
	}

	$tab = array("ref"=>'',"layers"=>'','mode'=>'25');
	if( $_GET['pos']>0 ){
		$rprd = boero_cycle_products_get($_GET['ccl'],$_GET['prd'],$_GET['pos']);
		if( ria_mysql_num_rows($rprd ) ){
			$l = ria_mysql_fetch_array($rprd);
			$tab = array( 'ref'=>$l['ref'],"layers"=>$l['layers'], 'mode'=>$l['mode'] );
		}
	}
	if( isset($_POST['mode']) ){
		$tab['mode'] = $_POST['mode'];
	}
	if( isset($_POST['layers']) ){
		$tab['layers'] = $_POST['layers'];
	}
	if( isset($_POST['ref']) ){
		$tab['ref'] = $_POST['ref'];
	}	

	define('ADMIN_PAGE_TITLE', 'Etape de cycle - Cycles - ' . _('Configuration'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
	
	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}elseif( isset($success) ){
		print '<div class="error-success">'.nl2br(htmlspecialchars($success)).'</div>';
		echo '<script>', "\n";
		echo 'window.parent.location.reload();', "\n";
		echo '</script>';
	}
?>
<form action="step.php?ccl=<?php print $_GET['ccl'];?>&amp;prd=<?php print $_GET['prd']; ?>&amp;pos=<?php print $_GET['pos']; ?>" method="post">
<table cellpadding="0" cellspacing="0">
	<caption><?php print 'Propriétés de l\'étape'; ?></caption>
<tfoot>
	<tr>
		<td colspan="3">
			<?php if( $_GET['pos'] ){ ?>
				<input type="submit" value="<?php print _('Mettre à jour'); ?>" name="upd" />
			<?php }else{ ?>
				<input type="submit" value="<?php print _('Ajouter'); ?>" />
			<?php } ?>
			<input type="button" value="<?php print _('Annuler'); ?>" onclick="window.parent.Shadowbox.close();" />
			<?php if( $_GET['pos'] ){ ?>
				<input type="submit" value="<?php print _('Supprimer'); ?>" name="del" />
			<?php } ?>
		</td>
	</tr>
</tfoot>
<tbody>
	<tr>
		<td><label for="ref">Référence du produit <span class="mandatory">*</span> :</label></td>
		<td><input type="text" name="ref" id="ref" class="code" maxlength="16" value="<?php print $tab['ref']; ?>" /></td>
	</tr>
	<tr>
		<td><label for="layers">Nombre de couches :</label></td>
		<td><input type="text" name="layers" id="layers" class="number" maxlength="6" value="<?php print $tab['layers']; ?>" /></td>
	</tr>
	
    <tr>
    	<td><label for="mode"><?php print 'Mode d\'application'; ?> <span class="mandatory">*</span>:</label></td>
        <td><select name="mode" id="mode">
        	<option value="25"><?php print 'Mode d\'application 1'; ?></option>
            <option value="35" <?php if( $tab['mode'] !=25) print 'selected="selected"' ?>><?php print 'Mode d\'application 2'; ?></option>
        </select></td>
    </tr>
	
</tbody>
</table>
</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>