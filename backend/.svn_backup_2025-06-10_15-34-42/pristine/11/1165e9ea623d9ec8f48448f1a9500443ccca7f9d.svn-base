<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/distribution.proto

namespace Google\Api\Servicecontrol\V1\Distribution;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Describing buckets with constant width.
 *
 * Generated from protobuf message <code>google.api.servicecontrol.v1.Distribution.LinearBuckets</code>
 */
class LinearBuckets extends \Google\Protobuf\Internal\Message
{
    /**
     * The number of finite buckets. With the underflow and overflow buckets,
     * the total number of buckets is `num_finite_buckets` + 2.
     * See comments on `bucket_options` for details.
     *
     * Generated from protobuf field <code>int32 num_finite_buckets = 1;</code>
     */
    private $num_finite_buckets = 0;
    /**
     * The i'th linear bucket covers the interval
     *   [offset + (i-1) * width, offset + i * width)
     * where i ranges from 1 to num_finite_buckets, inclusive.
     * Must be strictly positive.
     *
     * Generated from protobuf field <code>double width = 2;</code>
     */
    private $width = 0.0;
    /**
     * The i'th linear bucket covers the interval
     *   [offset + (i-1) * width, offset + i * width)
     * where i ranges from 1 to num_finite_buckets, inclusive.
     *
     * Generated from protobuf field <code>double offset = 3;</code>
     */
    private $offset = 0.0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $num_finite_buckets
     *           The number of finite buckets. With the underflow and overflow buckets,
     *           the total number of buckets is `num_finite_buckets` + 2.
     *           See comments on `bucket_options` for details.
     *     @type float $width
     *           The i'th linear bucket covers the interval
     *             [offset + (i-1) * width, offset + i * width)
     *           where i ranges from 1 to num_finite_buckets, inclusive.
     *           Must be strictly positive.
     *     @type float $offset
     *           The i'th linear bucket covers the interval
     *             [offset + (i-1) * width, offset + i * width)
     *           where i ranges from 1 to num_finite_buckets, inclusive.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Servicecontrol\V1\Distribution::initOnce();
        parent::__construct($data);
    }

    /**
     * The number of finite buckets. With the underflow and overflow buckets,
     * the total number of buckets is `num_finite_buckets` + 2.
     * See comments on `bucket_options` for details.
     *
     * Generated from protobuf field <code>int32 num_finite_buckets = 1;</code>
     * @return int
     */
    public function getNumFiniteBuckets()
    {
        return $this->num_finite_buckets;
    }

    /**
     * The number of finite buckets. With the underflow and overflow buckets,
     * the total number of buckets is `num_finite_buckets` + 2.
     * See comments on `bucket_options` for details.
     *
     * Generated from protobuf field <code>int32 num_finite_buckets = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setNumFiniteBuckets($var)
    {
        GPBUtil::checkInt32($var);
        $this->num_finite_buckets = $var;

        return $this;
    }

    /**
     * The i'th linear bucket covers the interval
     *   [offset + (i-1) * width, offset + i * width)
     * where i ranges from 1 to num_finite_buckets, inclusive.
     * Must be strictly positive.
     *
     * Generated from protobuf field <code>double width = 2;</code>
     * @return float
     */
    public function getWidth()
    {
        return $this->width;
    }

    /**
     * The i'th linear bucket covers the interval
     *   [offset + (i-1) * width, offset + i * width)
     * where i ranges from 1 to num_finite_buckets, inclusive.
     * Must be strictly positive.
     *
     * Generated from protobuf field <code>double width = 2;</code>
     * @param float $var
     * @return $this
     */
    public function setWidth($var)
    {
        GPBUtil::checkDouble($var);
        $this->width = $var;

        return $this;
    }

    /**
     * The i'th linear bucket covers the interval
     *   [offset + (i-1) * width, offset + i * width)
     * where i ranges from 1 to num_finite_buckets, inclusive.
     *
     * Generated from protobuf field <code>double offset = 3;</code>
     * @return float
     */
    public function getOffset()
    {
        return $this->offset;
    }

    /**
     * The i'th linear bucket covers the interval
     *   [offset + (i-1) * width, offset + i * width)
     * where i ranges from 1 to num_finite_buckets, inclusive.
     *
     * Generated from protobuf field <code>double offset = 3;</code>
     * @param float $var
     * @return $this
     */
    public function setOffset($var)
    {
        GPBUtil::checkDouble($var);
        $this->offset = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(LinearBuckets::class, \Google\Api\Servicecontrol\V1\Distribution_LinearBuckets::class);

