<?php
    /** \file import-data-harmonia.php
	 *
	 * 	Ce script est destiné à importer les articles ajoutés ou modifier d'Harmonia.
     * 
     *  Pour ne pas importer les articles, il faut mettre le paramètre no_prd à true
     *  Pour ne pas importer les clients, il faut mettre le paramètre no_usr à true
     *  Pour ne pas importer les modèles de commandes, il faut mettre le paramètre no_ord à true
     * 
     *  Ce script peut être appelé avec les paramètres suivants : 
     *      - no_prd : pour ne pas réaliser la synchro des produit / stock produit
     *      - no_usr : pour ne pas réaliser la synchro des comptes clients
     *      - no_ord : pour ne pas réaliser la synchro des commandes
     *      - no_model_ord : pour ne pas réaliser la synchro des modèles de commandes
     *      - no_cod_list : pour ne pas réaliser la synchro des codes fld
	 */

    set_include_path(dirname(__FILE__) . '/../include/');
    require_once("imports.inc.php");
    require_once("define.inc.php");


    //TODO: classer les articles dans des catégories ( catalogue > genre )


    // Lors de l'éxécution de l'import le fichier est de nouveau téléchargé à partir du ftp les modifications sur celui-ci ne sont pas prise en compte
    // Il faut donc créer un import de type file pour pouvoir sauvegarder l'ajout de l'entête au fichier.

    foreach( $configs as $config ){
        $owner_id = 0;

        // Information de connexion au ftp
        $url = "83.167.144.164";
        $login = "yuto";
        $password = "Y-F2019_!a!";

        $col_sep = ';';
        $text_separator = '"';

        // Le paramètre "no_prd" permet d'ignorer la synchro des produits / stocks
        if( !isset($ar_params['no_prd']) || !$ar_params['no_prd'] ){
            $error_prd = false;
            $error_stock = false;

            // Import des articles
            {
                $filename = "YUTO-SYNC/X3toYUTO/articles.txt";
                $name = "YUTO-SYNC/X3toYUTO/articles.txt";
                $ext = pathinfo( $filename, PATHINFO_EXTENSION );

                try{
                    $file = new ImportFile( $filename, 'ftp', $name );

                    $file->connectToFtp($url, $login, $password );
                    
                    $file->saveFile();

                    // Ajoute un entête au fichier
                    {
                        $handle = $file->openFile();
                        $file_data = "REFERENCE HML;TITRE;PUBLIE;SOMMEIL;SOUS-TITRE;SUPPORT;EAN13;GENRE;EDITEUR;COLLECTION;PARUTION;DISPONIBILITE;PP TTC;TX TVA;PP HT;INTERVENANTS;STOCK MAS;STOCK CPT;CATALOGUE;LONGUEUR;LARGEUR;EPAISSEUR;POIDS;NB-PAGES;infos\n";
                        $file_data .= $file->getContent();
                        file_put_contents($file->localFilePath, $file_data);

                    }

                    // Ajoute une colonne pour la catégorie de l'import et créer le fichier d'import de stock
                    {
                        $temp = file($file->localFilePath);

                        $first_line = true;
                        foreach($temp as $key => $value){

                            $value = str_replace("\n", "", $value);
                            $value = str_replace("\r", "", $value);
                            
                            $array_value = explode ( ";" , $value );

                            // Ajoute les colonnes catégorie, référence, poids, prix ttc et poids
                            if( $first_line ){
                                $temp[$key] = $value.";categorie;reference;FLD-TVA;FLD-PP-TTC;FLD-POIDS;FLD-INTERVENANTS\r\n";

                            }else{
                                
                                $category = '';
                                
                                if( $array_value[18] === '"F"' ){
                                    $category = 235640;
                                }elseif( $array_value[18] === '"G"' ){
                                    $category = 235641;
                                }elseif( $array_value[18] === '"N"' ){
                                    $category = 235643;
                                }

                                // Modifie la tva pour qu'elle soit en %
                                $array_value[13] = str_replace('"', '', $array_value[13])*100;

                                // Complete le fichier
                                $temp[$key] = implode( ';', $array_value).";".$category.";".$array_value[6].";".$array_value[13].";".$array_value[12].";".$array_value[22].";".$array_value[15]."\r\n";
                            }
                                
                            $first_line = false;
                        }


                        $fp = fopen($file->localFilePath, 'w');
                        foreach($temp as $key => $value){
                            fwrite($fp, $value);
                        }
                        fclose($fp);
                    }

                    $file->readFile( $col_sep, $text_separator );

                    if( !$file->checkColsAndLines() ){
                        error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                        $error_prd = true;
                    }
                }
                catch(Exception $e){
                    error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                    $error_prd = true;
                }


                // Création de l'import des articles
                if( !$error_prd ){
                    if( !$imp_id = ipt_imports_add(
                        $ext,
                        $file->original_filename,
                        $file->lines_count,
                        $file->getColumnsCount(),
                        $owner_id,
                        CLS_PRODUCT,
                        'create',
                        'add/upd',
                        $file->localFilePath,
                        $file->getCharset(),
                        $col_sep,
                        $text_separator,
                        $file->getSize(),
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        false,
                        true
                    )
                    ){
                        unlink( $file->localFilePath );
                        error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import d\'article.');
                        $error_prd = true;
                    }
                }

            
                // Création du mapping
                if( !$error_prd ){
                    if( !ipt_mapping_add( $imp_id, 0, 'FLD', 'reference-hml', 'reference-hml', null, null, 'fr', '', null, null, 0, 101475 )
                        || !ipt_mapping_add( $imp_id, 1, 'PRD_NAME', 'titre', 'titre', null, null, 'fr' )
                        || !ipt_mapping_add( $imp_id, 2, 'PRD_PUBLISH', 'oublie', 'publie', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}')
                        || !ipt_mapping_add( $imp_id, 3, 'PRD_SLEEP', 'sommeil', 'sommeil', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}')
                        || !ipt_mapping_add( $imp_id, 4, 'FLD', 'sous-titre', 'sous-titre', null, null, 'fr', '', null, null, 0, 101399)
                        || !ipt_mapping_add( $imp_id, 5, 'FLD', 'support', 'support', null, null, 'fr', '', null, null, 0, 101398 )
                        || !ipt_mapping_add( $imp_id, 6, 'PRD_EAN', 'ean13', 'ean13', null, null, 'fr' )
                        || !ipt_mapping_add( $imp_id, 7, 'FLD', 'genre', 'genre', null, null, 'fr', '', null, null, 0, 101400 )
                        || !ipt_mapping_add( $imp_id, 8, 'FLD', 'editeur', 'editeur', null, null, 'fr', '', null, null, 0, 101401 )
                        || !ipt_mapping_add( $imp_id, 9, 'FLD', 'collection', 'collection', null, null, 'fr', '', null, null, 0, 101402 )
                        || !ipt_mapping_add( $imp_id, 10, 'FLD', 'parution', 'parution', null, null, 'fr', '', null, null, 0, 101403 ) 
                        || !ipt_mapping_add( $imp_id, 11, 'FLD', 'disponibilite', 'disponibilite', null, null, 'fr', '', null, null, 0, 101404 )
                        || !ipt_mapping_add( $imp_id, 12, 'PRD_PRICE', 'pp-ttc', 'pp-ttc', null, null, 'fr', '', null, null, 0, 0, '{"gu_catf":"","tarif":"ttc"}' ) 
                        || !ipt_mapping_add( $imp_id, 13, 'PRD_TVA', 'tx-tva', 'tx-tva', null, null, 'fr' )
                        || !ipt_mapping_add( $imp_id, 14, 'PRD_PRICE', 'pp-ht', 'pp-ht', null, null, 'fr', '', null, null, 0, 0, '{"gu_catf":"","tarif":"ht"}' )                 
                        || !ipt_mapping_add( $imp_id, 15, 'PRD_DESC', 'intervenants', 'intervenants', null, null, 'fr' )
                        //|| !ipt_mapping_add( $imp_id, 16, ) stock-mas
                        //|| !ipt_mapping_add( $imp_id, 17, ) stock-cpt
                        || !ipt_mapping_add( $imp_id, 18, 'FLD', 'catalogue', 'catalogue', null, null, 'fr', '', null, null , 0, 101406 )
                        || !ipt_mapping_add( $imp_id, 19, 'PRD_LENGTH', 'longueur', 'longueur', null, null, 'fr', '', null, null, 0, 0, '', 'mm' )
                        || !ipt_mapping_add( $imp_id, 20, 'PRD_WIDTH', 'largeur', 'largeur', null, null, 'fr', '', null, null, 0, 0, '', 'mm' ) 
                        || !ipt_mapping_add( $imp_id, 21, 'PRD_HEIGHT', 'epaisseur', 'epaisseur', null, null, 'fr', '', null, null, 0, 0, '', 'mm' )
                        || !ipt_mapping_add( $imp_id, 22, 'PRD_WEIGHT', 'poids', 'poids', null, null, 'fr', '', null, null, 0, 0, '', 'kg' )
                        || !ipt_mapping_add( $imp_id, 23, 'FLD', 'nb-pages', 'nb-pages', null, null, 'fr', '', null, null, 0, 101412 )
                        || !ipt_mapping_add( $imp_id, 24, 'FLD', 'infos', 'infos', null, null, 'fr', '', null, null , 0, 101483 )
                        || !ipt_mapping_add( $imp_id, 25, 'PRD_CAT', 'categorie', 'categorie', null, null, 'fr', '', 'add', 'id', 0, 0, '', '', 233875, false )
                        || !ipt_mapping_add( $imp_id, 26, 'PRD_REF', 'reference', 'reference', 'ref', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                        || !ipt_mapping_add( $imp_id, 27, 'FLD', 'fld-tva', 'fld-tva', null, null, 'fr', '', null, null , 0, 101481 )
                        || !ipt_mapping_add( $imp_id, 28, 'FLD', 'fld-pp-ttc', 'fld-pp-ttc', null, null, 'fr', '', null, null , 0, 101482 )
                        || !ipt_mapping_add( $imp_id, 29, 'FLD', 'fld-poids', 'fld-poids', null, null, 'fr', '', null, null , 0, 101480 )
                        || !ipt_mapping_add( $imp_id, 30, 'FLD', 'fld-intervenants', 'fld-intervenants', null, null, 'fr', '', null, null , 0, 101405 )

                    ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import d\'article.');
                        $error_prd = true;
                    }
                }

                
                if( !$error_prd ){
                    if( !ipt_imports_exec( $imp_id ) ){
                        ipt_imports_set_state( $imp_id, 'pending' );
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des articles.');
                        $error_prd = true;
                    }
                }
            }

            // Attend que l'import d'article soit terminé avant de commencer l'import des stocks
            if( !$error_prd ){
                while( ipt_imports_get_state($imp_id) == 'processing' ){
                    sleep(60);
                }
            }

            // import des stocks
            {
                $filename = "YUTO-SYNC/X3toYUTO/stocks.txt";
                $name = "YUTO-SYNC/X3toYUTO/stocks.txt";
                $ext = pathinfo( $filename, PATHINFO_EXTENSION );

                try{
                    $file = new ImportFile( $filename, 'ftp', $name );

                    $file->connectToFtp($url, $login, $password );
                    
                    $file->saveFile();

                  
                    // Ajoute un entête au fichier
                    {
                        $handle = $file->openFile();
                        $file_data = "prd-ref;qte;depot-ref\n";
                        $file_data .= $file->getContent();
                        file_put_contents($file->localFilePath, $file_data);

                        $file->closeFile();
                    }


                    $file->readFile( $col_sep, $text_separator );

                    if( !$file->checkColsAndLines() ){
                        error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                        $error_stock = true;
                    }
                    
                }
                catch(Exception $e){
                    error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                    $error_stock = true;
                }


                // Création de l'import des stocks
                if( !$error_stock ){
                    if( !$imp_id = ipt_imports_add(
                        $ext,
                        $file->original_filename,
                        $file->lines_count,
                        $file->getColumnsCount(),
                        $owner_id,
                        CLS_STOCK,
                        'create',
                        'add/upd',
                        $file->localFilePath,
                        $file->getCharset(),
                        $col_sep,
                        $text_separator,
                        $file->getSize(),
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        false,
                        true
                    )
                    ){
                        unlink( $file->localFilePath );
                        error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import des stocks.');
                        $error_stock = true;
                    }
                }

                // Création du mapping
                if( !$error_stock ){
                    if( !ipt_mapping_add( $imp_id, 0, 'STK_PRD_ID', 'prd-ref', 'prd-ref', 'ref', null, 'fr')
                        || !ipt_mapping_add( $imp_id, 1, 'STK_QTE', 'qte', 'qte', null, null, 'fr' )
                        || !ipt_mapping_add( $imp_id, 2, 'STK_DPS_ID', 'depot-ref', 'depot-ref', 'id', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                    
                    ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping de l\'import des stocks.');
                        $error_stock = true;
                    }
                }
                
                if( !$error_stock ){
                    if( !ipt_imports_exec( $imp_id ) ){
                        ipt_imports_set_state( $imp_id, 'pending' );
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des stocks.');
                        $error_stock = true;
                    }
                }
            }
        }

        // Le paramètre "no_usr" permet d'ignorer la synchro des comptes clients
        if( !isset($ar_params['no_usr']) || !$ar_params['no_usr'] ){
            $usr_to_del = false;

            $error_usr = false;
            $error_adr = false;
            $error_discount = false;
            $error_del_usr = false;

            // Import des clients
            {
                $filename = "YUTO-SYNC/X3toYUTO/clients.txt";
                $name = "YUTO-SYNC/X3toYUTO/clients.txt";
                $ext = pathinfo( $filename, PATHINFO_EXTENSION );

                try{
                    $file = new ImportFile( $filename, 'ftp', $name );

                    $file->connectToFtp($url, $login, $password );
                    
                    $file->saveFile();

                    $file->readFile( $col_sep, $text_separator );

                    if( !$file->checkColsAndLines() ){
                        error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                        $error_usr = true;
                    }

                    // Ajoute un entête au fichier
                    {
                        $handle = $file->openFile();
                        $file_data = "code client;raison sociale;code adresse;adresse L1;adresse L2;adresse L3;code postal;ville;client bloqué;code pays;tel1;tel2;fax;mail;can-login;encours;remise 1;enseigne;canal distrib;contrat ?;mode reglement;echéance;rep1;rep2;secteur;info1;info2;info3;info4;info5;info6;to-del\n";
                        $file_data .= $file->getContent();
                        file_put_contents($file->localFilePath, $file_data);

                        $file->closeFile();
                    }

                    // Création du fichier d'import de suppression de client
                    {
                        $usr_file_name = 'usr_to_del-'.uniqid().'.csv';
                        $usr_file = fopen(IMPORT_ROOT_DIR.$usr_file_name, 'wb');
                        fwrite($usr_file, "usr-ref\r\n");

                        $temp = file($file->localFilePath);

                        $first_line = true;
                        foreach($temp as $key => $value){

                            $value = str_replace("\n", "", $value);
                            $value = str_replace("\r", "", $value);
                            
                            $array_value = explode ( ";" , $value );

                            if( !$first_line ){

                                // Complete le fichier d'import de suppression de client
                                if( $array_value[31] == 1 ){
                                    $usr_to_del = true;
                                    fwrite($usr_file, $array_value[0]."\r\n" );
                                }
                            
                            }
                                
                            $first_line = false;
                        }
                    }

                    // concaténe la raison social avec le code adresse
                    {
                        $temp = file($file->localFilePath);

                        $first_line = true;
                        foreach($temp as $key => $value){
                            
                            $array_value = explode ( ";" , $value );

                            if( !$first_line ){
                                // concaténe la raison social avec le code adresse
                                $array_value[1] = $array_value[1].' - '.str_replace('"', '', $array_value[2]);
                                
                                $temp[$key] = implode( ';', $array_value);
                            }
                            
                            $first_line = false;
                        }


                        $fp = fopen($file->localFilePath, 'w');
                        foreach($temp as $key => $value){
                            fwrite($fp, $value);
                        }
                        fclose($fp);
                    }
                }
                catch(Exception $e){
                    error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                    $error_usr = true;
                }

                if( !$error_usr ){

                    // Création de l'import des clients
                    if( !$imp_id = ipt_imports_add(
                        $ext,
                        $file->original_filename,
                        $file->lines_count,
                        $file->getColumnsCount(),
                        $owner_id,
                        CLS_USER,
                        'create',
                        'add/upd',
                        $file->localFilePath,
                        $file->getCharset(),
                        $col_sep,
                        $text_separator,
                        $file->getSize(),
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        false,
                        true
                    )
                    ){
                        unlink( $file->localFilePath );
                        error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import des clients.');
                        $error_usr = true;
                    }
                }

                // Création du mapping
                if( !$error_usr ){
                    if( !ipt_mapping_add( $imp_id, 0, 'USR_REF', 'code-client', 'code-client', 'ref', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                        || !ipt_mapping_add( $imp_id, 1, 'USR_SOCIETY', 'raison-sociale', 'raison-sociale', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                        || !ipt_mapping_add( $imp_id, 2, 'USR_ADR_REF', 'code-adresse', 'code-adresse', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                        || !ipt_mapping_add( $imp_id, 3, 'USR_ADDRESS1', 'adresse-l1', 'adresse-l1', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                        || !ipt_mapping_add( $imp_id, 4, 'USR_ADDRESS2', 'adresse-l2', 'adresse-l2', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                        //|| !ipt_mapping_add( $imp_id, 5, '', 'adresse-l3', 'adresse-l3', null, null, 'fr')
                        || !ipt_mapping_add( $imp_id, 6, 'USR_ZIP_CODE', 'code-postal', 'code-postal', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                        || !ipt_mapping_add( $imp_id, 7, 'USR_CITY', 'ville', 'ville', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                        || !ipt_mapping_add( $imp_id, 8, 'USR_IS_LOCKED', 'client-bloque', 'client-bloque', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}' )
                        || !ipt_mapping_add( $imp_id, 9, 'USR_COUNTRY', 'code-pays', 'code-pays', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                        || !ipt_mapping_add( $imp_id, 10, 'USR_PHONE', 'tel1', 'tel1', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                        || !ipt_mapping_add( $imp_id, 11, 'USR_CELLPHONE', 'tel2', 'tel2', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                        || !ipt_mapping_add( $imp_id, 12, 'USR_FAX', 'fax', 'fax', null, null, 'fr', '', null, null, 0, 0, '{"inv_adr":1}')
                        || !ipt_mapping_add( $imp_id, 13, 'USR_EMAIL', 'mail', 'mail', null, null, 'fr')
                        || !ipt_mapping_add( $imp_id, 14, 'USR_CAN_LOGIN', 'can-login', 'can-login', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}')
                        || !ipt_mapping_add( $imp_id, 15, 'USR_ENCOURS', 'encours', 'encours', null, null, 'fr' )
                        //|| !ipt_mapping_add( $imp_id, 16, '', 'remise-1', 'remise-1', null, null, 'fr')
                        || !ipt_mapping_add( $imp_id, 17, 'FLD', 'enseigne', 'enseigne', null, null, 'fr', '', null, null , 0, 101465 )
                        || !ipt_mapping_add( $imp_id, 18, 'FLD', 'canal-distrib', 'canal-distrib', null, null, 'fr', '', null, null , 0, 101466 )
                        || !ipt_mapping_add( $imp_id, 19, 'FLD', 'contrat', 'contrat', null, null, 'fr', '', null, null , 0, 101467 )
                        || !ipt_mapping_add( $imp_id, 20, 'FLD', 'mode-reglement', 'mode-reglement', null, null, 'fr', '', null, null, 0, 101473 )
                        || !ipt_mapping_add( $imp_id, 21, 'FLD', 'echeance', 'echeance', null, null, 'fr', '', null, null , 0, 101471 )
                        || !ipt_mapping_add( $imp_id, 22, 'USR_SELLER', 'rep1', 'rep1', null, null, 'fr', '', 'upd', 'ref' )
                        //|| !ipt_mapping_add( $imp_id, 23, 'FLD', 'rep2', 'rep2', null, null, 'fr', '', null, null , 0, 101413 )
                        || !ipt_mapping_add( $imp_id, 23, 'USR_RELATION_PARENT', 'rep2', 'rep2',null, null, 'fr', '', 'upd', 'ref', 0, 0, '', '', 0, 0)
                        || !ipt_mapping_add( $imp_id, 24, 'FLD', 'secteur', 'secteur', null, null, 'fr', '', null, null , 0, 101474 )
                        || !ipt_mapping_add( $imp_id, 25, 'FLD', 'info1', 'info1', null, null, 'fr', '', null, null , 0, 101472 )
                        || !ipt_mapping_add( $imp_id, 30, 'USR_DEFAULT_PRF', 'Profil par défaut', 'Profil par défaut', null, null, '', '', null, null, 0, 0, '"'.PRF_CUST_PRO.'"' )
                        || !ipt_mapping_add( $imp_id, 31, 'USR_MASK_YUTO', 'to-del', 'to-del', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}')
                    ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping de l\'import des clients.');
                        $error_usr= true;
                    }
                }
                
                
                if( !$error_usr ){
                    ipt_imports_set_state( $imp_id, 'pending' );
                    if( !ipt_imports_exec( $imp_id ) ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des clients.');
                    }
                }
            }

            // Attend que l'import de client soit terminé avant de commencer l'import des adresse de livraison
            if( !$error_usr ){
                while( ipt_imports_get_state($imp_id) == 'processing' ){
                    sleep(60);
                }
            }

            // import d'adresse de livraison
            {
                $filename = "YUTO-SYNC/X3toYUTO/adresses.txt";
                $name = "YUTO-SYNC/X3toYUTO/adresses.txt";
                $ext = pathinfo( $filename, PATHINFO_EXTENSION );

                try{
                    $file = new ImportFile( $filename, 'ftp', $name );

                    $file->connectToFtp($url, $login, $password );
                    
                    $file->saveFile();

                    $file->readFile( $col_sep, $text_separator );

                    if( !$file->checkColsAndLines() ){
                        error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                        $error_adr = true;
                    }

                    // Ajoute un entête au fichier
                    {
                        $handle = $file->openFile();
                        $file_data = "code client;code adresse;raison social;add1;add2;add3;code postal;ville;pays;tel1;tel2;adresse principale\n";
                        $file_data .= $file->getContent();
                        file_put_contents($file->localFilePath, $file_data);

                        $file->closeFile();
                    }

                    // concaténe la raison social avec le code adresse
                    {
                        $temp = file($file->localFilePath);

                        $first_line = true;
                        foreach($temp as $key => $value){
                            
                            $array_value = explode ( ";" , $value );

                            if( !$first_line ){
                                // concaténe la raison social avec le code adresse
                                $array_value[2] = $array_value[2].' - '.str_replace('"', '', $array_value[1]);
                                
                                $temp[$key] = implode( ';', $array_value);
                            }
                            
                            $first_line = false;
                        }


                        $fp = fopen($file->localFilePath, 'w');
                        foreach($temp as $key => $value){
                            fwrite($fp, $value);
                        }
                        fclose($fp);
                    }
                }
                catch(Exception $e){
                    error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                    $error_adr = true;
                }


                // Création de l'import des adresse de livraison
                if( !$error_adr ){
                    if( !$imp_id = ipt_imports_add(
                        $ext,
                        $file->original_filename,
                        $file->lines_count,
                        $file->getColumnsCount(),
                        $owner_id,
                        CLS_USER,
                        'create',
                        'add/upd',
                        $file->localFilePath,
                        $file->getCharset(),
                        $col_sep,
                        $text_separator,
                        $file->getSize(),
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        false,
                        true
                    )
                    ){
                        unlink( $file->localFilePath );
                        error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import d\'adresse de livraison.');
                        $error_adr = true;
                    }
                }

                
                // Création du mapping
                if( !$error_adr ){

                    if( !ipt_mapping_add( $imp_id, 0, 'USR_REF', 'code-client', 'code-client', 'ref', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                        || !ipt_mapping_add( $imp_id, 1, 'USR_ADR_REF', 'code-adresse', 'code-adresse', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                        || !ipt_mapping_add( $imp_id, 2, 'USR_SOCIETY', 'raison-social', 'raison-social', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                        || !ipt_mapping_add( $imp_id, 3, 'USR_ADDRESS1', 'add1', 'add1', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                        || !ipt_mapping_add( $imp_id, 4, 'USR_ADDRESS2', 'add2', 'add2', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                        //|| !ipt_mapping_add( $imp_id, 5, )
                        || !ipt_mapping_add( $imp_id, 6, 'USR_ZIP_CODE', 'code-postal', 'code-postal', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                        || !ipt_mapping_add( $imp_id, 7, 'USR_CITY', 'ville', 'ville', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                        || !ipt_mapping_add( $imp_id, 8, 'USR_COUNTRY', 'pays', 'pays', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                        || !ipt_mapping_add( $imp_id, 9, 'USR_PHONE', 'tel1', 'tel1', null, null, 'fr', '', null, null, 0, 0, '{"dlv_adr":"1"}' )
                        //|| !ipt_mapping_add( $imp_id, 10,)
                        || !ipt_mapping_add( $imp_id, 11, 'USR_MAIN_ADDRESS', 'adresse-principale', 'adresse-principale', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}' )


                    ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import d\'adresse de livraison.');
                        $error_adr = true;
                    }
                }

                
                if( !$error_adr ){
                    ipt_imports_set_state( $imp_id, 'pending' );
                    if( !ipt_imports_exec( $imp_id ) ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des adresses de livraison.');
                        $error_adr = true;
                    }
                }
            }


            // import de remise
            {
                $filename = "YUTO-SYNC/X3toYUTO/clients.txt";
                $name = "YUTO-SYNC/X3toYUTO/clients.txt";
                $ext = pathinfo( $filename, PATHINFO_EXTENSION );

                try{
                    $file = new ImportFile( $filename, 'ftp', $name );

                    $file->connectToFtp($url, $login, $password );
                    
                    $file->saveFile();

                    $file->readFile( $col_sep, $text_separator );

                    if( !$file->checkColsAndLines() ){
                        error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                        $error_discount = true;
                    }

                    // Ajoute un entête au fichier
                    {
                        $handle = $file->openFile();
                        $file_data = "code client;raison sociale;code adresse;adresse L1;adresse L2;adresse L3;code postal;ville;client bloqué;code pays;tel1;tel2;fax;mail;?;??;remise 1;enseigne;canal distrib;contrat ?;mode reglement;echéance;rep1;???;????;?????;;;;;;to-del\n";
                        $file_data .= $file->getContent();
                        file_put_contents($file->localFilePath, $file_data);

                        $file->closeFile();
                    }

                }
                catch(Exception $e){
                    error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                    $error_discount = true;
                }


                // Création de l'import des remises
                if( !$error_discount ){
                    if( !$imp_id = ipt_imports_add(
                        $ext,
                        $file->original_filename,
                        $file->lines_count,
                        $file->getColumnsCount(),
                        $owner_id,
                        CLS_PRICE,
                        'create',
                        'add/upd',
                        $file->localFilePath,
                        $file->getCharset(),
                        $col_sep,
                        $text_separator,
                        $file->getSize(),
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        false,
                        true,
                        '{"sub_class":"user_discount"}'
                    )
                    ){
                        unlink( $file->localFilePath );
                        error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import des remises.');
                        $error_discount = true;
                    }
                }

                
                // Création du mapping
                if( !$error_discount ){
                    if( !ipt_mapping_add( $imp_id, 0, 'PRC_USR', 'code-client', 'code-client', null, null, 'fr', '', null, 'ref' )
                        || !ipt_mapping_add( $imp_id, 16, 'PRC_DISCOUNT_VALUE', 'remise-1', 'remise-1', null, null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                        || !ipt_mapping_add( $imp_id, 30, 'PRC_DEFAULT_DISCOUNT_TYPE', 'Type de remise par défaut', 'Type de remise par défaut', null, null, '', '', null, null, 0, 0, "2" ) // Remise en %
                    ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des remises.');
                        $error_discount = true;
                    }
                }

                
                if( !$error_discount ){
                    ipt_imports_set_state( $imp_id, 'pending' );
                    if( !ipt_imports_exec( $imp_id ) ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des remises.');
                        $error_discount = true;
                    }
                }
            }

            // Import de suppression de client
            if( $usr_to_del && !$error_usr ){
                $filename = IMPORT_ROOT_DIR.$usr_file_name;
                $name = $usr_file_name;
                $ext = pathinfo( $filename, PATHINFO_EXTENSION );

                try{
                    $file = new ImportFile( $filename, 'file', $name );

                    $file->original_filename = $usr_file_name;
                    $file->filename = $usr_file_name;
                    $file->localFilePath = $filename;

                    $file->readFile( $col_sep, $text_separator );

                    if( !$file->checkColsAndLines() ){
                        error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                        $error_del_usr = true;
                    }
    
                }
                catch(Exception $e){
                    error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                    $error_del_usr = true;
                }

                // Création de l'import des stocks
                if( !$error_del_usr ){
                    if( !$imp_id = ipt_imports_add(
                        $ext,
                        $file->original_filename,
                        $file->lines_count,
                        $file->getColumnsCount(),
                        $owner_id,
                        CLS_USER,
                        'create',
                        'del',
                        $file->localFilePath,
                        $file->getCharset(),
                        $col_sep,
                        $text_separator,
                        $file->getSize(),
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        false,
                        true
                    )
                    ){
                        unlink( $file->localFilePath );
                        error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import de suppression de client.');
                        $error_del_usr = true;
                    }
                }

                // Création du mapping
                if( !$error_del_usr ){
                    if( !ipt_mapping_add( $imp_id, 0, 'USR_REF', 'usr-ref', 'usr-ref', 'ref', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 ) ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping de l\'import de suppression de client.');
                        $error_del_usr = true;
                    }
                }

                if( !$error_del_usr ){
                    ipt_imports_set_state( $imp_id, 'pending' );
                    if( !ipt_imports_exec( $imp_id ) ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import de suppression de client.');
                        $error_del_usr = true;
                    }
                }
            }

        }

        // Le paramètre "no_ord" permet d'ignorer la synchro des commandes
        if( !isset($ar_params['no_model_ord']) || !$ar_params['no_model_ord'] ){
            $error_model = false;

            // import des modeles de commande
            {
                $filename = "YUTO-SYNC/X3toYUTO/modeles_commandes.txt";
                $name = "YUTO-SYNC/X3toYUTO/modeles_commandes.txt";
                $ext = pathinfo( $filename, PATHINFO_EXTENSION );

                try{
                    $file = new ImportFile( $filename, 'ftp', $name );

                    $file->connectToFtp($url, $login, $password );
                    
                    $file->saveFile();

                  
                    // Ajoute un entête au fichier
                    {
                        $handle = $file->openFile();
                        $file_data = "ref;line;prd\n";
                        $file_data .= $file->getContent();
                        file_put_contents($file->localFilePath, $file_data);

                        $file->closeFile();
                    }

                    // Ajoute une colonne correspondant à la position de la ligne de commande
                    {

                        $temp = file($file->localFilePath);

                        $first_line = true;
                        foreach($temp as $key => $value){

                            $value = str_replace("\n", "", $value);
                            $value = str_replace("\r", "", $value);
                            
                            $array_value = explode ( ";" , $value );

                            if( $first_line ){
                                $temp[$key] = $value.";pos\r\n";

                            }else{
                                
                                // Complete le fichier
                                $temp[$key] = implode( ';', $array_value).";".$array_value[1]."\r\n";
                            }
                                
                            $first_line = false;
                        }


                        $fp = fopen($file->localFilePath, 'w');
                        foreach($temp as $key => $value){
                            fwrite($fp, $value);
                        }
                        fclose($fp);
                    }



                    $file->readFile( $col_sep, $text_separator );

                    if( !$file->checkColsAndLines() ){
                        error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                        $error_model = true;
                    }

                }
                catch(Exception $e){
                    error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                    $error_model = true;
                }


                // Création de l'import des modèle de commandes
                if( !$error_model ){
                    if( !$imp_id = ipt_imports_add(
                        $ext,
                        $file->original_filename,
                        $file->lines_count,
                        $file->getColumnsCount(),
                        $owner_id,
                        CLS_ORDER,
                        'create',
                        'add/upd',
                        $file->localFilePath,
                        $file->getCharset(),
                        $col_sep,
                        $text_separator,
                        $file->getSize(),
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        false,
                        true,
                        '{"sub_class":"model"}'
                    )
                    ){
                        unlink( $file->localFilePath );
                        error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import des modèles de commandes.');
                        $error_model = true;
                    }
                }

                // Création du mapping
                if( !$error_model ){
                    if( !ipt_mapping_add( $imp_id, 0, 'ORD_REF', 'ref', 'ref', 'ref', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                        || !ipt_mapping_add( $imp_id, 1, 'ORD_LINE', 'line', 'line', null, null, 'fr' )
                        || !ipt_mapping_add( $imp_id, 2, 'ORD_PRD_ID', 'prd', 'prd', null, null, 'fr', '', null, 'ref' )
                        || !ipt_mapping_add( $imp_id, 3, 'ORD_PRD_POS', 'pos', 'pos', null, null, 'fr' )
                    ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des modèles de commandes.');
                        $error_model = true;
                    }
                }

                if( !$error_model ){
                    if( !ipt_imports_exec( $imp_id ) ){
                        ipt_imports_set_state( $imp_id, 'pending' );
                        error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des modèles de commandes.');
                        $error_model = true;
                    }
                }
            }
        }
        if( !isset($ar_params['no_ord']) || !$ar_params['no_ord'] ){
            // import des commandes
            {
                $fld_type = 101477;
                $fld_code = 101512;

                $orders_directory = "YUTO-SYNC/X3toYUTO/cdes";

                $ftp_conn = ftp_connect( $url );

                if( !ftp_login( $ftp_conn, $login, $password ) ){
                    // Return the resource
                    throw new Exception("import-data-harmonia : Impossible d'établir une connexion au ftp");
                }

                ftp_pasv($ftp_conn, true);
                $ftp_files = ftp_nlist($ftp_conn, $orders_directory);
                $files = [];
                foreach ($ftp_files as $file) {
                    $file = str_replace($orders_directory.'/', '', $file);
                    if( preg_match("/^commandes_([0-9]{12}).txt$/", $file, $matches) ){
                        $files[$matches[1]] = $orders_directory.'/'.$file;
                    }
                }
                ksort($files);

                foreach( $files as $filename ){
                    $error_order = false;
                    $name = $filename;
                    $ext = pathinfo( $filename, PATHINFO_EXTENSION );
                    try{
                        $file = new ImportFile( $filename, 'ftp', $name );

                        $file->connectToFtp($url, $login, $password );

                        $file->saveFile();

                        // Ajoute un entête au fichier
                        {
                            $handle = $file->openFile();
                            $header = [
                                'ord_id',
                                'piece',
                                'user_ref',
                                'ord_date',
                                'order_ref',
                                'statut',
                                'type',
                                'code_liste',
                                'date_livr',
                                'line_id',
                                'prd_ref',
                                'price_ht',
                                'qte',
                                'tva',
                                'seller_ref',
                                'Reliquat',
                            ];
                            $file_data = implode(';', $header).PHP_EOL;
                            $file_data .= $file->getContent();
                            file_put_contents($file->localFilePath, $file_data);
    
                            $file->closeFile();
                        }
                        $file->readFile( $col_sep, $text_separator );
    
                        if( !$file->checkColsAndLines() ){
                            error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                            $error_order = true;
                        }
    
                    }
                    catch(Exception $e){
                        error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                        $error_order = true;
                    }

                    // Création de l'import des modèle de commandes
                    if( !$error_order ){
                        if( !$imp_id = ipt_imports_add(
                            $ext,
                            $file->original_filename,
                            $file->lines_count,
                            $file->getColumnsCount(),
                            $owner_id,
                            CLS_ORDER,
                            'create',
                            'add/upd',
                            $file->localFilePath,
                            $file->getCharset(),
                            $col_sep,
                            $text_separator,
                            $file->getSize(),
                            null,
                            null,
                            null,
                            null,
                            null,
                            0,
                            false,
                            true
                        )
                        ){
                            error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import des commandes.');
                            $error_order = true;
                        }
                    }

                    // Création du mapping
                    if( !$error_order ){
                        do{
                            if (!ipt_mapping_add( $imp_id, 0, 'ORD_ID', 'ord_id', 'ord-id', 'id', null, 'fr', '', null, null, 0, 0, '', '', 0, true )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 1, 'ORD_PIECE', 'piece', 'piece', 'piece', null, 'fr', '', null, null, 0, 0, '', '', 0, false )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 2, 'ORD_USR', 'user_ref', 'user-ref', 'ref', null, 'fr', '', null, 'ref' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 3, 'ORD_DATE', 'ord_date', 'ord-date', null, null, 'fr' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 4, 'ORD_REF', 'order_ref', 'order-ref', null, null, 'fr' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 5, 'ORD_STATE', 'statut', 'statut', null, null, 'fr', '', null, null, 0, 0, '{"8":"facture","10":"annule"}' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 6, 'FLD', 'type', 'type', null, null, 'fr', '', null, null, 0,  $fld_type)) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 7, 'FLD', 'code_liste', 'code-liste', null, null, 'fr', '', null, null, 0,  $fld_code)) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 8, 'ORD_DATE_LIVR', 'date_livr', 'date-livr', null, null, 'fr' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 9, 'ORD_LINE', 'line_id', 'line-id', null, null, 'fr' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 10, 'ORD_PRD_ID', 'prd_ref', 'prd-ref', null, null, 'fr', '', null, 'ref' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 11, 'ORD_PRD_PRICE', 'price_ht', 'price-ht', null, null, 'fr' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 12, 'ORD_PRD_QTE', 'qte', 'qte', null, null, 'fr' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 13, 'ORD_PRD_TVA', 'tva', 'tva', null, null, 'fr' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 14, 'ORD_SELLER', 'seller_ref', 'seller-ref', null, null, 'fr', '', null, 'ref' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                            if (!ipt_mapping_add( $imp_id, 15, 'ORD_PRD_INVOICE', 'Reliquat', 'reliquat', null, null, 'fr', '', null, null, 0, 0, '{"bool":["0"]}' )) {
                                error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                                $error_order = true;
                                break;
                            }
                        }while(false);
                    }

                    if( !$error_order ){
                        if( !ipt_imports_exec( $imp_id ) ){
                            ipt_imports_set_state( $imp_id, 'pending' );
                            error_log(__FILE__.':'.__LINE__.':'.$imp_id.' Une erreur est survenue lors de l\'exécution de l\'import des commandes.');
                        }
                        ftp_delete($ftp_conn, $filename);
                    }
                }
            }
        }

        // Le paramètre "no_cod_list" permet d'ignorer la synchro de la liste de code (fld)
        if( !isset($ar_params['no_cod_list']) || !$ar_params['no_cod_list'] ){
            // Identifiant du champ avancé contenant les codes
            $fld = 101478;

            // Nom du fichier sur le FTP contenant la liste des codes
            $filename = "YUTO-SYNC/X3toYUTO/codes_listes.txt";

            // Création du lien avec le FTP
            if( $ftp = ftp_connect($url) ){
                // Connexion au FTP
                if( ftp_login($ftp, $login, $password) ){
                    // Active le mode passif (nécéssaire sinon la récupération des éléments du FTP échoue)
                    ftp_pasv($ftp, true);

                    // Récupère le contenu du fichier du FTP en local pour le traitement
                    $res = ftp_get($ftp, $config['doc_dir'].'/temp-codes-lists.txt', $filename, FTP_BINARY);

                    // Si le fichier en local a bien été créé alors on peut passer à la suite
                    if( $res ){
                        $content = file_get_contents($config['doc_dir'].'/temp-codes-lists.txt');
                        $temp = explode("\n", $content);

                        $ar_val_ids = array();

                        // Insertion de chaque code dans les valeurs de restrictions du champs avancé
                        foreach( $temp as $t ){
                            // Supprime la présence de guillemet
                            $t = str_replace('"', '', $t);
                            if( trim($t) == '' ){
                                continue;
                            }
                            
                            $val_id = fld_restricted_values_get_id($fld, $t);
                            if( is_numeric($val_id) && $val_id > 0 ){
                                $ar_val_ids[] = $val_id;
                            }else{
                                // Création de la nouvelle valeur
                                $add = fld_restricted_values_add($fld, $t);
                                if( is_numeric($add) && $add > 0 ){
                                    $ar_val_ids[] = $add;
                                }
                            }    
                        }

                        // Supprime les valeurs qui ne sont plus d'actualité
                        $r_val = fld_restricted_values_get(0, $fld);
                        if( $r_val ){
                            while( $val = ria_mysql_fetch_assoc($r_val) ){
                                if( !in_array($val['id'], $ar_val_ids) ){
                                    fld_restricted_values_del($val['id']);
                                }
                            }
                        }
                    }
                }

                ftp_close($ftp);
            }
        }
    }