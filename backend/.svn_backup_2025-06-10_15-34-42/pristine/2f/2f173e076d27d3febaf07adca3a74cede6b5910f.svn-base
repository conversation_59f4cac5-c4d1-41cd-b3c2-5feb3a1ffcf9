<?php

// \cond onlyria
/**	\defgroup fld_objects Instances de classe personnalisée
 * 	\ingroup model_fields
 *	Ce module comprend les fonctions nécessaires à la gestion des instances des classes personnalisées
 *	@{
 */

/**	Cette fonction crée une nouvelle instance pour une classe personnalisée
 *	@param int $cls_id Obligatoire, identifiant de la classe
 *	@param string $obj_name Obligatoire, nom de l'instance
 *	@param int $obj_parent_id Optionnel, identifiant de l'objet parent
 *	@return bool False en cas d'échec
 *	@return int L'identifiant généré en cas de succès
 */
function fld_objects_add( $cls_id, $obj_name, $obj_parent_id=0 ){
	if( !fld_classes_is_tenant_linked($cls_id) ) return false;

	$obj_name = trim($obj_name);
	if( $obj_name==='' ) return false;

	if( is_numeric($obj_parent_id) && $obj_parent_id>0 ){
		$rparent = fld_objects_get( $obj_parent_id, $cls_id );
		if( !$rparent || !ria_mysql_num_rows($rparent) ) return false;
	}else{
		$obj_parent_id = 0;
	}

	global $config;

	ria_mysql_query('
		insert into fld_objects
			(obj_tnt_id, obj_cls_id, obj_date_created, obj_name, obj_parent_id)
		values
			('.$config['tnt_id'].', '.$cls_id.', now(), "'.addslashes($obj_name).'", '.( !$obj_parent_id ? 'NULL' : $obj_parent_id ).')
	');

	if( ria_mysql_errno() ){
		return false;
	}

	$object_id = ria_mysql_insert_id(); // On recupere l'id du nouvel objet créé avant l'execution de fld_classes_set_date_modified()

	// Met à jour la date de modification de la classe
	fld_classes_set_date_modified( $cls_id );

	return $object_id;
}

/** Cette fonction permet de mettre un jour une instance d'une classe personnalisé.
 *	@param int $obj_id Obligatoire, identifiant de l'instance
 *	@param $obj_name Obligatoire, nom de l'instance
 *	@param $obj_parent_id Optionnel, identifiant d'un objet parent
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function fld_objects_update( $obj_id, $obj_name, $obj_parent_id=0 ){
	global $config;

	if( !is_numeric($obj_id) || $obj_id<=0 ){
		return false;
	}
	if( trim($obj_name)=='' ){
		return false;
	}
	if( !is_numeric($obj_parent_id) || $obj_parent_id<0 ){
		return false;
	}

	$obj_parent_id = $obj_parent_id == $obj_id ? 0 : $obj_parent_id;

	return ria_mysql_query('
		update fld_objects
		set obj_name=\''.addslashes( $obj_name ).'\',
			obj_parent_id='.( $obj_parent_id>0 ? $obj_parent_id : 'null' ).'
		where obj_tnt_id='.$config['tnt_id'].'
			and obj_id='.$obj_id.'
	');

}

/**	Cette fonction supprime une instance d'une classe personnalisé
 *	@param int $obj_id Identifiant de l'instance
 *	@param $del_childs Optionnel, détermine si les enfants sont supprimés (sinon, leur parent est remis à NULL)
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_objects_del( $obj_id, $del_childs=false ){
	if( !is_numeric($obj_id) || $obj_id<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		update fld_objects
			set obj_date_deleted=now()
		where obj_id='.$obj_id.'
			and obj_tnt_id='.$config['tnt_id']
	);

	if( ria_mysql_errno() ){
		return false;
	}

	if( $r ){
		ria_mysql_query('
			update fld_objects
			set '.( $del_childs ? 'obj_date_deleted=now()' : 'obj_parent_id=NULL' ).'
			where obj_tnt_id='.$config['tnt_id'].' and obj_parent_id='.$obj_id.'
		');
	}

	return $r;
}

/**	Cette fonction détermine l'existence d'une instance de classe personnalisée
 *	@param int $obj_id Obligatoire, identifiant de l'instance
 *	@param int $cls_id Optionnel, identifiant de la classe de l'instance
 *	@return bool True si l'instance existe, False sinon
 */
function fld_objects_exists( $obj_id, $cls_id=0 ){
	if( !is_numeric($obj_id) || $obj_id<=0 ) return false;
	if( !is_numeric($cls_id) || $cls_id<0 ) return false;

	global $config;

	$sql = '
		select 1 from fld_objects
		where obj_date_deleted is null and
		obj_id='.$obj_id.' and
		obj_tnt_id='.$config['tnt_id'].'
	';

	if( $cls_id ){
		$sql .= ' and obj_cls_id='.$cls_id;
	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
		return false;
	}

	return ria_mysql_num_rows($r);
}

/**	Cette fonction récupère les informations concernent une (ou des) instance(s) d'une classe personnalisée
 *	$obj_id, $obj_parent_id et $cls_id ne peuvent pas être mutuellement à leur valeur par défaut
 *	@param int|array $obj_id Optinnel, identifiant d'une instance (ou tableau d'identifiants)
 *	@param int $cls_id Optionnel, identifiant d'une classe personnalisée
 *	@param int $obj_parent_id Optionnel, identifiant d'une instance directement parente (pas de récursion jusqu'en haut). Spécifier -1 pour ne récupérer que le premier niveau
 *	@param bool $is_sync Optionnel, permet de récupérer les instances synchronisées ou non
 *	@param $fld_search_params Optionnel, permet de faire une recherche par les valeurs des champs avancés rattachés à l'objet. L'utilisation de ce paramètre nécessite que le paramètre $cls_id soit spécifié. Le paramètre est alors un tableau contenant les clés / valeurs suivantes :
 *		- fld : clé obligatoire, champ avancé recherché. L'argument peut pendre plusieurs formes :
 *			- un identifiant unique : on recherche alors les objets pour lesquels ce champ est renseigné
 *			- array(clés => valeur) : la clé est l'ID du champ, la valeur est celle recherchée (voir "or_between_fld" pour le comportement si plusieurs champs spécifiés)
 *			- array(clés => array(valeur)) : la clé est l'ID du champ, les valeurs sont celles recherchées (voir "or_between_val" pour le comportement entre les valeurs)
 *		- or_between_val : clé optionnelle, détermine si la recherche entre les valeurs de champs est un "OU" ou un "ET". Par défaut "ET".
 *		- or_between_fld :  clé optionnelle, détermine si la recherche entre les champs est un "OU" ou un "ET". Par défaut "ET".
 *		- lng : clé optionnelle, langue de recherche pour les valeurs de champs. Par défaut c'est la langue par défaut (et non la langue courante).
 *	@param array $exclude Optionnel, permet d'exclure un ou plusieurs identifiant d'objets
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'instance
 *		- name : nom de l'instance
 *		- cls_id : identifiant de la classe de l'instance
 *		- date_created : date de création de l'instance
 *		- date_modified : date de dernière modification de l'instance
 *		- parent_id : identifiant de l'instance parente
 *		- is_sync : instance synchronisée oui / non
 */
function fld_objects_get( $obj_id=0, $cls_id=0, $obj_parent_id=0, $is_sync=null, $fld_search_params=false, $exclude=0 ){
	if( is_array($obj_id) ){
		if( !sizeof($obj_id) ) return false;
		foreach( $obj_id as $one_id ){
			if( !is_numeric($one_id) || $one_id<=0 ) return false;
		}
	}else{
		if( !is_numeric($obj_id) || $obj_id<0 ) return false;
		if( $obj_id ){
			$obj_id = array($obj_id);
		}else{
			$obj_id = array();
		}
	}
	if( !is_numeric($cls_id) || $cls_id<0 ) return false;
	if( $cls_id ){
		if( !fld_classes_is_tenant_linked($cls_id) ) return false;
	}

	if( !is_numeric($obj_parent_id) || $obj_parent_id<-1 ) return false;

	if( !$cls_id && !sizeof($obj_id) && $obj_parent_id<=0 ) return false;

	$exclude = control_array_integer($exclude, false);
	if( $exclude === false ){
		return false;
	}

	global $config;

	$sql = '
		select obj_id as "id", obj_cls_id as cls_id, obj_date_created as date_created,
		obj_date_modified as date_modified, obj_name as "name", obj_parent_id as parent_id, obj_is_sync as is_sync
		from fld_objects
		where obj_date_deleted is null and obj_tnt_id='.$config['tnt_id'].'
	';
	if( sizeof($obj_id) )
		$sql .= ' and obj_id in ('.implode(', ', $obj_id).')';
	if( $cls_id )
		$sql .= ' and obj_cls_id='.$cls_id;
	if( $obj_parent_id!==0 ){
		if( $obj_parent_id>0 )
			$sql .= ' and obj_parent_id='.$obj_parent_id;
		else
			$sql .= ' and obj_parent_id is null';
	}
	if( $is_sync!==null ){
		$sql .= ' and obj_is_sync = '.( $is_sync ? '1' : '0' );
	}

	if( $cls_id && is_array($fld_search_params) && isset($fld_search_params['fld']) ){
		$sql .= fld_classes_sql_get(
			$cls_id, $fld_search_params['fld'],
			isset($fld_search_params['or_between_val']) ? $fld_search_params['or_between_val'] : false,
			isset($fld_search_params['or_between_fld']) ? $fld_search_params['or_between_fld'] : false,
			isset($fld_search_params['lng']) ? $fld_search_params['lng'] : false
		);
	}

	if( count($exclude) ){
		$sql .= ' and obj_id not in ('.implode(', ', $exclude).')';
	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( mysql_error().' - '.$sql );
		return false;
	}

	return $r;
}

/**	Cette fonction permet de renommner un objet
 *	@param int $obj_id Identifiant de l'objet à renommer
 *	@param $obj_name Nouveau nom de l'objet
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_objects_set_name( $obj_id, $obj_name ){
	if( !fld_objects_exists( $obj_id ) ) return false;
	$obj_name = trim($obj_name);
	if( $obj_name==='' ) return false;

	global $config;

	$r = ria_mysql_query('
		update fld_objects
			set obj_name="'.addslashes($obj_name).'"
		where obj_id='.$obj_id.' and obj_tnt_id='.$config['tnt_id']
	);

	if( ria_mysql_errno() ){
		return false;
	}

	return $r;
}

/**	Cette fonction met à jour le marqueur de synchronisation d'un objet personnalisé
 *	@param int $id Identifiant de l'objet
 *	@param $sync Objet synchronisé oui / non
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_objects_set_is_sync( $id, $sync ){
	global $config;

	if( !fld_objects_exists( $id ) ){
		return false;
	}

	$r = ria_mysql_query('
		update fld_objects
			set obj_is_sync = '.( $sync ? '1' : '0' ).'
		where obj_id = '.$id.' and obj_tnt_id = '.$config['tnt_id']
	);

	if( !$r ){
		return false;
	}

	return true;
}

/**	Cette fonction met à jour la date de dernière modification d'un objet.
 *	@param int $cls_id Identifiant de la classe de l'objet.
 *	@param int|array $obj_id Identifiant de l'objet ou tableau d'identifiants (objets composés).
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function fld_objects_set_date_modified( $cls_id, $obj_id ){

	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	$obj_id = control_array_integer( $obj_id, true, true, true );
	if( $obj_id === false ){
		return false;
	}elseif( !$obj_id[0] ){
		// un premier élément différent de 0 est obligatoire
		return false;
	}

	switch( $cls_id ){
		case CLS_CATEGORY :
			return prd_categories_set_date_modified( $obj_id[0] );
		case CLS_PRODUCT :
			return prd_products_set_date_modified( $obj_id[0] );
		case CLS_USER :
			return gu_users_set_date_modified( $obj_id[0] );
		case CLS_PMT_CODE :
			return pmt_codes_set_date_modified( $obj_id[0] );
		case CLS_DOCUMENT :
			return doc_documents_set_date_modified( $obj_id[0] );
		case CLS_SELL_UNIT :
			return prd_sell_units_set_date_modified( $obj_id[0] );
		case CLS_BRAND :
			return prd_brands_set_date_modified( $obj_id[0] );
		case CLS_DEPOSIT :
			return prd_deposits_set_date_modified( $obj_id[0] );
		case CLS_TYPE_DOCUMENT :
			return doc_types_set_date_modified( $obj_id[0] );
		case CLS_PRICE_CATEGORY :
			return prd_prices_categories_set_date_modified( $obj_id[0] );
		case CLS_PROFIL :
			return gu_profiles_set_date_modified( $obj_id[0] );
		case CLS_RELATION_TYPE :
			return prd_relations_types_set_date_modified( $obj_id[0] );
		case CLS_ORD_PAYMENT :
			return ord_payment_types_set_date_modified( $obj_id[0] );
		case CLS_IMAGE :
			return img_images_set_date_modified( $obj_id[0] );
		case CLS_COLISAGE :
			return prd_colisage_types_set_date_modified( $obj_id[0] );
		case CLS_CGV_VERSION :
			return cgv_versions_set_date_modified( $obj_id[0] );
		case CLS_PRICE :
			return prc_prices_set_date_modified( $obj_id[0] );
		case CLS_TVA :
			return prc_tvas_set_date_modified( $obj_id[0] );
		case CLS_EXEMPT_TVA :
			return prc_tva_exempt_groups_set_date_modified( $obj_id[0] );
		case CLS_INVOICE :
			return ord_invoices_set_date_modified( $obj_id[0] );
		case CLS_PL :
			return ord_pl_set_date_modified( $obj_id[0] );
		case CLS_BL :
			return ord_bl_set_date_modified( $obj_id[0] );
		case CLS_RETURN :
			return ord_returns_set_date_modified( $obj_id[0] );
		case CLS_ORDER :
			return ord_orders_set_date_modified( $obj_id[0] );
		// à partir d'ici, classes liées
		case CLS_CLASSIFY :
			prd_categories_set_date_modified( $obj_id[0] );
			prd_products_set_date_modified( $obj_id[1] );
			return true;
		case CLS_PRD_COLISAGE :
			prd_colisage_types_set_date_modified( $obj_id[0] );
			prd_products_set_date_modified( $obj_id[1] );
			return true;
		case CLS_ADDRESS :
			$radr = gu_adresses_get( 0, $obj_id[0] );
			if( !$radr || !ria_mysql_num_rows($radr) ){
				return false;
			}
			$adr = ria_mysql_fetch_assoc($radr);
			return gu_users_set_date_modified( $adr['usr_id'] );
		case CLS_CGV_ARTICLE :
			$rcgv = cgv_articles_get( $obj_id[0] );
			if( !$rcgv || !ria_mysql_num_rows($rcgv) ){
				return false;
			}
			$cgv = ria_mysql_fetch_assoc($rcgv);
			return cgv_versions_set_date_modified( $cgv['ver_id'] );
		case CLS_INV_PRODUCT :
			return ord_invoices_set_date_modified( $obj_id[0] );
		case CLS_PL_PRODUCT :
			return ord_pl_set_date_modified( $obj_id[0] );
		case CLS_BL_PRODUCT :
			return ord_bl_set_date_modified( $obj_id[0] );
		case CLS_ORD_PRODUCT :
			return ord_orders_set_date_modified( $obj_id[0] );
		case CLS_ORD_PAYMENT_MODEL :
			return ord_payment_models_set_date_modified( $obj_id[0] );
	}

	return false;

}

/// @}
