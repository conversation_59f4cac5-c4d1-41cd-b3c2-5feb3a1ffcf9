UVS indirect calls
-----
<?php

id('var_dump')(1);
id('id')('var_dump')(2);
id()()('var_dump')(4);
id(['udef', 'id'])[1]()('var_dump')(5);
(function($x) { return $x; })('id')('var_dump')(8);
($f = function($x = null) use (&$f) {
    return $x ?: $f;
})()()()('var_dump')(9);
[$obj, 'id']()('id')($id)('var_dump')(10);
'id'()('id')('var_dump')(12);
('i' . 'd')()('var_dump')(13);
'\id'('var_dump')(14);
-----
!!php7
array(
    0: Expr_FuncCall(
        name: Expr_FuncCall(
            name: Name(
                parts: array(
                    0: id
                )
            )
            args: array(
                0: Arg(
                    value: Scalar_String(
                        value: var_dump
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
        args: array(
            0: Arg(
                value: Scalar_LNumber(
                    value: 1
                )
                byRef: false
                unpack: false
            )
        )
    )
    1: Expr_FuncCall(
        name: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Name(
                    parts: array(
                        0: id
                    )
                )
                args: array(
                    0: Arg(
                        value: Scalar_String(
                            value: id
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    value: Scalar_String(
                        value: var_dump
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
        args: array(
            0: Arg(
                value: Scalar_LNumber(
                    value: 2
                )
                byRef: false
                unpack: false
            )
        )
    )
    2: Expr_FuncCall(
        name: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Name(
                        parts: array(
                            0: id
                        )
                    )
                    args: array(
                    )
                )
                args: array(
                )
            )
            args: array(
                0: Arg(
                    value: Scalar_String(
                        value: var_dump
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
        args: array(
            0: Arg(
                value: Scalar_LNumber(
                    value: 4
                )
                byRef: false
                unpack: false
            )
        )
    )
    3: Expr_FuncCall(
        name: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_ArrayDimFetch(
                    var: Expr_FuncCall(
                        name: Name(
                            parts: array(
                                0: id
                            )
                        )
                        args: array(
                            0: Arg(
                                value: Expr_Array(
                                    items: array(
                                        0: Expr_ArrayItem(
                                            key: null
                                            value: Scalar_String(
                                                value: udef
                                            )
                                            byRef: false
                                        )
                                        1: Expr_ArrayItem(
                                            key: null
                                            value: Scalar_String(
                                                value: id
                                            )
                                            byRef: false
                                        )
                                    )
                                )
                                byRef: false
                                unpack: false
                            )
                        )
                    )
                    dim: Scalar_LNumber(
                        value: 1
                    )
                )
                args: array(
                )
            )
            args: array(
                0: Arg(
                    value: Scalar_String(
                        value: var_dump
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
        args: array(
            0: Arg(
                value: Scalar_LNumber(
                    value: 5
                )
                byRef: false
                unpack: false
            )
        )
    )
    4: Expr_FuncCall(
        name: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_Closure(
                    static: false
                    byRef: false
                    params: array(
                        0: Param(
                            type: null
                            byRef: false
                            variadic: false
                            name: x
                            default: null
                        )
                    )
                    uses: array(
                    )
                    returnType: null
                    stmts: array(
                        0: Stmt_Return(
                            expr: Expr_Variable(
                                name: x
                            )
                        )
                    )
                )
                args: array(
                    0: Arg(
                        value: Scalar_String(
                            value: id
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    value: Scalar_String(
                        value: var_dump
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
        args: array(
            0: Arg(
                value: Scalar_LNumber(
                    value: 8
                )
                byRef: false
                unpack: false
            )
        )
    )
    5: Expr_FuncCall(
        name: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Expr_FuncCall(
                        name: Expr_Assign(
                            var: Expr_Variable(
                                name: f
                            )
                            expr: Expr_Closure(
                                static: false
                                byRef: false
                                params: array(
                                    0: Param(
                                        type: null
                                        byRef: false
                                        variadic: false
                                        name: x
                                        default: Expr_ConstFetch(
                                            name: Name(
                                                parts: array(
                                                    0: null
                                                )
                                            )
                                        )
                                    )
                                )
                                uses: array(
                                    0: Expr_ClosureUse(
                                        var: f
                                        byRef: true
                                    )
                                )
                                returnType: null
                                stmts: array(
                                    0: Stmt_Return(
                                        expr: Expr_Ternary(
                                            cond: Expr_Variable(
                                                name: x
                                            )
                                            if: null
                                            else: Expr_Variable(
                                                name: f
                                            )
                                        )
                                    )
                                )
                            )
                        )
                        args: array(
                        )
                    )
                    args: array(
                    )
                )
                args: array(
                )
            )
            args: array(
                0: Arg(
                    value: Scalar_String(
                        value: var_dump
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
        args: array(
            0: Arg(
                value: Scalar_LNumber(
                    value: 9
                )
                byRef: false
                unpack: false
            )
        )
    )
    6: Expr_FuncCall(
        name: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Expr_FuncCall(
                        name: Expr_Array(
                            items: array(
                                0: Expr_ArrayItem(
                                    key: null
                                    value: Expr_Variable(
                                        name: obj
                                    )
                                    byRef: false
                                )
                                1: Expr_ArrayItem(
                                    key: null
                                    value: Scalar_String(
                                        value: id
                                    )
                                    byRef: false
                                )
                            )
                        )
                        args: array(
                        )
                    )
                    args: array(
                        0: Arg(
                            value: Scalar_String(
                                value: id
                            )
                            byRef: false
                            unpack: false
                        )
                    )
                )
                args: array(
                    0: Arg(
                        value: Expr_Variable(
                            name: id
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    value: Scalar_String(
                        value: var_dump
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
        args: array(
            0: Arg(
                value: Scalar_LNumber(
                    value: 10
                )
                byRef: false
                unpack: false
            )
        )
    )
    7: Expr_FuncCall(
        name: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_FuncCall(
                    name: Scalar_String(
                        value: id
                    )
                    args: array(
                    )
                )
                args: array(
                    0: Arg(
                        value: Scalar_String(
                            value: id
                        )
                        byRef: false
                        unpack: false
                    )
                )
            )
            args: array(
                0: Arg(
                    value: Scalar_String(
                        value: var_dump
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
        args: array(
            0: Arg(
                value: Scalar_LNumber(
                    value: 12
                )
                byRef: false
                unpack: false
            )
        )
    )
    8: Expr_FuncCall(
        name: Expr_FuncCall(
            name: Expr_FuncCall(
                name: Expr_BinaryOp_Concat(
                    left: Scalar_String(
                        value: i
                    )
                    right: Scalar_String(
                        value: d
                    )
                )
                args: array(
                )
            )
            args: array(
                0: Arg(
                    value: Scalar_String(
                        value: var_dump
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
        args: array(
            0: Arg(
                value: Scalar_LNumber(
                    value: 13
                )
                byRef: false
                unpack: false
            )
        )
    )
    9: Expr_FuncCall(
        name: Expr_FuncCall(
            name: Scalar_String(
                value: \id
            )
            args: array(
                0: Arg(
                    value: Scalar_String(
                        value: var_dump
                    )
                    byRef: false
                    unpack: false
                )
            )
        )
        args: array(
            0: Arg(
                value: Scalar_LNumber(
                    value: 14
                )
                byRef: false
                unpack: false
            )
        )
    )
)
