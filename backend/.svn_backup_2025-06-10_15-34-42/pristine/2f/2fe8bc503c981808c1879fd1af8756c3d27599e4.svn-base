[![Latest Stable Version](https://img.shields.io/packagist/v/phpunit/phpunit-mock-objects.svg?style=flat-square)](https://packagist.org/packages/phpunit/phpunit-mock-objects)
[![Minimum PHP Version](https://img.shields.io/badge/php-%3E%3D%205.6-8892BF.svg?style=flat-square)](https://php.net/)
[![Build Status](https://img.shields.io/travis/sebastian<PERSON>mann/phpunit-mock-objects/master.svg?style=flat-square)](https://travis-ci.org/sebastianbergmann/phpunit-mock-objects)

# PHPUnit_MockObject

**PHPUnit_MockObject** is the default mock object library for PHPUnit.

## Requirements

* PHP 5.6 is required but using the latest version of PHP is highly recommended

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

    composer require phpunit/phpunit-mock-objects

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

    composer require --dev phpunit/phpunit-mock-objects

