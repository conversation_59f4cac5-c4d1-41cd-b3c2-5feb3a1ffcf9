<?php
/** \file rebuild-search-content-image.php
 *
 * 	Ce script est destiné a mettre à jours les images des contenus de la recherche
 *
 */
set_include_path(dirname(__FILE__) . '/../include/');
require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );

$cls_id = isset($argv[1]) && is_numeric($argv[1]) ? $argv[1] : null;

if (is_null($cls_id)) {
	die('aucune classe de sélectionné');
}

// Select
{
	// product
	$prd_select = '
		select cnt_id, cnt_img_id, prd_img_id as img_id
		from search_contents
			join prd_classify on cly_tnt_id=cnt_tnt_id and cnt_id=cly_cnt_id
			join prd_products on prd_tnt_id=cnt_tnt_id and prd_id=cly_prd_id
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_type_id=2
			and prd_img_id!=cnt_img_id
	';

	// users
	$user_select = '
		select cnt_id, cnt_img_id, usr_img_id as img_id
		from search_contents
			join gu_users on usr_tnt_id=cnt_tnt_id and cnt_id=usr_cnt_id
		where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_type_id=3
			and usr_img_id!=cnt_img_id;
	';

	// category
	$cat_select = '
		select t.cnt_id, t.cnt_img_id, t.img_id from (
			select cnt_id, cnt_img_id, (select img_id from prd_cat_images where img_tnt_id=cnt_tnt_id and img_cat_id=cat_id order by img_pos asc limit 1) as img_id
			from search_contents
				join prd_categories on cat_tnt_id=cnt_tnt_id and cat_id=cnt_tag
			where cnt_tnt_id='.$config['tnt_id'].'
				and cnt_type_id=1
		) as t
		where t.cnt_img_id!=t.img_id;
	';


	//store
	$str_select = '
		select t.cnt_id, t.cnt_img_id, t.img_id from (
			select cnt_id, cnt_img_id, (select img_id from dlv_stores_images where img_tnt_id=cnt_tnt_id and img_str_id=str_id order by img_pos asc limit 1) as img_id
			from search_contents
				join dlv_stores on str_tnt_id=cnt_tnt_id and str_id=cnt_tag
			where cnt_tnt_id='.$config['tnt_id'].'
				and cnt_type_id=9
			) as t
		where t.cnt_img_id!=t.img_id;
	';

	// cms
	$cms_select = '
		select t.cnt_id, t.cnt_img_id, t.img_id from (
			select cnt_id, cnt_img_id, (select ci_img_id from cms_img where ci_tnt_id=cnt_tnt_id and ci_cat_id=cat_id order by ci_pos asc limit 1) as img_id
			from search_contents
				join cms_categories on cat_tnt_id=cnt_tnt_id and cat_id=cnt_tag
			where cnt_tnt_id='.$config['tnt_id'].'
				and cnt_type_id=10
			) as t
		where t.cnt_img_id!=t.img_id;
	';
}


$allowed_classes = array(
	CLS_PRODUCT => $prd_select,
	CLS_USER => $user_select,
	CLS_CATEGORY => $cat_select,
	CLS_STORE => $str_select,
	CLS_CMS => $cms_select,
);

if (!in_array($cls_id, array_keys($allowed_classes))) {
	die('Identifiant de classe invalid doit être dans ('.implode(',', array_keys($allowed_classes)).')');
}

$r = ria_mysql_query($allowed_classes[$cls_id]);

if (!$r || !ria_mysql_num_rows($r)) {
	die('Pas de résultat');
}

while ($cnt = ria_mysql_fetch_assoc($r)) {
	if (is_numeric($cnt['img_id'])) {
		search_contents_image_add( $cnt['cnt_id'], $cnt['img_id'] );
	}else{
		search_contents_image_del($cnt['cnt_id']);
	}
}





