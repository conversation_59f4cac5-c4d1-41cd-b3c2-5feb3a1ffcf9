<?php
	/** \file update-priceandquantity-cdiscount.php
	 *	\ingroup crontabs cdiscount
	 *	Ce cron permet de mettre à jour le tarif et la quantité des produits exportés vers CDiscount.
	 *	Seuls les produits dont l'une de ces deux informations n'est pas à jour seront actualisés.
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}
	
	require_once('comparators.inc.php');
	require_once('comparators/ctr.cdiscount.inc.php');
	require_once('tsk.comparators.inc.php');
	require_once('prices.inc.php');

	// Active ou non le mode test
	$mode_test 	= isset($ar_params['test']) && $ar_params['test'] ? true : false;
	
	$all_ctr_CDiscount = array_merge( array(CTR_CDISCOUNT), ctr_cdiscount_partners_get_ria_id() );
	
	foreach( $configs as $config ){
		foreach( $all_ctr_CDiscount as $one_ctr ){
			// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.			
			if( !ctr_comparators_actived($one_ctr) ){
				continue;
			}

			// Utilise le même catalogue que celui sur CDiscount
			if( ctr_cdiscount_partners_use_catalog($one_ctr) ){
				continue;
			}

			$config['tmp_cdiscount_id'] = $one_ctr;

			$update = ctr_cdiscount_offers_update_pricequantity( $mode_test );

			if( $update === false ){
				// Module RiaShoppping plus suivi, plus d'envoi de message
			}elseif( $update === true ){
				continue;
			}

			$prds = $update['prds'];
			$xml  = $update['xml'];

			$zipOffers = ctr_cdiscount_offers_create_zip( $xml, sizeof($prds), $mode_test );
			// si on est pas en mode test, on envoi les messages à CDiscount
			if( !$mode_test ){
				if( trim($xml)!='' ){
					if( !$zipOffers ){
						// Module RiaShoppping plus suivi, plus d'envoi de message
					} else {
						// création de la tache de mise à jour des tarifs pour les produits envoyés
						$ar_exec = array();
						foreach( $prds as $p ){
							if( ($tsk = tsk_comparators_add( $config['tmp_cdiscount_id'], $p, 'update-priceqte' )) ){
								$ar_exec[] = $tsk;
							}
						}
						
						// mise à jour de la date d'exécution des tâches
						tsk_comparators_set_completed( $ar_exec );
						
						// mise à jour de l'identifiant d'import pour toutes les tâches consernées par l'envoi
						tsk_comparators_set_import_id( $ar_exec, $zipOffers );
					}
				}
			} else {
				print 'URL du fichier ZIP des offres : '.$config['site_url'].$zipOffers."\n";
			}
		}
	}