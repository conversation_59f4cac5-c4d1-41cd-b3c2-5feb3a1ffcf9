<?php

	/**	\file export-prd-no-image.php
	 * 
	 * 	Ce fichier export les données contenues dans l'écran "Produits sans image" au format Microsoft Excel
	 * 
	 */

	require_once( $_SERVER['DOCUMENT_ROOT'].'/config.inc.php' );
	require_once( 'excel/PHPExcel.php' );

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_NO_IMG');

	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();
	
	// Détermine les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator("riaStudio")
								 ->setLastModifiedBy("riaStudio")
								 ->setTitle( $tab === 'tabMain' ? _('Export des produits sans image principale') : _('Export des produits sans image secondaire') )
								 ->setSubject( $tab === 'tabMain' ? _('Export des produits sans image principale') : _('Export des produits sans image secondaire') )
								 ->setDescription( $tab === 'tabMain' ? _('Export des produits sans image principale') : _('Export des produits sans image secondaire') )
								 ->setKeywords( $tab === 'tabMain' ? _('export produit sans image principale') : _('export produit sans image secondaire') )
								 ->setCategory("");
	
	
	// Création du document
	$objPHPExcel->getActiveSheet()->setSheetState(PHPExcel_Worksheet::SHEETSTATE_VERYHIDDEN);
	
	// Création de la feuille Excel
	$objWorksheet = $objPHPExcel->createSheet();
	$objWorksheet->setTitle( $tab === 'tabMain' ? _('Produits sans image principale') : _('Produits sans image secondaire') );
	
	// Détermine le nom des colonnes
	$objWorksheet->mergeCells('A1:G1');
	$objWorksheet->setCellValue('A1', $tab === 'tabMain' ? _('Liste des produits sans image principale') : _('Liste des produits sans image secondaire') );
	
	$objWorksheet->setCellValue('A2',  _('Référence'));
	$objWorksheet->setCellValue('B2',  _('Désignation'));
	$objWorksheet->setCellValue('C2',  _('Impressions'));
	$objWorksheet->setCellValue('D2',  _('Ventes'));
	$objWorksheet->setCellValue('E2',  _('Date de création'));
	$objWorksheet->setCellValue('F2',  _('Taux de conversion en %'));
	$objWorksheet->setCellValue('G2',  _('Lien'));
	
	// Police de l'entête du tableau
	$objWorksheet->getStyle('A1:G1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	$objWorksheet->getStyle('A1:G2')->getFont()->setBold(true);
	$objWorksheet->getStyle('A1:G1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet->getStyle('A1:G1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('00009999');
	$objWorksheet->getStyle('A2:G2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('DDDDDD');

	// Intégration des données sur les produits
	if ($prd_count) {
		$line = 3;
		while( $prd = ria_mysql_fetch_assoc($products) ){
			
			$rcat = prd_products_categories_get($prd['id'], true);
			if (! ($rcat && ria_mysql_num_rows($rcat))) continue;
			$cat = ria_mysql_fetch_array($rcat);

			$date_created = strtotime( $prd['date_created'] );
			
			$objWorksheet->setCellValue('A'.$line, $prd['ref']);
			$objWorksheet->setCellValue('B'.$line, $prd['title']);
			$objWorksheet->setCellValue('C'.$line, number_format($prd['hits'], 0, ',', ' '));
			$objWorksheet->setCellValue('D'.$line, number_format($prd['selled_web'], 0, ',', ' '));
			$objWorksheet->setCellValue('E'.$line, PHPExcel_Shared_Date::PHPToExcel( $date_created ));
			$objWorksheet->setCellValue('F'.$line, number_format(100 * $prd['taux'], 2, ',', ' '));
			$objWorksheet->setCellValue('G'.$line,  _('Ajouter des images'));
			$objWorksheet->getStyle('G'.$line)->getFont()->getColor()->setRGB('00009999');
			$objWorksheet->getCell('G'.$line)->getHyperlink()->setUrl(($config['site_url'].'/admin/catalog/product.php?cat=' . $cat['cat'] . '&prd=' . $prd['id'] . '&lng=fr&tab=images'));

			$line++;
		}
	}
		
	// Applique un alignement sur le haut â  toutes les cellules
	$objWorksheet->getStyle('A1:G'.$line)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
	$objWorksheet->getStyle('A3:A'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
	$objWorksheet->getStyle('C3:C'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

	$objWorksheet->getStyle('E2:E'.$line)->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_DATE_DDMMYYYY);
	
	// Fixe la largeur des colonnes
	$objWorksheet->getColumnDimension('A')->setWidth(15);
	$objWorksheet->getColumnDimension('B')->setWidth(60);
	$objWorksheet->getColumnDimension('C')->setWidth(15);
	$objWorksheet->getColumnDimension('D')->setWidth(15);
	$objWorksheet->getColumnDimension('E')->setWidth(15);
	$objWorksheet->getColumnDimension('F')->setWidth(25);
	$objWorksheet->getColumnDimension('G')->setWidth(20);

	// Met des bordures en place
	$objWorksheet->getStyle('A1:G'.($line-1))->getBorders()->applyFromArray(
		array(
			'allborders' => array(
				'style' => PHPExcel_Style_Border::BORDER_THIN,
			)
		)
	);
	
	// Confirgure l'ouverture d'Excel sur la case A1
	$objPHPExcel->setActiveSheetIndex(1);


	// Redirect output to a client web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="'. _(sprintf('produits-sans-image-%s',($tab === 'tabMain' ? 'principale' : 'secondaire'))).'.xls"');
	header('Cache-Control: max-age=0');

	// Ecrit le fichier et le sauvegarde
	$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
	$objWriter->save('php://output');
	exit;
	

