<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AD' => 'Andor',
  'AE' => 'Emira arab ini',
  'AF' => 'Afganistan',
  'AG' => 'Antigua-ek-Barbuda',
  'AI' => 'Anguilla',
  'AL' => 'Albani',
  'AM' => 'Armeni',
  'AO' => 'Angola',
  'AR' => 'Larzantinn',
  'AS' => 'Samoa amerikin',
  'AT' => 'Lostris',
  'AU' => 'Lostrali',
  'AW' => 'Aruba',
  'AZ' => 'Azerbaïdjan',
  'BA' => 'Bosni-Herzegovinn',
  'BB' => 'Barbad',
  'BD' => 'Banglades',
  'BE' => 'Belzik',
  'BF' => 'Burkina Faso',
  'BG' => 'Bilgari',
  'BH' => 'Bahreïn',
  'BI' => 'Burundi',
  'BJ' => 'Benin',
  'BM' => 'Bermid',
  'BN' => 'Brunei',
  'BO' => 'Bolivi',
  'BR' => 'Brezil',
  'BS' => 'Bahamas',
  'BT' => 'Boutan',
  'BW' => 'Botswana',
  'BY' => 'Belaris',
  'BZ' => 'Beliz',
  'CA' => 'Kanada',
  'CD' => 'Repiblik demokratik Kongo',
  'CF' => 'Repiblik Lafrik Santral',
  'CG' => 'Kongo',
  'CH' => 'Laswis',
  'CI' => 'Côte d’Ivoire',
  'CK' => 'Zil Cook',
  'CL' => 'Shili',
  'CM' => 'Kamerounn',
  'CN' => 'Lasinn',
  'CO' => 'Kolonbi',
  'CR' => 'Costa Rica',
  'CU' => 'Cuba',
  'CV' => 'Kap-Ver',
  'CY' => 'Cyprus',
  'CZ' => 'Repiblik Chek',
  'DE' => 'Almagn',
  'DJ' => 'Djibouti',
  'DK' => 'Dannmark',
  'DM' => 'Dominik',
  'DO' => 'Repiblik dominikin',
  'DZ' => 'Alzeri',
  'EC' => 'Ekwater',
  'EE' => 'Estoni',
  'EG' => 'Lezipt',
  'ER' => 'Erythre',
  'ES' => 'Lespagn',
  'ET' => 'Letiopi',
  'FI' => 'Finland',
  'FJ' => 'Fidji',
  'FK' => 'Zil malwinn',
  'FM' => 'Mikronezi',
  'FR' => 'Lafrans',
  'GA' => 'Gabon',
  'GB' => 'United Kingdom',
  'GD' => 'Grenad',
  'GE' => 'Zeorzi',
  'GF' => 'Gwiyann franse',
  'GH' => 'Ghana',
  'GI' => 'Zibraltar',
  'GL' => 'Greenland',
  'GM' => 'Gambi',
  'GN' => 'Gine',
  'GP' => 'Guadloup',
  'GQ' => 'Gine ekwatoryal',
  'GR' => 'Gres',
  'GT' => 'Guatemala',
  'GU' => 'Guam',
  'GW' => 'Gine-Bisau',
  'GY' => 'Guyana',
  'HN' => 'Honduras',
  'HR' => 'Kroasi',
  'HT' => 'Ayti',
  'HU' => 'Ongri',
  'ID' => 'Indonezi',
  'IE' => 'Irland',
  'IL' => 'Izrael',
  'IN' => 'Lenn',
  'IO' => 'Teritwar Britanik Losean Indien',
  'IQ' => 'Irak',
  'IR' => 'Iran',
  'IS' => 'Island',
  'IT' => 'Itali',
  'JM' => 'Zamaik',
  'JO' => 'Zordani',
  'JP' => 'Zapon',
  'KE' => 'Kenya',
  'KG' => 'Kirghizistan',
  'KH' => 'Kambodj',
  'KI' => 'Kiribati',
  'KM' => 'Komor',
  'KN' => 'Saint-Christophe-ek-Niévès',
  'KP' => 'Lakore-dinor',
  'KR' => 'Lakore-disid',
  'KW' => 'Koweit',
  'KY' => 'Zil Kayman',
  'KZ' => 'Kazakstan',
  'LA' => 'Laos',
  'LB' => 'Liban',
  'LC' => 'Sainte-Lucie',
  'LI' => 'Liechtenstein',
  'LK' => 'Sri Lanka',
  'LR' => 'Liberia',
  'LS' => 'Lezoto',
  'LT' => 'Lituani',
  'LU' => 'Luxembourg',
  'LV' => 'Letoni',
  'LY' => 'Libi',
  'MA' => 'Marok',
  'MC' => 'Monako',
  'MD' => 'Moldavi',
  'MG' => 'Madagaskar',
  'MH' => 'Zil Marshall',
  'MK' => 'Masedwann',
  'ML' => 'Mali',
  'MM' => 'Myanmar',
  'MN' => 'Mongoli',
  'MP' => 'Zil Maryann dinor',
  'MQ' => 'Martinik',
  'MR' => 'Moritani',
  'MS' => 'Montsera',
  'MT' => 'Malt',
  'MU' => 'Moris',
  'MV' => 'Maldiv',
  'MW' => 'Malawi',
  'MX' => 'Mexik',
  'MY' => 'Malezi',
  'MZ' => 'Mozambik',
  'NA' => 'Namibi',
  'NC' => 'Nouvel-Kaledoni',
  'NE' => 'Nizer',
  'NF' => 'Lil Norfolk',
  'NG' => 'Nizeria',
  'NI' => 'Nicaragua',
  'NL' => 'Oland',
  'NO' => 'Norvez',
  'NP' => 'Nepal',
  'NR' => 'Nauru',
  'NU' => 'Niowe',
  'NZ' => 'Nouvel Zeland',
  'OM' => 'Oman',
  'PA' => 'Panama',
  'PE' => 'Perou',
  'PF' => 'Polinezi franse',
  'PG' => 'Papouazi-Nouvel-Gine',
  'PH' => 'Filipinn',
  'PK' => 'Pakistan',
  'PL' => 'Pologn',
  'PM' => 'Saint-Pierre-ek-Miquelon',
  'PN' => 'Pitcairn',
  'PR' => 'Porto Rico',
  'PS' => 'Teritwar Palestinn',
  'PT' => 'Portigal',
  'PW' => 'Palau',
  'PY' => 'Paraguay',
  'QA' => 'Katar',
  'RE' => 'Larenion',
  'RO' => 'Roumani',
  'RU' => 'Larisi',
  'RW' => 'Rwanda',
  'SA' => 'Larabi Saoudit',
  'SB' => 'Zil Salomon',
  'SC' => 'Sesel',
  'SD' => 'Soudan',
  'SE' => 'Laswed',
  'SG' => 'Singapour',
  'SH' => 'Sainte-Hélène',
  'SI' => 'Sloveni',
  'SK' => 'Slovaki',
  'SL' => 'Sierra Leone',
  'SM' => 'Saint-Marin',
  'SN' => 'Senegal',
  'SO' => 'Somali',
  'SR' => 'Surinam',
  'ST' => 'São Tome-ek-Prínsip',
  'SV' => 'Salvador',
  'SY' => 'Lasiri',
  'SZ' => 'Swaziland',
  'TC' => 'Zil Tirk ek Caïcos',
  'TD' => 'Tchad',
  'TG' => 'Togo',
  'TH' => 'Thayland',
  'TJ' => 'Tadjikistan',
  'TK' => 'Tokelau',
  'TL' => 'Timor oriantal',
  'TM' => 'Turkmenistan',
  'TN' => 'Tinizi',
  'TO' => 'Tonga',
  'TR' => 'Tirki',
  'TT' => 'Trinite-ek-Tobago',
  'TV' => 'Tuvalu',
  'TW' => 'Taiwan',
  'TZ' => 'Tanzani',
  'UA' => 'Ikrenn',
  'UG' => 'Ouganda',
  'US' => 'Lamerik',
  'UY' => 'Uruguay',
  'UZ' => 'Ouzbekistan',
  'VA' => 'Lata Vatikan',
  'VC' => 'Saint-Vincent-ek-Grenadines',
  'VE' => 'Venezuela',
  'VG' => 'Zil vierz britanik',
  'VI' => 'Zil Vierz Lamerik',
  'VN' => 'Vietnam',
  'VU' => 'Vanuatu',
  'WF' => 'Wallis-ek-Futuna',
  'WS' => 'Samoa',
  'YE' => 'Yemenn',
  'YT' => 'Mayot',
  'ZA' => 'Sid-Afrik',
  'ZM' => 'Zambi',
  'ZW' => 'Zimbabwe',
);
