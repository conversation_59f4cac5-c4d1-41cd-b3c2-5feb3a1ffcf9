CREATE TABLE IF NOT EXISTS prw_competitors (
  cpt_id int(10) NOT NULL,
  cpt_name varchar(75) NOT NULL,
  cpt_lng_code varchar(5) NOT NULL,
  cpt_url varchar(255) NOT NULL,
  cpt_date_created datetime NOT NULL,
  cpt_date_deleted datetime NULL,
  cpt_date_modified timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (cpt_id)
)ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS prw_followed_products (
  pwf_tnt_id int(10) NOT NULL,
  pwf_cpt_id int(10) NOT NULL,
  pwf_prd_id int(10) NOT NULL,
  pwf_cpt_ref varchar(10) NOT NULL,
  pwf_disable BOOLEAN NOT NULL DEFAULT FALSE,
  pwf_date_created datetime NOT NULL,
  pwf_date_lastcheck DATETIME NULL,
  pwf_date_deleted datetime NULL,
  pwf_date_modified timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (pwf_tnt_id,pwf_cpt_id,pwf_prd_id)
)ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS prw_offers (
  ofr_tnt_id int(10) NOT NULL,
  ofr_id int(10) NOT NULL AUTO_INCREMENT,
  ofr_cpt_id int(10) NOT NULL,
  ofr_prd_id int(10) NOT NULL,
  ofr_landedprice  DECIMAL(10,2) NOT NULL,
  ofr_shippingprice  DECIMAL(10,2) NOT NULL,
  ofr_url varchar(29) NOT NULL,
  ofr_date_created datetime NOT NULL,
  ofr_date_deleted datetime NULL,
  ofr_date_modified timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (ofr_id),
  FOREIGN KEY (ofr_tnt_id,ofr_cpt_id,ofr_prd_id) REFERENCES prw_followed_products(pwf_tnt_id,pwf_cpt_id,pwf_prd_id)
)ENGINE=InnoDB;

INSERT INTO `riashop`.`cfg_variables` (`var_code`, `var_name`, `var_desc`, `var_default`, `var_type_id`)
VALUES ('price_watching_active', 'Veille tarifaire activée', 'Si la veille tarifaire est activée', '0', '8');

INSERT INTO `riashop`.`cfg_overrides` (`ovr_tnt_id`, `ovr_wst_id`, `ovr_usr_id`, `ovr_var_code`, `ovr_value`) VALUES ('16', '23', '0', 'price_watching_active', '1');

CREATE TABLE IF NOT EXISTS prw_competitors_tenants (
  ctn_tnt_id int(10) UNSIGNED NOT NULL,
  ctn_cpt_id int(10) NOT NULL,
  ctn_is_active BOOLEAN NOT NULL DEFAULT FALSE,
  ctn_date_activated DATETIME NULL,
  PRIMARY KEY (ctn_tnt_id,ctn_cpt_id),
  FOREIGN KEY (ctn_tnt_id) REFERENCES tnt_tenants(tnt_id),
  FOREIGN KEY (ctn_cpt_id) REFERENCES prw_competitors(cpt_id)
)ENGINE=InnoDB;

INSERT INTO cfg_emails (email_tnt_id, email_wst_id, email_code, email_name, email_desc, email_allow_from, email_allow_to, email_allow_cc, email_allow_bcc, email_from, email_to, email_cc, email_bcc, email_pos) VALUES
(16, 23, 'price-watching', 'Veille tarifaire', 'Recevez un rapport des produits surveiller qui ont subi une modification sur leur tarif chez la concurrence.', 0, 1, 1, 1, '', '<EMAIL>', '<EMAIL>', '', 0);

INSERT INTO cfg_emails (email_tnt_id, email_wst_id, email_code, email_name, email_desc, email_allow_from, email_allow_to, email_allow_cc, email_allow_bcc, email_from, email_to, email_cc, email_bcc, email_pos) VALUES
(1, 1, 'price-watching', 'Veille tarifaire', 'Recevez un rapport des produits surveiller qui ont subi une modification sur leur tarif chez la concurrence.', 0, 1, 1, 1, '', '<EMAIL>', '<EMAIL>', '', 0);

INSERT INTO cfg_emails (email_tnt_id, email_wst_id, email_code, email_name, email_desc, email_allow_from, email_allow_to, email_allow_cc, email_allow_bcc, email_from, email_to, email_cc, email_bcc, email_pos) VALUES
(39, 68, 'price-watching', 'Veille tarifaire', 'Recevez un rapport des produits surveiller qui ont subi une modification sur leur tarif chez la concurrence.', 0, 1, 1, 1, '', '<EMAIL>', '<EMAIL>', '', 0);