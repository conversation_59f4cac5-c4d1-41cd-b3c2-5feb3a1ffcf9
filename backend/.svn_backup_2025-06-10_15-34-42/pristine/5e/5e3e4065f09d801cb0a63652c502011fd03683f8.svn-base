<?php

	/**	Cette page affiche la liste des erratums et permet les actions suivantes :
	 *	- Ajout d'un erratum
	 *	- Suppression d'un ou plusieurs erratums
	 */

	require_once('erratums.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ERRATUM');

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) && isset($_POST['bnr']) && is_array($_POST['bnr']) ){
		foreach( $_POST['bnr'] as $b )
			cat_erratums_del($b);
		header('Location: index.php');
		exit;
	}

	define('ADMIN_PAGE_TITLE', _('Erratums').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
	
	// Chargement de la liste des erratums
	$erratums = cat_erratums_get();
	$erratums_count = ria_mysql_num_rows($erratums);
?>
	<h2><?php print _('Erratums'); ?></h2>

	<form action="index.php" method="post">
		<table class="checklist" id="table-erratums">
			<thead>
				<tr>
					<th id="err-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
					<th id="err-ref"><?php print _('Référence'); ?></th>
					<th id="err-name"><?php print _('Désignation du Produit'); ?></th>
					<th id="err-desc"><?php print _('Erratum'); ?></th>
					<th id="err-date"><?php print _('Publié le'); ?></th>
				</tr>
			</thead>
			<tbody>
				<?php
					if( !$erratums_count ){
						print '<tr><td colspan="5">'._('Aucun erratum').'</td></tr>';
					}else{
						while( $r = ria_mysql_fetch_array($erratums) ){
							print '	<tr>
										<td headers="err-sel"><input type="checkbox" class="checkbox" name="bnr[]" value="'.$r['id'].'" /></td>';
							if( gu_user_is_authorized('_RGH_ADMIN_TOOL_ERRATUM_EDIT') ){
								print '	<td headers="err-ref"><a href="edit.php?id='.$r['id'].'">'.htmlspecialchars($r['prd_ref']).'</a></td>';
							}else{
								print '	<td headers="err-ref">'.htmlspecialchars($r['prd_ref']).'</td>';						
							}
							print '		<td headers="err-name">'.htmlspecialchars($r['prd_name']).'</td>
										<td headers="err-desc">'.htmlspecialchars($r['desc']).'</td>
										<td headers="err-date">'.( $r['date_published'] ? ria_date_format($r['date_published']) : '' ).'</td>
									</tr>';
						}
					}
				?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="5">
						<?php if( $erratums_count && gu_user_is_authorized('_RGH_ADMIN_TOOL_ERRATUM_DEL') ){ ?>
						<input type="submit" name="del" class="btn-del" value="<?php print _('Supprimer'); ?>" title="<?php print _('Supprimer les erratums sélectionnés'); ?>" onclick="return confirmErratumsDelList();" />
						<?php }
						if( gu_user_is_authorized('_RGH_ADMIN_TOOL_ERRATUM_ADD') ){ ?>
						<div class="float-right">
							<input type="submit" name="add" value="<?php print _('Ajouter'); ?>" />
						</div>
						<?php } ?>
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
