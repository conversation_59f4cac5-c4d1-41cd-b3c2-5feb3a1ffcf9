<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

require_once('images.inc.php');
require_once('view.translate.inc.php');

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

if( !isset($_GET['image']) || !isset($_GET['cls_id']) || !isset($_GET['obj_id']) || !is_array($_GET['obj_id'])) {
    print '<script>window.parent.location = \'/admin/documents/images/index.php\'</script>';
    exit;
}

$r_img_obj = img_images_objects_get(
    0, 
    isset($_GET['obj_id'][0]) ? $_GET['obj_id'][0] : 0,
    isset($_GET['obj_id'][1]) ? $_GET['obj_id'][1] : 0,
    isset($_GET['obj_id'][2]) ? $_GET['obj_id'][2] : 0,
    0, 
    $_GET['image'], 
    $_GET['cls_id']
);

if (!$r_img_obj || !ria_mysql_num_rows($r_img_obj)) {
    print '<script>window.parent.location = \'/admin/documents/images/index.php\'</script>';
    exit;
}

$img_obj = ria_mysql_fetch_assoc($r_img_obj);

$r_img = img_images_get($img_obj['id']);
if (!$r_img || !ria_mysql_num_rows($r_img)) {
    print '<script>window.parent.location = \'/admin/documents/images/index.php\'</script>';
    exit;
}
$image = ria_mysql_fetch_assoc($r_img);

$lng = isset($_POST['lng']) && trim($_POST['lng']) 
    ? $_POST['lng'] 
    : (isset($_GET['lng']) && trim($_GET['lng']) ? $_GET['lng'] : false);

$isNotDefaultLng = $lng !== false && $lng != $config['i18n_lng'];

if (isset($_POST['save']) 
    && isset($_POST['alt']) 
    && !img_images_objects_set_alt($_POST['alt'], $lng, 0, 0, 0, 0, $img_obj['imo_id'])
) {
    $error = _('Erreur lors de l\'enregistrement du texte alternatif');
}

$alt = img_images_objects_get_alt($img_obj['imo_id'], $lng);

define('ADMIN_PAGE_TITLE', _('Images') . ' - ' . _('Attribut Alt'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');
define('ADMIN_CLASS_BODY', 'popup_img');
require_once('admin/skin/header.inc.php');
?>

<div class="popup-content">
    <h2><?php print view_image_is_sync( $image ) 
        . ' ' 
        . ($image['src_name'] != '' 
            ? htmlspecialchars($image['src_name'].'.'.$image['type'])
            : _('Nom de l\'image :')
                . ' <abbr title="' . _('Non communiqué') . '">' 
                    . _('N.C.') 
                . '</abbr>'
        );
    ?></h2>

    <?php print view_translate_menu($_SERVER['REQUEST_URI'], isset($_GET['lng']) ? $_GET['lng'] : i18n::getLang(), false); ?>

	<?php
    if( isset($error) ){
        print '<div class="error">'.nl2br( $error ).'</div>';
    }
	?>

    <form class="img-alt-form" method="post">
        <table>
            <caption><?php print _('Propriétés de l\'image') ?></caption>
            <tfoot>
                <tr>
                    <td colspan="2">
                        <input type="submit" name="save" value="<?php print _('Enregistrer') ?>" />
                    </td>
                </tr>
            </tfoot>
            <tbody>
                <tr>
                    <td>
                        <label for="alt"><?php print _('Texte alternatif') ?></label>
                    </td>
                    <td>
                        <input type="text" name="alt" value="<?php print htmlspecialchars($alt) ?>" />
                    </td>
                </tr>
            </tbody>
        </table>
    </form>
</div>

<?php
require_once('admin/skin/footer.inc.php');
?>