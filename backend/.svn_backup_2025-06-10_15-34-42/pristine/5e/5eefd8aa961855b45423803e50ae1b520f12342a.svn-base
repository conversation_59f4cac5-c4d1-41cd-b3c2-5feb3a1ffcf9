digraph sc {
  ratio="compress"
  node [fontsize="11" fontname="Arial" shape="record"];
  edge [fontsize="9" fontname="Arial" color="grey" arrowhead="open" arrowsize="0.5"];

  node_service_container [label="service_container (Psr\Container\ContainerInterface, Symfony\Component\DependencyInjection\ContainerInterface)\nSymfony\\Component\\DependencyInjection\\ContainerInterface\n", shape=record, fillcolor="#eeeeee", style="filled"];
  node_foo [label="foo\nstdClass\n", shape=record, fillcolor="#eeeeee", style="filled"];
  node_bar [label="bar\nstdClass\n", shape=record, fillcolor="#eeeeee", style="filled"];
  node_foo -> node_bar [label="" style="filled"];
}
