<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/datastore/v1/datastore.proto

namespace Google\Cloud\Datastore\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\Datastore\V1\ReadOptions\ReadConsistency instead.
     * @deprecated
     */
    class ReadOptions_ReadConsistency {}
}
class_exists(ReadOptions\ReadConsistency::class);
@trigger_error('Google\Cloud\Datastore\V1\ReadOptions_ReadConsistency is deprecated and will be removed in the next major release. Use Google\Cloud\Datastore\V1\ReadOptions\ReadConsistency instead', E_USER_DEPRECATED);

