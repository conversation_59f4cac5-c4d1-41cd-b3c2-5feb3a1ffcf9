<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/logging/type/http_request.proto

namespace GPBMetadata\Google\Logging\Type;

class HttpRequest
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0af9040a26676f6f676c652f6c6f6767696e672f747970652f687474705f" .
            "726571756573742e70726f746f1213676f6f676c652e6c6f6767696e672e" .
            "747970651a1e676f6f676c652f70726f746f6275662f6475726174696f6e" .
            "2e70726f746f22ef020a0b487474705265717565737412160a0e72657175" .
            "6573745f6d6574686f6418012001280912130a0b726571756573745f7572" .
            "6c18022001280912140a0c726571756573745f73697a6518032001280312" .
            "0e0a0673746174757318042001280512150a0d726573706f6e73655f7369" .
            "7a6518052001280312120a0a757365725f6167656e741806200128091211" .
            "0a0972656d6f74655f697018072001280912110a097365727665725f6970" .
            "180d20012809120f0a0772656665726572180820012809122a0a076c6174" .
            "656e6379180e2001280b32192e676f6f676c652e70726f746f6275662e44" .
            "75726174696f6e12140a0c63616368655f6c6f6f6b7570180b2001280812" .
            "110a0963616368655f686974180920012808122a0a2263616368655f7661" .
            "6c6964617465645f776974685f6f726967696e5f736572766572180a2001" .
            "280812180a1063616368655f66696c6c5f6279746573180c200128031210" .
            "0a0870726f746f636f6c180f20012809429f010a17636f6d2e676f6f676c" .
            "652e6c6f6767696e672e747970654210487474705265717565737450726f" .
            "746f50015a38676f6f676c652e676f6c616e672e6f72672f67656e70726f" .
            "746f2f676f6f676c65617069732f6c6f6767696e672f747970653b6c7479" .
            "7065aa0219476f6f676c652e436c6f75642e4c6f6767696e672e54797065" .
            "ca0219476f6f676c655c436c6f75645c4c6f6767696e675c547970656206" .
            "70726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

