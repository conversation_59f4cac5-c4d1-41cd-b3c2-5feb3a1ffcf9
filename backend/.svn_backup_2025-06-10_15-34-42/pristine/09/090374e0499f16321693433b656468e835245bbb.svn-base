<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  5993181 => 'Eutel',
  5993184 => 'Eutel',
  5993185 => 'Eutel',
  5993186 => 'Eutel',
  5993187 => 'Eutel',
  5993188 => 'Eutel',
  599319 => 'WIC',
  5994161 => 'Satel',
  5994164 => 'WIC',
  5994165 => 'WIC',
  5994166 => 'WIC',
  5994167 => 'WIC',
  5994168 => 'WIC',
  5994169 => 'Satel',
  599700 => 'Digicel',
  599701 => 'Digicel',
  599770 => 'Kla',
  599777 => 'Kla',
  59978 => '<PERSON>gicel',
  599790 => '<PERSON>pie',
  599795 => '<PERSON>pie',
  599796 => '<PERSON>pie',
  599951 => '<PERSON>pie',
  599952 => 'Chippie',
  599953 => 'Chippie',
  599954 => 'Chippie',
  599956 => 'Chippie',
  599957 => 'Chippie',
  599961 => 'CSC',
  599963 => 'GSM Caribbean',
  599965 => 'Digicel',
  599966 => 'Digicel',
  599967 => 'Digicel',
  599968 => 'Digicel',
  599969 => 'Digicel',
);
