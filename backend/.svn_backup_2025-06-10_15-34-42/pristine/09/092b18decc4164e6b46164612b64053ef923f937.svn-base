Implicitly public properties and methods
-----
<?php

abstract class A {
    var $a;
    static $b;
    abstract function c();
    final function d() {}
    static function e() {}
    final static function f() {}
    function g() {}
}
-----
array(
    0: Stmt_Class(
        flags: MODIFIER_ABSTRACT (16)
        name: A
        extends: null
        implements: array(
        )
        stmts: array(
            0: Stmt_Property(
                flags: 0
                props: array(
                    0: Stmt_PropertyProperty(
                        name: a
                        default: null
                    )
                )
            )
            1: Stmt_Property(
                flags: MODIFIER_STATIC (8)
                props: array(
                    0: Stmt_PropertyProperty(
                        name: b
                        default: null
                    )
                )
            )
            2: Stmt_ClassMethod(
                flags: MODIFIER_ABSTRACT (16)
                byRef: false
                name: c
                params: array(
                )
                returnType: null
                stmts: null
            )
            3: Stmt_ClassMethod(
                flags: MODIFIER_FINAL (32)
                byRef: false
                name: d
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
            4: Stmt_ClassMethod(
                flags: MODIFIER_STATIC (8)
                byRef: false
                name: e
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
            5: Stmt_ClassMethod(
                flags: MODIFIER_STATIC | MODIFIER_FINAL (40)
                byRef: false
                name: f
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
            6: Stmt_ClassMethod(
                flags: 0
                byRef: false
                name: g
                params: array(
                )
                returnType: null
                stmts: array(
                )
            )
        )
    )
)