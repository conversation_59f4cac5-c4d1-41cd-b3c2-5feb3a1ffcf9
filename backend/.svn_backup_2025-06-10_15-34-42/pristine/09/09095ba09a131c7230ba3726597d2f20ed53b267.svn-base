language: php

php:
  - 5.6
  - 7.0
  - 7.0snapshot
  - 7.1
  - 7.1snapshot
  - master

env:
  matrix:
    - DRIVER="xdebug"
    - DRIVER="phpdbg"

matrix:
  allow_failures:
    - php: master
  fast_finish: true
  exclude:
    - php: 5.6
      env: DRIVER="phpdbg"

sudo: false

before_install:
  - composer self-update
  - composer clear-cache

install:
  - travis_retry composer update --no-interaction --no-ansi --no-progress --no-suggest --optimize-autoloader --prefer-stable

script:
  - if [[ "$DRIVER" = 'phpdbg' ]]; then phpdbg -qrr vendor/bin/phpunit --coverage-clover=coverage.xml; fi
  - if [[ "$DRIVER" = 'xdebug' ]]; then vendor/bin/phpunit --coverage-clover=coverage.xml; fi

after_success:
  - bash <(curl -s https://codecov.io/bash)

notifications:
  email: false

