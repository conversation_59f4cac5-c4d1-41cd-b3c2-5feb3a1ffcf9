<?php

	/**	\file json-newsletters.php
	 *	Ce fichier retourne une liste de newsletters, éventuellement filtrée en fonction des paramètres fournis.
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWSLETTER');

	// Inclut les fonctions pour les newsletters
	require_once('newsletter.inc.php');
	
	// Défini si la recherche fait partie d'une pagination
	$page = isset($_POST['p']) && is_numeric($_POST['p']) && $_POST['p']>0 ? $_POST['p'] : 1;
	
	// Valeurs par défaut pour le filtre avancé
	if( !isset($_POST['type']) || !is_numeric($_POST['type']) )
		$_POST['type'] = NEWSLETTER_TYPE_ALL;
	
	if( !isset($_POST['ocp']) && !isset($_POST['oc']) )
		$_POST['oc'] = 0;
	else if( !isset($_POST['ocp']) && isset($_POST['oc']) )
		$_POST['oc'] = $_POST['oc'];
	else if( !isset($_POST['oc']) || $_POST['oc'] != $_POST['ocp'] )
		$_POST['oc'] = $_POST['ocp'];

	$dateStart = isset($_POST['date-start']) && isdate($_POST['date-start']) ? $_POST['date-start'] : false;
	$dateEnd = isset($_POST['date-end']) && isdate($_POST['date-end']) ? $_POST['date-end'] : false;
	$seg_id = isset($_POST['seg_id']) && is_numeric($_POST['seg_id']) ? $_POST['seg_id'] : 0;
	
	// Charge la liste sur laquelle on va travailler pour connaître son type
	$cat = nlr_categorie_get( $_POST[ 'oc' ] );
	if( !ria_mysql_num_rows($cat) ){
		header('Location: index.php');
		exit;
	}
	$c = ria_mysql_fetch_array( $cat );	
	
	// Chargement des adresses
	$search_phone = $c['type']=='phone' ? $_REQUEST['search-email'] : '';
	$search_email = $c['type']=='email' ? $_REQUEST['search-email'] : '';
	$remail = nlr_subscribers_get( $_POST['type'], 0, $search_email, $_POST['oc'], 0, $dateStart, $dateEnd, $seg_id, 0, false, $search_phone );

	// Tableau contenant toutes les adresses
	$emails['mail'] = array();
	$emails['nb_mails'] = 0;
	
	if( $remail!=false ){
		// Nombre d'adresse
		$emails['nb_mails'] = ria_mysql_num_rows($remail);
		
		// On se place sur la position de départ de lecture des résultats
		if( $page>1 )
			ria_mysql_data_seek( $remail, ($page-1)*50 );
		
		$count = 0;
		while( $email = ria_mysql_fetch_array($remail) ){
			
			// Intégration d'un tableau contenant les informations sur une adresse
			$emails['mail'][] = array(
				'id'					=> $email['id'],
				'email'					=> $email['email'],
				'email_verified'		=> $email['email-verified'],
				'phone'					=> $email['phone'],
				'cat_id'				=> $email['cat_id'],
				'cat'					=> $email['cat'],
				'inscript_requested'	=> $email['inscript-requested'],
				'inscript_confirmed'	=> $email['inscript-confirmed'],
				'uninscript_requested'	=> $email['uninscript-requested'],
				'uninscript_confirmed'	=> $email['uninscript-confirmed'],
				'usr_id'				=> $email['usr_id'],
				'prf_name'				=> $email['prf_name'],
				'prc_name'				=> $email['prc_name'],
				'usr_orders'			=> $email['usr_orders'],
				'type_cat'				=> $email['type_cat']
			);
			
			// Comptabilise le nombre d'alert ajouté dans la liste
			$count++;
			
			// Si le nombre d'adresse retourné est fixé et atteint, on arrête la lecture du résultats
			if( $count>=50 )
				break;
		}
	}
	
	// Encode le tableau sous format JSON
	print json_encode( $emails );
