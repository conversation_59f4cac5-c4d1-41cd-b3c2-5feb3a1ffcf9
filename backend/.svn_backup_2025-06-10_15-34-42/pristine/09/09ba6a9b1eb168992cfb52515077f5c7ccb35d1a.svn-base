<?php

namespace PhpParser\Parser;

/* GENERATED file based on grammar/tokens.y */
final class Tokens
{
    const YYERRTOK = 256;
    const T_INCLUDE = 257;
    const T_INCLUDE_ONCE = 258;
    const T_EVAL = 259;
    const T_REQUIRE = 260;
    const T_REQUIRE_ONCE = 261;
    const T_LOGICAL_OR = 262;
    const T_LOGICAL_XOR = 263;
    const T_LOGICAL_AND = 264;
    const T_PRINT = 265;
    const T_YIELD = 266;
    const T_DOUBLE_ARROW = 267;
    const T_YIELD_FROM = 268;
    const T_PLUS_EQUAL = 269;
    const T_MINUS_EQUAL = 270;
    const T_MUL_EQUAL = 271;
    const T_DIV_EQUAL = 272;
    const T_CONCAT_EQUAL = 273;
    const T_MOD_EQUAL = 274;
    const T_AND_EQUAL = 275;
    const T_OR_EQUAL = 276;
    const T_XOR_EQUAL = 277;
    const T_SL_EQUAL = 278;
    const T_SR_EQUAL = 279;
    const T_POW_EQUAL = 280;
    const T_COALESCE = 281;
    const T_BOOLEAN_OR = 282;
    const T_BOOLEAN_AND = 283;
    const T_IS_EQUAL = 284;
    const T_IS_NOT_EQUAL = 285;
    const T_IS_IDENTICAL = 286;
    const T_IS_NOT_IDENTICAL = 287;
    const T_SPACESHIP = 288;
    const T_IS_SMALLER_OR_EQUAL = 289;
    const T_IS_GREATER_OR_EQUAL = 290;
    const T_SL = 291;
    const T_SR = 292;
    const T_INSTANCEOF = 293;
    const T_INC = 294;
    const T_DEC = 295;
    const T_INT_CAST = 296;
    const T_DOUBLE_CAST = 297;
    const T_STRING_CAST = 298;
    const T_ARRAY_CAST = 299;
    const T_OBJECT_CAST = 300;
    const T_BOOL_CAST = 301;
    const T_UNSET_CAST = 302;
    const T_POW = 303;
    const T_NEW = 304;
    const T_CLONE = 305;
    const T_EXIT = 306;
    const T_IF = 307;
    const T_ELSEIF = 308;
    const T_ELSE = 309;
    const T_ENDIF = 310;
    const T_LNUMBER = 311;
    const T_DNUMBER = 312;
    const T_STRING = 313;
    const T_STRING_VARNAME = 314;
    const T_VARIABLE = 315;
    const T_NUM_STRING = 316;
    const T_INLINE_HTML = 317;
    const T_CHARACTER = 318;
    const T_BAD_CHARACTER = 319;
    const T_ENCAPSED_AND_WHITESPACE = 320;
    const T_CONSTANT_ENCAPSED_STRING = 321;
    const T_ECHO = 322;
    const T_DO = 323;
    const T_WHILE = 324;
    const T_ENDWHILE = 325;
    const T_FOR = 326;
    const T_ENDFOR = 327;
    const T_FOREACH = 328;
    const T_ENDFOREACH = 329;
    const T_DECLARE = 330;
    const T_ENDDECLARE = 331;
    const T_AS = 332;
    const T_SWITCH = 333;
    const T_ENDSWITCH = 334;
    const T_CASE = 335;
    const T_DEFAULT = 336;
    const T_BREAK = 337;
    const T_CONTINUE = 338;
    const T_GOTO = 339;
    const T_FUNCTION = 340;
    const T_CONST = 341;
    const T_RETURN = 342;
    const T_TRY = 343;
    const T_CATCH = 344;
    const T_FINALLY = 345;
    const T_THROW = 346;
    const T_USE = 347;
    const T_INSTEADOF = 348;
    const T_GLOBAL = 349;
    const T_STATIC = 350;
    const T_ABSTRACT = 351;
    const T_FINAL = 352;
    const T_PRIVATE = 353;
    const T_PROTECTED = 354;
    const T_PUBLIC = 355;
    const T_VAR = 356;
    const T_UNSET = 357;
    const T_ISSET = 358;
    const T_EMPTY = 359;
    const T_HALT_COMPILER = 360;
    const T_CLASS = 361;
    const T_TRAIT = 362;
    const T_INTERFACE = 363;
    const T_EXTENDS = 364;
    const T_IMPLEMENTS = 365;
    const T_OBJECT_OPERATOR = 366;
    const T_LIST = 367;
    const T_ARRAY = 368;
    const T_CALLABLE = 369;
    const T_CLASS_C = 370;
    const T_TRAIT_C = 371;
    const T_METHOD_C = 372;
    const T_FUNC_C = 373;
    const T_LINE = 374;
    const T_FILE = 375;
    const T_COMMENT = 376;
    const T_DOC_COMMENT = 377;
    const T_OPEN_TAG = 378;
    const T_OPEN_TAG_WITH_ECHO = 379;
    const T_CLOSE_TAG = 380;
    const T_WHITESPACE = 381;
    const T_START_HEREDOC = 382;
    const T_END_HEREDOC = 383;
    const T_DOLLAR_OPEN_CURLY_BRACES = 384;
    const T_CURLY_OPEN = 385;
    const T_PAAMAYIM_NEKUDOTAYIM = 386;
    const T_NAMESPACE = 387;
    const T_NS_C = 388;
    const T_DIR = 389;
    const T_NS_SEPARATOR = 390;
    const T_ELLIPSIS = 391;
}
