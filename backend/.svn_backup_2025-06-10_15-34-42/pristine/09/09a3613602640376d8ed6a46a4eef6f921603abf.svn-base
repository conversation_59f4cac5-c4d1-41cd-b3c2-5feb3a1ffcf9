<?php

	/** \file workqueue-rewards.php
	 *	\ingroup tools rewards_workqueue
	 *
	 * 	Ce script est destiné à traiter les tâches contenues dans la file d'attente d'application des points de fidélité.
	 *
	 */

	 set_include_path(dirname(__FILE__) . '/../include/');
	require_once('rewards.inc.php');

	$tnt_arg = isset($argv[1]) && is_numeric($argv[1]) ? $argv[1] : 0;

	$configs = cfg_variables_get_all_tenants( $tnt_arg );
	foreach ($configs as $config) {
		$r_tsk = tsk_rewards_get();
		
		if ($r_tsk) {
			while ($tsk = ria_mysql_fetch_assoc($r_tsk)) {
				tsk_rewards_apply( $tsk['email'], $tsk['usr_ref'] );
			}
		}
	}

