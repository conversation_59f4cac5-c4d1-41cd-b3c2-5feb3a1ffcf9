var currentSearchAutocompleteAjax=false;
(function(b){b.fn.extend({autocomplete:function(a,e){var f=typeof a=="string";e=b.extend({},b.Autocompleter.defaults,{url:f?a:null,data:f?null:a,delay:f?b.Autocompleter.defaults.delay:10,max:e&&!e.scroll?10:150},e);e.highlight=e.highlight||function(c){return c};e.formatMatch=e.formatMatch||e.formatItem;return this.each(function(){new b.Autocompleter(this,e)})},result:function(a){return this.bind("result",a)},search:function(a){return this.trigger("search",[a])},flushCache:function(){return this.trigger("flushCache")},setOptions:function(a){return this.trigger("setOptions",[a])},unautocomplete:function(){return this.trigger("unautocomplete")}});b.Autocompleter=function(F,y){var L={UP:38,DOWN:40,DEL:46,TAB:9,RETURN:13,ESC:27,COMMA:188,PAGEUP:33,PAGEDOWN:34,BACKSPACE:8};var O=b(F).attr("autocomplete","off").addClass(y.inputClass);var E;var P="";var S=b.Autocompleter.Cache(y);var T=0;var A;var z={mouseDownOnSelect:false};var C=b.Autocompleter.Select(y,F,J,z);var Q;b.browser.opera&&b(F.form).bind("submit.autocomplete",function(){if(Q){Q=false;return false}});O.bind((b.browser.opera?"keypress":"keydown")+".autocomplete",function(c){T=1;A=c.keyCode;switch(c.keyCode){case L.UP:c.preventDefault();if(C.visible()){C.prev()}else{D(0,true)}break;case L.DOWN:c.preventDefault();if(C.visible()){C.next()}else{D(0,true)}break;case L.PAGEUP:c.preventDefault();if(C.visible()){C.pageUp()}else{D(0,true)}break;case L.PAGEDOWN:c.preventDefault();if(C.visible()){C.pageDown()}else{D(0,true)}break;case y.multiple&&b.trim(y.multipleSeparator)==","&&L.COMMA:case L.TAB:case L.RETURN:if(J()){c.preventDefault();Q=true;return false}break;case L.ESC:C.hide();break;default:clearTimeout(E);E=setTimeout(D,y.delay);break}}).focus(function(){T++}).blur(function(){T=0;if(!z.mouseDownOnSelect){G()}}).click(function(){if(T++>1&&!C.visible()){D(0,true)}}).bind("search",function(){var d=(arguments.length>1)?arguments[1]:null;function c(g,f){var h;if(f&&f.length){for(var e=0;e<f.length;e++){if(f[e].result.toLowerCase()==g.toLowerCase()){h=f[e];break}}}if(typeof d=="function"){d(h)}else{O.trigger("result",h&&[h.data,h.value])}}b.each(H(O.val()),function(e,f){I(f,c,c)})}).bind("flushCache",function(){S.flush()}).bind("setOptions",function(){b.extend(y,arguments[1]);if("data" in arguments[1]){S.populate()}}).bind("unautocomplete",function(){C.unbind();O.unbind();b(F.form).unbind(".autocomplete")});function J(){var g=C.selected();if(!g){return false}var h=g.result;P=h;if(y.multiple){var i=H(O.val());if(i.length>1){var d=y.multipleSeparator.length;var e=b(F).selection().start;var c,f=0;b.each(i,function(j,k){f+=k.length;if(e<=f){c=j;return false}f+=d});i[c]=h;h=i.join(y.multipleSeparator)}h+=y.multipleSeparator}O.val(h);N();O.trigger("result",[g.data,g.value]);return true}function D(e,d){if(A==L.DEL){C.hide();return}var c=O.val();if(!d&&c==P){return}P=c;c=B(c);if(c.length>=y.minChars){O.addClass(y.loadingClass);if(!y.matchCase){c=c.toLowerCase()}I(c,R,N)}else{K();C.hide()}}function H(c){if(!c){return[""]}if(!y.multiple){return[b.trim(c)]}return b.map(c.split(y.multipleSeparator),function(d){return b.trim(c).length?b.trim(d):null})}function B(c){if(!y.multiple){return c}var e=H(c);if(e.length==1){return e[0]}var d=b(F).selection().start;if(d==c.length){e=H(c)}else{e=H(c.replace(c.substring(d),""))}return e[e.length-1]}function M(d,c){if(y.autoFill&&(B(O.val()).toLowerCase()==d.toLowerCase())&&A!=L.BACKSPACE){O.val(O.val()+c.substring(B(P).length));b(F).selection(P.length,P.length+c.length)}}function G(){clearTimeout(E);E=setTimeout(N,200)}function N(){var c=C.visible();C.hide();clearTimeout(E);K();if(y.mustMatch){O.search(function(d){if(!d){if(y.multiple){var e=H(O.val()).slice(0,-1);O.val(e.join(y.multipleSeparator)+(e.length?y.multipleSeparator:""))}else{O.val("");O.trigger("result",null)}}})}}function R(d,c){if(c&&c.length&&T){K();C.display(c,d);M(d,c[0].value);C.show()}else{N()}}function I(g,c,d){if(!y.matchCase){g=g.toLowerCase()}var e=S.load(g);if(e&&e.length){c(g,e)}else{if((typeof y.url=="string")&&(y.url.length>0)){var f={timestamp:+new Date()};b.each(y.extraParams,function(i,h){f[i]=typeof h=="function"?h():h});if(currentSearchAutocompleteAjax){currentSearchAutocompleteAjax.abort()}currentSearchAutocompleteAjax=$.ajax({mode:"abort",port:"autocomplete"+F.name,dataType:y.dataType,url:y.url,data:b.extend({q:B(g),limit:y.max},f),success:function(i){var h=y.parse&&y.parse(i)||a(i);S.add(g,h);c(g,h)},complete:function(){currentSearchAutocompleteAjax.abort()}})}else{C.emptyList();d(g)}}}function a(g){var d=[];var c=g.split("\n");for(var e=0;e<c.length;e++){var f=b.trim(c[e]);if(f){f=f.split("|");d[d.length]={data:f,value:f[0],result:y.formatResult&&y.formatResult(f,f[0])||f[0]}}}return d}function K(){O.removeClass(y.loadingClass)}};b.Autocompleter.defaults={inputClass:"ac_input",resultsClass:"ac_results",loadingClass:"ac_loading",minChars:1,delay:400,matchCase:false,matchSubset:true,matchContains:false,cacheLength:10,max:100,mustMatch:false,extraParams:{},selectFirst:true,formatItem:function(a){return a[0]},formatMatch:null,autoFill:false,width:0,height:"auto",multiple:false,multipleSeparator:", ",highlight:function(a,d){return a.replace(new RegExp("(?![^&;]+;)(?!<[^<>]*)("+d.replace(/([\^\$\(\)\[\]\{\}\*\.\+\?\|\\])/gi,"\\$1")+")(?![^<>]*>)(?![^&;]+;)","gi"),"<strong>$1</strong>")},scroll:true,scrollHeight:180};b.Autocompleter.Cache=function(j){var l={};var m=0;function a(c,e){if(!j.matchCase){c=c.toLowerCase()}var d=c.indexOf(e);if(j.matchContains=="word"){d=c.toLowerCase().search("\\b"+e.toLowerCase())}if(d==-1){return false}return d==0||j.matchContains}function i(c,d){if(m>j.cacheLength){k()}if(!l[c]){m++}l[c]=d}function n(){if(!j.data){return false}var h={},e=0;if(!j.url){j.cacheLength=1}h[""]=[];for(var g=0,s=j.data.length;g<s;g++){var f=j.data[g];f=(typeof f=="string")?[f]:f;var r=j.formatMatch(f,g+1,j.data.length);if(r===false){continue}var d=r.charAt(0).toLowerCase();if(!h[d]){h[d]=[]}var c={value:r,data:f,result:j.formatResult&&j.formatResult(f)||r};h[d].push(c);if(e++<j.max){h[""].push(c)}}b.each(h,function(o,p){j.cacheLength++;i(o,p)})}setTimeout(n,25);function k(){l={};m=0}return{flush:k,add:i,populate:n,load:function(c){if(!j.cacheLength||!m){return null}if(!j.url&&j.matchContains){var g=[];for(var f in l){if(f.length>0){var d=l[f];b.each(d,function(o,h){if(a(h.value,c)){g.push(h)}})}}return g}else{if(l[c]){return l[c]}else{if(j.matchSubset){for(var e=c.length-1;e>=j.minChars;e--){var d=l[c.substr(0,e)];if(d){var g=[];b.each(d,function(o,h){if(a(h.value,c)){g[g.length]=h}});return g}}}}}return null}}};b.Autocompleter.Select=function(E,D,w,s){var H={ACTIVE:"ac_over"};var A,G=-1,C,x="",a,t;function v(){if(b(".ac_results").length!=0){return}a=b("<div/>").hide().addClass(E.resultsClass).appendTo(document.body);t=b("<ul/>").appendTo(a).mouseover(function(c){if(z(c).nodeName&&z(c).nodeName.toUpperCase()=="LI"){G=b("li",t).removeClass(H.ACTIVE).index(z(c));b(z(c)).addClass(H.ACTIVE)}}).click(function(c){b(z(c)).addClass(H.ACTIVE);w();D.focus();return false}).mousedown(function(){s.mouseDownOnSelect=true}).mouseup(function(){s.mouseDownOnSelect=false});if(E.width>0){a.css("width",E.width)}}function z(d){var c=d.target;while(c&&c.tagName!="LI"){c=c.parentNode}if(!c){return[]}return c}function u(d){A.slice(G,G+1).removeClass(H.ACTIVE);F(d);var e=A.slice(G,G+1).addClass(H.ACTIVE);if(E.scroll){var c=0;A.slice(0,G).each(function(){c+=this.offsetHeight});if((c+e[0].offsetHeight-t.scrollTop())>t[0].clientHeight){t.scrollTop(c+e[0].offsetHeight-t.innerHeight())}else{if(c<t.scrollTop()){t.scrollTop(c)}}}}function F(c){G+=c;if(G<0){G=A.size()-1}else{if(G>=A.size()){G=0}}}function B(c){return E.max&&E.max<c?E.max:c}function y(){if(!t){return this}t.empty();var c=B(C.length);for(var f=0;f<c;f++){if(!C[f]){continue}var d=E.formatItem(C[f].data,f+1,c,C[f].value,x);if(d===false){continue}var e=b("<li/>").html(E.highlight(d,x)).addClass(f%2==0?"ac_even":"ac_odd").appendTo(t)[0];b.data(e,"ac_data",C[f])}A=t.find("li");if(E.selectFirst){A.slice(0,1).addClass(H.ACTIVE);G=0}if(b.fn.bgiframe){t.bgiframe()}}return{display:function(c,d){v();C=c;x=d;y()},next:function(){u(1)},prev:function(){u(-1)},pageUp:function(){if(G!=0&&G-8<0){u(-G)}else{u(-8)}},pageDown:function(){if(G!=A.size()-1&&G+8>A.size()){u(A.size()-1-G)}else{u(8)}},hide:function(){a&&a.hide();A&&A.removeClass(H.ACTIVE);G=-1},visible:function(){return a&&a.is(":visible")},current:function(){return this.visible()&&(A.filter("."+H.ACTIVE)[0]||E.selectFirst&&A[0])},show:function(){if(!a){return this}var c=b(D).offset();a.css({width:typeof E.width=="string"||E.width>0?E.width:b(D).width(),top:c.top+D.offsetHeight,left:c.left}).show();if(E.scroll){t.scrollTop(0);t.css({maxHeight:E.scrollHeight,height:E.height,overflow:"auto"});if(b.browser.msie&&typeof document.body.style.maxHeight==="undefined"){var d=0;A.each(function(){d+=this.offsetHeight});var f=d>E.scrollHeight;t.css("height",f?E.scrollHeight:d);if(!f){A.width(t.width()-parseInt(A.css("padding-left"))-parseInt(A.css("padding-right")))}}var e=0;t.find("li").each(function(){e+=parseInt(b(this).height())});if(e>0){t.css("height",E.height>e?"auto":E.height)}}},selected:function(){var c=A&&A.filter("."+H.ACTIVE).removeClass(H.ACTIVE);return c&&c.length&&b.data(c[0],"ac_data")},emptyList:function(){t&&t.empty()},unbind:function(){a&&a.remove()}}};b.fn.selection=function(n,k){if(n!==undefined){return this.each(function(){if(this.createTextRange){var c=this.createTextRange();if(k===undefined||n==k){c.move("character",n);c.select()}else{c.collapse(true);c.moveStart("character",n);c.moveEnd("character",k);c.select()}}else{if(this.setSelectionRange){this.setSelectionRange(n,k)}else{if(this.selectionStart){this.selectionStart=n;this.selectionEnd=k}}}})}var a=this[0];if(a.createTextRange){var j=document.selection.createRange(),o=a.value,l="<->",m=j.text.length;j.text=l;var p=a.value.indexOf(l);a.value=o;this.selection(p,p+m);return{start:p,end:p+m}}else{if(a.selectionStart!==undefined){return{start:a.selectionStart,end:a.selectionEnd}}}}})(jQuery);

/*$(document).ready(function(){
	if( typeof $("#q")!=undefined&&$("#q").length ){
		try{
			$("#q").autocomplete("/admin/ajax/default/ajax-search.php",{delay:750,minChars:2,matchSubset:1,matchContains:1,max:20,cacheLength:10,autoFill:false,selectFirst:false}).result(function(d,a){
				this.form.submit()
			});
		}catch(b){}
	}
});*/