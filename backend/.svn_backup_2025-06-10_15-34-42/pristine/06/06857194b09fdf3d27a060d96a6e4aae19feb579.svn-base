@import "node_modules/include-media/dist/_include-media.scss";
@import "variables";

$mdc-theme-primary: $dark-color;
$mdc-theme-secondary: $white;
$mdc-theme-on-primary: $white;
$mdc-theme-on-secondary: $white;

$mdc-typography-font-family: 'Montserrat', sans-serif;

$mdc-typography-font-weight-values: (
  thin: 500,
  light: 500,
  regular: 500,
  medium: 500,
  bold: 600,
  black: 600
);

@import "default";

@import "@material/button/mdc-button";
@import "@material/checkbox/mdc-checkbox";
@import "@material/floating-label/mdc-floating-label";
@import "@material/form-field/mdc-form-field";
@import "@material/icon-button/mdc-icon-button";
@import "@material/icon-toggle/mdc-icon-toggle";
@import "@material/line-ripple/mdc-line-ripple";
@import "@material/ripple/mdc-ripple";
@import "@material/textfield/mdc-text-field";
@import "@material/theme/mdc-theme"; 
@import "@material/typography/mdc-typography";

@import "base";
@import "helper";
@import "inputs";
@import "tabstrip";
@import "alerts";
@import "nomenclature";
@import "discount";
@import "autorizations";
@import "tables";
@import "new-customer";
@import "customers";
@import "catalog";
@import "orders";
@import "promotions";
@import "segments";
@import "config";
@import "fdv";
@import "comparator";
@import "stats";
@import "tools";
@import "pop-up";
@import "moderation";
@import "negociations";
@import "options";
@import "view-admin-inc";
@import "addresses";
@import "documents";
@import "linear-raised";
@import "modules/tablesorter";
@import "modules/buttons";
@import "modules/tinymce";

@import "introduction";
@import "main-menu";
