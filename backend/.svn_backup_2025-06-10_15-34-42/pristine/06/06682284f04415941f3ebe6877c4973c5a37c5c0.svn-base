<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/datastore/v1/query.proto

namespace Google\Cloud\Datastore\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A filter on a specific property.
 *
 * Generated from protobuf message <code>google.datastore.v1.PropertyFilter</code>
 */
class PropertyFilter extends \Google\Protobuf\Internal\Message
{
    /**
     * The property to filter by.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyReference property = 1;</code>
     */
    private $property = null;
    /**
     * The operator to filter by.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyFilter.Operator op = 2;</code>
     */
    private $op = 0;
    /**
     * The value to compare the property to.
     *
     * Generated from protobuf field <code>.google.datastore.v1.Value value = 3;</code>
     */
    private $value = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Cloud\Datastore\V1\PropertyReference $property
     *           The property to filter by.
     *     @type int $op
     *           The operator to filter by.
     *     @type \Google\Cloud\Datastore\V1\Value $value
     *           The value to compare the property to.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Datastore\V1\Query::initOnce();
        parent::__construct($data);
    }

    /**
     * The property to filter by.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyReference property = 1;</code>
     * @return \Google\Cloud\Datastore\V1\PropertyReference|null
     */
    public function getProperty()
    {
        return isset($this->property) ? $this->property : null;
    }

    public function hasProperty()
    {
        return isset($this->property);
    }

    public function clearProperty()
    {
        unset($this->property);
    }

    /**
     * The property to filter by.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyReference property = 1;</code>
     * @param \Google\Cloud\Datastore\V1\PropertyReference $var
     * @return $this
     */
    public function setProperty($var)
    {
        GPBUtil::checkMessage($var, \Google\Cloud\Datastore\V1\PropertyReference::class);
        $this->property = $var;

        return $this;
    }

    /**
     * The operator to filter by.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyFilter.Operator op = 2;</code>
     * @return int
     */
    public function getOp()
    {
        return $this->op;
    }

    /**
     * The operator to filter by.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyFilter.Operator op = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setOp($var)
    {
        GPBUtil::checkEnum($var, \Google\Cloud\Datastore\V1\PropertyFilter\Operator::class);
        $this->op = $var;

        return $this;
    }

    /**
     * The value to compare the property to.
     *
     * Generated from protobuf field <code>.google.datastore.v1.Value value = 3;</code>
     * @return \Google\Cloud\Datastore\V1\Value|null
     */
    public function getValue()
    {
        return isset($this->value) ? $this->value : null;
    }

    public function hasValue()
    {
        return isset($this->value);
    }

    public function clearValue()
    {
        unset($this->value);
    }

    /**
     * The value to compare the property to.
     *
     * Generated from protobuf field <code>.google.datastore.v1.Value value = 3;</code>
     * @param \Google\Cloud\Datastore\V1\Value $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkMessage($var, \Google\Cloud\Datastore\V1\Value::class);
        $this->value = $var;

        return $this;
    }

}

