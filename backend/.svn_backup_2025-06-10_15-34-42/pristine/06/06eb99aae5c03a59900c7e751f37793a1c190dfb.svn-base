<?php
namespace Pdf;
/**
 * \ingroup PieceDeVente
 */
class OrderInstallements
{
	/**
	 * objet pdf
	 *
	 * @var pdf $pdf
	 */
	protected $pdf;

	/**
	 * $order
	 *
	 * @var $order
	 */
	protected $order;

	/**
	 * $pay_name
	 *
	 * @var string $pay_name
	 */
	protected $pay_name = '';

	/**
	 * $text_instead_of_date
	 *
	 * @var string $text_instead_of_date
	 */
	protected $text_instead_of_date;

	/**
	 * $installements
	 *
	 * @var array $installements
	 */
	protected $installements = array();

	/**
	 * __construct
	 *
	 * @param \FPDF $pdf
	 * @param mixed $order
	 * @return void
	 */
	public function __construct(\FPDF $pdf, array $order, array & $installments = null, $pay_name = null)
	{
		$this->pdf = $pdf;

		$this->order = $order;

		if (is_null($installments)) {
			$this->loadInstallements();
		} else {
			$this->installements = $installments;
			$this->pay_name = $pay_name;
		}
	}

	/**
	 * withTextInsteadOfDate
	 *
	 * @param mixed $text
	 * @return void
	 */
	public function withTextInsteadOfDate($text)
	{
		$this->text_instead_of_date = $text;

		return $this;
	}

	/**
	 * hasInstallements
	 *
	 * @return void
	 */
	public function hasInstallements()
	{
		return !empty($this->installements);
	}

	/**
	 * Retourne le nom complet du moyen de paiement
	 *
	 * @return string
	 */
	public function paymentName()
	{
		return $this->pay_name;
	}

	/**
	 * Génère le block des échéance
	 *
	 * @param mixed $x
	 * @param mixed $y
	 * @return void
	 */
	public function generate($x = null, $y = null)
	{
		$length = count($this->installements) - 1;
		foreach ($this->installements as $i => $installement) {
			if (!is_null($y)) {
				$this->pdf->setY($y);
			}else{
				$y = $this->pdf->getY();
			}
			if (!is_null($x)) {
				$this->pdf->setX($x);
			}else{
				$x = $this->pdf->getX();
			}
			$date = new \DateTime($installement['expire']);
			$h = 4;
			if ($length == $i && (!is_null($this->text_instead_of_date))) {
				$this->pdf->cell(20, $h, utf8_decode($this->text_instead_of_date), 0, 0, 'L');
			}else{
				$this->pdf->cell(20, $h, utf8_decode('le ' . $date->format('d/m/Y')), 0, 0, 'L');
			}
			$this->pdf->cell(30, $h, utf8_decode($installement['pay_name']), 0, 0, 'R');
			$this->pdf->cell(20, $h, utf8_decode(ria_number_french($installement['rest'])), 0, 0, 'R');
			$y += $h;
		}
	}

	/**
	 * Récupère les échéance de paiement
	 *
	 * @return void
	 */
	protected function loadInstallements()
	{
		$r_installements = ord_installments_get(0, 1, $this->order['id']);
		if ($r_installements && ria_mysql_num_rows($r_installements)) {
			while ($installement = ria_mysql_fetch_assoc($r_installements)) {
				$r_pay = gu_users_payment_types_get(0, $installement['pay']);
				$installement['pay_name'] = '';
				if ($r_pay && ria_mysql_num_rows($r_pay)) {
					$pay = ria_mysql_fetch_assoc($r_pay);
					$installement['pay_name'] = $pay['name'];
					$this->pay_name = gu_users_payment_types_view($pay);
				}
				$this->installements[] = $installement;
			}
		}
	}
}