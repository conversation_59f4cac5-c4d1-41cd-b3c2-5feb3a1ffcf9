/**	\file default.js
 * Ce fichier est inclus dans tout le back-office. Pour des questions de performances, il faut
 * limiter au maximum son contenu. Ne doivent s'y trouver que des éléments de niveau global
 * (pas de traitements spécifiques à une page ou catégorie).
 */

/**	Cette fonction gère le click sur une ligne de tableau de données pour :
 * 	1. mettre à jour la classe facilitant la visualisation des lignes cochées / décochées
 * 	2. activer ou désactiver la case à cocher "sélectionner tout" / "désectionner tout" en fonction des lignes actuellement sélectionnées du tableau.
 * 	@param line balise TR concernée par l'événement click
*/
function lineClick(line){
	var $line = $(line);
	// Toggle selected style
	var checked = $line.hasClass('selected');
	$line[checked ? 'removeClass' : 'addClass']('selected');
	checked = ! checked;
	// Check/uncheck the option for this line
	var inputs = line.getElementsByTagName('input');
	for( var i=0; i<inputs.length; i++ ){
		if( !$(inputs[i]).hasClass('lock') && inputs[i].type=='checkbox' ){
			inputs[i].checked = checked;
		}
	}
}

// fonction qui disabled ou inversement les inputs
function inputsDisabled() {
	$('.js-input-disabled.none').parents('form').each(function(){
		var form = $(this);

		$(this).find('.checkbox, thead [type="checkbox"]').change(function(){
			if (form.find('tbody .checkbox:checked').length ){
				form.find('.js-input-disabled').prop("disabled", false);
				form.find('.js-input-disabled').removeClass('none');
			}else{
				form.find('.js-input-disabled').prop("disabled", true);
				form.find('.js-input-disabled').addClass('none');
			}
		});
	});
}

// Désactive un champ input
function disabled( input ){
	input.attr('readonly', true);
	input.css('background-color', '#dddddd');
	input.css('border-style', 'inset');
}
// Active un champ input
function enabled( input ){
	input.attr('readonly', false);
	input.css('background-color', '');
	input.css('border-style', '');
}
/** Gère l'action de la case à cocher "Tout Cocher" / "Tout décocher"
 *
 * @param {checkbox} chkAll
 */
function checkAllClick(chkAll){

	// Handle the check all/none checkbox
	/*var tables = document.getElementsByTagName('table');
	var table = tables[0];*/
	var parent = chkAll;
	while( parent.tagName!='TABLE' ){
		parent = parent.parentNode;
	}

	var tableBody = parent.getElementsByTagName('tbody');
	var lines = tableBody[0].getElementsByTagName('tr');

	// All options should be checked/unchecked according to the main checkbox state
	if( chkAll.checked ){
		for( var i=0; i<lines.length; i++ )
			if( ! $(lines[i]).hasClass('selected') && !$("input[type='checkbox']",lines[i]).is(':disabled') ){
				lineClick(lines[i]);
			}
	}else{
		for( var i=0; i<lines.length; i++ ){
			if( $(lines[i]).hasClass('selected') && !$("input[type='checkbox']",lines[i]).is(':disabled') ){
				lineClick(lines[i]);
			}
		}
	}
}

/** Cette fonction ajoute des \ devant les caractères \, " et '
 *	@param  text texte à protéger
 *	@return le texte passé en argument, dans lequel les caractères présentant un danger sont précédés d'un antislash
 */
function addslashes(text) {
	 return (text+'').replace(/[\\"']/g, '\\$&').replace(/\u0000/g, '\\0');
}

function htmlspecialchars(ch) {
   return $('<div/>').text(ch).html();
}

// Retire les messages d'erreur et de succès actuellement affichés
function removeMessages() {
	$('.error').remove();
	$('.error-success').remove();
}

/*	Retire les espaces de début et de fin de chaîne
 *	@param str Chaîne de caractère à traiter
 *	@return la chaîne de caractère passée en argument, moins les espaces de début et de fin de chaîne
 */
function trim( str ){
	if( !str ) return '';
	str = str.replace( /^\s/, '' );
	str = str.replace( /\s$/, '' );
	return str;
}

// Détermine si la chaine de caractère passée en paramètre correspond à une date au format jj/mm/aaaa
function validDate( str ){
	if( !str ) return false;
	if( !str.match( /^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4}$/ ) )
		return false;

	// Segmente la chaîne
	var dparts = str.split( '/' );
	for( var i=0; i<dparts.length; i++ ){
		dparts[i] = dparts[i].replace( /^0+/, '' );
		dparts[i] = parseInt(dparts[i]);
		if( isNaN(dparts[i]) ) dparts[i] = 0;
	}

	var mlengths = new Array( 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 );
	if( dparts[2]%4==0 ){
		mlengths[1] = 29;
	}

	// Vérifie la validité du mois
	if( dparts[1]<1 || dparts[1]>12 ){
		return false;
	}

	// Vérifie la validité du jour
	if( dparts[0]<1 || dparts[0]>mlengths[dparts[1]-1] ){
		return false;
	}

	return true;
}

/** Fonction permmettant le formatage d'une date au format dd/mm/YYYY sous la forme dd mois YYYY
 */
function format_date(date){
	var month = new Array( 'Jan.','Fév.', 'Mar.', 'Avr.', 'Mai.', 'Jui.', 'Juil.', 'Aoû.', 'Sep.', 'Oct.', 'Nov.', 'Déc.' );
	var reg = new RegExp("[/]+", "g");

	date = date.toString().split( reg );

	var m = date[1];
	if( date[1].substring( 0, 1 ) == '0' ){
		m = date[1].substring( 1 );
	}

	return date[0] + ' ' + month[ parseInt(m)-1 ] + ' ' + date[2];
}

/** Cette fonction converti une date au format dd/mm/YYYY sous la forme YYYY-mm-dd
 */
function dateparse(date){
	var reg=new RegExp("[/]+", "g");
	date = date.toString().split(reg);

	return date[2]+'-'+date[1]+'-'+date[0];
}

// Détermine si la chaine de caractère passée en paramètre correspond à une heure au format hh:mm
function validHour( str ){
	if( !str ) return false;
	return str.match( /^[0-9]{1,2}:[0-9]{1,2}$/ );
}

// Retourne un booléen indiquant si la chaine passée en paramètre contient une adresse email valide.
function validEmail( str ){
	return str.match( /^[a-z0-9\-\_]+(\.[a-z0-9\-\_]+)*@[a-z0-9\-\_]+(\.[a-z0-9\-\_]+)*$/i );
}

// Retourne vrai si la chaîne de caractère passée en paramètre contient un entier non signé
function validInt( str ){
	return str.match( /^[0-9]+$/ );
}

// Retourne vrai si la chaîne de caractère passée en paramètre contient un nombre à virgule flottante non signé
function validFloat( str ){
	str = str.replace( /\s/g, '' );
	return str.match( /^[0-9]+(\.|,[0-9]+)?$/ );
}

// Complète la fonction parseInt en lui permettant de traiter des nombres mis en forme
function parseFInt(str){
	str = str.replace( /\s/g, '' );
	if( str.match( /^0+$/ ) ) return 0;
	str = str.replace( /^0+/, '' );
	return parseInt(str);
}

// Complète la fonction parseFloat en lui permettant de traiter des nombres mis en forme
function parseFFloat(str){
	return parseFloat( str.replace( /\s/g, '' ).replace( ',', '.' ).replace( /[^0-9\.]/,'') );
}

// Convertie une date et heure sous forme de chaîne en date/heure native
function parseDateTime(str){
	var re = /([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4}) ([0-9]{1,2}):([0-9]{1,2})/;
	var dparts = re.exec( str );
	return new Date( parseFInt(dparts[3]), parseFInt(dparts[2])-1, parseFInt(dparts[1]), parseFInt(dparts[4]), parseFInt(dparts[5]) );
}

/** Prend en charge le formatage d'un nombre
 *	@param {float} number Nombre à formater
 *	@param {int} decimals Nombre de décimales à afficher
 *	@param {string} sep Séparateur de décimales
 *	@param {string} th_sep Séparateur de milliers
 */
function number_format( number, decimals, sep, th_sep ) {
	if( number==='' || isNaN(number) ) return '';
	number = Math.round( number * Math.pow(10, decimals) ) / Math.pow(10, decimals);
	var str_number = number.toString();
	var arr_int = str_number.split(".");
	if( !arr_int[0] ) arr_int[0] = "0";
	if( !arr_int[1] ) arr_int[1] = "";
	if( arr_int[1].length < decimals){
		var nachkomma = arr_int[1];
		for( var i=arr_int[1].length+1; i<=decimals; i++ ){
			nachkomma += "0";
		}
		arr_int[1] = nachkomma;
	}
	if( th_sep!="" && arr_int[0].length>3 ){
		var Begriff = arr_int[0];
		arr_int[0] = "";
		for( var j=3; j<Begriff.length; j+=3 ){
			var Extrakt = Begriff.slice(Begriff.length - j, Begriff.length - j + 3);
			arr_int[0] = th_sep + Extrakt +  arr_int[0] + "";
		}
		var str_first = Begriff.substr(0, (Begriff.length % 3 == 0)?3:(Begriff.length % 3));
		arr_int[0] = str_first + arr_int[0];
	}
	if( decimals==0 ){
		return arr_int[0];
	}else{
		return arr_int[0]+sep+arr_int[1];
	}
}

/** Met en forme une heure au format 00:00
 * 	@param {string} hour Heure à mettre en forme
 */
function formatHour(hour){
	hour = trim(hour);
	if( hour.match( /^[0-9]{1}$/ ) ){
		return '0' + hour + ':00';
	}else if( hour.match( /^[0-9]{2}$/ ) ){
		return hour + ':00';
	}else if( hour.match( /^[0-9]{1}:[0-9]{1}$/ ) ){
		return hour.replace( /^([0-9]{1}):([0-9]{1})$/, '0$1:0$2' );
	}else if( hour.match( /^[0-9]{1}:[0-9]{2}$/ ) ){
		return '0' + hour;
	}else{
		return hour;
	}
}

// Cette fonction étend la méthode toUpperCase en lui permettant de mieux traiter les caractères accentués
function strtoupper(str){
	str = str.replace( /[àáâãäåæ]/g, 'A' );
	str = str.replace( /[ç]/g, 'C' );
	str = str.replace( /[èéêë]/g, 'E' );
	str = str.replace( /[ìíîï]/g, 'I' );
	str = str.replace( /[ð]/g, 'D' );
	str = str.replace( /[ñ]/g, 'N' );
	str = str.replace( /[òóôõöœø]/g, 'O' );
	str = str.replace( /[š]/g, 'S' );
	str = str.replace( /[ùúûü]/g, 'U' );
	str = str.replace( /[ý]/g, 'Y' );
	str = str.replace( /[ž]/g, 'Z' );
	return str.toUpperCase();
}

// Cette fonction permet de vérifier qu'une valeur est bien présente dans un tableau
function inArray( needle, haystack ) {
    var length = haystack.length;

    for( var i = 0; i < length; i++ ){
        if( haystack[i] == needle ){
        	return true;
        }
    }

    return false;
}

/** Gère le maximum de caratères dans les textarea
 *	@param Input Obligatoire, champ de saisie dont le nombre de caractères maximum doit être contrôlé
 *	@param nb Obligatoire, nombre de caractères maximum autorisé dans ce champ
 */
function limit(Input, nb) {
	if( Input.value.length>nb ){
		Input.value = Input.value.substring( 0, nb );
	}
}

// Autorise seulement les nombres dans le champs passé en paramètre
function only_numeric(Input){
	var reg = new RegExp("^[0-9]*$");
	if( !reg.test($(Input).val()) ){
		$(Input).val("");
	}
}

function escapeHtml(text) {
	return $('<div/>').text(text).html();
}

function checkAll( inParent ){
	$(inParent).find('input[type=checkbox]').prop("indeterminate", false).removeAttr('checked');
	$(inParent).find('input[type=checkbox]').attr('checked', 'checked');
	return false;
}

function unCheckAll( inParent ){
	$(inParent).find('input[type=checkbox]').prop("indeterminate", false).removeAttr('checked');
	return false;
}

// Lorsque nous avons une table avec scroll, cette fonction qui permet de vérfifier la largeur du tableau, pour ajouter ou supprimer l'ombre
function verifWidthTable() {
	if( $('div').hasClass('table-cols-changed') ){
		var contentTableToScroll = $('.table-cols-changed');
		var tableToScroll = $('.table-cols-changed table');
	}else{
		var contentTableToScroll = $('.table-layout-large');
		var tableToScroll = $('.table-layout-large table');
	}

	var barShadow = $('.bar-shadow');
	barShadow.height(tableToScroll.height());

	if( tableToScroll.width() <= contentTableToScroll.width() ){
		barShadow.addClass('none');
	}else{
		stateScrollTable(contentTableToScroll, tableToScroll);
	}
}

// Cette fonction permet de vérifier l'état du scroll sur le tableau
function stateScrollTable(contentTableToScroll, tableToScroll) {

	// Si on est au début du tableau, on supprime l'ombre de gauche
	if( contentTableToScroll.scrollLeft() <= 2 ){
		$('.shadow-left').addClass('none');
	}else{
		if( $('.shadow-left').hasClass('none') ){
			$('.shadow-left').removeClass('none');
		}
	}

	// Si on scroll jusqu'au bout du tableau, on supprime l'ombre de droite
	if( contentTableToScroll.scrollLeft() >= (tableToScroll.width() - contentTableToScroll.width()) ){
		$('.shadow-right').addClass('none');
	}else{
		if( $('.shadow-right').hasClass('none') ){
			$('.shadow-right').removeClass('none');
		}
	}
}

/** Retire un paramètre de la queryString contenue dans une url
 *
 * @param {string} key Paramètre à retirer de l'url
 * @param {string} sourceURL Url à traiter
 */
function removeParam(key, sourceURL) {
	var rtn = sourceURL.split("?")[0],
		param,
		params_arr = [],
		queryString = (sourceURL.indexOf("?") !== -1) ? sourceURL.split("?")[1] : "";

	if( queryString !== "" ){
		params_arr = queryString.split("&");

		for( var i = params_arr.length - 1; i>=0; i-- ){
			param = params_arr[i].split("=")[0];

			if( param === key ){
				params_arr.splice( i, 1 );
			}
		}

		rtn = rtn + "?" + params_arr.join("&");
	}

	return rtn;
}


/** Permet de mettre a jour la valeur des inputs d'un champs avancé après recherche d'un compte utilisateur via la popup
 * @param id Identifiant du compte utilisateur sélectionné par la recherche
 * @param email E-mail
 * @param name Libellé du compte utilisateur (peut contenir du HTML)
 * @param input_id Identifiant du champs contenant l'identifiant du compte utilisateur
 * @param input_email Identifiant du champs texte contenant l'email
 */
function default_select_user( id, email, name, input_id, input_email ){
	$('#' + input_email).val( email );
	$('#' + input_id).val( id );
}

/** A partir d'une url passée en argument, extrait les paramètres contenus dans la queryString
 *
 * @param {string} params url à analyser
 * \return les paramètres contenus dans l'url, sous forme de tableau associatif clé => valeur
 */
function getParamsInUrl( params ){
	var match,
		pl     = /\+/g,  // Regex for replacing addition symbol with a space
		search = /([^&=]+)=?([^&]*)/g,
		decode = function (s) { return decodeURIComponent(s.replace(pl, " ")); },
		query  = params;

	var urlParams = {};
	while( match = search.exec(query) ){
		var key = decode( match[1] );

		key = key.replace(/-([a-z])/g, function (g) { return g[1].toUpperCase(); });
		key = key.replace(/_([a-z])/g, function (g) { return g[1].toUpperCase(); });

		urlParams[ key ] = decode( match[2] );
	}

	return urlParams;
}

$(document).ready(
	function(){

		initFixedSubmenu();
		stateSubmenu(true);

		$(window).resize(function(){
			initFixedSubmenu();
		});

		if( typeof $('.list-wst-lng .check-wst') ){
			$('.list-wst-lng .check-wst').each(function(){
				$(this).prop("indeterminate", false).removeAttr('checked');

				var nbCheckbox = $(this).parent().find('.language [type=checkbox]').length;
				var nbChecked  = $(this).parent().find('.language [type=checkbox]:checked').length

				if( nbChecked>0 ){
					if( nbChecked != nbCheckbox ){
						$(this).prop('indeterminate', true);
					}else{
						$(this).attr('checked', 'checked');
					}
				}
			});
		}
		if( typeof $('.riacheckall') ){
			$('.riacheckall').each(function(){
				$(this).prop("indeterminate", false).removeAttr('checked');

				var allCheck = $(this).parents('table');
				allCheck.find('tbody input[type=checkbox]').click(function(){
					allCheck.find('.riacheckall').prop("indeterminate", false).removeAttr('checked');

					var nbCheckbox = allCheck.find('tbody [type=checkbox]').length;
					var nbChecked = allCheck.find('tbody [type=checkbox]:checked').length

					if( nbChecked>0 ){
						if( nbChecked != nbCheckbox ){
							allCheck.find('.riacheckall').prop('indeterminate', true);
						}else{
							allCheck.find('.riacheckall').attr('checked', 'checked');
						}
					}
				});
			});
		}

		// S'il y a un tableau avec scroll horizontal
		if( $('div').hasClass('table-cols-changed') || $('div').hasClass('table-layout-large') ){

			var classBorder = '';
			// Initialisation des variables
			if( $('div').hasClass('table-cols-changed') ){
				var contentTableToScroll = $('.table-cols-changed');
				var tableToScroll = $('.table-cols-changed table');
				classBorder = ' container-border';
			}else{
				var contentTableToScroll = $('.table-layout-large');
				var tableToScroll = $('.table-layout-large table');
			}

			/* SHADOW SUR LES TABLEAUX QUI ONT UN SCROLL */
			// Fonction qui permet de construire des div qui permettent d'afficher l'ombre
			function constructShadow() {
				contentTableToScroll.wrap('<div class="container-shadow'+classBorder+'"></div>');
				$('.container-shadow').prepend('<div class="bar-shadow shadow-left none"></div><div class="bar-shadow shadow-right"></div>');
			}

			constructShadow(); // construction des div qui permettent d'afficher l'ombre
			verifWidthTable(); // appel de la fonction qui permet de vérfifier la largeur du tableau, pour ajouter ou supprimer l'ombre
			stateScrollTable(contentTableToScroll, tableToScroll); // appel de la fonction qui permet de révirfier l'état du scroll sur le tableau

			// détecte lorsqu'on atteint la fin du scroll horizontal d'un tableau
			$(function() {
				contentTableToScroll.scroll( function() {
					stateScrollTable(contentTableToScroll, tableToScroll);
				});
			})

			$(window).resize(function(){
				verifWidthTable();
			});
		}

		/*SUBMENU*/
		// Partie spécifique pour les Types de rapports
		// Ne dois pas rester dans le fichier default.js
		// copy de l'objet location pour travailler dessus
		var $last = false;
		var siteLocation = $("#site-location");
		var cat = $( 'a:nth-child(3)', siteLocation);

		var current_location = $.extend({}, window.location);
		if( current_location.pathname.indexOf('fdv/reports/types/') != -1 ){
			current_location.pathname = current_location.pathname.replace('/types/edit.php', '/edit.php');
		}

		$('#site-submenu a').each(function(k,el){

			var editUrl = el.pathname.replace('index.php', 'edit.php');
			if(el.pathname === current_location.pathname || editUrl === current_location.pathname){

				if( $last ){
					$last.removeClass('ria-admin-ui-submenu--active');
				}

				if (el.search === current_location.search || el.pathname.indexOf('search-suggestions.php') > 0 ) {
					var span = '<span class="ria-admin-ui-submenu--active disabled">'+  $(this).context.innerHTML + '</span>';
					$(this).parent().prepend(span);
					$(this).remove();

					$last = $(this);
				}

			}
		});

		if( !$last ){
			$('#site-submenu a').each(function(k,el){
				if( cat.text().indexOf($(el).text()) !== -1 ){
					$(this).addClass('ria-admin-ui-submenu--active');
					$last = $(this);
					return false;
				}
			});
		}

		var cat = $( 'a:nth-child(2)', siteLocation);
		if( !$last ){
			$('#site-submenu a').each(function(k,el){
				if( cat.text().indexOf($(el).text()) !== -1 ){
					$(this).addClass('ria-admin-ui-submenu--active');
					$last = $(this);
					return false;
				}
			});
		}

		// Gestion des onglets. Rend l'onglet actif non cliquable
		var tabstripSelected = $('.tabstrip > li > .selected').not('.tabstripjax > li > .selected');
		if( !tabstripSelected.is('input') ){
			var tabstripSpan = '<span class="selected">'+  tabstripSelected.text() + '</span>';
			tabstripSelected.parent().prepend(tabstripSpan);
			tabstripSelected.remove();
		}

		// déclenche l'autosize sur les textarea
		autosize(document.querySelectorAll('textarea'));

		// Gestion de l'affichage des actions si une ou plusieurs case à cocher le sont dans le tbody
		inputsDisabled();

		// Empêche les recherches vides
		$('#global-search').submit(function (){
			if( $('#mdc-input').val().trim()=='' ){
				return false;
			}
		});

	}
).delegate( // Gestion mutualisé de la case à cocher générale dans un tableau
	'.riacheckall', 'click', function(){
		if( $(this).is(':checked') ){
			$('.riacheckall').parents('table').find('tbody input[type=checkbox]').attr('checked', 'checked');
		}else{
			$('.riacheckall').parents('table').find('tbody input[type=checkbox]').removeAttr('checked');
		}
	}
).delegate(
	'.riaoncheck', 'click', function(){
		if( !$(this).parents('table').find('tbody [type=checkbox]:checked').length ){
			return false;
		}
	}
).delegate( // Gestion du choix des sites / langues pour les contenus
	'.list-wst-lng .check-wst', 'click', function(){
		if( $(this).is(':checked') ){
			$(this).parent().find('.language [type=checkbox]').attr('checked', 'checked');
		}else{
			$(this).parent().find('.language [type=checkbox]').removeAttr('checked');
		}
	}
).delegate( // Gestion du choix des sites / langues pour les contenus
	'.list-wst-lng .language .checkbox', 'click', function(){
		var checkWebsite = $(this).parents('.list-wst-lng').find('.check-wst');

		checkWebsite.prop("indeterminate", false).removeAttr('checked');

		var nbCheckbox = $(this).parent().find('[type=checkbox]').length;
		var nbChecked  = $(this).parent().find('[type=checkbox]:checked').length

		if( nbChecked>0 ){
			if( nbChecked != nbCheckbox ){
				checkWebsite.prop('indeterminate', true);
			}else{
				checkWebsite.attr('checked', 'checked');
			}
		}
	}
).delegate( // Bouton Dupliquer (une commande). Utilisé sur /orders/orders.php, /orders/order.php et /search/index.php
	'#ord-duplicate', 'click', function(e){
		e.preventDefault();
		var ord = $(this).closest('.ord-actions').find("input[name=ord-id]").val();

		displayPopup('Duplication de commande', '', '/admin/ajax/orders/popup-order-duplication.php?ord_id=' + ord, '', 650, 400);
	}
).delegate( // Bouton commande rapide. Utilisé sur /orders/order.php
	'#quick-order', 'click', function(e){
		e.preventDefault();
		var ord = $(this).closest('.ord-actions').find("input[name=ord-id]").val();

		displayPopup('Commande rapide', '', '/admin/ajax/orders/popup-quick-order.php?ord_id=' + ord, '', 750, 600);
	}
).delegate( // Bouton Modifier (une adresse de facturation/livraison sur la fiche de commande).
	'.order-address-edit-btn', "click", function(event){
		event.preventDefault();

		var $elt = $(this);
		displayPopup($elt.attr('title'), '', $elt.attr('href'), 'hidePopup(' + $elt.data('ord') + ')');
	}
).delegate( // Animation du sous-menu au clic de la flèche
	'.js-animate-submenu', 'click', function(){
		var elt = $(this);
		elt.toggleClass('animate-is-close');
		elt.toggleClass('animate-is-open');

		saveState(); // On enregistre le nouvel état du sous-menu
		stateSubmenu(); // On change l'état du sous menu
	}

);
// Merci de ne pas ajouter de delegate ici qui ne soit pas global. Préferez l'utilisation des fichiers locaux
// à une catégorie ou une page.


{ /* GESTION DU SOUS-MENU */

	// Compare la taille du sous-menu et du contenu de la page
	// Adapte la taille du contenu de la page si le sous-menu est plus grand
	function compareSizeBlocks(){
		$(document).ready(function(){
			$('#site-content').height('auto');

			var col_left_height = $('#site-submenu').outerHeight();
			var col_right_height = $('#site-content').outerHeight();

			if( col_left_height > col_right_height ){
				$('#site-content').height(col_left_height);
			}
			// $(window).resize(function(){
			// 	if( col_left_height > col_right_height ){
			// 		$('#site-content').height(col_left_height);
			// 	}else{
			// 		$('#site-content').height('auto');
			// 	}
			// });
		});
	}

	// Fixe le sous-menu au scroll
	// Modification en fonction de l'état du sous-menu
	function stateSubmenu(noAnimate) {
		var elt = $('.js-animate-submenu');
		var winWidth = $(window).width();
		var mobile = false;

		// Si le menu est affiché en mode mobile
		if (winWidth < 1024 ){
			mobile = true;
		}
		if( elt.hasClass('animate-is-close') ){

			if( noAnimate == true ){
				$('.ria-admin-ui-menu .ria-admin-ui-submenu').css('left', '-200px');
				elt.css('left', '8px');
				if( mobile == true ){
					$('.ria-admin-ui-menu .ria-admin-ui-submenu li').css('opacity', '0');
				}else{
					$('.ria-admin-ui-menu .ria-admin-ui-submenu li').css('opacity', '1');
				}
				$('#site-content').css('margin-left', '20px');
			}else{
				$('.ria-admin-ui-menu .ria-admin-ui-submenu').animate({left: '-200px'});
				elt.animate({left: '8px'});
				if( mobile == true ){
					$('.ria-admin-ui-menu .ria-admin-ui-submenu li').animate({opacity: '0'});
				}else{
					$('.ria-admin-ui-menu .ria-admin-ui-submenu li').animate({opacity: '1'});
				}

				$('#site-content').animate({marginLeft: '20px'});
			}

			elt.prop('title', 'Ouvrir le menu');
		}
		else{
			if( noAnimate == true ){
				$('.ria-admin-ui-menu .ria-admin-ui-submenu').css('left', '0');
				elt.css('left', '188px');
				$('.ria-admin-ui-menu .ria-admin-ui-submenu li').css('opacity', '1');
				$('#site-content').css('margin-left', '200px');
			}else{
				$('.ria-admin-ui-menu .ria-admin-ui-submenu').animate({left: '0'});
				elt.animate({left: '188px'});
				$('.ria-admin-ui-menu .ria-admin-ui-submenu li').animate({opacity: '1'});
				$('#site-content').animate({marginLeft: '200px'});
			}
			elt.prop('title', 'Fermer le menu');

		}
		window.setTimeout( verifWidthTable, 500 );
	}

	// Sauvegarde l'état du sous-menu
	function saveState() {
		$.ajax({
			type: "POST",
			url: '/admin/ajax/config/ajax-state-sub-menu.php',
			dataType: 'json',
			success: function() {
			}
		});
	}

	// Fonction qui vérifie l'état du scroll afin d'adapter le sous-menu
	function scrollFunction() {
		var fixedBlock = jQuery('#site-submenu');
		var submenu = jQuery('#site-submenu');
		var fixedFold = jQuery('.block-submenu');
		var col_left_height = $('#site-submenu').outerHeight();
		var col_right_height = $('#site-content').outerHeight();
		var win = jQuery(window);
		var winHeight = win.height();
		var scroll =  winHeight + win.scrollTop();
		var header = $('.ria-admin-ui-header').outerHeight()+$('.ria-admin-ui-menubar').outerHeight();
		var col_left_height_and_header =  col_left_height + header;
		var col_right_height_and_header =  col_right_height + header;

		// Lorsqu'on ne voit plus le header, on fixe la flèche du sous menu
		if( win.scrollTop() > header ) {
			fixedFold.addClass("fixedFold");
		}else{
			fixedFold.removeClass("fixedFold");
		}

		// Si la fenêtre est redimentionnée, on compare la taille des blocs de sous-menu et de contenu
		$(window).resize(function(){
			compareSizeBlocks();
		});

		// Lorsque le sous-menu est plus grand que la hauteur de la fenêtre
		if( col_left_height_and_header > winHeight && col_left_height > winHeight ){
			// si le sous-menu est plus petit que le contenu de la page
			if( (col_left_height < col_right_height || fixedBlock.hasClass('fixedBottom')) && (scroll > col_left_height_and_header) ){
				fixedBlock.addClass("fixedBottom");

			// si le sous-menu est plus grand que le contenu de la page
			}else{
				fixedBlock.removeClass("fixedBottom");
			}
		}
		// Lorsque le sous-menu est plus petit que la fenetre et que le contenu est plus grand
		// OU que le menu+entête est plus grand que la fenêtre, mais que le menu tout seul est plus petit
		else if( (col_left_height_and_header < winHeight && col_right_height_and_header > winHeight) || (col_left_height_and_header > winHeight && col_left_height < winHeight) ){
			if( (scroll - winHeight) > header ){
				submenu.addClass("fixedTop");
			}else{
				submenu.removeClass("fixedTop");
			}
		}
	}

	// Fixe le sous-menu au scroll
	function initFixedSubmenu(){
		var win = jQuery(window);

		compareSizeBlocks();
		scrollFunction();

		win.on('scroll', scrollFunction); // Lorsqu'on scroll, on vérifie où on est dans la page, pour adapter le sous-menu
	}

}

// Vide un champ d'upload de fichier
function resetFileInput(id){
	$('#'+id).val('');
}

//	Coupe le text fourni à la longueur fournie et ajoute le trail (agit comme strcut() de strings.inc.php)
function strcut(text, length, trail){
	if( typeof trail == 'undefined' ){
		trail = '...';
	}

	text = text.replace(/(<([^>]+)>)/ig,"");
	if(text.length <= length){
		return text;
	}

	text = text.substr(0, length);
	text = text.substr(0, text.lastIndexOf(' ')+1);
	text = text+trail.trim();
	return text;
}


$.reduce = function(arr, fnReduce, valueInitial) {
	if (Array.prototype.reduce) {
		return Array.prototype.reduce.call(arr, fnReduce, valueInitial);
	}

	$.each(arr, function(i, value) {
		valueInitial = fnReduce.call(null, valueInitial, value, i, arr);
	});
	return valueInitial;
};

/** Appelle en Ajax le script serveur qui va réaliser la duplication d'une commande
 *
 * @param {int} ord_id Obligatoire, identifiant de la commande
 * @param {string} ref_input Obligatoire, référence de la nouvelle commande
 * @param {int} user_id Obligatoire, identifiant du compte utilisateur auquel le duplicata sera rattaché
 */
function parent_order_duplicate( ord_id, ref_input, user_id ){
	$.ajax({
		type: 'post',
		url : '/admin/ajax/orders/ajax-order-duplicate.php',
		data: 'ord_id=' + ord_id + '&ref=' + ref_input + '&user=' + user_id + '&admin=1',
		dataType: 'json',
		success: function(res) {
			if (res.code == 100) {
				var location = window.location.href.substring(0, window.location.href.indexOf('/admin'));

				location = location + "/admin/orders/order.php?ord=" + res.new_ord;
				window.location.href = location;
			}
		}
	});
}

/**	Lance l'ouverture de la popup permettant la duplication d'une commande,
 *	elle permet la sélection du compte client auquel la commande va être associée.
 *	@param {int} usr Obligatoire, identifiant du compte utilisateur
 *	@param {int} ord Obligatoire, identifiant de la commande à dupliquer
 *	@param {string} ref Obligatoire, référence de la commande à dupliquer
 */
function ord_duplicate_user( usr, ord, ref ){
    displayPopup('Duplication de commande', '', '/admin/ajax/orders/popup-order-duplication.php?ord_id=' + ord + '&user=' + usr + '&ref=' + ref, '', 650, 400);
    return false;
}

// ECMA-262, Edition 5, 15.4.4.18
// Référence: http://es5.github.io/#x15.4.4.18
if (!Array.prototype.forEach) {

	Array.prototype.forEach = function(callback /*, thisArg*/) {

		var T, k;

		if (this == null) {
		throw new TypeError(' this vaut null ou n est pas défini');
		}

		var O = Object(this);

		var len = O.length >>> 0;

		if (typeof callback !== "function") {
		throw new TypeError(callback + ' n est pas une fonction');
		}

		if (arguments.length > 1) {
		T = arguments[1];
		}

		k = 0;

		while (k < len) {

		var kValue;

		if (k in O) {

			kValue = O[k];

			callback.call(T, kValue, k, O);
		}
		k++;
		}
	};
}

document.addEventListener('DOMContentLoaded', function () {
	var tables = document.querySelectorAll('.table-responsive');

	for (var i = 0; i < tables.length; i++) {
		var table = tables[i];

		let labels = Array.from(table.querySelectorAll('th')).map(function (th) {
			return th.innerText;
		});

		table.querySelectorAll('td').forEach(function (td, i) {
			if ($(td).parents('tfoot').length !== 1) {
				td.setAttribute('data-label', labels[i % labels.length]);
			}
		});
	}
});

/* Permet de modifier la taille des textarea automatiquement en fonction du contenu
 * Source https://github.com/jackmoore/autosize
 */
const map = (typeof Map === "function") ? new Map() : (function () {
	const keys = [];
	const values = [];

	return {
		has: function(key) {
			return keys.indexOf(key) > -1;
		},
		get: function (key) {
			return values[keys.indexOf(key)];
		},
		set: function (key, value) {
			if (keys.indexOf(key) === -1) {
				keys.push(key);
				values.push(value);
			}
		},
		delete: function(key) {
			const index = keys.indexOf(key);
			if (index > -1) {
				keys.splice(index, 1);
				values.splice(index, 1);
			}
		},
	}
})();

let createEvent = function (name) {
	return new Event(name, {bubbles: true});
};

try {
	new Event('test');
} catch(e) {
	// IE does not support `new Event()`
	createEvent = function (name) {
		const evt = document.createEvent('Event');
		evt.initEvent(name, true, false);
		return evt;
	};
}

function assign(ta) {
	if (!ta || !ta.nodeName || ta.nodeName !== 'TEXTAREA' || map.has(ta)) return;

	let heightOffset = null;
	let clientWidth = null;
	let cachedHeight = null;

	function init() {
		const style = window.getComputedStyle(ta, null);

		if (style.resize === 'vertical') {
			ta.style.resize = 'none';
		} else if (style.resize === 'both') {
			ta.style.resize = 'auto';
		}

		if (style.boxSizing === 'content-box') {
			heightOffset = -(parseFloat(style.paddingTop)+parseFloat(style.paddingBottom));
		} else {
			heightOffset = parseFloat(style.borderTopWidth)+parseFloat(style.borderBottomWidth);
		}
		// Fix when a textarea is not on document body and heightOffset is Not a Number
		if (isNaN(heightOffset)) {
			heightOffset = 0;
		}

		update();
	}

	function changeOverflow(value) {
		{
			// Chrome/Safari-specific fix:
			// When the textarea y-overflow is hidden, Chrome/Safari do not reflow the text to account for the space
			// made available by removing the scrollbar. The following forces the necessary text reflow.
			const width = ta.style.width;
			ta.style.width = '0px';
			// Force reflow:
			/* jshint ignore:start */
			ta.offsetWidth;
			/* jshint ignore:end */
			ta.style.width = width;
		}

		ta.style.overflowY = value;
	}

	function getParentOverflows(el) {
		const arr = [];

		while (el && el.parentNode && el.parentNode instanceof Element) {
			if (el.parentNode.scrollTop) {
				arr.push({
					node: el.parentNode,
					scrollTop: el.parentNode.scrollTop,
				})
			}
			el = el.parentNode;
		}

		return arr;
	}

	function resize() {
		if (ta.scrollHeight === 0) {
			// If the scrollHeight is 0, then the element probably has display:none or is detached from the DOM.
			return;
		}

		const overflows = getParentOverflows(ta);
		const docTop = document.documentElement && document.documentElement.scrollTop; // Needed for Mobile IE (ticket #240)

		ta.style.height = '';
		ta.style.height = (ta.scrollHeight+heightOffset)+'px';

		// used to check if an update is actually necessary on window.resize
		clientWidth = ta.clientWidth;

		// prevents scroll-position jumping
		overflows.forEach(function (el) {
			return el.node.scrollTop = el.scrollTop;
		});

		if (docTop) {
			document.documentElement.scrollTop = docTop;
		}
	}

	function update() {
		resize();

		const styleHeight = Math.round(parseFloat(ta.style.height));
		const computed = window.getComputedStyle(ta, null);

		// Using offsetHeight as a replacement for computed.height in IE, because IE does not account use of border-box
		var actualHeight = computed.boxSizing === 'content-box' ? Math.round(parseFloat(computed.height)) : ta.offsetHeight;

		// The actual height not matching the style height (set via the resize method) indicates that
		// the max-height has been exceeded, in which case the overflow should be allowed.
		if (actualHeight < styleHeight) {
			if (computed.overflowY === 'hidden') {
				changeOverflow('scroll');
				resize();
				actualHeight = computed.boxSizing === 'content-box' ? Math.round(parseFloat(window.getComputedStyle(ta, null).height)) : ta.offsetHeight;
			}
		} else {
			// Normally keep overflow set to hidden, to avoid flash of scrollbar as the textarea expands.
			if (computed.overflowY !== 'hidden') {
				changeOverflow('hidden');
				resize();
				actualHeight = computed.boxSizing === 'content-box' ? Math.round(parseFloat(window.getComputedStyle(ta, null).height)) : ta.offsetHeight;
			}
		}

		if (cachedHeight !== actualHeight) {
			cachedHeight = actualHeight;
			const evt = createEvent('autosize:resized');
			try {
				ta.dispatchEvent(evt);
			} catch (err) {
				// Firefox will throw an error on dispatchEvent for a detached element
				// https://bugzilla.mozilla.org/show_bug.cgi?id=889376
			}
		}
	}

	const pageResize = function() {
		if (ta.clientWidth !== clientWidth) {
			update();
		}
	};

	const destroy = (function (style) {
		window.removeEventListener('resize', pageResize, false);
		ta.removeEventListener('input', update, false);
		ta.removeEventListener('keyup', update, false);
		ta.removeEventListener('autosize:destroy', destroy, false);
		ta.removeEventListener('autosize:update', update, false);

		Object.keys(style).forEach(function (key) {
			ta.style[key] = style[key];
		});

		map.delete(ta);
	}).bind(ta, {
		height: ta.style.height,
		resize: ta.style.resize,
		overflowY: ta.style.overflowY,
		overflowX: ta.style.overflowX,
		wordWrap: ta.style.wordWrap,
	});

	ta.addEventListener('autosize:destroy', destroy, false);

	// IE9 does not fire onpropertychange or oninput for deletions,
	// so binding to onkeyup to catch most of those events.
	// There is no way that I know of to detect something like 'cut' in IE9.
	if ('onpropertychange' in ta && 'oninput' in ta) {
		ta.addEventListener('keyup', update, false);
	}

	window.addEventListener('resize', pageResize, false);
	ta.addEventListener('input', update, false);
	ta.addEventListener('autosize:update', update, false);
	ta.style.overflowX = 'hidden';
	ta.style.wordWrap = 'break-word';

	map.set(ta, {
		destroy: destroy,
		update: update,
	});

	init();
}

function destroy(ta) {
	const methods = map.get(ta);
	if (methods) {
		methods.destroy();
	}
}

function update(ta) {
	const methods = map.get(ta);
	if (methods) {
		methods.update();
	}
}

let autosize = null;

// Do nothing in Node.js environment and IE8 (or lower)
if (typeof window === 'undefined' || typeof window.getComputedStyle !== 'function') {
	autosize = function (el) {
		return el;
	};
	autosize.destroy = function (el) {
		return el;
	};
	autosize.update = function (el) {
		return el;
	};
} else {
	autosize = function (el) {
		if (el) {
			Array.prototype.forEach.call(el.length ? el : [el], function (x) {
				return assign(x);
			});
		}
		compareSizeBlocks();
		return el;
	};
	autosize.destroy = function (el) {
		if (el) {
			Array.prototype.forEach.call(el.length ? el : [el], destroy);
		}
		return el;
	};
	autosize.update = function (el) {
		if (el) {
			Array.prototype.forEach.call(el.length ? el : [el], update);
		}
		return el;
	};
}
/* FIN AUTOSIZE TEXTAREA
 */

// pour détection des devices
var devices = {};
	devices.mobile = "screen and (max-width: 767px)";
	devices.notMobile = "screen and (min-width: 768px)";
	devices.tablet = "screen and (min-width: 768px) and (max-width: 1279px)";
	devices.mobileTablet = "screen and (max-width: 1279px)";
	devices.pc = "screen and (min-width: 1280px)";

/**
 * Fonction pour palier au bug viewport plus grand que la surface visible sur les mobiles récents.
 * (navbar du bas qui cache le contenu de la page)
 * @param {*} el : objet jQuery $('');
 */
function mobileHeightFix(el) {
	var wh = $(window).innerHeight();
	el.outerHeight(wh);
}