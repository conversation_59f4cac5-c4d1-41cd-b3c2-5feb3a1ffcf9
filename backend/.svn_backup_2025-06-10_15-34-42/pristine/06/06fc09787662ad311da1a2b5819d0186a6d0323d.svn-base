<?php
/**
 * ChannelCatalogMarketplaceProperty
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * ChannelCatalogMarketplaceProperty Class Doc Comment
 *
 * @category Class
 * @description Model for fetching a channel catalog marketplace property
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class ChannelCatalogMarketplaceProperty implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = 'discriminatorType';

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'channelCatalogMarketplaceProperty';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'name' => '\Swagger\Client\Model\ChannelCatalogMarketplacePropertyName',
        'info' => '\Swagger\Client\Model\BeezUPCommonInfoSummaries',
        'description' => 'string',
        'position' => 'int',
        'read_only' => 'bool',
        'min_length' => 'int',
        'max_length' => 'int',
        'min_items' => 'int',
        'max_items' => 'int',
        'lov_link' => '\Swagger\Client\Model\BeezUPCommonLOVLink3',
        'lov_required' => 'bool',
        'required' => 'bool',
        'offer_id_required' => 'int',
        'visible' => 'bool',
        'type' => '\Swagger\Client\Model\Type',
        'discriminator_type' => '\Swagger\Client\Model\ChannelCatalogMarketplacePropertyDiscriminatorType',
        'pattern' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'name' => null,
        'info' => null,
        'description' => null,
        'position' => null,
        'read_only' => null,
        'min_length' => 'int32',
        'max_length' => 'int32',
        'min_items' => 'int32',
        'max_items' => 'int32',
        'lov_link' => null,
        'lov_required' => null,
        'required' => null,
        'offer_id_required' => null,
        'visible' => null,
        'type' => null,
        'discriminator_type' => null,
        'pattern' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'name' => 'name',
        'info' => 'info',
        'description' => 'description',
        'position' => 'position',
        'read_only' => 'readOnly',
        'min_length' => 'minLength',
        'max_length' => 'maxLength',
        'min_items' => 'minItems',
        'max_items' => 'maxItems',
        'lov_link' => 'lovLink',
        'lov_required' => 'lovRequired',
        'required' => 'required',
        'offer_id_required' => 'offerIdRequired',
        'visible' => 'visible',
        'type' => 'type',
        'discriminator_type' => 'discriminatorType',
        'pattern' => 'pattern'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'name' => 'setName',
        'info' => 'setInfo',
        'description' => 'setDescription',
        'position' => 'setPosition',
        'read_only' => 'setReadOnly',
        'min_length' => 'setMinLength',
        'max_length' => 'setMaxLength',
        'min_items' => 'setMinItems',
        'max_items' => 'setMaxItems',
        'lov_link' => 'setLovLink',
        'lov_required' => 'setLovRequired',
        'required' => 'setRequired',
        'offer_id_required' => 'setOfferIdRequired',
        'visible' => 'setVisible',
        'type' => 'setType',
        'discriminator_type' => 'setDiscriminatorType',
        'pattern' => 'setPattern'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'name' => 'getName',
        'info' => 'getInfo',
        'description' => 'getDescription',
        'position' => 'getPosition',
        'read_only' => 'getReadOnly',
        'min_length' => 'getMinLength',
        'max_length' => 'getMaxLength',
        'min_items' => 'getMinItems',
        'max_items' => 'getMaxItems',
        'lov_link' => 'getLovLink',
        'lov_required' => 'getLovRequired',
        'required' => 'getRequired',
        'offer_id_required' => 'getOfferIdRequired',
        'visible' => 'getVisible',
        'type' => 'getType',
        'discriminator_type' => 'getDiscriminatorType',
        'pattern' => 'getPattern'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['name'] = isset($data['name']) ? $data['name'] : null;
        $this->container['info'] = isset($data['info']) ? $data['info'] : null;
        $this->container['description'] = isset($data['description']) ? $data['description'] : null;
        $this->container['position'] = isset($data['position']) ? $data['position'] : null;
        $this->container['read_only'] = isset($data['read_only']) ? $data['read_only'] : false;
        $this->container['min_length'] = isset($data['min_length']) ? $data['min_length'] : 0;
        $this->container['max_length'] = isset($data['max_length']) ? $data['max_length'] : null;
        $this->container['min_items'] = isset($data['min_items']) ? $data['min_items'] : 1;
        $this->container['max_items'] = isset($data['max_items']) ? $data['max_items'] : 1;
        $this->container['lov_link'] = isset($data['lov_link']) ? $data['lov_link'] : null;
        $this->container['lov_required'] = isset($data['lov_required']) ? $data['lov_required'] : false;
        $this->container['required'] = isset($data['required']) ? $data['required'] : false;
        $this->container['offer_id_required'] = isset($data['offer_id_required']) ? $data['offer_id_required'] : null;
        $this->container['visible'] = isset($data['visible']) ? $data['visible'] : true;
        $this->container['type'] = isset($data['type']) ? $data['type'] : null;
        $this->container['discriminator_type'] = isset($data['discriminator_type']) ? $data['discriminator_type'] : null;
        $this->container['pattern'] = isset($data['pattern']) ? $data['pattern'] : null;

        // Initialize discriminator property with the model name.
        $discriminator = array_search('discriminatorType', self::$attributeMap);
        $this->container[$discriminator] = static::$swaggerModelName;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['name'] === null) {
            $invalidProperties[] = "'name' can't be null";
        }
        if ($this->container['position'] === null) {
            $invalidProperties[] = "'position' can't be null";
        }
        if (($this->container['position'] < 1)) {
            $invalidProperties[] = "invalid value for 'position', must be bigger than or equal to 1.";
        }

        if ($this->container['read_only'] === null) {
            $invalidProperties[] = "'read_only' can't be null";
        }
        if (!is_null($this->container['min_length']) && ($this->container['min_length'] < 0)) {
            $invalidProperties[] = "invalid value for 'min_length', must be bigger than or equal to 0.";
        }

        if (!is_null($this->container['max_length']) && ($this->container['max_length'] < 1)) {
            $invalidProperties[] = "invalid value for 'max_length', must be bigger than or equal to 1.";
        }

        if (!is_null($this->container['min_items']) && ($this->container['min_items'] < 0)) {
            $invalidProperties[] = "invalid value for 'min_items', must be bigger than or equal to 0.";
        }

        if (!is_null($this->container['max_items']) && ($this->container['max_items'] < 1)) {
            $invalidProperties[] = "invalid value for 'max_items', must be bigger than or equal to 1.";
        }

        if ($this->container['required'] === null) {
            $invalidProperties[] = "'required' can't be null";
        }
        if ($this->container['visible'] === null) {
            $invalidProperties[] = "'visible' can't be null";
        }
        if ($this->container['type'] === null) {
            $invalidProperties[] = "'type' can't be null";
        }
        if ($this->container['discriminator_type'] === null) {
            $invalidProperties[] = "'discriminator_type' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['name'] === null) {
            return false;
        }
        if ($this->container['position'] === null) {
            return false;
        }
        if ($this->container['position'] < 1) {
            return false;
        }
        if ($this->container['read_only'] === null) {
            return false;
        }
        if ($this->container['min_length'] < 0) {
            return false;
        }
        if ($this->container['max_length'] < 1) {
            return false;
        }
        if ($this->container['min_items'] < 0) {
            return false;
        }
        if ($this->container['max_items'] < 1) {
            return false;
        }
        if ($this->container['required'] === null) {
            return false;
        }
        if ($this->container['visible'] === null) {
            return false;
        }
        if ($this->container['type'] === null) {
            return false;
        }
        if ($this->container['discriminator_type'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets name
     *
     * @return \Swagger\Client\Model\ChannelCatalogMarketplacePropertyName
     */
    public function getName()
    {
        return $this->container['name'];
    }

    /**
     * Sets name
     *
     * @param \Swagger\Client\Model\ChannelCatalogMarketplacePropertyName $name name
     *
     * @return $this
     */
    public function setName($name)
    {
        $this->container['name'] = $name;

        return $this;
    }

    /**
     * Gets info
     *
     * @return \Swagger\Client\Model\BeezUPCommonInfoSummaries
     */
    public function getInfo()
    {
        return $this->container['info'];
    }

    /**
     * Sets info
     *
     * @param \Swagger\Client\Model\BeezUPCommonInfoSummaries $info info
     *
     * @return $this
     */
    public function setInfo($info)
    {
        $this->container['info'] = $info;

        return $this;
    }

    /**
     * Gets description
     *
     * @return string
     */
    public function getDescription()
    {
        return $this->container['description'];
    }

    /**
     * Sets description
     *
     * @param string $description Indicate the description of the property
     *
     * @return $this
     */
    public function setDescription($description)
    {
        $this->container['description'] = $description;

        return $this;
    }

    /**
     * Gets position
     *
     * @return int
     */
    public function getPosition()
    {
        return $this->container['position'];
    }

    /**
     * Sets position
     *
     * @param int $position Indicate the position of the property in the display group
     *
     * @return $this
     */
    public function setPosition($position)
    {

        if (($position < 1)) {
            throw new \InvalidArgumentException('invalid value for $position when calling ChannelCatalogMarketplaceProperty., must be bigger than or equal to 1.');
        }

        $this->container['position'] = $position;

        return $this;
    }

    /**
     * Gets read_only
     *
     * @return bool
     */
    public function getReadOnly()
    {
        return $this->container['read_only'];
    }

    /**
     * Sets read_only
     *
     * @param bool $read_only Indicate if the value cannot be changed. This is used for example for ebay token that should not be changed.
     *
     * @return $this
     */
    public function setReadOnly($read_only)
    {
        $this->container['read_only'] = $read_only;

        return $this;
    }

    /**
     * Gets min_length
     *
     * @return int
     */
    public function getMinLength()
    {
        return $this->container['min_length'];
    }

    /**
     * Sets min_length
     *
     * @param int $min_length Indicates the minimum size of the property value
     *
     * @return $this
     */
    public function setMinLength($min_length)
    {

        if (!is_null($min_length) && ($min_length < 0)) {
            throw new \InvalidArgumentException('invalid value for $min_length when calling ChannelCatalogMarketplaceProperty., must be bigger than or equal to 0.');
        }

        $this->container['min_length'] = $min_length;

        return $this;
    }

    /**
     * Gets max_length
     *
     * @return int
     */
    public function getMaxLength()
    {
        return $this->container['max_length'];
    }

    /**
     * Sets max_length
     *
     * @param int $max_length Indicates the maximum size of the property value
     *
     * @return $this
     */
    public function setMaxLength($max_length)
    {

        if (!is_null($max_length) && ($max_length < 1)) {
            throw new \InvalidArgumentException('invalid value for $max_length when calling ChannelCatalogMarketplaceProperty., must be bigger than or equal to 1.');
        }

        $this->container['max_length'] = $max_length;

        return $this;
    }

    /**
     * Gets min_items
     *
     * @return int
     */
    public function getMinItems()
    {
        return $this->container['min_items'];
    }

    /**
     * Sets min_items
     *
     * @param int $min_items Indicates the minimum item count of the property value.
     *
     * @return $this
     */
    public function setMinItems($min_items)
    {

        if (!is_null($min_items) && ($min_items < 0)) {
            throw new \InvalidArgumentException('invalid value for $min_items when calling ChannelCatalogMarketplaceProperty., must be bigger than or equal to 0.');
        }

        $this->container['min_items'] = $min_items;

        return $this;
    }

    /**
     * Gets max_items
     *
     * @return int
     */
    public function getMaxItems()
    {
        return $this->container['max_items'];
    }

    /**
     * Sets max_items
     *
     * @param int $max_items Indicates the maximum item count of the property value
     *
     * @return $this
     */
    public function setMaxItems($max_items)
    {

        if (!is_null($max_items) && ($max_items < 1)) {
            throw new \InvalidArgumentException('invalid value for $max_items when calling ChannelCatalogMarketplaceProperty., must be bigger than or equal to 1.');
        }

        $this->container['max_items'] = $max_items;

        return $this;
    }

    /**
     * Gets lov_link
     *
     * @return \Swagger\Client\Model\BeezUPCommonLOVLink3
     */
    public function getLovLink()
    {
        return $this->container['lov_link'];
    }

    /**
     * Sets lov_link
     *
     * @param \Swagger\Client\Model\BeezUPCommonLOVLink3 $lov_link lov_link
     *
     * @return $this
     */
    public function setLovLink($lov_link)
    {
        $this->container['lov_link'] = $lov_link;

        return $this;
    }

    /**
     * Gets lov_required
     *
     * @return bool
     */
    public function getLovRequired()
    {
        return $this->container['lov_required'];
    }

    /**
     * Sets lov_required
     *
     * @param bool $lov_required Indicates if the property value must be in the list of value.
     *
     * @return $this
     */
    public function setLovRequired($lov_required)
    {
        $this->container['lov_required'] = $lov_required;

        return $this;
    }

    /**
     * Gets required
     *
     * @return bool
     */
    public function getRequired()
    {
        return $this->container['required'];
    }

    /**
     * Sets required
     *
     * @param bool $required Indicate if the property is required or not
     *
     * @return $this
     */
    public function setRequired($required)
    {
        $this->container['required'] = $required;

        return $this;
    }

    /**
     * Gets offer_id_required
     *
     * @return int
     */
    public function getOfferIdRequired()
    {
        return $this->container['offer_id_required'];
    }

    /**
     * Sets offer_id_required
     *
     * @param int $offer_id_required Indicates the offer identifier required to configure this property.
     *
     * @return $this
     */
    public function setOfferIdRequired($offer_id_required)
    {
        $this->container['offer_id_required'] = $offer_id_required;

        return $this;
    }

    /**
     * Gets visible
     *
     * @return bool
     */
    public function getVisible()
    {
        return $this->container['visible'];
    }

    /**
     * Sets visible
     *
     * @param bool $visible Indicates if this property should be displayed in the configuration page.
     *
     * @return $this
     */
    public function setVisible($visible)
    {
        $this->container['visible'] = $visible;

        return $this;
    }

    /**
     * Gets type
     *
     * @return \Swagger\Client\Model\Type
     */
    public function getType()
    {
        return $this->container['type'];
    }

    /**
     * Sets type
     *
     * @param \Swagger\Client\Model\Type $type type
     *
     * @return $this
     */
    public function setType($type)
    {
        $this->container['type'] = $type;

        return $this;
    }

    /**
     * Gets discriminator_type
     *
     * @return \Swagger\Client\Model\ChannelCatalogMarketplacePropertyDiscriminatorType
     */
    public function getDiscriminatorType()
    {
        return $this->container['discriminator_type'];
    }

    /**
     * Sets discriminator_type
     *
     * @param \Swagger\Client\Model\ChannelCatalogMarketplacePropertyDiscriminatorType $discriminator_type discriminator_type
     *
     * @return $this
     */
    public function setDiscriminatorType($discriminator_type)
    {
        $this->container['discriminator_type'] = $discriminator_type;

        return $this;
    }

    /**
     * Gets pattern
     *
     * @return string
     */
    public function getPattern()
    {
        return $this->container['pattern'];
    }

    /**
     * Sets pattern
     *
     * @param string $pattern Channel catalog marketplace setting value format validation regular expression
     *
     * @return $this
     */
    public function setPattern($pattern)
    {
        $this->container['pattern'] = $pattern;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


