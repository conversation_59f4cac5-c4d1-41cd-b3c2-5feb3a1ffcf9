<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:project="http://www.example.com/schema/projectwithxsd"
    xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd
                        http://www.example.com/schema/projectwithxsd http://www.example.com/schema/projectwithxsd/project-1.0.xsd">

    <parameters>
        <parameter key="project.parameter.foo">BAR</parameter>
    </parameters>

    <services>
        <service id="project.service.foo" class="BAR" />
    </services>

    <project:bar />

</container>
