{"name": "simplesamlphp/simplesamlphp-module-discopower", "description": "Fancy tabbed discovery service with filtering capabilities where SPs can have different sets of metadata listed", "type": "simplesamlphp-module", "keywords": ["simplesamlphp", "discopower", "discovery"], "license": "LGPL-3.0-or-later", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"preferred-install": {"simplesamlphp/simplesamlphp": "source", "*": "dist"}}, "autoload": {"psr-4": {"SimpleSAML\\modules\\discopower\\": "lib/"}}, "autoload-dev": {"psr-4": {"SimpleSAML\\Test\\Utils\\": "vendor/simplesamlphp/simplesamlphp/tests/Utils"}}, "require": {"php": ">=5.6", "simplesamlphp/composer-module-installer": "~1.1", "webmozart/assert": "~1.4 <1.6"}, "require-dev": {"simplesamlphp/simplesamlphp": "^1.17", "phpunit/phpunit": "~5.7"}, "support": {"issues": "https://github.com/tvdijen/simplesamlphp-module-discopower/issues", "source": "https://github.com/tvdijen/simplesamlphp-module-discopower"}}