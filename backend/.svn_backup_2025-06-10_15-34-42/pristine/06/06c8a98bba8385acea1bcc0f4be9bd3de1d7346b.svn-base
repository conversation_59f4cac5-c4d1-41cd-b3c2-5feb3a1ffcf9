<?php

	/**	\file index.php
	 *	Cette page affiche la liste des Actualités
	 */

	require_once('news.inc.php');
	require_once('websites.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWS');

	$url = '';
	if( isset($_GET['lng']) && in_array(strtolower($_GET['lng']), $config['i18n_lng_used']) ){
		$url .= '?lng='.$_GET['lng'];
	}
	
	if( isset($_GET['archived']) ){
		$url .= ( $url!='' ? '&archived=' : '?archived=' ).$_GET['archived'];
	}
		
	if(isset($_GET['wst'])){
		if($_GET['wst']=='all') 
			$_SESSION['websitepicker'] = '0';
		else
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
		
		header('Location: index.php'.$url );
		exit;
	}

	// Bouton Enregistrer (les positions)
	if( isset($_POST['save-pos']) ){
		if( isset($_POST['pos']) && is_array($_POST['pos']) && sizeof($_POST['pos']) ){
			foreach( $_POST['pos'] as $news_id => $pos ){
				if( !is_numeric($pos) || $pos < 0 ){
					$pos = null;
				}

				news_update_pos( $news_id, $pos );
			}
		}

		header('Location: /admin/tools/news/index.php'.$url);
		exit;
	}
	
	$wst_id = isset($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ? $_SESSION['websitepicker'] : 0;
	$_GET['lng'] = isset($_GET['lng']) && in_array( strtolower($_GET['lng']), $config['i18n_lng_used'] ) ? $_GET['lng'] : $config['i18n_lng'];

	if( !isset($_GET['archived']) )
		$_GET['archived'] = false;

	// Bouton Ajouter
	if( isset($_POST['add']) && isset($_POST['name']) ){
		header('Location: edit.php?name='.urlencode(trim($_POST['name'])).'&archived='.$_GET['archived']);
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) && isset($_POST['news']) && is_array($_POST['news']) ){
		foreach( $_POST['news'] as $n )
			news_del($n);
		header('Location: index.php?archived='.$_GET['archived']);
		exit;
	}

	$can_move = gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_MOVE');

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Actualités').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');

	if (isset($_GET['cat']) && news_categories_exists($_GET['cat'])) {
		$cat = ria_mysql_fetch_assoc( news_categories_get($_GET['cat']) );
		?><h2><?php print _('Actualités :'); ?> <?php print htmlspecialchars($cat['name']); ?><a href="/admin/tools/news/categories/edit.php?cat=<?php print $cat['id']; ?>" class="edit-cat"><?php echo _("Modifier cette catégorie"); ?></a></h2><?php
	}else{
		?><h2><?php print _('Actualités'); ?><?php if ($_GET['archived']) print ' '._('archivées').' ' ?></h2><?php
	}
	
	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();
	
	// Catégorie d'actualités
	$cat_param = !isset($_GET['cat']) || !is_numeric($_GET['cat']) ? 0 : $_GET['cat'];
	
	// Charge la liste des actualités
	$news = news_get( 0, false, $_GET['archived'], $cat_param, $wst_id, $lng, false );

?>
	<input type="hidden" name="archived" id="archived" value="<?php print $_GET['archived']; ?>" />
	<?php
		print view_websites_selector( $wst_id, true, 'riapicker', true );

		
		print view_translate_menu( 'index.php?wst='.$wst_id.( isset($_GET['archived']) ? '&archived='.$_GET['archived'] : '' ), $lng );
	?>

	<form action="index.php?archived=<?php print $_GET['archived']; ?>" method="post">
		<table id="table-tools-news" class="checklist">
			<thead>
				<tr>
					<th id="news-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
					<th id="news-name"><?php print _('Titre'); ?></th>
					<th id="news-pub-date"><?php print _('Publier le'); ?></th>
					<th id="news-pub"><?php print _('Publiée ?'); ?></th>
					<?php if( $can_move ){ ?>
					<th id="news-pos"><?php print _('Position'); ?></th>
					<?php } ?>
				</tr>
			</thead>
			<tbody>
				<?php
					if( !$news || !ria_mysql_num_rows($news) ){
				?>
				<tr><td colspan="<?php print $can_move? '5': '4'; ?>"><?php print _('Aucune actualité'); ?></td></tr>
				<?php
					}else{
						while( $r = ria_mysql_fetch_assoc($news) ){
				?>
				<tr>
					<td headers="news-sel">
						<input type="checkbox" class="checkbox" name="news[]" value="<?php print $r['id']; ?>" />
					</td>
					<td headers="news-name">
						<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_VIEW') ){ ?>
						<a href="/admin/tools/news/edit.php?news=<?php print $r['id']; ?>&amp;archived=<?php print $_GET['archived']; ?>"><?php print $r['id']; ?> - <?php print htmlspecialchars($r['name']); ?></a>
						<?php }else{ ?>
						<?php print $r['id']; ?> - <?php print htmlspecialchars($r['name']); ?>
						<?php } ?>
					</td>
					<td headers="news-pub-date"><?php print ria_date_format($r['publish_date']); ?></td>
					<td headers="news-pub"><?php print $r['published'] ? 'Oui' : 'Non'; ?></td>
					<?php if( $can_move ){ ?>
					<td headers="news-pos"><input class="number" type="text" name="pos[<?php print $r['id']; ?>]" value="<?php print $r['pos']; ?>" /></td>
					<?php } ?>
				</tr>
				<?php
						}
					}
				?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="<?php print $can_move? '5': '4'; ?>">
						<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_DEL') && ria_mysql_num_rows($news)>0 ){ ?>
						<input type="submit" name="del" class="btn-del" value="<?php print _('Supprimer'); ?>" onclick="return newsConfirmDelList()" />
						<?php } ?>
						
						<div class="float-right">
							<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_ADD') ){ ?>
							<label for="name"><?php print _('Ajouter une actualité :'); ?></label>
							<input type="text" name="name" id="name" maxlength="75" />
							<input type="submit" name="add" value="<?php print _('Ajouter'); ?>" />
							<?php }
							if( ria_mysql_num_rows($news)>0 ){ ?>
							<input type="submit" title="<?php print _('Enregistrer les positions'); ?>" value="<?php print _('Enregistrer'); ?>" class="" name="save-pos" />
							<?php } ?>
						</div>
						
					</td>
				</tr>
				
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>