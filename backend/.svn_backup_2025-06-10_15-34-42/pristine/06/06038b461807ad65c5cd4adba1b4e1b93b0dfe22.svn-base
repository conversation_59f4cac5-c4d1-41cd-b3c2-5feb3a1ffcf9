<?php 
/**
 * \ingroup dam
 * @{		
 * \page api-documents-file-get Chargement
 *
 * Cette fonction permet de récupérer un document
 *
 *	 \code
 *		GET /documents/file/
 *	 \endcode
 *	
 * @param id Obligatoire, identifiant du document
 *	
 * @return le document sans l'entête de réponse en JSON en cas de succès.
 *@}
*/
switch( $method ){
	case 'get': 	
		if( isset($_GET['id']) && doc_documents_exists($_GET['id'])){
			
			if( is_file($config['doc_dir'].'/'.$_GET['id']) ){
				readfile($config['doc_dir'].'/'.$_GET['id']);
			} 

		}
		exit;
		break;

	/** @{@}
 	 * @{
	 * \page api-documents-file-add Ajout
	 *
	 * Cette fonction ajoute le fichier d'une image.
	 *
	 * \code
	 *		POST /documents/file/
	 * \endcode
	 * 	
	 * @param $doc Obligatoire, le fichier du document
	 * @param $tmp_name Obligatoire, le nom du fichier a ajouter
	 * @param $type Obligatoire, identifiant du type de document
	 * @param $filename Obligatoire, le type du document
	 * @param $name Obligatoire, la désignation du document (ne peut pas être vide)
	 * @param $desc Obligatoire, La description du document
	 *	
	 * @return id, Identifiant du documents
	 * @}
	*/

	case 'add':

		if( !isset($_FILES['doc']['tmp_name']) ){
			throw new Exception("Paramètre invalide. (doc)");
		}


		if( !isset($_POST['type']) ){
			throw new Exception("Paramètres invalide. (type)");
		}

		if( !isset($_POST['filename']) ){
			throw new Exception("Paramètres invalide. (filename)");
		}

		if( !isset($_POST['name']) ){
			throw new Exception("Paramètres invalide. (name)");
		}

		if( !isset($_POST['desc']) ){
			throw new Exception("Paramètres invalide. (desc)");
		}

		
		$result = false;
		$new_id = doc_documents_add($_POST['type'], $_FILES['doc']['tmp_name'], $_POST['filename'], $_POST['name'], $_POST['desc']);
		if ($new_id != null && $new_id > 0) {
			$content["id"] = $new_id;
			$result = true;
		}

		break;

}