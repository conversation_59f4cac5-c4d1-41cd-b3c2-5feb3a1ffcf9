
if (something) {
}

for (i = 0; i < 10; i++) {
}

while (true) {
    for (i = 0; i < 10; i++) {
    }

    if (something) {
    }

    do {
    } while (true);
}

if (one) {
} else (two) {
} else if (three) {
}

if (one) {
} else (two) {
} else if (three) {
}

switch (blah) {
    case 'one':
        if (blah) {
            // There are no spaces before break.
        }
    break;

    default:
        if (blah) {
            // There are no spaces before break.
        }
    break;
}

switch (blah) {
    case 'one':
        if (blah) {
            // There are no spaces before break.
        }
    break;

    default:
        if (blah) {
            // Code here.
        }
}

for (i = 0; i < 10; i++) {
    if (blah) {
    }

    break;
}

while (true) {
    for (i = 0; i < 10; i++) {
        if (something) {
        }
    }

    do {
        alert(i);
    } while (true);
}

for (i = 0; i < 10; i++) {
    if (blah) {
    }
}

var x = {
    a: function () {
        if (blah) {
        }

    },
};

if (one) {
}
// else if something
else if (two) {
} // else do something
else {
}
