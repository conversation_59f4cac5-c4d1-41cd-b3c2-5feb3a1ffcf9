<?php
	// Sofinco
	/**
	 *	Ce module permet les paiement avec Sofinco
	 *
	 *	Variables de config utilisées :
	 *	url_payment			:	Url de la page paiement
	 *	url_payment_success	:	Url de la page paiement effectué
	 *
	 *	Variables de config à personnaliser (pour la prod uniquement) :
	 *	spplus_key_test		:	Clé pour la signature (en mode test)
	 *	spplus_key			:	Clé pour la signature
	 *	spplus_site_id		:	Identifiant du site
	 *
	 *	Ces infos sont disponibles dans l'inteface SystemPay en ligne (Paramétrages > Boutique > %boutique%}
	 *	La signature est visible dans > Certificats (il faut valider les tests pour obtenir le certificat de production)
	 *
	 *	@{
	 */

	require_once('PaymentExternal.inc.php');
	require_once('ord.installments.inc.php');
	require_once('NetAffiliation.inc.php');

	/**	\brief Cette classe simplifie l'intégration du paiement à crédit Sofinco
	 */
	class Sofinco extends PaymentExternal {

		private static $Instance; ///< Instance

		private $ord_id = false;

		/**
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lève une exception contenant une description de l'erreur
		 * \return Rien car il y a une redirection à la fin
		 */
		public static function doPayment() {
			return self::getInstance()->_doPayment();
		}

		/**
		 * Renvoie le singleton
		 * \return Le singleton
		 */
		public static function getInstance() {
			if (! self::$Instance) self::$Instance = new self;
			return self::$Instance;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lève une exception contenant une description de l'erreur
		 *	@return L'instance
		 */
		public static function getPaymentResult() {
			return self::getInstance()->_getPaymentResult();
		}

		/**
		 * Génération du formulaire affichant les cartes disponibles celui-ci redirigera l'utilisateur vers la banque.
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * \return Le formulaire html
		 */
		public function _doPayment() {
			global $config;

			$orderId = $this->ord_id ? $this->ord_id : $this->getOrderId();
			$amount = $this->getOrderAmount();


			// récupère la commande
			$rord = ord_orders_get(0, $orderId);
			if( !$rord || !ria_mysql_num_rows($rord) ) throw new Exception('Impossible de récupérer la commande.');
			$ord = ria_mysql_fetch_array($rord);

			// récupère l'utilisateur
			$rusr = gu_users_get($ord['user']);
			if( !$rusr || !ria_mysql_num_rows($rusr) ) throw new Exception('Impossible de récupérer l\'utilisateur de la commande.');
			$usr = ria_mysql_fetch_array($rusr);

			$param = array();

			// champs obligatoires
			$param['q6'] = $this->getMerchantId(); // Id site
			$param['p0'] = 'PANIER'; // Fonction du module crédit
			$param['ti'] = $orderId; // N° de commande du client
			$param['s3'] = number_format($amount,2,",",""); // Montant de la commande

			$adr1 = $usr['address1'];
			$adr2 = $usr['address2'];

			if (strlen($adr1) > 32) {
				$adr1 = strcut( $adr1, 32, '' );
				$adr2 = trim( str_replace($adr1, '', $usr['address1']) ).' '.$adr2;
			}

			if (strlen($adr2) > 32) {
				$adr2 = strcut( $adr2, 32, '' );
			}

			// champs facultatif
			$param['i4'] = $usr['title_id']; // Civilité du client
			$param['i1'] = urlencode(str_remove_accents($usr['adr_lastname'])); // Nom du client
			$param['i2'] = urlencode(str_remove_accents($usr['adr_firstname'])); // Prénom du client
			// $param['d1'] = ; // Date de naissance du client
			$param['a1'] = urlencode(str_remove_accents($adr1)); // Adresse de facturation du client
			$param['a2'] = urlencode(str_remove_accents($adr2)); // Complément d’adresse (bâtiment, étage…)
			$param['a3'] = urlencode($usr['zipcode']); // Code postal
			$param['a5'] = urlencode(str_remove_accents($usr['city'])); // Ville
			$param['a9'] = urlencode($usr['email']); // Adresse e-mail client
			$param['p5'] = urlencode($config['site_url'].$config['sofinco_return']); // Adresse e-mail client
			if( is_numeric($usr['phone']) ){
				$param['a6'] = urlencode($usr['phone']); // Téléphone client
			}

			$p = array();
			foreach( $param as $k => $v ){
				$p[] = $k.'='.utf8_decode($v); // les valeurs doivents être en ISO
			}

			mail( '<EMAIL>', ( $this->getContext() == PaymentExternal::CONTEXT_DEV ? '[TEST] ' : '' ).'Sofinco - Access payment ['.$config['tnt_id'].']', $config['sofinco_url'].'?'.implode('&',$p) );

			header('Location: '.$config['sofinco_url'].'?'.implode('&',$p));
			exit;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	\return L'instance
		 */
		public function _getPaymentResult() {
			global $config;

			mail( '<EMAIL>', ( $this->getContext() == PaymentExternal::CONTEXT_DEV ? '[TEST] ' : '' ).'Sofinco - return payment ['.$config['tnt_id'].']', print_r($_GET, true) );

			// c5	Nombre d’échéance	Numérique
			// c6	Montant de l’échéance	Numérique
			// ml	« établissement prêteur SOFINCO »	String

			if( !isset($_GET['ti']) || !is_numeric( $_GET['ti'] ) ) {
				throw new Exception( "Sofinco : le numéro de commande n'est pas valide." );
			}
			$orderId = $_GET['ti'];

			if( !isset($_GET['c3']) || !is_numeric($_GET['c3']) ){
				throw new Exception( "Sofinco : la réponse de sofinco ne semble pas correcte." );
			}

			ord_orders_pay_type_set($orderId, _PAY_SOFINCO);

			switch( $_GET['c3'] ){
				case 0: // envoi OPC vers le Client
				case 2: // envoi OPC vers le Client

					// Véfifie l'état de la commande
					$state = ord_orders_get_state($orderId);
					if ($state === false || $state != _STATE_BASKET) throw new exception("Sofinco : La commande $orderId semble déjà avoir été traitée ! (state = $state)");

					// enregistrement des informations de crédit
					$amount = str_replace(',','.', $_GET['s3']);
					$rate = str_replace(',','.', $_GET['c4']);
					$length = str_replace(',','.', $_GET['c5']);
					if( !ord_payment_credits_add( $orderId, $_GET['c2'], $amount, $rate, ABO_PERIOD_MONTH, $length, utf8_encode($_GET['ml']) ) ){
						throw new Exception("Sofinco : erreur lors de l'enregistrement des données ".print_r($_GET, true));
					}

					ord_orders_update_status($orderId, _STATE_WAIT_PAY);
					ord_orders_update_status($orderId, _STATE_PAY_WAIT_CONFIRM);

					// Confirmation de la commande à NetAffiliation
					$affi = new NetAffiliation();
					$affi->getOnlinePaymentTag( $orderId );

					break;
				case 1:
				default: // refusé
					// Annulation de la commande si elle n'est pas au statut panier ou panier enregistré
					$state_id = ord_orders_get_state( $orderId );

					if (!in_array($state_id, array(_STATE_BASKET, _STATE_BASKET_SAVE))) {
						ord_orders_update_status($orderId, _STATE_CANCEL_USER);
					}

					throw new Exception( "Sofinco : le financement de la commande à été refusé." );
					break;
			}
		}

		/**
		 *	Cette fonction returne le merchant_id
		 */
		public function getMerchantId(){
			global $config;
			return $config['sofinco_merchant_id'];
		}

		/**
		 *	Permet de surcharger le ord_id
		 *	\param $ord_id Obligatoire, identifiant de commande à utiliser
		 */
		public function setOrdId( $ord_id ){
			$this->ord_id = $ord_id;
		}

		/**
		 * Permet de récupérer l'accroche en fonction d'un montant donné
		 * Le résultat est mis en cache pendant 24h.
		 */
		public function getInfo($amount, $length=false){
			if( !is_numeric($amount) ) return false;

			global $config,$memcached;

			$p = array();
			$p[] = 'p0=WSACCROCHE';
			$p[] = 'q6='.$this->getMerchantId();
			$p[] = 'p4='.number_format($amount,2,',','');
			if( is_numeric($length) ){
				$p[] = 'd1='.$length;
			}

			$memkey = 'sofinco_'.implode('&',$p);
			if( $kid = $memcached->get($memkey) ){
				return $kid;
			}

			$file = file_get_contents( $config['sofinco_url'].'?'.implode('&',$p) );

			if( empty($file) ){
				return false;
			}

			$xml = simplexml_load_string($file);
			if( !($C_RETOUR = $xml->C_RETOUR) ){
				return false;
			}

			switch($C_RETOUR->__toString()){
				case '00': // traitement réussi
					$return = array();

					if( $tmp = $xml->SIMU->NB_ECHEANCES ){
						$return['nb_echeances'] = $tmp->__toString();
					}

					if( $tmp = $xml->SIMU->MT_ECHEANCE_SANS_ASSURANCE ){
						$return['echeances'] = str_replace(',','.',$tmp->__toString());
					}

					if( $tmp = $xml->SIMU->LIB_MT_CREDIT_RECALCULE ){
						$return['montant'] = $tmp->__toString();
					}

					if( $tmp = $xml->SIMU->LIB_TX_TEG_AN ){
						$return['taux'] = $tmp->__toString();
					}

					if( $tmp = $xml->ML->LIB_MENTION_SANITAIRE ){
						$return['mention'] = $tmp->__toString();
					}

					break;
				case '01': // Paramètres manquants ou incorrect (erreurs sur le contrôle de surface, information mal envoyée)
					$return = 'Les paramètres sont incorrectes';
					break;
				case '02': // Erreur service (service de indisponible)
					$return = 'Service indisponible';
					break;
				case '02': // Aucune offre trouvée ou échéance incorrecte
					$return = 'Aucune offre disponible pour cette simulation.' ;
					break;
			}

			$memcached->set( $memkey, $return, 3600*24 );

			return $return;
		}

		public function getSimuLink($amount){
			if( !is_numeric($amount) ) return false;
			global $config;

			return $config['sofinco_url'].'?q6='.$this->getMerchantId().'&p0=PRODUIT&p4='.number_format($amount,2,',','');
		}
	}

