<?php

class PRODUCT_LIST
{

    /**
     * @var string $Status
     */
    protected $Status = null;

    /**
     * @var ArrayOfProduct $Products
     */
    protected $Products = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return string
     */
    public function getStatus()
    {
      return $this->Status;
    }

    /**
     * @param string $Status
     * @return PRODUCT_LIST
     */
    public function setStatus($Status)
    {
      $this->Status = $Status;
      return $this;
    }

    /**
     * @return ArrayOfProduct
     */
    public function getProducts()
    {
      return $this->Products;
    }

    /**
     * @param ArrayOfProduct $Products
     * @return PRODUCT_LIST
     */
    public function setProducts($Products)
    {
      $this->Products = $Products;
      return $this;
    }

}
