<?php
	require_once('orders.inc.php');
	require_once('ord.returns.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_RETURN');
	
	unset($error, $success);
	
	// Création retour
	if( isset($_POST['return-submit']) ){
		if( !isset($_POST['return-order']) ){
			$error = _('Certaines données n\'ont pas été transmises.');
		}else{
			$id = $_POST['return-order'];

			// Dans le cas où il ne s'agit pas d'un identifiant qui a été transmis, on récupère tout ce qu'il y a avant le tiret éventuel
			if( !is_numeric($id) ){ 
				$id = substr( $id, 0, strpos($id, ' - ') );
			}

			$r_ord = ord_orders_get(0, $id);
			if( !$r_ord || !ria_mysql_num_rows($r_ord) ){
				$error = _('Cette référence de commande n\'existe pas.');
			}else{
				$order = ria_mysql_fetch_assoc( $r_ord );

				if( !in_array($order['state_id'], ord_returns_orders_states_allowed_get()) ){
					$error = _('Cette commande ne peut pas faire l\'objet d\'un retour car elle n\'a pas encore été expédiée.');
				}elseif( !ord_returns_keeped_products_get_count($id) ){
					$error = _('Tous les produits de cette commande font déjà l\'objet d\'un retour.');
				}else{
					header('Location: return.php?ret=0&ord='.$id);
					exit;
				}
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Gestion des retours').' - '._('Commandes'));
	require_once('admin/skin/header.inc.php');

	if (isset($error)) print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	if (isset($success)) print '<div class="success">'.nl2br(htmlspecialchars($success)).'</div>';
?>

<h2><?php print _('Gestion des retours')?></h2>

<p><?php print _('Cette interface va vous permettre de traiter l\'ensemble des retours. Il est très important de bien valider les différents états de retour, car vos clients seront avertis par email de la progression de leur retour.')?></p>

<form action="returns.php" method="get">
	<p><?php print _('Vous avez la possibilité de rechercher un retour en renseignant son numéro ci-contre :'); ?>
	<input type="text" name="ref" id="ref" maxlength="16" /> <input class="btn-action" type="submit" value="<?php print _('Rechercher')?>" /></p>
</form>


<dl>
	<dt><a href="returns.php"><?php printf(_('Tous les retours (%d)'), number_format(ord_returns_states_get_count(0)))?></a></dt>
	<dd><?php print _('Vous trouverez ici tous les retours validés, quel que soit leur état de traitement.')?></dd>
</dl>

<?php
	$states = ord_returns_states_get();
	while ($dat = ria_mysql_fetch_assoc($states)) {
		print '<dl>';
			print '<dt><a href="returns.php?state='.$dat['id'].'">'.$dat['name_plural'].' ('.number_format(ord_returns_states_get_count($dat['id'])).')</a></dt>';
			print '<dd>'.$dat['desc'].'</dd>';
		print '</dl>';
	}
?>
<?php if( gu_user_is_authorized('_RGH_ADMIN_ORDER_RETURN_ADD') ){ ?>
	<h3><?php print _('Créer un retour')?></h3>
	<form id="form-return" action="index.php" method="post">
	<table class="checklist">
		<caption><?php print _('Créer un retour')?></caption>
	<tfoot>
		<tr>
			<td colspan="2">
				<div class="float-right">
					<input name="return-submit" type="submit" value="<?php print _('Ajouter')?>" />
				</div>
				<div class="clear-right"></div>
			</td>
		</tr>
	</tfoot>
	<tbody>
		<tr>
			<td id="td-form-return-1"><label for="return-order"><?php print _('Référence d\'une commande :'); ?></label></td>
			<td id="td-form-return-2"><input id="return-order" name="return-order" type="text" maxlength="16" /></td>
		</tr>
	</tbody>
	</table>
	</form>
<?php } ?>
<?php
	require_once('admin/skin/footer.inc.php');
?>