
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: de\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{core:frontpage:link_phpinfo}"
msgstr "PHPinfo"

msgid "{core:no_state:report_text}"
msgstr ""
"Wenn das Problem weiter besteht, kannst du diesen Fehler den "
"Systemadministratoren melden."

msgid "{core:no_state:cause_backforward}"
msgstr "Das Benutzen der Zurück- und Vorwärts-Schaltflächen im Web-Browser."

msgid "{core:no_metadata:not_found_for}"
msgstr "Für folgende Entität konnten keine Metadaten gefunden werden:"

msgid "{core:frontpage:link_shib13example}"
msgstr "Shibboleth 1.3 SP Beispiel - Anmelden über ihren Shibboleth IdP testen"

msgid "{core:no_state:suggestions}"
msgstr "Empfehlungen um dieses Problem zu lösen:"

msgid "{core:frontpage:login_as_admin}"
msgstr "Als Administrator anmelden"

msgid "{core:short_sso_interval:warning}"
msgstr ""
"Wir haben festgestellt, dass seit Ihrer letzten Anmeldung bei diesem "
"Diensteanbieter nur wenige Sekunden vergangen sind. Deswegen gehen wir "
"davon aus, dass es ein Problem mit diesem Anbieter gibt."

msgid "{core:frontpage:link_doc_sp}"
msgstr "SimpleSAMLphp als Service Provider benutzen"

msgid "{core:frontpage:link_meta_saml2sphosted}"
msgstr "Hosted SAML 2.0 Service Provider Metadaten (automatisch generiert)"

msgid "{core:frontpage:link_openidprovider}"
msgstr "OpenID Provider Seite - Alpha Version (Testcode)"

msgid "{core:frontpage:link_doc_install}"
msgstr "SimpleSAMLphp installieren"

msgid "{core:frontpage:link_diagnostics}"
msgstr "Diagnose des Hostnamen, Ports und Protokolls"

msgid "{core:no_state:suggestion_goback}"
msgstr "Kehre zur letzen Seite zurück und versuche es erneut."

msgid "{core:no_state:causes}"
msgstr "Dieser Fehler könnte durch folgendes verursacht werden:"

msgid "{core:frontpage:link_meta_saml2idphosted}"
msgstr "Hosted SAML 2.0 Identity Provider Metadaten (automatisch generiert)"

msgid "{core:frontpage:optional}"
msgstr "Optional"

msgid "{core:frontpage:link_meta_shib13sphosted}"
msgstr "Hosted Shibboleth 1.3 Service Provider Metadaten (automatisch generiert)"

msgid "{core:frontpage:doc_header}"
msgstr "Dokumentation"

msgid "{core:frontpage:link_doc_advanced}"
msgstr "Fortgeschrittene Eigenschaften von SimpleSAMLphp"

msgid "{core:frontpage:required_ldap}"
msgstr "Erforderlich für LDAP"

msgid "{core:frontpage:authtest}"
msgstr "Teste die konfigurierten Authentifizierungsquellen"

msgid "{core:frontpage:link_meta_overview}"
msgstr "Überblick über die Metadaten dieser Installation. Diagnose der Metadaten."

msgid "{core:frontpage:configuration}"
msgstr "Konfiguration"

msgid "{core:frontpage:welcome}"
msgstr "Willkommen"

msgid "{core:frontpage:link_doc_shibsp}"
msgstr ""
"Shibboleth 1.3 SP für die Zusammenarbeit mit einem SimpleSAMLphp IdP "
"konfigurieren"

msgid "{core:no_state:header}"
msgstr "Statusinformationen verloren"

msgid "{core:frontpage:metadata_header}"
msgstr "Metadaten"

msgid "{core:frontpage:link_doc_maintenance}"
msgstr "Verwaltung und Konfiguration von SimpleSAMLphp"

msgid "{core:frontpage:link_configcheck}"
msgstr "SimpleSAMLphp Konfigurationsprüfung"

msgid "{core:frontpage:page_title}"
msgstr "SimpleSAMLphp Installationsseite"

msgid "{core:no_cookie:header}"
msgstr "Cookie fehlt"

msgid "{core:frontpage:warnings}"
msgstr "Warnungen"

msgid "{core:frontpage:link_xmlconvert}"
msgstr "XML zu SimpleSAMLphp Metadaten Konvertierer"

msgid "{core:frontpage:link_cleardiscochoices}"
msgstr "Lösche meine IdP Auswahl in den IdP \"discovery services\""

msgid "{core:frontpage:loggedin_as_admin}"
msgstr "Sie sind als Administrator angemeldet"

msgid "{core:frontpage:auth}"
msgstr "Authentifizierung"

msgid "{core:no_metadata:suggestion_user_link}"
msgstr ""
"Sind Sie lediglich einem Verweis einer anderen Website hierher gefolgt, "
"sollten Sie diesen Fehler den Betreibern der Website melden."

msgid "{core:no_state:description}"
msgstr ""
"Wir konnten die Statusinformationen für die aktuelle Anfrage nicht "
"lokalisieren."

msgid "{core:frontpage:show_metadata}"
msgstr "Metadaten zeigen"

msgid "{core:no_state:suggestion_closebrowser}"
msgstr "Schließe den Web-Browser und versuche es erneut."

msgid "{core:short_sso_interval:warning_header}"
msgstr "Zu kurzes Intervall zwischen generellen Anmeldeereignissen."

msgid "{core:frontpage:intro}"
msgstr ""
"<strong>Glückwunsch</strong>, Sie haben erfolgreich SimpleSAMLphp "
"installiert. Dies ist die Startseite der Installation, hier gibts es "
"Links zu Testbeispielen, Diagnose, Metadaten und anderer relevanten "
"Dokumentation."

msgid "{core:no_metadata:header}"
msgstr "Keine Metadaten gefunden"

msgid "{core:frontpage:link_meta_shib13idphosted}"
msgstr "Hosted Shibboleth 1.3 Identity Provider Metadaten (automatisch generiert)"

msgid "{core:frontpage:required}"
msgstr "Erforderlich"

msgid "{core:no_metadata:config_problem}"
msgstr ""
"Ursache ist wahrscheinlich eine Fehlkonfiguration auf Seiten des Service "
"Providers oder des Identity Providers."

msgid "{core:frontpage:warnings_suhosin_url_length}"
msgstr ""
"Die Länge der Anfrageparameter wird durch die PHP-Erweiterung Suhosin "
"begrenzt. Bitte erhöhe den Wert der Option suhosin.get.max_value_length "
"auf mindestens 2048 Bytes."

msgid "{core:frontpage:warnings_https}"
msgstr ""
"<strong>Sie benutzen keine HTTPS</strong> - verschlüsselte Kommunikation "
"mit dem Nutzer. SimpleSAMLphp funktioniert zum Testen auch mit HTTP "
"problemlos, aber in einer Produktionsumgebung sollten Sie HTTPS benutzen."
" [ <a href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">Lesen sie mehr über die Verwaltung von SimpleSAMLphp</a> "
"]"

msgid "{core:frontpage:federation}"
msgstr "Föderation"

msgid "{core:frontpage:required_radius}"
msgstr "Erforderlich für Radius"

msgid "{core:no_state:cause_openbrowser}"
msgstr "Das Öffnen des Web-Browser mit gespeicherten Tabs aus der letzten Sitzung."

msgid "{core:frontpage:checkphp}"
msgstr "Überprüfen der PHP Installation"

msgid "{core:frontpage:link_doc_idp}"
msgstr "SimpleSAMLphp als Identitiy Provider benutzen"

msgid "{core:no_state:report_header}"
msgstr "Diesen Fehler melden"

msgid "{core:frontpage:link_saml2example}"
msgstr "SAML 2.0 SP Beispiel - Anmelden über ihren IdP testen"

msgid "{core:no_state:cause_nocookie}"
msgstr "Cookies könnten im Web-Browser deaktiviert sein."

msgid "{core:frontpage:about_header}"
msgstr "Über SimpleSAMLphp"

msgid "{core:frontpage:about_text}"
msgstr ""
"Hey, dieses SimpleSAMLphp ist eine ziemlich coole Sache, wo kann ich mehr"
" darüber lesen? Sie finden mehr Informationen über <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp auf dem Feide "
"RnD blog</a> auf <a href=\"http://uninett.no\">UNINETT</a>."

msgid "{core:no_metadata:suggestion_developer}"
msgstr ""
"Arbeiten Sie selbst an einem Web Single Sign-On System, stimmt mit den "
"benutzten Metadaten etwas nicht. Überprüfen Sie die Metadaten des "
"Identity Providers und des Service Providers."

msgid "{core:no_cookie:retry}"
msgstr "Erneut versuchen"

msgid "{core:frontpage:useful_links_header}"
msgstr "Nützliche Links für ihre Installation"

msgid "{core:frontpage:metadata}"
msgstr "Metadaten"

msgid "{core:frontpage:recommended}"
msgstr "Empfohlen"

msgid "{core:frontpage:link_doc_googleapps}"
msgstr "SimpleSAMLphp als IdP für Google Apps für Education verwernden"

msgid "{core:frontpage:tools}"
msgstr "Werkzeuge"

msgid "{core:short_sso_interval:retry}"
msgstr "Versuche Anmeldung erneut"

msgid "{core:no_cookie:description}"
msgstr ""
"Sie scheinen Cookies in Ihrem Browser deaktiviert zu haben. Bitte "
"überprüfen Sie die Einstellungen in Ihrem Browser und versuchen Sie es "
"erneut."

msgid "{core:frontpage:deprecated}"
msgstr "Veraltet"

msgid "You are logged in as administrator"
msgstr "Sie sind als Administrator angemeldet"

msgid "Go back to the previous page and try again."
msgstr "Kehre zur letzen Seite zurück und versuche es erneut."

msgid "If this problem persists, you can report it to the system administrators."
msgstr ""
"Wenn das Problem weiter besteht, kannst du diesen Fehler den "
"Systemadministratoren melden."

msgid "Welcome"
msgstr "Willkommen"

msgid "SimpleSAMLphp configuration check"
msgstr "SimpleSAMLphp Konfigurationsprüfung"

msgid "Metadata overview for your installation. Diagnose your metadata files"
msgstr "Überblick über die Metadaten dieser Installation. Diagnose der Metadaten."

msgid "XML to SimpleSAMLphp metadata converter"
msgstr "XML zu SimpleSAMLphp Metadaten Konvertierer"

msgid "Required"
msgstr "Erforderlich"

msgid "Warnings"
msgstr "Warnungen"

msgid "Documentation"
msgstr "Dokumentation"

msgid "Hosted Shibboleth 1.3 Service Provider Metadata (automatically generated)"
msgstr "Hosted Shibboleth 1.3 Service Provider Metadaten (automatisch generiert)"

msgid "PHP info"
msgstr "PHPinfo"

msgid "About SimpleSAMLphp"
msgstr "Über SimpleSAMLphp"

msgid "Hosted SAML 2.0 Service Provider Metadata (automatically generated)"
msgstr "Hosted SAML 2.0 Service Provider Metadaten (automatisch generiert)"

msgid "Retry login"
msgstr "Versuche Anmeldung erneut"

msgid "Required for LDAP"
msgstr "Erforderlich für LDAP"

msgid "Close the web browser, and try again."
msgstr "Schließe den Web-Browser und versuche es erneut."

msgid "Federation"
msgstr "Föderation"

msgid "We were unable to locate the state information for the current request."
msgstr ""
"Wir konnten die Statusinformationen für die aktuelle Anfrage nicht "
"lokalisieren."

msgid "Delete my choices of IdP in the IdP discovery services"
msgstr "Lösche meine IdP Auswahl in den IdP \"discovery services\""

msgid ""
"This is most likely a configuration problem on either the service "
"provider or identity provider."
msgstr ""
"Ursache ist wahrscheinlich eine Fehlkonfiguration auf Seiten des Service "
"Providers oder des Identity Providers."

msgid "Configure Shibboleth 1.3 SP to work with SimpleSAMLphp IdP"
msgstr ""
"Shibboleth 1.3 SP für die Zusammenarbeit mit einem SimpleSAMLphp IdP "
"konfigurieren"

msgid "Using the back and forward buttons in the web browser."
msgstr "Das Benutzen der Zurück- und Vorwärts-Schaltflächen im Web-Browser."

msgid "Metadata not found"
msgstr "Keine Metadaten gefunden"

msgid "Missing cookie"
msgstr "Cookie fehlt"

msgid "Cookies may be disabled in the web browser."
msgstr "Cookies könnten im Web-Browser deaktiviert sein."

msgid "Opened the web browser with tabs saved from the previous session."
msgstr "Das Öffnen des Web-Browser mit gespeicherten Tabs aus der letzten Sitzung."

msgid "Tools"
msgstr "Werkzeuge"

msgid "Test configured authentication sources "
msgstr "Teste die konfigurierten Authentifizierungsquellen"

msgid ""
"You appear to have disabled cookies in your browser. Please check the "
"settings in your browser, and try again."
msgstr ""
"Sie scheinen Cookies in Ihrem Browser deaktiviert zu haben. Bitte "
"überprüfen Sie die Einstellungen in Ihrem Browser und versuchen Sie es "
"erneut."

msgid "Installing SimpleSAMLphp"
msgstr "SimpleSAMLphp installieren"

msgid "Deprecated"
msgstr "Veraltet"

msgid ""
"<strong>Congratulations</strong>, you have successfully installed "
"SimpleSAMLphp. This is the start page of your installation, where you "
"will find links to test examples, diagnostics, metadata and even links to"
" relevant documentation."
msgstr ""
"<strong>Glückwunsch</strong>, Sie haben erfolgreich SimpleSAMLphp "
"installiert. Dies ist die Startseite der Installation, hier gibts es "
"Links zu Testbeispielen, Diagnose, Metadaten und anderer relevanten "
"Dokumentation."

msgid "This error may be caused by:"
msgstr "Dieser Fehler könnte durch folgendes verursacht werden:"

msgid ""
"<strong>You are not using HTTPS</strong> - encrypted communication with "
"the user. HTTP works fine for test purposes, but in a production "
"environment, you should use HTTPS. [ <a "
"href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-"
"maintenance\">Read more about SimpleSAMLphp maintenance</a> ]"
msgstr ""
"<strong>Sie benutzen keine HTTPS</strong> - verschlüsselte Kommunikation "
"mit dem Nutzer. SimpleSAMLphp funktioniert zum Testen auch mit HTTP "
"problemlos, aber in einer Produktionsumgebung sollten Sie HTTPS benutzen."
" [ <a href=\"https://simplesamlphp.org/docs/stable/simplesamlphp-maintenance"
"\">Lesen sie mehr über die Verwaltung von SimpleSAMLphp</a> "
"]"

msgid "Metadata"
msgstr "Metadaten"

msgid "Retry"
msgstr "Erneut versuchen"

msgid "SimpleSAMLphp Maintenance and Configuration"
msgstr "Verwaltung und Konfiguration von SimpleSAMLphp"

msgid "Diagnostics on hostname, port and protocol"
msgstr "Diagnose des Hostnamen, Ports und Protokolls"

msgid ""
"If you are an user who received this error after following a link on a "
"site, you should report this error to the owner of that site."
msgstr ""
"Sind Sie lediglich einem Verweis einer anderen Website hierher gefolgt, "
"sollten Sie diesen Fehler den Betreibern der Website melden."

msgid "Using SimpleSAMLphp as an Identity Provider"
msgstr "SimpleSAMLphp als Identitiy Provider benutzen"

msgid "Optional"
msgstr "Optional"

msgid "Suggestions for resolving this problem:"
msgstr "Empfehlungen um dieses Problem zu lösen:"

msgid ""
"This SimpleSAMLphp thing is pretty cool, where can I read more about it? "
"You can find more information about it at the <a "
"href=\"https://simplesamlphp.org/\">SimpleSAMLphp web page </a> over at "
"<a href=\"http://uninett.no\">UNINETT</a>."
msgstr ""
"Hey, dieses SimpleSAMLphp ist eine ziemlich coole Sache, wo kann ich mehr"
" darüber lesen? Sie finden mehr Informationen über <a "
"href=\"http://rnd.feide.no/simplesamlphp\">SimpleSAMLphp auf dem Feide "
"RnD blog</a> auf <a href=\"http://uninett.no\">UNINETT</a>."

msgid "Shibboleth 1.3 SP example - test logging in through your Shib IdP"
msgstr "Shibboleth 1.3 SP Beispiel - Anmelden über ihren Shibboleth IdP testen"

msgid "Authentication"
msgstr "Authentifizierung"

msgid "SimpleSAMLphp installation page"
msgstr "SimpleSAMLphp Installationsseite"

msgid "Show metadata"
msgstr "Metadaten zeigen"

msgid "SimpleSAMLphp as an IdP for Google Apps for Education"
msgstr "SimpleSAMLphp als IdP für Google Apps für Education verwernden"

msgid "State information lost"
msgstr "Statusinformationen verloren"

msgid "Hosted SAML 2.0 Identity Provider Metadata (automatically generated)"
msgstr "Hosted SAML 2.0 Identity Provider Metadaten (automatisch generiert)"

msgid "OpenID Provider site - Alpha version (test code)"
msgstr "OpenID Provider Seite - Alpha Version (Testcode)"

msgid "Required for Radius"
msgstr "Erforderlich für Radius"

msgid "We were unable to locate the metadata for the entity:"
msgstr "Für folgende Entität konnten keine Metadaten gefunden werden:"

msgid "SAML 2.0 SP example - test logging in through your IdP"
msgstr "SAML 2.0 SP Beispiel - Anmelden über ihren IdP testen"

msgid "Using SimpleSAMLphp as a Service Provider"
msgstr "SimpleSAMLphp als Service Provider benutzen"

msgid ""
"We have detected that there is only a few seconds since you last "
"authenticated with this service provider, and therefore assume that there"
" is a problem with this SP."
msgstr ""
"Wir haben festgestellt, dass seit Ihrer letzten Anmeldung bei diesem "
"Diensteanbieter nur wenige Sekunden vergangen sind. Deswegen gehen wir "
"davon aus, dass es ein Problem mit diesem Anbieter gibt."

msgid "Recommended"
msgstr "Empfohlen"

msgid ""
"If you are a developer who is deploying a single sign-on solution, you "
"have a problem with the metadata configuration. Verify that metadata is "
"configured correctly on both the identity provider and service provider."
msgstr ""
"Arbeiten Sie selbst an einem Web Single Sign-On System, stimmt mit den "
"benutzten Metadaten etwas nicht. Überprüfen Sie die Metadaten des "
"Identity Providers und des Service Providers."

msgid "SimpleSAMLphp Advanced Features"
msgstr "Fortgeschrittene Eigenschaften von SimpleSAMLphp"

msgid "Too short interval between single sign on events."
msgstr "Zu kurzes Intervall zwischen generellen Anmeldeereignissen."

msgid "Checking your PHP installation"
msgstr "Überprüfen der PHP Installation"

msgid ""
"The length of query parameters is limited by the PHP Suhosin extension. "
"Please increase the suhosin.get.max_value_length option to at least 2048 "
"bytes."
msgstr ""
"Die Länge der Anfrageparameter wird durch die PHP-Erweiterung Suhosin "
"begrenzt. Bitte erhöhe den Wert der Option suhosin.get.max_value_length "
"auf mindestens 2048 Bytes."

msgid "Useful links for your installation"
msgstr "Nützliche Links für ihre Installation"

msgid "Configuration"
msgstr "Konfiguration"

msgid "Hosted Shibboleth 1.3 Identity Provider Metadata (automatically generated)"
msgstr "Hosted Shibboleth 1.3 Identity Provider Metadaten (automatisch generiert)"

msgid "Login as administrator"
msgstr "Als Administrator anmelden"

msgid "Report this error"
msgstr "Diesen Fehler melden"

