<?php
/**
 *  \defgroup History Historique des actions 
 *  \ingroup OnBoardingYuto
 * 
 *  @{
*/

	require_once('act_actions.inc.php');

	switch( $method ){
		case 'get':
			$result = false;
			break;
		/** @{@}
		 * @{
		 * \page api-actions-history-add Ajout
		 *
		 * Cette fonction permet d'ajouter une action à l'historique 	
		 *
		 *		\code
		 *			POST /actions/history/
		 *  	 \endcode
		 *
		 * @param int $act_id Identifiant de l'action concerné
		 * @param $date_created Date/Heure de création de l'historique
		 * @param int $author_id Identifiant de l'utilisateur concerné
		 * @param int $cls_id Identifiant de de la classe d'objet
		 * @param int $obj_id_0 Identifiant de l'objet
		 * @param int $obj_id_1 Identifiant de l'objet
		 * @param int $obj_id_2 Identifiant de l'objet
		 *
		 * @return int l'identifiant du nouveau historique en cas de succès
		 *
		 * @}
		*/	
		case 'add':
			$id = add_action_history($_REQUEST);
			$result = true;
			$content = array('id' => $id);
			break;
	}

// \cond onlyria
function add_action_history($raw_data){
	global $method, $config;


	// controles des paramètres pour l'historique

	if(  !isset($raw_data['act_id']) || !is_numeric($raw_data['act_id'])  )
	{
		throw new Exception("Paramètres historique de l'action invalide (act_id)");
	}


	if( !isset($raw_data['date_created']))
	{
		throw new Exception("Paramètres historique de l'action invalide (date_created)");
	}

	if( !isset($raw_data['author_id']) || !is_numeric($raw_data['author_id']) )
	{
		throw new Exception("Paramètres historique de l'action invalide (author_id)");
	}

	if(  !isset($raw_data['cls_id']) || !is_numeric($raw_data['cls_id'])  )
	{
		throw new Exception("Paramètres historique de l'action invalide (cls_id)");
	}

	if(  !isset($raw_data['obj_id_0'])  )
	{
		throw new Exception("Paramètres historique de l'action invalide (obj_id_0)");
	}


	if(  !isset($raw_data['obj_id_1'])  )
	{
		throw new Exception("Paramètres historique de l'action invalide (obj_id_1)");
	}


	// controles des paramètres pour l'historique
	if(  !isset($raw_data['obj_id_2'])  )
	{
		throw new Exception("Paramètres historique de l'action invalide (obj_id_2)");
	}


	// création de l'historique
	$id = act_actions_history_add($raw_data['act_id'],$raw_data['date_created'],$raw_data['author_id'],$raw_data['cls_id'],$raw_data['obj_id_0'],$raw_data['obj_id_1'],$raw_data['obj_id_2']);

	if( !$id ){
		throw new Exception("Une erreur est survenue lors de la création de l'historique du l'action : ".$raw_data['act_id'].', '.$raw_data['date_created'].', '.$raw_data['author_id'].'');
	}

	return $id;

}
// \endcond

/// @}