<?php
/**
 * ChannelCatalogListLinks
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * ChannelCatalogListLinks Class Doc Comment
 *
 * @category Class
 * @description Indicates how global actions you can do like how to create a channel catalog, exclusion filter operators, etc...
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class ChannelCatalogListLinks implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'channelCatalogListLinks';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'self' => '\Swagger\Client\Model\LinksGetChannelCatalogsLink',
        'beez_up_columns' => '\Swagger\Client\Model\ExternalLinksCatalogGetBeezUPColumnsLink',
        'add' => '\Swagger\Client\Model\LinksAddChannelCatalogLink',
        'exclusion_filter_operators' => '\Swagger\Client\Model\LinksGetChannelCatalogExclusionFilterOperatorsLink'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'self' => null,
        'beez_up_columns' => null,
        'add' => null,
        'exclusion_filter_operators' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'self' => 'self',
        'beez_up_columns' => 'beezUPColumns',
        'add' => 'add',
        'exclusion_filter_operators' => 'exclusionFilterOperators'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'self' => 'setSelf',
        'beez_up_columns' => 'setBeezUpColumns',
        'add' => 'setAdd',
        'exclusion_filter_operators' => 'setExclusionFilterOperators'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'self' => 'getSelf',
        'beez_up_columns' => 'getBeezUpColumns',
        'add' => 'getAdd',
        'exclusion_filter_operators' => 'getExclusionFilterOperators'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['self'] = isset($data['self']) ? $data['self'] : null;
        $this->container['beez_up_columns'] = isset($data['beez_up_columns']) ? $data['beez_up_columns'] : null;
        $this->container['add'] = isset($data['add']) ? $data['add'] : null;
        $this->container['exclusion_filter_operators'] = isset($data['exclusion_filter_operators']) ? $data['exclusion_filter_operators'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        return true;
    }


    /**
     * Gets self
     *
     * @return \Swagger\Client\Model\LinksGetChannelCatalogsLink
     */
    public function getSelf()
    {
        return $this->container['self'];
    }

    /**
     * Sets self
     *
     * @param \Swagger\Client\Model\LinksGetChannelCatalogsLink $self self
     *
     * @return $this
     */
    public function setSelf($self)
    {
        $this->container['self'] = $self;

        return $this;
    }

    /**
     * Gets beez_up_columns
     *
     * @return \Swagger\Client\Model\ExternalLinksCatalogGetBeezUPColumnsLink
     */
    public function getBeezUpColumns()
    {
        return $this->container['beez_up_columns'];
    }

    /**
     * Sets beez_up_columns
     *
     * @param \Swagger\Client\Model\ExternalLinksCatalogGetBeezUPColumnsLink $beez_up_columns beez_up_columns
     *
     * @return $this
     */
    public function setBeezUpColumns($beez_up_columns)
    {
        $this->container['beez_up_columns'] = $beez_up_columns;

        return $this;
    }

    /**
     * Gets add
     *
     * @return \Swagger\Client\Model\LinksAddChannelCatalogLink
     */
    public function getAdd()
    {
        return $this->container['add'];
    }

    /**
     * Sets add
     *
     * @param \Swagger\Client\Model\LinksAddChannelCatalogLink $add add
     *
     * @return $this
     */
    public function setAdd($add)
    {
        $this->container['add'] = $add;

        return $this;
    }

    /**
     * Gets exclusion_filter_operators
     *
     * @return \Swagger\Client\Model\LinksGetChannelCatalogExclusionFilterOperatorsLink
     */
    public function getExclusionFilterOperators()
    {
        return $this->container['exclusion_filter_operators'];
    }

    /**
     * Sets exclusion_filter_operators
     *
     * @param \Swagger\Client\Model\LinksGetChannelCatalogExclusionFilterOperatorsLink $exclusion_filter_operators exclusion_filter_operators
     *
     * @return $this
     */
    public function setExclusionFilterOperators($exclusion_filter_operators)
    {
        $this->container['exclusion_filter_operators'] = $exclusion_filter_operators;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


