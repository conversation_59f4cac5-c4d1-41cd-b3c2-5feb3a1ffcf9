<?php
// \cond onlyria

/**
* \defgroup api-devices-debug Debug des tablettes DEPRECIER ( Passage des requêtes via push google/apple)
* \ingroup Yuto
* @{
*/

switch( $method ){
	// envoi les requetes en attente
	/** @{@}
 	*	@{
	*	 \page api-devices-debug-get Chargement
	*
	*	 Cette fonction récupère les requêtes en attente
	*		\code
	*			GET /devices/debug/
	*		\endcode
	*
	*	 @return Liste de requêtes avec les colonnes :
	*				- receivers : ????
	*				- queries : ????
	*	@}
	*/
	case 'get':

		$queries = array();

		// test la présence d'un fichier
		$dir = dirname(__FILE__).'/../debug/';

		if ($handle = opendir($dir)) {
			while (false !== ($entry = readdir($handle))) {
				if ($entry != "." && $entry != "..") {

					if( preg_match('/^'.$_REQUEST['token'].'-.*\.xml/', $entry) ){
						$xml = simplexml_load_file($dir.$entry);

						$r = array();
						foreach( $xml->receivers->receiver as $tmp ){
							array_push($r, (string)$tmp);
						}
						$q = array();
						foreach( $xml->queries->query as $tmp ){
							array_push($q,(string) $tmp);
						}
						$queries[] = array('receivers' => $r, 'queries' => $q);

						// le unlink devrait plutot être au retour
						unlink($dir.$entry);
					}
				}
			}
			closedir($handle);
		}

		$result = true;
		if( sizeof($queries) ){
			$content = $queries;
		}

		break;
	/** @{@}
 	*	@{
	*	 \page api-devices-debug-add Ajout
	*
	*	 Cette fonction envoie la réponse à une requête par email
	*		 \code
	*			POST/devices/debug/
	*		 \endcode
	*
	*	 @param receivers Obligatoire, Destinataire de l'émail
	*	 @param query Obligatoire, Requête
	*	 @param data Obligatoire, Résultat de la requête
	*
	*	 @return
	*	@}
	*/
	// permet l'envoi de la réponse de la tablette.
	case 'add':

		if( isset($_POST['receivers'], $_POST['query'], $_POST['data']) && is_array($_POST['receivers'])){

			$mail = new Email();
			$mail->setFrom('<EMAIL>');
			$mail->setSubject('Fdv requete '.$config['dev_id']);

			$usr_name = '';
			$rusr = gu_users_get($config['usr_id']);
			if( $rusr && ria_mysql_num_rows($rusr) ){
				$usr = ria_mysql_fetch_assoc($rusr);
				$usr_name = $usr['adr_firstname'].' '.$usr['adr_lastname'];
			}

			$paragraph = "<strong>Tnt id :</strong> \n".$config['tnt_id']." - ".$_REQUEST['logtoken'];
			$paragraph .= "\n<strong>Device id</strong> : \n".$config['dev_id']." - ".$_REQUEST['token'];
			$paragraph .= "\n<strong>Version</strong> : \n".$config['dev_version'].' - '.$config['dev_brand'].' - '.$config['dev_model']." \n";
			$paragraph .= "\n<strong>Utilisateur</strong> : \n".$usr_name." \n";

			$paragraph .= "\n<strong>Query</strong> : \n".$_POST['query']." \n";
			if (isset($_POST['result']) && !empty($_POST['result'])) {
				$paragraph .= "\n<strong>Result</strong> : \n".$_POST['result']." \n";
			}
			$paragraph .= "\n<strong>Data :</strong>";
			$mail->addParagraph($paragraph);

			$mail->addBlankTextLine();

			$mail->openTable(570);
			if( $config['dev_version'] >= 253 || ($config['dev_brand']=='Apple' && $config['dev_version'] >= 99) ){

				$lines = explode("[SAUT]", $_POST['data']);
				if( sizeof($lines) ){
					foreach( $lines as $line){

						$cols = explode("[COL]", $line);
						if( sizeof($cols) ){
							$mail->openTableRow();
							foreach ($cols as $value) {
								$mail->addCell( (string) $value );
							}
							$mail->closeTableRow();
						}else{
							$mail->addParagraph($line);
						}
					}
				}else{
					$mail->addParagraph($_POST['data']);
				}

			}else{
				$lines = explode(PHP_EOL, $_POST['data']);
				foreach( $lines as $line){

					$row = explode(";", $line);
					$mail->openTableRow();

					foreach ($row as $value) {
						$mail->addCell( (string) $value );
					}
					$mail->closeTableRow();
				}
			}

			$mail->closeTable();
			$i = 0;
			foreach( $_POST['receivers'] as $receiver ){
				if( isemail($receiver) ){
					if ( !$i ) {
						$mail->setTo($receiver);
					}else{
						$mail->addTo($receiver);
					}
					$i++;
				}
			}

			$mail->send();
		}

		break;
}

///@}

// \endcond