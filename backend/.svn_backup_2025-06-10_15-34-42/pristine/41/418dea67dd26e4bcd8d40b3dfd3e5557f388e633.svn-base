<?php
	/**
	*	Ce script permet de mettre � jour toutes les positions des images en fonction de leur img_id.
	*/
	
	set_include_path(dirname(__FILE__) . '/../include/');

	require_once( 'db.inc.php' );
	
	$rline = ria_mysql_query('
		select img_tnt_id, img_prd_id
		from prd_images
		group by img_tnt_id, img_prd_id
	');
	if( $rline && ria_mysql_num_rows($rline) ){
		while( $line = ria_mysql_fetch_array( $rline ) ){
			$rimg = ria_mysql_query('select img_tnt_id, img_id, img_prd_id from prd_images where img_tnt_id = '.$line['img_tnt_id'].' and img_prd_id='.$line['img_prd_id'].' order by img_id desc');
			
			print 'Image '.$line['img_tnt_id'].' : '.$line['img_prd_id']."\n";
			if( $rimg ){
				$pos = 1;
				while( $img = ria_mysql_fetch_array( $rimg ) ){
					ria_mysql_query('update prd_images set img_pos = '.$pos.' where img_tnt_id = '.$img['img_tnt_id'].' and img_id = '.$img['img_id'].' and img_prd_id = '.$img['img_prd_id'].'');
					$pos++;
				}
			}
		}
	}
	