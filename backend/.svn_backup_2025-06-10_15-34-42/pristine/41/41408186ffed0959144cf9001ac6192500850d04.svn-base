Comments
-----
<?php

/** doc 1 */
/* foobar 1 */
// foo 1
// bar 1
$var;

if ($cond) {
    /** doc 2 */
    /* foobar 2 */
    // foo 2
    // bar 2
}

/** doc 3 */
/* foobar 3 */
// foo 3
// bar 3
-----
array(
    0: Expr_Variable(
        name: var
        comments: array(
            0: /** doc 1 */
            1: /* foobar 1 */
            2: // foo 1
            3: // bar 1
        )
    )
    1: Stmt_If(
        cond: Expr_Variable(
            name: cond
        )
        stmts: array(
            0: Stmt_Nop(
                comments: array(
                    0: /** doc 2 */
                    1: /* foobar 2 */
                    2: // foo 2
                    3: // bar 2
                )
            )
        )
        elseifs: array(
        )
        else: null
    )
    2: Stmt_Nop(
        comments: array(
            0: /** doc 3 */
            1: /* foobar 3 */
            2: // foo 3
            3: // bar 3
        )
    )
)
-----
<?php

/** doc */
/* foobar */
// foo
// bar

?>
-----
array(
    0: Stmt_Nop(
        comments: array(
            0: /** doc */
            1: /* foobar */
            2: // foo
            3: // bar
        )
    )
)
-----
<?php

// comment
if (42) {}
-----
array(
    0: Stmt_If(
        cond: Scalar_LNumber(
            value: 42
        )
        stmts: array(
        )
        elseifs: array(
        )
        else: null
        comments: array(
            0: // comment
        )
    )
)