<?php

	/** \file confirm-orders-cdiscount.php
	 *
	 * 	Ce script est destiné à confirmer les nouvelles commandes réalisées sur la place de marché CDiscount (C le marché).
	 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
	 *
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('orders.inc.php');
	require_once('comparators/ctr.cdiscount.inc.php');
	// Active ou non le mode test
	$mode_test = isset($ar_params['test']) && $ar_params['test'] == 'test';

	$all_ctr_CDiscount = array_merge( array(CTR_CDISCOUNT), ctr_cdiscount_partners_get_ria_id() );

	// Traitement
	foreach( $configs as $config ){
		if( in_array($config['tnt_id'], [1]) ){
			continue;
		}

		foreach( $all_ctr_CDiscount as $one_ctr ){
			// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, on passe au client suivant.
			if( !ctr_comparators_actived($one_ctr) ){
				continue;
			}

			$states = array( 6, 7, 8 );

			// Récupère le compte client, le produit frais de port et le service de livraison
			$pms = ctr_params_get_array( $one_ctr, array('USR_ID') );
			if( !isset($pms['USR_ID']) || !gu_users_exists($pms['USR_ID']) ){
				continue;
			}

			$config['tmp_cdiscount_id'] = $one_ctr;

			// Récupère les commandes pouvant faire l'objet d'une expédition
			$rord = ord_orders_get( $pms['USR_ID'], 0, $states, 0, null, false, false, false, false, false, false, '', true, array(_FLD_ORD_CTR_SHIPPED=>'Non') );
			$xml = ''; $ord_not_confirm = array();
			if( $rord && ria_mysql_num_rows($rord) ){

				$ar_ref = array();
				while( $ord = ria_mysql_fetch_array($rord) ){
					if( isset($config['ctr_confirm_date_active']) && isdate($config['ctr_confirm_date_active']) ){
						if( strtotime($config['ctr_confirm_date_active']) > strtotime($ord['date_en']) ){
							continue;
						}
					}

					$ord_ref = fld_object_values_get( $ord['id'], _FLD_ORD_MKT_ID, '', false, '' );

					$tmp = ctr_cdiscount_confirm_shipped_get_xml( $ord['id'], $ord_ref );

					if( trim($tmp)!='' ){
						$xml .= $tmp;
						$ar_ref[ $ord_ref ] = $ord['id'];
					} else {
						$ord_not_confirm[] = $ord['id'];
					}
				}
			}

			if( !$mode_test ){
				if( trim($xml)!='' ){
					$msg_error = '';
					if( sizeof($ord_not_confirm) ){
						$msg_error .= 'Impossible de confirmer l\'expédition des commandes suivantes : '.implode(', ', $ord_not_confirm)."\n";
					}

					$obj = ctr_cdiscount_confirm_shipped( $xml );
					if( isset($obj->ValidateOrderListResult) ){
						$results = $obj->ValidateOrderListResult;
						if( !is_array($results->ValidateOrderResults->ValidateOrderResult) ){
							$results->ValidateOrderResults->ValidateOrderResult = array( $results->ValidateOrderResults->ValidateOrderResult );
						}
						foreach( $results->ValidateOrderResults->ValidateOrderResult as $order ){
							if( $order->Validated!='1' ){
								$order->Errors = !is_array($order->Errors) ? array( $order->Errors ) : $order->Errors;
								$order->Warnings = !is_array($order->Warnings) ? array( $order->Warnings ) : $order->Warnings;

								$msg_error .= '[Commande '.$order->OrderNumber.' - '.$config['site_name'].'] Erreur de confirmation d\'expédition : '.print_r($order->Errors, true).' | '.print_r($order->Warnings, true);
							}elseif( !isset($ar_ref[$order->OrderNumber]) || !fld_object_values_set( $ar_ref[$order->OrderNumber], _FLD_ORD_CTR_SHIPPED, 'Oui') ){
								$msg_error .= '[Commande '.$order->OrderNumber.' - '.$config['site_name'].'] Impossible de mettre à jour l\'information du champ avancé "Expédition confirmée".';
							}
						}
					}

					if( trim($msg_error)!='' ){
						// Module RiaShoppping plus suivi, plus d'envoi de message
					}
				}
			} else {
				print $xml."\n";
			}
		}
	}
