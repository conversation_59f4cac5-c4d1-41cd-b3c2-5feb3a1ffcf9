<?php
	require_once('categories.inc.php');
	require_once('stats.inc.php');


	// Filtre sur l'origine de la commande
	$params = view_origins_get_params();
	$origin = $params['origin'];
	$gescom = $params['gescom'];
	$is_web = $params['is_web'];

   	$filtre = array();
	$pay_id = isset($_SESSION['ord_pay_id']) ? $_SESSION['ord_pay_id'] : 0;
	$filtre['seller_id']= isset($_SESSION['ord_seller_id']) && is_numeric($_SESSION['ord_seller_id']) ? $_SESSION['ord_seller_id'] : 0;
	$stores = isset($_SESSION['ord_store_id']) ? $_SESSION['ord_store_id'] : array();

	// Récupération des statistiques
	$carts = stats_graphs_get_datas( 'order-carts', $date_start, $date_end, $filtre, -1, $wst_id, false, $origin, $pay_id, $stores, $gescom, $is_web );
	$completed = stats_graphs_get_datas( 'order-completed', $date_start, $date_end, $filtre, -1, $wst_id, false, $origin, $pay_id, $stores, $gescom, $is_web );
	$canceled = stats_graphs_get_datas( 'order-canceled', $date_start, $date_end, $filtre, -1, $wst_id, false, $origin, $pay_id, $stores, $gescom, $is_web );

	print '<h3>'._('Nombre de commandes').'</h3>';
	print '<div id="graph-orders"></div>';

?>
<script>
	$(function () {
		$('#graph-orders').highcharts({
			chart: {
				type: "spline",
				plotBorderWidth: 0,
				animation: false,
				renderTo: 'container',
				events: {
					load: function (event) {
						var extremes = this.yAxis[0].getExtremes();
						if (extremes.dataMax == 0) {
							this.yAxis[0].setExtremes(0, 5);
						}
					}
				}
			},
			credits: {
				enabled: false
			},
			exporting: {
				filename: 'statistiques-nombre-commandes'
			},
			title: {
				text: '',
				x: -20
			},
			xAxis: {
				categories: [<?php print '\''.implode('\', \'', array_keys($completed)).'\''; ?>]
			},
			yAxis: {
				title: {
					text: ''
				},
				min: 0,
				plotLines: [{
					value: 0,
					width: 1,
					color: '#808080'
				}]
			},
			legend: {
				layout: 'horizontal',
				align: 'center',
				verticalAlign: 'top',
				borderWidth: 0
			},
			tooltip: {
				shared: true,
				crosshairs: true,
				followTouchMove: true,
				style: {
					fontSize: "13px"
				},
				formatter: function() {
					var str = '<span style="font-size: 12px;">'+ this.x +'</span>';

					$.each(this.points, function(i, point) {
						str += '<br/><span style="color:'+point.series.color+'">'+ point.series.name +'</span>: <b>'+
						point.y+'</b>';
					});

					return str;
				},
			},
			series: [
				<?php if( tnt_tenants_have_websites() ){ ?>
				{
					name: "<?php print _('Créations de paniers'); ?>",
					data: [<?php print implode( ', ', array_values($carts) ); ?>],
					color: '#4572A7'
				},
				<?php } ?>
				{
					name: "<?php print _('Commandes validées'); ?>",
					data: [<?php print implode( ', ', array_values($completed) ); ?>],
					color: '#89A54E'
				},
				{
					name: "<?php print _('Commandes annulées'); ?>",
					data: [<?php print implode( ', ', array_values($canceled) ); ?>],
					color: '#AA4643'
				}
			]
		});
	});
</script>