<?xml version="1.0" encoding="UTF-8" ?>
<routes xmlns="http://symfony.com/schema/routing"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://symfony.com/schema/routing
        https://symfony.com/schema/routing/routing-1.0.xsd">

    <route id="blog" path="/blog">
        <default key="_controller">
            <string>AcmeBlogBundle:Blog:index</string>
        </default>
        <default key="map">
            <map>
                <bool key="boolean" xsi:nil="true" />
                <int key="integer" xsi:nil="true" />
                <float key="float" xsi:nil="true" />
                <string key="string" xsi:nil="1" />
                <list key="list" xsi:nil="true" />
                <map key="map" xsi:nil="true" />
            </map>
        </default>
    </route>
</routes>
