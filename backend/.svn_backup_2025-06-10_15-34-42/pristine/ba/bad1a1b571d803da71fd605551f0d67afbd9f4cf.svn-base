<?php
	set_include_path(dirname(__FILE__) . '/../include/');
	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	
	require_once('users.inc.php');
	require_once('delivery.inc.php');

	$fld_id = isset($argv[1]) && is_numeric($argv[1]) && $argv[1] > 0 ? $argv[1] : false;
	if (!$fld_id) {
		print 'Merci de préciser le numéro du champ avancé contenant la référence du compte client sur le magasin.'.PHP_EOL;
		exit;
	}

	$typ_img_store = isset($argv[2]) && is_numeric($argv[2]) && $argv[2] > 0 ? $argv[2] : false;
	if (!$typ_img_store) {
		print 'Merci de préciser le\'identifiant du type d\'image magasin.'.PHP_EOL;
		exit;
	}

	$r_stores = dlv_stores_get( 0, null, false, 0, 0, false, 0, '', 0, 0, $fld_id );
	if( !$r_stores || !ria_mysql_num_rows($r_stores) ){
		print 'Aucun magasin trouvé'.PHP_EOL;
		exit;
	}

	$nb = 0;
	while( $store = ria_mysql_fetch_assoc($r_stores) ){
		$usr_id = fld_object_values_get( $store['id'], $fld_id);
		if( !$usr_id ){
			continue;
		}

		$r_usr = gu_users_get($usr_id);
		if( !$r_usr || !ria_mysql_num_rows($r_usr) ){
			continue;
		}

		$usr = ria_mysql_fetch_assoc($r_usr);
		if (!is_numeric($usr['img_id']) || !$usr['img_id']) {
			continue;
		}

		$str_img_id = dlv_stores_images_get( $store['id'], $typ_img_store );
		if ($str_img_id && ria_mysql_num_rows($str_img_id)) {
			continue;
		}

		if( !dlv_stores_images_add_existing( $store['id'], $usr['img_id'], array($typ_img_store) ) ){
			print 'Erreur copy image store : '.$store['id'].' - img : '.$usr['img_id'].PHP_EOL;
		}
		
		$nb++;
	}

	print 'Nombre de magasin avec utilisateur : '.$nb;


