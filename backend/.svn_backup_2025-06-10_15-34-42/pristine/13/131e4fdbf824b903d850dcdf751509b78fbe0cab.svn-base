$(document).ready( function(){

    $('#selectseller .selectorview').click(function(){
        if($('#selectseller .selector').css('display')=='none'){
            $('#selectseller .selector').show();
        }else{
            $('#selectseller .selector').hide();
        }
    });

    $('#selectseller .selector a').click(function(){
        $('#selectseller .selectorview .left .view').html($(this).html());
        $('#selectseller .selector').hide();
            
        var seller = $(this).attr('name').replace('seller-', '');
        if($('#seller').length > 0){	
            $('#seller').val(seller);	
        }else{
            $('#selectseller').after('<input type="hidden" id="seller" name="seller" value="'+seller+'"/>');
        }                

        var date = '';
        $('#notif').html('').removeClass('error');
        if( $('#date1').length && $('#date2').length ){
            date = '&date_start='+$('#date1').val()+'&date_end='+$('#date2').val();
        }

        $.ajax({
            url : '/admin/fdv/prize/ajax-prize.php?seller='+($('#seller').length ? $('#seller').val() : 0 )+date,
            beforeSend : function(){
                $('.load-ajax-opacity').show();
            },
            success : function(html){
                $('#palmares').html(html)
            }
        }).done(function(){
            $('.load-ajax-opacity').hide();
        }).fail(function(){
            $('.load-ajax-opacity').hide();
            $('#notif').html(prizeErreurChargement).addClass('error');
        });
    });
    
}).delegate( '.toggle-seller', 'click', function(){
    var elem = $(this);
    var i = 0;
    var eTable = elem.parents('table');
    
    eTable.find('tbody tr:hidden').each(function(){
        if (i >= 10) {
            return;
        }
        
        if (!$(this).hasClass('separator')) {
            $(this).show();
        }

        if ($(this).prev().hasClass('show-seller')) {
            eTable.find('.separator').hide();
            i++;
        }

        i++;
    });
    
    if (eTable.find('tbody tr.hidden-seller:hidden').length <= 0) {
        elem.hide();
    } else if (eTable.find('tbody tr.hidden-seller:hidden').length <= 10) {
        elem.html(prizeAfficheRepresentant);
    }
    
    eTable.find('.prize-only-first').show();
    return false;
}).delegate('.prize-only-first', 'click', function(){
    var i = 0;
    var eTable = $(this).parents('table');

    eTable.find('tbody tr').each(function(){
        if (i >= 3) {
            $(this).hide();
        }
        
        if (!$(this).hasClass('separator')) {
            i++;
        }

        eTable.find('.seperator, .show-seller').show();
    });

    if (eTable.find('.show-seller').length) {
        eTable.find('.show-seller').show();
        eTable.find('.separator').show();
    }

    eTable.find('.prize-only-first').hide();
    eTable.find('.toggle-seller').show();
});
