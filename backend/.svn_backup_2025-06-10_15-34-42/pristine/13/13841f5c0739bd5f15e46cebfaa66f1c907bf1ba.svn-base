var tabImgClsID = false;
var tabImgObjID_0 = false;
var tabImgObjID_1 = false;
var tabImgObjID_2 = false;
var urlParmsObjs = '';
var imoToDel;

$(document).ready(
	function(){
		renderSortableList();
		$('input[name=addimg]').click(function(){

			var url = '/admin/ajax/media/img_popup.php?classe=' + tabImgClsID + urlParmsObjs;
			displayPopup('Ajouter des images', '', url, false, 1010, 600);					
			return false;
		});
		$('input[name=delimg]').click(function(){
			if( confirm(tabMediaConfirmSupImg) ){
				var imgs_id = new Array(); 
				$('input[type=checkbox][name="imgs[]"]:checked').each(function(){
					imgs_id.push( $(this).val() );
				});

				var imgs_unpublish_id = new Array(); 
				$('input[type=checkbox][name="imgs-unpublish[]"]:checked').each(function(){
					imgs_unpublish_id.push( $(this).val() );
				});

				

				$.get('/admin/ajax/media/img_actions.php?classe=' + tabImgClsID + urlParmsObjs + '&delimg=1&del-img-main='+( $('input[type=checkbox][name=del-img-main ]:checked').length ? '1':'0' )+'&imgs-unpublish[]='+imgs_unpublish_id.join('&imgs-unpublish[]=')+'&imgs[]='+imgs_id.join('&imgs[]='),'', function(html){
					if (tabImgClsID == 1) {
						var html = JSON.parse(html);
						$('.imgs-w').html(html.publish);
						$('.imgs-w2').html(html.unpublish);
					}else{
						$('.imgs-w').html(html);
					}

					init_img();
				});
			}
			renderSortableList();
			return false;
		});


		// Supprime les images sélectionnées
		this.delImages = function(cls_id, obj_id_0, obj_id_1, obj_id_2){

			if ($('.msg')) {
				$('.msg').remove();
			}
			
			if (typeof imoToDel == undefined){
				alert(tabMediaAlertSelectionImg);
				return false;
			}else{
				if( !confirm(tabMediaConfirmSupImg) ) return false;
			}

			var url = '/admin/ajax/media/img_actions.php?';
				url += '&delObjImg=true';
				url += '&classe=' + cls_id;
				url += '&obj[]=' + obj_id_0;
				url += '&obj[]=' + obj_id_1;
				url += '&obj[]=' + obj_id_2;

			for (var i = 0; i < imoToDel.length; i++) {
				url += '&objs[]=' + imoToDel[i];
			}

			$.ajax({
				url: url,
				type: 'GET',
				dataType: 'json',
				success : function(response){
					$('li.preview[data-checked=checked]').remove();
					$('#site-content h2').after('<div class="msg success">' + response.message +'</div>');
					imoToDel = undefined;
				},
				error: function(response){
					$('#site-content h2').after('<div class="msg error">' + response.responseText +'</div>');
				}
			});

			return false;
		}

		// Gère la sélection/désélection des images
		this.prevClick = function(element, imo_id){
			
			if (typeof imoToDel === 'undefined') {
				imoToDel = Array();
			}

			if ($(element).attr('data-checked') !== 'undefined' && $(element).attr('data-checked') === 'checked') {
				$(element).attr('data-checked', '');

				if ($('#img-store-' + imo_id).length > 0) {
					$('#img-store-' + imo_id).attr('checked', false);
				}

				element.style.border = '';
				element.style.boxShadow = '';
				
				delete imoToDel[imo_id];
			} else {
				$(element).attr('data-checked', 'checked');

				if ($('#img-store-' + imo_id).length > 0) {
					$('#img-store-' + imo_id).attr('checked', true);
				}

				element.style.border = '1px solid #4080c0';
				element.style.boxShadow = '0px 0px 10px #4080c0';

				imoToDel.push(imo_id);
			}
		}

		this.addImg = function(type_id){
			var url = '/admin/ajax/media/img_popup.php?obj_type_id=' + type_id + '&classe=' + tabImgClsID + urlParmsObjs;
			displayPopup('Ajouter des images', '', url, false, 1010, 600);					
			return false;
		}

		this.delType = function(that, type_id){

			if (!window.confirm(tabMediaConfirmSupTypeImg)) { return false;}

			if ($('.msg')) {
				$('.msg').remove();
			}

			$.ajax({
				url: '/admin/ajax/media/img_types.php',
				type: 'GET',
				dataType: 'json',
				data: {
					action: 'del',
					type_id: type_id,
				},
				success : function(){
					$(that).parent().closest('tr').remove();
					if($('#body_type_img').find('tr').length == 0){
						$('#body_type_img').append('<tr class="no_type"><td colspan="2">Aucun type n\'a été défini.</td></tr>');
					}
					init_img();
				},
				error : function(error){
					$('#site-content h2').after('<div class="msg error">' + error.responseJSON.error +'</div>');
				}
			});
			return false;
		}

		this.editType = function(that, type_id){


			
			var form = $('<form id="type_form"></form>');
			
			var table = $('<table></table>');
			table.css({
				width: '90%',
				margin: '2em auto'
			});
			table.append('<caption>' + tabMediaCaptionImageLiee + '</caption>');

			var tbody = $('<tbody></tbody>');
			tbody.append('<tr class="col150px"><td><label for="typename">' + tabMediaLibelleTypeImg + '</label></td><td><input type="text" id="typename" value="'+$('.type_name'+type_id).text().replace(' :','')+'"></td></tr>');

			var foot = $('<tfoot></tfoot>');
			foot.append('<td colspan="2"><input type="submit" value="Valider"></td>');

			table.append(tbody);
			table.append(foot);
			form.append(table);

			var layout = $('<div id="popup-content"></div>');
			layout.append(form);

			form.submit(function(event) {
				event.preventDefault();

				if ($('.msg')) {
					$('.msg').remove();
				}

				var val = $.trim($('#type_form #typename').val())
				$.ajax({
					url: '/admin/ajax/media/img_types.php',
					type: 'GET',
					dataType: 'json',
					data: {
						type_name: val,
						type_id: type_id,
						action: 'update'
					},
					success: function(response){
						$('.type_name'+type_id).text(val);
						$('#site-content h2').after('<div class="msg success">' + response.message + '</div>');
						hidePopup();
					},
					error: function(error){
						$('#type_form').before('<div class="msg error">' + error.message + '</div>');
					}
				});
			});

			displayPopup('Edition du type', layout, '', false, 750, 200);

			return false;
		}

		this.addType = function(cls_id,obj0,obj1,obj2){
			
			var table_wrap = $('input[name=input_type_name]').parent().closest('table');
			var type_name = $('input[name=input_type_name]').val();

			if ($('.msg')) {
				$('.msg').remove();
			}
			
			if (!cls_id) {
				$('#site-content h2').after('<div class="msg error">' + tabMediaMsgErreurParam + '</div>');
				return false;
			}else if ($.trim(type_name).length == "") {
				$('#site-content h2').after('<div class="msg error">' + tabMediaMsgErreurTitre + '</div>');
				return false;
			}else if ($.trim(type_name).length > 45) {
				$('#site-content h2').after('<div class="msg error">' + tabMediaMsgErreurTypeImg + '</div>');
				return false;
			}

			$.ajax({
				url: '/admin/ajax/media/img_types.php',
				type: 'GET',
				dataType: 'json',
				data: {
					action: 'add',
					cls_id: cls_id,
					type_name: type_name,
					obj0: obj0,
					obj1: obj1,
					obj2: obj2
				},
				success : function(response){
					$('.no_type').remove();
					$('#typename').val('');
					$(table_wrap).append(response.html);
					// $('#site-content h2').after('<div class="msg success">' + response.message +'</div>');
					init_img();
				},
				error : function(error){
					$('#site-content h2').after('<div class="msg error">' + error.responseJSON.error +'</div>');
				}
			});		
			
			return false;

		}
		
	}
).delegate(
	'input[name=pop-ctr-addimg]', 'click', function(){
		var action = 'editInfosCtrMarket(' + $(this).attr('data-ctr-id') + ', ' + $(this).attr('data-prd-id') + ')';
		parent.displayPopup('Ajouter des images', '', '/admin/ajax/media/img_popup.php?classe=' + tabImgClsID + urlParmsObjs, action, 1010, 600);					
		return false;
	}
).delegate(
	'input[name=pop-ctr-delimg]', 'click', function(){
		if( confirm(tabMediaConfirmSupImg) ){
			var imgs_id = new Array(); 
			$('input[type=checkbox][name="imgs[]"]:checked').each(function(){
				imgs_id.push( $(this).val() );
			});
			$.get('/admin/ajax/media/img_actions.php?classe=' + tabImgClsID + urlParmsObjs + '&delimg=1&del-img-main='+( $('input[type=checkbox][name=del-img-main ]:checked').length ? '1':'0' )+'&imgs[]='+imgs_id.join('&imgs[]=') + '&in_popup=1','', function(html){
				$('.imgs-w').html(html);
				init_img();
			});
		}
		return false;
	}
);

function refresh_img_list(type_id){
	$.get('/admin/ajax/media/img_actions.php?obj_type_id=' + type_id + '&classe=' + tabImgClsID + urlParmsObjs,'', function(html){
		if (type_id && type_id > 0) {
			$('#imgs-w'+type_id).html(html);
		}else{
			if(tabImgClsID == 1){//Si produit
				var html = JSON.parse(html);
				$('.imgs-w').html(html.publish);
				$('.imgs-w2').html(html.unpublish);
			}else{
				$('.imgs-w').html(html);
			}
			
		}
		init_img();
	});				
}

function init_img( clsID, objID_0, objID_1, objID_2, imgClsObject){
	
	if( $.isNumeric(clsID) && clsID>0 ){
		tabImgClsID = clsID;
	}

	if( $.isNumeric(objID_0) && objID_0>0 ){
		tabImgObjID_0 = objID_0;
		urlParmsObjs += '&obj[0]=' + objID_0;
	}

	if( $.isNumeric(objID_1) && objID_1>0 ){
		tabImgObjID_0 = objID_1;
		urlParmsObjs += '&obj[1]=' + objID_1;
	}

	if( $.isNumeric(objID_2) && objID_2>0 ){
		tabImgObjID_0 = objID_2;
		urlParmsObjs += '&obj[2]=' + objID_2;
	}

	if( $.isNumeric(imgClsObject) && imgClsObject>0 ){
		urlParmsObjs += '&imgClsObject=' + imgClsObject;
	}
	$( ".sortable" ).each(function(){
		list = this;
		$(list).sortable({
			items: "li:not(.sortable-disabled)",
			connectWith: ".connectedSortable",
			helper: 'clone',
			tolerance: "pointer",
			
			start: function(event, ui){
				ui.item.css('opacity','0.3');
			},
			stop: function(event, ui){
				var liste_dest = ui.item.parent().attr('id');
				var liste_source = $(this).attr('id');
				$('li',list).fadeIn(1);
				ui.item.css('opacity','1');
				// image suivante
				img_next = false;
				if( ui.item.next().length ){
					img_next = ui.item.next().attr('id').replace('img','');
				}
				// image précédente
				img_prev = false;
				if( typeof ui.item.prev().attr('id') != 'undefined' ){
					img_prev = ui.item.prev().attr('id').replace('img','');
				}
				if ($(this).data('typeid') > 0) {
					urlParmsObjs += '&type_id=' + $(this).data('typeid');
				}
				if((liste_source == 'sortable1' && liste_dest == 'sortable2') || (liste_dest == 'sortable2' && liste_source == 'sortable2') ){
					data = 'classe=' + tabImgClsID + urlParmsObjs + '&img='+ui.item.attr('id').replace('img','')+'&img_next='+img_next+'&img_prev='+img_prev+'&position=-1'; 
				}else{
					data = 'classe=' + tabImgClsID + urlParmsObjs + '&img='+ui.item.attr('id').replace('img','')+'&img_next='+img_next+'&img_prev='+img_prev+'&position='+ui.item.index();
				}
				if(liste_source == 'sortable2' && liste_dest == 'sortable1'){
					$("#no_img").remove();
						$(ui.item).children('input').attr('name', 'imgs[]');
				}

				if(liste_source == 'sortable1' && liste_dest == 'sortable2'){
					if($('#sortable1 > *').length == 0){
						$("#sortable1").append('<li class="sortable-disabled" id="no_img">' + tabMediasAucuneImagePublie + '</li>');
					}
					$(ui.item).children('input').attr('name', 'imgs-unpublish[]');
				}
				renderSortableList();
				$.get('/admin/ajax/media/img_pos.php', data, function(){
					if( typeof $('.has-main-img') != 'undefined' && $('.has-main-img').length ){
						if(typeof $('[name=del-img-main]').parents('li').attr('id') != 'undefined'){
							var firstImage = $('.imgs-w li:first');
							if( typeof $(firstImage).attr('id') != 'undefined'){

								$('[name=del-img-main]').attr('name', 'imgs[]');

								$('.main-img').removeClass('main-img');

								firstImage.find('.checkbox').attr('name', 'del-img-main');
								firstImage.addClass('main-img');
							}	
						}
					}
				});
			}
		});
	});
	
	$( ".sortable" ).disableSelection();

}



//Modifie la taille des listes d'images
var renderSortableList = function(){
	var nbImgPerRow = Math.floor($('.sortable').width()/159);
	var nbRowImgPublish = Math.ceil(($('#sortable1 > *').length - 1)/ nbImgPerRow)+1;
	var nbRowImgUnpublish = Math.ceil($('#sortable2 > *').length / nbImgPerRow);
	$("#sortable1").css("height",nbRowImgPublish*159);
	$("#sortable2").css("height",nbRowImgUnpublish*159);
}

