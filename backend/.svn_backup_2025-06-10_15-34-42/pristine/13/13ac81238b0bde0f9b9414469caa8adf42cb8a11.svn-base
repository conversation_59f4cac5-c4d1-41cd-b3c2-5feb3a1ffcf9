var oldIBAN = '';
$(document).ready(
	function(){
		$('.checkall').click(function(){
			$('.check-prf').attr('checked', 'checked');
			return false;
		});
		
		$('.uncheckall').click(function(){
			$('.check-prf').removeAttr('checked', 'checked');
			return false;
		});
		
		$('#save').click(function(){
			$('.error, .success').remove();
		});
	}
).delegate( '#del-all', 'click', 
	function(){
		
		if( $(this).is(':checked') ){
			$('.del-trf').attr('checked', 'checked');
		} else {
			$('.del-trf').removeAttr('checked');
		}
		
	}
).delegate( '#cbank, #counter, #account, #key', 'keyup', 
	function(){
		var iban = $('#cbank').val() + $('#counter').val() + $('#account').val() + $('#key').val();
		
		if( oldIBAN!=iban ){
			oldIBAN = iban;
			
			$('#iban2').val( '' );
			$('#iban3').val( '' );
			$('#iban4').val( '' );
			$('#iban5').val( '' );
			$('#iban6').val( '' );
			$('#iban7').val( '' );

			iban = iban.match(new RegExp(".{0,4}", "g")).join('~');
			var partIBAN = iban.split( new RegExp("~", "g") );
			
			$('#iban2').val( partIBAN[0] );
			$('#iban3').val( partIBAN[1] );
			$('#iban4').val( partIBAN[2] );
			$('#iban5').val( partIBAN[3] );
			$('#iban6').val( partIBAN[4] );
			$('#iban7').val( partIBAN[5] );
			
		}
		
	}
);

function confirmDelTransfer(){
	if( !$('.del-trf').length || parseInt($('.del-trf:checked').length)>0 ){
		return window.confirm(paiementsConfirmSuppressionInfoBancaire);
	}
	
	return false;
}