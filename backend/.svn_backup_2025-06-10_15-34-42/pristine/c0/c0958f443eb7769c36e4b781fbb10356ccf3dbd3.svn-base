<?php
require_once('email.inc.php');
require_once('PaymentExternal/Payplug_old.inc.php');

define('ADMIN_PAGE_TITLE', _('Paiement de mon abonnement'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');
define('ADMIN_CLASS_BODY', 'popup-yuto-payment');

// Dans le cas d'une réactivation d'abonnement, il n'y a pas de numéro d'étape
if (!isset($_GET['step_max'])) {
	$_GET['step_max'] = 0;
}

// Réinitialise le tableau d'erreur(s)
$error = array();

// Initialisation des informations sur l'abonnement
$licences 	= isset($_GET['licences']) && is_numeric($_GET['licences']) && $_GET['licences'] > 0 ? $_GET['licences'] : 1;
$type		= isset($_GET['type']) && in_array($_GET['type'], array('month', 'year')) ? $_GET['type'] : 'month';

// Chargement des informations sur le produit correspondant à l'abonnement choisi (depuis RiaStudio)
$package = RegisterGCP::getPackage($config['tnt_id']);
$prd_packages = dev_package_get_products($package);

// Contrôle la récupération des informations sur l'abonnement
if (!is_array($prd_packages) || !count($prd_packages)) {
	$g_error = _('Impossible de charger les informations sur l\'abonnement choisi.');
}

// S'il s'agit de la version Business, un surcoût hébergement est appliqué
if ($package == 'business') {
	$hosting = false;
	foreach ($prd_packages['hosting'] as $qty => $data) {
		if ($licences >= $qty) {
			$hosting = $data;
		}
	}
}

// Récupère l'abonnement actuel
$now_subscribtion = dev_subscribtions_yuto_get();

// Calcul les date du future abonnement
// Ajoute une journée à la période d'essai pour déterminer la date de début et de fin de l'abonnement
// Seulement si un abonnement en cours est active
if ($now_subscribtion) {
	$date_start = new Datetime(dateparse($now_subscribtion['date_end']));
	$date_end = new Datetime(dateparse($now_subscribtion['date_end']));

	$date_start->modify('+ 1 day');
}else{
	$date_start = new DateTime();
	$date_end = new DateTime(); // La date de fin est calculée plus tard en fonction du type d'abonnement choisi
}

// Initialise le tableau contenant les informations sur l'abonnement choisi
$cart_abo = array();

// Récupère l'identifiant client RiaStudio lié à l'abonnement
$user_sub = dev_subscribtions_get_user_yuto();

$old_config = $config;
$old_ria_db_connect = $ria_db_connect;

// Réalise une connexion à RiaStudio
RegisterGCPConnection::init(52, true);

// Si l'on est pas en cours d'exécution du formulaire de paiement, le panier est créé
if (!isset($_POST['payplugToken'])) {
	$config['use_catalog_restrictions'] = false;

	// Création d'un panier
	if (isset($_SESSION['admin_ord_yuto3']) && ord_orders_exists($_SESSION['admin_ord_yuto3'], $user_sub, _STATE_BASKET)) {
		$ord_id = $_SESSION['admin_ord_yuto3'];
	} else {
		$ord_id = ord_orders_add_sage($user_sub, date('d/m/Y'), _STATE_BASKET, '', '', '', 0, 227);
		if (is_numeric($ord_id) && $ord_id > 0) {
			$_SESSION['admin_ord_yuto3'] = $ord_id;
		}
	}

	if ($ord_id) {
		$ord_prd = $prd_packages[$type == 'month' ? 'mensuel' : 'annuel'];

		// Le panier est vidé à chaque fois pour prendre en compte tout changement fait entre deux rechargements de pages
		ord_products_del($ord_id);

		// Ajoute le bon produit à la commande en fonction du type d'abonnement
		if (!ord_products_add($ord_id, $ord_prd['id'], $licences, '', false, null, 0, false, 0, $ord_prd['sub_id'])) {
			$g_error = _('Une erreur est survenue lors la commande de votre abonnement. Veuillez réessayer ou prendre <a target="_blank" href="https://www.yuto.fr/contact/">contact avec nous</a>.');
		}

		// Ajout du coût de l'hébergement pour la version Business
		if ($package == 'business') {
			if (!ord_products_add($ord_id, $hosting['id'], ($type == 'month' ? 1 : 12), '', false, null, 0, false, 0)) {
				$g_error = _('Une erreur est survenue lors la commande de votre abonnement. Veuillez réessayer ou prendre <a target="_blank" href="https://www.yuto.fr/contact/">contact avec nous</a>.');
			}
		}

		// Date de fin de l'abonnement
		fld_object_values_set($ord_id, 100073, $date_end->format('d/m/Y'));
	}
}

// Charge les informations du panier dans une variable
$cart_abo = ria_mysql_fetch_assoc(ord_orders_get_simple(
	array('id' => $_SESSION['admin_ord_yuto3']),
	array(),
	array(),
	array(),
	array(
		'type' => 'replace',
		'columns' => array(
			'ord_id' => 'id',
			'ord_usr_id' => 'user_id',
			'ord_total_ht' => 'total_ht',
			'ord_total_ttc' => 'total_ttc',
		)
	)
));

// Paiement de l'abonnement avec une empreinte bancaire déjà enregistré
if (isset($_GET['use-print']) && $_GET['use-print'] == 1) {
	try {
		$payplug = new Payplug();
		$payplug->createSimplePayment($cart_abo['id'], false, false, true);
		$payplug->_getPaymentResult($_SESSION['payplug_payment_id']);
	} catch (Exception $e) {
		$error[] = _('Une erreur inattendue s\'est produite lors du règlement de votre abonnement. Merci de prendre contact avec l\'adresse : <a href="mailto:<EMAIL>"><EMAIL></a>.');
		mail('<EMAIL>', 'error payplug', print_r($e, true));
	}

	if (!count($error)) {
		$data = http_build_query(array(
			'step_max' => $_GET['step_max'],
			'type' => $_GET['type'],
			'hiddenform' => true,
		));

		$_SESSION['success_pay_abo'] = array(
			'ord' 			=> $cart_abo['id'],
			'type' 			=> $type,
			'user' 			=> $cart_abo['user_id'],
			'total_ht' 	=> $cart_abo['total_ht'],
			'total_ttc' => $cart_abo['total_ttc'],
			'licences' 	=> $licences,
		);

		header('Location: /options/popup-payment.php?' . $data);
		exit;
	}
}

// Paiement de l'abonnement avec enregistrement de l'empreinte bancaire pour la récursivité
if (isset($_POST['payplugToken'])) {
	if (!count($error)) {
		$pay_id = '';

		// Réalise le paiement
		try {
			$payplug = new Payplug();
			$pay_id = $payplug->createSimplePayment($cart_abo['id'], true, $_POST['payplugToken']);
		} catch (Exception $e) {
			$error[] = _('Une erreur inattendue s\'est produite lors du règlement de votre abonnement. Merci de prendre contact avec l\'adresse : <a href="mailto:<EMAIL>"><EMAIL></a>.');
			mail('<EMAIL>', 'error payplug', print_r($e, true));
		}

		if (trim($pay_id) != '') {
			if ($payplug->_getPaymentResult($pay_id) === false) {
				$error[] = _('Une erreur inattendue s\'est produite lors du règlement de votre abonnement. Merci de prendre contact avec l\'adresse : <a href="mailto:<EMAIL>"><EMAIL></a>.');
				mail('<EMAIL>', 'error payplug', print_r($e, true));
			}
		}
	}

	if (!count($error)) {
		$data = http_build_query(array('step_max' => $_GET['step_max'], 'type' => $_GET['type']));
		$_SESSION['success_pay_abo'] = array(
			'ord' 			=> $cart_abo['id'],
			'type' 			=> $type,
			'user' 			=> $cart_abo['user_id'],
			'total_ht' 	=> $cart_abo['total_ht'],
			'total_ttc' => $cart_abo['total_ttc'],
			'licences' 	=> $licences,
		);

		header('Location: /options/popup-payment.php?' . $data);
		exit;
	}
}

$config = $old_config;
$ria_db_connect = $old_ria_db_connect;

// Charge les informations sur le compte administrateur
$user = ria_mysql_fetch_assoc(gu_users_get($_SESSION['usr_id']));

require_once('admin/skin/header.inc.php');
?>
<div class="msg"><?php
	if( isset($_SESSION['success_pay_abo']) ){
		// Détermine la date de fin de l'abonnement en fonction du type mensuel ou annuel
		$date_end->modify('+ 1 ' . $_SESSION['success_pay_abo']['type']);

		// Création du nouvel abonnement une fois le paiement réalisé
		if( !dev_subscribtions_add($_SESSION['success_pay_abo']['licences'], $date_start->format('Y-m-d'), $date_end->format('Y-m-d'), 0, $_SESSION['success_pay_abo']['ord'], $_SESSION['success_pay_abo']['total_ttc'], $_SESSION['success_pay_abo']['user'], $_SESSION['success_pay_abo']['total_ht']) ){
			$error[] = _('Une erreur est survenue lors de l\'enregistrement de votre abonnement. Merci de prendre <a target="_blank" href="https://www.yuto.fr/contact/">contact avec nous</a>.');
		}

		// Force le rechargement des droits à l'administration pour le compte en cours de connexion
		// Il peut y avoir deux clé de cache.
		$memcached->delete($config['tnt_id'].':'.$config['wst_id'].':gu_users_load_admin_rights:usr-'.$_SESSION['usr_id'].':with_code-0');
		$memcached->delete($config['tnt_id'].':'.$config['wst_id'].':gu_users_load_admin_rights:usr-'.$_SESSION['usr_id'].':with_code-1');

		?><script>
			parent.location.href = '/admin/options/subscription.php';
		</script><?php
	}

	if (count($error)) {
		print '<div class="error">'.nl2br( implode('<br >', $error) ).'</div>';
	}
?></div>

<?php if (!isset($_GET['hiddenform'])) { ?>
	<form action="popup-payment.php?<?php print http_build_query($_GET); ?>" method="post" id="payplug-form" data-payplug="form">
		<h2><?php
					if (is_numeric($_GET['step_max']) && $_GET['step_max'] > 0) {
						print str_replace('#param[etape]#', $_GET['step_max'], _('Étape #param[etape]# sur #param[etape]# : Paiement de mon abonnement'));
					} else {
						print _('Paiement de mon abonnement');
					}
					?></h2>

		<p><?php
			if( $cart_abo['total_ttc'] == $cart_abo['total_ht'] ){
				print str_replace(
					array(
						'#param[type]#',
						'#param[total]#',
						'#param[licences]#',
						'#param[date]#',
					),
					array(
						$_GET['type'] == 'month' ? _('mensuel') : _('annuel'),
						number_format($cart_abo['total_ht'], 2, ',', ' '),
						$licences . ' ' . ($licences > 1 ? _('licences') : _('licence')),
						$date_start->format('d/m/Y'),
					),
					_('Vous avez choisi un règlement #param[type]#, le montant de votre abonnement est de <strong>#param[total]# € HT pour #param[licences]#</strong>. Votre compte bancaire sera débité suite à la validation de votre abonnement. Celui-ci débutera à l’issue de votre période d’essai, le #param[date]#.')
				);
			}else{
				print str_replace(
					array(
						'#param[type]#',
						'#param[total]#',
						'#param[licences]#',
						'#param[date]#',
					),
					array(
						$_GET['type'] == 'month' ? _('mensuel') : _('annuel'),
						number_format($cart_abo['total_ttc'], 2, ',', ' '),
						$licences . ' ' . ($licences > 1 ? _('licences') : _('licence')),
						$date_start->format('d/m/Y'),
					),
					_('Vous avez choisi un règlement #param[type]#, le montant de votre abonnement est de <strong>#param[total]# € TTC pour #param[licences]#</strong>. Votre compte bancaire sera débité suite à la validation de votre abonnement. Celui-ci débutera à l’issue de votre période d’essai, le #param[date]#.')
				);
			}
		?></p>
		<dl style="border:none;">
			<dt><?php print _('Mes coordonnées bancaires'); ?></dt>
			<dd>
				<div style="display: inline-block;">
					<label for="firstname"><span class="mandatory">*</span> <?php print _('Prénom :') ?></label><br />
					<input name="firstname" id="firstname" type="text" placeholder="" autocomplete="off" value="<?php echo htmlspecialchars($user['adr_firstname']); ?>" required />
				</div>
				<div style="display: inline-block;">
					<label for="lastname"><span class="mandatory">*</span> <?php print _('Nom :') ?></label><br />
					<input name="lastname" id="lastname" type="text" placeholder="" value="<?php echo htmlspecialchars($user['adr_lastname']); ?>" required />
				</div>
			</dd>
			<dd>
				<label for="card_number"><span class="mandatory">*</span> <?php print _('Numéro de carte :'); ?></label><br />
				<input id="card_number" name="card_number" type="text" placeholder="Numéro de carte" value="" pattern="[0-9\s]{19}" required />
				<input type="hidden" name="card_number_copy" id="card_number_copy" value="" data-payplug="card_number" />
			</dd>
			<dd>
				<div style="display: inline-block;">
					<label for="card_month_year"><span class="mandatory">*</span> <?php print _('Date d\'expiration :'); ?></label><br />
					<input id="card_month_year" name="card_month_year" type="text" placeholder="MM/AA" data-payplug="card_month_year" value="" required />
				</div>
				<div style="display: inline-block;">
					<label for="card_cvv"><span class="mandatory">*</span> <?php print _('Code de validation :'); ?></label><br />
					<input style="width:60px !important" id="card_cvv" name="card_cvv" type="text" placeholder="123" autocomplete="off" data-payplug="card_cvv" value="" min="0" max="999" step="1" maxlength="3" required />
				</div>
			</dd>

			<dd>&nbsp;</dd>
			<dd>
				<input type="checkbox" id="cgv" name="cgv" required>
				<label for="cgv">&nbsp;<?php print _('J\'accepte les '); ?><a target="_blank" rel="noopener noreferrer" href="https://start.yuto.fr/conditions-generales-de-vente/"><?php print _('Conditions générales de vente') ?></a>.</label>
			</dd>
			<dd>&nbsp;</dd>

			<dd>
				<button type="submit" name="validpaiement" class="btn btn-primary" data-payplug="submit"><?php print _('J\'accepte le paiement de mon abonnement'); ?></button>
				<ul class="error" style="display:none;">
					<li id="error-card-bad" style="display:none;"><?php print _('Numéro de carte invalide.'); ?></li>
					<li id="error-cvv-bad" style="display:none;"><?php print _('Cryptogramme invalide.'); ?></li>
					<li id="error-expiry-bad" style="display:none;"><?php print _('Date d\'expiration invlide'); ?></li>
				</ul>
			</dd>
			<dd>&nbsp;</dd>
		</dl>
	</form>

	<!-- Javacript propre au module de paiement PayPlug -->
	<script src="https://api.payplug.com/js/1.0/payplug.js"></script>
	<script>
		<!--
		Payplug.setPublishableKey('<?php echo $config['payplug_public_key']; ?>');

		var payplugResponseHandler = function(code, response, details) {
			$('ul.error, #error-card-bad, #error-cvv-bad, #error-expiry-bad').hide();
			var oneError = false;

			if (code == 'card_number_invalid') {
				$('#error-card-bad').show();
				oneError = true;
			}

			if (code == 'cvv_invalid') {
				$('#error-cvv-bad').show();
				oneError = true;
			}

			if (code == 'expiry_date_invalid') {
				$('#error-expiry-bad').show();
				oneError = true;
			}

			if (oneError) {
				$('ul.error').show();
				$('.popup_ria_back_load, .popup_ria_back_notice').remove();
			}

			return false;
		};

		document.addEventListener('DOMContentLoaded', function() {
			[].forEach.call(document.querySelectorAll("[data-payplug='form']"), function(el) {
				el.addEventListener('submit', function(event) {
					$('body').append('<div class="popup_ria_back_load abs"></div><div class="popup_ria_back_notice notice">' + yutoPaymentAbo + '</div>');
					$('.popup_ria_back_load, .popup_ria_back_notice').show();
					$('.popup_ria_back_load, .popup_ria_back_notice').css('z-index', '1')

					var form = document.querySelectorAll("#payplug-form")[0];
					Payplug.card.createToken(form, payplugResponseHandler, {
						'amount': <?php print round($cart_abo['total_ttc'], 2) * 100; ?>,
						'currency': 'EUR'
					});

					event.preventDefault();
				})
			})
		});

		$(document).ready(function() {
			// Ajout de masque de saisir sur le numéro de carte et la date d'expiration
			$('#card_number').mask('0000 0000 0000 0000');
			$('#card_month_year').mask('00/00');

			// Ajout d'une copie automatique du numéro de carte dans un champ hidden car Payplug refuse tout espace dans le numéro de carte
			$('#card_number').on('change input', function() {
				$('#card_number_copy').val($(this).val().replace(/\s/g, ''));
			});
		});
		-->
	</script>
	<?php
		if (isset($_SESSION['success_pay_abo'])) {
			?><script>
			$('body').append('<div class="popup_ria_back_load abs"></div><div class="popup_ria_back_notice notice">' + yutoPaymentAbo + '</div>');
			$('.popup_ria_back_load, .popup_ria_back_notice').show();
			$('.popup_ria_back_load, .popup_ria_back_notice').css('z-index', '1')
		</script><?php
								}
							}
							?>