<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  37161 => 'Jūrmala',
  37162 => 'Valmiera',
  371630 => 'Jelgava',
  371631 => 'Tukums',
  371632 => 'Talsi',
  371633 => 'Kuldiga',
  371634 => 'Liepaja',
  371635 => 'Ventspils',
  371636 => 'Ventspils',
  371637 => 'Dobele',
  371638 => 'Saldus',
  371639 => 'Bauska',
  371640 => 'Limbaži',
  371641 => 'Cēsis',
  371642 => 'Valmiera',
  371643 => '<PERSON><PERSON><PERSON><PERSON>',
  371644 => '<PERSON><PERSON>ben<PERSON>',
  371645 => 'Balvi',
  371646 => '<PERSON>ē<PERSON>kne',
  371647 => '<PERSON><PERSON>',
  371648 => '<PERSON><PERSON>',
  371649 => '<PERSON><PERSON><PERSON>uk<PERSON>',
  371650 => 'Ogre',
  371651 => 'Aizkraukle',
  371652 => 'Jēkabpils',
  371653 => 'Preiļi',
  371654 => 'Daugavpils',
  371655 => 'Ogre',
  371656 => 'Krāslava',
  371657 => 'Ludza',
  371658 => 'Daugavpils',
  371659 => 'Cēsis',
  37166 => 'Riga',
  37167 => 'Riga',
  371682 => 'Valmiera',
  371683 => 'Jēkabpils',
  371684 => 'Liepāja',
  371686 => 'Jelgava',
  37169 => 'Riga',
);
