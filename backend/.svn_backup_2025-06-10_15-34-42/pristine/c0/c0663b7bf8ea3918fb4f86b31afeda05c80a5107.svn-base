sudo: false

language: php

matrix:
    fast_finish: true
    include:
        # Need to use Trusty for PHP 5.3
        - php: 5.3
          env: COMPOSER_FLAGS="--prefer-stable --prefer-lowest"
          dist: precise
        - php: 5.4
        - php: 5.5
        - php: 5.6
        - php: 7.0
        - php: 7.1
        - php: 7.2
        # Use the newer stack for HHVM as HHVM does not support Precise
        - php: hhvm
          dist: trusty

before_script:
 - travis_retry composer update --no-interaction $COMPOSER_FLAGS
 - mkdir -p build/logs

script:
  - ./vendor/bin/phpunit --coverage-text --coverage-clover build/logs/clover.xml

after_script:
  - php vendor/bin/coveralls -v --exclude-no-stmt
  - wget https://scrutinizer-ci.com/ocular.phar
  - php ocular.phar code-coverage:upload --format=php-clover build/logs/clover.xml

notifications:
  irc: "irc.appliedirc.com#applied"
