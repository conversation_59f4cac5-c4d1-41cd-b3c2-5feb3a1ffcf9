<?php
/// \cond onlyria

require_once('define.inc.php');
require_once('users.inc.php');

/** \defgroup model_couchDB CouchDb
 *	\ingroup yuto system
 *	Ce module comprend les fonctions nécessaires à la gestion de couchDb
 *	@{
 */

class CouchDB {
	private static $_couchdb_conf = array();

	/** Cette fonction permet d'initialisé une connexion à couchDB sur la base riasahop par défaut.
	 * 	@param string $db Optionnel, permet de dire quelle base utiliser
	 * 	@return object Un objet CouchDB
	 */
	public static function create($db=_COUCHDB_DB_NAME){
		global $config;

		// cas de certaine bdd dupliqué par tenant
		$db_renamed = $db;

		if (!isset($config['env_sandbox']) || $config['env_sandbox']) {
			// Espace de maquette
			$connection = array(
				'db' => getenv('ENVRIA_COUCHDB_DB_NAME_PREFIX').'_'.$db_renamed,
				'design_doc' => $db_renamed,
				'protocol' => 'http',
				'domain' => 'couchdb-dev:5984',
				'login' => 'admin2',
				'password' => '123456',
			);
		} else {
			// Espace de production
			$connection = array(
				'db' => getenv('ENVRIA_COUCHDB_DB_NAME_PREFIX').'_'.$db_renamed,
				'design_doc' => $db_renamed,
				'protocol' => getenv('ENVRIA_COUCHDB_PROTOCOL'),
				'domain' => getenv('ENVRIA_COUCHDB_URL'),
				'login' => getenv('ENVRIA_COUCHDB_LOGIN'),
				'password' => getenv('ENVRIA_COUCHDB_PASSWORD')
			);
		}

		$couchDB = new CouchDB();
		self::setConnection($connection);

		return $couchDB;
	}

	/** Cette fonction permet de définir les paramètres de connexion
	 *  @param array $connect Obligatoire, tableau devant contenu : db, protocol, domain, login et password
	 */
	public static function setConnection($connect) {
		if (!ria_array_key_exists(array('db', 'protocol', 'domain', 'login', 'password'), $connect)) {
			return false;
		}

		self::$_couchdb_conf = $connect;
	}

	/** Permet la récupération d'un document
	 * 	@param int $cls_id Obligatoire, Identifiant de la classe souhaité
	 * 	@param int $id Obligatoire, Identifiant du document
	 * 	@param array $params Facultatif, tableau de paramètres complémentaires
	 * 	@return object un objet avec tous le contenu du document si celui-ci existe, sinon un objet avec avec une "error"
	 */
	public static function get($cls_id, $id, $params=array()){
		global $config;

		$data = self::exec('GET', $id );

		// check en fonction des paramètres donné
		if( isset($data['_id']) ){
			$params = array_merge(array(
				'tnt_id' => $config['tnt_id'],
				'cls_id' => $cls_id,
			), $params);

			foreach( $params as $k => $p ){
				if( !isset($data[$k]) || $data[$k]!=$p ){
					return false;
				}
			}

			$result = $data['content'];
			$result['_id'] = $data['_id'];
			return $result;

		}
		return false;
	}

	/** Permet de retourner les changements fait sur un type de donnée
	 *  @param int $cls_id : Identifiant de la classe souhaité
	 *  @param $filter : identifiant du filtre à utiliser
	 *  @param $params : Tablaeau de paramètre clé => valeur pour filtrer au mieux les besoins
	 *      - since : id de séquence pour récupérer les data depuis un id particulier
	 *      - include_docs : si true permet d'inclure tous le contenu des documents dans le retour
	 *      - limit : nombre de ligne maximal à retourner
	 *      - descending : mode de trie de la liste, si false les données les plus anciennes seront en premier.
	 *  Paramètre en fonction du filtre :
	 *      - sync :
	 *          - need_sync : Permet de récupérer que les éléments à synchroniser
	 *          - is_sync : Permet de ne récupérer que les éléments synchronisé ou non
	 *
	 *  @return object un objet avec comme contenu :
	 *      - results : liste des objets retournée sous forme de tableau d'objets
	 *      ou
	 *      - error : si une erreur de récupération à eu lieu
	 */
	public static function getChanges($cls_id, $timestamp=0, $limit=0, $offset=0){
		global $config;

		if( !is_numeric($timestamp) ){
			$timestamp = 0;
		}

		$params = array();
		$params["startkey"] = array($config['tnt_id'], $cls_id, $timestamp);
		$params["endkey"] = array($config['tnt_id'], $cls_id, time());
		$params["descending"] = false;
		$params["include_docs"] = true;

		if( $limit > 0 ){
			$params["limit"] = $limit;
		}
		if( $offset > 0 ){
			$params["skip"] = $offset;
		}

		return self::getView('cls_by_modified', $params, null, false);
	}

	/** Permet de retourner les un count sur les changements fait, attention dès que l'on trouve 1 changement on additionnes tous les autres changements tous tenant, le total sera tjr plus élevé que le nombre de doc réel pour ce tenant, mais meilleur en perf
	 * 	@param int $cls_id : Identifiant de la classe souhaité
	 * 	@param $timestamp : date tipe timestamp à partir de laquel commencer à récupérer les datas
	 *
	 *  @return int le nombre d'éléments dans la vue
	 */
	public static function getChangesCount($cls_id, $timestamp=0){
		global $config;

		if( !is_numeric($timestamp) ){
			$timestamp = 0;
		}

		$params = array();
		$params["startkey"] = array($config['tnt_id'], $cls_id, $timestamp);
		$params["endkey"] = array($config['tnt_id'], $cls_id, time());
		$params["descending"] = false;

		return self::getViewCount('cls_by_modified', $params);
	}

	/** DEPRECATED, pas bonne niveau perf : préférer l'utilisation de vue.
	 * Permet de retourner les changements fait sur un type de données
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 * 	@param $filter : identifiant du filtre à utiliser
	 *	@param $params : Tableau de paramètre clé => valeur pour filtrer au mieux les besoins
	 *		- since : id de séquence pour récupérer les data depuis un id particulier
	 *		- include_docs : si true permet d'inclure tous le contenu des documents dans le retour
	 *		- limit : nombre de ligne maximal à retourner
	 *		- descending : mode de trie de la liste, si false les données les plus anciennes seront en premier.
	 *	Paramètre en fonction du filtre :
	 *		- sync :
	 *			- need_sync : Permet de ne récupérer que les éléments à synchroniser
	 *			- is_sync : Permet de ne récupérer que les éléments synchronisés ou non
	 *
	 *	@return object un objet avec comme contenu :
	 *		- results : liste des objets retournés sous forme de tableau d'objets
	 *		ou
	 *		- error : si une erreur de récupération a eu lieu
	 */
	public static function getAll($cls_id, $params=array()){
		global $config;

		$params['tnt_id'] = (int)$config['tnt_id'];
		$params['cls_id'] = $cls_id;
		$params['deleted'] = array('$exists' => false);

		$tmp_param = array();

		$primary = array('sort','limit','skip');
		foreach( $primary as $p ){
			if( isset($params[$p]) ){
				$tmp_param[$p] = $params[$p];
				unset($params[$p]);
			}
		}

		$selector = array();

		$primary = array('_id','tnt_id','cls_id','created','deleted');
		foreach( $primary as $p ){
			if( isset($params[$p]) ){
				$selector[$p] = $params[$p];
				unset($params[$p]);
			}
		}

		if( sizeof( $params ) ){
			foreach( $params as $k => $v ){
				$selector['content.'.$k] = $v;
			}
		}

		$tmp_param['selector'] = $selector;

		$res = self::exec('POST', '_find', $tmp_param );
		if( !$res || isset($res['error']) ){
			return false;
		}

		// parcours la liste des result pour ne retourner que les content
		$final = array();

		$results = $res['docs'];
		foreach( $results as $r ){
			$tmp = $r['content'];
			$tmp['_id'] = $r['_id'];

			$final[] = $tmp;
		}

		return $final;
	}

	/** Cette fonction permet la récupération de documents via une vue sur CouchDb.
	 *  @param string $view - Chaine de caractère représentant le code de la vue
	 *  @param string $params - Tableau de paramètre clé => valeur pour filtrer au mieux les besoins
	 *      - startkey : Clé de début de la forme suivante "Key1,Key2,Key3..."
	 *      - endkey : Clé de fin de la forme suivante "Key1,Key2,Key3..."
	 *      - include_docs :  si true permet d'inclure tous le contenu des documents dans le retour
	 *      - descending : mode de trie de la liste, si false les données les plus anciennes seront en premier.
	 *	@return array un tableau avec :
	 *		- results : liste des objets retournés sous forme de tableau d'objets
	 *		ou
	 *		- error : si une erreur de récupération a eu lieu
	 */
	public static function getView($view, $params=array(), $design=null, $remove_deleted=true){
		if( trim($view)=='' ) return false;
		global $config;

		$params['reduce'] = false;

		$url = '_design/'.($design ? $design : self::$_couchdb_conf['design_doc']).'/_view/'.$view.'?'.self::queryString($params);
		$res = self::exec('GET', $url);

		if( !$res || isset($res['error']) ){
			return false;
		}

		$results = [];

		// Retire du résultat les lignes ayant été supprimées
		if( isset($res['rows']) && is_array($res['rows']) ){
			foreach( $res['rows'] as $row ){
				if( !$remove_deleted || (!isset($row['doc']['deleted']) || trim($row['doc']['deleted']) == '') ){
					$results[] = $row;
				}
			}
		}

		return $results;
	}

	/** Cette fonction permet de récupérer le nombre de documents via une vue CouchDb
	 *  @param string $view - Chaine de caractère représentant le code de la vue
	 *  @param string $params - Tableau de paramètre clé => valeur pour filtrer au mieux les besoins
	 *      - startkey : Clé de début de la forme suivante "Key1,Key2,Key3..."
	 *      - endkey : Clé de fin de la forme suivante "Key1,Key2,Key3..."
	 *      - include_docs :  si true permet d'inclure tous le contenu des documents dans le retour
	 *      - descending : mode de trie de la liste, si false les données les plus anciennes seront en premier.
	 *
	 *  @return int le nombre d'éléments dans la vue
	 */
	public static function getViewCount($view, $params=array()){
		if( trim($view)=='' ) return false;

		global $config;

		$params['reduce'] = true;

		$url = '_design/'.self::$_couchdb_conf['design_doc'].'/_view/'.$view.'?'.self::queryString($params);
		$res = self::exec('GET', $url);

		if( !$res || isset($res['error']) ){
			return false;
		}

		$value = sizeof($res['rows']) ? $res['rows'][0]['value'] : 0;

		return $value;
	}

	/** Cette fonction permet de récupérer le reduce custom via une vue CouchDb
	 *  @param string $view - Chaine de caractère représentant le code de la vue
	 *  @param string $params - Tableau de paramètre clé => valeur pour filtrer au mieux les besoins
	 *      - startkey : Clé de début de la forme suivante "Key1,Key2,Key3..."
	 *      - endkey : Clé de fin de la forme suivante "Key1,Key2,Key3..."
	 *      - include_docs :  si true permet d'inclure tous le contenu des documents dans le retour
	 *      - descending : mode de trie de la liste, si false les données les plus anciennes seront en premier.
	 *
	 *  @return int le nombre d'éléments dans la vue
	 */
	public static function getViewReduce($view, $params=array()){
		if( trim($view)=='' ) return false;

		global $config;

		$params['reduce'] = true;

		$url = '_design/'.self::$_couchdb_conf['design_doc'].'/_view/'.$view.'?'.self::queryString($params);
		$res = self::exec('GET', $url);

		if( !$res || isset($res['error']) ){
			return false;
		}

		$results = [];

		if( isset($res['rows']) && is_array($res['rows']) ){
			return $res['rows'];
		}

		return array();
	}

	/** Permet de créer un document dans le couchdb
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 *	@param $data : tableau des données sous forme de clé => valeur, attention si des champs avancés doivent être envoyés, il faut que ce soit la clé "fields"
	 *	@return object un objet :
	 *		- ok : true si l'élément à correctement été inseré
	 *		- id : id de l'élément créé
	 */
	public static function add($cls_id, $data){
		global $config;

		if( isset($data['_id']) ) {
			unset($data['_id']);
		}

		// ajout de data complémentaire
		$tmp_data = array();
		$tmp_data['tnt_id'] = (int)$config['tnt_id'];
		$tmp_data['cls_id'] = $cls_id;
		$tmp_data['created'] = time();
		$tmp_data['modified'] = time();
		$tmp_data['content'] = $data;

		return (array) self::exec('POST', '', $tmp_data );
	}

	/** Permet la mise à jour d'un document dans le couchdb, attention tout le "content" doit être renvoyé à chaque mise à jour
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 * 	@param int $id : Identifiant du document
	 *	@param $data : tableau des données sous forme de clé => valeur, attention si des champs avancés doivent être envoyés, il faut que ce soit la clé "fields"
	 *	@return object un objet :
	 *		- ok : true si l'élément à correctement été modifié
	 */
	public static function update($cls_id, $id, $data){
		global $config;

		// récupère l'obj avant pour avoir la révision
		$result = self::exec('GET', $id );
		if( isset($result['error']) ) return false;
		$rev = $result['_rev'];

		if( isset($data['_id']) ) {
			unset($data['_id']);
		}

		// ajout de data complémentaire
		$tmp_data = array();
		$tmp_data['tnt_id'] = (int)$config['tnt_id'];
		$tmp_data['cls_id'] = $cls_id;
		$tmp_data['created'] = $result['created'];
		$tmp_data['modified'] = time();
		$tmp_data['content'] = $data;

		$res = self::exec('PUT', $id.'?rev='.$rev, $tmp_data );
		if( !$res || isset($res['error']) ){
			return false;
		}

		return true;
	}

	/** Permet la suppression d'un document dans le couchdb, attention cela fait une supression virtuelle.
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 * 	@param int $id : Identifiant du document
	 *	@return object un objet :
	 *		- ok : true si l'élément à correctement été supprimé
	 */
	public static function delete($cls_id, $id){
		global $config;

		// récupère l'obj avant pour avoir la révision
		$result = self::exec('GET', $id);
		if (isset($result['error'])){
			return false;
		}

		$rev = $result['_rev'];

		$tmp_data = array();
		$tmp_data['tnt_id'] = $result['tnt_id'];
		$tmp_data['cls_id'] = $result['cls_id'];
		$tmp_data['created'] = $result['created'];
		$tmp_data['modified'] = time();
		$tmp_data['deleted'] = time(); // suppression virtuel
		$tmp_data['content'] = $result['content'];

		$res = self::exec('PUT', $id . '?rev=' . $rev, $tmp_data);

		if (!$res || isset($res['error'])) {
			return false;
		}

		return true;
	}

	/** Cette fonction permet de supprimer les données d'un tenant présent dans toutes les bases CouchDB.
	 * 	@param string $db Optionnel, permet de limiter le ménage à une seule base de données
	 * 	@param int $cls_id Optionnel, permet de limiter le ménage à une seule classe de documents
	 */
	public static function clear( $db='', $cls_id=0 ){
		global $config;

		{
			// Chargement de toutes les bases de données
			self::create();
			$exec_db = self::exec('GET', '_all_dbs');

			$ar_db = [];
			if( is_array($exec_db) ){
				foreach( $exec_db as $one_db ){
					if( strstr($one_db, getenv(ENVRIA_COUCHDB_DB_NAME_PREFIX). '_riashop_') ){
						if( trim($db) == '' || $db == $one_db ){
							$ar_db[] = $one_db;
						}
					}
				}
			}
		}

		foreach( $ar_db as $one_db ){
			self::create( $one_db );

			// Pagination des documents
			$page = 1; $max_page = 1000;
			$nb_by_page = 1000;
			$count_deleted = 0;

			// Récupère les documents selon la pagination
			// Tant qu'il y a des documents et autant que demandé par page, on récupèrera la page suivante
			while( true ){
				if( $page > $max_page ){
					break;
				}

				$get_url = '_all_docs?descending=false&include_docs=true&reduce=false&limit='.$nb_by_page;
				if( $page > 1 ){
					$get_url .= '&skip='.( (($page - 1) * $nb_by_page) - $count_deleted );
				}

				// Récupère les documents
				$results = self::exec( 'GET', $get_url );

				// Si aucun document n'est récupéré, on arrête ici la récupération
				if( !isset($results['rows']) || !is_array($results['rows']) ){
					break;
				}

				foreach( $results['rows'] as $row ){
					if( !isset($row['doc']['tnt_id']) || $row['doc']['tnt_id'] != $config['tnt_id'] ){
						continue;
					}

					if( $cls_id > 0 && (!isset($row['doc']['cls_id']) || $row['doc']['cls_id'] != $cls_id) ){
						continue;
					}

					// Révision actuel et identifiant du document
					$doc_id = $row['id'];
					$rev_id = $row['value']['rev'];

					// Supprime le document
					self::exec( 'DELETE', $doc_id.'?rev='.$rev_id );

					$count_deleted++;
				}

				// S'il existe moins de documents que la page demandée
				// Alors il s'agit de la dernière page et la récupération des documents s'arrête
				if( count($results['rows']) != $nb_by_page ){
					break;
				}

				$page++;
			}
		}
	}

	/** Cette fonction permet de formatter un tableau de paramètre key => values en tableau de param pret à envoyer au couchdb
	 *  @param array $params : Tableau des données possible de passer en paramètre
	 *      - startkey : Clé de début de la forme suivante "array(Key1,Key2,Key3...)"
	 *      - endkey : Clé de fin de la forme suivante "array(Key1,Key2,Key3...)"
	 *      - include_docs :  si true permet d'inclure tous le contenu des documents dans le retour
	 *      - descending : mode de trie de la liste, si false les données les plus anciennes seront en premier.
	 *  @return string une chaine de caractère formatter de la facon suivante : startkey=[Key2,Key2..]&includes_docs=true...
	 */
	private static function queryString($params=array()){

		$is_descending = isset($params['descending']) && $params['descending'];

		if( isset($params['group']) && $params['group']){
			$params['include_docs'] = false;
			$params['reduce'] = true;
		}


		$query_params = array();
		foreach($params as $k => $v){
			switch( $k ){
				case 'startkey' :
				case 'endkey' :


					if( !is_array($v) ){
						$v = array($v);
					}

					//traitement particulier pour ajouter 1 à la clé de fin
					// les recherches doivent être un intervalle on ajoute toujours +1 même si la clé c'est le tnt
					// le couchdb récupére les doc avec < endkey
					if($k == 'endkey' ){
						$last_value_on_key = $v[sizeof($v)-1];

						if( is_numeric($last_value_on_key) ){
							$last_value_on_key ++;
						}else if( isdateheure($last_value_on_key) ){
							$last_value_on_key = date('Y-m-d H:i:s', strtotime($last_value_on_key)+1);
						}

						$v[sizeof($v)-1] = $last_value_on_key;
					}

					// escaping
					foreach($v as $i => $vk){
						if( !is_numeric($vk) ){
							$v[$i] = '"'.str_replace('"','""',urlencode($vk)).'"';
						}
					}

					// dans le cas d'un ordre desc il faut inversé les start / end key
					$kv = $k;
					if( $is_descending ){
						if($k == 'startkey' ){
							$kv = 'endkey';
						}else if($k == 'endkey' ){
							$kv = 'startkey';
						}
					}

					$query_params[] = $kv.'=['.implode(',',$v).']';
					break;
				case 'reduce' :
				case 'include_docs' :
				case 'descending' :
				case 'group' :
				case 'inclusive_end' :
					$query_params[] = $k.'='.($v ? 'true':'false');
					break;
				case 'group_level' :
				case 'stale' :
				case 'limit' :
				case 'key' :
				case 'skip' :
					$query_params[] = $k.'='.urlencode($v);
					break;

			}
		}

		return implode('&', $query_params);
	}


	/** Cette fonction lance un clean sur tous les reste de précalcul de vue qui servent plus
	 *	Attention elle lance un thread sur le couchdb et retourne directement "ok", le traitement est fait en async donc.
	 */
	private static function cleanup(){
		return self::exec("POST", "_view_cleanup", false, false);
	}


	/** Cette fonction permet de supprimer définitivement un document.
	 * 	@param string $doc_id Obligatoire, identifiant d'un document
	 * 	@param string $rev_id Obligatoire, identifiant de la dernière révision du document
	 * 	@return bool Résultat de la suppression en cas de succès, false dans le cas contraire
	 */
	public function temp($doc_id, $rev_id ){
		if( trim($doc_id) == '' ){
			return false;
		}

		if( trim($rev_id) == '' ){
			return false;
		}

		return self::exec( 'DELETE', $doc_id.'?rev='.$rev_id );
	}

	/** Cette fonction permet de supprimer définitivement un document.
	 * 	@param string $doc_id Obligatoire, identifiant d'un document
	 * 	@param string $rev_id Facultatif, identifiant de la dernière révision du document
	 * 	@return bool Résultat de la suppression en cas de succès, false dans le cas contraire
	 */
	public function hardDelete($doc_id, $rev_id=null){
		if( trim($doc_id) == '' ){
			return false;
		}

		if( !$rev_id ){
			$result = self::exec('GET', $doc_id );
			if( isset($result['error']) ) return false;
			$rev_id = $result['_rev'];
		}

		return self::exec( 'DELETE', $doc_id.'?rev='.$rev_id );
	}

	/** Cette fonction permet l'éxécution de la requete sur le serveur couchdb
	 * 	@param $method Obligatoire, action à effectuer, prend les valeurs suivante : GET, PUT, POST, DELETE
	 * 	@param string $url Obligatoire, url à appeller ( sans le domaine ), en général c'est l'id du document et sa révision
	 * 	@param $data Facultatif, contenu complet du document.
	 *	@return string la réponse json du serveur
	 */
	private static function exec($method, $url, $data=false, $logerror=true){

		$ch = curl_init();

		if( trim(self::$_couchdb_conf['login']) == ''){
			$domain = self::$_couchdb_conf['protocol'] .'://'.self::$_couchdb_conf['domain'];
		}else{
			$domain = self::$_couchdb_conf['protocol'] .'://'.self::$_couchdb_conf['login'].':'.self::$_couchdb_conf['password'].'@'.self::$_couchdb_conf['domain'];
		}

		$curl_url = $domain;
		if( $url == '_all_dbs' ){
			$curl_url .= '/_all_dbs';
		}else{
			$curl_url .= '/'.self::$_couchdb_conf['db'];

			if( trim($url) != '' ){
				$curl_url .= '/'.$url;
			}
		}

		 //print 'curl_url => '.$curl_url.PHP_EOL;
		curl_setopt($ch, CURLOPT_URL,  $curl_url);
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);


		if( $data ){
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
		}

		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
			'Content-type: application/json',
			'Accept: */*'
		));

		$response = curl_exec($ch);

		if( !$response ){
			error_log(__FILE__.':'.__LINE__.' curl exec failed : '.$method.':'.$domain.'/'.self::$_couchdb_conf['db'].(trim($url) ? '/'.$url : "")." error=".curl_error($ch));
		}
		curl_close($ch);

		$res = json_decode($response, true);
		if( $logerror && isset($res['error']) ){
			error_log(__FILE__.':'.__LINE__.' '.print_r($res, true).' url='.$url. ' curl_url='. $curl_url.' db='.self::$_couchdb_conf['db']);
		}

		return $res;
	}
}

{ // Toutes la partie en dessous est dépréciée et ne sert qu'à rendre rétroactive la classe CouchDB
	/** Permet la récupération d'un document
	 * 	@deprecated Il faut utiliser la classe.
	 * 	@param int $cls_id : Identifiant de la classe souhaité
	 * 	@param int $id : Identifiant du document
	 * 	@param $params : tableau de paramètre complémentaire
	 * 	@return object un objet avec tous le contenu du document si celui-ci existe, sinon un objet avec avec une "error"
	 */
	function couchdb_get($cls_id, $id, $params=array()){
		return CouchDB::create()->get($cls_id, $id, $params);
	}

	/** Permet de créer un document dans le couchdb
	 * 	@deprecated Il faut utiliser la classe.
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 *	@param $data : tableau des données sous forme de clé => valeur, attention si des champs avancés doivent être envoyés, il faut que ce soit la clé "fields"
 	 *	@return object un objet :
 	 *		- ok : true si l'élément à correctement été inseré
 	 *		- id : id de l'élément créé
 	 */
	function couchdb_add($cls_id, $data){
		return CouchDB::create()->add($cls_id, $data);
	}

	/** Permet la mise à jour d'un document dans le couchdb, attention tout le "content" doit être renvoyé à chaque mise à jour
	 * 	@deprecated Il faut utiliser la classe.
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 * 	@param int $id : Identifiant du document
	 *	@param $data : tableau des données sous forme de clé => valeur, attention si des champs avancés doivent être envoyés, il faut que ce soit la clé "fields"
 	 *	@return object un objet :
 	 *		- ok : true si l'élément à correctement été modifié
 	 */
	function couchdb_upd($cls_id, $id, $data){
		return CouchDB::create()->update($cls_id, $id, $data);
	}

	/** Permet la suppression d'un document dans le couchdb, attention cela fait une supression virtuelle.
	 * 	@deprecated Il faut utiliser la classe.
	 * 	@param int $cls_id : Identifiant de la classe souhaitée
	 * 	@param int $id : Identifiant du document
	 *	@return object un objet :
 	 *		- ok : true si l'élément à correctement été supprimé
 	 */
	function couchdb_del($cls_id, $id){
		return CouchDB::create()->delete($cls_id, $id);
	}
}

/// @}

/// \endcond
