<?php
/** 
 * \defgroup api-wishlist Wishlist
 * \ingroup crm
 * @{		
*/

// \cond onlyria
/**
 * Ajoute ou met à jour une wishlist
 * @param raw_data données d'une wishlist
 *
 * @return l'id de la wishlist si réussi, une exception sinon
*/
function update_wishlists($raw_data){
	global $method, $config, $is_sync;


	if( !isset($raw_data['id']) || !is_numeric($raw_data['id'])
		|| !isset($raw_data['usr_id']) || !is_numeric($raw_data['usr_id'])  ){
		throw new Exception('Paramètres invalide');
	}

	if( !isset($raw_data['date_created']) || !$raw_data['date_created'] ){
		$raw_data['date_created'] = false;
	}

	if( $method == "add" ){

		$gw_id = gu_wishlists_add( $raw_data['usr_id'], $raw_data['name'], null, $raw_data['publish'], null, $raw_data['is_system'] );
		if( !$gw_id ){
			throw new Exception('Erreur lors de la création de la wishlist');
		}

	}else{

		if( !isset($raw_data['id']) ){
			throw new Exception('Paramètres invalide');
		}

		$gw_id = $raw_data['id'];

		if( !gu_wishlists_update( $gw_id, $raw_data['name'], $raw_data['desc'], $raw_data['publish'] ) ){
			throw new Exception('Erreur lors de la modification de la wishlist');
		}
	}

	$error = false;

	// vide tous les produits
	$wobj = gu_wishlists_products_get(false, $gw_id);

	if( $wobj && ria_mysql_num_rows($wobj) ){
		while( $obj = ria_mysql_fetch_assoc($wobj) ){
			gu_wishlists_products_del( $gw_id, $obj['prd_id']);
		}
	}

	// ajout des produits liés
	if( isset($raw_data['products']) && is_array($raw_data['products']) ){
		foreach( $raw_data['products'] as $products ){
			if( !isset($products['prd_id']) ){
				$error = true;
				break;
			}

			gu_wishlists_products_add(  $products['prd_id'], $gw_id, $products['desc'] );
		}
	}

	if( $error && $method == "add" ){
		gu_wishlists_del($gw_id);
	}
	if( $error ){
		throw new Exception('Erreur lors de l\'ajout des produits liés');
	}

	return $gw_id;

}
// \endcond

switch ($method) {
	/** @{@}
 	 * @{
	 * \page api-wishlists-index-get Chargement
	 *
	 *	Cette fonction récupére une wishlists ou plusieurs wishlists
	 *	 
	 *	\code
	 *		GET /wishlists/
	 *	\endcode
	 *
	 *	@param int $id Facultatif, identifiant de la liste de favori
	 *	@param int $usr_id Facultatif, identifiant d'un utilisateur, 0 = liste global
	 *	@param bool $publish Facultatif, retourne les listes publiés ou non
	 *	@param $website Facultatif, permet de filtrer sur un website si null on prend le site actuel si false tous les sites
	 *	@param $get_user Facultatif, par défaut les informations sur le compte client ne sont pas retourné, mettre True pour que ce soit le cas (Nom, Prénom et Société)
	 *	@param $is_system Facultatif, indique s'il faut retourner toutes les whishlists (null, valeur par défaut) ou bien seulement celles qui sont système (true) ou client (false)
	 *	
	 *	@return array Retourne un tableau de donnée avec si le paramètre "id" est donnée 1 seul élement contenant l'ensemble du détail pour une wishlist, si "id" n'est pas donnée retourne une liste contenant pour chaque entrée tout le détail de la wishlist
	 *
	 *	@}
	*/
	case 'get':

		$id = isset($_REQUEST['id']) ? $_REQUEST['id'] : 0;
		$usr_id = isset($_REQUEST['usr_id']) ? $_REQUEST['usr_id'] : 0;

		$wishlists = gu_wishlists_get($id, $usr_id, null, false, false, null);

		if( $wishlists && ria_mysql_num_rows($wishlists) ) {
			$result = true;
			$array = array();
			while ($wl = ria_mysql_fetch_assoc($wishlists)) {
				$array[] = dev_devices_get_object_simplified(CLS_WISHLISTS, array($wl['id']));
			}
			if( $id ){
				$content = $array[0];
			}else{
				$content = $array;
			}
		}
		break;

	case 'add':
	/** @{@}
 	 *	@{
	 *	\page api-wishlists-index-add Ajout
	 *
	 * 	cette fonction ajoute une wishlist
	 *
	 *	 \code
	 *		POST /wishlists/
	 *	 \endcode
	 *	
	 *	 @param int $id Obligatoire, identifiant de la wishlist
	 *	 @param int $usr_id Obligatoire, identifiant de l'utilisateur
	 *	 @param bool $publish Facultatif, wishlist publiée ou non
	 *	 @param $url_alias Facultatif, url de la wishlist
	 *	 @param $is_system Facultatif, toutes les whishlists (null, valeur par défaut) ou bien seulement celles qui sont système (true) ou client (false)
	 *	 @param $products ? Liste des produits a ajouter à la wishlist
	 *	
	 * 	 @return Retourne l'identifiant de la wishlist
	 *	@}
	*/
		$gw_id = update_wishlists($_REQUEST);

		// si une erreur à eu lieu on supprime la wishlist
		if( $gw_id && is_numeric($gw_id) ){
			$result = true;
			$content = array('id' => $gw_id);
		}

		break;

	case 'upd':
	/** @{@}
 	 *	@{
	 *	\page api-wishlists-index-upd Mise à jour 
	 *	cette fonction modifie une wishlist
	 *	 \code
	 *		PUT /wishlists/
	 *	 \endcode
	 *	
	 *	 @param int $id Obligatoire, identifiant de la wishlist
	 *	 @param int $usr_id Obligatoire, identifiant de l'utilisateur
	 *	 @param bool $publish Facultatif, wishlist publiée ou non
	 * 	 @param $url_alias Facultatif, url de la wishlist
	 *	 @param $is_system Facultatif, toutes les whishlists (null, valeur par défaut) ou bien seulement celles qui sont système (true) ou client (false)
	 *	 @param $products ? Liste des produits a ajouter à la wishlist
	 *	
	 *	 @return true si la modification réussie, false dans le cas contraire
	 *	@}
	*/
		if( update_wishlists($_REQUEST) ){
			$result = true;
		}

		break;

	case 'del':
	/** @{@}
 	 *	@{
	 *	\page api-wishlists-index-del Suppression
	 *	cette fonction supprime une wishlist
	 *	 \code
	 *		DELETE /wishlists/
	 *	 \endcode
	 *	
	 *	 @param int $id Obligatoire, identifiant de la wishlist
	 *	
	 *	 @return true si la wishlist est supprimée, false dans le cas contraire
	 *	@}
	*/
		if( !isset($_REQUEST['id']) || !is_numeric($_REQUEST['id']) ){
			throw new Exception('Paramètres invalide');
		}

		if( !gu_wishlists_del($_REQUEST['id']) ){
			throw new Exception("Erreur lors de la suppression de la wishlist.");
		}

		$result = true;

		break;
}
///@}