var current_ajax_request = false;
var liste_plage = [];
$(document).ready(function() {
	// Gestion du selecteur de fermeture exceptionnelle
	$('#closingpicker .selectorview').click(function(){
		if($('#closingpicker .selector').css('display')=='none'){
			$('#closingpicker .selector').show();
		}else{
			$('#closingpicker .selector').hide();
		}
	});
	$("#closingpicker .selector a").click(function(){
		const periode = $(this).attr('name');
		const p = periode.substring(periode.indexOf('-')+1, periode.length);
		const str = $('#str_id').val();
		window.location.href = '/admin/config/livraison/stores/edit.php?str=' + str + '&tab=opening&page=1&period='+p+'#form-closing';
	});

	// Permet la réorganisation des services
	if( typeof $('.srv-table') != 'undefined' && $('.srv-table').length ){
		riaSortable.create({
			'table'	:	$('.srv-table'),
			'url'	:	'/admin/ajax/config/ajax-dlv-services-position-update.php'
		});
	}

	var url_params = {
		c : $('#country').val() || 'FR',
		region : $('#county').val() || '',
		pub : $('#publish').val() || '',
		seg : $('#seg').val() || '',
		sales_types : $('#sales_types').val() || '',
	};

	function realoadPage(){
		window.location.href = '/admin/config/livraison/stores/index.php?'+$.param(url_params);
	}

	if( typeof $('#riacountriespicker') != 'undefined' && $('#riacountriespicker').length ){
		$('#riacountriespicker .selector a').click(function(){
			var country = $(this).attr('name');
			country = country.substring(country.indexOf('-')+1, country.length);
			$('#country').val( country );
			url_params.c = country;
			realoadPage();
		});
	}

	if( typeof $('#riacountiespicker') != 'undefined' && $('#riacountiespicker').length ){
		$('#riacountiespicker .selector a').click(function(){
			var county = $(this).attr('name');
			county = county.substring(county.indexOf('-')+1, county.length);
			$('#county').val( county );
			url_params.region = county;
			realoadPage();
		});
	}

	if( typeof $('#riapublishpicker') != 'undefined' && $('#riapublishpicker').length ){
		$('#riapublishpicker .selector a').click(function(){
			var publish = $(this).attr('name');
			publish = publish.substring(publish.indexOf('-')+1, publish.length);
			$('#publish').val( publish );
			url_params.pub = publish;
			realoadPage();
		});
	}

	if( typeof $('#riasegmentpicker') != 'undefined' && $('#riasegmentpicker').length ){
		$('#riasegmentpicker .selector a').click(function(){
			var segment = $(this).attr('name');
			segment = segment.substring(segment.indexOf('-')+1, segment.length);
			$('#seg').val( segment );
			url_params.seg = segment;
			realoadPage();
		});
	}

	if( typeof $('#riatypesalespicker') != 'undefined' && $('#riatypesalespicker').length ){
		$('#riatypesalespicker .selector a').click(function(){
			var sales_types = $(this).attr('name');
			sales_types = sales_types.substring( sales_types.indexOf('-')+1, sales_types.length );
			$('#sales_types').val( sales_types );
			url_params.sales_types = sales_types;
			realoadPage();
		});
	}

	if( typeof $('#tag_desc') != 'undefined' && $('#tag_desc').length ){
		$('#tag_desc').riametas({ padding: true, type : "desc" });
		$('#tag_title').riametas({ padding: true, type : "title" });
		$('#tag_title_ref').riametas({ type : "title" });
		$('#tag_desc_ref').riametas({ type : "desc" });
	}

	$(document).on('change', '.selectZone', ajaxGetOptions);


	$("#addZone").click(function(){
		$("#rowZone").val(parseInt($("#rowZone").val())+1);
		var row = $("#rowZone").val();
		var clone = $(this).parent().find('select').clone(true,true);
		clone.show();
		clone.attr('data-row', row);
		clone.attr('name', 'zone[1]['+row+']');
		$(this).parent().parent().parent().find('#tableZone').append("<tr><td id='geographicalZone"+row+"'></td></tr>");
		clone.appendTo($("#geographicalZone"+row));
		$("<a class='del' id='buttonDell'>Supprimer</a></td></tr>").appendTo($("#geographicalZone"+row));
	});


	$("#addPrice").click(function(){
		$("#rowPrice").val(parseInt($("#rowPrice").val())+1);
		var row = $("#rowPrice").val();
		var clone = $(this).parent().find('div').clone(true,true);
		clone.css('display', 'inline');

		//incremente le nom/id des input
		clone.find("#value_min").attr('name','value_min['+ row +']').attr('id','value_min['+ row +']');
		clone.find("#tarif").attr('name','tarif['+ row +']').attr('id','tarif['+ row +']');
		clone.find("#prorata").attr('name','prorata['+ row +']').attr('id','prorata['+ row +']');
		clone.find("#cumule").attr('name','cumule['+ row +']').attr('id','cumule['+ row +']');
		clone.find("#slice").attr('name','slice['+ row +']').attr('id','slice['+ row +']');
		clone.find("#sl_price").attr('name','sl_price['+ row +']').attr('id','sl_price['+ row +']');

		//change l'attribut for des labels
		clone.find("#labelValueMiN").attr('for','value_min['+ row +']');
		clone.find("#labelTarif").attr('for','tarif['+ row +']');
		clone.find("#labelProrata").attr('for','prorata['+ row +']');
		clone.find("#labelCumule").attr('for','cumule['+ row +']');
		clone.find("#labelSlice").attr('for','slice['+ row +']');
		clone.find("#labelSlPrice").attr('for','sl_price['+ row +']');

		$(this).parent().parent().parent().find("#tablePrice").append("<tr><td id='tarif"+row+"' colspan='2'></td></tr>");
		clone.appendTo($("#tarif"+row));
	});

	$(document)
		.on('click', '.riapicker .selectorview', function () {
			var selector = $(this).parents('.riapicker').find('.selector');

			if (selector.css('display') == 'none') {
				$('.selector').hide();
				selector.show();
			} else {
				selector.hide();
			}
		})
		.on('click', '.riapicker .selector a', function(event){
			var $elt = $(event.currentTarget);
			var $form = $elt.parents('form:eq(0)');

			$form.find('input[type="hidden"].wst-id')
				.val($elt.attr('name').replace(/w-(\d+)/, '$1'));

			$form.submit();
		});

	$(document).on('click', '.chkProrata', function(){
		if($(this).is(':checked')){
			$(".prorata",this.parentNode.parentNode.parentNode).show();
		}else{
			$(".prorata",this.parentNode.parentNode.parentNode).hide();
		}
	});


	$(document).on('click', '#buttonDell', function(){
		this.parentNode.parentNode.remove();
	});

	$(document).on('click', '#buttonDellPrice', function(){
    	this.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.remove();
	});

	var type={
		"qte" : livraisonQte,
		"weight" : livraisonWeight,
		"weight_net" : livraisonWeightNet,
		"HT" : livraisonHT,
		"TTC" : livraisonTTC
	};

	$('#type').click(function(){
		$(document).find(".spanType").each(function(){
			$(".spanType").html(type[$('#type :selected').val()]);
		})
	});

	$(document).find(".spanType").each(function(){
				$(".spanType").html(type[$("#type :selected").val()]);
			})
	// Ouverture de la popup pour l'jout de plage horaire
	$('#add-plage').click(function() {
		var store = $('#str_id').val();
		displayPopup( lirvaisonGestionPlageHoraire, '', '/admin/config/livraison/stores/popup-plage-horaire.php?store=' + store );
	});

	// Gestion de la popup pour le modification de plage horaire
	$('.edit-plage').click(function() {
		var id = $(this).data('id');
		var store = $('#str_id').val();
		displayPopup( 'Ajouter une plage horaire', '', '/admin/config/livraison/stores/popup-plage-horaire.php?store=' + store + '&plage=' + id);
	});

	// Gestion de la sélection et déselection de plage
	$('.dlv_hour_plage').click(function() {
		var id = $(this).data('id');

		if(liste_plage.includes(id)){
			$(this).removeClass('dlv_hour_plage_select');
			var index = liste_plage.indexOf(id);
  			liste_plage.splice(index, 1);
		}else{
			$(this).addClass('dlv_hour_plage_select');
			liste_plage.push(id);
		}
	});

	// Suppression de plage horaire
	$('#del-plage').click(function() {
		$.ajax({
			url: '/admin/ajax/config/ajax-dlv-plage.php',
			type: 'POST',
			data: {del_plage: 1, del_liste_plage: liste_plage},
		})
		.done(function(data) {
			data = JSON.parse(data);
			if(data['Erreur'] == 0){
				location.reload();
			}
		});
	});

	$('#clickandcollect').click(function () {
		$('#tr_timeslots_enabled').toggle();
	});
});

$('.check-all').on('click', function(){
	$(".sale-type").find('input[type=checkbox]').attr('checked','checked');
	return false;
});

$('.uncheck-all').on('click', function(){
	$(".sale-type").find('input[type=checkbox]').removeAttr('checked');
	return false;
});

$(document).delegate('a[name=selectstore]', 'click', function(){
	$('.error').remove();
	var page = 1;
	if( $(this).attr('data-page') ) page = $(this).attr('data-page');

	var complURL = '';
	if( $(this).attr('data-url') ) complURL = '&' + $(this).attr('data-url');

	$.get( '/admin/config/livraison/stores/popup-stores.php?p='+page+complURL, function(html){
		$('#popup-content').html(html);
	});
});

$(document).delegate('#selectstore', 'click', function(){
	$('.error').remove();
	if( !$('input[type=radio]:checked, input[type=checkbox]:checked').length ){
		$('.pop-form-search').before( '<div class="error">' + livraisonSelectMagasin + '</div>' );
		return false;
	}

	var id = $('input[type=radio]:checked').val();
	var name = $('input[type=radio]:checked').attr('data-name');
 	window.parent.parent_select_store( id, name );
	window.parent.hidePopup();
});

$('#employees').delegate('.employee .employee-image', 'click', function () {
	var $inPut = $(this).parent().find(".checkbox");
	$('#check-all').removeAttr('checked');
	if( !$inPut.is(':checked') ){
		$inPut.attr('checked', 'checked');
		$inPut.closest('.employee').addClass('del-selected');
	}else {
		$inPut.removeAttr('checked');
		$inPut.closest('.employee').removeClass('del-selected');
	}
});


function strSwitchTablePage( page, pages, mdl, fld, sort, dir ){
	// Place le scroll en haut à gauche de l'écran
	swichPageLoad(4);

	// Requête AJAX pour le changement de page
	$.ajax({
		type: "POST",
		url: '/admin/ajax/stores/json-stores.php',
		data: 'p='+page+'&mdl='+mdl+'&fld='+fld+'&limit=25&pub=' + $('#publish').val() + '&c=' + $('#country').val() + '&region=' + $('#county').val() +'&sort='+sort+'&dir='+dir+'&sales_types='+$('#sales_types').val(),
		dataType: 'json',
		async:true,
		success: function(msg){
			var html = '';

			// Créé le contenu du tableau
			for( var i=0 ; i<msg.length ; i++ ){
				html += '	<tr>';
				html += '		<td headers="select"><input type="checkbox" value="'+msg[i].id+'" name="str[]" class="checkbox"></td>';
				html += '		<td headers="id">' + msg[i].id + '</td>';
				html += '		<td headers="name">'+viewStrIsSync(msg[i].is_sync)+' <a href="edit.php?str='+msg[i].id+'&amp;page='+page+'">'+htmlspecialchars(msg[i].name)+'</a></td>';
				html += '		<td headers="address"><address>'+htmlspecialchars(msg[i].address1)+(msg[i].address2 ? '<br>'+htmlspecialchars(msg[i].address2) : '')+'<br>'+htmlspecialchars(msg[i].zipcode)+' '+htmlspecialchars(msg[i].city)+'</address></td>';
				html += '		<td>'+ (msg[i].manager ? htmlspecialchars(msg[i].manager) +'<br>' : '')+(msg[i].phone ? htmlspecialchars(msg[i].phone)+'<br>' : '') +(msg[i].fax ? htmlspecialchars(msg[i].fax)+'<br>' : '') + '<a href="mailto:'+htmlspecialchars(msg[i].email)+'">'+htmlspecialchars(msg[i].email)+'</a></td>';
				html += '	</tr>';
			}

			// Affiche le contenu
			$("#site-content table tbody").html(html);

			// Affichage de la pagination
			$("#pagination").html(switchPage(page, pages, 3, 3, 3, 2, 'strSwitchTablePage', ', '+mdl+', '+fld + ', \''+sort+'\', \''+dir+'\'', 'index.php', ( mdl ? '&mdl='+mdl : fld ? '&fld='+fld : '' )));

			// Modification des headers
			$('#list-stores').find('thead th').removeClass('headerSortDown').removeClass('headerSortUp');
			switch( sort ){
				case 'id' :
					$("#ref").html(
						'<a onclick="return strSwitchTablePage(1, '+pages+', '+mdl+', '+fld+', \'id\', \'' + (dir === 'asc' ? 'desc' : 'asc') + '\')" href="index.php?sort=id&dir=' + (dir === 'asc' ? 'desc' : 'asc') + '&mdl=' + mdl + '&fld=' + fld + '">Référence</a>'
					).addClass(
						dir=='asc' ? 'headerSortUp' : 'headerSortDown'
					);
					break;
				case 'name' :
					$("#name").html(
						'<a onclick="return strSwitchTablePage(1, '+pages+', '+mdl+', '+fld+', \'name\', \'' + (dir === 'asc' ? 'desc' : 'asc') + '\')" href="index.php?sort=name&dir=' + (dir === 'asc' ? 'desc' : 'asc') + '&mdl=' + mdl + '&fld=' + fld + '">Nom du magasin</a>'
					).addClass(
						dir=='asc' ? 'headerSortUp' : 'headerSortDown'
					);
					break;
				case 'address' :
					$("#address").html(
						'<a onclick="return strSwitchTablePage(1, '+pages+', '+mdl+', '+fld+', \'address\', \'' + (dir === 'asc' ? 'desc' : 'asc') + '\')" href="index.php?sort=address&dir=' + (dir === 'asc' ? 'desc' : 'asc') + '&mdl=' + mdl + '&fld=' + fld + '">Adresse</a>'
					).addClass(
						dir=='asc' ? 'headerSortUp' : 'headerSortDown'
					);
					break;
			}
		},
		erro: function(){
			return true;
		}
	});
	return false;
}

// Vérifie le formulaire d'ajout/modification de zone
function zonesValidForm(frm){
	if( !trim(frm.name.value) ){
		alert(livraisonAlertNomZoneLivraison);
		frm.name.focus();
		return false;
	}
}
function zoneAdd(){
	window.location.href = 'edit.php';
	return false;
}
function zoneCancelEdit(){
	window.location.href = 'index.php';
	return false;
}
function zoneConfirmDel(){
	return window.confirm(livraisonConfirmSuppressionZoneLivraison);
}
function depotConfirmDel(){
	return window.confirm(livraisonConfirmSuppressionDepotStock);
}
function zoneConfirmDelList(){
	return window.confirm(livraisonConfirmSuppressionZonesLivraisons);
}

// Fonctions de gestion des services
function srvValidForm(input){
	var frm = input.parents('form');

	if( !trim(frm.find('#name').val()) ){
		alert(livraisonAlertNomServiceLivraison);
		frm.find('#name').focus();
		return false;
	}
	if( frm.find('#alert-msg').val().indexOf('%nom%')==-1 ){
		return window.confirm(livraisonConfirmotificationEditionLivraison);
	}
}
function srvAdd(){
	window.location.href = 'edit.php';
	return false;
}
function srvCancelEdit(){
	window.location.href = 'index.php';
	return false;
}
function srvConfirmDel(){
	return window.confirm(livraisonConfirmSuppressionServiceLivraison);
}
function srvConfirmDelList(){
	return window.confirm(livraisonConfirmSuppressionServicesLivraisons);
}

// Fonctions de gestion des colis
function pkgValidForm(frm){

	frm.length.value = trim(frm.length.value);
	if( !frm.length.value ){
		alert(livraisonAlertLongeurColis);
		frm.length.focus();
		return false;
	}
	if( !validInt(frm.length.value) ){
		alert(livraisonAlertLongeurColisInvalide);
		frm.length.focus();
		return false;
	}
	if( parseInt(frm.length.value)<=0 ){
		alert(livraisonAlertLongeurColisSupZero);
		frm.length.focus();
		return false;
	}

	frm.width.value = trim(frm.width.value);
	if( !frm.width.value ){
		alert(livraisonAlertLargeurColis);
		frm.width.focus();
		return false;
	}
	if( !validInt(frm.width.value) ){
		alert(livraisonAlertLargeurColisInvalide);
		frm.width.focus();
		return false;
	}
	if( parseInt(frm.width.value)<=0 ){
		alert(livraisonAlertLargeurColisSupZero);
		frm.width.focus();
		return false;
	}

	frm.height.value = trim(frm.height.value);
	if( !frm.height.value ){
		alert(livraisonAlertHauteurColis);
		frm.height.focus();
		return false;
	}
	if( !validInt(frm.height.value) ){
		alert(livraisonAlertHauteurColisInvalide);
		frm.height.focus();
		return false;
	}
	if( parseInt(frm.height.value)<=0 ){
		alert(livraisonAlertHauteurColisSupZero);
		frm.height.focus();
		return false;
	}

	frm.elements['price-ht'].value = trim(frm.elements['price-ht'].value);
	if( frm.elements['price-ht'].value ){
		if( !validFloat(frm.elements['price-ht'].value) ){
			alert(livraisonAlertPrixColisInvalide);
			frm.elements['price-ht'].focus();
			return false;
		}
		if( parseFloat(frm.elements['price-ht'].value)<=0 ){
			alert(livraisonAlertPrixColisSupZero);
			frm.elements['price-ht'].focus();
			return false;
		}
	}
}
function pkgAdd(){
	window.location.href = 'edit.php';
	return false;
}
function pkgCancelEdit(){
	window.location.href = 'index.php';
	return false;
}
function pkgConfirmDel(){
	return window.confirm(livraisonConfirmSuppressionColis);
}
function pkgConfirmDelList(){
	return window.confirm(livraisonConfirmSupressionColisMulti);
}

// Fonctions de gestion des magasins
function strValidForm(frm){
	if( frm.name ){
		if( !trim(frm.name.value) ){
			alert(livraisonAlertNomMagasin);
			frm.name.focus();
			return false;
		}
	}else if( frm.image ){
		if( trim(frm.image.value) ){
			if( $('input[name="types[]"]:checked').length==0 ){
				alert(livraisonAlertCategorieImage);
				return false;
			}
		}
		if( frm.elements['del-img[]'] ){
			for( var i=0; i<frm.elements['del-img[]'].length; i++ ){
				if( frm.elements['del-img[]'][i].checked ){
					return window.confirm(livraisonConfirmSuppressionImageMagasin);
				}
			}
		}
	}
}

function editSectors(id){
	var url = 'popup-sector-edit.php';
	url += '?id='+id;

	displayPopup(livraisonDisplayPopupModifierSecteur, '', url);

}

function refreshSectors(sectorList){
	$("select#sector option").each(function() {
		$(this).remove();
	});
	$('select#sector').append($('<option>', {
		value:0,
		text: LivraisonAucunSecteur
	}));
	for(var i =0;i < sectorList.length;i++){
		$('select#sector').append($('<option>', {
			value:sectorList[i].id,
			text: sectorList[i].name
		}));
	}
}

function strAdd(){
	window.location.href = 'edit.php';
	return false;
}
function strCancelEdit(){
	window.location.href = 'index.php';
	return false;
}
function strConfirmDelList(){
	return window.confirm(livraisonConfirmSuppressionMagasins);
}
function strConfirmDel(){
	return window.confirm(livraisonConfirmSuppressionMagasin);
}
function supprimerimage(img, cat, website) {
	$. ajax({
		type: "POST",
		url: '/admin/config/livraison/stores/delete_img.php',
		data: 'img='+img+'&str='+cat+'&wst='+website,
		async: false,
		success: function(msg){
			eval(msg);
			if(reponse){
				$("#img"+img).fadeOut();
			}
			else {
				alert(livraisonEditionMagasinErreur);
			}
		}
	});
	$(".listes").slideUp();
}
function checkAllClick(inPut){
	$("#site-content table tbody input[type=checkbox]").each(function(){
		if( $(inPut).is(':checked') )
			$(this).attr('checked', 'checked');
		else
			$(this).removeAttr('checked');
	});
}



var lastDay = '';
var lastHr = -1;
var check = false,
	action = false;
var ajaxTimeout = '';

/**
 Cette fonction permet de gérer la sélection des heures pendant lesquelles les expéditions sont effectuées.
 */
function selectedHourly(day, hourly){
    if( ajaxTimeout !== '' ){
        clearTimeout(ajaxTimeout);
    }
	// Gestoin d'une heure en particulier
	if( $("#hr-"+day+"-"+hourly).parent().attr('class')=='hr-active' ){
		$("#hr-"+day+"-"+hourly).parent().attr('class', 'hr-inactive');
		$("#hr-"+day+"-"+hourly).removeAttr('checked');
		check = false;
	} else {
		$("#hr-"+day+"-"+hourly).parent().attr('class', 'hr-active');
		$("#hr-"+day+"-"+hourly).attr('checked', 'checked');
		check = true;
	}

	// Gestion d'une période d'horaires
	if( lastDay==day && lastHr>=0 ){
		if( check && $("#hr-"+day+"-"+lastHr).parent().attr('class')=='hr-active' )
			action = true;
		else if( !check && $("#hr-"+day+"-"+lastHr).parent().attr('class')=='hr-inactive' )
			action = true;

		if(	action ){
			if( lastHr>hourly ){
				// Si la première heure est supérieur à la seconde, on sélection en arrière
				for( i=lastHr ; i>=hourly ; i-- ){
					check ? $("#hr-"+day+"-"+i).parent().attr('class', 'hr-active') : $("#hr-"+day+"-"+i).parent().attr('class', 'hr-inactive');
					check ? $("#hr-"+day+"-"+i).attr('checked', 'checked') : $("#hr-"+day+"-"+i).removeAttr('checked');
				}
			} else {
				// Si la première heure est inférieure à la seconde, on sélection en avant
				for( i=lastHr ; i<=hourly ; i++ ){
					check ? $("#hr-"+day+"-"+i).parent().attr('class', 'hr-active') : $("#hr-"+day+"-"+i).parent().attr('class', 'hr-inactive');
					check ? $("#hr-"+day+"-"+i).attr('checked', 'checked') : $("#hr-"+day+"-"+i).removeAttr('checked');
				}
			}
		}

		// Réinitialise les variables d'historique
		lastDay = '';
		lastHr = -1;
		check = action = false;
	} else {

		// Initialise les variables d'historique
		lastDay = day;
		lastHr = hourly;


	}

	// Gestion du libellé des périodes
	var period = '';
	var start = 0,
		end = 0,
		count = 0;
	var first = true;
	for( var i=0; i<25 ; i++ ){
		if( $("#hr-"+day+'-'+i).parent().attr('class')=='hr-active' ){
			if( first ){
				start = i;
				first = false;
			}
			end = i;
		} else {
			if( $("#hr-"+day+'-'+(i-1)).parent().attr('class')=='hr-active' && ( $("#hr-"+day+'-'+i).parent().attr('class')=='hr-inactive' || i==24 ) ){
				count++;
				// Mise en forme de l'heure
				start = start<10 ? '0'+start+':00' : start+':00';
				end = (end+1)<10 ? '0'+(end+1)+':00' : (end+1)+':00';

				// Création du libellé des périodes
				if( start!=end )
					period += start+' à '+end+' ';
				else{
					end = (start+1)<10 ? '0'+(start+1)+':00' : (start+1)+':00';
					period += start+' à '+end+' ';
				}
				if( count==2 ){
					period += '<br />';
					count = 0;
				}
			}

			first = true;
		}
	}
    var e = event.target;
    ajaxOpeningSaving(e, day);

	// Affiche le libellé des périodes
	period = period!='' ? period : livraisonFerme;
	period = start=='00:00' && end=='24:00' ? livraisonTouteJournee : period;
	$("#period-"+day).html(period);
}

/**
 *	Cette fonction permet de réinitialiser les heures à 0 pendant lesquelles les expéditions ont lieu.
 */
function reset(day) {
    if( ajaxTimeout !== '' ){
        clearTimeout(ajaxTimeout);
    }
	// Réinitialise la colonne période
	$("#period-" + day).html(livraisonFerme);
	for (var i = 0; i < 24; i++) {
		$("#hr-" + day + "-" + i).parent().attr('class', 'hr-inactive');
		$("#hr-" + day + "-" + i).removeAttr('checked');
	}

    ajaxOpeningSaving( event.target, day );
	// Réinitialise les variables d'historique
	lastDay = '';
	lastHr = -1;
	lastcheck = false;
}

function ajaxOpeningSaving(e,day){
    var inputs = $(e).parent().parent().find('input:checked');
    var params = getParamsInUrl(window.location.search.replace('?',''));
    var url = 'str='+params.str+'&day='+day;
    var data = { 'hr':{} };
    for(var i =0; i<inputs.length; i++){
        data.hr[i] = inputs[i].value;
    }

    $.ajax({
        type: 'POST',
        url: '/admin/ajax/config/ajax-dlv-opening.php?' + url,
        dataType: 'JSON',
        async: true,
        data: data,
        success: function(res){
            ajaxTimeout = setTimeout(function(){
                if($('.notice').length){
                    $('.notice').remove();
                }
                var successDiv = $('<div><div>').addClass('success notice').html(res.success);
                successDiv.insertBefore('#form_store');
            },1000);
        },
        error: function(res){
            if($('.notice').length === 0 ){
                $('.notice').remove();
            }
            var errorDiv = $('<div><div>').addClass('error notice').html(res);
            errorDiv.insertBefore('#form_store');
        }
    })
}


var ajaxGetOptions = function(){

	var id = $(this).attr('id');
	var regex = /-(.+)/;
	id = parseInt(regex.exec(id)[1])+1;
	var row = $(this).attr('data-row');
	var name = "zone["+id+"]["+row+"]";

	$(this).parent().find('#zone-'+id).remove();
	$(this).parent().find('#zone-'+(id+1)).remove();
	$(this).parent().find('#zone-'+(id+2)).remove();
	$(this).parent().find('#zone-'+(id+3)).remove();
	$(this).parent().find('#zone-'+(id+4)).remove();

	if($(this).val()!=0){
		var data = "id="+$(this).val();

		$.ajax({
			url: '/admin/ajax/config/ajax-livraison.php',
			data: data,
			method: 'get',
			dataType: 'json',
			success: function(data){
				var region = '<select id="zone-'+id+'" name='+ name +' class="zone selectZone" data-row='+row+'><option value=0>'+data.type+'</option>';
				var first = true;
				$.each(data.zone, function(k,option){
					if(first){
						first = false;

					}
					region = region + "<option value="+k+">"+ option+"</option>";
				});
				region = region + "</select>";
				$(region).insertBefore($("#geographicalZone"+row+" #buttonDell"));
			},
			error:function(){
			}
		});
	}
}

if (typeof $('.clockpicker').clockpicker !== "undefined") {
	$('.clockpicker').clockpicker();
}

/**
	Cette fonction permet de mettre en place le formulaire d'ajout d'une fermeture exceptionnelle
*/
function closing_add(){
	if( $("#new-closing").length==1 ){

		closing_del(0);
	}

	$("#none-closing").hide();
	var html = '';

	// Création du formulaire d'ajout
	html += '<tr id="new-closing"><td headers="clg-name" class="td-info"><input type="text" name="name-period-0" id="name-period-0" value="" /></td>';
	html += '<td headers="clg-start" class="td-date"><input class="datepickerexceptionnelle" type="text" name="start-period-0" id="start-period-0" value="" autocomplete="off" /></td>';
	html += '<td headers="clg-end" class="td-date"><input class="datepickerexceptionnelle" type="text" name="end-period-0" id="end-period-0" value="" autocomplete="off" /></td>';
	html += '<td headers="clg-action" class="td-action"><input class="action" type="button" name="save-period" id="save-period-0" value="' + expeditionsEnregistrer + '" onclick="lavascript:closing_save(0);" />';
	html += '<div id="save-load-0" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>';
	html += '<br /><a class="del button" onclick="javascript:closing_del(0);">' + expeditionsAnnuler + '</a></td></tr>';

	// Affiche ce formulaire et initialise les champs date
	$("#tb-closing tbody").append(html);
	loadDatePicker();
}

/**
	Cette fonction permet de faire apparaître le formulaire de modification des informations sur une fermeture exceptionnelle
*/
function closing_edit(id, name, start, end){
	// Création HTML du formulaire d'édition d'une fermeture exceptionnelle
	var html = '<td headers="clg-name" class="td-info"><input type="text" name="name-period-'+id+'" id="name-period-'+id+'" value="'+name+'" /></td>';
	html += '<td headers="clg-start" class="td-date"><input class="datepickerexceptionnelle" type="text" name="start-period-'+id+'" id="start-period-'+id+'" value="'+start+'" /></td>';
	html += '<td headers="clg-end" class="td-date"><input class="datepickerexceptionnelle" type="text" name="end-period-'+id+'" id="end-period-'+id+'" value="'+end+'" /></td>';
	html += '<td headers="clg-action" class="td-action"><input class="action" type="button" name="save-period" id="save-period-'+id+'" value="' + expeditionsEnregistrer + '" title="' + expeditionsEnregistrerModif + '" onclick="lavascript:closing_save('+id+');" />';
	html += '<div id="save-load-'+id+'" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>';
	html += '<br /><a class="del" title="' + expeditionsAnnulerModif + '" onclick="javascript:closing_edit_cancel('+id+', \''+name+'\', \''+start+'\', \''+end+'\');">' + expeditionsAnnuler + '</a></td>';

	// Affiche le formulaire et initalise les champs date
	$("#closing-"+id).html(html);
	loadDatePicker();
}

/**
	Cette fonction permet de sauvegarder une fermeture exceptionnelle
*/
function closing_save(id){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();

	// Affiche l'image pour montrer que l'action a bien lieu
	$("#save-period-"+id).hide(); $("#save-period-"+id).parent().find('a.del').hide(); $("#save-load-"+id).show();
	var idElem = id;
	// Requête AJAX pour la sauvegarde
	$.ajax({
		type: 'POST',
		url: '/admin/config/expeditions/ajax-closing.php',
		data: 'addclosing=1&closing='+id+'&'+$("form").serialize(),
		dataType: 'xml',
		success: function(xml) {
			if( $(xml).find('result').attr('type')==0 ){
				$("#tb-closing").before( '<div class="error">'+$(xml).find('error').text()+'</div>' );

				$('#save-load-' + idElem).parents('tr').find('input, a').show();
				$('#save-load-' + idElem).hide();
			} else {
				// Affiche le message de succès
				$("#tb-closing").before( '<div class="error-success">'+$(xml).find('success').text()+'</div>' );

				// Récupère les informations sur la fermeture
				var id = $(xml).find('result').attr('id');
				var name = $(xml).find('result').attr('name');
				var start = $(xml).find('result').attr('start');
				var end = $(xml).find('result').attr('end');

				// Affiche ces informations
				if( $(xml).find('result').attr('add')==1 ){ // il s'agit d'une nouvelle fermeture
					var html = '<tr id="closing-'+id+'">';
					html += '<td headers="clg-name" class="td-info">'+name+'</td>';
					html += '<td headers="clg-start" class="td-date">'+start+'</td>';
					html += '<td headers="clg-end" class="td-date">'+end+'</td>';
					html += '<td headers="clg-action" class="td-action"><a class="del" title="' + expeditionsSupprimerFermeture + '" onclick="javascript:closing_del('+id+');"><img class="edit-closing" src="/admin/images/del.svg" alt="Editer" title="' + expeditionsEditerFermeture + '" onclick="javascript:closing_edit('+id+', \''+addslashes(name)+'\', \''+start+'\', \''+end+'\');" />';
					html += '<div id="save-load-'+id+'" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>';
					html += '</a></td>';
					html += '</tr>';
					$("#tb-closing").append(html);
					$("#new-closing").remove();
				} else {
					// Il s'agit d'une mise à jour
					closing_edit_cancel(id, name, start, end);
				}
			}
		}
	});
}

/**
	Cette fonction permet d'annuler les modifications apportées lors d'une édition
*/
function closing_edit_cancel(id, name, start, end){
	// Constuit la ligne sans les input
	html = '<td headers="clg-name" class="td-info">'+name+'</td>';
	html += '<td headers="clg-start" class="td-date">'+start+'</td>';
	html += '<td headers="clg-end" class="td-date">'+end+'</td>';
	html += '<td headers="clg-action" class="td-action">';
	html += '<img class="edit-closing" src="/admin/images/expeditions/edit.png" alt="Editer" title="' + expeditionsEditerFermeture + '" onclick="javascript:closing_edit('+id+', \''+addslashes(name)+'\', \''+start+'\', \''+end+'\');" />';
	html += '<div id="save-load-'+id+'" class="save-load"><img alt="" src="/admin/images/loader2.gif"></div>';
	html += '<br /><a class="del" title="' + expeditionsSupprimerFermeture + '" onclick="javascript:closing_del('+id+');">' + expeditionsSupprimer + '</a></td>';

	// Affecte le nouveau HTML à la ligne du tableau
	$("#closing-"+id).html(html);
}

/**
	Cette fonction permet de supprimer une fermeture exceptionnelle
*/
function closing_del(id){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();

	if( id==0 ){
		// Suppression du formulaire d'ajout
		$("#new-closing").remove();

		// S'il n'y a plus de fermeture, on affiche le message "Aucune fermeture ... "
		if( $("#tb-closing tbody tr").length==1 )
			$("#none-closing").show();
	} else {

		// Affiche l'image pour montrer que l'action a bien lieu
		$("#closing-"+id).find('img.edit-closing').hide();
		$("#closing-"+id).find('a.del').hide();
		$("#save-load-"+id).show();

		// Demande confirmation avant la suppression d'une fermeture exceptionnelle
		if( window.confirm(expeditionsConfirmSuppressionFermeture) ){
			// Suppression d'une fermeture enregistrée dans la base
			$.ajax({
				type: 'POST',
				url: '/admin/config/expeditions/ajax-closing.php',
				data: 'delclosing=1&closing='+id,
				dataType: 'xml',
				success: function(xml) {
					if( $(xml).find('result').attr('type')==0 )
						$("#tb-closing").before( '<div class="error">'+$(xml).find('error').text()+'</div>' );
					else {
						$("#tb-closing").before( '<div class="error-success">'+$(xml).find('success').text()+'</div>' );
						$("#closing-"+id).remove();

						// S'il n'y a plus de fermeture, on affiche le message "Aucune fermeture ... "
						if( $("#tb-closing tbody tr").length==1 )
							$("#none-closing").show();
					}
				}
			});
		} else {
			// Réaffiche les éléments pour les actions
			$("#closing-"+id).find('img.edit-closing').show();
			$("#closing-"+id).find('a.del').show();
			$("#save-load-"+id).hide();
		}
	}

}

/**
	Cette fonction permet de charger ou de recharger le sélectionneur de date pour les champs de type date
*/
function loadDatePicker(){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();

	// Parcours tous les champs de type date
	$('input.datepickerexceptionnelle').each(function(){
		var temp = this ;

		// Implémente le sélecteur de date sur chacun d'entre eux.
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				var date = $(temp).val();
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					if( formated!=date )
						$(temp).DatePickerHide();
				}
			}
		});

	});
}

// Lors du changement du type de service de livraison, si le type "Points relais" est choisi alors les options de sont affichés
$(document).on('change', '[id="table-conf-livr-service-edit"] [name="type_id"]', function(){
	var is_relays = $(this).val() == 2;

	if( is_relays ){
		$('[id="table-conf-livr-service-edit"] .rly_option').removeClass('hide');
	}else{
		$('[id="table-conf-livr-service-edit"] .rly_option').addClass('hide');
	}
});