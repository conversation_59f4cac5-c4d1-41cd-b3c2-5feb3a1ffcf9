<?php

/**	\file twig.inc.php
 * 	Ce fichier contient les fonctions global pouvant être utilisé lors de l'implémentation de Twig
 */

// Ajout de la function i18n à Twig
$translation = new \Twig\TwigFunction('i18n', function ($txt) {
	return i18n::get($txt);
});
$twig->addFunction($translation);

// Ajout de la function getRequestURI à Twig
$getUrl = new \Twig\TwigFunction('getRequestURI', function () {
	return $_SERVER['REQUEST_URI'];
});
$twig->addFunction($getUrl);

// Ajout de la function $_GET['param'] à Twig
$getParam = new \Twig\TwigFunction('get_param', function () {
	// return name
	if(isset($_GET['name'])) {
		return $_GET['name'];
	}
	// return address
	if(isset($_GET['addr'])) {
		return $_GET['addr'];
	}
	// payment method
	if(isset($_GET['payment'])) {
		return $_GET['payment'];
	}
	// and so on...
});
$twig->addFunction($getParam);

$uniqid = new \Twig\TwigFunction('uniqid', function(){
	return uniqid();
});
$twig->addFunction($uniqid);

// Ajout du filtre de formatage des numéro de téléphone
// Valider utilité si les numéro de téléphones sont stockés sous diverses formats
$phone = new \Twig\TwigFilter('phone', function ($num) {
	switch(strlen($num)) {
		case 10: // numéro français standard à 10 chiffres
			return preg_replace('/^(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})$/', '$1.$2.$3.$4.$5', $num);
		default: // type inconnu
			return $num;
	}
});
$twig->addFilter($phone);

// Ajout du filtre nl2dash (\n -> " - ")
$nl2dash = new \Twig\TwigFilter('nl2dash', function ($txt) {
	return preg_replace('/\n/', ' - ', $txt);
});
$twig->addFilter($nl2dash);

/** Cette fonction gère l'affichage d'une balise image, elle peut aussi ne retourner que l'url de l'image.
 *	\param $img_id Obligatoire, identifiant d'une image (si faux alors on utilisera l'image par défaut)
 *	\param $size Obligatoire, configuration d'image à utiliser
 *	\param $alt Optionnel, contenu de la balise alt
 *	\param $title Optionnel, contenu de la balise title
 *	\param $only_url Optionnel, mettre true pour retourner que l'url de l'image
 *	\param $class Optionnel, classe à appliquer sur l'image
 *	\return La balise HTML de l'image
 */
$image = new \Twig\TwigFunction('image', function ( $img_id, $thumb, $alt='', $title='', $only_url=false, $class='' ){
	global $config;

	if( !array_key_exists($thumb, $config['img_sizes']) ){
		return '';
	}

	$size = $config['img_sizes'][ $thumb ];
	if( !is_numeric($img_id ) || $img_id<=0 ){
		$img_id = $config['default_image'];
	}

	$url = $config['img_url'].'/'.$size['dir'].'/'.$img_id.'.'.$size['format'];
	$dir = $config['img_dir'].'/'.$size['dir'].'/'.$img_id.'.'.$size['format'];

	if( isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] ){
		$url = str_replace( 'http://', 'https://', $url );
	}

	$img_url_exists = @getimagesize( $dir );
	if( !is_array($img_url_exists) || !sizeof($img_url_exists) ){
		$img_id = $config['default_image'];

		$url = $config['img_url'].'/'.$size['dir'].'/'.$img_id.'.'.$size['format'];
		if( isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] ){
			$url = str_replace( 'http://', 'https://', $url );
		}
	}

	if ($only_url) {
		return $url;
	}

	$alt = html_revert_riawisiwig( $alt );
	$title = html_revert_riawisiwig( $title );

	return '
		<img
			class="'.$class.( $img_id == $config['default_image'] ? ' no-cursor' : '' ).'"
			src="'.$url.( $img_id == $config['default_image'] ? '?1' : '' ).'"
			alt="'.htmlspecialchars( $alt ).'"
			title="'.htmlspecialchars( $title ).'"
			width="'.$size['width'].'" height="'.$size['height'].'" />
	';
});

$twig->addFunction($image);

/** Cette fonction permet de gérer l'affichage d'un tarif en HT ou en TTC selon l'utilisateur connecté
 * 	@param float $ht Obligatoire, montant HT du tarif
 * 	@param float $tva_rate Optionnel, taux de TVA (par défaut régler à 20 %)
 * 	@param float $ttc Optionnel, montant TTC du tarif
 * 	@param bool $force_ht Optionnel, permet de forcer un affichage en HT (par défaut à false)
 * 	@return string Le tarif mis en forme
 */
$display_price = new \Twig\TwigFunction('display_price', function( $ht, $tva=1.200, $ttc=null, $force_ht=false ){
	if( !is_numeric($tva) || $tva < 1 ){
		$tva = 1.200;
	}

	$price = $ht;

	if( !$force_ht && (!isset($_SESSION['usr_display_prices']) || $_SESSION['usr_display_prices'] == 'ttc') ){
		if( is_numeric($ttc) && $ttc >= 0 ){
			$price = $ttc;
		}else{
			$price = $ht * $tva;
		}
	}

	return number_format( $price, 2, ',', ' ' );
});

$twig->addFunction($display_price);

/** Cette fonction gère l'affichage d'un tarif.
 * 	@param $price Obligatoire, montant HT d'un tarif
 * 	@return string Le tarif mis en forme
 */
$price = new \Twig\TwigFunction('price', function( $price ){
	$formatter = new NumberFormatter( i18n::getLangISO(), NumberFormatter::DECIMAL );
	$formatter->setAttribute( NumberFormatter::MIN_FRACTION_DIGITS, 2 );
  return $formatter->format( $price );
});

$twig->addFunction($price);

/** Cette fonction permet de vérifier qu'un compte a bien accès à un droit
 * 	@param array $code Obligatoire, tableau du ou des droits à vérifier
 * 	@param bool $all Optionnel, par défaut le compte doit avoir accès à tous les codes donnée, mettre true pour avoir au moins un des codes accessible
 * 	@return bool true si l'accès est confirmé, false dans le cas contraire
 */
$has_access = new \Twig\TwigFunction('has_access', function( $code, $all=true, $usr_id=0 ){
	return gu_users_rights_used( $code, $usr_id, 0, $all );
});

$twig->addFunction($has_access);


/** Filtre preg_replace
 * @param	string	$pattern
 * @param	string	$replacement
 * @param	string	$subject
 * @return	string|array|null preg_replace returns an array if the subject parameter is an array, or a string otherwise.
 */
$preg_replace = new \Twig\TwigFilter('preg_replace', function($subject, $pattern, $replacement){
	return preg_replace($pattern, $replacement, $subject);
});

$twig->addFilter($preg_replace);