<?php
namespace Pdf;
/**
 * \ingroup PieceDeVente
 */
class Taxes
{
    /**
	 * @var array $tva
	 */
	protected $tva;

    /**
	 * @var array $ecotaxe
	 */
	protected $ecotaxe;

	/**
	 * __construct
	 *
	 * \return void
	 */
	public function __construct()
	{
		$this->tva = array();

		$this->ecotaxe = array(
			'base' => 0,
			'amount' => 0,
		);
	}

	/**
	 * Retourne le tableau des tva
	 *
	 * \return void
	 */
	public function tva()
	{
		return $this->tva;
	}

	/**
	 * Modification de la TVA
	 * Array(base => float, amount => float)
	 *
	 * \param array $tva 
	 * 
	 * \return void
	 */
	public function setTva(array $tva)
	{
		$this->tva = $tva;

		return $this;
	}

	/**
	 * Retourne le tableau de l'écotaxe
	 *
	 * \return void
	 */
	public function ecotaxe()
	{
		return $this->ecotaxe;
	}

	/**
	 * Modification de l'écotaxe
	 *
	 * \param array $ecotaxe
	 * 
	 * \return void
	 */
	public function setEcotaxe(array $ecotaxe)
	{
		$this->ecotaxe = $ecotaxe;

		return $this;
	}

	/**
	 * Ajoute un taux tva
	 *
	 * \param mixed $rate
	 * \param mixed $base
	 * \param mixed $amount
	 * 
	 * \return void
	 */
	public function addTvaRate($rate, $base, $amount)
	{
		if ($rate <= 1) {
			return;
		}

		if ($this->rateExists($rate)) {
			$this->updateRate($rate, $base, $amount);
		} else {
			$this->addRate($rate, $base, $amount);
		}
	}

	/**
	 * Ajoute un montant d'écotaxe
	 *
	 * \param mixed $base
	 * \param mixed $amount
	 * 
	 * \return void
	 */
	public function addEcotaxe($base, $amount)
	{
		$this->ecotaxe['base'] += $base;
		$this->ecotaxe['amount'] += $amount;
	}

	/**
	 * Détermine si un taux existe
	 *
	 * \param string $rate
	 * 
	 * \return boolean
	 */
	protected function rateExists($rate)
	{
		return array_key_exists($rate, $this->tva);
	}

	/**
	 * Met ajour un taux de tva
	 *
	 * \param mixed $rate
	 * \param mixed $base
	 * \param mixed $amount
	 * 
	 * \return void
	 */
	protected function updateRate($rate, $base, $amount)
	{
		$this->tva[$rate]['base'] += $base;
		$this->tva[$rate]['amount'] += $amount;
	}

	/**
	 * Ajoute un taux de tva au tableau
	 *
	 * \param mixed $rate
	 * \param mixed $base
	 * \param mixed $amount
	 * 
	 * \return void
	 */
	protected function addRate($rate, $base, $amount)
	{
		$this->tva[$rate] = array(
			'base' => $base,
			'amount' => $amount
		);
	}
}