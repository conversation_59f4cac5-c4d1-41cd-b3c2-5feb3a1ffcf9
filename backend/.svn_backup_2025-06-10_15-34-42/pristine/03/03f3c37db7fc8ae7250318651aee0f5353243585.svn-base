<?php

class T_ReserveResponse
{

    /**
     * @var XCHANGE_TR $T_ReserveResult
     */
    protected $T_ReserveResult = null;

    /**
     * @param XCHANGE_TR $T_ReserveResult
     */
    public function __construct($T_ReserveResult)
    {
      $this->T_ReserveResult = $T_ReserveResult;
    }

    /**
     * @return XCHANGE_TR
     */
    public function getT_ReserveResult()
    {
      return $this->T_ReserveResult;
    }

    /**
     * @param XCHANGE_TR $T_ReserveResult
     * @return T_ReserveResponse
     */
    public function setT_ReserveResult($T_ReserveResult)
    {
      $this->T_ReserveResult = $T_ReserveResult;
      return $this;
    }

}
