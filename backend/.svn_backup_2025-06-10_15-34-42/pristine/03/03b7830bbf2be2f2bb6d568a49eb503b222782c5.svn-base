/**
 *	Permet de modifier la position des lignes en ajax via drag&drop
 *	Ex :
 *	
 *	riaSortable.create({
 *		'table'	:	$('#categories'),
 *		'url'	:	'/admin/ajax/catalog/ajax-position-update.php'
 *	});
 *
 */

	/**	Constructeur
	 */
	var riaSortable = function(param) {
		var object = this;
		var table = param.table;
		
		if (table.length !== 1) throw riaSortableErreurTable;
		
		var tbody = table;
		
		this.tbody = tbody;
		this.url = param.url;
		this.rowPrefix = (param.rowPrefix !== undefined ? param.rowPrefix : 'line-');
		
		if (param.beforeUpdate) this.beforeUpdate = param.beforeUpdate;
		
		// Vide les cellules up/down et place un drag element
		tbody.find('.ria-cell-move').each(function() {
			if( $(this).find('.ria-row-catchable').length == 0 ){
				var drag = $('<div></div>')
					.addClass('ria-row-catchable')
					.attr('title', 'Déplacer')
				;
				$(this)
					.empty()
					.append(drag)
				;
			}
		});
				
		var items;
		if (param.items) {
			var _items = param.items;
			items = (typeof _items !== 'function') ? function() { return _items; } : _items;
		}
		else {
			items = function() {
				var rowPrefix = object.rowPrefix;
				var r = [];
				var escRowPrefix = rowPrefix.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
				tbody.find('.ria-row-orderable').each(function() {
					if( (new RegExp('^' + escRowPrefix)).exec($(this).attr('id')) ){
						r.push($(this));
					}
				});
				return r;
			};
		}
		this.__getItems = items;
		
		// Sortable
		tbody
			.sortable({
				'items'		:	items(),
				'handle'	:	'.ria-row-catchable',
				'update'	:	function(e, ui) { return object.update(e, ui); }
			})
		;
		
		this.currentOrder = this.getOrder();
		return this;
	};
	
	/**	Constructeur statique
	 */
	riaSortable.create = function(param) {
		var table = param.table;
		var arg = param;
		table.each(function() {
			arg.table = $(this);
			new riaSortable(arg);
		});
		return this;
	};
	
	riaSortable.prototype = {
		/**	Annulation
		 */
		'cancel': function() {
			this.tbody.sortable('cancel');
			return this;
		},
		
		/**	Renvoie les items
		 */
		'getItems': function() {
			return this.__getItems.call(this);
		},
		
		/**	Renvoie la liste des ids dans l'ordre
		 */
		'getOrder': function() {
			var r = [];
			var items = this.getItems();
			var rowPrefix = this.rowPrefix;
			$.each(items, function(key, value) {
				r.push(value.attr('id').replace(rowPrefix, ''));
			});
			return r;
		},
		
		/**	Déplacement
		 */
		'update': function(e, ui) {
			var row = ui.item;
			
			var source = row.attr('id').replace(this.rowPrefix, '');
			
			// Recherche où la source a été déplacée
			var current = this.currentOrder;
			var order = this.getOrder();
			
			var i, n = current.length, p, target;
			for (i=0; i<n; i++) {
				if (order[i] == source) { target = i; if (p !== undefined) break; }
				else if (current[i] == source) { p = i; if (target !== undefined) break; }
			}
			var action = (target > p) ? 'after' : 'before';
			target = current[target];
			
			if (this.beforeUpdate) if (this.beforeUpdate.call(this, {'source': source, 'target': target, 'action': action}) === false) return this;
			
			var ani = $('<img alt="" src="/admin/images/loader2.gif" style="border: none" />');
			
			var hand = $(row.find('.ria-row-catchable')[0]);
			hand.hide();				// Masque dragable
			hand.parent().append(ani);	// Joue l'animation
			
			var object = this;
			
			var error = function() {
				object.cancel();
				window.alert(riaSortableAlertErreurEnregistremzent);
			};
			
			// Requête ajax
			var success = function (response) {
				if (!(response && response.success)) return error();

				object.currentOrder = order;
			};

			var complete = function () {
				hand.show();
				ani.remove();
			};

			if (this.url) {
				$.ajax({
					'url'		:	this.url,
					'data'		:	'source=' + encodeURIComponent(source) + '&target=' + encodeURIComponent(target) + '&action=' + encodeURIComponent(action),
					'type'		:	'post',
					'dataType'	:	'json',
					'success'	:	success.bind(this),
					'error'		:	error,
					'complete'	:	complete.bind(this)
				});
			} else {
				object.currentOrder = order;
				complete();
			}

			return this;
		}
	};
