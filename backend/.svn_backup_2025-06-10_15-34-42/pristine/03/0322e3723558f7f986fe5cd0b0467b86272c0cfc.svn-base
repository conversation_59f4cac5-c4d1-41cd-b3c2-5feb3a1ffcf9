<?php

	/** \file refresh-stores-gps-compute.php
	 * 	Ce script est permet de mettre à jour les coordonées géographie des magasins (calculées automatiquement).
	 */

	 set_include_path(dirname(__FILE__) . '/../include/');
	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	
	$rstr = dlv_stores_get( 0, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
	if( !$rstr || !ria_mysql_num_rows($rstr) ){
		return false;
	}
	
	while( $str = ria_mysql_fetch_array($rstr) ){
		if( (!is_numeric($str['latitude_compute']) || !is_numeric($str['longitude_compute'])) && (!is_numeric($str['latitude']) || !is_numeric($str['longitude'])) ){
			
			// recherche les coordonées GPS du magasin en 2 étape (adresse complète et si aucun résultat, adresse imcomplète -zipcode, ville et Pays)
			$gps = sys_google_maps_search( $str['address1'].' '.$str['zipcode'].' '.$str['city'].', '.$str['country'] );
			if( !is_array($gps) || !isset($gps['lat'], $gps['lng']) ){
				$gps = sys_google_maps_search( $str['zipcode'].' '.$str['city'].', '.$str['country'] );
			}
			
			// mise à jour des coodonées GPS trouvées
			if( is_array($gps) && isset($gps['lat'], $gps['lng']) && is_numeric($gps['lat']) && is_numeric($gps['lng']) ){
				dlv_stores_set_coordinates_compute( $str['id'], $gps['lat'], $gps['lng'] );
			}
			
		}
	}
