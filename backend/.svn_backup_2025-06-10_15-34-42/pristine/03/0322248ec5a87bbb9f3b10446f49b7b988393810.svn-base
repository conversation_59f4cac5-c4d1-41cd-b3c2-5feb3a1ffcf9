<?php

require_once('delivery.inc.php');

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE_SRV');

if( isset($_POST['add-main']) ){
	header('location: edit.php');
	exit;
}

$error = false;

if( isset($_POST['del-main'], $_POST['srv']) ){
	foreach( $_POST['srv'] as $srv ){
		if( !dlv_store_services_del($srv) ){
			$error = _('Une erreur est survenue lors de la suppression d\'un service.');
			break;
		}
	}

	if( !$error ){
		$success = _('Le service a bien été supprimé.');
	}
}

define('ADMIN_PAGE_TITLE', _('Services') . ' - ' . _('Magasins') . ' - ' . _('Livraison des commandes'));

// Charge la liste des services
$r_services = dlv_store_services_get();
$services_count = ria_mysql_num_rows($r_services);

require_once('admin/skin/header.inc.php');

?>

<h2><?php print _('Services'); ?> (<?php print ria_number_format($services_count) ?>)</h2>
<?php if( isset($_GET['success']) && $_GET['success'] === 'true' ){ ?>
	<div class="notice success"><?php print _('Le service a bien été enregistré'); ?></div>
<?php } ?>
<?php if( $error ){ ?>
	<div class="notice error"><?php print htmlspecialchars($error); ?></div>
<?php } ?>
<?php if( isset($success) ){ ?>
	<div class="notice success"><?php print htmlspecialchars($success); ?></div>
<?php } ?>
<div class="notice">
	<?php print _('Pour améliorer la présentation de vos points de vente, vous pouvez définir ici un ou plusieurs services (Accueil, Technique, Administratif, etc...) qui viendront compléter la présentation des membres du personnel.'); ?>
</div>
<form action="index.php" method="post">
	<table id="table-services" class="checklist">
		<thead>
			<tr>
				<th id="cat-sel">
					<input type="checkbox" class="checkbox" onclick="checkAllClick(this);">
				</th>
				<th><?php print _("Nom"); ?></th>
				<th class="align-right"><?php print _('Nombre d\'employés'); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php if( $r_services && ria_mysql_num_rows($r_services) ){ ?>
				<?php while( $service = ria_mysql_fetch_assoc($r_services) ){ ?>
					<tr>
						<td headers="cat-sel">
							<input type="checkbox" class="checkbox" name="srv[]" value="<?php print $service['id']; ?>">
						</td>
						<td>
							<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_SRV_EDIT') ){ ?>
								<a href="edit.php?srv=<?php print $service['id']; ?>"><?php print htmlspecialchars($service['name']); ?></a>
							<?php }else{ ?>
								<?php print htmlspecialchars($service['name']); ?>
							<?php } ?>
						</td>
						<td class="align-right">
							<?php print ria_number_format(dlv_store_services_count($service['id'])); ?>
						</td>
					</tr>
				<?php } ?>
			<?php }else{ ?>
				<tr>
					<td colspan="3"><?php print _('Aucun service'); ?></td>
				</tr>
			<?php } ?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="3">
					<?php if( $services_count && gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_SRV_DEL') ){ ?>
						<button type="submit" class="btn-del" name="del-main"><?php print _('Supprimer'); ?></button>
					<?php } ?>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_SRV_ADD') ){ ?>
						<input type="submit" name="add-main" value="<?php print _('Ajouter'); ?>">
					<?php } ?>
				</td>
			</tr>
		</tfoot>
	</table>
</form>

<?php

require_once('admin/skin/footer.inc.php');