<?php

set_include_path(dirname(__FILE__) . '/../include/');
	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );

	$count = $missing = $resolved = 0;

	$images = img_images_get();
	while( $image = ria_mysql_fetch_array($images) ){
		$img_id = $image['id'];
		if( img_images_exists( $img_id ) ){
			foreach( $config['img_sizes'] as $key=>$size ){
				$count++;
				$filename = $config['img_dir'].'/'.$size['width'].'x'.$size['height'].'/'.$img_id.'.jpg';
				
				if( file_exists($filename) ){
					// Effectue une passe supplémentaire d'optimisation sur les images (inspirée par Yahoo Smush.it)
					// Pour les images de type jpeg, compresse l'image à l'aide de jpegtran
					exec('jpegtran -copy none -optimize -perfect -outfile "'.$filename.'" "'.$filename.'"');
				}				
			}
		}
	}
	echo number_format($resolved,0,',',' ').' fichiers regeneres sur un total de '.number_format($missing,0,',',' ').' images manquantes'."\n";


