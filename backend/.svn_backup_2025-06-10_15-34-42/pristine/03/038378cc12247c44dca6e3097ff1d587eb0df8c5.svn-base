<?php
/**
 * \defgroup api-actions-restrictions Restrictions
 * \ingroup OnBoardingYuto
 * @{
 * \page api-actions-restrictions-get Chargement
 *
 * Cette fonction récupére une liste des restrictions pour les actions de l'onboarding Yuto
 *
 *		\code
 *			GET /actions/restrictions/
 *		\endcode
 *
 * @return Liste des rectrictions contenant les colonnes :
 *		\code{.json}
 *			{
 *				 "act_id" : identifiant de l'action parent
 *				 "grp_id" : identifiant du group (il faut valider un groupe pour valider une action)
 *				 "require_id" : identifiant de l'action requise (il faut valider toutes les actions d'un groupe pour valider le groupe)
 *			}
 *		\endcode
 * @}
*/

	require_once('act_actions.inc.php');

	switch( $method ){
		case 'get':


			$rstates = act_actions_restrictions_get()  ;
			if( $rstates && ria_mysql_num_rows($rstates) ){
				while($state = ria_mysql_fetch_assoc($rstates)){
					$content[] = $state;
				}
			}
			$result = true;
			break;
	}
