<?php
/**
 *	\defgroup bank Informations bancaires 
 *	\ingroup crm
 * @{
*/

switch( $method ){
	/** @{@}
 	 * @{	
	 * \page api-banks-index-add Ajout 
	 *
	 * Cette fonction permet d'ajouter des informations bancaires
	 *
	 *		\code
	 *			POST /banks/
	 *		\endcode
	 *			
	 * @param usr_id Obligatoire, identifiant de l'utilisateur
	 * @param name Obligatoire, nom donné aux informations bancaires
	 * @param rib_code_bank Obligatoire, code de la banque
	 * @param rib_counter Obligatoire, code du guichet
	 * @param rib_account Obligatoire, numéro du compte
	 * @param rib_key Obligatoire, clé du RIB
	 * @param iban Obligatoire, International Bank Account Number
	 * @param bic Obligatoire, Bank Identifier Code
	 * @param structure_type Facultatif, type de structure bancaire (0 = locale, 1 = autre, 2 = BBAN, 3 = IBAN)
	 * @param agency_name Facultatif, nom de l'agence
	 * @param comments Facultatif, commentaires divers
	 * @param address1 Facultatif, premier champ adresse
	 * @param address2 Facultatif, second champ adresse
	 * @param zipcode Facultatif, code postal
	 * @param city Facultatif, ville
	 * @param country_code Facultatif, pays en code ISO 2 caractères
	 *	
	 * @return id : Identifiant des informations bancaire
	 * @}		
	*/
	case 'add':

		if( !isset($_REQUEST['usr_id'],$_REQUEST['name'],$_REQUEST['rib_code_bank'],$_REQUEST['rib_counter'],$_REQUEST['rib_account'],$_REQUEST['rib_key'],$_REQUEST['iban'],$_REQUEST['bic']) ){
			throw new Exception("Paramètres invalide" );
		}

		if( !isset($_REQUEST['structure_type']) || !$_REQUEST['structure_type'] ) $_REQUEST['structure_type'] =  0;
		if( !isset($_REQUEST['agency_name']) ) $_REQUEST['agency_name'] =  null;
		if( !isset($_REQUEST['comments']) ) $_REQUEST['comments'] =  null;

		if( !isset($_REQUEST['address1']) ) $_REQUEST['address1'] =  "";
		if( !isset($_REQUEST['address2']) ) $_REQUEST['address2'] =  "";
		if( !isset($_REQUEST['zipcode']) ) $_REQUEST['zipcode'] =  "";
		if( !isset($_REQUEST['city']) ) $_REQUEST['city'] =  "";
		if( !isset($_REQUEST['country_code']) ) $_REQUEST['country_code'] =  "";

		$address_data = null;
		if( $_REQUEST['address1'] || $_REQUEST['address2'] || $_REQUEST['zipcode'] || $_REQUEST['city'] || $_REQUEST['country_code'] ){
			$address_data = array(
				'address1' => $_REQUEST['address1'],
				'address2' => $_REQUEST['address2'],
				'zipcode' => $_REQUEST['zipcode'],
				'city' => $_REQUEST['city'],
				'country_code' => $_REQUEST['country_code']
				);
		}

		if( !is_numeric($_REQUEST['usr_id']) ){
			$_REQUEST['usr_id'] = 0;
		}
		
		$id = site_bank_details_add( $_REQUEST['name'], $_REQUEST['rib_code_bank'], $_REQUEST['rib_counter'], $_REQUEST['rib_account'], $_REQUEST['rib_key'], $_REQUEST['iban'], $_REQUEST['bic'], $_REQUEST['usr_id'], $_REQUEST['structure_type'], $_REQUEST['agency_name'], $address_data, $_REQUEST['comments'] );
		if( $id > 0 ){
			$result = true;
			$content = array('id'=>$id);
		}

		break;

	/** @{@}
 	 * @{	
	 * \page api-banks-index-upd Mise à jour
	 *
	 * Permet la mise jour d'informations bancaires.	
	 *
	 *		\code
	 *			PUT	/banks/
	 *		\endcode
	 *
	 * @param id Obligatoire, identifiant des inforamtions bancaires
	 * @param usr_id Obligatoire, identifiant de l'utilisateur
	 * @param name Obligatoire, nom donné aux informations bancaires
	 * @param rib_code_bank Obligatoire, code de la banque
	 * @param rib_counter Obligatoire, code du guichet
	 * @param rib_account Obligatoire, numéro du compte
	 * @param rib_key Obligatoire, clé du RIB
	 * @param iban Obligatoire, International Bank Account Number
	 * @param bic Obligatoire, Bank Identifier Code
	 * @param structure_type Facultatif, type de structure bancaire (0 = locale, 1 = autre, 2 = BBAN, 3 = IBAN)
	 * @param agency_name Facultatif, nom de l'agence
	 * @param comments Facultatif, commentaires divers
	 * @param address1 Facultatif, premier champ adresse
	 * @param address2 Facultatif, second champ adresse
	 * @param zipcode Facultatif, code postal
	 * @param city Facultatif, ville
	 * @param country_code Facultatif, pays en code ISO 2 caractères
	 *
	 * @return true si la mise à jour s'est déroulée avec succès. 
	 * @}
	*/
	case 'upd':

		if( !isset($_REQUEST['id'],$_REQUEST['usr_id'],$_REQUEST['name'],$_REQUEST['rib_code_bank'],$_REQUEST['rib_counter'],$_REQUEST['rib_account'],$_REQUEST['rib_key'],$_REQUEST['iban'],$_REQUEST['bic']) ){
			throw new Exception("Paramètres invalide" );
		}
		
		if( !isset($_REQUEST['structure_type']) || $_REQUEST['structure_type'] ) $_REQUEST['structure_type'] =  0;
		if( !isset($_REQUEST['agency_name']) ) $_REQUEST['agency_name'] =  null;
		if( !isset($_REQUEST['comments']) ) $_REQUEST['comments'] =  null;

		if( !isset($_REQUEST['address1']) ) $_REQUEST['address1'] =  "";
		if( !isset($_REQUEST['address2']) ) $_REQUEST['address2'] =  "";
		if( !isset($_REQUEST['zipcode']) ) $_REQUEST['zipcode'] =  "";
		if( !isset($_REQUEST['city']) ) $_REQUEST['city'] =  "";
		if( !isset($_REQUEST['country_code']) ) $_REQUEST['country_code'] =  "";

		$address_data = null;
		if( $_REQUEST['address1'] && $_REQUEST['address2'] && $_REQUEST['zipcode'] && $_REQUEST['city'] && $_REQUEST['country_code'] ){
			$address_data = array(
				'address1' => $_REQUEST['address1'],
				'address2' => $_REQUEST['address2'],
				'zipcode' => $_REQUEST['zipcode'],
				'city' => $_REQUEST['city'],
				'country_code' => $_REQUEST['country_code']
				);
		}

		if( !is_numeric($_REQUEST['usr_id']) ){
			$_REQUEST['usr_id'] = 0;
		}

		if( site_bank_details_update( $_REQUEST['id'], $_REQUEST['name'], $_REQUEST['rib_code_bank'], $_REQUEST['rib_counter'], $_REQUEST['rib_account'], $_REQUEST['rib_key'], $_REQUEST['iban'], $_REQUEST['bic'], $_REQUEST['usr_id'], $_REQUEST['structure_type'], $_REQUEST['agency_name'], $address_data, $_REQUEST['comments'] ) ){
			$result = true;
		}

		break;

	/** @{@}
 	 * @{	
	 * \page api-banks-index-del Suppression 
	 *
	 * Permet la suppression d'informations bancaires
	 *		
	 *		\code
	 *			DELETE	/banks/
	 *		\endcode
	 *		
	 * @param bnk_id Obligatoire, identifiant des informations bancaires
	 *		
	 * @return true si la suppression s'est déroulée avec succès. 
	 * @}
	*/
	case 'del':

		if( !is_numeric($_REQUEST['bnk_id']) ){
			throw new Exception('Paramètres incomplet');
		}

		if( !site_bank_details_del($_REQUEST['bnk_id']) ){
			throw new Exception("Erreur lors de la suppression du rib");
		}else{
			$result = true;
		}
		
		break;
}
///@}