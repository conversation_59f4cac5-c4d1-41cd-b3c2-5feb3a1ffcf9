<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

	require_once( 'images.inc.php' );
	
	$limit_img = 70;
	
	$size = $config['img_sizes']['medium'];
	$html = '';
	
	if( isset($_POST['delImage']) ){ // suppression d'un lien entre une image et un contenu
		header('Content-type: application/xml');
		$html = '<?xml version="1.0" encoding="utf8"?>';
		if( isset($_POST['class'], $_POST['idCnt']) ){
			$success = true;
			switch( $_POST['class'] ){
				case CLS_PRODUCT:
					$img_main = prd_images_main_get( $_POST['idCnt'] );
					if( $img_main = $_POST['delImage'] && !prd_images_main_del($_POST['idCnt']) ){
						$success = false;
					}
					if( !prd_images_del($_POST['idCnt'], $_POST['delImage']) ){
						$success = false;
					}
					break;
				case CLS_CATEGORY:
					if( !prd_cat_images_del($_POST['idCnt'], $_POST['delImage']) ){
						$success = false;
					}
					break;
				case CLS_BRAND:
					if( !prd_brands_image_del($_POST['idCnt']) ){
						$success = false;
					}
					break;
				case CLS_STORE:
					if( !dlv_stores_images_del($_POST['idCnt'], $_POST['delImage']) ){
						$success = false;
					}
					break;
				case CLS_CMS:
					$categ = ria_mysql_fetch_array( cms_categories_get($_POST['idCnt'], false, false, -1, false, false, true, null, false, null, false) );
					if( !cms_images_del($_POST['delImage'], $_POST['idCnt'], $categ['wst_id']) ){
						$success = false;
					}
					break;
				case CLS_NEWS:
					if( !news_images_del($_POST['delImage'], $_POST['idCnt']) ){
						$success = false;
					}
					break;
				case CLS_USER:
					if( !img_images_objects_del(CLS_USER, 0, $_POST['delImage'], 0, $_POST['idCnt'])){
						$success = false;
					}
					break;
			}
			$html .= '<result type="'.( $success ? '1' : '0' ).'"></result>';
		}else{
			$html .= '	<result type="0"></result>';
		}
	} elseif( isset($_POST['reloadImage']) ){ // recherche l'affichage des liens entre une image et ses contenus
		$contenu = array( CLS_PRODUCT, CLS_CATEGORY, CLS_BRAND, CLS_STORE, CLS_CMS, CLS_NEWS, CLS_USER );
		
		foreach( $contenu as $c ){
			$html .= img_images_view_linked( $_POST['reloadImage'], $c );
		}
		
	} elseif( isset($_POST['loadListImage'], $_POST['image'], $_POST['nbimage']) ){ // affiche le contenu du carroussel
		$sizeM = $config['img_sizes']['medium'];
		$unused = isset($_POST['unused']) && $_POST['unused'] ? true : false;
		
		// récupère les images
		$rimg = img_images_get( 0, '', '', '', '', $unused ? 0 : null );
		$ar_img = array();
		if( $rimg ){
			while( $img = ria_mysql_fetch_array($rimg) ){
				if( file_exists($config['img_dir'].'/'.$sizeM['dir'].'/'.$img['id'].'.'.$sizeM['format']) ){
					$ar_img[] = $img['id'];
				}
			}
		}
		
		if( sizeof($ar_img) ){
			$pos_img = array_search( $_POST['image'], $ar_img );
			
			// nombre d'image maximum
			$nbmax = $_POST['nbimage'];
			
			// nombre minimum d'image avant et après
			$nb = floor( $_POST['nbimage']/2 );
			
			// position de la première image
			$min = isset($_POST['min']) && $_POST['min']>=0 ? $_POST['min'] : ( $pos_img-$nb>0 ? $pos_img-$nb : 0 );
			
			// déplacement vers l'arrière
			$before = $min-$nbmax>=0 ? $min-$nbmax : 0;
			$html .= $min>0 ? '<div class="prev-img"><a href="#" onclick="return loadListImages('.$before.')"></a></div>' : '<div class="prev-img"></div>';
			
			$count = 0; $i=$min;
			while( $count<$nbmax ){
				if( !isset($ar_img[$i]) )
					break;
				if( $i!=$pos_img ){
					$html .= '	<div class="img-images">';
					$html .= '		<a href="edit.php?image='.$ar_img[ $i ].'&unused='.$unused.'" >';
					$html .= '			<img src="'.$config['img_url'].'/'.$sizeM['dir'].'/'.$ar_img[ $i ].'.'.$sizeM['format'].'" alt="" width="'.$sizeM['width'].'" height="'.$sizeM['height'].'" />';
					$html .= '		</a>';
					$html .= '	</div>';
					$count++;
				}
				$i++;
			}

			// déplacement vers l'avant
			$html .= $i<sizeof($ar_img) ? '<div class="next-img"><a href="#" onclick="return loadListImages('.$i.')"></a></div>' : '<div class="next-img"></div>';
		}
	} elseif( isset($_POST['image']) ) { // génère le contenu de la popup de l'image
		$unused = isset($_POST['unused']) && $_POST['unused'] ? true : false;
		$image = ria_mysql_fetch_array( img_images_get($_POST['image']) );
		
		// recherche les contenus rattachés à l'image
		$ar_prd = img_images_get_by_class( $_POST['image'], CLS_PRODUCT );		// produit
		$ar_cat = img_images_get_by_class( $_POST['image'], CLS_CATEGORY );		// catégorie de produit
		$ar_brd = img_images_get_by_class( $_POST['image'], CLS_BRAND ); 		// marques
		$ar_str = img_images_get_by_class( $_POST['image'], CLS_STORE );		// magasins
		$ar_cms = img_images_get_by_class( $_POST['image'], CLS_CMS );			// gestion de contenu
		$ar_nws = img_images_get_by_class( $_POST['image'], CLS_NEWS );			// actualité
		$ar_usr = img_images_get_by_class( $_POST['image'], CLS_USER );			// clients
		
		

		// On récupère l'image et on affiche l'image par défaut si l'image n'existe pas
		if( is_numeric($_POST['image']) && $_POST['image'] > 0 ){
			// variable qui récupère l'url d'image
			$url_img = $config['img_url'].'/'.$size['dir'].'/'.$_POST['image'].'.'.$size['format'];
			if( !ria_file_exists_byurl($url_img) ){
				$url_img = view_admin_get_img_default('small');
			}
		}else{
			$url_img = view_admin_get_img_default('small');
			if( !ria_file_exists_byurl($url_img) ){
				$url_img = '/admin/images/default.jpg';
			}
		}

		$html = '	<div class="head-img-info">';
		$html .= '		<div class="img-media">';
		$html .= '			<img src="'.$url_img .'" width="'.$size['width'].'" height="'.$size['height'].'" alt="'.( $image['src_name']!='' ? htmlspecialchars($image['src_name']).'.'.$image['type'] : _('Nom de l\'image : Nom disponible') ).'" />';
		$html .= '		</div>';
		$html .= '		<div class="img-used">';
		$html .= '			<span class="name-img">'.view_image_is_sync($image).' '.( $image['src_name']!='' ? htmlspecialchars($image['src_name']).'.'.$image['type'] : _('Nom de l\'image : Non disponible') ).'</span>';			
		if( (is_array($ar_prd) && sizeof($ar_prd)) || (is_array($ar_cat) && sizeof($ar_cat)) || (is_array($ar_brd) && sizeof($ar_brd)) || (is_array($ar_str) && sizeof($ar_str)) || (is_array($ar_cms) && sizeof($ar_cms)) || (is_array($ar_nws) && sizeof($ar_nws)) || (is_array($ar_usr) && sizeof($ar_usr)) ){
			$html .= '			'._('Utilisations :').' <ul>';
			if( is_array($ar_prd) && sizeof($ar_prd) )
				$html .= '				<li>'.sizeof($ar_prd).' produit'.( sizeof($ar_prd)>1 ? 's' : '' ).'</li>';
			if( is_array($ar_cat) && sizeof($ar_cat) )
				$html .= '				<li>'.sizeof($ar_cat).' catégorie'.( sizeof($ar_cat)>1 ? 's' : '' ).'</li>';
			if( is_array($ar_brd) && sizeof($ar_brd) )
				$html .= '				<li>'.sizeof($ar_brd).' marque'.( sizeof($ar_brd)>1 ? 's' : '' ).'</li>';
			if( is_array($ar_str) && sizeof($ar_str) )
				$html .= '				<li>'.sizeof($ar_str).' magasin'.( sizeof($ar_str)>1 ? 's' : '' ).'</li>';
			if( is_array($ar_cms) && sizeof($ar_cms) )
				$html .= '				<li>'.sizeof($ar_cms).' contenu'.( sizeof($ar_cms)>1 ? 's' : '' ).'</li>';
			if( is_array($ar_nws) && sizeof($ar_nws) )
				$html .= '				<li>'.sizeof($ar_nws).' actualité'.( sizeof($ar_nws)>1 ? 's' : '' ).'</li>';
			if( is_array($ar_usr) && sizeof($ar_usr) )
				$html .= '				<li>'.sizeof($ar_usr).' client'.( sizeof($ar_usr)>1 ? 's' : '' ).'</li>';
			$html .= '			</ul>';
		}else{
			$html .= '			'._('Cette image n\'est associée à aucun contenu.').'<br />';
		}
		$html .= '			<a class="more" href="edit.php?image='.$_POST['image'].( $unused ? '&amp;unused=1' : '' ).'">' . _("Plus d'informations") . '</a>';
		$html .= '		</div>';
		$html .= '		<div class="clear"></div>';
		$html .= '	</div>';
	}
	
	print $html;
    exit;