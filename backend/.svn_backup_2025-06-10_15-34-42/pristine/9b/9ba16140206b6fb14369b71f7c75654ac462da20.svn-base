<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AC' => 'Enez Ascension',
  'AD' => 'Andorra',
  'AE' => 'Emirelezhioù Arab Unanet',
  'AF' => 'Afghanistan',
  'AG' => 'Antigua ha Barbuda',
  'AI' => 'Anguilla',
  'AL' => 'Albania',
  'AM' => 'Armenia',
  'AO' => 'Angola',
  'AQ' => 'Antarktika',
  'AR' => 'Arcʼhantina',
  'AS' => 'Samoa Amerikan',
  'AT' => 'Aostria',
  'AU' => 'Aostralia',
  'AW' => 'Aruba',
  'AX' => 'Inizi Åland',
  'AZ' => 'Azerbaidjan',
  'BA' => 'Bosnia ha Herzegovina',
  'BB' => 'Barbados',
  'BD' => 'Bangladesh',
  'BE' => 'Belgia',
  'BF' => 'Burkina Faso',
  'BG' => 'Bulgaria',
  'BH' => 'Bahrein',
  'BI' => 'Burundi',
  'BJ' => 'Benin',
  'BL' => 'Saint Barthélemy',
  'BM' => 'Bermuda',
  'BN' => 'Brunei',
  'BO' => 'Bolivia',
  'BQ' => 'Karib Nederlandat',
  'BR' => 'Brazil',
  'BS' => 'Bahamas',
  'BT' => 'Bhoutan',
  'BW' => 'Botswana',
  'BY' => 'Belarus',
  'BZ' => 'Belize',
  'CA' => 'Kanada',
  'CC' => 'Inizi Kokoz',
  'CD' => 'Kongo - Kinshasa',
  'CF' => 'Republik Kreizafrikan',
  'CG' => 'Kongo - Brazzaville',
  'CH' => 'Suis',
  'CI' => 'Aod an Olifant',
  'CK' => 'Inizi Cook',
  'CL' => 'Chile',
  'CM' => 'Kameroun',
  'CN' => 'Sina',
  'CO' => 'Kolombia',
  'CR' => 'Costa Rica',
  'CU' => 'Kuba',
  'CV' => 'Kab-Glas',
  'CW' => 'Curaçao',
  'CX' => 'Enez Christmas',
  'CY' => 'Kiprenez',
  'CZ' => 'Republik Tchek',
  'DE' => 'Alamagn',
  'DG' => 'Diego Garcia',
  'DJ' => 'Djibouti',
  'DK' => 'Danmark',
  'DM' => 'Dominica',
  'DO' => 'Republik Dominikan',
  'DZ' => 'Aljeria',
  'EA' => 'Ceuta ha Melilla',
  'EC' => 'Ecuador',
  'EE' => 'Estonia',
  'EG' => 'Egipt',
  'EH' => 'Sahara ar Cʼhornôg',
  'ER' => 'Eritrea',
  'ES' => 'Spagn',
  'ET' => 'Etiopia',
  'FI' => 'Finland',
  'FJ' => 'Fidji',
  'FK' => 'Inizi Falkland',
  'FM' => 'Mikronezia',
  'FO' => 'Inizi Faero',
  'FR' => 'Frañs',
  'GA' => 'Gabon',
  'GB' => 'Rouantelezh-Unanet',
  'GD' => 'Grenada',
  'GE' => 'Jorjia',
  'GF' => 'Gwiana cʼhall',
  'GG' => 'Gwernenez',
  'GH' => 'Ghana',
  'GI' => 'Jibraltar',
  'GL' => 'Greunland',
  'GM' => 'Gambia',
  'GN' => 'Ginea',
  'GP' => 'Gwadeloup',
  'GQ' => 'Ginea ar Cʼheheder',
  'GR' => 'Gres',
  'GS' => 'Inizi Georgia ar Su hag Inizi Sandwich ar Su',
  'GT' => 'Guatemala',
  'GU' => 'Guam',
  'GW' => 'Ginea-Bissau',
  'GY' => 'Guyana',
  'HK' => 'Hong Kong RMD Sina',
  'HN' => 'Honduras',
  'HR' => 'Kroatia',
  'HT' => 'Haiti',
  'HU' => 'Hungaria',
  'IC' => 'Inizi Kanariez',
  'ID' => 'Indonezia',
  'IE' => 'Iwerzhon',
  'IL' => 'Israel',
  'IM' => 'Enez Vanav',
  'IN' => 'India',
  'IO' => 'Tiriad breizhveurat Meurvor Indez',
  'IQ' => 'Iraq',
  'IR' => 'Iran',
  'IS' => 'Island',
  'IT' => 'Italia',
  'JE' => 'Jerzenez',
  'JM' => 'Jamaika',
  'JO' => 'Jordania',
  'JP' => 'Japan',
  'KE' => 'Kenya',
  'KG' => 'Kyrgyzstan',
  'KH' => 'Kambodja',
  'KI' => 'Kiribati',
  'KM' => 'Komorez',
  'KN' => 'Saint Kitts ha Nevis',
  'KP' => 'Korea an Norzh',
  'KR' => 'Korea ar Su',
  'KW' => 'Koweit',
  'KY' => 'Inizi Cayman',
  'KZ' => 'Kazakstan',
  'LA' => 'Laos',
  'LB' => 'Liban',
  'LC' => 'Saint Lucia',
  'LI' => 'Liechtenstein',
  'LK' => 'Sri Lanka',
  'LR' => 'Liberia',
  'LS' => 'Lesotho',
  'LT' => 'Lituania',
  'LU' => 'Luksembourg',
  'LV' => 'Latvia',
  'LY' => 'Libia',
  'MA' => 'Maroko',
  'MC' => 'Monaco',
  'MD' => 'Moldova',
  'ME' => 'Montenegro',
  'MF' => 'Saint Martin',
  'MG' => 'Madagaskar',
  'MH' => 'Inizi Marshall',
  'MK' => 'Makedonia',
  'ML' => 'Mali',
  'MM' => 'Myanmar (Birmania)',
  'MN' => 'Mongolia',
  'MO' => 'Macau RMD Sina',
  'MP' => 'Inizi Mariana an Norzh',
  'MQ' => 'Martinik',
  'MR' => 'Maouritania',
  'MS' => 'Montserrat',
  'MT' => 'Malta',
  'MU' => 'Moris',
  'MV' => 'Maldivez',
  'MW' => 'Malawi',
  'MX' => 'Mecʼhiko',
  'MY' => 'Malaysia',
  'MZ' => 'Mozambik',
  'NA' => 'Namibia',
  'NC' => 'Kaledonia Nevez',
  'NE' => 'Niger',
  'NF' => 'Enez Norfolk',
  'NG' => 'Nigeria',
  'NI' => 'Nicaragua',
  'NL' => 'Izelvroioù',
  'NO' => 'Norvegia',
  'NP' => 'Nepal',
  'NR' => 'Nauru',
  'NU' => 'Niue',
  'NZ' => 'Zeland-Nevez',
  'OM' => 'Oman',
  'PA' => 'Panamá',
  'PE' => 'Perou',
  'PF' => 'Polinezia Cʼhall',
  'PG' => 'Papoua Ginea-Nevez',
  'PH' => 'Filipinez',
  'PK' => 'Pakistan',
  'PL' => 'Polonia',
  'PM' => 'Sant-Pêr-ha-Mikelon',
  'PN' => 'Enez Pitcairn',
  'PR' => 'Puerto Rico',
  'PS' => 'Tiriadoù Palestina',
  'PT' => 'Portugal',
  'PW' => 'Palau',
  'PY' => 'Paraguay',
  'QA' => 'Qatar',
  'RE' => 'Ar Reünion',
  'RO' => 'Roumania',
  'RS' => 'Serbia',
  'RU' => 'Rusia',
  'RW' => 'Rwanda',
  'SA' => 'Arabia Saoudat',
  'SB' => 'Inizi Salomon',
  'SC' => 'Sechelez',
  'SD' => 'Soudan',
  'SE' => 'Sveden',
  'SG' => 'Singapour',
  'SH' => 'Saint-Helena',
  'SI' => 'Slovenia',
  'SJ' => 'Svalbard',
  'SK' => 'Slovakia',
  'SL' => 'Sierra Leone',
  'SM' => 'San Marino',
  'SN' => 'Senegal',
  'SO' => 'Somalia',
  'SR' => 'Surinam',
  'SS' => 'Susoudan',
  'ST' => 'São Tomé ha Príncipe',
  'SV' => 'Salvador',
  'SX' => 'Sint Maarten',
  'SY' => 'Siria',
  'SZ' => 'Swaziland',
  'TA' => 'Tristan da Cunha',
  'TC' => 'Inizi Turks ha Caicos',
  'TD' => 'Tchad',
  'TF' => 'Douaroù aostral Frañs',
  'TG' => 'Togo',
  'TH' => 'Thailand',
  'TJ' => 'Tadjikistan',
  'TK' => 'Tokelau',
  'TL' => 'Timor-Leste',
  'TM' => 'Turkmenistan',
  'TN' => 'Tunizia',
  'TO' => 'Tonga',
  'TR' => 'Turkia',
  'TT' => 'Trinidad ha Tobago',
  'TV' => 'Tuvalu',
  'TW' => 'Taiwan',
  'TZ' => 'Tanzania',
  'UA' => 'Ukraina',
  'UG' => 'Ouganda',
  'UM' => 'Inizi diabell ar Stadoù-Unanet',
  'US' => 'Stadoù-Unanet',
  'UY' => 'Uruguay',
  'UZ' => 'Ouzbekistan',
  'VA' => 'Vatikan',
  'VC' => 'Sant Visant hag ar Grenadinez',
  'VE' => 'Venezuela',
  'VG' => 'Inizi Gwercʼh Breizh-Veur',
  'VI' => 'Inizi Gwercʼh ar Stadoù-Unanet',
  'VN' => 'Viêt Nam',
  'VU' => 'Vanuatu',
  'WF' => 'Wallis ha Futuna',
  'WS' => 'Samoa',
  'XK' => 'Kosovo',
  'YE' => 'Yemen',
  'YT' => 'Mayotte',
  'ZA' => 'Suafrika',
  'ZM' => 'Zambia',
  'ZW' => 'Zimbabwe',
);
