<?php

require_once('Export/Exception/InvoiceExportException.php');

use \Export\Exception\InvoiceExportException;

/**
 * Export d'une facture
 *
 * @param int $ord_id Identifiant de la commande facturée
 * @param int $inv_id Identifiant de la facture
 * @param string $output_format Format de sortie
 * @param array|null & $options Liste des options
 * @param array|null & $ord Données de la commande (au format ord_orders_get_simple) pour optimisation
 *
 * @return void
 */
function export_invoice( $ord_id, $inv_id, $output_format, array &$options = null, array & $ord = null, array & $invoice = null ){
    $data = array();

    $data['ord'] = $ord;
    // Charge la commande si elle n'a pas déjà été fournie en paramètre
    if( is_null($data['ord']) ){
        $r_ord = ord_orders_get_simple(array('id' => $ord_id));
        if( !$r_ord || !ria_mysql_num_rows($r_ord) ){
            throw new InvoiceExportException($ord_id, $inv_id, 'Commande non trouvée');
        }
        $data['ord'] = ria_mysql_fetch_assoc($r_ord);
    }

    // Si demandé, vérifie que le statut de la commande fait partie des status autorisés
    if( isset($options['check_status']) && $options['check_status']
        && isset($options['required_statuses']) && is_array($options['required_statuses'])
        && !in_array(
            intval($data['ord']['state_id']),
            $options['required_statuses']
        )
    ){
        throw new InvoiceExportException($data['ord']['id'], $inv_id, 'Le statut de la commande ne convient pas');
    }

    // Charge la facture si elle n'a pas déjà été fournie en paramètre
    $data['invoice'] = null;
    if (is_null($data['invoice'])) {
        $r_inv = ord_invoices_get($inv_id);

        if( !$r_inv || !ria_mysql_num_rows($r_inv) ){
            throw new InvoiceExportException($data['ord']['id'], $inv_id, 'Facture non trouvée');
        }

        $data['invoice'] = ria_mysql_fetch_assoc($r_inv);
    }

    $r_inv_prd = ord_inv_products_get($data['invoice']['id'], false, false, null, null, $data['ord']['id']);

    $data['inv_products'] = array();
    $data['ecotaxe'] = array('base' => 0, 'amount' => 0);
    $data['tva'] = array();
    if (false !== $r_inv_prd && ria_mysql_num_rows($r_inv_prd) > 0) {
        while ($inv_prd = ria_mysql_fetch_assoc($r_inv_prd)) {
            $inv_prd['label'] = export_invoice_product_name($inv_prd);
            $data['inv_products'][] = $inv_prd;


			if ($inv_prd['ecotaxe'] > 0) {
				$eco_total_ht = $inv_prd['ecotaxe'] * $inv_prd['qte'];
                $eco_total_ttc = $eco_total_ht * _TVA_RATE_DEFAULT;

                $data['ecotaxe']['base'] += $eco_total_ht;
                $data['ecotaxe']['amount'] += $eco_total_ttc - $eco_total_ht;
			}

			$total_ht = $inv_prd['total_ht'];
			$total_ttc = $inv_prd['total_ttc'];
			if (isset($inv_prd['discount_type']) && isset($inv_prd['discount'])){
				if ($inv_prd['discount_type'] === "0"){ // Euros
					$total_ht = $inv_prd['total_ht'] - $inv_prd['discount'];
                    $total_ttc = $inv_prd['total_ttc'] - ($inv_prd['discount'] * $inv_prd['tva_rate']);
				} else { // %
					$total_ht = $inv_prd['total_ht'] * (1-($inv_prd['discount']/100));
                    $total_ttc = $inv_prd['total_ttc'] * (1-($inv_prd['discount']/100));
				}
            }

            if (!isset($data['tva'][$inv_prd['tva_rate']])) {
                $data['tva'][$inv_prd['tva_rate']] = array('base' => 0, 'amount' => 0);
            }

            $data['tva'][$inv_prd['tva_rate']]['base'] += $total_ht;
            $data['tva'][$inv_prd['tva_rate']]['amount'] += $total_ttc - $total_ht;
        }
    }

    if( !isset($data['invoice']['usr_id']) ){
        throw new InvoiceExportException($data['ord']['id'], $data['invoice']['id'], 'Aucun utilisateur lié à la facture');
    }

    $r_user = gu_users_get($data['invoice']['usr_id']);
    $data['user'] = null;
    if( $r_user === false || ria_mysql_num_rows($r_user) <= 0 ){
        throw new InvoiceExportException($data['ord']['id'], $data['invoice']['id'], 'Utilisateur non trouvé');
    }

    $data['user'] = ria_mysql_fetch_assoc($r_user);

    $data['owner'] = site_owner_get();

    $data['addresses'] = ord_orders_address_load($data['ord']);
    $data['addresses']['invoice']['type'] = 'facturation';
    $data['addresses']['delivery']['type'] = 'livraison';

    $data['pay_name'] = '';
    $data['installments'] = array();
    $r_installments = ord_installments_get(0, 1, $data['ord']['id']);
    if ($r_installments && ria_mysql_num_rows($r_installments)) {
        while ($installement = ria_mysql_fetch_assoc($r_installments)) {
            $r_pay = gu_users_payment_types_get(0, $installement['pay']);
            $installement['pay_name'] = '';
            if ($r_pay && ria_mysql_num_rows($r_pay)) {
                $pay = ria_mysql_fetch_assoc($r_pay);
                $installement['pay_name'] = $pay['name'];
                $data['pay_name'] = gu_users_payment_types_view($pay);
            }
            $data['installments'][] = $installement;
        }
    }

    if (trim($data['pay_name']) == '') {
        $r_pay = false;
        if ($data['ord']['pay_id'] <= 0) {
            $r_pay = gu_users_payment_types_get($data['user']['id']);
        } else {
            $r_pay = gu_users_payment_types_get(0, $data['ord']['pay_id']);
        }

        if ($r_pay && ria_mysql_num_rows($r_pay)) {
            $pay = ria_mysql_fetch_assoc($r_pay);
            $data['pay_name'] = gu_users_payment_types_view($pay);
        }
    }

    switch( $output_format ){
        case 'excel':
            return export_invoice_excel($data, $options);
        case 'pdf':
            return export_invoice_pdf($data, $options);
    }
}

function export_invoice_product_name( array & $inv_prd){
    $comment = '';
    if (isset($inv_prd['notes']) && $inv_prd['notes']) {
        $comment = '
					Notes : ' . $inv_prd['notes'];
    }

    return $inv_prd['name'] . $comment;
}

/**
 * Export d'une facture au format PDF
 *
 * @param array & $data Données de la facture
 * @param array|null & $options Options de génération du PDF
 *
 * @return mixed Facture formatée
 */
function export_invoice_pdf( array & $data, array & $options = null ){
    require_once('Pdf/InvoicePdf.php');
    $pdf = new Pdf\InvoicePdf($data['invoice']);

    if(!is_null($options)){
         $pdf->setOptions($options);
    }

    $pdf->setData($data);

    if($data ['ord']['pay_id'] == _PAY_VIREMENT){
        $pdf->setDisplayBankInfo();
    }

    $pdf->table()->withBody($data['inv_products']);
    $pdf->defaultProductTable();

    $filename = !isset($options['filename']) || trim($options['filename']) == ''
        ? $data['ord']['id'] . '.pdf'
        : $options['filename'];

    $output = isset($options['output']) && !is_null($options['output']) ? $options['output'] : 'D';

    $pdf->headerOnAllPages(false)
        ->showTaxcode(false)
        ->addTotalPage()
        ->generate($filename, $output);
}