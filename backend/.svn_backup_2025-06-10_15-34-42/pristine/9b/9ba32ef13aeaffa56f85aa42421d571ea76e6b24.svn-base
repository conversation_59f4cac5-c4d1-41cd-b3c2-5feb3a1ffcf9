<?php
/**
 * \defgroup orders_get-list Entête
 * \ingroup orders 
 *	@{	
 * 	\page api-orders-index-get-list Chargement
 *
 *	Cette fonction récupère un entête de commande complet 
 *
 *		\code
 *			GET /orders/get-list/
 *		\endcode
 *
 * @param int $id Facultatif, Identifiant de la commande ou tableau d'identifiants de commandes
 * @param int $usr Facultatif, Identifiant d'un compte client sur lequel filtrer le résultat
 * @param bool $masked Facultatif, permet la récupération de commandes masquées (elles sont filtrées par défaut)
 * @param int $state Facultatif, permet la crétation d'un filtre sur les statuts des commandes 
 * @param int $start Facultatif, pagination : enregistrement de départ à retourner (attention, pas encore implémenté)
 * @param int $limit Facultatif, pagination : nombre maximum de comptes à retourner
 *
 * @return json La ou les commandes demandées au format json avec les colonnes suivantes :
 * 		\code{.json}
 *		       {
 *					"user" : identifiant du client ayant passé la commande
 *					"id" : Identifiant de la commande
 *					"ref" : Référence de la commande (identifiant complété par des 0, 8 caractères)
 *					"piece" : numéro de pièce dans la gestion commerciale
 *					"date" : date/heure de la commande
 *					"date_en" : date au format US
 *					"state_id" : identifiant du statut de commande
 *					"state_name" : libellé du statut de commande
 *					"products" : nombre d'articles contenus dans la commande
 *					"total_ht" : montant total de la commande, hors taxes
 *					"total_ttc" : montant total de la commande, ttc
 *					"adr_invoices" : identifiant de l'adresse de facturation
 *					"adr_delivery" : identifiant de l'adresse de livraison
 *					"srv_id" : identifiant du service de livraison demandé par le client
 *					"str_id" : identifiant du magasin dans lequel le client souhaite être livré
 *					"dlv-notes" : consignes de livraison
 *					"pmt_id" : identifiant d'un code promotion appliqué à la commande
 *					"seller_id" : identifiant du représentant ayant enregistré la commande
 *					"comments" : commentaires du représentant ayant enregistré la commande
 *					"cnt_id" : identifiant de la commande dans le moteur de recherche
 *					"ord_livr" : date de livraison prévue de la commande
 *					"nsync" : détermine si la commande nécessite une synchronisation avec la gestion commerciale
 *					"need_sync" : détermine si la commande nécessite une synchronisation avec la gestion commerciale
 *					"total_ht_delivered" : montant total des articles non livrés de la commande
 *					"dps_id" : dépôt de livraison de la commande (commandes fournisseurs)
 *					"pkg_id" : package de livraison de la commande (commandes fournisseurs)
 *					"age" : Age de la commande
 *					"relanced" : Détermine si le client de la commande doit être relancé pour paiement complémentaire (Atos / Bigship uniquement)
 *					"alert-livr" : Détermine si une alerte email pour les produits en rupture de la commande doit être envoyée (Bigship uniquement)
 *					"rly_id" : Identifiant du point-relais de livraison
 *					"parent" : Identifiant du compte parent, si la commande est liée à un compte enfant (à ignorer si la variable de configuration "parent_is_order_holder" n'est pas activée)
 *					"wst_id" : Identifiant du site pour lequel la commande a été passée
 *					"date_archived" : Date d'archivation
 *					"date_modified" : Date de modification
 *					"state_sage" : non utilisé
 *					"opt-gift" : option cadeau activée oui / non
 *					"opt-gift-message" : message de l'option cadeau
 *					"pay_id" : identifiant du moyen de paiement de la commande
 *					"card_id" : identifiant du type de CB de la commande
 *					"reliquats" : est un reliquat oui / non
 *					"ord_livr_fr" : date de livraison prévue au format FR
 *					"date_modified_fr" : date de dernière modification au format FR
 *					"masked" : commande masquée oui / non
 *					"parent_id" : identifiant de la commande parent
 *       		},
 *		\endcode
*/

switch( $method ){
	// récupération d'une commande
	case 'get':

		// Filtre sur l'identifiant de commande
		$ids = 0;
		if( isset($_GET['id']) && is_array($_GET['id']) ){
			foreach( $_GET['id'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants fournis en arguments sont incorrects");
				}
			}
			$ids = $_GET['id'];
		}elseif( isset($_GET['id']) && is_numeric($_GET['id']) ){
			$ids = $_GET['id'];
		}

		// Filtre sur le compte client
		$usr = 0;
		if( isset($_GET['usr']) && is_numeric($_GET['usr']) ){
			$usr = $_GET['usr'];
		}

		// Filtre sur le status de commande
		$states = 0;
		if( isset($_GET['state']) && is_array($_GET['state']) ){
			foreach( $_GET['state'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants fournis en arguments sont incorrects");
				}
			}
			$states = $_GET['state'];
		}elseif( isset($_GET['state']) && is_numeric($_GET['state']) ){
			$states = $_GET['state'];
		}

		// paramètre start
		$start = 0;
		if( isset($_GET['start']) && is_numeric($_GET['start']) ){
			$start = $_GET['start'];
		}

		// paramètre limit
		$limit = 0;
		if( isset($_GET['limit']) && is_numeric($_GET['limit']) ){
			$limit = $_GET['limit'];
		}

		// Filtre sur l'état masqué / non masqué
		$masked = isset($_GET['masked']) && $_GET['masked']=='1';

		$array = array();
		$rord = ord_orders_get(
			$usr, $ids, $states, $limit, null, false, false, false, false, false, false, '', false, false, false, false, false, false, false, false, $masked,
			0, 0, 0, 0, 0
		);
		while($ord = ria_mysql_fetch_assoc($rord)){
			unset( $ord['tenant'] );
			$array[] = $ord;
		}

		$result = true;
		$content = $array;

		break;
}
///@}