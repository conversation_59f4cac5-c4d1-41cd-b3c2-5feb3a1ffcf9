<?php

	/**	\file index.php
	 *	Ce fichier affiche la liste des appareils Yuto connectés ou en attente de connexion à l'instance.
	 *	Le contenu de cet écran différe en fonction des droits d'accès : les supers-administrateurs voient
	 *	tous les appareils, les administrateurs ne voient que les administrateurs (pas les supers-administrateurs).
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_FDV_DEVICE');

	unset($error);
	require_once('devices.inc.php');

	$author = isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] > 0 ? $_GET['author'] : 0;
	$doublon = isset($_GET['doublon']) && $_GET['doublon'] ? true : false;
    $client_status = isset($_GET['status']) && $_GET['status'] ? $_GET['status'] : '';

	// Gère l'export au format CSV/Excel
	if( isset($_REQUEST["exportParc"]) ){
		header('Location: export.php?author='.$author);
		//exit;
	}

	// Bouton Configuration (Forcer les appareils à resynchroniser leurs paramètres de configuration)
	if( isset($_POST['refresh-config']) ){
		cfg_variable_delete_cache();
	}

	// Envoi de requêtes SQL
	if( isset($_POST['btn-sql'], $_POST['sql']) && !gu_users_is_tenant_linked($_SESSION["usr_id"], $_SESSION["usr_email"] ) ){

		$sql_parts = preg_split('/;\s*[\n|\r\n]?/', $_POST['sql']);

		foreach( $_POST['dev'] as $one_dev ){

			$rdevice = dev_devices_get( $one_dev );
			if( !$rdevice || !ria_mysql_num_rows($rdevice) ){
				continue;
			}

			$device = ria_mysql_fetch_assoc( $rdevice );

			if( sizeof($sql_parts) ){
				$sqls = array();
				foreach( $sql_parts as $sql_one ){
					$sql_one = str_replace(PHP_EOL, ' ', $sql_one);
					if( strlen($sql_one) > 0 ){
						$sqls[] = $sql_one;
					}
				}
				if( sizeof($sqls) ){
					$dests = isset($_POST['sql-dest']) ? explode("\r", $_POST['sql-dest']) : array();

					if( $device['version'] >= 253 || ($device['brand']=='Apple' && $device['version'] >= 99) ){
						
						foreach( $sqls as $s ){
							if( !dev_send_notifications($device["id"], '', '', $data=array('cls_id'=>-1, 'receivers' => implode(',', $dests), 'query' => $s)) ){
								$error = _('Impossible d\'envoyer les requêtes.');
							}else{
								$success = _('La requete à été envoyé.');
							}
						}

					}else{

						$xml = '<?xml version="1.0" encoding="utf-8"?><xml><receivers>';
						foreach( $dests as $d ){
							$xml .= '<receiver>'.xmlentities($d).'</receiver>';
						}
						$xml .= '</receivers><queries>';
						foreach( $sqls as $s ){
							$xml .= '<query>'.xmlentities($s).'</query>';
						}
						$xml .= '</queries></xml>';
						$dir = dirname(__FILE__).'/../../../api/debug/';
						if( file_put_contents( $dir.md5($config["tnt_id"].$device["id"].$device["date_created_en"]).'-'.time().'.xml', $xml )===false ){
							$error = _('Impossible de créer le fichier de requêtes.');
						}else{
							$success = _('Le fichier est en attente d\'exécution par la tablette.');
						}
					}

				}
			}
		}

		if( !isset($error) ){
			header('Location: /admin/fdv/devices/index.php');
			exit;
		}

	}

	// Boutons super-administrateurs :
	// - Configuration
	// - Modèles
	// - Restrictions
	// - Etat de commandes
	// - Ecran de synchronisation
	if( isset($_POST) ){
		if( isset($_POST['dev']) && is_array($_POST['dev']) && sizeof($_POST['dev']) ){
			foreach( $_POST['dev'] as $one_dev ){
				if( isset($_POST['refresh-config']) ){ // Configuration
					dev_devices_need_sync_add($one_dev,_DEV_TASK_CFG);
				}
				if( isset($_POST['refresh-model']) ){ // Modèles
					dev_devices_need_sync_add($one_dev,_DEV_TASK_MODEL);
				}
				if( isset($_POST['refresh-restriction']) ){ // Restrictions
					dev_devices_need_sync_add($one_dev,_DEV_TASK_RESTRICTIONS);
				}
				if( isset($_POST['refresh-screen']) ){ // Ecran de synchronisation
					dev_devices_need_sync_add($one_dev,_DEV_TASK_NEED_SYNC);
				}
				if( isset($_POST['refresh-states']) ){ // Etat de commandes
					dev_devices_need_sync_add($one_dev,_DEV_TASK_ORD_STATES);
				}
			}
		}
	}

	// Activation d'un ou plusieurs appareils
	if( isset($_POST['active']) ){

		// Calcule le nombre d'appareils actuellement actifs
		$total_devices = NULL;
		
		// On récupère le nombre d'apparails actuellement connecté
		$devices = dev_devices_get( 0, 0, '', -1, '=', false, false, true, true, false, false, false );
		if( $devices ){
			$total_devices = 0;
			while( $device = ria_mysql_fetch_assoc($devices) ){
				// Ne prend pas en compte les appareils rattachés à un client super-admin
				if( $device['usr_tnt_id'] == $config['tnt_id'] ){
					$total_devices++;
				}
			}
			ria_mysql_data_seek( $devices, 0 );
		}

		// Récupère le nombre d'appareils maximum autorisés
		$max_devices = dev_subscribtions_get_max();

		// Procède à l'activation tout en contrôlant :
		// - que les appareils super-administrateurs ne sont pas décomptés
		// - que le nombre d'appareils actifs simultanément n'est pas atteint
		if( isset($_POST['dev']) && is_array($_POST['dev']) && sizeof($_POST['dev']) ){
			foreach( $_POST['dev'] as $one_dev ){

				$dev_usr_id = dev_devices_get_user($one_dev);

				$active = false;
				if( !gu_users_is_tenant_linked($dev_usr_id) ){
					$active = true;
				}else{
					if( $max_devices>0 && $max_devices<=$total_devices ){
						$error = sprintf(_("Vous avez atteint le maximum de %d appareils autorisés par votre abonnement.")."\n"._("Merci de prendre contact avec nous."), $max_devices);
						break;
					}else{
						$active = true;
					}

					// si l'appareil est déjà activé alors il est déjà compté dans le total
					if( !dev_devices_is_activated($one_dev) ){
						$total_devices++;
					}
				}

				if( $active && !dev_devices_activate($one_dev) ){
					$error = _("Une erreur inattendue s'est produite lors de l'activation des appareils.")."\n"._("Merci de réessayer ou prendre contact pour nous signaler ce problème.");
					break;
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['success-active-device'] = sizeof( $_POST['dev'] );
			header('Location: /admin/fdv/devices/index.php');
			exit;
		}
	}

	// Procède à la désactivation d'un ou plusieurs appareils
	if( isset($_POST['deactive']) ){
		if( isset($_POST['dev']) && is_array($_POST['dev']) && sizeof($_POST['dev']) ){
			foreach( $_POST['dev'] as $one_dev ){
				if( !dev_devices_deactivate($one_dev) ){
					$error = _("Une erreur inattendue s'est produite lors de la désactivation des appareils.")."\n"._("Merci de réessayer ou prendre contact pour nous signaler ce problème.");
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['success-deactive-device'] = sizeof( $_POST['dev'] );
			header('Location: /admin/fdv/devices/index.php');
			exit;
		}
	}

	// Procède à la suppression compte d'un ou plusieurs appareils
	if( isset($_POST['delete']) ){
		if( isset($_POST['dev']) && is_array($_POST['dev']) && sizeof($_POST['dev']) ){
			foreach( $_POST['dev'] as $one_dev ){
				if( !dev_devices_del($one_dev) ){
					$error = _("Une erreur inattendue s'est produite lors de la suppression des appareils.")."\n"._("Merci de réessayer ou prendre contact pour nous signaler ce problème.");
				}
			}
		}

		if( !isset($error) ){
			$_SESSION['success-delete-device'] = sizeof( $_POST['dev'] );
			header('Location: /admin/fdv/devices/index.php');
			exit;
		}
	}

	// Chargement des données
	if( !isset($_REQUEST['sort']) ){
		$_GET['sort'] = $_GET['dir'] = $_REQUEST['sort'] = $_REQUEST['dir'] = '';
	}
	$sort = array();
	if( isset($_GET['sort']) && $_GET['sort']!='' && isset($_GET['dir']) ){
		$sort = array( $_GET['sort']=>$_GET['dir'] );
	}

	// Les administrateurs ne voient que les devices administrateurs ou utilisateurs, mais pas les devices super-administrateurs
	$rdevice = dev_devices_get( 0, $author, '', -1, '=', false, false, null, false, 0, $sort, true, $doublon, $client_status );
	$count_report = $rdevice ? ria_mysql_num_rows( $rdevice ) : 0;

	// Compte le nombre de devices actifs
	$ractivated = dev_devices_get( 0, 0, '', -1, '=', false, false, true, true );
	$count_activated = $ractivated ? ria_mysql_num_rows( $ractivated ) : 0;

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Gestion de parc / Flotte d\'appareils') . ' - Yuto');
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print _('Gestion de parc / Flotte d\'appareils'); ?> (<?php printf( _('%d appareils activés'), ria_number_format($count_activated) ); ?>)</h2>

	<div class="notice">
		<p><?php print _('Retrouvez ci-dessous tous les appareils sur lesquels l\'application est installée.').' '._('Un guide d\'installation est disponible sur la page suivante :')?> <a href="setup-guide.php"><?php print _('Guide d\'installation')?></a></p>
	</div>

	<?php

		// Prépare les messages de succès
		if( isset($_SESSION['success-active-device']) ){
			$success = _('Les appareils ont bien été activés.');
			if( is_numeric($_SESSION['success-active-device']) && $_SESSION['success-active-device'] == 1 ){
				$success = _('L\'appareil a bien été activé.');
			}

			unset( $_SESSION['success-active-device'] );
		}elseif( isset($_SESSION['success-deactive-device']) ){
			$success = _('Les appareils ont bien été désactivés.');
			if( is_numeric($_SESSION['success-deactive-device']) && $_SESSION['success-deactive-device'] == 1 ){
				$success = _('L\'appareil a bien été désactivé.');
			}

			unset( $_SESSION['success-deactive-device'] );
		}elseif( isset($_SESSION['success-delete-device']) ){
			$success = _('Les appareils ont bien été supprimés.');
			if( is_numeric($_SESSION['success-delete-device']) && $_SESSION['success-delete-device'] == 1 ){
				$success = _('L\'appareil a bien été supprimé.');
			}

			unset( $_SESSION['success-delete-device'] );
		}

		// Affiche les messages de succès ou d'erreur
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}elseif( isset($success) ){
			print '<div class="success">'.$success.'</div>';
		}
	?>

	<form action="/admin/fdv/devices/index.php" method="post">
		<input type="hidden" id="author_id" name="author" value="<?php print $author; ?>" />
		<input type="hidden" name="doublon" value="<?php print $doublon; ?>" />
		<input type="hidden" name="status" value="<?php print $client_status; ?>" />
		<table id="tb-liste-fabricant" class="checklist tablesorter">
			<?php

				// Converti le résultat de requête en tableau associatif
				$devices = array();
				while ( $device = ria_mysql_fetch_assoc($rdevice) ) {
					$devices[] = $device;
				}

				// Colonnes du tableau
				$columns = array();

				// Fabricant, modèle
				$columns[] = array(
					'id' => 'dev-key',
					'code' => 'name',
					'name' => _('Fabricant, modèle'),
					'align' => 'left',
					'sortable' => true,
					'title' => ''
				);

				// Version de Yuto
				$columns[] = array(
					'id' => 'dev-version',
					'code' => 'version',
					'name' => _('Version'),
					'align' => 'left',
					'sortable' => true,
					'title' => ''
				);

				// Nom d'utilisateur
				$columns[] = array(
					'id' => 'dev-user',
					'code' => 'user',
					'name' => _('Utilisateur'),
					'align' => 'left',
					'sortable' => true,
					'title' => ''
				);

				// Dernière synchro
				$columns[] = array(
					'id' => 'dev-last-sync',
					'code' => 'last-sync',
					'name' => _('Dernière synchro'),
					'align' => 'left',
					'sortable' => true,
					'title' => ''
				);

				// Affiche le sélecteur d'utilisateur, mais uniquement s'il y a suffisamment d'utilisateurs pour que ce soit pertinent
				$rall_author = dev_devices_get_all_user();
				if( isset($_GET['author']) || isset($_GET['doublon']) || isset($_GET['status']) || ($count_report>10 && $rall_author && ria_mysql_num_rows($rall_author)) ){
			?>
					<div class="stats-menu">
						<div id="riadatepicker"></div>
						<div class="riapicker" id="select-fdv-devices-user">
							<div class="selectorview">
								<div class="left">
									<span class="function_name"><?php print _('Utilisateur'); ?></span>
									<br/><span class="view"><?php
										if( $author<=0 ){
											print _('Tous les utilisateurs');
										}else{
											$select_author 	= array( 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 );

											$r_select_author = gu_users_get( $author );
											if( $r_select_author && ria_mysql_num_rows($r_select_author) ){
												$select_author = ria_mysql_fetch_assoc( $r_select_author );
											}

											print trim( $select_author['adr_firstname'].' '.$select_author['adr_lastname'].' '.$select_author['society'] );
										}
									?></span>
								</div>
								<a name="btn" class="btn"><img alt="" src="/admin/images/stats/fleche.gif" class="fleche" /></a>
								<div class="clear"></div>
							</div>
							<div class="selector">
								<a name="author-0"><?php print _('Tous les utilisateurs')?></a>
								<?php
									while( $all_author = ria_mysql_fetch_assoc($rall_author) ){
										print '<a name="author-'.$all_author['id'].'">'.htmlspecialchars( trim($all_author['firstname'].' '.$all_author['lastname'].' '.$all_author['society']) ).'</a>';
									}
								?>
							</div>
						</div>
                        <?php if ($config['tnt_id'] == 105 && $config['wst_id'] == 154) {?>
                            <div class="riapicker" id="select-doublon-devices-user">
                                <div class="selectorview">
                                    <div class="left">
                                        <span class="function_name"><?php print _('Recherche des doublons'); ?></span>
                                        <br/>
                                        <span class="view">
                                            <?php if ($doublon) {
                                                print _('Afficher les doublons');
                                            } else {
                                                print _('Tous les utilisateurs');
                                            } ?>
                                        </span>
                                    </div>
                                    <a name="btn" class="btn"><img alt="" src="/admin/images/stats/fleche.gif" class="fleche" /></a>
                                    <div class="clear"></div>
                                </div>
                                <div class="selector">
                                    <a name=""><?php print _('Tous les utilisateurs'); ?></a>
                                    <a name="show-doublon"><?php print _('Afficher les doublons'); ?></a>
                                </div>
                            </div>

                            <div class="riapicker" id="select-status-user">
                                <div class="selectorview">
                                    <div class="left">
                                        <span class="function_name"><?php print _('Statut'); ?></span>
                                        <br/>
                                        <span class="view">
                                            <?php if (empty($client_status)) {
                                                print _('Tous');
                                            } else if ($client_status == 'permanent') {
                                                print _('Permanents');
                                            } else {
                                                print _('Non permanents');
                                            }
                                            ?>
                                        </span>
                                    </div>
                                    <a name="btn" class="btn"><img alt="" src="/admin/images/stats/fleche.gif" class="fleche" /></a>
                                    <div class="clear"></div>
                                </div>
                                <div class="selector">
                                    <a name=""><?php print _('Tous'); ?></a>
                                    <a name="permanent"><?php print _('Permanents'); ?></a>
                                    <a name="non-permanent"><?php print _('Non permanents'); ?></a>
                                </div>
                            </div>
                        <?php } ?>
						<div class="clear"></div>
					</div>
			<?php } ?>
			<thead>
				<tr>
					<th id="dev-checked"><input class="riacheckall" type="checkbox" name="checkall" value="" /></th>
					<?php
						foreach( $columns as $column ){
							$class = $column['sortable'] ? 'header ' : '';
							$class .= $_REQUEST['sort']==$column['code'] ? ( $_REQUEST['dir']=='asc' ?  'headerSortUp' : 'headerSortDown'  ) : '';
							$dir = $_REQUEST['sort']==$column['code'] && $_REQUEST['dir']=='asc' ? 'desc' : 'asc';
							print '<th id="'.$column['id'].'" class="'.$class.'" title="'.$column['title'].'">';
							if( $column['sortable'] ){
							    $html = '<a  href="index.php?';
							    if ($author) {
							        $html .= 'author='.$author.'&amp;';
                                } else if ($doublon) {
                                    $html .= 'doublon='.$doublon.'&amp;';
                                } else if ($client_status) {
                                    $html .= 'status='.$client_status.'&amp;';
                                }
							    $html .= 'sort='.$column['code'].'&amp;dir='.$dir.'">';
								print $html;
							}
							print htmlspecialchars( $column['name'] );
							if( $column['sortable'] ){
								print '</a>';
							}
							print '</th>';
						}
					?>
					<th id="dev-last-location"><?php print _('Dernière localisation'); ?></th>
				</tr>
			</thead>
			<tbody><?php

				if( !$rdevice || !ria_mysql_num_rows($rdevice) ){
					// Aucun appareil à afficher dans la liste
					print '<tr><td colspan="6">'._('Aucun appareil enregistré').'</td></tr>';
				}else{

					// Affiche la liste des appareils
					foreach( $devices as $device ){

						$user_name = '';

						// Charge le compte utilisateur associé à l'appareil
						$row_style = '';
						$ruser = gu_users_get( $device['usr_id'] );
						if( $ruser && ria_mysql_num_rows($ruser) ){
							$user = ria_mysql_fetch_assoc( $ruser );

							$user_name = '
								<a href="/admin/customers/edit.php?usr='.$user['id'].'">'.view_usr_is_sync( $user ).' '.trim( htmlspecialchars($user['adr_firstname'].' '.$user['adr_lastname'].' '.$user['society']) ).'</a>
							';
							if( $user['tenant']==0 ){ // Les tablettes associées à des super-administrateurs sont affichées dans une couleur différente
								$row_style = ' class="bg-pink"';
							}
						}
						$device_name = (isset($device['brand']) || isset($device['model'])) ? $device['brand'].' '.$device['model'] : $device['key'];

						$device['date_last_call'] = $device['date_last_call'] == null ? $device['date_created_en'] : $device['date_last_call'];

						// L'affichage de la cellule "Dernier signal" dépend de la date de dernier signal
						// Si trop ancienne, elle est mise en valeur pour attirer l'attention.
						$signal_alert_class = 'alive';
						if( strtotime($device['date_last_call']." +4 HOUR") < strtotime(date('Y-m-d H:i:s')) ){
							$signal_alert_class = 'dead';
						}else if( strtotime($device['date_last_call']." +1 HOUR") < strtotime(date('Y-m-d H:i:s')) ){
							$signal_alert_class = 'lost';
						}

						// L'affichage de la cellule "Dernière synchronisation" dépend de la date de dernière synchronisation.
						// Si trop ancienne, elle est mise en valeur pour attirer l'attention.
						$last_sync_class = 'alive';
						if( strtotime($device['date_last_sync']." +24 HOUR") < strtotime(date('Y-m-d H:i:s')) ){
							$last_sync_class = 'dead';
						}else if( strtotime($device['date_last_sync']." +4 HOUR") < strtotime(date('Y-m-d H:i:s')) ){
							$last_sync_class = 'lost';
						}

						// Affichage des informations
						print '
							<tr'.$row_style.'>
								<td headers="dev-checked">
									<input class="checkall" type="checkbox" name="dev[]" value="'.$device['id'].'" />
								</td>
								<td headers="dev-key">
									'.view_devices_is_actived( $device ).'
									<a href="/admin/fdv/devices/edit.php?id='.$device['id'].'">'.htmlspecialchars(ucfirst($device_name)).'</a>
								</td>
								<td headers="dev-version">
									'.htmlspecialchars( $device['version'] ).'
								</td>
								<td headers="dev-user">
									'.$user_name.'
								</td>
								<td headers="dev-last-sync">
									<span class="signal-alert '.$last_sync_class.'">'.
									( $device['date_last_sync'] ? ria_date_format($device['date_last_sync'], true) : '<span title="'._('En attente de 1ère synchronisation').'">'._('En attente').'</span>' )
									.'</span>
								</td>
								<td headers="dev-last-location">
						';

						if( trim($device['loc_lat']) != '' && trim($device['loc_lng']) != '' ){
							print '
									<a href="#" onclick="return showDeviceLocation('.$device['loc_lat'].', '.$device['loc_lng'].', '.$device['loc_accuracy'].',\''.$device['date_last_call'].'\');">'._('Voir la localisation').'</a>
							';
						}else{
							print _('Inconnue');
						}

						print '
								</td>
							</tr>
						';
					}
				}

			?></tbody>
			<?php
			$can_add_licence = gu_user_is_authorized('_RGH_ADMIN_OPTION_SUBSCRIPTION');

			if( $can_add_licence || $count_report ){ ?>
			<tfoot>
				<tr>
					<td colspan="6">
					<?php if( $count_report ){ // Les boutons d'actions n'apparaissent que s'ils sont pertinents ?>
						<input class="riaoncheck btn-move" type="submit" name="active" value="<?php print _('Activer'); ?>" title="<?php print _('Activer les appareils sélectionnés'); ?>" />
						<input class="riaoncheck btn-move" type="submit" name="deactive" value="<?php print _('Désactiver'); ?>" title="<?php print _('Désactiver les appareils sélectionnés'); ?>" />
						<input onclick="return confirmDeleteMultiDevice();" type="submit" class="riaoncheck btn-move" name="delete" value="<?php print _('Supprimer'); ?>" title="<?php print _('Supprimer les appareils sélectionnés'); ?>" />
						<input type="button" name="exportParc" id="exportParc" value="<?php print _('Exporter'); ?>" />
					<?php } ?>
					<?php if( $can_add_licence ){ ?>	
						<input type="button" id="addLicence" value="Ajout de licence" onclick="window.location='/admin/options/subscription.php';" />
					<?php } ?>
					</td>
				</tr>
			</tfoot>
			<?php } ?>
		</table>

		<?php
			// Options réservés aux super-administrateurs (RiaStudio)
			if( !gu_users_is_tenant_linked($_SESSION["usr_id"], $_SESSION["usr_email"] ) ){
				if( ria_mysql_num_rows($rdevice) ){
				?>
				<div class="notice"><?php print _('Vous pouvez ci-dessous forcer l\'actualisation de certaines tâches pour les appareils :'); ?></div>
				<input type="submit" name="refresh-config" value="<?php print _('Configuration'); ?>" title="<?php print _('Forcer les tablettes à resynchroniser leurs paramètres de configuration'); ?>" />
				<input type="submit" name="refresh-model" value="Modèles" title="<?php print _('Forcer les tablettes à resynchroniser les Modèles de commande'); ?>" />
				<input type="submit" name="refresh-restriction" value="<?php print _('Restrictions'); ?>" title="<?php print _('Forcer les tablettes à resynchroniser les Restrictions sur le catalogue'); ?>" />
				<input type="submit" name="refresh-states" value="<?php print _('Etat de commandes'); ?>" title="<?php print _('Forcer les tablettes à resynchroniser les Etat de commandes'); ?>" />
				<input type="submit" name="refresh-screen" value="<?php print _('Ecran de synchronisation'); ?>" title="<?php print _('Forcer les tablettes à repasser par l\'étape de première synchronisation'); ?>" />

				<div class="notice margin-top-10">
					En qualité de super-administrateur, vous pouvez utiliser l'interface ci-dessous pour exécuter des requêtes SQL sur l'ensemble des appareils de l'instance :
				</div>

					<table id="tb-exec-sql">
						<caption><?php print _('Exécution de requêtes SQL sur les appareils'); ?></caption>
						<tbody>
							<tr>
								<td class="label">
									<label for="sql"><?php print _('Requêtes :'); ?></label>
								</td>
								<td>
									<textarea id="sql" name="sql"><?php print isset($_POST['sql']) ? htmlspecialchars($_POST['sql']) : ''; ?></textarea>
								</td>
							</tr>
							<tr>
								<td></td>
								<td>
									<sub>(<?php print _('séparez les requêtes par un point-virgule et un saut de ligne'); ?>)</sub>
								</td>
							</tr>
							<tr>
								<td class="label">
									<label for="sql-dest"><?php print _('Destinataires :'); ?></label>
								</td>
								<td>
									<textarea id="sql-dest" name="sql-dest"><?php
										print isset($_POST['sql-dest']) ? htmlspecialchars($_POST['sql-dest']) : '';

										if ( isset($_SESSION["usr_email"]) && trim($_SESSION["usr_email"]) != "") {
											print trim($_SESSION["usr_email"])."\n";
										}
									?></textarea>
								</td>
							</tr>
						</tbody>
						<tfoot>
							<tr>
								<td colspan="2">
									<input type="submit" value="<?php print _('Envoyer'); ?>" name="btn-sql" />
								</td>
							</tr>
						</tfoot>
					</table>
				<?php
				}
			}
		?>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>
