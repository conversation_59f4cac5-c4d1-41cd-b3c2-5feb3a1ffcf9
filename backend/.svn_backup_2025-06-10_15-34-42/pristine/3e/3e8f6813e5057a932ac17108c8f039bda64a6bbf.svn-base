<?php

// Dépendances
require_once('db.inc.php');
require_once('images.inc.php');
require_once('search.inc.php');
require_once('products.inc.php');


/** \defgroup model_prd_images Images produits
 *	\ingroup model_images
 *
 *	Ce module comprend les fonctions nécessaires à la gestion des images de produits.
 *	Une ou plusieurs images peuvent être associées à chacun des produits.
 *
 *	Cette fonction exploite les directives de configuration suivantes :
 *		- $config['img_dir'] : dossier d'enregistrement des images.
 *		- $config['img_sizes'] : tableau des dimensions de vignettes. Chaque entrée de ce tableau doit contenir les clés width et height.
 *
 *	Le tableau des tailles de vignettes est utilisé comme ceci :
 *	\code
 *		foreach( $config['img_sizes'] as $size )
 *			print $size['width'].' '.$size['height'];
 *	\endcode
 *
 *	Les tailles de vignettes sont utilisées comme des maximums. La génération de vignette conserve les proportions des images.
 *	Il est possible d'utiliser autant de tailles de vignettes que souhaité, mais celle-ci doivent être triées par ordre croissant.
 *
 *	@{
 */

/**	Retourne des informations sur les images secondaires de produits suivant des paramètres optionnels
 *	@param int $prd Optionnel, identifiant de produit ou tableau d'identifiants
 *	@param int|array $img Optionnel, identifiant d'image ou tableau d'identifiants
 *	@param bool $correled Optionnel, si activé, les IDs de produits sont liés aux IDs d'images pour ne retourner que les lignes où la relation prd / img existe ($prd et $img doivent être de même taille)
 *	@param bool $publish Optionnel, par défaut seule les images encores activées pour le(s) produit(s) seront retournées, mettre false pour avoir les autres et null pour toutes les avoir
 *	@param bool $get_main Optionnel, permet de récupérer également l'image principale du produit
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'image
 *		- prd_id : identifiant du produit
 *		- pos : position relative de l'image dans le listing pour ce produit
 *		- alt : attribut alt de l'image
 *		- is_main : booléen indiquant si l'image est principale ou non
 */
function prd_images_get( $prd=0, $img=0, $correled=false, $publish=true, $get_main=false ){
	global $config;

	$prd = control_array_integer( $prd, false );
	if( $prd === false ){
		return false;
	}

	$img = control_array_integer( $img, false );
	if( $img === false ){
		return false;
	}

	if( $correled && sizeof($prd) != sizeof($img) ){
		return false;
	}

	$sql = '
		select imo_img_id as id, imo_pos as pos, imo_obj_id_0 as prd_id, imo_alt as alt, imo_is_main as is_main
		from img_images_objects
		where imo_tnt_id = '.$config['tnt_id']
			.' and imo_cls_id = ' . CLS_PRODUCT
			. ' and imo_date_deleted is null
	';

	if( !$get_main ){
		$sql .= ' and imo_is_main = 0';
	}

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($prd); $i++ ){
			$cnds[] = 'imo_obj_id_0 = '.$prd[ $i ].' and imo_img_id = '.$img[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($prd) ){
			$sql .= ' and imo_obj_id_0 in ('.implode(', ', $prd).')';
		}
		if( sizeof($img) ){
			$sql .= ' and imo_img_id in ('.implode(', ', $img).')';
		}
	}

	if($publish !== null){
		if($publish){
			$sql .= ' and imo_publish = 1';
		}else{
			$sql .= ' and imo_publish = 0';
		}
	}

	if( sizeof($img) != 1 ){
		$sql .= ' order by imo_pos asc, imo_img_id desc';
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de charger un tableau contenant les identifiants de toutes les images d'un produit
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param $include_child Optionnel, par défaut les images des articles enfants sont exclues, mettre True pour les inclures au résultat
 *	@param $get_prd_id Optionnel, par défaut retourne un tableau des identifiants images, mettre true pour retourner un tableau associatif : prd_id => array(img_id, img_id, ...)
 *	@return array Un tableau contenant les identifiants d'images
 */
function prd_images_get_all( $prd_id, $include_child=false, $get_prd_id=false ){
	$ar_img_ids = array();

	if( !is_numeric($prd_id) && $prd_id <= 0 ){
		return $ar_img_ids;
	}

	global $config;

	$sql = '
		select imo_img_id as img_id, imo_obj_id_0 as prd_id
		from img_images_objects
		where imo_tnt_id = '.$config['tnt_id'].'
			and imo_cls_id = '.CLS_PRODUCT.'
			and imo_date_deleted is null
			and imo_obj_id_0 = '.$prd_id.'
			and imo_publish = 1
		order by imo_is_main desc, imo_pos
	';

	if( $include_child ){
		$ordered = prd_relations_order_get('childs');

		$sql = '
			select img_id, res.prd_id as prd_id
			from(
					select img_id, prd_id, child_pos, isparent, imo_is_main, img_pos
					from (
						select imo_img_id as img_id, (imo_pos+1) as img_pos, imo_obj_id_0 as prd_id, imo_is_main, -1 as child_pos, 1 as isparent
						from img_images_objects
						where imo_tnt_id = '.$config['tnt_id'].'
							and imo_cls_id = 1
							and imo_date_deleted is null
							and imo_obj_id_0 = '.$prd_id.'
							and imo_publish = 1

						union

						select imo_img_id as img_id, (imo_pos + 1) as img_pos, imo_obj_id_0 as prd_id, imo_is_main, ifnull(prd_child_pos, 0) as child_pos, 2 as isparent
						from img_images_objects
							join prd_hierarchy on (imo_tnt_id = prd_tnt_id and imo_obj_id_0 = prd_child_id)
						where imo_tnt_id = '.$config['tnt_id'].'
							and imo_cls_id = 1
							and imo_date_deleted is null
							and prd_parent_id = '.$prd_id.'
							and imo_publish = 1

					) as res_images
			) as res
				join prd_products as p on (prd_tnt_id = '.$config['tnt_id'].' and p.prd_id = res.prd_id)
			order by isparent asc, imo_is_main desc'.( !$ordered ? ', prd_ref asc' : ', child_pos asc').', img_pos
		';
	}

	$res = ria_mysql_query( $sql );

	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			if( !is_numeric($r['img_id']) || $r['img_id'] <= 0 ){
				continue;
			}

			if( $get_prd_id ){
				if( !array_key_exists($r['prd_id'], $ar_img_ids) ){
					$ar_img_ids[ $r['prd_id'] ] = array();
				}

				if( !in_array($r['img_id'], $ar_img_ids[ $r['prd_id'] ]) ){
					$ar_img_ids[ $r['prd_id'] ][] = $r['img_id'];
				}
			}else{
				if( !in_array($r['img_id'], $ar_img_ids) ){
					$ar_img_ids[] = $r['img_id'];
				}
			}
		}
	}

	return $ar_img_ids;
}

/** Cette fonction met une image en état publié ou non
 *	@param int $prd_id Obligatoire, identifiant d'un article
 *	@param int $img_id Obligatoire, identifian d'une image
 *	@param bool $publish Obligatoire, booléen indiquant si l'image doit être publiée ou non
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_images_set_publish( $prd_id, $img_id, $publish ){
	return img_images_objects_set_publish(intval($publish), 0, $prd_id, 0, 0, 0, $img_id, CLS_PRODUCT);
}

/** Cette fonction permet de charger un tableau contenant les images des articles parents de l'article passé en paramètre.
 *	@param int $prd_id Obligatoire, identifiant d'un article
 *	@param bool $only_main Optionnel, par défaut seul les images principales sont chargées
 *	@return array Un tableau des images
 */
function prd_images_get_in_parents( $prd_id, $only_main=true ){
	global $config;

	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return array();
	}

	$res = ria_mysql_query('
		select imo_img_id as img_id
		from img_images_objects
			join prd_hierarchy on (prd_tnt_id = ' . $config['tnt_id'] . ' and imo_obj_id_0 = prd_parent_id)
		where imo_tnt_id = '.$config['tnt_id'].'
			and imo_cls_id = '.CLS_PRODUCT.'
			and imo_date_deleted is null
			and imo_publish = 1
			and imo_is_main = ' . ($only_main ? 1 : 0). '
			and prd_child_id = '.intval($prd_id)
	);

	if( !$res || !ria_mysql_num_rows($res) ){
		return array();
	}

	$ar_imgs = array();
	while( $r = ria_mysql_fetch_assoc($res) ){
		$ar_imgs[] = $r['img_id'];
	}

	return $ar_imgs;
}

/** Permet l'upload d'une image principale à associer à un produit.
 *
 *	@param int $prd Identifiant du produit.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *
 */
function prd_images_main_upload( $prd, $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return prd_images_main_add( $prd, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );
}

/** Permet l'upload d'une image qui sera ensuite ajoutée aux images du produit.
 *
 *	@param int $prd Identifiant du produit.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *
 */
function prd_images_upload( $prd, $fieldname ){

	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return prd_images_add( $prd, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );

}

/** Permet l'ajout d'un fichier image à un produit. Cette fonction est similaire à prd_images_upload,
 *	a l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile
 *	lors d'importation.
 *
 *	@param int $prd Identifiant du produit.
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source
 *	@param bool $sync Facultatif, détermine si l'image est synchronisée
 *
 *	@return int L'identifiant attribué à l'image, False en cas d'échec.
 *
 */
function prd_images_main_add( $prd, $filename, $srcname='', $sync=false ){
	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	// crée l'image, ou récupère l'image existante p/r au md5 de son contenu
	$new_img_id = img_images_add( $filename, $srcname, true, $sync );
	if( !$new_img_id ){
		return false;
	}

	global $config;

	// récupère l'ancienne image principale du produit
	$old_img = false;
	$sql = 'select prd_img_id as img from prd_products where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prd;
	$r = ria_mysql_query($sql);
	if( $r && ria_mysql_num_rows($r) ){
		$old_img = ria_mysql_result($r, 0, 'img');
	}

	// si l'image a changé
	if( $old_img != $new_img_id ){
		$sql = 'update prd_products set prd_img_id = '.$new_img_id.' where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prd;
		if( ria_mysql_query($sql) ){
			$r_img_obj = img_images_objects_get(0, $prd, 0, 0, 0, $new_img_id, CLS_PRODUCT, null, null);
			if (!$r_img_obj || !ria_mysql_num_rows($r_img_obj)) {
				img_images_objects_add(CLS_PRODUCT, $new_img_id, null, $prd, 0, 0, null, null, true, true);
			} else {
				img_images_objects_set_is_main(true, 0, $prd, 0, 0, 0, $new_img_id, CLS_PRODUCT);
				img_images_objects_set_publish(true, 0, $prd, 0, 0, 0, $new_img_id, CLS_PRODUCT, true);
				img_images_objects_set_pos(0, 0, $prd, 0, 0, 0, $new_img_id, CLS_PRODUCT, true, true);
			}

			prd_products_update_completion( $prd );
			img_images_count_update( $new_img_id );

			// mise à jour du count de l'ancienne image
			if( $old_img ){
				img_images_count_update( $old_img );
			}

			$sql = 'select cly_cnt_id as cnt_id from prd_classify where cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = '.$prd;
			if( $search_contents = ria_mysql_query($sql) ){
				while( $r = ria_mysql_fetch_array($search_contents) ){
					search_contents_image_add( $r['cnt_id'], $new_img_id );
				}
			}
		}else{
			$new_img_id = false;
		}
	}

	return $new_img_id;
}

/**	Cette fonction permet la réutilisation d'une image existante pour un produit.
 *	@param int $prd Obligatoire, Identifiant du produit.
 *	@param int $img Obligatoire, Identifiant du fichier image.
 *	@param bool $sync Facultatif, permet de définir si la relation provient de la gestion commerciale si oui alors on met à jour l'image
 *	@param bool $refresh_count Optionnel, par défaut on mettra à jour le nombre d'utilisation de l'image après l'avoir rattachée au produit, mettre False pour ne pas mettre à jour
 * 	@param string $alt Facultatif, Texte alternatif
 * 	@param bool $no_del Optionnel, par défaut l'image principale actuelle est remplace, mettre true pour la déplacer dans les secondaires
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_images_main_add_existing( $prd, $img, $sync=false, $refresh_count=true, $alt=null, $no_del=false ){
	global $config;
	if( !prd_products_exists($prd) ) return false;
	if( !img_images_exists($img) ) return false;

	$ar_refresh_count = array();

	if( $sync ) {
		ria_mysql_query( 'update img_images set img_is_sync=1 where img_tnt_id = '.$config['tnt_id']. ' and img_id = '.$img );
	}

	$oldImg = prd_images_main_get( $prd );

	if( $oldImg == $img ){
		return true;
	}

	ria_mysql_query('update prd_products set prd_img_id='.$img.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);

	// suppression de l'ancienne image principale
	$r_old = img_images_objects_get(0, $prd, 0, 0, 0, 0, CLS_PRODUCT, null, true);
	if ($r_old && ria_mysql_num_rows($r_old)) {
		$old = ria_mysql_fetch_assoc($r_old);
		if( !$no_del ){
			img_images_objects_del(CLS_PRODUCT, 0, 0, $old['imo_id']);
		}else{
			img_images_objects_set_is_main( 0, 0, $prd, 0, 0, $old['imo_id'], 0, CLS_PRODUCT );
		}
	}

	$r_img_obj = img_images_objects_get(0, $prd, 0, 0, 0, $img, CLS_PRODUCT, null, null);
	if (!$r_img_obj || !ria_mysql_num_rows($r_img_obj)) {
		img_images_objects_add(CLS_PRODUCT, $img, null, $prd, 0, 0, null, null, true, true);
	} else {
		img_images_objects_set_is_main(true, 0, $prd, 0, 0, 0, $img, CLS_PRODUCT);
		img_images_objects_set_pos(0, 0, $prd, 0, 0, 0, $img, CLS_PRODUCT);
		img_images_objects_set_publish(true, 0, $prd, 0, 0, 0, $img, CLS_PRODUCT, true);
	}

	if( $oldImg > 0 ){
		if( $refresh_count ){
			img_images_count_update( $oldImg );
		}else{
			$ar_refresh_count[] = $oldImg;
		}
	}

	prd_products_update_completion( $prd );
	$search_contents = ria_mysql_query('select cly_cnt_id as cnt_id from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$prd);
	while( $r = ria_mysql_fetch_array($search_contents) ){
		search_contents_image_add( $r['cnt_id'], $img, false );
	}

	if( $refresh_count ){
		img_images_count_update($img);
	}else{
		$ar_refresh_count[] = $img;
	}

	return ( $refresh_count ? true : $ar_refresh_count );
}

/** Permet l'ajout d'un fichier image à un produit. Cette fonction est similaire à prd_images_upload,
 *	a l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile
 *	lors d'importation.
 *
 *	@param int $prd Obligatoire, identifiant du produit.
 *	@param string $filename Obligatoire, nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source.
 *	@param bool $sync Facultatif, détermine si l'image est synchronisée
 *	@param bool $check_main Facultatif, détermine si, en cas d'absence de l'image principale, une image secondaire est utilisée (Vrai par défaut)
 *	@param bool $refresh_count Optionnel, par défaut on mettra à jour le nombre d'utilisation de l'image après l'avoir rattachée au produit, mettre False pour ne pas mettre à jour
 *	@param bool $publish Optionnel, Indique si l'image doit être publié ou non
 * 	@param string $alt Facultatif, Texte alternatif
 *  @param null|int $pos Optionnel, par défaut à null donc ignoré, permet de forcer la position de l'image (une image principale aura toujours 0 pour la position)
 *
 *	@return int L'identifiant attribué à l'image, ou False en cas d'échec.
 */
function prd_images_add( $prd, $filename, $srcname='', $sync=false, $check_main=true, $refresh_count=true, $publish=true, $alt=null, $pos=null ){

	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	if( trim($filename) == '' ){
		return false;
	}

	// création de l'image
	$id = img_images_add( $filename, $srcname, true, $sync );
	if( !$id ){
		return false;
	}

	// création du lien
	if( !prd_images_add_existing($prd, $id, $check_main, $refresh_count, $publish , $alt, $pos) ){
		return false;
	}

	return $id;

}

/**	Cette fonction permet la réutilisation d'une image existante pour un produit.
 *	Si la liaison existe déjà, aucune action n'est effectuée.
 *
 *	@param int $prd Obligatoire, Identifiant du produit.
 *	@param int $img Obligatoire, Identifiant de l'image.
 *	@param bool $check_main Facultatif, détermine si, en cas d'absence de l'image principale, une image secondaire est utilisée (Vrai par défaut)
 *	@param bool $refresh_count Optionnel, par défaut on mettra à jour le nombre d'utilisation de l'image après l'avoir rattachée au produit, mettre False pour ne pas mettre à jour
 *	@param bool $publish Optionnel, Indique si l'image doit être publié ou non
 * 	@param string $alt Facultatif, Texte alternatif
 *  @param null|int $pos Optionnel, par défaut à null donc ignoré, permet de forcer la position de l'image (une image principale aura toujours 0 pour la position)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_images_add_existing( $prd, $img, $check_main=true, $refresh_count=true , $publish=true, $alt=null, $pos=null ){
	global $config;

	if( !prd_products_exists( $prd ) || !img_images_exists( $img ) ){
		return false;
	}

	if( $rexist = prd_images_get( $prd, $img, false, null ) ){
		if( ria_mysql_num_rows($rexist) ){
			prd_images_set_publish($prd, $img, true);
			return true;
		}
	}

	$main_img = prd_images_main_get( $prd );
	if( is_numeric($main_img) && $main_img == $img ){
		return true;
	}

	// Détermine s'il s'agit de l'image principale du produit
	$is_main = $check_main && !is_numeric($main_img);

	// Détermine la position de l'image
	if( is_numeric($pos) && $pos >= 0 ){
		if( $is_main ){
			$pos = 0;
		}
	}else{
		// récupère la position de la dernière image (+1)
		if( $is_main ){
			$pos = 0;
		}else{
			$pos = img_images_objects_get_max_pos(0, $prd, 0, 0, 0, 0, CLS_PRODUCT, $publish, false);
			$pos = is_null($pos) ? 0 : ($pos + 1);
		}
	}

	// S'il s'agit de l'image principale, l'identfiant est aussi renseigné directement sur le produit
	if( $is_main ){
		ria_mysql_query('
			update prd_products
			set prd_img_id = '.$img.'
			where prd_tnt_id = '.$config['tnt_id'].'
				and prd_id = '.$prd.'
		');
	}

	$r_img_obj = img_images_objects_get( 0, $prd, 0, 0, 0, $img, CLS_PRODUCT, null, true );
	$img_obj_id = false;
	if (!$r_img_obj || !ria_mysql_num_rows($r_img_obj)) {
		$img_obj_id = img_images_objects_add(CLS_PRODUCT, $img, null, $prd, 0, 0, $pos, null, $publish, $is_main);
	} else {
		$img_obj_id = ria_mysql_result($r_img_obj, 0, 'id');
		img_images_objects_set_is_main($is_main, 0, $prd, 0, 0, 0, $img, CLS_PRODUCT);
		img_images_objects_set_publish($publish, 0, $prd, 0, 0, 0, $img, CLS_PRODUCT);
		img_images_objects_set_pos($pos, 0, $prd, 0, 0, 0, $img, CLS_PRODUCT);
	}

	if( !$img_obj_id){
		return false;
	}

	prd_products_set_date_modified( $prd );

	$ar_refresh_count = array();
	if( $refresh_count ){
		img_images_count_update( $img );
	}else{
		$ar_refresh_count[] = $img;
	}

	return ( $refresh_count ? true : $ar_refresh_count );

}

/** Permet la suppression de l'image principale du produit. Aucune erreur n'est générée si l'image était déjâ supprimée.
 *  @param int $prd Obligatoire, identifiant du produit dont on souhaite supprimer l'image principale
 *	@param int $img_id Optionnel, identifiant d'une image, permet de supprimer l'image principale seulement si elle correspond à l'identifiant donné
 *  @return bool True en cas de succès, False si le produit n'existe pas.
 */
function prd_images_main_del( $prd, $img_id=0 ){
	global $config;

	if( !prd_products_exists($prd) ){
		return false;
	}
	if (!is_numeric($img_id) || $img_id < 0) {
		return false;
	}

	// Récupère l'image principale actuelle
	$rexists = img_images_objects_get(0, $prd, 0, 0, 0, $img_id, CLS_PRODUCT, true, true);

	if ($rexists && ria_mysql_num_rows($rexists)) {
		$exists = ria_mysql_fetch_assoc($rexists);

		ria_mysql_query('update prd_products set prd_img_id=null where prd_tnt_id=' . $config['tnt_id'] . ' and prd_id=' . $prd);

		// récupère la position de la dernière image non publié (+1)
		$pos = img_images_objects_get_max_pos(0, $prd, 0, 0, 0, 0, CLS_PRODUCT, false, null);
		$pos = is_null($pos) ? 0 : $pos + 1;

		ria_mysql_query('
			update img_images_objects
				set imo_is_main = 0,
					imo_publish = 0,
					imo_pos = '.$pos.',
					imo_date_modified = now()
				where imo_tnt_id = ' . $config['tnt_id']
					. ' and imo_cls_id = '.CLS_PRODUCT.'
						and imo_date_deleted is null
						and imo_img_id = ' . $exists['id']
					. ' and imo_obj_id_0 = ' . $prd . '
		');

		search_contents_image_del_from_tag(CLS_PRODUCT, $prd, $exists['id']);
	}

	return true;
}

/**	Cette fonction permet le chargement de l'image principal d'un produit
 *	@param int $prd Obligatoire, identifiant du produit dont on souhaite connaître l'identifiant de l'image principale
 *	@return	l'identifiant de l'image principale, ou false si aucune image principale n'a été définie pour le produit
 */
function prd_images_main_get( $prd ){
	global $config;
	if (!is_numeric($prd) || $prd <= 0) return false;

	$rimg = ria_mysql_query('select prd_img_id from prd_products where prd_tnt_id=' . $config['tnt_id'] . ' and prd_id=' . $prd);
	if (ria_mysql_num_rows($rimg)) {
		return ria_mysql_result($rimg, 0, 0);
	}

	return false;
}

/**	Cette fonction supprime un ou des liens entre un produit et ses images secondaires.
 *
 *	@param int $prd Obligatoire, identifiant de l'image.
 *	@param int $img Optionnel, identifiant de l'image. Si omis, toutes les images du produit seront retirées.
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 *
 */
function prd_images_del( $prd, $img=0 ){
	global $config;

	if( !is_numeric($prd) || !is_numeric($img) ){
		return false;
	}

	if( is_numeric($img) && $img > 0 ){
		// S'il s'agit de l'image principal sa suppression sera lancée
		$is_main = img_images_objects_get(0, $prd, 0, 0, 0, $img, CLS_PRODUCT, true, true);
		if( $is_main && ria_mysql_num_rows($is_main) ){
			return prd_images_main_del( $prd, $img );
		}
	}

	// recalcule la position des images secondaires au delà de celle à supprimer
	$sql = '
		select imo_pos
		from img_images_objects
		where imo_tnt_id = '.$config['tnt_id']
			.' and imo_cls_id = '.CLS_PRODUCT
			.' and imo_obj_id_0 = '.$prd
			.' and imo_date_deleted is null'
			.' and imo_img_id = '.$img
			.' and imo_publish = 1
			and imo_is_main = 0
	';
	if( $r_pos = ria_mysql_query($sql) ){
		if( ria_mysql_num_rows($r_pos) ){
			$pos = ria_mysql_result($r_pos, 0, 0);
			ria_mysql_query('
				update img_images_objects
				set imo_pos = imo_pos-1
				where imo_tnt_id = '.$config['tnt_id']
					.' and imo_cls_id = '.CLS_PRODUCT
					.' and imo_date_deleted is null
						and imo_is_main = 0
						and imo_obj_id_0 = '.$prd
					.' and imo_pos > '.$pos.'
			');
		}
	}

	// supprime le lien entre image et produit
	$sql = '
		update img_images_objects
			set imo_date_deleted = now()
		where imo_tnt_id = '.$config['tnt_id']
			.' and imo_cls_id = '.CLS_PRODUCT
			.' and imo_date_deleted is null
				and imo_is_main = 0
				and imo_obj_id_0 = '.$prd.'
	';
	if( $img > 0 ){
		$sql .= ' and imo_img_id = '.$img;
	}

	if( !ria_mysql_query($sql) ){
		return false;
	}

	prd_products_set_date_modified( $prd );

	if( $img > 0 ){
		return img_images_count_update( $img );
	}

	return img_images_count_update( 0, $prd );

}

/**	Cette fonction permet la recopie des images d'un produit parent vers ses produits enfants. L'image principale ainsi que les images secondaires sont concernées.
 *	@param int $prd Obligatoire, identifiant du produit parent dont on doit recopier les images
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_images_copy_to_childs( $prd ){

	if( !prd_products_exists( $prd ) ){
		return false;
	}

	// Si aucun produit enfant, inutile de continuer
	$rchilds = prd_products_get_simple( 0, '', false, 0, false, false, false, false, array('childs' => true, 'parent' => $prd) );
	if( !$rchilds || !ria_mysql_num_rows($rchilds) ){
		return false;
	}

	$childs = ria_mysql_fetch_assoc_all($rchilds);

	$error = false;

	// Recopie l'image principale sur les produits enfants
	$img = prd_images_main_get( $prd );
	if( $img ){
		// gestion de la duplication de l'attribut alt
		$alt = null;
		$img_objet = img_images_objects_get( 0, $prd, 0, 0, 0, $img, 1);
		if($img_objet && ria_mysql_num_rows($img_objet)) {
			$res = ria_mysql_fetch_assoc($img_objet);
			if($res['alt'] != ""){
				$alt = $res['alt'];
			}
		}
		foreach( $childs as $child ){
			if( !prd_images_main_add_existing( $child['id'], $img ) ){
				$error = true;
			}
			if( $alt != null ){
				img_images_objects_set_alt($alt, false, 0, $child['id'], 0, 0, 0, $img, 1, true, true);
			}

		}
	}

	// Recopie les images secondaires sur les produits enfants
	$rimages = prd_images_get( $prd );
	if( $rimages && ria_mysql_num_rows($rimages) ){
		$images = ria_mysql_fetch_assoc_all($rimages);
		foreach( $childs as $child ){
			foreach( $images as $img ){
				if( !prd_images_add_existing( $child['id'], $img['id'] ) ){
					$error = true;
				}

				if( trim($img['alt']) ){
					img_images_objects_set_alt($img['alt'], false, 0, $child['id'], 0, 0, 0, $img['id'], 1, true, false);
				}
			}

			prd_products_set_date_modified( $child['id'] );
		}
	}

	return !$error;

}

/**	Cette fonction recalcule la position de toutes les images secondaires d'un produit.
 *	Le calcul est basé sur le nom source de l'image, auquel on a retiré le code article, pour ne garder que le suffixe "_xxx".
 *	Si l'image a plusieurs noms sources (un pour chaque article), on prend celui qui correspondant au produit.
 *	@param int $prd Identifiant du produit dont on souhaite recalculer les images secondaires.
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_images_rebuild_order( $prd ){

	if( !prd_products_exists( $prd ) ){
		return false;
	}

	// charge les images secondaires du produit
	$rimg = prd_images_get( $prd );
	if( !$rimg ){
		return false;
	}

	// aucune image secondaire
	if( !ria_mysql_num_rows($rimg) ){
		return true;
	}

	global $config;

	// une seule image secondaire
	if( ria_mysql_num_rows($rimg) == 1 ){
		return ria_mysql_query('
			update img_images_objects
			set imo_pos = 1
			where
				imo_tnt_id = '.$config['tnt_id'].'
				and imo_cls_id = '.CLS_PRODUCT.'
				and imo_date_deleted is null
				and imo_img_id = '.ria_mysql_result($rimg, 0, 'id').'
				and imo_obj_id_0 = '.$prd.'
		');
	}

	// récupère le code article du produit
	$prd_ref = prd_products_get_ref( $prd );

	// création d'un tableau des images, contenant une colonne avec le nom source de l'image
	$imgs_ar = array();
	while( $img = ria_mysql_fetch_assoc($rimg) ){

		$img['src_name'] = '';
		$names = img_image_names_get( $img['id'], $prd_ref.$config['img_sync_code_separator'], true, true );
		if( $names && ria_mysql_num_rows($names) ){
			$img['src_name'] = ria_mysql_result($names, 0, 'name');
		}

		$img['src_name'] = str_replace($prd_ref.$config['img_sync_code_separator'], '', $img['src_name']);
		$imgs_ar[] = $img;

	}

	// tri croissant du tableau
	$imgs_ar = array_msort( $imgs_ar, array('src_name' => SORT_ASC) );

	// mise à jour des positions selon l'ordre du tableau
	$error = false;

	$i = 0;
	foreach( $imgs_ar as $img ){
		$i++;
		$sql = '
			update img_images_objects
			set imo_pos = '.$i.'
			where
				imo_tnt_id = '.$config['tnt_id'].'
				and imo_cls_id = '.CLS_PRODUCT.'
				and imo_date_deleted is null
				and imo_img_id = '.$img['id'].'
				and imo_obj_id_0 = '.$prd.'
		';
		if( !ria_mysql_query($sql) ){
			$error = true;
			break;
		}
	}

	return !$error;

}

/**	Cette fonction permet de mettre à jour la position d'une image pour un produit donné
 * 	@param int $prd Obligatoire, identifiant du produit
 * 	@param int $img Obligatoire, identifiant de l'image
 * 	@param int $pos Obligatoire, position à affecter à l'image
 *
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function prd_images_set_pos( $prd, $img, $pos ){
	global $config;

	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}
	if( !is_numeric($img) || $img <= 0 ){
		return false;
	}
	if( !is_numeric($pos) || $pos < 0 ){
		return false;
	}

	return ria_mysql_query('
		update img_images_objects
			set imo_pos = '.$pos.'
		where imo_tnt_id = '.$config['tnt_id'].'
			and imo_obj_id_0 = '.$prd.'
			and imo_img_id = '.$img.'
			and imo_cls_id = '.CLS_PRODUCT.'
			and imo_date_deleted is null
	');

}

/** Cette fonction permet de récupérer les articles qui ne sont pas liés à une image. Les produits en sommeil
 *	ne sont pas retournés par cette fonction.
 *
 *	@param bool $main Optionnel, par défaut on récupère les produits sans images principale (mettre False pour récupérer ceux sans images secondaire)
 *	@param bool $published  Optionnel, mettre True pour les articles publiés, False pour les autres (par défaut à null)
 *	@param bool $orderable Optionnel, mettre True pour ne récupérer que les articles commandable (par défaut à null)
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant du produit
 *				- name : nom du produit
 *				- title : titre du produit
 *				- selled_web : nombre de vente Web
 *				- hits : nombre de consultation de la fiche produti
 *				- is_sync : si oui ou non l'article est synchronisé
 *				- date_created : date de création du produit
 *				- taux : pourcentage entre le nombre de commande et le nombre de vue
 */
function prd_products_get_not_images( $main=true, $published=null, $orderable=null){
	global $config;

	$sql = '
		select prd_id as id, prd_ref as ref, prd_name as name, if(ifnull(prd_title, "")="", prd_name, prd_title) as title, prd_selled_web as selled_web, prd_hits as hits, prd_is_sync as is_sync, prd_date_created as date_created,
		( if(ifnull(prd_hits, 0) > 0, (prd_selled_web / prd_hits), 0) ) as taux
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_date_deleted is null
			and prd_sleep=0
	';

	if( $main ){
		$sql .= '
			and (
				ifnull(prd_img_id, 0) = 0

				or

				not exists (
					select 1
					from img_images
					where img_tnt_id = '.$config['tnt_id'].'
						and img_id = prd_img_id
						and img_date_deleted is null
				)
			)
		';
	}else{
		$sql .= '
			and not exists (
				select 1
				from img_images_objects
				where imo_tnt_id = '.$config['tnt_id'].'
				and imo_cls_id = '.CLS_PRODUCT.'
				and imo_date_deleted is null
				and imo_obj_id_0 = prd_id
				and imo_publish = 1
				and imo_is_main = 0
			)
		';
	}

	if( $published !== null ){
		if( $published ){
			$sql .= ' and prd_publish = 1 and prd_publish_cat = 1';
		}else{
			$sql .= ' and (prd_publish = 0 or prd_publish_cat = 0)';
		}
	}elseif( $orderable === true ){
		$sql .= ' and prd_publish = 1 and prd_publish_cat = 1';
	}

	if( $orderable === true ){
		$sql .= ' and prd_orderable = 1';
	}

	$sql .= ' order by prd_hits desc ';

	return ria_mysql_query( $sql );
}

/**
 * Cette fonction permet de supprimer virtuellement les images en double des produits
 * @return bool True s'il n'y a pas eu d'erreur pendant l'execution, false sinon
 */
function prd_images_clean_duplicates(){
	global $config;

	$sql = '
		SELECT imo_id as id, imo_type_id as type_id, imo_obj_id_0 as obj_id_0, imo_obj_id_1 as obj_id_1, imo_obj_id_2 as obj_id_2, imo_img_id as img_id, imo_is_main as is_main
		FROM img_images_objects
		WHERE imo_tnt_id = '.$config['tnt_id'].'
		AND imo_date_deleted is null
		AND imo_cls_id = '.CLS_PRODUCT.'
		ORDER BY imo_is_main desc, imo_pos asc
	';

	$r = ria_mysql_query($sql);

	if( !$r ){
		return false;
	}
	$ar_imgs = [];
	$errors = [];

	while( $img = ria_mysql_fetch_assoc($r) ){
		$skey = $img['img_id'].'_'.$img['obj_id_0'].'_'.$img['obj_id_1'].'_'.$img['obj_id_2'];

		// Si le produit a une image en double -> suppression virtuelle
		if( array_key_exists($skey, $ar_imgs) && $ar_imgs[$skey] == $img['img_id'] && (!is_numeric($img['is_main']) || !$img['is_main']) ){

			if( !img_images_objects_del(CLS_PRODUCT, $img['type_id'], $img['img_id'], $img['id'], $img['obj_id_0'], $img['obj_id_1'], $img['obj_id_2']) ){
				$errors[] = $img['id'];
			}
			continue;
		}
		$ar_imgs[$skey] = $img['img_id'];

	}
	return count($errors) > 0 ? false : true;

}
/// @}


