<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/datastore/v1/query.proto

namespace Google\Cloud\Datastore\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\Datastore\V1\PropertyFilter\Operator instead.
     * @deprecated
     */
    class PropertyFilter_Operator {}
}
class_exists(PropertyFilter\Operator::class);
@trigger_error('Google\Cloud\Datastore\V1\PropertyFilter_Operator is deprecated and will be removed in the next major release. Use Google\Cloud\Datastore\V1\PropertyFilter\Operator instead', E_USER_DEPRECATED);

