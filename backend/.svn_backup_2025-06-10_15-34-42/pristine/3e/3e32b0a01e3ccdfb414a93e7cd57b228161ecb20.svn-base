<?php

/**
 * \defgroup api-tenants-yuto <PERSON>
 * \ingroup Locataires
 * @{
 * \page api-tenants-yuto-add Ajout
 *
 * Cette fonction permet d'activer l'offre Yuto pour un tenant existant
 *
 *		\code
 *			POST /tenants/yuto/
 *		\endcode
 *
 * @param {string} $package Obligatoire, formule Yuto choisie (essentiel ou business)
 * @param {bool} $is_default Obligatoire, s'il s'agit du site par défaut
 * @param {int} $licences Obligatoire, nombre de licence à activer
 * @param {bool} $abo-active Obligatoire, si oui ou non la gestion de l'abonnement est activé
 * @param {string} $abo-type Obligatoire, type d'abonnement (month | year)
 * @param {float} $abo-amount Obligatoire, montant ht de l'abonnement
 * @param {int} $abo-testing Obligatoire, nombre de jours d'essai offert
 * @param {int} $abo-user Obligatoire, identifiant du compte client sur RiaStudio
 * @param {array} $cfg Obligatoire, tableau contenant différentes variables de configuration
 * 						- dev_need_activation : si les appareils doivent être manuellement activée pour permettre une synchronisation
 * 						- fdv_usr_suspect_active : si la notion de "suspect" est activé
 * 						- fdv_usr_prospect_active : si la notion de "prospect" est activé
 * @param {bool} $sendmail Obligatoire, si oui ou non le mail de confirmation de création est envoyé (il s'agit du mail prévu pour start.yuto.fr)
 * @param {string} $admin_email Optionnel, adresse mail de la personne qui recevra le mail de confirmation
 *
 * @return true si l'activation s'est déroulée avec succès
 */

require_once('RegisterGCP.inc.php');

switch( $method ){
	case 'add':
		{ // Contrôle des informations obligatoire
			if( !isset($_REQUEST['package']) || !in_array($_REQUEST['package'], ['essentiel', 'business']) ){
				throw new BadFunctionCallException('L\'information "formule choisie" est manquante ou fausse.');
			}

			if( !isset($_REQUEST['is_default']) ){
				throw new BadFunctionCallException('L\'information "site par défaut" est manquante.');
			}

			if( !isset($_REQUEST['licences']) || !is_numeric($_REQUEST['licences']) || $_REQUEST['licences'] <= 0 ){
				throw new BadFunctionCallException('Le nombre de licences est manquant ou faux.');
			}

			if( !isset($_REQUEST['abo-active']) ){
				throw new BadFunctionCallException('L\'information "abonnement activé" est manquante.');
			}

			if( !isset($_REQUEST['start']) || !isdate($_REQUEST['start']) ){
				throw new BadFunctionCallException('L\'information "date de début d\'abonnement" est manquante ou fausse.');
			}

			if( !isset($_REQUEST['abo-type']) ){
				throw new BadFunctionCallException('L\'information "type d\'abonnement" est manquante.');
			}

			if( !isset($_REQUEST['abo-amount']) || !is_numeric($_REQUEST['abo-amount']) || $_REQUEST['abo-amount'] < 0 ){
				throw new BadFunctionCallException('L\'information "montant ht d\'abonnement" est manquante ou fausse.');
			}

			if( !isset($_REQUEST['abo-testing']) || !is_numeric($_REQUEST['abo-testing']) || $_REQUEST['abo-testing'] < 0 ){
				throw new BadFunctionCallException('L\'information "nombre de jours d\'essai" est manquante ou fausse.');
			}

			if( !isset($_REQUEST['abo-user']) || !is_numeric($_REQUEST['abo-user']) || $_REQUEST['abo-user'] <= 0 ){
				throw new BadFunctionCallException('L\'identifiant du compte RiaStudio est manquant ou faux.');
			}

			if(
				!isset($_REQUEST['cfg']) || !is_array($_REQUEST['cfg'])
				|| !ria_array_key_exists(['dev_need_activation', 'fdv_usr_suspect_active', 'fdv_usr_prospect_active'], $_REQUEST['cfg'])
			){
				throw new BadFunctionCallException('Les variables de configuration sont manquantes.');
			}

			if( !isset($_REQUEST['sendmail']) ){
				throw new BadFunctionCallException('L\'information "envoi mail de confirmation" est manquante.');
			}
		}

		$old_ria_db_connect = $ria_db_connect;
		$old_config = $config;
		RegisterGCPConnection::init($_REQUEST['tenant'], true);

		// Récupère l'identifiant du tenant dans le registre
		$key_gcp = RegisterGCP::getRegisterKey( $_REQUEST['tenant'] );
		if( trim($key_gcp) == '' ){
			throw new BadFunctionCallException('Impossible de récupérer la clé du locataire.');
		}

		// Permet de désactiver la création de la cat_root par défaut
		$_REQUEST['no_cat_root'] = true;

		// Création du site Yuto
		$wst_yuto = Monitoring::addWebsite( $_REQUEST['tenant'], _WST_TYPE_FDV, 'Yuto', $_REQUEST);

		// Défini les droits pour le nouveau site
		Monitoring::setWebsiteRights( $_REQUEST['tenant'], $wst_yuto, _WST_TYPE_FDV, $_REQUEST['package'], true );

		// Mise à jour du package
		$monitoring = new Monitoring();
		$monitoring->setPackage( $key_gcp, $_REQUEST['package'] );

		$ria_db_connect = $old_ria_db_connect;
		$config = $old_config;

		$result = true;
		break;
}

///@}