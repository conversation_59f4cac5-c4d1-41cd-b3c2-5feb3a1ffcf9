<?php
/**
 * Unit test class for the AnonClassDeclaration sniff.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2006-2019 Squiz Pty Ltd (ABN **************)
 * @license   https://github.com/squizlabs/PHP_CodeSniffer/blob/master/licence.txt BSD Licence
 */

namespace PHP_CodeSniffer\Standards\PSR12\Tests\Classes;

use PHP_CodeSniffer\Tests\Standards\AbstractSniffUnitTest;

class AnonClassDeclarationUnitTest extends AbstractSniffUnitTest
{


    /**
     * Returns the lines where errors should occur.
     *
     * The key of the array should represent the line number and the value
     * should represent the number of errors that should occur on that line.
     *
     * @return array<int, int>
     */
    public function getErrorList()
    {
        return [
            28 => 3,
            30 => 1,
            31 => 4,
            32 => 1,
            33 => 1,
            34 => 1,
            35 => 2,
            36 => 1,
            37 => 3,
            39 => 1,
            40 => 1,
            43 => 3,
            44 => 4,
            45 => 1,
            48 => 1,
            52 => 3,
            53 => 1,
            54 => 1,
            55 => 1,
            56 => 2,
            63 => 1,
            75 => 1,
        ];

    }//end getErrorList()


    /**
     * Returns the lines where warnings should occur.
     *
     * The key of the array should represent the line number and the value
     * should represent the number of warnings that should occur on that line.
     *
     * @return array<int, int>
     */
    public function getWarningList()
    {
        return [];

    }//end getWarningList()


}//end class
