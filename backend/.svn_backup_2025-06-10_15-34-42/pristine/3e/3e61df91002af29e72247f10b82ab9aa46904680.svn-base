<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_FIELD');

	if (! isset($_GET['mdl'], $_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception('Erreur post !');
	
	$mdl = $_GET['mdl'];
	$source = $_POST['source'];
	$target = $_POST['target'];
	$action = $_POST['action'];
	
	require_once('fields.inc.php');
	
	$response = array('success' => fld_model_fields_position_update($mdl, $source, $target, $action));
	
	print json_encode($response);
	exit;
