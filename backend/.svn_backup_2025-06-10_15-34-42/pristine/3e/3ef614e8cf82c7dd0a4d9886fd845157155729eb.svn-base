/**
 * CSS de Commandes
 */


/* Tableau de la liste des commandes */
#table-liste-commandes {
    width: 100%;
    #ord-id, #ord-state {
        width: 100px;
    }
    #ord-date, #ord-ht, #ord-ht {
        width: 120px;
    }
    #lst_orders {
        #td-ord-id {
            width: 200px;
        }
    }
    #td-ord-id {
        ul {
            list-style: none;
            margin: 10px 0 0 0;
        }
    }
    @include media('<=medium') {
        .align-right {
            text-align: left !important;
        }
    }
}

#table-une-commande{
    max-width: 100%;
    table-layout: fixed;
    .bloc-ghost {
        width: 180px;
        height: 0;
    }
    tbody tr {
        &.order-products-row {
            > td {
                padding-right: 0 !important;
            }
            td:first-child  {
                width: 100%;
            }
        }
        td:not(.multi-colspan):not(.td-engrenage):not(#adresses):first-child {
            width: 190px !important;
            @include media('<=medium') {
                width: 100% !important;
            }
        }
        @include media('<medium') {
            #adresses.multi-colspan {
                padding-right: 0 !important;
            }
        }
    }
    #table-adresse-livraison {
        width: 100%;
    }
    tfoot > tr:first-child > td:first-child {
        white-space : normal;
    }
    tfoot > tr:last-child > td:first-child {
        white-space : nowrap;
    }
}

.tb_invoice {
    .multi-colspan {
        span {
            display: inline-block;
            &.title-detail {
                width: 190px;
            }
        }
    }
    .first-col {
        width: 190px !important;
    }
    @include media('<large') {
        #ord-products-articles {
            .td-prd-ref-name-1, .padding-top-5 {
                display: block !important;
            }
        }
        .ord-prd-row {
            td {
                width: 100% !important;

            }
        }
    }
}

#table-date-montant {
    min-width: 0 !important;
    @include media('<medium') {
        max-width: 270px;
        tr, td {
            width: 100% !important;
        }
        th {
            display: none;
        }
        td {

            text-align: left !important;
            &::before {
                display: inline-block;
                font-weight: bold;
                margin-right: 10px;
            }
            &:first-child::before {
                content: 'Date : ';
            }
            &:last-child::before {
                content: 'Montant : ';
            }
        }
    }
}

/* table Acomptes */
#table-acompte {
    width: 100%;
}

/* Tableau colis */
#tb-colis {
    @include media('<medium') {
        tr, td {
            width: 100% !important;
        }
        th {
            display: none;
        }
        td {
            &::before {
                display: inline-block;
                font-weight: bold;
            }
            &:first-child::before {
                content: 'Bon de livraison : ';
            }
            &:nth-child(2)::before {
                content: 'Service de livraison : ';
            }
            &:nth-child(3)::before {
                content: 'Numéro de colis : ';
            }
            &:last-child::before {
                content: 'Etiquette : ';
            }
        }
    }
}

/* Adresses de facturation/livraison */
#ord-adresses-row {
    td#adresses {
        padding: 0;
        width: 100%;
        #ord-addresses {
            display: flex;
            width: 100%;
            flex-wrap: wrap;
            .th-info-client, .th-user-adress-facture, .th-user-adress-livraison {
                background: #DADCFF;
                padding: 5px 0 5px 10px;
                color: #232E63;
                font-size: 13px;
                font-weight: 600;
            }
            .th-user-adress-facture, .th-user-adress-livraison {
                flex-grow: 1;
                flex-shrink: 1;
                flex-basis: 0%;
                @include media('<medium') {
                    @media screen {
                        width: 100%;
                        flex: 0 1 auto;
                    }
                }
            }
            .th-user-adress-facture {
                order: 1;
            }
            .th-user-adress-livraison {
                order: 2;
                @include media('<medium') {
                    @media screen {
                        order: 5;
                    }
                }
            }
            .th-info-client {
                width: 100%;

            }
            #ord-addresses-compte-client {
                width: 100%;
                order: 3;
                padding: 5px 0 5px 10px;
                @include media('<medium') {
                    @media screen {
                        order: 2;
                    }
                }
                div {
                    display: inline-block;
                }
            }
            .adresse-facturation {
                order: 4;
                @include media('<medium') {
                    @media screen {
                        order: 3;
                    }
                }
            }
            .adresse-facturation, .adresse-livraison {
                padding: 5px 0 5px 10px;
                flex-grow: 1;
                flex-shrink: 1;
                flex-basis: 0%;
                @include media('<medium') {
                    @media screen {
                        width: 100%;
                    }
                }
            }
            .adresse-livraison {
                order: 5;
                @include media('<medium') {
                    @media screen {
                        order: 6;
                    }
                }
            }

            .details-adresse {
                width: 100%;
                display: flex;
            }
            #details-supp-email {
                width: 100%;
                display: flex;
                order: 6;
                padding: 5px 0 5px 10px;
                margin-top: 5px;
                @include media('<medium') {
                    @media screen {
                        order: 4;
                    }
                }
            }
            .title-detail {
                display: inline-block;
                width: 190px;
                @include media('<medium') {
                    @media screen {
                        width: 100px;
                    }
                }
            }
            .td-engrenage {
                padding-top: 6px !important;
            }
        }
    }
}
.ord-prd-info {
    padding-top: 8px;
    padding-left: 3px;
}


/* Liste des articles */
#ord-products-articles {
    max-width: 100%;
    table-layout: fixed;
    th {
        .th-prd-comment {
            min-width: 150px;
        }
    }
    tbody tr.ord-prd-row {
        padding-right: 10px;
        td {
            &.td-engrenage:first-child {
                width: 20px !important;

            }
            .ord-prd-price-input {
                width: 80px;
                text-align: right;
            }
            .ord-prd-qte-input,
            .ord-prd-discount-input {
                width: 50px !important;
                text-align: right;
            }
            .edit-cat {
                margin-left: 0;
            }
            .ord-prd-comment-input, .ord-prd-name-input {
                width: 100% !important;
                min-width: 150px;
                max-width: 100%;
                display: block;
            }
            .ord-prd-ref-name {
                width: 100%;
                .ord-prd-nmc-row-main, .ord-prd-nmc-row {
                    width: 100%;
                    display: flex !important;
                    .td-prd-ref-name-1 {
                        word-break: break-word; 
                        padding-right: 3px;
                        @include media('<large') {
                            width: 100px !important;
                        }
                        width: 125px !important;
                        display: inline-block;
                        vertical-align: top;
                    }
                    .td-prd-designation {
                        display: inline-block;
                        flex-grow: 1;
                        flex-shrink: 1;
                        flex-basis: 0%;
                        .prd-designation-title {
                            display: flex;
                            align-items: center;
                            .ord-prd-qte {
                                width: 15px !important;
                                text-align: right;
                            }
                        }
                    }
                }
                .ord-prd-nmc-row {
                    padding-left: 15px !important;
                    padding-top: 12px !important;
                    font-size: 10px;
                }
            }
            @include media('<large') {
                @media screen {
                    &.td-remise {
                        display: flex;
                        flex-wrap: wrap;
                        .ord-prd-discount-input {
                            margin-right: 3px;
                        }
                    }
                }
            }
        }
    }

    .ord-prd-discount-select {
        width: 100px;
    }
    /* le td de la dernière roue crantée */
    .last-td {
        padding-left: 5px !important;
    }

    /* Responsive */
    @include media('<sxlarge') {
        @media screen {
            th,
            td,
            tr,
            .td-prd-ref-name-1,
            .td-prd-designation {
                display: flex;
                flex-wrap: wrap;
                width: 100% !important;
            }
            .thead-none {
                display: none;
            }
            td, th{
                &[data-label]::before {
                    content: attr(data-label);
                }
            }
            td,
            tr{
                width: 100% !important;
                box-sizing: border-box;
            }
            td {
                text-align: left;
                .ord-prd-name-input {
                    max-width: 100%;
                }

                &.td-prd-prix-unitaire::before,
                &.td-prd-quantite::before,
                &.td-prd-remise::before,
                &.td-prd-total::before {
                    font-weight: bold;
                    display: inline-block;
                }

                &.td-prd-prix-unitaire::before {
                    content: 'Prix Unitaire : ';
                    padding-right: 5px;
                }
                &.td-prd-quantite::before {
                    content: 'Quantité : ';
                    padding-right: 5px;
                }
                &.td-prd-remise::before {
                    content: 'Remise : ';
                    padding-right: 5px;
                }
                &.td-prd-total::before {
                    content: 'Total : ';
                    padding-right: 5px;
                }
                &.align-right {
                    text-align : inherit !important;
                }
            }
            tbody tr:nth-child(even) {
                background-color: $grey-color;
            }
        }
    }
}

#table-retours, #order-models {
    width: 100%;
    #ord-id {
        width: 50px;
    }
    #ord-date {
        width: 200px;
    }
    #ord-products, #ord-ht, #ord-ttc {
        width: 160px;
    }
}
.th-150 {
    width: 150px;
}
#table-champs-personnalises {
    width: 100%;
    tr {
        vertical-align: middle !important;
    }
}

#ord-products {
    width: 100%;
    margin: 0 !important;
    border-style: none !important;
    thead tr th {
        background-color: #f1f1f1 !important;
        .th-ord-prod-20 {
            width: 20px;
        }
        .th-ord-prod-125 {
            width: 125px;
        }
        #ord-pos {
            width: 40px;
        }
    }

}

.th-ord-prod-100 {
    width: 100px;
}

.td-padding-0 {
    padding: 0;
}

/* Models */
#table-propriete-model {
    width: 100%;
    #td-propr-model {
        width: 150px;
    }
}
.table-articles {
    width: 100%;
    .th-art-w20 {
        width: 20px;
    }
    .th-art-w150 {
        width: 150px;
    }
    .th-art-w180 {
        width: 180px;
    }
    #ord-pos {
        width: 40px;
    }
}
#autorization {
    width: 100%;
    .th-autor-mod-1 {
        width: 20px;
    }
    .th-autor-mod-2 {
        width: 200px;
    }
    &.ncmd_model_rights {
        margin-top: 10px;
    }
}
#popup-content {
    #tb-popup-catalogue {
        th:nth-child(2) {
            padding-left: 0;
        }
        tbody tr td:nth-child(2){
            padding-left: 0;
        }
    }
}

/* popup ncmd-right */
#popup-content {
    .ncmd_model_rights {
        select.little_input {
            width: 320px;

        }
        .col_second {
            display: flex;
            .little_input[type="text"] ,
            .little_input[name="choose_new_usr"] {
                width: auto;
                margin-left: 10px;
            }
        }
        .col_second select.little_input {
            width: 280px;
        }
    }
}

/* Retours */
form#form-return {
    #td-form-return-1 {
        width: 200px;
    }
    #td-form-return-2 {
        width: 300px;
        input {
            width: 150px;
        }
    }
}

.list {
    width: 100%;
    #ord-id, #ord-ht, #ord-ttc {
        width: 100px;
    }
    #ord-date, #ord-products {
        width: 150px;
    }
}

#popup-content pre {
    background-color: $grey-color;
    padding: 15px;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin-bottom: 10px;
}

/**
 * Zone "Pipeline commercial"
 */

.sign-rate-block, .sign-date-block {
    min-width: 300px;
    max-width: 400px;

    #text-sign-date, #text-sign-rate {
        font-style: italic;
        margin-bottom: 0 !important;
        text-align: right;
    }

    #slider-sign-rate, #slider-sign-date {
        appearance: none;
        background-color: #FFF;
        display: block;
        height: 24px;
        margin-top: .25rem;
        overflow: hidden;
        width: 100%;

        &:focus {
            outline: none;
        }

        &::-webkit-slider-runnable-track {
            appearance: none;
            background: #606F7B;
            border: 0 solid #000;
            border-radius: 1px;
            box-shadow: 0 0 0 #000;
            cursor: pointer;
            height: 5px;
            width: 100%;
        }

        &::-webkit-slider-thumb {
            appearance: none;
            background: #DE751F;
            border: 0 solid #2497E3;
            border-radius: 50%;
            box-shadow: 0 0 0 #000;
            cursor: pointer;
            height: 18px;
            margin-top: -6.5px;
            width: 18px;
        }

        &::-moz-range-track {
            background: #606F7B;
            border: 0 solid #000;
            border-radius: 1px;
            box-shadow: 0 0 0 #000;
            cursor: pointer;
            height: 5px;
            width: 100%;
        }

        &::-moz-range-thumb {
            background: #DE751F;
            border: 0 solid #2497E3;
            border-radius: 50%;
            box-shadow: 0 0 0 #000;
            cursor: pointer;
            height: 18px;
            width: 18px;
        }

        &::-ms-track {
            background: transparent;
            border-color: transparent;
            color: transparent;
            cursor: pointer;
            height: 5px;
            width: 100%;
        }

        &::-ms-fill-lower {
            background: #DE751F;
            border: 0px solid #000000;
            border-radius: 2px;
            box-shadow: 0px 0px 0px #000000;
        }

        &::-ms-fill-upper {
            background: #606F7B;
            border: 0px solid #000000;
            border-radius: 2px;
            box-shadow: 0px 0px 0px #000000;
        }

        &::-ms-thumb {
            background: #DE751F;
            border: 0px solid #2497E3;
            border-radius: 50%;
            box-shadow: 0px 0px 0px #000000;
            cursor: pointer;
            height: 18px;
            margin-top: 1px;
            width: 18px;
        }

        &:focus::-ms-fill-lower, &::-moz-range-progress {
            background: #DE751F;
        }

        &:focus::-ms-fill-upper, &::-moz-range-track {
            background: #606F7B;
        }
    }
}

/**
 * Règles CSS pour la version imprimable
 */

@media print {
    // Modifications du tableau d'une commande
    #table-une-commande{
        tbody tr > td:not(.multi-colspan):not(.td-engrenage):not(#adresses):first-child {
            width: 190px !important;
        }
        td.large {
            width: auto;
        }
        #ord-adresses-row td#adresses #ord-addresses .title-detail{
            width: 100px;
            margin-right: 3px;
        }
        .bloc-ghost {
            width: 0;
        }
        #ord-products-articles{
            .th-prd-comment {
                width: 220px !important;
            }
            .col80px {
                width: 80px !important;
                padding-right: 10px;
            }
            .col100px {
                width: 100px !important;
            }
            .td-prd-ref-name-1, .td-remise.td-prd-remise {
                width: 100px !important;
            }
            .ord-pos.col40px, th:last-child {
                width: 0px;
                padding: 0 !important;
                padding-right: 0 !important;
            }
            .ref-des {
                .col125px {
                    width: 100px !important;
                }
            }
            .ria-cell-move {
                padding: 0 !important;
                * {
                    display: none !important;
                }
            }
            .discount {
                width: 135px !important;
                text-align: center !important;
            }
            .ord-prd-row:last-of-type {
                border-bottom: 1px solid grey !important;
            }
            tfoot  {
                border-top: none;
                tr {
                    display: flex !important;
                    width: 100% !important;
                }
                th.align-right, td.multi-colspan {
                    width: auto !important;
                    display: inline-block !important;
                    border: none;
                }
            }
        }
        .ord-action-reward {
            text-align: left;
        }
    }
    // Modifications du tableau de Champs personnalisés
    #table-champs-personnalises{
        #model-pick {
            vertical-align: middle;
        }
    }

    // Modifications communes aux 2 tableaux de la page d'une commande
    #table-champs-personnalises, #table-une-commande{
        & > tbody > tr  {
            & > td {
                width: auto !important;
                display: inline-block !important;
            }
        }
        td, .ord-prd-row, #ord-products-articles tr{
            page-break-inside: avoid;
        }
    }
}