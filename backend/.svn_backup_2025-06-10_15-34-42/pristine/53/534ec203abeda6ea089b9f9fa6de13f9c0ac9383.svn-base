<?php
    /** \file import-orders.php
	 *
	 * 	Ce script est destiné à importer les commandes d'Harmonia.
	 */

    require_once("imports.inc.php");
    require_once("define.inc.php");



    // Lors de l'éxécution de l'import le fichier est de nouveau téléchargé à partir du ftp les modifications sur celui-ci ne sont pas prise en compte
    // Il faut donc créer un import de type file pour pouvoir sauvegarder l'ajout de l'entête au fichier.

    foreach( $configs as $config ){
        $owner_id = 0;

        // Information de connexion au ftp
        $url = "**************";
        $login = "yuto";
        $password = "Y-F2019_!a!";

        $col_sep = ';';
        $text_separator = '"';

        // import des commandes
        {
            $fld_type = 101477;
            $fld_code = 101512;

            $orders_directory = "YUTO-SYNC/X3toYUTO/cdes";

            $ftp_conn = ftp_connect( $url );

            if( !ftp_login( $ftp_conn, $login, $password ) ){
                // Return the resource
                throw new Exception("import-data-harmonia : Impossible d'établir une connexion au ftp");
            }

            ftp_pasv($ftp_conn, true);
            $ftp_files = ftp_nlist($ftp_conn, $orders_directory);
            $files = [];
            foreach ($ftp_files as $file) {
                $file = str_replace($orders_directory.'/', '', $file);
                if( preg_match("/^commandes_([0-9]{12}).txt$/", $file, $matches) ){
                    $files[$matches[1]] = $orders_directory.'/'.$file;
                }
            }
            ksort($files);

            foreach( $files as $filename ){
                $error_order = false;
                $name = $filename;
                $ext = pathinfo( $filename, PATHINFO_EXTENSION );
                try{
                    $file = new ImportFile( $filename, 'ftp', $name );

                    $file->connectToFtp($url, $login, $password );

                    $file->saveFile();

                    // Ajoute un entête au fichier
                    {
                        $handle = $file->openFile();
                        $header = [
                            'ord_id',
                            'piece',
                            'user_ref',
                            'ord_date',
                            'order_ref',
                            'statut',
                            'type',
                            'code_liste',
                            'date_livr',
                            'line_id',
                            'prd_ref',
                            'price_ht',
                            'qte',
                            'tva',
                            'seller_ref',
                            'Reliquat',
                        ];
                        $file_data = implode(';', $header).PHP_EOL;
                        $file_data .= $file->getContent();
                        file_put_contents($file->localFilePath, $file_data);

                        $file->closeFile();
                    }
                    $file->readFile( $col_sep, $text_separator );

                    if( !$file->checkColsAndLines() ){
                        error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                        $error_order = true;
                    }

                }
                catch(Exception $e){
                    error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                    $error_order = true;
                }

                // Création de l'import des modèle de commandes
                if( !$error_order ){
                    if( !$imp_id = ipt_imports_add(
                        $ext,
                        $file->original_filename,
                        $file->lines_count,
                        $file->getColumnsCount(),
                        $owner_id,
                        CLS_ORDER,
                        'create',
                        'add/upd',
                        $file->localFilePath,
                        $file->getCharset(),
                        $col_sep,
                        $text_separator,
                        $file->getSize(),
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        false,
                        true
                    )
                    ){
                        error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import des commandes.');
                        $error_order = true;
                    }
                }

                // Création du mapping
                if( !$error_order ){
                    do{
                        if (!ipt_mapping_add( $imp_id, 0, 'ORD_ID', 'ord_id', 'ord-id', 'id', null, 'fr', '', null, null, 0, 0, '', '', 0, true )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 1, 'ORD_PIECE', 'piece', 'piece', 'piece', null, 'fr', '', null, null, 0, 0, '', '', 0, false )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 2, 'ORD_USR', 'user_ref', 'user-ref', 'ref', null, 'fr', '', null, 'ref' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 3, 'ORD_DATE', 'ord_date', 'ord-date', null, null, 'fr' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 4, 'ORD_REF', 'order_ref', 'order-ref', null, null, 'fr' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 5, 'ORD_STATE', 'statut', 'statut', null, null, 'fr', '', null, null, 0, 0, '{"8":"facture","10":"annule"}' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 6, 'FLD', 'type', 'type', null, null, 'fr', '', null, null, 0,  $fld_type)) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 7, 'FLD', 'code_liste', 'code-liste', null, null, 'fr', '', null, null, 0,  $fld_code)) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 8, 'ORD_DATE_LIVR', 'date_livr', 'date-livr', null, null, 'fr' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 9, 'ORD_LINE', 'line_id', 'line-id', null, null, 'fr' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 10, 'ORD_PRD_ID', 'prd_ref', 'prd-ref', null, null, 'fr', '', null, 'ref' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 11, 'ORD_PRD_PRICE', 'price_ht', 'price-ht', null, null, 'fr' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 12, 'ORD_PRD_QTE', 'qte', 'qte', null, null, 'fr' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 13, 'ORD_PRD_TVA', 'tva', 'tva', null, null, 'fr' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 14, 'ORD_SELLER', 'seller_ref', 'seller-ref', null, null, 'fr', '', null, 'ref' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                        if (!ipt_mapping_add( $imp_id, 15, 'ORD_PRD_INVOICE', 'Reliquat', 'reliquat', null, null, 'fr', '', null, null, 0, 0, '{"bool":["0"]}' )) {
                            error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import des commandes.');
                            $error_order = true;
                            break;
                        }
                    }while(false);
                }

                if( !$error_order ){
                    if( !ipt_imports_exec( $imp_id ) ){
                        ipt_imports_set_state( $imp_id, 'pending' );
                        error_log(__FILE__.':'.__LINE__.':'.$imp_id.' Une erreur est survenue lors de l\'exécution de l\'import des commandes.');
                    }
                    ftp_delete($ftp_conn, $filename);
                }
            }
        }
    }