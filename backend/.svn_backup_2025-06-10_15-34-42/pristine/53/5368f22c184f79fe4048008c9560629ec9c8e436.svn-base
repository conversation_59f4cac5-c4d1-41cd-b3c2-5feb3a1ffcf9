<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  27603 => 'MTN',
  27604 => 'MTN',
  27605 => 'MTN',
  27606 => 'Vodacom',
  27607 => 'Vodacom',
  27608 => 'Vodacom',
  27609 => 'Vodacom',
  27610 => 'Cell C',
  27611 => 'Cell C',
  27612 => 'Cell C',
  27613 => 'Cell C',
  27614 => 'Telkom Mobile',
  27615 => 'Cell C',
  27616 => 'Cell C',
  27617 => 'Cell C',
  27618 => 'Cell C',
  27619 => 'Cell C',
  2762 => 'Cell C',
  27630 => 'MTN',
  27631 => 'MTN',
  27632 => 'MTN',
  27633 => 'MTN',
  27634 => 'MTN',
  27635 => 'MTN',
  27636 => 'Vodacom',
  27637 => 'Vodacom',
  27638 => 'MTN',
  27639 => 'MTN',
  27640 => 'MTN',
  27641 => 'Cell C',
  27642 => 'Cell C',
  27643 => 'Cell C',
  27644 => 'Cell C',
  27645 => 'Cell C',
  27646 => 'Vodacom',
  27647 => 'Vodacom',
  27648 => 'Vodacom',
  27649 => 'Vodacom',
  27660 => 'Vodacom',
  27661 => 'Vodacom',
  27662 => 'Vodacom',
  27663 => 'Vodacom',
  27664 => 'Vodacom',
  27665 => 'Vodacom',
  27710 => 'MTN',
  27711 => 'Vodacom',
  27712 => 'Vodacom',
  27713 => 'Vodacom',
  27714 => 'Vodacom',
  27715 => 'Vodacom',
  27716 => 'Vodacom',
  27717 => 'MTN',
  27718 => 'MTN',
  27719 => 'MTN',
  2772 => 'Vodacom',
  2773 => 'MTN',
  2774 => 'Cell C',
  27741 => 'Virgin Mobile',
  2776 => 'Vodacom',
  2778 => 'MTN',
  2779 => 'Vodacom',
  27810 => 'MTN',
  27811 => 'Telkom Mobile',
  27812 => 'Telkom Mobile',
  27813 => 'Telkom Mobile',
  27814 => 'Telkom Mobile',
  27815 => 'Telkom Mobile',
  27816 => 'WBS Mobile',
  27817 => 'Telkom Mobile',
  27818 => 'Vodacom',
  2782 => 'Vodacom',
  2783 => 'MTN',
  2784 => 'Cell C',
);
