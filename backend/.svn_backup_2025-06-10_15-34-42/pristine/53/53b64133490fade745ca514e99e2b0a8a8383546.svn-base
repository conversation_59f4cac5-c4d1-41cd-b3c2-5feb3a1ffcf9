<?php 
    $is_ajax = false;
	
    $r_services = dlv_services_get();
    if( !$r_services ){
        $error = _('Une erreur s\'est produite lors du chargement des services de livraison disponibles');
    }   

    //si requete ajax 
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}

	if( !$is_ajax ) {
        define('ADMIN_PAGE_TITLE', _('Sélection du service de livraison'));
        define('ADMIN_HEAD_POPUP', true);
        define('ADMIN_ID_BODY', 'popup-content');
        require_once('admin/skin/header.inc.php');
    }
    if (isset($error)) {
		print '<div class="error">'.nl2br($error).'</div>';
		exit;
    } elseif(!isset($error) && ria_mysql_num_rows($r_services)) {
        print '<div class="notice">'._('Veuillez sélectionner un service de livraison.').'</div>';
    }

    $bl_id = isset($_GET['bl_id']) && intval($_GET['bl_id']) ? intval($_GET['bl_id']) : null;
?>
<table class="srv-table checklist">
    <caption><?php print _('Liste des services disponibles'); ?></caption>
	<thead>
		<tr>
			<th id="name"><?php print _('Nom du service'); ?></th>
			<th id="select-service" class="col100px"></th>
		</tr>
	</thead>
	<tbody>
    
    <?php
        if($r_services){
            if( !ria_mysql_num_rows($r_services) ){
                print '<tr><td colspan="2">'._('Aucun service de livraison').'</td></tr>';
            }else{
                while( $r = ria_mysql_fetch_assoc($r_services) ){
                    print '<tr id="line-' . $r['id'] . '">';
                    print 	    '<td headers="name">'.htmlspecialchars($r['name']).'</td>';
                    print 		'<td headers="select-service" align="center"><a class="dlv-srv-add" name="dlv-srv-add">'._('Sélectionner').'</a></td>';
                    print       '<input type="hidden" name="srv_id" id="srv_id" value="'.$r['id'].'" />';
                    print '</tr>';
                }
            }
        }
    ?>
    </tbody>
</table>

<?php 
    if( !$is_ajax ){
?>
    <script><!--
        $(document).ready(function(){
            $(".dlv-srv-add").click(function(){
                var srv_id = $(this).parents("tr:eq(0)").find("#srv_id").val();
                
                <?php
                if (isset($_GET['bl_id'])) {
                    print 'window.parent.parent_select_delivery_service(srv_id, ' 
                        . (isset($_GET['bl_id']) && intval($_GET['bl_id']) ? intval($_GET['bl_id']) : 'null')
                        . ');';
                } else {
                    print 'window.parent.parent_select_delivery_service(srv_id);';
                }
                ?>
                
                window.parent.hidePopup();
            });
        });
    --></script>

<?php
        require_once('admin/skin/footer.inc.php'); 
    }
?>