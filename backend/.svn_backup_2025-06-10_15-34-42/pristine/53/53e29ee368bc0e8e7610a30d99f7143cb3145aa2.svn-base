<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  336001 => 'Orange France',
  336002 => 'SFR',
  336003 => 'Bouygues',
  336007 => 'SFR',
  336008 => 'Orange France',
  336009 => 'Bouygues',
  33601 => 'SFR',
  33603 => 'SFR',
  336040 => 'SFR',
  336041 => 'SFR',
  336044 => 'SFR',
  336045 => 'SFR',
  336046 => 'SFR',
  336047 => 'SFR',
  336048 => 'SFR',
  336049 => 'SFR',
  336050 => 'SFR',
  336051 => 'SFR',
  336052 => 'SFR',
  336053 => 'SFR',
  336054 => 'SFR',
  336069 => 'SFR',
  33607 => 'Orange France',
  33608 => 'Orange France',
  33609 => 'SFR',
  3361 => 'SFR',
  3362 => 'SFR',
  33634 => 'SFR',
  33635 => 'SFR',
  336360 => 'SFR',
  336361 => 'SFR',
  336362 => 'SFR',
  336363 => 'SFR',
  336364 => 'SFR',
  3364166 => 'SFR',
  3364167 => 'SFR',
  3364168 => 'SFR',
  3364169 => 'SFR',
  33642 => 'Orange France',
  33643 => 'Orange France',
  33645 => 'Orange France',
  33646 => 'SFR',
  33647 => 'Orange France',
  33648 => 'Orange France',
  33650 => 'Bouygues',
  33653 => 'Bouygues',
  33654 => 'Orange France',
  33655 => 'SFR',
  33658 => 'Bouygues',
  33659 => 'Bouygues',
  3366 => 'Bouygues',
  3367 => 'Orange France',
  3368 => 'Orange France',
  33692 => 'Bouygues',
  33693 => 'Bouygues',
  33696 => 'Bouygues',
  33698 => 'Bouygues',
  33699 => 'Bouygues',
);
