<?php

require_once 'Services/Abstract.class.php';
require_once 'Services/Contacts/Type.class.php';

class ContactService extends AbstractService
{
	/**	Identifiant du contact
	 * @var	int
	 */
	protected $id = 0;

	/**	Civilité du contact
	 * @var	array
	 */
	protected $title = [
		'id'	=> 4,
		'name'	=> 'NC'
	];

	/**	Prénom du contact
	 * @var	string|null
	 */
	protected $firstname = null;

	/**	Nom du contact
	 * @var	string|null
	 */
	protected $lastname = null;

	/**	Adresse email du contact
	 * @var	string|null
	 */
	protected $email = null;

	/**	Téléphone du contact
	 * @var	string|null
	 */
	protected $phone = null;

	/**	Fax du contact
	 * @var	string|null
	 */
	protected $fax = null;

	/**	Identifiant de l'image de profil du contact
	 * @var	int|null
	 */
	protected $img_id = null;

	/**	Fonction du contact
	 * @var	string|null
	 */
	protected $func = null;

	/**	Identifiant du type de contact
	 * @var	int
	 */
	protected $type_id = 0;

	/**	Tableau des informations du type associé au contact
	 * @var	null|array
	 */
	protected $type = null;

	/**	Tableau des attributs de requête
	 * @var	array
	 */
	protected $attributes = [];

	/**	Constructeur de la classe
	 * @param	int		$cnt	Obligatoire, Identifiant d'un contact
	 * @param	int		$type	Optionnel, Identifiant du type associé au contact
	 * @param	bool	$load	Optionnel, True pour charger les informations du contact
	 * @return	void
	 */
	public function __construct($cnt, $type = 0, $load = true)
	{
		$this->id = is_numeric($cnt) && $cnt > 0 ? (int)$cnt : 0;
		$this->type_id = is_numeric($type) && $type > 0 ? (int)$type : 0;

		if (is_bool($load) && $load) {
			$this->__loadContact();
		}
	}

	/**	Charge les informations de base du type de contact
	 * @return	ContactService	L'instance en cours
	 */
	private function __loadContact()
	{
		if ($this->id < 1) {
			return $this;
		}
		global $config;

		$sql = '
			select
				cnt.cnt_id as cnt,
				gt.title_id,
				gt.title_name,
				cnt.cnt_firstname as firstname,
				cnt.cnt_lastname as lastname,
				cnt.cnt_email as email,
				cnt.cnt_phone as phone,
				cnt.cnt_fax as fax,
				cnt.cnt_img_id as img_id,
				fct.fct_name as func,
				ctt.ctt_type_id as type
			from
				cnt_contacts cnt

			inner join
				cnt_contacts_types ctt
			on
					ctt.ctt_tnt_id = ' . $config['tnt_id'] . '
				and ctt.ctt_cnt_id = cnt.cnt_id
				' . ($this->type_id > 0 ? 'and ctt_type_id=' . $this->type_id : '') . '

			inner join
				gu_titles gt
			on
					cnt.cnt_title_id=gt.title_id

			left join
				cnt_functions fct
			on
					fct.fct_tnt_id = ' . $config['tnt_id'] . '
				and fct.fct_id = cnt.cnt_fct_id

			where
					cnt.cnt_tnt_id = ' . $config['tnt_id'] . '
				and cnt.cnt_id = ' . $this->id . '
		';

		$rcnt = ria_mysql_query($sql);

		if (!ria_mysql_num_rows($rcnt)) {
			return $this;
		}
		$cnt = ria_mysql_fetch_assoc($rcnt);

		$this->__set('firstname', $cnt['firstname']);
		$this->__set('lastname', $cnt['lastname']);
		$this->__set('email', $cnt['email']);
		$this->__set('phone', $cnt['phone']);
		$this->__set('fax', $cnt['fax']);
		$this->__set('img_id', $cnt['img_id']);
		$this->__set('func', $cnt['func']);
		$this->__set('title', [
			'id'	=> is_numeric($cnt['title_id']) && $cnt['title_id'] > 0 ? $cnt['title_id'] : 4,
			'name'	=> is_string($cnt['title_name']) && trim($cnt['title_name']) != '' ? $cnt['title_name'] : 'NC'
		]);

		return $this;
	}

	/**	Charge le type de contact
	 * @return	ContactService	L'instance en cours
	 */
	public function type()
	{
		if ($this->id < 1 || $this->type_id < 1) {
			return $this;
		}
		$this->type = new ContactTypeService($this->type_id);

		return $this;
	}

	/**	Retourne un tableau de toutes les informations du type de contact
	 * @return	array	Tableau contenant toutes les informations du type de contact
	 */
	public function getFullData()
	{
		$ar_contact = $this->getData(false);

		$ar_contact['type'] = $this->type instanceof ContactTypeService ? $this->type->getData() : null;

		return $ar_contact;
	}

	/**	Permet de mettre à jour la valeur d'une propriété
	 * @param	string	$property	Obligatoire, Nom de la propriété
	 * @param	mixed	$value		Obligatoire, Valeur de la propriété
	 * @return	ContactService
	 */
	public function __set($property, $value)
	{
		if(!property_exists($this, $property)){
			return $this;
		}
		$this->{$property} = $this->__sanitizeProperty($property, $value);
		return $this;
	}

	/**	Permet de controler la valeur d'une propriété
	 * @param	string	$property	Obligatoire, Nom de la propriété
	 * @param	mixed	$value	Obligatoire, Valeur de la propriété
	 * @return	mixed	La valeur
	 */
	private function __sanitizeProperty($property, $value)
	{
		switch ($property) {
			case 'firstname':
			case 'lastname':
			case 'email':
			case 'phone':
			case 'fax':
			case 'func':
				return is_string($value) && trim($value) != '' ? $value : null;

			case 'img_id':
				return is_numeric($value) && $value > 0 ? $value : null;

			case 'title':
				return !is_array($value) || !isset($value['id'], $value['name']) ? $value : null;

			default:
				return false;
		}
	}
}
