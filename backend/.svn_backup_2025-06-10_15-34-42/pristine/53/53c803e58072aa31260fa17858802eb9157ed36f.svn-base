@charset "UTF-8";
/* Global */
/* Bleu */
/* exemples d'utilisation : dans les tableaux, ex : Outils > Zones d’action / Outils > Bannières */
/* exemples d'utilisation : bg de niveau 2 dans les entêtes de tableaux */
/* exemples d'utilisation : infos */
/* exemples d'utilisation : infos border */
/* Gris */
/* exemples d'utilisation : couleurs utilisées principalement pour  les bordures des tableaux */
/* exemples d'utilisation : footer, rollover */
/* exemples d'utilisation : Couleur utilisée pour le custom du scroll */
/* exemples d'utilisation : Couleur utilisée pour le custom du scroll */
/* Vert */
/* exemples d'utilisation : en filet : donner une nouvelle valeur (vert trop foncé pour l’instant) */
/* exemples d'utilisation : dans les tableaux, ex : Outils > Zones d’action / Outils > Bannières en filet : donner une nouvelle valeur (vert trop foncé pour l’instant) // fusionné avec $positive-hover (#e5ffe5) */
/* exemples d'utilisation : // valeur positive */
/* warning (Jaune) */
/* exemples d'utilisation : Filet de block de texte pour les WARNING // Anciennement $yellow-medium */
/* exemples d'utilisation : bg de block de texte pour les WARNING // Anciennement $yellow-light */
/* success */
/* exemples d'utilisation : Filet de block de texte pour les success */
/* exemples d'utilisation : bg de block de texte pour les success */
/* info / notes */
/* exemples d'utilisation : Filet de block de texte pour les notes */
/* exemples d'utilisation : bg de block de texte pour les notes */
/* danger */
/* exemples d'utilisation : Filet de block de texte pour les alertes */
/* exemples d'utilisation : bg de block de texte pour les alertes */
/* exemples d'utilisation : survol d'un élément coloré en $red-pale */
/* Autres */
/* ALERT VARIABLES */
:root {
  --mdc-theme-primary: $dark-color;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fadeIn {
  -webkit-animation-name: fadeIn;
          animation-name: fadeIn;
}

@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.fadeOut {
  -webkit-animation-name: fadeOut;
          animation-name: fadeOut;
}

.progress-bar, .progress-bar-pending {
  width: 100%;
  background-color: #b3b9ff;
  height: 25px;
  text-align: center;
  border-radius: 5px;
}

.bar-progression {
  width: 0px;
  height: 25px;
  background-color: #5377fb;
  position: absolute;
  border-radius: 5px;
  opacity: 0.5;
}

.span-progression {
  color: white;
  font-weight: bold;
  font-size: 14px;
  height: 25px;
  text-align: center;
  vertical-align: middle;
  line-height: 25px;
  position: relative;
}

/** imports/index.php **/
#table-map-file {
  position: relative;
}

.tools-import-customers {
  max-width: 1200px;
}

.tools-import-customers p {
  line-height: 19px;
  color: #232E63;
  padding: 0 5px;
  margin: 0;
}

.tools-import-customers > .block-actions {
  display: flex;
}

@media (max-width: 767px) {
  .tools-import-customers > .block-actions {
    flex-direction: column;
  }
}

.tools-import-customers > .block-actions > div:first-child {
  margin-left: 0;
}

.tools-import-customers > .block-actions > div:nth-child(3) {
  margin-right: 0;
}

.tools-import-customers .block-action {
  width: calc(100% / 3);
  margin: 10px 10px 20px 10px;
  padding: 20px;
  border: 1px solid #ABB2FF;
  border-radius: 4px;
  box-shadow: 0 2px 10px 0 #d2d2d2;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

@media (max-width: 767px) {
  .tools-import-customers .block-action {
    width: 100%;
    margin: 10px 0;
  }
}

.tools-import-customers .block-action .title {
  font-size: 17px;
  color: #232E63;
  border-bottom: none;
  padding-left: 45px;
  margin-top: 0;
  position: relative;
}

.tools-import-customers .block-action .title:before {
  position: absolute;
  content: "";
  left: 0;
  top: 50%;
  margin-top: -18px;
  width: 35px;
  height: 35px;
  background-repeat: no-repeat;
  background-size: contain;
}

.tools-import-customers .block-action.--start-import .title:before {
  background-image: url("/admin/images/imports/import.svg");
}

.tools-import-customers .block-action.--download-example .title:before {
  background-image: url("/admin/images/imports/download.svg");
}

.tools-import-customers .block-action.--file-prepare .title:before {
  background-image: url("/admin/images/imports/table.svg");
}

.tools-import-customers .block-action.--start-import {
  display: block;
  position: relative;
}

.tools-import-customers .block-action.--start-import form {
  height: 100%;
  display: block;
  position: relative;
  padding-bottom: 48px;
}

.tools-import-customers .block-action.--start-import form .input-file-container {
  flex: 1 1 auto;
}

.tools-import-customers .block-action.--start-import form > div:first-child {
  margin-bottom: 20px;
}

.tools-import-customers .block-action.--start-import form select,
.tools-import-customers .block-action.--start-import form input[type="password"],
.tools-import-customers .block-action.--start-import form input[type="text"] {
  width: 100%;
}

.tools-import-customers .block-action.--start-import form label {
  color: #7b82a1;
}

.tools-import-customers .block-action.--start-import form button {
  position: absolute;
  bottom: 0;
  margin-left: auto;
  width: 100px;
  margin-right: auto;
  left: 0;
  right: 0;
}

.tools-import-customers .block-action.--download-example .title + p {
  flex: 1 1 auto;
}

.tools-import-customers .block-action.--file-prepare ul {
  flex: 1 1 auto;
  margin-top: 0;
}

.tools-import-customers .block-action.--download-example, .tools-import-customers .block-action.--file-prepare {
  align-content: space-between;
}

.tools-import-customers .block-action.--download-example button,
.tools-import-customers .block-action.--download-example .button, .tools-import-customers .block-action.--file-prepare button,
.tools-import-customers .block-action.--file-prepare .button {
  align-self: center;
}

.tools-import-customers .block-action button,
.tools-import-customers .block-action .button {
  margin-top: 15px;
  padding: 10px 20px;
  font-size: 12px;
  text-align: center;
}

.tools-import-customers .block-action a.button[target="_blank"] {
  padding-right: 35px;
  margin-right: 0;
}

.tools-import-customers .block-action .field-helper {
  color: #A9A9A9;
}

.tools-import-customers .imports-unfinished-history {
  margin-bottom: 20px;
}

.tools-import-customers .imports-unfinished-file {
  display: flex;
  flex-direction: column;
  padding: 5px 0;
}

.tools-import-customers .imports-unfinished-file h3 {
  border-bottom: none;
  display: block;
  width: 100%;
  margin: 0;
  padding-bottom: 10px;
}

.tools-import-customers .imports-unfinished-file .alldata {
  width: 100%;
}

.tools-import-customers .imports-unfinished-file .unfinished-columns {
  width: 19%;
  display: inline-block;
  vertical-align: middle;
}

@media (max-width: 1229px) {
  .tools-import-customers .imports-unfinished-file .unfinished-columns {
    width: 32%;
  }
}

@media (max-width: 767px) {
  .tools-import-customers .imports-unfinished-file .unfinished-columns {
    width: 100%;
  }
}

.tools-import-customers .imports-unfinished-file .unfinished-columns.state {
  padding-right: 20px;
}

@media (max-width: 1229px) {
  .tools-import-customers .imports-unfinished-file .unfinished-columns.state {
    padding-right: 0;
  }
}

@media (max-width: 767px) {
  .tools-import-customers .imports-unfinished-file .unfinished-columns.state {
    padding-right: 0;
  }
}

.tools-import-customers .imports-unfinished-file .unfinished-columns .imp-name {
  word-break: break-all;
}

.tools-import-customers .unfinished-file-delete {
  position: absolute;
  right: 10px;
  top: calc(50% - 12px);
}

#table-imports .imp-success {
  color: #008000;
}

#table-imports .imp-warning {
  color: #f5a623;
}

#table-imports .imp-err {
  color: #ff0000;
}

.advance-settings {
  background-color: rgba(218, 220, 255, 0.5);
  border: 1px solid #3D50DF;
  border-radius: 5px;
  display: none;
  margin-top: 20px;
  padding: 0 20px;
}

.imp-form-fields {
  margin: 10px 0;
}

.imp-sep-list .check-label {
  display: block;
}

.check-label {
  margin-top: 5px;
}

.input-file-container div {
  margin-bottom: 10px;
}

/** imports/import.php**/
.table {
  width: 100%;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
}

.table-map-head,
.table-map-section,
.table-map-row {
  display: flex;
}

@media (max-width: 767px) {
  .table-map-head,
  .table-map-section,
  .table-map-row {
    flex-direction: column;
  }
}

.table-map-cell {
  padding: 14px 10px;
}

@media (max-width: 1199px) {
  .table-map-cell select,
  .table-map-cell input:not([type="radio"]) {
    /* width: 100%; */
  }
}

.table-map-cell > .table-map-cell {
  padding: 0;
}

.table-map-cell.--cell-save {
  max-width: 180px;
  margin-right: 60px;
}

@media (max-width: 1023px) {
  .table-map-cell.--cell-save {
    margin-right: 15px;
  }
}

.table-map-cell.--cell-cancel {
  flex: 0 1 auto;
}

.table-map-cell.--cell-import {
  text-align: right;
  align-items: flex-end;
  flex: 1 1 auto;
}

.table-map-cell.--column-name {
  font-weight: 600;
  word-wrap: break-word;
}

.table-map-cell.--column-destination {
  position: relative;
}

.table-map-cell.--column-destination .info-click {
  display: none;
  width: 24px;
  height: 24px;
  position: absolute;
  left: 10px;
  top: 14px;
  background-image: url("/admin/images/imports/info-light.svg");
  background-repeat: no-repeat;
  background-size: contain;
  cursor: pointer;
}

@media (max-width: 767px) {
  .table-map-cell.--column-destination .info-click {
    top: 16px;
    left: -20px;
  }
}

.table-map-cell.--column-destination .info-click:hover {
  background-image: url("/admin/images/info-dark.svg");
}

.table-map-cell.--column-destination.--has-notice .info-click {
  display: block;
}

.table-map-cell.--column-destination.--has-notice .option-notice {
  position: absolute;
  display: none;
  z-index: 10;
  top: 0;
  left: -210px;
  width: 420px;
  background: #fff;
  padding: 20px;
  box-shadow: 0 2px 20px 0 rgba(35, 46, 99, 0.4);
  cursor: help;
}

@media (max-width: 767px) {
  .table-map-cell.--column-destination.--has-notice .option-notice {
    position: static;
    width: 100%;
  }
}

.table-map-cell.--column-action {
  width: 100%;
  display: flex;
  padding-top: 0;
}

@media (max-width: 767px) {
  .table-map-cell.--column-action {
    flex-direction: column;
  }
}

.table-map-cell.--column-action .--column-action-info {
  width: 50%;
  padding-left: 60px;
}

@media (max-width: 1199px) {
  .table-map-cell.--column-action .--column-action-info {
    width: 40%;
  }
}

@media (max-width: 767px) {
  .table-map-cell.--column-action .--column-action-info {
    width: 100%;
    padding-left: 0;
  }
}

.table-map-cell.--column-action .--column-action-select {
  width: 50%;
  padding-left: 50px;
}

@media (max-width: 1199px) {
  .table-map-cell.--column-action .--column-action-select {
    width: 60%;
  }
}

@media (max-width: 767px) {
  .table-map-cell.--column-action .--column-action-select {
    width: 100%;
    padding-left: 0;
  }
}

.table-map-cell.--column-action .--column-action-select .map-option {
  position: relative;
}

.table-map-cell.--column-action .--column-action-select .map-option .option-notice {
  top: -15px;
}

.table-map-cell.--column-action .--column-action-select .info-click {
  position: relative;
  left: 0;
  top: -4px;
  float: left;
}

.table-map-section {
  flex-direction: column;
  margin-bottom: 25px;
}

.table-map-title {
  padding: 5px 12px;
  background: #DADCFF;
  border: 1px solid #A9A9A9;
  border-bottom: none;
  font-weight: bold;
  font-size: 16px;
}

.table-map-title span {
  font-weight: normal;
}

.table-map-head,
.table-map-row:not(.--row-bottom-button) {
  flex-wrap: wrap;
  padding-left: 60px;
  padding-right: 20px;
}

.table-map-head > div,
.table-map-row:not(.--row-bottom-button) > div {
  flex: 1 1 auto;
  width: calc(50%/2);
}

@media (max-width: 1199px) {
  .table-map-head > div,
  .table-map-row:not(.--row-bottom-button) > div {
    width: calc(40%/2);
  }
}

@media (max-width: 767px) {
  .table-map-head > div,
  .table-map-row:not(.--row-bottom-button) > div {
    width: 100%;
  }
}

.table-map-head > div:nth-child(3),
.table-map-row:not(.--row-bottom-button) > div:nth-child(3) {
  flex: 2 1 auto;
  padding-left: 50px;
  width: 50%;
}

@media (max-width: 1199px) {
  .table-map-head > div:nth-child(3),
  .table-map-row:not(.--row-bottom-button) > div:nth-child(3) {
    width: 60%;
  }
}

@media (max-width: 767px) {
  .table-map-head > div:nth-child(3),
  .table-map-row:not(.--row-bottom-button) > div:nth-child(3) {
    width: 100%;
    padding-left: 10px;
  }
}

.table-map-head {
  position: static;
  background: #232E63;
  color: #fff;
  font-size: 14px;
}

.table-map-head.fixed-header {
  position: fixed;
  top: 0;
  z-index: 10;
}

.table-map-row {
  border: 1px solid #A9A9A9;
}

.table-map-row + .table-map-row {
  border-top: none;
}

.table-map-row.--row-bottom-button {
  padding-left: 12px;
}

.table-map-row.--row-bottom-button > div {
  width: auto;
}

.table-map-row.--valid {
  background-image: url("/admin/images/imports/alerte-ok-bleue.svg");
  background-repeat: no-repeat;
  background-position: left 20px top 10px;
}

.table-map-row.--warning {
  background-image: url("/admin/images/imports/warning.svg");
  background-repeat: no-repeat;
  background-position: left 20px top 10px;
}

.table-map-row.--linked select:not([multiple]) {
  background-image: url("/admin/images/input-select-alt.svg");
}

@media (max-width: 1199px) {
  .table-map-row.--row-bottom-button .table-map-cell input {
    width: auto;
  }
}

.table-map-row.--no-import {
  background: #EEEEEE;
}
