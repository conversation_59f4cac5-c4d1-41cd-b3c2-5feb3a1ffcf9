<?php
/// \cond onlyria
/** 
 * 	\defgroup api-devices-restrictions Restrictions  
 * 	\ingroup Yuto 
 * 	@{		
 * 	\page api-devices-restrictions-get Chargement
 *
 * 	Cette fonction permet le chargement d'une restriction sous forme de zip 
 *	Lors de la synchronisation Yuto supprime tous ce qu'il a en local pour ajouter ce qui est donné par ce fichier zip.
 *	Le déclenchement à l'appel de ce endpoint ce fait via le retour donnée par riashop sur l'appel PUT /devices/index 
 	(première action de yuto lors du lancement du processus de synchronisation)
 *
 *	\code
 *		GET /devices/restrictions/
 *	\endcode
 *	
 * 	@return un fichier zip contenant les informations 
 *		- cls-1.txt : (Obligatoire) 
 *				contient le insert into en sql de toutes les règles de restriction de la table : prd_restrictions
 *		- cls1.txt : (Obligatoire) 
 *				contient le insert into en sql de toutes les règles calculé pour les produits, de restriction de la table : prd_restrictions_products
 *		- cls4.txt : (Obligatoire) 
 *				contient le insert into en sql de toutes les règles calculé pour les catégories, de restriction de la table : prd_restrictions_categories
 *		- cls5.txt : (Obligatoire) 
 *				contient le insert into en sql de toutes les règles calculé pour les marques, table : prd_restrictions_brands
 * 
 * 	@}
*/

switch( $method ){

	// récupère les éléments des restrictions
	case 'get':

		if( $config['use_catalog_restrictions'] ){
			// récpuère l'utilisateur
			$rusr = gu_users_get($config['usr_id']);
			if( $rusr && ria_mysql_num_rows($rusr) ){

				$usr = ria_mysql_fetch_assoc($rusr);


				$zip_url = dirname(__FILE__).'/../upload-temp/restrictions-'.$config['tnt_id'].'-'.$config['dev_id'].'.zip';
				if( !file_exists($zip_url) ){
					$zip = new ZipArchive();
					if($zip->open($zip_url, ZIPARCHIVE::CREATE) == TRUE){

						$restrictions_cls = array();
						$restrictions_cls[-1] = array();
						$restrictions_cls[CLS_PRODUCT] = array();
						$restrictions_cls[CLS_BRAND] = array();
						$restrictions_cls[CLS_CATEGORY] = array();

							$rr = prd_restrictions_get();
							if( $rr && ria_mysql_num_rows($rr) ){
								while( $r = ria_mysql_fetch_assoc($rr) ){
									$restrictions_cls[-1][] = $r;
								}
							}
							$rprd = prd_restrictions_cache_get(CLS_PRODUCT, -1, -2, 0, 0);
							if( $rprd && ria_mysql_num_rows($rprd) ){
								while( $r = ria_mysql_fetch_assoc($rprd) ){
									$restrictions_cls[CLS_PRODUCT][] = $r;
								}
							}
							$rbrd = prd_restrictions_cache_get(CLS_BRAND, -1, -2, 0, 0);
							if( $rbrd && ria_mysql_num_rows($rbrd) ){
								while( $r = ria_mysql_fetch_assoc($rbrd) ){
									$restrictions_cls[CLS_BRAND][] = $r;
								}
							}
							$rcat = prd_restrictions_cache_get(CLS_CATEGORY, -1, -2, 0, 0);
							if( $rcat && ria_mysql_num_rows($rcat) ){
								while( $r = ria_mysql_fetch_assoc($rcat) ){
									$restrictions_cls[CLS_CATEGORY][] = $r;
								}
							}

					//	}

						$pas = 500;

						// les tablettes avec android 4.0 ( version 15 ) ne supporte pas les insert multiples
						if( $config['dev_os_version'] < 16 && $config['dev_brand']!='Apple' ){
							$pas = 1;
						}

						// préparation de la réponse en compactant les données ( préformaté pour une insertion en base de donnée )
						foreach( $restrictions_cls as $cls_id => $data) {
							$cpt = 0;

							$sql_final = "";
							if( sizeof($data) > 0 ){
								foreach( $data as  $p ){
									if( $cpt>0 && $cpt%$pas == 0 ){
										$sql_final .= ';'."\n";
										$cpt = 0;
									}
									if( $cpt==0 ){
										switch ($cls_id) {
											case CLS_PRODUCT:
												$sql_final .= "insert into prd_restrictions_products (rec_usr_fld_id,rec_usr_value,rec_prd_id) values ";
												break;
											case CLS_BRAND:
												$sql_final .= "insert into prd_restrictions_brands (rec_usr_fld_id,rec_usr_value,rec_brd_id) values ";
												break;
											case CLS_CATEGORY:
												$sql_final .= "insert into prd_restrictions_categories (rec_usr_fld_id,rec_usr_value,rec_cat_id) values ";
												break;
											case -1:
												$sql_final .= "insert into prd_restrictions (rec_id,rec_usr_fld_id,rec_usr_value,rec_fld_id,rec_value,rec_grp_id,rec_symbol) values ";
												break;
										}
									}

									if( $cls_id==-1 ){
										$sql_final .= ($cpt>0 ? ',':'').'('.$p['id'].','
											.( !$p['usr_fld_id'] ? 'null' : $p['usr_fld_id'] ).','
											.( !$p['usr_value'] ? 'null' : '\''.str_replace("'","''",$p['usr_value']).'\'')
											.','.$p['fld_id']
											.',\''.str_replace("'","''",$p['value']).'\''
											.','.$p['grp_id']
											.',\''.str_replace("'","''",$p['symbol']).'\''
											.')';
									}else{

										$sql_final .= ($cpt>0 ? ',':'').'('.( !$p['fld_id'] ? 'null' : $p['fld_id'] ).','.( !$p['val_id'] ? 'null' : '\''.str_replace("'","''",$p['val_id']).'\'').','.$p['obj_id'].')';
									}

									$cpt++;
								}

								$sql_final .= ';'."\n";
							}

							$zip->addFromString('cls'.$cls_id.'.txt',  $sql_final);
						}
						$zip->close();
					}else{
						exit;
					}
				}

				// envoi le zip
				header("Content-type: application/octet-stream");
				header("Content-disposition: attachment; filename=restrictions.zip");
				ob_clean();
				flush();
				readfile($zip_url);
				@unlink($zip_url);
				exit;
			}
		}

		break;
}
/// \endcond
