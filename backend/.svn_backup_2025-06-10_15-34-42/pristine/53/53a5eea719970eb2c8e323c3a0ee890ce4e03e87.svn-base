<?php

// \cond onlyria
/**	\defgroup model_product_values Enregistrement des valeurs
 * 	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à l'enregistrement des valeurs contenus
 *	dans les champs personnalisés.
 *	@{
 */
/// \endcond

// \cond onlyria
/** Cette fonction permet la suppression d'une valeur dans un champ personnalisé pour un objet donné.
 *
 * @param  $obj Obligatoire, Identifiant de l'objet (ou tableau d'identifiants)
 * @param  $class Optionnel, Identifiant de la classe de l'objet. La valeur par défaut est CLS_PRODUCT (1 - Produit)
 * @param  $upd_date_modified Optionnel, Définit si la date de modification de l'objet est mise à jour
 * @return La valeur en cas de succès, false en cas d'échec
 */
function fld_object_values_del( $obj, $class=CLS_PRODUCT, $upd_date_modified=true ){
	if( !fld_classes_exists($class) ){
		return false;
	}

	// Autorise le 0 dans tous les éléments du tableaux (pas parfait).
	$obj = control_array_integer($obj, true, true, true);
	if( $obj === false ){
		return false;
	}

	global $config;

	$sql = '
		delete from
			fld_object_values
		where
			pv_tnt_id='.$config['tnt_id'].'
				and pv_fld_id in (
					select fld_id
					from fld_fields
					where fld_cls_id='.$class.'
						and fld_tnt_id in (0, '.$config['tnt_id'].')
				)
	';

	for( $i=0; $i < count($obj); $i++ ){
		$sql .= ' and pv_obj_id_'.$i.' = '.$obj[$i];
	}

	$result = ria_mysql_query($sql);

	// Mise à jour de la date de modification de l'objet.
	if( $result && $upd_date_modified ){
		fld_objects_set_date_modified($class, $obj);
	}

	return $result;
}
/// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les objects en réalisant un like sur la valeur
 *	@param int $cls_id Type d'objet recherché
 *	@param $value Valeur recherchée
 *	@param int $fld_id Optionnel, identifiant d'un champ avancé
 *	@return array Un tableau contenant les objects
 */
function fld_object_values_get_like( $cls_id, $value, $fld_id=0 ){
	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	if( trim($value) == '' ){
		return false;
	}

	$fld_id = control_array_integer( $fld_id, false );
	if ($fld_id === false) {
		return false;
	}

	global $config;

	$sql = '
		select pv_obj_id_0 as obj_id_0, pv_obj_id_1 as obj_id_1, pv_obj_id_2 as obj_id_2
		from fld_object_values
		where pv_tnt_id = '.$config['tnt_id'].'
			and pv_value like "%'.addslashes( $value ).'%"
	';

	if (is_array($fld_id) && count($fld_id)) {
		$sql .= ' and pv_fld_id in ('.implode(', ', $fld_id).')';
	}

	$res = ria_mysql_query( $sql );

	$ar_objs = array();
	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_objs[] = array( $r['obj_id_0'], $r['obj_id_1'], $r['obj_id_2'] );
		}
	}

	return $ar_objs;
}
/// \endcond

/**	Cette fonction permet le chargement d'une valeur dans un champ personnalisé pour un objet donné (ou un tableau d'objets dans le cas particulier de $multival).
 *	Pour les types hiérarchique et pointeur sur articles, le résultat est retourné traité (référence(s) pour les articles, nom(s) pour la hiérarchie)
 *	@param int|array $obj Obligatoire, identifiant de l'objet ( ou tableau d'identifiants pour les clés composées )
 *	@param int $fld Obligatoire, identifiant du champ personnalisé
 *	@param string $lng Optionnel, code ISO 639-1 de la langue
 *	@param bool $return_val Optionnel, par défaut, on retourne la valeur de restriction s'il s'agit d'une liste hiérarchique, mettre true pour retourner l'identifiant de la valeur. S'il s'agit d'une liste déroulante hierarchique, un tableau contenant le ou les identifiants sera retourné.
 *	@param bool $admin Optionnel, par défaut il ne s'agit par un appel depuis l'espace d'administration, mettre true pour que ce soit le cas et donc désactiver memcached
 *	@param bool $multival Optionnel, permet de déclarer $obj comme étant un tableau d'identifiants simples (par ex. plusieurs identifiants de produits). Ne doit pas être utilisé pour une classe à clé composée (ex. CLS_ORD_PRODUCT), bien que le contrôle ne soit pas effectué.
 *	@param bool $direct Optionnel, ne fonctionne que lorsque la langue en paramètre est différente de la langue par défaut, si la valeur dans la langue n'existe pas on retourne la valeur de la langue par défaut, mettre true pour que ce ne soit pas le cas
 *	@return la valeur en cas de succès (ou un tableau si $multival), false en cas d'échec
 *
 */
function fld_object_values_get( $obj, $fld, $lng='', $return_val=false, $admin=false, $multival=false, $direct=false ){
	global $config, $memcached;

	if (!is_numeric($fld) || $fld<=0) {
		return false;
	}

	$key_mem = $config['tnt_id'].':'.$config['wst_id'].':fld_object_values_get:info_field:'.$fld;
	if ($get = $memcached->get($key_mem)) {
		if ($get == 'none') {
			return false;
		}

		$f = $get;
	}else{
		$r_fld = ria_mysql_query('
			select fld_is_physical as physical, fld_cls_id as cls_id, fld_type_id as type_id
			from fld_fields
			where (fld_tnt_id = 0 or fld_tnt_id = '.$config['tnt_id'].')
				and fld_date_deleted is null
				and fld_id = '.$fld.'
		');

		$f = 'none';
		if ($r_fld && ria_mysql_num_rows($r_fld)) {
			$f = ria_mysql_fetch_assoc($r_fld);
		}

		$memcached->set($key_mem, $f, 60);
		if ($f == 'none') {
			return false;
		}
	}

	if( is_array($obj) ){
		if( $multival ){
			// si multival, le tableau doit contenir au moins un élément
			if( !sizeof($obj) ) return false;
		}else{
			// sinon, le tableau doit contenir entre 1 et COUNT_OBJ_ID éléments (nombre de colonnes "pv_obj_id_x")
			if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		}
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	}else{
		if( !is_numeric($obj) || $obj<=0 ) return false;

		$obj = array( $obj );
	}

	$lng = strtolower(trim($lng));

	if( !$lng || !in_array($lng, i18n_languages_get_array( true )) ){
		$lng = $config['i18n_lng'];
	}

	// si champ physique et langue par défaut
	if( $f['physical'] && $config['i18n_lng'] == $lng ){
		return false;
	}

	$active_memcached = !$admin;

	// liste de champs à ne pas mettre en cache
	$exclude_fields = array(_FLD_BRD_URL, _FLD_CAT_URL, _FLD_PRD_URL, _FLD_STR_URL, _FLD_FAQ_CAT_URL, _FLD_FAQ_QST_URL, _FLD_CMS_URL, _FLD_DOC_TYPE_URL, _FLD_NEWS_URL, _FLD_PRD_EBAY_ID, _FLD_PRD_EBAY_DISABLED, _FLD_PRD_EBAY_END, _FLD_PRD_EBAY_FEES);
	// liste de classes dont les champs ne doivent pas être mis en cache
	$exclude_classes = array(CLS_ORDER, CLS_USER, CLS_ORD_PRODUCT, CLS_INV_PRODUCT, CLS_BL_PRODUCT, CLS_PL_PRODUCT, CLS_INVOICE, CLS_BL, CLS_PMT_CODE);

	if( in_array($fld, $exclude_fields) || in_array($f['cls_id'], $exclude_classes) ){
		$active_memcached = false;
	}

	if( isset($config['fld_memcached_deactivate']) && $config['fld_memcached_deactivate'] ){
		$active_memcached = false;
	}

	global $memcached;

	$memkey = 'fields:fldobjectvaluesget:'.implode(':', $obj).':'.$fld.':'.$lng.':'.$return_val.':'.$multival.':'.$direct;

	if( $active_memcached ){
		$kid = $memcached->get($memkey);
		if( ria_is_memcached_result_ok($memcached) ){
			return $kid;
		}
	}

	$sub_sql = '';
	if( $multival ){
		// pas de clé composé : uniquement un IN sur la colonne 0
		$sub_sql = ' and pv_obj_id_0 in ('.implode(', ', $obj).')';
	}else{
		$i = 0;
		while( $i<sizeof($obj) ){
			$sub_sql .= ' and pv_obj_id_'.$i.'='.$obj[$i];
			$i++;
		}
	}

	$sql = '
		select pv_value as val, pv_lng_code as lng, pv_obj_id_0 as obj_id
		from fld_object_values
		where pv_tnt_id='.$config['tnt_id'].'
			'.$sub_sql.'
			and pv_fld_id='.$fld.'
			and pv_lng_code = "'.$lng.'"
	';

	if( !$direct && $lng != $config['i18n_lng'] ){
		$sql .= '
			union all

			select pv_value as val, pv_lng_code as lng, pv_obj_id_0 as obj_id
			from fld_object_values
			where pv_tnt_id='.$config['tnt_id'].'
				'.$sub_sql.'
				and pv_fld_id='.$fld.'
				and pv_lng_code = "'.$config['i18n_lng'].'"
		';
	}

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
		return false;
	}

	if( $multival ){
		// si langue par défaut pas de problème : on crée un tableau des valeurs retournées
		$val = array();
		while( $row = ria_mysql_fetch_array($r) ){
			if( $lng != $config['i18n_lng'] ){
				// si pas langue par défaut : on peut avoir, pour un même objet, la valeur traduite et la valeur dans la langue par défaut
				// dans ce cas, on crée un tableau associatif qu'on transforme plus bas en tableau simple
				// le tableau associatif a pour clé le obj_id_0 de chaque ligne
				// pour chaque clé, on crée la valeur si pas initialisée (test !isset()) ou on la met à jour si elle était initialisée avec la langue par défaut (hors on choisit de préférence la langue traduite)
				if( !isset($val[ $row['obj_id'] ]) || $val[ $row['obj_id'] ][1] == $config['i18n_lng'] ){
					$val[ $row['obj_id'] ] = array($row['val'], $row['lng']);
				}
			}else{
				$val[] = $row['val'];
			}
		}
		// reconversion en tableau simple
		if( $lng != $config['i18n_lng'] ){
			$temp = array();
			foreach( $val as $k=>$v ){
				$temp[] = $v[0];
			}
			$val = $temp;
		}
	}else{
		$val = ria_mysql_result($r, 0, 'val');
	}

	// si type HIERARCHY, on retourne un tableau de valeurs si $return_val (ce qui n'est pas le cas des types SELECT et SELECT MULTIPLE)
	// donc si on est HIERARCHY et $multival, on retournera un tableau de tableaux
	if( $f['type_id'] == FLD_TYPE_SELECT_HIERARCHY ){
		$values = array();
		if( $return_val ){
			// simule un tableau de tableaux
			if( !$multival ) $val = array($val);
			// procède à l'explode de chaque sous-tableau
			foreach( $val as $v ){
				$values[] = explode(', ', $v );
			}
			// retire la simulation
			if( !$multival ) $values = $values[0];
		}else{
			// simule un tableau de tableaux
			if( !$multival ) $val = array($val);
			// procède à la conversion id => name sur chaque sous-tableau
			foreach( $val as $v ){
				$val_name_tabs = array();
				// explode des ids
				$val_id_tabs = explode(', ', $v);
				foreach( $val_id_tabs as $val_id ){
					// conversion en names
					$val_name = fld_restricted_values_get_name( $val_id );
					if( trim($val_name) )
						$val_name_tabs[] = $val_name;
				}
				// implode des names
				if( sizeof($val_name_tabs) ){
					$values[] = implode(', ', $val_name_tabs);
				}
			}
			if( !sizeof($values) ) return false;
			// retire la simulation
			if( !$multival ) $values = $values[0];
		}
		$val = $values;
	}

	$time_cache = isset($config['time_cache_fields']) && is_numeric($config['time_cache_fields']) && $config['time_cache_fields'] > 0 ? $config['time_cache_fields'] : 3600;
	$memcached->set( $memkey, $val, $time_cache );
	return $val;
}

/**	Cette fonction permet l'enregistrement de plusieurs valeur pour différent champs avancé
 *	Attention les controles sont très limité sur cette fonction, il vaut mieux préférer fld_object_values_set, sauf dans des cas particulier
 *
 *	@param array $data Obligatoire, tableau des données sous la forme :
 *		array(
 *			'cls_id' => 1,
 *			'obj_0' => 1,
 *			'obj_1' => 1,
 *			'obj_2' => 1,
 *			'fld_id' => 1,
 *			'lng' => 'fr',
 *			'value' => 'Val'
 *		)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_object_values_set_multiple( $data ){
	global $config;
	if( !is_array($data) ){
		return false;
	}

	$each_different_obj = array();

	$fields = array( 'pv_tnt_id','pv_fld_id','pv_value', 'pv_obj_id_0', 'pv_obj_id_1', 'pv_obj_id_2', 'pv_lng_code' );
	$lines = array();

	foreach($data as $d){

		if( !ria_array_key_exists( array('value','cls_id','fld_id','obj_0'), $d ) ){
			return false;
		}
		if( !is_numeric($d['cls_id']) || $d['cls_id'] == 0 ){
			return false;
		}
		if( !is_numeric($d['fld_id']) || $d['fld_id'] == 0){
			return false;
		}
		if( !isset($d['lng']) ){
			$d['lng'] = strtolower($config['i18n_lng']);
		}

		if( !is_numeric($d['obj_0']) ){
			return false;
		}

		if( !isset($d['obj_1']) ){
			$d['obj_1'] = 0;
		}
		if( !is_numeric($d['obj_1']) ){
			return false;
		}

		if( !isset($d['obj_2']) ){
			$d['obj_2'] = 0;
		}
		if( !is_numeric($d['obj_2']) ){
			return false;
		}

		// Contrôle si au moins un obj_id contient un identifiant positif
		$has_id_over_zero = false;
		for( $i=0;$i<3;$i++ ){
			if( $d['obj_'.$i] > 0 ){
				$has_id_over_zero = true;
				break;
			}
		}

		if( !$has_id_over_zero ){
			return false;
		}


		if( trim($d['value']) == '' ){

			ria_mysql_query('
				delete from
					fld_object_values
				where
					pv_tnt_id='.$config['tnt_id'].'
					and pv_fld_id='.$d['fld_id'].'
					and pv_obj_id_0='.$d['obj_0'].'
					and pv_obj_id_1='.$d['obj_1'].'
					and pv_obj_id_2='.$d['obj_2'].'
					and pv_lng_code=\''.$d['lng'].'\''
			);

			fld_objects_set_date_modified( $d['cls_id'], array($d['obj_0'], $d['obj_1'], $d['obj_2']));

		}else{
			$each_different_obj[$d['cls_id'].$d['obj_0'].$d['obj_1'].$d['obj_2']] = array('cls_id'=> $d['cls_id'], 'ids' => array($d['obj_0'], $d['obj_1'], $d['obj_2']));

			$tmp_values = array( $config['tnt_id'], $d['fld_id'],'\''.addslashes($d['value']).'\'', $d['obj_0'], $d['obj_1'], $d['obj_2'], '\''.addslashes($d['lng']).'\'');
			$lines[] = '('.implode(',', $tmp_values).')';
		}
	}

	if( sizeof($lines) > 0 ){
		ria_mysql_query('replace into fld_object_values	('.implode( ',',$fields ).') values	'.implode( ',',$lines ));
		if( ria_mysql_affected_rows() > 0 ){
			foreach( $each_different_obj as $obj ){
				fld_objects_set_date_modified( $obj['cls_id'], $obj['ids'] );
			}
		}
	}

	return true;
}

/**	Cette fonction permet l'enregistrement d'une valeur dans un champ personnalisé.
 *	Avant l'enregistrement, les contrôles de saisi sont appliqués et peuvent provoquer
 *	le refus de la modification.
 *
 *	Quel que soit le type de champ, la valeur nulle est acceptée.
 *
 *	@param $obj Obligatoire, identifiant de l'objet ( ou tableau d'identifiants pour les clés composées )
 *	@param $fld Obligatoire, identifiant du champ personnalisé
 *	@param $value Obligatoire, valeur à enregistrer dans le champ. Dans le cas d'une liste déroulante, donner les identifiants (dans un tableau ou séparé par \c , dans le cas de choix multiple)
 *	@param string $lng Optionnel, code ISO 639-1 de la langue, par défaut la langue utilisé est celle du site
 *	@param $no_action Optionnel, si vrai, aucune autre action n'est effectuée après l'enregistrement de la valeur (réécritures d'URLs, réindexation...)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function fld_object_values_set( $obj, $fld, $value, $lng=false, $no_action=false ){
	global $config, $memcached;

	$rfld = fld_fields_get($fld);
	if( $rfld===false || !ria_mysql_num_rows($rfld) ) return false;
	$f = ria_mysql_fetch_array($rfld);

	if( ( $lng===false || strtolower($lng)==strtolower($config['i18n_lng']) ) && $f['physical'] ) return false;

	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	}elseif( !is_numeric($obj) || $obj<=0 ) return false;

	if( !is_array($obj) )
		$obj = array( $obj );

	$value = is_array($value) ? implode( ', ', $value ) : trim($value);

	$old_val = '';
	if( $f['type_id']==FLD_TYPE_IMAGE ){
		// On récupère la valeur actuel
		$v = fld_object_values_get( $obj, $fld, $lng!=false ? $lng : '', false, true );
		if( trim($v)!='' ){
			$old_val = $v;
		}
	}

	if( $value=='' ){

		$sql = '
			delete from
				fld_object_values
			where
				pv_tnt_id='.$config['tnt_id'].'
				and pv_fld_id='.$fld
		;

		$i = 0;
		while( $i<sizeof($obj) ){
			$sql .= ' and pv_obj_id_'.$i.'='.$obj[$i];
			$i++;
		}

		$sql .= ' and pv_lng_code=\''.( $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : $config['i18n_lng'] ).'\'';

		$bstart = microtime(true);
		$res = ria_mysql_query( $sql );

		if( $res && $f['cls_id']==CLS_PRODUCT ){
			prd_products_update_completion( $obj[0] );
		}

		// mise à jour des données liées
		fld_objects_set_date_modified( $f['cls_id'], $obj );

	}else{

		if( $f['type_id']==FLD_TYPE_INT || $f['type_id']==FLD_TYPE_FLOAT )
			$value = str_replace( array(' ',','), array('','.'), $value );

		// Contrôle de la limite minimum
		if( $f['min'] ){
			switch( $f['type_id'] ){
				case FLD_TYPE_TEXT:
				case FLD_TYPE_TEXTAREA:
					if( strlen($value)<$f['min'] ) return ERR_FLD_MIN;
					break;
				case FLD_TYPE_INT:
				case FLD_TYPE_FLOAT:
					if( $value<$f['min'] ) return ERR_FLD_MIN;
					break;
			}
		}

		// Contrôle de la limite maximum
		if( $f['max'] ){
			switch( $f['type_id'] ){
				case FLD_TYPE_TEXT:
				case FLD_TYPE_TEXTAREA:
					if( strlen($value)>$f['max'] ) return ERR_FLD_MAX;
					break;
				case FLD_TYPE_INT:
				case FLD_TYPE_FLOAT:
					if( $value>$f['max'] ) return ERR_FLD_MAX;
					break;
			}
		}

		// Contrôle spécifique aux listes de choix à sélection unique
		if( $f['type_id']==FLD_TYPE_SELECT ){
			// Résoud l'identifiant de valeur
			$value = fld_restricted_values_get_name( $value );
		}

		// Contrôle spécifique aux listes de choix à sélection multiple
		if( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE ){
			$value = explode( ',', $value );
			$names = array();
			foreach( $value as $v )
				$names[] = fld_restricted_values_get_name(trim($v));
			$value = implode(', ', $names);
		}

		// Contrôle spécifique aux listes de choix hiérarchique
		if( $f['type_id']==FLD_TYPE_SELECT_HIERARCHY ){
			$val_tab = explode( ',', $value );
			$tmp = array();
			foreach( $val_tab as $v ){
				if( fld_restricted_values_exists($v) ){
					$tmp[] = $v;
				}
			}
			$value = implode(",", $tmp);
			if( $value == '' ){
				return false;
			}
		}

		if( $f['type_id']==FLD_TYPE_DATE ){
			if( !isdate($value) && !isdateheure($value) ){
				return false;
			}
			if( $f['old_txt_type'] ){ // on retire la partie TIME
				$value = dateparse(substr($value, 0, 10));
			}else{
				if( isdate($value) ){ // on ajoute la partie TIME
					$value .= ' 00:00:00';
				}
				$value = dateheureparse($value);
			}
		}

		$data = array();
		$data[] = array(
			'value' => $value,
			'obj_0' => isset($obj[0]) ? $obj[0] : 0,
			'obj_1' => isset($obj[1]) ? $obj[1] : 0,
			'obj_2' => isset($obj[2]) ? $obj[2] : 0,
			'fld_id' => $f['id'],
			'cls_id' => $f['cls_id'],
			'lng' => ($lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : $config['i18n_lng'])
		);

		$bstart = microtime(true);
		$res = fld_object_values_set_multiple($data);

		if( $res && $f['cls_id']==CLS_PRODUCT ){
			prd_products_update_completion( $obj[0] );
		}

	}

	// reset cache des champs avancé pour 90% des cas d'utilisation
	// fld_object_values_get( $obj, $fld )
	$fldmemkey = 'fields:fldobjectvaluesget:'.implode(':', $obj).':'.$fld.':'.$lng.':::';
	$memcached->delete($fldmemkey);

	// Mise à jour du nombre d'utilisation de l'ancienne et de la nouvelle image
	if( $f['type_id']==FLD_TYPE_IMAGE ){
		if( trim($old_val)!='' && is_numeric($old_val) && $old_val ){
			img_images_count_update( $old_val );
		}

		if( is_numeric($value) && $value ){
			img_images_count_update( $value );
		}
	}

	// Gestion des urls multilingue
	if( !$no_action && strtolower($lng)!=$config['i18n_lng'] && strtolower($lng)!='ru' ){
		switch( $fld ){
			case _FLD_CAT_NAME : // Catégories
			case _FLD_CAT_TITLE :
				$ar_cat = array(); $ar_cat[] = $obj[0];

				// Recherche l'url de la catégorie et maj de l'url dans la langue $lng
				$url_df = prd_categories_get_url( $obj[0] );
				$res = rew_rewritemap_add_multilingue( array($obj[0]), CLS_CATEGORY, $lng, $url_df, _FLD_CAT_URL );

				// Recherche les catégories enfants
				$childs = prd_categories_childs_get_array( $obj[0], true );
				if( is_array($childs) && sizeof($childs)>0 ){
					$all_url = prd_categories_get_url( $childs, true );
					sort($childs);
					$i = 0;
					foreach( $childs as $child ){
						// Recherche l'url de la catégorie et maj de l'url dans la langue $lng
						$res = rew_rewritemap_add_multilingue( array($child), CLS_CATEGORY, $lng, $all_url[$i], _FLD_CAT_URL );
						$ar_cat[] = $child;
						$i++;
					}
				}

				// Met à jour les urls des produits contenus dans la catégorie et dans celle de ses enfants
				foreach( $ar_cat as $cat ){
					$rprd = prd_products_get_simple( 0, '', false, $cat );
					while( $p = ria_mysql_fetch_array($rprd) ){
						$res = rew_rewritemap_add_multilingue( array($cat, $p['id']), CLS_PRODUCT, $lng, $p['url_alias'], _FLD_PRD_URL );
					}
				}

				try{
					// Réindex la catégorie dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
						'cls_id' => CLS_CATEGORY,
						'obj_id_0' => $obj[0],
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}

				break;
			// Produits
			case _FLD_PRD_NAME:
			case _FLD_PRD_TITLE:
				// Recherche les classements du produit
				$rcat = prd_classify_get( false, $obj[0] );
				while( $cat = ria_mysql_fetch_array($rcat) ){
					// Recherche l'url du produit et maj de l'url dans la langue $lng
					$res = rew_rewritemap_add_multilingue(array($cat['cat'], $obj[0]), CLS_PRODUCT, $lng, $cat['url'], _FLD_PRD_URL);

					try{
						// Réindex le classement dans le moteur de recherche.
						RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
							'cls_id' => CLS_CLASSIFY,
							'obj_id_0' => $obj[0],
							'obj_id_1' => $cat['cat'],
						));
					}catch(Exception $e){
						error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
					}
				}

				break;
			// Catégories de la FAQ
			case _FLD_FAQ_CAT_NAME:
				// Recherche l'url de la question et maj de l'url dans la langue $lng
				$url_df = faq_categories_get_url($obj[0]);
				$res = rew_rewritemap_add_multilingue(array($obj[0]), CLS_FAQ_CAT, $lng, $url_df, _FLD_FAQ_CAT_URL);

				try{
					// Réindex la catégorie dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
						'cls_id' => CLS_FAQ_CAT,
						'obj_id_0' => $obj[0],
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}

				break;
			// Questions de la FAQ
			case _FLD_FAQ_QST_NAME:
				// Recherche l'url de la catégorie et maj de l'url dans la langue $lng
				$url_df = faq_questions_get_url($obj[0]);
				$res = rew_rewritemap_add_multilingue(array($obj[0]), CLS_FAQ_QST, $lng, $url_df, _FLD_FAQ_QST_URL);

				try{
					// Réindex la question dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
						'cls_id' => CLS_FAQ_QST,
						'obj_id_0' => $obj[0],
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}

				break;
			// Gestion de contenu
			case _FLD_CMS_NAME:
				$parent = cms_categories_get_parent_id($obj[0]);

				// Recherche l'url de la catégorie et maj de l'url dans la langue $lng
				$url_df = cms_categories_get_url($obj[0]);
				$res = rew_rewritemap_add_multilingue(array($obj[0], $parent), CLS_CMS, $lng, $url_df, _FLD_CMS_URL);

				if( !$parent ){
					// Recherche les catégories enfants et mag leur url dans la langue $lng
					$rcat = cms_categories_get(0, false, true, $obj[0], false, false, true, null, false, null, false);

					if( $rcat ){
						while( $cat = ria_mysql_fetch_array($rcat) ){
							$url_df = cms_categories_get_url($cat['id']);
							$res = rew_rewritemap_add_multilingue(array($cat['id'],$obj[0]), CLS_CMS, $lng, $url_df, _FLD_CMS_URL);
						}
					}
				}

				try{
					// Réindex la catégorie dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
						'cls_id' => CLS_CMS,
						'obj_id_0' => $obj[0],
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}

				break;
			case _FLD_DOC_TYPE_NAME : // Types de document
				// Recherche l'url du type de documment et maj de l'url dans la langue $lng
				$url_df = doc_types_get_url( $obj[0] );
				$res = rew_rewritemap_add_multilingue( array($obj[0]), CLS_TYPE_DOCUMENT, $lng, $url_df, _FLD_DOC_TYPE_URL );
				break;
			case _FLD_NEWS_CAT_NAME : // Catégories d'actualité
				// Recherche l'url de la catégorie et maj de l'url dans la langue $lng
				$url_df = news_categories_get_url( $obj[0] );
				$res = rew_rewritemap_add_multilingue( array($obj[0]), CLS_NEWS_CAT, $lng, $url_df, _FLD_NEWS_CAT_URL );
				break;
			// Actualités
			case _FLD_NEWS_NAME:
				// Recherche l'url de l'actualité et maj de l'url dans la langue $lng.
				$url_df = news_get_url($obj[0]);
				$res = rew_rewritemap_add_multilingue(array($obj[0]), CLS_NEWS, $lng, $url_df, _FLD_NEWS_URL);

				try{
					// Réindex l'actualité dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
						'cls_id' => CLS_NEWS,
						'obj_id_0' => $obj[0],
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}

				break;
			// Marques
			case _FLD_BRD_NAME:
			case _FLD_BRD_TITLE:
				// Recherche l'url de la marque et maj de l'url dans la langue $lng
				$url_df = prd_brands_get_url( $obj[0] );
				$res = rew_rewritemap_add_multilingue( array($obj[0]), CLS_BRAND, $lng, $url_df, _FLD_BRD_URL );

				break;
			case _FLD_STR_NAME:
			case _FLD_STR_CITY:
				// Recherche l'url du magasin et maj de l'url dans la langue $lng
				$url_df = dlv_stores_get_url($obj[0]);
				$res = rew_rewritemap_add_multilingue(array($obj[0]), CLS_STORE, $lng, $url_df, _FLD_STR_URL);

				try{
					// Réindex le magasin dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
						'cls_id' => CLS_STORE,
						'obj_id_0' => $obj[0],
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}

				break;
		}
	}

	// Applique les points de fidélité sur la facture si le champ avancé est dans la config "fields_apply_rewards_on_invoice".
	// (Principalement utilisé pour Pierre Oteiza).
	if( is_array($config['fields_apply_rewards_on_invoice']) && !empty($config['fields_apply_rewards_on_invoice']) && in_array($fld, $config['fields_apply_rewards_on_invoice']) ){
		rwd_actions_apply('RWA_INVOICE', CLS_INVOICE, $obj[0]);
	}

	// Dans le cas du champs de type chance de signature et que la valeur est à 10 (100%) alors on renseigne la date de signature du devis.
	if( $fld == _FLD_ORD_SIGN_RATE ){
		fld_object_values_set($obj[0], _FLD_ORD_PIPE_SIGN_WIN_DATE, $value == 10 ? date('Y-m-d H:i:s') : '');
	}

	return $res;
}

// \cond onlyria
/** Cette fonction permet de retirer les contenus rattachés à un champ personnalisé
 *	@param $fld Obligatoire, identifiant du champ avancé
 *	@param $force_del Facultatif, permet de forcé la suppression des valeurs pour un champs avancé même si ce champs est synchronisé
 *	@return bool Retourne false si le champ avancé n'existe pas, s'il est synchronisé ou s'il est physique
 *	@return bool Retourne false si la suppression a échouée
 *	@return bool Retourne true si la suppression s'est correctement déroulée
 */
function fld_object_values_clear( $fld, $force_del=false ){
	global $config;

	if( !is_numeric($fld) || $fld <= 0 ){
		return false;
	}

	if( !$force_del && (fld_fields_get_is_sync( $fld ) || fld_fields_is_physical( $fld )) ){
		return false;
	}

	$class = fld_fields_get_class( $fld );

	$rget = ria_mysql_query('
		select pv_obj_id_0 as id, pv_obj_id_1 as id1, pv_obj_id_2 as id2
		from fld_object_values
		where pv_tnt_id = '.$config['tnt_id'].'
			and pv_fld_id = '.$fld.'
	');

	$r = ria_mysql_query('
		delete from fld_object_values
		where pv_tnt_id = '.$config['tnt_id'].'
			and pv_fld_id = '.$fld.'
	');

	// mise à jour de la date de modification de l'objet
	if( $r && $rget ){
		while( $get = ria_mysql_fetch_assoc($rget) ){
			fld_objects_set_date_modified( $class, array($get['id'],$get['id1'],$get['id2']) );
		}
	}

	return $r;

}
/// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la liste des valeurs de champs avancé pour un ou plusieurs objets
 *	@param int $cls Obligatoire, identifiant de la classe concernée
 *	@param int|array $obj Obligatoire, identifiant de l'objet ou tableau d'identifiants our les clés composées ou tableau de tableau d'identifiants pour les clés composées
 *	@param bool $multi_key Facultatif, permet de dire si le paramètre obj est un tableau de tableau de clé ou seulement un tableau d'id
 *	@param bool $exclude_fld Facultatif, permet d'exclure des champs avancés
 *	@param bool $include_fld Facultatif, permet de restreinte les champs avancés dont les valeurs seront retournées
 *	@return bool|resource false en cas d'échec ou un résultat de ria_mysql_query avec les colonnes suivantes :
 *			- obj_value : valeur de l'objet pour le champs
 *			- lng_code : langue de la valeur
 *			- obj_id_0 : identifiant de l'objet
 *			- obj_id_1 : identifiant de l'objet 1
 *			- obj_id_2 : identifiant de l'objet 2
 *			- id : identifiant du champs
 *			- name : nom du champ avancé
 *			- type_id : identifiant du type de champ
 */
function fld_object_values_get_all($cls, $obj, $multi_key=false, $exclude_fld=false, $include_fld=false){
	if( !is_numeric($cls) || $cls <= 0 ){
		return false;
	}

	if( $multi_key ){
		if( !is_array($obj) ){
			return false;
		}
		foreach( $obj as $k => $o ){
			$obj[$k] = control_array_integer( $o, true, true );
			if( $obj[$k] === false ){
				return false;
			}elseif( !$obj[$k][0] ){
				return false;
			}
		}
	}else{
		if( is_numeric($obj) ){
			$obj = array($obj);
		}

		$obj = control_array_integer( $obj, true, true );
		if( $obj === false ){
			return false;
		}elseif( !$obj[0] ){
			return false;
		}
	}
	if( $exclude_fld != false ){
		$exclude_fld = control_array_integer( $exclude_fld, true, true );
		if( $exclude_fld === false ){
			return false;
		}
	}

	if( $include_fld != false ){
		$include_fld = control_array_integer( $include_fld, false, true );
		if( $include_fld === false ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			pv_value as obj_value, pv_lng_code as lng_code, pv_obj_id_0 as obj_id_0, pv_obj_id_1 as obj_id_1, pv_obj_id_2 as obj_id_2,
			pv_fld_id as id, fld_name as name, fld_type_id as type_id
		from fld_object_values
		join fld_fields on pv_fld_id = fld_id and (pv_tnt_id = fld_tnt_id or fld_tnt_id = 0)
		where pv_tnt_id = '.$config['tnt_id'].' and fld_cls_id = '.$cls.'
	';

	if( $multi_key ){
		$wheres = array();
		$wheres_single = array();
		foreach( $obj as $o ){
			if( sizeof( $o ) == 1 ){
				$wheres_single[] = $o[0];
			}else{
				$where = ' pv_obj_id_0 = '.$o[0];
				if( isset($o[1]) ){
					$where .= ' and pv_obj_id_1 = '.$o[1];
				}else{
					$where .= ' and pv_obj_id_1 = 0';
				}
				if( isset($o[2]) ){
					$where .= ' and pv_obj_id_2 = '.$o[2];
				}else{
					$where .= ' and pv_obj_id_2 = 0';
				}
				$wheres[] = $where;
			}
		}
		if( sizeof( $wheres_single ) ){
			$sql .= ' and pv_obj_id_0 in ('.implode(',', $wheres_single).')';
		}else{
			$sql .= ' and (('.implode(') or (', $wheres).'))';
		}
	}else{
		$sql .= ' and pv_obj_id_0 = '.$obj[0];
		if( isset($obj[1]) ){
			$sql .= ' and pv_obj_id_1 = '.$obj[1];
		}
		if( isset($obj[2]) ){
			$sql .= ' and pv_obj_id_2 = '.$obj[2];
		}
	}

	if( $exclude_fld ){
		$sql .= ' and fld_id not in ('.implode(',', $exclude_fld).') ';
	}

	if( $include_fld && sizeof($include_fld) ){
		$sql .= ' and fld_id in ('.implode(',', $include_fld).') ';
	}

	return ria_mysql_query($sql);
}
/// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les valeurs distinctes utilisées pour un champ avancé.
 * 	\param $fld_id Obligatoire, identifiant d'un champ avancé
 * 	\return Un tableau des valeurs (ce tableau est mis en cache pour une durée de 4h)
 */
function fld_object_values_get_distinct( $fld_id ){
	global $config, $memcached;

	if( !is_numeric($fld_id) || $fld_id <= 0 ){
		return false;
	}

	$key_memcached = 'fld_object_values_get_distinct:'.$config['tnt_id'].':'.$fld_id;
	if( $get = $memcached->get($key_memcached) ){
		return ($get == 'none' ? [] : $get);
	}

	$res = ria_mysql_query('
		select distinct pv_value as val
		from fld_object_values
		where pv_tnt_id = '.$config['tnt_id'].'
			and pv_fld_id = '.$fld_id.'
	');

	$ar_vals = [];

	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_vals[] = $r['val'];
		}
	}

	$memcached->set( $key_memcached, (count($ar_vals) ? $ar_vals : 'none'), 4 * 60 *60 );
	return $ar_vals;
}
/// \endcond

// \cond onlyria
/** Cette fonction permet de fitrer un tableau d'object et ne retourne que ceux liés à la valeur donnée.
 *	@param $obj Obligatoire, identifiant de l'objet ou tableau d'identifiants our les clés composées ou tableau de tableau d'identifiants pour les clés composées
 *	@param $fld Obligatoire, identifiant du champ avancé
 *	@param $value Obligatoire, valeur à contrôler
 *	@param $multi_key Facultatif, permet de dire si le paramètre obj est un tableau de tableau de clé ou seulement un tableau d'id
 *	@return array Un tableau des identifiants d'objets rattachés à la valeur (même format qu'initialement donné dans $obj)
 */
function fld_object_values_filter_objects( $obj, $fld, $value, $multi_key=false ){
	if( $multi_key ){
		if( !is_array($obj) || !count($obj) ){
			return false;
		}

		foreach( $obj as $k => $o ){
			$obj[$k] = control_array_integer( $o, true, true );
			if( $obj[$k] === false ){
				return false;
			}elseif( !$obj[$k][0] ){
				return false;
			}
		}
	}else{
		if( is_numeric($obj) ){
			$obj = array($obj);
		}

		$obj = control_array_integer( $obj, true, true );
		if( $obj === false ){
			return false;
		}elseif( !$obj[0] ){
			return false;
		}
	}

	if (!is_numeric($fld) || $fld <= 0) {
		return false;
	}

	$value = trim($value);
	if ($value == '') {
		return false;
	}

	global $config;

	$sql_objs = '';

	if( $multi_key ){
		$wheres = array();
		$wheres_single = array();
		foreach( $obj as $o ){
			if( sizeof( $o ) == 1 ){
				$wheres_single[] = $o[0];
			}else{
				$where = ' pv_obj_id_0 = '.$o[0];
				if( isset($o[1]) ){
					$where .= ' and pv_obj_id_1 = '.$o[1];
				}
				if( isset($o[2]) ){
					$where .= ' and pv_obj_id_2 = '.$o[2];
				}
				$wheres[] = $where;
			}
		}
		if( sizeof( $wheres_single ) ){
			$sql_objs .= ' and pv_obj_id_0 in ('.implode(',', $wheres_single).')';
		}else{
			$sql_objs .= ' and (('.implode(') or (', $wheres).'))';
		}
	}else{
		$sql_objs .= ' and pv_obj_id_0 = '.$obj[0];
		if( isset($obj[1]) ){
			$sql_objs .= ' and pv_obj_id_1 = '.$obj[1];
		}
		if( isset($obj[2]) ){
			$sql_objs .= ' and pv_obj_id_2 = '.$obj[2];
		}
	}

	$res = ria_mysql_query('
		select pv_obj_id_0 as obj_id_0, ifnull(pv_obj_id_1, 0) as obj_id_1, ifnull(pv_obj_id_2, 0) as obj_id_2
		from fld_object_values
		where pv_tnt_id = '.$config['tnt_id'].'
			and pv_fld_id = '.$fld.'
			and (
				lower(pv_value) = lower("'.addslashes($value).'")
				or lower(pv_value) like lower("'.addslashes($value).',%")
				or lower(pv_value) like lower("%, '.addslashes($value).',%")
				or lower(pv_value) like lower("%, '.addslashes($value).'")
			)

			'.$sql_objs.'
	');

	$res_ids = array();

	if ($res) {
		while ($r = ria_mysql_fetch_assoc($res)) {
			if ($multi_key && count($wheres_single)) {
				$res_ids[] = $r['obj_id_0'];
			}else{
				$tmp = array( $r['obj_id_0']);

				if (is_numeric($r['obj_id_1']) && $r['obj_id_1']) {
					$tmp = array( $r['obj_id_1'] );
				}

				if (is_numeric($r['obj_id_2']) && $r['obj_id_2']) {
					$tmp = array( $r['obj_id_2'] );
				}

				$res_ids[] = $tmp;
			}
		}
	}

	return $res_ids;
}
/// \endcond

/// \cond onlyria
/** Cette fonction permet de récupérer les estimations de frais de port.
 * 	@param int $obj Obligatoire, identifiant d'un panier
 * 	@return ressource Un résultat MySQL contenant :
 * 				- fld_id : identifiant du champ avancé (cf. FLD_PRODUCT_FDP_1, FLD_PRODUCT_FDP_2)
 * 				- srv_name : nom du service de livraison
 * 				- port : montant des frais de port
 */
function fld_objects_port_delivery($obj){
	global $config;

	// Vérification
	if( !is_numeric($obj) || $obj <= 0 ){
		return false;
	}

	// Liste des champs avancé lié au frais de port
	$fld_port = [
		FLD_PRODUCT_FDP_1, FLD_PRODUCT_FDP_2, FLD_PRODUCT_FDP_3,
		FLD_PRODUCT_FDP_4, FLD_PRODUCT_FDP_5, FLD_PRODUCT_FDP_6,
		FLD_PRODUCT_FDP_7, FLD_PRODUCT_FDP_8, FLD_PRODUCT_FDP_9,
	];

	$sql = '
		select pv_fld_id as fld_id, srv_name, pv_value as port
		from fld_object_values
			join dlv_services on ( pv_tnt_id = srv_tnt_id and pv_fld_id = srv_fld_id )
		where pv_tnt_id = '.$config['tnt_id'].'
			and pv_fld_id in ('.implode(', ', $fld_port).')
			and pv_obj_id_0 = '.$obj.'
	';

	$stmt = ria_mysql_query( $sql );
	if( !$stmt || !ria_mysql_num_rows($stmt) ){
		return false;
	}

	return $stmt;
}

/// \endcond

// \cond onlyria
/// @}
/// \endcond
