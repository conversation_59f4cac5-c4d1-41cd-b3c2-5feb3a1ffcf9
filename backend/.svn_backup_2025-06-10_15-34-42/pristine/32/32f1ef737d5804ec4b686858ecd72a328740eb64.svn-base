# ImportationCustomColumnLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**save** | [**\Swagger\Client\Model\LinksImportationSaveCustomColumnLink**](LinksImportationSaveCustomColumnLink.md) |  | 
**delete** | [**\Swagger\Client\Model\LinksCatalogDeleteCustomColumnLink**](LinksCatalogDeleteCustomColumnLink.md) |  | 
**expression** | [**\Swagger\Client\Model\LinksCatalogGetCustomColumnExpressionLink**](LinksCatalogGetCustomColumnExpressionLink.md) |  | 
**map** | [**\Swagger\Client\Model\LinksImportationMapCustomColumnLink**](LinksImportationMapCustomColumnLink.md) |  | [optional] 
**unmap** | [**\Swagger\Client\Model\LinksImportationUnmapCustomColumnLink**](LinksImportationUnmapCustomColumnLink.md) |  | [optional] 
**product_sample_value** | [**\Swagger\Client\Model\LinksImportationGetProductSampleCustomColumnValueLink**](LinksImportationGetProductSampleCustomColumnValueLink.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


