<?php

	/** \file export-tenant-sql.php
	 * 	Ce script exporte l'ensemble des données d'un locataire, au format SQL. Il s'utilise comme suit :
	 *	\code{.sh}
	 *	php export-tenant-sql.php tenant [base de donnée] > export.sql
	 *	\endcode
	 *	où :
	 *	- tenant est l'identifiant du locataire à exporter (obligatoire)
	 *	- base de données est le nom de la base de données source (par défaut : riashop)
	 */
	
	if( !isset($argv[1]) || !is_numeric($argv[1]) ){
		$help = 'Utilisation du l\'exportation SQL : export-tenant-sql.php identifiant_tenant [base de donnée]'."\n";
		$help .= '	- premier paramètre obligatoire, correspond à l\'identifiant de locataire.'."\n";
		$help .= '	- deuxième paramètre optionnel, correspond au nom de la base de données d\'où les données doivent être exportées, par défaut riashop'."\n";
		$help .= '	- troixième paramètre optionnel, détermine si le script créer un dump ou un fichier permettant de supprimer les données de tous un tenant'."\n";
		$help .= '	- quatrième paramètre optionnel, permet d\'exclure l\'historique de commande (ord_orders, ord_products, ord_invoices...)'."\n";
		die($help);
	}

	$deleted = isset($argv[3]) && $argv[3]=='true' ? true : false;
	$exclude_ord = isset($argv[4]) && $argv[4]=='true' ? true : false;
	
	// Nom de la base de données d'où les données doivent être exportée
	$database = isset($argv[2]) ? $argv[2] : 'riashop';
	
	// Dossier d'include
	set_include_path(dirname(__FILE__) . '/../include/');

	// Connexion à la base de données
	require_once( 'db.inc.php' );

	// Type ne devant pas être entre guillemets
	$type_num = array('int', 'decimal', 'tinyint', 'smallint', 'mediumint', 'bigint', 'float', 'double', 'real', 'bit', 'boolean', 'serial');

	// Table exclut de l'exportation
	$exclude = array(
		// Comparateurs de prix
		'ctr_catalogs', 'ctr_categories', 'ctr_cat_hierarchy', 'ctr_cat_publish', 'ctr_comparators', 'ctr_comparator_tenants', 'ctr_params', 'ctr_param_fields', 'ctr_prd_categories', 'ctr_cat_field_mapping', 'ctr_cat_redirections', 'ctr_filters',
		// Forum
		'frm_category', 'frm_cat_child', 'frm_post', 'frm_post_hierarchy',
		// Page d'accueil
		'hpg_homepages', 'hpg_products', 'hpg_products_types',
		// Zones
		'sys_zones_hierarchy',
		// Tables globales
		'tnt_website_types', 'cat_types', 'cfg_variables', 'fld_types', 'adn_menu',
		'gu_adresses_cities', 'gu_adr_types', 'gu_messages_state', 'gu_messages_type', 'gu_tell_a_friend_type', 
		'gu_titles', 'ord_states', 'prc_symbols', 'prc_symbol_types', 'prc_types', 'prd_tvas', 'search_content_types', 
		'search_keywords', 'search_places', 'sys_countries', 'sys_countries_currencies', 'sys_currencies', 
		'sys_languages', 'sys_translate_contexts', 'sys_zones', 'sys_zone_types', 'tsk_tasks',
		// tables n'existant plus sur riashop international
		'brd_retailers', 'gu_adresses_departements', 'gu_adresses_regions', 'pmt_groups', 'pmt_promotions', 'prc_ecotaxe_exempt_conditions', 
		'prc_ecotaxe_exempt_groups', 'prd_config', 'prd_prices', 'prd_users_prices', 'proloisirs_pls', 'util_countries',
		// Moteur de recherche
		'search_contains', 'search_contents', 'search_substitut', 'search_terms', 
		'search_cache_types', 'search_caches', 'search_clickthroughs', 'search_log', 'search_results', 'search_results_hidden',
		// Commande / Bon de livraison / Bon de prépration / Facture
		/*'ord_orders', 'ord_products', 'ord_invoices', 'ord_inv_products', 'ord_bl', 'ord_bl_products', 'ord_pl', 'ord_pl_products', 'ord_orders_states',*/
		// Statistique
		'stats_cat_hourly', 'stats_hourly', 'stats_origins', 'stats_prd_categories_totals', 'stats_prd_categories_users', 'stats_prd_categories_users_histo', 'stats_prd_hourly', 'stats_prd_products_totals', 
		'stats_prd_products_users', 'stats_prd_products_users_histo', 'stats_rewards', 'stats_rewards_bak', 'stats_stores_search', 'stats_stores_views',
		// Autre
		'tsk_search', 'tsk_tasks_activation', 'tsk_tasks_tenants',

		// Table seulement sur Beijing
		'bak_antispam', 'bio_img_keywords', 'gu_alerts', 'gu_alerts_types', 'nlr_subscribers_products', 'prc_check_histo', 'prd_exports', 'prd_prices_caches', 'prd_products_archive',
		'prd_restrictions_SegmentVersion', 'prd_restrictions_brands_SegmentVersion', 'prd_restrictions_categories_SegmentVersion', 'prd_restrictions_products_SegmentVersion', 'rwd_seg_cat_rewards', 'rwd_seg_prd_rewards', 'search_results_hidden',
		'search_results_masked', 'site_bank_users', 'test_charles',
		'search_cache_types', 'search_caches', 'search_clickthroughs', 'search_log', 'search_results', 'search_results_hidden', 'tsk_search', 'tsk_tasks_activation', 'tsk_tasks_tenants',
		'antispam','rwd_cat_rewards', 'rwd_prd_rewards',
	);

	if( $exclude_ord ){
		$exclude = array_merge( $exclude, array(
			'ord_orders', 'ord_products', 'ord_invoices', 'ord_inv_products', 'ord_bl', 'ord_bl_products', 'ord_pl', 'ord_pl_products', 'ord_orders_states'
		));
	}
	
	// Exclusion de colonne (key=>array(cols) où key est égal au nom de la table)
	$exclude_cols = array(
		'gu_users_payment_credentials' => array('upc_name', 'upc_card_brand', 'upc_card_number', 'upc_expiry_month', 'upc_expiry_year', 'upc_bank_code', 'upc_date_deleted'),
		'ord_orders' => array('ord_date_created'),
		'ord_returns' => array('return_cod_id'),
		'seg_segment_criterions' => array('sgc_origin'),
		'stats_stores_views' => array('stat_session')
	);

	// Premières tables dont les données seront exportées
	$first = array( 
		'tnt_tenants', 'tnt_websites'
	);
	
	// Secondes tables dont les données seront exportées
	$seconde = array(
		'fld_classes', 'fld_fields', 'gu_users', 'gu_adresses', 'antispam', 'gu_profiles', 'ord_payment_types'
	);

	// Tableau contenant toutes les tables
	$array_tables = array();
	
	// Récupère la structure des première table (-28)
	$tables = ria_mysql_query('
		select table_name as name
		from information_schema.tables
		where table_schema = \''.$database.'\'
			and table_name in (\''.implode( '\',\'', $first ).'\')
		'.( $deleted ? ' order by table_name desc' : '' ).'
	');

	print "SET FOREIGN_KEY_CHECKS = 0;\n";
	
	if( $deleted )
		print create_requests_deleted_SQL($tables, $argv[1], $database);
	else
		create_requests_SQL($tables, $argv[1], $database);



	// Récupère la structure des tables avec le tenant facultatif (-28)
	$tables = ria_mysql_query('
		select table_name as name
		from information_schema.tables
		where table_schema = \''.$database.'\'
			and table_name in (\''.implode( '\',\'', $seconde ).'\')
	');

	if( $deleted )
		print create_requests_deleted_SQL($tables, $argv[1], $database);
	else
		create_requests_SQL($tables, $argv[1], $database);



	// Récupère la structure des dernières tables
	$tables = ria_mysql_query('
		select table_name as name
		from information_schema.tables
		where table_schema = \''.$database.'\'
			and table_name not in (\''.implode( '\',\'', array_merge( $first, $seconde, $exclude ) ).'\')
	');

	if( $deleted )
		print create_requests_deleted_SQL($tables, $argv[1], $database);
	else
		create_requests_SQL($tables, $argv[1], $database);

	if( !$deleted && $argv[1]==9 )
		print 'update '.$database.'.fld_object_values set pv_lng_code=\'EN\' where pv_tnt_id='.$argv[1].';';

	print "SET FOREIGN_KEY_CHECKS = 1;\n";
	
	// Function qui créer les requêtes d'insertion
	function create_requests_SQL($tables, $tnt, $database){
		global $exclude_cols;

		while( $table = ria_mysql_fetch_assoc($tables) ) {
			// Récupère les colonnes de ces tables
			$rcol = ria_mysql_query('desc '.$database.'.'.$table['name']) or die(mysql_error());
			
			$first = true; $cols = ''; $tcols = array();
			while( $col = ria_mysql_fetch_assoc($rcol) ){
				if( array_key_exists($table['name'], $exclude_cols) ){
					if( in_array($col['Field'], $exclude_cols[ $table['name'] ]) ){
						continue;
					}
				}

				// Liste des colonnes
				$cols .= $first ? $col['Field'] : ', '.$col['Field'];
				$tcols[ $col['Field'] ] = array( 'typ'=>$col['Type'], 'cannull'=>$col['Null'], 'default'=>$col['Default'] );

				// Colonne fesant le lien avec le tenant
				if( strpos($col['Field'], 'tnt_id')!==false )
					$tnt_field = $col['Field'];

				$first = false;
			}

			// récupère les données
			$current = 0;
			$limit = 1000;
			do{
				$sql = 'select '.$cols.' from '.$database.'.'.$table['name'].' where '.$tnt_field.'='.$tnt .' limit '.$current.','.$limit;
				$result =  ria_mysql_query( $sql );

				$has_results = $result && ria_mysql_num_rows($result);

				if (!$has_results) {
					break;
				}

				$last = false; $count = 1;
				// Affiche la requête SQL d'insertion
				print 'insert into '.$table['name'].'( '.$cols.' ) values'."\n";

				while( $res = ria_mysql_fetch_assoc($result) ){
					print '( ';
					$r = 1;
					foreach( $tcols as $col=>$info ){
						$last = $r==sizeof($tcols) ? true : false;
						if( trim($res[ $col ])!='' )
							$val = $res[ $col ];
						elseif( $info['cannull']=='YES' )
							$val = 'null';
						else
							$val = $info['default'];

						if( $val=='null' ){
							print $val;
						}else{
							print '\''.addslashes(stripslashes($val)).'\'';
						}
						if( !$last ){
							print ', ';
						}
						$r++;
					}

					if( $count!=ria_mysql_num_rows($result) ){
						print ' ),'."\n";
					}else{
						print ' );'."\n";
					}
					$count++;
				}

				$current += $limit;
			}
			while ($has_results);
		}
	}
	
	// Cette fonction créer les requêtes de suppression.
	function create_requests_deleted_SQL($tables, $tnt, $database){
		$delete = '';
		
		while( $table = ria_mysql_fetch_array($tables) ) {
			// Récupère les colonnes de ces tables
			$rcol = ria_mysql_query('desc '.$database.'.'.$table['name']) or die(mysql_error());
			
			$tnt_field='' ; $first = true; $cols = ''; $tcols = array();
			while( $col = ria_mysql_fetch_array($rcol) ){
				// Liste des colonnes
				$cols .= $first ? $col['Field'] : ', '.$col['Field'];
				$tcols[ $col['Field'] ] = array( 'typ'=>$col['Type'], 'cannull'=>$col['Null'], 'default'=>$col['Default'] );
				
				// Colonne fesant le lien avec le tenant
				if( strpos($col['Field'], 'tnt_id')!==false )
					$tnt_field = $col['Field'];
				
				$first = false;
			}
			
			// Requête de suppression
			if( trim($tnt_field)!='' )
				$delete .= 'delete from '.$database.'.'.$table['name'].' where '.$tnt_field.'='.$tnt.';'."\n";
		}
		return $delete;
	}

