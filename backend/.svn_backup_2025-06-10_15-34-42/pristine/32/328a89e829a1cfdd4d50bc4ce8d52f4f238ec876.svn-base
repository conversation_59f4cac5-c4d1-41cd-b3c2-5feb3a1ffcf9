<?php

	/**	\file check-action-import.php
	 * 
	 * 	L'importation de données supporte 4 actions différentes :
	 * 	- Ajout uniquement
	 * 	- Ajout et mise à jour
	 * 	- Mise à jour uniquement
	 * 	- Suppression
	 * 
	 * 	Ce fichier est appelé en Ajax pour contrôler les droits de l'utilisateur actuellement connecté, et ne lui retourner
	 * 	que les actions qu'il est autorisé à effectuer ; en fonction du type de donnés qu'il a sélectionné.
	 * 
	 * 	Le type de données est fourni grâce à l'argument class, qui est obligatoire. Cet argument contient l'identifiant
	 * 	numérique du type de données suivants :
	 * 	- CLS_PRODUCT
	 *  - CLS_USER
	 *  - CLS_CATEGORY
	 *  - CLS_PRICE
	 *  - CLS_STOCK
	 * 
	 */

    // Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT');
	
	// L'argument class est obligatoire
	if( !isset($_GET['class']) ){
		exit;
	}

    require_once( 'imports.inc.php' );

	// Les contrôles effectués dépendent de la classe sélectionnée
    $action = array();
    switch( $_GET['class'] ){
        case CLS_PRODUCT :{
            if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_ADD') ){
                $action['add'] = ipt_action_display( 'add' );
            }
            if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_ADD') && gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_EDIT')){
                $action['add/upd'] = ipt_action_display( 'add/upd' );
            }
            if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_EDIT') ){
                $action['upd'] = ipt_action_display( 'upd' );
            }
            if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_DEL') ){
                $action['del'] = ipt_action_display( 'del' );
            }
            break;
        }
        case CLS_USER :{
            if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD') ){
                $action['add'] = ipt_action_display( 'add' );
            }
            if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_ADD') && gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_EDIT')){
                $action['add/upd'] = ipt_action_display( 'add/upd' );
            }
            if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_EDIT') ){
                $action['upd'] = ipt_action_display( 'upd' );
            }
            if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_DEL') ){
                $action['del'] = ipt_action_display( 'del' );
            }
            break;
        }
        case CLS_CATEGORY :{
            if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_ADD') ){
                $action['add'] = ipt_action_display( 'add' );
            }
            if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_ADD') && gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_EDIT')){
                $action['add/upd'] = ipt_action_display( 'add/upd' );
            }
            if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_EDIT') ){
                $action['upd'] = ipt_action_display( 'upd' );
            }
            if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_DEL') ){
                $action['del'] = ipt_action_display( 'del' );
            }
            break;
        }
        case CLS_PRICE :
        case CLS_STOCK :
        default :{
            $action = array( 
                'add' => ipt_action_display( 'add' ), 
                'add/upd' => ipt_action_display( 'add/upd' ),
                'upd' => ipt_action_display( 'upd' ),
                'del' => ipt_action_display( 'del' ) );
        }
    }

    print json_encode( $action );
