<documentation title="Todo Comments">
    <standard>
    <![CDATA[
    TODO Statements should be taken care of.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A comment without a todo.">
        <![CDATA[
// <em>Handle strange case</em>
if ($test) {
    $var = 1;
}
        ]]>
        </code>
        <code title="Invalid: A todo comment.">
        <![CDATA[
// <em>TODO</em>: This needs to be fixed!
if ($test) {
    $var = 1;
}
        ]]>
        </code>
    </code_comparison>
</documentation>
