<?php
/**
 * MarketplacesOrdersOrderApi
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Api;

use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\MultipartStream;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\RequestOptions;
use Swagger\Client\ApiException;
use Swagger\Client\Configuration;
use Swagger\Client\HeaderSelector;
use Swagger\Client\ObjectSerializer;

/**
 * MarketplacesOrdersOrderApi Class Doc Comment
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class MarketplacesOrdersOrderApi
{
    /**
     * @var ClientInterface
     */
    protected $client;

    /**
     * @var Configuration
     */
    protected $config;

    /**
     * @var HeaderSelector
     */
    protected $headerSelector;

    /**
     * @param ClientInterface $client
     * @param Configuration   $config
     * @param HeaderSelector  $selector
     */
    public function __construct(
        ClientInterface $client = null,
        Configuration $config = null,
        HeaderSelector $selector = null
    ) {
        $this->client = $client ?: new Client();
        $this->config = $config ?: new Configuration();
        $this->headerSelector = $selector ?: new HeaderSelector();
    }

    /**
     * @return Configuration
     */
    public function getConfig()
    {
        return $this->config;
    }

    /**
     * Operation changeOrder
     *
     * Change your marketplace Order Information (accept, ship, etc.)
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $change_order_type The Order change type (required)
     * @param  string $user_name Sometimes the user in the e-commerce application is not the same as user associated with the current subscription key. We recommend providing your application&#39;s user login. (required)
     * @param  string $if_match ETag value to identify the last known version of requested resource.\\ To ensure that you are making a change on the lastest version of the resource.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (required)
     * @param  bool $test_mode If true, the operation will be not be sent to marketplace. But the validation will be taken in account. (optional, default to false)
     * @param  \Swagger\Client\Model\ChangeOrderRequest $request request (optional)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function changeOrder($marketplace_technical_code, $account_id, $beez_up_order_id, $change_order_type, $user_name, $if_match, $test_mode = 'false', $request = null)
    {
        $this->changeOrderWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $change_order_type, $user_name, $if_match, $test_mode, $request);
    }

    /**
     * Operation changeOrderWithHttpInfo
     *
     * Change your marketplace Order Information (accept, ship, etc.)
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $change_order_type The Order change type (required)
     * @param  string $user_name Sometimes the user in the e-commerce application is not the same as user associated with the current subscription key. We recommend providing your application&#39;s user login. (required)
     * @param  string $if_match ETag value to identify the last known version of requested resource.\\ To ensure that you are making a change on the lastest version of the resource.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (required)
     * @param  bool $test_mode If true, the operation will be not be sent to marketplace. But the validation will be taken in account. (optional, default to false)
     * @param  \Swagger\Client\Model\ChangeOrderRequest $request (optional)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function changeOrderWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $change_order_type, $user_name, $if_match, $test_mode = 'false', $request = null)
    {
        $returnType = '';
        $request = $this->changeOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $change_order_type, $user_name, $if_match, $test_mode, $request);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation changeOrderAsync
     *
     * Change your marketplace Order Information (accept, ship, etc.)
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $change_order_type The Order change type (required)
     * @param  string $user_name Sometimes the user in the e-commerce application is not the same as user associated with the current subscription key. We recommend providing your application&#39;s user login. (required)
     * @param  string $if_match ETag value to identify the last known version of requested resource.\\ To ensure that you are making a change on the lastest version of the resource.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (required)
     * @param  bool $test_mode If true, the operation will be not be sent to marketplace. But the validation will be taken in account. (optional, default to false)
     * @param  \Swagger\Client\Model\ChangeOrderRequest $request (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function changeOrderAsync($marketplace_technical_code, $account_id, $beez_up_order_id, $change_order_type, $user_name, $if_match, $test_mode = 'false', $request = null)
    {
        return $this->changeOrderAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $change_order_type, $user_name, $if_match, $test_mode, $request)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation changeOrderAsyncWithHttpInfo
     *
     * Change your marketplace Order Information (accept, ship, etc.)
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $change_order_type The Order change type (required)
     * @param  string $user_name Sometimes the user in the e-commerce application is not the same as user associated with the current subscription key. We recommend providing your application&#39;s user login. (required)
     * @param  string $if_match ETag value to identify the last known version of requested resource.\\ To ensure that you are making a change on the lastest version of the resource.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (required)
     * @param  bool $test_mode If true, the operation will be not be sent to marketplace. But the validation will be taken in account. (optional, default to false)
     * @param  \Swagger\Client\Model\ChangeOrderRequest $request (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function changeOrderAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $change_order_type, $user_name, $if_match, $test_mode = 'false', $request = null)
    {
        $returnType = '';
        $request = $this->changeOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $change_order_type, $user_name, $if_match, $test_mode, $request);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'changeOrder'
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $change_order_type The Order change type (required)
     * @param  string $user_name Sometimes the user in the e-commerce application is not the same as user associated with the current subscription key. We recommend providing your application&#39;s user login. (required)
     * @param  string $if_match ETag value to identify the last known version of requested resource.\\ To ensure that you are making a change on the lastest version of the resource.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (required)
     * @param  bool $test_mode If true, the operation will be not be sent to marketplace. But the validation will be taken in account. (optional, default to false)
     * @param  \Swagger\Client\Model\ChangeOrderRequest $request (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function changeOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $change_order_type, $user_name, $if_match, $test_mode = 'false', $request = null)
    {
        // verify the required parameter 'marketplace_technical_code' is set
        if ($marketplace_technical_code === null || (is_array($marketplace_technical_code) && count($marketplace_technical_code) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $marketplace_technical_code when calling changeOrder'
            );
        }
        // verify the required parameter 'account_id' is set
        if ($account_id === null || (is_array($account_id) && count($account_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $account_id when calling changeOrder'
            );
        }
        // verify the required parameter 'beez_up_order_id' is set
        if ($beez_up_order_id === null || (is_array($beez_up_order_id) && count($beez_up_order_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $beez_up_order_id when calling changeOrder'
            );
        }
        // verify the required parameter 'change_order_type' is set
        if ($change_order_type === null || (is_array($change_order_type) && count($change_order_type) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $change_order_type when calling changeOrder'
            );
        }
        // verify the required parameter 'user_name' is set
        if ($user_name === null || (is_array($user_name) && count($user_name) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $user_name when calling changeOrder'
            );
        }
        // verify the required parameter 'if_match' is set
        if ($if_match === null || (is_array($if_match) && count($if_match) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $if_match when calling changeOrder'
            );
        }

        // $resourcePath = '/v2/user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/{changeOrderType}';
        $resourcePath = '/orders/v3/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/{changeOrderType}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;

        // query params
        if ($user_name !== null) {
            $queryParams['userName'] = ObjectSerializer::toQueryValue($user_name);
        }
        // query params
        if ($test_mode !== null) {
            $queryParams['testMode'] = $test_mode ? 'true' : 'false';
        }
        // header params
        if ($if_match !== null) {
            $headerParams['If-Match'] = ObjectSerializer::toHeaderValue($if_match);
        }

        // path params
        if ($marketplace_technical_code !== null) {
            $resourcePath = str_replace(
                '{' . 'marketplaceTechnicalCode' . '}',
                ObjectSerializer::toPathValue($marketplace_technical_code),
                $resourcePath
            );
        }
        // path params
        if ($account_id !== null) {
            $resourcePath = str_replace(
                '{' . 'accountId' . '}',
                ObjectSerializer::toPathValue($account_id),
                $resourcePath
            );
        }
        // path params
        if ($beez_up_order_id !== null) {
            $resourcePath = str_replace(
                '{' . 'beezUPOrderId' . '}',
                ObjectSerializer::toPathValue($beez_up_order_id),
                $resourcePath
            );
        }
        // path params
        if ($change_order_type !== null) {
            $resourcePath = str_replace(
                '{' . 'changeOrderType' . '}',
                ObjectSerializer::toPathValue($change_order_type),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($request)) {
            $_tempBody = $request;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        $httpBody = \GuzzleHttp\json_encode( $httpBody->getContainer() );

        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation clearMerchantOrderInfo
     *
     * Clear an Order's merchant information
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function clearMerchantOrderInfo($marketplace_technical_code, $account_id, $beez_up_order_id)
    {
        $this->clearMerchantOrderInfoWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id);
    }

    /**
     * Operation clearMerchantOrderInfoWithHttpInfo
     *
     * Clear an Order's merchant information
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function clearMerchantOrderInfoWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id)
    {
        $returnType = '';
        $request = $this->clearMerchantOrderInfoRequest($marketplace_technical_code, $account_id, $beez_up_order_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation clearMerchantOrderInfoAsync
     *
     * Clear an Order's merchant information
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function clearMerchantOrderInfoAsync($marketplace_technical_code, $account_id, $beez_up_order_id)
    {
        return $this->clearMerchantOrderInfoAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation clearMerchantOrderInfoAsyncWithHttpInfo
     *
     * Clear an Order's merchant information
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function clearMerchantOrderInfoAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id)
    {
        $returnType = '';
        $request = $this->clearMerchantOrderInfoRequest($marketplace_technical_code, $account_id, $beez_up_order_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'clearMerchantOrderInfo'
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function clearMerchantOrderInfoRequest($marketplace_technical_code, $account_id, $beez_up_order_id)
    {
        // verify the required parameter 'marketplace_technical_code' is set
        if ($marketplace_technical_code === null || (is_array($marketplace_technical_code) && count($marketplace_technical_code) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $marketplace_technical_code when calling clearMerchantOrderInfo'
            );
        }
        // verify the required parameter 'account_id' is set
        if ($account_id === null || (is_array($account_id) && count($account_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $account_id when calling clearMerchantOrderInfo'
            );
        }
        // verify the required parameter 'beez_up_order_id' is set
        if ($beez_up_order_id === null || (is_array($beez_up_order_id) && count($beez_up_order_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $beez_up_order_id when calling clearMerchantOrderInfo'
            );
        }

        $resourcePath = '/user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/clearMerchantOrderInfo';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($marketplace_technical_code !== null) {
            $resourcePath = str_replace(
                '{' . 'marketplaceTechnicalCode' . '}',
                ObjectSerializer::toPathValue($marketplace_technical_code),
                $resourcePath
            );
        }
        // path params
        if ($account_id !== null) {
            $resourcePath = str_replace(
                '{' . 'accountId' . '}',
                ObjectSerializer::toPathValue($account_id),
                $resourcePath
            );
        }
        // path params
        if ($beez_up_order_id !== null) {
            $resourcePath = str_replace(
                '{' . 'beezUPOrderId' . '}',
                ObjectSerializer::toPathValue($beez_up_order_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation getOrder
     *
     * Get full Order and Order Item(s) properties
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Swagger\Client\Model\Order
     */
    public function getOrder($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        list($response) = $this->getOrderWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match);
        return $response;
    }

    /**
     * Operation getOrderWithHttpInfo
     *
     * Get full Order and Order Item(s) properties
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Swagger\Client\Model\Order, HTTP status code, HTTP response headers (array of strings)
     */
    public function getOrderWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        $returnType = '\Swagger\Client\Model\Order';
        $request = $this->getOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\Order',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation getOrderAsync
     *
     * Get full Order and Order Item(s) properties
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getOrderAsync($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        return $this->getOrderAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation getOrderAsyncWithHttpInfo
     *
     * Get full Order and Order Item(s) properties
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getOrderAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        $returnType = '\Swagger\Client\Model\Order';
        $request = $this->getOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'getOrder'
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function getOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        // verify the required parameter 'marketplace_technical_code' is set
        if ($marketplace_technical_code === null || (is_array($marketplace_technical_code) && count($marketplace_technical_code) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $marketplace_technical_code when calling getOrder'
            );
        }
        // verify the required parameter 'account_id' is set
        if ($account_id === null || (is_array($account_id) && count($account_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $account_id when calling getOrder'
            );
        }
        // verify the required parameter 'beez_up_order_id' is set
        if ($beez_up_order_id === null || (is_array($beez_up_order_id) && count($beez_up_order_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $beez_up_order_id when calling getOrder'
            );
        }

        $resourcePath = '/user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;

        // header params
        if ($if_none_match !== null) {
            $headerParams['If-None-Match'] = ObjectSerializer::toHeaderValue($if_none_match);
        }

        // path params
        if ($marketplace_technical_code !== null) {
            $resourcePath = str_replace(
                '{' . 'marketplaceTechnicalCode' . '}',
                ObjectSerializer::toPathValue($marketplace_technical_code),
                $resourcePath
            );
        }
        // path params
        if ($account_id !== null) {
            $resourcePath = str_replace(
                '{' . 'accountId' . '}',
                ObjectSerializer::toPathValue($account_id),
                $resourcePath
            );
        }
        // path params
        if ($beez_up_order_id !== null) {
            $resourcePath = str_replace(
                '{' . 'beezUPOrderId' . '}',
                ObjectSerializer::toPathValue($beez_up_order_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation getOrderHistory
     *
     * Get an Order's harvest and change history
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Swagger\Client\Model\OrderHistory
     */
    public function getOrderHistory($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        list($response) = $this->getOrderHistoryWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match);
        return $response;
    }

    /**
     * Operation getOrderHistoryWithHttpInfo
     *
     * Get an Order's harvest and change history
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Swagger\Client\Model\OrderHistory, HTTP status code, HTTP response headers (array of strings)
     */
    public function getOrderHistoryWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        $returnType = '\Swagger\Client\Model\OrderHistory';
        $request = $this->getOrderHistoryRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\OrderHistory',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation getOrderHistoryAsync
     *
     * Get an Order's harvest and change history
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getOrderHistoryAsync($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        return $this->getOrderHistoryAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation getOrderHistoryAsyncWithHttpInfo
     *
     * Get an Order's harvest and change history
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function getOrderHistoryAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        $returnType = '\Swagger\Client\Model\OrderHistory';
        $request = $this->getOrderHistoryRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'getOrderHistory'
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function getOrderHistoryRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        // verify the required parameter 'marketplace_technical_code' is set
        if ($marketplace_technical_code === null || (is_array($marketplace_technical_code) && count($marketplace_technical_code) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $marketplace_technical_code when calling getOrderHistory'
            );
        }
        // verify the required parameter 'account_id' is set
        if ($account_id === null || (is_array($account_id) && count($account_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $account_id when calling getOrderHistory'
            );
        }
        // verify the required parameter 'beez_up_order_id' is set
        if ($beez_up_order_id === null || (is_array($beez_up_order_id) && count($beez_up_order_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $beez_up_order_id when calling getOrderHistory'
            );
        }

        $resourcePath = '/user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/history';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;

        // header params
        if ($if_none_match !== null) {
            $headerParams['If-None-Match'] = ObjectSerializer::toHeaderValue($if_none_match);
        }

        // path params
        if ($marketplace_technical_code !== null) {
            $resourcePath = str_replace(
                '{' . 'marketplaceTechnicalCode' . '}',
                ObjectSerializer::toPathValue($marketplace_technical_code),
                $resourcePath
            );
        }
        // path params
        if ($account_id !== null) {
            $resourcePath = str_replace(
                '{' . 'accountId' . '}',
                ObjectSerializer::toPathValue($account_id),
                $resourcePath
            );
        }
        // path params
        if ($beez_up_order_id !== null) {
            $resourcePath = str_replace(
                '{' . 'beezUPOrderId' . '}',
                ObjectSerializer::toPathValue($beez_up_order_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation harvestOrder
     *
     * Send harvest request for a single Order
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function harvestOrder($marketplace_technical_code, $account_id, $beez_up_order_id)
    {
        $this->harvestOrderWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id);
    }

    /**
     * Operation harvestOrderWithHttpInfo
     *
     * Send harvest request for a single Order
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function harvestOrderWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id)
    {
        $returnType = '';
        $request = $this->harvestOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation harvestOrderAsync
     *
     * Send harvest request for a single Order
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function harvestOrderAsync($marketplace_technical_code, $account_id, $beez_up_order_id)
    {
        return $this->harvestOrderAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation harvestOrderAsyncWithHttpInfo
     *
     * Send harvest request for a single Order
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function harvestOrderAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id)
    {
        $returnType = '';
        $request = $this->harvestOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'harvestOrder'
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function harvestOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id)
    {
        // verify the required parameter 'marketplace_technical_code' is set
        if ($marketplace_technical_code === null || (is_array($marketplace_technical_code) && count($marketplace_technical_code) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $marketplace_technical_code when calling harvestOrder'
            );
        }
        // verify the required parameter 'account_id' is set
        if ($account_id === null || (is_array($account_id) && count($account_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $account_id when calling harvestOrder'
            );
        }
        // verify the required parameter 'beez_up_order_id' is set
        if ($beez_up_order_id === null || (is_array($beez_up_order_id) && count($beez_up_order_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $beez_up_order_id when calling harvestOrder'
            );
        }

        $resourcePath = '/user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/harvest';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($marketplace_technical_code !== null) {
            $resourcePath = str_replace(
                '{' . 'marketplaceTechnicalCode' . '}',
                ObjectSerializer::toPathValue($marketplace_technical_code),
                $resourcePath
            );
        }
        // path params
        if ($account_id !== null) {
            $resourcePath = str_replace(
                '{' . 'accountId' . '}',
                ObjectSerializer::toPathValue($account_id),
                $resourcePath
            );
        }
        // path params
        if ($beez_up_order_id !== null) {
            $resourcePath = str_replace(
                '{' . 'beezUPOrderId' . '}',
                ObjectSerializer::toPathValue($beez_up_order_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation headOrder
     *
     * Get the meta information about the order (ETag, Last-Modified)
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function headOrder($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        $this->headOrderWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match);
    }

    /**
     * Operation headOrderWithHttpInfo
     *
     * Get the meta information about the order (ETag, Last-Modified)
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function headOrderWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        $returnType = '';
        $request = $this->headOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation headOrderAsync
     *
     * Get the meta information about the order (ETag, Last-Modified)
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function headOrderAsync($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        return $this->headOrderAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation headOrderAsyncWithHttpInfo
     *
     * Get the meta information about the order (ETag, Last-Modified)
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function headOrderAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        $returnType = '';
        $request = $this->headOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'headOrder'
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  string $if_none_match ETag value to identify the last known version of requested resource.\\ To avoid useless exchange, we recommend you to indicate the ETag you previously got from this operation.\\ If the ETag value does not match the response will be 200 to give you a new content, otherwise the response will be: 304 Not Modified, without any content.\\ For more details go to this link: http://tools.ietf.org/html/rfc7232#section-2.3 (optional)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function headOrderRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $if_none_match = null)
    {
        // verify the required parameter 'marketplace_technical_code' is set
        if ($marketplace_technical_code === null || (is_array($marketplace_technical_code) && count($marketplace_technical_code) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $marketplace_technical_code when calling headOrder'
            );
        }
        // verify the required parameter 'account_id' is set
        if ($account_id === null || (is_array($account_id) && count($account_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $account_id when calling headOrder'
            );
        }
        // verify the required parameter 'beez_up_order_id' is set
        if ($beez_up_order_id === null || (is_array($beez_up_order_id) && count($beez_up_order_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $beez_up_order_id when calling headOrder'
            );
        }

        $resourcePath = '/user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;

        // header params
        if ($if_none_match !== null) {
            $headerParams['If-None-Match'] = ObjectSerializer::toHeaderValue($if_none_match);
        }

        // path params
        if ($marketplace_technical_code !== null) {
            $resourcePath = str_replace(
                '{' . 'marketplaceTechnicalCode' . '}',
                ObjectSerializer::toPathValue($marketplace_technical_code),
                $resourcePath
            );
        }
        // path params
        if ($account_id !== null) {
            $resourcePath = str_replace(
                '{' . 'accountId' . '}',
                ObjectSerializer::toPathValue($account_id),
                $resourcePath
            );
        }
        // path params
        if ($beez_up_order_id !== null) {
            $resourcePath = str_replace(
                '{' . 'beezUPOrderId' . '}',
                ObjectSerializer::toPathValue($beez_up_order_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'HEAD',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation setMerchantOrderInfo
     *
     * Set an Order's merchant information
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  \Swagger\Client\Model\SetMerchantOrderInfoRequest $request request (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function setMerchantOrderInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $request)
    {
        $this->setMerchantOrderInfoWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $request);
    }

    /**
     * Operation setMerchantOrderInfoWithHttpInfo
     *
     * Set an Order's merchant information
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  \Swagger\Client\Model\SetMerchantOrderInfoRequest $request (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function setMerchantOrderInfoWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $request)
    {
        $returnType = '';
        $request = $this->setMerchantOrderInfoRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $request);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation setMerchantOrderInfoAsync
     *
     * Set an Order's merchant information
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  \Swagger\Client\Model\SetMerchantOrderInfoRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function setMerchantOrderInfoAsync($marketplace_technical_code, $account_id, $beez_up_order_id, $request)
    {
        return $this->setMerchantOrderInfoAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $request)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation setMerchantOrderInfoAsyncWithHttpInfo
     *
     * Set an Order's merchant information
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  \Swagger\Client\Model\SetMerchantOrderInfoRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function setMerchantOrderInfoAsyncWithHttpInfo($marketplace_technical_code, $account_id, $beez_up_order_id, $request)
    {
        $returnType = '';
        $request = $this->setMerchantOrderInfoRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $request);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'setMerchantOrderInfo'
     *
     * @param  string $marketplace_technical_code The marketplace technical code (required)
     * @param  int $account_id The account identifier (required)
     * @param  string $beez_up_order_id The BeezUP Order identifier (required)
     * @param  \Swagger\Client\Model\SetMerchantOrderInfoRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function setMerchantOrderInfoRequest($marketplace_technical_code, $account_id, $beez_up_order_id, $request)
    {
        // verify the required parameter 'marketplace_technical_code' is set
        if ($marketplace_technical_code === null || (is_array($marketplace_technical_code) && count($marketplace_technical_code) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $marketplace_technical_code when calling setMerchantOrderInfo'
            );
        }
        // verify the required parameter 'account_id' is set
        if ($account_id === null || (is_array($account_id) && count($account_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $account_id when calling setMerchantOrderInfo'
            );
        }
        // verify the required parameter 'beez_up_order_id' is set
        if ($beez_up_order_id === null || (is_array($beez_up_order_id) && count($beez_up_order_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $beez_up_order_id when calling setMerchantOrderInfo'
            );
        }
        // verify the required parameter 'request' is set
        if ($request === null || (is_array($request) && count($request) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $request when calling setMerchantOrderInfo'
            );
        }

        $resourcePath = '/user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/setMerchantOrderInfo';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($marketplace_technical_code !== null) {
            $resourcePath = str_replace(
                '{' . 'marketplaceTechnicalCode' . '}',
                ObjectSerializer::toPathValue($marketplace_technical_code),
                $resourcePath
            );
        }
        // path params
        if ($account_id !== null) {
            $resourcePath = str_replace(
                '{' . 'accountId' . '}',
                ObjectSerializer::toPathValue($account_id),
                $resourcePath
            );
        }
        // path params
        if ($beez_up_order_id !== null) {
            $resourcePath = str_replace(
                '{' . 'beezUPOrderId' . '}',
                ObjectSerializer::toPathValue($beez_up_order_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($request)) {
            $_tempBody = $request;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Create http client option
     *
     * @throws \RuntimeException on file opening failure
     * @return array of http client options
     */
    protected function createHttpClientOption()
    {
        $options = [];
        if ($this->config->getDebug()) {
            $options[RequestOptions::DEBUG] = fopen($this->config->getDebugFile(), 'a');
            if (!$options[RequestOptions::DEBUG]) {
                throw new \RuntimeException('Failed to open the debug file: ' . $this->config->getDebugFile());
            }
        }

        return $options;
    }
}
