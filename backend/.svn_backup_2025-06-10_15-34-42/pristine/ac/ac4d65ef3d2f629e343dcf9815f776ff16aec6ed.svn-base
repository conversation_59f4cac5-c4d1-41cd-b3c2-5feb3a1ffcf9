<?php

require_once('db.inc.php');
require_once('define.inc.php');
require_once('strings.inc.php');
require_once('rewrite.inc.php');
require_once('products.inc.php');
require_once('cat.images.inc.php');
require_once('domains.inc.php');
require_once('obj_position.inc.php');
require_once('sys.sorts.inc.php');
require_once('prd.stocks.inc.php');
require_once('ria.queue.inc.php');

/** \defgroup model_categories Catégories de produits
 *	\ingroup pim
 *
 *	Ce module comprend les fonctions nécessaires à la gestion des catégories de produits.
 *	Les catégories permettent le classement des produits. Une catégorie peut contenir de 0 à n sous catégories.
 *	Le nombre de sous niveaux n'est pas limité, même s'il est souhaitable qu'il ne soit pas trop important.
 *
 *	\bug Les mots clés de catégorie ne sont pas gérés.
 *	\bug En affichage revendeur, option Centralisé activée, certaines catégories sont vides et pourtant disponibles à la consultation.
 *
 *	@{
 */

// \cond onlyria
/**	Permet l'ajout d'une catégorie.
 *	Le nom de la catégorie est automatiquement mis en forme par cette fonction :
 *	suppression des espaces de début et de fin de chaîne, et mise en majuscules
 *	de la première lettre.
 *
 *	Tous les noms de catégories contenant des guillemets seront refusés.
 *
 *	Deux catégories ne peuvent porter le même nom dans le même niveau hiérarchique.
 *	Si la catégorie que l'on tente d'insérer existe déjà au même niveau dans la base de données,
 *	son identifiant est retourné.
 *
 *	Les titres de catégories permettent la surcharge de la dénomination d'une catégorie SAGE.
 *	Ils sont utilisés pour obtenir un libellé différent dans la boutique.
 *
 *	@param string $name Nom de la catégorie.
 *	@param string $title Titre de la catégorie
 *	@param string $desc Description de la catégorie
 *	@param int $parent Optionnel, identifiant de la catégorie parente
 *	@param bool $publish Optionnel, booléen indiquant si la catégorie est publiée ou non
 *	@param bool $is_sync Optionnel, booléen indiquant si la catégorie est synchronisée avec la gestion commerciale
 *	@param string $ref Optionnel, référence de la catégorie dans la gestion commerciale.
 *	@param bool $is_solde Optionnel, si oui ou non la catégorie ne contient que des articles en soldes
 *	@param int $cod_id Optionnel, le contenu de la catégorie est déterminé par les articles présents dans une promotion
 *
 *	@return int l'identifiant de la catégorie en cas de succès
 *	@return bool false en cas d'erreur
 *
 */
function prd_categories_add( $name, $title='', $desc='', $parent=0, $publish=false, $is_sync=false, $ref=null, $is_solde=false, $cod_id=0 ){

	if( !is_numeric($parent) || $parent<0 ){
		return false;
	}

	if( trim($name) == '' ){
		return false;
	}

	if( $parent > 0 && !prd_categories_exists( $parent ) ){
		return false;
	}
	$publish = strtolower(trim($publish))=='false' || !$publish ? false : true;

	$name = trim(str_replace('"','\'\'',$name));
	$name = ucfirst($name);
	$title = ucfirst(trim($title));
	$desc = trim(ucfirst(sanitize($desc)));

	if( $title == $name ){
		$title = '';
	}

	global $config;

	// Calcule la position d'affichage de la nouvelle catégorie
	$pos = null;
	$order = prd_categories_order_get( $parent );
	if( $order ){

		$sql = '
			select max(cat_pos) + 1
			from prd_categories
			where cat_tnt_id = '.$config['tnt_id'].'
				and cat_date_deleted is null
		';
		if( $parent ){
			$sql .= ' and cat_parent_id = '.$parent;
		}else{
			$sql .= ' and cat_parent_id is null';
		}

		$rpos = ria_mysql_query($sql);
		if( $rpos && ria_mysql_num_rows($rpos) ){
			$pos = ria_mysql_result($rpos, 0, 0);
		}

	}

	$fields = array();
	$values = array();

	$fields[] = 'cat_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'cat_name';
	$values[] = '"'.addslashes($name).'"';

	$fields[] = 'cat_title';
	$values[] = '"'.addslashes($title).'"';

	$fields[] = 'cat_desc';
	$values[] = '"'.addslashes($desc).'"';

	$fields[] = 'cat_publish';
	$values[] = $publish ? 1 : 0;

	$fields[] = 'cat_is_sync';
	$values[] = $is_sync ? 1 : 0;

	$fields[] = 'cat_date_created';
	$values[] = 'now()';

	if( $parent ){
		$fields[] = 'cat_parent_id';
		$values[] = $parent;
	}

	if( $pos !== null ){
		$fields[] = 'cat_pos';
		$values[] = $pos;
	}

	if( trim($ref) ){
		$fields[] = 'cat_ref';
		$values[] = '"'.addslashes(trim($ref)).'"';
	}

	$fields[] = 'cat_is_soldes';
	$values[] = $is_solde ? 1 : 0;

	if( !ria_mysql_query('insert into prd_categories ('.implode(', ', $fields).') values ('.implode(', ', $values).')') ){
		return false;
	}

	$id = ria_mysql_insert_id();

	// On met à jour le nombre d'utilisation des images
	img_images_update_from_riawysiwyg($desc);

	// Maintient à jour la table de hiérarchie
	if( $parent ){
		prd_categories_hierarchy_add($parent, $id);
	}

	// Ajoute l'url virtuelle pour cette catégorie
	prd_categories_url_alias_add($id);

	try{
		// Index la catégorie de produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_CATEGORY,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return $id;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de définir si une catégorie est synchronisée avec la gestion commerciale, ou non.
 *	@param int $cat Identifiant de la catégorie
 *	@param bool $is_sync Vrai si la catégorie est synchronisée, faux dans le cas contraire
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_categories_set_is_sync( $cat, $is_sync ){
	global $config;

	if( !prd_categories_exists($cat) ){
		return false;
	}

	$res = ria_mysql_query('
		update prd_categories
			set cat_is_sync='.( $is_sync ? 1 : 0 ).'
		where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cat
	);
	if( $res ){
		// Mise à jour de la date de modification de la catégorie
		prd_categories_set_date_modified( $cat );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'interrogation de la propriété is_sync d'une catégorie donnée.
 *	@param int $cat Identifiant de la catégorie à interroger
 *	@return bool true si la catégorie est synchronisée avec la gestion commerciale, false dans le cas contraire
 */
function prd_categories_get_is_sync( $cat ){
	global $config;

	if( !prd_categories_exists($cat) ){
		return false;
	}
	return ria_mysql_result(ria_mysql_query('
		select cat_is_sync from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cat
	),0,0);
}
// \endcond

// \cond onlyria
/** Cette fonction récupère l'identifiant de publication d'une catégorie
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@return bool Retourne true si la catégorie est publiée
 *	@return bool Retourne false si la catégorie n'est pas publiée, le paramètre est omis ou bien si la catégorie n'existe pas
 */
function prd_categories_get_publish( $cat ){
	if( !is_numeric($cat) || $cat<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select cat_publish as publish
		from prd_categories
		where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cat
	);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'publish' );
}
// \endcond

// \cond onlyria
/**	Permet le déplacement vers le haut d'une catégorie. S'applique aux catégories dont les enfants
 *	sont triés de manière personnalisée.
 *	@param int $id Catégorie à déplacer vers le haut
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_categories_move_up( $id ){
	global $config;
	if( !is_numeric($id) ){
		return false;
	}

	// Charge la catégorie
	$rcat = prd_categories_get($id);
	if( !ria_mysql_num_rows($rcat) ){
		return false;
	}
	$cat = ria_mysql_fetch_array($rcat);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( $cat['pos']==='' ){
		return false;
	}

	// S'assure que la catégorie n'est pas déjà la première de la liste
	if( $cat['pos']===0 ){
		return false;
	}

	// Permute la catégorie avec celle qui se trouve juste au dessus
	ria_mysql_query('
		update prd_categories set cat_pos='.($cat['pos']-1).'
		where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id
	);
	ria_mysql_query('
		update prd_categories set cat_pos='.($cat['pos']).'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_pos='.($cat['pos']-1).'
			and cat_parent_id'.( $cat['parent_id'] ? '='.$cat['parent_id'] : ' is null' ).'
			and cat_id!='.$id
	);

	return true;
}
// \endcond

// \cond onlyria
/**	Permet le déplacement vers le bas d'une catégorie. S'applique aux catégories dont les enfants
 *	sont triés de manière personnalisée.
 *	@param int $id Catégorie à déplacer vers le bas
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_categories_move_down( $id ){
	global $config;

	if( !is_numeric($id) ){
		return false;
	}

	// Charge la catégorie
	$rcat = prd_categories_get($id);
	if( !ria_mysql_num_rows($rcat) ){
		return false;
	}
	$cat = ria_mysql_fetch_array($rcat);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( $cat['pos']==='' ){
		return false;
	}

	// S'assure que la catégorie n'est pas déjà en fin de la liste
	$rnext = ria_mysql_query('
		select cat_id from prd_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_parent_id'.( $cat['parent_id'] ? '='.$cat['parent_id'] : ' is null' ).'
			and cat_pos='.($cat['pos']+1)
	);
	if( !ria_mysql_num_rows($rnext) ){
		return false;
	}

	// Permute la catégorie avec celle qui se trouve juste au dessous
	ria_mysql_query('
		update prd_categories set cat_pos='.($cat['pos']+1).'
		where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id
	);
	ria_mysql_query('
		update prd_categories set cat_pos='.($cat['pos']).'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_pos='.($cat['pos']+1).'
			and cat_parent_id'.( $cat['parent_id'] ? '='.$cat['parent_id'] : ' is null' ).'
			and cat_id!='.$id
	);

	return true;
}
// \endcond

// \cond onlyria
/**	Indexe une catégorie par le moteur de recherche (les produits de la catégorie ne sont pas concernés).
 *	@param int $id Obligatoire, identifiant de la catégorie
 *	@param string $lng Facultatif, code d'une langue spécifique
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function prd_categories_index_add( $id, $lng=false ){
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];

	if( $cat = ria_mysql_fetch_array(prd_categories_get($id)) ){

		if( strtolower($lng)!=strtolower($config['i18n_lng']) ){
			$tsk_cat = fld_translates_get( CLS_CATEGORY, $cat['id'], $lng, $cat, array(_FLD_CAT_NAME=>'name', _FLD_CAT_TITLE=>'title', _FLD_CAT_DESC=>'desc', _FLD_CAT_URL=>'url_alias') );
			$cat['url_alias'] = $tsk_cat['url_alias']; $cat['name'] = $tsk_cat['name']; $cat['title'] = $tsk_cat['title']; $cat['desc'] = $tsk_cat['desc'];
		}

		// Mets à jour le nombre de produits publiés pour la catégorie (rentre en compte dans sa publication dans le moteur
		prd_categories_refresh_products_published($id);

		// Recherche ses domaines
		$dmn = array();
		$r_domains = prd_domain_categories_get(0, $id);
		while( $domains = ria_mysql_fetch_array($r_domains) ){
			$dmn[] = $domains['dmn_name'];
		}

		$description = html_strip_tags($cat['desc']);

		$ar_keywords = array();

		$tmp = page_obj_key( CLS_CATEGORY, array($id), true );
		if( trim($tmp)!='' ){
			$ar_keywords = array_merge( preg_split('/,/i',$tmp), $ar_keywords );
		}

		$tmp = page_obj_key( CLS_CATEGORY, array($id), false );
		if( trim($tmp)!='' ){
			$ar_keywords = array_merge( preg_split('/,/i',$tmp), $ar_keywords );
		}

		$ar_keywords = array_unique( $ar_keywords );
		$keywords = implode( ' ', array_unique($ar_keywords) );


		$publish = true;
		if ($cat['no_index'] == '1') {
			$publish = false;
		}else{
			$publish = prd_categories_is_published($cat['id']);

			if ($publish && !prd_categories_is_index($cat['id'])) {
				$publish = false;
			}
		}

		$cid = search_index_content( $cat['url_alias'], 'prd-cat', (implode(' ',$dmn)).' '.$cat['title'], $description, $cat['title'].' '.$cat['name'].' '.$cat['desc'].' '.$keywords, '/admin/catalog/index.php?cat='.$cat['id'], $publish, $cat['id'], false, '', '', $lng );

		if( strtolower($lng)==strtolower($config['i18n_lng']) && !ria_mysql_query('update prd_categories set cat_cnt_id='.$cid.' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id) ){
			return false;
		}

		// Met à jour l'image principale pour cette catégorie
		$img = ria_mysql_fetch_array( prd_cat_images_get($id) );
		search_contents_image_add($cid, $img['id']);

		return $cid;

	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Publie le résultat de moteur de recherche associé à une catégorie.
 *	@param int $id Identifiant de la catégorie
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_categories_index_publish( $id ){
	global $config;

	if( !is_numeric($id) ){
		return false;
	}

	if( $cid = ria_mysql_result(ria_mysql_query('select cat_cnt_id from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id),0,0) ){
		return search_contents_publish( $cid );
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Dépublie le résultat de moteur de recherche associé à une catégorie.
 *	@param int $id Identifiant de la catégorie
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_categories_index_unpublish( $id ){
	global $config;

	if( !is_numeric($id) ){
		return false;
	}

	if( $cid = ria_mysql_result(ria_mysql_query('select cat_cnt_id from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id),0,0) ){
		return search_contents_unpublish( $cid );
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Rafraichit le bit publié pour une entrée de catégorie dans le moteur de recherche
 *	@param int $id Identifiant de la catégorie
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_categories_index_publish_refresh( $id ){
	global $config;
	if( $cat = ria_mysql_fetch_array(prd_categories_get($id)) ){
		if( prd_categories_is_published($id) ){
			// Publie la catégorie
			search_contents_publish($cat['cnt_id']);
			// Publie les produits
			$products = ria_mysql_query('select cly_cnt_id as id from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$id.' and cly_cnt_id is not null');
			while( $r = ria_mysql_fetch_array($products) ){
				search_contents_publish($r['id']);
			}
		}else{
			// Dépublie la catégorie
			search_contents_unpublish($cat['cnt_id']);
			// Dépublie les produits
			$products = ria_mysql_query('select cly_cnt_id as id, cly_prd_id as prd from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$id.' and cly_cnt_id is not null');
			while( $r = ria_mysql_fetch_array($products) ){
				$rp = prd_products_get_simple( $r['prd'], '', true, $id );
				if( !$rp || !ria_mysql_num_rows($rp) ){
					search_contents_unpublish($r['id']);
				}
			}
		}
		return true;
	}
}
// \endcond

/**	Permet le test de l'existance d'une catégorie dans la base de données.
 *
 *	@param int $id Obligatoire, Identifiant de la catégorie à tester
 *	@param bool $ctrl_restrictions Facultatif, booléen indiquant si les éventuelles restrictions d'accès doivent être prises en compte dans la réponse.
 *		Valeur par défaut : true.
 *
 *	@return bool true si la catégorie existe dans la base, false si elle n'existe pas.
 *
 */
function prd_categories_exists( $id, $ctrl_restrictions=true ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$sql = 'select cat_id from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id.' and cat_date_deleted is null';

	if( $ctrl_restrictions && $config['use_catalog_restrictions'] ){
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'cat_id' ).')';
	}

	$sql .= ' limit 0,1';

	return ria_mysql_num_rows(ria_mysql_query( $sql ))>0;

}

/**	Permet le test de l'existance d'une catégorie dans la base de données à partir de sa référence.
 *
 *	@param string $ref Obligatoire, Référence de la catégorie à tester
 *	@param bool $ctrl_restrictions Facultatif, booléen indiquant si les éventuelles restrictions d'accès doivent être prises en compte dans la réponse.
 *		Valeur par défaut : true.
 *
 *	@return bool true si la catégorie existe dans la base, false si elle n'existe pas.
 *
 */
function prd_categories_exists_ref( $ref, $ctrl_restrictions=true ){
	global $config;

	if( !trim($ref) ){
		return false;
	}

	$sql = '
		select cat_id from prd_categories
		where cat_tnt_id='.$config['tnt_id'].' and cat_ref="'.addslashes( $ref ).'" and cat_date_deleted is null
	';

	if( $ctrl_restrictions && $config['use_catalog_restrictions'] ){
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'cat_id' ).')';
	}

	$sql .= ' limit 0,1';

	return ria_mysql_num_rows(ria_mysql_query( $sql ))>0;

}

/**	Permet le test de l'existance d'une catégorie dans la base de données à partir de son emplacement et de son nom.
 *
 * 	@param int $parent_id Obligatoire, identifiant de la catégorie parente (mettre 0 pour recherche une catégorie sans parente)
 *	@param string $name Obligatoire, nom de la catégorie à tester
 *	@param bool $ctrl_restrictions Facultatif, booléen indiquant si les éventuelles restrictions d'accès doivent être prises en compte dans la réponse.
 *		Valeur par défaut : true.
 *
 *	@return bool true si la catégorie existe dans la base, false si elle n'existe pas.
 *
 */
function prd_categories_exists_name( $parent_id, $name, $ctrl_restrictions=true ){
	global $config;

	if( !is_numeric($parent_id) ){
		return false;
	}

	if( !trim($name) ){
		return false;
	}

	$sql = '
		select cat_id from prd_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_name="'.addslashes( $name ).'"
	';

	if( $parent_id <= 0 ){
		$sql .= ' and (cat_parent_id = 0 or cat_parent_id is null)';
	}else{
		$sql .= ' and cat_parent_id='.$parent_id;
	}

	$sql .= '
			and cat_date_deleted is null
	';

	if( $ctrl_restrictions && $config['use_catalog_restrictions'] ){
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'cat_id' ).')';
	}

	$sql .= ' limit 0,1';

	return ria_mysql_num_rows(ria_mysql_query( $sql ))>0;

}

/**	Retourne la profondeur d'une catégorie dans l'arborescence.
 *	Les catégories de plus haut niveau appartiennent au niveau 0, et ainsi de suite.
 *	Les valeurs de profondeur sont absolues.
 *
 *	@param int $id Obligatoire, Identifiant de la catégorie
 *
 *	@return int La profondeur de la catégorie dans l'arborescence
 */
function prd_categories_depth_get( $id ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}
	if( $id==0 ){
		return 0;
	}

	return ria_mysql_num_rows(prd_categories_parents_get($id));
}

/**	Retourne l'ensemble des catégories parent de la catégorie passée en argument, triées par niveau décroissant.
 *	Les paramètre $limit et $offset deviennent obsolète si $id est un tableau de plusieurs ids.
 *
 *	@param int $id Obligatoire, identifiant ou tableau d'identifiants de catégorie dont on souhaite trouver les parents.
 *	@param int $limit Optionnel, nombre maximum de résultats à retourner (profondeur maximum à explorer)
 *	@param int $offset Optionnel, offset à partir duquel démarrer le résultat
 *	@param string $sort Optionnel, par défaut les catégories parentes sont retournées en partant du premier niveau, mettre 'desc' pour les retourner
 *		à partir du dernier niveau
 * 	@param array $filter Optionnel, tableau contenant les principaux filtres de récupération :
 * 		- fld : tableau de recherche sur les champs avancés
 * 		- or_between_val : dans un contexte où $fld est un tableau associatif, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque valeur possible d'un même champ (si non spécifié, la valeur logique est ET)
 *		- or_between_fld : dans un contexte où $fld est un tableau, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque champs (si non spécifié, la valeur logique est ET)
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : l'identifiant de la catégorie
 *			- name : la désignation de la catégorie
 *			- title : l'alias donné à la catégorie
 *			- desc : la description de la catégorie
 *			- keywords : les mots clés associés à la catégorie
 *			- url_alias : l'url simplifiée de la catégorie
 *			- depth : la profondeur de la catégorie dans l'arborescence
 *			- parent_id : l'identifiant de la catégorie parente
 *			- tag_title : nom personnalisé pour le référencement
 *
 */
function prd_categories_parents_get( $id, $limit=0, $offset=0, $sort='asc', $filter=[]){
	global $config;

	$ar_ids = control_array_integer( $id );
	if( !$ar_ids ){
		return false;
	}

	if( sizeof($ar_ids)>1 ){
		$limit = $offset = 0;
	}

	if( !is_numeric($limit) || $limit<=0 ){
		$limit = '18446744073709551615';
	}

	if( !is_numeric($offset) || $offset<=0 ){
		$offset = 0;
	}

	if( !in_array($sort, array('asc', 'desc')) ){
		$sort = 'asc';
	}

	$filter = [
		'fld'				=> ria_array_get( $filter, 'fld', false),
		'or_between_val'	=> ria_array_get( $filter, 'or_between_val', false),
		'or_between_fld'	=> ria_array_get( $filter, 'or_between_fld', false)
	];

	$sql = '
		select
			cat_child_id as child_id, cat_id as id, cat_name as name, if(cat_title!="",cat_title,cat_name) as title,
			cat_desc as "desc", cat_keywords as keywords,
			if(ifnull(cat_url_perso, "") = "", cat_url_alias, cat_url_perso) as url_alias,
			cat_parent_depth as depth, prd_categories.cat_parent_id as parent_id, cat_tag_title as tag_title
		from prd_cat_hierarchy, prd_categories
		where prd_cat_hierarchy.cat_tnt_id='.$config['tnt_id'].'
			and prd_cat_hierarchy.cat_tnt_id = prd_categories.cat_tnt_id
			and prd_cat_hierarchy.cat_parent_id = cat_id
			and cat_child_id in ( '.implode( ', ', $ar_ids ).' )
			and cat_date_deleted is null
	';

	if( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished'] ){
		$sql .= ' and ( (cat_publish and cat_is_sync) or not cat_is_sync ) ';
	}

	if (is_numeric($filter['fld']) || (is_array($filter['fld'] && count($filter['fld'])))) {
		$or_between_val = is_bool($filter['or_between_val']) ? $filter['or_between_val'] : false;
		$or_between_fld = is_bool($filter['or_between_fld']) ? $filter['or_between_fld'] : false;
		$sql .= fld_classes_sql_get( CLS_CATEGORY, $filter['fld'], $or_between_val, $or_between_fld);
	}

	$sql .= '
		order by '.sql_order_by_array( $ar_ids, 'cat_child_id', 'asc' ).', cat_parent_depth '.$sort.'
		limit '.$offset.','.$limit.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction retourne un tableau d'identifiants des catégories parents d'une ou plusieurs catégories données
 *	@param int $id Obligatoire, identifiant ou tableau d'identifiants de catégories
 *	@param bool $get_flat_list Optionnel, permet de retourner un tableau simple des identifiants parent (par défaut à False)
 *	@param string $sort Optionnel, par défaut les identifiants de catégories parentes sont retournées en partant du premier niveau, mettre 'desc'
 *		pour les retourner à partir du dernier niveau
 *
 *	@return array Si $id est un tableau, alors un tableau contenant pour chaque catégorie un tableau des identifiants de ses catégories parentes
 *	@return array Si $id est un numéro ou $get_flast_list est égal à True, alors un tableau simple contenant tous les identifiant des catégories parentes
 */
function prd_categories_parents_get_array( $id, $get_flat_list=false, $sort='asc' ){
	$ar_ids = control_array_integer( $id );
	if( !$ar_ids ){
		return array();
	}

	if( !in_array($sort, array('asc', 'desc')) ){
		$sort = 'asc';
	}

	$rcat = prd_categories_parents_get( $id, 0, 0, $sort );
	if( !$rcat || !ria_mysql_num_rows($rcat) ){
		return array();
	}

	$tmp = array();
	while( $cat = ria_mysql_fetch_array($rcat) ){
		if( is_numeric($id) || $get_flat_list ){
			$tmp[] = $cat['id'];
		}else{
			if( !isset($tmp[ $cat['child_id'] ]) ){
				$tmp[ $cat['child_id'] ] = array();
			}

			$tmp[ $cat['child_id'] ][] = $cat['id'];
		}
	}

	return is_numeric($id) || $get_flat_list ? array_unique( $tmp ) : $tmp;
}

// \cond onlyria
/**	Ajoute l'url virtuelle d'une catégorie donnée
 *	@param int $id identifiant de la catégorie
 *	@return string|bool L'url de la catégorie, ou False en cas d'échec
 */
function prd_categories_url_alias_add( $id ){
	global $config;

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) ){
		return false;
	}

	$url = false;

	// Crée les alias
	$alias = rew_rewritemap_generated( array($id), CLS_CATEGORY );

	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_CATEGORY);
		if( $prd_pages  ){
			$added = false;
			while( $page = ria_mysql_fetch_array($prd_pages) ){
				if( !$added ){
					rew_rewritemap_add_specify_class( CLS_CATEGORY, $alias.$page['key'], $alias.$page['key'].'/', 301, $wst['id'], false, null, $id );
					$url = rew_rewritemap_add_specify_class( CLS_CATEGORY, $alias.$page['key'].'/', $page['url'].( strstr($page['url'], '?') ? '&' : '?' ).'cat='.$id, 200, $wst['id'], false, null, $id );
				}else{
					rew_rewritemap_add_specify_class( CLS_CATEGORY, $alias.$page['key'], $alias.$page['key'].'/', 301, $wst['id'], false, null, $id );
					rew_rewritemap_add_specify_class( CLS_CATEGORY, $alias.$page['key'].'/', $page['url'].( strstr($page['url'], '?') ? '&' : '?' ).'cat='.$id, 200, $wst['id'], false, null, $id );
				}
				$added = true;
			}
		}
	}

	if( $url!==false ){
		ria_mysql_query('update prd_categories set cat_url_alias=\''.addslashes($url).'\' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);
	}

	return $url;
}
// \endcond

// \cond onlyria
/**	Retourne les catégories enfants et petits enfants d'une catégorie donnée.
 *
 *	@param int $id Obligatoire, Identifiant de la catégorie mère, ou tableau d'identifiants de catégories mères
 *	@param bool $published  Facultatif, Indique si toutes les sous-familles sont retournées ou seulement celles qui sont publiées
 *	@return string La liste des identifiants des catégories enfants, séparés par une virgule.
 *
 */
function prd_categories_childs_get_list( $id, $published=false ){
	static $prev_id = 0;
	static $prev_published = false;
	static $prev_childs = array();

	if( $id==$prev_id && $published==$prev_published ){
		return $prev_childs;
	}

	$childs = prd_categories_childs_get_array( $id, $published );
	$prev_childs = implode(',',$childs);
	$prev_published = $published;
	$prev_id = $id;

	return $prev_childs;
}
// \endcond

/**	Retourne les catégories enfants et petits enfants d'une catégorie donnée.
 *
 *	@param int $id Identifiant de la catégorie mère, ou tableau d'identifiants de catégories mères
 *	@param bool $published  Indique si toutes les sous-familles sont retournées (false), seulement celles qui sont publiées (true) ou non publiées (-1)
 *	@param $products Par défaut toutes les catégories enfants et petits enfants sont retournés, mettre true pour ne retourner que les catégories avec
 *		des produits
 *	@return array Un tableau des identifiants des catégories enfants. En cas d'erreur, un tableau vide est retourné
 *
 */
function prd_categories_childs_get_array( $id, $published=false, $products=false ){
	global $config;

	$childs = array();
	if( is_array($id) ){
		if( sizeof($id)==0 ) return $childs;

		foreach( $id as $cat ){
			if( !is_numeric($cat) ) return $childs;
		}

	}elseif( !is_numeric($id) ) return $childs;

	$sql = '
			select cat_child_id as id
			from prd_cat_hierarchy as hry, prd_categories
			where cat_date_deleted is null and
			hry.cat_tnt_id='.$config['tnt_id'].' and
			hry.cat_tnt_id=prd_categories.cat_tnt_id and
			hry.cat_parent_id'.( is_array($id) ? ' in ('.implode(',',$id).')' : '='.$id ).' and
			cat_child_id=cat_id
	';

	if( $published ){
		$sql .= ' and cat_publish = ' . ($published === -1 ? 0 : 1);
	}elseif( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true ){
		$sql .= ' and ( (cat_publish and cat_is_sync) or not cat_is_sync )';
	}

	if( $products ){
		$sql .= ' and cat_products';
	}

	if( $config['use_catalog_restrictions'] ){
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'cat_id' ).')';
	}

	if( $result = ria_mysql_query( $sql ) ){
		while( $r = ria_mysql_fetch_array( $result ) ){
			$childs[] = $r['id'];
		}
	}

	return $childs;
}

// \cond onlyria
/**	Retourne les identifiants des catégories parentes et des catégories enfant d'une catégorie donnée.
 *	@param int $id Identifiant de la catégorie
 *	@return resource Un résultat de requête MySQL :
 *		- hry : identifiants des catégories parentes et des catégories enfants de la catégorie passée en paramètre
 *		- is-parent : détermine si l'identifiant est un parent (ou supérieur) de la catégorie ou un enfant (ou inférieur)
 *		- depth : profondeur
 *		- title : nom de la catégorie destination
 *		- src : identifiant de la catégorie source (celle passé en paramètre)
 */
function prd_categories_hierarchy_get( $id ){
	global $config;

	if( !is_numeric($id) ){
		return false;
	}

	$sub_sql = '';
	if( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true ){
		$sub_sql = ' and ( (c.cat_is_sync and c.cat_publish) or not c.cat_is_sync ) ';
	}

	return ria_mysql_query('
		select c.cat_id as hry, 1 as "is-parent", cat_parent_depth as depth, if(ifnull(cat_title, \'\')=\'\', cat_name, cat_title) as title, h.cat_child_id as src
		from prd_cat_hierarchy as h, prd_categories as c
		where h.cat_tnt_id='.$config['tnt_id'].' and h.cat_child_id='.$id.' and
		c.cat_tnt_id=h.cat_tnt_id and c.cat_date_deleted is null and c.cat_id=h.cat_parent_id
		'.$sub_sql.'
		union
		select c.cat_id as hry, 0 as "is-parent", cat_parent_depth as depth, if(ifnull(cat_title, \'\')=\'\', cat_name, cat_title) as title, h.cat_parent_id as src
		from prd_cat_hierarchy as h, prd_categories as c
		where h.cat_tnt_id='.$config['tnt_id'].' and h.cat_parent_id='.$id.' and
		c.cat_tnt_id=h.cat_tnt_id and c.cat_date_deleted is null and c.cat_id=h.cat_child_id
		'.$sub_sql.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction retourne le nombre maximum de niveau de catégorie.
 *	@return int|bool Le nombre maximum de niveau de catégorie, False en cas d'erreur
 */
function prd_categories_hierarchy_get_max_depth(){
	global $config;

	$sql = '
		select max(cat_parent_depth) + 2 as max_depth
		from prd_cat_hierarchy
		where cat_tnt_id='.$config['tnt_id'].'
	';

	$res = ria_mysql_query( $sql );
	if( !$res && !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	return $r['max_depth'];
}
// \endcond

/**	Retourne l'ensemble des catégories présentes dans la base de données, éventuellement filtrées
 *	par les paramètres optionnels fournis.
 *
 *	Le résultat est fourni sous la forme d'un résultat de requête mysql,
 *	comprenant les colonnes :
 *		- id : identifiant de la catégorie
 *		- name : nom de la catégorie
 *		- url_alias : url simplifiée pour la catégorie
 *		- desc : description de la catégorie
 *		- keywords : mots clés spécifiques à la catégorie (pour le référencement avancé)
 *		- parent_id : identifiant de la catégorie parente
 *		- products : nombre de produits publiés dans la catégorie
 *		- publish : booléen indiquant si la catégorie est publiée ou non
 *		- cnt_id : identifiant de la catégorie dans le moteur de recherche
 *		- is_sync : booléen indiquant si la catégorie est synchronisée avec la gestion commerciale
 *
 *	Par défaut, les catégories sont retournées triées par nom.
 *
 *	Si l'un des paramètres optionnels est invalide, il est ignoré.
 *
 *	@param int $id Optionnel, identifiant d'une catégorie sur laquelle filtrer le résultat.
 *	@param bool $published  Optionnel, détermine si toutes les catégories sont retournées, ou seulement celles qui ont des produits publiés (ignoré si id est fourni).
 *	@param int $parent Optionnel, identifiant de la catégorie parent (permet la récupération des sous catégories). Ce paramètre est ignoré si le paramètre id est fourni.
 *	@param string $name Optionnel, nom de la catégorie
 *	@param $supplier Optionnel, identifiant d'un fournisseur sur lequel filtrer le résultat
 *	@param bool $isSync Optionnel, détermine si les catégories en retour sont uniquement celles synchronisées avec la gestion commerciale
 *	@param $indmn Optionnel, détermine si la catégorie fait parti d'un domaine (valeur possible true ou false)
 *	@param $sort Optionnel, paramètre de tri (actuellement disponible : "random" pour un tri aléatoire, "fld-[ID]" pour un tri sur les valeurs d'un champ avancé)
 *	@param int|array $brd Optionnel, tableau d'identifiants de marque sur lesquels filtrer le résultat
 *	@param $recursive_from_parent Optionnel, détermine si, quand $parent est spécifié et supérieur à 0, si les sous-catégories de deuxième niveau et inférieur sont retournées
 *	@param int|array $fld Optionnel, champ avancé sur lequel filtrer le résultat. Ce paramètre peut être un identifiant, un tableau d'identifiants ou un tableau associatif identifiant => valeur
 *	@param $or_between_val Optionnel, dans un contexte où $fld est un tableau associatif, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque valeur possible d'un même champ (si non spécifié, la valeur logique est ET)
 *	@param $or_between_fld Optionnel, dans un contexte où $fld est un tableau, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque champs (si non spécifié, la valeur logique est ET)
 *	@param string $lng Optionnel, permet de spécifier une autre langue que celle de la configuration actuelle. Utilse s'il existe des valeurs de champs avancés multilingues
 *	@param $exclude Optionnel, par défaut aucune catégorie est exclut, mettre l'identifiant ou un tableau d'identifiants de catégorie à exclure
 *	@param $not_only_destock Optionnel, par défaut à False, mettre True pour exclure les catégories ne contenant que des articles en déstockage
 *	@param $control_parent Optionnel, par défaut l'identifiant parent n'est pas prit en compte si $id est égal à un numérique, mettre true pour que ce soit le cas
 *	@param $rsl_id Optionnel, identifiant d'un revendeur sur lequel filtrer le résultat
 *	@param $cat_ref Optionnel, filtrage par référence de catégorie.
 *	@param int $rsl_wst_id Optionnel, filtrage du site internet pour le revendeur.
 *	@param bool $is_active Optionnel, par défaut toutes les catégories seront retourné, mettre true pour récupérer celle dont les dates de publication la rendent active
 *	@param bool $check_segments Optionnel, si activé verifie que la catégorie doit être affichée pour le compte connecté.
 *	@param bool $have_stock Optionnel, si vrai seul les catégories avec un article ayant du stock sont retournées, par défaut à false
 *	@param int $dps Optionnel, identifiant du dépôt à utiliser pour contrôler l'information de stock des articles
 *	@param bool|array $prd_fld Optionnel, permet de récupérer les catégories contenant des produits rattachés à un ou plusieurs d'un ou plusieurs champs avancé (si ce paramètre est renseigné, les catégories seront trié selon le nombre de produits correspondant à ce filtre). Il s'agit d'un tableau contenant : fld, or_between_val, or_between_fld et lng (les trois derniers sont optionnels).
 *	@param $is_not_actived Optionnel, par défaut toutes les catégories seront retourné, mettre true pour récupérer celle dont les dates de publication la rendent inactive
 *	@param array $prd_ids Optionnel, liste d'identifiants produits permettant de remonter les catégories qui contiennent ces produits ou contenant des enfants qui les contiennent
 *	@param bool|array $prd_fld_recursive Optionnel, permet de filtrer le prd_fld sur la hierarchie de la catégorie pas juste la catégorie en question
 *	@param $hierarchy_depth Optionnel, permet de filtrer en fonction de la profondeur de la catégorie dans l'arborescence
 *	@param int|null $pkg_id Optionnel, permet de filtrer en fonction du type de package des produits de la catégorie
 *
 *	@return resource Un résultat de requête MySQL, comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie
 *			- ref : référence de la catégorie
 *			- name : désignation de la catégorie
 *			- title : désignation de la catégorie (surcharge utilisable en cas de synchronisation)
 *			- desc : description de la catégorie
 *			- url_alias : url de la catégorie dans le site public
 *			- keywords : mots clés associés à la catégorie
 *			- parent_id : identifiant de la catégorie parente
 *			- products : nombre de produits publiés contenus dans cette catégorie
 *			- publish : booléen indiquant si la catégorie est publiée ou non
 *			- cnt_id : identifiant du résultat de moteur de recherche associé à la catégorie
 *			- pos : position d'affichage de la catégorie (ordre)
 *			- is_sync : booléen indiquant si la catégorie est synchronisée avec la gestion commerciale
 *			- tag_title : nom de la page pour le référencement
 *			- tag_desc : description de la page pour le référencement
 *			- date_from : date de début d'affichage de la catégorie, au format jj/mm/aaaa
 *			- hour_from : heure de début d'affichage de la catégorie, au format hh:mm
 *			- date_to : date de fin d'affichage de la catégorie, au format jj/mm/aaaa
 *			- hour_to : heure de fin de la catégorie, au format hh:mm
 *			- date_from_en : date de début d'affichage au format en
 *			- date_to_en : date de fin d'affichage au format en
 *			- first_publish : date de première publication
 *			- first_publish_en : date de première publication au format en
 *			- ref : référence de la catégorie
 *			- is_solde : si la catégorie est exclusivement réservée au solde
 *			- cod_id : identifiant d'un code promotion déterminant le contenu de la catégorie
 *			- no_index : si oui ou non la catégorie doit être indexée
 *			- is_active : si oui ou non la catégorie est active
 *
 */
function prd_categories_get(
	$id=0, $published=false, $parent=0, $name='', $supplier=false, $isSync=false, $indmn=null, $sort=false, $brd=array(),
	$recursive_from_parent = false, $fld=false, $or_between_val=false, $or_between_fld=false, $lng=false, $exclude=false,
	$not_only_destock=false, $control_parent=false, $rsl_id=false, $cat_ref=null, $rsl_wst_id=false, $is_active=false,
	$check_segments=false,  $have_stock=false, $dps=0, $prd_fld=false, $is_not_actived=false, $prd_ids=0, $prd_fld_recursive=false,
	$hierarchy_depth=false, $pkg_id=null
){
	global $config;

	$soldes = pmt_soldes_get_next_period();
	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : i18n::getLang();

	if( !is_numeric($id) && !is_array($id) ) return false;
	if( !is_numeric($parent) ) return false;
	$name = trim($name);
	if( $supplier!==false && !is_numeric($supplier) ) return false;

	if( $exclude!==false ){
		$exclude = control_array_integer( $exclude, false );
		if( !$exclude ){
			return false;
		}
	}

	if( is_array($id) && count($id) <= 0 ){
		return false;
	}

	if( $prd_fld !== false ){
		if( !is_array($prd_fld) ){
			return false;
		}

		if( !ria_array_key_exists(array( 'fld' ), $prd_fld) ){
			return false;
		}

		if( !isset($prd_fld['or_between_val']) ){
			$prd_fld['or_between_val'] = false;
		}

		if( !isset($prd_fld['or_between_fld']) ){
			$prd_fld['or_between_fld'] = false;
		}

		if( !isset($prd_fld['lng']) ){
			$prd_fld['lng'] = false;
		}else{
			$prd_fld['lng'] = strtolower2( $prd_fld['lng'] );

			if( !in_array($prd_fld['lng'], $config['i18n_lng_code']) ){
				return false;
			}
		}
	}

	$prd_ids = control_array_integer( $prd_ids, false );
	if ($prd_ids === false) {
		return false;
	}

	$sql =	'
		select cat_id as id, cat_name as name, if(cat_title="",cat_name,cat_title) as title,
			if(ifnull(cat_url_perso, \'\')=\'\', cat_url_alias, cat_url_perso) as url_alias, cat_url_perso as url_perso, cat_url_alias,
			cat_desc as "desc", cat_keywords as keywords,
			cat_parent_id as parent_id, cat_products as products, cat_publish as publish,
			cat_cnt_id as cnt_id, cat_pos as pos, cat_is_sync as is_sync,
			cat_tag_title as tag_title, cat_tag_desc as tag_desc,
			date_format(cat_date_from,"%d/%m/%Y") as "date_from", time_format(cat_date_from,"%H:%i") as "hour_from",
			date_format(cat_date_to,"%d/%m/%Y") as "date_to", time_format(cat_date_to,"%H:%i") as "hour_to",
			cat_date_from as date_from_en, cat_date_to as date_to_en,
			date_format(cat_first_publish, "%d/%m/%Y à %H:%i") as first_publish, cat_first_publish as first_publish_en,
			cat_ref as "ref", cat_is_soldes as is_soldes, cat_no_index as no_index,
			(cat_date_from<=now() and (cat_date_to is null or cat_date_to>now()) and (cat_is_soldes = 0 or (now()>="'.$soldes['start']['date'].'" and now()<="'.$soldes['stop']['date'].'"))) as is_active,

			date_format(cat_date_created,"%d/%m/%Y à %H:%i") as date_created,
			date_format(cat_date_modified,"%d/%m/%Y à %H:%i") as date_modified

		from prd_categories as c
		where cat_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null
	';

	// gestion des restrictions si variable activée
	if( isset($config['use_catalog_restrictions']) && $config['use_catalog_restrictions'] ){
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'c.cat_id' ).')';
	}

	if( $published && $supplier===false && $rsl_id===false ){
		$sql .= ' and cat_publish and cat_products>0 ';
	}
	elseif( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true )
		$sql .= ' and ( (cat_is_sync and cat_publish) or not cat_is_sync )';

	if( is_numeric($id) && $id>0 ) $sql .= ' and cat_id='.$id;

	if( !(is_numeric($id) && $id>0) || $control_parent ){
		if( $parent>0 ){
			if( !$recursive_from_parent ){
				$sql .= ' and cat_parent_id='.$parent;
			}else{
				$sql .= ' and cat_id in ( 	select cat_child_id
				from prd_cat_hierarchy
				where cat_parent_id = '.$parent.'
				and cat_tnt_id = '.$config['tnt_id'].' ) ';
			}
		}elseif( $parent!==-1 && !$id && !$name ){
			$sql .= ' and cat_parent_id is null';
		}
	}

	// Récupère en fonction de la profondeur
	if( is_numeric($hierarchy_depth) && $hierarchy_depth>=0 ){
		$sql .= ' and cat_id in ( 	select cat_child_id
				from prd_cat_hierarchy
				where cat_parent_depth = '.$hierarchy_depth.'
				and cat_tnt_id = '.$config['tnt_id'].' ) ';
	}

	if( is_array($id) ){
		$sql .= ' and cat_id in ('.implode(',',$id).')';
	}

	if( !is_numeric($dps) || $dps <= 0 ){
		if( isset($config['prd_deposits']) && $config['prd_deposits']=='use-customer' ){
			$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
		}else{
			$dps = prd_deposits_get_main();
		}
	}

	if( $name ) $sql .= " and cat_name='".addslashes($name)."'";

	$brands = array();
	if( is_array($brd) && sizeof($brd) ){
		foreach( $brd as $b ){
			if( is_numeric($b) && $b>0 )
				$brands[] = $b;
		}
	}

	if( $supplier!==false || sizeof($brands) ){

		$sql .= '
			and (exists (
				select 1 from prd_classify
				join prd_products on cly_prd_id=prd_id and cly_tnt_id=prd_tnt_id
				'.( $supplier!==false ? 'join prd_suppliers on prd_id=ps_prd_id and prd_tnt_id=ps_tnt_id' : '' ).'
				left join prd_stocks on prd_tnt_id=sto_tnt_id and prd_id=sto_prd_id and '.$dps.'=sto_dps_id and sto_is_deleted=0
				where prd_publish and prd_publish_cat and not prd_childonly and (not prd_sleep or (' . prd_stocks_get_sql() . '-ifnull(sto_prepa,0)>0))
				'.( $supplier!==false ? 'and ps_usr_id='.$supplier : '' ).'
				'.( sizeof($brands) ? 'and prd_brd_id in ('.implode(', ', $brands).')' : '' ).'
				and cly_tnt_id='.$config['tnt_id'].' and cly_cat_id=cat_id

				) or exists (

				select 1 from prd_cat_hierarchy as h
				join prd_classify on h.cat_tnt_id=cly_tnt_id and h.cat_child_id=cly_cat_id
				join prd_products on cly_prd_id=prd_id and cly_tnt_id=prd_tnt_id
				'.( $supplier!==false ? 'join prd_suppliers on prd_id=ps_prd_id and prd_tnt_id=ps_tnt_id' : '' ).'
				left join prd_stocks on prd_tnt_id=sto_tnt_id and prd_id=sto_prd_id and '.$dps.'=sto_dps_id and sto_is_deleted=0
				where prd_publish and prd_publish_cat and not prd_childonly and (not prd_sleep or (' . prd_stocks_get_sql() . '-ifnull(sto_prepa,0)>0))
				'.( $supplier!==false ? 'and ps_usr_id='.$supplier : '' ).'
				'.( sizeof($brands) ? 'and prd_brd_id in ('.implode(', ', $brands).')' : '' ).'
				and h.cat_tnt_id='.$config['tnt_id'].' and h.cat_parent_id=c.cat_id
			))
		';

	}

	if( $isSync!==false ){
		$sql .= ' and cat_is_sync = 1';
	}

	if( $indmn===true ){
		$sql .= ' and cat_id in ( select (pdc.dmc_cat_id) from prd_domain_categories pdc where pdc.dmc_tnt_id = '.$config['tnt_id'].')';
	}
	if( $indmn===false ){
		$sql .= ' and cat_id not in(select distinct(dmc_cat_id) from prd_domain_categories where dmc_tnt_id = '.$config['tnt_id'].')';
	}

	if( is_array($exclude) && sizeof($exclude) ){
		$sql .= ' and cat_id not in ('.implode(', ', $exclude).')';
	}

	if( $not_only_destock ){
		$sql .= '
			and (
				exists (
					select 1
					from prd_classify
						join prd_products on cly_tnt_id = prd_tnt_id and cly_prd_id = prd_id
					where cly_tnt_id = '.$config['tnt_id'].'
						and cly_cat_id = cat_id
						and ifnull(prd_date_deleted, "") = ""
						'.( $published ? ' and prd_publish and prd_publish_cat' : '' ).'
						and prd_sleep = 0
				) or exists (
					select 1
					from prd_classify
						join prd_products on cly_tnt_id = prd_tnt_id and cly_prd_id = prd_id
						join prd_cat_hierarchy as ch on ch.cat_tnt_id = cly_tnt_id and ch.cat_child_id=cly_cat_id
					where cly_tnt_id='.$config['tnt_id'].'
						and ch.cat_parent_id=c.cat_id
						and ifnull(prd_date_deleted, "") = ""
						'.( $published ? ' and prd_publish and prd_publish_cat' : '' ).'
						and prd_sleep = 0
				)
			)
		';
	}

	if( $have_stock ){
		$sql_stock_conditions = '
			prd_follow_stock = 0
			or ' . prd_stocks_get_sql() . ' - sto_prepa > 0
			or prd_countermark = 1
			or exists (
				select 1 from prd_products as pch
				join prd_hierarchy as ph on
					pch.prd_tnt_id = ph.prd_tnt_id and pch.prd_id = ph.prd_child_id
				left join prd_stocks as pchs on
					pch.prd_tnt_id = pchs.sto_tnt_id and pch.prd_id = pchs.sto_prd_id and '.$dps.' = pchs.sto_dps_id and pchs.sto_is_deleted=0
				where
					ph.prd_parent_id = prd_id and ph.prd_tnt_id = '.$config['tnt_id'].'
					and pch.prd_date_deleted is null and (
						pch.prd_follow_stock = 0 or
						' . prd_stocks_get_sql('pchs') . ' - ifnull(pchs.sto_prepa, 0) > 0 or
						pch.prd_countermark = 1
					)
			)
		';

		$sql .= '
			and (
				exists (
					select 1
					from prd_classify
						join prd_products as p on cly_tnt_id = prd_tnt_id and cly_prd_id = prd_id
						join prd_stocks on prd_tnt_id=sto_tnt_id and prd_id=sto_prd_id and '.$dps.'=sto_dps_id and sto_is_deleted=0
					where cly_tnt_id = '.$config['tnt_id'].'
						and cly_cat_id = cat_id
						and prd_date_deleted is null
						'.( $published ? ' and prd_publish and prd_publish_cat' : '' ).'
						and ('.$sql_stock_conditions.')
				) or exists (
					select 1
					from prd_classify
						join prd_products as p on cly_tnt_id = prd_tnt_id and cly_prd_id = prd_id
						join prd_cat_hierarchy as ch on ch.cat_tnt_id = cly_tnt_id and ch.cat_child_id=cly_cat_id
						join prd_stocks on prd_tnt_id=sto_tnt_id and prd_id=sto_prd_id and '.$dps.'=sto_dps_id and sto_is_deleted=0
					where cly_tnt_id='.$config['tnt_id'].'
						and ch.cat_parent_id=cat_id
						and prd_date_deleted is null
						'.( $published ? ' and prd_publish and prd_publish_cat' : '' ).'
						and ('.$sql_stock_conditions.')
				)
			)
		';
	}

	if( trim($cat_ref) ){
		$sql .= ' and cat_ref = "'.addslashes($cat_ref).'"';
	}

	$sql .= fld_classes_sql_get( CLS_CATEGORY, $fld, $or_between_val, $or_between_fld, $lng, 'c' );

	if( $rsl_id ){
		$sql .= '
			and (exists (
				select 1 from prd_resellers
				join prd_products p on prs_prd_id=prd_id and prs_tnt_id=p.prd_tnt_id
				join prd_classify on p.prd_id=cly_prd_id and p.prd_tnt_id=cly_tnt_id
				where
					prs_tnt_id = '.$config['tnt_id'].'
				and p.prd_date_deleted is null
			    and p.prd_publish=1
			    and p.prd_publish_cat=1
			    and p.prd_childonly=0
			    and p.prd_sleep=0
			    and prs_usr_id='.$rsl_id.'
			    and prs_publish=1
			    and cly_cat_id=c.cat_id ';

		if( $rsl_wst_id  && is_numeric($rsl_wst_id) ){
			$sql .= 'and prs_wst_id='.$rsl_wst_id;
		}

		$sql .= ' ) or exists ( ';

		$sql .= '
				select 1 from prd_resellers
				join prd_products p on prs_prd_id=prd_id and prs_tnt_id=p.prd_tnt_id
				join prd_hierarchy hp on p.prd_tnt_id=hp.prd_tnt_id and p.prd_id=hp.prd_child_id
				join prd_products hpp on hp.prd_tnt_id=hpp.prd_tnt_id and hp.prd_parent_id=hpp.prd_id
				join prd_classify on hp.prd_parent_id=cly_prd_id and p.prd_tnt_id=cly_tnt_id
				where
					prs_tnt_id = '.$config['tnt_id'].'
				and hpp.prd_date_deleted is null
			    and hpp.prd_publish=1
			    and hpp.prd_publish_cat=1
			    and hpp.prd_childonly=0
			    and p.prd_sleep=0
			    and prs_usr_id='.$rsl_id.'
			    and prs_publish=1
			    and cly_cat_id=c.cat_id ';

		if( $rsl_wst_id  && is_numeric($rsl_wst_id) ){
			$sql .= 'and prs_wst_id='.$rsl_wst_id;
		}

		$sql .= ' ) or exists ( ';

		$sql .= 'select 1 from prd_resellers
				join prd_products p on prs_prd_id=prd_id and prs_tnt_id=p.prd_tnt_id
				join prd_classify on p.prd_id=cly_prd_id and p.prd_tnt_id=cly_tnt_id
				join prd_cat_hierarchy h on cly_tnt_id=h.cat_tnt_id and cly_cat_id=h.cat_child_id
				where
					prs_tnt_id = '.$config['tnt_id'].'
				and p.prd_date_deleted is null
			    and p.prd_publish=1
			    and p.prd_publish_cat=1
			    and p.prd_childonly=0
			    and p.prd_sleep=0
			    and prs_usr_id='.$rsl_id.'
			    and prs_publish=1
			    and h.cat_parent_id=c.cat_id ';

		if( $rsl_wst_id  && is_numeric($rsl_wst_id) ){
			$sql .= 'and prs_wst_id='.$rsl_wst_id;
		}

		$sql .= ' ) or exists ( ';

		$sql .= 'select 1 from prd_resellers
				join prd_products p on prs_prd_id=prd_id and prs_tnt_id=p.prd_tnt_id
				join prd_hierarchy hp on p.prd_tnt_id=hp.prd_tnt_id and p.prd_id=hp.prd_child_id
				join prd_products hpp on hp.prd_tnt_id=hpp.prd_tnt_id and hp.prd_parent_id=hpp.prd_id
				join prd_classify on hp.prd_parent_id=cly_prd_id and p.prd_tnt_id=cly_tnt_id
				join prd_cat_hierarchy h on cly_tnt_id=h.cat_tnt_id and cly_cat_id=h.cat_child_id
				where
					prs_tnt_id = '.$config['tnt_id'].'
				and hpp.prd_date_deleted is null
			    and hpp.prd_publish=1
			    and hpp.prd_publish_cat=1
			    and hpp.prd_childonly=0
			    and p.prd_sleep=0
			    and prs_usr_id='.$rsl_id.'
			    and prs_publish=1
			    and h.cat_parent_id=c.cat_id ';

		if( $rsl_wst_id  && is_numeric($rsl_wst_id) ){
			$sql .= 'and prs_wst_id='.$rsl_wst_id;
		}

		$sql .= '))';
	}

	if( $is_active ){
		$sql .= '
			and cat_date_from<=now() and (cat_date_to is null or cat_date_to>now())
			and (cat_is_soldes = 0 or (now()>="'.$soldes['start']['date'].'" and now()<="'.$soldes['stop']['date'].'"))
		';
	}

	if($is_not_actived){
		$sql .= '
			and (cat_date_from < NOW() AND cat_date_to < NOW()) ';
	}

	if (count($prd_ids) > 0) {
		$sql .= '
			and (
				exists (
					select 1
					from prd_classify
					where cly_tnt_id = '.$config['tnt_id'].'
						and cly_cat_id = c.cat_id
						and cly_prd_id in ('.implode(', ', $prd_ids).')
				)

				or

				exists (
					select 1
					from prd_classify
						join prd_cat_hierarchy as h on (cly_tnt_id = h.cat_tnt_id and cly_cat_id = cat_child_id)
					where cly_tnt_id = '.$config['tnt_id'].'
						and cat_parent_id = c.cat_id
						and cly_prd_id in ('.implode(', ', $prd_ids).')
				)
			)
		';
	}
	// getsion du filtre sur le type de package des produits de la catégorie
	if( is_numeric($pkg_id) && $pkg_id > 0 ){
		$sql .= '
			and exists (
				select 1
				from prd_classify pc
					join prd_cat_hierarchy h on pc.cly_cat_id=h.cat_child_id and h.cat_tnt_id='.$config['tnt_id'].'
					join prd_colisage_classify pcc on pc.cly_prd_id = pcc.cly_prd_id and pcc.cly_tnt_id='.$config['tnt_id'].'
					join prd_colisage_types pct on pcc.cly_col_id = pct.col_id and pct.col_tnt_id='.$config['tnt_id'].'
				where pc.cly_tnt_id='.$config['tnt_id'].' and (h.cat_parent_id=cat_id or cly_cat_id=cat_id)
			 		and pct.col_pkg_id = '. $pkg_id .'
		';
		if( is_numeric($dps) && $dps > 0 ){
			$sql .= '
					and pct.col_dps_id = '. $dps .'
			';
		}
		$sql .= ' ) ';
	}

	if( $prd_fld !== false ){
		$sql .= '
			and (
				exists (
					select cly_cat_id
					from prd_classify
						join prd_products on ( prd_tnt_id = cly_tnt_id and prd_id = cly_prd_id )
					where cly_tnt_id = '.$config['tnt_id'].'
						and cly_cat_id = cat_id
					';

					$sql .= fld_classes_sql_get(CLS_PRODUCT, $prd_fld['fld'], $prd_fld['or_between_val'], $prd_fld['or_between_fld'], $prd_fld['lng'], $alias_tbl = '', $check_on_childs = '', $like_type = false, $case_insensitive = false);

				$sql .= '
					)
				';
		if( $prd_fld_recursive ){

			$sql .='	or

					exists (
						select 1
						from prd_classify
							join prd_cat_hierarchy as h on (cly_tnt_id = h.cat_tnt_id and cly_cat_id = cat_child_id)
							join prd_products on ( prd_tnt_id = cly_tnt_id and prd_id = cly_prd_id )
						where cly_tnt_id = '.$config['tnt_id'].'
							and cat_parent_id = c.cat_id
						';

						$sql .= fld_classes_sql_get(CLS_PRODUCT, $prd_fld['fld'], $prd_fld['or_between_val'], $prd_fld['or_between_fld'], $prd_fld['lng'], $alias_tbl = '', $check_on_childs = '', $like_type = false, $case_insensitive = false);

				$sql .= '
					)
			';
		}

		$sql .= '
			)
		';
	}

	if( $prd_fld === false ){
		// Tri du résultat (valeurs par défaut)
		if( $sort==false || !is_array($sort) || sizeof($sort)==0 )
			$sort = array();

		// Converti le paramètre de tri en SQL
		$sort_final = array();

		// Récupère un éventuel tri par prix
		$price_sort_dir = '';
		foreach( $sort as $col=>$dir ){
			$col = strtolower(trim($col));
			$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';
			if( substr($col, 0, 4)=='fld-' ){
				// tri par champ avancé
				$fld_id = substr( $col, 4, strlen($col)-4 );
				if( is_numeric($fld_id) && $fld_id>0 ){
					$type_fld = fld_fields_get_type( $fld_id );
					$numeric_types = array( FLD_TYPE_INT, FLD_TYPE_FLOAT, FLD_TYPE_REFERENCES_ID );
					$sub_sql = '
						(
							select
								'.( in_array( $type_fld, $numeric_types ) ? 'cast(pv_value as decimal)' : 'pv_value' ).'
							from
								fld_object_values
							where
								pv_tnt_id='.$config['tnt_id'].' and pv_lng_code="'.strtolower($lng).'" and pv_obj_id_0=c.cat_id and pv_fld_id='.$fld_id.'
						) '.$dir.'
					';
					if( $lng!=$config['i18n_lng'] && in_array( $lng, $config['i18n_lng_used'] ) ){
						// gestion des valeurs en langue étrangère
						$sub_sql = '
							(
								select
									'.( in_array( $type_fld, $numeric_types ) ? 'cast(ifnull(v1.pv_value, v2.pv_value) as decimal)' : 'ifnull(v1.pv_value, v2.pv_value)' ).'
								from
									fld_object_values as v2 left join fld_object_values as v1 on (
										"'.strtolower($lng).'"=v1.pv_lng_code and v2.pv_tnt_id=v1.pv_tnt_id and v2.pv_fld_id=v1.pv_fld_id and v2.pv_obj_id_0=v1.pv_obj_id_0
									)
								where
									v2.pv_tnt_id='.$config['tnt_id'].' and v2.pv_lng_code="'.$config['i18n_lng'].'" and v2.pv_obj_id_0=c.cat_id and v2.pv_fld_id='.$fld_id.'
							) '.$dir.'
						';
					}
					array_push( $sort_final, $sub_sql );
				}
			}else{
				switch( $col ){
					case 'set':
						if( is_array($id) && sizeof($id)>1 ){
							$query_orderby = 'case ';
							$cpt = 0;
							foreach( $id as $current_id ){
								$query_orderby .= 'when cat_id='.$current_id.' then '.$cpt.' ';
								$cpt++;
							}
							$query_orderby .= ' end asc';

							array_push( $sort_final, $query_orderby );
						}
						break;
					case 'random' :
						array_push( $sort_final, 'rand()' );
						break;
					case 'products' :
					array_push( $sort_final, 'cat_products ' . $dir );
					break;
				}
			}
		}

		// Ajoute la clause de tri
		if( sizeof($sort_final)==0 && (!is_numeric($id) || $id<=0) ) $sort_final = array( 'cat_pos asc', 'if(cat_title!="",cat_title,cat_name)' );
		if( is_array($sort_final) && sizeof($sort_final) )
			$sql .= ' order by '.implode( ', ', $sort_final ).' ';
	}elseif($prd_fld_recursive === false) {
		$sql .= '
			order by (
				select count(*)
				from prd_classify
					join prd_products on ( prd_tnt_id = cly_tnt_id and prd_id = cly_prd_id )
				where cly_tnt_id = cat_tnt_id
					and cly_cat_id = cat_id
		';

		$sql .= fld_classes_sql_get( CLS_PRODUCT, $prd_fld['fld'], $prd_fld['or_between_val'], $prd_fld['or_between_fld'], $prd_fld['lng'], $alias_tbl='', $check_on_childs='', $like_type=false, $case_insensitive=false );

		$sql .= '
			) desc
		';
	}

	$r = ria_mysql_query($sql);

	if( !$r ){
		error_log( mysql_error().' => '.$sql );
	}

	if( $config['tnt_id'] != 39 ){
		$check_segments = false;
	}

	if( $r && ria_mysql_num_rows($r) && $check_segments ){
		$id_ar = array();

		$usr_id = 0; // non connecté
		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] )
			$usr_id = $_SESSION['admin_view_user'];
		elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] )
			$usr_id = $_SESSION['usr_id'];

		while( $c = ria_mysql_fetch_array($r) ){
			if( seg_objects_check_segment( CLS_CATEGORY, $c['id'], CLS_USER, $usr_id ) ){
				$id_ar[] = $c['id'];
			}
		}

		if( !sizeof($id_ar) ) return false;

		return prd_categories_get( $id_ar, false, 0, '', false, false, null, $sort, array(), false, false, false, false, $lng, false, false, false, false, null, false, false, false );
	}

	return $r;
}

// \cond onlyria
/** Alias de prd_categories_get pour récupérer plus facilement a partir de la référence;
 * @param string $cat_ref Référence gescom de la catégorie
 * @param bool $publish Facultatif, filtre si la catégorie est publié ou non
 *
 * @see prd_categories_get
 */
function prd_categories_get_byref($cat_ref, $publish=false){
	return prd_categories_get( 0, $publish, -1, '', false, false, null, false,  array(),  false, false, false, false, false, false, false, false, false, $cat_ref);
}
// \endcond

// \cond onlyria
/**	Retourne l'ensemble des catégories présentes dans la base de données, éventuellement filtrées
 *	par les paramètres optionnels fournis.
 *
 *	Si l'argument $id est renseigné, le résultat est fourni sous la forme d'un tableau associatf,
 *	comprenant les clés suivantes :
 *		- id : identifiant de la catégorie
 *		- name : nom de la catégorie
 *		- url_alias : url simplifiée pour la catégorie
 *		- desc : description de la catégorie
 *		- keywords : mots clés spécifiques à la catégorie (pour le référencement avancé)
 *		- parent_id : identifiant de la catégorie parente
 *		- products : nombre de produits publiés dans la catégorie
 *		- publish : booléen indiquant si la catégorie est publiée ou non
 *		- cnt_id : identifiant de la catégorie dans le moteur de recherche
 *		- is_sync : booléen indiquant si la catégorie est synchronisée avec la gestion commerciale
 *
 *	Si le paramètre $id est à 0, la fonction retournera un tableau contenant les catégories sous forme de tableaux associatif
 *
 *	Les catégories sont retournées triées par nom.
 *
 *	Si l'un des paramètres optionnels est invalide, il est ignoré.
 *
 *	@param int $id Optionnel, identifiant d'une catégorie sur laquelle filtrer le résultat.
 *	@param bool $published  Optionnel, détermine si toutes les catégories sont retournées, ou seulement celles qui ont des produits publiés (ignoré si id est fourni).
 *	@param int $parent Optionnel, identifiant de la catégorie parent (permet la récupération des sous catégories). Ce paramètre est ignoré si le paramètre id est fourni.
 *	@param string $name Optionnel, nom de la catégorie
 *	@param $supplier Optionnel, identifiant d'un fournisseur sur lequel filtrer le résultat
 *	@param bool $isSync Optionnel, détermine si les catégories en retour sont uniquement celles synchronisées avec la gestion commerciale
 *	@param $indmn Optionnel, détermine si la catégorie fait parti d'un domaine (valeur possible true ou false)
 *	@param $sort Optionnel, paramètre de tri (actuellement disponible : "random" pour un tri aléatoire, "fld-[ID]" pour un tri sur les valeurs d'un champ avancé)
 *	@param array $brd Optionnel, tableau d'identifiants de marque sur lesquels filtrer le résultat
 *	@param $recursive_from_parent Optionnel, détermine si, quand $parent est spécifié et supérieur à 0, si les sous-catégories de deuxième niveau et inférieur sont retournées
 *	@param int|array $fld Optionnel, champ avancé sur lequel filtrer le résultat. Ce paramètre peut être un identifiant, un tableau d'identifiants ou un tableau associatif identifiant => valeur
 *	@param $or_between_val Optionnel, dans un contexte où $fld est un tableau associatif, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque valeur possible d'un même champ (si non spécifié, la valeur logique est ET)
 *	@param $or_between_fld Optionnel, dans un contexte où $fld est un tableau, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque champs (si non spécifié, la valeur logique est ET)
 *	@param string $lng Optionnel, permet de spécifier une autre langue que celle de la configuration actuelle. Utilse s'il existe des valeurs de champs avancés multilingues
 *	@param $exclude Optionnel, par défaut aucune catégorie est exclut, mettre l'identifiant ou un tableau d'identifiants de catégorie à exclure
 *	@param $not_only_destock Optionnel, par défaut à False, mettre True pour exclure les catégories ne contenant que des articles en déstockage
 *	@param $control_parent Optionnel, par défaut l'identifiant parent n'est pas prit en compte si $id est égal à un numérique, mettre true pour que ce soit le cas
 *	@param $rsl_id Optionnel, identifiant d'un revendeur sur lequel filtrer le résultat
 *	@param $cat_ref Optionnel, filtrage par référence de catégorie.
 *	@param $rsl_wst_id Optionnel, filtrage du site internet pour le revendeur.
 *	@param bool $is_active Optionnel, par défaut toutes les catégories seront retourné, mettre true pour récupérer celle dont les dates de publication la rendent active
 *	@param bool $check_segments Optionnel, si activé verifie que la catégorie doit être affichée pour le compte connecté.
 *
 *	@return Si $cat est égal à 0 ou un tableau d'identifiants, un tableau contenant pour chaque élément un tableau associatif décrivant la catégorie.
 *	@return Si $cat est différent de 0, un tableau associatif comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie, ou tableau d'identifiants de catégories
 *			- name : désignation de la catégorie
 *			- title : désignation de la catégorie
 *			- url_alias : url de la catégorie dans le site publique
 *			- desc : description de la catéogrie
 *			- keywords : mots clés associés à la catégorie
 *			- parent_id : identifiant de la catégorie parente
 *			- products : nombre de produits publiés contenus dans cette catégorie
 *			- publish : booléen indiquant si la catégorie est publiée ou non
 *			- cnt_id : identifiant du résultat de moteur de recherche associé à la catégorie
 *			- pos : position d'affichage de la catégorie (ordre)
 *			- is_sync : booléen indiquant si la catégorie est synchronisée avec la gestion commerciale
 *			- tag_title : nom de la page pour le référencement
 *			- tag_desc : description de la page pour le référencement
 *			- date_from : date de début d'affichage de la catégorie, au format jj/mm/aaaa
 *			- hour_from : heure de début d'affichage de la catégorie, au format hh:mm
 *			- date_to : date de fin d'affichage de la catégorie, au format jj/mm/aaaa
 *			- hour_to : heure de fin de la catégorie, au format hh:mm
 *			- date_from_en : date de début d'affichage au format en
 *			- date_to_en : date de fin d'affichage au format en
 *			- first_publish : date de première publication
 *			- first_publish_en : date de première publication au format en
 *			- ref : référence de la catégorie
 *
 */
function prd_categories_get_array( $id=0, $published=false, $parent=0, $name='', $supplier=false, $isSync=false, $indmn=null, $sort=false, $brd = array(), $recursive_from_parent = false, $fld=false, $or_between_val=false, $or_between_fld=false, $lng=false, $exclude=false, $not_only_destock=false, $control_parent=false, $rsl_id=false, $cat_ref=null, $rsl_wst_id=false, $is_active=false, $check_segments=false ){
	global $config, $memcached;

	// Construit la clé memcached
	$ar_key = array(
		'fn' => 'prd_categories_get_array',
		'tnt' => $config['tnt_id'],
		'id' => is_array($id) ? implode( ',', $id ) : $id,
		'published' => $published,
		'parent' => $parent,
		'name' => $name,
		'supplier' => $supplier,
		'isSync' => $isSync,
		'indmn' => $indmn,
		'sort' => is_array($sort) ? implode( ',', $sort ) : $sort,
		'brd' => is_array($brd) ? implode( ',', $brd ) : $brd,
		'recursive_from_parent' => $recursive_from_parent,
		'fld' => is_array($fld) ? implode( ',', $fld ) : $fld,
		'or_between_val' => is_array($or_between_val) ? implode( ',', $or_between_val ) : $or_between_val,
		'or_between_fld' => is_array($or_between_fld) ? implode( ',', $or_between_fld ) : $or_between_fld,
		'lng' => $lng,
		'exclude' => is_array($exclude) ? implode( ',', $exclude ) : $exclude,
		'not_only_destock' => $not_only_destock,
		'control_parent' => $control_parent,
		'rsl_id' => $rsl_id,
		'cat_ref' => $cat_ref,
		'rsl_wst_id' => $rsl_wst_id,
		'is_active' => $is_active,
		'check_segments' => $check_segments
	);
	$mkey = 'prd_categories_get_array:'.md5( serialize( $ar_key ) );

	// Si le résultat existe déjà en cache, retourne le résultat en cache
	$mget = $memcached->get( $mkey );
	if( $mget && $mget!='' ){
		return $mget;
	}


	// Interroge prd_categories_get et transforme son résultat en tableau associatif
	$categories = array();
	$rcategories = prd_categories_get( $id, $published, $parent, $name, $supplier, $isSync, $indmn, $sort, $brd, $recursive_from_parent, $fld, $or_between_val, $or_between_fld, $lng, $exclude, $not_only_destock, $control_parent, $rsl_id, $cat_ref, $rsl_wst_id, $is_active, $check_segments );
	if( is_numeric($id) && $id>0 ){
		if( !$rcategories || !ria_mysql_num_rows($rcategories) ){
			return false;
		}

		$categories = ria_mysql_fetch_assoc($rcategories);
	}else{
		if( !$rcategories ){
			return false;
		}

		while( $cat = ria_mysql_fetch_assoc($rcategories) ){
			$categories[] = $cat;
		}
	}

	// Stocke le résultat dans memcached pour usage ultérieur
	$memcached->set( $mkey, $categories, 60 * 30 );


	// Retourne le résultat
	return $categories;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les informations sur une catégorie tout en vérifiant qu'elle appartient à une arborescence
 *
 *	@param int $id Obligatoire, identifiant d'une catégorie sur laquelle filtrer le résultat.
 *	@param int $parent Optionnel, identifiant de la catégorie parent (permet la récupération des sous catégories). Ce paramètre est ignoré si le paramètre id est fourni.
 *	@param bool $published  Optionnel, détermine si toutes les catégories sont retournées, ou seulement celles qui ont des produits publiés (ignoré si id est fourni).
 *	@param $recursive_from_parent Optionnel, détermine si, quand $parent est spécifié et supérieur à 0, si les sous-catégories de deuxième niveau et inférieur sont retournées
 *	@param $get_param Optionnel, tableau de paramètres de filtre supplémentaires (name, supplier, isSync, indmn, sort, brd, fld, or_between_val, or_between_fld, lng, exclude, not_only_destock)
 *
 *	@return Le même résultat que prd_categories_get()
 */
function prd_categories_get_in_hierarchy( $id, $parent, $published=false, $recursive_from_parent=false, $get_param=array() ){
	if( !isset($get_param['name']) ){
		$get_param['name'] = '';
	}
	if( !isset($get_param['supplier']) ){
		$get_param['supplier'] = false;
	}
	if( !isset($get_param['isSync']) ){
		$get_param['isSync'] = false;
	}
	if( !isset($get_param['indmn']) ){
		$get_param['indmn'] = null;
	}
	if( !isset($get_param['sort']) ){
		$get_param['sort'] = false;
	}
	if( !isset($get_param['brd']) ){
		$get_param['brd'] = array();
	}
	if( !isset($get_param['fld']) ){
		$get_param['fld'] = false;
	}
	if( !isset($get_param['or_between_val']) ){
		$get_param['or_between_val'] = false;
	}
	if( !isset($get_param['or_between_fld']) ){
		$get_param['or_between_fld'] = false;
	}
	if( !isset($get_param['lng']) ){
		$get_param['lng'] = false;
	}
	if( !isset($get_param['exclude']) ){
		$get_param['exclude'] = false;
	}
	if( !isset($get_param['not_only_destock']) ){
		$get_param['not_only_destock'] = false;
	}
	if( !isset($get_param['rsl_id']) ){
		$get_param['rsl_id'] = false;
	}
	if( !isset($get_param['cat_ref']) ){
		$get_param['cat_ref'] = null;
	}
	if( !isset($get_param['rsl_wst_id']) ){
		$get_param['rsl_wst_id'] = false;
	}
	if( !isset($get_param['is_active']) ){
		$get_param['is_active'] = false;
	}
	return prd_categories_get( $id, $published, $parent, $get_param['name'], $get_param['supplier'], $get_param['isSync'], $get_param['indmn'], $get_param['sort'], $get_param['brd'], $recursive_from_parent, $get_param['fld'], $get_param['or_between_val'], $get_param['or_between_fld'], $get_param['lng'], $get_param['exclude'], $get_param['not_only_destock'], true, $get_param['rsl_id'], $get_param['cat_ref'], $get_param['rsl_wst_id'], $get_param['is_active'] );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de connaître le nombre de catégories disponible dans la base de données.
 *	@param bool $publish Optionnel mettre à true si on veut connaître le nombre de catégories publiée
 *	@return le nombre de catégories publiées, False en cas d'erreur
 */
function prd_categories_count( $publish=false ){
	global $config;

	$sql =	'
		select count(cat_id)
		from prd_categories
		where cat_tnt_id='.$config['tnt_id'].' and
		cat_date_deleted is null
	';

	if( $publish )
		$sql .= ' and cat_publish';
	elseif( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true )
		$sql .= ' and ( (cat_is_sync and cat_publish) or not cat_is_sync )';

	$result = ria_mysql_query( $sql );
	if( $result==false ) return false;

	return ria_mysql_result( $result,0,0 );
}
// \endcond

/** Permet de récupérer l'identifiant de la catégorie parent d'une catégorie
 *	@param int $id Obligatoire, identifiant d'une catégorie
 *	@return int Retourne l'identifiant de la catégorie parent d'une catégorie
 *	@return bool Retourne false si le paramètre est omis ou bien si la catégorie n'existe pas
 */
function prd_categories_get_parent_id( $id ){
	if( !prd_categories_exists($id) ) return false;
	global $config;

	$res = ria_mysql_query( 'select cat_parent_id as parent from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'parent' );
}

/** Permet de retourner le nombre de produit publié dans une catégorie
 *	@param int $id Identifiant d'une catégorie
 *	@return Le nombre de produit publié dans la catégorie
 *	@return bool False si le paramètre est omis ou bien si la catégorie n'existe pas
 */
function prd_categories_get_prd_count( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query( 'select cat_products as products from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null and cat_id='.$id );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'products' );
}

/** Permet de récupérer le nom d'une catégorie
 *	@param int $id Obligatoire, identifiant d'une catégorie
 *	@param bool $title Optionnel, par défaut le nom est retourné, mettre True pour prendre la surcharge
 *	@return string Retourne le nom de la catégorie
 *	@return bool Retourne false si le paramètre est omis ou bien si la catégorie n'existe pas
 */
function prd_categories_get_name( $id, $title=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$sub_sql = !$title ? 'cat_name' : 'if(ifnull(cat_title, "")="", cat_name, cat_title)';
	$res = ria_mysql_query('
		select '.$sub_sql.' as name
		from prd_categories
		where cat_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null and cat_id='.$id
	);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'name' );
}

// \cond onlyria
/** Cette fonction permet de récupérer le chemin d'une catégorie sous forme de chaines de caractères.
 *	Chaque catégorie sera séparée par un caractère paramétrable.
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie
 *	@param $caract Optionnel, caratère de séparation par défaut : ">"
 */
function prd_categories_get_ariane( $cat_id, $caract='>' ){
	if( !is_numeric($cat_id) || $cat_id<=0 ){
	    return false;
	}

	global $config;

	$ar_categs = array();

	$rparent = prd_categories_parents_get( $cat_id );
	if( $rparent && ria_mysql_num_rows($rparent) ){
	    while( $parent = ria_mysql_fetch_array($rparent) ){
	    	$ar_categs[] = $parent['name'];
	    }
	}

	$ar_categs[] = prd_categories_get_name( $cat_id );
	return implode( ' '.$caract.' ', $ar_categs );
}
// \endcond

// \cond onlyria
/**	Permet le chargement d'une catégorie par son url. Les champs retournés sont identiques à la fonction
 *	\c prd_categories_get. Seules les catégories publiées sont retournées par cette fonction.
 *	@param string $url Url "rewritée" d'une catégorie
 *	@return resource un résultat de requête MySQL comprenant les mêmes colonnes que le résultat de la fonction \c prd_categories_get.
 */
function prd_categories_get_byurl( $url ){
	global $config;
	$rcat = ria_mysql_query('select cat_id from prd_categories where cat_tnt_id='.$config['tnt_id'].' and (cat_url_alias="'.addslashes($url).'" or cat_url_perso="'.addslashes($url).'")');
	if( !ria_mysql_num_rows($rcat) ) return false;
	return prd_categories_get( ria_mysql_result($rcat,0,0), true );
}
// \endcond

/** Cette fonction retourne l'url d'une catégorie
 *	@param int|array $cat Obligatoire, identifiant d'une catégorie (ou tableau d'identifiants)
 *	@param $force_array Optionnel, si activé le résultat, en cas de succès, est forcément un tableau (même si un seul identifiant)
 *	@return Retourne l'url de la catégorie si elle existe (ou tableau d'urls, les résultats sont alors triés par ID croissant)
 *	@return bool Retourne false dans le cas contraire
 */
function prd_categories_get_url( $cat, $force_array=false ){
	if( is_array($cat) ){
		if( !sizeof($cat) ){
			return false;
		}
		foreach( $cat as $c ){
			if( !is_numeric($c) || $c<=0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($cat) || $cat<=0 ){
			return false;
		}
		$cat = array($cat);
	}
	global $config;

	$sql = '
		select if(ifnull(cat_url_perso, \'\')=\'\', cat_url_alias, cat_url_perso) as cat_url_alias
		from prd_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id in ('.implode(', ', $cat).')
	';
	if( sizeof($cat) > 1 ){
		$sql .= ' order by cat_id asc';
	}

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
		return false;
	}

	$cats_url = array();
	while( $cr = ria_mysql_fetch_array($res) ){
		$cats_url[] = $cr['cat_url_alias'];
	}

	if( sizeof($cat) == 1 && !$force_array ){
		return $cats_url[0];
	}

	return $cats_url;
}

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information d'indexation d'une catégorie
 *	@param int $cat_id Obligatoire, identifiant de la catégorie
 *	@param $index Obligatoire, mettre True pour indexer la catégorie, False pour ne pas indéxer
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_categories_set_index( $cat_id, $index ){
	if( !is_numeric($cat_id) || $cat_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update prd_categories
		set cat_no_index = '.( $index ? '0' : '1' ).'
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$cat_id;

	try{
		// Index la catégorie de produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_CATEGORY,
			'obj_id_0' => $cat_id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	$r_cats = prd_categories_get(0, false, $cat_id, '', false, false, null, false, array(), true);

	if( $r_cats && ria_mysql_num_rows($r_cats) ){
		while( $cat = ria_mysql_fetch_assoc($r_cats) ){
			try{
				// Index la catégorie de produit dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
					'cls_id' => CLS_CATEGORY,
					'obj_id_0' => $cat['id'],
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}
		}
	}

	$r_prds = prd_products_get_simple(0, '', false, $cat_id, true);

	if( $r_prds && ria_mysql_num_rows($r_prds) ){
		while( $prd = ria_mysql_fetch_assoc($r_prds) ){
			try{
				// Index la catégorie de produit dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
					'cls_id' => CLS_PRODUCT,
					'obj_id_0' => $prd['id'],
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}

			if( isset($prd['cat_id']) ){
				try{
					// Index la catégorie de produit dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
						'cls_id' => CLS_CLASSIFY,
						'obj_id_0' => $prd['id'],
						'obj_id_1' => $prd['cat_id'],
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}
			}
		}
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'url d'une catégorie
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param string $url Obligatoire, url personnsalisée d'une catégorie
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_categories_set_url( $cat, $url ){
	if( !is_numeric($cat) || $cat<=0 ){
	    return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_categories
		set cat_url_alias = "'.addslashes( $url ).'"
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$cat.'
	');

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'url personnsalisée d'une catégorie.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param string $url Obligatoire, url personnsalisée d'une catégorie
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_categories_set_url_perso( $cat, $url ){
	if( !is_numeric($cat) || $cat<=0 ){
	    return false;
	}

	if( trim($url)=='' ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_categories
		set cat_url_perso=\''.addslashes( $url ).'\'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat.'
	');

	if( !$res ){
		return false;
	}

	$rcnt = ria_mysql_query('
		select cat_cnt_id as cnt_id
		from prd_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat.'
	');

	if( $rcnt && ria_mysql_num_rows($rcnt) ){
	    $cnt = ria_mysql_fetch_array( $rcnt );

	    if( $cnt['cnt_id'] ){
	    	$res = ria_mysql_query('
	    		update search_contents
	    		set cnt_url=\''.addslashes( $url ).'\'
	    		where cnt_tnt_id='.$config['tnt_id'].'
	    			and cnt_id='.$cnt['cnt_id'].'
	    			and cnt_type_id=1
    		');

    		if( !$res ){
    			return false;
    		}
	    }
	}

	return true;
}
// \endcond// \cond onlyria
/** Cette fonction permet de supprimer l'url personnsalisée d'une catégorie tout en remettant l'url de base pour la recherche de contenu
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param string $base_url Obligatoire, url personnsalisée d'une catégorie
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_categories_del_and_set_base_url_perso( $cat, $base_url ){
	if( !is_numeric($cat) || $cat<=0 ){
	    return false;
	}

	if( trim($base_url)=='' ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_categories
		set cat_url_perso=\'\'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat.'
	');

	if( !$res ){
		return false;
	}

	$rcnt = ria_mysql_query('
		select cat_cnt_id as cnt_id
		from prd_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat.'
	');

	if( $rcnt && ria_mysql_num_rows($rcnt) ){
	    $cnt = ria_mysql_fetch_array( $rcnt );

	    if( $cnt['cnt_id'] ){
	    	$res = ria_mysql_query('
	    		update search_contents
	    		set cnt_url=\''.addslashes( $base_url ).'\'
	    		where cnt_tnt_id='.$config['tnt_id'].'
	    			and cnt_id='.$cnt['cnt_id'].'
	    			and cnt_type_id=1
    		');

    		if( !$res ){
    			return false;
    		}
	    }
	}

	return true;
}
// \endcond

/**	Cette fonction permet le chargement des marques représentées dans une catégorie donnée.
 *	@param int $cat Identifiant de la catégorie, ou l'une des valeurs spéciales new, promotions, destockage
 *	@param bool $published  Optionnel, indique si seul les produits publiés sont pris en compte pour l'extraction des marques (valeur par défaut: \c true)
 *	@param bool $catchilds Optionnel, indique si les catégories enfants sont également prises en compte (valeur par défaut: \c true)
 *	@param bool $centralized Optionnel, indique si seul les produits centralisés sont pris en compte (valeur par défaut: \c false)
 *	@param $new Optionnel, indique si seul les produits nouveaux sont pris en compte (valeur par défaut : \c false)
 *	@param $promotion Optionnel, indique si seul les produits en promotion sont pris en compte (valeur par défaut : \c false)
 *	@param $destockage Optionnel, indique si seul les produits en déstockage sont pris en compte (valeur par défaut : \c false)
 *	@param int|array $fld Optionnel, ID de champ avancé, ou tableau de champs avancés, ou tableau associatif ID => valeur de champ
 *	@param $or_between_val Optionnel, condition booléenne entre les valeurs d'un même champ
 *	@param $or_between_fld Optionnel, condition booléenne entre les champs
 *	@param string $lng Optionnel, langue de filtre des champs avancés (attention si non spécifié prend la langue par défaut et pas la langue courante)
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la marque
 *			- title : désignation de la marque
 *			- name : nom de la marque
 *			- url : url du site internet de la marque
 *			- img_id : identifiant de l'image représentant la marque
 *			- products : nombre de produits de cette marque dans la catégorie
 */
function prd_categories_brands_get( $cat, $published=true, $catchilds=true, $centralized=false, $new=false, $promotion=false, $destockage=false, $fld=false, $or_between_val=false, $or_between_fld=false, $lng=false ){
	global $config;

	$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();

	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : $config['i18n_lng'];

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}
	$sql = '
		select brd_id as id, if(brd_title!="",brd_title,brd_name) as title, brd_name as name,
			brd_url as url, brd_img_id as img_id, count(prd_id) as products
	';
	if( $promotion ){
		$sql .= '
			from prd_products
				inner join prd_brands on (brd_tnt_id='.$config['tnt_id'].' and prd_brd_id=brd_id)
				inner join prc_prices on (prc_tnt_id='.$config['tnt_id'].' and prd_id=prc_prd_id)
				left join prd_stocks on (sto_tnt_id='.$config['tnt_id'].' and prd_id=sto_prd_id and sto_dps_id='.$dps.' and sto_is_deleted=0)
			where prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null
				and prc_date_start<=now()
				and prc_date_end>now()
				and prc_is_promotion=1
				and prc_is_deleted=0
		';
	}elseif( $catchilds ){
		$sql .= '
			from prd_classify
				inner join prd_products on (prd_tnt_id='.$config['tnt_id'].' and cly_prd_id=prd_id)
				inner join prd_brands on (brd_tnt_id='.$config['tnt_id'].' and prd_brd_id=brd_id)
				left join prd_stocks on (sto_tnt_id='.$config['tnt_id'].' and prd_id=sto_prd_id and sto_dps_id='.$dps.' and sto_is_deleted=0)
			where cly_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null
				and (cly_cat_id='.$cat.' or cly_cat_id in (select cat_child_id from prd_cat_hierarchy where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id='.$cat.'))
		';
	}else{
		$sql .= '
			from prd_classify
				inner join prd_products on (prd_tnt_id='.$config['tnt_id'].' and cly_prd_id=prd_id)
				inner join prd_brands on (brd_tnt_id='.$config['tnt_id'].' and prd_brd_id=brd_id)
				left join prd_stocks on (sto_tnt_id='.$config['tnt_id'].' and prd_id=sto_prd_id and sto_dps_id='.$dps.' and sto_is_deleted=0)
			where cly_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null
				and cly_cat_id='.$cat.'
		';
	}
	$sql .= ' and brd_date_deleted is null and not prd_childonly ';

	if( $new ){
		$sql .= ' and prd_new!="-1" and (prd_new="1" or datediff(now(),prd_date_created)<='.$config['prd_new_days'].') ';
	}elseif( $destockage ){
		$sql .= ' and prd_publish and prd_sleep and (' .  prd_stocks_get_sql() . '-sto_prepa)>0';
	}

	if( $published )
		$sql .= ' and prd_publish and prd_publish_cat and (not prd_sleep or (' .  prd_stocks_get_sql() . '-sto_prepa)>0) and not prd_childonly';
	elseif( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true )
		$sql .= ' and ( (prd_is_sync and prd_publish) or not prd_is_sync )';
	if( $centralized )
		$sql .= ' and prd_centralized ';

	$sql .= fld_classes_sql_get( CLS_PRODUCT, $fld, $or_between_val, $or_between_fld, $lng );

	$sql .= '
		group by brd_id, if(brd_title!="",brd_title,brd_name), brd_url, brd_img_id
		order by if(brd_title!="",brd_title,brd_name)
	';

	return ria_mysql_query($sql);
}

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information de position d'une catégorie
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie
 *	@param int $pos Obligatoire, position à lui affecter (entier supérieur ou égal à zéro)
 *	@return Retourne True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_categories_set_pos( $cat_id, $pos ){
	if( !is_numeric($cat_id) || !$cat_id ){
		return false;
	}

	if( !is_numeric($pos) || $pos<0 ){
	    return false;
	}

	global $config;

	$sql = '
		update prd_categories
		set cat_pos = '.$pos.'
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$cat_id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de la méthode de tri utilisée pour un ensemble de catégories.
 *	Seules les catégories enfant de la catégorie passée en paramètre seront affectées.
 *	@param int $cat Obligatoire : Catégorie dont on souhaite modifier l'ordre d'apparition des enfants. 0 pour la racine du catalogue.
 *	@param $order Obligatoire :  Mode de tri. 0/false pour un tri alphabétique, 1/true pour un tri numérique défini par l'utilisateur
 *	@param $cat_order Optionnel : si le tri est personnalisé, fournir ici le tableau des identifiants des catégories enfants dans l'ordre de tri souhaité
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_categories_order_update( $cat, $order, $cat_order=null ){
	global $config;
	if( !is_numeric($cat) ) return false;
	if( $cat!=0 && !prd_categories_exists($cat) ) return false;

	if( $cat_order !== null ){
		$cat_order = control_array_integer( $cat_order );
		if( $cat_order === false ){
			return false;
		}
		$cat_order = array_unique($cat_order);
	}

	$res = false;
	$ar_cats = array();

	if( $order ){

		// charge les catégories enfants de la catégorie parent
		$childs = prd_categories_get( 0, false, $cat );
		while( $c = ria_mysql_fetch_array($childs) ){
			$ar_cats[] = $c['id'];
		}

		// si $cat_order est spécifié, on s'assure qu'il ne contient que des identifiants présents dans la liste réelle des enfants "$ar_cats"
		if( $cat_order !== null ){
			// NB : comme "array_intersect" préserve les clés, on les reset pour être sûr d'avoir des clés 0, 1, 2...
			$cat_order = array_values(array_intersect($cat_order, $ar_cats));
		}

		// $pos commence à 0 ou à l'index qui suit le dernier élément "$cat_order"
		$pos = 0;
		if( $cat_order !== null ){
			$pos = sizeof($cat_order);
		}

		foreach( $ar_cats as $c_id ){
			$real_pos = $pos;
			if( $cat_order !== null && in_array($c_id, $cat_order) ){
				// index de "$c_id" dans "$cat_order"
				$real_pos = array_search($c_id, $cat_order);
			}else{
				// incrément "$pos" uniquement si pas de match dans "$cat_order"
				$pos++;
			}
			ria_mysql_query('update prd_categories set cat_pos = '.$real_pos.' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$c_id);
		}

		$res = true;

	}else{
		$rcat = false;
		if( $cat==0 ){
			$rcat = ria_mysql_query( 'select cat_id from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id is null' );
			$res  = ria_mysql_query('update prd_categories set cat_pos=null where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id is null');
		}else{
			$rcat = ria_mysql_query( 'select cat_id from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id='.$cat );
			$res  = ria_mysql_query('update prd_categories set cat_pos=null where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id='.$cat);
		}

		if( $rcat && ria_mysql_num_rows($rcat) ){
			while( $c = ria_mysql_fetch_array($rcat) ){
				$ar_cats[] = $c['cat_id'];
			}
		}
	}

	// Mise à jour de la date de modification de la catégorie
	if( sizeof($ar_cats) ){
		prd_categories_set_date_modified( $ar_cats );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier ses sous-catégories.
 *	@param int $cat Identifiant de la catégorie, ou 0 pour la racine du catalogue.
 *	@return bool false si la méthode de tri est alphabétique, true si la méthode de tri est personnalisée
 */
function prd_categories_order_get( $cat ){
	global $config;
	if( !is_numeric($cat) ) return false;
	if( $cat!=0 && !prd_categories_exists($cat) ) return false;

	$sql = '
		select cat_id from prd_categories
		where cat_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null
			and cat_pos is not null
	';
	if( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true )
		$sql .= ' and ( (cat_is_sync and cat_publish) or not cat_is_sync )';
	if( $cat==0 )
		$sql .= ' and cat_parent_id is null';
	else
		$sql .= ' and cat_parent_id='.$cat;

	$res = ria_mysql_query($sql);
	if($res===false) return false;

	return ria_mysql_num_rows($res)>0;

}
// \endcond

// \cond onlyria
/**	Retourne l'ensemble des catégories présentes dans la base de données. Cette fonction est utilisée pour la génération
 *	du fichier RDF des catégories.
 *
 *	Le résultat est fourni sous la forme d'un résultat de requête mysql,
 *	comprenant les colonnes :
 *		- id : identifiant de la catégorie
 *		- name : nom de la catégorie
 *		- description : description de la catégorie
 *		- products : nombre de produits publiés dans la catégorie
 *
 *	Les catégories sont retournées triées par nom.
 *
 *	@param bool $publish Facultatif, si true seules les catégories publiées seront retournées. Si false, toutes les catégories seront retournées.
 *	@param bool $isSync Facultatif, si true seules les catégories synchronisées avec la gestion commerciale seront retournées.
 *	@param $cancel_mask_gescom Facultatif, si true la variable de configuration admin_catalog_hide_source_unpublished n'est pas prise en compte
 *	@param int|array $parent_id Optionnel, identifiant d'une catégorie parent ou tableau d'identifiants (récupère toute l'arborescence à partir de cette id)
 *
 *	@return resource Un résultat de requête MySQL, comprenant les colonnes suivantes : id, name, desc, products
 *
 */
function prd_categories_get_all( $publish=false, $isSync=false, $cancel_mask_gescom=true, $parent_id=0 ){
	$parent_id = control_array_integer( $parent_id, false );
	if ($parent_id === false) {
		return false;
	}

	global $config;

	$sql = '
			select cat_id as id, cat_ref as ref, cat_name as name, if(cat_title!="",cat_title,cat_name) as title,
			cat_desc as "desc", cat_products as products, c.cat_parent_id as parent_id,
			if(ifnull(cat_url_perso, \'\')=\'\', cat_url_alias, cat_url_perso) as url, cat_url_perso as url_perso, cat_url_alias,
			cat_publish as publish
			from prd_categories as c
	';

	if (count($parent_id)) {
		$sql .= ' join prd_cat_hierarchy as h on ( c.cat_tnt_id = h.cat_tnt_id and c.cat_id = h.cat_child_id)';
	}

	$sql .= '
			where c.cat_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null
	';
	if( $publish )
		$sql .= ' and cat_publish';
	if( $isSync )
		$sql .= ' and cat_is_sync=1';
	if( !$cancel_mask_gescom && isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true )
		$sql .= ' and ( (cat_is_sync and cat_publish) or not cat_is_sync )';

	if (count($parent_id)) {
		$sql .= ' and h.cat_parent_id in ('.implode(', ', $parent_id).')';
	}

	$sql .= '
			order by cat_name
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Retourne les catégories principales (niveaux 0 et 1), sous la forme d'une requête à exploiter à l'aide d'un algorithme
 *	de rupture.
 *
 *	La requête résultat contient les colonnes suivantes :
 *
 *		- id : identifiant de la catégorie de niveau 0
 *		- name : nom de la catégorie de niveau 0
 *		- child_id : identifiant de la catégorie de niveau 1
 *		- child_name : nom de la catégorie de niveau 1
 *
 */
function prd_categories_main_get(){
	global $config;

	$sql = '
		select parents.cat_id as id, parents.cat_name as name, parents.cat_products as products, enfants.cat_id as child_id, enfants.cat_name as child_name, enfants.cat_products as child_products
		from prd_categories as parents
			left join prd_categories as enfants on (enfant.cat_tnt_id='.$config['tnt_id'].' and parents.cat_id=enfants.cat_parent_id)
		where parent.cat_tnt_id='.$config['tnt_id'].' and parents.cat_parent_id is null
			and parents.cat_products>0 and enfants.cat_products>0
	';
	if( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true )
		$sql .= ' and ( (enfants.cat_is_sync and enfants.cat_publish) or not enfants.cat_is_sync ) and ( (parents.cat_is_sync and parents.cat_publish) or not parents.cat_is_sync )';
	$sql .= '
		order by parents.cat_name, enfants.cat_name
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Permet la mise à jour d'une catégorie.
 *
 *	Les modifications apportées sur le nom de la catégorie et sur la description sont les mêmes que lors d'un ajout
 *	(suppression des caractères de début et de fin de chaîne, mise en majuscules du premier caractère).
 *
 *	Tous les noms de catégories vides ou contenant des guillemets seront refusés.
 *
 *	Il est possible que la mise à jour échoue si, lors d'un changement de catégorie parente, la catégorie de destination
 *	contient une catégorie portant le même nom.
 *
 *	@todo Mettre à jour l'url alias lorsque la catégorie est renommée ou déplacée
 *	@todo Mettre à jour l'entrée du moteur de recherche correspondant à la catégorie
 *
 *	@param int $id Identifiant de la catégorie à mettre à jour
 *	@param string $name Nouveau nom de la catégorie.
 *	@param string $title Titre de la catégorie
 *	@param string $desc Nouvelle description de la catégorie
 *	@param int $parent Optionnel. Nouvelle catégorie parente, indiquer 0 si aucune
 *	@param bool $publish Optionnel. Booléen indiquant si la catégorie est accessible dans la partie publique ou non.
 *	@param string $date_from Optionnel. Date de début de publication de la catégorie
 *	@param string $date_to Optionnel. Date de fin de publication de la catégorie
 *	@param $is_solde Optionnel, si oui ou non la catégorie ne contient que des articles en soldes
 *	@param $cod_id Optionnel, le contenu de la catégorie est déterminé par les articles présents dans une promotion
 *
 *	@return bool Retourne true en cas de succès, false en cas d'échec
 *
 *
 */
function prd_categories_update( $id, $name, $title, $desc, $parent='', $publish=false, $date_from=false, $date_to=false, $is_solde=false, $cod_id=0 ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !trim($name) ) return false;
	if( !is_numeric($parent) || $parent<0 ) return false;
	if( $id==$parent ) return false;
	if( $publish==='False' ){
		$publish = false;
	}
	elseif( $publish==='True' ){
		$publish = true;
	}

	$new_desc = $desc;

	$name = str_replace('"','\'\'',$name);
	$name = addslashes(ucfirst(trim($name)));
	$title = addslashes(ucfirst(trim($title)));
	$desc = addslashes(ucfirst(trim(sanitize($desc))));
	if( !is_numeric($parent) || $parent<=0 ) $parent = '';

	if( $title==$name ) $title = '';

	if( $id==$parent ) return false;

	if($date_from && preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4} [0-9]{1,2}:[0-9]{2}$/',$date_from) ){
		$date_from = '"'.preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+) ([0-9]{1,2}):([0-9]{2})$/', '\3-\2-\1 \4:\5:00', $date_from ).'"';
	}else{
		$date_from = 'null';
	}
	if( trim($date_to) && preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4} [0-9]{1,2}:[0-9]{2}$/',$date_to) ){
		$date_to = '"'.preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+) ([0-9]{1,2}):([0-9]{2})$/', '\3-\2-\1 \4:\5:00', $date_to ).'"';
	}else{
		$date_to = 'null';
	}

	$sql = '
		select cat_parent_id, cat_publish, cat_desc, cat_first_publish as "first_publish"
		from prd_categories
		where cat_tnt_id = '.$config['tnt_id'].' and cat_id = '.$id.'
	';

	$r = ria_mysql_query($sql);

	if( $old = ria_mysql_fetch_array($r) ){


		$set_first_publish = false;
		if( $publish ){
			if( !$old['cat_publish'] && !$old['first_publish'] ){
				$set_first_publish = true;
			}
		}


		$sql = '
			update prd_categories
			set
				cat_name = "'.$name.'",
				cat_title = "'.$title.'",
				cat_desc = "'.$desc.'",
				cat_date_from = '.$date_from.',
				cat_date_to = '.$date_to.',
				cat_publish = '.( $publish ? 1 : 0 ).',
				cat_is_soldes = '.( $is_solde ? 1 : 0 ).'
		';
		if( $set_first_publish ){
			$sql .= ', cat_first_publish = now() ';
		}
		$sql .= '
			where
				cat_tnt_id = '.$config['tnt_id'].' and cat_id = '.$id.'
		';

		$upd_res = ria_mysql_query($sql);

		if( $upd_res ){
			try{
				// Index la catégorie de produit dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
					'cls_id' => CLS_CATEGORY,
					'obj_id_0' => $id,
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}

			// On met à jour le nombre d'utilisation des images
			img_images_update_from_riawysiwyg( $old['cat_desc'].' '.$new_desc );
		}

		// Reconstruit la hiérarchie
		if( $upd_res && $parent!=$old['cat_parent_id'] ){
			prd_categories_move( $id, $parent );
		}

		// Rafraichit l'info de publication indirecte pour les produits
		if( abs($old['cat_publish'])!=abs($publish) ){
			prd_categories_is_published_cache_reset($id);
			prd_categories_rebuild_prd_publish_cat( $id, true );
		}

		return $upd_res;
	}
	return false;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la description d'une catégorie.
 *	@param int $id Identifiant de la catégorie.
 *	@param string $desc Description de la catégorie.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_categories_set_desc( $id, $desc ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$desc = ucfirst(
		trim(sanitize($desc))
	);

	$sql = '
		select cat_desc
		from prd_categories
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$id;

	$rold_desc = ria_mysql_query($sql);

	if( !$rold_desc || !ria_mysql_num_rows($rold_desc) ){
		return false;
	}

	$old_desc = ria_mysql_result($rold_desc, 0, 0);

	if( $old_desc == $desc ){
		return true;
	}

	$sql = '
		update prd_categories
		set cat_desc = "'.addslashes($desc).'"
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$id;

	$r = ria_mysql_query($sql);

	if( $r ){
		try{
			// Index la catégorie de produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_CATEGORY,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		// Mise à jour du "img_count" des images utilisées dans la description.
		img_images_update_from_riawysiwyg( $old_desc.' '.$desc );
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Met à jour les mots clés utilisés pour le référencement
 *	@param int $cat Identifiant de la catégorie à mettre à jour
 *	@param string $keywords Mots clés complémentaires pour le référencement
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_categories_update_keywords( $cat, $keywords ){
	global $config;
	if( !prd_categories_exists($cat) ) return false;

	$res = ria_mysql_query('update prd_categories set cat_keywords=\''.addslashes($keywords).'\' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cat);

	// Mise à jour de la date de modification de la catégorie
	prd_categories_set_date_modified( $cat );

	return $res;
}
// \endcond

// \cond onlyria
/** Met à jour la partie pour le référencement d'une catégories
 *	@param int $cat Obligatoire, Identifiant de la catégorie
 *	@param string $tag_title Optionnel, Titre utilisé pour le référencement de la catégorie
 *	@param string $tag_desc Optionnel, Description utilisé pour le référencement de la catégorie
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_categories_update_referencing( $cat, $tag_title='', $tag_desc='' ){
	global $config;
	if( !prd_categories_exists($cat) ) return false;

	$res = ria_mysql_query('update prd_categories set cat_tag_title="'.addslashes($tag_title).'", cat_tag_desc="'.addslashes($tag_desc).'" where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cat);

	// Mise à jour de la date de modification de la catégorie
	prd_categories_set_date_modified( $cat );

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise TITLE d'une catégorie.
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@param string $tag_title Optionnel, titre utilisé pour le référencement de la catégorie
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_categories_update_referencing_tag_title( $cat, $tag_title='' ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update prd_categories
		set cat_tag_title='.( trim($tag_title) ? '\''.addslashes($tag_title).'\'' : 'null' ).'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat.'
	';

	$res = ria_mysql_query( $sql );
	if( $res ){
		// Mise à jour de la date de modification de la catégorie
		prd_categories_set_date_modified( $cat );
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise META DESCRIPTION d'une catégorie.
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@param string $tag_desc Optionnel, description utilisée pour le référencement de la catégorie
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_categories_update_referencing_tag_desc( $cat, $tag_desc='' ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update prd_categories
		set cat_tag_desc='.( trim($tag_desc) ? '\''.addslashes($tag_desc).'\'' : 'null' ).'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$cat.'
	';

	$res = ria_mysql_query( $sql );
	if( $res ){
		// Mise à jour de la date de modification de la catégorie
		prd_categories_set_date_modified( $cat );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction est une version simplifiée de la fonction prd_categories_update pour la synchronisation avec SAGE.
 *
 *	@param int $id Identifiant de la catégorie à mettre à jour
 *	@param string $name Nouveau nom de la catégorie.
 *	@param bool $publish Optionnel, booléen indiquant si la catégorie est accessible dans la partie publique ou non.
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 *	@todo L'url virtuelle de la catégorie devrait être reconstruite si le nom de la catégorie à changé
 *
 */
function prd_categories_update_sage( $id, $name, $publish ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !trim($name) ) return false;

	$name = str_replace('"','\'\'',$name);
	$name = addslashes(trim(ucfirst($name)));

	// détermine si l'information "cat_first_publish" doit être mise à jour
	$set_date_publish = false;
	if( $publish ){
		$sql = '
			select cat_publish as "publish", cat_first_publish as "first_publish"
			from prd_categories
			where cat_tnt_id = '.$config['tnt_id'].' and cat_id = '.$id.'
		';

		$old_publish = $first_publish = false;
		if( $rold = ria_mysql_query($sql) ){
			if( $old_row = ria_mysql_fetch_assoc($rold) ){
				$old_publish = $old_row['publish'];
				$first_publish = $old_row['first_publish'];
			}
		}

		if( !$old_publish && !$first_publish ){
			$set_date_publish = true;
		}
	}

	$sql = '
		update prd_categories
		set
			cat_name = "'.$name.'",
			cat_publish = '.( $publish ? 1:0 ).'
	';
	if( $set_date_publish ){
		$sql .= ', cat_first_publish = now() ';
	}
	$sql .= '
		where
			cat_tnt_id = '.$config['tnt_id'].' and cat_id = '.$id.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		prd_categories_rebuild_prd_publish_cat( $id, true );
	}
	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la date de modification (date d'excution de cette fonction) d'une catégorie.
 *	@param int|array $cat Obligatoire, identifiant ou tableau d'identifiants des catégories dont la date de dernière modification doit être mise à jour
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_categories_set_date_modified( $cat ){
	if( !is_array($cat) ){
		if( !is_numeric($cat) || $cat<=0 ){
			return false;
		}

		$cat = array( $cat );
	}else{
		if( !sizeof($cat) ){
			return false;
		}

		foreach( $cat as $c ){
			if( !is_numeric($c) || $c<=0 ){
				return false;
			}
		}
	}

	global $config;

	$sql = '
		update prd_categories
		set cat_date_modified=now()
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id in ('.implode( ', ', $cat ).')
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Déplace la catégorie avant ou après une autre catégorie
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *	L'utilisateur doit s'assurer que les 2 catégories appartiennent au même parent (sinon ça n'a pas de sens)
 *
 *	@param $source Identifiant de la catégorie source
 *	@param $target Identifiant de la catégorie cible
 *	@param $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
 */
function prd_categories_position_update( $source, $target, $where ) {
	$res = obj_position_update( DD_CATEGORY, $source, $target, $where );

	// Mise à jour de la date de modification de la catégorie source et cible
	prd_categories_set_date_modified( array($source, $target) );

	return $res;
}
// \endcond

// \cond onlyria
/**	Déplace une catégorie dans une autre catégorie.
 *	Pour déplacer une catégorie à la racine, il suffit d'indiquer 0 pour le paramètre $parent.
 *
 *	@param int $id Identifiant de la catégorie à déplacer
 *	@param int $parent Identifiant de la nouvelle catégorie parente
 *	@param $keep_url Booléen, 1 si les urls ne doivent pas étre modifié
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 *	\bug Lorsque la catégorie est déplacée, les urls virtuelles des produits qu'elle contient ne sont pas mis à jour.
 *
 */
function prd_categories_move( $id, $parent=NULL, $keep_url = 0 ){
	global $config;
	if( $id==$parent ) return false;
	if( $parent=='' || $parent=='0' || $parent==0 ) $parent = NULL;

	// Charge la catégorie
	$rcat = prd_categories_get($id);
	if( !ria_mysql_num_rows($rcat) ) return false;
	$cat = ria_mysql_fetch_array($rcat);

	$old_parent = $cat['parent_id'];

	// Modifie l'ordre de tri dans sa catégorie actuelle
	if( is_numeric($cat['pos']) ){
		ria_mysql_query('update prd_categories set cat_pos=cat_pos-1 where cat_tnt_id='.$config['tnt_id'].' and cat_pos>'.$cat['pos'].' and cat_parent_id'.( $cat['parent_id'] ? '='.$cat['parent_id'] : ' is null' ).' and cat_id!='.$cat['id']);
	}
	// Place la catégorie en fin de liste dans la nouvelle catégorie
	$pos = ria_mysql_result(ria_mysql_query('select max(cat_pos)+1 from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id'.( is_numeric($parent) && $parent>0 ? '='.$parent : ' is null' )),0,0);
	if( $pos=='' ) $pos = 'null';
	ria_mysql_query('update prd_categories set cat_pos='.$pos.' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);

	// Déplace la catégorie
	$res = ria_mysql_query('update prd_categories set cat_parent_id='.( is_numeric($parent) && $parent>0 ? $parent : 'null' ).' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);

	if( is_numeric($old_parent) && $old_parent>0 ){
		prd_categories_hierarchy_rebuild($old_parent, true);
	}

	$ar_cats = array($old_parent, $id);
	if( is_numeric($parent) && $parent>0 ){
		$ar_cats[] = $parent;
		prd_categories_hierarchy_rebuild($parent, true);
	}

	prd_categories_hierarchy_rebuild( $id, true );

	// Mise à jour de la date de modification des catégories où le produit est classé
	prd_categories_set_date_modified( $ar_cats );

	if(!$keep_url){
		// Suppression des urls traduites
		rew_rewritemap_del_multilingue( _FLD_CAT_URL, array($id) );

		// Supprime les urls virtuelles de la catégorie
		rew_rewritemap_del( $cat['url_alias'] );
		rew_rewritemap_del( substr($cat['url_alias'],0,-1) );

		// Redéfinie l'url virtuelle de la catégorie
		prd_categories_url_alias_add( $id );
	}

	try{
		// Index la catégorie de produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_CATEGORY,
			'obj_id_0' => $id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	// Met à jour (en cascade) les nombres de produits publiés de l'ancien et du nouveau parent
	if( is_numeric($cat['parent_id']) )
		prd_categories_refresh_products_published( $cat['parent_id'] );
	if( is_numeric($parent) )
		prd_categories_refresh_products_published( $parent );

	prd_categories_rebuild_prd_publish_cat( $id, true );

	return $res;
}
// \endcond

// \cond onlyria
/**	Provoque la supression d'une catégorie. Deux modes de suppression sont gérés par cette fonction.
 *	Il est possible de ne supprimer que la catégorie ($products=false) ou la catégorie et ses produits ($products=true).
 *
 *	Aucune suppression physique n'a réellement lieu, que ce soit pour les catégories ou pour les produits touchés par cette opération.
 *	La suppression d'une catégorie entraîne la suppression de ses sous-catégories.
 *
 *	En cas d'erreur lors de la suppression d'une sous catégorie, la fonction s'arrête.
 *
 *	@param int $id Identifiant de la catégorie à supprimer
 *	@param $products facultatif. false si seul la catégorie est supprimée, true si les produits doivent également être supprimés.
 *	@param bool $is_sync Par défaut à false, mettre true pour forcer la suppression de contenus synchronisés
 *	@param $check_sync Optionnel, par défaut à true, permet de vérifier qu'aucune sous catégorie n'est pas synchronisée
 *
 *	@return bool false en cas d'erreur, true en cas de succès.
 *
 *	@todo Support du paramètre $products : Suppression des produits de la catégorie (nécessite la table prd_classify)
 *	@todo Rendre la suppression des sous catégories optionnelle ? Provoquer une erreur si la catégorie n'est pas vide ?
 *
 *
 */
function prd_categories_del( $id, $products=false, $is_sync=false, $check_sync=true ){
	global $config;
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	// Si la suppression n'est pas initiée par la synchronisation
	// Un contrôle est fait pour savoir si l'une des catégories enfants n'est pas synchronisée
	if( $check_sync && $is_sync === false ){
		$r_child_is_sync = ria_mysql_query('
			select 1
			from prd_cat_hierarchy as h
				join prd_categories as c on (c.cat_tnt_id = '.$config['tnt_id'].' and c.cat_id = h.cat_child_id)
			where h.cat_tnt_id = '.$config['tnt_id'].'
				and h.cat_parent_id = '.$id.'
					and c.cat_date_deleted is null
					and ifnull(c.cat_is_sync, 0) = 1
		');

		if( $r_child_is_sync && ria_mysql_num_rows($r_child_is_sync) ){
			return false;
		}
	}

	// Charge la catégorie
	$rcat = prd_categories_get($id);
	if( !$rcat || !ria_mysql_num_rows($rcat) ){
		return true;
	}

	$cat = ria_mysql_fetch_array($rcat);

	// Actualise les tris personnalisés
	$order = prd_categories_order_get($cat['parent_id']);
	if( $order ){
		if( $cat['parent_id'] ){
			ria_mysql_query('update prd_categories set cat_pos=cat_pos-1 where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id='.$cat['parent_id'].' and cat_pos>='.$cat['pos']);
		}else{
			ria_mysql_query('update prd_categories set cat_pos=cat_pos-1 where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id is null and cat_pos>='.$cat['pos']);
		}
	}

	// Supprime les sous catégories
	$childs = prd_categories_get(0,false,$id);
	while( $r = ria_mysql_fetch_array($childs) ){
		if( !prd_categories_del($r['id'], $products, $is_sync, false) ){
			return false;
		}
	}

	//supprime l'url canonique si elle existe
	prd_categories_del_canonical($id);

	// Supprime la catégorie de la table de hiérarchie
	prd_categories_hierarchy_del( $id );

	// Supprime l'url virtuelle vers la catégorie
	$alias = ria_mysql_result(ria_mysql_query('select cat_url_alias from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id),0,0);
	ria_mysql_query('delete from rew_rewritemap where url_tnt_id='.$config['tnt_id'].' and url_intern="'.$alias.'" and url_code=301');

	// Suppression des urls traduites
	rew_rewritemap_del_multilingue( _FLD_CAT_URL, array($id) );

	// Supprime les urls virtuelles de la catégorie
	rew_rewritemap_del( $cat['url_alias'] );
	rew_rewritemap_del( substr($cat['url_alias'],0,-1) );

	// Détache les produits classés dans cette catégorie (entraîne la suppression des entrées
	// correspondantes dans le moteur de recherche, ainsi que la suppression des urls virtuelles)
	$products = ria_mysql_query('select cly_prd_id as id from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$id);
	while( $p = ria_mysql_fetch_array($products) )
		prd_products_del_from_cat( $p['id'], $id, $is_sync );

	// Retire la catégorie du moteur de recherche
	if( $cat['cnt_id'] )
		search_index_clean('prd-cat',$cat['cnt_id']);

	// Supprime la catégorie
	$r = ria_mysql_query('update prd_categories set cat_date_deleted=now() where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$id);

	if( $r ){
		// supprime les tarifs liés
		prc_prices_del( 0, 0, 0, $id );
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Cette fonction enregistre une relation parent/enfant dans la table de hiérarchie.
 *
 *	@param int $parent Identifiant de la catégorie parent
 *	@param int $child Identifiant de la catégorie enfant
 *
 */
function prd_categories_hierarchy_add( $parent, $child ){
	global $config;

	if( !is_numeric($parent) || $parent<=0 ) return false;
	if( !is_numeric($child) || $child<=0 ) return false;

	// Ajoute la catégorie en tant qu'enfant direct de sa catégorie parente
	$depth = prd_categories_depth_get($parent);
	ria_mysql_query('insert into prd_cat_hierarchy (cat_tnt_id,cat_parent_id,cat_child_id,cat_parent_depth) values ('.$config['tnt_id'].','.$parent.','.$child.','.$depth.');');

	// Maintient à jour la hiérarchie indirecte
	$gparents = prd_categories_parents_get($parent);
	while( $r = ria_mysql_fetch_array($gparents) )
		ria_mysql_query('insert into prd_cat_hierarchy (cat_tnt_id,cat_parent_id,cat_child_id,cat_parent_depth) values ('.$config['tnt_id'].','.$r['id'].','.$child.','.$r['depth'].');');

	return true;
}
// \endcond

// \cond onlyria
/** Permet la suppression d'une relation parent/enfant dans la table de hiérarchie.
 *
 *	@param int $child Identifiant de la catégorie enfant de la relation
 *
 */
function prd_categories_hierarchy_del( $child ){
	global $config;

	if( !is_numeric($child) || $child<=0 ) return false;
	return ria_mysql_query('delete from prd_cat_hierarchy where cat_tnt_id='.$config['tnt_id'].' and cat_child_id='.$child);
}
// \endcond

// \cond onlyria
/**	Reconstruit la hiérarchie complète des catégories de produits. La reconstruction se fait de manière hiérarchique, en partant
 *	des catégories de premier niveau pour descendre vers les catégories les plus profondes.
 *
 *	@param int $parent Obligatoire, point de départ de la reconstruction. Pour une reconstruction complète, laisser vide.
 *	@param $forced Facultatif, booléen indiquant si la recontruction doit être forcée ou non. Indiquer vrai si l'argument $parent est supérieur à 0.
 *
 */
function prd_categories_hierarchy_rebuild( $parent=0, $forced=false ){
	if( !is_numeric($parent) || $parent<0 ) return false;
	global $config;

	// Le vidage de la table de hiérarchie ne se fait qu'au premier appel
	if( $parent==0 ){
		ria_mysql_query('delete from prd_cat_hierarchy where cat_tnt_id='.$config['tnt_id']);
	} elseif( $forced ){
		ria_mysql_query('delete from prd_cat_hierarchy where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id='.$parent);
	}

	// Reconstruit la hiérarchie pour toutes les catégories enfants
	$categories = prd_categories_get( 0, false, $parent );
	while( $c = ria_mysql_fetch_array($categories) ){
		prd_categories_hierarchy_add( $c['parent_id'], $c['id'] );
		prd_categories_hierarchy_rebuild( $c['id'] );
	}

}
// \endcond

/** Cette fonction permet de vérifier qu'une hiérarchie entre catégorie existe
 *	@param int $parent_id Obligatoire, identifiant d'une catégorie parent
 *	@param $child_id Obligatoire, identifiant ou tableau d'identifiants d'une catégorie enfant
 *	@return cat_parent_depth si la hiérarchie existe (donc potentiellement zéro), False dans le cas présent
 */
function prd_categories_hierarchy_exists( $parent_id, $child_id ){
	if (!is_numeric($parent_id) || $parent_id <= 0) {
		return false;
	}

	$child_id = control_array_integer($child_id);
	if ($child_id===false) {
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select cat_parent_depth
		from prd_cat_hierarchy
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_parent_id = '.$parent_id.'
			and cat_child_id in ('.implode(', ', $child_id).')
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['cat_parent_depth'];
}

// \cond onlyria
/**	Cette fonction teste si la catégorie passée en paramètre possède une ou plusieurs catégories enfants
 *	et retourne le résultat.
 *	@param int $cat Identifiant de la catégorie à tester
 *	@return bool true si la catégorie possède une ou plusieurs catégories enfants
 *	@return bool false si la catégorie ne possède aucune catégorie enfant
 *	@return bool false si une erreur est survenue
 */
function prd_categories_have_childs( $cat ){
	global $config;

	if( !is_numeric($cat) || $cat<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('
		select cat_child_id from prd_cat_hierarchy
		where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id='.$cat.'
	'))>0;
}
// \endcond

// \cond onlyria
/**	Retourne le nombre de produits contenus dans une catégorie (y compris les produits des sous-catégories)
 *
 *	@param int $cat Identifiant de la catégorie dont on souhaite décompter le nombre de produits
 *	@return Le nombre de produits contenus dans la catégorie (sous-catégories comprises)
 *
 *	\bug Dans les sous-catégories, seul les produits publiés sont comptés, alors que tous devraient l'être
 *
 */
function prd_categories_products_count_get( $cat ){
	global $config;
	if( !is_numeric($cat) || $cat<=0 ) return false;
	$childs_products = ria_mysql_result(ria_mysql_query('select sum(cat_products) from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id='.$cat),0,0);
	$products = ria_mysql_result(ria_mysql_query('select count(*) from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$cat),0,0);
	return $childs_products + $products;
}
// \endcond

// \cond onlyria
/**	Retourne le nombre de produits publiés contenus dans une catégorie (y compris les produits des sous-catégories)
 *
 *	@param int $cat Identifiant de la catégorie dont on souhaite décompter le nombre de produits
 *	@return Le nombre de produits publiés contenus dans la catégorie (sous-catégories comprises)
 *
 */
function prd_categories_products_published_count_get( $cat ){
	global $config;

	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( $config['tnt_id']==2 ){
		$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
	}else{
		$dps = prd_deposits_get_main();
	}

	if( !$dps ){
		$dps = 0;
	}

	$products = ria_mysql_result(
		ria_mysql_query('
			select count(*)
			from prd_classify, prd_products
			where cly_tnt_id='.$config['tnt_id'].'
				and prd_tnt_id=cly_tnt_id
				and cly_prd_id=prd_id
				and prd_publish
				and cly_cat_id='.$cat.'
				and (prd_sleep=0 or ((select sum(' . prd_stocks_get_sql() . ') from prd_stocks where sto_tnt_id='.$config['tnt_id'].' and sto_prd_id=prd_id and sto_is_deleted=0 and sto_dps_id='.$dps.')>0))
				and prd_childonly=0
				and prd_date_deleted is null'
		),0,0
	);

	$childs_products = ria_mysql_result(ria_mysql_query('select sum(cat_products) from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_parent_id='.$cat.' and cat_publish = 1 and cat_date_deleted is null'),0,0);

	return $products + $childs_products;

}
// \endcond

// \cond onlyria
/** Recalcule le nombre de produits publiés contenus dans la catégorie.
 *	@param int $cat Identifiant de la catégorie à rafraichir
 */
function prd_categories_refresh_products_published( $cat ){
	global $config;
	if( !is_numeric($cat) || $cat<=0 ) return false;

	$r_cat = ria_mysql_query('
		select cat_is_soldes as is_soldes, cat_products as count_prd
		from prd_categories
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$cat.'
			and cat_date_deleted is null
	');

	if( !$r_cat || !ria_mysql_num_rows($r_cat) ){
		return false;
	}

	$d_cat = ria_mysql_fetch_assoc( $r_cat );
	$count = 0;

	$r_cod_cat = prd_categories_codes_get( $cat );
	if ($r_cod_cat && ria_mysql_num_rows($r_cod_cat)) {
		while ($cod_cat=ria_mysql_fetch_assoc($r_cod_cat)) {
			$r_cod_prd = pmt_codes_products_get( $cod_cat['cod_id'], false, 0, false, true );
			if( is_array($r_cod_prd) ){
				foreach( $r_cod_prd as $cod_prd ){
					if (prd_products_get_publish($cod_prd['prd_id'])) $count++;
				}
			}
		}
	} else {
		$count = prd_categories_products_published_count_get($cat);
	}

	$cat_is_published = prd_categories_is_published($cat);
	if (
		($d_cat['count_prd'] == 0 && $count > 0 && !$cat_is_published) ||
		($d_cat['count_prd'] > 0 && $count == 0 && $cat_is_published)
	) {
		prd_categories_is_published_cache_reset($cat);
	}

	ria_mysql_query('update prd_categories set cat_products='.$count.' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cat);

	$parents = prd_categories_parents_get($cat);
	while( $r = ria_mysql_fetch_array($parents) ){
		if( $r['id'] == $cat ){
			break;
		}

		prd_categories_refresh_products_published( $r['id'] );
	}

}
// \endcond

// \cond onlyria
/**	Cette fonction vérifie si prd_categories_refresh_products_published() a besoin d'être appelée sur une catégorie (= procède à une simulation).
 *	@param int $cat_id Identifiant de la catégorie.
 *	@return bool True si MAJ nécessaire, False sinon.
 */
function prd_categories_need_refresh_products_published( $cat_id ){
	if( !is_numeric($cat_id) || $cat_id <= 0 ){
		return false;
	}

	global $config;

	$old_count = 0;

	// récupère le décompte précédent
	$sql = '
		select cat_products from prd_categories
		where cat_tnt_id = '.$config['tnt_id'].' and cat_id = '.$cat_id.'
	';

	if( $rold_count = ria_mysql_query($sql) ){
		if( ria_mysql_num_rows($rold_count) ){
			$old_count = ria_mysql_result($rold_count, 0, 0);
		}
	}

	$count = prd_categories_products_published_count_get( $cat_id );

	return $count != $old_count;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la publication d'une catégorie de produits sur le site marchand.
 *	Pour les catégories synchronisées avec la gestion commerciale, la publication ne peut avoir lieu
 *	via la boutique.
 *
 *	@param int|array $cat Identifiant de la catégorie à publier, ou tableau des identifiants de catégories à publier.
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
function prd_categories_publish( $cat ){
	global $config;
	if( is_numeric($cat) ) $cat = array($cat);
	if( !is_array($cat) ) return false;

	foreach( $cat as $c ){
		if( !prd_categories_exists($c) ) return false;
		ria_mysql_query('update prd_categories set cat_publish=1, cat_first_publish = if(cat_first_publish is null, now(), cat_first_publish) where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$c);
		prd_categories_is_published_cache_reset($c);
		prd_categories_refresh_products_published($c);
		prd_categories_index_publish_refresh($c);

		prd_categories_rebuild_prd_publish_cat( $c, true );
	}
	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la dé-publication d'une catégorie de produits sur le site marchand.
 *
 *	@param int|array $cat Identifiant de la catégorie à dépublier, ou tableau des identifiants de catégories à dépublier.
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
function prd_categories_unpublish( $cat ){
	global $config;
	if( is_numeric($cat) ) $cat = array($cat);
	if( !is_array($cat) ) return false;

	foreach( $cat as $c ){
		if( !prd_categories_exists($c) ) return false;
		ria_mysql_query('update prd_categories set cat_publish=0 where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$c);
		prd_categories_is_published_cache_reset($c);
		prd_categories_refresh_products_published($c);
		prd_categories_index_publish_refresh($c);

		prd_categories_rebuild_prd_publish_cat( $c, true );

		//supprime l'url canonique si elle existe
		prd_categories_del_canonical($c);
	}
	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction va tester la hiérarchie des catégories de produits pour déterminer si la catégorie passée
 *	en argument doit apparaître dans la boutique ou non. En effet, le seul paramètre Publish n'est pas suffisant
 *	pour que la catégorie apparaisse, il faut que les catégories parentes soient elles aussi publiées.
 *
 *	Cette fonction est justement chargée de s'assurer que les catégories parentes sont elles aussi publiées pour
 *	déterminer si la catégorie doit être accessible dans la boutique.
 *
 *	Pour être plus rapide lors de la reconstruction de l'index, cette fonction utilise un cache (expire avec la fin
 *	de la requête).
 *	Il faut donc prêter attention à son utilisation, car le cas suivant ne fonctionnera pas comme attendu :
 *
 *	\code
 *	print prd_categories_is_publish($id); // Retourne 0
 *	prd_categories_publish($id); // Publie la catégorie
 *	prd_categories_is_publish($id); // Retourne 0, en raison du cache
 *	\endcode
 *
 *	@param int $id Identifiant de la catégorie à tester
 *	@return bool true si la catégorie doit être publiée
 *	@return bool false en cas d'erreur ou si la catégorie ne doit pas être publiée
 *
 */
function prd_categories_is_published( $id ){
	global $config;
	global $prd_categories_is_published_cache;

	// Interrogation du cache
	if( !isset($prd_categories_is_published_cache) ){
		$prd_categories_is_published_cache = array();
	}else{
		if( isset($prd_categories_is_published_cache[$id]) ){
			return $prd_categories_is_published_cache[$id];
		}
	}

	$soldes = pmt_soldes_get_next_period();

	// Recherche dans la base de données
	if( !is_numeric($id) || $id<=0 ) return false;
	$c = ria_mysql_query('
		select cat_publish, cat_parent_id, cat_products
		from prd_categories
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_id='.$id.'

			and cat_date_from<=now() and (cat_date_to is null or cat_date_to>now())
			and (cat_is_soldes = 0 or (now()>="'.$soldes['start']['date'].'" and now()<="'.$soldes['stop']['date'].'"))
	');

	$r = ria_mysql_fetch_array($c);
	$p = $r['cat_publish']!='0' && $r['cat_products']>0 ? true : false;

	if( $p && $r['cat_parent_id'] ){
		$p = ria_mysql_result(ria_mysql_query('
			select count(*)
			from prd_cat_hierarchy as hry, prd_categories
			where hry.cat_tnt_id='.$config['tnt_id'].'
				and hry.cat_tnt_id=prd_categories.cat_tnt_id
				and hry.cat_parent_id=cat_id
				and cat_child_id='.$id.'
				and (
					cat_publish=0
					or ( cat_date_from > now() or (cat_date_to is not null and cat_date_to < now()) )
					or ( cat_is_soldes = 1 and (now() < "'.$soldes['start']['date'].'" or now() > "'.$soldes['stop']['date'].'") )
				)
		'),0,0)=='0';
	}

	// Ajout du résultat au cache
	$prd_categories_is_published_cache[$id] = $p;

	return $p;
}

/** Cette fonction permet de vérifier qu'une catégorie est à la fois publiée et active.
 * 	@param int $cat_id Obligatoire, identifiant d'une catégorie
 * 	@return bool True si elle est publiée et active, False dans le cas contraire
 */
function prd_categories_is_actived($cat_id) {
	if (!is_numeric($cat_id) || $cat_id <= 0) {
		return false;
	}

	global $config;

	$soldes = pmt_soldes_get_next_period();

	$res = ria_mysql_query('
		select 1
		from prd_categories
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$cat_id. '
			and cat_publish and cat_products > 0
			and cat_date_from<=now() and (cat_date_to is null or cat_date_to>now())
			and (cat_is_soldes = 0 or (now()>="'.$soldes['start']['date'].'" and now()<="'.$soldes['stop']['date'].'"))
	');

	return $res && ria_mysql_num_rows($res);
}

/** Cette fonction permet la réinitialisation du cache des catégories publiées pour une catégorie définie passée en argument
 *
 * @param integer $cat_id Obligatoire, identifiant de la catégorie dont on souhaite réinitialiser le cache
 * @return void
 */
function prd_categories_is_published_cache_reset($cat_id){
	global $config, $prd_categories_is_published_cache;

	if (isset($prd_categories_is_published_cache)) {
		if( array_key_exists($cat_id, $prd_categories_is_published_cache)){
			unset($prd_categories_is_published_cache[$cat_id]);
		}

		$r_child = ria_mysql_query('
			select cat_child_id as id
			from prd_cat_hierarchy
			where cat_tnt_id = '.$config['tnt_id'].'
				and cat_parent_id = '.$cat_id.'
		');

		if ($r_child) {
			while ($child = ria_mysql_fetch_assoc($r_child)) {
				if (array_key_exists($child['id'], $prd_categories_is_published_cache)) {
					unset($prd_categories_is_published_cache[$child['id']]);
				}
			}
		}
	}
}

/** Cette fonction permet de savoir si une catégories est indexée donc si elle sort dans le moteur de recherche et le sitemap
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie
 *	@param $hierarchy Optionnel, par défaut, on regardera si sa hiérarchie est indexer, mettre False pour l'ignorer
 *	@return bool True si elle est indexée, False dans le cas contraire
 */
function prd_categories_is_index( $cat_id, $hierarchy=true ){
	if (!is_numeric($cat_id) || $cat_id <= 0) {
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select 1
		from prd_categories
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$cat_id.'
			and cat_no_index = 1
	');

	if ($res && ria_mysql_num_rows($res)) {
		return false;
	}

	if ($hierarchy) {
		$res = ria_mysql_query('
			select 1
			from prd_categories as c
				join prd_cat_hierarchy as h on (c.cat_tnt_id = h.cat_tnt_id and c.cat_id = h.cat_parent_id)
			where c.cat_tnt_id = '.$config['tnt_id'].'
				and h.cat_child_id = '.$cat_id.'
				and c.cat_no_index = 1
		');

		if ($res && ria_mysql_num_rows($res)) {
			return false;
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction va reconstruire l'index du moteur de recherche pour toutes les catégories.
 *	Les produits contenus dans ces catégories ne sont pas concernés par l'opération.
 *	@param string $lng Optionnel, si le paramètre est précisé, alors les catégories seront réindéxées seulement dans cette langue
 *
 *	Etapes :
 *		- vide l'index précédent (pose un problème en production car il n'y a plus du coup de résultat le temps de la reconstruction)
 *		- crée une nouvelle entrée d'index pour chaque catégorie
 *
 *	Nouvel algorithme à mettre en place :
 *		- ajoute chaque catégorie qui n'existe pas encore dans l'index
 *		- met à jour l'index de chaque catégorie déjà dans l'index
 *		- supprime de l'index les catégories qui n'existeraient plus (n'est pas censé se produire)
 *
 */
function prd_categories_index_rebuild( $lng=false ){
	global $config;

	// Reconstruit l'index
	$categories = ria_mysql_query('
		select cat_id as id, cat_cnt_id as cnt_id
		from prd_categories
		where cat_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null'
	);

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? array($lng) : $config['i18n_lng_used'];

	foreach( $lng as $l ){
		ria_mysql_data_seek( $categories, 0 );
		while( $c = ria_mysql_fetch_array($categories) ){
			prd_categories_index_add( $c['id'], strtolower($l) );
		}
	}

	return true;

}
// \endcond

// \cond onlyria
/**	Cette fonction va reconstruire totalement les urls de catégories.
 *	Les produits contenus dans ces catégories ne sont pas concernés par cette opération.
 *	@param int $parent Facultatif, catégorie de départ de la reconstruction
 */
function prd_categories_rebuild_urls( $parent=0 ){
	global $config;

	$categories = prd_categories_get( 0, false, $parent );
	$size = ria_mysql_num_rows($categories);

	while( $cat = ria_mysql_fetch_array($categories) ){

		// Supprime l'url principale
		rew_rewritemap_del( $cat['url_alias'] );

		// Supprime la redirection vers /
		rew_rewritemap_del( substr($cat['url_alias'],0,-1) );

		// Ajoute l'url virtuelle pour cette catégorie
		$url_df = prd_categories_url_alias_add( $cat['id'] );
		if( trim($url_df)!='' ){
			foreach( $config['i18n_lng_used'] as $lng ){
				if( $lng!=$config['i18n_lng'] )
					rew_rewritemap_add_multilingue( array($cat['id']), CLS_CATEGORY, $lng, $url_df, _FLD_CAT_URL );
			}
		}

		try{
			// Index la catégorie de produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_CATEGORY,
				'obj_id_0' => $cat['id'],
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		// Applique le même traitement aux familles enfant
		prd_categories_rebuild_urls( $cat['id'] );
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne les familles contenant des produits pour lesquels un champ donné est renseigné
 *	@param $fld Obligatoire, identifiant du champ sur lequel filtrer le résultat
 *	@param int $parent Optionnel, identifiant d'une catégorie parent
 *	@param string $lng Optionnel, permet de filtrer les valeurs du champ renseignés uniquement dans une seule langue
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie
 *			- name : désignation de la catégorie
 *			- title : désignation de la catégorie pour le web
 *			- url_alias : url simplifiée de la catégorie
 *			- desc : description de la catégorie
 *			- keywords : mots clés spécifiques à la catégorie
 *			- parent_id : identifiant de la catégorie parente (null si aucune)
 *			- products : nombre de produits publiés dans la catégorie
 *			- publish : indique si la catégorie est publiée ou non
 *			- cnt_id : identifiant de la catégorie dans le moteur de recherche
 *			- pos : position d'affichage de la catégorie (lorsqu'elle est listée avec d'autres catégories de même niveau)
 *			- is_sync : indique si la catégorie est synchronisée avec la gestion commerciale, ou non
 */
function prd_categories_get_by_field( $fld, $parent=0, $lng=false ){
	if( !is_numeric($fld) || $fld <= 0 ){
		return false;
	}

	if( fld_fields_get_class( $fld ) != CLS_PRODUCT ){
		return false;
	}

	global $config;

	if( $lng !== false && !in_array(strtolower(trim($lng)), $config['i18n_lng_used']) ){
		return false;
	}

	$sql = '
		select cat_id as id, cat_name as name, if(ifnull(cat_title,"")="",cat_name,cat_title) as title,
			if(ifnull(cat_url_perso, \'\')=\'\', cat_url_alias, cat_url_perso) as url_alias, cat_desc as "desc", cat_keywords as keywords,
			cat_parent_id as parent_id, cat_products as products, cat_publish as publish,
			cat_cnt_id as cnt_id, cat_pos as pos, cat_is_sync as is_sync
		from prd_categories as cat
		where cat.cat_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null and cat_publish
	';
	if( is_numeric( $parent ) && $parent>0 ){
		$sql .= ' and cat_parent_id = '.$parent;
	}else{
		$sql .= ' and cat_parent_id is null';
	}
	$sql .= '
		and (exists (
			select 1 from prd_cat_hierarchy as hry
			join prd_categories as cat2 on hry.cat_tnt_id = cat2.cat_tnt_id and hry.cat_child_id=cat2.cat_id
			join prd_classify on cat2.cat_tnt_id = cly_tnt_id and cat2.cat_id = cly_cat_id
			join prd_products on cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id
			join fld_object_values on prd_tnt_id = pv_tnt_id and prd_id = pv_obj_id_0
			where cat2.cat_date_deleted is null and cat2.cat_publish
			and prd_date_deleted is null and prd_publish and prd_publish_cat
			and pv_fld_id = '.$fld.' '.( $lng !== false ? ' and pv_lng_code = "'.strtolower(trim($lng)).'"' : '' ).'
			and hry.cat_tnt_id = '.$config['tnt_id'].' and hry.cat_parent_id = cat.cat_id

			) or exists (

			select 1 from prd_classify
			join prd_products on cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id
			join fld_object_values on prd_tnt_id = pv_tnt_id and prd_id = pv_obj_id_0
			where prd_date_deleted is null and prd_publish and prd_publish_cat
			and pv_fld_id = '.$fld.' '.( $lng !== false ? ' and pv_lng_code = "'.strtolower(trim($lng)).'"' : '' ).'
			and cly_tnt_id = '.$config['tnt_id'].' and cly_cat_id = cat.cat_id
		))
		order by cat_pos, if(ifnull(cat_title,"")="",cat_name,cat_title)
	';

	$r = ria_mysql_query($sql);

	if( !$r ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
		}
		return false;
	}

	return $r;
}
// \endcond

/**	Cette fonction retourne les familles de produits qui contiennent des nouveautés publiées
 *	@param int $parent Facultatif, identifiant de la catégorie parente
 *	@param bool $with_img Optionnel, par défaut les produits doivent avoir une image, mettre false pour ignorer cette restriction
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie
 *			- name : désignation de la catégorie
 *			- title : désignation de la catégorie pour le web
 *			- url_alias : url simplifiée de la catégorie
 *			- desc : description de la catégorie
 *			- keywords : mots clés spécifiques à la catégorie
 *			- parent_id : identifiant de la catégorie parente (null si aucune)
 *			- products : nombre de produits publiés dans la catégorie
 *			- publish : indique si la catégorie est publiée ou non
 *			- cnt_id : identifiant de la catégorie dans le moteur de recherche
 *			- pos : position d'affichage de la catégorie (lorsqu'elle est listée avec d'autres catégories de même niveau)
 *			- is_sync : indique si la catégorie est synchronisée avec la gestion commerciale, ou non
 */
function prd_categories_get_for_new( $parent=0, $with_img=true ){
	global $config;

	$sql = '
		select cat_id as id, cat_name as name, if(ifnull(cat_title,"")="",cat_name,cat_title) as title,
			if(ifnull(cat_url_perso, \'\')=\'\', cat_url_alias, cat_url_perso) as url_alias, cat_desc as "desc", cat_keywords as keywords,
			cat_parent_id as parent_id, cat_products as products, cat_publish as publish,
			cat_cnt_id as cnt_id, cat_pos as pos, cat_is_sync as is_sync
		from prd_categories as cat
		where cat.cat_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null and cat_publish
	';
	if( is_numeric( $parent ) && $parent>0 ){
		$sql .= ' and cat_parent_id = '.$parent;
	}else{
		$sql .= ' and cat_parent_id is null';
	}
	$sql .= '
		and (exists (
			select 1 from prd_cat_hierarchy as hry
			join prd_categories as cat2 on hry.cat_tnt_id = cat2.cat_tnt_id and hry.cat_child_id=cat2.cat_id
			join prd_classify on cat2.cat_tnt_id = cly_tnt_id and cat2.cat_id = cly_cat_id
			join prd_products on cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id
			where cat2.cat_date_deleted is null and cat2.cat_publish
			and prd_date_deleted is null and prd_publish and prd_publish_cat
			and prd_new!="-1" and (prd_new="1" or datediff(now(),prd_date_created)<='.$config['prd_new_days'].')
			'.( $with_img ? ' and prd_img_id is not null' : '' ).'
			and hry.cat_tnt_id = '.$config['tnt_id'].' and hry.cat_parent_id = cat.cat_id

			) or exists (

			select 1 from prd_classify
			join prd_products on cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id
			where prd_date_deleted is null and prd_publish and prd_publish_cat
			and prd_new!="-1" and (prd_new="1" or datediff(now(),prd_date_created)<='.$config['prd_new_days'].')
			'.( $with_img ? ' and prd_img_id is not null' : '' ).'
			and cly_tnt_id = '.$config['tnt_id'].' and cly_cat_id = cat.cat_id
		))
		order by cat_pos, if(ifnull(cat_title,"")="",cat_name,cat_title)
	';

	$r = ria_mysql_query( $sql );

	if( !$r ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql );
		}
		return false;
	}

	return $r;
}

/**	Cette fonction retourne les familles de produits qui contiennent des produits en déstockage publiés
 *	@param int $parent Facultatif, identifiant de la catégorie parente
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie
 *			- name : désignation de la catégorie
 *			- title : désignation de la catégorie pour le web
 *			- url_alias : url simplifiée de la catégorie
 *			- desc : description de la catégorie
 *			- keywords : mots clés spécifiques à la catégorie
 *			- parent_id : identifiant de la catégorie parente (null si aucune)
 *			- products : nombre de produits publiés dans la catégorie
 *			- publish : indique si la catégorie est publiée ou non
 *			- cnt_id : identifiant de la catégorie dans le moteur de recherche
 *			- pos : position d'affichage de la catégorie (lorsqu'elle est listée avec d'autres catégories de même niveau)
 *			- is_sync : indique si la catégorie est synchronisée avec la gestion commerciale, ou non
 */
function prd_categories_get_for_destockage( $parent=0 ){
	global $config;

	if( $config['prd_deposits']=='use-main' ){
		$dps = prd_deposits_get_main();
	}else{
		$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
	}

	$sql = '
		select cat_id as id, cat_name as name, if(ifnull(cat_title,"")="",cat_name,cat_title) as title,
			if(ifnull(cat_url_perso, \'\')=\'\', cat_url_alias, cat_url_perso) as url_alias, cat_desc as "desc", cat_keywords as keywords,
			cat_parent_id as parent_id, cat_products as products, cat_publish as publish,
			cat_cnt_id as cnt_id, cat_pos as pos, cat_is_sync as is_sync
		from prd_categories as cat
		where cat.cat_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null and cat_publish
	';
	if( is_numeric( $parent ) && $parent>0 ){
		$sql .= ' and cat_parent_id = '.$parent;
	}else{
		$sql .= ' and cat_parent_id is null';
	}
	$sql .= '
		and (exists (
			select 1 from prd_cat_hierarchy as hry
			join prd_categories as cat2 on hry.cat_tnt_id = cat2.cat_tnt_id and hry.cat_child_id=cat2.cat_id
			join prd_classify on cat2.cat_tnt_id = cly_tnt_id and cat2.cat_id = cly_cat_id
			join prd_products on cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id
			join prd_stocks on prd_tnt_id = sto_tnt_id and prd_id = sto_prd_id and sto_is_deleted=0
			where cat2.cat_date_deleted is null and cat2.cat_publish
			and prd_date_deleted is null and prd_publish and prd_publish_cat
			and sto_dps_id = '.$dps.' and prd_sleep and (' . prd_stocks_get_sql() . '-sto_prepa) > 0
			and hry.cat_tnt_id = '.$config['tnt_id'].' and hry.cat_parent_id = cat.cat_id

			) or exists (

			select 1 from prd_classify
			join prd_products on cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id
			join prd_stocks on prd_tnt_id = sto_tnt_id and prd_id = sto_prd_id and sto_is_deleted=0
			where prd_date_deleted is null and prd_publish and prd_publish_cat
			and sto_dps_id = '.$dps.' and prd_sleep and (' . prd_stocks_get_sql() . '-sto_prepa) > 0
			and cly_tnt_id = '.$config['tnt_id'].' and cly_cat_id = cat.cat_id
		))
		order by cat_pos, if(ifnull(cat_title,"")="",cat_name,cat_title)
	';

	$r = ria_mysql_query( $sql );

	if( !$r ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql );
		}
		return false;
	}

	return $r;
}

/**	Cette fonction retourne les familles de produits qui contiennent des produits publiés actuellement en promotion.
 *	\bug Les promotions spéciales ne sont pas prises en compte dans le résultat.
 *
 *	@param int $parent Facultatif, identifiant de la catégorie parente (spécifier -1 pour récupérer dans toutes l'arborescence).
 *	@param $get_prom_prd_count Facultatif, permet d'activer le calcul des produits en promotion dans chaque catégorie.
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la catégorie
 *			- name : désignation de la catégorie
 *			- title : désignation de la catégorie pour le web
 *			- url_alias : url simplifiée de la catégorie
 *			- desc : description de la catégorie
 *			- keywords : mots clés spécifiques à la catégorie
 *			- parent_id : identifiant de la catégorie parente (null si aucune)
 *			- products : nombre de produits publiés dans la catégorie
 *			- publish : indique si la catégorie est publiée ou non
 *			- cnt_id : identifiant de la catégorie dans le moteur de recherche
 *			- pos : position d'affichage de la catégorie (lorsqu'elle est listée avec d'autres catégories de même niveau)
 *			- is_sync : indique si la catégorie est synchronisée avec la gestion commerciale, ou non
 *			- products_promotions : uniquement si $get_prom_prd_count est activé. Nombre total de produits en promotion.
 */
function prd_categories_get_for_promotions( $parent=0, $get_prom_prd_count=false ){

	global $config;

	// le dépôt est requis pour déterminer les produits en déstockage
	$dps = prd_deposits_get_main();
	if( $config['prd_deposits'] == 'use-customer' ){
		if( isset($_SESSION['usr_dps_id']) && is_numeric($_SESSION['usr_dps_id']) && $_SESSION['usr_dps_id'] > 0 ){
			$dps = $_SESSION['usr_dps_id'];
		}
	}

	$sql = '
		select
			cat_id as id, cat_name as name, if(ifnull(cat_title, "") = "", cat_name, cat_title) as title,
			if(ifnull(cat_url_perso, "") = "", cat_url_alias, cat_url_perso) as url_alias, cat_desc as "desc", cat_keywords as "keywords",
			cat_parent_id as parent_id, cat_products as "products", cat_publish as "publish",
			cat_cnt_id as cnt_id, cat_pos as "pos", cat_is_sync as is_sync
	';

	if( $get_prom_prd_count ){
		$sql .= ', (
				select count(distinct p_id) from (
					select prd_id as p_id, hry.cat_parent_id as cat_id from
						prd_cat_hierarchy as hry
						join prd_categories as cat2 on hry.cat_tnt_id = cat2.cat_tnt_id and hry.cat_child_id = cat2.cat_id
						join prd_classify on cat2.cat_tnt_id = cly_tnt_id and cat2.cat_id = cly_cat_id
						join prd_products on cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id
						join prc_prices on prd_tnt_id = prc_tnt_id and prd_id = prc_prd_id
					where
						cat2.cat_date_deleted is null and cat2.cat_publish = 1
						and prd_date_deleted is null and prd_publish = 1 and prd_publish_cat = 1 and (
							prd_sleep = 0 or ifnull((
								select ' . prd_stocks_get_sql() . ' - sto_prepa from prd_stocks
								where sto_tnt_id = prd_tnt_id and sto_prd_id = prd_id and sto_dps_id = '.$dps.' and sto_is_deleted=0
							), 0) > 0
						) and prc_date_start <= now() and prc_date_end > now() and prc_is_promotion = 1 and prc_is_deleted = 0
						and hry.cat_tnt_id = '.$config['tnt_id'].'

					union all

					select prd_id as p_id, cly_cat_id as cat_id from
						prd_classify
						join prd_products on cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id
						join prc_prices on prd_tnt_id = prc_tnt_id and prd_id = prc_prd_id
					where
						prd_date_deleted is null and prd_publish = 1 and prd_publish_cat = 1 and (
							prd_sleep = 0 or ifnull((
								select ' . prd_stocks_get_sql() . ' - sto_prepa from prd_stocks
								where sto_tnt_id = prd_tnt_id and sto_prd_id = prd_id and sto_dps_id = '.$dps.' and sto_is_deleted=0
							), 0) > 0
						) and prc_date_start <= now() and prc_date_end > now() and prc_is_promotion = 1 and prc_is_deleted = 0
						and cly_tnt_id = '.$config['tnt_id'].'
				) as tmp
				where
					tmp.cat_id = cat.cat_id
			) as products_promotions
		';
	}

	$sql .= '
		from
			prd_categories as cat
		where
			cat.cat_tnt_id = '.$config['tnt_id'].' and cat_date_deleted is null and cat_publish = 1
	';

	if( is_numeric($parent) && $parent > 0 ){
		$sql .= '
			and cat_parent_id = '.$parent.'
		';
	}elseif( $parent !== -1 ){
		$sql .= '
			and cat_parent_id is null
		';
	}

	$sql .= '
			and (exists (
				select prd_id as p_id from
					prd_cat_hierarchy as hry
					join prd_categories as cat2 on hry.cat_tnt_id = cat2.cat_tnt_id and hry.cat_child_id = cat2.cat_id
					join prd_classify on cat2.cat_tnt_id = cly_tnt_id and cat2.cat_id = cly_cat_id
					join prd_products on cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id
					join prc_prices on prd_tnt_id = prc_tnt_id and prd_id = prc_prd_id
				where
					cat2.cat_date_deleted is null and cat2.cat_publish = 1
					and prd_date_deleted is null and prd_publish = 1 and prd_publish_cat = 1 and (
						prd_sleep = 0 or ifnull((
							select ' . prd_stocks_get_sql() . ' - sto_prepa from prd_stocks
							where sto_tnt_id = prd_tnt_id and sto_prd_id = prd_id and sto_dps_id = '.$dps.' and sto_is_deleted=0
						), 0) > 0
					) and prc_date_start <= now() and prc_date_end > now() and prc_is_promotion = 1 and prc_is_deleted = 0
					and hry.cat_tnt_id = '.$config['tnt_id'].' and hry.cat_parent_id = cat.cat_id

				) or exists (

				select prd_id as p_id from
					prd_classify
					join prd_products on cly_prd_id = prd_id and cly_tnt_id = prd_tnt_id
					join prc_prices on prd_tnt_id = prc_tnt_id and prd_id = prc_prd_id
				where
					prd_date_deleted is null and prd_publish = 1 and prd_publish_cat = 1 and (
						prd_sleep = 0 or ifnull((
							select ' . prd_stocks_get_sql() . ' - sto_prepa from prd_stocks
							where sto_tnt_id = prd_tnt_id and sto_prd_id = prd_id and sto_dps_id = '.$dps.' and sto_is_deleted=0
						), 0) > 0
					) and prc_date_start <= now() and prc_date_end > now() and prc_is_promotion = 1 and prc_is_deleted = 0
					and cly_tnt_id = '.$config['tnt_id'].' and cly_cat_id = cat.cat_id
			))
		order by
			cat_pos, if(ifnull(cat_title, "") = "", cat_name, cat_title)
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql );
		}
		return false;
	}

	return $res;

}

// \cond onlyria
/** Détermine si une catégorie doit être publiée sur un site en fonction d'une langue (le paramètre "cat_products" ne pouvant être pris en compte)
 *
 *	@param int $cat_id Identifiant de la catégorie
 *	@param string $lng Code ISO de la langue. S'il s'agit de la langue par défaut, le paramètre cat_products est utilisé
 *
 *	@return bool True si la catégorie doit être affichée, False sinon
 */
function prd_categories_has_products( $cat_id, $lng ){
	global $config;
	global $memcached;

	if( !prd_categories_exists( $cat_id ) ) return false;
	if( !in_array( $lng, $config['i18n_lng_used'] ) ) return false;

	if( $kid = $memcached->get( 'category:prdcategorieshasproducts:'.$config['tnt_id'].':'.$cat_id.':'.$lng ) ){
		return $kid;
	}

	$products = true;
	if( $lng!=$config['i18n_lng'] && isset($config['catalog_publish_lng']) && $config['catalog_publish_lng'] ){
		$rp = prd_products_get_simple( 0, '', false, $cat_id, true, array( _FLD_PRD_PUBLISH=>'Oui' ), false, false, array('childs'=>true) );
		if( !$rp || !ria_mysql_num_rows($rp) ){
			$products = false;
		}
	}elseif( !prd_categories_get_prd_count( $cat_id ) ){
		$products = false;
	}

	$memcached->set( 'category:prdcategorieshasproducts:'.$config['tnt_id'].':'.$cat_id.':'.$lng, $products, 300 );
	return $products;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de nettoyer le champ cly_url_is_canonical
 *	@param int $cat Obligatoire, Identifiant de la catégorie supprimé
 *	\\afinir
 */
function prd_categories_del_canonical( $cat ){
	if( !is_numeric($cat) || $cat <= 0 ) return false;
	global $config;

	// récupère toutes les classifications concerné par cette modification y compris les classifications des enfants et renseigner cly_url_is_cononical à null
	$sql = '
		select cly_prd_id as prd_id
		from prd_classify cly
		where cly_tnt_id = '.$config['tnt_id'].'
		and cly_cat_id = '.$cat.'
		and cly.cly_url_is_canonical = 1

		union

		select cly_prd_id as prd_id
			from prd_categories c, prd_cat_hierarchy as hry, prd_classify cly
			where
				hry.cat_tnt_id='.$config['tnt_id'].' and
				hry.cat_tnt_id=c.cat_tnt_id and
				c.cat_tnt_id=cly.cly_tnt_id and
				hry.cat_parent_id ='.$cat .' and
				hry.cat_child_id=c.cat_id and
				cly.cly_url_is_canonical = 1 and
				cly.cly_cat_id = c.cat_id
		';
	$rcly = ria_mysql_query( $sql );
	while( $cly = ria_mysql_fetch_array( $rcly ) ){
		ria_mysql_query( 'update prd_classify set cly_url_is_canonical=null where cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = '.$cly['prd_id'] );
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si une catégorie est enfant d'une autre catégorie, indépendamment du nombre de niveaux intérmédiaires qu'il peut y avoir entre les deux. Les deux catégories ne doivent pas être supprimées.
 *	@param $cat_child Identifiant de la catégorie enfant.
 *	@param $cat_parent Identifiant de la catégorie parent.
 *	@return bool True si les catégories ont un lien de parenté, False sinon.
 */
function prd_categories_is_child_of( $cat_child, $cat_parent ){

	if( !is_numeric($cat_child) || $cat_child <= 0 ){
		return false;
	}
	if( !is_numeric($cat_parent) || $cat_parent <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1 from prd_cat_hierarchy as h
			join prd_categories as c1 on h.cat_tnt_id = c1.cat_tnt_id and h.cat_parent_id = c1.cat_id
			join prd_categories as c2 on h.cat_tnt_id = c2.cat_tnt_id and h.cat_child_id = c2.cat_id
		where h.cat_tnt_id = '.$config['tnt_id'].'
			and h.cat_parent_id = '.$cat_parent.'
			and h.cat_child_id = '.$cat_child.'
			and c1.cat_date_deleted is null
			and c2.cat_date_deleted is null
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère des catégories issus des produits qui sont compris dans l'historique de commandes d'un client, filtrés selon des critères optionnels.
 *	@param int $usr_id Obligatoire, identifiant du client.
 *	@param int|array $brd_id Optionnel, identifiant ou tableau d'identifiants de marques.
 *	@param int|array $cat_id Optionnel, identifiant ou tableau d'identifiants de catégories.
 *	@param bool $catchilds Optionnel, détermine si le paramètre $cat permet une recherche dans les sous-catégories.
 *	@param $inc_invoices Optionnel, détermine si les factures sans commande d'origine doivent être prises en compte.
 *	@param $fields Optionnel, permet de filtrer les lignes de commande (et de facture si $inc_invoices activé) suivant la présence, ou la valeur, d'un ou plusieurs champs avancés.
 *		L'argument doit être spécifié sous la forme d'un tableau de tableaux associatifs, chaque sous-tableau comprenant :
 *			- id : l'identifiant du champ avancé (classe "ligne de commande").
 *			- [inv_id] : l'identifiant du champ avancé (classe "ligne de facture"), si $inc_invoices est activé.
 *			- [value] : valeur de filtrage optionnelle.
 *	@param bool $publish Optionnel, détermine si les produits publiés, non publiés ou les deux doivent être retournés.
 *	@param bool $orderable Optionnel, détermine si les produits commandables, non commandables ou les deux doivent être retournés.
 *	@param $cat_level Optionnel, détermine le niveau (absolu) des familles à retourner (null : tous les niveaux).
 *
 *	@return bool False en cas d'échec.
 *	@return Un résultat identique à la fonction prd_categories_get() pour les catégories récupérées.
 */
function prd_categories_get_from_history( $usr_id, $brd_id=0, $cat_id=0, $catchilds=false, $inc_invoices=false, $fields=false, $publish=null, $orderable=null, $cat_level=null ){

	if( $cat_level !== null && ( !is_numeric($cat_level) || $cat_level < 0 ) ){
		return false;
	}

	$cat_id = control_array_integer( $cat_id, false );
	if( $cat_id === false ){
		return false;
	}

	// charge les produits associés
	$rprd = prd_products_get_from_history( $usr_id, $brd_id, $cat_id, $catchilds, $inc_invoices, $fields, $publish, $orderable );

	if( !$rprd ){
		return false;
	}

	$final_cats = $checked = array();

	while( $p = ria_mysql_fetch_assoc($rprd) ){

		// charge tous les classements de chaque produit
		if( $rcly = prd_classify_get( false, $p['id'] ) ){
			while( $cly = ria_mysql_fetch_assoc($rcly) ){

				// $checked permet de ne pas vérifier plusieurs fois chaque catégorie
				if( !in_array($cly['cat'], $checked) ){

					// détermine si la catégorie de classement fait partie de(s) l'arborescence(s) spécifiée(s) en paramètre (s'il y'en a)
					$in_tree = true;
					if( sizeof($cat_id) ){
						$in_tree = in_array($cly['cat'], $cat_id);
						if( !$in_tree && $catchilds ){
							foreach( $cat_id as $one_c ){
								if( prd_categories_is_child_of( $cly['cat'], $one_c ) ){
									$in_tree = true;
									break;
								}
							}
						}
					}

					if( $in_tree ){
						// charge la catégorie à la bonne profondeur
						if( $cat_level !== null ){
							$depth = prd_categories_depth_get( $cly['cat'] );
							if( $cat_level == $depth ){
								$final_cats[] = $cly['cat'];
							}elseif( $depth > $cat_level ){
								if( $rtree = prd_categories_parents_get( $cly['cat'] ) ){
									while( $croot = ria_mysql_fetch_assoc($rtree) ){
										if( $croot['depth'] == $cat_level ){
											$final_cats[] = $croot['id'];
											break;
										}
									}
								}
							}
						}else{
							$final_cats[] = $cly['cat'];
						}
					}

				}
				$checked[] = $cly['cat'];

			}
		}

	}

	if( !sizeof($final_cats) ){
		return false;
	}

	return prd_categories_get( $final_cats );

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour l'information de publication  des produits dans la catégorie donnée.
 *	@param int $cat_id Obligatoire, identifiant de la catégorie.
 *	@param $reindex_classify Optionnel, détermine si une réindexation du classement du produit dans la catégorie doit être effectuée.
 */
function prd_categories_rebuild_prd_publish_cat( $cat_id, $reindex_classify=false ){

	$cats_ar = prd_categories_childs_get_array( $cat_id );
	if( !is_array($cats_ar) ){
		$cats_ar = array();
	}
	$cats_ar[] = $cat_id;

	global $config;

	$sql = '
		select cly_prd_id as id
		from prd_classify
		where cly_tnt_id = '.$config['tnt_id'].'
			and cly_cat_id in ('.implode(', ', $cats_ar).')
	';

	if( $products = ria_mysql_query($sql) ){
		while( $p = ria_mysql_fetch_assoc($products) ){
			prd_products_reset_publish_cat($p['id'], false, false);
		}
	}

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la référence d'une catégorie.
 *	@param int $id Identifiant de la catégorie.
 *	@param string $ref Nouvelle référence de la catégorie (Null autorisé, mais pas vide).
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_categories_set_ref( $id, $ref ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}
	if( $ref !== null ){
		$ref = trim($ref);
		if( !$ref ){
			return false;
		}
	}

	global $config;

	$sql = '
		update prd_categories
		set cat_ref = '.( $ref !== null ? '"'.addslashes($ref).'"' : 'NULL' ).'
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$id.'
			and cat_date_deleted is null
	';

	return ria_mysql_query($sql);

}
// \endcond

/// @}

/** \defgroup cat_sort_type Types de tri associés aux catégories
 *	\ingroup model_categories
 *	Ce module comprend les fonctions nécessaires à la gestion des types de tri associés aux catégorie de produits.
 *	@{
 */

// \cond onlyria
/** Cette fonction permet l'attribution d'un type de tri sur la liste des produits d'une catégorie
 *
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@param $type Obligatoire, identifiant d'un type de tri
 *	@param $dir Optionnel, il s'agit de l'ordre du tri par défaut il est croissant 'asc', mettre 'desc' pour avoir l'ordre décroissant
 *	@param $recursive Optionnel, par défaut le tri ne s'applique pas aux catégories enfants, mettre true si tel doit être le cas
 *
 *	@return bool Retourne true si l'ajout s'est correctement passé
 *	@return bool Retourne false dans le cas contraire
 */
function prd_categories_sort_types_add( $cat, $type, $dir='asc', $recursive=false ){
	if( !prd_categories_exists($cat) ) return false;
	if( !sys_sort_types_exists($type) ) return false;
	global $config;

	$dir = $type==SORT_PERSO || $type==SORT_RANDOM ? 'asc' : $dir;
	$dir = trim( $dir )!='' ? strtolower($dir) : 'asc';
	if( !in_array($dir, array('asc', 'desc')) ) return false;

	// Supprime la ligne existante
	ria_mysql_query( 'delete from prd_categories_sort where sort_tnt_id='.$config['tnt_id'].' and sort_cat_id='.$cat );

	// Si le tri est personnalisé, on affecte une position au produit
	if( $type==SORT_PERSO ){
		$cats[] = $cat;

		// Si le tri est récursive, il faut affecter une position aux produits des catégories enfants
		if( $recursive ){
			$tmp = prd_categories_childs_get_array( $cat );
			$cats = array_merge($cats, $tmp);
		}

		foreach( $cats as $c ){
			$array_prd = array();
			$current_max_pos = -1;
			if( $rp = prd_products_get_simple( 0, '', false, $c, false, false, false, false, array('childs'=>true) ) ){
				while( $p = ria_mysql_fetch_array($rp) ){
					if( is_numeric($p['prd_pos']) && $p['prd_pos']>$current_max_pos ){
						$current_max_pos = $p['prd_pos'];
					}
					$array_prd[] = $p;
				}
			}
			$count = $current_max_pos + 1;
			if( sizeof($array_prd) ){
				$tmp = array();
				foreach( $array_prd as $p ){
					// en premier, les produits qui ont déjà une position
					// en deuxième, les produits publiés (sauf si "sort_perso_type_ignore_publish")
					// en dernier le reste
					if( is_numeric($p['prd_pos']) && $p['prd_pos']>=0 ){
						continue;
					}elseif(
						( !isset($config['sort_perso_type_ignore_publish']) || !$config['sort_perso_type_ignore_publish'] )
						&& $p['publish'] && $p['publish_cat']
					){
						ria_mysql_query('update prd_classify set cly_prd_pos='.$count.' where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$p['id'].' and cly_cat_id='.$c);
						$count++;
					}else{
						$tmp[] = $p['id'];
					}
				}
				foreach( $tmp as $p ){
					ria_mysql_query('update prd_classify set cly_prd_pos='.$count.' where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$p.' and cly_cat_id='.$c);
					$count++;
				}
			}
		}
	}

	return ria_mysql_query('
		insert into prd_categories_sort (sort_tnt_id, sort_cat_id, sort_type, sort_dir, sort_is_recursive)
		values('.$config['tnt_id'].', '.$cat.', '.$type.', \''.$dir.'\', '.($recursive ? 1 : 0).')
	');
}
// \endcond

// \cond onlyria
/** Cette fonction charge la configuration de tri d'une catégorie
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@return bool Retourne false si une erreur s'est produite
 *	@return resource Retourne un résultat MySQL contenant :
 *				- cat_id : identifiant de la catégorie
 *				- type : identifiant du type de tri
 *				- dir : direction du tri, croissant ou descroissant
 *				- is_recursive : si le tri s'applique aux catégories enfants
 *				- name : nom du tri
 *				- desc : description du tri
 */
function prd_categories_sort_types_get( $cat ){
	if( !is_numeric($cat) || $cat<=0 ) return false;

	global $config;

	$sql = '
		select sort_cat_id as cat_id, sort_type as type, sort_dir as dir, if(sort_is_recursive, 1, 0) as is_recursive, sort_name as name, sort_desc as "desc"
		from prd_categories_sort
			join sys_sort_types on sort_type=sort_id
		where sort_tnt_id='.$config['tnt_id'].'
			and sort_cat_id='.$cat.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

/** Cette fonction permet de récupérer le mode de tri d'une catégorie de produits
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@return bool Retourne false si une erreur s'est produite
 *	@return Retourne dans le cas contraire un tableau contenant le mode de tri :
 *				- type : identifiant du type de tri
 *				- dir : direction du tri (asc|desc)
 */
function prd_categories_sort_get( $cat ){
	if( !is_numeric($cat) || $cat<=0 ) return false;

	static $prev_cat = 0;
	static $prev_sort = array();

	if( $cat==$prev_cat && sizeof($prev_sort) ){
		return $prev_sort;
	}

	// Vérifié en premier s'il n'existe pas un tri direct
	$rs = prd_categories_sort_types_get( $cat );
	if( $rs && ($s = ria_mysql_fetch_array($rs)) ){
		$prev_cat = $cat;
		$prev_sort = array( 'type'=>$s['type'], 'dir'=>$s['dir'], 'is_recursive'=>$s['is_recursive'], 'inherited'=>$cat );
		return $prev_sort;
	}

	// Récupère les catégories parents
	$parents = prd_categories_parents_get_array( $cat );
	if( !is_array($parents) || !sizeof($parents) ) return false;
	$parents = array_reverse( $parents );

	foreach( $parents as $parent ){
		$rs = prd_categories_sort_types_get( $parent );
		if( $rs && ($s = ria_mysql_fetch_array($rs)) && $s['is_recursive'] ){
			$prev_cat = $cat;
			$prev_sort = array( 'type'=>$s['type'], 'dir'=>$s['dir'], 'is_recursive'=>$s['is_recursive'], 'inherited'=>$parent );
			return $prev_sort;
		}
	}

	return false;

};

// \cond onlyria
/** Cette fonction permet de supprimer un tri sur une catégorie
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@return bool Retourne true si la suppression s'est correctement déroulée
 *	@return bool Retourne false dans le cas contraire
 */
function prd_categories_sort_types_del( $cat ){
	if( !is_numeric($cat) || $cat<=0 ) return false;
	global $config;

	return ria_mysql_query('delete from prd_categories_sort where sort_tnt_id='.$config['tnt_id'].' and sort_cat_id='.$cat);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de réinitialisé tous les tris mis en place
 *	@return bool Retourne true si la réinitialisation est faite
 *	@return bool Retourne false si une erreur est survenue
 */
function prd_categories_sort_types_reset(){
	global $config;

	return ria_mysql_query('delete from prd_categories_sort where sort_tnt_id='.$config['tnt_id']);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les catégories n'ayant aucune image
 *	@param bool $published  Optionnel, ignoré par défaut, mettre True pour avoir que les catégories publiées, False pour les autres
 *	@param $secondary_only Optionnel, ignoré par défaut mettre True pour n'avoir que les catégories sans images secondaires
 */
function prd_categories_get_imageless($published = null, $secondary_only = null){
	global $config;
	$sql =	'
		select
			cat_id as id, cat_name as name, if(cat_title="",cat_name,cat_title) as title,
			if(ifnull(cat_url_perso, \'\')=\'\', cat_url_alias, cat_url_perso) as url_alias, cat_url_perso as url_perso, cat_url_alias,
			cat_desc as "desc", cat_keywords as keywords,
			cat_parent_id as parent_id, cat_products as products, cat_publish as publish,
			cat_cnt_id as cnt_id, cat_pos as pos, cat_is_sync as is_sync,
			cat_tag_title as tag_title, cat_tag_desc as tag_desc,
			date_format(cat_date_from,"%d/%m/%Y") as "date_from", time_format(cat_date_from,"%H:%i") as "hour_from",
			date_format(cat_date_to,"%d/%m/%Y") as "date_to", time_format(cat_date_to,"%H:%i") as "hour_to",
			cat_date_from as date_from_en, cat_date_to as date_to_en,
			date_format(cat_first_publish, "%d/%m/%Y à %H:%i") as first_publish, cat_first_publish as first_publish_en,
			cat_ref as "ref"
		from prd_categories as c
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_date_deleted is null
	';

	if($secondary_only === true){
		$sql .= ' and (select count(img_cat_id) from prd_cat_images as b WHERE c.cat_id = b.img_cat_id and b.img_tnt_id = '.$config['tnt_id'].') = 1 ';
	}else{
		$sql .= ' and NOT EXISTS (select img_cat_id from prd_cat_images as b WHERE c.cat_id = b.img_cat_id and b.img_tnt_id = '.$config['tnt_id'].') ';
	}

	if( $published !== null ){
		if ($published) {
			$sql .=' and cat_publish and cat_products > 0';
		}else{
			$sql .= ' and (cat_publish = 0 or cat_products <= 0)';
		}
	}

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}
	return $r;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information disant qu'une catégorie est exclusivement réservée aux soldes.
 *	@param int $cat_id Identifiant d'une catégorie
 *	@param $is_soldes Optionnel, si oui ou non la catégorie est réservée aux soldes, par défaut à True
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_categories_set_is_soldes( $cat_id, $is_soldes=true ){
	if (!is_numeric($cat_id) || $cat_id <= 0) {
		return false;
	}

	global $config;

	return ria_mysql_query('
		update prd_categories
		set cat_is_soldes = '.($is_soldes ? 1 : 0).'
		where cat_tnt_id = '.$config['tnt_id'].'
			and cat_id = '.$cat_id.'
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajout un lien entre une catégorie et une promotion (promotion à application automatique)
 *	@param int $cat_id Identifiant d'une catégorie
 *	@param $cod_id Identifiant d'une promotion
 *	@return bool True si l'ajout s'est correctement déroulée, False dans le cas contraire
 */
function prd_categories_codes_add( $cat_id, $cod_id ){
	if (!is_numeric($cat_id) || $cat_id<=0) {
		return false;
	}

	if (!is_numeric($cod_id) || $cod_id<=0) {
		return false;
	}

	$r_pmt = pmt_codes_get( $cod_id, null, false, false, true );
	if (!$r_pmt || !ria_mysql_num_rows($r_pmt)) {
		return false;
	}

	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'ccd_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'ccd_cat_id';
	$values[] = $cat_id;

	$fields[] = 'ccd_cod_id';
	$values[] = $cod_id;

	$res = ria_mysql_query('
		insert into prd_categories_codes
			('.implode(',', $fields).')
		values
			('.implode(',', $values).')
	');

	if (!$res) {
		return false;
	}

	prd_categories_refresh_products_published( $cat_id );
	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une relation entre une catégorie et un ou plusieurs promotion (voir toutes).
 *	@param int $cat_id Identifiant d'une catégorie
 *	@param $cod_id Optionnel, identifiant ou tableau d'identifiants de promotions
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function prd_categories_codes_del( $cat_id, $cod_id=0 ){
	if (!is_numeric($cat_id) || $cat_id<=0) {
		return false;
	}

	$cod_id = control_array_integer( $cod_id, false );
	if ($cod_id === false) {
		return false;
	}

	global $config;

	$sql = '
		delete from prd_categories_codes
		where ccd_tnt_id = '.$config['tnt_id'].'
			and ccd_cat_id = '.$cat_id.'
	';

	if( count($cod_id) ){
		$sql .= ' and ccd_cod_id in ('.implode(',', $cod_id).')';
	}

	$res = ria_mysql_query( $sql );
	if (!$res) {
		return false;
	}

	prd_categories_refresh_products_published( $cat_id );
	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les promotions rattachées à une catégorie
 *	@param int|array $cat_id Optionnel, identifiant ou tableau d'identifiants de catégories
 *	@param $cod_id Optionnel, identifiant ou tableau d'identifiants de promotions
 *	@return resource Un résultat MySQL contenant :
 *				- cat_id : identifiant de la catégorie
 *				- cat_name : nom de la catégorie
 *				- cod_id : identifiant de la promotion
 *				- cod_name : nom de la catégorie
 */
function prd_categories_codes_get( $cat_id=0, $cod_id=0 ){
	$cat_id = control_array_integer($cat_id, false);
	if ($cat_id===false) {
		return false;
	}

	$cod_id = control_array_integer($cod_id, false);
	if ($cod_id===false) {
		return false;
	}

	global $config;

	$sql = '
		select cat_id, cat_name, cod_id, cod_name
		from prd_categories_codes
			join prd_categories on (cat_tnt_id = ccd_tnt_id and cat_id = ccd_cat_id)
			join pmt_codes on (cod_tnt_id = ccd_tnt_id and cod_id = ccd_cod_id)
		where ccd_tnt_id = '.$config['tnt_id'].'
			and cat_date_deleted is null
			and cod_date_deleted is null
	';

	if (count($cat_id)) {
		$sql .= ' and ccd_cat_id in ('.implode(',', $cat_id).')';
	}

	if (count($cod_id)) {
		$sql .= ' and ccd_cod_id in ('.implode(',', $cod_id).')';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer un tableau d'identifiants de produits inclut dans une catégorie selon les codes promotions qui lui sont rattachées.
 *	@param int|array $cat_id Identifiant ou tableau d'identifiants de catégories
 *	@return array Un tableau contenant les identifiants de produits
 */
function prd_categories_codes_get_prd_ids( $cat_id ){
	$cat_id = control_array_integer($cat_id, true);
	if ($cat_id===false) {
		return false;
	}

	$ar_prd_ids = array();
	require_once 'Stock/PmtOutstock.php';
	global $config;
	$dps = 0;
	if( $config['prd_deposits']=='use-main' ){
		$dps = prd_deposits_get_main();
	}else{
		$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
	}
	$r_cod_cat = prd_categories_codes_get( $cat_id );
	if ($r_cod_cat) {
		while ($cod_cat=ria_mysql_fetch_assoc($r_cod_cat)) {
			$pmt_codes_get_available_stocks = pmt_codes_get_available_stocks( $cod_cat['cod_id']);
			$r_cod_prd = pmt_codes_products_get( $cod_cat['cod_id'], false, 0, false, true );
			if( is_array($r_cod_prd) ){
				foreach( $r_cod_prd as $cod_prd ){
					$in_stock = true;
					// si on est pas dans le context de l'admin riashop
					// on vérifie si pour
					if( !defined('CONTEXT_IS_ADMIN') && $pmt_codes_get_available_stocks ){
						$PmtOutstock = new \Stock\PmtOutstock( $cod_prd['prd_id'], $dps );
						$stock = $PmtOutstock->getStock();
						if( $PmtOutstock->isActive() && prd_products_get_follow_stock( $cod_prd['prd_id'] ) ){
							if( !is_numeric($stock) || $stock <= 0 ){
								$in_stock = false;
							}
						}
					}
					if( $in_stock ){
						$ar_prd_ids[] = $cod_prd['prd_id'];
					}
				}
			}
		}
	}

	return array_unique( $ar_prd_ids );
}
// \endcond

// \cond onlyria
/** Détermine si il y a des produits commun dans 2 catégorie
 *	@param int $cat1_id Identifiant de la catégorie 1
 *	@param int $cat2_id Identifiant de la catégorie 2
 *
 *	@return bool Retourne true si il y a des produits dans les deux catégorie, false dans le cas contraires
 */
function prd_categories_have_products_in_both($cat1_id, $cat2_id){

	if( !is_numeric($cat1_id) || $cat1_id <= 0 ){
		throw new InvalidArgumentException("$cat1_id doit être > 0");
	}

	if( !is_numeric($cat2_id) || $cat2_id <= 0 ){
		throw new InvalidArgumentException("$cat2_id doit être > 0");
	}

	global $config;

	$sql = '
		select 1
		from prd_classify as cl_a
			join prd_classify as cl_b on (cl_b.cly_tnt_id=cl_a.cly_tnt_id and cl_b.cly_prd_id=cl_a.cly_prd_id)
			join prd_categories as cat on (cl_b.cly_cat_id=cat.cat_id and cl_a.cly_tnt_id=cat.cat_tnt_id)
		where cl_a.cly_tnt_id='.$config['tnt_id'].'
			and cl_a.cly_cat_id='.$cat2_id.'
			and cl_b.cly_cat_id='.$cat1_id.'
			and cat.cat_date_deleted is null
			and cat.cat_publish = 1
	';

	$r = ria_mysql_query($sql);

	if( $r && ria_mysql_num_rows($r) ){
		return true;
	}

	return false;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les données que l'on souhaite exporter, pour la catégorie donnée ainsi que pour ses sous catégories.
 * @param $fields Obligatoire. Définit les colonnes que l'on va exporter
 * @param int $cat_id Facultatif. Identifiant de la catégorie dont on va faire l'export. Par défaut 0 pour récupérer sur toutes les catégories.
 * @param $category_name Facultatif. Désignation de la catégorie parent. Fil d'ariane permettant l'affichage sous la forme "Catégorie >> Sous-catégorie 1 >> Sous-catégorie 2 etc".
 * @param $recursive Facultatif. Variable booléenne. True si l'on va chercher les sous-catégories, false sinon. Par défaut à true.
 * @param $image_sizes Facultatif. Tableau des tailles des champs image sélectionnés. Par défaut c'est un tableau vide.
 * @param $exclude_ids Facultatif. Tableau des identifiants des catégories à ne pas prendre en compte. Par défaut c'est un tableau vide.
 * @param $exp_id Facultatif. Identifiant de l'export
 * @return Le tableau comportant les lignes à exporter en cas de succès, false en cas d'échec.
 */
function prd_categories_export($fields, $cat_id=0, $category_name='', $recursive=true, $image_sizes=array(), $exclude_ids=array(), $exp_id=0){
	if( !isset($fields) ){
		return false;
	}
	if( !is_numeric($cat_id) || $cat_id < 0 ){
		return false;
	}
	if( !is_array($image_sizes) ){
		return false;
	}
	if( !is_array($exclude_ids) ){
		return false;
	}
	if( !is_numeric($exp_id) ){
		return false;
	}

	global $config;

	$sub_categories = prd_categories_get( 0, false, $cat_id );
	$nb_categories = ria_mysql_num_rows( $sub_categories );
	if( $nb_categories > 0 ){

		if( $exp_id ){
			exp_exports_upd( $exp_id, 'processing', $nb_categories, 0);
		}

		if( !$category_name == '' ){
			$c_name = $category_name.' >> ';
		}else{
			$c_name = '';
		}

		$rows = array();
		$tab_rows = array();

		$cursor = 0;
		while( $cat = ria_mysql_fetch_assoc($sub_categories) ){
			$cursor++;

			// Met à jour le nombre de ligne traité de l'export à chaque tranche de 10% de catégories traités
			if( $exp_id ){
				if( $nb_categories > 20 ){
					if( !($cursor%($nb_categories/10)) ){
						exp_exports_upd( $exp_id, '', $nb_categories, $cursor );
					}
				}
			}

			if( in_array($cat['id'], $exclude_ids) ){
				continue;
			}
			$data_row = array();
			foreach( $fields as $field => $name ){
				if( $name == 'title' ){
					$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $c_name.$cat[$name]);
				}elseif( is_numeric($name) ){
					$res = fld_fields_get_values($name,$cat['id']);
					$value = ria_mysql_fetch_assoc($res);
					$row_value = '';
					if( array_key_exists($name, $image_sizes) && $value['value'] !== null ){
						$img_type = img_images_get_type($value['value']);
						$row_value = $config['img_url'].'/'.$image_sizes[$name].'/'.$value['value'].'.'.$img_type;
					}else{
						$row_value = $value['value'];
					}
					$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $row_value);
				}else{
					$data_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $cat[$name]);
				}
			}
			$tab_rows[] = implode(';', $data_row) ;
			if( $recursive ){
				$tab_rows[] = prd_categories_export($fields, $cat['id'], $c_name.$cat['title'], $recursive, $image_sizes, $exclude_ids);
			}
		}

		foreach( $tab_rows as $tab => $row ){
			if( is_array($row) ){
				foreach( $row as $t=>$r ){
					if($r !== null){
						$rows[] = $r;
					}
				}
			}else{
				if( $row !== null ){
					$rows[] = $row;
				}
			}
		}

		return $rows;
	}
}
// \endcond

/// @}

