<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/decl.proto

namespace Google\Api\Expr\V1beta1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * An identifier declaration.
 *
 * Generated from protobuf message <code>google.api.expr.v1beta1.IdentDecl</code>
 */
class IdentDecl extends \Google\Protobuf\Internal\Message
{
    /**
     * Optional type of the identifier.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.DeclType type = 3;</code>
     */
    private $type = null;
    /**
     * Optional value of the identifier.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.Expr value = 4;</code>
     */
    private $value = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Api\Expr\V1beta1\DeclType $type
     *           Optional type of the identifier.
     *     @type \Google\Api\Expr\V1beta1\Expr $value
     *           Optional value of the identifier.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Expr\V1Beta1\Decl::initOnce();
        parent::__construct($data);
    }

    /**
     * Optional type of the identifier.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.DeclType type = 3;</code>
     * @return \Google\Api\Expr\V1beta1\DeclType
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Optional type of the identifier.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.DeclType type = 3;</code>
     * @param \Google\Api\Expr\V1beta1\DeclType $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Expr\V1beta1\DeclType::class);
        $this->type = $var;

        return $this;
    }

    /**
     * Optional value of the identifier.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.Expr value = 4;</code>
     * @return \Google\Api\Expr\V1beta1\Expr
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * Optional value of the identifier.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.Expr value = 4;</code>
     * @param \Google\Api\Expr\V1beta1\Expr $var
     * @return $this
     */
    public function setValue($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Expr\V1beta1\Expr::class);
        $this->value = $var;

        return $this;
    }

}

