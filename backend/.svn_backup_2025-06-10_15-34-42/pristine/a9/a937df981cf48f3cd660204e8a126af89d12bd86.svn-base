<?php

require_once('excel/PHPExcel.php');

/**
 * \class ImportFile
 * \brief Cette classe permet de lire un fichier CSV ou Excel et déterminer le nombre de lignes et de colonnes
 *	que le fichier contient.
 * \ingroup ImportModule
 * @{
 */
class ImportFile{

	private $handle = null; ///< La resource du fichier
	private $content = null; ///< Le contenu du fichier
	private $importType = false; ///< Indique si le fichier est sur ftp ou non
	private $import_dir = ''; ///< chemin du dossier de sauvegarde de l'import
	private $ftp_connection = null; ///< Connection au ftp

	public $localFilePath = null; ///< le nom ou chemin vers le fichier
	public $filename = null; ///< nom du fichier
	public $original_filename = null; ///< nom du fichier original
	public $serverPath = null; ///< chemin du fichier sur le serveur ftp
	public $charset = null; ///< l'encodage du fichier
	public $size = null; ///< la taille du fichier
	public $ext = null; ///< extension du fichier
	public $columns = array(); ///< les colonnes du fichier
	public $lines_count = 0; ///< le nombre de lignes du fichier

	/** Le constructeur permet d'initialiser les variables impératives pour le traitement d'un fichier
	 *	@param string $filepath Chemin ou url vers un fichier
	 *	@param string $importType Type de fichier à importer :
	 *                    - file : fichier à télécharger depuis un ordinateur
	 *                    - ftp : fichier à télécharger depuis un ftp
	 *                    - url : fichier à télécharger depuis une url
	 *	@param string $filename Facultatif, le nom du fichier à sauvegarder, utile pour les fichiers uploadés
	 */
	public function __construct($filepath, $importType, $filename = ""){
		global $config;

		$allowedTypes = array('ftp' => 'ftp', 'url' => 'url', 'file' => 'file');

		if (!isset($allowedTypes[strtolower($importType)])) {
			throw new Exception(_("La méthode de récupération du fichier est incorrecte"));
		}

		$this->importType = $importType;

		$this->import_dir = IMPORT_ROOT_DIR;

		// Vérifie l'existance du dossier d'importation
		if (!is_dir($this->import_dir)) {
			// @ pour ne pas avoir de warning si le dossier n'a pas les droits d'accès
			if (@mkdir($this->import_dir, 755, true)) {
				chgrp($this->import_dir, 'apache');
				chown($this->import_dir, 'apache');
			}else{
				error_log('[Tenant ' . $config['tnt_id'] . '][erreur][ImportFile::ImportFile:' . __LINE__ . '] - Il est possible que les droits soient inexistants sur le dossier "' . $this->import_dir . '"');
				throw new RuntimeException(_("Le dossier de l'import ne peut pas être créé"));
			}
		}

		$this->original_filename = $filename;
		$this->localFilePath = $filepath;
		$this->serverPath = $filepath;
		$tmp_filename = $filename;
		if( trim($filename) == '' ){
			$tmp_filename = $filepath;
		}
		$this->ext = strtolower(pathinfo(	$tmp_filename, PATHINFO_EXTENSION));

		if (trim($filename) == "") {
			$this->filename = $this->sanitizeFileName(pathinfo($filepath, PATHINFO_FILENAME)) . '.csv';
		}else{
			$this->filename = $this->sanitizeFileName($filename);
		}
	}

	/** Cette fonction permet de nettoyer/protéger le nom d'un fichier des caractères pouvant présenter un risque
	 *	pour la sécurité
	 *    @param string $dangerousFilename Nom d'un fichier à contrôler
	 *
	 *    @return string|bool le Nom du fichier contrôlé, false si le nom du fichier n'est pas un string
	 */
	function sanitizeFileName($dangerousFilename){

		if (!is_string($dangerousFilename)) {
			return false;
		}

		$dangerousCharacters = array(" ", '"', "'", "&", "/", "\\", "?", "#", '(', ')');
		return strtolower( str_replace($dangerousCharacters, '_', $dangerousFilename) );
	}

	/** Cette fonction permet d'enregistrer un fichier distant en local
	 *	@return bool true en cas de succès, false en cas d'échec
	 */
	public function saveFile(){

		$sanitized = $this->sanitizeFileName(pathinfo($this->filename, PATHINFO_FILENAME));

		// Génère un nom uniquement pour le fichier
		$tmp_name = 'tmp-' . $sanitized . '-' . uniqid() . '.csv';
		$this->filename = $sanitized . '-' . uniqid() . '.csv';

		// Téléchargement du fichier et déplacement dans l'espace de travail
		switch ($this->importType) {
			case 'file': {
				if (!copy($this->localFilePath, $this->import_dir . $tmp_name)) {
					throw new Exception(_("Impossible d'enregistrer le fichier"));
				}
				break;
			}
			case 'ftp': {
				if (!isset($this->ftp_connection)) {
					throw new Exception(_("Aucune connexion avec un serveur FTP n'a été établie"));
				}
				if (!ftp_get($this->ftp_connection, $this->import_dir . $tmp_name, $this->localFilePath, FTP_BINARY)) {
					throw new Exception(_("Impossible d'enregistrer le fichier localement"));
				}
				break;
			}
			case 'url': {
				if (!file_put_contents($this->import_dir . $tmp_name, fopen($this->localFilePath, 'r'))) {
					throw new Exception(_("Impossible d'enregistrer le fichier localement"));
				}
				break;
			}
		}

		if( $this->ext=='odt' || $this->ext=='xls' || $this->ext=='xlsx' ){

			// Converti le fichier en CSV pour uniformiser les traitements ultérieurs
			if ($this->ext == 'xls') {
				$objReader = PHPExcel_IOFactory::createReader('Excel5');
			}else{
				$objReader = PHPExcel_IOFactory::createReader('Excel2007');
			}
			$objPHPExcelReader = $objReader->load($this->import_dir . $tmp_name);

			$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcelReader, 'CSV');

			// Enregistre le fichier au format CSV
			$objWriter->save($this->import_dir . $tmp_name);
		}

		// Actualise l'emplacement du fichier après déplacement
		$this->localFilePath = $this->import_dir . $tmp_name;

		return $this->convertToUTF8();
	}

	/** Cette fonction permet de supprimer un fichier
	 *	@return bool true en cas de succès, false en cas d'échec
	 */
	public function deleteFile(){
		return unlink($this->localFilePath);
	}

	/** Cette fonction permet de récupérer la taille d'un fichier en octets
	 *	@return int la taille du fichier, en octets
	 */
	public function getSize(){
		return filesize($this->localFilePath);
	}

	/** cette fonction permet de récupérer le chemin vers le fichier à importer
	 *	@return string le chemin local si file ou le chemin sur le serveur ftp si ftp. Rien si URL.
	 */
	public function getFilePath(){
		if ($this->importType == 'file') {
			return $this->localFilePath;
		} elseif ($this->importType == 'ftp') {
			return $this->serverPath;
		}else{
			return '';
		}
	}

	/** Cette fonction permet de se connecter à un serveur ftp
	 *	@param string $url nom de domaine ou adresse ip du serveur
	 *	@param string $login identifiant pour se connecter au serveur ftp
	 *	@param string $password mot de passe pour se connecter au serveur ftp
	 *	@param bool $is_ssl Optionnel par défaut à false, mettre true pour réaliser une connexion ftp_ssl
	 *
	 *	@return resource une connection ftp en cas de succès, false en cas d'échec
	 */
	public function connectToFtp($url, $login, $password, $is_ssl=false){

		if (isset($this->ftp_connection)) {
			return $this->ftp_connection;
		}

		preg_match('/(.*):([0-9]+)/', $url, $output_array);

		if( is_array($output_array) && count($output_array) == 3 ){
			if( $is_ssl ){
				$conn = ftp_ssl_connect( $output_array[1], $output_array[2] );
			}else{
				$conn = ftp_connect( $output_array[1], $output_array[2] );
			}
		}else{
			if( $is_ssl ){
				$conn = ftp_ssl_connect($url);
			}else{
				$conn = ftp_connect($url);
			}
		}

		if (!ftp_login($conn, $login, $password)) {
			// Return the resource
			throw new Exception(_("Impossible d'établir une connexion au serveur FTP"));
		}

		ftp_pasv($conn, true);

		return $this->ftp_connection = $conn;
	}

	/** Cette fonction permet de fermer une connexion ftp
	 *	@return bool true en cas de succès, false en cas d'échec
	 */
	public function closeFtpConnection(){

		if (isset($this->ftp_connection)) {
			return ftp_close($this->ftp_connection);
		}

		return true;
	}

	/** Cette fonction permet de tester la présence du fichier sur disque (côté serveur)
	 *
	 *	@return bool true si le fichier existe sur le serveur, false dans le cas contraire
	 */
	public function exists(){
		return file_exists($this->localFilePath);
	}

	/** Cette fonction permet d'ouvrir un fichier
	 *
	 *	@return resource le handle du fichier
	 */
	public function openFile(){

		if (!is_null($this->handle)) {
			return $this->handle;
		}
		if (!file_exists($this->localFilePath)) {
			throw new Exception(_("Impossible de sauvegarder votre fichier, veuillez vérifier l'encodage (UTF-8)."));
		}
		$this->handle = fopen($this->localFilePath, 'r');
		if (!$this->handle) {
			throw new Exception(_("Impossible d'ouvrir votre fichier, veuillez vérifier le contenu de votre fichier."));
		}

		return $this->handle;
	}

	/** Cette fonction permet la fermeture du fichier
	 *
	 *	@return resource le handle du fichier
	 */
	public function closeFile(){
		fclose($this->handle);
		$this->handle = null;
		return true;
	}

	/** Cette fonction permet le chargement du contenu du fichier
	 *	@return string le contenu du fichier en cas de succès, ou chaîne vide en cas d'échec
	 */
	public function getContent(){
		if (is_null($this->content)) {
			$this->content = file_get_contents($this->localFilePath);
		}

		return $this->content;
	}

	/** Cette fonction permet de retourner l'encodage du fichier
	 *
	 *	@return string le charset du fichier
	 */
	public function getCharset(){
		if (is_null($this->charset)) {
			$tmp_file_content = file_get_contents($this->localFilePath, false, null, 0, 50000);
			$this->charset = mb_detect_encoding($tmp_file_content, array(
				'UTF-8', 'ASCII',
				'ISO-8859-1', 'ISO-8859-2', 'ISO-8859-3', 'ISO-8859-4', 'ISO-8859-5',
				'ISO-8859-6', 'ISO-8859-7', 'ISO-8859-8', 'ISO-8859-9', 'ISO-8859-10',
				'ISO-8859-13', 'ISO-8859-14', 'ISO-8859-15', 'ISO-8859-16',
				'Windows-1251', 'Windows-1252', 'Windows-1254',
			 ), true);
			unset($tmp_file_content);
		}
		return $this->charset;
	}

	/** Cette fonction  permet de convertir un fichier non utf8 en utf8
	 *
	 *	@return bool true si le fichier a bien été converti
	 */
	public function convertToUTF8(){
		if ($this->getCharset() == 'UTF-8') {
			return true;
		}

		if ($this->getCharset() == 'unknown-8bit') { // Si aucun type d'encodage détecté, on éssaye d'encoder en UTF-8 à l'aide de la fonction utf8_encode
			$file_data = file_get_contents($this->localFilePath);

			$encoding = mb_detect_encoding($file_data, 'ISO-8859-1, UTF-8');

			if ($encoding == 'ISO-8859-1') {
				$utf8_file_data = utf8_encode($file_data);
				file_put_contents($this->import_dir . $this->filename, $utf8_file_data);
			} elseif ($encoding == 'UTF-8') {
				file_put_contents($this->import_dir . $this->filename, $file_data);
			}else{
				return false;
			}
		}else{
			$res = exec('iconv -f ' . $this->getCharset() . ' -t utf-8 ' . $this->localFilePath . ' -o ' . $this->import_dir . $this->filename);
		}

		unlink($this->localFilePath);
		$this->localFilePath = $this->import_dir . $this->filename;

		return true;
	}

	/**	Cette fonction est chargée de détecter automatiquement le séparateur de colonnes utilisé dans un fichier CSV.
	 * 	Elle lit pour cela la première et la seconde ligne du fichier pour déterminer le séparateur le plus probable.
	 * 	Le séparateur trouvé doit donner le même nombre de colonnes pour l'entête que pour la première ligne de données.
	 * 	Si aucune solution n'est trouvée, retourne false
	 */
	public function detectCsvColDelimiter(){

		// Délimiteurs couramment rencontrés
		$headerDelimiters = array(
			';' => 0,
			',' => 0,
			"\t" => 0,
			'|' => 0
		);
		$dataDelimiters = array(
			';' => 0,
			',' => 0,
			"\t" => 0,
			'|' => 0
		);

		// Récupère la première ligne du fichier
		$handle = $this->openFile();
		$firstLine = fgets($handle);
		$secondLine = fgets($handle);
		$this->closeFile();

		// Teste les différents délimiteurs possibles.
		// Calcule le nombre de colonnes obtenues avec chaque délimiteur sur l'entête du fichier.
		foreach ($headerDelimiters as $delimiter => &$count) {
			$count = count(str_getcsv($firstLine, $delimiter));
		}

		// Calcule le nombre de colonnes obtenues avec chaque délimiteur sur la première ligne de données
		foreach ($dataDelimiters as $delimiter => &$count) {
			$count = count(str_getcsv($secondLine, $delimiter));
		}

		// Compare les deux résultats
		$headerDelimiter = array_search(max($headerDelimiters), $headerDelimiters);
		$dataDelimiter = array_search(max($dataDelimiters), $dataDelimiters);

		// Le nombre de colonnes est identique
		if ($headerDelimiter == $dataDelimiter) {
			return $headerDelimiter;
		}else{

			// Le nombre de colonnes trouvées est différent, teste les deux délimiteurs
			// pour déterminer lequel donne le même nombre de colonnes pour l'entête et le corps

			// Délimiteur trouvé dans l'entête
			$headerDelimiter = array_search(max($dataDelimiters), $headerDelimiters);
			$countHeaders = count(str_getcsv($firstLine, $headerDelimiter));
			$countDatas = count(str_getcsv($secondLine, $headerDelimiter));
			if ($countHeaders == $countDatas) {
				return $headerDelimiter;
			}

			// Délimiteur trouvé dans les données
			$dataDelimiter = array_search(max($headerDelimiters), $dataDelimiter);
			$countHeaders = count(str_getcsv($firstLine, $dataDelimiter));
			$countDatas = count(str_getcsv($secondLine, $dataDelimiter));
			if ($countHeaders == $countDatas) {
				return $dataDelimiter;
			}
		}

		// Arrivé là, il est impossible de déterminer de façon sûre le délimiteur
		return false;
	}

	/** Cette fonction permet de lire le fichier csv
	 *    Pour lire les valeurs de retour il faut réaliser une liste avec les noms des variables désirées
	 *    ex : list($nombre_de_lignes, $colonnes_du_fichier) = readFile($col_sep, $textDelimiter);
	 *
	 *    @param string $col_sep Le caractère séparateur des colonnes du csv
	 *    @param string $textDelimiter Le caractère qui délimitera le text ex : "
	 *
	 *    @return array un tableau avec les variables suivantes :
	 *        - line_count : le nombre de lignes
	 *        - columns : un tableau avec les colonnes du fichier
	 */
	public function readFile($col_sep, $textDelimiter = '"'){
		if (trim($textDelimiter) == '') {
			$textDelimiter = '"';
		}
		$first = true;

		while ($line = fgetcsv($this->openFile(), 0, $col_sep, $textDelimiter)) {
			if ($first) {
				foreach ($line as $key => $col) {
					$this->columns[$key] = htmlspecialchars($col);
				}
				$first = false;
			}else{
				$this->lines_count++;
			}
		}
		$this->closeFile();

		return array($this->lines_count, $this->columns);
	}

	/** Cette fonction permet de récupérer le nombre de ligne d'un fichier (hors entête).
	 * 	@return int Le nombre de ligne (0 si le fichier n'existe pas ou ne contient que l'entête)
	 */
	public function getLinesCount(){
		if( !file_exists($this->localFilePath) ){
			return 0;
		}

		$fp = file( $this->localFilePath );
		$count = count($fp) - 1;
		return ( $count > 0 ? $count : 0 );
	}

	/** Cette fonction permet de récupérer le nombre de colonnes du fichier
	 *
	 *	@return int le nombre de colonnes du fichier
	 */
	public function getColumnsCount(){
		return count($this->columns);
	}

	/** Cette fonction permet de vérifier si le nombre de colonnes et de lignes est supérieur à 0
	 *	@return bool true si le fichier contient lignes et colonnes, false dans le cas contraire
	 */
	public function checkColsAndLines(){
		return ($this->lines_count > 0 && $this->getColumnsCount() > 0);
	}


	/** Cette fonction permet d'archiver un fichier d'import après son exécution
	 *
	 *	@return bool true si le changement de dossier s'est produit sans échec, false dans le cas contraire
	 */
	public function archivedFile(){
		if (!is_dir($this->import_dir . 'archives/')) {
			if (!mkdir($this->import_dir . 'archives/')) {
				return false;
			}
		}
		if (rename($this->localFilePath, $this->import_dir . 'archives/' . $this->filename)) {
			$this->localFilePath = $this->import_dir . 'archives/' . $this->filename;

			return true;
		}else{
			return false;
		}
	}
}
