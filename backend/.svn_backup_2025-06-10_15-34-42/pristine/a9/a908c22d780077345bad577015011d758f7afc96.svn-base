<?php
/**
 * \ingroup PriceWatching
 */
/**
 * \interface ReportEmailing
 */
interface ReportEmailing {
	/**
	 * @param $name Nom du concurrent
	 *
	 * @return mixed
	 */
	public function setConcurenceName( $name );

	/**
	 * @param array $changes Un tableau avec les modifications de prix enregistrer par la veille tarifaire
	 *
	 * @return mixed
	 */
	public function emailChanges( array $changes );

	/**
	 * @param array $errors Tableau avec l'ensemble des erreurs survenus lors de la veille tarifaire
	 *
	 * @return mixed
	 */
	public function emailErrors( array $errors );

	/**
	 * @param array $pending Tableau avec les identifiant de produit en attentent de veille tarifaire
	 *
	 * @return mixed
	 */
	public function emailPending( array $pending );
}