<?php

	/**	\file js_search.php
	 *
	 *	Ce fichier est chargé d'afficher la prévisualisation d'une recherche telle qu'elle doit apparaître dans le front-office.
	 *	Elle donne accès aux fonctionnalités suivantes :
	 *	 - Désactivation d'un résultat pour la requête lancée
	 *	 - Désactivation d'un résultat pour toutes les recherches
	 *	 - Réinitialisation des statistiques de clics
	 *	 - Affiche la présence éventuelle d'une redirection de recherche
	 *	
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH');
	
	if( !isset($_GET['scc']) ){
		$g_error = _("Une erreur inattendue s'est produite lors du chargement des résultats de recherche.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
	}else{
		// Ré-initialisation des statistiques de clics
		if( isset($_GET['reinit-stats'], $_GET['seg'], $_GET['scc']) ){
			if( !search_clickthroughs_reset($_GET['seg'], $_GET['scc']) ){
				$error = _("Une erreur inattendue s'est produite lors de la réinitialisation des clics pour cette recherche. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}

		// Filtre sur la section (catégorie de premier niveau)
		$section = false;
		if( isset($_GET['section']) && $_GET['section']>0 ){
			$section = $_GET['section'];
		}

		$cnt = false;
		if( isset($_GET['cnt']) && $_GET['cnt']>0 ){
			$cnt = $_GET['cnt'];
		}

		// Filtre "N'afficher que les résultats activés"
		$active = isset($_GET['active']) && $_GET['active']==0 ? false : true;

		// Recherche la présence éventuelle d'une recherche de substitution
		$redirection = search_substitut_exists( $_GET['search'] );
		if( $redirection ){
			$val_redir = search_substitut_get( $_GET['search'] );

			$scc = search_caches_exists_for_search( $_GET['seg'], $section, false, $val_redir, true, false );
			if( isset($scc['id']) && is_numeric($scc['id']) && $scc['id']>0 ){
				$_GET['scc'] = $scc['id'];
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Recherche').' '.$_GET['search'].' - '._('Statistiques de recherche'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'stat-popup-content');
	define('ADMIN_CLASS_BODY', 'popup_img');
	require_once('admin/skin/header.inc.php');
	
	if( isset($g_error) ){
		print '<div class="error">'.nl2br($g_error).'</div>';
	}else{
		// Affiche les éventuels messages d'erreur
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
		
		// Si la recherche fait l'objet d'une redirection, l'utilisateur en est informé par un cadre d'information
		if( $redirection ){
			if( trim($val_redir)!='' ){
				print '<div class="notice">';
				print _(sprintf('La recherche %s fait l\'objet d\'une redirection de recherche vers : %s.', $_GET['search'], htmlspecialchars( $val_redir )));
				print '<br />';
				print  _('Vous pouvez modifier cette redirection en utilisant le').' <a href="#" onclick="parent.addRedirectionSearch('.$_GET['seg'].',\''.urldecode($_GET['search']).'\')"> '._('formulaire de gestion des redirections').'</a>.';
				print '</div>';
			}
		}
	?>	
	
	<table class="stats-search-table">
	<tbody>
	</tbody>
	<tfoot>
		<tr><td colspan="3"></td></tr>
	</tfoot>
	
	</table>
			
	<?php
		if( $_GET['scc']>0 ){
			print '
				<hr />
				<div class="action-popup-stats-search">
					<form id="form-reinit-stats" action="/admin/stats/js_search.php" method="get">
						<input type="hidden" name="seg" id="seg" value="'.$_GET['seg'].'" />
						<input type="hidden" name="scc" id="scc" value="'.$_GET['scc'].'" />
						<input type="hidden" name="search" id="search" value="'.$_GET['search'].'" />
						<input type="hidden" name="section" id="section" value="'.$_GET['section'].'" />
						<input type="hidden" name="cnt" id="cnt" value="'.$_GET['cnt'].'" />
						<input type="hidden" name="active" id="active" value="'.$active.'" />
						<input type="submit" name="reinit-stats" id="reinit-stat" value="'._('Réinitialiser les statistiques de clics').'" />
					</form>
					<form id="form-filter" action="/admin/stats/js_search.php" method="post">
						<input type="checkbox" name="filter-active" id="filter-active" value="" '.( $active ? 'checked="checked"' : '' ).' />
						<label for="filter-active">'._('N\'afficher que les résultats activés').'</label>
					</form>
				</div>
			';
		}
	}
?>
	<script><!-- <?php 
		if( !isset($g_error) ){
			// initialisation de variables globales
			print '
				var _PageShow = 1;
				var _NbResByPage = '.$config['prd_list_length'].';
				var _Seg = '.( isset($_GET['seg']) ? $_GET['seg'] : 0 ).';
				var _Search = \''.addslashes($_GET['search']).'\';
				var _SearchUrlEncode = \''.urlencode($_GET['search']).'\';
				var _Section = '.( trim($section) ? $section : 'false' ).';
				var _Scc = '.( isset($_GET['scc']) ? $_GET['scc'] : 0 ).';
				var _Cnt = '.( $cnt>0 ? $cnt : 0 ).';
				var _NbRes = 0;
				var _ActiveNoPublish = '.( $active ? 1 : 0 ).';
				var _MaxPageShow = 0;
			';
		}
	?> --></script>

<?php
	require_once('admin/skin/footer.inc.php');
?>