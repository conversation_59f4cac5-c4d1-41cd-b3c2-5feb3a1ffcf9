<?php

/**
 *  \defgroup piece_bl Référence de la gestion commerciale 
 *  \ingroup Bl
 * 
 *  @{
 *	\page api-bl-piece-upd Mise à jour
 *
 *	Cette fonction permet de mettre à jour le numéro de pièce d'un bon de livraison
 *
 *	\code
 *		PUT	/bl/piece
 *	\endcode
 *
 *	@param id Obligatoire : identifiant du bon de livraison
 *	@param piece Obligatoire : numéro de pièce
 *
 *	@return true si la mise à jour s'est déroulé avec succès. 
 * @}
*/
switch( $method ){
	case 'upd':
		if( !isset($_REQUEST['id'], $_REQUEST['piece']) || !is_numeric($_REQUEST['id']) || !$is_sync){
			throw new Exception("Paramètre invalide.");
		}

		if( ord_bl_set_piece($_REQUEST['id'],$_REQUEST['piece']) ){
			$result = true;
		}
		break;
}