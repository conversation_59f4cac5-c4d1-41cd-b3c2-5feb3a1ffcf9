<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/admin/v1/iam.proto

namespace Google\Iam\Admin\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Iam\Admin\V1\Permission\PermissionLaunchStage instead.
     * @deprecated
     */
    class Permission_PermissionLaunchStage {}
}
class_exists(Permission\PermissionLaunchStage::class);
@trigger_error('Google\Iam\Admin\V1\Permission_PermissionLaunchStage is deprecated and will be removed in the next major release. Use Google\Iam\Admin\V1\Permission\PermissionLaunchStage instead', E_USER_DEPRECATED);

