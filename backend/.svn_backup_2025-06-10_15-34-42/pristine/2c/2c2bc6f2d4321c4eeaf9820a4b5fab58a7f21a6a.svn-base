
// amount-box
function amount_box_blur(box) {
	var id = (new RegExp('^amount-box-([0-9]+)$').exec(box.attr('id')));
	if (id && id.length > 1) id = id[1];
	else return;
	
	var v = amount_box_get_real_value(box);
	if (v == 0) v = '';
	else v = Math.min(v, parseInt($('#qte-'+id).text()));
	box.val(v);
}

function amount_box_get_real_value(box) {
	var v = box.val();
	var t;
	if ((t = new RegExp('^([0-9]+)$').exec(v)) && t.length > 1) return parseInt(t[1]);
	return 0;
}

$(document).ready(function() {
	$('td div.amount-box').each(function() {
		var children = $(this).children();
		var box = $(children[0]);
		box.blur(function() { amount_box_blur(box); });
		$(children[1]).click(function() { box.val(amount_box_get_real_value(box)+1); amount_box_blur(box); });
		$(children[2]).click(function() { box.val(amount_box_get_real_value(box)-1); amount_box_blur(box); });
	});
	
	// Parcours tous les champs de type date
	$('input.datepicker').each(function(){
		var temp = this ;
		
		// Implémente le sélecteur de date sur chacun d'entre eux.
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				var date = $(temp).val();
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					if( formated!=date )
						$(temp).DatePickerHide();
				}
			}
		});
		
	});
});

// check-all
var checkCascade = true;
var checkAllCascade = true;

var timerSel = 0;
function checkSel() {
	clearTimeout(timerSel);
	timerSel = setTimeout(function() {
		var count = 0;
		var check;
		$('#table-return-returned tbody .prd-row input[type="checkbox"]').each(function() { if ($(this).attr('checked')) { count++; check = $(this); } });
		var current_state = $('#current-state').val();
		
		if (current_state == 1) $('#return-returned-check-mode').removeAttr('disabled');
		$('#return-returned-check-motif').removeAttr('disabled');
		$('#return-returned-check-reason, #return-returned-check-message, #return-returned-check-state').removeAttr('disabled');
		
		setTimeout(function() {
			if ($('#return-returned-check-reason').attr('checked') != (count == 1)) $('#return-returned-check-reason').click();
			if ($('#return-returned-check-message').attr('checked') != (count == 1)) $('#return-returned-check-message').click();
			if ($('#return-returned-check-mode').attr('checked') != (count == 1)) $('#return-returned-check-mode').click();
			if ($('#return-returned-check-motif').attr('checked') != (count == 1)) $('#return-returned-check-motif').click();
			
			if (count == 0) $('#return-returned-check-reason, #return-returned-check-message, #return-returned-check-mode, #return-returned-check-state, #return-returned-check-motif').attr('disabled', 'disabled');
			
			if (count == 1) {
				var id = new RegExp('^return-returned-check-([0-9]+)$').exec(check.attr('id'))[1];
				var reason = $('#return-returned-reason-'+id).val();
				
				$('#reason-returned-'+((reason !== '') ? reason : 'other')).attr('checked', 'checked');
				$('#return-returned-message').val($('#return-returned-message-'+id).text());
				$('#mode-returned-'+$('#return-returned-mode-'+id).val()).attr('checked', 'checked');
				// $('#return-returned-state').val($('#return-returned-state-'+id).val());
				$('#return-returned-motif').val($('#return-returned-motif-'+id).text());
			}
			
			$('#return-returned-state')[count ? 'removeAttr' : 'attr']('disabled', 'disabled');
			$('#return-returned-submit-state')[count ? 'removeAttr' : 'attr']('disabled', 'disabled');
			
			$('#return-returned-up')[(count && current_state != 2 && current_state != 6) ? 'removeAttr' : 'attr']('disabled', 'disabled');
			$('#return-returned-advanced')[(count) ? 'removeAttr' : 'attr']('disabled', 'disabled');
			$('#return-returned-del')[(count && current_state == 1) ? 'removeAttr' : 'attr']('disabled', 'disabled');
		}, 50);
	}, 1);
}

function checkAll() {
	if (! checkAllCascade) return false;
	checkAllCascade = false;
	var t = true;
	$('#table-return-returned tbody .prd-row input[type="checkbox"]').each(function() { t = t && $(this).attr('checked'); });
	if ($('#return-returned-check-all').attr('checked') != t) $('#return-returned-check-all').click();
	checkAllCascade = true;
	checkSel();
	return true;
}

function getGET() {
	var get = {};
	var l = String(document.location);
	var p = l.indexOf('?');
	if (p > -1) {
		l = l.substring(p+1, l.length).split('&');
		var i;
		for (i=0; i<l.length; i++) {
			l[i] = l[i].split('=');
			get[l[i][0]] = l[i][1];
		}
	}
	return get;
}

$(document).ready(function() {
	// check-all
	$('#table-return-returned tbody .prd-row input[type="checkbox"]').each(function() {
		$(this).click(function() { return checkAll(); });
	});
	$('#return-returned-check-all').click(function() {
		if (! checkCascade) return false;
		checkCascade = false;
		var checked = $(this).attr('checked');
		$('#table-return-returned tbody .prd-row input[type="checkbox"]').each(function() { if ($(this).attr('checked') != checked) $(this).click(); });
		checkCascade = true;
		checkSel();
		return true;
	});
	
	// delete
	$('#return-returned-del').click(function() {
		return window.confirm(returnConfirmSuppressionProduitBonRetour);
	});
	
	// disable/enable
	$('#return-returned-check-reason').click(function() {
		var o = $(this);
		setTimeout(function() { var check = o.attr('checked'); $('#return-returned-reasons input[type="radio"]').each(function() { $(this)[check ? 'removeAttr' : 'attr']('disabled', 'disabled'); }); }, 1);
	});
	$('#return-returned-check-message').click(function() { var o = $(this); setTimeout(function() { $('#return-returned-message')[o.attr('checked') ? 'removeAttr' : 'attr']('disabled', 'disabled'); if (o.attr('checked')) $('#return-returned-message').focus(); }, 1); });
	$('#return-returned-check-mode').click(function() {
		var o = $(this);
		setTimeout(function() { var check = o.attr('checked'); $('#return-returned-modes input[type="radio"]').each(function() { $(this)[check ? 'removeAttr' : 'attr']('disabled', 'disabled'); }); }, 1);
	});
	$('#return-returned-check-state').click(function() {
		var o = $(this);
		setTimeout(function() {
			$('#return-returned-state')[o.attr('checked') ? 'removeAttr' : 'attr']('disabled', 'disabled');
		}, 1);
	});
	
	$('#form-return').submit(function() {
		var state = $('#return-state').val();
		if (state == $('#current-state').val()) return true;
		return true;
	});
	
	$('#return-returned-submit-state').click(function() {
		// récupère les produits sélectionnés
		var liste = '';
		$('#table-return-returned tbody .prd-row input[type="checkbox"]').each(function() {
			if (! $(this).attr('checked')) return;
			var id = (new RegExp('^return-returned-check-([0-9]+)$')).exec($(this).attr('id'))[1];
			liste += ((liste != '') ? ',' : '') + id;
		});
		
		var motif = $('<input name="motif" value="'+$('#return-returned-motif').val()+'" />').serialize();
		var qte = $('#return-returned-qte').length ? $('#return-returned-qte').val() : 0;
		location.href = 'return.php?ret='+$('#return-id').val()+'&return-submit-state=&liste='+liste+'&state='+$('#return-returned-state').val()+'&'+motif+'&qte='+qte;
	});
		
	$('#return-advanced').fancybox({
		'width': 800,
		'height': 600,
		'padding': 10,
		'onClosed': function() {
			location.href = 'return.php?ref='+getGET().ref;
		}
	});
	
	checkSel();
});