<?php
	/** \file export-beezup-catalog.php
	 *	\ingroup crontabs ctr_comparators
	 *
	 * 	Ce script est destiné à générer le flux catalogue pour BeezUp.
	 */

	if (!isset($ar_params)) {
		print "L'exécution de ce script nécessite l'appel de execute-script.php.".PHP_EOL;
		exit;
	}

	require_once( 'sitemaps.inc.php' );
	require_once( 'comparators.inc.php' );
	require_once( 'Services/Template.class.php' );
	require_once( 'Services/Catalog/Product.class.php' );
	require_once( 'Services/Cart/Cart.class.php' );
	require_once( 'Services/Cart/Delivery.class.php' );
	require_once( 'Services/Customer/Customer.class.php' );

	foreach( $configs as $config ){
        if (!empty($ar_params['wst_id']) && $ar_params['wst_id'] != $config['wst_id']) {
            continue;
        }
		Sitemap::resetClassLists();

		$start = microtime(true);

		// lien utilisé pour la création du fichier d'export du catalogue
		$dirname = $config['ctr_dir'].'/'.md5( $config['tnt_id'].$config['date-created'] ).'/';
		$file = $dirname.'tmp-beezup.xml';
		$url_file = $config['site_url'].'/shopbots/'.md5( $config['tnt_id'].$config['date-created'] ).'/beezup.xml';

		// Vérifie que le comparateur Google Shopping est bien activé pour ce client.
		if( !ctr_comparators_actived(CTR_BEEZUP) ){
			if( file_exists($file) ){
				unlink( $file );
			}

			continue;
		}

		// si le dossier contenant les fichiers n'existe pas, on le créé avec les droits www-data
		if( !file_exists($dirname) ){
			mkdir( $dirname, 0755 );
			chgrp( $dirname, 'www-data' );
			chown( $dirname, 'www-data' );
		}

		// Chargement de la configuration du flux produit
		$cfg_flow = [];
		$temp = ctr_params_get_value( CTR_BEEZUP, 'cfg_flow_beezup' );
		if( trim($temp) != '' ){
			$cfg_flow = json_decode( $temp, true );
		}

		// création des urls vers le fichier d'export
		ctr_comparators_file_update( CTR_BEEZUP, $url_file );

		// création du fichier
		$f = fopen( $file, 'w' );

		fwrite( $f, '<?xml version="1.0" encoding="UTF-8"?>'."\n" );
		fwrite( $f, '<catalogue lang="FR">'."\n" );

		// Récupération des catégories ayant été activées pour BeezUp
		$ar_cat_ids = ctr_prd_categories_get_array( CTR_BEEZUP, $config['wst_id'] );

		$filter_prd = [
			// Inclure ou non seulement les articles en stocks
			'have_stock' => isset($cfg_flow['prd_stock']) && $cfg_flow['prd_stock'] == '1' ? true : null,

			// Récupère le stock d'un dépôt précis
			'dps' => isset($cfg_flow['dps_id']) ? $cfg_flow['dps_id'] : 0,

			// Inclusion des articles enfants
			'childs' => true,

			// Inclusion des articles seulements commandable
			'orderable' => isset($cfg_flow['prd_parent']) && $cfg_flow['prd_parent'] ? null : true,
		];


		// Charge les produits avec les tarifs de bases
		$r_product = prd_products_get_simple( 0, '', false, $ar_cat_ids, true, false, false, false, $filter_prd );

		if( $r_product ){
			while( $product = ria_mysql_fetch_assoc($r_product) ){
				add_product( $f, $product, $cfg_flow );
			}
		}

		fwrite( $f, '</catalogue>'."\n" );

		rename( $dirname.'tmp-beezup.xml', $dirname.'beezup.xml' );
		@unlink( $dirname.'tmp-beezup.xml' );
	}

	/** Cette fonction permet de générer le XML du catalogue produit pour BeezUp.
	 *  @param File $file Obligatoire, emplacement du fichier XML (doit déjà être initialisé via fopen)
	 *  @param array $product Obligatoire, données liées au produit tel que retourner par prd_products_get_simple()
	 *  @param array $cfg_flow Obligatoire, configuration du flux
	 *  @return bool true en cas de succès d'écriture du XML, false dans le cas contraire
	 */
	function add_product( $file, $product, $cfg_flow ){
		global $config;

		// Les produits sans ean ne seront pas exportés
		// Sauf pour certain tenant :
		// 	- 14 : Coopcorico - Cavac - Terre de Viande
		if( !in_array($config['tnt_id'], [14]) ){
			if( trim($product['barcode']) == '' ){
				return false;
			}
		}

		// Contrôle que le produit est bien activé
		if( !ctr_catalogs_exists(CTR_BEEZUP, $product['id']) ){
			return false;
		}

		{ // Transformation sur certaines données du produit

			// Sur la description longue
			$product['desc-long'] = html_revert_wysiwyg( $product['desc-long'], true );

			// Quantité en stock
			if( !is_numeric($product['stock']) ){
				$product['stock'] = 0;
			}

			// Info de stock
			$product['info_stock'] = 'En Stock';
			if( $product['stock'] <= 0 ){
				$product['info_stock'] = 'En Rupture';
			}

			// Durée de grantie
			// Dans RiaShop celle-ci est en mois, dans le flux elle est en année
			$product['garantie'] = $product['garantie'] / 12;

			// Chargement des tarifs
			$product['price_ttc'] = 0;
			try{
				$Product = new ProductService( [
					'prd' => $product['id'],
					'usechildsprice' => $config['tnt_id'] == 1 ? false : true
				] );
				$Product->general();
				$data = $Product->getData();

				$product['tva_rate'] = $data['tvarate'];
				$product['is_promo'] = $data['ispromo'];
				$product['price_ttc'] = $data['pricettc'];
				$product['orig_price_ttc'] = $data['origpricettc'];

				$product['is_parent'] = $Product->getIsParent();
			}catch(Exception $e ){
				// pas de blocage, on passe au produit suivant
			}
			if( !is_numeric($product['price_ttc']) || $product['price_ttc'] <= 0 ){
				return false;
			}

			// Surcharge du titre pour BeezUP
			$ovr_title = ctr_catalogs_get_prd_title( CTR_BEEZUP, $product['id'], false );
			if( trim($ovr_title) != '' ){
				$product['title'] = $ovr_title;
			}

			// Surcharge de la description pour BeezUP
			$ovr_desc = ctr_catalogs_get_prd_desc( CTR_BEEZUP, $product['id'], false );
			if( trim($ovr_desc) != '' ){
				$product['desc-long'] = $ovr_desc;
			}

			// Surcharge de l'image principale
			$r_ovr_image = ctr_catalogs_images_get( $product['id'], CTR_BEEZUP );
			if( $r_ovr_image && ria_mysql_num_rows($r_ovr_image) ){
				$ovr_image = ria_mysql_fetch_assoc( $r_ovr_image );
				$product['img_id'] = $ovr_image['id'];
			}

			// URL de l'image principale
			$thumbs = $config['img_sizes'][ $cfg_flow['cfg_img'] ];
			$img_url = '';
			if( $product['img_id'] > 0 ){
				$img_url = $config['img_url'].'/'.$thumbs['width'].'x'.$thumbs['height'].'/'.$product['img_id'].'.'.$thumbs['format'];
			}
		}

		{ // Classement du produit en interne
			$ar_cats = [];

			$rhy = prd_products_categories_get( $product['id'], true, false, null, $config['cat_root'] );
			if( $rhy && ria_mysql_num_rows($rhy) ){
				$hy = ria_mysql_fetch_assoc( $rhy );

				// récupère la hiérarchie complète
				$rparent = prd_categories_parents_get( $hy['cat'] );
				if( $rparent && ria_mysql_num_rows($rparent) ){
					$tmp_hy = '';
					while( $parent = ria_mysql_fetch_array($rparent) ){
						if( $parent['id'] == $config['cat_root'] ){
							continue;
						}

						$ar_cats[ $parent['id'] ] = $parent['title'];
					}

					$ar_cats[ $hy['cat'] ] = $hy['title'];
				}else{
					$ar_cats[ $hy['cat'] ] = $hy['title'];
				}
			}
		}

		if( $cfg_flow['carrier_default'] > 0 ){ // Informations de livraison
			$Delivery = DeliveryService::getInstance()->reload();
			$Delivery->setDefaultLocation([
				'zipcode' => '',
				'city' => '',
				'cntcode' => $cfg_flow['zone_default'],
				'country' => strtoupper( sys_countries_get_name( $cfg_flow['zone_default']) ),
			]);
			$Delivery->getPort( [], [$product['id']], true );
			$ar_delivery = $Delivery->getData();

			if( !array_key_exists('srv'.$cfg_flow['carrier_default'], $ar_delivery) ){
				return false;
			}

			$ar_delivery = $ar_delivery[ 'srv'.$cfg_flow['carrier_default'] ];
		}

		{ // Recherche d'une surcharge de config soit sur le produit soit sur son arborescence de catégorie
			$surcharge_cfg_dlvdelay = $surcharge_cfg_dlvcost = '';

			$cfg_prd = ctr_catalogs_get_params( CTR_BEEZUP, $product['id'] );
			if( is_array($cfg_prd) ){
				if( isset($cfg_prd['dlvdelay']) && trim($cfg_prd['dlvdelay']) != '' ){
					$surcharge_cfg_dlvdelay = $cfg_prd['dlvdelay'];
				}

				if( isset($cfg_prd['dlvcost']) && trim($cfg_prd['dlvcost']) != '' ){
					$surcharge_cfg_dlvcost = $cfg_prd['dlvcost'];
				}
			}

			// Si une des deux surcharges est vide alors on regarde s'il en existe pas une sur son classments
			// Plus la catégorie est profonde dans l'arborscence plus la priorité est élevée
			$temp_ar_cats = []; // Pour ne pas changer l'ordre dans le tableau original
			foreach( $ar_cats as $k=>$v ){
				array_unshift( $temp_ar_cats, $k );
			}

			foreach( $temp_ar_cats as $cat_id ){
				$temp = ctr_prd_categories_get_params( CTR_BEEZUP, $cat_id );
				$cfg_cat = json_decode( $temp, true );

				if( isset($cfg_cat['dlvdelay']) && trim($cfg_cat['dlvdelay']) != '' && trim($surcharge_cfg_dlvdelay) == '' ){
					$surcharge_cfg_dlvdelay = $cfg_cat['dlvdelay'];
				}

				if( isset($cfg_cat['dlvcost']) && trim($cfg_cat['dlvcost']) != '' && trim($surcharge_cfg_dlvcost) == '' ){
					$surcharge_cfg_dlvcost = $cfg_cat['dlvcost'];
				}

				// On ne va pas plus loin dès lors qu'une surcharge est trouvé pour toutes les données
				if( trim($surcharge_cfg_dlvdelay) != '' && trim($surcharge_cfg_dlvcost) != '' ){
					break;
				}
			}
		}

		$ar_prd_links = $ar_prd_docs = $ar_imgs_second = [];

		if( isset($cfg_flow['prd_links']) && $cfg_flow['prd_links'] ){ // Article(s) lié(s)
			$r_prd_rel = prd_relations_get( $product['id'], null, null, true );
			if( $r_prd_rel ){
				while( $prd_rel = ria_mysql_fetch_assoc($r_prd_rel) ){
					$ar_prd_links[] = $prd_rel['dst_ref'];
				}
			}
		}

		if( isset($cfg_flow['prd_docs']) && $cfg_flow['prd_docs'] ){ // Documents liés au produit
			if( isset($config['site_url_download']) && trim($config['site_url_download']) != '' ){
				$r_doc = doc_products_get( 0, $product['id'], 0, [], $config['wst_id'] );

				if( $r_doc ){
					while( $doc = ria_mysql_fetch_assoc($r_doc) ){
						$ar_prd_docs[] = str_replace( '#doc_id#', $doc['doc_id'], $config['site_url_download'] );
					}
				}
			}
		}

		if( isset($cfg_flow['prd_docs']) && $cfg_flow['prd_docs'] ){ // Image(s) secondaire(s)
			$r_img = prd_images_get( $product['id'] );
			if( $r_img ){
				while( $img = ria_mysql_fetch_assoc($r_img) ){
					$thumbs = $config['img_sizes'][ $cfg_flow['cfg_img'] ];
					$ar_imgs_second[] = $config['img_url'].'/'.$thumbs['width'].'x'.$thumbs['height'].'/'.$img['id'].'.'.$thumbs['format'];
				}
			}
		}

		$parent_id = false;
		$r_parent = prd_hierarchy_get( 0, $product['id'] );
		if( $r_parent && ria_mysql_num_rows($r_parent) ){
			$parent = ria_mysql_fetch_assoc( $r_parent );
			$parent_id = $parent['parent'];
		}

		fwrite( $file, '<produit>'."\n" );

		$i = 1;
		foreach( $ar_cats as $cat_name ){
			fwrite( $file, '	<categorie'.$i.'><![CDATA['.$cat_name.']]></categorie'.$i.'>'."\n" );
			$i++;
		}

		fwrite( $file, '	<identifiant_unique><![CDATA['.$product['ref'].']]></identifiant_unique>'."\n" );
		fwrite( $file, '	<titre><![CDATA['.$product['title'].']]></titre>'."\n" );
		fwrite( $file, '	<ean><![CDATA['.$product['barcode'].']]></ean>'."\n" );
		fwrite( $file, '	<ecotaxe currency="EUR">'.number_format( $product['ecotaxe'] * $product['tva_rate'], 2, ',', ' ' ).'</ecotaxe>'."\n" );
		fwrite( $file, '	<url_produit><![CDATA['.$config['site_url'].$product['url_alias'].']]></url_produit>'."\n" );
		fwrite( $file, '	<description><![CDATA['.$product['desc-long'].']]></description>'."\n" );
		fwrite( $file, '	<stock>'.$product['stock'].'</stock>'."\n" );
		fwrite( $file, '	<disponibilite>'.$product['info_stock'].'</disponibilite>'."\n" );
		fwrite( $file, '	<garantie>'.$product['garantie'].'</garantie>'."\n" );
		fwrite( $file, '	<poids_brut unit="g">'.$product['weight'].'</poids_brut>'."\n" );
		fwrite( $file, '	<poids_net unit="g">'.$product['weight_net'].'</poids_net>'."\n" );
		fwrite( $file, '	<longueur unit="cm">'.$product['length'].'</longueur>'."\n" );
		fwrite( $file, '	<largeur unit="cm">'.$product['width'].'</largeur>'."\n" );
		fwrite( $file, '	<hauteur unit="cm">'.$product['height'].'</hauteur>'."\n" );
		fwrite( $file, '	<nouveau>'.( $product['new'] ? 'Oui' : 'Non' ).'</nouveau>'."\n" );
		fwrite( $file, '	<parent>'.( $product['is_parent'] ? 'Oui' : 'Non' ).'</parent>'."\n" );
		fwrite( $file, '	<commandable>'.( $product['orderable'] ? 'Oui' : 'Non' ).'</commandable>'."\n" );
		fwrite( $file, '	<suivi_en_stock>'.( $product['follow_stock'] ? 'Oui' : 'Non' ).'</suivi_en_stock>'."\n" );

		if( count($ar_prd_links) > 0 ){
			fwrite( $file, '	<articles_lies><![CDATA['.implode(',', $ar_prd_links).']]></articles_lies>'."\n" );
		}

		if( count($ar_prd_docs) > 0 ){
			fwrite( $file, '	<documents><![CDATA['.implode(',', $ar_prd_docs).']]></documents>'."\n" );
		}

		if( count($ar_imgs_second) > 0 ){
			fwrite( $file, '	<urls_images_secondaires><![CDATA['.implode(',', $ar_imgs_second).']]></urls_images_secondaires>'."\n" );
		}

		// Identifiant du produit parent
		if( is_numeric($parent_id) && $parent_id > 0 ){
			fwrite( $file, '	<groupe_id>'.$parent_id.'</groupe_id>'."\n" );
		}

		// Information de tarifs
		if( $product['is_promo'] ){
			fwrite( $file, '	<prix currency="EUR">'.number_format( $product['price_ttc'], 2, ',', ' ' ).'</prix>'."\n" );
			fwrite( $file, '	<prixBarre currency="EUR">'.number_format( $product['orig_price_ttc'], 2, ',', ' ' ).'</prixBarre>'."\n" );
		}else{
			fwrite( $file, '	<prix currency="EUR">'.number_format( $product['price_ttc'], 2, ',', ' ' ).'</prix>'."\n" );
		}

		// Information de livraison
		if( $cfg_flow['carrier_default'] > 0 ){
			fwrite( $file, '	<delai_de_livraison>'.$ar_delivery['zone']['dlv_min'].'-'.$ar_delivery['zone']['dlv_max'].'</delai_de_livraison>'."\n" );
			if( $ar_delivery['zone']['franco_value_min'] > 0 && $product['price_ttc'] > $ar_delivery['zone']['franco_value_min'] ){
				fwrite( $file, '	<frais_de_port currency="EUR">0</frais_de_port>'."\n" );
			}else{
				fwrite( $file, '	<frais_de_port currency="EUR">'.number_format( ($ar_delivery['port'] * _TVA_RATE_DEFAULT), 2, ',', ' ' ).'</frais_de_port>'."\n" );
			}
		}else{
			if( trim($surcharge_cfg_dlvdelay) != '' ){
				fwrite( $file, '	<delai_de_livraison>'.$surcharge_cfg_dlvdelay.'</delai_de_livraison>'."\n" );
			}else{
				fwrite( $file, '	<delai_de_livraison>'.$cfg_flow['dlvdelay'].'</delai_de_livraison>'."\n" );
			}

			if( trim($surcharge_cfg_dlvcost) != '' ){
				fwrite( $file, '	<frais_de_port currency="EUR">'.number_format( $surcharge_cfg_dlvcost, 2, ',', ' ' ).'</frais_de_port>'."\n" );
			}else{
				fwrite( $file, '	<frais_de_port currency="EUR">'.number_format( $cfg_flow['dlvcost'], 2, ',', ' ' ).'</frais_de_port>'."\n" );
			}
		}

		fwrite( $file, '	<marque><![CDATA['.$product['brd_title'].']]></marque>'."\n" );

		if( trim($img_url) != '' ){
			fwrite( $file, '	<url_image><![CDATA['.$img_url.']]></url_image>'."\n" );
		}

		// Gestion des champs avancés
		if( count($cfg_flow['fields']) > 0 ){
			$r_field = fld_fields_get( $cfg_flow['fields'], 0, 0, 0, 0, $product['id'], null, [], false, [], null, CLS_PRODUCT );
			while( $field = ria_mysql_fetch_assoc($r_field) ){
					$code = str_replace('-', '_', urlalias($field['name']) );
					fwrite( $file, '	<fld_'.$code.'><![CDATA['.$field['obj_value'].']]></fld_'.$code.'>'."\n" );
			}
		}

		fwrite( $file, '</produit>'."\n" );
	}