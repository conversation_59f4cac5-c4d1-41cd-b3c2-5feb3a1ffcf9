<?php

	/**	\file ajax-order-products-position-update.php
	 *	Ce fichier permet la mise à jour de la position d'une ligne de produit dans une commande.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source, sous la forme "id-produit,id-ligne"
	 *	- target : identifiant de l'objet cible, sous la forme "id-produit,id-ligne"
	 *	- action : soit "before" soit "after"
	 *	- ord : identifiant de la commande à mettre à jour
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

	if( !isset($_POST['source'], $_POST['target'], $_POST['action'],$_GET['ord']) ){
		throw new Exception(_("Une erreur inattendue s'est produite lors de l'enregistrement.\nVeuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur."));
	}
	
    $ord_id = $_GET['ord'];
	
	$source = explode(',', $_POST['source']);
	$source = array('id' => $source[0], 'line' => $source[1]);
	$target = explode(',', $_POST['target']);
    $target = array('id' => $target[0], 'line' => $target[1]);
    
    $action = $_POST['action'];
	
	require_once('orders.inc.php');
	
	$response = array('success' => ord_products_position_update($ord_id, $source, $target, $action));
	
	print json_encode($response);
	exit;

