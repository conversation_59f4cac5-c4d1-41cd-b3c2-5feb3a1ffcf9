<?php
/**
 * \defgroup api-stocks-stats Stocks
 * \ingroup stats
 * @{
 * \page api-stocks-stats-get Chargement
 *
 * Cet appel retourne des statistiques sur les stocks 
 * 
 *		\code
 *			GET /stocks/stats/
 *		\endcode
 *
 * @param int $dps_id Facultatif, Identifiant (ou tableau d'identifiants) de dépôts de stockage sur lesquelles filtrer le résultat
 *
 * @return json Un résultat au format Json contenant les clés suivantes :
 *	\code{.json}
 *     {
 * 		"count" : Nombre de références uniques
 * 		"units" : Quantité physique (en unités) actuellement en stock
 * 		"selled" : Quantité déjà vendue en attente de préparation
 * 		"leaving" :  Quantité en cours de préparation
 * 		"incoming" : Quantité commandée en attente de réception
 * 		"purchase_value" : Valeur du stock, en prix d'achat
 *     }
 *	\endcode
 * @}
*/

require_once('http.inc.php');
require_once('scm/stats.inc.php');

if( $method=='get' ){

	// Le résultat est mis en cache pour 15 minutes
	http_cache_control( 900 );

	// Filtres sur la période
	/*if(
		!isset($_GET['dateStart']) || !trim($_GET['dateStart']) || !isdate($_GET['dateStart'])
		|| !isset($_GET['dateStop']) || !trim($_GET['dateStop']) || !isdate($_GET['dateStop'])
	){
		throw new Exception("Les dates de début et de fin sont obligatoires (dateStart et dateStop)");
	}*/

	// Filtre sur le dépôt
	$dps_id = 0;
	if( isset($_GET['dps']) && is_numeric($_GET['dps']) ){
		$dps_id = $_GET['dps'];
	}

	// Calcule les statistiques sur les stocks
	$content = scm_stocks_quantities_stats_get( $dps_id );
	$result = is_array($content) && $content['count']!==false;

}
