<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 61.1 (89650) - https://sketch.com -->
    <title>icons / up / hover</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="16" cy="16" r="16"></circle>
        <filter x="-26.6%" y="-20.3%" width="153.1%" height="153.1%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.195093969 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="icons-/-up-/-hover" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icons-/-plus-/-plus_electric_blue_shadow-copy">
            <g id="Oval-Copy-4">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use fill="#5377FB" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
            <line x1="14.0327079" y1="12.9441718" x2="14.0327079" y2="18.2558282" id="Path-955" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" transform="translate(14.032708, 15.600000) scale(1, -1) rotate(-225.000000) translate(-14.032708, -15.600000) "></line>
            <line x1="17.7886162" y1="12.9441718" x2="17.7886162" y2="18.2558282" id="Path-955-Copy" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" transform="translate(17.788616, 15.600000) scale(1, -1) rotate(-495.000000) translate(-17.788616, -15.600000) "></line>
        </g>
    </g>
</svg>