<?php
    /** \file get-all-tenants.php
     * 	Ce script est chargé de récupérer tous les identifiants de tenants présents sur le serveur.
     *  Il affichera les identifiants les uns en dessous des autres.
     */

    set_include_path(dirname(__FILE__) . '/../include/');
    require_once('RegisterGCP.inc.php');
    require_once('db.inc.php');
    
    if (RegisterGCP::onGcloud()) {
	    $ar_connections = RegisterGCP::create()->getConnections();
	    foreach ($ar_connections as $connection) {
	    	print $connection['mysql_server'].PHP_EOL;
	    }
    }
