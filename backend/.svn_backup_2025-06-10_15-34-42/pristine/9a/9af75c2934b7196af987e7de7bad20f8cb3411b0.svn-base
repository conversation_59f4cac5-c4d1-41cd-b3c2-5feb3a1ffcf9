<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/schema.proto

namespace Google\Cloud\PubSub\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Request for the `ListSchemas` method.
 *
 * Generated from protobuf message <code>google.pubsub.v1.ListSchemasRequest</code>
 */
class ListSchemasRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. The name of the project in which to list schemas.
     * Format is `projects/{project-id}`.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     */
    private $parent = '';
    /**
     * The set of Schema fields to return in the response. If not set, returns
     * Schemas with `name` and `type`, but not `definition`. Set to `FULL` to
     * retrieve all fields.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.SchemaView view = 2;</code>
     */
    private $view = 0;
    /**
     * Maximum number of schemas to return.
     *
     * Generated from protobuf field <code>int32 page_size = 3;</code>
     */
    private $page_size = 0;
    /**
     * The value returned by the last `ListSchemasResponse`; indicates that
     * this is a continuation of a prior `ListSchemas` call, and that the
     * system should return the next page of data.
     *
     * Generated from protobuf field <code>string page_token = 4;</code>
     */
    private $page_token = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $parent
     *           Required. The name of the project in which to list schemas.
     *           Format is `projects/{project-id}`.
     *     @type int $view
     *           The set of Schema fields to return in the response. If not set, returns
     *           Schemas with `name` and `type`, but not `definition`. Set to `FULL` to
     *           retrieve all fields.
     *     @type int $page_size
     *           Maximum number of schemas to return.
     *     @type string $page_token
     *           The value returned by the last `ListSchemasResponse`; indicates that
     *           this is a continuation of a prior `ListSchemas` call, and that the
     *           system should return the next page of data.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Pubsub\V1\Schema::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. The name of the project in which to list schemas.
     * Format is `projects/{project-id}`.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getParent()
    {
        return $this->parent;
    }

    /**
     * Required. The name of the project in which to list schemas.
     * Format is `projects/{project-id}`.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setParent($var)
    {
        GPBUtil::checkString($var, True);
        $this->parent = $var;

        return $this;
    }

    /**
     * The set of Schema fields to return in the response. If not set, returns
     * Schemas with `name` and `type`, but not `definition`. Set to `FULL` to
     * retrieve all fields.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.SchemaView view = 2;</code>
     * @return int
     */
    public function getView()
    {
        return $this->view;
    }

    /**
     * The set of Schema fields to return in the response. If not set, returns
     * Schemas with `name` and `type`, but not `definition`. Set to `FULL` to
     * retrieve all fields.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.SchemaView view = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setView($var)
    {
        GPBUtil::checkEnum($var, \Google\Cloud\PubSub\V1\SchemaView::class);
        $this->view = $var;

        return $this;
    }

    /**
     * Maximum number of schemas to return.
     *
     * Generated from protobuf field <code>int32 page_size = 3;</code>
     * @return int
     */
    public function getPageSize()
    {
        return $this->page_size;
    }

    /**
     * Maximum number of schemas to return.
     *
     * Generated from protobuf field <code>int32 page_size = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setPageSize($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_size = $var;

        return $this;
    }

    /**
     * The value returned by the last `ListSchemasResponse`; indicates that
     * this is a continuation of a prior `ListSchemas` call, and that the
     * system should return the next page of data.
     *
     * Generated from protobuf field <code>string page_token = 4;</code>
     * @return string
     */
    public function getPageToken()
    {
        return $this->page_token;
    }

    /**
     * The value returned by the last `ListSchemasResponse`; indicates that
     * this is a continuation of a prior `ListSchemas` call, and that the
     * system should return the next page of data.
     *
     * Generated from protobuf field <code>string page_token = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setPageToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->page_token = $var;

        return $this;
    }

}

