<?php
/**
 * AnalyticsStoreIndexLinks
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * AnalyticsStoreIndexLinks Class Doc Comment
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class AnalyticsStoreIndexLinks implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'analyticsStoreIndexLinks';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'self' => '\Swagger\Client\Model\LinksAnalyticsIndexLink',
        'tracking_status' => '\Swagger\Client\Model\LinksGetStoreTrackingStatusLink',
        'tracked_clicks' => '\Swagger\Client\Model\LinksGetStoreTrackedClicksLink',
        'tracked_orders' => '\Swagger\Client\Model\LinksGetStoreTrackedOrdersLink',
        'tracked_external_orders' => '\Swagger\Client\Model\LinksGetStoreTrackedExternalOrdersLink',
        'report_by_day' => '\Swagger\Client\Model\LinksGetStoreReportByDayLink',
        'report_by_channel' => '\Swagger\Client\Model\LinksGetStoreReportByChannelLink',
        'report_by_category' => '\Swagger\Client\Model\LinksGetStoreReportByCategoryLink',
        'report_by_product' => '\Swagger\Client\Model\LinksGetStoreReportByProductLink',
        'optimise_all' => '\Swagger\Client\Model\LinksOptimiseAllLink',
        'optimise' => '\Swagger\Client\Model\LinksOptimiseLink',
        'optimise_by_channel' => '\Swagger\Client\Model\LinksOptimiseByChannelLink',
        'optimise_by_category' => '\Swagger\Client\Model\LinksOptimiseByCategoryLink',
        'optimise_by_product' => '\Swagger\Client\Model\LinksOptimiseByProductLink',
        'report_filters' => '\Swagger\Client\Model\LinksGetReportFiltersLink',
        'rules' => '\Swagger\Client\Model\LinksGetRulesLink'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'self' => null,
        'tracking_status' => null,
        'tracked_clicks' => null,
        'tracked_orders' => null,
        'tracked_external_orders' => null,
        'report_by_day' => null,
        'report_by_channel' => null,
        'report_by_category' => null,
        'report_by_product' => null,
        'optimise_all' => null,
        'optimise' => null,
        'optimise_by_channel' => null,
        'optimise_by_category' => null,
        'optimise_by_product' => null,
        'report_filters' => null,
        'rules' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'self' => 'self',
        'tracking_status' => 'trackingStatus',
        'tracked_clicks' => 'trackedClicks',
        'tracked_orders' => 'trackedOrders',
        'tracked_external_orders' => 'trackedExternalOrders',
        'report_by_day' => 'reportByDay',
        'report_by_channel' => 'reportByChannel',
        'report_by_category' => 'reportByCategory',
        'report_by_product' => 'reportByProduct',
        'optimise_all' => 'optimiseAll',
        'optimise' => 'optimise',
        'optimise_by_channel' => 'optimiseByChannel',
        'optimise_by_category' => 'optimiseByCategory',
        'optimise_by_product' => 'optimiseByProduct',
        'report_filters' => 'reportFilters',
        'rules' => 'rules'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'self' => 'setSelf',
        'tracking_status' => 'setTrackingStatus',
        'tracked_clicks' => 'setTrackedClicks',
        'tracked_orders' => 'setTrackedOrders',
        'tracked_external_orders' => 'setTrackedExternalOrders',
        'report_by_day' => 'setReportByDay',
        'report_by_channel' => 'setReportByChannel',
        'report_by_category' => 'setReportByCategory',
        'report_by_product' => 'setReportByProduct',
        'optimise_all' => 'setOptimiseAll',
        'optimise' => 'setOptimise',
        'optimise_by_channel' => 'setOptimiseByChannel',
        'optimise_by_category' => 'setOptimiseByCategory',
        'optimise_by_product' => 'setOptimiseByProduct',
        'report_filters' => 'setReportFilters',
        'rules' => 'setRules'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'self' => 'getSelf',
        'tracking_status' => 'getTrackingStatus',
        'tracked_clicks' => 'getTrackedClicks',
        'tracked_orders' => 'getTrackedOrders',
        'tracked_external_orders' => 'getTrackedExternalOrders',
        'report_by_day' => 'getReportByDay',
        'report_by_channel' => 'getReportByChannel',
        'report_by_category' => 'getReportByCategory',
        'report_by_product' => 'getReportByProduct',
        'optimise_all' => 'getOptimiseAll',
        'optimise' => 'getOptimise',
        'optimise_by_channel' => 'getOptimiseByChannel',
        'optimise_by_category' => 'getOptimiseByCategory',
        'optimise_by_product' => 'getOptimiseByProduct',
        'report_filters' => 'getReportFilters',
        'rules' => 'getRules'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['self'] = isset($data['self']) ? $data['self'] : null;
        $this->container['tracking_status'] = isset($data['tracking_status']) ? $data['tracking_status'] : null;
        $this->container['tracked_clicks'] = isset($data['tracked_clicks']) ? $data['tracked_clicks'] : null;
        $this->container['tracked_orders'] = isset($data['tracked_orders']) ? $data['tracked_orders'] : null;
        $this->container['tracked_external_orders'] = isset($data['tracked_external_orders']) ? $data['tracked_external_orders'] : null;
        $this->container['report_by_day'] = isset($data['report_by_day']) ? $data['report_by_day'] : null;
        $this->container['report_by_channel'] = isset($data['report_by_channel']) ? $data['report_by_channel'] : null;
        $this->container['report_by_category'] = isset($data['report_by_category']) ? $data['report_by_category'] : null;
        $this->container['report_by_product'] = isset($data['report_by_product']) ? $data['report_by_product'] : null;
        $this->container['optimise_all'] = isset($data['optimise_all']) ? $data['optimise_all'] : null;
        $this->container['optimise'] = isset($data['optimise']) ? $data['optimise'] : null;
        $this->container['optimise_by_channel'] = isset($data['optimise_by_channel']) ? $data['optimise_by_channel'] : null;
        $this->container['optimise_by_category'] = isset($data['optimise_by_category']) ? $data['optimise_by_category'] : null;
        $this->container['optimise_by_product'] = isset($data['optimise_by_product']) ? $data['optimise_by_product'] : null;
        $this->container['report_filters'] = isset($data['report_filters']) ? $data['report_filters'] : null;
        $this->container['rules'] = isset($data['rules']) ? $data['rules'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['self'] === null) {
            $invalidProperties[] = "'self' can't be null";
        }
        if ($this->container['tracking_status'] === null) {
            $invalidProperties[] = "'tracking_status' can't be null";
        }
        if ($this->container['tracked_clicks'] === null) {
            $invalidProperties[] = "'tracked_clicks' can't be null";
        }
        if ($this->container['tracked_orders'] === null) {
            $invalidProperties[] = "'tracked_orders' can't be null";
        }
        if ($this->container['report_filters'] === null) {
            $invalidProperties[] = "'report_filters' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['self'] === null) {
            return false;
        }
        if ($this->container['tracking_status'] === null) {
            return false;
        }
        if ($this->container['tracked_clicks'] === null) {
            return false;
        }
        if ($this->container['tracked_orders'] === null) {
            return false;
        }
        if ($this->container['report_filters'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets self
     *
     * @return \Swagger\Client\Model\LinksAnalyticsIndexLink
     */
    public function getSelf()
    {
        return $this->container['self'];
    }

    /**
     * Sets self
     *
     * @param \Swagger\Client\Model\LinksAnalyticsIndexLink $self self
     *
     * @return $this
     */
    public function setSelf($self)
    {
        $this->container['self'] = $self;

        return $this;
    }

    /**
     * Gets tracking_status
     *
     * @return \Swagger\Client\Model\LinksGetStoreTrackingStatusLink
     */
    public function getTrackingStatus()
    {
        return $this->container['tracking_status'];
    }

    /**
     * Sets tracking_status
     *
     * @param \Swagger\Client\Model\LinksGetStoreTrackingStatusLink $tracking_status tracking_status
     *
     * @return $this
     */
    public function setTrackingStatus($tracking_status)
    {
        $this->container['tracking_status'] = $tracking_status;

        return $this;
    }

    /**
     * Gets tracked_clicks
     *
     * @return \Swagger\Client\Model\LinksGetStoreTrackedClicksLink
     */
    public function getTrackedClicks()
    {
        return $this->container['tracked_clicks'];
    }

    /**
     * Sets tracked_clicks
     *
     * @param \Swagger\Client\Model\LinksGetStoreTrackedClicksLink $tracked_clicks tracked_clicks
     *
     * @return $this
     */
    public function setTrackedClicks($tracked_clicks)
    {
        $this->container['tracked_clicks'] = $tracked_clicks;

        return $this;
    }

    /**
     * Gets tracked_orders
     *
     * @return \Swagger\Client\Model\LinksGetStoreTrackedOrdersLink
     */
    public function getTrackedOrders()
    {
        return $this->container['tracked_orders'];
    }

    /**
     * Sets tracked_orders
     *
     * @param \Swagger\Client\Model\LinksGetStoreTrackedOrdersLink $tracked_orders tracked_orders
     *
     * @return $this
     */
    public function setTrackedOrders($tracked_orders)
    {
        $this->container['tracked_orders'] = $tracked_orders;

        return $this;
    }

    /**
     * Gets tracked_external_orders
     *
     * @return \Swagger\Client\Model\LinksGetStoreTrackedExternalOrdersLink
     */
    public function getTrackedExternalOrders()
    {
        return $this->container['tracked_external_orders'];
    }

    /**
     * Sets tracked_external_orders
     *
     * @param \Swagger\Client\Model\LinksGetStoreTrackedExternalOrdersLink $tracked_external_orders tracked_external_orders
     *
     * @return $this
     */
    public function setTrackedExternalOrders($tracked_external_orders)
    {
        $this->container['tracked_external_orders'] = $tracked_external_orders;

        return $this;
    }

    /**
     * Gets report_by_day
     *
     * @return \Swagger\Client\Model\LinksGetStoreReportByDayLink
     */
    public function getReportByDay()
    {
        return $this->container['report_by_day'];
    }

    /**
     * Sets report_by_day
     *
     * @param \Swagger\Client\Model\LinksGetStoreReportByDayLink $report_by_day report_by_day
     *
     * @return $this
     */
    public function setReportByDay($report_by_day)
    {
        $this->container['report_by_day'] = $report_by_day;

        return $this;
    }

    /**
     * Gets report_by_channel
     *
     * @return \Swagger\Client\Model\LinksGetStoreReportByChannelLink
     */
    public function getReportByChannel()
    {
        return $this->container['report_by_channel'];
    }

    /**
     * Sets report_by_channel
     *
     * @param \Swagger\Client\Model\LinksGetStoreReportByChannelLink $report_by_channel report_by_channel
     *
     * @return $this
     */
    public function setReportByChannel($report_by_channel)
    {
        $this->container['report_by_channel'] = $report_by_channel;

        return $this;
    }

    /**
     * Gets report_by_category
     *
     * @return \Swagger\Client\Model\LinksGetStoreReportByCategoryLink
     */
    public function getReportByCategory()
    {
        return $this->container['report_by_category'];
    }

    /**
     * Sets report_by_category
     *
     * @param \Swagger\Client\Model\LinksGetStoreReportByCategoryLink $report_by_category report_by_category
     *
     * @return $this
     */
    public function setReportByCategory($report_by_category)
    {
        $this->container['report_by_category'] = $report_by_category;

        return $this;
    }

    /**
     * Gets report_by_product
     *
     * @return \Swagger\Client\Model\LinksGetStoreReportByProductLink
     */
    public function getReportByProduct()
    {
        return $this->container['report_by_product'];
    }

    /**
     * Sets report_by_product
     *
     * @param \Swagger\Client\Model\LinksGetStoreReportByProductLink $report_by_product report_by_product
     *
     * @return $this
     */
    public function setReportByProduct($report_by_product)
    {
        $this->container['report_by_product'] = $report_by_product;

        return $this;
    }

    /**
     * Gets optimise_all
     *
     * @return \Swagger\Client\Model\LinksOptimiseAllLink
     */
    public function getOptimiseAll()
    {
        return $this->container['optimise_all'];
    }

    /**
     * Sets optimise_all
     *
     * @param \Swagger\Client\Model\LinksOptimiseAllLink $optimise_all optimise_all
     *
     * @return $this
     */
    public function setOptimiseAll($optimise_all)
    {
        $this->container['optimise_all'] = $optimise_all;

        return $this;
    }

    /**
     * Gets optimise
     *
     * @return \Swagger\Client\Model\LinksOptimiseLink
     */
    public function getOptimise()
    {
        return $this->container['optimise'];
    }

    /**
     * Sets optimise
     *
     * @param \Swagger\Client\Model\LinksOptimiseLink $optimise optimise
     *
     * @return $this
     */
    public function setOptimise($optimise)
    {
        $this->container['optimise'] = $optimise;

        return $this;
    }

    /**
     * Gets optimise_by_channel
     *
     * @return \Swagger\Client\Model\LinksOptimiseByChannelLink
     */
    public function getOptimiseByChannel()
    {
        return $this->container['optimise_by_channel'];
    }

    /**
     * Sets optimise_by_channel
     *
     * @param \Swagger\Client\Model\LinksOptimiseByChannelLink $optimise_by_channel optimise_by_channel
     *
     * @return $this
     */
    public function setOptimiseByChannel($optimise_by_channel)
    {
        $this->container['optimise_by_channel'] = $optimise_by_channel;

        return $this;
    }

    /**
     * Gets optimise_by_category
     *
     * @return \Swagger\Client\Model\LinksOptimiseByCategoryLink
     */
    public function getOptimiseByCategory()
    {
        return $this->container['optimise_by_category'];
    }

    /**
     * Sets optimise_by_category
     *
     * @param \Swagger\Client\Model\LinksOptimiseByCategoryLink $optimise_by_category optimise_by_category
     *
     * @return $this
     */
    public function setOptimiseByCategory($optimise_by_category)
    {
        $this->container['optimise_by_category'] = $optimise_by_category;

        return $this;
    }

    /**
     * Gets optimise_by_product
     *
     * @return \Swagger\Client\Model\LinksOptimiseByProductLink
     */
    public function getOptimiseByProduct()
    {
        return $this->container['optimise_by_product'];
    }

    /**
     * Sets optimise_by_product
     *
     * @param \Swagger\Client\Model\LinksOptimiseByProductLink $optimise_by_product optimise_by_product
     *
     * @return $this
     */
    public function setOptimiseByProduct($optimise_by_product)
    {
        $this->container['optimise_by_product'] = $optimise_by_product;

        return $this;
    }

    /**
     * Gets report_filters
     *
     * @return \Swagger\Client\Model\LinksGetReportFiltersLink
     */
    public function getReportFilters()
    {
        return $this->container['report_filters'];
    }

    /**
     * Sets report_filters
     *
     * @param \Swagger\Client\Model\LinksGetReportFiltersLink $report_filters report_filters
     *
     * @return $this
     */
    public function setReportFilters($report_filters)
    {
        $this->container['report_filters'] = $report_filters;

        return $this;
    }

    /**
     * Gets rules
     *
     * @return \Swagger\Client\Model\LinksGetRulesLink
     */
    public function getRules()
    {
        return $this->container['rules'];
    }

    /**
     * Sets rules
     *
     * @param \Swagger\Client\Model\LinksGetRulesLink $rules rules
     *
     * @return $this
     */
    public function setRules($rules)
    {
        $this->container['rules'] = $rules;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


