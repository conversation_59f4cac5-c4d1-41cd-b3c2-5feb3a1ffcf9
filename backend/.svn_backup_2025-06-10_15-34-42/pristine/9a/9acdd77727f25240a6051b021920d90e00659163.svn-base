<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="phpunit.xsd"
         bootstrap="tests/bootstrap.php"
         backupGlobals="false"
         verbose="true">
    <testsuites>
        <testsuite name="small">
            <directory suffix="Test.php">tests/Framework</directory>
            <directory suffix="Test.php">tests/Extensions</directory>
            <directory suffix="Test.php">tests/Runner</directory>
            <directory suffix="Test.php">tests/Util</directory>
        </testsuite>

        <testsuite name="large">
            <directory suffix=".phpt">tests/TextUI</directory>
            <directory suffix=".phpt">tests/Regression</directory>
        </testsuite>
    </testsuites>

    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">src</directory>
            <exclude>
                <file>src/Framework/Assert/Functions.php</file>
                <file>src/Util/PHP/eval-stdin.php</file>
            </exclude>
        </whitelist>
    </filter>

    <php>
        <const name="PHPUNIT_TESTSUITE" value="true"/>
    </php>
</phpunit>
