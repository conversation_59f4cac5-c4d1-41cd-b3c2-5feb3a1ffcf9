<?php

namespace Pheanstalk\Command;

use <PERSON>eanstalk\YamlResponseParser;

/**
 * The 'list-tubes-watched' command.
 *
 * Lists the tubes on the watchlist.
 *
 * <AUTHOR>
 * @package Pheanstalk
 * @license http://www.opensource.org/licenses/mit-license.php
 */
class ListTubesWatchedCommand
    extends AbstractCommand
{
    /* (non-phpdoc)
     * @see Command::getCommandLine()
     */
    public function getCommandLine()
    {
        return 'list-tubes-watched';
    }

    /* (non-phpdoc)
     * @see Command::getResponseParser()
     */
    public function getResponseParser()
    {
        return new YamlResponseParser(
            YamlResponseParser::MODE_LIST
        );
    }
}
