<?php

/**	\defgroup model_users_adresses Adresses de livraison et de facturation
 *	\ingroup model_users
 *	Les fonctions de ce module permettent de gérer les adresses postales associées à un compte client.
 *	@{
 */

// \cond onlyria
/**	Cette fonction permet la vérification d'un identifiant de type d'adresse.
 *	@param int $id Identifiant du type d'adresse à vérifier
 *	@return bool true si l'identifiant passé en paramètre existe
 *	@return bool false si l'identifiant n'existe pas dans la base de données
 */
function gu_adr_types_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select type_id from gu_adr_types where type_id='.$id))==1;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement des types d'adresses supportées par la boutique
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du type d'adresse
 *			- name : désignation du type d'adresse
 */
function gu_adr_types_get(){
	return ria_mysql_query('
		select type_id as id, type_name as name from gu_adr_types order by type_id
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement des types d'adresses supportées par le moteur, sous forme de tableau associatif
 *	@return array un tableau associatif comprenant les colonnes suivantes :
 *			- id : identifiant du type d'adresse
 *			- name : désignation du type d'adresse
 */
function gu_adr_types_get_array(){

	$ar_types = array();

	$rtypes = gu_adr_types_get();
	while( $type = ria_mysql_fetch_array($rtypes) ){
		$ar_types[] = $type;
	}

	return $ar_types;
}
// \endcond

/** Permet l'enregistrement d'une nouvelle adresse.
 *
 *	@param int $user Obligatoire, identifiant de l'utilisateur auquel l'adresse est rattachée.
 *	@param int $type Obligatoire, type d'adresse 1:Particulier, 2:Société, 3:Professionnel, 4:Autre
 *	@param string $title Obligatoire, Identifiant du titre (seulement si particulier)
 *	@param string $firstname Obligatoire, Prénom de l'utilisateur (seulement si particulier)
 *	@param string $lastname Obligatoire, Nom de l'utilisateur (seulement si particulier)
 *	@param string $society Facultatif, Nom de l'entreprise (seulement si professionnel)
 *	@param string $siret Facultatif, No SIRET (seulement si professionnel)
 *	@param string $address1 Facultatif, Première partie de l'adresse
 *	@param string $address2 Facultatif, Seconde partie de l'adresse
 *	@param string $postal_code Facultatif, Code postal
 *	@param string $city Facultatif, Ville
 *	@param string $country Facultatif, Pays
 *	@param string $phone Facultatif, Numéro de téléphone
 *	@param string $fax Facultatif, Numéro de fax
 *	@param string $mobile Facultatif, Numéro de téléphone portable
 *	@param string $work Facultatif, Numéro de téléphone en journée
 *	@param string $adr_name Facultatif, Description de l'adresse
 *	@param string $email Facultatif, adresse email de l'adresse
 *	@param string $cnt_code Facultatif, code Pays tel que défini par la norme ISO 3166-2
 *	@param string $address3 Facultatif, Troisième partie de l'adresse
 *	@param string $ref_gescom Facultatif, référence de l'adresse dans la gescom
 *	@param string $country_state Facultatif, état / province de l'adresse
 *
 *	@return bool false en cas d'échec, L'identifiant attribué à l'adresse en cas de succès.
 *
 */
function gu_adresses_add( $user, $type, $title, $firstname, $lastname, $society='', $siret='', $address1='', $address2='', $postal_code='', $city='', $country='', $phone='', $fax='', $mobile='', $work='', $adr_name='', $email='', $cnt_code=null, $address3='', $ref_gescom=null, $country_state='' ){
	global $config;

	if( !gu_users_exists($user) ){ return false; }
	if( !gu_adr_types_exists($type) ){ return false; }

	if( $type==1 ){ // Particulier
		if( !gu_titles_exists($title) ){ return false; }
		$society = '';
		$siret = '';
	}elseif( $type==2 ){ // Société
		$title = 'null';
		$firstname = '';
		$lastname = '';
		if( !trim($society) ) return false;
		$siret = preg_replace( '/\s/', '', $siret );
		if( !validSIRET($siret) ) $siret = '';// return false;
	}else{ // Professionnel
		if( !gu_titles_exists($title) ) $title = 'null';
	}

	if( $cnt_code == null || strlen($cnt_code) != 2 ){
		// tente de récupérer le code en fonction du pays
		$cnt = sys_countries_get_code( $country );
		$cnt_code = ($cnt==false ? null : $cnt);
	}

	if (trim($country_state)) {
		$r_zone = sys_zones_get(0, '', '', false, 0, $cnt_code);
		if (!$r_zone || !ria_mysql_num_rows($r_zone)) {
			$country_state = '';
		}
	}

	// Vérifie que le champ address2 ne contient pas un numéro de téléphone, ce qui peut arriver à cause de l'auto-complétion du navigateur
	if( preg_match( '/^[0-9]{10}$/', $address2)==1 ){
		$address2 = '';
	}

	$loc_lat = $loc_lng = $loc_date = 'null';

	$fields = array(
		'adr_tnt_id', 'adr_usr_id', 'adr_type_id', 'adr_title_id', 'adr_firstname', 'adr_lastname',
		'adr_society', 'adr_siret', 'adr_address1', 'adr_address2', 'adr_address3', 'adr_postal_code', 'adr_city', 'adr_country',
		'adr_phone', 'adr_fax', 'adr_mobile', 'adr_phone_work', 'adr_date_created', 'adr_desc', 'adr_email', 'adr_cnt_code',
		'adr_latitude', 'adr_longitude', 'adr_date_location','adr_ref_gescom','adr_country_state'
	);

	$values = array(
		$config['tnt_id'], $user, $type, $title, '"'.addslashes(ucfirst(trim($firstname))).'"', '"'.addslashes(ucfirst(trim($lastname))).'"',
		'"'.addslashes(ucfirst(trim($society))).'"', '"'.$siret.'"', '"'.addslashes(ucfirst(trim($address1))).'"',
		'"'.addslashes(ucfirst(trim($address2))).'"', '"'.addslashes(ucfirst(trim($address3))).'"',
		'"'.addslashes(trim($postal_code)).'"', '"'.addslashes(ucfirst(trim($city))).'"', '"'.addslashes(trim(strtoupper2($country))).'"',
		'"'.addslashes(trim($phone)).'"', '"'.addslashes(trim($fax)).'"', '"'.addslashes(trim($mobile)).'"', '"'.addslashes(trim($work)).'"',
		'now()', '"'.addslashes(ucfirst(trim($adr_name))).'"', '"'.addslashes(strtolower(trim($email))).'"',
		$cnt_code ? "'".addslashes(strtoupper($cnt_code))."'": "null",
		'"'.addslashes($loc_lat).'"', '"'.addslashes($loc_lng).'"', $loc_date, $ref_gescom ? '\''.addslashes($ref_gescom).'\'':'null',
		($country_state ? '"'.addslashes($country_state).'"' : 'null')
	);

	$sql = 'insert into gu_adresses ('.implode(', ', $fields).') values ('.implode(', ', $values).')';

	if( !ria_mysql_query($sql) ){
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
		return false;
	}

	$adr_id = ria_mysql_insert_id();

	try{
		// Calcule les coordonnées géographiques (latitude, longitude).
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_USR_GEOLOC, array('id' => $adr_id));
	}catch(Exception $e){
		error_log( __FILE__.':'.__LINE__.' '.$e->getMessage() );
		return false;
	}

	gu_users_set_date_modified( $user );

	return $adr_id;

}

/** Permet la modification d'une adresse existante.
 *
 *	@param int $user Identifiant de l'utilisateur auquel l'adresse est rattachée.
 *	@param int $id Identifiant de l'adresse à modifier
 *	@param int $type Type d'adresse 1:Particulier, 2:Professionnel
 *	@param int $title Identifiant du titre (seulement si particulier)
 *	@param string $firstname Prénom de l'utilisateur (seulement si particulier)
 *	@param string $lastname Nom de l'utilisateur (seulement si particulier)
 *	@param string $society Nom de l'entreprise (seulement si professionnel)
 *	@param string $siret No SIRET (seulement si professionnel)
 *	@param string $address1 Première partie de l'adresse
 *	@param string $address2 Seconde partie de l'adresse
 *	@param string $postal_code Code postal
 *	@param string $city Ville
 *	@param string $country Pays
 *	@param string $phone Numéro de téléphone
 *	@param string $fax Numéro de fax
 *	@param string $mobile Numéro de téléphone portable
 *	@param string $work Numéro de téléphone en journée
 *	@param bool $from_sync Détermine si l'opération provient d'une synchronisation sage (true) ou d'une intervention de l'utilisateur (false défaut)
 *	@param string $adr_desc Optionnel Description de l'adresse. Si null, la mise à jour ne prendra pas en compte ce champ
 *	@param string $email Optionnnel, adresse email (NULL ne change pas la valeur précédente)
 *	@param string $cnt_code Optionnnel, code Pays tel que défini par la norme ISO 3166-2
 *	@param string $address3 Optionnel, Troisième partie de l'adresse
 *	@param string $ref_gescom Optionnel, référence de l'adresse dans la gescom
 *	@param string $country_state Optionnel, état / province de l'adresse
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 *
 */
function gu_adresses_update( $user, $id, $type, $title, $firstname, $lastname, $society, $siret, $address1, $address2, $postal_code, $city, $country, $phone=NULL, $fax=NULL, $mobile=NULL, $work=NULL, $from_sync=false, $adr_desc=null, $email=null, $cnt_code=null, $address3='', $ref_gescom=null, $country_state=null){
	global $config;

	if( !gu_users_exists($user) ){ return false; }
	if( !gu_adr_types_exists($type) ){ return false; }

	if( $type==1 ){ // Particulier
		if( !gu_titles_exists($title) ){ return false; }
		$society = '';
		$siret = '';
	}elseif( $type==2 ){ // Société
		$title = 'null';
		$firstname = '';
		$lastname = '';
		if( !trim($society) ){ return false; }
		$siret = preg_replace( '/\s/', '', $siret );
		if( !validSIRET($siret) ){ $siret = ''; }
	}else{ // Professionnel
		if( !gu_titles_exists($title) ){ $title = 'null'; }
	}

	if ($country_state !== null) {
		$r_zone = sys_zones_get(0, '', '', false, 0, $cnt_code);
		if (!$r_zone || !ria_mysql_num_rows($r_zone)) {
			$country_state = '';
		}
	}

	// Charge l'adresse à modifier
	$radr = gu_adresses_get( $user, $id );
	if( !$radr || !ria_mysql_num_rows($radr) ){
		return false;
	}

	$adr = ria_mysql_fetch_array($radr);

	if( $mobile===NULL ) $mobile = $adr['mobile'];
	if( $work===NULL ) $work = $adr['phone_work'];
	if( $email===NULL ) $email = $adr['email'];

	$need_sync = (
		$type!=$adr['type_id'] ||
		( is_numeric($title) && !$adr['title_id'] ) ||
		( !is_numeric($title) && $adr['title_id'] ) ||
		( is_numeric($title) && $adr['title_id'] && $title!=$adr['title_id'] ) ||
		$firstname!=$adr['firstname'] ||
		$lastname!=$adr['lastname'] ||
		$society!=$adr['society'] ||
		$siret!=$adr['siret'] ||
		$address1!=$adr['address1'] ||
		$address2!=$adr['address2'] ||
		$address3!=$adr['address3'] ||
		$postal_code!=$adr['postal_code'] ||
		$city!=$adr['city'] ||
		$country!=$adr['country'] ||
		$phone!=$adr['phone'] ||
		$fax!=$adr['fax'] ||
		$mobile!=$adr['mobile'] ||
		$work!=$adr['phone_work'] ||
		$email!=$adr['email'] ||
		($adr_desc!==null && $adr_desc!=$adr['description'])
	);

	$firstname = addslashes(ucfirst(trim($firstname)));
	$lastname = addslashes(ucfirst(trim($lastname)));
	$society = addslashes(ucfirst(trim($society)));
	$address1 = addslashes(ucfirst(trim($address1)));
	$address2 = addslashes(ucfirst(trim($address2)));
	$address3 = addslashes(ucfirst(trim($address3)));
	$city = addslashes(ucfirst(trim($city)));
	if( $adr_desc !== null ){
		$adr_desc = addslashes(ucfirst(trim($adr_desc)));
	}

	if( $cnt_code == null || strlen($cnt_code) != 2 ){
		// tente de récupérer le code en fonction du pays
		$cnt = sys_countries_get_code( $country );
		$cnt_code = ($cnt==false ? null : $cnt);
	}

	$loc_lat = is_numeric($adr['latitude']) ? $adr['latitude'] : 'null';
	$loc_lng = is_numeric($adr['longitude']) ? $adr['longitude'] : 'null';
	$loc_date = '\''.$adr['date_location'].'\'';

	$res = ria_mysql_query("
		update gu_adresses set
			adr_type_id=".$type.",
			adr_title_id=".$title.",
			adr_firstname='".$firstname."', adr_lastname='".$lastname."',
			adr_society='".$society."',
			adr_siret='".$siret."',
			adr_address1='".$address1."', adr_address2='".$address2."', adr_address3='".$address3."',
			adr_postal_code='".addslashes(trim($postal_code))."',
			adr_city='".$city."', adr_country='".addslashes(trim(strtoupper2($country)))."',
			adr_cnt_code=".( $cnt_code ? "'".addslashes(strtoupper($cnt_code))."'": "null" ).",
			adr_latitude='".addslashes($loc_lat)."',
			adr_longitude='".addslashes($loc_lng)."',
			adr_date_location=".$loc_date."
			".( $country_state !== null ? ", adr_country_state='".addslashes($country_state)."'" : "" )."
		where adr_tnt_id=".$config['tnt_id']." and adr_usr_id=".$user." and adr_id=".$id."
	");

	if ($adr['manual_location'] != 1) {
		if($address1!=$adr['address1'] || $postal_code!=$adr['postal_code'] || $city!=$adr['city'] || $country!=$adr['country']){
			try{
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_USR_GEOLOC, array('id' => $id));
			}catch(Exception $e){
				error_log( __FILE__.':'.__LINE__.' '.$e->getMessage() );
				return false;
			}
		}
	}

	if( $phone!==null ){
		ria_mysql_query("
			update gu_adresses set adr_phone='".addslashes(trim($phone))."'
			where adr_tnt_id=".$config['tnt_id']." and adr_usr_id=".$user." and adr_id=".$id
		);
	}
	if( $fax!==null ){
		ria_mysql_query("
			update gu_adresses set adr_fax='".addslashes(trim($fax))."'
			where adr_tnt_id=".$config['tnt_id']." and adr_usr_id=".$user." and adr_id=".$id
		);
	}
	if( $mobile!==null ){
		ria_mysql_query("
			update gu_adresses set adr_mobile='".addslashes(trim($mobile))."'
			where adr_tnt_id=".$config['tnt_id']." and adr_usr_id=".$user." and adr_id=".$id
		);
	}
	if( $work!==null ){
		ria_mysql_query("
			update gu_adresses set adr_phone_work='".addslashes(trim($work))."'
			where adr_tnt_id=".$config['tnt_id']." and adr_usr_id=".$user." and adr_id=".$id
		);
	}
	if( $adr_desc!==null ){
		ria_mysql_query("
			update gu_adresses set adr_desc='".$adr_desc."'
			where adr_tnt_id=".$config['tnt_id']." and adr_usr_id=".$user." and adr_id=".$id
		);
	}
	if( $email!==null ){
		ria_mysql_query("
			update gu_adresses set adr_email='".addslashes(strtolower(trim($email)))."'
			where adr_tnt_id=".$config['tnt_id']." and adr_usr_id=".$user." and adr_id=".$id
		);
	}
	if( $ref_gescom!==null ){
		ria_mysql_query("
			update gu_adresses set adr_ref_gescom='".addslashes(trim($ref_gescom))."'
			where adr_tnt_id=".$config['tnt_id']." and adr_usr_id=".$user." and adr_id=".$id
		);
	}

	if( $res ){
		try{
			// Index l'utilisateur dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_USER,
				'obj_id_0' => $user,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		if( $need_sync && !$from_sync ){
			ria_mysql_query('
				update gu_adresses
				set adr_need_sync=1
				where adr_tnt_id='.$config['tnt_id'].'
					and adr_usr_id='.$user.'
					and adr_id='.$id
			);
		}
	}else if( ria_mysql_errno() ){
		error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
	}

	if( $res ){
		gu_users_set_date_modified($user);

		// S'il s'agit de l'adresse de facturation lié à un compte ayant accès au registre
		// Son compte sera aussi mis à jour dans le registre
		if( RegisterGCP::onGcloud() ){
			$r_data_user = ria_mysql_query('
				select usr_prf_id as prf, usr_email as email, usr_adr_invoices as adr_inv_id
				from gu_users
				where usr_tnt_id = '.$config['tnt_id'].'
					and usr_id = '.$user.'
			');

			if( $r_data_user && ria_mysql_num_rows($r_data_user) ){
				$data_user = ria_mysql_fetch_assoc( $r_data_user );

				if( in_array($data_user['prf'], $config['admin_access_profiles']) && $data_user['adr_inv_id'] == $id ){
					$n_admin = new Administrator();
					$n_admin->load( $data_user['email'] );

					if( trim($n_admin->getEmail()) != '' ){
						$n_admin->setCivility( $title )
										->setFirstname($firstname)
										->setLastname($lastname)
										->setPhone( $phone === null ? '' : $phone)
										->setFax( $fax === null ? '' : $fax)
										->setMobile( $mobile === null ? '' : $mobile)
										->setWork( $work === null ? '' : $work);

						// Enregistre le compte administrateur dans le registre
						if( $n_admin->save(false) !== true ){
							return false;
						}
					}
				}
			}
		}
	}

	return $res;
}

/**
 * Cette fonction permet de savoir si l'adresse à été supprimé virtuellement ou non
 *	@param int $adr_id Obligatoire, identifiant de l'adresse
 *
 *	@return bool True ou False en fonction de sa suppression
 */
function gu_adresses_is_virtual_deleted( $adr_id ){
    global $config;

    if( !$adr_id || !is_numeric($adr_id) ){
       return false;
    }

	$res = ria_mysql_query('
		select 1 from gu_adresses
		where adr_date_deleted is not null
			and adr_tnt_id='.$config['tnt_id'].'
			and adr_id='.$adr_id
	);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res,0,0) == 1;
}


// \cond onlyria
/** Cette fonction permet de rafraichir la localisation de l'adresse
 * @param int $id Obligatoire, Identifiant de l'adresse à mettre à jour
 * @param float $loc_lat Obligatoire, Coordonnée latitude de l'adresse
 * @param float $loc_lng Obligatoire, Coordonnée  longitude de l'adresse
 * @param string $date Obligatoire, Date de localisation de l'adresse
 * @return bool false en cas d'échec, et true en cas de mise à jour
 */
function gu_adresses_set_coordinates( $id, $loc_lat, $loc_lng, $date ){
	global $config;

	if( !is_numeric($id) ){
		return false;
	}
	if( $loc_lat === null || $loc_lng === null ){
		return false;
	}
	if( !gu_adresses_exists($id) ){
		return false;
	}
	if( isdateheure($date) ){
		$datesql = 'DATE("'.dateheureparse($date).'")';
	}else{
		return false;
	}

	$res = ria_mysql_query("
		update gu_adresses set
			adr_latitude='".addslashes($loc_lat)."',
			adr_longitude='".addslashes($loc_lng)."',
			adr_date_location=".$datesql.",
			adr_manual_location='1'
		where (adr_tnt_id=".$config['tnt_id']." or adr_tnt_id=0) and adr_id=".$id."
	");

	if( $res ){
		$radr = ria_mysql_query("select adr_usr_id from gu_adresses where (adr_tnt_id=".$config['tnt_id']." or adr_tnt_id=0) and adr_id=".$id);
		if( $radr ){
			$adr = ria_mysql_fetch_assoc($radr);
			gu_users_set_date_modified( $adr['adr_usr_id'] );
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de rafraichir la localisation de l'adresse.
 * @param int $id Obligatoire, Identifiant de l'adresse à mettre à jour
 * @return bool false en cas d'échec, et true en cas de mise à jour, -1 dans le cas ou l'api ne fonctionne plus, -2 dans le cas ou l'adresse n'est pas renseigner
 */
function gu_adresses_refresh_coordinates( $id ){
	if( !is_numeric($id) ){
		return false;
	}
	global $config;

	// récupère l'adresse
	$radr = gu_adresses_get(0, $id);
	if( !$radr || !ria_mysql_num_rows($radr) ){
		return false;
	}

	$adr = ria_mysql_fetch_assoc($radr);
	if( isset($adr["manual_location"]) && $adr["manual_location"] == 1 ){
		return -3;
	}

	$loc_lat = $adr['latitude'];
	$loc_lng = $adr['longitude'];
	$need_request = true;

	// tente de récupérer la localisation de l'adresse avec la société en premier lieu
	if( $need_request ){
		$gps = false;
		if( trim($adr['society']) != '' ){
			$gps = sys_google_maps_search( trim($adr['society']).' '.trim($adr['address1']).' '.trim($adr['zipcode']).' '.trim($adr['city']).', '.trim($adr['country']), false, 'fr', true );
			if( $gps === -1 ){
				return -1;
			}
		}

		// mise à jour des coodonées GPS trouvées avec la société
		if( is_array($gps) && isset($gps['lat'], $gps['lng']) && is_numeric($gps['lat']) && is_numeric($gps['lng']) && $gps['lat'] != 0 && $gps['lng'] != 0 ){
			$loc_lat = str_replace(array(',', ' '), array('.', ''), $gps['lat']);
			$loc_lng = str_replace(array(',', ' '), array('.', ''), $gps['lng']);
		}else{
			// Ne pas faire un appel à Google maps geocoding si l'adresse n'est pas renseigné
			if( trim($adr['country']) == '' || trim($adr['city']) == '' || trim($adr['zipcode'] == '' )){
				return -2;
			}

			$gps = sys_google_maps_search( trim($adr['address1']).' '.trim($adr['zipcode']).' '.trim($adr['city']).', '.trim($adr['country']), false, 'fr', true );

			if( $gps === -1 ){
				return -1;
			}

			if( is_array($gps) && isset($gps['lat'], $gps['lng']) && is_numeric($gps['lat']) && is_numeric($gps['lng']) && $gps['lat'] != 0 && $gps['lng'] != 0 ){
				$loc_lat = str_replace(array(',', ' '), array('.', ''), $gps['lat']);
				$loc_lng = str_replace(array(',', ' '), array('.', ''), $gps['lng']);
			}
		}
	}

	$res = ria_mysql_query("
		update gu_adresses set
			adr_latitude=".( $loc_lat ? '"'.addslashes($loc_lat).'"' : 'null' ).",
			adr_longitude=".( $loc_lng ? '"'.addslashes($loc_lng).'"' : 'null' ).",
			adr_date_location=now()
		where (adr_tnt_id=".$config['tnt_id']." or adr_tnt_id=0) and adr_id=".$adr['id']."
	");

	if( $res ){
		gu_users_set_date_modified( $adr['usr_id'] );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification d'une adresse de livraison. Elle va déterminer si cette adresse est suffisamment complète pour y effectuer une livraison.
 *	@param $user Obligatoire, identifiant de l'utilisateur
 *	@param $adr Obligatoire, identifiant de l'adresse à contrôler
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_adresses_is_complete( $user, $adr ){
	// Charge l'adresse à modifier
	$radr = gu_adresses_get($user,$adr);
	if( !$radr || !ria_mysql_num_rows($radr) ){
		return false;
	}

	$adr = ria_mysql_fetch_array($radr);
	if( $adr['type_id']==1 || $adr['type_id']==3 ){
		if( !gu_titles_exists($adr['title_id']) || !trim($adr['firstname']) || !trim($adr['lastname']) ){
			return false;
		}
	}

	if( $adr['type_id']==2 || $adr['type_id']==3 ){
		if( !trim($adr['society']) || !trim($adr['siret']) ){
			return false;
		}
	}

	if( !trim($adr['address1']) || !trim($adr['zipcode']) || !trim($adr['city']) || !trim($adr['country']) ){
		return false;
	}

	if( !sys_countries_exists_name($adr['country']) ){
		return false;
	}

	return true;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'une adresse est masquée ou non.
 *	@param int $adr_id Obligatoire, identifiant d'une adresse
 *	@return bool True si l'adresse est masquée, False dans le cas contraire
 */
function gu_adresses_is_masked( $adr_id ){
	global $config;

	if( !is_numeric($adr_id) || $adr_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from gu_adresses
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_id = '.$adr_id.'
			and adr_date_masked is not null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour les coordonnées téléphones sur une adresse.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param int $adr Obligatoire, identifiant d'une adresse
 *	@param string $phone Optionnel, numéro de téléphone fixe
 *	@param string $fax Optionnel, numéro de fax
 *	@param string $mobile Optionnel, numéro de téléphone mobile
 *	@param string $work Optionnel, numéro de téléphone en journée
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function gu_adresses_update_telephone( $usr, $adr, $phone='', $fax='', $mobile='', $work='' ){
	global $config;

	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		update gu_adresses
		set adr_phone = "'.addslashes(trim($phone)).'",
			adr_fax = "'.addslashes(trim($fax)).'",
			adr_mobile = "'.addslashes(trim($mobile)).'",
			adr_phone_work = "'.addslashes(trim($work)).'"
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_usr_id = '.$usr.'
			and adr_id = '.$adr.'
	');

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de la civilité, du prénom et du nom sur une adresse.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param $adr Obligatoire, identifiant d'une adresse
 *	@param int $title Identifiant du titre (seulement si particulier)
 *	@param string $firstname Prénom de l'utilisateur (seulement si particulier)
 *	@param string $lastname Nom de l'utilisateur (seulement si particulier)
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function gu_adresses_update_name( $usr, $adr, $title, $firstname, $lastname ){

	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr<=0 ){
		return false;
	}

	if( !gu_titles_exists($title) ) return false;
	if( !trim($firstname) ) return false;
	if( !trim($lastname) ) return false;

	global $config;

	$res = ria_mysql_query('
		update gu_adresses
		set adr_title_id='.$title.',
			adr_firstname="'.addslashes(trim(ucfirst($firstname))).'",
			adr_lastname="'.addslashes(trim(ucfirst($lastname))).'"
		where adr_tnt_id='.$config['tnt_id'].'
			and adr_usr_id='.$usr.'
			and adr_id='.$adr.'
	');

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le numéro de téléphone.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param $adr Obligatoire, identifiant d'une adresse
 *	@param string $phone Optionnel, numéro de téléphone
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function gu_adresses_set_phone( $usr, $adr, $phone='' ){
	global $config;

	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		update gu_adresses
		set adr_phone = "'.addslashes(trim($phone)).'"
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_usr_id = '.$usr.'
			and adr_id = '.$adr.'
	');

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le numéro de téléphone portable.
 *	@param int $usr Obligatoire, identifiant d'un compte client
 *	@param $adr Obligatoire, identifiant d'une adresse
 *	@param string $mobile Optionnel, numéro de téléphone
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function gu_adresses_set_mobile( $usr, $adr, $mobile='' ){
	global $config;

	if( !is_numeric($usr) || $usr<=0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		update gu_adresses
		set adr_mobile = "'.addslashes(trim($mobile)).'"
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_usr_id = '.$usr.'
			and adr_id = '.$adr.'
	');

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Retourne les adresses nécessitant une synchronisation avec le compte client correspondant dans la gestion commerciale.
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'adresse
 *		- usr_id : identifiant de l'utilisateur auquel l'adresse appartient
 *		- usr_ref : référence de l'utilisateur
 *		- society : société
 *		- title_id : identifiant de civilité
 *		- title_name : nom de civilité
 *		- firstname : prénom
 *		- lastname : nom de famille
 *		- siret : numéro siret de la société
 *		- address1 : adresse
 *		- address2 : complément d'adresse
 *		- address3 : complément d'adresse
 *		- zipcode : code postal
 *		- city : ville
 *		- country : pays
 *		- country_code : code pays iso
 *		- phone : téléphone
 *		- fax : fax
 *		- mobile : téléphone mobile
 *		- phone_work : téléphone pro
 *		- email : adresse email
 *		- description : description / infos complémentaires
 *		- date_modified : date et heure de dernière modification
 *	@todo Cette fonction est à revoir dans le contexte où il existe une adresse de facturation et une adresse de livraison par défaut
 */
function gu_adresses_get_updated(){
	global $config;

	$sql = '
		select	adr_id as id,
				adr_society as society,
				adr_title_id as title_id,
				title_name,
				adr_firstname as firstname,
				adr_lastname as lastname,
				adr_siret as siret,
				adr_address1 as address1,
				adr_address2 as address2,
				adr_address3 as address3,
				adr_postal_code as zipcode,
				adr_city as city,
				adr_country as country,
				adr_cnt_code as country_code,
				adr_phone as phone,
				adr_fax as fax,
				adr_mobile as mobile,
				adr_phone_work as phone_work,
				adr_email as email,
				adr_desc as description,
				adr_date_modified as date_modified,
				usr_id, usr_ref
		from gu_adresses
			inner join gu_users on (adr_usr_id=usr_id and usr_adr_invoices=adr_id and usr_tnt_id='.$config['tnt_id'].')
			left join gu_titles on (adr_title_id=title_id)
		where adr_tnt_id='.$config['tnt_id'].' and usr_ref!="" and adr_need_sync and usr_date_deleted is null and adr_date_deleted is null and usr_is_sync = 1
	';

	if( $config['tnt_id']==5 ){
		$sql .= '
			and usr_last_login>\'2009/07/01\'
			and lower(trim(adr_country)) in (
				\'france\',
				\'monaco\',
				\'\',
				\'guadeloupe\',
				\'guyane française\',
				\'martinique\',
				\'réunion\',
				\'saint-barthélemy\',
				\'saint-martin\',
				\'nouvelle-calédonie\',
				\'polynésie française\',
				\'saint-pierre-et-miquelon\',
				\'mayotte\',
				\'reunion\',
				\'saint-barthelemy\',
				\'saint barthelemy\',
				\'saint barthélemy\',
				\'saint martin\',
				\'st-martin\',
				\'st martin\',
				\'st barthelemy\',
				\'st barthélemy\',
				\'st-barthelemy\',
				\'st-barthélemy\',
				\'saint pierre et miquelon\',
				\'st-pierre-et-miquelon\',
				\'st pierre et miquelon\',
				\'nouvelle-caledonie\',
				\'nouvelle calédonie\',
				\'nouvelle caledonie\',
				\'polynesie française\',
				\'terres australes francaises\',
				\'terres australes françaises\',
				\'wallis et futuna\',
				\'wallis-et-futuna\'
			)
		';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Enregistre qu'une adresse a été synchronisée avec son équivalent dans la gestion commerciale, pour la retirer de la file
 *	d'attente des adresses à mettre à jour.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur auquel l'adresse appartient
 *	@param int $adr Obligatoire, identifiant de l'adresse à indiquer comme synchronisée
 *	@param bool $need_sync Facultatif, détermine dans quel sens la valorisation doit être faite
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_adresses_set_updated( $usr, $adr, $need_sync=false ){
	global $config;

	if( !is_numeric($usr) || $usr <= 0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		update gu_adresses
		set adr_need_sync = '.( $need_sync ? 1 : 0 ).'
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_usr_id = '.$usr.'
			and adr_id = '.$adr.'
	');

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le complément d'adresse.
 *	@param int $usr Obligaroire, identifiant de l'utilisateur auquel l'adresse appartient
 *	@param $adr Obligaroire, identifiant de l'adresse
 *	@param string $address2 Optionnel, complément d'adresse
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_adresses_set_address2( $usr, $adr, $address2='' ){
	global $config;

	if( !is_numeric($usr) || $usr <= 0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		update gu_adresses
		set adr_address2 = "'.addslashes(trim($address2)).'"
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_usr_id = '.$usr.'
			and adr_id = '.$adr.'
	');

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le complément d'adresse.
 *	@param int $usr Obligaroire, identifiant de l'utilisateur auquel l'adresse appartient
 *	@param $adr Obligaroire, identifiant de l'adresse
 *	@param string $address3 Optionnel, complément d'adresse
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_adresses_set_address3( $usr, $adr, $address3='' ){
	global $config;

	if( !is_numeric($usr) || $usr <= 0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		update gu_adresses
		set adr_address3 = "'.addslashes(trim($address3)).'"
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_usr_id = '.$usr.'
			and adr_id = '.$adr.'
	');

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le complément d'adresse2 et 3.
 *	@param int $usr Obligaroire, identifiant de l'utilisateur auquel l'adresse appartient
 *	@param $adr Obligaroire, identifiant de l'adresse
 *	@param string $address2 Optionnel, complément d'adresse
 *	@param string $address3 Optionnel, complément d'adresse
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_adresses_set_adr_complement( $usr, $adr, $address2='', $address3='' ){
	global $config;

	if( !is_numeric($usr) || $usr <= 0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr <= 0 ){
		return false;
	}
	if( trim($address2) == '' && trim($address3) == '' ){
		return false;
	}

	if( trim($address2) == '' ){
		$address2 = $address3;
		$address3 = '';
	}

	$sql = '
		update gu_adresses
		set adr_address2 = "'.addslashes(trim($address2)).'"
	';

	if( trim($address2) !== "" ){
		$sql .= 'set adr_address3 = "'.addslashes(trim($address3)).'"';
	}

	$sql .= 'where adr_tnt_id = '.$config['tnt_id'].'
			and adr_usr_id = '.$usr.'
			and adr_id = '.$adr.'';

	$res = ria_mysql_query($sql);

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction est un alias de gu_adresses_set_postal_code().
 *	@param int $usr Identifiant de l'utilisateur auquel l'adresse appartient.
 *	@param $adr Identifiant de l'adresse à modifier.
 *	@param string $zipcode Nouveau code postal de l'adresse.
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_adresses_set_zipcode( $usr, $adr, $zipcode ){
	return gu_adresses_set_postal_code( $usr, $adr, $zipcode );
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le code postal d'une adresse donnée.
 *	@param int $usr Identifiant de l'utilisateur auquel l'adresse appartient.
 *	@param $adr Identifiant de l'adresse à modifier.
 *	@param $postal_code Nouveau code postal de l'adresse.
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_adresses_set_postal_code( $usr, $adr, $postal_code ){
	global $config;

	if( !is_numeric($usr) || $usr <= 0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		update gu_adresses
		set adr_postal_code = "'.addslashes(trim($postal_code)).'"
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_usr_id = '.$usr.'
			and adr_id = '.$adr.'
	');

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour l'adresse email d'une adresse donnée
 *	Cette fonction ne peut pas être utilisée pour les super-administrateurs
 *	@param int $usr Obligatoire, identifiant du compte
 *	@param int $adr Obligatoire, identifiant de l'adresse
 *	@param string $email Obligatoire, nouvelle adresse email (NULL est autorisée)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_adresses_set_email( $usr, $adr, $email ){
	global $config;

	if( !is_numeric($usr) || $usr <= 0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr <= 0 ){
		return false;
	}

	if( $email !== null ){
		$email = "'".addslashes(strtolower(trim($email)))."'";
	}else{
		$email = 'NULL';
	}

	$res = ria_mysql_query('
		update gu_adresses
		set adr_email = '.$email.'
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_id = '.$adr.'
			and adr_usr_id = '.$usr.'
	');

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la description de l'adresse d'un utilisateur.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur.
 *	@param int $adr Obligatoire, Identifiant de l'adresse.
 *	@param string $desc Obligatoire, Description (Null est autorisé).
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function gu_adresses_set_desc( $usr, $adr, $desc ){
	global $config;

	if( !is_numeric($usr) || $usr <= 0 ){
		return false;
	}
	if( !is_numeric($adr) || $adr <= 0 ){
		return false;
	}

	if( $desc === null ){
		$desc = 'NULL';
	}else{
		$desc = '"'.addslashes(trim($desc)).'"';
	}

	$res = ria_mysql_query('
		update gu_adresses
		set adr_desc = '.$desc.'
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_id = '.$adr.'
			and adr_usr_id = '.$usr.'
	');

	if( $res ){
		gu_users_set_date_modified( $usr );
	}

	return $res;

}
// \endcond

/** Cette fonction permet de charger une ou plusieurs adresses, en utilisant les paramètres optionnels comme filtre.
 *	Il est à noter que la fonction ne fait pas de trim() sur les arguments
 *
 *	@param int $user Optionnel, identifiant de l'utilisateur propriétaire de l'adresse
 *	@param int $id Optionnel, identifiant de l'adresse
 *	@param string $zipcode Optionnel, code postal sur lequel filtrer le résultat
 *	@param array $sort Optionnel, paramètre de tri
 *	@param string $described Optionnel, si True, l'adresse doit être décrite
 *	@param string $city Optionnel, ville sur laquelle filtrer le résultat
 *	@param string $address1 Optionnel, adresse sur laquelle filtrer le résultat
 *	@param string $address2 Optionnel, complément d'adresse sur lequel filtrer le résultat
 *	@param string $firstname Optionnel, prénom sur lequel filtrer le résultat
 *	@param string $lastname Optionnel, nom sur lequel filtrer le résultat
 *	@param string $society Optionnel, nom de société sur lequel filtrer le résultat
 *	@param string $siret Optionnel, numéro de SIRET sur lequel filtrer le résultat
 *	@param bool $no_casse Optionnel, par défaut la recherche sur l'adresse est sensible à la casse, mettre true pour que ce ne soit pas le cas
 *	@param string $email Optionnel, permet de filtrer une ou des adresses par l'adresse email
 *	@param int|array $exclude Optionnel, identifiant ou tableau d'identifiants d'adresse à exclure du résultat
 *	@param bool $is_masked  OPtionnel, par défaut toutes les adresses sont récupérées, mettre True pour n'avoir que celles masquées et False pour n'avoir que celles non masquées
 *	@param bool $is_invoice Optionnel, par défaut toutes les adresses sont récupérées, mettre True pour n'avoir que celles étant utilisées comme adresse de facturation
 *	@param string $address3 Optionnel, complément d'adresse sur lequel filtrer le résultat
 *	@param $coordinates Facultatif, la valeur -1 les adresses sans aucunes coordonnées
 *	@param string $ref_gescom Facultatif, référence de l'adresse dans la gescom
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- usr_id : identifiant de l'utilisateur
 *		- id : identifiant de l'adresse
 *		- type_id : identifiant du type d'adresse (1 : Particulier, 2 : Professionnel, 3 : Mixte)
 *		- type_name : désignation du type d'adresse
 *		- title_id : identifiant de la civilité
 *		- title_name : libellé de la civilité
 *		- firstname : prénom (si type=1 ou 3)
 *		- lastname : nom de famille (si type=1 ou 3)
 *		- society : nom de l'entreprise (si type=2 ou 3)
 *		- siret : Siret de l'entreprise
 *		- address1 : première composante de l'adresse
 *		- address2 : seconde composante de l'adresse
 *		- address3 : troisième composante de l'adresse
 *		- postal_code : code postal
 *		- zipcode : code postal (alias)
 *		- city : ville
 *		- country : pays
 *		- country_code : code du pays ISO
 *		- phone : numéro de téléphone
 *		- fax : fax
 *		- mobile : numéro de téléphone portable
 *		- phone_work : numéro de téléphone en journée
 *		- description : description de l'adresse
 *		- email : adresse email
 *		- date_masked : date à laquelle l'adresse a été masqué (null si non masquée)
 *		- ref_gescom : référence de l'adresse dans la gescom
 *		- country_state : état / province de l'adresse
 *		- date_created : date de création de l'adresse, au format US
 *		- date_modified : date de dernière modification de l'adresse, au format US
 *		- manual_location : booléen indiquant si la géolocalisation a été définie de façon manuelle (true) ou automatique (false)
 *	@return bool False en cas d'échec
 */
function gu_adresses_get( $user=0, $id=0, $zipcode='', $sort=array(), $described=false, $city='', $address1='', $address2='', $firstname='', $lastname='', $society='', $siret='', $no_casse=false, $email=false, $exclude=false, $is_masked=null, $is_invoice=false, $address3='', $coordinates=false, $ref_gescom=false ){

	$user = control_array_integer( $user, false );
	if( $user === false ){
		return false;
	}

	if( is_array($id) ){
		if( !sizeof($id) ){
			return false;
		}
		foreach( $id as $one_id ){
			if( !is_numeric($one_id) || $one_id <= 0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($id) || $id < 0 ){
			return false;
		}elseif( $id ){
			$id = array($id);
		}else{
			$id = array();
		}
	}

	if( !is_array($exclude) ){
		if( is_numeric($exclude) && $exclude>0 ){
			$exclude = array( $exclude );
		}else{
			$exclude = array();
		}
	}else{
		foreach( $exclude as $e ){
			if( !is_numeric($e) || $e<=0 ){
				return false;
			}
		}
	}

	global $config;

	$sql = '
		select adr_usr_id as usr_id, adr_id as id, type_id, type_name, title_id, title_name, adr_firstname as firstname, adr_lastname as lastname,
		adr_society as society, adr_siret as siret, adr_address1 as address1, adr_address2 as address2, adr_address3 as address3,
		adr_postal_code as postal_code, adr_postal_code as zipcode, adr_city as city, adr_country as country, adr_cnt_code as country_code,
		adr_phone as phone, adr_fax as fax, adr_mobile as mobile, adr_phone_work as phone_work, adr_desc as description, adr_email as email,
		adr_date_created as date_created, adr_date_modified as date_modified, adr_manual_location as manual_location,
		adr_date_masked as date_masked, adr_latitude as latitude, adr_longitude as longitude, adr_date_location as date_location,
		adr_ref_gescom as ref_gescom, adr_country_state as country_state
		from gu_adresses
			left join gu_adr_types on adr_type_id = type_id
			left join gu_titles on adr_title_id = title_id
		where adr_tnt_id in (0, '.$config['tnt_id'].')
			and adr_date_deleted is null
	';

	if( sizeof($user) ){
		$sql .= ' and adr_usr_id in ('.implode(', ', $user).')';
	}

	if( sizeof($id) ){
		$sql .= ' and adr_id in ('.implode(', ', $id).')';
	}
	if( $zipcode ){
		$sql .= ' and adr_postal_code = "'.addslashes( trim($zipcode) ).'"';
	}
	if( $described ){
		$sql .= ' and trim(ifnull(adr_desc, "")) != ""';
	}
	if( $city ){
		if( !$no_casse ){
			$sql .= ' and adr_city = "'.addslashes( $city ).'"';
		} else {
			$sql .= ' and lower(adr_city) = lower("'.addslashes( $city ).'")';
		}
	}
	if( $address1 ){
		if( !$no_casse ){
			$sql .= ' and adr_address1 = "'.addslashes( $address1 ).'"';
		} else {
			$sql .= ' and lower(adr_address1) = lower("'.addslashes( $address1 ).'")';
		}
	}
	if( $address2 ){
		if( !$no_casse ){
			$sql .= ' and adr_address2 = "'.addslashes( $address2 ).'"';
		} else {
			$sql .= ' and lower(adr_address2) = lower("'.addslashes( $address2 ).'")';
		}
	}
	if( $address3 ){
		if( !$no_casse ){
			$sql .= ' and adr_address3 = "'.addslashes( $address3 ).'"';
		} else {
			$sql .= ' and lower(adr_address3) = lower("'.addslashes( $address3 ).'")';
		}
	}
	if( $firstname ){
		if( !$no_casse ){
			$sql .= ' and adr_firstname = "'.addslashes( $firstname ).'"';
		} else {
			$sql .= ' and lower(adr_firstname) = lower("'.addslashes( $firstname ).'")';
		}
	}
	if( $lastname ){
		if( !$no_casse ){
			$sql .= ' and adr_lastname = "'.addslashes( $lastname ).'"';
		} else {
			$sql .= ' and lower(adr_lastname) = lower("'.addslashes( $lastname ).'")';
		}
	}
	if( $society ){
		if( !$no_casse ){
			$sql .= ' and adr_society = "'.addslashes( $society ).'"';
		} else {
			$sql .= ' and lower(adr_society) = lower("'.addslashes( $society ).'")';
		}
	}
	if( $siret ){
		$sql .= ' and adr_siret = "'.addslashes( $siret ).'"';
	}
	if( $email ){
		$sql .= ' and lower(adr_email) = "'.addslashes(strtolower(trim($email))).'"';
	}
	if( $ref_gescom ){
		$sql .= ' and adr_ref_gescom = "'.addslashes(trim($ref_gescom)).'"';
	}
	if( sizeof($exclude) ){
		$sql .= ' and adr_id not in ('.implode( ', ', $exclude ).')';
	}

	if( $is_masked !== null ){
		if( $is_masked ){
			$sql .= ' and adr_date_masked is not null';
		}else{
			$sql .= ' and adr_date_masked is null';
		}
	}

	if( $is_invoice ){
		$sql .= '
			and exists (
				select 1
				from gu_users
				where usr_tnt_id = '.$config['tnt_id'].'
					and usr_id = adr_usr_id
					and usr_date_deleted is null
					and usr_adr_invoices = adr_id
			)
		';
	}

	if( is_numeric($coordinates) && $coordinates === -1 ){
		$sql .= '
			and ifnull(adr_longitude, 0.00000000) = 0.00000000
			and ifnull(adr_latitude, 0.00000000) = 0.00000000
			and ifnull(adr_date_location, "0000-00-00 00:00:00") = "0000-00-00 00:00:00"
		';

		if( !is_array($sort) || (is_array($sort) && !sizeof($sort)) ){
			$sort = array('loc_date' => 'asc');
		}
	}

	if( is_array($sort) && sizeof($sort) ){
		$s = array();
		foreach( $sort as $field => $dir ){
			$field = strtolower(trim($field));
			$dir = strtolower(trim($dir)) == 'desc' ? 'desc' : 'asc';
			switch( $field ){
				case 'society':
					$s[] = 'society '.$dir;
					break;
				case 'loc_date':
					$s[] = 'IFNULL(adr_date_location, 0) '.$dir;
					break;
			}
		}
		$sql .= ' order by '.implode(', ', $s);
	}

	return ria_mysql_query($sql);
}

/**	Vérifie la validité d'un identifiant d'adresse
 *	@param int $id Identifiant d'adresse à tester
 *	@param int $usr_id Optionnel, identifiant d'un compte
 *	@return bool true si l'identifiant est valide et correspond à une adresse enregistrée
 *	@return bool false si l'identifiant est invalide ou ne correspond à aucune adresse enregistrée.
 */
function gu_adresses_exists( $id, $usr_id=0 ){
	global $config;

	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}

	$sql = '
		select adr_id
		from gu_adresses
		where (adr_tnt_id=0 or adr_tnt_id='.$config['tnt_id'].')
			and adr_id='.$id.'
			and adr_date_deleted is null
	';

	if( $usr_id > 0 ){
		$sql .= ' and adr_usr_id = '.$usr_id;
	}

	$res = ria_mysql_query( $sql );
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}

// \cond onlyria
/**	Cette fonction permet la suppression définitive d'une adresse (livraison/facturation).
 *	Si l'adresse n'existe déjà plus, aucune erreur n'est générée.
 *	@param int $id Obligatoire, Identifiant de l'adresse à supprimer.
 *	@return bool true en cas de succès, false en cas d'erreur.
 */
function gu_adresses_del( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	// charge l'adresse à supprimer pour récupérer le compte utilisateur asocié
	$radr = gu_adresses_get( 0, $id );
	if( !$radr ){
		return false;
	}elseif( !ria_mysql_num_rows($radr) ){
		return true; // l'adresse n'existe pas, c'est OK
	}
	$adr = ria_mysql_fetch_assoc($radr);

	$res = ria_mysql_query('
		update gu_adresses
		set adr_date_deleted = now()
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_id = '.$id.'
	');

	if( $res ){
		gu_users_set_date_modified( $adr['usr_id'] );
	}
	return $res;

}
// \endcond

/**	Cette fonction permet de marquer une adresse comme masquée
 *	@param int $id Obligatoire, Identifiant de l'adresse à supprimer.
 *	@param bool $masked Optionnel, par défaut l'adresse sera masquée, mettre False pour retirer le masquage
 *	@return bool true en cas de succès, false en cas d'erreur.
 */
function gu_adresses_set_masked( $id, $masked=true ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	// charge l'adresse à supprimer pour récupérer le compte utilisateur associé
	$radr = gu_adresses_get( 0, $id );
	if( !$radr ){
		return false;
	}elseif( !ria_mysql_num_rows($radr) ){
		return true; // l'adresse n'existe pas, c'est OK
	}

	$adr = ria_mysql_fetch_assoc($radr);

	$res = ria_mysql_query('
		update gu_adresses
		set adr_date_masked = '.( $masked ? 'now()' : 'null' ).'
		where adr_tnt_id = '.$config['tnt_id'].'
			and adr_id = '.$id.'
	');

	if( $res ){
		gu_users_set_date_modified( $adr['usr_id'] );
	}

	return $res;

}

/**	Cette fonction permet de savoir si un adresse est utilisé dans une commande
 *	@param int $id Identifiant de l'adresse à controler.
 *	@return bool true en cas de succès, false en cas d'erreur.
 */
function gu_adresses_is_used( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1 from ord_orders
		where ord_tnt_id='.$config['tnt_id'].' and (ord_adr_invoices='.$id.' or ord_adr_delivery='.$id.')
	');

	if( $res && ria_mysql_num_rows($res) > 0 ){
		return true;
	}

	return false;

}

 // \cond onlyria
/** Cette fonction retourne le contenu de la table gu_adresses_city, selon des filtres optionnels.
 *	Il est possible de déterminer, avec cette fonction, si un code postal et un code INSEE ont une correspondance
 *	@param $insee Optionnel, code INSEE de la ville
 *	@param string $name Optionnel, nom de la ville
 *	@param string $zipcode Optionnel, code postal de la ville
 *	@param $sort Optionnel, tableau associatif de tri ( nom_du_champ => direction_du_tri )
 *	@param $name_is_part Optionnel, si true, le nom de ville est considéré comme la partie gauche d'un nom complet (utiliser LIKE)
 *	@param $zip_is_part Optionnel, si true, le code postal est considéré comme la partie gauche d'un code complet (utiliser LIKE)
 *
 *	@return bool False en cas d'erreur(s)
 *	@return resource Un résultat de requête SQL comprenant les champs suivants :
 *		- insee : code INSEE de la ville
 *		- name : nom de la ville ( en majuscules, sans accents et sans caractères spéciaux )
 *		- zipcode : code postal de la ville
 */
function gu_adresses_get_cities( $insee=false, $name=false, $zipcode=false, $sort=false, $name_is_part=false, $zip_is_part=false ){

	$sql = 'select city_insee as insee, city_name as name, city_zipcode as zipcode
			from gu_adresses_cities where 1
		';

	if( $insee!=false )
		$sql .= ' and city_insee=\''.addslashes( trim($insee) ).'\'';

	if ($name != false) {

		// Adaptation de la recherche, si on utilise une abréviation dans le cas des villes commençant par Saint(e)
		$name = str_replace(array('.'), array(''), strtoupper2(trim($name)));
		if (strlen($name) > 3 && substr($name, 0, 3) == 'ST ') {
			$name = 'SAINT ' . substr($name, 3, (strlen($name) - 3));
		} elseif (strlen($name) > 4 && substr($name, 0, 4) == 'STE ') {
			$name = 'SAINTE ' . substr($name, 4, (strlen($name) - 4));
		} elseif (strlen($name) > 3 && substr($name, 0, 3) == 'ST-') {
			$name = 'SAINT-' . substr($name, 3, (strlen($name) - 3));
		} elseif (strlen($name) > 4 && substr($name, 0, 4) == 'STE-') {
			$name = 'SAINTE-' . substr($name, 4, (strlen($name) - 4));
		}

		if ($name_is_part == true) {
			$sql .= ' and city_name like \'' . addslashes($name) . '%\'';
		} else {
			$sql .= ' and city_name=\'' . addslashes($name) . '\'';
		}
	}

	if( $zipcode!=false ){
		if( $zip_is_part==true ){
			$sql .= ' and city_zipcode like \''.addslashes( trim($zipcode) ).'%\'';
		}else{
			$sql .= ' and city_zipcode=\''.addslashes( trim($zipcode) ).'\'';
		}
	}

	// Vérifie le paramètre sort
	if( is_array( $sort ) ){
		$first = true;
		$a = $b = $c = false;
		foreach($sort as $field => $dir){
			if( (($field=='insee' && !$a) || ($field=='name' && !$b) || ($field=='zipcode' && !$c)) && ( $dir=='asc' || $dir='desc' )  ){
				$sql .= ($first ? ' order by' : ',').$field.' '.$dir;
				$first = false;
				switch( $field ){
					case 'insee':
						$a = true;
						break;
					case 'name':
						$b = true;
						break;
					default:
						$c = true;
						break;
				}
			}
			if( $first ){
				$sql .= ' order by name asc';
			}
		}
	}else{
		$sql .= ' order by name asc';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction retourne une liste distincte de ville selon des paramètres facultatifs
 *	@param string $zipcode Facultatif, tout ou partie du code postal
 *	@param string $city Facultatif, tout ou partie du nom de la ville
 *
 *	@return resource un résultat de requête MySQL comprenant les champs suivants :
 *		- name : nom de la ville
 *		- insee : code insee de la ville
 *		- zipcode : code postal de la ville ( le premier trouvé sur le group by )
 */
function gu_adresses_get_distinct_cities( $zipcode='', $city='' ){
	if (trim($city) != '') {
		$city = str_replace(array('.'), array(''), strtoupper2($city));
		if (strlen($city) >= 3 && substr($city, 0, 3) == 'ST ') {
			$city = 'SAINT%' . substr($city, 3, (strlen($city) - 3));
		} elseif (strlen($city) >= 4 && substr($city, 0, 4) == 'STE ') {
			$city = 'SAINTE%' . substr($city, 4, (strlen($city) - 4));
		} elseif (strlen($city) >= 3 && substr($city, 0, 3) == 'ST-') {
			$city = 'SAINT%' . substr($city, 3, (strlen($city) - 3));
		} elseif (strlen($city) >= 4 && substr($city, 0, 4) == 'STE-') {
			$city = 'SAINTE%' . substr($city, 4, (strlen($city) - 4));
		} elseif (strlen($city) >= 6 && substr($city, 0, 6) == 'SAINT ') {
			$city = 'SAINT%' . substr($city, 6, (strlen($city) - 6));
		} elseif (strlen($city) >= 7 && substr($city, 0, 7) == 'SAINTE ') {
			$city = 'SAINTE%' . substr($city, 7, (strlen($city) - 7));
		}
		$city = trim($city);
	}

	$sql = '
		select d1.dzn_name as zipcode, d2.dzn_code as insee, d2.dzn_name as name
		from sys_zones as d1
			join sys_zones as d2 on (d1.dzn_id = d2.dzn_parent_id)
		where d1.dzn_type_id = '._ZONE_ZIPCODES.' and d2.dzn_type_id = '._ZONE_INSEE.'
			and d1.dzn_is_deprecated = 0 and d2.dzn_is_deprecated = 0
	';

	if (trim($zipcode) != '') {
		$sql .= ' and d1.dzn_name like "'.addslashes($zipcode).'%"';
	}

	if (trim($city) != '') {
		$sql .= ' and d2.dzn_name like "'.addslashes($city).'%"';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère la liste des codes postaux, éventuellement filtré selon un paramètre optionnel
 *	@param $zip_str_part Optionnel, début de chaîne
 *	@param string $desc Optionnel, si True, la requête est triée par ordre décroissant de code postal, sinon par ordre croissant
 *	@return resource Un résultat de requête SQL comprenant le champ suivant :
 *		- zipcode : code postal
 *	@return bool False en cas d'échec
 */
function gu_adresses_get_zipcodes( $zip_str_part='', $desc=false ){

	$sql = 'select distinct( city_zipcode ) as zipcode from gu_adresses_cities';

	if( $zip_str_part!='' ){
		$sql .= ' where city_zipcode like \''.addslashes( $zip_str_part ).'%\' ';
	}
	$sql .= ' order by zipcode '.($desc===true ? 'desc' : 'asc');

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le code postal d'une adresse, par défaut celui de l'adresse de facturation.
 *	@param int $usr_id Optionnel, identifiant d'un compte client
 *	@param int $adr_id Optionnel, identifiant d'une adresse
 *	@param $dept Optionnel, par défaut aucun traitement n'est fait sur le résultat, mettre True pour récupérer le département
 *	@return Le code code postal ou le département
 */
function gu_adresses_get_zipcode( $usr_id=0, $adr_id=0, $dept=false ){
	if( !is_numeric($usr_id) || $usr_id<0 ){
		return false;
	}

	if( !is_numeric($adr_id) || $adr_id<0 ){
		return false;
	}

	if( !$usr_id && !$adr_id ){
		return false;
	}

	global $config;

	$sql = '
		select adr_postal_code as zipcode
		from gu_adresses
	';

	if( !$adr_id ){
		$sql .= ' join gu_users on (adr_tnt_id=usr_tnt_id and adr_id=usr_adr_invoices)';
	}

	$sql .= '
		where (adr_tnt_id=0 or adr_tnt_id='.$config['tnt_id'].')
	';

	if( $usr_id ){
		$sql .= ' and adr_usr_id='.$usr_id;
	}

	if( $adr_id ){
		$sql .= ' and adr_id='.$adr_id;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	$zipcode = str_pad( $r['zipcode'], 5, '0', STR_PAD_LEFT );

	return ( $dept ? substr($zipcode, 0, 2) : $zipcode );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le pays d'une adresse dont l'identifiant est donné en paramètre
 *	@param int $adr_id Obligatoire, identifiant d'une adresse
 *	@return bool False si l'adresse n'existe pas ou que le paramètre fourni est faux, le pays dans le cas contraire
 */
function gu_adresses_get_country( $adr_id ){
	global $config;

	if( !is_numeric($adr_id) || $adr_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select adr_country as country
		from gu_adresses
		where adr_tnt_id='.$config['tnt_id'].'
			and adr_id='.$adr_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_array( $res );
	return $r['country'];
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le SIRET d'une adresse.
 *	@param int $adr_id Obligatoire, identifiant d'une adresse
 *	@return Le numero de SIRET renseigné pour celle-ci
 */
function gu_adresses_get_siret( $adr_id ){
	global $config;

	if( !is_numeric($adr_id) || $adr_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select adr_siret as siret
		from gu_adresses
		where adr_tnt_id='.$config['tnt_id'].'
			and adr_id='.$adr_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['siret'];
}
// \endcond


// \cond onlyria
/** Cette fonction permet de récupérer le code de l'état / province de l'adresse donnée.
 *	@param int $adr_id Obligatoire, identifiant d'une adresse
 *	@return Le code de l'état / province de l'adresse
 */
function gu_adresses_get_country_state( $adr_id ){
	global $config;

	if( !is_numeric($adr_id) || $adr_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select adr_country_state as country_state
		from gu_adresses
		where adr_tnt_id='.$config['tnt_id'].'
			and adr_id='.$adr_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['country_state'];
}
// \endcond

/** Cette fonction permet de contrôler que l'adresse n'est pas déjà une adresse existante
 *	@param $columns Obligatoire, tableau contenant les colonnes à vérifier ainsi que leur valeur
 *	@param $is_invoice Optionnel, par défaut le contrôle est réaliser sur les adresse de facturation, mettre false pour toutes les inclures
 *	@return bool True si l'adresse de existe déjà, False dans le cas contraire
 */
function gu_adresses_get_doublon( $columns, $is_invoice=true ){
	if( !is_array($columns) || !sizeof($columns) ){
		return false;
	}

	global $config;

	$cols_accepted  = array( 'adr_address1', 'adr_address2', 'adr_address3', 'adr_city', 'adr_postal_code', 'adr_country');
	$caract_replace = array( '\'', '"', ' ', '\\', '-', '_', '|', '*', '@' );

	$sql_where 		= '';
	$sql_replace 	= '';

	foreach( $caract_replace as $one_caract ){
		if( trim($sql_replace) == '' ){
			$sql_replace = 'replace(#REPLACE#, "'.addslashes($one_caract).'", "")';
		}else{
			$sql_replace = str_replace( '#REPLACE#', 'replace(#REPLACE#, "'.addslashes($one_caract).'", "")', $sql_replace );
		}
	}

	foreach( $columns as $col=>$val ){
		if( !in_array($col, $cols_accepted) ){
			continue;
		}

		$sql_where .= ' and lower('.str_replace( '#REPLACE#', $col, $sql_replace ).') = lower("'.addslashes( str_replace($caract_replace, '', $val) ).'")';
	}

	if( trim($sql_where) == '' ){
		return false;
	}

	$sql = '
		select 1
		from gu_adresses
		where adr_tnt_id = '.$config['tnt_id'].'
			'.$sql_where.'
	';

	if( $is_invoice ){
		$sql .= '
			and exists (
				select 1
				from gu_users
				where usr_tnt_id = '.$config['tnt_id'].'
					and usr_id = adr_usr_id
					and usr_date_deleted is null
					and usr_adr_invoices = adr_id
			)
		';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

// \cond onlyria
/** Cette fonction vérifie la cohérence d'une adresse
 *	Pour qu'une adresse soit cohérente, il faut que le nom de la ville et/ou son code INSEE corresponde au code postal
 *	@param string $zipcode Obligatoire, code postal
 *	@param string $country Obligatoire, nom complet du pays
 *	@param int $insee ce paramètre n'est plus utilisé
 *	@param string $city_name Facultatif, nom de la ville
 *
 *	@return bool True si adresse valide, False sinon
 */
function gu_adresses_city_match( $zipcode, $country, $insee=false, $city_name=false ){
	$code_country = sys_countries_get_code($country);
	if (trim($code_country) == '') {
		return false;
	}

	$array_france_code = array('FR', 'GP', 'GF', 'MQ', 'YT', 'MC', 'NC', 'PF', 'RE', 'BL', 'MF', 'PM');
	if (!in_array($code_country, $array_france_code)){
		return true; // pays étranger
	}

	$res = gu_adresses_get_distinct_cities($zipcode, $city_name);
	return $res && ria_mysql_num_rows($res);
}
// \endcond

// \cond onlyria
/** Détermine si le pays et le code postal forment un couple cohérent dans le cadre d'une boutique souhaitant détecter la France hors-métropole (DOM-TOM)
 *	Cette fonction ne détecte pas la cohérence sur des codes postaux étrangers
 *	@param string $country Pays de l'utilisateur
 *	@param string $zipcode Code postal de l'utilisateur
 *	@return bool True si les informations sont cohérentes, False sinon
 */
function gu_adresses_zipcode_match( $country, $zipcode ){

	if( strtolower(trim($country))=='france' && ( !is_numeric($zipcode) || $zipcode<1000 || $zipcode>=97000 ) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction vérifie qu'un code postal existe bien
 *	@param string $zipcode Obligatoire, code postal à vérifier
 *	@return bool True en cas de succès, False en cas d'échec
 */
function gu_adresses_zipcode_exists( $zipcode ){
	if( trim($zipcode)=='' ) return false;
	if( strlen($zipcode)<4 ) return false;

	if( strlen($zipcode)==4 ){
		$zipcode = '0'.$zipcode;
	}

	return ria_mysql_num_rows( ria_mysql_query('select 1 from gu_adresses_cities where city_zipcode=\''.addslashes($zipcode).'\'') )>0;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récuperer la région et le département d'un utilisateur.
 *	@param int $usr Obligatoire, identifiant d'un utilisateur
 *	@param string $sort Facultatif paramètre de tri ('departement', 'region', 'ville' ou 'code_postal')
 *	@param string $desc_sort Facultatif détermine l'ordre de tri
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- usr : Identifiant de l'utilisateur
 *		- dpt_name : Nom du département
 *		- rgn_name : Nom de la région
 *		- rgn_id : Identifiant de la région
 */
function gu_adresses_location_get( $usr, $sort='', $desc_sort=false ) {
	global $config;

	if( is_array($usr) ){
		if( !sizeof($usr) ){
			return false;
		}else{
			foreach( $usr as $u ){
				if( !is_numeric($u) || $u<=0 )
					return false;
			}
		}
	}elseif( !is_numeric($usr) || $usr<=0 ){
		return false;
	}else{
		$usr = array( $usr );
	}

	$sql = '
		select
			ad.adr_usr_id as usr, dp.dzn_name as dpt_name, rg.dzn_name as rgn_name, rg.dzn_id as rgn_id
		 from
			sys_zones dp
			join sys_zones rg
				on dp.dzn_parent_id=rg.dzn_id
			join sys_zones zip
				on dp.dzn_id=zip.dzn_parent_id
			join gu_adresses ad
				on ad.adr_postal_code=zip.dzn_code
		 where
			dp.dzn_type_id='._ZONE_DPT_FRANCE.' and
			zip.dzn_type_id='._ZONE_ZIPCODES.' and
			rg.dzn_type_id='._ZONE_RGN_FRANCE.' and
			adr_tnt_id = '.$config['tnt_id'].' and
			adr_usr_id in ( '.implode( ',', $usr ).' )
	';

	switch( $sort ){
		case 'departement':
			$sql .= ' order by dpt_name asc';
			break;
		case 'region':
			$sql .= ' order by rgn_name asc';
			break;
		case 'code_postal':
			$sql .= ' order by ad.adr_postal_code asc';
			break;
		case 'ville':
			$sql .= ' order by ad.adr_city asc';
			break;
	}

	return ria_mysql_query($sql);
}
// \endcond

/// @}

