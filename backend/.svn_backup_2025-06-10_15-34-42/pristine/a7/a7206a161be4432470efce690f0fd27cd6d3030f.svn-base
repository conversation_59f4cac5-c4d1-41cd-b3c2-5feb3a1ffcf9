<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/datastore/v1/query.proto

namespace Google\Cloud\Datastore\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\Datastore\V1\EntityResult\ResultType instead.
     * @deprecated
     */
    class EntityResult_ResultType {}
}
class_exists(EntityResult\ResultType::class);
@trigger_error('Google\Cloud\Datastore\V1\EntityResult_ResultType is deprecated and will be removed in the next major release. Use Google\Cloud\Datastore\V1\EntityResult\ResultType instead', E_USER_DEPRECATED);

