<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AC' => 'i-Ascension Island',
  'AD' => 'i-Andorra',
  'AE' => 'i-United Arab Emirates',
  'AF' => 'i-Afghanistan',
  'AG' => 'i-Antigua ne-Barbuda',
  'AI' => 'i-Anguilla',
  'AL' => 'i-Albania',
  'AM' => 'i-Armenia',
  'AO' => 'i-Angola',
  'AQ' => 'i-Antarctica',
  'AR' => 'i-Argentina',
  'AS' => 'i-American Samoa',
  'AT' => 'i-Austria',
  'AU' => 'i-Australia',
  'AW' => 'i-Aruba',
  'AX' => 'i-Åland Islands',
  'AZ' => 'i-Azerbaijan',
  'BA' => 'i-Bosnia ne-Herzegovina',
  'BB' => 'i-Barbados',
  'BD' => 'i-Bangladesh',
  'BE' => 'i-Belgium',
  'BF' => 'i-Burkina Faso',
  'BG' => 'i-Bulgaria',
  'BH' => 'i-Bahrain',
  'BI' => 'i-Burundi',
  'BJ' => 'i-Benin',
  'BL' => 'i-Saint Barthélemy',
  'BM' => 'i-Bermuda',
  'BN' => 'i-Brunei',
  'BO' => 'i-Bolivia',
  'BQ' => 'i-Caribbean Netherlands',
  'BR' => 'i-Brazil',
  'BS' => 'i-Bahamas',
  'BT' => 'i-Bhutan',
  'BW' => 'iBotswana',
  'BY' => 'i-Belarus',
  'BZ' => 'i-Belize',
  'CA' => 'i-Canada',
  'CC' => 'i-Cocos (Keeling) Islands',
  'CD' => 'i-Congo - Kinshasa',
  'CF' => 'i-Central African Republic',
  'CG' => 'i-Congo - Brazzaville',
  'CH' => 'i-Switzerland',
  'CI' => 'i-Côte d’Ivoire',
  'CK' => 'i-Cook Islands',
  'CL' => 'i-Chile',
  'CM' => 'i-Cameroon',
  'CN' => 'i-China',
  'CO' => 'i-Colombia',
  'CR' => 'i-Costa Rica',
  'CU' => 'i-Cuba',
  'CV' => 'i-Cape Verde',
  'CW' => 'i-Curaçao',
  'CX' => 'i-Christmas Island',
  'CY' => 'i-Cyprus',
  'CZ' => 'i-Czechia',
  'DE' => 'i-Germany',
  'DG' => 'i-Diego Garcia',
  'DJ' => 'i-Djibouti',
  'DK' => 'i-Denmark',
  'DM' => 'i-Dominica',
  'DO' => 'i-Dominican Republic',
  'DZ' => 'i-Algeria',
  'EA' => 'i-Cueta ne-Melilla',
  'EC' => 'i-Ecuador',
  'EE' => 'i-Estonia',
  'EG' => 'i-Egypt',
  'EH' => 'i-Western Sahara',
  'ER' => 'i-Eritrea',
  'ES' => 'i-Spain',
  'ET' => 'i-Ethiopia',
  'FI' => 'i-Finland',
  'FJ' => 'i-Fiji',
  'FK' => 'i-Falkland Islands',
  'FM' => 'i-Micronesia',
  'FO' => 'i-Faroe Islands',
  'FR' => 'i-France',
  'GA' => 'i-Gabon',
  'GB' => 'i-United Kingdom',
  'GD' => 'i-Grenada',
  'GE' => 'i-Georgia',
  'GF' => 'i-French Guiana',
  'GG' => 'i-Guernsey',
  'GH' => 'i-Ghana',
  'GI' => 'i-Gibraltar',
  'GL' => 'i-Greenland',
  'GM' => 'i-Gambia',
  'GN' => 'i-Guinea',
  'GP' => 'i-Guadeloupe',
  'GQ' => 'i-Equatorial Guinea',
  'GR' => 'i-Greece',
  'GS' => 'i-South Georgia ne-South Sandwich Islands',
  'GT' => 'i-Guatemala',
  'GU' => 'i-Guam',
  'GW' => 'i-Guinea-Bissau',
  'GY' => 'i-Guyana',
  'HK' => 'i-Hong Kong SAR China',
  'HN' => 'i-Honduras',
  'HR' => 'i-Croatia',
  'HT' => 'i-Haiti',
  'HU' => 'i-Hungary',
  'IC' => 'i-Canary Islands',
  'ID' => 'i-Indonesia',
  'IE' => 'i-Ireland',
  'IL' => 'kwa-Israel',
  'IM' => 'i-Isle of Man',
  'IN' => 'i-India',
  'IO' => 'i-British Indian Ocean Territory',
  'IQ' => 'i-Iraq',
  'IR' => 'i-Iran',
  'IS' => 'i-Iceland',
  'IT' => 'i-Italy',
  'JE' => 'i-Jersey',
  'JM' => 'i-Jamaica',
  'JO' => 'i-Jordan',
  'JP' => 'i-Japan',
  'KE' => 'i-Kenya',
  'KG' => 'i-Kyrgyzstan',
  'KH' => 'i-Cambodia',
  'KI' => 'i-Kiribati',
  'KM' => 'i-Comoros',
  'KN' => 'i-Saint Kitts ne-Nevis',
  'KP' => 'i-North Korea',
  'KR' => 'i-South Korea',
  'KW' => 'i-Kuwait',
  'KY' => 'i-Cayman Islands',
  'KZ' => 'i-Kazakhstan',
  'LA' => 'i-Laos',
  'LB' => 'i-Lebanon',
  'LC' => 'i-Saint Lucia',
  'LI' => 'i-Liechtenstein',
  'LK' => 'i-Sri Lanka',
  'LR' => 'i-Liberia',
  'LS' => 'iLesotho',
  'LT' => 'i-Lithuania',
  'LU' => 'i-Luxembourg',
  'LV' => 'i-Latvia',
  'LY' => 'i-Libya',
  'MA' => 'i-Morocco',
  'MC' => 'i-Monaco',
  'MD' => 'i-Moldova',
  'ME' => 'i-Montenegro',
  'MF' => 'i-Saint Martin',
  'MG' => 'i-Madagascar',
  'MH' => 'i-Marshall Islands',
  'MK' => 'i-Macedonia',
  'ML' => 'iMali',
  'MM' => 'i-Myanmar (Burma)',
  'MN' => 'i-Mongolia',
  'MO' => 'i-Macau SAR China',
  'MP' => 'i-Northern Mariana Islands',
  'MQ' => 'i-Martinique',
  'MR' => 'i-Mauritania',
  'MS' => 'i-Montserrat',
  'MT' => 'i-Malta',
  'MU' => 'i-Mauritius',
  'MV' => 'i-Maldives',
  'MW' => 'iMalawi',
  'MX' => 'i-Mexico',
  'MY' => 'i-Malaysia',
  'MZ' => 'i-Mozambique',
  'NA' => 'i-Namibia',
  'NC' => 'i-New Caledonia',
  'NE' => 'i-Niger',
  'NF' => 'i-Norfolk Island',
  'NG' => 'i-Nigeria',
  'NI' => 'i-Nicaragua',
  'NL' => 'i-Netherlands',
  'NO' => 'i-Norway',
  'NP' => 'i-Nepal',
  'NR' => 'i-Nauru',
  'NU' => 'i-Niue',
  'NZ' => 'i-New Zealand',
  'OM' => 'i-Oman',
  'PA' => 'i-Panama',
  'PE' => 'i-Peru',
  'PF' => 'i-French Polynesia',
  'PG' => 'i-Papua New Guinea',
  'PH' => 'i-Philippines',
  'PK' => 'i-Pakistan',
  'PL' => 'i-Poland',
  'PM' => 'i-Saint Pierre kanye ne-Miquelon',
  'PN' => 'i-Pitcairn Islands',
  'PR' => 'i-Puerto Rico',
  'PS' => 'i-Palestinian Territories',
  'PT' => 'i-Portugal',
  'PW' => 'i-Palau',
  'PY' => 'i-Paraguay',
  'QA' => 'i-Qatar',
  'RE' => 'i-Réunion',
  'RO' => 'i-Romania',
  'RS' => 'i-Serbia',
  'RU' => 'i-Russia',
  'RW' => 'i-Rwanda',
  'SA' => 'i-Saudi Arabia',
  'SB' => 'i-Solomon Islands',
  'SC' => 'i-Seychelles',
  'SD' => 'i-Sudan',
  'SE' => 'i-Sweden',
  'SG' => 'i-Singapore',
  'SH' => 'i-St. Helena',
  'SI' => 'i-Slovenia',
  'SJ' => 'i-Svalbard ne-Jan Mayen',
  'SK' => 'i-Slovakia',
  'SL' => 'i-Sierra Leone',
  'SM' => 'i-San Marino',
  'SN' => 'i-Senegal',
  'SO' => 'i-Somalia',
  'SR' => 'i-Suriname',
  'SS' => 'i-South Sudan',
  'ST' => 'i-São Tomé kanye ne-Príncipe',
  'SV' => 'i-El Salvador',
  'SX' => 'i-Sint Maarten',
  'SY' => 'i-Syria',
  'SZ' => 'i-Swaziland',
  'TA' => 'i-Tristan da Cunha',
  'TC' => 'i-Turks ne-Caicos Islands',
  'TD' => 'i-Chad',
  'TF' => 'i-French Southern Territories',
  'TG' => 'i-Togo',
  'TH' => 'i-Thailand',
  'TJ' => 'i-Tajikistan',
  'TK' => 'i-Tokelau',
  'TL' => 'i-Timor-Leste',
  'TM' => 'i-Turkmenistan',
  'TN' => 'i-Tunisia',
  'TO' => 'i-Tonga',
  'TR' => 'i-Turkey',
  'TT' => 'i-Trinidad ne-Tobago',
  'TV' => 'i-Tuvalu',
  'TW' => 'i-Taiwan',
  'TZ' => 'i-Tanzania',
  'UA' => 'i-Ukraine',
  'UG' => 'i-Uganda',
  'UM' => 'i-U.S. Minor Outlying Islands',
  'US' => 'i-United States',
  'UY' => 'i-Uruguay',
  'UZ' => 'i-Uzbekistan',
  'VA' => 'i-Vatican City',
  'VC' => 'i-Saint Vincent ne-Grenadines',
  'VE' => 'i-Venezuela',
  'VG' => 'i-British Virgin Islands',
  'VI' => 'i-U.S. Virgin Islands',
  'VN' => 'i-Vietnam',
  'VU' => 'i-Vanuatu',
  'WF' => 'i-Wallis ne-Futuna',
  'WS' => 'i-Samoa',
  'XK' => 'i-Kosovo',
  'YE' => 'i-Yemen',
  'YT' => 'i-Mayotte',
  'ZA' => 'iNingizimu Afrika',
  'ZM' => 'i-Zambia',
  'ZW' => 'iZimbabwe',
);
