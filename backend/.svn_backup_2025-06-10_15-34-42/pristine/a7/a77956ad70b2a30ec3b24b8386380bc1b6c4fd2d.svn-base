<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

use Twig\Compiler;

/**
 * Represents a node in the AST.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @deprecated since 1.12 (to be removed in 3.0)
 */
interface Twig_NodeInterface extends \Countable, \IteratorAggregate
{
    /**
     * Compiles the node to PHP.
     */
    public function compile(Compiler $compiler);

    /**
     * @deprecated since 1.27 (to be removed in 2.0)
     */
    public function getLine();

    public function getNodeTag();
}
