<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagInterface;
use SchemaDotOrg\Tags\TagReview;
use SchemaDotOrg\Tags\TagAggregateRating;

/**
 * \ingroup SchemaTag
 * @{
 */
/**
 * \brief Cette classe correspond au tag SearchAction complémente le tag WebSite
 */
class TagRecipe extends TagBase
{
	/**
	 * Type de balise
	 *
	 * @var string $type
	 */
	protected $type = 'Recipe';

	/**
	 * ajoute l'auteur de la recette
	 *
	 * @param string $author Nom de l'auteur
	 * @return self Retourne l'instance
	 */
	public function setAuthor($author){
		$this->addField('author', $author);

		return $this;
	}

	/**
	 * Ajoute la description de la recette
	 *
	 * @param string $description Description de la recette
	 * @return self retourne l'instance
	 */
	public function setDescription($description){
		$this->addField('description', $description);

		return $this;
	}

	/**
	 * Ajoute un ingrédient a la recette
	 *
	 * @param string $ingredient Ingrédient dans la recette
	 * @return self retourne l'instance
	 */
	public function addIngredient($ingredient){
		$this->fields['recipeIngredient'][] = $ingredient;

		return $this;
	}

	/**
	 * Ajoute le nom de la recette
	 *
	 * @param string $name Nom de la recette
	 * @return self Retourne l'instance
	 */
	public function setName($name){
		$this->addField('name', $name);

		return $this;
	}

	/**
	 * Ajoute une étape de préparation
	 *
	 * @param string $instruction Instruction de préparation
	 * @return self Retourne l'instance
	 */
	public function addRecipeInstructions($instruction){
		$this->fields['recipeInstructions'][] = $instruction;

		return $this;
	}

	/**
	 * La recette est pour combien de personne
	 *
	 * @param integer $number_of_people Nombre de personne
	 * @return self Retourne l'instance
	 */
	public function forHowmany($number_of_people){
		$this->addField('recipeYield', $number_of_people . 'personne'.($number_of_people>1 ? 's' : '') );

		return $this;
	}

	/**
	 * ajoute les mots clés à la recette
	 *
	 * @param int $keywords mots clés de la de la recette
	 * @return self retourne l'instance
	 */
	Public function setKeywords($keywords){
		$this->fields['keywords'][] = $keywords;

		return $this;
	}

	/**
	 * ajoute le temps de prépartion de la recette
	 *
	 * @param int $min temps de préparation de la recette en minutes
	 * @return self retourne l'instance
	 */
	Public function setPrepTime($min){
		$this->addField('prepTime', 'PT'.$min.'M');

		return $this;
	}

	/**
	 * ajoute le temps de prépartion de la recette
	 *
	 * @param int $min temps de cuisson de la recette en minutes
	 * @return self retourne l'instance
	 */
	Public function setCookTime($min){
		$this->addField('cookTime', 'PT'.$min.'M');

		return $this;
	}

	/**
	 * Date de publication
	 *
	 * @param DateTime $date date de publication
	 * @return self retourne l'instance
	 */
	public function setDatePublished(\DateTime $date){
		$this->addField('datePublished', $date);

		return $this;
	}

	/**
	 * Ajoute une url d'image
	 *
	 * @param string $img_url url de l'image
	 * @return self retourne l'instance
	 */
	public function addImage($img_url){
		$this->fields['image'][] = $img_url;

		return $this;
	}

	/**
	 * Ajoute une review au produit
	 *
	 * @param array|TagReview $review
	 * @return self retourne l'instance
	 */
	public function addReview($review){
		if (!($review instanceof TagReview)) {
			$review = new TagReview($review);
		}
		$this->fields['review'][] = $review;

		return $this;
	}

	/**
	 * Ajoute TagAggregateRating
	 *
	 * @param TagAggregateRating $AggregateRating
	 * @return self retourne l'instance
	 */
	public function setAggregateRating(TagAggregateRating $AggregateRating){
		$this->addField('aggregateRating', $AggregateRating);

		return $this;
	}
}
/// @}