<?php
namespace SchemaDotOrg\Tags;

use SchemaDotOrg\Tags\TagBase;
use SchemaDotOrg\Tags\TagRating;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag TagReview qui complémente TagProduct
 */
class TagReview extends TagBase {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	protected $type = "Review";

	/**
	 * Résultat de prd_reviews_get ou tableau contenant les mêmes champs
	 *
	 * @var array $review
	 */
	private $review;

	/**
	 * Undocumented function
	 *
	 * @param array $review Résultat de prd_reviews_get ou tableau contenant les mêmes champs
	 */
	public function __construct(array $review){
		$this->review = $review;
		$this->init();
	}

	/**
	 * Permet de retourner le type de tag
	 *
	 * @return string le type de tag
	 */
	public function type(){
		return $this->type;
	}

	/**
	 * Ajoute le nombre de vote
	 *
	 * @param TagRating $TagRating
	 * @return self retourne l'instance
	 */
	public function setReviewRating(TagRating $TagRating){
		$this->addField('reviewRating', $TagRating);

		return $this;
	}

	/**
	 * Ajoute la mayenne des notes
	 *
	 * @return slef Retourne l'instance
	 */
	private function init(){

		$name = $this->review['usr_firstname'].' ' .$this->review['usr_lastname'];
		if (!trim($name)) {
			$name = $this->review['usr_email'];
		}
		$this->addField('author', $name);

		$date = \DateTime::createFromFormat('d/m/Y \à H:i', $this->review['date_published']);
		if ($date instanceof DateTime) {
			$this->addField('datePublished', $date->format('Y-m-d'));
		}
		$this->addField('name', $this->review['name']);
		$this->addField('description', $this->review['desc']);

		$this->setReviewRating(new TagRating(5, $this->review['note'], 1));

		return $this;
	}
}