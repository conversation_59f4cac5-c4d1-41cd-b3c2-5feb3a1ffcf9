<?php

require_once('db.inc.php');
require_once('products.inc.php');
require_once('users.inc.php');
require_once('email.inc.php');

/**	\defgroup model_erratums Erratums
 *	\ingroup tools
 *	Ce module comprend les fonctions nécessaires à la gestion d'une erratum sur un catalogue
 *	@{
 */

 // \cond onlyria
/**	Cette fonction permet l'ajout d'un erratum
 *	@param int $prd Obligatoire, identifiant du produit sur lequel porte l'erratum
 *	@param int $usr Obligatoire, identifiant de l'utilisateur déposant l'erratum
 *	@param string $desc Obligatoire, contenu/description de l'erratum
 *	@param int $type Facultatif, détermine le type d'erratum (site Internet : 1, catalogue papier : 2)
 *	@return int l'identifiant attribué a l'erratum en cas de succès, false en cas d'échec
 */
function cat_erratums_add( $prd, $usr, $desc, $type=false ){
	global $config;
	$http_host_ria = 'riashop-'.$_SERVER['HTTP_HOST'];

	if( !prd_products_exists($prd) ) return false;
	if( !trim($desc) ) return false;
	if( !gu_users_exists($usr) ) return false;
	if( $type!=1 && $type!=2 ) $type = 'null';

	$res = ria_mysql_query('insert into cat_erratums (err_tnt_id,err_prd_id,err_usr_id,err_desc,err_date_created,err_type_id) values ('.$config['tnt_id'].','.$prd.','.$usr.',\''.addslashes($desc).'\',now(),'.$type.')');
	if( !$res ){
		return false;
	}
	$id = ria_mysql_insert_id();

	// Envoi une notification par email au modérateur
	if( $config['cat_erratums_moderation']=='before' || $config['cat_erratums_moderation']=='after' ){
		$product = ria_mysql_fetch_array(prd_products_get($prd));
		$ruser = gu_users_get($usr);
		if( !ria_mysql_num_rows($ruser) ) return false;
		$user = ria_mysql_fetch_array($ruser);

		// Mets en forme la désignation du produit
		$product_name = $product['name'];
		if( $product['ref']!='' )
			$product_name .= ' (ref : '.$product['ref'].')';

		// Détermine l'identité de l'auteur du commentaire
		$sender = gu_users_get_name($user['id']).' <'.$user['email'].'>';

		$rcat = prd_products_categories_get($prd);
		if( !ria_mysql_num_rows($rcat) ) return false;
		$cat = $rcat['cat'];

		// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
		$rcfg = cfg_emails_get('erratum');
		if( !ria_mysql_num_rows($rcfg) ) return false;
		$cfg = ria_mysql_fetch_array($rcfg);

		$email = new Email();
		$email->setSubject( 'Nouvel erratum' );
		//$email->setFrom( $config['site_name'].' <'.$config['email_contact'].'>' );
		$email->setFrom( $sender );
		$email->addTo( $cfg['to'] );
		if( $cfg['cc'] ) $email->addCc( $cfg['cc'] );
		if( $cfg['bcc'] ) $email->addBcc( $cfg['bcc'] );
		if( $cfg['reply-to'] ) $email->setReplyTo( $cfg['reply-to'] );

		$email->addHtml( '
			<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
				<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$id.'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
			</div>
		');

		$email->addHtml( $config['email_html_header'] );
		// Construction du corps du message
		$email->addParagraph( 'Bonjour,' );
		$email->addParagraph('Un nouvel erratum vient d\'être enregistré sur le site '.$config['site_url'].' . Cet avis a été déposé par '.$sender.' à '.date('H:i').' aujourd\'hui. Vous trouverez son commentaire ci-dessous :');

		$email->openTable();
		if( $type==1 || $type==2 ){
			$email->openTableRow();
			$email->addCell( 'S\'applique au : ' );
			$email->addCell( $type==1 ? 'Site Internet' : 'Catalogue papier' );
			$email->closeTableRow();
		}
		$email->openTableRow();
		$email->addCell( 'Produit concerné : ' );
		$email->addCell( $product_name );
		$email->closeTableRow();
		$email->openTableRow();
		$email->addCell( 'Contenu du commentaire : ' );
		$email->addCell( nl2br($desc) );
		$email->closeTableRow();
		$email->closeTable();

		// Indique si l'avis est publié ou non.
		$url_prd = 'https://'.$http_host_ria.'/admin/catalog/product.php?prd='.$prd.'&cat='.$cat.'&tab=reviews&rvw='.$id;
		if( $config['cat_erratums_moderation']=='before' ){
			$email->addParagraph('Conformément au mode de modération choisi, cet avis attend votre approbation pour être affiché dans la boutique. Pour le publier, veuillez vous rendre dans <a href="'.$url_prd.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=erratums&amp;utm_content=prd-reviews">votre interface d\'administration</a>.');
		}else{
			$email->addParagraph('Conformément au mode de modération choisi, cet avis est déjà publié dans la boutique. Si vous souhaitez le retirer, vous pouvez le faire en vous rendant <a href="'.$url_prd.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=erratums&amp;utm_content=prd-reviews">dans votre interface d\'administration</a>.');
		}

		// Indique comment contacter l'auteur de l'avis consommateur.
		$email->addParagraph( "Pour contacter l'auteur de cet avis consommateur, répondez simplement à cet email." );

		$email->addHtml( $config['email_html_footer'] );
		$email->addHtml( '
			<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
				<a style="text-decoration: none" href="https://'.$http_host_ria.'/admin/moderation/index.php?cnt='.$id.'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
			</div>
		');
		$email->send();
	}
	return $id;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour d'un erratum
 *	@param int $id Obligatoire, identifiant de l'erratum à actualiser
 *	@param int $prd Obligatoire, identifiant du produit sur lequel porte l'erratum
 *	@param string $desc Obligatoire, contenu/description de l'erratum
 *	@param int $type Facultatif, type d'erratum. Les valeurs acceptés sont 1 : Site Internet, ou 2 : Catalogue papier
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cat_erratums_update( $id, $prd, $desc, $type=false ){
	global $config;

	if( !cat_erratums_exists($id) ) return false;

	if( !prd_products_exists($prd) ) return false;
	if( !trim($desc) ) return false;
	if( $type!=1 && $type!=2 ) $type = 'null';

	return ria_mysql_query('
		update cat_erratums set
			err_prd_id='.$prd.',
			err_desc=\''.addslashes($desc).'\',
			err_type_id='.$type.'
		where err_tnt_id='.$config['tnt_id'].' and err_id='.$id.'
	');
}
// \endcond

/**	Cette fonction permet le chargement d'un ou plusieurs erratums, éventuellement filtrés en fonction des parametres
 *	optionnels fournis.
 *
 *	@param int $id Facultatif, identifiant d'un erratum sur lequel filtrer le résultat
 *	@param int $prd Facultatif, identifiant d'un produit sur lequel filtrer le résultat
 *	@param bool $publish Facultatif, si true ne retourne que les erratums publiés, si false ne retourne que les erratums non publiés
 *
 *	@return resource un résultat de requete MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de l'erratum
 *			- prd_id : identifiant du produit concerné par l'erratum
 *			- prd_ref : référence du produit concerné par l'erratum
 *			- prd_name : désignation du produit concerné par l'erratum
 *			- usr_id : identifiant de l'utilisateur ayant déposé l'avis
 *			- type_id : type d'erratum (1 ou 2)
 *			- desc : description/contenu de l'erratum
 *			- date_published : date/heure de publication de l'erratum
 */
function cat_erratums_get( $id=0, $prd=0, $publish=null ){
	global $config;

	if( !is_numeric($id) ) return false;
	if( !is_numeric($prd) ) return false;

	$sql = '
		select err_id as id, prd_id, prd_ref, prd_name, err_desc as "desc", err_usr_id as usr_id, err_type_id as type_id,
			date_format(err_date_created,"%d/%m/%Y") as date_created, date_format(err_date_published,"%d/%m/%Y") as date_published
		from cat_erratums, prd_products
		where err_tnt_id='.$config['tnt_id'].'
			and prd_tnt_id=err_tnt_id
			and err_prd_id=prd_id
			and err_date_deleted is null
	';
	if( $id>0 )
		$sql .= ' and err_id='.$id;
	if( $prd )
		$sql .= ' and err_prd_id='.$prd;
	if( $publish!==null ){
		if( $publish )
			$sql .= ' and err_date_published is not null';
		else
			$sql .= ' and err_date_published is null';
	}

	return ria_mysql_query( $sql );
}

// \cond onlyria
/**	Cette fonction permet la publication d'un erratum
 *	@param int $id Identifiant de l'erratum à publier
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cat_erratums_publish( $id ){
	global $config;
	if( !is_numeric($id) ) return false;
	return ria_mysql_query('update cat_erratums set err_date_published=now() where err_tnt_id='.$config['tnt_id'].' and err_id='.$id.' and err_date_deleted is null');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification d'un identifiant d'erratum
 *	@param int $id Obligatoire, l'identifiant de l'erratum a contrôler
 *	@return bool true si l'identifiant est valide et correspond a un erratum enregistré
 *	@return bool false si l'identifiant est invalide ou s'il ne correspond a aucun erratum enregistré
 */
function cat_erratums_exists( $id ){
	global $config;
	if( !is_numeric($id) ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select err_id from cat_erratums where err_tnt_id='.$config['tnt_id'].' and err_id='.$id.' and err_date_deleted is null'))>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'un erratum
 *	@param int $id Obligatoire, identifiant de l'erratum à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function cat_erratums_del( $id ){
	global $config;
	if( !is_numeric($id) ) return false;
	return ria_mysql_query('update cat_erratums set err_date_deleted=now() where err_tnt_id='.$config['tnt_id'].' and err_id='.$id.' and err_date_deleted is null');
}
// \endcond

/// @}
