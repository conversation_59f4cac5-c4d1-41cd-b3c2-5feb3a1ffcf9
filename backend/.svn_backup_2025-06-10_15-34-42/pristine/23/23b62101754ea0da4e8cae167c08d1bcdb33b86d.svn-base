<?php

namespace Muglug\PackageVersions;

/**
 * This class is generated by muglug/package-versions-56, specifically by
 * @see \Muglug\PackageVersions\Installer
 *
 * This file is overwritten at every run of `composer install` or `composer update`.
 */
final class Versions
{
    public static $VERSIONS = array (
  'gettext/gettext' => 'v4.8.2@e474f872f2c8636cf53fd283ec4ce1218f3d236a',
  'gettext/languages' => '2.6.0@38ea0482f649e0802e475f0ed19fa993bcb7a618',
  'paragonie/random_compat' => 'v2.0.18@0a58ef6e3146256cc3dc7cc393927bcc7d1b72db',
  'phpfastcache/riak-client' => '3.4.3@d771f75d16196006604a30bb15adc1c6a9b0fcc9',
  'phpmailer/phpmailer' => 'v6.1.3@a25ae38e03de4ee4031725498a600012364787c7',
  'psr/container' => '1.0.0@b7ce3b176482dbbc1245ebf52b181af44c2cf55f',
  'psr/log' => '1.1.2@446d54b4cb6bf489fc9d75f55843658e6f25d801',
  'robrichards/xmlseclibs' => '3.0.4@0a53d3c3aa87564910cae4ed01416441d3ae0db5',
  'simplesamlphp/composer-module-installer' => 'v1.1.6@b70414a2112fe49e97a7eddd747657bd8bc38ef0',
  'simplesamlphp/saml2' => 'v3.4.2@3806d276edb066c60aa3d748ffd0681d92ffbda7',
  'simplesamlphp/simplesamlphp-module-adfs' => 'v0.9.5@3ac7d15825e609152ca04faceea80ee0db3afcb1',
  'simplesamlphp/simplesamlphp-module-authcrypt' => 'v0.9.1@cc2950cf710933063192e883ba2804321b8af6db',
  'simplesamlphp/simplesamlphp-module-authfacebook' => 'v0.9.2@661cc25ac21ea422552a2394ea35ce9e8873ce39',
  'simplesamlphp/simplesamlphp-module-authorize' => 'v0.9.0@470470373f352d0682b7e57ff41626fd0453c7c3',
  'simplesamlphp/simplesamlphp-module-authtwitter' => 'v0.9.1@29a15e58061222632fea9eb2c807aef5e2c0d54a',
  'simplesamlphp/simplesamlphp-module-authwindowslive' => 'v0.9.1@f40aecec6c0adaedb6693309840c98cec783876e',
  'simplesamlphp/simplesamlphp-module-authx509' => 'v0.9.1@32f4fb3822b4325fdccbff824996e82fa1042e0d',
  'simplesamlphp/simplesamlphp-module-authyubikey' => 'v0.9.1@8c27bfeb4981d2e6fa40a831e945f40c5a4ad3d2',
  'simplesamlphp/simplesamlphp-module-cas' => 'v0.9.1@63b72e4600550c507cdfc32fdd208ad59a64321e',
  'simplesamlphp/simplesamlphp-module-cdc' => 'v0.9.1@16a5bfac7299e04e5feb472af328e07598708166',
  'simplesamlphp/simplesamlphp-module-consent' => 'v0.9.4@bb9e9af9ae5ffaf7bf5047793be9401f873cb91d',
  'simplesamlphp/simplesamlphp-module-consentadmin' => 'v0.9.1@466e8d0d751f0080162d78e63ab2e125b24d17a1',
  'simplesamlphp/simplesamlphp-module-discopower' => 'v0.9.2@016dc96a3f83414f49cbfa40079c0b137fa656b2',
  'simplesamlphp/simplesamlphp-module-exampleattributeserver' => 'v1.0.0@63e0323e81c32bc3c9eaa01ea45194bb10153708',
  'simplesamlphp/simplesamlphp-module-expirycheck' => 'v0.9.1@ee92306763729b05256ced90acb0f7eca20b7e3f',
  'simplesamlphp/simplesamlphp-module-ldap' => 'v0.9.4@21301b3fcd7bc6147acdc673ada9e17e5282e908',
  'simplesamlphp/simplesamlphp-module-memcachemonitor' => 'v0.9.1@0e08e87707cd7b1fb91bbcf65cc454d8849571b0',
  'simplesamlphp/simplesamlphp-module-memcookie' => 'dev-master@fb17b5e5ff507fac166df3fe6d79b5c27feec3c1',
  'simplesamlphp/simplesamlphp-module-metarefresh' => 'v0.9.3@3451ed118b7ebf7ba2657cff267c82c684b19ebe',
  'simplesamlphp/simplesamlphp-module-negotiate' => 'v0.9.4@08998d51b38592c5e90bfdcb61c91a8255b35f5f',
  'simplesamlphp/simplesamlphp-module-oauth' => 'v0.9.1@17450420b5d4c1810055b8ab655cc4d045a0c477',
  'simplesamlphp/simplesamlphp-module-preprodwarning' => 'v0.9.1@925ef60b51a7230286b390c0abc0e815d8b9768e',
  'simplesamlphp/simplesamlphp-module-radius' => 'v0.9.3@36bd0f39f9a13f7eb96ead97c97c3634aa1c3f2d',
  'simplesamlphp/simplesamlphp-module-riak' => 'v0.9.1@c1a9d9545cb4e05b9205b34624850bb777aca991',
  'simplesamlphp/simplesamlphp-module-sanitycheck' => 'v0.9.0@1efbeab5df8e616522690bcc6e49a99436a748b9',
  'simplesamlphp/simplesamlphp-module-smartattributes' => 'v0.9.1@b45d3ecd916e359a9cae05f9ae9df09b5c42f4e6',
  'simplesamlphp/simplesamlphp-module-sqlauth' => 'v0.9.1@31bce8763ad97f4b4473e4ad4a5a96ddc136ef6b',
  'simplesamlphp/simplesamlphp-module-statistics' => 'v0.9.4@1bb1e46921d8dc84707bc9cd3c307c8abd723ac7',
  'simplesamlphp/twig-configurable-i18n' => 'v2.2@b036c134157ce40ed66da2fc9d01f63e3b1d3abd',
  'symfony/config' => 'v3.4.36@a599a867d0e4a07c342b5f1e656b3915a540ddbe',
  'symfony/debug' => 'v3.4.36@f72e33fdb1170b326e72c3157f0cd456351dd086',
  'symfony/dependency-injection' => 'v3.4.36@0d201916bfb3af939fec3c0c8815ea16c60ac1a2',
  'symfony/event-dispatcher' => 'v3.4.36@f9031c22ec127d4a2450760f81a8677fe8a10177',
  'symfony/filesystem' => 'v3.4.36@00cdad0936d06fab136944bc2342b762b1c3a4a2',
  'symfony/http-foundation' => 'v3.4.36@d2d0cfe8e319d9df44c4cca570710fcf221d4593',
  'symfony/http-kernel' => 'v3.4.36@c42c8339acb28cfff0fb1786948db4d23d609ff7',
  'symfony/polyfill-ctype' => 'v1.13.1@f8f0b461be3385e56d6de3dbb5a0df24c0c275e3',
  'symfony/polyfill-mbstring' => 'v1.13.1@7b4aab9743c30be783b73de055d24a39cf4b954f',
  'symfony/polyfill-php56' => 'v1.13.1@53dd1cdf3cb986893ccf2b96665b25b3abb384f4',
  'symfony/polyfill-php70' => 'v1.13.1@af23c7bb26a73b850840823662dda371484926c4',
  'symfony/polyfill-util' => 'v1.13.1@964a67f293b66b95883a5ed918a65354fcd2258f',
  'symfony/routing' => 'v3.4.36@b689ccd48e234ea404806d94b07eeb45f9f6f06a',
  'symfony/yaml' => 'v3.4.36@dab657db15207879217fc81df4f875947bf68804',
  'twig/extensions' => 'v1.5.4@57873c8b0c1be51caa47df2cdb824490beb16202',
  'twig/twig' => 'v1.42.4@e587180584c3d2d6cb864a0454e777bb6dcb6152',
  'webmozart/assert' => '1.5.0@88e6d84706d09a236046d686bbea96f07b3a34f4',
  'whitehat101/apr1-md5' => 'v1.0.0@8b261c9fc0481b4e9fa9d01c6ca70867b5d5e819',
  'composer/ca-bundle' => '1.2.4@10bb96592168a0f8e8f6dcde3532d9fa50b0b527',
  'composer/xdebug-handler' => '1.4.0@cbe23383749496fe0f373345208b79568e4bc248',
  'doctrine/instantiator' => '1.0.5@8e884e78f9f0eb1329e445619e04456e64d8051d',
  'guzzlehttp/guzzle' => '6.5.0@dbc2bc3a293ed6b1ae08a3651e2bfd213d19b6a5',
  'guzzlehttp/promises' => 'v1.3.1@a59da6cf61d80060647ff4d3eb2c03a2bc694646',
  'guzzlehttp/psr7' => '1.6.1@239400de7a173fe9901b9ac7c06497751f00727a',
  'mikey179/vfsstream' => 'v1.6.8@231c73783ebb7dd9ec77916c10037eff5a2b6efe',
  'muglug/package-versions-56' => '1.2.4@a67bed26deaaf9269a348e53063bc8d4dcc60ffd',
  'myclabs/deep-copy' => '1.7.0@3b8a3a99ba1f6a3952ac2747d989303cbd6b7a3e',
  'nikic/php-parser' => 'v3.1.5@bb87e28e7d7b8d9a7fda231d37457c9210faf6ce',
  'openlss/lib-array2xml' => '0.5.1@c8b5998a342d7861f2e921403f44e0a2f3ef2be0',
  'php-coveralls/php-coveralls' => 'v2.2.0@3e6420fa666ef7bae5e750ddeac903153e193bae',
  'php-cs-fixer/diff' => 'v1.3.0@78bb099e9c16361126c86ce82ec4405ebab8e756',
  'phpdocumentor/reflection-common' => '1.0.1@21bdeb5f65d7ebf9f43b1b25d404f87deab5bfb6',
  'phpdocumentor/reflection-docblock' => '3.3.2@bf329f6c1aadea3299f08ee804682b7c45b326a2',
  'phpdocumentor/type-resolver' => '0.4.0@9c977708995954784726e25d0cd1dddf4e65b0f7',
  'phpspec/prophecy' => '1.9.0@f6811d96d97bdf400077a0cc100ae56aa32b9203',
  'phpunit/php-code-coverage' => '4.0.8@ef7b2f56815df854e66ceaee8ebe9393ae36a40d',
  'phpunit/php-file-iterator' => '1.4.5@730b01bc3e867237eaac355e06a36b85dd93a8b4',
  'phpunit/php-text-template' => '1.2.1@31f8b717e51d9a2afca6c9f046f5d69fc27c8686',
  'phpunit/php-timer' => '1.0.9@3dcf38ca72b158baf0bc245e9184d3fdffa9c46f',
  'phpunit/php-token-stream' => '1.4.12@1ce90ba27c42e4e44e6d8458241466380b51fa16',
  'phpunit/phpunit' => '5.7.27@b7803aeca3ccb99ad0a506fa80b64cd6a56bbc0c',
  'phpunit/phpunit-mock-objects' => '3.4.4@a23b761686d50a560cc56233b9ecf49597cc9118',
  'psr/http-message' => '1.0.1@f6561bf28d520154e4b0ec72be95418abe6d9363',
  'ralouphie/getallheaders' => '3.0.3@120b605dfeb996808c31b6477290a714d356e822',
  'sebastian/code-unit-reverse-lookup' => '1.0.1@4419fcdb5eabb9caa61a27c7a1db532a6b55dd18',
  'sebastian/comparator' => '1.2.4@2b7424b55f5047b47ac6e5ccb20b2aea4011d9be',
  'sebastian/diff' => '1.4.3@7f066a26a962dbe58ddea9f72a4e82874a3975a4',
  'sebastian/environment' => '2.0.0@5795ffe5dc5b02460c3e34222fee8cbe245d8fac',
  'sebastian/exporter' => '2.0.0@ce474bdd1a34744d7ac5d6aad3a46d48d9bac4c4',
  'sebastian/global-state' => '1.1.1@bc37d50fea7d017d3d340f230811c9f1d7280af4',
  'sebastian/object-enumerator' => '2.0.1@1311872ac850040a79c3c058bea3e22d0f09cbb7',
  'sebastian/recursion-context' => '2.0.0@2c3ba150cbec723aa057506e73a8d33bdb286c9a',
  'sebastian/resource-operations' => '1.0.0@ce990bb21759f94aeafd30209e8cfcdfa8bc3f52',
  'sebastian/version' => '2.0.1@99732be0ddb3361e16ad77b68ba41efc8e979019',
  'sensiolabs/security-checker' => 'v5.0.3@46be3f58adac13084497961e10eed9a7fb4d44d1',
  'simplesamlphp/simplesamlphp-test-framework' => 'v0.0.14@d9b90d829ffd1597f0119570b4b1f5b7c91a56e1',
  'squizlabs/php_codesniffer' => '3.5.3@557a1fc7ac702c66b0bbfe16ab3d55839ef724cb',
  'symfony/console' => 'v3.4.36@1ee23b3b659b06c622f2bd2492a229e416eb4586',
  'symfony/stopwatch' => 'v3.4.36@efe0af281ad336bc3b10375c88b117499f1d8494',
  'vimeo/psalm' => '1.1.9@d15cf3b7f50249caf933144c8926c8e69aff3d34',
  'simplesamlphp/simplesamlphp' => '1.18.3.0@',
);

    private function __construct()
    {
    }

    /**
     * @throws \OutOfBoundsException if a version cannot be located
     */
    public static function getVersion($packageName)
    {
        $selfVersion = self::$VERSIONS;

        if (! isset($selfVersion[$packageName])) {
            throw new \OutOfBoundsException(
                'Required package "' . $packageName . '" is not installed: cannot detect its version'
            );
        }

        return $selfVersion[$packageName];
    }

    /**
     * @throws \OutOfBoundsException if a version cannot be located
     */
    public static function getShortVersion($packageName)
    {
        return explode('@', static::getVersion($packageName))[0];
    }

    /**
     * @throws \OutOfBoundsException if a version cannot be located
     */
    public static function getMajorVersion($packageName)
    {
        return explode('.', static::getShortVersion($packageName))[0];
    }
}
