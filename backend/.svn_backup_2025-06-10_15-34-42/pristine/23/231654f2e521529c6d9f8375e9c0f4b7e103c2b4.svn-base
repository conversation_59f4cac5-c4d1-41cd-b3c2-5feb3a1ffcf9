<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_MOD');

	require_once( 'messages.inc.php' );
	
	// Ajoute ou retire un message des spams
	if( isset($_POST['spam'], $_POST['msg']) ){
		$res = false;
		if( $msg = ria_mysql_fetch_array( messages_get(0, '', null, $_POST['msg']) ) ){
			$res = gu_messages_set_spam( $_POST['msg'], $_POST['spam'] ? true : false );
			if( $msg['ip']!='' && $_POST['spam'] && !ats_ips_add($msg['ip']) ){
				$res = false;
			}elseif( $msg['ip']!='' && !$_POST['spam'] && !ats_ips_del($msg['ip']) ){
				$res = false;
			}
		}
		print $res;
		exit;
	}

	// Charger les réponses
	if( isset($_POST['reload-answers']) ){
		$html = '';

		if( isset($_POST['msg_id']) && is_numeric($_POST['msg_id']) && $_POST['msg_id'] > 0 ){
			$messages_reply = contacts_get_replies( $_POST['msg_id'] );
			if( $messages_reply ){
				if( mysql_num_rows($messages_reply) == 1 ){
					$html .= '<br />
						<a id="show-lst-rep-'.$_POST['msg_id'].'" onclick="show_rep('.$_POST['msg_id'].')">' . _("Afficher la réponse") . '</a>
						<a id="hide-lst-rep-'.$_POST['msg_id'].'" onclick="hide_rep('.$_POST['msg_id'].')" class="none">' . _("Masquer la réponse") . '</a>
					';
				}elseif( mysql_num_rows($messages_reply) > 1 ){
					$html .= '<br />
						<a id="show-lst-rep-'.$_POST['msg_id'].'" onclick="show_rep('.$_POST['msg_id'].')">' . _("Afficher les réponses") . '</a>
						<a id="hide-lst-rep-'.$_POST['msg_id'].'" onclick="hide_rep('.$_POST['msg_id'].')" class="none">' . _("Masquer les réponses") . '</a>
					';
				}

				$html .= '<div id="lst-rep-'.$_POST['msg_id'].'" class="none">';

				while( $msg_reply = mysql_fetch_assoc($messages_reply) ){
					$html .= '
						<div class="rep">
							<span class="bold">' . _("Votre réponse :") . '</span>
							<br /><em>Le '.$msg_reply['date_created'].'</em>
							<br /><br />
							'.nl2br( htmlspecialchars($msg_reply['body']) ).'
					';
				
					$r_file = messages_files_get(0,$msg_reply['id']);
					if( mysql_num_rows($r_file)>0 ){
						$html .= '
							<div>
								<br />' . _('Pièces jointes :') . '
								<ul>
						';
						
						while( $file = mysql_fetch_assoc($r_file) ){
							$html .= '<li><a href="/admin/customers/dl.php?file='.$file['id'].'">'.htmlspecialchars( $file['name'] ).'</a> <span class="size-file">('.ria_format_bytes( $file['size'] ).')</span></li>';
						}
						
						$html .= '
								</ul>
							</div>
						';
					}

					$html .= '</div>';
				}

				$html .= '
							<div class="clear"></div>
						</div>
				';
			}
		}

		print $html;
		exit;
	}
	
	// Répondre à un message
	if( isset($_POST['submit-rep']) ){
		$file = isset($_POST['tab-file']) ? explode(",",$_POST['tab-file']) : null;
		$_POST['msg'] = str_replace( 'msg-', '', $_POST['msg'] );

		$rmsg = messages_get( 0, '', null, $_POST['msg']);
		if( !$rmsg || !ria_mysql_num_rows($rmsg) ){
			print '0'; exit;
		}
		
		$msg = ria_mysql_fetch_array( $rmsg );
		if( $msg['usr_id']>0 && gu_users_exists($msg['usr_id']) ){
			$msg['email'] = gu_users_get_email($msg['usr_id']);
		}
		
		$res = gu_messages_send_reply($msg['email'], $msg['id'], $_SESSION['usr_id'], $msg['subject'], $_POST['reponce-message'], $msg['body'], $file);
		
		if( $res ){
			$html = '';
			
			$messages_reply = contacts_get_replies( $msg['id'] );
			if( $messages_reply ){
				if( ria_mysql_num_rows($messages_reply)==1 ){
					$html .= '<br />
						<a id="show-lst-rep-'.$msg['id'].'" onclick="show_rep('.$msg['id'].')">' . _("Afficher la réponse") . '</a>
						<a id="hide-lst-rep-'.$msg['id'].'" onclick="hide_rep('.$msg['id'].')" class="none">' . _("Masquer la réponse") . '</a>
					';
				}elseif( ria_mysql_num_rows($messages_reply)>1 ){
					$html .= '<br />
						<a id="show-lst-rep-'.$msg['id'].'" onclick="show_rep('.$msg['id'].')">' . _("Afficher les réponses") . '</a>
						<a id="hide-lst-rep-'.$msg['id'].'" onclick="hide_rep('.$msg['id'].')" class="none">' . _("Masquer les réponses") . '</a>
					';
				}
				
				$html .= '<div id="lst-rep-'.$msg['id'].'" class="none">';
				
					while( $msg_reply = ria_mysql_fetch_array($messages_reply) ){
						
						$html .= '<div class="rep">';
							$html .= '<span class="bold">' . _("Votre réponse :") . '</span><br />
								<em>Le '.$msg_reply['date_created'].'</em><br /><br />
							';
							$html .= nl2br(htmlspecialchars($msg_reply['body']));
							$r_file = messages_files_get(0,$msg_reply['id']);
							if( ria_mysql_num_rows($r_file)>0 ){
							
								$html .= '<div>
									<br />' . _("Pièces jointes :") . '<ul>';
									while( $file = ria_mysql_fetch_array($r_file) ){
										$html .= '<li><a href="/admin/customers/dl.php?file='.$file['id'].'">'.htmlspecialchars( $file['name'] ).'</a> <span class="size-file">('.ria_format_bytes( $file['size'] ).')</span></li>';
									
									}
									$html .= '</ul>
								</div>';
							
							}
						$html .= '</div>';
					
					}
				$html .= '		<div class="clear"></div>';
				$html .= '</div>';
			}
			
			print $html;
			exit;	
		} else {
			print 0; 
			exit;
		}
	}

	// Publication / Dépublication
	if( isset($_POST['msg'], $_POST['publish']) ){
	
		// Publier un message
		if( $_POST['publish'] ){
			if( !gu_messages_publish($_POST['msg']) ){
				print 0;
				exit;
			}
			if (!gu_messages_unset_comment($_POST['msg'])){
				print 0;
				exit;
			}
		}
		
		// Dépublier un message
		if( !$_POST['publish'] ){
			if( !gu_messages_unpublish($_POST['msg']) ){
				print 0;
				exit;
			}
			if (isset($_POST['comment']) && trim($_POST['comment']) != ''){
				if (!gu_messages_set_comment($_POST['msg'], $_POST['comment'])){
					print 0;
					exit;
				}
			}
		}
		
		$html = '';
		if( $msg = ria_mysql_fetch_array( messages_get(0, '', null, $_POST['msg']) ) ){
			// Date de publication ou dépublication
			if( $msg['date_publish']!='' ){
				$date_publish = $msg['date_publish'];
				if( $msg['usr_publish']>0 && ($usrP = ria_mysql_fetch_array( gu_users_get($msg['usr_publish']) )) ){
					$date_publish .= ' <br />' . _('Par ').view_usr_is_sync( $usrP );
					$date_publish .= ' <a target="_blank" href="/admin/customers/edit.php?usr='.$usrP['id'].'">'.htmlspecialchars( $usrP['adr_firstname'].' '.$usrP['adr_lastname'] ).'</a>';
				}	
			}

			// Information sur le statut du message
			switch( $msg['publish'] ){
				case 1 : 
					$html .= '<span class="bold">' . _('Modération :') . '</span>'; 
					$html .= '<span class="info-publish">' . _('Approuvé le ');
					if( $date_publish!='' ){
						$html .= $date_publish;
					}
					$html .= '<br /><a onclick="moderateMessage('.$msg['id'].', false)" class="unchecked">' . _('Ne plus approuver') . '</a></span>';
					break;
				case 0 : 
					$html .= '<span class="bold">' . _('Modération :') . '</span>';
					$html .= '<span class="info-publish">' . _('Refusé le ');
					if( $date_publish!='' ){
						$html .= $date_publish;
					}else{
						$html .= '<br />Par ';
					}
					$html .= '<br /><a onclick="moderateMessage('.$msg['id'].', true)" class="checked">' . _("Approuver") . '</a></span>';
					if ($msg['comment'] != ""){
						$html .= '<br /><span class="bold">' . _('Commentaire sur la désapprobation :') . '</span>';
						$html .= '<br /><em>'.htmlspecialchars( $msg['comment'] ).'</em>';
					}
					break;
				default : 
					print '<span class="bold">' . _('Modération :') . '</span>';
					print '<span class="info-publish">';
					print sprintf( _('En attente depuis le %s'), $msg['date'] );
					print '<br /><a onclick="moderateMessage( '.$msg['id'].', true)" class="checked">' . _("Approuver") . '</a> | <a onclick="moderateMessage( '.$msg['id'].', false)" class="unchecked">' . _("Désapprouver") . '</a>';
					print '</span>';
					break;
			}
		}
		
		print $html;
		exit;
	}
