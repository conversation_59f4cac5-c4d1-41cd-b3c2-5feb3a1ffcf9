<?php

	/** \file refresh-messages-answer-count.php
	 * 	Ce script détecte les messages pour lequels le nombre de réponses enregistrées (cnt_reply) n'est pas valide
	 */

	 set_include_path(dirname(__FILE__) . '/../include/');

	require_once('messages.inc.php');
	require_once(str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php');
	
	$noanswers = messages_get_with_noreply();
	
	$cnt_affect = 0;
	print 'Vous trouverez ci-dessous les message dont le nombre de réponse était faux : '."\n";
	print '--------------------------------------------------------------'."\n";
	while( $cnt = ria_mysql_fetch_array($noanswers) ){
		if( messages_answer_count_set( $cnt['id'], $cnt['nbanswer'] )>0 ){
			print $cnt['id']."\n";
			$cnt_affect++;
		}
	}
	
	$s = $cnt_affect>1 ? 's' : '';
	
	print 'Il y avait au total '.$cnt_affect.' message'.$s.' concerné'.$s.' par un nombre de réponse faux.'."\n";

