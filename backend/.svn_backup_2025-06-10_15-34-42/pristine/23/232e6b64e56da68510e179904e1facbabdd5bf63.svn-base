<?php

require_once('Services/Service.class.php');
require_once('cms.inc.php');

/**	\brief Cette classe permet de charger les données d'une page de contenu
 */
class PageService extends Service {
	protected $title = ''; ///< Titre de la page de contenu
	protected $intro = ''; ///< Introduction de la page de contenu
	protected $intronoformated = ''; ///< Introduction de la page de contenu sans formatage du contenu
	protected $content = ''; ///< Contenu de la page
	protected $url = ''; ///< URl de la page de contenu
	protected $fields = []; ///< Tableau des champs avancés liés à la page de contenu (n'est chargé que sur demande)
	protected $images = []; ///< Tableau des images
	protected $publish = true; ///< Si publiée ou non
	protected $published = null; ///< Date de publication
	protected $modified = null; ///< Date de dernière modification
	protected $parents = null; ///< Tableau des parents
	protected $children = null; ///< Tableau des enfants

	protected $id = 0; ///< Identifiant de la page de contenu

	private $depth = 1; ///< Profondeur des pages à récupérer

	/** Cette fonction permet de créer un objet de la classe PageService.
	 *  @param array $data Optionnel, permet de pré-renseigner les informations
	 */
	public function __construct( $data=[] ){
		$this->id = ria_array_get( $data, 'cms', 0 );

		$this->title = ria_array_get( $data, 'title', '' );
		$this->intro = ria_array_get( $data, 'intro', '' );
		$this->intronoformated = html_revert_wysiwyg( ria_array_get($data, 'intro', '') );
		$this->content = ria_array_get( $data, 'content', '' );
		$this->url = ria_array_get( $data, 'url', '' );
		$this->publish = array_key_exists('publish', $data) && is_bool($data['publish']) ? $data['publish'] : true;

	}

	/** Cette fonction permet de charger le contenu d'une page.
	 *  @return	object	L'objet en cours
	 */
	public function load(){
		global $config;

		$r_cms = cms_categories_get( $this->id, $config['wst_id'], $this->publish );
		if( !ria_mysql_num_rows($r_cms) ){
			$this->id = 0;
			return $this;
		}
		$cms = i18n::getTranslation( CLS_CMS, ria_mysql_fetch_assoc($r_cms) );

		$this->title = $cms['name'];
		$this->intronoformated = $cms['short_desc'];
		$this->intro = view_site_format_description( $cms['short_desc'] );
		$this->content = view_site_format_riawysiwyg( $cms['desc'] );
		$this->url = $cms['url'];
		$this->published = $cms['date'];
		$this->modified = $cms['date_modified'];

		return $this;
	}

	/**	Permet de tester l'existance d'une page de contenu
	 * @return	bool	True si la page existe, false sinon
	 */
	public function exists(){
		return is_numeric($this->id) && $this->id > 0;
	}

	/**	Récupération des images
	 * @param	array	$size	Tableau des tailles d'images
	 * @return	PageService		L'objet en cours
	 */
	public function images($size){
		global $config;

		if( !$this->id || !is_array($size) || !count($size) ){
			return $this;
		}
		$_size = [];

		foreach($size as $s){
			if( isset($config['img_sizes'][$s]) ){
				$_size[] = $s;
			}
		}

		if( !count($_size) ){
			return $this;
		}
		$r_image = cms_images_get( $this->id );

		if( !ria_mysql_num_rows($r_image) ){
			return $this;
		}
		$this->images = new Collection();

		while( $image = ria_mysql_fetch_assoc($r_image) ){
			$imgs = [];

			foreach($_size as $s){
				$thumb = $config['img_sizes'][$s];
				$imgs[$s] = [
					'id'		=> $image['id'],
					'url'		=> $config['img_url'].'/'.$thumb['dir'].'/'.$image['id'].'.'.$thumb['format'],
					'alt'		=> $this->title,
					'width'		=> $thumb['width'],
					'height'	=> $thumb['height']
				];
			}
			$this->images->addItem( $imgs );
		}
		return $this;

	}

	/**	Cette méthode permet de récupérer les catégories cms parentes s'il y en a
	 * @param	array	$exclude	Optionnel, tableau des identifiants de catégories cms à exclure du résultat
	 * @param	bool	$only_ids	Optionnel, False pour charger les données des catégories cms parentes
	 * @return	PageService	L'objet en cours
	 */
	public function parents($exclude=[], $only_ids=false){

		if( !$this->id ){
			return $this;
		}
		$parents = cms_categories_get_parents($this->id);

		if( !is_array($parents) || !count($parents) ){
			return $this;
		}
		$exclude = is_array($exclude) ? $exclude : [];
		$only_ids = is_bool($only_ids) ? $only_ids : false;
		$ar_parents = [];

		foreach($parents as $parent){

			if( in_array($parent, $exclude) ){
				continue;
			}

			if( $only_ids ){
				$ar_parents[] = $parent;
				continue;
			}
			$Parent = new PageService(['cms' => $parent]);
			$Parent->load();
			$data = $Parent->getData();

			if( !is_numeric($data['id']) || $data['id'] <= 0){
				continue;
			}
			$ar_parents[] = $data;
		}

		if( count($ar_parents) ){
			$this->parents = $ar_parents;
		}
		return $this;
	}

	/**	Cette méthode permet de récupérer les catégories cms enfants s'il y en a
	 * @param	int|array	$fields		Optionnel, Identifiant ou tableau d'identifiants de champs avancés
	 * @param	int			$depth		Optionnel, permet de préciser un niveau de profondeur de récupération
	 * @return	object		L'objet en cours
	 */
	public function children($fields=false, $depth=0){

		if( !$this->id ){
			return $this;
		}
		global $config, $hook;

		$ar_filters = [
			'fld'				=> false,
			'fld_or'			=> false,
			'current'			=> true,
			'or_between_fld'	=> null,
			'name'				=> false,
			'archived'			=> null,
			'check_segments'	=> true,
			'sort'				=> false

		];
		$ar_hook_filters = $hook->apply_filter('PageService_setParametersOnChildren', $ar_filters);

		$ar_filters = array_merge($ar_filters, $ar_hook_filters);

		$children = cms_categories_get(0, $config['wst_id'], $this->publish, $this->id, $ar_filters['fld'], $ar_filters['fld_or'], $ar_filters['current'], $ar_filters['or_between_fld'], $ar_filters['name'], $ar_filters['archived'], $ar_filters['check_segments'], $ar_filters['sort']);

		if( !ria_mysql_num_rows($children) ){
			return $this;
		}
		$depth--;
		$load_fields = is_numeric($fields) || (is_array($fields) && count($fields));
		$ar_children = [];

		while($child = ria_mysql_fetch_assoc($children)){
			$child = i18n::getTranslation( CLS_CMS, $child );

			$Child = new PageService([
				'cms'		=> $child['id'],
				'title'		=> $child['name'],
				'intro'		=> view_site_format_description( $child['short_desc'] ),
				'content'	=> view_site_format_riawysiwyg( $child['desc'] ),
				'url'		=> $child['url'],
				'publish'	=> $this->publish
			]);

			$hook->do_action('PageService_onLoadingChild', ['Child' => $Child]);

			if( $load_fields ){
				$Child->fields($fields);
			}

			if( $depth > 1 ){
				$Child->children($fields, $depth);
			}
			$data = $Child->getData();

			if( !is_numeric($data['id']) || $data['id'] <= 0){
				continue;
			}
			$ar_children[] = $data;
		}

		if( count($ar_children) ){
			$this->children = $ar_children;
		}
		return $this;

	}

	/** Cette fonction permet de charger dans un tableau les pages présentes dans une zone.
	 *  @param int $zone Obligatoire, identifiant d'une zone
	 * 	@param string $thumb Optionnel, permet de charger les images des pages dans une dimensions précise
	 * 	@param bool $children Optionnel, par défaut seul le premier niveau est retourner, mettre true pour charger les pages enfants en même temps
	 *  @return array Un tableau contenant le détails sur les pages de cette zone
	 */
	public function zone( $zone, $thumbs=false, $children=false ){
		global $config;

		if( !is_numeric($zone) || $zone <= 0 ){
			throw new Exception('Aucune zone n\'est défini.');
		}

		$ar_cms = new Collection();

		$r_cms = cms_categories_get( 0, $config['wst_id'], true, $zone );
		if( $r_cms ){
			while( $cms = ria_mysql_fetch_assoc($r_cms) ){
				$cms = i18n::getTranslation( CLS_CMS, $cms );

				$page = new PageService([
					'cms' => $cms['id'],
					'title' => $cms['name'],
					'intro' => view_site_format_description( $cms['short_desc'] ),
					'content' => view_site_format_riawysiwyg( $cms['desc'] ),
					'url' => $cms['url'],
				]);

				$page->fields();

				if( $thumbs !== false ){
					$page->images( [$thumbs] );
				}

				if( $children === true ){
					$page->children( 0 );
				}

				$ar_cms->addItem( $page );
			}
		}

		return $this->transformObjectToArray( $ar_cms );
	}

	/** Cette fonction permet de charger dans un tableau les pages présentes dans une zone définie par un ou plusieurs champs avancés.
	 *  @param array $fields Obligatoire, tableau contenant les règles de restriction de la zone (ex. [fld_id => fld_val])
	 *  @param int $zone Optionnel, permet de limiter à une zone bien précise
	 *  @return array Un tableau contenant le détails sur les pages de cette zone
	 */
	public function zoneByFields( $fields, $zone=-1 ){
		global $config, $hook;

		if( !is_numeric($zone) || $zone < -1 ){
			throw new Exception('La zone définie est invalide.');
		}
		$r_cms = cms_categories_get( 0, $config['wst_id'], true, $zone, $fields );

		if( !ria_mysql_num_rows($r_cms) ){
			return false;
		}
		$ar_cms = new Collection();

		while( $cms = ria_mysql_fetch_assoc($r_cms) ){
			$cms = i18n::getTranslation(CLS_CMS, $cms);

			$page = new PageService([
				'cms'		=> $cms['id'],
				'title'		=> $cms['name'],
				'intro'		=> view_site_format_description( $cms['short_desc'] ),
				'content'	=> view_site_format_riawysiwyg( $cms['desc'] ),
				'url'		=> $cms['url'],
			]);

			$page->fields();

			$hook->do_action('PageService_onloadPageByField', ['Page'	=> $page]);

			$ar_cms->addItem( $page );
		}

		return $this->transformObjectToArray( $ar_cms );
	}

	/** Cette fonction permet de charger les champs avancés liés à une page de contenu.
	 * @param	int|array	$id	Optionnel, Identifiant ou tableau d'identifiants de champ.s avancé.s sur lequel/ lesquel filtrer le résultat
	 * @return	object		L'instance en cours
	 */
	public function fields($id=0){

		if( !$this->id ){
			return $this;
		}
		$r_fields = fld_fields_get( $id, 0, -2, 0, 0, $this->id, null, [], false, [], null, CLS_CMS );

		if( !ria_mysql_num_rows($r_fields) ){
			return $this;
		}

		while( $field = ria_mysql_fetch_assoc($r_fields) ){
			// Transforme la valeur en tableau pour certains types de champs avancés
			// Liste de choix unique, Liste de choix multiple ou Liste de choix multiple hiérarchique
			if( in_array( $field['type_id'], [FLD_TYPE_SELECT, FLD_TYPE_SELECT_MULTIPLE, FLD_TYPE_SELECT_HIERARCHY]) ){
				$field['obj_value'] = explode( ', ', $field['obj_value'] );
			}

			$this->fields[ 'field'.$field['id'] ] = [
				'id' => $field['id'],
				'value' => $field['obj_value'],
			];
		}

		return $this;
	}
}