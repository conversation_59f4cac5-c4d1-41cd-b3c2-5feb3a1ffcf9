<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/resources.proto

namespace Google\Cloud\ServiceManagement\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * The full representation of a Service that is managed by
 * Google Service Management.
 *
 * Generated from protobuf message <code>google.api.servicemanagement.v1.ManagedService</code>
 */
class ManagedService extends \Google\Protobuf\Internal\Message
{
    /**
     * The name of the service. See the [overview](/service-management/overview)
     * for naming requirements.
     *
     * Generated from protobuf field <code>string service_name = 2;</code>
     */
    private $service_name = '';
    /**
     * ID of the project that produces and owns this service.
     *
     * Generated from protobuf field <code>string producer_project_id = 3;</code>
     */
    private $producer_project_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $service_name
     *           The name of the service. See the [overview](/service-management/overview)
     *           for naming requirements.
     *     @type string $producer_project_id
     *           ID of the project that produces and owns this service.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Servicemanagement\V1\Resources::initOnce();
        parent::__construct($data);
    }

    /**
     * The name of the service. See the [overview](/service-management/overview)
     * for naming requirements.
     *
     * Generated from protobuf field <code>string service_name = 2;</code>
     * @return string
     */
    public function getServiceName()
    {
        return $this->service_name;
    }

    /**
     * The name of the service. See the [overview](/service-management/overview)
     * for naming requirements.
     *
     * Generated from protobuf field <code>string service_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setServiceName($var)
    {
        GPBUtil::checkString($var, True);
        $this->service_name = $var;

        return $this;
    }

    /**
     * ID of the project that produces and owns this service.
     *
     * Generated from protobuf field <code>string producer_project_id = 3;</code>
     * @return string
     */
    public function getProducerProjectId()
    {
        return $this->producer_project_id;
    }

    /**
     * ID of the project that produces and owns this service.
     *
     * Generated from protobuf field <code>string producer_project_id = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setProducerProjectId($var)
    {
        GPBUtil::checkString($var, True);
        $this->producer_project_id = $var;

        return $this;
    }

}

