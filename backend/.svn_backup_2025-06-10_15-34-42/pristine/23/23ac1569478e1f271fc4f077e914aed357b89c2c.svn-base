<?php
namespace SchemaDotOrg\Tags;
require_once('SchemaDotOrg/Tags/TagPotentialAction.php');
/**
 * \ingroup SchemaTag
 * @{
 */
/**
 * \brief Cette classe correspond au tag SearchAction complémente le tag WebSite
 */
class TagSearchAction extends TagPotentialAction {
	/**
	 * Le constructeur permet de définir le type
	 *
	 * @param string $path Chemin vers la recherche
	 */
	public function __construct($path){
		global $config;
		parent::__construct('SearchAction');
		$url = $config['site_url'].$path;
		$this->setSearchUrl($url);
	}

	/**
	 * Lien du moteur de recherche du site ex : "http://www.graphicbiz.fr/rechercher/?q="
	 *
	 * @param string $url
	 * @return void
	 */
	private function setSearchUrl($url){
		$this->setTarget($url.'{search_term_string}')
			->setQueryInput('search_term_string');
	}
}
/// @}