<?php 

	/**	\file popup-redirection-search.php
	 *	Cette page apparaît en popup et permet la création d'une redirection dans le moteur de recherche interne.
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT');

	require_once('search.inc.php');
	if( isset($_GET['wst']) ) {
		$_GET['wst'] = str_replace('w-', '', $_GET['wst']);
	}
	
	$old = isset($_GET['old']) ? urldecode( $_GET['old'] ) : '';
	$id_seg = isset($_POST['engines']) ? $_POST['engines'] : (isset($_GET['seg']) ? $_GET['seg'] : 0);
	$lng = isset($_GET['lng']) && trim($_GET['lng'])!='' ? $_GET['lng'] : $config['i18n_lng'];
	$id = isset($_GET['substitut']) && is_numeric($_GET['substitut']) && $_GET['substitut']>0 ? $_GET['substitut'] : 0;
	$wst = isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst']>0 ? $_GET['wst'] : 0;

	// Bouton Enregistrer
	if( isset($_POST['save-redir-search']) ){
		if( !isset($_POST['old-search']) || trim($_POST['old-search'])=='' ){
			$error = _("Veuillez renseigner la recherche d'origine");
		} elseif( !isset($_POST['new-search']) ){
			$error = _("Veuillez renseigner la recherche de substitution");
		} else {
			$lng = isset($_POST['lang']) && trim($_POST['lang'])!='' ? $_POST['lang'] : $lng;

			$success = false;
			if( search_substitut_exists('', $lng, $wst, $id_seg, $id) ){
				if (!gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_UPD')) {
					$error = _('Vous n\'êtes pas autorisé à modifier une redirection de recherche.');
				} else {
					$success = search_substitut_update( $_POST['old-search'], $_POST['new-search'], $lng, $wst, $id_seg, $id );
				}
			} elseif( trim($_POST['new-search'])!='' ){
				if (!gu_user_is_authorized('_RGH_ADMIN_STATS_SEARCH_SUBSTITUT_ADD')) {
					$error = _('Vous n\'êtes pas autorisé à ajouter une redirection de recherche.');
				} else {
					$success = search_substitut_add( $_POST['old-search'], $_POST['new-search'], $lng, $wst, $id_seg, $id );
				}
			}
			if( $success ){
				$_SESSION['save-redir-search-success'] = _('L\'enregistrement de votre redirection s\'est correctement déroulé.');
				header('Location: ' . $_SERVER['REQUEST_URI']);
				exit;
			}
			
			if( !$success && !isset($error) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la redirection de recherche. \nVeuillez réessayer ou prendre contacte pour nous signaler le problème.");
			}
		}
	}

	$substitut = null;
	if( $old != '' || $id > 0 ){
		$r_substitut = search_substitut_get($old != '' ? $old : false, $lng, false, 'asc', $wst, $id_seg, $id, false);
		if( $r_substitut && ria_mysql_num_rows($r_substitut)){
			$substitut = ria_mysql_fetch_assoc($r_substitut);
		}
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Création').' - '._('Redirections de recherche'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-add-redirect');
	define('ADMIN_CLASS_BODY', 'popup_img');
	require_once('admin/skin/header.inc.php');
?>
		<form method="post" id="add-redir-search" action="/admin/stats/popup-redirection-search.php?old=<?php print urlencode( $old ); ?>&amp;seg=<?php print urlencode($id_seg); ?>&amp;wst=<?php print urlencode($wst); ?>&amp;lng=<?php print urlencode($lng); ?>&amp;substitut=<?php print urlencode($id); ?>">
			<input type="hidden" name="search-lng" id="search-lng" value="<?php print isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ? htmlspecialchars($_GET['lng']) : htmlspecialchars($config['i18n_lng']); ?>" />
			<?php
				if( isset($error) ){
					print '<div class="error">'.nl2br( $error ).'</div>';
				} elseif( isset($_SESSION['save-redir-search-success']) ){
					print '<div class="error-success">'.htmlspecialchars($_SESSION['save-redir-search-success']).'</div>';
					unset($_SESSION['save-redir-search-success']);
				}
			?>
			
			<div class="notice"><?php print _('Vous avez la possibilité de créer une redirection de recherche qui remplacera une recherche avec le terme contenu dans "Recherche d\'origine" par une recherche sur le terme contenu dans "Remplacer par".'); ?></div>
			<div class="redirection">
				<?php
					$rseg = search_engines_get();
					if( $rseg && ria_mysql_num_rows($rseg)>1 ){
						print '	<div class="elem">';
						print '		<label for="engines" title="'._('Moteur de recherche présent sur votre site').'">'._('Moteur de recherche :').'</label>';
						print '		<select name="engines" id="engines">';
						print '			<option value="0">'.htmlspecialchars(_("Tous les emplacements")).'</option>';
						
						while( $seg = ria_mysql_fetch_array($rseg) ){
							$selected = !is_null($substitut) && $substitut['seg_id'] == $seg['id'] || $id_seg==$seg['id'] ? 'selected="selected"' : '';
							print '		<option value="'.$seg['id'].'" '.$selected.'>'.htmlspecialchars($seg['wst_name'].' - '.$seg['name']).'</option>';
						}
						print '		</select>';
						print '	</div>';
					}

					if( sizeof($config['i18n_lng_used'])>1 ){
						print '	<div class="elem">';
						print '		<label for="lang" title="'._('Moteur de recherche présent sur votre site').'">'._('Langue').' :</label>';
						print '		<select name="lang" id="lang">';
						print '			<option value="-1"></option>';
						
						foreach( $config['i18n_lng_used'] as $code ){
							print '		<option value="'.$code.'" '.( !is_null($substitut) && $substitut['lng_code'] == $code || $lng==$code ? 'selected="selected"' : '' ).'>'.htmlspecialchars( i18n_languages_get_name($code) ).'</option>';
						}

						print '		</select>';
						print '	</div>';
					}
				?>
				<div class="elem">
					<label for="old-search"><span class="mandatory">*</span> <?php print _('Recherche d\'origine :'); ?></label>
					<input type="text" name="old-search" id="old-search" value="<?php print isset($_POST['old-search']) ? htmlspecialchars( urldecode($_POST['old-search']) ) : (!is_null($substitut) ? htmlspecialchars($substitut['search']) : htmlspecialchars(urldecode($old))); ?>" />
					<img alt="<?php print _('Pré-visualisation'); ?>" title="<?php print _('Pré-visualisation de la recherche'); ?>" name="loupe" src="/admin/images/petite_loupe_active.svg" class="img-stat-search" onclick="return redirectionSearchPreview( $(this) )" />
				</div>
				<div class="elem">
					<label for="new-search"><span class="mandatory">*</span> <?php print _('Remplacer par :'); ?></label>
					<input type="text" name="new-search" id="new-search" value="<?php print htmlspecialchars(isset($_POST['new-search']) ? $_POST['new-search'] : (!is_null($substitut) ? $substitut['substitut'] : '')); ?>" />
					<img alt="<?php print _('Pré-visualisation'); ?>" title="<?php print _('Pré-visualisation de la recherche'); ?>" name="loupe" src="/admin/images/petite_loupe_active.svg" class="img-stat-search" onclick="return redirectionSearchPreview( $(this) )" />
				</div>
				<div class="actions">
					<input type="submit" name="save-redir-search" id="save-redir-search" value="<?php print _('Enregistrer'); ?>" />
					<input type="button" name="cancel-redir-search" id="cancel-redir-search" value="<?php print _('Annuler'); ?>" onclick="return parent.hidePopup();" />
					<div class="clear"></div>
					<sub><span class="mandatory">*</span> <?php print _('Champ obligatoire'); ?></sub>
				</div>
			</div>
			<div id="head-results"></div>
			<div id="results"></div>
		</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>