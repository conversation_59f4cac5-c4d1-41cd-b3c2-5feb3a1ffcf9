<?php
/**
 * ImportationReporting
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * ImportationReporting Class Doc Comment
 *
 * @category Class
 * @description The catalog importation reporting
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class ImportationReporting implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'importationReporting';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'step_name' => 'string',
        'user_id' => '\Swagger\Client\Model\BeezUPCommonUserId',
        'success' => 'bool',
        'total_product_count' => 'int',
        'total_product_error_count' => 'int',
        'total_product_success_count' => 'int',
        'errors' => '\Swagger\Client\Model\BeezUPCommonUserErrorMessage[]',
        'last_update_utc_date' => '\DateTime',
        'auto_imported' => 'bool',
        'begin_utc_date' => '\DateTime',
        'end_utc_date' => '\DateTime',
        'input_configuration_url' => 'string',
        'steps' => 'map[string,bool]'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'step_name' => null,
        'user_id' => null,
        'success' => null,
        'total_product_count' => 'int32',
        'total_product_error_count' => 'int32',
        'total_product_success_count' => 'int32',
        'errors' => null,
        'last_update_utc_date' => 'date-time',
        'auto_imported' => null,
        'begin_utc_date' => 'date-time',
        'end_utc_date' => 'date-time',
        'input_configuration_url' => 'uri',
        'steps' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'step_name' => 'stepName',
        'user_id' => 'userId',
        'success' => 'success',
        'total_product_count' => 'totalProductCount',
        'total_product_error_count' => 'totalProductErrorCount',
        'total_product_success_count' => 'totalProductSuccessCount',
        'errors' => 'errors',
        'last_update_utc_date' => 'lastUpdateUtcDate',
        'auto_imported' => 'autoImported',
        'begin_utc_date' => 'beginUtcDate',
        'end_utc_date' => 'endUtcDate',
        'input_configuration_url' => 'inputConfigurationUrl',
        'steps' => 'steps'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'step_name' => 'setStepName',
        'user_id' => 'setUserId',
        'success' => 'setSuccess',
        'total_product_count' => 'setTotalProductCount',
        'total_product_error_count' => 'setTotalProductErrorCount',
        'total_product_success_count' => 'setTotalProductSuccessCount',
        'errors' => 'setErrors',
        'last_update_utc_date' => 'setLastUpdateUtcDate',
        'auto_imported' => 'setAutoImported',
        'begin_utc_date' => 'setBeginUtcDate',
        'end_utc_date' => 'setEndUtcDate',
        'input_configuration_url' => 'setInputConfigurationUrl',
        'steps' => 'setSteps'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'step_name' => 'getStepName',
        'user_id' => 'getUserId',
        'success' => 'getSuccess',
        'total_product_count' => 'getTotalProductCount',
        'total_product_error_count' => 'getTotalProductErrorCount',
        'total_product_success_count' => 'getTotalProductSuccessCount',
        'errors' => 'getErrors',
        'last_update_utc_date' => 'getLastUpdateUtcDate',
        'auto_imported' => 'getAutoImported',
        'begin_utc_date' => 'getBeginUtcDate',
        'end_utc_date' => 'getEndUtcDate',
        'input_configuration_url' => 'getInputConfigurationUrl',
        'steps' => 'getSteps'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['step_name'] = isset($data['step_name']) ? $data['step_name'] : null;
        $this->container['user_id'] = isset($data['user_id']) ? $data['user_id'] : null;
        $this->container['success'] = isset($data['success']) ? $data['success'] : null;
        $this->container['total_product_count'] = isset($data['total_product_count']) ? $data['total_product_count'] : null;
        $this->container['total_product_error_count'] = isset($data['total_product_error_count']) ? $data['total_product_error_count'] : null;
        $this->container['total_product_success_count'] = isset($data['total_product_success_count']) ? $data['total_product_success_count'] : null;
        $this->container['errors'] = isset($data['errors']) ? $data['errors'] : null;
        $this->container['last_update_utc_date'] = isset($data['last_update_utc_date']) ? $data['last_update_utc_date'] : null;
        $this->container['auto_imported'] = isset($data['auto_imported']) ? $data['auto_imported'] : null;
        $this->container['begin_utc_date'] = isset($data['begin_utc_date']) ? $data['begin_utc_date'] : null;
        $this->container['end_utc_date'] = isset($data['end_utc_date']) ? $data['end_utc_date'] : null;
        $this->container['input_configuration_url'] = isset($data['input_configuration_url']) ? $data['input_configuration_url'] : null;
        $this->container['steps'] = isset($data['steps']) ? $data['steps'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['last_update_utc_date'] === null) {
            $invalidProperties[] = "'last_update_utc_date' can't be null";
        }
        if ($this->container['begin_utc_date'] === null) {
            $invalidProperties[] = "'begin_utc_date' can't be null";
        }
        if ($this->container['steps'] === null) {
            $invalidProperties[] = "'steps' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['last_update_utc_date'] === null) {
            return false;
        }
        if ($this->container['begin_utc_date'] === null) {
            return false;
        }
        if ($this->container['steps'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets step_name
     *
     * @return string
     */
    public function getStepName()
    {
        return $this->container['step_name'];
    }

    /**
     * Sets step_name
     *
     * @param string $step_name The last step name of the importation process
     *
     * @return $this
     */
    public function setStepName($step_name)
    {
        $this->container['step_name'] = $step_name;

        return $this;
    }

    /**
     * Gets user_id
     *
     * @return \Swagger\Client\Model\BeezUPCommonUserId
     */
    public function getUserId()
    {
        return $this->container['user_id'];
    }

    /**
     * Sets user_id
     *
     * @param \Swagger\Client\Model\BeezUPCommonUserId $user_id user_id
     *
     * @return $this
     */
    public function setUserId($user_id)
    {
        $this->container['user_id'] = $user_id;

        return $this;
    }

    /**
     * Gets success
     *
     * @return bool
     */
    public function getSuccess()
    {
        return $this->container['success'];
    }

    /**
     * Sets success
     *
     * @param bool $success Indicate if the importation succeed or not.
     *
     * @return $this
     */
    public function setSuccess($success)
    {
        $this->container['success'] = $success;

        return $this;
    }

    /**
     * Gets total_product_count
     *
     * @return int
     */
    public function getTotalProductCount()
    {
        return $this->container['total_product_count'];
    }

    /**
     * Sets total_product_count
     *
     * @param int $total_product_count Indicate the total product count detected in the catalog during the importation.
     *
     * @return $this
     */
    public function setTotalProductCount($total_product_count)
    {
        $this->container['total_product_count'] = $total_product_count;

        return $this;
    }

    /**
     * Gets total_product_error_count
     *
     * @return int
     */
    public function getTotalProductErrorCount()
    {
        return $this->container['total_product_error_count'];
    }

    /**
     * Sets total_product_error_count
     *
     * @param int $total_product_error_count Indicate the total product count in error detected in the catalog during the importation.
     *
     * @return $this
     */
    public function setTotalProductErrorCount($total_product_error_count)
    {
        $this->container['total_product_error_count'] = $total_product_error_count;

        return $this;
    }

    /**
     * Gets total_product_success_count
     *
     * @return int
     */
    public function getTotalProductSuccessCount()
    {
        return $this->container['total_product_success_count'];
    }

    /**
     * Sets total_product_success_count
     *
     * @param int $total_product_success_count Indicate the total product count in success in the catalog during the importation.
     *
     * @return $this
     */
    public function setTotalProductSuccessCount($total_product_success_count)
    {
        $this->container['total_product_success_count'] = $total_product_success_count;

        return $this;
    }

    /**
     * Gets errors
     *
     * @return \Swagger\Client\Model\BeezUPCommonUserErrorMessage[]
     */
    public function getErrors()
    {
        return $this->container['errors'];
    }

    /**
     * Sets errors
     *
     * @param \Swagger\Client\Model\BeezUPCommonUserErrorMessage[] $errors Indicate the error message list related to this importation.
     *
     * @return $this
     */
    public function setErrors($errors)
    {
        $this->container['errors'] = $errors;

        return $this;
    }

    /**
     * Gets last_update_utc_date
     *
     * @return \DateTime
     */
    public function getLastUpdateUtcDate()
    {
        return $this->container['last_update_utc_date'];
    }

    /**
     * Sets last_update_utc_date
     *
     * @param \DateTime $last_update_utc_date Indicate the last update UTC date of the reporting.
     *
     * @return $this
     */
    public function setLastUpdateUtcDate($last_update_utc_date)
    {
        $this->container['last_update_utc_date'] = $last_update_utc_date;

        return $this;
    }

    /**
     * Gets auto_imported
     *
     * @return bool
     */
    public function getAutoImported()
    {
        return $this->container['auto_imported'];
    }

    /**
     * Sets auto_imported
     *
     * @param bool $auto_imported Indicate if this importation is an auto import or not.
     *
     * @return $this
     */
    public function setAutoImported($auto_imported)
    {
        $this->container['auto_imported'] = $auto_imported;

        return $this;
    }

    /**
     * Gets begin_utc_date
     *
     * @return \DateTime
     */
    public function getBeginUtcDate()
    {
        return $this->container['begin_utc_date'];
    }

    /**
     * Sets begin_utc_date
     *
     * @param \DateTime $begin_utc_date Indicate the begin UTC date of this importation.
     *
     * @return $this
     */
    public function setBeginUtcDate($begin_utc_date)
    {
        $this->container['begin_utc_date'] = $begin_utc_date;

        return $this;
    }

    /**
     * Gets end_utc_date
     *
     * @return \DateTime
     */
    public function getEndUtcDate()
    {
        return $this->container['end_utc_date'];
    }

    /**
     * Sets end_utc_date
     *
     * @param \DateTime $end_utc_date Indicate the end UTC date of this importation.
     *
     * @return $this
     */
    public function setEndUtcDate($end_utc_date)
    {
        $this->container['end_utc_date'] = $end_utc_date;

        return $this;
    }

    /**
     * Gets input_configuration_url
     *
     * @return string
     */
    public function getInputConfigurationUrl()
    {
        return $this->container['input_configuration_url'];
    }

    /**
     * Sets input_configuration_url
     *
     * @param string $input_configuration_url Indicate the input url of this importation.
     *
     * @return $this
     */
    public function setInputConfigurationUrl($input_configuration_url)
    {
        $this->container['input_configuration_url'] = $input_configuration_url;

        return $this;
    }

    /**
     * Gets steps
     *
     * @return map[string,bool]
     */
    public function getSteps()
    {
        return $this->container['steps'];
    }

    /**
     * Sets steps
     *
     * @param map[string,bool] $steps Indicate the steps that have been passed during the importation process
     *
     * @return $this
     */
    public function setSteps($steps)
    {
        $this->container['steps'] = $steps;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


