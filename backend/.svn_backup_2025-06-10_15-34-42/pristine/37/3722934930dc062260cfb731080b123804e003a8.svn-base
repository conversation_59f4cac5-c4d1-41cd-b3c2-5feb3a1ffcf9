<documentation title="Function and Method Names">
    <standard>
    <![CDATA[
    Functions and methods should be named using the "studly caps" style (also referred to as "bumpy case" or "camel caps"). Functions should in addition have the package name as a prefix, to avoid name collisions between packages. The initial letter of the name (after the prefix) is lowercase, and each letter that starts a new "word" is capitalized.
    ]]>
    </standard>
    <code_comparison>
        <code title="Examples of valid function names">
        <![CDATA[
connect()
getData()
buildSomeWidget()
XML_RPC_serializeData()
        ]]>
        </code>
        <code title="Examples of invalid function names">
        <![CDATA[
Connect()
get_data()
        ]]>
        </code>
    </code_comparison>
</documentation>
