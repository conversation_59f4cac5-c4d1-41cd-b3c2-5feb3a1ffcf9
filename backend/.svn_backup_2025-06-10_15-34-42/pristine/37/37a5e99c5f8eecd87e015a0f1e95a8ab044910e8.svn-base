<?php

	/**	\file edit.php
	 *	Cette page permet l'affichage, la mise à jour et la suppression d'une erratum au catalogue.
	 */

	require_once('erratums.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	if( isset($_GET['id']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ERRATUM_EDIT');
	}elseif( !isset($_GET['id']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ERRATUM_ADD');
	}
	
	unset($error);

	// Vérifie l'identifiant passé en argument
	if( isset($_GET['id']) && $_GET['id']!=0 && !cat_erratums_exists($_GET['id']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) && isset($_GET['id']) ){
		if( !cat_erratums_del($_GET['id']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression de l'erratum.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php');
			exit;
		}
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) || isset($_POST['publish']) ){		
		if( $_POST['prd_ref']=='' ){
			$error = _('Veuillez indiquer la référence du produit concerné par l\'erratum.');
		}elseif ( $_POST['desc']=='' ){
			$error = _('Veuillez indiquer votre commentaire.');
		}elseif( !prd_products_exists_ref($_POST['prd_ref'],false) ){
			$error = _("La référence du produit n'a pas été trouvée dans la base article.");
		}else{
			if( !isset($_POST['type']) ) $_POST['type'] = false;
			$rprd = prd_products_get(0,$_POST['prd_ref']);
			if( ria_mysql_num_rows($rprd) ){
				$prd = ria_mysql_fetch_array($rprd);
				if( $_GET['id'] ){
					if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
						if( !cat_erratums_update( $_GET['id'], $prd['id'], $_POST['desc'], $_POST['type'] ) ){
							$error = _("La mise à jour de l'erratum a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
					}elseif( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ){
						$values = array(
							_FLD_ERR_DESC=>$_POST['desc']
						);
						if( !fld_translates_add($_GET['id'], $_GET['lng'], $values) ){
							$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
					}
				}else{
					if( !cat_erratums_add( $prd['id'], $_SESSION['usr_id'], $_POST['desc'], $_POST['type'] ) ){
						$error = _("La création de l'erratum a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
					}
				}
				if( !isset($error) ){
					if( isset($_POST['publish']) ){
						if( !cat_erratums_publish( $_GET['id'] ) ){
							$error = _("La publication de l'erratum a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
						}
					}
				}
				if( !isset($error) ){
					header('Location: index.php');
					exit;
				}
			}else{
				$error = _("Le chargement du produit a échoué pour une raison inconnue.");
			}
		}	
	}

	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();

	// Chargement de l'erratum
	$erratum = array('id'=>0,'prd_ref'=>'','prd_name'=>'','desc'=>'','date_published'=>'');
	if( isset($_GET['id']) && is_numeric($_GET['id']) && $_GET['id']>0 ){
		$erratum = ria_mysql_fetch_array(cat_erratums_get($_GET['id']));
		// Récupère les informations traduite
		if( $lng!=$config['i18n_lng'] ){
			$tsk_erratum = fld_translates_get( CLS_FAQ_CAT, $erratum['id'], $lng, $erratum, array(_FLD_ERR_DESC=>'desc' ), true );
			$erratum['desc'] = $tsk_erratum['desc'];
		}
	}

	if( isset($_POST['prd_ref']) ) $erratum['prd_ref'] = trim($_POST['prd_ref']);
	if( isset($_POST['prd_name']) ) $erratum['prd_name'] = trim($_POST['prd_name']);
	if( isset($_POST['desc']) ) $erratum['desc'] = trim($_POST['desc']);
	
	// Défini le titre de la page
	if( trim($erratum['prd_name']) ){
		$page_title = 'Erratum '.$erratum['prd_name'];
	}else{
		$page_title = 'Créer un erratum';
	}
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Erratums').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print htmlspecialchars( $page_title ); ?></h2>

	<?php

		// Affichage des messages d'erreur
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
		
		// Affiche le menu de langue
		if( isset($_GET['id']) && is_numeric($_GET['id']) && $_GET['id']>0 )
			print view_translate_menu( 'edit.php?id='.$erratum['id'], $lng );
	?>
	<div class="error none" id="error-ref"></div>

	<form id="erratum-form" action="edit.php?id=<?php print $erratum['id']; ?>&amp;lng=<?php print $lng; ?>" method="post" onsubmit="return erratumsValidForm(this)">
		<table>
			<tbody>
				<tr><th colspan="2"><?php print _('Propriétés générales'); ?></th></tr>
				<tr>
					<td><label for="prd_ref"><span class="mandatory">*</span> <?php print _('Référence du produit :'); ?></label></td>
					<td>
						<input type="text" name="prd_ref" value="<?php print htmlspecialchars($erratum['prd_ref']); ?>" maxlength="18" /> 
						<input type="button" value="Rechercher" onclick="erratumGetPrdName();"/>
					</td>
				</tr>
				<tr>
					<td><label for="prd_name"><span class="mandatory">*</span> <?php print _('Désignation du produit :'); ?></label></td>
					<td><input type="text" name="prd_name" id="prd-name" value="<?php print htmlspecialchars($erratum['prd_name']); ?>" maxlength="75" disabled="disabled" /></td>
				</tr>
				<tr>
					<td><label for="desc"><span class="mandatory">*</span> <?php print _('Commentaires :'); ?></label></td>
					<td><textarea name="desc" id="desc" cols="45" rows="15"><?php print htmlspecialchars($erratum['desc']); ?></textarea></td>
				</tr>
				<tr>
					<td><?php print _('S\'applique au :'); ?></td>
					<td>
						<input type="radio" class="radio" name="type" id="type-1" value="1" <?php if( isset($erratum['type_id']) && $erratum['type_id']==1 ) print 'checked="checked"'; ?> /> <label for="type-1"><?php print _('Site Internet'); ?></label>
						<input type="radio" class="radio" name="type" id="type-2" value="2" <?php if( isset($erratum['type_id']) && $erratum['type_id']==2 ) print 'checked="checked"'; ?> /> <label for="type-2"><?php print _('Catalogue Papier'); ?></label>
					</td>
				</tr>
				<?php if( isset($erratum['date_created']) ){ ?>
				<tr>
					<td><?php print _('Créé le :'); ?></td>
					<td><?php print $erratum['date_created']; ?></td>
				</tr>
				<?php }
				if( isset($erratum['usr_id']) ){ ?>
				<tr>
					<td><?php print _('Créé par :'); ?></td>
					<td><?php
						$rusr = gu_users_get( $erratum['usr_id'] );
						if( ria_mysql_num_rows($rusr) ){
							$usr = ria_mysql_fetch_array($rusr);
							print '<a href="/admin/customers/edit.php?usr='.$usr['id'].'">';
							if( $usr['ref'] ) print $usr['ref'].' - ';
							if( $usr['adr_invoices'] ){
								$adr = ria_mysql_fetch_array(gu_adresses_get($usr['id'],$usr['adr_invoices']));
								if( $adr['type_id']==1 ){
									print htmlspecialchars($adr['lastname']).', '.htmlspecialchars($adr['firstname']);
								}elseif( $adr['type_id']==2 ){
									print htmlspecialchars($adr['society']);
								}else{
									if( $adr['society']!='' ) print htmlspecialchars($adr['society']).', ';
									print htmlspecialchars($adr['lastname']).', '.htmlspecialchars($adr['firstname']);
								}
							}
							print '</a>';
						}
					?></td>
				</tr>
				<?php } ?>
				<tr>
					<td><?php print _('Publié le :'); ?></td>
					<td><?php print $erratum['date_published'] ? $erratum['date_published'] : _('Non publié'); ?></td>
				</tr>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<?php if( $erratum['date_published']=='' && gu_user_is_authorized('_RGH_ADMIN_TOOL_ERRATUM_PUBLISH') ){ ?>
					<input type="submit" name="publish" value="<?php print _('Publier'); ?>" />
					<?php } ?>
					<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
					<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" onclick="return erratumsCancelEdit(this.form)" />
					<?php if( $erratum['id']>0 && gu_user_is_authorized('_RGH_ADMIN_TOOL_ERRATUM_DEL') ){ ?>
					<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" onclick="return erratumsConfirmDel()" />
					<?php } ?>
				</td></tr>
			</tfoot>
		</table>
	</form>	
<?php
	require_once('admin/skin/footer.inc.php');
?>
