<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators.inc.php');
	
	// informations sur le comparateur
	if( isset($_GET['ctr']) ){
		$rctr = ctr_comparators_get( $_GET['ctr'], true, false, null );
		if( !$rctr || !ria_mysql_num_rows($rctr) )
			unset($_GET['ctr']);
		else
			$ctr = ria_mysql_fetch_array( $rctr );
	} else {
		$_GET['ctr'] = 0;
	}
	
	// identifiant d'une famille parente
	$_GET['cat'] = isset($_GET['cat'], $_GET['ctr']) && ctr_categories_exists($_GET['cat'], $_GET['ctr']) ? $_GET['cat'] : 0;
	
	if( isset($_POST['save-choose']) ){
		if( !isset($_POST['category']) || !is_numeric($_POST['category']) || $_POST['category']<=0 ) 
			$error = _('Veuillez sélectionner une famille dans la liste ci-dessous.');
		else {
			$_GET['cat'] = $_POST['category'];
			
			// rattache la catégorie à la famille du produit
			if( isset($_GET['prdcat']) && $_GET['prdcat'] && !ctr_prd_categories_add($_GET['ctr'], $_GET['prdcat'], $_POST['category']) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			} elseif( isset($_GET['prd']) && $_GET['prd'] && !ctr_catalogs_add($_GET['ctr'], $_GET['prd'], $_POST['category']) ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}
	}

	$parent = '';
	if( $_GET['cat']>0 ){
		$rparent = ctr_categories_get( $_GET['ctr'], $_GET['cat'], '', true );
		if( $rparent && ria_mysql_num_rows($rparent) ){
			$parent = ria_mysql_fetch_array( $rparent );
		}
	}
	
	$endurl = '';
	if( isset($_GET['prdcat']) )
		$endurl = '&amp;prdcat='.$_GET['prdcat'];
	elseif( isset($_GET['prd']) )
		$endurl =  '&amp;prd='.$_GET['prd'];

	define('ADMIN_PAGE_TITLE', _('Liste des catégories'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( !isset($_GET['ctr']) || (!isset($_GET['prdcat']) && !isset($_GET['prd'])) ){
		print '<div class="error">'._('Un problème est survenu lors du chargement des familles du comparateur.').'<br />'._('Veuillez réessayer ou prendre contact pour nous signaler le problème.').'</div>';
	} else {
		if( isset($error) ){
			print '<div class="error">'.nl2br($error).'</div>';
		}
?>
		<form action="/admin/catalog/popup-export-comparators.php?ctr=<?php print $_GET['ctr'].$endurl; ?>" method="post" onsubmit="return verifChoose();">
			<div class="margin-bottom-10">
				<?php if( $parent!='' ){ ?>
				<a href="/admin/catalog/popup-export-comparators.php?<?php print 'ctr='.$ctr['id'].'&amp;cat='.$parent['parent'].$endurl; ?>" class="float-left">
					<input type="button" name="filter" id="filter" value="<?php print _('Retour'); ?>" />
				</a><?php
				} ?>
				<div class="align-right" id="cat-name">
					<label for="filter"><?php print _('Rechercher une catégorie :'); ?><label>
					<input type="text" name="filter" id="filter" value="" />
				</div>
			</div>
			<table class="checklist" id="choose-cat-ctr">
				<caption>
					<?php if( $parent!='' ){
						print $parent['name'];
					} else { ?>
						<?php print _('Choisissez une famille du comparateur')?> "<?php print $ctr['name']; ?>"
					<?php } ?>
				</caption>
				<thead>
					<tr>
						<th id="cat-name"><?php print _('Désignation')?></th>
					</tr>
				</thead>
				<tbody><?php
					$pselected = false;
					if( is_array($parent) && sizeof($parent) )
						$pselected = in_array($ctr['id'], $config['ctr_free']) || $parent['cpc']>0;
					
					$rcat = ctr_categories_get( $ctr['id'], 0, $_GET['cat'], true );
					if( !$rcat || !ria_mysql_num_rows($rcat) ){
						if( $pselected && $_GET['cat']>0 ){
							print '	<tr>
										<td>
											<input checked="checked" type="radio" name="category" id="category" value="'.$_GET['cat'].'" />
											<label for="category">'._('Sélectionner cette famille').'</label>
										</td>
									</tr>';
						} else
							print '<tr><td>'._('Aucune famille n\'a été trouvée.').'</td></tr>';
					} else {
						$url = '/admin/catalog/popup-export-comparators.php?ctr='.$ctr['id'];
						if( isset($_GET['prdcat']) )
							$url .= '&amp;prdcat='.$_GET['prdcat'];
						elseif( isset($_GET['prd']) )
							$url .= '&amp;prd='.$_GET['prd'];
						
						while( $cat = ria_mysql_fetch_array($rcat) ){
							$selected = in_array($ctr['id'], $config['ctr_free']) || $cat['cpc']>0 || $ctr['marketplace'];
							$havechilds = ctr_categories_have_childs($ctr['id'], $cat['id']);
							if( !$selected && !$havechilds ) continue;
							
							$attr = '';
							if( isset($_GET['pre_select']) && $_GET['pre_select']==$cat['id'] )
								$attr .= ' checked="checked"';
							if( !$selected ){
								$attr .= ' disabled="disabled"';
								$attr .= ' title="'._('Vous ne pouvez choisir cette catégorie directement. Veuillez sélectionner une sous-catégorie.').'"';
							}
							
							print '	<tr>
										<td>
											<input'.$attr.' type="radio" name="category" id="category-'.$cat['id'].'" value="'.$cat['id'].'" />';
							if( ctr_categories_have_childs($ctr['id'], $cat['id']) )
								print '		<a href="'.$url.'&amp;cat='.$cat['id'].'">'.$cat['name'].'</a>';
							else
								print '		<label for="category-'.$cat['id'].'">'.$cat['name'].'</label>
										</td>';
							print '	</tr>';
						}
						
						if( $pselected && $_GET['cat']>0 ){
							print '	<tr>
										<td>
											<input type="radio" name="category" id="category" value="'.$_GET['cat'].'" />
											<label for="category">'._('Sélectionner cette famille').'</label>
										</td>
									</tr>';
						}
					}
				?></tbody>
				<tfoot>
					<tr>
						<td>
							<input type="submit" name="save-choose" id="save-choose" value="<?php print _('Choisir')?>" />
							<input type="button" value="<?php print _('Annuler')?>" onclick="parent.hidePopupChoose(<?php print $ctr['id']; ?>)" />
						</td>
					</tr>
				</tfoot>
			</table>
		</form>
		<?php } ?>
		<script>
			function verifChoose(){
				$('.error').remove();
				
				// vérifié qu'une famille est sélectionné
				if( !$('#choose-cat-ctr tbody input:checked').length ){
					window.scrollBy(0,-1000);
					$('#choose-cat-ctr').before('<div class="error"><?php print _('Veuillez sélectionner une famille faisant partie de la liste ci-dessous :'); ?>');
					return false;
				}
				
				return true;
			}
			<?php if( isset($_POST['save-choose']) && !isset($error) ){?>
				parent.hidePopupChoose(<?php print $_GET['ctr']; ?>, 'save', '<?php print isset($_GET['prdcat']) ? 'cat' : 'prd'; ?>');
			<?php }?>
			
			$(document).ready(function(){
				$('#filter').autocomplete({
					source:'/admin/ajax/comparators/search-categories.php?ctr=' + <?php print $_GET['ctr']; ?>,
					minLength: 2,
					select: function (event, ui) {
						event.preventDefault();
						data = ui.item.label.split('|');
						if( data[1] ){
							var url = '/admin/catalog/popup-export-comparators.php?ctr=<?php print $_GET['ctr'].mb_eregi_replace('amp;', '', $endurl); ?>&cat='+data[1];
							if( data[2] )
								url += '&pre_select=' + data[2];
							
							window.location.href = url;
						}
					},
				});
			});
		</script>
<?php
	require_once('admin/skin/footer.inc.php');