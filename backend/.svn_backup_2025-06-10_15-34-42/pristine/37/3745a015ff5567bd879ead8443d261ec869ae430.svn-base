<?php

/** \file GetMatchingProductForIdRequest.php
 * Cette classe permet d'instancier et d'envoyer une requête de type "GetMatchingProductForId".
 *
 * @see https://docs.developer.amazonservices.com/en_UK/products/Products_GetMatchingProductForId.html
 */

require_once __DIR__ . '/../../AmazonMWSRequest.php';

require_once 'comparators/MarketplaceWebServiceProducts/Model/GetMatchingProductForIdRequest.php';
require_once 'comparators/MarketplaceWebServiceProducts/Model/IdListType.php';

class GetMatchingProductForIdRequest extends AmazonMWSRequest
{
	protected $merchantIdField = 'SellerId';

	public function build()
	{
		$this->request = new MarketplaceWebServiceProducts_Model_GetMatchingProductForIdRequest;

		if( array_key_exists('MarketplaceId', $this->params) ){
			$this->request->withMarketplaceId($this->params['MarketplaceId']);
		}

		if( array_key_exists('IdType', $this->params) ){
			$this->request->withIdType($this->params['IdType']);
		}

		if( array_key_exists('IdList', $this->params) && array_key_exists('Id', $this->params['IdList']) ){
			$this->request->withIdList(
				new MarketplaceWebServiceProducts_Model_IdListType($this->params['IdList'])
			);
		}
	}

	public function send()
	{
		return $this->amazon->getClient()
			->getMatchingProductForId($this->request)
			->getGetMatchingProductForIdResult();
	}
}