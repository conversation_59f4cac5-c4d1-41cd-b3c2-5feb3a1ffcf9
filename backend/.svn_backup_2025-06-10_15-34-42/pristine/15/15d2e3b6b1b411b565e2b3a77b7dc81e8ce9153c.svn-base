<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/pubsub.proto

namespace Google\Cloud\PubSub\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Response for the `ListSubscriptions` method.
 *
 * Generated from protobuf message <code>google.pubsub.v1.ListSubscriptionsResponse</code>
 */
class ListSubscriptionsResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * The subscriptions that match the request.
     *
     * Generated from protobuf field <code>repeated .google.pubsub.v1.Subscription subscriptions = 1;</code>
     */
    private $subscriptions;
    /**
     * If not empty, indicates that there may be more subscriptions that match
     * the request; this value should be passed in a new
     * `ListSubscriptionsRequest` to get more subscriptions.
     *
     * Generated from protobuf field <code>string next_page_token = 2;</code>
     */
    private $next_page_token = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Cloud\PubSub\V1\Subscription[]|\Google\Protobuf\Internal\RepeatedField $subscriptions
     *           The subscriptions that match the request.
     *     @type string $next_page_token
     *           If not empty, indicates that there may be more subscriptions that match
     *           the request; this value should be passed in a new
     *           `ListSubscriptionsRequest` to get more subscriptions.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Pubsub\V1\Pubsub::initOnce();
        parent::__construct($data);
    }

    /**
     * The subscriptions that match the request.
     *
     * Generated from protobuf field <code>repeated .google.pubsub.v1.Subscription subscriptions = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getSubscriptions()
    {
        return $this->subscriptions;
    }

    /**
     * The subscriptions that match the request.
     *
     * Generated from protobuf field <code>repeated .google.pubsub.v1.Subscription subscriptions = 1;</code>
     * @param \Google\Cloud\PubSub\V1\Subscription[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSubscriptions($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\PubSub\V1\Subscription::class);
        $this->subscriptions = $arr;

        return $this;
    }

    /**
     * If not empty, indicates that there may be more subscriptions that match
     * the request; this value should be passed in a new
     * `ListSubscriptionsRequest` to get more subscriptions.
     *
     * Generated from protobuf field <code>string next_page_token = 2;</code>
     * @return string
     */
    public function getNextPageToken()
    {
        return $this->next_page_token;
    }

    /**
     * If not empty, indicates that there may be more subscriptions that match
     * the request; this value should be passed in a new
     * `ListSubscriptionsRequest` to get more subscriptions.
     *
     * Generated from protobuf field <code>string next_page_token = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setNextPageToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->next_page_token = $var;

        return $this;
    }

}

