<?xml version="1.0" encoding="UTF-8"?>
<ListOrdersByNextTokenResponse xmlns="https://mws.amazonservices.com/Orders/2013-09-01">
   <ListOrdersByNextTokenResult>
        <NextToken>String</NextToken>
        <CreatedBefore>1969-07-21T02:56:03Z</CreatedBefore>
        <LastUpdatedBefore>1969-07-21T02:56:03Z</LastUpdatedBefore>
        <Orders>
            <Order>
                <AmazonOrderId>String</AmazonOrderId>
                <SellerOrderId>String</SellerOrderId>
                <PurchaseDate>1969-07-21T02:56:03Z</PurchaseDate>
                <LastUpdateDate>1969-07-21T02:56:03Z</LastUpdateDate>
                <OrderStatus>String</OrderStatus>
                <FulfillmentChannel>String</FulfillmentChannel>
                <SalesChannel>String</SalesChannel>
                <OrderChannel>String</OrderChannel>
                <ShipServiceLevel>String</ShipServiceLevel>
                <ShippingAddress>
                    <Name>String</Name>
                    <AddressLine1>String</AddressLine1>
                    <AddressLine2>String</AddressLine2>
                    <AddressLine3>String</AddressLine3>
                    <City>String</City>
                    <County>String</County>
                    <District>String</District>
                    <StateOrRegion>String</StateOrRegion>
                    <PostalCode>String</PostalCode>
                    <CountryCode>String</CountryCode>
                    <Phone>String</Phone>
                </ShippingAddress>
                <OrderTotal>
                    <CurrencyCode>String</CurrencyCode>
                    <Amount>String</Amount>
                </OrderTotal>
                <NumberOfItemsShipped>1</NumberOfItemsShipped>
                <NumberOfItemsUnshipped>1</NumberOfItemsUnshipped>
                <PaymentExecutionDetail>
                    <PaymentExecutionDetailItem>
                        <Payment>
                        <CurrencyCode>String</CurrencyCode>
                        <Amount>String</Amount>
                        </Payment>
                        <PaymentMethod>String</PaymentMethod>
                    </PaymentExecutionDetailItem>
                </PaymentExecutionDetail>
                <PaymentMethod>String</PaymentMethod>
                <PaymentMethodDetails>
                    <PaymentMethodDetail>String</PaymentMethodDetail>
                </PaymentMethodDetails>
                <MarketplaceId>String</MarketplaceId>
                <BuyerEmail>String</BuyerEmail>
                <BuyerName>String</BuyerName>
                <BuyerCounty>String</BuyerCounty>
                <BuyerTaxInfo>
                    <CompanyLegalName>String</CompanyLegalName>
                    <TaxingRegion>String</TaxingRegion>
                    <TaxClassifications>
                        <TaxClassification>
                        <Name>String</Name>
                        <Value>String</Value>
                        </TaxClassification>
                    </TaxClassifications>
                </BuyerTaxInfo>
                <ShipmentServiceLevelCategory>String</ShipmentServiceLevelCategory>
                <ShippedByAmazonTFM>true</ShippedByAmazonTFM>
                <TFMShipmentStatus>String</TFMShipmentStatus>
                <CbaDisplayableShippingLabel>String</CbaDisplayableShippingLabel>
                <OrderType>String</OrderType>
                <EarliestShipDate>1969-07-21T02:56:03Z</EarliestShipDate>
                <LatestShipDate>1969-07-21T02:56:03Z</LatestShipDate>
                <EarliestDeliveryDate>1969-07-21T02:56:03Z</EarliestDeliveryDate>
                <LatestDeliveryDate>1969-07-21T02:56:03Z</LatestDeliveryDate>
                <IsBusinessOrder>true</IsBusinessOrder>
                <PurchaseOrderNumber>String</PurchaseOrderNumber>
                <IsPrime>true</IsPrime>
                <IsPremiumOrder>true</IsPremiumOrder>
                <ReplacedOrderId>String</ReplacedOrderId>
                <IsReplacementOrder>true</IsReplacementOrder>
            </Order>
        </Orders>
    </ListOrdersByNextTokenResult>
   <ResponseMetadata>
        <RequestId>String</RequestId>
    </ResponseMetadata>
</ListOrdersByNextTokenResponse>
