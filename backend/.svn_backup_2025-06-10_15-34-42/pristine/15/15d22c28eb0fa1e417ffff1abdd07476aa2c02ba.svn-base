<?php

// \cond onlyria
/**	\defgroup model_fields_types Types de champs
 * 	\ingroup model_fields
 *
 *	Ce module comprend les fonctions nécessaires à la gestion des types de champs.
 *	Les types de champs sont définies à l'avance lors du développement, et ne
 *	peuvent pas être manipulés par les utilisateurs.
 *	@{
 */

/**	Cette fonction permet le chargement d'un ou plusieurs types de champs, filtrés
 *	en fonction des paramètres optionnels fournis.
 *
 *	@param int $id Optionnel, identifiant d'un type de champ sur lequel filtrer le résultat
 *	@param bool $for_admin_display Optionnel, détermine si les types retournés sont ceux disponibles pour la gestion des champs avancés de l'admin ( REFERENCES_ID est exclu )
 *	@param bool $include_fld_generic Optionnel, détermine si la jointure avec fields prends en compte les champs libres génériques
 *
 *	@return resource un résultat de requête MySQL comprenant les champs suivants :
 *			- id : identifiant du type de champ
 *			- name : désignation du type de champ
 *			- fields : nombre de champ ayant ce type
 */
function fld_types_get( $id=0, $for_admin_display=true, $include_fld_generic=true ){
	global $config;

	if( !is_numeric($id) || $id<0 ) return false;

	$sql = '
		select
			type_id as id,
			type_name as name, (
				select count(*) from fld_fields where fld_date_deleted is null and type_id=fld_type_id and ( '.( $include_fld_generic ? 'fld_tnt_id=0 or ' : '' ).' fld_tnt_id='.$config['tnt_id'].' )
			) as "fields"
		from fld_types
		where 1
	';
	if( $id>0 ) $sql .= ' and type_id='.$id;
	if( $for_admin_display ) $sql .= ' and type_id not in ('.FLD_TYPE_REFERENCES_ID.')';

	$sql .= '
		order by type_id asc
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction permet le chargement de la désignation d'un type de champ
 *	à partir de son identifiant.
 *	@param int $id Obligatoire, identifiant du type de champ à résoudre
 *	@return string le nom associé à l'identifiant passé en paramètre
 *	@return bool false si l'identifiant est invalide ou si une erreur s'est produite
 */
function fld_types_get_name( $id ){
	$rfld = fld_types_get($id,false);
	if( $rfld===false ) return false;
	if( ria_mysql_num_rows($rfld)==0 ) return false;
	$fld = ria_mysql_fetch_array($rfld);
	return $fld['name'];
}

/**	Cette fonction permet la vérification d'un identifiant de type de champ.
 *	@param int $id Obligatoire, identifiant à vérifier
 *	@return bool true si l'identifiant existe
 *	@return bool false si l'identifiant est invalide ou ne correspond à aucun type
 *	de champ enregistré.
 */
function fld_types_exists( $id ){

	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_num_rows(ria_mysql_query('select type_id from fld_types where type_id='.$id))==1;

}

/**	Cette fonction détermine le nombre de champs d'un type donné.
 *	@param int $id Obligatoire, identifiant du type de champ
 *	@param bool $include_fld_generic Facultatif, détermine si les champs libres génériques sont
 *	@return int le nombre de champs du type passé en paramètre
 *	@return bool false en cas d'erreur
 */
function fld_types_get_fields_count( $id, $include_fld_generic=true ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$rcount = ria_mysql_query('
		select count(*)
		from fld_fields
		where fld_date_deleted is null and (
			'.( $include_fld_generic ? 'fld_tnt_id=0 or ' : '' ).' fld_tnt_id='.$config['tnt_id'].'
		) and fld_type_id='.$id
	);

	if( ria_mysql_num_rows($rcount) ){
		return ria_mysql_result($rcount,0,0);
	}else{
		return 0;
	}
}

/// @}
// \endcond
