<?php
    /** \file groupauto-export-stats.php
    * 	Ce script réalise un export des stats de GROUPAUTO et l'envoi ensuite pas mail.
    */

    set_include_path(dirname(__FILE__).'/../include/');
    require_once( 'env.inc.php');
    require_once('define.inc.php');
    require_once('db.inc.php');
    require_once('ria.mysql.inc.php');
    require_once('strings.inc.php');
    require_once('email.inc.php');
    require_once('devices.inc.php');

    $ar_tnt_ids = array(11, 61, 64, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 92, 116);
    $ar_connections = array();

	require_once('RegisterGCP.inc.php');
	if (RegisterGCP::onGcloud()) {
        // Liste des serveurs sur lequels se trouve un tenant Groupauto
        foreach ($ar_tnt_ids as $tnt_id) {
            $tmp = RegisterGCP::create()->getConnections($tnt_id);

            if (is_array($tmp) && count($tmp)) {
                $ar_connections = array_merge($ar_connections, $tmp);
            }

        }

        foreach ($ar_connections as $connection) {
            RegisterGCPConnection::connect($connection);
            send_groupauto_stats($ar_tnt_ids);
        }
    } else {
        send_groupauto_stats($ar_tnt_ids);
    }

    function send_groupauto_stats($ar_tnt_ids) {
        $year = date('Y');
        $month = date('m') - 1;

        if (date('m') == 1) {
            $year = $year - 1;
            $month = 12;
        }

        $exclude_state = implode(', ', array(_STATE_BASKET, _STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND, _STATE_DEVIS));

        $sql_stats_yuto = '
            select tnt_id, tnt_name, s.nb_tablette,
                n1.ca as "CA Yuto n-1", n1.nb as "Nb cmd Yuto n-1",
                n.ca as "CA Yuto n", n.nb as "Nb cmd Yuto n",
                date_format(nmonth.cmddate, "%m") as "Mois", nmonth.ca as "CA Yuto Mois n", nmonth.nb as "Nb cmd Yuto Mois n"
            from tnt_tenants
                left join (
                    select count(dev_tnt_id) as nb_tablette, dev_tnt_id
                    from dev_devices
                    join gu_users on dev_tnt_id = usr_tnt_id and dev_usr_id = usr_id
                    where dev_is_active = 1 and dev_date_deleted is null
                    group by dev_tnt_id
                ) as s on s.dev_tnt_id = tnt_id

                left join (
                    SELECT ord_tnt_id, sum(ord_total_ht) as ca, count(ord_id) as nb, min(ord_date)
                    FROM ord_orders
                    join tnt_websites on ord_wst_id = wst_id and ord_tnt_id=wst_tnt_id
                    where wst_wty_id=6 and ord_pay_id is not null and ord_state_id not in ('.$exclude_state.')
                    and ord_date > "'.($year-1).'-01-01"  and ord_date < "'.$year.'-01-01"
                    group by ord_tnt_id
                ) as n1 on n1.ord_tnt_id = tnt_id

                left join (
                    SELECT ord_tnt_id, sum(ord_total_ht) as ca, count(ord_id) as nb, min(ord_date)
                    FROM ord_orders
                    join tnt_websites on ord_wst_id = wst_id and ord_tnt_id=wst_tnt_id
                    where wst_wty_id=6 and ord_pay_id is not null and ord_state_id not in ('.$exclude_state.')
                    and ord_date > "'.$year.'-01-01"  and ord_date < "'.($year+1).'-01-01"
                    group by ord_tnt_id
                ) as n on n.ord_tnt_id = tnt_id

                left join (
                    SELECT ord_tnt_id, sum(ord_total_ht) as ca, count(ord_id) as nb, min(ord_date)  as cmddate
                    FROM ord_orders
                    join tnt_websites on ord_wst_id = wst_id and ord_tnt_id=wst_tnt_id
                    where wst_wty_id=6 and ord_pay_id is not null and ord_state_id not in ('.$exclude_state.')
                    and ord_date > "'.$year. '-01-01"  and ord_date < "'.($year+1).'-01-01"
                    group by ord_tnt_id, date_format(ord_date,"%m")
                ) as nmonth on nmonth.ord_tnt_id = tnt_id
            where tnt_id in ('.implode(', ', $ar_tnt_ids).')
        ';

        $sql_stats_period = '
            SELECT tnt_name, ord_tnt_id, sum(ord_total_ht) as ca, count(ord_id) as nb, min(ord_date)
            FROM ord_orders
                join tnt_websites on ord_wst_id = wst_id and ord_tnt_id=wst_tnt_id
                join tnt_tenants on tnt_id = wst_tnt_id
            where wst_wty_id=6 and ord_pay_id is not null and ord_state_id not in ('.$exclude_state.')
                and ord_date >= "'.($year-1).'-01-01"  and ord_date < "'.($year-1). '-'.(($month + 1) > 12 ? 12 : ($month + 1)).'-01"
                and wst_tnt_id in ('.implode(', ', $ar_tnt_ids).')
            group by ord_tnt_id
        ';


        $sql_stats_total = str_replace('wst_wty_id=6 and ord_pay_id is not null and ', '', $sql_stats_yuto);

        $r_stat_yuto = ria_mysql_query($sql_stats_yuto);
        if (!$r_stat_yuto || !ria_mysql_num_rows($r_stat_yuto)) {
            error_log('Erreur SQL export GROUPAUTO : '.$sql_stats_yuto.' => '.ria_mysql_error());
        }

        $r_stat_total = ria_mysql_query($sql_stats_total);
        if (!$r_stat_total || !ria_mysql_num_rows($r_stat_total)) {
            error_log('Erreur SQL export GROUPAUTO : '.$sql_stats_total.' => '.ria_mysql_error());
        }

        $r_stat_period = ria_mysql_query($sql_stats_period);
        if (!$r_stat_period || !ria_mysql_num_rows($r_stat_period)) {
            error_log('Erreur SQL export GROUPAUTO : '.$sql_stats_period.' => '.ria_mysql_error());
        }

        $header_csv = array(
            'Tenant_ID',
            'Sociétés',

            'Nombre de commerciaux',
            'Nombre de licences actives',

            'CA Yuto n-1',
            'Nombre de commandes Yuto n-1',

            'CA Yuto n-1 periode',
            'Nombre de commandes Yuto n-1 période',

            'CA Yuto n',
            'Nombre de commande Yuto n',
        );

        for ($i=1; $i < 13; $i++) {
            if ($i > $month) {
                break;
            }

            $month_name = month_french($i);
            $header_csv = array_merge($header_csv, array(
                'CA YUTO '.$month_name.' '.$year,
                'Nombre de commandes YUTO '.$month_name.' '.$year,
                'CA Total '.$month_name.' '.$year,
            ));
        }

        $data_one_tenant = array(
            'Tenant_ID' => '',
            'Sociétés' => '',

            'Nombre de commerciaux' => 0,
            'Nombre de licences actives' => 0,

            'CA Yuto n-1' => 0,
            'Nombre de commandes Yuto n-1' => 0,

            'CA Yuto n-1 periode' => 0,
            'Nombre de commandes Yuto n-1 période' => 0,

            'CA Yuto n' => 0,
            'Nombre de commande Yuto n' => 0,
        );

        for ($i = 1; $i < 13; $i++) {
            if ($i > $month) {
                break;
            }

            $month_name = month_french($i);
            $data_one_tenant = array_merge($data_one_tenant, array(
                'CA YUTO ' . $month_name . ' ' . $year => 0,
                'Nombre de commandes YUTO ' . $month_name . ' ' . $year => 0,
                'CA Total ' . $month_name . ' ' . $year => 0,
            ));
        }

        $init_tenant = array();

        $data = array();

        while ($stat_yuto = ria_mysql_fetch_assoc($r_stat_yuto)) {
            if (!in_array($stat_yuto['tnt_name'], $init_tenant)) {
                $data[$stat_yuto['tnt_name']] = $data_one_tenant;
                $init_tenant[] = $stat_yuto['tnt_name'];

                $GLOBALS['config']['tnt_id'] = $stat_yuto['tnt_id'];
                $r_dev = dev_devices_get(0, 0, '', -1, '=', false, false, true, true);
                $data[$stat_yuto['tnt_name']]['Nombre de licences actives'] = $r_dev ? ria_mysql_num_rows($r_dev) : 0;
            }

            $data[$stat_yuto['tnt_name']]['Tenant_ID'] = $stat_yuto['tnt_id'];
            $data[$stat_yuto['tnt_name']]['Sociétés'] = $stat_yuto['tnt_name'];


            $data[$stat_yuto['tnt_name']]['CA Yuto n-1'] = $stat_yuto['CA Yuto n-1'];
            $data[$stat_yuto['tnt_name']]['Nombre de commandes Yuto n-1'] = $stat_yuto['Nb cmd Yuto n-1'];

            $data[$stat_yuto['tnt_name']]['CA Yuto n'] = $stat_yuto['CA Yuto n'];
            $data[$stat_yuto['tnt_name']]['Nombre de commande Yuto n'] = $stat_yuto['Nb cmd Yuto n'];

            $data_month = month_french((int) $stat_yuto['Mois']);

            if (array_key_exists('CA YUTO '.$data_month.' '.$year,  $data[$stat_yuto['tnt_name']])) {
                $data[$stat_yuto['tnt_name']]['CA YUTO '.$data_month.' '.$year] = $stat_yuto['CA Yuto Mois n'];
                $data[$stat_yuto['tnt_name']]['Nombre de commandes YUTO '.$data_month.' '.$year] = $stat_yuto['Nb cmd Yuto Mois n'];
            }
        }

        while ($stat_total = ria_mysql_fetch_assoc($r_stat_total)) {
            if (!in_array($stat_total['tnt_name'], $init_tenant)) {
                $data[$stat_total['tnt_name']] = $data_one_tenant;
                $init_tenant[] = $stat_total['tnt_name'];
            }


            $data_month = month_french((int) $stat_total['Mois']);

            if (array_key_exists('CA YUTO '.$data_month.' '.$year,  $data[$stat_total['tnt_name']])) {
                $data[$stat_total['tnt_name']]['CA Total '.$data_month.' '.$year] = $stat_total['CA Yuto Mois n'];
            }
        }

        while ($stat_period = ria_mysql_fetch_assoc($r_stat_period)) {
            if (!in_array($stat_period['tnt_name'], $init_tenant)) {
                $data[$stat_period['tnt_name']] = $data_one_tenant;
                $init_tenant[] = $stat_period['tnt_name'];
            }

            $data[$stat_period['tnt_name']]['CA Yuto n-1 periode'] = $stat_period['ca'];
            $data[$stat_period['tnt_name']]['Nombre de commandes Yuto n-1 période'] = $stat_period['nb'];
        }

        // Retire l'identifiant du CSV
        unset($header_csv[0]);

        $filename = dirname(__FILE__).'/tmp/groupauto-stats.csv';
        $handle = fopen($filename, 'w');
        fputcsv($handle, $header_csv, ';', '"');

        foreach ($data as $tnt_name => $one_d) {
            unset($one_d['Tenant_ID']);
            fputcsv($handle, $one_d, ';', '"');
        }

        fclose($handle);

        $GLOBALS['config']['wst_id'] = 0;

        $email = new Email();

        $email->setFrom('<EMAIL>');
        $email->addTo('<EMAIL>');
        $email->addBcc('<EMAIL>,<EMAIL>');
        $email->setSubject('Statistiques GROUPAUTO du '.date('d/m/Y'));
        $email->addAttachment($filename);
        $email->addParagraph('Ci-joint le rapport de statistiques pour GROUPAUTO.');

        if (!$email->send()) {
            error_log('Erreur lors de l\'envoi des stats pour GROUPAUTO');
        }
    }
