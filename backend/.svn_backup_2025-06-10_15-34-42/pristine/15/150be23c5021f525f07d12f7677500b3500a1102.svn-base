<?php

/**	\file ajax-dlv-opening.php
 * 
 * 	Ce fichier permet l'intéraction en Ajax avec les fonctions du modèle des horaires d'ouverture (point de vente)
 * 
 */

// Vérifie que l'utilisateur en cours à le droit de charger les magasins
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE');

// Vérifie les paramètres str et day
if( !isset( $_GET['str'] ) || !isset( $_GET['day'] ) ){
	header('location: /admin');
	exit;
}

$str_id = $_GET['str'];
if( !dlv_stores_exists($str_id) ){
	header('location: /admin');
	exit;
}

$day = dlv_day_get_id($_GET['day']);

// Supprime les périodes déjà enregistrées
if( !dlv_store_opening_del($str_id, $day) ){
	$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires d'ouverture du magasin.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur");
}

// Parcour tous les jours de la semaine
if( isset($_POST['hr']) && !isset($error) ){
	$temp = $count = $start = $end = 0;
	$first = true;
	// Parcour chaque heure pour trouver les périodes d'horaires
	foreach( $_POST['hr'] as $key=>$hour ){
			if( $first ) {
				$start = $hour;
				$temp = $hour;
				$count++;
				$first = false;
			}
			
			$h = $hour - $temp;
			if( $h>1 ){ // Il s'agi d'une autre période
				
				// On enregistre la période
				$end = $end>0 ? $end : $start;
				if( !dlv_store_opening_add($str_id, $_GET['day'], $start, $end) ){
					$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires d'ouverture du magasin.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur");
					break;
				}
				$start = $hour;
				$temp = $hour;
				$count = 0;
			} else {
				// Il s'agit de la même périod
				$end = $hour;
				$temp = $hour;
				$count++;
			}
			$end = $hour;
	}
	// On enregistre la dernière période
	if( !dlv_store_opening_add($str_id, $_GET['day'], $start, $end) ){
		$error = _("Une erreur inattendue est survenue lors de l'enregistrement des horaires d'ouverture du magasin.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur");
	}
}
if( isset($error) ){
	echo $error;
	exit;
}

print json_encode( array('success' => _('Les horaires ont bien été enregistrés')) );
exit;