{"name": "giggsey/libphonenumber-for-php", "type": "library", "description": "PHP Port of Google's libphonenumber", "keywords": ["phonenumber", "libphonenumber", "mobile", "validation", "geocoding", "geolocation"], "homepage": "https://github.com/giggsey/libphonenumber-for-php", "license": "Apache-2.0", "support": {"irc": "irc://irc.appliedirc.com/lobby", "source": "https://github.com/giggsey/libphonenumber-for-php", "issues": "https://github.com/giggsey/libphonenumber-for-php/issues"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "autoload-dev": {"psr-4": {"libphonenumber\\buildtools\\": "build/", "libphonenumber\\Tests\\": "tests/"}}, "archive": {"exclude": ["tests/", "build/", "/.travis.yml", "/build.xml", "phpunit.xml.dist", "libphonenumber-for-php.spec", "/.styleci.yml"]}, "require": {"php": ">=5.3.2", "ext-mbstring": "*", "giggsey/locale": "^1.2"}, "require-dev": {"phing/phing": "^2.7", "pear/versioncontrol_git": "^0.5", "pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "phpunit/phpunit": "^4.8|^5.0", "symfony/console": "^2.8|^3.0", "satooshi/php-coveralls": "^1.0"}, "extra": {"branch-alias": {"dev-master": "8.x-dev"}}}