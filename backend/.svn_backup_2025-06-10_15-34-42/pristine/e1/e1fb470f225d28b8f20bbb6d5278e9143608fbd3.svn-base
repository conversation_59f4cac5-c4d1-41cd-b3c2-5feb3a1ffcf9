<?php
/**
 * \defgroup sms_api Gestion Api des partenaires
 * \ingroup sms
 * Ce module comporte les classe qui gère les Api pour chaque partenaire
 * @{
*/
/**\interface SmsInterface
 * \brief Toutes classe qui gère l'envois de sms dois implémenter cette interface
 * 	ce qui permet de changer d'api à volonté sans compromètre l'éxécution de l'envois
 */
interface SmsInterface{

	/** Permet de configurer l'objet pour communiquer avec l'api
	 * \param  $api tableau associatif avec les identifiactions de connexion
	 */
	public function genConnection(array $api);

	/** Permet de vérifier si un numéros mobile est bien au format international
	 * \param  $phone numéro mobile
	 * \return true si c'est le bon format, false si non
	 */
	Public function checkIsInternational( $phone );

	/** Permet d'initialisé un destinataire
	 * \param $receiver Information du destinataire (généralement un numéro mobile)
	 * (si c'est un mobile une vérifiaction avec self::checkIsInternational dois être obligatoire )
	 */
	public function setReceiver( $receiver );

	/** Permet d'envoyer le sms à travers l'Api
	 * \param  $message Message à envoyer au destinataire
	 * \return Dois retourner la réponse de l'Api
	 */
	public function send( $message );

}
/// @}