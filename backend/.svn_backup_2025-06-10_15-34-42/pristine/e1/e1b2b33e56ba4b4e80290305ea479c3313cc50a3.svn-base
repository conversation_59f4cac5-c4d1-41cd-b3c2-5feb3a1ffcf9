<?php

/** \file GetProductCategoriesForASINRequest.php
 * Cette classe permet d'instancier et d'envoyer une requête de type "GetMatchingProductForId".
 *
 * @see http://docs.developer.amazonservices.com/en_UK/products/Products_GetProductCategoriesForASIN.html
 */

require_once __DIR__ . '/../../AmazonMWSRequest.php';

require_once 'comparators.inc.php';

require_once 'comparators/MarketplaceWebServiceProducts/Model/GetProductCategoriesForASINRequest.php';

class GetProductCategoriesForASINRequest extends AmazonMWSRequest
{
	protected $merchantIdField = 'SellerId';

	public function build()
	{
		$this->request = new MarketplaceWebServiceProducts_Model_GetProductCategoriesForASINRequest;

		if( array_key_exists('MarketplaceId', $this->params) ){
			$this->request->withMarketplaceId($this->params['MarketplaceId']);
		}

		if( array_key_exists('ASIN', $this->params) ){
			$this->request->withASIN($this->params['ASIN']);
		}
	}

	public function send()
	{
		$categories = array();

		$result = $this->amazon->getClient()
			->getProductCategoriesForASIN($this->request)
			->getGetProductCategoriesForASINResult()
			->getSelf()[0];

		while( $result->isSetParent() ){
			$id = ctr_categories_exists_ref(CTR_AMAZON, $result->getProductCategoryId());

			$categories[] = ria_mysql_fetch_array(
				ctr_categories_get(CTR_AMAZON, $id)
			);

			$result = $result->getParent();
		}

		return $categories;
	}
}