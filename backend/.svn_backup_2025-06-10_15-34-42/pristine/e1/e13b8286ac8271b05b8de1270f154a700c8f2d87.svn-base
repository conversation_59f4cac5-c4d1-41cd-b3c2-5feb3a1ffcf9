<?php

/** \defgroup model_object_serials Gestion des lots
 *	Ce module comprend les fonctions nécessaires à la gestion des lots sur les produits / commandes..
 *	@{
 */

/** Permet de tester l'existance d'un lot
 *
 * @param  int $cls_id Obligatoire, Identifiant de la classe
 * @param  int $obj_id_0 Obligatoire, Identifiant 1 de l'objet
 * @param  int $obj_id_1 Facultatif, Identifiant 2 de l'objet. 0 par defaut
 * @param  int $obj_id_2 Facultatif, Identifiant 2 de l'objet. 0 par defaut
 * @param  String $serial Facultatif, Serial de l'objet. '' par defaut
 *
 * @return bool true si le lot existe, false autrement
 */
function obj_serials_exists($cls_id, $obj_id_0, $obj_id_1=0, $obj_id_2=0, $serial=""){
	global $config;

	if( !is_numeric($cls_id) || $cls_id <= 0){
		return false;
	}
	if( !is_numeric($obj_id_0) || $obj_id_0 <= 0){
		return false;
	}
	if( !is_numeric($obj_id_1) ){
		return false;
	}
	if( !is_numeric($obj_id_2) ){
		return false;
	}

	$sql = '
		select 1 from obj_serials
		where osl_tnt_id='.$config['tnt_id'].'
			and osl_cls_id='.$cls_id.'
			and osl_obj_id_0='.$obj_id_0.'
			and osl_obj_id_1='.$obj_id_1.'
			and osl_obj_id_2='.$obj_id_2.'
			and osl_serial=\''.addslashes($serial).'\'
	';

	return ria_mysql_num_rows(ria_mysql_query($sql))>0;
}

/** Permet de récuprer une ligne de lot
 *
 * @param  int $cls_id Obligatoire, Identifiant de la classe
 * @param  int $obj_id_0 Facultatif, Identifiant 1 de l'objet. null par defaut
 * @param  int $obj_id_1 Facultatif, Identifiant 2 de l'objet. null par defaut
 * @param  int $obj_id_2 Facultatif, Identifiant 2 de l'objet. null par defaut
 * @param  String $serial Facultatif, Serial de l'objet. null par defaut
 * @param  int $dps_id Facultatif, depôt de l'objet. 0 par defaut
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- cls_id : Identifiant de la classe
 *		- obj_id_0 : Identifiant 1 de l'objet
 *		- obj_id_1 : Identifiant 2 de l'objet
 *		- obj_id_2 : Identifiant 3 de l'objet
 *		- date_dlc : date de limite de consomation
 *		- date_production : date de production
 *		- dps_id : identifiant du dépot
 *		- serial : numéro de serie
 *		- qte : Quantité
 *		- ref_gescom : Référence de la gescom
 */
function obj_serials_get($cls_id=0, $obj_id_0=null, $obj_id_1=null, $obj_id_2=null, $serial=null, $dps_id=0 ){
	global $config;

	if( !is_numeric($cls_id) ){
		return false;
	}
	if( $obj_id_0 !== null && !is_numeric($obj_id_0) ){
		return false;
	}
	if( $obj_id_1 !== null && !is_numeric($obj_id_1) ){
		return false;
	}
	if( $obj_id_2 !== null && !is_numeric($obj_id_2) ){
		return false;
	}

	$sql = '
		select
			osl_id as id,
			osl_cls_id as cls_id,
			osl_obj_id_0 as obj_id_0,
			osl_obj_id_1 as obj_id_1,
			osl_obj_id_2 as obj_id_2,
			osl_date_dlc as date_dlc,
			osl_date_production as date_production,
			osl_dps_id as dps_id,
			osl_serial as serial,
			osl_ref_gescom as ref_gescom,
			osl_qte as qte
		from obj_serials
		where osl_tnt_id='.$config['tnt_id'];

	if ($cls_id > 0) {
		$sql = $sql.' and osl_cls_id='.$cls_id;
	}
	if ($obj_id_0 !== null) {
		$sql = $sql.' and osl_obj_id_0='.$obj_id_0;
	}
	if ($obj_id_1 !== null) {
		$sql = $sql.' and osl_obj_id_1='.$obj_id_1;
	}
	if ($obj_id_2 !== null) {
		$sql = $sql.' and osl_obj_id_2='.$obj_id_2;
	}

	if ($serial !== null && trim($serial) != "") {
		$sql = $sql.' and osl_serial=\''.addslashes($serial).'\'';
	}
	if ($dps_id > 0) {
		$sql = $sql.' and (osl_dps_id='.$dps_id.' or osl_dps_id is null)';
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer la liste des seriales pour un ou plusieurs objets source
 *	@param $cls Obligatoire, identifiant de la classe concerné
 *	@param $obj Obligatoire, identifiant de l'objet ou tableau d'identifiants our les clés composées ou tableau de tableau d'identifiants pour les clés composées
 *	@param $multi_key Facultatif, permet de dire si le paramètre obj est un tableau de tableau de clé ou seulement un tableau d'id
 *	@return bool false en cas d'échec ou un résultat de ria_mysql_query avec les colonnes suivantes :
 *		- id : identifiant du serial
 *		- cls_id : identifiant de la classe de l'objet
 *		- obj_id_0 : identifiant de l'objet
 *		- obj_id_1 : identifiant de l'objet (clé composée - 0 sinon)
 *		- obj_id_2 : identifiant de l'objet (clé composée - 0 sinon)
 *		- date_dlc : date de limite de consomation
 *		- date_production : date de production
 *		- dps_id : identifiant du dépot
 *		- serial : numéro de serie
 *		- qte : Quantité
 *		- ref_gescom : Référence de la gescom
 */
function obj_serials_get_all($cls, $obj, $multi_key=false){

	if( !is_numeric($cls) || $cls <= 0 ){
		return false;
	}

	if( $multi_key ){
		if( !is_array($obj) ){
			$obj = array($obj);
			// return false;
		}
		foreach( $obj as $k => $o ){
			$obj[$k] = control_array_integer( $o, true );
			if( $obj[$k] === false ){
				return false;
			}elseif( !$obj[$k][0] ){
				return false;
			}
		}
	}else{
		if( is_numeric($obj) ){
			$obj = array($obj);
		}

		$obj = control_array_integer( $obj, true );
		if( $obj === false ){
			return false;
		}elseif( !$obj[0] ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			osl_id as id,
			osl_cls_id as cls_id,
			osl_obj_id_0 as obj_id_0,
			osl_obj_id_1 as obj_id_1,
			osl_obj_id_2 as obj_id_2,
			osl_date_dlc as date_dlc,
			osl_date_production as date_production,
			osl_dps_id as dps_id,
			osl_serial as serial,
			osl_ref_gescom as ref_gescom,
			osl_qte as qte
		from obj_serials

		where osl_tnt_id = '.$config['tnt_id'].' and osl_cls_id = '.$cls.'
	';

	if( $multi_key ){
		$wheres = array();
		$wheres_single = array();
		foreach( $obj as $o ){
			if (is_numeric($o)) {
				$wheres_single[] = $o;
			}else if( sizeof( $o ) == 1 ){
				$wheres_single[] = $o[0];
			}else{
				$where = ' osl_obj_id_0 = '.$o[0];
				if( isset($o[1]) ){
					$where .= ' and osl_obj_id_1 = '.$o[1];
				}
				if( isset($o[2]) ){
					$where .= ' and osl_obj_id_2 = '.$o[2];
				}
				$wheres[] = $where;
			}
		}
		if( sizeof( $wheres_single ) ){
			$sql .= ' and osl_obj_id_0 in ('.implode(',', $wheres_single).')';
		}else{
			$sql .= ' and (('.implode(') or (', $wheres).'))';
		}
	}else{
		$sql .= ' and osl_obj_id_0 = '.$obj[0];
		if( isset($obj[1]) ){
			$sql .= ' and osl_obj_id_1 = '.$obj[1];
		}
		if( isset($obj[2]) ){
			$sql .= ' and osl_obj_id_2 = '.$obj[2];
		}
	}

	return ria_mysql_query($sql);
}

/** Permet d'ajouter un lot sur un objet
 *
 * @param  int $cls_id Obligatoire, Identifiant de la classe
 * @param  int $obj_id_0 Obligatoire, Identifiant 1 de l'objet
 * @param  int $obj_id_1 Obligatoire, Identifiant 2 de l'objet
 * @param  int $obj_id_2 Obligatoire, Identifiant 3 de l'objet
 * @param  date $dlc Facultatif, Date de la date limite de consommation
 * @param  date $production Facultatif, Date de la production du lot
 * @param  string $serial Facultatif, Numéro du lot
 * @param  int $dps_id Facultatif, Numéro du dépot de stockage du lot
 * @param  int $qte Facultatif, Quantité concerné par le lot ( quantité dispo si c'est sur un produit, quantité prise si c'est sur une commande )
 * @param  string $ref_gescom Facultatif, Référence de la ligne dans la gestion commerciale
 *
 * @return int l'id généré pour le numéro de lot si l'ajout est ok, false autrement
 */
function obj_serials_add( $cls_id, $obj_id_0, $obj_id_1=0, $obj_id_2=0, $dlc=null, $production=null, $serial="", $dps_id=null, $qte=null, $ref_gescom=null ){
	global $config;

	if( !is_numeric($cls_id) || $cls_id <= 0){
		return false;
	}
	if( !is_numeric($obj_id_0) || $obj_id_0 <= 0){
		return false;
	}
	if( !is_numeric($obj_id_1) ){
		return false;
	}
	if( !is_numeric($obj_id_2) ){
		return false;
	}

	if( obj_serials_exists($cls_id, $obj_id_0, $obj_id_1, $obj_id_2, $serial) ){
		return obj_serials_upd($cls_id, $obj_id_0, $obj_id_1, $obj_id_2, $dlc, $production, $serial, $dps_id, $qte, $ref_gescom);
	}

	$datas = array();
	$datas['osl_tnt_id'] = $config['tnt_id'];
	$datas['osl_date_created'] = 'now()';
	$datas['osl_cls_id'] = $cls_id;
	$datas['osl_obj_id_0'] = $obj_id_0;
	$datas['osl_obj_id_1'] = $obj_id_1;
	$datas['osl_obj_id_2'] = $obj_id_2;
	$datas['osl_serial'] = "'".addslashes($serial)."'";

	if( isdateheure($dlc) ) {
		$datas['osl_date_dlc'] = "'".addslashes($dlc)."'";
	}
	if( isdateheure($production) ) {
		$datas['osl_date_production'] = "'".addslashes($production)."'";
	}
	if( is_numeric($dps_id) ){
		$datas['osl_dps_id'] = $dps_id;
	}
	if( is_numeric($qte) ){
		$datas['osl_qte'] = $qte;
	}
	if( $serial ){
		$datas['osl_ref_gescom'] = "'".addslashes($ref_gescom)."'";
	}

	$res = ria_mysql_query("
		insert into obj_serials (".implode(',',array_keys($datas)).")
		values (".implode(',', array_values($datas)).");
	");

	if( !$res ){
		return false;
	}

	$insert_id = ria_mysql_insert_id();
	fld_objects_set_date_modified($cls_id, array($obj_id_0, $obj_id_1, $obj_id_2));

	return $insert_id;
}

/** Permet de mettre à jour un lot sur un objet
 *
 * @param  int $cls_id Obligatoire, Identifiant de la classe
 * @param  int $obj_id_0 Obligatoire, Identifiant 1 de l'objet
 * @param  int $obj_id_1 Obligatoire, Identifiant 2 de l'objet
 * @param  int $obj_id_2 Obligatoire, Identifiant 3 de l'objet
 * @param  date $dlc Facultatif, Date de la date limite de consommation
 * @param  date $production Facultatif, Date de la production du lot
 * @param  string $serial Obligatoire, Numéro du lot
 * @param  int $dps_id Facultatif, Numéro du dépot de stockage du lot
 * @param  int $qte Facultatif, Quantité concerné par le lot ( quantité dispo si c'est sur un produit, quantité prise si c'est sur une commande )
 * @param  string $ref_gescom Facultatif, Référence de la ligne dans la gestion commerciale
 *
 * @return bool true si la mise à jour est ok, false autrement
 */
function obj_serials_upd($cls_id, $obj_id_0, $obj_id_1=0, $obj_id_2=0, $dlc=null, $production=null, $serial="", $dps_id=null, $qte=null, $ref_gescom=null){
	global $config;

	if( !is_numeric($cls_id) || $cls_id <= 0){
		return false;
	}
	if( !is_numeric($obj_id_0) || $obj_id_0 <= 0){
		return false;
	}
	if( !is_numeric($obj_id_1) ){
		return false;
	}
	if( !is_numeric($obj_id_2) ){
		return false;
	}

	$cnd = array(
		"osl_date_modified=now()",
	);

	if( isdateheure($dlc) ) {
		$cnd[] = "osl_date_dlc='".addslashes($dlc)."'";
	}
	if( isdateheure($production) ) {
		$cnd[] = "osl_date_production='".addslashes($production)."'";
	}
	if( is_numeric($dps_id) ){
		$cnd[] = "osl_dps_id=".$dps_id;
	}
	if( is_numeric($qte) ){
		$cnd[] = "osl_qte=".$qte;
	}
	if( $ref_gescom !== null && trim($ref_gescom) != "" ){
		$cnd[] = "osl_ref_gescom='".addslashes($ref_gescom)."'";
	}

	if( !sizeof($cnd) ){
		return false;
	}

	$sql = "
		update obj_serials set
			".implode(',',$cnd)."
		where
			osl_tnt_id=".$config['tnt_id']."
			and osl_cls_id=".$cls_id."
			and osl_obj_id_0=".$obj_id_0."
			and osl_obj_id_1=".$obj_id_1."
			and osl_obj_id_2=".$obj_id_2."
			and osl_serial='".addslashes($serial)."'
	";
	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	fld_objects_set_date_modified($cls_id, array($obj_id_0, $obj_id_1, $obj_id_2));

	return true;
}

/** Permet de supprimer un lot sur un objet
 *
 * @param  int $cls_id Obligatoire, Identifiant de la classe
 * @param  int $obj_id_0 Obligatoire, Identifiant 1 de l'objet
 * @param  int $obj_id_1 Obligatoire, Identifiant 2 de l'objet
 * @param  int $obj_id_2 Obligatoire, Identifiant 3 de l'objet
 * @param  string $serial Obligatoire, Numéro du lot
 *
 * @return bool true si la suppression est effective, false autrement
 */
function obj_serials_del($cls_id, $obj_id_0, $obj_id_1=0, $obj_id_2=0, $serial=""){
	global $config;

	if( !is_numeric($cls_id) || $cls_id <= 0){
		return false;
	}
	if( !is_numeric($obj_id_0) || $obj_id_0 <= 0){
		return false;
	}
	if( !is_numeric($obj_id_1) ){
		return false;
	}
	if( !is_numeric($obj_id_2) ){
		return false;
	}
	if( !obj_serials_exists($cls_id, $obj_id_0, $obj_id_1, $obj_id_2, $serial) ){
		return true;
	}

	$sql = "
		delete from obj_serials where
			osl_tnt_id=".$config['tnt_id']."
			and osl_cls_id=".$cls_id."
			and osl_obj_id_0=".$obj_id_0."
			and osl_obj_id_1=".$obj_id_1."
			and osl_obj_id_2=".$obj_id_2."
			and osl_serial='".addslashes($serial)."'
	";
	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	fld_objects_set_date_modified($cls_id, array($obj_id_0, $obj_id_1, $obj_id_2));

	return true;
}

/// @}