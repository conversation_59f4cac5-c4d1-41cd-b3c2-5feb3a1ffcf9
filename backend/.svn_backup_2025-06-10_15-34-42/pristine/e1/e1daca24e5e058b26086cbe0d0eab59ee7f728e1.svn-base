<?php
	// MyPDF_VerticalPanel
	/*
	*/
	
	require_once('mypdf/MyPDF_Element.php');
	require_once('mypdf/MyPDF_GridPanel.php');
	
	class MyPDF_VerticalPanel extends MyPDF_Element {
	
		// attributs
		
			private	$_panel;
		
		// méthodes
		
			// __construct
			/* Constructeur */
			public function __construct($param = array()) {
				parent::__construct($param);
				
				$rows = (array_key_exists('rows', $param)) ? $param['rows'] : 0;
				$this->_panel = new MyPDF_GridPanel(array('parent' => $this, 'rows' => $rows, 'cols' => 1));
			}
			
			// add
			/* Ajoute un élément */
			public function add($param) {
				$this->addRow();
				$this->getCell(array('row' => $this->getRows()-1))->setWidget(array('widget' => $param['element']));
				return $this;
			}
			
			// addRow
			/* Ajoute une ligne */
			public function addRow() {
				$this->getPanel()->addRow();
				return $this;
			}
			
			// build
			/* Build */
			public function build($param = array()) {
				$this->getPanel()->build($param);
				return $this;
			}
			
			// getCell
			/* Renvoie une cellule */
			public function getCell($param) {
				return $this->getPanel()->getCell(array('row' => $param['row'], 'col' => 0));
			}
			
			// getCols
			/* Renvoie le nombre de colonnes */
			public function getCols() {
				return 1;
			}
			
			// getHeight
			/* Renvoie height */
			public function getHeight() {
				return $this->getPanel()->getHeight();
			}
			
			// getLeft
			/* Renvoie left */
			public function getLeft() {
				return $this->getParent()->getLeft();
			}
			
			// getPanel
			/* Renvoie le panel */
			public function getPanel() {
				return $this->_panel;
			}
			
			// getRows
			/* Renvoie le nombre de lignes */
			public function getRows() {
				return $this->getPanel()->getRows();
			}
			
			// getTop
			/* Renvoie top */
			public function getTop() {
				return $this->getParent()->getTop();
			}
			
			// getWidth
			/* Renvoie width */
			public function getWidth() {
				return $this->getPanel()->getWidth();
			}
		
	}

