<?php
/**	\file import.php
 * 	Cette page est utilisée pour enregistrer un nouvel import de données.
 */

// Vérifie que l'utilisateur en cours à le droit d'importer des données
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT_ADD');

require_once('imports.inc.php');

$result = false;
$errors = array();
$content = null;

if( !isset( $_POST['imp-method'] ) || trim( $_POST['imp-method'] ) == '' ){
	$errors[] = _('Aucune méthode d\'import n\'a été saisie.');
}else{
	$url = $login = $password = $period = $period_value = null;
	$is_ftp = false;
	$filename = $name = '';

	// Contrôles de saisie spécifiques à la méthode de saisie
	switch( $_POST['imp-method'] ){
		case 'file': // Téléchargement d'un fichier CSV depuis le back-office
			if( !isset( $_FILES['imp-file'] ) || $_FILES['imp-file']['size'] == 0 || $_FILES['imp-file']['tmp_name'] == '' || $_FILES['imp-file']['error'] == 4 ){
				$errors['all'][] = _('Vous devez sélectionner un fichier.');
			}else{
				$ext = strtolower( pathinfo( $_FILES['imp-file']['name'], PATHINFO_EXTENSION ) );

				if( !in_array( $ext, ipt_imports_get_allowed_extensions() ) ){
					$errors[] = _('Votre fichier doit être au format XLS, XLSX ou CSV.');
				}
			}
			$filename = $_FILES['imp-file']['tmp_name'];
			$name = $_FILES['imp-file']['name'];
			break;
		case 'ftp': // Téléchargement automatique du fichier CSV depuis un serveur FTP
			if( !isset( $_POST['imp-file'] ) || trim( $_POST['imp-file'] ) == '' ){
				$errors[] = _('Aucun nom de fichier n\'a été saisi');
			}else{
				$filename = $_POST['imp-file'];
				$name = $_POST['imp-file'];
				$ext = strtolower( pathinfo( $filename, PATHINFO_EXTENSION ) );

				if( !in_array( $ext, ipt_imports_get_allowed_extensions() ) ){
					$errors[] = _('Votre fichier doit être au format XLS, XLSX ou CSV.');
				}
			}
			if( !isset( $_POST['imp-url'] ) || trim( $_POST['imp-url'] ) == '' ){
				$errors[] = _('Aucune url n\'a été saisie');
			}else{
				$url = $_POST['imp-url'];
			}
			if( !isset( $_POST['imp-login'] ) || trim( $_POST['imp-login'] ) == '' ){
				$errors[] = _('Aucun login n\'a été saisi');
			}else{
				$login = $_POST['imp-login'];
			}
			if( !isset( $_POST['imp-password'] ) || trim( $_POST['imp-password'] ) == '' ){
				$errors[] = _('Aucun mot de passe n\'a été saisi');
			}else{
				$password = $_POST['imp-password'];
			}
			$is_ftp = true;
			break;
		case 'url': // Téléchargement automatique du fichier CSV depuis une URL
			if( !isset( $_POST['imp-url'] ) || trim( $_POST['imp-url'] ) == '' ){
				$errors[] = _('Aucune url n\'a été saisie');
			}else{
				$url = $_POST['imp-url'];
				$filename = $url;
				$ext = strtolower( pathinfo( $filename, PATHINFO_EXTENSION ) );
				$name = pathinfo( $filename, PATHINFO_FILENAME ).'.'.$ext;

				if( !in_array( $ext, ipt_imports_get_allowed_extensions() ) ){
					$errors[] = _('Votre fichier doit être au format XLS, XLSX ou CSV.');
				}
			}
			break;
	}

	// Contrôles de saisie liés à la récurrence d'importation
	if( $_POST['imp-method'] == 'ftp' || $_POST['imp-method'] == 'url' ){
		if( isset( $_POST['imp-auto'] ) && $_POST['imp-auto'] == '1' ){
			if( !isset( $_POST['imp-period'] ) || trim( $_POST['imp-period'] ) == '' || !in_array( $_POST['imp-period'], array_keys(ipt_import_get_recurence()) ) ){
				$errors[] = _('Aucune récurrence pour l\'import');
			}else{
				if( !isset( $_POST['imp-period-values'] ) ){
					$errors[] = _('Aucune valeur pour la période de récurrence');
				}else{
					$period = $_POST['imp-period'];
					$period_value = $_POST['imp-period-values'];
				}
			}
		}
	}
}

// Autres contrôles de saisie
if( !isset( $_POST['imp-class'] ) || !fld_classes_exists( $_POST['imp-class'] ) ){
	$errors[] = _('Merci de sélectionner le contenu de votre import.');
}
if( !isset( $_POST['imp-separator'] ) ){
	$errors[] = _('Vous devez sélectionner un séparateur.');
}elseif( $_POST['imp-separator'] == 'other' && ( !isset( $_POST['imp-separator-other-text'] ) || is_numeric( $_POST['imp-separator-other-text'] ) || $_POST['imp-separator-other-text'] === '' ) ){
	$errors[] = _('Vous devez indiquer le séparateur utilisé.');
}
if( !isset( $_POST['imp-action'] ) || !in_array( $_POST['imp-action'], ipt_imports_get_import_actions() ) ){
	$errors[] = _('Vous devez séléctionner un type d\'action.');
}

// Si aucune erreur de saisie, test de l'import
if( empty( $errors ) ){
	try{
		$file = new ImportFile( $filename, $_POST['imp-method'], $name );
		if( $is_ftp ){
			$file->connectToFtp($url, $login, $password );
		}
		$file->saveFile();

		// Pour les fichiers CSV, le délimiteur est détecté automatiquement
		// Pour les fichiers XLS et XLSX, le délimiteur est toujours la virgule
		if( $ext=='csv' ){
			$col_sep = $file->detectCsvColDelimiter();
		}else{ 
			$col_sep = ',';
		}

		if( strlen($col_sep) > 1){
			$errors[] = _('Le séparateur de colonne doit être d\'un caractère.');
		}

		if( strlen($_POST['imp-text-separator']) > 1 ){
			$errors[] = _('Le délimiteur de texte doit être d\'un caractère.');
		}

		if(empty($errors)){
			$file->readFile( $col_sep, $_POST['imp-text-separator'] );

			if( !$file->checkColsAndLines() ){
				$errors[] = _('Votre fichier ne contient aucune lignes ou aucune colonnes');
			}
		}

		if( !empty($errors) ){
			unlink($file->localFilePath);
		}

	}
	catch(Exception $e){
		$errors[] = $e->getMessage();
	}
}

// changer l'état suivant le nombre de lignes
if( empty( $errors ) ){
	$is_system = false;
	if( $config['USER_RIASTUDIO'] && isset($_POST['is_system']) ){
		$is_system = true;
	}
	$is_sync = false;
	if( $config['USER_RIASTUDIO'] && isset($_POST['is_sync']) ){
		$is_sync = true;
	}
	$info = false;
	if( isset($_POST['info']) ){
		if( $_POST['info'] == 'model' ){
			$info = json_encode( array( 'sub_class' => 'model' ) );
		}elseif( $_POST['info'] == 'user_discount' ){
			$info = json_encode( array( 'sub_class' => 'user_discount' ) );
		}
	}
	$state = 'create';
	if( !$imp_id = ipt_imports_add(
		$ext,
		$file->original_filename,
		$file->lines_count,
		$file->getColumnsCount(),
		$_SESSION['usr_id'],
		$_POST['imp-class'],
		$state,
		$_POST['imp-action'],
		$file->getFilePath(),
		$file->getCharset(),
		$col_sep,
		$_POST['imp-text-separator'],
		$file->getSize(),
		$url,
		$login,
		$password,
		$period,
		$period_value,
		0,
		$is_system,
		$is_sync,
		$info
	)
	){
		unlink( $file->localFilePath );
		$errors[] = _('Une erreur s\'est produite lors de la création de votre import');
	}else{
		if( $_POST['imp-method'] == 'ftp' || $_POST['imp-method'] == 'url' ){
			$file->deleteFile();
		}

		if( empty( $errors ) ){
			$result = true;
			$content = array(
				'redirect' => '/admin/tools/imports/mapping.php',
				'params' => array('imp' => $imp_id)
			);
		}
	}
}

if( $content===null && sizeof($errors) ){
	$content = $errors;
}

// Envoie la réponse au navigateur
header('Content-Type: application/json');
echo json_encode(array(
	'result' => $result,
	'time' => date('Y-m-d H:i:s'),
	'message' => '',
	'content' => $content,
));