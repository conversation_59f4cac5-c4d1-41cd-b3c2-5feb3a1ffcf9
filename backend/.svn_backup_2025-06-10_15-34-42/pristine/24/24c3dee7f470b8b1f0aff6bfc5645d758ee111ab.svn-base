<?php
	require_once('orders.inc.php');

	/**
	 *@backupGlobals disabled
	*/
	class ordersDelProductTest extends PHPUnit_Framework_TestCase {

        /** Cette fonction permet de tester la suppression de produit dans une commande
         */
        public function testOrdDelProducts(){

            // Test la suppression d'un seul produit
            $this->assertTrue(ord_products_del( 2, 1), 'Erreur: le produit n\'a pas été supprimé de la commande');
          
            $rord = ord_products_get(2);
            $this->assertTrue($rord && ria_mysql_num_rows($rord) == 2, 'Erreur lors de la vérification de la suppression du produit de la commande');
       
            while($ord = ria_mysql_fetch_assoc($rord)){
                $this->assertTrue($ord['id'] != 1 , 'Erreur: le produit n\'a pas été supprimé de la commande');
            }

            $this->assertEquals(720, ord_orders_get_total_without_port(2, true, true), 'Erreur: le montant de la commande n\'a pas été mis à jour lors de la suppression d\'un produit');

            // Test la suppression de tout les produits restant
            $this->assertTrue(ord_products_del(2), 'Erreur: les produits n\'ont pas été supprimé de la commande');
            $rord = ord_products_get(2);
            $this->assertTrue($rord || ria_mysql_num_rows($rord), 'Erreur lors de la vérification de la suppression des produits de la commande');
            $this->assertTrue(ria_mysql_num_rows($rord) == 0, 'Erreur: les produits n\'ont pas été supprimés');

            $this->assertEquals(0,ord_orders_get_total_without_port(2, true, true), 'Erreur: le montant de la commande n\'a pas été mis à jour lors de la suppression d\'un produit');
        }
		
	}
