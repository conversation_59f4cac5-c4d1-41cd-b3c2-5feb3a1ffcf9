<?php 
/** \file ajax-import-help.php
 *  Cette page est utilisée pour récupérer les informations d'aide à afficher sur la page imports/index.php
 */

// Cette page doit obligatoirement être appelée en Ajax
if( !IS_AJAX ){
	header('HTTP/1.0 403 Forbidden');
	exit;
}

// Vérifie que l'utilisateur en cours à le droit d'importer des données
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT');

if (!isset($_GET['imp_type']) || !is_numeric($_GET['imp_type'])) {
	exit;
}
require_once('view.imports.inc.php');

$type = $_GET['imp_type'];

// on vérifie qu'on ne demande pas modèle de commande ou remise client
if (isset($_GET['imp_data']) && $_GET['imp_data'] !== '') {
	$type = $_GET['imp_type'].'-'.$_GET['imp_data'];
}

$response             = array();
$response['download'] = ria_view_example_import_file($type);
$response['help']     = ria_view_help_import_file($type);

header('Content-Type: application/json');
echo json_encode(array(
	'success' => true,
	'content' => $response
));
exit;