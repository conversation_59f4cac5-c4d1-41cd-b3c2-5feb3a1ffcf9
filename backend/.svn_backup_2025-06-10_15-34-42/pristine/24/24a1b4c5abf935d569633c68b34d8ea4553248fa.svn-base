<?php

	/** \file refresh-products-publish-parent.php
	 * 	Ce script est destiné à mettre à jour l'information de publication d'un produit parent en fonction de ses articles enfants
	 *
	 *  Le paramètre incatroot peut être passer en supplément permettant de considérer que les articles enfants ne sont pas forcément classé
	 *  dans la catégorie principale ($config['cat_root']). Pour cela il faut que incatroot soit égal à "no" (par défaut à "yes").
	 *  Ex. php execute-script.php --tnt_id [TNT_ID] --script refresh-products-publish-parent --other="incatroot=no"
	 */

	if (!isset($ar_params)) {
		print "L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL;
		exit;
	}

	require_once('products.inc.php');

	$ar_unique = [];

	foreach( $configs as $config ){
		$cat_root = $config['cat_root'];
		if( isset($ar_params['incatroot']) && $ar_params['incatroot'] == 'no' ){
			$cat_root = 0;
		}

		// Récupéré les produits parents
		$r_parent = prd_hierarchy_get();
		if( $r_parent ){
			while( $parent = ria_mysql_fetch_assoc($r_parent) ){
				// Le calcul n'est fait qu'une fois par article parent
				if( in_array($parent['parent'], $ar_unique) ){
					continue;
				}

				// Si le parent n'est pas publié dans la catégorie principale on ne fait rien de plus
				$r_prd = prd_products_get_simple( $parent['parent'], '', true, $config['cat_root'], true );
				if( !$r_prd || !ria_mysql_num_rows($r_prd) ){
					continue;
				}

				// Récupère les articles enfants publiés
				$r_child = prd_products_get_simple( 0, '', true, $cat_root, true, false, false, false, ['parent' => $parent['parent'], 'childs' => true] );
				if( $r_child ){
					// Si aucun article enfant publié alors le parent est dépublié
					if( ria_mysql_num_rows($r_child) == 0 ){
						prd_products_unpublish( $parent['parent'] );
					}
				}

				$ar_unique[] = $parent['parent'];
			}
		}
	}
