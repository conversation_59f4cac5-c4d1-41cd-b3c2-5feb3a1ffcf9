<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagBase;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au Tag Thing dont les autres tag peuvent hériter
 */
class TagThing extends TagBase {
	/**
	 * Le type de tag
	 *
	 * @var string $type
	 */
	protected $type = "Thing";

	/**
	 * Cette fonction permet d'initialisé le champ name de l'organisation
	 *
	 * @param string $name le nom de l'organisation
	 * @return self retourne l'instance
	 */
	public function setName($name){
		return $this->addField('name', $name);
	}

	/**
	 * Ajoute la description
	 *
	 * @param string $desc description de l'objet
	 * @return self retourne l'instance
	 */
	public function setDescription($desc){
		return $this->addField('description', strip_tags($desc));
	}

	/**
	 * Cette fonction permet d'initialisé le champ url de l'organisation
	 *
	 * @param string $url l'url de l'organisation
	 * @return self retourne l'instance
	 */
	public function setUrl($url){
		return $this->addField('url', $url);
	}

	/**
	 * Cette fonction permet d'initialisé le champ sameAs de l'organisation
	 *
	 * @param array $sameAs Tableau de lien en relation a l'organisation
	 * @return self retourne l'instance
	 */
	public function setSameAs(array $sameAs){
		return $this->addField('sameAs', $sameAs);
	}

	/**
	 * Permet d'ajouter les images
	 *
	 * @param string $url Url de l'image
	 * @return void
	 */
	public function addImage($url){
		$this->fields['image'][] = $url;

		return $this;
	}

}