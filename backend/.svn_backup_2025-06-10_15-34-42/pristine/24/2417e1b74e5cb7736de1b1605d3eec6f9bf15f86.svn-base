<?php
namespace RGPD\Trackers;

use RGPD\Trackers\GoogleAnalytics;
/** \ingroup TrackerGoogle
 * @{
 */
/**
 * \class UniversalAnalytics
 * \brief UniversalAnalytics gère l'initialisation de analytics.js
 * Initialisation
 * \code{.php}
 * 		use RGPD\Trackers\UniversalAnalytics;
 *		require_once 'RGPD/autoload.php';
 *		$UniversalAnalytics = new UniversalAnalytics('UA-30403140-1');
 * \endcode
 * Ajout d'une transaction
 *	\code{.php}
 *		$UniversalAnalytics->addTransaction($order, '[nom de l'affiliation]');
 *	\endcode
 * Ajout d'un évennement
 *	\code{.php}
 *		$UniversalAnalytics->addEvent('newsletter', 'inscript-news', 'Inscription newsletter');
 *	\endcode
 * Ajout d'une vue de page
 *	\code{.php}
 *		$UniversalAnalytics->addPageView();
 *	\endcode
 * Ajout de code personalisé
 *	\code{.php}
 *		$UniversalAnalytics->withCustomCode(function(){
 *	 		// evvent plus complex
 *			echo 'ga("send", "event", {
 *				"action": "mon-action"
 *			});';
 *		});
 *	\endcode
 */
class UniversalAnalytics extends GoogleAnalytics
{
	private $events = array(); ///< array Liste des évènements ga('send', 'event', ...); a générer

	private $page_view = ''; ///< string Code ga('send', 'pageview'[, page]);

	private $transaction_code = ''; ///< string Code js de la transaction en court avec la commande et les produits

	/** Cette fonction permet l'ajout un tag ga('send', 'pageview');
	 *
	 * \param string $page Facultatif, nom de la page
	 * \return UniversalAnalytics L'instance
	 */
	public function addPageView($page=null)
	{
		if (is_null($page) || trim($page) == '') {
			$this->page_view = "ga('send', 'pageview');";
		}else{
			$this->page_view = "ga('send', 'pageview', '".htmlspecialchars($page)."');";
		}

		return $this;
	}

	/** Cette fonction permet addTransaction Génère le code de transaction
	 * ga('require', 'ecommerce');
	 * ga('ecommerce:addTransaction', { ... })
	 *
	 * \param array $order Commande retourné par ord_orders_get ou ord_orders_get_with_addresses
	 * \param string $affiliation L'affiliation analytics
	 * \return UniversalAnalytics L'instance
	 */
	public function addTransaction(array $order, $affiliation)
	{
		$products = ord_products_get($order['id']);
		ob_start();
		?>
			ga('require', 'ecommerce');
			ga('ecommerce:addTransaction', {
				'id': '<?php print $order['id']; ?>',																// Transaction ID. Required.
				'affiliation': '<?php echo htmlspecialchars($affiliation);?>',										// Affiliation or store name.
				'revenue': '<?php print number_format($order['total_ttc'],2,".",""); ?>',							// Grand Total.
				'shipping': '<?php print number_format( ord_orders_port_get($order['id'], true), 2, '.', '') ?>',	// Shipping.
				'tax': '<?php print number_format($order['total_ttc'] - $order['total_ht'],2,".",""); ?>'			// Tax.
			});
		<?php
		while ($prd = ria_mysql_fetch_assoc($products)) {
			$prd_info = ria_mysql_fetch_assoc(prd_products_get($prd['id']));
			$cat = ria_mysql_fetch_assoc(prd_products_categories_get( $prd['id'], true ));
			?>
				ga('ecommerce:addItem', {
					'id': '<?php echo $order['id']; ?>',									// Transaction ID. Required.
					'name': <?php echo json_encode($prd_info['name']); ?>,					// Product name. Required.
					'sku': <?php echo json_encode($prd_info['ref']); ?>,					// SKU/code.
					'category': <?php echo json_encode($cat['name']); ?>,					// Category or variation.
					'price': '<?php echo number_format($prd['price_ht'], 2, '.', ''); ?>',	// Unit price.
					'quantity': '<?php echo $prd['qte']; ?>'								// Quantity.
				});
			<?php
		}
		?>
			ga('ecommerce:send');
			ga('send', 'pageview', '/commande-send.html');
		<?php

		$this->transaction_code = ob_get_clean();

		return $this;
	}
	/** Cette fonction permet addEvent Ajoute un évènement ga('send', 'event', ...);
	 *
	 * \param mixed $eventCategory Typically the object that was interacted with (e.g. 'Video')
	 * \param mixed $eventAction The type of interaction (e.g. 'play')
	 * \param mixed $eventLabel Optional, Useful for categorizing events (e.g. 'Fall Campaign')
	 * \param mixed $eventValue Optional, A numeric value associated with the event (e.g. 42)
	 * \return UniversalAnalytics L'instance
	 */
	public function addEvent($eventCategory, $eventAction, $eventLabel=null, $eventValue=null)
	{
		$this->events[] = array(
			'Category' => $eventCategory,
			'Action' => $eventAction,
			'Label' => $eventLabel,
			'Value' => $eventValue
		);

		return $this;
	}
	/** Cette fonction permet getEvents Retourne la liste des évènements
	 *
	 * \return array Liste des évènements
	 */
	public function getEvents()
	{
		return $this->events;
	}
	/** Cette fonction permet renderTarteaucitronCode
	 *
	 * \param mixed $with_script_tag Retourne le js avec ou sans script tag
	 * \return string Retourne le js d'initialisation du module
	 */
	public function renderTarteaucitronCode($with_script_tag=true)
	{
		$tag = '
			'.($with_script_tag ? '<script>' : '').'
			tarteaucitron.user.analyticsUa = "'.htmlspecialchars($this->UA_UID).'";
			';
		$tag .= 'tarteaucitron.user.analyticsMore = function () {';
		// ajout pageview
		$tag .= $this->page_view;
		// ajout des évènements
		$tag .= $this->formatEvents();
		// ajout code de transaction
		$tag .= $this->transaction_code;
		/* add here your optionnal ga.push() */
		if (is_callable($this->custom_code)) {
			ob_start();
			$this->custom_code->__invoke();
			$tag .= ob_get_clean();
		}
		$tag .= '};';
		$tag .= '
			(tarteaucitron.job = tarteaucitron.job || []).push("analytics");
			'.($with_script_tag ? '</script>' : '').'
		';
		return $tag;
	}
	/** Cette fonction permet formatEvents Permet de générer le code js pour les evènements au format :
	 * ga('send', 'event', [eventCategory], [eventAction], [eventLabel], [eventValue]);\n
	 *
	 * \return string Retourne une chaine vide ou les évènement ga('send', 'event', ....);
	 */
	private function formatEvents()
	{
		if (empty($this->events)) {
			return '';
		}
		$events = '';
		foreach($this->events as $event) {
			$events .= "ga('send', 'event', '".htmlspecialchars($event['Category'])."', '".htmlspecialchars($event['Action'])."'";
				if (!is_null($event['Label'])) {
					$events .= ", '".htmlspecialchars($event['Label'])."'";
				}
				if (!is_null($event['Value'])) {
					$events .= ", '".htmlspecialchars($event['Value'])."'";
				}
			$events .= ");".PHP_EOL;
		}

		return $events;
	}
}
/// @}