<?php
	/** \file send-alert-owner.php
	 *  Ce script permet de renvoyer la notification "Nouvelle commande internet" au propriétaire
	 *  Ce script prend en paramètre 2 info obligatoire (date de début et date de fin)
	 */

	set_include_path(dirname(__FILE__) . '/../include/');
	require_once('orders.inc.php');

	if( !RegisterGCP::onGcloud() ){
		error_log('Ce script n\'est accessible que pour l\'environnement GCP.');
		exit;
	}

	// Initialise les variables de période (paramètres obligatoire)
	$date_start = isset($argv[1]) && isdateheure($argv[1]) ? dateheureparse($argv[1]) : '';
	$date_end   = isset($argv[2]) && isdateheure($argv[2]) ? dateheureparse($argv[2]) : '';

	if( !trim($date_start) || !trim($date_end) ){
		error_log('Les paramètres de date de début et de fin sont obligatoire.');
		exit;
	}elseif( strtotime($date_start) > strtotime($date_end) ){
		error_log('La date de début ne peut être postérieure à la date de fin.');
		exit;
	}
	
	// Charge les différentes connexions possibles aux SQL sur la GCP
	$ar_connections = RegisterGCP::create()->getConnections();
	foreach( $ar_connections as $connection ){
		// Etabli la connexion MySQL
		RegisterGCPConnection::connect($connection);

		// Chargement des variables de configurations de tous les tenants se trouvant sur ce serveur
		$configs = cfg_variables_get_all_tenants();

		// Pour chaque tenant, on récupère les commandes à notifier
		foreach($configs as $one_config){
			$GLOBALS['config'] = $one_config;

			// Chargement des commande selon la période saisie et on récupère en complément le numéro de pièce
			$r_order = ord_orders_get_simple(
				array(),
				array(
					'start' => $date_start,
					'end' => $date_end,
				),
				array(),
				array(),
				array(
					'type'=>'complete', 
					'columns' => array('ord_piece'=>'piece'))
			);

			if( !$r_order || !ria_mysql_num_rows($r_order) ){
				break;
			}
			
			while( $order = ria_mysql_fetch_assoc($r_order) ){
				// Si la commande n'est pas synchronisé, le mail ne sera pas envoyé
				if(!trim($order['piece'])){
					continue;
				}

				// Si aucun moyen de paiement alors le mail ne sera pas envoyé
				// Il sagit dans ce cas d'une commande provenant de la gestion commerciale
				if( !is_numeric($order['pay_id']) || $order['pay_id'] <= 0 ){
					continue;
				}
				
				// Envoi du mail "Nouvelle commande internet
				ord_alert_shop_owner($order['id']);
			}
		}
	}
