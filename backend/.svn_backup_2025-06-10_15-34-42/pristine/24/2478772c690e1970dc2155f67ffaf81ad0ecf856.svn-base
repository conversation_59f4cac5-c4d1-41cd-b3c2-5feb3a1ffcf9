<?php
require_once('delivery.inc.php');

// Vérifie que l'utilisateur en cours peut accéder à cette page
if( isset($_GET['job']) ){
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE_JOB_EDIT');
}else{ // !isset($_GET['job'])
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE_JOB_ADD');
}

$errors = array();
do{
	// Bouton Enregistrer
	if( isset($_POST['save-main']) ){
		if( !is_string($_POST['name']) || trim($_POST['name']) =='' ){
			$errors[] = 'Un nom doit être renseigné.';
		}
		$name = htmlspecialchars($_POST['name']);
		$desc = htmlspecialchars($_POST['desc']);
		
		if( count($errors) ){
			break;
		}
		
		if( isset($_GET['job']) ){
			if( !dlv_store_jobs_update($_GET['job'], $name, $desc ) ){
				$errors[] = _('Une erreur est survenue lors de l\'enregistrement, veuillez réessayer ou contacter l\'administrateur.');
				break;
			}
			header('location: index.php?success=true');
			exit;
		}
		
		if( !dlv_store_jobs_add($name, $desc) ){
			$errors[] = _('Une erreur est survenue lors de l\'enregistrement, veuillez réessayer ou contacter l\'administrateur.');
			break;
		}
		header('location: index.php?success=true');
		exit;
	}
	// Bouton Supprimer
	if( isset($_POST['del-main'], $_GET['job']) ){
		if(!dlv_store_jobs_del($_GET['job'])){
			$errors[] = _('Une erreur est survenue lors de la suppression d\'un poste de travail.');
			break;
		}
		header('location: index.php?success=del');
		exit;
	}
	// Bouton Annuler
	if( isset($_POST['cancel-main']) ){
		header('location: index.php');
		exit;
	}
	
	// Chargement
	if( isset($_POST['name'])){
		$data = $_POST;
	}elseif( isset($_GET['job']) ){
		$res = dlv_store_jobs_get($_GET['job']);
		if( !$res ){
			header('location: index.php');
			exit;
		}
		
		$data = ria_mysql_fetch_assoc($res);
	}else{
		$data = array(
			'name' => '',
			'desc' => ''
		);
	}
}while(0);

// Défini le titre de la page
$page_title = $data['name'] ? $data['name'] : 'Nouveau poste';
define('ADMIN_PAGE_TITLE', $page_title.' - '._('Magasins') . ' - ' ._('Livraison des commandes') . ' - ' ._('poste'));
require_once('admin/skin/header.inc.php');
?>
	<h2><?php print htmlspecialchars( $page_title ); ?></h2>
	<?php
		if( count($errors) ){
			echo '<div class="notice error">';
			foreach($errors as $error){
				echo '<p>'.$error.'</p>';
			}
			echo '</div>';
		}
	?>
	<form action="edit.php<?php echo (isset($_GET['job']) ? '?job='.$_GET['job'] : '')?>" method="post">
		<table id="table-poste-edit">
			<tfoot>
			<tr>
				<td class="tdleft">
					<?php if(isset($_GET['job']) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_JOB_DEL') ){?><input type="submit" name="del-main" value="<?php echo _("Supprimer"); ?>"> <?php } ?>
				</td>
				<td>
					<input type="submit" name="save-main" value="<?php echo (isset($_GET['job']) ? _('Enregistrer'): _('Ajouter')) ?>">
					<input type="submit" name="cancel-main" value="<?php echo _("Annuler"); ?>">					
				</td>
			</tr>
			</tfoot>
			<tbody>
			<tr>
				<td><label for="name"><?php echo _('Nom :'); ?></label></td>
				<td><input type="text" id="name" name="name" value="<?php echo (isset($data['name']) ? htmlspecialchars($data['name']) : '')?>"></td>
			</tr>
			<tr>
				<td><label for="desc"><?php echo _('Description du poste :'); ?></label></td>
				<td><textarea name="desc" id="desc" cols="50" rows="10"><?php echo (isset($data['desc']) ? htmlspecialchars($data['desc']) : '')?></textarea></td>
			</tr>
			</tbody>
		</table>
	</form>
	

<?php
require_once('admin/skin/footer.inc.php');
?>