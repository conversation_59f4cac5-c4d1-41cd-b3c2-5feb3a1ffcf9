<?php

	/**	\file index.php
	 * 
	 * 	Cette page affiche la liste des champs personnalisés globaux et spécifiques à l'installation. Elle propose
	 * 	plusieurs filtres permettrant de trouver plus facilement ce que l'on cherche :
	 * 	- par classe
	 * 	- par type de champ
	 * 	- par modèle de saisie
	 * 	- par catégorie de champs
	 * 
	 */

	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_FIELD');

	unset($error);

	if( !isset($_REQUEST['cat']) || !is_numeric($_REQUEST['cat']) ){
		$_REQUEST['cat'] = 0;
	}
	if( !isset($_REQUEST['type']) || !is_numeric($_REQUEST['type']) ){
		$_REQUEST['type'] = 0;
	}
	if( !isset($_GET['mdl']) || !is_numeric($_GET['mdl']) ){
		$_GET['mdl'] = 0;
	}
	if( !isset($_GET['cls']) || !is_numeric($_GET['cls']) ){
		$_GET['cls'] = 0;
	}

	// Déplacement vers le haut
	if (isset($_GET['up'])) {
		if (! fld_classes_fields_update_position( $_GET['cls'], $_GET['up'], 'up' )) $error = _(" Une erreur inattendue s'est produite lors du déplacement du contenu. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");

		if (! isset($error)) {
			header('Location: /admin/config/fields/fields/index.php'.(isset($_GET['cls']) ? '?cls='.$_GET['cls'] : ''));
			exit;
		}
	}

	// Déplacement vers le bas
	if (isset($_GET['down'])) {
		if (!fld_classes_fields_update_position( $_GET['cls'], $_GET['down'], 'down' )) $error = _(" Une erreur inattendue s'est produite lors du déplacement du contenu. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		if (! isset($error)) {
			header('Location: /admin/config/fields/fields/index.php'.(isset($_GET['cls']) ? '?cls='.$_GET['cls'] : ''));
			exit;
		}
	}

	$url_action = '/admin/config/fields/fields/index.php';

	$selectclass = _('Toutes les classes');
	if( isset($_GET['cls']) && $_GET['cls']>0 ){
		$tmp = fld_classes_get_name( $_GET['cls'] );
		if( trim($tmp)!='' ){
			$selectclass = $tmp;
		}

		$url_action .= (strstr($url_action, '?') ? '&' : '?').'cls='.$_GET['cls'];
	}

	$selecttypes = _('Tous les types');
	if( isset($_GET['type']) && $_GET['type']>0 ){
		$rtype = fld_types_get( $_GET['type'] );
		if( $rtype && ria_mysql_num_rows($rtype) ){
		    $type = ria_mysql_fetch_array( $rtype );
		    $selecttypes = htmlspecialchars( $type['name'] );
		}

		$url_action .= (strstr($url_action, '?') ? '&' : '?').'type='.$_GET['type'];
	}

	$selectcategories = _('Toutes les catégories');
	if( isset($_GET['cat']) && $_GET['cat']>0 ){
		$rcat = fld_categories_get( $_GET['cat'] );
		if( $rcat && ria_mysql_num_rows($rcat) ){
			$cat = ria_mysql_fetch_array( $rcat );
			$selectcategories = htmlspecialchars( fld_classes_get_name( $cat['cls_id'] ).' >> '.$cat['name'] );
		}

		$url_action .= (strstr($url_action, '?') ? '&' : '?').'cat='.$_GET['cat'];
	}

	// Suppression dans la liste
	if( isset($_POST['del']) && isset($_POST['fld']) ){
		foreach( $_POST['fld'] as $z ){
			if( !fld_fields_get_is_sync($z) && fld_fields_is_tenant_linked($z) ){
				if( !fld_fields_del($z) ){
					$count = fld_fields_is_used($z);
					if( $count>0 ){
						$error = str_replace("#param[nom_champs]#", fld_fields_get_name($z), str_replace("#param[nb_elemnt]#", $count, _("Le champ #param[nom_champs]# est utilisé par #param[nb_elemnt]# élément(s).\nVeuillez supprimer les valeurs avant de supprimer le champ."))) ;
					}else{
						$error = _("Une erreur inattendue s'est produite lors de la suppression d'un des champs.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
					}
					break;
				}
			}
		}
		if( !isset($error) ){ // Redirige la requête en conservant les filtres
			header('Location: '.$url_action);
			exit;
		}
	}

	// Vider les contenus d'une ou plusieurs unitées de mesure
	if( isset($_POST['clear'], $_POST['fld']) ){
		$size = sizeof($_POST['fld']);
		foreach( $_POST['fld'] as $z ){
			if( !fld_fields_get_is_sync($z) ){
				if( !fld_object_values_clear($z) )
					$error = "Une erreur inattendu s'est produite lors du retrait de tous les contenu ".( $size>1 ? "d'un des champs avancés sélectionnés" : "du champ avancé sélectionné" ).". \n Veuillez réessayer ou prendre contact avec l'administrateur.";
			}
		}
		if( !isset($error) ){ // Redirige la requête en conservant les filtres
			header('Location: '.$url_action);
			exit;
		}
	}

	// Ajout d'un nouveau champ avancé
	if( isset($_POST['add']) ){
		header('Location: /admin/config/fields/fields/edit.php?fld=0&cls='.$_POST['cls_id'].'&type='.$_POST['type_id'].'&cat='.$_POST['cat_id']);
		exit;
	}

	$have_fields = false;
	$fld_classes = isset($_GET['cls']) && is_numeric($_GET['cls']) ? $_GET['cls'] : 0;
	$sort = $fld_classes ? array('fld-pos' => 'asc') : array();

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Champs personnalisés') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	$fields = fld_fields_get( 0, $_REQUEST['cat'], $_GET['mdl'], $_REQUEST['type'], 0, 0, null, $sort, false, array(), null, $fld_classes);
	$fields_count = ria_mysql_num_rows( $fields );
?>

	<form action="<?php print $url_action; ?>" method="post">

	<h2>
		<?php print _('Champs personnalisés'); ?> (<?php print ria_number_format($fields_count) ?>)

		<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_FIELD_ADD') ){ ?>
			<input type="submit" name="add" class="btn-add float-right" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter un champ personnalisé"); ?>" />
		<?php } ?>
	</h2>

	<div class="stats-menu">
		<div id="selectclass" class="riapicker">
			<div class="selectorview">
				<div class="left">
					<span class="function_name"><?php echo _("Classes"); ?></span>
					<br /><span class="view"><?php print $selectclass; ?></span>
				</div>
				<a class="btn" name="btn">
					<img class="fleche" src="/admin/images/stats/fleche.gif" alt="" />
				</a>
				<div class="clear"></div>
			</div>
			<div class="selector"><?php
				print '
					<a name="cls-0">' . _("Toutes les classes") . '</a>
					<a class="selector-sep"></a>
				';

				$rcls = fld_classes_get( 0, false, true, true, null, true );
				if( $rcls && ria_mysql_num_rows($rcls) ){
					while( $cls = ria_mysql_fetch_array($rcls) ){
						print '
							<a name="cls-'.$cls['id'].'">'.htmlspecialchars( $cls['name'] ).'</a>
						';
					}
				}
			?></div>
		</div>
		<div id="selecttypes" class="riapicker">
			<div class="selectorview">
				<div class="left">
					<span class="function_name"><?php echo _("Types"); ?></span>
					<br /><span class="view"><?php print $selecttypes; ?></span>
				</div>
				<a class="btn" name="btn">
					<img class="fleche" src="/admin/images/stats/fleche.gif" alt="" />
				</a>
				<div class="clear"></div>
			</div>
			<div class="selector"><?php
				print '
					<a name="type-0">' . _("Tous les types") . '</a>
					<a class="selector-sep"></a>
				';

				$rtype = fld_types_get();
				if( $rtype && ria_mysql_num_rows($rtype) ){
					while( $type = ria_mysql_fetch_array($rtype) ){
						print '
							<a name="type-'.$type['id'].'">'.htmlspecialchars( $type['name'] ).'</a>
						';
					}
				}
			?></div>
		</div>
		<div id="selectcategories" class="riapicker">
			<div class="selectorview">
				<div class="left">
					<span class="function_name"><?php echo _("Catégories"); ?></span>
					<br /><span class="view"><?php print $selectcategories; ?></span>
				</div>
				<a class="btn" name="btn">
					<img class="fleche" src="/admin/images/stats/fleche.gif" alt="" />
				</a>
				<div class="clear"></div>
			</div>
			<div class="selector"><?php
				print '
					<a name="cat-0">' . _("Toutes les catégories") . '</a>
					<a class="selector-sep"></a>
				';

				if( $classes = fld_classes_get( 0, false, true, true, null, true ) ){
					while( $cls = ria_mysql_fetch_array($classes) ){
						$categories = fld_categories_get( 0, false, $cls['id'] );
						while( $r = ria_mysql_fetch_array($categories) ){
							print '<a name="cat-'.$r['id'].'">'.htmlspecialchars( $cls['name'].' >> '.$r['name'] ).'</a>';
						}
					}
				}
			?></div>
		</div>
		<div class="clear"></div>
	</div>

	<?php
		if( isset($error) ){
			print '<div class="error">'._(nl2br(htmlspecialchars($error))).'</div>';
		}

		$colspan = 4;
		if( $_GET['cls'] ){
			$colspan = 5;
		}
	?>

	<input type="hidden" name="cls_id" id="cls_id" value="<?php print $_GET['cls']; ?>" />
	<input type="hidden" name="type_id" id="type_id" value="<?php print $_REQUEST['type']; ?>" />
	<input type="hidden" name="cat_id" id="cat_id" value="<?php print $_REQUEST['cat']; ?>" />

	<table id="table-champs-personnalises" class="checklist" <?php print ( isset($_GET['cls']) && is_numeric($_GET['cls']) ? 'data-cls="'.$_GET['cls'].'"' : '' )?>>
		<thead>
			<tr>
				<th id="select">
					<input type="checkbox" class="checkbox" onclick="checkAllClick(this);">
				</th>
				<th id="name"><?php print _('Nom du champ'); ?></th>
				<th id="type"><?php print _('Type'); ?></th>
				<th id="objects"><?php print _('Eléments'); ?></th>
				<?php
					if( $_GET['cls'] ){
						print '<th id="fld-cls-'.$_GET['cls'].'"></th>';
					}
				?>
			</tr>
		</thead>
		<tbody>
			<?php
				if( $classes = fld_classes_get($fld_classes) ){
					while( $class = ria_mysql_fetch_assoc($classes) ){
						if( $fields = fld_fields_get( 0, $_REQUEST['cat'], $_GET['mdl'], $_REQUEST['type'], 0, 0, null, $sort, false, array(), null, $class['id'] ) ){
							$old_cat = -1;
							$current = 0; $count = ria_mysql_num_rows($fields);
							while( $r = ria_mysql_fetch_array($fields) ){

								if( $_REQUEST['cat']==0 && $old_cat!=$r['cat_id'] ){
									print '<tr class="th-head-second"><th colspan="'.$colspan.'">'.htmlspecialchars( $class['name'].( $r['cat_name']!='' ? ' >> '.$r['cat_name'] : '' ) ).'</th></tr>';
									$old_cat = $r['cat_id'];
								}

								$link = '';
								$common_part = ria_number_format(fld_fields_is_used($r['id'])).' '.htmlspecialchars($class['name']);
								switch( $class['id'] ){
									case CLS_PRODUCT:
										if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT') ){
											$link = '<a href="/admin/catalog/index.php?fld='.$r['id'].'">'.$common_part.'</a>';
										}else{
											$link = $common_part;
										}
										break;
									case CLS_STORE:
										if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE') ){
											$link = '<a href="../../livraison/stores/index.php?fld='.$r['id'].'">'.$common_part.'</a>';
										}else{
											$link = $common_part;
										}
										break;
									case CLS_USER:
										if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER') ){
											$link = '<a href="/admin/customers/index.php?fld='.$r['id'].'">'.$common_part.'</a>';
										}else{
											$link = $common_part;
										}
										break;
									case CLS_ORDER:
										if( gu_user_is_authorized('_RGH_ADMIN_ORDER') ){
											$link = '<a href="/admin/orders/orders.php?fld='.$r['id'].'">'.$common_part.'</a>';
										}else{
											$link = $common_part;
										}
										break;
									case CLS_BRAND:
										if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND') ){
											$link = '<a href="/admin/catalog/brands/index.php?fld='.$r['id'].'">'.$common_part.'</a>';
										}else{
											$link = $common_part;
										}
										break;
									default : {
										if( !fld_classes_is_physical($r['cls_id']) ){
											$link = '<a href="/admin/config/fields/classes/objects.php?cls='.$r['cls_id'].'">'.$common_part.'</a>';
										}else{
											$link = $common_part;
										}
										break;
									}
								}

								print '<tr class="ria-row-orderable" id="line-'.$r['id'].'">
											<td headers="select"><input type="checkbox" class="checkbox" name="fld[]" value="'.$r['id'].'" '.( $r['is-sync']==1 || !$r['tenant'] ? 'disabled="disabled"' : '' ).' /></td>';
								if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_FIELD_EDIT') ){
									print '	<td headers="name"><a href="edit.php?fld='.$r['id'].'">'.htmlspecialchars($r['name']).'</a></td>';
								}else{
									print '	<td headers="name">'.htmlspecialchars( $r['name'] ).'</td>';
								}
								print '		<td headers="type">'.htmlspecialchars( $r['type_name'] ).'</td>
											<td headers="objects">'.$link.'</td>';
								if( $_GET['cls'] ){
									print '	<td headers="fld-cls-'.$_GET['cls'].'" class="align-center ria-cell-move">';
									print '		<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
									print '	</td>';
									$current++;
								}
								print '</tr>';

								if( !$have_fields )
									$have_fields = true;
							}
						}
					}
				}

				if( !$have_fields )
					print '<tr><td colspan="'.$colspan.'">' . _("Aucun champ personnalisé") . '</td></tr>';
			?>
		</tbody>
		<tfoot class="print-none">
			<tr><td colspan="<?php print $colspan ?>">
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_FIELD_EDIT') ){ ?>
				<input type="submit" name="clear" class="btn-del float-left" value="<?php echo _("Vider"); ?>" title="<?php echo _("Retirer les éléments associés aux champs sélectionnés."); ?>" onclick="return fldFieldConfirmClear()" />
				<?php }
				if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_FIELD_DEL') ){ ?>
				<input type="submit" name="del" class="btn-del float-left" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les champs sélectionnés"); ?>" onclick="return fldFieldConfirmDelList()" />
				<?php }
				if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_FIELD_ADD') ){ ?>
				<input type="submit" name="add" class="btn-add" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter un champ personnalisé"); ?>" />
				<?php } ?>
			</td></tr>
		</tfoot>
	</table>
</form>

<?php

require_once('admin/skin/footer.inc.php');