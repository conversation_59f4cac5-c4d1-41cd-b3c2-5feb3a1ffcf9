/** \file zipcode-city-autocompletion.js
 *  Ce fichier est à inclure pour activer l'auto-complétion de codes postaux / villes
 *  lors de la saisie d'adresses postales.
 *  Les champs #country, #zipcode et #city sont nécessaires au bon fonctionnement 
 */

// Liste des pays pour lesquels l'autocomplétion des codes postaux / villes est disponible
var country_autocomplete = ['ALLEMAGNE','ITALIE','FRANCE','ESPAGNE','LIECHTENSTEIN','POLYNÉSIE FRANÇAISE','PORTUGAL','SUISSE','PAYS-BAS','AUTRICHE','IRLANDE','BELGIQUE'];

// Contient les données de référence pour l'auto-complétion (liste des villes et code postaux)
var jsonCityZipcode = null;

// Affiche dans les champs #zipcode et #city la valeur choisie dans l'auto-complétion 
function displayAucompleteCity( label ){

	// La source est construite sous la formate CODEPOSTAL - VILLE
	var regex = /(\ -\ )/;
	var pos = regex.exec(label).index;

	// De la source de données, extrait la partie code postal pour la mettre dans le champ Zipcode
	$('#zipcode').val( label.substr(0, pos) );
	// De la source de données, extrait seulement la ville pour la mettre dans le champ City
	$('#city').val( label.substr(pos+3) );

}

// Détermine si l'auto-complétion par ville/code postal est disponible ou non
function isCityAutocompleteEnabled(){

	// L'auto-complétion ne se déclenchera que si le pays est supporté et la liste des villes chargée
	if( $('#country').length && country_autocomplete.includes($('#country').val().toUpperCase()) ){
		// Si la liste des villes est déjà disponible (chargée), lance la recherche
		if( jsonCityZipcode !== null ){
			return true;
		}
	}

	return false;
}

$(document).ready(
	function(){

        // En cas de modification du pays, charge la liste des villes / codes postaux
		$('#country').change(function() {
			
			jsonCityZipcode = null;

			// Précharge le fichier de données contenant les villes/codes postaux du pays actuellement sélectionné
			// pour qu'il n'y ai plus de temps d'attente lorsque l'utilisateur va utiliser l'auto-complétion
			if( $('#country').length && country_autocomplete.includes($('#country').val().toUpperCase()) ){
				$.ajax({
					dataType: 'json',
					type: 'get',
					url: '/admin/ajax/customers/json-city-zipcode.php',
					data: { "country": $('#country').val().toUpperCase(), "version": 2 },
					success: function(data) {
						jsonCityZipcode = data;
					}
				});
			}
		
		});
		
		// Déclenche l'événement de changement de pays pour pré-charger le fichier json
		$('#country').change();
        
        // Défini la fonction d'autocomplétion pour le champ Ville (city)
		if(typeof $('#zipcode') !== "undefined" && $('#city').length){
			$('#city').autocomplete({
				source: function(request, response) {
					// L'auto-complétion ne se déclenchera que si le pays est supporté et la liste des villes chargée
					if( isCityAutocompleteEnabled() ){

						var results = $.ui.autocomplete.filter(jsonCityZipcode.zipcode, strtoupper(request.term));
						response(results);

					}
				},
				delay: 500,
				minLength: 3,
				select: function (event, ui) { // Affiche la valeur sélectionnée
					event.preventDefault();
					displayAucompleteCity( ui.item.label );
				},
			});
		}
        
        // Défini l'autocomplétion pour le champ Code Postal (zipcode)
		if(typeof $('#zipcode') !== "undefined" && $('#zipcode').length){
			$('#zipcode').autocomplete({
				source: function(request, response) {
					// L'auto-complétion ne se déclenchera que si le pays est supporté et la liste des villes chargée
					if( isCityAutocompleteEnabled() ){

						// 1er filtre sur "contient"
						var results = $.ui.autocomplete.filter(jsonCityZipcode.zipcode, request.term);

						// 2ème filtre "commence par"
						var resultsStartWith = $.map(results, function (item) {
							if (item.indexOf(request.term) == 0) {
								return item;
							} else {
								return null;
							}
						});

						response(resultsStartWith);

					}
				},
				delay: 500,
				minLength: 2, // Autocomplétion à partir de 3 caractères uniquement
				select: function (event, ui) { // Affiche la valeur sélectionnée
					event.preventDefault();
					displayAucompleteCity( ui.item.label );
				},
			});
		}

		// Pour forcer la désactivation de la saisie automatique du navigateur il faut mettre une chaine invalide à l'attribut autocomplete car la valeur off est ignoré
		$("#city").attr("autocomplete", "nope");
		$("#zipcode").attr("autocomplete", "nope");

    }
);