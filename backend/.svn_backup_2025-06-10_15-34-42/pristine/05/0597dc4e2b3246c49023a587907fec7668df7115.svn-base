<?php

set_include_path(dirname(__FILE__) . '/../include/');

	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once('products.inc.php');

	$products = prd_products_get( 0, '', 0, true, 0, 0, -1, false, false, false, true, false, false, array('date-published'=>'desc') );

	$fp = fopen( $config['site_dir'].'/rss/destockage.rss', 'w' ) or die( 'erreur' );

	fwrite( $fp, '<?xml version="1.0" encoding="utf-8"?>'."\n" );
	fwrite( $fp, '<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">'."\n" );

	// Description du fil RSS
	fwrite( $fp, '	<channel>'."\n" );
	fwrite( $fp, '		<title>Déstockage - '.xmltextentities($config['site_name']).' - '.xmltextentities($config['site_desc']).'</title>'."\n" );
	fwrite( $fp, '		<link>'.$config['site_url'].'/catalogue/destockage</link>'."\n" );
	fwrite( $fp, '		<description>'.xmltextentities($config['meta_description']).'</description>'."\n" );
	fwrite( $fp, '		<language>fr-FR</language>'."\n" );
	fwrite( $fp, '		<atom:link href="'.$config['site_url'].'/rss/destockage.rss" rel="self" type="application/rss+xml" />'."\n" );
	fwrite( $fp, '		<pubDate>'.strftime('%a, %d %b %Y %H:%M:%S +0100').'</pubDate>'."\n" );
	fwrite( $fp, '		<ttl>1440</ttl>'."\n" );

	// Affichage des produits
	while( $p = ria_mysql_fetch_array($products) ){

		$p['url_alias'] = prd_products_get_url( $p['id'], true );

		$cat_name = '';
		$rcats = prd_products_categories_get( $p['id'] );
		if( ria_mysql_num_rows($rcats) )
			$cat_name = ria_mysql_result( $rcats, 0, 'title' );

		fwrite( $fp, '		<item>'."\n" );
		fwrite( $fp, '			<title>'.xmltextentities($p['name']).'</title>'."\n" );
		fwrite( $fp, '			<link>'.$config['site_url'].$p['url_alias'].'</link>'."\n" );
		fwrite( $fp, '			<description>'.strlines(xmltextentities($p['desc'])).'</description>'."\n" );
		fwrite( $fp, '			<category domain="">'.xmltextentities($cat_name).'</category>'."\n" );
		fwrite( $fp, '			<guid isPermaLink="true">'.$config['site_url'].$p['url_alias'].'</guid>'."\n" );
		fwrite( $fp, '			<pubDate>'.$p['date_published_rss'].'</pubDate>'."\n" );
		fwrite( $fp, '			<source url="'.$config['site_url'].'/rss/destockage.rss">'.xmltextentities($config['site_name']).'</source>'."\n" );
		fwrite( $fp, '		</item>');

	}

	fwrite( $fp, '	</channel>'."\n" );
	fwrite( $fp, '</rss>'."\n" );

	fclose( $fp );


