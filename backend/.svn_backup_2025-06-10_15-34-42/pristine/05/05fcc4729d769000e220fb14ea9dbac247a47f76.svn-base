<?php
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à accès à cette page
	if( $_GET['cls']!=0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_CLASS_EDIT');
	}elseif( $_GET['cls']==0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_CLASS_ADD');
	}
	
	if( isset($_GET['cancel']) ){
		header('Location: /admin/config/fields/classes/index.php');
		exit;
	}
	
	$cls_id = isset($_GET['cls']) && is_numeric($_GET['cls']) ? $_GET['cls'] : 0;
	$cls = array( 'tenant'=>$config['tnt_id'], 'id'=>0, 'name'=>'', 'desc'=>'', 'fields'=>0, 'models'=>0, 'categories'=>0, 'physical'=>0 );
	
	if( $cls_id>0 ){
		$rcls = fld_classes_get( $cls_id, false, true, true, null, null, true );
		if( $rcls && ria_mysql_num_rows($rcls) ){
			$cls = ria_mysql_fetch_assoc( $rcls );
		}
	}
	
	// Pour le moment les classes systèmes ne sont pas accessibles
	if( $cls['tenant']!=$config['tnt_id'] ){
		header('Location: /admin/config/fields/classes/index.php');
		exit;
	}
	
	if( isset($_POST) ){
		$cls['name'] = isset($_POST['cls-name']) ? $_POST['cls-name'] : $cls['name'];
		$cls['desc'] = isset($_POST['cls-desc']) ? $_POST['cls-desc'] : $cls['desc'];
	}

	if( isset($_POST['del']) ){
		if( !fld_classes_del($cls['id']) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la classe. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		}
		
		if( !isset($error) ){
			header('Location: /admin/config/fields/classes/index.php');
			exit;
		}
	}
	
	if( isset($_POST['save']) ){
		if( !isset($_POST['cls-name'], $_POST['cls-desc']) ){
			$error = _("Une ou plusieurs informations sont manquantes.");
		}elseif( trim($_POST['cls-name'])=='' ){
			$error = _("Veuillez renseigner la désignation de la classe.");
		}else{
			if( $cls['id']==0 ){
				$add = fld_classes_add( $_POST['cls-name'], $_POST['cls-desc'] );
				if( !$add ){
					$error = _("Une erreur inattendue s'est produite lors de la création de la classe. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				}else{
					$cls['id'] = $add;
				}
			}else{
				if( !fld_classes_update($cls['id'], $_POST['cls-name'], $_POST['cls-desc']) ){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour des informations de la classe. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
		}
		
		if( !isset($error) ){
			header('Location: /admin/config/fields/classes/edit.php?cls='.$cls['id']);
			exit;
		}
	}
	
	// Lecture seule sur les classes physiques
	$readonly = $cls['physical'] || $cls['tenant']==0;

	$page_title = $cls['name'] ? $cls['name'] : 'Nouvelle classe';
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Classes') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print htmlspecialchars( $page_title ); ?></h2>
	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
	?>
	
	<form action="/admin/config/fields/classes/edit.php?cls=<?php print $cls['id']; ?>" method="post">
		<table id="table-fiche-classe">
			<tbody>
				<tr>
					<td>
						<label for="cls-ref"><?php echo _('Référence :'); ?></label>
					</td>
					<td>
						<input type="text" id="cls-ref" value="<?php print $_GET['cls']?>" disabled="disabled" />
					</td>
				</tr>
				<tr>
					<td>
						<label for="cls-name"><span class="mandatory">*</span> <?php echo _('Désignation :'); ?></label>
					</td>
					<td>
						<input type="text" name="cls-name" id="cls-name" value="<?php print htmlspecialchars( $cls['name'] ); ?>" <?php print $readonly ? 'disabled="disabled"' : ''; ?> />
					</td>
				</tr>
				<tr>
					<td>
						<label for="cls-desc"><?php print _('Description :'); ?></label>
					</td>
					<td>
						<textarea name="cls-desc" id="cls-desc" <?php print $readonly ? 'disabled="disabled"' : ''; ?>><?php print htmlspecialchars( $cls['desc'] ); ?></textarea>
					</td>
				</tr>
				
				<?php if( $cls['id']>0 ){ ?>
				<tr>
					<td><?php print _('Champs :'); ?></td>
					<td><?php
						print '<a href="/admin/config/fields/fields/index.php?cls='.$cls['id'].'">'.ria_number_format($cls['fields']).' champ'.($cls['fields'] > 1 ? 's' : '').'</a>';
					?></td>
				</tr>
				<tr>
					<td><?php print _('Objets :'); ?></td>
					<td><?php
						$objects = fld_classes_get_objects_count( $cls['id'] );
						print '<a href="/admin/config/fields/classes/objects.php?cls='.$cls['id'].'">'.ria_number_format($objects).' objet'.($objects > 1 ? 's' : '').'</a>';
					?></td>
				</tr>
				<?php } ?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="2">
						<input type="submit" value="<?php echo _("Enregistrer"); ?>" name="save" />
						<input type="submit" value="<?php echo _("Annuler"); ?>" name="cancel" />
						<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CLASS_DEL') ){ ?>
						<input type="submit" onclick="return fldClasseConfirmDel();" value="<?php echo _("Supprimer"); ?>" name="del" />
						<?php } ?>
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>