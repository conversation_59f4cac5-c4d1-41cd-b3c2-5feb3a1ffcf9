<?php

/** \defgroup pdm_ebay Ebay
 *	\ingroup pdm
 *	Ce module comprend les fonctions nécessaires à la communication avec la plateforme Ebay
 *	@{
 */

	require_once('email.inc.php');
	require_once('comparators.inc.php');

	/** Cette fonction permet de récupérer le compte client a qui les commandes eBay sera affecté.
	 *	@return int|false L'identifiant du compte, False si ce dernier n'est pas défini
	*/
	function ctr_ebay_get_user(){
		$params = ctr_params_get( CTR_EBAY, 'USR_ID' );
		if( !$params || !ria_mysql_num_rows($params) ){
			return false;
		}

		return ria_mysql_result( $params, 0, 'fld' );
	}

	/**	\class EBay
	 *	\brief Cette classe facilite la gestion des communications avec l'API d'Ebay
	 */
	class EBay {
		private $version; ///< Version
		private $runame; ///< Element d'identification auprès de l'api Ebay
		private $loginURL; ///< Url de connexion

		public $country = 'fr'; ///< Pays
		public $sandbox = false; ///< Mode Développement (true) ou Production (false)
		public $user_id = 0; ///< Identifiant du compte utilisateur Ebay pour l'importation des commandes

		private $devID; ///< Identifiant de développeur (RiaStudio)
		private $appID; ///< Identifiant de l'application (RiaShop)
		private $certID; ///< Certificat pour le cryptage des échanges
		private $apiUrl; ///< Url de l'API Ebay (dépend du mode Développement/Production)
		private $apiCall;
		private $loginUrl; ///< Url à utiliser pour l'authentification (dépend du mode Développement/Production)

		/// Données de localisation
		private $country_data = array(
			'fr' => array(
				'site_id' => 71,
				'language' => 'fr_FR',
				'currency' => 'EUR',
				'site_name' => 'France',
				'site_extension' => 'fr',
				'img_stats' => 'views/img/ebay_stats.png',
				'code_iso' => 'FR',
				'code_token' => 'France'
			)
		);

		/**	Constructeur
		 *	@param $sandbox Facultatif, booléen indiquant si l'exécution se fait en production (false) ou en développement (true)
		 */
		public function __construct( $sandbox=false ){
			$this->sandbox = $sandbox ? true : false;

			$this->devID = 'eeecfcf3-7320-4431-9336-812ef10f0223';

			// Les éléments d'identification et d'appel dépend du mode (production / développement)
			$this->version = 847;
			if( $this->sandbox ){
				$this->appID = 'RiaStudi-72fa-4629-9ad9-24d867c16fea';
				$this->certID = 'de05b4b4-6e63-4e95-b556-6453df4321fc';
				$this->apiUrl = 'https://api.sandbox.ebay.com/ws/api.dll';
				$this->runame = 'RiaStudio-RiaStudi-72fa-4-ctdubjz';
				$this->loginURL = 'https://signin.sandbox.ebay.'.EBay::getSiteExtension().'/ws/eBayISAPI.dll';
			}else{
				// Récupérer les variables du client

				$this->appID = 'RiaStudi-1101-497e-9975-cc82529c85eb';
				$this->certID = '2be7b5e3-f41f-49b6-a2d7-b657f15c2ca1';
				$this->apiUrl = 'https://api.ebay.com/ws/api.dll';
				$this->runame = 'RiaStudio-RiaStudi-1101-4-dimyn';
				$this->loginURL = 'https://signin.ebay.'.EBay::getSiteExtension().'/ws/eBayISAPI.dll';
			}

			// Récupère le compte client à utiliser pour l'import des commandes et le calcul du prix des produits
			$rusr = ctr_params_get( CTR_EBAY, 'USR_ID' );
			if( $rusr && ria_mysql_num_rows($rusr) ){
				$this->user_id = ria_mysql_result( $rusr, 0, 'fld' );
			}
		}

		/** Cette fonction permet de récupérer les informations sur le produit frais de port paramétré.
		 *	Un cache de 30 minutes est mis en place sur cette fonction
		 *	@return bool False si le produit n'est pas paramètré, sinon les informations sur le produit contenant dans un tableau :
		 *				- id : identifiant du produit frais de port
		 *				- ref : référence du produit frais de port
		 *				- name : nom du produit frais de port
		 */
		public function getProductShipping(){
			global $config;
			global $memcached;

			if($shipping = $memcached->get( 'ebay:getproductshipping:'.$config['tnt_id'] ) ){
				return $shipping;
			}

			$port = '';

			// Récupère la référence dans la configuration d'eBay
			$rport = ctr_params_get( CTR_EBAY, 'port_ref' );
			if( $rport && ria_mysql_num_rows($rport) ){
				$port = ria_mysql_result( $rport, 0, 'fld' );
			}

			if( trim($port)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le paramètre des frais de port n\'est pas défini.');
				return false;
			}

			// Récupère les informations sur le produit
			$rp = prd_products_get_simple( 0, $port );
			if( !$rp || !ria_mysql_num_rows($rp) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le produit frais de port n\'existe pas.');
				return false;
			}

			$p = ria_mysql_fetch_array( $rp );

			$shipping = array(
				'id' => $p['id'],
				'ref' => $p['ref'],
				'name' => $p['name']
			);

			$memcached->set( 'ebay:getproductshipping:'.$config['tnt_id'], $shipping, 900 );
			return $shipping;
		}

		/** Cette fonction retourne l'url de connexion permettant de rattacher un compte eBay à RiaShopping.
		 *	@return string L'URL de connexion
		 */
		public function getUrlLogin(){
			return $this->loginURL.'?SignIn&runame='.$this->runame.'&SessID='.EBay::getSessionID(true);
		}

		/** Cette fonction permet de générer un token.
		 */
		public function fetchToken(){
			$token = cfg_overrides_get_value( 'ebay_api_token' );
			if( trim($token)!='' ){
				return $token;
			}

			global $config;

			$userID = '';
			$rparam = ctr_params_get( CTR_EBAY, 'user_id' );
			if( $rparam && ria_mysql_num_rows($rparam) ){
				$param = ria_mysql_fetch_array( $rparam );
				if( trim($param['fld'])!='' ){
					$userID = $param['fld'];
				}
			}

			if( trim($userID)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le paramètre du compte client eBay n\'est pas défini.');
				return false;
			}

			$sessionID = EBay::getSessionID();
			if( trim($sessionID)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de récupérer l\'identifiant de session.');
				return false;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8" ?>
				<FetchTokenRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<RequesterCredentials>
						<Username>'.$userID.'</Username>
					</RequesterCredentials>
					<SessionID>'.$sessionID.'</SessionID>
				</FetchTokenRequest>
			';

			$response = EBay::sendRequest('FetchToken', $request);
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			if( !cfg_overrides_set_value('ebay_api_token', $response->eBayAuthToken) || !cfg_overrides_set_value('ebay_reload_token', false) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour la variable de configuration "ebay_api_token" ("'.$response->eBayAuthToken.'").');
				return false;
			}

			if( !cfg_overrides_set_value('ebay_reload_token', 0) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre l\'information d\'envoi du mail de reconnexion à 0.');
			}

			return $response->eBayAuthToken;
		}

		/** Cette fonction permet de modifier le site sur lequel les produits seront envoyés
		 *	@param $code Obligatoire, code du pays
		 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		public function setCountryCode( $code ){
			if( trim($code)=='' ){
				return false;
			}

			if( !isset($this->country_data[$code]) ){
				return false;
			}

			$this->country = $code;
		}

		/** Cette fonction permet de mettre à jour les catégories de eBay proposées dans RiaShop.
		 *	@param $show_update Optionnel, mettre à true pour voir la progression de la mise à jour, False par défaut
		 *	@return bool true si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		public function updateEBayCategories( $show_update=false ){
			global $config;

			$categs = EBay::getCategories();
			if( !isset($categs->CategoryArray->Category) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune catégorie n\'a été retournée pour la mise à jour.');
				return false;
			}

			$cats = $categs->CategoryArray->Category;

			$upd_hierarchy = false;
			$ar_refs = array();
			$links = array();

			// Ajout des catégories si elle n'existe pas
			foreach( $cats as $cat ){
				// Exclu les catégories dont le paiement immédiat n'est pas accepté
				if( !isset($cat->AutoPayEnabled) || !$cat->AutoPayEnabled ){
					continue;
				}

				// Exclu les catégories dépréciée
				if( isset($cat->Expired) && $cat->Expired ){
					continue;
				}

				if( $show_update ){
					print $cat->CategoryID.' => '.$cat->CategoryName."\n";
				}

				// Controle si la catégorie n'existe pas
				$exists = ctr_categories_get_id_byref( CTR_EBAY, $cat->CategoryID );
				if( !$exists ){
					// Ajout de la catégorie
					$c = ctr_categories_add( CTR_EBAY, $cat->CategoryName, $cat->CategoryID );
					if( $c ){
						// Ajout du lien parent - enfant
						if( (int) $cat->CategoryID != (int) $cat->CategoryParentID ){
							$links[] = array(
								'parent' => $cat->CategoryParentID,
								'child' => $cat->CategoryID
							);
						}

						$upd_hierarchy = true;
					}
				}else{
					ctr_categories_update_name( CTR_EBAY, $exists, $cat->CategoryName );

					if( $show_update ){
						print 'update name '.$cat->CategoryName."\n";
					}
				}

				$ar_refs[] = $cat->CategoryID;
			}

			// Rattache les catégories entres elles
			if( sizeof($links) ){
				foreach( $links as $link ){
					$parent = ctr_categories_get_id_byref( CTR_EBAY, $link['parent'] );
					$child = ctr_categories_get_id_byref( CTR_EBAY, $link['child'] );

					if( $parent && $child ){
						ctr_categories_update_parent( CTR_EBAY, $child, $parent );
					}
				}
			}

			// Supprime les catégories désactivées
			$rcat = ctr_categories_get( CTR_EBAY );
			if( !$rcat || !ria_mysql_num_rows($rcat) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune catégorie eBay n\'a été retournée.');
				return false;
			}

			$ar_disabled = array();
			while( $cat = ria_mysql_fetch_array($rcat) ){
				if( !in_array($cat['ref'], $ar_refs) ){
					ctr_categories_update_disabled( CTR_EBAY, $cat['id'] );
					$upd_hierarchy = true;
					$ar_disabled[] = $cat['id'].' - '.$cat['name'];
				}
			}

			// Mise à jour de l'arborescence, si un ajout ou désactivation de catégorie a eu lieu
			if( $upd_hierarchy ){
				if( $show_update ){
					print "\n".'Reconstruction de la hierarchie des categories eBay...'."\n";
				}

				ctr_categories_hierarchy_rebuild( CTR_EBAY );
			}

			return true;
		}

		/** Cette fonction permet de gérer un produit quelque soit l'action sur eBay.
		 *	@param int $id Obligatoire, identifiant d'un produit
		 *	@param $action Optionnel, par défautl il s'agit d'un ajout 'add', mettre 'update' pour mettre à jour un produit ou 'delete' pour désactiver le produit
		 *	@param $price Optionnel, il s'agit du prix de vente du produit : obligatoire si l'action est égale à "updatepriceqte", ignoré dans les autres cas
		 *	@param $qty Optionnel, il s'agit de la quantité en stock du produit : obligatoire si l'action est égale à "updatepriceqte", ignoré dans les autres cas
		 *	@param $old_price Optionnel, il s'agit du prix avant promotion : ignoré si l'action n'est pas égale à "updatepriceqte"
		 *	@return bool True si l'action s'est correctement déroulée, False dans le cas contraire
		 */
		public function worqueueEBayProduct( $id, $action='add', $price=0, $qty=0, $old_price=0 ){
			global $config;

			if( !is_numeric($id) || $id<=0 ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant produit "'.$id.'" est faux.');
				return false;
			}

			if( !in_array($action, array( 'add', 'update', 'updatepriceqte', 'delete' )) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'action "'.$action.'" passée en paramètre est inconnue.');
				return false;
			}

			// Récupère les informations sur l'export du produit sur eBay
			$itemID = fld_object_values_get( array(CTR_EBAY, $id), _FLD_PRD_EBAY_ID );

			$rp = prd_products_get_simple( $id );
			if( !$rp || !ria_mysql_num_rows($rp) ){
				if( trim($itemID)!='' ){
					// error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Confirmer la désactivation du produit "'.$id.'" avant de retirer ce log.');
					if( !EBay::endFixedPriceItem($itemID, $id) ){
						return false;
					}

					return true;
				}

				return false;
			}

			if( $action=='updatepriceqte' ){
				if( !is_numeric($price) || $price<=0 ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le prix du produit "'.$id.'" est inférieur ou égale à zéro, impossible à mettre à jour.');
					return false;
				}

				if( !is_numeric($qty) || $qty<0 ){
					// error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La quantité du produit "'.$id.'" est inférieure à zéro, impossible à mettre à jour.');
					return false;
				}

				if( !is_numeric($old_price) || $old_price<0 ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le prix promo du produit "'.$id.'" est inférieur à zéro, impossible à mettre à jour.');
					return false;
				}
			}

			// Récupère les informations sur le produit
			$p = ria_mysql_fetch_array( $rp );

			$is_parent = prd_products_is_parent( $p['id'] );

			// S'il ne s'agit pas d'un produit parent, on récupère l'identifiant de son produit parent
			$parent = false;
			if( !$is_parent ){
				$parent = prd_products_get_parent( $p['id'], true );

				// Si le parent n'est pas exporté, l'export du produit enfant est considéré comme un produit normal
				if( $parent && !ctr_catalogs_is_publish(CTR_EBAY, $parent) ){
					$parent = false;
				}
			}

			// Détermine si le produit est actuellement désactivé (seulement si l'item_id est renseigné
			$is_disabled = trim($itemID)!='' ? EBay::getProductIsDisabled( $p['id'] ) : false;

			// Mise à jour du prix et de la quantité
			if( $action=='updatepriceqte' ){
				if( $is_parent ){
					return true;
				}

				if( $parent && trim($itemID)=='' ){
					$itemID = fld_object_values_get( array(CTR_EBAY, $parent), _FLD_PRD_EBAY_ID );
					if( $itemID ){
						if( $qty<=0 ){
							if( !EBay::endFixedPriceChild($itemID, $parent, $p['ref']) ){
								return false;
							}
						}else{
							if( EBay::getProductIsDisabled($parent) ){
								if( !EBay::relistFixedPriceItem($itemID, $parent) ){
									return false;
								}
							}else{
								if( !EBay::updatePriceAndQuantity($itemID, $price, $qty, $p['ref'], $old_price, $p['id']) ){
									return false;
								}
							}
						}
					}
				}else{
					if( $itemID ){
						if( $qty<=0 ){
							if( !EBay::endFixedPriceItem($itemID, $p['id']) ){
								return false;
							}
						}else{
							if( $is_disabled ){
								if( !EBay::relistFixedPriceItem($itemID, $p['id']) ){
									return false;
								}
							}else{
								if( !EBay::updatePriceAndQuantity($itemID, $price, $qty, '', $old_price, $p['id']) ){
									return false;
								}
							}
						}
					}
				}

				return true;
			}

			// Gestion de l'ajout d'un produit ou de la mise à jour
			if( $action=='add' || $action=='update' ){
				if( !$is_parent && !$parent ){ // Il s'agit d'un produit normal
					$category = EBay::getExportCategory( $p['id'] );
					if( !$category || trim($category)=='' ){
						// error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune catégorie eBay n\'est définie pour l\'ajout / mise à jour du produit "'.$p['id'].'".');
						return false;
					}

					if( $p['stock']<=0 ){
						if( $itemID && !$is_disabled ){
							if( !EBay::endFixedPriceItem($itemID, $p['id']) ){
								return false;
							}
						}

						return true;
					}else{
						if( $itemID ){
							if( $is_disabled ){
								if( !EBay::relistFixedPriceItem($itemID, $p['id']) ){
									return false;
								}
							}else{
								$data = EBay::getDataForExportProduct( 'ReviseFixedPriceItem', $p, $category );

								if( !EBay::reviseFixedPriceItem($itemID, $data) ){
									return false;
								}
							}
						}else{
							$data = EBay::getDataForExportProduct( 'AddFixedPriceItem', $p, $category );

							if( !EBay::addFixedPriceItem($data) ){
								return false;
							}
						}

						return true;
					}
				}else{

					if( $is_parent ){
						$category = EBay::getExportCategory( $p['id'] );
						if( !$category || trim($category)=='' ){
							// error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune catégorie eBay n\'est définie pour l\'ajout / mise à jour du produit "'.$p['id'].'".');
							return false;
						}

						if( EBay::getAcceptedMultiSKU($category)!='false' ){
							// La catégorie d'export du produit parent accepte les déclinaisons de produit
							if( $itemID ){
								$data = EBay::getDataForExportProduct( 'ReviseFixedPriceItem', $p, $category, true );

								// Aucune déclinaison n'est disponible, on désactive le produit parent
								if( $data==-1 ){
									if( !$is_disabled && !EBay::endFixedPriceItem($itemID, $p['i']) ){
										return false;
									}

									return true;
								}

								if( $is_disabled ){
									if( !EBay::relistFixedPriceItem($itemID, $p['id']) ){
										return false;
									}
								}else{
									if( !EBay::reviseFixedPriceItem($itemID, $data) ){
										return false;
									}
								}
							}else{
								$data = EBay::getDataForExportProduct( 'AddFixedPriceItem', $p, $category, true );

								// Aucune déclinaison n'est disponible, on n'exporte pas le produit parent
								if( $data==-1 ){
									return true;
								}

								if( !EBay::addFixedPriceItem($data) ){
									return false;
								}
							}

							return true;
						}else{
							// La catégorie d'export du produit parent n'accepte pas les déclinaisons de produit, on exporte les produits enfants séparéments (comme des produits normaux)
							$childs = ctr_catalogs_get_childs( CTR_EBAY, $p['id'], true );
							if( !is_array($childs) || !sizeof($childs) ){
								return true;
							}

							foreach( $childs as $child ){
								$itemID_child = fld_object_values_get( array(CTR_EBAY, $child), _FLD_PRD_EBAY_ID );

								$rpchild = prd_products_get_simple( $child );
								if( !$rpchild || !ria_mysql_num_rows($rpchild) ){
									error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le produit enfant "'.$child.'" n\'existe plus.');
									return false;
								}

								$pchild = ria_mysql_fetch_array( $rpchild );

								if( $pchild['stock']<=0 ){
									if( $itemID_child && !$is_disabled ){
										if( !EBay::endFixedPriceItem($itemID_child, $pchild['id']) ){
											return false;
										}
									}

									return true;
								}else{
									if( $itemID_child ){
										if( $is_disabled ){
											if( !EBay::relistFixedPriceItem($itemID, $child) ){
												return false;
											}
										}else{
											$data = EBay::getDataForExportProduct( 'ReviseFixedPriceItem', $pchild, $category );

											if( !EBay::reviseFixedPriceItem($itemID_child, $data) ){
												return false;
											}
										}
									}else{
										$data = EBay::getDataForExportProduct( 'AddFixedPriceItem', $pchild, $category );

										if( !EBay::addFixedPriceItem($data) ){
											return false;
										}
									}
								}
							}

							return true;
						}
					}else{
						$itemID_parent = fld_object_values_get( array(CTR_EBAY, $parent), _FLD_PRD_EBAY_ID );
						if( $itemID_parent ){ // Mise à jour
							// Contrôle que la catégorie d'export du produit parent accepte les variations
							$category = EBay::getExportCategory( $parent );
							if( !$category || trim($category)=='' ){
								// error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune catégorie eBay n\'est définie pour l\'ajout / mise à jour du produit "'.$parent.'".');
								return false;
							}

							if( EBay::getAcceptedMultiSKU($category)!='false' ){
								return EBay::updateProductVariation( $itemID_parent, $parent, $category );
							}
						}

						return EBay::worqueueEBayProduct( $parent, $action );
					}

				}

			}

			// Gestion de la suppression d'un produit
			if( $action=='delete' ){

				if( $is_parent ){ // Il s'agit de la suppression d'un produit parent
					if( $itemID ){
						if( !EBay::endFixedPriceItem($itemID, $p['id']) ){
							return false;
						}

						if( !fld_object_values_set(array(CTR_EBAY, $p['id']), _FLD_PRD_EBAY_DISABLED, date('Y-m-d H:i:s')) ){
							error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour la date de désactivation du produit "'.$p['id'].'" : '.date('Y-m-d H:i:s').'.');
							return false;
						}
					}

					return true;
				}elseif( $parent && !$itemID ){ // Il s'agit de la suppression d'un produit enfant, on récupère donc l'identifiant de l'objet parent
					$itemID 	= fld_object_values_get( array(CTR_EBAY, $parent), _FLD_PRD_EBAY_ID );
					if( $itemID ){
						if( !EBay::endFixedPriceChild($itemID, $parent, $p['ref']) ){
							return false;
						}

						if( !fld_object_values_set(array(CTR_EBAY, $p['id']), _FLD_PRD_EBAY_DISABLED, date('Y-m-d H:i:s')) ){
							error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour la date de désactivation du produit "'.$p['id'].'" : '.date('Y-m-d H:i:s').'.');
							return false;
						}
					}

					return true;
				}else{ // Il s'agit d'un produit normal
					if( $itemID ){
						if( !EBay::endFixedPriceItem($itemID, $p['id']) ){
							return false;
						}

						if( !fld_object_values_set(array(CTR_EBAY, $p['id']), _FLD_PRD_EBAY_DISABLED, date('Y-m-d H:i:s')) ){
							error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour la date de désactivation du produit "'.$p['id'].'" : '.date('Y-m-d H:i:s').'.');
							return false;
						}
					}

					return true;
				}

			}
		}

		/** Cette fonction permet de savoir si un produit est désactivé (manullement ou automatiquement).
		 *	@param $prd Obligatoire, identifiant du produit
		 *	@return bool True si le produit est désactivé, False dans le cas contraire
		 */
		public function getProductIsDisabled( $prd ){
			global $config;

			if( !is_numeric($prd) || $prd<=0 ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant produit "'.$prd.'" est faux.');
				return false;
			}

			$disabled 	= fld_object_values_get( array(CTR_EBAY, $prd), _FLD_PRD_EBAY_DISABLED ); // Date de désactivation manuelle
			$date_end 	= fld_object_values_get( array(CTR_EBAY, $prd), _FLD_PRD_EBAY_END );		// Date de désactivation automatique retournée par eBay lors de l'ajout du produit
			$date_has_passed = trim($date_end)!='' && strtotime(date('Y-m-d H:i:s'))>=strtotime($date_end);

			return trim($disabled)!='' || $date_has_passed;
		}

		/** Cette fonction permet de récupérer les caractéristiques d'une catégorie
		 *	@param int $cat Obligatoire, identifiant d'une catégorie chez eBay (correspond à la référence dans RiaShop)
		 *	@param $feature Optionnel, identifiant de la caractéritique
		 *	@return bool False si les deux paramètres sont omis, sinon le résultat renvoyé par eBay
		 */
		public function getCategoryFeatures( $cat, $feature=false ){
			global $config;

			if( trim($cat)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La référence catégorie "'.$cat.'" est fausse.');
				return false;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<GetCategoryFeaturesRequest xmlns="urn:ebay:apis:eBLBaseComponents">
			';

			if( $feature ){
				$request .= '
					<FeatureID>'.$feature.'</FeatureID>
				';
			}

			if( $cat ){
				$request .= '
					<CategoryID>'.$cat.'</CategoryID>
				';
			}

			$request .= '
					<ViewAllNodes>true</ViewAllNodes>
					<WarningLevel>High</WarningLevel>
					<DetailLevel>ReturnAll</DetailLevel>
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
				</GetCategoryFeaturesRequest>
			';

			return EBay::sendRequest( 'GetCategoryFeatures', $request );
		}

		/** Cette fonction permet de récupérer les caractéristiques d'un produit selon sa catégorie d'export.
		 *	Un cache de 30 minutes est mis en place sur cette fonction.
		 *	@param int $cat Obligatoire, identifiant de la catégorie chez eBay (correspond à la référence dans RiaShop)
		 *	@return bool False si le paramètre est omis ou faux, sinon un tableau construit de cette façon :
		 *				array(
		 *					'name' => nom de la caractéristique,
		 *					'mode' => FreeText = champ libre, Prefilled = pré-rempli par eBay, en lecture seul, SelectionOnly = liste déroulante de valeur (toute autre valeur retournera une erreur)
		 *					'values' => valeurs de sélection / valeur recommandée
		 */
		public function getCategorySpecifics( $cat ){
			global $config;
			global $memcached;

			if( trim($cat)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La référence catégorie "'.$cat.'" est fausse.');
				return false;
			}

			if($specifics = $memcached->get( 'ebay:getcategoryspecifics:'.$config['tnt_id'].':'.$cat ) ){
				return $specifics;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<GetCategorySpecifics xmlns="urn:ebay:apis:eBLBaseComponents">
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
					<CategorySpecific>
						<CategoryID>'.$cat.'</CategoryID>
					</CategorySpecific>
					<MaxValuesPerName>2147483647</MaxValuesPerName>
				</GetCategorySpecifics>
			';

			$response = EBay::sendRequest( 'GetCategorySpecifics', $request );
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			if( !isset($response->Recommendations->NameRecommendation) ){
				return false;
			}

			$fields = $response->Recommendations->NameRecommendation;

			$specifics = array();
			$i = 0;
			foreach( $fields as $f ){
				$specifics[ $i ] = array(
					'name' 		=> (string) $f->Name,
					'mode' 		=> (string) $f->ValidationRules->SelectionMode,
					'values' 	=> array(),
					'rules'		=> array()
				);

				if( isset($f->ValueRecommendation) ){
					foreach( $f->ValueRecommendation as $vr ){
						$specifics[ $i ]['values'][] = (string) $vr->Value;
					}
				}

				if( isset($f->ValidationRules) ){
					$rule = $f->ValidationRules;

					$type = isset($rule->ValueType) ? $rule->ValueType : '';

					$specifics[ $i ]['rules'] = array(
						'type' => isset($rule->ValueType) ? (string) $rule->ValueType : '',
						'maxlength' => isset($rule->MaxValues) ? (int) $rule->MaxValues : 0,
						'minlength' => isset($rule->MinValues) ? (int) $rule->MinValues : 0,
						'relations' => array()
					);

					if( isset($rule->Relationship) ){
						$relation = $rule->Relationship;
						foreach( $relation as $r ){
							$specifics[ $i ]['rules']['relations'][] = (string) $r->ParentName;
						}
					}
				}

				$i++;
			}

			$memcached->set( 'ebay:getcategoryspecifics:'.$config['tnt_id'].':'.$cat, $specifics, 900 );
			return $specifics;
		}

		/** Cette fonction permet de retourner la liste des services de livraisons proposés par eBay.
		 *	Un cache de 3 jours est mis en place sur cette fonction
		 *	@param $code Code du service eBay
		 *	@return array Un tableau contenant :
		 */
		public function getShippingServices( $code='' ){
			global $config;
			global $memcached;

			if($services = $memcached->get( 'ebay:getshippingservices:'.$config['tnt_id'] ) ){
				if( trim($code)!='' ){
					if( !isset($services[$code]) ){
						error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le code "'.$code.'" n\'existe pas chez eBay.');
						return false;
					}else{
						return $services[$code];
					}
				}

				return $services;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<GeteBayDetailsRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
					<DetailName>ShippingCarrierDetails</DetailName>
					<DetailName>ShippingServiceDetails</DetailName>
				</GeteBayDetailsRequest>
			';

			$response = EBay::sendRequest( 'GeteBayDetails', $request );
			if( !isset($response->Ack) || $response->Ack!='Success' ){
				return false;
			}

			if( !isset($response->ShippingServiceDetails) ){
				return false;
			}

			$services = array();
			foreach( $response->ShippingServiceDetails as $shipping ){
				$services[ (string) $shipping->ShippingService ] = array(
					'Description' 			=> (string) $shipping->Description,
					'ShippingService' 		=> (string) $shipping->ShippingService,
					'ShippingServiceID' 	=> (int) $shipping->ShippingServiceID,
					'ShippingTimeMax' 		=> isset($shipping->ShippingTimeMax) ? (int) $shipping->ShippingTimeMax : 0,
					'ShippingTimeMin' 		=> isset($shipping->ShippingTimeMin) ? (int) $shipping->ShippingTimeMin : 0,
					'ServiceType' 			=> (string) $shipping->ServiceType,
					'ValidForSellingFlow' 	=> isset($shipping->ValidForSellingFlow) && $shipping->ValidForSellingFlow ? true : false,
					'ExpeditedService' 		=> isset($shipping->ExpeditedService) && $shipping->ExpeditedService ? true : false,
					'InternationalService' 	=> isset($shipping->InternationalService) && $shipping->InternationalService ? true : false,
					'ShippingCategory' 		=> (string) $shipping->ShippingCategory
				);
			}

			$memcached->set( 'ebay:getshippingservices:'.$config['tnt_id'], $services, 259200 );

			if( trim($code)!='' ){
				if( !isset($services[$code]) ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le code "'.$code.'" n\'existe pas chez eBay.');
					return false;
				}else{
					return $services[$code];
				}
			}

			return $services;
		}

		/** Cette fonction permet de mettre à jour les services de livraisons
		 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		public function updateShippingServices(){
			$shipping = EBay::getShippingServices();
			if( !is_array($shipping) || !sizeof($shipping) ){
				return false;
			}

			ctr_carriers_del(CTR_EBAY);

			// Mise à jour des services de livraison d'eBay
			$ar_codes = array();
			foreach( $shipping as $code=>$info ){
				if( !ctr_carriers_add(CTR_EBAY, $code, $info['Description'], $info['ShippingTimeMax'], $info['ShippingTimeMin'], $info['InternationalService']) ){
					return false;
				}

				$ar_codes[] = $code;
			}

			// Supprime les services de livraison qui ne sont plus disponible
			$rsrv = ctr_carriers_get( CTR_EBAY );
			if( $rsrv && ria_mysql_num_rows($rsrv) ){
				while( $srv = ria_mysql_fetch_array($rsrv) ){
					if( !in_array($srv['code'], $ar_codes) ){
						if( !ctr_carriers_del(CTR_EBAY, $srv['id']) ){
							return false;
						}
					}
				}
			}

			return true;
		}

		/** Cette fonction permet de mettre à jour la famille eBay dans laquelle un produit est exporté
		 *	@param $prd_id Obligatoire, identifiant d'une catégorie
		 *	@return bool True en cas de succès, False dans le cas contraire
		 */
		public function setProductFamily( $prd_id ){
			if( !is_numeric($prd_id) || $prd_id<=0 ){
				return false;
			}

			$itemID = fld_object_values_get( array(CTR_EBAY, $prd_id), _FLD_PRD_EBAY_ID );
			if( trim($itemID) == '' ){
				return false;
			}

			$category = EBay::getExportCategory( $prd_id );
			if( trim($category) == '' ){
				return false;
			}

			$r_product = prd_products_get_simple( $prd_id );
			if( !$r_product || !ria_mysql_num_rows($r_product) ){
				return false;
			}

			$product = ria_mysql_fetch_assoc( $r_product );

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<ReviseFixedPriceItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
					<WarningLevel>High</WarningLevel>
					<Item>
						<ItemID>'.$itemID.'</ItemID>
						<PrimaryCategory>
							<CategoryID>'.$category.'</CategoryID>
						</PrimaryCategory>
			';

			$mpn = prd_suppliers_get_one_ref( $product['id'] );

			if( trim($product['brd_title']) != '' || trim($mpn) != '' ){
				$request .= '
					<ItemSpecifics>
				';

				if( trim($product['brd_title']) != '' ){
					$request .= '
							<NameValueList>
								<Name>Marque</Name>
								<Value>'.ucfirst( strtolower2($product['brd_title']) ).'</Value>
							</NameValueList>
					';
				}

				if( trim($mpn) != '' ){
					$request .= '
							<NameValueList>
								<Name>MPN</Name>
								<Value>'.$mpn.'</Value>
							</NameValueList>
					';
				}

				$request .= '
					</ItemSpecifics>
				';
			}

			if( trim($product['barcode']) != '' ){
				$request .= '
						<ProductListingDetails>
							<EAN>'.$product['barcode'].'</EAN>
						</ProductListingDetails>
				';
			}

			$request .= '
					</Item>
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
				</ReviseFixedPriceItemRequest>
			';

			$response = EBay::sendRequest( 'reviseFixedPriceItem', $request );
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			return $response;
		}

		/** Cette fonction permet de générer un identifiant de session utilisé lors de la génération du token.
		 *	@param $forced Optionnel, par défaut à false, mettre true pour forcer la récupération d'un nouvel identifiant de session
		 *	@return int L'identifiant de session ou False en cas d'erreur
		 */
		private function getSessionID( $forced=false ){
			if( !$forced ){
				$session = cfg_overrides_get_value( 'ebay_api_session' );
				if( trim($session)!='' ){
					return $session;
				}
			}

			global $config;

			$request = '
				<?xml version="1.0" encoding="utf-8" ?>
				<GetSessionIDRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<Version>'.$this->version.'</Version>
					<RuName>'.$this->runame.'</RuName>
				</GetSessionIDRequest>
			';

			$response = EBay::sendRequest( 'GetSessionID', $request );

			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			if( !cfg_overrides_set_value('ebay_api_session', $response->SessionID) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour la variable de configuration "ebay_api_session" ("'.$response->SessionID.'").');
				return false;
			}

			return (string) $response->SessionID;
		}

		/** Cette fonction permet de récupérer les catégories d'eBay
		 *	@return Les informations sur les catégories eBay
		 */
		private function getCategories(){
			$request = '
				<?xml version="1.0" encoding="utf-8" ?>
				<GetCategoriesRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
					<CategorySiteID>'.EBay::getSiteID().'</CategorySiteID>
					<LevelLimit>5</LevelLimit>
					<ViewAllNodes>true</ViewAllNodes>
					<DetailLevel>ReturnAll</DetailLevel>
					<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
				</GetCategoriesRequest>
			';

			return EBay::sendRequest( 'GetCategories', $request );
		}

		/** Cette fonction permet de récupérer l'extension pour les urls selon la langue paramétrée.
		 *	@return L'extension du site ou False en cas de problème
		 */
		private function getSiteExtension(){
			if( trim($this->country)=='' ){
				return false;
			}

			return $this->country_data[ $this->country ]['site_extension'];
		}

		/** Cette fonction permet de récupérer le code iso du pays selon la langue paramétrée.
		 *	@return Le code iso du pays ou false en cas de problème
		 */
		private function getISOCodeCountry(){
			if( trim($this->country)=='' ){
				return false;
			}

			return $this->country_data[ $this->country ]['code_iso'];
		}

		/** Cette fonction permet de récupérer la devise selon la langue paramétrée.
		 *	@return La devise utilisée ou false en cas de problème
		 */
		private function getCurrency(){
			if( trim($this->country)=='' ){
				return false;
			}

			return $this->country_data[ $this->country ]['currency'];
		}

		/** Cette fonction permet de récupérer l'identifiant du site selon la langue paramétrée.
		 *	@return int L'identifiant du site ou False en cas de problème
		 */
		private function getSiteID(){
			if( trim($this->country)=='' ){
				return false;
			}

			return $this->country_data[ $this->country ]['site_id'];
		}

		/** Cette fonction permet de récupérer le code pays à utiliser lors de l'ajout / mise à jour / désactivation d'un produit.
		 *	@return Le code paus ou False en cas de problème
		 */
		private function getCodeCountryInProductExport(){
			if( trim($this->country)=='' ){
				return false;
			}

			return $this->country_data[ $this->country ]['code_token'];
		}

		/** Cette fonction permet de récupérer la langue des messages d'erreur selon la langue paramétrée.
		 *	@return La langue des messages d'erreur ou False en cas de problème
		 */
		private function getErrorLanguage(){
			if( trim($this->country)=='' ){
				return false;
			}

			return $this->country_data[ $this->country ]['language'];
		}

		/** Cette fonction retourne le délai de préparation d'une commande (le délai de livraison est exclu)
		 *	Un cache de 30 minutes est mis en place sur cette fonction.
		 *	@return Le délai de préparation d'une commande
		 */
		private function getDispatchTimeMax(){
			global $config;
			global $memcached;

			if($param = $memcached->get( 'ebay:getdispatchtimemax:'.$config['tnt_id'] ) ){
				return $param;
			}

			$time = 0;
			$rparam = ctr_params_get( CTR_EBAY, 'DispatchTimeMax' );
			if( $rparam && ria_mysql_num_rows($rparam) ){
				$time = ria_mysql_result( $rparam, 0, 'fld' );
			}

			$memcached->set( 'ebay:getdispatchtimemax:'.$config['tnt_id'], $time, 900 );
			return $time;
		}

		/** Cette fonction retourne le code postal de la boutique.
		 *	Un cache de 30 minutes est mis en place sur cette fonction.
		 *	@return Le code postal de la boutique
		 */
		private function getShopPostalCode(){
			global $config;
			global $memcached;

			if($param = $memcached->get( 'ebay:getshoppostalcode:'.$config['tnt_id'] ) ){
				return $param;
			}

			$time = 0;
			$rparam = ctr_params_get( CTR_EBAY, 'ShopPostalCode' );
			if( $rparam && ria_mysql_num_rows($rparam) ){
				$time = ria_mysql_result( $rparam, 0, 'fld' );
			}

			$memcached->set( 'ebay:getshoppostalcode:'.$config['tnt_id'], $time, 900 );
			return $time;
		}

		/** Cette fonction retourne l'adresse mail du compte PayPal à utiliser pour le paiement.
		 *	Un cache de 30 minutes est mis en place sur cette fonction.
		 *	@return L'adresse mail du compte PayPal
		 */
		private function getPayPalEmailAddress(){
			global $config;
			global $memcached;

			if($param = $memcached->get( 'ebay:getpaypalemailaddress:'.$config['tnt_id'] ) ){
				return $param;
			}

			$time = 0;
			$rparam = ctr_params_get( CTR_EBAY, 'PayPalEmailAddress' );
			if( $rparam && ria_mysql_num_rows($rparam) ){
				$time = ria_mysql_result( $rparam, 0, 'fld' );
			}

			$memcached->set( 'ebay:getpaypalemailaddress:'.$config['tnt_id'], $time, 900 );
			return $time;
		}

		/** Cette fonction retourne les informations sur un objet d'eBay.
		 *	@param $item_id Obligatoire, identifiant de l'objet
		 *	@return Les informations sur l'objet, False si le paramètre est omis ou une erreur lors de la requête à eBay
		 */
		function getItem( $item_id ){
			global $config;

			if( trim($item_id)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant de l\'objet "'.$item_id.'" est faux.');
				return false;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<GetItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<DetailLevel>ReturnAll</DetailLevel>
					<ItemID>'.$item_id.'</ItemID>
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
				</GetItemRequest>
			';

			$response = EBay::sendRequest( 'GetItem', $request );
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			return $response;
		}

		/** Cette fonction retourne la durée de validité d'une offre choisi par le client pour ses produits.
		 *	Un cache de 30 minutes est mis en place sur cette fonction.
		 *	@param int $cat Obligatoire, identifiant de la catégorie RiaShop
		 *	@return La durée de validité d'une offre de vente
		 */
		public function getListingDuration( $cat ){
			global $config;
			global $memcached;

			if($param = $memcached->get( 'ebay:getlistingduration:'.$config['tnt_id'].':'.$cat ) ){
				if( trim($param)=='' ){
					$param = 'GTC';
				}

				return $param;
			}

			$time = false;
			$rparam = ctr_params_get( CTR_EBAY, 'ListingDuration' );
			if( $rparam && ria_mysql_num_rows($rparam) ){
				$time = ria_mysql_result( $rparam, 0, 'fld' );
			}

			$valid = 'GTC';
			if( trim($time)!='' ){
				$time = preg_replace( '/( jours| jour)/i', '', $time );
				if( is_numeric($time) && in_array($time, array(1,3,5,7,10,30)) ){
					$valid = 'Days_'.$time;
				}
			}

			// Contrôle que la durée de validité peut être utilisée, si ce n'est pas le cas alors on récupère la plus grande durée
			$listing = EBay::getCategoryFeatures( $cat, 'ListingDurations' );

			$list = false;
			if( isset($listing->Category) ){
				foreach( $listing->Category as $c ){
					if( isset($c->ListingDuration) ){
						$list = $c->ListingDuration;
					}

					if( $c->CategoryID==$cat ){
						break;
					}
				}
			}

			if( $list==false ){
				if( isset($listing->SiteDefaults->ListingDuration) ){
					$list = $listing->SiteDefaults->ListingDuration;
				}
			}

			if( !is_array($list) || !sizeof($list) ){
				return 'GTC';
			}

			$duration_set_id = 0;
			foreach( $list as $l ){
				if( $l['type']=='StoresFixedPrice' ){
					$duration_set_id = (int) $l[0];
					break;
				}
			}

			if( !$duration_set_id || !isset($listing->FeatureDefinitions->ListingDurations->ListingDuration) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune information sur la durée maximum d\'activation du produit n\'est disponible.');
				return false;
			}

			$ar_valid = array();
			foreach( $listing->FeatureDefinitions->ListingDurations->ListingDuration as $set ){
				if( ((int) $set['durationSetID'])==$duration_set_id ){
					foreach( $set->Duration as $d ){
						$ar_valid[] = (string) $d;
					}
				}
			}

			if( !in_array($valid, $ar_valid) ){
				$valid = end( $ar_valid );
			}

			if( trim($valid)=='' ){
				$valid = 'GTC';
			}

			$memcached->set( 'ebay:getlistingduration:'.$config['tnt_id'].':'.$cat, $valid, 900 );
			return $valid;
		}

		/** Cette fonction permet de récupérer le XML pour l'attribut ItemSpecifics
		 *	Un cache de 30 minutes est mis en place sur cette fonction.
		 *	@param $prd Obligatoire, identifiant d'un produit
		 *	@param int $cat Obligatoire, référence de la catégorie eBay dans laquelle le produit est exporté
		 *	@return Le code XML pour l'attribut ItemSpecifics, False en cas d'erreur
		 */
		private function getProductsItemSpecifics( $prd, $cat ){
			global $config;
			global $memcached;

			if( !is_numeric($prd) || $prd<=0 ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant du produit "'.$prd.'" est faux.');
				return false;
			}

			if( trim($cat)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La référence catégorie "'.$cat.'" est fausse.');
				return false;
			}

			if($itemSpecifics = $memcached->get( 'ebay:getproductsitemspecifics:'.$config['tnt_id'].':'.$prd.':'.$cat ) ){
				return $itemSpecifics;
			}

			$feature = EBay::getCategoryFeatures( $cat, 'ItemSpecificsEnabled' );
			if( !isset($feature->Ack) || $feature->Ack=='Failure' ){
				return false;
			}

			if( !isset($feature->SiteDefaults->ItemSpecificsEnabled) || $feature->SiteDefaults->ItemSpecificsEnabled!='Enabled' ){
				return false;
			}

			$itemSpecifics = '';

			$params = ctr_catalogs_get_params( CTR_EBAY, $prd );
			if( is_array($params) && sizeof($params) ){
				foreach( $params as $key=>$param ){
					if( $key=='opt-grp' ){
						continue;
					}

					$vals = trim($param)!='' ? explode( ';', $param ) : array();
					if( !is_array($vals) || !sizeof($vals) ){
						return false;
					}

					$itemSpecifics .= '
						<NameValueList>
							<Name><![CDATA['.$key.']]></Name>
					';

					foreach( $vals as $val ){
						$itemSpecifics .= '
							<Value><![CDATA['.$val.']]></Value>
						';
					}

					$itemSpecifics .= '
						</NameValueList>
					';
				}
			}

			$memcached->set( 'ebay:getproductsitemspecifics:'.$config['tnt_id'].':'.$prd.':'.$cat, $itemSpecifics, 900 );
			return $itemSpecifics;
		}

		/** Cette fonction permet de récupérer le XML pour la politique de retour d'un produit.
		 *	Un cache de 30 minutes est mis en place sur cette fonction.
		 *
		 *	@param $prd Obligatoire, identifiant d'un produit
		 *	@return Le code XML pour la politique de retour d'un produit, False en cas d'erreur
		 *	@todo Brancher cette fonctionnalité avec le système de retour de RiaShop.
		 */
		private function getReturnPolicy( $prd ){
			global $config;

			if( !is_numeric($prd) || $prd<=0 ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant produit "'.$prd.'" est faux.');
				return false;
			}

			// Délai de retour
			$delay = 'Days_14';
			$rdelay = ctr_params_get( CTR_EBAY, 'ReturnsWithinOption' );
			if( $rdelay && ria_mysql_num_rows($rdelay) ){
				$d = ria_mysql_result( $rdelay, 0, 'fld' );
				switch( $d ){
					case '14 jours' :
						$delay = 'Days_14';
						break;
					case '30 jours' :
						$delay = 'Days_30';
						break;
					case '60 jours' :
						$delay = 'Days_60';
						break;
					case '1 mois' :
						$delay = 'Months_1';
						break;
				}
			}

			// Partie responsable des frais de retour, par défaut il s'agit de l'acheteur
			$opt = 'Buyer';
			$ropt = ctr_params_get( CTR_EBAY, 'ShippingCostPaidByOption' );
			if( $ropt && ria_mysql_num_rows($ropt) ){
				$o = ria_mysql_result( $ropt, 0, 'fld' );
				switch( $d ){
					case 'Acheteur' :
						$opt = 'Buyer';
						break;
					case 'Vendeur' :
						$opt = 'Seller';
						break;
				}
			}

			// Description des messages de retour
			$desc = '';
			$rdesc = ctr_params_get( CTR_EBAY, 'return_policy' );
			if( $rdesc && ria_mysql_num_rows($rdesc) ){
				$desc = ria_mysql_result( $rdesc, 0, 'fld' );
			}

			$xml = '
				<ReturnPolicy>
					<ReturnsAcceptedOption>ReturnsAccepted</ReturnsAcceptedOption>
					<ReturnsWithinOption>'.$delay.'</ReturnsWithinOption>
					<ShippingCostPaidByOption>'.$opt.'</ShippingCostPaidByOption>
					<Description>'.$desc.'</Description>
				</ReturnPolicy>
			';

			return $xml;
		}

		/** Cette fonction permet de savoir si une catégorie accepte l'export des déclinaisons
		 *	Un cache de 30 minutes est mis en place sur cette fonction.
		 *	@param int $cat Obligatoire, identifiant de la catégorie eBay où le produit est exporté (référence de la catégorie dans RiaShop)
		 *	@return bool True si les déclinaisons sont acceptées, False dans le cas contraire
		 */
		public function getAcceptedMultiSKU( $cat ){
			global $config;
			global $memcached;

			if( trim($cat)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La référence catégorie "'.$cat.'" est fausse.');
				return false;
			}

			if($t = $memcached->get( 'ebay:getacceptedmultisku:'.$config['tnt_id'].':'.$cat ) ){
				return $t;
			}

			$feature = EBay::getCategoryFeatures( $cat, 'VariationsEnabled' );
			if( !isset($feature->Ack) || $feature->Ack=='Failure' ){
				return false;
			}

			$accept_multi_sku = false;
			if( isset($feature->Category) ){
				foreach( $feature->Category as $c ){
					if( $c->VariationsEnabled=='true' ){
						$accept_multi_sku = true;
					}
				}
			}elseif( isset($feature->SiteDefaults->VariationsEnabled) ){
				if( $feature->SiteDefaults->VariationsEnabled=='true' ){
					$accept_multi_sku = true;
				}
			}

			$memcached->set( 'ebay:getacceptedmultisku:'.$config['tnt_id'].':'.$cat, $accept_multi_sku ? 'true' : 'false', 900 );
			return $accept_multi_sku ? 'true' : 'false';
		}

		/** Cette fonction permet de récupérer toutes la partie des variations
		 *
		 *	TODO : Peut être faudra t-il rajouter un cache de 30 minutes ???
		 *	TODO : Peut être faudra t-il modifier la fonction pour les mises à jour ou réactivation de produit ???
		 *
		 *	@param $prd Obligatoire, identifiant du produit parent
		 *	@param int $cat Obligatoire, identifiant de la catégorie eBay où le produit est exporté (référence de la catégorie dans RiaShop) : permet de contrôler que la catégorie accepte bien les variations
		 *	@return array Un tableau contenant :
		 *				- [xml] : Le code XML des variations
		 *				- [childs] : Un tableau contenant pour identifiant produit enfant enfant : le prix et la quantité exporté. Exemple [prd_id] => array( 'price' => '', 'qty' => '' )
		 */
		private function getVariations( $prd, $cat ){
			global $config;

			if( !is_numeric($prd) || $prd<=0 ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant produit "'.$prd.'" est faux.');
				return false;
			}

			if( trim($cat)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La référence catégorie "'.$cat.'" est fausse.');
				return false;
			}

			if( EBay::getAcceptedMultiSKU($cat)=='false' ){
				return false;
			}

			$params = ctr_catalogs_get_params( CTR_EBAY, $prd );
			if( !isset($params['opt-grp']) || !is_array($params['opt-grp']) || !sizeof($params['opt-grp']) ){
				// error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Les paramètres de déclinaisons ne sont pas défini pour le produit "'.$prd.'".');
				return false;
			}

			$rchild = prd_products_get_simple( 0, '', false, 0, false, false, false, false, array('have_stock'=>true, 'childs'=>true, 'parent'=>$prd) );
			if( !$rchild || !ria_mysql_num_rows($rchild) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucun produit enfant n\'est disponible pour le parent : '.$prd.'.');
				return false;
			}

			// Initialisation des variables
			$ar_data = array();
			$ar_specifics = array();
			$ar_images = array();
			$ar_childs = array();

			while( $child = ria_mysql_fetch_array($rchild) ){
				// Vérifier que le produit enfant est exportable vers eBay
				if( !ctr_catalogs_is_publish(CTR_EBAY, $child['id']) ){
					continue;
				}

				$rprice = prd_products_get_price( $child['id'], $this->user_id );
				if( !$rprice || !ria_mysql_num_rows($rprice) ){
					continue;
				}else{
					$price = ria_mysql_fetch_array( $rprice );

					$child['price_ht'] = $price['price_ht'];
					$child['price_ttc'] = $price['price_ttc'];
				}

				$old_price = 0;
				$pmt = prc_promotions_get( $child['id'], $this->user_id, 0, 1, 0, array('price_ht' => $child['price_ht'], 'tva_rate' => $child['tva_rate']) );
				if( isset($pmt['price_ht'], $pmt['price_ttc']) ){
					$old_price = $child['price_ttc'];
					$child['price_ht'] = $pmt['price_ht'];
					$child['price_ttc'] = $pmt['price_ttc'];
				}

				// Configuration des images à utiliser lors de l'export
				$thumb = $config['img_sizes']['rueducommerce'];

				$ar_img_url = array();
				if( $child['img_id'] ){
					$ar_img_url[] = $config['site_url'].'/images/products/'.$thumb['width'].'x'.$thumb['height'].'/'.$child['img_id'].'.'.$thumb['format'];
				}

				// Récupère les images secondaire du produit
				$rimg = prd_images_get( $child['id'] );
				if( $rimg && ria_mysql_num_rows($rimg) ){
					while( $img = ria_mysql_fetch_array($rimg) ){
						if( $img['id']==$child['img_id'] ){
							continue;
						}

						$ar_img_url[] = $config['site_url'].'/images/products/'.$thumb['width'].'x'.$thumb['height'].'/'.$img['id'].'.'.$thumb['format'];
					}
				}

				$ar_data[ $child['ref'] ] = array();

				// récupère la valeur des paramètres
				$ar_data[ $child['ref'] ]['options'] = array();
				foreach( $params['opt-grp'] as $key=>$opt ){
					$name = $value = '';
					switch( $key ){
						case 'weight' :
							if( is_numeric($child['weight']) && $child['weight']>0 ){
								$name = 'Poids brut';
								$value = $child['weight']>=1000 ? ($child['weight']/1000).' kg' : $child['weight'].' g';
							}
							break;
						case 'weight_net' :
							if( is_numeric($child['weight_net']) && $child['weight_net']>0 ){
								$name = 'Poids net';
								$value = $child['weight_net']>=1000 ? ($child['weight_net']/1000).' kg' : $child['weight_net'].' g';
							}
							break;
						default :
							$v = fld_object_values_get( array(CTR_EBAY, $child['id']), $key );
							if( trim($v)!='' ){
								$name = fld_fields_get_name( $key );
								$value = $v;
							}
							break;
					}

					if( trim($name)!='' && trim($value)!='' ){
						if( !isset($ar_specifics[$key]) ){
							$ar_specifics[$key] = array( 'name' => $name, 'values' => array() );
						}

						$ar_specifics[$key]['values'][] = $value;
						$ar_data[ $child['ref'] ]['options'][] = array( 'name'=>$name, 'value'=> $value );

						if( !isset($ar_images[$name][$value]) ){
							$ar_images[$name][$value] = $ar_img_url;
						}
					}
				}

				$ar_data[ $child['ref'] ]['price'] 	= number_format( $child['price_ttc'], 2, '.', '' );
				$ar_data[ $child['ref'] ]['qty'] 	= $child['stock'];
				$ar_data[ $child['ref'] ]['old_price'] = $old_price;

				$ar_childs[ $child['id'] ] = array( 'price' => $child['price_ht'], 'qty' => $child['stock'] );
			}

			if( !sizeof($ar_specifics) ){
				return false;
			}

			$xml = '
				<Variations>
					<VariationSpecificsSet>
			';

			foreach( $ar_specifics as $spe ){
				$xml .= '
						<NameValueList>
							<Name>'.$spe['name'].'</Name>
				';

				$values = array_unique( $spe['values'] );
				foreach( $values as $v ){
					$xml .= '
							<Value>'.$v.'</Value>
					';
				}

				$xml .= '
						</NameValueList>
				';
			}

			$xml .= '
					</VariationSpecificsSet>
			';

			foreach( $ar_data as $sku=>$info ){
				$xml .= '
					<Variation>
						<SKU>'.$sku.'</SKU>
						<StartPrice>'.$info['price'].'</StartPrice>
				';

				if( is_numeric($info['old_price']) && $info['old_price']>0 ){
					$xml .= '
						<DiscountPriceInfo> DiscountPriceInfoType
							<MinimumAdvertisedPriceExposure>DuringCheckout</MinimumAdvertisedPriceExposure>
							<OriginalRetailPrice>'.number_format( $info['old_price'], 2, '.', '' ).'</OriginalRetailPrice>
						</DiscountPriceInfo>
					';
				}

				$xml .= '
						<Quantity>'.$info['qty'].'</Quantity>
						<VariationSpecifics>
				';

				foreach( $info['options'] as $opt ){
					$xml .= '
							<NameValueList>
								<Name>'.$opt['name'].'</Name>
								<Value>'.$opt['value'].'</Value>
							</NameValueList>
					';
				}

				$xml .= '
						</VariationSpecifics>
					</Variation>
				';
			}

			if( sizeof($ar_images) ){
				foreach( $ar_images as $key=>$img_val ){
					$xml .= '
						<Pictures>
							<VariationSpecificName>'.$key.'</VariationSpecificName>
					';

					foreach( $img_val as $key=>$imgs ){
						$xml .= '
							<VariationSpecificPictureSet>
								<VariationSpecificValue>'.$key.'</VariationSpecificValue>
						';

						$i = 1;
						foreach( $imgs as $img ){
							if( $i > 12 ){
								break;
							}

							$xml .= '
								<PictureURL>'.$img.'</PictureURL>
							';

							$i++;
						}

						$xml .= '
							</VariationSpecificPictureSet>
						';
					}

					$xml .= '
						</Pictures>
					';
				}
			}

			$xml .= '
				</Variations>
			';

			return array( 'xml' => $xml, 'childs' => $ar_childs );
		}

		/** Cette fonction permet de récupérer le détail des services de livraison pour un produit (où produits enfants)
		 *	@param $prd Obligatoire, identifiant d'un produit
		 *	@return bool False en cas d'erreur, sinon le code XML des services de livraison
		 */
		private function getShippingDetails( $prd, $min_price=0 ){
			global $config;

			if( !isset($config['ebay_carriers']) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La variable de configuration "ebay_carriers" n\'est pas définie.');
				return false;
			}

			$carrier = json_decode( $config['ebay_carriers'], true );
			if( !is_array($carrier) || !sizeof($carrier) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune configuration de livraison n\'est renseignée.');
				return false;
			}

			$ar_unavailable = array();

			$rsrv = dlv_products_unavailable_get( $prd );
			if( $rsrv && ria_mysql_num_rows($rsrv) ){
				while( $srv = ria_mysql_fetch_array($rsrv) ){
					$ar_unavailable[] = $srv['srv_id'];
				}
			}

			$rchild = prd_products_get_simple( 0, '', false, 0, false, false, false, false, array('childs'=>true, 'parent'=>$prd) );
			if( $rchild && ria_mysql_num_rows($rchild) ){
				while( $child = ria_mysql_fetch_array($rchild) ){
					$rsrv = dlv_products_unavailable_get( $child['id'] );
					if( $rsrv && ria_mysql_num_rows($rsrv) ){
						while( $srv = ria_mysql_fetch_array($rsrv) ){
							$ar_unavailable[] = $srv['srv_id'];
						}
					}
				}
			}

			$ar_unavailable = array_unique( $ar_unavailable );

			$ar_unique = array();
			$ar_shipping = array();

			foreach( $carrier as $srv=>$info ){
				if( in_array($srv, $ar_unavailable) ){
					continue;
				}

				$rsrv = dlv_services_get( $srv );
				if( !$rsrv || !ria_mysql_num_rows($rsrv) ){
					continue;
				}

				$srv = ria_mysql_fetch_array( $rsrv );

				$ebay_srv = EBay::getShippingServices( $info['carrier'] );
				if( !is_array($ebay_srv) ){
					continue;
				}

				if( in_array($ebay_srv['ShippingService'], $ar_unique) ){
					continue;
				}

				$ar_unique[] = $ebay_srv['ShippingService'];

				$free = false;
				if( $srv['dealer-free-ht']>0 && $min_price>=($srv['dealer-free-ht']*_TVA_RATE_DEFAULT) ){
					$free = true;
				}

				$ar_shipping[] = array(
					'service' => $ebay_srv['ShippingService'],
					'free' => ( $free ? 'true' : 'false' ),
					'price' => !$free ? number_format( $srv['price-ttc'], 2, '.', '' ) : 0,
					'cost' => !$free ? ($info['cost-supp']>0 ? number_format( $info['cost-supp'], 2, '.', '' ) :0) : 0
				);
			}

			$ar_shipping = array_msort( $ar_shipping, array('price'=>SORT_ASC) );

			$xml = ''; $i = 1;
			foreach( $ar_shipping as $shipping ){
				if( $i>5 ){
					break;
				}

				$xml .= '
					<ShippingServiceOptions>
						<ShippingServicePriority>'.$i.'</ShippingServicePriority>
						<ShippingService>'.$shipping['service'].'</ShippingService>
						<FreeShipping>'.$shipping['free'].'</FreeShipping>
						<ShippingServiceCost currencyID="'.EBay::getCurrency().'">'.$shipping['price'].'</ShippingServiceCost>
						<ShippingServiceAdditionalCost>'.$shipping['cost'].'</ShippingServiceAdditionalCost>
					</ShippingServiceOptions>
				';

				$i++;
			}

			if( trim($xml)=='' ){
				return false;
			}

			return $xml;
		}

		/** Cette fonction permet de désactiver la vente d'un produit
		 *	@param $item_id Obligatoire, identifiant d'un objet chez eBay
		 *	@param $prd Obligatoire, identifant du produit
		 *	@return bool True si la désactivation s'est correctement déroulée, False dans le cas contraire
		 */
		private function endFixedPriceItem( $item_id, $prd ){
			global $config;

			if( trim($item_id)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant de l\'objet "'.$item_id.'" est faux.');
				return false;
			}

			// On contrôle que le produit n'est pas déjà désactivée
			$date_disabled = fld_object_values_get( array(CTR_EBAY, $prd), _FLD_PRD_EBAY_DISABLED );
			if( trim($date_disabled)!='' ){
				return true;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<EndFixedPriceItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
					<WarningLevel>High</WarningLevel>
					<ItemID>'.$item_id.'</ItemID>
					<EndingReason>NotAvailable</EndingReason>
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
					<WarningLevel>High</WarningLevel>
				</EndFixedPriceItemRequest>
			';

			$response = EBay::sendRequest( 'EndFixedPriceItem', $request, false, $prd );
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			// Mise à jour de la date de désactivation du produit
			if( !fld_object_values_set(array(CTR_EBAY, $prd), _FLD_PRD_EBAY_DISABLED, date('Y-m-d H:i:s')) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour la date de désactivation du produit "'.$prd.'" : '.date('Y-m-d H:i:s').'.');
				return false;
			}

			return true;
		}

		/** Cette fonction permet de désactiver la vente d'une déclinaison d'un produit (un produit enfant).
		 *	@param $item_id Obligatoire, identifiant d'un objet eBay représentant le produit parent
		 *	@param $parent Obligatoire, identifiant du produit parent
		 *	@param string $ref Obligatoire, référence de la déclinaison (produit enfant)
		 *	@return bool True si la désactivation s'est correctement déroulée, False dans le cas contraire
		 */
		private function endFixedPriceChild( $item_id, $parent, $ref ){
			global $config;

			if( trim($item_id)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant de l\'objet "'.$item_id.'" est faux.');
				return false;
			}

			if( !is_numeric($parent) || $parent<=0 ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant du parent "'.$parent.'" est faux.');
				return false;
			}

			if( trim($ref)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La référence de la déclinaison "'.$ref.'" est fausse.');
				return false;
			}

			// Récupère les informations sur le produit parent chez eBay
			$info = EBay::getItem( $item_id );
			if( $info===false || !isset($info->Item->Variations->Variation) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de récupérer les déclinaisons du parent "'.$item_id.'" directement chez eBay.');
				return false;
			}

			// S'il ne reste qu'une seul déclinaison sur eBay
			if( sizeof($info->Item->Variations->Variation)==1 ){
				$variation = $info->Item->Variations->Variation;
				if( !isset($variation->SKU) || $variation->SKU!=$ref ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La déclinaison restante chez eBay ne corresponds pas au produit à désactiver "'.$ref.'".');
					return false;
				}

				// On désactive le produit parent
				if( !EBay::endFixedPriceItem($item_id, $parent) ){
					return false;
				}

				return true;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<ReviseFixedPriceItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
					<WarningLevel>High</WarningLevel>
					<Item>
						<ItemID>'.$item_id.'</ItemID>
						<Variations>
							<Variation>
								<Delete>true</Delete>
								<SKU>'.$ref.'</SKU>
							</Variation>
						</Variations>
					</Item>
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
				</ReviseFixedPriceItemRequest>
			';

			$response = EBay::sendRequest( 'ReviseFixedPriceItem', $request );
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			return true;
		}

		/** Cette fonction permet de récupérer l'identifiant de la catégorie d'export d'un produit.
		 *	@param $prd Obligatoire, identifiant d'un produit
		 *	@return bool False si l'identifiant du produit est faux ou qu'il n'est exporté dans aucune catégorie
		 *	@return int L'identifiant de la catégorie d'export
		 */
		private function getExportCategory( $prd ){
			global $config;

			if( !is_numeric($prd) || $prd<=0 ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant produit "'.$prd.'" est faux.');
				return false;
			}

			$category = false;

			$rcat = ctr_catalogs_get_categorie( CTR_EBAY, $prd, false, true );
			if( is_numeric($rcat) && $rcat>0 ){
				$category = ctr_categories_get_ref( CTR_EBAY, $rcat, true );
			}

			return $category;
		}

		/** Cette fonction permet de récupérer toutes les informations permettant de gérer l'export des produits vers eBay.
		 *	@param $action Obligatoire, action réalisé sur le produit, peut contenir : AddFixedPriceItem ou ReviseFixedPriceItem
		 *	@param $prd Obligatoire, identifiant du produit ou directement le résultat d'un ria_mysql_fetch_array(prd_products_get_simple($prd))
		 *	@param $category Optionnel, catégorie eBay vers laquelle le produit est exporté
		 *	@param $variation Optionnel, par défaut les déclinaisons de produit ne seront pas récupérées, mettre true pour qu'elles le soient
		 *	@return -1 Dans le cas où l'on souhaite exporter des déclinaison, mais aucune n'est exportable pour le moment
		 *	@return bool False si une erreur s'est produite sinon un tableau contenant toutes les informations pour l'export
		 */
		public function getDataForExportProduct( $action, $prd, $category='', $variation=false ){
			global $config;

			if( !in_array($action, array('AddFixedPriceItem', 'ReviseFixedPriceItem')) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'action passée en paramètre n\'existe pas.');
				return false;
			}

			if( is_numeric($prd) ){
				if( !prd_products_exists($prd) ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le produit passé en paramètre n\'existe plus.');
					return false;
				}

				$prd = ria_mysql_fetch_array( prd_products_get_simple($prd) );
			}

			// Si on souhaite exporté les déclinaisons, on regarde s'il existe des déclinaisons publiés
			if( $variation ){
				$rchilds = prd_products_get_simple( 0, '', false, 0, false, false, false, false, array('have_stock'=>true, 'orderable'=>true, 'childs'=>true, 'parent'=>$prd['id']) );
				if( !$rchilds ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Une erreur s\'est produite lors de la récupération des produits enfant du parent "'.$prd['id'].'".');
					return false;
				}else{
					if( !ria_mysql_num_rows($rchilds) ){
						return -1;
					}
				}
			}

			// Configuration des images à utiliser lors de l'export
			$thumb = $config['img_sizes']['rueducommerce'];

			// Récupère le titre
			$title = ctr_catalogs_get_prd_title( CTR_EBAY, $prd['id'], false, true );

			// Récupère la description
			$desc = ctr_catalogs_get_prd_desc( CTR_EBAY, $prd['id'], false, true );

			// Récupère l'image principal du produit
			$ar_img_url = array();
			if( $prd['img_id'] ){
				$ar_img_url[] = $config['site_url'].'/images/products/'.$thumb['width'].'x'.$thumb['height'].'/'.$prd['img_id'].'.'.$thumb['format'];
			}

			// Récupère les images secondaire du produit
			$rimg = prd_images_get( $prd['id'] );
			if( $rimg && ria_mysql_num_rows($rimg) ){
				while( $img = ria_mysql_fetch_array($rimg) ){
					if( $img['id']==$prd['img_id'] ){
						continue;
					}

					$ar_img_url[] = $config['site_url'].'/images/products/'.$thumb['width'].'x'.$thumb['height'].'/'.$img['id'].'.'.$thumb['format'];
				}
			}

			// Catégorie d'export du produit
			if( trim($category)=='' ){
				$category = EBay::getExportCategory( $prd['id'] );
				if( !$category || trim($category)=='' ){
					// error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune catégorie eBay n\'est définie pour l\'ajout / mise à jour du produit "'.$prd['id'].'".');
					return false;
				}
			}

			$rprice = prd_products_get_price( $prd['id'], $this->user_id );
			if( !$rprice || !ria_mysql_num_rows($rprice) ){
				error_log( '[EBay - Tenant '.$config['tnt_id'].'] Produit '.$prd['id'].', impossible de récupérer le tarif de ce produit' );
				return false;
			}

			$price = ria_mysql_fetch_array( $rprice );

			$min_price = $price['price_ttc'];
			if( $variation ){
				$rprice = prd_products_get_child_min_price( $prd['id'] );
				if( !$rprice || !ria_mysql_num_rows($rprice) ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de récupérer le plus petit prix des déclinaisons du produit "'.$prd['id'].'".');
					return false;
				}

				$min_price = ria_mysql_result( $rprice, 0, 'price_ttc' );
			}

			// Détails des services de livraisons
			$shipping = EBay::getShippingDetails( $prd['id'], $min_price );
			if( !$shipping ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de récupérer le détail des services de livraison.');
				return false;
			}

			// Déclinaison du produit
			if( $variation ){
				$v = EBay::getVariations( $prd['id'], $category );
				if( !is_array($v) || !sizeof($v) ){
					// error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune déclinaison n\'existe pour le produit "'.$prd['id'].'".');
					return false;
				}
			}

			switch( $action ){
				case 'AddFixedPriceItem' :
				{
					$data = array(
						'id' => $prd['id'],
						'sku' => $prd['ref'],
						'title' => $title,
						'desc' => $desc,
						'brand' => ucfirst( strtolower2($prd['brd_title']) ),
						'mpn' => prd_suppliers_get_one_ref( $prd['id'] ),
						'barcode' => $prd['barcode'],
						'pictures' => $ar_img_url,
						'category' => $category,
						'item_specifics' => EBay::getProductsItemSpecifics( $prd['id'], $category ),
						'return_policy' => EBay::getReturnPolicy( $prd['id'] ),
						'listing_duration' => EBay::getListingDuration( $category ),
						'shipping_details' => $shipping
					);

					if( $variation ){
						$data['variations'] = $v['xml'];
						$data['childs'] = $v['childs'];
					}else{
						$pmt = prc_promotions_get( $prd['id'], $this->user_id, 0, 1, 0, array('price_ht' => $price['price_ht'], 'tva_rate' => $price['tva_rate']) );

						$old_price = $price['price_ttc'];
						if( isset($pmt['price_ht'], $pmt['price_ttc']) ){
							$price['price_ht'] = $pmt['price_ht'];
							$price['price_ttc'] = $pmt['price_ttc'];
						}else{
							unset($pmt);
						}

						$data['start_price'] = number_format( $price['price_ttc'], 2, '.', '' );
						$data['price_ht'] = $price['price_ht'];
						$data['quantity'] = $prd['stock'];

						if( isset($pmt) ){
							$data['discount'] = '
								<MinimumAdvertisedPriceExposure>DuringCheckout</MinimumAdvertisedPriceExposure>
								<OriginalRetailPrice>'.number_format( $old_price, 2, '.', '' ).'</OriginalRetailPrice>
							';
						}
					}

					break;
				}
				case 'ReviseFixedPriceItem' :
				{
					$data = array(
						'id' => $prd['id'],
						'sku' => $prd['ref'],
						'title' => $title,
						'desc' => $desc,
						'brand' => ucfirst( strtolower2($prd['brd_title']) ),
						'mpn' => prd_suppliers_get_one_ref( $prd['id'] ),
						'barcode' => $prd['barcode'],
						'pictures' => $ar_img_url,
						'category' => $category,
						'item_specifics' => EBay::getProductsItemSpecifics( $prd['id'], $category ),
						'return_policy' => EBay::getReturnPolicy( $prd['id'] ),
						'listing_duration' => EBay::getListingDuration( $category ),
						'shipping_details' => $shipping
					);

					if( $variation ){
						$data['variations'] = $v['xml'];
						$data['childs'] = $v['childs'];
					}else{
						$pmt = prc_promotions_get( $prd['id'], $this->user_id, 0, 1, 0, array('price_ht' => $price['price_ht'], 'tva_rate' => $price['tva_rate']) );

						$old_price = $price['price_ttc'];
						if( isset($pmt['price_ht'], $pmt['price_ttc']) ){
							$price['price_ht'] = $pmt['price_ht'];
							$price['price_ttc'] = $pmt['price_ttc'];
						}else{
							unset($pmt);
						}

						$data['start_price'] = number_format( $price['price_ttc'], 2, '.', '' );
						$data['price_ht'] = $price['price_ht'];
						$data['quantity'] = $prd['stock'];

						if( isset($pmt) ){
							$data['discount'] = '
								<MinimumAdvertisedPriceExposure>DuringCheckout</MinimumAdvertisedPriceExposure>
								<OriginalRetailPrice>'.number_format( $old_price, 2, '.', '' ).'</OriginalRetailPrice>
							';
						}
					}

					break;
				}
			}

			return $data;
		}

		/** Cette fonction permet de générer le code pour l'ajout d'un produit
		 *	@param $data Obligatoire, tableau contenant toutes les informations à utiliser pour l'ajout d'un produit sur eBay
		 *	@return bool False en cas d'erreur, le code XML d'ajout d'un produit dans le cas contraire
		 */
		private function addFixedPriceItem( $data ){
			global $config;

			if( !is_array($data) || !sizeof($data) ){
				// error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune donnée n\'est renseignée pour l\'ajout d\'un produit.');
				return false;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<AddFixedPriceItem xmlns="urn:ebay:apis:eBLBaseComponents">
					<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
					<WarningLevel>High</WarningLevel>
					<Item>
						<SKU>'.$data['sku'].'</SKU>
						<Title><![CDATA['.$data['title'].']]></Title>
			';

			if( sizeof($data['pictures']) ){
				$request .= '
							<PictureDetails>
								<GalleryType>Gallery</GalleryType>
								'.( sizeof($data['pictures']>1) ? '<PhotoDisplay>PicturePack</PhotoDisplay>' : '' ).'
				';

				$i = 1;
				foreach( $data['pictures'] as $picture ){
					if( $i > 12 ){
						break;
					}

					$request .= '
								<PictureURL>'.$picture.'</PictureURL>
					';

					$i++;
				}

				$request .= '
							</PictureDetails>
				';
			}

			$request .= '
						<Description><![CDATA['.$data['desc'].']]></Description>
						<PrimaryCategory>
							<CategoryID>'.$data['category'].'</CategoryID>
						</PrimaryCategory>
						<ConditionID>1000</ConditionID>
						'.( isset($data['start_price']) ? '<StartPrice>'.$data['start_price'].'</StartPrice>' : '' ).'
						'.( isset($data['discount']) ? '<DiscountPriceInfo>'.$data['discount'].'</DiscountPriceInfo>' : '' ).'
						<CategoryMappingAllowed>true</CategoryMappingAllowed>
						<Country>'.EBay::getISOCodeCountry().'</Country>
						<Currency>'.EBay::getCurrency().'</Currency>
						<DispatchTimeMax>'.EBay::getDispatchTimeMax().'</DispatchTimeMax>
						<ListingDuration>'.$data['listing_duration'].'</ListingDuration>
						<ListingType>StoresFixedPrice</ListingType>
						<PaymentMethods>PayPal</PaymentMethods>
						<PayPalEmailAddress>'.EBay::getPayPalEmailAddress().'</PayPalEmailAddress>
						<PostalCode>'.EBay::getShopPostalCode().'</PostalCode>
						'.( isset($data['quantity']) ? '<Quantity>'.$data['quantity'].'</Quantity>' : '' ).'
						<ItemSpecifics>
							'.$data['item_specifics'].'
			';

			if( trim($data['brand']) != '' ){
				$request .= '
							<NameValueList>
								<Name>Marque</Name>
								<Value>'.$data['brand'].'</Value>
							</NameValueList>
				';
			}

			if( trim($data['mpn']) != '' ){
				$request .= '
							<NameValueList>
								<Name>MPN</Name>
								<Value>'.$data['mpn'].'</Value>
							</NameValueList>
				';
			}

			$request .= '
						</ItemSpecifics>
						'.$data['return_policy'].'
						'.( isset($data['variations']) ? $data['variations'] : '' ).'
						<ShippingDetails>'.$data['shipping_details'].'</ShippingDetails>
						<Site>'.EBay::getCodeCountryInProductExport().'</Site>
			';

			if( trim($data['barcode']) != '' ){
				$request .= '
						<ProductListingDetails>
							<EAN>'.$data['barcode'].'</EAN>
						</ProductListingDetails>
				';
			}

			$request .= '
					</Item>
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
				</AddFixedPriceItem>
			';

			$response = EBay::sendRequest( 'AddFixedPriceItem', $request );
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			if( !isset($response->ItemID) ){
				return false;
			}

			if( !fld_object_values_set(array(CTR_EBAY, $data['id']), _FLD_PRD_EBAY_ID, (string) $response->ItemID) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour l\'identifiant d\'objet "'.$response->ItemID.'" pour le produit "'.$data['id'].'".');
				return false;
			}

			if( isset($response->EndTime) ){
				$date_end = date( 'Y-m-d H:i:s', strtotime((string)$response->EndTime) );
				if( !fld_object_values_set(array(CTR_EBAY, $data['id']), _FLD_PRD_EBAY_END, $date_end) ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour la date de fin "'.$date_end.'" de validité du produit "'.$data['id'].'".');
					return false;
				}
			}

			$fees = array();
			foreach( $response->Fees->Fee as $fee ){
				$fees[ (string) $fee->Name ] = (float) $fee->Fee;
			}

			if( !fld_object_values_set(array(CTR_EBAY, $data['id']), _FLD_PRD_EBAY_FEES, json_encode($fees)) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour les frais de mise en vente du produit "'.$data['id'].'".');
				return false;
			}

			// Enregistre le tarif et le stock lors de son export
			if( isset($data['start_price'], $data['quantity']) ){
				ctr_catalogs_update_price( CTR_EBAY, $data['id'], $data['price_ht'] );
				ctr_catalogs_update_quantity( CTR_EBAY, $data['id'], $data['quantity'] );
			}elseif( $data['childs'] ){
				foreach( $data['childs'] as $id=>$info ){
					ctr_catalogs_update_price( CTR_EBAY, $id, $info['price'] );
					ctr_catalogs_update_quantity( CTR_EBAY, $id, $info['qty'] );
				}
			}

			return true;
		}

		/** Cette fonction permet de mettre à jour les informations sur un produit déjà exporté
		 *	@param $item_id Obligatoire, identifiant de l'objet chez eBay représentant le produit
		 *	@param $data Obligatoire, tableau contenant les informations pour la mise à jour du produit chez eBay
		 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		private function reviseFixedPriceItem( $item_id, $data ){
			global $config;

			if( trim($item_id)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant de l\'objet "'.$item_id.'" est faux.');
				return false;
			}

			if( !is_array($data) || !sizeof($data) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune donnée n\'est renseignée pour la mise à jour du produit.');
				return false;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<ReviseFixedPriceItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
					<WarningLevel>High</WarningLevel>
					<Item>
						<ItemID>'.$item_id.'</ItemID>
			';

			if( sizeof($data['pictures']) ){
				$request .= '
							<PictureDetails>
								<GalleryType>Gallery</GalleryType>
								'.( sizeof($data['pictures']>1) ? '<PhotoDisplay>PicturePack</PhotoDisplay>' : '' ).'
				';

				$i = 1;
				foreach( $data['pictures'] as $picture ){
					if( $i > 12 ){
						break;
					}

					$request .= '
								<PictureURL>'.$picture.'</PictureURL>
					';

					$i++;
				}

				$request .= '
							</PictureDetails>
				';
			}

			$request .= '
						<DispatchTimeMax>'.EBay::getDispatchTimeMax().'</DispatchTimeMax>
						<ListingDuration>'.$data['listing_duration'].'</ListingDuration>
						'.( isset($data['quantity']) ? '<Quantity>'.$data['quantity'].'</Quantity>' : '' ).'
						'.( isset($data['start_price']) ? '<StartPrice>'.$data['start_price'].'</StartPrice>' : '' ).'
						'.( isset($data['discount']) ? '<DiscountPriceInfo>'.$data['discount'].'</DiscountPriceInfo>' : '' ).'
						<Title><![CDATA['.$data['title'].']]></Title>
						<Description><![CDATA['.$data['desc'].']]></Description>
						<ShippingDetails>'.$data['shipping_details'].'</ShippingDetails>
						<ItemSpecifics>
							'.$data['item_specifics'].'
			';

			if( trim($data['brand']) != '' ){
				$request .= '
							<NameValueList>
								<Name>Marque</Name>
								<Value>'.$data['brand'].'</Value>
							</NameValueList>
				';
			}

			if( trim($data['mpn']) != '' ){
				$request .= '
							<NameValueList>
								<Name>MPN</Name>
								<Value>'.$data['mpn'].'</Value>
							</NameValueList>
				';
			}

			$request .= '
						</ItemSpecifics>
						'.$data['return_policy'].'
						'.( isset($data['variations']) ? $data['variations'] : '' ).'
			';

			if( trim($data['barcode']) != '' ){
				$request .= '
						<ProductListingDetails>
							<EAN>'.$data['barcode'].'</EAN>
						</ProductListingDetails>
				';
			}

			$request .= '
					</Item>
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
				</ReviseFixedPriceItemRequest>
			';

			$response = EBay::sendRequest( 'ReviseFixedPriceItem', $request );
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				if( isset($response->Errors) ){
					foreach( $response->Errors as $e ){
						if( $e->ErrorCode==291 ){
							// Si la quantité présente sur eBay a toute été vendue, alors on doit relister le produit
							return EBay::relistFixedPriceItem( $item_id, $data['id'] );
						}
					}
				}

				return false;
			}

			if( isset($response->EndTime) ){
				$date_end = date( 'Y-m-d H:i:s', strtotime((string)$response->EndTime) );
				if( !fld_object_values_set(array(CTR_EBAY, $data['id']), _FLD_PRD_EBAY_END, $date_end) ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour la date de fin "'.$date_end.'" de validité du produit "'.$data['id'].'".');
					return false;
				}
			}

			// Enregistre le tarif et le stock lors de son export
			if( isset($data['start_price'], $data['quantity']) ){
				ctr_catalogs_update_price( CTR_EBAY, $data['id'], $data['price_ht'] );
				ctr_catalogs_update_quantity( CTR_EBAY, $data['id'], $data['quantity'] );
			}elseif( $data['childs'] ){
				foreach( $data['childs'] as $id=>$info ){
					ctr_catalogs_update_price( CTR_EBAY, $id, $info['price'] );
					ctr_catalogs_update_quantity( CTR_EBAY, $id, $info['qty'] );
				}
			}

			return true;
		}

		/** Cette fonction permet de mettre à jour les déclinaisons d'un produit parent
		 *	@param $item_id Obligatoire, identifiant de l'objet eBay représentant le parent
		 *	@param $parent Obligatoire, identifiant d'un produit parent
		 *	@param $category Optionnel, catégorie eBay vers laquelle le produit est exporté
		 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
		 */
		private function updateProductVariation( $item_id, $parent, $category='' ){
			global $config;

			if( trim($item_id)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant de l\'objet "'.$item_id.'" est faux.');
				return false;
			}

			// Catégorie d'export du produit
			if( trim($category)=='' ){
				$category = EBay::getExportCategory( $parent );
				if( !$category || trim($category)=='' ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune catégoie d\'export n\'est définie pour le produit "'.$parent.'".');
					return false;
				}
			}

			$variation = EBay::getVariations( $parent, $category );
			if( !is_array($variation) || !sizeof($variation) ){
				// error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune déclinaison n\'est disponible pour le produit "'.$parent.'".');
				return false;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<ReviseFixedPriceItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
					<WarningLevel>High</WarningLevel>
					<Item>
						<ItemID>'.$item_id.'</ItemID>
						'.$variation['xml'].'
					</Item>
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
				</ReviseFixedPriceItemRequest>
			';

			$response = EBay::sendRequest( 'ReviseFixedPriceItem', $request );
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			return true;
		}

		/** Cette fonction permet de mettre à jour la quantité d'un produit qu'il soit ou non une déclinaison
		 *	@param $item_id Obligatoire, identifiant de l'objet produit chez eBay
		 *	@param $price Obligatoire, nouveau prix du produit
		 *	@param $qty Obligatoire, nouveau stock disponible du produit
		 *	@param string $ref Optionnel, référence de la déclinaison (produit enfant)
		 *	@param $old_price Optionnel, prix avant promotion
		 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		private function updatePriceAndQuantity( $item_id, $price, $qty, $ref='', $old_price=0, $prd_id=0 ){
			global $config;

			if( trim($item_id)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant d\'objet "'.$item_id.'" est faux.');
				return false;
			}

			if( !is_numeric($price) || $price<=0 ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le prix du produit est inférieur ou égal à zéro, impossible à mettre à jour.');
				return false;
			}

			if( !is_numeric($qty) || $qty<0 ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La quantité est inférieur à zéro, impossible à mettre à jour.');
				return false;
			}

			$product = array( 'barcode' => '', 'id' => '', 'brd_title' => '' );

			if( is_numeric($prd_id) && $prd_id > 0 ){
				$r_product = prd_products_get_simple( $prd_id );
				if( !$r_product || !ria_mysql_num_rows($r_product) ){
					return false;
				}

				$product = ria_mysql_fetch_assoc( $r_product );
			}

			if( trim($ref)=='' ){
				$request = '
					<?xml version="1.0" encoding="utf-8"?>
					<ReviseFixedPriceItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
						<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
						<WarningLevel>High</WarningLevel>
						<Item>
							<ItemID>'.$item_id.'</ItemID>
							<StartPrice>'.number_format( $price, 2, '.', '' ).'</StartPrice>
				';

				if( is_numeric($old_price) && $old_price>0 ){
					$request .= '
							<DiscountPriceInfo>
								<MinimumAdvertisedPriceExposure>DuringCheckout</MinimumAdvertisedPriceExposure>
								<OriginalRetailPrice>'.number_format( $old_price, 2, '.', '' ).'</OriginalRetailPrice>
							</DiscountPriceInfo>
					';
				}

				$request .= '
							<Quantity>'.$qty.'</Quantity>
				';

				$mpn = prd_suppliers_get_one_ref( $product['id'] );

				if( trim($product['brd_title']) != '' || trim($mpn) != '' ){
					$request .= '
						<ItemSpecifics>
					';

					if( trim($product['brd_title']) != '' ){
						$request .= '
								<NameValueList>
									<Name>Marque</Name>
									<Value>'.ucfirst( strtolower2($product['brd_title']) ).'</Value>
								</NameValueList>
						';
					}

					if( trim($mpn) != '' ){
						$request .= '
								<NameValueList>
									<Name>MPN</Name>
									<Value>'.$mpn.'</Value>
								</NameValueList>
						';
					}

					$request .= '
						</ItemSpecifics>
					';
				}

				if( trim($product['barcode']) != '' ){
					$request .= '
							<ProductListingDetails>
								<EAN>'.$product['barcode'].'</EAN>
							</ProductListingDetails>
					';
				}

				$request .= '
						</Item>
						<RequesterCredentials>
							<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
						</RequesterCredentials>
					</ReviseFixedPriceItemRequest>
				';
			}else{
				$request = '
					<?xml version="1.0" encoding="utf-8"?>
					<ReviseFixedPriceItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
						<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
						<WarningLevel>High</WarningLevel>
						<Item>
							<ItemID>'.$item_id.'</ItemID>
							<Variations>
								<Variation>
									<SKU>'.$ref.'</SKU>
									<StartPrice>'.number_format( $price, 2, '.', '' ).'</StartPrice>
				';

				if( is_numeric($old_price) && $old_price>0 ){
					$request .= '
									<DiscountPriceInfo>
										<MinimumAdvertisedPriceExposure>DuringCheckout</MinimumAdvertisedPriceExposure>
										<OriginalRetailPrice>'.number_format( $old_price, 2, '.', '' ).'</OriginalRetailPrice>
									</DiscountPriceInfo>
					';
				}

				$request .= '
									<Quantity>'.$qty.'</Quantity>
								</Variation>
							</Variations>
						</Item>
						<RequesterCredentials>
							<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
						</RequesterCredentials>
					</ReviseFixedPriceItemRequest>
				';
			}

			$response = EBay::sendRequest( 'ReviseFixedPriceItem', $request, false, $prd_id);
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			$rp = ria_mysql_query( 'select pv_obj_id_1 from fld_object_values where pv_tnt_id='.$config['tnt_id'].' and pv_value=\''.$item_id.'\' and pv_obj_id_1!=0' );
			if( $rp && ria_mysql_num_rows($rp) ){
				$p = ria_mysql_result( $rp, 0, 'pv_obj_id_1' );

				if( $p && isset($response->EndTime) ){
					$date_end = date( 'Y-m-d H:i:s', strtotime((string)$response->EndTime) );
					if( !fld_object_values_set(array(CTR_EBAY, $p), _FLD_PRD_EBAY_END, $date_end) ){
						error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour la date de fin "'.$date_end.'" de validité du produit "'.$p.'".');
						return false;
					}
				}
			}

			return true;
		}

		/** Cette fonction permet de réactiver la vente d'un produit ayant été désactivé.
		 *	@param $item_id Obligatoire, identifiant de l'objet produit chez eBay
		 *	@param $prd Obligatoire, identifiant du produit
		 *	@param $data Obligatoire, tableau contenant les informations nécessaire à la réactivation d'un produit
		 *	@return bool True si la réactivation s'est correctement déroulée, False dans le cas contraire
		 */
		private function relistFixedPriceItem( $item_id, $prd ){
			global $config;

			if( trim($item_id)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant d\'objet "'.$item_id.'" est faux.');
				return false;
			}

			if( !is_numeric($prd) || $prd<=0 ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] L\'identifiant produit "'.$prd.'" est faux.');
				return false;
			}

			$category = EBay::getExportCategory( $prd );
			if( !is_numeric($category) || $category <= 0 ){
				return false;
			}

			$data = EBay::getDataForExportProduct( 'ReviseFixedPriceItem', $prd, $category );
			if( !is_array($data) || !sizeof($data) ){
				return false;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<RelistFixedPriceItemRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
					<WarningLevel>High</WarningLevel>
					<Item>
						<ItemID>'.$item_id.'</ItemID>
			';

			if( trim($data['brand']) != '' || trim($data['mpn']) != '' ){
				$request .= '
						<ItemSpecifics>
				';

				if( trim($data['brand']) != '' ){
					$request .= '
							<NameValueList>
								<Name>Marque</Name>
								<Value>'.$data['brand'].'</Value>
							</NameValueList>
					';
				}

				if( trim($data['mpn']) != '' ){
					$request .= '
							<NameValueList>
								<Name>MPN</Name>
								<Value>'.$data['mpn'].'</Value>
							</NameValueList>
					';
				}

				$request .= '
						</ItemSpecifics>
				';
			}

			if( trim($data['barcode']) != '' ){
				$request .= '
						<ProductListingDetails>
							<EAN>'.$data['barcode'].'</EAN>
						</ProductListingDetails>
				';
			}

			$request .= '
					</Item>
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
					<WarningLevel>High</WarningLevel>
				</RelistFixedPriceItemRequest>
			';

			$response = EBay::sendRequest( 'RelistFixedPriceItem', $request, false, $prd );
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			if( !isset($response->ItemID) ){
				return false;
			}

			if( !fld_object_values_set(array(CTR_EBAY, $prd), _FLD_PRD_EBAY_ID, (string) $response->ItemID) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour l\'identifiant d\'objet "'.$response->ItemID.'" pour le produit "'.$prd.'".');
				return false;
			}

			if( isset($response->EndTime) ){
				$date_end = date( 'Y-m-d H:i:s', strtotime((string)$response->EndTime) );
				if( !fld_object_values_set(array(CTR_EBAY, $prd), _FLD_PRD_EBAY_END, $date_end) ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de mettre à jour la date de fin "'.$date_end.'" de validité du produit "'.$prd.'".');
					return false;
				}
			}

			fld_object_values_set( array(CTR_EBAY, $prd), _FLD_PRD_EBAY_DISABLED, '' );
			return EBay::worqueueEBayProduct( $prd, 'update' );
		}

		/** Cette fonction permet de récupérer les commandes passées sur eBay.
		 *	@return array Un tableau contenant les informations sur la commande
		 */
		public function getOrdersList() {
			global $config;

			// Service de livraison en place
			$carrier = json_decode( $config['ebay_carriers'], true );
			if( !is_array($carrier) || !sizeof($carrier) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune configuration de livraison n\'est renseignée.');
				return false;
			}

			$request = '
				<?xml version="1.0" encoding="utf-8"?>
				<GetOrdersRequest xmlns="urn:ebay:apis:eBLBaseComponents">
					<DetailLevel>ReturnAll</DetailLevel>
					<ErrorLanguage>'.EBay::getErrorLanguage().'</ErrorLanguage>
					<WarningLevel>High</WarningLevel>
					<Pagination>
						<EntriesPerPage>100</EntriesPerPage>
						<PageNumber>1</PageNumber>
					</Pagination>
					<NumberOfDays>10</NumberOfDays>
					<OrderRole>Seller</OrderRole>
					<RequesterCredentials>
						<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
					</RequesterCredentials>
				</GetOrdersRequest>
			';

			$response = EBay::sendRequest( 'GetOrders', $request );
			if( !isset($response->Ack) || $response->Ack=='Failure' ){
				return false;
			}

			$ar_orders = array();
			if( isset($response->OrderArray->Order) ){
				$i = 0;
				foreach( $response->OrderArray->Order as $ord ){

					// On ne récupère que les commandes complétées
					if( $ord->OrderStatus!='Completed' ){
						continue;
					}

					// Vérification annexe
					if( !isset($ord->ShippingAddress) ){ // Adresse de livraison
						continue;
					}
					if( !isset($ord->ShippingServiceSelected) ){ // Service de livraison
						continue;
					}
					if( !isset($ord->TransactionArray->Transaction) ){ // Lignes de commande
						continue;
					}

					$address = $ord->ShippingAddress;

					// Service de livraison sélectionné
					$shipping = $ord->ShippingServiceSelected;
					$service = false;
					foreach( $carrier as $info ){
						if( $info['carrier']==$shipping->ShippingService ){
							$service = $info['service'];
							break;
						}
					}

					if( !$service ){
						error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucun service pour la commande'.((string) $ord->OrderID).'.');
						continue;
					}

					$ar_orders[ $i ] = array(
						'ref' => (string) $ord->OrderID,
						'date' => date( 'Y-m-d H:i:s', strtotime((string) $ord->CheckoutStatus->LastModifiedTime) ),
						'address' => array(
							'name' => (string) $address->Name,
							'street1' => (string) $address->Street1,
							'street2' => (string) $address->Street2,
							'city' => (string) $address->CityName,
							'zipcode' => (string) $address->PostalCode,
							'country' => (string) $address->Country,
							'phone' => (string) $address->Phone
						),
						'products' => array(),
						'shipping' => array(
							'service' => $service,
							'amount' => (float) $shipping->ShippingServiceCost
						)
					);

					$email = '';
					foreach( $ord->TransactionArray->Transaction as $line ){
						if( isset($line->Buyer->Email) ){
							if( trim($email)=='' ){
								$email = (string) $line->Buyer->Email;
							}
						}

						$ref = (string) (isset($line->Variation->SKU) ? $line->Variation->SKU : $line->Item->SKU);
						$title = (string) (isset($line->Variation->VariationTitle) ? $line->Variation->VariationTitle : $line->Item->Title);

						$ar_orders[ $i ]['products'][] = array(
							'id' => (string) $line->OrderLineItemID,
							'ref' => $ref,
							'title' => $title,
							'price' => (float) $line->TransactionPrice,
							'qty' => (int) $line->QuantityPurchased
						);
					}

					$ar_orders[ $i ]['address']['email'] = $email;

					$i++;
				}
			}

			return $ar_orders;
		}

		/** Cette fonction permet de confirmer l'expédition d'une commande
		 *	@param $ord Obligatoire, résultat tel que retourné par ria_mysql_fetch_array( ord_orders_get($ord_id) )
		 *	@return bool True si la confirmation s'est correctement déroulée, False dans le cas contraire
		 */
		public function confirmDeliveryOrder( $ord ){
			global $config;

			if( !is_array($ord) || !sizeof($ord) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune information sur la commande.');
				return false;
			}

			if( !isset($ord['id'], $ord['state_id']) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Toutes les informations sur la commande ne sont pas fournies.');
				return false;
			}

			if( !in_array($ord['state_id'], array(6, 7, 8)) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le statut "'.$ord['state_id'].'" de la commande "'.$ord['id'].'" ne permet pas d\'informer eBay de son expédition.');
				return true;
			}

			$shipping = EBay::getProductShipping();
			if( !is_array($shipping) || !sizeof($shipping) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de récupérer les informations sur le produit frais de port.');
				return false;
			}

			$rblp = ord_bl_products_get( 0, $ord['id'] );
			if( !$rblp || !ria_mysql_num_rows($rblp) ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de récupérer le détail du BL pour la commande "'.$ord['id'].'".');
				return false;
			}

			$ar_flyers = ctr_comparators_get_ord_import_add( CTR_EBAY );

			$service = '';
			$count = $send = 0;
			while( $blp = ria_mysql_fetch_array($rblp) ){
				// Exclut la notification d'expédition des flyers
				if( in_array($blp['ref'], $ar_flyers) ){
					continue;
				}

				if( trim($service)=='' ){
					if( trim($service) == '' ){
						$service = ord_bl_get_srv_name( $blp['bl_id'] );
						if( trim($service)=='' ){
							error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de rÃ©cupÃ©rer le nom du service de livraison ("'.$blp['bl_id'].'").');
							break;
						}
					}
				}

				if( $shipping['id']==$blp['id'] ){
					continue;
				}

				if( trim($blp['colis'])=='' ){
					continue;
				}

				$count++;

				$is_send = fld_object_values_get( array($blp['bl_id'], $blp['id']), _FLD_BL_PRD_MKT);
				if( trim($is_send)!='Oui' ){
					// Récupère le numéro de ligne chez eBay
					$line = fld_object_values_get( array($ord['id'], $blp['id']), _FLD_PRD_ORD_MKT_ID );
					if( trim($line)=='' ){
						error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de récupérer le numéro de ligne ("'.$ord['id'].'" - "'.$blp['id'].'").');
						continue;
					}

					$request = '
						<?xml version="1.0" encoding="utf-8"?>
						<CompleteSaleRequest xmlns="urn:ebay:apis:eBLBaseComponents">
							<Version>'.$this->version.'</Version>
							<WarningLevel>High</WarningLevel>
							<OrderLineItemID>'.$line.'</OrderLineItemID>
							<Shipment>
								<ShipmentTrackingDetails>
									<ShipmentTrackingNumber>'.$blp['colis'].'</ShipmentTrackingNumber>
									<ShippingCarrierUsed>'.$service.'</ShippingCarrierUsed>
								</ShipmentTrackingDetails>
							</Shipment>
							<Shipped>true</Shipped>
							<RequesterCredentials>
								<eBayAuthToken>'.EBay::fetchToken().'</eBayAuthToken>
							</RequesterCredentials>
						</CompleteSaleRequest>
					';

					$response = EBay::sendRequest( 'CompleteSale', $request );
					if( !isset($response->Ack) || $response->Ack=='Failure' ){
						continue;
					}

					if( !fld_object_values_set( array($blp['bl_id'], $blp['id']), _FLD_BL_PRD_MKT, 'Oui') ){
						error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de noter le numéro de colis comme envoyé ("'.$blp['bl_id'].'" - "'.$blp['id'].'").');
					}
				}

				$send++;
			}

			if( $count==$send ){
				if( !fld_object_values_set( $ord['id'], _FLD_ORD_CTR_SHIPPED, 'Oui') ){
					error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de noter la commande comme expédiée ("'.$ord['id'].'").');
				}
			}

			return true;
		}

		/** Cette fonction lance une requête vers eBay.
		 *	@param $action Obligatoire, action réalisée
		 *	@param $request Obligatoire, contenu de la requête
		 *	@param $prd Optionnel, identifiant d'un produit
		 *	@return Le résultat de la requête envoyée eBay, False si l'un des paramètres est omis ou faux
		 */
		private function sendRequest( $action, $request, $shopping=false, $prd=0 ){
			global $config;

			if( $action=='ReviseFixedPriceItem' || $action=='AddFixedPriceItem' ){
				// die( $request."\n" );
			}

			if( trim($action)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La requête a effectuée sur eBay n\'est pas renseignée.');
				return false;
			}

			if( trim($request)=='' ){
				error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] La requête est vide.');
				return false;
			}

			$headers = array (
				'X-EBAY-API-COMPATIBILITY-LEVEL:'.$this->version,
				'X-EBAY-API-DEV-NAME:'.$this->devID,
				'X-EBAY-API-APP-NAME:'.$this->appID,
				'X-EBAY-API-CERT-NAME:'.$this->certID,
				'X-EBAY-API-SITEID:'.EBay::getSiteID(),
				'X-EBAY-API-CALL-NAME:'.$action
			);
			// Log pour la requète ebay
			ob_start();
			echo 'Request_['.$config['tnt_id'].']_from : '.date('Y-m-d H:i:s').' START===('.PHP_EOL;
			$out = fopen('php://output', 'w');
			// Exécute la requête
			$c = curl_init();
			curl_setopt( $c, CURLOPT_URL, $this->apiUrl );
			// curl_setopt( $c, CURLINFO_HEADER_OUT, true );
			curl_setopt( $c, CURLOPT_HTTPHEADER, $headers );
			curl_setopt( $c, CURLOPT_POST, 1 );
			curl_setopt( $c, CURLOPT_POSTFIELDS, $request );
			curl_setopt( $c, CURLOPT_RETURNTRANSFER, 1 );
			curl_setopt($c, CURLOPT_VERBOSE, true);
			curl_setopt($c, CURLOPT_STDERR, $out);

			$response = curl_exec($c);
			curl_close($c);
			// fin de la capture du log
			fclose($out);
			echo 'Response__from : '.date('Y-m-d H:i:s').PHP_EOL;
			echo $response.PHP_EOL;
			echo ')===END'.PHP_EOL;
			$debug = ob_get_clean();
			error_log($debug, 3, '/var/log/php/ebay_requests.log');
			if( substr($response, 0, 5)!='<?xml' ){
				error_log('XML invalide eBay : '.$response."\n", 3, '/var/log/php/ebay_error.log');
				return false;
			}

			$response = simplexml_load_string( $response );
			if( !isset($response->Ack) || $response->Ack!='Success' ){
				EBay::sendErrorMessage( $request, $response, $prd );
			}

			return $response;
		}

		/** Cette fonction permet d'envoi un email lors d'une erreur de requête eBay.
		 *	@param $error Obligatoire, contenu de la réponse eBay avec l'erreur
		 *	@param $request Obligatoire, requête envoyée à eBay
		 *	@param $prd Optionnel, identifiant d'un produit
		 *	@return bool False si l'un des paramètres est omis, True si l'envoi de l'email est correctement fait, False dans le cas contraire
		 */
		private static function sendErrorMessage( $request, $error, $prd=0 ){
			if( trim($request)=='' ){
				return false;
			}

			if( !isset($error->Errors) ){
				return false;
			}

			global $config;

			// Code erreur à ignorer
			$ignored = array(
				21919158, // Compte PayPal incorrecte
				21917319, // L'option Galerie plus n'est plus disponible
				21916970,  // Le compte vendeur ne peut pas proposer de promotion (le tarif du produit est tout de même le tarif réduit)
				21919403
			);

			if( isset($error->Ack) && $error->Ack != 'Failure' ){
				return true;
			}

			$i = 0;
			$list_code = '';

			foreach( $error->Errors as $e ){
				if( isset($e->ErrorCode) ){
					$code = $e->ErrorCode;

					if( in_array($code, $ignored) ){
						continue;
					}

					// Envoyer une demande de reconnexion entre RiaShopping et eBay
					switch( $code ){
						case 931 :
						case 21916016 :
						case 21916017 : {
							$reload = cfg_overrides_get_value( 'ebay_reload_token' );
							if( !$reload ){
								$email = new Email();
								$email->setFrom( 'RiaShopping <<EMAIL>>');
								// $email->addBcc( '<EMAIL>');
								$email->addTo( '<EMAIL>');
								$email->setSubject( 'Réinitialisation de la connexion RiaShoppping / eBay' );
								$email->addHtml( '<table width="480" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="http://www.riastudio.fr/images/template/header/banner.png"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );

								$email->addParagraph('Cher client, chère cliente,');
								$email->addParagraph('La connexion entre RiaShopping et eBay a été réinitiliasée, pour le moment, toutes les actions vers eBay sont en attente d\'une reconnexion.');
								$email->addHtml('<p>Afin de vous reconnecter, vous devez aller dans <a href="'.$config['site_url'].'/admin/comparators/params.php?ctr=15">RiaShop > Places de marchés > eBay > Configurations</a> et cliquer sur "Rattacher mon compte eBay".</p>');

								$email->addHtml( '</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(0, 47, 59);"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb(255, 255, 255);" href="mailto:<EMAIL>"><EMAIL></a></div></div>' );
								$email->send();

								cfg_overrides_set_value('ebay_reload_token', 1);
							}

							return true;
						}
						case 196 : {
							if( is_numeric($prd) && $prd>0 ){
								// On récupère la date de fin de validiter du produit en erreur
								$date = fld_object_values_get( array(CTR_EBAY, $prd), _FLD_PRD_EBAY_END, '', false, true );
								if( isdateheure($date) ){
									if( strtotime($date)>time() ){
										return true;
									}

									$i = 0;
									while( true ){
										$date = date( 'Y-m-d H:i:s', strtotime( $date.' +30 days' ) );
										if( $date=='1970-01-01 01:00:00' ){
											$date = date( 'Y-m-d H:i:s', strtotime('+30 days') );
										}

										if( strtotime($date)>time() || $i>12 ){
											break;
										}

										$i++;
									}

									if( fld_object_values_set(array(CTR_EBAY, $prd), _FLD_PRD_EBAY_END, $date) ){
										return true;
									}
								}
							}
						}
						case 21919067 : {
							if( is_numeric($prd) && $prd>0 ){
								if( isset($e->ErrorParameters[1]->Value) && trim($e->ErrorParameters[1]->Value)!='' ){
									if( fld_object_values_set(array(CTR_EBAY, $prd), _FLD_PRD_EBAY_ID, $e->ErrorParameters[1]->Value) ){
										return true;
									}
								}
							}
							break;
						}
						case 1047 :{
							if( is_numeric($prd) && $prd>0 ){
								if( fld_object_values_set(array(CTR_EBAY, $prd), _FLD_PRD_EBAY_DISABLED, date('Y-m-d H:i:s')) ){
									return true;
								}
							}
							break;
						}
						case 17 :{
							if( isset($e->ErrorParameters->Value) ){
								$itemID = (string) $e->ErrorParameters->Value;

								if( trim($itemID)!='' ){
									$rproduct_id =  ria_mysql_query('
										select pv_obj_id_1 from fld_object_values where pv_tnt_id='.$config['tnt_id'].' and pv_obj_id_0='.CTR_EBAY.' and pv_value="'.addslashes( $itemID ).'"
									');

									if( $rproduct_id && ria_mysql_num_rows($rproduct_id) ){
										$product_id = ria_mysql_result( $rproduct_id, 0, 'pv_obj_id_1' );

										$res = ria_mysql_query('
											delete from fld_object_values where pv_tnt_id='.$config['tnt_id'].' and pv_obj_id_0='.CTR_EBAY.' and pv_obj_id_1='.$product_id.'
										');

										error_log('EBAY code Error 17 : delete from fld_object_values where pv_tnt_id='.$config['tnt_id'].' and pv_obj_id_0='.CTR_EBAY.' and pv_obj_id_1='.$product_id."\n", 3, '/var/log/php/ebay_error.log');

										if( $res ){
											return true;
										}
									}
								}
							}

							break;
						}
						case 291 :{
							preg_match_all( '/<ItemID>(.*)<\/ItemID>/u', $request, $matches );

							if( isset($matches[1][0]) && trim($matches[1][0])!='' ){
								$rproduct_id =  ria_mysql_query('
									select pv_obj_id_1 from fld_object_values where pv_tnt_id='.$config['tnt_id'].' and pv_obj_id_0='.CTR_EBAY.' and pv_value="'.addslashes( $matches[1][0] ).'"
								');

								if( $rproduct_id && ria_mysql_num_rows($rproduct_id) ){
									$product_id = ria_mysql_result( $rproduct_id, 0, 'pv_obj_id_1' );

									error_log('Gestion erreur eBay 291 : fld_object_values_set : '.$product_id.' - '.date('Y-m-d H:i:s')."\n", 3, '/var/log/php/ebay_error.log');

									if( fld_object_values_set(array(CTR_EBAY, $product_id), _FLD_PRD_EBAY_DISABLED, date('Y-m-d H:i:s')) ){
										return true;
									}
								}
							}

							break;
						}
						case 240 :{
							if( !strstr($request, 'ReviseFixedPriceItemRequest') || !strstr($request, '<PrimaryCategory>') ){
								preg_match_all( '/<ItemID>(.*)<\/ItemID>/u', $request, $matches );

								if( isset($matches[1][0]) && trim($matches[1][0])!='' ){
									$rproduct_id =  ria_mysql_query('
										select pv_obj_id_1 from fld_object_values where pv_tnt_id='.$config['tnt_id'].' and pv_obj_id_0='.CTR_EBAY.' and pv_value="'.addslashes( $matches[1][0] ).'"
									');

									if( $rproduct_id && ria_mysql_num_rows($rproduct_id) ){
										$product_id = ria_mysql_result( $rproduct_id, 0, 'pv_obj_id_1' );

										$t_ebay = new EBay();
										if( $t_ebay->setProductFamily($product_id) ){
											return true;
										}
									}
								}
							}

							break;
						}
						default: {
							$list_code .= ( trim($list_code) != '' ? ', ' : '' ).$code;
							break;
						}
					}
				}

				$i++;
			}

			global $config;

			if( $i>0 ){
				$message = '[eBay - Tenant '.$config['tnt_id'].'] - Erreur sendRequest'.PHP_EOL;
				if( is_numeric($prd) &&  $prd > 0 ){
					$message .= ' Identifiant produit en erreur : '.$prd.PHP_EOL;
				}
				foreach($error->Errors as $error){
					$message .= ' * '.$error->ShortMessage.PHP_EOL;
				}

				$message .= PHP_EOL.$request.PHP_EOL.'Liste des codes erreur non prient en charge : '.$list_code."\n";
				error_log($message, 3, '/var/log/php/ebay_error.log');
				return true;
			}
		}
	}

/// @}