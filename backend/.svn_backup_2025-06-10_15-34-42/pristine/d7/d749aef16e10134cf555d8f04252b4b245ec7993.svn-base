 <?php
/**
 *\defgroup api-users-index Utilisateurs
 *\ingroup crm
 *@{
*/

switch( $method ){
	/** @{@}
 	 *	@{
	 * \page api-users-index-get Chargement
	 *
	 * Cette fonction retourne une liste des utilisateurs
	 *
	 *		\code
	 *			GET /users/
	 *		\endcode
	 *
	 * @param int $id Facultatif, identifiant ou tableau d'identifiants sur lesquels filtrer le résultat
	 * @param string $ref Facultatif, référence de l'utilisateur
	 * @param string $email Facultatif, adresse email d'un utilisateur
	 * @param string $is_sync Facultatif, l'utilisateur est synchronisé ?
	 * @param int $prf Facultatif, profil de l'utilisateur
	 * @param int $start Facultatif, pagination : enregistrement de départ à retourner
	 * @param int $limit Facultatif, pagination : nombre maximum de comptes à retourner
	 *
	 * @return json Liste d'utilisateurs contenant les colonnes :
 	 *	\code{.json}
	 *		{
	 *		"tenant" : Identifiant du locataire (si 0 alors il s'agit d'un super-administrateur)
	 *		"id" : Identifiant de l'utilisateur dans la base de données (ou tableau)
	 *		"ref" : code client dans le logiciel sage
	 *		"society" : nom de la société (professionnels uniquement)
	 *		"siret" : numéro de siret
	 *		"phone" : numéro de téléphone
	 *		"fax" : numéro de fax
	 *		"mobile" : numéro de téléphone portable
	 *		"work" : numéro de téléphone dans la journée (travail)
	 *		"title_name" : civilité de l'utilisateur (Monsieur, Madame, Mademoiselle)
	 *		"adr_firstname" : prénom de l'utilisateur
	 *		"adr_lastname" : nom de l'utilisateur
	 *		"address1" : première partie de l'adresse
	 *		"address2" : deuxième partie de l'adresse
	 *		"address3" : troisième partie de l'adresse
	 *		"zipcode" : code postal
	 *		"city" : ville de l'adresse de facturation
	 *		"country" : pays de l'adresse de facturation
	 *		"cnt_code" : code pays de l'adresse de facturation à 2 caractères
	 *		"email" : Adresse email de l'utilisateur
	 *		"adr_email" : Adresse email de facturation
	 *		"password" : Mot de passe de l'utilisateur, encrypté grâce à la fonction MD5
	 *		"adr_invoices" : Identifiant de l'adresse de facturation de l'utilisateur
	 *		"adr_delivery" : Identifiant de l'adresse de livraison par défaut de l'utilisateur
	 *		"prf_id" : identifiant du profil utilisateur (tel que défini dans la table gu_profiles)
	 *		"prf_name" : désignation du profil utilisateur (tel que défini dans la table gu_profiles)
	 *		"date_created" : date et heure de création du compte utilisateur
	 *		"usr_date_created" : idem date_created mais au format EN
	 *		"date_modified" : date/heure de dernière modification du compte utilisateur
	 *		"last_login" : date/heure de dernière connexion de l'utilisateur
	 *		"last_login_en" : idem last_login mais au format EN
	 *		"cnt_id" : identifiant de l'entrée du moteur de recherche correspondant au compte
	 *		"orders" : nombre de commandes passées par le client
	 *		"orders_web" : nombre de commandes web passées par le client via la plateforme
	 *		"orders_canceled" : nombre de commandes annulées
	 *		"orders_canceled_web" : nombre de commandes web annulées
	 *		"display_prices" : ttc ou ht suivant le type de compte et ses préférences
	 *		"display_buy" : booléen indiquant si l'affichage du prix d'achat est effectué (revendeurs uniquement)
	 *		"opt_stocks" : mode d'affichage des stocks
	 *		"opt_centralized" : restreindre l'affichage aux seuls produits centralisés (revendeurs uniquement)
	 *		"prc_id" : identifiant de la catégorie tarifaire à appliquer au client
	 *		"encours" : encours du compte client
	 *		"dps_id" : identifiant du dépôt de rattachement du client
	 *		"is_sync" : indique si le compte est synchronisé avec la gestion commerciale
	 *		"naf" : code NAF de l'activité principale du client (professionnels uniquement)
	 *		"website" : adresse du site Internet du client (professionels uniquement)
	 *		"taxcode" : Numéro de TVA Intracommunautaire
	 *		"longitude" : coordonnées gps de l'adresse de livraison (si disponible)
	 *		"latitude" : coordonnées gps de l'adresse de livraison (si disponible)
	 *		"distance" : distance par rapport au point de recherche (uniquement si have_coordinates comprend des coordonnées gps)
	 *		"myprd" : option d'affichage des produits fournisseurs (actuellement utilisé sur le site fournisseur de BigShip)
	 *		"fur_alerts" : si l'alerte des modifications est activée (actuellement utilisé sur le site fournisseur de BigShip)
	 *		"cac_id" : Identifiant de la catégorie comptable
	 *		"img_id" : Identifiant de l'image principale de l'utilisateur
	 *		"parent_id" : Identifiant du compte parent
	 *		"can_login" : Si oui ou non l'utilisateur peut se connecter
	 *		"surname" : surnom du compte utilisateur
	 *		"title_id" : identifiant de civilité
	 *		"dob" : date de naissance du compte
	 *		"lng_code" : langue par défaut du client (s'applique par exemple aux notifications)
	 *		"encours_allow" : encours autorisé pour le compte (différent de usr_encours)
	 *		"accept_partners" : détermine si le compte accepte de recevoir des offres des partenaires commerciaux
	 *		"seller_id" : identifiant du commercial
	 *		"discount" : [obsolète] taux de remise client
	 *		"alert_cc" : adresses email en copie
	 *		"title_name" : nom de civilité
	 *		"type_id" : type d'adresse de facturation
	 *		"wst_id" : identifiant du site d'origine du compte (NULL si synchro)
	 *		"adr_desc" : description de l'adresse de facturation
	 *		"is_locked" : détermine si le compte est bloqué ou non
	 *		"opm_id" : identifiant du modèle de paiement associé au client
	 *		"bnk_id" : identifiant des informations bancaires principales du client
	 *		"rco_id" : identifiant du code risque de l'utilisateur
	 *		}
	 * 	\endcode
     *	@}
	*/
	case 'get':

		// Paramètre identifiant
		$ids = 0;
		if( isset($_GET['id']) && is_array($_GET['id']) ){
			foreach( $_GET['id'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants fournis en arguments sont incorrects");
				}
			}
			$ids = $_GET['id'];
		}elseif( isset($_GET['id']) && is_numeric($_GET['id']) ){
			$ids = $_GET['id'];
		}

		// paramètre email
		$email = '';
		if( isset($_GET['email']) ) {
			$email = $_GET['email'];
		}

		// paramètre ref
		$ref = '';
		if( isset($_GET['ref']) ) {
			$ref = $_GET['ref'];
		}

		// paramètre is_sync
		$is_sync = false;
		if( isset($_GET['is_sync']) ) {
			$is_sync = $_GET['is_sync'];
		}

		// paramètre de profil
		$prf = 0;
		if( isset($_GET['prf']) && ( is_numeric($_GET['prf']) || is_array($_GET['prf']) ) ){
			$prf = $_GET['prf'];
		}

		// paramètre start
		$start = 0;
		if( isset($_GET['start']) && is_numeric($_GET['start']) ){
			$start = $_GET['start'];
		}

		// paramètre limit
		$limit = 0;
		if( isset($_GET['limit']) && is_numeric($_GET['limit']) ){
			$limit = $_GET['limit'];
		}

		$array = array();
		$rusr = gu_users_get(
			$ids, $email, '', $prf, '', 0, $ref, 'created', 'desc', false, $is_sync, '', false, 0, '', 0, false, false, null, 0, false, false, null,
			false, 0, 0, false, false, $start, $limit
		);

		while($usr = ria_mysql_fetch_assoc($rusr)){
			unset($usr['password']);
			$array[] = $usr;
		}

		$result = true;
		$content = $array;

		break;
	/** @{@}
 	 *	@{
	 * \page api-users-index-add Ajout
	 *
	 * Cette fonction ajoute un utilisateur
	 *
	 *		\code
	 *			POST /users/
	 *		\endcode
	 *
	 * @param string $email Obligatoire, Adresse email de l'utilisateur, si invalide on mettra un mail par défaut
	 * @param string $ref Obligatoire, Référence
	 * @param string $title Obligatoire, Identifiant du titre
	 * @param string $firstname Obligatoire, Prénom de l'utilisateur
	 * @param string $lastname Obligatoire, Nom de l'utilisateur
	 * @param string $society Obligatoire, Nom de l'entreprise
	 * @param string $siret Obligatoire, No SIRET
	 * @param string $address1 Obligatoire, Première partie de l'adresse
	 * @param string $address2 Obligatoire, Seconde partie de l'adresse
	 * @param string $zipcode Obligatoire, Code postal
	 * @param string $city Obligatoire, Ville
	 * @param string $country Obligatoire, Pays
	 * @param string $phone Obligatoire, Numéro de téléphone
	 * @param string $fax Obligatoire, Numéro de fax
	 * @param int $prc Obligatoire, Identifiant de la catégorie tarifaire
	 * @param string $mobile Facultatif, Numéro de téléphone portable
	 * @param string $work Facultatif, Numéro de téléphone en journée
	 * @param int $type Facultatif, type de l'adresse
	 * @param int $prf Facultatif, Profil à attribuer à l'utilisateur
	 * @param string $naf Facultatif, Code NAF
	 * @param $website Facultatif, Adresse du site Internet du client
	 * @param string $taxcode Facultatif, Numéro de tva intracommunautaire (25 caractères maxi)
	 * @param int $seller_id Facultatif, Identifiant du vendeur
	 * @param $parent Facultatif, identifiant d'un compte utilisateur parent
	 * @param $opm_id Facultatif, identifiant du modèle
	 * @param string $desc Facultatif, Description
	 * @param $dps_id Facultatif, Identifiant du dépot de stockage pour le compte client
	 * @param bool $can_login Facultatif, Permet de définir si le compte peut ou non ce connecter
	 * @param bool manual_location Facultatif, permet de rentrer une localisation manuelle (==1)
	 * @param float longitude Facultatif, longitude de la localisation manuelle
	 * @param float latitude Facultatif, latitude de la localisation manuelle
	 *
	 * @return json contentant : Identifiant de l'utilisateur et de son adresse de facturation :
 	 *	\code{.json}
	 *		{
	 *		"usr_id" : Identifiant de l'utilisateur
	 *		"adr_id" : Identifiant de l'adresse de facturation
	 *		}
 	 * 	\endcode
	 *	@}
	*/
	case 'add':
		if( !isset($_REQUEST['email'], $_REQUEST['ref'], $_REQUEST['title'], $_REQUEST['firstname'], $_REQUEST['lastname'], $_REQUEST['society'],
			$_REQUEST['siret'], $_REQUEST['address1'], $_REQUEST['address2'], $_REQUEST['zipcode'], $_REQUEST['city'], $_REQUEST['country'],
			$_REQUEST['phone'], $_REQUEST['fax'], $_REQUEST['prc']) ){

			throw new Exception('Paramètres incomplet');
		}

		if( !isset($_REQUEST['mobile']) ) $_REQUEST['mobile'] = '';
		if( !isset($_REQUEST['work']) ) $_REQUEST['work'] = '';
		// société + siret valide = 2 (société)
		// nom + prénom + civilité = 1 (particulier)
		// autre = 3 (pro)

		$type = 3;
		if( trim($_REQUEST['society']) && validSIRET(preg_replace('/\s/', '', $_REQUEST['siret'])) ){
			$type = 2;
		}elseif( trim($_REQUEST['firstname']) && trim($_REQUEST['lastname']) && gu_titles_exists( $_REQUEST['title'] ) ){
			$type = 1;
		}
		if( isset($_REQUEST['type']) && is_numeric($_REQUEST['type']) ){
			$type = $_REQUEST['type'];
		}

		$prf = isset($_REQUEST['prf']) && $_REQUEST['prf'] ? $_REQUEST['prf'] : null;
		$can_login = isset($_REQUEST['can_login']) ? $_REQUEST['can_login'] : true;

		if( $prf == 1 ){
			error_log(__FILE__.':'.__LINE__.' - création d\'un compte administrateur (tnt = '.$config['tnt_id'].', ref = '.$_REQUEST['ref'].')');
		}

		if( trim($_REQUEST['email'])=="" || !gu_valid_email($_REQUEST['email']) ){
			$usr = gu_users_add_without_email( gu_password_create(), $prf, $_REQUEST['ref'], $is_sync, 0, 0, null, false, $can_login );
		}else{
			$usr = gu_users_add( $_REQUEST['email'], gu_password_create(), $prf, $_REQUEST['ref'], $is_sync, 0, 0, null, false, $can_login );
		}

		if( $usr ){

			$adr = gu_adresses_add( $usr, $type, $_REQUEST['title'], $_REQUEST['firstname'], $_REQUEST['lastname'], $_REQUEST['society'], $_REQUEST['siret'],
				$_REQUEST['address1'], $_REQUEST['address2'], $_REQUEST['zipcode'], $_REQUEST['city'], $_REQUEST['country'], $_REQUEST['phone'], $_REQUEST['fax'], $_REQUEST['mobile'], $_REQUEST['work'] );
			if( !$adr ){
				gu_users_del($usr);
				throw new Exception("La création de l'adresse de l'utilisateur a échoué : ".print_r($_REQUEST, true));
			}else{
				gu_users_address_set($usr,$adr);
				gu_users_set_prc($usr,$_REQUEST['prc']);
				if( isset($_REQUEST['naf']) ){
					gu_users_set_naf( $usr, $_REQUEST['naf'] );
				}
				if( isset($_REQUEST['website']) ){
					gu_users_set_website( $usr, $_REQUEST['website'] );
				}
				if( isset($_REQUEST['taxcode']) ){
					gu_users_set_taxcode( $usr, $_REQUEST['taxcode'] );
				}
				if( isset($_REQUEST['seller_id']) && is_numeric($_REQUEST['seller_id']) ){
					gu_users_set_seller_id( $usr, $_REQUEST['seller_id'] );
				}
				if( isset($_REQUEST['dps_id']) ){
					gu_users_update_dps( $usr, $_REQUEST['dps_id'] );
				}
				if( isset($_REQUEST['parent']) ){
					gu_users_set_parent_id( $usr, $_REQUEST['parent'] );
				}
				if( isset($_REQUEST['opm_id']) ){
					gu_users_payment_models_set( $usr, $_REQUEST['opm_id'] );
				}
				if( isset($_REQUEST['desc']) ){
					gu_adresses_set_desc( $usr, $adr, $_REQUEST['desc'] );
				}
				if( isset($_REQUEST['date_created']) ){
					gu_users_set_date_created( $usr, $_REQUEST['date_created'] );
				}

				if (isset($_REQUEST['manual_location'], $_REQUEST['longitude'], $_REQUEST['latitude']) && $_REQUEST['manual_location'] == 1) {
					gu_adresses_set_coordinates($adr, $_REQUEST['latitude'], $_REQUEST['longitude'], $_REQUEST['date_location']);
				}

				// Maintenant que l'utilisateur et ses tables liées est ajouté, on informe les intéréssés de cet ajout en envoyant une notification
				$rusr = gu_users_get($usr);
				if( $rusr ){
					$new_usr = ria_mysql_fetch_assoc($rusr);

					// ajout des champs avancés
					if(isset($_REQUEST['fields'])) {
						$fields_delete_missing = (isset($_REQUEST['fields_delete_missing']) && !$_REQUEST['fields_delete_missing']) ? false : true;
						fields_sync(CLS_USER, $usr, 0, 0, $_REQUEST['fields'], $fields_delete_missing);
					}

					if( isset($_REQUEST['masked']) && $_REQUEST['masked'] ){
						// cas pour la synchronisation des yuto le compte est supprimer
						// on  va faire une 2eme requetes pour le réactiver à la suite.
						gu_users_set_deleted($usr, true);
					}else{
						push_notify_user_device_destinataires(NT_TYPE_USER_ADD, null, $new_usr);
						gu_users_notify_shop_owner($usr);
					}

					// Ajout des moyens de paiements par défaut au compte client
					if( isset($config['admin_users_created_add_payments']) && $config['admin_users_created_add_payments'] ){
						if (isset($config['default_usr_payments']) && is_array($config['default_usr_payments'])) {
							foreach ($config['default_usr_payments'] as $pay_id) {
								gu_users_payment_types_add($usr, $pay_id, 0, 0, 0);
							}
						}
					}
				}

				$result = true;
				$content = array('usr_id' => $usr, 'adr_id' => $adr);
			}

		}else{
			throw new Exception("La création de l'utilisateur a échoué: ".print_r($_REQUEST, true));
		}

		break;
	/** @{@}
 	 *	@{
	 * \page api-users-index-upd Mise à jour
	 *
	 * Cette fonction modifie un utilisateur.
	 *
	 *		\code
	 *			PUT /users/
	 *		\endcode
	 *
	 * @param int $id Obligatoire, Identifiant de l'utilisateur
	 * @param string $email Obligatoire, Adresse email de l'utilisateur, si invalide on mettra un mail par défaut
	 * @param string $ref Obligatoire, Référence
	 * @param string $title Obligatoire, Identifiant du titre
	 * @param string $firstname Obligatoire, Prénom de l'utilisateur
	 * @param string $lastname Obligatoire, Nom de l'utilisateur
	 * @param string $society Obligatoire, Nom de l'entreprise
	 * @param string $siret Obligatoire, No SIRET
	 * @param string $address1 Obligatoire, Première partie de l'adresse
	 * @param string $address2 Obligatoire, Seconde partie de l'adresse
	 * @param string $zipcode Obligatoire, Code postal
	 * @param string $city Obligatoire, Ville
	 * @param string $country Obligatoire, Pays
	 * @param string $phone Obligatoire, Numéro de téléphone
	 * @param string $fax Obligatoire, Numéro de fax
	 * @param int $prc Obligatoire, Identifiant de la catégorie tarifaire
	 * @param string $mobile Facultatif, Numéro de téléphone portable
	 * @param string $work Facultatif, Numéro de téléphone en journée
	 * @param int $type Facultatif, type de l'adresse
	 * @param int $prf Facultatif, Profil à attribuer à l'utilisateur
	 * @param string $naf Facultatif, Code NAF
	 * @param $website Facultatif, Adresse du site Internet du client
	 * @param string $taxcode Facultatif, Numéro de tva intracommunautaire (25 caractères maxi)
	 * @param int $seller_id Facultatif, Identifiant du vendeur
	 * @param $parent Facultatif, identifiant d'un compte utilisateur parent
	 * @param $opm_id Facultatif, identifiant du modèle
	 * @param string $desc Facultatif, Description
	 * @param $adr_delivery Facultatif, Adresse de livraison principale de l'utilisateur
	 * @param $dps_id Facultatif, Identifiant du dépot de stockage pour le compte client
	 * @param bool $can_login Facultatif, Permet de définir si le compte peut ou non ce connecter
	 * @param bool manual_location Facultatif, permet de rentrer une localisation manuelle (==1)
	 * @param float longitude Facultatif, longitude de la localisation manuelle
	 * @param float latitude Facultatif, latitude de la localisation manuelle
	 *
	 * @return true si la mise à jour s'est déroulée avec succès.
	 *	@}
	*/
	case 'upd' :

		if( !isset($_REQUEST['id']) ){
			throw new Exception("Erreur de paramètre.");
		}

		// tente une restauration du compte cas d'une erreur de confirmation initiale
		if( isset($_REQUEST['masked']) && !gu_users_exists($_REQUEST['id']) ){
			usr_restore_account($_REQUEST['id']);
		}

		if( !gu_users_exists($_REQUEST['id']) ){
			throw new Exception("Utilisateur introuvable.");
		}

		$r_old_usr = gu_users_get($_REQUEST['id']);
		if (!$r_old_usr  || !ria_mysql_num_rows($r_old_usr) ) {
			$result = false;
			break;
		}

		$old_usr = ria_mysql_fetch_assoc($r_old_usr);

		if( isset($_REQUEST['naf']) ){
			if( !gu_users_set_naf( $_REQUEST['id'], $_REQUEST['naf'] ) ){
				throw new Exception("Erreur lors de la mise à jour du code naf.");
			}
		}
		if( isset($_REQUEST['website']) ){
			if( !gu_users_set_website( $_REQUEST['id'], $_REQUEST['website'] ) ){
				throw new Exception("Erreur lors de la mise à jour du site web.");
			}
		}
		if( isset($_REQUEST['taxcode']) ){
			if( !gu_users_set_taxcode( $_REQUEST['id'], $_REQUEST['taxcode'] ) ){
				throw new Exception("Erreur lors de la mise à jour de la taxe.");
			}
		}
		if( isset($_REQUEST['seller_id']) ){
			if( !gu_users_set_seller_id( $_REQUEST['id'], $_REQUEST['seller_id'] ) ){
				throw new Exception("Erreur lors de la mise à jour du représentant ( seller_id ).");
			}
		}
		if( isset($_REQUEST['opm_id']) ){
			if( $_REQUEST['opm_id'] == 0 ) $_REQUEST['opm_id'] = null;
			if( !gu_users_payment_models_set( $_REQUEST['id'], $_REQUEST['opm_id'] ) ){
				throw new Exception("Erreur lors de la mise à jour du modèle de paiement.");
			}
		}
		if( isset($_REQUEST['bnk_id']) ){
			if( !gu_users_set_bank_details_main( $_REQUEST['id'], $_REQUEST['bnk_id'] ) ){
				throw new Exception("Erreur lors de la mise à jour du rib.");
			}
		}

		// Mise à jour de la date de naissance
		if( isset($_REQUEST['dob']) ){
			if( !gu_users_set_date_of_birth( $_REQUEST['id'], $_REQUEST['dob'] ) ){
				throw new Exception("Erreur lors de la mise à jour de la date de naissance.");
			}
		}

		$usr = ria_mysql_fetch_assoc(gu_users_get($_REQUEST['id']));

		if( isset($_REQUEST['email']) && $_REQUEST['email']!=$usr['email'] ){

			if(trim($_REQUEST['email']) == '' || !gu_valid_email($_REQUEST['email']) ){
				$email = time().'@yuto.com';
				if( !gu_users_update_email($_REQUEST['id'], $email) ){
					throw new Exception("Erreur lors de la suppression de l'email : ".$_REQUEST['email'].' par '.$email);
				}
			}else{
				if( !gu_users_get_can_login($_REQUEST['id']) || gu_users_get_doublon_email($_REQUEST['id'], $_REQUEST['email'])==0 ){
					if( !gu_users_update_email($_REQUEST['id'], $_REQUEST['email']) ){
						throw new Exception("Erreur lors de la mise à jour de l'email : ".$_REQUEST['email']);
					}
				}
			}
		}


		//Mise à jour des données utilisateur présentes dans la table adresses
		if(isset($_REQUEST['title'], $_REQUEST['firstname'], $_REQUEST['lastname'], $_REQUEST['society'], $_REQUEST['siret'], $_REQUEST['address1'], $_REQUEST['address2'], $_REQUEST['zipcode'], $_REQUEST['city'], $_REQUEST['country'], $_REQUEST['phone'], $_REQUEST['fax'], $_REQUEST['mobile'], $_REQUEST['work'],$_REQUEST['address3'])){
			$type = 1;
			if(isset($_REQUEST['society']) && trim($_REQUEST['society']) ){
				if((isset($_REQUEST['firstname']) && trim($_REQUEST['firstname'])) || (isset($_REQUEST['lastname']) && trim($_REQUEST['lastname']))){
					$type = 3;
				}else{
					$type = 2;
				}
			}

			if(isset($_REQUEST['title']) && $_REQUEST['title'] == 0 ){
				$_REQUEST['title']=4;
			}

			if (isset($_REQUEST['manual_location'], $_REQUEST['longitude'], $_REQUEST['latitude']) && $_REQUEST['manual_location'] == 1) {
				gu_adresses_set_coordinates($usr['adr_invoices'], $_REQUEST['latitude'], $_REQUEST['longitude'], $_REQUEST['date_location']);
			}

			if( !isset($_REQUEST['mobile']) ) $_REQUEST['mobile'] = '';
			if( !isset($_REQUEST['work']) ) $_REQUEST['work'] = '';

			if( gu_adresses_update($_REQUEST['id'],$usr['adr_invoices'], $type, $_REQUEST['title'], $_REQUEST['firstname'], $_REQUEST['lastname'], $_REQUEST['society'], $_REQUEST['siret'], $_REQUEST['address1'], $_REQUEST['address2'], $_REQUEST['zipcode'], $_REQUEST['city'], $_REQUEST['country'], $_REQUEST['phone'], $_REQUEST['fax'], $_REQUEST['mobile'], $_REQUEST['work'], $is_sync, null, null, null, $_REQUEST['address3']) ){

				// ajoute la description de l'adresse si disponible
				if( isset($_REQUEST['desc']) ){
					gu_adresses_set_desc( $_REQUEST['id'], $usr['adr_invoices'], $_REQUEST['desc'] );
				}
			}
		}
		//fin maj table addresses


		if( isset($_REQUEST['parent']) ){
			if( !gu_users_set_parent_id( $_REQUEST['id'], $_REQUEST['parent'] ) ){
				throw new Exception("Erreur lors de la mise à jour du parent.");
			}
		}

		if( isset($_REQUEST['dps_id']) ){
			if( prd_deposits_exists($_REQUEST['dps_id']) && !gu_users_update_dps( $_REQUEST['id'], $_REQUEST['dps_id'] ) ){
				throw new Exception("Erreur lors de la mise à jour du dépot.");
			}
		}

		if( isset($_REQUEST['prc']) ){
			if( !gu_users_set_prc( $_REQUEST['id'], $_REQUEST['prc'] ) ){
				throw new Exception("Erreur lors de la mise à jour de la catégorie tarifaire : ".$_REQUEST['prc']);
			}
		}

		if( isset($_REQUEST['prf']) ){

			if( $_REQUEST['prf'] == 1 ){
				error_log(__FILE__.':'.__LINE__.' - création d\'un compte administrateur (tnt = '.$config['tnt_id'].', id = '.$_REQUEST['id'].')');
			}

			if( !gu_users_set_profile( $_REQUEST['id'], $_REQUEST['prf'], true ) ){
				throw new Exception("Erreur lors de la mise à jour du profils client : ".$_REQUEST['prf']);
			}
		}

		if( isset($_REQUEST['adr_delivery']) ){
			$adr = null;
			if( $_REQUEST['adr_delivery'] > 0 ) $adr = $_REQUEST['adr_delivery'];

			if( !gu_users_address_delivery_set( $_REQUEST['id'], $adr ) ){
				throw new Exception("Erreur lors de la mise à jour de l'adresse de livraison par défault.");
			}
		}

		// ajout des champs avancés
		if(isset($_REQUEST['fields'])) {
			$fields_delete_missing = (isset($_REQUEST['fields_delete_missing']) && !$_REQUEST['fields_delete_missing']) ? false : true;
			fields_sync(CLS_USER, $_REQUEST['id'], 0, 0, $_REQUEST['fields'], $fields_delete_missing);
		}

		// Maintenant que l'utilisateur et ses tables liées est modifié, on informe les intéréssés de ces modifications en envoyant une notification
		$r_new_usr = gu_users_get($_REQUEST['id']);
		if (!$r_new_usr ||  !ria_mysql_num_rows($r_new_usr) ) {
			$result = false;
			break;
		}

		// permet de forcer la sync à mettre à jour le contact, temporairement présent ici le tmeps que la tache ce base sur le need_sync des users
		//YUTO-1489
		if( !$is_sync ){
			gu_adresses_set_updated( $_REQUEST['id'], $usr['adr_invoices'], true );
		}

		$new_usr = ria_mysql_fetch_assoc($r_new_usr);

		push_notify_user_device_destinataires(NT_TYPE_USER_UPD, $old_usr, $new_usr);

		switch( $config['tnt_id'] ){
			case 59 :
			case 104 :
			case 105 :
				// lancement de l'export de la commande dans SalesForce en tâche asynchrone
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SYNC_SALESFORCE_USERS_SEND, array('id'=> $_REQUEST['id']));
				break;
		}

		$result = true;
		break;
	/** @{@}
 	 *	@{
	 * \page api-users-index-del Suppression
	 *
	 * Cette fonction supprime des utilisateurs
	 *
	 *		\code
	 *			DELETE /users/
	 *		\endcode
	 *
	 * @param int $id obligatoire, Tableau d'identifiant d'utilisateur
	 *
	 * @return true si l'utilisateur est bien supprimé
	 *	@}
	*/
	case 'del' :

		$old_usr = ria_mysql_fetch_assoc(gu_users_get($_REQUEST['id']));

		if( !isset($_REQUEST['id']) || !is_numeric($_REQUEST['id']) ){
			throw new Exception("Les identifiants fournis en arguments sont incorrects");
		}

		if( !isset($_REQUEST['is_sync'] ) || !$_REQUEST['is_sync'] ){
			$rusr = gu_users_get($_REQUEST['id']);
			if( $rusr && ria_mysql_num_rows($rusr) ){
				$usr = ria_mysql_fetch_assoc($rusr);
				if( $usr['is_sync'] ){
					throw new Exception("L'utilisateur ne peut etre supprimé car il est present sur la gestion commerciale");
				}
			}
		}

		if( !gu_users_del($_REQUEST['id']) ){
			throw new Exception("Erreur lors de la suppression de l'utilisateur");
		}else{
			$result = true;
		}

		// Maintenant que l'utilisateur et ses tables liées est supprimé, on informe les intéréssés de cette suppression en envoyant une notification
		push_notify_user_device_destinataires(NT_TYPE_USER_DELETE, $old_usr, null);
		break;
}

///@}