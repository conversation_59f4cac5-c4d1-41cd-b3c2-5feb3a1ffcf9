<?php
// Etape 1 : Script de telechargement des fichiers zip sur le FTP Corep
require_once('CorepImport.php');

$CorepImport = new CorepImport('download');
$CorepImport->connect();

if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage('Lancement du téléchargement');
}

// Verification de l'existence du repertoire source et de la presence de fichiers dans celui-ci
if (!$CorepImport->ftpDirectoryExistsAndContainsFiles()) {
    $error = 'Le répertoire cible n\'existe pas ou est vide';
    if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
        $CorepImport->logMessage($error);
    }
    throw new Exception($error);
    die;
}

// Recuperation de la liste dess fichiers à importer sur le FTP
$msg = "Récupération de la liste des fichiers sur le FTP";
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg.". Veuillez patienter". PHP_EOL);
if ($CorepImport->getFilesFromFtp() === false) {
    $error = 'Le répertoire cible ne contient aucun fichier importable';
    if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
        $CorepImport->logMessage($error);
    }
    throw new Exception($error);
    die;
}

// Telechergament des fichiers
$msg = "Téléchargement des fichiers depuis le FTP";
if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
    $CorepImport->logMessage($msg);
}
echo($msg.". Veuillez patienter". PHP_EOL);
if ($CorepImport->downloadFiles() === false) {
    $error = 'Impossible de télécharger les fichiers depuis le FTP';
    if ($CorepImport::_COREP_ENABLE_LOG_ === true) {
        $CorepImport->logMessage($error);
    }
    throw new Exception($error);
    exit;
}

if (!empty($CorepImport->errors)) {
    echo(implode('\n', $CorepImport->errors));
}

$success = sprintf("%d fichiers téléchargés sur %d", $CorepImport->counters['downloaded_files'], $CorepImport->counters['ftp_files']);
echo($success. PHP_EOL);

echo("Fin du téléchargement. Executez le script integrate.php pour intégrer les produits". PHP_EOL);

// Suite - étape 2 : fichier intergate.php