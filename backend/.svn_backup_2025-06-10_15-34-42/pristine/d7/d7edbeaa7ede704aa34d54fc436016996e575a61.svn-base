<?php
/**
 * \defgroup sms Envoi de SMS
 * \ingroup marketplace
 * Ce module gère la configuration d'envoi SMS
 */

/**
 * \defgroup sms_partners Partenaire d'envoi de SMS
 * \ingroup sms
 * Ce module contient la classse SMS_Partners
 */

/**\class SMS_Partners
 * \brief Cette classe fourni une abstraction pour la gestion de partenaires pour l'envoi de SMS
 */
class SMS_Partners {

	/** Cette fonction permet de récuperer les partenaires
	 * \param $id Optionnel, identifiant d'un partenaire
	 *
	 *\return Le résultat de cette fonction est retourné sous la forme d'un résultat de requête MySQL, comprenant les colonnes suivantes :
	 *        - id : Identifiant du partenaire
	 *        - name : Nom du partenaire
	 *        - desc : description du partenaire
	 *        - url : url vers le site du partenaire
	 *        - api_params : configuration de l'api pour ce tenant chez un partenaire
	 *        - date_created : date de création du partenaire
	 */
	public static function getPartners( $id=0 ){
		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}

		global $config;

		$sql = '
			select ptn_id as id, ptn_name as name, ptn_desc as desc, ptn_url as url, ptn_api_params as api_params, ptn_date_created as date_created
			from sms_partners
			where ptn_date_deleted is null
		';

		if( $id ){
			$sql .= '
				and ptn_id='.$id.'
			';
		}

		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}
	}

	/** Cette fonction permet d'ajouter des partenaires
	 * @param string $name Nom du partenaire
	 * @param string $desc Optionnel, description du partenaire
	 * @param string $url Optionnel, url du site du partenaire
	 *
	 * @return int L'identifiant du partenaire créé, false en cas d'échec
	 */
	public static function addPartners( $name, $desc='', $url='' ){
		global $config;

		if( !is_string( $name ) || trim( $name ) == '' ){
			return false;
		}

		if( !is_string( $desc ) ){
			return false;
		}

		if( !is_string( $url ) || !filter_var( $url, FILTER_VALIDATE_URL ) ){
			return false;
		}

		$data = array(
			'cpg_tnt_id'       => $config['tnt_id'],
			'ptn_name'         => '"'.addslashes( $name ).'"',
			'ptn_desc'         => '"'.addslashes( $desc ).'"',
			'ptn_url'          => '"'.filter_var( $url, FILTER_SANITIZE_URL ).'"',
			'ptn_date_created' => 'now()'
		);

		$keys = implode( ',', array_keys( $data ) );
		$values = implode( ',', array_values( $data ) );

		$sql = '
			insert into sms_partners
			('.$keys.') values ('.$values.')
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return ria_mysql_insert_id();
	}

	/** Cette fonction permet de modifier un partner
	 * \param  $id Identifiant du partenaire
	 * \param  $name Optionnel, nom du partenaire
	 * \param  $desc Optionnel, description du partenaire
	 * \param  $url Optionnel, url du partenaire
	 *
	 * \return true si succès, false en cas d'échec
	 */
	public static function updatePartners( $id, $name = '', $desc = '', $url = '' ){
		if( !is_numeric( $id ) || $id <= 0 ){
			return false;
		}

		if( !is_string( $name ) || trim( $name ) == '' ){
			return false;
		}

		if( !is_string( $desc ) ){
			return false;
		}

		if( !is_string( $url ) || !filter_var( $url, FILTER_VALIDATE_URL ) ){
			return false;
		}

		$data = array(
			'ptn_name="'.addslashes( trim( $name ) ).'"',
			'ptn_desc="'.addslashes( trim( $desc ) ).'"',
			'ptn_url="'.filter_var( $url, FILTER_SANITIZE_URL ).'"'
		);


		if( !sizeof( $data ) ){
			return false;
		}

		$sql = 'update sms_partners set';
		$sql .= implode( ', ', $data );
		$sql .= '
			where ptn_id='.$id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de supprimer un partenaire virtuelement de la base de donnée
	 * \param  $id Identifiant du partenaire
	 *
	 * \return l'identifiant du partenaire éliminer, false si échec
	 */
	public static function deletePartners( $id ){
		if( !is_numeric( $id ) || $id <= 0 ){
			return false;
		}

		$sql = '
			update table sms_partners set
			ptn_date_deleted = now()
			where ptn_id='.$id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return $id;
	}

	/**	Cette fonction permet de récupérer pour un tenant les configurations partenaires cpg
	 *	@param int $tnt_id Optionnel, identifiant d'un tenant
	 *	@param int $ptn_id Optionnel, identifiant d'un partenaire
	 *	@param bool $with_details Facultatif, si true récupère aussi la config api
	 *	@param bool $is_activated Optionnel, booléan si le partenaire est activé ou non pour un tenant, null si peux importe
	 *
	 *	@return resource Le résultat de cette fonction est retourné sous la forme d'un résultat de requête MySQL, comprenant les colonnes suivantes :
	 *            - tnt_id : identifiant du tenant
	 *            - ptn_id :identifiant du partenaire
	 *            - name : Nom du partenaire
	 *            - api_params : configuration de l'api pour ce tenant chez un partenaire
	 *            - is_activated : 1 si activé, 0 si désactivé
	 *            - date_activated : date d'activation
	 */
	public static function getPartnersTenants( $tnt_id = 0, $ptn_id = 0, $with_details=true, $is_activated = true ){
		if( !is_numeric( $tnt_id ) || $tnt_id < 0 ){
			return false;
		}

		if( !is_numeric( $ptn_id ) || $ptn_id < 0 ){
			return false;
		}

		$sql = '
			select ptt_tnt_id as tnt_id, ptt_ptn_id as ptn_id, ptn_name as name, ptt_is_active as ptt_is_active, ptt_date_activated as date_activated
			';
		if($with_details){
			$sql .= ', ptn_api_params as api_params ';
		}
		$sql .='
			from sms_partners_tenants
			left join sms_partners on ptn_id=ptt_ptn_id
			where ptt_tnt_id = '.$tnt_id.'
		';
		if($ptn_id){
			$sql .= 'and ptt_ptn_id = '.$ptn_id.' ';
		}

		if( $is_activated !== null ){
			if( $is_activated ){
				$sql .= ' and ptt_is_active = 1';
			}else{
				$sql .= ' and ptt_is_active = 0';
			}
		}
		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}

		return $res;
	}

	/** Cette fonction permet pour un tenant d'ajouter une configuration et d'activé  ou non un partenaire
	 * \param $tnt_id Obligatoire, Identifiant du tenant
	 * \param $ptn_id Obligatoire, Identifiant du partenaire
	 * \param $api_params Obligatoire, chaine de caractère JSON qui contient les paramètres de connexion à l'api
	 * \param $is_activated Optionnel, booléan true par défaut false pour ne pas activé.
	 *
	 * \return true si succès sinon false si éche
	 */
	public static function addPartnersTenants(
		$tnt_id,
		$ptn_id,
		$api_params,
		$is_activated = true
	){
		if( !is_numeric( $tnt_id ) || $tnt_id <= 0 ){
			return false;
		}

		if( !is_numeric( $ptn_id ) || $ptn_id <= 0 ){
			return false;
		}

		if( !is_string( $api_params ) || trim( $api_params ) == '' ){
			return false;
		}

		$sql = '
			insert into sms_partners_tenants
				( ptt_tnt_id, ptt_ptn_id, ptt_is_activated, ptt_api_params'.( !$is_activated ? : ', ptt_date_activated' ).' )
			values
				( '.$tnt_id.', '.$ptn_id.', "'.addslashes( $api_params ).'", '.( $is_activated ? '1, now()' : '0' ).' )
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de modifier un configuration d'un lien tenant/partenaire ou d'activer/désactiver un partenaire
	 * \param $tnt_id Identifiant du tenant
	 * \param $ptn_id Identifiant du partenaire
	 * \param $api_params Optionnel, chaine de caractère JSON qui contient les paramètres de connexion à l'api
	 * \param $is_activated Optionnel, booléan true par défaut false pour ne pas activé ou null pour ne pas modifier.
	 *
	 * \return true si succès sinon false
	 */
	public static function updatePartnersTenants(
		$tnt_id,
		$ptn_id,
		$api_params = false,
		$is_activated = true
	){
		if( !is_numeric( $tnt_id ) || $tnt_id <= 0 ){
			return false;
		}

		if( !is_numeric( $ptn_id ) || $ptn_id <= 0 ){
			return false;
		}

		if( $api_params && ( !is_string( $api_params ) || trim( $api_params ) === '' ) ){
			return false;
		}

		$sql = '
			update sms_partners_tenants set
		';

		$data = array();

		if( !is_null( $is_activated ) ){
			$data[] = 'ptt_is_activated = '.( $is_activated ? '1' : '0' );

			if( $is_activated ){
				$data[] = '
					ptt_date_activated = now()
				';
			}
		}


		if( $api_params ){
			$data[] = '
				ptt_api_params = "'.addslashes( trim( $api_params ) ).'"
			';
		}

		if( sizeof( $data ) ){
			$sql .= implode( ', ', $data ).' ';
		}


		$sql .= '
			where ptt_tnt_id = '.$tnt_id.'
				and ptt_ptn_id = '.$ptn_id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de supprimer de la base données un lien tenant/partenaire
	 * \param $tnt_id Identifiant du tenant
	 * \param $ptn_id Identifiant du partenaire
	 *
	 *\return true en cas de succès, sinon false en cas d'échec
	 */
	public static function delPartnersTenants( $tnt_id, $ptn_id ){
		if( !is_numeric( $tnt_id ) || $tnt_id <= 0 ){
			return false;
		}

		if( !is_numeric( $ptn_id ) || $ptn_id <= 0 ){
			return false;
		}

		$sql = '
			delete from sms_partners_tenants
			where ptt_tnt_id = '.$tnt_id.'
				and ptt_ptn_id = '.$ptn_id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de récupérer les quota pour un partener et un tenant
	 * \param  $tnt_id Identifiant du tenant
	 * \param  $ptn_id Identifiant du partenaire
	 * \param  $id Identifiant du quota
	 *
	 * \return Le résultat est retourné sous la forme d'un résultat de requête MYSQL, avec les
	 * colonnes suivantes :
	 *                - tnt_id : identifaitn du tenant
	 *                - ptn_id : Identifiant du partenaire
	 *                - qte : La quantité de cpg utilisable
	 * 				  - qte_use : La quantité de quota utilisé
	 *                - date_credited : date a laquel a été créditer le tenant en cpg
	 *                - date_expires : date d'expiration des cpg
	 */
	public static function getPartnersQuota( $tnt_id, $ptn_id, $id = 0 ){
		if( !is_numeric( $tnt_id ) || $tnt_id <= 0 ){
			return false;
		}

		if( !is_numeric( $ptn_id ) || $ptn_id <= 0 ){
			return false;
		}

		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}
		global $config;
		$sql = '
			select qta_tnt_id as tnt_id, qta_id as id, qta_ptn_id as ptn_id, qta_qte as qte, qta_qte_use as qte_use, qta_date_credited as date_credited, qta_date_expired as date_expired
			from sms_partners_quota
			where qta_tnt_id='.$config['tnt_id'].'
				and qta_ptn_id='.$ptn_id.'
				and qta_date_deleted is null
				and qta_date_expired > now()
				and qta_qte != qta_qte_use
			order by qta_date_expired asc;
		';

		if( $id ){
			$sql .= ' and qta_id='.$id;
		}

		$res = ria_mysql_query( $sql );

		if( !$res || !ria_mysql_num_rows( $res ) ){
			return false;
		}

		return $res;
	}


	public static function getAllUsedQuota(){

		global $config;

		$sql = '
			select sum(qta_qte_use) as qte_used
			from sms_partners_quota
			where qta_tnt_id='.$config['tnt_id'].'
		';
		$r = ria_mysql_query($sql);

		if( !$r || !ria_mysql_num_rows($r) ){
			return false;
		}

		return ria_mysql_result($r, 0, 0);
	}
	/** Cette fonction permet d'ajouter un quota pour un partener et un tenant
	 * \param  $tnt_id Identifiant du tenant
	 * \param  $ptn_id Idnetifiant du partenaire
	 * \param  $qte Quantité de cpg
	 * \param  $date_expires Date d'expiration des cpg
	 *
	 * \return true si succès, false si erreur
	 */
	public static function addPartnersQuota( $tnt_id, $ptn_id, $qte, $date_expires = null ){
		if( !is_numeric( $tnt_id ) || $tnt_id <= 0 ){
			return false;
		}

		if( !is_numeric( $ptn_id ) || $ptn_id <= 0 ){
			return false;
		}

		if( !is_numeric( $qte ) || $qte <= 0 ){
			return false;
		}

		if( !is_null($date_expires) && !isdate( $date_expires ) ){
			return false;
		}

		$sql = '
			insert into sms_partners_quota
				(qta_tnt_id, qta_ptn_id, qta_qte, qta_qte_use, qta_date_credited'.( !is_null( $date_expires ) ? ', qta_date_expired)' : '' ).')
			values
				('.$tnt_id.', '.$ptn_id.', '.$qte.',0 , now()'.( !is_null( $date_expires ) ? ', "'.$date_expires.'")' : 'DATE_ADD(now(), INTERVAL 100 YEAR)' ).')
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de modifier le quota pour un partenaire
	 * \param $id Identifiant du partenaire
	 * \param $qte Quantité
	 * \param $qte_use Quantité déjà utilisée
	 * \param $date_expires date d'expiration
	 *
	 * \return true si succès, false si échec
	 */
	public static function updatePartnersQuota( $id, $qte=0, $qte_use=0, $date_expires=null ){
		if( !is_numeric( $id ) || $id < 0 ){
			return false;
		}

		if( !is_numeric( $qte ) || $qte < 0 ){
			return false;
		}
		if( !is_numeric( $qte_use ) || $qte_use < 0 ){
			return false;
		}

		if( !is_null( $date_expires ) && !isdate( $date_expires ) ){
			return false;
		}
		global $config;

		$sql = '
			update sms_partners_quota set
		';

		$data = array();

		if( $qte ){
			$data[] = 'qta_use='.$qte;
		}
		if( $qte_use ){
			$data[] = 'qta_qte_use='.$qte_use;
		}

		if( !is_null( $date_expires ) ){
			$data[] = 'qta_date_expired="'.$date_expires.'"';
		}

		if( !sizeof( $data ) ){
			return false;
		}

		$sql .= implode( ', ', $data );

		$sql .= '
			where qta_tnt_id='.$config['tnt_id'].'
				and qta_id='.$id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}

	/** Cette fonction permet de supprimer un quota
	 * \param $tnt_id Identifiant du tenant
	 * \param $ptn_id Identifiant du partenaire
	 *
	 * \return true si succès, false si échec
	 */
	public static function delPartnersQuota( $tnt_id, $ptn_id ){
		if( !is_numeric( $tnt_id ) || $tnt_id <= 0 ){
			return false;
		}

		if( !is_numeric( $ptn_id ) || $ptn_id <= 0 ){
			return false;
		}

		$sql = '
			update sms_partners_quota set
			qta_date_deleted = now()
			where qta_tnt_id='.$tnt_id.'
				and qta_ptn_id='.$ptn_id.'
		';

		$res = ria_mysql_query( $sql );

		if( !$res ){
			return false;
		}

		return true;
	}
}