<?php

require_once('db.inc.php');
require_once('i18n.inc.php');

/**
 * Ajouter un zone à une image
 *
 * @param int $img_id Identifiant de l'image
 * @param float $x Position x de la zone en ratio de l'image source
 * @param float $y Position y de la zone en ratio de l'image source
 * @param float $width Largeur de la zone en ratio de l'image source
 * @param float $height Hauteur de la zone en ratio de l'image source
 * @param int $cls_id Classe de objet auquel est attachée l'image
 * @param int $obj_id_0 Optionnel, premier identifiant de objet auquel est attachée l'image
 * @param int $obj_id_1 Optionnel, deuxième identifiant de objet auquel est attachée l'image
 * @param int $obj_id_2 Optionnel, troisième identifiant de objet auquel est attachée l'image
 * @param string|null $title Optionnel, titre
 * @param string|null $desc Optionnel, description
 * @param int|null $href_type Optionnel, Type de lien @see define.inc.php
 * @param int|null $href Optionnel, Lien
 * @param int|null $href_target Optionnel, Cible de la balise <a> décrivant le lien
 * @param string|null $lng_code Optionnel, langue (Prends la langue courante par défaut)
 * @param int $wst_id Optionnel, identifiant sur lequel est appliqué la zone de clique (par défaut site courant donc site par défaut)
 *
 * @return bool False en cas d'erreur sinon le résultat de mysql_query()
 */
function img_zones_add(
    $img_id,
    $x,
    $y,
    $width,
    $height,
    $cls_id = null,
    $obj_id_0 = 0,
    $obj_id_1 = 0,
    $obj_id_2 = 0,
    $title = null,
    $desc = null,
    $href_type = null,
    $href = null,
    $href_target = null,
    $lng_code = null,
    $wst_id = 0
) {
    global $config;
    if (!is_numeric($img_id)
        || !is_numeric($x)
        || !is_numeric($y)
        || !is_numeric($width)
        || !is_numeric($height)
        || !is_numeric($obj_id_0)
        || !is_numeric($obj_id_1)
        || !is_numeric($obj_id_2)
        || (!is_null($cls_id) && !is_numeric($cls_id))
        || (!is_null($title) && (!is_string($title) || strlen($title = trim($title)) > 255))
        || (!is_null($desc) && (!is_string($desc) || strlen($desc = trim($desc)) > 65536))
        || (!is_null($lng_code) && (!is_string($lng_code) || strlen($lng_code = trim($lng_code)) <= 0 || strlen($lng_code) > 5))
        || (!is_null($href_type) && !is_numeric($href_type))
        || (!is_null($href) && (!is_string($href) || strlen($href = trim($href)) > 255))
        || (!is_null($href_target) && (!is_string($href_target) || strlen($href_target = trim($href_target)) > 255))
        || !is_numeric($wst_id)
    ) {
        return false;
    }

	$values = array(
		'imz_tnt_id' => $config['tnt_id'],
		'imz_wst_id' => $wst_id > 0 ? $wst_id : $config['wst_id'],
		'imz_lng_code' => "'".( is_null( $lng_code ) ? i18n::getLang() : $lng_code )."'",
		'imz_img_id' => $img_id,
		'imz_cls_id' => is_null($cls_id) ? '0' : $cls_id,
		'imz_obj_id_0' => $obj_id_0==0 ? '0' : $obj_id_0,
		'imz_obj_id_1' => $obj_id_1==0 ? '0' : $obj_id_1,
		'imz_obj_id_2' => $obj_id_2==0 ? '0' : $obj_id_2,
		'imz_x' => $x,
        'imz_y' => $y,
        'imz_width' => $width,
        'imz_height' => $height,
		'imz_title' => is_null($title) ? 'NULL' : "'".addslashes($title)."'",
		'imz_desc' => is_null($desc) ? 'NULL' : "'".addslashes($desc)."'",
		'imz_href_type' => is_null($href_type) ? 'NULL' : "'".addslashes($href_type)."'",
		'imz_href' => is_null($href) ? 'NULL' : "'".addslashes($href)."'",
		'imz_href_target' => is_null($href_target) ? 'NULL' : "'".addslashes($href_target)."'",
		'imz_date_created' => 'now()'
	);

	$res = ria_mysql_query('
		insert into img_zones
			('.implode( ',', array_keys($values) ).')
		values
			('.implode( ',', array_values($values) ).')
	');

	if( !$res ){
		error_log( ria_mysql_error() );
	}

    if ($res) {
        return ria_mysql_insert_id();
    }

    return false;
}

/** Permet le chargement d'une ou plusieurs zone(s) cliquable(s) d'image(s)
 *
 * @param int|false $id Identifiant de la zone d'image
 * @param string|false $lng_code Langue (Prends la langue courante par défaut)
 * @param int|false $img_id Identifiant de l'image
 * @param int|false $cls_id Classe de objet auquel est rattachée l'image
 * @param int|false $obj_id_0 Premier identifiant de objet auquel est rattachée l'image
 * @param int|false $obj_id_1 Deuxième identifiant de objet auquel est rattachée l'image
 * @param int|false $obj_id_2 Troisième identifiant de objet auquel est rattachée l'image
 * @param array|false $sort Tableau associatif de colonnes de tri ('colonne' => 'ASC|DESC')
 * @param int|false|null $wst_id Identifiant du website, false wst_id en cours (par défaut), null pour ne pas utiliser
 *
 * @return resource|bool False en cas d'erreur sinon le résultat de ria_mysql_query()
 */
function img_zones_get(
    $id = false,
    $lng_code = false,
    $img_id = false,
    $cls_id = false,
    $obj_id_0 = false,
    $obj_id_1 = false,
    $obj_id_2 = false,
    $sort = false,
    $wst_id = false
) {
    global $config;

    if ((false !== $id && !is_numeric($id))
        || (false !== $lng_code && (!is_string($lng_code) || strlen($lng_code) <= 0 || strlen($lng_code) > 5))
        || (false !== $cls_id && !is_numeric($cls_id))
        || (false !== $img_id && !is_numeric($img_id))
        || (false !== $obj_id_0 && !is_numeric($obj_id_0))
        || (false !== $obj_id_1 && !is_numeric($obj_id_1))
        || (false !== $obj_id_2 && !is_numeric($obj_id_2))
    ) {
        return false;
    }
    $wst_id = $wst_id === false ? $config['wst_id'] : $wst_id;
    $wst_id = $wst_id === null ? null : (!is_numeric($wst_id) || $wst_id < 1 ? $config['wst_id'] : $wst_id);

	$sql = '
		select
			imz_id as id,
			imz_tnt_id as tnt_id,
			imz_cls_id as cls_id,
			imz_img_id as img_id,
			imz_x as x,
			imz_y as y,
			imz_width as width,
			imz_height as height,
			imz_obj_id_0 as obj_id_0,
			imz_obj_id_1 as obj_id_1,
			imz_obj_id_2 as obj_id_2,
			imz_wst_id as wst_id,
			imz_title as title,
			imz_desc as "desc",
			imz_href_type as href_type,
			imz_href as href,
			imz_href_target as href_target,
			imz_date_created as date_created,
			imz_date_modified as date_modified,
			imz_date_deleted as date_deleted
		from img_zones
		where imz_tnt_id = '.$config['tnt_id'].'
			and imz_date_deleted IS NULL
			and imz_lng_code = "'.( $lng_code ? $lng_code : i18n::getLang() ).'"
	';

    if( is_numeric($wst_id) ){
        $sql .=  ' and imz_wst_id = '.$wst_id;
    }

    if (false !== $id) {
        $sql .= ' and imz_id = '.$id;
    }

    if (false !== $img_id) {
        $sql .= ' and imz_img_id = '.$img_id;
    }

    if (false !== $cls_id) {
        $sql .= ' and imz_cls_id = '.$cls_id;
    }

    if (false !== $obj_id_0) {
        $sql .= ' and imz_obj_id_0 = '.$obj_id_0;
    }

    if (false !== $obj_id_1) {
        $sql .= ' and imz_obj_id_1 = '.$obj_id_1;
    }

    if (false !== $obj_id_2) {
        $sql .= ' and imz_obj_id_2 = '.$obj_id_2;
    }

    if (is_array($sort)) {
        $orderBySql = '';
        foreach ($sort as $key => $value) {
            $orderBySql .= (empty($orderBySql) ? ' ' : ', ') . addslashes($key) . ' ' . (is_string($value) && strtoupper($value) === 'DESC' ? 'DESC' : 'ASC');
        }

        if (!empty($orderBySql)){
            $sql .= ' order by' . $orderBySql;
        }
    }

    return ria_mysql_query($sql);
}

/**
 * Mise à jour  d'une zone d'image
 * set x|y|width|height|title|desc|href_type|href|href_target
 * where img_id|lng_code|cls_id|obj_id_0|obj_id_1|obj_id_2
 *
 * @param int|false $img_id Identifiant de l'image
 * @param int|false $lng_code Langue (Prends la langue courante par défaut)
 * @param int|false $cls_id Classe de objet auquel est rattachée l'image
 * @param int|false $obj_id_0 Premier identifiant de objet auquel est rattachée l'image
 * @param int|false $obj_id_1 Deuxième identifiant de objet auquel est rattachée l'image
 * @param int|false $obj_id_2 Troisième identifiant de objet auquel est rattachée l'image
 * @param float|false $x Position x de la zone en ratio de l'image source
 * @param float|false $y Position y de la zone en ratio de l'image source
 * @param float|false $width Largeur de la zone en ratio de l'image source
 * @param float|false $height Hauteur de la zone en ratio de l'image source
 * @param string|false $title Titre
 * @param string|false $desc Description
 * @param int $wst_id Optionnel, identifiant du site sur lequel la zone est appliquée, par défaut il s'agit du site par défaut
 *
 * @return bool False en cas d'erreur sinon le résultat de mysql_query()
 */
function img_zones_update(
    $id = false,
    $lng_code = false,
    $img_id = false,
    $cls_id = false,
    $obj_id_0 = false,
    $obj_id_1 = false,
    $obj_id_2 = false,
    $x = false,
    $y = false,
    $width = false,
    $height = false,
    $title = false,
    $desc = false,
    $href_type = false,
    $href = false,
    $href_target = false,
    $wst_id = 0
) {
    global $config;

    if ((false !== $id && !is_numeric($id))
        || (false !== $lng_code && (!is_string($lng_code) || strlen($lng_code) <= 0 || strlen($lng_code) > 5))
        || (false !== $cls_id && !is_numeric($cls_id))
        || (false !== $img_id && !is_numeric($img_id))
        || (false !== $x && !is_numeric($x))
        || (false !== $y && !is_numeric($y))
        || (false !== $width && !is_numeric($width))
        || (false !== $height && !is_numeric($height))
        || (false !== $obj_id_0 && !is_numeric($obj_id_0))
        || (false !== $obj_id_1 && !is_numeric($obj_id_1))
        || (false !== $obj_id_2 && !is_numeric($obj_id_2))
        || (false !== $title && (!is_string($title) || strlen($title = trim($title)) > 255))
        || (false !== $desc && (!is_string($desc) || strlen($desc = trim($desc)) > 65536))
        || (false !== $href_type && !is_numeric($href_type))
        || (false !== $href && (!is_string($href) || strlen($href = trim($href)) > 255))
        || (false !== $href_target && (!is_string($href_target) || strlen($href_target = trim($href_target)) > 255))
        || !is_numeric($wst_id)
    ) {
        return false;
    }

    $setSql = '';

    if (false !== $x) {
        $setSql .= (empty($setSql) ? ' ' : ', ') . '`imz_x` = ' . floatval($x);
    }

    if (false !== $y) {
        $setSql .= (empty($setSql) ? ' ' : ', ') . '`imz_y` = ' . floatval($y);
    }

    if (false !== $width) {
        $setSql .= (empty($setSql) ? ' ' : ', ') . '`imz_width` = ' . floatval($width);
    }

    if (false !== $height) {
        $setSql .= (empty($setSql) ? ' ' : ', ') . '`imz_height` = ' . floatval($height);
    }

    if (false !== $title) {
        $setSql .= (empty($setSql) ? ' ' : ', ') . '`imz_title` = \'' . addslashes($title) . '\'';
    }

    if (false !== $desc) {
        $setSql .= (empty($setSql) ? ' ' : ', ') . '`imz_desc` = \'' . addslashes($desc) . '\'';
    }

    if (false !== $href_type) {
        $setSql .= (empty($setSql) ? ' ' : ', ') . '`imz_href_type` = ' . intval($href_type);
    }

    if (false !== $href) {
        $setSql .= (empty($setSql) ? ' ' : ', ') . '`imz_href` = \'' . addslashes($href) . '\'';
    }

    if (false !== $href_target) {
        $setSql .= (empty($setSql) ? ' ' : ', ') . '`imz_href_target` = \'' . addslashes($href_target) . '\'';
    }

    if (empty($setSql)) {
        return false;
    }

    $whereSql = '';

    if (false !== $id) {
        $whereSql .= sprintf(' and `imz_id` = %d', intval($id));
    } else {
        if (false !== $cls_id) {
            $whereSql .= sprintf(' and `imz_cls_id` = %d', intval($cls_id));
        }

        if (false !== $img_id) {
            $whereSql .= sprintf(' and `imz_img_id` = %d', intval($img_id));
        }

        if (false !== $obj_id_0) {
            $whereSql .= sprintf(' and `imz_obj_id_0` = %d', intval($obj_id_0));
        }

        if (false !== $obj_id_1) {
            $whereSql .= sprintf(' and `imz_obj_id_1` = %d', intval($obj_id_1));
        }

        if (false !== $obj_id_2) {
            $whereSql .= sprintf(' and `imz_obj_id_2` = %d', intval($obj_id_2));
        }
    }

    $res = ria_mysql_query(
        sprintf(
            <<<SQL
            update `img_zones`
                set %s
            where `imz_tnt_id` = %d
                and `imz_wst_id` = %d
                and `imz_lng_code` = '%s'
                and `imz_date_deleted` IS NULL
                %s
SQL
            ,
            $setSql,
            intval($config['tnt_id']),
            intval($wst_id > 0 ? $wst_id : $config['wst_id']),
            addslashes(false !== $lng_code ? $lng_code : i18n::getLang()),
            $whereSql
        )
    );

    if( !$res ){
        error_log( ria_mysql_error());
    }

    return $res;
}

/** Cette fonction permet de supprimer une zone cliquable sur une image pour un contenu.
 *  @param int $id Identifiant de l'image
 *  @param string|false $lng_code Langue (Ne prends pas la langue courante par défaut!)
 *  @param int $wst_id Optionnel, identifiant du site pour laquelle la zone a été créé, par défaut il s'agit du site par défaut
 *  @return resource|bool False en cas d'erreur sinon le résultat de ria_mysql_query()
 */
function img_zones_delete($id, $lng_code=false, $wst_id=0) {
    global $config;

    if (!is_numeric($id)
        || (false !== $lng_code && (!is_string($lng_code) || strlen($lng_code) <= 0 || strlen($lng_code) > 5))
        || !is_numeric($wst_id)
    ) {
        return false;
    }

    return ria_mysql_query(
        sprintf(
            'update `img_zones` set `imz_date_deleted` = CURRENT_TIMESTAMP where `imz_tnt_id` = %d and `imz_wst_id` = %d and `imz_id` = %d%s',
            intval($config['tnt_id']),
            intval($wst_id > 0 ? $wst_id : $config['wst_id']),
            intval($id),
            false !== $lng_code ? ' and `imz_lng_code` = \'' . addslashes($lng_code) . '\'' : ''
        )
    );
}

/**
 * Vue des zones d'une image
 * La fonction view_img doit être définie (@see view.site.inc.php)
 * Positionne les zones sur l'image en fonction de la taille et des filtres appliqués (@todo Compatible fill et crop uniquement)
 *
 * @param int $img_id Identifiant de l'image
 * @param array $size Configuration de la taille de l'image (@see cfg_images)
 * @param string $alt Texte de remplacement de l'image
 * @param string $title Titre de l'image
 * @param string $img_class Class CSS de la balise image
 * @param int|false $zone_id  Identifiant de la zone d'image
 * @param string|false $lng_code Code de la langue courante
 * @param int|false $cls_id Contexte de l'image : classe d'objet
 * @param int|false $obj_id_0 Contexte de l'image : premier identifiant
 * @param int|false $obj_id_1 Contexte de l'image : second identifiant
 * @param int|false $obj_id_2 Contexte de l'image : troisième identifiant
 * @param array|false $sort Option de tri (attribut => sens de tri)
 *
 * @return string Vue HTML
 */
function img_zones_view(
    $img_id,
    $img_code,
    $alt = '',
    $title = '',
    $img_class = '',
    $id = false,
    $lng_code = false,
    $cls_id = false,
    $obj_id_0 = false,
    $obj_id_1 = false,
    $obj_id_2 = false,
    $sort = false
) {
    if (!function_exists('view_img')) {
        return '';
    }

    global $config;
    $size = $config['img_sizes'][$img_code];
    $html = view_img($img_id, $size, $alt, $title, false, $img_class);

    $zones = img_zones_get($id, $lng_code, $img_id, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2, $sort);

    if (!$zones || !ria_mysql_num_rows($zones) || !$img_code) {
        return $html;
    }

    $html = '<div class="image-zoning" style="display: block; position: relative;">'
        . $html;

    $ar_filter = array();
    $rfilter = img_filters_get($img_code);
    if ($rfilter && ria_mysql_num_rows($rfilter)) {
        while ($filter = ria_mysql_fetch_assoc($rfilter)) {
            if (isset($config['default_image']) && $config['default_image'] == $img_id) {
                $filter['filter_code'] = 'crop';
            }

            $ar_filter[] = $filter;
        }
    }

    $baseSize = img_images_source_infos($img_id);
    $r = $baseSize['width'] / $baseSize['height'];

    $sizeSecond = $size;
    $padding = array(
        'top' => 0,
        'left' => 0
    );

    foreach ($ar_filter as $filter) {
        switch ($filter['filter_code']) {
            case 'crop':
                $sizeSecond = $baseSize;
                $padding['top'] = ($size['height'] - $sizeSecond['height']) / 2;
                $padding['left'] = ($size['width'] - $sizeSecond['width']) / 2;
                break;

            case 'fill':
                $fullSide = $size['width'] > $size['height'] ? 'height' : 'width';
                $otherSide = $fullSide == 'width' ? 'height' : 'width';

                $sizeSecond[$fullSide] = $size[$fullSide];
                $sizeSecond[$otherSide] = $size[$fullSide] * ($fullSide == 'width' ? 1 / $r : $r);

                $padding['top'] = ($size['height'] - $sizeSecond['height']) / 2;
                $padding['left'] = ($size['width'] - $sizeSecond['width']) / 2;
                break;
        }
    }

    while ($zones && $zone = ria_mysql_fetch_assoc($zones)) {
        $coords = array();
        $coords[] = ceil($padding['left'] + $sizeSecond['width'] * floatval($zone['x']));
        $coords[] = ceil($padding['top'] + $sizeSecond['height'] * floatval($zone['y']));
        $coords[] = ceil($sizeSecond['width'] * floatval($zone['width']));
        $coords[] = ceil($sizeSecond['height'] * floatval($zone['height']));


        $html .= '<a style="position: absolute;'
            . ' left:' . 100 * ($coords[0] / $size['width']) . '%;'
            . ' top: ' . 100 * ($coords[1] / $size['height']) . '%;'
            . ' width: ' . 100 * ($coords[2] / $size['width']) . '%;'
            . ' height: ' . 100 * ($coords[3] / $size['height']) . '%;'
            . '" href="' . htmlspecialchars($zone['href']) . '"'
            . ' target="' . htmlspecialchars($zone['href_target']) . '"'
            . ' >&nbsp;</a>';
    }

    return view_site_replace_href($html, false, true, false) . '</div>';
}