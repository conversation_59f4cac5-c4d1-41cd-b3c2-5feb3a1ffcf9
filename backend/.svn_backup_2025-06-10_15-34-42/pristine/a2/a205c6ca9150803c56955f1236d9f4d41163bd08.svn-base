<?php
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à accès à cette page
	if( isset($_GET['cat']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_CATEG_EDIT');
	}elseif( !isset($_GET['cat']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_CATEG_ADD');
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Vérifie la validité de l'identifiant d'unité passé en paramètre
	if( isset($_GET['cat']) && $_GET['cat']!=0 ){
		if( !fld_categories_exists($_GET['cat']) ){
			header('Location: index.php');
			exit;
		}
	}

	unset($error);

	// Bouton Supprimer
	if( isset($_POST['del']) ){
		if( !fld_categories_del($_GET['cat']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la catégorie.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php');
			exit;
		}
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) ){
		if( !isset($_POST['name']) || !trim($_POST['name']) )
			$error = _("Veuillez indiquer le nom de la catégorie de champs.");
		elseif( !isset($_POST['desc']) )
			$error = _("Une ou plusieurs informations obligatoires sont manquantes.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		elseif( isset($_GET['cat']) && $_GET['cat']==0 ){
			if( !isset($_POST['classtype']) || !is_numeric($_POST['classtype']) || $_POST['classtype']<=0 )
				$error = _("Le type de données de la catégorie n'a pas été sélectionné ou est invalide.");
			else{
				// Ajout
				$res = fld_categories_add($_POST['name'],$_POST['desc'],$_POST['classtype']);
				if( $res===ERR_NAME_EXISTS )
					$error = _("Une catégorie portant le même nom existe déjà.\nVeuillez choisir un autre nom.");
				elseif( $res==false )
					$error = "Une erreur inattendue s'est produite lors de l'enregistrement de la catégorie.\nVeuillez réessayer ou prendre contact avec l'administrateur.";
			}
		}elseif( isset($_GET['cat']) && $_GET['cat']>0 ){
			// Modification
			$res = fld_categories_update($_GET['cat'],$_POST['name'],$_POST['desc']);
			if( $res===ERR_NAME_EXISTS )
				$error =_( "Une catégorie portant le même nom existe déjà.\nVeuillez choisir un autre nom.");
			elseif( !$res )
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la catégorie.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}
	
	// Chargement
	$cat = array('id'=>0,'name'=>'','desc'=>'','cls_id'=>CLS_PRODUCT);
	if( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat']>0 ){
		$cat = ria_mysql_fetch_array(fld_categories_get($_GET['cat']));
	}
	if( isset($_POST['name']) ) $cat['name'] = $_POST['name'];
	if( isset($_POST['desc']) ) $cat['desc'] = $_POST['desc'];

	// Défini le titre de la page
	$page_title = ( isset($cat['name']) && $cat['name']!='' ) ? _('Catégorie').' '.$cat['name'] : _('Nouvelle catégorie de champs');
	define('ADMIN_PAGE_TITLE', $page_title.' '._('Catégories de champs') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	// Affiche le titre de la page
	echo '<h2>'.htmlspecialchars( $page_title ).'</h2>'; 

	if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}

	?>

	<form action="edit.php?cat=<?php print $cat['id'] ?>" method="post" onsubmit="return fldCatValidForm(this)">
		<table>
			<tbody>
				<tr>
					<td><label for="name"><span class="mandatory">*</span> <?php echo _('Nom :'); ?></label></td>
					<td><input type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars($cat['name']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="classtype"><span class="mandatory">*</span> <?php echo _("S'applique aux :"); ?></label></td>
					<td>
						<?php $classes = fld_classes_get( 0, false, true, true, null, true ); ?>
						<select id="classtype" name="classtype" <?php print !isset($_GET['cat']) || $_GET['cat']==0 ? '' : 'disabled="disabled"'; ?>>
							<?php
							
							if( $classes ){
								while( $cls = ria_mysql_fetch_array($classes) ){
									print '<option value="'.$cls['id'].'" '.( $cls['id']==$cat['cls_id'] ? 'selected="selected"' : '' ).'>'.htmlspecialchars( $cls['name'] ).'</option>';
								}
							}
							?>
						</select>
					</td>
				</tr>
				<tr>
					<td><label for="desc"><?php echo _('Description :'); ?></label></td>
					<td><textarea name="desc" id="desc" rows="15" cols="40"><?php print htmlspecialchars($cat['desc']); ?></textarea></td>
				</tr>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
					<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="return fldCatCancelEdit()" />
					<?php if( $cat['id']>0 && gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_CATEG_DEL') ){ ?>
					<input type="submit" name="del" value="<?php echo _("Supprimer"); ?>" onclick="return fldCatConfirmDel()" />
					<?php } ?>
				</td></tr>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>