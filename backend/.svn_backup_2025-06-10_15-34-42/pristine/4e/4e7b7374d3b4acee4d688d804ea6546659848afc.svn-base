<?php

	/**	\file index.php
	 *	Cette page sert de fiche à une zone de livraison. Elle permet sa création, sa modification ainsi que sa suppression.
	 */

	require_once('delivery.inc.php');

	$_GET['zone'] = isset($_GET['zone']) ? $_GET['zone'] : 0; 
	
	// Vérifie que l'utilisateur a bien accès à la page
	if( $_GET['zone']!=0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_ZONE_VIEW');
	}else{ // $_GET['zone']==0
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_ZONE_ADD');
	}
	
	// Vérifie que l'utilisateur a bien le droit de modifier un service de livraison
	if( isset($_POST['save']) && $_GET['zone']!=0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_ZONE_EDIT');
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}


	// Vérifie la validité de l'identifiant de zone passé en paramètre
	if( isset($_GET['zone']) && $_GET['zone']!=0 ){
		if( !dlv_zones_exists($_GET['zone']) ){
			header('Location: index.php');
			exit;
		}
	}

	unset($error);

	// Suppression
	if( isset($_POST['del']) ){
		if( !dlv_zones_del($_GET['zone']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la zone.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php');
			exit;
		}
	}

	// Enregistrement
	if( isset($_POST['save']) ){
		if( isset($_POST['check_tab']) && $_POST['check_tab']=='general' ){
			$tab = 'general';
			// Récupère les données saisies pour pouvoir les replacer dans le formulaire en cas d'erreur
			$zone = array(
				'id'		=>0,
				'name'			=> $_POST['name'],
				'desc'			=> $_POST['desc'],
				'is_active'	=> (isset($_POST['active']) ? $_POST['active'] : 0),
				'services'	=> (isset($_POST['services']) ? $_POST['services'] : array()), 
				'type'			=> (isset($_POST['type']) ? $_POST['type'] : ''),
				'dlv_min'		=> (isset($_POST['dlv_min']) ? $_POST['dlv_min'] : ''),
				'dlv_max'		=> (isset($_POST['dlv_max']) ? $_POST['dlv_max'] : ''),
				'dlv_step'	=> (isset($_POST['dlv_step']) ? $_POST['dlv_step'] : ''),
				'dlv_days'	=> (isset($_POST['dlv_days']) ? $_POST['dlv_days'] : []),
			);

			$_POST['active'] = isset($_POST['active']);
			$_POST['services'] = isset($_POST['services']) ? $_POST['services'] : array();
			if( !isset($_POST['name']) || !trim($_POST['name']) ){
				$error = _("Veuillez indiquer le nom de la zone de livraison.");
			}elseif( !isset($_POST['desc']) ){
				$error = _("Une ou plusieurs informations obligatoires sont manquantes.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
			}elseif( isset($_POST['dlv_min']) && trim($_POST['dlv_min']) != '' && (!is_numeric($_POST['dlv_min']) || $_POST['dlv_min'] <= 0) ){
				$error = _('Le délai de livraison minimal doit être un entier supérieur à zéro.');
			}elseif( isset($_POST['dlv_max']) && trim($_POST['dlv_max']) != '' && (!is_numeric($_POST['dlv_max']) || $_POST['dlv_max'] <= 0) ){
				$error = _('Le délai de livraison minimal doit être un entier supérieur à zéro.');
			}elseif( isset($_POST['dlv_step']) && trim($_POST['dlv_step']) != '' && (!is_numeric($_POST['dlv_step']) || $_POST['dlv_step'] <= 0) ){
				$error = _('L\'heure limite de commande doit être un entier supérieur à zéro.');
			}elseif( isset($_GET['zone']) && $_GET['zone']==0 ){
				$delivery = [];

				if( isset($_POST['dlv_min']) && trim($_POST['dlv_min']) ){
					$delivery['min'] = $_POST['dlv_min'];
				}
				if( isset($_POST['dlv_max']) && trim($_POST['dlv_max']) ){
					$delivery['max'] = $_POST['dlv_max'];
				}
				if( isset($_POST['dlv_step']) && trim($_POST['dlv_step']) ){
					$delivery['step'] = $_POST['dlv_step'];
				}
				if( isset($_POST['dlv_days']) && is_array($_POST['dlv_days']) && count($_POST['dlv_days']) ){
					$delivery['days'] = implode(', ', $_POST['dlv_days']);
				}

				if( !isset($_POST['type']) ){
					$_POST['type'] = false;
				}

				// Ajout
				$idZone = dlv_zones_add( $_POST['name'], $_POST['desc'], $_POST['active'], $_POST['services'], $_POST['type'], $delivery );
				if( !$idZone ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la zone.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}else{
					if(isset($_POST['value_min'])){
						foreach($_POST['value_min'] as $key => $value){
							if(isset($_POST['cumule'][$key])){
								$_POST['cumule'][$key]=true;
							}else{
								$_POST['cumule'][$key]=false;
							}

							if(isset($_POST['prorata'][$key])){
								$_POST['prorata'][$key]=true;
							}else{
								$_POST['prorata'][$key]=false;
							}

							if(!isset($_POST['slice'][$key])){
								$_POST['slice'][$key]=false;
							}

							if(!isset($_POST['sl_price'][$key])){
								$_POST['sl_price'][$key]=false;
							}

							//récupére les données saisies pour pouvoir les replacer dans le formulaire en cas d'erreur
							$zone['price'][$key]=array('value_min' => $_POST['value_min'][$key], 
													'tarif' 	=> $_POST['tarif'][$key], 
													'prorata' => $_POST['prorata'][$key], 
													'cumule' 	=> $_POST['cumule'][$key],
													'slice' => $_POST['slice'][$key],
													'sl_price' 	=> $_POST['sl_price'][$key]);


							if(!$idPrices[]=dlv_package_prices_add( $_POST['tarif'][$key], prd_products_get_id($config['dlv_prd_references'][0]), 0, $_POST['cumule'][$key], $_POST['prorata'][$key], $_POST['value_min'][$key], $_POST['slice'][$key], $_POST['sl_price'][$key])){
								dlv_zones_del($idZone);
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement d'un tarif.\nVeuillez réessayer ou prendre contact avec l'administrateur.");

							}

						}

						// Récupère la zone la plus précise des zones renseignées
						$child_zone=array();
						foreach($_POST['zone'] as $key => $value){
							foreach($value as $k => $v){
								if($v != 0){
									$child_zone[$k]= $v;
								}
							}
						}

						// Récupère les données saisies pour pouvoir les replacer dans le formulaire en cas d'erreur
						foreach($child_zone as $key => $value){
							$zoneParent = $value;
							while($rZone = sys_zones_get_parent($value)){
								$zoneParent .= ",".$rZone;
								$value=$rZone; 
							}
							$zone['zone'][$key]=array_reverse (explode(",", $zoneParent));
						}

						if(!isset($error)){
							foreach($idPrices as $idPrice){
								foreach($child_zone as $key => $value){
									if(!dlv_package_price_zones_add($idPrice,$value,$idZone)){
										dlv_zones_del($idZone);
										$error = _("Une erreur inattendue s'est produite lors de l'enregistrement d'une zone géographique.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
									}
								}
							}
						}	

						if (!isset($error)){
							$_GET['zone'] = $idZone;
						}
					}
				}
			}elseif( isset($_GET['zone']) && $_GET['zone']>0 ){
				$delivery = [];

				if( isset($_POST['dlv_min']) && trim($_POST['dlv_min']) ){
					$delivery['min'] = $_POST['dlv_min'];
				}
				if( isset($_POST['dlv_max']) && trim($_POST['dlv_max']) ){
					$delivery['max'] = $_POST['dlv_max'];
				}
				if( isset($_POST['dlv_step']) && trim($_POST['dlv_step']) ){
					$delivery['step'] = $_POST['dlv_step'];
				}
				if( isset($_POST['dlv_days']) && is_array($_POST['dlv_days']) && count($_POST['dlv_days']) ){
					$delivery['days'] = implode(', ', $_POST['dlv_days']);
				}

				if( !isset($_POST['type']) ){
					$_POST['type'] = false;
				}

				// Modification
				if( !dlv_zones_update($_GET['zone'],$_POST['name'],$_POST['desc'],$_POST['active'],$_POST['services'], $_POST['type'], $delivery ) ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la zone.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}

				// Supprime les anciennes valeurs
				if( isset($config['dlv_active_port_config']) && $config['dlv_active_port_config'] ){
					$price_zone = dlv_package_price_zones_get(false, false, $_GET['zone']);
					while( $value = ria_mysql_fetch_array($price_zone) ){
						dlv_package_price_zones_del($value['dpp_id'], $value['dzn_id']);
						dlv_package_prices_del($value['dpp_id']);
					}
				}

				if( isset($_POST['value_min']) ){
					foreach($_POST['value_min'] as $key => $value){
						if(isset($_POST['cumule'][$key])){
							$_POST['cumule'][$key]=true;
						}else{
							$_POST['cumule'][$key]=false;
						}

						if(isset($_POST['prorata'][$key])){
							$_POST['prorata'][$key]=true;
						}else{
							$_POST['prorata'][$key]=false;
						}

						if(!isset($_POST['slice'][$key])){
							$_POST['slice'][$key]=false;
						}

						if(!isset($_POST['sl_price'][$key])){
							$_POST['sl_price'][$key]=false;
						}

						if(!$idPrices[]=dlv_package_prices_add( $_POST['tarif'][$key], prd_products_get_id($config['dlv_prd_references'][0]), 0, $_POST['cumule'][$key], $_POST['prorata'][$key], $_POST['value_min'][$key], $_POST['slice'][$key], $_POST['sl_price'][$key])){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement d'un tarif.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
						}
					}

					// Récupère la zone la plus précise des zone renseigné
					$child_zone=array();
					foreach($_POST['zone'] as $key => $value){
						foreach($value as $k => $v){
							if($v != 0){
								$child_zone[$k]= $v;
							}
						}
					}

					foreach($idPrices as $idPrice){
						if($idPrice != 0){
							foreach($child_zone as $key => $value){
								if(!dlv_package_price_zones_add($idPrice,$value,$_GET['zone'])){
									$error = _("Une erreur inattendue s'est produite lors de l'enregistrement d'une zone géographique.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
								}
							}
						}
						
					}
				}
			}
		} 
		if( !isset($error) ){
			if (isset($_GET['zone']) && $_GET['zone']>0 ) {
				header('Location: edit.php?zone='.$_GET['zone']);
				exit;
			} else {
				header('Location: index.php');
				exit;
			}
		}

	}
	
	$typeZone=array(
		1 => _("Choisissez une région *"),
		2 => _("Choisissez un département"),
		3 => _("Choisissez un état Américain"),
		4 => _("Choisissez un land Allemand"),
		5 => _("Choisissez un code postal"),
		6 => _("Choisissez une ville"),
		8 => _("Choisissez une région *"),
		9 => _("Choisissez un département"),
		13 => _("Choisissez un pays")
	);

	$tab = '';
	if( !$_GET['zone'] ){
		$tab = 'general';
	}

	// Chargement
	if( isset($_GET['zone']) && is_numeric($_GET['zone']) && $_GET['zone']>0 ){
		$zone = dlv_package_price_zones_get_all($_GET['zone']);
		$page_title = htmlspecialchars($zone['name']);
	}else{ // Initialise une zone de livraison vide
		$zone = array(
			'id'		=> 0,
			'name'		=> '',
			'desc'		=> '',
			'is_active'	=> false,
			'services'	=> array(), 
			'type'		=> 'qte', 
			'zone' 		=> array(),
			'price'		=> array(),
			'dlv_min' => '',
			'dlv_max' => '',
			'dlv_step' => '',
			'dlv_days' => []
		);
		$page_title = _('Nouvelle zone de livraison');
	}
	
	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Zones de livraison') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	// Les jours d'expéditions sont stockés sous forme de chaine de caractère, on la transforme en tableau
	if( !is_array($zone['dlv_days']) ){
		$zone['dlv_days'] = trim($zone['dlv_days']) != '' ? explode( ', ', $zone['dlv_days'] ) : [];
	}
?>
	<h2><?php print $page_title; ?></h2>

	<?php
		// Affichage des messages d'erreur
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<form action="edit.php?zone=<?php print $zone['id'] ?>" method="post">
		<input type="hidden" name="check_tab" id="check_tab" value="" />
		<input type="hidden" id="rowZone" value=<?php echo count($zone['zone']); ?> />
		<input type="hidden" id="rowPrice" value=<?php echo count($zone['price']); ?> />

		<ul class="tabstrip">
		<li><input type="submit" name="tabGeneral" value="Général" /></li>
		<?php if ($_GET['zone']>0 && isset($config['dlv_active_port_config']) && $config['dlv_active_port_config']){ ?>
			<li><input type="submit" name="tabProducts" value="Produits" /></li>
		<?php } ?>
		</ul>
		<div id="tabpanel">
			<table id="dlv-zones" class="dlv-zones">
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save" value="<?php echo _('Enregistrer'); ?>" />
					<input type="submit" name="cancel" value="<?php echo _('Annuler'); ?>" onclick="return zoneCancelEdit()" />
					<?php if( $zone['id']>0 && gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_ZONE_DEL') ){ ?>
					<input type="submit" name="del" value="<?php echo _('Supprimer'); ?>" onclick="return zoneConfirmDel()" />
					<?php } ?>
				</td></tr>
			</tfoot>
			<tbody>
				<tr>
					<td><span class="mandatory">*</span> <label for="name"><?php print _('Nom :'); ?></label></td>
					<td><input type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars($zone['name']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="desc"><?php echo _('Description :'); ?></label></td>
					<td><textarea name="desc" id="descZone" rows="15" cols="40"><?php print htmlspecialchars($zone['desc']); ?></textarea></td>
				</tr>
				<tr>
					<td><label for="active"><?php echo _('Activée :'); ?></label></td>
					<td><input type="checkbox" class="checkbox" name="active" id="active" <?php print $zone['is_active'] ? 'checked="checked"' : '' ?> /> <label for="active"><?php echo _("Accepter les commandes à livrer dans cette zone"); ?></label></td>
				</tr>

				<?php
					print '<tr><th colspan="2">'._('Livraison').'</th></tr>'
						.'<tr>'
							.'<td>'
								.'<label for="dlv_min">'._('Délai minimal :').'</label>'
							.'</td>'
							.'<td>'
								.'<input name="dlv_min" id="dlv_min" class="number" type="number" min="1" value="'.htmlspecialchars( $zone['dlv_min'] ).'" /> '._('jour(s) ouvré(s)')
							.'</td>'
						.'</tr>'
						.'<tr>'
							.'<td>'
								.'<label for="dlv_max">'._('Délai maximal :').'</label>'
							.'</td>'
							.'<td>'
								.'<input name="dlv_max" id="dlv_max" class="number" type="number" min="1" value="'.htmlspecialchars( $zone['dlv_max'] ).'" /> '._('jour(s) ouvré(s)')
							.'</td>'
						.'</tr>'
						.'<tr>'
							.'<td>'
								.'<label for="dlv_step">'._('Heure limite :').'</label>'
							.'</td>'
							.'<td>'
								.'<input name="dlv_step" id="dlv_step" class="number" type="number" min="1" value="'.htmlspecialchars( $zone['dlv_step'] ).'" /> '
								.'<p>'._('Précisez à partir de quelle heure les délais sont ralongés d\'une journée. Laissez vide pour ne pas ralonger les délais.
										<br />(ex. Passer 13h, l\'expédition des commandes est faite que le lendemain matin)').'</p>'
							.'</td>'
						.'</tr>'
						.'<tr>'
							.'<td>'
								.'<label for="day-1">'._('Expédition :').'</label>'
							.'</td>'
							.'<td>';

							$days = [1 => _('Lundi'), 2 => _('Mardi'), 3 => _('Mercredi'), 4 => _('Jeudi'), 5 => _('Vendredi'), 6 => _('Samedi'), 0 => _('Dimanche')];
							foreach( $days as $key=>$day ){
								$checked = '';
								if( is_array($zone['dlv_days']) && in_array($key, $zone['dlv_days']) ){
									$checked = 'checked="checked"';
								}

								print '<div>'
									.'<input '.$checked.' type="checkbox" name="dlv_days[]" id="day-'.$key.'" value="'.$key.'" />'
									.'<label for="day-'.$key.'">'.htmlspecialchars($day).'</label>'
								.'</div>';
							}

							print '</td>'
						.'</tr>';

					if( isset($config['dlv_active_port_config']) && $config['dlv_active_port_config'] ){ ?>
					<tr><th colspan="2"><?php echo _("Zones géographiques"); ?></th></tr>
						<tr >
							<td>
								<span class="mandatory">*</span>
								<label><?php echo _('Zones géographiques :'); ?></label>
							</td>
							<td>
								<table class="tableZone" id="tableZone">
									<?php
									$i=0;
									foreach($zone['zone'] as $key => $zoneId){
										$i++;
										echo '<tr>
												<td id="geographicalZone'.$i.'">';
										$parent = -1;
										foreach($zoneId as $key => $zo){
											$geographicalZone = sys_zones_get( 0, '', '', false, $parent, '', $parent === -1 ? _ZONE_PAYS : 0, array(), -1, -1, true, false );
											if($geographicalZone){
												$first = true;
												while ($z = ria_mysql_fetch_array($geographicalZone)){
													if($first){
														$first=false;
														$option = '<option value="0">'.htmlspecialchars( $typeZone[$z['type_id']] ).'</option>';
													}
													$label = sys_zone_get_label($z);

													if ($z['is_deprecated']) {
														$label .= ' (obsolète)';
													}

													if($z['id'] == $zo){
														$option .='<option value="'.$z['id'].'" selected>'.htmlspecialchars( $label ).'</option>';
													}else{
														$option .='<option value="'.$z['id'].'">'.htmlspecialchars( $label ).'</option>';
													}
													
												}	
											}
											?>
											<select id="<?php echo 'zone-'.($key+1); ?>" name="<?php echo 'zone['.($key+1).']['.$i.']'; ?>" class="zone selectZone" data-row="<?php echo $i; ?>" >
												<?php
													print($option);
												?>
											</select>
											<?php

											$parent=$zo;

										}
										$geographicalZone = sys_zones_get( 0, '', '', false, $parent, '', 0, array(), -1, -1, true, true);
										if($geographicalZone && mysql_num_rows($geographicalZone)){
											$first = true;
											while( $z = ria_mysql_fetch_array($geographicalZone) ){
												if( $first ){
													$first=false;
													$option = '<option value="0">'.htmlspecialchars( $typeZone[$z['type_id']] ).'</option>';
												}

												$option .='<option value="'.$z['id'].'">'.htmlspecialchars( sys_zone_get_label($z) ).'</option>';
												
											}	

										?>
											<select  id="<?php echo 'zone-'.($key+2); ?>" name="<?php echo 'zone['.($key+2).']['.$i.']'; ?>" class="zone selectZone" data-row="<?php echo $i; ?>" >
												<?php
													print($option);
												?>
											</select>
										<?php
										}
										echo '<a class="del button" id="buttonDell">' . _("Supprimer") . '</a></td></tr>';
									}
									?>
								</table>
							</td>
							
						</tr>
						<tr>
							<td></td>
							<td>
								<select id='zone-1' name='zone[1][]' class="zone selectZone"  style='display:none'>
										<?php	
											$geographicalZone = sys_zones_get(0, '', '', false, -1, '', _ZONE_PAYS, array(), -1, -1, true, true);
											if($geographicalZone){
												$option = '<option value=0>' . _("Choisissez un pays") . '</option>';
												while ($z = ria_mysql_fetch_array($geographicalZone)){
													$option .='<option value = '.$z['id'].'>'.htmlspecialchars( sys_zone_get_label($z) ).'</option>';		
												}	
											}	
											print($option);	
										?>
								</select>
								<a id="addZone" class="addLink"><?php echo _("Ajouter une zone"); ?></a>
							</td>
						</tr>
					<tr><th colspan="2"><?php echo _("Tarifs"); ?></th></tr>	
					<tr>
					<td><span class="mandatory">*</span> <label for="type"><?php echo _('Calculé sur :'); ?></label></td>
						<td>
							<select id="type" name="type" class="zone">
								<option value=""><?php echo _("Choisissez une règle"); ?></option>
								<option value="qte"<?php print $zone['type'] == 'qte' ? ' selected' : '' ; ?>><?php echo _("Quantité"); ?></option>
								<option value="weight"<?php print $zone['type'] == 'weight' ? ' selected' : '' ; ?>><?php echo _("Poids Brut"); ?></option>
								<option value="weight_net"<?php print $zone['type'] == 'weight_net' ? ' selected' : '' ; ?>><?php echo _("Poids Net"); ?></option>
								<option value="HT"<?php print $zone['type'] == 'HT' ? ' selected' : '' ; ?>><?php echo _("Montant HT"); ?></option>
								<option value="TTC"<?php print $zone['type'] == 'TTC' ? ' selected' : '' ; ?>><?php echo _("Montant TTC"); ?></option>
							</select>
						</td>
					</tr>
					<tr>
						<td>
							<span class="mandatory">*</span> <?php echo _('Tarifs appliqués :'); ?> 
						</td>
						<td>
							<table class="tablePrice" id="tablePrice">
								<?php
								$i=0;
								foreach($zone['price'] as $key => $price)
								{
									$i++;
								?>
								<tr>
									<td id="<?php echo 'tarif'.$i?>" colspan="2">
										<div>
											<table class="tarif">
												<tr>
													<td>
														<label for="<?php echo 'value_min['.$i.']';?>"><span class="mandatory">*</span> Pour <span class="spanType"><?php echo _("une quantité supérieure ou égal à"); ?></span> : </label>
													</td>
													<td>
														<input class="zone" type="text" name="<?php echo 'value_min['.$i.']';?>" id="<?php echo 'value_min['.$i.']';?>" value="<?php print htmlspecialchars($zone['price'][$key]['value_min']); ?>">
														</label>
													</td>
													<td>
														<a class="del button" id="buttonDellPrice"><?php echo _("Supprimer"); ?></a>
													</td>
												</tr>
												<tr>
													<td>
														<label for="<?php echo 'tarif['.$i.']';?>"><span class="mandatory">*</span> <?php echo _('Appliquer le tarif :'); ?></label>
													</td>
													<td colspan="2">
														<input class="zone" type="text" name="<?php echo 'tarif['.$i.']';?>" id="<?php echo 'tarif['.$i.']';?>" value="<?php print htmlspecialchars($zone['price'][$key]['tarif']); ?>">
													</td>
												</tr>
												<tr>
													<td>
														<label for="<?php echo 'prorata['.$i.']';?>" ><?php echo _("Activer le"); ?> <abbr title="<?php echo _("Calculer un montant de frais de port par tranche qui viendra remplacer ou se cumuler au tarif à appliquer."); ?>"><?php echo _("Prorata"); ?></abbr> :</label>
													</td>
													<td colspan="2">
														<input class="chkProrata" type="checkbox" class="checkbox" name="<?php echo 'prorata['.$i.']';?>" id="<?php echo 'prorata['.$i.']';?>" <?php if($zone['price'][$key]['prorata']){ echo 'checked';} ?>>
													</td>
												</tr>
												<tr class = "prorata" <?php if(!$zone['price'][$key]['prorata']){ echo 'style ="display:none"';} ?>>
													<td>
														<label for="<?php echo 'cumule['.$i.']';?>"><?php print _('Cumuler :'); ?></label>
													</td>
													<td colspan="2">
														<input type="checkbox" class="checkbox" name="<?php echo 'cumule['.$i.']';?>" id="<?php echo 'cumule['.$i.']';?>" <?php if($zone['price'][$key]['cumule']){ echo 'checked';} ?>>
													</td>
												</tr>
												<tr class="prorata" <?php if(!$zone['price'][$key]['prorata']){ echo 'style ="display:none"';} ?>>
													<td>
														<label for="<?php echo 'slice['.$i.']';?>" ><?php print _('Tranche :'); ?></label>
													</td>
													<td colspan="2">
														<input class="zone" type="text" name="<?php echo 'slice['.$i.']';?>" id="<?php echo 'slice['.$i.']';?>" value="<?php print htmlspecialchars($zone['price'][$key]['slice']); ?>">
													</td>
												</tr>
												<tr class = "prorata" <?php if(!$zone['price'][$key]['prorata']){ echo 'style ="display:none"';} ?>>
													<td>
														<label for="<?php echo 'sl_price['.$i.']';?>" ><?php echo _("Montant tranche :"); ?> </label>
													</td>
													<td colspan="2">
														<input class="zone" type="text" name="<?php echo 'sl_price['.$i.']';?>" id="<?php echo 'sl_price['.$i.']';?>" value="<?php print htmlspecialchars($zone['price'][$key]['sl_price']); ?>">
													</td>
												</tr>
											</table>
										</div>
									</td>
								</tr>
								<?php
								}
								?>
							</table>
						</td>
						
					</tr>
					<tr>
						<td></td>
						<td>
						<div style="display:none">
							<table class="tarif">
									<tr>
										<td>
											<label id="labelValueMin"><span class="mandatory">*</span> <?php echo _("Pour"); ?> <span class="spanType"><?php echo _("une quantité supérieure ou égale à :"); ?></span> </label>
										</td>
										<td>
											<input class="zone" type="text" id="value_min" value="">
										</td>
										<td>
											<a class="del button" id="buttonDellPrice"><?php echo _("Supprimer"); ?></a>
										</td>
									</tr>
									<tr>
										<td>
											<label id="labelTarif"><?php echo _("Appliquer le tarif :"); ?></label>
										</td>
										<td colspan="2">
											<input class="zone" type="text" id="tarif" value="" />
										</td>
									</tr>
									<tr>
										<td>
											<label id="labelProrata"><?php echo _("Activer le"); ?> <abbr title="<?php echo _("Calculer un montant de frais de port par tranche qui viendra remplacer ou se cumuler au tarif à appliquer"); ?>"><?php echo _("Prorata"); ?></abbr> : </label>
										</td>
										<td colspan="2">
											<input type="checkbox" class="chkProrata" id="prorata" />
										</td>
									</tr>
									<tr class = "prorata" style ="display:none">
										<td>
											<label id="labelCumule"><?php print _('Cumuler :'); ?></label>
										</td>
										<td colspan="2">
											<input type="checkbox" class="checkbox"  id="cumule" />
										</td>
									</tr>
									<tr class = "prorata" style ="display:none">
										<td>
											<label id="labelSlice"><?php print _('Tranche :'); ?></label>
										</td>
										<td colspan="2">
											<input class="zone" type="text"  id="slice" value="" />
										</td>
									</tr>
									<tr class = "prorata" style ="display:none">
										<td>
											<label id="labelSlPrice"><?php echo _("Montant tranche :"); ?></label>
										</td>
										<td colspan="2">
											<input class="zone" type="text" id="sl_price" value="" />
										</td>
									</tr>
								</table>
							</div>
							<a id = "addPrice" class = "addLink"><?php echo _("Ajouter un tarif"); ?></a>
						</td>
					</tr>
				<?php  }?>
				<tr><th colspan="2"><?php echo _("Services"); ?></th></tr>
				<tr>
					<td><?php echo _("Services disponibles :"); ?></td>
					<td>
						<?php
							$services = dlv_services_get();
							if( $services ){
								$i=0;
								print '<table class="services"><tr>';
								while( $r = ria_mysql_fetch_array($services) ){
									print '<td><input type="checkbox" class="checkbox" name="services[]" id="srv'.$r['id'].'" value="'.$r['id'].'" '.( in_array($r['id'],$zone['services']) ? 'checked="checked"' : '' ).' /> ';
									print '<label for="srv'.$r['id'].'">'.htmlspecialchars($r['name']).'</label>'.($i % 2? '</td></tr><tr>': '</td>');
									$i++;
								}
								print '</tr></table>';
							}else{
								print '<div class="error">' . _("Une erreur s'est produite lors du chargement des services de livraison disponibles.") . '</div>';
							}
						?>
					</td>
				</tr>
			</tbody>
			</table>
			<?php if($tab != 'general' && isset($config['dlv_active_port_config']) && $config['dlv_active_port_config']) { ?>
			<table id="tb-tabProducts" class="dlv-zones tb_rewards checklist">
				<caption><?php echo _("Règles d'inclusion / d'exclusion"); ?></caption>
				<tbody>
					<tr><td colspan="2" class="dlv-spe-rules dlv-td-all-catalog">
						<input type="radio" <?php print $zone['all_catalog'] ? 'checked="checked"' : ''; ?> value="1" id="dlv-all-catalog-1" name="dlv-all-catalog" class="radio" />
						<label for="dlv-all-catalog-1"><?php echo _("Inclure tous les produits, sauf exceptions ci-dessous :"); ?></label><br />
						<input type="radio" <?php print !$zone['all_catalog'] ? 'checked="checked"' : ''; ?> value="0" id="dlv-all-catalog-0" name="dlv-all-catalog" class="radio" />
						<label for="dlv-all-catalog-0"><?php echo _("Exclure tous les produits, sauf exceptions ci-dessous :"); ?></label>
					</td></tr>
					<tr><td colspan="2" class="dlv-spe-rules">

						<fieldset id="dlv-add-rule">
							<legend><?php echo _("Ajouter une exception"); ?></legend>
							
							<input type="hidden" name="elem-type" id="elem-type-prd" value="" />
							<input type="hidden" name="elem-id" id="elem-id-prd" value="" />
							
							<div>
								<input disabled="disabled" type="radio" id="dlv-add-rule-prd" value="prd" name="dlv-add-rule" class="radio" />
								<label class="inline rwd-add-rule-label" for="dlv-prd-name"><?php echo _("Une référence de produit :"); ?></label>
								<input type="text" class="ref" maxlength="16" id="dlv-prd-name" name="dlv-prd-name" />
								<input type="button" name="dlv-ref-select" id="dlv-ref-select" class="button" value="<?php echo _("Choisir"); ?>" />
							</div>

							<div>
								<input disabled="disabled" type="radio" id="dlv-add-rule-cat" value="cat" name="dlv-add-rule" class="radio" />
								<label class="inline rwd-add-rule-label" for="dlv-add-rule-cat"><?php echo _("Une catégorie de produits :"); ?></label>
								<input class="text" type="text" readonly="readonly" id="dlv-cat-name" name="dlv-cat-name" />
								<input type="button" name="dlv-cat-select" id="dlv-cat-select" class="button" value="<?php echo _("Choisir"); ?>" />
							</div>

							<div>
								<input disabled="disabled" type="radio" id="dlv-add-rule-brd" value="brd" name="dlv-add-rule" class="radio" />
								<label class="inline rwd-add-rule-label" for="dlv-add-rule-brd"><?php echo _("Une marque de produits :"); ?></label>
								<input class="text" type="text" readonly="readonly" id="dlv-brd-name" name="dlv-brd-name" />
								<input type="button" name="dlv-brd-select" id="dlv-brd-select" class="button" value="<?php echo _("Choisir"); ?>" />
							</div>

							<div class="dlv-rules-buttons">
								<input title="<?php echo _("Inclure dans la zone"); ?>" onclick="return addRulesProducts( true );" type="submit" value="<?php echo _("Inclure dans la zone"); ?>" name="dlv-prd-include" class="button" />
								<input title="<?php echo _("Exclure de la zone"); ?>" onclick="return addRulesProducts( false );" type="submit" value="<?php echo _("Exclure de la zone"); ?>" name="dlv-prd-exclude" class="button" />
							</div>

						</fieldset>

					</td></tr>
					<tr><td colspan="2" id="dlv-list-rules-prd" class="dlv-spe-rules">
						<div class="notice"><?php echo _("Aucune exception n'est enregistrée pour le moment."); ?></div>
					</td></tr>
				</tbody>
				<tfoot>
					<tr><td colspan="2">
						<input onclick="saveRulesProducts();" type="button" name="save" value="<?php print _('Enregistrer'); ?>" />
						<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" onclick="return zoneCancelEdit()" />
					</td></tr>
				</tfoot>
			</table>
			<?php } ?>
		</div>
	</form>
	<div class="page-load-block"></div>

	<script><!--
		<?php
			if( isset($zone['type']) && $tab=="general" ){
				print 'document.getElementById("type").value = "'.$zone['type'].'"';
			}
		?>	

		var zone = <?php print $_GET['zone']; ?>;
		
		// Disable tous les champs/boutons si on accède à cette page en lecture seul
    	<?php if( isset($_GET['zone']) && $_GET['zone'] != 0 && !gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_ZONE_EDIT') ){ ?>
			$(document).ready(function(){
				$('table').find('input, select, textarea').attr('disabled', 'disabled')
			});
		<?php } ?>
	//--></script>
	
<?php
	require_once('admin/skin/footer.inc.php');
?>