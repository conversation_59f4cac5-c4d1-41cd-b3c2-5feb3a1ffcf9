<?php

	/**	\file contact.php
	 *
	 * 	Ce fichier affiche des statistiques sur les contacts reçus par les différents sites web.
	 * 	Ces statistiques sont affichées sous forme de graphiques.
	 * 	La librairie Highcharts est utilisée pour le rendu.
	 *
	 */

	require_once('stats.inc.php');
	require_once('tenants.inc.php');
	require_once('websites.inc.php');
	require_once('admin/get-filters.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_CONTACT');

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Contacts') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Contacts').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Statistiques de contacts'); ?></h2>
	<div class="stats-menu">
		<div id="riadatepicker"></div>
		<?php print view_websites_selector( (isset($wst_id) ? $wst_id : 0), true, 'riapicker', true, 'Tous les sites', false, true ); ?>
		<div class="clear"></div>
	</div>

	<input type="hidden" name="date1" id="date1" value="<?php print htmlspecialchars( $date1 ); ?>" />
	<input type="hidden" name="date2" id="date2" value="<?php print htmlspecialchars( $date2 ); ?>" />

	<?php view_import_highcharts(); ?>
	<?php
		require_once( 'admin/highcharts/graph-contacts.php' );
	?>
	<script><!--
		var urlHighcharts = '/admin/stats/contact.php';
		<?php view_date_initialized( 0, '', array(), array('autoload'=>true) ); ?>
		$(document).ready(function(){
			$('.selector a:not([name="perso"])').mouseup(function(){
				setTimeout(function(){
					window.location='contact.php?date1='+ $('#date1').val() +'&date2=' + $('#date2').val();
				},50);
			});
			$('#btn_submit').mouseup(function(){
				setTimeout(function(){
					window.location='contact.php?date1=' + $('#date1').val() + '&date2=' + $('#date2').val();
				},50);
			});
		});
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>