<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/datastore/v1/query.proto

namespace Google\Cloud\Datastore\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\Datastore\V1\CompositeFilter\Operator instead.
     * @deprecated
     */
    class CompositeFilter_Operator {}
}
class_exists(CompositeFilter\Operator::class);
@trigger_error('Google\Cloud\Datastore\V1\CompositeFilter_Operator is deprecated and will be removed in the next major release. Use Google\Cloud\Datastore\V1\CompositeFilter\Operator instead', E_USER_DEPRECATED);

