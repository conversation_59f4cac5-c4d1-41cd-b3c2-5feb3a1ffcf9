<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  58212 => 'Caracas/Miranda/Vargas',
  58234 => 'Miranda',
  58235 => 'Anzoátegui/Bolívar/Guárico',
  58237 => 'Federal Dependencies',
  58238 => 'Guárico',
  58239 => 'Miranda',
  58241 => 'Carabobo',
  58242 => 'Carabobo',
  58243 => 'Aragua/Carabobo',
  58244 => 'Aragua',
  58245 => 'Aragua/Carabobo',
  58246 => 'Aragua/Guárico',
  58247 => 'Apure/Guárico',
  58248 => 'Amazonas',
  58249 => 'Carabobo/Falcón',
  58251 => '<PERSON>/Yaracuy',
  58252 => '<PERSON>',
  58253 => 'Lara/Yaracuy',
  58254 => 'Yaracuy',
  58255 => 'Portuguesa',
  58256 => 'Portuguesa',
  58257 => 'Portuguesa',
  58258 => 'Cojedes',
  58259 => 'Falcón',
  58260 => 'Colombia',
  58261 => 'Zulia',
  58262 => 'Zulia',
  58263 => 'Zulia',
  58264 => 'Zulia',
  58265 => 'Zulia',
  58266 => 'Zulia',
  58267 => 'Zulia',
  58268 => 'Falcón',
  58269 => 'Falcón',
  58270 => 'Colombia',
  58271 => 'Mérida/Trujillo/Zulia',
  58272 => 'Trujillo',
  58273 => 'Barinas/Mérida',
  58274 => 'Mérida',
  58275 => 'Mérida/Zulia',
  58276 => 'Táchira',
  58277 => 'Táchira',
  58278 => 'Apure/Barinas',
  58281 => 'Anzoátegui',
  58282 => 'Anzoátegui',
  58283 => 'Anzoátegui',
  58285 => 'Bolívar',
  58286 => 'Bolívar',
  58287 => 'Delta Amacuro/Monagas',
  58288 => 'Bolívar',
  58291 => 'Monagas',
  58292 => 'Monagas',
  58293 => 'Sucre',
  58294 => 'Sucre',
  58295 => 'Nueva Esparta',
);
