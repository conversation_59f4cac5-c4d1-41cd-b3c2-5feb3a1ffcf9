<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Twig\Error;

/**
 * Exception thrown when an error occurs during template loading.
 *
 * <AUTHOR> <<EMAIL>>
 */
class LoaderError extends Error
{
}

class_alias('Twig\Error\LoaderError', 'Twig_Error_Loader');
