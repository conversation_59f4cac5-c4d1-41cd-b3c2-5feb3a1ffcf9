<?php

require_once('Services/Collection.class.php');

/**	\brief Cette classe permet de gérer de façon générique les contrôllers qui étendront tous de cette classe.
 *
 */
abstract class Service {
	/** Cette fonction permet de récupérer les données de l'objet courant sous format d'un tableau associative.
	 *  \return Un tableau contenant les informations liés à l'objet courant
	 */
	public function getData(){
		return self::transformObjectToArray($this);
	}

	/** Cette fonction permet de contrôle qu'un ou plusieurs codes sont présents dans le paramètre $_GET.
	 *  \param $ar_code Obligatoire, code ou tableau de codes à contrôler
	 *  \return Si un des codes fournis est absente alors la fonctionne retourne False, True si tous les codes sont trouvés
	 */
	protected function isGetID($ar_code) {
		if( !is_array($ar_code) ){
			$ar_code = array($ar_code);
		}

		$result = true;
		foreach( $ar_code as $code ){
			if( !isset($_GET[ $code ]) && is_numeric($_GET[ $code ]) && $_GET[ $code ] > 0 ){
				$result = false;
				break;
			}
		}

		return $result;
	}

	/** Cette fonction permet de ransformer un objet en tableau récursivement.
	 *  \param $obj Obligatoire, objet à transformer
	 *  \return Un tableau associative contenant les informations accessible par la classe
	 */
	protected static function transformObjectToArray($obj){
		// S'il s'agit d'un objet, on le transforme en tableau
		if( is_a($obj, 'Collection') ){
			$array = $obj->getAll();
		}elseif( is_object($obj) ){
			$array = get_object_vars($obj);
		}else{
			$array = $obj;
		}

		if( !is_array($array) ){
			return [];
		}

		foreach( $array as $k=>$o ){
			// Tant que l'information est un tableau ou un objet on rappel la fonction
			$array[ $k ] = is_array($o) || is_object($o) ? self::transformObjectToArray($o) : $o;
		}

		return $array;
	}
}