# ChannelFirstLevelCategory

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**channel_category_id** | [**\Swagger\Client\Model\BeezUPCommonChannelCategoryId**](BeezUPCommonChannelCategoryId.md) |  | 
**channel_category_name** | **string** | The channel category name | 
**channel_category_level** | [**\Swagger\Client\Model\ChannelCategoryLevel**](ChannelCategoryLevel.md) |  | 
**channel_category_default_cost** | [**\Swagger\Client\Model\ChannelCategoryDefaultCost**](ChannelCategoryDefaultCost.md) |  | [optional] 
**channel_category_channel_code** | [**\Swagger\Client\Model\ChannelCategoryChannelCode**](ChannelCategoryChannelCode.md) |  | [optional] 
**channel_category_column_overrides** | [**\Swagger\Client\Model\ChannelCategoryColumnOverrides**](ChannelCategoryColumnOverrides.md) |  | [optional] 
**sub_categories** | [**\Swagger\Client\Model\ChannelCategory[]**](ChannelCategory.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


