$(document).ready(function(){
src = JSON.parse(src);
data = JSON.parse(data);

var dataG = new Array();
for( var i=0; i<data.length; i++ ){
    dataG.push(parseInt(data[i]));
}


var color = new Array(); // Distribution des couleurs
if(src[0] == 'Femme'){
    color = ["#E869AA", "#0C5DA5", "#CCCCCC"];
}
else if(src[0] == 'Homme'){
    color = ["#0C5DA5", "#CCCCCC"];
}
if (src[1] == 'NC') {
    color[1] = "#CCCCCC";
}

// Traduction
for (var i in src) {
    if( src[i] == 'Femme' ){
        src[i] = graphGenderWoman;
    }else if( src[i] == 'Homme' ){
        src[i] = graphGenderMan;
    }else{
        src[i] = graphGenderSociety;
    }
}

var graphe = new Array();
for (var i = 0; i < src.length; i++) {
    var k = [src[i],dataG[i]];
    graphe.push(k);
}

var urlHighcharts = '/admin/stats/customers/gender.php';
function refreshGraph(){
	setTimeout(function(){
			window.location = urlHighcharts + '?date1='+ $('#date1').val() +'&date2=' + $('#date2').val();
		},50);
}
$(document).ready(function(){
	$('.selector a:not([name="perso"])').mouseup(refreshGraph);
	$('#btn_submit').mouseup(refreshGraph);
});
$('#btn_submit').mouseup(function(){
    setTimeout(function(){
    date1 = $('#date1').val();
    date2 = $('#date2').val();
    date1 = date1.substr(6,4) + '-' + date1.substr(3,2) + '-' + date1.substr(0,2);
    date2 = date2.substr(6,4) + '-' + date2.substr(3,2) + '-' + date2.substr(0,2);
    window.location='gender.php?date1='+date1+'&date2='+date2;
    },50);
});


const optionsGraphique = {
    chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        renderTo: document.getElementById('graph-gender'),
    },
    title: {
        text: ''
    },
	credits: {
		enabled: false
	},
    xAxis: {
        categories: src
    },
    colors: color,
    tooltip: {
        pointFormat: '{series.name}: <b>{point.y}</b>',
    },
    plotOptions: {
        pie: {
			allowPointSelect: true,
			cursor: 'pointer',
			dataLabels: {
				enabled: true,
				color: '#000000',
				connectorColor: '#000000',
				format: '<b>{point.name}</b>: {point.percentage:.1f}%'
            }
        },
        series: {
			stacking: 'normal'
        },
    },
    series: [{
        type: 'pie',
        name: graphGenderNumber,
        data: graphe,
    }]
};

new Highcharts.Chart(optionsGraphique);
});