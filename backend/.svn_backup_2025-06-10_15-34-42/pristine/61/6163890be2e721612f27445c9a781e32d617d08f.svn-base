<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/admin/v1/iam.proto

namespace Google\Iam\Admin\V1\Role;

use UnexpectedValueException;

/**
 * A stage representing a role's lifecycle phase.
 *
 * Protobuf type <code>google.iam.admin.v1.Role.RoleLaunchStage</code>
 */
class RoleLaunchStage
{
    /**
     * The user has indicated this role is currently in an Alpha phase. If this
     * launch stage is selected, the `stage` field will not be included when
     * requesting the definition for a given role.
     *
     * Generated from protobuf enum <code>ALPHA = 0;</code>
     */
    const ALPHA = 0;
    /**
     * The user has indicated this role is currently in a Beta phase.
     *
     * Generated from protobuf enum <code>BETA = 1;</code>
     */
    const BETA = 1;
    /**
     * The user has indicated this role is generally available.
     *
     * Generated from protobuf enum <code>GA = 2;</code>
     */
    const GA = 2;
    /**
     * The user has indicated this role is being deprecated.
     *
     * Generated from protobuf enum <code>DEPRECATED = 4;</code>
     */
    const DEPRECATED = 4;
    /**
     * This role is disabled and will not contribute permissions to any members
     * it is granted to in policies.
     *
     * Generated from protobuf enum <code>DISABLED = 5;</code>
     */
    const DISABLED = 5;
    /**
     * The user has indicated this role is currently in an EAP phase.
     *
     * Generated from protobuf enum <code>EAP = 6;</code>
     */
    const EAP = 6;

    private static $valueToName = [
        self::ALPHA => 'ALPHA',
        self::BETA => 'BETA',
        self::GA => 'GA',
        self::DEPRECATED => 'DEPRECATED',
        self::DISABLED => 'DISABLED',
        self::EAP => 'EAP',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(RoleLaunchStage::class, \Google\Iam\Admin\V1\Role_RoleLaunchStage::class);

