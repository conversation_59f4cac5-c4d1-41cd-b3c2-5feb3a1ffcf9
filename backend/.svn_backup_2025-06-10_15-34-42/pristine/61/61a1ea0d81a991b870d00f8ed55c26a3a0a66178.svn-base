SET FOREIGN_KEY_CHECKS = 0;

/*delete from fld_classes where cls_tnt_id=7;*/
/*delete from fld_fields where fld_tnt_id=7;*/
delete from gu_adresses where adr_tnt_id=7;
delete from gu_profiles where prf_tnt_id=7;
delete from gu_users where usr_tnt_id=7;
/*delete from ord_payment_types where pay_tnt_id=7;*/
delete from adv_banners where bnr_tnt_id=7;
delete from adv_places where plc_tnt_id=7;
/*delete from adv_websites where bnw_tnt_id=7;*/
/*delete from ats_ips where ip_tnt_id=7;*/
/*delete from ats_keywords where key_tnt_id=7;*/
/*delete from cat_erratums where err_tnt_id=7;*/
delete from cfg_emails where email_tnt_id=7;
/*delete from cfg_images where img_tnt_id=7;*/
/*delete from cfg_images_filters where cif_tnt_id=7;*/
/*delete from cfg_urls where url_tnt_id=7;*/
/*delete from cgv_articles where art_tnt_id=7;*/
/*delete from cgv_versions where ver_tnt_id=7;*/
/*delete from cms_categories where cat_tnt_id=7;*/
/*delete from cms_img where ci_tnt_id=7;*/
/*delete from cnt_contacts where cnt_tnt_id=7;*/
/*delete from cnt_contacts_types where ctt_tnt_id=7;*/
/*delete from cnt_functions where fct_tnt_id=7;*/
/*delete from cnt_types where type_tnt_id=7;*/
/*delete from ctr_carriers_services where ccs_tnt_id=7;*/
/*delete from ctr_cat_auctions where cta_tnt_id=7;*/
/*delete from ctr_catalogs2 where ctl_tnt_id=7;*/
/*delete from ctr_catalogs_images where img_tnt_id=7;*/
/*delete from ctr_clicks where cct_tnt_id=7;*/
/*delete from ctr_compaign_products where ccp_tnt_id=7;*/
/*delete from ctr_compaigns where ctc_tnt_id=7;*/
/*delete from ctr_comparator_logs where ccl_tnt_id=7;*/
/*delete from ctr_filters_conditions where cfc_tnt_id=7;*/
/*delete from ctr_images where img_tnt_id=7;*/
/*delete from ctr_models where mdl_tnt_id=7;*/
/*delete from ctr_models_comparators where mct_tnt_id=7;*/
/*delete from dev_devices where dev_tnt_id=7;*/
/*delete from dev_devices_doc_obj_sync where dos_tnt_id=7;*/
/*delete from dev_devices_locations where ddl_tnt_id=7;*/
/*delete from dev_devices_need_sync where dns_tnt_id=7;*/
/*delete from dev_devices_tasks where ddt_tnt_id=7;*/
/*delete from dev_devices_usages where ddu_tnt_id=7;*/
/*delete from dev_subscribtions where ds_tnt_id=7;*/
/*delete from dlv_conditions where cnd_tnt_id=7;*/
/*delete from dlv_events where evt_tnt_id=7;*/
/*delete from dlv_expedition_periods where epp_tnt_id=7;*/
/*delete from dlv_holidays where hld_tnt_id=7;*/
/*delete from dlv_options where opt_tnt_id=7;*/
/*delete from dlv_package_price_countries where ppc_tnt_id=7;*/
/*delete from dlv_package_price_zones where ppz_tnt_id=7;*/
/*delete from dlv_package_prices where dpp_tnt_id=7;*/
/*delete from dlv_packages where pkg_tnt_id=7;*/
/*delete from dlv_relay_types_used where rlw_tnt_id=7;*/
/*delete from dlv_relays_ban where rlb_tnt_id=7;*/
/*delete from dlv_sales_types where type_tnt_id=7;*/
/*delete from dlv_sectors where sct_tnt_id=7;*/
delete from dlv_service_available where avl_tnt_id=7;
delete from dlv_services where srv_tnt_id=7;
/*delete from dlv_store_jobs where stj_tnt_id=7;*/
/*delete from dlv_store_opening where sto_tnt_id=7;*/
/*delete from dlv_store_sales_types where slc_tnt_id=7;*/
/*delete from dlv_store_services where sts_tnt_id=7;*/
/*delete from dlv_store_teams where stt_tnt_id=7;*/
delete from dlv_stores where str_tnt_id=7;
/*delete from dlv_stores_brands where stb_tnt_id=7;*/
/*delete from dlv_stores_images where img_tnt_id=7;*/
/*delete from dlv_stores_img_types where type_tnt_id=7;*/
delete from dlv_zones where zone_tnt_id=7;
/*delete from doc_channels where chl_tnt_id=7;*/
/*delete from doc_channels_medias where chm_tnt_id=7;*/
/*delete from doc_channels_playlists where chp_tnt_id=7;*/
/*delete from doc_channels_websites where chw_tnt_id=7;*/
/*delete from doc_documents where doc_tnt_id=7;*/
/*delete from doc_downloads where dwl_tnt_id=7;*/
/*delete from doc_erratums where der_tnt_id=7;*/
/*delete from doc_hosts_params where hsp_tnt_id=7;*/
/*delete from doc_medias where med_tnt_id=7;*/
/*delete from doc_medias_objects where mob_tnt_id=7;*/
/*delete from doc_medias_websites where mdw_tnt_id=7;*/
/*delete from doc_objects where dob_tnt_id=7;*/
/*delete from doc_playlists where pls_tnt_id=7;*/
/*delete from doc_playlists_medias where plm_tnt_id=7;*/
/*delete from doc_playlists_websites where plw_tnt_id=7;*/
/*delete from doc_prd_categories where dpc_tnt_id=7;*/
/*delete from doc_products where dpr_tnt_id=7;*/
/*delete from doc_types where type_tnt_id=7;*/
/*delete from doc_websites where dw_tnt_id=7;*/
/*delete from email_options where opt_tnt_id=7;*/
/*delete from erm_objects where ero_tnt_id=7;*/
/*delete from erm_stats where ers_tnt_id=7;*/
/*delete from err_errors where err_tnt_id=7;*/
/*delete from exp_node_variables where nod_tnt_id=7;*/
/*delete from exp_nodes where nod_tnt_id=7;*/
/*delete from exp_nodes_hierarchy where hrc_tnt_id=7;*/
/*delete from exp_session_path where esp_tnt_id=7;*/
/*delete from exp_session_variables where svr_tnt_id=7;*/
/*delete from exp_sessions where ses_tnt_id=7;*/
/*delete from exp_var_types where typ_tnt_id=7;*/
/*delete from exp_variables where var_tnt_id=7;*/
/*delete from faq_categories where cat_tnt_id=7;*/
/*delete from faq_questions where qst_tnt_id=7;*/
/*delete from fld_categories where cat_tnt_id=7;*/
/*delete from fld_classes_fields_priority where cfp_tnt_id=7;*/
/*delete from fld_classes_patterns where ptn_tnt_id=7;*/
/*delete from fld_model_fields where mf_tnt_id=7;*/
/*delete from fld_models where mdl_tnt_id=7;*/
/*delete from fld_object_models where pm_tnt_id=7;*/
/*delete from fld_object_notes where ono_tnt_id=7;*/
delete from fld_object_values where pv_tnt_id=7;
/*delete from fld_objects where obj_tnt_id=7;*/
/*delete from fld_restricted_values where val_tnt_id=7;*/
/*delete from fld_restricted_values_constraints where rvc_tnt_id=7;*/
/*delete from fld_restricted_values_translates where vlg_tnt_id=7;*/
/*delete from fld_units where unit_tnt_id=7;*/
/*delete from fld_update_requests where fur_tnt_id=7;*/
/*delete from fly_fiscal_year where fly_tnt_id=7;*/
/*delete from gsr_words where gsr_tnt_id=7;*/
/*delete from gu_accounting_categories where cac_tnt_id=7;*/
/*delete from gu_bookmarks where bmk_tnt_id=7;*/
/*delete from gu_contacts where cnt_tnt_id=7;*/
/*delete from gu_livr_alerts where alert_tnt_id=7;*/
delete from gu_messages where cnt_tnt_id=7;
/*delete from gu_messages_files where file_tnt_id=7;*/
/*delete from gu_messages_images where msi_tnt_id=7;*/
/*delete from gu_moderate_fields where gmf_tnt_id=7;*/
/*delete from gu_ord_alerts where alert_tnt_id=7;*/
/*delete from gu_profiles_rights where prg_tnt_id=7;*/
/*delete from gu_relations_types where grt_tnt_id=7;*/
/*delete from gu_rights where rgh_tnt_id=7;*/
/*delete from gu_risk_codes where rco_tnt_id=7;*/
/*delete from gu_sponsor_links where spl_tnt_id=7;*/
/*delete from gu_sponsor_points where spp_tnt_id=7;*/
/*delete from gu_sponsor_promotions where spp_tnt_id=7;*/
/*delete from gu_sponsors where sp_tnt_id=7;*/
/*delete from gu_tell_a_friend where cnt_tnt_id=7;*/
/*delete from gu_trading_rules where gtr_tnt_id=7;*/
/*delete from gu_users_cgv where usg_tnt_id=7;*/
/*delete from gu_users_logins where usl_tnt_id=7;*/
/*delete from gu_users_payment_credentials where upc_tnt_id=7;*/
/*delete from gu_users_payment_types where upt_tnt_id=7;*/
/*delete from gu_users_rights where urg_tnt_id=7;*/
/*delete from gu_wishlists where gw_tnt_id=7;*/
/*delete from gu_wishlists_products where gwp_tnt_id=7;*/
/*delete from img_image_names where imn_tnt_id=7;*/
/*delete from img_images where img_tnt_id=7;*/
/*delete from img_images_objects where imo_tnt_id=7;*/
/*delete from img_images_types where imt_tnt_id=7;*/
/*delete from img_zones where imz_tnt_id=7;*/
/*delete from ipt_imports where imp_tnt_id=7;*/
/*delete from ipt_mapping where map_tnt_id=7;*/
/*delete from ipt_report_rows where row_tnt_id=7;*/
/*delete from ipt_reports where rep_tnt_id=7;*/
/*delete from ipt_row_errors where err_tnt_id=7;*/
/*delete from ipt_tasks where tsk_tnt_id=7;*/
/*delete from mkt_campaigns where cpg_tnt_id=7;*/
/*delete from mkt_campaigns_emails where cpe_tnt_id=7;*/
/*delete from mkt_campaigns_newsletter_cat where cpn_tnt_id=7;*/
/*delete from mkt_campaigns_objects where mco_tnt_id=7;*/
/*delete from mkt_campaigns_phones where cph_tnt_id=7;*/
/*delete from mkt_campaigns_segments where cps_tnt_id=7;*/
/*delete from mkt_campaigns_users where cur_tnt_id=7;*/
/*delete from mkt_channels where chl_tnt_id=7;*/
/*delete from mkt_triggers where trg_tnt_id=7;*/
/*delete from mkt_triggers_conditions where mtc_tnt_id=7;*/
/*delete from mkt_triggers_groups where mtg_tnt_id=7;*/
/*delete from news where news_tnt_id=7;*/
delete from news_cat_hierarchy where cah_tnt_id=7;
/*delete from news_categories where cat_tnt_id=7;*/
/*delete from news_img where ni_tnt_id=7;*/
/*delete from news_stores where ns_tnt_id=7;*/
/*delete from news_websites where nw_tnt_id=7;*/
/*delete from nlr_cat where cnt_tnt_id=7;*/
/*delete from nlr_mailjet where nlm_tnt_id=7;*/
delete from nlr_subscribers where sub_tnt_id=7;
/*delete from ord_bl where bl_tnt_id=7;*/
/*delete from ord_bl_products where prd_tnt_id=7;*/
/*delete from ord_card_types where card_tnt_id=7;*/
/*delete from ord_installments where itm_tnt_id=7;*/
/*delete from ord_inv_products where prd_tnt_id=7;*/
/*delete from ord_invoices where inv_tnt_id=7;*/
/*delete from ord_model_users where omu_tnt_id=7;*/
/*delete from ord_models_position where omd_tnt_id=7;*/
/*delete from ord_models_products_position where omp_tnt_id=7;*/
delete from ord_orders where ord_tnt_id=7;
/*delete from ord_orders_promotions where oop_tnt_id=7;*/
/*delete from ord_orders_signature where sig_tnt_id=7;*/
/*delete from ord_orders_states where oos_tnt_id=7;*/
/*delete from ord_payment_credits where opc_tnt_id=7;*/
/*delete from ord_payment_model_details where omd_tnt_id=7;*/
/*delete from ord_payment_models where opm_tnt_id=7;*/
/*delete from ord_pl where pl_tnt_id=7;*/
/*delete from ord_pl_products where prd_tnt_id=7;*/
delete from ord_products where prd_tnt_id=7;
/*delete from ord_products_intentions where prd_tnt_id=7;*/
/*delete from ord_products_promotions where opm_tnt_id=7;*/
/*delete from ord_promotions_excluded where ope_tnt_id=7;*/
/*delete from ord_related where rel_tnt_id=7;*/
/*delete from ord_returns where return_tnt_id=7;*/
/*delete from ord_returns_categories where cat_tnt_id=7;*/
/*delete from ord_returns_mode_allowed where rma_tnt_id=7;*/
/*delete from ord_returns_products where prd_tnt_id=7;*/
/*delete from ord_returns_reasons where reason_tnt_id=7;*/
/*delete from ord_states_name where stn_tnt_id=7;*/
/*delete from ord_subscriptions where ops_tnt_id=7;*/
/*delete from ord_transactions where ord_tnt_id=7;*/
/*delete from per_events where pev_tnt_id=7;*/
/*delete from per_holidays where hld_tnt_id=7;*/
/*delete from per_objects where pob_tnt_id=7;*/
/*delete from per_periods where per_tnt_id=7;*/
/*delete from per_types where type_tnt_id=7;*/
/*delete from pmt_brands where pmt_tnt_id=7;*/
/*delete from pmt_categories where pmt_tnt_id=7;*/
/*delete from pmt_code_conditions where pcc_tnt_id=7;*/
/*delete from pmt_code_groups where grp_tnt_id=7;*/
/*delete from pmt_codes where cod_tnt_id=7;*/
/*delete from pmt_codes_services where cod_tnt_id=7;*/
/*delete from pmt_codes_variations where cvt_tnt_id=7;*/
/*delete from pmt_offer_products where pop_tnt_id=7;*/
/*delete from pmt_offers where off_tnt_id=7;*/
/*delete from pmt_products where pmt_tnt_id=7;*/
/*delete from pmt_products_sets where pmt_tnt_id=7;*/
/*delete from pmt_profiles where pmt_tnt_id=7;*/
/*delete from pmt_segments where pmt_tnt_id=7;*/
/*delete from pmt_users where pmt_tnt_id=7;*/
/*delete from pmt_websites where pmt_tnt_id=7;*/
/*delete from prc_group_conditions where pgc_tnt_id=7;*/
/*delete from prc_price_conditions where ppc_tnt_id=7;*/
/*delete from prc_price_fields where ppf_tnt_id=7;*/
delete from prc_prices where prc_tnt_id=7;
/* delete from prc_prices2 where prc_tnt_id=7; */
delete from prc_prices_drop where pcd_tnt_id=7;
delete from prc_promotion_groups where grp_tnt_id=7;
/*delete from prc_tva_exempt_conditions where ptc_tnt_id=7;*/
/*delete from prc_tva_exempt_groups where ptg_tnt_id=7;*/
/*delete from prc_tva_fields where ptf_tnt_id=7;*/
delete from prc_tvas where ptv_tnt_id=7;
delete from prd_brands where brd_tnt_id=7;
delete from prd_cat_hierarchy where cat_tnt_id=7;
/*delete from prd_cat_images where img_tnt_id=7;*/
delete from prd_categories where cat_tnt_id=7;
/*delete from prd_categories_codes where ccd_tnt_id=7;*/
/*delete from prd_categories_sort where sort_tnt_id=7;*/
/*delete from prd_category_filters where cft_tnt_id=7;*/
delete from prd_classify where cly_tnt_id=7;
delete from prd_colisage_classify where cly_tnt_id=7;
delete from prd_colisage_types where col_tnt_id=7;
/*delete from prd_deposits where dps_tnt_id=7;*/
/*delete from prd_domain_categories where dmc_tnt_id=7;*/
/*delete from prd_domains where dmn_tnt_id=7;*/
/*delete from prd_hierarchy where prd_tnt_id=7;*/
/*delete from prd_images where img_tnt_id=7;*/
/*delete from prd_nomenclatures_options where prd_tnt_id=7;*/
/*delete from prd_nomenclatures_products where pnp_tnt_id=7;*/
/*delete from prd_nomenclatures_types where type_tnt_id=7;*/
/*delete from prd_options where opt_tnt_id=7;*/
/*delete from prd_options_products where opt_tnt_id=7;*/
/*delete from prd_pre_orders where ord_tnt_id=7;*/
/*delete from prd_pre_orders_hint where ord_tnt_id=7;*/
/*delete from prd_pre_orders_options where opt_tnt_id=7;*/
/*delete from prd_prices_categories where prc_tnt_id=7;*/
/*delete from prd_product_positions where ppp_tnt_id=7;*/
delete from prd_products where prd_tnt_id=7;
/*delete from prd_relations where rel_tnt_id=7;*/
/*delete from prd_relations_types where type_tnt_id=7;*/
delete from prd_resellers where prs_tnt_id=7;
/*delete from prd_reservations where rsv_tnt_id=7;*/
/*delete from prd_restrictions where rec_tnt_id=7;*/
/*delete from prd_restrictions_brands where rec_tnt_id=7;*/
/*delete from prd_restrictions_categories where rec_tnt_id=7;*/
/*delete from prd_restrictions_products where rec_tnt_id=7;*/
/*delete from prd_sell_units where sun_tnt_id=7;*/
/*delete from prd_services_unavailable where psu_tnt_id=7;*/
delete from prd_stocks where sto_tnt_id=7;
/*delete from prd_stocks_infos where psi_tnt_id=7;*/
/*delete from prd_stocks_schedule where pss_tnt_id=7;*/
/*delete from prd_subscriptions where sub_tnt_id=7;*/
/*delete from prd_suppliers where ps_tnt_id=7;*/
/*delete from prd_suppliers_references where sup_tnt_id=7;*/
/*delete from prw_competitors_tenants where ctn_tnt_id=7;*/
/*delete from prw_followed_products where pwf_tnt_id=7;*/
/*delete from prw_offers where ofr_tnt_id=7;*/
/*delete from rel_relations_hierarchy where rrh_tnt_id=7;*/
/*delete from rel_relations_objects where rro_tnt_id=7;*/
/*delete from rel_relations_types where rrt_tnt_id=7;*/
/*delete from rev_revisions where rev_tnt_id=7;*/
/*delete from rew_rewritemap where url_tnt_id=7;*/
/*delete from rew_rewritemap2 where url_tnt_id=7;*/
/*delete from rew_tags where tag_tnt_id=7;*/
/*delete from rp_checkin where rck_tnt_id=7;*/
/*delete from rp_report_models where rpm_tnt_id=7;*/
/*delete from rp_report_notes where rprn_tnt_id=7;*/
/*delete from rp_report_objects where rpro_tnt_id=7;*/
/*delete from rp_report_type_notes where rptn_tnt_id=7;*/
/*delete from rp_report_types where rpt_tnt_id=7;*/
/*delete from rp_reports where rpr_tnt_id=7;*/
/*delete from rsl_brands where rsl_tnt_id=7;*/
/*delete from rsl_resellers where rsl_tnt_id=7;*/
/*delete from rsl_specialists where rss_tnt_id=7;*/
/*delete from rwd_action_conditions_configs where cfg_tnt_id=7;*/
/*delete from rwd_actions_configs where cfg_tnt_id=7;*/
/*delete from rwd_brands where rwb_tnt_id=7;*/
/*delete from rwd_catalogs where rwc_tnt_id=7;*/
/*delete from rwd_categories where rwc_tnt_id=7;*/
/*delete from rwd_prf_cat_rewards where rwc_tnt_id=7;*/
/*delete from rwd_prf_prd_rewards where rwp_tnt_id=7;*/
/*delete from rwd_products where rwp_tnt_id=7;*/
/*delete from rwd_products_sets where rwp_tnt_id=7;*/
/*delete from rwd_rewards where rwd_tnt_id=7;*/
/*delete from rwd_users where rwu_tnt_id=7;*/
/*delete from search_engine_types where set_tnt_id=7;*/
/*delete from search_engines where seg_tnt_id=7;*/
/*delete from seg_criterions_sources where scs_tnt_id=7;*/
/*delete from seg_objects where sgo_tnt_id=7;*/
/*delete from seg_segment_criterions where sgc_tnt_id=7;*/
/*delete from seg_segments where seg_tnt_id=7;*/
/*delete from site_bank_details where bnk_tnt_id=7;*/
/*delete from site_bank_profils where sbp_tnt_id=7;*/
/*delete from site_owner where owner_tnt_id=7;*/
/*delete from sms_partners_quota where qta_tnt_id=7;*/
/*delete from sms_partners_tenants where ptt_tnt_id=7;*/
/*delete from stats_campaigns where stat_tnt_id=7;*/
/*delete from tnt_filters where flt_tnt_id=7;*/
/*delete from tnt_tenant_tokens where ttt_tnt_id=7;*/
/*delete from tnt_translates where ttl_tnt_id=7;*/
/*delete from tnt_websites_languages where twl_tnt_id=7;*/
/*delete from tsk_comparators where tsk_tnt_id=7;*/
/*delete from tsk_imports where tsk_tnt_id=7;*/
/*delete from tsk_rewards where tsk_tnt_id=7;*/
/*delete from tsk_tasks_activities where tta_tnt_id=7;*/
delete from prw_followed_lists where pfl_tnt_id=7;
delete from prw_followed_users where pfu_tnt_id=7;
delete from prw_followed_products where pwf_tnt_id=7;
delete from prw_linear_raised where plr_tnt_id=7;
delete from prw_offers where ofr_tnt_id=7;
delete from prw_states where ps_tnt_id=7;
delete from prw_offers_states where pst_tnt_id=7;
delete from prw_followed_list_sections  where fls_tnt_id =7;
SET FOREIGN_KEY_CHECKS = 1;



INSERT INTO gu_adresses
(adr_tnt_id, adr_usr_id, adr_id, adr_type_id, adr_desc, adr_title_id, adr_firstname, adr_lastname, adr_society, adr_siret, adr_address1, adr_address2, adr_address3, adr_postal_code, adr_city, adr_country, adr_cnt_code, adr_country_state, adr_phone, adr_fax, adr_mobile, adr_phone_work, adr_email, adr_date_created, adr_date_modified, adr_need_sync, adr_date_masked, adr_date_deleted, adr_latitude, adr_longitude, adr_date_location, adr_manual_location, adr_ref_gescom)
VALUES
('7', '1', '1', '1', 'une adresse de facturation', NULL, '', '', '', '', '', '', '', '', '', '', NULL, NULL, '', '', '', '', NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', '0', NULL, NULL, NULL, NULL, NULL, '0', NULL);

INSERT INTO gu_adresses
(adr_tnt_id, adr_usr_id, adr_id, adr_type_id, adr_desc, adr_title_id, adr_firstname, adr_lastname, adr_society, adr_siret, adr_address1, adr_address2, adr_address3, adr_postal_code, adr_city, adr_country, adr_cnt_code, adr_country_state, adr_phone, adr_fax, adr_mobile, adr_phone_work, adr_email, adr_date_created, adr_date_modified, adr_need_sync, adr_date_masked, adr_date_deleted, adr_latitude, adr_longitude, adr_date_location, adr_manual_location, adr_ref_gescom)
VALUES
('7', '2', '2', '1', 'une adresse de livraison', NULL, '', '', '', '', '', '', '', '', '', '', NULL, NULL, '', '', '', '', NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', '0', NULL, NULL, NULL, NULL, NULL, '0', NULL);

INSERT INTO gu_adresses
(adr_tnt_id, adr_usr_id, adr_id, adr_type_id, adr_desc, adr_title_id, adr_firstname, adr_lastname, adr_society, adr_siret, adr_address1, adr_address2, adr_address3, adr_postal_code, adr_city, adr_country, adr_cnt_code, adr_country_state, adr_phone, adr_fax, adr_mobile, adr_phone_work, adr_email, adr_date_created, adr_date_modified, adr_need_sync, adr_date_masked, adr_date_deleted, adr_latitude, adr_longitude, adr_date_location, adr_manual_location, adr_ref_gescom)
VALUES
('7', '1', '3', '1', 'une adresse', '1', 'prenom', 'nom', '', '', 'adresse 1', 'adresse 2', '', '79000', 'niort', 'france', 'fr', NULL, '0549050505', '0549050505', '0607080910', '0549050505', '<EMAIL>', '2018-05-07 00:00:00', '2018-05-07 00:00:00', '0', NULL, NULL, '42', '-1', NULL, '0', NULL);

INSERT INTO gu_adresses
(adr_tnt_id, adr_usr_id, adr_id, adr_type_id, adr_desc, adr_title_id, adr_firstname, adr_lastname, adr_society, adr_siret, adr_address1, adr_address2, adr_address3, adr_postal_code, adr_city, adr_country, adr_cnt_code, adr_country_state, adr_phone, adr_fax, adr_mobile, adr_phone_work, adr_email, adr_date_created, adr_date_modified, adr_need_sync, adr_date_masked, adr_date_deleted, adr_latitude, adr_longitude, adr_date_location, adr_manual_location, adr_ref_gescom)
VALUES
('7', '100', '100', '1', 'une adresse ', 1, 'prenom', 'nom', '', '', 'première partie de l''adresse', '', '', '79000', 'niort', 'france', 'fr', NULL, '05 49 50 51 52', '05 06 07 08 09', '06 07 08 09 10', '', '<EMAIL>', '2018-05-07 00:00:00', '2018-05-07 00:00:00', '0', NULL, NULL, '42', '-1', NULL, '0', NULL);




INSERT INTO gu_users
(usr_tnt_id, usr_id, usr_ref, usr_email, usr_password, usr_prf_id, usr_prc_id, usr_adr_invoices, usr_adr_delivery, usr_cnt_id, usr_orders, usr_orders_web, usr_orders_canceled, usr_orders_canceled_web, usr_display_prices, usr_opt_stocks, usr_opt_centralized, usr_encours, usr_seller_id, usr_dps_id, usr_date_created, usr_date_modified, usr_date_deleted, usr_last_login, usr_is_sync, usr_discount, usr_cnd_id, usr_alert_cc, usr_naf, usr_website, usr_taxcode, usr_display_buy, usr_latitude, usr_longitude, usr_is_locked, usr_can_login, usr_show_myprd, usr_fur_alerts, usr_cac_id, usr_surname, usr_img_id, usr_parent_id, usr_sponsor, usr_date_sponsor, usr_dob, usr_lng_code, usr_encours_allow, usr_accept_partners, usr_wst_id, usr_opm_id, usr_bnk_id, usr_rco_id, usr_ref_gescom, usr_need_sync, usr_is_confirmed, usr_date_confirmed, usr_store_choosed, usr_date_password)
VALUES
('7', '1', 'REF1', '<EMAIL>', md5("motdepasse"), '2', '3283', '3', NULL, NULL, '9', '8', '1', '0', 'ttc', '0', '1', '20', NULL, NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, '2018-05-11 00:00:00', '0', '0.000000', NULL, '', '4764Z', 'www.website.com', 'FR77485352843', '0', '42', '-1', '0', '1', '0', '0', NULL, 'surnom', NULL, NULL, NULL, NULL, '1996-11-11 00:00:00', 'fr', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', '1', NULL, NULL, NULL),


('7', '34', 'REF34', '<EMAIL>', md5("motdepasse34"), '3', '3283', '3', NULL, NULL, '9', '8', '1', '0', 'ttc', '0', '1', '20', NULL, NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, '2018-05-11 00:00:00', '0', '0.000000', NULL, '', '4764Z', 'www.website.com', 'FR77485352843', '0', '42', '-1', '0', '1', '0', '0', NULL, 'surnom', NULL, NULL, NULL, NULL, '1996-11-11 00:00:00', 'fr', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', '1', NULL, NULL, NULL),
('7', '35', 'REF35', '<EMAIL>', md5("motdepasse35"), '2', '3283', '3', NULL, NULL, '9', '8', '1', '0', 'ttc', '0', '1', '20', NULL, NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, '2018-05-11 00:00:00', '0', '0.000000', NULL, '', '4764Z', 'www.website.com', 'FR77485352843', '0', '42', '-1', '0', '1', '0', '0', NULL, 'surnom', NULL, NULL, NULL, NULL, '1996-11-11 00:00:00', 'fr', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', '1', NULL, NULL, NULL),
('7', '36', 'REF36', '<EMAIL>', md5("motdepasse36"), '3', '3283', '3', NULL, NULL, '9', '8', '1', '0', 'ttc', '0', '1', '20', NULL, NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, '2018-05-11 00:00:00', '0', '0.000000', NULL, '', '4764Z', 'www.website.com', 'FR77485352843', '0', '42', '-1', '0', '1', '0', '0', NULL, 'surnom', NULL, NULL, NULL, NULL, '1996-11-11 00:00:00', 'fr', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', '1', NULL, NULL, NULL),
('7', '37', 'REF37', '<EMAIL>', md5("motdepasse37"), '5', '3283', '3', NULL, NULL, '9', '8', '1', '0', 'ttc', '0', '1', '20', NULL, NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, '2018-05-11 00:00:00', '0', '0.000000', NULL, '', '4764Z', 'www.website.com', 'FR77485352843', '0', '42', '-1', '0', '1', '0', '0', NULL, 'surnom', NULL, NULL, NULL, NULL, '1996-11-11 00:00:00', 'fr', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', '1', NULL, NULL, NULL),
('7', '38', 'REF38', '<EMAIL>', md5("motdepasse38"), '3', '3283', '3', NULL, NULL, '9', '8', '1', '0', 'ttc', '0', '1', '20', NULL, NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, '2018-05-11 00:00:00', '0', '0.000000', NULL, '', '4764Z', 'www.website.com', 'FR77485352843', '0', '42', '-1', '0', '1', '0', '0', NULL, 'surnom', NULL, NULL, NULL, NULL, '1996-11-11 00:00:00', 'fr', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', '1', NULL, NULL, NULL),
('7', '39', 'REF39', '<EMAIL>', md5("motdepasse39"), '3', '3283', '3', NULL, NULL, '9', '8', '1', '0', 'ttc', '0', '1', '20', NULL, NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, '2018-05-11 00:00:00', '0', '0.000000', NULL, '', '4764Z', 'www.website.com', 'FR77485352843', '0', '42', '-1', '0', '1', '0', '0', NULL, 'surnom', NULL, NULL, NULL, NULL, '1996-11-11 00:00:00', 'fr', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', '1', NULL, NULL, NULL),
('7', '40', 'REF40', '<EMAIL>', md5("motdepasse40"), '3', '3283', '3', NULL, NULL, '9', '8', '1', '0', 'ttc', '0', '1', '20', NULL, NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, '2018-05-11 00:00:00', '0', '0.000000', NULL, '', '4764Z', 'www.website.com', 'FR77485352843', '0', '42', '-1', '0', '1', '0', '0', NULL, 'surnom', NULL, NULL, NULL, NULL, '1996-11-11 00:00:00', 'fr', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', '1', NULL, NULL, NULL),
('7', '41', 'REF41', '<EMAIL>', md5("motdepasse41"), '1', '3283', '3', NULL, NULL, '9', '8', '1', '0', 'ttc', '0', '1', '20', NULL, NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, '2018-05-11 00:00:00', '0', '0.000000', NULL, '', '4764Z', 'www.website.com', 'FR77485352843', '0', '42', '-1', '0', '1', '0', '0', NULL, 'surnom', NULL, NULL, NULL, NULL, '1996-11-11 00:00:00', 'fr', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', '1', NULL, NULL, NULL),
('7', '42', 'REF42', '<EMAIL>', md5("motdepasse42"), '3', '3283', '3', NULL, NULL, '9', '8', '1', '0', 'ttc', '0', '1', '20', NULL, NULL, '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, '2018-05-11 00:00:00', '0', '0.000000', NULL, '', '4764Z', 'www.website.com', 'FR77485352843', '0', '42', '-1', '0', '1', '0', '0', NULL, 'surnom', NULL, NULL, NULL, NULL, '1996-11-11 00:00:00', 'fr', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', '1', NULL, NULL, NULL);


INSERT INTO gu_users
(usr_tnt_id, usr_prf_id, usr_email, usr_password, usr_ref, usr_date_created, usr_display_prices, usr_is_sync, usr_accept_partners, usr_can_login, usr_is_confirmed, usr_prc_id, usr_id, usr_wst_id, usr_is_locked)
VALUES
(7, 2, "<EMAIL>", md5("motdepasse2"), "", now(), "ttc", 0, 0, 1, 1, 3283, 2, 12, 1);

INSERT INTO gu_users
(usr_tnt_id, usr_prf_id, usr_email, usr_password, usr_ref, usr_date_created, usr_display_prices, usr_is_sync, usr_accept_partners, usr_can_login, usr_is_confirmed, usr_prc_id, usr_id, usr_wst_id)
VALUES
(7, 2, "<EMAIL>", md5("motdepasse3"), "", now(), "ttc", 0, 0, 1, 1, 3283, 3, 12);

INSERT INTO gu_users
(usr_tnt_id, usr_prf_id, usr_email, usr_password, usr_ref, usr_date_created, usr_display_prices, usr_is_sync, usr_accept_partners, usr_can_login, usr_is_confirmed, usr_prc_id, usr_id, usr_wst_id, usr_is_locked, usr_parent_id)
VALUES
(7, 1, "<EMAIL>", md5("motdepasse4"), "", now(), "ttc", 0, 0, 1, 1, 3283, 4, 12, 1, 1);

/* utilisateur utilisé pour les test de mise à jour et de suppression*/
INSERT INTO gu_users
(usr_tnt_id, usr_prf_id, usr_email, usr_password, usr_ref, usr_date_created, usr_display_prices, usr_is_sync, usr_accept_partners, usr_can_login, usr_is_confirmed, usr_prc_id, usr_id, usr_wst_id)
VALUES
(7, 3, "<EMAIL>", md5("motdepasse5"), "", now(), "ttc", 0, 0, 1, 1, 3283, 100, 12);




INSERT INTO prd_brands
(brd_tnt_id, brd_id, brd_name, brd_title, brd_url, brd_publish, brd_products, brd_date_modified, brd_date_deleted, brd_img_id, brd_is_sync, brd_desc, brd_url_alias, brd_cnt_id, brd_tag_title, brd_tag_desc, brd_keywords, brd_pos)
VALUES
('7', '1', 'Marque test 1', '', 'http://marquetest1.com/', '1', '0', CURRENT_TIMESTAMP, NULL, NULL, '0', 'une marque de test', 'http://urlalias.com/', NULL, NULL, NULL, NULL, NULL);

INSERT INTO prd_brands
(brd_tnt_id, brd_id, brd_name, brd_title, brd_url, brd_publish, brd_products, brd_date_modified, brd_date_deleted, brd_img_id, brd_is_sync, brd_desc, brd_url_alias, brd_cnt_id, brd_tag_title, brd_tag_desc, brd_keywords, brd_pos)
VALUES
('7', '2', 'Marque test 2', '', 'http://marquetest2.com/', '1', '0', CURRENT_TIMESTAMP, NULL, NULL, '0', '', '', NULL, NULL, NULL, NULL, NULL);

/* marque utilisé pour les test de mise à jour et de suppression */
INSERT INTO prd_brands
(brd_tnt_id, brd_id, brd_name, brd_title, brd_url, brd_publish, brd_products, brd_date_modified, brd_date_deleted, brd_img_id, brd_is_sync, brd_desc, brd_url_alias, brd_cnt_id, brd_tag_title, brd_tag_desc, brd_keywords, brd_pos)
VALUES
('7', '3', 'Marque del', '', 'http://marquedel.com/', '1', '0', CURRENT_TIMESTAMP, NULL, NULL, '0', '', '', NULL, NULL, NULL, NULL, NULL);

INSERT INTO prd_brands
(brd_tnt_id, brd_id, brd_name, brd_title, brd_url, brd_publish, brd_products, brd_date_modified, brd_date_deleted, brd_img_id, brd_is_sync, brd_desc, brd_url_alias, brd_cnt_id, brd_tag_title, brd_tag_desc, brd_keywords, brd_pos)
VALUES
('7', '4', 'Marque del', '', 'http://marquedel.com/', '1', '0', CURRENT_TIMESTAMP, NULL, NULL, '0', '', '', NULL, NULL, NULL, NULL, NULL);




INSERT INTO prd_categories
(cat_tnt_id, cat_id, cat_ref, cat_name, cat_title, cat_url_alias, cat_desc, cat_keywords, cat_parent_id, cat_publish, cat_first_publish, cat_products, cat_products_childs, cat_cnt_id, cat_pos, cat_date_created, cat_date_modified, cat_date_deleted, cat_is_sync, cat_tag_title, cat_tag_desc, cat_url_perso, cat_return_allowed, cat_date_from, cat_date_to, cat_is_soldes, cat_no_index)
VALUES
('7', '3', NULL, 'catégorie parent', 'catégorie parent', '/catalogue/parent', 'une catégorie de test', 'test', NULL, '1', NULL, '0', '0', NULL, NULL, '2018-05-09 00:00:00', '2018-05-09 00:00:00', NULL, '0', NULL, NULL, NULL, '1', '2018-05-09 00:00:00', NULL, '0', '0');

INSERT INTO prd_categories
(cat_tnt_id, cat_id, cat_ref, cat_name, cat_title, cat_url_alias, cat_desc, cat_keywords, cat_parent_id, cat_publish, cat_first_publish, cat_products, cat_products_childs, cat_cnt_id, cat_pos, cat_date_created, cat_date_modified, cat_date_deleted, cat_is_sync, cat_tag_title, cat_tag_desc, cat_url_perso, cat_return_allowed, cat_date_from, cat_date_to, cat_is_soldes, cat_no_index)
VALUES
('7', '1', NULL, 'catégorie fille 1', 'catégorie fille 1', '/catalogue/parent/fille-1', 'une catégorie de test', 'test', 3, '1', NULL, '2', '0', NULL, NULL, '2018-05-09 00:00:00', '2018-05-09 00:00:00', NULL, '0', NULL, NULL, NULL, '1', '2018-05-09 00:00:00', NULL, '0', '0');

INSERT INTO prd_categories
(cat_tnt_id, cat_id, cat_ref, cat_name, cat_title, cat_url_alias, cat_desc, cat_keywords, cat_parent_id, cat_publish, cat_first_publish, cat_products, cat_products_childs, cat_cnt_id, cat_pos, cat_date_created, cat_date_modified, cat_date_deleted, cat_is_sync, cat_tag_title, cat_tag_desc, cat_url_perso, cat_return_allowed, cat_date_from, cat_date_to, cat_is_soldes, cat_no_index)
VALUES
('7', '2', NULL, 'catégorie fille 2', 'catégorie fille 2', '/catalogue/parent/fille-2', 'une catégorie de test', 'test', 3, '1', NULL, '0', '0', NULL, NULL, '2018-05-09 00:00:00', '2018-05-09 00:00:00', NULL, '0', NULL, NULL, NULL, '1', '2018-05-09 00:00:00', NULL, '0', '0');

INSERT INTO prd_categories
(cat_tnt_id, cat_id, cat_ref, cat_name, cat_title, cat_url_alias, cat_desc, cat_keywords, cat_parent_id, cat_publish, cat_first_publish, cat_products, cat_products_childs, cat_cnt_id, cat_pos, cat_date_created, cat_date_modified, cat_date_deleted, cat_is_sync, cat_tag_title, cat_tag_desc, cat_url_perso, cat_return_allowed, cat_date_from, cat_date_to, cat_is_soldes, cat_no_index)
VALUES
('7', '4', NULL, 'catégorie petite fille 1', 'catégorie petite fille 1', '/catalogue/parent/fille-2/petite-fille-1', 'une catégorie de test', 'test', 2, '1', NULL, '0', '0', NULL, NULL, '2018-05-09 00:00:00', '2018-05-09 00:00:00', NULL, '0', NULL, NULL, NULL, '1', '2018-05-09 00:00:00', NULL, '0', '0');

INSERT INTO prd_categories
(cat_tnt_id, cat_id, cat_ref, cat_name, cat_title, cat_url_alias, cat_desc, cat_keywords, cat_parent_id, cat_publish, cat_first_publish, cat_products, cat_products_childs, cat_cnt_id, cat_pos, cat_date_created, cat_date_modified, cat_date_deleted, cat_is_sync, cat_tag_title, cat_tag_desc, cat_url_perso, cat_return_allowed, cat_date_from, cat_date_to, cat_is_soldes, cat_no_index)
VALUES
('7', '5', NULL, 'catégorie petite fille 2', 'catégorie petite fille 2', '/catalogue/parent/fille-2/petite-fille-2', 'une catégorie de test', 'test', 2, '1', NULL, '0', '0', NULL, NULL, '2018-05-09 00:00:00', '2018-05-09 00:00:00', NULL, '0', NULL, NULL, NULL, '1', '2018-05-09 00:00:00', NULL, '0', '0');




INSERT INTO prd_cat_hierarchy
(cat_tnt_id, cat_parent_id, cat_child_id, cat_parent_depth)
VALUES
('7', '3', '1', '0');

INSERT INTO prd_cat_hierarchy
(cat_tnt_id, cat_parent_id, cat_child_id, cat_parent_depth)
VALUES
('7', '3', '2', '0');

INSERT INTO prd_cat_hierarchy
(cat_tnt_id, cat_parent_id, cat_child_id, cat_parent_depth)
VALUES
('7', '3', '4', '0');

INSERT INTO prd_cat_hierarchy
(cat_tnt_id, cat_parent_id, cat_child_id, cat_parent_depth)
VALUES
('7', '3', '5', '0');

INSERT INTO prd_cat_hierarchy
(cat_tnt_id, cat_parent_id, cat_child_id, cat_parent_depth)
VALUES
('7', '2', '4', '1');

INSERT INTO prd_cat_hierarchy
(cat_tnt_id, cat_parent_id, cat_child_id, cat_parent_depth)
VALUES
('7', '2', '5', '1');




INSERT INTO prd_colisage_types
(col_tnt_id, col_id, col_name, col_qte, col_is_sync, col_is_deleted, col_date_modified, col_dps_id, col_pkg_id)
VALUES
('7', '1', 'Colisage test', '10', '0', '0', '2018-05-11 00:00:00', NULL, NULL);

INSERT INTO prd_colisage_types
(col_tnt_id, col_id, col_name, col_qte, col_is_sync, col_is_deleted, col_date_modified, col_dps_id, col_pkg_id)
VALUES
('7', '2', 'Colisage test 2', '15', '0', '0', '2018-05-11 00:00:00', NULL, NULL);



/* Ajoute le produit 1 dans le colisage 1 */
INSERT INTO fld_object_values
(pv_tnt_id, pv_lng_code, pv_obj_id_0, pv_obj_id_1, pv_obj_id_2, pv_fld_id, pv_value)
VALUES
('7', 'fr', '2', '1', '1', '490', '1');




INSERT INTO prd_colisage_classify
(cly_tnt_id, cly_col_id, cly_prd_id, cly_is_default, cly_prd_ref, cly_prd_barcode, cly_date_modified)
VALUES
('7', '1', '1', '0', NULL, NULL, '2018-05-14 00:00:00');

INSERT INTO prd_colisage_classify
(cly_tnt_id, cly_col_id, cly_prd_id, cly_is_default, cly_prd_ref, cly_prd_barcode, cly_date_modified)
VALUES
('7', '2', '5', '0', NULL, NULL, '2018-05-14 00:00:00');




INSERT INTO prd_products (prd_tnt_id, prd_id, prd_ref, prd_name, prd_title, prd_desc, prd_desc_long, prd_publish, prd_publish_cat, prd_date_published, prd_first_published, prd_brd_id, prd_brd_pos, prd_weight, prd_weight_net, prd_length, prd_width, prd_height, prd_centralized, prd_garantie, prd_stock_livr, prd_keywords, prd_date_created, prd_date_modified, prd_date_deleted, prd_img_id, prd_orderable, prd_countermark, prd_childonly, prd_new, prd_sleep, prd_is_sync, prd_completion, prd_taxcode, prd_barcode, prd_selled, prd_hits, prd_bestseller, prd_is_bestseller, prd_bestseller_point, prd_nomenclature_type, prd_sell_weight, prd_ecotaxe, prd_rvw_avg, prd_tag_title, prd_tag_desc, prd_perishable, prd_purchase_avg, prd_selled_web, prd_follow_stock, prd_url_rebuild, prd_sun_id, prd_canonical_id, prd_buy_points, prd_ref_gescom, prd_no_index)
VALUES
('7', '1', 'REF1', 'produit test 1', '', 'description du produit 1', 'une longue description du produit 1', '1', '1', '2018-05-07 00:00:00', '2018-05-07 00:00:00', '1', NULL, '8', '9', '10', '11', '12', '0', 12, '2100-06-11', 'mot clé', '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, NULL, '1', '0', '0', '0', '1', '0', '0', 'taxcode', 'barcode', NULL, NULL, '0', '0', '0', '0', '0', '0.0000', NULL, NULL, NULL, '0', NULL, NULL, '1', '0', NULL, NULL, NULL, NULL, '0');

INSERT INTO prd_products (prd_tnt_id, prd_id, prd_ref, prd_name, prd_title, prd_desc, prd_desc_long, prd_publish, prd_publish_cat, prd_date_published, prd_first_published, prd_brd_id, prd_brd_pos, prd_weight, prd_weight_net, prd_length, prd_width, prd_height, prd_centralized, prd_garantie, prd_stock_livr, prd_keywords, prd_date_created, prd_date_modified, prd_date_deleted, prd_img_id, prd_orderable, prd_countermark, prd_childonly, prd_new, prd_sleep, prd_is_sync, prd_completion, prd_taxcode, prd_barcode, prd_selled, prd_hits, prd_bestseller, prd_is_bestseller, prd_bestseller_point, prd_nomenclature_type, prd_sell_weight, prd_ecotaxe, prd_rvw_avg, prd_tag_title, prd_tag_desc, prd_perishable, prd_purchase_avg, prd_selled_web, prd_follow_stock, prd_url_rebuild, prd_sun_id, prd_canonical_id, prd_buy_points, prd_ref_gescom, prd_no_index)
VALUES
('7', '2', 'REF2', 'produit test 2', '', 'description du produit 2', 'une longue description du produit 2', '1', '1', '2018-05-07 00:00:00', '2018-05-07 00:00:00', '1', NULL, '8', '9', '10', '11', '12', '0', NULL, NULL, 'mot clé', '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, 1, '1', '0', '0', '1', '0', '0', '0', 'taxcode', 'barcode', NULL, NULL, '0', '0', '0', '0', '0', '0.0000', NULL, NULL, NULL, '0', NULL, NULL, '0', '0', NULL, NULL, NULL, NULL, '0');

INSERT INTO prd_products (prd_tnt_id, prd_id, prd_ref, prd_name, prd_title, prd_desc, prd_desc_long, prd_publish, prd_publish_cat, prd_date_published, prd_first_published, prd_brd_id, prd_brd_pos, prd_weight, prd_weight_net, prd_length, prd_width, prd_height, prd_centralized, prd_garantie, prd_stock_livr, prd_keywords, prd_date_created, prd_date_modified, prd_date_deleted, prd_img_id, prd_orderable, prd_countermark, prd_childonly, prd_new, prd_sleep, prd_is_sync, prd_completion, prd_taxcode, prd_barcode, prd_selled, prd_hits, prd_bestseller, prd_is_bestseller, prd_bestseller_point, prd_nomenclature_type, prd_sell_weight, prd_ecotaxe, prd_rvw_avg, prd_tag_title, prd_tag_desc, prd_perishable, prd_purchase_avg, prd_selled_web, prd_follow_stock, prd_url_rebuild, prd_sun_id, prd_canonical_id, prd_buy_points, prd_ref_gescom, prd_no_index)
VALUES
('7', '3', 'REF3', 'produit test 3', '', 'description du produit 3', 'une longue description du produit 3', '0', '0', '2018-05-07 00:00:00', '2018-05-07 00:00:00', '1', NULL, '8', '9', '10', '11', '12', '0', NULL, NULL, 'mot clé', '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, NULL, '1', '0', '0', '0', '0', '0', '0', 'taxcode', 'barcode', NULL, NULL, '0', '0', '0', '0', '0', '0.0000', NULL, NULL, NULL, '0', NULL, NULL, '1', '0', NULL, NULL, NULL, NULL, '0');

INSERT INTO prd_products (prd_tnt_id, prd_id, prd_ref, prd_name, prd_title, prd_desc, prd_desc_long, prd_publish, prd_publish_cat, prd_date_published, prd_first_published, prd_brd_id, prd_brd_pos, prd_weight, prd_weight_net, prd_length, prd_width, prd_height, prd_centralized, prd_garantie, prd_stock_livr, prd_keywords, prd_date_created, prd_date_modified, prd_date_deleted, prd_img_id, prd_orderable, prd_countermark, prd_childonly, prd_new, prd_sleep, prd_is_sync, prd_completion, prd_taxcode, prd_barcode, prd_selled, prd_hits, prd_bestseller, prd_is_bestseller, prd_bestseller_point, prd_nomenclature_type, prd_sell_weight, prd_ecotaxe, prd_rvw_avg, prd_tag_title, prd_tag_desc, prd_perishable, prd_purchase_avg, prd_selled_web, prd_follow_stock, prd_url_rebuild, prd_sun_id, prd_canonical_id, prd_buy_points, prd_ref_gescom, prd_no_index)
VALUES
('7', '4', 'REF4', 'produit test 4', '', 'description du produit 4', 'une longue description du produit 4', '0', '0', '2018-05-07 00:00:00', '2018-05-07 00:00:00', '2', NULL, '8', '9', '10', '11', '12', '0', NULL, NULL, 'mot clé', '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, NULL, '1', '0', '0', '0', '0', '0', '0', 'taxcode', 'barcode', NULL, NULL, '0', '0', '0', '0', '0', '0.0000', NULL, NULL, NULL, '0', NULL, NULL, '1', '0', NULL, NULL, NULL, NULL, '0');

INSERT INTO prd_products (prd_tnt_id, prd_id, prd_ref, prd_name, prd_title, prd_desc, prd_desc_long, prd_publish, prd_publish_cat, prd_date_published, prd_first_published, prd_brd_id, prd_brd_pos, prd_weight, prd_weight_net, prd_length, prd_width, prd_height, prd_centralized, prd_garantie, prd_stock_livr, prd_keywords, prd_date_created, prd_date_modified, prd_date_deleted, prd_img_id, prd_orderable, prd_countermark, prd_childonly, prd_new, prd_sleep, prd_is_sync, prd_completion, prd_taxcode, prd_barcode, prd_selled, prd_hits, prd_bestseller, prd_is_bestseller, prd_bestseller_point, prd_nomenclature_type, prd_sell_weight, prd_ecotaxe, prd_rvw_avg, prd_tag_title, prd_tag_desc, prd_perishable, prd_purchase_avg, prd_selled_web, prd_follow_stock, prd_url_rebuild, prd_sun_id, prd_canonical_id, prd_buy_points, prd_ref_gescom, prd_no_index)
VALUES
('7', '5', 'REF5', 'produit test 5', '', 'description du produit 5', 'une longue description du produit 5', '1', '1', '2018-05-07 00:00:00', '2018-05-07 00:00:00', '3', NULL, '8', '9', '10', '11', '12', '0', NULL, NULL, 'mot clé', '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, NULL, '1', '0', '0', '0', '0', '0', '0', 'taxcode', 'barcode', NULL, NULL, '0', '0', '0', '0', '0', '0.0000', NULL, NULL, NULL, '0', NULL, NULL, '1', '0', NULL, NULL, NULL, NULL, '0');

INSERT INTO prd_products (prd_tnt_id, prd_id, prd_ref, prd_name, prd_title, prd_desc, prd_desc_long, prd_publish, prd_publish_cat, prd_date_published, prd_first_published, prd_brd_id, prd_brd_pos, prd_weight, prd_weight_net, prd_length, prd_width, prd_height, prd_centralized, prd_garantie, prd_stock_livr, prd_keywords, prd_date_created, prd_date_modified, prd_date_deleted, prd_img_id, prd_orderable, prd_countermark, prd_childonly, prd_new, prd_sleep, prd_is_sync, prd_completion, prd_taxcode, prd_barcode, prd_selled, prd_hits, prd_bestseller, prd_is_bestseller, prd_bestseller_point, prd_nomenclature_type, prd_sell_weight, prd_ecotaxe, prd_rvw_avg, prd_tag_title, prd_tag_desc, prd_perishable, prd_purchase_avg, prd_selled_web, prd_follow_stock, prd_url_rebuild, prd_sun_id, prd_canonical_id, prd_buy_points, prd_ref_gescom, prd_no_index)
VALUES
('7', '6', 'REF6', 'produit test 6', '', 'description du produit 6', 'une longue description du produit 6', '1', '1', '2018-05-07 00:00:00', '2018-05-07 00:00:00', '4', NULL, '8', '9', '10', '11', '12', '0', NULL, NULL, 'mot clé', '2018-05-07 00:00:00', '2018-05-07 00:00:00', NULL, NULL, '1', '0', '0', '0', '0', '0', '0', 'taxcode', 'barcode', NULL, NULL, '0', '0', '0', '0', '0', '0.0000', NULL, NULL, NULL, '0', NULL, NULL, '1', '0', NULL, NULL, NULL, NULL, '0');

/* produit utilisé pour les test de mise à jour et suppression */
INSERT INTO prd_products
(prd_tnt_id, prd_id, prd_ref, prd_name, prd_desc, prd_publish, prd_date_published, prd_brd_id, prd_weight, prd_length, prd_width, prd_height, prd_keywords, prd_date_created, prd_orderable, prd_is_sync, prd_taxcode, prd_follow_stock)
VALUES
(7, 100,"REF", "Produit test", "", 0, null, null, null, null, null, null, "", now(), 1, 0, "", 1);




INSERT INTO prc_prices (prc_tnt_id, prc_id, prc_prd_id, prc_cat_id, prc_type_id, prc_date_start, prc_date_end, prc_is_deleted, prc_is_cumuled, prc_is_sync, prc_name, prc_value, prc_qte_min, prc_qte_max, prc_is_promotion, prc_grp_id, prc_date_modified, prc_date_deleted)
VALUES
('7', '1', '1', '0', '1', '1000-01-01 00:00:00', '9999-12-31 23:59:59', '0', '0', '0', NULL, '5', '1', '2147483647', '0', NULL, CURRENT_TIMESTAMP, NULL);

INSERT INTO prc_prices (prc_tnt_id, prc_id, prc_prd_id, prc_cat_id, prc_type_id, prc_date_start, prc_date_end, prc_is_deleted, prc_is_cumuled, prc_is_sync, prc_name, prc_value, prc_qte_min, prc_qte_max, prc_is_promotion, prc_grp_id, prc_date_modified, prc_date_deleted)
VALUES
('7', '2', '2', '0', '1', '1000-01-01 00:00:00', '9999-12-31 23:59:59', '0', '0', '0', NULL, '10', '1', '2147483647', '0', NULL, CURRENT_TIMESTAMP, NULL);

INSERT INTO prc_prices (prc_tnt_id, prc_id, prc_prd_id, prc_cat_id, prc_type_id, prc_date_start, prc_date_end, prc_is_deleted, prc_is_cumuled, prc_is_sync, prc_name, prc_value, prc_qte_min, prc_qte_max, prc_is_promotion, prc_grp_id, prc_date_modified, prc_date_deleted)
VALUES
('7', '3', '3', '0', '1', '1000-01-01 00:00:00', '9999-12-31 23:59:59', '0', '0', '0', NULL, '20', '1', '2147483647', '0', NULL, CURRENT_TIMESTAMP, NULL);




INSERT INTO prd_resellers
(prs_tnt_id, prs_wst_id, prs_prd_id, prs_usr_id, prs_ref, prs_stock, prs_price_ht, prs_price_promo_ht, prs_publish, prs_date_modified)
VALUES
('7', '12', '2', '3', NULL, NULL, '15', NULL, '1', CURRENT_TIMESTAMP);




INSERT INTO prd_classify
(cly_tnt_id, cly_cat_id, cly_prd_id, cly_prd_pos, cly_url_alias, cly_cnt_id, cly_is_sync, cly_prd_is_bestseller, cly_tag_title, cly_tag_desc, cly_url_perso, cly_keywords, cly_cat_publish, cly_cat_is_sync, cly_url_is_canonical, cly_date_created, cly_prd_title, cly_prd_desc, cly_prd_desc_long)
VALUES
('7', '1', '1', NULL, '/catalogue/parent/fille-1/produit-1', NULL, '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-09 00:00:00', NULL, NULL, NULL);

INSERT INTO prd_classify
(cly_tnt_id, cly_cat_id, cly_prd_id, cly_prd_pos, cly_url_alias, cly_cnt_id, cly_is_sync, cly_prd_is_bestseller, cly_tag_title, cly_tag_desc, cly_url_perso, cly_keywords, cly_cat_publish, cly_cat_is_sync, cly_url_is_canonical, cly_date_created, cly_prd_title, cly_prd_desc, cly_prd_desc_long)
VALUES
('7', '1', '2', NULL, '/catalogue/parent/fille-1/produit-2', NULL, '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-09 00:00:00', NULL, NULL, NULL);

INSERT INTO prd_classify
(cly_tnt_id, cly_cat_id, cly_prd_id, cly_prd_pos, cly_url_alias, cly_cnt_id, cly_is_sync, cly_prd_is_bestseller, cly_tag_title, cly_tag_desc, cly_url_perso, cly_keywords, cly_cat_publish, cly_cat_is_sync, cly_url_is_canonical, cly_date_created, cly_prd_title, cly_prd_desc, cly_prd_desc_long)
VALUES
('7', '2', '3', NULL, '/catalogue/parent/fille-2/produit-3', NULL, '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-09 00:00:00', NULL, NULL, NULL);

INSERT INTO prd_classify
(cly_tnt_id, cly_cat_id, cly_prd_id, cly_prd_pos, cly_url_alias, cly_cnt_id, cly_is_sync, cly_prd_is_bestseller, cly_tag_title, cly_tag_desc, cly_url_perso, cly_keywords, cly_cat_publish, cly_cat_is_sync, cly_url_is_canonical, cly_date_created, cly_prd_title, cly_prd_desc, cly_prd_desc_long)
VALUES
('7', '4', '4', NULL, '/catalogue/parent/fille-2/petite-fille-1/produit-4', NULL, '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2018-05-09 00:00:00', NULL, NULL, NULL);





INSERT INTO adv_places
(plc_tnt_id, plc_wst_id, plc_id, plc_name, plc_desc, plc_width, plc_height, plc_date_deleted, plc_img_code, plc_type)
VALUES
('7', '12', '1', 'place test', '', '200', '200', NULL, NULL, '1');




INSERT INTO adv_banners
(bnr_tnt_id, bnr_id, bnr_plc_id, bnr_name, bnr_alt, bnr_url, bnr_date_from, bnr_date_to, bnr_image, bnr_pos, bnr_date_deleted, bnr_prd_id)
VALUES
('7', '1', '1', 'bannière', '', 'www.urlbnr.com', '2018-05-04 00:00:00', NULL, '', NULL, NULL, 1);

/* bannière utilisé pour les test de mise à jour et de suppression*/
INSERT INTO adv_banners
(bnr_tnt_id, bnr_id, bnr_plc_id, bnr_name, bnr_alt, bnr_url, bnr_date_from, bnr_date_to, bnr_image, bnr_pos, bnr_date_deleted, bnr_prd_id)
VALUES
('7', '100', '1', 'bnr del', '', '', '2018-05-04 00:00:00', NULL, '', NULL, NULL, NULL);




INSERT INTO nlr_subscribers
(sub_tnt_id, sub_id, sub_email, sub_inscript_requested, sub_inscript_confirmed, sub_cat_id, sub_prd_cat_id)
VALUES
(7, 1,'<EMAIL>', now(), now(), 1, 0);

/* email utilisé pour les test de suppression*/
INSERT INTO nlr_subscribers
(sub_tnt_id, sub_id, sub_email, sub_inscript_requested, sub_inscript_confirmed, sub_cat_id, sub_prd_cat_id)
VALUES
(7, 100,'<EMAIL>', now(), now(), 1, 0);



/* commande utilisé pour les test d'ajout de produit*/
INSERT INTO ord_orders
(ord_tnt_id, ord_id, ord_date, ord_products, ord_prd_count, ord_total_ht, ord_total_ttc, ord_total_ht_delivered, ord_margin, ord_adr_invoices, ord_adr_delivery, ord_state_id, ord_state_sage, ord_piece, ord_ref, ord_need_sync, ord_date_livr, ord_usr_id, ord_srv_id, ord_str_id, ord_dlv_notes, ord_opt_gift, ord_opt_gift_message, ord_pay_id, ord_card_id, ord_masked, ord_pmt_id, ord_seller_id, ord_seller_com, ord_comments, ord_cnt_id, ord_date_created, ord_date_modified, ord_date_archived, ord_dps_id, ord_pkg_id, ord_relanced, ord_alert_livr, ord_rly_id, ord_wst_id, ord_user_ip, ord_parent_id, ord_contact_id, ord_stats_products, ord_stats_prd_count, ord_stats_total_ht, ord_stats_total_ttc, ord_stats_margin, ord_dlv_cls_id, ord_dlv_obj_id)
VALUES
('7', '1', '2018-05-07 00:00:00', '0', '0', '0.00', '0.00', '0.00', NULL, NULL, NULL, '1', NULL, '', '', '0', NULL, NULL, NULL, NULL, '', '0', '', NULL, NULL, '0', NULL, NULL, NULL, '', NULL, NULL, CURRENT_TIMESTAMP, NULL, NULL, NULL, '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO ord_orders
(ord_tnt_id, ord_id, ord_date, ord_products, ord_prd_count, ord_total_ht, ord_total_ttc, ord_total_ht_delivered, ord_margin, ord_adr_invoices, ord_adr_delivery, ord_state_id, ord_state_sage, ord_piece, ord_ref, ord_need_sync, ord_date_livr, ord_usr_id, ord_srv_id, ord_str_id, ord_dlv_notes, ord_opt_gift, ord_opt_gift_message, ord_pay_id, ord_card_id, ord_masked, ord_pmt_id, ord_seller_id, ord_seller_com, ord_comments, ord_cnt_id, ord_date_created, ord_date_modified, ord_date_archived, ord_dps_id, ord_pkg_id, ord_relanced, ord_alert_livr, ord_rly_id, ord_wst_id, ord_user_ip, ord_parent_id, ord_contact_id, ord_stats_products, ord_stats_prd_count, ord_stats_total_ht, ord_stats_total_ttc, ord_stats_margin, ord_dlv_cls_id, ord_dlv_obj_id)
VALUES
('7', '2', '2018-05-07 00:00:00', '0', '0', '0.00', '0.00', '0.00', NULL, NULL, NULL, '1', NULL, '', '', '0', NULL, NULL, NULL, NULL, '', '0', '', NULL, NULL, '0', NULL, NULL, NULL, '', NULL, NULL, CURRENT_TIMESTAMP, NULL, NULL, NULL, '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

/* commandes utilisé pour les test de calcule du prix total et pour le test de ord_orders_get */
INSERT INTO ord_orders
(ord_tnt_id, ord_id, ord_date, ord_products, ord_prd_count, ord_total_ht, ord_total_ttc, ord_total_ht_delivered, ord_margin, ord_adr_invoices, ord_adr_delivery, ord_state_id, ord_state_sage, ord_piece, ord_ref, ord_need_sync, ord_date_livr, ord_usr_id, ord_srv_id, ord_str_id, ord_dlv_notes, ord_opt_gift, ord_opt_gift_message, ord_pay_id, ord_card_id, ord_masked, ord_pmt_id, ord_seller_id, ord_seller_com, ord_comments, ord_cnt_id, ord_date_created, ord_date_modified, ord_date_archived, ord_dps_id, ord_pkg_id, ord_relanced, ord_alert_livr, ord_rly_id, ord_wst_id, ord_user_ip, ord_parent_id, ord_contact_id, ord_stats_products, ord_stats_prd_count, ord_stats_total_ht, ord_stats_total_ttc, ord_stats_margin, ord_dlv_cls_id, ord_dlv_obj_id)
VALUES
('7', '3', '2018-05-07 00:00:00', '0', '0', '0.00', '0.00', '0.00', NULL, 1, 1, '1', NULL, '', '', '0', '2019-05-07 00:00:00', 1, 1, 1, 'note de livraison', '0', '', NULL, NULL, '0', NULL, 3, NULL, 'un commentaire', 3, NULL, CURRENT_TIMESTAMP, NULL, NULL, NULL, '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);




INSERT INTO ord_products
(prd_tnt_id, prd_ord_id, prd_id, prd_line_id, prd_qte, prd_ref, prd_name, prd_price_ht, prd_tva_rate, prd_price_ttc, prd_ecotaxe, prd_sell_points, prd_brd_id, prd_date_livr, prd_notes, prd_date_created, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id, prd_date_modified, prd_purchase_avg, prd_dps_id, prd_price_brut_ht, prd_weight, prd_is_free, prd_is_delayed, prd_pos)
VALUES
('7', '2', '1', '1', '5', 'REF', 'produit test', '5', '1.2', NULL, '0.0000', NULL, NULL, '2020-01-03 00:00:00', 'une note', '2018-01-03 00:00:00', '0', NULL, NULL, NULL, NULL, '2018-01-04 00:00:00', NULL, NULL, NULL, NULL, '0', NULL, NULL);

INSERT INTO ord_products
(prd_tnt_id, prd_ord_id, prd_id, prd_line_id, prd_qte, prd_ref, prd_name, prd_price_ht, prd_tva_rate, prd_price_ttc, prd_ecotaxe, prd_sell_points, prd_brd_id, prd_date_livr, prd_notes, prd_date_created, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id, prd_date_modified, prd_purchase_avg, prd_dps_id, prd_price_brut_ht, prd_weight, prd_is_free, prd_is_delayed, prd_pos)
VALUES
('7', '2', '2', '1', '10', 'REF2', 'produit test 2', '10', '1.2', NULL, '0.0000', NULL, NULL, NULL, '', CURRENT_TIMESTAMP, '0', NULL, NULL, NULL, NULL, CURRENT_TIMESTAMP, NULL, NULL, NULL, NULL, '0', NULL, NULL);

INSERT INTO ord_products
(prd_tnt_id, prd_ord_id, prd_id, prd_line_id, prd_qte, prd_ref, prd_name, prd_price_ht, prd_tva_rate, prd_price_ttc, prd_ecotaxe, prd_sell_points, prd_brd_id, prd_date_livr, prd_notes, prd_date_created, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id, prd_date_modified, prd_purchase_avg, prd_dps_id, prd_price_brut_ht, prd_weight, prd_is_free, prd_is_delayed, prd_pos)
VALUES
('7', '2', '3', '1', '25', 'REF3', 'produit test 3', '20', '1.2', NULL, '0.0000', NULL, NULL, NULL, '', CURRENT_TIMESTAMP, '0', NULL, NULL, NULL, NULL, CURRENT_TIMESTAMP, NULL, NULL, NULL, NULL, '0', NULL, NULL);

INSERT INTO ord_products
(prd_tnt_id, prd_ord_id, prd_id, prd_line_id, prd_qte, prd_ref, prd_name, prd_price_ht, prd_tva_rate, prd_price_ttc, prd_ecotaxe, prd_sell_points, prd_brd_id, prd_date_livr, prd_notes, prd_date_created, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id, prd_date_modified, prd_purchase_avg, prd_dps_id, prd_price_brut_ht, prd_weight, prd_is_free, prd_is_delayed, prd_pos)
VALUES
('7', '3', '1', '1', '5', 'REF', 'produit test', '5', '1.2', NULL, '0.0000', NULL, NULL, '2020-01-03 00:00:00', 'une note', '2018-01-03 00:00:00', '0', NULL, NULL, NULL, NULL, '2018-01-04 00:00:00', NULL, NULL, NULL, NULL, '0', NULL, NULL);

INSERT INTO ord_products
(prd_tnt_id, prd_ord_id, prd_id, prd_line_id, prd_qte, prd_ref, prd_name, prd_price_ht, prd_tva_rate, prd_price_ttc, prd_ecotaxe, prd_sell_points, prd_brd_id, prd_date_livr, prd_notes, prd_date_created, prd_need_sync, prd_parent_id, prd_childs_line_id, prd_cod_id, prd_ord_child_id, prd_date_modified, prd_purchase_avg, prd_dps_id, prd_price_brut_ht, prd_weight, prd_is_free, prd_is_delayed, prd_pos)
VALUES
('7', '3', '2', '1', '10', 'REF2', 'produit test 2', '10', '1.2', NULL, '0.0000', NULL, NULL, NULL, '', CURRENT_TIMESTAMP, '0', NULL, NULL, NULL, NULL, CURRENT_TIMESTAMP, NULL, NULL, NULL, NULL, '0', NULL, NULL);




INSERT INTO dlv_zones
(zone_tnt_id, zone_id, zone_name, zone_desc, zone_is_active, zone_type)
VALUES
('7', '1', 'France Métro', '', '1', NULL);

INSERT INTO dlv_zones
(zone_tnt_id, zone_id, zone_name, zone_desc, zone_is_active, zone_type)
VALUES
('7', '2', 'Nouvelle-Aquitaine', '', '1', NULL);




INSERT INTO dlv_services
(srv_tnt_id, srv_id, srv_name, srv_desc, srv_url_site, srv_url_colis, srv_is_active, srv_alert_msg, srv_price_ht, srv_dealer_price_ht, srv_dealer_free_ht, srv_weight_min, srv_weight_max, srv_ord_amount_min, srv_ord_amount_max, srv_pos, srv_accept_consign, srv_img_id, srv_type_id, srv_date_created, srv_date_deleted, srv_date_modified)
VALUES
('7', '1', 'service de livraison', 'description du service de livraison', 'http://www.url.fr/', 'http://www.urlcolis.fr/', '1', 'message d''alerte', '10', '15', '150', '0', '500', '0', '100', NULL, '1', NULL, NULL, '2018-05-07 00:00:00', NULL, '2018-05-07 00:00:00');

INSERT INTO dlv_services
(srv_tnt_id, srv_id, srv_name, srv_desc, srv_url_site, srv_url_colis, srv_is_active, srv_alert_msg, srv_price_ht, srv_dealer_price_ht, srv_dealer_free_ht, srv_weight_min, srv_weight_max, srv_ord_amount_min, srv_ord_amount_max, srv_pos, srv_accept_consign, srv_img_id, srv_type_id, srv_date_created, srv_date_deleted, srv_date_modified)
VALUES
('7', '2', 'service de livraison', 'description du service de livraison', 'http://www.url.fr/', 'http://www.urlcolis.fr/', '1', 'message d''alerte', '10', '15', '150', '0', '500', '0', '100', NULL, '0', NULL, NULL, '2018-05-07 00:00:00', NULL, '2018-05-07 00:00:00');

/* Service utilisé pour les test de mise à jour et de suppression */
INSERT INTO dlv_services
(srv_tnt_id, srv_id, srv_name, srv_desc, srv_url_site, srv_url_colis, srv_is_active, srv_alert_msg, srv_price_ht, srv_dealer_price_ht, srv_dealer_free_ht, srv_weight_min, srv_weight_max, srv_ord_amount_min, srv_ord_amount_max, srv_pos, srv_accept_consign, srv_img_id, srv_type_id, srv_date_created, srv_date_deleted, srv_date_modified)
VALUES
('7', '100', 'service de livraison', 'description du service de livraison', 'http://www.url.fr/', 'http://www.urlcolis.fr/', '1', 'message d''alerte', '10', '15', '150', '0', '500', '0', '100', NULL, '1', NULL, NULL, '2018-05-07 00:00:00', NULL, '2018-05-07 00:00:00');




INSERT INTO dlv_service_available
(avl_tnt_id, avl_srv_id, avl_zone_id, avl_hours_min, avl_hours_max, avl_price_ht, avl_free_ht, avl_date_created, avl_date_modified, avl_date_deleted)
VALUES
('7', '2', '1', NULL, NULL, NULL, NULL, '2018-05-16 00:00:00', NULL, NULL);




INSERT INTO dlv_stores
(str_tnt_id, str_id, str_name, str_title, str_url, str_desc, str_address1, str_address2, str_zipcode, str_city, str_country, str_manager, str_phone, str_fax, str_email, str_website, str_publish, str_allow_delivery, str_cnt_id, str_date_deleted, str_date_modified, str_latitude, str_longitude, str_sct_id, str_tag_title, str_keywords, str_tag_desc, str_is_sync, str_latitude_compute, str_longitude_compute, str_clickandcollect)
VALUES
('7', '1', 'un magasin', NULL, 'www.magasinurl.com', 'une description d''un magasin', 'adresse du magasin', '', '79 000', 'niort', 'FRANCE', '', '05 49 05 05 05', '05 05 05 05 05', '<EMAIL>', 'www.website.fr', '0', '1', NULL, NULL, '2018-05-07 00:00:00', '42', '-1', NULL, NULL, 'mot clé', NULL, '0', NULL, NULL, '0');




INSERT INTO gu_messages
(cnt_tnt_id, cnt_id, cnt_firstname, cnt_lastname, cnt_society, cnt_email, cnt_email_to, cnt_email_cc, cnt_ip, cnt_phone, cnt_subject, cnt_body, cnt_ans_body, cnt_note, cnt_note_delivery, cnt_note_package, cnt_public, cnt_date_created, cnt_date_modified, cnt_date_publish, cnt_date_delete, cnt_usr_id, cnt_usr_sender, cnt_sending, cnt_reply, cnt_answer_count, cnt_prd_id, cnt_news_id, cnt_type, cnt_receiver_email, cnt_spam_id, cnt_prd_type, cnt_prd_model, cnt_prd_brand, cnt_state, cnt_send, cnt_cat_id, cnt_parent, cnt_usr_publish, cnt_is_sync, cnt_wst_id, cnt_str_id, cnt_ord_id)
VALUES
('7', '1', 'alexis', 'chassat', NULL, '<EMAIL>', NULL, NULL, '***************', '0102030405', 'Sujet', 'Un avis sur le produit 1', NULL, '4', '5', '4', '0', '2018-05-14 00:00:00', '2018-05-14 00:00:00', NULL, NULL, '1', NULL, '0', NULL, NULL, 1, NULL, '5', NULL, NULL, NULL, NULL, NULL, '1', '1', NULL, NULL, NULL, '0', 12, NULL, NULL);

INSERT INTO gu_messages
(cnt_tnt_id, cnt_id, cnt_firstname, cnt_lastname, cnt_society, cnt_email, cnt_email_to, cnt_email_cc, cnt_ip, cnt_phone, cnt_subject, cnt_body, cnt_ans_body, cnt_note, cnt_note_delivery, cnt_note_package, cnt_public, cnt_date_created, cnt_date_modified, cnt_date_publish, cnt_date_delete, cnt_usr_id, cnt_usr_sender, cnt_sending, cnt_reply, cnt_answer_count, cnt_prd_id, cnt_news_id, cnt_type, cnt_receiver_email, cnt_spam_id, cnt_prd_type, cnt_prd_model, cnt_prd_brand, cnt_state, cnt_send, cnt_cat_id, cnt_parent, cnt_usr_publish, cnt_is_sync, cnt_wst_id, cnt_str_id, cnt_ord_id)
VALUES
('7', '2', 'alexis', 'chassat', NULL, '<EMAIL>', NULL, NULL, '***************', '0102030405', 'Sujet', 'Un avis sur le produit 1', NULL, '3', '2', '2', '0', '2018-05-14 00:00:00', '2018-05-14 00:00:00', NULL, NULL, '1', NULL, '0', NULL, NULL, 1, NULL, '5', NULL, NULL, NULL, NULL, NULL, '1', '1', NULL, NULL, NULL, '0', 12, NULL, NULL);




INSERT INTO prd_stocks
(sto_tnt_id, sto_prd_id, sto_dps_id, sto_qte, sto_com, sto_res, sto_res_web, sto_prepa, sto_mini, sto_maxi, sto_date_modified, sto_date_restocking, sto_is_deleted)
VALUES
('7', '1', '9868', '20', '15', '0', '0', '5', '10', '50', '2018-05-14 00:00:00', NULL, '0');




INSERT INTO cfg_emails
(email_tnt_id, email_wst_id, email_code, email_name, email_desc, email_allow_from, email_allow_to, email_allow_cc, email_allow_bcc, email_from, email_to, email_cc, email_bcc, email_pos)
VALUES
('7', '12', 'newsletter', 'Inscription à la newsletter', 'Un mail vous est directement envoyé lorsqu''une personne demande votre newsletter.', '1', '0', '0', '1', '<EMAIL>', '', '', '', '0');




INSERT INTO prc_tvas
(ptv_tnt_id, ptv_id, ptv_tva_rate, ptv_prd_id, ptv_is_sync, ptv_cac_id, ptv_cnt_code, ptv_date_modified, ptv_date_deleted)
VALUES
('7', '1', '1.2', '1', '0', NULL, NULL, '2018-05-16 00:00:00', NULL);