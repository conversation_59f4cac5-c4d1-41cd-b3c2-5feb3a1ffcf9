<?php

/**
 * \defgroup geoloc_adresses Géolocalisation 
 * \ingroup adresses
 * @{
 * \page api-addresses-geoloc-upd Mise à jour 
 *	
 * Cette fonction va faire une requete google pour géolocaliser l'adresse
 *
 *		\code
 *			PUT	/addresses/geoloc/
 *		\endcode
 *
 * @param id Obligatoire, Identifiant de l'adresse
 *
 * @return true si la mise à jour est effectuée sinon indique l'erreur 
 * @}
*/

switch( $method ){
	case 'upd':

		if( !isset($_REQUEST['id']) ){
			throw new Exception("Il manque le paramètre l'identifiant de l'adresse");
		}

		if( !gu_adresses_refresh_coordinates($_REQUEST['id']) ){
			throw new Exception("Erreur dans la mise à jour des coordonnées.");
		}
		$result = true;
		break;
}
