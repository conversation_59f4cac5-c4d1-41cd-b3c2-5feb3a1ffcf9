<?php

// \cond onlyria
/**	\defgroup gu_users_cgv Gestion des acceptations des CGV
 *	\ingroup model_users
 *	Les fonctions de ce module permettent de gérer l'enregistrement des acceptations des versions de CGV par un utilisateur
 *	@{
 */

/** Cette fonction permet d'ajouter une acceptation de CGV.
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@param int $ver_id Obligatoire, identifiant d'une version de cgv
 *	@return bool True Si l'enregistrement s'est correctement déroulée, False dans le cas contraire
 */
function gu_users_cgv_add( $usr_id, $ver_id ){
	global $config;

	if( !gu_users_exists($usr_id) ){
		return false;
	}

	if( !cgv_versions_exists($ver_id) ){
		return false;
	}

	return ria_mysql_query('
		replace into gu_users_cgv
			( usg_tnt_id, usg_usr_id, usg_ver_id, usg_date_accepted )
		values
			( '.$config['tnt_id'].', '.$usr_id.', '.$ver_id.', now() )
	');

}

/** Cette fonction permet de récupérer toute les acceptation de CGV d'un client trié de la plus récente a la plus ancienne
 * @param int $usr_id Obligatoire, identifiant d'un compte client
 *
 * @return resource un résultat de requête MySQL contenant les données suivantes
 * 		- id : identifiat de la version de cgv
 * 		- name : nom de la version de cgv
 * 		- wst : identifiant du site associé a la version de cgv
 * 		- date_accepted : date d'acceptation de la version de cgv
 * @return bool false en cas d'erreur
 */
function gu_users_cgv_get( $usr_id ){
	global $config;

	if( !gu_users_exists($usr_id) ){
		return false;
	}

	return ria_mysql_query('
		select usg_ver_id as id, usg_date_accepted as date_accepted, ver_wst_id as wst, ver_name  as name
		from gu_users_cgv, cgv_versions
		where usg_tnt_id ='.$config['tnt_id'].'
			and ver_tnt_id = '.$config['tnt_id'].'
			and usg_usr_id = '.$usr_id.'
			and usg_ver_id = ver_id
		order by usg_date_accepted DESC
	');
}



/** Cette fonction permet de savoir si un compte client a accepté une version de CGV
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@param $ver_id Obligatoire, identifiant d'une version de cgv
 *	@return bool True si le compte a déjà accepté les CGV, False dans le cas contraire
 */
function gu_users_cgv_exists( $usr_id, $ver_id ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id<=0 ){
		return false;
	}

	if( !is_numeric($ver_id) || $ver_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from gu_users_cgv
		where usg_tnt_id = '.$config['tnt_id'].'
			and usg_usr_id = '.$usr_id.' and usg_ver_id = '.$ver_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer la date à laquelle les CGV ont été acceptées
 *	@param int $usr_id Obligatoire, identifiant d'un compte client
 *	@param $ver_id Obligatoire, identifiant d'une version de cgv
 *	@return La date d'acceptation (format EN), False si le compte client n'a jamais accepté les CGV
 */
function gu_users_cgv_get_date_accepted( $usr_id, $ver_id ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id<=0 ){
		return false;
	}

	if( !is_numeric($ver_id) || $ver_id<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select usg_date_accepted as "accepted"
		from gu_users_cgv
		where usg_tnt_id = '.$config['tnt_id'].'
			and usg_usr_id = '.$usr_id.' and usg_ver_id = '.$ver_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['accepted'];
}

/// @}
// \endcond
