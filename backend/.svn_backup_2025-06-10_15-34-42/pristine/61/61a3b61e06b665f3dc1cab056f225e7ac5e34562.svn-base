<?php

class CreneauWS extends \SoapClient {

	/**
	 * @var array $classmap The defined classes
	 */
	private static $classmap = array(
		'searchDeliverySlot'            => '\\searchDeliverySlot',
		'searchDeliverySlotResponse'    => '\\searchDeliverySlotResponse',
		'deliverySlotResponse'          => '\\deliverySlotResponse',
		'wsResponse'                    => '\\wsResponse',
		'slot'                          => '\\slot',
		'confirmDeliverySlotV2'         => '\\confirmDeliverySlotV2',
		'confirmDeliverySlotV2Response' => '\\confirmDeliverySlotV2Response',
		'serviceResponseV2'             => '\\serviceResponseV2',
		'productServiceV2'              => '\\productServiceV2',
		'productService'                => '\\productService',
		'getAdresseGeocodage'           => '\\getAdresseGeocodage',
		'getAdresseGeocodageResponse'   => '\\getAdresseGeocodageResponse',
		'geocodageResponse'             => '\\geocodageResponse',
		'confirmDeliverySlot'           => '\\confirmDeliverySlot',
		'confirmDeliverySlotResponse'   => '\\confirmDeliverySlotResponse',
		'serviceResponse'               => '\\serviceResponse',
	);

	/**
	 * @param array  $options A array of config values
	 * @param string $wsdl    The wsdl file to use
	 */
	public function __construct( array $options = array(), $wsdl = null ){
		global $config;

		foreach( self::$classmap as $key => $value ){
			if( !isset( $options['classmap'][$key] ) ){
				$options['classmap'][$key] = $value;
			}
		}
		$options = array_merge( array(
			'features' => 1,
		), $options );
		if( !$wsdl ){
			$wsdl = 'https://ws.chronopost.fr/rdv-cxf/services/CreneauServiceWS?wsdl';
		}
		parent::__construct( $wsdl, $options );
		$namespace = 'http://cxf.soap.ws.creneau.chronopost.fr/';
		$soapheader = array();
		$soapheader[] = new \SoapHeader( $namespace, 'accountNumber', $config['chronordv_accountNumber'] );
		$soapheader[] = new \SoapHeader( $namespace, 'password', $config['chronordv_password'] );
		$this->__setSoapHeaders( $soapheader );
	}

	/**
	 * @param searchDeliverySlot $parameters
	 *
	 * @return searchDeliverySlotResponse
	 */
	public function searchDeliverySlot( searchDeliverySlot $parameters ){
		return $this->__soapCall( 'searchDeliverySlot', array( $parameters ) );
	}

	/**
	 * @param confirmDeliverySlotV2 $parameters
	 *
	 * @return confirmDeliverySlotV2Response
	 */
	public function confirmDeliverySlotV2( confirmDeliverySlotV2 $parameters ){
		return $this->__soapCall( 'confirmDeliverySlotV2', array( $parameters ) );
	}

	/**
	 * @param getAdresseGeocodage $parameters
	 *
	 * @return getAdresseGeocodageResponse
	 */
	public function getAdresseGeocodage( getAdresseGeocodage $parameters ){
		return $this->__soapCall( 'getAdresseGeocodage', array( $parameters ) );
	}

	/**
	 * @param confirmDeliverySlot $parameters
	 *
	 * @return confirmDeliverySlotResponse
	 */
	public function confirmDeliverySlot( confirmDeliverySlot $parameters ){
		return $this->__soapCall( 'confirmDeliverySlot', array( $parameters ) );
	}

}
