<?php
// \cond onlyria

/** \defgroup model_products_pre_orders Pré-Commandes / Commandes de pré-saison
 *	\ingroup pieces
 *	Ce module comprend les fonctions nécessaires à la gestion des commandes de pré-saison.
 *	@{
 */

require_once('db.inc.php');
require_once('users.inc.php');

/// Année pour les pré-commandes
define( 'PRD_PRE_ORDERS_YEAR', 2022 );
/// Modèle de pré-commande pré-rempli avec les volumes commandés l'année précédente
define( 'PRD_PRE_ORDER_MODEL_FILLED', '1' );
/// Modèle de pré-commande vide
define( 'PRD_PRE_ORDER_MODEL_EMPTY', '0' );
/// numéro du premier mois (sur l'année précédent PRD_PRE_ORDERS_YEAR)
define( 'FIRST_MONTH', 10 );

/**	Indique si les pré-saisons sont ouvertes ou fermées pour une année donnée
 *	@param $year Année pour laquelle on doit déterminer si les pré-saisons ouvertes ou non
 *	@todo Programmer le corps de cette fonction
 *	@return Cette fonction retourne toujours false, tant que les périodes de pré-saisons n'auront pas été programmées
 */
function prd_pre_orders_is_closed( $year ){
	return false;
}

/**	Cette fonction est chargée de recalculer les commandes passées en début d'année par l'utilisateur en cours.
 *	Elle est à employer obligatoirement avant tout usage de la fonction \c prd_pre_orders_get_hint .
 *	Afin d'éviter les problèmes posés par l'utilisation simultanée de \c prd_pre_orders_get_hint et de cette fonction,
 *	il est recommandée de n'appeller cette fonction que dans le cadre de traitement de nuit.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur pour lequel on souhaite reconstruire l'aide de saisie
 */
function prd_pre_orders_refresh_hint( $usr ){
	global $config;
	if( !gu_users_exists($usr) ) return false;

	ria_mysql_query('delete from prd_pre_orders_hint where ord_tnt_id='.$config['tnt_id'].' and ord_usr_id='.$usr);
	ria_mysql_query('
		insert into prd_pre_orders_hint
			(ord_tnt_id,ord_usr_id,ord_prd_id,ord_prd_month,ord_prd_qte)
		select	'.$config['tnt_id'].', bl_usr_id, prd_id, month(bl_date), sum(prd_qte)
		from	ord_bl, ord_bl_products
		where	bl_tnt_id='.$config['tnt_id'].' and prd_tnt_id=bl_tnt_id and bl_id=prd_bl_id
				and bl_state_id=8 and bl_masked = 0
				and (
					( year(bl_date)=('.PRD_PRE_ORDERS_YEAR.' - 1) and month(bl_date)<'.FIRST_MONTH.' )
					or ( year(bl_date)=('.PRD_PRE_ORDERS_YEAR.' - 2) and month(bl_date)>='.FIRST_MONTH.' )
				)
				and bl_usr_id='.$usr.'
		group by prd_id, month(bl_date);
	');

}

/**	Cette fonction permet le recalcul complet des indications de commandes de pré-saison.
 *
 */
function prd_pre_orders_refresh_hints(){
	$users = gu_users_get();
	while( $u = ria_mysql_fetch_array($users) )
		prd_pre_orders_refresh_hint( $u['id'] );
}

/**	Cette fonction retourne les statistiques sur les commandes de l'année précédente. Pour fournir ses résultats,
 *	cette fonction utilise une table pré-calculée. Il faut donc s'assurer que la fonction \c prd_pre_orders_refresh_hint
 *	a bien été appellée avant toute utilisation.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur pour lequel on souhaite retourner l'aide à la décision
 *	@return Ces statistiques sont retournées sous la forme d'un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du produit
 *		- ref : référence du produit
 *		- name : désignation du produit
 *		- sleep: produit en sommeil
 *		- m1 : quantité commandée en janvier
 *		- m2 : quantité commandée en février
 *		- m3 : quantité commandée en mars
 *		- m4 : quantité commandée en avril
 *		- m5 : quantité commandée en mai
 *		- m6 : quantité commandée en juin
 *		- m7 : quantité commandée en juillet
 *		- m8 : quantité commandée en août
 *		- m9 : quantité commandée en septembre
 *		- m10 : quantité commandée en octobre
 *		- m11 : quantité commandée en novembre
 *		- m12 : quantité commandée en décembre
 */
function prd_pre_orders_get_hint( $usr ){
	global $config;
	if( !gu_users_exists($usr) ) return false;

	return ria_mysql_query('
		select prd_id as id, prd_ref as ref, prd_name as name, prd_centralized as centralized, prd_sleep as sleep,
			sum(m1.ord_prd_qte) as m1, sum(m2.ord_prd_qte) as m2, sum(m3.ord_prd_qte) as m3,
			sum(m4.ord_prd_qte) as m4, sum(m5.ord_prd_qte) as m5, sum(m6.ord_prd_qte) as m6,
			sum(m7.ord_prd_qte) as m7, sum(m8.ord_prd_qte) as m8, sum(m9.ord_prd_qte) as m9,
			sum(m10.ord_prd_qte) as m10, sum(m11.ord_prd_qte) as m11, sum(m12.ord_prd_qte) as m12
		from prd_products
			left join prd_pre_orders_hint as m1 on (m1.ord_tnt_id='.$config['tnt_id'].' and prd_id=m1.ord_prd_id and m1.ord_prd_month=1 and m1.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m2 on (m2.ord_tnt_id='.$config['tnt_id'].' and prd_id=m2.ord_prd_id and m2.ord_prd_month=2 and m2.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m3 on (m3.ord_tnt_id='.$config['tnt_id'].' and prd_id=m3.ord_prd_id and m3.ord_prd_month=3 and m3.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m4 on (m4.ord_tnt_id='.$config['tnt_id'].' and prd_id=m4.ord_prd_id and m4.ord_prd_month=4 and m4.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m5 on (m5.ord_tnt_id='.$config['tnt_id'].' and prd_id=m5.ord_prd_id and m5.ord_prd_month=5 and m5.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m6 on (m6.ord_tnt_id='.$config['tnt_id'].' and prd_id=m6.ord_prd_id and m6.ord_prd_month=6 and m6.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m7 on (m7.ord_tnt_id='.$config['tnt_id'].' and prd_id=m7.ord_prd_id and m7.ord_prd_month=7 and m7.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m8 on (m8.ord_tnt_id='.$config['tnt_id'].' and prd_id=m8.ord_prd_id and m8.ord_prd_month=8 and m8.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m9 on (m9.ord_tnt_id='.$config['tnt_id'].' and prd_id=m9.ord_prd_id and m9.ord_prd_month=9 and m9.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m10 on (m10.ord_tnt_id='.$config['tnt_id'].' and prd_id=m10.ord_prd_id and m10.ord_prd_month=10 and m10.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m11 on (m11.ord_tnt_id='.$config['tnt_id'].' and prd_id=m11.ord_prd_id and m11.ord_prd_month=11 and m11.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m12 on (m12.ord_tnt_id='.$config['tnt_id'].' and prd_id=m12.ord_prd_id and m12.ord_prd_month=12 and m12.ord_usr_id='.$usr.')
		where prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null and prd_publish and prd_publish_cat and prd_orderable
		group by prd_id, prd_ref, prd_name
		order by prd_ref;
	');
}

/**	Cette fonction retourne les informations de pré-commande. Pour fournir ses résultats,
 *	cette fonction utilise une table pré-calculée. Il faut donc s'assurer que la fonction \c prd_pre_orders_refresh_hint
 *	a bien été appellée avant toute utilisation.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur
 *	@param $all Facultatif, indique s'il faut retourner la totalité des produits, ou seulement ceux qui ont été commandés
 *	@return Ces informations sont retournées sous la forme d'un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du produit
 *		- ref : référence du produit
 *		- name : désignation du produit
 *		- sleep: produit en sommeil
 *		- m1 : quantité commandée en janvier de l'année précédente
 *		- p1 : pré-commande pour janvier de cette année
 *		- m2 : quantité commandée en février de l'année précédente
 *		- p2 : pré-commande pour février de cette année
 *		- m3 : quantité commandée en mars de l'année précédente
 *		- p3 : pré-commande pour mars de cette année
 *		- m4 : quantité commandée en avril de l'année précédente
 *		- p4 : pré-commande pour avril de cette année
 *		- m5 : quantité commandée en mai de l'année précédente
 *		- p5 : pré-commande pour mai de cette année
 *		- m6 : quantité commandée en juin de l'année précédente
 *		- p6 : pré-commande pour juin de cette année
 *		- m7 : quantité commandée en juillet de l'année précédente
 *		- p7 : pré-commande pour juillet de cette année
 *		- m8 : quantité commandée en août de l'année précédente
 *		- p8 : pré-commande pour août de cette année
 *		- m9 : quantité commandée en septembre de l'année précédente
 *		- p9 : pré-commande pour septembre de cette année
 *		- m10 : quantité commandée en octobre de l'année précédente
 *		- p10 : pré-commande pour octobre de cette année
 *		- m11 : quantité commandée en novembre de l'année précédente
 *		- p11 : pré-commande pour novembre de cette année
 *		- m12 : quantité commandée en décembre de l'année précédente
 *		- p12 : pré-commande pour décembre de cette année
 *		- cat_name : Nom de la catégorie
 *		- tot_n2 : Totaux commandés pour le produit lors de l'année N-2
 */
function prd_pre_orders_get( $usr, $all=true ){
	global $config;
	if( !gu_users_exists($usr) ) return false;

	$subcat = '
		select if(cat_title="",cat_name,cat_title) as cat_name
		from prd_categories
		join prd_classify on cat_id=cly_cat_id and cat_tnt_id=cly_tnt_id
		where cat_date_deleted is null and cat_tnt_id='.$config['tnt_id'].'
		and cly_prd_id=prd_id
		order by cat_products desc, cat_publish desc
		limit 0, 1
	';

	$sql = '
		select prd_id as id, prd_ref as ref, prd_name as name, prd_centralized as centralized, prd_sleep as sleep,
		('.$subcat.') as cat_name, (
				select '.($config['use_decimal_qte'] ? 'sum(bp.prd_qte)' : 'cast(sum(bp.prd_qte) as signed)' ).' from ord_bl_products as bp
				join ord_bl on bl_id=prd_bl_id and bl_tnt_id=bp.prd_tnt_id
				where bl_state_id=8 and bl_masked = 0
				and (
					( year(bl_date)=('.PRD_PRE_ORDERS_YEAR.' - 2) and month(bl_date)<'.FIRST_MONTH.' )
					or ( year(bl_date)=('.PRD_PRE_ORDERS_YEAR.' - 3) and month(bl_date)>='.FIRST_MONTH.' )
				)
				and bl_usr_id='.$usr.' and bl_tnt_id='.$config['tnt_id'].' and bp.prd_id=p.prd_id
			) as tot_n2,
			sum(m1.ord_prd_qte) as m1, sum(p1.ord_prd_qte) as p1,
			sum(m2.ord_prd_qte) as m2, sum(p2.ord_prd_qte) as p2,
			sum(m3.ord_prd_qte) as m3, sum(p3.ord_prd_qte) as p3,
			sum(m4.ord_prd_qte) as m4, sum(p4.ord_prd_qte) as p4,
			sum(m5.ord_prd_qte) as m5, sum(p5.ord_prd_qte) as p5,
			sum(m6.ord_prd_qte) as m6, sum(p6.ord_prd_qte) as p6,
			sum(m7.ord_prd_qte) as m7, sum(p7.ord_prd_qte) as p7,
			sum(m8.ord_prd_qte) as m8, sum(p8.ord_prd_qte) as p8,
			sum(m9.ord_prd_qte) as m9, sum(p9.ord_prd_qte) as p9,
			sum(m10.ord_prd_qte) as m10, sum(p10.ord_prd_qte) as p10,
			sum(m11.ord_prd_qte) as m11, sum(p11.ord_prd_qte) as p11,
			sum(m12.ord_prd_qte) as m12, sum(p12.ord_prd_qte) as p12
		from prd_products as p
			left join prd_pre_orders_hint as m1 on (m1.ord_tnt_id='.$config['tnt_id'].' and prd_id=m1.ord_prd_id and m1.ord_prd_month=1 and m1.ord_usr_id='.$usr.')
			left join prd_pre_orders as p1 on (p1.ord_tnt_id='.$config['tnt_id'].' and prd_id=p1.ord_prd_id and p1.ord_prd_month=1 and p1.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m2 on (m2.ord_tnt_id='.$config['tnt_id'].' and prd_id=m2.ord_prd_id and m2.ord_prd_month=2 and m2.ord_usr_id='.$usr.')
			left join prd_pre_orders as p2 on (p2.ord_tnt_id='.$config['tnt_id'].' and prd_id=p2.ord_prd_id and p2.ord_prd_month=2 and p2.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m3 on (m3.ord_tnt_id='.$config['tnt_id'].' and prd_id=m3.ord_prd_id and m3.ord_prd_month=3 and m3.ord_usr_id='.$usr.')
			left join prd_pre_orders as p3 on (p3.ord_tnt_id='.$config['tnt_id'].' and prd_id=p3.ord_prd_id and p3.ord_prd_month=3 and p3.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m4 on (m4.ord_tnt_id='.$config['tnt_id'].' and prd_id=m4.ord_prd_id and m4.ord_prd_month=4 and m4.ord_usr_id='.$usr.')
			left join prd_pre_orders as p4 on (p4.ord_tnt_id='.$config['tnt_id'].' and prd_id=p4.ord_prd_id and p4.ord_prd_month=4 and p4.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m5 on (m5.ord_tnt_id='.$config['tnt_id'].' and prd_id=m5.ord_prd_id and m5.ord_prd_month=5 and m5.ord_usr_id='.$usr.')
			left join prd_pre_orders as p5 on (p5.ord_tnt_id='.$config['tnt_id'].' and prd_id=p5.ord_prd_id and p5.ord_prd_month=5 and p5.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m6 on (m6.ord_tnt_id='.$config['tnt_id'].' and prd_id=m6.ord_prd_id and m6.ord_prd_month=6 and m6.ord_usr_id='.$usr.')
			left join prd_pre_orders as p6 on (p6.ord_tnt_id='.$config['tnt_id'].' and prd_id=p6.ord_prd_id and p6.ord_prd_month=6 and p6.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m7 on (m7.ord_tnt_id='.$config['tnt_id'].' and prd_id=m7.ord_prd_id and m7.ord_prd_month=7 and m7.ord_usr_id='.$usr.')
			left join prd_pre_orders as p7 on (p7.ord_tnt_id='.$config['tnt_id'].' and prd_id=p7.ord_prd_id and p7.ord_prd_month=7 and p7.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m8 on (m8.ord_tnt_id='.$config['tnt_id'].' and prd_id=m8.ord_prd_id and m8.ord_prd_month=8 and m8.ord_usr_id='.$usr.')
			left join prd_pre_orders as p8 on (p7.ord_tnt_id='.$config['tnt_id'].' and prd_id=p8.ord_prd_id and p8.ord_prd_month=8 and p8.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m9 on (m9.ord_tnt_id='.$config['tnt_id'].' and prd_id=m9.ord_prd_id and m9.ord_prd_month=9 and m9.ord_usr_id='.$usr.')
			left join prd_pre_orders as p9 on (p9.ord_tnt_id='.$config['tnt_id'].' and prd_id=p9.ord_prd_id and p9.ord_prd_month=9 and p9.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m10 on (m10.ord_tnt_id='.$config['tnt_id'].' and prd_id=m10.ord_prd_id and m10.ord_prd_month=10 and m10.ord_usr_id='.$usr.')
			left join prd_pre_orders as p10 on (p10.ord_tnt_id='.$config['tnt_id'].' and prd_id=p10.ord_prd_id and p10.ord_prd_month=10 and p10.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m11 on (m11.ord_tnt_id='.$config['tnt_id'].' and prd_id=m11.ord_prd_id and m11.ord_prd_month=11 and m11.ord_usr_id='.$usr.')
			left join prd_pre_orders as p11 on (p11.ord_tnt_id='.$config['tnt_id'].' and prd_id=p11.ord_prd_id and p11.ord_prd_month=11 and p11.ord_usr_id='.$usr.')
			left join prd_pre_orders_hint as m12 on (m12.ord_tnt_id='.$config['tnt_id'].' and prd_id=m12.ord_prd_id and m12.ord_prd_month=12 and m12.ord_usr_id='.$usr.')
			left join prd_pre_orders as p12 on (p12.ord_tnt_id='.$config['tnt_id'].' and prd_id=p12.ord_prd_id and p12.ord_prd_month=12 and p12.ord_usr_id='.$usr.')
		where prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null and prd_publish and prd_publish_cat and prd_orderable
		group by prd_id
	';
	if( !$all ){
		$sql .= '
			having p1!=0 or p2!=0 or p3!=0 or p4!=0 or p5!=0 or p6!=0 or p7!=0
		';
	}
	$sql .= '
		order by ('.$subcat.'), prd_ref;
	';
	// print $sql;

	return ria_mysql_query($sql);
}

/**	Cette fonction retourne les produits d'une commande de pré-saison dans un format compatible avec les commandes classiques.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur pour lequel charger une pré-commande
 *	@param $month Obligatoire, Identifiant du mois pour lequel charger une pré-commande
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du produit
 *			- ref : référence du produit
 *			- name : désignation du produit
 *			- qte : quantité commandée
 *			- price_ht : prix unitaire du produit, hors taxes
 *			- tva_rate : taux de tva s'appliquant au produit
 *			- price_ttc : prix unitaire du produit, toutes taxes comprises
 *			- total_ht : total hors taxes pour ce produit et cette quantité (ligne de commande)
 *			- total_ttc : total toutes taxes comprises pour cette ligne de commande
 */
function ord_pre_orders_products_get( $usr, $month ){
	global $config;
	if( !is_numeric($usr) ) return false;
	if( !is_numeric($month) ) return false;

	$usersql = !isset($_SESSION['usr_tnt_id']) || (isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']>0) ? $usr : 0;

	$exempt = gu_users_is_tva_exempt( $usersql );
	$usr_holder = gu_users_get_prices_holder( $usersql );
	$prc = gu_users_get_prc( $usr_holder, true );
	$usr_ref = gu_users_get_ref( $usr_holder, true );

	$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

	$sql = '
		select
			prd.prd_id as id, prd_ref as ref, prd_name as name, ord_prd_qte as qte,
			get_price_ht( prd.prd_tnt_id, prd.prd_id, '.$usr_holder.', ord_prd_qte, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", prd.prd_brd_id, prd.prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'prd.prd_ecotaxe').' ) as price_ht,
			'.( $exempt ? '1' : 'get_tva( prd.prd_tnt_id, prd.prd_id, '.gu_users_get_accouting_category( $usersql, true ).', '.gu_users_get_cnt_code($usersql, true).' )' ).' as tva_rate
		from
			prd_pre_orders inner join
			prd_products as prd
				on ( prd.prd_tnt_id='.$config['tnt_id'].' and ord_prd_id=prd.prd_id )
		where
			ord_tnt_id='.$config['tnt_id'].' and
			ord_usr_id='.$usr.' and
			ord_prd_month='.$month.'
	';

	$sql = '
		select
			d_tbl.*,
			(d_tbl.tva_rate * d_tbl.price_ht) as price_ttc,
			(d_tbl.price_ht * d_tbl.qte) as total_ht,
			(d_tbl.price_ht * d_tbl.tva_rate * d_tbl.qte) as total_ttc
		from ('.$sql.') as d_tbl
	';

	$r = ria_mysql_query($sql);

	// débugage tva
	if( ria_mysql_errno() ){
		error_log('ord_pre_orders_products_get - '.mysql_error().' - '.$sql);
	}

	return $r;
}

/**	Cette fonction permet le chargement des totaux de commande (HT et TTC) pour un utilisateur et un mois donné.
 *	Le détail des produits commandés n'est pas retourné par cette fonction.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur dont on souhaite charger les commandes de pré-saison en cours d'édition
 *	@param $month Obligatoire, identifiant du mois dont on souhaite charger les commande de pré-saison en cours d'édition. Entier compris entre 1 et 12 (inclus).
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- total_ht : montant total hors taxes de la commande (float)
 *			- total_ttc : montant total toutes taxes comprises de la commande (float)
 */
function ord_pre_orders_get( $usr, $month ){
	global $config;
	if( !is_numeric($usr) ) return false;
	if( !is_numeric($month) ) return false;

	$usersql = !isset($_SESSION['usr_tnt_id']) || (isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']>0) ? $usr : 0;

	$exempt = gu_users_is_tva_exempt( $usersql );
	$usr_holder = gu_users_get_prices_holder( $usersql );
	$prc = gu_users_get_prc( $usr_holder, true );
	$usr_ref = gu_users_get_ref( $usr_holder, true );

	$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

	$sql = '
		select
			get_price_ht( prd.prd_tnt_id, prd.prd_id, '.$usr_holder.', ord_prd_qte, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", prd.prd_brd_id, prd.prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'prd.prd_ecotaxe').' ) as price_ht, '.( $exempt ? '1' : 'get_tva( prd.prd_tnt_id, prd.prd_id, '.gu_users_get_accouting_category( $usersql, true ).', '.gu_users_get_cnt_code($usersql, true).' )' ).' as tva_rate, ord_prd_qte
		from
			prd_pre_orders inner join
			prd_products as prd
				on ( prd.prd_tnt_id='.$config['tnt_id'].' and ord_prd_id=prd.prd_id )
		where
			ord_tnt_id='.$config['tnt_id'].' and
			ord_usr_id='.$usr.' and
			ord_prd_month='.$month.'
	';

	$sql = '
		select sum(d_tbl.price_ht * d_tbl.ord_prd_qte) as total_ht, sum(d_tbl.price_ht * d_tbl.tva_rate * d_tbl.ord_prd_qte) as total_ttc
		from ('.$sql.') as d_tbl
	';

	$r = ria_mysql_query($sql);

	// débugage tva
	if( ria_mysql_errno() ){
		error_log('ord_pre_orders_get - '.mysql_error().' - '.$sql);
	}

	return $r;
}

/**	Cette fonction est chargée de recopier les quantités de pré-commande de l'année passée pour renseigner
 *	les précommandes de cette année.
 *	@param int $usr Identifiant de l'utilisateur pour lequel l'opération est à mener.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_pre_orders_copy_hints( $usr ){
	global $config;
	if( !gu_users_exists($usr) ) return false;

	ria_mysql_query('delete from prd_pre_orders where ord_tnt_id='.$config['tnt_id'].' and ord_usr_id='.$usr);
	ria_mysql_query('
		insert into prd_pre_orders
			(ord_tnt_id,ord_usr_id,ord_prd_id,ord_prd_month,ord_prd_qte)
		select ord_tnt_id, ord_usr_id,ord_prd_id,ord_prd_month,ord_prd_qte
		from prd_pre_orders_hint
		where ord_tnt_id='.$config['tnt_id'].' and ord_usr_id='.$usr.'
	');
}

/**	Cette fonction est chargée de clore les commandes de pré-saison pour un utilisateur donné.
 *	Cette opération concrétise l'intention d'achat du client et est un préalable obligatoire à l'importation.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur dont on souhaite finaliser les commandes de pré-saison.
 */
function prd_pre_orders_finalize( $usr ){
	if(prd_pre_orders_is_finalized( $usr )) return false;

	global $config;

	if( !is_numeric($usr) ) return false;

	$res = ria_mysql_query('
		update prd_pre_orders_options set opt_finalized=1 where opt_tnt_id='.$config['tnt_id'].' and opt_usr_id='.$usr.' and opt_year='.PRD_PRE_ORDERS_YEAR.'
	');
	if( $res ){
		$rusr = gu_users_get($usr);
		$adrinv = ria_mysql_result($rusr,0,'adr_invoices');
		$adrdlv = ria_mysql_result($rusr,0,'adr_delivery');
		for( $month=1; $month<=12; $month++ ){
			$products = ord_pre_orders_products_get( $usr, $month );
			if( ria_mysql_num_rows($products) ){
				// Création de la commande (statut panier)
				if( ria_mysql_query('insert into ord_orders (ord_tnt_id,ord_usr_id,ord_adr_invoices,ord_adr_delivery,ord_date,ord_state_id,ord_ref) values ('.$config['tnt_id'].','.$usr.','.$adrinv.','.$adrdlv.',now(),1,"PRESAISON '.str_pad($month,2,'0',STR_PAD_LEFT).'/'.PRD_PRE_ORDERS_YEAR.'")') ){
					$ord = ria_mysql_insert_id();
					// Insertion des produits
					while( $p = ria_mysql_fetch_array($products) ){
						if( $config['tnt_id'] == 2 ){
							if( isset($config['prd_pre_order_price'][$p['ref']]) && $config['prd_pre_order_price'][$p['ref']] > 0  ){
								ord_products_add_free( $ord, $p['ref'], $p['name'], $config['prd_pre_order_price'][$p['ref']], $p['qte'], null, '', $p['tva_rate']);
							}else{
								ord_products_add( $ord, $p['id'], $p['qte'], '', false);
							}
						}
						else{
							ord_products_add( $ord, $p['id'], $p['qte'], '', false);
						}
						ord_products_notes_update( $ord, $p['id'], '', '01/'.$month.'/'.PRD_PRE_ORDERS_YEAR );
					}
					ord_orders_pay_type_set( $ord, 3 ); // Règlement par l'encours
					ord_orders_update_status( $ord, 3, '' ); // En attente de règlement, rejoins le parcours classique (à importer dans Sage).
				}
			}
		}
	}
	return $res;
}

/**	Cette fonction permet de déterminer si une pré-saison est clotûrée pour un utilisateur donné.
 *	@param int $usr Obligatoire, identifiant de l'utilisateur dont on souhaite finaliser les commandes de pré-saison.
 */
function prd_pre_orders_is_finalized( $usr ){
	global $config;
	if( !is_numeric($usr) ) return false;

	$rclosed = ria_mysql_query('
		select opt_finalized from prd_pre_orders_options where opt_tnt_id='.$config['tnt_id'].' and opt_usr_id='.$usr.' and opt_year='.PRD_PRE_ORDERS_YEAR.'
	');
	if( !ria_mysql_num_rows($rclosed) ){
		return false;
	}else{
		return ria_mysql_result($rclosed,0,0);
	}
}

/**	Cette fonction permet l'enregistrement d'une quantité de commande de pré-saison pour un article donné.
 *	@param int $usr Identifiant de l'utilisateur
 *	@param int $prd Identifiant du produit
 *	@param $month Numéro du mois pour lequel la commande est passée
 *	@param $qte Quantité à pré-commander
 */
function prd_pre_orders_set( $usr, $prd, $month, $qte ){
	global $config;
	if( !gu_users_exists($usr) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( !is_numeric($month) || $month<1 || $month>12 ) return false;
	if( $qte!='' && !is_numeric($qte) ) return false;

	ria_mysql_query('delete from prd_pre_orders where ord_tnt_id='.$config['tnt_id'].' and ord_usr_id='.$usr.' and ord_prd_id='.$prd.' and ord_prd_month='.$month);
	if( is_numeric($qte) && $qte!=0 ){
		return ria_mysql_query('
			insert into prd_pre_orders
				(ord_tnt_id,ord_usr_id,ord_prd_id,ord_prd_month,ord_prd_qte)
			values
				('.$config['tnt_id'].','.$usr.','.$prd.','.$month.','.$qte.')
		');
	}
	return true;
}

/**	Cette fonction permet de supprimer les présaison pour un utilisateur
 *	@param int $usr Identifiant de l'utilisateur
 *	@return bool true en cas de succès et false en cas d'échec
 */
function prd_pre_orders_del( $usr ){
  	global $config;

	if( !gu_users_exists($usr) ) return false;

	return ria_mysql_query('delete from prd_pre_orders where ord_tnt_id='.$config['tnt_id'].' and ord_usr_id='.$usr);
}

/**	Cette fonction permet la définition du modèle de saisi préféré de l'utilisateur.
 *	Il est possible de modifier le modèle de saisie d'une année sur l'autre, mais pas en cours d'année.
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur pour lequel on souhaite définir le modèle de saisie
 *	@param $model Obligatoire, identifiant du modèle sélectionné par l'utilisateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_pre_orders_set_model( $usr, $model ){
	global $config;
	if( !gu_users_exists($usr) ) return false;

	ria_mysql_query('
		replace into prd_pre_orders_options
			(opt_tnt_id,opt_usr_id,opt_year,opt_model)
		values
			('.$config['tnt_id'].','.$usr.','.PRD_PRE_ORDERS_YEAR.','.$model.');
	');

}

/**	Retourne le modèle de saisie choisi pour les commandes de pré-saison pour un utilisateur donné.
 *	@param int $usr Identifiant de l'utilisateur pour lequel on souhaite définir le modèle de saisie
 *	@return le modèle de saisie retenu pour la saison en cours, ou false si aucun modèle n'a été défini
 */
function prd_pre_orders_get_model( $usr ){
	global $config;
	if( !gu_users_exists($usr) ) return false;

	$ropt = ria_mysql_query('
		select opt_model from prd_pre_orders_options
		where opt_tnt_id='.$config['tnt_id'].' and opt_usr_id='.$usr.' and opt_year='.PRD_PRE_ORDERS_YEAR.'
	');
	if( !ria_mysql_num_rows($ropt) ) return false;

	return ria_mysql_result($ropt,0,0);
}

/// @}

// \endcond
