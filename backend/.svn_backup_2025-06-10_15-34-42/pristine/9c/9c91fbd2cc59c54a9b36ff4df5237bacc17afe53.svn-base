<?php

require_once('prd/deposits.inc.php');
require_once('products.inc.php');
require_once('ria.queue.inc.php');

/** \defgroup scm_dps_stocks Gestion des stocks
 *	\ingroup scm
 *	Les fonctions de ce module permettent de gérer les entrées, sorties et échéancier des mouvements de stock.
 *	Les stocks des produits sont répartis dans des dépôts, l'échéancier est basé sur les commandes (clients et fournisseurs)
 *	Attention, l'échéancier est une reproduction des mouvements de l'historique (il n'est pas basé directement sur ord_orders / ord_products)
 *	@{
 */

// \cond onlyria
/**	Cette fonction permet l'ajout d'informations de stocks pour un produit et un dépôt donné.
 *
 *	@param int $prd Identifiant du produit
 *	@param int $dps Identifiant du dépôt
 *	@param $qte Quantité actuellement en stock du produit.
 *	@param $res Quantité du stock actuel déjà réservée pour vente.
 *	@param $com Quantité en commande chez le fournisseur, en attente de livraison.
 *	@param $prepa Quantité en cours de préparation
 *	@param $mini Quantité minimale
 *	@param $maxi Quantité maximale
 *	@param $res_web Optionnel. Quantité du stock actuel déjà réservée pour vente sur la boutique si use_stock_res_web activé
 *	@param string $date_restocking Optionnel, date de dernière remise en stock
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_dps_stocks_add( $prd, $dps, $qte, $res, $com, $prepa, $mini, $maxi, $res_web=0, $date_restocking = null){

	// La validité des identifiants sera contrôlée par l'intégrité référentielle
	if( !is_numeric($prd) ) return false;
	if( !is_numeric($dps) ) return false;
	if( !is_null($date_restocking) && trim($date_restocking) != '' && !isdateheure($date_restocking) ) return false;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	$res = str_replace( array(',',' '), array('.',''), $res );
	$res_web = str_replace( array(',',' '), array('.',''), $res_web );
	$com = str_replace( array(',',' '), array('.',''), $com );
	$prepa = str_replace( array(',',' '), array('.',''), $prepa );
	$mini = str_replace( array(',',' '), array('.',''), $mini );
	$maxi = str_replace( array(',',' '), array('.',''), $maxi );

	global $config;

	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
		$res = floor($res);
		$res_web = floor($res_web);
		$com = floor($com);
		$prepa = floor($prepa);
		$mini = floor($mini);
		$maxi = floor($maxi);
	}

	if( !is_numeric($qte) ) return false;
	if( !is_numeric($res) ) return false;
	if( !is_numeric($res_web) ) return false;
	if( !is_numeric($com) ) return false;
	if( !is_numeric($prepa) ) return false;
	if( !is_numeric($mini) ) return false;
	if( !is_numeric($maxi) ) return false;

	$old = null;
	$restocking = false;

	$r_old = ria_mysql_query('select sto_qte, sto_prepa, sto_date_restocking from prd_stocks where sto_tnt_id = '.$config['tnt_id'].' and sto_prd_id = '.$prd.' and sto_dps_id = '.$dps);

	if (prd_products_get_sleep($prd)) {
		$old = false;

		if ($r_old && ria_mysql_num_rows($r_old)) {
			$old = ria_mysql_fetch_assoc( $r_old );
		}
	}

	if (is_null($date_restocking) && $r_old && ria_mysql_num_rows($r_old)) {
		ria_mysql_data_seek($r_old, 0);

		$tmp_old = ria_mysql_fetch_assoc( $r_old );
		$restocking = ($qte - $prepa) > 0 && $tmp_old['sto_qte'] <= 0;
		$date_restocking = $tmp_old['sto_date_restocking'];
	}elseif( trim($date_restocking) === '' ){
		$date_restocking = null;
	}

	$sql = '
		replace into prd_stocks
			(sto_tnt_id, sto_prd_id, sto_dps_id, sto_qte, sto_res, sto_res_web, sto_com, sto_prepa, sto_mini, sto_maxi, sto_is_deleted, sto_date_restocking)
		values
			('.$config['tnt_id'].', '.$prd.', '.$dps.', '.$qte.', '.$res.','.$res_web.', '.$com.', '.$prepa.', '.$mini.', '.$maxi.', 0, '.($restocking ? 'now()' : ($date_restocking === null ? 'null' : '"'.addslashes( $date_restocking ).'"')).')
	';

	$result = ria_mysql_query($sql);
	if( !$result ){
		return false;
	}

	// recalcul des stocks des nomenclatures qui dépendent de ce produit
	if( !prd_products_is_nomenclature($prd) ){
		prd_nomenclatures_products_rebuild_stock( 0, $prd );
	}

	if ($old !== null) {
		$tsk_search = true;

		if ($old !== false) {
			$in_stock 		= $old['sto_qte'] - $old['sto_prepa'] ? true : false;
			$new_in_stock 	= $qte - $prepa ? true : false;

			if ($in_stock == $new_in_stock) {
				$tsk_search = false;
			}
		}

		if ($tsk_search) {
			try{
				// Index le produit dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
					'cls_id' => CLS_PRODUCT,
					'obj_id_0' => $prd,
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}
		}
	}

	return $result;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour d'informations de stocks pour un produit et un dépôt donné.
 *
 *	@param int $prd_id Obligatoire, identifiant du produit.
 *	@param int $dps_id Obligatoire, identifiant du dépôt.
 *	@param $qte Obligatoire, quantité actuellement en stock du produit.
 *	@param $res Obligatoire, quantité du stock actuel déjà réservée pour vente.
 *	@param $com Obligatoire, quantité en commande chez le fournisseur, en attente de livraison.
 *	@param $prepa Obligatoire, quantité en cours de préparation.
 *	@param $mini Obligatoire, quantité minimale.
 *	@param $maxi Obligatoire, quantité maximale.
 *	@param $get_switch Optionnel. Si activé, la valeur de retour indique si l'état du stock ("rupture" ou "en stock") a changé (true si oui, null si non, false si échec).
 *	@param $res_web Optionnel. Quantité du stock actuel déjà réservée pour vente sur la boutique si use_stock_res_web activé
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 *	@return Si $get_switch est activée, True, False ou Null (voir le commentaire de $get_switch).
 */
function prd_dps_stocks_update( $prd_id, $dps_id, $qte, $res, $com, $prepa, $mini, $maxi, $get_switch=false, $res_web=0, $date_restocking=null ){

	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}
	if( !is_numeric($dps_id) || $dps_id <= 0 ){
		return false;
	}
	if( !is_null($date_restocking) && trim($date_restocking) != '' && !isdateheure($date_restocking) ){
		return false;
	}

	global $config;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	$res = str_replace( array(',',' '), array('.',''), $res );
	$res_web = str_replace( array(',',' '), array('.',''), $res_web );
	$com = str_replace( array(',',' '), array('.',''), $com );
	$prepa = str_replace( array(',',' '), array('.',''), $prepa );
	$mini = str_replace( array(',',' '), array('.',''), $mini );
	$maxi = str_replace( array(',',' '), array('.',''), $maxi );

	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
		$res = floor($res);
		$com = floor($com);
		$prepa = floor($prepa);
		$mini = floor($mini);
		$maxi = floor($maxi);
	}

	if( !is_numeric($qte) ) return false;
	if( !is_numeric($res) ) return false;
	if( !is_numeric($res_web) ) return false;
	if( !is_numeric($com) ) return false;
	if( !is_numeric($prepa) ) return false;
	if( !is_numeric($mini) ) return false;
	if( !is_numeric($maxi) ) return false;

	// détermine si le produit est disponible avant de mettre à jour le stock
	$avail_before = prd_products_is_available( $prd_id );

	// si la ligne n'existe pas, elle est crée
	if( !prd_dps_stocks_exists( $prd_id, $dps_id ) ){
		$res_add = prd_dps_stocks_add( $prd_id, $dps_id, $qte, $res, $com, $prepa, $mini, $maxi, $res_web, $date_restocking );
		$is_available = prd_products_is_available( $prd_id );

		if(!$avail_before && $is_available && in_array($config['tnt_id'], [588, 1279])){
			gu_livr_alerts_notify( $prd_id );
		}
		return $res_add;
	}

	// récupère le précédent stock exact du produit
	$sql = '
		select sto_qte as qte, sto_prepa as prepa
		from prd_stocks
		where sto_tnt_id = '.$config['tnt_id'].' and sto_prd_id = '.$prd_id.' and sto_dps_id = '.$dps_id.'
	';

	$old_stock = false;
	$old = 0;
	$rold = ria_mysql_query($sql);

	if( $rold && ria_mysql_num_rows($rold) ){
		$old_stock = ria_mysql_fetch_assoc( $rold );

		$old = $old_stock['qte'];
	}

	$sql_date_restocking = 'sto_date_restocking';
	if( !is_null($date_restocking) ){
		if( trim($date_restocking) == '' ){
			$sql_date_restocking = 'null';
		}else{
			$sql_date_restocking = '"'.addslashes( $date_restocking ).'"';
		}
	}elseif( ($qte - $prepa) > 0 && $old <= 0 ){
		$sql_date_restocking = 'now()';
	}

	$sql = '
		update prd_stocks
		set
			sto_qte = '.$qte.',
			sto_res = '.$res.',
			sto_res_web = '.$res_web.',
			sto_com = '.$com.',
			sto_prepa = '.$prepa.',
			sto_mini = '.$mini.',
			sto_maxi = '.$maxi.',
			sto_date_restocking = '.$sql_date_restocking.'
		where
			sto_tnt_id = '.$config['tnt_id'].' and sto_prd_id = '.$prd_id.' and sto_dps_id = '.$dps_id.' and sto_is_deleted=0
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	// recalcul des stocks des nomenclatures qui dépendent de ce produit
	if( !prd_products_is_nomenclature( $prd_id ) ){
		prd_nomenclatures_products_rebuild_stock( 0, $prd_id );
	}

	// si le produit n'était plus disponible et qu'il l'est à nouveau
	// attention, la gestion du dépôt ne convient pas pour Boero
	$dps_id_main = prd_deposits_get_main();
	if( !$avail_before && $dps_id_main && prd_products_is_available( $prd_id ) ){
		gu_livr_alerts_notify( $prd_id );
		gu_delayed_alerts_notify( $prd_id );
	}

	// Si le produit est en sommeil et devient indisponible, on le retire des résultats du moteur de recherche
	if( $avail_before && prd_products_get_sleep( $prd_id ) && !prd_products_is_available( $prd_id ) ){
		prd_search_results_unpublish( $prd_id );
	}

	if( $get_switch ){
		$new = $qte - $prepa;
		if( ( $old > 0 && $new <= 0 ) || ( $old <= 0 && $new > 0 ) ){
			return true;
		}else{
			return null;
		}
	}

	if (prd_products_get_sleep($prd_id)) {
		$tsk_search = true;
		if ($old_stock !== false) {
			$in_stock 		= $old_stock['qte'] - $old_stock['prepa'] ? true : false;
			$new_in_stock 	= $qte - $prepa ? true : false;

			if ($in_stock == $new_in_stock) {
				$tsk_search = false;
			}
		}

		if ($tsk_search) {
			try{
				// Index le produit dans le moteur de recherche.
				RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
					'cls_id' => CLS_PRODUCT,
					'obj_id_0' => $prd_id,
				));
			}catch(Exception $e){
				error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
			}
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du champ Quantité minimale pour un produit et un dépôt donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $dps Obligatoire, identifiant du dépôt
 *	@param int $mini Obligatoire, quantité minimale
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_dps_stocks_update_mini( $prd, $dps, $mini ){
	global $config;
	if( !prd_dps_stocks_exists( $prd, $dps ) ) return false;

	$mini = str_replace( array(',',' '), array('.',''), $mini );

	if( !$config['use_decimal_qte'] ){
		$mini = floor($mini);
	}

	if( !is_numeric($mini) ){
		return false;
	}

	$res = ria_mysql_query('
		update prd_stocks set
			sto_mini='.$mini.'
		where sto_tnt_id='.$config['tnt_id'].' and sto_prd_id='.$prd.' and sto_dps_id='.$dps.' and sto_is_deleted=0
	');

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du champ quantité réservé.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $dps Obligatoire, identifiant du dépôt
 *	@param $res Obligatoire, quantité réservée
 *	@param bool $move Optionnel, par défaut à false, mettre True pour retirer la quantité réservé de celle présente
 *	@param bool $zero Optionnel, par défaut à false, mettre True pour empêcher une quantité en dessous de zéro (seulement si $move est à true)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_dps_stocks_update_res( $prd, $dps, $res, $move=false, $zero=false ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	if (!is_numeric($dps) || $dps <= 0) {
		return false;
	}

	global $config;

	$res = str_replace( array(',',' '), array('.',''), $res );
	if( !$config['use_decimal_qte'] ){
		$res = floor($res);
	}

	if( !is_numeric($res) ){
		return false;
	}

	$res = ria_mysql_query('
		update prd_stocks
		set sto_res = '.( !$move ? $res : (!$zero ? '(sto_res - '.$res.')' : 'if((sto_res - '.$res.') < 0, 0, (sto_res - '.$res.'))') ).'
		where sto_tnt_id='.$config['tnt_id'].'
			and sto_prd_id='.$prd.'
			and sto_dps_id='.$dps.'
			and sto_is_deleted=0
	');

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du champ Quantité maximale pour un produit et un dépôt donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $dps Obligatoire, identifiant du dépôt
 *	@param $maxi Obligatoire, quantité maximale
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_dps_stocks_update_maxi( $prd, $dps, $maxi ){
	global $config;
	if( !prd_dps_stocks_exists( $prd, $dps ) ) return false;

	$maxi = str_replace( array(',',' '), array('.',''), $maxi );

	if( !$config['use_decimal_qte'] ){
		$maxi = floor($maxi);
	}

	if( !is_numeric($maxi) ){
		return false;
	}

	$res = ria_mysql_query('
		update prd_stocks set
			sto_maxi='.$maxi.'
		where sto_tnt_id='.$config['tnt_id'].' and sto_prd_id='.$prd.' and sto_dps_id='.$dps.' and sto_is_deleted=0
	');

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction vérifie qu'un produit est stocké (ou stockable) dans un dépôt donné.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $dps Obligatoire, identifiant du dépôt
 *	 @return bool true en cas de succès, false en cas d'échec
 */
function prd_dps_stocks_exists( $prd, $dps ){
	global $config;
	if( !is_numeric($prd) ) return false;
	if( !is_numeric($dps) ) return false;

	return ria_mysql_num_rows(ria_mysql_query('
		select sto_prd_id, sto_dps_id from prd_stocks
		where sto_tnt_id='.$config['tnt_id'].' and sto_prd_id='.$prd.' and sto_dps_id='.$dps.' and sto_is_deleted=0
	'));
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'informations de stocks pour un produit et un dépôt donné.
 *
 *	@param int $prd Identifiant du produit
 *	@param int $dps Identifiant du dépôt
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_dps_stocks_del( $prd=0, $dps=0 ){
	if( !is_numeric($prd) || !is_numeric($dps) ){
		return false;
	}

	global $config;

	$str = '
		update prd_stocks
		set sto_is_deleted=1
		where sto_tnt_id='.$config['tnt_id'].'
			and sto_is_deleted=0
	';

	if( $prd > 0 && $dps > 0)
		$str .= ' and sto_prd_id='.$prd.' and sto_dps_id='.$dps;
	else {
		if( $prd > 0 ){
			$str .= ' and sto_prd_id='.$prd;
		}

		if( $dps > 0 ){
			$str .= ' and sto_dps_id='.$dps;
		}
	}

	$res = ria_mysql_query($str);

	if( !$res ){
		return false;
	}

	// recalcul des stocks des nomenclatures qui dépendent de ce produit
	if( $prd > 0 && !prd_products_is_nomenclature($prd) ){
		prd_nomenclatures_products_rebuild_stock(0, $prd);
	}

	if( prd_products_get_sleep($prd) ){
		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $prd,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return true;
}
// \endcond

/**	Cette fonction permet le chargement des informations de stocks
 *
 *	@param int $prd Facultatif, Identifiant du produit sur lequel filtrer le résultat (ou tableau)
 *	@param int|array $dps Facultatif, identifiant d'un dépôt sur lequel filtrer le résultat (ou tableau)
 *	@param int $start Facultatif, détermine à partir de quel ligne les résultats sont retournés
 *	@param int $limit Facultatif, détermine le nombre de lignes maximal à retourner (0 = illimité)
 *	@param bool $correled Facultatif, si activé et $prd et $dps des tableaux de même taille, les couples prd / dps sont vérifiés un à un
 *	@param int $parent Facultatif, par défaut à False, mettre True pour retourné les informations des articles enfant de $prd (ignoré si $prd n'est pas renseigné ou $correled à true)
 *	@param bool $with_stock Facultatif, par défaut à False, mettre True pour retourné les informations sur le stock actuel cela ajoute une clé stock dans le résultat mysql
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- dps_id : identifiant du dépôt
 *			- dps_name : désignation du dépôt
 *			- dps_is_main : indique s'il s'agit du dépôt principal ou non
 *			- qte : quantité actuellement en stock
 *			- res : quantité réservée
 *			- res_web : quantité réservée sur la boutique en ligne si config use_stock_res_web
 *			- com : quantité commandée
 *			- prepa : quantité en cours de préparation
 *			- mini : quantité minimale à avoir en stock
 *			- maxi : quantité maximale à avoir en stock
 *			- prd : identifiant du produit
 *			- is_sync : dépôt synchronisé oui / non
 *			- date_modified : date de dernière modification
 *			- date_modified_en : date de dernière modification au format EN
 *			- date_restocking : date de remise en stock
 *			- date_restocking_en : date de remise en stock au format EN
 *			- date_date_last_notified : date de dernière notification de stock
 *			- date_date_last_notified_en : date de dernière notification de stock au format EN
 *			- stock : Seulement si la variable de config $with_stock est a true
 *	@return bool False en cas d'échec
 */
function prd_dps_stocks_get( $prd=0, $dps=0, $start=0, $limit=0, $correled=false, $parent=false, $with_stock=false ){

	$prd = control_array_integer( $prd, false );
	if( $prd === false ){
		return false;
	}

	$dps = control_array_integer( $dps, false );
	if( $dps === false ){
		return false;
	}

	if( $correled && sizeof($prd) != sizeof($dps) ){
		return false;
	}

	if (!$correled && $parent) {
		if (count($prd)) {
			$prd = prd_products_get_childs_ids( $prd );
		}
	}

	global $config;

	$sql = '
		select
			dps_id, dps_name, dps_is_main, sto_prd_id as prd, dps_is_sync as is_sync,
			ifnull('.($config['use_decimal_qte'] ? prd_stocks_get_sql() : 'cast(' . prd_stocks_get_sql() . ' as signed)' ).', 0) as qte,
			ifnull('.($config['use_decimal_qte'] ? 'sto_res' : 'cast(sto_res as signed)' ).', 0) as res,
			ifnull('.($config['use_decimal_qte'] ? 'sto_res_web' : 'cast(sto_res_web as signed)' ).', 0) as res_web,
			ifnull('.($config['use_decimal_qte'] ? 'sto_com' : 'cast(sto_com as signed)' ).', 0) as com,
			ifnull('.($config['use_decimal_qte'] ? 'sto_prepa' : 'cast(sto_prepa as signed)' ).', 0) as prepa,
			ifnull('.($config['use_decimal_qte'] ? 'sto_mini' : 'cast(sto_mini as signed)' ).', 0) as mini,
			ifnull('.($config['use_decimal_qte'] ? 'sto_maxi' : 'cast(sto_maxi as signed)' ).', 0) as maxi,
			'.(!$with_stock ? '' :
				($config['use_decimal_qte'] ? prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('sto_prd_id', 'dps_id', true) :
				'cast(' . prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('sto_prd_id', 'dps_id', true).' as signed)' ).' as stock,'
			).'
			date_format(sto_date_modified, "%d/%m/%Y à %H:%i") as date_modified, sto_date_modified as date_modified_en,
			date_format(sto_date_restocking, "%d/%m/%Y à %H:%i") as date_restocking, sto_date_restocking as date_restocking_en,
			date_format(sto_date_last_notified, "%d/%m/%Y à %H:%i") as date_last_notified, sto_date_last_notified as date_last_notified_en
		from
			prd_deposits
			left join prd_stocks on dps_tnt_id = sto_tnt_id and dps_id = sto_dps_id
		where
			dps_tnt_id = '.$config['tnt_id'].' and dps_is_deleted = 0 and ifnull(sto_is_deleted, 0)=0
	';

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($dps); $i++ ){
			$cnds[] = 'dps_id = '.$dps[ $i ].' and sto_prd_id = '.$prd[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($dps) ){
			$sql .= ' and dps_id in ('.implode(', ', $dps).')';
		}
		if( sizeof($prd) ){
			$sql .= ' and sto_prd_id in ('.implode(', ', $prd).')';
		}
	}

	$sql .= '
		order by dps_is_main desc, dps_name
	';

	if( is_numeric($limit) && is_numeric($start) && $start >= 0 && $limit > 0  ){
		$sql .= '
			limit '.$start.', '.$limit.'
		';
	}

	return ria_mysql_query($sql);

}

// \cond onlyria
/** Cette fonction aggère les stocks de tous les dépôts oâ¹ un produit est stocké
 *	Elle est notamment utile pour déterminer si un article est disponible chez Boero (ou tout client fonctionnant en dépôt / client)
 *	@param int $prd Identifiant du produit
 *	@return bool False en cas d'erreur
 *	@return resource un résultat de requête MySQL comprenant les champs suivants :
 *		- phy : Stock sans déduction des préparations
 *		- qte : stock avec préparations déduites
 *		- prepa : Stock en préparation
 *		- res : Stock réservé
 *		- res_web : Stock réservé sur la boutique en ligne si config use_stock_res_web
 *		- com : Stock en commande fournisseur
 *		- mini : Stock minimal moyen par dépôt
 *		- maxi : Stock maximal moyen par dépôt
 */
function prd_dps_stocks_get_sum( $prd ){
	global $config;

	if( !prd_products_exists($prd) ) return false;

	$sql = '
		select
			'.($config['use_decimal_qte'] ? 'sum(' . prd_stocks_get_sql() . ')' : 'cast(sum(' . prd_stocks_get_sql() . ') as signed)' ).' as phy,
			'.($config['use_decimal_qte'] ? 'sum(' . prd_stocks_get_sql() . ')-sum(sto_prepa)' : 'cast((sum(' . prd_stocks_get_sql() . ')-sum(sto_prepa)) as signed)' ).' as qte,
			'.($config['use_decimal_qte'] ? 'sum(sto_res)' : 'cast(sum(sto_res) as signed)' ).' as res,
			'.($config['use_decimal_qte'] ? 'sum(sto_res_web)' : 'cast(sum(sto_res_web) as signed)' ).' as res_web,
			'.($config['use_decimal_qte'] ? 'sum(sto_com)' : 'cast(sum(sto_com) as signed)' ).' as com,
			'.($config['use_decimal_qte'] ? 'sum(sto_prepa)' : 'cast(sum(sto_prepa) as signed)' ).' as prepa,
			'.($config['use_decimal_qte'] ? 'floor(avg(sto_mini))' : 'cast(floor(avg(sto_mini)) as signed)' ).' as mini,
			'.($config['use_decimal_qte'] ? 'floor(avg(sto_maxi))' : 'cast(floor(avg(sto_maxi)) as signed)' ).' as maxi
		from
			prd_deposits
			join prd_stocks
				on ( sto_tnt_id='.$config['tnt_id'].' and dps_id=sto_dps_id )
		where dps_tnt_id='.$config['tnt_id'].' and dps_is_deleted = 0
		and sto_prd_id='.$prd.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la date de dernière remise en stock d'un article dans un dépôt.
 *	@param int $prd_id Identifiant d'un article
 *	@param int $dps_id Identifiant d'un dépôt
 *	@return string|bool La date de dernière remise en stock (false si aucune remise en stock n'a été faite)
 */
function prd_dps_stocks_get_date_restocking( $prd_id, $dps_id ){
	if (!is_numeric($prd_id) || $prd_id<=0) {
		return false;
	}

	if (!is_numeric($dps_id) || $dps_id<=0) {
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select ifnull(sto_date_restocking) as date_restocking
		from prd_stocks
		where sto_tnt_id = '.$config['tnt_id'].'
			and sto_prd_id = '.$prd_id.'
			and sto_dps_id = '.$dps_id.'
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['date_restocking'];
}
// \endcond

// \cond onlyria
/**	Met à jour la quantité en stock et la quantité réservée suite à une commande passée dans la boutique
 *	@param int $prd Obligatoire, identifiant de l'article
 *	@param int $dps Obligatoire, identifiant du dépôt
 *	@param array|string $qte_move Facultatif, quantité à mouvementer
 *	@param bool|int $notify Facultatif, par défault à False, aucune notification de stock minimum atteint sera envoyé, mettre true ou l'identifiant du site sur laquelle est passée la commande pour en envoyer une.
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_stocks_set_reserved( $prd, $dps, $qte_move=1, $notify=false ){
	global $config;

	if( !is_numeric($prd) ) return false;

	if( is_array( $qte_move ) && isset( $qte_move['qte'], $qte_move['res'] ) ){
		$stl = $qte_move;
		$qte_move = $qte_move['res'];

	}else{
		if( !is_numeric($dps) ) return false;
	}

	$qte_move = str_replace( array(',',' '), array('.',''), $qte_move );
	if( !$config['use_decimal_qte'] ){
		$qte_move = floor($qte_move);
	}
	if( !is_numeric($qte_move) ) return false;

	/* spé freevox
		il migre de dépôt et il nous non pas laissé le temps de bien étudier la situation
		https://riastudio.atlassian.net/browse/FREEVOXRG-214
		le dps principale devient 77 neuf id:108582
		si le mouvement de stock ce fait sur 77 neuf donc on vérifie que le mouvement n'est pas suppérieur au stock actuel
		si c'est suppérieur on déplace dans le vieux dépôt 93-NEUF id:9872
	*/
	if( $config['tnt_id'] == 39 && $dps == $config['default_dps_id']){
		$r_sto = prd_dps_stocks_get($prd, $dps, 0, 0, false, false, true);
		if ($r_sto && ria_mysql_num_rows($r_sto)) {
			$sto = ria_mysql_fetch_assoc( $r_sto );
			$stock = $sto['stock'] > 0 ? $sto['stock'] : 0;
			if( $qte_move > $stock ){
				prd_stocks_set_reserved( $prd, 9872, ($qte_move - $stock));
				$qte_move = $stock;
			}
		}
	}
	$alldps = array($dps);
	if( isset($config['dps_reservation_linked']) && in_array($dps, $config['dps_reservation_linked']) ){
		$alldps = $config['dps_reservation_linked'];
	}

	$sql = '
		update
			prd_stocks
		set
			'.( !isset($config['stock_reservation_impact_available']) || $config['stock_reservation_impact_available'] ? 'sto_qte=( sto_qte-'.$qte_move.' ), ' : '' ).'
			'.prd_stocks_sto_res().'=( '.prd_stocks_sto_res().'+'.$qte_move.' )
		where
			sto_prd_id='.$prd.' and
			'.( isset( $stl ) ? '' : 'sto_dps_id in ('.implode(',',$alldps).') and ' ).'
			sto_tnt_id='.$config['tnt_id'].'
	';
	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	if( !isset( $stl ) && $notify ){
		foreach( $alldps as $dps ){
			prd_stock_notify_qty_min( $prd, $dps, $notify );
		}
	}

	// recalcul des stocks des nomenclatures qui dépendent de ce produit
	if( !prd_products_is_nomenclature($prd) ){
		prd_nomenclatures_products_rebuild_stock( 0, $prd );
	}

	if( isset( $stl ) ){
		require_once 'Stock/StockLimited.php';
		$StockLimited = new \Stock\StockLimited();

		if( isset( $stl['id'] ) && $StockLimited->periodIdExists( $stl['id'] ) ){
			return $StockLimited->update( $stl['id'], (int)$prd, 'week', null, null, $stl['qte'], $qte_move );

		}elseif( isset( $stl['start'], $stl['end'] ) ){
			return $StockLimited->add( (int)$prd, 'week', $stl['start'], $stl['end'], $stl['qte'], $qte_move );

		}
		return false;

	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'envoyer un mail d'alerte lorsque le stock d'un produit est égal ou inférieur au stock minimal pour ce produit
 *  @param int $prd_id Obligatoire, Identifiant du produit
 * 	@param int $dps Obligatoire, Identifiant du dépôt
 *	@param int $wst_id Facultatif, identifiant du site d'où provient la commande
 *	@return bool True si l'envoi s'est bien passé, false si échec ou le tenant ne possède pas de config email pour cette notification
 */
function prd_stock_notify_qty_min( $prd_id, $dps, $wst_id=0 ){
	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	if( !is_numeric($dps) || $dps <= 0 ){
		return false;
	}

	global $config;
	$http_host_ria = $config['backoffice_url'];

	if( !is_numeric($wst_id) || $wst_id <= 0 ){
		$wst_id = $config['wst_id'];
	}

	$r_cfg_email = cfg_emails_get( 'notify-stock-min', $wst_id );
	if( !$r_cfg_email || !ria_mysql_num_rows($r_cfg_email)){
		return true;
	}

	$cfg_email = ria_mysql_fetch_assoc( $r_cfg_email );
	if( !isset($cfg_email['to']) || trim($cfg_email['to']) === "" ){
		return true;
	}

	$rstock = prd_dps_stocks_get( $prd_id, $dps);
	if( !$rstock || !ria_mysql_num_rows($rstock) ){
		return false;
	}

	$stock = ria_mysql_fetch_assoc($rstock);
	if( !is_numeric($stock['mini']) || $stock['mini'] <= 0 ){
		return true;
	}

	$remaining_stock = $stock['qte'];
	if( isset($config['stock_reservation_impact_available']) && !$config['stock_reservation_impact_available'] ){
		$remaining_stock = $stock['qte']-$stock['res'];
	}
	if( $remaining_stock > $stock['mini'] ){
		return false;
	}

	$now = new DateTime('now');
	if (isset($config['prd_stock_notify_delay']) && $config['prd_stock_notify_delay'] > 0) {
		$date_last_notified = $stock['date_last_notified_en'];
		if (!is_null($date_last_notified)) {
			$past = new DateTime($date_last_notified);
			$interval = $past->diff($now);
			$hours = $interval->format('%a') * 24 + $interval->h;
			if ($hours < $config['prd_stock_notify_delay']) {
				return false;
			}
		}
	}

	$rdeposite = prd_deposits_get($dps);
	$deposite = ria_mysql_fetch_assoc($rdeposite);
	$rprd = prd_products_get_simple( $prd_id );
	$prd = ria_mysql_fetch_assoc( $rprd );

	require_once('email.inc.php');

	$email = new Email();
	$email->setFrom( 'RiaShop stock minimal <<EMAIL>>' );
	$email->addTo( $cfg_email['to'] );
	$email->addCC( $cfg_email['cc'] );
	$email->addBcc( $cfg_email['bcc'] );
	$email->setSubject( 'Alerte stock minimal atteint' );

	$email->addHtml( '<table width="auto" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="http://www.riastudio.fr/images/template/header/banner.png"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );

	$email->addParagraph( 'Cher client, chère cliente,' );
	$email->addParagraph( 'Votre produit de référence '.htmlspecialchars( $prd['ref'] ).' a atteint le stock minimal de '.$stock['mini'].', dans le dépôt '.htmlspecialchars( $deposite['name'] ).($deposite['ref']==''?'.':', de référence '.htmlspecialchars( $deposite['ref'] )));
	$link_to_product = '<img class="sync" width="16" height="16" src="https://'.$http_host_ria.'/admin/images/sync/'.( $prd['is_sync'] ? 1 : 0 ).'.svg" title="'.( $prd['is_sync'] ? 'Ce produit est synchronisé avec votre gestion commerciale' : 'Ce produit n\'existe que dans votre boutique en ligne' ).'" alt="'.( $prd['is_sync'] ? 'Ce produit est synchronisé avec votre gestion commerciale' : 'Ce produit n\'existe que dans votre boutique en ligne' ).'" /> <a href="https://'.$http_host_ria.'/admin/catalog/product.php?cat=0&prd='.$prd['id'].'&lng=fr&tab=stocks">'.htmlspecialchars( $prd['ref'].' '.$prd['name'] ).'</a>';

	$email->addParagraph('Voir le produit : '.$link_to_product);

	$email->addHtml( '</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(0, 47, 59);"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb( 255, 255, 255 );" href="mailto:<EMAIL>"><EMAIL></a></div></div>' );

	$sent_successfull = $email->send();

	if ($sent_successfull) {
		prd_stocks_update_date_last_notified($prd_id, $dps, $now);
	}
	return $sent_successfull;
}
// \endcond

// \cond onlyria
/**
 * Cette fonction permet de mettre à jour la date de dernière notification d'une ligne de stock
 *
 * @param integer $prd_id Identifiant du produit
 * @param integer $dps_id Identifiant du dépôt
 * @param DateTime $date Date de notification
 * @return bool Si la requête sql c'est bien exécuté
 */
function prd_stocks_update_date_last_notified($prd_id, $dps_id, DateTime $date){
	global $config;

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	if( !is_numeric($dps_id) || $dps_id<=0 ){
		return false;
	}

	$sql = '
		update prd_stocks
		set sto_date_last_notified = "'.$date->format('Y-m-d H:i:s').'"
		where sto_tnt_id = '.$config['tnt_id'].'
			and sto_prd_id='.$prd_id.'
			and sto_dps_id='.$dps_id.'
			and sto_is_deleted != 1;
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la quantité en commande fournisseur pour un article et un dépôt donné. Si la ligne n'existe pas, elle sera crée.
 *	@param int $prd Obligatoire, Identifiant du produit
 *	@param int $dps Obligatoire, Identifiant du dépôt
 *	@param int $cmd Obligatoire, Quantité en commande fournisseur
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_stocks_set_commanded( $prd, $dps, $cmd ){

	if( !is_numeric($prd) ) return false;
	if( !is_numeric($dps) ) return false;
	if( !is_numeric($cmd) ) return false;

	global $config;

	if( !prd_dps_stocks_exists( $prd, $dps ) ){
		return prd_dps_stocks_add( $prd, $dps, 0, 0, $cmd, 0, 0, 0 );
	}

	$sql = 'update prd_stocks set sto_com='.$cmd.' where sto_prd_id='.$prd.' and sto_dps_id='.$dps.' and sto_tnt_id='.$config['tnt_id'];

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	// recalcul des stocks des nomenclatures qui dépendent de ce produit
	if( !prd_products_is_nomenclature($prd) ){
		prd_nomenclatures_products_rebuild_stock( 0, $prd );
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction ajoute un mouvement à l'échéancier
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param $date Obligatoire, date et heure du mouvement
 *	@param $qte Obligatoire, quantité mouvementée
 *	@param bool $confirmed Optionnel, mouvement confirmé oui/non
 *
 *	@return bool False en cas d'échec
 *	@return int L'identifiant du mouvement généré en cas de succès
 */
function prd_stocks_schedule_add( $prd, $date, $qte, $confirmed=true ){
	if( !prd_products_exists( $prd ) ) return false;
	if( !isdateheure($date) ) return false;
	if( !is_numeric($qte) || $qte==0 ) return false;

	$date = dateheureparse($date);

	global $config;

	$r = ria_mysql_query('insert into prd_stocks_schedule (pss_tnt_id, pss_prd_id, pss_date, pss_qte, pss_confirmed) values ('.$config['tnt_id'].', '.$prd.', "'.$date.'", '.$qte.', '.( $confirmed ? '1' : '0' ).')');

	if( !$r ) return false;

	$id = ria_mysql_insert_id();
	prd_products_set_date_modified( $prd );

	return $id;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la quantité d'un mouvement de l'échéancier
 *	@param int $id Obligatoire, identifiant du mouvement
 *	@param $qte Obligatoire, nouvelle quantité mouvementée
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_stocks_schedule_set_qte( $id, $qte ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_numeric($qte) || $qte==0 ) return false;

	global $config;

	$res = ria_mysql_query('update prd_stocks_schedule set pss_qte='.$qte.' where pss_tnt_id='.$config['tnt_id'].' and pss_id='.$id);
	if( !$res ){
		return false;
	}

	$rp = ria_mysql_query( 'select pss_prd_id from prd_stocks_schedule where pss_tnt_id='.$config['tnt_id'].' and pss_id='.$id );
	if( $rp && ria_mysql_num_rows($rp) ){
		$p = ria_mysql_result( $rp, 0, 'pss_prd_id' );
		prd_products_set_date_modified( $p );
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la date d'un mouvement de l'échéancier
 *	@param int $id Obligatoire, identifiant du mouvement
 *	@param $date Obligatoire, nouvelle date du mouvement
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_stocks_schedule_set_date( $id, $date ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !isdateheure($date) ) return false;

	$date = dateheureparse($date);

	global $config;

	$res = ria_mysql_query('update prd_stocks_schedule set pss_date="'.$date.'" where pss_tnt_id='.$config['tnt_id'].' and pss_id='.$id);

	$rp = ria_mysql_query( 'select pss_prd_id from prd_stocks_schedule where pss_tnt_id='.$config['tnt_id'].' and pss_id='.$id );
	if( $rp && ria_mysql_num_rows($rp) ){
		$p = ria_mysql_result( $rp, 0, 'pss_prd_id' );
		prd_products_set_date_modified( $p );
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la confirmation d'un mouvement de l'échéancier
 *	@param int $id Obligatoire, identifiant du mouvement
 *	@param bool $reverse Optionnel, si True, il s'agit d'une déconfirmation
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_stocks_schedule_set_confirmed( $id, $reverse=false ){
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	$res = ria_mysql_query('update prd_stocks_schedule set pss_confirmed='.( $reverse ? '0' : '1' ).' where pss_tnt_id='.$config['tnt_id'].' and pss_id='.$id);

	$rp = ria_mysql_query( 'select pss_prd_id from prd_stocks_schedule where pss_tnt_id='.$config['tnt_id'].' and pss_id='.$id );
	if( $rp && ria_mysql_num_rows($rp) ){
		$p = ria_mysql_result( $rp, 0, 'pss_prd_id' );
		prd_products_set_date_modified( $p );
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction supprime un (ou des) mouvement(s) de l'échéancier
 *	Les paramètres $id et le couple  $prd / $date sont mutuellement obligatoires
 *	Si $id est spécifié, les autres arguments sont ignorés
 *	@param int $id Optionnel, identifiant du mouvement
 *	@param int $prd Optionnel, identifiant du produit
 *	@param $date Optionnel, date et heure du mouvement
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_stocks_schedule_del( $id=0, $prd=0, $date=null ){
	if( !is_numeric($id) || $id<0 ) $id = 0;
	if( !is_numeric($prd) || $prd<0 ) $prd = 0;
	if( $date!==null && !isdateheure($date) ) $date = null;

	if( !$id ){
		if( !$prd || $date===null ) return false;
	}

	if( $date!==null ) $date = dateheureparse($date);

	global $config;

	$sql = 'delete from prd_stocks_schedule where pss_tnt_id='.$config['tnt_id'];

	if( $id )
		$sql .= ' and pss_id='.$id;
	else
		$sql .= ' and pss_prd_id='.$prd.' and pss_date="'.$date.'"';

	if( $prd ){
		prd_products_set_date_modified( $prd );
	}

	return ria_mysql_query($sql);
}
// \endcond

/**	Cette fonction récupère un sous groupe des mouvements de l'échéancier en fonction de paramètres optionnels
 *	@param int $id Optionnel, identifiant d'un mouvement (ou tableau d'identifiants)
 *	@param int $prd Optionnel, identifiant d'un porduit (ou tableau d'identifiants)
 *	@param string $date_start Optionnel, date et heure de début de période
 *	@param string $date_end Optionnel, date et heure de fin de période
 *	@param string $ref Optionnel, référence d'un produit
 *	@param string $barcode Optionnel, code barre du produit
 *	@param bool $confirmed Optionnel, mouvement confirmé ou non (null = tous)
 *	@param $min_qty Optionnel, quantité minimal mouvementée
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du mouvement
 *		- prd_id : identifiant du produit
 *		- prd_ref : référence du produit
 *		- prd_barcode : codebarre du produit
 *		- date : date du mouvement
 *		- qte : quantité mouvementée
 *		- confirmed : détermine si le mouvement est confirmé
 */
function prd_stocks_schedule_get( $id=0, $prd=0, $date_start=null, $date_end=null, $ref=null, $barcode=null, $confirmed=true, $min_qty=false ){
	if( is_array($id) ){
		foreach( $id as $single_id ){
			if( !is_numeric($single_id) || $single_id<=0 ) return false;
		}
	}else{
		if( is_numeric($id) && $id>0 )
			$id = array($id);
		else
			$id = array();
	}
	if( is_array($prd) ){
		foreach( $prd as $single_prd ){
			if( !is_numeric($single_prd) || $single_prd<=0 ) return false;
		}
	}else{
		if( is_numeric($prd) && $prd>0 )
			$prd = array($prd);
		else
			$prd = array();
	}
	if( $date_start!==null && !isdateheure($date_start) ) return false;
	if( $date_end!==null && !isdateheure($date_end) ) return false;

	if( $date_start!==null ) $date_start = dateheureparse($date_start);
	if( $date_end!==null ) $date_end = dateheureparse($date_end);

	if( $date_start!==null && $date_end!==null ){
		if( strtotime($date_end)<strtotime($date_start) )
			return false;
	}

	if( $min_qty !== false && !is_numeric($min_qty) ){
		return false;
	}

	global $config;

	$sql = '
		select pss_id as id, prd_id, prd_ref, prd_barcode, pss_date as "date", pss_qte as qte, pss_confirmed as confirmed
		from prd_stocks_schedule
		join prd_products on pss_tnt_id=prd_tnt_id and pss_prd_id=prd_id
		where pss_tnt_id='.$config['tnt_id'].'
	';
	if( sizeof($id) )
		$sql .= ' and pss_id in ('.implode(', ', $id).')';
	if( sizeof($prd) )
		$sql .= ' and prd_id in ('.implode(', ', $prd).')';
	if( $ref!==null )
		$sql .= ' and upper(prd_ref)="'.addslashes(strtoupper(trim($ref))).'"';
	if( $barcode!==null )
		$sql .= ' and upper(prd_barcode)="'.addslashes(strtoupper(trim($barcode))).'"';
	if( $date_start!==null )
		$sql .= ' and pss_date>="'.$date_start.'"';
	if( $date_end!==null )
		$sql .= ' and pss_date<="'.$date_end.'"';
	if( $confirmed===true )
		$sql .= ' and pss_confirmed=1';
	elseif( $confirmed===false )
		$sql .= ' and pss_confirmed=0';
	if( $min_qty !== false ){
		$sql .= ' and pss_qte >= '.$min_qty;
	}

	$sql .= ' order by pss_date asc';

	return ria_mysql_query($sql);
}

// \cond onlyria
/**	Cette fonction détermine l'existence d'un mouvement en fonction de paramètres optionnels
 *	Au moins un des paramètres doit être renseigné
 *	Si $id est spécifié, les autres arguments sont ignorés
 *	@param int $id Optionnel, identifiant du mouvement
 *	@param int $prd Optionnel, identifiant du produit
 *	@param $date Optionnel, date et heure du mouvement
 *
 *	@return bool True si un mouvement existe, False sinon
 */
function prd_stocks_schedule_exists( $id=0, $prd=0, $date=null ){
	if( !is_numeric($id) || $id<0 ) $id = 0;
	if( !is_numeric($prd) || $prd<0 ) $prd = 0;
	if( $date!==null && !isdateheure($date) ) $date = null;

	if( !$id && !$prd && $date===null ) return false;

	if( $date!==null ) $date = dateheureparse($date);

	global $config;

	$sql = 'select 1 from prd_stocks_schedule where pss_tnt_id='.$config['tnt_id'];

	if( $id ){
		$sql .= ' and pss_id='.$id;
	}else{
		if( $prd && $date!==null ){
			$sql .= ' and pss_prd_id='.$prd.' and pss_date="'.$date.'"';
		}elseif( $prd ){
			$sql .= ' and pss_prd_id='.$prd;
		}else{
			$sql .= ' and pss_date="'.$date.'"';
		}
	}

	$r = ria_mysql_query($sql);

	return $r && ria_mysql_num_rows($r);
}
// \endcond

// \cond onlyria
/**	Cette fonction calcule le stock réel d'un produit à une date future, en fonction des mouvements à suivre et du stock courant
 *	@param int $prd Identifiant du produit
 *	@param $date Optionnel, date pour laquelle on souhaite connaitre la disponibilité en stock (forcément supérieure au timestamp courant). Avec NULL, on récupère les mouvements les plus lointains
 *	@param bool $confirmed Optionnel, détermine si seul les mouvements confirmés sont pris en compte
 *
 *	@return bool False en cas d'échec
 *	@return int La quantité en stock disponible à la date donnée
 */
function prd_stocks_schedule_get_stock_at_date( $prd, $date=null, $confirmed=true ){
	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !isdateheure($date) ) return false;

	$date = dateheureparse($date);

	if( strtotime($date)<=time() ) return false;

	// récupère l'état actuel du produit
	$rprd_data = prd_products_get_simple( $prd );
	if( !$rprd_data || !ria_mysql_num_rows($rprd_data) ) return false;
	$prd_date = ria_mysql_fetch_array($rprd_data);

	$rschedule = prd_stocks_schedule_get( 0, $prd, date('Y-m-d H:i:s'), $date, null, null, $confirmed );
	if( !$rschedule ) return false;

	$stock = $prd_date['stock'];
	while( $mouv = ria_mysql_fetch_array($rschedule) ){
		$stock += $mouv['qte'];
	}

	return $stock;
}
// \endcond

/** Cette fonction permet de récupérer les produits réservés d'un panier sauvegardé
 * 	@param int $usr_id Facultatif, identifiant de l'utilisateur
 * 	@param int $dps_id Facultatif, identifiant du dépot
 * 	@param int $prd_id Facultatif, identifiant du produit
 * 	@param int $ord_id Facultatif, identifiant de la commande
 *
 *	@return bool False si l'un des paramètres est faux
 * 	@return resource Retourne un résultat mysql avec les colonnes suivantes :
 * 			- tnt_id : identifiant du tenant
 * 			- usr_id : identifiant de l'utilisateur
 * 			- dps_id : identifiant du dépots
 * 			- prd_id : identifiant du produit
 * 			- ord_id : identifiant de la commande
 * 			- date_expires : date de fin de la réservation
 * 			- date_deleted : date de suppression de la réservation
 * 			- prd_qte : quantité du produit
 */
function prd_reservations_get( $usr_id=0, $dps_id=0, $prd_id=0, $ord_id=0){
	if( !is_numeric($usr_id) || $usr_id<0 ){
		return false;
	}

	if( !is_numeric($dps_id) || $dps_id<0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<0 ){
		return false;
	}

	if( !is_numeric($ord_id) || $ord_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select rsv_tnt_id as tnt_id, rsv_usr_id as usr_id, rsv_dps_id as dps_id, rsv_prd_id as prd_id, rsv_ord_id as ord_id, rsv_date_expires as date_expires, rsv_prd_qte as prd_qte
		from prd_reservations
		where rsv_tnt_id = '.$config['tnt_id'].'
			and rsv_date_expires >= now()
	';

	if( $usr_id ){
		$sql .= ' and rsv_usr_id='.$usr_id;
	}

	if( $prd_id ){
		$sql .= ' and rsv_prd_id='.$prd_id;
	}

	if( $dps_id ){
		$sql .= ' and rsv_dps_id='.$dps_id;
	}

	if( $ord_id ){
		$sql .= ' and rsv_ord_id='.$ord_id;
	}

	$res = ria_mysql_query($sql);
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return $res;
}

// \cond onlyria
/** Cette fonction permet de rajouter pour un dépot et une commande, un produit réserver
 * 	@param int $ord_id Obligatoire, Identifiant de la commande (ou du panier)
 * 	@param int $prd_id Obligatoire, Identifiant du produit à réserver
 *	@param int $line_id Obligatoire, identifiant de la ligne dans le panier
 *	@param $qty Obligatoire, quantité du produit
 * 	@param int $usr_id Obligatoire, Identifiant de l'utilisateur auquel appartient la commande
 * 	@param int $dps_id Obligatoire, Identifiant du dépot de stockage dans lequel la réservation doit avoir lieu
 *	@param string $date_expires Obligatoire, date à laquelle la réservation doit expirer
 *
 * 	@return bool True si l'ajout c'est bien effectué, False dans le cas contraire
 */
function prd_reservations_add( $ord_id, $prd_id, $line_id, $qty, $usr_id, $dps_id, $date_expires ){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	if( !is_numeric($line_id) || $line_id < 0 ){
		return false;
	}

	if( !is_numeric($qty) || $qty <= 0 ){
		return false;
	}

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	if( !is_numeric($dps_id) || $dps_id<=0 ){
		return false;
	}

	$date_expires = dateheureparse( $date_expires );
	if( !isdateheure($date_expires) ){
		return false;
	}

	global $config;

	$data = array(
		'rsv_tnt_id' 		=> $config['tnt_id'],
		'rsv_ord_id' 		=> $ord_id,
		'rsv_prd_id' 		=> $prd_id,
		'rsv_line_id' 		=> $line_id,
		'rsv_date_expires' 	=> '"'.addslashes( $date_expires ).'"',
		'rsv_prd_qte' 		=> $qty,
		'rsv_usr_id' 		=> $usr_id,
		'rsv_dps_id' 		=> $dps_id,
	);

	$sql = '
		insert into prd_reservations
			( '.implode( ', ',array_keys($data) ).' )
		values
			( '.implode( ', ', array_values($data) ).' )
		';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}
	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de modifier une réservation déja existante
 * 	@param int $usr_id Obligatoire, Identifiant de l'utilisateur
 * 	@param int $dps_id Obligatoire, Identifiant du dépot
 * 	@param int $ord_id Obligatoire, Identifiant de la commande ou du panier
 * 	@param string $date_expires Obligatoire, Date d'expiration de la réservation
 * 	@param int $prd_id Facultatif, identifiant du produit
 *	@param $prd_qte Facultatif, quantité du produit
 *
 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_reservations_upd( $usr_id, $dps_id, $ord_id, $date_expires, $prd_id=0, $prd_qte=false ){
	if( !is_numeric($usr_id) || $usr_id<=0 ){
		return false;
	}

	if( !is_numeric($dps_id) || $dps_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<0 ){
		return false;
	}

	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}

	if( !isdateheure($date_expires) ){
		return false;
	}

	if( $prd_qte !== false && !is_numeric($prd_qte) && $prd_qte<=0 && $prd_id == 0){
		return false;
	}

	global $config;

	$data = array(
		'rsv_usr_id='.$usr_id,
		'rsv_dps_id='.$dps_id,
		'rsv_ord_id='.$ord_id,
		'rsv_date_expires= "'.dateheureparse($date_expires).'"'
	);

	if( $prd_id ){
		$data[] = 'rsv_prd_id='.$prd_id;
	}

	if( $prd_qte !== false ){
		$data[] = 'rsv_prd_qte='.$prd_qte;
	}

	$sql = '
		update prd_reservations
		set '.implode( ', ', $data ).'
		where rsv_tnt_id = '.$config['tnt_id'].'
			and rsv_usr_id='.$usr_id.'
			and rsv_dps_id='.$dps_id.'
			and rsv_ord_id='.$ord_id.'
	';

	if( $prd_id ){
		$sql .= 'and rsv_prd_id='.$prd_id;
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer virtuellement des reservations pour une commande.
 * 	@param int $ord_id Identifiant de la commande ou du panier
 * 	@param int $prd_id Facultatif, identifiant d'un produit
 * 	@param int $dps_id Facultatif, identifiant du dépot
 *
 * 	@return bool True si la suppression s'est bien déroulée, False dans le cas contraire
 */
function prd_reservations_delete( $ord_id, $prd_id=0, $dps_id=0 ){
	if( !is_numeric($ord_id) || $ord_id<=0 ){
		return false;
	}

	if( !is_numeric($prd_id) || $prd_id<0 ){
		return false;
	}

	if( !is_numeric($dps_id) || $dps_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		delete from prd_reservations
		where rsv_tnt_id = '.$config['tnt_id'].'
			and rsv_ord_id = '.$ord_id.'
	';

	if($prd_id){
		$sql .= ' and rsv_prd_id = '.$prd_id;
	}

	if($dps_id){
		$sql .= ' and rsv_dps_id = '.$dps_id;
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de générer une sous requête mysql pour récupérer la quantité total réeservé d'un produit pour tous les paniers sauvegardés
 * 	@param int $prd_id Selecteur de l'identifiant du porduit dans la requete mysql
 * 	@param int $dps_id Identifiant du dépot
 * 	@param $is_minus Détermine si la requête doit faire parti d'un calcul ou non
 * 	@return string retourne le sql pour récupérer la quantité de produit sauvegarder.
 */
function prd_reservations_get_sql_where_stock( $prd_id, $dps_id, $is_minus=false){
	global $config;

	if( !isset($config['res_stock_is_active']) || !$config['res_stock_is_active'] ){
		return '';
	}

	$res_time = prd_deposits_get_res_time( $dps_id );
	if( !is_numeric($res_time) || $res_time <= 0 ){
		return '';
	}

	$sql = '';

	if($is_minus){
		$sql .= '-';
	}

	$ord_id = isset($_SESSION['ord_id']) ? $_SESSION['ord_id'] : 0;

	$sql .= 'IFNULL(
		(select sum(rsv_prd_qte)
		from prd_reservations
		join ord_orders on ord_id=rsv_ord_id and ord_tnt_id=rsv_tnt_id
		where rsv_tnt_id = '.$config['tnt_id'].'
			and rsv_prd_id='.$prd_id.'
			and rsv_dps_id='.$dps_id.'
			and rsv_ord_id != '.$ord_id.'
			and rsv_date_expires >= now()
			and ord_state_id in ('._STATE_BASKET.', '._STATE_BASKET_SAVE.')),0
	)';

	return $sql;
}
// \endcond

/** Récupère toutes les réservations d'un produit, permet de déterminer si le stock d'un produit est déjà entièrement réservé
 *	@param int $prd_id Identifiant du produit
 * 	@param int $dps_id Identifiant du dépot
 *	@param int $ord_id Optionnel, identifiant de la commande ou tableau d'identifiants
 */
function prd_reservations_get_all_qte($prd_id, $dps_id, $ord_id = false){
	global $config;
	$sql = 'select sum(rsv_prd_qte)
		from prd_reservations
		where rsv_tnt_id = '.$config['tnt_id'].'
			and rsv_prd_id='.$prd_id.'
			and rsv_dps_id='.$dps_id.'
			and rsv_date_expires >= now() ';
	if( (is_numeric($ord_id) && $ord_id > 0)) {
		$sql .= "and rsv_ord_id = ".$ord_id;
	}
	elseif( is_array($ord_id) ){
		$sql .= "and rsv_ord_id IN ";
		$in = "(";
		foreach($ord_id as $id){
			$in .= "".$id.", ";
		}
		$in = substr($in, 0, -2).") ";
		$sql .= $in;
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de mettre à jour le stock réservé pour une commande.
 *	@param int $ord_id Obligatoire, identifiant d'une commande
 *	@return bool True en cas de succès, False dans le cas contraire
 */
function prd_reservations_order_apply( $ord_id ){
	global $config;

	// Contrôle que le module de réservation de stock est bien activé
	if( !isset($config['res_stock_is_active']) || !$config['res_stock_is_active'] ){
		return true;
	}

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	// Suppression des réservations actuelles
	if( !prd_reservations_delete($ord_id) ){
		return false;
	}

	$r_prd = ria_mysql_query('
		select prd_id, prd_line_id as line_id, prd_ord_id as ord_id, prd_qte as qty, prd_dps_id as dps_id
		from ord_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_ord_id = '.$ord_id.'
			and prd_ref not in ("'.implode('", "', $config['dlv_prd_references']).'")
	');

	if( !$r_prd ){
		return false;
	}elseif( !ria_mysql_num_rows($r_prd) ){
		return true;
	}

	$r_user = ria_mysql_query( 'select ord_usr_id from ord_orders where ord_tnt_id = '.$config['tnt_id'].' and ord_id = '.$ord_id );
	if( !$r_user || !ria_mysql_num_rows($r_user) ){
		return false;
	}

	$user = ria_mysql_fetch_assoc( $r_user );

	$user_id = $user['ord_usr_id'];
	if( !is_numeric($user_id) || $user_id <= 0 ){
		return true;
	}

	// Dépôt principal (pour ne pas le recalculer à chaque fois)
	if( $config['tnt_id'] !=2 ){
		$dps_id_main = prd_deposits_get_main();
	}

	while( $prd = ria_mysql_fetch_assoc($r_prd) ){
		$dps_id = 0;

		// Détermine le dépôt sur chaque ligne produit (peut-être différent)
		if( is_numeric($prd['dps_id']) && $prd['dps_id'] > 0 ){
			$dps_id = $prd['dps_id'];
		}else{
			if( $config['tnt_id']==2 ){
				$dps_id = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
			}else{
				$dps_id = $dps_id_main;
			}
		}

		// Génère la date d'expiration de la réservation
		$date_expires = prd_dps_set_expires_date( $dps_id );
		if( !isdateheure($date_expires) ){
			continue;
		}

		if( !prd_reservations_add($prd['ord_id'], $prd['prd_id'], $prd['line_id'], $prd['qty'], $user_id, $dps_id, $date_expires) ){
			return false;
		}
	}

	return true;
}

// \cond onlyria
/** Cette fonction permet de calculer la date de fin de réservation en fonction de la date du jour et du délai de réservation renseigné pour le dépôt.
 *	@param int $dps_id Obligatoire, identifiant du dépôt
 *
 *	@return bool False si le paramètre obligatoire est omis ou faux
 *	@return -1 si aucun délai de réservation n'a été renseigné
 *	@return La date d'expiration en fonction du délai de réservation renseigné
 */
function prd_dps_set_expires_date( $dps_id ){
	if( !is_numeric($dps_id) || $dps_id <= 0 ){
		return false;
	}

	$res_time = prd_deposits_get_res_time( $dps_id );
	if( !is_numeric($res_time) || $res_time <= 0 ){
		return -1;
	}

	return date( 'Y-m-d H:i:s', strtotime('+'.$res_time.' hours') );
}
// \endcond

/// @}

// \cond onlyria
/** \defgroup prd_infos_stock Informations sur l'historique des stocks
 *	\ingroup pim_products
 *	Les fonctions de ce module permettent de gérer l'historique des stocks des différents produits.
 *	@{
 */

/**	Cette fonction permet d'insérer une ligne d'informations concernant l'historique de stock produit
 *	@param int $prd_id Obligatoire, identifiant du produit concerné par cette ligne
 *	@param string $label Obligatoire, informations concernant le stock
 *	@param $date Optionnel, date de l'information
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_stocks_infos_add($prd_id, $label, $date=false){
	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}

	if (trim($label) == '') {
		return false;
	}

	if ($date && !isdateheure($date)) {
		return false;
	}

	global $config;

	$date = $date ? '"'.addslashes( $date ).'"' : 'now()';

	$sql ='
		insert into prd_stocks_infos
			( psi_tnt_id, psi_wst_id, psi_datetime, psi_prd_id, psi_label )
		values
			('.$config["tnt_id"].', '.$config["wst_id"].', '.$date .', '.$prd_id.', "'.addslashes($label).'")
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction vérifie si une ligne d'informations concernant le produit existe sinon l'ajoute
 *	@param int $prd_id Obligatoire, identifiant du produit concerné par cette ligne
 *	@param $label Obligatoire, informations concernant le stock
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_stocks_infos_add_if_not_exist($prd_id, $label){
	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}

	if (trim($label) == '') {
		return false;
	}

	global $config;

	$sql = '
		select psi_label as label
		from prd_stocks_infos
		where psi_tnt_id = '.$config["tnt_id"].'
			and psi_wst_id = '.$config["wst_id"].'
			and psi_prd_id = '.$prd_id.'
		order by psi_datetime desc
		limit 1
	';

	$res = ria_mysql_query($sql);

	if ($res && ria_mysql_num_rows($res)) {
		$data = ria_mysql_fetch_assoc($res);

		if ($data['label'] == $label) {
			return true;
		}
	}

	return prd_stocks_infos_add($prd_id, $label);
}

/**	Cette fonction retourne l'ensemble des informations historiques disponibles sur le stock d'un produit
 *	@param int $prd_id Obligatoire, identifiant du produit concerné par cette ligne
 *	@param int $wst_id Optionel, dans le cas ou l'on souhaite limiter la récupération sur un website
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de la ligne
 *		- date : date/heure de l'événement
 *		- prd_id : identifiant du produit concerné
 *		- label : descriptif du mouvement
 *	@return bool false dans le cas d'une erreur
 */
function prd_stocks_infos_get( $prd_id, $wst_id=false ){
	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}

	if ($wst_id !== false) {
		if (!is_numeric($wst_id) || $wst_id <= 0) {
			return false;
		}
	}

	global $config;

	$sql = '
		select psi_id as id, psi_datetime as date, psi_prd_id as prd_id, psi_label as label
		from prd_stocks_infos
		where psi_tnt_id = '.$config["tnt_id"].'
			and psi_prd_id = '.$prd_id;

	if ($wst_id !== false) {
		$sql .='and psi_wst_id = '.$config["wst_id"];
	}

	$sql .='
		order by date desc
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction perment de supprimer une ligne d'information concernant les informations sur les stocks produit
 *	@param int $id Obligatoire, identifiant de la ligne à supprimer
 *	@param int $prd_id Obligatoire, identifiant du produit concerné par cette ligne
 *	 @return bool true en cas de succès, false en cas d'échec
 */
function prd_stocks_infos_delete( $id, $prd_id ){
	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		delete from prd_stocks_infos
		where psi_tnt_id = '.$config["tnt_id"].'
			and psi_wst_id = '.$config["wst_id"].'
			and psi_id = '.$id.'
			and psi_prd_id = '.$prd_id;

	return ria_mysql_query($sql);
}

/** Cette fonction permet de déterminer si l'on utilise le stock réservé web ou non.
 *	Elle retourne la colonne de stock réservé à utiliser (soit web soit non web en fonction de la configuration)
 * @return string soit sto_res_web, soit sto_res
 */
function prd_stocks_sto_res(){
	global $config;
	if (isset($config['use_stock_res_web']) && $config['use_stock_res_web']) {
	    return 'sto_res_web';
	} else {
	    return 'sto_res';
	}
}
/** Cette fonction permet d'ajouté ou de soustraire un nombre définit de produit sur tous les produits.
 *  Utilise les variables de config :
 *  - stock_change_signe : Définit si on ajoute ou soustrait
 *  - stock_change_value : Définit le nombre de produit à ajouter ou soustraire du stock.
 * @param string $alias Facultatif, alias de la colone dans le mysql
 * @return string Retourne une chaine de caractère mysql pour ajouter ou non des un nombre de produits
 */
function prd_stocks_get_sql($alias = ""){
	global $config;
	$config['stock_change_signe'] = isset($config['stock_change_signe']) ? $config['stock_change_signe'] : '+';
	$config['stock_change_value'] = isset($config['stock_change_value']) ? $config['stock_change_value'] : 0;

	$sql = "(ifnull(" . ( ($alias != "") ? $alias . "." : "" ) . "sto_qte, 0)" . $config['stock_change_signe'] . $config['stock_change_value'] . ")";

	return $sql;
}

/// @}
// \endcond