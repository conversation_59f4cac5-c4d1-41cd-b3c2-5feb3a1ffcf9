<?php
// \cond

require_once('delivery.inc.php');

/** \defgroup model_periods Periodes
 *	Ce module comprend les fonctions nécessaires à la gestion des périodes
 *
 *	@{
 */

/*
 *	Cette fonction permet de récupérer toutes les périodes
 *	@param $pob_id, Identifiant de l'objet
 *	@param $day_id Optionnel, Identifiant d'un jour de la semaine (de 1 à 7)
 *	@param $with_minute Optionnel, Permet de retourner start et end avec les minutes
 *	@return array Retourne un tableau MySQL contenant :
 *			- pob_id : identifiant d'un objet de période
 *			- day_id : identifiant du jour (de 1 à 7)
 *			- start : l'heure de début
 *			- end : l'heure de fin
 *			- int-start : l'heure de debut en int
 * 			- int-end : l'heure de fin en int
 *	@return bool Retourne false si l'un des paramètres est faux
 */
function per_periods_get($pob_id, $day_id = 0, $with_minute=false){
	if( !is_numeric($pob_id) || $pob_id<=0 ) return false;
	global $config;
	if($with_minute){
		$sql = '
			select per_day_id as day_id, per_pob_id as pob_id, time_format(per_hour_start, "%H:%i") as start, time_format(per_hour_stop, "%H:%i") as end
			from per_periods
			where per_tnt_id = '.$config['tnt_id'].'
		';
	}else{
		$sql = '
			select per_day_id as day_id, per_pob_id as pob_id, time_format(per_hour_start, "%H:00") as start, time_format(per_hour_stop, "%H:00") as end,
			time_format(per_hour_start,"%H") as "int-start", time_format(per_hour_stop,"%H") as "int-end"
			from per_periods
			where per_tnt_id = '.$config['tnt_id'].'
		';
	}


	$sql .= ' and per_pob_id = '.$pob_id.' ';

	if($day_id != 0){
		$sql .= ' and per_day_id = '.$day_id.' ';
	}

	$res = ria_mysql_query($sql);

	return $res;
}

/*	Cette fonction permet de mettre a jour une période
 *	@param $pob_id Obligatoire, Identifiant de l'objet
 *	@param $day_id Obligatoire, Identifiant d'un jour de la semaine ( de 1 à 7)
 *	@param $hour_start Obligatoire, heure du debut de la période
 *	@param $hour_stop Obligatoire, heure du fin de la période
 *	@param $with_minute, True si $hour_start et $hour_stop sont de la forme hh:mm
 *	@return bool Retourne true en cas de succès, false dans le cas contraire
 */
function per_periods_update($pob_id, $day_id, $hour_start=false, $hour_stop=false, $with_minute=false){
	if( !is_numeric($pob_id) || $pob_id<=0 ) return false;
	if( !is_numeric($day_id) || !in_array($day_id , array(1, 2, 3, 4, 5, 6, 7)) ) return false;
	if($with_minute == false && (!is_numeric($hour_start) || !is_numeric($hour_stop))) return false;

	global $config;

	if($with_minute){
		$sql = '
			update per_periods set per_hour_start = \''.$hour_start.'\', per_hour_stop = \''.$hour_stop.'\'
			where per_pob_id = '.$pob_id.'
			and per_day_id = '.$day_id.'
			and per_tnt_id = '.$config['tnt_id'].'
		';
	}else{
		$sql = '
			update per_periods set per_hour_start = \''.$hour_start.':00\', per_hour_stop = \''.$hour_stop.':00\'
			where per_pob_id = '.$pob_id.'
			and per_day_id = '.$day_id.'
			and per_tnt_id = '.$config['tnt_id'].'
		';
	}


	if(!ria_mysql_query($sql)){
		return false;
	}
	return true;
}

/* Cette fonction permet d'ajouter une période d'horaires d'expédition
 *	@param $pob_id Obligatoire, identifiant de l'objet de la table per_objects
 *	@param $day_id Obligatoire, identifiant du jour de 1 à 7
 *	@param $hour_start Obligatoire, heure du début de la période
 *	@param $hour_stop Obligatoire, heure du fin de la période
 *	@param $with_minute, True si $hour_start et $hour_stop sont de la forme hh:mm
 *	@return Retourne l'identifiant de la période si l'ajout s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function per_periods_add( $pob_id, $day_id, $hour_start, $hour_stop, $with_minute=false ){
	if( !per_objects_exists($pob_id) ) return false;
	if( !$with_minute && (!is_numeric($hour_stop) || !is_numeric($hour_start) || $hour_stop<$hour_start) ) return false;
	if($with_minute && ($hour_start == '' || $hour_stop == '')) return false;
	global $config;

	if($with_minute){
		$sql = 'insert into per_periods
			( per_tnt_id, per_pob_id, per_day_id, per_hour_start, per_hour_stop )
		values
			( '.$config['tnt_id'].', '.$pob_id.', '.dlv_day_get_id($day_id).', \''.$hour_start.'\', \''.$hour_stop.'\' )
		';
	}else{
		$sql = 'insert into per_periods
			( per_tnt_id, per_pob_id, per_day_id, per_hour_start, per_hour_stop )
		values
			( '.$config['tnt_id'].', '.$pob_id.', '.dlv_day_get_id($day_id).', \''.$hour_start.':00\', \''.$hour_stop.':00\' )
		';
	}


	$res = ria_mysql_query($sql);
	per_objects_set_date_modified($pob_id);
	if( !$res ){
		return false;
	}

	return $res;
}

/**
 * Cette fonction permet de supprimer un période
 * @param $pob_id Obligatoire, identifiant de l'objet à supprimer
 *	@param day_id Obligatoire, identifiant du jour de 1 à 7
 * @return bool true en cas de succès ou false en cas d'échec
 */
function per_periods_del( $pob_id , $day_id = 0){
	if(!per_objects_exists($pob_id)) return false;
	global $config;

	$sql = ' delete from per_periods where 1';
	$sql .= ' and per_pob_id ='.$pob_id.' ';
	$sql .= ' and per_tnt_id = '.$config['tnt_id'];

	if(in_array($day_id, array(1, 2, 3, 4, 5, 6, 7) )){
		$sql.= ' and per_day_id =' .$day_id;
	}

	per_objects_set_date_modified($pob_id);

	return ria_mysql_query($sql);
}

/*. Cette fonction permet de récupérer les jours fériés selon les paramètres fournis
 *	@param $year Optionnel, permet de spécifier une année
 *	@param $exp Option permet de spécifier si les expéditions ont lieu par défaut toutes les dates sont retournées (null)
 *	@param $pob_id Facultatif, filtre sur l'identifiant d'un objet
 *	@return array Retourne un tableau MySQL contenant les résultats :
 *				- date  : date du jour fériés au format YYYY-MM-DD
 *				- exp : 0 si les expédition n'ont pas lieu, 1 dans le cas contraire
 */
function per_holidays_get( $year=0, $exp=null, $pob_id=0 ){
	if( !is_numeric($pob_id) || $pob_id<=0 ) return false;
	if ($pob_id>0 && !per_objects_exists($pob_id)) return false;
	global $config;

	$sql = '
		select hld_pob_id as pob_id, hld_date as date, hld_expedition as exp
		from per_holidays
		where hld_tnt_id='.$config['tnt_id'].'
	';
	if( $year>0 ){
		$sql .= ' and year(hld_date) = \''.$year.'\'';
	}

	if( $pob_id>0 ){
		$sql .= ' and hld_pob_id = '.$pob_id.' ';
	}

	if( $exp ){
		$sql .= ' and hld_expedition';
	}elseif( $exp!==null ){
		$sql .= ' and not hld_expedition';
	}

	return ria_mysql_query( $sql );
}


/* Cette fonction permet d'ajouter un jour férié
 *	@param $pob_id Obligatoire, Identifiant de l'objet périod
 *	@param $date Optionnel, date du jour férié au format YYYY-MM-DD
 *	@param $expedition Optionnel, determine si l'expédition aura lieu, par défaut à false
 *	@return bool Retourne true si l'ajout a fonctionné correctement
 *	@return bool Retourne false dans le cas contraire
 */
function per_holidays_update( $pob_id, $date=null, $expedition=false ){
	if( $date != null && !isdate($date) ) return false;
	if (!per_objects_exists($pob_id)) return false;
	global $config;
	$sql = '
		update per_holidays';
	if ($expedition != null) {
		$sql .= ' set hld_expedition = '.( $expedition ? 1 : 0 ).' ';
	}

	$sql .= 'where hld_tnt_id='.$config['tnt_id'].'
		and hld_pob_id = '.$pob_id.'
		and hld_date=\''.$date.'\'
	';

	per_objects_set_date_modified($pob_id);
	return ria_mysql_query( $sql );
}

/* Cette fonction permet d'ajouter un jour férié
 *	@param $pob_id Obligatoire, Identifiant de l'objet périod
 *	@param $date Obligatoire, date du jour férié au format YYYY-MM-DD
 *	@param $expedition Optionnel, determine si l'expédition aura lieu, par défaut à false
 *	@return bool Retourne true si l'ajout a fonctionné correctement
 *	@return bool Retourne false dans le cas contraire
 */
function per_holidays_add( $pob_id, $date, $expedition=false ){
	if( !isdate($date) ) return false;
	if (!per_objects_exists($pob_id)) return false;
	global $config;
	// Si la date est déjà enregistrer dans la base de données, on met à jour le booléen sur l'expédition
	if( per_holidays_exists($date, $pob_id) ){
		return per_holidays_update($pob_id, $date, $expedition);
	}

	$sql = '
		insert into per_holidays
			( hld_tnt_id, hld_pob_id, hld_date, hld_expedition )
		values
			( '.$config['tnt_id'].', '.$pob_id.', \''.$date.'\', '.( $expedition ? 1 : 0 ).' )
	';

	per_objects_set_date_modified($pob_id);

	return ria_mysql_query( $sql );
}


/* Cette fonction permet de tester l'existance d'un jour férié dans la base de données
 *	@param $date Obligatoire, date du jour férié au format YYYY-MM-DD
 *	@param $pob_id Obligatoire, Identifiant de l'objet périod
 *	@return bool Retourne true s'il existe
 *	@return bool Retourne false dans le cas contraire
 */
function per_holidays_exists( $date, $pob_id ){
	if( !isdate($date) ) return false;
	global $config;
	$sql = 'select 1 from per_holidays where hld_tnt_id='.$config['tnt_id'].' and hld_date=\''.$date.'\' and hld_pob_id='.$pob_id;

	return ria_mysql_num_rows( ria_mysql_query($sql) )>0;
}

/** Cette fonction permet la vérification d'un identifiant de période
 *	@param $pob_id Optionnel, identifiant à tester.
 *	@return bool true si l'objet période existe, false dans le cas contraire.
 */
function per_objects_exists( $pob_id ){
	if( !is_numeric($pob_id) || $pob_id<=0 ) return false;
	global $config;

	$r = per_objects_get(0, -1, -1, -1, $pob_id);
	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return true;
}

/** Cette fonction permet la mise à jour de la date de modification d'un objet
 *	@param $pob_id Obligatoire, identifiant de l'objet
 *	@return bool true l'update s'est bien déroulé, false sinon
 */

function per_objects_set_date_modified( $pob_id ){
	if( !is_numeric($pob_id) || $pob_id<=0 ) return false;
	global $config;

	if (!per_objects_exists($pob_id)) return false;

	return ria_mysql_query('update per_objects set pob_date_modified = now() where pob_id ='.$pob_id.' and pob_tnt_id='.$config['tnt_id']);
}


/** Cette fonction permet la suppression d'un objet
 *	@param $pob_id Obligatoire, identifiant de l'objet
 *	@return bool true la suppression s'est bien déroulé, false sinon
 */
function per_objects_del( $pob_id ){
	if( !is_numeric($pob_id) || $pob_id<=0 ) return false;
	global $config;

	if (!per_objects_exists($pob_id)) return false;

	return ria_mysql_query('update per_objects set pob_date_deleted = now() where pob_id ='.$pob_id.' and pob_tnt_id='.$config['tnt_id']);
}


/**
 * Cette fonction permet de récupérer un objet en fonction de sa classse et de son id
 *
 * @param int $cls_id Facultatif, Identifiant de la classe période
 * @param int $obj_id_0 Facultatif, Identifiant de l'object physique
 * @param int $obj_id_1 Facultatif, Identifiant de l'object physique
 * @param int $obj_id_2 Facultatif, Identifiant de l'object physique
 * @param $pob_id Facultatif, identifiant de l'objet période sur lequel filtrer le résultat
 * @param int $type_id Facultatif, Tableau d'identifiant d'un type de période
 *
 * @return Retourne un résultat de type mysql avec les colonnes suivantes:
 *		- id : Identifiant de la classe période
 *		- type : Identifiant du type 'per_types' ( ex: ouverture de magasin, horaires de travail pour le tracking )
 *		- obj_id_0 : Identifiant de l'objet physique
 *		- obj_id_1 : Identifiant de l'objet physique
 *		- obj_id_2 : Identifiant de l'objet physique
 */
function per_objects_get( $cls_id = 0, $obj_id_0=0, $obj_id_1=0, $obj_id_2=0, $pob_id=0, $type_id=array() ){
	if (!is_numeric($cls_id) || $cls_id < 0) return false;
	if (!is_numeric($obj_id_0) ) return false;
	if (!is_numeric($obj_id_1) ) return false;
	if (!is_numeric($obj_id_2) ) return false;

	$pob_id = control_array_integer( $pob_id, false );
	if( $pob_id === false ){
		return false;
	}

	global $config;
	$sql = 'select pob_id as id,
			pob_cls_id as cls_id,
			pob_type_id as type_id,
			pob_obj_id_0 as obj_id_0,
			pob_obj_id_1 as obj_id_1,
			pob_obj_id_2 as obj_id_2,
			pob_date_created as date_created,
			pob_date_modified as date_modified
			from per_objects where 1';

	if( sizeof($pob_id) ){
		$sql .= ' and pob_id in ('.implode(', ', $pob_id).')';
	}

	if ($obj_id_0 >=0 ) {
		$sql .= ' and pob_obj_id_0 = '.$obj_id_0.' ';
	}

	if ($obj_id_1 >=0 ) {
		$sql .= ' and pob_obj_id_1 = '.$obj_id_1.' ';
	}

	if ($obj_id_2 >=0 ) {
		$sql .= ' and pob_obj_id_2 = '.$obj_id_2.' ';
	}

	if ($cls_id >0 ) {
		$sql .= ' and pob_cls_id = '.$cls_id.' ';
	}

	if( is_array($type_id) && !empty($type_id)){
		$sql .= ' and pob_type_id in ('.implode(', ',$type_id).')';
	}

	$sql .= " and pob_date_deleted is null";

	$sql .= ' and pob_tnt_id='.$config['tnt_id'].' ';

	return ria_mysql_query($sql);
}

/**
 * Cette fonction permet d'ajouter un objet période
 * @param int $cls_id Obligatoire, identifiant de classe
 * @param int $type_id Obligatoire, identifiant du type
 * @param int $obj_id_0 Optionnel
 * @param int $obj_id_1 Optionnel
 * @param int $obj_id_2 Optionnel
 * @return bool Retourne false en cas d'échec ou l'identifiant du nouvel objet en cas de succès.
 */
function per_objects_add( $cls_id, $type_id, $obj_id_0=0, $obj_id_1=0, $obj_id_2=0 ){
	if (!is_numeric($cls_id) || $cls_id <= 0) return false;
	if (!is_numeric($type_id) || $type_id <= 0) return false;
	if (!is_numeric($obj_id_0) || $obj_id_0 < 0) return false;
	if (!is_numeric($obj_id_1) || $obj_id_1 < 0) return false;
	if (!is_numeric($obj_id_2) || $obj_id_2 < 0) return false;
	global $config;

	$sql = 'insert into per_objects
		(pob_tnt_id, pob_cls_id, pob_obj_id_0,  pob_obj_id_1,  pob_obj_id_2, pob_type_id)
			values
		('.$config['tnt_id'].', '.$cls_id.', '.$obj_id_0.', '.$obj_id_1.', '.$obj_id_2.', '.$type_id.')';

	$res = ria_mysql_query($sql);
	if (!$res) {
		return false;
	}

	return ria_mysql_insert_id();
}

/**
 *	Cette fonction permet de récupérer un type de périodes ( ex: ouverture de magasin, horaires de travail pour le tracking )
 *	@param int $type_id Obligatoire, Identifiant d'un type sur lequel filtrer le résultat
 *	@return array Retourne un tableau de type mysql avec les colonnes suivantes :
 *			- id : Identifiant du type
 *			- name : Nom du type
 *	@return bool Retourne false en cas d'échec
 */
function per_types_get( $type_id=0 ){

	if (!is_numeric($type_id) || $type_id < 0) return false;
	global $config;
	$sql = 'select type_id as id, type_name as name from per_types where type_tnt_id in ('.$config['tnt_id'].', 0) and type_date_deleted is null ';

	if ($type_id > 0) {
		$sql .= ' and type_id = '.$type_id.'';
	}

	$r = ria_mysql_query($sql);
	return $r;
}

/**
 *	Cette fonction permet l'ajout d'un type de période ( ex: ouverture de magasin, horaires de travail pour le tracking )
 *	@param string $name Obligatoire, désignation du type de période
 *	@return Retourne l'identifiant de la nouvelle ligne ou false en cas d'échec
 */
function per_types_add( $name ){
	if( !trim($name) ) return false;
	global $config;

	$r = ria_mysql_query('insert into per_types (types_tnt_id, type_name) values ('.$config['tnt_id'].', '.trim(ria_mysql_escape_string($name)).' )');
	if (!$r) {
		return false;
	}

	return $r;
}

/**
 *	Cette fonction permet de supprimer un type de période en fonction de son identifiant
 *	@param int $type_id Obligatoire, identifiant du type de période à supprimer
 *	@return bool true en cas de succès sinon false
 */
function per_types_del( $type_id ){
	if (!is_numeric($type_id) || $type_id < 0) return false;
	global $config;

	return ria_mysql_query('update per_types set type_date_deleted=\'now()\' where type_id = '.$type_id.' and type_tnt_id='.$config['tnt_id']);
}

/**
 *  Cette fonction permet de mettre à jour un type de période
 *	@param int $type_id Obligatoire, identifiant du type de période à mettre à jour
 *  @param string $name Obligatoire, nom du type à mettre à jour
 */
function per_types_update( $type_id, $name ){
	global $config;

	if (!is_numeric($type_id) || $type_id < 0) return false;
	if( trim($name)=='' ) return false;

	return ria_mysql_query('update per_types set type_date_modified=\'now()\', type_name='.addslashes($name).' where type_id = '.$type_id.' and type_tnt_id = '.$config['tnt_id']);
}


/* Cette fonction permet d'ajouter un évènement dans la base de données
 *	@param $pob_id Obligatoire, identifiants de l'object
 *	@param string $name Obligatoire, nom affecté à l'évènement
 *	@param $start Obligatoire, date de début
 *	@param $end Obligatoire, date de fin
 *	@return Retourne l'identifiant du nouvel évènement si son ajout s'est bien passé
 *	@return bool Retourne false dans le cas contraire
 */
function per_events_add( $pob_id, $name, $start, $end ){
	if ( !per_objects_exists($pob_id)) return false;
	if( trim($name)=='' ) return false;
	if( !isdateheure($start) || !isdateheure($end) || !cmp_date($end, $start) ) return false;
	global $config;

	$sql = '
		insert into per_events
			( pev_pob_id, pev_tnt_id, pev_name, pev_date_start, pev_date_stop )
		values
			( '.$pob_id.', '.$config['tnt_id'].', \''.addslashes($name).'\', \''.addslashes($start).'\', \''.addslashes($end).'\' )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}
	$inserted_id = ria_mysql_insert_id();
	per_objects_set_date_modified($pob_id);
	return $inserted_id;
}

/* Cette fonction permet de récupérer un ou plusieurs événement enregistrer dans la base de données
 *	@param $pob_id Optionnel, identifiant d'un objet period
 *	@param $pev_id Optionnel, identifiant d'un événement
 *	@param $period Optionnel, type de période
 				- 1 Retourne les évenements qui sont en cours mais pas terminé
 				- 2 Retourne les évenements dont la date de début n'est pas encore passé, soit "A venir"
 *	@return bool Retourne false si l'un des paramètres est faux
 *	@return array Retourne un tableau MySQL contenant tous les résultats :
 *				- id : identifiant de l'évènement
 *				- name : intitulé de l'évènement
 *				- start : date de début (inclus)
 *				- end : date de fin (inclus)
 */
function per_events_get( $pob_id=0, $pev_id=0, $period=0 ){
	if (!is_numeric($pob_id) || $pob_id < 0 || !per_objects_exists($pob_id)) return false;
	global $config;

	$sql = '
		select pev_id as id, pev_pob_id as pob_id, pev_name as name, date_format(pev_date_start, "%d/%m/%Y") as start, date_format(pev_date_stop, "%d/%m/%Y") as end,
		pev_date_start as start_en, pev_date_stop as end_en
		from per_events
		where pev_tnt_id='.$config['tnt_id'].'
	';

	if( $period==1 ){
		$sql .= ' and pev_date_start<=now() and pev_date_stop>=now()';
	}
	elseif( $period==2 ){
		$sql .= ' and pev_date_start>now()';
	}

	if( $pev_id>0 ){
		$sql .= ' and pev_id='.$pev_id;
	}

	if ($pob_id > 0) {
		$sql .= ' and pev_pob_id='.$pob_id;
	}

	$sql .= ' order by pev_date_start, pev_date_stop, pev_name';

	return ria_mysql_query( $sql );
}

/* Cette fonction permet de mettre à jour un évènement
 *	@param $pob_id Obligatoire, identifiant de l'objet
 *	@param $pev_id Obligatoire, identifiants de l'évenement
 *	@param string $name Obligatoire, nom de l'évènement
 *	@param $start Obligatoire, date de début
 *	@param $end Obligatoire, date de fin
 *	@return bool Retourne true si la mise à jour s'est correctement déroulée, false dans le cas contraire
 */
function per_events_update( $pob_id, $pev_id, $name, $start, $end ){
	if (!is_numeric($pob_id) || $pob_id <= 0 || !per_objects_exists($pob_id) ) return false;
	if (!is_numeric($pev_id) || $pev_id <= 0 || !per_events_exists($pev_id) ) return false;
	if( trim($name)=='' ) return false;
	if( !isdateheure($start) || !isdateheure($end) || !cmp_date($end, $start) ) return false;
	global $config;

	$sql = '
		update per_events
		set pev_name=\''.addslashes($name).'\',
			pev_date_start=\''.addslashes($start).'\',
			pev_date_stop=\''.addslashes($end).'\'
		where pev_tnt_id='.$config['tnt_id'].'
			and pev_id='.$pev_id.'
	';

	per_objects_set_date_modified($pob_id);

	return ria_mysql_query($sql);
}


/* Cette fonction permet de supprimer un évènement enregistré dans la base de données
 * Au minimum un dex deux arguments doit être renseigné
 *	@param $pev_id Optionnel, identifiant d'un évènement
 *	@param $pob_id Optionnel, identifiant d'un object
 *	@return bool Retourne true si la suppression s'est correctement déroulée, false dans le cas contraire ou si aucun des
 */
function per_events_del( $pev_id=0, $pob_id=0 ){
	if( !is_numeric($pev_id) || $pev_id < 0 || ($pev_id > 0 && !per_events_exists($pev_id)) ) return false;
	if( !is_numeric($pob_id) || $pob_id < 0 || ($pob_id > 0 && !per_objects_exists($pob_id)) ) return false;
	global $config;

	$sql = 'delete from per_events where pev_tnt_id='.$config['tnt_id'].' ';

	if ($pev_id > 0) {
		$sql .= ' and pev_id ='.$pev_id.' ';
	}else if ($pob_id > 0) {
		$sql .= ' and pev_pob_id ='.$pob_id.' ';
	}else{
		return false;
	}

	return ria_mysql_query($sql);
}

/* Cette fonction permet de vérifier l'existance d'un évènement dans la base de données
 *	@param int $id Obligatoire, identifiant d'un évènement
 *	@return bool Retourne true si l'évènement est trouvé, false dans le cas contraire
 */
function per_events_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	return ria_mysql_num_rows( ria_mysql_query('select 1 from per_events where pev_tnt_id='.$config['tnt_id'].' and pev_id='.$id) )>0;
}



/// @}

// \endcond