<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/pubsub.proto

namespace Google\Cloud\PubSub\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Settings for validating messages published against a schema.
 *
 * Generated from protobuf message <code>google.pubsub.v1.SchemaSettings</code>
 */
class SchemaSettings extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. The name of the schema that messages published should be
     * validated against. Format is `projects/{project}/schemas/{schema}`. The
     * value of this field will be `_deleted-schema_` if the schema has been
     * deleted.
     *
     * Generated from protobuf field <code>string schema = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     */
    private $schema = '';
    /**
     * The encoding of messages validated against `schema`.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Encoding encoding = 2;</code>
     */
    private $encoding = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $schema
     *           Required. The name of the schema that messages published should be
     *           validated against. Format is `projects/{project}/schemas/{schema}`. The
     *           value of this field will be `_deleted-schema_` if the schema has been
     *           deleted.
     *     @type int $encoding
     *           The encoding of messages validated against `schema`.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Pubsub\V1\Pubsub::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. The name of the schema that messages published should be
     * validated against. Format is `projects/{project}/schemas/{schema}`. The
     * value of this field will be `_deleted-schema_` if the schema has been
     * deleted.
     *
     * Generated from protobuf field <code>string schema = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getSchema()
    {
        return $this->schema;
    }

    /**
     * Required. The name of the schema that messages published should be
     * validated against. Format is `projects/{project}/schemas/{schema}`. The
     * value of this field will be `_deleted-schema_` if the schema has been
     * deleted.
     *
     * Generated from protobuf field <code>string schema = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setSchema($var)
    {
        GPBUtil::checkString($var, True);
        $this->schema = $var;

        return $this;
    }

    /**
     * The encoding of messages validated against `schema`.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Encoding encoding = 2;</code>
     * @return int
     */
    public function getEncoding()
    {
        return $this->encoding;
    }

    /**
     * The encoding of messages validated against `schema`.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Encoding encoding = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setEncoding($var)
    {
        GPBUtil::checkEnum($var, \Google\Cloud\PubSub\V1\Encoding::class);
        $this->encoding = $var;

        return $this;
    }

}

