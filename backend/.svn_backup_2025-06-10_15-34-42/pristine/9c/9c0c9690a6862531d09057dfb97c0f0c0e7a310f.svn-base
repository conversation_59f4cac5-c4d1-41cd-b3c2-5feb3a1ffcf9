$(document).ready(function(){
    //On récupère les valeurs des inputs
    $("#client_id").focus();
        
    // Observe les modifications dans le champ du client id
    const old_client_id = $("#client_id").val();
    $("#client_id").keyup(function(){
        //Si le champs ne contient pas la même valeur que celle initiale, on affiche le bouton
        if (old_client_id != $("#client_id").val()){
            if($('#button_regenerate').length == 0) {
                $("#formulaire_identifiants_instagram").append('<input id="button_regenerate" type="submit" value="' + instagramRegenererToken + '" name="regenerate" />');
            }
            if($('#button_generate').length != 0) {
                $('#button_generate').remove();
            }
        }else{
            //Sinon si le bouton était affiché, on l'efface
            if($('#button_regenerate').length != 0) {
                $('#button_regenerate').remove();
            }
        }
    });

    // Observe les modifications dans le champ du client secret
    const old_client_secret = $("#client_secret").val();
    $("#client_secret").keyup(function(){
        //Si le champs ne contient pas la même valeur que celle initiale, on affiche le bouton
        if (old_client_secret != $("#client_secret").val()){
            if($('#button_regenerate').length == 0) {
                $("#formulaire_identifiants_instagram").append('<input id="button_regenerate" type="submit" value="' + instagramRegenererToken + '" name="regenerate" />');
            }
            if($('#button_generate').length != 0) {
                $('#button_generate').remove();
            }
        }else{
            //Sinon si le bouton était affiché, on l'efface
            if($('#button_regenerate').length != 0) {
                $('#button_regenerate').remove();
            }
        }
    });
});