<?php

// \cond onlyria

require_once('orders.inc.php');

/** \defgroup model_orders_signature Gestion de la signature sur un devis ou une commande
 *	\ingroup yuto oms
 *
 *	Ce module comprend les fonctions nécessaires à la gestion de la signature d'un devis ou d'une commande.
 *	la signature est stockée en tant que texte et représente les coordonnées d'un nuage de traits.
 *	Une commande ne peut être signée qu'une seule fois, et la signature ne peut plus être modifiée (les informations sur l'utilisateur peuvent l'être).
 *	Une table séparée a été crée pour alléger la table ord_orders et pour insérer des informations supplémentaires :
 *		- usr_id : ID d'utilisateur de la personne signataire, s'il existe
 *		- firstname : prénom de la personne signataire
 *		- lastname : nom de la personne signataire
 *		- function : fonction / poste de la personne signataire dans la société
 *	Note : partout où il est spécifié "commande" dans les commentaires, le statut "devis" (_STATE_DEVIS) est également approprié
 *
 *	@{
 */

/**	Cette fonction signe une commande
 *	@param int $ord_id Obligatoire, identifiant de la commande
 *	@param $signature Obligatoire, signature au format text
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur signataire
 *	@param string $firstname Optionnel, prénom du signataire
 *	@param string $lastname Optionnel, nom du signataire
 *	@param $function Optionnel, fonction / poste du signataire dans la société
 *
 *	@return bool True en cas de succès, False en cas d'échec. Si la commande est déjà signée, la fonction retournera False
 */
function ord_orders_signature_add( $ord_id, $signature, $usr_id=null, $firstname=null, $lastname=null, $function=null ){
	if( !ord_orders_exists( $ord_id, 0, 0, true ) ){
		return false;
	}
	if( ord_orders_signature_exists( $ord_id ) ){
		return false;
	}

	$signature = trim($signature);
	if( $signature == '' ){
		return false;
	}else{
		$signature = '"'.addslashes($signature).'"';
	}

	if( $usr_id === null ){
		$usr_id = 'NULL';
	}elseif( !gu_users_exists( $usr_id ) ){
		return false;
	}

	$firstname = $firstname === null ? 'NULL' : '"'.addslashes($firstname).'"';
	$lastname = $lastname === null ? 'NULL' : '"'.addslashes($lastname).'"';
	$function = $function === null ? 'NULL' : '"'.addslashes($function).'"';

	global $config;

	$sql = '
		insert into ord_orders_signature
			(sig_tnt_id, sig_ord_id, sig_signature, sig_usr_id, sig_firstname, sig_lastname, sig_function)
		values
			('.$config['tnt_id'].', '.$ord_id.', '.$signature.', '.$usr_id.', '.$firstname.', '.$lastname.', '.$function.')
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		ord_orders_set_date_modified( $ord_id );
	}

	return $res;

}

/**	Cette fonction met à jour les informations sur l'utilisateur ayant signé une commande
 *	Elle ne permet pas de mettre à jour la signature.
 *	Les arguments optionnels ne peuvent être à False tous en même temps (False permettant de ne pas mettre à jour l'information).
 *	@param int $ord_id Obligatoire, identifiant de la commande signée
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur signataire (null autorisé)
 *	@param string $firstname Optionnel, prénom du signataire
 *	@param string $lastname Optionnel, nom du signataire
 *	@param $function Optionnel, fonction / poste du signataire dans la société
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_orders_signature_upd( $ord_id, $usr_id=false, $firstname=false, $lastname=false, $function=false ){
	if( !ord_orders_signature_exists( $ord_id ) ){
		return false;
	}

	if( $usr_id !== false ){
		if( $usr_id !== null && !gu_users_exists( $usr_id ) ){
			return false;
		}
	}

	if( $firstname !== false && $firstname !== null ){
		$firstname = trim($firstname);
	}
	if( $lastname !== false && $lastname !== null ){
		$lastname = trim($lastname);
	}
	if( $function !== false && $function !== null ){
		$function = trim($function);
	}

	if( $usr_id === false && $firstname === false && $lastname === false && $function === false ){
		return false;
	}

	global $config;

	$conditions = array();
	if( $usr_id !== false ){
		$conditions[] = 'sig_usr_id = '.( $usr_id === null ? 'NULL' : $usr_id );
	}
	if( $firstname !== false ){
		$conditions[] = 'sig_firstname = '.( $firstname === null ? 'NULL' : '"'.addslashes($firstname).'"' );
	}
	if( $lastname !== false ){
		$conditions[] = 'sig_lastname = '.( $lastname === null ? 'NULL' : '"'.addslashes($lastname).'"' );
	}
	if( $function !== false ){
		$conditions[] = 'sig_function = '.( $function === null ? 'NULL' : '"'.addslashes($function).'"' );
	}

	$sql = '
		update ord_orders_signature
		set '.implode(', ', $conditions).'
		where sig_tnt_id = '.$config['tnt_id'].' and sig_ord_id = '.$ord_id.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		ord_orders_set_date_modified( $ord_id );
	}

	return $res;

}

/**	Cette fonction récupère des informations concernant une commande signée, ou la liste des commandes signées par un utilisateur
 *	Les paramètres sont mutuellement obligatoires (soit ord_id, soit usr_id, soit firstname + lastname + usr_parent_id)
 *	@param int $ord_id Optionnel, identifiant d'une commande pour laquelle on souhaite récupérer les informations de signature (ou tableau d'identifiants)
 *	@param int $usr_id Optionnel, identifiant d'un utilisateur signataire
 *	@param string $firstname Optionnel, prénom du signataire (insensible à la casse)
 *	@param string $lastname Optionnel, nom du signataire (insensible à la casse)
 *	@param int $usr_parent_id Optionnel, identifiant de la société à laquelle la commande est rattachée
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- ord_id : identifiant de la commande
 *		- signature : signature
 *		- usr_id : identifiant de l'utilisateur signataire
 *		- usr_parent_id : identifiant de la société ayant passée la commande
 *		- firstname : prénom du signataire
 *		- lastname : nom du signataire
 *		- function : fonction / poste du signataire dans la société
 *		- date_created : date et heure de création (disponible depuis le 12/01/2021)
 */
function ord_orders_signature_get( $ord_id = 0, $usr_id = 0, $firstname = '', $lastname = '', $usr_parent_id = 0 ){
	if( is_array($ord_id) ){
		foreach( $ord_id as $one_id ){
			if( !is_numeric($one_id) || $one_id <= 0 ){
				return false;
			}
		}
	}elseif( !is_numeric($ord_id) || $ord_id < 0 ){
		return false;
	}elseif( $ord_id ){
		$ord_id = array($ord_id);
	}else{
		$ord_id = array();
	}
	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}
	if( !is_numeric($usr_parent_id) || $usr_parent_id < 0 ){
		return false;
	}
	$firstname = trim($firstname);
	$lastname = trim($lastname);
	if( !sizeof($ord_id) && !$usr_id && ( $firstname == '' || $lastname == '' || !$usr_parent_id ) ){
		return false;
	}

	global $config;

	$sql = '
		select
			ord_id, sig_signature as signature, sig_usr_id as usr_id,
			if(ifnull(usr_parent_id, 0) = 0, ord_usr_id, usr_parent_id) as usr_parent_id,
			sig_firstname as firstname, sig_lastname as lastname, sig_function as function,
			sig_date_created as date_created
		from
			ord_orders_signature
			join ord_orders on sig_tnt_id = ord_tnt_id and sig_ord_id = ord_id
			left join gu_users on ord_tnt_id = if(usr_tnt_id=0, ord_tnt_id, usr_tnt_id) and ord_usr_id = usr_id
		where
			sig_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($ord_id) ){
		$sql .= ' and ord_id in ('.implode(', ', $ord_id).')';
	}
	if( $usr_id ){
		$sql .= ' and sig_usr_id = '.$usr_id;
	}
	if( $firstname && $lastname && $usr_parent_id ){
		$sql .= '
			and lower(sig_firstname) = lower("'.addslashes($firstname).'")
			and lower(sig_lastname) = lower("'.addslashes($lastname).'")
			and if(ifnull(usr_parent_id, 0) = 0, ord_usr_id, usr_parent_id) = '.$usr_parent_id.'
		';
	}

	return ria_mysql_query($sql);
}

/**	Cette fonction récupère la signature (et uniquement cette information) d'une commande
 *	@param int $ord_id Identifiant de la commande
 *	@return La signature, sous la forme d'une chaîne de caractères
 *	@return bool False en cas d'échec ou si la commande n'est pas signée
 */
function ord_orders_get_signature( $ord_id ){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}
	if( !ord_orders_exists( $ord_id ) ){
		return false;
	}

	global $config;

	$sql = 'select sig_signature from ord_orders_signature where sig_tnt_id = '.$config['tnt_id'].' and sig_ord_id = '.$ord_id;

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);
}

/**	Cette fonction détermine si une commande a été signée
 *	@param int $ord_id Identifiant de la commande
 *	@return bool True si la commande a été signée, False sinon
 */
function ord_orders_signature_exists( $ord_id ){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}
	if( !ord_orders_exists( $ord_id ) ){
		return false;
	}

	global $config;

	$sql = 'select 1 from ord_orders_signature where sig_tnt_id = '.$config['tnt_id'].' and sig_ord_id = '.$ord_id;

	if( !( $r = ria_mysql_query($sql) ) ){
		return false;
	}

	return ria_mysql_num_rows($r);
}

/**	Cette fonction récupère les triplets distincts nom / prénom / fonction associés aux signatures.
 *	@param $ord_usr_id Optionnel, identifiant du client d'une commande pour lequel on souhaite récupérer les triplets.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- firstname : prénom du signataire
 *		- lastname : nom du signataire
 *		- function : fonction du signataire
 */
function ord_orders_signature_get_distinct_users( $ord_usr_id=0 ){
	if( !is_numeric($ord_usr_id) || $ord_usr_id < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select distinct
			ifnull(adr_firstname, sig_firstname) as firstname,
			ifnull(adr_lastname, sig_lastname) as lastname,
			sig_function as function
		from
			ord_orders_signature
			join ord_orders on sig_tnt_id = ord_tnt_id and sig_ord_id = ord_id
			left join gu_users on sig_tnt_id = if(usr_tnt_id = 0, sig_tnt_id, usr_tnt_id) and sig_usr_id = usr_id
			left join gu_adresses on usr_tnt_id = adr_tnt_id and usr_id = adr_usr_id and usr_adr_invoices = adr_id
		where
			sig_tnt_id = '.$config['tnt_id'].'
	';

	if( $ord_usr_id ){
		$sql .= ' and ord_usr_id = '.$ord_usr_id;
	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('ord_orders_signature_get_distinct_users '.mysql_error().' - '.$sql);
	}

	return $r;
}

/// @}

// \endcond
