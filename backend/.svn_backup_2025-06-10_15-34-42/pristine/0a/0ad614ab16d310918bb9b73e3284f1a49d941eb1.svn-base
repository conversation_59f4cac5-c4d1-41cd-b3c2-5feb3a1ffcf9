<?php 

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REDIRECTION_404');

	require_once('errors.inc.php');
	
	$_GET['url'] = urldecode( $_GET['url'] );

	// Permet de marquer une erreur 404 comme résolue
	if( isset($_POST['resolved']) ){
		if( !err_errors_set_resolved($_GET['wst'], $_GET['url'], $_GET['lng']) ){
			$error = _("Une erreur inattendue s'est produite lors du marquage de cette erreur 404 comme résolue. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		}
	}
	
	// Permet de marquer une erreur 404 comme non résolue (uniquement si elle a été préalablement marquée comme résolue)
	if( isset($_POST['noresolved']) ){
		if( !err_errors_set_resolved($_GET['wst'], $_GET['url'], $_GET['lng'], false) ){
			$error = _("Une erreur inattendue s'est produite lors du marquage de cette erreur 404 comme non résolue. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		}
	}
	
	// Charge les informations disponibles sur cette erreur 404
	$rinfo = err_errors_get( isset($_GET['wst']) ? $_GET['wst'] : 0, $_GET['url'], false, '', null, null );
	
	// On récupère l'url du site.
	$site_url = $config['site_url'];
	if( isset($_GET['wst']) ){
		$website = wst_websites_languages_get($_GET['wst'], isset($_GET['lng']) ? $_GET['lng'] : '');
		if( ria_mysql_num_rows($website)>0 ){
			$website = ria_mysql_fetch_array($website);
			$site_url = $website['url'];
		}
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Liste des sources') . ' - ' . _('Erreurs 404') . ' - ' . _('Redirections') . ' - ' . _('Configuration'));
	define('ADMIN_ID_BODY', 'stat-popup-errors');
	define('ADMIN_HEAD_POPUP', true);
	require_once('admin/skin/header.inc.php');
	
	if( !$rinfo || !ria_mysql_num_rows($rinfo) ){
		print '<div class="error">' . _("Une erreur inattendue s'est produite lors du chargement des informations sur l'erreur 404.") . ' <br />' . _("(Veuillez réessayer ou prendre contact pour nous signaler le problème.") . '</div>';
	} else {
		$info = ria_mysql_fetch_array( $rinfo );
?>
    <table class="checklist">
 	   <caption><?php echo _("Informations générales"); ?></caption>
       <col width="175" /><col width="350" />
 	   <tbody>
    		<tr>
	    		<th id="404-url"><?php echo _('URL :'); ?></th>
    			<td headers="404-url"><a target="_blank" href="<?php print $site_url.$_GET['url']; ?>"><?php print $_GET['url']; ?></a></td>
            </tr>
            <tr>
            	<th><?php echo _("Dernière détection :"); ?></th>
                <td><?php print ria_date_format($info['last_fr']); ?></td>
            </tr>
            <tr>
            	<th><?php echo _("Première détection :"); ?></th>
                <td><?php print ria_date_format($info['first_fr']); ?></td>
           	</tr>
		</tbody>
	</table>
	
    <?php
		$sources = err_errors_get_sources( $_GET['url'] );
	?>
    <table>
    	<caption><?php echo _("Liste des urls sources"); ?></caption>
        <col width="600" /><col width="100" />
        <thead>
        	<tr>
            	<th><?php echo _("Source"); ?></th>
                <th><?php echo _("Occurences"); ?></th>
            </tr>
        </thead>
        <tbody><?php
			if( !$sources || !ria_mysql_num_rows($sources) ){
				print '<tr><td colspan="2">' . _('Aucune source n\'a pu être déterminée') . '</td></tr>';	
			} else {
				while( $source = ria_mysql_fetch_array($sources) ){
					print '	<tr>';
					print '		<td><a target="_blank" href="'.$source['source'].'">'.urldecode( $source['source'] ).'</a></td>';
					print '		<td class="right">'.$source['occurence'].'</td>';
					print '	</tr>';
				}
			}
        ?></tbody>
    </table>

	<form action="js_infos.php?url=<?php print urlencode($_GET['url']); ?>&wst=<?php print $_GET['wst']; ?>&lng=<?php print $_GET['lng']; ?>" method="post">
		<?php if( !$info['resolved'] ){ ?>
			<input class="btn-action" type="submit" name="resolved" id="resolved" value="<?php echo _("Marquer comme corrigée"); ?>" />
		<?php } else { ?>
			<input class="btn-action" type="submit" name="noresolved" id="noresolved" value="<?php echo _("Marquer comme non corrigée"); ?>" />
		<?php } ?>
	</form>

	<script><!--
	<?php 
		if( isset($_POST['resolved']) || isset($_POST['noresolved']) ){
	?>
			parent.reload_errors(0);
			parent.hidePopup();
	<?php
		}
	?>
	--></script>
<?php 
	} 

	require_once('admin/skin/footer.inc.php');
?>