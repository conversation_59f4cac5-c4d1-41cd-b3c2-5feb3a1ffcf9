{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/datastore": {"description": "View and manage your Google Cloud Datastore data"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "View and manage your data across Google Cloud Platform services"}}}}, "servicePath": "", "description": "Accesses the schemaless NoSQL database to provide fully managed, robust, scalable storage for your application.\n", "kind": "discovery#restDescription", "rootUrl": "https://datastore.googleapis.com/", "basePath": "", "ownerDomain": "google.com", "name": "datastore", "batchPath": "batch", "id": "datastore:v1", "documentationLink": "https://cloud.google.com/datastore/", "revision": "20171017", "title": "Google Cloud Datastore API", "discoveryVersion": "v1", "ownerName": "Google", "version_module": true, "resources": {"projects": {"methods": {"beginTransaction": {"flatPath": "v1/projects/{projectId}:beginTransaction", "id": "datastore.projects.beginTransaction", "path": "v1/projects/{projectId}:beginTransaction", "description": "Begins a new transaction.", "request": {"$ref": "BeginTransactionRequest"}, "response": {"$ref": "BeginTransactionResponse"}, "parameterOrder": ["projectId"], "httpMethod": "POST", "parameters": {"projectId": {"description": "The ID of the project against which to make the request.", "type": "string", "required": true, "location": "path"}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "commit": {"request": {"$ref": "CommitRequest"}, "description": "Commits a transaction, optionally creating, deleting or modifying some\nentities.", "response": {"$ref": "CommitResponse"}, "parameterOrder": ["projectId"], "httpMethod": "POST", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"], "parameters": {"projectId": {"description": "The ID of the project against which to make the request.", "type": "string", "required": true, "location": "path"}}, "flatPath": "v1/projects/{projectId}:commit", "id": "datastore.projects.commit", "path": "v1/projects/{projectId}:commit"}, "reserveIds": {"description": "Prevents the supplied keys' IDs from being auto-allocated by Cloud\nDatastore.", "request": {"$ref": "ReserveIdsRequest"}, "httpMethod": "POST", "parameterOrder": ["projectId"], "response": {"$ref": "ReserveIdsResponse"}, "parameters": {"projectId": {"description": "The ID of the project against which to make the request.", "type": "string", "required": true, "location": "path"}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"], "flatPath": "v1/projects/{projectId}:reserveIds", "path": "v1/projects/{projectId}:reserveIds", "id": "datastore.projects.reserveIds"}, "rollback": {"response": {"$ref": "RollbackResponse"}, "parameterOrder": ["projectId"], "httpMethod": "POST", "parameters": {"projectId": {"location": "path", "description": "The ID of the project against which to make the request.", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"], "flatPath": "v1/projects/{projectId}:rollback", "id": "datastore.projects.rollback", "path": "v1/projects/{projectId}:rollback", "description": "Rolls back a transaction.", "request": {"$ref": "RollbackRequest"}}, "runQuery": {"description": "Queries for entities.", "request": {"$ref": "RunQueryRequest"}, "httpMethod": "POST", "parameterOrder": ["projectId"], "response": {"$ref": "RunQueryResponse"}, "parameters": {"projectId": {"type": "string", "required": true, "location": "path", "description": "The ID of the project against which to make the request."}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"], "flatPath": "v1/projects/{projectId}:runQuery", "path": "v1/projects/{projectId}:runQuery", "id": "datastore.projects.runQuery"}, "lookup": {"response": {"$ref": "LookupResponse"}, "parameterOrder": ["projectId"], "httpMethod": "POST", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"], "parameters": {"projectId": {"description": "The ID of the project against which to make the request.", "type": "string", "required": true, "location": "path"}}, "flatPath": "v1/projects/{projectId}:lookup", "id": "datastore.projects.lookup", "path": "v1/projects/{projectId}:lookup", "request": {"$ref": "LookupRequest"}, "description": "Looks up entities by key."}, "allocateIds": {"description": "Allocates IDs for the given keys, which is useful for referencing an entity\nbefore it is inserted.", "request": {"$ref": "AllocateIdsRequest"}, "httpMethod": "POST", "parameterOrder": ["projectId"], "response": {"$ref": "AllocateIdsResponse"}, "parameters": {"projectId": {"description": "The ID of the project against which to make the request.", "type": "string", "required": true, "location": "path"}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"], "flatPath": "v1/projects/{projectId}:allocateIds", "path": "v1/projects/{projectId}:allocateIds", "id": "datastore.projects.allocateIds"}}, "resources": {"operations": {"methods": {"cancel": {"path": "v1/{+name}:cancel", "id": "datastore.projects.operations.cancel", "description": "Starts asynchronous cancellation on a long-running operation.  The server\nmakes a best effort to cancel the operation, but success is not\nguaranteed.  If the server doesn't support this method, it returns\n`google.rpc.Code.UNIMPLEMENTED`.  Clients can use\nOperations.GetOperation or\nother methods to check whether the cancellation succeeded or whether the\noperation completed despite cancellation. On successful cancellation,\nthe operation is not deleted; instead, it becomes an operation with\nan Operation.error value with a google.rpc.Status.code of 1,\ncorresponding to `Code.CANCELLED`.", "httpMethod": "POST", "response": {"$ref": "Empty"}, "parameterOrder": ["name"], "parameters": {"name": {"pattern": "^projects/[^/]+/operations/[^/]+$", "location": "path", "description": "The name of the operation resource to be cancelled.", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"], "flatPath": "v1/projects/{projectsId}/operations/{operationsId}:cancel"}, "delete": {"response": {"$ref": "Empty"}, "parameterOrder": ["name"], "httpMethod": "DELETE", "parameters": {"name": {"location": "path", "description": "The name of the operation resource to be deleted.", "type": "string", "required": true, "pattern": "^projects/[^/]+/operations/[^/]+$"}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"], "flatPath": "v1/projects/{projectsId}/operations/{operationsId}", "id": "datastore.projects.operations.delete", "path": "v1/{+name}", "description": "Deletes a long-running operation. This method indicates that the client is\nno longer interested in the operation result. It does not cancel the\noperation. If the server doesn't support this method, it returns\n`google.rpc.Code.UNIMPLEMENTED`."}, "get": {"description": "Gets the latest state of a long-running operation.  Clients can use this\nmethod to poll the operation result at intervals as recommended by the API\nservice.", "response": {"$ref": "GoogleLongrunningOperation"}, "parameterOrder": ["name"], "httpMethod": "GET", "parameters": {"name": {"pattern": "^projects/[^/]+/operations/[^/]+$", "location": "path", "description": "The name of the operation resource.", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"], "flatPath": "v1/projects/{projectsId}/operations/{operationsId}", "id": "datastore.projects.operations.get", "path": "v1/{+name}"}, "list": {"flatPath": "v1/projects/{projectsId}/operations", "id": "datastore.projects.operations.list", "path": "v1/{+name}/operations", "description": "Lists operations that match the specified filter in the request. If the\nserver doesn't support this method, it returns `UNIMPLEMENTED`.\n\nNOTE: the `name` binding allows API services to override the binding\nto use different resource name schemes, such as `users/*/operations`. To\noverride the binding, API services can add a binding such as\n`\"/v1/{name=users/*}/operations\"` to their service configuration.\nFor backwards compatibility, the default name includes the operations\ncollection id, however overriding users must ensure the name binding\nis the parent resource, without the operations collection id.", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "parameterOrder": ["name"], "httpMethod": "GET", "parameters": {"pageToken": {"location": "query", "description": "The standard list page token.", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "type": "string", "required": true, "pattern": "^projects/[^/]+$", "location": "path"}, "pageSize": {"location": "query", "format": "int32", "description": "The standard list page size.", "type": "integer"}, "filter": {"description": "The standard list filter.", "type": "string", "location": "query"}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}}}}}}, "parameters": {"bearer_token": {"description": "OAuth bearer token.", "type": "string", "location": "query"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "type": "string", "location": "query"}, "upload_protocol": {"type": "string", "location": "query", "description": "Upload protocol for media (e.g. \"raw\", \"multipart\")."}, "prettyPrint": {"location": "query", "description": "Returns response with indentations and line breaks.", "default": "true", "type": "boolean"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "type": "string", "location": "query"}, "uploadType": {"type": "string", "location": "query", "description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\")."}, "$.xgafv": {"type": "string", "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "enum": ["1", "2"], "description": "V1 error format."}, "callback": {"location": "query", "description": "JSONP", "type": "string"}, "alt": {"enum": ["json", "media", "proto"], "type": "string", "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "description": "Data format for response.", "default": "json"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "type": "string", "location": "query"}, "access_token": {"description": "OAuth access token.", "type": "string", "location": "query"}, "quotaUser": {"location": "query", "description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "type": "string"}, "pp": {"description": "Pretty-print response.", "default": "true", "type": "boolean", "location": "query"}}, "schemas": {"GoogleDatastoreAdminV1beta1ExportEntitiesMetadata": {"type": "object", "properties": {"outputUrlPrefix": {"description": "Location for the export metadata and data files. This will be the same\nvalue as the\ngoogle.datastore.admin.v1beta1.ExportEntitiesRequest.output_url_prefix\nfield. The final output location is provided in\ngoogle.datastore.admin.v1beta1.ExportEntitiesResponse.output_url.", "type": "string"}, "entityFilter": {"$ref": "GoogleDatastoreAdminV1beta1EntityFilter", "description": "Description of which entities are being exported."}, "progressEntities": {"description": "An estimate of the number of entities processed.", "$ref": "GoogleDatastoreAdminV1beta1Progress"}, "common": {"$ref": "GoogleDatastoreAdminV1beta1CommonMetadata", "description": "Metadata common to all Datastore Admin operations."}, "progressBytes": {"$ref": "GoogleDatastoreAdminV1beta1Progress", "description": "An estimate of the number of bytes processed."}}, "id": "GoogleDatastoreAdminV1beta1ExportEntitiesMetadata", "description": "Metadata for ExportEntities operations."}, "CompositeFilter": {"properties": {"filters": {"description": "The list of filters to combine.\nMust contain at least one filter.", "items": {"$ref": "Filter"}, "type": "array"}, "op": {"enumDescriptions": ["Unspecified. This value must not be used.", "The results are required to satisfy each of the combined filters."], "enum": ["OPERATOR_UNSPECIFIED", "AND"], "description": "The operator for combining multiple filters.", "type": "string"}}, "id": "CompositeFilter", "description": "A filter that merges multiple other filters using the given operator.", "type": "object"}, "AllocateIdsResponse": {"description": "The response for Datastore.AllocateIds.", "type": "object", "properties": {"keys": {"description": "The keys specified in the request (in the same order), each with\nits key path completed with a newly allocated ID.", "items": {"$ref": "Key"}, "type": "array"}}, "id": "AllocateIdsResponse"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a\nnetwork API call.", "type": "object", "properties": {"response": {"additionalProperties": {"type": "any", "description": "Properties of the object. Contains field @type with type URL."}, "description": "The normal response of the operation in case of success.  If the original\nmethod returns no data on success, such as `Delete`, the response is\n`google.protobuf.Empty`.  If the original method is standard\n`Get`/`Create`/`Update`, the response should be the resource.  For other\nmethods, the response should have the type `XxxResponse`, where `Xxx`\nis the original method name.  For example, if the original method name\nis `TakeSnapshot()`, the inferred response type is\n`TakeSnapshotResponse`.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that\noriginally returns it. If you use the default HTTP mapping, the\n`name` should have the format of `operations/some/unique/name`.", "type": "string"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"description": "Service-specific metadata associated with the operation.  It typically\ncontains progress information and common metadata such as create time.\nSome services might not provide such metadata.  Any method that returns a\nlong-running operation should document the metadata type, if any.", "type": "object", "additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}}, "done": {"description": "If the value is `false`, it means the operation is still in progress.\nIf `true`, the operation is completed, and either `error` or `response` is\navailable.", "type": "boolean"}}, "id": "GoogleLongrunningOperation"}, "PropertyFilter": {"properties": {"value": {"description": "The value to compare the property to.", "$ref": "Value"}, "property": {"$ref": "PropertyReference", "description": "The property to filter by."}, "op": {"description": "The operator to filter by.", "type": "string", "enumDescriptions": ["Unspecified. This value must not be used.", "Less than.", "Less than or equal.", "Greater than.", "Greater than or equal.", "Equal.", "Has ancestor."], "enum": ["OPERATOR_UNSPECIFIED", "LESS_THAN", "LESS_THAN_OR_EQUAL", "GREATER_THAN", "GREATER_THAN_OR_EQUAL", "EQUAL", "HAS_ANCESTOR"]}}, "id": "PropertyFilter", "description": "A filter on a specific property.", "type": "object"}, "CommitResponse": {"description": "The response for Datastore.Commit.", "type": "object", "properties": {"mutationResults": {"description": "The result of performing the mutations.\nThe i-th mutation result corresponds to the i-th mutation in the request.", "items": {"$ref": "MutationResult"}, "type": "array"}, "indexUpdates": {"format": "int32", "description": "The number of index entries updated during the commit, or zero if none were\nupdated.", "type": "integer"}}, "id": "CommitResponse"}, "PartitionId": {"description": "A partition ID identifies a grouping of entities. The grouping is always\nby project and namespace, however the namespace ID may be empty.\n\nA partition ID contains several dimensions:\nproject ID and namespace ID.\n\nPartition dimensions:\n\n- May be `\"\"`.\n- Must be valid UTF-8 bytes.\n- Must have values that match regex `[A-Za-z\\d\\.\\-_]{1,100}`\nIf the value of any dimension matches regex `__.*__`, the partition is\nreserved/read-only.\nA reserved/read-only partition ID is forbidden in certain documented\ncontexts.\n\nForeign partition IDs (in which the project ID does\nnot match the context project ID ) are discouraged.\nReads and writes of foreign partition IDs may fail if the project is not in an active state.", "type": "object", "properties": {"namespaceId": {"description": "If not empty, the ID of the namespace to which the entities belong.", "type": "string"}, "projectId": {"description": "The ID of the project to which the entities belong.", "type": "string"}}, "id": "PartitionId"}, "Entity": {"type": "object", "properties": {"properties": {"additionalProperties": {"$ref": "Value"}, "description": "The entity's properties.\nThe map's keys are property names.\nA property name matching regex `__.*__` is reserved.\nA reserved property name is forbidden in certain documented contexts.\nThe name must not contain more than 500 characters.\nThe name cannot be `\"\"`.", "type": "object"}, "key": {"$ref": "Key", "description": "The entity's key.\n\nAn entity must have a key, unless otherwise documented (for example,\nan entity in `Value.entity_value` may have no key).\nAn entity's kind is its key path's last element's kind,\nor null if it has no key."}}, "id": "Entity", "description": "A Datastore data object.\n\nAn entity is limited to 1 megabyte when stored. That _roughly_\ncorresponds to a limit of 1 megabyte for the serialized form of this\nmessage."}, "GoogleDatastoreAdminV1beta1Progress": {"type": "object", "properties": {"workEstimated": {"format": "int64", "description": "An estimate of how much work needs to be performed. May be zero if the\nwork estimate is unavailable.", "type": "string"}, "workCompleted": {"format": "int64", "description": "The amount of work that has been completed. Note that this may be greater\nthan work_estimated.", "type": "string"}}, "id": "GoogleDatastoreAdminV1beta1Progress", "description": "Measures the progress of a particular metric."}, "QueryResultBatch": {"properties": {"endCursor": {"format": "byte", "description": "A cursor that points to the position after the last result in the batch.", "type": "string"}, "moreResults": {"enum": ["MORE_RESULTS_TYPE_UNSPECIFIED", "NOT_FINISHED", "MORE_RESULTS_AFTER_LIMIT", "MORE_RESULTS_AFTER_CURSOR", "NO_MORE_RESULTS"], "description": "The state of the query after the current batch.", "type": "string", "enumDescriptions": ["Unspecified. This value is never used.", "There may be additional batches to fetch from this query.", "The query is finished, but there may be more results after the limit.", "The query is finished, but there may be more results after the end\ncursor.", "The query is finished, and there are no more results."]}, "snapshotVersion": {"format": "int64", "description": "The version number of the snapshot this batch was returned from.\nThis applies to the range of results from the query's `start_cursor` (or\nthe beginning of the query if no cursor was given) to this batch's\n`end_cursor` (not the query's `end_cursor`).\n\nIn a single transaction, subsequent query result batches for the same query\ncan have a greater snapshot version number. Each batch's snapshot version\nis valid for all preceding batches.\nThe value will be zero for eventually consistent queries.", "type": "string"}, "skippedCursor": {"format": "byte", "description": "A cursor that points to the position after the last skipped result.\nWill be set when `skipped_results` != 0.", "type": "string"}, "skippedResults": {"format": "int32", "description": "The number of results skipped, typically because of an offset.", "type": "integer"}, "entityResultType": {"description": "The result type for every entity in `entity_results`.", "type": "string", "enumDescriptions": ["Unspecified. This value is never used.", "The key and properties.", "A projected subset of properties. The entity may have no key.", "Only the key."], "enum": ["RESULT_TYPE_UNSPECIFIED", "FULL", "PROJECTION", "KEY_ONLY"]}, "entityResults": {"description": "The results for this batch.", "items": {"$ref": "EntityResult"}, "type": "array"}}, "id": "QueryResultBatch", "description": "A batch of results produced by a query.", "type": "object"}, "PathElement": {"description": "A (kind, ID/name) pair used to construct a key path.\n\nIf either name or ID is set, the element is complete.\nIf neither is set, the element is incomplete.", "type": "object", "properties": {"name": {"description": "The name of the entity.\nA name matching regex `__.*__` is reserved/read-only.\nA name must not be more than 1500 bytes when UTF-8 encoded.\nCannot be `\"\"`.", "type": "string"}, "kind": {"description": "The kind of the entity.\nA kind matching regex `__.*__` is reserved/read-only.\nA kind must not contain more than 1500 bytes when UTF-8 encoded.\nCannot be `\"\"`.", "type": "string"}, "id": {"format": "int64", "description": "The auto-allocated ID of the entity.\nNever equal to zero. Values less than zero are discouraged and may not\nbe supported in the future.", "type": "string"}}, "id": "PathElement"}, "GqlQueryParameter": {"id": "GqlQueryParameter", "description": "A binding parameter for a GQL query.", "type": "object", "properties": {"cursor": {"format": "byte", "description": "A query cursor. Query cursors are returned in query\nresult batches.", "type": "string"}, "value": {"$ref": "Value", "description": "A value parameter."}}}, "GoogleLongrunningListOperationsResponse": {"type": "object", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "id": "GoogleLongrunningListOperationsResponse", "description": "The response message for Operations.ListOperations."}, "BeginTransactionResponse": {"description": "The response for Datastore.BeginTransaction.", "type": "object", "properties": {"transaction": {"format": "byte", "description": "The transaction identifier (always present).", "type": "string"}}, "id": "BeginTransactionResponse"}, "LookupResponse": {"id": "LookupResponse", "description": "The response for Datastore.Lookup.", "type": "object", "properties": {"deferred": {"description": "A list of keys that were not looked up due to resource constraints. The\norder of results in this field is undefined and has no relation to the\norder of the keys in the input.", "items": {"$ref": "Key"}, "type": "array"}, "missing": {"description": "Entities not found as `ResultType.KEY_ONLY` entities. The order of results\nin this field is undefined and has no relation to the order of the keys\nin the input.", "items": {"$ref": "EntityResult"}, "type": "array"}, "found": {"items": {"$ref": "EntityResult"}, "type": "array", "description": "Entities found as `ResultType.FULL` entities. The order of results in this\nfield is undefined and has no relation to the order of the keys in the\ninput."}}}, "RunQueryResponse": {"description": "The response for Datastore.RunQuery.", "type": "object", "properties": {"batch": {"$ref": "QueryResultBatch", "description": "A batch of query results (always present)."}, "query": {"$ref": "Query", "description": "The parsed form of the `GqlQuery` from the request, if it was set."}}, "id": "RunQueryResponse"}, "AllocateIdsRequest": {"description": "The request for Datastore.AllocateIds.", "type": "object", "properties": {"keys": {"description": "A list of keys with incomplete key paths for which to allocate IDs.\nNo key may be reserved/read-only.", "items": {"$ref": "Key"}, "type": "array"}}, "id": "AllocateIdsRequest"}, "CommitRequest": {"id": "CommitRequest", "description": "The request for Datastore.Commit.", "type": "object", "properties": {"transaction": {"format": "byte", "description": "The identifier of the transaction associated with the commit. A\ntransaction identifier is returned by a call to\nDatastore.BeginTransaction.", "type": "string"}, "mode": {"type": "string", "enumDescriptions": ["Unspecified. This value must not be used.", "Transactional: The mutations are either all applied, or none are applied.\nLearn about transactions [here](https://cloud.google.com/datastore/docs/concepts/transactions).", "Non-transactional: The mutations may not apply as all or none."], "enum": ["MODE_UNSPECIFIED", "TRANSACTIONAL", "NON_TRANSACTIONAL"], "description": "The type of commit to perform. Defaults to `TRANSACTIONAL`."}, "mutations": {"description": "The mutations to perform.\n\nWhen mode is `TRANSACTIONAL`, mutations affecting a single entity are\napplied in order. The following sequences of mutations affecting a single\nentity are not permitted in a single `Commit` request:\n\n- `insert` followed by `insert`\n- `update` followed by `insert`\n- `upsert` followed by `insert`\n- `delete` followed by `update`\n\nWhen mode is `NON_TRANSACTIONAL`, no two mutations may affect a single\nentity.", "items": {"$ref": "Mutation"}, "type": "array"}}}, "BeginTransactionRequest": {"description": "The request for Datastore.BeginTransaction.", "type": "object", "properties": {"transactionOptions": {"$ref": "TransactionOptions", "description": "Options for a new transaction."}}, "id": "BeginTransactionRequest"}, "KindExpression": {"description": "A representation of a kind.", "type": "object", "properties": {"name": {"description": "The name of the kind.", "type": "string"}}, "id": "KindExpression"}, "Key": {"description": "A unique identifier for an entity.\nIf a key's partition ID or any of its path kinds or names are\nreserved/read-only, the key is reserved/read-only.\nA reserved/read-only key is forbidden in certain documented contexts.", "type": "object", "properties": {"path": {"description": "The entity path.\nAn entity path consists of one or more elements composed of a kind and a\nstring or numerical identifier, which identify entities. The first\nelement identifies a _root entity_, the second element identifies\na _child_ of the root entity, the third element identifies a child of the\nsecond entity, and so forth. The entities identified by all prefixes of\nthe path are called the element's _ancestors_.\n\nAn entity path is always fully complete: *all* of the entity's ancestors\nare required to be in the path along with the entity identifier itself.\nThe only exception is that in some documented cases, the identifier in the\nlast path element (for the entity) itself may be omitted. For example,\nthe last path element of the key of `Mutation.insert` may have no\nidentifier.\n\nA path can never be empty, and a path can have at most 100 elements.", "items": {"$ref": "PathElement"}, "type": "array"}, "partitionId": {"$ref": "PartitionId", "description": "Entities are partitioned into subsets, currently identified by a project\nID and namespace ID.\nQueries are scoped to a single partition."}}, "id": "Key"}, "LatLng": {"description": "An object representing a latitude/longitude pair. This is expressed as a pair\nof doubles representing degrees latitude and degrees longitude. Unless\nspecified otherwise, this must conform to the\n<a href=\"http://www.unoosa.org/pdf/icg/2012/template/WGS_84.pdf\">WGS84\nstandard</a>. Values must be within normalized ranges.\n\nExample of normalization code in Python:\n\n    def NormalizeLongitude(longitude):\n      \"\"\"Wraps decimal degrees longitude to [-180.0, 180.0].\"\"\"\n      q, r = divmod(longitude, 360.0)\n      if r > 180.0 or (r == 180.0 and q <= -1.0):\n        return r - 360.0\n      return r\n\n    def NormalizeLatLng(latitude, longitude):\n      \"\"\"Wraps decimal degrees latitude and longitude to\n      [-90.0, 90.0] and [-180.0, 180.0], respectively.\"\"\"\n      r = latitude % 360.0\n      if r <= 90.0:\n        return r, NormalizeLongitude(longitude)\n      elif r >= 270.0:\n        return r - 360, NormalizeLongitude(longitude)\n      else:\n        return 180 - r, NormalizeLongitude(longitude + 180.0)\n\n    assert 180.0 == NormalizeLongitude(180.0)\n    assert -180.0 == NormalizeLongitude(-180.0)\n    assert -179.0 == NormalizeLongitude(181.0)\n    assert (0.0, 0.0) == NormalizeLatLng(360.0, 0.0)\n    assert (0.0, 0.0) == NormalizeLatLng(-360.0, 0.0)\n    assert (85.0, 180.0) == NormalizeLatLng(95.0, 0.0)\n    assert (-85.0, -170.0) == NormalizeLatLng(-95.0, 10.0)\n    assert (90.0, 10.0) == NormalizeLatLng(90.0, 10.0)\n    assert (-90.0, -10.0) == NormalizeLatLng(-90.0, -10.0)\n    assert (0.0, -170.0) == NormalizeLatLng(-180.0, 10.0)\n    assert (0.0, -170.0) == NormalizeLatLng(180.0, 10.0)\n    assert (-90.0, 10.0) == NormalizeLatLng(270.0, 10.0)\n    assert (90.0, 10.0) == NormalizeLatLng(-270.0, 10.0)", "type": "object", "properties": {"longitude": {"format": "double", "description": "The longitude in degrees. It must be in the range [-180.0, +180.0].", "type": "number"}, "latitude": {"format": "double", "description": "The latitude in degrees. It must be in the range [-90.0, +90.0].", "type": "number"}}, "id": "LatLng"}, "GoogleDatastoreAdminV1beta1CommonMetadata": {"id": "GoogleDatastoreAdminV1beta1CommonMetadata", "description": "Metadata common to all Datastore Admin operations.", "type": "object", "properties": {"startTime": {"format": "google-datetime", "description": "The time that work began on the operation.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The client-assigned labels which were provided when the operation was\ncreated. May also include additional labels.", "type": "object"}, "endTime": {"type": "string", "format": "google-datetime", "description": "The time the operation ended, either successfully or otherwise."}, "state": {"enumDescriptions": ["Unspecified.", "Request is being prepared for processing.", "Request is actively being processed.", "Request is in the process of being cancelled after user called\ngoogle.longrunning.Operations.CancelOperation on the operation.", "Request has been processed and is in its finalization stage.", "Request has completed successfully.", "Request has finished being processed, but encountered an error.", "Request has finished being cancelled after user called\ngoogle.longrunning.Operations.CancelOperation."], "enum": ["STATE_UNSPECIFIED", "INITIALIZING", "PROCESSING", "CANCELLING", "FINALIZING", "SUCCESSFUL", "FAILED", "CANCELLED"], "description": "The current state of the Operation.", "type": "string"}, "operationType": {"enumDescriptions": ["Unspecified.", "ExportEntities.", "ImportEntities."], "enum": ["OPERATION_TYPE_UNSPECIFIED", "EXPORT_ENTITIES", "IMPORT_ENTITIES"], "description": "The type of the operation. Can be used as a filter in\nListOperationsRequest.", "type": "string"}}}, "ArrayValue": {"id": "ArrayValue", "description": "An array value.", "type": "object", "properties": {"values": {"description": "Values in the array.\nThe order of this array may not be preserved if it contains a mix of\nindexed and unindexed values.", "items": {"$ref": "Value"}, "type": "array"}}}, "GoogleDatastoreAdminV1beta1ExportEntitiesResponse": {"description": "The response for\ngoogle.datastore.admin.v1beta1.DatastoreAdmin.ExportEntities.", "type": "object", "properties": {"outputUrl": {"type": "string", "description": "Location of the output metadata file. This can be used to begin an import\ninto Cloud Datastore (this project or another project). See\ngoogle.datastore.admin.v1beta1.ImportEntitiesRequest.input_url.\nOnly present if the operation completed successfully."}}, "id": "GoogleDatastoreAdminV1beta1ExportEntitiesResponse"}, "GqlQuery": {"id": "GqlQuery", "description": "A [GQL query](https://cloud.google.com/datastore/docs/apis/gql/gql_reference).", "type": "object", "properties": {"queryString": {"description": "A string of the format described\n[here](https://cloud.google.com/datastore/docs/apis/gql/gql_reference).", "type": "string"}, "positionalBindings": {"description": "Numbered binding site @1 references the first numbered parameter,\neffectively using 1-based indexing, rather than the usual 0.\n\nFor each binding site numbered i in `query_string`, there must be an i-th\nnumbered parameter. The inverse must also be true.", "items": {"$ref": "GqlQueryParameter"}, "type": "array"}, "namedBindings": {"type": "object", "additionalProperties": {"$ref": "GqlQueryParameter"}, "description": "For each non-reserved named binding site in the query string, there must be\na named parameter with that name, but not necessarily the inverse.\n\nKey must match regex `A-Za-z_$*`, must not match regex\n`__.*__`, and must not be `\"\"`."}, "allowLiterals": {"description": "When false, the query string must not contain any literals and instead must\nbind all values. For example,\n`SELECT * FROM Kind WHERE a = 'string literal'` is not allowed, while\n`SELECT * FROM Kind WHERE a = @value` is.", "type": "boolean"}}}, "Filter": {"id": "Filter", "description": "A holder for any type of filter.", "type": "object", "properties": {"propertyFilter": {"description": "A filter on a property.", "$ref": "PropertyFilter"}, "compositeFilter": {"description": "A composite filter.", "$ref": "CompositeFilter"}}}, "RunQueryRequest": {"description": "The request for Datastore.RunQuery.", "type": "object", "properties": {"readOptions": {"$ref": "ReadOptions", "description": "The options for this query."}, "query": {"$ref": "Query", "description": "The query to run."}, "gqlQuery": {"$ref": "GqlQuery", "description": "The GQL query to run."}, "partitionId": {"$ref": "PartitionId", "description": "Entities are partitioned into subsets, identified by a partition ID.\nQueries are scoped to a single partition.\nThis partition ID is normalized with the standard default context\npartition ID."}}, "id": "RunQueryRequest"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated\nempty messages in your APIs. A typical example is to use it as the request\nor the response type of an API method. For instance:\n\n    service Foo {\n      rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty);\n    }\n\nThe JSON representation for `Empty` is empty JSON object `{}`.", "type": "object", "properties": {}, "id": "Empty"}, "TransactionOptions": {"description": "Options for beginning a new transaction.\n\nTransactions can be created explicitly with calls to\nDatastore.BeginTransaction or implicitly by setting\nReadOptions.new_transaction in read requests.", "type": "object", "properties": {"readOnly": {"$ref": "Read<PERSON>nly", "description": "The transaction should only allow reads."}, "readWrite": {"$ref": "ReadWrite", "description": "The transaction should allow both reads and writes."}}, "id": "TransactionOptions"}, "GoogleDatastoreAdminV1beta1ImportEntitiesMetadata": {"type": "object", "properties": {"common": {"$ref": "GoogleDatastoreAdminV1beta1CommonMetadata", "description": "Metadata common to all Datastore Admin operations."}, "inputUrl": {"description": "The location of the import metadata file. This will be the same value as\nthe google.datastore.admin.v1beta1.ExportEntitiesResponse.output_url\nfield.", "type": "string"}, "progressBytes": {"description": "An estimate of the number of bytes processed.", "$ref": "GoogleDatastoreAdminV1beta1Progress"}, "entityFilter": {"description": "Description of which entities are being imported.", "$ref": "GoogleDatastoreAdminV1beta1EntityFilter"}, "progressEntities": {"description": "An estimate of the number of entities processed.", "$ref": "GoogleDatastoreAdminV1beta1Progress"}}, "id": "GoogleDatastoreAdminV1beta1ImportEntitiesMetadata", "description": "Metadata for ImportEntities operations."}, "Query": {"description": "A query for entities.", "type": "object", "properties": {"kind": {"description": "The kinds to query (if empty, returns entities of all kinds).\nCurrently at most 1 kind may be specified.", "items": {"$ref": "KindExpression"}, "type": "array"}, "distinctOn": {"description": "The properties to make distinct. The query results will contain the first\nresult for each distinct combination of values for the given properties\n(if empty, all results are returned).", "items": {"$ref": "PropertyReference"}, "type": "array"}, "order": {"description": "The order to apply to the query results (if empty, order is unspecified).", "items": {"$ref": "PropertyOrder"}, "type": "array"}, "projection": {"items": {"$ref": "Projection"}, "type": "array", "description": "The projection to return. Defaults to returning all properties."}, "endCursor": {"type": "string", "format": "byte", "description": "An ending point for the query results. Query cursors are\nreturned in query result batches and\n[can only be used to limit the same query](https://cloud.google.com/datastore/docs/concepts/queries#cursors_limits_and_offsets)."}, "limit": {"format": "int32", "description": "The maximum number of results to return. Applies after all other\nconstraints. Optional.\nUnspecified is interpreted as no limit.\nMust be >= 0 if specified.", "type": "integer"}, "filter": {"$ref": "Filter", "description": "The filter to apply."}, "offset": {"format": "int32", "description": "The number of results to skip. Applies before limit, but after all other\nconstraints. Optional. Must be >= 0 if specified.", "type": "integer"}, "startCursor": {"type": "string", "format": "byte", "description": "A starting point for the query results. Query cursors are\nreturned in query result batches and\n[can only be used to continue the same query](https://cloud.google.com/datastore/docs/concepts/queries#cursors_limits_and_offsets)."}}, "id": "Query"}, "ReadOnly": {"type": "object", "properties": {}, "id": "Read<PERSON>nly", "description": "Options specific to read-only transactions."}, "EntityResult": {"properties": {"cursor": {"format": "byte", "description": "A cursor that points to the position after the result entity.\nSet only when the `EntityResult` is part of a `QueryResultBatch` message.", "type": "string"}, "version": {"format": "int64", "description": "The version of the entity, a strictly positive number that monotonically\nincreases with changes to the entity.\n\nThis field is set for `FULL` entity\nresults.\n\nFor missing entities in `LookupResponse`, this\nis the version of the snapshot that was used to look up the entity, and it\nis always set except for eventually consistent reads.", "type": "string"}, "entity": {"$ref": "Entity", "description": "The resulting entity."}}, "id": "EntityResult", "description": "The result of fetching an entity from Datastore.", "type": "object"}, "Value": {"properties": {"entityValue": {"description": "An entity value.\n\n- May have no key.\n- May have a key with an incomplete key path.\n- May have a reserved/read-only key.", "$ref": "Entity"}, "geoPointValue": {"$ref": "LatLng", "description": "A geo point value representing a point on the surface of Earth."}, "keyValue": {"$ref": "Key", "description": "A key value."}, "integerValue": {"type": "string", "format": "int64", "description": "An integer value."}, "stringValue": {"description": "A UTF-8 encoded string value.\nWhen `exclude_from_indexes` is false (it is indexed) , may have at most 1500 bytes.\nOtherwise, may be set to at least 1,000,000 bytes.", "type": "string"}, "excludeFromIndexes": {"description": "If the value should be excluded from all indexes including those defined\nexplicitly.", "type": "boolean"}, "doubleValue": {"type": "number", "format": "double", "description": "A double value."}, "timestampValue": {"format": "google-datetime", "description": "A timestamp value.\nWhen stored in the Datastore, precise only to microseconds;\nany additional precision is rounded down.", "type": "string"}, "booleanValue": {"description": "A boolean value.", "type": "boolean"}, "nullValue": {"description": "A null value.", "type": "string", "enumDescriptions": ["Null value."], "enum": ["NULL_VALUE"]}, "blobValue": {"format": "byte", "description": "A blob value.\nMay have at most 1,000,000 bytes.\nWhen `exclude_from_indexes` is false, may have at most 1500 bytes.\nIn JSON requests, must be base64-encoded.", "type": "string"}, "meaning": {"type": "integer", "format": "int32", "description": "The `meaning` field should only be populated for backwards compatibility."}, "arrayValue": {"$ref": "ArrayValue", "description": "An array value.\nCannot contain another array value.\nA `Value` instance that sets field `array_value` must not set fields\n`meaning` or `exclude_from_indexes`."}}, "id": "Value", "description": "A message that can hold any of the supported value types and associated\nmetadata.", "type": "object"}, "ReadWrite": {"id": "ReadWrite", "description": "Options specific to read / write transactions.", "type": "object", "properties": {"previousTransaction": {"format": "byte", "description": "The transaction identifier of the transaction being retried.", "type": "string"}}}, "LookupRequest": {"description": "The request for Datastore.Lookup.", "type": "object", "properties": {"keys": {"description": "Keys of entities to look up.", "items": {"$ref": "Key"}, "type": "array"}, "readOptions": {"$ref": "ReadOptions", "description": "The options for this lookup request."}}, "id": "LookupRequest"}, "ReserveIdsRequest": {"description": "The request for Datastore.ReserveIds.", "type": "object", "properties": {"keys": {"description": "A list of keys with complete key paths whose numeric IDs should not be\nauto-allocated.", "items": {"$ref": "Key"}, "type": "array"}, "databaseId": {"description": "If not empty, the ID of the database against which to make the request.", "type": "string"}}, "id": "ReserveIdsRequest"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different\nprogramming environments, including REST APIs and RPC APIs. It is used by\n[gRPC](https://github.com/grpc). The error model is designed to be:\n\n- Simple to use and understand for most users\n- Flexible enough to meet unexpected needs\n\n# Overview\n\nThe `Status` message contains three pieces of data: error code, error message,\nand error details. The error code should be an enum value of\ngoogle.rpc.Code, but it may accept additional error codes if needed.  The\nerror message should be a developer-facing English message that helps\ndevelopers *understand* and *resolve* the error. If a localized user-facing\nerror message is needed, put the localized message in the error details or\nlocalize it in the client. The optional error details may contain arbitrary\ninformation about the error. There is a predefined set of error detail types\nin the package `google.rpc` that can be used for common error conditions.\n\n# Language mapping\n\nThe `Status` message is the logical representation of the error model, but it\nis not necessarily the actual wire format. When the `Status` message is\nexposed in different client libraries and different wire protocols, it can be\nmapped differently. For example, it will likely be mapped to some exceptions\nin Java, but more likely mapped to some error codes in C.\n\n# Other uses\n\nThe error model and the `Status` message can be used in a variety of\nenvironments, either with or without APIs, to provide a\nconsistent developer experience across different environments.\n\nExample uses of this error model include:\n\n- Partial errors. If a service needs to return partial errors to the client,\n    it may embed the `Status` in the normal response to indicate the partial\n    errors.\n\n- Workflow errors. A typical workflow has multiple steps. Each step may\n    have a `Status` message for error reporting.\n\n- Batch operations. If a client uses batch request and batch response, the\n    `Status` message should be used directly inside batch response, one for\n    each error sub-response.\n\n- Asynchronous operations. If an API call embeds asynchronous operation\n    results in its response, the status of those operations should be\n    represented directly using the `Status` message.\n\n- Logging. If some API errors are stored in logs, the message `Status` could\n    be used directly after any stripping needed for security/privacy reasons.", "type": "object", "properties": {"code": {"format": "int32", "description": "The status code, which should be an enum value of google.rpc.Code.", "type": "integer"}, "message": {"description": "A developer-facing error message, which should be in English. Any\nuser-facing error message should be localized and sent in the\ngoogle.rpc.Status.details field, or localized by the client.", "type": "string"}, "details": {"description": "A list of messages that carry the error details.  There is a common set of\nmessage types for APIs to use.", "items": {"type": "object", "additionalProperties": {"type": "any", "description": "Properties of the object. Contains field @type with type URL."}}, "type": "array"}}, "id": "Status"}, "PropertyOrder": {"description": "The desired order for a specific property.", "type": "object", "properties": {"direction": {"description": "The direction to order by. Defaults to `ASCENDING`.", "type": "string", "enumDescriptions": ["Unspecified. This value must not be used.", "Ascending.", "Descending."], "enum": ["DIRECTION_UNSPECIFIED", "ASCENDING", "DESCENDING"]}, "property": {"description": "The property to order by.", "$ref": "PropertyReference"}}, "id": "PropertyOrder"}, "ReserveIdsResponse": {"properties": {}, "id": "ReserveIdsResponse", "description": "The response for Datastore.ReserveIds.", "type": "object"}, "GoogleDatastoreAdminV1beta1EntityFilter": {"description": "Identifies a subset of entities in a project. This is specified as\ncombinations of kinds and namespaces (either or both of which may be all, as\ndescribed in the following examples).\nExample usage:\n\nEntire project:\n  kinds=[], namespace_ids=[]\n\nKinds Foo and Bar in all namespaces:\n  kinds=['Foo', 'Bar'], namespace_ids=[]\n\nKinds Foo and Bar only in the default namespace:\n  kinds=['Foo', 'Bar'], namespace_ids=['']\n\nKinds Foo and Bar in both the default and Baz namespaces:\n  kinds=['Foo', 'Bar'], namespace_ids=['', 'Baz']\n\nThe entire Baz namespace:\n  kinds=[], namespace_ids=['Baz']", "type": "object", "properties": {"kinds": {"description": "If empty, then this represents all kinds.", "items": {"type": "string"}, "type": "array"}, "namespaceIds": {"description": "An empty list represents all namespaces. This is the preferred\nusage for projects that don't use namespaces.\n\nAn empty string element represents the default namespace. This should be\nused if the project has data in non-default namespaces, but doesn't want to\ninclude them.\nEach namespace in this list must be unique.", "items": {"type": "string"}, "type": "array"}}, "id": "GoogleDatastoreAdminV1beta1EntityFilter"}, "PropertyReference": {"description": "A reference to a property relative to the kind expressions.", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the property.\nIf name includes \".\"s, it may be interpreted as a property name path."}}, "id": "PropertyReference"}, "Projection": {"description": "A representation of a property in a projection.", "type": "object", "properties": {"property": {"description": "The property to project.", "$ref": "PropertyReference"}}, "id": "Projection"}, "Mutation": {"description": "A mutation to apply to an entity.", "type": "object", "properties": {"delete": {"description": "The key of the entity to delete. The entity may or may not already exist.\nMust have a complete key path and must not be reserved/read-only.", "$ref": "Key"}, "baseVersion": {"format": "int64", "description": "The version of the entity that this mutation is being applied to. If this\ndoes not match the current version on the server, the mutation conflicts.", "type": "string"}, "insert": {"$ref": "Entity", "description": "The entity to insert. The entity must not already exist.\nThe entity key's final path element may be incomplete."}, "update": {"description": "The entity to update. The entity must already exist.\nMust have a complete key path.", "$ref": "Entity"}, "upsert": {"description": "The entity to upsert. The entity may or may not already exist.\nThe entity key's final path element may be incomplete.", "$ref": "Entity"}}, "id": "Mutation"}, "ReadOptions": {"properties": {"transaction": {"type": "string", "format": "byte", "description": "The identifier of the transaction in which to read. A\ntransaction identifier is returned by a call to\nDatastore.BeginTransaction."}, "readConsistency": {"enumDescriptions": ["Unspecified. This value must not be used.", "Strong consistency.", "Eventual consistency."], "enum": ["READ_CONSISTENCY_UNSPECIFIED", "STRONG", "EVENTUAL"], "description": "The non-transactional read consistency to use.\nCannot be set to `STRONG` for global queries.", "type": "string"}}, "id": "ReadOptions", "description": "The options shared by read requests.", "type": "object"}, "RollbackResponse": {"type": "object", "properties": {}, "id": "RollbackResponse", "description": "The response for Datastore.Rollback.\n(an empty message)."}, "MutationResult": {"description": "The result of applying a mutation.", "type": "object", "properties": {"version": {"format": "int64", "description": "The version of the entity on the server after processing the mutation. If\nthe mutation doesn't change anything on the server, then the version will\nbe the version of the current entity or, if no entity is present, a version\nthat is strictly greater than the version of any previous entity and less\nthan the version of any possible future entity.", "type": "string"}, "conflictDetected": {"description": "Whether a conflict was detected for this mutation. Always false when a\nconflict detection strategy field is not set in the mutation.", "type": "boolean"}, "key": {"$ref": "Key", "description": "The automatically allocated key.\nSet only when the mutation allocated a key."}}, "id": "MutationResult"}, "RollbackRequest": {"properties": {"transaction": {"format": "byte", "description": "The transaction identifier, returned by a call to\nDatastore.BeginTransaction.", "type": "string"}}, "id": "RollbackRequest", "description": "The request for Datastore.Rollback.", "type": "object"}}, "protocol": "rest", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "version": "v1", "baseUrl": "https://datastore.googleapis.com/"}