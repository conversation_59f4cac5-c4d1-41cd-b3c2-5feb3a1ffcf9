<?php
	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWSLETTER_EDIT');

	if( !isset($_GET['cat']) || !nlr_categories_exists($_GET['cat']) ){
		header('Location: /admin/tools/newsletter/index.php');
		exit;
	}

	$nlr = ria_mysql_fetch_array( nlr_categorie_get($_GET['cat']) );

	if( isset($_POST['save']) ){
		$wst_id = isset($_POST['website']) ? $_POST['website'] : 0;

		if( !isset($_POST['name'], $_POST['desc']) ){
			$error = _("Une ou plusieurs informations sont manquantes.");
		}elseif( trim($_POST['name'])=='' ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes.");
		}elseif( !nlr_categorie_update($_GET['cat'], $_POST['name'], $_POST['desc'], $wst_id) ){
			$error = _("Une erreur inattendue s'est produite lors de la mise à jour des informations sur la newsletter. Merci de réessayer ou prendre contact pour nous signaler le problème.");
		}

		if( !isset($error) ){
			$_SESSION['save-nlr-success'] = true;

			header('Location: edit-cat.php?cat='.$_GET['cat']);
			exit;
		}
	}

	if( isset($_POST['cancel']) ){
		header('Location: list.php?oc='.$_GET['cat']);
		exit;
	}

	if( isset($_POST['delete']) ){
		if( !nlr_categorie_del($_GET['cat']) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la newsletter. Merci de réessayer ou prendre contact pour nous signaler le problème.");
		}

		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}


	require_once('newsletter.inc.php');

	require_once('admin/skin/header.inc.php');

	print '
		<h2>'._('Newsletter').' '.htmlspecialchars( $nlr['cat'] ).'</h2>
	';

	if( isset($error) ){
		print '<div class="error">'.nl2br( $error ).'</div>';
	}elseif( isset($_SESSION['save-nlr-success']) ){
		print '<div class="success">'._('L\'enregistrement des informations s\'est correctement déroulé.').'</div>';
		unset($_SESSION['save-nlr-success']);
	}

	print '
		<form method="post" action="edit-cat.php?cat='.$_GET['cat'].'">
			<table>
				<caption>'._('Général').'</caption>
				<tbody>
					<tr>
						<td><label for="name"><span class="mandatory">*</span> '._('Nom :').'</label></td>
						<td><input type="text" maxlength="75" value="'.htmlspecialchars( isset($_POST['name']) ? $_POST['name'] : $nlr['cat'] ).'" id="name" name="name" /></td>
					</tr>
					<tr>
						<td><label for="desc">'._('Description :').'</label></td>
						<td><textarea rows="5" cols="40" id="desc" name="desc">'.htmlspecialchars( isset($_POST['desc']) ? $_POST['desc'] : $nlr['desc'] ).'</textarea></td>
					</tr>
	';

	$rwst = wst_websites_get();
	if( $rwst && ria_mysql_num_rows($rwst)>1 ){
		print '
					<tr>
						<td><label for="website">'._('Inscription depuis').' :</label></td>
						<td>
							<select name="website" id="website">
								<option value="0">'._('Tous les sites').'</option>
		';

		while( $wst = ria_mysql_fetch_array($rwst) ){
			$selected = $wst['id']==(isset($_POST['website']) ? $_POST['website'] : $nlr['wst_id']) ? 'selected="selected"' : '';

			print '
								<option '.$selected.' value="'.$wst['id'].'">'.htmlspecialchars( $wst['name'] ).'</option>
			';
		}

		print '
							</select>
						</td>
					</tr>
		';
	}

	print '		</tbody>
				<tfoot>
					<tr>
						<td colspan="2">
							<input type="submit" value="'._('Enregistrer').'" name="save" />
							<input type="submit" value="'._('Annuler').'" name="cancel" />';
							if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWSLETTER_DEL') ){			
			print '					<input type="submit" value="'._('Supprimer').'" name="delete" />';
							}
			print '				</td>
					</tr>
				</tfoot>
			</table>
		</form>
	';

	require_once('admin/skin/footer.inc.php');
