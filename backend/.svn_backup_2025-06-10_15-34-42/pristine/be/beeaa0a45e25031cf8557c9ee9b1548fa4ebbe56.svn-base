{"name": "phpdocumentor/type-resolver", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^5.5 || ^7.0", "phpdocumentor/reflection-common": "^1.0"}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "autoload-dev": {"psr-4": {"phpDocumentor\\Reflection\\": ["tests/unit"]}}, "require-dev": {"phpunit/phpunit": "^5.2||^4.8.24", "mockery/mockery": "^0.9.4"}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}