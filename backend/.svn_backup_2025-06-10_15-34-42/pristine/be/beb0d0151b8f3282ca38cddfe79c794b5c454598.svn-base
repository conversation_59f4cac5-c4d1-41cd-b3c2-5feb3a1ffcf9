# AccountInfoLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**self** | [**\Swagger\Client\Model\LinksGetUserAccountInfoLink**](LinksGetUserAccountInfoLink.md) |  | 
**save_personal_info** | [**\Swagger\Client\Model\LinksSavePersonalInfoLink**](LinksSavePersonalInfoLink.md) |  | 
**change_password** | [**\Swagger\Client\Model\LinksChangePasswordLink**](LinksChangePasswordLink.md) |  | 
**save_company_info** | [**\Swagger\Client\Model\LinksSaveCompanyInfoLink**](LinksSaveCompanyInfoLink.md) |  | 
**get_profile_picture_info** | [**\Swagger\Client\Model\LinksGetProfilePictureInfoLink**](LinksGetProfilePictureInfoLink.md) |  | 
**save_profile_picture_info** | [**\Swagger\Client\Model\LinksSaveProfilePictureInfoLink**](LinksSaveProfilePictureInfoLink.md) |  | 
**get_credit_card_info** | [**\Swagger\Client\Model\LinksGetCreditCardInfoLink**](LinksGetCreditCardInfoLink.md) |  | 
**save_credit_card_info** | [**\Swagger\Client\Model\LinksSaveCreditCardInfoLink**](LinksSaveCreditCardInfoLink.md) |  | 
**activate_user_account** | [**\Swagger\Client\Model\LinksActivateUserAccountLink**](LinksActivateUserAccountLink.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


