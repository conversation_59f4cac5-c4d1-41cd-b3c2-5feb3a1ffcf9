<?php

/**	\defgroup model_prd_tellafriend Envoyer à un ami
 *	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion de la fonction "Envoyer à un ami" présente sur un certain nombre de fiches produit
 *	@{
 */

require_once('db.inc.php');
require_once('products.inc.php');
require_once('email.inc.php');
require_once('strings.inc.php');
//require_once('view.site.inc.php');
require_once('messages.inc.php');
require_once('prices.inc.php');

/**	Cette fonction permet l'envoi et le stockage d'un email de type "Recommander un article à un ami"
 *	@param int $prd Obligatoire, Identifiant du produit à envoyer
 *	@param int $cat Obligatoire, Identifiant de la catégorie dans laquelle le produit à été vu par l'émetteur
 *	@param $sender_firstname Obligatoire, Prénom de l'émetteur (chaîne vide possible)
 *	@param $sender_lastname Obligatoire, Nom de famille de l'émetteur (chaîne vide possible)
 *	@param $sender_email Obligatoire, Adresse email de l'émetteur
 *	@param $receiver_email Obligatoire, Adresse email du destinataire
 *	@param $notes Obligatoire, Message d'accompagnement
 *	@param bool $published  Facultatif, booléen indiquant si seul les produits publiés peuvent faire l'objet d'un avis ou non
 *	@param $review Facultatif, Identifiant du commentaire à afficher
 */
function prd_tell_a_friend_send( $prd, $cat, $sender_firstname, $sender_lastname, $sender_email, $receiver_email, $notes, $published=true, $review=0 ){
	global $config;

	if( !prd_products_exists($prd) ) return false;
	if( !prd_categories_exists($cat) ) return false;
	if( !isemail($sender_email) ) return false;
	if( !isemail($receiver_email) ) return false;

	// if( isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR']=='************' ) return 0; // toolsnfun.com (javascript désactivé requis pour voir le site)

	$rprd = prd_products_get($prd,'',0,$published,$cat);
	$rcat = prd_categories_get($cat);

	$prd_row = ria_mysql_fetch_array($rprd);
	$cat_row = ria_mysql_fetch_array($rcat);

	if( !ria_mysql_num_rows($rprd) ){
		return false;
	}else{
		$msg = add_message($sender_firstname, $sender_lastname, "", $sender_email, "", "", $notes, "PRODUCT", $receiver_email, $published, 0, $prd_row['id'], "", "", "", $cat_row['id'], 0, false, true );

		//Si le message n'est pas spammer
		if( $msg ){
			prd_products_set_date_modified( $prd );
			prd_tell_a_friend_send_mail( $msg );
		}
	}

	return true;

}

// \cond onlyria
/**	Cette fonction permet l'envoi et le stockage d'un email de type "Recommander un article à un ami"
 *	@param int $id Obligatoire, Identifiant du message à envoyé
 *	@param bool $published  Facultatif, booléen indiquant si seul les produits publiés peuvent faire l'objet d'un avis ou non
 *	@param $review Facultatif, Identifiant du commentaire à afficher
 *	@param $moderate Facultatif, permet d'envoyer le message avec des liens de gestion (destiné aux admin)
 *	@return bool Retourne true si l'envoi s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function prd_tell_a_friend_send_mail( $id, $published=true, $review=0, $moderate=false ){
	$rmsg = messages_get( 0, '', 0, $id );
	if( !$rmsg || !ria_mysql_num_rows($rmsg) )
		return false;

	$msg = ria_mysql_fetch_array( $rmsg );
	if( $msg['spam_id']!='' )
		return true;

	global $config;

	$rprd = prd_products_get($msg['prd_id'],'',0,$published,$msg['cat_id']);
	$rcat = prd_categories_get($msg['cat_id']);

	$prd = ria_mysql_fetch_array($rprd);
	$cat = ria_mysql_fetch_array($rcat);

	if( trim($msg['firstname']) || trim($msg['lastname']) ){
		$msg['firstname'] = str_replace( array('/','\\',':','=',';','?'), '', $msg['firstname'] );
		$msg['firstname'] = substr( $msg['firstname'], 0, 75 );
		$msg['lastname'] = str_replace( array('/','\\',':','=',';','?'), '', $msg['lastname'] );
		$msg['lastname'] = substr( $msg['lastname'], 0, 75 );
		$email_from = trim($msg['firstname'].' '.$msg['lastname']).' <'.$msg['email'].'>';
	}else{
		$email_from = $msg['email'];
	}

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get('tip-a-friend');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);

	$email = new Email();
	$email->setSubject( $prd['name'].' sur le site '.$config['site_name'] );
	$email->setFrom( $email_from );

	if( !$moderate ){
		$email->addTo( $msg['receiver_email'] );
	}else{
		$email->addTo( $cfg['bcc'] );
	}

	if( $moderate ){
		$email->addHtml( '
			<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
				<a style="text-decoration: none" href="https://app.fr/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
			</div>
		');
	}
	$email->addHtml( $config['email_html_header'] );

	$email->addParagraph( 'Cet email vous a été envoyé à la demande de <a href="mailto:'.xhtmlentities($email_from).'">'.xhtmlentities($email_from).'</a> depuis le site <a href="'.$config['site_url'].'?utm_source=alertes&utm_medium=email&utm_campaign=tell-a-friend">'.$config['site_name'].'</a>.'.( trim($msg['body']) ? ' Voici son message : ' : '') );

	if( trim($msg['body']) ){
		$msg['body'] = html_strip_tags($msg['body']); // Pour les spammeurs
		$msg['body'] = preg_replace( '/\[url[^\]]*\][^\[]+\[\/url\]/i', ' ', $msg['body'] );
		$email->addHorizontalRule();
		$email->addParagraph( nl2br(htmlspecialchars($msg['body'])) );
		$email->addHorizontalRule();
	}

	$descriptif = '<b>'.htmlspecialchars($prd['name']).'</b><br />Référence : '.$prd['ref'];

	// Affichage de l'image 'Promotion'
	$pmt = prc_promotions_get( $prd['id'] );
	if( is_array($pmt) && sizeof($pmt) ){
		$descriptif .= '<br /><img src="'.$config['site_url'].'/images/products/promotion.gif" width="57" height="11" alt="Promotion" title="Promotion" />';
	}
	// Affichage de l'image 'Nouveau'
	if( $prd['new'] ){
		$descriptif .= '<br /><img src="'.$config['site_url'].'/images/products/nouveau.gif" width="47" height="11" alt="Nouveau produit" title="Nouveau produit" />';
	}
	// Affichage de l'image 'Déstockage'
	if( $prd['sleep'] && $prd['stock']>0 ){
		$descriptif .= '<br /><img src="'.$config['site_url'].'/images/products/destockage.gif" width="61" height="13" alt="Déstockage" title="Déstockage" />';
	}

	$descriptif .= '<br /><br />'.$prd['desc'];

	$descriptif .= '<br /><br /><a href="'.$config['site_url'].$prd['url_alias'].'?utm_source=alertes&utm_medium=email&utm_campaign=tell-a-friend">En savoir plus sur cet article</a>';

	$email->openTable('auto',0);
	$email->openTableRow();
	if( $prd['img_id'] ){
		$size = $config['img_sizes']['medium'];
		$email->addCell( '<a href="'.$config['site_url'].$prd['url_alias'].'?utm_source=alertes&utm_medium=email&utm_campaign=tell-a-friend"><img src="'.$config['site_url'].'/images/products/'.$size['width'].'x'.$size['height'].'/'.$prd['img_id'].'.jpg" alt="Photographie du produit '.htmlspecialchars($prd['name']).'" border="0" /></a>' );
	}else{
		$email->addCell( '<a href="'.$config['site_url'].$prd['url_alias'].'?utm_source=alertes&utm_medium=email&utm_campaign=tell-a-friend"><img src="'.$config['site_url'].'/images/products/150x150/default.gif" width="150" height="150" alt="Image Indisponible" title="Image Indisponible" border="0" /></a>' );
	}
	$email->addCell( $descriptif );
	$email->closeTableRow();
	$email->closeTable();

	$email->addHtml( $config['email_html_footer'] );

	if( $moderate ){
		$email->addHtml( '
			<div style="width:480px;background-color: #FFFBCC; border: solid 1px #E6DB55; margin-bottom: 10px; padding: 3px;text-align:right;">
				<a style="text-decoration: none" href="https://app.fr/admin/moderation/index.php?cnt='.$msg['id'].'&amp;action=signal-spam"><input type="button" name="signal-spam" id="signal-spam" value="Signaler comme spam" /></a>
			</div>
		');
	}

	$res = $email->send();

	// Envoyer aux admins
	if( !$moderate ){
		$res = prd_tell_a_friend_send_mail( $id, $published, $review, true );
	}
	return $res;
}
// \endcond

/// @}
