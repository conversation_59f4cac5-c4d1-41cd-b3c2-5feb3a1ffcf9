<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/credentials/v1/common.proto

namespace Google\Iam\Credentials\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>google.iam.credentials.v1.SignBlobResponse</code>
 */
class SignBlobResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * The ID of the key used to sign the blob.
     *
     * Generated from protobuf field <code>string key_id = 1;</code>
     */
    private $key_id = '';
    /**
     * The signed blob.
     *
     * Generated from protobuf field <code>bytes signed_blob = 4;</code>
     */
    private $signed_blob = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $key_id
     *           The ID of the key used to sign the blob.
     *     @type string $signed_blob
     *           The signed blob.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Iam\Credentials\V1\Common::initOnce();
        parent::__construct($data);
    }

    /**
     * The ID of the key used to sign the blob.
     *
     * Generated from protobuf field <code>string key_id = 1;</code>
     * @return string
     */
    public function getKeyId()
    {
        return $this->key_id;
    }

    /**
     * The ID of the key used to sign the blob.
     *
     * Generated from protobuf field <code>string key_id = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setKeyId($var)
    {
        GPBUtil::checkString($var, True);
        $this->key_id = $var;

        return $this;
    }

    /**
     * The signed blob.
     *
     * Generated from protobuf field <code>bytes signed_blob = 4;</code>
     * @return string
     */
    public function getSignedBlob()
    {
        return $this->signed_blob;
    }

    /**
     * The signed blob.
     *
     * Generated from protobuf field <code>bytes signed_blob = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setSignedBlob($var)
    {
        GPBUtil::checkString($var, False);
        $this->signed_blob = $var;

        return $this;
    }

}

