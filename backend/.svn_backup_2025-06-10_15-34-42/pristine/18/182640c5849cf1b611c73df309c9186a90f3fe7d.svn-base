<?php
/** 
 * \defgroup api-users-bank Informations bancaires principale 
 * \ingroup crm
 * @{	
 * \page api-users-bank-upd Mise à jour 
 *
 * Cette fonction assigne des informations bancaires comme étant celles principales d'un client.
 *
 *		\code
 *			PUT /users/bank/
 *		\endcode
 *	
 * @param int $usr_id Obligatoire, Identifiant de l'utilisateur.
 * @param $bnk_id Obligatoire, Identifiant des informations bancaires
 *	
 * @return true si la mise à jour s'est déroulée avec succès 
 * @}
*/

switch( $method ){
	case 'upd':
		if( !isset($_REQUEST['usr_id'], $_REQUEST['bnk_id']) ){
			throw new Exception('Paramètres incomplet');
		}


		if( gu_users_set_bank_details_main( $_REQUEST['usr_id'], $_REQUEST['bnk_id'] > 0 ? $_REQUEST['bnk_id'] : null) ){
			$result = true;
		}

		break;
}
