function confirmErratumsDelList(){
	return window.confirm(erratumconfirmSuppressionMultiple);
}
function erratumsCancelEdit(){
	// Annule les modifications sur un erratum (en retournant à la liste des erratums)
	window.location.href = 'index.php';
	return false;
}
function erratumsConfirmDel(){
	return window.confirm(erratumconfirmSuppression);
}
function erratumsValidForm(frm){
	if( !trim(frm.prd_ref.value) ){
		alert(erratumAlertRefProduit);
		frm.prd_ref.focus();
		return false;
	}
	if( !trim(frm.desc.value) ){
		alert(erratumAlertCommetnaire);
		frm.desc.focus();
		return false;
	}
	return true;
}

var errorOpen = false;
function erratumGetPrdName()
{
	$.ajax({
		type: 'GET',
		data: $('#erratum-form').serialize(),
		url: 'search.php', 
		dataType: 'xml', 
		success: function(xml){
			var error = $(xml).find('error');
			if( error.attr('message') ){
				
				if( !errorOpen ){
					$('#error-ref').append(error.attr('message'));
					$("#error-ref").fadeIn().slideDown();
				}else{
					$("#error-ref").fadeOut('normal',function(){$("#error-ref").fadeIn();})
				}
				errorOpen = true;
				
			}else{
				
				$('#prd-name').attr('value',$(xml).find('product').attr('name'));
				if( errorOpen ){
					$("#error-ref").fadeIn().slideUp('normal',function(){$("#error-ref").empty();});
				}
				errorOpen = false;
			}
		}
	});
	return false;
}