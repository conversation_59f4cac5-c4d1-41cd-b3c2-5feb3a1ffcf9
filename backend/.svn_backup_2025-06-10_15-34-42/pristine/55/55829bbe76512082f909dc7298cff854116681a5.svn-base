<?php

class UnitConverter {

	private $value    = null;
	private $baseUnit = false;
	private $units    = array();

	public function __construct( $value=null, $unit=false ){
		//create units array
		$this->defineUnits();
		//unit optional
		if( !is_null( $value ) ){
			//set from unit
			$this->from( $value, $unit );
		}
	}
	/** Cette fonction permet d'initialisé les unité de valeur */
	function defineUnits(){
		$this->units = array(
			///////Units Of Length///////
			"m"     => array( "base" => "m", "conversion" => 1 ),
			//meter - base unit for distance
			"km"    => array( "base" => "m", "conversion" => 1000 ),
			//kilometer
			"dm"    => array( "base" => "m", "conversion" => 0.1 ),
			//decimeter
			"cm"    => array( "base" => "m", "conversion" => 0.01 ),
			//centimeter
			"mm"    => array( "base" => "m", "conversion" => 0.001 ),
			//parsec
			"micro"	=> array( "base" => "m", "conversion" => 0.000001 ),
			//micrometer

			///////Units Of Volume///////
			"l"     => array( "base" => "l", "conversion" => 1 ),
			//litre - base unit for volume
			"ml"    => array( "base" => "l", "conversion" => 0.001 ),
			//mililitre
			"cl"    => array( "base" => "l", "conversion" => 0.0001 ),
			//centilitre
			"m3"    => array( "base" => "l", "conversion" => 1 ),
			//meters cubed
			"pt"    => array( "base" => "l", "conversion" => 0.56826125 ),
			//pint
			"gal"   => array( "base" => "l", "conversion" => 4.405 ),
			//gallon

			///////Units Of Weight///////
			"kg"    => array( "base" => "kg", "conversion" => 1 ),
			//kilogram - base unit for weight
			"g"     => array( "base" => "kg", "conversion" => 0.001 ),
			//gram
			"gr"	=> array( "base" => "kg", "convertion" => 0.00000006483),
			// grain
			"mg"    => array( "base" => "kg", "conversion" => 0.000001 ),
			//miligram
			"t"     => array( "base" => "kg", "conversion" => 1000 ),
			//metric tonne
			//////Units Of Speed///////
			"mps"   => array( "base" => "mps", "conversion" => 1 ),
			//meter per seond - base unit for speed
			"kph"   => array( "base" => "mps", "conversion" => 0.44704 ),
			//kilometer per hour
			"mph"   => array( "base" => "mps", "conversion" => 0.277778 ),
			//kilometer per hour
			///////Units Of Time///////
			"s"     => array( "base" => "s", "conversion" => 1 ),
			//second - base unit for time
			"year"  => array( "base" => "s", "conversion" => 31536000 ),
			//year - standard year
			"month" => array( "base" => "s", "conversion" => 18748800 ),
			//month - 31 days
			"week"  => array( "base" => "s", "conversion" => 604800 ),
			//week
			"day"   => array( "base" => "s", "conversion" => 86400 ),
			//day
			"hr"    => array( "base" => "s", "conversion" => 3600 ),
			//hour
			"min"   => array( "base" => "s", "conversion" => 30 ),
			//minute
		);
	}

	/** Cette fonction permet d'initailisé la valeur a convertir a l'unité de base
	 * @param integer|string $value la valeur a convertir
	 * @param string $unit l'unité de la valeur
	 *
	 * @return void Cette fonction initialise $this->value
	 * @throws Exception
	 */
	public function from( $value, $unit ){
		// vérification si la valeur n'est pas null
		if( is_null( $value ) ){
			throw new Exception( "La valeur est null" );
		}
		if( $unit ){
			// vérification si l'unité existe
			if( array_key_exists( $unit, $this->units ) ){
				$unitLookup = $this->units[$unit];
				//on convertie a l'unité de base
				$this->baseUnit = $unitLookup["base"];
				$this->value = $value * $unitLookup["conversion"];
			}else{
				throw new Exception( "L'unité n'existe pas" );
			}
		}else{
			$this->value = $value;
		}
	}

	/** Cette fonction permet de convertir la valeur a une autre unité
	 *	@param $unit l'unité a laquel convertir
	 *	@param $decimals le nombre de chiffre après la virgule
	 *	@param $round si on arrondie la valeur ou non
	 *
	 *	@return la valeur convertie
	 */
	public function to( $unit, $decimals = null, $round = true ){
		//Vérifiactionsi la valeur est initialisé
		if( is_null( $this->value ) ){
			throw new Exception( "La valeur a convertir n'est pas initialisé" );
		}
		//Vérification si l'unité est passé en paramètre
		if( !$unit ){
			throw new Exception( "Unit n'est pas passer en paramètre" );
		}

		//Vérifiaction si l'unité existe
		if( array_key_exists( $unit, $this->units ) ){
			$unitLookup = $this->units[$unit];
			$result = 0;
			// On vérifie si l'unité a convertir possède la même unité de base
			if( $this->baseUnit ){
				if( $unitLookup["base"] != $this->baseUnit ){
					throw new Exception( "Ne peux pas convertir entre des unité de différent type" );
				}
			}else{
				$this->baseUnit = $unitLookup["base"];
			}
			// On calcul la convertion
			$result = $this->value / $unitLookup["conversion"];

			// On réalise des précision sur le résultat
			if( !is_null( $decimals ) ){
				if( $round ){
					//round to the specifidd number of decimals
					$result = round( $result, $decimals );
				}else{
					//truncate to the nearest number of decimals
					$shifter = $decimals ? pow( 10, $decimals ) : 1;
					$result = floor( $result * $shifter ) / $shifter;
				}
			}

			return $result;
		}else{
			throw new Exception( "L'unité n'existe pas" );
		}
	}
}