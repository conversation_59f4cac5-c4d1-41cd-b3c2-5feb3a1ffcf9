$(document).ready(function() {
	if( typeof $('#cms-categories') != 'undefined' ){
		riaSortable.create({
			'table'		:	$('#cms-categories'),
			'url'		:	'/admin/ajax/cms/ajax-position-update.php'
		});
	}

	if( typeof $('#cms-categories.all-parents') != 'undefined' ){
		riaSortable.create({
			'table'		:	$('#cms-categories.all-parents'),
			'rowPrefix'	:	'category-',
			'url'		:	'/admin/ajax/cms/ajax-position-update.php'
		});
	}

	if( typeof $('.ria-checklist-cms') != 'undefined' ){
		riaSortable.create({
			'table'	:	$('.ria-checklist-cms'),
			'url'	:	'/admin/ajax/cms/ajax-position-update.php'
		});
	}

	if( typeof $('#tbody-rev input[type="checkbox"]') != 'undefined' ){
		$('#tbody-rev input[type="checkbox"]').each(function() {
			$(this).click(function() { return revCheckAll(); });
		});
	}

	if( typeof $('#revCheckAll') != 'undefined' ){
		$('#revCheckAll').click(function() {
			if (! checkCascade) return false;
			checkCascade = false;
			var checked = $(this).attr('checked');
			$('#tbody-rev input[type="checkbox"]').each(function() { if ($(this).attr('checked') != checked) $(this).click(); });
			checkCascade = true;
			return true;
		});
	}
	
	if( typeof $('.popup-cms') != 'undefined' && $('.popup-cms').length ){
		$('.popup-cms').fancybox({
			'padding': 0
		});
	}
	
	// restore
	if( typeof $('a.ver-restore') != 'undefined' ){
		$('a.ver-restore').each(function() {
			var id = ((new RegExp('^ver-([0-9]+)-restore$')).exec($(this).attr('id')))[1];
			$(this).click(function() {
				return (confirm(cmsConfirmRestaurer+" : \n"+'« '+$('#ver-'+id+'-title').text()+' » ?'));
			});
		});
	}

	if( typeof Shadowbox != 'undefined' ){
		Shadowbox.init();
	}
	
	if( $('input[name="tabReferencement"]').hasClass('selected') ){
		$('#tag_desc').riametas({ padding: true, type : "desc" });
		$('#tag_title').riametas({ padding: true, type : "title" });
		$('#tag_title_ref').riametas({ type : "title" });
		$('#tag_desc_ref').riametas({ type : "desc" });
	}
}).delegate( // Lien permettant de visualiser les révisions (cms)
	'.js-open-revision', 'click', function(e){
	e.preventDefault();
	var url = $(this).attr('href');
	displayPopup($(this).text(), '', url, '', 800, 800
);
});

function supprimerimage(img, cat, website){
	$. ajax({
		type: "POST",
		url: '/admin/tools/cms/delete_img.php',
		data: 'img='+img+'&cat='+cat+'&wst='+website,
		async: false,
		success: function(msg){
			eval(msg);
			if(reponse){
				$("#img"+img).fadeOut();
			}
			else alert(cmsAlertSuppressionImage);
		}
	});

	$(".listes").slideUp();
}

var checkCascade = true;
var checkAllCascade = true;

function revCheckAll() {
	if (! checkAllCascade) return false;
	checkAllCascade = false;
	var t = true;
	$('#tbody-rev input[type="checkbox"]').each(function() { t = t && $(this).attr('checked'); });
	if ($('#revCheckAll').attr('checked') != t) $('#revCheckAll').click();
	checkAllCascade = true;
	return true;
}

function previewClick( preview ){
	// Change la couleur de la bordure de l'image
	preview.style.borderColor = preview.style.borderColor == '' ? '#4574BF' : '';
	var chk = preview.getElementsByTagName('input');
	chk[0].checked = preview.style.borderColor!='';
	
	$('.edit-zones')
		.toggle($('.preview input:checked').length == 1)
		.off('click')
		.click(function () {
			var idStart = window.location.search.indexOf('&cat=') + 5;
			var idStop = window.location.search.indexOf('&', idStart);
			var id = decodeURIComponent(window.location.search.substr(idStart, idStop - idStart));
			var $preview = $('.preview input:checked').parents('.preview:eq(0)');
			displayPopup(cmsDiplayPopupZonesCliquables, '', '/admin/documents/images/zones.php?image=' + $preview.attr('id').replace(/^img/, '') + '&cls_id=11&obj_id_0=' + id, null, 756, 602);
		});
	
	//Affichage du bouton suppirmer
	if( $('.preview input:checked').length >= 1 ){
		$('.delimg').show();
	}else{
		$('.delimg').hide();
	}
}

// Permet d'éditer une URL simplifiée
function editUrlCmsRedirection( id, url ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();

	$("#tb-redirection tfoot").addClass('none');
	var html = '';
	html += '	<td headers="url" class="td-url">';
	html += '		<input type="text" name="url-'+id+'" value="'+url+'" />';
	html += '		<input type="hidden" name="url_old-'+id+'" value="'+url+'" />';
	html += '	</td>';
	html += '	<td headers="action" class="td-action">';
	html += '		<input class="action" type="button" name="save-maj-url" value="' + cmsEnregistrer + '" onclick="saveCmsUrlRedirection('+id+')" />';
	html += '		<a class="del button" onclick="canceleditUrlCmsRedirection('+id+', \''+url+'\')">' + cmsAnnuler + '</a>';
	html += '	</td>';
	
	$("#url-"+id).html( html );
}

// Permet d'annulée les modifications faites sur l'URL simplifiée
function canceleditUrlCmsRedirection( id, url ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();

	if( id>0 ){
		var html = '';
		
		html += '	<td headers="url">'+url+'</td>';
		html += '	<td headers="action" class="td-action">';
		html += '		<a class="edit-url button" onclick="editUrlCmsRedirection('+id+', \''+url+'\')">' + periodeEditer + '</a>';
		html += '		<br /><a class="del button" onclick="delUrlCmsRedirection('+id+', \''+url+'\')">' + cmsSupprimer + '</a>';
		html += '	</td>';
		$("#url-"+id).html( html );
		$("#tb-redirection tfoot").addClass('none');
	} else {
		$("#url-0").remove();
		if( $("#tb-redirection tbody tr").length==1 )
			$("#no-url").show();
		
		$("#tb-redirection tfoot").removeClass('none');
	}
}

// Permet de supprimer une URL simplifiée avec un code de redirection 301 seulement
function delUrlCmsRedirection( id, url ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();

	$("#tb-redirection tfoot").removeClass('none');
	if( window.confirm(cmsAlertSuppressionUrl) ){
		$.ajax({
			type: "POST",
			url: '/admin/tools/cms/ajax_redirections.php',
			data: 'del=1&url='+encodeURIComponent(url),
			dataType: 'xml',
			success: function(xml) {
				
				// Si la suppression a réussie
				if( $(xml).find('result').attr('type') == '1' ){
					$("#tb-redirection").before("<div class=\"error-success\">" + cmsSuccesSuppressionUrl + "</div>");
					$("#url-"+id).remove();
				} else{
					// Gestion des messages d'erreur
					$("#tb-redirection").before("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
					return false;
				}
				
			}
		});
		if( $("#tb-redirection tbody tr").length==2 )
			$("#no-url").show();
	}
}

// Permet d'afficher le formulaire d'ajout d'un url simplifiée (elle aura pour code de redirection 301)
var nb = 0;
function addUrlCmsRedirection(count){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();

	$("#no-url").hide();
	$("#url-0").remove();
	$("#tb-redirection tfoot").addClass('none');
	var html = '';
	
	html += '<tr id="url-0">';
	html += '	<td headers="url" class="td-url">';
	html += '		<input type="text" name="url-0" value="" />';
	html += '	</td>';
	html += '	<td headers="action" class="td-action">';
	html += '		<input class="action" type="button" name="add-url" value="' + cmsEnregistrer + '" onclick="saveCmsUrlRedirection(0, '+(count+nb)+')" />';
	html += '		<a class="del button" onclick="canceleditUrlCmsRedirection(0)">' + cmsAnnuler + '</a>';
	html += '	</td>';
	html += '</tr>';
	nb++;
	$("#tb-redirection tbody").append(html);
}

function saveCmsUrlRedirection( id, count ){
	// Retire les messages d'erreur et de succès actuellement affichés
	removeMessages();

	var action = id>0 ? 'save-maj-url=1' : 'add-url=1';
	
	// Requête AJAX d'ajout de redirection
	$.ajax({
		type: "POST",
		url: '/admin/tools/cms/ajax_redirections.php',
		data: action+'&url_id='+id+'&'+$("#formulaire_cms").serialize(),
		dataType: 'xml',
		success: function(xml) {
			// Si l'ajout a réussie
			if( $(xml).find('result').attr('type') == '1' ){
				if( id>0 ){
					canceleditUrlCmsRedirection( id, $("input[name=url-"+id+"]").val() );
					$("#tb-redirection").before("<div class=\"error-success\">" + cmsSuccesMajUrl + "</div>");
				} else {
					$("#tb-redirection tbody").append('<tr id="url-'+count+'"></tr>');
					canceleditUrlCmsRedirection( count, $('#url-0 input[type=text]').val() );
					$("#tb-redirection").before("<div class=\"error-success\">" + cmsSuccesAjoutUrl + "</div>");
					$("#url-0").remove();
				}
			} else{
				// Gestion des messages d'erreur
				$("#tb-redirection").before("<div class=\"error\">"+$(xml).find('error').text()+"</div>");
				return false;
			}
			
		}
	});
}

function addStep(link){
	Shadowbox.open({
		player: 'iframe',
		content: link,
		width: 900
	});
}