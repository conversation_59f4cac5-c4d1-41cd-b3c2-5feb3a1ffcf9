<?php
namespace Riashop\PriceWatching\models\LinearRaised;
/**
 * \ingroup LinearRaisedModel
 */

/** \class prw_states
 * \brief Cette class permet la gestion des opérations CRUD de la table prw_states
 */
class prw_states {
	private static function validString($string) {
		return is_string($string) && trim($string);
	}
	private static function validInteger($int) {
		return is_numeric($int) && $int > 0;
	}
	/**
	 * Cette fonction permet d'ajouter un statut d'offre
	 *
	 * @param sting $name Nom du statut
	 * @return integer|boolean
	 */
	public static function add($name) {
		if (!self::validString($name)) {
			throw new \InvalidArgumentException("Le nom doit être une chaine de caractère");
		}

		global $config;

		$values = array(
			$config['tnt_id'],
			'"'.addslashes($name).'"',
			'now()',
		);

		$insert = '
			insert into prw_states
				(ps_tnt_id, ps_name, ps_date_created)
			values
				('.implode(', ', $values).')
		';

		$r = ria_mysql_query($insert);

		if (!$r) {
			return false;
		}

		return ria_mysql_insert_id();
	}

	private static function getSQL($ids=null) {
		global $config;

		$select = '
			select
				ps_id as id,
				ps_name as name,
				ps_date_created as date_created,
				ps_date_modified as date_modified
			from prw_states
			where ps_tnt_id='.$config['tnt_id'].'
				and ps_date_deleted is null
		';

		if (!is_null($ids) && is_array($ids)) {
			$select .= ' and ps_id in ('.implode(', ', $ids).') ';
		}

		return $select;
	}
	/**
	 * Cette fonction retourne un ou plusieurs statut
	 *
	 * @param integer|array $id
	 * @return resource|boolean
	 */
	public static function get($id) {
		$ids = control_array_integer($id);

		if (!$ids) {
			throw new \InvalidArgumentException("Identifiant ou tableau d'identifiant");
		}

		$select = self::getSQL($ids);

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}
	/**
	 * Retourne tous les statuts pour un tenant
	 *
	 * @return resource|boolean
	 */
	public static function getAll() {

		$select = self::getSQL();

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}
	/**
	 * Cette fonction permet la mise a jours d'un état
	 *
	 * @param integer $id Identifiant de l'état
	 * @param string $name Nom de l'état
	 * @return boolean Retourne true si mise à jours est un succès, false dans le cas contraire
	 */
	public static function update($id, $name) {
		if (!self::validInteger($id)) {
			throw new \InvalidArgumentException("id doit être un entier");
		}
		if (!self::validString($name)) {
			throw new \InvalidArgumentException("Le nom doit être une chaine de caractère");
		}

		global $config;

		$update = '
			update prw_states
				set ps_name = "'.addslashes($name).'"
			where ps_tnt_id='.$config['tnt_id'].'
				and ps_id='.$id.'
				and ps_date_deleted is null
		';

		return ria_mysql_query($update);
	}
	/**
	 * Cette fonction permet la suppression d'un état
	 *
	 * @param integer $id Identifiant de l'offre
	 * @return void
	 */
	public static function delete($id) {
		if (!self::validInteger($id)) {
			throw new \InvalidArgumentException("id doit être un entier");
		}

		global $config;

		$delete = '
			update prw_states
				set ps_date_deleted = now()
			where ps_tnt_id='.$config['tnt_id'].'
				and ps_id='.$id.'
				and ps_date_deleted is null
		';

		return ria_mysql_query($delete);
	}
}