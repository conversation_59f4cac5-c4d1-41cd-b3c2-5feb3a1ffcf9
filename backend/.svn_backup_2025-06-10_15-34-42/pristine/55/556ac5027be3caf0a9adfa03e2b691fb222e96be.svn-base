
/**
	Cette fonction permet de charger ou de recharger le sélectionneur de date pour les champs de type date
*/
$(document).ready(function(){
	$('#riawebsitepicker .selectorview').click(function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
			$('#riadatepicker .selector').hide();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	});
	
	$('#riawebsitepicker .selector a').click( function(){
		window.location.href = '/admin/tools/news/index.php?cat='+$('#cat').val()+'&archived='+$('#archived').val()+'&wst='+$(this).attr('name');
	});
	
	loadDatePicker();
	
	$('[name=str-add]').click(function(){
		$('#news-str').append('<option value="'+ $('#str-search-val').val() +'">'+$('#str-search').val()+'</option>');
		$('#str-search-val').val('');
		$('#str-search').val('');
		return false;
	});
	$('[name=save]').click(function(){
		if( $('#news-str option').length ) {
			$('#news-str option').each(function(){
				$(this).attr('selected','selected');
			});
		}
	});
	
	if( typeof $('#tag_desc') != 'undefined' && $('#tag_desc').length ){
		$('#tag_desc').riametas({ padding: true, type : "desc" });
		$('#tag_title').riametas({ padding: true, type : "title" });
		$('#tag_title_ref').riametas({ type : "title" });
		$('#tag_desc_ref').riametas({ type : "desc" });
	}
		
	$('.check-all').click(function(){
		$($(this).parent().get(0)).find('input[type=checkbox]').attr('checked','checked');
		return false;
	});
	
	$('.uncheck-all').click(function(){
		$($(this).parent().get(0)).find('input[type=checkbox]').removeAttr('checked');
		return false;
	});
	
	// lorsque l'on clique sur "approuver" "désaprouver" ... on change l'etat du commentaire et l'affichage
	$('.checked, .unchecked').click(function(){
		$.ajax({
			url:$(this).attr('href'),
			type:'GET',
			dataType:"xml",
			success: function(html) {
				if($(html).find('reviews').attr('publish')==1){
					$('.tr-'+$(html).find('reviews').attr('id')+' .rcontent .approve').addClass('none');
					$('.tr-'+$(html).find('reviews').attr('id')+' .rcontent .unapprove').removeClass('none');
					$('.tr-'+$(html).find('reviews').attr('id')+' .rstate').html('Publié');
				}
				if($(html).find('reviews').attr('publish')==0){
					$('.tr-'+$(html).find('reviews').attr('id')+' .rcontent .approve').removeClass('none');
					$('.tr-'+$(html).find('reviews').attr('id')+' .rcontent .unapprove').addClass('none');
					$('.tr-'+$(html).find('reviews').attr('id')+' .rstate').html('Réfusé');
				}
			}
		});
		return false;
	});
	
	// lorsqu'on clique sur un bouton "Editer" on affiche le formulaire d'édition
	$('.edit').click(function(){
		remove_form();	
		const comment_id = $(this).attr('name').replace('cmt-', '');
		
		const old_title = $('.tr-'+comment_id+' .ctitle strong').html();
		var old_desc = $('.tr-'+comment_id+' .cdesc').html();
		old_desc = old_desc.replace(/\n/gm,'');
		
		$('.tr-'+comment_id+' .ctitle strong').html('<input type="text" id="ajtitle" name="title" value="'+old_title+'" />');
		$('.tr-'+comment_id+' .cdesc').html('<textarea name="ajdesc" id="ajdesc" rows="5" cols="40"></texarea>');
			$('#ajdesc').html(old_desc.replace(/<br>/gm,'\n'));
			$('#ajdesc').elastic();
		$('.tr-'+comment_id+' .cdesc').after('<div class="button"><input type="button" onclick="save_comment()" class="submit" name="valider" value="' + newsValider + '"/><input type="button" class="submit" onclick="remove_form()" name="annuler" value="' + newsAnnuler + '"/></div>');
		
		return false;
	});

	//lorsqu'on clique sur un bouton "Répondre" on affiche le formulaire 
	$('.response').click(function(){
		remove_form();
		const comment_id = $(this).attr('name').replace('cmt-', '');
		const old_resp = $('.tr-'+comment_id+' .cresp').html();
		
		$('.tr-'+comment_id+' .cresp').html('<textarea name="ajresp" id="ajresp" rows="5" cols="40"></texarea>');
			$('#ajresp').html(old_resp.replace(/<br>/gm,'\n'));
			$('#ajresp').elastic();
		$('.tr-'+comment_id+' .cresp').after('<div class="button"><input type="button" onclick="save_resp()" class="submit" name="valider" value="' + newsValider + '"/><input type="button" class="submit" onclick="remove_form()" name="annuler" value="' + newsAnnuler + '"/></div>');
		
	});
	
	//permet de cocher / décocher les commentaires 
	$('.check_all_comment').click(function(){
		if($(this).attr('checked')){
			$('.scomment').attr('checked','checked');
		}else{
			$('.scomment').attr('checked', '');
		}
	});
	
});
//fonction pour sauvegarder le commmentaire
function save_comment(){
	if(comment_id > 0){
		var new_title = $('#ajtitle').val();
		var new_desc = $('#ajdesc').val();
		$.ajax({
			url:'edit.php',
			type:'POST',
			data: 'save_comment='+comment_id+'&title='+new_title+'&desc='+new_desc ,
			dataType:"xml",
			async: false,
			success: function() {
				old_title = new_title;
				old_desc = new_desc;
				remove_form();
			}
		});
	}
}
//fonction pour sauvegarder la réponse
function save_resp(){
	if(comment_id > 0){
		var new_resp = $('#ajresp').val();
		$.ajax({
			url:'edit.php',
			type:'POST',
			data: 'save_comment='+comment_id+'&resp='+new_resp ,
			dataType:"xml",
			async: false,
			success: function() {
				old_resp = new_resp;
				remove_form();
			}
		});
	}
}
//fonction permettant de retirer les formulaires
function remove_form(){
	if(comment_id > 0){
		if($('#ajtitle').length > 0){
			$('.tr-'+comment_id+' .ctitle strong').html(old_title);
			$('#ajtitle').remove();
		}
		if($('#ajdesc').length > 0){
			$('.tr-'+comment_id+' .cdesc').html(old_desc.replace(/\n/gm,'<br>'));
			$('#ajdesc').remove();
		}
		if($('#ajresp').length > 0){
			$('.tr-'+comment_id+' .cresp').html(old_resp.replace(/\n/gm,'<br>'));
			$('#ajresp').remove();
		}
		$('.tr-'+comment_id+' input[type=button]').each(function(){
			$(this).remove();
		});
	}
}

function newsConfirmDel(){
	return window.confirm(newsConfirmSuppressionActualite);
}
function newsConfirmDelList(){
	return window.confirm(newsConfirmSuppressionActualites);
}
function newsValidForm(frm){
	if( $('.tabstrip .selected').attr('name') != 'tabGeneral' ){
		return true;
	}
	
	if( $.trim($('#name').val())=='' ){
		alert(newsAlertTitreActualite);
		frm.name.focus();
		return false;
	}

	if( frm.elements['publish-opt'][2].checked ){
		if( !trim(frm.elements['publish-date'].value) ){
			alert(newsDatePublicationActualite);
			frm.elements['publish-date'].focus();
			return false;
		}else if( !validDate(frm.elements['publish-date'].value) ){
			alert(newsAlertFormatDatePublicationActualite);
			frm.elements['publish-date'].focus();
			return false;
		}
	}
	return true;
}
function newsCancelEdit(frm){
	if( frm.action.indexOf('archived=1')>-1 )
		window.location.href = 'index.php?archived=1';
	else
		window.location.href = 'index.php';
	return false;
}

function loadDatePicker(){
	// removeMessages();
	
	// Parcours tous les champs de type date
	$('input.datepicker').each(function(){
		var temp = this ;
		
		// Implémente le sélecteur de date sur chacun d'entre eux.
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					$(temp).DatePickerHide();
				}
			}
		});
		
	});	
}
// Gère la sélection/désélection des images
function previewClick(preview){
	// Change la couleur de la bordure de l'image
	preview.style.borderColor = preview.style.borderColor == '' ? '#abb2ff' : '';
	var chk = preview.getElementsByTagName('input');
	chk[0].checked = preview.style.borderColor!='';
	
	$('.edit-zones')
		.toggle($('.preview input:checked').length == 1)
		.off('click')
		.click(function () {
			var idStart = window.location.search.indexOf('&news=') + 7;
			var idStop = window.location.search.indexOf('&', idStart);
			var id = decodeURIComponent(window.location.search.substr(idStart, idStop - idStart));
			var $preview = $('.preview input:checked').parents('.preview:eq(0)');
			displayPopup(newsDefinirCliqueZone, '', '/admin/documents/images/zones.php?image=' + $preview.attr('id').replace(/^img/, '') + '&cls_id=14&obj_id_0=' + id, null, 756, 602);
		});
	$('.edit-alt')
		.toggle($('.preview input:checked').length == 1);
	$('.delimg')
		.toggle($('.preview input:checked').length >= 1);
}

function clickPublishTo(frm){
	frm.elements['to-opt'][1].checked = true;
}

function clickPublishFrom(frm){
	frm.elements['from-opt'][2].checked = true;
}