<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/schema.proto

namespace Google\Cloud\PubSub\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Request for the `ValidateMessage` method.
 *
 * Generated from protobuf message <code>google.pubsub.v1.ValidateMessageRequest</code>
 */
class ValidateMessageRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. The name of the project in which to validate schemas.
     * Format is `projects/{project-id}`.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     */
    private $parent = '';
    /**
     * Message to validate against the provided `schema_spec`.
     *
     * Generated from protobuf field <code>bytes message = 4;</code>
     */
    private $message = '';
    /**
     * The encoding expected for messages
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Encoding encoding = 5;</code>
     */
    private $encoding = 0;
    protected $schema_spec;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $parent
     *           Required. The name of the project in which to validate schemas.
     *           Format is `projects/{project-id}`.
     *     @type string $name
     *           Name of the schema against which to validate.
     *           Format is `projects/{project}/schemas/{schema}`.
     *     @type \Google\Cloud\PubSub\V1\Schema $schema
     *           Ad-hoc schema against which to validate
     *     @type string $message
     *           Message to validate against the provided `schema_spec`.
     *     @type int $encoding
     *           The encoding expected for messages
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Pubsub\V1\Schema::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. The name of the project in which to validate schemas.
     * Format is `projects/{project-id}`.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getParent()
    {
        return $this->parent;
    }

    /**
     * Required. The name of the project in which to validate schemas.
     * Format is `projects/{project-id}`.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setParent($var)
    {
        GPBUtil::checkString($var, True);
        $this->parent = $var;

        return $this;
    }

    /**
     * Name of the schema against which to validate.
     * Format is `projects/{project}/schemas/{schema}`.
     *
     * Generated from protobuf field <code>string name = 2 [(.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getName()
    {
        return $this->readOneof(2);
    }

    public function hasName()
    {
        return $this->hasOneof(2);
    }

    /**
     * Name of the schema against which to validate.
     * Format is `projects/{project}/schemas/{schema}`.
     *
     * Generated from protobuf field <code>string name = 2 [(.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->writeOneof(2, $var);

        return $this;
    }

    /**
     * Ad-hoc schema against which to validate
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Schema schema = 3;</code>
     * @return \Google\Cloud\PubSub\V1\Schema|null
     */
    public function getSchema()
    {
        return $this->readOneof(3);
    }

    public function hasSchema()
    {
        return $this->hasOneof(3);
    }

    /**
     * Ad-hoc schema against which to validate
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Schema schema = 3;</code>
     * @param \Google\Cloud\PubSub\V1\Schema $var
     * @return $this
     */
    public function setSchema($var)
    {
        GPBUtil::checkMessage($var, \Google\Cloud\PubSub\V1\Schema::class);
        $this->writeOneof(3, $var);

        return $this;
    }

    /**
     * Message to validate against the provided `schema_spec`.
     *
     * Generated from protobuf field <code>bytes message = 4;</code>
     * @return string
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * Message to validate against the provided `schema_spec`.
     *
     * Generated from protobuf field <code>bytes message = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setMessage($var)
    {
        GPBUtil::checkString($var, False);
        $this->message = $var;

        return $this;
    }

    /**
     * The encoding expected for messages
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Encoding encoding = 5;</code>
     * @return int
     */
    public function getEncoding()
    {
        return $this->encoding;
    }

    /**
     * The encoding expected for messages
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Encoding encoding = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setEncoding($var)
    {
        GPBUtil::checkEnum($var, \Google\Cloud\PubSub\V1\Encoding::class);
        $this->encoding = $var;

        return $this;
    }

    /**
     * @return string
     */
    public function getSchemaSpec()
    {
        return $this->whichOneof("schema_spec");
    }

}

