<?php

// \cond onlyria
/** \defgroup model_products_search Recherche des produits
 * 	\ingroup pim_products searchengine
 *	Les fonctions contenus dans ce module servent d'interface avec le moteur de recherche. Elles facilitent
 *	la maintenance de l'index.
 *	@{
 */
// \endcond

// \cond onlyria
/** Ajoute un classement au moteur de recherche.
 *
 * @param int $product_id Obligatoire, Identifiant du produit
 * @param int $category_id Obligatoire, Identifiant de la catégorie
 * @param string $lang Facultatif, Code ISO 639-1 de la langue dans laquelle le produit est indexé
 * @return bool true en cas de succès, false en cas d'échec
 */
function prd_search_results_add( $product_id, $category_id, $lang=false ){
	// Vérifie si le produit et la catégorie existe.
	if( !(prd_products_exists($product_id, false, true) && prd_categories_exists($category_id)) ){
		return false;
	}

	global $config;

	// Si aucune langue n'est passée en argument ou si la langue rensignée n'est pas valide
	// nous utilisons la langue par défaut du site.
	if( !$lang || !in_array($lang, $config['i18n_lng_used']) ){
		$lang = $config['i18n_lng'];
	}

	// Charge les informations nécessaires à l'indexation.
	$item = ria_mysql_query('
		select cat_id,
			if(cat_title!="",cat_title,cat_name) as cat_name,
			prd_id as id,
			prd_ref as ref,
			prd_barcode as barcode,
			prd_no_index as no_index,
			if(prd_title!="",prd_title,prd_name) as title,
			prd_name as name,
			prd_desc as "desc",
			prd_desc_long as "desc-long",
			if(ifnull(cly_url_perso, \'\')=\'\', cly_url_alias, cly_url_perso) as url_alias,
			prd_publish,
			prd_publish_cat,
			prd_sleep,
			if(brd_title!="",brd_title,brd_name) as brd_name,
			cly_keywords as keywords,
			prd_img_id as img_id,
			cat_date_deleted
		from prd_categories
		inner join prd_classify
			on (cat_id=cly_cat_id and cly_tnt_id='.$config['tnt_id'].')
		inner join prd_products
			on (cly_prd_id=prd_id and prd_tnt_id='.$config['tnt_id'].')
		left join prd_brands
			on (prd_brd_id=brd_id and brd_tnt_id='.$config['tnt_id'].')
		where cat_tnt_id='.$config['tnt_id'].'
			and prd_id='.$product_id.'
			and cat_id='.$category_id.'
			and cat_date_deleted is null
	');

	if( !($product = ria_mysql_fetch_array($item)) ){
		return false;
	}

	// Si la langue utilisée n'est pas celle par défaut, nous récupérons les traductions du produit.
	if( strtolower($lang) !== strtolower ($config['i18n_lng']) ){
		$product_t = fld_translates_get(CLS_PRODUCT, $product_id, $lang, $product, array(
			_FLD_PRD_NAME => 'name',
			_FLD_PRD_TITLE => 'title',
			_FLD_PRD_DESC => 'desc',
			_FLD_PRD_DESC_LG => 'desc-long'
		));

		// Assigne les traductions au produit.
		$product['name'] = $product_t['name'];
		$product['title'] = $product_t['title'];
		$product['desc'] = $product_t['desc'];
		$product['desc-long'] = $product_t['desc-long'];
	}

	// Tableau du contenu à indexer.
	$content = array($product['title']);

	if( search_is_field_selected(CLS_PRODUCT, 'prd-category-name') ){
		$content[] = $product['cat_name'];
	}

	// Tableau de référence à indexer.
	$ref = array($product['ref']);

	// Récupère les fournisseurs et index la "référence fournisseur" du produit
	// seulement si les fournisseurs sont configurés pour être indexés.
	$suppliers = array();

	if( search_is_field_selected(CLS_PRODUCT, 'prd-suppliers') ){
		$r_suppliers = prd_suppliers_get($product_id);

		while( $supplier = ria_mysql_fetch_array($r_suppliers) ){
			$suppliers[] = $supplier['barcode'];
			$ref[] = $supplier['ref'];
		}
	}

	$content[] = implode(',', $suppliers);

	// Réindexation des références utilisateurs (si activé).
	if( isset($config['active_index_custom_ref']) && $config['active_index_custom_ref'] ){
		require_once('prd/resellers.inc.php');

		if( $r_refs_user = prd_resellers_get_ref($product_id) ){
			while( $user = ria_mysql_fetch_assoc($r_refs_user) ){
				$ref[] = $user['ref'];
			}
		}
	}

	$ref = implode(' ', $ref);

	// Récupère les références des produits enfants (si activé).
	$children_ref = '';

	if( search_is_field_selected(CLS_PRODUCT, 'prd-children-ref') ){
		$children = ria_mysql_query('
			select prd_ref as ref
			from prd_products as p
			join prd_hierarchy as h
				on (p.prd_tnt_id=h.prd_tnt_id and p.prd_id=h.prd_child_id)
			where p.prd_tnt_id='.$config['tnt_id'].'
				and h.prd_parent_id='.$product_id.'
				and p.prd_date_deleted is null
		');

		if( $children && ria_mysql_num_rows($children) ){
			while( $child = ria_mysql_fetch_array($children) ){
				$children_ref .= $child['ref'].' ';
			}
		}
	}

	if( isset($config['prd_relations_index']) && is_array($config['prd_relations_index']) && count($config['prd_relations_index']) ){
		$r_rel = ria_mysql_query('
			select prd_ref as ref
			from prd_relations
			join prd_products
				on (prd_tnt_id=rel_tnt_id and prd_id=rel_dst_id)
			where rel_tnt_id='.$config['tnt_id'].'
				and rel_src_id='.$product_id.'
				and rel_type_id in ('.implode(', ', $config['prd_relations_index']).')
				and prd_date_deleted is null
		');

		if( $r_rel && ria_mysql_num_rows($r_rel) ){
			while( $rel = ria_mysql_fetch_array($r_rel) ){
				$children_ref .= $rel['ref'].' ';
			}
		}
	}

	$content[] = $children_ref;

	// Détermine si ce contenu est public ou privé.
	$is_publish = $product['prd_publish'] && $product['prd_publish_cat'] && prd_categories_is_published($category_id);

	// Les produits en sommeil et indisponible sont automatiquement retirés des résultats du moteur de recherche.
	if( $is_publish && $product['prd_sleep'] ){
		$dps = prd_deposits_get_main();

		// Tenant 2 = Boero
		if( $config['tnt_id'] == 2 && isset($_SESSION['usr_dps_id']) ){
			$dps = $_SESSION['usr_dps_id'];
		}

		$is_publish = false;

		if( $dps > 0 ){
			$r_d = prd_dps_stocks_get($product_id, $dps);

			if( $r_d && ria_mysql_num_rows($r_d) ){
				$d = ria_mysql_fetch_array($r_d);

				$is_publish = ($d['qte'] - $d['prepa']) > 0;
			}
		}
	}

	if( $product['no_index'] == 1 ){
		$is_publish = false;
	}elseif( $is_publish ){
		if (!prd_products_is_index($product_id)) {
			$is_publish = false;
		}
	}

	// Détermine la section du produit.
	$r_section = prd_categories_parents_get($category_id);
	$section = ria_mysql_num_rows($r_section) ? ria_mysql_result($r_section, 0, 0) : $category_id;

	$description = html_strip_tags($product['desc'].' '.$product['desc-long']);

	// Indexation des champs avancés (si activé).
	$fields = '';

	if( search_is_field_selected(CLS_PRODUCT, 'prd-custom-fields') ){
		$r_fields = fld_fields_get(0, 0, -2, 0, 0, $product_id, null, array(), false, array(), null, CLS_PRODUCT);
		if( $r_fields && ria_mysql_num_rows($r_fields) ){
			while( $field = ria_mysql_fetch_array($r_fields) ){
				$fields .= fld_object_values_get($product_id, $field['id'], $lang, false, true).',';
			}
		}
	}

	$content[] = str_replace(',', ' ', $fields);

	// Indexation des mots-clés (si activé).
	$keywords = array();

	if( search_is_field_selected(CLS_PRODUCT, 'prd-custom-keywords') ){
		$tmp = page_obj_key(CLS_PRODUCT, array($product_id, $category_id), true);
		if( trim($tmp) ){
			$keywords = array_merge(preg_split('/,/i', $tmp), $keywords);
		}
	}

	if( search_is_field_selected(CLS_PRODUCT, 'prd-keywords') ){
		$tmp = page_obj_key(CLS_PRODUCT, array($product_id, $category_id), false);
		if( trim($tmp) ){
			$keywords = array_merge(preg_split('/,/i', $tmp), $keywords);
		}
	}

	$content[] = implode(' ', array_unique($keywords));

	// Indexation du code barre (si activé).
	if( search_is_field_selected(CLS_PRODUCT, 'prd-barcode') ){
		$content[] = $product['barcode'];
	}

	// Indexation de la marque (si activé).
	if( search_is_field_selected(CLS_PRODUCT, 'prd-brand') ){
		$content[] = $product['brd_name'];
	}

	// Indexation du produit par le moteur de recherche.
	$content_id = search_index_content(
		$product['url_alias'],
		'prd',
		($product['title'] . ($product['brd_name'] ? ' - '.$product['brd_name'] : '')),
		$description,
		implode(' ', $content),
		'/admin/catalog/product.php?cat='.$product['cat_id'].'&amp;prd='.$product_id,
		($is_publish ? 1 : 0),
		$product_id,
		$section,
		$ref,
		'',
		$lang
	);

	// Rattache l'identifiant du contenu dans la table "prd_classify" (Rattachement de produits aux catégories).
	// Uniquement pour la langue par défaut.
	if( strtolower($lang) === strtolower($config['i18n_lng']) ){
		ria_mysql_query('
			update prd_classify
			set cly_cnt_id='.$content_id.'
			where cly_tnt_id='.$config['tnt_id'].'
				and cly_prd_id='.$product_id.'
				and cly_cat_id='.$category_id
		);
	}

	// Rattache l'image du produit au contenu.
	search_contents_image_add($content_id, $product['img_id']);

	return true;
}
// \endcond

// \cond onlyria
/**	Met à jour une entrée dans le moteur de recherche
 *
 *	@param int $prd Identifiant du produit.
 *	@param int $cat Identifiant de la catégorie
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function prd_search_results_update( $prd, $cat ){
	try{
		// Index le classement dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_CLASSIFY,
			'obj_id_0' => $prd,
			'obj_id_1' => $cat,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'un contenu dans l'index du moteur de recherche.
 *	@param int $prd Identifiant du produit.
 *	@param int $cat Identifiant de la catégorie
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_search_results_del( $prd, $cat ){
	global $config;

	if( !is_numeric($prd) || !is_numeric($cat) ) return false;

	$prd = ria_mysql_query('select cly_cnt_id from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$cat.' and cly_prd_id='.$prd);
	if( !ria_mysql_num_rows($prd) )
		return false;
	if( $cnt_id = ria_mysql_result($prd,0,0) ){
		$rcnt = ria_mysql_query('
			select cnt_id
			from search_contents
			where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_alt_url = (select cnt_alt_url from search_contents where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$cnt_id.')
			and cnt_lng_code!=\''.strtolower($config['i18n_lng']).'\'
			and cnt_id!='.$cnt_id.'
			');
		if( $rcnt ){
			while( $cnt = ria_mysql_fetch_array($rcnt) ){
				search_index_clean( 'prd', $cnt['cnt_id'] );
			}
		}
		return search_index_clean( 'prd', $cnt_id );
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Publie un résultat du moteur de recherche
 *	@param int $prd Identifiant du produit.
 *	@param int $cat Identifiant de la catégorie
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_search_results_publish( $prd, $cat ){
	global $config;

	if( !is_numeric($prd) || !is_numeric($cat) ) return false;
	if( $cnt_id = ria_mysql_result(ria_mysql_query('select cly_cnt_id from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$cat.' and cly_prd_id='.$prd),0,0) ){
		return search_contents_publish( $cnt_id );
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/** Cette fonction permet de déterminer si un produit doit être afficher ou non dans les résultats des caches de recherches.
 *	De cette façon, la mise à jour des caches se fait aussitôt.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $cat Facultatif, identifiant d'une catégorie dans laquelle le produit est classé
 *	@return bool Retourne true si tout c'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 *	@return bool Retourne false si le produit n'existe pas
 */
function prd_search_results_system( $prd, $cat=0 ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	global $config;

	// Récupère tous les identifiants de contenu pour un produit (les produits sont indexés avec un lien catagorie - produit)
	$rcnt = prd_classify_get(false, $prd, $cat);
	if($rcnt===false ) {
		return false;
	}

	if( $config['prd_deposits']=='use-customer' ){
		$stock = ria_mysql_fetch_array( prd_dps_stocks_get_sum($prd) );
		$orderable = $stock['qte']>0 ? true : false;
	} else {
		$orderable = prd_products_is_available($prd);
	}

	if (ria_mysql_num_rows($rcnt)) {
		$p = ria_mysql_fetch_array(prd_products_get_simple($prd));

		while( $cnt = ria_mysql_fetch_array($rcnt) ){
			// Si le produit est en sommeil et devient indisponible, on le retir des résultats de cache de recherche
			if( prd_products_get_sleep($prd) && !$orderable ){
				if( !search_contents_unpublish_system($cnt['cnt']) )
					return false;
			}elseif( $p['publish'] && $p['publish_cat'] && prd_categories_is_published($cnt['cat']) ){
				if( !search_contents_publish($cnt['cnt']) || !search_contents_publish_system($cnt['cnt']) )
					return false;
			}else{
				if( !search_contents_unpublish($cnt['cnt']) )
					return false;
			}
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Dépublie un résultat du moteur de recherche
 *	@param int $prd Obligatoire, Identifiant du produit.
 *	@param int $cat Facultatif, Identifiant de la catégorie
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_search_results_unpublish( $prd, $cat=0 ){
	global $config;

	if( !is_numeric($prd) || !is_numeric($cat) ) return false;

	// Retir ou remet le produit dans les caches de recherche
	prd_search_results_system($prd);

	$sql = 'select cly_cnt_id from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$prd;
	if( $cat>0 ){
		$sql .= ' and cly_cat_id='.$cat;
	}

	if( $cnt_id = ria_mysql_result(ria_mysql_query($sql),0,0) ){
		return search_contents_unpublish( $cnt_id );
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/** Retire le produit de l'index de recherche de la base de données
 *
 *	@param int $prd Identifiant du produit à supprimer de l'index
 *
 */
function prd_products_index_clean( $prd ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;

	$results = ria_mysql_query('select cly_cnt_id as id from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$prd);
	while( $r = ria_mysql_fetch_array($results) ){

		$rcnt = ria_mysql_query('
			select cnt_id
			from search_contents
			where cnt_tnt_id='.$config['tnt_id'].'
			and cnt_alt_url = (select cnt_alt_url from search_contents where cnt_tnt_id='.$config['tnt_id'].' and cnt_id='.$r['id'].')
			and cnt_lng_code!=\''.strtolower($config['i18n_lng']).'\'
			and cnt_id!='.$r['id'].'
			');

		if( $rcnt ){

			while( $cnt = ria_mysql_fetch_array($rcnt) ){
				search_index_clean( 'prd', $cnt['cnt_id'] );
			}
		}

		if( $r['id'] )
			search_index_clean( 'prd', $r['id'] );
	}
}
// \endcond

// \cond onlyria
/** Reconstruit l'index du moteur de recherche pour tous les produits du catalogue, tous les produits d'une catégorie donnée ou pour un produit unique.
 *
 * @param int $prd Optionnel, identifiant du produit à réindexer. Si ce paramètre n'est pas fourni, tous les produits sont réindexés (très long).
 * @param string $lng Optionnel, code ISO 639-1 de la langue, par défaut les produits sont réindexé dans toutes les langues
 * @param int $cat Optionnel, identifiant d'une catégorie pour laquelle on souhaite réindexer un ou plusieurs produits
 * @param bool $recurs Optionnel, détermine si la recherche à partir de $cat est récursive (False par défaut). Sans effet si $prd est spécifié ou $cat non spécifié
 * @return bool true en cas de succès, false en cas d'échec
 */
function prd_products_index_rebuild( $prd=0, $lng=false, $cat=0, $recurs=false ){
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? array($lng) : $config['i18n_lng_used'];

	if( !is_numeric($prd) || $prd < 0 || !is_numeric($cat) || $cat < 0 ){
		return false;
	}

	$sql = '
		select cly_prd_id as prd, cat_id as cat
		from prd_classify
		join prd_categories on (cly_cat_id=cat_id and cly_tnt_id=cat_tnt_id)
		join prd_products on (prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id)
		where cly_tnt_id='.$config['tnt_id'].'
			and prd_date_deleted is null
			and cat_date_deleted is null
	';

	if( $prd != 0 ){
		$sql .= ' and cly_prd_id='.$prd;
	}

	if( $cat != 0 ){
		$sql .= ' and cat_id='.$cat;
	}

	// Pas de récursivité si le produit est spécifié ou la catégorie non spécifiée.
	if( $recurs && !$prd && $cat ){
		$sql .= '
			union
			select cly_prd_id as prd, c.cat_id as cat
			from prd_classify
			join prd_categories as c on (cly_cat_id=c.cat_id and cly_tnt_id=c.cat_tnt_id)
			join prd_products on (prd_tnt_id=cly_tnt_id and prd_id=cly_prd_id)
			join prd_cat_hierarchy as h on (cly_cat_id=h.cat_child_id and cly_tnt_id=h.cat_tnt_id)
			where cly_tnt_id='.$config['tnt_id'].'
				and prd_date_deleted is null
				and h.cat_parent_id='.$cat.'
				and c.cat_date_deleted is null
		';
	}

	$r_products = ria_mysql_query($sql);

	if( !$r_products ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
		}

		return false;
	}

	while( $product = ria_mysql_fetch_array($r_products) ){
		// Retire ou remet le produit dans les caches de recherche.
		prd_search_results_system($product['prd']);

		// Met à null la date de mise à jour des caches de recherche oâ¹ se trouve le produit.
		prd_products_reset_update_cache($product['prd']);

		foreach( $lng as $l ){
			prd_search_results_add($product['prd'], $product['cat'], $l);
		}
	}

	return true;
}
// \endcond

// \cond onlyria
/** Reconstruit l'index du moteur de recherche pour les produits qui ne seraient pas indexés.
 */
function prd_products_index_complete(){
	global $config;

	$items = ria_mysql_query('
		select cly_prd_id as prd, cly_cat_id as cat
		from prd_classify
		where cly_tnt_id='.$config['tnt_id'].' and cly_cnt_id is null
		');

	while( $r = ria_mysql_fetch_array($items) ){
		prd_search_results_add($r['prd'],$r['cat']);
	}
}
// \endcond

// \cond onlyria
/** @} */
// \endcond

