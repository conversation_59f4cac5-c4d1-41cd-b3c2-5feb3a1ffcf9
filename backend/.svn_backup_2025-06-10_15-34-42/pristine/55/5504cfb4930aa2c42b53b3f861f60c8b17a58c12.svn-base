<?php

require_once('orders.inc.php');
require_once('email.inc.php');


/** \defgroup model_ord_installments Gestion des paiements en plusieurs fois (cartes, chèque)
 *	\ingroup oms
 *	Ce module comprend les fonctions nécessaires à la gestion de tout ce qui concerne le paiement, hormis les modules pour chaque TPE bancaire.
 *
 *	Les acomptes sont transversaux à tous les types de document (commande, bon de préparation, bon de livraison, facture), chaque type est représenté par une identifiant (respectivement 1, 2, 3 et 4).
 *	Un acompte est représenté par les informations suivantes :
 *		- Un identifiant unique
 *		- Une pièce de vente liée
 *		- Deux montants : total et restant (requis pour la gestion Atos)
 *		- Date de création, expiration et suppression
 *		- Numéro de transaction liée
 *
 *	@{
 */

/** Cette fonction crée un acompte
 *	@param int $pay Obligatoire, identifiant du moyen de paiement
 *	@param int $type Obligatoire, identifiant du type de pièce
 *	@param string $piece Obligatoire, identifiant de la pièce
 *	@param float $amount Facultatif, montant total de l'acompte
 *	@param float $rest Facultatif, montant restant sur l'acompte
 *	@param string $date Facultatif, date de création de l'acompte
 *	@param $expire Facultatif, délais d'expiration de la transaction
 *	@param $transaction Facultatif, identifiant de la transaction SIPS
 *	@param bool $deleted Facultatif, transaction refusée
 *	@param int $state Facultatif, état de l'accompte
 *	@param bool $is_deadline Facultatif, si la ligne est une ligne d'échéancier ou d'accompte
 *	@param float $amount_percent Facultatif, montant de l'accompte a régler en pourcentage
 *
 *	@return int Identifiant de l'acompte inséré en cas de succès
 *	@return bool False en cas d'échec
 *
 */
function ord_installments_add( $pay, $type, $piece, $amount=0, $rest=0, $date=false, $expire=null, $transaction=null , $deleted=false, $state=0, $is_deadline=null, $amount_percent=null ){
	global $config;

	if( !ord_payment_types_exists( $pay ) ) return false;
	if( !is_numeric( str_replace( array(',',' '), array('.', ''), $amount) ) ) return false;
	if( !is_numeric( str_replace( array(',',' '), array('.', ''), $rest) ) ) return false;
	if( $date!=false && !isdateheure($date) ) return false;
	if( $expire!=null && $expire<0 ) return false;
	if( !is_numeric($state) || $state<0 ) return false;
	if( $transaction!=null && !is_numeric( $transaction ) ) return false;

	if( $amount<0 || $rest<0 || $rest>$amount ) return false;

	switch( $type ){
		case 1: // commande client
			if( !ord_orders_exists( $piece ) ) return false;
			break;
		case 2: // PL client
			if( !ord_pl_exists( $piece ) ) return false;
			break;
		case 3: // BL client
			if( !ord_bl_exists( $piece ) ) return false;
			break;
		case 4: // facture client
			if( !ord_invoices_exists( $piece ) ) return false;
			break;
		default: // type non reconnu
			return false;
			break;
	}

	$fields = array();
	$values = array();

	$fields[] = 'itm_pay_id';
	$values[] = $pay;

	if( $is_deadline !== null ){
		$fields[] = 'itm_is_deadline';
		$values[] = $is_deadline ? 1 : 0;
	}

	$fields[] = 'itm_type_id';
	$values[] = $type;

	$fields[] = 'itm_piece_id';
	$values[] = $piece;

	$fields[] = 'itm_amount_total';
	$values[] = str_replace( array(',',' '), array('.', ''), $amount);

	$fields[] = 'itm_amount_rest';
	$values[] = str_replace( array(',',' '), array('.', ''), $rest);

	if( $amount_percent && is_numeric($amount_percent)){
		$fields[] = 'itm_amount_percent';
		$values[] = str_replace( array(',',' '), array('.', ''), $amount_percent);
	}

	$fields[] = 'itm_date_created';
	if( $date==false )
		$values[] = 'now()';
	else
		$values[] = '\''.dateheureparse( $date ).'\'';

	if( $expire!=null ){
		$fields[] = 'itm_date_expired';

		if( isdateheure($expire) ){
			$values[] = '\''.dateheureparse( $expire ).'\'';
		}else{
			$values[] = 'date_add(now(),INTERVAL '.$expire.' DAY)';
		}
	}

	if( $transaction!=null ){
		$fields[] = 'itm_transaction_id';
		$values[] = $transaction;
	}

	if($deleted ){
		$fields[] = 'itm_date_deleted';
		$values[] = 'now()';
	}

	$fields[] = 'itm_state';
	$values[] = $state;

	$sql = 'insert into ord_installments (itm_tnt_id,'.implode( ',', $fields ).') values ('.$config['tnt_id'].','.implode( ',', $values ).')';

	if( ria_mysql_query( $sql ) )
		return ria_mysql_insert_id();
	else
		return false;
}

// \cond onlyria
/** Cette fonction met à jour un acompte
 *
 *	Cette fonction ne permet pas la mise à jour de la pièce rattachée à l'acompte ( voir ord_installments_upd_piece ).
 *
 *	@param int $itm Obligatoire, identifiant de l'acompte
 *	@param $pay Facultatif, identifiant du moyen de paiement ( n'est pas modifié si 0 )
 *	@param float $amount Facultatif, montant total de l'acompte ( n'est pas modifié si False )
 *	@param $rest Facultatif, montant restant sur l'acompte ( n'est pas modifié si False )
 *	@param $date Facultatif, date de création de l'acompte ( n'est pas modifiée si False )
 *	@param $expire Facultatif, date d'expiration de l'acompte ( n'est pas modifiée si False, utiliser NULL pour valoriser explicitement que l'acompte n'expire pas )
 *	@param $transaction Facultatif, identifiant de la transaction SIPS ( n'est pas modifié si 0, utiliser NULL pour valoriser explicitement que l'acompte n'a pas d'ID de transaction )
 *	@param $state Facultatif, état de l'accompte
 *	@param $is_deadline Facultatif, si la ligne est un accompte ou une échéance
 *	@param $amount_percent Facultatif, montant en % de l'accompte
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 *
 */
function ord_installments_upd( $itm, $pay=0, $amount=false, $rest=false, $date=false, $expire=false, $transaction=0, $state=null, $is_deadline=null, $amount_percent=null ){
	global $config;
	// contrôles
	if( !ord_installments_exists( $itm ) ) return false;
	if( $pay!=0 && !ord_payment_types_exists( $pay ) ) return false;
	if( $amount!=false && ( !is_numeric( $amount ) || $amount<0 ) ) return false;
	if( $rest!=false && ( !is_numeric( $rest ) || $rest<0 ) ) return false;
	if( $transaction!=null && !is_numeric( $transaction ) ) return false;
	if( $date!=false && !isdateheure($date) ) return false;
	if( $expire!=null && $expire!=false && !isdateheure($expire) ) return false;
	if( $state!==null && (!is_numeric($state) || $state<0) ) return false;
	if( $amount_percent!==null && !is_numeric($amount_percent) ) return false;

	// vérification de la présence d'une modification
	if( $pay==0 && $amount==false && $rest==false && $date==false && $expire==false && $transaction==0 )
		return true;

	if( $amount!=false && $rest!=false && $rest>$amount ) return false;

	$statements = array();

	if( $is_deadline !==null ){
		$statements[] = 'itm_is_deadline='.($is_deadline ? '1' : '0');
	}

	if( $state !==null ){
		$statements[] = 'itm_state='.$state;
	}

	if( $pay!=0 )
		$statements[] = 'itm_pay_id='.$pay;

	if( $amount!=false )
		$statements[] = 'itm_amount_total='.str_replace( array(',',' '), array('.',''), $amount );

	if( $amount_percent!=null )
		$statements[] = 'itm_amount_percent='.str_replace( array(',',' '), array('.',''), $amount_percent );

	if( $rest!=false )
		$statements[] = 'itm_amount_rest='.str_replace( array(',',' '), array('.',''), $rest );

	if( $date!=false )
		$statements[] = 'itm_date_created=\''.dateheureparse( $date ).'\'';

	if( $expire!=false )
		$statements[] = 'itm_date_expired='.( $expire==null ? 'NULL' : '\''.dateheureparse( $expire ).'\'' );

	if( $transaction!=0 )
		$statements[] = 'itm_transaction_id='.( $transaction==null ? 'NULL' : $transaction );

	$sql = 'update ord_installments set '.implode( ',', $statements );
	$sql .= ' where itm_tnt_id='.$config['tnt_id'].' and itm_id='.$itm;

	return ria_mysql_query( $sql );
}
// \endcond

/** Cette fonction supprime virtuellement un acompte
 *	@param int $itm Obligatoire, identifiant de l'acompte
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 *
 */
function ord_installments_del( $itm ){
	global $config;
	if( !ord_installments_exists( $itm ) ) return false;

	$sql = 'update ord_installments set itm_date_deleted=now() where itm_tnt_id='.$config['tnt_id'].' and itm_id='.$itm;
	return ria_mysql_query( $sql );
}

/**	Cette fonction permet de récupérer les informations sur un acompte
 *	@param int $itm Facultatif, identifiant de l'acompte
 *	@param $type Facultatif, identifiant du type de pièce à laquelle l'acompte est rattaché
 *	@param $piece Facultatif, identifiant de la pièce à laquelle l'acompte est rattaché
 *	@param $usable Facultatif, filtre uniquement les acomptes utilisables (non expirés et montant restant > 0)
 *	@param $deleted Facultatif, si true, retire le filtre sur la suppression virtuelle
 *
 *	@return bool False en cas d'erreur
 *	@return resource un résultat de requête mySQL contenant les champs suivants :
 *		- id, identifiant de l'acompte
 *		- deleted, détermine si l'acompte est supprimé ou non
 *		- type, identifiant du type de pièce auquel l'acompte est rattaché
 *		- piece, pièce à laquelle l'acompte est rattaché
 *		- pay, identifiant du moyen de paiement de l'acompte
 *		- total, motant total de l'acompte
 *		- rest, montant non facturé
 *		- spend, montant facturé
 *		- transaction, identifiant de la transaction SPIS, peut être NULL
 *		- date, date de création de l'acompte
 *		- expire, date d'expiration de l'acompte
 *		- form_date_created : date de création au format prêt pour affichage
 *		- form_date_expired : date d'expiration au format prêt pour affichage
 *		- state : état de l'accompte
 *		- is_deadline : si Oui ou Non il s'agit d'une ligne d'échéancier
 *		- amount_percent : % de l'échéance
 *
 */
function ord_installments_get( $itm=0, $type=0, $piece=0, $usable=false, $deleted=false ){
	if( $itm!=0 && !ord_installments_exists( $itm ) ){
		return false;
	}
	if( !is_numeric($type) || $type<0 ){
		return false;
	}
	if( !is_numeric($piece) || $piece<0 ){
		return false;
	}
	global $config;

	$sql = '
		select
			itm_id as id, ( itm_date_deleted is not null ) as deleted, itm_type_id as type, itm_piece_id as piece, itm_pay_id as pay, itm_amount_total as total,
			itm_amount_rest as rest, (itm_amount_total - itm_amount_rest) as spend, itm_transaction_id as transaction, itm_date_created as date, date_format(itm_date_created,"%d/%m/%Y à %H:%i") as form_date_created, itm_date_expired as expire, date_format(itm_date_expired,"%d/%m/%Y à %H:%i") as form_date_expired, itm_state as state, itm_is_deadline as is_deadline, itm_amount_percent as amount_percent
		from
			ord_installments
		where itm_tnt_id='.$config['tnt_id'].'
	';
	if( $itm!=0 ){
		$sql .= ' and itm_id='.$itm;
	}

	if( $type!=0 ){
		$sql .= ' and itm_type_id='.$type;
	}

	if( $piece!=0 ){
		$sql .= ' and itm_piece_id='.$piece;
	}

	if( $deleted!=true ){
		$sql .= ' and itm_date_deleted is null';
	}

	if( $usable==true ){
		$sql .= ' and itm_date_expired is not null and itm_date_expired >= now()';
	}

	return ria_mysql_query( $sql );
}

// \cond onlyria
/** Cette fonction permet de savoir s'il s'agit d'un acompte ou d'un échéancier.
 *	@param $piece Obligatoire, identifiant de la pièce lié aux informations
 *	@return bool True s'il s'agit d'un échéancier, False dans le cas contraire
 */
function ord_installments_is_deadline( $piece ){
	if (!is_numeric($piece) || $piece <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select 1
		from ord_installments
		where itm_tnt_id = '.$config['tnt_id'].'
			and itm_piece_id = '.$piece.'
			and itm_is_deadline = 1
	';

	$res = ria_mysql_query($sql);
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la somme des accomptes sur une pièce
 *	@param $type Obligatoire, identifiant du type d'accompte
 *	@param $piece Obligatoire, identifiant de la pièce
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête mySQL comprenant la colonne suivante :
 *		- accompte : Somme totale des acomptes
 */
function ord_installments_get_sum( $type, $piece ){
	if( !is_numeric($type) || $type<=0 ) return false;
	if( !is_numeric($piece) || $piece<=0 ) return false;
	global $config;

	return ria_mysql_query('select ifnull(sum(itm_amount_total),0) as accompte from ord_installments where itm_tnt_id='.$config['tnt_id'].' and itm_type_id = '.$type.' and itm_piece_id = '.$piece);
}
// \endcond

// \cond onlyria
/** Cette fonction teste l'existence d'un acompte
 *  @param int $itm Obligatoire, identifiant de l'acompte
 *	@param $deleted Falcultatif, si true, ne filtre pas les éléments supprimés virtuellements
 *
 *	@return bool True si l'acompte existe
 *	@return bool False s'il n'existe pas
 *
 */
function ord_installments_exists( $itm, $deleted=false ){
	if( !is_numeric( $itm ) || $itm<=0 ) return false;
	global $config;

	$sql = 'select itm_id from ord_installments where itm_tnt_id='.$config['tnt_id'].' and itm_id='.$itm;

	if( $deleted!=true )
		$sql .= ' and itm_date_deleted is null';

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}
// \endcond

// \cond onlyria
/** Cette fonction teste l'existence d'un échéancier sur une piece
 *  @param $type Obligatoire, identifiant du type de pièce
 * 	@param $piece Obligatoire, identifiant de la pièce
 *
 *	@return bool True si l'acompte existe
 *	@return bool False s'il n'existe pas
 *
 */
function ord_installments_exists_piece( $type, $piece ){
	if (!is_numeric($type) || $type <= 0) {
		return false;
	}

	if (!is_numeric($piece) || $piece <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select itm_id
		from ord_installments
		where itm_tnt_id='.$config['tnt_id']. '
			and itm_type_id = '.$type.'
			and itm_piece_id = '.$piece.'
	';

	$res = ria_mysql_query($sql);
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}
	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour la pièce à laquelle l'acompte est rattaché
 *	@param int $itm Obligatoire, identifiant de l'acompte
 *	@param $type Obligatoire, identifiant du type de pièce
 *	@param $piece Obligatoire, identifiant de la pièce
 *
 * 	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 *
 */
function ord_installments_upd_piece( $itm, $type, $piece ){
	if( !ord_installments_exists( $itm ) ) return false;
	if( !is_numeric($type) ) return false;
	global $config;
	switch( $type ){
		case 1:	// Commande
			if( !ord_orders_exists( $piece ) ) return false;
			break;
		case 2:	// PL
			if( !ord_pl_exists( $piece ) ) return false;
			break;
		case 3:	// BL
			if( !ord_bl_exists( $piece ) ) return false;
			break;
		case 4:	// Facture
			if( !ord_invoices_exists( $piece ) ) return false;
			break;
		default:
			return false;
			break;
	}

	$old_itm = ria_mysql_fetch_array( ord_installments_get( $itm ) );
	if( $old_itm['type'] == $type && $old_itm['piece'] == $piece )
		return true;

	$sql = 'update ord_installments set itm_type_id='.$type.', itm_piece_id='.$piece.' where itm_tnt_id='.$config['tnt_id'].' and itm_id='.$itm;

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le montant restant quand une dépense est effectuée sur l'acompte
 *	@param int $itm Obligatoire, identifiant de l'acompte
 *	@param $spend Obligatoire, montant prélevé
 *	@param $type Obligatoire, identifiant du type de pièce ayant utilisé l'acompte
 *	@param $piece Obligatoire, identifiant de la pièce ayant utilisé l'acompte
 *	@param $has_rel Facultatif, détermine si l'acompte doit être géré en vue d'un reliquat
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function ord_installments_set_spend( $itm, $spend, $type, $piece, $has_rel=true ){
	global $config;
	$spend = str_replace(array(',',' '), array('.',''),$spend);
	if( !ord_installments_exists( $itm ) ) return false;
	if( !is_numeric($spend) || $spend<=0 ) return false;
	if( !is_numeric($piece) || $piece<=0 ) return false;
	if( !is_numeric($type) || $type<1 || $type>4 ) return false;

	$r_itm = ord_installments_get( $itm );
	if( $i = ria_mysql_fetch_array( $r_itm ) ){
		if( ($i['rest'] - $spend) < 0 )
			$spend = $i['rest']; // ce cas de figure ne devrait pas arriver

		// mise à jour de l'acompte standard (montant restant - montant dépensé = nouveau montant restant)
		if( !ria_mysql_query('update ord_installments set itm_amount_rest='.str_replace( array(',',' '), array('.',''), ( $i['rest'] - $spend ) ).' where itm_tnt_id='.$config['tnt_id'].' and itm_id='.$itm) )
			return false;

		// l'acompte provient d'une commande et a été depensé dans un BL (cas le plus fréquent)
		if( $type==3 && $i['type']==1 ){
			// acompte restant
			if( ($i['rest'] - $spend  > 0) && $has_rel!=false ){

				$usr_ref = ''; // référence client
				if( $rusr = ria_mysql_query('select usr_ref from gu_users, ord_orders where ord_tnt_id='.$config['tnt_id'].' and (usr_tnt_id=0 or usr_tnt_id=ord_tnt_id) and usr_id=ord_usr_id and ord_id='.$i['piece']) )
					$usr_ref = ria_mysql_result( $rusr,0,0 );

				$fail = true;
				$attempts = 0;

				while( $fail==true && $attempts<5 ){
					//on recupere l'id de la transaction en fonction du jour
					$installment = ria_mysql_fetch_array( ria_mysql_query('select count(*) as nb from ord_installments where itm_tnt_id='.$config['tnt_id'].' and SUBSTRING(itm_date_created from 1 for 10) = date_format(now(),"%Y-%m-%d")') );
					$new_transaction = $installment['nb']+1;

					// création d'un duplicata
					$buffer = "<service component=\"office\" name=\"duplicate\"><duplicate merchant_id=\"".$config['merchant_id']."\" merchant_country=\"".$config['merchant_country']."\" transaction_id=\"".$new_transaction."\" amount=\"".number_format($i['rest'] - $spend,2,'','')."\" currency_code=\"".$config['money_code']."\" from_transaction_id=\"".$i['transaction']."\" from_payment_date=\"".substr(str_replace('-', '', $i['date']),0,8)."\" capture_mode=\"VALIDATION\" capture_day=\"".$config['delay_expiration']."\" order_id=\"".addslashes($usr_ref)."\" /></service>\n";
					$reponse =  ord_call_atos( $buffer );

					if( $reponse != -1 )
					{
						if( $reponse['response_code'] == 00 )
						{
							$fail = false;

							// création du duplicata dans notre base
							$new_id = ord_installments_add( 1,$i['type'],$i['piece'],number_format(($i['rest'] - $spend)+($i['total']-$i['rest']),2,'.',' '),number_format($i['rest'] - $spend,2,'.',' '),$reponse['transaction_date'],$config['delay_expiration'],$new_transaction );
							if( is_numeric($new_id) && $new_id > 0 ) // l'acompte original est transféré au BL, son montant est celui dépensé, son reste est 0
								ria_mysql_query('update ord_installments set itm_amount_total='.str_replace( array(',',' '),array('.',''),$spend ).', itm_amount_rest=0, itm_type_id='.$type.', itm_piece_id='.$piece.' where itm_tnt_id='.$config['tnt_id'].' and itm_id='.$itm);
						}
					}

					// A chaque échec, on écrit la transaction supprimée
					if( $fail==true ){
						$attempts++;
						ord_installments_add( 1,$i['type'],$i['piece'],number_format(($i['rest'] - $spend)+($i['total']-$i['rest']),2,'.',' '),number_format($i['rest'] - $spend,2,'.',' '),false,$config['delay_expiration'],$new_transaction,true );
					}
				}

				if( $fail==true ){
					$msg = 'Echec, après 5 tentatives, de la duplication de la transaction '.$i['transaction'].' du '.$i['date'].'.';

					$cfg = ria_mysql_fetch_array(cfg_emails_get( 'atos-error' ));

					$emaillog = new Email();
					$emaillog->setSubject( 'Erreur de duplication de transaction' );
					$emaillog->setFrom( '<EMAIL>' );
					$emaillog->addTo( $cfg['to'] );
					$emaillog->addCC( $cfg['cc'] );
					$emaillog->addBcc( $cfg['bcc'] );
					$emaillog->setReplyTo( $cfg['reply-to'] );
					$emaillog->addParagraph( 'Bonjour,' );
					$emaillog->addParagraph( 'Après 5 tentatives, le montant restant de la transaction '.$i['transaction'].' du '.$i['date'].' n\'a pas pu être dupliqué. S\'il y a un reliquat sur cette commande (N° RiaShop '.$i['piece'].'), il ne pourra pas être encaissé.' );
					$emaillog->send();

					error_log( $config['tnt_id']."\t".date('H:i:s')."\t".$msg."\n", 3, '/var/www/sync.fr/logs/error.log' );
				}
			} else {
				// l'acompte a été totalement utilisé
				// on crée une acompte toujours rattaché à la commande dans le cas où l'acompte original avait déjà subit une ponction [impossible à terme]
				if( $i['total']>$spend )
					ord_installments_add( 1,$i['type'],$i['piece'],number_format(($i['total']-$spend),2,'.',' '),0,substr($i['date'],0,10),$config['delay_expiration'] );
				ria_mysql_query('update ord_installments set itm_amount_total='.str_replace( array(',',' '),array('.',''),$spend ).', itm_amount_rest=0, itm_type_id='.$type.', itm_piece_id='.$piece.' where itm_tnt_id='.$config['tnt_id'].' and itm_id='.$itm);
			}
		}

		return true;
	}

	return false;
}
// \endcond

// \cond onlyria
/**	Cette fonction notifie un client lorsque son acompte est insuffisant pour créer une facture et lui propose un lien pour y remédier
 *	@param int $bl Obligatoire, identifiant du bon de livraison à facturer
 *	@param $lessbls Facultatif, liste de BL négatif utilisés comme acomptes (promotions, retours, etc.)
 *	@param $is_test Facultatif, détermine s'sil s'agit d'un test (pas d'envoi au client). La valeur par défaut est False
 *
 *	@return bool True si le mail a bien été envoyé, False en cas d'échec
 */
function ord_installments_notify( $bl, $lessbls=array(), $is_test=false ){
	global $config;

	if( !ord_bl_exists( $bl ) ) return false;
	if( !is_array($lessbls)  ) return false;

	$tbl=ord_bl_get($bl);
	if( !ria_mysql_num_rows($tbl) ) return false;
	$bl = ria_mysql_fetch_array( $tbl  );

	// montant total de la remise (en valeur absolue)
	$discount = 0;

	// Les BL négatifs doivent :
	// - Tous exister
	// - Appartenir au même client
	// - Etre d'un montant total négatif
	if( sizeof($lessbls)>0 ){
		foreach( $lessbls as $lessbl ){
			if( !ord_bl_exists($lessbl) ) return false;

			$rblless = ord_bl_get($lessbl);
			if( !ria_mysql_num_rows($rblless) ) return false;
			$blless = ria_mysql_fetch_array($rblless);

			if( $bl['usr_id']!=$blless['usr_id'] ) return false;
			if( $blless['total_ttc']>=0 ) return false;

			$discount += abs($blless['total_ttc']);
		}
	}

	$user = ria_mysql_fetch_array( gu_users_get( $bl['usr_id'] ) );
	$orders = ord_bl_orders_get( $bl['id'],true );

	$ords = array();
	while($order = ria_mysql_fetch_array($orders))
		$ords[] = ria_mysql_fetch_array(ord_orders_get(0, $order['id']));


	$str_ord = array();
	$date_ord = array();
	foreach( $ords as $ord ){
		$str_ord[] = $ord['piece'];
		if(!in_array($ord['date'], $date_ord))
			$date_ord[] = $ord['date'];
	}

	// création des paramètres de l'url
	$uri_params = array();
	if( sizeof($lessbls)>0 ){
		$chars = array( 'c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z' );
		for( $cpt=0;$cpt<sizeof($lessbls);$cpt++ )
			$uri_params[] = $chars[$cpt].'='.$lessbls[$cpt];
	}

	$cfg = ria_mysql_fetch_array(cfg_emails_get( 'relaunch-pay' ));

	$email = new Email();

	$email->setFrom( $cfg['from'] );
	if( $is_test )
		$email->addTo( '<EMAIL>' );
	else
		$email->addTo( $user['email'] );
	$email->addBcc( $cfg['bcc'] );
	$email->setReplyTo( $cfg['reply-to'] );

	$email->setSubject('Demande de paiement');


	$email->addHtml( $config[ 'email_new_html_header' ] );

	$email->addHtml( '<div style="text-align:center"><img alt="Reglement" title="Reglement" src="'.$config['site_url'].'/images/email/header_reglement.gif"/></div><div style="padding:20px;">' );

	if( sizeof($ords)==1 )
		$email->addParagraph('Votre Numéro de compte client : '.$user['ref'].'<br/>Votre Numéro de commande : '.$ords[0]['piece']);
	elseif( sizeof($ords)>1 )
		$email->addParagraph('Votre Numéro de compte client : '.$user['ref'].'<br/>Vos Numéros de commandes : '.implode(', ',$str_ord));
	else
		$email->addParagraph('Votre Numéro de compte client : '.$user['ref'].'<br/>Votre Numéro de commande : '.$bl['piece']);

	if( isset($user['adr_firstname']) || isset($user['adr_lastname']) )
		$name = $user['adr_firstname'].' '.$user['adr_lastname'];
	else
		$name = $user['society'];

	$email->addParagraph( 'Cher '.$name.',' );

	if(sizeof($ords)==1)
		$email->addParagraph( 'Nous faisons suite à votre commande du '.$ords[0]['date'].' d\'un montant total de '.number_format( $bl['total_ttc'],2,',','' ).' euros.' );
	elseif( sizeof($ords)>1 )
		$email->addParagraph( 'Nous faisons suite à vos commandes du '.implode(', ', $date_ord).' d\'un montant total de '.number_format( $bl['total_ttc'],2,',','' ).' euros.' );
	else
		$email->addParagraph( 'Nous faisons suite à votre commande du '.$bl['date'].' d\'un montant total de '.number_format( $bl['total_ttc'],2,',','' ).' euros.' );

	$amount_rest = ord_installment_get_amount_rest( $bl['id'],$discount );
	if( round($bl['total_ttc']-$amount_rest-$discount,2) > 0 )
		$email->addParagraph( 'Vous avez déjà autorisé un règlement de '.number_format(($bl['total_ttc']-$amount_rest-$discount),2,',','' ).' euros' );

	if( $discount>0 )
		$email->addParagraph( 'Vous disposez d\'une remise totale de '.number_format($discount,2,',','' ).' euros' );

	$email->addParagraph( 'Votre commande présente un <strong>solde de '.(number_format($amount_rest ,2,',','' )).' euros</strong>' );

	$paramsstring = '';
	if( sizeof($uri_params)>0 )
		$paramsstring = '&'.implode( '&',$uri_params );

	$email->addParagraph( 'Afin de finaliser son traitement, nous vous remercions de bien vouloir valider le règlement du complément via le lien suivant :<br/><a href="'.$config['site_url'].'/commander/relance/?b='.$bl['id'].$paramsstring.'">'.$config['site_url'].'/commander/relance/?b='.$bl['id'].$paramsstring.'</a>' );

	$email->addParagraph( 'Cordialement,<br/> L\'équipe Big Ship Accastillage' );

	$email->addParagraph( '<span style="font-size:0.8em; color:#8F8F8F;">Nous sommes en permanence à l\'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n\'hésitez pas à nous contacter: <a href="'.$config['site_url'].'">'.$config['site_url'].'</a></span>' );

	$email->addHtml( '</div><img alt="un réseau de magasin" title="Un réseau de magasin" src="'.$config['site_url'].'/images/email/footer_public.gif"/>' );

	$email->addHtml( $config['email_new_html_footer'] );

	return $email->send();
}
// \endcond

// \cond onlyria
/**	Alias de ord_installments_notify()
 *	@param int $bl Voir ord_installments_notify()
 *	@param $lessbls Optionnel, voir ord_installments_notify()
 *	@param $is_test Optionnel, voir ord_installments_notify()
 *	@return Voir ord_installments_notify()
 */
function ord_installment_notify( $bl, $lessbls=array(), $is_test=false ){
	return ord_installments_notify( $bl, $lessbls, $is_test );
}
// \endcond

// \cond onlyria
/** Cette fonction notifie le client pour un paiement coimplémentaire.
 *	Contrairement à ord_installment_notify, cette fonction se abse sur la commande et non sur le BL.
 *	Elle offre le choix au client entre un paiement manuel et une duplication de la tranasction déjà existante (dans la mesure du possible).
 *
 *	@param int $ord Obligatoire, identifiant de la commande
 *	@param $is_test Facultatif, détermine s'il s'agit d'un test (pas d'envoi au client). La valeur par défaut est False
 *
 *	@return bool True en cas de succès, False sinon
 */
function ord_installments_notify_order( $ord, $is_test=false ){
	global $config;

	if( !ord_orders_exists($ord) ){
		return false;
	}
	ord_orders_update_totals( $ord );

	// commande
	$rord = ord_orders_get( 0,$ord );

	if( !ria_mysql_num_rows($rord) ){
		return false;
	}
	$ord = ria_mysql_fetch_array( $rord );

	// Si la commande est facturée
	if( !is_numeric($ord['state_id']) || $ord['state_id'] == _STATE_INVOICE ){
		return false;
	}

	// client
	$rusr = gu_users_get( $ord['user'] );

	if( !ria_mysql_num_rows($rusr) ){
		return false;
	}
	$user = ria_mysql_fetch_assoc( $rusr );
	$name = ( isset($user['adr_firstname']) || isset($user['adr_lastname']) ? $user['adr_firstname'].' '.$user['adr_lastname'] : $user['society'] );

	// acompte
	$ritm = ord_installments_get(0 , 1, $ord['id'], true);

	if( !ria_mysql_num_rows($ritm) ){
		return false;
	}
	$itm = ria_mysql_fetch_assoc( $ritm );
	$solde = $ord['total_ttc'] - $itm['total'];
	$str_dates = $itm['form_date_created'];

	if( $solde < 0.1 ){
		return false;
	}
	$email = new Email();
	$cfg = ria_mysql_fetch_assoc(cfg_emails_get( 'relaunch-pay' ));
	$email->setFrom( $cfg['from'] );
	$email->addTo( $is_test ? '<EMAIL>' : $user['email'] );
	$email->addBcc( $cfg['bcc'] );
	$email->setReplyTo( $cfg['reply-to'] );

	// header
	$email->setSubject('Demande de paiement');
	$email->addHtml( $config[ 'email_new_html_header' ] );
	$email->addHtml( '<div style="text-align:center"><img alt="Reglement" title="Reglement" src="'.$config['site_url'].'/images/email/header_reglement.gif"/></div><div style="padding:20px;">' );

	// intro
	$email->addParagraph('Votre Numéro de compte client : '.$user['ref'].'<br/>Votre Numéro de commande : '.$ord['piece']);
	$email->addParagraph( 'Cher '.$name.',' );
	$email->addParagraph( 'Nous faisons suite à votre commande du '.$ord['date'].' d\'un montant initial de '.number_format($itm['total'],2,',','').' euros.' );
	$email->addParagraph( 'Après modifications, cette commande présente un montant total de '.number_format($ord['total_ttc'],2,',','').' euros.' );

	$email->addParagraph( 'Lors de la saisie de la commande, vous avez déjà autorisé un règlement de '.number_format($itm['total'],2,',','' ).' euros le '.$str_dates.', par carte bancaire.' );
	$email->addParagraph( 'Votre commande présente un <strong>solde de '.(number_format($solde,2,',','' )).' euros</strong>' );

	$email->addHtml( '
		Afin de finaliser son traitement, nous vous remercions de bien vouloir procéder au règlement du complément via le lien suivant :<br />
		<a href="'.$config['site_url'].'/commander/relance/?type=1&b='.$ord['id'].'">'.$config['site_url'].'/commander/relance/?type=1&b='.$ord['id'].'</a>
	' );

	// footer
	$email->addParagraph( 'Cordialement,<br/> L\'équipe Big Ship Accastillage' );
	$email->addParagraph( '<span style="font-size:0.8em; color:#8F8F8F;">Nous sommes en permanence à l\'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n\'hésitez pas à nous contacter: <a href="'.$config['site_url'].'">'.$config['site_url'].'</a></span>' );
	$email->addHtml( '</div><img alt="un réseau de magasin" title="Un réseau de magasin" src="'.$config['site_url'].'/images/email/footer_public.gif"/>' );
	$email->addHtml( $config['email_new_html_footer'] );

	return $email->send();
}
// \endcond

// \cond onlyria
/**	Alias de ord_installments_notify_order()
 *	@param int $ord Voir ord_installments_notify_order()
 *	@param $is_test Optionnel, voir ord_installments_notify_order()
 *	@return Voir ord_installments_notify_order()
 */
function ord_installment_notify_order( $ord, $is_test=false ){
	return ord_installments_notify_order( $ord, $is_test );
}
// \endcond

// \cond onlyria
/** Cette fonction récupère le montant restant d'un BL à facturer
 *	@param int $bl Obligatoire, identifiant du bl
 *	@param $discount Optionnel, montant d'une éventuelle promotion, à déduire du total
 *
 *	@return Le montant restant TTC
 *	@return bool False en cas d'échec
 */
function ord_installments_get_amount_rest( $bl, $discount=0 ){
	global $config;
	if( !ord_bl_exists( $bl ) ) return false;
	if( !is_numeric($discount) ) return false;
	$total_itm = $total_bl = 0;

	$sql = '
		select
			ifnull(sum(
				case
					when itm_date_expired>=now()
					then itm_amount_total
					else (itm_amount_total - itm_amount_rest)
				end
			),0) as tot
		from
			ord_installments
		where
			itm_tnt_id='.$config['tnt_id'].' and
			itm_date_deleted is null and
			itm_type_id=3 and
			itm_piece_id='.$bl
	;

	// acomptes liés au BL
	$results = ria_mysql_query($sql);
	if( $r = ria_mysql_fetch_array( $results ) )
		$total_itm = $r['tot'];

	// parcours des commandes
	$ords = ord_bl_orders_get( $bl );
	while( $ord = ria_mysql_fetch_array( $ords ) ){


		$sub_tot = 0;

		// montant des lignes du BL pour cette commande
		$sql = '
			select
				ifnull(sum(ifnull(prd_price_ttc, prd_price_ht*prd_tva_rate)*prd_qte),0) as tot
			from
				ord_bl_products
			where
				prd_tnt_id='.$config['tnt_id'].' and
				prd_bl_id='.$bl.' and
				prd_ord_id='.$ord['id']
		;

		// parcours des règlements de la commande
		// A ce stade, on est certain que le montant dépensé de chaque acompte ne l'a pas été pour le BL en cours
		// car dans ce cas, l'acompte serait rattaché au BL via la fonction ord_installments_set_spend
		$results = ria_mysql_query($sql);
		if( $r = ria_mysql_fetch_array( $results ) )
			$sub_tot = $r['tot'];

		$sql = '
			select
				itm_amount_rest
			from
				ord_installments
			where
				itm_tnt_id='.$config['tnt_id'].' and
				itm_piece_id='.$ord['id'].' and
				itm_type_id=1 and
				itm_date_deleted is null and
				itm_date_expired>=now()
		';

		// les règlements sont parcourus jusqu'à recouvrement de sub_tot (ou tous parcourus)
		$rglms = ria_mysql_query($sql);
		while( $rgl = ria_mysql_fetch_array( $rglms ) ){
			if( $sub_tot>0 ){
				if( $sub_tot > $rgl['itm_amount_rest'] ){
					$total_itm = ($total_itm + $rgl['itm_amount_rest']);
					$sub_tot = ($sub_tot - $rgl['itm_amount_rest']);
				}else{
					$total_itm = ($total_itm + $sub_tot);
					$sub_tot = 0;
				}
			}
		}
	}

	$sql = '
		select
			ifnull(sum( ifnull(prd_price_ttc,prd_tva_rate * prd_price_ht) * prd_qte ),0) as tot
		from
			ord_bl_products
		where
			prd_tnt_id='.$config['tnt_id'].' and
			prd_bl_id='.$bl
	;

	// montant total du BL
	$results = ria_mysql_query($sql);
	if( $r = ria_mysql_fetch_array( $results ) )
		$total_bl = $r['tot'];

	// différence entre le montant total et les acomptes
	if( $total_itm > 0)
		$total_bl = ($total_bl - $total_itm);

	if( $discount>0 )
		$total_bl -= $discount;

	if($total_bl < 0)
		$total_bl = 0;

	return $total_bl;
}
// \endcond

// \cond onlyria
/** Alias de ord_installments_get_amount_rest()
 *	@param int $bl Voir ord_installments_get_amount_rest()
 *	@param $discount Optionnel, voir ord_installments_get_amount_rest()
 *	@return Voir ord_installments_get_amount_rest()
 */
function ord_installment_get_amount_rest( $bl, $discount=0 ){
	return ord_installments_get_amount_rest( $bl, $discount );
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère et crée, pour la date du jour, le dernier numéro de transaction
 *	@return Le numéro de transaction généré, ou False en cas d'échec
 */
function ord_installments_create_transaction(){
	global $config;

	$today = date( 'Y-m-d', strtotime('- 120 minute') );

	$sql = '
		select max(ord_transaction_id) as transaction_id
		from ord_transactions
		where ord_tnt_id = '.$config['tnt_id'].'
		and ord_date = "'.addslashes($today).'"
	';

	// Récupère le dernier transaction_id de la journée
	$r = ria_mysql_query($sql);
	if( !$r ) return false;

	$transaction_id = ria_mysql_num_rows($r) ? ria_mysql_result($r, 0, 'transaction_id') : 0;

	$error = false;
	while( !$error ){
		$transaction_id++;

		$sql = '
			insert into ord_transactions
				(ord_tnt_id, ord_date, ord_transaction_id)
			values
				('.$config['tnt_id'].', "'.addslashes($today).'", '.$transaction_id.')
		';

		$r = ria_mysql_query($sql);

		if( !$r ){
			if( ria_mysql_errno() != 1062 ){
				$error = true;
			}
		}else{
			break; // insertion OK, on breake
		}
	}

	return $error ? false : $transaction_id;
}
// \endcond

// \cond onlyria
/**	Alias de ord_installments_create_transaction()
 *	@return Voir ord_installments_create_transaction()
 */
function ord_transactions_create(){
	return ord_installments_create_transaction();
}
// \endcond

/**	Permet l'enregistrement d'une carte bleue et du paiement associé sur disque, dans un fichier crypté. Dans le cas d'un paiement Atos, envoie les informations au serveur Atos et stocke le résultat dans un ord_installements.
 *	@param $cmd Identifiant de la commande à laquelle le fichier se rapporte
 *	@param $type Type de carte bleue (Visa, etc.)
 *	@param string $name Nom du porteur de la carte
 *	@param $number Numéro de la carte
 *	@param $crypto Cryptogramme visuel
 *	@param $month Mois d'expiration
 *	@param $year Année d'expiration
 *	@param $atos Facultatif, si True : Module de paiment atos. Si False : module normal
 *	@param $inst_type Facultatif, Type de payement, pour différencier les commandes des bon de livraisons ..
 *	@param $force_price Facultatif, si différent de False, montant à envoyer à la place de celui de la commande (ex : différenciel)
 *	@param $override_all Facultatif, si true, $force_price s'applique (dans le cas contraire il ne s'applique pas quelque soit sa valeur)
 *
 *	@return bool false en cas d'erreur, true en cas de succès
 */
function ord_card_save( $cmd, $type, $name, $number, $crypto, $month, $year, $atos=false , $inst_type=1, $force_price=false, $override_all=false ){
	global $config;

	if( $inst_type !=1 && $inst_type !=3)
		return false;

	if( $inst_type == 1 ){
		$rcmd = ord_orders_get( 0,$cmd );
		if( !ria_mysql_num_rows($rcmd) ) return false;

		$commd = ria_mysql_fetch_array($rcmd);
		$user = ria_mysql_fetch_array(gu_users_get($commd['user']));
	}
	else{
		$rcmd = ord_bl_get( $cmd );
		if( !ria_mysql_num_rows($rcmd) ) return false;

		$commd = ria_mysql_fetch_array($rcmd);
		$user = ria_mysql_fetch_array(gu_users_get($commd['usr_id']));
	}

	// Contrôle les informations de carte bleue
	if( !ord_card_types_exists($type) ) return false;
	if( !trim($name) ) return false;
	$number = preg_replace( '/ /', '', $number );
	if( !is_numeric($number) ) return false;
	if( !is_numeric($month) ) return false;
	if( !is_numeric($year) ) return false;

	$price = $commd['total_ttc'];
	if( $force_price && ($inst_type == 3 || $override_all) )
		$price = $force_price;

	if( $atos ){
		//on recupere l'id de la transaction
		$res = ria_mysql_query('select count(*) as nb from ord_installments where itm_tnt_id='.$config['tnt_id'].' and SUBSTRING(itm_date_created from 1 for 10) = date_format(now(),"%Y-%m-%d")');
		if (! $res) return false;
		if (! ($res = ria_mysql_fetch_array($res))) return false;
		$transaction_id = $res['nb'] + 1;

		if($type == 1)
			$cards = 'VISA';
		elseif($type == 2)
			$cards = 'CB';
		elseif($type == 3)
			$cards = 'MASTERCARD';
		elseif($type == 4)
			$cards = 'MASTERCARD';

		$usr_ref = ( $user['ref'] == '' ? $user['society'] == '' ? $user['adr_lastname'] : $user['society'] : $user['ref'] );

		$buffer = "<service component=\"office\" name=\"author\"><author merchant_id=\"".$config['merchant_id']."\" merchant_country=\"".$config['merchant_country']."\" transaction_id=\"".$transaction_id."\" amount=\"".number_format($price,2,'','')."\" currency_code=\"".$config['money_code']."\" card_number=\"".$number."\" cvv_flag=\"1\" cvv_key=\"".$crypto."\" card_validity=\"".$year.$month."\" card_type=\"".$cards."\" capture_mode=\"VALIDATION\" order_id=\"".addslashes(substr(str_replace('&','',$usr_ref),0,30))."\" capture_day=\"".$config['delay_expiration']."\" security_indicator=\"09\"/></service>\n";

		$reponse =  ord_call_atos($buffer);

		if($reponse == -1) {
			$error = 'Le serveur de transaction ne répond pas. <br/> Veuillez réssayer plus tard ou contactez-nous ' ;
		}
		else if($reponse['response_code'] == 12){
			$error = 'Une erreur à eu lieu lors de la transaction';
		}else if($reponse['response_code'] == 5){
			$error = 'Votre carte à été refusé';

		}else if($reponse['response_code'] == 14){
			$error = 'Coordonnées bancaires ou cryptogramme visuel invalides.';

		}else if($reponse['response_code'] == 90 || $reponse['response_code'] == 99){
			$error = 'Nous rencontrons quelques soucis techniques pour accèder à votre banque.<br/> Veuillez ressayer plus tard ou contactez-nous ';

		}else if($reponse['response_code'] == 94){
			$error = 'L\'identifiant de la transaction est deja présent.';

		}else if ($reponse['response_code'] != 00) {
			$error = $reponse['response_code'].'Votre paiment n\'a pas été pris en compte, veuillez reassayer';
		}

		ord_installments_add(1,$inst_type,$cmd,number_format($price,2,'.',' '),number_format($price,2,'.',' '),$reponse['transaction_date'],$config['delay_expiration'],$transaction_id,isset($error));

		if( isset($error) ) return $error;
	}

	if( $inst_type == 1 ){
		// Crée le fichier xml décrivant la carte
		$filename = $config['ord-xml-dir'].'/'.str_pad($commd['id'],8,'0',STR_PAD_LEFT).'.rcb';
		$xml = fopen( $filename, 'w' );
		fwrite( $xml, '<?xml version="1.0" encoding="utf-8"?>' );
		fwrite( $xml, '<commande>' );
		fwrite( $xml, '	<numero>'.str_pad($commd['id'],8,'0',STR_PAD_LEFT).'</numero>' );
		fwrite( $xml, '	<montant>'.number_format($price,2,',',' ').'</montant>' );
		fwrite( $xml, '	<date>'.$commd['date'].'</date>' );
		fwrite( $xml, '	<carte>' );
		fwrite( $xml, '		<numero>'.$number.'</numero>' );
		fwrite( $xml, '		<type>'.ord_card_types_name($type).'</type>' );
		fwrite( $xml, '		<titulaire>'.$name.'</titulaire>' );
		fwrite( $xml, '		<expiration>'.$month.'/'.$year.'</expiration>' );
		fwrite( $xml, '		<complement>'.$crypto.'</complement>' );
		fwrite( $xml, '	</carte>' );
		fwrite( $xml, '</commande>' );
		fclose( $xml );

		// Crypte le fichier de carte bleue
		$key = false;
		if( $config['ord-rsa-store'] ){
			$str_id = isset($config['fld_ord_str']) ?  fld_object_values_get($commd['id'], $config[ 'fld_ord_str'] ) : $commd['str_id'];

			if( $config['tnt_id'] == 4 ){
				// dans le cas de proloisirs on récupère le premier magasin pour 1 revendeur ( pour avoir qu'une clé privé par revendeur )
				// récupère le revendeur
				$ref_revendeur = fld_object_values_get( $str_id, $config['fld_ref_revendeur']);

				// recupère les magasins pour ce revendeurs et prend le premier
				$stores = dlv_stores_get( 0, null, array('name'=>'asc'), 0, 0, false, 0, '', 0, 0, array( $config['fld_ref_revendeur'] => $ref_revendeur) );
				if( $stores && ria_mysql_num_rows( $stores ) ){
					if( $store = ria_mysql_fetch_array( $stores ) ){
						$str_id = $store['id'];
					}
				}
			}

			$key = str_replace('public', 'public-'.($str_id), $config['ord-rsa-key']);
		}

		// test si une clé est présente pour ce magasin sinon on prend la clé par défaut.
		if( !$key || !is_file( $key ) ) $key = $config['ord-rsa-key'];

		shell_exec( $config['ord-rsa-dir'].'/rsa encrypt '.$filename.' '.$key );
	}

	return true;
}

// \cond onlyria
/**	Permet d'envoyer une requete aux serveurs atos pour une transaction banquaire
 *	@param $xml Obligatoire, requete au format xml respectant les normes Atos Origin
 *	@param $returnxml Facultatif, si True, c'est le XML qui est renvoyé (à confirmer)
 *	@return -1 en cas d'erreur, ou le numéro de réponse de atos en cas de succès (ou le XML si $returnxml est précisé)
 */
function ord_call_atos( $xml, $returnxml=false ){
	global $config;

	$xml = str_replace("\r\n","",$xml);

	$sock = fsockopen($config['atos_ip'], $config['atos_port_service'], $errno, $errstr, 10);

	$tab = array();

	if($sock)
	{
		fputs($sock,$xml);

		$result = fgets($sock,8192);

		if($result != '' ){

			$name = '';
			$dom = new DomDocument();
			$dom->loadXML($result);

			$racine = $dom->getElementsByTagName('response')->item(0);

			if(isset($racine) && $racine->hasAttribute("name")){
			    $element = $dom->getElementsByTagName($racine->getAttribute("name"));
				$name = $racine->getAttribute("name");

				foreach($element as $e)
				{
					if ($e->hasAttribute("response_code")) {
						$tab['response_code'] = $e->getAttribute("response_code");
					}
					if ($e->hasAttribute("transaction_date")) {
						$tab['transaction_date'] = substr($e->getAttribute("transaction_date"),0,4).'-'.substr($e->getAttribute("transaction_date"),4,2).'-'.substr($e->getAttribute("transaction_date"),6,2);
					}
				}
		    }

			/* envoi de l'email pour les log */
			$email = new Email();
			$email->setSubject( 'Transaction Atos : '.$name.' - '.(isset($tab['response_code']) ? $tab['response_code'] : 'n/a') );
			$email->setFrom( '<EMAIL>' );
			$email->addTo( '<EMAIL>' );
			$email->openTable();
			$email->openTableRow(); $email->addCell( 'Requête' );$email->addCell( htmlspecialchars(preg_replace('/card_number=\"[0-9]*\"/U','card_number="****"',$xml)) ); $email->closeTableRow();
			$email->openTableRow(); $email->addCell( 'Réponse' );$email->addCell( htmlspecialchars($result) ); $email->closeTableRow();
			$email->closeTable();
			$email->send();

			if($returnxml){
				return $result;
			}


		}
		fclose($sock);
	}

	if( !sizeof($tab) ) return '-1';

	return $tab;
}
// \endcond

/** Cette fonction permet de simuler un paiement en plusieurs fois.
 *	@param float $amount Obligatoire, montant total à diviser
 *	@param $month Obligatoire, nombre de mois
 *	@param $first Obligatoire, date de première mensualité
 *	@param $round Facultatif, précision de l'arrondi réalisé (nombre de décimales après la virgule). Valeur par défaut : 0.
 *	@param $first_amount Optionnel, permet de donner un premier montant personnalisé
 *
 *	@return array Un tableau contenant l'échéancier
 *	@return bool False si l'un des paramètres est faux ou omis
 */
function ord_orders_get_schedule($amount, $month, $first, $round = 0, $first_amount=0){
	if (!is_numeric($amount) || $amount <= 0) {
		return false;
	}

	if (!is_numeric($month) || $month <= 0) {
		return false;
	}

	if (!isdate($first)) {
		return false;
	}

	if ($first_amount <= 0) {
		$first_amount = !is_numeric($round) || $round <= 0 ? ceil($amount / $month) : round(($amount / $month), $round);
	}

	$amount_monthly = ($amount - $first_amount) / ($month - 1);

	$schedule = array(
		$first => $first_amount,
	);

	$total = $first_amount;
	for ($i = 1; $i < ($month - 1); $i++) {
		$total += round($amount_monthly, 2);
		$schedule[date('Y-m-d', strtotime('+' . ($i * 30) . ' days'))] = round($amount_monthly, 2);
	}

	$schedule[date('Y-m-d', strtotime('+' . ($i * 30) . ' days'))] = round(($amount - $total), 2);

	return $schedule;
}

/** Cette fonction permet de simuler un paiement en plusieurs fois par chèque.
 *	@param float $amount Obligatoire, montant total à diviser
 *	@param $month Obligatoire, nombre de mois
 *	@param $first Obligatoire, date de première mensualité
 *	@param $round Facultatif, précision de l'arrondi réalisé (nombre de décimales après la virgule). Valeur par défaut : 0.
 *	@return array Un tableau contenant l'échéancier
 *	@return bool False si l'un des paramètres est faux ou omis
 */
function ord_cheque_get_schedule( $amount, $month, $first, $round=0 ){
	return ord_orders_get_schedule($amount, $month, $first, $round);
}

// \cond onlyria
/**	Cette fonction est chargée de vérifier un identifiant de mode de paiement.
 *	@param $type Identifiant du mode de paiement à vérifier
 *	@return bool true si le mode de paiement est valide
 *	@return bool false si le mode de paiement est invalide ou si une erreur s'est produite
 */
function ord_payment_types_exists( $type ){

	if( !is_numeric($type) || $type <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select pay_id from ord_payment_types
		where pay_tnt_id in (0, '.$config['tnt_id'].')
			and pay_id = '.$type.'
			and pay_is_deleted = 0
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère la dénomination d'un moyen de paiement.
 *	@param int $id identifiant du mode de règlement dont on souhaite retrouver le nom
 *	@return string Le nom du moyen de paiement
 *	@return bool false en cas d'échec
 */
function ord_payment_types_get_name( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select pay_name as name
		from ord_payment_types
		where pay_tnt_id in (0, '.$config['tnt_id'].')
			and pay_is_deleted = 0
			and pay_id = '.$id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res, 0, 'name');

}
// \endcond

// \cond onlyria
/** Cette fonction permet le chargement de la liste des moyens de paiement reconnus par la boutique.
 *	@param int $id Optionnel, identifiant ou tableau d'identifiants
 *	@param string $name Optionnel, designation du moyen de paiement
 *	@return bool False en cas d'échec
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du moyen de paiement
 *			- name : désignation du moyen de paiement
 *			- pos : position de tri
 *			- date_modified : date de dernière modification
 */
function ord_payment_types_get( $id=0, $name='' ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		select
			pay_id as id, pay_name as name, pay_pos as "pos", pay_date_modified as "date_modified"
		from
			ord_payment_types
		where
			pay_tnt_id in (0, '.$config['tnt_id'].')
			and pay_is_deleted = 0
	';

	if( sizeof($id) ){
		$sql .= ' and pay_id in ('.implode(', ', $id).')';
	}

	if( $name != '' ){
		$sql .= ' and pay_name = "'.addslashes($name).'"';
	}

	return ria_mysql_query($sql);

}
// \endcond

/** Cette fonction permet de charger un tableau des moyens de paiement.
 * 	@return array Un tableau contenant pour chaque moyen de paiement
 * 		- id : identifiant du moyen de paiement
 * 		- name : nom du moyen de paiement
 * 		- pos : position pour le tri
 */
function ord_payment_types_get_array(){
	$ar_payments = array();

	$r_payment = ord_payment_types_get();
	if ($r_payment && ria_mysql_num_rows($r_payment)) {
		while ($payment = ria_mysql_fetch_assoc($r_payment)) {
			$ar_payments[$payment['id']] = array(
				'id' 	=> $payment['id'],
				'name' 	=> $payment['name'],
				'pos' 	=> $payment['pos'],
			);
		}

		$ar_payments = array_msort($ar_payments, array('pos' => SORT_ASC, 'name' => SORT_ASC));
	}

	return $ar_payments;
}

// \cond onlyria
/** Cette fonction ajoute un moyen de paiement
 * @param string $name Obligatoire, nom du moyen de paiement
 * @return int l'identifiant du moyen de paiement en cas de succès, sinon flase
 */
function ord_payment_types_add( $name ){
	if( !$name ){
		return false;
	}
	global $config;

	$sql="insert into ord_payment_types(pay_tnt_id, pay_name, pay_date_modified) value(".$config['tnt_id'].", '".$name."', now())";
	if(!ria_mysql_query( $sql )){
		return false;
	}
	return mysql_insert_id();
}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un mode de paiement.
 *	@param int $id Identifiant du mode de paiement.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_payment_types_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update ord_payment_types
		set pay_date_modified = now()
		where pay_tnt_id in (0, '.$config['tnt_id'].')
			and pay_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de récupérer via CouchDB les tentatives de paiements depuis CouchDB en fonction des paramètres founis
 *	@param int $ord_id Identifiant de la commande
 *	@param $start Optionnel, début de la recherche dans CouchDB
 *	@param int $limit Optionnel, nombre de données à récupérer
 *	@param $attempt_type_filter Optionnel, permet de filtrer sur le type de tentative de paiement à récupérer
 *	@return array un tableau renvoyé par CouchDB en cas de succès, False en cas d'échec.
 */
function ord_payment_couchdb_get( $ord_id, $start=0, $limit=0, $attempt_type_filter=false ){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	global $config;

	{ //On compose le startkey et l'endkey dans l'ordre des clés dans la vue CouchDb. Il ne faut pas de valeur nulle pour une clé située entre deux autres.
		$params = array();
		$params['startkey'] = array($config['tnt_id'], $ord_id, 0);
		$params['endkey'] = array($config['tnt_id'],$ord_id,(time()+1));
		$params['skip'] = $start;
		$params['include_docs'] = true;
		$params['descending'] = true;
		$params['reduce'] = false;

		switch($attempt_type_filter){
			case 'returns' : {
				$view = 'returns_attempts';
				break;
			}
			case 'accesses' : {
				$view = 'accesses_attempts';
				break;
			}
			default: {
				$view = 'all_payments';
				break;
			}
		}
	}

	if (!isset($view)){
		return false;
	}

	if ($limit) {
		$params['limit'] = $limit;
	}

	$couchDB = CouchDB::create(_COUCHDB_HIST_PAY_DB_NAME);

	$results = $couchDB::getView($view, $params);

	//On ne récupère que les content et les identifiants de documents dans CouchDb
	$final = array();
	foreach( $results as $r ){
		$tmp = $r['doc']['content'];
		$tmp['_id'] = $r['doc']['_id'];

		$final[] = $tmp;
	}

	return $final;
}
// \endcond

/** Cette fonction permet de récupérer via CouchDB le nombre de tentatives de paiements depuis CouchDB en fonction des paramètres founis
 *	@param int $ord_id Identifiant de la commande
 *	@param $attempt_type_filter Optionnel, permet de filtrer sur le type de tentative de paiement à récupérer
 *	@return array un tableau renvoyé par CouchDB en cas de succès, False en cas d'échec.
 */
function ord_payment_couchdb_get_count($ord_id, $attempt_type_filter=false){
	if (!is_numeric($ord_id) || $ord_id < 0){
		return false;
	}

	global $config;

	$params = array();
	$params['startkey'] = array($config['tnt_id'],$ord_id,0);
	$params['endkey'] = array($config['tnt_id'],$ord_id,(time()+1));

	switch($attempt_type_filter){
		case 'returns' : {
			$view = 'returns_attempts';
			break;
		}
		case 'accesses' : {
			$view = 'accesses_attempts';
			break;
		}
		default: {
			$view = 'all_payments';
			break;
		}
	}

	if (!isset($view)){
		return false;
	}

	$couchDB = CouchDB::create(_COUCHDB_HIST_PAY_DB_NAME);

	return $couchDB::getViewCount($view, $params);
}


// \cond onlyria
/**	Cette fonction est chargée de vérifier la validité d'un identifiant de type de carte bleue.
 *	@param int $card Identifiant de carte bleue à vérifier
 *	@return bool true si l'identifiant est valide
 *	@return bool false si l'identifiant est invalide ou si une erreur s'est produite
 */
function ord_card_types_exists( $card ){
	if( !is_numeric($card) || $card<=0 ) return false;
	global $config;
	$r = ria_mysql_query('select card_id from ord_card_types where card_tnt_id='.$config['tnt_id'].' and card_id='.$card);
	if( !$r ) return false;
	return ria_mysql_num_rows($r)>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement des types de carte bleues définies dans la base de données.
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du type de carte
 *			- name : nom du type de carte
 */
function ord_card_types_get(){
	global $config;
	return ria_mysql_query('
		select card_id as id, card_name as name
		from ord_card_types
		where card_tnt_id='.$config['tnt_id'].'
		order by card_id
	');
}
// \endcond

// \cond onlyria
/**	Détermine le libellé d'un type de carte à partir de son identifiant
 *	@param int $card Identifiant du type de carte
 *	@return string Le nom du type de carte
 *	@return bool false en cas d'erreur
 */
function ord_card_types_name( $card ){
	if( !ord_card_types_exists($card) ) return false;
	global $config;
	$r = ria_mysql_query('select card_name as name from ord_card_types where card_tnt_id='.$config['tnt_id'].' and card_id='.$card);
	if( !$r || !ria_mysql_num_rows($r) ) return false;
	return ria_mysql_result( $r, 0, 'name' );
}
// \endcond

/// @}

// \cond onlyria

/** \defgroup model_ord_payment_credits Gestion des paiements à crédit sur les commandes
 *	\ingroup oms
 *	Ce module comprend les fonctions nécessaires à la gestion du paiement par crédit à la consommation (type Sofinco)
 *	Un crédit est caractérisé par :
 *		- Un commande auquel il est rattaché
 *		- Un numéro de dossier alphanumérique, et éventuellement un nom d'établissement
 *		- Un montant (qui généralement diffère du montant de la commande, du fait des frais de dossier par exemple)
 *		- Date de création / modification / suppression
 *		- Le cas échéant, le détail du crédit : taux, type de mensualité (se référer aux constantes "ABO_"), durée des mensualités
 *	@{
 */

/**	Cette fonction crée un nouveau crédit
 *	@param int $ord_id Obligatoire, identifiant de la commande
 *	@param string $ref Obligatoire, référence du crédit
 *	@param float $amount Obligatoire, montant du crédit
 *	@param $rate Optionnel, taux
 *	@param $length_type Optionnel, type de mensualité
 *	@param $length Optionnel, durée des mensualités
 *	@param string $name Optionnel, nom de l'établissement
 *
 *	@return int L'identifiant du crédit en cas de succès, False en cas d'échec
 */
function ord_payment_credits_add( $ord_id, $ref, $amount, $rate=null, $length_type=null, $length=null, $name=null ){
	if( !ord_orders_exists( $ord_id ) ){
		return false;
	}

	$amount = str_replace(array(',', ' '), array('.', ''), $amount);
	if( !is_numeric($amount) || $amount < 0 ){
		return false;
	}

	global $config;

	$fields = array( 'opc_tnt_id', 'opc_ord_id', 'opc_ref', 'opc_amount', 'opc_date_created' );
	$values = array( $config['tnt_id'], $ord_id, '"'.addslashes(trim($ref)).'"', $amount, 'now()' );

	if( $rate !== null ){
		$rate = str_replace(array(',', ' '), array('.', ''), $rate);
		if( !is_numeric($rate) || $rate < 0 ){
			return false;
		}
		$fields[] = 'opc_rate';
		$values[] = $rate;
	}

	if( $length_type !== null ){
		if( !is_numeric($length_type) ){
			return false;
		}else{
			$type_list = array(ABO_PERIOD_DAY, ABO_PERIOD_WEEK, ABO_PERIOD_MONTH, ABO_PERIOD_YEAR, ABO_PERIOD_MONTH_CIV, ABO_PERIOD_YEAR_CIV);
			if( !in_array($length_type, $type_list) ){
				return false;
			}
		}
		$fields[] = 'opc_length_type';
		$values[] = $length_type;
	}

	if( $length !== null ){
		if( !is_numeric($length) || $length < 0 ){
			return false;
		}
		$fields[] = 'opc_length';
		$values[] = $length;
	}

	if( $name !== null ){
		$fields[] = 'opc_name';
		$values[] = '"'.addslashes(trim($name)).'"';
	}

	$sql = 'insert into ord_payment_credits ('.implode(', ', $fields).') values ('.implode(', ', $values).')';

	if( !ria_mysql_query($sql) ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
		}
		return false;
	}

	return ria_mysql_insert_id();
}

/**	Cette fonction met à jour les informations sur un crédit
 *	Les paramètres optionnels ne peuvent pas être tous à leur valeur par défaut
 *	@param int $id Obligatoire, identifiant du crédit à modifier
 *	@param int $ord_id Optionnel, identifiant de la commande
 *	@param string $ref Optionnel, référence
 *	@param float $amount Optionnel, montant
 *	@param $rate Optionnel, taux (nullable)
 *	@param $length_type Optionnel, type de mensualité (nullable)
 *	@param $length Optionnel, durée des mensualités (nullable)
 *	@param string $name Optionnel, nom de l'établissement créditeur (nullable)
 *	@param string $date_end Optionnel, date de fin (nullable)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_payment_credits_upd( $id, $ord_id = false, $ref = false, $amount = false, $rate = false, $length_type = false, $length = false, $name = false, $date_end = false ){
	if( !ord_payment_credits_exists( $id ) ){
		return false;
	}

	$conditions = array();

	if( $ord_id !== false ){
		if( !ord_orders_exists( $ord_id ) ){
			return false;
		}
		$conditions[] = 'opc_ord_id = '.$ord_id;
	}

	if( $amount !== false ){
		$amount = str_replace(array(',', ' '), array('.', ''), $amount);
		if( !is_numeric($amount) || $amount < 0 ){
			return false;
		}
		$conditions[] = 'opc_amount = '.$amount;
	}

	if( $rate !== false ){
		if( $rate !== null ){
			$rate = str_replace(array(',', ' '), array('.', ''), $rate);
			if( !is_numeric($rate) || $rate < 0 ){
				return false;
			}
		}
		$conditions[] = 'opc_rate = '.( $rate === null ? 'NULL' : $rate );
	}

	if( $length_type !== false ){
		if( $length_type !== null ){
			$type_list = array(ABO_PERIOD_DAY, ABO_PERIOD_WEEK, ABO_PERIOD_MONTH, ABO_PERIOD_YEAR, ABO_PERIOD_MONTH_CIV, ABO_PERIOD_YEAR_CIV);
			if( !is_numeric($length_type) || !in_array($length_type, $type_list) ){
				return false;
			}
		}
		$conditions[] = 'opc_length_type = '.( $length_type === null ? 'NULL' : $length_type );
	}

	if( $length !== false ){
		if( $length !== null ){
			if( !is_numeric($length) || $length < 0 ){
				return false;
			}
		}
		$conditions[] = 'opc_length = '.( $length === null ? 'NULL' : $length );
	}

	if( $date_end !== false ){
		if( $date_end !== null ){
			if( !isdate($date_end) ){
				if( !isdateheure($date_end) ){
					return false;
				}else{
					$date_end = substr(dateheureparse($date_end), 0, 10);
				}
			}else{
				$date_end = dateparse($date_end);
			}
		}
		$conditions[] = 'opc_date_end = '.( $date_end === null ? 'NULL' : '"'.$date_end."'" );
	}

	if( $ref !== false ){
		$conditions[] = 'opc_ref = "'.addslashes(trim($ref)).'"';
	}

	if( $name !== false ){
		$conditions[] = 'opc_name = '.( $name === null ? 'NULL' : '"'.addslashes(trim($name)).'"' );
	}

	if( !sizeof($conditions) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_payment_credits
		set '.implode(', ', $conditions).'
		where opc_id = '.$id.' and opc_tnt_id = '.$config['tnt_id'].' and opc_date_deleted is null
	';

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
	}

	return $r;
}

/**	Cette fonction supprime un crédit
 *	@param int $id Identifiant du crédit
 *	@return bool true en cas de succès ou si le crédit avait déjà été supprimé, False en cas d'échec
 */
function ord_payment_credits_del( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !ord_payment_credits_exists( $id ) ){
		return true;
	}

	global $config;

	$sql = 'update ord_payment_credits set opc_date_deleted = now() where opc_tnt_id = '.$config['tnt_id'].' and opc_id = '.$id;

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
	}

	return $r;
}

/**	Cette fonction détermine l'existence d'un crédit
 *	@param int $id Identifiant du crédit
 *	@return bool True si le crédit existe, False sinon
 */
function ord_payment_credits_exists( $id ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = 'select 1 from ord_payment_credits where opc_date_deleted is null and opc_tnt_id = '.$config['tnt_id'].' and opc_id = '.$id;

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
	}

	return $r && ria_mysql_num_rows($r);
}

/**	Cette fonction récupère des crédits selont des paramètres optionnels
 *	les résultats sont triés du plus récemment actif au plus ancien (date_end NULL, date_creation desc)
 *	@param int $id Optionnel, identifiant d'un crédit ou tableau d'identifiants de crédits
 *	@param int $ord_id Optionnel, identifiant d'une commande
 *	@param string $ref Optionnel, référence d'un crédit
 *	@param string $date_start Optionnel, date de début du crédit sans la partie horaire (comparaison avec ">=")
 *	@param $ended Optionnel, détermine si on souhaite les crédits terminés (true), ceux en cours (false) ou les deux (null, par défaut)
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : identifiant du crédit
 *		- ord_id : identifiant de la commande
 *		- ref : référence du crédit
 *		- amount : montant total TTC
 *		- rate : taux
 *		- length_type : type de mensualité
 *		- length : durée des mensualités
 *		- name : nom de l'établissement
 *		- date_end : date de fin du crédit au format FR
 *		- date_end_en : date de fin du crédit au format EN
 *		- date_created : date de début / création du crédit au format FR
 *		- date_created_en : date de début / création du crédit au format EN
 *		- date_modified : date de dernière modification au format FR
 *		- date_modified_en : date de dernière modification au format EN
 */
function ord_payment_credits_get( $id = 0, $ord_id = 0, $ref = '', $date_start = false, $ended = null ){
	if( is_array($id) ){
		foreach( $id as $one_id ){
			if( !is_numeric($one_id) || $one_id <= 0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($id) || $id < 0 ){
			return false;
		}
		if( $id ){
			$id = array($id);
		}else{
			$id = array();
		}
	}

	if( !is_numeric($ord_id) || $ord_id < 0 ){
		return false;
	}

	if( $date_start !== false ){
		if( !isdate($date_start) ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			opc_id as "id", opc_ord_id as ord_id, opc_ref as "ref", opc_amount as "amount", opc_rate as "rate",
			opc_length_type as length_type, opc_length as "length", opc_name as "name",
			opc_date_end as date_end_en, date_format(opc_date_end,"%d/%m/%Y à %H:%i") as date_end,
			opc_date_created as date_created_en, date_format(opc_date_created,"%d/%m/%Y à %H:%i") as date_created,
			opc_date_modified as date_modified_en, date_format(opc_date_modified,"%d/%m/%Y à %H:%i") as date_modified
		from
			ord_payment_credits
		where
			opc_tnt_id = '.$config['tnt_id'].' and opc_date_deleted is null
	';
	if( sizeof($id) ){
		$sql .= ' and opc_id in ('.implode(', ', $id).')';
	}
	if( $ord_id ){
		$sql .= ' and opc_ord_id = '.$ord_id;
	}
	if( trim($ref) != '' ){
		$sql .= ' and opc_ref = "'.addslashes(trim($ref)).'"';
	}
	if( $date_start ){
		$sql .= ' and date(opc_date_created) >= "'.dateparse($date_start).'"';
	}
	if( $ended !== null ){
		if( $ended ){
			$sql .= ' and opc_date_end is not null';
		}else{
			$sql .= ' and opc_date_end is null';
		}
	}

	$sql .= '
		order by
			ifnull(opc_date_end, "1000-01-01 00:00:00") asc,
			opc_date_created desc
	';

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
	}

	return $r;
}

/**	Cette fonction récupère les identifiants de crédits rattachés à une commande
 *	@param int $ord_id Obligatoire, identifiant de la commande
 *	@param $type Optionnel, détermine si l'on  souhaite récupérer :
 *		- l'identifiant du crédit le plus actuel ("new", valeur par défaut)
 *		- tous les identifiants ("all")
 *		- le plus ancien ("old")
 *		- tous les actifs ("all-actif")
 *
 *	@return bool False en cas d'échec
 *	@return Un identifiant de crédit unique si "new" ou "old"
 *	@return array Un tableau d'identifiants si "all" ou "all-actif"
 */
function ord_payment_credits_get_id( $ord_id, $type='new' ){
	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	$type = strtolower(trim($type));

	$credits = ord_payment_credits_get( 0, $ord_id, '', false, ( $type == 'all-actif' ? false : null ) );

	if( !$credits ){
		return false;
	}

	$count = ria_mysql_num_rows($credits);

	switch( $type ){

		case 'new':
		case 'old':
			if( $count <= 0 ){
				return false;
			}
			$index = $type == 'new' ? 0 : $count - 1;
			return ria_mysql_result($credits, $index, 'id');

		case 'all':
		case 'all-actif':
			$ar_id = array();
			while( $credit = ria_mysql_fetch_array($credits) ){
				$ar_id[] = $credit['id'];
			}
			return $ar_id;

	}

	return false;
}

/** Cette fonction permet de récupérer la mensualité d'un créti
 *	@param $capital Obligatoire, montant emprunté
 *	@param $taux Obligatoire, taux applé au crédit
 *	@param $month Obligatoire, nombre de mensualité
 *	@return Le montant de chaque mensualité, False si l'un des paramètres obligatoires est faux ou omis
 */
function ord_payment_credits_get_monthly( $capital, $taux, $month ){
	if( !is_numeric($capital) || $capital<=0 ){
		return false;
	}

	if( !is_numeric($taux) || $taux<0 ){
		return false;
	}

	if( !is_numeric($month) || $month<=0 ){
		return false;
	}

	$monthly = $capital / $month;
	if( $taux>0 ){
		$monthly = ( $capital * ($taux / 12) ) / ( 1 - pow( (1 + ($taux / 12)), ($month * (-1) )) );
	}

	return $monthly;
}

/// @}

// \endcond

// \cond onlyria
/** \defgroup model_ord_payment_models Gestion des modèles de réglement
 * 	\ingroup oms
 *	Ce module comprend les fonctions nécessaires à la gestion des modèles de réglement
 *	Un modèle est représenté par les informations suivantes :
 *		- Un identifiant unique
 *		- Un nom
 *		- Une liste de moyen associé :
 *			- Un identifiant de paiement
 *			- Jour de réglement
 *			- Type de délais (0:Immédiat, 1:fin de civil, 2:fin de mois)
 *			- Jour de remise de la facture après expiration du délai
 *			- Un pourcentage appliqué ( si null alors 100% )
 *	@{
 */

/** Cette fonction teste l'existence d'un modèle
 *  @param int $id Obligatoire, identifiant du modèle
 *
 *	@return bool True si le modèle existe
 *	@return bool False s'il n'existe pas
 *
 */
function ord_payment_models_exists( $id ){
	if( !is_numeric( $id ) || $id<=0 ) return false;
	global $config;

	$sql = 'select opm_id from ord_payment_models where opm_tnt_id='.$config['tnt_id'].' and opm_id='.$id;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction permet la création d'un modèle de règlement
 *	@param string $name Obligatoire, Nom du modèle
 *
 *	@return Identifiant du modèle créé
 *	@return bool False en cas d'échec
 */
function ord_payment_models_add( $name ){
	if( trim($name) == '' ) return false;

	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'opm_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'opm_name';
	$values[] = "'".addslashes($name)."'";

	$fields[] = 'opm_date_created';
	$values[] = 'now()';

	$sql = 'insert into ord_payment_models ('.implode( ',', $fields ).') values ('.implode( ',', $values ).')';

	if( ria_mysql_query( $sql ) ){
		return ria_mysql_insert_id();
	}else{
		return false;
	}
}

/** Cette fonction permet la mise à jour d'un modèle de règlement
 *	@param int $id Obligatoire, Identifiant du modèle de règlement
 *	@param string $name Obligatoire, Nom du modèle
 *
 *	@return int Identifiant du modèle créé
 *	@return bool False en cas d'échec
 */
function ord_payment_models_upd( $id, $name ){
	if( !ord_payment_models_exists($id) ) return false;
	if( trim($name) == '' ) return false;
	global $config;

	$sql = 'update ord_payment_models set opm_name=\''.addslashes($name).'\' ';
	$sql .= ' where opm_tnt_id='.$config['tnt_id'].' and opm_id='.$id;

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les modèles
 *	@param int $id Facultatif : Identifiant du modèle
 *
 *	@return bool false en cas d'échec ou un résultat de ria_mysql_query contenant les colonnes suivantes :
 *		- id : identifnant du modèle
 *		- name : nom du modèle
 *		- date_created : date de création du modèle au format dd/mm/YYYY à H:i
 *		- date_created_en : date de création du modèle au format en
 */
function ord_payment_models_get( $id=0 ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		select
			opm_id as id, opm_name as name, date_format(opm_date_created,"%d/%m/%Y à %H:%i") as date_created, opm_date_created as date_created_en
		from ord_payment_models
		where opm_tnt_id = '.$config['tnt_id'].'
			and opm_is_deleted = 0
	';

	if( sizeof($id) ){
		$sql .= ' and opm_id in ('.implode(', ', $id).')';
	}

	return ria_mysql_query($sql);

}

/** Cette fonction permet de supprimer un modèle de paiement
 * @param int $id Obligatoire : identifiant du modèle à supprimer
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function ord_payment_models_del( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !ord_payment_models_exists( $id ) ){
		return true;
	}

	global $config;

	$sql = '
		update ord_payment_models
		set opm_is_deleted = 1, opm_date_deleted = now()
		where opm_tnt_id = '.$config['tnt_id'].'
			and opm_id = '.$id.'
	';

	return ria_mysql_query($sql);

}

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un modèle de paiement.
 *	@param int $id Identifiant ou tableau d'identifiants des modèles.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_payment_models_set_date_modified( $id ){
	global $config;

	$id = control_array_integer( $id );
	if( $id === false ){
		return false;
	}

	return ria_mysql_query('
		update ord_payment_models
		set opm_date_modified = now()
		where opm_tnt_id = '.$config['tnt_id'].'
			and opm_id in ('.implode(', ' , $id).')
	');

}
// \endcond

/** Cette fonction permet de tester l'existance d'une ligne dans le détail d'un modèle
 * 	@param int $mdl_id Obligatoire : identifiant du modèle
 *	@param int $pay_id Obligatoire : identifiant du moyen de paiement
 *	@param $days Obligatoire : Nombre de jours pour les paiements par compte
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function ord_payment_model_details_exists( $mdl_id, $pay_id, $days ){
	if( !is_numeric($mdl_id) || $mdl_id < 0 ) return false;
	if( !is_numeric($pay_id) || $pay_id < 0 ) return false;
	if( !is_numeric($days) || $days < 0 ) return false;
	global $config;

	$sql = 'select 1 from ord_payment_model_details where omd_tnt_id='.$config['tnt_id'].' and omd_opm_id='.$mdl_id.' and omd_pay_id='.$pay_id.' and omd_days='.$days;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction permet d'ajouter une ligne dans le détail d'un modèle
 *
 *	@todo pour le paramètres days_type, l'intitulé "Fin de civil" n'est pas suffisamment claire. Même chose pour $amount_type et $amount_value.
 *
 * 	@param int $mdl_id Obligatoire : identifiant du modèle
 *	@param int $pay_id Obligatoire : identifiant du moyen de paiement
 *	@param $days Obligatoire : Nombre de jours pour les paiements par compte
 *	@param $days_type Obligatoire : Type de délai (0:Immédiat, 1:fin de civil, 2:fin de mois)
 *	@param $day_stop Obligatoire : Jour de remise de la facture après expiration du délai
 *	@param $amount_type Obligatoire : type du montant prélevé (0 = pourcentage, 1 = équilibrage, 2 = montant)
 *	@param $amount_value Facultatif : valeur du montant prélevé, suivant le type (Null pour l'équilibrage)
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function ord_payment_model_details_add( $mdl_id, $pay_id, $days, $days_type, $day_stop, $amount_type, $amount_value=null ){

	if( !ord_payment_models_exists( $mdl_id ) ){
		return false;
	}
	if( !ord_payment_types_exists( $pay_id ) ){
		return false;
	}
	if( !is_numeric($days) || $days < 0 ){
		return false;
	}
	if( !is_numeric($days_type) || $days_type < 0 ){
		return false;
	}
	if( !is_numeric($day_stop) || $day_stop < 0 ){
		return false;
	}
	if( !in_array($amount_type, array(0, 1, 2)) ){
		return false;
	}
	if( $amount_value !== null ){
		$amount_value = str_replace(array(',', ' '), array('.', ''), $amount_value);
		if( !is_numeric($amount_value) ){
			return false;
		}
	}
	if( $amount_type == 1 ){
		$amount_value = null;
	}

	// controle que cette ligne n'est pas déjà existante
	if( ord_payment_model_details_exists( $mdl_id, $pay_id, $days ) ){
		return ord_payment_model_details_upd( $mdl_id, $pay_id, $days, $days_type, $day_stop, $amount_type, $amount_value );
	}

	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'omd_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'omd_opm_id';
	$values[] = $mdl_id;

	$fields[] = 'omd_pay_id';
	$values[] = $pay_id;

	$fields[] = 'omd_days';
	$values[] = $days;

	$fields[] = 'omd_days_type';
	$values[] = $days_type;

	$fields[] = 'omd_day_stop';
	$values[] = $day_stop;

	$fields[] = 'omd_amount_type';
	$values[] = $amount_type;

	if( $amount_value !== null ){
		$fields[] = 'omd_amount_value';
		$values[] = $amount_value;
	}

	$sql = 'insert into ord_payment_model_details ('.implode( ',', $fields ).') values ('.implode( ',', $values ).')';

	$res = ria_mysql_query( $sql );

	if( !$res ){
		return false;
	}

	fld_objects_set_date_modified(CLS_ORD_PAYMENT_MODEL, array($mdl_id));

	return true;
}

/** Cette fonction permet de mettre à jour une ligne de détail d'un modèle de payment
 * 	@param int $mdl_id Obligatoire : identifiant du modèle
 *	@param int $pay_id Obligatoire : identifiant du moyen de paiement
 *	@param $days Obligatoire : Nombre de jours pour les paiements par compte
 *	@param $days_type Obligatoire : Type de délai (0:Immédiat, 1:fin de civil, 2:fin de mois)
 *	@param $day_stop Obligatoire : Jour de remise de la facture après expiration du délai
 *	@param $amount_type Obligatoire : type du montant prélevé (0 = pourcentage, 1 = équilibrage, 2 = montant)
 *	@param $amount_value Facultatif : valeur du montant prélevé, suivant le type (Null pour l'équilibrage)
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function ord_payment_model_details_upd( $mdl_id, $pay_id, $days, $days_type, $day_stop, $amount_type, $amount_value=null ){

	if( !ord_payment_model_details_exists( $mdl_id, $pay_id, $days ) ){
		return false;
	}
	if( !is_numeric($days_type) || $days_type < 0 ){
		return false;
	}
	if( !is_numeric($day_stop) || $day_stop < 0 ){
		return false;
	}
	if( !in_array($amount_type, array(0, 1, 2)) ){
		return false;
	}
	if( $amount_value !== null ){
		$amount_value = str_replace(array(',', ' '), array('.', ''), $amount_value);
		if( !is_numeric($amount_value) ){
			return false;
		}
	}
	if( $amount_type == 1 ){
		$amount_value = null;
	}

	global $config;

	$statements = array();
	$statements[] = 'omd_days_type = '.$days_type;
	$statements[] = 'omd_day_stop = '.$day_stop;
	$statements[] = 'omd_amount_type = '.$amount_type;
	$statements[] = 'omd_amount_value = '.( $amount_value !== null ? $amount_value : 'NULL' );

	$sql = '
		update ord_payment_model_details
		set '.implode(', ', $statements).'
		where omd_tnt_id = '.$config['tnt_id'].'
			and omd_opm_id = '.$mdl_id.'
			and omd_pay_id = '.$pay_id.'
			and omd_days = '.$days.'
	';
	$res = ria_mysql_query( $sql );

	if( !$res ){
		return false;
	}

	fld_objects_set_date_modified(CLS_ORD_PAYMENT_MODEL, array($mdl_id));

	return true;

}

/** Cette fonction permet de récupérer le détails d'un modèle
 * 	@param int $mdl_id Obligatoire : identifiant du modèle
 *	@param int $pay_id Obligatoire : identifiant du moyen de paiement
 *	@param $days Obligatoire : Nombre de jours pour les paiements par compte
 *
 *	@return bool false en cas d'échec ou un résultat de ria_mysql_query contenant les colonnes suivantes :
 *		- opm_id : identifnant du modèle
 *		- name : nom du modèle
 *		- pay_id : identifiant du moyen de payment
 *		- pay_name : nom du moyen de paiement
 *		- days : Nombre de jours pour les paiements par compte
 *		- days_type : Type de délai (0:Immédiat, 1:fin de civil, 2:fin de mois)
 *		- day_stop : Jour de remise de la facture après expiration du délai
 *		- amount_type : type du montant prélevé (0 = pourcentage, 1 = équilibrage, 2 = montant)
 *		- amount_value : valeur du montant prélevé, suivant le type (Null pour l'équilibrage)
 */
function ord_payment_model_details_get( $mdl_id=0, $pay_id=0, $days=null ){

	if( !is_numeric($mdl_id) || $mdl_id < 0 ){
		return false;
	}
	if( !is_numeric($pay_id) || $pay_id < 0 ){
		return false;
	}
	if( $days !== null && ( !is_numeric($days) || $days < 0 ) ){
		return false;
	}

	global $config;

	$sql = '
		select
			omd_opm_id as opm_id, omd_pay_id as pay_id, omd_days as days, omd_days_type as days_type, omd_day_stop as day_stop,
			omd_amount_type as amount_type, omd_amount_value as amount_value, opm_name as name, pay_name as pay_name
		from
			ord_payment_model_details
			join ord_payment_models on omd_tnt_id = opm_tnt_id and omd_opm_id = opm_id
			join ord_payment_types on (omd_tnt_id = pay_tnt_id or pay_tnt_id = 0) and omd_pay_id = pay_id
		where omd_tnt_id = '.$config['tnt_id'].'
			and opm_is_deleted = 0
	';

	if( $mdl_id > 0 ){
		$sql .= ' and omd_opm_id = '.$mdl_id;
	}

	if( $pay_id > 0 ){
		$sql .= ' and omd_pay_id = '.$pay_id;
	}

	if( $days !== null ){
		$sql .= ' and omd_days = '.$days;
	}

	return ria_mysql_query($sql);

}

/** Cette fonction permet de supprimer tout ou partie du détail d'un modèle de règlement
 *
 * 	@param int $mdl_id Obligatoire : identifiant du modèle
 *	@param int $pay_id Optionnel : identifiant du moyen de paiement
 *	@param $days Optionnel : Nombre de jours pour les paiements par compte (attention, 0 diffère de NULL)
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function ord_payment_model_details_del( $mdl_id, $pay_id=0, $days=null ){

	if( !is_numeric($mdl_id) || $mdl_id <= 0 ){
		return false;
	}
	if( !is_numeric($pay_id) || $pay_id < 0 ){
		return false;
	}
	if( $days !== null && ( !is_numeric($days) || $days < 0 ) ){
		return false;
	}

	global $config;

	$sql = '
		delete from ord_payment_model_details
		where omd_tnt_id = '.$config['tnt_id'].'
			and omd_opm_id = '.$mdl_id.'
	';
	if( $pay_id ){
		$sql .= 'and omd_pay_id = '.$pay_id;
	}
	if( $days !== null ){
		$sql .= ' and omd_days = '.$days;
	}

	$res = ria_mysql_query( $sql );

	if( !$res ){
		return false;
	}

	fld_objects_set_date_modified(CLS_ORD_PAYMENT_MODEL, array($mdl_id));

	return true;

}

/// @}

// \endcond


