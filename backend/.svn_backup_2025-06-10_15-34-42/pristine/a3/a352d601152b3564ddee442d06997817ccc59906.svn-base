language: php

sudo: false

cache:
    directories:
        - vendor
        - $HOME/.composer/cache/files

env:
    - DEPS=no

before_install:
    - phpenv config-rm xdebug.ini

before_script:
    - if [ "$DEPS" == "low" ]; then composer --prefer-lowest --prefer-stable update; fi;
    - if [ "$DEPS" == "no" ]; then composer install; fi;

script: |
    ./vendor/bin/simple-phpunit

matrix:
    include:
        - php: 5.3
          dist: precise
        - php: 5.4
        - php: 5.5
        - php: 5.6
          env: DEPS=low
        - php: 7.0
        - php: 7.1
        - php: 7.2
    fast_finish: true
