# ImportationMonitoringLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**self** | [**\Swagger\Client\Model\LinksImportationGetImportationMonitoringLink**](LinksImportationGetImportationMonitoringLink.md) |  | 
**technical_progression** | [**\Swagger\Client\Model\LinksImportationTechnicalProgressionLink**](LinksImportationTechnicalProgressionLink.md) |  | 
**cancel** | [**\Swagger\Client\Model\LinksImportationCancelLink**](LinksImportationCancelLink.md) |  | [optional] 
**configure_remaining_catalog_columns** | [**\Swagger\Client\Model\LinksImportationConfigureRemainingCatalogColumnsLink**](LinksImportationConfigureRemainingCatalogColumnsLink.md) |  | [optional] 
**commit_columns** | [**\Swagger\Client\Model\LinksImportationCommitColumnsLink**](LinksImportationCommitColumnsLink.md) |  | [optional] 
**commit** | [**\Swagger\Client\Model\LinksImportationCommitLink**](LinksImportationCommitLink.md) |  | [optional] 
**activate_auto_import** | [**\Swagger\Client\Model\LinksImportationActivateAutoImportLink**](LinksImportationActivateAutoImportLink.md) |  | [optional] 
**catalog_columns** | [**\Swagger\Client\Model\LinksImportationGetDetectedCatalogColumnsLink**](LinksImportationGetDetectedCatalogColumnsLink.md) |  | [optional] 
**custom_columns** | [**\Swagger\Client\Model\LinksImportationGetCustomColumnsLink**](LinksImportationGetCustomColumnsLink.md) |  | [optional] 
**product_samples** | [**\Swagger\Client\Model\LinksImportationGetProductSampleLink**](LinksImportationGetProductSampleLink.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


