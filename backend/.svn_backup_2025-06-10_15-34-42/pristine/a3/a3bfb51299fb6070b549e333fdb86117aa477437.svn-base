Basic operators
-----
<?php

$a ** $b;

++$a;
--$a;
$a++;
$a--;

@$a;
~$a;
-$a;
+$a;

(int) $a;
(integer) $a;
(float) $a;
(double) $a;
(real) $a;
(string) $a;
(binary) $a;
(array) $a;
(object) $a;
(bool) $a;
(boolean) $a;
(unset) $a;

$a * $b;
$a / $b;
$a % $b;
$a + $b;
$a - $b;
$a . $b;
$a << $b;
$a >> $b;
$a < $b;
$a <= $b;
$a > $b;
$a >= $b;
$a == $b;
$a != $b;
$a <> $b;
$a === $b;
$a !== $b;
$a <=> $b;
$a & $b;
$a ^ $b;
$a | $b;
$a && $b;
$a || $b;
$a ? $b : $c;
$a ?: $c;
$a ?? $c;

$a = $b;
$a **= $b;
$a *= $b;
$a /= $b;
$a %= $b;
$a += $b;
$a -= $b;
$a .= $b;
$a <<= $b;
$a >>= $b;
$a &= $b;
$a ^= $b;
$a |= $b;
$a =& $b;

$a and $b;
$a xor $b;
$a or $b;

$a instanceof Foo;
$a instanceof $b;
-----
$a ** $b;
++$a;
--$a;
$a++;
$a--;
@$a;
~$a;
-$a;
+$a;
(int) $a;
(int) $a;
(double) $a;
(double) $a;
(double) $a;
(string) $a;
(string) $a;
(array) $a;
(object) $a;
(bool) $a;
(bool) $a;
(unset) $a;
$a * $b;
$a / $b;
$a % $b;
$a + $b;
$a - $b;
$a . $b;
$a << $b;
$a >> $b;
$a < $b;
$a <= $b;
$a > $b;
$a >= $b;
$a == $b;
$a != $b;
$a != $b;
$a === $b;
$a !== $b;
$a <=> $b;
$a & $b;
$a ^ $b;
$a | $b;
$a && $b;
$a || $b;
$a ? $b : $c;
$a ?: $c;
$a ?? $c;
$a = $b;
$a **= $b;
$a *= $b;
$a /= $b;
$a %= $b;
$a += $b;
$a -= $b;
$a .= $b;
$a <<= $b;
$a >>= $b;
$a &= $b;
$a ^= $b;
$a |= $b;
$a =& $b;
$a and $b;
$a xor $b;
$a or $b;
$a instanceof Foo;
$a instanceof $b;
