<?php
	/**	\file rebild-relations-hierarchy.php
	*	Ce fichier est destiné à reconstruire la hiérarchie des relations entre objets.
	*/
	set_include_path(dirname(__FILE__) . '/../include/');

	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once( 'relations.inc.php' );

    $type_id = isset($argv[1]) ? $argv[1] : 0;
	$obj = isset($argv[2]) ? $argv[2] : 0;
	
	if (!is_numeric($type_id) || $type_id <= 0) {
		die('Le type de relation est obligatoire.');
	}
	
	rel_relations_hierarchy_rebuild( $type_id, $obj );