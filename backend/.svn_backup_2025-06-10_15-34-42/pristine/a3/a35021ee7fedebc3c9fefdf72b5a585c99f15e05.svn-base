<documentation title="Useless Overriding Methods">
    <standard>
    <![CDATA[
    Methods should not be defined that only call the parent method.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A method that extends functionality on a parent method.">
        <![CDATA[
final class Foo
{
    public function bar()
    {
        parent::bar();
        <em>$this->doSomethingElse();</em>
    }
}
        ]]>
        </code>
        <code title="Invalid: An overriding method that only calls the parent.">
        <![CDATA[
final class Foo
{
    public function bar()
    {
        <em>parent::bar();</em>
    }
}
        ]]>
        </code>
    </code_comparison>
</documentation>
