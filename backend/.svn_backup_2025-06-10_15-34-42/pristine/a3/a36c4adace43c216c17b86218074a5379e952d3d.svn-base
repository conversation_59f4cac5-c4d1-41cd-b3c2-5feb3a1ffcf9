<?php
	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_PAIEMENT_YESBYCASH');
	
	$active = cfg_overrides_get_value( 'yesbycash_notify_active', $config['wst_id'] );
	$time 	= cfg_overrides_get_value( 'yesbycash_notify_delay', $config['wst_id'] );
	
	$active = in_array( $active, array('Oui', 'oui', '1') ) ? true : false;
	$time	= is_numeric($time) && $time > 0 ? $time : 24;

	if( isset($_POST['save']) ){
		$s_active 	= isset($_POST['active']) && $_POST['active'] == '1' ? '1' : '0';
		$s_time 	= isset($_POST['time']) && is_numeric($_POST['time']) && $_POST['time'] > 0 ? $_POST['time'] : 24;

		if( 
			!cfg_overrides_set_value( 'yesbycash_notify_active', $s_active, $config['wst_id'] )
			|| !cfg_overrides_set_value( 'yesbycash_notify_delay', $s_time, $config['wst_id'] )
		){
			$error = _("Une erreur inattendue s'est produite lors de l'enregistrement. Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		}

		if( !isset($error) ){
			$_SESSION['save-config-yesbycash'] = true;
			header('Location: /admin/config/paiements/yesbycash.php');
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Règlement par YesByCash') . ' - ' . _('Modes de règlement') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
<h2><?php echo _("YesByCash"); ?></h2>
<p><?php echo _("Configurez ci-dessous le module de paiement YesByCash"); ?>  :</p>
<form action="/admin/config/paiements/yesbycash.php" method="post">
	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}elseif( isset($_SESSION['save-config-yesbycash']) ){
			print '<div class="success">' . _('La configuration de YesByCash a bien été enregistrée.') . '</div>';
			unset( $_SESSION['save-config-yesbycash'] );
		}
	?>

	<dl>
		<dt><?php print _('Relance :'); ?></dt>
		<dd><?php echo _("Vous pouvez activer / désactiver ci-dessous la relance faite aux clients n'ayant pas encore réglé leur commande payée via YesByCash."); ?></dd>
		<dd>
			<br /><label for="active-1"><?php echo _('Activer :'); ?></label>
			<input <?php print $active ? 'checked="checked"' : ''; ?> type="radio" name="active" id="active-1" value="1" /> <label for="active-1"><?php echo _("Oui"); ?></label>
			<input <?php print !$active ? 'checked="checked"' : ''; ?> type="radio" name="active" id="active-0" value="0" /> <label for="active-0"><?php echo _("Non"); ?></label>
		</dd>
		<dd><label for="active-1"><?php echo _("Envoyer la relance"); ?></label>&nbsp;<input class="col-numeric" type="text" size="4" name="time" name="time" value="<?php print $time; ?>" />&nbsp;<?php echo _("heure(s) après la confirmation de commande."); ?></dd>
	</dl>

	<div class="ria-admin-ui-actions">
		<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
	</div>
	
</form>

<?php
	require_once('admin/skin/footer.inc.php');
?>