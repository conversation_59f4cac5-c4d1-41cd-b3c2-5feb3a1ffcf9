<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/billing.proto

namespace GPBMetadata\Google\Api;

class Billing
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ab4020a18676f6f676c652f6170692f62696c6c696e672e70726f746f12" .
            "0a676f6f676c652e6170692293010a0742696c6c696e6712450a15636f6e" .
            "73756d65725f64657374696e6174696f6e7318082003280b32262e676f6f" .
            "676c652e6170692e42696c6c696e672e42696c6c696e6744657374696e61" .
            "74696f6e1a410a1242696c6c696e6744657374696e6174696f6e121a0a12" .
            "6d6f6e69746f7265645f7265736f75726365180120012809120f0a076d65" .
            "7472696373180220032809426e0a0e636f6d2e676f6f676c652e61706942" .
            "0c42696c6c696e6750726f746f50015a45676f6f676c652e676f6c616e67" .
            "2e6f72672f67656e70726f746f2f676f6f676c65617069732f6170692f73" .
            "657276696365636f6e6669673b73657276696365636f6e666967a2020447" .
            "415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

