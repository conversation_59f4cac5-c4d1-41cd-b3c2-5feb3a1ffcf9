<?php
/// \cond onlyria

require_once('define.inc.php');
require_once('users.inc.php');

use Google\Cloud\BigQuery\BigQueryClient;
use Google\Cloud\Core\ExponentialBackoff;


/** \defgroup model_bigquery BigQuery
 *	\ingroup yuto system
 *	Ce module comprend les fonctions nécessaires à la gestion de BigQuery
 *	@{
 */

DEFINE('BIGQUERY_DB_INVOICES_STATS', 'invoice_stats');

class BigQuery {
	private $bigQueryClient = null;

	private $project_id = '';
	private $dataset = '';
	private $table = '';

	public static function create($db_name){
		return new BigQuery($db_name);
	}

	/** Constructeur de la classe, le paramètre db_name est important et permet d'instancier le bon dataset
	 *  @param array $db_name : Constante du dataset a utiliser pour l'instance
	 */
	public function __construct($db_name){
		global $config;

		$this->dataset = $db_name;
		$this->table = 'riashop_invoices';

		if (!isset($config['env_sandbox']) || $config['env_sandbox']) {
			$this->project_id = "infra-dev-riashop";
		}else{
			$this->project_id = "riashop-186610";
		}

		$this->bigQueryClient = new BigQueryClient([
		    'projectId' => $this->project_id,
		]);

		// création de l'ensemble de donnée s'il n'existe pas
		$dataset = $this->bigQueryClient->dataset($db_name);
		if( !$dataset->exists() ){
			$dataset = $this->bigQueryClient->createDataset($db_name);
		}

		// création de la table pour le client si elle n'existe pas
		$table = $dataset->table($this->table);
		if( !$table->exists() ){
			$this->createTable();
		}

	}

	/** Cette fonction permet de retourner les statistiques sur les factures 
	 *  @param array $period : période utilisé pour le group by, Facultatif si le paramètre group_key est donnée
	 *	@param $date_start : Date de début d'analyse
	 *	@param $date_end : Date de fin d'analyse
	 *	@param $usr_id : Identifiant du client 
	 *	@param $prd_id : Identifiant du produit 
	 *	@param $cat_id : Identifiant de la catégorie 
	 *	@param $seller_id : Identifiant du commercial 
	 *	@param $group_key : prd / brd / cat / usr, permet d'avoir le top 20 en fonction des paramètres données
	 *	@param $order_by : sort de trie du résultat pour le moment fonctionnel avec seulement le group_key
	 *  @return un tableau associatif avec les colonnes suivantes : 
	 *		- total_ht 
	 *		- total_ttc 
	 *		- products : Sommes des quantités
	 *		- marge_ht 
	 *		- marge_ttc 
	 *		- obj_id_0 : disponible si group_key donnée, c'est l'identifiant de l'objet en question  
	 *		- year : disponible si period donnée 
	 *		- month : disponible si period donnée 
	 *		- day : disponible si period donnée 
	 */
	public function getInvoiceStats($period=null, $date_start, $date_end, $usr_id=0, $prd_id=0, $cat_id=0, $seller_id=0, $group_key=false, $cat_childs=null, $order_by=null, $brd_id=0, $limit=20){
		if( $period!==null && !in_array(strtoupper($period), array('DAY', 'MONTH', 'YEAR', 'WEEK', 'QUARTER','SEMESTER')) ){
			throw new Exception("Type de période non gérer");
		}
		
		$usr_id = control_array_integer( $usr_id, false );
		if( $usr_id!==0 && $usr_id===false ){
			throw new Exception("Paramètre usr_id invalide.");
		}
		$prd_id = control_array_integer( $prd_id, false );
		if( $prd_id!==0 && $prd_id===false ){
			throw new Exception("Paramètre prd_id invalide.");
		}
		if( !is_numeric($brd_id) ){
			throw new Exception("Paramètre brd_id invalide.");
		}
		if( !is_numeric($seller_id) ){
			throw new Exception("Paramètre seller_id invalide.");
		}
		if( !is_numeric($cat_id) ){
			throw new Exception("Paramètre cat_id invalide.");
		}

		$table_name = '`'.$this->project_id.'.'.$this->dataset.'.'.$this->table.'`'; 


		$select = array(
			'sum(prd.price_ht * prd.qte) as total_ca_ht',
			'sum(prd.price_ttc * prd.qte) as total_ca_ttc',
			'sum(prd.qte) as products',
			'sum(prd.purchase_avg * prd.qte) as marge_ht',
			'sum(prd.purchase_avg * prd.tva_rate * prd.qte) as marge_ttc',
			'count(distinct piece.id) as count',
		);

		$where = array('1=1');
		if( isdate($date_start) && isdate($date_end) ){
			$where[] = 'date_en between \''.addslashes($date_start).'\' and \''.addslashes($date_end).'\'';
		}
		else if( isdate($date_start) ){
			$where[] = 'date_en >= \''.addslashes($date_start).'\' ';
		}
		else if( isdate($date_end) ){
			$where[] = 'date_en < \''.addslashes($date_end).'\' ';
		}
		if($usr_id){
			$where[] = 'usr_id in ('.implode(',', $usr_id).')';
		}
		if($prd_id){
			$where[] = 'prd.id in ('.implode(',', $prd_id).')';
		}
		if($brd_id){
			$where[] = 'prd.brd_id = '.$brd_id;
		}
		if($cat_id){
			$where[] = $cat_id.' in UNNEST(prd.cat_ids)';
		}
		if($seller_id){
			$where[] = $seller_id.' in UNNEST(prd.sellers)';
		}

		if( $group_key ){

			

			switch($group_key){
				case 'prd': 
					$col_group = 'prd.id';
					break;
				case 'brd': 
					$col_group = 'prd.brd_id';
					break;
				case 'seller': 
					$col_group = 'seller';
					if($seller_id){
						$where[] = ' seller = '.$seller_id;
					}
					break;
				case 'cat': 
					$col_group = 'cat';

					if( $cat_childs!==null  ){
						$cat_childs = control_array_integer( $cat_childs );
						if( $cat_childs === false ){
							throw new Exception("Paramètre cat_childs invalide.");
						}
						$where[] = ' cat in ('.implode(',',$cat_childs).') ';
					}
					break;
				case 'usr': 
					$col_group = 'usr_id';
					break;
				default:
					throw new Exception("Mauvais groupe");
			}

			$sql = '
				SELECT 
					'.$col_group.' as obj_id_0,
					'.implode(',',$select).'
				FROM '.$table_name.' AS piece,
				UNNEST(products) AS prd
				'.($col_group=='cat' ? ',UNNEST(prd.cat_ids) AS cat' : '').'
				'.($col_group=='seller' ? ',UNNEST(prd.sellers) AS seller' : '').'
				WHERE '.implode(' and ', $where).'
				GROUP BY '.$col_group.'
			';

			if( $order_by!==null && $order_by == 'products' ){
				$sql .= ' order by sum(prd.qte) desc ';
			}else{
				$sql .= ' order by sum(prd.price_ht * prd.qte) desc ';					
			}

			if( $limit > 0 ){
				$sql .= ' limit '.$limit;
			}

		}else{

			$period = strtoupper($period);

			$period_fields = array(); 
			if( $period == 'DAY' || $period == 'MONTH' || $period == 'YEAR' || $period == 'QUARTER' || $period == 'WEEK' || $period == 'SEMESTER' ){
				$period_fields[] = 'extract(ISOYEAR FROM date_en)';
				$select[] = 'extract(ISOYEAR FROM date_en) as year';
			}
			if( $period == 'DAY' || $period == 'MONTH' ){
				$period_fields[] = 'extract(MONTH FROM date_en) ';
				$select[] = 'extract(MONTH FROM date_en) as month';
			}
			if( $period == 'DAY'  ){
				$period_fields[] = 'extract(DAY FROM date_en) ';
				$select[] = 'extract(DAY FROM date_en) as day';
			}
			if( $period == 'QUARTER'  ){
				$period_fields[] = 'extract(QUARTER FROM date_en) ';
				$select[] = 'extract(QUARTER FROM date_en) as quarter';
			}
			if( $period == 'SEMESTER'  ){
				$period_fields[] = 'CONCAT((extract(ISOYEAR FROM date_en)),\'-S\',(IF(extract(QUARTER FROM date_en) < 3, 1, 2))) ';
				$select[] = 'IF(min(extract(QUARTER FROM date_en)) < 3, 1, 2) as semester';
			}
			if( $period == 'WEEK'  ){
				$period_fields[] = 'extract(ISOWEEK FROM date_en) ';
				$select[] = 'extract(ISOWEEK FROM date_en) as week';
			}

			$sql = '
				SELECT 
					'.implode(',',$select).'
				FROM '.$table_name.' AS piece,
				UNNEST(products) AS prd
				WHERE '.implode(' and ', $where).'
				GROUP BY 
					'.implode(',',$period_fields).'
			';

		}

		return $this->query($sql);
	}

	/**
	 *	Cette fonction permet d'executer une query sur Google et d'obtenir le résultats
	 *	@param $sql Requete Sql sous formet texte
	 *	@return tableau des datas ou une Exception si une erreur à eu lieu
	 */
	private function query($sql){
		try{
			$queryResults = $this->bigQueryClient->runQuery($this->bigQueryClient->query($sql));

			if ($queryResults->isComplete()) {

				$tmp = array();
			    $rows = $queryResults->rows();
			    foreach ($rows as $row) {
					$tmp[] = (array)$row;
				}
				return $tmp;

			} else {
			    throw new Exception('Erreur dans la requete BIGQUERY');
			}
		}catch(Exception $e){
			    throw new Exception($sql."::".$e->getMessage());
		}
	}

	/**
	 * Cette fonction permet l'insertion de documents dans la base Google, attention c'est long ! cette action peut rendre 30s
	 * @param $docs: tableau de document a insert, attention seul les colonnes présente dans le schéma google seront importer le reste sera ignoré. Une colonne "id" est obligatoire et doit être unique
	 *	@return true si la requete à aboutit, sinon une exception
	 */
	public function insertAll($docs){
		if( !is_array($docs) ){
			throw new Exception("Ca doit être un tableau de document en entré");
		}

		$ids = array();
		$topush = array();
		foreach( $docs as $doc ){
			if( !isset($doc['id']) ){
				throw new Exception("Les documents doivents comporter une clé ID et être unique");
			}
			$ids[] = $doc['id'];

			$data = $this->parseData($this->getSchema(), $doc);
			$topush[] = json_encode($data);
		}	


		// supprime les doc  
		$queryResults = $this->bigQueryClient->runQuery($this->bigQueryClient->query(
		    'DELETE FROM `'.$this->project_id.'.'.$this->dataset.'.'.$this->table.'` WHERE ID in ('.implode(',', $ids).')'
		));


		// insert les doc
		$table = $this->bigQueryClient->dataset($this->dataset)->table($this->table);

		$loadConfig = $table->load(implode("\n", $topush))->sourceFormat('NEWLINE_DELIMITED_JSON');
		$job = $table->runJob($loadConfig);

		// poll the job until it is complete
		$backoff = new ExponentialBackoff(10);
		$backoff->execute(function () use ($job) {
		    $job->reload();
		    if (!$job->isComplete()) {
		        throw new Exception('Job has not yet completed', 500);
		    }
		});
		// check if the job has errors
		if (isset($job->info()['status']['errorResult'])) {
		    $error = $job->info()['status']['errorResult']['message'];
			throw new Exception("Error job Bigquery : ".$error.print_r($job->info()['status']['errors'], true));
		} else {
			return true;
		}
/*
		$insertResponse = $table->insertRows();

		if ($insertResponse->isSuccessful()) {
		    return true;
		} else {
			$errors = array();
		    foreach ($insertResponse->failedRows() as $row) {
		        foreach ($row['errors'] as $error) {
		        	$errors[] = $error['reason']." : ".$error['message'];
		        }
		    }
		    throw new Exception("Error insert data ".print_r($data, true)." : ".implode("\n", $errors));
		}*/
	}

	/**
	 *	Cette fonction permet de vérifier les datas donnée en fonction du schéma, utile pour des casts php, tous ce qui est pas dans le schéma sera non retourné
	 *	@param $schema Schéma de la base google en provenance de la fonction getScheme de cette classe
	 *	@param $doc Docuement a parser.
	 *	@return le document avec seulement les colonnes a insert dans la Db google en fonction du schéma
	 */
	public function parseData($schema, $doc){
		$final_doc = array();
		foreach( $schema as $field ){
			if( isset($doc[$field['name']]) ){

				switch ($field['type']) {
					case 'DATE':
						$date = new DateTime($doc[$field['name']]);
						$final_doc[$field['name']] = $date->format("Y-m-d");
						break;
					
					case 'RECORD':

						if( $field["fields"] && is_array($doc[$field['name']]) ){
							$final_doc[$field['name']] = array();
							foreach($doc[$field['name']] as $tmp){
								$final_doc[$field['name']][] = $this->parseData($field['fields'], $tmp);
							}
						}
						else{
							$final_doc[$field['name']] = $doc[$field['name']];
						}

						break;
					
					case 'INTEGER':
						if( $field["mode"] == 'REPEATED' ){
							$final_doc[$field['name']] = array();
							foreach($doc[$field['name']] as $tmp){
								$final_doc[$field['name']][] = (int) $tmp;
							}
						}else{
							$final_doc[$field['name']] = (int) $doc[$field['name']];
						}
						break;
						

					default:
						$final_doc[$field['name']] = $doc[$field['name']];
						break;
				}
			}
		}

		return $final_doc;
	}

	/**
	 *	Cette fonction permet de récupéer le schéma de la table 
	 *	@return un tableau avec le schéma tel que Google le demande pour la création de la table ou l'insert des lignes.
	 */
	public function getSchema(){

		if( $this->dataset == BIGQUERY_DB_INVOICES_STATS ){
			return array(
				array('name' => 'id', 				'type' => 'INTEGER',			 'mode' => 'REQUIRED', ),
				array('name' => 'date_en', 			'type' => 'DATE',				 'mode' => 'REQUIRED', ),
				array('name' => 'usr_id', 			'type' => 'INTEGER',			 'mode' => 'REQUIRED', ),
				array('name' => 'piece', 			'type' => 'STRING',				 'mode' => 'REQUIRED', ),
				array('name' => 'ref', 				'type' => 'STRING',				 'mode' => 'REQUIRED', ),
				array('name' => 'products', 		'type' => 'RECORD',				 'mode' => 'REPEATED', 		
					'fields' => array(
						array('name' => 'id', 			'type' => 'INTEGER',			 'mode' => 'REQUIRED', ),
						array('name' => 'line', 		'type' => 'INTEGER',			 'mode' => 'REQUIRED', ),
						array('name' => 'qte', 			'type' => 'FLOAT',				 'mode' => 'REQUIRED', ),
						array('name' => 'brd_id', 		'type' => 'INTEGER',			 'mode' => 'NULLABLE', ),
						array('name' => 'title', 		'type' => 'STRING',				 'mode' => 'REQUIRED', ),
						array('name' => 'ref', 			'type' => 'STRING',				 'mode' => 'REQUIRED', ),
						array('name' => 'price_ht', 	'type' => 'FLOAT',				 'mode' => 'REQUIRED', ),
						array('name' => 'price_ttc', 	'type' => 'FLOAT',				 'mode' => 'REQUIRED', ),
						array('name' => 'ecotaxe', 		'type' => 'FLOAT',				 'mode' => 'NULLABLE', ),
						array('name' => 'tva_rate', 	'type' => 'FLOAT',				 'mode' => 'NULLABLE', ),
						array('name' => 'purchase_avg', 'type' => 'FLOAT',				 'mode' => 'NULLABLE', ),
						array('name' => 'sellers', 		'type' => 'INTEGER',			 'mode' => 'REPEATED', ),
						array('name' => 'cat_ids', 		'type' => 'INTEGER',			 'mode' => 'REPEATED', ),
				)),
			);
		}

		throw new Exception("Pas de schéma pour ce type de dataset");		
	}

	/**
	 *	cette fonction passe de manière récursive pour faire un tableau avec 1 seul niveau pour la création sql de la table
	 */
	private function getFieldSqlMode($f){

		switch($f['type']){
			case 'INTEGER': $f['type'] = 'INT64'; break;
			case 'FLOAT': $f['type'] = 'FLOAT64'; break;
		}

		switch($f['mode']){
			case 'NULLABLE': return $f['name'].' '.$f['type'].'';
			case 'REQUIRED': return $f['name'].' '.$f['type'].' not null'; 
			case 'REPEATED': return $f['name'].' ARRAY<'.$f['type'].'>'; 
		}
	}

	/**
	 *	Cette fonction permet de générer le create en sql en fonction du schéma
	 */
	private function createTable(){

		// création de la table en sql car on peut pas partionner .. 
		$table_name = '`'.$this->project_id.'.'.$this->dataset.'.'.$this->table.'`';


		$fields = array(); 
		foreach( $this->getSchema() as $r ){

			if( $r['type'] == 'RECORD' && $r['mode'] == 'REPEATED' ){

				$subfields = array();
				foreach( $r['fields'] as $f ){
					$subfields[] = $this->getFieldSqlMode($f);
				}

				$fields[] = $r['name'].' ARRAY<STRUCT<'.implode(',', $subfields).'>>';

			} else {
				$fields[] = $this->getFieldSqlMode($r);
			}
		}

		
		$create = 'CREATE TABLE '.$table_name.' ( '.implode(',', $fields).' )';

		if( $this->dataset == BIGQUERY_DB_INVOICES_STATS ){
			$create .= ' PARTITION BY date_en';
		}

		$this->query($create);

	}
}

/// @}

/// \endcond
