function test(id, buttons) // cool function
{
    alert('hello');
    alert('hello again'); // And again.
    // Valid comment.

}//end test()

var good = true; // Indeed.

mig.Gallery.prototype = {

    init: function(cb)
    {

    },//end init()

    imageClicked: function(id)
    {

    }//end imageClicked()

};

dfx.getIframeDocument = function(iframe)
{

    return doc;

};//end dfx.getIframeDocument()

// Verify that multi-line control structure with comments and annotations are left alone.
if (condition // comment
    && anotherCondition) {
    condition = true;
}
