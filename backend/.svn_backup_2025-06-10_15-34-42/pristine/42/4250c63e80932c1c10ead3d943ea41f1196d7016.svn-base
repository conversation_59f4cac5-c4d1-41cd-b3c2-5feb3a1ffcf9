<?php
namespace Pdf;

use DateTime;

require_once('Pdf/PieceDeVente.php');
require_once('Pdf/OrderInstallements.php');
/** \devgroup InvoicePdf InvoicePdf
 * \ingroup PieceDeVente
 */
final class InvoicePdf extends PieceDeVente
{
	/**
	 * __construct
	 *
	 * \param array $invoice
	 * 
	 * \return void
	 */
	public function __construct(array $invoice)
	{
		parent::__construct();
		$this->data['invoice'] = $invoice;
	}

	/**
	 * Function called before PDF generation
	 *
	 * \return void
	 */
	protected function bootstrap()
	{

		if( !isset($this->data['ord']) ){
			$this->loadOrders();
		}

		if( !isset($this->data['user']) ){
			$this->loadUserInfo();
		}

		if( !isset($this->data['addresses']) ){
			$this->loadInvoiceAdress();
		}

		parent::bootstrap();

		$this->SetSubject(str_replace('#param[ref]#', $this->data['invoice']['ref'], $this->requireOption('subject')));
    	$this->SetTitle(str_replace('#param[ref]#', $this->data['invoice']['ref'], $this->requireOption('name')));
	}

	protected function getDefaultOptions()
	{
		global $config;

		return array(
			'subject' => $config['pdf_generation_inv_subject'],
			'name' => $config['pdf_generation_inv_name'],
			'logo' => $config['pdf_generation_inv_logo'],
			'logo_disposition' => $config['pdf_generation_inv_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_inv_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_inv_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_inv_display_dlv_address'],
			'prd_reduce' => $config['pdf_generation_inv_prd_reduce'],
			'header' => $config['pdf_generation_inv_header'],
			'header_content' => $config['pdf_generation_inv_header_content'],
			'footer' => $config['pdf_generation_inv_footer'],
			'footer_content' => $config['pdf_generation_inv_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_inv_prd_reftruncated'],
			'prd_barcode' => $config['pdf_generation_inv_prd_barcode'],
			'font_size' => $config['pdf_generation_inv_font_size'],
			'ref' => ''
		);
	}

	/**
	 * Configure le tableau pour avoir un comportement par defaut. Colonnes :
	 * - ref
	 * - Désignation
	 * - Quantité
	 * - Prix unitaire brut
	 * - Remise
	 * - prix unitaire net
	 * - montant total ht
	 *
	 * @return Pdf::ProductTable
	 */
	public function defaultProductTable()
	{
		global $config;

		$this->table()
			->withTbodyFontSize($this->requireOption('font_size'));
		
		if( $this->getOption('prd_img') && isset($config['img_sizes']['small']) ){
			$that = & $this;

			$this->table()
				->addColumn(new Column('Image', 'L', $this->convertPixelToFPDF(120), function ($p) use ($that) {
					global $config;

					if (!$p['img_id']
						|| !file_exists($config['img_dir'] . '/' . $config['img_sizes']['small']['dir'] . '/' . $p['img_id'] . '.' . $config['img_sizes']['small']['format'])) {
						return '';
					}

					return $this->Image(
						$config['img_dir'] . '/' . $config['img_sizes']['small']['dir'] . '/' . $p['img_id'] . '.' . $config['img_sizes']['small']['format'],
						$this->getX(),
						$this->getY() + 1,
						$this->convertPixelToFPDF($config['img_sizes']['small']['width']),
						$this->convertPixelToFPDF($config['img_sizes']['small']['height'] - 5)
					);
				}));
		}

		$this->table()
			->addColumn(new Column('Référence', 'L', 15, 'ref'))
			->addColumn(new Column('Désignation', 'L', $this->getOption('prd_reduce') ? 40 : 50, function($p){
				if (isset($p['label']) && trim($p['label']) != '') {
					return $p['label'];
				}

				$comment = '';
				if (isset($p['notes']) && $p['notes']) {
					$comment = '
					Notes : ' . $p['notes'];
				}

				return $p['name'] . $comment;
			}))
			->addColumn(new Column('Qté', 'C', 10, 'qte'))
			->addColumn($this->formatPriceColumn('Px unit. brut', 'R', 20, 'price_ht'));

		if ($this->getOption('prd_reduce')) {
			$this->table()
				->addColumn(new Column('Remise', 'C', 10, function ($product) {
					$remise = 0;
					if (isset($product['price_brut_ht']) && is_numeric($product['price_brut_ht']) && $product['price_brut_ht'] > 0) {
						$remise = 100 - ($product['price_ht'] * 100 / $product['price_brut_ht']);
					}
					return number_format($remise, 0) . ' %';
				}));
		}

		$this->table()
			->addColumn($this->formatPriceColumn('Px unit. net', 'R', 15, 'price_ht'))
			->addColumn($this->formatPriceColumn('Montant HT', 'R', 15, 'total_ht'));
		
		if ($this->getOption('prd_code_ean')) {
			$this->table()
				->addColumn(new Column('Code EAN', 'ean', 30, function ($p) {
					return prd_products_get_barcode($p['id']);
				}), 2);
		}

		return $this->table();
	}

	/**
	 * Contient le corp du pdf dans ce cas le tableau
	 *
	 * \return void
	 */
	public function body()
	{
		$this->table->generateTable();
	}

	/**
	 * Génèrer la ligne d'inforamtion utilisateur
	 *
	 * \return void
	 */
	protected function userInfoRow()
	{
		$this->requireData('invoice');
		$this->requireData('user');

		$this->SetY($this->getY() + 6);
		$this->resetX();
		$this->SetFont($this->font_family, 'B', 9);
		//header
		$this->Cell(23, 6, utf8_decode('Facture N°'), 1, 0, 'C');
		$this->Cell(23, 6, utf8_decode('Date'), 1, 0, 'C');
		$this->Cell(47, 6, utf8_decode('Client'), 1, 1, 'C');
		//content
		$this->SetFont($this->font_family, '', 9);
		$date = new DateTime($this->data['invoice']['date_en']);
		$this->Cell(23, 6, utf8_decode(($this->data['invoice']['piece'] ? $this->data['invoice']['piece'] : $this->data['invoice']['ref'])), 1, 0, 'C');
		$this->Cell(23, 6, utf8_decode($date->format('d/m/Y')), 1, 0, 'C');
		$this->Cell(47, 6, utf8_decode($this->data['user']['ref']), 1, 0, 'C');

		if ($this->shouldShowTaxcode() && trim($this->data['user']['taxcode'])) {
			$this->Cell(50, 6, utf8_decode('N° intracommunautaire : ' . $this->data['user']['taxcode']), 0, 1, 'L');
		} else {
			$this->Cell(0, 6, '', 0, 1, 'L');
		}
	}

	/**
	 * Génère la page du total de la commande
	 *
	 * \return void
	 */
	protected function generateTotalPage()
	{
		$y = $this->getY();
		$this->SetFont($this->font(), 'B', 8);
		//header
		$this->Cell(15, 7, utf8_decode('CODE'), 1, 0, 'L');
		$this->Cell(22, 7, utf8_decode('BASE'), 1, 0, 'C');
		$this->Cell(13, 7, utf8_decode('TAUX'), 1, 0, 'C');
		$this->Cell(27, 7, utf8_decode('MONTANT'), 1, 1, 'R');
		//row
		$this->SetFont($this->font(), '', 9);
		$first = 'Tva';
		$total = 0;
		foreach( $this->taxes()->tva() as $rate => $tva ){
			$this->Cell(15, 5, utf8_decode($first), 1, 0, 'L');
			$this->Cell(22, 5, $this->price($tva['base']) . $this->currency, 1, 0, 'R');
			$this->Cell(13, 5, (($rate - 1) * 100) . ' %', 1, 0, 'C');
			$this->Cell(27, 5, $this->price($tva['amount']) . $this->currency, 1, 1, 'R');
			$first = '';
			$total += $tva['amount'];
		}

		//row
		$eco = $this->taxes()->ecotaxe();
		$rate = 0;
		if( $eco['base'] > 0 ){
			$rate = $eco['amount'] * 100 / $eco['base'];
		}

		$this->Cell(15, 5, utf8_decode('Eco'), 1, 0, 'L');
		$this->Cell(22, 5, $this->price($eco['base']) . $this->currency, 1, 0, 'R');
		$this->Cell(13, 5, number_format($rate) . ' %', 1, 0, 'C');
		$this->Cell(27, 5, $this->price($eco['amount']) . $this->currency, 1, 1, 'R');
		$total += $eco['amount'];
		//row
		$this->Cell(15, 7, utf8_decode('TOTAL :'), 1, 0, 'L');
		$this->Cell(35, 7, utf8_decode('Tva+Eco-participation'), 1, 0, 'R');
		$this->Cell(27, 7, $this->price($total) . $this->currency, 1, 1, 'R');

		$this->setXY(90, $y);
		$this->SetFont($this->font(), 'B', 8);
		$this->Cell(37, 7, utf8_decode('TOTAL H.T.'), 1, 0, 'C');
		$this->Cell(37, 7, utf8_decode('TOTAL T.T.C.'), 1, 0, 'C');
		$this->Cell(37, 7, utf8_decode('NET A PAYER'), 1, 0, 'C');

		$this->setXY(90, $y + 7);
		$this->SetFont($this->font(), '', 9);
		$this->Cell(37, 7, $this->price($this->data['invoice']['total_ht']) . $this->currency, 1, 0, 'R');
		$this->Cell(37, 7, $this->price($this->data['invoice']['total_ttc']) . $this->currency, 1, 0, 'R');
		$this->Cell(37, 7, $this->price($this->data['invoice']['total_ttc']) . $this->currency, 1, 0, 'R');

		if( isset($this->data['payments']) && !empty($this->data['payments']) || isset($this->data['ord']) ){
			$this->setXY(90, $y + 14);
			$this->SetFont($this->font(), 'B', 7);
			$this->Cell(40, 7, utf8_decode('Conditions de règlement :'), 0, 2, 'C');
			$this->SetFont($this->font(), '', 6);

			if( isset($this->data['payments']) && !empty($this->data['payments']) ){
				$this->generatePayments();
			}elseif( isset($this->data['ord']) ){
				$this->generateOrderInstallements();
			}
		}
	}

	public function setPayments(array $payments)
	{
		foreach ($payments as $value) {
			if (!ria_array_key_exists(array('label', 'amount', 'date'), $value)) {
				return false;
			}
		}

		$this->data['payments'] = $payments;
	}

	protected function generatePayments()
	{
		$this->requireData('payments');

		$length = count($this->data['payments']) - 1;

		$y = $this->getY();
		foreach ($this->data['payments'] as $i => $payment) {
			$date = new \DateTime($payment['date']);
			$h = 4;
			if ($length == $i && (!is_null($this->text_instead_of_date))) {
				$this->cell(20, $h, utf8_decode($this->text_instead_of_date), 0, 0, 'L');
			}else{
				$this->cell(20, $h, utf8_decode('le ' . $date->format('d/m/Y')), 0, 0, 'L');
			}
			$this->cell(30, $h, utf8_decode($payment['label']), 0, 0, 'R');
			// $this->cell(20, $h, utf8_decode(ria_number_french($payment['rest'])), 0, 0, 'R');
			$y += $h;
		}
	}

	protected function generateOrderInstallements()
	{
		if( is_null($this->getData('ord')) || !is_null($this->getData('installments')) ){
			return;
		}

		$installments = ria_array_get($this->data, 'installments', null);
		$ordInstallements = new OrderInstallements(
			$this,
			$this->data['ord'],
			$installments,
			isset($this->data['pay_name']) ? $this->data['pay_name'] : null
		);
		
		$this->data['pay_name'] = $ordInstallements->paymentName();
		if (trim($this->data['pay_name']) == '') {
			/**
			 * \deprecated Les appels à MySQL ne doivent plus se faire via cette classe
			 */
			$params = array(0, $this->data['ord']['pay_id']);
			if ($this->data['ord']['pay_id'] <= 0) {
				$params = array($this->data['ord']['usr_id']);
			}
			$r_pay = call_user_func_array('gu_users_payment_types_get', $params);
			$this->data['pay_name'] = '';
			if ($r_pay && ria_mysql_num_rows($r_pay)) {
				$pay = ria_mysql_fetch_assoc($r_pay);
				$this->data['pay_name'] = gu_users_payment_types_view($pay);
			}
		}

		$this->Cell(40, 7, utf8_decode($this->data['pay_name']), 0, 0, 'C');

		$ordInstallements->generate($this->getX(), $this->getY() - 5);
	}

	/**
	 * Récupère les informations de l'utilisateur
	 *
	 * \return void
	 * \deprecated
	 */
	public function loadUserInfo()
	{
		$r_user = gu_users_get($this->data['invoice']['usr_id']);
		if ($r_user && ria_mysql_num_rows($r_user)) {
			$this->withUser(ria_mysql_fetch_assoc($r_user));
		}
	}

	/**
	 * Récupère les commande de la facture
	 *
	 * \return void
	 * \deprecated
	 */
	protected function loadOrders()
	{
		$r_orders = ord_invoices_orders_get($this->data['invoice']['id']);
		if ($r_orders && ria_mysql_num_rows($r_orders)) {
			$this->data['ord'] = ria_mysql_fetch_assoc($r_orders);
		}
	}

	/**
	 * Récupère l'adresse de facturation
	 *
	 * \return void
	 * \deprecated
	 */
	protected function loadInvoiceAdress()
	{
		if (is_null($this->getData('ord'))) {
			$this->loadUserAdress();
		}else{
			$this->loadOrderAdress();
		}
	}

	/**
	 * Récupèrer les informations des adresses
	 *
	 * \return void
	 * \deprecated
	 */
	protected function loadOrderAdress()
	{
		$this->requireData('ord');

		$this->data['addresses'] = ord_orders_address_load($this->data['ord']);
		if( $this->data['addresses'] ){
			if( isset($this->data['addresses']['delivery']) ){
				$this->withDlvAdress($this->data['addresses']['delivery']);
			}

			if( isset($this->data['addresses']['invoice']) ){
				$this->withInvAdress($this->data['addresses']['invoice']);
			}
		}
	}

	/**
	 * Récupère l'adresse de facturation du client
	 *
	 * \return void
	 * \deprecated
	 */
	protected function loadUserAdress()
	{
		$this->requireData('user');

		$id_adress = gu_users_get_adr_invoices($this->data['user']['id']);
		if ($id_adress) {
			$r_adress = gu_adresses_get(0, $id_adress);
			if ($r_adress && ria_mysql_num_rows($r_adress)) {
				$this->withInvAdress(ria_mysql_fetch_assoc($r_adress));
			}
		}
	}

	public function blocHeader()
	{
		$height = 0;
		$this->Cell(50, 3, '', 0, 2);
		if ($this->getOption('header')) {
			$this->SetFont($this->font(), '', 9);
			$line_height = 4;
			$w = $this->w - $this->rMargin - $this->x;
			$height = ceil(($this->GetStringWidth(iconv('utf8', 'windows-1252', $this->requireOption('header_content'))) / $w)) * $line_height;

			$this->MultiCell(0, 4, iconv('utf8', 'windows-1252', $this->requireOption('header_content')), 0, 2, '');
		}
		$this->SetY($this->GetY() + $height);
	}

	public function blocFooter()
	{
		if ($this->getOption('footer')) {
			$y = $this->getY() + 4;
			$this->SetY($y);

			$this->SetFont($this->font(), '', 9);
			$this->MultiCell(0, 4, iconv('utf8', 'windows-1252', $this->requireOption('footer_content')), 0, 2, '');
			$this->Cell(0, 2, '', 0, 2);
		}
	}
}