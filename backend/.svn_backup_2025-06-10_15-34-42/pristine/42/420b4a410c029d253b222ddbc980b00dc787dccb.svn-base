<?php

	/** \file send-cart-notify.php
	 *	Il est possible de passer en paramètre l'identifiant d'un locataire.
	 * 	Ce script est chargé de relancer les clients pour le panier non finalisé
	 *
	 */

	 set_include_path(dirname(__FILE__) . '/../include/');
	require_once('email.inc.php');
	require_once('products.inc.php');
	require_once('prices.inc.php');
	
	$tnt = isset($argv[1]) && is_numeric($argv[1]) && $argv[1]>0 ? $argv[1] : 0;

	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_websites( $tnt );
	if( !is_array($configs) || !sizeof($configs) ){
		return false;
	}

	foreach( $configs as $config ){
		// Vérifie que la fonctionnalitée est bien activée pour ce client, dans le cas contraire, passe au client suivant.			
		if( !isset($config['prices_drop_actived']) || !$config['prices_drop_actived'] ){
			continue;
		}

		$inscript = prc_prices_drop_get_products( '', $config['wst_id'] );
		if( !is_array($inscript) ){
			continue;
		}

		$time_date_30j		= strtotime( '-30 days' );

		$is_promotion 		= false;
		$current_activated 	= 0;
		$include_deleted	= true;
		$price_is_notify	= array();

		foreach( $inscript as $email=>$prd_ids ){
			if( !is_array($prd_ids) || !sizeof($prd_ids) ){
				continue;
			}

			// Seul les comptes clients ayant le droit de voir les tarifs doivent reçevoir cette notification
			$r_user = gu_users_get( 0, $email );
			if( !$r_user || !ria_mysql_num_rows($r_user) ){
				continue;
			}

			$user = ria_mysql_fetch_assoc( $r_user );
			if( !gu_users_rights_used( '_RGH_CATALOG_DSP_PRC', $user['id']) ){
				continue;
			}

			if( in_array('all', $prd_ids) ){
				$prd_ids = 0;
			}

			$r_product = prd_products_get_simple( $prd_ids, '', true, $config['cat_root'], true, false, true, false, array('childs'=>true) );
			if( !$r_product || !ria_mysql_num_rows($r_product) ){
				continue;
			}

			$ar_prices_drop = array();
			while( $product = ria_mysql_fetch_assoc($r_product) ){
				$r_price = prc_prices_get( 0, 1, false, false, false, $product['id'], false, false, false, null, null, 1, $current_activated, false, null, array(), array(), null, $is_promotion, 0, null, ($product['price_ht']+0.01), 0, $include_deleted );
				if( !$r_price ){
					continue;
				}

				$price_max = 0;
				while( $price = ria_mysql_fetch_assoc($r_price) ){
					$is_notified = fld_object_values_get( $price['id'], _FLD_PRC_DROP_NTF );
					if( $is_notified == 'Oui' ){
						continue;
					}

					$price_is_notify[] = $price['id'];

					if( $price['date-end'] == '9999-12-31 23:59:59' && trim($price['date_deleted_en']) != '' ){
						if( strtotime($price['date_deleted_en']) > $time_date_30j ){
							if( $price_max < $price['value'] ){
								$price_max = $price['value'];
							}
						}
					}elseif( strtotime($price['date-end']) < time() && strtotime($price['date-end']) > $time_date_30j ){
						if( $price_max < $price['value'] ){
							$price_max = $price['value'];
						}
					}
				}

				if( $price_max > 0 ){
					$ar_prices_drop[] = array_merge( $product, array('price_max' => $price_max) );
				}
			}

			$price_is_notify = array_unique( $price_is_notify );
			foreach( $price_is_notify as $one_prc ){
				fld_object_values_set( $one_prc, _FLD_PRC_DROP_NTF, 'Oui' );
			}
			
			if( sizeof($ar_prices_drop) ){
				$res = prc_prices_drop_notify( $email, $ar_prices_drop, $config['wst_id'] );
			}
		}
	}
