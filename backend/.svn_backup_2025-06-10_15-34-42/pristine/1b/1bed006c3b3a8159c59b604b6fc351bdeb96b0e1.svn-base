<?php
/**
 * \ingroup PriceWatching
 */
/**
 * \interface iCompetitors
 * \brief Cette interface doit être implémenté par chaque nouveau concurrent
 *
 * Cette interface permet l'abstraction du concurrent car chaque concurrent exposera la même api, ainsi on peux switch de concurrent a volonté
 */
interface iCompetitors
{
	/**
	 * Cette fonction doit pouvoir récupérer l'identifiant d'un produit chez le concurrent
	 * en utilisant un identifiant comme le code barre
	 *
	 * \param  string $prd_ref    référence du produit, généralement le code bar,
	 *                            avec le quel on trouve la référence chez le concurrent.
	 *
	 * \return string         la référence du produit chez le concurrent.
	 */
	public function getCptRef( $prd_ref );

	/**
	 * Cette fonction doit pouvoir récuperer les tarifs des produits depuis la concurrence
	 *
	 * \param  string $prd_cpt_ref    référence du produit, généralement la référence du
	 *                                produit chez le concurrent.
	 *
	 * \return array              Un tableau généralement composé du prix TTC et
	 *                               des frais de livraison.
	 */
	public function getOffer( $prd_cpt_ref );
}