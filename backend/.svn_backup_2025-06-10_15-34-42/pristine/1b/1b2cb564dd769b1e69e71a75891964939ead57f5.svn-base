<?php

require_once('db.inc.php');
require_once('tenants.inc.php');
require_once('prd/images.inc.php');
require_once('strings.inc.php');
require_once('ria.queue.inc.php');

/** \defgroup model_images Bibliothèque d'images
 *	\ingroup dam
 *
 *  Ce module comprend les fonctions nécessaires à la gestion d'une bibliothèque d'images.
 *  Elle n'est pas liée à l'utilisation qui en est faite par la suite (produits, pages de contenu, etc...).
 *
 *  Le comportement de ces fonctions est régi par les directives de configuration suivantes :
 *	- img_dir : chemin absolu vers le répertoire de stockage des images
 *	- img_sizes : tableau des tailles d'images. Chaque entrée de ce tableau doit contenir les clés width et height.
 *
 *  Les tailles de vignettes sont utilisées comme des maximums. La génération de vignette conserve
 *  les proportions des images. Il est possible d'utiliser autant de tailles de vignettes que souhaité,
 *  mais celles-ci doivent être triées par ordre croissant.
 *
 * @{
*/

/** Permet l'upload d'une image qui sera ensuite ajoutée à la bibliothèque d'images
 *
 *  @param string $fieldname Obligatoire, Nom du champ de formulaire contenant l'image à uploader.
 *  @return int L'identifiant attribué à l'image.
 *
 */
function img_images_upload( $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return img_images_add($_FILES[$fieldname]['tmp_name'],$_FILES[$fieldname]['name']);
}

/** Permet l'ajout d'un fichier image. Cette fonction est similaire à img_images_upload,
 *  à l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile
 *  lors d'importation.
 *	@param string $filename Obligatoire, Nom du fichier image à ajouter (le fichier doit être local)
 *	@param string $src_filename Facultatif, nom original du fichier sur le poste utilisateur
 *	@param bool $override_src_name Facultatif, si False et si l'image existe déjà, le nom original n'est surchargé que si img_src_name est à NULL
 *	@param bool $sync Facultatif, true si l'image provient de la gestion commerciale
 *	@param string $ref_gescom Optionnel, référence externe de l'image
 *
 *  @return bool false en cas d'erreur
 *  @return int l'identifiant de la nouvelle image, en cas de succès
 */
function img_images_add( $filename, $src_filename='', $override_src_name=true, $sync=false, $ref_gescom='' ){
	global $config;

	// Vérifie l'existance du fichier
	if( !file_exists($filename) ) return false;

	// nom du fichier original sans extension
	$srcname = '';
	// type de fichier original (extension)
	$srctype = '';

	if( $src_filename!=='' ){
		// recherche le dernier caractères "." dans le nom du fichier (l'extension suit)
		$pos = strrpos( $src_filename, '.' );
		if( $pos!==false ){
			$srcname = substr( $src_filename, 0, $pos );
			$srctype = substr( $src_filename, $pos + 1 );
		}
	}

	// Vérifie si le fichier n'existe pas déjâ , si c'est le cas, l'identifiant de ce fichier est retourné
	$file = fopen($filename, 'r');
	$filesize = filesize($filename);
	if( $filesize>0 ){
		$content = fread($file, $filesize);
		$r_img = img_images_get(0, md5($content));
		if( ria_mysql_num_rows($r_img)>0 ){
			$img = ria_mysql_fetch_array($r_img);
			if( $override_src_name ){
				img_images_set_source_name( $img['id'], $srcname );
				img_images_set_type( $img['id'], $srctype );
			}else{
				if( trim($img['src_name'])=='' ){
					img_images_set_source_name( $img['id'], $srcname );
				}elseif( $srcname != '' ){
					// ajoute quand même le nouveau nom à l'historique
					img_image_names_add( $img['id'], $srcname );
				}
				if( trim($img['type'])=='' )
					img_images_set_type( $img['id'], $srctype );
			}

			if( $sync ){
				// marque l'image comme étant désormais synchronisée (elle pouvait ne pas l'être avant)
				img_images_set_is_sync( $img['id'], true );
			}

			img_images_set_ref_gescom( $img['id'], $ref_gescom );

			return $img['id'];
		}
	}
	fclose($file);

	$sql = '
		insert into img_images
			( img_tnt_id, img_id, img_is_sync, img_ref_gescom )
		values
			( '.$config['tnt_id'].', 0, '.($sync ? 1 : 0).', '.( trim($ref_gescom) != '' ? '"'.addslashes( $ref_gescom ).'"' : 'null' ).' )
	';

	// Référence l'image dans la base de données
	if( !ria_mysql_query($sql) ){
		return false;
	}

	$img_id = ria_mysql_insert_id();
	img_images_set_source_name( $img_id, $srcname );
	img_images_set_type( $img_id, $srctype );

	if( !$src_filename ) {
		$src_filename = $filename;
	}

	copy($filename, $config['img_dir'].'/source/'.$img_id.'-'.basename($src_filename));

	// Enregistre le MD5 de la nouvelle image
	$file = fopen($config['img_dir'].'/source/'.$img_id.'-'.basename($src_filename), 'r');
	$filesize = filesize($filename);

	if( $filesize > 0 ){
		$content = fread($file, $filesize);
		img_images_md5_update($img_id, md5($content));
	}

	img_images_thumbnails_refresh($img_id);
	img_images_count_update($img_id);

	return $img_id;
}

/**	Cette fonction met à jour le marqueur de synchronisation d'une image.
 *	@param int $img_id Obligatoire, Identifiant de l'image.
 *	@param bool $is_sync Obligatoire, Image synchronisée oui / non.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function img_images_set_is_sync( $img_id, $is_sync ){

	if( !is_numeric($img_id) || $img_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update img_images
		set img_is_sync = '.( $is_sync ? 1 : 0 ).'
		where img_tnt_id = '.$config['tnt_id'].' and img_id = '.$img_id.'
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction met à jour la référence externe d'une image.
 *	@param int $img_id Obligatoire, Identifiant de l'image.
 *	@param string $ref_gescom Optionnel, reference externe (si vide, la référence sera supprimée)
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function img_images_set_ref_gescom( $img_id, $ref_gescom='' ){
	if( !is_numeric($img_id) || $img_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update img_images
		set img_ref_gescom = '.( trim($ref_gescom) != '' ? '"'.addslashes( $ref_gescom ).'"' : 'null' ).'
		where img_tnt_id = '.$config['tnt_id'].'
			and img_id = '.$img_id.'
	';

	return ria_mysql_query($sql);
}

/** Effectue une passe supplémentaire d'optimisation sur une image (inspirée par Yahoo Smush.it)
 *	pour améliorer son chargement. Cette méthode est testée et recommandée par Yahoo Slow.
 *	@param string $filename Chemin complet vers le fichier à optimiser
 *	@return void
 */
function img_images_optimize( $filename ){
	global $config;

	// Extrait l'extension du fichier
	$extension = preg_replace( '/^[^\.]+\./', '', basename($filename) );

	switch( $extension ){
		// Pour les images de type jpeg, compresse l'image à l'aide de jpegtran
		case 'jpg':
			if ($config['tnt_id']!=5) {
				$backgroundImagick = new Imagick(realpath($filename));
				$imagick = new Imagick();

				$imagick->setCompressionQuality(80);
				$imagick->newPseudoImage(
					$backgroundImagick->getImageWidth(),
					$backgroundImagick->getImageHeight(),
					'canvas:white'
				);

				$imagick->compositeImage(
					$backgroundImagick,
					Imagick::COMPOSITE_ATOP,
					0,
					0
				);

				$imagick->setFormat("jpg");
				$imagick->writeImage( $filename );
			}

			exec('jpegtran -copy none -optimize -perfect -outfile "'.$filename.'" "'.$filename.'"');
			break;
		// Utilise pngcrush pour optimiser le fichier. La méthode par force brute est utilisée
		// (teste toute les possibilités et sélectionne la meilleure)
		/*case 'png':
			exec('pngcrush -brute -q -ow "'.$filename.'"');
			break;*/
		// Pour l'heure, aucune optimisation en place
		case 'gif':
			break;
	}
}

/** Cette fonction permet d'obtenir des informations sur l'image source
 *	@param int $img_id Obligatoire, identifiant d'une image source
 *	@return array Un tableau contenant des informations sur l'image source :
 *				- width : largeur
 *				- height : hauteur
 *				- mime : format du fichier, ex : image/jpeg
 */
function img_images_source_infos( $img_id ){
	$sources = array(
		'width' => '',
		'height' => '',
		'format' => ''
	);

	if( !is_numeric($img_id) || $img_id<=0 ){
		return $sources;
	}

	global $config;

	$file_dir = '';
	$all = opendir( $config['img_dir'].'/source' );

	while( $file = readdir($all) ){
		if( preg_match('/^('.$img_id.')[\-\.]{1}(.*)/i',  $file) || $file == $img_id ){
			$file_dir = $config['img_dir'].'/source/'.$file;
			break;
		}
	}

	if( trim($file_dir)!='' && is_file($file_dir) ){
		$imagick = new Imagick($file_dir);

		$geo = $imagick->getImageGeometry();
		// si l'orientation est 6, le côté droit de l'image est en fait le haut
		// par rapport à l'orientation de l'apareil
		// donc on inverse le height avec le width
		// pour plus d'explication sur les valeur de rotation voir ce lien
		// http://sylvana.net/jpegcrop/exif_orientation.html
		$orientation = $imagick->getImageOrientation();

		if( is_numeric($orientation) && $orientation == 6 ){
			$width = $geo['height'];
			$height = $geo['width'];
		}else{
			$width = $geo['width'];
			$height = $geo['height'];
		}

		$sources = array(
			'width' => $width,
			'height' => $height,
			'mime' => $imagick->getImageMimeType()
		);
	}

	return $sources;
}

/** Vérifie un identifiant d'image.
 *  @param int $id Obligatoire, Identifiant de l'image à vérifier
 *  @return bool true si l'identifiant existe, false en cas d'erreur
 */
function img_images_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	return ria_mysql_num_rows(ria_mysql_query('
		select img_id from img_images
		where img_tnt_id='.$config['tnt_id'].' and img_id='.$id.' and img_date_deleted is null
	'))>0;
}

/** Cette fonction permet de récupérer l'identifiant d'une image selon un source name
 *	@param string $src_name Obligatoire, source name de l'image
 *	@return int L'identifiant de l'image (un seul), False en cas d'erreur
 */
function img_images_get_id_by_srcname( $src_name ){
	if( trim($src_name) == '' ){
		return false;
	}

	global $config;

	$sql = '
		select img_id as id
		from img_images
		where img_tnt_id = '.$config['tnt_id'].'
			and img_src_name = "'.addslashes( $src_name ).'"
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['id'];
}

/** Cette fonction permet de remplacer une image par une autre
 *	@param int $id Obligatoire, identifiant d'une image
 *	@param string $fieldname Obligatoire, nom du champ qui contient la nouvelle image à uploader
 *	@return bool Retourne true si le changement s'est correctement déroulé
 *	@return bool Retourne false dans le cas contraire
 */
function img_images_replace( $id, $fieldname ){
	if( !img_images_exists($id) ) return false;
	global $config;

	// upload la nouvelle images (vérifie que les images sont bien différentes)
	$new_id = img_images_upload( $fieldname );
	if( !$new_id ) return false;
	elseif( $new_id==$id ) return true;

	img_images_count_update( $id );

	// met à jour les appels à l'ancien ID avec le nouvel ID de l'image
	ria_mysql_query( 'update dlv_stores_images set img_id='.$new_id.' where img_tnt_id='.$config['tnt_id'].' and img_id='.$id ); // magasin
	ria_mysql_query( 'update prd_cat_images set img_id='.$new_id.' where img_tnt_id='.$config['tnt_id'].' and img_id='.$id ); // catégorie
	ria_mysql_query( 'update prd_products set prd_img_id='.$new_id.' where prd_tnt_id='.$config['tnt_id'].' and prd_img_id='.$id ); // produit (principale)
	ria_mysql_query( 'update prd_brands set brd_img_id='.$new_id.' where brd_tnt_id='.$config['tnt_id'].' and brd_img_id='.$id ); // marque
	ria_mysql_query( 'update news_img set ni_img_id='.$new_id.' where ni_tnt_id='.$config['tnt_id'].' and ni_img_id='.$id ); // actualité
	ria_mysql_query( 'update search_contents set cnt_img_id='.$new_id.' where cnt_tnt_id='.$config['tnt_id'].' and cnt_img_id='.$id ); // Contenu de recherche
	ria_mysql_query( 'update cms_img set ci_img_id='.$new_id.' where ci_tnt_id='.$config['tnt_id'].' and ci_img_id='.$id ); // CMS
	ria_mysql_query( 'update gu_messages_images set msi_img_id='.$new_id.' where msi_tnt_id='.$config['tnt_id'].' and msi_img_id='.$id ); // CMS
	ria_mysql_query( 'update gu_users set usr_img_id='.$new_id.' where usr_tnt_id='.$config['tnt_id'].' and usr_img_id='.$id ); // avatar
	ria_mysql_query( 'update img_images_objects set imo_img_id='.$new_id.' where imo_tnt_id='.$config['tnt_id'].' and imo_img_id='.$id ); // association image / objet

	// met à jour l'ID de l'image dans la description des CMS
	$rcms = ria_mysql_query( 'select cat_id, cat_desc from cms_categories where cat_tnt_id='.$config['tnt_id'].' and cat_desc like \'%'.$id.'.%\' and date_deleted is null');
	if( $rcms ){
		while( $cms = ria_mysql_fetch_array($rcms) ){
			$desc = preg_replace( '/(.*)(\/'.$id.'\.)(jpg|png|gif)(.*)/i' , '$1/'.$new_id.'.$3$4', $cms['cat_desc'] );
			ria_mysql_query( 'update cms_categories set cat_desc=\''.addslashes($desc).'\' where cat_tnt_id='.$config['tnt_id'].' and cat_id='.$cms['cat_id'] );
		}
	}

	// met à jour l'ID de l'image dans la description des magasin
	$rstr = ria_mysql_query( '
		select str_id, str_desc from dlv_stores where str_tnt_id ='.$config['tnt_id'].' and str_desc like "%'.$id.'.%" and str_date_deleted is null' );

	if( $rstr ){
		while( $str = ria_mysql_fetch_array($rstr) ){
			$desc = preg_replace( '/(.*)(\/'.$id.'\.)(jpg|png|gif)(.*)/i' , '$1/'.$new_id.'.$3$4', $str['str_desc'] );
			ria_mysql_query( 'update dlv_stores set str_desc=\''.addslashes($desc).'\' where str_tnt_id='.$config['tnt_id'].' and str_id='.$str['str_id'] );
		}
	}

	img_images_del( $id, true );
	img_images_count_update( $new_id );
	return $new_id;
}

/** Permet la suppression virtuelle d'une image
 *	@param int $id Identifiant de l'image à supprimer
 *	@param bool $deleted Facultatif, confirmation de suppression (par défaut, se contente de mettre à jour le nombre d'utilisations)
 *	@param bool $sync Facultatif, si l'image est synchronisée et le paramètre à false, elle ne sera pas supprimée
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function img_images_del( $id, $deleted=false, $sync=false ){
	global $config;

	if( $deleted ){
		$rimg = img_images_get( $id );
		if( !$rimg || !ria_mysql_num_rows($rimg) ) return false;
		$img = ria_mysql_fetch_array( $rimg );

		//if( !$sync && $img['is_sync'] ) return false;

		foreach( $config['img_sizes'] as $code=>$size ){
			@unlink( $config['img_dir'].'/'.$size['dir'].'/'.$id.'.jpg' );
			@unlink( $config['img_dir'].'/'.$size['dir'].'/'.$id.'.png' );
			@unlink( $config['img_dir'].'/'.$size['dir'].'/'.$id.'.gif' );
		}

		// Dissocie l'image des possibles contenus associés
		{
			// Produits
			ria_mysql_query( 'update prd_products set prd_img_id=null where prd_tnt_id='.$config['tnt_id'].' and prd_img_id='.$id );

			// Catégories de produits
			ria_mysql_query( 'delete from prd_cat_images where img_tnt_id='.$config['tnt_id'].' and img_id='.$id );

			// Marques
			ria_mysql_query( 'update prd_brands set brd_img_id=null where brd_tnt_id='.$config['tnt_id'].' and brd_img_id='.$id );

			// Magasins
			ria_mysql_query( 'delete from dlv_stores_images where img_tnt_id='.$config['tnt_id'].' and img_id='.$id );

			// Pages de contenu
			ria_mysql_query( 'delete from cms_img where ci_tnt_id='.$config['tnt_id'].' and ci_img_id='.$id );

			// Actualités
			ria_mysql_query( 'update news set news_img_id=null where news_tnt_id='.$config['tnt_id'].' and news_img_id='.$id );
			ria_mysql_query( 'delete from news_img where ni_tnt_id='.$config['tnt_id'].' and ni_img_id='.$id );

			// Clients
			ria_mysql_query( 'update gu_users set usr_img_id=null where usr_tnt_id='.$config['tnt_id'].' and usr_img_id='.$id );
		}
		{
			$r_search = ria_mysql_query('
				select cnt_id, cnt_tag, cnt_type_id
				from search_contents
				where cnt_tnt_id = '.$config['tnt_id'].'
					and cnt_publish=1
					and cnt_img_id='.$id.'
			');

			if ($r_search && ria_mysql_num_rows($r_search)) {
				$type_id_to_cls_id = array(
					2 => CLS_PRODUCT,
					3 => CLS_USER,
					1 => CLS_CATEGORY,
					9 => CLS_STORE,
					10 => CLS_CMS,
				);
				$array_of_cnt_ids = array();
				while($search = ria_mysql_fetch_assoc($r_search)) {
					if (!array_key_exists($search['cnt_type_id'], $type_id_to_cls_id)) {
						continue;
					}
					$cls_id = $type_id_to_cls_id[$search['cnt_type_id']];
					$array_of_cnt_ids[] = $search['cnt_id'];

					try{
						// Index le contenu dans le moteur de recherche.
						RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
							'cls_id' => $cls_id,
							'obj_id_0' => $search['cnt_tag'],
						));
					}catch(Exception $e){
						error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
					}
				}

				search_contents_image_del($array_of_cnt_ids);
			}
		}
		ria_mysql_query( 'update img_images_objects set imo_date_deleted=now() where imo_tnt_id='.$config['tnt_id'].' and imo_img_id='.$id );
		return ria_mysql_query('update img_images set img_date_deleted=now(), img_count=0 where img_tnt_id='.$config['tnt_id'].' and img_id='.$id);
	}

	return img_images_count_update($id);
}

/** Tente de créer les dossiers de stockage des images.
 *	Chaque format d'image dispose d'un dossier de stockage différent, nommé width x height.
 *
 *	@return bool Cette fonction retourne true si tous les dossiers existe déjà ou si les dossiers manquants ont pu être créés. Elle retourne false si un dossier n'a pas pu être créé.
 *
 */
function img_images_create_dirs(){
	global $config;

	foreach( $config['img_sizes'] as $code=>$size ){
		$dir = $config['img_dir'].'/'.$size['dir'];
		if (file_exists($dir)) {
			continue;
		}

		mkdir( $dir, 0755 );
		chgrp( $dir, 'apache' );
		chown( $dir, 'apache' );
	}

	return true;
}

/**	Cette fonction permet le chargement des images utilisées dans la boutique en ligne.
 *	@param int $id Facultatif, Identifiant ou tableau d'identifiants d'images précises (récupère les images virtuellement supprimées)
 *	@param string $md5 Facultatif, MD5 du contenu de l'image
 *	@param string $src_name Facultatif, Nom original de l'image importée
 *	@param string $type Facultatif, Extension du type de l'image
 *	@param string $name Facultatif, partie du nom source
 *	@param int $count Facultatif, Nombre maximal d'utilisations
 *	@param bool $have_src_name Facultatif, Par défaut les images sont retournées même si elles n'ont pas de nom, mettre true pour le cas contraire
 *	@param string $content Facultatif, Mettre un type de contenu pour récupérer les images qui leurs sont rattachées type accepté : prd, prd-cat, str, cms ou news
 *	@param bool $search_name_deep Facultatif, détermine si les paramètres $name et $src_name entrainent une recherche dans l'historique des noms, ou seulement le nom courant.
 *	@param bool $is_associated Facultatif, true pour récupérer les images ayant déjà été associée, False pour les autre (par défaut on ne tient pas compte de ce paramètre)
 *	@param array|false $sort Optionnel, paramètre de tri (valeurs acceptée : id, src_name, date_modified, set => respecte le tri de $id)
 *	@param bool $is_masked Facultatif, indique si l'on souhaite retourne les images masquées (true) ou seulement les images non masquées (false). Par défaut, toutes les images sont retournées, qu'elles soient masquées ou non (null).
 *	@param bool $is_sync Facultatif, permet de filtre toutes les images synchronisés
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de l'image
 *			- src_name : nom d'origine de l'image
 *			- type : format de l'image (jpg, png, gif...)
 *			- is_sync : image synchronisée oui / non
 *			- ref_gescom : référence externe de l'image
 *			- is_associated : image ayant déjà été associée à un contenu
 *			- date_modified : date de dernière modification (format EN)
 *			- is_masked : si oui ou non l'image doit être masquée dans la fonctionnalité "Associations automatiques"
 *			- md5_content : md5 du contenu de l'image
 */
function img_images_get( $id=0, $md5='', $src_name='', $type='', $name='', $count=null, $have_src_name=false, $content=false, $search_name_deep=false, $is_associated=null, $sort=false, $is_masked=null, $is_sync=null ){

	if( $content !== false && !in_array($content, array('prd', 'prd-cat', 'str', 'cms', 'news')) ){
		return false;
	}

	if( is_array($id) ){
		foreach( $id as $one_id ){
			if( !is_numeric($one_id) || $one_id <= 0 ){
				return false;
			}
		}
	}elseif( !is_numeric($id) || $id < 0 ){
		return false;
	}elseif( $id ){
		$id = array($id);
	}else{
		$id = array();
	}

	$md5 = trim($md5);
	$src_name = trim($src_name);
	$type = strtolower(trim($type));
	$name = trim($name);

	// La recherche par nom ne fonctionne pas si la recherche contient l'extension.
	$name = str_replace( array('.jpg','.png','.gif','.pdf'), '', $name );

	global $config;

	$sql = '
		select img_id as id, img_src_name as src_name, img_type as type, img_is_sync as is_sync, img_ref_gescom as ref_gescom,
			img_is_associated as is_associated, img_date_modified as date_modified, img_is_masked as is_masked, img_md5 as md5_content
		from img_images as img
		where img_tnt_id = '.$config['tnt_id'].'
	';

	if( $content !== false ){
		switch( $content ){
			case 'prd' : {
				$sql .= '
					and (
						exists (
							select 1
							from img_images_objects
							where imo_tnt_id = '.$config['tnt_id'].'
								and imo_date_deleted is null
								and imo_cls_id = ' . CLS_PRODUCT . '
								and imo_img_id = img_id
						)
					)
				';
				break;
			}
			case 'prd-cat' : {
				$sql .= '
					and exists (
						select 1
						from prd_cat_images as cimg
							join prd_categories on cimg.img_tnt_id = cat_tnt_id and img_cat_id = cat_id
						where cimg.img_tnt_id = '.$config['tnt_id'].'
							and cimg.img_id = img.img_id
							and cat_date_deleted is null
					)
				';
				break;
			}
			case 'str' : {
				$sql .= '
					and exists (
						select 1
						from dlv_stores_images as dimg
							join dlv_stores on dimg.img_tnt_id = str_tnt_id and img_str_id = str_id
						where dimg.img_tnt_id = '.$config['tnt_id'].'
							and dimg.img_id = img.img_id
							and str_date_deleted is null
					)
				';
				break;
			}
			case 'cms' : {
				$sql .= '
					and exists (
						select 1
						from cms_img
							join cms_categories on ci_tnt_id = cat_tnt_id and ci_cat_id = cat_id
						where ci_tnt_id = '.$config['tnt_id'].'
							and ci_img_id = img_id
							and date_deleted is null
					)
				';
				break;
			}
			case 'news' : {
				$sql .= '
					and (
						exists (
							select 1
							from news
							where news_tnt_id = '.$config['tnt_id'].'
								and news_img_id = img_id
						) or exists (
							select 1
							from news_img
							where ni_tnt_id = '.$config['tnt_id'].'
								and ni_img_id = img_id
						)
					)
				';
				break;
			}
		}
	}

	if( sizeof($id) ){
		$sql .= ' and img_id in ('.implode(', ', $id).')';
	}else{
		$sql .= ' and img_date_deleted is null';
		if( $md5 !== '' ){
			$sql .= ' and img_md5 = "'.addslashes($md5).'"';
		}
		if( $src_name != '' ){
			if( $search_name_deep ){
				$sql .= '
					and exists (
						select 1 from img_image_names
						where imn_tnt_id = '.$config['tnt_id'].' and imn_img_id = img_id
						and imn_name = "'.addslashes($src_name).'"
					)
				';
			}else{
				$sql .= ' and img_src_name = "'.addslashes($src_name).'"';
			}
		}
		if( $type !== '' ){
			$sql .= ' and img_type = "'.addslashes($type).'"';
		}
	}

	if( $count !== null ){
		$sql .= ' and img_count <= '.$count;
	}

	if( $name != '' ){
		if( $search_name_deep ){
			$sql .= '
				and exists (
					select 1 from img_image_names
					where imn_tnt_id = '.$config['tnt_id'].' and imn_img_id = img_id
					and lower(imn_name) like lower("%'.addslashes($name).'%")
				)
			';
		}else{
			$sql .= ' and lower(img_src_name) like lower("%'.addslashes($name).'%")';
		}
	}

	if( $have_src_name ){
		$sql .= ' and img_src_name is not null and img_src_name != ""';
	}

	if( $is_associated!==null ){
		if( $is_associated ){
			$sql .= ' and img_is_associated=1';
		}else{
			$sql .= ' and img_is_associated=0';
		}
	}

	if( $is_masked!==null ){
		if( $is_masked ){
			$sql .= ' and img_is_masked=1';
		}else{
			$sql .= ' and img_is_masked=0';
		}
	}

	if( $is_sync!==null ){
		if( $is_sync ){
			$sql .= ' and img_is_sync=1';
		}else{
			$sql .= ' and img_is_sync=0';
		}
	}

	$sort_final = array();
	if( is_array($sort) && sizeof($sort) ){
		// Récupère un éventuel tri par identifiant ou nom du fichier
		foreach( $sort as $col=>$dir ){
			switch( $col ){
				case 'id':
					$sort_final[] = 'img_id '.$dir;
					break;
				case 'src_name':
					$sort_final[] = 'img_src_name '.$dir;
					break;
				case 'date_modified':
					$sort_final[] = 'img_date_modified '.$dir;
					break;
				case 'set':
					$query_orderby = sql_order_by_array( $id, 'img_id', $dir );
					if( trim($query_orderby)!='' ){
						$sort_final[] = $query_orderby;
					}
					break;
			}
		}
	}

	if( sizeof($sort_final)==0 ) $sort_final = array( 'img_id asc' );

	$sql .= ' order by '.implode(', ', $sort_final );

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql);
	}

	return $r;

}

/** Cette fonction permet de mettre à jour le MD5 d'une image
 *
 *	@param int $id Obligatoire, identifiant d'une image
 *	@param string $md5 Obligatoire, md5 de l'image
 *
 *	@return bool Retourne true si la mise à jour a bien fonctionnée
 *	@return bool Retourne false dans le cas contraire ou bien si l'un des paramètres est faux
 *
 */
function img_images_md5_update( $id, $md5 ){
	if( !img_images_exists($id) ) return false;
	if( $md5=='' ) return false;
	global $config;

	return ria_mysql_query('update img_images set img_md5="'.$md5.'" where img_tnt_id='.$config['tnt_id'].' and img_id='.$id);
}

/** Cette fonction permet de mettre à jour le nom original d'une image
 *	@param int $id Identifiant de l'image
 *	@param string $src_name Nom de la source, chaîne vide permet d'assigner la valeur NULL
 *
 *	@return bool true ou False suivent le succès ou l'échec de l'opération
 */
function img_images_set_source_name( $id, $src_name ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}
	$src_name = trim($src_name);

	global $config;

	$sql = '
		update img_images
		set
			img_src_name = '.( $src_name == '' ? 'NULL' : '"'.addslashes($src_name).'"' ).'
		where
			img_tnt_id = '.$config['tnt_id'].' and img_id = '.$id.'
	';

	$r = ria_mysql_query($sql);

	if( $r && $src_name != '' ){
		// historique des noms
		img_image_names_add( $id, $src_name );
	}

	return $r;

}

/** Cette fonction permet de mettre à jour l'extension originale d'une image
 *	@param int $id Obligatoire, Identifiant de l'image
 *	@param string $type Obligatoire, Extension. Chaîne vide permet d'assigner la valeur NULL
 *
 *	@return bool true ou False suivent le succès ou l'échec de l'opération
 */
function img_images_set_type( $id, $type ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}
	$type = strtolower( trim( $type ) );

	return ria_mysql_query('
		update img_images set img_type='.( $type=='' ? 'NULL' : '\''.addslashes($type).'\'' ).'
		where img_tnt_id='.$config['tnt_id'].' and img_id='.$id
	);
}

/** Cette fonction permet de mettre à jour l'information pour savoir si une image a déjà été associée
 *	@param int $id Obligatoire, Identifiant de l'image
 *	@param bool $is_associated Facultatif, Si oui ou non l'image a déjà été associée. Valeur par défaut : true.
 *
 *	@return bool true ou False suivent le succès ou l'échec de l'opération
 */
function img_images_set_is_associated( $id, $is_associated=true ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	return ria_mysql_query('
		update img_images set img_is_associated='.( $is_associated ? 1 : 0 ).'
		where img_tnt_id='.$config['tnt_id'].' and img_id='.$id
	);
}

/** Cette fonction permet de mettre à jour l'information pour savoir si une image est désactiver manuellement ou non de l'association automatique.
 *	@param int $id Obligatoire, Identifiant de l'image
 *	@param bool $is_masked Obligatoire, si oui ou non l'image a déjà été associée
 *
 *	@return bool true ou False suivent le succès ou l'échec de l'opération
 */
function img_images_set_is_masked( $id, $is_masked ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	return ria_mysql_query('
		update img_images set img_is_masked='.( $is_masked ? 1 : 0 ).'
		where img_tnt_id='.$config['tnt_id'].' and img_id='.$id
	);
}

/** Cette fonction permet de mettre à jour le nombre de fois ou une image est utilisée.
 *	@param int $id Facultatif, identifiant ou tableau d'identifiants d'image
 *	@param int $prd Facultatif, identifiant d'un produit
 *	@return bool true en cas de succès
 *	@return bool false en cas d'erreur
 */
function img_images_count_update( $id=0, $prd=0 ){
	global $config;

	if( $id!=0 ){
		$images = img_images_get($id);
	}elseif( $prd!=0 ){
		$images = img_images_objects_get(0, $prd, 0, 0, 0, $id, CLS_PRODUCT, true);
	}else{
		$images = img_images_get();
	}

	// Tableau contenant les différentes descriptions pouvant contenir une image
	$desc_array = array();

	// récupération des CMS
	{
		$sql = '
			select cat_desc as "desc"
			from cms_categories
			where cat_tnt_id = '.$config['tnt_id'].'
				and date_deleted is null
				and cat_desc!=""
		';

		if( $r_cms = ria_mysql_query($sql) ){
			while( $cms = ria_mysql_fetch_array($r_cms) ){
				$desc_array[] = strtolower($cms['desc']);
			}
		}elseif( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
	}

	// récupération des magasins
	{
		$sql = '
			select str_desc as "desc"
			from dlv_stores
			where str_tnt_id ='.$config['tnt_id'].'
				and str_date_deleted is null
				and str_desc!=""
		';

		if( $r_stores = ria_mysql_query($sql) ){
			while( $str = ria_mysql_fetch_array($r_stores) ){
				$desc_array[] = strtolower($str['desc']);
			}
		}elseif( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
	}

	// récupération des produits
	{
		$sql = '
			select prd_desc_long as "desc-long"
			from prd_products
			where prd_tnt_id='.$config['tnt_id'].'
				and prd_date_deleted is null
				and prd_desc_long!=""
		';

		if( $r_prd = ria_mysql_query($sql) ){
			while( $prd = ria_mysql_fetch_array($r_prd) ){
				$desc_array[] = strtolower($prd['desc-long']);
			}
		}elseif( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
	}

	// récupération des catégories
	{
		$sql = '
			select cat_desc as "desc"
			from prd_categories
			where cat_tnt_id='.$config['tnt_id'].'
				and cat_date_deleted is null
				and cat_desc!=""
		';

		if( $r_cat = ria_mysql_query($sql) ){
			while( $cat = ria_mysql_fetch_array($r_cat) ){
				$desc_array[] = strtolower($cat['desc']);
			}
		}elseif( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
	}

	// récupération des actualités
	{
		$sql = '
			select news_desc as "desc"
			from news
			where news_tnt_id='.$config['tnt_id'].'
				and news_desc!=""
		';

		if( $r_news = ria_mysql_query($sql) ){
			while( $news = ria_mysql_fetch_array($r_news) ){
				$desc_array[] = strtolower($news['desc']);
			}
		}elseif( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		}
	}

	// Récupère les champs avancés de type TEXT LONG utilisant TinyMCE pour leur édition
	{
		$ar_fld = array();

		$r_fld = ria_mysql_query('
			select fld_id
			from fld_fields
			where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].')
				and fld_date_deleted is null
				and fld_type_id='.FLD_TYPE_TEXTAREA.'
				and fld_old_txt_type=0
				and fld_is_physical=0
		');

		if( $r_fld && ria_mysql_num_rows($r_fld) ){
			while( $fld = ria_mysql_fetch_array($r_fld) ){
				$ar_fld[] = $fld['fld_id'];
			}
		}

		if( sizeof($ar_fld) ){
			$r_fld_imgs = ria_mysql_query('
				select pv_value
				from fld_object_values
				where pv_tnt_id='.$config['tnt_id'].'
					and pv_fld_id in ('.implode(', ', $ar_fld).')
			');

			if( $r_fld_imgs && ria_mysql_num_rows($r_fld_imgs) ){
				while( $r = ria_mysql_fetch_array($r_fld_imgs) ){
					$desc_array[] = $r['pv_value'];
				}
			}
		}
	}

	// Récupère les identifiants de champs avancés de type IMAGE puis les identifiants des images contenus dans les champs avancés
	{
		$ar_fld = array();
		$fld_imgs = array();

		$r_fld = ria_mysql_query('
			select fld_id
			from fld_fields
			where (fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].')
				and fld_date_deleted is null
				and fld_type_id='.FLD_TYPE_IMAGE.'
				and fld_is_physical=0
		');

		if( $r_fld && ria_mysql_num_rows($r_fld) ){
			while( $fld = ria_mysql_fetch_array($r_fld) ){
				$ar_fld[] = $fld['fld_id'];
			}
		}

		if( sizeof($ar_fld) ){
			$r_fld_imgs = ria_mysql_query('
				select trim(pv_value) as id
				from fld_object_values
				where pv_tnt_id='.$config['tnt_id'].'
					and pv_fld_id in ('.implode(', ', $ar_fld).')
			');

			if( $r_fld_imgs && ria_mysql_num_rows($r_fld_imgs) ){
				while( $r = ria_mysql_fetch_array($r_fld_imgs) ){
					if( is_numeric($r['id']) && $r['id'] ){
						if( !isset($fld_imgs[ $r['id'] ]) ){
							$fld_imgs[ $r['id'] ] = 0;
						}

						$fld_imgs[ $r['id'] ] = $fld_imgs[ $r['id'] ] + 1;
					}
				}
			}
		}
	}

	if( $images ){
		while( $img = ria_mysql_fetch_array($images) ){
			$count = 0;

			$sql = '
				select sum(nombre) as total_count from (
					select count(*) as nombre from dlv_stores_images
					join dlv_stores on ( img_tnt_id=str_tnt_id and img_str_id=str_id )
					where img_tnt_id='.$config['tnt_id'].' and img_id='.$img['id'].' and str_date_deleted is null

					union all

					select count(*) as nombre from prd_cat_images
					join prd_categories on ( img_tnt_id=cat_tnt_id and img_cat_id=cat_id )
					where img_tnt_id='.$config['tnt_id'].' and img_id='.$img['id'].' and cat_date_deleted is null

					union all

					select count(*) as nombre from prd_brands
					where brd_tnt_id='.$config['tnt_id'].' and brd_img_id='.$img['id'].' and brd_date_deleted is null

					union all

					select count(*) as nombre from news_img
					join news on ( ni_tnt_id=news_tnt_id and ni_news_id=news_id )
					where ni_tnt_id='.$config['tnt_id'].' and ni_img_id='.$img['id'].'

					union all

					select count(*) as nombre from cms_img
					join cms_categories on ( ci_tnt_id=cat_tnt_id and ci_cat_id=cat_id and ci_wst_id=cat_wst_id )
					where ci_tnt_id='.$config['tnt_id'].' and ci_img_id='.$img['id'].' and date_deleted is null

					union all

					select count(*) as nombre from gu_users
					where usr_tnt_id='.$config['tnt_id'].' and usr_img_id='.$img['id'].' and usr_date_deleted is null

					union all

					select count(*) as nombre from
					(
						select distinct prd_id from
						(
							select prd_id as prd_id
							from prd_products
							where prd_tnt_id='.$config['tnt_id'].' and prd_img_id='.$img['id'].' and prd_date_deleted is null

							UNION ALL

							select imo_obj_id_0 as prd_id
							from img_images_objects
							where imo_tnt_id='.$config['tnt_id'].' and imo_img_id='.$img['id'].' and imo_date_deleted is null and imo_cls_id = 1 and imo_is_main = 1
						) as tmpPrd
					) as tmpPrdDistinct

					union all

					select count(*) as nombre from gu_messages_images
					join gu_messages on ( msi_tnt_id=cnt_tnt_id and msi_cnt_id=cnt_id )
					where msi_tnt_id='.$config['tnt_id'].' and msi_img_id='.$img['id'].' and cnt_date_delete is null

					union all

					select count(*) as nombre from img_images_objects
					where imo_tnt_id='.$config['tnt_id'].' and imo_img_id='.$img['id'].' and imo_date_deleted is null
				) as result
			';

			$r = ria_mysql_query($sql);

			if( $r && ria_mysql_num_rows($r) ){
				$rfetch = ria_mysql_fetch_array($r);
				$count = $rfetch['total_count'];
			}elseif( ria_mysql_errno() ){
				error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
			}

			// Recherche de l'image dans les différentes description avec TinyMCE
			foreach( $desc_array as $desc ){
				$count += substr_count( $desc, $img['id'].'.jpg' );
				$count += substr_count( $desc, $img['id'].'.gif' );
				$count += substr_count( $desc, $img['id'].'.png' );
			}

			// Utilisation de l'image via un champ de type "IMAGE"
			if( isset($fld_imgs[ $img['id'] ]) ){
				$count += $fld_imgs[ $img['id'] ];
			}

			$sql = '
				update img_images set img_count='.$count.', img_is_associated='.( $count>1 ? '1' : '0' ).'
				where img_tnt_id='.$config['tnt_id'].' and img_id='.$img['id']
			;

			if( !ria_mysql_query($sql) ){
				if( ria_mysql_errno() ){
					error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
				}
				return false;
			}

		}
	}

	return true;
}

/** Cette fonction permet de récupérer les identifiants d'image contenu dans une description éditable par TinyMCE.
 *	@param string $desc Obligatoire, description à analyser
 *	@return array Un tableau contenant tous les identifiants d'images
 */
function img_images_get_from_riawysiwyg( $desc ){
	$imgs = array();

	if( trim($desc)=='' ){
		return $imgs;
	}

	preg_match_all( '/src="\/images\/products\/([0-9]+x[0-9]+)\/([0-9]+)\.(jpg|png|gif){1}"/i', $desc , $matches );
	if( isset($matches[2]) ){
		$imgs = $matches[2];
	}

	return array_unique( $imgs );
}

/** Cette fonction permet de mettre à jour le nombre d'utilisation des images contenus dans une description éditable par TinyMCE.
 *	@param string $desc Obligatoire, description à analyser
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function img_images_update_from_riawysiwyg( $desc ){
	if( trim($desc)=='' ){
		return true;
	}

	$imgs = img_images_get_from_riawysiwyg( $desc );
	if( is_array($imgs) && sizeof($imgs) ){
		return img_images_count_update( $imgs );
	}

	return true;
}

/** Cette fonction récupère le nombre d'utilisations d'une image donnée
 *	@param int $id Identifiant de l'image
 *	@param bool $deleted Optionnel : si True, les images supprimées seront incluses dans la recherche
 *
 *	@return bool False en cas d'échec, nombre d'utilisations en cas de succès
 */
function img_images_get_count( $id, $deleted=false ){
	global $config;

	if( !isset($id) || $id<=0 ) return false;

	$rimg = ria_mysql_query( 'select img_count from img_images where img_tnt_id='.$config['tnt_id'].' and img_id='.$id.( $deleted ? '' : ' and img_date_deleted is null' ) );

	if( !$rimg || !ria_mysql_num_rows($rimg) ) return false;

	return ria_mysql_result( $rimg, 0, 0 );
}

/** Cette fonction retourne les identifiant de contenu rattachés à une image, selon la classe choisie
 *	@param int $id Obligatoire, identifiant d'une image
 *	@param int $class Obligatoire, identifiant d'une classe de contenu
 *	@param int|array $obj Optionnel, identifiant ou tableau d'identifiants d'objets sur lesquels filtrer le résultat
 *	@return array Retourne un tableau contenant les identifiant des contenus
 */
function img_images_get_by_class( $id, $class, $obj=0 ){
	if( !img_images_exists($id) ) return false;
	if( !fld_classes_exists($class) ) return false;

	$obj = control_array_integer($obj, false);
	if ($obj === false) {
		return false;
	}

	global $config;

	$sql = false;
	switch( $class ){
		case CLS_CATEGORY:
			$sql = '
				select img_cat_id as id
				from prd_cat_images
				join prd_categories on (cat_tnt_id=img_tnt_id and cat_id=img_cat_id and cat_date_deleted is null)
				where img_tnt_id='.$config['tnt_id'].' and img_id='.$id.'
					'.( count($obj) ? ' and img_cat_id in ('.implode(', ', $obj).')' : '' ).'
			'; break;
		case CLS_BRAND:
			$sql = '
				select brd_id as id
				from prd_brands
				where brd_tnt_id='.$config['tnt_id'].' and brd_date_deleted is null and brd_img_id='.$id.'
					'.( count($obj) ? ' and brd_id in ('.implode(', ', $obj).')' : '' ).'
			'; break;
		case CLS_STORE:
			$sql = '
				select img_str_id as id
				from dlv_stores_images
				where img_tnt_id='.$config['tnt_id'].' and img_id='.$id.'
					'.( count($obj) ? ' and img_str_id in ('.implode(', ', $obj).')' : '' ).'
			'; break;
		case CLS_CMS:
			$sql = '
				select ci_cat_id as id
				from cms_img
					join cms_categories on (ci_tnt_id=cat_tnt_id and ci_cat_id=cat_id)
				where ci_tnt_id='.$config['tnt_id'].' and ci_img_id='.$id.' and date_deleted is null
					'.( count($obj) ? ' and ci_cat_id in ('.implode(', ', $obj).')' : '' ).'
			'; break;
		case CLS_NEWS:
			$sql = '
				select news_id as id
				from news
				where news_tnt_id='.$config['tnt_id'].'
					and ( news_img_id='.$id.' or news_id in (select ni_news_id from news_img where ni_tnt_id='.$config['tnt_id'].' and ni_img_id='.$id.') )
					'.( count($obj) ? ' and news_id in ('.implode(', ', $obj).')' : '' ).'
			'; break;
		case CLS_USER:
			// Image principale du compte client
			$sql = '
				select usr_id as id from gu_users
				where usr_tnt_id='.$config['tnt_id'].'
					and usr_img_id='.$id.'
			';
			if( count($obj) ){
				$sql .= ' and usr_id in ('. implode( ',', $obj ) .')';
			};
			// Images secondaires du compte client
			$sql .= '
				union

				select imo_obj_id_0 as id
				from img_images_objects
				where imo_tnt_id='.$config['tnt_id'].'
					and imo_img_id='.$id.'
					and imo_date_deleted is null
					and imo_cls_id = ' . $class . '
					'.( count($obj) ? ' and imo_obj_id_0 in ('.implode(', ', $obj).')' : '' ).'
			';
			break;
		case CLS_PRODUCT:
			$sql = '
				select imo_obj_id_0 as id
				from img_images_objects
				where imo_tnt_id='.$config['tnt_id'].'
					and imo_img_id='.$id.'
					and imo_date_deleted is null
					and imo_cls_id = ' . $class . '
					'.( count($obj) ? ' and imo_obj_id_0 in ('.implode(', ', $obj).')' : '' ).'
			';
			break;
	}

	if( $sql ){
		$res = ria_mysql_query( $sql );

		if( $res ){
			$ids_img = array();
			while( $r = ria_mysql_fetch_array($res) ){
				$ids_img[] = $r['id'];
			}

			return sizeof( $ids_img )>0 ? $ids_img : false;
		}
	}

	return false;
}

/** Cette fonction permet de récupérer le fichier source d'une image
 *	@param int $id Obligatoire, identifiant d'une image
 *	@return bool Retourne false si le fichier n'existe pas
 *	@return string Retourne l'adresse vers le fichier
 */
function img_images_get_filesource( $id ){
	global $config;

	$d = dir( $config['img_dir'].'/source' );
	while( false!==($entry = $d->read()) ){
		if( preg_match( '/^'.$id.'-/', $entry ) )
			return $entry;
	}

	return false;
}

/** Cette fonction retourne du code html des liens faits entre une image et ses contenu
 *	@param int $img Obligatoire, identifiant d'une image
 *	@param int $class Obligatoire, identifiant de la classe des contenus
 *	@return string Retourne du code html
 */
function img_images_view_linked( $img, $class ){
	global $config;

	// Vérifie que la classe est activée
	switch( $class ){
		case CLS_PRODUCT:
			if( !gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT') ){
				return '';
			}
			break;
		case CLS_CATEGORY:
			if( !gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG') ){
				return '';
			}
			break;
		case CLS_BRAND:
			if( !gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND') ){
				return '';
			}
			break;
		case CLS_STORE:
			if( !gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE') ){
				return '';
			}
			break;
		case CLS_CMS:
			if( !gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS') ){
				return '';
			}
			break;
		case CLS_NEWS:
			if( !gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS') ){
				return '';
			}
			break;
		case CLS_USER:
			if( !gu_user_is_authorized('_RGH_ADMIN_CUSTOMER') ){
				return '';
			}
			break;
	}

	$img = ria_mysql_fetch_array( img_images_get($img) );

	$title = '';
	switch( $class ){
		case CLS_PRODUCT: $title = _('Produits'); break;
		case CLS_CATEGORY: $title = _('Catégories'); break;
		case CLS_BRAND: $title = _('Marques'); break;
		case CLS_STORE: $title = _('Magasins'); break;
		case CLS_CMS: $title = _('Contenus'); break;
		case CLS_NEWS: $title = _('Actualités'); break;
		case CLS_USER: $title = _('Clients'); break;
	}

	$can_edit = true;
	$can_read = true;
	switch( $class ){
		case CLS_PRODUCT :
			$can_edit = gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_PRD');
			$can_read = gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW');
			break;
		case CLS_CATEGORY :
			$can_edit = gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_CAT');
			$can_read = gu_user_is_authorized('_RGH_ADMIN_CATALOG_CATEG_VIEW');
			break;
		case CLS_BRAND :
			$can_edit = gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_BRD');
			$can_read = gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND_VIEW');
			break;
		case CLS_STORE :
			$can_edit = gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_STR');
			$can_read = gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_VIEW');
			break;
		case CLS_CMS :
			$can_edit = gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_CMS');
			$can_read = gu_user_is_authorized('_RGH_ADMIN_TOOL_CMS_VIEW');
			break;
		case CLS_NEWS :
			$can_edit = gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_NEWS');
			$can_read = gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_VIEW');
			break;
		case CLS_USER :
			$can_edit = gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_USR');
			$can_read = gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_VIEW');
	}


	$html = '	<div class="linked-img">';
	$html .= '		<div class="name-links">
						<div class="name">'.htmlspecialchars( $title ).'</div>';
	if( $can_edit ){
		$html .= '			<input type="button" name="add" id="add-'.$class.'" value="'._('Ajouter').'" />';
	}
	$html .= '			</div>';
	$html .= '		<div class="links">';

	$ar_ids = img_images_get_by_class( $img['id'], $class );

	$ar_contenu = array();
	if( is_array($ar_ids) && sizeof($ar_ids) ){
		foreach( $ar_ids as $id ){
			switch( $class ){
				case CLS_PRODUCT:
					$rp = prd_products_get_simple( $id );
					if( $rp && ria_mysql_num_rows($rp) ){
						$p = ria_mysql_fetch_array( $rp );

						$url = '';
						$rcat = prd_products_categories_get($id);
						if( $rcat && ria_mysql_num_rows($rcat) ){
							$cat = ria_mysql_fetch_array( $rcat );
							$url = '/admin/catalog/product.php?cat='.$cat['cat'].'&amp;prd='.$id;
						}else{
							$url = '/admin/catalog/product.php?cat=0&amp;prd='.$id;
						}

						$ar_contenu[ $id ] = array( 'name'=>($p['name']), 'desc'=>($p['desc']), 'url'=>$url );
					}
					break;
				case CLS_CATEGORY:
					$rc = prd_categories_get( $id );
					if( $rc && ria_mysql_num_rows($rc) ){
						$c = ria_mysql_fetch_array( $rc );
						$ar_contenu[ $id ] = array( 'name'=>($c['name']), 'desc'=>($c['desc']), 'url'=>'/admin/catalog/edit.php?cat='.$id );
					}
					break;
				case CLS_BRAND:
					$rb = prd_brands_get( $id );
					if( $rb && ria_mysql_num_rows($rb) ){
						$b = ria_mysql_fetch_array( $rb );
						$ar_contenu[ $id ] = array( 'name'=>($b['name']), 'desc'=>($b['desc']), 'url'=>'/admin/catalog/brands/edit.php?brd='.$id );
					}
					break;
				case CLS_STORE:
					$rs = dlv_stores_get( $id, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
					if( $rs && ria_mysql_num_rows($rs) ){
						$s = ria_mysql_fetch_array( $rs );
						$ar_contenu[ $id ] = array('name'=>($s['name']), 'desc'=>($s['name'].' '.$s['address1'].' '.$s['address2'].' '.$s['zipcode'].' '.$s['city']), 'url'=>'/admin/config/livraison/stores/edit.php?str='.$id);
					}
					break;
				case CLS_CMS:
					$rc = cms_categories_get( $id, false, false, -1, false, false, true, null, false, null, false );
					if( $rc && ria_mysql_num_rows($rc) ){
						$c = ria_mysql_fetch_array( $rc );
						$ar_contenu[ $id ] = array( 'name'=>($c['name']), 'desc'=>($c['short_desc']), 'url'=>'/admin/tools/cms/edit.php?cat='.$id );
					}
					break;
				case CLS_NEWS:
					$rn = news_get( $id, false, null, 0, 0, false, false );
					if( $rn && ria_mysql_num_rows($rn) ){
						$n = ria_mysql_fetch_array( $rn );
						$ar_contenu[ $id ] = array( 'name'=>($n['name']), 'desc'=>($n['intro']), 'url'=>'/admin/tools/news/edit.php?news='.$id );
					}
					break;
				case CLS_USER:
					$ru = gu_users_get( $id );
					if( $ru && ria_mysql_num_rows($ru) ){
						$u = ria_mysql_fetch_array( $ru );
						$society = $u['society'];
						$person = trim($u['title_name'] .' '. $u['adr_firstname'] .' '. $u['adr_lastname']);
						if( $society && $person ){
							$uname = $society.', '.$person;
						}elseif( $society ){
							$uname = $society;
						}else{
							$uname = $person;
						}
						$ar_contenu[ $id ] = array( 'name'=>($uname), 'desc'=>'', 'url'=>'/admin/customers/edit.php?usr='.$id );
					}
					break;
			}
		}
	}

	if( sizeof($ar_contenu) ){
		foreach( $ar_contenu as $id=>$info ){
			$html .= ' 	<div id="cnt-img-'.$class.'-'.$id.'" class="cnt-infos">';
			if( $can_edit ){
				$html .= '	<div class="del-link"><a href="#" onclick="return delImagesLink('.$class.', '.$img['id'].', '.$id.')" title="'._('Retirer l\'image').'"></a></div>';
			}
			if( $can_read ){
				$html .= '	<div class="cnt-name"><a href="'.$info['url'].'" target="_blank">'.htmlspecialchars(html_revert_wysiwyg($info['name'])).'</a></div>';
			}else{
				$html .= '	<div class="cnt-name">'.htmlspecialchars(html_revert_wysiwyg($info['name'])).'</div>';
			}
			$html .= '		<div class="cnt-desc">'.htmlspecialchars(html_revert_wysiwyg($info['desc'])).'</div>';
			if( $can_read ){
				$html .= '	<div class="cnt-url"><a href="'.$info['url'].'" target="_blank">';
				switch( $class ){
					case CLS_PRODUCT :
						$html .= _('Catalogue').' &raquo; ';
						preg_match( '/cat=([0-9]+)/', $info['url'], $matches );
						if( isset($matches[1]) && is_numeric($matches[1]) && $matches[1] > 0 ){
							$cat = $matches[1];
							$rcp = prd_categories_parents_get( $cat );
							while( $cp = ria_mysql_fetch_array($rcp) ){
								$html .= htmlspecialchars($cp['title']).' &raquo; ';
							}

							$rc = prd_categories_get( $cat );
							if( ria_mysql_num_rows($rc) ){
								$c = ria_mysql_fetch_array($rc);
								$html .= htmlspecialchars($c['title']).' &raquo; ';
							}
						}else{
							$html .= 'Non classés &raquo; ';
						}

						$html .= htmlspecialchars(html_revert_wysiwyg($info['name']));
						break;
					case CLS_STORE:
						$html .= 'Magasins &raquo; ';
						$html .= htmlspecialchars(html_revert_wysiwyg($info['name']));
						break;
					default:
						$html .= htmlspecialchars($info['url'].' - '.$info['name']);
				}
				$html .= '	</a></div>';
			}
			$html .= '	</div>';
		}
	} else{
		switch( $class ){
			case CLS_PRODUCT:
				$html .= _('Cette image n\'est associée à aucun produit.'); break;
			case CLS_CATEGORY:
				$html .= _('Cette image n\'est associée à aucune catégorie.'); break;
			case CLS_BRAND:
				$html .= _('Cette image n\'est associée à aucune marque.'); break;
			case CLS_STORE:
				$html .= _('Cette image n\'est associée à aucun magasin.'); break;
			case CLS_CMS:
				$html .= _('Cette image n\'est associée à aucun contenu.'); break;
			case CLS_NEWS:
				$html .= _('Cette image n\'est associée à aucune actualité.'); break;
			case CLS_USER:
				$html .= _('Cette image n\'est associée à aucun client.'); break;
		}
	}

	$html .= '		</div>';
	$html .= '	</div>';
	return $html;
}

/**	Cette fonction enregistre un nom pour une image
 *	@param int $img_id Obligatoire, Identifiant de l'image
 *	@param string $name Obligatoire, Nom de l'image
 *	@return bool True en cas de succès, False en cas d'échec
 */
function img_image_names_add( $img_id, $name ){

	if( !img_images_exists( $img_id ) ){
		return false;
	}

	$name = trim($name);
	if( $name == '' || strlen($name) > 75 ){
		return false;
	}

	global $config;

	$sql = '
		replace into img_image_names
			(imn_tnt_id, imn_img_id, imn_name, imn_date_created)
		values
			('.$config['tnt_id'].', '.$img_id.', "'.addslashes($name).'", now())
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction supprime un nom pour une image
 *	@param int $img_id Identifiant de l'image
 *	@param string $name Nom de l'image
 *	@return bool True en cas de succès, False en cas d'échec
 */
function img_image_names_del( $img_id, $name ){

	if( !is_numeric($img_id) || $img_id <= 0 ){
		return false;
	}
	$name = trim($name);
	if( $name == '' ){
		return false;
	}

	global $config;

	$sql = '
		delete from img_image_names
		where imn_tnt_id = '.$config['tnt_id'].' and imn_img_id = '.$img_id.' and imn_name = "'.addslashes($name).'"
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction récupère des noms d'images, en fonction de paramètres optionnels
 *	@param int $img_id Optionnel, identifiant d'image, ou tableau d'identifiants d'images
 *	@param string $name Optionnel, nom, ou tableau de noms
 *	@param bool $nocasse Optionnel, si $name spécifié, détermine si la casse est ignoré
 *	@param bool $use_like Optionnel, si activé et $name spécifié, la recherche n'est pas sur le nom complet (like %[name]%)
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- img_id : identifiant de l'image
 *		- name : nom de l'image
 *		- date_created : date de création au format FR
 *		- date_created_en : date de création au format EN
 */
function img_image_names_get( $img_id = 0, $name = '', $nocasse = false, $use_like = false ){

	{ // contrôles
		if( is_array($img_id) ){
			foreach( $img_id as $one_id ){
				if( !is_numeric($one_id) || $one_id <= 0 ){
					return false;
				}
			}
		}elseif( !is_numeric($img_id) || $img_id < 0 ){
			return false;
		}elseif( $img_id ){
			$img_id = array($img_id);
		}else{
			$img_id = array();
		}

		$temp_ar_name = array();
		if( is_array($name) ){
			foreach( $name as $one_name ){
				$one_name = trim($one_name);
				if( $one_name != '' ){
					$temp_ar_name[] = addslashes($one_name);
				}
			}
		}else{
			$name = trim($name);
			if( $name != '' ){
				$temp_ar_name[] = addslashes($name);
			}
		}
		$name = $temp_ar_name;
	}

	global $config;

	$sql = '
		select
			imn_img_id as img_id, imn_name as "name",
			date_format(imn_date_created, "%d/%m/%Y à %H:%i") as date_created,
			imn_date_created as date_created_en
		from
			img_image_names
		where
			imn_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($img_id) ){
		$sql .= ' and imn_img_id in ('.implode(', ', $img_id).')';
	}

	if( sizeof($name) ){
		if( $use_like ){
			$sql .= ' and (';
			for( $i = 0; $i < sizeof($name); $i++ ){
				if( $i > 0 ){
					$sql .= ' or';
				}
				if( $nocasse ){
					$sql .= ' lower(imn_name) like lower("%'.$name[ $i ].'%")';
				}else{
					$sql .= ' imn_name like "%'.$name[ $i ].'%"';
				}
			}
			$sql .= ')';
		}else{
			if( $nocasse ){
				$sql .= ' and lower(imn_name) in (lower("'.implode('"), lower("', $name).'"))';
			}else{
				$sql .= ' and imn_name in ("'.implode('", "', $name).'")';
			}
		}
	}

	return ria_mysql_query($sql);

}

/**	Cette fonction retourne le type (extension) d'une image (quand celui-ci est connu dans la base de données).
 *	@param int $id Obligatoire, Identifiant de l'image.
 *	@return string|bool Le type d'image, ou false en cas d'échec.
 */
function img_images_get_type( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select img_type
		from img_images
		where img_tnt_id = '.$config['tnt_id'].' and img_id = '.$id.' and img_date_deleted is null
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);

}

/** Cette fonction retourne les filtres à appliquer sur une taille d'image
 *	@param bool $img_code Facultatif, code d'une taille d'image disponible dans cfg_images
 *	@param bool $filter_code Facultatif, code d'un filtre disponible dans img_images_filters
 *	@return resource Retourne un résultat de requête MySQL contenant les colonnes suivantes :
 *		- img_code : code de taille de l'image
 *		- filter_code : code du filtre
 *		- value : valeur du filtre doit respecter le pattern de img_images_filters
 *		- pos : position à laquelle le filtre doit s'appliquer
 *		- pattern : Patron de saisie du filtre
 *		- desc : Description du filtre
 */
function img_filters_get( $img_code=false, $filter_code=false ){
	global $config;

	$sql = 'select cif_img_code as img_code, cif_filter_code as filter_code, cif_width as width, cif_height as height, cif_value as value, cif_pos as pos, iif_pattern as pattern, iif_desc as "desc"
			from cfg_images_filters
			join img_images_filters on cif_filter_code = iif_code
			where cif_tnt_id = '.$config['tnt_id'];

	if( $img_code ){
		$sql .= ' and cif_img_code = \''.addslashes($img_code).'\'';
	}
	if( $filter_code ){
		$sql .= ' and cif_filter_code = \''.addslashes($filter_code).'\'';
	}
	$sql .= ' order by cif_pos asc';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de générer l'image dans les différents formats du locataire
 *	@param int $img_id Obligatoire, identifiant d'une image
 *	@param bool $force Optionnel, mettre true pour forcérer la régénration de toutes les miniature, par défault seules celles manquantes sont générées
 *	@param bool $customized Optionnel, permet de créer une image à taille personnalisée, mettre un tableau array( 'width'=>xx, 'height'=>xx )
 *	@param array $force_config Optionnel, permet de forcerla régénération des miniatures seulement pour certaines configurations d'images
 *
 *	@return bool False en cas d'échec de la régénération des miniature
 *	@return array En cas de succès, un tableau récapitulant les actions faites est retouné, il contiendra :
 *				- count : Nombre total de miniatures
 *				- missing : Nombre de miniatures manquantes
 *				- resolved : Nombre de miniatures créées
 */
function img_images_thumbnails_refresh( $img_id, $force=false, $customized=false, $force_config=array() ){
	if( !img_images_exists($img_id) ){
		return false;
	}


	if( $customized!==false ){
		if( !is_array($customized) || sizeof($customized)!=2 ){
			return false;
		}

		if( !isset($customized['width']) || !is_numeric($customized['width']) || $customized['width']<=0 ){
			return false;
		}

		if( !isset($customized['height']) || !is_numeric($customized['height']) || $customized['height']<=0 ){
			return false;
		}
	}

	global $config;

	$source = false;
	$d = dir( $config['img_dir'].'/source' );
	while( false!==($entry = $d->read()) ){
		if( preg_match( '/^'.$img_id.'(-|\.)/', $entry ) ){
			$source = $config['img_dir'].'/source/'.$entry;
		}
	}

	if( !is_file($source) ) {
		if($config['tnt_id'] == 1) {
			$source = $config['img_dir'].'/500x400/'.$img_id.'.jpg';
			$dest = $config['img_dir'].'/source/'.$img_id.'.jpg';
			if( is_file($source) ) {
				if(copy($source, $dest)) {
					return img_images_thumbnails_refresh( $img_id, false, false );
				} else {
					return false;
				}
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	$count = $resolved = $missing = 0;
	$ar_img_sizes = $config['img_sizes'];

	if( $customized!==false ){
		$ar_img_sizes = array();

		// Si le dossier de personnalisation d'images n'existe pas alors on le créé
		$customized_dir = $config['img_dir'].'/customized/';
		if( !file_exists($customized_dir) ){
			mkdir( $customized_dir, 0755 );
			chgrp( $customized_dir, 'apache' );
			chown( $customized_dir, 'apache' );
		}

		$ar_img_sizes['customized'] = array(
			'width' 		=> $customized['width'],
			'height' 		=> $customized['height'],
			'background' 	=> '#FFFFFF',
			'format' 		=> 'jpg',
			'clip' 			=> 0,
			'transparent' 	=> 0,
			'border' 		=> 0,
			'border-color' 	=> '',
			'corners' 		=> 0
		);
	}

	// avant tous redimensionnements on check si l'image à besoin dun refresh
	// permet d'eviter le passage a convert dans certain cas qui peut être gourmand et long
	$need_refresh = false;
	if( $customized===false && !$force ) {
		foreach( $ar_img_sizes as $key=>$size ){
			if( $key=='banner' ){
				continue;
			}

			if( $customized===false && $key=='customized' ){
				continue;
			}

			$count++;

			// Extension des images, par défault il s'agit d'une JPG
			$extension = in_array($size['format'], array('jpg', 'png', 'gif')) ? $size['format'] : 'jpg';

			if( !is_file( $config['img_dir'].'/'.$size['dir'].'/'.$img_id.'.'.$extension ) ){
				$need_refresh = true;
				break;
			}
		}
	}else{
		$need_refresh = true;
	}

	if( $need_refresh ) {
		img_images_create_dirs();

		$count = 0;

		$is_cmyk = $clip = false;
		$output = array();
		exec( '/usr/bin/identify -verbose '.addslashes_img_source($source), $output );
		if (!is_array($output) || !count($output)) {
			return false;
		}

		$is_cmyk = array_search( '  Colorspace: CMYK', $output )!==false;
		$clip = array_search( '  Clipping path:', $output )!==false;

		// permet la conversion dans l'espace de couleur rgb si l'image ne l'ai pas.
		// la lib php Imagick ne converti pas correctement dans ce cas la.
		if( $is_cmyk ){
			$source_tmp = $config['img_dir'].'/source/tmp';
			exec('/usr/bin/convert '.addslashes_img_source( $source ).' -colorspace sRGB '.$source_tmp, $output, $return );
			$source = $source_tmp;
		}


		list($source_width, $source_height, $source_type, $source_attr) = getimagesize($source);

		// Crée les thumbnails
		foreach( $ar_img_sizes as $key=>$size ){
			if( is_array($force_config) && sizeof($force_config) ){
				if( !in_array($key, $force_config) ){
					continue;
				}
			}

			if( $key=='banner' ){
				continue;
			}

			if( $customized===false && $key=='customized' ){
				continue;
			}

			$size_source = $source;

			// Extension des images, par défault il s'agit d'une JPG
			$extension = in_array($size['format'], array('jpg', 'png', 'gif')) ? $size['format'] : 'jpg';

			$count++;

			$destination = '';
			if( $customized!==false ){
				$destination = $config['img_dir'].'/customized/'.$size['width'].'x'.$size['height'].'-'.$img_id.'.'.$extension;
			}else{
				$destination = $config['img_dir'].'/'.$size['dir'].'/'.$img_id.'.'.$extension;
			}

			if( trim($destination)=='' || (is_file($destination) && !$force) ){
				continue;
			}

			$missing ++;
			@unlink( $destination );

			$return = false;

			// gravité et fond pas défaut
			$gravity = 'Center';
			$transparent = $extension=='png' && $size['transparent'];
			$background = new ImagickPixel($transparent ? 'transparent' : $size['background']);

			$ar_filter = array();

			if( $customized!==false ){
				$ar_filter[] = array(
					'img_code' 		=> 'medium',
					'filter_code' 	=> 'fill',
					'width' 		=> $customized['width'],
					'height' 		=> $customized['height'],
					'value' 		=> '',
					'pos' 			=> 0,
					'pattern' 		=> '',
					'desc' 			=> 'filtre propre à la personnalisation de dimension d\'image'
				);
			}else{
				$rfilter = img_filters_get($key);
				if( $rfilter && ria_mysql_num_rows($rfilter) ){
					while( $filter = ria_mysql_fetch_assoc($rfilter) ){
						if( isset($config['default_image']) && $config['default_image'] == $img_id ){
							$filter['filter_code'] = 'crop';
						}

						$ar_filter[] = $filter;
					}
				}
			}

			// on récupère les filtres
			if( sizeof($ar_filter) ){

				// si l'image à un clip-path
				if( $clip && $size['clip'] ){
					exec('/usr/bin/convert '.addslashes_img_source( $size_source ).' -background \''.$size['background'].'\' +clip-path \'#1\' -fill \''.$size['background'].'\' -draw "color 0,0 reset" '.$destination, $output, $return );
					$size_source = $destination;
				}

				//lecture de l'image d'origine
				$img = new Imagick($size_source);
				img_imagick_autorate( $img );

				$img->setResourceLimit(6, 2);
				$img->setImageColorSpace( imagick::COLORSPACE_SRGB );

				foreach( $ar_filter as $filter ){
					$tsize = $img->getImageGeometry();

					//permet de controle la gravité de l'image
					$img->setGravity( img_images_get_gravity($gravity) );

					$next_filter = false;
					// première passe de controle des filtres
					switch( $filter['filter_code'] ){
						case 'crop': {
							// controle si l'image source est plus petite que la zone alors on applique un fill à la place du crop.
							if( $tsize['width'] > $filter['width'] || $tsize['height'] > $filter['height'] ){
								$img->cropThumbnailImage( $filter['width'], $filter['height']);
								$next_filter = true;
							}
							break;
						}
						case 'fill': {
							if( ($filter['width'] > 0 && $tsize['width'] > $filter['width']) || ($filter['height'] > 0 && $tsize['height'] > $filter['height']) ){
								$ratio = true;

								$filter['height'] = (int) $filter['height'];
								$filter['width'] = (int) $filter['width'];

								if( !$filter['height'] ){
									$filter['height'] = false;
									$ratio = false;
								}

								if( !$filter['width'] ){
									$filter['width'] = false;
									$ratio = false;
								}

								$img->thumbnailImage( $filter['width'], $filter['height'], $ratio );
							}
							break;
						}
						case 'transparentColor': {
							if( $transparent && preg_match('/'.$filter['pattern'].'/', $filter['value'])  ){
								$img->transparentPaintImage( $filter['value'], 0.0 , 100 , false );
							}
							$next_filter = true;
							break;
						}
						case 'scale': {
							$img->scaleImage( $filter['width'], $filter['height'], false );
							break;
						}
						case 'gravity': {
							if( preg_match('/'.$filter['pattern'].'/', $filter['value']) ){
								$gravity = trim($filter['value']);
							}
							$next_filter = true;
							break;
						}
						case 'background': {
							if( preg_match('/'.$filter['pattern'].'/', $filter['value']) ){
								$background = new ImagickPixel($filter['value']);
							}
							$next_filter = true;
							break;
						}
						case 'resize': {
							$img->resizeImage($filter['width'], $filter['height'], imagick::COLORSPACE_SRGB, 1, true);
							break;
						}
						case 'watermark': {

							if( preg_match('/'.$filter['pattern'].'/', $filter['value']) ){

								$file = preg_replace('/'.$filter['pattern'].'/', '$1', $filter['value']);
								$pos_x = preg_replace('/'.$filter['pattern'].'/', '$2', $filter['value']);
								$pos_y = preg_replace('/'.$filter['pattern'].'/', '$3', $filter['value']);

								if( is_file($file) ){
									$watermark = new Imagick();
									$watermark->readImage($file);
									$watermark->scaleImage($filter['width'], $filter['height']);
									$img->compositeImage($watermark, imagick::COMPOSITE_OVER, $pos_x, $pos_y);
									$next_filter = true;
								}

							}
							break;
						}
						default: $next_filter = false; break;
					}

					if( $next_filter ){ continue; }


					$tsize = $img->getImageGeometry();
					if( $filter['width'] == 0 ){
						$filter['width'] = $tsize['width'];
					}
					if( $filter['height'] == 0 ){
						$filter['height'] = $tsize['height'];
					}

					$grav_pos = img_images_get_gravity_pos($gravity, $filter['width'], $filter['height'], $tsize['width'], $tsize['height']);
					$pos_x = $grav_pos['pos_x'];
					$pos_y = $grav_pos['pos_y'];

					$tmp = new Imagick();
					$tmp->newImage( $filter['width'], $filter['height'], $background );
					$tmp->compositeImage( $img, imagick::COMPOSITE_OVER, $pos_x, $pos_y );
					$img = $tmp;
				}

				$tsize = $img->getImageGeometry();
				$final_width = (int)$size['width'];
				$final_height = (int)$size['height'];

				// si la taille d'image est variable en largeur
				if( $final_width == 0 ){
					$final_width = $tsize['width'];
					if( $tsize['height'] > $final_height ){
						$img->thumbnailImage( 0, $final_height, false );
					}
				}
				// si la taille d'image est vaiable en hauteur
				elseif( $final_height == 0 ){
					$final_height = $tsize['height'];
					if( $tsize['width'] > $final_width ){
						$img->thumbnailImage( $final_width, 0, false );
					}
				}
				// si l'image est encore plus grande que ma zone
				elseif( $tsize['width'] > $final_width || $tsize['height'] > $final_height ){
					$img->thumbnailImage( $final_width, $final_height, true );
				}

				// position de l'image dans la fenetre finale.
				$grav_pos = img_images_get_gravity_pos($gravity, $final_width, $final_height, $tsize['width'], $tsize['height']);
				$pos_x = $grav_pos['pos_x'];
				$pos_y = $grav_pos['pos_y'];

				// création du conteneur final
				$tmp = new Imagick();
				$tmp->newImage( $final_width, $final_height, new ImagickPixel($transparent ? 'transparent' : $size['background']) );
				$tmp->compositeImage( $img, imagick::COMPOSITE_OVER, $pos_x, $pos_y );
				$img = $tmp;

				// si ma taille d'image à une bordure
				if( is_numeric($size['border']) && $size['border']>0 ){

					// réduit la taille de l'image selon la largeur de la bordure x2
					if( is_numeric($size['border']) && $size['border']>0 ){
						$final_width -= $size['border'] * 2;
						$final_height -= $size['border'] * 2;
					}
					$tsize = $img->getImageGeometry();
					$pos_x = $final_width / 2 - $tsize['width'] / 2;
					$pos_y = $final_height / 2 - $tsize['height'] / 2;

					$tmp = new Imagick();
					$tmp->newImage( $final_width, $final_height, new ImagickPixel($transparent ? 'transparent' : $size['background']) );
					$tmp->compositeImage( $img, imagick::COMPOSITE_OVER, $pos_x, $pos_y );
					$img = $tmp;


					if( is_numeric($size['corners']) && $size['corners']>0 ){
						$img->roundCorners( $size['corners'], $size['corners']);
					}
					$img->borderImage( $size['border-color'], $size['border'], $size['border'] );
				}

				// si ma taille d'image à des coins arrondis
				if( is_numeric($size['corners']) && $size['corners']>0 ){
					$img->roundCorners( $size['corners'], $size['corners']);
				}

				// enregistrement de l'image finale
				$img->setImageFormat($extension);
				$img->setImageColorSpace( imagick::COLORSPACE_SRGB );
				$img->setImageCompressionQuality(95);
				$img->writeImage( $destination );
			}

			if( is_file($destination) ){
				// Effectue une passe supplémentaire d'optimisation sur les images (inspirée par Yahoo Smush.it)
				if ($customized===false) {
					img_images_optimize( $destination );
				}

				$resolved++;
			}
		}
		@unlink($source_tmp);
	}

	return array( 'count' => $count, 'missing' => $missing, 'resolved' => $resolved );
}

/** Cette fonction permet de retourner une gravité de type imagick en fonction de la chaine de caractère (utilisé par img_images_thumbnails_refresh)
 * @param string $gravity Obligatoire, Chaine de caractère, si vide alors la gravité retournée sera Center
 * @return int l'identifiant de gravité tel que définie par imagemagick.
 */
function img_images_get_gravity( $gravity ){
	switch( $gravity ){
		case 'NorthWest':
			return imagick::GRAVITY_NORTHWEST;
			break;
		case 'North':
			return imagick::GRAVITY_NORTH;
			break;
		case 'NorthEast':
			return imagick::GRAVITY_NORTHEAST;
			break;
		case 'West':
			return imagick::GRAVITY_WEST;
			break;
		case 'East':
			return imagick::GRAVITY_EAST;
			break;
		case 'SouthWest':
			return imagick::GRAVITY_SOUTHWEST;
			break;
		case 'South':
			return imagick::GRAVITY_SOUTH;
			break;
		case 'SouthEast':
			return imagick::GRAVITY_SOUTHEAST;
			break;
		default:
			return imagick::GRAVITY_CENTER;
			break;
	}
}

/** Cette fonction permet de calculer la position x et y de départ en fonction d'un gravité donnée
 * (utilisé par img_images_thumbnails_refresh)
 * @param string $gravity Obligatoire, chaine de caratère correspondant à la gravité souhaité
 * @param int $source_w Obligatoire, Largeur de l'image source
 * @param int $source_h Obligatoire, Hauteur de l'image source
 * @param int $dest_w Obligatoire, Largeur de l'image de destination
 * @param int $dest_h Obligatoire, Hauteur de l'image de destination
 * @return array un tableau contenant :
 *		- pos_x : position x de départ
 *		- pos_y : position y de départ
 */
function img_images_get_gravity_pos( $gravity, $source_w, $source_h, $dest_w, $dest_h ){
	switch( $gravity ){
		case 'NorthWest':
			$pos_x = $pos_y = 0;
			break;
		case 'North':
			$pos_x = $source_w / 2 - $dest_w / 2;
			$pos_y = 0;
			break;
		case 'NorthEast':
			$pos_x = $source_w - $dest_w;
			$pos_y = 0;
			break;
		case 'West':
			$pos_x = 0;
			$pos_y = $source_h / 2 - $dest_h / 2;
			break;
		case 'East':
			$pos_x = $source_w - $dest_w;
			$pos_y = $source_h / 2 - $dest_h / 2;
			break;
		case 'SouthWest':
			$pos_x = 0;
			$pos_y = $source_h - $dest_h;
			break;
		case 'South':
			$pos_x = $source_w / 2 - $dest_w / 2;
			$pos_y = $source_h - $dest_h;
			break;
		case 'SouthEast':
			$pos_x = $source_w - $dest_w;
			$pos_y = $source_h - $dest_h;
			break;
		default:
			$pos_x = $source_w / 2 - $dest_w / 2;
			$pos_y = $source_h / 2 - $dest_h / 2;
			break;
	}
	return array( 'pos_x' => $pos_x, 'pos_y' => $pos_y );
}

/**	Cette fonction force la mise à jour de la date de dernière modification d'une image.
 *	@param int $id Obligatoire, Identifiant de l'image.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function img_images_set_date_modified( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update img_images
		set img_date_modified = now()
		where img_id = '.$id.' and img_tnt_id = '.$config['tnt_id'].'
	';

	return ria_mysql_query($sql);

}

/** Cette fonction permet de recherche les produits pouvant être rattaché automatiquement à une image
 *	@param string $search Obligatoire, texte à recherche
 *	@param int $limit Facultatif, par défaut la limite d'article retourné est de 15 (mettre 0 pour n'avoir aucune limite)
 *	@param bool $only_ref Facultatif, si vrai, la recherche est limitée à la seule référence du produit. Si faux (valeur par défaut), la recherche porte également sur le nom du produit.
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant du produit
 *				- ref : référence du produit
 *				- name : nom du produit
 *				- is_sync : si oui ou non le produit est synchronisé
 */
function img_images_search_products( $search, $limit=15, $only_ref=false ){
	if( trim($search) == '' ){
		return false;
	}

	$search = strtoupper2( $search );

	global $config;

	$sql = '
		select prd_id as id, prd_ref as ref, prd_name as name, prd_is_sync as is_sync, if(upper(prd_ref) = "'.addslashes( $search ).'", 1, 0) as exactly, prd_desc as "desc"
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_date_deleted is null
			and (
				upper(prd_ref) = "'.addslashes( $search ).'"
				'.( !$only_ref ? ' or upper(prd_name) like "%'.addslashes( $search ).'%"' : '' ).'
			)
	';

	if( is_numeric($limit) && $limit>0 ){
		$sql .= ' limit 0, '.$limit;
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les images ayant un lien direct avec les produits (via leur référence), mais n'ayant aucun lien avec un contenu.
 *	@param bool $is_associated Facultatif, par défaut seules les images n'ayant jamais été associées sont retournées,
 *		mettre True pour les autres et null pour toutes les avoir
 *	@param string $img_search Facultatif, permet de filtrer le résultat par rapport au nom de l'image
 *	@param string $img_like Facultatif, permet de définir quel type d'expression régulière est utilisé (ignoré si img_search est vide).
 *		Les valeurs acceptées sont :
 *			- start : débute par
 *			- equal : est égal à
 *			- end : se termine par
 *	@param bool $is_masked  Facultatif, permet de limiter le résultat aux seules images masquées (true) ou bien à celles qui ne le sont pas (false).
 *		Par défaut, les deux sont retournés.
 *	@return array Un tableau contenant les informations sur les images
 */
function img_images_get_with_products( $is_associated=false, $img_search='', $img_like='', $is_masked=null ){
	global $config;

	$ar_images 	= array();
	$ar_imgs 	= array();

	// Charge la liste des images qui ne sont associées à aucun contenu
	$sql = '
		select img_id as id, upper(img_src_name) as src_name, img_is_sync as is_sync
		from img_images
		where img_tnt_id = '.$config['tnt_id'].'
			and ifnull(img_src_name, "") != ""
			and img_count <= 0
			and img_date_deleted is null
	';

	if( $is_associated!==null ){
		$sql .= ' and img_is_associated='.( $is_associated ? '1' : '0' );
	}

	if( $is_masked!==null ){
		$sql .= ' and img_is_masked='.( $is_masked ? '1' : '0' );
	}

	$sql .= '
		order by img_src_name
	';

	$rimg = ria_mysql_query( $sql );
	if( !$rimg || !ria_mysql_num_rows($rimg) ){
		return $ar_images;
	}

	// Si img_search est renseigné, filtre la liste des images sur celles dont le nom correspond à la recherche
	$ref_search = array();
	while( $img = ria_mysql_fetch_assoc($rimg) ){
		if( $img_search != '' ){
			if( $img_like == 'equal' ){
				if( strtolower2($img['src_name']) != strtolower2($img_search) ){
					continue;
				}
			}else{
				$reg = '/'.( $img_like == 'start' ? '^' : '' ).$img_search.( $img_like == 'end' ? '$' : '' ).'/i';

				if( !preg_match($reg, $img['src_name']) ){
					continue;
				}
			}
		}

		$ar_imgs[ $img['id'] ] = array( 'id' => $img['id'], 'is_sync' => $img['is_sync'], 'src_name' => $img['src_name'] );

		if( isset($config['img_sync_with_prd']) && $config['img_sync_with_prd'] ){
			$ref_search[ $img['id'] ][] = str_replace('^', '/', $img['src_name']);

			if( isset($config['img_sync_with_prd_secondary']) && $config['img_sync_with_prd_secondary'] ){
				$separator_pos = strpos( $img['src_name'], $config['img_sync_code_separator'] );
				if( $separator_pos !== false ){
					$code_part = substr( $img['src_name'], 0, $separator_pos );
					if( trim($code_part) != '' ){
						$ref_search[ $img['id'] ][] = str_replace( '^', '/', $code_part );
					}
				}
			}
		}else{
			// Retire les -00 utilisés par certains clients pour distinguer les images primaires / secondaires, etc...
			$img['src_name'] = preg_replace( '/-[0-9]+$/', '', $img['src_name'] );
			$ref_search[ $img['id'] ][] = strtoupper2( $img['src_name'] );
		}
	}

	$str_ref_search = '';
	foreach( $ref_search as $img_id=>$refs ){
		$ar_refs = [];

		foreach( $refs as $ref ){
			$ref2 = '';

			if( isset($config['img_sync_with_prd_secondary']) && $config['img_sync_with_prd_secondary'] ){
				$separator_pos = strpos( $ref, $config['img_sync_code_separator'] );
				if( $separator_pos !== false ){
					$ref2 = substr($ref, 0, $separator_pos);
				}
			}

			$ar_refs[ $ref ] = $ref;
			if (trim($ref2) != '') {
				$ar_refs[ $ref2 ] = $ref2;
			}
		}

		$str_ref_search = implode('", "', $ar_refs);
		if( trim($str_ref_search) == '' ){
			continue;
		}

		$res = ria_mysql_query('
			select prd_id as id, prd_name as name, prd_ref as ref, prd_is_sync as is_sync, prd_childonly as childonly
			from prd_products
			where prd_tnt_id = '.$config['tnt_id'].'
				and prd_ref in ("'.$str_ref_search.'")
				and prd_date_deleted is null
		');

		if( !$res || !ria_mysql_num_rows($res) ){
			continue;
		}

		while( $r = ria_mysql_fetch_assoc($res) ){
			if( !isset($config['img_sync_no_childonly']) || $config['img_sync_no_childonly'] ){
				if( $r['childonly'] ){
					continue;
				}
			}

			foreach( $refs as $ref ){
				$ref2 = '';

				if( isset($config['img_sync_with_prd_secondary']) && $config['img_sync_with_prd_secondary'] ){
					$separator_pos = strpos( $ref, $config['img_sync_code_separator'] );
					if( $separator_pos !== false ){
						$ref2 = substr($ref, 0, $separator_pos);
					}
				}

				$r['ref'] = strtoupper2( $r['ref'] );
				if ($r['ref'] == $ref || $r['ref'] == $ref2) {
					$img = $ar_imgs[ $img_id ];

					if( is_numeric($img['id']) && $img['id'] > 0 ){
						if (!array_key_exists($img['id'], $ar_images)) {
							$ar_images[ $img['id'] ] = array(
								'img' => array( 'id'=>$img['id'], 'src_name'=>strtoupper2($img['src_name']), 'is_sync' => $img['is_sync'] ),
								'products' => array()
							);
						}

						$ar_images[ $img['id'] ]['products'][] = $r;
					}
				}
			}
		}
	}




	return $ar_images;
}

/** Cette fonction permet de charger un tableau des images ayant des liens avec des produits
 *
 *	@param bool $is_associated Optionnel, par défaut seules les images n'ayant jamais été associées sont retournées, mettre True pour les autres et null pour toutes les avoir
 *	@param string $img_search Optionnel, permet de filtrer le résultat par rapport au nom de l'image
 *	@param string $img_like Optionnel, permet de définir quel type d'expression régulière est utilisé (ignoré si img_search est vide). Les valeurs
 *		acceptées sont :
 *			- start : débute par
 *			- in : contient
 *			- end : se termine par
 *			- equal : strictement égal
 * 	@param bool $with_engine Optionnel, permet de trouver les produits associés via le moteur de recherche si l'image n'a pas de lien direct via la référence
 * 	@return array un tableau contenant les images avec une liste des produits associés
 */
function img_images_get_automatic_association( $is_associated=false, $img_search='', $img_like='', $with_engine=false ){
	// Contrôle sur le paramètre de l'expression régulière
	$img_like = in_array($img_like, array('start', 'in', 'end', 'equal')) ? $img_like : 'start';

	$ar_images = array();

	// Récupère les images étant directement liées à un article (via la référence) qui ne sont pas associées
	$ar_img_exclude = array();
	$tmp_images = img_images_get_with_products($is_associated, $img_search, $img_like, false );
	if( is_array($tmp_images) && sizeof($tmp_images) ){
		$ar_img_exclude = array_keys($tmp_images);
		$ar_images = $tmp_images;
	}

	global $config;

	// Récupère les images non associées n'ayant pas de lien direct avec des références produit
	if( !(isset($config['img_sync_with_prd']) && $config['img_sync_with_prd']) ){
		$r_images = img_images_get(0, '', '', '', '', 0, true, false, false, $is_associated, array('src_name'=>'asc'), false);
		if( $r_images && ria_mysql_num_rows($r_images) ){
			while( $img = ria_mysql_fetch_assoc($r_images) ){
				if( in_array($img['id'], $ar_img_exclude) ){
					continue;
				}

				// Si l'on a défini une référence pour laquelle on effectue une recherche
				if( $img_search != '' ){
					if( $img_like == 'equal' ){
						if( strtolower2($img['src_name']) != strtolower2($img_search) ){
							continue;
						}
					}else{
						$reg = '/'.( $img_like == 'start' ? '^' : '' ).$img_search.( $img_like == 'end' ? '$' : '' ).'/i';

						if( !preg_match($reg, $img['src_name']) ){
							continue;
						}
					}
				}

				$ar_product = array();

				// Si l'on recherche avec le moteur de recherche
				if( $with_engine ){
					$src_name = strtoupper2($img['src_name']);

					// Association via recherche dans le moteur de recherche
					$not_child = false;

					// Le nom de l'image est tronqué si un séparateur est défini dans le cas d'images secondaires existantes
					if( isset($config['img_sync_with_prd_secondary']) && $config['img_sync_with_prd_secondary'] ){
						$separator_pos = strpos( $src_name, $config['img_sync_code_separator'] );

						if( $separator_pos !== false ){
							$src_name = substr($src_name, 0, $separator_pos);
							$not_child = true;
						}
					}

					if( isset($config['search_general_rule']) ){
						$tmp = $config['search_general_rule'];
					}

					$config['search_general_rule'] = 'start';

					// Recherche des produits correspondants au nom de l'image
					$res = search(
						array('seg'=>1, 'keywords'=>$src_name, 'published'=>false, 'section'=>false, 'action'=>2, 'type'=>'prd'),
						false, 0, 200, false, null, 0, true
					);
					if( is_array($res) && sizeof($res) ){
						$i = 0;
						foreach( $res as $one_res ){
							// On ne synchronise pas les images des produits enfant seulement si la configuration est activée ou non renseingée
							if( !isset($config['img_sync_no_childonly']) || $config['img_sync_no_childonly'] ){
								if( isset($one_res['get']['childonly']) && $one_res['get']['childonly'] ){
									continue;
								}
							}

							// L'image n'est pas associée à plus de 10 produits
							if( $i >= 10 ){
								break;
							}

							if( !$not_child || !$one_res['get']['childonly'] ){
								$ar_product[ $one_res['get']['id'] ] = $one_res['get'];
								$i++;
							}
						}
					}

					if( isset($tmp) ){
						$config['search_general_rule'] = $tmp;
					}
				}

				// Complète le tableau avec les produits liés à l'image
				$ar_images[ $img['id'] ] = array(
					'img' => array( 'id'=>$img['id'], 'src_name'=>strtoupper2($img['src_name']), 'is_sync'=>$img['is_sync'] ),
					'products' => $ar_product,
				);
			}
		}
	}

	return $ar_images;
}

/** Cette fonction permet de générer l'url d'une image a partir de son identifiant par défault le thumbnail défini est "small"
 *	@param int $id Obligatoire, identifiant de l'image à rechercher
 *	@param string $thumb Facultatif, nom du format d'image à utiliser. Ce nom doit exister dans la configuration (img_sizes). Par défaut la taille d'image retournée est small (80x80)
 *	@return string L'url complète du fichier image
 */
function img_get_url( $id, $thumb='small' ){
	global $config;

	// Vérifie que la configuration du format d'image existe
	if( !isset($config['img_sizes'][$thumb]) ){
		return '';
	}

	$tp = $config['img_sizes'][$thumb];

	// Vérifie que les variables de configuration dont nous avons besoin est disponible
	if( !isset( $tp['width'], $tp['height'], $tp['format'] ) ){
		return '';
	}

	return $config['img_url'] .'/'. $tp['width'].'x'.$tp['height'].'/'.$id.'.'.$tp['format'];
}

/** Cette fonction permet de traiter l'orientation d'une image.
 *	@param Imagick $image Obligatoire, instance de classe Imagick
 *	@return Imagick L'objet avec l'image orientée dans le bon sens
 */
function img_imagick_autorate(Imagick $image){
	switch ($image->getImageOrientation()) {
		case Imagick::ORIENTATION_TOPLEFT:
		break;
	case Imagick::ORIENTATION_TOPRIGHT:
		$image->flopImage();
		break;
	case Imagick::ORIENTATION_BOTTOMRIGHT:
		$image->rotateImage("#000", 180);
		break;
	case Imagick::ORIENTATION_BOTTOMLEFT:
		$image->flopImage();
		$image->rotateImage("#000", 180);
		break;
	case Imagick::ORIENTATION_LEFTTOP:
		$image->flopImage();
		$image->rotateImage("#000", -90);
		break;
	case Imagick::ORIENTATION_RIGHTTOP:
		$image->rotateImage("#000", 90);
		break;
	case Imagick::ORIENTATION_RIGHTBOTTOM:
		$image->flopImage();
		$image->rotateImage("#000", 90);
		break;
	case Imagick::ORIENTATION_LEFTBOTTOM:
		$image->rotateImage("#000", -90);
		break;
	default: // Invalid orientation
		break;
	}

	$image->setImageOrientation(Imagick::ORIENTATION_TOPLEFT);
	return $image;
}


/**
 *	Cette fonction permet l'ajout d'un type d'image ( ex: photo de magasin, image secondaire utilisateur )
 *	@param int $cls_id Obligatoire, identifiant de la classe à laquelle le type d'image va être associé
 *	@param string $name Obligatoire, désignation du type d'image
 *	@return int|false Retourne l'identifiant de la nouvelle ligne ou false en cas d'échec
 */
function img_images_types_add( $cls_id, $name ){
	if(!is_numeric($cls_id) || $cls_id <= 0 || !fld_classes_exists($cls_id)) return false;
	if( !trim($name) ) return false;
	global $config;

	$sql = 'insert into img_images_types (imt_tnt_id, imt_cls_id, imt_name, imt_date_created) values ('.$config['tnt_id'].', '.$cls_id.', \''.trim(ria_mysql_escape_string($name)).'\', now() )';
	$r = ria_mysql_query($sql);
	if (!$r) {
		return false;
	}

	return ria_mysql_insert_id();
}

/**
 *	Cette fonction permet de supprimer un type d'image' en fonction de son identifiant
 *	@param int $imt_id Obligatoire, identifiant du type d'image à supprimer
 *	@return bool true en cas de succès sinon false
 */
function img_images_types_del( $imt_id ){
	if(!is_numeric($imt_id) || $imt_id <= 0) return false;
	global $config;

	if (ria_mysql_query('update img_images_types set imt_date_deleted=now() where imt_id = '.$imt_id.' and imt_tnt_id = '.$config['tnt_id'].'')) {
		return img_images_objects_del( 0, $imt_id, 0, 0, 0, 0, 0 );
	}
}

/**
 *	Cette fonction permet de déterminer l'existence d'un type d'image
 *	@param int $imt_id Obligatoire, identifiant du type d'image à supprimer
 *	@return bool true en cas de succès sinon false
 */
function img_images_types_exist( $imt_id ){
	if(!is_numeric($imt_id) || $imt_id <= 0) return false;

	$r = img_images_types_get(0, $imt_id);
	if ($r && ria_mysql_num_rows($r) > 0) {
		return true;
	}

	return false;
}

/** Cette fonction permet de mettre à jour un type d'image
 *	@param int $imt_id Obligatoire, identifiant du type d'image à mettre à jour
 *  @param string $name Obligatoire, nom du type à mettre à jour
 *	@return bool true en cas de succès, false en cas d'échec
 */
function img_images_types_update( $imt_id, $name ){
	if (!is_numeric($imt_id) || $imt_id < 0) return false;
	if( trim($name)=='' ) return false;
	global $config;
	$sql = 'update img_images_types set imt_name=\''.trim(ria_mysql_escape_string($name)).'\' where imt_id = '.$imt_id.' and imt_tnt_id = '.$config['tnt_id'].' and imt_date_deleted is null';

	return ria_mysql_query($sql);
}

/**
 *	Cette fonction permet de récupérer un ou plusieurs types d'images
 *	@param int $cls_id Facultatif, identifiant d'une classe d'objets sur laquelle filtrer le résultat
 *	@param int $type_id Facultatif, Identifiant d'un type sur lequel filtrer le résultat
 *	@return array Retourne un tableau de type mysql avec les colonnes suivantes :
 *			- id : Identifiant du type d'image
 *			- cls_id : identifiant de la classe à laquelle le type est associé
 *			- tnt_id : identifiant du locataire
 *			- name : Nom du type
 *	@return bool Retourne false en cas d'échec
 */
function img_images_types_get( $cls_id=0, $type_id=0 ){
	if (!is_numeric($type_id) || $type_id < 0) return false;
	if (!is_numeric($cls_id) || $cls_id < 0) return false;

	global $config;

	$sql = '
		select imt_id as id, imt_cls_id as cls_id, imt_tnt_id as tnt_id, imt_name as name
		from img_images_types
		where imt_tnt_id in ('.$config['tnt_id'].', 0) and imt_date_deleted is null
	';

	if ($type_id > 0) {
		$sql .= ' and imt_id = '.$type_id.'';
	}
	if ($cls_id > 0) {
		$sql .= ' and imt_cls_id = '.$cls_id.'';
	}

	$r = ria_mysql_query($sql);
	return $r;
}


/** Cette fonction permet d'ajouter un objet image
 * @param int $cls_id Obligatoire, identifiant de classe
 * @param int $img_id Obligatoire, identifiant de l'image
 * @param int $type_id Facultatif, identifiant du type d'image
 * @param int $obj_id_0 Optionnel, identifiant d'un contenu auquel associer l'image
 * @param int $obj_id_1 Optionnel, identifiant d'un contenu auquel associer l'image
 * @param int $obj_id_2 Optionnel, identifiant d'un contenu auquel associer l'image
 * @param int $pos Facultatif, position dans l'ordre des images d'un contenu
 * @param string $alt Facultatif, Texte alternatif
 * @param bool $publish Facultatif, image publiée ? null => toutes les images, 0 => non publiées, 1 => publiées
 * @param bool $is_main Facultatif, image principale ? null => toutes les images, 0 => non principales, 1 => principales
 *
 * @return int|bool Retourne l'identifiant du nouvel objet ou false en cas d'échec.
 */
function img_images_objects_add( $cls_id, $img_id, $type_id = null, $obj_id_0=0, $obj_id_1=0, $obj_id_2=0, $pos = null, $alt=null, $publish=null, $is_main=null){
	if (!is_numeric($cls_id) || $cls_id <= 0) return false;
	if (!is_numeric($img_id) || $img_id <= 0 || !img_images_exists($img_id)) return false;
	if (!is_null($type_id) && (!is_numeric($type_id) || $type_id <= 0 || (!img_images_types_exist($type_id)))) return false;
	if (!is_numeric($obj_id_0) || $obj_id_0 < 0) return false;
	if (!is_numeric($obj_id_1) || $obj_id_1 < 0) return false;
	if (!is_numeric($obj_id_2) || $obj_id_2 < 0) return false;
	if (!is_null($pos) && !is_numeric($pos)) return false;
	if (!is_null($publish) && (!is_bool($publish) && !is_numeric($publish))) return false;
	if (!is_null($is_main) && (!is_bool($is_main) && !is_numeric($is_main))) return false;
	global $config;

	$keys = array(
		'imo_tnt_id',
		'imo_img_id',
		'imo_cls_id',
		'imo_obj_id_0',
		'imo_obj_id_1',
		'imo_obj_id_2',
		'imo_date_created'
	);

	$values = array(
		$config['tnt_id'],
		$img_id,
		$cls_id,
		$obj_id_0,
		$obj_id_1,
		$obj_id_2,
		'now()'
	);

	if (!is_null($type_id)) {
		$keys[] = 'imo_type_id';
		$values[] = $type_id;
	}

	if (!is_null($pos)) {
		$keys[] = 'imo_pos';
		$values[] = $pos;
	}

	if (!is_null($alt)) {
		$keys[] = 'imo_alt';
		$values[] = '"' . addslashes($alt) . '"';
	}

	if(!is_null($publish)){
		$publish = intval($publish) == 0 ? 0 : 1;
		$keys[] = 'imo_publish';
		$values[] = $publish;
	}

	if(!is_null($is_main)){
		$is_main = intval($is_main) == 0 ? 0 : 1;
		$keys[] = 'imo_is_main';
		$values[] = $is_main;
	}

	$sql = 'insert into img_images_objects
		('.implode(',', $keys).')
			values
		('.implode(',', $values).')';

	$res = ria_mysql_query($sql);
	if (!$res) {
		return false;
	}
	$id = ria_mysql_insert_id();

	img_images_count_update($img_id);

	return $id;
}


/** Cette fonction permet la suppression d'une association entre une image et un objet de la base de données
 *	@param int $cls_id Obligatoire, identifiant de classe
 *	@param int $type_id Obligatoire, identifiant du type d'image
 *	@param int $img_id Obligatoire, identifiant de l'image
 *	@param int $imo_id Obligatoire, identifiant de l'objet
 *	@param int $obj_id_0 Optionnel, identifiant d'un contenu auquel associer l'image
 *	@param int $obj_id_1 Optionnel, identifiant d'un contenu auquel associer l'image
 *	@param int $obj_id_2 Optionnel, identifiant d'un contenu auquel associer l'image
 * 	@param bool $publish Facultatif, image publiée ? null => toutes les images, 0 => non publiées, 1 => publiées
 * 	@param bool $is_main Facultatif, image principale ? null => toutes les images, 0 => non principales, 1 => principales
 *
 *	@return bool true la suppression s'est bien déroulée, false dans le cas contraire
 */
function img_images_objects_del( $cls_id=0, $type_id=0, $img_id=0, $imo_id=0, $obj_id_0=0, $obj_id_1=0, $obj_id_2=0, $publish=null, $is_main=null ){
	if( !is_numeric($cls_id) || $cls_id < 0 ) return false;
	if( !is_numeric($type_id) || $type_id < 0 ) return false;
	if( !is_numeric($img_id) || $img_id < 0 ) return false;
	if( !is_numeric($imo_id) || $imo_id < 0 ) return false;
	if( !is_numeric($obj_id_0) || $obj_id_0 < 0 ) return false;
	if( !is_numeric($obj_id_1) || $obj_id_1 < 0 ) return false;
	if( !is_numeric($obj_id_2) || $obj_id_2 < 0 ) return false;
	if( !is_numeric($type_id) || $type_id < 0 ) return false;
	if (!is_null($publish) && (!is_bool($publish) && !is_numeric($publish))) return false;
	if (!is_null($is_main) && (!is_bool($is_main) && !is_numeric($is_main))) return false;
	if ($imo_id > 0 && !img_images_objects_exist($imo_id)) return false;
	// Si aucun argument n'est passé, il est possible de tout supprimer virtuellement, ce qu'il faut éviter
	if ($type_id == 0 && $img_id == 0 && $imo_id == 0 && $obj_id_0 == 0 && $obj_id_1 == 0 && $obj_id_2 == 0
		&& is_null($publish) && is_null($is_main)) return false;

	global $config;

	$sql = '
		update img_images_objects
		set imo_date_deleted = now()
		where imo_tnt_id='.$config['tnt_id'].'
			and imo_date_deleted is null
	';

	if ($type_id > 0) {
		$sql .= ' and imo_type_id = '.$type_id;
	}
	if ($img_id > 0) {
		$sql .= ' and imo_img_id = '.$img_id;
	}
	if ($imo_id > 0) {
		$sql .= ' and imo_id = '.$imo_id;
	}
	if ($obj_id_0 > 0) {
		$sql .= ' and imo_obj_id_0 = '.$obj_id_0;
	}
	if ($obj_id_1 > 0) {
		$sql .= ' and imo_obj_id_1 = '.$obj_id_1;
	}
	if ($obj_id_2 > 0) {
		$sql .= ' and imo_obj_id_2 = '.$obj_id_2;
	}
	if ($type_id > 0) {
		$sql .= ' and imo_type_id = '.$type_id;
	}
	if (!is_null($publish)) {
		$publish = intval($publish) == 0 ? 0 : 1;
		$sql .= ' and imo_publish = ' . $publish;
	}
	if (!is_null($is_main)) {
		$is_main = intval($is_main) == 0 ? 0 : 1;
		$sql .= ' and imo_is_main = ' . $is_main;
	}

	$res = ria_mysql_query($sql);
	if( !$res ) {
		return false;
	}

	if (is_numeric($img_id) && $img_id > 0) {
		img_images_count_update($img_id);
	}

	return $res;
}

/** Cette fonction permet de récupérer un objet en fonction de sa classse et de son id
 *
 * @param int $type_id Facultatif, Identifiant du type d'image
 * @param int $obj_id_0 Facultatif, Identifiant de l'object
 * @param int $obj_id_1 Facultatif, Identifiant de l'object
 * @param int $obj_id_2 Facultatif, Identifiant de l'object
 * @param int $imo_id Facultatif, identifiant de l'objet période sur lequel filtrer le résultat
 * @param int $img_id Facultatif, identifiant de l'image ou tableau d'identifiants
 * @param int $cls_id Facultatif, identifiant de la classe
 * @param bool $publish Facultatif, image publiée ? null => toutes les images, 0 => non publiées, 1 => publiées
 * @param bool $is_main Facultatif, image principale ? null => toutes les images, 0 => non principales, 1 => principales
 *
 * @return resource Retourne un résultat de type mysql avec les colonnes suivantes:
 *		- id : Identifiant de l'objet image
 *		- tnt_id : tenante
 *		- cls_id : Classe lié ( User, product,...)
 *		- img_id : Identifiant de l'image lié
 *		- type_id : Identifiant du type lié
 *		- obj_id_0 : Identifiant de l'object id_0
 *		- obj_id_1 : Identifiant de l'object id_1
 *		- obj_id_2 : Identifiant de l'object id_2
 *		- pos : Position de l'image dans l'objet
 *		- date_created : date de création
 *		- date_modified : date de modification
 *		- date_deleted : date de suppression
 */
function img_images_objects_get( $type_id=0, $obj_id_0=0, $obj_id_1=0, $obj_id_2=0, $imo_id=0, $img_id=0, $cls_id=0, $publish = null, $is_main = null){
	if (!is_numeric($type_id) || $type_id < 0) return false;
	if (!is_numeric($obj_id_0)) return false;
	if (!is_numeric($obj_id_1)) return false;
	if (!is_numeric($obj_id_2)) return false;
	if (!is_numeric($cls_id)) return false;
	if (!is_null($publish) && (!is_bool($publish) && !is_numeric($publish))) return false;
	if (!is_null($is_main) && (!is_bool($is_main) && !is_numeric($is_main))) return false;
	$imo_id = control_array_integer( $imo_id, false );
	if( $imo_id === false ){
		return false;
	}
	$img_id = control_array_integer( $img_id, false );
	if ($img_id === false) {
		return false;
	}

	global $config;
	$sql = '
		select
			imo_img_id as id,
			imo_tnt_id as tnt_id,
			imo_cls_id as cls_id,
			imo_img_id as img_id,
			imo_type_id as type_id,
			imo_obj_id_0 as obj_id_0,
			imo_obj_id_1 as obj_id_1,
			imo_obj_id_2 as obj_id_2,
			imo_pos as pos,
			imo_date_created as date_created,
			imo_date_modified as date_modified,
			imo_date_deleted as date_deleted,
			imo_alt as alt,
			imo_publish as publish,
			imo_is_main as main,
			imo_id
		from img_images_objects
		where 1
	';

	if ($type_id > 0) {
		$sql .= ' and imo_type_id='.$type_id.' ';
	}

	if( sizeof($imo_id) ){
		$sql .= ' and imo_id in ('.implode(', ', $imo_id).')';
	}

	$obj_id_0 = intval($obj_id_0);
	if ($obj_id_0 > 0 ) {
		$sql .= ' and imo_obj_id_0 = '.$obj_id_0.' ';
	}

	$obj_id_1 = intval($obj_id_1);
	if ($obj_id_1 > 0 ) {
		$sql .= ' and imo_obj_id_1 = '.$obj_id_1.' ';
	}

	$obj_id_2 = intval($obj_id_2);
	if ($obj_id_2 > 0 ) {
		$sql .= ' and imo_obj_id_2 = '.$obj_id_2.' ';
	}
	if (sizeof($img_id)) {
		$sql .= ' and imo_img_id in ( '. implode(', ', $img_id) .') ';
	}

	$cls_id = intval($cls_id);
	if ($cls_id > 0 ) {
		$sql .= ' and imo_cls_id = '.$cls_id.' ';
	}

	if (!is_null($publish)) {
		$publish = intval($publish) == 0 ? 0 : 1;
		$sql .= ' and imo_publish = '.$publish.' ';
	}
	if (!is_null($is_main)) {
		$is_main = intval($is_main) == 0 ? 0 : 1;
		$sql .= ' and imo_is_main = '.$is_main.' ';
	}

	$sql .= " and imo_date_deleted is null";

	$sql .= ' and imo_tnt_id='.$config['tnt_id'].' ';

	$sql .= ' order by imo_cls_id asc, imo_obj_id_0 asc, imo_obj_id_1 asc, imo_obj_id_2 asc, imo_pos asc, imo_img_id asc';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer la position maximale des images d'un objet
 *
 * @param int $type_id Facultatif, Identifiant du type d'image
 * @param int $obj_id_0 Facultatif, Identifiant de l'object
 * @param int $obj_id_1 Facultatif, Identifiant de l'object
 * @param int $obj_id_2 Facultatif, Identifiant de l'object
 * @param int $imo_id Facultatif, identifiant de l'objet période sur lequel filtrer le résultat
 * @param int $img_id Facultatif, identifiant de l'image ou tableau d'identifiants
 * @param int $cls_id Facultatif, identifiant de la classe
 * @param bool $publish Facultatif, image publiée ? null => toutes les images, 0 => non publiées, 1 => publiées
 * @param bool $is_main Facultatif, image principale ? null => toutes les images, 0 => non principales, 1 => principales
 *
 * @return int|null Retourne null s'il n'y a pas d'image correspondante ou un entier représentant la position maximale utilisée
 */
function img_images_objects_get_max_pos( $type_id=0, $obj_id_0=0, $obj_id_1=0, $obj_id_2=0, $imo_id=0, $img_id=0, $cls_id=0, $publish = null, $is_main = null){
	if (!is_numeric($type_id) || $type_id < 0) return false;
	if (!is_numeric($obj_id_0)) return false;
	if (!is_numeric($obj_id_1)) return false;
	if (!is_numeric($obj_id_2)) return false;
	if (!is_numeric($cls_id)) return false;
	if (!is_null($publish) && (!is_bool($publish) && !is_numeric($publish))) return false;
	if (!is_null($is_main) && (!is_bool($is_main) && !is_numeric($is_main))) return false;
	$imo_id = control_array_integer( $imo_id, false );
	if( $imo_id === false ){
		return false;
	}
	$img_id = control_array_integer( $img_id, false );
	if ($img_id === false) {
		return false;
	}

	global $config;
	$sql = '
		select max(imo_pos) as pos
		from img_images_objects
		where 1
	';

	if ($type_id > 0) {
		$sql .= ' and imo_type_id='.$type_id.' ';
	}

	if( sizeof($imo_id) ){
		$sql .= ' and imo_id in ('.implode(', ', $imo_id).')';
	}

	if ($obj_id_0 >=0 ) {
		$sql .= ' and imo_obj_id_0 = '.$obj_id_0.' ';
	}

	if ($obj_id_1 >=0 ) {
		$sql .= ' and imo_obj_id_1 = '.$obj_id_1.' ';
	}

	if ($obj_id_2 >=0 ) {
		$sql .= ' and imo_obj_id_2 = '.$obj_id_2.' ';
	}
	if (sizeof($img_id)) {
		$sql .= ' and imo_img_id in ( '. implode(', ', $img_id) .') ';
	}
	if ($cls_id >0 ) {
		$sql .= ' and imo_cls_id = '.$cls_id.' ';
	}
	if (!is_null($publish)) {
		$publish = intval($publish) == 0 ? 0 : 1;
		$sql .= ' and imo_publish = '.$publish.' ';
	}
	if (!is_null($is_main)) {
		$is_main = intval($is_main) == 0 ? 0 : 1;
		$sql .= ' and imo_is_main = '.$is_main.' ';
	}

	$sql .= " and imo_date_deleted is null";

	$sql .= ' and imo_tnt_id='.$config['tnt_id'].' ';

	$sql .= ' group by imo_cls_id, imo_obj_id_0, imo_obj_id_1, imo_obj_id_2';

	$res = ria_mysql_query($sql);

	return $res && ria_mysql_num_rows($res) ? ria_mysql_result($res, 0, 0) : null;
}

/** Cette fonction permet la vérification d'un identifiant de période
 *	@param int $imo_id Facultatif, identifiant de l'objet période sur lequel filtrer le résultat
 *	@param int $obj_id_0 Facultatif, Identifiant de l'object
 *	@param int $obj_id_1 Facultatif, Identifiant de l'object
 *	@param int $obj_id_2 Facultatif, Identifiant de l'object
 *	@param int $img_id Facultatif, Identifiant de l'image
 *	@param int $type_id Facultatif, Identifiant du type d'image
 *	@param int $cls_id Facultatif, identifiant de la classe
 *	@param bool $publish Facultatif, image publiée ? null => toutes les images, 0 => non publiées, 1 => publiées
 *	@param bool $is_main Facultatif, image principale ? null => toutes les images, 0 => non principales, 1 => principales
 *	@return bool true si l'objet période existe, false dans le cas contraire.
 */
function img_images_objects_exist( $imo_id=0, $obj_id_0=-1, $obj_id_1=-1, $obj_id_2=-1, $type_id=0, $img_id=0, $cls_id=0, $publish=null, $is_main=null){
	if( !is_numeric($imo_id) || $imo_id<0 ) return false;
	if( !is_numeric($obj_id_0)) return false;
	if( !is_numeric($obj_id_1)) return false;
	if( !is_numeric($obj_id_2)) return false;
	if( !is_numeric($cls_id)) return false;
	if( !is_numeric($img_id)) return false;
	if( !is_numeric($type_id)) return false;
	if (!is_null($publish) && (!is_bool($publish) && !is_numeric($publish))) return false;
	if (!is_null($is_main) && (!is_bool($is_main) && !is_numeric($is_main))) return false;
	global $config;

	$r = img_images_objects_get($type_id, $obj_id_0, $obj_id_1, $obj_id_2, $imo_id, $img_id, $cls_id, $publish, $is_main);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return true;
}

/** Cette fonction permet la mise à jour de la date de modification d'un objet
 *	@param int $imo_id Obligatoire, identifiant de l'objet
 *	@return bool true si la mise à jour s'est bien déroulée, false dans le cas contraire
 */
function img_images_objects_set_date_modified( $imo_id ){
	if( !is_numeric($imo_id) || $imo_id<=0 ) return false;
	if (!img_images_objects_exist($imo_id)) return false;

	return ria_mysql_query('update img_images_objects set imo_date_modified = now() where imo_id ='.$imo_id);
}

/** Cette fonction permet la mise à jour de la position de l'image dans la listes des images d'un contenu
 * @param int $pos Obligatoire, Nouvelle position
 * @param int $type_id Facultatif, Identifiant du type d'image
 * @param int $obj_id_0 Facultatif, Identifiant de l'object
 * @param int $obj_id_1 Facultatif, Identifiant de l'object
 * @param int $obj_id_2 Facultatif, Identifiant de l'object
 * @param int $imo_id Facultatif, identifiant de l'objet période sur lequel filtrer le résultat
 * @param int $img_id Facultatif, identifiant de l'image ou tableau d'identifiants
 * @param int $cls_id Facultatif, identifiant de la classe
 * @param bool $publish Facultatif, image publiée ? null => toutes les images, 0 => non publiées, 1 => publiées
 * @param bool $is_main Facultatif, image principale ? null => toutes les images, 0 => non principales, 1 => principales
 *
 *	@return bool true si la mise à jour s'est bien déroulée, false dans le cas contraire
 */
function img_images_objects_set_pos( $pos, $type_id=0, $obj_id_0=0, $obj_id_1=0, $obj_id_2=0, $imo_id=0, $img_id=0, $cls_id=0, $publish=null, $is_main=null ){
	if (!is_numeric($pos) || $pos < 0) return false;
	if (!is_numeric($type_id) || $type_id < 0) return false;
	if (!is_numeric($obj_id_0)) return false;
	if (!is_numeric($obj_id_1)) return false;
	if (!is_numeric($obj_id_2)) return false;
	if (!is_numeric($cls_id)) return false;
	if (!is_null($publish) && (!is_bool($publish) && !is_numeric($publish))) return false;
	if (!is_null($is_main) && (!is_bool($is_main) && !is_numeric($is_main))) return false;
	$imo_id = control_array_integer($imo_id, false);
	if ($imo_id === false) {
		return false;
	}
	$img_id = control_array_integer($img_id, false);
	if ($img_id === false) {
		return false;
	}

	global $config;
	$sql = '
		update img_images_objects set imo_pos = ' . $pos . ', imo_date_modified = now()
		where 1
	';

	if ($type_id > 0) {
		$sql .= ' and imo_type_id=' . $type_id . ' ';
	}

	if (sizeof($imo_id)) {
		$sql .= ' and imo_id in (' . implode(', ', $imo_id) . ')';
	}

	if ($obj_id_0 >= 0) {
		$sql .= ' and imo_obj_id_0 = ' . $obj_id_0 . ' ';
	}

	if ($obj_id_1 >= 0) {
		$sql .= ' and imo_obj_id_1 = ' . $obj_id_1 . ' ';
	}

	if ($obj_id_2 >= 0) {
		$sql .= ' and imo_obj_id_2 = ' . $obj_id_2 . ' ';
	}
	if (sizeof($img_id) > 0) {
		$sql .= ' and imo_img_id in (' . implode(', ', $img_id) . ') ';
	}
	if ($cls_id > 0) {
		$sql .= ' and imo_cls_id = ' . $cls_id . ' ';
	}
	if (!is_null($publish)) {
		$publish = intval($publish) == 0 ? 0 : 1;
		$sql .= ' and imo_publish = ' . $publish . ' ';
	}
	if (!is_null($is_main)) {
		$is_main = intval($is_main) == 0 ? 0 : 1;
		$sql .= ' and imo_is_main = ' . $is_main . ' ';
	}

	$sql .= " and imo_date_deleted is null";

	$sql .= ' and imo_tnt_id=' . $config['tnt_id'] . ' ';

	return ria_mysql_query($sql);
}

/**
 * Cette fonction permet de récupérer le texte alternatif d'une liaison objet-image
 *
 * @param int $imo_id Facultatif, identifiant de l'objet image
 * @param string $lng Facultatif, Langue courante
 * @param array $img_obj Facultatif, Objet image préchargé (pour optimisation)
 *
 * @return string|null|bool Retourne le texte alternatif ou null, false en cas d'erreur
 */
function img_images_objects_get_alt($imo_id, $lng = false, array & $img_obj = null){

	if (is_null($img_obj)) {
		$r_img_obj = img_images_objects_get(0, 0, 0, 0, $imo_id);
		if (!$r_img_obj || !ria_mysql_num_rows($r_img_obj)) {
			return false;
		}

		$img_obj = ria_mysql_fetch_assoc($r_img_obj);
	}

	if (!isset($img_obj['alt'])) {
		return false;
	}

	global $config;

	$alt = $img_obj['alt'];
	if ($lng !== false && $lng != $config['i18n_lng']) {
		switch ($_GET['cls_id']) {
			case CLS_PRODUCT:
				$alt = fld_object_values_get(array($img_obj['imo_id'], $img_obj['obj_id_0']), _FLD_IMG_OBJ_ALT, $lng);
				break;
			default:
				break;
		}
	}

	return $alt;
}

/** Cette fonction permet la mise à jour du texte alternatif de l'image
 *	@param string $alt Obligatoire, Texte alternatif
 *	@param string $lng Facultatif, Langue courante
 *	@param int $type_id Facultatif, Identifiant du type d'image
 *	@param int $obj_id_0 Facultatif, Identifiant de l'object
 *	@param int $obj_id_1 Facultatif, Identifiant de l'object
 *	@param int $obj_id_2 Facultatif, Identifiant de l'object
 *	@param int $imo_id Facultatif, identifiant de l'objet période sur lequel filtrer le résultat
 *	@param int $img_id Facultatif, identifiant de l'image ou tableau d'identifiants
 *	@param int $cls_id Facultatif, identifiant de la classe
 *	@param bool $publish Facultatif, image publiée ? null => toutes les images, 0 => non publiées, 1 => publiées
 *	@param bool $is_main Facultatif, image principale ? null => toutes les images, 0 => non principales, 1 => principales
 *
 *	@return bool true l'update s'est bien déroulé, false sinon
 */
function img_images_objects_set_alt($alt, $lng = false, $type_id = 0, $obj_id_0 = 0, $obj_id_1 = 0, $obj_id_2 = 0, $imo_id = 0, $img_id = 0, $cls_id = 0, $publish = null, $is_main = null){

	global $config;

	if (!is_numeric($type_id)) return false;
	if (!is_numeric($obj_id_0)) return false;
	if (!is_numeric($obj_id_1)) return false;
	if (!is_numeric($obj_id_2)) return false;
	if (!is_numeric($cls_id)) return false;
	if (!is_null($publish) && (!is_bool($publish) && !is_numeric($publish))) return false;
	if (!is_null($is_main) && (!is_bool($is_main) && !is_numeric($is_main))) return false;

	$imo_id = control_array_integer($imo_id, false);
	$img_id = control_array_integer($img_id, false);
	if (sizeof($imo_id) <= 0 && sizeof($img_id) <= 0) {
		return false;
	}

	if ($lng !== false && $lng != $config['i18n_lng']) {
		$r_img_obj = img_images_objects_get($type_id, $obj_id_0, $obj_id_1, $obj_id_2, $imo_id, $img_id, $cls_id, $publish, $is_main);
		if (!$r_img_obj || !ria_mysql_num_rows($r_img_obj)) {
			return false;
		}

		$res = true;
		while ($img_obj = ria_mysql_fetch_assoc($r_img_obj)) {
			if (!$img_obj['cls_id'] == CLS_PRODUCT) {
				$res = false;
				continue;
			}

			$res = $res && fld_object_values_set(
				array($img_obj['imo_id'], $img_obj['obj_id_0']),
				_FLD_IMG_OBJ_ALT,
				$_POST['alt'],
				$lng
			);
		}

		return $res;
	}

	global $config;
	$sql = '
		update img_images_objects set imo_alt = "' . addslashes($alt) . '", imo_date_modified = now()
		where 1
	';

	if ($type_id > 0) {
		$sql .= ' and imo_type_id=' . $type_id . ' ';
	}

	if (sizeof($imo_id)) {
		$sql .= ' and imo_id in (' . implode(', ', $imo_id) . ')';
	}

	$obj_id_0 = intval($obj_id_0);
	if ($obj_id_0 > 0) {
		$sql .= ' and imo_obj_id_0 = ' . $obj_id_0 . ' ';
	}

	$obj_id_1 = intval($obj_id_1);
	if ($obj_id_1 > 0) {
		$sql .= ' and imo_obj_id_1 = ' . $obj_id_1 . ' ';
	}

	$obj_id_2 = intval($obj_id_2);
	if ($obj_id_2 > 0) {
		$sql .= ' and imo_obj_id_2 = ' . $obj_id_2 . ' ';
	}

	if (sizeof($img_id) > 0) {
		$sql .= ' and imo_img_id in (' . implode(', ', $img_id) . ') ';
	}

	$cls_id = intval($cls_id);
	if ($cls_id > 0) {
		$sql .= ' and imo_cls_id = ' . $cls_id . ' ';
	}

	if (!is_null($publish)) {
		$publish = intval($publish) == 0 ? 0 : 1;
		$sql .= ' and imo_publish = ' . $publish . ' ';
	}

	if (!is_null($is_main)) {
		$is_main = intval($is_main) == 0 ? 0 : 1;
		$sql .= ' and imo_is_main = ' . $is_main . ' ';
	}

	$sql .= " and imo_date_deleted is null";

	$sql .= ' and imo_tnt_id=' . $config['tnt_id'];

	return ria_mysql_query($sql);
}

/** Cette fonction permet la mise à jour de la publication de l'image
 * @param bool $publish Obligatoire, image publiée ? null => toutes les images, 0 => non publiées, 1 => publiées
 * @param int $type_id Facultatif, Identifiant du type d'image
 * @param int $obj_id_0 Facultatif, Identifiant de l'object
 * @param int $obj_id_1 Facultatif, Identifiant de l'object
 * @param int $obj_id_2 Facultatif, Identifiant de l'object
 * @param int $imo_id Facultatif, identifiant de l'objet période sur lequel filtrer le résultat
 * @param int $img_id Facultatif, identifiant de l'image ou tableau d'identifiants
 * @param int $cls_id Facultatif, identifiant de la classe
 * @param bool $is_main Facultatif, image principale ? null => toutes les images, 0 => non principales, 1 => principales
 * @param bool $update_count Facultatif, mise à jour du nombre d'images publiées
 *
 *	@return bool true l'update s'est bien déroulé, false sinon
 */
function img_images_objects_set_publish($publish, $type_id = 0, $obj_id_0 = 0, $obj_id_1 = 0, $obj_id_2 = 0, $imo_id = 0, $img_id = 0, $cls_id = 0, $is_main = null, $update_count=true){
	if ((!is_bool($publish) && !is_numeric($publish))) return false;
	if (!is_numeric($type_id) || $type_id < 0) return false;
	if (!is_numeric($obj_id_0)) return false;
	if (!is_numeric($obj_id_1)) return false;
	if (!is_numeric($obj_id_2)) return false;
	if (!is_numeric($cls_id)) return false;
	if (!is_null($is_main) && (!is_bool($is_main) && !is_numeric($is_main))) return false;
	$imo_id = control_array_integer($imo_id, false);
	if ($imo_id === false) {
		return false;
	}
	$img_id = control_array_integer($img_id, false);
	if ($img_id === false) {
		return false;
	}

	$publish = intval($publish) == 0 ? 0 : 1;

	global $config;
	$sql = '
		update img_images_objects set imo_publish = ' . $publish . ', imo_date_modified = now()
		where 1
	';

	if ($type_id > 0) {
		$sql .= ' and imo_type_id=' . $type_id . ' ';
	}

	if (sizeof($imo_id)) {
		$sql .= ' and imo_id in (' . implode(', ', $imo_id) . ')';
	}

	if ($obj_id_0 >= 0) {
		$sql .= ' and imo_obj_id_0 = ' . $obj_id_0 . ' ';
	}

	if ($obj_id_1 >= 0) {
		$sql .= ' and imo_obj_id_1 = ' . $obj_id_1 . ' ';
	}

	if ($obj_id_2 >= 0) {
		$sql .= ' and imo_obj_id_2 = ' . $obj_id_2 . ' ';
	}
	if (sizeof($img_id) > 0) {
		$sql .= ' and imo_img_id in (' . implode(', ', $img_id) . ') ';
	}
	if ($cls_id > 0) {
		$sql .= ' and imo_cls_id = ' . $cls_id . ' ';
	}
	if (!is_null($is_main)) {
		$is_main = intval($is_main) == 0 ? 0 : 1;
		$sql .= ' and imo_is_main = ' . $is_main . ' ';
	}

	$sql .= " and imo_date_deleted is null";

	$sql .= ' and imo_tnt_id=' . $config['tnt_id'] . ' ';

	$res = ria_mysql_query($sql);

	if ($update_count) {
		img_images_count_update( $img_id );
	}

	return $res;
}

/** Cette fonction permet la mise à jour de l'option image principale
 * @param bool $is_main Obligatoire, image principale ? null => toutes les images, 0 => non principales, 1 => principales
 * @param int $type_id Facultatif, Identifiant du type d'image
 * @param int $obj_id_0 Facultatif, Identifiant de l'object
 * @param int $obj_id_1 Facultatif, Identifiant de l'object
 * @param int $obj_id_2 Facultatif, Identifiant de l'object
 * @param int $imo_id Facultatif, identifiant de l'objet période sur lequel filtrer le résultat
 * @param int $img_id Facultatif, identifiant de l'image ou tableau d'identifiants
 * @param int $cls_id Facultatif, identifiant de la classe
 * @param bool $publish Facultatif, image publiée ? null => toutes les images, 0 => non publiées, 1 => publiées
 *
 *	@return bool true l'update s'est bien déroulé, false sinon
 */
function img_images_objects_set_is_main($is_main, $type_id = 0, $obj_id_0 = 0, $obj_id_1 = 0, $obj_id_2 = 0, $imo_id = 0, $img_id = 0, $cls_id = 0, $publish = null){
	if (!is_bool($is_main) && !is_numeric($is_main)) return false;
	if (!is_numeric($type_id) || $type_id < 0) return false;
	if (!is_numeric($obj_id_0)) return false;
	if (!is_numeric($obj_id_1)) return false;
	if (!is_numeric($obj_id_2)) return false;
	if (!is_numeric($cls_id)) return false;
	if (!is_null($publish) && (!is_bool($publish) && !is_numeric($publish))) return false;
	$imo_id = control_array_integer($imo_id, false);
	if ($imo_id === false) {
		return false;
	}
	$img_id = control_array_integer($img_id, false);
	if ($img_id === false) {
		return false;
	}

	$is_main = intval($is_main) == 0 ? 0 : 1;

	global $config;
	$sql = '
		update img_images_objects set imo_is_main = ' . $is_main . ', imo_date_modified = now()
		where 1
	';

	if ($type_id > 0) {
		$sql .= ' and imo_type_id=' . $type_id . ' ';
	}

	if (sizeof($imo_id)) {
		$sql .= ' and imo_id in (' . implode(', ', $imo_id) . ')';
	}

	if ($obj_id_0 >= 0) {
		$sql .= ' and imo_obj_id_0 = ' . $obj_id_0 . ' ';
	}

	if ($obj_id_1 >= 0) {
		$sql .= ' and imo_obj_id_1 = ' . $obj_id_1 . ' ';
	}

	if ($obj_id_2 >= 0) {
		$sql .= ' and imo_obj_id_2 = ' . $obj_id_2 . ' ';
	}
	if (sizeof($img_id) > 0) {
		$sql .= ' and imo_img_id in (' . implode(', ', $img_id) . ') ';
	}
	if ($cls_id > 0) {
		$sql .= ' and imo_cls_id = ' . $cls_id . ' ';
	}
	if (!is_null($publish)) {
		$publish = intval($publish) == 0 ? 0 : 1;
		$sql .= ' and imo_publish = ' . $publish . ' ';
	}

	$sql .= " and imo_date_deleted is null";

	$sql .= ' and imo_tnt_id=' . $config['tnt_id'] . ' ';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de générer une image à partir d'un canvas
 * 	Exemple de chemin de point : moveTo:1012.65076:389.44037,lineTo:971.62744:424.59778,lineTo:918.4872:476.20813
 * 			- moveTo => permet de placé le pointeur au coordonnées souhaitées (x:y)
 * 			- lineTo => permet de tracer un trait jusqu'au coordonnées souhaitées (x:y)
 *
 *	@param string $path Obligatoire, chemin des points permettant le dessin
 *	@param string $filename Optionnel, chemin où l'on souhaite sauvegarder l'image (si non renseigné alors le blob de l'image est retourné)
 *	@param array $params Optionnel, paramètre de génération de l'image :
 * 			- width : largeur de l'image - par défaut 350 pixels
 * 			- height : hauteur de l'image - par défaut 150 pixels
 * 			- margin : marge à appliquer sur le chemin originial (en pixels) - par défaut 0
 * 			- fill-color : couleur des traits - par défaut 'black'
 * 			- background : couleur de fond de l'image - par défaut 'white'
 * 			- ext : extension de l'image ('jpg', 'png') - par défaut 'png'
 *
 * 	@return bool Seulement si $filename est renseigné, true ou false selon si l'image a bien été enregistrée
 * 	@return string Seulement si $filename n'est pas renseigné, retourne le blob de l'image
 */
function img_images_create_from_canvas( $path, $filename='', $params=[] ){
	if( trim($path) == '' ){
		return false;
	}

	$params['width'] 			= ria_array_get( $params, 'width', 350 );
	$params['height'] 		= ria_array_get( $params, 'height', 150 );
	$params['margin'] 		= ria_array_get( $params, 'margin', 0 );
	$params['fill-color'] = ria_array_get( $params, 'fill-color', 'black' );
	$params['background'] = ria_array_get( $params, 'background', 'white' );
	$params['ext'] 				= ria_array_get( $params, 'ext', 'png' );

	$path = explode( ',', $path );

	// Recherche le maximal de l'abscisse et l'ordonnée
	$max_x = $max_y = 0;
	foreach( $path  as $line ){
		$l =  explode(':', $line);

		// Abscisse
		if( $l[1] > $max_x ){
			$max_x = $l[1];
		}

		// Ordonnée
		if( $l[2] > $max_y ){
			$max_y = $l[2];
		}
	}

	// Calcul d'un ratio selon les dimensions souhaitées de l'image
	// Une marge peut être ajoutée
	$ratio_x = $params['width']  / ( $max_x + $params['margin'] );
	$ratio_y = $params['height'] / ( $max_y + $params['margin'] );

	// Création du dessin via Imagick
	$draw = new \ImagickDraw();

	// Défini la couleur du trait
	$draw->setFillColor( $params['fill-color'] );

	// Initialise à position
	$xs = $ys = 0;

	// Désine le canvas
	foreach( $path  as $line ){
		$l =  explode(':', $line);

		$l[1] = $l[1] * $ratio_x;
		$l[2] = $l[2] * $ratio_y;

		// Repositionne le pointeur de départ
		if( $l[0] == 'moveTo' ){
			$xs = $l[1];
			$ys = $l[2];
			continue;
		}

		$draw->line( $xs, $ys, $l[1], $l[2] );

		$xs = $l[1];
		$ys = $l[2];
	}

	// Création de l'image
	$imagick = new \Imagick();

	// Détermine les dimensions de l'image + la couleur de fond
	$imagick->newImage( $params['width'], $params['height'], $params['background'] );

	// Défini le format de l'image
	$imagick->setImageFormat( $params['ext'] );

	// Ajout du dessin dans l'image
	$imagick->drawImage($draw);

	// Sauvegarde l'image ou retour un blob
	if( $filename !== '' ){
		// Sauvegarde de l'image à l'emplacement souhaité
		if( !file_put_contents($filename, $imagick->getImageBlob()) ){
			return false;
		}

		return true;
	}

	return $imagick->getImageBlob();
}
/// @}