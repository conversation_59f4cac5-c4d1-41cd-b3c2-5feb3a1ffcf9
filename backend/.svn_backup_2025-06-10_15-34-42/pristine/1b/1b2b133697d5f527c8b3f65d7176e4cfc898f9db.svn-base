# ProfilePictureInfoWithDefault

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**profile_picture_url** | [**\Swagger\Client\Model\ProfilePictureUrl**](ProfilePictureUrl.md) |  | [optional] 
**profile_picture_selected** | [**\Swagger\Client\Model\ProfilePictureSelected**](ProfilePictureSelected.md) |  | 
**initials_profile_picture_url** | [**\Swagger\Client\Model\InitialsProfilePictureUrl**](InitialsProfilePictureUrl.md) |  | 
**gravatar_profile_picture_url** | [**\Swagger\Client\Model\GravatarProfilePictureUrl**](GravatarProfilePictureUrl.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


