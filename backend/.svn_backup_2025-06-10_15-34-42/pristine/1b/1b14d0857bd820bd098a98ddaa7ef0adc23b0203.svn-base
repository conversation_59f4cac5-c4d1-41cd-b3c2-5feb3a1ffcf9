# NextContractInfo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**trial_period_in_month** | **int** | The trial period in month | [optional] 
**billing_period_percent_discount** | **double** | The percent discount related to the billing period | [optional] 
**discount_duration_in_month** | **int** | The discount duration in month | [optional] 
**percent_discount** | **double** | The percent of the discount | [optional] 
**offer_id** | [**\Swagger\Client\Model\OfferId**](OfferId.md) |  | [optional] 
**store_count** | [**\Swagger\Client\Model\StoreCount**](StoreCount.md) |  | [optional] 
**start_utc_date** | [**\DateTime**](\DateTime.md) | The start date of your contract | [optional] 
**commitment_calculated_finish_utc_date** | [**\DateTime**](\DateTime.md) | The calculated end date of commitment | [optional] 
**billing_period_in_month** | **int** | The billing period in month | [optional] 
**fixed_price** | **double** | The fixed price of your contract | [optional] 
**offer_name** | **string** | The offer name based on /offers | [optional] 
**currency_code** | [**\Swagger\Client\Model\BeezUPCommonCurrencyCode**](BeezUPCommonCurrencyCode.md) |  | [optional] 
**contract_id** | [**\Swagger\Client\Model\ContractId**](ContractId.md) |  | [optional] 
**commitment_period_in_month** | **int** | The commitment period in month | [optional] 
**click_included** | **int** | The click included | [optional] 
**additional_click_price** | **double** | Additional click price | [optional] 
**ip_user_creation** | **string** | The IP of the user who creates the contract | [optional] 
**ip_user_modification** | **string** | The IP of the user who modified the contract | [optional] 
**fixed_and_variable_click_info** | [**\Swagger\Client\Model\FixedAndVariableClickModelInfo**](FixedAndVariableClickModelInfo.md) |  | [optional] 
**variable_model_info** | [**\Swagger\Client\Model\VariableModelInfo**](VariableModelInfo.md) |  | [optional] 
**is_commitment_renewal_automatically** | **bool** | Is commitment is automatically renewed | [optional] 
**discount_end_utc_date** | [**\DateTime**](\DateTime.md) | The end of your discount | [optional] 
**is_modifiable_contract** | **bool** | Is the contract is modifiable ? | [optional] 
**links** | [**\Swagger\Client\Model\NextContractInfoLinks**](NextContractInfoLinks.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


