<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1alpha1/conformance_service.proto

namespace GPBMetadata\Google\Api\Expr\V1Alpha1;

class ConformanceService
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Client::initOnce();
        \GPBMetadata\Google\Api\Expr\V1Alpha1\Checked::initOnce();
        \GPBMetadata\Google\Api\Expr\V1Alpha1\PBEval::initOnce();
        \GPBMetadata\Google\Api\Expr\V1Alpha1\Syntax::initOnce();
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Rpc\Status::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0af50e0a32676f6f676c652f6170692f657870722f7631616c706861312f" .
            "636f6e666f726d616e63655f736572766963652e70726f746f1218676f6f" .
            "676c652e6170692e657870722e7631616c706861311a26676f6f676c652f" .
            "6170692f657870722f7631616c706861312f636865636b65642e70726f74" .
            "6f1a23676f6f676c652f6170692f657870722f7631616c706861312f6576" .
            "616c2e70726f746f1a25676f6f676c652f6170692f657870722f7631616c" .
            "706861312f73796e7461782e70726f746f1a1f676f6f676c652f6170692f" .
            "6669656c645f6265686176696f722e70726f746f1a17676f6f676c652f72" .
            "70632f7374617475732e70726f746f22700a0c5061727365526571756573" .
            "7412170a0a63656c5f736f757263651801200128094203e0410212160a0e" .
            "73796e7461785f76657273696f6e18022001280912170a0f736f75726365" .
            "5f6c6f636174696f6e18032001280912160a0e64697361626c655f6d6163" .
            "726f73180420012808226e0a0d5061727365526573706f6e736512390a0b" .
            "7061727365645f6578707218012001280b32242e676f6f676c652e617069" .
            "2e657870722e7631616c706861312e5061727365644578707212220a0669" .
            "737375657318022003280b32122e676f6f676c652e7270632e5374617475" .
            "7322a7010a0c436865636b52657175657374123e0a0b7061727365645f65" .
            "78707218012001280b32242e676f6f676c652e6170692e657870722e7631" .
            "616c706861312e506172736564457870724203e0410212300a0874797065" .
            "5f656e7618022003280b321e2e676f6f676c652e6170692e657870722e76" .
            "31616c706861312e4465636c12110a09636f6e7461696e65721803200128" .
            "0912120a0a6e6f5f7374645f656e7618042001280822700a0d436865636b" .
            "526573706f6e7365123b0a0c636865636b65645f6578707218012001280b" .
            "32252e676f6f676c652e6170692e657870722e7631616c706861312e4368" .
            "65636b65644578707212220a0669737375657318022003280b32122e676f" .
            "6f676c652e7270632e53746174757322c6020a0b4576616c526571756573" .
            "74123b0a0b7061727365645f6578707218012001280b32242e676f6f676c" .
            "652e6170692e657870722e7631616c706861312e50617273656445787072" .
            "4800123d0a0c636865636b65645f6578707218022001280b32252e676f6f" .
            "676c652e6170692e657870722e7631616c706861312e436865636b656445" .
            "787072480012450a0862696e64696e677318032003280b32332e676f6f67" .
            "6c652e6170692e657870722e7631616c706861312e4576616c5265717565" .
            "73742e42696e64696e6773456e74727912110a09636f6e7461696e657218" .
            "04200128091a540a0d42696e64696e6773456e747279120b0a036b657918" .
            "012001280912320a0576616c756518022001280b32232e676f6f676c652e" .
            "6170692e657870722e7631616c706861312e4578707256616c75653a0238" .
            "01420b0a09657870725f6b696e6422670a0c4576616c526573706f6e7365" .
            "12330a06726573756c7418012001280b32232e676f6f676c652e6170692e" .
            "657870722e7631616c706861312e4578707256616c756512220a06697373" .
            "75657318022003280b32122e676f6f676c652e7270632e53746174757322" .
            "e8010a0c497373756544657461696c7312410a0873657665726974791801" .
            "2001280e322f2e676f6f676c652e6170692e657870722e7631616c706861" .
            "312e497373756544657461696c732e5365766572697479123a0a08706f73" .
            "6974696f6e18022001280b32282e676f6f676c652e6170692e657870722e" .
            "7631616c706861312e536f75726365506f736974696f6e120a0a02696418" .
            "0320012803224d0a08536576657269747912180a1453455645524954595f" .
            "554e5350454349464945441000120f0a0b4445505245434154494f4e1001" .
            "120b0a075741524e494e47100212090a054552524f52100332d7020a1243" .
            "6f6e666f726d616e63655365727669636512670a05506172736512262e67" .
            "6f6f676c652e6170692e657870722e7631616c706861312e506172736552" .
            "6571756573741a272e676f6f676c652e6170692e657870722e7631616c70" .
            "6861312e5061727365526573706f6e7365220dda410a63656c5f736f7572" .
            "636512680a05436865636b12262e676f6f676c652e6170692e657870722e" .
            "7631616c706861312e436865636b526571756573741a272e676f6f676c65" .
            "2e6170692e657870722e7631616c706861312e436865636b526573706f6e" .
            "7365220eda410b7061727365645f6578707212570a044576616c12252e67" .
            "6f6f676c652e6170692e657870722e7631616c706861312e4576616c5265" .
            "71756573741a262e676f6f676c652e6170692e657870722e7631616c7068" .
            "61312e4576616c526573706f6e736522001a15ca411263656c2e676f6f67" .
            "6c65617069732e636f6d427a0a1c636f6d2e676f6f676c652e6170692e65" .
            "7870722e7631616c706861314217436f6e666f726d616e63655365727669" .
            "636550726f746f50015a3c676f6f676c652e676f6c616e672e6f72672f67" .
            "656e70726f746f2f676f6f676c65617069732f6170692f657870722f7631" .
            "616c706861313b65787072f80101620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

