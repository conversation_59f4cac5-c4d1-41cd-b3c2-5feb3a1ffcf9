/* Ensembles des messages d'information, warning, erreurs et retour d'événements. */
.error {
  color: red;
  background-color: #FFDDDD;
  border-color: red;
}

.notice-default {
  background-color: $yellow-light;
  border-color: $yellow-medium;
}
.success {
  color: $green;
  background-color: #ddffdd;
  border-color: $green;
  p {
    padding: 0 !important;
  }
}
.error-success {
  color: $green;
  background-color: #C2FFBF;
  border-color: $green;
}
.notice, .notice-default, .error, .error-success, .success {
  display: block;
  border-radius: 5px;
  margin-bottom: 10px;
  padding: 5px;
  border-style: solid;
  border-width: 1px;
  > *:last-child {
    margin-bottom: 0;
  }
  b {
    font-size: 600;
  }
  label {
    font-weight: 600;
  }
  select {
    width: auto;
    margin-left: 45px;	
  }
  .more {
    display: block;
    text-decoration: underline;
  }
  .more-info,
  .more-hide {
   display: none;
  }
  &.inline-block {
    display: inline-block;
    margin: 0;
  }
}
.notice {
  position: relative;
  background: $info;
  border: 1px solid $info-border;
  border-radius: 4px;
  color: $medium-dark-color;
  padding-left: 28px;

  &:before {
    position: absolute;
    content: "";
    top: 50%;
    margin-top: -12px;
    left: 2px;
    width: 24px;
    height: 24px;
    background-image: url('/admin/images/info-light.svg');
    background-size: contain;
    background-repeat: no-repeat;
  }

  a {
    text-decoration: underline;
    &:hover {
      text-decoration: none;
    }
  }

  &.header {
	  &:before {
		  background: none;
	  }
  }
}
.notice-msg {
  margin-top: 10px;
}
.notice-default p {
  text-align: left;
  margin-left: 165px;
}

table .error {
  color: #ff0000 !important;
  background-color: white !important;
  border-style: none;
  margin: 0;
  padding: 0 !important;
}

tr, th {
  &.error {
    display: table-row;
  }
}

.load-ajax-opacity {
	width: 100%;
	height: 100%;
	position: fixed;
  top: 0;
  left: 0;
	background: black;
	opacity: 0.5;
	z-index: 9998;
	display: none;
}

.message-ajax-opacity {
	display: none;
	z-index: 9999;
	position: fixed;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

@include media('<smallmedium') {
  .notice-default {
    .select-client {
      vertical-align: middle;
    }
    .select-client, p {
      margin-left: 0 !important;
    }
  }
}
