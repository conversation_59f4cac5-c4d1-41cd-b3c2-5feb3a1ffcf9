<?php
/**
 * ChannelCatalogExportationReporting
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * ChannelCatalogExportationReporting Class Doc Comment
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class ChannelCatalogExportationReporting implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'channelCatalogExportationReporting';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'exportation_utc_date' => '\DateTime',
        'exportation_duration' => 'string',
        'cache_status' => 'string',
        'exported_product_count' => 'int',
        'client_ip_address' => 'string',
        'client_user_agent' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'exportation_utc_date' => 'date-time',
        'exportation_duration' => null,
        'cache_status' => null,
        'exported_product_count' => 'int32',
        'client_ip_address' => null,
        'client_user_agent' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'exportation_utc_date' => 'exportationUtcDate',
        'exportation_duration' => 'exportationDuration',
        'cache_status' => 'cacheStatus',
        'exported_product_count' => 'exportedProductCount',
        'client_ip_address' => 'clientIpAddress',
        'client_user_agent' => 'clientUserAgent'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'exportation_utc_date' => 'setExportationUtcDate',
        'exportation_duration' => 'setExportationDuration',
        'cache_status' => 'setCacheStatus',
        'exported_product_count' => 'setExportedProductCount',
        'client_ip_address' => 'setClientIpAddress',
        'client_user_agent' => 'setClientUserAgent'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'exportation_utc_date' => 'getExportationUtcDate',
        'exportation_duration' => 'getExportationDuration',
        'cache_status' => 'getCacheStatus',
        'exported_product_count' => 'getExportedProductCount',
        'client_ip_address' => 'getClientIpAddress',
        'client_user_agent' => 'getClientUserAgent'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['exportation_utc_date'] = isset($data['exportation_utc_date']) ? $data['exportation_utc_date'] : null;
        $this->container['exportation_duration'] = isset($data['exportation_duration']) ? $data['exportation_duration'] : null;
        $this->container['cache_status'] = isset($data['cache_status']) ? $data['cache_status'] : null;
        $this->container['exported_product_count'] = isset($data['exported_product_count']) ? $data['exported_product_count'] : null;
        $this->container['client_ip_address'] = isset($data['client_ip_address']) ? $data['client_ip_address'] : null;
        $this->container['client_user_agent'] = isset($data['client_user_agent']) ? $data['client_user_agent'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['exportation_utc_date'] === null) {
            $invalidProperties[] = "'exportation_utc_date' can't be null";
        }
        if (!is_null($this->container['exportation_duration']) && !preg_match("/(00:1[5-9])|(00:[2-5][0-9])|(0[1-9]:[0-5][0-9])|(1[0-9]:[0-5][0-9])|(2[0-3]:[0-5][0-9])/", $this->container['exportation_duration'])) {
            $invalidProperties[] = "invalid value for 'exportation_duration', must be conform to the pattern /(00:1[5-9])|(00:[2-5][0-9])|(0[1-9]:[0-5][0-9])|(1[0-9]:[0-5][0-9])|(2[0-3]:[0-5][0-9])/.";
        }

        if ($this->container['client_ip_address'] === null) {
            $invalidProperties[] = "'client_ip_address' can't be null";
        }
        if ($this->container['client_user_agent'] === null) {
            $invalidProperties[] = "'client_user_agent' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['exportation_utc_date'] === null) {
            return false;
        }
        if (!preg_match("/(00:1[5-9])|(00:[2-5][0-9])|(0[1-9]:[0-5][0-9])|(1[0-9]:[0-5][0-9])|(2[0-3]:[0-5][0-9])/", $this->container['exportation_duration'])) {
            return false;
        }
        if ($this->container['client_ip_address'] === null) {
            return false;
        }
        if ($this->container['client_user_agent'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets exportation_utc_date
     *
     * @return \DateTime
     */
    public function getExportationUtcDate()
    {
        return $this->container['exportation_utc_date'];
    }

    /**
     * Sets exportation_utc_date
     *
     * @param \DateTime $exportation_utc_date The exportation UTC date
     *
     * @return $this
     */
    public function setExportationUtcDate($exportation_utc_date)
    {
        $this->container['exportation_utc_date'] = $exportation_utc_date;

        return $this;
    }

    /**
     * Gets exportation_duration
     *
     * @return string
     */
    public function getExportationDuration()
    {
        return $this->container['exportation_duration'];
    }

    /**
     * Sets exportation_duration
     *
     * @param string $exportation_duration The exportation duration. \\ '00:01:00' measn 1 minute.
     *
     * @return $this
     */
    public function setExportationDuration($exportation_duration)
    {

        if (!is_null($exportation_duration) && (!preg_match("/(00:1[5-9])|(00:[2-5][0-9])|(0[1-9]:[0-5][0-9])|(1[0-9]:[0-5][0-9])|(2[0-3]:[0-5][0-9])/", $exportation_duration))) {
            throw new \InvalidArgumentException("invalid value for $exportation_duration when calling ChannelCatalogExportationReporting., must conform to the pattern /(00:1[5-9])|(00:[2-5][0-9])|(0[1-9]:[0-5][0-9])|(1[0-9]:[0-5][0-9])|(2[0-3]:[0-5][0-9])/.");
        }

        $this->container['exportation_duration'] = $exportation_duration;

        return $this;
    }

    /**
     * Gets cache_status
     *
     * @return string
     */
    public function getCacheStatus()
    {
        return $this->container['cache_status'];
    }

    /**
     * Sets cache_status
     *
     * @param string $cache_status The cache status during the exportation
     *
     * @return $this
     */
    public function setCacheStatus($cache_status)
    {
        $this->container['cache_status'] = $cache_status;

        return $this;
    }

    /**
     * Gets exported_product_count
     *
     * @return int
     */
    public function getExportedProductCount()
    {
        return $this->container['exported_product_count'];
    }

    /**
     * Sets exported_product_count
     *
     * @param int $exported_product_count The exportated product count during this exportation
     *
     * @return $this
     */
    public function setExportedProductCount($exported_product_count)
    {
        $this->container['exported_product_count'] = $exported_product_count;

        return $this;
    }

    /**
     * Gets client_ip_address
     *
     * @return string
     */
    public function getClientIpAddress()
    {
        return $this->container['client_ip_address'];
    }

    /**
     * Sets client_ip_address
     *
     * @param string $client_ip_address The IP address of the client who requests this exportation
     *
     * @return $this
     */
    public function setClientIpAddress($client_ip_address)
    {
        $this->container['client_ip_address'] = $client_ip_address;

        return $this;
    }

    /**
     * Gets client_user_agent
     *
     * @return string
     */
    public function getClientUserAgent()
    {
        return $this->container['client_user_agent'];
    }

    /**
     * Sets client_user_agent
     *
     * @param string $client_user_agent The http header User-Agent sent by the client who requests this operation
     *
     * @return $this
     */
    public function setClientUserAgent($client_user_agent)
    {
        $this->container['client_user_agent'] = $client_user_agent;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


