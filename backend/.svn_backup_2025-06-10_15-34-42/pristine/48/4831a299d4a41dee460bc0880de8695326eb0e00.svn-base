<?php

	/**	\file ajax-fields.php
	 * 
	 * 	Cette page fournit la liste des valeurs de restriction pour un champ personnalisé et une langue donnée.
	 * 	La réponse est fournie au format HTML, sous forme d'une liste de balises <option> destinée à remplir
	 * 	une balise <select>.
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_FIELD');

	require_once('fields.inc.php');
	
	if( isset($_POST['restrictedValue'], $_POST['lng'], $_POST['fld']) ){
		// Récupère les valeurs de restriction de ce champ pour la langue choisie
		$rvalues = fld_restricted_values_get( 0, $_POST['fld'], '', 0, '', $_POST['lng'] );
		
		if( $rvalues ){
			while( $value = ria_mysql_fetch_array($rvalues) ){
				print '<option value="'.$value['id'].'">'.htmlspecialchars($value['name']).'</option>';
			}
		}
	}
