<?php
namespace EventService;
/**
 *	\ingroup EventService
 *  Le Dispatcher permet de faire la liaison entre les event et les listeners, c'est le responsable d'émettre un event et que celui-ci passe par les listeners
 */
class Dispatcher
{
	/**
	 * $container
	 *
	 * @var Container|null $container
	 */
	protected $container;
	/**
	 * $listeners
	 *
	 * @var array $listeners
	 */
	protected $listeners = array();
	/**
	 * $wildcards
	 *
	 * @var array $wildcards
	 */
	protected $wildcards = array();
	/**
     * Create a new event dispatcher instance.
     *
     * @param  Container|null  $container
     * @return void
     */
    public function __construct($container = null)
    {
		if (!is_null($container)) {
			$this->container = $container;
		}
	}
	/**
     * Register an event listener with the dispatcher.
     *
     * @param  string|array  $events
     * @param  mixed  $listener
     * @return void
     */
    public function listen($events, $listener)
    {
        foreach ((array) $events as $event) {
            if (strstr($event, '*')) {
                $this->setupWildcardListen($event, $listener);
            } else {
                $this->listeners[$event][] = $this->makeListener($listener);
            }
        }
	}
	/**
     * Determine if a given event has listeners.
     *
     * @param  string  $eventName
     * @return bool
     */
    public function hasListeners($eventName)
    {
        return isset($this->listeners[$eventName]) || isset($this->wildcards[$eventName]);
    }
	/**
     * Setup a wildcard listener callback.
     *
     * @param  string  $event
     * @param  mixed  $listener
     * @return void
     */
    protected function setupWildcardListen($event, $listener)
    {
        $this->wildcards[$event][] = $this->makeListener($listener, true);
	}
	/**
     * Register an event listener with the dispatcher.
     *
     * @param  \Closure|string  $listener
     * @param  bool  $wildcard
     * @return Closure
     */
    public function makeListener($listener, $wildcard = false)
    {
        if (is_string($listener)) {
            return $this->createClassListener($listener, $wildcard);
        }
        return function ($event, $payload) use ($listener, $wildcard) {
            if ($wildcard) {
                return $listener($event, $payload);
            }
            return call_user_func_array($listener, array_values($payload));
        };
	}
	/**
     * Create a class based listener using the IoC container.
     *
     * @param  string  $listener
     * @param  bool  $wildcard
     * @return Closure
     */
    public function createClassListener($listener, $wildcard = false)
    {
        $callable = $this->createClassCallable($listener);
        return function ($event, $payload) use ( $callable, $wildcard) {
            if ($wildcard) {
                return call_user_func($callable, $event, $payload);
            }
            return call_user_func_array(
                $callable, $payload
            );
        };
	}
	/**
     * Create the class based event callable.
     *
     * @param  string  $listener
     * @return callable
     */
    protected function createClassCallable($listener)
    {
		$class = $listener;
		$method = 'handle';

        return array(new $class, $method);
	}

    /**
     * Fire an event and call the listeners. alias de dispatch
     *
     * @param  string|object  $event
     * @param  mixed  $payload
     * @param  bool  $halt
     * @return array|null
     */
    public function emit($event, $payload = array(), $halt = false)
    {
        return $this->dispatch($event, $payload, $halt);
    }
	/**
     * Fire an event and call the listeners.
     *
     * @param  string|object  $event
     * @param  mixed  $payload
     * @param  bool  $halt
     * @return array|null
     */
    public function dispatch($event, $payload = array(), $halt = false)
    {
        // When the given "event" is actually an object we will assume it is an event
        // object and use the class as the event name and this event itself as the
        // payload to the handler, which makes object based events quite simple.
        list($event, $payload) = $this->parseEventAndPayload(
            $event, $payload
        );

        $responses = array();
        foreach ($this->getListeners($event) as $listener) {
            $response = $listener($event, $payload);
            // If a response is returned from the listener and event halting is enabled
            // we will just return this response, and not call the rest of the event
            // listeners. Otherwise we will add the response on the response list.
            if ($halt && ! is_null($response)) {
                return $response;
            }
            // If a boolean false is returned from a listener, we will stop propagating
            // the event to any further listeners down in the chain, else we keep on
            // looping through the listeners and firing every one in our sequence.
            if ($response === false) {
                break;
            }
            $responses[] = $response;
        }
        return $halt ? null : $responses;
	}
	/**
     * Get all of the listeners for a given event name.
     *
     * @param  string  $eventName
     * @return array
     */
    public function getListeners($eventName)
    {
        $listeners = isset($this->listeners[$eventName]) ? $this->listeners[$eventName] : array();
        $listeners = array_merge(
            $listeners, $this->getWildcardListeners($eventName)
        );
        return class_exists($eventName, false)
                    ? $this->addInterfaceListeners($eventName, $listeners)
                    : $listeners;
	}
	/**
     * Add the listeners for the event's interfaces to the given array.
     *
     * @param  string  $eventName
     * @param  array  $listeners
     * @return array
     */
    protected function addInterfaceListeners($eventName, array $listeners = array())
    {
        foreach (class_implements($eventName) as $interface) {
            if (isset($this->listeners[$interface])) {
                foreach ($this->listeners[$interface] as $names) {
                    $listeners = array_merge($listeners, (array) $names);
                }
            }
        }
        return $listeners;
    }
	/**
     * Get the wildcard listeners for the event.
     *
     * @param  string  $eventName
     * @return array
     */
    protected function getWildcardListeners($eventName)
    {
        $wildcards = array();
        foreach ($this->wildcards as $key => $listeners) {
            if ($key == $eventName) {
                $wildcards = array_merge($wildcards, $listeners);
            }
        }
        return $wildcards;
    }
	/**
     * Parse the given event and payload and prepare them for dispatching.
     *
     * @param  mixed  $event
     * @param  mixed  $payload
     * @return array
     */
    protected function parseEventAndPayload($event, $payload)
    {
        if (is_object($event)) {
            list($payload, $event) = array(array($event), get_class($event));
        }
        return array($event, is_array($payload) ? $payload : array($payload));
    }
    /**
     * Register an event subscriber with the dispatcher.
     *
     * @param  object|string  $subscriber
     * @return void
     */
    public function subscribe($subscriber)
    {
        $subscriber = $this->resolveSubscriber($subscriber);
        $subscriber->subscribe($this);
    }
    /**
     * Resolve the subscriber instance.
     *
     * @param  object|string  $subscriber
     * @return mixed
     */
    protected function resolveSubscriber($subscriber)
    {
        if (is_string($subscriber)) {
            // return $this->container->make($subscriber);
            return new $subscriber;
        }
        return $subscriber;
    }
}