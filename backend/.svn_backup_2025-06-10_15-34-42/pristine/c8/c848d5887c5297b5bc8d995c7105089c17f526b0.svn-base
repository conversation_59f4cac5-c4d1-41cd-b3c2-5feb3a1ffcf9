<?php
/**
 * CurrentContractInfoTest
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Please update the test case below to test the model.
 */

namespace Swagger\Client;

/**
 * CurrentContractInfoTest Class Doc Comment
 *
 * @category    Class
 * @description CurrentContractInfo
 * @package     Swagger\Client
 * <AUTHOR> Codegen team
 * @link        https://github.com/swagger-api/swagger-codegen
 */
class CurrentContractInfoTest extends \PHPUnit_Framework_TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass()
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp()
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown()
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass()
    {
    }

    /**
     * Test "CurrentContractInfo"
     */
    public function testCurrentContractInfo()
    {
    }

    /**
     * Test attribute "trial_period_in_month"
     */
    public function testPropertyTrialPeriodInMonth()
    {
    }

    /**
     * Test attribute "billing_period_percent_discount"
     */
    public function testPropertyBillingPeriodPercentDiscount()
    {
    }

    /**
     * Test attribute "discount_duration_in_month"
     */
    public function testPropertyDiscountDurationInMonth()
    {
    }

    /**
     * Test attribute "percent_discount"
     */
    public function testPropertyPercentDiscount()
    {
    }

    /**
     * Test attribute "offer_id"
     */
    public function testPropertyOfferId()
    {
    }

    /**
     * Test attribute "store_count"
     */
    public function testPropertyStoreCount()
    {
    }

    /**
     * Test attribute "start_utc_date"
     */
    public function testPropertyStartUtcDate()
    {
    }

    /**
     * Test attribute "commitment_calculated_finish_utc_date"
     */
    public function testPropertyCommitmentCalculatedFinishUtcDate()
    {
    }

    /**
     * Test attribute "billing_period_in_month"
     */
    public function testPropertyBillingPeriodInMonth()
    {
    }

    /**
     * Test attribute "fixed_price"
     */
    public function testPropertyFixedPrice()
    {
    }

    /**
     * Test attribute "offer_name"
     */
    public function testPropertyOfferName()
    {
    }

    /**
     * Test attribute "currency_code"
     */
    public function testPropertyCurrencyCode()
    {
    }

    /**
     * Test attribute "contract_id"
     */
    public function testPropertyContractId()
    {
    }

    /**
     * Test attribute "commitment_period_in_month"
     */
    public function testPropertyCommitmentPeriodInMonth()
    {
    }

    /**
     * Test attribute "click_included"
     */
    public function testPropertyClickIncluded()
    {
    }

    /**
     * Test attribute "additional_click_price"
     */
    public function testPropertyAdditionalClickPrice()
    {
    }

    /**
     * Test attribute "ip_user_creation"
     */
    public function testPropertyIpUserCreation()
    {
    }

    /**
     * Test attribute "ip_user_modification"
     */
    public function testPropertyIpUserModification()
    {
    }

    /**
     * Test attribute "fixed_and_variable_click_info"
     */
    public function testPropertyFixedAndVariableClickInfo()
    {
    }

    /**
     * Test attribute "variable_model_info"
     */
    public function testPropertyVariableModelInfo()
    {
    }

    /**
     * Test attribute "is_commitment_renewal_automatically"
     */
    public function testPropertyIsCommitmentRenewalAutomatically()
    {
    }

    /**
     * Test attribute "discount_end_utc_date"
     */
    public function testPropertyDiscountEndUtcDate()
    {
    }

    /**
     * Test attribute "is_modifiable_contract"
     */
    public function testPropertyIsModifiableContract()
    {
    }

    /**
     * Test attribute "links"
     */
    public function testPropertyLinks()
    {
    }
}
