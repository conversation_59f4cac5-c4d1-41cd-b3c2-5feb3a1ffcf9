Object access
-----
<?php

// property fetch variations
$a->b;
$a->b['c'];
$a->b{'c'};

// method call variations
$a->b();
$a->{'b'}();
$a->$b();
$a->$b['c']();

// array dereferencing
$a->b()['c'];
$a->b(){'c'}; // invalid PHP: drop Support?
-----
!!php5
array(
    0: Expr_PropertyFetch(
        var: Expr_Variable(
            name: a
            comments: array(
                0: // property fetch variations
            )
        )
        name: b
        comments: array(
            0: // property fetch variations
        )
    )
    1: Expr_ArrayDimFetch(
        var: Expr_PropertyFetch(
            var: Expr_Variable(
                name: a
            )
            name: b
        )
        dim: Scalar_String(
            value: c
        )
    )
    2: Expr_ArrayDimFetch(
        var: Expr_PropertyFetch(
            var: Expr_Variable(
                name: a
            )
            name: b
        )
        dim: Scalar_String(
            value: c
        )
    )
    3: Expr_MethodCall(
        var: Expr_Variable(
            name: a
            comments: array(
                0: // method call variations
            )
        )
        name: b
        args: array(
        )
        comments: array(
            0: // method call variations
        )
    )
    4: Expr_MethodCall(
        var: Expr_Variable(
            name: a
        )
        name: Scalar_String(
            value: b
        )
        args: array(
        )
    )
    5: Expr_MethodCall(
        var: Expr_Variable(
            name: a
        )
        name: Expr_Variable(
            name: b
        )
        args: array(
        )
    )
    6: Expr_MethodCall(
        var: Expr_Variable(
            name: a
        )
        name: Expr_ArrayDimFetch(
            var: Expr_Variable(
                name: b
            )
            dim: Scalar_String(
                value: c
            )
        )
        args: array(
        )
    )
    7: Expr_ArrayDimFetch(
        var: Expr_MethodCall(
            var: Expr_Variable(
                name: a
                comments: array(
                    0: // array dereferencing
                )
            )
            name: b
            args: array(
            )
            comments: array(
                0: // array dereferencing
            )
        )
        dim: Scalar_String(
            value: c
        )
        comments: array(
            0: // array dereferencing
        )
    )
    8: Expr_ArrayDimFetch(
        var: Expr_MethodCall(
            var: Expr_Variable(
                name: a
            )
            name: b
            args: array(
            )
        )
        dim: Scalar_String(
            value: c
        )
    )
    9: Stmt_Nop(
        comments: array(
            0: // invalid PHP: drop Support?
        )
    )
)