<?php
/** \file worker.php
 *	\ingroup crontabs
 *
 * 	Ce script permet de lancer un worker pour traiter la pile de taches
 *  Exemple d'appel :
 *      - php worker.php --server=[name du serveur sql] --priority=[low ou high] --debug=[1 = pour avoir des log sur la console]
 */

set_include_path(dirname(__FILE__) . '/../include/');

require_once('ria.queue.inc.php');


$ar_params = getopt('.', array('server:', 'priority:', 'debug:'));

if (!ria_array_key_exists(array('server', 'priority'), $ar_params)) {
	error_log('Paramètre incorrect');
	exit;
}

if( !in_array($ar_params['priority'], array(RiaQueue::LOW, RiaQueue::HIGH, RiaQueue::HIGHER, RiaQueue::STATS))){
	error_log('Paramètre priorité incorrect');
	exit;
}

RiaQueue::getInstance($ar_params['server'])->launchWorks($ar_params['priority'], isset($ar_params['debug']) && $ar_params['debug']);