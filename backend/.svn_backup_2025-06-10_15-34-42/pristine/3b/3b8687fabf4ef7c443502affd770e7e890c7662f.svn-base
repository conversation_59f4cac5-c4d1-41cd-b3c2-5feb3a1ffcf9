<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/datastore/v1/entity.proto

namespace Google\Cloud\Datastore\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * An array value.
 *
 * Generated from protobuf message <code>google.datastore.v1.ArrayValue</code>
 */
class ArrayValue extends \Google\Protobuf\Internal\Message
{
    /**
     * Values in the array.
     * The order of values in an array is preserved as long as all values have
     * identical settings for 'exclude_from_indexes'.
     *
     * Generated from protobuf field <code>repeated .google.datastore.v1.Value values = 1;</code>
     */
    private $values;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Cloud\Datastore\V1\Value[]|\Google\Protobuf\Internal\RepeatedField $values
     *           Values in the array.
     *           The order of values in an array is preserved as long as all values have
     *           identical settings for 'exclude_from_indexes'.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Datastore\V1\Entity::initOnce();
        parent::__construct($data);
    }

    /**
     * Values in the array.
     * The order of values in an array is preserved as long as all values have
     * identical settings for 'exclude_from_indexes'.
     *
     * Generated from protobuf field <code>repeated .google.datastore.v1.Value values = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getValues()
    {
        return $this->values;
    }

    /**
     * Values in the array.
     * The order of values in an array is preserved as long as all values have
     * identical settings for 'exclude_from_indexes'.
     *
     * Generated from protobuf field <code>repeated .google.datastore.v1.Value values = 1;</code>
     * @param \Google\Cloud\Datastore\V1\Value[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setValues($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\Datastore\V1\Value::class);
        $this->values = $arr;

        return $this;
    }

}

