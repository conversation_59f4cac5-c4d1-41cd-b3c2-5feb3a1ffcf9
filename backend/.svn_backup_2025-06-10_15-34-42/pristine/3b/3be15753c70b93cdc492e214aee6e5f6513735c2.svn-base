/*
	*	\brief Ce script est chargée d'installer quatre fonctions utilisateurs pour une base de données  Ces quatres fonctions sont destinées au calcul du tarif des produits.
*/

USE riashop;

/* Procède à la désinstallation des procédures et fonctions précédemment installées */
-- public
DROP FUNCTION IF EXISTS GET_TVA;
DROP FUNCTION IF EXISTS COMPARE_PRICES;
DROP FUNCTION IF EXISTS PRICE_IS_APPLY;
DROP FUNCTION IF EXISTS GET_PRICE_HT;
DROP FUNCTION IF EXISTS USR_IS_EXEMPT;
-- privé
DROP FUNCTION IF EXISTS RIA_IS_APPLY;
DROP FUNCTION IF EXISTS RIA_PRC_CONDITIONS_TEST_VALUE;
DROP FUNCTION IF EXISTS RIA_CHECK_PHYSIC_CONDITION;
DROP FUNCTION IF EXISTS RIA_CHECK_VIRTUAL_CONDITION;
DROP FUNCTION IF EXISTS RIA_GET_CHILD_VALUES;
DROP FUNCTION IF EXISTS RIA_PRICES_COMPARE_PRIORITY;
DROP FUNCTION IF EXISTS RIA_GET_PRICE_CAT_PRIORITY;
DROP FUNCTION IF EXISTS RIA_PRICE_CONDITION_EXIST;
DROP FUNCTION IF EXISTS RIA_PRICE_CONDITION_GET;
DROP FUNCTION IF EXISTS RIA_GET_VAL_ID_DEPTH_MAX;
DROP FUNCTION IF EXISTS RIA_IS_DECIMAL;
DROP FUNCTION IF EXISTS RIA_IS_UINT;
DROP FUNCTION IF EXISTS RIA_IS_BORNED_DECIMAL;
DROP FUNCTION IF EXISTS RIA_IS_DATE;
DROP FUNCTION IF EXISTS RIA_PARSE_DATE;
DROP FUNCTION IF EXISTS RIA_GET_ID_AT_POS;
DROP FUNCTION IF EXISTS RIA_GET_VAL_AT_POS;
DROP PROCEDURE IF EXISTS RIA_GET_BORNED_DATE;

DELIMITER //

/*
	*	\brief Recherche un entier à une position donnée dans une liste sérialisée, dont le séparateur est "|" (exemple : "|456|123|789")
	*	\param	string_base Obligatoire, non NULL, chaîne représentant la liste sérialisée
	*	\param	position Obligatoire, non NULL, position en base 0 du nombre dans la liste
	*	\return	La valeur du nombre entier stocké à la position donnée, ou NULL en cas d'échec
*/
CREATE FUNCTION RIA_GET_ID_AT_POS(string_base VARCHAR(1024), position INT) RETURNS INT DETERMINISTIC
BEGIN
	DECLARE i, val INT;
	
	SET i = 0;
	
	WHILE i <= position DO
		IF i < position THEN
			SET string_base = SUBSTRING(string_base, LOCATE('|', string_base, 2));
		ELSE
			IF LOCATE('|', string_base, 2) > 0 THEN
				SET string_base = SUBSTRING(string_base, LOCATE('|', string_base) + 1, LOCATE('|', string_base, 2) - LOCATE('|', string_base) - 1);
			ELSE
				SET string_base = SUBSTRING(string_base, LOCATE('|', string_base) + 1);
			END IF;
			IF string_base != '' THEN
				SET val = CAST(string_base AS UNSIGNED);
			END IF;
		END IF;
		SET i = i + 1;
	END WHILE;
	
	RETURN val;
	
END//

/*
	*	\brief Recherche un décimal à une position donnée dans une liste sérialisée, dont le séparateur est "|" (exemple : "|456.22|123.15|789.0")
	*	\param	string_base Obligatoire, non NULL, chaîne représentant la liste sérialisée
	*	\param	position Obligatoire, non NULL, position en base 0 du nombre dans la liste
	*	\return	La valeur du nombre décimal stocké à la position donnée, ou NULL en cas d'échec
*/
CREATE FUNCTION RIA_GET_VAL_AT_POS(string_base VARCHAR(1024), position INT) RETURNS DECIMAL(12, 6) DETERMINISTIC
BEGIN
	DECLARE i INT;
	DECLARE val DECIMAL(12, 6);
	
	SET i = 0;
	
	WHILE i <= position DO
		IF i < position THEN
			SET string_base = SUBSTRING(string_base, LOCATE('|', string_base, 2));
		ELSE
			IF LOCATE('|', string_base, 2) > 0 THEN
				SET string_base = SUBSTRING(string_base, LOCATE('|', string_base) + 1, LOCATE('|', string_base, 2) - LOCATE('|', string_base) - 1);
			ELSE
				SET string_base = SUBSTRING(string_base, LOCATE('|', string_base) + 1);
			END IF;
			IF string_base != '' THEN
				SET val = CAST(string_base AS DECIMAL(12, 6));
			END IF;
		END IF;
		SET i = i + 1;
	END WHILE;
	
	RETURN val;
	
END//

/*
	*	\brief Détermine si une chaîne de caractères est un numérique quelconque (entier ou décimal). Cette fonction n'a pas vocation à être publique
	*	\param v_val Obligatoire, non NULL, chaîne à tester
	*	\return Vrai si la chaîne est un numérique, faux sinon
*/
CREATE FUNCTION RIA_IS_DECIMAL(v_val VARCHAR(1024)) RETURNS TINYINT DETERMINISTIC
BEGIN
	SET v_val = TRIM(v_val);
	RETURN v_val REGEXP '^(-|\\+){0,1}([0-9]+\\.[0-9]*|[0-9]*\\.[0-9]+|[0-9]+)$';
END//

/*
	*	\brief Détermine si une chaîne de caractères est un entier positif. Cette fonction n'a pas vocation à être publique
	*	\param v_val Obligatoire, non NULL, chaîne à tester
	*	\return Vrai si la chaîne est un entier positif, faux sinon
*/
CREATE FUNCTION RIA_IS_UINT(v_val VARCHAR(1024)) RETURNS TINYINT DETERMINISTIC
BEGIN
	SET v_val = TRIM(v_val);
	RETURN v_val REGEXP '^[0-9]+$';
END//

/*
	*	\brief Détermine si une chaîne de caractères est la concaténation de deux numériques par un point-virgule. Cette fonction n'a pas vocation à être publique
	*	\param v_val Obligatoire, non NULL, chaîne à tester
	*	\return Vrai si la chaîne est une concaténation de deux numériques par un point-virgule, faux sinon
*/
CREATE FUNCTION RIA_IS_BORNED_DECIMAL(v_val VARCHAR(1024)) RETURNS TINYINT DETERMINISTIC
BEGIN
	SET v_val = TRIM(v_val);
	RETURN v_val REGEXP '^(-|\\+){0,1}([0-9]+\\.[0-9]*|[0-9]*\\.[0-9]+|[0-9]+);(-|\\+){0,1}([0-9]+\\.[0-9]*|[0-9]*\\.[0-9]+|[0-9]+)$';
END//

/*
	*	\brief Cette fonction détermine si une chaîne représente une date. Plusieurs formats sont acceptés (FR, EN, avec ou sans la partie TIME). Cette fonction n'a pas vocation à être publique
	*	\param v_val Obligatoire, non NULL, chaîne à tester
	*	\return Vrai si la chaîne représente une date valide, faux sinon
*/
CREATE FUNCTION RIA_IS_DATE(v_val VARCHAR(1024)) RETURNS TINYINT DETERMINISTIC
BEGIN
	SET v_val = TRIM(v_val);
	IF v_val REGEXP '^[0-9]{2}\/[0-9]{2}\/[0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}$' THEN
		RETURN 1;
	ELSEIF v_val REGEXP '^[0-9]{2}\/[0-9]{2}\/[0-9]{4}$' THEN
		RETURN 1;
	ELSEIF v_val REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' THEN
		RETURN 1;
	ELSEIF v_val REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2}' THEN
		RETURN 1;
	ELSE
		RETURN 0;
	END IF;
END//

/*
	*	\brief Crée une date au format YYYY-MM-DD HH:MM:SS à partir d'une chaîne. Cette fonction n'a pas vocation à être publique
	*	\param	v_val Obligatoire, non NULL, chaîne à convertir
	*	\return 0000-00-00 00:00:00 si la châine d'entrée est invalide. Dans le cas contraire, une chaîne castable en DATETIME
*/
CREATE FUNCTION RIA_PARSE_DATE(v_val VARCHAR(1024)) RETURNS CHAR(19) DETERMINISTIC
BEGIN
	SET v_val = TRIM(v_val);
	IF v_val REGEXP '^[0-9]{2}\/[0-9]{2}\/[0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}$' THEN
		RETURN CONCAT(SUBSTRING(v_val, 7, 4), '-', SUBSTRING(v_val, 4, 2), '-', SUBSTRING(v_val, 1, 2), ' ', SUBSTRING(v_val, 12, 2), ':', SUBSTRING(v_val, 15, 2), ':', SUBSTRING(v_val, 18, 2));
	ELSEIF v_val REGEXP '^[0-9]{2}\/[0-9]{2}\/[0-9]{4}$' THEN
		RETURN CONCAT(SUBSTRING(v_val, 7, 4), '-', SUBSTRING(v_val, 4, 2), '-', SUBSTRING(v_val, 1, 2), ' 00:00:00');
	ELSEIF v_val REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$' THEN
		RETURN v_val;
	ELSEIF v_val REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' THEN
		RETURN CONCAT(v_val, ' 00:00:00');
	END IF;

	RETURN '0000-00-00 00:00:00';
END//

/*
	*	\brief Cette procédure est chargée de convertir une chaine composée de deux dates séparées par un ";" en deux dates. Cette procédure n'a pas vocation à être publique
	*	\param	v_val Obligatoire, en entrée, non NULL, valeur à parser
	*	\param	is_ok Obligatoire, en sortie, détermine si la chaîne d'entrée était valide pour parser les élements en dates
	*	\param	val1 Facultatif, en sortie, valeur de la première date si "is_ok" est vrai
	*	\param	val2 Facultatif, en sortie, valeur de la deuxième date si "is_ok" est vrai
*/
CREATE PROCEDURE RIA_GET_BORNED_DATE(IN v_val VARCHAR(1024), OUT is_ok TINYINT, OUT val1 CHAR(19), OUT val2 CHAR(19)) DETERMINISTIC
BEGIN
	SET v_val = TRIM(v_val);
	SET is_ok = 0;
	SET val1 = NULL;
	SET val2 = NULL;
	
	IF
		v_val REGEXP '^[0-9]{2}\/[0-9]{2}\/[0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2};[0-9]{2}\/[0-9]{2}\/[0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}$'
		OR v_val REGEXP '^[0-9]{2}\/[0-9]{2}\/[0-9]{4};[0-9]{2}\/[0-9]{2}\/[0-9]{4}$'
		OR v_val REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2};[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$'
		OR v_val REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2};[0-9]{4}-[0-9]{2}-[0-9]{2}$'
	THEN
		SET is_ok = 1;
		SET val1 = RIA_PARSE_DATE(SUBSTRING(v_val, 1, LOCATE(';',v_val)));
		SET val2 = RIA_PARSE_DATE(SUBSTRING(v_val, LOCATE(';',v_val) + 1));
	END IF;
	
END//

/*
	*	\brief Détermine, pour une condition tarifaire sur un champ physique, si elle est respectée ddans un contexte donné. Cette fonction n'a pas vocation à être publique
	*	\param tenant Non NULL, identifiant du locataire
	*	\param cndSymbol Non NULL, symbole de comparaison
	*	\param fldPhysicalName Non NULL, nom physique du champ
	*	\param value1 Non NULL, ID principal du contexte (ID client, ID produit, etc...)
	*	\param value2 Non NULL, ID secondaire du contexte (classe à clé composée)
	*	\param cndValue Non NULL, valeur de comparaison
	*	\param usr_prc_id Non NULL, identifiant de la catégorie tarifaire du client, ou celle par défaut.
	*	\param usr_ref Non NULL, code client.
	*	\param brd_id Null autorisé, marque du produit (-1 pour indiquer que l'information n'a pas été précalculée).
	*	\param centralized Null autorisé, produit centralisé oui/non (Null pour indiquer que l'information n'a pas été précalculée).
	*
	*	\return Vrai si la condition tarifaire est respectée, faux dans les autres cas
*/
CREATE FUNCTION RIA_CHECK_PHYSIC_CONDITION(tenant INT, cndSymbol VARCHAR(10), fldPhysicalName VARCHAR(75), value1 INT, value2 INT, cndValue VARCHAR(75), usr_prc_id INT, usr_ref VARCHAR(17), brd_id INT, centralized TINYINT) RETURNS TINYINT NOT DETERMINISTIC READS SQL DATA
BEGIN

	DECLARE is_ok, fct_result TINYINT;
	DECLARE date1, date2 CHAR(19);
	
	SET fct_result = 0;
	
	-- switch suivant le champ (impossible de faire des requêtes dynamiques)
	-- pour un nouveau champ, l'ajouter dans un ELSEIF (ex : "usr_cac_id" à venir)
	
	IF fldPhysicalName = 'usr_ref' THEN
		-- code client
		-- symboles : "=" et "!=", "LIKE" et "NOT LIKE"
		-- il est possible de rechercher sur une partie du code
		IF cndSymbol = 'LIKE' THEN
			IF usr_ref LIKE CONCAT('%', REPLACE(cndValue, '%', '\%'), '%') THEN
				SET fct_result = 1;
			END IF;
		ELSEIF cndSymbol = 'NOT LIKE' THEN
			IF usr_ref NOT LIKE CONCAT('%', REPLACE(cndValue, '%', '\%'), '%') THEN
				SET fct_result = 1;
			END IF;
		ELSEIF cndSymbol = '=' THEN
			IF usr_ref = cndValue THEN
				SET fct_result = 1;
			END IF;
		ELSEIF cndSymbol = '!=' THEN
			IF usr_ref != cndValue THEN
				SET fct_result = 1;
			END IF;
		END IF;
	ELSEIF fldPhysicalName = 'usr_prc_id' THEN
		-- catégorie tarifaire du client
		-- cndValue contient l'ID de la actégorie (nombre entier)
		-- symboles : "=" et "!="
		IF RIA_IS_UINT(cndValue) THEN
			IF cndSymbol = '=' THEN
				IF usr_prc_id = CAST(cndValue AS UNSIGNED) THEN
					SET fct_result = 1;
				END IF;
			ELSEIF cndSymbol = '!=' THEN
				IF usr_prc_id != CAST(cndValue AS UNSIGNED) THEN
					SET fct_result = 1;
				END IF;
			END IF;
		END IF;
	ELSEIF fldPhysicalName = 'usr_cac_id' THEN
		IF RIA_IS_UINT(cndValue) THEN
			IF cndSymbol = '=' THEN
				SELECT IFNULL(usr_cac_id, 0) = CAST(cndValue AS UNSIGNED) INTO fct_result
				FROM gu_users
				WHERE (usr_tnt_id=tenant OR usr_tnt_id=0) AND usr_id=value1 AND usr_date_deleted IS NULL;
			ELSEIF cndSymbol = '!=' THEN
				SELECT IFNULL(usr_cac_id, 0) != CAST(cndValue AS UNSIGNED) INTO fct_result
				FROM gu_users
				WHERE (usr_tnt_id=tenant OR usr_tnt_id=0) AND usr_id=value1 AND usr_date_deleted IS NULL;
			END IF;
		END IF;
	ELSEIF fldPhysicalName = 'prd_brd_id' THEN
		-- marque du produit
		-- cndValue contient l'ID de la marque (nombre entier)
		-- 0 équivaut à NULL
		-- symboles : "=" et "!="
		IF RIA_IS_UINT(cndValue) THEN
			IF CAST(cndValue AS UNSIGNED) = 0 THEN
				IF cndSymbol = '=' THEN
					IF brd_id = -1 THEN
						SELECT prd_brd_id IS NULL INTO fct_result
						FROM prd_products
						WHERE prd_tnt_id=tenant AND prd_id=value1;
					ELSE
						SET fct_result = IF(brd_id IS NULL, 1, 0);
					END IF;
				ELSEIF cndSymbol = '!=' THEN
					IF brd_id = -1 THEN
						SELECT prd_brd_id IS NOT NULL INTO fct_result
						FROM prd_products
						WHERE prd_tnt_id=tenant AND prd_id=value1;
					ELSE
						SET fct_result = IF(brd_id IS NOT NULL, 1, 0);
					END IF;
				END IF;
			ELSE
				IF cndSymbol = '=' THEN
					IF brd_id = -1 THEN
						SELECT prd_brd_id = CAST(cndValue AS UNSIGNED) INTO fct_result
						FROM prd_products
						WHERE prd_tnt_id=tenant AND prd_id=value1;
					ELSE
						SET fct_result = IF(CAST(cndValue AS UNSIGNED) = brd_id, 1, 0);
					END IF;
				ELSEIF cndSymbol = '!=' THEN
					IF brd_id = -1 THEN
						SELECT prd_brd_id != CAST(cndValue AS UNSIGNED) INTO fct_result
						FROM prd_products
						WHERE prd_tnt_id=tenant AND prd_id=value1;
					ELSE
						SET fct_result = IF(CAST(cndValue AS UNSIGNED) != brd_id, 1, 0);
					END IF;
				END IF;
			END IF;
		END IF;
	ELSEIF fldPhysicalName = 'cly_col_id' THEN
		-- table associative produit / conditionnement (tarifs futur extranet sodip)
		-- symboles : "=" et "!="
		IF RIA_IS_UINT(cndValue) THEN
			IF CAST(cndValue AS UNSIGNED) = 0 THEN
				IF cndSymbol = '=' THEN
					SELECT COUNT(*) = 0 INTO fct_result
					FROM prd_colisage_classify
					WHERE cly_tnt_id=tenant AND cly_col_id=value2 AND cly_prd_id=value1;
				ELSEIF cndSymbol = '!=' THEN
					SELECT COUNT(*) > 0 INTO fct_result
					FROM prd_colisage_classify
					WHERE cly_tnt_id=tenant AND cly_col_id=value2 AND cly_prd_id=value1;
				END IF;
			ELSE
				IF cndSymbol = '=' THEN
					SELECT COUNT(*) > 0 INTO fct_result
					FROM prd_colisage_classify
					WHERE cly_tnt_id=tenant AND cly_col_id=value2 AND cly_col_id=CAST(cndValue AS UNSIGNED) AND cly_prd_id=value1;
				ELSEIF cndSymbol = '!=' THEN
					SELECT COUNT(*) = 0 INTO fct_result
					FROM prd_colisage_classify
					WHERE cly_tnt_id=tenant AND cly_col_id=value2 AND cly_col_id=CAST(cndValue AS UNSIGNED) AND cly_prd_id=value1;
				END IF;
			END IF;
		END IF;
	ELSEIF fldPhysicalName = 'prd_centralized' THEN
		-- produit centralisé oui/non (spécialité Bigship)
		-- symboles : "=" et "!="
		IF LOWER(cndValue) = 'oui' THEN
			SET cndValue = '1';
		ELSE
			SET cndValue = '0';
		END IF;
		IF cndSymbol = '=' THEN
			IF centralized IS NULL THEN
				SELECT prd_centralized = CAST(cndValue AS UNSIGNED) INTO fct_result
				FROM prd_products
				WHERE prd_tnt_id=tenant AND prd_id=value1;
			ELSE
				SET fct_result = IF(CAST(cndValue AS UNSIGNED) = centralized, 1, 0);
			END IF;
		ELSEIF cndSymbol = '!=' THEN
			IF centralized IS NULL THEN
				SELECT prd_centralized != CAST(cndValue AS UNSIGNED) INTO fct_result
				FROM prd_products
				WHERE prd_tnt_id=tenant AND prd_id=value1;
			ELSE
				SET fct_result = IF(CAST(cndValue AS UNSIGNED) != centralized, 1, 0);
			END IF;
		END IF;
	END IF;

	RETURN fct_result;
	
END//

/*
	*	\brief Détermine, pour une condition tarifaire sur un champ virtuel, si elle est respectée ddans un contexte donné. Cette fonction n'a pas vocation à être publique
	*	\param	fldId Obligatoire, non NULL, identifiant du champ avancé
	*	\param	tenant Obligatoire, non NULL, identifiant du locataire
	*	\param	cndSymbol Obligatoire, non NULL, symbole de comparaison
	*	\param	fldType Obligatoire, non NULL, type de données du champ
	*	\param	value1 Obligatoire, non NULL, ID principal du contexte (ID client, ID produit, etc...)
	*	\param	value2 Obligatoire, non NULL, ID secondaire du contexte (classe à clé composée)
	*	\param	cndValue Obligatoire, non NULL, valeur de comparaison
	*	\return Vrai si la condition tarifaire est respectée, faux dans les autres cas
*/
CREATE FUNCTION RIA_CHECK_VIRTUAL_CONDITION(fldType INT, cndSymbol VARCHAR(10), cndValue VARCHAR(75), tenant INT, fldId INT, value1 INT, value2 INT, lng_code VARCHAR(5)) RETURNS TINYINT NOT DETERMINISTIC READS SQL DATA
BEGIN

	DECLARE done INT DEFAULT 0;
	DECLARE is_ok, fct_result TINYINT DEFAULT 0;
	DECLARE ids_lst VARCHAR(1024);
	DECLARE date1, date2 CHAR(19);
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	
	-- Là encore, il n'est pas possible d'utiliser du SQL dynamique. Un switch suivant le symbole et le type des données est donc obligatoire (mais, contrairement aux champs physique, le switch est désormais fixe sauf ajout d'un nouveau type)
	
	-- types numériques (entier, décimal, pointeur)
	IF fldType = 3 OR fldType = 4 OR fldType = 11 THEN
		-- "est compris entre"
		IF cndSymbol = '><' THEN
			-- la valeur doit être correctement castée
			IF RIA_IS_BORNED_DECIMAL(cndValue) THEN
				SELECT CAST(pv_value AS DECIMAL) >= CAST(SUBSTRING(cndValue, 1, LOCATE(';',cndValue)) AS DECIMAL)
				AND CAST(pv_value AS DECIMAL) <= CAST(SUBSTRING(cndValue, LOCATE(';',cndValue) + 1) AS DECIMAL) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			END IF;
		ELSEIF RIA_IS_DECIMAL(cndValue) THEN -- les numériques sont toujours castés en décimaux
			IF cndSymbol = '=' THEN
				SELECT CAST(pv_value AS DECIMAL) = CAST(cndValue AS DECIMAL) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '!=' THEN
				SELECT CAST(pv_value AS DECIMAL) != CAST(cndValue AS DECIMAL) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '<' THEN
				SELECT CAST(pv_value AS DECIMAL) < CAST(cndValue AS DECIMAL) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '>' THEN
				SELECT CAST(pv_value AS DECIMAL) > CAST(cndValue AS DECIMAL) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '<=' THEN
				SELECT CAST(pv_value AS DECIMAL) <= CAST(cndValue AS DECIMAL) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '>=' THEN
				SELECT CAST(pv_value AS DECIMAL) >= CAST(cndValue AS DECIMAL) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			END IF;
		END IF;
	ELSEIF fldType = 12 THEN
		-- liste de choix hiérarchique (la valeur en entrée est un ID de l'arbre hiérarchique)
		IF RIA_IS_UINT(cndValue) THEN
			-- recherche sur 5 degrés de profondeur hiérarchique (le maximum existant est 3, et c'est pour un champ non utilisé pour les tarifs. Pour un champ tarifé, le max. est de 2)
			SELECT CAST(pv_value AS UNSIGNED) IN (
				SELECT CAST(cndValue AS UNSIGNED)
				UNION ALL
				SELECT val_id FROM fld_restricted_values
				WHERE val_parent_id=CAST(cndValue AS UNSIGNED) AND val_tnt_id=tenant
				UNION ALL
				SELECT v1.val_id FROM fld_restricted_values AS v0
				JOIN fld_restricted_values AS v1 ON (v0.val_id=v1.val_parent_id AND v0.val_tnt_id=v1.val_tnt_id)
				WHERE v0.val_parent_id=CAST(cndValue AS UNSIGNED) AND v0.val_tnt_id=tenant
				UNION ALL
				SELECT v2.val_id FROM fld_restricted_values AS v0
				JOIN fld_restricted_values AS v1 ON (v0.val_id=v1.val_parent_id AND v0.val_tnt_id=v1.val_tnt_id)
				JOIN fld_restricted_values AS v2 ON (v1.val_id=v2.val_parent_id AND v1.val_tnt_id=v2.val_tnt_id)
				WHERE v0.val_parent_id=CAST(cndValue AS UNSIGNED) AND v0.val_tnt_id=tenant
				UNION ALL
				SELECT v3.val_id FROM fld_restricted_values AS v0
				JOIN fld_restricted_values AS v1 ON (v0.val_id=v1.val_parent_id AND v0.val_tnt_id=v1.val_tnt_id)
				JOIN fld_restricted_values AS v2 ON (v1.val_id=v2.val_parent_id AND v1.val_tnt_id=v2.val_tnt_id)
				JOIN fld_restricted_values AS v3 ON (v2.val_id=v3.val_parent_id AND v2.val_tnt_id=v3.val_tnt_id)
				WHERE v0.val_parent_id=CAST(cndValue AS UNSIGNED) AND v0.val_tnt_id=tenant
			) INTO fct_result
			FROM fld_object_values
			WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
			AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
		END IF;
	ELSEIF fldType = 1 OR fldType = 2 OR fldType = 6 OR fldType = 5 THEN
		-- types texte et listes
		-- il y a sensibilité à la casse
		IF cndSymbol = '=' THEN
			SELECT pv_value = cndValue INTO fct_result 
			FROM fld_object_values
			WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
			AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
		ELSEIF cndSymbol = '!=' THEN
			SELECT pv_value != cndValue INTO fct_result 
			FROM fld_object_values
			WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
			AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
		ELSEIF cndSymbol = 'LIKE' THEN
			SELECT pv_value LIKE CONCAT('%', REPLACE(cndValue, '%', '\%'), '%') INTO fct_result 
			FROM fld_object_values
			WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
			AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
		ELSEIF cndSymbol = 'NOT LIKE' THEN
			SELECT pv_value NOT LIKE CONCAT('%', REPLACE(cndValue, '%', '\%'), '%') INTO fct_result 
			FROM fld_object_values
			WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
			AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
		END IF;
	ELSEIF fldType = 8 THEN
		-- Type booléen, les valeurs sont restreintes (0, 1, "Oui", "Non", chaine vide pour non)
		IF LOWER(cndValue) = 'non' OR cndValue = '0' OR cndValue = '' THEN
			SET cndValue = '0';
			IF cndSymbol = '=' THEN
				SELECT ( LOWER(pv_value) = 'non' OR LOWER(pv_value) = '0' ) INTO fct_result 
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '!=' THEN
				SELECT ( LOWER(pv_value) != 'non' AND LOWER(pv_value) != '0' ) INTO fct_result 
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			END IF;
		ELSEIF LOWER(cndValue) = 'oui' OR cndValue = '1' THEN
			SET cndValue = '1';
			IF cndSymbol = '=' THEN
				SELECT ( LOWER(pv_value) = 'oui' OR LOWER(pv_value) = '1' ) INTO fct_result 
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '!=' THEN
				SELECT ( LOWER(pv_value) != 'oui' AND LOWER(pv_value) != '1' ) INTO fct_result 
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			END IF;
		END IF;
		-- ligne inexistante (typiquement, le champ frais de port)
		IF done THEN
			IF ( cndValue = '1' AND cndSymbol = '!=' ) OR ( cndValue = '0' AND cndSymbol = '=' ) THEN
				SET fct_result = 1;
			END IF;
		END IF;
	ELSEIF fldType = 10 THEN
		-- type date (le plus complexe car la valeur est polymorphe)
		IF RIA_IS_UINT(cndValue) THEN
			-- la valeur est un ID, représentant une certaine forme de date
			IF CAST(cndValue as UNSIGNED)=1 THEN
				-- ID pour le "mois et jour" courant (plus communément date anniversaire, par exemple)
				IF cndSymbol = '=' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) = MONTH(NOW()) AND DAY(CAST(pv_value AS DATETIME)) = DAY(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '!=' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) != MONTH(NOW()) AND DAY(CAST(pv_value AS DATETIME)) != DAY(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '<' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) < MONTH(NOW()) AND DAY(CAST(pv_value AS DATETIME)) < DAY(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '>' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) > MONTH(NOW()) AND DAY(CAST(pv_value AS DATETIME)) > DAY(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '<=' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) <= MONTH(NOW()) AND DAY(CAST(pv_value AS DATETIME)) <= DAY(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '>=' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) >= MONTH(NOW()) AND DAY(CAST(pv_value AS DATETIME)) >= DAY(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				END IF;
			ELSEIF CAST(cndValue as UNSIGNED)=2 THEN
				-- ID pour la semaine courante
				IF cndSymbol = '=' THEN
					SELECT WEEK(CAST(pv_value AS DATETIME)) = WEEK(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '!=' THEN
					SELECT WEEK(CAST(pv_value AS DATETIME)) != WEEK(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '<' THEN
					SELECT WEEK(CAST(pv_value AS DATETIME)) < WEEK(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '>' THEN
					SELECT WEEK(CAST(pv_value AS DATETIME)) > WEEK(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '<=' THEN
					SELECT WEEK(CAST(pv_value AS DATETIME)) <= WEEK(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '>=' THEN
					SELECT WEEK(CAST(pv_value AS DATETIME)) >= WEEK(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				END IF;
			ELSEIF CAST(cndValue as UNSIGNED)=3 THEN
				-- ID pour le mois courant
				IF cndSymbol = '=' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) = MONTH(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '!=' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) != MONTH(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '<' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) < MONTH(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '>' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) > MONTH(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '<=' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) <= MONTH(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				ELSEIF cndSymbol = '>=' THEN
					SELECT MONTH(CAST(pv_value AS DATETIME)) >= MONTH(NOW()) INTO fct_result
					FROM fld_object_values
					WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
					AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
				END IF;
			END IF;
		ELSEIF RIA_IS_DATE(cndValue) THEN
			-- la valeur est une date (comparaison standard)
			IF cndSymbol = '=' THEN
				SELECT CAST(pv_value AS datetime) = RIA_PARSE_DATE(cndValue) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '!=' THEN
				SELECT CAST(pv_value AS datetime) != RIA_PARSE_DATE(cndValue) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '<' THEN
				SELECT CAST(pv_value AS datetime) < RIA_PARSE_DATE(cndValue) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '>' THEN
				SELECT CAST(pv_value AS datetime) > RIA_PARSE_DATE(cndValue) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '<=' THEN
				SELECT CAST(pv_value AS datetime) <= RIA_PARSE_DATE(cndValue) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			ELSEIF cndSymbol = '>=' THEN
				SELECT CAST(pv_value AS datetime) >= RIA_PARSE_DATE(cndValue) INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			END IF;
		ELSE
			-- la valeur est une période (deux dates : début et fin)
			CALL RIA_GET_BORNED_DATE(cndValue, is_ok, date1, date2);
			IF is_ok THEN
				SELECT CAST(pv_value AS datetime) >= date1 AND CAST(pv_value AS datetime) <= date2  INTO fct_result
				FROM fld_object_values
				WHERE pv_lng_code=lng_code AND pv_tnt_id=tenant AND pv_fld_id=fldId
				AND pv_obj_id_0=value1 AND pv_obj_id_1=value2;
			END IF;
		END IF;
	END IF;

	RETURN fct_result;
	
END//

/*
	*	\brief Détermine si une condition tarifaire est corréllée ou non avec le contexte appelant. Cette fonction n'a pas vocation à être publique
	*	\param fldId Non NULL, identifiant du champ avancé de la condition tarifaire
	*	\param fldType Non NULL, identifiant du type de champ avancé
	*	\param cndValue Non NULL, valeur de la condition tarifaire, à laquelle le contexte devra correspondre
	*	\param cndSymbol Non NULL, détermine le symbole de la condition tarifaire
	*	\param fldIsPhysical Non NULL, détermine si le champ avancé est virtuel (faux) ou est un pointeur sur un champ physique (vrai)
	*	\param fldPhysicalName Non NULL, détermine le nom physique du champ si fldIsPhysical est vrai
	*	\param value1 Non NULL, identifiant du contexte actuellement testé (iexemples : ID client, ID commande, ID produit, etc...)
	*	\param tenant Non NULL, identifiant du locataire
	*	\param value2 Non NULL, identifiant secondaire du contexte (voir "value1" pour plus d'informations) dans le cas où la classe "cls" spécifiée est composée d'une clé primaire double (dans le cas contraire, 0 doit être spcifié). Note : il n'existe pas, à l'heure actuelle, de "valueX" pour une clé composée d'un nombre X de membres
	*	\param usr_prc_id Non NULL, identifiant de la catégorie tarifaire de l'utilisateur ou celle par défaut.
	*	\param usr_ref Non NULL, code client.
	*	\param brd_id Null autorisé, marque du produit (-1 si l'information n'a pas été précalculée).
	*	\param centralized Null autorisé, produit centralisé oui/non (Null si l'information n'a pas été précalculée).
	*
	*	\return Vrai si la condition tarifiare est valide pour le contexte, faux sinon
*/
CREATE FUNCTION RIA_PRC_CONDITIONS_TEST_VALUE( fldId INT, fldType INT, cndValue VARCHAR(75), cndSymbol VARCHAR(10), fldIsPhysical TINYINT, fldPhysicalName VARCHAR(75), value1 INT, tenant INT, value2 INT, usr_prc_id INT, lng_code VARCHAR(5), usr_ref VARCHAR(17), brd_id INT, centralized TINYINT ) RETURNS TINYINT NOT DETERMINISTIC READS SQL DATA
BEGIN

	DECLARE fct_result TINYINT DEFAULT 1;

	-- il est tout à fait possible de comparer la condition tarifaire à... rien (exemple : on ne connait pas le usr_id, et il y a une condition sur celui-ci)
	IF value1 = 0 THEN
		IF fldType = 10 THEN
			-- type date, tous les symboles échouent sauf "différent de" qui passe systématiquement
			IF cndSymbol != '!=' THEN
				SET fct_result = 0;
			END IF;
		ELSEIF fldType = 1 OR fldType = 2 OR fldType = 6 OR fldType = 5 OR fldType = 12 THEN
			-- type texte ou liste de choix
			IF cndSymbol = '=' OR cndSymbol = 'LIKE' THEN
				-- égal à chaine vide
				IF cndValue != '' THEN
					SET fct_result = 0;
				END IF;
			ELSEIF cndSymbol = '!=' OR cndSymbol = 'NOT LIKE' THEN
				-- non égal à du texte
				IF cndValue = '' THEN
					SET fct_result = 0;
				END IF;
			ELSE
				-- les autres symboles échouent
				SET fct_result = 0;
			END IF;
		ELSEIF fldType = 3 OR fldType = 4 OR fldType = 11 THEN
			-- types numériques
			IF fldPhysicalName = 'usr_prc_id' THEN
				-- gestion spéciale du champ "Catégorie tarifaire du client"
				IF cndSymbol = '=' THEN
					-- égal à valeur par défaut
					IF NOT RIA_IS_UINT(cndValue) THEN
						SET fct_result = 0;
					ELSEIF CAST(cndValue AS UNSIGNED) != usr_prc_id THEN
						SET fct_result = 0;
					END IF;
				ELSEIF cndSymbol = '!=' THEN
					-- différent de valeur par défaut
					IF RIA_IS_UINT(cndValue) THEN
						IF CAST(cndValue AS UNSIGNED) = usr_prc_id THEN
							SET fct_result = 0;
						END IF;
					END IF;
				ELSE
					-- les autres symboles échouent
					SET fct_result = 0;
				END IF;
			ELSEIF fldPhysicalName = 'usr_cac_id' THEN
				-- gestion spéciale du champ "Catégorie comptable du client"
				IF cndSymbol = '=' THEN
					-- égal à valeur par défaut
					IF NOT RIA_IS_UINT(cndValue) THEN
						SET fct_result = 0;
					ELSEIF CAST(cndValue AS UNSIGNED) != 0 THEN
						SET fct_result = 0;
					END IF;
				ELSEIF cndSymbol = '!=' THEN
					-- différent de valeur par défaut
					IF RIA_IS_UINT(cndValue) THEN
						IF CAST(cndValue AS UNSIGNED) = 0 THEN
							SET fct_result = 0;
						END IF;
					END IF;
				ELSE
					-- les autres symboles échouent
					SET fct_result = 0;
				END IF;
			ELSEIF fldPhysicalName = 'prd_brd_id' THEN
				-- gestion spéciale du champ "Marque du produit"
				IF cndSymbol = '=' THEN
					-- égal à valeur par défaut
					IF NOT RIA_IS_UINT(cndValue) THEN
						SET fct_result = 0;
					ELSEIF CAST(cndValue AS UNSIGNED) != 0 THEN
						SET fct_result = 0;
					END IF;
				ELSEIF cndSymbol != '!=' THEN
					-- différent de valeur par défaut
					IF RIA_IS_UINT(cndValue) THEN
						IF CAST(cndValue AS UNSIGNED) = 0 THEN
							SET fct_result = 0;
						END IF;
					END IF;
				ELSE
					-- les autres symboles échouent
					SET fct_result = 0;
				END IF;
			ELSEIF fldType = 11 THEN
				-- les autres cas de pointeurs seront à traiter individuellement
				SET fct_result = 0;
			ELSEIF fldType = 3 THEN
				-- Type entier
				IF cndSymbol = '=' THEN
					-- égal à 0
					IF NOT RIA_IS_UINT(cndValue) THEN
						SET fct_result = 0;
					ELSEIF CAST(cndValue AS UNSIGNED) != 0 THEN
						SET fct_result = 0;
					END IF;
				ELSEIF cndSymbol != '!=' THEN
					-- différent de 0
					IF RIA_IS_UINT(cndValue) THEN
						IF CAST(cndValue AS UNSIGNED) = 0 THEN
							SET fct_result = 0;
						END IF;
					END IF;
				ELSE
					-- les autres symboles échouent
					SET fct_result = 0;
				END IF;
			ELSE
				-- Type décimal
				IF cndSymbol = '=' THEN
					-- égal à 0
					IF NOT RIA_IS_DECIMAL(cndValue) THEN
						SET fct_result = 0;
					ELSEIF CAST(cndValue as DECIMAL(24, 8)) != 0 THEN
						SET fct_result = 0;
					END IF;
				ELSEIF cndSymbol != '!=' THEN
					-- différent de 0
					IF RIA_IS_DECIMAL(cndValue) THEN
						IF CAST(cndValue as DECIMAL(24, 8)) = 0 THEN
							SET fct_result = 0;
						END IF;
					END IF;
				ELSE
					-- les autres symboles échouent
					SET fct_result = 0;
				END IF;
			END IF;
		ELSEIF fldType = 8 AND NOT fldIsPhysical THEN
			IF LOWER(cndValue) = 'non' OR cndValue = '0' OR cndValue = '' THEN
				SET cndValue = '0';
			ELSEIF LOWER(cndValue) = 'oui' OR cndValue = '1' THEN
				SET cndValue = '1';
			END IF;
			IF ( cndValue = '1' AND cndSymbol = '!=' ) OR ( cndValue = '0' AND cndSymbol = '=' ) THEN
				SET fct_result = 1;
			ELSE
				SET fct_result = 0;
			END IF;
		ELSE
			-- les autres types échouent [image, ...]
			SET fct_result = 0;
		END IF;
	ELSE
		-- la gestion diffère suivant qu'il s'agisse d'un champ avancé ou d'un pointeur vers un champ physique
		IF fldIsPhysical THEN
			SET fct_result = RIA_CHECK_PHYSIC_CONDITION(tenant, cndSymbol, fldPhysicalName, value1, value2, cndValue, usr_prc_id, usr_ref, brd_id, centralized);
		ELSE
			SET fct_result = RIA_CHECK_VIRTUAL_CONDITION(fldType, cndSymbol, cndValue, tenant, fldId, value1, value2, lng_code);
		END IF;
	END IF;

	RETURN fct_result;

END//

/*
	*	\brief Détermine si un tarif s'applique à un contexte donné. Cette fonction n'a pas vocation à être publique
	*	\param force_check_price Non NULL, détermine si l'intégrité du paramètre "prId" doit être vérifié
	*	\param prId Non NULL, identifiant du tarif à tester
	*	\param tenant Non NULL, identifiant du locataire
	*	\param prd Non NULL, identifiant du produit
	*	\param usr Non NULL, identifiant du client
	*	\param col Non NULL, identifiant du conditionnement
	*	\param usr_prc_id Non NULL, identifiant de la catégorie tarifaire du client ou celle par défaut.
	*	\param usr_ref Non NULL, code client.
	*	\param brd_id Null autorisé, marque du produit (-1 si l'information n'a pas été précalculée).
	*	\param centralized Null autorisé, produit centralisé oui/non (Null si l'information n'a pas été précalculée).
	*
	*	\return Vrai si le tarif s'applique dans le contexte, faux sinon
*/
CREATE FUNCTION RIA_IS_APPLY( force_check_price TINYINT, prId INT, tenant INT, prd INT, usr INT, col INT, usr_prc_id INT, lng_code VARCHAR(5), usr_ref VARCHAR(17), brd_id INT, centralized TINYINT ) RETURNS TINYINT NOT DETERMINISTIC READS SQL DATA
BEGIN

	DECLARE fct_result, v_physical, v_tmp_val TINYINT DEFAULT 1;
	DECLARE v_value, v_phy_name, list_ids_block VARCHAR(75);
	DECLARE v_symbol VARCHAR(10);
	DECLARE id_checker, v_id, v_type, done, v_class_id, done2 INT DEFAULT 0;

	-- on récupère toutes les conditions tarifaires pour le tarif en argument
	-- actuellement seules les classe 2 (user), 1 (produit) et 9 (conditionnement) sont utilisées
	DECLARE conditions_cursor CURSOR FOR
	SELECT fld_type_id, ppc_value, ppc_symbol, fld_is_physical, IFNULL(fld_physical_name, ''), fld_id, fld_cls_id
	FROM prc_price_conditions JOIN fld_fields ON ( ppc_fld_id=fld_id AND (ppc_tnt_id=fld_tnt_id OR fld_tnt_id=0) )
	WHERE ppc_prc_id=prId AND ppc_tnt_id=tenant AND fld_date_deleted IS NULL AND fld_cls_id in (2, 1, 9);

	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	
	-- Si activé, force un contrôle de la validité de l'identifiant de tarif
	IF force_check_price THEN
		SELECT COUNT(*) INTO id_checker
		FROM prc_prices
		WHERE prc_is_deleted=0
		AND prc_id=prId AND prc_tnt_id=tenant;

		IF id_checker = 0 THEN
			RETURN 0;
		END IF;
	END IF;

	-- la boucle part du principe que le résultat sera True et recherche une condition non respectée
	OPEN conditions_cursor;
	REPEAT
		FETCH conditions_cursor INTO v_type, v_value, v_symbol, v_physical, v_phy_name, v_id, v_class_id;
		IF NOT done THEN
			-- les arguments passées à RIA_PRC_CONDITIONS_TEST_VALUE diffèrent en fonction de chaque classe (identifiant du produit si 1, identifiant du client si 2, identifiant du produit et du conditionnement si 9)
			-- test spécifique pour le champ avancé des catégories tarifaires sans remises
			IF v_id = 3027 THEN
				BEGIN
					-- déclaration d'un deuxième handler pour éviter d'activer le premier
					DECLARE CONTINUE HANDLER FOR NOT FOUND SET done2 = 1;
					
					-- récupère la valeur du champ avancé "Catégories hors-remises" du produit
					SELECT pv_value INTO list_ids_block
					FROM fld_object_values
					WHERE pv_tnt_id = tenant AND pv_fld_id = v_id
					AND pv_lng_code = lng_code AND pv_obj_id_0 = prd
					AND pv_obj_id_1 = 0 AND pv_obj_id_2 = 0;
					
					IF list_ids_block IS NULL THEN
						SET list_ids_block = '';
					END IF;
					
					-- la liste des catégories bloquées contient la catégorie du contexte
					IF
						list_ids_block = TRIM(CAST(usr_prc_id AS CHAR(75)))
						OR list_ids_block LIKE CONCAT('%, ', TRIM(CAST(usr_prc_id AS CHAR(75))))
						OR list_ids_block LIKE CONCAT(TRIM(CAST(usr_prc_id AS CHAR(75))), ',%')
						OR list_ids_block LIKE CONCAT('%, ', TRIM(CAST(usr_prc_id AS CHAR(75))), ',%')
					THEN
						SET v_tmp_val = 0;
					END IF;
				END;
			ELSEIF v_class_id = 2 THEN
				SET v_tmp_val = RIA_PRC_CONDITIONS_TEST_VALUE( v_id, v_type, v_value, v_symbol, v_physical, v_phy_name, usr, tenant, 0, usr_prc_id, lng_code, usr_ref, brd_id, centralized );
			ELSEIF v_class_id = 1 THEN
				SET v_tmp_val = RIA_PRC_CONDITIONS_TEST_VALUE( v_id, v_type, v_value, v_symbol, v_physical, v_phy_name, prd, tenant, 0, usr_prc_id, lng_code, usr_ref, brd_id, centralized );
			ELSEIF v_class_id = 9 THEN
				SET v_tmp_val = RIA_PRC_CONDITIONS_TEST_VALUE( v_id, v_type, v_value, v_symbol, v_physical, v_phy_name, prd, tenant, col, usr_prc_id, lng_code, usr_ref, brd_id, centralized );
			ELSE
				SET v_tmp_val = 0;
			END IF;
			-- condition non respectée
			IF NOT v_tmp_val THEN
				SET fct_result = 0;
			END IF;
		END IF;
	UNTIL done
	END REPEAT;
	CLOSE conditions_cursor;

	RETURN fct_result;

END//

/*
	*	\brief Détermine, pour le tarif et le champ avancé spécifié, la valeur de la condition tarifaire. Cette fonction n'a pas vocation a être publique. 
	*	\param prcId Obligatoire, non NULL, identifiant du tarif
	*	\param fldId Obligatoire, non NULL, identifiant du champ avancé
	*	\param tenant Obligatoire, non NULL, identifiant du locataire
	*	\return La valeur de la condition pour le tarif spécifié (NULL si non trouvé)
*/
CREATE FUNCTION RIA_PRICE_CONDITION_GET( prcId INT, fldId INT, tenant INT ) RETURNS VARCHAR(75) NOT DETERMINISTIC READS SQL DATA
BEGIN

	DECLARE my_val VARCHAR(75);

	DECLARE CONTINUE HANDLER FOR NOT FOUND SET my_val = NULL;
	
	SELECT ppc_value INTO my_val
	FROM prc_price_conditions
	WHERE ppc_prc_id=prcId AND ppc_fld_id=fldId AND ppc_tnt_id=tenant;
	
	RETURN my_val;

END//

/*
	*	\brief Détermine, entre deux valeurs de restriction de type "12 - hiérarchique", quelle est la plus profonde. Cette fontion n'a pas vocation a être publique
	*	\param	tenant Obligatoire, non NULL, identifiant du locataire
	*	\param	val1 Obligatoire, non NULL, identifiant de la première valeur de restriction
	*	\param	val2 Obligatoire, non NULL, identifiant de la deuxième valeur de restriction
	*	\return L'identifiant de la valeur de restriction dont la hiérarchie est la plus profonde, ou 0 en cas d'égalité ou d'erreur(s)
*/
CREATE FUNCTION RIA_GET_VAL_ID_DEPTH_MAX( tenant INT, val1 INT, val2 INT ) RETURNS INT NOT DETERMINISTIC READS SQL DATA
BEGIN

	DECLARE count1, count2, valFather, valtemp INT DEFAULT 0;
	DECLARE MAX_DEPTH INT DEFAULT 255; -- max_depth permet d'éviter les boucles infinies (par exemple, pointeur de champ sur lui même)

	SET valFather = val1;

	IF val1 <= 0 AND val2 > 0 THEN
		RETURN val2;
	ELSEIF val1 > 0 AND val2 <= 0 THEN
		RETURN val1;
	ELSEIF val1 <= 0 AND val2 <= 0 THEN
		RETURN 0;
	ELSEIF val1 = val2 THEN
		RETURN 0;
	END IF;

	REPEAT

		IF valFather > 0 THEN
			SELECT IFNULL(val_parent_id, 0) INTO valtemp FROM fld_restricted_values
			WHERE val_tnt_id=tenant AND val_id=valFather;

			SET valFather = valtemp;
		END IF;

		SET count1 = count1 + 1;
	UNTIL valFather = 0 OR count1 >= MAX_DEPTH
	END REPEAT;

	SET valFather = val2;

	REPEAT

		IF valFather > 0 THEN
			SELECT IFNULL(val_parent_id, 0) INTO valtemp FROM fld_restricted_values
			WHERE val_tnt_id=tenant AND val_id=valFather;
			SET valFather = valtemp;
		END IF;

		SET count2 = count2 + 1;
	UNTIL valFather = 0 OR count2 >= MAX_DEPTH
	END REPEAT;


	IF count1 > count2 THEN
		RETURN val1;
	ELSEIF count1 < count2 THEN
		RETURN val2;
	END IF;

	RETURN 0;

END//

/*
	*	\brief Détermine, entre deux tarifs, celui qui est prioritaire (s'il y a lieu). Cette fonction n'a pas vocation a être publique
	*	\param	prc_id_1, Obligatoire, non NULL, identifiant du premier tarif
	*	\param	prc_id_2, Obligatoire, non NULL, identifiant du deuxième tarif
	*	\param	tenant, Obligatoire, non NULL, identifiant du locataire
	*	\return L'identifiant du tarif prioritaire, ou 0 en cas d'égalité ou d'erreur(s)
*/
CREATE FUNCTION RIA_PRICES_COMPARE_PRIORITY(prc_id_1 INT, prc_id_2 INT, tenant INT) RETURNS INT NOT DETERMINISTIC READS SQL DATA
BEGIN

	DECLARE IsPromotion1, IsSync1, IsPromotion2, IsSync2, qteManaged TINYINT DEFAULT 0;
	DECLARE QteMin1, PrdId1, CatId1, QteMin2, PrdId2, CatId2, fldId, fldId1, fldId2, fldId3, fldTypeId, fldPriority, done, force_done_id, winnerid, idcatpriority, idcatpriority2, depthTop, valId1, valId2, idp_win INT DEFAULT 0;
	DECLARE theNext INT DEFAULT 1;
	DECLARE vFld1, vFld2, v1_Fld1, v1_Fld2, v2_Fld1, v2_Fld2, v3_Fld1, v3_Fld2 VARCHAR(75);

	/*
		L'ordre classique de détermination de l'importance d'un tarif est celui-ci (décroissant)
		- Est une promotion boutique Oui/Non
		- Parcourt des conditions tarifaires possibles
			- parmi ses conditions, gestion de la condition spéciale "Quantité minimale" (cf. plus bas)
		- Est directement sur le produit Oui/Non
		- Est directement sur une catégorie Oui/Non
			- Si les deux le sont, quelle est la catégorie la plus proche du produit ?
		- Période de validité plus ou moins courte
	*/
	
	-- Récupère tous les champs libres sur lesquels on peut appliquer une condition tarifaire, triés par priorité de la plus importante à la moindre
	DECLARE fields_cursor CURSOR FOR
	SELECT ppf_fld_id, ppf_fld_id_1, ppf_fld_id_2, ppf_fld_id_3, ppf_priority
	FROM prc_price_fields 
	WHERE ppf_tnt_id=tenant
	ORDER BY ppf_priority ASC;

	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	
	IF prc_id_1 = prc_id_2 THEN
		RETURN 0;
	END IF;
	
	-- informations tarif 1
	SELECT prc_is_promotion, prc_qte_min, prc_prd_id, prc_cat_id, prc_is_sync
	INTO IsPromotion1, QteMin1, PrdId1, CatId1, IsSync1
	FROM prc_prices
	WHERE prc_is_deleted=0 AND prc_tnt_id=tenant and prc_id=prc_id_1;

	IF done THEN
		RETURN 0;
	END IF;
	
	-- informations tarif 2
	SELECT prc_is_promotion, prc_qte_min, prc_prd_id, prc_cat_id, prc_is_sync
	INTO IsPromotion2, QteMin2, PrdId2, CatId2, IsSync2
	FROM prc_prices
	WHERE prc_is_deleted=0 AND prc_tnt_id=tenant and prc_id=prc_id_2;

	IF done THEN
		RETURN 0;
	END IF;
	
	-- les promotions boutiques sont prioritaires
	IF IsPromotion1 AND NOT IsPromotion2 THEN
		RETURN prc_id_1;
	ELSEIF IsPromotion2 AND NOT IsPromotion1 THEN
		RETURN prc_id_2;
	END IF;

	/*
		Important : le curseur retourne toutes les conditions tarifaires potentielles sauf une : la quantité minimale d'achat (qui n'est pas un champ avancé en tant que tel)
		Lorsque l'on va parcourir la boucle, on utilise l'information de classement "ppf_priority" pour déterminer l'ordre de priorité
		Ce champ "ppf_priority" est prévu pour contenir une éventuelle rupture séquencielle (exemple : 1, 2, 3, 5)
		Dans l'exemple ci-dessus, "4" est donc la position de la condition "Quantité minimale" dans l'ordre de priorité
		Si la séquence de "ppf_priority" contient plusieurs "trous" (1, 2, 4, 5, 7) On considère que 3 est la position de la quantité minimale, le deuxième trou est ignoré (marqueur booléen "qteManaged")
	*/
	
	OPEN fields_cursor;
	REPEAT
		FETCH fields_cursor INTO fldId, fldId1, fldId2, fldId3, fldPriority;
		IF NOT done THEN
			
			-- le "trou" pour la condition sur la quantité minimale vient d'être trouvé
			IF fldPriority > theNext AND NOT qteManaged AND force_done_id = 0 THEN
				
				-- comparaison sur la quantité minimale (la plus élevée l'emporte)
				IF QteMin1 > QteMin2 THEN
					SET force_done_id = prc_id_1;
				ELSEIF QteMin1 < QteMin2 THEN
					SET force_done_id = prc_id_2;
				END IF;
				
				-- un tarif concerne le produit, l'autre concerne un nuiveau inférieur (catégorie ou globale)
				IF force_done_id = 0 AND PrdId1 != 0 AND PrdId2 = 0 THEN
					SET force_done_id = prc_id_1;
				ELSEIF force_done_id = 0 AND PrdId1 = 0 AND PrdId2 != 0 THEN
					SET force_done_id = prc_id_2;
				END IF;
				
				-- un tarif concerne une catégorie, l'autre est global
				IF force_done_id = 0 AND CatId1 != 0 AND CatId2 = 0 THEN
					SET force_done_id = prc_id_1;
				ELSEIF force_done_id = 0 AND CatId1 = 0 AND CatId2 != 0 THEN
					SET force_done_id = prc_id_2;
				ELSEIF force_done_id = 0 AND CatId1 != 0 AND CatId2 != 0 THEN
					-- les deux tarifs concernent une catégorie, on détermine laquelle est prioritaire
					SET idcatpriority = RIA_GET_PRICE_CAT_PRIORITY( prc_id_1, CatId1, IsSync1, prc_id_2, CatId2, IsSync2, tenant );
					IF idcatpriority > 0 THEN
						SET force_done_id = idcatpriority;
					END IF;
				END IF;
				
				SET theNext = theNext + 1;
			END IF;

			IF force_done_id = 0 THEN
			
				-- le champ avancé "catégories tarifaires bloquées" n'entre pas dans la comparaison
				IF fldId != 3027 THEN

					-- on recherche si les deux tarifs ont les conditions spécifiées pour les champ en questions
					SET vFld1 = RIA_PRICE_CONDITION_GET( prc_id_1, fldId, tenant );
					SET vFld2 = RIA_PRICE_CONDITION_GET( prc_id_2, fldId, tenant );
							
					IF vFld1 IS NOT NULL AND vFld2 IS NULL THEN
						SET force_done_id = prc_id_1;
					ELSEIF vFld1 IS NULL AND vFld2 IS NOT NULL THEN
						SET force_done_id = prc_id_2;
					ELSEIF vFld1 IS NOT NULL AND vFld2 IS NOT NULL THEN

						-- un tarif concerne le produit, l'autre concerne un nuiveau inférieur (catégorie ou globale)
						IF force_done_id = 0 AND PrdId1 != 0 AND PrdId2 = 0 THEN
							SET force_done_id = prc_id_1;
						ELSEIF force_done_id = 0 AND PrdId2 != 0 AND PrdId1 = 0 THEN
							SET force_done_id = prc_id_2;
						END IF;

						-- recherche pour le deuxième champs 
						IF fldId1 > 0 THEN
							SET v1_Fld1 = RIA_PRICE_CONDITION_GET( prc_id_1, fldId1, tenant );
							SET v1_Fld2 = RIA_PRICE_CONDITION_GET( prc_id_2, fldId1, tenant );
									
							IF v1_Fld1 IS NOT NULL AND v1_Fld2 IS NULL THEN
								SET force_done_id = prc_id_1;
							ELSEIF v1_Fld1 IS NULL AND v1_Fld2 IS NOT NULL THEN
								SET force_done_id = prc_id_2;
							END IF;
						END IF;

						-- recherche pour le troisième champs 
						IF force_done_id=0 AND fldId2 > 0 THEN
							SET v2_Fld1 = RIA_PRICE_CONDITION_GET( prc_id_1, fldId2, tenant );
							SET v2_Fld2 = RIA_PRICE_CONDITION_GET( prc_id_2, fldId2, tenant );
									
							IF v2_Fld1 IS NOT NULL AND v2_Fld2 IS NULL THEN
								SET force_done_id = prc_id_1;
							ELSEIF v2_Fld1 IS NULL AND v2_Fld2 IS NOT NULL THEN
								SET force_done_id = prc_id_2;
							END IF;
						END IF;

						-- recherche pour le quatrième champs 
						IF force_done_id=0 AND fldId3 > 0 THEN
							SET v3_Fld1 = RIA_PRICE_CONDITION_GET( prc_id_1, fldId3, tenant );
							SET v3_Fld2 = RIA_PRICE_CONDITION_GET( prc_id_2, fldId3, tenant );
									
							IF v3_Fld1 IS NOT NULL AND v3_Fld2 IS NULL THEN
								SET force_done_id = prc_id_1;
							ELSEIF v3_Fld1 IS NULL AND v3_Fld2 IS NOT NULL THEN
								SET force_done_id = prc_id_2;
							END IF;
						END IF;

						/*
						Les priorités ne fonctionne pas correctement avec le type de champs sur ce point, a voir plus tard si la demande est faite
						-- pour le type "12 - hiérarchique", il ya une particularité
						-- la valeur hiérarchique la plus profonde l'emporte
						IF force_done_id==0 && fldTypeId = 12 THEN

							IF RIA_IS_UINT(vFld1) AND NOT RIA_IS_UINT(vFld2) THEN
								SET force_done_id = prc_id_1;
							ELSEIF NOT RIA_IS_UINT(vFld1) AND RIA_IS_UINT(vFld2) THEN
								SET force_done_id = prc_id_2;
							ELSEIF RIA_IS_UINT(vFld1) AND RIA_IS_UINT(vFld2) THEN

								SET valId1 = CAST(vFld1 AS UNSIGNED);
								SET valId2 = CAST(vFld2 AS UNSIGNED);

								SET depthTop = RIA_GET_VAL_ID_DEPTH_MAX( tenant, valId1, valId2 );
								IF depthTop = valId1 THEN
									SET force_done_id = prc_id_1;
								ELSEIF depthTop = valId2 THEN
									SET force_done_id = prc_id_2;
								END IF;

							END IF;
						END IF;
						*/
						
						-- un tarif concerne une catégorie, l'autre est global
						IF force_done_id = 0 AND CatId1 != 0 AND CatId2 = 0 THEN
							SET force_done_id = prc_id_1;
						ELSEIF force_done_id = 0 AND CatId2 != 0 AND CatId1 = 0 THEN
							SET force_done_id = prc_id_2;
						ELSEIF force_done_id = 0 AND CatId2 != 0 AND CatId1 != 0 THEN
							-- les deux tarifs concernent une catégorie, on détermine laquelle est prioritaire
							SET idcatpriority2 = RIA_GET_PRICE_CAT_PRIORITY( prc_id_1, CatId1, IsSync1, prc_id_2, CatId2, IsSync2, tenant );
							IF idcatpriority2 > 0 THEN
								SET force_done_id = idcatpriority2;
							END IF;
						END IF;

					END IF;
				END IF;
				
				SET theNext = fldPriority + 1;
			END IF;

		END IF;
	UNTIL done
	END REPEAT;
	CLOSE fields_cursor;

	IF force_done_id > 0 THEN
		RETURN force_done_id;
	END IF;

	-- il n'y a pas de "trou" pour la condition sur la quantité minimale, elle est donc gérée en dernier
	IF NOT qteManaged THEN
		IF QteMin1 > QteMin2 THEN
			RETURN prc_id_1;
		ELSEIF QteMin1 < QteMin2 THEN
			RETURN prc_id_2;
		END IF;
	END IF;

	-- un tarif concerne le produit, l'autre concerne un nuiveau inférieur (catégorie ou globale)
	IF PrdId1 != 0 AND PrdId2 = 0 THEN
		RETURN prc_id_1;
	ELSEIF PrdId2 != 0 AND PrdId1 = 0 THEN
		RETURN prc_id_2;
	END IF;
	
	-- un tarif concerne une catégorie, l'autre est global
	IF CatId1 != 0 AND CatId2 = 0 THEN
		RETURN prc_id_1;
	ELSEIF CatId2 != 0 AND CatId1 = 0 THEN
		RETURN prc_id_2;
	ELSEIF CatId2 != 0 AND CatId1 != 0 THEN
		-- les deux tarifs concernent une catégorie, on détermine laquelle est prioritaire
		SET idp_win = RIA_GET_PRICE_CAT_PRIORITY( prc_id_1, CatId1, IsSync1, prc_id_2, CatId2, IsSync2, tenant );
		IF idp_win != 0 THEN
			RETURN idp_win;
		END IF;
	END IF;

	-- égalité toujours, la période de validité la plus courte l'emporte
	SELECT prc_id INTO winnerid
	FROM prc_prices
	WHERE prc_tnt_id=tenant AND prc_id IN (prc_id_1, prc_id_2)
	ORDER BY prc_date_end-prc_date_start
	LIMIT 0, 1;

	RETURN winnerid;

END//

/*
	*	\brief Détermine, entre deux prix associés à une catégorie, celui qui est prioritaire. Cette fonction n'a pas vocation a être publique
	*	\bug Cette fonction est incohérente avec le système établi dans la fonction "GET_PRICE_HT" pour récupérer l'arboresence tarifable.
	*	\param	prc_id_1 Obligatoire, non NULL,  identifiant du premier tarif
	*	\param	CatId1 Obligatoire, non NULL, détermine l'identifiant de la catégorie rattachée au premier tarif
	*	\param	prdIsSync1 Obligatoire, non NULL, détermine si le produit du premier tarif est synchronisé
	*	\param	prc_id_2 Obligatoire, non NULL, identifiant du deuxième tarif
	*	\param	CatId2 Obligatoire, non NULL, détermine l'identifiant de la catégorie rattachée au deuxième tarif
	*	\param	prdIsSync2 Obligatoire, non NULL, détermine si le produit du deuxième tarif est synchronisé
	*	\param	tenant Obligatoire, non NULL, identifiant du locataire
	*	\return L'identifiant du tarif prioritaire, ou 0 si aucun n'a pu être déterminé
*/
CREATE FUNCTION RIA_GET_PRICE_CAT_PRIORITY( prc_id_1 INT, CatId1 INT, prdIsSync1 INT , prc_id_2 INT , CatId2 INT , prdIsSync2 INT , tenant INT ) RETURNS INT NOT DETERMINISTIC READS SQL DATA
BEGIN

	DECLARE cat_publish_1, cat_publish_2, cat_is_sync_1, cat_is_sync_2 TINYINT;
	DECLARE p1depth, p2depth INT;
	
	IF CatId1 = CatId2 THEN
		RETURN 0;
	ELSEIF CatId1 = 0 THEN
		RETURN prc_id_2;
	ELSEIF CatId2 = 0 THEN
		RETURN prc_id_1;
	END IF;

	/*
		On recherche ici une logique de priorité entre deux tarifs rattachés à une catégorie de produit
		L'ordre classique décroissant est le suivant :
		- La catégorié est publiée sur l'un et pas sur l'autre
		- Si le produit en entrée est synchronisée :
			- Une catégorie est synchronisée et pas l'autre
		Toujours égalité : la catégorie est la plus profonde hiérarchiquement
	*/
	
	SELECT cat_publish, cat_is_sync INTO cat_publish_1, cat_is_sync_1
	FROM prd_categories
	WHERE cat_tnt_id=tenant AND cat_date_deleted IS NULL AND cat_id=CatId1;

	SELECT cat_publish, cat_is_sync INTO cat_publish_2, cat_is_sync_2
	FROM prd_categories
	WHERE cat_tnt_id=tenant AND cat_date_deleted IS NULL AND cat_id=CatId2;

	IF cat_publish_1 AND NOT cat_publish_2 THEN
		RETURN prc_id_1;
	ELSEIF NOT cat_publish_1 AND cat_publish_2 THEN
		RETURN prc_id_2;
	END IF;

	IF prdIsSync1 AND prdIsSync2 THEN
		IF cat_is_sync_1 AND NOT cat_is_sync_2 THEN
			RETURN prc_id_1;
		ELSEIF NOT cat_is_sync_1 AND cat_is_sync_2 THEN
			RETURN prc_id_2;
		END IF;
	ELSEIF prdIsSync1 AND cat_is_sync_1 THEN
		RETURN prc_id_1;
	ELSEIF prdIsSync2 AND cat_is_sync_2 THEN
		RETURN prc_id_2;
	END IF;

	SELECT MAX(cat_parent_depth) INTO p1depth
	FROM prd_cat_hierarchy
	WHERE cat_tnt_id=tenant and cat_child_id=CatId1;

	SELECT MAX(cat_parent_depth) INTO p2depth
	FROM prd_cat_hierarchy
	WHERE cat_tnt_id=tenant and cat_child_id=CatId2;

	IF p1depth > p2depth THEN
		RETURN prc_id_1;
	ELSEIF p2depth > p1depth THEN
		RETURN prc_id_2;
	END IF;

	RETURN 0;

END//

/*
	*	 \brief	Cette fonction est chargée de trouver le prix HT dun produit, hors promotions boutique, dans un contexte donné. Elle devra être renommé sans le suffixe 2 lors de la msie en production. La fonction peut retourner un prix négatif dans certains contextes.
	*	\param	tenant Non NULL, identifiant du locataire
	*	\param	prd Non NULL, identifiant du produit
	*	\param	usr Non NULL, identifiant du client (peut être 0 pour un client non connecté)
	*	\param	qte Non NULL, quantité souhaitée du produit (note : le prix retourné est celui unitaire, mais tenant compte d'une éventuel réduction pour commande en nombre). Si conditionnement, cette quantité doit être multipliée au préalable par col_qte.
	*	\param	col NULL autorisé, identifiant d'un conditionnement particulier. Propre au locatire Sodip, son utilisation n'est pas recommandée
	*	\param	discType Non NULL, identifiant du type de calcul des remises (actuellement 3 possiblités : 0 pour remises en cascade, 1 pour remises additionnées, 2 pour choix de la meilleure remise)
	*	\param	usr_prc_id Non NULL, identifiant de la catégorie tarifaire du client ou par défaut.
	*	\param	lastPriceDays NULL autorisé, détermine la prise en compte éventuel des tarifs de l'historique. Si 0 ou NULL, ou si usr n'est pas spécifié, ce paramètre est ignoré. Sinon, indique le nombre de jours sur lequel les factures peuvent être utilisées pour déterminer le dernier prix de vente. Si rien n'a été vendu, le calcul se poursuit normalement.
	*	\param lng_code Non NULL, langue par défaut pour récupérer les valeurs des champs avancés.
	*	\param cat_root_prices NULL autorisé, identifiant de la catégorie root dont l'arborescence est porteuse des tarifs.
	*	\param usr_ref NULL autorisé, code client de l'utilisateur.
	*	\param brd_id NULL autorisé, marque du produit (-1 si l'information n'est pas précalculée).
	*	\param centralized NULL autorisé, produit centralisé oui/non (Null si l'information n'est pas précalculée).
	*	\param ecotaxe si la valeur est supérieure à 0, ce montant (l'écotaxe du produit) sera exclut de toutes remises
	*
	*	\return	NULL en cas d'erreur ou si aucun tarif n'a pas être trouvé, le tarif dans le cas contraire, avec une précision à 6 décimales
 */
CREATE FUNCTION GET_PRICE_HT( tenant INT, prd INT, usr INT, qte INT, col INT, discType INT, usr_prc_id INT, lastPriceDays INT, lng_code VARCHAR(5), cat_root_prices INT, usr_ref VARCHAR(17), brd_id INT, centralized TINYINT, ecotaxe DECIMAL(8, 4) ) RETURNS DECIMAL(12, 6) NOT DETERMINISTIC READS SQL DATA
BEGIN

	-- Toutes les déclarations, sauf curseurs
	DECLARE v_prc_id, done, count_values, count_values_ordered, cpt_i, v_prc_id_top, cpt_top, cpt_j, v_prc_id_2, current_id, discount_counter, my_price_type, disc_type, actual_cat, v_prc_cat_id INT;
	DECLARE cat0_id, cat1_id, cat2_id, cat3_id, cat4_id, cat5_id, cat6_id, cat7_id, cat8_id, cat9_id, cat10_id, cat11_id, cat_count, v_cat_id_temp INT;
	DECLARE fct_result, super_price_ht, disc_val, sumRate, my_price_val, price_temp, price_from_histo DECIMAL(12, 6);
	DECLARE isFirstDiscount, you_have_to_stop, negativeBase, superIsInit, my_price_is_cumuled, rank_is_ok, first_pass TINYINT;
	DECLARE price_rows, price_rows_ordered, promo_id_rows, promo_val_rows VARCHAR(1024);
		
	-- récupère les prix qui respectent les conditions de base (dates, quantité min et max, produit, catégories)
	DECLARE prices  CURSOR FOR
	SELECT prc_id
	FROM prc_prices USE INDEX (prc_tnt_id_4)
	WHERE prc_is_promotion=0
	AND prc_is_deleted=0
	AND prc_tnt_id=tenant
	AND prc_date_start<=NOW()
	AND prc_date_end>NOW()
	AND (
		(
			prc_prd_id=prd
			AND prc_cat_id=0
		) OR (
			prc_prd_id=0
			AND prc_cat_id IN (0, actual_cat, cat0_id, cat1_id, cat2_id, cat3_id, cat4_id, cat5_id, cat6_id, cat7_id, cat8_id, cat9_id, cat10_id, cat11_id)
		)
	)
	AND prc_qte_min<=qte
	AND prc_qte_max>=qte
	AND NOT EXISTS (
		SELECT 1 FROM prc_price_conditions
		WHERE ppc_tnt_id=tenant AND ppc_symbol='='
		AND (
			(
				ppc_fld_id=455 AND ppc_value!=usr_ref
			) OR (
				ppc_fld_id=456 AND ppc_value!=usr_prc_id
			)
		)
		AND ppc_prc_id = prc_id
	);
	
	-- récupération de la hiérarchie montante à partir de actual_cat
	DECLARE cat_parents CURSOR FOR
	SELECT cat_parent_id
	FROM prd_cat_hierarchy
	WHERE cat_tnt_id=tenant
	AND cat_child_id=actual_cat;
	
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	
	SET fct_result = NULL;
	SET cat0_id = 0;
	SET cat1_id = 0;
	SET cat2_id = 0;
	SET cat3_id = 0;
	SET cat4_id = 0;
	SET cat5_id = 0;
	SET cat6_id = 0;
	SET cat7_id = 0;
	SET cat8_id = 0;
	SET cat9_id = 0;
	SET cat10_id = 0;
	SET cat11_id = 0;
	
	-- Contrôle de l'entrée
	IF tenant IS NULL OR prd IS NULL OR usr IS NULL OR qte IS NULL OR usr_prc_id IS NULL OR discType IS NULL OR discType < 0 OR discType > 2 OR lng_code IS NULL THEN
		RETURN fct_result;
	END IF;
	IF col IS NULL THEN
		SET col = 0;
	END IF;
	IF lastPriceDays IS NULL OR lastPriceDays <= 0 THEN
		SET lastPriceDays = 0;
	END IF;
	IF usr_ref IS NULL THEN
		SET usr_ref = '';
	END IF;
	
	SET done = 0;
	
	SET done = 0;
	SET count_values = 0;
	SET count_values_ordered = 0;
	SET price_rows = '';
	SET price_rows_ordered = '';
	SET promo_id_rows = '';
	SET promo_val_rows = '';
	
	SET done = 0;
	
	IF cat_root_prices IS NULL THEN
		SET cat_root_prices = 0;
	END IF;
	
	IF cat_root_prices > 0 THEN
		/* On récupère "actual_cat" depuis l'arborescence de "cat_root_prices" */
		/* On conserve un tri "sync, publish, pos" qui n'a théoriquement pas d'intérêt (le produit n'est classé qu'une fois dans l'arboresence "cat_root_prices") */
		SELECT c.cat_id INTO actual_cat FROM prd_categories as c
		JOIN prd_classify ON c.cat_tnt_id = cly_tnt_id AND c.cat_id = cly_cat_id
		JOIN prd_cat_hierarchy AS h ON c.cat_tnt_id = h.cat_tnt_id AND c.cat_id = h.cat_child_id
		WHERE cly_prd_id = prd
			AND c.cat_date_deleted IS NULL
			AND cly_tnt_id = tenant
			AND h.cat_parent_id = cat_root_prices
		ORDER BY c.cat_is_sync DESC, c.cat_publish DESC, c.cat_pos ASC
		LIMIT 0, 1;
		
		SET done = 0;
	END IF;
	
	IF actual_cat IS NULL OR actual_cat = 0 THEN
		/* On récupère "actual_cat" depuis toute l'arborescence */
		SELECT cat_id INTO actual_cat FROM prd_categories
		JOIN prd_classify ON cat_tnt_id=cly_tnt_id AND cat_id=cly_cat_id
		WHERE cly_prd_id=prd
			AND cat_date_deleted IS NULL
			AND cly_tnt_id=tenant
		ORDER BY cat_is_sync DESC, cat_publish DESC, cat_pos ASC
		LIMIT 0,1;
		
		SET done = 0;
	END IF;
	
	/* le produit n'est pas classé */
	IF actual_cat IS NULL THEN
		SET actual_cat = 0;
	END IF;
	
	IF usr <= 0 THEN
		SET lastPriceDays = 0;
	END IF;
	
	IF lastPriceDays > 0 THEN
		
		-- si la requête ci-dessous ne donne aucun résultat, "done" sera égale à 1 et la recherche d'un prix se poursuivra normalement
		SET done = 0;
		
		-- recherche du dernier prix pratiqué pour le client pour ce produit
		SELECT prd_price_ht INTO price_from_histo
		FROM ord_inv_products
		JOIN ord_invoices ON prd_inv_id=inv_id AND prd_tnt_id=inv_tnt_id
		WHERE inv_usr_id=usr AND prd_tnt_id=tenant AND prd_id=prd
		AND DATE_ADD(inv_date, INTERVAL lastPriceDays DAY) >= NOW()
		ORDER BY inv_date, prd_line_id DESC
		LIMIT 0, 1;
		
		IF NOT done THEN
			IF price_from_histo != 0 THEN
				RETURN price_from_histo;
			END IF;
		END IF;
		
	END IF;
	
	SET done = 0;
	SET cat_count = 0;
	
	/* pas plus de 12 niveaux (actuellement, le max est à 5) */
	/* en l'absence d'un type array, on est contrait de faire ainsi */
	OPEN cat_parents;
	REPEAT
		FETCH cat_parents INTO v_cat_id_temp;
		IF NOT done THEN
			IF cat_count = 0 THEN
				SET cat0_id = v_cat_id_temp;
			ELSEIF cat_count = 1 THEN
				SET cat1_id = v_cat_id_temp;
			ELSEIF cat_count = 2 THEN
				SET cat2_id = v_cat_id_temp;
			ELSEIF cat_count = 3 THEN
				SET cat3_id = v_cat_id_temp;
			ELSEIF cat_count = 4 THEN
				SET cat4_id = v_cat_id_temp;
			ELSEIF cat_count = 5 THEN
				SET cat5_id = v_cat_id_temp;
			ELSEIF cat_count = 6 THEN
				SET cat6_id = v_cat_id_temp;
			ELSEIF cat_count = 7 THEN
				SET cat7_id = v_cat_id_temp;
			ELSEIF cat_count = 8 THEN
				SET cat8_id = v_cat_id_temp;
			ELSEIF cat_count = 9 THEN
				SET cat9_id = v_cat_id_temp;
			ELSEIF cat_count = 10 THEN
				SET cat10_id = v_cat_id_temp;
			ELSEIF cat_count = 11 THEN
				SET cat11_id = v_cat_id_temp;
			END IF;
			SET cat_count = cat_count + 1;
		END IF;
	UNTIL done
	END REPEAT;
	CLOSE cat_parents;
	
	SET done = 0;
	
	OPEN prices;
	REPEAT
		FETCH prices INTO v_prc_id;
		IF NOT done THEN
			-- vérifie pour chaque tarif qu'il est applicable par rapport au détail des conditions tarifaires
			if RIA_IS_APPLY( 0, v_prc_id, tenant, prd, usr, col, usr_prc_id, lng_code, usr_ref, brd_id, centralized ) THEN
				-- enregistre les ID de tarifs ayant passé le test avec succès dans un 'chaine d'identifiants' (avec un séprateur |)
				SET price_rows = CONCAT(price_rows, '|', v_prc_id);
				SET count_values = count_values + 1;
			END IF;
		END IF;
	UNTIL done
	END REPEAT;
	CLOSE prices;
	
	SET done = 0;
	
	IF count_values > 0 THEN
		-- tri des tarifs par priorité décroissante (la colonne "prc_order" de la table temporaire sert à stocker l'ordre de tri)
		-- price_rows_ordered contient la même chose que price_rows (une chaine représentant les ID de tarifs, séparés par un caractère spécial), sauf que dans cette variable les identifiant sont triés par importance décroissante
		IF count_values > 1 THEN
			WHILE count_values_ordered < count_values DO
				SET cpt_top = 0;
				SET rank_is_ok = 0;
				SET first_pass = 1;
				WHILE first_pass OR NOT rank_is_ok DO
					SET first_pass = 0;
					IF price_rows_ordered LIKE CONCAT('%|', RIA_GET_ID_AT_POS(price_rows, cpt_top)) OR
						price_rows_ordered LIKE CONCAT(RIA_GET_ID_AT_POS(price_rows, cpt_top), '|%') OR
						price_rows_ordered LIKE CONCAT('%|', RIA_GET_ID_AT_POS(price_rows, cpt_top), '|%') THEN
						SET cpt_top = cpt_top + 1;
					ELSE
						SET rank_is_ok = 1;
					END IF;
				END WHILE;
				SET cpt_i = 0;
				WHILE cpt_i < count_values DO
					SET v_prc_id_top = RIA_GET_ID_AT_POS(price_rows, cpt_top);
					SET v_prc_id_2 = RIA_GET_ID_AT_POS(price_rows, cpt_i);
					IF
						price_rows_ordered NOT LIKE CONCAT('%|', v_prc_id_2) AND
						price_rows_ordered NOT LIKE CONCAT(v_prc_id_2, '|%') AND
						price_rows_ordered NOT LIKE CONCAT('%|', v_prc_id_2, '|%')
					THEN
						IF RIA_PRICES_COMPARE_PRIORITY( v_prc_id_top, v_prc_id_2, tenant ) = v_prc_id_2 THEN
							SET cpt_top = cpt_i;
						END IF;
					END IF;
					SET cpt_i = cpt_i + 1;
				END WHILE;
				SET price_rows_ordered = CONCAT(price_rows_ordered, '|', RIA_GET_ID_AT_POS(price_rows, cpt_top));
				SET count_values_ordered = count_values_ordered + 1;
			END WHILE;
		ELSE
		
			SET price_rows_ordered = price_rows;
		
		END IF;
		
		SET done = 0;
		SET discount_counter = 0;
		SET you_have_to_stop = 0;
		SET negativeBase = 0; -- Détermine si un prix négatif sera autorisé en sortie finale
		SET isFirstDiscount = 1; -- Détermine l'apparition de la première remise (comme on part d'un tri de priorité décroissante, cette dernière remise n'a pas obligatoi à être cumulable, contrairement aux autres)

		-- parcourt des tarifs triés par ordre décroissant de priorité : au premier tarif de type "prix net" trouvé, on peut sortir
		-- promo_id_rows et promo_val_rows permettent de stocker respectivement les ID et les valeurs des remises qui pourront s'appliquer
		SET cpt_i = 0;
		WHILE cpt_i < count_values AND NOT you_have_to_stop DO
			SET current_id = RIA_GET_ID_AT_POS(price_rows_ordered, cpt_i);
			SELECT prc_type_id, prc_value, prc_is_cumuled INTO my_price_type, my_price_val, my_price_is_cumuled
			FROM prc_prices WHERE prc_tnt_id=tenant AND prc_id=current_id;

			IF my_price_type = 1 THEN
				SET fct_result = my_price_val;
				SET negativeBase = my_price_val < 0;
				SET you_have_to_stop = 1;
			ELSEIF isFirstDiscount OR my_price_is_cumuled OR discType = 2 THEN
				SET isFirstDiscount = 0;
				SET discount_counter = discount_counter + 1;
				SET promo_id_rows = CONCAT(promo_id_rows, '|', my_price_type);
				SET promo_val_rows = CONCAT(promo_val_rows, '|', my_price_val);
			END IF;
			SET cpt_i = cpt_i + 1;
		END WHILE;
		
		-- on ne poursuit que si on a trouvé une base tarifaire, et pas seulement des remises (ou rien)
		IF fct_result IS NOT NULL THEN
			
			SET done = 0;

			IF discount_counter > 0 THEN
			
				-- discType cotnient un ID qui détermine al manière dont les remises sont appliquées par rapport au prix de base
			
				IF discType = 1 THEN -- Calcul des remises en cascade

					-- Inverse l'ordre du tableau des remises
					SET cpt_i = discount_counter - 1;
					WHILE cpt_i >= 0 DO
						SET disc_type = RIA_GET_ID_AT_POS(promo_id_rows, cpt_i);
						SET disc_val = RIA_GET_VAL_AT_POS(promo_val_rows, cpt_i);
						-- applique les remises successivement du général au particulier
						IF disc_type = 2 THEN
							IF ecotaxe > 0 THEN
								SET fct_result = ((fct_result-ecotaxe) * ((100 - disc_val) / 100)) + ecotaxe;
							ELSE
								SET fct_result = fct_result * ((100 - disc_val) / 100);
							END IF;
						ELSEIF disc_type = 3 THEN
							IF negativeBase THEN
								SET fct_result = fct_result + disc_val;
							ELSE
								SET fct_result = fct_result - disc_val;
							END IF;
						END IF;
						SET cpt_i = cpt_i - 1;
					END WHILE;

				ELSEIF discType = 0 THEN -- Addition des remises

					SET sumRate = 0; -- le taux total de remise est stocké dans cette variable

					SET cpt_i = 0;
					WHILE cpt_i < discount_counter DO
						SET disc_type = RIA_GET_ID_AT_POS(promo_id_rows, cpt_i);
						SET disc_val = RIA_GET_VAL_AT_POS(promo_val_rows, cpt_i);
						IF disc_type = 2 THEN
							SET sumRate = sumRate + ( disc_val / 100 );
						ELSEIF disc_type = 3 THEN
							IF negativeBase THEN
								SET fct_result = fct_result + disc_val;
							ELSE
								SET fct_result = fct_result - disc_val;
							END IF;
						END IF;
						SET cpt_i = cpt_i + 1;
					END WHILE;

					IF sumRate > 1 THEN
						SET fct_result = 0;
					ELSE
						IF ecotaxe > 0 THEN
							SET fct_result = (( fct_result - ecotaxe ) * ( 1 - sumRate )) + ecotaxe;
						ELSE 
							SET fct_result = fct_result * ( 1 - sumRate );
						END IF;
					END IF;

				ELSEIF discType = 2 THEN -- -- Recherche et application de la meilleure remise

					SET super_price_ht = -1;
					SET superIsInit = 0;
					
					SET cpt_i = 0;
					WHILE cpt_i < discount_counter DO
						SET disc_type = RIA_GET_ID_AT_POS(promo_id_rows, cpt_i);
						SET disc_val = RIA_GET_VAL_AT_POS(promo_val_rows, cpt_i);
						SET price_temp = fct_result;
						IF disc_type = 2 THEN
							IF ecotaxe > 0 THEN
								SET price_temp = ((price_temp - ecotaxe) * (1 - ( disc_val / 100))) + ecotaxe;
							ELSE
								SET price_temp = price_temp * (1 - ( disc_val / 100));
							END IF;
							IF ( price_temp > 0 OR negativeBase ) AND ( NOT superIsInit OR super_price_ht > price_temp ) THEN
								SET super_price_ht = price_temp;
								SET superIsInit = 1;
							END IF;
						ELSEIF disc_type = 3 THEN
							IF negativeBase THEN
								SET price_temp = price_temp + disc_val;
							ELSE
								SET price_temp = price_temp - disc_val;
							END IF;
							IF ( price_temp > 0 OR negativeBase ) AND ( NOT superIsInit OR super_price_ht>price_temp ) THEN
								SET super_price_ht = price_temp;
								SET superIsInit = 1;
							END IF;
						END IF;
						SET cpt_i = cpt_i + 1;
					END WHILE;

					SET fct_result = super_price_ht;

				END IF;
			END IF;
			
			-- le tarif trouvé est négatif suite à la gestion des remises, alors qu'il était positif au début (impossible)
			IF fct_result IS NOT NULL AND fct_result < 0 AND NOT negativeBase THEN
				SET fct_result = NULL;
			END IF;
			
		END IF;

	END IF;

	RETURN fct_result;

END//

/*
	*	\brief Cette fonction détermine si un utilisateur est exempt de taxe
	*	\param tenant Non NULL, identifiant du locataire
	*	\param usr Non NULL, identifiant du client (0 si non connecté)
	*	\param usr_prc_id Non NULL, identifiant de la catégorie tarifaire du client ou celle par défaut.
	*	\param lng_code Non NULL, langue par défaut pour récupérer les valeurs des champs avancés.
	*	\param usr_ref NULL autorisé, code client.
	*	\return 1 si éxonération, 0 sinon
*/
CREATE FUNCTION USR_IS_EXEMPT(tenant INT, usr INT, usr_prc_id INT, lng_code VARCHAR(5), usr_ref VARCHAR(17)) RETURNS TINYINT NOT DETERMINISTIC READS SQL DATA
BEGIN

	DECLARE exempt, done, group_have_rows TINYINT DEFAULT 0;
	DECLARE ptgid INT;
	
	DECLARE done2, v_is_physical TINYINT DEFAULT 0;
	DECLARE cndok TINYINT DEFAULT 1;
	DECLARE v_fld_id, v_cls_id, v_type_id INT;
	DECLARE v_value, v_physical_name VARCHAR(75);
	DECLARE v_symbol VARCHAR(10);
	
	-- Principe de l'éxonération :
	-- Un groupe est constitué de plusieurs conditions d'éxonérations
	-- Il suffit de valider un groupe pour être éxonéré (fonctionnement en OU)
	-- Au sein d'un groupe, toutes les conditions doivent être respectées (fonctionnement en ET)
	
	-- charge les éventuels groupes d'éxonérations
	DECLARE cursor_exo_global CURSOR FOR
		SELECT ptg_id FROM prc_tva_exempt_groups WHERE ptg_tnt_id=tenant and ptg_date_deleted IS NULL;

	-- chargement du détail d'un groupe d'exonération
	DECLARE cursor_exo CURSOR FOR
		SELECT fld_id, fld_cls_id, fld_type_id, ptc_value, ptc_symbol, fld_is_physical, IFNULL(fld_physical_name,'')
		FROM prc_tva_exempt_conditions JOIN prc_tva_fields
			ON ( ptc_fld_id=ptf_fld_id AND ptc_tnt_id=ptf_tnt_id )
		JOIN fld_fields
			ON ( ptf_fld_id=fld_id AND ( fld_tnt_id=ptf_tnt_id OR fld_tnt_id=0 ) )
		JOIN prc_tva_exempt_groups
			ON ( ptc_ptg_id=ptg_id AND ptc_tnt_id=ptg_tnt_id )
		WHERE ptg_tnt_id=tenant AND fld_date_deleted IS NULL AND ptg_date_deleted IS NULL AND ptg_id=ptgid;		
	
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	
	IF tenant IS NULL OR usr IS NULL OR usr_prc_id IS NULL OR lng_code IS NULL THEN
		RETURN NULL;
	END IF;
	
	IF usr_ref IS NULL THEN
		SET usr_ref = '';
	END IF;
	
	OPEN cursor_exo_global;
	REPEAT
		FETCH cursor_exo_global INTO ptgid;
		IF NOT done AND NOT exempt THEN
			-- création d'un nouveau bloc pour gérer le curseur imbriqué
			BEGIN
				DECLARE CONTINUE HANDLER FOR NOT FOUND SET done2 = 1;
				SET group_have_rows = 0;
				SET cndok = 1;
				
				-- recherche d'une exonération
				OPEN cursor_exo;
				REPEAT
					FETCH cursor_exo INTO v_fld_id, v_cls_id, v_type_id, v_value, v_symbol, v_is_physical, v_physical_name;
					IF NOT done2 AND cndok THEN
						SET group_have_rows = 1;
						IF v_cls_id = 2 THEN
							SET cndok = RIA_PRC_CONDITIONS_TEST_VALUE( v_fld_id, v_type_id, v_value, v_symbol, v_is_physical, v_physical_name, usr, tenant, 0, usr_prc_id, lng_code, usr_ref, -1, NULL );
						ELSE
							SET cndok = 0;
						END IF;
					END IF;
				UNTIL done2
				END REPEAT;
				CLOSE cursor_exo;
				SET done2 = 0;
				
				-- toutes les conditions ont été réunies, l'exemption est possible
				IF cndok AND group_have_rows THEN
					SET exempt = 1;
				END IF;
			END;
		END IF;
	UNTIL done
	END REPEAT;
	CLOSE cursor_exo_global;
	
	RETURN exempt;
	
END//

/*
	*	\brief Cette fonction est chargée de retourner le taux de TVA d'un article dans un contexte donné.
	*	\param tenant Obligatoire, non Null, identifiant du locataire.
	*	\param prd Obligatoire, non Null, identifiant du produit.
	*	\param cac_id Obligatoire, Nullable, catégorie comptable du client ou catégorie comptable par défaut.
	*	\return Le taux de TVA sous la forme d'un coefficient (d'une précision de 3 chiffres après la virgule), ou Null en cas d'échec.
*/
CREATE FUNCTION GET_TVA(tenant INT, prd INT, cac_id INT) RETURNS DECIMAL(4, 3) NOT DETERMINISTIC READS SQL DATA
BEGIN
	
	DECLARE final_tva DECIMAL(4, 3);
	
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET final_tva = NULL;

	-- contrôles préalables
	IF tenant IS NULL OR prd IS NULL THEN
		RETURN NULL;
	END IF;
	
	-- on récupère les tvas liées directement au produit
	SELECT ptv_tva_rate INTO final_tva FROM prc_tvas
	WHERE ptv_date_deleted IS NULL AND ptv_tnt_id = tenant AND ptv_prd_id = prd
	AND (ptv_cac_id IS NULL OR ptv_cac_id = cac_id)
	ORDER BY IFNULL(ptv_cac_id, 0) DESC
	LIMIT 0, 1;
	
	RETURN final_tva;
	
END//

/*
	*	\brief Cette fonction détermine si un tarif s'applique dans un contexte donné. Elle doit être renommée sans le suffixe "2" lors de sa mise en production
	*	\param tenant Non NULL, identifiant du locataire
	*	\param prdId Non NULL, identifiant du produit
	*	\param usrId Non NULL, identifiant du client (0 si le client n'est pas loggué)
	*	\param colId NULL autorisé, identifiant du conditionnement
	*	\param prcId Non NULL, identifiant du tarif à tester
	*	\param usr_prc_id Non NULL, identifiant de la catégorie tarifaire du client ou celle par défaut
	*	\param lng_code Non NULL, code langue pour les valeurs de champs avancés.
	*	\param usr_ref NULL autorisé, code client.
	*	\param brd_id NULL autorisé, marque du produit (-1 si l'information n'est pas précalculée).
	*	\param centralized NULL autorisé, produit centralisé oui/non (Null si l'information n'est pas précalculée).
	*
	*	\return Vrai si le tarif s'applique, faux dans le cas contraire, ou en cas d'erreur(s) dans les paramètres
*/
CREATE FUNCTION PRICE_IS_APPLY( tenant INT, prdId INT, usrId INT, colId INT, prcId INT, usr_prc_id INT, lng_code VARCHAR(5), usr_ref VARCHAR(17), brd_id INT, centralized TINYINT ) RETURNS TINYINT NOT DETERMINISTIC
BEGIN
	
	IF tenant IS NULL OR  prdId IS NULL OR usrId IS NULL OR prcId IS NULL OR usr_prc_id IS NULL OR lng_code IS NULL THEN
		RETURN 0;
	END IF;
	IF colId IS NULL THEN
		SET colId = 0;
	END IF;
	IF usr_ref IS NULL THEN
		SET usr_ref = '';
	END IF;
	
	RETURN RIA_IS_APPLY( 1, prcId, tenant, prdId, usrId, colId, usr_prc_id, lng_code, usr_ref, brd_id, centralized );
	
END//

/*
	*	\brief	Cette fonction compare deux tarifs et détermine lequel est prioritaire.
	*	\param	tenant Obligatoire, non NULL, identifiant du locataire
	*	\param	prcId1 Obligatoire, non NULL, identifiant du premier tarif
	*	\param	prcId2 Obligatoire, non NULL, identifiant du second tarif
	*	\return 0 En cas d'erreur(s), l'identifiant du tarif prioritaire dans le cas contraire
*/
CREATE FUNCTION COMPARE_PRICES(tenant INT, prcId1 INT, prcId2 INT) RETURNS INT NOT DETERMINISTIC READS SQL DATA
BEGIN
	
	IF tenant IS NULL OR prcId1 IS NULL OR prcId2 IS NULL OR prcId2 = prcId1 THEN
		RETURN 0;
	END IF;
	
	RETURN RIA_PRICES_COMPARE_PRIORITY(prcId1, prcId2, tenant);
	
END//
