
services:
    service_container:
        class: Symfony\Component\DependencyInjection\ContainerInterface
        public: true
        synthetic: true
    Symfony\Component\DependencyInjection\Tests\Fixtures\Prototype\Foo:
        class: Symfony\Component\DependencyInjection\Tests\Fixtures\Prototype\Foo
        public: true
        tags:
            - { name: tag, k: v }
        lazy: true
        properties: { p: 1 }
        calls:
            - [setFoo, ['@foo']]

        shared: false
        configurator: c
    foo:
        class: App\FooService
        public: true
