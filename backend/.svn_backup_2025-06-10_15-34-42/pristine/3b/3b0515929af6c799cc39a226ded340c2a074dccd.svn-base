<?php
// \cond onlyria

require_once('define.inc.php');
require_once('users.inc.php');
require_once('couchDb.inc.php');

/** \defgroup model_chat Chat
 *	\ingroup fdv
 *	Ce module comprend les fonctions nécessaires à la gestion du chat
 *	@{
 */

/** Cette fonction permet de tester l'existence d'une conversation
 *	@param int $id Obligatoire, identifiant de la conversation
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function ch_conversations_exists( $id ){

	$results = CouchDB::create(_COUCHDB_CHAT_DB_NAME)->get(CLS_CHAT_CONVERSATIONS, $id);

	if( isset($results['_id']) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}
	return false;
}

/** Cette fonction permet de récupèrer une liste de conversation
 *	@param int $id Facultatif : identifiant de la conversation
 *	@param int $offset Facultatif : identifiant du premier changement souhaité ( since de couchdb )
 *	@param int $limit Facultatif : nombre d'élément à retourner
 *	@param int $cls_id Optionnel, identifiant d'une classe d'objet sur lequel porte les échanges
 *	@param int $obj_id_0 Optionnel, identifiant de l'objet
 *	@param int $obj_id_1 Optionnel, identifiant de l'objet (si composé)
 *	@param int $obj_id_2 Optionnel, identifiant de l'objet (si composé)
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function ch_conversations_get( $id=0, $offset=null, $limit=null, $cls_id=0, $obj_id_0=0, $obj_id_1=0, $obj_id_2=0 ){

	$params = array();

	if( $id ){
		$params['_id'] = $id;
	}
	if( $offset ){
		$params['skip'] = $offset;
	}
	if( $limit ){
		$params['limit'] = $limit;
	}
	if( $cls_id ){
		$params['ch_cls_id'] = $cls_id;
	}
	if( $obj_id_0 ){
		$params['ch_obj_id_0'] = $obj_id_0;
	}
	if( $obj_id_1 ){
		$params['ch_obj_id_1'] = $obj_id_1;
	}
	if( $obj_id_2 ){
		$params['ch_obj_id_2'] = $obj_id_2;
	}

	$params['sort'] =  array(array("created" => "desc"));

	$results = CouchDB::create(_COUCHDB_CHAT_DB_NAME)->getAll(CLS_CHAT_CONVERSATIONS, $params);
	if( !$results ){
		return false;
	}

	return $results;
}

/** Cette fonction permet d'ajouter une ligne dans les conversations
 *	@param array $followers Obligatoire, liste de usr_id concerné par la conversation
 *	@param int $cls_id Facultatif, id de la classe
 *	@param int $obj_id_0 Facultatif, id de l'objet
 *	@param int $obj_id_1 Facultatif, id de l'objet
 *	@param int $obj_id_2 Facultatif, id de l'objet
 *	@param string $date_created Facultatif, date de création de la conversation
 *
 *	@return int l'identifiant de la conversation en cas de succès
 *	@return bool False en cas d'échec
 */
function ch_conversations_add( $followers=array(), $cls_id=0, $obj_id_0='0', $obj_id_1='0', $obj_id_2='0', $date_created=null){

	if( !is_numeric($cls_id) ){
		return false;
	}
	if( !is_array($followers) ){
		return false;
	}
	foreach($followers as $flw){
		if( !gu_users_exists($flw) ){
			return false;
		}
	}

	// check si une conversation n'a pas déjà été lancé pour cet objet
	if( $cls_id > 0 && $obj_id_0 ){
		$exists = ch_conversations_get( 0, null, null, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2 );
		if( $exists && sizeof($exists)>0 ){
			$first = $exists[0];

			// on ne fait que une mise à jour des followers
			foreach($followers as $flw){
				if( !ch_conversations_add_followers($first['_id'], $flw) ){
					return false;
				}
			}
			return $first['_id'];
		}
	}

	global $config;

	$date_created = $date_created && isdateheure($date_created) ? $date_created : dateheureparse(date('Y-m-d H:i:s'));

	$values = array();

	// ajout des datas normal
	$values['ch_cls_id'] = $cls_id;
	$values['ch_obj_id_0'] = $obj_id_0;
	$values['ch_obj_id_1'] = $obj_id_1;
	$values['ch_obj_id_2'] = $obj_id_2;
	$values['ch_date_created'] = $date_created;
	$values['messages'] = array();

	// récupère ici les personnes qui devrait être concerné par la conversation en fonction de l'élément
	if( $cls_id ){
		switch ($cls_id) {
			case CLS_CALLS:
				$call = gcl_calls_get_by_view($obj_id_0);
				if (is_array($call) ) {
					if( isset($call['gcl_author_id']) ){
						$followers[] = $call['gcl_author_id'];
					}
				}
				break;
			case CLS_REPORT:
				$reports = rp_reports_get($obj_id_0);
				if ($reports && ria_mysql_num_rows($reports)) {
					while( $report = ria_mysql_fetch_array($reports) ){
						$followers[] = $report['author_id'];
					}
				}
				break;
			case CLS_NOTIFICATIONS:
				$notification = nt_notifications_get($obj_id_0);
				if ($notification) {
					if( $notification['nt_author_id'] ){
						$followers[] = $notification['nt_author_id'];
					}
					$followers = array_merge($followers, $notification['users']);
				}
				break;
		}
	}

	$values['followers'] = $followers;

	$results = CouchDB::create(_COUCHDB_CHAT_DB_NAME)->add(CLS_CHAT_CONVERSATIONS, $values);

	if( isset($results['ok']) ){
		return $results['id'];
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}
	return false;
}

/** Cette fonction permet l'ajout d'observateur sur les converations
 *	@param int $id Facultatif, id de la conversation
 *	@param int $usr_id Facultatif, id de l'utilisateur à ajouter
 *
 *	@return bool False en cas d'échec sinon True
 */
function ch_conversations_add_followers( $id, $usr_id ){
	if( !gu_users_exists($usr_id) ){
		return false;
	}

	$convers = CouchDB::create(_COUCHDB_CHAT_DB_NAME)->get(CLS_CHAT_CONVERSATIONS, $id);
	if( !$convers ){
		return false;
	}

	if( !isset($convers['followers']) ){
		$convers['followers'] = array();
	}

	$convers['followers'][] = $usr_id;

	// évite les doublons
	$convers['followers'] = array_unique($convers['followers']);

	if( CouchDB::create(_COUCHDB_CHAT_DB_NAME)->update(CLS_CHAT_CONVERSATIONS, $id, $convers) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' erreur de mise à jour '.print_r($convers, true));
	}

	return false;
}

/** Cette fonction permet de supprimer une conversation. La suppression est réalisée de façon virtuelle (corbeille)
 * 	@param int $id Obligatoire, identifiant de la conversation à supprimer
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function ch_conversations_del( $id ){
	if( !ch_conversations_exists( $id ) ){
		return false;
	}

	$results = CouchDB::create(_COUCHDB_CHAT_DB_NAME)->delete(CLS_CHAT_CONVERSATIONS, $id);

	if( isset($results['ok']) ){
		return true;
	}else{
		error_log(__FILE__.':'.__LINE__.' '.print_r($results, true));
	}

	return false;
}

/**	Cette fonction permet l'envoi sur les serveurs Google/Apple des messages
 *	@param int $conv_id Obligatoire, Identifiant de la conversation
 *	@param $unique_key Obligatoire, Identifiant du message
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function ch_messages_send($conv_id, $unique_key){
	$conversation = CouchDB::create(_COUCHDB_CHAT_DB_NAME)->get(CLS_CHAT_CONVERSATIONS, $conv_id);
	if( !$conversation ) {
		return false;
	}

	if( !isset($conversation['followers']) || !sizeof($conversation['followers']) ){
		error_log(__FILE__.':'.__LINE__." conversation sans utilisateurs : ".$conv_id);
		return false;
	}

	$sending = array(); // tableau de donnée pour la liste des envois effectué

	foreach( $conversation['followers'] as $usr ){
		if( is_numeric($usr) ){

			// récupère l'ensemble des tablettes pour chaque utilisateur
			$rdev = dev_devices_get(0, $usr, '', -1, '=', false, false, true);
			if( $rdev && ria_mysql_num_rows($rdev) ){
				while( $dev = ria_mysql_fetch_assoc($rdev) ){

					// on ne tiens pas compte des potentiels erreurs d'envoi
					dev_send_notifications($dev['id'], '', '', array('cls_id'=> CLS_CHAT_MESSAGES, 'conv_id' => $conversation['_id'], 'msg_id'=>$unique_key));
				}
			}

		}
	}

	return true;
}

/** Cette fonction permet de récupèrer une liste de messages
 *	@param int $id Facultatif, identifiant de la conversation
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function ch_messages_get( $id=0 ){
	$results = CouchDB::create(_COUCHDB_CHAT_DB_NAME)->get(CLS_CHAT_CONVERSATIONS, $id);
	if( !$results ){
		return false;
	}

	return $results['messages'];
}

/** Cette fonction permet d'ajouter une ligne dans les conversations
 *	@param int $conv_id Obligatoire, identifiant d'une conversation
 *	@param int $author_id Obligatoire, identifiant d'un utilisateur
 *	@param $unique_key Obligatoire, clé unique en fonction de l'appareil et de l'heure, important de la donnée pour les suppressions par exemple
 *	@param $content Facultatif, id de l'objet
 *	@param string $date_created Facultatif, date de création de la conversation
 *
 *	@return int l'identifiant du message en cas de succès
 *	@return bool False en cas d'échec
 */
function ch_messages_add( $conv_id, $author_id, $unique_key, $content='', $date_created=null ){
	if( !gu_users_exists($author_id) ){
		return false;
	}
	if( !ch_conversations_exists($conv_id) ){
		return false;
	}
	if( trim($content)=='' ){
		return false;
	}
	$date_created = $date_created && isdateheure($date_created) ? $date_created : dateheureparse(date('Y-m-d H:i:s'));

	// récupère le détail de la conversation
	$convers = CouchDB::create(_COUCHDB_CHAT_DB_NAME)->get(CLS_CHAT_CONVERSATIONS, $conv_id);
	if( !$convers ){
		return false;
	}

	$rauthor = gu_users_get($author_id);
	if( !$rauthor ) return false;
	$author = ria_mysql_fetch_assoc($rauthor);

	$values = array();
	$values['unique_id'] = $unique_key;
	$values['author_id'] = $author_id;
	$values['author_email'] = $author['email'];
	$values['author_name'] = trim($author['adr_firstname'].' '.$author['adr_lastname'].' '.$author['society']);
	$values['content'] = $content;
	$values['date_created'] = $date_created;

	// ajout de l'auteur aux followers
	$convers['followers'][] = $author_id;
	$convers['followers'] = array_unique($convers['followers']);
	$convers['messages'][] = $values;

	if( CouchDB::create(_COUCHDB_CHAT_DB_NAME)->update(CLS_CHAT_CONVERSATIONS, $conv_id, $convers) ){

		// lance l'envoi des notifications push
		ch_messages_send($conv_id, $unique_key);

		return true;
	}

	return false;
}

/** Cette fonction permet de supprimer un message d'un message
 *	@param int $conv_id Obligatoire, identifiant de la conversation
 *	@param $unique_key Obligatoire, identifiant d'un message
 *
 *	@return int l'identifiant du message en cas de succès
 *	@return bool False en cas d'échec
 */
function ch_messages_del( $conv_id, $unique_key ){
	if( !ch_conversations_exists($conv_id) ){
		return false;
	}

	$result = CouchDB::create(_COUCHDB_CHAT_DB_NAME)->get(CLS_CHAT_CONVERSATIONS, $conv_id);

	if( $result ){
		$final_msg = array();
		foreach( $result['messages'] as $msg ){
			if( $msg['id'] != $unique_key ){
				$final_msg[] = $msg;
			}
		}
		$result['messages'] = $final_msg;

		if( CouchDB::create(_COUCHDB_CHAT_DB_NAME)->update(CLS_CHAT_CONVERSATIONS, $result) ){
			return true;
		}
	}

	return false;
}

/// @}

// \endcond
