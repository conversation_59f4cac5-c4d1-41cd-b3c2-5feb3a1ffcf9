Upgrade notes for SimpleSAMLphp 1.7
===================================

  * The attribute names generated by the twitter and facebook authentication sources have changed.
  * Several new options have been added to config.php, and some have been renamed.
    The old options should still work, but it is suggested that you look at the config.php file in config-templates, and compare it to your own.
  * There have been several changes to the internal API.
    Most of the changes will not be noticed by the application using SimpleSAMLphp.
    See the changelog for more details about the changes.
  * Relative redirects are no longer supported.
    If your application passes relative URL's to the `SimpleSAML_Utilities::redirect()`-function, it will no longer work.
    This also applies if you pass relative URL's to other functions that do redirects.
