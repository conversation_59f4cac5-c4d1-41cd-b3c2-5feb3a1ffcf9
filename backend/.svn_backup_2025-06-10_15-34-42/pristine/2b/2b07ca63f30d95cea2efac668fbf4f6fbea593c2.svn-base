<?php
/**
 * \defgroup api-reports-need_sync Synchronisation 
 * \ingroup api-reports
 * @{
 * \page api-reports-need_sync-upd Mise à jour 
 *
 * cette fonction modifie la demande de synchronisation sur les rapports de visites
 *
 *		\code
 *			PUT /reports/need_sync/
 *		\endcode
 *	
 * @param int $id Obligatoire, identifiant du rapport
 * @param bool $need_sync Obligatoire, besoin de syncrhoniser le rapport 
 *	
 * @return true si la modification s'est déroulée avec succès, false dans le cas contraire
 * @}
*/
switch ($method) {

	case 'upd':

		if( !isset($_REQUEST['id']) || !isset($_REQUEST['need_sync']) || !is_numeric($_REQUEST['id']) ){
			throw new Exception('Paramètres invalide');
		}

		if( !rp_reports_set_need_sync($_REQUEST['id'], $_REQUEST['need_sync']) ){
			throw new Exception("Erreur lors de la modification du need_sync sur le rapport.");
		}

		if( $is_sync && !rp_reports_set_is_sync($_REQUEST['id'], true) ){
			throw new Exception('Erreur lors de la modification du is_sync sur le rapport.');
		}

		$result = true;

		break;

}