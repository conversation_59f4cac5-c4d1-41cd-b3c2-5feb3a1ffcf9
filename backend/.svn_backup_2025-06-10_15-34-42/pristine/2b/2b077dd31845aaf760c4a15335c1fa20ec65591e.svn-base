<?php

	/**	\file rights.php
	 *	Cette page permet la gestion des droits globaux sur l'instance RiaShop. Son utilisation est réservée à l'interne.
	 */

    // Si le params existe alors on l'utilise sinon on met le wst_id de la config
    $paramExists = isset($_GET['wst_id']) && is_numeric($_GET['wst_id']) && $_GET['wst_id'] > 0;

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Mes options'), '/admin/options/index.php' )
		->push( _('Gestion des droits d\'accès') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Gestion des droits d\'accès') .' - ' . _('Mes options'));

    if( !$config['USER_RIASTUDIO'] && $_SESSION['usr_email'] != '<EMAIL>' ){
        header('HTTP/1.0 403 Forbidden');
        exit;
    }

    //Sauvegarde
    if( isset($_POST['save-admin-rights']) ){
		if(!isset($_POST['rgh'])){
			wst_website_rights_del($config['tnt_id'], $paramExists ? $_GET['wst_id'] : $config['wst_id']);
		}else{
            wst_website_rights_add($config['tnt_id'], $paramExists ? $_GET['wst_id'] : $config['wst_id'], $_POST['rgh']);
        }

        //Recharge le cache
        gu_users_load_admin_rights( true, true );
        gu_users_load_admin_rights( false, true );

    }

    require_once('admin/skin/header.inc.php');
?>

<div class="notice"><?php print _("Cette interface n'est visible que par les super-administrateurs."); ?></div>

<h2><?php print _("Gestion des droits"); ?></h2>

<?php
    $action = "rights.php";
    if ($paramExists) {
        $action .= "?wst_id=".$_GET['wst_id'];
    }
?>
<form id="form_rights" action=<?php echo $action; ?> method="post">

    <h3><?php print _("Importer les droits d'un autre site"); ?></h3>

    <select id="wst">
        <option></option><?php
        $r_tnt = tnt_tenants_get();
        while( $tnt = ria_mysql_fetch_assoc($r_tnt) ){
            $r_wst = wst_websites_get( 0, false, $tnt['id']);
            while( $wst = ria_mysql_fetch_assoc($r_wst) ){ ?>
                <option value="<?php print $wst['id']; ?>" tnt="<?php print $tnt['id']; ?>"><?php print htmlspecialchars( $tnt['name'].': '.$wst['name'] ); ?></option><?php
            }
        } ?>
    </select>
    <input type="button" id="import" value="<?php print _('Importer'); ?>" /><br/>

    <h3><?php print _("Liste des droits d'accès"); ?></h3>

    <div class="stats-menu">
	<div class="ria-admin-ui-filters">
		<div id="riadatepicker"></div>
		<?php print view_websites_selector( $paramExists ? $_GET['wst_id'] : $config['wst_id'] , true, 'riapicker', true, 'Tous les sites', false, false );
		?>
	</div>
	<div class="clear"></div>
</div>

    <?php print view_admin_rights($config['tnt_id'], $paramExists ? $_GET['wst_id'] : $config['wst_id']); ?>
</form>


<?php
	require_once('admin/skin/footer.inc.php');
?>
