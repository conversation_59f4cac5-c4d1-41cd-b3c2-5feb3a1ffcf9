<?php
require_once('db.inc.php');
require_once('images.inc.php');
require_once('documents.inc.php');

/** \defgroup model_doc_images Images associées à un document
 *	\ingroup model_images
 *	Ce module comprend les fonctions nécessaires à la gestion des images de documents.
 *	Une ou plusieurs images peuvent être associées à chacun des documents.
 *
 *	Cette fonction exploite les directives de configuration suivantes :
 *		- $config['img_dir'] : dossier d'enregistrement des images.
 *		- $config['img_sizes'] : tableau des dimensions de vignettes. Chaque entrée de ce tableau doit contenir les clés width et height.
 *
 *	Le tableau des tailles de vignettes est utilisé comme ceci :
 *	\code
 *		foreach( $config['img_sizes'] as $size )
 *			print $size['width'].' '.$size['height'];
 *	\endcode
 *
 *	Les tailles de vignettes sont utilisées comme des maximums. La génération de vignette conserve les proportions des images.
 *	Il est possible d'utiliser autant de tailles de vignettes que souhaité, mais celle-ci doivent être triées par ordre croissant.
 *
 *	@{
 */

/** Retourne l'ensemble des fichiers images attachés à un document donné.
 *	Les paramètres $limit et $offset sont ignoré si un identifiant d'image est donné en paramètre
 *
 *	@param int $doc_id Optionnel, identifiant du document
 *	@param int $img_id Optionnel, identifiant de l'image
 *	@param int $limit Optionnel, nombre maximum de résultats à retourner (profondeur maximum à explorer)
 *	@param int $offset Optionnel, offset à partir duquel démarrer le résultat
 *
 *	@return resource Un résultat de requête MySQL comprenant la colonne suivante :
 *			- id : identifiant de l'image
 *          - pos : la position de l'image
 *          - doc_id : l'identifiant du document de l'image
 */
function doc_images_get( $doc_id=0, $img_id=0, $limit=0, $offset=0 ){
	if( !is_numeric($doc_id) || $doc_id<=0 ){
		return false;
	}

	if( !is_numeric($img_id) ){
		return false;
	}

	if( !is_numeric($limit) ){
		return false;
	}

	if( !is_numeric($offset) ){
		return false;
	}

	global $config;

	$sql = '
        select imo_img_id as id, imo_pos as pos, imo_obj_id_0 as doc_id, imo_alt as alt, imo_is_main as is_main
        from img_images_objects
        where imo_tnt_id='.$config['tnt_id'].'
            and imo_obj_id_0='.$doc_id.'
            and imo_cls_id = '.CLS_DOCUMENT.'
            and imo_date_deleted is null
    ';

	if( $img_id > 0 ){
		$sql .= ' and imo_img_id='.$img_id.' limit 0,1';
	}else{
		$sql .= ' order by imo_pos asc';

		if( $limit > 0 ){
			if( $offset < 0 ){
				$offset = 0;
			}

			$sql .= ' limit '.$offset.', '.$limit;
		}
    }

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer l'identifiant de l'image principale d'un document
 *	@param int $doc_id Obligatoire, identifiant d'un document
 *
 *	@return int|false L'identifiant de la première image, 0 si aucune image ou si le paramètre obligatoire est omis ou faux
 */
function doc_images_get_main( $doc_id ){
	$img_id = 0;

	if( !is_numeric($doc_id) || $doc_id<=0 ){
		return $img_id;
	}

	global $config;

    $r_img = doc_images_get( $doc_id, 0, 1 );

	if( $r_img && ria_mysql_num_rows($r_img) ){
		$image = ria_mysql_fetch_assoc( $r_img );
		$img_id = $image['id'];
	}

	return $img_id;
}

/** Permet l'upload d'une image qui sera ensuite ajoutée aux images du document
 *	@param int $doc_id Obligatoire, Identifiant du document.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 */
function doc_images_upload( $doc_id, $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return doc_images_add( $doc_id, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );
}

/** Permet l'ajout d'un fichier image à un document. Cette fonction est notamment utile lors d'importations.
 *
 *	@param int $doc_id Obligatoire, identifiant du document.
 *	@param string $filename Obligatoire, nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source.
 *	@param bool $check_main Facultatif, si la cible n'a pas d'image principale, cette image le deviendra
 *	@param string $alt Facultatif, texte alternatif de l'image
 *
 *	@return int|bool L'identifiant attribué à l'image en cas de succès, false en cas d'échec.
 *
 */
function doc_images_add( $doc_id, $filename, $srcname='', $check_main=true, $alt=null ){

    if( !is_numeric($doc_id) || $doc_id <= 0 ){
		return false;
	}

	if( trim($filename) == '' ){
		return false;
	}

	// création de l'image
	$id = img_images_add( $filename, $srcname, true );
	if( !$id ){
		return false;
	}

	// création du lien
	if( !doc_images_add_existing( $doc_id, $id, $check_main, $alt) ){
		return false;
	}

	return $id;
}

/**	Cette fonction permet la réutilisation d'une image existante pour un document.
 *	@param int $doc_id Obligatoire, Identifiant du document.
 *	@param int $img_id Obligatoire, Identifiant du fichier image.
 *	@param bool $check_main Facultatif, si la cible n'a pas d'image principale, celle-ci va le devenir. Valeur par défaut : false.
 *	@param string $alt Facultatif, texte alternatif de l'image
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_images_add_existing( $doc_id, $img_id, $check_main=false, $alt=null ){
    if( !doc_documents_exists($doc_id) ) return false;
    if( !img_images_exists($img_id) ) return false;

    $main_img = doc_images_get_main( $doc_id );
	if( is_numeric($main_img) && $main_img == $img_id ){
		return true;
    }

    // récupère la position de la dernière image (+1)
    $pos = img_images_objects_get_max_pos(0, $doc_id, 0, 0, 0, 0, CLS_DOCUMENT, null, false);

    $is_main = $check_main && !is_numeric($main_img);

    $pos = $is_main ? 0 : (is_null($pos) ? 0 : $pos + 1);
    $r_img_obj = img_images_objects_get(0, $doc_id, 0, 0, 0, $img_id, CLS_DOCUMENT, null, true);
    $img_obj_id = false;
    if( !$r_img_obj || !ria_mysql_num_rows($r_img_obj) ){
        $img_obj_id = img_images_objects_add(CLS_DOCUMENT, $img_id, null, $doc_id, 0, 0, $pos, null, null, $is_main);
    }else{
        $img_obj_id = ria_mysql_result($r_img_obj, 0, 'id');
        img_images_objects_set_is_main($is_main, 0, $doc_id, 0, 0, 0, $img_id, CLS_DOCUMENT);
        img_images_objects_set_pos($pos, 0, $doc_id, 0, 0, 0, $img_id, CLS_DOCUMENT);
    }

    if( !$img_obj_id ){
        return false;
    }

	return img_images_count_update($img_id);
}

/** Permet la suppression d'une ou plusieurs images de documents.
 *
 *	@param int $doc_id Obligatoire, Identifiant du document.
 *	@param int $img_id Optionnel, identifiant de l'image à supprimer. Si ce paramètre est omis, toutes les images du document seront supprimées.
 *
 */
function doc_images_del( $doc_id, $img_id=0 ){
    if( !is_numeric($doc_id) && $doc_id <= 0) return false;
	if( !is_numeric($img_id) && $img_id < 0 ) return false;

    global $config;

	$r_image = doc_images_get($doc_id, $img_id);
	if( !$r_image ){
		return false;
    }

	if( !ria_mysql_num_rows($r_image) ){
		return true;
    }

	// recalcule la position des images secondaires au delà de celle à supprimer
	$sql = '
		select imo_pos
		from img_images_objects
        where imo_tnt_id = '.$config['tnt_id'].'
            and imo_cls_id = '.CLS_DOCUMENT.'
            and imo_obj_id_0 = '.$doc_id.'
            and imo_date_deleted is null'.'
            and imo_img_id = '.$img_id.'
            and imo_publish = 1
			and imo_is_main = 0
    ';

	if( $r_pos = ria_mysql_query($sql) ){
		if( ria_mysql_num_rows($r_pos) ){
			$pos = ria_mysql_result($r_pos, 0, 0);
			$sql = '
				update img_images_objects
				set imo_pos = imo_pos-1
                where imo_tnt_id = '.$config['tnt_id'].'
                    and imo_cls_id = '.CLS_DOCUMENT.' and imo_date_deleted is null
                    and imo_is_main = 0
                    and imo_obj_id_0 = '.$doc_id.'
                    and imo_pos > '.$pos.'
			';
			ria_mysql_query($sql);
		}
	}

	// supprime le lien entre image et produit
	$sql = '
		update img_images_objects
			set imo_date_deleted = now()
        where imo_tnt_id = '.$config['tnt_id'].'
            and imo_cls_id = '.CLS_DOCUMENT.'
            and imo_date_deleted is null
            and imo_is_main = 0
            and imo_obj_id_0 = '.$doc_id.'
    ';

	if( $img_id > 0 ){
		$sql .= ' and imo_img_id = '.$img_id;
	}

	if( !ria_mysql_query($sql) ){
		return false;
	}

	if( $img_id > 0 ){
		return img_images_count_update($img_id);
	}

	return img_images_count_update(0, $doc_id);
}
///@}

/** \defgroup model_doc_types_images Images associées à un type de document
 *	\ingroup model_images
 *	Ce module comprend les fonctions nécessaires à la gestion des images de type de documents.
 *	Une ou plusieurs images peuvent être associées à chacun des documents.
 *
 *	Cette fonction exploite les directives de configuration suivantes :
 *		- $config['img_dir'] : dossier d'enregistrement des images.
 *		- $config['img_sizes'] : tableau des dimensions de vignettes. Chaque entrée de ce tableau doit contenir les clés width et height.
 *
 *	Le tableau des tailles de vignettes est utilisé comme ceci :
 *	\code
 *		foreach( $config['img_sizes'] as $size )
 *			print $size['width'].' '.$size['height'];
 *	\endcode
 *
 *	Les tailles de vignettes sont utilisées comme des maximums. La génération de vignette conserve les proportions des images.
 *	Il est possible d'utiliser autant de tailles de vignettes que souhaité, mais celle-ci doivent être triées par ordre croissant.
 *
 *	@{
 */

/** Retourne l'ensemble des fichiers images attachés à un type de document donné.
 *	Les paramètres $limit et $offset sont ignoré si un identifiant d'image est donné en paramètre
 *
 *	@param int $type_id Optionnel, identifiant du type de document
 *	@param int $img_id Optionnel, identifiant de l'image
 *	@param int $limit Optionnel, nombre maximum de résultats à retourner (profondeur maximum à explorer)
 *	@param int $offset Optionnel, offset à partir duquel démarrer le résultat
 *
 *	@return resource Un résultat de requête MySQL comprenant la colonne suivante :
 *			- id : identifiant de l'image
 *          - pos : la position de l'image
 *          - type_id : l'identifiant du type de document de l'image
 */
function doc_types_images_get( $type_id=0, $img_id=0, $limit=0, $offset=0 ){
	if( !is_numeric($type_id) || $type_id<=0 ){
		return false;
	}

	if( !is_numeric($img_id) ){
		return false;
	}

	if( !is_numeric($limit) ){
		return false;
	}

	if( !is_numeric($offset) ){
		return false;
	}

	global $config;

	$sql = '
        select imo_img_id as id, imo_pos as pos, imo_obj_id_0 as type_id, imo_alt as alt, imo_is_main as is_main
        from img_images_objects
        where imo_tnt_id='.$config['tnt_id'].'
            and imo_obj_id_0='.$type_id.'
            and imo_cls_id = '.CLS_TYPE_DOCUMENT.'
            and imo_date_deleted is null
    ';

	if( $img_id > 0 ){
		$sql .= ' and imo_img_id='.$img_id.' limit 0,1';
	}else{
		$sql .= ' order by imo_pos asc';

		if( $limit > 0 ){
			if( $offset < 0 ){
				$offset = 0;
			}

			$sql .= ' limit '.$offset.', '.$limit;
		}
    }

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer l'identifiant de l'image principale d'un type de document
 *	@param int $type_id Obligatoire, identifiant d'un type de document
 *
 *	@return int|bool L'identifiant de la première image, 0 si aucune image ou si le paramètre obligatoire est omis ou faux
 */
function doc_types_images_get_main( $type_id ){
	$img_id = 0;

	if( !is_numeric($type_id) || $type_id<=0 ){
		return $img_id;
	}

	global $config;

    $r_img = doc_types_images_get( $type_id, 0, 1 );

	if( $r_img && ria_mysql_num_rows($r_img) ){
		$image = ria_mysql_fetch_assoc( $r_img );
		$img_id = $image['id'];
	}

	return $img_id;
}

/** Permet l'upload d'une image qui sera ensuite ajoutée aux images du type de document
 *	@param int $type_id Identifiant du type de document.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 */
function doc_types_images_upload( $type_id, $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return doc_types_images_add( $type_id, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );
}

/** Permet l'ajout d'un fichier image à un type de document. Cette fonction est notamment utile lors d'importations.
 *
 *	@param int $type_id Identifiant du type de document.
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source.
 *	@param bool $check_main Facultatif, si la cible n'a pas d'image principale, celle-ci le deviendra. true par défaut.
 *	@param string $alt Facultatif, texte alternatif de l'image
 *
 *	@return int|bool L'identifiant attribué à l'image en cas de succès, false en cas d'échec.
 *
 */
function doc_types_images_add( $type_id, $filename, $srcname='', $check_main=true, $alt=null ){
    if( !is_numeric($type_id) || $type_id <= 0 ){
		return false;
	}

	if( trim($filename) == '' ){
		return false;
	}

	// création de l'image
	$id = img_images_add( $filename, $srcname, true );
	if( !$id ){
		return false;
	}

	// création du lien
	if( !doc_types_images_add_existing( $type_id, $id, $check_main, $alt) ){
		return false;
	}

	return $id;
}

/**	Cette fonction permet la réutilisation d'une image existante pour un type de document.
 *	@param int $type_id Obligatoire, Identifiant du type de document.
 *	@param int $img_id Obligatoire, Identifiant du fichier image.
 *	@param bool $check_main Facultatif, si la cible n'a pas d'image principale, celle-ci le deviendra. true par défaut.
 *	@param string $alt Facultatif, texte alternatif de l'image
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_types_images_add_existing( $type_id, $img_id, $check_main=false, $alt=null ){
    if( !doc_types_exists($type_id) ) return false;
    if( !img_images_exists($img_id) ) return false;

    $main_img = doc_images_get_main( $type_id );
	if( is_numeric($main_img) && $main_img == $img_id ){
		return true;
    }

    // récupère la position de la dernière image (+1)
    $pos = img_images_objects_get_max_pos(0, $type_id, 0, 0, 0, 0, CLS_TYPE_DOCUMENT, null, false);

    $is_main = $check_main && !is_numeric($main_img);

    $pos = $is_main ? 0 : (is_null($pos) ? 0 : $pos + 1);
    $r_img_obj = img_images_objects_get(0, $type_id, 0, 0, 0, $img_id, CLS_TYPE_DOCUMENT, null, true);
    $img_obj_id = false;
    if( !$r_img_obj || !ria_mysql_num_rows($r_img_obj) ){
        $img_obj_id = img_images_objects_add(CLS_TYPE_DOCUMENT, $img_id, null, $type_id, 0, 0, $pos, null, null, $is_main);
    }else{
        $img_obj_id = ria_mysql_result($r_img_obj, 0, 'id');
        img_images_objects_set_is_main($is_main, 0, $type_id, 0, 0, 0, $img_id, CLS_TYPE_DOCUMENT);
        img_images_objects_set_pos($pos, 0, $type_id, 0, 0, 0, $img_id, CLS_TYPE_DOCUMENT);
    }

    if( !$img_obj_id ){
        return false;
    }

	return img_images_count_update($img_id);
}

/** Permet la suppression d'une ou plusieurs images de documents.
 *
 *	@param int $type_id Identifiant du type de document.
 *	@param int $img_id Optionnel, identifiant de l'image à supprimer. Si ce paramètre est omis, toutes les images du type de document seront supprimées.
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_types_images_del( $type_id, $img_id=0 ){
    if( !is_numeric($type_id) && $type_id <= 0) return false;
	if( !is_numeric($img_id) && $img_id < 0 ) return false;

    global $config;

	$r_image = doc_types_images_get($type_id,$img_id);
	if( !$r_image ){
		return false;
	}

	if( !ria_mysql_num_rows($r_image) ){
		return true;
    }

	// recalcule la position des images secondaires au delà de celle à supprimer
	$sql = '
		select imo_pos
		from img_images_objects
        where imo_tnt_id = '.$config['tnt_id'].'
            and imo_cls_id = '.CLS_TYPE_DOCUMENT.'
            and imo_obj_id_0 = '.$type_id.'
            and imo_date_deleted is null'.'
            and imo_img_id = '.$img_id.'
            and imo_publish = 1
			and imo_is_main = 0
    ';

	if( $r_pos = ria_mysql_query($sql) ){
		if( ria_mysql_num_rows($r_pos) ){
			$pos = ria_mysql_result($r_pos, 0, 0);
			$sql = '
				update img_images_objects
				set imo_pos = imo_pos-1
                where imo_tnt_id = '.$config['tnt_id'].'
                    and imo_cls_id = '.CLS_TYPE_DOCUMENT.' and imo_date_deleted is null
                    and imo_is_main = 0
                    and imo_obj_id_0 = '.$type_id.'
                    and imo_pos > '.$pos.'
			';
			ria_mysql_query($sql);
		}
	}

	// supprime le lien entre image et produit
	$sql = '
		update img_images_objects
			set imo_date_deleted = now()
        where imo_tnt_id = '.$config['tnt_id'].'
            and imo_cls_id = '.CLS_TYPE_DOCUMENT.'
            and imo_date_deleted is null
            and imo_is_main = 0
            and imo_obj_id_0 = '.$type_id.'
	';
	if( $img_id > 0 ){
		$sql .= ' and imo_img_id = '.$img_id;
	}

	if( !ria_mysql_query($sql) ){
		return false;
	}

	if( $img_id > 0 ){
		return img_images_count_update($img_id);
	}

	return img_images_count_update(0, $type_id);
}
///@}