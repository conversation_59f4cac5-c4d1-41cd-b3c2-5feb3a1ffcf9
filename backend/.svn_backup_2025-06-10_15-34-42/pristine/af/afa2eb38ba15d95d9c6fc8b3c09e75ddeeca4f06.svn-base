<?php
// \cond onlyria

require_once('define.inc.php');
require_once('users.inc.php');
require_once('flow/notifications.inc.php');

/** \defgroup model_reports Rapports de visite
 *	\ingroup yuto crm
 *	Ce module comprend les fonctions nécessaires à la gestion des rapports de visite pour l'application Yuto
 *	@{
 */

/** Cette fonction permet de tester l'existence d'un type de rapport
 * 	@param int $type_id Obligatoire : identifiant du type de rapport
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_types_exists( $type_id ){
	if( !is_numeric($type_id) || $type_id <= 0 ) return false;
	global $config;

	$sql = 'select 1 from rp_report_types where (rpt_tnt_id=0 or rpt_tnt_id='.$config['tnt_id'].') and rpt_date_deleted is null and rpt_id='.$type_id;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction permet d'ajouter un type de rapport
 *	@param string $type_name Obligatoire, Nom du rapport à ajouter
 *	@param int $rtg_id Obligatoire, Identifiant du groupe de rapport
 *
 *	@return int L'identifiant du nouveau rapport ou False en cas d'erreur
 */
function rp_types_add( $type_name, $rtg_id ){
	if( !is_string($type_name) || sizeof(trim($type_name)) <= 0 ) return false;
	if( !is_numeric($rtg_id) || $rtg_id <= 0 || !rp_types_groups_exists($rtg_id) ) return false;

	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'rpt_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'rpt_name';
	$values[] = '\''.ria_mysql_escape_string($type_name).'\'';

	$fields[] = 'rpt_rtg_id';
	$values[] = $rtg_id;

	$fields[] = 'rpt_date_created';
	$values[] = 'now()';

	$sql = 'insert into rp_report_types ('.implode( ',', $fields ).') values ('.implode( ',', $values ).')';


	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet de mettre à jour un type d rapport
 *	@param int $type_id Obligatoire : identifiant du type de rapport
 *	@param $type_name Obligatoire : nom du rapport
 *	@param $rtg_id Obligatoire : identifiant du groupe de rapport
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function rp_types_update( $type_id, $type_name, $rtg_id=0 ){
	if( !is_numeric($type_id) || $type_id <= 0 || !rp_types_exists($type_id) ) return false;
	if( !is_string($type_name) || sizeof(trim($type_name)) <= 0 ) return false;
	if( !is_numeric($rtg_id)) return false;

	global $config;

	$statements = array();
	$statements[] = 'rpt_name = \''.ria_mysql_escape_string($type_name).'\'';

	if ( $rtg_id > 0 && rp_types_groups_exists($rtg_id) ) {
		$statements[] = 'rpt_rtg_id = '.$rtg_id;
	}


	$sql = 'update rp_report_types set '.implode(', ', $statements).' where rpt_id = ' . $type_id . ' and rpt_tnt_id = '.$config['tnt_id'];

	$res = ria_mysql_query($sql);

	if ($res) {
		rp_types_set_date_modified($type_id);
	}

	if( ria_mysql_errno() ){
		error_log('[rp_types_update] Echec du chargement : '.mysql_error()."\n".$sql);
	}

	return $res;
}

/** Cette fonction permet supprimer un type d rapport
 *	@param int $type_id Obligatoire : identifiant du type de rapport
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function rp_types_del( $type_id){
	if( !is_numeric($type_id) || $type_id <= 0 || !rp_types_exists($type_id) ) return false;
	global $config;

	$sql = '
		update rp_report_types set rpt_date_deleted=now()
		where rpt_tnt_id = '.$config['tnt_id'].'
			and rpt_id = '.$type_id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	// supression de tous les rapports de ce type
	$reports = rp_reports_get( 0, $type_id);
	while( $report = ria_mysql_fetch_assoc($reports) ){
		rp_reports_del($report['id']);
	}

	return true;
}

/** Cette fonction permet récupérer les types de rapport disponible
 * @param int $type_id Facultatif, identifiant d'un type de rapport
 * @param string $name Facultatif, nom du type de rapport
 * @param $case_sensitive Facultatif, filtre sur le nom sensible à la case ?
 *
 * @return resource Un résultat MySQL contenant :
 *				- id : identifiant du type
 *				- name : nom du type
 *				- rtg_id : identifiant du groupe (classements)
 */
function rp_types_get( $type_id=0, $name = false, $case_sensitive = true){
	if( !is_numeric($type_id) || $type_id < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select rpt_id as id, rpt_name as name, rpt_rtg_id as rtg_id, rpt_tnt_id as tnt_id
		from rp_report_types
		where (rpt_tnt_id=0 or rpt_tnt_id='.$config['tnt_id'].')
			and rpt_date_deleted is null
	';

	if( $type_id > 0 ){
		$sql .= ' and rpt_id='.$type_id;
	}

	if( $name !== false && trim($name) != '' ){
		if( $case_sensitive){
			$sql .= ' and rpt_name like \''.addslashes($name).'\'';
		} else {
			$sql .= ' and CAST(rpt_name AS CHAR CHARACTER SET utf8) COLLATE utf8_general_ci like \''.addslashes($name).'\'';
		}
	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('[rp_types_get] Echec du chargement : '.mysql_error()."\n".$sql);
	}

	return $res;
}

/** Cette fonction permet de récupérer le nom d'un type de rapport
 *	@param int $type_id Obligatoire, identifiant d'un type
 *	@return bool False si le paramètre est omis ou faux, sinon le nom du type de rapport
 */
function rp_types_get_name( $type_id ){
	if( !is_numeric($type_id) || $type_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select rpt_name
		from rp_report_types
		where (rpt_tnt_id=0 or rpt_tnt_id='.$config['tnt_id'].')
			and rpt_date_deleted is null
			and rpt_id='.$type_id;

	$res = ria_mysql_query($sql);
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['rpt_name'];
}

/**	Cette fonction force la mise à jour de la date de dernière modification d'un type de rapport.
 *	@param int $id Obligatoire, Identifiant ou tableau d'identifiants d'un type de rapport
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function rp_types_set_date_modified( $id ){

	$id = control_array_integer( $id );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		update rp_report_types
		set rpt_date_modified = now()
		where rpt_tnt_id = '.$config['tnt_id'].'
			and rpt_id in ('.implode(', ' , $id).')
	';

	return ria_mysql_query($sql);

}

/** Cette fonction permet de tester l'existence d'un type de note
 * 	@param $note_id Obligatoire : identifiant du type de note
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_type_notes_exists( $note_id ){
	if( !is_numeric($note_id) || $note_id < 0 ) return false;
	global $config;

	$sql = 'select 1 from rp_report_type_notes where (rptn_tnt_id=0 or rptn_tnt_id='.$config['tnt_id'].') and rptn_date_deleted is null and rptn_id='.$note_id;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction permet récupérer les types de note disponible
 *	@param int $id Facultatif, identifiant d'un type de note
 *	@param int $type_id Facultatif, identifiant d'un type de rapport
 *
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant du type
 *				- name : nom du type
 *				- type_id : identifiant du type de note
 */
function rp_type_notes_get( $id=0, $type_id=0 ){
	if( $id > 0 && !rp_type_notes_exists( $id ) ){
		return false;
	}
	if( $type_id > 0 && !rp_types_exists( $type_id ) ){
		return false;
	}

	global $config;

	$sql = '
		select rptn_id as id, rptn_name as name, rptn_rpt_id as type_id
		from rp_report_type_notes
		where (rptn_tnt_id=0 or rptn_tnt_id='.$config['tnt_id'].')
			and rptn_date_deleted is null
	';

	if( $id > 0 ){
		$sql .= ' and rptn_id='.$id;
	}

	if( $type_id > 0 ){
		$sql .= ' and rptn_rpt_id='.$type_id;
	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('[rp_type_notes_get] Echec du chargement : '.mysql_error()."\n".$sql);
	}

	return $res;
}

/** Cette fonction permet de récupérer les groupes de type de rapport
 * @param $rtg_id Facultatif, Identifiant d'un groupe de type de rapport
 *
 * @return resource Un résultat MySQL contenant :
 *				- id : identifiant du groupe
 *				- name : nom du groupe
 */
function rp_type_groups_get($rtg_id=0){
	if( $rtg_id > 0 && !rp_types_groups_exists( $rtg_id ) ){
		return false;
	}

	global $config;

	$sql = '
		select rtg_id as id, rtg_name as name
		from rp_report_type_groups where 1 ';

	if( $rtg_id > 0 ){
		$sql .= ' and rtg_id='.$rtg_id;
	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('[rp_type_groups_get] Echec du chargement : '.mysql_error()."\n".$sql);
	}

	return $res;
}

/** Cette fonction permet de tester l'existence d'un groupe de type de rapport
 * 	@param $rtg_id Obligatoire : identifiant du groupe de type
 *
 * @return bool True en cas de succès ou False si non
 */
function rp_types_groups_exists( $rtg_id ){
	if( !is_numeric($rtg_id) || $rtg_id < 0 ) return false;
	global $config;

	$sql = 'select 1 from rp_report_type_groups where rtg_id='.$rtg_id;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction permet de récupérer les modèls de données
 * @param $rpt_id Facultatif, Identifiant d'un type de rapport sur lequel filtrer
 * @param int $mdl_id Facultatif, Identifiant d'un modèle de données sur lequel filtrer
 *
 * @return resource Un résultat MySQL contenant :
 *				- rpt_id : identifiant du rapport
 *				- mdl_id : identifiant du modèle de données
 */
function rp_types_models_get($rpt_id=0, $mdl_id=0){
	if( $rpt_id > 0 && !rp_types_exists( $rpt_id ) ){
		return false;
	}
	if( $mdl_id > 0 && !fld_models_exists( $mdl_id ) ){
		return false;
	}

	global $config;

	$sql = '
		select rpm_rpt_id as rpt_id, rpm_mdl_id as mdl_id
		from rp_report_models
		where rpm_tnt_id = '.$config['tnt_id'];

	if( $rpt_id > 0 ){
		$sql .= ' and rpm_rpt_id='.$rpt_id;
	}

	if( $mdl_id > 0 ){
		$sql .= ' and rpm_mdl_id='.$mdl_id;
	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('[rp_types_models_get] Echec du chargement : '.mysql_error()."\n".$sql);
	}

	return $res;
}

/** Cette fonction permet de mettre à jour les models associés à un type de rapport
 * @param $rpt_id Obligatoire, Identifiant d'un type de rapport
 * @param $mdl_ids Facultatif, identifiant modèle ou tableau d'identifiants à insérer
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function rp_type_models_update( $rpt_id, $mdl_ids=array() ){
	if( $rpt_id > 0 && !rp_types_exists( $rpt_id ) ){
		return false;
	}
	if( is_array($mdl_ids) && !count($mdl_ids) ){
		$mdl_ids = null;
	}else{
		$mdl_ids = control_array_integer( $mdl_ids, false );
		if( $mdl_ids === false ){
			return false;
		}
	}

	global $config;

	$sql = 'delete from rp_report_models where rpm_tnt_id = ' . $config['tnt_id'] . ' and rpm_rpt_id = ' . $rpt_id;
	$res = ria_mysql_query($sql);

	if( is_array($mdl_ids) && count($mdl_ids) ){
		$sql = 'insert into rp_report_models (rpm_tnt_id, rpm_rpt_id, rpm_mdl_id) values';

		for ($i=0; $i <sizeof($mdl_ids) ; $i++) {
			if (!fld_models_exists($mdl_ids[$i])) continue;
			$sql .= ' ('.$config['tnt_id'].', '.$rpt_id.', '.$mdl_ids[$i].' )';

			if ($i+1 < sizeof($mdl_ids)) {
				$sql .= ',';
			}
		}
	}

	$res = ria_mysql_query($sql);

	if ($res) {
		rp_types_set_date_modified($rpt_id);
	}
	if( ria_mysql_errno() ){
		error_log('[rp_type_models_update] Echec du chargement : '.mysql_error()."\n".$sql);
	}

	return $res;
}

/** Cette fonction permet de tester l'existence d'un rapport
 * 	@param int $id Obligatoire : identifiant du rapport
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_reports_exists( $id ){
	if( !is_numeric($id) || $id < 0 ) return false;
	global $config;

	$sql = 'select 1 from rp_reports where rpr_tnt_id='.$config['tnt_id'].' and rpr_date_deleted is null and rpr_id='.$id;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction permet récupérer les informations sur les rapports de visite.
 * 	Par défaut, la liste est triée par date de création décroissante.
 *
 *	@param int $id Facultatif, identifiant d'un rapport
 *	@param int $type_id Facultatif, identifiant d'un type de rapport ou une liste de type de rapport
 *	@param int $usr_id Facultatif, identifiant de l'utilisateur des rapports
 *	@param $author Facultatif, identifiant de l'utilisateur qui est l'auteur du rapport
 *	@param string $date_start Facultatif, date de départ sur laquelle filtrer le résultat
 *	@param string $date_end Facultatif, date de fin sur laquelle filtrer le résultat
 *	@param string $ref Facultatif, référence du rapport
 *	@param bool $is_sync Facultatif, affiche tous les rapports syncrhonisé ou non , si null prend les deux
 *	@param bool $need_sync Facultatif, affiche tous les rapports à syncrhonisé ou non , si null prend les deux
 *	@param $sort Facultatif, permet de trier le résultat (tableau clé => valeur où valeur est asc ou dir) :
 *				- date_created => asc | desc
 *	@param mixed $ord_com Optionnel, identifiant d'une commande générée indirectement (mettre null pour récupérer les rapports n'étant pas utilisés pour identifier une commande indirect)
 *	@param int $offset Optionnel, ligne de départ à appliquer sur la requete
 *	@param int $limit Optionnel, limite à appliquer sur la requete
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant du rapport
 *				- type_id : identifiant du type de rapport
 *				- author_id : identifiant de l'auteur
 *				- author_ref : numéro de référence de l'auteur
 *				- author_name : nom de l'auteur
 *				- author_is_sync : l'auteur est il synchronisé ?
 *				- author_is_deleted : l'auteur est il supprimé ?
 *				- comments : commentaires
 *				- date_created_en : date de création du rapport au format Y-m-d h:i:s
 *				- date_created : date de création du rapport au format d/m/Y à H:i
 *				- type_name : nom du type de rapport
 *				- usr_id : identifiant de l'utilisateur concerné par le rapport
 *				- usr_ref : numéro de référence du client
 *				- usr_name : nom du client
 *				- usr_is_sync : le compte est-il synchronisé ?
 *				- usr_is_deleted : le compte est-il supprimé ?
 *				- usr_city : ville du client (! adresse de facturation)
 */
function rp_reports_get( $id=0, $type_id=0, $usr_id=0, $author=0, $date_start=null, $date_end=null, $ref=null, $is_sync=null, $need_sync=null, $sort=false, $ord_com=false, $offset=false, $limit=false ){
	if( $id > 0 && !rp_reports_exists( $id ) ){
		return false;
	}
	// Vérification que type id est soit une liste d'identifiant ou un type précis
	if(!is_array($type_id) && !is_numeric($type_id)){
		return false;
	}

	// on vérifie que le(s) type(s) existe
	if( is_array($type_id) && count($type_id) > 0 ){
		foreach ($type_id as $type){
			if( !rp_types_exists( $type_id ) ){
				return false;
			}
		}
	}

	// on vérifie que le(s) type(s) existe
	if( is_numeric($type_id) && $type_id<0 ){
		return false;
	}

	$usr_id = control_array_integer( $usr_id, false );
	if( $usr_id === false ){
		return false;
	}

	if( $author > 0 && !gu_users_exists( $author ) ){
		return false;
	}
	if ($date_start != null && !isdateheure($date_start) ) {
		return false;
	}
	if ($date_end != null && !isdateheure($date_end) ) {
		return false;
	}

	if( $ord_com !== false && $ord_com !== null ){
		$ord_com = control_array_integer( $ord_com, false );
		if( $ord_com === false ){
			return false;
		}
	}

	global $config;

	$sql = '
		select
			rpr_id as id,
			rpr_rpt_id as type_id,

			rpr_usr_id as usr_id,
			usr.usr_ref,
			usr.usr_is_sync as usr_is_sync,
			if( usr.usr_date_deleted is null, 0, 1 ) as usr_is_deleted,
			IF(uadr.adr_firstname="", uadr.adr_society, concat(uadr.adr_firstname," ",uadr.adr_lastname)) as usr_name,
			uadr.adr_city as usr_city,

			rpr_author_id as author_id,
			author.usr_ref as author_ref,
			author.usr_is_sync as author_is_sync,
			if( author.usr_date_deleted is null, 0, 1 ) as author_is_deleted,
			IF(adr.adr_firstname="", adr.adr_society, concat(adr.adr_firstname," ",adr.adr_lastname)) as author_name,

			rpr_comments as comments,
			rpr_date_created as date_created_en,
			date_format(rpr_date_created,"%d/%m/%Y à %H:%i") as date_created,
			date_format(rpr_date_created,"%Y-%m-%d") as simple_date,
			rpt_name as type_name

		from rp_reports
			join rp_report_types on (rpt_tnt_id=0 or rpt_tnt_id=rpr_tnt_id) and rpt_id=rpr_rpt_id
			left join gu_users as author on author.usr_id=rpr_author_id and (author.usr_tnt_id=rpr_tnt_id or author.usr_tnt_id=0)
			left join gu_adresses as adr on adr.adr_id=author.usr_adr_invoices and (adr.adr_tnt_id=usr_tnt_id or adr.adr_tnt_id=0)
			left join gu_users as usr on usr.usr_id=rpr_usr_id and usr.usr_tnt_id=rpr_tnt_id
			left join gu_adresses as uadr on uadr.adr_id=usr.usr_adr_invoices and uadr.adr_tnt_id=usr.usr_tnt_id
		where rpr_tnt_id='.$config['tnt_id'].'
			and rpt_date_deleted is null
			and rpr_date_deleted is null
	';

	if( $id > 0 ){
		$sql .= ' and rpr_id='.$id;
	}

	// recherche sur les types
	if( is_numeric($type_id) && $type_id > 0 ){
		$sql .= ' and rpr_rpt_id='.$type_id;
	}

	// recherche sur les types
	if( is_array($type_id) && count($type_id) > 0 ){
		$sql .= ' and rpr_rpt_id in('.implode(',', $type_id).')';
	}
	if( count($usr_id) ){
		$sql .= ' and rpr_usr_id in ('.implode( ', ', $usr_id ).')';
	}
	if( $author > 0 ){
		$sql .= ' and rpr_author_id='.$author;
	}

	if ( $date_start != null  ) {
		$sql .= ' and rpr_date_created >= \''.$date_start.' 00:00:00\' ';
	}

	if( $date_end != null ){
		$sql .= ' and rpr_date_created <= \''.$date_end.' 23:59:59\' ';
	}

	if( $ref !==null ){
		$sql .= ' and rpr_ref=\''.addslashes($ref).'\'';
	}

	if( $is_sync !==null ){
		$sql .= ' and rpr_is_sync='.($is_sync ? '1':'0');
	}

	if( $need_sync !==null ){
		$sql .= ' and rpr_need_sync='.($need_sync ? '1':'0');
	}

	// Restriction des rapports sur leur utilisation pour identifier les commandes indirectes
	if( $ord_com !== false ){
		if( $ord_com === null ){
			// Non utilisé pour identifier une commande indirecte
			$sql .= ' and ifnull(rpr_order_com, 0) = 0';
		}else{
			// Utilisé pour une commande ou une liste de commandes
			$sql .= ' and rpr_order_com in ('.implode(', ', $ord_com).')';
		}
	}

	if ($sort == false || !is_array($sort) || sizeof($sort) == 0){
		$sort = array( 'date_created'=>'desc' );
	}

	// Converti le paramètre de tri en SQL
	$sort_final = array();
	foreach ($sort as $col => $dir) {
		$col = strtolower(trim($col));
		$dir = strtolower(trim($dir)) == 'desc' ? 'desc' : 'asc';

		// tri par champ avancé
		switch ($col) {
			case 'date_created':
				array_push($sort_final, 'rpr_date_created '.$dir);
				break;
		}
	}

	if (is_array($sort_final) && sizeof($sort_final)){
		$sql .= ' order by '.implode(', ', $sort_final);
	}

	if( is_numeric($limit) && $limit > 0 ){
		$sql .= ' limit '.( is_numeric($offset) ? $offset : 0 ).', '.$limit;
	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('[rp_reports_get] Echec du chargement : '.mysql_error()."\n".$sql);
	}

	return $res;
}

/** Cette fonction permet de récupérer la liste de tous les auteurs (non supprimé) de rapport émis via l'application
 *	@param int $type_id Optionnel, identifiant d'un type de rapport
 *	@param $sort Facultatif, permet de trier le résultat (tableau clé => valeur où clé est le nom de la colonne SQL et valeur est asc ou desc). Applicable sur une des colonnes des tables suivants : gu_users, gu_adresses, rp_reports
 *	@return resource Un résultat MySQL contenant :
 *								- id : identifiant de l'auteur
 * 								- firstname : prénom de l'auteur
 * 								- lastname : nom de l'auteur
 * 								- society : société de l'auteur
 * 								- nb_reposts : nombre de rapport émis
 */
function rp_reports_get_all_author( $type_id=0, $sort=array() ){
	if( !is_numeric($type_id) || $type_id < 0 ){
		return false;
	}

	$type_id = control_array_integer($type_id);

	global $config;

	$sql = '
		select usr_id as id, adr_firstname as firstname, adr_lastname as lastname, adr_society as society, count(*) as nb_reports
		from rp_reports
			join gu_users on ((usr_tnt_id = 0 or usr_tnt_id = rpr_tnt_id) and usr_id = rpr_author_id)
			join gu_adresses on ((adr_tnt_id = 0 or adr_tnt_id = usr_tnt_id) and adr_usr_id = usr_id and adr_id = usr_adr_invoices)
		where rpr_tnt_id = '.$config['tnt_id'].'
			and usr_date_deleted is null
		group by usr_id, adr_firstname, adr_lastname, adr_society
	';

	// Recherche sur les types de rapports
	if( is_array($type_id) ){
		$sql .= ' and rpr_rpt_id in ('. implode(',',$type_id) . ')';
	}

	if ($sort == false || !is_array($sort) || sizeof($sort) == 0){
		$sort = array();
	}

	// Converti le paramètre de tri en SQL
	$sort_final = array();
	foreach( $sort as $col => $dir ){
		$col = strtolower(trim($col));
		$dir = strtolower(trim($dir));

		array_push( $sort_final, $col." ".$dir );
	}

	if(is_array($sort_final) && sizeof($sort_final) ){
		$sql .= ' order by '.implode(', ', $sort_final);
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer la liste de tous les clients pour lesquels un rapport a été émis.
 * 	@param int $type_id Optionnel, identifiant d'un type de rapport
 *	@param array $sort Facultatif, permet de trier le résultat (tableau clé => valeur où clé est le nom de la colonne SQL et valeur est asc ou desc). Applicable sur une des colonnes des tables suivants : gu_users, gu_adresses, rp_reports
 *	@return resource Un résultat MySQL contenant :
 *			- id : identifiant du client
 *			- ref : code client
 * 			- firstname : prénom du client
 * 			- lastname : nom du client
 * 			- society : société du client
 * 			- nb_reposts : nombre de rapport émis
 */
function rp_reports_get_all_user( $type_id=0, $sort=array() ){
	if( !is_numeric($type_id) || $type_id < 0 ){
		return false;
	}

	$type_id = control_array_integer($type_id);

	global $config;

	$sql = '
		select usr_id as id, usr_ref as ref, adr_firstname as firstname, adr_lastname as lastname, adr_society as society, count(*) as nb_reports
		from rp_reports
			join gu_users on ((usr_tnt_id = 0 or usr_tnt_id = rpr_tnt_id) and usr_id = rpr_usr_id)
			join gu_adresses on ((adr_tnt_id = 0 or adr_tnt_id = usr_tnt_id) and adr_usr_id = usr_id and adr_id = usr_adr_invoices)
		where rpr_tnt_id = '.$config['tnt_id'].'
			and usr_date_deleted is null
		group by usr_id, adr_firstname, adr_lastname, adr_society
	';

	// Recherche sur les types de rapports
	if( is_array($type_id) ){
		$sql .= ' and rpr_rpt_id in ('. implode(',',$type_id) . ')';
	}

	if ($sort == false || !is_array($sort) || sizeof($sort) == 0){
		$sort = array();
	}

	// Converti le paramètre de tri en SQL
	$sort_final = array();
	foreach( $sort as $col => $dir ){
		$col = strtolower(trim($col));
		$dir = strtolower(trim($dir));

		array_push( $sort_final, $col." ".$dir );
	}

	if(is_array($sort_final) && sizeof($sort_final) ){
		$sql .= ' order by '.implode(', ', $sort_final);
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'ajouter une ligne dans les rapports
 * 	@param int $type_id Obligatoire : identifiant du type de rapport
 * 	@param int $usr_id Obligatoire : identifiant de l'utilisateur
 * 	@param $author Obligatoire : identifiant de l'auteur
 * 	@param string $comments Facultatif : commentaire
 * 	@param string $date_created Facultatif : permet de définir la date de création du rapport ( format En )
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function rp_reports_add( $type_id, $usr_id, $author, $comments=false, $date_created=false ){
	if( !rp_types_exists( $type_id ) ){
		return false;
	}
	if( !gu_users_exists( $usr_id ) ){
		return false;
	}
	if( !gu_users_exists( $author ) ){
		return false;
	}
	if( $date_created && !isdateheure($date_created) ){
		return false;
	}
	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'rpr_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'rpr_rpt_id';
	$values[] = $type_id;

	$fields[] = 'rpr_usr_id';
	$values[] = $usr_id;

	$fields[] = 'rpr_author_id';
	$values[] = $author;

	if( $comments && trim($comments) ){
		$fields[] = 'rpr_comments';
		$values[] = '\''.addslashes($comments).'\'';
	}

	if( $date_created ){
		$fields[] = 'rpr_date_created';
		$values[] =  '\''.dateheureparse($date_created).'\'';
	}

	$sql = 'insert into rp_reports ('.implode( ',', $fields ).') values ('.implode( ',', $values ).')';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}


	$id = ria_mysql_insert_id();

	// Envoi d'une notification par mail
	$report = ria_mysql_fetch_assoc( rp_reports_get($id) );
	flow_notifications_send( CLS_REPORT, dev_devices_get_object_simplified(CLS_REPORT, array($report['id']), $report) );

	return $id;
}

/** Cette fonction permet de mettre à jour une ligne d'un type de rapport
 * 	@param int $id Obligatoire : identifiant du rapport
 * 	@param int $type_id Obligatoire : identifiant du type de rapport
 * 	@param int $usr_id Obligatoire : identifiant de l'utilisateur
 * 	@param $author Obligatoire : identifiant de l'auteur
 * 	@param string $comments Facultatif : commentaire
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function rp_reports_upd( $id, $type_id, $usr_id, $author, $comments=false ){
	if( !rp_reports_exists( $id ) ){
		return false;
	}
	if( !rp_types_exists( $type_id ) ){
		return false;
	}
	if( !gu_users_exists( $usr_id ) ){
		return false;
	}
	if( !gu_users_exists( $author ) ){
		return false;
	}

	global $config;

	$statements = array();
	$statements[] = 'rpr_rpt_id = '.$type_id;
	$statements[] = 'rpr_usr_id = '.$usr_id;
	$statements[] = 'rpr_author_id = '.$author;
	if( $comments!==false ){
		$statements[] = 'rpr_comments = \''.addslashes($comments).'\'';
	}

	$sql = '
		update rp_reports
		set '.implode(', ', $statements).'
		where rpr_tnt_id = '.$config['tnt_id'].'
			and rpr_id = '.$id.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer un rapport. La suppression est réalisée de façon virtuelle (corbeille)
 * 	@param int $id Obligatoire, identifiant du rapport à supprimer
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_reports_del( $id ){
	if( !rp_reports_exists( $id ) ){
		return false;
	}

	global $config;

	// supprime les notes liées
	$rnote = rp_report_notes_get($id);
	if( $rnote ){
		while( $n = ria_mysql_fetch_array($rnote) ){
			rp_report_notes_del($id, $n['note_id']);
		}
	}

	// supprime les objets liées
	$robj = rp_report_objects_get($id);
	if( $robj ){
		while( $o = ria_mysql_fetch_array($robj) ){
			rp_report_objects_del($id, $o['cls_id'], $o['obj_id_0'], $o['obj_id_1'], $o['obj_id_2']);
		}
	}

	$sql = '
		update rp_reports set rpr_date_deleted=now()
		where rpr_tnt_id = '.$config['tnt_id'].'
			and rpr_id = '.$id.'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction de définir un rapport comme à synchroniser
 * 	@param $rpr_id Obligatoire : identifiant du rapport
 * 	@param bool $need_sync Obligatoire : si true le rapport est à synchroniser avec l'erp
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_reports_set_need_sync( $rpr_id, $need_sync=true ){
	if( !rp_reports_exists( $rpr_id ) ){
		return false;
	}

	global $config;

	$sql = '
		update rp_reports set rpr_need_sync='.($need_sync ? '1':'0').'
		where rpr_tnt_id = '.$config['tnt_id'].'
			and rpr_id = '.$rpr_id.'
	';

	return ria_mysql_query($sql);
}

/**	Cette fonction force la mise à jour de la date de dernière modification de rapports.
 *	@param int $id Obligatoire, Identifiant ou tableau d'identifiants des rapports
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function rp_reports_set_date_modified( $id ){

	$id = control_array_integer( $id );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		update rp_reports
		set rpr_date_modified = now()
		where rpr_tnt_id = '.$config['tnt_id'].'
			and rpr_id in ('.implode(', ' , $id).')
	';

	return ria_mysql_query($sql);

}

/** Cette fonction de définir un rapport comme synchronisé
 * 	@param $rpr_id Obligatoire : identifiant du rapport
 * 	@param bool $is_sync Obligatoire : si true le rapport est marqué synchro
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_reports_set_is_sync( $rpr_id, $is_sync=true ){
	if( !rp_reports_exists( $rpr_id ) ){
		return false;
	}

	global $config;

	$sql = '
		update rp_reports set rpr_is_sync='.($is_sync ? '1':'0').'
		where rpr_tnt_id = '.$config['tnt_id'].'
			and rpr_id = '.$rpr_id.'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de définir la référence du rapport
 * 	@param $rpr_id Obligatoire : identifiant du rapport
 * 	@param string $ref Obligatoire : nouvelle référence du rapport
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_reports_set_ref( $rpr_id, $ref=null ){
	if( !rp_reports_exists( $rpr_id ) ){
		return false;
	}

	global $config;

	$sql = '
		update rp_reports set rpr_ref=\''.addslashes($ref).'\'
		where rpr_tnt_id = '.$config['tnt_id'].'
			and rpr_id = '.$rpr_id.'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction d'affecter un rapport à un checin
 * 	@param $rpr_id Obligatoire : identifiant du rapport
 * 	@param $rck_id Obligatoire : identifiant du check-in
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_reports_set_checkin( $rpr_id, $rck_id ){
	if( !rp_reports_exists( $rpr_id ) ){
		return false;
	}
	if( !rp_checkin_exists( $rck_id ) ){
		return false;
	}

	global $config;

	$sql = '
		update rp_reports set rpr_rck_id='.$rck_id.'
		where rpr_tnt_id = '.$config['tnt_id'].'
			and rpr_id = '.$rpr_id.'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de mettre à jour la vente indirecte liée à ce rapport.
 * 	Une vente peut être liée à plusieurs rapports réalisées précédemment à la commande.
 * 	@param int $rpr_id Obligatoire, identifiant du rapport
 * 	@param int $ord_id Obligatoire, identifiant de la commande
 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function rp_reports_set_order( $rpr_id, $ord_id ){
	if( !is_numeric($rpr_id) || $rpr_id <= 0 ){
		return false;
	}

	if( !is_numeric($ord_id) || $ord_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update rp_reports
		set rpr_order_com = '.$ord_id.'
		where rpr_tnt_id = '.$config['tnt_id'].'
			and rpr_id = '.$rpr_id.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de tester l'existence d'une note sur un rapport
 * 	@param $rpr_id Obligatoire, identifiant du rapport
 *	@param $rptn_id Obligatoire, identifiant du type de note
 *
 *	@return bool True en cas de succès ou False en cas d'erreur
 */
function rp_report_notes_exists( $rpr_id, $rptn_id ){
	if( !is_numeric($rpr_id) || $rpr_id < 0 ) return false;
	if( !is_numeric($rptn_id) || $rptn_id < 0 ) return false;
	global $config;

	$sql = 'select 1 from rp_report_notes where rprn_tnt_id='.$config['tnt_id'].' and rprn_rpr_id='.$rpr_id.' and rprn_rptn_id='.$rptn_id;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction permet récupérer les notes des rapports
 *	@param $rpr_id Facultatif, identifiant d'un rapport
 *	@param $type_id Facultatif, identifiant d'un type de rapport
 *	@param $note_id Facultatif, identifiant d'une type de note
 *
 *	@return resource Un résultat MySQL contenant :
 *				- rpr_id : identifiant du rapport
 *				- note_id : identifiant du type de note
 *				- value : valeur numérique de la note
 *				- comments : commentaires sur la note
 *				- note_name : nom du type du note
 *				- type_id : identifiant du type de rapport
 */
function rp_report_notes_get( $rpr_id=0, $type_id=0, $note_id=0 ){
	if( $rpr_id > 0 && !rp_reports_exists( $rpr_id ) ){
		return false;
	}
	if( $type_id > 0 && !rp_types_exists( $type_id ) ){
		return false;
	}
	if( $note_id > 0 && !rp_type_notes_exists( $note_id ) ){
		return false;
	}
	global $config;

	$sql = '
		select rprn_rpr_id as rpr_id, rprn_rptn_id as note_id, rprn_value as value, rprn_comments as comments,
			rptn_name as note_name, rpr_rpt_id as type_id
		from rp_report_notes
			join rp_reports on rpr_tnt_id=rprn_tnt_id and rprn_rpr_id=rpr_id
			join rp_report_type_notes on (rptn_tnt_id=0 or rptn_tnt_id=rprn_tnt_id) and rptn_id=rprn_rptn_id
		where rprn_tnt_id='.$config['tnt_id'].'
			and rptn_date_deleted is null
			and rpr_date_deleted is null
	';

	if( $rpr_id > 0 ){
		$sql .= ' and rpr_id='.$rpr_id;
	}
	if( $type_id > 0 ){
		$sql .= ' and rpr_rpt_id='.$type_id;
	}
	if( $note_id > 0 ){
		$sql .= ' and rprn_rptn_id='.$note_id;
	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('[rp_report_notes_get] Echec du chargement : '.mysql_error()."\n".$sql);
	}

	return $res;
}

/** Cette fonction permet d'ajouter une ligne de note pour un rapport
 * 	@param $rpr_id Obligatoire, identifiant du rapport
 * 	@param $rptn_id Obligatoire, identifiant du type de note
 * 	@param $value Obligatoire, note attribué à ce rapport (valeur numérique comprise entre 1 et 10)
 * 	@param $comments Facultatif, commentaire
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function rp_report_notes_add( $rpr_id, $rptn_id, $value, $comments=false ){
	if( !rp_reports_exists( $rpr_id ) ){
		return false;
	}
	if( !rp_type_notes_exists( $rptn_id ) ){
		return false;
	}
	if( !is_numeric($value) || $value < 0 || $value > 10 ){
		return false;
	}
	if( rp_report_notes_exists( $rpr_id, $rptn_id ) ){
		return rp_report_notes_upd( $rpr_id, $rptn_id, $value, $comments );
	}
	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'rprn_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'rprn_rpr_id';
	$values[] = $rpr_id;

	$fields[] = 'rprn_rptn_id';
	$values[] = $rptn_id;

	$fields[] = 'rprn_value';
	$values[] = $value;

	if( $comments && tim($comments) ){
		$fields[] = 'rprn_comments';
		$values[] = '\''.addslashes($comments).'\'';
	}

	$sql = 'insert into rp_report_notes ('.implode( ',', $fields ).') values ('.implode( ',', $values ).')';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de mettre à jour une ligne de note pour un rapport
 * 	@param $rpr_id Obligatoire, identifiant du rapport
 * 	@param $rptn_id Obligatoire, identifiant du type de note
 * 	@param $value Obligatoire, note attribué à ce rapport (valeur numérique comprise entre 1 et 10)
 * 	@param $comments Facultatif, commentaire
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function rp_report_notes_upd( $rpr_id, $rptn_id, $value, $comments=false ){
	if( !rp_reports_exists( $rpr_id ) ){
		return false;
	}
	if( !rp_type_notes_exists( $rptn_id ) ){
		return false;
	}
	if( !is_numeric($value) || $value < 0 || $value > 10 ){
		return false;
	}
	if( !rp_report_notes_exists( $rpr_id, $rptn_id ) ){
		return false;
	}

	global $config;

	$statements = array();
	$statements[] = 'rprn_value = '.$value;
	if( $comments!==false ){
		$statements[] = 'rprn_comments = \''.addslashes($comments).'\'';
	}

	$sql = '
		update rp_report_notes
		set '.implode(', ', $statements).'
		where rprn_tnt_id = '.$config['tnt_id'].'
			and rprn_rpr_id = '.$rpr_id.'
			and rprn_rptn_id = '.$rptn_id.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer une note sur un rapport
 * 	@param $rpr_id Obligatoire, identifiant du rapport
 * 	@param $rptn_id Obligatoire, identifiant du type de note
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_report_notes_del( $rpr_id, $rptn_id ){

	if( !rp_report_notes_exists( $rpr_id, $rptn_id ) ){
		return false;
	}

	global $config;

	$sql = '
		delete from rp_report_notes
		where rprn_tnt_id = '.$config['tnt_id'].'
			and rprn_rpr_id = '.$rpr_id.'
			and rprn_rptn_id = '.$rptn_id.'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de tester l'existence d'un objet sur un rapport
 * 	@param $rpr_id Obligatoire : identifiant du rapport
 * 	@param int $cls_id Obligatoire : identifiant de classe
 * 	@param $obj_id_0 Obligatoire : identifiant de l'objet 0
 * 	@param $obj_id_1 Obligatoire : identifiant de l'objet 1
 * 	@param $obj_id_2 Obligatoire : identifiant de l'objet 2
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_report_objects_exists( $rpr_id, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2 ){
	if( !is_numeric($rpr_id) || $rpr_id < 0 ) return false;
	if( !is_numeric($cls_id) || $cls_id < 0 ) return false;
	if( !is_numeric($obj_id_0) || $obj_id_0 < 0 ) return false;
	if( !is_numeric($obj_id_1) || $obj_id_1 < 0 ) return false;
	if( !is_numeric($obj_id_2) || $obj_id_2 < 0 ) return false;
	global $config;

	$sql = 'select 1 from rp_report_objects where rpro_tnt_id='.$config['tnt_id'].' and rpro_rpr_id='.$rpr_id.' and rpro_cls_id='.$cls_id.' and rpro_obj_id_0='.$obj_id_0.' and rpro_obj_id_1='.$obj_id_1.' and rpro_obj_id_2='.$obj_id_2;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction permet récupérer les objets lié à un rapports
 * 	@param $rpr_id Obligatoire : identifiant du rapport
 * 	@param int $cls_id Obligatoire : identifiant de classe
 * 	@param $obj_id_0 Obligatoire : identifiant de l'objet 0
 * 	@param $obj_id_1 Obligatoire : identifiant de l'objet 1
 * 	@param $obj_id_2 Obligatoire : identifiant de l'objet 2
 *
 *	@return resource Un résultat MySQL contenant :
 *				- rpr_id : identifiant du rapport
 *				- cls_id : identifiant de classe
 *				- obj_id_0 : identifiant de l'objet 0
 *				- obj_id_1 : identifiant de l'objet 1
 *				- obj_id_2 : identifiant de l'objet 2
 */
function rp_report_objects_get( $rpr_id=0, $cls_id=0, $obj_id_0=0, $obj_id_1=0, $obj_id_2=0 ){
	if( $rpr_id > 0 && !rp_reports_exists( $rpr_id ) ){
		return false;
	}
	if( !is_numeric($cls_id) || $cls_id < 0 ){
		return false;
	}
	if( !is_numeric($obj_id_0) || $obj_id_0 < 0 ){
		return false;
	}
	if( !is_numeric($obj_id_1) || $obj_id_1 < 0 ){
		return false;
	}
	if( !is_numeric($obj_id_2) || $obj_id_2 < 0 ){
		return false;
	}
	global $config;

	$sql = '
		select rpro_rpr_id as rpr_id, rpro_cls_id as cls_id, rpro_obj_id_0 as obj_id_0, rpro_obj_id_1 as obj_id_1,
			rpro_obj_id_2 as obj_id_2
		from rp_report_objects
			join rp_reports on rpr_tnt_id=rpro_tnt_id and rpro_rpr_id=rpr_id
		where rpro_tnt_id='.$config['tnt_id'].'
			and rpr_date_deleted is null
	';

	if( $rpr_id > 0 ){
		$sql .= ' and rpr_id='.$rpr_id;
	}
	if( $cls_id > 0 ){
		$sql .= ' and rpro_cls_id='.$cls_id;
	}
	if( $obj_id_0 > 0 ){
		$sql .= ' and rpro_obj_id_0='.$obj_id_0;
	}
	if( $obj_id_1 > 0 ){
		$sql .= ' and rpro_obj_id_1='.$obj_id_1;
	}
	if( $obj_id_2 > 0 ){
		$sql .= ' and rpro_obj_id_2='.$obj_id_2;
	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('[rp_report_objects_get] Echec du chargement : '.mysql_error()."\n".$sql);
	}

	return $res;
}

/** Cette fonction permet d'ajouter une ligne d'objet pour un rapport
 * 	@param $rpr_id Obligatoire : identifiant du rapport
 * 	@param int $cls_id Obligatoire : identifiant de classe
 * 	@param $obj_id_0 Obligatoire : identifiant de l'objet 0
 * 	@param $obj_id_1 Obligatoire : identifiant de l'objet 1
 * 	@param $obj_id_2 Obligatoire : identifiant de l'objet 2
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function rp_report_objects_add( $rpr_id, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2 ){
	if( !is_numeric($rpr_id) || $rpr_id < 0 ) return false;
	if( !is_numeric($cls_id) || $cls_id < 0 ) return false;
	if( !is_numeric($obj_id_0) || $obj_id_0 < 0 ) return false;
	if( !is_numeric($obj_id_1) || $obj_id_1 < 0 ) return false;
	if( !is_numeric($obj_id_2) || $obj_id_2 < 0 ) return false;

	if( rp_report_objects_exists( $rpr_id, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2 ) ){
		return true;
	}
	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'rpro_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'rpro_rpr_id';
	$values[] = $rpr_id;

	$fields[] = 'rpro_cls_id';
	$values[] = $cls_id;

	$fields[] = 'rpro_obj_id_0';
	$values[] = $obj_id_0;

	$fields[] = 'rpro_obj_id_1';
	$values[] = $obj_id_1;

	$fields[] = 'rpro_obj_id_2';
	$values[] = $obj_id_2;

	$sql = 'insert into rp_report_objects ('.implode( ',', $fields ).') values ('.implode( ',', $values ).')';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer une ligne d'objet pour un rapport
 * 	@param $rpr_id Obligatoire : identifiant du rapport
 * 	@param int $cls_id Obligatoire : identifiant de classe
 * 	@param $obj_id_0 Obligatoire : identifiant de l'objet 0
 * 	@param $obj_id_1 Obligatoire : identifiant de l'objet 1
 * 	@param $obj_id_2 Obligatoire : identifiant de l'objet 2
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function rp_report_objects_del( $rpr_id, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2 ){
	if( !rp_report_objects_exists( $rpr_id, $cls_id, $obj_id_0, $obj_id_1, $obj_id_2 ) ){
		return false;
	}
	global $config;

	$sql = '
		delete from rp_report_objects
		where rpro_tnt_id = '.$config['tnt_id'].'
			and rpro_rpr_id = '.$rpr_id.'
			and rpro_cls_id = '.$cls_id.'
			and rpro_obj_id_0 = '.$obj_id_0.'
			and rpro_obj_id_1 = '.$obj_id_1.'
			and rpro_obj_id_2 = '.$obj_id_2.'
	';

	return ria_mysql_query( $sql );
}



/** Cette fonction permet de tester l'existence d'un check-in
 * 	@param $rck_id Obligatoire : identifiant du check-in
 *
 * @return bool True en cas de succès ou False en cas d'erreur
 */
function rp_checkin_exists( $rck_id ){
	if( !is_numeric($rck_id) || $rck_id < 0 ) return false;
	global $config;

	$sql = 'select 1 from rp_checkin where rck_tnt_id='.$config['tnt_id'].' and rck_date_deleted is null and rck_id='.$rck_id;
	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}

/** Cette fonction permet récupérer les checkin effectués
 * 	@param $rck_id Obligatoire : identifiant du check-in
 *	@param int $usr_id Facultatif, identifiant de du client concerné par les check-in
 *	@param $author Facultatif, identifiant de l'utilisateur qui est l'auteur du check-in
 *
 *	@return resource Un résultat MySQL contenant :
 *				- rpr_id : identifiant du rapport
 *				- cls_id : identifiant de classe
 *				- obj_id_0 : identifiant de l'objet 0
 *				- obj_id_1 : identifiant de l'objet 1
 *				- obj_id_2 : identifiant de l'objet 2
 */
function rp_checkin_get( $rck_id=0, $usr_id=0, $author=0 ){
	if( $rck_id > 0 && !rp_checkin_exists( $rck_id ) ){
		return false;
	}
	global $config;

	$sql = '
		select rck_id as id, rck_usr_id as usr_id, rck_author_id as author_id, rck_date_start as date_start_en, rck_date_end as date_end_en,
			rck_latitude as latitude, rck_longitude as longitude, rck_confirmed as confirmed, rck_date_created as date_created_en
		from rp_checkin
		where rck_tnt_id='.$config['tnt_id'].'
			and rck_date_deleted is null
	';

	if( is_numeric($rck_id) && $rck_id > 0 ){
		$sql .= ' and rck_id='.$rck_id;
	}
	if( is_numeric($usr_id) && $usr_id > 0 ){
		$sql .= ' and rck_usr_id='.$usr_id;
	}
	if( is_numeric($author) && $author > 0 ){
		$sql .= ' and rck_author_id='.$author;
	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('[rp_checkin_get] Echec du chargement : '.mysql_error()."\n".$sql);
	}

	return $res;
}

/** Cette fonction permet d'ajouter une ligne de check-in
 * 	@param int $usr_id Obligatoire, identifiant du client
 * 	@param $author_id Obligatoire, identifiant de l'utilisateur qui est l'auteur du check-in
 * 	@param string $date_start Obligatoire, date de début du check-in
 * 	@param string $date_end Obligatoire, date de fin du check-in
 * 	@param $latitude Obligatoire, latitude du check-in
 * 	@param $longitude Obligatoire, longitude du check-in
 * 	@param $confirmed Facultatif, détermine si le check-in a été validé ou non par l'auteur. Valeur par défaut : false
 * 	@param string $date_created Facultatif, date de création du check-in. Valeur par défaut : false
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function rp_checkin_add( $usr_id, $author_id, $date_start, $date_end, $latitude, $longitude, $confirmed=false, $date_created=false ){

	if( !gu_users_exists( $usr_id ) ){
		return false;
	}
	if( !gu_users_exists( $author_id ) ){
		return false;
	}
	if( !isdateheure($date_start) ){
		return false;
	}
	if( !isdateheure($date_end) ){
		return false;
	}
	if( $date_created && !isdateheure($date_created) ){
		return false;
	}
	if( !is_numeric($latitude) ){
		return false;
	}
	if( !is_numeric($longitude) ){
		return false;
	}
	if( $confirmed!==false && !is_numeric($confirmed) ){
		return false;
	}

	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'rck_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'rck_usr_id';
	$values[] = $usr_id;

	$fields[] = 'rck_author_id';
	$values[] = $author_id;

	$fields[] = 'rck_date_start';
	$values[] =  '\''.dateheureparse($date_start).'\'';

	$fields[] = 'rck_date_end';
	$values[] =  '\''.dateheureparse($date_end).'\'';

	$fields[] = 'rck_latitude';
	$values[] = $latitude;

	$fields[] = 'rck_longitude';
	$values[] = $longitude;

	$fields[] = 'rck_confirmed';
	$values[] = $confirmed!==false ? $confirmed:"0";

	$fields[] = 'rck_date_created';
	if( $date_created ){
		$values[] =  '\''.dateheureparse($date_created).'\'';
	}else{
		$values[] = "now()";
	}

	$sql = 'insert into rp_checkin ('.implode( ',', $fields ).') values ('.implode( ',', $values ).')';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return ria_mysql_insert_id();
}

/** Cette fonction permet de modifier une ligne de check-in
 * 	@param int $id Obligatoire, identifiant du checkin
 * 	@param int $usr_id Obligatoire, identifiant du client
 * 	@param $author_id Obligatoire, identifiant de l'utilisateur qui est l'auteur du check-in
 * 	@param string $date_start Obligatoire, date de début du check-in
 * 	@param string $date_end Obligatoire, date de fin du check-in
 * 	@param $latitude Obligatoire, latitude du check-in
 * 	@param $longitude Obligatoire, longitude du check-in
 * 	@param $confirmed Facultatif, détermine si le check-in a été validé ou non par l'auteur. Valeur par défaut : false.
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function rp_checkin_upd( $id, $usr_id, $author_id, $date_start, $date_end, $latitude, $longitude, $confirmed=false ){

	if( !rp_checkin_exists( $id ) ){
		return false;
	}
	if( !gu_users_exists( $usr_id ) ){
		return false;
	}
	if( !gu_users_exists( $author_id ) ){
		return false;
	}
	if( !isdateheure($date_start) ){
		return false;
	}
	if( !isdateheure($date_end) ){
		return false;
	}
	if( !is_numeric($latitude) ){
		return false;
	}
	if( !is_numeric($longitude) ){
		return false;
	}

	global $config;

	$fields = array();

	$fields[] = 'rck_usr_id='.$usr_id;
	$fields[] = 'rck_author_id='.$author_id;
	$fields[] = 'rck_date_start=\''.dateheureparse($date_start).'\'';
	$fields[] = 'rck_date_end=\''.dateheureparse($date_end).'\'';
	$fields[] = 'rck_latitude='.$latitude;
	$fields[] = 'rck_longitude='.$longitude;

	if( $confirmed!==false && !is_numeric($confirmed) ){
		$fields[] = 'rck_confirmed='.($confirmed!==false ? $confirmed:"0");
	}

	$sql = 'update rp_checkin set '.implode( ',', $fields ).' where rck_tnt_id='.$config['tnt_id'].' and rck_id='.$id;

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer une ligne de check-in
 * 	@param $rck_id Obligatoire : identifiant du check-in
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function rp_checkin_del( $rck_id ){
	if( !rp_checkin_exists( $rck_id ) ){
		return false;
	}
	global $config;

	$sql = '
		update rp_checkin set rck_date_deleted = now()
		where rck_tnt_id = '.$config['tnt_id'].' and rck_id = '.$rck_id.'
	';

	return ria_mysql_query( $sql );
}

/// @}

// \endcond
?>