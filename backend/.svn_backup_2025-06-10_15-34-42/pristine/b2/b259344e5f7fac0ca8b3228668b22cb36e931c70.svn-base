<?php

	/**	\file popup-export-calls.php
	 *	Ce fichier est utilisé pour l'exportation des rapports d'appels. Il est affiché dans le back-office sous forme de popup.
	 * 	Il permet de paramétrer l'export (format, colonnes), l'export effectif étant réalisé par le fichier export-calls.php.
	 */
	
	// if( !isset($_GET['export'])){
	// 	$g_error = _("Une erreur inattendue s'est produite lors du chargement des informations.");
	// }

    $default = array( 'title' );

    $link = 'popup-export-calls.php';
    
    if(isset($_POST['export'])){
        if( !isset($_POST['columns']) || !is_array($_POST['columns']) || !sizeof($_POST['columns']) ){
            $error = _("Veuillez sélectionner une ou plusieurs informations à inclure dans cet export");
        } else {
            $columns = isset($_POST['columns']) && is_array($_POST['columns']) ? $_POST['columns'] : array();
        }
    
        if( !isset($error)){
            $success = true;
        }
    }

    // Exécute un export des rapports d'appels
    if( isset($_POST['export']) && !isset($error)){
        include('export-calls.php');
        exit;
    }
    
    define('ADMIN_PAGE_TITLE', _('Export des rapports d\'appels') .' - Yuto');
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
    require_once('admin/skin/header.inc.php');
?>
	<div id="export">
        <?php 
            if( isset($g_error) ){ 
                print '<div class="error">'.nl2br( $g_error ).'</div>';
            } else {
                print '
                    <form action="'.$link.'" method="post">
                ';

                if( isset($error) ){
                    print '<div class="error">'.nl2br( $error ).'</div>';
                } elseif( isset($success) ){
                    print '<div class="success">'._('L\'enregistrement s\'est correctement déroulé.').'</div>';
                }
		?>
			<h2>
				<span style="float: left;"><?php print _('Gestion de l\'export')?></span>
				<div class="export-action">
					<input class="btn-action" type="submit" name="export" value="<?php print _('Télécharger')?>" />
				</div>
				<div class="clear"></div>
			</h2>

			<span class="part-export">
				<?php print _('Informations :'); ?> 
				<a href="#" class="check-all-col"><?php print _('Cocher tout')?></a> | <a href="#" class="uncheck-all-col"><?php print _('Décocher tout')?></a>
			</span>
			<div class="clear"></div>
			
			<?php
				$ar_cols = array(
                    'date' => _('Date') ,
                    'author' => _('Auteur'),
                    'dest' => _('Destinataire'),
                    'number' => _('Numéro appelé'),
                    'duration' => _('Durée'),
                    'comments' => _('Commentaires'),
                );

				print '
					<div class="cols">
				';
				
				$i = 0;
				foreach( $ar_cols as $key=>$name ){
					$checked = in_array( $key, $default ) ? 'checked="checked"' : '';
                                        
                    print '
                        <div class="elems">
                            <input type="checkbox" '.$checked.' value="'.$name.'" id="col-'.$key.'" name="col-filter['.$key.']" class="col-filter" />
                            <label for="col-'.$key.'">'.htmlspecialchars( $name ).'</label>
                        </div>
                    ';
                }
                				
				print '
					</div>
                ';
            ?>
                
			<div class="clear"></div>
			
			<div class="export-action">
				<h2></h2>
				<input class="btn-action" type="submit" name="export" value="<?php print _('Télécharger')?>" />
			</div>
		</form>
		<?php } ?>
	</div>
	<script src="/admin/js/jquery.min.js"></script>
	<script><!--
		$(document).delegate(
            '.check-all-col', 'click', function(){
					$('.col-filter').attr('checked', 'checked');
					return false;
            }
        ).delegate(
            '.uncheck-all-col', 'click', function(){
                $('.col-filter').removeAttr('checked');
                return false;
            }
        ).delegate(
            'input[name=export]', 'click', function(){
                var cols = '';
                var heads = '';
                
                $('.col-filter:checked').each(function(){
                    var p = $(this).attr('name').replace('col-filter[', '').replace(']', '');
                    var v = $(this).val();
                    if (p == 'author'){
                        heads += '&heads[]=id_author&heads[]=name_author&heads[]=firstname_author&heads[]=society_author';
                        cols += '&cols[]=Identifiant de l\'auteur&cols[]=Nom de l\'auteur&cols[]=Prénom de l\'auteur&cols[]=Société de l\'auteur';
                    } else if (p == 'dest') {
                        heads += '&heads[]=id_dest&heads[]=name_dest&heads[]=firstname_dest&heads[]=society_dest';
                        cols += '&cols[]=Identifiant du destinataire&cols[]=Nom du destinataire&cols[]=Prénom du destinataire&cols[]=Société du destinataire';
                    } else {
                        heads += '&heads[]=' + p;
                        cols += '&cols[]=' + v;
                    }
                });

                if(cols != ''){
                    var url = '/admin/fdv/reports/calls/export-calls.php?'+ cols + heads + '&author=' + '<?php print $_GET['author']; ?>' + '&date1=' + '<?php print $_GET['date1']; ?>' + '&date2=' + '<?php print $_GET['date2']; ?>';

                    $('body').append('<div class="popup_ria_back_load"></div>');
                    $('body').append('<div class="popup_ria_back_notice notice"><?php print _('Votre export est en cours de préparation, veuillez patienter...')?></div>');

                    $.ajax({
                        type 	: 'get',
                        url 	: url,
                        data 	: '',
                        async 	: true,
                        success : function(urlDownload){
                            $('.popup_ria_back_notice').html('<?php print _('Votre export est prêt à être téléchargé :'); ?> <a href="#" class="download"><?php print _('Télécharger')?></a>');
                        }
                    });
                } else {
                    if ($('#error-download').length) {
                        $("#error-download").html("<?php print _("Veuillez sélectionner une ou plusieurs informations à inclure dans cet export"); ?>");
                    } else {
                        $('.error').remove();
                        $('.success').remove();
                        $("div#export>form>h2").before('<div class="error" id="error-download"><?php print _("Veuillez sélectionner une ou plusieurs informations à inclure dans cet export"); ?></div>');
                    }
                }
                return false;
            }
		).delegate(
			'.download', 'click', function(){
				var url = parent.window.location.href;
				url = url.replace('#', '', url);
                url = url + (url.match(/\?/) ? '&' : '?') + 'downloadexportcalls=1';

				parent.window.location.href = url;
				parent.hidePopup();
			}
		);
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
