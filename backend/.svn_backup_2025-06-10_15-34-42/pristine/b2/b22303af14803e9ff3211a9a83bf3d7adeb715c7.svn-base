<?php
	// CMCIC
	/** \defgroup cmcic CMCIC - Crédit mutuel / CIC / Monetico
	 *	\ingroup payment_external
	 *
	 *	Ce module permet les paiement avec CMCIC - Crédit mutuel
	 *
	 *	Variables de config utilisées :
	 *	url_payment			:	Url de la page paiement
	 *	url_payment_success	:	Url de la page paiement effectué
	 *
	 *	Variables de config à personnaliser (pour la prod uniquement) :
	 *	CMCIC_key_test		:	Clé pour la signature (en mode test)
	 *
	 *	@{
	 */

	/// Clé d'accès à l'API Crédit Mutuel / CIC
	define( 'CMCIC_CLE', $config['CMCIC_CLE'] );
	define( 'CMCIC_TPE', $config['CMCIC_TPE'] );
	/// Version de l'API utilisée
	define( 'CMCIC_VERSION', $config['CMCIC_VERSION'] );
	/// Url du serveur hébergeant l'API
	define( 'CMCIC_SERVEUR', $config['CMCIC_SERVEUR'] );
	/// Code société
	define( 'CMCIC_CODESOCIETE', $config['CMCIC_CODESOCIETE'] );
	/// Url de retour en cas de succès
	define( 'CMCIC_URLOK', $config['CMCIC_URLOK'] );
	/// Url de retour en cas d'échec
	define( 'CMCIC_URLKO', $config['CMCIC_URLKO'] );

	require_once('PaymentExternal.inc.php');
	require_once("PaymentExternal/CMCIC/CMCIC_Tpe.inc.php");

	/**	@class CMCIC
	 *	\brief Cette classe est l'implémentation concrète du fournisseur CMCIC en tant que prestataire de paiement externe.
	 *
	 */
	class CMCIC extends PaymentExternal {
		/// Valeur max de l'identifiant de transaction
		const MAX_TRANSACTION_ID		=	899999;
		/// Valeur à utiliser pour un paiement en Euro
		const DEVISE_EURO				=	'EUR';
		/// Langue de l'interface de paiement
		const LANG						=	'FR';

		private static $Instance;

		public $response_ord_id = 0;

		//Code de retours et noms associés
		protected $returns = array(
			"Annulation" => "Transaction annulée",
			"payetest" => "Transaction acceptée",
			"paiement" => "Transaction acceptée",
			"paiement_pf2" => "Le paiement a été accepté sur le serveur productif pour la partie 2",
			"paiement_pf3" => "Le paiement a été accepté sur le serveur productif pour la partie 3",
			"paiement_pf4" => "Le paiement a été accepté sur le serveur productif pour la partie 4",
			"Annulation_pf2" => "Le paiement a été refusé sur le serveur productif pour la partie 2",
			"Annulation_pf3" => "Le paiement a été refusé sur le serveur productif pour la partie 3",
			"Annulation_pf4" => "Le paiement a été refusé sur le serveur productif pour la partie 4",
			"others" => "Transaction annulée, le code de retour n'est pas géré",
		);


		/*
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @param bool $nojs Facultatif, détermine si javascript est utilisable ou non
		 * @return void
		*/
		public static function doPayment( $nojs=false ){
			return self::getInstance()->_doPayment( $nojs );
		}

		/**
		 * Renvoie le singleton
		 * @return object Le singleton
		 */
		public static function getInstance(){
			if( !self::$Instance ){
				self::$Instance = new self;
			}
			return self::$Instance;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@return object L'instance
		 */
		public static function getPaymentResult(){
			return self::getInstance()->_getPaymentResult();
		}

		/**
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @param bool $nojs Facultatif, détermine si javascript peut être utilisé ou bien doit être désactivé (true)
		 * @return void
		*/
		public function _doPayment( $nojs=false ){
			$orderId = $this->getOrderId();
			$amount = $this->getOrderAmount();

			// free texte : a bigger reference, session context for the return on the merchant website
			$sTexteLibre = "";

			// transaction date : format d/m/y:h:m:s
			$sDate = date("d/m/Y:H:i:s");

			// between 2 and 4
			$sNbrEch = "";

			// date echeance 1 - format dd/mm/yyyy
			$sDateEcheance1 = "";

			// montant échéance 1 - format  "xxxxx.yy" (no spaces)
			$sMontantEcheance1 = "";

			// date echeance 2 - format dd/mm/yyyy
			$sDateEcheance2 = "";

			// montant échéance 2 - format  "xxxxx.yy" (no spaces)
			$sMontantEcheance2 = "";

			// date echeance 3 - format dd/mm/yyyy
			$sDateEcheance3 = "";

			// montant échéance 3 - format  "xxxxx.yy" (no spaces)
			$sMontantEcheance3 = "";

			// date echeance 4 - format dd/mm/yyyy
			$sDateEcheance4 = "";

			// montant échéance 4 - format  "xxxxx.yy" (no spaces)
			$sMontantEcheance4 = "";

			// contexte_commande
			$r_adrs = ord_orders_get_with_adresses( $_SESSION['usr_id'], $orderId );
			$adrs = ria_mysql_fetch_assoc_all( $r_adrs );

			$build_context = [ // Tableau de données à convertir en json
				"billing" => [
					"addressLine1" => $adrs[0]['inv_address1'],
					"city" => $adrs[0]['inv_city'],
					"postalCode" => $adrs[0]['inv_postal_code'],
					"country" => $adrs[0]['inv_country']
				],
				"shipping" => [
					"city" => $adrs[0]['dlv_city'],
					"postalCode" => $adrs[0]['dlv_postal_code'],
					"country" => $adrs[0]['dlv_country']
				]
			];

			if( empty($adrs[0]['inv_society']) ){ // Gestion pro/part dans l'adresse de facturation
				$build_context["billing"] = array("lastname" => $adrs[0]['inv_lastname']) + $build_context["billing"];
				$build_context["billing"] = array("firstname" => $adrs[0]['inv_firstname']) + $build_context["billing"];
			}else{
				$build_context["billing"] = array("firstname" => $adrs[0]['inv_society']) + $build_context["billing"];
			}

			if( !empty($adrs[0]['dlv_address2']) ){ // Gestion de l'adresse de livraison
				$build_context["shipping"] = array("addressLine2" => $adrs[0]['dlv_address2']) + $build_context["shipping"];
				$build_context["shipping"] = array("addressLine1" => $adrs[0]['dlv_address1']) + $build_context["shipping"];
			}else{
				$build_context["shipping"] = array("addressLine1" => $adrs[0]['dlv_address1']) + $build_context["shipping"];
			}

			if( empty($adrs[0]['dlv_society']) ){ // Gestion pro/part dans l'adresse de livraison
				$build_context["shipping"] = array("lastname" => $adrs[0]['dlv_lastname']) + $build_context["shipping"];
				$build_context["shipping"] = array("firstname" => $adrs[0]['dlv_firstname']) + $build_context["shipping"];
			}else{
				$build_context["shipping"] = array("firstname" => $adrs[0]['dlv_society']) + $build_context["shipping"];
			}

			$build_context = json_encode($build_context, JSON_PRETTY_PRINT); // création du fichier Json
			$sContext = base64_encode( $build_context ); // encodage du json en base 64

			// 3D secure
			$s3dsV1 = 0; // 3DSecure V1.0 / https://www.monetico-paiement.fr/fr/info/documentations/Monetico_Paiement_documentation_technique_v2.0.pdf (page 10)
			$s3dsV2 = "challenge_mandated"; // 3DSecure V2.X / même URL et même page

			$oTpe = new CMCIC_Tpe(self::LANG);
			$oHmac = new CMCIC_Hmac($oTpe);

			// Data to certify
			$PHP1_FIELDS = sprintf(CMCIC_CGI1_FIELDS, 		"3dsdebrayable=".$s3dsV1,
															"TPE=".$oTpe->sNumero,
															"ThreeDSecureChallenge=".$s3dsV2,
															"contexte_commande=".$sContext,
															"date=".$sDate,
															"dateech1=".$sDateEcheance1,
															"dateech2=".$sDateEcheance2,
															"dateech3=".$sDateEcheance3,
															"dateech4=".$sDateEcheance4,
															"lgue=".$oTpe->sLangue,
															"mail=".$this->getUserEmail(),
															"montant=".$amount.self::DEVISE_EURO,
															"montantech1=".$sMontantEcheance1,
															"montantech2=".$sMontantEcheance2,
															"montantech3=".$sMontantEcheance3,
															"montantech4=".$sMontantEcheance4,
															"nbrech=".$sNbrEch,
															"reference=".$orderId,
															"societe=".$oTpe->sCodeSociete,
															"texte-libre=".HtmlEncode($sTexteLibre),
															"url_retour_err=".$oTpe->sUrlKO,
															"url_retour_ok=".$oTpe->sUrlOK,
															"version=".$oTpe->sVersion
			);

			// MAC computation
			$sMAC = $oHmac->computeHmac($PHP1_FIELDS);

			$params = array(
				"version" => $oTpe->sVersion,
				"TPE" => $oTpe->sNumero,
				"date" => $sDate,
				"montant" => $amount.self::DEVISE_EURO,
				"reference" => $orderId,
				"MAC" => $sMAC,
				"url_retour_ok" => $oTpe->sUrlOK,
				"url_retour_err" => $oTpe->sUrlKO,
				"lgue" => $oTpe->sLangue,
				"societe" => $oTpe->sCodeSociete,
				"contexte_commande" => $sContext,
				"texte-libre" => HtmlEncode($sTexteLibre),
				"mail" => $this->getUserEmail(),
				"nbrech" => $sNbrEch,
				"dateech1" => $sDateEcheance1,
				"montantech1" => $sMontantEcheance1,
				"dateech2" => $sDateEcheance2,
				"montantech2" => $sMontantEcheance2,
				"dateech3" => $sDateEcheance3,
				"montantech3" => $sMontantEcheance3,
				"dateech4" => $sDateEcheance4,
				"montantech4" => $sMontantEcheance4,
				"ThreeDSecureChallenge" => $s3dsV2,
				"3dsdebrayable" => $s3dsV1
			);

			// Enregistre l'accès à la banque dans CouchDB
			$user = ria_mysql_fetch_assoc( gu_users_get($_SESSION['usr_id']) );
			$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $params['reference'])));

			$this->data_couchDB['user_id'] = $user['id'];
			$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
			$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
			$this->data_couchDB['user_email'] = $user['email'];

			$this->data_couchDB['ord_id'] = $order['id'];
			$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
			$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];
			$this->data_couchDB['data'] = array_merge( $params, ['data_context' => json_decode($build_context, true)] );
			$this->savePaymentInCouchDB();

			?>

			<form class="cmcic" action="<?php echo $oTpe->sUrlPaiement;?>" method="post" id="PaymentRequest">
				<input type="hidden" name="3dsdebrayable"			id="3dsdebrayable"    			value="<?php echo $s3dsV1; ?>" />
				<input type="hidden" name="TPE"                 	id="TPE"            			value="<?php echo $oTpe->sNumero;?>" />
				<input type="hidden" name="ThreeDSecureChallenge"	id="ThreeDSecureChallenge"    	value="<?php echo $s3dsV2; ?>" />
				<input type="hidden" name="contexte_commande"      	id="contexte_commande" 			value="<?php echo $sContext; ?>" />
				<input type="hidden" name="date"                	id="date"           			value="<?php echo $sDate;?>" />
				<input type="hidden" name="dateech1"           	 	id="dateech1"       			value="<?php echo $sDateEcheance1;?>" />
				<input type="hidden" name="dateech2"            	id="dateech2"      				value="<?php echo $sDateEcheance2;?>" />
				<input type="hidden" name="dateech3"            	id="dateech3"       			value="<?php echo $sDateEcheance3;?>" />
				<input type="hidden" name="dateech4"            	id="dateech4"       			value="<?php echo $sDateEcheance4;?>" />
				<input type="hidden" name="lgue"                	id="lgue"           			value="<?php echo $oTpe->sLangue;?>" />
				<input type="hidden" name="MAC"                 	id="MAC"            			value="<?php echo $sMAC;?>" />
				<input type="hidden" name="mail"                	id="mail"          			 	value="<?php echo $this->getUserEmail();?>" />
				<input type="hidden" name="montant"             	id="montant"        			value="<?php echo $amount.self::DEVISE_EURO;?>" />
				<input type="hidden" name="montantech1"        	 	id="montantech1"    			value="<?php echo $sMontantEcheance1;?>" />
				<input type="hidden" name="montantech2"         	id="montantech2"    			value="<?php echo $sMontantEcheance2;?>" />
				<input type="hidden" name="montantech3"         	id="montantech3"    			value="<?php echo $sMontantEcheance3;?>" />
				<input type="hidden" name="montantech4"         	id="montantech4"    			value="<?php echo $sMontantEcheance4;?>" />
				<input type="hidden" name="nbrech"              	id="nbrech"         			value="<?php echo $sNbrEch;?>" />
				<input type="hidden" name="reference"           	id="reference"      			value="<?php echo $orderId;?>" />
				<input type="hidden" name="societe"             	id="societe"        			value="<?php echo $oTpe->sCodeSociete; ?>" />
				<input type="hidden" name="texte-libre"         	id="texte-libre"    			value="<?php echo HtmlEncode($sTexteLibre); ?>" />
				<input type="hidden" name="url_retour_err"      	id="url_retour_err" 			value="<?php echo $oTpe->sUrlKO;?>" />
				<input type="hidden" name="url_retour_ok"       	id="url_retour_ok"  			value="<?php echo $oTpe->sUrlOK;?>" />
				<input type="hidden" name="version"             	id="version"        			value="<?php echo $oTpe->sVersion;?>" />
				<input type="submit" name="bouton"              	id="bouton"        				value="Payer par CB" style="<?php print !$nojs ? 'display: none;': '' ?>"/>
			</form>
			<?php
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@return object L'instance
		 */
		public function _getPaymentResult(){

			// Begin Main : Retrieve Variables posted by CMCIC Payment Server
			$CMCIC_bruteVars = getMethode();

			$return_code = $CMCIC_bruteVars['code-retour'];
			if (!array_key_exists( $return_code, $this->returns)){
				$return_code = "others";
			}
			$name = $this->returns[$return_code];
			$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $CMCIC_bruteVars['reference'])));
			$user = ria_mysql_fetch_assoc(gu_users_get($order['usr_id']));

			$this->data_couchDB['ord_id'] = $order['id'];
			$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
			$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];

			$this->data_couchDB['user_id'] = $user['id'];
			$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
			$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
			$this->data_couchDB['user_email'] = $user['email'];

			$this->data_couchDB['data'] = $CMCIC_bruteVars;
			$this->data_couchDB['code_id'] = $CMCIC_bruteVars['code-retour'];
			$this->data_couchDB['code_name'] = $name;
			$this->saveReturnPaymentInCouchDB();

			switch($CMCIC_bruteVars['code-retour']) {
				case "Annulation" :
					// Payment has been refused
					// put your code here (email sending / Database update)
					// Attention : an autorization may still be delivered for this payment
					break;

				case "payetest":
				case "paiement":

					$this->response_ord_id = $CMCIC_bruteVars['reference'] ;

					// Véfifie l'état de la commande
					$state = ord_orders_get_state( $this->response_ord_id );
					if ($state === false || $state >= _STATE_WAIT_PAY){
						throw new exception("CMCIC : La commande $this->response_ord_id semble déjà avoir été traitée ! (state = $state)");
					}

					ord_orders_pay_type_set($this->response_ord_id, _PAY_CB);
					if( $state < _STATE_WAIT_PAY ){
						ord_orders_update_status($this->response_ord_id, _STATE_WAIT_PAY, '');
					}
					ord_orders_update_status($this->response_ord_id, _STATE_PAY_CONFIRM, '');

					break;


				/*** ONLY FOR MULTIPART PAYMENT ***/
				case "paiement_pf2":
				case "paiement_pf3":
				case "paiement_pf4":
					// Payment has been accepted on the productive server for the part #N
					// return code is like paiement_pf[#N]
					// put your code here (email sending / Database update)
					// You have the amount of the payment part in $CMCIC_bruteVars['montantech']
					break;

				case "Annulation_pf2":
				case "Annulation_pf3":
				case "Annulation_pf4":
					// Payment has been refused on the productive server for the part #N
					// return code is like Annulation_pf[#N]
					// put your code here (email sending / Database update)
					// You have the amount of the payment part in $CMCIC_bruteVars['montantech']
					break;

			}

			$receipt = CMCIC_CGI2_MACOK;

			return sprintf(CMCIC_CGI2_RECEIPT,$receipt);
		}

		/**
		 *	Récupère la clé
		 *	@return La clé du site
		 */
		public function getKey(){
			return CMCIC_CLE ;
		}

	}

	/// @}
?>