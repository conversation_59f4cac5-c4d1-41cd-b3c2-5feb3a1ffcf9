<?php
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à accès à cette page
	if( isset($_GET['unit']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_UNIT_EDIT');
	}else{ // !isset($_GET['unit'])
		gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_UNIT_ADD');
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php');
		exit;
	}

	// Vérifie la validité de l'identifiant d'unité passé en paramètre
	if( isset($_GET['unit']) && $_GET['unit']!=0 ){
		if( !fld_units_exists($_GET['unit']) ){
			header('Location: index.php');
			exit;
		}
	}

	unset($error);

	// Suppression
	if( isset($_POST['del']) ){
		if( !fld_units_del($_GET['unit']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression de l'unité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php');
			exit;
		}
	}

	// Enregistrement
	if( isset($_POST['save']) ){
		if( !isset($_POST['name']) || !trim($_POST['name']) )
			$error = "Veuillez indiquer le nom de l'unité de mesure.";
		elseif( !isset($_POST['symbol']) || !trim($_POST['symbol']) )
			$error = _("Veuillez indiquer le symbole de l'unité de mesure.");
		elseif( !isset($_POST['desc']) )
			$error = _("Une ou plusieurs informations obligatoires sont manquantes.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		elseif( isset($_GET['unit']) && $_GET['unit']==0 ){
			// Ajout
			if( !fld_units_add($_POST['symbol'],$_POST['name'],$_POST['desc']) )
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'unité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}elseif( isset($_GET['unit']) && $_GET['unit']>0 ){
			// Modification
			if( !fld_units_update($_GET['unit'],$_POST['symbol'],$_POST['name'],$_POST['desc']) )
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'unité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}
	
	// Chargement
	$unit = array('id'=>0,'symbol'=>'','name'=>'','desc'=>'');
	if( isset($_GET['unit']) && is_numeric($_GET['unit']) && $_GET['unit']>0 ){
		$unit = ria_mysql_fetch_array(fld_units_get($_GET['unit']));
	}
	if( isset($_POST['name']) ) $unit['name'] = $_POST['name'];
	if( isset($_POST['symbol']) ) $unit['symbol'] = $_POST['symbol'];
	if( isset($_POST['desc']) ) $unit['desc'] = $_POST['desc'];

	// Défini le titre de la page
	$page_title = ( isset($unit['name']) && $unit['name']!='' ) ? _('Unité de mesure').' '.$unit['name'] : 'Nouvelle unité de mesure';
	define('ADMIN_PAGE_TITLE', _('Unités de mesure') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');


	
	echo '<h2>'.htmlspecialchars( $page_title ).'</h2>'; 


	if( isset($error) )
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';

?>

	<form action="edit.php?unit=<?php print $unit['id'] ?>" method="post" onsubmit="return fldUnitValidForm(this)">
		<table>
			<tbody>
				<tr>
					<td><label for="name"><span class="mandatory">*</span> <?php echo _('Nom :'); ?></label></td>
					<td><input type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars($unit['name']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="symbol"><span class="mandatory">*</span> <?php echo _('Symbole :'); ?></label></td>
					<td><input type="text" name="symbol" id="symbol" maxlength="12" value="<?php print htmlspecialchars($unit['symbol']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="desc"><?php echo _('Description :'); ?></label></td>
					<td><textarea name="desc" id="desc" rows="15" cols="40"><?php print htmlspecialchars($unit['desc']); ?></textarea></td>
				</tr>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save" value="<?php echo _("Enregistrer"); ?>" />
					<input type="submit" name="cancel" value="<?php echo _("Annuler"); ?>" onclick="return fldUnitCancelEdit()" />
					<?php if( $unit['id']>0 && gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_UNIT_DEL') ){ ?>
					<input type="submit" name="del" value="<?php echo _("Supprimer"); ?>" onclick="return fldUnitConfirmDel()" />
					<?php } ?>
				</td></tr>
			</tfoot>
		</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>