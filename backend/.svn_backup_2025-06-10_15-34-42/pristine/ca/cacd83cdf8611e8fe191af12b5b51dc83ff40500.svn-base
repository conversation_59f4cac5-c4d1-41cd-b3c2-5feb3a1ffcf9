<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagInterface;
use SchemaDotOrg\Tags\TagContactPoint;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag WebSite
 */
class TagWebsite implements TagInterface {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	private $type = "WebSite";

	/**
	 * Tableau avec les champs pour ce tag
	 *
	 * @var array $fields
	 */
	private $fields = array();

	/**
	 * Constructeur de la classe
	 * @param string $name
	 * @param string $url
	 */
	public function __construct($name='', $url=''){
		global $config;
		$this->fields['@type'] = $this->type;

		if (!trim($name)) {
			$name = $config['site_name'];
		}
		if (!trim($url)) {
			$url = $config['site_url'];
		}
		$this->setName($name)
			->setUrl($url);

	}
	/**
	 * Permet de retourner le type de tag
	 *
	 * @return string le type de tag
	 */
	public function type(){
		return $this->type;
	}

	/**
	 * Permet d'ajouter un champ au tag
	 *
	 * @param string $name Le nom du champ
	 * @param mixed $value La valeur du champ
	 * @return self retourne l'instance
	 */
	public function addField($name, $value){
		$this->fields[$name] = $value;

		return $this;
	}

	/**
	 * Cette fonction permet de retourner la liste des champs
	 *
	 * @return array le tableau des champs
	 */
	public function getFields(){
		return $this->fields;
	}

	/**
	 * Cette fonction permet d'initialisé le champ name de siteb
	 *
	 * @param string $name le nom du site
	 * @return self retourne l'instance
	 */
	public function setName($name){
		$this->addField('name', $name);

		return $this;
	}

	/**
	 * Cette fonction permet d'initialisé le champ alternateName du site
	 *
	 * @param string $alternateName le nom alternatif du site
	 * @return self retourne l'instance
	 */
	public function setAlternateName($alternateName){
		$this->addField('alternateName', $alternateName);

		return $this;
	}

	/**
	 * Cette fonction permet d'initialisé le champ url de l'organisation
	 *
	 * @param string $url Lien du site
	 * @return self retourne l'instance
	 */
	public function setUrl($url){
		$this->addField('url', $url);

		return $this;
	}

	/**
	 * Cette fonction permet d'initialisé le champ name de l'organisation
	 *
	 * @param TagPotentialAction $PotentialAction Le tag contenant une action qui peux être réalisé
	 * @return self retourne l'instance
	 */
	public function addContactPoint(TagPotentialAction $PotentialAction){

		$this->fields['potentialAction'][] = $PotentialAction->getFields();

		return $this;
	}
}

/// @}