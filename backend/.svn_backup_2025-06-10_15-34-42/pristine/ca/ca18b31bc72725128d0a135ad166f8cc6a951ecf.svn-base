<?xml version="1.0" encoding="UTF-8" ?>
<routes xmlns="http://symfony.com/schema/routing"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/routing
        https://symfony.com/schema/routing/routing-1.0.xsd">

    <route id="blog" path="/blog">
        <default key="_controller">
            <string>AcmeBlogBundle:Blog:index</string>
        </default>
        <default key="list">
            <list>
                <bool xsi:nil="true" />
                <int xsi:nil="true" />
                <float xsi:nil="1" />
                <string xsi:nil="true" />
                <list xsi:nil="true" />
                <map xsi:nil="true" />
            </list>
        </default>
    </route>
</routes>
