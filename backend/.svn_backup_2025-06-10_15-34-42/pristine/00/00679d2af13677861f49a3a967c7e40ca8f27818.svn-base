<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/money.proto

namespace GPBMetadata\Google\Type;

class Money
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0ace010a17676f6f676c652f747970652f6d6f6e65792e70726f746f120b" .
            "676f6f676c652e74797065223c0a054d6f6e657912150a0d63757272656e" .
            "63795f636f6465180120012809120d0a05756e697473180220012803120d" .
            "0a056e616e6f7318032001280542600a0f636f6d2e676f6f676c652e7479" .
            "7065420a4d6f6e657950726f746f50015a36676f6f676c652e676f6c616e" .
            "672e6f72672f67656e70726f746f2f676f6f676c65617069732f74797065" .
            "2f6d6f6e65793b6d6f6e6579f80101a20203475450620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

