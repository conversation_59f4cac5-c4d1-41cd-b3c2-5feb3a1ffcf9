<?php
	// MyPDF
	/*
		Surcouche de FPDF pour créer des pdf plus facilement.
		MyPDF enveloppe une FPDF qu'il ne construit qu'à l'appel de build ou output
		Possibilité d'ajouter dans le document les éléments suivants :
		- GridPanel		:	Tableau
		- VerticalPanel :	GridPanel mais une seule colonne
		- Text			:	Texte
		Les cellules des tableaux de redimensionnent toutes seules en fonction de leur contenu
		Si on veut fixer des tailles fixes, on ne peut le faire que sur le texte
		Cette class n'est pas forcément fiable à 100% mais peut être utile
	*/
	
	require_once('fpdf/fpdf.php');
	require_once('mypdf/MyPDF_Element.php');
	require_once('mypdf/MyPDF_VerticalPanel.php');
	
	class MyPDF extends MyPDF_Element {
	
		// méthodes statiques
			
			// getLineHeight
			/* Renvoie la hauteur d'une ligne */
			public static function getLineHeight() {
				return 5;
			}
			
			// getTextWidth
			/* Renvoie la longueur du texte */
			public static function getTextWidth($param) {
				$pdf = new MyPDF($param);
				$str = $param['str'];
				$rows = explode("\n", $str);
				$w = 0;
				foreach ($rows as $row) $w = max($w, $pdf->build()->GetStringWidth($row) + 3);
				return $w;
			}
		
		// attributes
		
			private	$_rootPanel;	// rootPanel
		
		// methods
		
			// __construct
			/* Constructeur */
			public function __construct($param = array()) {
				parent::__construct($param);
				
				$bold = (array_key_exists('bold', $param)) ? $param['bold'] : false;
				$font = (array_key_exists('font', $param)) ? $param['font'] : 'Arial';
				$fontSize = (array_key_exists('fontSize', $param)) ? $param['fontSize'] : 12;
				$italic = (array_key_exists('italic', $param)) ? $param['italic'] : false;
				$underline = (array_key_exists('underline', $param)) ? $param['underline'] : false;
				
				$this->setBold(array('bold' => $bold));
				$this->setFont(array('font' => $font));
				$this->setFontSize(array('fontSize' => $fontSize));
				$this->setItalic(array('italic' => $italic));
				$this->setUnderline(array('underline' => $underline));
				
				$this->setRootPanel(array('rootPanel' => new MyPDF_VerticalPanel(array('parent' => $this))));
			}
			
			// add
			/* Ajoute un élément */
			public function add($param) {
				$this->getRootPanel()->add($param);
				return $this;
			}
			
			// build
			/* Build */
			public function build($param = array()) {
				$pdf = new FPDF();
				
				parent::build(array('pdf' => $pdf));
				
				$pdf->AddPage();
				$this->getRootPanel()->build(array('pdf' => $pdf));
				return $pdf;
			}
			
			// getHeight
			/* Renvoie la hauteur */
			public function getHeight() {
				return 287;
			}
			
			// getLeft
			/* Renvoie left */
			public function getLeft() {
				return 5;
			}
			
			// getRootPanel
			/* Renvoie root panel */
			public function getRootPanel() {
				return $this->_rootPanel;
			}
			
			// getTop
			/* Renvoie top */
			public function getTop() {
				return 5;
			}
			
			// getWidth
			/* Renvoie la largeur */
			public function getWidth() {
				return 200;
			}
			
			// output
			/* output */
			public function output($param = null) {
				$pdf = $this->build();
				if ($param) return $pdf->Output($param['filename']);
				else return $pdf()->Output();
			}
			
			// setRootPanel
			/* Affecte root panel */
			public function setRootPanel($param) {
				$this->_rootPanel = $param['rootPanel'];
				return $this;
			}
		
	}

