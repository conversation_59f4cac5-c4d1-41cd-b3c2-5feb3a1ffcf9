<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1alpha1/eval.proto

namespace GPBMetadata\Google\Api\Expr\V1Alpha1;

class PBEval
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Expr\V1Alpha1\Value::initOnce();
        \GPBMetadata\Google\Rpc\Status::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0af8040a23676f6f676c652f6170692f657870722f7631616c706861312f" .
            "6576616c2e70726f746f1218676f6f676c652e6170692e657870722e7631" .
            "616c706861311a17676f6f676c652f7270632f7374617475732e70726f74" .
            "6f22a4010a094576616c537461746512330a0676616c7565731801200328" .
            "0b32232e676f6f676c652e6170692e657870722e7631616c706861312e45" .
            "78707256616c7565123b0a07726573756c747318032003280b322a2e676f" .
            "6f676c652e6170692e657870722e7631616c706861312e4576616c537461" .
            "74652e526573756c741a250a06526573756c74120c0a0465787072180120" .
            "012803120d0a0576616c756518022001280322b3010a094578707256616c" .
            "756512300a0576616c756518012001280b321f2e676f6f676c652e617069" .
            "2e657870722e7631616c706861312e56616c7565480012330a056572726f" .
            "7218022001280b32222e676f6f676c652e6170692e657870722e7631616c" .
            "706861312e4572726f72536574480012370a07756e6b6e6f776e18032001" .
            "280b32242e676f6f676c652e6170692e657870722e7631616c706861312e" .
            "556e6b6e6f776e536574480042060a046b696e64222e0a084572726f7253" .
            "657412220a066572726f727318012003280b32122e676f6f676c652e7270" .
            "632e537461747573221b0a0a556e6b6e6f776e536574120d0a0565787072" .
            "73180120032803426c0a1c636f6d2e676f6f676c652e6170692e65787072" .
            "2e7631616c7068613142094576616c50726f746f50015a3c676f6f676c65" .
            "2e676f6c616e672e6f72672f67656e70726f746f2f676f6f676c65617069" .
            "732f6170692f657870722f7631616c706861313b65787072f80101620670" .
            "726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

