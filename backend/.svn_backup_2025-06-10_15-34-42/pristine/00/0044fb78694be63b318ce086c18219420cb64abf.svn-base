<documentation title="Multi-line If Conditions">
    <standard>
    <![CDATA[
    Multi-line if conditions should be indented one level and each line should begin with a boolean operator.  The end parenthesis should be on a new line.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Correct indentation.">
        <![CDATA[
if ($foo
<em>    </em>&& $bar
) {
}
        ]]>
        </code>
        <code title="Invalid: No indentation used on the condition lines.">
        <![CDATA[
if ($foo
<em></em>&& $bar
) {
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Boolean operator at the start of the line.">
        <![CDATA[
if ($foo
    <em>&&</em> $bar
) {
}
        ]]>
        </code>
        <code title="Invalid: Boolean operator at the end of the line.">
        <![CDATA[
if ($foo <em>&&</em>
    $bar
) {
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: End parenthesis on a new line.">
        <![CDATA[
if ($foo
    && $bar
<em>)</em> {
}
        ]]>
        </code>
        <code title="Invalid: End parenthesis not moved to a new line.">
        <![CDATA[
if ($foo
    && $bar<em>)</em> {
}
        ]]>
        </code>
    </code_comparison>
</documentation>
