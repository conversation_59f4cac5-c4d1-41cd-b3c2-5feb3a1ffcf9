<?php

	/** \file export-google-shopping.php
	 *
	 * 	Ce script est destiné à mettre à jour les fichiers d'export Google Shopping
	 *	des clients ayant activé cette option. Il est lancé chaque jour par une tâche planifiée.
	 *
	 */

	 set_include_path(dirname(__FILE__) . '/../include/');

	require_once( 'db.inc.php' );
	require_once( 'define.inc.php' );
	require_once( 'cfg.variables.inc.php' );
	require_once( 'strings.inc.php' );
	require_once( 'images.inc.php' );

	unset($config);

	$tnt = isset($argv[1]) && is_numeric($argv[1]) && $argv[1]>0 ? $argv[1] : 0;
	$img = isset($argv[2]) && is_numeric($argv[2]) && $argv[2]>0 ? $argv[2] : 0;

	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_tenants( $tnt );
	if( !is_array($configs) || !sizeof($configs) ){
		return false;
	}

	// Actualise le nombre d'utilisations de chaque image
	foreach( $configs as $config ){
		img_images_count_update( $img );
	}

