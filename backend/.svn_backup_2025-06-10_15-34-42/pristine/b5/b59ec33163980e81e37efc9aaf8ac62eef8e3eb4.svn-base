<?xml version="1.0" encoding="utf-8"?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service class="Symfony\Component\DependencyInjection\ContainerInterface" id="service_container" public="true" synthetic="true"/>
        <service alias="service_container" id="Psr\Container\ContainerInterface" public="false"/>
        <service alias="service_container" id="Symfony\Component\DependencyInjection\ContainerInterface" public="false"/>
    </services>
</container>
