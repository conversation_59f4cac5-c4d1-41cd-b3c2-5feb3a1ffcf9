<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AC' => 'Ascension-suálui',
  'AD' => 'Andorra',
  'AE' => 'Arabiemirkodeh',
  'AF' => 'Afganistan',
  'AG' => 'Antigua já Barbuda',
  'AI' => 'Anguilla',
  'AL' => 'Albania',
  'AM' => 'Armenia',
  'AO' => 'Angola',
  'AQ' => 'Antarktis',
  'AR' => 'Argentina',
  'AS' => 'Amerika Samoa',
  'AT' => 'Nuorttâriijkâ',
  'AU' => 'Australia',
  'AW' => 'Aruba',
  'AX' => 'Vuáskueennâm',
  'AZ' => 'Azerbaidžan',
  'BA' => 'Bosnia já Herzegovina',
  'BB' => 'Barbados',
  'BD' => 'Bangladesh',
  'BE' => 'Belgia',
  'BF' => 'Burkina Faso',
  'BG' => 'Bulgaria',
  'BH' => 'Bahrain',
  'BI' => 'Burundi',
  'BJ' => 'Benin',
  'BL' => 'St. Barthélemy',
  'BM' => 'Bermuda',
  'BN' => 'Brunei',
  'BO' => 'Bolivia',
  'BR' => 'Brasilia',
  'BS' => 'Bahama',
  'BT' => 'Bhutan',
  'BW' => 'Botswana',
  'BY' => 'Vielgis-Ruoššâ',
  'BZ' => 'Belize',
  'CA' => 'Kanada',
  'CC' => 'Kookossuolluuh (Keelingsuolluuh)',
  'CF' => 'Koskâ-Afrika täsiväldi',
  'CH' => 'Sveitsi',
  'CI' => 'Côte d’Ivoire',
  'CK' => 'Cooksuolluuh',
  'CL' => 'Chile',
  'CM' => 'Kamerun',
  'CN' => 'Kiina',
  'CO' => 'Kolumbia',
  'CR' => 'Costa Rica',
  'CU' => 'Kuuba',
  'CV' => 'Cape Verde',
  'CW' => 'Curaçao',
  'CX' => 'Juovlâsuálui',
  'CY' => 'Kypros',
  'CZ' => 'Tšekki',
  'DE' => 'Saksa',
  'DG' => 'Diego Garcia',
  'DJ' => 'Djibouti',
  'DK' => 'Tanska',
  'DM' => 'Dominica',
  'DO' => 'Dominikaanisâš täsiväldi',
  'DZ' => 'Algeria',
  'EA' => 'Ceuta já Melilla',
  'EC' => 'Ecuador',
  'EE' => 'Eestieennâm',
  'EG' => 'Egypti',
  'ER' => 'Eritrea',
  'ES' => 'Espanja',
  'ET' => 'Etiopia',
  'FI' => 'Suomâ',
  'FJ' => 'Fidži',
  'FK' => 'Falklandsuolluuh',
  'FM' => 'Mikronesia littoväldi',
  'FO' => 'Färsuolluuh',
  'FR' => 'Ranska',
  'GA' => 'Gabon',
  'GB' => 'Ovtâstum Kunâgâskodde',
  'GD' => 'Grenada',
  'GE' => 'Georgia',
  'GF' => 'Ranska Guyana',
  'GG' => 'Guernsey',
  'GH' => 'Ghana',
  'GI' => 'Gibraltar',
  'GL' => 'Grönland',
  'GM' => 'Gambia',
  'GN' => 'Guinea',
  'GP' => 'Guadeloupe',
  'GQ' => 'Peeivitäsideijee Guinea',
  'GR' => 'Kreikka',
  'GS' => 'Maadâ-Georgia já Máddááh Sandwichsuolluuh',
  'GT' => 'Guatemala',
  'GU' => 'Guam',
  'GW' => 'Guinea-Bissau',
  'GY' => 'Guyana',
  'HK' => 'Hongkong – Kiina e.h.k.',
  'HN' => 'Honduras',
  'HR' => 'Kroatia',
  'HT' => 'Haiti',
  'HU' => 'Uŋgar',
  'IC' => 'Kanariasuolluuh',
  'ID' => 'Indonesia',
  'IE' => 'Irland',
  'IL' => 'Israel',
  'IM' => 'Mansuálui',
  'IN' => 'India',
  'IO' => 'Brittilâš India väldimeerâ kuávlu',
  'IQ' => 'Irak',
  'IR' => 'Iran',
  'IS' => 'Island',
  'IT' => 'Italia',
  'JE' => 'Jersey',
  'JM' => 'Jamaika',
  'JO' => 'Jordan',
  'JP' => 'Jaapaan',
  'KE' => 'Kenia',
  'KG' => 'Kirgisia',
  'KH' => 'Kambodža',
  'KI' => 'Kiribati',
  'KM' => 'Komoreh',
  'KN' => 'St. Kitts já Nevis',
  'KP' => 'Tave-Korea',
  'KR' => 'Maadâ-Korea',
  'KW' => 'Kuwait',
  'KY' => 'Caymansuolluuh',
  'KZ' => 'Kazakstan',
  'LA' => 'Laos',
  'LB' => 'Libanon',
  'LC' => 'St. Lucia',
  'LI' => 'Liechtenstein',
  'LK' => 'Sri Lanka',
  'LR' => 'Liberia',
  'LS' => 'Lesotho',
  'LT' => 'Liettua',
  'LU' => 'Luxemburg',
  'LV' => 'Latvia',
  'LY' => 'Libya',
  'MA' => 'Marokko',
  'MC' => 'Monaco',
  'MD' => 'Moldova',
  'ME' => 'Montenegro',
  'MF' => 'St. Martin',
  'MG' => 'Madagaskar',
  'MH' => 'Marshallsuolluuh',
  'ML' => 'Mali',
  'MM' => 'Myanmar (Burma)',
  'MN' => 'Mongolia',
  'MO' => 'Macao - – Kiina e.h.k.',
  'MP' => 'Tave-Marianeh',
  'MQ' => 'Martinique',
  'MR' => 'Mauritania',
  'MS' => 'Montserrat',
  'MT' => 'Malta',
  'MU' => 'Mauritius',
  'MV' => 'Malediveh',
  'MW' => 'Malawi',
  'MX' => 'Meksiko',
  'MY' => 'Malaysia',
  'MZ' => 'Mosambik',
  'NA' => 'Namibia',
  'NC' => 'Uđđâ-Kaledonia',
  'NE' => 'Niger',
  'NF' => 'Norfolksuálui',
  'NG' => 'Nigeria',
  'NI' => 'Nicaragua',
  'NL' => 'Vuáládâhenâmeh',
  'NO' => 'Taažâ',
  'NP' => 'Nepal',
  'NR' => 'Nauru',
  'NU' => 'Niue',
  'NZ' => 'Uđđâ-Seeland',
  'OM' => 'Oman',
  'PA' => 'Panama',
  'PE' => 'Peru',
  'PF' => 'Ranska Polynesia',
  'PG' => 'Papua-Uđđâ-Guinea',
  'PH' => 'Filipineh',
  'PK' => 'Pakistan',
  'PL' => 'Puola',
  'PM' => 'St. Pierre já Miquelon',
  'PN' => 'Pitcairn',
  'PR' => 'Puerto Rico',
  'PT' => 'Portugal',
  'PW' => 'Palau',
  'PY' => 'Paraguay',
  'QA' => 'Qatar',
  'RE' => 'Réunion',
  'RO' => 'Romania',
  'RS' => 'Serbia',
  'RU' => 'Ruoššâ',
  'RW' => 'Ruanda',
  'SA' => 'Saudi Arabia',
  'SB' => 'Salomosuolluuh',
  'SC' => 'Seychelleh',
  'SD' => 'Sudan',
  'SE' => 'Ruotâ',
  'SG' => 'Singapore',
  'SH' => 'Saint Helena',
  'SI' => 'Slovenia',
  'SJ' => 'Čokkeväärih já Jan Mayen',
  'SK' => 'Slovakia',
  'SL' => 'Sierra Leone',
  'SM' => 'San Marino',
  'SN' => 'Senegal',
  'SO' => 'Somalia',
  'SR' => 'Surinam',
  'SS' => 'Maadâ-Sudan',
  'ST' => 'São Tomé já Príncipe',
  'SV' => 'El Salvador',
  'SX' => 'Sint Maarten',
  'SY' => 'Syria',
  'SZ' => 'Swazieennâm',
  'TA' => 'Tristan da Cunha',
  'TC' => 'Turks- já Caicossuolluuh',
  'TD' => 'Tšad',
  'TF' => 'Ranska máddááh kuávluh',
  'TG' => 'Togo',
  'TH' => 'Thaieennâm',
  'TJ' => 'Tadžikistan',
  'TK' => 'Tokelau',
  'TL' => 'Timor-Leste',
  'TM' => 'Turkmenistan',
  'TN' => 'Tunisia',
  'TO' => 'Tonga',
  'TR' => 'Turkki',
  'TT' => 'Trinidad já Tobago',
  'TV' => 'Tuvalu',
  'TW' => 'Taiwan',
  'TZ' => 'Tansania',
  'UA' => 'Ukraina',
  'UG' => 'Uganda',
  'UM' => 'Ovtâstum Staatâi sierânâssuolluuh',
  'US' => 'Ovtâstum Staatah',
  'UY' => 'Uruguay',
  'UZ' => 'Uzbekistan',
  'VA' => 'Vatikan',
  'VC' => 'St. Vincent já Grenadines',
  'VE' => 'Venezuela',
  'VG' => 'Brittiliih Nieidâsuolluuh',
  'VI' => 'Ovtâstum Staatâi Nieidâsuolluuh',
  'VN' => 'Vietnam',
  'VU' => 'Vanuatu',
  'WF' => 'Wallis já Futuna',
  'WS' => 'Samoa',
  'XK' => 'Kosovo',
  'YE' => 'Jemen',
  'YT' => 'Mayotte',
  'ZA' => 'Maadâ-Afrikka',
  'ZM' => 'Sambia',
  'ZW' => 'Zimbabwe',
);
