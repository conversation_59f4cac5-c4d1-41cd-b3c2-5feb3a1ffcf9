<?php

	/**	\file export-bestsellers-prd.php
	 *
	 * 	Ce fichier réalise l'export des données de l'écran bestsellers-prd.php, au format Microsoft Excel.
	 * 
	 */

	require_once('products.inc.php');
	require_once('fields.inc.php');
	require_once( 'excel/PHPExcel.php' );

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_BESTSELLER_PRD');
	
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : date('Y-m-d');
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;

	// Filtre sur l'origine de la commande
	$params = view_origins_get_params();
	$origin = $params['origin'];
	$gescom = $params['gescom'];
	$is_web = $params['is_web'];

	$wst_id = 0;
	if( isset($_SESSION['websitepicker']) ){
		$wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
	}

	// Gère le filtre par représentant
	$seller_id = isset($_SESSION['ord_seller_id']) ? $_SESSION['ord_seller_id'] : 0;

	$rbest = stats_bestsellers_get( $date1, $date2, 100, $origin, $gescom, $wst_id, false, 0, 0, false, 0, 0, $seller_id );

	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();

	// Déterminé les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator( "riaStudio" )
								 ->setLastModifiedBy( "riaStudio" )
								 ->setTitle( _("Meilleurs Ventes / Produit") )
								 ->setSubject( _("Meilleurs Ventes / Produit") )
								 ->setDescription( _("Meilleurs Ventes / Produit") )
								 ->setKeywords( _("Meilleurs Ventes / Produit") )
								 ->setCategory( "" );
	
	// Création du fichier Excel
	$objWorksheet1 = $objPHPExcel->getActiveSheet();
	$objWorksheet1->setTitle(  _("Produits") );

	// Entête du tableau des catégories
	$objWorksheet1->mergeCells( 'A1:G1' );
	$objWorksheet1->setCellValue( 'A1', _("Produits") );
	$objWorksheet1->setCellValue( 'A2', _("Référence") );
	$objWorksheet1->setCellValue( 'B2', _('Désignation') );
	$objWorksheet1->setCellValue( 'C2', _('Ventes') );
	$objWorksheet1->setCellValue( 'D2', _('Par commandes') );
	$objWorksheet1->setCellValue( 'E2', _('Marge brute') );
	$objWorksheet1->setCellValue( 'F2', _('CA') );
	$objWorksheet1->setCellValue( 'G2', _('CA Total') );

	// Style sur l'entête du tableau
	$objWorksheet1->getStyle('A1:G2')->getFont()->setBold(true);
	$objWorksheet1->getStyle('A1:G2')->getFont()->setSize(11);
	$objWorksheet1->getStyle('A1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet1->getStyle('A1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('000099');
	$objWorksheet1->getStyle('A2:G2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('BBBBBB');
	$objWorksheet1->getStyle('A2:G2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);


	$line = 3;
	$total = array( 'orders'=>0, 'margin'=>0, 'by-ord'=>0, 'ca'=>0, 'ca-ord'=>0 );
	if( $rbest && ria_mysql_num_rows($rbest) ){
		while( $best = ria_mysql_fetch_array($rbest) ){
			$total['orders'] += $best['orders'];
			$total['by-ord'] += $best['orders']>0 ? $best['qte']/$best['orders'] : 0;
			$total['margin'] += $best['margin'];
			$total['ca'] += $best['total_ht'];
			$total['ca-ord'] += $best['ord_total_ht'];

			$objWorksheet1->setCellValueExplicit('A'.$line, $best['ref'], PHPExcel_Cell_DataType::TYPE_STRING);
			$objWorksheet1->setCellValue( 'B'.$line, $best['name']);
			$objWorksheet1->setCellValue( 'C'.$line, number_format( $best['orders'], 0, ',', ' ' ) );
			$objWorksheet1->setCellValue( 'D'.$line, number_format( $best['orders']>0 ? $best['qte']/$best['orders'] : 0, 0, ',', ' ' ) );
			$objWorksheet1->setCellValue( 'E'.$line, number_format( $best['margin'], 2, ',', ' ' ) );
			$objWorksheet1->setCellValue( 'F'.$line, number_format( $best['total_ht'], 2, ',', ' ' ) );
			$objWorksheet1->setCellValue( 'G'.$line, number_format( $best['ord_total_ht'], 2, ',', ' ' ) );
			
			$line++;
		}
	}
	
	$objWorksheet1->getStyle('A1:A'.$line)->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_TEXT);

	// Pieds du tableau des produits
	$objWorksheet1->mergeCells( 'A'.$line.':B'.$line );
	$objWorksheet1->setCellValue( 'A'.$line, _('Totaux').' :' );
	$objWorksheet1->setCellValue( 'C'.$line, number_format($total['orders'], 0, ',', ' ') );
	$objWorksheet1->setCellValue( 'D'.$line, number_format($total['by-ord'], 0, ',', ' ') );
	$objWorksheet1->setCellValue( 'E'.$line, number_format($total['margin'], 2, ',', ' ') );
	$objWorksheet1->setCellValue( 'F'.$line, number_format($total['ca'], 2, ',', ' ') );
	$objWorksheet1->setCellValue( 'G'.$line, number_format($total['ca-ord'], 2, ',', ' ') );

	$objWorksheet1->getStyle('A'.$line.':G'.$line)->getFont()->setBold(true);
	$objWorksheet1->getStyle('A'.$line.':G'.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('BBBBBB');
	$objWorksheet1->getStyle('A'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
	$objWorksheet1->getStyle('C2:G'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

	// Applique des formats aux cellules C5:G7
	$objWorksheet1->getStyle('E3:G'.$line)->getNumberFormat()->applyFromArray( array('code' => '# ##0') );	
	
	// Bordure du tableau
	$objWorksheet1->getStyle('A2:G'.$line)->getBorders()->applyFromArray(array(
		'allborders' => array(
			'style' => PHPExcel_Style_Border::BORDER_THIN, 
			'color' => array( 'rgb' => '808080') 
		)
	));

	// Largueur des cellules
	$objWorksheet1->getColumnDimension('A')->setWidth(20);
	$objWorksheet1->getColumnDimension('B')->setWidth(45);
	$objWorksheet1->getColumnDimension('C')->setWidth(15);
	$objWorksheet1->getColumnDimension('D')->setWidth(15);
	$objWorksheet1->getColumnDimension('E')->setWidth(15);
	$objWorksheet1->getColumnDimension('F')->setWidth(15);
	$objWorksheet1->getColumnDimension('G')->setWidth(15);

	// Redirect output to a client web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="meilleures-ventes-produits.xls"');
	header('Cache-Control: max-age=0');

	// Ecrit le fichier et le sauvegarde
	$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
	$objWriter->save('php://output');
	exit;