<?php

/**	\file view.php
 *	Cette page permet la création d'un rapport de visite.
 *	Elle accepte les paramètres suivants :
 *	- _GET['type'] : facultatif, identifiant du type de rapport de visite à créer
 */

require_once('reports.inc.php');
require_once('categories.inc.php');
require_once('products.inc.php');
require_once('cfg.variables.inc.php');

$type  = (isset($_GET['type'])) ? $_GET['type'] : 0;
$usr_select   = 0;
$comment   = '';
$date_deb  = '';
$hour_deb  = '';
$date_fin  = '';
$hour_fin  = '';

// Bouton Enregistrer
if (isset($_POST['save'])) {
	if (
		!isset($_POST['type']) || !isset($_POST['user-id']) || !isset($_POST['user-id']) || $_POST['user-id'] < 0 ||
		!isset($_POST['date_deb']) ||  !isset($_POST['hour_from']) ||
		!isset($_POST['date_fin']) ||  !isset($_POST['hour_to'])
	) {
		$error = _('Veuillez renseigner les champs obligatoire.');
	}


	if (!isset($error) && !isdate($_POST['date_deb'])) {
		$error = _("La date de début doit être saisie au format jj/mm/aaaa.\nVeuillez réessayer.");
	} elseif (!isset($error) && !ria_is_time($_POST['hour_from'])) {
		$error = _("L'heure de début doit être saisie au format hh:mm.\nVeuillez réessayer");
	}

	if (!isset($error)) {
		$date_from = $_POST['date_deb'] . ' ' . $_POST['hour_from'];
	}
	if (!isset($error) && !isdate($_POST['date_fin'])) {
		$error = _("La date de fin doit être saisie au format jj/mm/aaaa.\nVeuillez réessayer.");
	} elseif (!isset($error) && !ria_is_time($_POST['hour_to'])) {
		$error = _("L'heure de fin doit être saisie au format hh:mm.\nVeuillez réessayer");
	}

	if (!isset($error)) {
		$date_to = $_POST['date_fin'] . ' ' . $_POST['hour_to'];

		$date_deb_en = dateheureparse($date_from);
		$date_fin_en = dateheureparse($date_to);

		if (strtotime($date_deb_en) >= strtotime($date_fin_en)) {
			$error = 'La date de début (jj/mm/aaaa hh:mm) doit être antérieure à la date de fin (jj/mm/aaaa hh:mm).';
		}
	}

	if (!isset($error)) {
		$date_to = $_POST['date_fin'] . ' ' . $_POST['hour_to'];
		$date_fin_en = dateheureparse($date_to);

		if (strtotime($date_fin_en) > strtotime('now')) {
			$error = _('La date de fin ne peut pas être supérieure à la date et heure actuelle (jj/mm/aaaa hh:mm).');
		}
	}

	if (!isset($error)) {
		$a = DateTime::createFromFormat('d/m/Y H:i', $date_from);
		$b = DateTime::createFromFormat('d/m/Y H:i', $date_to);

		$interval = $a->diff($b);
		$hours    = ($interval->days * 24) + $interval->h + ($interval->i / 60) + ($interval->s / 3600);
		if($hours > 12){
			$error = _('Un rapport de visite ne peut pas durer plus de 12 heures.');
		}
	}

	if (!isset($error)) {
		$add = rp_reports_add($_POST['type'], $_POST['user-id'], $_SESSION['usr_id'], $_POST['comments'], date('d/m/Y H:i:s'));

		if (!$add) {
			$error = _('Une erreur est survenue lors de la création');
		} else {
			if( isset($_POST['date_deb']) && isset($_POST['date_fin']) ){
				$checkin = rp_checkin_add($_POST['user-id'], $_SESSION['usr_id'], $date_from, $date_to, 0, 0);
				rp_report_objects_add($add, CLS_CHECKIN, $checkin, 0, 0);

				if (!$checkin) {
					$error = _('Le rapport a été créé avec succès.');
				} else {
					$success = _('Erreur lors de l\'ajout des dates du rapport.');
				}
			}

			if (!isset($error)) {
				header('Location: /admin/fdv/reports/index.php?type=' . $_POST['type']);
				exit;
			}
		}
	} else {
		$type     = (isset($_POST['type'])) ? $_POST['type'] : 0;
		$usr_select      = (isset($_POST['user-id'])) ? $_POST['user-id'] : 0;
		$comment  = (isset($_POST['comments'])) ? $_POST['comments'] : '';
		$date_deb = (isset($_POST['date_deb'])) ? $_POST['date_deb'] : '';
		$hour_deb = (isset($_POST['hour_from'])) ? $_POST['hour_from'] : '';
		$date_fin = (isset($_POST['date_fin'])) ? $_POST['date_fin'] : '';
		$hour_fin = (isset($_POST['hour_to'])) ? $_POST['hour_to'] : '';
	}
}

// Bouton Annuler
if (isset($_POST['cancel'])) {
	header('Location: /admin/fdv/reports/index.php?type=' . $type['id']);
	exit;
}

$r_author 	= array('id' => 0, 'adr_firstname' => '', 'adr_lastname' => '', 'society' => '', 'is_sync' => 0);


$rr_author = gu_users_get($_SESSION['usr_id']);
if ($rr_author && ria_mysql_num_rows($rr_author)) {
	$r_author = ria_mysql_fetch_assoc($rr_author);
}



$author = isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] > 0 ? $_GET['author'] : 0;

$title =  _('Ajout d\'un rapport');
define('ADMIN_PAGE_TITLE', $title);
require_once('admin/skin/header.inc.php');

// Objets liés au rapport
$robject = rp_report_objects_get(0);

$colspan = $author ? 2 : 3;

// Détermine l'onglet en cours de consultation
if (!isset($_GET['tab'])) $_GET['tab'] = '';
$tab = 'general';
if (isset($_POST['tabFields']) || $_GET['tab'] == 'fields') {
	$tab = 'fields';
}
?>

<h2><?php print _('Ajout d\'un rapport'); ?></h2>

<?php
if (isset($error)) {
	print '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
} elseif (isset($success)) {
	print '<div class="error-success">' . nl2br(htmlspecialchars($success)) . '</div>';
} elseif (isset($notice)) {
	print '<div class="notice">' . nl2br($notice) . '</div>';
}
?>

<form action="edit.php" method="post">
	<table class="checklist ui-sortable">
		<tbody>
			<tr>
				<th colspan="2"><?php print _('Informations générales') ?></th>
			</tr>
			<tr>
				<td class="col170px"><span class="mandatory">*</span> <label for="type"><?php print _('Type de rapport :') ?></label></td>
				<td class="col-m"><select name="type" id="type"><?php
													$rtypes = rp_types_get();
													while ($t = ria_mysql_fetch_array($rtypes)) {
														print '<option value="' . $t['id'] . '"' . ($t['id'] == $type ? ' selected="selected"' : '') . '>' . htmlspecialchars($t['name']) . '</option>';
													}
													?></select></td>
			</tr>
			<tr>
				<td><span class="mandatory">*</span> <label><?php print _('Auteur :') ?></label></td>
				<td><?php
					print ($r_author['id'] > 0 ? '<a href="/admin/customers/edit.php?usr=' . $r_author['id'] . '">' : '') . '
					' . view_usr_is_sync($r_author) . ' ' . htmlspecialchars(trim($r_author['adr_firstname'] . ' ' . $r_author['adr_lastname'] . ' ' . $r_author['society'])) . '
					' . ($r_author['id'] > 0 ? '</a>' : '');
					?></td>
			</tr>
			<tr>
				<td><span class="mandatory">*</span> <label><?php print _('Client :') ?></label></td>
				<td id="user-id-report">
					<?php
					// on gère l'affichage du client si un client est choisit
					if ($usr_select != 0) {
						$r_user = gu_users_get($usr_select);
						if ($r_user && ria_mysql_num_rows($r_user)) {
							$usr = ria_mysql_fetch_assoc($r_user);
						}

						if (!$r_user) {
							$ref = str_pad($usr_select, 8, '0', STR_PAD_LEFT);
							print '<span class="barre" title="' . _('Ce compte client a été supprimé') . '">' . htmlspecialchars($ref) . '</span>';
						} else {
							if (!$usr['ref']) {
								$usr['ref'] = str_pad($usr_select, 8, '0', STR_PAD_LEFT);
							}
							print '<input name="user-id" id="user-id" value="' . $usr['id'] . '" type="hidden">';
							print '<a href="/admin/customers/edit.php?usr=' . $usr_select . '" target="_blank">';
							print view_usr_is_sync($usr) . ' ' . htmlspecialchars($usr['ref']) . ' ' . htmlspecialchars(gu_users_get_name($usr_select));
							print '</a>  -  ';
						}
					}

					?>
					<a href="#" id="upd-ord-usr"><?php print _('Sélectionner un compte client'); ?></a>
				</td>
			</tr>
			<tr>
				<td><label for="comments"><?php print _('Commentaires :') ?></label></td>
				<td><textarea name="comments" id="comments" rows="15" cols="40"><?php print $comment; ?></textarea></td>
			</tr>
			<tr>
				<td><span class="mandatory">*</span> <label for="date_deb"><?php print _('Date de début :') ?></label></td>
				<td>
					<input type="text" name="date_deb" class="date datepicker" id="date_deb" autocomplete="off" onfocus="blur();" value="<?php print $date_deb; ?>" />
					<label for="hour_from"><?php print _('à'); ?></label>
					<select class="hour hour_select" name="hour_from" id="hour_from" autocomplete="off">
						<?php
						$start = "00:00";
						$end = "23:45";

						$tStart = strtotime($start);
						$tEnd = strtotime($end);
						$tNow = $tStart;

						while ($tNow <= $tEnd) {
							$selected = ( $hour_deb == date("H:i", $tNow) ) ? 'selected' : '';
							
							print '<option value="' . date("H:i", $tNow)  . '" ' . $selected . '>' .  date("H:i", $tNow) . '</option>';
							$tNow = strtotime('+15 minutes', $tNow);
						}
						?>
					</select>
				</td>
			</tr>

			<tr>
				<td><span class="mandatory">*</span> <label for="date_fin"><?php print _('Date de fin :') ?></label></td>
				<td>
					<input type="text" name="date_fin" class="date datepicker" id="date_fin" autocomplete="off" onfocus="blur();" value="<?php print $date_fin; ?>" />
					<label for="hour_to"><?php print _('à'); ?></label>
					<select class="hour hour_select" name="hour_to" id="hour_to">
						<?php
						$start = "00:00";
						$end = "23:45";

						$tStart = strtotime($start);
						$tEnd = strtotime($end);
						$tNow = $tStart;

						while ($tNow <= $tEnd) {
							$selected = ( $hour_fin == date("H:i", $tNow) ) ? 'selected' : '';
							print '<option value="' . date("H:i", $tNow)  . '" ' . $selected . '>' .  date("H:i", $tNow) . '</option>';
							$tNow = strtotime('+15 minutes', $tNow);
						}
						?>
					</select>
				</td>
			</tr>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="2">
					<input title="<?php print _('Enregistrer le rapport'); ?>" type="submit" name="save" value="<?php print _('Enregistrer') ?>" />
					<input title="<?php print _('Annuler l\'ajout du rapport'); ?>" type="submit" name="cancel" value="<?php print _('Annuler') ?>" />
				</td>
			</tr>
		</tfoot>
	</table>
</form>

<?php
require_once('admin/skin/footer.inc.php');
?>