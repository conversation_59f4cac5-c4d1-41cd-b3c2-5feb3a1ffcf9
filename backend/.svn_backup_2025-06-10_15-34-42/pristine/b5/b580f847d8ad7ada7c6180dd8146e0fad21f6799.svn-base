<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/usage.proto

namespace GPBMetadata\Google\Api;

class Usage
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ae5020a16676f6f676c652f6170692f75736167652e70726f746f120a67" .
            "6f6f676c652e617069226a0a05557361676512140a0c726571756972656d" .
            "656e747318012003280912240a0572756c657318062003280b32152e676f" .
            "6f676c652e6170692e557361676552756c6512250a1d70726f6475636572" .
            "5f6e6f74696669636174696f6e5f6368616e6e656c180720012809225d0a" .
            "09557361676552756c6512100a0873656c6563746f721801200128091220" .
            "0a18616c6c6f775f756e726567697374657265645f63616c6c7318022001" .
            "2808121c0a14736b69705f736572766963655f636f6e74726f6c18032001" .
            "2808426c0a0e636f6d2e676f6f676c652e617069420a557361676550726f" .
            "746f50015a45676f6f676c652e676f6c616e672e6f72672f67656e70726f" .
            "746f2f676f6f676c65617069732f6170692f73657276696365636f6e6669" .
            "673b73657276696365636f6e666967a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

