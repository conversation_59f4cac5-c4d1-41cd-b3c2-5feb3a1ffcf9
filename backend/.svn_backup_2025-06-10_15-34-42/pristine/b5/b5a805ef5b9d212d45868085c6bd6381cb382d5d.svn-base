<?php

/**	\file export-price-watching.php
 * 	Ce fichier exporte les données de l'écran de veille tarifaire dans un fichier sur disque
 * 	($config['doc_dir'].'/export-price-watching-'.$_SESSION['usr_id'].'.csv') et préviens la page
 * 	appelante en retournant le message de success au format json
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRICE_WATCHING');

if( !IS_AJAX || !isset($config['price_watching_active']) || !$config['price_watching_active']) {
	header('location: /admin/index.php');
	exit;
}
require_once('PriceWatching/models/prw_followed_products.inc.php');
require_once('PriceWatching/models/prw_offers.inc.php');
require_once('PriceWatching/models/prw_competitors.inc.php');

// chargement des models
$prw_competitors = new prw_competitors();
$competitors = $prw_competitors->prw_competitors_getActive();
if( !$competitors ){
	$competitors = array();
}
$prw_followed_products = new prw_followed_products();

$is_selection = false;
if( isset($_GET['sel'])){
	$is_selection = true;
}

$ar_followed = array();
$cpt_offers = array();


$followed = $prw_followed_products->prw_followed_products_getAll_enable_group_by_id($is_selection);
if (is_array($followed) && count($followed)) {
	foreach ($followed as $value) {
		$ar_followed[ $value['prd_id'] ] = $value;

		foreach($competitors as $cpt){
			$ar_followed[ $value['prd_id'] ][ 'prc-price-'.$cpt['id'] ] = 0;
			$ar_followed[ $value['prd_id'] ][ 'prc-promo-'.$cpt['id'] ] = 0;
			$ar_followed[ $value['prd_id'] ][ 'prc-ecart-'.$cpt['id'] ] = null;
		}
	}

	$prw_offers = new prw_offers();
	$last_offers = $prw_offers->get_offer_getLast( 0, PRW_CLIENT );
	if ($last_offers) {
		foreach ($last_offers as $prd_id=>$value) {
			if (!array_key_exists($prd_id, $ar_followed)) {
				continue;
			}

			$ar_followed[ $prd_id ][ 'prc-price-'.PRW_CLIENT ] = $value['ListingPrice'];
			$ar_followed[ $prd_id ][ 'prc-promo-'.PRW_CLIENT ] = is_numeric($value['promo_price']) ? $value['promo_price'] : 0;
		}
	}

	foreach($competitors as $cpt ){
		if ($cpt['id'] == PRW_CLIENT) {
			continue;
		}
		$last_offers_cpt = $prw_offers->get_offer_getLast( 0, $cpt['id'] );

		if (is_array($last_offers_cpt)) {
			foreach ($last_offers_cpt as $prd_id=>$value) {
				if (!array_key_exists($prd_id, $ar_followed)) {
					continue;
				}

				$prc_cli = $ar_followed[ $prd_id ][ 'prc-price-'.PRW_CLIENT ];
				if (is_numeric($ar_followed[ $prd_id ][ 'prc-promo-'.PRW_CLIENT ]) && $ar_followed[ $prd_id ][ 'prc-promo-'.PRW_CLIENT ] > 0) {
					$prc_cli = $ar_followed[ $prd_id ][ 'prc-promo-'.PRW_CLIENT ];
				}

				$ar_followed[ $prd_id ][ 'url-'.$cpt['id'] ]        = $value['url'];
				$ar_followed[ $prd_id ][ 'prc-price-'.$cpt['id'] ]  = $value['ListingPrice'];
				$ar_followed[ $prd_id ][ 'prc-ecart-'.$cpt['id'] ]  = $prc_cli - $value['ListingPrice'];
			}
		}
	}

}
if (!isset($_GET['mask-unavailable']) || !isset($_GET['mask-unupdated'])) {
	foreach ($ar_followed as $key => $value) {
		$can_del = false;

		if (!isset($_GET['mask-unavailable'])) {
			$can_del = true;

			foreach($competitors as $cpt ){
				if ($cpt['id'] == PRW_CLIENT) {
					continue;
				}

				if ($ar_followed[ $key ][ 'prc-price-'.$cpt['id'] ] != 0) {
					$can_del = false;
				}
			}
		}

		if (!isset($_GET['mask-unupdated'])) {
			if (trim($ar_followed[ $key ]['last_check']) == '') {
				$can_del = true;
			}
		}elseif (!isset($_GET['mask-unavailable']) && trim($ar_followed[ $key ]['last_check']) == '') {
			$can_del = false;
		}

		if ($can_del) {
			unset($ar_followed[ $key ]);
		}
	}
}
$file_csv = $config['doc_dir'].'/export-price-watching-'.$_SESSION['usr_id'].'.csv';
$handle = fopen($file_csv, 'w');

$headers = array(
	_('Référence'),
	_('Désignation'),
	_('Dernière actualisation'),
	_('Votre prix promo TTC'),
	_('Votre prix TTC')
);
foreach ($competitors as $key => $value){
	$headers[] = _('Prix TTC').' '.$value['name'];
	$headers[] = _('Écart avec').' '.$value['name'];
}

fputcsv($handle,$headers, ';' );

foreach ($ar_followed as $fld) {
	$promo_price = ( isset($fld['prc-promo-'.PRW_CLIENT] ) ? ria_number_format($fld['prc-promo-'.PRW_CLIENT], NumberFormatter::CURRENCY, 2) : '' );
	$cliPrice = ( isset($fld['prc-price-'.PRW_CLIENT] ) ? ria_number_format($fld['prc-price-'.PRW_CLIENT], NumberFormatter::CURRENCY, 2) : '' );

	$line = array(
		$fld['ref'],
		$fld['name'],
		dateheureunparse($fld['last_check']),
		$promo_price,
		$cliPrice,
	);
	foreach ($competitors as $key => $value) {
		$last = $prw_offers->get_offer_getLast( $fld['prd_id'], $value['id'] );
		if( isset($last['ListingPrice']) ){
			$line[] = str_replace('.',',',$last['ListingPrice']).' €';
		}elseif( empty($fld['last_check']) ){
			$line[] = _('En cours d\'actualisation');
		}else{
			$line[] = _('Non disponible');
		}

		if( isset($last['ListingPrice']) ){
			$dif = $price - $last['ListingPrice'];
			if( $dif > 0 ){
				$line[] = '+' . number_format( $dif, 2, ',', '') . ' €';
			}elseif( $dif < 0 ){
				$line[] = number_format( $dif, 2, ',', '') . ' €';
			}else{
				$line[] = '0,00 €';
			}
		}elseif( empty($fld['last_check']) ){
			$line[] = _('En cours d\'actualisation');
		}else{
			$line[] = _('Non disponible');
		}
	}
	fputcsv($handle,$line, ';' );
}

fclose($handle);

echo json_encode(array('success'=> true));
exit;