<?php

// Dépendances
require_once('db.inc.php');
require_once('define.inc.php');
require_once('categories.inc.php');
require_once('brands.inc.php');
require_once('search_fields.inc.php');
require_once('search.inc.php');
require_once('users.inc.php');
require_once('rewrite.inc.php');
require_once('strings.inc.php');
require_once('email.inc.php');
require_once('cfg.emails.inc.php');
require_once('fields.inc.php');
require_once('antispam.inc.php');
require_once('messages.inc.php');
require_once('obj_position.inc.php');
require_once('delivery.inc.php');
require_once('prices.inc.php');
require_once('gu.categories.inc.php');
require_once('keywords.inc.php');
require_once('exports.inc.php');
require_once('ria.queue.inc.php');

// Sous-modules
require_once('prd.stocks.inc.php');
require_once('prd/restrictions.inc.php');
require_once('prd/deposits.inc.php');
require_once('prd/suppliers.inc.php');
require_once('prd/tvas.inc.php');
require_once('prd/images.inc.php');
require_once('prd/search-results.inc.php');
require_once('prd/classify.inc.php');
require_once('prd/hierarchy.inc.php');
require_once('prd/parents.inc.php');
require_once('prd/childs.inc.php');
require_once('prd/suppliers/references.inc.php');
require_once('prd/exports.inc.php');
require_once('prd/missing.inc.php');
require_once('prd/rewards.inc.php');
require_once('prd/stocks.inc.php');
require_once('prd/rewritemap.inc.php');
require_once('prd/positions.inc.php');
require_once('prd/stats.inc.php');

/** \defgroup pim_products Produits
 *	\ingroup pim
 *	Ce module comprend les fonctions nécessaires à la gestion des produits.
 *	Les fonctions permettant la gestion des tables annexes sont regroupées dans des modules séparés.
 *	@{
 */

// \cond onlyria
/*	Cette fonction permet de mettre à null la date de dernière mise à jour des caches où se trouvent le produit
 *	@param int $prd Obligatoire, identifiant du produit
 *	@return bool Retourne true les mises à jours se sont bien passés
 *	@return bool Retourne false dans le cas contraire
 */
function prd_products_reset_update_cache( $prd ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	// Met à null la date de modification des caches dans lesquels se trouve le produits
	$rclassify = prd_classify_get(false, $prd);

	// La présence du produit dans le moteur de recherche se fait par le lien avec la catégorie.
	// Pour chaque lien, on regarde si des caches y sont rattachés et met leur date de dernière mise à jour à null
	if( $rclassify!=false ){
		while( $classify = ria_mysql_fetch_array($rclassify) ){
			if( !search_caches_update_date_modified(0, $classify['cnt']) )
				return false;
		}
	}

	return true;
}
// \endcond


// \cond onlyria
/** Permet l'ajout d'un produit. Les produits n'ayant aucun nom ne peuvent pas être enregistrés
 *	dans la base de données.
 *
 *	@param string $ref Référence
 *	@param string $name Nom du produit
 *	@param string $desc Description
 *	@param $brand Identifiant de la marque
 *	@param bool $publish Détermine si le produit est affiché dans la boutique ou non
 *	@param $weight Poids du produit, en gramme
 *	@param $length Longueur du produit, en centimètres
 *	@param $width Largeur du produit, en centimètres
 *	@param $height Largeur du produit, en centimètres
 *	@param string $keywords Mots clés complémentaires, utilisés par le moteur de recherche
 *	@param bool $is_sync Optionnel, booléen indiquant si le produit est synchronisé avec la gestion commerciale
 *	@param string $taxcode Optionnel, code douanier
 *
 *	@return int L'identifiant du produit en cas de succès, false en cas d'échec
 *
 */
function prd_products_add( $ref, $name, $desc='', $brand=0, $publish=false, $weight=0, $length=0, $width=0, $height=0, $keywords='', $is_sync=false, $taxcode='' ){
	global $config;

	// Protection sur les paramètres d'entrée
	if( !trim($ref) ) return false;
	if( !is_numeric($brand) ) return false;
	if( trim($name)=='' ) return false;
	$weight = preg_replace( '/\s/', '', $weight ); if( !is_numeric($weight) || $weight<=0 ) $weight = 'null';
	$length = preg_replace( '/\s/', '', $length ); if( !is_numeric($length) || $length<=0 ) $length = 'null';
	$width  = preg_replace( '/\s/', '', $width );  if( !is_numeric($width) || $width<=0 ) $width = 'null';
	$height = preg_replace( '/\s/', '', $height ); if( !is_numeric($height) || $height<=0 ) $height = 'null';

	// Vérifie l'identifiant de la marque
	if( $brand>0 && !prd_brands_exists($brand) )
		return false;

	if( $brand==0 ) $brand = 'null';
	$ref = trim(strtoupper2($ref));
	$name = trim(ucfirst($name));
	$desc = trim(ucfirst(sanitize($desc)));
	$keywords = ucfirst(trim($keywords));

	$orderable = true;
	if( $config['tnt_id']==1 ){
		$orderable = substr($ref,-2,2)!='RG';
	}
	$follow_stock = !isset($config['products_follow_stock_default']) || $config['products_follow_stock_default'];

	$sql = '
	insert into prd_products (
	prd_tnt_id, prd_ref, prd_name, prd_desc, prd_publish, prd_date_published, prd_brd_id, prd_weight, prd_length,
	prd_width, prd_height, prd_keywords, prd_date_created, prd_orderable, prd_is_sync, prd_taxcode, prd_follow_stock
	) values (
	'.$config['tnt_id'].', "'.addslashes($ref).'", "'.addslashes($name).'", "'.addslashes($desc).'", 0, null, '.$brand.',
	'.$weight.', '.$length.', '.$width.', '.$height.', "'.addslashes($keywords).'", now(), '.($orderable ? 1 : 0).',
	'.($is_sync ? 1 : 0).', "'.addslashes($taxcode).'", '.($follow_stock ? 1 : 0).'
	)
	';

	if( ria_mysql_query($sql) ){
		$id = ria_mysql_insert_id();
	}else{
		return false;
	}

	// Inutile d'indexer le produit, car celui-ci n'est pas accessible dans la boutique
	// tant qu'il n'est pas classé dans une catégorie.
	if( $publish ){
		prd_products_publish( $id );
	}

	// Met à jour le nombre de produits de la marque
	if( $brand!='null' ){
		prd_brands_update_products($brand);
	}

	return $id;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de définir si un produit est synchronisé avec la gestion commerciale, ou non.
 *	@param int $prd Identifiant du produit
 *	@param bool $is_sync Vrai si le produit est synchronisé, faux dans le cas contraire
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_products_set_is_sync( $prd, $is_sync ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	global $config;

	return ria_mysql_query('update prd_products set prd_is_sync='.( $is_sync ? 1 : 0 ).' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'interrogation de la propriété is_sync d'un produit donné.
 *	@param int $prd Identifiant du produit à interroger
 *	@return bool true si le produit est synchronisé avec la gestion commerciale, false dans le cas contraire
 */
function prd_products_get_is_sync( $prd ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	global $config;

	return ria_mysql_result(ria_mysql_query('select prd_is_sync from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd),0,0);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information d'indexation d'un produit
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param $index Obligatoire, mettre True pour indexer le produit, False pour ne pas indéxer
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_products_set_index( $prd_id, $index ){
	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		update prd_products
		set prd_no_index = '.($index ? '0' : '1').'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$prd_id);

	try{
		// Index le produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_PRODUCT,
			'obj_id_0' => $prd_id,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction est chargée de recalculer les produits devant apparaître dans la rubrique "Meilleures ventes".
 *	Le mode de mise à jour choisi évite un effet de blanc si un utilisateur consulte la liste des meilleures ventes pendant la mise à jour.
 *	Le calcul est effectué catégorie par catégorie, afin de disposer d'une liste des meilleures ventes pour chacune.
 */
function prd_products_set_bestsellers(){
	global $config;

	$bench_start = microtime(true);

	// Ce tableau sert au stockage temporaire du nombre de points attribués à un produit pour son classement
	// dans la liste des bestsellers
	$bestsellers = array();
	$limit = 100;

	// Charge la liste des produits contenus dans la ca+e, triés par nombre de ventes décroissant
	// Le premier produit reçoit 5 points, le deuxième 4 points, etc...
	$products = ria_mysql_query('
		select prd_id as id from prd_products
		where prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null
			and prd_orderable and not prd_sleep and prd_publish and prd_img_id is not null
			and prd_bestseller in ("0","1")
		order by prd_selled desc
		limit 0,'.$limit.'
	');

	$points = $limit;
	while( $p = ria_mysql_fetch_array($products) ){
		$bestsellers[ $p['id'] ] = $points-- * $config['bestsellers_weight_selled'];
	}

	$max_age = isset($config['bestsellers_max_age']) ? $config['bestsellers_max_age'] : 60;

	// Charge la liste des produits contenus dans la catégorie, triés par chiffre d'affaires décroissant
	// Le premier produit reçoit 5 points, le deuxième 4 points, etc...
	$products = ria_mysql_query('
		select p.prd_id as id from prd_products as p, ord_products as o, ord_orders
		where  p.prd_tnt_id='.$config['tnt_id'].' and o.prd_tnt_id=p.prd_tnt_id and ord_tnt_id=p.prd_tnt_id and p.prd_id=o.prd_id and o.prd_ord_id=ord_id and prd_date_deleted is null
			and prd_orderable and not prd_sleep and prd_publish and prd_img_id is not null
			and prd_bestseller in ("0","1") and datediff(now(),ord_date)<'.$max_age.'
		group by p.prd_id
		order by sum(prd_price_ht) desc
		limit 0,'.$limit.'
	');

	$points = $limit;
	while( $p = ria_mysql_fetch_array($products) ){
		if( !isset($bestsellers[ $p['id'] ]) ) $bestsellers[ $p['id'] ] = 0;
		$bestsellers[ $p['id'] ] += $points-- * $config['bestsellers_weight_ca'];
	}

	// Charge la liste des produits contenus dans la catégorie, triés par quantités vendues décroissant
	// Le premier produit reçoit 5 points, le deuxième 4 points, etc...
	$products = ria_mysql_query('
		select p.prd_id as id from prd_products as p, ord_products as o, ord_orders
		where  p.prd_tnt_id='.$config['tnt_id'].' and o.prd_tnt_id=p.prd_tnt_id and ord_tnt_id=p.prd_tnt_id and p.prd_id=o.prd_id and o.prd_ord_id=ord_id and prd_date_deleted is null
			and prd_orderable and not prd_sleep and prd_publish and prd_img_id is not null
			and prd_bestseller in ("0","1") and datediff(now(),ord_date)<'.$max_age.'
		group by p.prd_id
		order by sum(prd_qte) desc
		limit 0,'.$limit.'
	');

	$points = $limit;
	while( $p = ria_mysql_fetch_array($products) ){
		if( !isset($bestsellers[ $p['id'] ]) ) $bestsellers[ $p['id'] ] = 0;
		$bestsellers[ $p['id'] ] += $points-- * $config['bestsellers_weight_qte'];
	}

	// Ne conserve que les 5 produits ayant cumulé le plus de points
	arsort($bestsellers);

	if( sizeof($bestsellers) ){
		foreach( $bestsellers as $prd => $points ){
			ria_mysql_query('
				update prd_products set prd_bestseller_point='.$points.'
				where prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null and prd_id='.$prd
			);
			if( ria_mysql_errno() ){
				error_log( __FILE__.':'.__LINE__.' '.mysql_error() );
			}
		}

		ria_mysql_query('
			update prd_products set prd_bestseller_point=0
			where prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null
				and prd_id not in ('.implode( ',', array_keys($bestsellers) ).')'
		);
		if( ria_mysql_errno() ){
			error_log( __FILE__.':'.__LINE__.' '.mysql_error() );
		}

	}

	error_log( __FILE__.':'.__LINE__.' temps d\'exécution de prd_products_set_bestsellers() pour le locataire '.$config['tnt_id'].' : '.( microtime(true) - $bench_start ).'\n', 3, '/var/log/php/bench_cron.log' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de gérer l'information de publication d'un produit parent en fonction de ses enfants.
 *	On récupère les articles parents en fonction des enfants ($childs) sauf s'ils sont déjà passés en paramètre ($parents).
 *	Au moins un des deux paramètres obligatoires ($childs, $parents) doit être donnés.
 *
 *	@param int|array $childs Identifiant ou tableau d'identifiants des articles enfants
 *	@param int $parents Identifiant ou tableau d'identifiants des articles parents
 *	@param $no_action Optionnel, par défaut l'information de publication du parent est mise à jour, mettre true pour pré-visualiser (aucune mise à jour sera alors faite)
 *	@return bool False si un des paramètres est faux ou bien si aucun des deux paramètres obligatoires ($childs, $parents) n'est fournit
 *	@return bool True si la mise à jour s'est correctement déroulée
 */
function prd_products_set_parent_publish( $childs=0, $parents=0, $no_action=false ){
	global $config;

	if (!isset($config['catalog_parent_publish']) || $config['catalog_parent_publish'] != 'auto') {
		return true;
	}

	$childs = control_array_integer( $childs, false );
	if ($childs === false) {
		return false;
	}

	$parents = control_array_integer( $parents, false );
	if ($parents === false) {
		return false;
	}

	if (!count($childs) && !count($parents)) {
		return false;
	}

	if (!count($parents)) {
		// Récupère les articles parents
		$c_parents = prd_products_get_parents_ids( $childs );
		$parents = array_merge( $c_parents, $childs );
	}

	if (!is_array($parents) || !count($parents)) {
		return true;
	}

	// Aucune action possible sur les articles parents synchronisés.
	// Leur publication / dépublication doit venir de la gestion commerciale.
	$r_parent = ria_mysql_query('
		select prd_id as id, prd_publish as publish
		from prd_products as p
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id in ('.implode(', ', $parents).')
			and prd_date_deleted is null and prd_is_sync = 0
			and exists (
				select 1
				from prd_hierarchy as h
				where h.prd_tnt_id = '.$config['tnt_id'].'
					and h.prd_parent_id = p.prd_id
			)
	');

	if (!$r_parent) {
		return false;
	}

	$ar_publish_prds 	= array();
	$ar_unpublish_prds 	= array();

	while ($parent = ria_mysql_fetch_assoc($r_parent)) {
		$res = ria_mysql_query('
			select 1
			from prd_products as p
				join prd_hierarchy as h on (h.prd_tnt_id = p.prd_tnt_id and prd_child_id = prd_id and prd_parent_id = '.$parent['id'].')
			where p.prd_tnt_id = '.$config['tnt_id'].'
				and prd_date_deleted is null
				and prd_publish and prd_publish_cat
		');

		if (!$res) {
			return false;
		}

		$publish = ( ria_mysql_num_rows($res) > 0 );
		if ($publish && !$parent['publish']) {
			$ar_publish_prds[] = $parent['id'];
		}elseif (!$publish && $parent['publish']) {
			$ar_unpublish_prds[] = $parent['id'];
		}
	}

	if ($no_action) {
		print '<pre>';
		print 'Publier : '; print_r( $ar_publish_prds );
		print 'Depublier : '; print_r( $ar_unpublish_prds );
		print '</pre>';
	}else{
		if (count($ar_publish_prds)) {
			if (!prd_products_publish($ar_publish_prds)) {
				return false;
			}
		}

		if (count($ar_unpublish_prds)) {
			if (!prd_products_unpublish($ar_unpublish_prds)) {
				return false;
			}
		}
	}

	return true;
}
// \endcond

/**	Retourne la date de prochaine disponibilité pour un produit donné, ou chaine vide si aucune date n'est définie.
 *	L'information de date de livraison est soit renseignée manuellement par l'administrateur dans l'interface
 *	d'administration, soit renseignée via SAGE si le champ extra date_dispo est disponible.
 *	@param int $prd Identifiant du produit à interroger
 *	@param $version_en Optionnel, par défaut la date est retourné au format dd/mm/aaaa, mettre true pour la retourné au format yyyy-mm-dd
 *	@return la date de prochaine livraison, au format dd/mm/aaaa
 */
function prd_products_date_available( $prd, $version_en=false ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	global $config;

	$sub_sql = !$version_en ? 'date_format(prd_stock_livr,"%d/%m/%Y")' : 'date_format(prd_stock_livr,"%Y-%m-%d")';
	$res = ria_mysql_query('
		select if(prd_stock_livr<ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY),null,'.$sub_sql. ') as date_available
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
		and prd_id='.$prd.'
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['date_available'];
}

// \cond onlyria
/** Cette fonction permet de savoir si un produit est indexé donc s'il doit sortir dans le moteur de recherche et le sitemap
 *	@param int $prd_id Obligatoire, identifiant du produit
 *	@param $classify Optionnel, par défaut, on regardera si l'un de ses classements publiés est indexé
 *	@param int $cat_id Optionnel, identifiant d'une catégorie (ignoré si $classify=false), permet de vérifier si un classement est indexé.
 *	@return bool True si elle est indexée, False dans le cas contraire
 */
function prd_products_is_index( $prd_id, $classify=true, $cat_id=0 ){
	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}

	if (!is_numeric($cat_id) || $cat_id < 0) {
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select 1
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$prd_id.'
			and prd_no_index = 1
	');

	if ($res && ria_mysql_num_rows($res)) {
		return false;
	}

	$index = true;

	if ($classify) {
		$index = false;

		$sql_cly = '
			select cly_cat_id as cat, cat_no_index as no_index
			from prd_classify
				join prd_categories on (cat_tnt_id = cly_tnt_id and cat_id = cly_cat_id )
			where cly_tnt_id = '.$config['tnt_id'].'
				and cly_prd_id = '.$prd_id.'
				and cat_publish = 1
		';

		if ($cat_id) {
			$sql_cly .= ' and cly_cat_id = '.$cat_id;
		}

		$res = ria_mysql_query( $sql_cly );
		if ($res) {
			while ($r = ria_mysql_fetch_assoc($res)) {
				if ($r['no_index'] == '0') {
					if( prd_categories_is_index($r['cat']) ){
						$index = true;
						break;
					}
				}
			}
		}
	}

	return $index;
}
// \endcond

/**	Cette fonction teste si un produit est disponible, en fonction de son état de stock.
 *	@param int $prd Identifiant du produit à interroger
 *	@return bool true si le produit est disponible
 *	@return bool false si le produit n'est pas disponible
 *	@return bool false si le produit n'a pas été trouvé
 */
function prd_products_is_available( $prd ){
	$rprd = prd_products_get_simple( $prd, '', true, 0, false, false, false, false, array('childs'=>true) );
	if( !ria_mysql_num_rows($rprd) ){
		return false;
	}
	$prd = ria_mysql_fetch_assoc($rprd);

	if( !$prd['orderable'] ){
		return false; // produit non-commandable.
	}
	global $config;

	if (in_array($config['tnt_id'], [588, 1279])) {

		$res = ria_mysql_query('
			select
				dps.dps_id as id,
				dps.dps_name as name,
				fld2.pv_value as agency
			from prd_deposits dps
			right join fld_object_values fld1
			on
				fld1.pv_tnt_id=' . $config['tnt_id'] . '
			and fld1.pv_fld_id= 114450
			and fld1.pv_obj_id_0=dps.dps_id
			and lower(fld1.pv_value) in ("yes", "oui", 1)
			left join fld_object_values fld2
			on
				fld2.pv_tnt_id=' . $config['tnt_id'] . '
			and fld2.pv_fld_id= 114506
			and fld2.pv_obj_id_0=dps.dps_id
			where
				dps.dps_tnt_id=' . $config['tnt_id'] . '
			and dps.dps_is_deleted=0
		');

		if (!ria_mysql_num_rows($res)) {
			return false;
		}
		$is_parent = prd_products_is_parent($prd['id']);
		$stock = 0;

		while ($dps = ria_mysql_fetch_assoc($res)) {

			if ($is_parent) {
				$rstock = ria_mysql_query('
					select
						ifnull(' . ($config['use_decimal_qte'] ? 'sum(' . prd_stocks_get_sql() . '-sto_prepa)' : 'cast(sum(' . prd_stocks_get_sql() . '-sto_prepa) as signed)') . ', 0) as child_qte
					from prd_stocks

					right join prd_hierarchy h
					on
						sto_tnt_id=h.prd_tnt_id
					and sto_prd_id=h.prd_child_id
					and h.prd_parent_id=' . $prd['id'] . '

					right join prd_products p
					on
						p.prd_id=sto_prd_id
					and p.prd_tnt_id=sto_tnt_id
					and p.prd_publish=1
					and p.prd_publish_cat=1
					and p.prd_orderable=1
					and p.prd_date_deleted is null

					where
						sto_tnt_id=' . $config['tnt_id'] . '
					and sto_dps_id=' . $dps['id'] . '
					and sto_is_deleted=0
				');

				if (!ria_mysql_num_rows($rstock)) {
					continue;
				}
				$stock += ria_mysql_result($rstock, 0, 'child_qte');
				continue;
			}

			$rstock = ria_mysql_query('
				select
					ifnull(' . ($config['use_decimal_qte'] ? prd_stocks_get_sql() . '-sto_prepa' . prd_reservations_get_sql_where_stock($prd['id'], $dps, true) : 'cast(' . prd_stocks_get_sql() . '-sto_prepa' . prd_reservations_get_sql_where_stock($prd['id'], $dps, true) . ' as signed)') . ', 0) as stock
				from prd_stocks
				where
					sto_tnt_id=' . $config['tnt_id'] . '
				and sto_prd_id=' . $prd['id'] . '
				and sto_dps_id=' . $dps['id'] . '
				and sto_is_deleted=0
			');

			if (!ria_mysql_num_rows($rstock)) {
				continue;
			}
			$stock += ria_mysql_result($rstock, 0, 'stock');
		}
		$prd['stock'] = $stock;

		return !$prd['follow_stock'] || $prd['stock'] > 0;

	}

	if( !$prd['follow_stock'] || $prd['stock']>0 ){
		return true; // disponible, et peut déjà être commandée
	}

	if( $prd['centralized']==0  ){
		return true; // disponible, et peut déjà être commandée.
	}

	if( $prd['countermark']!=0 ){
		return true; // disponible, et peut déjà être commandée.
	}
	return false;
}

// \cond onlyria
/**	Cette fonction va vérifier si au moins l'une des catégories dans lesquelles il est publié lui permettent
 *	d'apparaître dans la boutique en ligne. Cette information est précalculée pour accélérer le chargement des
 *	listes de produits dans la boutique.
 *	@param int $prd Produit a tester
 *	@return bool true si le produit est publié par au moins une de ses catégories
 *	@return bool false si le produit n'est publié par aucune de ses catégories
 */
function prd_products_cat_published( $prd ){
	global $config;

	if( !is_numeric($prd) ) return false;

	$soldes = pmt_soldes_get_next_period();

	// Charge les catégories parents (directes) offrant la publication
	$categories = ria_mysql_query('
		select cat_id as id
		from prd_classify, prd_categories
		where cly_tnt_id='.$config['tnt_id'].'
			and cat_tnt_id=cly_tnt_id
			and cly_cat_id=cat_id
			and cat_publish!=0
			and cly_prd_id='.$prd.'
			and cat_date_deleted is null
			and cat_date_from<=now() and (cat_date_to is null or cat_date_to>now())
			and (cat_is_soldes = 0 or (now()>="'.$soldes['start']['date'].'" and now()<="'.$soldes['stop']['date'].'"))
	');

	// Vérifie que l'arborescence au dessus de ces catégories permet leur publication
	// Une seule catégorie suffit pour que le produit puisse apparaître dans la boutique
	while( $c = ria_mysql_fetch_array($categories) ){
		if( prd_categories_is_published($c['id']) ){
			return 1;
		}
	}
	return 0;
}
// \endcond

// \cond onlyria
/** Permet la modification d'un produit. Les produits n'ayant aucun nom ne peuvent pas être enregistrés
 *	dans la base de données.
 *
 *	Si le produit n'existe pas dans la base de données, cette fonction retourne false.
 *
 *	@param int $id Identifiant du produit à modifier
 *	@param string $ref Nouvelle Référence du produit
 *	@param string $name Nouveau nom du produit
 *	@param string $desc Description du produit
 *	@param $brand Identifiant de la marque
 *	@param bool $publish Détermine si le produit est affiché dans la boutique ou non
 *	@param $weight Poids du produit, en gramme
 *	@param $length Longueur du produit, en centimètres
 *	@param $width Largeur du produit, en centimètres
 *	@param $height Hauteur du produit, en centimètres
 *	@param string $keywords Mots clés complémentaires, utilisés par le moteur de recherche
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
function prd_products_update( $id, $ref, $name, $desc='', $brand=0, $publish=0, $weight=0, $length=0, $width=0, $height=0, $keywords='' ){
	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	global $config;

	// Protection sur les paramètres d'entrée
	if( !is_numeric($brand) || $brand<0 ) return false;
	if( !trim($ref) ) return false;
	if( !trim($name) ) return false;
	$weight = preg_replace( '/\s/', '', $weight );
	$length = preg_replace( '/\s/', '', $length );
	$width  = preg_replace( '/\s/', '', $width );
	$height = preg_replace( '/\s/', '', $height );

	// Charge le produit pour comparaison
	$prd = ria_mysql_query('
		select	prd_id as id, prd_ref as ref, prd_name as name, prd_desc as "desc", prd_brd_id as brd_id,
		prd_weight as weight, prd_length as length, prd_width as width, prd_height as height,
		prd_keywords as keywords, prd_publish as publish, prd_publish_cat as publish_cat, prd_url_rebuild as url_rebuild
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id.'
		limit 0, 1
		');

	// Charge le produit pour comparaison
	if( !($p = ria_mysql_fetch_array($prd)) )
		return false;

	// Mise en forme
	$ref = trim(strtoupper2($ref));
	$name = trim(ucfirst($name));
	$desc = trim(ucfirst(sanitize($desc)));
	$keywords = ucfirst(trim($keywords));

	// Compare les 2 versions, pour s'éviter des opérations lourdes si aucune modification n'a été faite
	if( $p['ref']==$ref && $p['name']==$name && $p['desc']==$desc && $p['brd_id']==$brand ){
		if( $p['weight']==$weight && $p['length']==$length && $p['width']==$width && $p['height']==$height ){
			if( $p['keywords']==$keywords && abs($p['publish'])==abs($publish) ){
				prd_products_set_parent_publish($id);
				return true;
			}
		}
	}

	if( !is_numeric($weight) || $weight<=0 ) $weight = 'null';
	if( !is_numeric($length) || $length<=0 ) $length = 'null';
	if( !is_numeric($width) || $width<=0 ) $width = 'null';
	if( !is_numeric($height) || $height<=0 ) $height = 'null';

	// Vérifie l'identifiant de la marque
	if( $brand<=0 )
		$brand = 'null';
	elseif( !prd_brands_exists($brand) )
		return false;

	$result = ria_mysql_query(
		"update prd_products set prd_ref='".addslashes($ref)."', prd_name='".addslashes($name)."', prd_desc='".addslashes($desc)."',".
		"prd_brd_id=".$brand.", ".
		"prd_weight=".$weight.", prd_length=".$length.", prd_width=".$width.", prd_height=".$height.", prd_keywords='".addslashes($keywords)."' ".
		"where prd_tnt_id=".$config['tnt_id']." and prd_id=".$id
		);

	if( $result ){
		// Publie/Dé-publie le produit
		if( $publish && !$p['publish'] )
			prd_products_publish($id);
		elseif( !$publish && $p['publish'] )
			prd_products_unpublish($id);

		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		// Met à jour le nombre de produits des marques concernées
		if( $brand )
			prd_brands_update_products($brand);
		if( $p['brd_id'] )
			prd_brands_update_products($p['brd_id']);
		// Met à jour le nombre de produits publiés dans la catégorie
		$categories = prd_products_categories_get($id);
		while( $r = ria_mysql_fetch_array($categories) )
			prd_categories_refresh_products_published($r['cat']);
		// Met à jour le taux de remplissage du produit
		prd_products_update_completion( $id );

		// Reconstruit l'url du produit si demandé
		if( $p['url_rebuild'] && $p['name']!=$name ){
			$rcly = prd_classify_get( false, $id );
			if( $rcly && ria_mysql_num_rows($rcly) ){
				while( $cly = ria_mysql_fetch_array($rcly) ){
					$new_url = prd_products_url_alias_add( $id, $cly['cat'] );
					if( $new_url != $cly['url'] ){
						// créer les redirections 301
						rew_rewritemap_add( $cly['url'], $new_url, 301, 0, false, CLS_PRODUCT, array($cly['cat'], $cly['prd']) );
						ria_mysql_query('delete from rew_rewritemap where url_tnt_id='.$config['tnt_id'].' and url_code=200 and url_extern="'.addslashes( $cly['url'] ).'"');
						rew_rewritemap_clean_cache( array($cly['url'], $new_url) );
					}
				}
			}

			prd_products_set_url_rebuild( $id, false );
		}

		prd_products_set_parent_publish($id);
	}

	return $result;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du nom du produit, sans incidence sur les autres champs.
 *	@param int $id Obligatoire, identifiant du produit
 *	@param string $name Obligatoire, nouvelle désignation du produit
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_products_update_name( $id, $name ){
	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	global $config;

	$name = trim(ucfirst($name));
	if( $name=='' ) return false;

	$result = ria_mysql_query('
		update prd_products set prd_name=\''.addslashes($name).'\' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id.'
		');
	if( $result ){
		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		// Reconstruit l'url du produit si demandé
		$rebuild = ria_mysql_query('select 1 from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id.' and prd_url_rebuild=1');
		if( $rebuild && ria_mysql_num_rows($rebuild) ){
			$rcly = prd_classify_get( false, $id );
			if( $rcly && ria_mysql_num_rows($rcly) ){
				while( $cly = ria_mysql_fetch_array($rcly) ){
					$new_url = prd_products_url_alias_add( $id, $cly['cat'] );
					if( $new_url != $cly['url'] ){
						// créer les redirections 301
						rew_rewritemap_add( $cly['url'], $new_url, 301, 0, false, CLS_PRODUCT, array($cly['cat'], $cly['prd']) );
						ria_mysql_query('delete from rew_rewritemap where url_tnt_id='.$config['tnt_id'].' and url_code=200 and url_extern="'.addslashes( $cly['url'] ).'"');
						rew_rewritemap_clean_cache( array($cly['url'], $new_url) );
					}
				}
			}

			prd_products_set_url_rebuild( $id, false );
		}
	}

	return $result;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du titre du produit. Le titre est utilisé pour surchargé le nom du produit
 *	dans le cas ou celui-ci ne peut pas être modifié dans la gestion commerciale.
 *
 *	@param int $id Obligatoire, identifiant du produit à mettre à jour
 *	@param string $title Obligatoire, titre du produit
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_products_update_title( $id, $title ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$title = trim(
		ucfirst($title)
	);

	$result = ria_mysql_query('
		update prd_products
		set prd_title=\''.addslashes($title).'\'
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$id
	);

	if( $result ){
		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		// Reconstruit l'url du produit si demandé.
		$rebuild = ria_mysql_query('
			select 1
			from prd_products
			where prd_tnt_id='.$config['tnt_id'].'
				and prd_id='.$id.'
				and prd_url_rebuild=1
		');

		if( $rebuild && ria_mysql_num_rows($rebuild) ){
			$rcly = prd_classify_get(false, $id);

			if( $rcly && ria_mysql_num_rows($rcly) ){
				while( $cly = ria_mysql_fetch_array($rcly) ){
					$new_url = prd_products_url_alias_add($id, $cly['cat']);

					if( $new_url != $cly['url'] ){
						// créer les redirections 301
						rew_rewritemap_add($cly['url'], $new_url, 301, 0, false, CLS_PRODUCT, array($cly['cat'], $cly['prd']));
						ria_mysql_query('delete from rew_rewritemap where url_tnt_id='.$config['tnt_id'].' and url_code=200 and url_extern="'.addslashes( $cly['url'] ).'"');
						rew_rewritemap_clean_cache(array($cly['url'], $new_url));
					}
				}
			}

			prd_products_set_url_rebuild($id, false);
		}
	}

	return $result;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour de la marque du produit, sans incidence sur les autres champs.
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param int $brd Obligatoire, identifiant de la nouvelle marque du produit
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_products_update_brand( $prd, $brd ){
	if( !is_numeric($prd) || $prd <= 0 || !prd_brands_exists($brd) ){
		return false;
	}

	global $config;

	// Récupère l'ancienne marque du produit pour mettre à jour son nombre de produit.
	$rbrd = ria_mysql_query('select prd_brd_id as brd from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);
	if( $rbrd && ria_mysql_num_rows($rbrd) ){
		$old_brd = ria_mysql_result($rbrd, 0, 'brd');
	}

	$result = ria_mysql_query('
		update prd_products
		set prd_brd_id='.$brd.'
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$prd
	);

	if( $result ){
		try{
			// Réindex le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $prd,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		// Met à jour le nombre de produits publiés dans la marque (et l'ancienne marque).
		prd_brands_update_products($brd);

		if( $old_brd > 0 ){
			prd_brands_update_products($old_brd);
		}
	}

	return $result;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour des informations de dimensions du produit.
 *	@param int $prd Identifiant du produit à mettre à jour
 *	@param $weight Poids brut du produit, en grammes
 *	@param $width Largeur du produit, en centimètres
 *	@param $height Hauteur du produit, en centimètres
 *	@param $length Longueur/Profondeur du produit, en centimètres
 *	@return bool true en cas de succès
 *	@return bool false en cas d'erreur
 */
function prd_products_update_size( $prd, $weight, $width, $height, $length ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	global $config;

	$weight = preg_replace( '/\s/', '', $weight ); if( !is_numeric($weight) || $weight<=0 ) $weight = 'null';
	$width  = preg_replace( '/\s/', '', $width );  if( !is_numeric($width) || $width<=0 ) $width = 'null';
	$height = preg_replace( '/\s/', '', $height ); if( !is_numeric($height) || $height<=0 ) $height = 'null';
	$length = preg_replace( '/\s/', '', $length ); if( !is_numeric($length) || $length<=0 ) $length = 'null';

	$result = ria_mysql_query('update prd_products set prd_weight='.$weight.', prd_width='.$width.', prd_height='.$height.', prd_length='.$length.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);
	if( $result ) prd_products_update_completion( $prd );
	return $result;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du poids d'un produit.
 *	@param int $prd Identifiant du produit à mettre à jour
 *	@param $brut Poids brut du produit
 *	@param $net Poids net du produit
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_products_update_weight( $prd, $brut, $net ){
	global $config;

	$net = str_replace(" ", '', $net);
	$brut = str_replace(" ", '', $brut);
	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($brut) || $brut<=0 ) $brut = 'null';
	if( !is_numeric($net) || $net<=0 ) $net = 'null';

	$result = ria_mysql_query('update prd_products set prd_weight='.$brut.', prd_weight_net='.$net.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);
	if( $result ) prd_products_update_completion( $prd );
	return $result;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la mise à jour du référencement pour un produit donné.
 *	@param int $prd Obligatoire, Identifiant du produit
 *	@param string $tag_title Optionnel, il s'agit du contenu de la balise title
 *	@param string $tag_desc Optionnel, il s'agit du contenu de la balise descroption
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_products_update_referencing( $prd, $tag_title='', $tag_desc='' ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	global $config;

	return ria_mysql_query('update prd_products set prd_tag_title="'.addslashes( $tag_title ).'", prd_tag_desc="'.addslashes( $tag_desc ).'" where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour des mots clés spécifiques à un produit donné.
 *	@param int $prd Identifiant du produit à mettre à jour
 *	@param string $keywords Mots clés à attribuer au produit
 *	@return bool true en cas de succès
 *	@return bool false en cas d'erreur
 */
function prd_products_update_keywords( $prd, $keywords ){
	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	global $config;

	$keywords = str_replace(array("\r", "\n"), array(' ', ' '), $keywords);

	ria_mysql_query('
		update prd_products
		set prd_keywords="'.addslashes($keywords).'"
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$prd
	);

	try{
		// Index le produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
			'cls_id' => CLS_PRODUCT,
			'obj_id_0' => $prd,
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour de la description du produit, en laissant intact les autres champs.
 *	Elle est principalement utile pour la synchronisation avec une base de données externe.
 *
 *	Après modification de la description du produit, cette fonction déclenche sa réindexation par le moteur
 *	de recherche.
 *
 *	@param int $id Obligatoire, identifiant du produit
 *	@param string $desc Obligatoire, description du produit
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function prd_products_update_desc( $id, $desc ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$desc = trim(
		ucfirst(sanitize($desc))
	);

	$old_desc = prd_products_get_desc($id);

	if( $old_desc === false ){
		return false;
	}

	if( $desc == $old_desc ){
		return true;
	}

	$res = ria_mysql_query('
		update prd_products
		set prd_desc=\''.addslashes($desc).'\'
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$id
	);

	if( $res ){
		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		prd_products_update_completion($id);
	}

	return $res;
}
// \endcond

/** Récupère la description courte d'un produit
 *	@param int $id identifiant du produit
 *	@return string|bool La description courte du produit, False en cas d'échec
 */
function prd_products_get_desc( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query( 'select prd_desc from prd_products where prd_id='.$id.' and prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null' );

	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res, 0, 0 );
}

/** Récupère la description longue d'un produit
 *	@param int $id identifiant du produit
 *	@return string|bool La description longue du produit, False en cas d'échec
 */
function prd_products_get_desc_long( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query( 'select prd_desc_long from prd_products where prd_id='.$id.' and prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null' );

	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res, 0, 0 );
}

// \cond onlyria
/**	Cette fonction permet la mise à jour du poids net du produit, en laissant intact les autres champs.
 *	Elle est principalement utile pour la synchronisation avec une base de données externe.
 *
 *	@param int $id Obligatoire, identifiant du produit
 *	@param int $weight Obligatoire, poids net du produit, en grammes
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function prd_products_update_weight_net( $id, $weight ){
	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	global $config;

	$weight = str_replace( ' ', '', $weight );
	if( !is_numeric($weight) ) return false;

	return ria_mysql_query('update prd_products set prd_weight_net='.addslashes($weight).' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du poids brut du produit, en laissant intact les autres champs.
 *	Elle est principalement utile pour la synchronisation avec une base de données externe.
 *
 *	@param int $id Obligatoire, identifiant du produit
 *	@param int $weight Obligatoire, poids net du produit, en grammes
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function prd_products_update_weight_brut( $id, $weight ){
	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	global $config;

	$weight = str_replace( ' ', '', $weight );
	if( !is_numeric($weight) ) return false;

	return ria_mysql_query('update prd_products set prd_weight='.addslashes($weight).' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du code douanier du produit, en laissant intact les autres champs.
 *	Elle est principalement utile pour la synchronisation avec une base de données externe.
 *
 *	@param int $id Obligatoire, identifiant du produit
 *	@param string $taxcode Obligatoire, code douanier du produit
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function prd_products_update_taxcode( $id, $taxcode ){
	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	global $config;

	return ria_mysql_query('update prd_products set prd_taxcode=\''.addslashes($taxcode).'\' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du code barres d'un produit, en laissant intact les autres champs.
 *	Elle est principalement utile pour la synchronisation avec une base de données externe.
 *
 *	@param int $id Obligatoire, identifiant du produit
 *	@param string $barcode Obligatoire, code barres du produit
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function prd_products_update_barcode( $id, $barcode ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	$barcode = str_replace(' ', '', trim($barcode));

	$res = ria_mysql_query('
		update prd_products
		set prd_barcode=\''.addslashes($barcode).'\'
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$id
	);

	if( $res ){
		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $res;
}
// \endcond

/**	Cette fonction récupère le code-barre d'un produit
 *	@param int $id Identifiant du produit
 *	@return string Le code-barre du produit
 *	@return bool False en cas d'échec
 */
function prd_products_get_barcode( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		select prd_barcode
		from prd_products
		where prd_id='.$id.' and prd_tnt_id='.$config['tnt_id'].'
			and prd_date_deleted is null
	');

	if( !$r || !ria_mysql_num_rows( $r ) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}

// \cond onlyria
/**	Cette fonction est une version simplifiée de prd_products_update pour la synchronisation avec la gestion commerciale.
 *	Elle ne met à jour que les champs concernés par la synchronisation.
 *
 *	@param int $id Identifiant du produit
 *	@param string $ref Référence du produit
 *	@param string $name Désignation du produit
 *	@param bool $publish Booléen indiquant si le produit doit être publié ou non
 *	@param int $brd Identifiant de la marque du produit
 *	@param string $taxcode Code douanier
 *	@param $countermark Optionnel, contremarque oui / non (par défaut, le champ n'est pas mis à jour)
 *
 *	\bug L'url simplifiée du produit n'est pas maintenue à jour par cette fonction.
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 *
 */
function prd_products_update_sage( $id, $ref, $name, $publish, $brd, $taxcode, $countermark=null ){
	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !trim($ref) ){
		return false;
	}
	if( !trim($name) ){
		return false;
	}

	$ref = trim(strtoupper2($ref));
	$name = trim(ucfirst($name));

	if( $brd=='' || $brd==0 ){
		$brd = 'null';
	}elseif( !prd_brands_exists( $brd ) ){
		return false;
	}

	// récupère la précédente marque
	$old_brd = prd_products_get_brand( $id );

	global $config;

	$sql = '
		update prd_products
		set prd_ref = "'.addslashes($ref).'",
			prd_name = "'.addslashes($name).'",
			prd_brd_id = '.$brd.',
			prd_taxcode = "'.addslashes($taxcode).'"
	';
	if( $countermark !== null ){
		$sql .= ', prd_countermark = '.( $countermark ? 1 : 0 ).' ';
	}
	$sql .= '
		where prd_tnt_id = '.$config['tnt_id'].'
		and prd_id = '.$id.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){

		// Met à jour le nombre de produits de l'ancienne marque
		if( $old_brd ){
			prd_brands_update_products( $old_brd );
		}

		// Met à jour le nombre de produits de la nouvelle marque
		if( $brd ){
			prd_brands_update_products( $brd );
		}

		if( $publish ){
			prd_products_publish( $id );
		}elseif( !$publish ){
			prd_products_unpublish( $id );
		}

		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		prd_products_update_completion( $id );
	}

	return $res;
}
// \endcond

/**	Cette fonction récupère la marque d'un produit.
 *	@param int $prd_id Identifiant du produit.
 *	@return int L'identifiant de la marque du produit
 *	@return bool false en cas d'échec
 *	@return null si le produit n'a pas de marque.
 */
function prd_products_get_brand( $prd_id ){
	global $config;

	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	$r = ria_mysql_query('
		select prd_brd_id from prd_products
		where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prd_id.' and prd_date_deleted is null
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);

}

/**	Cette fonction récupère le nom de la marque d'un produit.
 *	@param int $prd_id Identifiant du produit.
 *	@param bool $title Optionnel, par défaut la surcharge du nom sera toujours retourné en premier, mettre False pour obtenir le nom sychronisé
 *	@return string Le nom de la marque du produit, vide en cas d'échec ou si le produit n'a pas de marque.
 */
function prd_products_get_brd_name( $prd_id, $title=true ){
	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select
			'.($title ? 'if(ifnull(brd_title, "")!="", brd_title, brd_name)' : 'brd_name').' as name
		from prd_products
			join prd_brands on (brd_tnt_id = prd_tnt_id and brd_id = prd_brd_id)
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$prd_id.'
			and prd_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['name'];

}

// \cond onlyria
/**	Cette fonction permet la mise à jour du champ Centralisé pour un produit donné.
 *
 *	@param int $id Identifiant du produit. Si cet identifiant ne correspond à aucun produit, la fonction retournera false.
 *	@param bool $centralized Booléen indiquant si l'achat de ce produit est centralisé (information revendeurs)
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function prd_products_centralized_set( $id, $centralized ){
	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	global $config;

	return ria_mysql_query('update prd_products set prd_centralized='.( $centralized ? 1:0 ).' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du champ Marque pour un produit donné.
 *
 *	@param int $id Identifiant du produit. Si cet identifiant ne correspond à aucun produit, la fonction retournera false.
 *	@param int $brd Identifiant de la marque. Tout identifiant invalide aboutira à la mise à null du champ pour ce produit
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function prd_products_set_brand( $id, $brd ){
	global $config;

	// Récupère l'ancienne marque.
	$rp = prd_products_get_simple( $id );

	if( !$rp || !ria_mysql_num_rows($rp) ){
		return false;
	}

	$p = ria_mysql_fetch_array($rp);

	if( $p['brd_id'] == $brd ){
		return true;
	}

	if( !prd_brands_exists($brd) ){
		$brd = 'null';
	}

	$res = ria_mysql_query('update prd_products set prd_brd_id='.$brd.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id);

	if( $res ){
		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		prd_brands_update_products($p['brd_id']);
		prd_brands_update_products($brd);
		prd_products_update_completion($id);
	}

	return $res;
}
// \endcond

/**	Cette fonction est un alias de "prd_products_get" offrant deux avantages :
 *		- Par défaut, les prix ne sont pas retournés, rendant les performances meilleures
 *		- Moins de paramètres sont à spécifier
 * Tout nouveau développement devrait utiliser cette méthode plutôt que prd_products_get()
 *
 * @param int|array $id Optionnel, identifiant d'un produit ou tableau d'identifiants de produits
 * @param string $ref Optionnel, référence d'un produit ou tableau de références de produits
 * @param bool $published Optionnel, détermine si les produits retournés sont publiés (True), non publiés (-1) ou indifférent (par défaut)
 * @param int|array $cat Optionnel, identifiant d'une catégorie ou tableau d'identifiants de catégories dans laquelle (lesquelles) le produit est classé. Utilisez -1 pour rechercher les produits non classés
 * @param bool $catchilds Optionnel, détermine, dans le cas où $cat est spécifié, si les produits classés dans les catégories enfants de celle(s) spécifiée(s) sont retournés
 * @param int $fld Optionnel, identifiant d'un champ avancé pour lequel le produit à une valeur de renseignée
 * @param bool $with_price Optionnel, détermine si le prix du produit doit être retourné, ce qui n'est pas le cas par défaut. Note : le paramètre $sort peut également obliger à calculer le prix des produits, sans pour autant le retourner
 * @param array $sort Optionnel, paramètre de tri sous la forme d'un tableau associatif "nom de la colonne ou de l'alias" => "sens du tri". Voir le détail de la fonction prd_products_get() pour les spécificités concernant les tarifs et le tri par champ avancé
 * @param array $others_params Optionnel, tableau associatifs de filtres supplémentaires. La clé est le nom du filtre, la valeur est celle du filtre, par exemple array('childs' => true, 'brand' => 1). Les noms de filtre sont corrélés au nom des paramètres de la fonction prd_products_get(), mais plusieurs syntaxes sont autorisées ("_", "-", ucfirst)
 * @return resource Un résultat de requête MySQL, dont les colonnes sont décrites dans le commentaire de la fonction "prd_products_get()", false en cas d'échec
 */
function prd_products_get_simple( $id=0, $ref='', $published=false, $cat=0, $catchilds=false, $fld=false, $with_price=false, $sort=false, $others_params=false ){
	$rowstart = $brand = $parent = $parent_from_child = $sell_unit = 0;
	$maxrows = -1;
	$orderable = $ordered = $countermark = $have_stock = $have_secondary_image_only = $price_min = $price_max = $is_nomenclature = $user = $pkg_id = null;
	$follow_stock = $publish_prd = $in_garanty = $fld_filter_related_class = null;
	$new = $destockage = $promotion = $childs = $centralized = $uncompleted = $have_image_only = false;
	$hide_sleeping = $nomenclature_type = $or_between_fld = $onlychilds = $name_start_with = false;
	$supplier = $mdl = $dps = $isSync = $exclude = $bestsellers = $no_weight = $reseller_id = $date_published = false;
	$no_related = $exclude_cat = $or_between_val = $fld_in_child = $lng = $price_borne_ttc = $use_child_price = false;
	$full_like = $cumul_sort = $check_promotions = $ref_pattern = $fld_case_insensitive = $prs_wst_id = $can_be_canonical = false;
	$fld_use_hierarchy_ids = $publish_reseller = true;
	$barcode = $prs_ref = '';
	$only_sleeping = $prs_mandatory = $exclude_cat_children = $only_prd_ids = false;

	$others = [];

	if( is_array($others_params) && sizeof($others_params) ){
		foreach( $others_params as $k => $v ){
			switch( $k ){
				case 'new': $new = $v; break;
				case 'destockage': $destockage = $v; break;
				case 'promotion': $promotion = $v; break;
				case 'childs': $childs = $v; break;
				case 'rowstart': $rowstart = $v; break;
				case 'limit':
				case 'maxrows': $maxrows = $v; break;
				case 'centralized': $centralized = $v; break;
				case 'supplier': $supplier = $v; break;
				case 'uncompleted': $uncompleted = $v; break;
				case 'have_image_only':
				case 'have-image-only':
				case 'haveImageOnly': $have_image_only = $v; break;
				case 'have_secondary_image_only':
				case 'have-secondary-image-only':
				case 'haveSecondaryImageOnly' : $have_secondary_image_only = $v; break;
				case 'hide_sleeping':
				case 'hideSleeping':
				case 'hide-sleeping': $hide_sleeping = $v; break;
				case 'mdl': $mdl = $v; break;
				case 'countermark': $countermark = $v; break;
				case 'have_stock':
				case 'have-stock':
				case 'haveStock': $have_stock = $v; break;
				case 'dps': $dps = $v; break;
				case 'pkg_id': $pkg_id = $v; break;
				case 'orderable': $orderable = $v; break;
				case 'isSync':
				case 'is_sync':
				case 'is-sync': $isSync = $v; break;
				case 'exclude': $exclude = $v; break;
				case 'bestSellers':
				case 'best_sellers':
				case 'best-sellers': $bestsellers = $v; break;
				case 'ordered': $ordered = $v; break;
				case 'no_weight':
				case 'no-weight':
				case 'noWeight': $no_weight = $v; break;
				case 'no_related':
				case 'noRelated':
				case 'no-related': $no_related = $v; break;
				case 'exclude_cat':
				case 'exclude-cat':
				case 'excludeCat': $exclude_cat = $v; break;
				case 'excludeCatChildren':
				case 'exclude_cat_children': $exclude_cat_children = $v; break;
				case 'only-prd-ids':
				case 'onlyPrdIds':
				case 'only_prd_ids': $only_prd_ids = $v; break;
				case 'brand': $brand = $v; break;
				case 'fld_or':
				case 'fld-or':
				case 'fldOr': $or_between_val = $v; break;
				case 'or_between_val':
				case 'or-between-val':
				case 'orBeetweenVal': $or_between_val = $v; break;
				case 'nomenclature_type':
				case 'nomenclature-type':
				case 'nomenclatureType': $nomenclature_type = $v; break;
				case 'or_between_fld':
				case 'or-between-fld':
				case 'orBetweenFld': $or_between_fld = $v; break;
				case 'parent' : $parent = $v; break;
				case 'onlychilds' : $onlychilds = $v; break;
				case 'fldInChild':
				case 'fld-in-child':
				case 'fld_in_child' : $fld_in_child = $v; break;
				case 'name_start_with' : $name_start_with = $v; break;
				case 'lng' : $lng = $v; break;
				case 'price_min':
				case 'priceMin':
				case 'price-min': $price_min = $v; break;
				case 'price_max':
				case 'priceMax':
				case 'price-max': $price_max = $v; break;
				case 'price_borne_ttc':
				case 'priceBorneTtc':
				case 'price-borne-ttc': $price_borne_ttc = $v; break;
				case 'reseller_id':
				case 'reseller-id':
				case 'resellerId': $reseller_id = $v; break;
				case 'publish_reseller':
				case 'publishReseller':
				case 'publish-reseller': $publish_reseller = $v; break;
				case 'date-published':
				case 'datePublished':
				case 'date_published': $date_published = $v; break;
				case 'is_nomenclature':
				case 'is-nomenclature':
				case 'isNomenclature': $is_nomenclature = $v; break;
				case 'follow_stock':
				case 'followStock':
				case 'follow-stock': $follow_stock = $v; break;
				case 'use_child_price':
				case 'use-child-price':
				case 'useChildPrice': $use_child_price = $v; break;
				case 'full_like':
				case 'full-like':
				case 'fullLike': $full_like = $v; break;
				case 'publish_prd':
				case 'publishPrd':
				case 'publish-prd': $publish_prd = $v; break;
				case 'cumul_sort':
				case 'cumul-sort':
				case 'cumulSort': $cumul_sort = $v; break;
				case 'barcode' : $barcode = $v; break;
				case 'ingaranty':
				case 'in_garanty': $in_garanty = $v; break;
				case 'parent_from_child':
				case 'parent-from-child':
				case 'parentFromChild': $parent_from_child = $v; break;
				case 'check_promotions':
				case 'checkPromotions':
				case 'check-promotions': $check_promotions = $v; break;
				case 'sell_unit':
				case 'sell-unit':
				case 'sellUnit': $sell_unit = $v; break;
				case 'title_pattern':
				case 'title-pattern':
				case 'titlePattern': $others['title_pattern'] = $v; break;
				case 'ref_pattern':
				case 'ref-pattern':
				case 'refPattern': $ref_pattern = $v; break;
				case 'fld_case_insensitive':
				case 'fld-case-insensitive':
				case 'fldCaseInsensitive': $fld_case_insensitive = $v; break;
				case 'fld_use_hierarchy_ids':
				case 'fld-use-hierarchy-ids':
				case 'fldUseHierarchyIds': $fld_use_hierarchy_ids = $v; break;
				case 'prs_ref':
				case 'prsRef':
				case 'prs-ref': $prs_ref = $v; break;
				case 'prs_wst_id': $prs_wst_id = $v; break;
				case 'can_be_canonical': $can_be_canonical = $v; break;
				case 'only_sleeping': $only_sleeping = $v; break;
				case 'prs_mandatory': $prs_mandatory = $v; break;
				case 'fld_filter_related_class': $fld_filter_related_class = $v; break;
				case 'user': $user = $v; break;
				case 'have_stock_or_ordered': $others['have_stock_or_ordered'] = $v; break;
			}
		}
	}

	if( isset($others_params['confo_stock']) ){
		$others['confo_stock'] = $others_params['confo_stock'];
	}
	if( isset($others_params['confo_sort_price']) ){
		$others['confo_sort_price'] = $others_params['confo_sort_price'];
		$sort = false;
	}
	if( isset($others_params['use_prd_title']) ){
		$others['use_prd_title'] = $others_params['use_prd_title'];
	}

	return prd_products_get(
		$id, $ref, $brand, $published, $cat, $rowstart, $maxrows, $catchilds, $centralized, $new, $destockage, $promotion, $childs, $sort, $supplier, $uncompleted, $have_image_only, $hide_sleeping, $fld, $mdl, $countermark, $have_stock, $dps, $orderable, $isSync, $exclude, $bestsellers, $ordered, $or_between_val, $no_weight, $no_related, $exclude_cat, $with_price, $nomenclature_type, $or_between_fld, $onlychilds, $fld_in_child, $have_secondary_image_only, $name_start_with, $lng, $price_min, $price_max, $price_borne_ttc, $reseller_id, $publish_reseller, $date_published, $is_nomenclature, $follow_stock, $parent, $use_child_price, $full_like, $publish_prd, $cumul_sort, $barcode, $in_garanty, $parent_from_child, $check_promotions, $sell_unit, $ref_pattern, $fld_case_insensitive, $prs_ref, $prs_wst_id,$can_be_canonical, $only_sleeping, $prs_mandatory, $fld_use_hierarchy_ids,$exclude_cat_children, $only_prd_ids, $user, $pkg_id, $fld_filter_related_class, $others
	);
}

// \cond onlyria
/**	Retourne l'ensemble des produits contenus dans la base de données, filtrés
 *	en fonction des paramètres optionnels fournis. Les produits supprimés Â« logiquement Â»
 *	ne sont pas retournés.
 *	Si $published n'est pas précisé mais que la variable de configuration Â« admin_catalog_hide_source_unpublished Â» est à TRUE, les articles non publiés dans la gestion commerciale ne seront pas retournés
 *
 *	@param int $id Optionnel, Identifiant (ou tableau d'identifiant) d'un produit sur lequel filtrer le résultat.
 *	@param string $ref Optionnel, Référence (ou tableau de références) d'un produit sur lequel filtrer le résultat.
 *	@param int|array $brand Optionnel, Identifiant d'une marque ou tableau d'identifiants de marques sur laquelle/lesquelles filtrer le résultat. Pour ne charger que les produits sans marque, indiquer -1
 *	@param bool $published Optionnel, Booléen indiquant s'il faut seulement retourner les produits publiés (true), tous les produits (false) ou les produits non publiés (-1)
 *	@param int|array $cat Optionnel, identifiant d'une catégorie dont on souhaite récupérer les produits, ou tableau d'identifiants de catégories
 *	@param int $rowstart Optionnel, numéro de la ligne de départ du résultat.
 *	@param int $maxrows Optionnel, nombre de lignes maximum à retourner.
 *	@param bool $catchilds Optionnel, indique si les produits contenus dans les catégories enfant sont également retournés. Ce paramètre est ignoré si l'argument $cat n'est pas spécifié ou invalide.
 *	@param bool $centralized Optionnel, renvoyer tous les produits ou seulement les produits centralisés
 *	@param bool $new Optionnel, permet de réduire le résultat aux articles nouveaux
 *	@param bool $destockage Optionnel, ne retourne que les articles en déstockage
 *	@param bool $promotion Optionnel, ne retourne que les produits en promotion
 *	@param bool $childs Optionnel. Par défaut, les produits 'enfant seulement' ne sont pas retournés. Mettre true pour les inclure au résultat
 *	@param array $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par nom. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : ref, name, price, brand, publish, completion, date-created, date-promo, random, selled, catchilds. Il est possible de trier par valeur de champs avancés en indiquant 'fld-IDDUCHAMPAVANCE' => 'asc/desc' .Les valeurs autorisées pour la direction sont : asc, desc. ( dans le cas contraire, les critères suivants ne seront pas pris en compte ). Le tri par prix ne peut pas être cumulé avec d'autres critères de tri. Il fera l'objet d'un todo ultérieur.
 *	@param int $supplier Optionnel, identifiant d'un fournisseur sur lequel filtrer le résultat
 *	@param bool $uncompleted Optionnel, si vrai, ne retourne que les produits incomplets
 *	@param bool $have_image_only Optionnel, si vrai ne retourne que les produit ayant une image
 *	@param bool $hide_sleeping Optionnel, si vrai, les produits en sommeils ne seront pas retournés par la requête
 *	@param int $fld Optionnel, identifiant d'un champ avancé sur lequel filtrer le résultat. Seul les produits ayant une valeur renseignée dans ce champ seront retournés.
 *	@param int $mdl Optionnel, identifiant d'un modèle de saisie sur lequel filtrer le résultat. Seul les produits sur lesquels ce modèle est appliqué seront retournés.
 *	@param bool $countermark Optionnel, si vrai seul les produits en contremarque sont retournés. Si faux, seul les produits qui ne sont pas en contremarque sont retournés. Valeur par défaut : null (tous les produits).
 *	@param bool $have_stock Optionnel, si vrai seul les produits ayant du stock sont retournés. Si faux, seul les produits épuisés sont retournés. Valeur par défaut : null (tous les produits)
 *	@param int $dps Optionnel, permet l'affichage du stock sur un dépôt donné (surcharge tout autre paramètre de dépôt)
 *	@param bool $orderable Optionnel, si vrai seul les produits commandables sont retournés. Si faux, seul les produits non commandables sont retournés. Valeur par défaut : null (tous les produits)
 *	@param bool $isSync Optionnel, si vrai ne recupère que les produits synchronisés avec la gestion commerciale
 *	@param int|array $exclude Optionnel, identifiant de produit ou tableau d'identifiants de produits à exclure du résultat
 *	@param bool $bestsellers Optionnel, si true, ne retourne que les meilleures ventes
 *	@param bool $ordered Optionnel, si true récupère seulement les articles en commande fournisseur, si false récupère uniquement les produits qui ne sont pas en commande fournisseur.
 *	@param $or_between_val Optionnel, indique si, pour le tableau $fld, les différents champs sont séparés par des "ET" (vrai par défaut) ou des "OU"
 * 	@param $no_weight Optionnel, par défaut on ne tient pas compte de cette argument, mettre à true pour ne récupérer que les produits dont les champs poids brut ou poids net ne sont pas renseigné
 *	@param $no_related Optionnel, par défaut on ne tient pas compte de cette argument, mettre à true pour ne récupérer que les produits qui n'ont aucun artiles liés
 *	@param $exclude_cat Optionnel, identifiant ou tableau d'identifiants de catégories à exclure du résutats
 *	@param bool $with_price Optionnel, si false, les tarifs ne sont pas chargés (utile pour des longues listes de produits où le prix n'est pas nécessaire). Est annulé par une éventuelle clause de tri
 *	@param $nomenclature_type Optionnel, permet de filtrer les produits suivant le type de nomenclature (identifiant ou tableau d'identifiants)
 *	@param $or_between_fld Optionnel, permet de mettre un "or" entre deux champs avancés
 *	@param bool $onlychilds Optionnel, ce paramètre permet de récupérer les produits seulement s'ils ont des enfants (l'utilisation du paramètre $fld_in_child à true est obligatoire)
 *	@param bool $fld_in_child Optionnel, permet de spécifier s'il l'on souhaite vérifier les informations de champs avancé aussi sur les produits enfants
 *	@param bool $have_secondary_image_only Optionnel, permet de spécifier si chaque produit doit avoir au moins une image secondaire. null : indifférent, true pour oui et false pour non.
 *	@param string $name_start_with Optionnel, permet de filtrer les produits sur le début de leur nom
 *	@param string $lng Permet de forcer la langue utilisée dans la requête
 *	@param float $price_min Facultatif, prix minimal du produit (la variable $with_price doit être active). Borne inclue
 *	@param float $price_max Facultatif, prix maximal du produit (la variable $with_price doit être active). Borne inclue
 *	@param bool $price_borne_ttc Facultatif, détermine si les bornes spécifiées en $price_min et $price_min sont HT ou TTC
 *	@param int $reseller_id Facultatif, identifiant d'un revendeur (en left join, si le revendeur n'existe pas tout est retourné)
 *	@param bool $publish_reseller Facultatif, détermine si le produit est publié chez le revendeur
 *	@param string $date_published Facultatif, détermine si l'on souhaite récupérer les produits publiés à partir d'une certaine date.
 *	@param bool $is_nomenclature Facultatif, permet de récupérer les produits qui sont (ou non) des nomenclatures fixes
 *	@param bool $follow_stock Facultatif, permet de filtrer sur les produits suivis ou non en stock (null par défaut)
 *	@param int $parent Facultatif, identifiant d'un produit parent (ou tableau). Remplace prd_childs_get()
 *	@param bool $use_child_price Facultatif, si activé, le prix de l'enfant minimal (pour les produits parents) est retourné et utilisé dans la clause de tri
 *	@param $full_like Optionnel, permet de spécifier un LIKE %% dans la recherche par valeur(s) de champ(s) avancé(s) (ou égal strict avec la valeur -1)
 *	@param bool $publish_prd Optionnel, permet de filtrer sur la publication "prd_publish" sans filtrer sur le fait que le produit soit classé ou non dans une famille publiée. La valeur par défaut est NULL (tous les produits).
 *	@param $cumul_sort Optionnel, mettre 'before' pour appliquer le tri personnalisée ($sort) avant le tri par défaut, 'after' pour appliquer le tri par défaut avant le tri personnalisé ($sort), le paramètre sera ignoré pour tout autre valeur
 *	@param string $barcode Optionnel, code EAN d'un produit
 *	@param $in_garanty Optionnel, Spécifier True pour n'avoir que les produits en garantie, False pour ceux hors-garantie. Null (par défaut) pour ignorer le filtre.
 *	@param int $parent_from_child Optionnel, identifiant d'un produit à partir duquel on souhaite récupérer son ou ses parents, ou tableau d'identifiants.
 *	@param bool $check_promotions Optionnel, si activée et $promotion activée également, les promotions seront controlées par rapport au contexte (client connecté). ATTENTION : le résultat retourné sera un tableau et non un pointeur MySQL.
 *	@param $sell_unit Optionnel, identifiant d'unité de vente ou tableau d'identifiants. Permet de filtrer les produits selon leur unité. Null est une valeur autorisée et permet de récupérer les produits sans unité.
 *	@param $ref_pattern Optionnel, permet de spécifier que la référence doit respecter un schéma précis (ou au contraire ne pas le respecter). L'argument doit être un tableau associatif ou la clé est le motif (le caractère * indique "tous les caractères autorisés"), la valeur est un booléen indiquant l'inclusion (True) ou l'exclusion (False). Si plusieurs motifs, ils sont séparés par des "OU" logiques.
 *	@param $fld_case_insensitive Optionnel, par défaut la recherche sur les valeurs de champs avancés sont sensible à casse, mettre true pour qu'elle ne le soit pas
 *	@param $prs_ref Optionnel, référence personnalisé du produit ($reseller_id doit obligatoirement être fournie)
 *	@param $prs_wst_id Optionnel, identifiant du site web sur lequel filtrer les résultats, utile uniquement avec le paramètre $reseller_id
 *	@param $can_be_canonical Optionnel, par défaut ce paramètre est ignoré, mettre True pour ne récupérer que les produits pouvant être utilisé pour générer l'url canonique d'un autre article
 *	@param $only_sleeping Optionnel, Spécifier True pour ne récupérer que les produits en sommeil.
 *	@param $prs_mandatory Optionnel, par défaut à False, mettre True pour rendre obligatoire la présence d'une ligne dans prd_reseller pour gérer la publication du produit
 *	@param $fld_use_hierarchy_ids Optionnel, par défaut à true, mettre false pour dire que le tableau de valeur correspond aux noms des valeurs
 *	@param bool $exclude_cat_children Optionnel, par défaut à false, permet d'exclure les catégories enfant des catégories à exclure
 *	@param bool $only_prd_ids Facultatifs, retourne que les id des produits si a true
 *	@param int $user L'identifiant de l'utilisateur pour lequel les prix doivent être calculés, par défaut utilise l'utilisateur connecté
 *	@param int $pkg_id Attention à utiliser en association avec $dps, Identifiant ou liste d'identifiant de type de package pour récupérer que des produit lié à un conditionnement
 *	@param array|null $fld_filter_related_class Optionnel, Tableau avec en clé l'id de la classe et en valeur un tableau avec en clé l'id du champs avancé et en valeur un tableau au format suivant :
 * 			['fld' => int|array, 'or_between_val'=> bool, 'or_between_fld'=> bool  ]
 * 			or_between_val et or_between_fld sont optionnels
 * 			Actuelement ce paramètre ne fonctionne que avec la classe CLS_PRD_COLISAGE et liaison avec le paramètre $pkg_id. Si $pkg_id n'est pas renseigné le filtre ne fonctionneras pas.
 * 	@param $others Optionnel, paramètre spécifique
 *
 *	Le résultat est fourni sous la forme d'un résultat de requête mysql,
 *	comprenant les colonnes :
 *		- id : identifiant du produit
 *		- url_alias : chemin virtuel, disponible uniquement lorsque le paramètre $cat est utilisé
 *		- cat_id : identifiant de la catégorie, disponible uniquement lorsque le paramètre $cat est utilisé
 *		- ref : référence du produit
 *		- name : nom du produit
 *		- title : titre du produit (nom si titre vide)
 *		- desc : description du produit
 *		- desc-long: description longue du produit
 *		- ecotaxe : montant de l'ecotaxe pour ce produit (inclus dans le prix)
 *		- barcode : code barres du produit
 *		- price_ht : prix hors taxe (sauf si with_price est à false et pas de clause de tri sur le tarif)
 *		- tva_rate : taux de tva appliqué au produit (sauf si with_price est à false et pas de clause de tri sur le tarif)
 *		- price_ttc : prix toutes taxes comprises (sauf si with_price est à false et pas de clause de tri sur le tarif)
 *		- publish : booléen indiquant si le produit est publié ou non
 *		- publish_cat : booléen indiquant si la catégorie dans laquelle le produit est classé est publiée ou non
 *		- date_first_published : date de première publication
 *		- date_published : date de publication du produit. N'est renseigné que si le produit est publié.
 *		- date_published_rss : date de publication, formatée pour l'utilisation dans certains flux RSS (format : %a, %d %b %Y %H:%i:%s +0100)
 *		- brd_id : identifiant de la marque du produit
 *		- brd_title : désignation de la marque du produit
 *		- brd_img_id : identifiant de l'image associée à la marque
 *		- brd_url : url du site internet de la marque
 * 		- brd_pos : la position du produit en fonction de la marque
 *		- weight : poids brut du produit, en grammes
 *		- weight_net : poids net du produit, en grammes
 *		- selled : nombre de vente du produit
 *		- hits : nombre de consultations
 *		- length : Longueur du produit, en centimètres
 *		- width : Largeur du produit, en centimètres
 *		- height : Hauteur du produit, en centimètres
 *		- stock : Stock réel
 *		- stock_real : Stock réel (hors quantité en préparation)
 *		- stock_res : Quantité du stock déjà vendue en attente de départ
 *		- stock_com : Quantité en commande chez le fournisseur, en attente de livraison
 *		- stock_prepa : Quantité en cours de préparation
 *		- stock_livr : Date de prochaine livraison
 *		- centralized : Produit centralisé (0 ou 1)
 *		- keywords : Mots clés complémentaires
 *		- sleep : article en sommeil
 *		- img_id : image principale du produit
 *		- destockage : indique si le produit est en déstockage
 *		- taxcode : code douanier du produit
 *		- supplier_ref : référence fournisseur (fournisseur principal)
 *		- supplier_delay : délai de livraison (fournisseur principal)
 *		- supplier_price : prix d'achat du produit auprès du fournisseur principal
 *		- supplier_barcode : code barre du produit (fournisseur principal)
 *		- supplier_packing : colisage du produit (fournisseur principal)
 *		- supplier_conversion : conversion unités d'achat / unités de vente (fournisseur principal)
 *		- supplier_id : identifiant du fournisseur (fournisseur principal)
 *		- tag_title : balise title de la page produit pour le référencement
 *		- tag_desc : balise méta-description de la page produit pour le référencement
 *		- garantie : la durée de grarantie
 *		- is_sync : détermine si le produit est synchronisé avec la gestion commerciale
 *		- completion : Taux de complétion
 *		- sell_weight : Détermine si l'article est vendu à l'unité de mesure ou à une unité indivisible (si 1, requiert une conversion dans ord_products)
 *		- nomenclature_type : Identifiant du type de nomenclature
 *		- purchase_avg : Prix d'achat moyen pondéré
 *		- orderable : détermine si l'article est commandable (orderable coché + [actif ou toujours en stock])
 *		- childonly : détermine si l'article est enfant seulement
 *		- selled_web : nombre de ventes du produit sur le web
 *		- prs_publish (si $reseller_id spécifié) : publication oui / non du produit chez le revendeur
 *		- prs_stock (si $reseller_id spécifié) : quantité en stock chez le revendeur
 *		- prs_price_ht (si $reseller_id spécifié) : prix HT chez le revendeur
 *		- prs_price_promo_ht (si $reseller_id spécifié) : prix HT promotionnel chez le revendeur
 *		- prs_ref (si $reseller_id spécifié) : référence du produit pour le revendeur
 *		- follow_stock : produit suivi en stock oui / non
 *		- is_orderable : détermine si le produit est coché "commandable" (note : un produit peut être coché mais avoir la valeur "orderable" False à cause de sa mise en sommeil)
 *		- child_pos : position de l'article enfant dans l'ensemble des articles enfants (seulement si $parent défini et ne correspond qu'à un seul parent)
 *		- hry_child_is_sync : détermine si la liaison parent/enfant est synchronisée (seulement si $parent défini et ne correspond qu'à un seul parent)
 *		- hry_parent_is_sync : détermine si la liaison parent/enfant est synchronisée (seulement si $parent_from_child défini et ne correspond qu'à un seul enfant)
 *		- date_first_published_en : date de première publication au format en
 *		- sun_id : identifiant de l'unité de vente du produit.
 *		- can_be_canonical : identifiant du produit utilisé comme canonique
 *		- no_index : si oui ou non un produit est indexé
 *		- promo_price_ht : seulement si $check_promotions est à true, correspond au tarif après application de la promotion sur produit
 *
 *	Les produits sont retournées triées par nom par défaut.
 *	La date de publication n'est pas encore exploitée.
 */


function prd_products_get( $id=0, $ref='', $brand=0, $published=false, $cat=0, $rowstart=0, $maxrows=-1, $catchilds=false, $centralized=false, $new=false, $destockage=false, $promotion=false, $childs=false, $sort=false, $supplier=false, $uncompleted=false, $have_image_only=false, $hide_sleeping=false, $fld=false, $mdl=false, $countermark=null, $have_stock=null, $dps=false, $orderable=null, $isSync=false, $exclude=false, $bestsellers=false, $ordered=null, $or_between_val=false, $no_weight=false, $no_related=false, $exclude_cat=false, $with_price=true, $nomenclature_type=false, $or_between_fld=false, $onlychilds=false, $fld_in_child=false, $have_secondary_image_only=null, $name_start_with = false, $lng=false, $price_min=null, $price_max=null, $price_borne_ttc=false, $reseller_id=false, $publish_reseller=true, $date_published=false, $is_nomenclature=null, $follow_stock=null, $parent=0, $use_child_price=false, $full_like=false, $publish_prd=null, $cumul_sort=false, $barcode='', $in_garanty=null, $parent_from_child=0, $check_promotions=false, $sell_unit=0, $ref_pattern=false, $fld_case_insensitive=false, $prs_ref='', $prs_wst_id=false, $can_be_canonical=false, $only_sleeping=false, $prs_mandatory=false, $fld_use_hierarchy_ids=true, $exclude_cat_children=false, $only_prd_ids=false, $user=null, $pkg_id=null, $fld_filter_related_class=null, $others=[] ){
	$exclude = control_array_integer(($exclude === false ? 0 : $exclude), false);

	if( $exclude === false ){
		return false;
	}

	if( $brand != -1 ){
		$brand = control_array_integer($brand, false);
		if( $brand === false ){
			return false;
		}
	}

	$parent = control_array_integer($parent, false);
	if( $parent === false ){
		return false;
	}

	$parent_from_child = control_array_integer($parent_from_child, false);
	if( $parent_from_child === false ){
		return false;
	}

	$id = control_array_integer($id, false);
	if( $id === false ){
		return false;
	}

	if( $supplier!==false && !is_numeric($supplier) ){
		return false;
	}

	global $config;

	$lng = $lng != false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : i18n::getLang();

	if( $exclude_cat!==false && is_numeric($exclude_cat) ){
		$exclude_cat = array($exclude_cat);
	}

	// Paramètres dépendants de l'utilisateur en cours.
	$user = !is_null($user) ? $user : (isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0);

	if( $dps === false || !prd_deposits_exists($dps) ){
		if( $config['tnt_id']==2 ){
			$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
		}else{
			$dps = prd_deposits_get_main();
		}
	}

	if( !$dps ){ $dps = 0; }

	if( !is_array($ref) ){
		if( trim($ref) ){
			$ref = array($ref);
		}else{
			$ref = array();
		}
	}

	// parse les unités de vente, mais ne retourne pas False s'il y a des erreurs
	// Null est parsé en -1
	$tmp_ar_sellunit = array();
	if( !is_array($sell_unit) ){
		$sell_unit = array($sell_unit);
	}
	foreach( $sell_unit as $sun_id ){
		if( is_numeric($sun_id) && $sun_id > 0 ){
			$tmp_ar_sellunit[] = $sun_id;
		}elseif( $sun_id === null ){
			$tmp_ar_sellunit[] = -1;
		}
	}
	$sell_unit = $tmp_ar_sellunit;

	if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) ){
		// Permet la consultation sous l'identité d'une autre personne (réservé aux administrateurs et représentants)
		$user = $_SESSION['admin_view_user'];
	}elseif( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==4 && $config['tnt_id']==1 ){
		// les revendeurs Bigship ne voient pas leur prix via cette fonction
		$user = 0;
	}
	if( isset($_SESSION['usr_tnt_id']) && !$_SESSION['usr_tnt_id'] ){
		if( !isset($_SESSION['admin_view_user']) || !is_numeric($_SESSION['admin_view_user']) ){
			$user = 0;
		}
	}

	// Contrôle que $cat soit un numérique ou un tableau de numérique
	if( $cat !== -1 ){
		$cat = control_array_integer( $cat, false );
		if( $cat === false ){
			return false;
		}
	}

	$prd_ids_codes = array();
	if( is_array($cat) && count($cat) ){
		$prd_ids_codes = prd_categories_codes_get_prd_ids( $cat );

		if (is_array($prd_ids_codes) && count($prd_ids_codes)) {
			$cat = [ $config['cat_root'] ];
			$catchilds = true;
		}
	}

	if( trim($prs_ref) != '' ){
		if( !is_numeric($reseller_id) || $reseller_id <= 0 ){
			return false;
		}
	}

	if( !isset($config['prd_new_days']) || !is_numeric($config['prd_new_days']) || $config['prd_new_days'] < 0 ){
		$config['prd_new_days'] = 60;
	}

	if (is_array($exclude_cat)) {
		if ($exclude_cat_children) {
			$merge = prd_categories_childs_get_array($exclude_cat,$published);
			if (is_array($merge)) {
				$exclude_cat = array_merge($exclude_cat, $merge);
			}
		}
		$exclude_cat = array_unique($exclude_cat);
	}

	$col_new = isset($config['prd_new_date']) && in_array($config['prd_new_date'], array('prd_date_created', 'prd_first_published', 'prd_date_published')) ? $config['prd_new_date'] : 'prd_date_created';
	$sql = 	'
		select
		prd.prd_id as id';
	if (!$only_prd_ids || $with_price) {
		$sql .= ', prd_ref as ref, prd_name as name, '.( isset($others['use_prd_title']) && $others['use_prd_title'] ? 'prd_title as title' : 'if(prd_title="",prd_name,prd_title) as title' ).',
		prd_desc as "desc", prd_desc_long as "desc-long", prd_barcode as barcode,
		prd_publish as publish, prd_publish_cat as publish_cat, prd_no_index as no_index,
		(prd_orderable and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('prd.prd_id', $dps, true).')>0)) as orderable, prd_childonly as childonly,
		prd_countermark as countermark, prd_centralized as centralized, prd_ecotaxe as ecotaxe,
		prd_new!="-1" and if( '.$config['prd_new_days'].' != 0, (prd_new="1" or datediff(now(),'.$col_new.')<='.$config['prd_new_days'].'), prd_new="1") as new,
		date_format(prd_first_published,"%d/%m/%Y à %H:%i") as date_first_published,
		date_format(prd_date_published,"%d/%m/%Y à %H:%i") as date_published, date_format(prd_date_published,"%a, %d %b %Y %H:%i:%s +0100") as date_published_rss,
		brd_id, if(brd_title!="",brd_title,brd_name) as brd_title, brd_img_id, brd_url, prd_brd_pos as brd_pos,
		prd_weight as weight, prd_weight_net as weight_net, prd_selled as selled, prd_hits as hits, prd_length as "length", prd_width as width, prd_height as height,
		'.($config['use_decimal_qte'] ? prd_stocks_get_sql() : 'cast(' . prd_stocks_get_sql() . ' as signed)' ).' as stock_real,
		'.($config['use_decimal_qte'] ? prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('prd.prd_id', $dps, true) : 'cast(' . prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('prd.prd_id', $dps, true).' as signed)' ).' as stock,
		'.($config['use_decimal_qte'] ? prd_stocks_sto_res() : 'cast('.prd_stocks_sto_res().' as signed)' ).' as stock_res,
		'.($config['use_decimal_qte'] ? 'sto_com' : 'cast(sto_com as signed)' ).' as stock_com,
		'.($config['use_decimal_qte'] ? 'sto_prepa' : 'cast(sto_prepa as signed)' ).' as stock_prepa,
		'.($config['use_decimal_qte'] ? 'sto_mini' : 'cast(sto_mini as signed)' ).' as stock_mini,
		'.($config['use_decimal_qte'] ? 'sto_maxi' : 'cast(sto_maxi as signed)' ).' as stock_maxi,
		if(prd_stock_livr < date(ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY)), null, date_format(prd_stock_livr,"%d/%m/%Y")) as stock_livr,
			prd_keywords as keywords, prd_sleep as sleep,
		prd_img_id as img_id, date_format(prd_date_created,"%d/%m/%Y à %H:%i") as date_created,
		date_format(prd_date_modified,"%d/%m/%Y à %H:%i") as date_modified,
		prd_new as new_rule, prd_is_sync as is_sync, prd_taxcode as taxcode,
		prd_publish and prd_publish_cat and prd_sleep and (' . prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('prd.prd_id', $dps, true).')>0 as destockage, prd_completion as completion,
		ps_ref as supplier_ref, ps_delay as supplier_delay, ps_price as supplier_price, ps_barcode as supplier_barcode,
		ps_packing as supplier_packing, ps_conversion as supplier_conversion, ps_usr_id as supplier_id, prd_rvw_avg as avg,
		prd_tag_title as tag_title, prd_tag_desc as tag_desc, prd_garantie as garantie, prd_sell_weight as sell_weight,
		prd_nomenclature_type as nomenclature_type, prd_purchase_avg as purchase_avg, prd_selled_web as selled_web,
		prd.prd_follow_stock as follow_stock, prd_orderable as is_orderable, prd_first_published as date_first_published_en, prd_sun_id as sun_id,prd.prd_canonical_id as prd_canonical_id
		';

		if( (is_array($cat) && count($cat)) || is_array($exclude_cat) )
			$sql .= ', cly.cly_prd_pos as prd_pos ';
		if( is_numeric($reseller_id) && $reseller_id>0 ){
			// colonnes de la table prd_resellers
			$sql .= ', prs_publish, prs_stock, prs_price_ht, prs_price_promo_ht, prs_ref ';
		}

		if( sizeof($parent)==1 ){
			$sql .= ', hch.prd_child_pos as child_pos, hch.prd_hry_is_sync as hry_child_is_sync';
		}
		if( sizeof($parent_from_child)==1 ){
			$sql .= ', hpa.prd_hry_is_sync as hry_parent_is_sync';
		}
		if( $promotion ){
			$sql .= ',
				date_format(prc_date_start,"%d/%m/%Y") as pmt_date_start,
				date_format(prc_date_start,"%a, %d %b %Y %H:%i:%s +0100") as pmt_date_start_rss,
				date_format(prc_date_end,"%d/%m/%Y") as pmt_date_stop,
				prc_id as pmt_id
			';
		}

		if( is_array($cat) && count($cat) ){
			$sql .= ', if(ifnull(cly.cly_url_perso, \'\')=\'\', cly.cly_url_alias, cly.cly_url_perso) as url_alias, cly.cly_url_perso as url_perso, cly.cly_url_alias, cly.cly_cat_id as cat_id ';
			if( $published ) {
				$sql .= ', cat_pos, cat_parent_id, cat_name, if(ifnull(cat_title, "") != "", cat_title, cat_name) as cat_title ';
			}
		}
	}

	if( is_array($cat) && count($cat) ){
		if( !$catchilds ){
			$sql .= ' from prd_products as prd use index (prd_id) inner join prd_classify as cly FORCE INDEX FOR JOIN (PRIMARY) on (prd.prd_id=cly.cly_prd_id and cly.cly_tnt_id='.$config['tnt_id'].' and cly.cly_cat_id in ('.implode(',',$cat).') '.( is_array($exclude_cat) ? 'and cly.cly_cat_id not in ('.implode(',', $exclude_cat).')' : '' ).' ) ';
		}else{
			$categories = prd_categories_childs_get_list($cat,$published);
			if( count($cat) ){
				$categories = $categories ? implode(',',$cat).','.$categories : implode(',',$cat);
			}

			$sql .= ' from prd_products as prd use index (prd_id) inner join prd_classify as cly FORCE INDEX FOR JOIN (PRIMARY) on (prd.prd_id=cly.cly_prd_id and cly.cly_tnt_id='.$config['tnt_id'].' and cly.cly_cat_id in ('.$categories.') '.( is_array($exclude_cat) ? 'and cly.cly_cat_id not in ('.implode(',', $exclude_cat).')' : '' ).' ) ';
		}
		if( $published == true ){
			$sql .= ' inner join prd_categories on (cly.cly_cat_id=cat_id and cat_tnt_id='.$config['tnt_id'].')';
		}
		if( $promotion ){
			$sql .= ' inner join prc_prices on ( prc_tnt_id=prd.prd_tnt_id and prc_prd_id=prd.prd_id ) ';
		}
		$sql .= ' left join prd_brands on (brd_tnt_id='.$config['tnt_id'].' and prd_brd_id=brd_id)';
	}elseif ( $cat === -1 ){
		$sql .= ' from prd_products as prd use index (prd_id) left join prd_classify as cly on prd.prd_tnt_id = cly.cly_tnt_id and prd.prd_id = cly.cly_prd_id left join prd_brands on (prd.prd_tnt_id=brd_tnt_id and prd_brd_id=brd_id)';
	}else{
		$sql .= ' from prd_products as prd use index (prd_id) left join prd_brands on (prd_brd_id=brd_id and brd_tnt_id='.$config['tnt_id'].')';
		if( $promotion ){
			$sql .= ' inner join prc_prices on ( prc_tnt_id=prd.prd_tnt_id and prc_prd_id=prd.prd_id ) ';
		}
		if( is_array($exclude_cat) ){
			$sql .= ' inner join prd_classify as cly on ( prd.prd_id=cly.cly_prd_id and cly.cly_cat_id not in ('.implode(',', $exclude_cat).') )';
		}
	}

	if (!$only_prd_ids || $with_price) {
		if( $supplier===false ){
			$sql .= ' left join prd_suppliers on (prd.prd_id=ps_prd_id and ps_tnt_id='.$config['tnt_id'].' and ps_main!=0) ';
		}else{
			$sql .= ' inner join prd_suppliers on (prd.prd_id=ps_prd_id and ps_tnt_id='.$config['tnt_id'].' and ps_usr_id='.$supplier.')';
		}
	}

	if( $pkg_id !== null && is_numeric($dps) ){
		$pkg_ids = control_array_integer($pkg_id);
		if( $pkg_ids ){
			$sql .= ' inner join prd_colisage_types on (col_dps_id='.$dps.' and col_tnt_id='.$config['tnt_id'].' and col_pkg_id in ('.implode(',', $pkg_ids).') and col_is_deleted=0)';
			$sql .= ' inner join prd_colisage_classify as col on (prd.prd_id=col.cly_prd_id and col.cly_tnt_id='.$config['tnt_id'].' and col_id=col.cly_col_id)';
		}
	}
	if( is_numeric($mdl) && $mdl>0 ){
		$rmdl = fld_models_get($mdl);
		if( $rmdl!==false && ria_mysql_num_rows($rmdl) ){
			$mdl = ria_mysql_fetch_array($rmdl);
			if( $mdl['cls_id']==CLS_PRODUCT ){
				$sql .= ' inner join fld_object_models on ( pm_tnt_id='.$config['tnt_id'].' and pm_obj_id_0=prd.prd_id and pm_mdl_id='.$mdl['id'].' )';
			}
		}
	}

	$sql .= ' left join prd_stocks on (prd.prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0) ';

	if( sizeof($parent) ){
		$sql .= ' join prd_hierarchy as hch on
		prd.prd_tnt_id = hch.prd_tnt_id and
		prd.prd_id = hch.prd_child_id and
		hch.prd_parent_id in ('.implode(', ', $parent).')
		';
	}

	if( sizeof($parent_from_child) || $fld_in_child ){
		$sql .= ( $fld_in_child && !$onlychilds ? ' left' : '' ).' join prd_hierarchy as hpa on
		prd.prd_tnt_id = hpa.prd_tnt_id and
		prd.prd_id = hpa.prd_parent_id
		';
		if( sizeof($parent_from_child) ){
			$sql .= ' and hpa.prd_child_id in ('.implode(', ', $parent_from_child).')';
		}
	}

	if( is_numeric($reseller_id) && $reseller_id>0 ){
		$sql .= ' '.( trim($prs_ref) == '' ? 'left' : '' ).' join prd_resellers on prd.prd_id=prs_prd_id and prd.prd_tnt_id=prs_tnt_id and '.$reseller_id.'=prs_usr_id';

		if (is_numeric($prs_wst_id)) {
			$sql .= ' and (prs_wst_id=0 or prs_wst_id='.$prs_wst_id.')';
		}
	}

	// Filtres
	$sql .= ' where prd.prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null ';

	// gestion des restrictions si variable activée (hors use_catalog_restrictions_usr_price)
	if( $config['use_catalog_restrictions'] && !$config['use_catalog_restrictions_usr_price'] ){
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd.prd_id' ).')';
	}

	if (is_array($prd_ids_codes) && count($prd_ids_codes)) {
		$sql .= ' and prd_id in ('.implode(',', $prd_ids_codes).')';
	}

	if( $publish_prd !== null ){
		if( $publish_prd ){
			$sql .= ' and prd_publish = 1';
		}else{
			$sql .= ' and prd_publish = 0';
		}
	}

	if( is_numeric($reseller_id) && $reseller_id>0 ){
		if( $publish_reseller !== null ){
			// publication chez le revendeur
			$sql .= ' and ifnull(prs_publish, 1)='.( $publish_reseller ? '1' : '0' );
		}
		if( trim($prs_ref) != '' ){
			$sql .= ' and prs_ref = "'.addslashes( $prs_ref ).'"';
		}
		if( is_numeric($prs_wst_id) ){
			$sql .= ' and (prs_wst_id='.$prs_wst_id;
			if (!$prs_mandatory) {
				$sql .= ' or ifnull(prs_wst_id,0)=0';
			}
			$sql .= ' )';
		}else{
			$sql .= ' and ifnull(prs_wst_id, 0)=0';
		}
	}
	if($can_be_canonical){
		$sql .= ' and prd_canonical_id is null';
	}
	if( $no_related ){
		$sql .= ' and not exists (
		select rel_src_id
		from prd_relations
		where rel_tnt_id='.$config['tnt_id'].'
		and rel_src_id=prd.prd_id
		and rel_is_deleted=0
		)';
	}

	if( sizeof($id) ){
		$sql .= ' and prd.prd_id in ('.implode(',', $id).')';
	}

	if( count($ref)>0 ){
		$refs = array();
		foreach( $ref as $rf ){
			$refs[] = "'".addslashes( strtolower(trim($rf)) )."'";
		}
		$ref = $refs;
		$sql .= ' and lower(prd_ref) in ('.implode(',',$ref).')';
	}
	if( $brand==-1 ){
		$sql .= ' and prd_brd_id is null';
	}elseif( sizeof($brand) ){
		$sql .= ' and prd_brd_id in ('.implode( ',', $brand ).')';
	}

	if( $published ) { // removed  "and prd_publish_cat" ( no way to enable / disable it in RIASHOP backoffice )
		$condition = 'prd_publish and prd_publish_cat and ('.($only_sleeping ? 'prd_sleep':'not prd_sleep').' or (' . prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('prd.prd_id', $dps, true).')>0)';
		if ($published === -1) $sql .= 'and not (' . $condition . ')';
		else $sql .= ' and ' . $condition;
	}
	elseif( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true ){
		$sql .= ' and ( (prd_is_sync and prd_publish) or not prd_is_sync ) '; // masque les articles non publiés gescom
	}

	if( $date_published!=false && isdate($date_published)  ) {
		$sql .= ' and date(prd_date_published) >=\''.$date_published.'\'';
	}

	if( $only_sleeping ){
		$sql .= ' and prd_sleep ';
	}
	if( $hide_sleeping ){
		$sql .= ' and not prd_sleep ';
	}
	if( $centralized ){
		$sql .= ' and prd_centralized!=0';
	}
	if( $new ){
		$sql .= ' and prd_new!="-1" and if( '.$config['prd_new_days'].' != 0, (prd_new="1" or datediff(now(),'.$col_new.')<='.$config['prd_new_days'].'), prd_new="1") ';
	}
	if( $promotion ){
		$sql .= '
		and prc_is_deleted=0
		and prc_date_start<=now()
		and prc_is_promotion=1
		and prc_date_end > now()
		';
		//and price_is_apply( prc_tnt_id, prc_prd_id, '.$user.', 0, 0, prc_id, '.$config['default_prc_id'].' )!=0
	}

	if( $destockage ){
		$sql .= ' and prd_publish and prd_publish_cat and prd_sleep and (' . prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('prd.prd_id', $dps, true).')>0';
	}
	if( sizeof($id)!=1 && !sizeof($ref) && !$childs && !$destockage && !$promotion ){
		$sql .= ' and prd_childonly=0';
	}
	if( $uncompleted ){
		$sql .= ' and prd_completion<100';
	}
	if( $have_image_only===-1 ){
		$sql .= ' and ( prd_img_id is null or prd_img_id not in ( select img_id from img_images where img_tnt_id='.$config['tnt_id'].' and img_date_deleted is null ) )';
	}elseif( $have_image_only ){
		$sql .= ' and prd_img_id is not null';
	}
	if( $have_secondary_image_only !== null ) {
		$sql .= ' and ' . ($have_secondary_image_only ? '' : 'not ') . 'exists (
		select 1 from prd_images as pi
		join img_images as i on pi.img_tnt_id=i.img_tnt_id and pi.img_id=i.img_id
		where i.img_date_deleted is null and pi.img_prd_id=prd.prd_id and pi.img_tnt_id=prd.prd_tnt_id and pi.img_publish = 1
		)';
	}

	$sql .= fld_classes_sql_get( CLS_PRODUCT, $fld, $or_between_val, $or_between_fld, $lng, 'prd', $fld_in_child ? 'hpa.prd_child_id' : '', $full_like, $fld_case_insensitive, $fld_use_hierarchy_ids );

	if( is_array($fld_filter_related_class) ){
		$allowed_cls_ids = array(CLS_PRD_COLISAGE);
		foreach( $fld_filter_related_class as $cls_id => $fld ){
			$rel_fld = ria_array_get($fld, 'fld',  false);
			$rel_or_between_val = ria_array_get($fld, 'or_between_val',  $or_between_val);
			$rel_or_between_fld = ria_array_get($fld, 'or_between_fld',  $or_between_fld);

			if( $rel_fld === false ){
				continue;
			}

			$use_related_fld = $join_table = false;

			if( !in_array($cls_id, $allowed_cls_ids) ){
				continue;
			}

			switch( $cls_id ){
				case CLS_PRD_COLISAGE:
					// Cela valide la jointure avec prd_colisage_classify
					if( $pkg_id !== null && is_numeric($dps) && isset($pkg_ids) ){
						$use_related_fld = true;
						$join_table = 'col';
					}
					break;
			}

			if( $use_related_fld && $join_table ){
				$sql .= fld_classes_sql_get( $cls_id, $rel_fld, $rel_or_between_val, $rel_or_between_fld, $lng, $join_table, '', $full_like, $fld_case_insensitive, $fld_use_hierarchy_ids );
			}
		}
	}

	if( $countermark!==null ){
		if( $countermark ){
			$sql .= ' and prd_countermark';
		}else{
			$sql .= ' and not prd_countermark';
		}
	}

	if( isset($others['have_stock_or_ordered']) && $others['have_stock_or_ordered'] ){
		$sql .= ' and (';
	}

	if( $have_stock!==null ){

		// non suivi en stock, ou en stock, ou en contremarque, ou parent avec au moins un enfant en stock / non suivi / en contremarque
		$sql_stock_conditions = '
		prd_follow_stock = 0
		or ' . prd_stocks_get_sql() . ' - sto_prepa > 0
		or prd_countermark = 1
		or exists (
		select 1 from prd_products as pch
		join prd_hierarchy as ph on
		pch.prd_tnt_id = ph.prd_tnt_id and pch.prd_id = ph.prd_child_id
		left join prd_stocks as pchs on
		pch.prd_tnt_id = pchs.sto_tnt_id and pch.prd_id = pchs.sto_prd_id and '.$dps.' = pchs.sto_dps_id and pchs.sto_is_deleted=0
		where
		ph.prd_parent_id = prd.prd_id and pch.prd_tnt_id = '.$config['tnt_id'].'
		and pch.prd_date_deleted is null and (
		pch.prd_follow_stock = 0 or
		' . prd_stocks_get_sql('pchs') . ' - ifnull(pchs.sto_prepa, 0) > 0 or
		pch.prd_countermark = 1
		)
		)
		';

		if( !isset($others['have_stock_or_ordered']) || !$others['have_stock_or_ordered'] ){
			$sql .= ' and ';
		}

		if( $have_stock ){
			$sql .= ' ('.$sql_stock_conditions.')';
		}else{
			$sql .= ' not ('.$sql_stock_conditions.')';
		}
	}
	if( $ordered!==null ){
		if( isset($others['have_stock_or_ordered']) && $others['have_stock_or_ordered'] ){
			$sql .= ' or (';
		}else{
			$sql .= ' and ';
		}

		if( $supplier===false ){
			if( $ordered ){
				$sql .= ' sto_com > 0';
			}else{
				$sql .= ' sto_com <= 0';
			}
		}else{
			if( $ordered ){
				$sql .= ' prd.prd_id in ( select prd_id from ord_products as op where op.prd_tnt_id='.$config['tnt_id'].' and prd_qte>0 and prd_ord_id in ( select ord_id from ord_orders where ord_tnt_id='.$config['tnt_id'].' and ord_usr_id='.$supplier.' and ord_state_id in (15, 16, 20) ) )';
			}else{
				$sql .= ' prd.prd_id not in ( select prd_id from ord_products as op where op.prd_tnt_id='.$config['tnt_id'].' and prd_qte>0 and prd_ord_id in ( select ord_id from ord_orders where ord_tnt_id='.$config['tnt_id'].' and ord_usr_id='.$supplier.' and ord_state_id in (15, 16, 20) ) )';
			}
		}

		if( isset($others['have_stock_or_ordered']) && $others['have_stock_or_ordered'] ){
			$sql .= ')';
		}
	}

	if( isset($others['have_stock_or_ordered']) && $others['have_stock_or_ordered'] ){
		$sql .= ')';
	}

	if( $orderable!==null ){
		if( $orderable ){
			$sql .= ' and prd_orderable and ('.($only_sleeping ? 'prd_sleep':'not prd_sleep').' or (' . prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('prd.prd_id', $dps, true).')>0)';
		}else{
			$sql .= ' and (not prd_orderable or (prd_sleep and (' . prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('prd.prd_id', $dps, true).')<=0))';
		}
	}
	if( $isSync===true ){
		$sql .= ' and prd_is_sync=1';
	}
	if( sizeof($exclude) ){
		$sql .= ' and prd.prd_id not in ('.implode(',', $exclude).')';
	}
	if( $name_start_with ){
		$sql .= ' and lower(prd.prd_name) like \''.addslashes( strtolower($name_start_with) ).'%\'';
	}

	//pour recuperer les meilleurs ventes de toutes les categories
	if( is_array($cat) && !count($cat) && $bestsellers){
		$sql .= ' and prd_bestseller_point > 0 ';
	}

	if( is_array($cat) && count($cat) && $bestsellers ){
		$sql .= ' and cly.cly_prd_is_bestseller > 0';
	}

	if( $cat===-1 ){
		$sql .= '
		and (
		cly.cly_cat_id is null
		or (cly.cly_cat_id in (select cat_id from prd_categories where cat_tnt_id=cly.cly_tnt_id and cat_date_deleted is not null))
		or (cly.cly_cat_id not in (select cat_id from prd_categories where cat_tnt_id=cly.cly_tnt_id and cat_date_deleted is null))
		)
		';
	}

	// Filtre sur les champs de poids
	if( $no_weight ){
		$sql .= ' and ( prd_weight is null or prd_weight_net is null ) and prd_ref not in (\''.implode('\',\'',$config['dlv_prd_references']).'\')';
	}

	if( is_numeric($nomenclature_type) && $nomenclature_type>=0 ){
		$sql .= ' and prd_nomenclature_type='.$nomenclature_type;
	}elseif( is_array($nomenclature_type) ){
		$ok_nomenclature = array();
		foreach( $nomenclature_type as $nt ){
			if( is_numeric($nt) && $nt>=0 )
				$ok_nomenclature[] = $nt;
		}
		if( sizeof($ok_nomenclature) ){
			$sql .= ' and prd_nomenclature_type in ('.implode( ', ', $ok_nomenclature ).')';
		}
	}

	if( $is_nomenclature!==null ){
		$sql .= '
		AND (
		'.( !$is_nomenclature ? 'NOT' : '' ).' EXISTS (
		SELECT 1 FROM prd_nomenclatures_products
		JOIN prd_products AS pch ON pnp_tnt_id = pch.prd_tnt_id AND pnp_child_id = pch.prd_id
		WHERE pnp_tnt_id = '.$config['tnt_id'].' AND pnp_parent_id = prd.prd_id
		AND pch.prd_date_deleted IS NULL
		)
		';
		if( $config['prd_linked_nomenclature_activate'] ){
			$sql .= ( !$is_nomenclature ? ' OR ' : ' AND ' ).' prd_nomenclature_type != '.NM_TYP_LINKED;
		}
		$sql .= '
		)
		';
	}

	if( $follow_stock!==null ){
		$sql .= ' and prd.prd_follow_stock='.( $follow_stock ? '1' : '0' );
	}

	if( trim($barcode)!='' ){
		$barcode = str_replace( ' ', '', $barcode );
		$sql .= ' and prd.prd_barcode="'.addslashes( trim($barcode) ).'"';
	}

	if( $in_garanty!==null ){
		$sql .= ' and ifnull(prd.prd_garantie, "")'.( $in_garanty ? '!=' : '=' ).'""';
	}

	if( sizeof($sell_unit) ){
		$sql .= ' and ifnull(prd_sun_id, -1) in ('.implode(', ', $sell_unit).')';
	}

	$ref_pattern_sql = array();
	if( is_array($ref_pattern) && sizeof($ref_pattern) ){
		foreach( $ref_pattern as $pattern => $is_inc ){
			if( trim($pattern) ){
				$is_case_sensitive = !preg_match('/\*{1,2}/', $pattern);
				$pattern = str_replace('*', '%', wildcard_slashes(addslashes($pattern)));

				if( $is_case_sensitive ){
					$ref_pattern_sql[] = 'prd_ref'.( $is_inc ? '' : ' not' ).' like "'.$pattern.'"';
					continue;
				}
				$ref_pattern_sql[] = 'upper(prd_ref)'.( $is_inc ? '' : ' not' ).' like upper("'.$pattern.'")';
			}
		}
	}

	if( sizeof($ref_pattern_sql) ){
		$sql .= ' and ('.implode(' or ', $ref_pattern_sql).')';
	}

	$title_pattern_sql = array();
	if( isset($others['title_pattern']) && is_array($others['title_pattern']) && sizeof($others['title_pattern']) ){
		foreach( $others['title_pattern'] as $pattern => $is_inc ){
			if( trim($pattern) ){
				$is_case_sensitive = !preg_match('/\*{1,2}/', $pattern);
				$pattern = str_replace('*', '', $pattern);

				if( $is_case_sensitive ){
					$title_pattern_sql[] = 'prd_name'.( $is_inc ? '' : ' not' ).' like "'.$pattern.'"';
					continue;
				}
				$ex = explode(' ', $pattern);

				if( !count($ex) ){
					continue;
				}

				foreach($ex as $val){
					$val = trim($val);

					if( $val == '' ){
						continue;
					}
					$title_sql = '';
					if( count($title_pattern_sql) ){
						$title_sql = $is_inc ? 'and ' : 'or ';
					}
					$title_sql .= 'lower(prd_name)'.( $is_inc ? '' : ' not' ).' like lower("%'.wildcard_slashes(addslashes($val)).'%")';
					$title_pattern_sql[] = $title_sql;
				}
			}
		}
	}

	if( sizeof($title_pattern_sql) ){
		$sql .= ' and ('.implode(' ', $title_pattern_sql).')';
	}

	// gestion des restrictions si variable activée (avec use_catalog_restrictions_usr_price)
	if ($config['use_catalog_restrictions'] && $config['use_catalog_restrictions_usr_price']) {
		$sql .= ' and exists ('.prd_restrictions_get_ids(CLS_PRODUCT, 'prd.prd_id').')';
	}

	if( isset($others['confo_stock']) || isset($others['confo_sort_price']) ){
		$cnd_usr_code = 0;
		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] > 0 ){
			$cnd_usr_code = gu_users_get_parent_ref( $_SESSION['admin_view_user'], true );
		}elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] > 0 ){
			$cnd_usr_code = gu_users_get_parent_ref( $_SESSION['usr_id'], true );
		}
	}

	if( count($others) ){
		// Hack Conforama pour la gestion du filtre "Disponibilité"
		if( isset($others['confo_stock']) && is_array($others['confo_stock']) && count($others['confo_stock']) ){
			$sql .= ' and ( ';

			$first = true;
			foreach( $others['confo_stock'] as $one_confo_stock ){
				if( !$first ){
					$sql .= ' or ';
				}

				$sql .= '
					exists(
						select 1 from prd_stocks
							join prc_prices
								on (prc_tnt_id = '.$config['tnt_id'].' and prc_prd_id = sto_prd_id and prc_dps_id = sto_dps_id)
							join prc_price_conditions
								on (ppc_tnt_id = '.$config['tnt_id'].' and ppc_prc_id = prc_id
									and ppc_fld_id = '._FLD_USR_REF.'
									and ppc_value = "'.htmlspecialchars( $cnd_usr_code ).'"
								)
						where sto_tnt_id = '.$config['tnt_id'].'
							and sto_prd_id = prd_id
				';

				if( $one_confo_stock == '1' ){
					$sql .= ' and sto_qte > 10';
				}elseif( $one_confo_stock == '2' ){
					$sql .= '
						and sto_qte > 0
						and sto_qte <= 10
					';
				}elseif( $one_confo_stock == '3' ){
					$sql .= ' and sto_qte <= 0';
				}

				$sql .= '
					)

					or exists(
						select 1 from fld_object_values
						where pv_tnt_id = '.$config['tnt_id'].'
							and pv_obj_id_0 = prd_id
							and pv_fld_id = 105895
							and (pv_value = "DIRECT" or pv_value = "IMPORT")
					)
				';

				$first = false;
			}

			$sql .= '
				)
			';
		}
	}

	$sql .= '
		group by prd.prd_id
	';

	// Tri du résultat (valeurs par défaut)
	$price_sort_dir = '';
	$sort_cat = array( 'type'=>$config['cat_sort_type'], 'dir'=>$config['cat_sort_dir'], 'is_recursive'=>false );

	if( $sort_cat['type'] == SORT_PRICE ){
		// Le tri par prix n'est possible que si une catégorie est donnée en paramètre et qu'il ne s'agisse pas de la cat_root
		if( !is_array($cat) || !count($cat) || ($cat[0] == $config['cat_root'] && $config['cat_root'] > 0) ){
			$sort_cat = false;
		}
	}

	// Converti le paramètre de tri en SQL
	$sort_final = [];

	$cumul_sort = is_string($cumul_sort) && in_array( $cumul_sort, array('before', ' after') ) ? $cumul_sort : false;

	if( $sort==false || !is_array($sort) || sizeof($sort)==0 || $cumul_sort ){
		$tmp = array();
		if( is_array($cat) && count($cat) ){
			$tmp = prd_categories_sort_get( array_shift($cat) );
		}



		$sort_cat = is_array($tmp) && sizeof($tmp) ? $tmp : $sort_cat;

		if( is_array($sort_cat) && sizeof($sort_cat) ){
			switch( $sort_cat['type'] ){
				case SORT_PERSO :
				$sort_final = array( 'cly_prd_pos '.$sort_cat['dir'] );
				break;
				case SORT_ALPHA :
				$sort_final = array( 'if(prd_title="",prd_name,prd_title) '.$sort_cat['dir'] ); break;
				case SORT_PRICE :
				$with_price = true;
				$price_sort_dir = $sort_cat['dir']; break;
				case SORT_REF :
				$sort_final = array( 'prd_ref '.$sort_cat['dir'] ); break;
				case SORT_BRAND :
				$sort_final = array( 'if(brd_title!="",brd_title,brd_name) '.$sort_cat['dir'] ); break;
				case SORT_COMPLETION :
				$sort_final = array( 'prd_completion '.$sort_cat['dir'] ); break;
				case SORT_DATE_CREATED :
				$sort_final = array( 'prd_date_created '.$sort_cat['dir'] ); break;
				case SORT_DATE_PUBLISH :
				$sort_final = array( 'prd_date_published '.$sort_cat['dir'] ); break;
				case SORT_DATE_PROMO :
				$sort_final = $promotion ? array( 'prd_date_published '.$sort_cat['dir'] ) : array( 'if(prd_title="",prd_name,prd_title) asc' );
				break;
				case SORT_SELLED :
				$sort_final = array( 'prd_selled '.$sort_cat['dir'] ); break;
				case SORT_RANDOM :
				$sort_final = array( 'rand()' ); break;
				case SORT_STOCK :
				$sort_final = array( prd_stocks_get_sql() . '-ifnull(sto_prepa,0) '.$sort_cat['dir'] ); break;
			}
		}
	}

	if( !$only_prd_ids ){
		if( is_array($sort) && sizeof($sort) ){
			// Récupère un éventuel tri par prix
			foreach( $sort as $col=>$dir ){
				$dir = !is_array($dir) && strtolower( trim($dir) )=='desc' ? 'desc' : 'asc';
				if( substr($col, 0, 4)=='fld-' ){
					$fld_id = substr( $col, 4, strlen($col)-4 );
					if( is_numeric($fld_id) && $fld_id>0 ){
						$type_fld = fld_fields_get_type( $fld_id );
						$numeric_types = array( FLD_TYPE_INT, FLD_TYPE_FLOAT, FLD_TYPE_REFERENCES_ID );
						$sub_sql = '
						(
						select
						'.( in_array( $type_fld, $numeric_types ) ? 'cast(pv_value as decimal)' : 'pv_value' ).'
						from
						fld_object_values
						where
						pv_tnt_id='.$config['tnt_id'].' and pv_lng_code=\''.strtolower($lng).'\' and pv_obj_id_0=prd.prd_id and pv_fld_id='.$fld_id.'
						) '.$dir.'
						';
						if( $lng!=$config['i18n_lng'] && in_array( $lng, $config['i18n_lng_used'] ) ){
							$sub_sql = '
							(
							select
							'.( in_array( $type_fld, $numeric_types ) ? 'cast(ifnull(v1.pv_value, v2.pv_value) as decimal)' : 'ifnull(v1.pv_value, v2.pv_value)' ).'
							from
							fld_object_values as v2 left join fld_object_values as v1 on (
							\''.strtolower($lng).'\'=v1.pv_lng_code and v2.pv_tnt_id=v1.pv_tnt_id and v2.pv_fld_id=v1.pv_fld_id and v2.pv_obj_id_0=v1.pv_obj_id_0
							)
							where
							v2.pv_tnt_id='.$config['tnt_id'].' and v2.pv_lng_code=\''.$config['i18n_lng'].'\' and v2.pv_obj_id_0=prd.prd_id and v2.pv_fld_id='.$fld_id.'
							) '.$dir.'
							';
						}

						if( !$cumul_sort || $cumul_sort=='after' ){
							array_push( $sort_final, $sub_sql );
						}else{
							array_unshift( $sort_final, $sub_sql );
						}
					}
				}else{
					switch( $col ){
						case 'set':
							if( is_array($id) && sizeof($id)>1 ){
								$query_orderby = 'case ';
								$cpt = 0;
								foreach( $id as $current_id ){
									$query_orderby .= 'when prd.prd_id='.$current_id.' then '.$cpt.' ';
									$cpt++;
								}
								$query_orderby .= ' end asc';

								if( !$cumul_sort || $cumul_sort=='after' ){
									array_push( $sort_final, $query_orderby );
								}else{
									array_unshift( $sort_final, $query_orderby );
								}
							}
							break;
						case 'perso' :
							if( is_array($cat) && !empty($cat) ){
								if( !$cumul_sort || $cumul_sort=='after' ){
									array_push( $sort_final, 'cly_prd_pos '.$dir );
								}else{
									array_unshift( $sort_final, 'cly_prd_pos '.$dir );
								}
							}
							break;
						case 'stock' :
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, prd_stocks_get_sql() . '-ifnull(sto_prepa,0) '.$dir );
							}else{
								array_unshift( $sort_final, prd_stocks_get_sql() . 'ifnull(sto_prepa,0) '.$dir );
							}
							break;
						case 'catchilds' :
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push ($sort_final, 'prd_childonly '.$dir );
							}else{
								array_unshift( $sort_final, 'prd_childonly '.$dir );
							}
							break;
						case 'ref':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'prd_ref '.$dir );
							}else{
								array_unshift( $sort_final, 'prd_ref '.$dir );
							}
							break;
						case 'hits':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'prd_hits '.$dir );
							}else{
								array_unshift( $sort_final, 'prd_hits '.$dir );
							}
							break;
						case 'title':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'title '.$dir );
							}else{
								array_unshift( $sort_final, 'title '.$dir );
							}
							break;
						case 'name':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'name '.$dir );
							}else{
								array_unshift( $sort_final, 'name '.$dir );
							}
							break;
						case 'brand':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'if(brd_title!="",brd_title,brd_name) '.$dir );
							}else{
								array_unshift( $sort_final, 'if(brd_title!="",brd_title,brd_name) '.$dir );
							}
							break;
						case 'completion':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'prd_completion '.$dir );
							}else{
								array_unshift( $sort_final, 'prd_completion '.$dir );
							}
							break;
						case 'date-created':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'prd_date_created '.$dir );
							}else{
								array_unshift( $sort_final, 'prd_date_created '.$dir );
							}
							break;
						case 'date-published':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'date_published '.$dir );
							}else{
								array_unshift( $sort_final, 'date_published '.$dir );
							}
							break;
						case 'date-promo':
							if( $promotion ){
								if( !$cumul_sort || $cumul_sort=='after' ){
									array_push( $sort_final, 'pmt_date_start '.$dir );
								}else{
									array_unshift( $sort_final, 'pmt_date_start '.$dir );
								}

								if( !$cumul_sort || $cumul_sort=='after' ){
									array_push( $sort_final, 'pmt_date_stop '.$dir );
								}else{
									array_unshift( $sort_final, 'pmt_date_stop '.$dir );
								}
							}
							break;
						case 'publish':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'prd_publish '.$dir );
							}else{
								array_unshift( $sort_final, 'prd_publish '.$dir );
							}
							break;
						case 'publish-cat':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'prd_publish_cat '.$dir );
							}else{
								array_unshift( $sort_final, 'prd_publish_cat '.$dir );
							}
							break;
						case 'cat':
							if( $cat!=0 ){
								if( !$cumul_sort || $cumul_sort=='after' ){
									array_push( $sort_final, 'cat_pos '.$dir );
								}else{
									array_unshift( $sort_final, 'cat_pos '.$dir );
								}

								if( !$cumul_sort || $cumul_sort=='after' ){
									array_push( $sort_final, 'if(cat_title!="",cat_title,cat_name) '.$dir );
								}else{
									array_unshift( $sort_final, 'if(cat_title!="",cat_title,cat_name) '.$dir );
								}
							}
							break;
						case 'random':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'rand()' );
							}else{
								array_unshift( $sort_final, 'rand()' );
							}
							break;
						case 'selled':
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, 'selled '.$dir );
							}else{
								array_unshift( $sort_final, 'selled '.$dir );
							}
							break;
						case 'bestseller':
							if( is_array($cat) && count($cat) ){
								if( !$cumul_sort || $cumul_sort=='after' ){
									array_push( $sort_final, 'cly_prd_is_bestseller '.$dir );
								}else{
									array_unshift( $sort_final, 'cly_prd_is_bestseller '.$dir );
								}
							}

							if( is_array($cat) && !count($cat) && $bestsellers){
								if( !$cumul_sort || $cumul_sort=='after' ){
									array_push( $sort_final, 'prd_bestseller_point desc' );
								}else{
									array_unshift( $sort_final, 'prd_bestseller_point desc' );
								}
							}
							break;
						case 'fld':
							// on ne peut trier par champ avancé que dans le cas où $fld contient un seul ID de champ, et pas de valeur(s)
							if( ( is_numeric($fld) && $fld>0 ) || ( is_array($fld) && sizeof($fld)==1 && is_numeric($fld[0]) && $fld[0]>0 ) ){
								if( is_array($fld) ) $fld = $fld[0];
								$type_fld = fld_fields_get_type( $fld );
								$numeric_types = array( FLD_TYPE_INT, FLD_TYPE_FLOAT, FLD_TYPE_REFERENCES_ID );
								$sub_sql = '
								(
								select
								'.( in_array( $type_fld, $numeric_types ) ? 'cast(pv_value as decimal)' : 'pv_value' ).'
								from
								fld_object_values
								where
								pv_tnt_id='.$config['tnt_id'].' and pv_lng_code=\''.strtolower($lng).'\' and pv_obj_id_0=prd.prd_id and pv_fld_id='.$fld.'
								) '.$dir.'
								';
								if( $lng!=$config['i18n_lng'] && in_array( $lng, $config['i18n_lng_used'] ) ){
									$sub_sql = '
									(
									select
									'.( in_array( $type_fld, $numeric_types ) ? 'cast(ifnull(v1.pv_value, v2.pv_value) as decimal)' : 'ifnull(v1.pv_value, v2.pv_value)' ).'
									from
									fld_object_values as v2 left join fld_object_values as v1 on (
									\''.strtolower($lng).'\'=v1.pv_lng_code and v2.pv_tnt_id=v1.pv_tnt_id and v2.pv_fld_id=v1.pv_fld_id and v2.pv_obj_id_0=v1.pv_obj_id_0
									)
									where
									v2.pv_tnt_id='.$config['tnt_id'].' and v2.pv_lng_code=\''.$config['i18n_lng'].'\' and v2.pv_obj_id_0=prd.prd_id and v2.pv_fld_id='.$fld.'
									) '.$dir.'
									';
								}

								if( !$cumul_sort || $cumul_sort=='after' ){
									array_push( $sort_final, $sub_sql );
								}else{
									array_unshift( $sort_final, $sub_sql );
								}
							}
							break;
						case 'note' :
							$sqlNote = '(select avg(cnt_note) from gu_messages where cnt_tnt_id=prd.prd_tnt_id and cnt_prd_id=prd.prd_id) '.$dir;
							$sqlDelivery = '(select avg(cnt_note_delivery) from gu_messages where cnt_tnt_id=prd.prd_tnt_id and cnt_prd_id=prd.prd_id) '.$dir;
							$sqlPackage = '(select avg(cnt_note_package) from gu_messages where cnt_tnt_id=prd.prd_tnt_id and cnt_prd_id=prd.prd_id) '.$dir;

							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, $sqlNote );
							}else{
								array_unshift( $sort_final, $sqlNote );
							}

							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, $sqlDelivery );
							}else{
								array_unshift( $sort_final, $sqlDelivery );
							}

							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, $sqlPackage );
							}else{
								array_unshift( $sort_final, $sqlPackage );
							}
							break;
						case 'related-count' :
							$sub_sql = '
							(
							select count(*) as related_count
							from prd_relations as pr
							where pr.rel_tnt_id='.$config['tnt_id'].'
							and pr.rel_dst_id=prd.prd_id
							and pr.rel_is_deleted=0
							and pr.rel_src_id in (
							select x.prd_id
							from prd_products as x
							where x.prd_tnt_id='.$config['tnt_id'].'
							and x.prd_date_deleted is null
							)
							) ' . $dir . '
							';

							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, $sub_sql );
							}else{
								array_unshift( $sort_final, $sub_sql );
							}
							break;
						case 'new' :
							$sub_sql = '(prd_new!="-1" and if( '.$config['prd_new_days'].' != 0, (prd_new="1" or datediff(now(),'.$col_new.')<='.$config['prd_new_days'].'), prd_new="1")) '.$dir;

							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, $sub_sql );
							}else{
								array_unshift( $sort_final, $sub_sql );
							}
							break;
						case 'child_pos' :
							if( sizeof($parent)==1 ){
								$sub_sql = 'hch.prd_child_pos '.$dir;

								if( !$cumul_sort || $cumul_sort=='after' ){
									array_push( $sort_final, $sub_sql );
								}else{
									array_unshift( $sort_final, $sub_sql );
								}
							}
							break;
						case 'brd_pos' :
							$sub_sql = 'prd_brd_pos '.$dir;
							if( !$cumul_sort || $cumul_sort=='after' ){
								array_push( $sort_final, $sub_sql );
							}else{
								array_unshift( $sort_final, $sub_sql );
							}
							break;
						case 'prcprices':
							$sub_sql = '(
								select prc_value
								from prc_prices
								where prc_tnt_id = '.$config['tnt_id'].'
									and prc_prd_id = prd.prd_id
									and prc_is_deleted = 0
									and prc_is_promotion = 0
								limit 1
							) '.$dir;

							array_push( $sort_final, $sub_sql );
							break;
					}
				}

				// Tri par prix
				if( $col=='price' ){
					$price_sort_dir = $dir;
					break;
				}
			}
		}
	}

	// Ajoute la clause de tri
	if( sizeof($sort_final)==0 ) $sort_final = array( 'prd_name asc' );

	if( isset($others['confo_sort_price']) && in_array($others['confo_sort_price'], ['asc', 'desc']) ){
		$sort_final = [];
	}

	// si rien sur les prix et tri eprso spécifié, on applique le tri immédiatement
	if( $price_sort_dir=='' && ( (is_array($sort) && sizeof($sort)) || $sort_cat['type']!=SORT_PRICE ) && is_array($sort_final) && count($sort_final) ){
		$sql .= ' order by '.implode( ', ', $sort_final ).' ';
	}

	// Gère la limitation des résultats
	if( !is_numeric($rowstart) || $rowstart < 0 ){
		$rowstart = 0;
	}
	$origin_maxrows = !is_numeric( $maxrows ) || $maxrows <= 0 ? null : $maxrows;
	if( sizeof($id) == 1 ){
		$maxrows = 1;
	}elseif( !is_numeric($maxrows) ){
		$maxrows = -1;
		$origin_maxrows = null;
	}elseif( $promotion && $check_promotions ){
		// note : pour la récupération de promotion stricte, la pagination est déplacée
		$maxrows = -1;
	}

	if( count($others) ){
		// Hack Conforama pour le tri selon un indice de prix
		if( isset($others['confo_sort_price']) && in_array($others['confo_sort_price'], ['asc', 'desc']) ){
			$sql .= '
				order by ifnull((
					select min( cast(replace(pv_value, ",", "") as decimal(10,2)) )
					from prc_prices
						join prc_price_conditions on (ppc_tnt_id = '.$config['tnt_id'].' and ppc_prc_id = prc_id)
						join fld_object_values on (pv_tnt_id = '.$config['tnt_id'].' and pv_obj_id_0 = prc_id and pv_fld_id = 114139)
					where prc_tnt_id = '.$config['tnt_id'].'
						and prc_prd_id = prd_id
						and ppc_fld_id = '._FLD_USR_REF.'
						and ppc_value = "'.htmlspecialchars( $cnd_usr_code ).'"
						and prc_date_deleted is null
				), '.( $others['confo_sort_price'] == 'asc' ? '999999999' : '-1').') '.$others['confo_sort_price'].'
			';
		}
	}

	$sql .= 'limit '.$rowstart.', '.( $maxrows>0 ? $maxrows : '18446744073709551615' );

	if( $with_price ){
		if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
			$usr_holder = gu_users_get_prices_holder( $user );
			$prc = gu_users_get_prc( $usr_holder, true );

			$sql = '
				select
					tmp.*,
					prc_value as price_ht,
					ptv_tva_rate as tva_rate
				from
					( '.$sql.' ) as tmp
				left join
					prc_tvas on (
						ptv_date_deleted is null
						and ptv_tnt_id='.$config['tnt_id'].'
						and ptv_prd_id=tmp.id
						and ifnull(ptv_cnt_code, "FR") = "FR"
					)
				left join
					prc_prices on (
						prc_is_deleted=0
						and prc_tnt_id='.$config['tnt_id'].'
						and prc_date_start<=now() and prc_date_end>now()
						and prc_prd_id=tmp.id
						and exists (
							select 1
							from prc_price_conditions
							where ppc_prc_id=prc_id
								and ppc_tnt_id='.$config['tnt_id'].'
								and ppc_fld_id = '._FLD_USR_PRC.'
								and ppc_value = '.$prc.'
						)
						and prc_qte_min=1
						and prc_is_promotion=0
					)
				group by tmp.id
			';
		}elseif( in_array((int)$config['tnt_id'], [1024]) ){
			$cnt_code = gu_users_get_cnt_code($user);
			$cnt_code = str_replace( '"', '', $cnt_code );
			if( $cnt_code === 'NULL' ){
				$cnt_code = 'FR';
			}

			$sql = '
				select
					tmp.*,
					prc_value as price_ht,
					ptv_tva_rate as tva_rate
				from
					( '.$sql.' ) as tmp
				join
					prc_tvas
				on
						ptv_date_deleted is null
					and ptv_tnt_id='.$config['tnt_id'].'
					and ptv_prd_id=tmp.id
					and ptv_cnt_code = "'.htmlspecialchars($cnt_code).'"

				join
					prc_prices
				on
						prc_is_deleted=0
					and prc_tnt_id='.$config['tnt_id'].'
					and ( prc_date_start<=now() ) and ( prc_date_end>now() )
					and prc_prd_id=tmp.id
					and exists (
						select 1 from prc_price_conditions where ppc_prc_id=prc_id and ppc_tnt_id='.$config['tnt_id'].'
					)
					and prc_qte_min=1
					and prc_is_promotion=0
				group by tmp.id
			';
		}else{
			$exempt = gu_users_is_tva_exempt( $user );
			$usr_holder = gu_users_get_prices_holder( $user );
			$prc = gu_users_get_prc( $usr_holder, true );
			$usr_ref = gu_users_get_ref( $usr_holder, true );
			$lng_base = wst_websites_languages_default();

			$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

			if( $use_child_price ){
				// si produit avec des enfants et parent non commandable, le prix mini des enfants est retourné (et utilisé pour un éventuel tri)
				$sql = '
				select tmp.*,
				if(
					'.( !isset($config['get_price_parent_orderable']) || $config['get_price_parent_orderable'] ? 'tmp.orderable=0 and' : '' ).' exists (
				select 1 from prd_hierarchy as hy
				join prd_products as hchild on hy.prd_tnt_id = hchild.prd_tnt_id and  hy.prd_child_id = hchild.prd_id
				where hy.prd_tnt_id='.$config['tnt_id'].' and hy.prd_parent_id=tmp.id
				and hchild.prd_date_deleted is null
				), (
				select get_price_ht( '.$config['tnt_id'].', hchild.prd_id, '.$usr_holder.', 1, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.$lng_base.'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", hchild.prd_brd_id, hchild.prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'hchild.prd_ecotaxe').' )
				from prd_hierarchy as hy
				join prd_products as hchild on hy.prd_tnt_id = hchild.prd_tnt_id and  hy.prd_child_id = hchild.prd_id
				where hy.prd_tnt_id='.$config['tnt_id'].' and hy.prd_parent_id=tmp.id and hchild.prd_date_deleted is null
				order by get_price_ht( '.$config['tnt_id'].', hchild.prd_id, '.$usr_holder.', 1, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.$lng_base.'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", hchild.prd_brd_id, hchild.prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'hchild.prd_ecotaxe').' ) asc
				limit 0, 1
				), get_price_ht( '.$config['tnt_id'].', tmp.id, '.$usr_holder.', 1, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.$lng_base.'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", tmp.brd_id, tmp.centralized, '.($apply_remise_ecotaxe ? '0' : 'tmp.ecotaxe').' )
				) as price_ht,
				if(
					'.( !isset($config['get_price_parent_orderable']) || $config['get_price_parent_orderable'] ? 'tmp.orderable=0 and' : '' ).' exists (
				select 1 from prd_hierarchy as hy
				join prd_products as hchild on hy.prd_tnt_id = hchild.prd_tnt_id and  hy.prd_child_id = hchild.prd_id
				where hy.prd_tnt_id='.$config['tnt_id'].' and hy.prd_parent_id=tmp.id
				and hchild.prd_date_deleted is null
				), (
				select '.( $exempt ? '1' : 'get_tva( '.$config['tnt_id'].', hchild.prd_id, '.gu_users_get_accouting_category( $user, true ).', '.gu_users_get_cnt_code($user, true).' )' ).'
				from prd_hierarchy as hy
				join prd_products as hchild on hy.prd_tnt_id = hchild.prd_tnt_id and  hy.prd_child_id = hchild.prd_id
				where hy.prd_tnt_id='.$config['tnt_id'].' and hy.prd_parent_id=tmp.id and hchild.prd_date_deleted is null
				order by get_price_ht( '.$config['tnt_id'].', hchild.prd_id, '.$usr_holder.', 1, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.$lng_base.'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", hchild.prd_brd_id, hchild.prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'hchild.prd_ecotaxe').' ) asc
				limit 0, 1
				), '.( $exempt ? '1' : 'get_tva( '.$config['tnt_id'].', tmp.id, '.gu_users_get_accouting_category( $user, true ).', '.gu_users_get_cnt_code($user, true).' )' ).'
				) as tva_rate
				from ( '.$sql.' ) as tmp
				';
			}else{
				$sql = '
				select tmp.*, get_price_ht( '.$config['tnt_id'].', tmp.id, '.$usr_holder.', 1, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.$lng_base.'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", tmp.brd_id, tmp.centralized, '.($apply_remise_ecotaxe ? '0' : 'tmp.ecotaxe').' ) as price_ht, '.( $exempt ? '1' : 'get_tva( '.$config['tnt_id'].', tmp.id, '.gu_users_get_accouting_category( $user, true ).', '.gu_users_get_cnt_code($user, true).' )' ).' as tva_rate
				from ( '.$sql.' ) as tmp
				';
			}
		}

		if( $price_sort_dir ){
			$sql .= ' order by if( sell_weight and ifnull(weight_net, 0)>0, price_ht*weight_net/1000, price_ht) '.$price_sort_dir;
		}elseif( (!is_array($sort) || !sizeof($sort)) && $sort_cat['type']==SORT_PRICE && is_array($sort_final) && count($sort_final) ){
			$sql .= ' order by '.implode(', ', $sort_final );
		}

		$sql = '
			select tmp2.*, tmp2.tva_rate * tmp2.price_ht as price_ttc
			from (
				'.$sql. '
				limit 0, 18446744073709551615
			) as tmp2
			where 1
		';

		if( $price_min!==null ){
			$price_min = str_replace( array(',', ' '), array('.', ''), $price_min );
			if( is_numeric($price_min) ){
				$sql .= ' and '.( $price_borne_ttc ? 'tmp2.tva_rate * tmp2.price_ht' : 'tmp2.price_ht' ).' >= '.$price_min;
			}
		}

		if( $price_max!==null ){
			$price_max = str_replace( array(',', ' '), array('.', ''), $price_max );
			if( is_numeric($price_max) ){
				$sql .= ' and '.( $price_borne_ttc ? 'tmp2.tva_rate * tmp2.price_ht' : 'tmp2.price_ht' ).' <= '.$price_max;
			}
		}

		if( $sort_cat['type'] == SORT_PERSO ) {
			if( (is_array($cat) && count($cat)) || is_array($exclude_cat) ){
				$sql .= ' order by tmp2.prd_pos asc';
			}
		}elseif( isset($sort['prcprices']) && in_array($sort['prcprices'], ['asc', 'desc']) ){
			$sql .= ' order by tmp2.price_ht '.$sort['prcprices'];
		}elseif( $price_sort_dir ){
			$sql .= ' order by if( sell_weight and ifnull(weight_net, 0)>0, price_ht*weight_net/1000, price_ht) '.$price_sort_dir;
		}

	}
    echo($sql);
    echo "\n========================================================\n";
	$r = ria_mysql_query($sql);
	if( $check_promotions && $promotion && $r ){
		// récupération du client connecté
		$usr_id = 0;
		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] ){
			$usr_id = $_SESSION['admin_view_user'];
		}elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] ){
			$usr_id = $_SESSION['usr_id'];
		}

		$ar_prd = array();
		while( $p = ria_mysql_fetch_assoc($r) ){
			// récupère les promotions rattachées au produit
			if( $rprom = prc_prices_get_promotions( $p['id'], 1, 1, false, null, 0, 0 ) ){
				while( $prom = ria_mysql_fetch_assoc($rprom) ){
					// détermine si la promotion s'applique dans le contexte
					if( prc_prices_is_apply( $prom['id'], $p['id'], $usr_id ) ){
						if (array_key_exists('price_ht', $p)) {
							// 1 => Nouveau tarif, 2 => Remise en pourcentage, 3 => Remise en valeur
							switch ($prom['type']) {
								case 1:
									$p['promo_price_ht'] = $prom['value'];
									break;
								case 2:
									$p['promo_price_ht'] = $p['price_ht'] * ( (100 - $prom['value']) / 100 );
									break;
								case 3:
									$p['promo_price_ht'] = $p['price_ht'] - $prom['value'];
									break;
							}
						}

						$ar_prd[] = $p;
						break;
					}
				}
			}
		}

		// pagination du tableau
		$ar_prd = array_slice($ar_prd, 0, $origin_maxrows);

		return $ar_prd;
	}

	return $r;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de rechercher un produit avec un début de référence et/ou une partie d'un nom
 *	@param string $search contenu recherché
 *	@return resource Retourne un résultat MySQL :
 *			- id : identifiant du produit
 *			- ref : référence du produit
 *			- title : titre du produit
 *			- name : description du produit
 */
function prd_products_get_like( $search ){
	global $config;

	$sub_sql = '';
	if( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true ){
		$sub_sql = ' and ( (prd_is_sync and prd_publish) or not prd_is_sync ) ';
	}

	$search = addslashes(mb_strtoupper(trim($search), 'UTF-8'));
	$search_esc = str_replace(' ','%',($search));

	return ria_mysql_query('
		select prd_id as id, prd_ref as ref, prd_name as name, prd_title as title, INSTR(upper(prd_ref),\''.$search.'\') as pos
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_date_deleted is null
			and not prd_sleep
			and upper(prd_ref) like \''.$search.'%\'
			'.$sub_sql.'

		union
		select prd_id as id, prd_ref as ref, prd_name as name, prd_title as title, INSTR(upper(prd_name),\''.$search.'\') as pos
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_date_deleted is null
			and not prd_sleep
			and upper(prd_name) like \''.$search.'%\'
			'.$sub_sql.'

		union
		select prd_id as id, prd_ref as ref, prd_name as name, prd_title as title, INSTR(upper(prd_name),\''.$search_esc.'\') as pos
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_date_deleted is null
			and not prd_sleep
			and upper(prd_name) like \'%'.$search_esc.'%\'
			'.$sub_sql.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer un produit par sa référence
 *	@param string $ref Obligatoire, référence du début, il peut s'agit d'un début de référence si le second paramètre est à true
 *	@param $is_start Optionnel, si la référence passée en paramètre est un début, par défaut à false
 *	@param $case_sensitive Optionnel, détermine si la recherche est sensible à la casse
 *	@param $sub_match Optionnel, tableau de caractères (ou de chaines) à remplacer dans la référence du produit. Chaque substitution est un sous-tableau à deux colonnes. Exemple :
 *		array(
 *			array('src' => '/', 'dst' => '^'),
 *			array('src' => '$', 'dst' => 'DOLLAR')
 *		)
 *
 *	@return array Retourne un tableau MySQL contenant les résultats :
 *				- id : identifiant du produit
 *				- ref : référence de produit
 *				- title : titre du produit
 *				- name : nom du produit
 *				- desc : description du produit
 *				- is_sync : booléen indiquant si le produit est synchronisé ou non
 *	@return bool Retourne false si la référence n'existe pas
 */
function prd_products_get_byref( $ref, $is_start=false, $case_sensitive=true, $sub_match=array() ){

	$ref = trim($ref);
	if( $is_start && !$ref ){
		return false;
	}

	global $config;

	$sql = '
	select prd_id as id, prd_name as name, prd_title as title, prd_desc as "desc", prd_ref as ref, prd_is_sync as is_sync
	from prd_products
	where prd_tnt_id = '.$config['tnt_id'].' and prd_date_deleted is null
	';

	$column = 'prd_ref';
	foreach( $sub_match as $sub ){
		if( is_array($sub) && isset($sub['src']) && isset($sub['dst']) ){
			$column = 'replace('.$column.', "'.addslashes($sub['src']).'", "'.addslashes($sub['dst']).'")';
		}
	}

	if( $is_start ){
		if( $case_sensitive ){
			$sql .= ' and '.$column.' like "'.wildcard_slashes(addslashes($ref), '_').'%"';
		}else{
			$sql .= ' and lower('.$column.') like "'.wildcard_slashes(addslashes(strtolower($ref)), '_').'%"';
		}
	}else {
		if( $case_sensitive ){
			$sql .= ' and '.$column.' = "'.addslashes($ref).'"';
		}else{
			$sql .= ' and lower('.$column.') = "'.addslashes(strtolower($ref)).'"';
		}
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Permet de récupérer un produit par son nom (nom sage ou nom redéfini)
 *	@param string $name Obligatoire, nom du produit recherché
 *	@return resource Retourne un résultat MySQL contenant les colonnes :
 *				- id : identifiant du produit
 *				- ref : référence du produit
 *				- title : titre du produit
 *				- name : nom du produit
 *				- desc : description du produit
 *	@return bool Retourne false si le paramètre est omis ou bien qu'il n'existe aucun produit avec ce nom
 */
function prd_products_get_byname( $name ){
	global $config;

	if( trim($name)=='' ) return false;

	return ria_mysql_query( '
		select prd_id as id, prd_ref as ref, prd_name as name, prd_title as title
		from prd_products
		where prd_date_deleted is null
		and prd_tnt_id='.$config['tnt_id'].'
		and ( lower(prd_name)= "'.addslashes(strtolower($name)).'" or lower(prd_title)="'.addslashes(strtolower($name)).'" )
	');

}
// \endcond

// \cond onlyria
/** Assigne une nouvelle valeur au champ prd_sell_weight, qui détermine le type de vente du produit (à l'unité de mesure (poids, volume, etc...) ou à une unité indivisible)
 *	@param int $prd Identifiant de l'article
 *	@param bool $is_weight Optionnel, type de vente (par défaut, à l'unité indivisible)
 *	@return bool true en cas de succès, False en cas d'échec
 */
function prd_products_set_is_selling_weight( $prd, $is_weight=false ){
	global $config;

	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	$is_weight = $is_weight ? 1 : 0;

	return ria_mysql_query('
		update prd_products
			set prd_sell_weight='.$is_weight.'
		where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd
	);
}
// \endcond

// \cond onlyria
/** récupère la valeur de prd_sell_weight, qui détermine le type de vente du produit (à l'unité de mesure (poids, volume, etc...) ou à une unité indivisible)
 *	@param int $prd Identifiant du produit
 *	@return bool True si l'article est vendu au poids ou au volume (ou toute autre unité de mesure), False sinon
 */
function prd_products_get_is_selling_weight( $prd ){
	global $config;

	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	$res = ria_mysql_query('
		select prd_sell_weight
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$prd.'
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['prd_sell_weight'];
}
// \endcond

// \cond onlyria
/** Cette fonction récupère le poids net d'un produit
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@return int Le poids net de l'article, en grammes
 *	@return bool False si le paramètre est omis ou bien si le produit n'existe pas
 */
function prd_products_get_weight_net( $prd ){
	if( !is_numeric($prd) || $prd<=0 ) return false;
	global $config;

	$res = ria_mysql_query( 'select prd_weight_net as weight_net from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd );

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'weight_net' );
}
// \endcond

// \cond onlyria
/** Cette fonction récupère le poids brut d'un produit
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@return int Le poids brut de l'article, en grammes
 *	@return bool False si le paramètre est omis ou bien si le produit n'existe pas
 */
function prd_products_get_weight_brut( $prd ){
	if( !is_numeric($prd) || $prd<=0 ) return false;
	global $config;

	$res = ria_mysql_query( 'select prd_weight as weight from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd );

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'weight' );
}
// \endcond

/** Cette fonction permet de récupérer le poids d'un produit.
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param string $weight Optionnel, par défaut le poids brut est retourné, mettre "net" pour avoir le poids net
 *	@return int Le poids du produit en grammes selon les paramètres donnés
 */
function prd_products_get_weight( $prd_id, $weight='brut' ){
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return 0;
	}

	if( !in_array($weight, array('brut', 'net')) ){
		$weight = 'net';
	}

	global $config;

	$sub_sql = 'if( ifnull(prd_weight, 0) = 0, ifnull(prd_weight_net, 0), prd_weight )';
	if( $weight == 'net' ){
		$sub_sql = 'if( ifnull(prd_weight_net, 0) = 0, ifnull(prd_weight, 0), prd_weight_net )';
	}

	$res = ria_mysql_query('
		select '.$sub_sql.' as weight
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
		and prd_id = '.$prd_id.'
		');

	if( !$res || !ria_mysql_num_rows($res) ){
		return 0;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['weight'];
}

// \cond onlyria
/**	Cette fonction permet la mise à jour du nombre de ventes réalisées pour un article
 *	@param int $tnt Facultatif, identifiant d'un locataire particulier
 *	@param int $prd Facultatif, identifiant du produit pour lequel effectuer le calcul. Si omis, tous les produits seront recalculés.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_products_refresh_selled( $tnt=0, $prd=0 ){

	if( !is_numeric($tnt) || $tnt<0 ) return false;
	if( !is_numeric($prd) || $prd<0 ) return false;

	$limit = 0;
	if( $tnt>0 ){

		// requête sur les variabless de config "en dur"
		$rconf = ria_mysql_query("
			select ovr_value from cfg_overrides
			where ovr_tnt_id=".$tnt." and ovr_var_code='compute_selled_limit_days'
			");
		if( $rconf && ria_mysql_num_rows($rconf) ){
			$limit = ria_mysql_result( $rconf, 0, 0 );
		}
	}

	$sql = '
		update prd_products as prd
		join (
			select count(prd_id) as cpt, prd_id
			from ord_orders
			join ord_products as ord on ( ord_tnt_id=ord.prd_tnt_id and ord_id=prd_ord_id )
			where 1 ';

	if( $tnt>0 ){
		$sql .= ' and ord.prd_tnt_id='.$tnt;
	}

	$sql .= '
			and ord_state_id in (3, 4, 5, 6, 7, 8, 11, 24, 25, '._STATE_INV_STORE.')
			'.( $limit>0 ? 'and datediff(now(),ord_date)<'.$limit : '' ).'
		) as stats on stats.prd_id = prd.prd_id
		set prd_selled=stats.cpt
		where prd.prd_date_deleted is null
	';

	if( $tnt>0 ){
		$sql .= ' and prd.prd_tnt_id='.$tnt;
	}
	if( $prd>0 ){
		$sql .= ' and prd.prd_id='.$prd;
	}
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du nombre de ventes web réalisées pour un article
 *	@param int $tnt Facultatif, identifiant d'un locataire particulier
 *	@param int $prd Facultatif, identifiant du produit pour lequel effectuer le calcul. Si omis, tous les produits seront recalculés.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_products_refresh_selled_web( $tnt=0, $prd=0 ){

	if( !is_numeric($tnt) || $tnt<0 ) return false;
	if( !is_numeric($prd) || $prd<0 ) return false;

	$limit = 0;
	if( $tnt>0 ){

		// requête sur les variabless de config "en dur"
		$rconf = ria_mysql_query("
			select ovr_value from cfg_overrides
			where ovr_tnt_id=".$tnt." and ovr_var_code='compute_selled_limit_days'
			");
		if( $rconf && ria_mysql_num_rows($rconf) ){
			$limit = ria_mysql_result( $rconf, 0, 0 );
		}
	}

	$sql = '
		update prd_products as prd
		join (
			select count(prd_id) as cpt, prd_id
			from ord_orders
			join ord_products as ord on ( ord_tnt_id=ord.prd_tnt_id and ord_id=prd_ord_id )
			where 1 ';

	if( $tnt>0 ){
		$sql .= ' and ord.prd_tnt_id='.$tnt;
	}

	$sql .= '
			and ord_pay_id is not null
			and ord_state_id in (3, 4, 5, 6, 7, 8, 11, 24, 25, '._STATE_INV_STORE.')
			'.( $limit>0 ? 'and datediff(now(),ord_date)<'.$limit : '' ).'
		) as stats on stats.prd_id = prd.prd_id
		set prd_selled_web=stats.cpt
		where prd.prd_date_deleted is null
	';

	if( $tnt>0 )
		$sql .= ' and prd.prd_tnt_id='.$tnt;

	if( $prd>0 )
		$sql .= ' and prd.prd_id='.$prd;

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du nombre de consultations réalisées pour un article
 *	@param int $tnt_id Facultatif, identifiant du locataire pour lequel réaliser l'opération
 *	@param int $prd Facultatif, identifiant du produit pour lequel réaliser l'opération, false pour tous les produits
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_products_refresh_hits($tnt_id=0, $prd=false){
	global $config;

	$prd_id = 'prd.prd_id';

	if( is_numeric($prd) && $prd > 0 ){
		$tnt_id = $config['tnt_id'];

		if( !prd_products_exists($prd) ){
			return false;
		}
		$prd_id = $prd;

	}

	if( $tnt_id===0 ){
		// Tous les tenants
		$rtnt = tnt_tenants_get();
		if( !$rtnt ){
			return false;
		}
		while( $tnt = ria_mysql_fetch_assoc($rtnt) ){
			if( !prd_products_refresh_hits($tnt['id']) ){
				return false;
			}
		}
		return true;

	}elseif( !is_numeric($tnt_id) || $tnt_id<0 ){
		return false;

	}

	$limit = 90; //Fixe la limite du calcul à 90 jours par défaut.

	// requête sur les variables de config "en dur"
	$rconf = ria_mysql_query("
		select ovr_value from cfg_overrides
		where ovr_tnt_id=".$tnt_id." and ovr_var_code='compute_selled_limit_days'
		");
	if( $rconf && ria_mysql_num_rows($rconf) ){
		$limit = ria_mysql_result( $rconf, 0, 0 );
	}

	return ria_mysql_query('
		update prd_products as prd
		set prd_hits = (
			select SUM(stat_prd_hits) as hits
			from stats_prd_hourly
			where stat_prd_id = '.$prd_id.'
			and stat_tnt_id = prd.prd_tnt_id
			'.( $limit>0 ? ' and datediff(now(), stat_datetime)<'.$limit : '' ).'
		)
		where
			prd.prd_tnt_id = ' . $tnt_id . '
			and prd.prd_date_deleted is null
			and prd.prd_id = ' . $prd_id . '
	');

}
// \endcond

// \cond onlyria
/**	Permet le chargement d'un produit par son url. Les champs retournés sont identiques à la fonction
 *	\c prd_products_get. Par défaut, seul les produits publiés sont retournées par cette fonction.
 *	@param string $url Url "rewritée" d'un produit
 *	@param bool $published  Booléen indiquant si seul les produits publiés sont recherchés (true, valeur par défaut), ou bien si tout les produits sont recherchés (false).
 *	@return resource un résultat de requête MySQL comprenant les mêmes colonnes que le résultat de la fonction \c prd_products_get.
 */
function prd_products_get_byurl( $url, $published=true ){
	global $config;

	$sub_sql = '';
	if( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true ){
		$sub_sql = ' and ((prd_is_sync and prd_publish) or not prd_is_sync ) ';
	}

	$rprd = ria_mysql_query('select cly_prd_id as id, cly_cat_id as cat_id from prd_classify, prd_products where cly_tnt_id='.$config['tnt_id'].' and prd_tnt_id=cly_tnt_id and cly_prd_id=prd_id and cly_url_alias="'.addslashes($url).'"' . ( $published ? ' and prd_publish and prd_publish_cat' : $sub_sql ) );
	if( !ria_mysql_num_rows($rprd) ) return false;
	$prd = ria_mysql_fetch_array($rprd);

	return prd_products_get( $prd['id'],'',0,$published,$prd['cat_id'] );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour de la description longue d'un produit.
 *	C'est une solution temporaire en attendant que le paramètre desc_long ne soit ajouté aux fonctions prd_products_add et prd_products_update.
 *	@param int $prd Identifiant du produit
 *	@param string $desc Description longue du produit
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 *	@todo Remplacer cette fonction par un nouveau paramètre à prd_products_add et prd_products_update
 *
 */
function prd_products_update_desc_long( $prd, $desc ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	global $config;

	// Récupère l'ancienne description
	$subject = prd_products_get_desc_long( $prd ).' '.$desc;

	$res = ria_mysql_query('update prd_products set prd_desc_long=\''.addslashes(sanitize($desc)).'\' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);

	if( $res ){
		prd_products_update_completion($prd);
		img_images_update_from_riawysiwyg( $subject );
	}

	return $res;
}
// \endcond

/** Teste l'existance d'un identifiant produit dans la base de données.
 *	Attention, car cette fonction retournera false pour les produits supprimés virtuellement.
 *
 *	@param int $id Identifiant à tester.
 *	@param bool $published Optionnel, restreint la recherche aux produits publiés.
 *	@param bool $deleted Optionnel, permet la recherche dans les produits supprimés
 *	@param string $lng Optionnel, code ISO d'une langue pour lequel le produit doit être publié (uniquement si $published est vrai)
 *
 *	@return bool true si le produit existe, false dans le cas contraire.
 *
 */
function prd_products_exists( $id, $published=false, $deleted=false, $lng=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	if( $lng!==false && !in_array( strtolower($lng), $config['i18n_lng_used'] ) ) return false;
	$lng = ( $lng===false ? i18n::getLang() : strtolower($lng) );

	$sql = 'select prd_id from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id;
	if( $published ){
		if( (isset($config['catalog_publish_lng']) && !$config['catalog_publish_lng']) || $lng==$config['i18n_lng'] ){
			$sql .= ' and prd_publish and prd_publish_cat';
		}else{
			$sql .= ' and exists ( select 1 from fld_object_values where pv_tnt_id='.$config['tnt_id'].' and pv_fld_id='._FLD_PRD_PUBLISH.' and pv_obj_id_0=prd_id and pv_value in (\'Oui\', \'oui\', \'1\') and pv_lng_code=\''.addslashes($lng).'\' )';
		}
	}
	if( !$deleted ) $sql .= ' and prd_date_deleted is null';
	if( $config['use_catalog_restrictions'] ){
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd_id' ).')';
	}

	$r = ria_mysql_num_rows(ria_mysql_query($sql))>0;

	return $r;
}

// \cond onlyria
/**	Cette fonction est chargée de mettre à jour le champ "Taux de remplissage" d'un produit donné.
 *	Ce pourcentage indique le nombre de champs remplis / sur le nombre de champs disponibles pour le produit.
 *	L'absence de modèle de saisie est comptée comme un champ manquant.
 *
 *	Pour ne pas être trop négatif, les champs obligatoires (ref, désignation) sont pris en compte dans le calcul.
 *
 *	@param int $prd Identifiant du produit à actualiser.
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function prd_products_update_completion( $prd ){
	global $config;

	if( !is_numeric($prd) ) return false;

	$count_fields = $count_missing = 0;

	$count_fields += 2; // Tient compte de la référence et de la désignation qui sont systématiquement renseignés.

	// Sélectionne les champs classiques entrant dans le calcul
	$static_fields = ria_mysql_query('
		select	prd_desc, prd_img_id, prd_brd_id, prd_desc_long,
		prd_weight, prd_height, prd_width, prd_length
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd.'
		');
	if( !ria_mysql_num_rows($static_fields) ) return false; // Le produit n'existe pas
	$static = ria_mysql_fetch_array($static_fields);
	if( !trim($static['prd_desc']) ) $count_missing++;
	if( !trim($static['prd_desc_long']) ) $count_missing++;
	if( !trim($static['prd_img_id']) ) $count_missing++;
	if( !trim($static['prd_brd_id']) ) $count_missing++;
	/*if( !trim($static['prd_weight']) ) $count_missing++;
	if( !trim($static['prd_height']) ) $count_missing++;
	if( !trim($static['prd_width']) ) $count_missing++;
	if( !trim($static['prd_length']) ) $count_missing++;*/
	$count_fields += 3; // Champs testés si dessus


	if( $config['tnt_id']==1 ){
		// test les articles liés
		$childs = prd_products_get($prd,'',0,false,0,0,-1,false,false,false,false,false,true);
		if(!ria_mysql_num_rows($childs))
			$count_missing++;

		// test les images secondaires
		$imgs = prd_images_get($prd);
		if(!ria_mysql_num_rows($imgs)){
			$count_missing++;
		}
	}

	// Sélectionne les champs dynamiques entrant dans le calcul
	$dyn_fields = fld_fields_get(0,0,0,0,0,$prd,null,array(),false,array(),null,CLS_PRODUCT);
	if( $dyn_fields!==false && ria_mysql_num_rows($dyn_fields) ){
		while( $f = ria_mysql_fetch_array($dyn_fields) ){
			if( $config['tnt_id']!=1 || $f['id']!=279 ){
				$count_fields++;
				if( !trim($f['obj_value']) )
					$count_missing++;
			}
		}
	}

	return ria_mysql_query('
		update prd_products set
		prd_completion='.( ($count_fields - $count_missing) / $count_fields * 100 ).'
		where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd.'
		');

}
// \endcond

// \cond onlyria
/**	Cette fonction est chargée de recalculer le taux de remplissage de la totalité des produits
 *	contenus dans la base de données.
 */
function prd_products_update_completions(){
	global $config;

	$products = ria_mysql_query('select prd_id from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null');
	while( $p = ria_mysql_fetch_array($products) ){
		prd_products_update_completion( $p['prd_id'] );
	}

}
// \endcond

/**	Cette fonction est similaire à la fonction prd_products_exists. La seule différence est que celle-ci
 *	vérifie la référence d'un produit au lieu de tester son identifiant.
 *	@param string $ref Référence du produit à tester
 *	@param bool $publish Booléen indiquant si la recherche porte sur les produits publiés ou non
 *	@return int l'identifiant du produit en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_products_exists_ref( $ref, $publish=true ){
	global $config;

	$ref = trim(strtoupper2($ref));

	$sql = 'select prd_id from prd_products where prd_tnt_id='.$config['tnt_id'].' and lower(prd_ref)=\''.addslashes(strtolower($ref)).'\' and prd_date_deleted is null';
	if( $publish ){
		$sql .= ' and prd_publish and prd_publish_cat';
	}
	$prd = ria_mysql_query($sql);
	if( !$prd || !ria_mysql_num_rows($prd) ){
		return false;
	}else{
		return ria_mysql_result( $prd, 0, 0 );
	}
}

/**	Retourne la première url publique disponible pour un produit donné
 *	@param int $id Obligatoire, Identifiant du produit
 *	@param bool $published  Optionnel, booléen permettant de ne retourner que les urls des produits publiés. Valeur par défaut : false.
 *	@param int $cat Optionnel, identifiant d'une catégorie. Valeur par défaut : 0.
 *	@param bool $is_canonical Optionnel, force la récupération d'une URL canonique. Valeur par défaut : false.
 *	@return string L'url alias pour le produit demandé, ou l'url alias de son parent s'il s'agit d'un article lié uniquement
 */
function prd_products_get_url( $id, $published=false, $cat=0, $is_canonical=false ){
	global $config, $memcached;

	if( $cat!=0 && !is_numeric($cat) ) return false;
	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	$key_memcached = 'tnt:'.$config['tnt_id'].':prd_products_get_url:id:'.$id.':published:'.$published.':cat:'.$cat.':is_canonical:'.$is_canonical.':lang:'.i18n::getLang();
	$kid = $memcached->get( $key_memcached );

	if( ria_is_memcached_result_ok($memcached) && $kid !== false){
		return $kid;
	}

	// Si la langue n'est pas celle par défaut, on tente de prendre une url traduite d'abord
	if( i18n::getLang()!=$config['i18n_lng'] ){
		$sql = '
		select pv_value
		from fld_object_values
		join prd_classify on ( cly_tnt_id=pv_tnt_id and cly_cat_id=pv_obj_id_0 and cly_prd_id=pv_obj_id_1 )
		';

		if( $cat>0 )
			$sql .= ' join prd_cat_hierarchy as hry on ( hry.cat_tnt_id=cly_tnt_id and cly_cat_id=cat_child_id and cat_parent_id='.$cat.' )';

		$sql.= '
		where pv_tnt_id='.$config['tnt_id'].'
		and pv_obj_id_1='.$id.'
		and pv_lng_code=\''.strtolower(i18n::getLang()).'\'
		and pv_fld_id = '._FLD_PRD_URL.'
		';

		if ($is_canonical) {
			$sql .= ' and cly_url_is_canonical = 1';
		}

		if( $published ){
			$sql .= ' and not exists (
			select cat_id
			from prd_categories
			where cat_tnt_id='.$config['tnt_id'].'
			and cat_id=cly_cat_id
			and (not cat_publish or cat_date_deleted is not null)

			) and not exists (

			select cat_id
			from prd_cat_hierarchy as hry, prd_categories as cat
			where hry.cat_tnt_id='.$config['tnt_id'].'
			and cat.cat_tnt_id=hry.cat_tnt_id
			and hry.cat_parent_id=cat_id
			and hry.cat_child_id=cly_cat_id
			and not cat_publish
			)';
		}

		if( $config['use_catalog_restrictions'] ){
			$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'cly_cat_id' ).')';
		}

		$sql .= '
		order by IFNULL(cly_url_is_canonical,0) desc
		limit 0,1
		';

		$r = ria_mysql_query($sql);
		if( ria_mysql_errno() ){
			error_log( $sql.' - '.mysql_error() );
		}

		if( $r && ria_mysql_num_rows($r)>0 ){
			return ria_mysql_result($r,0,0);
		}
	}

	$sql = '
		select
			if(ifnull(cly_url_perso, \'\')=\'\', cly_url_alias, cly_url_perso) as cly_url_alias
		from
			prd_classify
	';

	if( $cat>0 ){
		$sql .= '
			join
				prd_cat_hierarchy as hry2
			on
					hry2.cat_tnt_id=cly_tnt_id
				and cly_cat_id=hry2.cat_child_id
				and hry2.cat_parent_id='.$cat
		;
	}
	$sql .= '
		where
				cly_tnt_id='.$config['tnt_id'].'
			and cly_prd_id='.$id.'
			and (cly_url_alias != "" or cly_url_perso is not null)
	';

	if ($is_canonical) {
		$sql .= ' and cly_url_is_canonical = 1';
	}

	if( $config['use_catalog_restrictions'] ){
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_CATEGORY, 'cly_cat_id' ).')';
	}

	if( $published ){
		$sql .= ' and not exists (
		select cat_id from prd_categories where cat_tnt_id='.$config['tnt_id'].' and cat_id=cly_cat_id and (not cat_publish or cat_date_deleted is not null)
		) and not exists (
		select cat_id from prd_cat_hierarchy as hry, prd_categories as cat
		where hry.cat_tnt_id='.$config['tnt_id'].' and cat.cat_tnt_id='.$config['tnt_id'].' and hry.cat_parent_id=cat_id and hry.cat_child_id=cly_cat_id
		and not cat_publish
		)';
	}
	$sql .= 'order by IFNULL(cly_url_is_canonical,0) desc limit 0,1';

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log( 'prd_products_get_url( id='.$id.', published='.$published.', '.$cat.' )' );
		error_log( 'prd_products_get_url : '.$sql.' - '.mysql_error() );
	}

	if( $r && ria_mysql_num_rows($r)>0 ){
		$url = ria_mysql_result($r,0,0);
		$memcached->set( $key_memcached, $url, 60*60 );
		return $url;
	}
	return false;
}

// \cond onlyria
/** Cette fonction permet de mettre à jour la date de modification (date d'excution de cette fonction) d'un produit.
 *	@param int $prd Obligatoire, identifiant ou tableau d'identifiants d'un produit
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function prd_products_set_date_modified( $prd ){
	global $config;

	$prd = control_array_integer( $prd );
	if( $prd === false ){
		return false;
	}

	return ria_mysql_query('
		update prd_products
		set prd_date_modified = now()
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id in ('.implode(', ' , $prd).')
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification du champ orderable,
 *	qui détermine si le produit est commandable ou non.
 *	@param int $prd Identifiant du produit à modifier
 *	@param bool $orderable booléen indiquant si la commande du produit est possible
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_products_set_orderable( $prd, $orderable ){
	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	global $config;

	if( !ria_mysql_query('update prd_products set prd_orderable='.($orderable ? 1 : 0).' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd) ){
		return false;
	}

	prd_search_results_system($prd);

	try{
		// Index le produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $prd,
			));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	return true;
}
// \endcond
/**
 * Cette fonction permet de recupérer l'information de commandable en fonction d'un dépot et si le produit et en sommeil
 * @param int $prd_id Identifiant du porduit
 * @param int $dps_id Identifiant du dépôt
 *
 * @return string|bool Retourne 1 si commandable, false ou 0 si non
 */
function prd_products_is_orderable( $prd_id, $dps_id ){
	global $config;

	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}
	if( !is_numeric($dps_id) || $dps_id <= 0 ){
		return false;
	}

	$r = ria_mysql_query('
		select (prd_orderable and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock($prd_id, $dps_id, true).')>0)) as orderable
		from prd_products
			left join prd_stocks on sto_tnt_id=prd_tnt_id and sto_prd_id=prd_id
		where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prd_id.' and prd_date_deleted is null and sto_dps_id='.$dps_id.'
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);
}

// \cond onlyria
/**	Cette fonction récupère l'information "est commandable oui / non" d'un produit.
 *	C'est une condition -théoriquement- nécessaire, mais pas suffisante, pour qu'un produit soit commandable sur la boutique.
 *	@param int $prd Identifiant du produit à tester
 *	@return bool True si le produit est marqué comme étant commandable (et qu'il n'est pas supprimé), False sinon
 */
function prd_products_get_orderable( $prd ){
	global $config;

	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	$r = ria_mysql_query('
		select prd_orderable
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prd.' and prd_date_deleted is null
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification du champ follow_stock,
 *	qui détermine si le produit est suivi en stock ou non.
 *	@param int $prd Identifiant du produit à modifier
 *	@param bool $follow_stock booléen indiquant si le produit est suivi ou non en stock
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_products_set_follow_stock( $prd, $follow_stock ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;

	return ria_mysql_query('
		update prd_products set prd_follow_stock='.( $follow_stock ? 1 : 0 ).'
		where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd.'
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information de reconstruction des urls d'un produit.
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param bool $rebuild Optionnel, par défaut à False, mettre True pour demander la reconstruction des urls
 *	@return bool True en cas succès de la mise à jour, False dans le cas contraire
 */
function prd_products_set_url_rebuild( $prd_id, $rebuild=false ){
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	global $config;

	return ria_mysql_query('
		update prd_products
		set prd_url_rebuild='.( $rebuild ? '1' : '0' ).'
		where prd_tnt_id='.$config['tnt_id'].'
		and prd_id='.$prd_id.'
		');

}
// \endcond

/**	Cette fonction récupère l'information follow_stock, qui détermine si un produit est suivi en stock ou non
 *	@param int $id Identifiant du produit
 *	@return bool True si le produit est suivi en stock, False sinon
 */
function prd_products_get_follow_stock( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		select prd_follow_stock from prd_products
		where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id.' and prd_date_deleted is null
	');

	if( !$r || !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result($r, 0, 0);
}

// \cond onlyria
/**	Cette fonction permet la modification du champ countermark,
 *	qui détermine si le produit est vendu en contre-marque ou non.
 *	@param int $prd Identifiant du produit à modifier
 *	@param bool $countermark booléen indiquant si le produit est en contre-marque
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_products_set_countermark( $prd, $countermark ){
	global $config;

	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	if( !ria_mysql_query('update prd_products set prd_countermark='.( $countermark ? 1 : 0 ).' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd) )
		return false;

	return prd_search_results_system( $prd );
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si un produit est ou non en contremarque
 *	@param int $id Identifiant du produit
 *	@return bool False en cas d'échec ou si le produit n'existe pas
 *	@return bool Produit en contremarque oui / non
 */
function prd_products_get_countermark( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	$r = ria_mysql_query('
		select prd_countermark from prd_products
		where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id.'
			and prd_date_deleted is null
	');

	if( !$r || !ria_mysql_num_rows( $r ) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification du champ new,
 *	qui détermine si le produit est nouveau ou non.
 *	@param int $prd Identifiant du produit à modifier
 *	@param $new tristate indiquant si le produit est à afficher comme nouveau (-1 jamais, 0 auto, 1 toujours)
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_products_set_new( $prd, $new ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	global $config;

	if( $new!='-1' && $new!='0' && $new!='1' ) return false;
	return ria_mysql_query('update prd_products set prd_new=\''.$new.'\' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification du champ sleep,
 *	qui détermine si le produit est en sommeil ou non.
 *	@param int $prd Identifiant du produit à modifier
 *	@param bool $sleep booléen indiquant si le produit est mis en sommeil
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_products_set_sleep( $prd, $sleep ){
	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}
	if (!prd_products_exists($prd)) {
		return $sleep;
	}

	global $config;

	$sql = '
	update prd_products
	set prd_sleep = '.( $sleep ? 1 : 0 ).'
	where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prd.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$ar_cats = array();
	$categories = prd_products_categories_get( $prd );
	while( $r = ria_mysql_fetch_array($categories) ){
		prd_categories_refresh_products_published( $r['cat'] );
		$ar_cats[] = $r['cat'];
	}

	prd_products_refresh_childonly_publish_cat( $prd, $ar_cats );

	try{
		// Index le produit dans le moteur de recherche.
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $prd,
			));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

	prd_search_results_system( $prd );

	if( !$sleep ){
		prd_products_rebuild_urls( $prd );
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement du champ sleep, qui détermine si le produit est en sommeil ou non.
 *	@param int $prd Identifiant du produit à interroger
 *	@return bool true si le produit est en sommeil
 *	@return bool false dans le cas contraire
 */
function prd_products_get_sleep( $prd ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;
	$rsleep = ria_mysql_query('select prd_sleep from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);
	if( !ria_mysql_num_rows($rsleep) ) return false;
	return ria_mysql_result( $rsleep, 0, 0 );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet du récupérer le nombre de fois qu'un article a été vendu
 *	@param int $prd Identifiant du produit à interroger
 *	@return int Le nombre de ventes de l'article passé en paramètre
 */
function prd_products_get_selled( $prd ){
	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	global $config;

	$rselled = ria_mysql_query('
		select prd_selled
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
		and prd_id='.$prd.'
	');

	if( !$rselled || !ria_mysql_num_rows($rselled) ){
		return false;
	}

	$selled = ria_mysql_fetch_assoc( $rselled );
	return $selled['prd_selled'];
}
// \endcond

// \cond onlyria
/** Détermine si le produit peut apparaître dans les listes (false), ou s'il est limité
 *	à la liste des articles enfants (true).
 *	@param int $prd_id Identifiant du produit à modifier
 *	@param bool $childonly booléen indiquant si le produit est limité aux listes de produits liés
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_products_set_childonly( $prd_id, $childonly ){

	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
	update prd_products
	set prd_childonly = '.( $childonly ? 1 : 0 ).'
	where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prd_id.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$ar_cats = array();
	$categories = prd_products_categories_get( $prd_id );
	while( $r = ria_mysql_fetch_assoc($categories) ){
		prd_categories_refresh_products_published( $r['cat'] );
		$ar_cats[] = $r['cat'];
	}

	if( !$childonly ){
		prd_products_refresh_childonly_publish_cat( $prd_id, $ar_cats );
	}

	return true;

}
// \endcond

// \cond onlyria
/** Cette fonction permet le chargement du champ childonly, qui détermine si le produit peut apparaître dans les listes (false), ou s'il est limité
 *	à la liste des articles enfants (true).
 *	@param int $prd Identifiant du produit à modifier
 *	@return bool la valeur de la propriété "childonly" du produit, ou false en cas d'échec
 */
function prd_products_get_childonly( $prd ){
	global $config;

	if( !is_numeric($prd) ) return false;
	$rc = ria_mysql_query('select prd_childonly from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);
	if( ria_mysql_num_rows($rc) ){
		return ria_mysql_result($rc,0,0);
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/** Cette fonction est un alias de prd_products_get() déterminant le nombre de lignes retournées par cette dernière
 *
 *	@param int $id Optionnel, identifiant d'un produit ou tableau de produits
 *	@param int|array $brand Optionnel, identifiant d'une marque ou tableau d'identifiants de marques. Pour compter les produits sans marque, indiquer -1.
 *	@param bool $published  Optionnel, publication oui/indifférent (-1 pour "non" explicite)
 *	@param int $cat Optionnel, identifiant de catégorie
 *	@param bool $centralized Optionnel, produits centralisés oui/indifférent
 *	@param bool $new Optionnel, produits nouveaux oui/indifférent
 *	@param bool $destockage Optionnel, produits en déstockage oui/indifférent
 *	@param bool $promotions Optionnel, produits en promotion oui/indifférent
 *	@param bool $catchilds Optionnel, inclusion des produits des sous-catégories oui/non
 *	@param bool $orderable Optionnel, produits commandables oui/indifférent
 *	@param int|array $fld Optionnel, identifiant ou tableau de champs avancés
 *	@param $fld_or Optionnel, séparateur entre les valeurs de restrictions du champ avancé ($fld)
 *
 *	@return bool False en cas d'échec
 *	@return int Le nombre de produits correspondant aux critères
 *	@deprecated Utiliser directement ria_mysql_num_rows( prd_products_get_simple() )
 */
function prd_products_get_count( $id=0, $brand=0, $published=false, $cat=0, $centralized=false, $new=false, $destockage=false, $promotions=false, $catchilds=false, $orderable=null, $fld=false, $fld_or=false ){
	global $config;

	$params = array( 'brand'=>$brand, 'centralized'=>$centralized, 'new'=>$new, 'destockage'=>$destockage, 'promotions'=>$promotions, 'orderable'=>$orderable, 'fld_or'=>$fld_or );

	if( defined('CONTEXT_IS_ADMIN') ){
		$params['childs'] = isset($_SESSION['usr_admin_show_childs']) ? $_SESSION['usr_admin_show_childs'] : true;
		$params['hide_sleeping'] = !isset($_SESSION['usr_admin_hide_sleeping']) ? $config['admin_hide_sleeping'] : $_SESSION['usr_admin_hide_sleeping'];
	}

	$rprd = prd_products_get_simple( $id, '', $published, $cat, $catchilds, $fld, false, false, $params );

	if( !$rprd ){
		return false;
	}

	return ria_mysql_num_rows($rprd);
}
// \endcond

// \cond onlyria
/** Permet la supression d'un produit. Aucune suppression physique n'a réellement lieu.
 *
 *	@param int $id Identifiant du produit à supprimer.
 *	@param bool $is_sync Par défaut à false, mettre true pour forcer la suppression de contenus synchronisés
 *	@return bool true si la restauration à réussi, false dans le cas contraire
 *
 */
function prd_products_del( $id, $is_sync=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select prd_is_sync, prd_brd_id as brd_id
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.addslashes( $id ).'
			and prd_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return true;
	}

	$p = ria_mysql_fetch_assoc( $res );

	// Si le produit est synchronisé et qu'on ne supprime pas en mode synchronisé on retourne false
	if( $p['prd_is_sync'] == 1 && !$is_sync ){
		return false;
	}

	$res = ria_mysql_query('
		update prd_products
		set prd_publish=0, prd_date_deleted=now()
		where prd_tnt_id='.$config['tnt_id'].'
		and prd_id='.$id.'
	');

	if( $res ){

		$categories = prd_products_categories_get($id);
		while( $r = ria_mysql_fetch_array($categories) ){
			prd_products_del_from_cat( $id, $r['cat'], $is_sync );
		}

		if( $p['brd_id'] ){
			prd_brands_update_products( $p['brd_id'] );
		}

		// supprime les tarifs et taxes liés
		prc_prices_del( 0, 0, $id );
		prc_tvas_del( 0, $id );
	}
	return true;
}
// \endcond

// \cond onlyria
/**	Permet la restauration d'un produit préalablement supprimé. Lors de sa restauration, le produit
 *	sera marqué comme non-publié. Il faudra le publier de nouveau pour qu'il apparaisse dans la boutique.
 *
 *	@param int $id Identifiant du produit à restaurer
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *
 */
function prd_products_restore( $id ){
	global $config;

	if( ria_mysql_query('update prd_products set prd_date_deleted=null, prd_publish=0, prd_date_published=null, prd_first_published=null where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id) ){
		try{
			// Indexe le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		return true;
	}

	return false;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de reconstruire l'url d'un produit pour chacun de ses classements (attention, cette fonction ne fera pas de redirection 301 avec l'ancienne).
 *	@param int $prd_id Obligatoire, ientifiant d'un produit
 *	@param int|array $cat_id Optionnel, identifiant ou tableau d'identifiants permettant de limité la reconstruction à ces catégories
 *	@return bool True si les urls ont bien été recréées, False dans le cas contraire
 *	@deprecated Cette fonction n'a plus d'action réelle, les select sont actifs mais pas les update (ils déclenchent un warning par email)
 */
function prd_products_rebuild_urls( $prd_id, $cat_id=0 ){
	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	if( !is_array($cat_id) ){
		if( !is_numeric($cat_id) || $cat_id<0 ){
			return false;
		}

		$cat_id = $cat_id>0 ? array($cat_id) : array();
	}else{
		foreach( $cat_id as $c ){
			if( !is_numeric($c) || $c<=0 ){
				return false;
			}
		}
	}

	global $config;

	// Récupère les informations suivantes sur le produit : prd_publish, prd_publish_cat, prd_sleep
	$rp_info = ria_mysql_query('
		select prd_publish, prd_publish_cat, prd_sleep
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
		and prd_id='.$prd_id.'
		and prd_date_deleted is null
		');

	if( !$rp_info || !ria_mysql_num_rows($rp_info) ){
		return false;
	}

	$p_info = ria_mysql_fetch_array( $rp_info );

	// Reconstruction des urls seulement si le produit est activé sur le site
	if( $p_info['prd_publish'] && $p_info['prd_publish_cat'] && !$p_info['prd_sleep'] ){
		$rcly = prd_classify_get( false, $prd_id, (sizeof($cat_id) ? $cat_id : 0), 0, false, true );
		if( $rcly ){
			while( $cly = ria_mysql_fetch_array($rcly) ){
				// Contrôle que l'url du classement est redirigée vers une autre url
				$sql = '
					select 1
					from rew_rewritemap
					where url_tnt_id = '.$config['tnt_id'].'
					and (
						url_extern = "'.addslashes( $cly['url'] ).'"
						or
						url_extern = "'.addslashes( rew_strip($cly['url']) ).'"
					)
					and url_code = 301
					and url_lng_code = "'.addslashes( $config['i18n_lng'] ).'"
				';

				$rewrite = ria_mysql_query( $sql );

				if( $rewrite && ria_mysql_num_rows($rewrite) ){
					$rew = ria_mysql_fetch_assoc( $rewrite );

					// Reconstruction de l'url du produit
					$new_url = prd_products_url_alias_add( $cly['prd'], $cly['cat'] );

					if( !$new_url ){
						return false;
					}
				}
			}
		}

		return true;
	}

	return false;
}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'un produit à une catégorie. Si l'argument $prd est un tableau, tous les produits de ce tableau
 *	seront ajoutés à la catégorie $cat. Si $cat est elle même un tableau, le ou les produits de l'argument $prd seront
 *	ajoutées aux catégories de $cat.
 *
 *	@param int $prd Soit l'identifiant d'un produit, soit un tableau d'identifiants de produits
 *	@param int|array $cat Soit l'identifiant d'une catégorie, soit un tableau d'identifiants de catégories
 *	@param bool $is_sync Détermine si ce classement provient de la gestion commerciale, ou non
 * 	@param bool|array Optionnel, false pour un appel direct, un tableau des paramètres pour un appel via la workqueue
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_products_add_to_cat( $prd, $cat, $is_sync=false, $workqueue=false ){
	global $config;

	if( !is_array($prd) && !is_numeric($prd) ){
		return false;
	}

	if( !is_array($cat) && !is_numeric($cat) ){
		return false;
	}

 	// En interne, la fonction n'utilise que des tableaux
	 if( is_numeric($prd) ){
		$prd = array( $prd );
	}

	// En interne, la fonction n'utilise que des tableaux
	if( is_numeric($cat) ){
		$cat = array( $cat );
	}

	if( !$workqueue ){
		// Vérifie que tous les identifiants de produits correspondent à des produits référencés
		$res = ria_mysql_query('
			select 1
			from prd_products
			where prd_tnt_id = '.$config['tnt_id'].'
				and prd_date_deleted is null
				and prd_id in ('.implode(', ', $prd).')
		');

		if( !$res || count($prd) != ria_mysql_num_rows($res) ){
			return false;
		}

		// Vérifie que tous les identifiants de catégories correspondent à des catégories référencées
		$res = ria_mysql_query('
			select 1
			from prd_categories
			where cat_tnt_id = '.$config['tnt_id'].'
				and cat_date_deleted is null
				and cat_id in ('.implode(', ', $cat).')
		');

		if( !$res || count($cat) != ria_mysql_num_rows($res) ){
			return false;
		}

		// Ajout des classements de produits
		foreach( $prd as $p ){
			// récupère l'url canonique du produit
			$canonical = prd_classify_get_canonical_url( $p );

			foreach( $cat as $c ){
				$res = ria_mysql_query('
					replace into prd_classify
						( cly_tnt_id, cly_prd_id, cly_cat_id, cly_is_sync, cly_url_is_canonical, cly_date_created )
					values
						('.$config['tnt_id'].', '.$p.', '.$c.', '.( $is_sync ? 1 : 0 ).', '.( $canonical? 0 : 'null' ).', now() )
				');

				if( !$res ){
					error_log( __FILE__.':'.__LINE__.' Erreur lors de la création d\'un classement (tnt:'.$config['tnt_id'].',$p:'.$p.',$c:'.$c.',$is_sync:'.( $is_sync ? 1 : 0 ).',$canonical:'.( $canonical? 0 : 'null' ).' )');
					return false;
				}
			}
		}

		// Ajout au worker la gestion de toutes les autres actions
		$ar_data_workqueue = [
			'prd' => $prd,
			'cat' => $cat,
			'is_sync' => $is_sync,
			'workqueue' => true
		];

		RiaQueue::getInstance()->addJob( RiaQueue::WORKER_CLASSIFY_ADD, $ar_data_workqueue );
	}else{
		// Ajoute le(s) produit(s) aux catégorie(s) demandée(s)
		foreach( $prd as $p ){
			foreach( $cat as $c ){
				// Crée un alias pour les urls simplifiées
				prd_products_url_alias_add( $p, $c );

				// met à jour le nombre de produits publiés de la catégorie
				prd_categories_refresh_products_published( $c );

				// Met à jour la position de ce produit (en dernière position)
				$sort = prd_categories_sort_get( $c );
				if( is_array($sort) && isset($sort['type']) && $sort['type']==SORT_PERSO ){
					$rpos = ria_mysql_query( 'select max(cly_prd_pos)+1 as pos from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$c );
					if( $rpos && ria_mysql_num_rows($rpos) ){
						$pos = ria_mysql_result( $rpos, 0, 'pos' );
						ria_mysql_query( 'update prd_classify set cly_prd_pos='.$pos.' where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$c.' and cly_prd_id='.$p );
					}
				}

				try{
					// Index le classement dans le moteur de recherche.
					RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
						'cls_id' => CLS_CLASSIFY,
						'obj_id_0' => $p,
						'obj_id_1' => $c,
					));
				}catch(Exception $e){
					error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
				}
			}

			// Met à jour le champ prd_publish_cat indiquant si un produit peut apparaître par sa catégorie
			$cat_is_publish = prd_products_cat_published( $p ) ? 1 : 0;
			ria_mysql_query('update prd_products set prd_publish_cat='.$cat_is_publish.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$p);
			if( $cat_is_publish ){
				prd_products_rebuild_urls( $p );
			}

			prd_products_refresh_childonly_publish_cat( $p, $cat );

			// Si le produit est publié, on met à jour la date de modification de la catégorie
			$is_publish = ria_mysql_query('select prd_publish from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$p);
			if( $is_publish && ria_mysql_num_rows($is_publish) ){
				prd_categories_set_date_modified( $cat );
			}
		}

		// mise à jour de la date de modification du produit
		prd_products_set_date_modified($prd);
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de gérer les url simplifiées d'un produit
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *	@return bool Retourne true si l'enregistrement de l'url a fonctionné
 *	@return bool Retourne false dans le cas contraire
 */
function prd_products_url_alias_add( $prd, $cat ){
	if( !prd_products_exists($prd) ) return false;
	if( !prd_categories_exists($cat) ) return false;
	global $config;

	// Crée l'alias principal
	$alias = rew_rewritemap_generated( array($prd, $cat), CLS_PRODUCT );

	// Récupère les sites
	$rwst = wst_websites_get( 0, array('id'=>'asc'));
	if( !$rwst || !ria_mysql_num_rows($rwst) )
		return false;

	$url = null;

	// Crée les alias
	$added = false;
	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_PRODUCT);
		if( $prd_pages ){
			while( $page = ria_mysql_fetch_array($prd_pages) ){
				$url_intern = $page['url'].'?cat='.$cat.'&prd='.$prd;
				if( isset($config['url_no_category']) && $config['url_no_category'] ){
					$url_intern = $page['url'].'?cat=0&prd='.$prd;
				}

				if( !$added ){
					$url = rew_rewritemap_add_specify_class( CLS_PRODUCT, $alias.$page['key'], $url_intern, 200, $wst['id'], false, null, array($cat, $prd) );
				}else{
					rew_rewritemap_add_specify_class( CLS_PRODUCT, $url, $url_intern, 200, $wst['id'], false, null, array($cat, $prd) );
				}
				$added = true;
			}
		}
	}

	if( $url===null ){
		return false;
	}

	ria_mysql_query('update prd_classify set cly_url_alias="'.$url.'" where cly_tnt_id='.$config['tnt_id'].' and cly_cat_id='.$cat.' and cly_prd_id='.$prd);

	return $url;
}
// \endcond

// \cond onlyria
/**	Permet la suppression d'un produit d'une catégorie.
 * Si l'argument $prd est un tableau, tous les produits de ce tableau seront supprimés de la catégorie $cat.
 * Si $cat est elle même un tableau, le ou les produits de l'argument $prd seront supprimés des catégories de $cat.
 *
 * @param int $prd Soit l'identifiant d'un produit, soit un tableau d'identifiants de produits
 * @param int|array $cat Soit l'identifiant d'une catégorie, soit un tableau d'identifiants de catégories
 * @param bool $is_sync Détermine si ce classement provient de la gestion commerciale, ou non
 * @param bool $redirection Optionnel, par défaut une 301 vers l'url canonique (ou une url publiée sera mise en place), mettre False pour qu'elle ne le soit pas
 * @param bool|array $workqueue Optionnel, false pour un appel direct, un tableau des paramètres pour un appel via la workqueue
 *
 * @return bool true en cas de succès, false en cas d'échec
 */
function prd_products_del_from_cat( $prd, $cat, $is_sync=false, $redirection=true, $workqueue=false ){
	// Vérification des paramètres "$prd" et "$cat".
	if( (!is_array($prd) && !is_numeric($prd)) || (!is_array($cat) && !is_numeric($cat)) ){
		return false;
	}

	// Normalise les paramètres en tableau.
	if( is_numeric($prd) ){
		$prd = array($prd);
	}
	if( is_numeric($cat) ){
		$cat = array($cat);
	}

	// Vérifie qu'il s'agit bien d'identifiants de produits
	foreach( $prd as $p ){
		if( !is_numeric($p) || $p <= 0 ){
			return false;
		}
	}

	// Vérifie qu'il s'agit bien d'identifiants de catégories
	foreach( $cat as $c ){
		if( !is_numeric($c) || $c <= 0 ){
			return false;
		}
	}

	global $config;

	// Suppression du/des classements, les autres actions seront réalisées via la worqueue
	if( !$workqueue ){
		$ar_old_url = [];

		foreach( $prd as $p ){
			foreach( $cat as $c ){		// Récupère l'ancienne url de classement
				$res = ria_mysql_query('
					select cly_url_alias
					from prd_classify
					where cly_tnt_id = '.$config['tnt_id'].'
						and cly_prd_id = '.$p.'
						and cly_cat_id = '.$c.'
				');

				if( !$res ){
					return false;
				}elseif( ria_mysql_num_rows($res) ){
					$r = ria_mysql_fetch_assoc( $res );
					$ar_old_url[ $c.'-'.$p ] = $r['cly_url_alias'];
				}

				// Supprime le classement du produit
				if( $is_sync || !prd_products_categories_get_is_sync($p,$c) ){
					if( !ria_mysql_query('delete from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$p.' and cly_cat_id='.$c) ){
						return false;
					}
				}
			}
		}

		$ar_data_workqueue = [
			'prd' => $prd,
			'cat' => $cat,
			'is_sync' => $is_sync,
			'redirection' => $redirection,
			'workqueue' => [
				'old_url' => $ar_old_url,
			]
		];

		RiaQueue::getInstance()->addJob( RiaQueue::WORKER_CLASSIFY_DEL, $ar_data_workqueue );
	}else{
		// Va stocker la liste des catégories ayant besoin que leur date de dernière mise à jour soit actualisée
		$categories_modified = array();

		foreach( $prd as $p ){
			foreach( $cat as $c ){
				// La suppression "utilisateur" n'est autorisée que si le lien n'est pas synchronisé
				if( $is_sync || !prd_products_categories_get_is_sync($p,$c) ){
					$can_del_url = true;
					if( $redirection ){
						$canonical_obj = $publish_obj = array();
						$canonical_url = $publish_cly_url = $old_cly_url = '';

						if( isset($workqueue['old_url'][$c.'-'.$p]) ){
							$old_cly_url = $workqueue['old_url'][$c.'-'.$p];
						}

						// Récupère une autre url publiée utilisée par l'article ainsi que son url canonique
						$rcly = prd_classify_get( false, $p, 0, 0, false, true );
						if( $rcly ){
							while( $cly = ria_mysql_fetch_assoc($rcly) ){
								if( $cly['cat'] != $c ){
									if( $cly['is_canonical'] ){
										$canonical_obj = array( $cly['cat'], $p );
										$canonical_url = $cly['url'];
									}elseif( trim($publish_cly_url) == '' ){
										$publish_obj 	 = array( $cly['cat'], $p );
										$publish_cly_url = $cly['url'];
									}

									if( trim($publish_cly_url) != '' && trim($canonical_url) != '' ){
										break;
									}
								}
							}
						}

						$url_redirection = trim($canonical_url) != '' ? $canonical_url : $publish_cly_url;

						// Gestion du multilinque
						foreach( $config['i18n_lng_used'] as $one_lng ){
							$m_url = fld_object_values_get( array($c, $p), _FLD_PRD_URL, $one_lng, false, true );

							if( trim($m_url) != '' ){
								$m_canonical_url = $m_publish_cly_url = '';
								if( sizeof($canonical_obj) ){
									$m_canonical_url	= fld_object_values_get( $canonical_obj, _FLD_PRD_URL, $one_lng, false, true );
								}
								if( sizeof($publish_obj) ){
									$m_publish_cly_url	= fld_object_values_get( $publish_obj, _FLD_PRD_URL, $one_lng, false, true );
								}

								$m_url_redirection = trim($m_canonical_url) != '' ? $m_canonical_url : $m_publish_cly_url;

								if( trim($m_url_redirection) != '' && $m_url_redirection != $m_url ){
									$res = ria_mysql_query('
										update rew_rewritemap
											set url_intern="'.addslashes( $m_url_redirection ).'", url_code=301
										where url_tnt_id='.$config['tnt_id'].'
											and url_lng_code = "'.addslashes( $one_lng ).'"
											and ( url_extern="'.addslashes( $m_url ).'" or url_intern="'.addslashes( $m_url ).'" )
									');

									if( $res ){
										fld_object_values_set( array($c,$p), _FLD_PRD_URL, $m_url_redirection, $one_lng );
									}
								}
							}
						}

						// Remplace les urls supprimées par des redirections 301 vers les autres urls du produit
						if( trim($url_redirection)!='' && $url_redirection != $old_cly_url ){
							$res = ria_mysql_query('
								update rew_rewritemap
									set url_intern="'.addslashes( $url_redirection ).'", url_code=301
								where url_tnt_id='.$config['tnt_id'].'
									and ( url_extern="'.addslashes( $old_cly_url ).'" or url_intern="'.addslashes( $old_cly_url ).'" )
							');

							if( $res ){
								$can_del_url = false;
							}
						}
					}

					// Supprime l'entrée correspondante du couple produit/classement dans le moteur de recherche
					prd_search_results_del( $p, $c );

					// Suppression des urls traduites du produit
					if( $can_del_url ){
						rew_rewritemap_del_multilingue( _FLD_PRD_URL, array($c,$p) );
					}

					// Si pour le produit donnée il n'y a plus de canonique alors on met à null les autres
					if( !ria_mysql_num_rows(ria_mysql_query('select 1 from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$p.' and cly_url_is_canonical=1')) ){
						ria_mysql_query('
							update prd_classify
							set cly_url_is_canonical = null
							where cly_tnt_id = '.$config['tnt_id'].'
								and cly_prd_id = '.$p.'
						');
					}

					if( $can_del_url ){
						if( isset($config['url_no_category']) && $config['url_no_category'] ){
							rew_rewritemap_del_by_objects( CLS_PRODUCT, array($c, $p) );
						}else{
							// Récupère les sites
							$rwst = wst_websites_get();
							if( !$rwst || !ria_mysql_num_rows($rwst) ){
								return false;
							}

							// Supprime les urls virtuelles du produit
							while( $wst = ria_mysql_fetch_array($rwst) ){
								$prd_pages = cfg_urls_get( $wst['id'], CLS_PRODUCT);
								if( $prd_pages ){
									while( $page = ria_mysql_fetch_array($prd_pages) ){
										rew_rewritemap_del( '', $page['url'].'?cat='.$c.'&prd='.$p );
									}
								}
							}
						}
					}
				}

			}

			// Met à jour le champ prd_publish_cat indiquant si un produit peut apparaître par sa catégorie
			$cat_is_publish = prd_products_cat_published( $p ) ? 1 : 0;
			ria_mysql_query('update prd_products set prd_publish_cat='.$cat_is_publish.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$p);
			if( $cat_is_publish ){
				prd_products_rebuild_urls( $p );
			}

			// Si le produit est publié, on met à jour la date de modification de la catégorie
			$is_publish = ria_mysql_query('
				select prd_publish
				from prd_products
				where prd_tnt_id = '.$config['tnt_id'].'
					and prd_id = '.$p.'
					and prd_publish
			');

			if( $is_publish && ria_mysql_num_rows($is_publish) ){
				$categories_modified[] = $cat;
			}

			// Recalcule l'information "prd_publish_cat" des produits enfant-seulement
			prd_products_refresh_childonly_publish_cat( $p, $cat );
		}

		// Ce bloc regroupe les traitements de catégorie, pour éviter qu'ils soient exécutés autant de fois qu'il y a de produits.
		// Les traitements en lots sur les produits d'une catégorie étant supposés plus courants que les traitements en lots de catégories
		foreach( $cat as $c ){
			// Met à jour la position des produits de la même catégorie
			$sort = prd_categories_sort_get( $c );
			if( is_array($sort) && isset($sort['type']) && $sort['type']==SORT_PERSO ){
				$rprd = ria_mysql_query('
					select cly_prd_id as prd
					from prd_classify
					where cly_tnt_id = '.$config['tnt_id'].'
						and cly_cat_id = '.$c.'
					order by cly_prd_pos asc
				');

				if( $rprd && ria_mysql_num_rows($rprd) ){
					$count = 0;
					while( $prd = ria_mysql_fetch_array($rprd) ){
						ria_mysql_query('
							update prd_classify
							set cly_prd_pos = '.$count.'
							where cly_tnt_id = '.$config['tnt_id'].'
								and cly_cat_id = '.$c.'
								and cly_prd_id = '.$prd['prd'].'
						');

						$count++;
					}
				}
			}

			// Met à jour le nombre de produits publiés dans la catégorie
			prd_categories_refresh_products_published( $c );
		}

		// Rafraîchi la date de dernière mise à jour des catégories impactées par les modifications
		// Ce traitement est ici pour éviter que chaque produit d'une même catégorie génère un appel
		// à cette fonction
		if( sizeof($categories_modified) ){
			prd_categories_set_date_modified( $categories_modified );
		}
	}

	return true;
}
// \endcond

/**	Cette fonction retourne les identifiants de catégories dans lesquelles un produit est classé.
 *
 *	@param int $prd Obligatoire, Identifiant du produit (ou tableau d'identifiants).
 *	@param bool $published  Optionnel, détermine si toutes les catégories seront retournées (false) ou si seul les catégories publiées le seront (true)
 *	@param $recursive_publish Optionnel, si True, la hiérarchie parent doit également être publiée
 *	@param $is_canonical Optionnel, si True, seulement les classifications canonique seront retourné si false seulement les classifications non canonique
 *	@param $cat_root Optionnel, identifiant d'une catégorie racine
 *	@param $depth Optionnel, limite le résultat à un certain niveau de catégorie (ignoré si $cat_root vaut 0)
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- cat : identifiant de la catégorie
 *		- name : désignation de la catégorie
 *		- title : titre de la catégorie
 *		- cly_is_sync : détermine si le lien de classement provient de la gestion commerciale, ou non
 *		- url_is_canonical : si oui ou non l'url est identifiant comme étant l'url principale
 *		- url_alias : url du produit dans sa catégorie (priorité : cly_url_perso > cly_url_alias)
 *		- url_perso : url personnalisée du produit
 *		- cly_url_alias : url du produit générée automatiquement par le système
 *		- cat_url_alias : url de la catégorie
 *		- date_created : date de création du classement
 *		- date_created_en : date de création du classement au format en
 *		- prd_id : identifiant du produit
 *
 *	@return bool false si l'identifiant du produit est invalide
 */
function prd_products_categories_get( $prd, $published=false, $recursive_publish=false, $is_canonical=null, $cat_root=0, $depth=null ){

	$prd = control_array_integer( $prd );
	if( $prd === false ){
		return false;
	}

	$cat_root = control_array_integer( $cat_root, false );
	if( $cat_root === false ){
		return false;
	}

	global $config;

	$sql = '
	select cly_cat_id as cat, cat_name as name, if(cat_title!="",cat_title,cat_name) as title, cly_is_sync, cly_url_is_canonical as url_is_canonical,
	if(ifnull(cly_url_perso, \'\')=\'\', cly_url_alias, cly_url_perso) as url_alias, cly_url_perso as url_perso, cly_url_alias, cat_url_alias,
		date_format(cly_date_created, "%d/%m/%Y à %H:%i") as date_created, cly_date_created as date_created_en, cly_prd_id as prd_id
	from prd_classify
	join prd_categories as c on cly_tnt_id=cat_tnt_id and cly_cat_id=cat_id
	where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id in ('.implode(', ', $prd).')
	and cat_date_deleted is null
	';
	if( $published ){
		$sql .= '
		and cat_publish=1
		';
		if( $recursive_publish ){
			$sql .= '
			and not exists (
			select 1 from prd_cat_hierarchy as h
			join prd_categories as c2 on h.cat_tnt_id=c2.cat_tnt_id and h.cat_parent_id=c2.cat_id
			where c2.cat_publish!=1 and h.cat_child_id=c.cat_id and h.cat_tnt_id='.$config['tnt_id'].'
			'.( $depth !== null ? ' and h.cat_parent_depth = '.$depth : '' ).'
			)
			';
		}
	}

	if( sizeof($cat_root) ){
		$sql .= '
		and exists (
		select 1 from prd_cat_hierarchy as h
		where h.cat_tnt_id = '.$config['tnt_id'].'
		and c.cat_id=h.cat_child_id
		and h.cat_parent_id in ('.implode( ', ', $cat_root ).')
		)
		';
	}

	if( $is_canonical===true ){
		$sql .= ' and cly_url_is_canonical=1';
	}
	if( $is_canonical===false ){
		$sql .= ' and cly_url_is_canonical=0';
	}
	return ria_mysql_query($sql);
}

// \cond onlyria
/**	Cette fonction permet la définition d'un lien produit/catégorie comme étant synchronisé
 *	avec la gestion commerciale. Le besoin pour cette fonction n'est que temporaire durant la
 *	mise en place initiale du marqueur de synchronisation.
 *	@param int $prd Identifiant du produit
 *	@param int $cat Identifiant de la catégorie
 *
 */
function prd_products_categories_set_is_sync( $prd, $cat ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;

	return ria_mysql_query('
		update prd_classify set cly_is_sync=1
		where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$prd.' and cly_cat_id='.$cat.'
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de déterminer si un lien produit/catégorie est synchronisé ou non
 *	avec la gestion commerciale.
 *	@param int $prd Identifiant du produit
 *	@param int $cat Identifiant de la catégorie
 *
 */
function prd_products_categories_get_is_sync( $prd, $cat ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($cat) || $cat<=0 ) return false;

	$r_is_sync = ria_mysql_query('
		select cly_is_sync from prd_classify
		where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$prd.' and cly_cat_id='.$cat.'
		');
	if( !ria_mysql_num_rows($r_is_sync) )
		return false;
	return ria_mysql_result($r_is_sync,0,0);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de déterminer si un produit est classé dans une catégorie donnée.
 *
 *	@param int $prd Identifiant du produit
 *	@param int|array $cat Identifiant ou tableau d'identifiants de catégories
 *
 *	@return bool true si le produit est classé dans la catégorie demandée, false dans le cas contraire.
 *
 */
function prd_products_categories_exists( $prd, $cat ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;

	$cat = control_array_integer($cat, true);
	if( $cat === false ){
		return false;
	}

	// Teste si le produit se trouve dans la catégorie
	$res = ria_mysql_query( 'select 1 from prd_classify where cly_tnt_id='.$config['tnt_id'].' and cly_prd_id='.$prd.' and cly_cat_id in ('.implode(', ', $cat).')' );
	if( $res && ria_mysql_num_rows($res) ){
		return true;
	}

	$res = ria_mysql_query('
		select 1
		from prd_classify, prd_cat_hierarchy
		where cly_tnt_id='.$config['tnt_id'].' and cat_tnt_id=cly_tnt_id and cly_cat_id=cat_child_id
		and cat_parent_id in ('.implode(', ', $cat).')
		and cly_prd_id='.$prd.'
		');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

/**	Cette fonction retourne les identifiants de catégories dans lesquelles un produit apparaît.
 *
 *	Le résultat est retourné sous la forme d'un tableau à une dimension, contenant les identifiants
 *	des catégories de produit.
 *
 *	@param int $prd Obligatoire, identifiant ou tableau d'identifiants produit
 *	@param bool $published  Facultatif, détermine si toutes les catégories seront retournées (false) ou si seul les catégories publiées le seront (true)
 *	@param $recursive_publish Facultatif, si True, la hiérarchie parent doit également être publiée
 *	@param $is_canonical Facultatif, si True, seulement les classifications canonique seront retourné si false seulement les classifications non canonique
 *
 *	@return array un tableau a une dimension contenant les identifiants des catégories dans lesquelles un produit est classé
 */
function prd_products_categories_get_array( $prd, $published=false, $recursive_publish=false, $is_canonical=null ){
	$categs = array();

	$categories = prd_products_categories_get( $prd, $published, $recursive_publish, $is_canonical );
	if( !$categories || !ria_mysql_num_rows($categories) ){
		return $categs;
	}

	while( $r = ria_mysql_fetch_array($categories) ){
		$categs[] = $r['cat'];
	}

	return $categs;

}

// \cond onlyria
/**	Cette fonction permet de définir dans quelles catégories un produit doit apparaître.
 *	Si une ou plusieurs catégories dans lesquelles le produit est actuellement classé sont absentes
 *	du tableau $categs, le produit est retiré de ces catégories.
 *
 *	@param int $prd Obligatoire, identifiant du produit dont on souhaite définir les catégories
 *	@param $categs Obligatoire, identifiant ou tableau des identifiants des catégories dans lesquelles le produit doit apparaître
 *	@param $old_categs Optionnel, identifiant ou tableau d'identifier des anciennes catégorie où le produit ne doit plus y être
 *	@param bool $is_sync Optionnel, détermine si ce classement provient de la gestion commerciale, ou non
 *	@param string $url_change Optionnel, par défaut les urls du produit seront modifiées, mettre False pour ne pas les impacter par le déplacement (le nombre de catégorie dans $categs et $old_categs doit être identique et les classements n'ont synchronisé)
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_products_categories_set( $prd, $categs, $old_categs=false, $is_sync=false, $url_change=true ){
	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	global $config;

	if( is_array($categs) ){
		if( !sizeof($categs) ){
			return false;
		}
		foreach( $categs as $categ ){
			if( !is_numeric($categ) || $categ <= 0 ){
				return false;
			}
		}
	}else{
		if( is_numeric($categs) && $categs > 0 ){
			$categs = array($categs);
		}else{
			return false;
		}
	}

	$e_categs = prd_products_categories_get_array($prd);

	// Retire le produit des catégories dans lesquelles il n'est plus
	if( !$old_categs ){
		$old_categs = array_diff( $e_categs, $categs );
	} else {
		if( is_numeric($old_categs) && !in_array($old_categs, $categs) ){
			$old_categs = array( $old_categs );
		}else{
			$old_categs = array();
		}
	}

	$new_url = ''; $old_url = array();

	// Ajoute le produit aux nouvelles catégories
	$new_categs = array_diff( $categs, $e_categs );

	// Déplacement de produit en conservant les URLs
	if (!$url_change && count($categs) == count($old_categs)) {
		foreach ($old_categs as $key=>$old_cat) {
			$res = ria_mysql_query('
				update prd_classify
				set cly_cat_id = '.$categs[ $key ].'
				where cly_tnt_id = '.$config['tnt_id'].'
					and cly_prd_id = '.$prd.'
					and cly_cat_id = '.$old_cat.'
					and cly_is_sync = 0
			');

			if (!$res) {
				return false;
			}

			if (ria_mysql_affected_rows()) {
				$res = ria_mysql_query('
					update rew_rewritemap
						set url_intern = replace(url_intern, "cat='.$old_cat.'", "cat='.$categs[ $key ].'"),
							url_obj_id_0='.$categs[ $key ].'
						where url_tnt_id = '.$config['tnt_id'].'
							and url_cls_id = '.CLS_PRODUCT.'
							and url_obj_id_0 = '.$old_cat.'
							and url_obj_id_1 = '.$prd.'
							and url_code = 200
				');

				if (!$res) {
					return false;
				}

				// Mise à jour du nombre de produits publiés des catégories
				prd_categories_refresh_products_published( $old_cat );
				prd_categories_refresh_products_published( $categs[ $key ] );
			}
		}

		return true;
	}

	if( sizeof($new_categs)>0 ){
		if( !prd_products_add_to_cat( $prd, $new_categs, $is_sync ) ){
			error_log( 'Echec de prd_products_add_to_cat('.$prd.', '.print_r($new_categs, true).', '.( $is_sync ? '1' : '0' ).')' );
		}

		$res = ria_mysql_query('
			select cly_url_alias as url
			from prd_classify
			where cly_tnt_id='.$config['tnt_id'].'
				and cly_prd_id='.$prd.'
				and cly_cat_id in ('.implode(', ', $new_categs).')
			limit 1
		');

		if( $res && ria_mysql_num_rows($res) ){
			$new_url = ria_mysql_result( $res, 0, 'url' );
		}
	}

	$ref = false;
	if( sizeof($old_categs)>0 ){
		if( sizeof($old_categs)==1 ){
			$res = ria_mysql_query('
				select cly_tag_title as tag_title, cly_tag_desc as tag_desc, cly_keywords as keywords
				from prd_classify
				where cly_tnt_id='.$config['tnt_id'].'
					and cly_prd_id='.$prd.'
					and cly_cat_id in ('.implode(', ', $old_categs).')
			');

			if( $res && ria_mysql_num_rows($res) ){
				$ref = ria_mysql_fetch_array( $res );
			}
		}

		// récupère les anciennes urls
		if( trim($new_url)!='' ){
			$res = ria_mysql_query('
				select cly_url_alias as url, url_wst_id as wst, url_lng_code as lng, cly_cat_id as cat, cly_prd_id as prd
				from prd_classify
				join rew_rewritemap on (cly_tnt_id=url_tnt_id and cly_url_alias=url_extern)
				where cly_tnt_id='.$config['tnt_id'].'
				and cly_prd_id='.$prd.'
				and cly_cat_id in ('.implode(', ', $old_categs).')
				');

			if( $res && ria_mysql_num_rows($res) ){
				while( $r = ria_mysql_fetch_array($res) ){
					$old_url[] = $r;
				}
			}
		}

		if( !prd_products_del_from_cat( $prd, $old_categs, $is_sync, false ) ){
			error_log( 'Echec de prd_products_del_from_cat('.$prd.', '.print_r($old_categs, true).', '.( $is_sync ? '1' : '0' ).')' );
		}
	}

	// créer les redirections 301
	if( sizeof($old_url) && trim($new_url)!='' ){
		foreach( $old_url as $old ){
			if ($old['url'] == $new_url) {
				continue;
			}

			if( !rew_rewritemap_add( $old['url'], $new_url, 301, $old['wst'], $old['lng'], CLS_PRODUCT, array($old['cat'], $old['prd']) ) ){
				error_log( 'Echec de rew_rewritemap_add('.$old['url'].', '.$new_url.', '.$old['wst'].', '.$old['lng'].', '.CLS_PRODUCT.', array('.$old['cat'].', '.$old['prd'].'))' );
			}
		}
	}

	if( $ref!==false ){
		foreach( $new_categs as $ncat ){
			if( !prd_classify_update_referencing( $ncat, $prd, $ref['tag_title'], $ref['tag_desc'], $ref['keywords']) ){
				error_log( 'Echec lors de la conservation du référencement ('.$ncat.' - '.$prd.') : '.$ref['tag_title'].';;'.$ref['tag_desc'].';;'.$ref['keywords'] );
			}
		}
	}

	prd_products_set_date_modified( $prd );

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier les produits d'une marque à un niveau donné.
 *	@param int $brd_id Obligatoire, identifiant d'une marque
 *	@return bool false si la méthode de tri est alphabétique, true si la méthode de tri est personnalisée
 */
function prd_products_brands_order_get( $brd_id ){
	global $config;

	if (!is_numeric($brd_id) || $brd_id <= 0) {
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_brd_pos is not null
			and prd_brd_id = '.$brd_id.'
			and prd_date_deleted is null
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'ordre des produits pour une marque donnée
 * 	@param int $brd_id Obligatoire, l'identifiant de la marque
 * 	@param $order Obligatoire, l'ordre dans lequel il faut trier - 0/false pour un tri alphabétique, 1/true pour un tri personnalisé
 * 	@return bool true si on a réussi à mettre à jour l'ordre, false en cas d'échec
 */
function prd_products_order_update( $brd_id, $order ){
	if (!is_numeric($brd_id) || $brd_id <= 0) {
		return false;
	}

	global $config;

	$res = false;

	if ($order){
		$sql = '
			select prd_id
			from prd_products
			where prd_tnt_id ='.$config['tnt_id'].'
				and prd_brd_id ='.$brd_id.'
			order by prd_name asc
		';

		$r_product = ria_mysql_query($sql);

		if ($r_product) {
			$pos = 0;

			while ($product = ria_mysql_fetch_assoc($r_product)){
				ria_mysql_query('
					update prd_products
					set prd_brd_pos = '.$pos.',
						prd_date_modified = now()
					where prd_tnt_id='.$config['tnt_id'].'
						and prd_id='.$product['prd_id'].'
						and prd_date_deleted is null
				');

				$pos++;
			}

			$res = true;
		}
	}else{
		$res  = ria_mysql_query('
			update prd_products
			set prd_brd_pos=null,
				prd_date_modified = now()
			where prd_tnt_id='.$config['tnt_id'].'
				and prd_brd_id='.$brd_id.'
		');
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de définir l'id du produit destination comme étant url canonique d'un produit source
 *	@param int $prdSrc Obligatoire, identifiant d'un produit initial
 *	@param int $prdDest Obligatoire, identifiant d'un produit son url canonique
 *
 *	@return bool True si la modification a fonctionné, false dans le cas contraire
 */
function prd_product_set_canonical( $prdSrc, $prdDest ){
	global $config;

	if( !is_numeric($prdSrc) || $prdSrc<=0 ) return false;
	if( !is_numeric($prdDest) || $prdDest<=0 ) return false;

	if( $prdSrc == $prdDest ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
		and prd_id = '.$prdDest.'
		and ifnull(prd_canonical_id, 0) > 0
		');

	if( $res && ria_mysql_num_rows($res) ){
		return false;
	}

	if( !ria_mysql_query('update prd_products set prd_canonical_id='.$prdDest.' where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prdSrc) ) return false;

	prd_products_set_date_modified( $prdSrc );
	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de définir a "NULL" le champs contenant le prd_canonical_id d'un produit
 *	@param int $prd Obligatoire, identifiant du
 *
 *	@return string Retourne l'url canonique pour un produit
 */
function prd_product_remove_canonical( $prd){
	if( !is_numeric($prd) || $prd<=0 ) return false;

	global $config;

	if( !ria_mysql_query('update prd_products set prd_canonical_id=NULL where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prd) ) return false;

	prd_products_set_date_modified( $prd );

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si un produit est utilisé pour générer une url canonique par un autre produit.
 *	@param int $prd_id Identifiant d'un produit
 *	@return bool True si le produit est utilisé pour générer une url canonique, False dans le cas contraire
 */
function prd_products_is_used_as_canonical( $prd_id ){
	global $config;

	if (!is_numeric($prd_id) || $prd_id<=0) {
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_date_deleted is null
			and prd_canonical_id = '.$prd_id.'
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction recalcule l'information "prd_publish_cat" des produits enfant-seulement frères d'un produit donné (par frère, on entend qu'ils sont classés au même endroit).
 *
 *	@param int $prd_id Obligatoire, identifiant du produit de base. La fonction ne fait rien si c'est un enfant-seulement.
 *	@param array $ar_cats Obligatoire, tableau des identifiants de catégories où $prd_id est classé.
 *
 *	@return bool True si succès ou si aucune action n'est à mener. False en cas d'échec.
 *	@todo Cette fonction devrait vérifier les produits enfant-seulement des catégories enfants de $ar_cats ? (à vérifier)
 */
function prd_products_refresh_childonly_publish_cat( $prd_id, $ar_cats ){
	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	if( prd_products_get_childonly( $prd_id ) ){
		return true;
	}

	$ar_cats = control_array_integer( $ar_cats, false );

	if( !is_array($ar_cats) ){
		return false;
	}

	global $config;

	// Retire des categories ($ar_cats) celles qui ne doivent pas être prisent en compte dans l'information de publication d'un article ($config['publish_childonly_cat_exclude'])
	if (isset($config['publish_childonly_cat_exclude']) && is_array($config['publish_childonly_cat_exclude'])) {
		foreach ($config['publish_childonly_cat_exclude'] as $value) {
			$key = array_search($value, $ar_cats);

			if ($key!==false) {
				unset($ar_cats[ $key ]);
			}
		}
	}

	// produit de base non classé
	if( !sizeof($ar_cats) ){
		return true;
	}

	// récupère les frères "enfant seulement" du produit (produits classés dans les mêmes catégories que le produit actuel)
	$sql = '
		select distinct prd_id as id, prd_publish_cat as old_publish_cat
		from
		prd_products
			join prd_classify on prd_tnt_id = cly_tnt_id and prd_id = cly_prd_id
		where
			prd_tnt_id = '.$config['tnt_id'].'
			and prd_date_deleted is null
			and prd_childonly = 1
			and cly_cat_id in ('.implode(', ', $ar_cats).')
	';

	$error = false;

	if( $rchilds = ria_mysql_query($sql) ){
		while( $c = ria_mysql_fetch_assoc($rchilds) ){

			// au moins une catégorie du produit publiée ?
			$cat_is_publish = prd_products_cat_published( $c['id'] );

			// Mise à jour de la publication d'un article que si l'ancienne valeur ($c['old_publish_cat']) est différente de la nouvelle ($cat_is_publish)
			if( $c['old_publish_cat'] != $cat_is_publish ){
				$sql = '
					update prd_products
					set prd_publish_cat = '.( $cat_is_publish ? '1' : '0' ).'
					where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$c['id'].'
				';

				if( !ria_mysql_query($sql) ){
					$error = true;
				}elseif( $cat_is_publish ){
					// reconstruction de l'URL si publication
					prd_products_rebuild_urls( $c['id'] );

					try{
						// Index le produit dans le moteur de recherche.
						RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
							'cls_id' => CLS_PRODUCT,
							'obj_id_0' => $prd_id,
						));
					}catch(Exception $e){
						error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
					}
				}
			}
		}
	}

	return !$error;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de provoquer la publication d'un ou plusieurs produits.
 *	Lorsqu'elle est utilisée, cette fonction met à jour le nombre de produits publiés dans la ou les catégories
 *	oâ¹ le produit apparaît.
 *
 *	@param int $prd Identifiant du produit à publier ou tableau des identifiants de produits à publier.
 *
 *	@todo Maintenir à jour le nombre de produits publiés de chaque marque.
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_products_publish( $prd ){
	global $config;

	// Validation des paramètres. En interne, la fonction n'utilise qu'un tableau
	if( is_numeric($prd) ) $prd = array( $prd );
	if( !is_array($prd) ) return false;
	foreach( $prd as $p ) if( !is_numeric($p) ) return false;

	$lst_prd = implode(',',$prd);

	ria_mysql_query('update prd_products set prd_first_published=now() where prd_tnt_id='.$config['tnt_id'].' and prd_id in ('.$lst_prd.') and prd_publish=0 and ifnull(prd_first_published, "")="";');
	ria_mysql_query('update prd_products set prd_publish=1, prd_date_published=now() where prd_tnt_id='.$config['tnt_id'].' and prd_id in ('.$lst_prd.') and prd_publish=0;');

	foreach( $prd as $p ){
		if( !is_numeric($p) || $p <= 0 ){
			continue;
		}
		$ar_cats = array();
		$categories = prd_products_categories_get($p);
		while( $r = ria_mysql_fetch_array($categories) ){
			$ar_cats[] = $r['cat'];
			// Met à jour le nombre de produits publiés par catégorie
			prd_categories_refresh_products_published($r['cat']);
		}

		if( sizeof($ar_cats) ){
			// Mise à jour de la date de modification des catégories où le produit est classé
			prd_categories_set_date_modified( $ar_cats );
		}

		$cat_is_publish = prd_products_cat_published( $p ) ? 1 : 0;
		ria_mysql_query('update prd_products set prd_publish_cat='.$cat_is_publish.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$p);
		if( $cat_is_publish ){
			prd_products_rebuild_urls( $p );
		}

		prd_products_refresh_childonly_publish_cat( $p, $ar_cats );

		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $p,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		// mise à jour du nombre de produit publié pour une marque
		$rbrd = prd_products_get_simple( $p );
		if( $rbrd && ria_mysql_num_rows($rbrd) ){
			$brd = ria_mysql_result( $rbrd, 0, 'brd_id' );
			if( is_numeric($brd) && $brd>0 ){
				prd_brands_update_products( $brd );
			}
		}
	}

	prd_products_set_parent_publish($prd);

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les taux de remplissage des champs poids et poids net des produits
 *	@param int $prd Optionnel, identifiant d'un produits
 *	@return resource Retourne un résultat de type MySQL avec :
 *			- nb_prd : nombre total de produits
 *			- no_sleep : nombre de produits pas en sommeil
 *			- publish_no_sleep : nombre de produits pas en sommeil et en ventes sur le site marchand
 *			- w_fills : nombre de produits dont le poids brut est renseigné
 *			- wn_fills : nombre de produits dont le poids net est renseigné
 *			- no_sleep_w : nombre de produits pas en sommeil avec le poids brut renseigné
 *			- no_sleep_wn : nombre de produits pas en sommeil avec le poids net renseigné
 *			- publish_no_sleep_w : nombre de produits pas en sommeil, en vente et dont le poids brut est renseigné
 *			- publish_no_sleep_wn : nombre de produits pas en sommeil, en vente et dont le poids net est renseigné
 */
function prd_products_weight_get_stats( $prd=0 ){
	global $config;

	if (!is_numeric($prd) || $prd < 0) {
		return false;
	}

	$sql = '
		select count(*) as nb_prd,
			sum( if( not prd_sleep, 1, 0 ) ) as no_sleep,
			sum( if( not prd_sleep and prd_publish and prd_publish_cat, 1, 0 ) ) as publish_no_sleep,
			sum( if( prd_weight IS NOT NULL , 1, 0 ) ) as w_fills,
			sum( if( prd_weight_net is not null, 1, 0 ) ) as wn_fills,
			sum( if( not prd_sleep and prd_weight is not null, 1, 0 ) ) as no_sleep_w,
			sum( if( not prd_sleep and prd_weight_net is not null, 1, 0 ) ) as no_sleep_wn,
			sum( if( not prd_sleep and prd_publish and prd_publish_cat and prd_weight is not null, 1, 0 ) ) as publish_no_sleep_w,
			sum( if( not prd_sleep and prd_publish and prd_publish_cat and prd_weight_net is not null, 1, 0 ) ) as publish_no_sleep_wn
		from prd_products
		where prd_tnt_id ='.$config['tnt_id'].'
			and prd_date_deleted is null
			and prd_ref not in (\''.implode('\',\'',$config['dlv_prd_references']).'\')
	';

	if( $prd>0 ){
		$sql .= ' and prd_id='.$prd;
	}
	$res = ria_mysql_query($sql);

	if( $res===false ){
		return false;
	}
	return ria_mysql_fetch_array($res);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retirer un ou plusieurs produits de la boutique en ligne.
 *
 *	@param int $prd Identifiant du produit à dé-publier ou tableau des identifiants de produits à dé-publier.
 *
 *	@todo Maintenir à jour le nombre de produits publiés de chaque marque.
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function prd_products_unpublish( $prd ){
	global $config;

	// Validation des paramètres. En interne, la fonction n'utilise qu'un tableau
	if( is_numeric($prd) ) $prd = array( $prd );
	if( !is_array($prd) ) return false;
	foreach( $prd as $p ) if( !is_numeric($p) ) return false;

	$lst_prd = '0';
	foreach( $prd as $p ){
		$lst_prd .= ','.$p;
	}

	ria_mysql_query('update prd_products set prd_publish=0 where prd_tnt_id='.$config['tnt_id'].' and prd_id in ('.$lst_prd.');');

	foreach( $prd as $p ){
		$ar_cats = array();
		$categories = prd_products_categories_get($p);
		while( $r = ria_mysql_fetch_array($categories) ){
			$ar_cats[] = $r['cat'];
			// Met à jour le nombre de produits publiés de la catégorie
			prd_categories_refresh_products_published($r['cat']);
		}

		if( sizeof($ar_cats) ){
			// Mise à jour de la date de modification des catégories où le produit est classé
			prd_categories_set_date_modified( $ar_cats );
		}

		$cat_is_publish = prd_products_cat_published( $p ) ? 1 : 0;
		ria_mysql_query('update prd_products set prd_publish_cat='.$cat_is_publish.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$p);
		if( $cat_is_publish ){
			prd_products_rebuild_urls( $p );
		}

		prd_products_refresh_childonly_publish_cat( $p, $ar_cats );

		try{
			// Index le produit dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
				'cls_id' => CLS_PRODUCT,
				'obj_id_0' => $p,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		// mise à jour du nombre de produit publié pour une marque
		$rbrd = prd_products_get_simple( $p );
		if( $rbrd && ria_mysql_num_rows($rbrd) ){
			$brd = ria_mysql_result( $rbrd, 0, 'brd_id' );
			if( is_numeric($brd) && $brd>0 ){
				prd_brands_update_products( $brd );
			}
		}
	}

	prd_products_set_parent_publish($prd);
	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la duplication totale ou partielle d'un produit
 *	@param int $prd Facultatif, Identifiant du produit sur lequel les données seront copiées. Si false, un nouveau produit sera créé
 *	@param int $prd_model Obligatoire, Identifiant du produit servant de modèle
 *	@param bool $price Facultatif, détermine si les tarifs doivent être dupliqués
 *	@param bool $hierarchy Facultatif, détermine si la hiérarchie parent/enfants doit être dupliquée
 *	@param bool $fields Facultatif, détermine si les champs avancés rattachés au produit doivent être dupliqués
 *	@param bool $description Facultatif, détermine si les descriptions doivent être dupliquées
 *	@param bool $images Facultatif, booléen indiquant si les images doivent être dupliquées ou non
 *	@param bool $classify Facultatif, booléen indiquant si la classification doit être dupliquée ou non
 *	@param bool $title Facultatif, booléen indiquant si le titre doit être dupliqué ou non
 * 	@param bool $canonical Facultatif, boolean indiquant si le lien canonique doit être dupliqué ou non
 *	@param bool $parent Facultatif, boolean indiquant si les produits parents doivent être dupliqués
 *	@param bool $child Facultatif, boolean indiquant si les produits enfants doivent être dupliqués
 * 	@param array $relation Facultatif, Tableau d'identifiants de type de relation (source prd_relations_types) devant être dupliquées
 *
 *	@return int|bool L'identifiant de la copie en cas de succès, False en cas d'échec
 */
function prd_products_duplicate( $prd=false, $prd_model, $price=false, $hierarchy=false , $fields=false, $description=false, $images=false, $classify=false, $title=false, $canonical=false, $parent=false, $child=false, $relation=array() ){
	global $config;

	if (!is_numeric($prd_model) || $prd_model <= 0) {
		return false;
	}

	$r_model = prd_products_get($prd_model);
	if (!$r_model || !ria_mysql_num_rows($r_model)) {
		return false;
	}

	$model = ria_mysql_fetch_array($r_model);

	if( $prd===false ){
		$ref = $model['ref'].'_copie';
		$name = $model['name'].' - copie';
		if( !($prd=prd_products_add($ref,$name,'',($model['brd_id'] ? $model['brd_id'] : 0),false,$model['weight'],$model['length'],$model['width'],$model['height']) ) ){
			return false;
		}
	}

	if( !is_numeric( $prd ) || !prd_products_exists( $prd ) ) return false;
	if( $prd == $prd_model ) return false;

	// champ avancé
	if( $fields ){
		//suppresion
		fld_object_values_del($prd);
		fld_object_models_del($prd);

		// duplication des champs avancés
		$flds = fld_models_get( 0, $prd_model );
		while( $f = ria_mysql_fetch_array($flds ) )
			fld_object_models_add($prd , $f['id']);

		$field_model = fld_fields_get( 0, 0, 0, 0, 0, $prd_model);
		while( $f = ria_mysql_fetch_array( $field_model ) ){

			if( $f['type_id']==FLD_TYPE_SELECT){
				$f['obj_value'] = fld_restricted_values_get_id($f['id'], $f['obj_value']);
			}
			else if( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE ){
				$value = explode( ',', $f['obj_value'] );
				$ids = array();
				foreach( $value as $v )
					$ids[] = fld_restricted_values_get_id($f['id'], trim($v));
				$f['obj_value'] = implode( $ids, ', ' );
			}

			// duplication des valeurs
			if( !fld_object_values_set( $prd, $f['id'] , $f['obj_value'] ) )
				return false;
		}

	}

	// Duplication de la description
	if( $description ){
		// Description courte
		prd_products_update_desc( $prd, $model['desc']);

		// Copie de la traduction de la description courte
		foreach( $config['i18n_lng_used'] as $lng ){
			$desc_model = fld_object_values_get( $prd_model, _FLD_PRD_DESC, $lng );

			fld_object_values_set( $prd, _FLD_PRD_DESC, $desc_model, $lng );
		}

		// Description longue
		prd_products_update_desc_long( $prd, $model['desc-long']);

		// Copie de la traduction de la description longue
		foreach( $config['i18n_lng_used'] as $lng ){
			$desc_long_model = fld_object_values_get( $prd_model, _FLD_PRD_DESC_LG, $lng );

			fld_object_values_set( $prd, _FLD_PRD_DESC_LG, $desc_long_model, $lng );
		}
	}

	// duplication du titre
	if( $title ){
		if( !prd_products_update_title($prd, $model['title']) ){
			return false;
		}

		// Copie de la traduction du titre
		foreach( $config['i18n_lng_used'] as $lng ){
			$title_model = fld_object_values_get( $prd_model, _FLD_PRD_TITLE, $lng );

			fld_object_values_set( $prd, _FLD_PRD_TITLE, $title_model, $lng );
		}
	}

	if( $hierarchy ) {
		//suppresion des relations du produit
		prd_hierarchy_del( $prd );

		// duplication de la hierarchie
		$hiers = prd_childs_get( $prd_model, false, false );

		while( $h = ria_mysql_fetch_array( $hiers ) ){
			prd_hierarchy_add( $prd, $h['id'], 0);
		}

		prd_relations_del($prd);

		// duplication des produits en relation
		$related = prd_relations_get( $prd_model );
		while( $r = ria_mysql_fetch_array( $related ) ){
			prd_relations_add( $prd, $r['dst_id'], $r['type_id'] );
		}
	}

	// Produits parents
	if( $parent ){
		// Suppression des produits du parent
		prd_hierarchy_del( 0, $prd );

		$r_hiers = prd_hierarchy_get( 0, $prd_model );
		if( !$r_hiers ){
			return false;
		}

		// Duplique les parents du produit model
		while( $hier = ria_mysql_fetch_assoc($r_hiers) ){
			prd_hierarchy_add( $hier['parent'], $prd );
		}
	}

	// Produits enfants
	if( $child ){
		// Suppression des produits enfants
		prd_hierarchy_del( $prd );

		$r_hiers = prd_hierarchy_get( $prd_model );
		if( !$r_hiers ){
			return false;
		}

		// Duplique les enfants du produit model
		while( $hier = ria_mysql_fetch_assoc($r_hiers) ){
			prd_hierarchy_add( $prd, $hier['child'] );
		}
	}

	// Relation entre produit
	if( count($relation) ){
		foreach( $relation as $rel ){
			prd_relations_del( $prd, 0, $rel );

			$r_rel_model = prd_relations_get( $prd_model, 0, $rel );

			while( $rel_model = ria_mysql_fetch_assoc($r_rel_model) ){
				prd_relations_add( $prd, $rel_model['dst_id'], $rel );
			}
		}
	}

	// duplication des prix
	if( $price ){
		if( $rprices = prc_prices_get( 0, 0, false, false, false, $prd_model ) ){
			while( $price = ria_mysql_fetch_array($rprices) ){

				$cnd_array = array();
				if( $rconditions = prc_price_conditions_get( $price['id'] ) ){
					while( $cnd = ria_mysql_fetch_array($rconditions) )
						$cnd_array[$cnd['fld']] = array( 'symbol'=>$cnd['symbol'], 'value'=>$cnd['value'] );
				}

				if( !prc_prices_add( $price['type'], $price['value'], $price['date-start'], $price['date-end'], $price['qte-min'], $prd, $price['cat'], $price['is-cumuled'], false, $price['name'], $cnd_array, $price['qte-max'], false, $price['grp_id'] ) )
					return false;
			}
		}

		// duplication des promotions
		if( $rprices = prc_prices_get( 0, 0, false, false, false, $prd_model, false, false, false, null, null, 1, 0, false, null, array(), array(), null, true ) ){
			while( $price = ria_mysql_fetch_array($rprices) ){

				$cnd_array = array();
				if( $rconditions = prc_price_conditions_get( $price['id'] ) ){
					while( $cnd = ria_mysql_fetch_array($rconditions) )
						$cnd_array[$cnd['fld']] = array( 'symbol'=>$cnd['symbol'], 'value'=>$cnd['value'] );
				}

				if( !prc_prices_add( $price['type'], $price['value'], $price['date-start'], $price['date-end'], $price['qte-min'], $prd, $price['cat'], $price['is-cumuled'], false, $price['name'], $cnd_array, $price['qte-max'], true, $price['grp_id'] ) )
					return false;
			}
		}

		// duplication de la tva
		if( $rtva = prc_tvas_get( 0, false, $prd_model ) ){
			while( $tva = ria_mysql_fetch_array($rtva) ){
				if( !prc_tvas_add( $tva['rate'], $prd, 0, false, $tva['cac'] ) )
					return false;
			}
		}
	}

	// duplication des catégories
	if( $classify ){
		$cat = prd_products_categories_get($prd_model);
		$tmp_cat = array();
		while( $c = ria_mysql_fetch_array($cat) ){
			$tmp_cat[] = $c['cat'];
		}
		if( sizeof( $tmp_cat ) ){
			prd_products_categories_set($prd , $tmp_cat);
		}

		// Gestion des urls multilingue
		foreach( $config['i18n_lng_used'] as $lng ){
			// Recherche les classements du produit
			$rcat = prd_classify_get( false, $prd );
			while( $cat = ria_mysql_fetch_array($rcat) ){
				// Recherche l'url du produit et maj de l'url dans la langue $lng
				$res = rew_rewritemap_add_multilingue(array($cat['cat'], $prd), CLS_PRODUCT, $lng, $cat['url'], _FLD_PRD_URL);
			}
		}


	}

	// Lien canonique
	if( $canonical ){
		$canonical_cat = prd_classify_get_canonical_cat( $prd_model );
		if( is_numeric($canonical_cat) && $canonical_cat > 0 ){
			// Création du classement si celui-ci n'existe pas déjà
			if( !prd_classify_exists($canonical_cat, $prd) ){
				if( !prd_products_categories_set($prd, $canonical_cat) ){
					return false;
				}
			}

			// Enregistre la catégorie canonique
			if( !prd_classify_set_canonical($prd, $canonical_cat) ){
				return false;
			}
		}
	}

	// onglet images
	if( $images ){
		if( $model['img_id'] ){
			prd_images_main_add_existing($prd, $model['img_id'] );
		}

		$rimage_model = prd_images_get( $prd_model );
		if( $rimage_model && ria_mysql_num_rows($rimage_model) ){
			while( $img = ria_mysql_fetch_array($rimage_model) ){
				prd_images_add_existing( $prd, $img['id']);
			}
		}
	}


	// On demande à ce que l'url soit recréer lors de la mise à jour du titre du produit
	prd_products_set_url_rebuild( $prd, true );

	return $prd;
}
// \endcond

/**	Cette fonction est chargée de déterminer si une référence produit correspond à du port (transport)
 *	@param string $ref Obligatoire, référence du produit à contrôler
 *	@return bool true si la référence correspond à des frais de port, false dans le cas contraire
 */
function prd_products_is_port( $ref ){
	global $config;
	return array_search( trim($ref), $config['dlv_prd_references'] )!==false;
}

// \cond onlyria
/**	Cette fonction est un alias de prd_products_is_port() sauf qu'elle permet une recherche à partir de l'identifiant du produit
 *	@param int $prd_id Identifiant du produit
 *	@return bool True si l'article est un frais de port, False sinon
 */
function prd_products_is_port_id( $prd_id ){
	return prd_products_is_port( trim(prd_products_get_ref($prd_id)) );
}
// \endcond

/** Cette fonction détermine si un article est suivi en stock
 *	Pour qu'un produit soit suivi en stock, les conditions suivantes doivent être réunies :
 *		- Le produit n'est pas un frais de port ou une option cadeau
 *		- Le produit n'est pas follow_stock = 0
 *		- Le produit a une ligne dans la table des stocks
 *	Il est possible de rechercher un produit soit en indiquant sa référence (paramètre $prd_ref), soit en indiquant son identifiant (paramètre $prd_id)
 *
 *	@param string $prd_ref Facultatif, référence du produit à tester
 *	@param int $prd_id Facultatif, identifiant du produit
 *
 *	@return bool True si l'article est suivi en stock, False sinon
 */
function prd_products_is_follow_stock( $prd_ref='', $prd_id=0 ){
	global $config;

	if( !is_numeric($prd_id) || $prd_id<0 ){
		return false;
	}
	if( trim($prd_ref)=='' && $prd_id==0 ){
		return false;
	}

	// Paramètre valide
	$prd_ref = trim( $prd_ref );

	// Frais de port
	if( in_array( $prd_ref,$config['dlv_prd_references'] ) ) return false;

	// Option cadeau
	if( $options = dlv_options_get() ){
		if( isset($options['gift-ref']) ){
			if( $prd_ref==$options['gift-ref'] )
				return false;
		}
	}

	// Produit suivi en stock
	$sql = '
		select prd_id
		from prd_products
		where prd_date_deleted is null
			and prd_follow_stock=1
	';

	if( $prd_id>0 ){
		$sql .= ' and prd_id='.$prd_id;
	}else{
		$sql .= 'and prd_ref="'.addslashes($prd_ref).'"';
	}

	$sql .= '
		and prd_tnt_id='.$config['tnt_id'].'
	';

	$res = ria_mysql_query( $sql );
	if( $res===false || !ria_mysql_num_rows($res) ) return false;

	return true;
}

/** Cette fonction permet de savoir si un produit est en destockage
 *	@param int $prd_id Obligatoire, identifiant d'un produit
 *	@param int $usr_id Optionnel, identifiant d'un compte client (permet de récupérer son dépôt de stock lié)
 *	@param int $dps Optionnel, identifiant d'un dépot de stockage
 *	@return bool True si le produit est en destockage, False dans le cas contraire
 */
function prd_products_is_destock( $prd_id, $usr_id=0, $dps=0 ){
	global $config;

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}
	if( !is_numeric($dps) || $dps<0 ){
		return false;
	}
	if( !is_numeric($usr_id) || $usr_id<0 ){
		return false;
	}

	// Identifiant du dépôt de stockage
	if( $dps===false || !prd_deposits_exists($dps) ){
		// if( $config['prd_deposits']=='use-customer' ){
		if( $config['tnt_id']==2 ){
			$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
		}else{
			$dps = prd_deposits_get_main();
		}
	}

	if( !$dps ) $dps = 0;

	$res = ria_mysql_query('
		select 1
		from prd_products
		join prd_stocks on (prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
		where prd_id='.$prd_id.'
			and prd_tnt_id='.$config['tnt_id'].'
			and prd_sleep and ('  .prd_stocks_get_sql(). '-sto_prepa)>0
			and prd_publish and prd_publish_cat
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

// \cond onlyria
/**	Cette fonction permet le renseignement de l' ecotaxe sur un produit
 *	@param int $prd Identifiant du produit
 *	@param float $value montant de l'écotaxe
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_products_set_ecotaxe( $prd, $value ){
	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	if( !is_numeric(str_replace(',','.',$value)) || str_replace(',','.',$value)<0 ) {
		return false;
	}

	global $config;

	return ria_mysql_query('update prd_products set prd_ecotaxe='.str_replace(',','.',$value).' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);
}
// \endcond

/** Récupère l'ecotaxe d'un produit
 *	@param int $prd Identifiant du produit
 *	@return float|bool Ecotaxe du produit, False en cas d'erreur
 */
function prd_products_get_ecotaxe( $prd ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;

	$result = ria_mysql_query( 'select prd_ecotaxe from prd_products where prd_id='.$prd.' and prd_tnt_id='.$config['tnt_id'] );
	if( !$result || !ria_mysql_num_rows($result) ) return false;

	return ria_mysql_result( $result,0,0 );
}

// \cond onlyria
/** Recupères les catégories d'une liste de produits
 *	@param int $cnt Obligatoire, identifiant d'un contenu du moteur de recherches
 *	@param bool $published  Optionnel, détermine si les articles doivent être publiés
 *	@param int $parent Optionnel, identifiant d'une catégorie parent
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- cat : Identifiant de la catégorie
 *		- name : Nom de la catégorie
 *		- title : Titre de la catégorie
 *		- nb_products : Nombre de produits dans cette catégorie
 */
function prd_products_categories_get_from_list( $cnt, $published=false, $parent=0 ){
	global $config;

	if( !is_array($cnt ) ) return false;
	$sql = '
		select cly_cat_id as cat, cat_name as name, if(cat_title!="",cat_title,cat_name) as title, count(cly_cat_id) as nb_products
		from prd_classify, prd_categories
		where cly_tnt_id='.$config['tnt_id'].' and cat_tnt_id=cly_tnt_id and cly_cat_id=cat_id and cly_cnt_id in ('.implode(',',$cnt).')
			and cat_date_deleted is null
			'.( $published ? ' and cat_publish':'' )
	;
	if( is_numeric($parent) && $parent>0 ) $sql .= ' and cat_parent_id='.$parent;
	elseif($parent === null) $sql .= ' and cat_parent_id is null ';
	$sql .= ' group by cly_cat_id';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction retourne un ou plusieurs articles selon un code-barre
 *	@param string $barcode Code-barre
 *	@return resource Un résultat de requête MySql comprenant les champs suivants :
 *		- id : Identifiant de l'article
 *		- ref : Référence de l'article
 *		- name : Nom de l'article
 *	@return bool False en cas d'échec
 */
function prd_products_get_by_barcode( $barcode ){
	global $config;

	if( !trim($barcode) ) return false;

	$barcode = str_replace( ' ', '', trim($barcode) );

	return ria_mysql_query('
		select prd_id as id, prd_ref as ref, prd_name as name
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null and prd_barcode=\''.addslashes($barcode).'\'
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère le titre du produit
 *	@param int $id Identifiant du produit à récupérer
 *	@return string Le titre du produit
 */
function prd_products_get_title( $id ){
	return prd_products_get_name( $id, true, true );
}
// \endcond

/** Cette fonction permet de savoir si un produit est nouveau ou non. Si le paramètre $prd_id correspond à un tableau, alors la fonction retourne True si au moins un article est nouveau
 *	@param int|array $prd_id Obligatoire, identifiant ou tableau d'identifiants de produits
 *	@return bool True l'article (ou au moins un article) est marqué comme nouveauté, False dans le cas contraire
 */
function prd_products_get_is_new( $prd_id ){
	global $config;

	$prd_id = control_array_integer($prd_id, true);
	if ($prd_id===false) {
		return false;
	}

	if( !isset($config['prd_new_days']) || !is_numeric($config['prd_new_days']) || $config['prd_new_days'] < 0 ){
		$config['prd_new_days'] = 60;
	}

	$col_new = isset($config['prd_new_date']) && in_array($config['prd_new_date'], array('prd_date_created', 'prd_first_published', 'prd_date_published')) ? $config['prd_new_date'] : 'prd_date_created';

	$res = ria_mysql_query('
		select 1
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id in ('.implode(', ', $prd_id).')
			and prd_date_deleted is null
			and prd_new!="-1" and if( '.$config['prd_new_days'].' != 0, (prd_new="1" or datediff(now(),'.$col_new.')<='.$config['prd_new_days'].'), prd_new="1")
	');

	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}

// \cond onlyria
/** Détermine le nom d'un article
 *	@param int $id Identifiant de l'article
 *	@param bool $shop_title Facultatif. Si True, le titre surchargé riaShop est retourné
 *	@param bool $no_name Facultatif. Si True et $shop_title True, l'information retournée est le titre même s'il est vide ou NULL
 *	@return string Le nom du produit, False en cas d'erreur
 */
function prd_products_get_name( $id, $shop_title=false, $no_name=false ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$col = 'prd_name';
	if( $shop_title ){
		$col = 'if(ifnull(prd_title,"")="", prd_name, prd_title)';
		if( $no_name ){
			$col = 'prd_title';
		}
	}

	$r = ria_mysql_query('
		select '.$col.'
		from prd_products
		where prd_id='.$id.' and prd_date_deleted is null and prd_tnt_id='.$config['tnt_id'].'
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);
}
// \endcond

/** Cette fonction retourne l'identifiant d'un ou plusieurs produits suivant leur référence
 *	@param string $ref Référence ou tableau de références (insensible(s) à la casse)
 *	@param int $catroot Optionnel, identifiant de catégorie racine. Valeur par défaut : 0
 *	@return int|array Identifiant ou tableau associatif d'identifiants ( ref=>id ). False en cas d'erreur
 */
function prd_products_get_id( $ref, $catroot = 0 ){
	global $config;

	$return_array = false;

	$refs = array();

	if( is_array($ref) ){
		$return_array = true;
		foreach( $ref as $r ){
			$r = trim($r);
			if( $r=='' ) return false;
			$refs[] = addslashes(strtoupper($r));
		}
	}else{
		$ref = trim($ref);
		if( $ref=='' ) return false;
		$refs[] = addslashes(strtoupper($ref));
	}

	$sql = '
		select prd_id as id, prd_ref as ref
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
			and upper(prd_ref) in (\''.implode( '\',\'',$refs ).'\')
			and prd_date_deleted is null
	';

	if( is_numeric($catroot) && $catroot > 0 ){
		$sql .= '
		and exists (
			select 1 from prd_classify
			where cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = prd_id
			and (
				cly_cat_id in (
					select cat_child_id from prd_cat_hierarchy
					where cat_tnt_id = '.$config['tnt_id'].' and cat_parent_id = '.$catroot.'
				) or cly_cat_id = '.$catroot.'
			)
		)
		';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' '.mysql_error().' '.$sql);
		}
		return false;
	}

	if( $return_array ){
		$arrayp = array();

		while( $prd = ria_mysql_fetch_array($res) )
			$arrayp[$prd['ref']] = $prd['id'];

		return $arrayp;
	}

	return ria_mysql_result( $res,0,0 );
}

/** Cette fonction retourne la référence du produit suivant l'identifiant
 *
 *	@param int $id Obligatoire, identifiant d'un produit
 *	@param bool $include_deleted Facultatif, si false ne retourne que les produits actifs (valeur par défaut). Si true, retourne également les produits supprimés.
 *
 *	@return string Retourne la référence du produit
 */
function prd_products_get_ref( $id, $include_deleted=false ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$sql = '
	select prd_ref
	from prd_products
	where prd_tnt_id='.$config['tnt_id'].'
	and prd_id='.$id.'
	';

	if( !$include_deleted ){
		$sql .= ' and prd_date_deleted is null';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res, 0, 0 );
}

// \cond onlyria
/** Récupère le nombre de ventes et le CA d'un ou plusieurs articles selon des paramètres optionnels
 *	@param int $prd Facultatif, identifiant d'article
 *	@param int $cat Facultatif, identifiant de catégorie
 *	@param $start Facultatif, date de début de prise en compte des ventes. La valeur par défaut utilise les ventes depuis l'origine
 *	@param $end Facultatif, date de fin de prise en compte des ventes. La valeur par défaut stoppe le calcul à la date du jour
 *	@param bool $is_active Facultatif, Si true et $prd non précisé, ne prend en charge que les articles publiés, commandables, non en sommeil et ayant une image principale
 *	@param $start_row Facultatif, Si supérieur à 0, le résultat est retourné à partir de la ligne indiqué
 *	@param int $limit Facultatif, Nombre maximal de résultats retournés
 *	@param $orderby Facultatif, tableau associatif de tri. Les clés autorisés sont les mêmes que les champs retournés, la valeur est asc ou desc.
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant de l'article
 *		- sell-count : Nombre de ventes
 *		- sell-ca-ht : Montant total de CA généré HT
 *		- sell-ca-ttc : Montant total de CA généré TTC
 *	@return bool False en cas d'erreur
 */
function prd_products_get_sell( $prd=0, $cat=0, $start=false, $end=false, $is_active=false, $start_row=0, $limit=0, $orderby=array() ){
	global $config;

	if( !is_numeric($prd) || $prd<0 ) return false;
	if( !is_numeric($cat) || $cat<0 ) return false;

	if( $start!==false && !isdate($start) ) return false;
	if( $end!==false && !isdate($end) ) return false;

	$sql = '
	select
	p.prd_id as id,
	'.($config['use_decimal_qte'] ? 'sum( op.prd_qte )' : 'cast(sum( op.prd_qte ) as signed)' ).' as "sell-count",
	sum( op.prd_price_ht*op.prd_qte ) as "sell-ca-ht",
	sum( op.prd_price_ht*op.prd_tva_rate*op.prd_qte ) as "sell-ca-ttc"
	from
	prd_products as p
	join ord_products  as op
	on p.prd_id=op.prd_id
	join ord_orders as o
	on op.prd_ord_id=o.ord_id
	where
	p.prd_tnt_id='.$config['tnt_id'].' and
	op.prd_tnt_id='.$config['tnt_id'].' and
	o.ord_tnt_id='.$config['tnt_id'].' and
	o.ord_state_id in ( 3,4,5,6,7,8,11,24,25,'._STATE_INV_STORE.' ) and
	o.ord_masked=0 and
	o.ord_usr_id in (
	select
	usr_id
	from
	gu_users
	where
	usr_tnt_id='.$config['tnt_id'].' and
	usr_date_deleted is null and
	usr_prf_id in ('.PRF_CUSTOMER.', '.PRF_CUST_PRO.', '.PRF_RESELLER.')
	) and
	prd_date_deleted is null
	';
	if( $prd!=0 )
		$sql .= ' and p.prd_id='.$prd;
	elseif( $cat!=0 )
		$sql .= ' and p.prd_id in (
	select
	cly_prd_id
	from
	prd_classify
	join prd_categories
	on cly_cat_id=cat_id
	where
	cat_date_deleted is null and
	cat_id='.$cat.' and
	cat_tnt_id='.$config['tnt_id'].' and
	cly_tnt_id='.$config['tnt_id'].'
	join
	select
	cly_prd_id
	from
	prd_categories as c
	join prd_cat_hierarchy as ch
	on c.cat_id=ch.cat_parent_id
	join dbo.prd_classify
	on ch.cat_child_id=cly_cat_id
	where
	c.cat_date_deleted is null and
	c.cat_id='.$cat.' and
	c.cat_tnt_id='.$config['tnt_id'].' and
	cly_tnt_id='.$config['tnt_id'].' and
	ch.cat_tnt_id='.$config['tnt_id'].'
	)';
	if( $start!==false )
		$sql .= ' and o.ord_date>=\''.dateparse($start).'\'';
	if( $end!==false )
		$sql .= ' and o.ord_date<=\''.dateparse($end).'\'';
	if( $is_active )
		$sql .= ' and prd_orderable and not prd_sleep and prd_publish and prd_img_id is not null';

	$sql .= '
	group by p.prd_id
	';
	if( is_array($orderby) && sizeof($orderby)>0 ){
		$ord_sql = array();

		foreach( $orderby as $k=>$v ){
			switch( $k ){
				case 'id':
				case 'sell-count';
				case 'sell-ca-ht';
				case 'sell-ca-ttc';
				$ord_sql[] = $k.' '.( $v!='desc' ? 'asc' : 'desc' );
				break;
			}
		}

		if( sizeof($ord_sql)>0 )
			$sql .= ' order by '.implode( ', ',$ord_sql);
	}
	$sql .= ' limit '.( is_numeric($start_row) && $start_row>0 ? $start_row : '0' ).',';
	$sql .= is_numeric($limit) && $limit>0 ? $limit : '18446744073709551615';

	return ria_mysql_query( $sql );
}
// \endcond

/** Détermine le tarif d'un produit dans un contexte donné
 *	@param int $prd Obligatoire, identifiant de l'article
 *	@param int $usr Facultatif, identifiant du client pour lequel on souhaite connaitre le tarif
 *	@param int $prc Facultatif, identifiant d'une catégorie tarifaire pour laquelle on souhaite connaitre le tarif (si usr non spécifié).
 *	@param int $ord Facultatif, identifiant d'un panier sur lequel une éxonération de TVA peut s'appliquer. Si ce paramètre est précisé, la validité de $usr sera vérifiée
 *	@param int $qte Facultatif, quantité du produit lors de la mise en panier. Si ce paramètre est précisé, il faut être vigiliant à la quantité éventuellement déjà mise en panier
 *	@param int $col Facultatif, identifiant d'un conditionnement sur lequel un ou des tarifs spécifiques sont appliqués
 *	@param bool $tva_only Optionnel, permet de ne calculer que la TVA
 *	@param bool $no_multiple Optionnel, si activé, la quantité conditionnée ne multiplie pas la quantité standard. A utiliser pour connaitre le prix de base d'un produit conditionné en dehors d'une remise sur la quantité.
 *
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- price_ht : Tarif HT du produit (sauf si $tva_only)
 *		- tva_rate : Taux de TVA du produit
 *		- price_ttc : Tarif TTC du produit (sauf si $tva_only)
 *	@return bool False en cas d'erreur
 */
function prd_products_get_price( $prd, $usr=0, $prc=0, $ord=0, $qte=1, $col=0, $tva_only=false, $no_multiple=false ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($usr) || $usr<0 ) return false;
	if( !is_numeric($prc) || $prc<0 ) return false;
	if( !is_numeric($ord) || $ord<0 ) return false;
	if( !is_numeric($qte) || $qte<1 ) return false;
	if( !is_numeric($col) || $col<0 ) return false;

	// Hack Chadog
	if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
		// Détermine le code du client
		$user_id = 0;
		if( $usr > 0 ){
			$user_id = $usr;
		}elseif( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] > 0 ){
			$user_id = $_SESSION['admin_view_user'];
		}elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] > 0 ){
			$user_id = $_SESSION['usr_id'];
		}

		if( !isset($user_id) ){
			return false;
		}

		$prc = gu_users_get_prc( $user_id, true );
		$cac = gu_users_get_cac( $user_id );

		if( !$cac ){
			$cac = 0;
		}

		// Si un panier et en cours et que le pays de livraison est en France alors on repasse sur le régime normal au niveau
		// de la catégorie comptable
		if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] > 0 ){
			$tmp_res = ria_mysql_query('
				select 1
				from ord_orders
					join gu_adresses on (adr_tnt_id = '.$config['tnt_id'].' and adr_id = ord_adr_delivery)
				where ord_tnt_id = '.$config['tnt_id'].'
					and ord_id = '.$_SESSION['ord_id'].'
					and ( upper(adr_cnt_code) = "FR" or upper(adr_country) = "FRANCE" )
			');

			if( $tmp_res && ria_mysql_num_rows($tmp_res) ){
				$cac = 1;
			}
		}

		$sql = '
			select
				prc_value as price_ht,
				ptv_tva_rate as tva_rate,
				(prc_value * ptv_tva_rate) as price_ttc
			from prc_prices
				left join
					prc_tvas on (
						ptv_date_deleted is null
						and ptv_tnt_id = '.$config['tnt_id'].'
						and ptv_prd_id = '.$prd.'
						and ifnull(ptv_cnt_code, "FR") = "FR"
						and ifnull(ptv_cac_id, 0) in (0, '.$cac.')
					)
			where prc_tnt_id = '.$config['tnt_id'].'
				and prc_is_deleted = 0
				and prc_date_start <= now() and prc_date_end > now()
				and prc_prd_id = '.$prd.'
				and exists (
					select 1
					from prc_price_conditions
					where ppc_prc_id = prc_id
						and ppc_tnt_id = '.$config['tnt_id'].'
						and ppc_fld_id = '._FLD_USR_PRC.'
						and ppc_value = '.$prc.'
				)
				and prc_qte_min = 1
				and prc_is_promotion = 0
			order by ifnull(ptv_cac_id, 0) desc
			limit 1
		';

		return ria_mysql_query( $sql );
	}

	if($config['tnt_id'] == 588 || $config['tnt_id'] == 1279){
		$user = isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] > 0 ? $_SESSION['usr_id'] : 0;
		$user = $usr > 0 ? $usr : $user;

		if($ord > 0){
			$rord = ria_mysql_query('
				select
					ord_id	as id,
					ord_usr_id as usr_id
				from
					ord_orders
				where
						ord_tnt_id='.$config['tnt_id'].'
					and ord_id='.$ord.'
			');

			if( ria_mysql_num_rows($rord) ){
				$ord = ria_mysql_fetch_assoc($rord);
				$user = $ord['usr_id'];
			}
		}
		$prc = gu_users_get_prc( $user, true );
		$cnt_code = gu_users_get_cnt_code($user);
		$cnt_code = str_replace( '"', '', $cnt_code );
		$sql_cac = '';

		if( $user > 0 ){
			$cac = gu_users_get_cac( $user );
			$sql_cac = 'and ifnull(ptv_cac_id, 0) in (0, ' . $cac . ')';
		}

		$sql = '
			select r.*
			from (
				(
					select
						prc_value as price_ht,
						ifnull(ptv_tva_rate, ' . _TVA_RATE_DEFAULT . ') as tva_rate,
						(prc_value * ifnull(ptv_tva_rate, ' . _TVA_RATE_DEFAULT . ')) as price_ttc
					from
						prc_prices
					left join
						prc_tvas
					on
							ptv_date_deleted is null
						and ptv_tnt_id=' . $config['tnt_id'] . '
						and ptv_prd_id=' . $prd . '
						and ptv_cnt_code = "'.htmlspecialchars($cnt_code).'"
						' . $sql_cac . '
					inner join
						prc_price_conditions
					on
							ppc_prc_id=prc_id
						and ppc_tnt_id=' . $config['tnt_id'] . '
						and ppc_fld_id = ' . _FLD_USR_PRC . '
						and ppc_value = ' . $prc . '
					where
							prc_is_deleted=0
						and prc_tnt_id=' . $config['tnt_id'] . '
						and ( prc_date_start<=now() ) and ( prc_date_end>now() )
						and prc_prd_id=' . $prd . '
						and prc_qte_min=1
						and prc_is_promotion=0
					order by prc_type_id asc, prc_value asc
					limit 1
				)
				union
				(
					select
					0 as price_ht,
					' . _TVA_RATE_DEFAULT . ' as tva_rate,
					0 as price_ttc
				)
				limit 1
			) r
		';

		return ria_mysql_query($sql);

	}

	if( $ord>0 ){
		require_once('orders.inc.php');

		$rord = ord_orders_get_masked( $ord );
		if( $rord===false || !ria_mysql_num_rows($rord) ) return false;
		$ord_array = ria_mysql_fetch_array($rord);

		// contrôle que le client de la commande et le client spécifié sont les mêmes
		if( $usr && $ord_array['user'] && $ord_array['user']!=$usr ) return false;
	}

	$usr = !isset($_SESSION['usr_tnt_id']) || (isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']>0) ? $usr : 0;

	$exempt = gu_users_is_tva_exempt( $usr );

	if( $tva_only ){
		$sql = '
		select
		'.( $exempt ? '1' : 'get_tva( '.$config['tnt_id'].', '.$prd.', '.gu_users_get_accouting_category( $usr, true ).', '.gu_users_get_cnt_code($usr, true).' )' ).' as tva_rate
		';
	}else{

		$usr_holder = gu_users_get_prices_holder( $usr );
		if( $usr_holder || !$prc ){
			$prc = gu_users_get_prc( $usr_holder, true );
		}
		$usr_ref = gu_users_get_ref( $usr_holder, true );

		// multiplicateur
		if( $col && !$no_multiple ){
			$rcol = prd_colisage_types_get( $col );
			if( $rcol && ria_mysql_num_rows($rcol) ){
				$col_qte = ria_mysql_result($rcol, 0, 'qte');
				if( $col_qte > 0 ){
					$qte *= $col_qte;
				}
			}
		}

		$apply_remise_ecotaxe = 0;
		if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
			$apply_remise_ecotaxe = prd_products_get_ecotaxe( $prd );

			if( !is_numeric($apply_remise_ecotaxe) || $apply_remise_ecotaxe <= 0 ){
				$apply_remise_ecotaxe = 0;
			}
		}

		$sql = '
		select d_tbl.*, d_tbl.price_ht * d_tbl.tva_rate as price_ttc from (
		select get_price_ht( '.$config['tnt_id'].', '.$prd.', '.$usr_holder.', '.$qte.', '.$col.', '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", -1, NULL, '.$apply_remise_ecotaxe.' ) as price_ht,
		'.( $exempt ? '1' : 'get_tva( '.$config['tnt_id'].', '.$prd.', '.gu_users_get_accouting_category( $usr, true ).', '.gu_users_get_cnt_code($usr, true, $ord).' )' ).' as tva_rate
		) as d_tbl
		';

	}

	$r = ria_mysql_query( $sql );

	// débugage tva
	if( ria_mysql_errno() ){
		error_log('prd_products_get_price - '.mysql_error().' - '.$sql);
	}

	return $r;
}

/** Détermine le tarif d'un produit parent dans un contexte donné
 *	@param int $prd Obligatoire, identifiant de l'article parent
 *	@param int $usr Facultatif, identifiant du client pour lequel on souhaite connaitre le tarif
 *	@param int $prc Facultatif, identifiant d'une catégorie tarifaire pour laquelle on souhaite connaitre le tarif (si usr non spécifié).
 *	@param int $ord Facultatif, identifiant d'un panier sur lequel une éxonération de TVA peut s'appliquer. Si ce paramètre est précisé, la validité de $usr sera vérifiée
 *	@param int $qte Facultatif, quantité du produit lors de la mise en panier. Si ce paramètre est précisé, il faut être vigiliant à la quantité éventuellement déjà mise en panier
 *	@param int $col Facultatif, identifiant d'un conditonnement sur lequel un ou des tarifs spécifiques sont appliqués
 *	@param bool $no_multiple Optionnel, si activé, la quantité conditionnée ne multiplie pas la quantité standard. A utiliser pour connaitre le prix de base d'un produit conditionné en dehors d'une remise sur la quantité.
 *	@param bool $publish Optionnel par défaut à false, mettre true pour tenir compte uniquement des articles enfants publiés
 *
 *	@return array Un tableau contenant les informations suivante
 *		- child_id : identifiant de l'article enfant pour lequel le prix est le plus petit
 *		- price_ht : Tarif HT du produit
 *		- tva_rate : Taux de TVA du produit
 *		- price_ttc : Tarif TTC du produit
 *	@return bool False en cas d'erreur
 */
function prd_products_get_parent_price( $prd, $usr=0, $prc=0, $ord=0, $qte=1, $col=0, $no_multiple=false, $publish=false ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}
	if( !is_numeric($usr) || $usr<0 ){
		return false;
	}
	if( !is_numeric($prc) || $prc<0 ){
		return false;
	}
	if( !is_numeric($ord) || $ord<0 ){
		return false;
	}
	if( !is_numeric($qte) || $qte<1 ){
		return false;
	}
	if( !is_numeric($col) || $col<0 ){
		return false;
	}

	if( $ord>0 ){
		require_once('orders.inc.php');

		$rord = ord_orders_get_masked( $ord );
		if( $rord===false || !ria_mysql_num_rows($rord) ){
			return false;
		}

		$ord_array = ria_mysql_fetch_array($rord);

		// contrôle que le client de la commande et le client spécifié sont les mêmes
		if( $usr && $ord_array['user'] && $ord_array['user']!=$usr ){
			return false;
		}
	}

	$usr = !isset($_SESSION['usr_tnt_id']) || (isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']>0) ? $usr : 0;

	// Le client est-il exonéré de TVA ?
	$exempt = gu_users_is_tva_exempt( $usr );

	$usr_holder = gu_users_get_prices_holder( $usr );
	if( $usr_holder || !$prc ){
		$prc = gu_users_get_prc( $usr_holder, true );
	}
	$usr_ref = gu_users_get_ref( $usr_holder, true );

	// multiplicateur
	if( $col && !$no_multiple ){
		$rcol = prd_colisage_types_get( $col );
		if( $rcol && ria_mysql_num_rows($rcol) ){
			$col_qte = ria_mysql_result($rcol, 0, 'qte');
			if( $col_qte > 0 ){
				$qte *= $col_qte;
			}
		}
	}

	$apply_remise_ecotaxe = 0;
	if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
		$apply_remise_ecotaxe = prd_products_get_ecotaxe( $prd );

		if( !is_numeric($apply_remise_ecotaxe) || $apply_remise_ecotaxe <= 0 ){
			$apply_remise_ecotaxe = 0;
		}
	}

	$r_child_price = ria_mysql_query('
		select d_tbl.*, d_tbl.price_ht * d_tbl.tva_rate as price_ttc
		from (
			select prd_child_id as child_id, get_price_ht( '.$config['tnt_id'].', prd_child_id, '.$usr_holder.', '.$qte.', '.$col.', '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", -1, NULL, '.$apply_remise_ecotaxe.' ) as price_ht,
			'.( $exempt ? '1' : 'get_tva( '.$config['tnt_id'].', prd_child_id, '.gu_users_get_accouting_category( $usr, true ).', '.gu_users_get_cnt_code($usr, true).' )' ).' as tva_rate
			from prd_hierarchy
			where prd_tnt_id = '.$config['tnt_id'].'
				and prd_parent_id = '.$prd.'
				and exists (
					select 1
					from prd_products
					where prd_tnt_id = '.$config['tnt_id'].'
						and prd_id = prd_child_id
						and prd_date_deleted is null
						'.( $publish ? ' and prd_publish = 1' : '' ).'
				)
		) as d_tbl
	');

	if( !$r_child_price || !ria_mysql_num_rows($r_child_price) ){
		return false;
	}

	$tmp = null;
	while( $child_price = ria_mysql_fetch_assoc($r_child_price) ){
		if( is_null($tmp) || $tmp['price_ht'] > $child_price['price_ht'] ){
			$tmp = $child_price;
		}
	}

	return $tmp;
}

// \cond onlyria
/** Détermine le tarif d'un produit dans un contexte donné, en utilisant un cache de 15 minutes
 *	@param int $prd Obligatoire, identifiant de l'article
 *	@param int $usr Facultatif, identifiant du client pour lequel on souhaite connaitre le tarif
 *	@param int $prc Facultatif, identifiant d'une catégorie tarifaire pour laquelle on souhaite connaitre le tarif. Si $usr est précisé, ce paramètre ne sera pas utilisé. Si aucun des deux paramètre ne sont précisés, la catégorie tarifaire par défaut sera utilisée
 *	@param int $ord Facultatif, identifiant d'un panier sur lequel une éxonération de TVA peut s'appliquer. Si ce paramètre est précisé, la validité de $usr sera vérifiée
 *	@param int $qte Facultatif, quantité du produit lors de la mise en panier. Si ce paramètre est précisé, il faut être vigiliant à la quantité éventuellement déjà mise en panier
 *	@param int $col Facultatif, identifiant d'un conditonnement sur lequel un ou des tarifs spécifiques sont appliqués
 *	@param bool $tva_only Optionnel, permet de ne calculer que la TVA
 *	@param bool $no_multiple Optionnel, si activé, la quantité conditionnée ne multiplie pas la quantité standard. A utiliser pour connaitre le prix de base d'un produit conditionné en dehors d'une remise sur la quantité.
 *	@return array Un tableau associatif comprenant les champs suivants :
 *		- price_ht : Tarif HT du produit (sauf si $tva_only)
 *		- tva_rate : Taux de TVA du produit
 *		- price_ttc : Tarif TTC du produit (sauf si $tva_only)
 *	@return bool False en cas d'erreur
 */
function prd_products_get_price_array( $prd, $usr=0, $prc=0, $ord=0, $qte=1, $col=0, $tva_only=false, $no_multiple=false ){
	global $config, $memcached;

	if(
		!is_numeric($prd)
		|| !is_numeric($usr)
		|| !is_numeric($prc)
		|| !is_numeric($ord)
		|| !is_numeric($qte)
		|| !is_numeric($col)
	){
		return false;
	}

	$key_memcached  = $config['tnt_id'].':'.$config['wst_id'].'::'.':'.$prd.':'.$usr.':'.$prc.':'.$ord.':'.$qte.':'.$col;
	$key_memcached .= ':'.( $tva_only ? 'tva_only' : 'none' );
	$key_memcached .= ':'.( $no_multiple ? 'no_multiple' : 'none' );

	if( !isset($_GET['force_cache']) && ($get = $memcached->get($key_memcached)) ){
		return $get == 'none' ? false : $get;
	}

	$price = false;
	if( $rprice = prd_products_get_price( $prd, $usr, $prc, $ord, $qte, $col, $tva_only, $no_multiple ) ){
		if( ria_mysql_num_rows($rprice) ){
			$price = ria_mysql_fetch_array( $rprice );
		}
	}

	$memcached->set( $key_memcached, ($price === false ? 'none' : $price), 60 * 60 );
	return $price;
}
// \endcond

// \cond onlyria
/** Détermine le tarif minimum d'un produit parent dans un contexte donné
 *	@param int $prd Obligatoire, identifiant de l'article parent
 *	@param int $usr Facultatif, identifiant du client pour lequel on souhaite connaitre le tarif
 *	@param int $prc Facultatif, identifiant d'une catégorie tarifaire pour laquelle on souhaite connaitre le tarif (si usr non spécifié).
 *	@param int $ord Facultatif, identifiant d'un panier sur lequel une éxonération de TVA peut s'appliquer. Si ce paramètre est précisé, la validité de $usr sera vérifiée
 *	@param int $qte Facultatif, quantité du produit lors de la mise en panier. Si ce paramètre est précisé, il faut être vigiliant à la quantité éventuellement déjà mise en panier
 *	@param int $col Facultatif, identifiant d'un conditonnement sur lequel un ou des tarifs spécifiques sont appliqués
 *	@param bool $publish_prd Optionnel, filtre uniquement les produits dont le prd_publish est True (ou False).
 *
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- price_ht : Tarif HT du produit
 *		- tva_rate : Taux de TVA du produit
 *		- price_ttc : Tarif TTC du produit
 *		- child_id : identifiant du produit pour lequel le prix est le plus bas
 *	@return bool False en cas d'erreur
 */
function prd_products_get_child_min_price( $prd, $usr=0, $prc=0, $ord=0, $qte=1, $col=0, $publish_prd=null ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($usr) || $usr<0 ) return false;
	if( !is_numeric($prc) || $prc<0 ) return false;
	if( !is_numeric($ord) || $ord<0 ) return false;
	if( !is_numeric($qte) || $qte<1 ) return false;
	if( !is_numeric($col) || $col<0 ) return false;

	if( $ord>0 ){
		require_once('orders.inc.php');

		$rord = ord_orders_get( 0, $ord );
		if( $rord===false || !ria_mysql_num_rows($rord) ) return false;
		$ord_array = ria_mysql_fetch_array($rord);

		// contrôle que le client de la commande et le client spécifié sont les mêmes
		if( $usr && $ord_array['user'] && $ord_array['user']!=$usr ) return false;
	}


	if( $config['prd_deposits']=='use-main' ){
		$dps = prd_deposits_get_main();
	}else{
		$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
	}

	$usr = !isset($_SESSION['usr_tnt_id']) || (isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']>0) ? $usr : 0;

	$exempt = gu_users_is_tva_exempt( $usr );
	$usr_holder = gu_users_get_prices_holder( $usr );
	if( $usr_holder || !$prc ){
		$prc = gu_users_get_prc( $usr_holder, true );
	}
	$usr_ref = gu_users_get_ref( $usr_holder, true );

	// multiplicateur
	if( $col ){
		$rcol = prd_colisage_types_get( $col );
		if( $rcol && ria_mysql_num_rows($rcol) ){
			$col_qte = ria_mysql_result($rcol, 0, 'qte');
			if( $col_qte > 0 ){
				$qte *= $col_qte;
			}
		}
	}

	$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

	$sql = 	'
	select distinct prd.prd_id as id,prd.prd_weight_net as weight_net, get_price_ht( '.$config['tnt_id'].', prd.prd_id, '.$usr_holder.', '.$qte.', '.$col.', '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", prd.prd_brd_id, prd.prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'prd.prd_ecotaxe').' ) as price_ht, '.( $exempt ? '1' : 'get_tva( '.$config['tnt_id'].', prd.prd_id, '.gu_users_get_accouting_category( $usr, true ).', '.gu_users_get_cnt_code($usr, true).' )' ).' as tva_rate, prd_child_id as child_id
	from prd_hierarchy as hry
	inner join prd_products as prd on (prd_child_id=prd.prd_id and prd.prd_tnt_id='.$config['tnt_id'].' and prd_parent_id='.$prd.')
	where prd.prd_orderable=1 and (prd.prd_sleep=0 or (prd.prd_sleep=1 and exists (
	select 1
	from prd_stocks
	where sto_prd_id=prd.prd_id
	and sto_dps_id='.$dps.'
	and ' . prd_stocks_get_sql() . '-sto_prepa>0
	and sto_tnt_id='.$config['tnt_id'].'
	and sto_is_deleted=0
	)
	))
	and prd_date_deleted is null
	';
	if( $publish_prd!==null ){
		$sql .= ' and prd_publish = '.( $publish_prd ? '1' : '0' );
	}

	$r = ria_mysql_query('
		select d_tbl.*, (d_tbl.price_ht * d_tbl.tva_rate) as price_ttc, d_tbl.price_ht as price_ht, d_tbl.tva_rate as tva_rate
		from ('.$sql.') as d_tbl
		order by price_ttc
		limit 0,1
	');

	// débugage tva
	if( ria_mysql_errno() ){
		error_log('prd_products_get_child_min_price - '.mysql_error().' - '.$sql);
	}

	return $r;
}
// \endcond

// \cond onlyria
/** Détermine le stock dispo pour les produits enfants
 *	@param int $prd Obligatoire, identifiant de l'article parent
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- child_qte : quantité en stock des produits enfants
 *	@return bool False en cas d'erreur
 */
function prd_products_get_childs_stock( $prd ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	if( $config['prd_deposits']=='use-main' ){
		$dps = prd_deposits_get_main();
	}else{
		$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
	}

	$res = ria_mysql_query('
		select ifnull('.($config['use_decimal_qte'] ? 'sum(' . prd_stocks_get_sql() . '-sto_prepa)' : 'cast(sum(' . prd_stocks_get_sql() . '-sto_prepa) as signed)' ).', 0) as child_qte
		from prd_stocks
			join prd_hierarchy on (sto_tnt_id=prd_tnt_id and sto_prd_id=prd_child_id)
		where sto_tnt_id='.$config['tnt_id'].'
			and prd_parent_id='.$prd.'
			and sto_dps_id='.$dps.'
			and sto_is_deleted=0
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		if( ria_mysql_errno() )
			error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );
		return false;
	}

	return ria_mysql_result( $res, 0, 'child_qte' );
}
// \endcond

// \cond onlyria
/** Détermine le stock dispo pour les produits enfants
 *	@param int $prd Obligatoire, identifiant de l'article parent
 *	@return bool true si ou moins un des articles enfants est nouveau, false dans le cas contraire
 */
function prd_products_get_childs_new( $prd ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	$col_new = isset($config['prd_new_date']) && in_array($config['prd_new_date'], array('prd_date_created', 'prd_first_published', 'prd_date_published')) ? $config['prd_new_date'] : 'prd_date_created';

	$res = ria_mysql_query('
		select 1
		from prd_products as p
			join prd_hierarchy as h on (p.prd_tnt_id = h.prd_tnt_id and prd_id = prd_child_id)
		where p.prd_tnt_id = '.$config['tnt_id'].'
			and prd_parent_id='.$prd.'
			and prd_date_deleted is null
			and prd_new != "-1"
			and if( '.$config['prd_new_days'].' != 0, (prd_new="1" or datediff(now(),'.$col_new.')<='.$config['prd_new_days'].'), prd_new="1" )
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Détermine la date de livraison la plus proche des produtis enfants
 *	@param int $prd Obligatoire, identifiant de l'article parent
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- stock : quantité en stock des produits enfants
 *	@return bool False en cas d'erreur
 */
function prd_products_get_childs_min_date_livr( $prd ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select prd_stock_livr
		from prd_products as p
			join prd_hierarchy as h on (p.prd_tnt_id=h.prd_tnt_id and p.prd_id=prd_child_id)
		where p.prd_tnt_id=25
			and prd_parent_id='.$prd.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'prd_stock_livr' );
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère le tarif minimal des enfants d'un produit parent. Par défaut, ce tarif prend en compte les promotions.
 *	@param int $parent Obligatoire, identifiant du produit parent. Si le produit n'est pas un parent, False sera retourné.
 *	@param $ref_like Optionnel, permet de spécifier un masque de référence ; seuls les enfants qui respectent ce masque seront controlés.
 *	@param bool $check_promo Optionnel, active ou non la vérification des promotions.
 *	@param bool $ht Optionnel, détermine si on s'intéresse au prix HT (True) ou TTC (False).
 *	@param bool $publish_prd Optionnel, filtre uniquement les produits enfants dont le prd_publish est à True (ou à False).
 *	@param null|bool $have_stock Optionnel, par défaut à null, ignoré, mettre true pour n'avoir que les articles enfants en stocks, false pour tous les autres
 *
 *	@return int Le tarif minimal des enfants, 0 si aucun n'a été trouvé.
 */
function prd_products_get_child_min_price_with_promo( $parent, $ref_like=false, $check_promo=true, $ht=false, $publish_prd=null, $have_stock=null ){

	if( !is_numeric($parent) || $parent<=0 ){
		return false;
	}elseif( !prd_products_is_parent( $parent ) ){
		// si le produit n'est pas parent, on ne charge pas la liste des enfants
		return 0;
	}

	$prd_ids = array();

	// les références des produits enfants devront commencer par le masque $ref_like
	if( trim($ref_like) ){
		if( $rprd = prd_products_get_byref( $ref_like, true ) ){
			while( $prd = ria_mysql_fetch_assoc($rprd) ){
				$prd_ids[] = $prd['id'];
			}
		}
	}

	$pref = $ht ? 'ht' : 'ttc';
	$min_price = 0;
	$is_set = false;

	// charge tous les enfants commandables
	$rchilds = prd_products_get_simple( 0, '', false, 0, false, false, true, false, array('childs' => true, 'is_orderable' => true, 'parent' => $parent, 'publish_prd' => $publish_prd, 'have_stock' => $have_stock) );
	if( $rchilds ){
		while( $c = ria_mysql_fetch_assoc($rchilds) ){
			// l'enfant ne fait pas partie du filtre par référence
			if( sizeof($prd_ids) && !in_array($c['id'], $prd_ids) ){
				continue;
			}

			// chargement de la promotion si nécessaire
			if( $check_promo ){
				$promo = prc_promotions_get( $c['id'], -1, 0, 1, 0, array('price_ht' => $c['price_ht'], 'tva_rate' => $c['tva_rate']) );
				if( is_array($promo) && sizeof($promo) ){
					$c['price_'.$pref] = $promo['price_'.$pref];
				}
			}

			if( !$is_set || $min_price > $c['price_'.$pref] ){
				$min_price = $c['price_'.$pref];
				$is_set = true;
			}
		}
	}

	return $min_price;

}
// \endcond

// \cond onlyria
/**	Cette fonction est un alias de prd_products_get() listant les produits :
 *		- commandables
 *		- actifs ou encore en stock (déstockage)
 *		- publiés dans la gestion
 *	Les prix des produits ne sont pas retournés. Les produits "enfant seulement" sont retournés.
 *	@param bool $with_price Optionnel, indique si les prix doivent ou non être retournés.
 *	@param bool $is_sync Optionnel, si spécifié, ne retourne que les produits synchronisés.
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL dont les colonnes sont les mêmes que prd_products_get()
 */
function prd_products_get_orderable_ref( $with_price = false, $is_sync = false ){
	return prd_products_get_simple( 0, '', false, 0, false, false, $with_price, false, array('childs' => true, 'publish_prd' => true, 'orderable' => true, 'isSync' => $is_sync) );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant ou un tableau d'identifiants des produits parents d'un autre produit
 *	@param int $child Obligatoire, identifiant d'un produit enfant
 *	@param bool $first Optionnel, par défaut tous les identifiants des produits parents sont retournés, mettre true pour ne retourner que le premier parent
 *	@return bool False si le paramètre est omis ou faux, ou bien si le produit n'est pas enfant d'un autre
 *	@return int L'identifiant ou un tableau d'identifiants des produits parents selon le second paramètre
 */
function prd_products_get_parent( $child, $first=false ){
	global $config;

	if( !is_numeric($child) || $child<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select prd_parent_id as parent_id
		from prd_hierarchy as h
			join prd_products as p on (p.prd_tnt_id = h.prd_tnt_id and prd_id = prd_parent_id)
		where h.prd_tnt_id='.$config['tnt_id'].'
			and prd_child_id='.$child.'
			and prd_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	if( $first ){
		return ria_mysql_result( $res, 0, 'parent_id' );
	}
	$ar_parent = [];

	while( $r = ria_mysql_fetch_assoc($res) ){
		$ar_parent[] = $r['parent_id'];
	}

	return $ar_parent;
}
// \endcond

// \cond onlyria
// A ne pas mettre en ligne
function prd_products_get_all(){
	global $config;

	return ria_mysql_query('
		select prd_id as id, prd_ref as ref, prd_is_sync as is_sync
		from prd_products
		where prd_date_deleted is null and prd_tnt_id='.$config['tnt_id']
	);
}
// \endcond

// \cond onlyria
/** Cette fonction permet la mise à jour de la durée de garantie d'un article.
 *
 *	@param int $id Obligatoire, identifiant du produit
 *  @param $garantie Obligatoire, durée de garantie en nombre de mois
 *
 *  @return bool true en cas de succès
 *  @return bool false en cas d'échec
 */
function prd_products_set_garantie( $id, $garantie ){
	global $config;

	if (!is_numeric($id) || $id <= 0) {
		return false;
	}
	if( !is_numeric($garantie) || $garantie<0 ) return false;

	return ria_mysql_query('update prd_products set prd_garantie='.$garantie.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$id);
}
// \endcond

/**	Cette fonction permet le chargement de la durée de garantie d'un produit donné.
 *	@param int $prd Obligatoire, identifiant du produit à interroger
 *	@return la durée de garantie, ou false en cas d'échec
 */
function prd_products_get_garantie( $prd ){
	global $config;

	if (!is_numeric($prd) || $prd <= 0) {
		return false;
	}

	$rprd = ria_mysql_query('select prd_garantie as garantie from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd.' and prd_date_deleted is null');
	if( !ria_mysql_num_rows($rprd) ) return false;

	$prd = ria_mysql_fetch_array($rprd);
	return $prd['garantie'];
}

// \cond onlyria
/** Récupère la liste complète des articles synchronisés avec la gestion commerciale
 *	@param bool $sleep Optionnel, condition de filtrage sur la mise en sommeil
 *	@param bool $publish Optionnel, condition de filtrage sur la publication
 *	@param bool $publish_cat Optionnel, condition de filtrage sur la publication de la catégorie du produit
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- id : Identifiant du produit
 *		- ref : Référence du produit
 */
function prd_products_get_synchronized( $sleep=null, $publish=null, $publish_cat=null ){
	global $config;

	$sql = '
	select
	prd_id as id,
	prd_ref as ref,
	prd_publish as publish,
	prd_publish_cat as publish_cat
	from
	prd_products
	where
	prd_date_deleted is null
	and prd_tnt_id='.$config['tnt_id'].'
	and prd_is_sync=1
	';

	if( $sleep!==null )
		$sql .= ' and prd_sleep='.( $sleep ? '1' : '0' );
	if( $publish!==null )
		$sql .= ' and prd_publish='.( $publish ? '1' : '0' );
	if( $publish_cat!==null )
		$sql .= ' and prd_publish_cat='.( $publish_cat ? '1' : '0' );

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction recalcule le prd_publish_cat d'un produit ou de tous les produits
 *	@param int $prd_id Optionnel, si précisé, seul le produit est recalculé, sinon tous les produits sont recalculés
 *	@param $print_result Optionnel, détermine si un message print est généré à chaque étape (pour utilisation en tant que tools ou cron)
 *	@param $no_action Optionnel, permet de simuler une mise à jour sans changement
 */
function prd_products_reset_publish_cat( $prd_id=0, $print_result=true, $no_action=false ){
	global $config;

	$sql = 'select prd_id as id, prd_ref as ref, prd_publish_cat as old_publish from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null';
	if( is_numeric($prd_id) && $prd_id>0 ){
		$sql .= ' and prd_id='.$prd_id;
	}

	if( $products = ria_mysql_query( $sql ) ){
		while( $p = ria_mysql_fetch_array($products) ){
			$publish = prd_products_cat_published($p['id']);

			if( $publish!=$p['old_publish'] ){
				if( $print_result ){
					print $p['ref'].' : '.$publish."\n";
				}

				if( !$no_action ){
					try{
						// Index le produit dans le moteur de recherche.
						RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_HIGH, array(
							'cls_id' => CLS_PRODUCT,
							'obj_id_0' => $p['id'],
						));
					}catch(Exception $e){
						error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
					}

					ria_mysql_query('update prd_products set prd_publish_cat='.( $publish ? 1 : 0 ).' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$p['id']);

					if( $publish ){
						prd_products_rebuild_urls($p['id']);
					}
				}
			}
		}
	}
}
// \endcond

/**	Cette fonction détermine si un produit est une nomenclature fixe RiaShop et, le cas échéant, le type de prix de cette nomenclature
 *	Un produit est une nomenclature à partir du moment où :
 *		- il existe au moins un enfant (non supprimé) dans "prd_nomenclatures_products"
 *		- le produit de base n'est pas de type "Articles liés" (4 / NM_TYP_LINKED) ou si la variable de configuration "prd_linked_nomenclature_activate" n'est pas activée
 *	@param int $id Obligatoire, identifiant du produit à tester
 *	@param $get_price_type Optionnel, détermine le type de test (false pour tester s'il s'agit d'une nomenclature, true pour tester le type de calcul du prix)
 *
 *	@return bool True ou False si $get_price_type est False, suivant que le produit soit ou non une nomenclature
 *	@return 1, 0, ou -1, si $get_price_type est True, signifiant respectivement :
 *		1 : le produit est une nomenclature et il existe un prix spécifique pour cette nomenclature
 *		0 : le produit n'est pas une nomenclature, ou une erreur est survenue
 *		-1 : le produit est une nomenclature, mais son prix est la somme des composants
 */
function prd_products_is_nomenclature( $id, $get_price_type = false ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}
	$rp = prd_products_get_simple( $id );
	if( !$rp || !ria_mysql_num_rows($rp) ){
		return $get_price_type ? 0 : false;
	}
	$p = ria_mysql_fetch_assoc($rp);

	if( $config['prd_linked_nomenclature_activate'] && $p['nomenclature_type'] == NM_TYP_LINKED ){
		return $get_price_type ? 0 : false;
	}

	$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

	$r = ria_mysql_query('
		select
		'.( $get_price_type ? 'get_price_ht( pnp_tnt_id, '.$id.', 0, 1, 0, '.$config['discount_apply_type'].', '.$config['default_prc_id'].', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "", prd_brd_id, prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'prd_ecotaxe').' )' : '1' ).'
		from prd_nomenclatures_products
			join prd_products on pnp_tnt_id = prd_tnt_id and pnp_child_id = prd_id
		where pnp_tnt_id='.$config['tnt_id'].'
			and pnp_parent_id='.$id.'
			and prd_date_deleted is null
		limit 0, 1
	');

	if( !$get_price_type ){
		return $r && ria_mysql_num_rows($r);
	}

	if( !$r || !ria_mysql_num_rows($r) ){
		return 0;
	}

	$have_price = ria_mysql_result($r, 0, 0);

	return $have_price ? 1 : -1;
}

// \cond onlyria
/**	Cette fonction met à jour le prix d'achat moyen pondéré d'un produit
 *	@param int $prd_id Obligatoire, Identifiant du produit
 *	@param float $purchase Obligatoire, Prix d'achat moyen pondéré
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_products_set_purchase_avg( $prd_id, $purchase ){
	global $config;

	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}

	$purchase = str_replace( array(',', ' '), array('.', ''), $purchase );
	if( !is_numeric($purchase) ) return false;

	return ria_mysql_query('
		update prd_products
			set prd_purchase_avg='.round( $purchase, 2 ).'
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$prd_id
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère le prix d'achat moyen pondéré d'un produit, et d'autres informations liées le cas échéant.
 *	Le tarif HT utilisé est celui de la catégorie tarifaire par défaut pour une quantité de 1
 *	@param int|array $prd_id Obligatoire, identifiant d'un produit ou tableau d'identifiants de produits
 *	@param bool $get_marge Facultatif, si True, la marge HT sera calculée pour chaque article, et le résultat sera trié par marge décroissante
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : Identifiant du produit
 *		- purchase_avg : Prix d'achat moyen pondéré
 *		- marge : marge HT actuelle, si demandée
 */
function prd_products_get_purchase_avg( $prd_id, $get_marge=false ){
	if( is_array($prd_id) ){
		if( !sizeof($prd_id) ) return false;
		foreach( $prd_id as $p ){
			if (!is_numeric($p) || $p <= 0) {
				return false;
			}
		}
	}else{
		if (!is_numeric($prd_id) || $prd_id <= 0) {
			return false;
		}

		$prd_id = array( $prd_id );
	}

	global $config;

	$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

	$sql = 'select prd_id as id, prd_purchase_avg as purchase_avg ';
	if( $get_marge ){
		$sql .= ', get_price_ht(
		'.$config['tnt_id'].', prd_id, 0, 1, 0, '.$config['discount_apply_type'].', '.$config['default_prc_id'].', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "", prd_brd_id, prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'prd_ecotaxe').'
		) as price_ht ';
	}
	$sql .= '
	from prd_products
	where prd_tnt_id='.$config['tnt_id'].'
	and prd_id in ('.implode( ', ', $prd_id ).')
	';

	if( $get_marge ){
		$sql = '
		select tmp.id, tmp.purchase_avg, (tmp.price_ht-tmp.purchase_avg)/tmp.purchase_avg*100 as marge
		from ('.$sql.') as tmp
		order by (tmp.price_ht-tmp.purchase_avg)/tmp.purchase_avg*100 desc
		';
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de récupérer une liste de produits parents à partir d'une liste d'enfants et une catégorie donnée (les enfants qui n'ont pas de parent dans la catégorie sont ainsi exclus). Si le produit en entrée n'appartient pas à un système parent / enfants (produit simple), on vérifie seulement s'il appartient à la catégorie.
 *	@param int|array $childs Obligatoire, tableau d'identifiants de produits enfants, ou identifiant d'un produit enfant unique
 *	@param int $cat Obligatoire, identifiant de la catégorie dans laquelle les parents doivent être classés
 *	@param bool $recursive_cat Optionnel, détermine si les sous-catégories sont prises en compte. Par défaut c'est le cas
 *	@param bool $respect_orderby Optionnel, détermine si le tri des éléments de $childs est respecté dans le tableau des parents en sortie. False par défaut.
 *
 *	@return bool False en cas d'échec
 *	@return array Un tableau des identifiants de produits parents
 */
function prd_products_get_parent_in_cat( $childs, $cat, $recursive_cat=true, $respect_orderby=false ){
	if( is_array($childs) ){
		foreach( $childs as $c ){
			if( !is_numeric($c) || $c <= 0 ){
				return false;
			}
		}
	}else{
		if( !is_numeric($childs) || $childs <= 0 ){
			return false;
		}
		$childs = array($childs);
	}

	if( !is_numeric($cat) || $cat <= 0 ){
		return false;
	}

	$imp = implode(',', $childs);

	global $config;

	$sql = '
	select distinct prd_id as id
	from prd_products as p
	join prd_classify on prd_id = cly_prd_id and prd_tnt_id = cly_tnt_id
	';
	if( $recursive_cat ){
		$sql .= '
		left join prd_cat_hierarchy on cly_tnt_id = cat_tnt_id and cly_cat_id = cat_child_id
		';
	}
	$sql .= '
	where prd_tnt_id = '.$config['tnt_id'].' and prd_date_deleted is null and (
	(
	prd_childonly = 0 and prd_id in ('.$imp.')
	) or (
	(
	prd_childonly = 1 or prd_id not in ('.$imp.')
	) and prd_id in (
	select prd_parent_id from prd_hierarchy as h
	where h.prd_tnt_id = p.prd_tnt_id and prd_child_id in ('.$imp.')
	)
	)
	)
	';
	if( $recursive_cat ){
		$sql .= '
		and (
		cly_cat_id = '.$cat.' or cat_parent_id = '.$cat.'
		)
		';
	}else{
		$sql .= ' and cly_cat_id = '.$cat;
	}

	if( $respect_orderby && sizeof($childs) > 1 ){
		$sql .= '
		order by
		case when prd_childonly = 0 and prd_id in ('.$imp.') then
		FIND_IN_SET(prd_id, "'.$imp.'")
		else
			FIND_IN_SET(prd_id, (
		select group_concat(prd_parent_id) from prd_hierarchy as h
		where h.prd_tnt_id = p.prd_tnt_id and prd_child_id in ('.$imp.')
		order by FIND_IN_SET(prd_child_id, "'.$imp.'")
		))
		end
		';
	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
		return false;
	}

	$products = array();

	while( $p = ria_mysql_fetch_array($r) ){
		$products[] = $p['id'];
	}

	return $products;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de récupérer unitairement l'information de publication.
 *	@param int $id Obligatoire, identifiant du produit.
 *	@param bool $get_publish_cat Optionnel, spécifie si on s'intéresse à la publication, ou la publication dans la catégorie.
 *	@return bool True si le produit est publié, False sinon.
 */
function prd_products_get_publish( $id, $get_publish_cat=false ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$r = ria_mysql_query('
		select '.( $get_publish_cat ? 'prd_publish_cat' : 'prd_publish' ).'
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$id.' and prd_date_deleted is null
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le champ avancé statique "Catégories tarifaires hors-remises" d'un produit.
 *	@param int $prd_id Identifiant du produit.
 *	@param int|array $cat_id Identifiant d'une catégorie tarifaire ou tableau d'identifiants (peut être un tableau vide ou 0 pour l'action "set").
 *	@param string $action_type Type d'action :
 *		- add : ajoute la ou les catégories données à la liste déjà en place.
 *		- del : supprime les catégories données de la liste déjà en place.
 *		- set : supprime la liste existante et la remplace par la ou les catégories données.
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_products_set_no_discount_prc( $prd_id, $cat_id, $action_type ){
	if (!is_numeric($prd_id) || $prd_id <= 0) {
		return false;
	}

	$cat_id = control_array_integer( $cat_id, false );
	if( !is_array($cat_id) ){
		return false;
	}

	$action_type = strtolower(trim($action_type));

	// les autres actions que "set" n'autorisent pas l'absence de catégories
	if( $action_type != 'set' && !sizeof($cat_id) ){
		return false;
	}

	$current = array();
	if( $action_type == 'add' || $action_type == 'del' ){

		// récupère la valeur actuelle
		$current = fld_object_values_get( $prd_id, _FLD_PRD_NO_DISCOUNT, '', false, true );
		if( trim($current) ){
			$current = control_array_integer( explode(', ', $current) );
			if( $current === false ){
				return false;
			}
		}else{
			$current = array();
		}

		if( $action_type == 'add' ){
			// ajoute les éléments de "$cat_id" à "$current"
			foreach( $cat_id as $one_id ){
				if( !in_array($one_id, $current) ){
					$current[] = $one_id;
				}
			}
		}else{
			// on ne conserve que les éléments de "$current" qui ne sont pas dans "$cat_id"
			$new_current = array();
			foreach( $current as $one_id ){
				if( !in_array($one_id, $cat_id) && !in_array($one_id, $new_current) ){
					$new_current[] = $one_id;
				}
			}
			$current = $new_current;
		}

	}elseif( $action_type == 'set' ){
		// écrase la valeur
		$current = $cat_id;
	}else{
		return false;
	}

	return fld_object_values_set( $prd_id, _FLD_PRD_NO_DISCOUNT, implode(', ', $current) );

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour l'unité de vente d'un ou plusieurs produits, donnés via leur identifiant ou par leur ancienne valeur d'unité de vente.
 *	@param int $prd Obligatoire, identifiant du produit, ou tableau d'identifiants de produits. 0 est autorisé si $old_sun est valide et différent de la valeur par défaut.
 *	@param int $sun Obligatoire, identifiant de la nouvelle unité de vente. Spécifiez Null pour ne plus rattacher le produit à une unité.
 *	@param int $old_sun Optionnel, identifiant de l'ancienne unité de vente du ou des produits. Peut prendre trois types de valeurs :
 *		- Null pour filtrer les produits dont l'unité n'est pas spécifiée.
 *		- 0 pour ne pas filtrer.
 *		- Un entier supérieur à 0 pour filtrer sur une unité spécifique.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_products_set_sell_unit( $prd, $sun, $old_sun=0 ){

	$prd = control_array_integer( $prd, false );
	if( $prd === false ){
		return false;
	}

	if( $sun !== null && !prd_sell_units_exists( $sun ) ){
		return false;
	}

	if( $old_sun !== null ){
		if( !is_numeric($old_sun) || $old_sun < 0 ){
			return false;
		}
	}

	if( !sizeof($prd) && $old_sun === 0 ){
		return false;
	}

	global $config;

	$sql = '
	update prd_products
	set
	prd_sun_id = '.( $sun ? $sun : 'NULL' ).'
	where
	prd_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($prd) ){
		$sql .= ' and prd_id in ('.implode(', ', $prd).')';
	}

	if( $old_sun === null ){
		$sql .= ' and prd_sun_id is null';
	}elseif( $old_sun ){
		$sql .= ' and prd_sun_id = '.$old_sun;
	}

	return ria_mysql_query($sql);

}
// \endcond

/**	Cette fonction récupère l'identifiant de l'unité de vente d'un produit donné. Le produit ne doit pas être supprimé virtuellement.
 *	@param int $prd Obligatoire, identifiant du produit.
 *	@return int L'identifiant de l'unité de vente du produit, ou False en cas d'échec.
 */
function prd_products_get_sell_unit( $prd ){
	global $config;

	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select prd_sun_id as id
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prd.'
			and prd_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$data = ria_mysql_fetch_assoc($res);
	return $data['id'];

}

// \cond onlyria
/**	Cette fonction récupère des produits qui sont compris dans l'historique de commandes d'un client, filtrés selon des critères optionnels.
 *	@param int $usr_id Obligatoire, identifiant du client.
 *	@param int|array $brd_id Optionnel, identifiant ou tableau d'identifiants de marques.
 *	@param int|array $cat_id Optionnel, identifiant ou tableau d'identifiants de catégories.
 *	@param bool $catchilds Optionnel, détermine si le paramètre $cat permet une recherche dans les sous-catégories.
 *	@param $inc_invoices Optionnel, détermine si les factures sans commande d'origine doivent être prises en compte.
 *	@param $fields Optionnel, permet de filtrer les lignes de commande (et de facture si $inc_invoices activé) suivant la présence, ou la valeur, d'un ou plusieurs champs avancés.
 *		L'argument doit être spécifié sous la forme d'un tableau de tableaux associatifs, chaque sous-tableau comprenant :
 *			- id : l'identifiant du champ avancé (classe "ligne de commande").
 *			- [inv_id] : l'identifiant du champ avancé (classe "ligne de facture"), si $inc_invoices est activé.
 *			- [value] : valeur de filtrage optionnelle.
 *	@param bool $publish Optionnel, détermine si les produits publiés, non publiés ou les deux doivent être retournés.
 *	@param bool $orderable Optionnel, détermine si les produits commandables, non commandables ou les deux doivent être retournés.
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du produit.
 *		- ref : référence du produit.
 *		- name : nom du produit.
 *		- title : titre du produit.
 *		- brd_id : identifiant de la marque du produit.
 *		- sun_name : nom de l'unité de vente
 */
function prd_products_get_from_history( $usr_id, $brd_id=0, $cat_id=0, $catchilds=false, $inc_invoices=false, $fields=false, $publish=null, $orderable=null ){

	if( !gu_users_exists( $usr_id ) ){
		return false;
	}

	$brd_id = control_array_integer( $brd_id, false );
	if( $brd_id === false ){
		return false;
	}

	$cat_id = control_array_integer( $cat_id, false );
	if( $cat_id === false ){
		return false;
	}

	if( !is_array($fields) ){
		$fields = array();
	}

	foreach( $fields as $fld_data ){
		if( !is_array($fld_data) ){
			return false;
		}
		if( !isset($fld_data['id']) || !is_numeric($fld_data['id']) || $fld_data['id'] <= 0 ){
			return false;
		}
		if( $inc_invoices && ( !isset($fld_data['inv_id']) || !is_numeric($fld_data['inv_id']) || $fld_data['inv_id'] <= 0 ) ){
			return false;
		}
	}

	global $config;

	$dps = prd_deposits_get_main();
	if( $config['prd_deposits'] == 'use-customer' ){
		$rusr = gu_users_get( $usr_id );
		if( $rusr && ria_mysql_num_rows($rusr) ){
			$dps = ria_mysql_result($rusr, 0, 'dps_id');
		}
	}

	$sql_inv = '';

	$sql = '
	select
	distinct p.prd_id as "id", p.prd_ref as "ref", p.prd_name as "name",
	if(ifnull(p.prd_title, "") = "", p.prd_name, p.prd_title) as "title",
		p.prd_brd_id as brd_id, sun_name
	from
	prd_products as p
	join ord_products as op on p.prd_tnt_id = op.prd_tnt_id and p.prd_id = op.prd_id
	join ord_orders on op.prd_tnt_id = ord_tnt_id and op.prd_ord_id = ord_id
	left join prd_sell_units on p.prd_sun_id = sun_id and p.prd_tnt_id = sun_tnt_id
	';
	if( $inc_invoices ){
		$sql_inv = '
		select
		distinct p.prd_id as "id", p.prd_ref as "ref", p.prd_name as "name",
		if(ifnull(p.prd_title, "") = "", p.prd_name, p.prd_title) as "title",
			p.prd_brd_id as brd_id, sun_name
		from
		prd_products as p
		join ord_inv_products as ip on p.prd_tnt_id = ip.prd_tnt_id and p.prd_id = ip.prd_id
		join ord_invoices on ip.prd_tnt_id = inv_tnt_id and ip.prd_inv_id = inv_id
		left join prd_sell_units on p.prd_sun_id = sun_id and p.prd_tnt_id = sun_tnt_id
		';
	}

	$i = 0;
	foreach( $fields as $fld_data ){
		$i++;
		$sql .= '
		join fld_object_values as v'.$i.' on
		op.prd_tnt_id = v'.$i.'.pv_tnt_id and op.prd_ord_id = v'.$i.'.pv_obj_id_0
		and op.prd_id = v'.$i.'.pv_obj_id_1 and op.prd_line_id = v'.$i.'.pv_obj_id_2
		and v'.$i.'.pv_lng_code = "'.$config['i18n_lng'].'" and v'.$i.'.pv_fld_id = '.$fld_data['id'].'
		';
		if( $inc_invoices && isset($fld_data['inv_id']) ){
			$sql_inv .= '
			join fld_object_values as v'.$i.' on
			ip.prd_tnt_id = v'.$i.'.pv_tnt_id and ip.prd_inv_id = v'.$i.'.pv_obj_id_0
			and ip.prd_id = v'.$i.'.pv_obj_id_1 and ip.prd_line_id = v'.$i.'.pv_obj_id_2
			and v'.$i.'.pv_lng_code = "'.$config['i18n_lng'].'" and v'.$i.'.pv_fld_id = '.$fld_data['inv_id'].'
			';
		}
	}

	$sql .= '
	where ord_tnt_id = '.$config['tnt_id'].'
	and ord_usr_id = '.$usr_id.'
	and ord_masked = 0
	and ord_state_id in ('.implode(', ', ord_states_get_ord_valid()).')
	';
	if( $inc_invoices ){
		$sql_inv .= '
		where inv_tnt_id = '.$config['tnt_id'].'
		and inv_usr_id = '.$usr_id.'
		and inv_masked = 0
		and prd_ord_id is null
		';
	}

	// clauses where communes à la requête de base et celle sur invoices
	$sql_where_more = ' and p.prd_date_deleted is null';

	$i = 0;
	foreach( $fields as $fld_data ){
		$i++;
		if( isset($fld_data['value']) && trim($fld_data['value']) ){
			$sql_where_more .= ' and v'.$i.'.pv_value = "'.addslashes($fld_data['value']).'"';
		}
	}

	if( $publish !== null ){
		$sql_where_more .= ' and p.prd_publish = '.( $publish ? '1' : '0' );
		$sql_where_more .= ' and p.prd_publish_cat = '.( $publish ? '1' : '0' );
		$sql_where_more .= ' and (
		p.prd_sleep = '.( $publish ? '0' : '1' ).' '.( $publish ? 'or' : 'and' ).' ifnull((
		select ' .prd_stocks_get_sql() . ' - sto_prepa from prd_stocks
		where sto_tnt_id = p.prd_tnt_id and sto_prd_id = p.prd_id
		and sto_dps_id = '.$dps.' and sto_is_deleted=0
		), 0) '.( $publish ? '>' : '<=' ).' 0
		)';
	}

	if( $orderable !== null ){
		$sql_where_more .= ' and p.prd_orderable = '.( $orderable ? '1' : '0' );
	}

	if( sizeof($brd_id) ){
		$sql_where_more .= ' and p.prd_brd_id in ('.implode(', ', $brd_id).')';
	}

	if( sizeof($cat_id) ){
		$sql_where_more .= ' and (';

		if( $catchilds ){
			$sql_where_more .= '
			exists (
			select 1 from prd_classify
			join prd_cat_hierarchy on cly_tnt_id = cat_tnt_id and cly_cat_id = cat_child_id
			where cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = p.prd_id
			and cat_parent_id in ('.implode(', ', $cat_id).')
			) or
			';
		}

		$sql_where_more .= ' exists (
		select 1 from prd_classify
		where cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = p.prd_id
		and cly_cat_id in ('.implode(', ', $cat_id).')
		)
		)';
	}

	$sql .= $sql_where_more;
	if( $inc_invoices ){
		$sql_inv .= $sql_where_more;
	}

	if( $inc_invoices ){
		$sql = '
		select tmp.id, tmp.ref, tmp.name, tmp.title, tmp.brd_id
		from (
		'.$sql.' union '.$sql_inv.'
		) as tmp
		';
	}

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('prd_products_get_from_history , erreur SQL : '.mysql_error()."\n".$sql);
	}

	return $res;

}
// \endcond


// \cond onlyria
/** cette fonction permet de modifier la référence d'un produit
 *	@param int $prd_id Identifiant du produit
 *	@param string $ref Nouvelle référence du produit
 *	@param bool $is_sync Facultatif, détermine si on surcharge pour modifier la référence d'un produit syncronisé, par defaut false, true pour surcharger
 *
 *	@return bool True si la modification c'est bien réaliser, false si échec
 */
function prd_products_set_ref( $prd_id, $ref, $is_sync=false ){
	global $config;

	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}
	if( !is_string($ref) || trim($ref) == '' ){
		return false;
	}
	if( prd_products_get_is_sync($prd_id) && !$is_sync ){
		return false;
	}

	return ria_mysql_query('
		update prd_products
		set prd_ref = "'.addslashes($ref).'"
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$prd_id.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de modifier l'identifiant de gescom a un produit
 *	@param int $prd_id Identifiant du produit
 *	@param string $ref_gescom Référence du produit dans la gescom
 *	@param bool $is_sync Facultatif, détermine si on surcharge pour modifier la référence de la gescom d'un produit syncronisé, par defaut false, true pour surcharger
 *
 *	@return bool True si la modification c'est bien réaliser, false si échec
 */
function prd_products_set_ref_gescom($prd_id, $ref_gescom, $is_sync=false){
	global $config;

	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}
	if( !is_string($ref_gescom) || trim($ref_gescom) == '' ){
		return false;
	}
	if( prd_products_get_is_sync($prd_id) && !$is_sync ){
		return false;
	}

	return ria_mysql_query('
		update prd_products
		set prd_ref_gescom = "'.addslashes($ref_gescom).'"
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$prd_id.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant d'un produit en fonction de son identifiant de gescom
 *	@param string $ref_gescom Identifiant gescom du produit
 *
 *	@return bool false si erreur sinon l'identifiant du produit
 */
function prd_products_get_by_ref_gescom( $ref_gescom ){
	global $config;

	if( trim($ref_gescom) == '' ){
		return false;
	}
	if( is_numeric($ref_gescom) && $ref_gescom <= 0 ){
		return false;
	}

	$r = ria_mysql_query('
		select prd_id as id
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_ref_gescom="'.addslashes($ref_gescom).'"
			and prd_date_deleted is null
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	$res = ria_mysql_fetch_assoc($r);
	return $res['id'];
}
/**
 * Cette fonction permet de récupérer la ref gescom d'un produit
 *
 * @param integer $prd_id Identifiant du produit
 * @return string|null Retourne la refgescom du produit sinon null
 */
function prd_products_get_refgescom($prd_id){
	global $config;

	if( !is_numeric($prd_id) && $prd_id <= 0 ){
		throw new InvalidArgumentException("prd_id doit être un entier positif");
	}

	$result = ria_mysql_query('
		select prd_ref_gescom as ref_gescom
		from prd_products
		where prd_tnt_id='.$config['tnt_id'].'
			and prd_id='.$prd_id.'
	');

	$ref = ria_mysql_result($result, 0, 'ref_gescom');

	return $ref ? $ref : null;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'url de redirection en cas d'indisponibilité du produit.
 * 	@param int $prd_id Obligatoire, Identifiant d'un produit
 *
 * 	@return string|bool L'url de redirection en cas de succès, false dans le cas contraire
 */
function prd_products_get_url_unreachable( $prd_id ){
	global $config;

	if( !is_numeric($prd_id) || $prd_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select prd_url_unreachable
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$prd_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'prd_url_unreachable' );
}
// \endcond


// \cond onlyria
/** Cette fonction permet de modifier l'url de redirection en cas d'indisponibilité du produit par une nouvelle url.
 * 	@param int $old_url Obligatoire, Ancienne url de redirection
 * 	@param int $new_url Obligatoire, Nouvelle url de redirection
 *
 * 	@return bool true en cas de succès, false dans le cas contraire
 */
function prd_products_change_url_unreachable( $old_url, $new_url ){
	global $config;


	if( !trim($old_url) || $old_url=='/' ){
		return false;
	}

	if( !trim($new_url) || $new_url=='/' ){
		return false;
	}

	$sql = '
	UPDATE prd_products
	SET prd_url_unreachable = \''.addslashes($new_url).'\'
	where prd_tnt_id='.$config['tnt_id'].'
	and prd_date_deleted is null
	and prd_url_unreachable=\''.addslashes($old_url).'\'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'url de redirection en cas d'indisponibilité du produit.
 * 	@param int $prd_id Obligatoire, Identifiant d'un produit
 * 	@param string $url Obligatoire, Url de redirection
 *
 * 	@return bool True en cas de succès, false dans le cas contraire
 */
function prd_products_set_url_unreachable( $prd_id, $url ){
	global $config;

	if( !is_numeric($prd_id)  || $prd_id <= 0 ){
		return false;
	}

	if( !is_string($url) ){
		return false;
	}

	return ria_mysql_query('
		update prd_products
		set prd_url_unreachable = "'.addslashes($url).'"
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$prd_id.'
	');
}
// \endcond

// \cond onlyria
/**
 * Cette fonction permet de savoir si le produit dispose d'un tarif négocié en fonction du client et du produit
 * @param integer $usr_id : identifiant du client pour lequel nous souhaitons effectué un tests, attention si 0 le résultat de cette fonction sera toujours false
 * @param integer $prd_id : identifiant du produit pour lequel on souhaite connaitre si le tarif est négocié
 * @return bool true dans le cas ou le tarifs est négocié, et false dans le cas contraire
 *
 * @throws InvalidArgumentException
 */
function prd_has_negotiated_price($usr_id, $prd_id) {
	global $config, $memcached;

	if (!is_numeric($usr_id) ||	$usr_id <= 0){
		throw new InvalidArgumentException("usr_id doit être un entier positif.");
	}
	if (!is_numeric($prd_id) || $prd_id <= 0){
		throw new InvalidArgumentException("prd_id doit être un entier positif.");
	}

	$cache_key = 'prd_is_negotiate:usr_id:'.$usr_id.':tnt_id:'.$config['tnt_id'];
	$prd_ids = array();

	if ( ($cached_prd_ids = $memcached->get($cache_key))) {
		$prd_ids = $cached_prd_ids;
	}else{
		$usr_ref = gu_users_get_ref($usr_id);
		$usr_central = fld_object_values_get($usr_id, _FLD_USR_CENTRAL,'',false,true);

		$res = ria_mysql_query("
			select prc_prd_id
			from prc_prices
			join prc_price_conditions on (prc_tnt_id = ppc_tnt_id and prc_id=ppc_prc_id)
			where prc_tnt_id = " . $config['tnt_id'] . "
				and	prc_date_start < now()
				and prc_date_end > now()
				and prc_prd_id is not null
				and (
						(
							ppc_fld_id=" . _FLD_USR_REF . "
							and ppc_value='" . addslashes($usr_ref) . "'
						)
						or
						(
							ppc_fld_id=" . _FLD_USR_CENTRAL . "
							and ppc_value='" . addslashes($usr_central) . "'
						)
					)
		");

		$prd_ids = array();
		if ($res && ria_mysql_num_rows($res)) {
			while ($prc = ria_mysql_fetch_assoc($res)) {
				$prd_ids[] = $prc['prc_prd_id'];
			}
			$memcached->set($cache_key,$prd_ids, 1000);
		}
	}


	return in_array($prd_id, $prd_ids );
}
// \endcond

/** Vérifie si le produit est soumis ou non à des frais de port spécifiques.
 *
 * @param  int $id Obligatoire, Identifiant du produit
 * @return bool true si le produit est soumis à des frais de port spécifiques, false autrement
 */
function prd_products_has_specific_shipping_charges( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select 1
		from prd_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$id.'
			and prd_has_specific_shipping_charges = 1
	');

	return $res && ria_mysql_num_rows($res);
}

/** Définie si le produit est soumis ou non à des frais de port spécifiques.
 *
 * @param  int $id Obligatoire, Identifiant du produit
 * @param  bool $has_specific_shipping_charges Obligatoire, Identifiant du produit
 * @return bool true si le produit est soumis à des frais de port spécifiques, false autrement
 */
function prd_products_set_specific_shipping_charges( $id, $has_specific_shipping_charges ){
	global $config;

	if( !is_numeric($id) || $id <= 0 || !is_bool($has_specific_shipping_charges) ){
		return false;
	}

	return ria_mysql_query('
		update prd_products
		set prd_has_specific_shipping_charges = '.($has_specific_shipping_charges ? 1 : 0).'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$id
	);

}

// \cond onlyria
/** @} */
// \endcond

/** @} */
