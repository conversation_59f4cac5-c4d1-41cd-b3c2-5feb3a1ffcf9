<?php

// Dépendances
require_once('db.inc.php');
require_once('gaparse.inc.php');
require_once('rewards.inc.php');
require_once('strings.inc.php');
require_once('i18n.inc.php');
require_once('define.inc.php');
require_once('prd.stocks.inc.php');
require_once('comparators.inc.php');
require_once('BigQuery.inc.php');
require_once('usr/hierarchy.inc.php');
// Sous-modules
if( isset($config) && $config['tnt_id']==4 ){
	require_once('ord/proloisirs.inc.php'); // Fonctions spécifiques à Proloisirs
}

/** \defgroup model_stats Statistiques
 *    Ce module comprend les fonctions nécessaires à la gestion des statistiques.
 *
 *    @todo Statistiques sur la création de comptes utilisateurs
 *    @todo Statistiques sur l'enregistrement de commandes
 *    @todo Statistiques sur l'annulation de commandes
 *    @todo Statistiques sur la newsletter
 *
 *    @{
 */

// \cond onlyria
/** Teste si des données statistiques sont disponibles pour une année donnée.
 *	@param int $year Année à tester
 *	@return bool true si des statistiques sont disponibles (même partielles)
 *	@return bool false si aucune statistique n'est disponible
 */
function stats_year_available($year){
	global $config;
	return ria_mysql_num_rows(ria_mysql_query(
		'select stat_datetime from stats_hourly
		where stat_tnt_id=' . $config['tnt_id'] . ' and stat_wst_id=' . $config['wst_id'] . ' and year(stat_datetime)=' . $year . '
		limit 0,1'
	)) > 0;
}
// \endcond

// \cond onlyria
/** Teste si des données statistiques sont disponibles pour un mois donnée.
 *	@param int $year Année à tester
 *	@param int $month Mois à tester
 *	@return bool true si des statistiques sont disponibles (même partielles)
 *	@return bool false si aucune statistique n'est disponible
 */
function stats_month_available($year, $month){
	global $config;
	return ria_mysql_num_rows(ria_mysql_query(
		'select stat_datetime from stats_hourly where stat_tnt_id=' . $config['tnt_id'] . ' and stat_wst_id=' . $config['wst_id'] . ' and year(stat_datetime)=' . $year . ' and month(stat_datetime)=' . $month . ' limit 0,1'
	)) > 0;
}
// \endcond

// \cond onlyria
/** Teste si des données statistiques sont disponibles pour le mois précédant.
 *	Le mois est attendu sous la forme yyyy-mm.
 *	@param string $month Mois, sous forme numérique (1=Janvier,...,12=Décembre)
 *	@return bool true si des statistiques sont disponibles (même partielles)
 *	@return bool false si aucune statistique n'est disponible
 */
function stats_previous_month_available($month){
	$prev_month = date('Y-m', strtotime('-1 month', strtotime($month . '-01')));
	$prev_year = preg_replace('/([0-9]{4})\-[0-9]{2}/', '\1', $prev_month);
	$prev_month = preg_replace('/[0-9]{4}\-([0-9]{2})/', '\1', $prev_month);

	return stats_month_available($prev_year, $prev_month);
}
// \endcond

// \cond onlyria
/** Teste si des données statistiques sont disponibles pour le mois précédant.
 *	Le mois est attendu sous la forme yyyy-mm.
 *	@param string $month Mois, sous forme numérique (1=Janvier,...,12=Décembre)
 *	@return bool true si des statistiques sont disponibles (même partielles)
 *	@return bool false si aucune statistique n'est disponible
 */
function stats_next_month_available($month){
	$next_month = date('Y-m', strtotime('+1 month', strtotime($month . '-01')));
	$next_year = preg_replace('/([0-9]{4})\-[0-9]{2}/', '\1', $next_month);
	$next_month = preg_replace('/[0-9]{4}\-([0-9]{2})/', '\1', $next_month);

	return stats_month_available($next_year, $next_month);
}
// \endcond

// \cond onlyria
/** La clé utilisée pour le stockage des statistiques est une date au format yyyy-mm-dd hh:00:00.
 *    Cette fonction retourne donc la date actuelle dans le format désiré.
 */
function stats_getdate(){
	return "'" . date('Y-m-d H:00:00') . "'";
}
// \endcond

// \cond onlyria
/** S'assure que les compteurs relatifs à une date/heure donnée pourront être incrémentés via un simple update.
 *    @param $d Date/Heure pour laquelle il faut s'assurer de la possibilité d'enregistrement (correctement parsée)
 *    @param int $wst_id Optionnel, permet de spécifier un autre site que celui de la configuration actuelle
 */
function stats_ensure_save($d, $wst_id = false){
	global $config;

	$wst_id = is_numeric($wst_id) && $wst_id > 0 ? $wst_id : $config['wst_id'];

	$sql = 'select 1 from stats_hourly where stat_tnt_id = ' . $config['tnt_id'] . ' and stat_wst_id = ' . $wst_id . ' and stat_datetime = ' . $d;
	$exist = ria_mysql_query($sql);
	if (!$exist || !ria_mysql_num_rows($exist)) {
		if (ria_mysql_errno()) {
			error_log(__FILE__ . ':' . __LINE__ . ' ' . mysql_error() . ' ' . $sql);
		}
		ria_mysql_query('insert into stats_hourly (stat_tnt_id,stat_wst_id,stat_datetime) values (' . $config['tnt_id'] . ',' . $wst_id . ',' . $d . ')');
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction teste la champ User-Agent du client pour déterminer s'il s'agit d'un robot.
 *	Cette fonction utilise la variable de session sys_is_bot pour sauvegarder son résultat et accélérer son fonctionnement
 *	@return bool true si le client est un robot connu ou si l'adresse ip fait partie d'un filtre à exclure des statistiques
 *	@return bool false dans le cas contraire
 */
function stats_is_bot(){

	// Si déjà identifié comme bot, les donnés d'identification ne sont pas réanalysées.
	if( isset($_SESSION['sys_is_bot']) && $_SESSION['sys_is_bot'] ){
		return true;
	}

	// Essaye de détecter le statut de bot à partir du User-Agent
	if( isset($_SERVER['HTTP_USER_AGENT']) ){
		$bots_ua = array(
			'200PleaseBot/', 'AcoonBot/', 'AdsBot-Google', 'AdsBot-Google-Mobile', 'AhrefsBot/', 'aiHitBot/', 'AlkalineBOT', 'ApacheBench/', 'Applebot', 'Apple-PubSub',
			'Ask Jeeves/Teoma', 'Baiduspider/', 'bingbot/', 'CareerBot/', 'CCBot/', 'CompSpyBot/', 'DotBot/', 'DoyoucheckBot', 'Exabot/', 'Flood/', 'FriendFeedBot/',
			'Gigabot', 'Google_Analytics_Snippet_Validator', 'Googlebot', 'GrapeshotCrawler/', 'gURLChecker', 'Go-http-client',
			'KaloogaBot/', 'KomodiaBot/', 'LinkChecker', 'Linkidator/', 'http://ltx71.com/', 'MJ12bot/', 'msnbot/', 'NCBot', 'oBot/', 'PaperLiBot/', 'Scooter/',
			'ScreenerBot', 'SemrushBot/', 'semvisuBot', 'SeznamBot/', 'ShopWiki/', 'SkimBot/', 'Sogou web spider/',
			'TweetmemeBot/', 'TwengaBot', 'UptimeRobot/', 'VoilaBot', 'VeBot', 'WASALive-Bot', 'Wget/',
			'Yahoo! Slurp', 'YandexBot/', 'YoudaoBot/', 'ZumBot/'
		);
		foreach( $bots_ua as $ua ){
			if( strstr($_SERVER['HTTP_USER_AGENT'], $ua ) ){
				$_SESSION['sys_is_bot'] = true;
				return true;
			}
		}

		if( $_SERVER['HTTP_USER_AGENT']=='ea' ){ // Détecté sur A&C
			$_SESSION['sys_is_bot'] = true;
			return true;
		}

		if( preg_match('/^Java\/.*$/', $_SERVER['HTTP_USER_AGENT']) ){
			$_SESSION['sys_is_bot'] = true;
			return true;
		}
	}

	// Exclu les erreurs 404 générés par des tests depuis l'interface d'administration. On pourrait ignorer tout simplement les erreurs 404
	// des administrateurs, ce qui permettrait d'éliminer cette variable.
	if( isset($_GET['testadminerror404']) && $_GET['testadminerror404'] ){
		$_SESSION['sys_is_bot'] = true;
		return true;
	}
	// Si les adresses ip des locaux clients sont renseignées, leurs erreurs 404 sont ignorées
	if( tnt_filters_is_detected() ){
		$_SESSION['sys_is_bot'] = true;
		return true;
	}

	// La liste d'URI ci-dessous ne sont pas appelés que par des bots à la recherche de failles de sécurité. Il n'est pas nécessaire qu'elles remontent dans le rapport
	// des erreurs 404 à nos utilisateurs, ils s'en plaignent (cf http://forge.riastudio.fr/issues/23161).
	$ignore_uris = array(
		'/wp-login.php', '/wp-config.php~', '/xmlrpc.php', '/blog/xmlrpc.php', '/wordpress/', '/wordpress/index.php', '/vcalendar/', '/wp-admin', '/wp-admin/', '/wp-admin/admin-ajax.php',
		'/wordpress/wp-admin/setup-config.php', '/old/wp-admin/setup-config.php', '/blog/wp-admin/setup-config.php', '/test/wp-admin/setup-config.php',
		'/wp/wp-admin/setup-config.php', '/wp-admin/setup-config.php', '/protal/wp-admin/setup-config.php', '/main/wp-admin/setup-config.php',
		'/cms/wp-admin/setup-config.php', '/press/wp-admin/setup-config.php', '/news/wp-admin/setup-config.php', '/wp-admin/admin.php',
		'/beta/wp-admin/setup-config.php', '/new/wp-admin/setup-config.php', '/blogs/wp-admin/setup-config.php', '/wp-admin/tools.php',
		'/site/wp-admin/setup-config.php', '/blog/beta/wp-admin/setup-config.php', '/wp/beta/wp-admin/setup-config.php', '/home/<USER>/setup-config.php',
		'/blog/wordpress/wp-admin/setup-config.php', '/wordpress/beta/wp-admin/setup-config.php', '/wp-content/plugins/fckeditor_for_wordpress/fckeditor',
		'/jm-ajax/upload_file', '/jm-ajax/upload_file/', '/bbs/utility/convert/index.php', '/convert/index.php', '/utility/convert/index.php',
		'/bbs/convert/index.php', '/plus/mytag_js.php', '/blog/robots.txt', '/database.php', '/libs.php', '/setup.php', '/logo_img.php', '/searchreplacedb2.php',
		'/backup/latest.backup.7z', '/latest.backup.7z', '/latest.backup.rar', '/backup/latest.backup.rar', '/backup/latest.backup.tar.gz', '/latest.backup.tar.gz',
		'/backup/latest.backup.zip', '/backup/latest.backup.tar', '/latest.backup.tar', '/latest.backup.zip', '/a2billing/admin/Public/index.php', '/unauth.cgi',
		'/zplug/ajax_asyn_link.old.php', '/sfx.php', '/tmp/sfx.php', '/nktt.php', '/libraries/joomla/web.php', '/libraries/joomla/css.php', '/libraries/joomla/wso.php',
		'/FCKeditor/editor/filemanager/connectors/php/connector.php', '/editor/editor/filemanager/browser/default/connectors/php/connector.php', '/FCKeditor/editor/filemanager/browser/default/connectors/php/connector.php',
		'/editor/editor/filemanager/connectors/php/connector.php', '/FCKeditor/editor/filemanager/browser/default/connectors/aspx/connector.aspx',
		'/FCKeditor/editor/filemanager/connectors/aspx/connector.aspx', '/editor/editor/filemanager/connectors/asp/connector.asp', '/editor/editor/filemanager/browser/default/connectors/asp/connector.asp', '/editor/editor/filemanager/browser/default/connectors/aspx/connector.aspx',
		'/editor/editor/filemanager/connectors/aspx/connector.aspx', '/FCKeditor/editor/filemanager/connectors/asp/connector.asp', '/FCKeditor/editor/filemanager/browser/default/connectors/asp/connector.asp', '/FCKeditor3/editor/filemanager/browser/default/connectors/aspx/connector.aspx',
		'/FCKeditor3/editor/filemanager/connectors/jsp/connector.jsp', '/FCKeditor3/editor/filemanager/connectors/asp/connector.asp',
		'/FCKeditor/editor/filemanager/connectors/jsp/connector.jsp', '/fckeditor/editor/filemanager/connectors/php/upload.php'
	);
	if( array_search( $_SERVER['REQUEST_URI'], $ignore_uris )!==false ){
		$_SESSION['sys_is_bot'] = true; // Ainsi, les requêtes suivantes seront automatiquement ignorées si le bot gère les cookies
		return true;
	}

	return false;
}
// \endcond

/** Cette fonction est à utiliser dans chaque page devant profiter des statistiques.
 *    Elle est chargée du décompte des hits et visites.
 *    Attention à ne pas l'inclure dans le fichier config, pour que les hits de
 *    l'interface d'administration ne soit loggés.
 */
function stats_save_request(){
	global $config;
	// Les requêtes effectués par les administrateurs ne sont pas prises en compte dans les statistiques.
	if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_ADMIN) return;

	// Les requêtes effectuées par des robots ne sont pas prises en compte
	if( stats_is_bot()) return;
	// Incrémente les compteurs d'accès pour la date et l'heure en cours
	$d = stats_getdate();
	stats_ensure_save($d);
	ria_mysql_query('update stats_hourly set stat_hits=stat_hits+1 where stat_tnt_id='.$config['tnt_id'].' and stat_wst_id='.$config['wst_id'].' and stat_datetime='.$d);
	if( !isset($_SESSION['stats_visit_saved']) ){
		$_SESSION['stats_visit_saved'] = ria_mysql_query('update stats_hourly set stat_visits=stat_visits+1 where stat_tnt_id='.$config['tnt_id'].' and stat_wst_id='.$config['wst_id'].' and stat_datetime='.$d);
	}
}

/** Cette fonction permet l'enregistrement d'une utilisation du formulaire de contact. Elle doit obligatoirement
 *    être employée après un appel à stats_save_request.
 */
function stats_add_contact(){
	// Les requêtes effectués par les administrateurs ne sont pas prises en compte dans les stats.
	if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_ADMIN) return;

	global $config;

	$d = stats_getdate();
	stats_ensure_save($d);
	ria_mysql_query('update stats_hourly set stat_contacts=stat_contacts+1 where stat_tnt_id=' . $config['tnt_id'] . ' and stat_wst_id=' . $config['wst_id'] . ' and stat_datetime=' . $d);
}

// \cond onlyria
/** Cette fonction permet l'enregistrement d'une nouvelle commande dans les statistiques. Elle doit obligatoirement
 *    être employée après un appel à stats_save_request.
 *
 *	@param float $total Total Hors Taxe de la commande
 *
 */
function stats_add_order_completed($total){
	// Les requêtes effectués par les administrateurs ne sont pas prises en compte dans les stats.
	if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_ADMIN) return;

	global $config;

	$d = stats_getdate();
	stats_ensure_save($d);
	ria_mysql_query('update stats_hourly set stat_orders_completed=stat_orders_completed+1, stat_orders_ca_completed=stat_orders_ca_completed+' . $total . ' where stat_tnt_id=' . $config['tnt_id'] . ' and stat_wst_id=' . $config['wst_id'] . ' and stat_datetime=' . $d);
}
// \endcond

// \cond onlyria
/** Cette fonction permet l'enregistrement d'une nouvelle commande annulée dans les statistiques.
 *    Elle doit obligatoirement être employée après un appel à stats_save_request.
 *
 *    @param $total Total Hors Taxe de la commande
 *
 */
function stats_add_order_canceled($total){
	// Les requêtes effectués par les administrateurs ne sont pas prises en compte dans les stats.
	if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_ADMIN) return;
	global $config;
	$d = stats_getdate();
	stats_ensure_save($d);
	ria_mysql_query('update stats_hourly set stat_orders_canceled=stat_orders_canceled+1, stat_orders_ca_canceled=stat_orders_ca_canceled+' . $total . ' where stat_tnt_id=' . $config['tnt_id'] . ' and stat_wst_id=' . $config['wst_id'] . ' and stat_datetime=' . $d);
}
// \endcond

/** Cette fonction permet l'enregistrement d'une utilisation de la foire aux questions. Elle doit obligatoirement
 *    être employée après un appel à stats_save_request.
 */
function stats_add_faq_access(){
	// Les requêtes effectués par les administrateurs ne sont pas prises en compte dans les stats.
	if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_ADMIN) return;
	// Les requêtes effectuées par des robots ne sont pas prises en compte
	if (stats_is_bot()) return;
	global $config;

	$d = stats_getdate();
	stats_ensure_save($d);
	ria_mysql_query('update stats_hourly set stat_faq_accesses=stat_faq_accesses+1 where stat_tnt_id=' . $config['tnt_id'] . ' and stat_wst_id=' . $config['wst_id'] . ' and stat_datetime=' . $d);
}

// \cond onlyria
/** Cette fonction permet l'enregistrement d'une nouvelle création de panier. Il est possible d'utiliser
 *    cette fonction sans appeler la fonction stats_save_request au préalable.
 */
function stats_add_cart_creation(){
	// Les requêtes effectués par les administrateurs ne sont pas prises en compte dans les stats.
	if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_ADMIN) return;
	global $config;
	$d = stats_getdate();
	stats_ensure_save($d);
	ria_mysql_query('update stats_hourly set stat_carts=stat_carts+1 where stat_tnt_id=' . $config['tnt_id'] . ' and stat_wst_id=' . $config['wst_id'] . ' and stat_datetime=' . $d);
}
// \endcond

/** Incrémente le nombre de visualisations d'une catégorie de produit.
 *
 *    Cette fonction consulte les variables de session $_SESSION['usr_id'] et $_SESSION['usr_prf_id']
 *    pour déterminer comment imputer les statistiques. Les statistiques des administrateurs ne sont
 *    jamais sauvegardées.
 *
 *    Si l'utilisateur n'est pas loggué, cette fonction conserve les visualisations dans une variable
 *    de session nommée $_SESSION['stats_categories_user']. La fonction de login devra déclencher l'enregistrement de ces
 *    statistiques et supprimer cette variable de session.
 *
 *    @param int $cat Identifiant de la catégorie de produits.
 *
 *    @return void
 *
 *    @deprecated Les résultats de cette fonction seront faussés par la suppression d'une catégorie.
 *
 */
function stats_prd_categories_add($cat){

	if (!is_numeric($cat) || $cat <= 0) return;
	if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_ADMIN) return;
	// Les requêtes effectuées par des robots ne sont pas prises en compte
	if (stats_is_bot()) return;
	global $config;

	// Statistiques de visualisation ciblées par utilisateur
	if( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) ){
		ria_mysql_query('insert into stats_prd_categories_users (stats_tnt_id, stats_wst_id, stats_cat_id,stats_usr_id,stats_date) values ('.$config['tnt_id'].','.$config['wst_id'].','.$cat.','.$_SESSION['usr_id'].',now())');
	}else{
		if( !isset($_SESSION['stats_categories_user']) ){
			$_SESSION['stats_categories_user'] = array();
		}
		// chaque hit est une ligne [ID produit / timestamp du hit]
		$_SESSION['stats_categories_user'][] = array( $cat, date('Y-m-d H:i:s') );
	}
}

/** Incrémente le nombre de visualisations d'un produit.
 *
 *    Cette fonction consulte les variables de session $_SESSION['usr_id'] et $_SESSION['usr_prf_id']
 *    pour déterminer comment imputer les statistiques. Les statistiques des administrateurs ne sont
 *    jamais sauvegardées.
 *
 *    Si l'utilisateur n'est pas loggué, cette fonction conserve les visualisations dans une variable
 *    de session nommée $_SESSION['stats_products']. La fonction de login devra déclencher l'enregistrement de ces
 *    statistiques et supprimer cette variable de session.
 *
 *    @param int $prd Identifiant du produit
 *
 *    @deprecated Les résultats de cette fonction seront faussés par la suppression d'un produit.
 */
function stats_prd_products_add($prd){
	if (!is_numeric($prd) || $prd <= 0) return;
	if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_ADMIN) return;
	// Les requêtes effectuées par des robots ne sont pas prises en compte
	if (stats_is_bot()) return;
	global $config;

	$d = stats_getdate();
	ria_mysql_query('insert into stats_prd_hourly (stat_tnt_id, stat_wst_id, stat_datetime,stat_prd_id) values (' . $config['tnt_id'] . ', ' . $config['wst_id'] . ',' . $d . ',' . $prd . ');');
	ria_mysql_query('update low_priority stats_prd_hourly set stat_prd_hits=stat_prd_hits+1 where stat_tnt_id=' . $config['tnt_id'] . ' and stat_wst_id=' . $config['wst_id'] . ' and stat_datetime=' . $d . ' and stat_prd_id=' . $prd);


	// Statistiques de visualisation ciblées
	if( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) ){
		ria_mysql_query('insert into stats_prd_products_users (stats_tnt_id, stats_wst_id, stats_prd_id,stats_usr_id,stats_date) values ('.$config['tnt_id'].','.$config['wst_id'].','.$prd.','.$_SESSION['usr_id'].',now())');
	}else{
		// Conserve les visualisations des produits jusqu'à ce que l'utilisateur se loggue
		if( !isset($_SESSION['stats_products']) ){
			$_SESSION['stats_products'] = array( $d=>array( $prd=> 1 ) );
		}elseif( !isset($_SESSION['stats_products'][$d]) ){
			$_SESSION['stats_products'][$d] = array( $prd=>1 );
		}elseif( !isset($_SESSION['stats_products'][$d][$prd]) ){
			$_SESSION['stats_products'][$d][$prd] = 1;
		}else{
			$_SESSION['stats_products'][$d][$prd] += 1;
		}
		if( !isset($_SESSION['stats_products_user']) ){
			$_SESSION['stats_products_user'] = array();
		}

		// chaque hit est une ligne [ID produit / timestamp du hit]
		array_unshift( $_SESSION['stats_products_user'], array( $prd, date('Y-m-d H:i:s') ));
	}
}

// \cond onlyria
/** Cette fonction est chargée d'enregistrer les statistiques de consultation ayant eu lieu avant que l'utilisateur
 *    ne se loggue. Pour les administrateurs, cette fonction effacera les statistiques anonymes de consultation.
 *    Pour les clients, cette fonction attachera les statistiques de consultation au compte, pour permettre de le profiler.
 */
function stats_prelogin_save(){
	global $config;

	// Statistisques globales sur les produits consultés
	if (isset($_SESSION['stats_products'])) {
		if (isset($_SESSION['usr_prf_id'])) {
			if ($_SESSION['usr_prf_id'] == PRF_ADMIN) {
				// Administrateur, supprime les statistiques
				while (list($d, $products) = each($_SESSION['stats_products']))
					while (list($prd, $hits) = each($products))
						ria_mysql_query('update low_priority stats_prd_hourly set stat_prd_hits=stat_prd_hits-' . $hits . ' where stat_tnt_id=' . $config['tnt_id'] . ' and stat_wst_id= ' . $config['wst_id'] . ' and stat_datetime=' . $d . ' and stat_prd_id=' . $prd);
			}
		}
	}

	// statistiques du client sur les produits consultés
	if (isset($_SESSION['stats_products_user']) && is_array($_SESSION['stats_products_user'])) {
		if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] != PRF_ADMIN) {
			foreach ($_SESSION['stats_products_user'] as $datas) {
				// élément 0 = id du produit, élément 1 = datetime du hit
				if (is_array($datas) && sizeof($datas) == 2 && is_numeric($datas[0]) && $datas[0] > 0 && isdateheure($datas[1])) {
					ria_mysql_query('
						insert into stats_prd_products_users
							(stats_tnt_id, stats_wst_id, stats_prd_id, stats_usr_id, stats_date)
						values
							(' . $config['tnt_id'] . ',' . $config['wst_id'] . ',' . $datas[0] . ',' . $_SESSION['usr_id'] . ',"' . dateheureparse($datas[1]) . '")
					');
				}
			}
		}
	}

	unset($_SESSION['stats_products']);
	unset($_SESSION['stats_products_user']);

	// statistiques du client sur les catégories consultées
	if (isset($_SESSION['stats_categories_user']) && is_array($_SESSION['stats_categories_user'])) {
		if (isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] != PRF_ADMIN) {
			foreach ($_SESSION['stats_categories_user'] as $datas) {
				// élément 0 = id du produit, élément 1 = datetime du hit
				if (is_array($datas) && sizeof($datas) == 2 && is_numeric($datas[0]) && $datas[0] > 0 && isdateheure($datas[1])) {
					ria_mysql_query('
						insert into stats_prd_categories_users
							(stats_tnt_id, stats_wst_id, stats_cat_id, stats_usr_id, stats_date)
						values
							(' . $config['tnt_id'] . ',' . $config['wst_id'] . ',' . $datas[0] . ',' . $_SESSION['usr_id'] . ',"' . dateheureparse($datas[1]) . '")
					');
				}
			}
		}
	}

	unset($_SESSION['stats_categories_user']);
}
// \endcond

// \cond onlyria
/** Cette fonction récupère les produits les plus consultés pour un client
 *    @param int $user Optionnel, identifiant du client
 *    @param int $wst Optionnel, identifiant d'un site en particulier (par défaut, tous les sites)
 *    @param int $limit Optionnel, nombre maximal de résultats retournés (pour compatibilité, le nombre par défaut est 5). Mettre -1 pour ne pas limiter
 *    @param int|array $exclude Optionnel, permet de donner un identifiant ou tableau d'identifiants de produit à exclure du résultat
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - id : identifiant du produit
 *        - name : nom du produit
 *        - views : nombre total de consultations
 */
function stats_user_products_get($user = 0, $wst = false, $limit = 5, $exclude = false){
	if (!is_numeric($user) || $user < 0) return false;
	if ($wst !== false && !wst_websites_exists($wst)) return false;
	if (!is_numeric($limit) || $limit == 0) return false;

	if ($exclude !== false) {
		if (!is_array($exclude)) {
			if (!is_numeric($exclude) || $exclude <= 0) {
				return false;
			}

			$exclude = array($exclude);
		} else {
			if (!sizeof($exclude)) {
				return false;
			}

			foreach ($exclude as $p) {
				if (!is_numeric($p) || $p <= 0) {
					return false;
				}
			}
		}
	}

	global $config;

	$subsql = '
		select s.stats_prd_id as id, count(*) as v
		from stats_prd_products_users as s
		where s.stats_tnt_id=' . $config['tnt_id'] . '
	';

	if ($user > 0) {
		$subsql .= ' and s.stats_usr_id=' . $user;
	}

	if ($wst !== false) {
		$subsql .= ' and s.stats_wst_id=' . $wst;
	}

	$subsql .= '
		group by s.stats_prd_id
		union all
		select h.stats_prd_id as id, h.stats_views as v
		from stats_prd_products_users_histo as h
		where h.stats_tnt_id=' . $config['tnt_id'] . '
	';

	if ($user > 0) {
		$subsql .= ' and h.stats_usr_id=' . $user;
	}

	if ($wst !== false) {
		$subsql .= ' and h.stats_wst_id=' . $wst;
	}

	$sql = '
		select prd_id as "id", if(ifnull(prd_title, "")="",prd_name,prd_title) as "name", sum(temp.v) as "views"
		from (' . $subsql . ') as temp, prd_products
		where prd_tnt_id=' . $config['tnt_id'] . ' and temp.id=prd_id and prd_date_deleted is null
	';

	if (is_array($exclude)) {
		$sql .= ' and prd_id not in (' . implode(', ', $exclude) . ')';
	}

	$sql .= '
		group by prd_id
		order by sum(temp.v) desc
	';
	if ($limit > 0)
		$sql .= ' limit 0, ' . $limit;

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Récupère les derniers produits consultés de la session en cours
 *    Si c'est un client connecté, on recherche dans la table SQL. Sinon, on recherche dans les informations de session
 *    @param int $limit Optionnel, nombre maximal de résultats
 *    @param bool $distinct Optionnel, si False, un même produit peut être retournés deux fois s'il a été consulté à deux dates différentes
 *    @param bool $publish Optionnel, détermine si les produits non publiés doivent être retournés
 *
 *	@return array Un tableau vide en cas d'échec
 *	@return array Un tableau de tableaux. Chaque sous-tableau est associatif et composé comme ceci :
 *		- id : Identifiant du produit
 *		- date : Date de dernière consultation
 *		- cat_id : Identifiant d'une catégorie dans laquelle le produit est classé. Si $publish, cette catégorie et sa hiérarchie sont publiés
 *	Les lignes du tableau sont triées par date de consultation de la plus récente à la plus ancienne
 */
function stats_user_products_get_last_consulted($limit = -1, $distinct = true, $publish = true){
	$ar_final = array();

	if( isset($_SESSION['usr_id']) ){
		if( !is_numeric($_SESSION['usr_id']) || $_SESSION['usr_id']<=0 ) return $ar_final;

		global $config;

		// on a besoin du dépôt pour être exhaustif dans la recherche des produits publiés
		if( $publish ){
			$dps = prd_deposits_get_main();
			if( $config['tnt_id']==2 && isset($_SESSION['usr_dps_id']) && is_numeric($_SESSION['usr_dps_id']) ){
				$dps = $_SESSION['usr_dps_id'];
			}
		}

		// interrogation du SQL

		$sql = '
			select prd_id as id, stats_date as date, ifnull((
				select cat_id from prd_classify
				join prd_categories as c on cly_cat_id=cat_id and cly_tnt_id=cat_tnt_id
				where cly_prd_id=prd_id and cly_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null
		';
		if ($publish) {
			$sql .= '
				and cat_publish=1 and not exists (
					select 1 from prd_cat_hierarchy as h
					join prd_categories as c2 on h.cat_tnt_id=c2.cat_tnt_id and h.cat_parent_id=c2.cat_id
					where h.cat_child_id=c.cat_id and h.cat_tnt_id='.$config['tnt_id'].' and c2.cat_publish!=1
				)
			';
		}
		$sql .= '
				limit 0, 1
			), 0) as cat_id
			from stats_prd_products_users as su
			join prd_products on stats_tnt_id=prd_tnt_id and stats_prd_id=prd_id
			where prd_date_deleted is null
		';
		if ($publish) {
			$sql .= '
				and prd_publish=1 and prd_publish_cat=1
				and (
					prd_sleep!=1 or ifnull((
						select ' . prd_stocks_get_sql() . '-sto_prepa from prd_stocks where sto_tnt_id='.$config['tnt_id'].'
						and sto_dps_id=' . $dps . ' and sto_prd_id=prd_id and sto_is_deleted=0
					), 0)>0
				)
			';
		}
		$sql .= '
			and stats_tnt_id=' . $config['tnt_id'] . ' and stats_wst_id=' . $config['wst_id'] . '
			and stats_usr_id=' . $_SESSION['usr_id'] . '
		';
		if ($distinct) {
			// on ne retourne pas les lignes qui concerne un même produit visité plus tard
			$sql .= '
				and not exists (
					select 1 from stats_prd_products_users as newer
					where newer.stats_tnt_id = '.$config['tnt_id'].'
					and newer.stats_wst_id = su.stats_wst_id
					and newer.stats_usr_id = su.stats_usr_id
					and newer.stats_prd_id = su.stats_prd_id
					and newer.stats_date > su.stats_date
				)
			';
		}
		$sql .= ' order by stats_date desc';
		if ($limit > 0)
			$sql .= ' limit 0, ' . $limit;

		if ($r = ria_mysql_query($sql)) {
			while ($row = ria_mysql_fetch_array($r)) {
				$ar_final[] = $row;
			}
		}

	} elseif (isset($_SESSION['stats_products_user']) && is_array($_SESSION['stats_products_user'])) {

		$all_prds = array(); // contient tous les produits parcourus, pour effectuer un distinct

		$i = 1;
		foreach ($_SESSION['stats_products_user'] as $stat_row) {

			// ligne du tableau de session invalide
			if( !is_array($stat_row) || sizeof($stat_row)<2 ){
				continue;
			}

			$prd_id = $stat_row[0];
			$date_view = $stat_row[1];

			// produit déjà parcouru
			if( $distinct && in_array($prd_id, $all_prds) ){
				continue;
			}

			$all_prds[] = $prd_id;

			// recherche de la catégorie dans laquelle le produit est classé (si le produit n'est pas classé, il n'est pas retourné)
			$rcat = prd_products_categories_get( $prd_id, $publish, $publish );
			if( !$rcat || !ria_mysql_num_rows($rcat) ){
				continue;
			}

			$ar_final[] = array( 'id' => $prd_id, 'date' => $date_view, 'cat_id' => ria_mysql_result($rcat, 0, 'cat') );
			$i++;

			// nombre maximal de produits parcourus
			if( $limit>0 && $i>$limit ){
				break;
			}
		}
	}

	return $ar_final;
}
// \endcond

// \cond onlyria
/** Cette fonction récupère les catégories les plus consultées pour un client
 *    @param int $user Obligatoire, identifiant du client
 *    @param int $wst Optionnel, identifiant d'un site en particulier (par défaut, tous les sites)
 *    @param int $limit Optionnel, nombre maximal de résultats retournés (pour compatibilité, le nombre par défaut est 5). Mettre -1 pour ne pas limiter
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - id : identifiant de la catégorie
 *        - name : nom de la catégorie
 *        - views : nombre total de consultations
 */
function stats_user_categories_get($user, $wst = false, $limit = 5){
	if (!is_numeric($user) || $user <= 0) return false;
	if ($wst !== false && !wst_websites_exists($wst)) return false;
	if (!is_numeric($limit) || $limit == 0) return false;

	global $config;

	$subsql = '
		select s.stats_cat_id as id, count(*) as v
		from stats_prd_categories_users as s
		where s.stats_tnt_id=' . $config['tnt_id'] . ' and s.stats_usr_id=' . $user . '
	';
	if ($wst !== false)
		$subsql .= ' and s.stats_wst_id=' . $wst;
	$subsql .= '
		group by s.stats_cat_id
		union all
		select h.stats_cat_id as id, h.stats_views as v
		from stats_prd_categories_users_histo as h
		where h.stats_tnt_id=' . $config['tnt_id'] . ' and h.stats_usr_id=' . $user . '
	';
	if ($wst !== false)
		$subsql .= ' and h.stats_wst_id=' . $wst;

	$sql = '
		select cat_id as "id", if(ifnull(cat_title, "")="",cat_name,cat_title) as "name", sum(temp.v) as "views"
		from (' . $subsql . ') as temp, prd_categories
		where cat_tnt_id=' . $config['tnt_id'] . ' and temp.id=cat_id and cat_date_deleted is null
		group by cat_id
		order by sum(temp.v) desc
	';
	if ($limit > 0)
		$sql .= ' limit 0, ' . $limit;

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Retourne les produits les plus mis en panier par cet utilisateur, sans que l'achat n'ait été confirmé
 *    @param int $user Identifiant de l'utilisateur
 *    @return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *            - id : identifiant du produit
 *            - ref : référence du produit
 *            - name : description du produit
 *            - price_ht : prix hors taxes du produit
 *            - whishes : nombre de mises en panier sans achat
 *    Le résultat est limité à 10 produits.
 */
function stats_user_whislist_get($user){
	global $config;
	if (!is_numeric($user) || $user <= 0) return false;

	$sql = '
		select p.prd_id as id, p.prd_ref as ref, p.prd_name as name, tmp.whishes as whishes, tmp.price_ht_avg as price_ht
		from
			prd_products as p join (
				select
					prd_id,
					count(*) as whishes,
					sum(prd_price_ht) / sum(prd_qte) as price_ht_avg
				from
					ord_orders join
					ord_products as op on ( ord_tnt_id=op.prd_tnt_id and ord_id=prd_ord_id )
				where
					ord_usr_id=' . $user . ' and
					ord_state_id in (' . implode(', ', ord_states_get_uncompleted()) . ') and
					ord_tnt_id=' . $config['tnt_id'] . '
				group by
					prd_id
			) as tmp
				on tmp.prd_id=p.prd_id
		where
			p.prd_date_deleted is null and
			p.prd_tnt_id=' . $config['tnt_id'] . '
			and not exists (
				select 1 from fld_object_values
				where pv_fld_id=' . _FLD_IS_PORT . ' and pv_lng_code=\'' . $config['i18n_lng'] . '\'
				and pv_tnt_id='.$config['tnt_id'].' and pv_obj_id_0=p.prd_id and lower(pv_value) in (\'oui\', \'1\')
			)
	';

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction retourne des statistiques sur le sexe des comptes clients
 *    @param string $type Optionnel, libellé déterminant le critère temporel : par date de création du compte ("inscription") ou par date de dernière connexion ("connexion", par défaut)
 *    @param string $date_start Optionnel, date de début
 *    @param string $date_end Optionnel, date de fin
 *    @param int $prf Optionnel, profil ou tableau de profils
 *    @param $group Optionnel, ajout d'une ou plusieurs (via un tableau) clauses GROUP BY supplémentaires. Les valeurs autorisées sont : "hour", "year", "month", "day" et "week"
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - name : Libellé (Homme, Femme ou NC)
 *        - count : Nombre d'occurences
 *        - year, month, ... : Valeur de chaque libellé de $group
 */
function stats_user_gender($type = 'connexion', $date_start = false, $date_end = false, $prf = 0, $group = ''){

	{ // controle des paramètres
		$sql_fld_type = strtolower(trim($type)) == 'connexion' ? 'usr_last_login' : 'usr_date_created';
		if (is_array($prf)) {
			$temp = array();
			foreach ($prf as $one_prf) {
				if (is_numeric($one_prf) && $one_prf > 0) $temp[] = $one_prf;
			}
			$prf = $temp;
		} else {
			if (!is_numeric($prf) || $prf <= 0) {
				$prf = array();
			} else {
				$prf = array($prf);
			}
		}
		$periods = array('hour', 'year', 'month', 'day', 'week');
		if (is_array($group)) {
			$temp = array();
			foreach ($group as $g) {
				if (in_array(strtolower(trim($g)), $periods)) $temp[] = strtolower(trim($g));
			}
			$group = $temp;
		} else {
			if (in_array(strtolower(trim($group)), $periods))
				$group = array(strtolower(trim($group)));
			else
				$group = array();
		}
		$group = array_unique($group);
	}

	global $config;

	$sql = '
		select
			case
				when adr_title_id=1 then "Homme"
				when adr_title_id=2 then "Femme"
				when adr_title_id=3 then "Femme"
				else "NC"
			end as "name",
			count(*) as "count"
	';
	foreach ($group as $g) {
		$sql .= ', ' . $g . '(' . $sql_fld_type . ') as "' . $g . '" ';
	}

	$sql .= '
		from
			gu_users
			join gu_adresses on usr_tnt_id=adr_tnt_id and usr_adr_invoices=adr_id and usr_id=adr_usr_id
		where
			usr_tnt_id=' . $config['tnt_id'] . '
			and usr_date_deleted is null
	';

	if( sizeof($prf) ){
		$sql .= ' and usr_prf_id in ('.implode(', ', $prf).')';
	}
	if( isdate($date_start) ){
		$sql .= ' and '.$sql_fld_type.' >= "'.dateparse($date_start).'"';
	}
	if( isdate($date_end) ){
		$sql .= ' and '.$sql_fld_type.' <= "'.dateparse($date_end).' 23:59:59"';
	}

	$sql .= '
		group by
		case
			when adr_title_id=1 then "Homme"
			when adr_title_id=2 then "Femme"
			when adr_title_id=3 then "Femme"
			else "NC"
		end
	';

	foreach ($group as $g) {
		$sql .= ', ' . $g . '(' . $sql_fld_type . ')';
	}

	$r = ria_mysql_query($sql);

	if (ria_mysql_errno()) {
		error_log(mysql_error() . ' - ' . $sql);
	}
	return $r;
}
// \endcond

// \cond onlyria
/** Cette fonction calcule des statistiques sur les montants de commandes
 *  @param $col Obligatoire, colonne à retourner. Les valeurs acceptées sont carts, completed et canceled
 *  @param $date1 Obligatoire, date de début de prise en compte
 *  @param $date2 Facultatif, date de fin de prise en compte
 *  @param $group Facultatif, type de statistiques ("hour" [par défaut, via une chaîne vide], "day", "week" ou "month")
 *  @param int|array $wst Facultatif, identifiant ou tableau d'identifiants de site pour le locataire courant
 *  @param $origin Facultatif, tableau d'identifiants d'origines de commandes sur lesquelles filtrer le résultat
 *  @param $pay_id Facultatif, tableau d'identifiants de moyens de paiement sur lesquels filtrer le résultat
 *  @param $store Facultatif, tableau d'identifiants de magasins sur lesquels filtrer le résultat
 *  @param $count Facultatif, indique si le résultat doit être en montant (false) ou en nombre d'éléments (true)
 *  @param int $seller_id Facultatif, permet de filtrer les données sur l'identifiant d'un commercial
 *  @param $only_seller_orders Si le seller_id est supèrieur à 0. Si false, il permet d'inclure les commandes passe par l'adv sur les clients du commercial ( seller_id )
 * 	@param $gescom facultatif, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 *  @param int $usr_id Liste des identifiants pour lesquelles recupérer les chriffre d'affaires
 * 	@param $with_exclude_from_stats Optionnel, avec ou sans les stats
 * 	@param $exclude Optionnel, commande a exclure
 * 	@param $states Optionnel, état de commande à filtrer
 * 	@param $ord_id Optionnel, identifiant de commande ou tableau d'identifiants de commandes sur lesquelles filtrer le résultat
 * 	@param $pmt_id Optionnel, identifiants de promotion à filtrer
 * 	@param $array_fld Optionnel, champs avancé à filtrer
 * 	@param $get_child Optionnel, par défaut les commandes enfants seront retournées, mettre false pour les exclures, True pour n'avoir que les commandes enfants
 *	@param $currency Optionnel, code représentant la devise utilisée (ISO 4217)
 *	@param $dps_id Optionnel, identifiant du dépôt
 *	@param $profiles Optionnel, identifiant ou tableau d'identifiants de comptes utilisateurs sur lesquel(s) filtrer le résultat
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *        - group : nom de la colonne spécifiée via l'argument $group, représente le type de statistiques
 *        - carts || completed || canceled
 *        - total_ht : total ht
 *		  - total_ttc : total ttc
 *		  - average_ht : panier moyen ht
 *		  - average_ttc : panier moyen ttc
 *		  - marge : marge brute
 *		  - count : nombre de commande
 *		  - volume : alias de count
 *		  - sday : jour
 *		  - sweek : semaine
 *		  - syear : année
 *		  - smonth : mois
 *		  - date : date de la commande format US
 */
function stats_orders_ca(
	$col, $date1, $date2 = '', $group = '', $wst = false, $origin = false, $pay_id = 0, $store=array(), $count = false, $seller_id = 0,
	$only_seller_orders = true, $gescom = null, $usr_id = 0, $with_exclude_from_stats=true, $exclude=array(), $states=array(), $ord_id=0,
	$pmt_id=0, $array_fld=false, $get_child=null, $currency=false, $dps_id=0, $profiles=0
){
	global $config;

	if ($group != '' && !in_array($group, array('hour', 'day', 'week', 'month', 'year'))){
		return false;
	}

	// Si une recherche par référence ou par pièce de commande, pas de restriction de date donc les valeurs false sont permises
	if( !control_array_integer($ord_id) && (!isdateheure($date1) || (trim($date2) && !isdateheure($date2))) ){
		return false;
	}

	// Si un utilisateur de type représentant est connecté, on applique automatiquement le filtre sur le seller_id pour qu'il ne puisse voir que ses commandes
	// et pas les autres. Ce filtre automatique ne s'applique que si nous sommes dans l'administration
	if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN ){
		if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
			$seller_id = $_SESSION['usr_seller_id'];
		}
	}

	if (!is_numeric($seller_id)) {
		return false;
	}

	if ($wst === false) {
		$wst = 0;
	}

	$wst = control_array_integer($wst, false);

	if ($wst === false) {
		return false;
	}

	if( $array_fld !== false ){
		if( !is_array($array_fld) ){
			return false;
		}

		if( !ria_array_key_exists(array( 'or_between_val', 'or_between_fld' ), $array_fld) ){
			return false;
		}

		if( !isset($array_fld['fld']) && !isset($array_fld['fld-prd']) ){
			return false;
		}

		if( !isset($array_fld['or_between_val']) ){
			$array_fld['or_between_val'] = false;
		}

		if( !isset($array_fld['or_between_fld']) ){
			$array_fld['or_between_fld'] = false;
		}

		if( !isset($array_fld['lng']) ){
			$array_fld['lng'] = false;
		}else{
			$array_fld['lng'] = strtolower2( $array_fld['lng'] );

			if( !in_array($array_fld['lng'], $config['i18n_lng_code']) ){
				return false;
			}
		}
	}

	$sub_origin = ord_orders_get_sql_origin( $origin );

	$sql = 'select ';

	$group_by = '';
	switch ($group) {
		case 'day' :
			$sql .= '
				(dayofyear(ord_date) + ((year(ord_date)-year("' . $date1 . '"))*(dayofyear(concat(year(ord_date),\'-12-31\')))) - (dayofyear("' . $date1 . '")))  as "day",
			';
			$group_by .= '
				group by day
				order by day
			';
			break;
		case 'week' :
			$sql .= '
				year(ord_date) as year,
				week(ord_date,1)+54*(year(ord_date)-' . substr($date1, 0, 4) . ')-(select week("' . $date1 . '",1) as first_week) as "week",
			';
			$group_by .= '
				group by year(ord_date), week
				order by year(ord_date), week
			';
			break;
		case 'month' :
			$sql .= '
				year(ord_date) as syear,
				month(ord_date) as smonth,
				((month(ord_date) + ((year(ord_date)-year("' . $date1 . '"))*12)) - (month("' . $date1 . '"))) as "month",
			';
			$group_by .= '
				group by year(ord_date), month
				order by year(ord_date), month
			';
			break;
		case 'year' :
			$sql .= '
				year(ord_date) as year,
			';
			$group_by .= '
				group by year
				order by year
			';
			break;
		case 'hour':
			$sql .= '
				hour(ord_date) as "hour",
			';
			$group_by .= '
				group by sday, hour
				order by sday, hour
			';
			break;
		default :
			$sql .= '
				hour(ord_date) as "hour",
			';
			$group_by .= '
				order by date(ord_date)
			';
			break;
	}

	$stat = '';
	if ($with_exclude_from_stats) {
		$stat = 'stats_';
	}

	if( isset($config['ord_multi_currency']) && $config['ord_multi_currency'] ){
		$sql .= '
			ifnull( sum(prd_price_ht * prd_qte), 0 ) as total_ht,
			ifnull( sum(prd_price_ht * prd_tva_rate * prd_qte), 0 ) as total_ttc,
			ifnull( sum(prd_price_ht * prd_tva_rate * prd_qte), 0 ) as ' . $col . ',
			ifnull( sum(prd_price_ht * prd_qte)/count(DISTINCT ord_id), 0 ) as average_ht,
			ifnull( sum(prd_price_ht * prd_tva_rate * prd_qte)/count(DISTINCT ord_id), 0 ) as average_ttc,
			ifnull( sum((prd_price_ht - prd_purchase_avg) * prd_qte), 0 ) as marge,
		';
	}else{
		$sql .= '
			ifnull( sum(ord_'.$stat.'total_ht), 0 ) as total_ht,
			ifnull( sum(ord_'.$stat.'total_ttc), 0 ) as total_ttc,
			ifnull( sum(ord_'.$stat.'total_ttc), 0 ) as ' . $col . ',
			ifnull( sum(ord_'.$stat.'total_ht)/count(DISTINCT ord_id), 0 ) as average_ht,
			ifnull( sum(ord_'.$stat.'total_ttc)/count(DISTINCT ord_id), 0 ) as average_ttc,
			ifnull( sum(ord_stats_margin), 0 ) as marge,
		';
	}

	$sql .= '
			count(DISTINCT ord_id) as count,
			count(DISTINCT ord_id) as volume,
			day(ord_date) as sday,
			week(ord_date) as sweek,
			year(ord_date) as syear,
			month(ord_date) as smonth,
			ord_date as date,
			group_concat(distinct ord_id) as listorder
		from ord_orders
	';

	if(
		(isset($config['ord_multi_currency']) && $config['ord_multi_currency'])
		|| (defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && gu_user_is_authorized('_RGH_ADMIN_ORDER_USE_STATE_LINE'))
		|| (is_numeric($dps_id) && $dps_id > 0)
	){
		$sql .= ' join ord_products on (prd_tnt_id = '.$config['tnt_id'].' and prd_ord_id = ord_id)';
	}

	$sql .= '
			left join gu_users on (usr_tnt_id = '.$config['tnt_id'].' and usr_id = ord_usr_id)
	';

	if ($pmt_id > 0) {
		$sql .= '	join ord_orders_promotions on (ord_tnt_id=oop_tnt_id and ord_id=oop_ord_id)';
	}

	if (is_array($origin) && sizeof($origin)) {
		$sql .= ' join stats_origins on ( stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id )';
	} elseif ($origin === -1) {
		$sql .= ' left join stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )';
	}

	$sql .= '
		where ord_tnt_id=' . $config['tnt_id'] . '
			and ord_masked=0
	';

	if ($get_child !== null) {
		if ($get_child) {
			$sql .= ' and ifnull(ord_parent_id, 0) != 0';
		}else{
			$sql .= ' and ifnull(ord_parent_id, 0) = 0';
		}
	}

	if ($exclude = control_array_integer($exclude)) {
		$sql .= ' and ord_id not in ('.implode(',', $exclude) .')';
	}

	if ($ord_ids = control_array_integer($ord_id)) {
		$sql .= ' and ord_id in ('.implode(',', $ord_ids) .')';
	}elseif( $ord_id == -1 ){
		$sql .= ' and ord_id = -1';
	}

	if (!($states = control_array_integer($states))) {
		switch ($col) {
			case 'carts':
				$states = array(1,14,21);
				break;
			case 'completed':
				$states = ord_states_get_ord_valid();
				break;
			case 'canceled':
				$states = array(9,10,13);
				break;
		}
	}

	if( isset($array_fld['fld']) ){
		$sql .= fld_classes_sql_get( CLS_ORDER, $array_fld['fld'], $array_fld['or_between_val'], $array_fld['or_between_fld'], $array_fld['lng'] );
	}

	if( isset($array_fld['fld-prd']) ){
		$sql .= fld_classes_sql_get( CLS_ORD_PRODUCT, $array_fld['fld-prd'], $array_fld['or_between_val'], $array_fld['or_between_fld'], $array_fld['lng'] );
	}

	if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && gu_user_is_authorized('_RGH_ADMIN_ORDER_USE_STATE_LINE') ){
		$sql .= ' and prd_state_line in ('.implode(',', $states).')';
	}else{
		$sql .= ' and ord_state_id in ('.implode(',', $states).')';
	}

	if (is_array($wst) && sizeof($wst)) {
		$sql .= ' and ord_wst_id in (' . implode(', ', $wst) . ')';
	}

	// Ajout le SQL pour le filtre sur la ou les origines de commande
	$sql .= $sub_origin;

	if( $gescom!==null ){
		if( $gescom ){
			$sql .= ' and ord_state_id>=3 and ord_pay_id is null ';
		}else{
			$sql .= ' and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) ) ';
		}
	}

	if( isdateheure($date1) && isdateheure($date2) ){
		$sql .= ' and date(ord_date)>=\'' . dateheureparse($date1) . '\' and date(ord_date)<=\'' . dateheureparse($date2) . '\' ';
	}elseif( isdateheure($date1) ){
		$sql .= 'and date(ord_date)==\'' . dateheureparse($date1) . '\' ';
	}

	if( $pmt_id>0 ){
		$sql .= ' and oop_pmt_id='.$pmt_id;
	}

	// Filtre sur un moyen de paiement
	if( is_numeric($pay_id) && $pay_id>0 ){
		$arrid = control_array_integer($pay_id);
		$sql .=' and ord_pay_id in('.implode(', ',$arrid).')';
	}else if(is_array($pay_id) && sizeof($pay_id)){
		$sql .=' and ord_pay_id in('.implode(', ',$pay_id).')';
	}

	// Filtre sur un point de vente
	if( is_numeric($store) && $store>0 ){
		$arrid = control_array_integer($store);
		$sql .=' and ord_str_id in('.implode(', ',$arrid).')';
	}else if(is_array($store) && sizeof($store)){
		$sql .=' and ord_str_id in('.implode(', ',$store).')';
	}

	// Filtre sur le représentant
	if( is_numeric($seller_id) && $seller_id>0 ){
		if( defined('CONTEXT_IS_ADMIN') && CONTEXT_IS_ADMIN && isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER && isset($_SESSION['usr_seller_id']) && is_numeric($_SESSION['usr_seller_id']) && $_SESSION['usr_seller_id']>0 ){
			$sql .= ' and (
				(ord_seller_id ='.$seller_id.' or ( ord_seller_id is null and usr_seller_id = '.$seller_id.' ) )

				or exists (
					select 1
					from rel_relations_hierarchy
					where rrh_tnt_id = '.$config['tnt_id'].'
						and rrh_rrt_id = 2
						and rrh_src_0 = '.$seller_id.'
						and rrh_src_1 = 0
						and rrh_src_2 = 0
						and rrh_dst_0 = ord_usr_id
						and rrh_dst_1 = 0
						and rrh_dst_2 = 0
				)

				or exists (
					select 1
					from rel_relations_objects
					where rro_tnt_id = '.$config['tnt_id'].'
						and rro_rrt_id = 2
						and rro_src_0 = '.$seller_id.'
						and rro_src_1 = 0
						and rro_src_2 = 0
						and rro_dst_0 = ord_usr_id
						and rro_dst_1 = 0
						and rro_dst_2 = 0
				)
			)';
		}else{
			if( $only_seller_orders=="true" ){
				$sql .= ' and ord_seller_id = '.$seller_id;
			}else{
				$sql .= ' and (ord_seller_id = '.$seller_id.' or ( ord_seller_id is null and usr_seller_id = '.$seller_id.' ) )';
			}
		}
	}

	if( is_numeric($usr_id) && $usr_id>0 ){
		$arrid = control_array_integer($usr_id);
		$sql .=' and ord_usr_id in ('.implode(', ',$arrid).')';
	}else if(is_array($usr_id) && sizeof($usr_id)){
		$sql .=' and ord_usr_id in ('.implode(', ',$usr_id).')';
	}

	// Filtre par profil utilisateur
	if( is_numeric($profiles) && $profiles>0 ){
		$profiles[] = $profiles;
	}
	if( is_array($profiles) && sizeof($profiles)>0 ){
		$sql .= ' and exists ( select 1 from gu_users where usr_id=ord_usr_id and usr_tnt_id=ord_tnt_id and usr_prf_id in ('.implode(',',$profiles).') )';
	}

	// Filtre sur la devise utilisée
	if( $currency ){
		if( isset($config['ord_multi_currency']) && $config['ord_multi_currency'] ){
			$sql .= ' and prd_currency = "'.addslashes($currency).'"';
		}else{
			$sql .= ' and ord_currency = "'.addslashes($currency).'"';
		}
	}

	// Filtre sur le dépôt
	if(is_numeric($dps_id) && $dps_id>0){
		$sql .= ' and (
			ord_dps_id = '.$dps_id.'
			or exists(
				SELECT DISTINCT prd_ord_id
				FROM ord_products
				WHERE prd_tnt_id = '.$config['tnt_id'].'
				AND prd_ord_id = ord_id
				AND prd_dps_id = '.$dps_id.'
			)
		)';
	}

	$sql .= $group_by;

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction calcule des statistiques sur les montants de commandes
 * 	@param $col Obligatoire, colonne à retourner. Les valeurs acceptées sont carts, completed et canceled
 * 	@param $date1 Obligatoire, date de début de prise en compte
 * 	@param $date2 Facultatif, date de fin de prise en compte
 * 	@param $group Facultatif, type de statistiques ("hour" [par défaut, via une chaîne vide], "day", "week" ou "month")
 * 	@param int|array $wst Facultatif, identifiant ou tableau d'identifiants de site pour le locataire courant
 * 	@param $origin Facultatif, tableau d'identifiants d'origines de commandes sur lesquelles filtrer le résultat
 * 	@param $pay_id Facultatif, tableau d'identifiants de moyens de paiement sur lesquels filtrer le résultat
 * 	@param $store Facultatif, tableau d'identifiants de magasins sur lesquels filtrer le résultat
 * 	@param $count Facultatif, indique si le résultat doit être en montant (false) ou en nombre d'éléments (true)
 * 	@param int $seller_id Facultatif, permet de filtrer sur l'identifiants d'un commercial
 * 	@param $only_seller_orders Si le seller_id est supèrieur à 0. Si false, il permet d'inclure les commandes passe par l'adv sur les clients du commercial ( seller_id )
 * 	@param $gescom facultatif, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 * 	@param int $usr_id Optionnel, liste des identifiants pour lesquelles recupérer les chriffre d'affaires
 * 	@param $ord_id Optionnel, identifiant d'une commande
 * 	@param $direct Optionnel, par défaut n'est pas prit en compte, mettre true pour récupérer les statistiques sur les commandes direct pour le commercial, false pour l'indirect
 *
 * 	@return bool False en cas d'échec
 * 	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *        - group : nom de la colonne spécifiée via l'argument $group, représente le type de statistiques
 *        - carts : Montant total sur les paniers, ou nombre d'éléments si $count ==true
 *        - completed : Montant total sur les paniers validés, ou nombre d'éléments si $count ==true
 *        - canceled : Montant total sur les paniers abandonnés, ou nombre d'éléments si $count ==true
 */
function stats_orders_ca2($col, $date1, $date2 = '', $group = '', $wst = false, $origin = false, $pay_id = 0, $store=array(), $count = false, $seller_id = 0, $only_seller_orders = true, $gescom = null, $usr_id = 0, $ord_id=0, $direct=null){

	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year'))){
		$group = '';
	}
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) {
		return false;
	}
	if (!is_numeric($seller_id)) {
		return false;
	}

	if ($wst === false) {
		$wst = 0;
	}
	$wst = control_array_integer($wst, false);
	if ($wst === false) {
		return false;
	}
	// $sum_or_count = $count ? "count":"sum";

	$sub_origin = ord_orders_get_sql_origin( $origin );

	global $config;

	$sub_sql = "op.prd_qte * CASE WHEN IFNULL(p.prd_sell_weight, 0)=1 THEN IFNULL(col_qte / " . $config["weight_col_calc_lines"] . ", 1) ELSE IFNULL(col_qte, 1) END";

	$sql = "
		select sum(prd_price_ht) as " . $col . ",
		sum(prd_price_ttc) as total_ttc,
		sum(purchase_avg) as purchase_avg,
		sum(marge) as marge,
		day(ord_date) as sday,
		week(ord_date) as sweek,
		year(ord_date) as syear,
		month(ord_date) as smonth,
		date(ord_date) as date,
		group_concat(DISTINCT ord_id) as ord_ids,
		count(DISTINCT ord_id) as count,
		sum(prd_price_ht) / count(DISTINCT ord_id) as average_ht,
	";
	switch ($group) {
		case 'day' :
			$sql .= ' day ';
			break;
		case 'week' :
			$sql .= ' year, week ';
			break;
		case 'month' :
			$sql .= ' syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= ' year ';
			break;
		default :
			$sql .= ' hour ';
	}
	$sql .= "    from ( ";

	switch ($group) {
		case 'day' :
			$sql .= '
				select
					(dayofyear(ord_date) + ((year(ord_date)-year("' . $date1 . '"))*(dayofyear(concat(year(ord_date),\'-12-31\')))) - (dayofyear("' . $date1 . '")))  as "day",

				';
			break;
		case 'week' :
			$sql .= '
				select
					year(ord_date) as year,
					week(ord_date,1)+54*(year(ord_date)-' . substr($date1, 0, 4) . ')-(select week("' . $date1 . '",1) as first_week) as "week",
				';
			break;
		case 'month' :
			$sql .= '
				select
					year(ord_date) as syear,
					month(ord_date) as smonth,
					((month(ord_date) + ((year(ord_date)-year("' . $date1 . '"))*12)) - (month("' . $date1 . '"))) as "month",
				';
			break;
		case 'year' :
			$sql .= '
				select
					year(ord_date) as year,
				';
			break;
		default :
			$sql .= '
				select
					hour(ord_date) as hour,
				';
	}

	$sql .= "
	           ord_id, ( ( op.prd_price_ht - p.prd_ecotaxe ) *op.prd_qte) as prd_price_ht, ";
	$sql .= "
	          (op.prd_price_ttc*op.prd_qte) as prd_price_ttc, ";
	$sql .= "
	           CASE WHEN op.prd_purchase_avg IS NOT NULL THEN op.prd_purchase_avg ELSE IFNULL(p.prd_purchase_avg, op.prd_price_ht - p.prd_ecotaxe )*op.prd_qte END as purchase_avg, ";
	$sql .= "
	           ( op.prd_price_ht - p.prd_ecotaxe - IFNULL(op.prd_purchase_avg, p.prd_purchase_avg) ) * " . $sub_sql . " as marge, ord_date ";
	$sql .= "
	        from ";
	$sql .= "
	           ord_products as op ";
	$sql .= "
	      join prd_products as p on op.prd_id = p.prd_id and op.prd_tnt_id=p.prd_tnt_id";
	$sql .= "
	       join ord_orders on op.prd_ord_id = ord_id and op.prd_tnt_id=ord_tnt_id";
	$sql .= "
	        join gu_users on usr_id = ord_usr_id and op.prd_tnt_id=usr_tnt_id";
	if( is_array($origin) && sizeof($origin) ){
		$sql .= '
		 join stats_origins on ( stats_tnt_id=op.prd_tnt_id and stats_obj_id_0=ord_id )';
	}
	$sql .= "
	        left join fld_object_values as fld on p.prd_id = fld.pv_obj_id_0 and 0=fld.pv_obj_id_1 and 0=fld.pv_obj_id_2 and " . _FLD_PRD_EXCLUDE_FROM_STATS . "=fld.pv_fld_id and fld.pv_lng_code='fr' and op.prd_tnt_id=fld.pv_tnt_id";
	$sql .= "
	        left join fld_object_values as fld1 on ( prd_ord_id = fld1.pv_obj_id_0 and op.prd_id = fld1.pv_obj_id_1 and prd_line_id = fld1.pv_obj_id_2 and IFNULL(fld1.pv_fld_id, " . _FLD_PRD_COL_ORD_PRODUCT . ") = " . _FLD_PRD_COL_ORD_PRODUCT . " and IFNULL(fld1.pv_lng_code, '" . i18n::getLang() . "') = '" . i18n::getLang() . "' ) and op.prd_tnt_id=fld1.pv_tnt_id";
	$sql .= "
	       left join prd_colisage_types on IFNULL(fld1.pv_value, 0) = col_id and col_tnt_id=op.prd_tnt_id ";
	$sql .= "
	        where ord_masked = 0 and op.prd_tnt_id=".$config['tnt_id'];

	switch ($col) {
		case 'carts':
			$sql .= '
			 and ord_state_id in (1,14,21)';
			break;
		case 'completed':
			$sql .= '
			 and ord_state_id in ('.implode(', ', ord_states_get_ord_valid()).')';
			break;
		case 'canceled':
			$sql .= '
			 and ord_state_id in (9,10,13)';
			break;
	}
	if (is_array($wst) && sizeof($wst)) {
		$sql .= '
		 and ord_wst_id in (' . implode(', ', $wst) . ')';
	}
	$sql .= $sub_origin;

	if (!is_null($direct)) {
		if ($direct) {
			$sql .= '
				and ord_pay_id not null
				and ord_seller_com is null
				and ord_seller_id not null
			';
		}else{
			$sql .= '
				and ord_pay_id is null
				and ord_seller_com not null
				and ord_seller_id != ord_seller_com
			';
		}
	}
	if( $gescom!==null ){
		if( $gescom ){
			$sql .= '
			 and ord_state_id>=3 and ord_pay_id is null';
		}else{
			$sql .= '
			 and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) )';
		}
	}
	if ($date1 != null && $group != "all") {
		$sql .= "
		 and date(ord_date) >= '" . dateheureparse($date1) . "' ";
	}

	if ($date2 != null && $group != "all") {
		$sql .= "
		 and date(ord_date) <= '" . dateheureparse($date2) . "' ";
	}

	if(is_numeric($pay_id) && $pay_id>0){
		$arrid = control_array_integer($pay_id);
		$sql .='
		 and ord_pay_id in('.implode(', ',$arrid).')';
	}else if(is_array($pay_id) && sizeof($pay_id)){
		$sql .='
		 and ord_pay_id in('.implode(', ',$pay_id).')';
	}

	if(is_numeric($store) && $store>0){
		$arrid = control_array_integer($store);
		$sql .='
		 and ord_str_id in('.implode(', ',$arrid).')';
	}else if(is_array($store) && sizeof($store)){
		$sql .='
		 and ord_str_id in('.implode(', ',$store).')';
	}

	if( $only_seller_orders ){
		$sql .= "
		 and ord_seller_id is not null";
	}

	if ($seller_id > 0) {
		$sql .= "
		 and ord_seller_id = " . $seller_id;
	}

	if(is_numeric($usr_id) && $usr_id>0){
		$arrid = control_array_integer($usr_id);
		$sql .='
		 and ord_usr_id in('.implode(', ',$arrid).')';
	}else if(is_array($usr_id) && sizeof($usr_id)){
		$sql .='
		 and ord_usr_id in('.implode(', ',$usr_id).')';
	}

	if ($ord_id > 0) {
		$sql .= "
		 and ord_id = " . $ord_id;
	}

	$sql .= "
	 and (fld.pv_value is null or fld.pv_value!='Oui') ";

	if (!empty($config["ca_prd_ref_exclude"])) {
		$sql .= "
		 and op.prd_ref not in ('" . implode("','", $config["ca_prd_ref_exclude"]) . "') ";
	}

	$sql .= "   ) t1 ";

	switch ($group) {
		case 'day' :
			$sql .= '
				group by day
				order by day
			';
			break;
		case 'week' :
			$sql .= '
				group by year(ord_date), week
				order by year(ord_date), week
			';
			break;
		case 'month' :
			$sql .= '
				group by year(ord_date), month
				order by year(ord_date), month
			';
			break;
		case 'year' :
			$sql .= '
				group by year
				order by year
			';
			break;
		default :
			$sql .= '
				group by hour(ord_date)
				order by hour(ord_date)
			';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de calculer le chiffre d'affaires facturé.
 * 	@param string $col Obligatoire, colonne à retourner (alias personnalisable selon l'appel)
 * 	@param string $date1 Obligatoire, Date de début
 * 	@param string $date2 Optionnel, date de fin
 * 	@param string $group Optionnel, paramètre de retroupement (day, week, month, year)
 * 	@param bool $with_port Optionnel, exclu les frais de port par défaut (mettre true pour les inclures)
 * 	@param int $seller_id Optionnel, identifiant d'un représentant
 *	@param bool $only_seller_orders Optionnel, par défaut tient compte des factures rattachées à un compte lui-même rattaché à un représentant, mettre false pour tenir compte de tous les comptes
 * 	@param int $usr_id Optionnel, identifiant d'un compte client
 * 	@param int $inv_id Optionnel, identifiant d'une facture
 * 	@param null|bool $direct Optionnel, par défaut n'est pas pris en compte, mettre true pour récupérer les statistiques sur la facturation directe liée à un commercial, false pour l'indirect. La valeur par défaut est null
 * 	@param bool $only_web Optionnel, par défaut seule le CA issus de commandes Web est prit en compte, mettre false toutes les inclures
 *  @param array $origin Facultatif, tableau d'identifiants d'origines de commandes sur lesquelles filtrer le résultat
 * 	@return resource Un résultat MySQL contenant :
 *					- $col : chiffre d'affaires Hors Taxes (HT)
 *					- total_ttc : chiffre d'affaires Toutes Taxes Comprises (TTC)
 *					- purchase_avg : panier moyen
 *					- marge : marge brute réalisée
 *					- sday : jour de la facture
 *					- sweek : semaine de la facture
 *					- syear : année de la facture
 *					- smonth : mois de la facture
 *					- date : date de la facture (format en)
 *					- inv_ids : regroupement des identifiants de facture
 *					- count : nombre de facture
 */
function stats_invoices_ca_array( $col, $date1, $date2='', $group='', $with_port=false, $seller_id=0, $only_seller_orders=true, $usr_id=0, $inv_id=0, $direct=null, $only_web=true, $origin=false){
	global $config;
	$stats = array() ;

	if( isset($config['stats_couchdb_active']) && $config['stats_couchdb_active'] ){
		$query = BigQuery::create(BIGQUERY_DB_INVOICES_STATS)->getInvoiceStats($group, $date1, $date2, 0, 0, 0, $seller_id, false, null, null);
		if( $query ){
			foreach($query as $s ){
				$s[$col] = $s['total_ca_ht'];
				$stats[] = $s;
			}
		}
	}else{
		$query = stats_invoices_ca( $col, $date1, $date2, $group, $with_port, $seller_id, $only_seller_orders, $usr_id, $inv_id, $direct, $only_web, $origin);
		if( $query ){
			while( $s = ria_mysql_fetch_assoc($query) ){
				$s['year'] = $s['syear'];
				$s['week'] = $s['sweek'];
				$s['month'] = $s['smonth'];
				$s['day'] = $s['sday'];
				$stats[] = $s;
			}
		}
	}

	return $stats;
}


function stats_invoices_ca( $col, $date1, $date2='', $group='', $with_port=false, $seller_id=0, $only_seller_orders=true, $usr_id=0, $inv_id=0, $direct=null, $only_web=true, $origin=false){
	global $config;

	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year', 'hour'))){
		$group = '';
	}
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) {
		return false;
	}
	if (!is_numeric($seller_id)) {
		return false;
	}

	$sub_origin = ord_orders_get_sql_origin( $origin );
	$sql = "
		select
		sum(prd_price_ht) as " . $col . ",
		sum(prd_price_ttc) as total_ttc,
		sum(purchase_avg) as purchase_avg,
		sum(marge) as marge,
		day(inv_date) as sday,
		week(inv_date) as sweek,
		year(inv_date) as syear,
		month(inv_date) as smonth,
		date(inv_date) as date,
		group_concat(DISTINCT inv_id) as inv_ids,
		count(DISTINCT inv_id) as count,
	";
	switch ($group) {
		case 'day' :
			$sql .= ' day ';
			break;
		case 'week' :
			$sql .= ' year, week ';
			break;
		case 'month' :
			$sql .= ' syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= ' year ';
			break;
		default :
			$sql .= ' "hour" ';
	}
	$sql .= "    from ( ";
	$sql .= "    select inv_id, sum( oip.prd_price_ht * oip.prd_qte * if(inv_discount > 0, (1 - (inv_discount / 100)),1) ) as prd_price_ht, inv_date, ";
	switch ($group) {
		case 'day' :
			$sql .= '

					(dayofyear(inv_date) + ((year(inv_date)-year("' . $date1 . '"))*(dayofyear(concat(year(inv_date),\'-12-31\')))) - (dayofyear("' . $date1 . '")))  as "day",
				';
			break;
		case 'week' :
			$sql .= '

					year(inv_date) as year,
					week(inv_date,1)+54*(year(inv_date)-' . substr($date1, 0, 4) . ')-(select week("' . $date1 . '",1) as first_week) as "week",
				';
			break;
		case 'month' :
			$sql .= '
					year(inv_date) as syear,
					month(inv_date) as smonth,
					((month(inv_date) + ((year(inv_date)-year("' . $date1 . '"))*12)) - (month("' . $date1 . '"))) as "month",
				';
			break;
		case 'year' :
			$sql .= '
					year(inv_date) as year,
				';
			break;
		default :
			$sql .= '
					hour(inv_date) as "hour",
				';
	}

	$sql .= "
			sum(oip.prd_price_ttc*oip.prd_qte) as prd_price_ttc,
			sum(CASE WHEN oip.prd_purchase_avg IS NOT NULL THEN oip.prd_purchase_avg ELSE IFNULL(p.prd_purchase_avg, oip.prd_price_ht - oip.prd_ecotaxe)*oip.prd_qte END) as purchase_avg,
			sum(( oip.prd_price_ht - (CASE WHEN oip.prd_purchase_avg IS NOT NULL THEN oip.prd_purchase_avg ELSE IFNULL(p.prd_purchase_avg, oip.prd_price_ht - oip.prd_ecotaxe) END)) * oip.prd_qte) as marge
	    from ord_inv_products as oip
		join prd_products as p on oip.prd_id = p.prd_id and oip.prd_tnt_id=p.prd_tnt_id
		join ord_invoices on oip.prd_inv_id = inv_id  and oip.prd_tnt_id=inv_tnt_id
		left join gu_users on (usr_tnt_id = ".$config['tnt_id']." and usr_id = inv_usr_id)
		left join fld_object_values as fld on (
				p.prd_id = fld.pv_obj_id_0
				and 0=fld.pv_obj_id_1
				and 0=fld.pv_obj_id_2
				and fld.pv_fld_id=" . _FLD_PRD_EXCLUDE_FROM_STATS . "
				and fld.pv_lng_code='fr'
				and oip.prd_tnt_id=fld.pv_tnt_id
				)";


	$subquery_is_needed = false;
	$subquery = "
		select 1
		from ord_products as op
		left join ord_orders on op.prd_ord_id = ord_id  and op.prd_tnt_id=ord_tnt_id
	";

	if (is_array($origin) && sizeof($origin)) {
		$subquery_is_needed = true;
		$subquery .= ' join stats_origins on ( stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id )';
	} elseif ($origin === -1) {
		$subquery .= ' left join stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )';
	}
	$sql .= "
		where oip.prd_tnt_id=".$config['tnt_id']." ";

	$subquery .= "
		where oip.prd_ord_id = op.prd_ord_id
		and oip.prd_id = op.prd_id
		and oip.prd_tnt_id=op.prd_tnt_id
	";

	// date de début
	if ($date1 != null && $group != "all") {
		$sql .= "
		 and date(inv_date) >= '" . dateheureparse($date1) . "'
		 ";
	}

	if ($date2 != null && $group != "all") {
		$sql .= "
		 and date(inv_date) <= '" . dateheureparse($date2) . "'
		 ";
	}

	if(is_numeric($usr_id) && $usr_id>0){
		$arrid = control_array_integer($usr_id);
		$sql .='
		 and inv_usr_id in('.implode(', ',$arrid).')
		 ';
	}else if(is_array($usr_id) && sizeof($usr_id)){
		$sql .='
		 and inv_usr_id in('.implode(', ',$usr_id).')
		 ';
	}

	if ($inv_id > 0) {
		$sql .= "
		 and inv_id = " . $inv_id;
	}

	$sql .= $sub_origin;

	if( $only_web ){
		$subquery_is_needed = true;
		$subquery .= ' and ord_pay_id is not null';
	}
	if (!is_null($direct)) {
		$subquery_is_needed = true;
		if ($direct) {
			$subquery .= '
				and ord_seller_com is null
				and ord_seller_id is not null
			';
			if ($seller_id > 0) {
				$subquery .= "
				 and ord_seller_id = " . $seller_id;
			}
		}else{
			$subquery .= '
				and ord_seller_com is not null
				and ifnull(ord_seller_id, 0) != ifnull(ord_seller_com, 0)
				and ifnull(op.prd_rpr_id, 0) != 0
			';

			if ($seller_id > 0) {
				$subquery .= "
				 and ord_seller_com = " . $seller_id;
			}
		}
	}else{
		if( $only_seller_orders ){
			$subquery_is_needed = true;
			$subquery .= "
			 and ord_seller_id is not null";
		}

		if ($seller_id > 0) {
			$subquery .= '
			 and (
				 (ord_seller_id = '.$seller_id.' )
				 or
				 (ord_seller_com = '.$seller_id.' and ifnull(ord_seller_id, 0) != ifnull(ord_seller_com, 0) )
				) ';
		}
	}

	if( $subquery_is_needed ){
		$sql .= " and exists (".$subquery.")";
	}elseif ($seller_id > 0) {
		
		if($config['tnt_id'] == 1118){
			$sql_childs_hierarchy = '
			and ifnull(oip.prd_seller_id, usr_seller_id) = '.$seller_id.' ';

			$childs_id = gu_users_get_hierarchy_childs($seller_id);
				
			if(!empty($childs_id)){
				$sql_childs_hierarchy = '
				and (ifnull(oip.prd_seller_id, usr_seller_id) = '.$seller_id.' OR inv_usr_id IN ('.implode(",", $childs_id).'))
		';
	}
					
			$sql .= $sql_childs_hierarchy;
		}
		else{
			$sql .= '
			and ifnull(oip.prd_seller_id, usr_seller_id) = '.$seller_id.' ';
		}
		
		
	}

	if (!$with_port && !empty($config["dlv_prd_references"])) {
		$sql .= "
		 and oip.prd_ref not in ('".implode("','", $config["dlv_prd_references"] )."') ";
	}

	$sql .= "
	 and (fld.pv_value is null or fld.pv_value!='Oui') ";

	if (!empty($config["ca_prd_ref_exclude"])) {
		$sql .= "
		 and oip.prd_ref not in ('".implode("','", $config["ca_prd_ref_exclude"])."') ";
	}
	switch ($group) {
		case 'day' :
			$sql .= '
				group by day
				order by day
			';
			break;
		case 'week' :
			$sql .= '
				group by year(inv_date), week
				order by year(inv_date), week
			';
			break;
		case 'month' :
			$sql .= '
				group by year(inv_date), month
				order by year(inv_date), month
			';
			break;
		case 'year' :
			$sql .= '
				group by year
				order by year
			';
			break;
		default :
			$sql .= '
				group by hour(inv_date)
				order by hour(inv_date)
			';
	}

	$sql .= "     ) t1 ";

	switch ($group) {
		case 'day' :
			$sql .= '
				group by day
				order by day
			';
			break;
		case 'week' :
			$sql .= '
				group by year(inv_date), week
				order by year(inv_date), week
			';
			break;
		case 'month' :
			$sql .= '
				group by year(inv_date), month
				order by year(inv_date), month
			';
			break;
		case 'year' :
			$sql .= '
				group by year
				order by year
			';
			break;
		default :
			$sql .= '
				group by hour(inv_date)
				order by hour(inv_date)
			';
	}

	if( isset($_GET['testolivier']) ){print '<pre>'.htmlspecialchars( $sql ).'</pre>';}
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retourner les statistiques sur les factures
 *	@param string $date_start Obligatoire, Date de début d'analyse
 *	@param string $date_end Obligatoire, Date de fin d'analyse
 *	@param array|int $usr_id Optionnel, Identifiant ou tableau d'identifiants de clients
 *	@param array|int $prd_id Optionnel, Identifiant ou tableau d'identifiants de produits
 *	@param array|int $cat_id Optionnel, Identifiant d'une catégorie
 *	@param int $seller_id Optionnel, Identifiant du commercial
 *	@param int $brd_id Optionnel, identifiant d'une marque
 *	@param string $group_key Optionnel, prd / brd / cat / usr, permet d'avoir le top 20 en fonction des paramètres données
 *  @param string $period Optionnel, période utilisé pour le group by, Facultatif si le paramètre group_key est donnée
 *	@param string $order_by Optionnel, sort de trie du résultat pour le moment fonctionnel avec seulement le group_key
 *	@param int $limit Optionnel, impose une limite sur le nombre de résultat retourné lors de l'application du group by (par défaut à 20)
 *
 *	@return null Une exception sera levée en cas d'erreur
 *  @return array un tableau associatif avec les colonnes suivantes :
 *		- total_ht
 *		- total_ttc
 *		- products : Sommes des quantités
 *		- marge_ht
 *		- marge_ttc
 *		- obj_id_0 : disponible si group_key donnée, c'est l'identifiant de l'objet en question
 *		- year : disponible si period donnée
 *		- month : disponible si period donnée
 *		- day : disponible si period donnée
 */
function stats_invoices_get( $date_start, $date_end, $usr_id=0, $prd_id=0, $cat_id=0, $seller_id=0, $brd_id=0, $group_key=false, $period=null, $order_by=null, $limit=20 ){
	global $config;

	if( $period !== null && !in_array(strtoupper( $period ), array( 'DAY', 'MONTH', 'YEAR', 'WEEK', 'QUARTER','SEMESTER' )) ){
		throw new Exception("Type de période non gérer");
	}

	$usr_id = control_array_integer( $usr_id, false );
	if( $usr_id === false ){
		throw new Exception("Paramètre usr_id invalide.");
	}

	$prd_id = control_array_integer( $prd_id, false );
	if( $prd_id === false ){
		throw new Exception("Paramètre prd_id invalide.");
	}

	if( !is_numeric($brd_id) ){
		throw new Exception("Paramètre brd_id invalide.");
	}

	if( !is_numeric($seller_id) ){
		throw new Exception("Paramètre seller_id invalide.");
	}

	$cat_id = control_array_integer( $cat_id, false );
	if( $cat_id!==0 && $prd_id===false ){
		throw new Exception("Paramètre cat_id invalide.");
	}

	$col_group = '';
	$period_fields = [];
	$select_period = [];

	if( $group_key !== false ){
		switch( $group_key ){
			case 'prd':
				$col_group = 'ip.prd_id';
				break;
			case 'brd':
				$col_group = 'p.prd_brd_id';
				break;
			case 'seller':
				$col_group = 'pcs_dst_obj_0';
				break;
			case 'cat':
				$col_group = 'pcc_dst_obj_0';
				break;
			case 'usr':
				$col_group = 'inv_usr_id';
				break;
			default:
				throw new Exception("Mauvais groupe");
		}
	}else{
		if( $period === null ){
			throw new Exception( "Paramètre period invalide.");
		}

		$period = strtoupper($period);

		if( $period == 'DAY' || $period == 'MONTH' || $period == 'YEAR' || $period == 'QUARTER' || $period == 'WEEK' || $period == 'SEMESTER' ){
			$period_fields[] = 'year(inv_date)';
			$select_period[] = 'year(inv_date) as "year"';
		}
		if( $period == 'DAY' || $period == 'MONTH' ){
			$period_fields[] = 'month(inv_date) ';
			$select_period[] = 'month(inv_date) as "month"';
		}
		if( $period == 'DAY'  ){
			$period_fields[] = 'dayofmonth(inv_date) ';
			$select_period[] = 'dayofmonth(inv_date) as "day"';
		}
		if( $period == 'QUARTER'  ){
			$period_fields[] = 'quarter(inv_date) ';
			$select_period[] = 'quarter(inv_date) as "quarter"';
		}
		if( $period == 'SEMESTER'  ){
			$period_fields[] = 'CONCAT((year(inv_date)),\'-S\',(IF(quarter(inv_date) < 3, 1, 2))) ';
			$select_period[] = 'IF(min(quarter(inv_date)) < 3, 1, 2) as "semester"';
		}
		if( $period == 'WEEK'  ){
			$period_fields[] = 'weekofyear(inv_date) ';
			$select_period[] = 'weekofyear(inv_date) as "week"';
		}
	}

	// if(ifnull(ip.prd_purchase_avg, 0) > 0, ip.prd_purchase_avg, ifnull(p.prd_purchase_avg, 0))
	$sql = '
		select
			'.( trim($col_group) != '' ? $col_group.' as obj_id_0,' : '' ).'
			'.( count($select_period) > 0 ? implode( ', ', $select_period ).', ' : '' ).'
			sum( ip.prd_price_ht * ip.prd_qte ) as "total_ca_ht",
			sum( ip.prd_price_ht * ip.prd_tva_rate * ip.prd_qte ) as "total_ca_ttc",
			sum( ip.prd_qte ) as "products",
			sum( ip.prd_purchase_avg * ip.prd_qte ) as "marge_ht",
			sum( ip.prd_purchase_avg * ip.prd_tva_rate * ip.prd_qte ) as "marge_ttc",
			count( distinct inv_piece ) as "count"
		from ord_inv_products as ip
			join ord_invoices on (inv_tnt_id = '.$config['tnt_id'].' and inv_id = ip.prd_inv_id)
			left join prd_products as p on (p.prd_tnt_id = '.$config['tnt_id'].' and p.prd_id = ip.prd_id)
			left join fld_object_values on (
				ip.prd_id = pv_obj_id_0
				and 0 = pv_obj_id_1
				and 0 = pv_obj_id_2
				and '._FLD_PRD_EXCLUDE_FROM_STATS.' = pv_fld_id
				and pv_lng_code = "fr"
				and '.$config['tnt_id'].' = pv_tnt_id
			)
	';

	if( $group_key == 'cat' || count($cat_id) > 0 ){
		$sql .= ' join stats_pre_calculated_classify on (
			pcc_tnt_id = '.$config['tnt_id'].'
			and pcc_cls_id = '.CLS_INV_PRODUCT.'
			and pcc_obj_id_0 = ip.prd_inv_id
			and pcc_obj_id_1 = ip.prd_id
			and pcc_obj_id_2 = ip.prd_line_id
		)';
	}

	if( $group_key == 'seller' || $seller_id > 0 ){
		$sql .= ' join stats_pre_calculated_seller on (
			pcs_tnt_id = '.$config['tnt_id'].'
			and pcs_cls_id = '.CLS_INV_PRODUCT.'
			and pcs_obj_id_0 = ip.prd_inv_id
			and pcs_obj_id_1 = ip.prd_id
			and pcs_obj_id_2 = ip.prd_line_id
		)';
	}

	$sql .= '
		where ip.prd_tnt_id = '.$config['tnt_id'].'
			and ( pv_value is null or pv_value != "Oui" )
	';

	if( $date_start !== null ){
		$sql .= ' and date(inv_date) >= "'.addslashes( $date_start ).'"';
	}

	if( $date_end !== null ){
		$sql .= ' and date(inv_date) <= "'.addslashes( $date_end ).'"';
	}

	if( $seller_id > 0 ){
		$sql .= '
			and pcs_dst_obj_0 = '.$seller_id.'
		';
	}

	if( count($cat_id) > 0 ){
		$sql .=  '
			and pcc_dst_obj_0 in ('.implode( ', ', $cat_id ).')
		';

		if( $group_key == 'cat' && isset($config['cat_root']) && is_numeric($config['cat_root']) && $config['cat_root'] > 0 ){
			$sql .= ' and pcc_dst_obj_0 != '.$config['cat_root'];
		}
	}

	if( count($usr_id) > 0 ){
		$sql .= ' and inv_usr_id in ('.implode( ', ', $usr_id ).')';
	}

	if( count($prd_id) > 0 ){
		$sql .= ' and ip.prd_id in ('.implode( ', ', $prd_id ).')';
	}

	if( trim($col_group) != '' ){
		$sql .= ' group by '.$col_group;
	}elseif( count($period_fields) > 0 ){
		$sql .= ' group by '.implode( ', ', $period_fields );
	}

	$sql .= ' order by total_ca_ht desc';

	if( isset($_GET['debug']) ){
		print '<pre>'.htmlspecialchars( $sql ).'</pre>';
	}

	$res = ria_mysql_query( $sql );

	$final_array = [];
	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$final_array[] = $r;
		}
	}

	return $final_array;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de calculer le CA indirecte des commandes
 * @param $col Colonne à retourner (alias personnalisable selon l'appel)
 * @param $date1 Date de début
 * @param $date2 Optionnel, date de fin
 * @param $group Optionnel, paramètre de retroupement (day, week, month, year)
 * @param int $seller_id Optionnel, identifiant d'un représentant
 * @param $only_seller_orders Optionnel, par défaut tient compte des factures rattaché à un compte lui-même rattaché à un représentant, mettre false pour tenir compte de tous les comptes
 * @return resource Un résultat MySQL contenant
 *					- $col : ca ht
 *					- total_ttc : ca ttc
 *					- purchase_avg : panier moyen
 *					- marge : marge réalisée
 *					- sday : jour de la facture
 *					- sweek : semaine de la facture
 *					- syear : année de la facture
 *					- smonth : mois de la facture
 *					- date : date de la facture (format en)
 *					- ord_ids : regroupement des identifiants de commandes
 *					- count : nombre de commandes
 */
function stats_orders_indirect_ca($col, $date1, $date2 = '', $group = '', $seller_id=0, $only_seller_orders=true){
	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year'))){
		$group = '';
	}
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) {
		return false;
	}
	if (!is_numeric($seller_id)) {
		return false;
	}
	global $config;

	$sub_sql = "op.prd_qte * CASE WHEN IFNULL(p.prd_sell_weight, 0)=1 THEN IFNULL(col_qte / " . $config["weight_col_calc_lines"] . ", 1) ELSE IFNULL(col_qte, 1) END";

	$sql = "
		select sum(prd_price_ht) as " . $col . ",
		sum(prd_price_ttc) as total_ttc,
		sum(purchase_avg) as purchase_avg,
		sum(marge) as marge,
		day(ord_date) as sday,
		week(ord_date) as sweek,
		year(ord_date) as syear,
		month(ord_date) as smonth,
		date(ord_date) as date,
		group_concat(DISTINCT ord_id) as ord_ids,
		count(DISTINCT ord_id) as count,
	";
	switch ($group) {
		case 'day' :
			$sql .= ' day ';
			break;
		case 'week' :
			$sql .= ' year, week ';
			break;
		case 'month' :
			$sql .= ' syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= ' year ';
			break;
		default :
			$sql .= ' hour ';
	}
	$sql .= "    from ( ";

	switch ($group) {
		case 'day' :
			$sql .= '
				select
					(dayofyear(ord_date) + ((year(ord_date)-year("' . $date1 . '"))*(dayofyear(concat(year(ord_date),\'-12-31\')))) - (dayofyear("' . $date1 . '")))  as "day",

				';
			break;
		case 'week' :
			$sql .= '
				select
					year(ord_date) as year,
					week(ord_date,1)+54*(year(ord_date)-' . substr($date1, 0, 4) . ')-(select week("' . $date1 . '",1) as first_week) as "week",
				';
			break;
		case 'month' :
			$sql .= '
				select
					year(ord_date) as syear,
					month(ord_date) as smonth,
					((month(ord_date) + ((year(ord_date)-year("' . $date1 . '"))*12)) - (month("' . $date1 . '"))) as "month",
				';
			break;
		case 'year' :
			$sql .= '
				select
					year(ord_date) as year,
				';
			break;
		default :
			$sql .= '
				select
					hour(ord_date) as "hour",
				';
	}
	$sql .= "ord.ord_id as ord_id,	( ( op.prd_price_ht - p.prd_ecotaxe ) *op.prd_qte) as prd_price_ht,";
	$sql .= "            (op.prd_price_ttc*op.prd_qte) as prd_price_ttc, ";
	$sql .= "            CASE WHEN op.prd_purchase_avg IS NOT NULL THEN op.prd_purchase_avg ELSE IFNULL(p.prd_purchase_avg, op.prd_price_ht - p.prd_ecotaxe )*op.prd_qte END as purchase_avg, ";
	$sql .= "            ( op.prd_price_ht - p.prd_ecotaxe - IFNULL(op.prd_purchase_avg, p.prd_purchase_avg) ) * " . $sub_sql . " as marge, ord_date ";
	$sql .= "
			from (
				select ord_id, ord_tnt_id, ord_date
				from ord_orders";
	$sql .= "		join ord_products as o on ord_tnt_id=o.prd_tnt_id and ord_id=o.prd_ord_id
					join rp_reports on rpr_tnt_id=o.prd_tnt_id and rpr_usr_id=ord_usr_id and rpr_author_id!=ord_seller_id
					join rp_report_objects on rpro_tnt_id = o.prd_tnt_id and rpro_rpr_id = rpr_id and rpro_cls_id=3
				where ord_masked = 0 and ord_tnt_id=".$config['tnt_id']."
				and rpr_rpt_id = 1";
	switch ($col) {
		case 'carts':
			$sql .= ' and ord_state_id in (1,14,21)';
			break;
		case 'completed':
			$sql .= ' and ord_state_id in ('.implode(', ', ord_states_get_ord_valid()).')';
			break;
		case 'canceled':
			$sql .= ' and ord_state_id in (9,10,13)';
			break;
	}

	if ($date1 != null && $group != "all") {
		$sql .= " and date(ord_date) >= '" . dateheureparse($date1) . "' ";
	}

	if ($date2 != null && $group != "all") {
		$sql .= " and date(ord_date) <= '" . dateheureparse($date2) . "' ";
	}
	if( $only_seller_orders ){
		$sql .= " and ord_seller_id is not null";
	}
	if ($seller_id > 0) {
		$sql .= " and ord_seller_id = " . $seller_id;
	}
	$sql .= "			and (exists (
				    select 1
						from prd_classify, prd_cat_hierarchy
						where cly_tnt_id=".$config['tnt_id']." and cat_tnt_id=".$config['tnt_id']." and cly_cat_id=cat_child_id
						and cat_parent_id = rpro_obj_id_0
				    	and cly_prd_id=o.prd_id
					)
					or
					exists (
						select 1
						from prd_classify
						where cly_tnt_id=".$config['tnt_id']." and cly_prd_id=o.prd_id and cly_cat_id = rpro_obj_id_0
					)
					)
				and rpr_date_created <= ord_date
				group by ord_id
				order by rpr_date_created desc
			) as ord
			join ord_products as op on ord.ord_tnt_id=op.prd_tnt_id and ord.ord_id=op.prd_ord_id
			join prd_products as p on p.prd_tnt_id=ord.ord_tnt_id and p.prd_id=op.prd_id";
	$sql .= "        left join fld_object_values as fld on p.prd_id = fld.pv_obj_id_0 and 0=fld.pv_obj_id_1 and 0=fld.pv_obj_id_2 and " . _FLD_PRD_EXCLUDE_FROM_STATS . "=fld.pv_fld_id and fld.pv_lng_code='fr' and ord.ord_tnt_id=fld.pv_tnt_id";
	$sql .= "        left join fld_object_values as fld1 on ( prd_ord_id = fld1.pv_obj_id_0 and op.prd_id = fld1.pv_obj_id_1 and prd_line_id = fld1.pv_obj_id_2 and IFNULL(fld1.pv_fld_id, " . _FLD_PRD_COL_ORD_PRODUCT . ") = " . _FLD_PRD_COL_ORD_PRODUCT . " and IFNULL(fld1.pv_lng_code, '" . i18n::getLang() . "') = '" . i18n::getLang() . "' ) and ord.ord_tnt_id=fld1.pv_tnt_id";
	$sql .= "        left join prd_colisage_types on IFNULL(fld1.pv_value, 0) = col_id and col_tnt_id=ord.ord_tnt_id ";

	// $sql .= "	group by ord_id ";
	$sql .= "	) t1 ";

	switch ($group) {
		case 'day' :
			$sql .= '
				group by day
				order by day
			';
			break;
		case 'week' :
			$sql .= '
				group by year(ord_date), week
				order by year(ord_date), week
			';
			break;
		case 'month' :
			$sql .= '
				group by year(ord_date), month
				order by year(ord_date), month
			';
			break;
		case 'year' :
			$sql .= '
				group by year
				order by year
			';
			break;
		default :
			$sql .= '
				group by hour(ord_date)
				order by hour(ord_date)
			';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction calcule des statistiques sur le nombre de commandes
 * 	@param $date1 Obligatoire, date de début de prise en compte des statistiques
 * 	@param $date2 Facultatif, date de fin de prise en compte des statistiques
 * 	@param $group Facultatif, type de statistiques (hour [par défaut, via une chaîne vide], month, day, week)
 * 	@param int|array $wst Facultatif, identifiant ou tableau d'identifiants de sites pour le locataire courant
 * 	@param $origin Optionnel, origine des commandes
 *	@param $pay_id Optionnel, identiifnat du moyen de paiement
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *        - group : le nom de la colonne est suivant l'argument $group, détermine le type de statistiques
 *        - carts : nombre de paniers
 *        - completed : nombre de paniers validés
 *        - canceled : nombre de paniers abandonnés
 */
function stats_view_orders_count( $date1, $date2='', $group='', $wst=false, $origin=false, $pay_id=0 ){

	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year'))) return false;
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) return false;

	if ($wst === false) {
		$wst = 0;
	}
	$wst = control_array_integer($wst, false);
	if ($wst === false) {
		return false;
	}

	global $config;

	switch ($group) {
		case 'day' :
			$sql = 'select stat_datetime as date, (dayofyear(stat_datetime) + ((year(stat_datetime)-year("' . $date1 . '"))*(dayofyear(concat(year(stat_datetime),\'-12-31\')))) - (dayofyear("' . $date1 . '"))) as "day", month(stat_datetime) as smonth, sum(stat_carts) as carts, sum(stat_orders_completed) as completed, sum(stat_orders_canceled) as canceled';
			break;
		case 'week' :
			$sql = 'select week(stat_datetime,1)+54*(year(stat_datetime)-' . substr($date1, 0, 4) . ')-(select week("' . $date1 . '",1) as first_week) as "week",  sum(stat_carts) as carts, sum(stat_orders_completed) as completed, sum(stat_orders_canceled) as canceled';
			break;
		case 'month' :
			$sql = 'select month(stat_datetime) as smonth, year(stat_datetime) as syear, ((month(stat_datetime) + ((year(stat_datetime)-year("' . $date1 . '"))*12)) - (month("' . $date1 . '"))) as "month", sum(stat_carts) as carts, sum(stat_orders_completed) as completed, sum(stat_orders_canceled) as canceled';
			break;
		case 'year' :
			$sql = 'select year(stat_datetime) as year, sum(stat_carts) as carts, sum(stat_orders_completed) as completed, sum(stat_orders_canceled) as canceled';
			break;
		default :
			$sql = 'select hour(stat_datetime) as "hour", stat_carts as carts, stat_orders_completed as completed, stat_orders_canceled as canceled';
	}

	$sql .= '
		from stats_hourly
		where stat_tnt_id=' . $config['tnt_id'] . '
	';

	if (is_array($wst) && sizeof($wst)) {
		$sql .= ' and stat_wst_id in (' . implode(', ', $wst) . ')';
	}

	if (trim($date2) != '') {
		$sql .= ' and date(stat_datetime)>=\'' . $date1 . '\' and date(stat_datetime)<=\'' . $date2 . '\'';
	} else
		$sql .= 'and date(stat_datetime)=\'' . $date1 . '\'';

	switch ($group) {
		case 'day' :
			$sql .= 'group by dayofyear(stat_datetime)';
			break;
		case 'week' :
			$sql .= 'group by year(stat_datetime), week(stat_datetime,1)';
			break;
		case 'month' :
			$sql .= 'group by year(stat_datetime), month(stat_datetime)';
			break;
		case 'year' :
			$sql .= 'group by year(stat_datetime)';
			break;
		default :
			$sql .= 'order by stat_datetime';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction calcule des statistiques sur le formulaire de contact
 *    @param $date1 Obligatoire, date de début de prise en compte des statistiques
 *    @param $date2 Facultatif, date de fon de prise en compte des statistiques
 *    @param $group Facultatif, type de statistiques (jour, semaine, mois). Il faut préciser une valeur en anglais (day, week ou month). Par défaut, il s(git des statistiques horaires
 *    @param int $wst Facultatif, identifiant d'un site en particulier pour le locataire courant
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les champs suivants :
 *        - group : le nom de la colonne dépend de la chaîne précisé pour l'argument $group
 *        - contacts : statistiques sur les contacts
 *        - faq : statistiques sur l'accès à la FAQ
 */
function stats_contacts_count($date1, $date2 = '', $group = '', $wst = false){
	if ($group != '' && !in_array($group, array('day', 'week', 'month'))) return false;
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) return false;

	if ($wst === false) {
		$wst = 0;
	}
	$wst = control_array_integer($wst, false);
	if ($wst === false) {
		return false;
	}

	global $config;

	switch ($group) {
		case 'day' :
			$sql = '
				select cnt_date_created as date,
					dayofyear(cnt_date_created) + ((year(cnt_date_created)-year("' . $date1 . '"))*(dayofyear(concat(year(cnt_date_created),\'-12-31\')))) - (dayofyear("' . $date1 . '")) as "day",
					count(*) as contacts
				';
			break;
		case 'week' :
			$sql = '
				select
					week(cnt_date_created,1)+54*(year(cnt_date_created)-' . substr($date1, 0, 4) . ')-(select week("' . $date1 . '",1) as first_week) as "week"
					count(*) as contacts
			';
			break;
		case 'month' :
			$sql = '
				select month(cnt_date_created) as smonth,
				year(cnt_date_created) as syear,
				((month(cnt_date_created) + ((year(cnt_date_created)-year("' . $date1 . '"))*12)) - (month("' . $date1 . '"))) as "month",
				count(*) as contacts
			';
			break;
		case 'year' :
			break;
		default :
			$sql = '
				select hour(cnt_date_created) as "hour",
				count(*) as contacts
			';
	}

	$sql .= '
		from gu_messages
		where cnt_tnt_id=' . $config['tnt_id'] . '
			and cnt_type=3
	';

	if (is_array($wst) && sizeof($wst)) {
		$sql .= ' and cnt_wst_id in (' . implode(', ', $wst) . ')';
	}

	if (trim($date2) != '') {
		$sql .= ' and date(cnt_date_created)>=\'' . $date1 . '\' and date(cnt_date_created)<=\'' . $date2 . '\'';
	} else {
		$sql .= ' and date(cnt_date_created)=\'' . $date1 . '\'';
	}

	switch ($group) {
		case 'day' :
			$sql .= ' group by date(cnt_date_created)';
			break;
		case 'week' :
			$sql .= ' group by year(cnt_date_created), week(cnt_date_created,1)';
			break;
		case 'month' :
			$sql .= ' group by year(cnt_date_created), month(cnt_date_created)';
			break;
		default :
			$sql .= ' order by cnt_date_created';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction calcule des statistiques sur le nombre de pages vues pour un article donné
 *    @param $prd Obligatoire, identifiant de l'article
 *    @param $date1 Obligatoire, date de début de prise en compte
 *    @param $date2 Facultatif, date de fin de prise en compte
 *    @param $group Facultatif, type de statistiques (day, hour [par défaut], week, month)
 *    @param int $wst Facultatif, identifiant d'un site particulier pour le locataire courant
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - group : Le nom de la colonne dépend du paramètre $group fourni
 *        - hits : Nombre de pages vues
 */
function stats_view_products_seen($prd, $date1, $date2 = '', $group = '', $wst = false){
	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year'))) return false;
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) return false;
	if ($wst && !wst_websites_exists($wst)) return false;

	if (!is_array($prd)) {
		if (!is_numeric($prd) || $prd <= 0) {
			return false;
		}

		$prd = array($prd);
	} else {
		if (!sizeof($prd)) {
			return false;
		}

		foreach ($prd as $p) {
			if (!is_numeric($p) || $p <= 0) {
				return false;
			}
		}
	}

	global $config;

	switch ($group) {
		case 'day' :
			$sql = 'select stat_datetime as date, (dayofyear(stat_datetime) + ((year(stat_datetime)-year("' . $date1 . '"))*(dayofyear(concat(year(stat_datetime),\'-12-31\')))) - (dayofyear("' . $date1 . '"))) as "day", sum(stat_prd_hits) as hits ';
			break;
		case 'week' :
			$sql = 'select week(stat_datetime,1)+54*(year(stat_datetime)-' . substr($date1, 0, 4) . ')-(select week("' . $date1 . '",1) as first_week) as "week",  sum(stat_prd_hits) as hits';
			break;
		case 'month' :
			$sql = 'select month(stat_datetime) as smonth, year(stat_datetime) as syear,((month(stat_datetime) + ((year(stat_datetime)-year("' . $date1 . '"))*12)) - (month("' . $date1 . '"))) as "month", sum(stat_prd_hits) as hits';
			break;
		case 'year' :
			$sql = 'select year(stat_datetime) as year, sum(stat_prd_hits) as hits';
			break;
		default :
			$sql = 'select hour(stat_datetime) as "hour", stat_prd_hits as hits';
	}

	$sql .= '
		from stats_prd_hourly
		where stat_tnt_id=' . $config['tnt_id'] . '
			and stat_prd_id in (' . implode(', ', $prd) . ')
			' . ($wst ? ' and stat_wst_id=' . $wst : '') . '
	';

	if (trim($date2) != '') {
		$sql .= ' and date(stat_datetime)>=\'' . $date1 . '\' and date(stat_datetime)<=\'' . $date2 . '\'';
	} else
		$sql .= 'and date(stat_datetime)=\'' . $date1 . '\'';

	switch ($group) {
		case 'day' :
			$sql .= 'group by dayofyear(stat_datetime)';
			break;
		case 'week' :
			$sql .= 'group by year(stat_datetime), week(stat_datetime,1)';
			break;
		case 'month' :
			$sql .= 'group by year(stat_datetime), month(stat_datetime)';
			break;
		case 'year' :
			$sql .= 'group by year(stat_datetime)';
			break;
		default :
			$sql .= 'order by stat_datetime';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction calcule des statistiques de commande pour un produit donné
 *    @param $prd Obligatoire, identifiant d'un article
 *    @param $date1 Obligatoire, date de début de prise en compte
 *    @param $date2 Facultatif, date de fin de prise en compte
 *    @param $group Facultatif, type de statistiques (hour [par défaut via chaîne vide], day, month, week)
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - group : Le nom de la colonne dépend du paramètre $group
 *        - hits : Nombre de fois où l'article a été mis au panier et validé
 */
function stats_view_products_ordered($prd, $date1, $date2 = '', $group = ''){
	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year'))) return false;
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) return false;
	global $config;

	if (!is_array($prd)) {
		if (!is_numeric($prd) || $prd <= 0) {
			return false;
		}

		$prd = array($prd);
	} else {
		if (!sizeof($prd)) {
			return false;
		}

		foreach ($prd as $p) {
			if (!is_numeric($p) || $p <= 0) {
				return false;
			}
		}
	}

	$ratio = isset($config['weight_col_calc_lines']) && is_numeric($config['weight_col_calc_lines']) && $config['weight_col_calc_lines'] > 0 ? $config['weight_col_calc_lines'] : 1000;
	$sqlQte = 'sum( if( ifnull(p.prd_sell_weight, 0)=1, ifnull(col_qte/' . $ratio . ', 1), 1 ) )';

	switch ($group) {
		case 'day' :
			$sql = 'select ord_date as date, day(ord_date) as "day", ' . $sqlQte . ' as hits';
			break;
		case 'week' :
			$sql = 'select week(ord_date) as "week",  ' . $sqlQte . ' as hits';
			break;
		case 'month' :
			$sql = 'select month(ord_date) as "month", month(ord_date) as "smonth", year(ord_date) as "year", year(ord_date) as "syear", ' . $sqlQte . ' as hits';
			break;
		case 'year' :
			$sql = 'select year(ord_date) as "year", year(ord_date) as "syear", ' . $sqlQte . ' as hits';
			break;
		default :
			$sql = 'select hour(ord_date) as "hour", ' . $sqlQte . ' as hits';
	}

	$sql .= '
		from ord_orders, ord_products as op
		join prd_products as p on ( op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id )
		left join fld_object_values as vc on ( op.prd_tnt_id=vc.pv_tnt_id and op.prd_ord_id=vc.pv_obj_id_0 and op.prd_id=vc.pv_obj_id_1 and op.prd_line_id=vc.pv_obj_id_2 and ' . _FLD_PRD_COL_ORD_PRODUCT . '=vc.pv_fld_id )
		left join prd_colisage_types on ( op.prd_tnt_id=col_tnt_id and ifnull(vc.pv_value, 0)=col_id and col_is_deleted = 0 )
		left join fld_object_values as vp on ( op.prd_tnt_id=vp.pv_tnt_id and op.prd_ord_id=vp.pv_obj_id_0 and op.prd_id=vp.pv_obj_id_1 and op.prd_line_id=vp.pv_obj_id_2 and ' . _FLD_ORD_LINE_WEIGHT . '=vp.pv_fld_id )
		where ord_tnt_id=' . $config['tnt_id'] . ' and op.prd_tnt_id=ord_tnt_id
			and ord_id = prd_ord_id
			and ord_state_id in (' . implode(', ', ord_states_get_ord_valid()) . ')
			and op.prd_id in (' . implode(', ', $prd) . ')
	';

	if (trim($date2) != '') {
		$sql .= ' and date(ord_date)>=\'' . $date1 . '\' and date(ord_date)<=\'' . $date2 . '\'';
	} else
		$sql .= 'and date(ord_date)=\'' . $date1 . '\'';

	switch ($group) {
		case 'day' :
			$sql .= ' group by ord_date';
			break;
		case 'week' :
			$sql .= ' group by year(ord_date), week(ord_date,1)';
			break;
		case 'month' :
			$sql .= ' group by year(ord_date), month(ord_date)';
			break;
		case 'year' :
			$sql .= ' group by year(ord_date)';
			break;
		default :
			$sql .= ' group by hour(ord_date)';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/**	Cette fonction génère des statistiques sur l'origine des commandes
 *	\ingroup model_stats
 *	@param $prd Optionnel, identifiant (ou tableau) d'un produit contenu dans les commandes
 *	@param int|array $cat Optionnel, identifiant (ou tableau) d'une catégorie de produits contenue dans les commandes
 *	@param string $date_min Optionnel, date minimale de commande
 *	@param string $date_max Optionnel, date maximale de commande
 *	@param int|array $wst Optionnel, identifiant ou tableau d'identifiants de site sur lesquels la commande a été passée
 *	@param $group Optionnel, option de groupby sur la date (par jour, heure, semaine, mois, année ou rien [défaut])
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - source : info de source
 *        - name : info de source
 *        - medium : info de source
 *        - content : info de source
 *        - term : info de source
 *        - count : nombre d'occurences
 *        - prd_id : identifiant de produit si $prd spécifié
 *        - cat_id : identifiant de catégorie si $cat spécifié
 *        - date_group : suivant $group (rien si $group = '')
 */
function stats_origins_get_stats($prd = 0, $cat = 0, $date_min = false, $date_max = false, $wst = 0, $group = ''){
	{ // contrôles
		if (is_array($prd)) {
			if (!sizeof($prd)) return false;
			foreach ($prd as $p) {
				if (!is_numeric($p) || $p <= 0) return false;
			}
		} else {
			if (!is_numeric($prd) || $prd <= 0) {
				$prd = array();
			} else {
				$prd = array($prd);
			}
		}

		if (is_array($cat)) {
			if (!sizeof($cat)) return false;
			foreach ($cat as $c) {
				if (!is_numeric($c) || $c <= 0) return false;
			}
		} else {
			if (!is_numeric($cat) || $cat <= 0) {
				$cat = array();
			} else {
				$cat = array($cat);
			}
		}

		if ($date_min !== false) {
			if (!isdate($date_min)) return false;
			$date_min = dateparse($date_min);
		}
		if ($date_max !== false) {
			if (!isdate($date_max)) return false;
			$date_max = dateparse($date_max);
		}

		$wst = control_array_integer($wst, false);
		if ($wst === false) {
			return false;
		}

		if ($group != '' && !in_array($group, array('day', 'week', 'month', 'hour', 'year'))) return false;
	}

	global $config;

	$sql = '
		select
			stats_source as "source",
			stats_name as "name",
			stats_medium as "medium",
			stats_content as "content",
			stats_term as "term",
			count(*) as "count",
			sum(ord_total_ht) as "total_ht",
			sum(ord_total_ttc) as "total_ttc"
	';
	if (sizeof($prd))
		$sql .= ', prd_id';
	if (sizeof($cat))
		$sql .= ', ifnull(cat_parent_id, cly_cat_id) as cat_id';
	switch ($group) {
		case 'year':
			$sql .= ', year(ord_date) as date_group';
			break;
		case 'month':
			$sql .= ', month(ord_date) as date_group';
			break;
		case 'week':
			$sql .= ', week(ord_date) as date_group';
			break;
		case 'day':
			$sql .= ', day(ord_date) as date_group';
			break;
		case 'hour':
			$sql .= ', hour(ord_date) as date_group';
			break;
	}
	$sql .= '
		from ord_orders
			left join stats_origins on ord_tnt_id=stats_tnt_id and stats_obj_id_0=ord_id
	';
	if (sizeof($prd) || sizeof($cat)) {
		$sql .= ' join ord_products on ord_id=prd_ord_id and ord_tnt_id=prd_tnt_id ';
		if (sizeof($cat)) {
			$sql .= '
				join prd_classify on prd_id=cly_prd_id and prd_tnt_id=cly_tnt_id
				left join prd_cat_hierarchy on cly_tnt_id=cat_tnt_id and cly_cat_id=cat_child_id
			';
		}
	}
	$sql .= '
		where stats_tnt_id=' . $config['tnt_id'] . '
			and stats_cls_id=' . CLS_ORDER . '
			and ord_parent_id is null
			and ord_state_id in ('.implode( ',', ord_states_get_ord_valid() ).')
	';

	if (is_array($wst) && sizeof($wst)) {
		$sql .= ' and stats_wst_id in (' . implode(', ', $wst) . ')';
	}

	if ($date_min !== false)
		$sql .= ' and date(ord_date)>="' . $date_min . '"';
	if ($date_max !== false)
		$sql .= ' and date(ord_date)<="' . $date_max . '"';
	if (sizeof($prd))
		$sql .= ' and prd_id in (' . implode(', ', $prd) . ')';
	if (sizeof($cat))
		$sql .= ' and ifnull(cat_parent_id, cly_cat_id)  in (' . implode(', ', $cat) . ')';

	$sql .= '
		group by
			ifnull(stats_source, \'\'),
			ifnull(stats_name, \'\'),
			ifnull(stats_medium, \'\'),
			ifnull(stats_content, \'\'),
			ifnull(stats_term, \'\')
	';
	if (sizeof($prd))
		$sql .= ', prd_id';
	if (sizeof($cat))
		$sql .= ', ifnull(cat_parent_id, cly_cat_id)';
	switch ($group) {
		case 'year':
			$sql .= ', year(ord_date)';
			break;
		case 'month':
			$sql .= ', month(ord_date)';
			break;
		case 'week':
			$sql .= ', week(ord_date)';
			break;
		case 'day':
			$sql .= ', day(ord_date)';
			break;
		case 'hour':
			$sql .= ', hour(ord_date)';
			break;
	}
	$sql .= ' order by count desc';

	$r = ria_mysql_query($sql);
	if (ria_mysql_errno()) {
		error_log(mysql_error() . ' - ' . $sql);
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Cette fonction génère des statistiques sur l'origine des commandes
 *	\ingroup model_origins
 *	@param $prd Optionnel, identifiant (ou tableau) d'un produit contenu dans les commandes
 *	@param int|array $cat Optionnel, identifiant (ou tableau) d'une catégorie de produits contenue dans les commandes
 *	@param string $date_start Optionnel, date minimale de commande
 *	@param string $date_stop Optionnel, date maximale de commande
 *	@param int|array $wst Optionnel, identifiant ou tableau d'identifiants de site sur lesquels la commande a été passée
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - origin : nom de la source, parmi les valeurs suivantes : Référencement naturel, Référencement payant, Réseaux sociaux, Places de marché, etc...
 *        - orders : nombre de commandes depuis cette source sur la période étudiée
 *        - ca_ht : chiffre d'affaires HT réalisé depuis cette source
 *        - ca_ttc : chiffre d'affaires TTC réalisé depuis cette source
 *        - pourcent : pourcentage de commandes par rapport au total
 */
function stats_orders_get_origins( $prd=0, $cat=0, $date_start=false, $date_stop=false, $wst=0 ){

	// Contrôle du paramètre $prd
	if( is_array($prd) ){
		if( !sizeof($prd) ) return false;
		foreach( $prd as $p ){
			if( !is_numeric($p) || $p<=0 ) return false;
		}
	}else{
		if( !is_numeric($prd) || $prd<=0 ){
			$prd = array();
		}else{
			$prd = array($prd);
		}
	}

	// Contrôle du paramètre $cat
	if( is_array($cat) ){
		if( !sizeof($cat) ) return false;
		foreach( $cat as $c ){
			if( !is_numeric($c) || $c<=0 ) return false;
		}
	}else{
		if( !is_numeric($cat) || $cat<=0 ){
			$cat = array();
		}else{
			$cat = array($cat);
		}
	}
	// Contrôle du paramètre $date_start
	if( $date_start!==false ){
		if( !isdate($date_start) ) return false;
		$date_start = dateparse($date_start);
	}

	// Contrôle du paramètre $date_stopt
	if( $date_stop!==false ){
		if( !isdate($date_stop) ) return false;
		$date_stop = dateparse($date_stop);
	}

	// Contrôle du paramètre $wst
	$wst = control_array_integer( $wst, false );
	if( $wst === false ){
		return false;
	}

	global $config;

	$sql = '
	';
}
// \endcond

// \cond onlyria
/**	Cette fonction calcule des statistiques sur les performances des fiches produits
 *	@param array $sort Obligatoire, Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : ref, name, price, brand, publish, completion, date-created, date-promo, rand(), selled, catchilds. Les valeurs autorisées pour la direction sont : asc, desc.
 *	@param string $date1 Optionnel, date de début de prise en compte
 *	@param string $date2 Optionnel, date de fin de prise en compte
 *	@param int|array $wst Optionnel, identifiant (ou tableau d'identifiants) de site
 *	@param null|array $ids Optionnel, Tableau d'identifiants de produits. Tous les produits si null (par défaut)
 *	@param int|array $cat Optionnel, identifiant ou tableau d'identifiants de catégorie, ce paramètre est ignoré si des idenfiants de produits ($ids) sont fournis
 *	@param bool $recursive_cat Optionnel, récupère les produits dans les sous-familles de $cat, ignoré si ce dernier n'est pas renseigné
 *	@param int $limit Facultatif, nombre de produits maximum à retourner. La valeur par défaut est 5000.
 *	@param array $exclude_prd Facultatif, permet d'exclure certains articles du résultat
 *	@param bool $have_stock Facultatif, par défaut tous les produits sont retournés sans distinction, mettre True pour ne retourner que les articles en stock
 *
 *  @return bool False en cas d'échec
 *  @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - id : identifiant
 *        - ref : référence du produit
 *        - name : nom du produit
 *        - hits : Nombre de pages vues
 *        - sells : Nombre des ventes
 *        - conversion : Taux de conversion
 *        - price_purchase : Prix d'achat
 *        - price_sell : Prix de vente
 *        - margin : Marge
 *        - margin_rate : Taux de marge
 */
function stats_view_products_conversion( $sort=false, $date1='', $date2='', $wst=false, $ids=null, $cat=0, $recursive_cat=false, $limit=5000, $exclude_prd=array(), $have_stock=false ){

	if( ( $date1!='' && !isdate($date1) ) || ( $date2!='' && !isdate($date2) ) ) return false;

	if( is_array($wst) ){
		foreach( $wst as $w ){
			if( !is_numeric($w) || $w<=0 ) return false;
		}
	}elseif( $wst!==false ){
		if( !is_numeric($wst) || $wst<=0 ) return false;
		$wst = array($wst);
	}else{
		$wst = array();
	}

	if( is_array($ids) ){
		foreach( $ids as $id ){
			if( !is_numeric($id) || $id<=0 ) return false;
		}
	}elseif( $ids!==null ){
		if( !is_numeric($ids) || $ids<=0 ) return false;
		$ids = array($ids);
	}else{
		$ids = array();
	}

	if (($date1 != '' && !isdate($date1)) || ($date2 != '' && !isdate($date2))) return false;

	$allow_cols = array('ref', 'name', 'hits', 'sells', 'conversion', 'price_purchase', 'price_sell', 'margin', 'margin_rate', 'rand()' );

	$sort_final = array();
	if( is_array($sort) && sizeof($sort) ){
		foreach( $sort as $key => $dir ){
			if( in_array($key, $allow_cols) && in_array($dir, array( '', 'asc', 'desc')) ){
				$sort_final[] = $key.' '.$dir;
			}
		}
	}
	if( !sizeof($sort_final) ){
		$sort_final[] = 'conversion desc';
	}

	if (is_array($ids)) {
		foreach ($ids as $id) {
			if (!is_numeric($id) || $id <= 0) return false;
		}
	} elseif ($ids !== null) {
		if (!is_numeric($ids) || $ids <= 0) return false;
		$ids = array($ids);
	} else
		$ids = array();

	if (!is_numeric($limit) || $limit <= 0) {
		return false;
	}

	$exclude_prd = control_array_integer( $exclude_prd, false );
	if( $exclude_prd === false ){
		return false;
	}

	global $config;

	$allow_cols = array('ref', 'name', 'hits', 'sells', 'conversion', 'price_purchase', 'price_sell', 'margin', 'margin_rate');

	$sort_final = array();
	if (is_array($sort) && sizeof($sort)) {
		foreach ($sort as $key => $dir) {
			if (in_array($key, $allow_cols) && in_array($dir, array('asc', 'desc'))) {
				$sort_final[] = $key . ' ' . $dir;
			}
		}
	}
	if (!sizeof($sort_final)) {
		$sort_final[] = 'conversion desc';
	}

	if (!sizeof($ids)) {
		$cat = control_array_integer($cat, false);

		if (is_array($cat) && sizeof($cat)) {
			$rp = prd_products_get_simple(0, '', false, $cat, $recursive_cat);
			if ($rp && ria_mysql_num_rows($rp)) {
				$ids = array();

				while ($p = ria_mysql_fetch_assoc($rp)) {
					$ids[] = $p['id'];
				}
			}
		}
	}

	global $config;

	$dps = prd_deposits_get_main();

	$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

	// Identifiants de comptes utilisateurs dont les commandes ne doivent pas être prises en compte (places de marché)
	$usr_exclusions = ctr_comparators_get_user_ids();


	$sql = '
		select
			p.prd_id as id, p.prd_ref as ref, p.prd_name as name, hits, sells, p.prd_purchase_avg as price_purchase,
			get_price_ht( p.prd_tnt_id, p.prd_id, 0, 1, 0, ' . $config['discount_apply_type'] . ', ' . $config['default_prc_id'] . ', ' . $config['use_last_known_price'] . ', "' . wst_websites_languages_default() . '", ' . $config['cat_id_prices'] . ', "", p.prd_brd_id, p.prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'p.prd_ecotaxe').' ) as price_sell
		from
			prd_products as p
			left join (
				select op.prd_id, count(*) as sells
				from ord_orders
					join ord_products as op on ord_id=prd_ord_id and ord_tnt_id=op.prd_tnt_id
					left join stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )
				where ord_tnt_id=' . $config['tnt_id'] . ' and ord_state_id in (' . implode(',', ord_states_get_ord_valid()) . ')
					and ord_masked=0 '.( sizeof($usr_exclusions) ? 'and ord_usr_id not in ('.implode( ',', $usr_exclusions ).')' : '' );
	if ($date1 && !$date2) {
		$sql .= '
				and ord_date >= "' . dateparse($date1) . ' 00:00:00" and ord_date <= "' . dateparse($date1) . ' 23:59:59"
			';
	} elseif ($date1) {
		$sql .= '
				and ord_date >= "' . dateparse($date1) . ' 00:00:00"
			';
	}
	if ($date2) {
		$sql .= '
				and ord_date <= "' . dateparse($date2) . ' 23:59:59"
			';
	}
	if (sizeof($wst)) {
		$sql .= '
				and ord_wst_id in (' . implode(',', $wst) . ')
			';
	}

	// Seules les commandes web doivent être prises en compte pour que les statistiques soient justes
	$sql .= ' and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) )';

	// Les places de marché ne comptent pas dans les ventes à prendre en compte dans cette analyse, elles doivent être exclues également
	$exclude = array();
	$rctr = ctr_comparators_get( 0, false, false, true );
	while( $ctr = ria_mysql_fetch_array($rctr) ){
		$exclude[] = $ctr['source'];
	}
	if( sizeof($exclude) ){
		$sql .= ' and stats_source not in (\''.implode('\',\'', $exclude).'\')';
	}

	$sql .= '
				group by op.prd_id
			) as tmp_op on p.prd_id=tmp_op.prd_id
			left join (
				select stat_prd_id, sum(stat_prd_hits) as hits
				from stats_prd_hourly
				where stat_tnt_id=' . $config['tnt_id'] . '
	';
	if ($date1 && !$date2) {
		$sql .= '
				and stat_datetime >= "' . dateparse($date1) . ' 00:00:00" and stat_datetime <= "' . dateparse($date1) . ' 23:59:59"
			';
	} elseif ($date1) {
		$sql .= '
				and stat_datetime >= "' . dateparse($date1) . ' 00:00:00"
			';
	}
	if ($date2) {
		$sql .= '
				and stat_datetime <= "' . dateparse($date2) . ' 23:59:59"
			';
	}
	if (sizeof($wst)) {
		$sql .= '
				and stat_wst_id in (' . implode(',', $wst) . ')
			';
	}
	$sql .= '
				group by stat_prd_id
			) as tmp_stat on p.prd_id=tmp_stat.stat_prd_id
			left join prd_stocks as s on p.prd_id=s.sto_prd_id and ' . $config['tnt_id'] . '=s.sto_tnt_id and ' . $dps . '=sto_dps_id and sto_is_deleted=0
		where
			p.prd_date_deleted is null and p.prd_publish=1 and p.prd_publish_cat and p.prd_orderable and not p.prd_childonly
			and ( p.prd_sleep=0 or ( ' . prd_stocks_get_sql('s') .'-ifnull(s.sto_prepa, 0) )>0 )
			and p.prd_tnt_id=' . $config['tnt_id'] . '
	';

	if( sizeof($ids) ){
		$sql .= ' and p.prd_id in (' . implode(', ', $ids) . ')';
	}

	if( sizeof($exclude_prd) ){
		$sql .= ' and p.prd_id not in ('.implode(', ', $exclude_prd).')';
	}

	if( $have_stock ){
		$sql .= ' and (' . prd_stocks_get_sql() . '-sto_prepa) > 0';
	}

	$sql = '
		select
			id, ref, name, price_sell, price_purchase, ifnull(hits, 0) as hits, ifnull(sells, 0) as sells,
			if(ifnull(hits, 0) = 0, 0, ifnull(sells, 0) / ifnull(hits, 0) * 100) as conversion, price_sell - price_purchase as margin,
			( price_sell-price_purchase ) / price_purchase * 100 as margin_rate
		from
			(' . $sql . ') as tmp
		where hits > 0
		order by ' . implode(', ', $sort_final) . '
		limit '.$limit;

	$r = ria_mysql_query($sql);

	if (ria_mysql_errno()) {
		error_log(mysql_error() . ' - ' . $sql);
	}

	return $r;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour le nom
 *	\ingroup model_origins
 *	@param $obj Obligatoire, identifiant de l'objet ( ou tableau d'identifiants pour les clés composées )
 *	@param $class Obligatoire, identifiant d'une classe d'object
 *	@param string $name Facultatif, Nom
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function stats_origins_update_name($obj, $class, $name){
	if (!stats_origins_exists($obj, $class)) return -1;

	global $config;

	if (!is_array($obj)) $obj = array($obj);
	if (sizeof($obj) < 1 || sizeof($obj) > COUNT_OBJ_ID) return -2;
	for ($i = count($obj); $i < COUNT_OBJ_ID; $i++) $obj[$i] = 0;

	if (!is_numeric($class)) return -3;

	$O = array(
		'stats_tnt_id' => $config['tnt_id'],
		'stats_wst_id' => $config['wst_id'],
		'stats_cls_id' => $class
	);
	foreach ($obj as $i => $o) {
		if (!is_numeric($o) || $o < 0) return -4;
		$O['stats_obj_id_' . $i] = $o;
	}

	global $config;

	$sql = '
		update stats_origins
		set
			stats_name = \'' . addslashes($name) . '\',
			stats_source = \'' . addslashes($name) . '\'
		where (' . implode(',', array_keys($O)) . ') = (' . implode(',', $O) . ')
	';
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de lier une action (commande, contact...) à son origine.
 *	\ingroup model_origins
 *	@param $obj Obligatoire, identifiant de l'objet ( ou tableau d'identifiants pour les clés composées )
 *	@param $class Obligatoire, identifiant d'une classe d'objet
 *	@param $ip Facultatif, Adresse Ip de l'internaute ayant effectué l'action
 *	@param $source Facultatif, permet de surcharger le paramètre campaign_source de Google Analytics.
 *	@param string $name Facultatif, permet de surcharger le paramètre campaign_name de Google Analytics.
 *	@param $medium Facultatif, permet de surcharger le paramètre campaign_medium de Google Analytics.
 *	@param $content Facultatif, permet de surcharger le paramètre campaign_content de Google Analytics.
 *	@param $term Facultatif, permet de surcharger le paramètre campaign_term de Google Analytics.
 *	@param $time Facultatif, permet de surcharger le paramètre Nombre de visites de Google Analytics.
 *	@param $pages Facultatif, permet de surcharger le paramètre pages_viewed de Google Analytics.
 *	@param $first_visit Facultatif, permet de surcharger le paramètre first_visit de Google Analytics.
 *	@param $previous_visit Facultatif, permet de surcharger le paramètre date de la visite précédente de Google Analytics.
 *	@param $current Facultatif, permet de surcharger le paramètre current_visit_started de Google Analytics.
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function stats_origins_add($obj, $class, $ip = null, $source = '', $name = '', $medium = '', $content = null, $term = null, $time = null, $pages = null, $first_visit = null, $previous_visit = null, $current = null){
	if (stats_origins_exists($obj, $class)) {
		stats_origins_refresh($obj, $class, $ip);
		return true;
	}
	if (!fld_classes_exists($class)) return false;
	if (is_array($obj)) {
		if (sizeof($obj) < 1 || sizeof($obj) > COUNT_OBJ_ID) return false;
		foreach ($obj as $o) {
			if (!is_numeric($o) || $o < 0) return false;
		}
	} else {
		if (!is_numeric($obj) || $obj <= 0) return false;

		$obj = array($obj);
	}

	global $config;

	// récupère les informations d'origine
	$origin = new GA_Parse();

	if (trim($source) == '')
		$source = $origin->campaign_source;
	if (trim($name) == '')
		$name = $origin->campaign_name;
	if (trim($medium) == '')
		$medium = $origin->campaign_medium;
	if (trim($content) == '')
		$content = $origin->campaign_content;
	if (trim($term) == '')
		$term = $origin->campaign_term;
	if (trim($first_visit) == '')
		$first_visit = $origin->first_visit;
	if (trim($previous_visit) == '')
		$previous_visit = $origin->previous_visit;
	if (trim($current) == '')
		$current = $origin->current_visit_started;
	if (trim($time) == '')
		$time = $origin->times_visited;
	if (trim($pages) == '')
		$pages = $origin->pages_viewed;

	if ($name == '(referral)' && isset($_SESSION['http_referer'])) {
		$content = $_SESSION['http_referer'];
	}

	$cols = array('stats_tnt_id', 'stats_wst_id', 'stats_cls_id');
	$vals = array($config['tnt_id'], $config['wst_id'], $class);

	// clé de l'objet
	$i = 0;
	while ($i < sizeof($obj)) {
		$cols[] = 'stats_obj_id_' . $i;
		$vals[] = $obj[$i];
		$i++;
	}

	// enregistre l'adresse ip
	if (isset($_SERVER['REMOTE_ADDR'])) {
		$cols[] = 'stats_ip';
		$vals[] = '\'' . addslashes($_SERVER['REMOTE_ADDR']) . '\'';
	}

	// informations sur l'origine
	$cols[] = 'stats_source';
	$vals[] = '\'' . addslashes($source) . '\'';

	$cols[] = 'stats_name';
	$vals[] = '\'' . addslashes($name) . '\'';

	$cols[] = 'stats_medium';
	$vals[] = '\'' . addslashes($medium) . '\'';

	$cols[] = 'stats_content';
	$vals[] = '\'' . addslashes($content) . '\'';

	$cols[] = 'stats_term';
	$vals[] = '\'' . addslashes($term) . '\'';

	$cols[] = 'stats_first_visit';
	$vals[] = isdateheure($first_visit) ? '\'' . $first_visit . '\'' : 'null';

	$cols[] = 'stats_previous_visit';
	$vals[] = isdateheure($previous_visit) ? '\'' . $previous_visit . '\'' : 'null';

	$cols[] = 'stats_current_visit';
	$vals[] = isdateheure($current) ? '\'' . $current . '\'' : 'null';

	$cols[] = 'stats_times_visited';
	$vals[] = is_numeric($time) && $time > 0 ? $time : 'null';

	$cols[] = 'stats_pages_viewed';
	$vals[] = is_numeric($pages) && $pages > 0 ? $pages : 'null';

	$sql = '
		insert into stats_origins
			(' . implode(', ', $cols) . ')
		values
			(' . implode(', ', $vals) . ')
	';

	$res = ria_mysql_query($sql);

	if (ria_mysql_errno()) {
		error_log(mysql_error() . ' - ' . $sql);
	}
	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'origine d'une action
 *	\ingroup model_origins
 *	@param $obj Obligatoire, identifiant de l'objet (ou tableu d'identifiants pour les clés composées)
 *	@param $class Obligatoire, identifiant d'une classe d'object
 *	@param int $wst Optionnel, identifiant d'un site sur lequel filtrer le résultat
 *	@param string $date_start Optionnel, date de début d'une période
 *	@param string $date_end Optionnel, date de fin d'une période
 *	@param $check_host Optionnel, si oui on vérifie les resolutions d'ip
 *
 *    @return bool false en cas d'échec de récupération, sinon un résultat MySQL contenant :
 *                - wst_id : identifiant du site d'origine
 *                - source : source à l'origine de l'action
 *                - name : nom à l'origine de l'action
 *                - medium : support à l'origine de l'action
 *                - content : contenu à l'origine de l'action
 *                - term : terme utilisé à l'origine de l'action
 *                - pages_viewed : nombre de pages visitées avant l'action
 *                - first_visit : date de la première visite
 *                - previous_visit : date de la précédente visite
 *                - current_visit : date de la visite à l'origine de l'action
 *                - times_visited : temps de visite avant la réalisation de l'action
 *                - ip : IP utilisée lors du passage de commande
 *                - ip_host : résolution de l'adresse IP utilisée lors du passage de commande
 */
function stats_origins_get($obj, $class, $wst = 0, $date_start = false, $date_end = false, $check_host = true){
	return stats_origins_get_all($class, $obj, $wst, $date_start, $date_end, $check_host);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'origine d'une action
 *	\ingroup model_origins
 *	@param $class Obligatoire, identifiant d'une classe d'object
 *	@param $obj Obligatoire, identifiant de l'objet (ou tableu d'identifiants pour les clés composées)
 *	@param int $wst Optionnel, identifiant d'un site sur lequel filtrer le résultat
 *	@param string $date_start Optionnel, date de début d'une période
 *	@param string $date_end Optionnel, date de fin d'une période
 *	@param $check_host Optionnel, si oui on vérifie les resolutions d'ip
 *
 *    @return bool false en cas d'échec de récupération, sinon un résultat MySQL contenant :
 *                - wst_id : identifiant du site d'origine
 *                - source : source à l'origine de l'action
 *                - name : nom à l'origine de l'action
 *                - medium : support à l'origine de l'action
 *                - content : contenu à l'origine de l'action
 *                - term : terme utilisé à l'origine de l'action
 *                - pages_viewed : nombre de pages visitées avant l'action
 *                - first_visit : date de la première visite
 *                - previous_visit : date de la précédente visite
 *                - current_visit : date de la visite à l'origine de l'action
 *                - times_visited : temps de visite avant la réalisation de l'action
 *                - ip : IP utilisée lors du passage de commande
 *                - ip_host : résolution de l'adresse IP utilisée lors du passage de commande
 */
function stats_origins_get_all($class, $obj = false, $wst = 0, $date_start = false, $date_end = false, $check_host = true){
	if (!is_numeric($class) || $class <= 0) return false;

	if ($obj !== false) {
		if (is_array($obj)) {
			if (sizeof($obj) < 1 || sizeof($obj) > COUNT_OBJ_ID) return false;
			foreach ($obj as $o) {
				if (!is_numeric($o) || $o < 0) return false;
			}
		} else {
			if (!is_numeric($obj) || $obj <= 0) return false;

			$obj = array($obj);
		}
	}

	if ($date_start != false && !isdate($date_start)) return false;
	if ($date_end != false && !isdate($date_end)) return false;
	if (!is_numeric($wst) || $wst < 0) return false;
	global $config;

	$sql = '
		select stats_wst_id as wst_id, stats_source as source, stats_name as name, stats_medium as medium, stats_content as content,
			stats_term as term, stats_pages_viewed as pages_viewed, stats_first_visit as first_visit, stats_previous_visit as previous_visit,
			stats_current_visit as current_visit, stats_times_visited as times_visited, stats_ip as ip, stats_ip_host as ip_host, stats_obj_id_0 as obj_id_0, stats_obj_id_1 as obj_id_1, stats_obj_id_2 as obj_id_2
		from stats_origins
		where stats_tnt_id=' . $config['tnt_id'] . '
			and stats_cls_id=' . $class . '
	';

	if (is_array($obj)) {
		$i = 0;
		while ($i < sizeof($obj)) {
			$sql .= ' and stats_obj_id_' . $i . '=' . $obj[$i];
			$i++;
		}
	}

	if ($wst > 0) $sql .= ' and stats_wst_id=' . $wst;
	if ($date_start != false) $sql .= ' and date(stats_current_visit)>=\'' . $date_start . '\'';
	if ($date_end != false) $sql .= ' and date(stats_current_visit)<=\'' . $date_end . '\'';

	$result = ria_mysql_query($sql);

	if ($check_host && $result && ria_mysql_num_rows($result)) {
		$update_host = false;
		while ($r = ria_mysql_fetch_array($result)) {
			if (!$r['ip_host']) { // si la résolution de l'host n'a pas été faite alors on tente de la faire.
				$host = @gethostbyaddr($r['ip']);
				if ($host) {
					if (ria_mysql_query('	update stats_origins
										set stats_ip_host = \'' . addslashes($host) . '\'
										where stats_tnt_id=' . $config['tnt_id'] . '
											and stats_wst_id=' . $r['wst_id'] . '
											and stats_cls_id=' . $class . '
											and stats_obj_id_0 = ' . $r['obj_id_0'] . '
											and stats_obj_id_1 = ' . $r['obj_id_1'] . '
											and stats_obj_id_2 = ' . $r['obj_id_2']
					)) {
						$update_host = true;
					}
				}
			}
		}
		ria_mysql_data_seek($result, 0);

		if ($update_host) {
			return stats_origins_get($obj, $class, $wst, $date_start, $date_end, false);
		}
	}

	return $result;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de savoir si pour une action, on connait déjà l'origine.
 *	\ingroup model_origins
 *	@param $obj Obligatoire, identifiant de l'objet (ou tableau d'identifiants pour les clés composées)
 *	@param $class Obligatoire, identifiant d'une classe d'objet
 *	@return bool true si l'origine est connue, false dans le cas contraire
 */
function stats_origins_exists($obj, $class){
	if (!is_numeric($class) || $class <= 0) return false;
	if (is_array($obj)) {
		if (sizeof($obj) < 1 || sizeof($obj) > COUNT_OBJ_ID) return false;
		foreach ($obj as $o) {
			if (!is_numeric($o) || $o < 0) return false;
		}
	} else {
		if (!is_numeric($obj) || $obj <= 0) return false;

		$obj = array($obj);
	}

	global $config;

	$sql = '
		select 1
		from stats_origins
		where stats_tnt_id=' . $config['tnt_id'] . '
			and stats_wst_id=' . $config['wst_id'] . '
			and stats_cls_id=' . $class . '
	';

	$i = 0;
	while ($i < sizeof($obj)) {
		$sql .= ' and stats_obj_id_' . $i . ' = ' . $obj[$i];
		$i++;
	}

	$res = ria_mysql_query($sql);
	if (!$res || !ria_mysql_num_rows($res))
		return false;

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de rafraichir l'origine de la commande
 *	\ingroup model_origins
 *	@param $obj Obligatoire, identifiant de l'objet (ou tableau d'identifiants pour les clés composées)
 *	@param $class Obligatoire, identifiant d'une classe d'objet
 *	@param $ip Facultatif, Adresse Ip de l'internaute ayant effectué l'action
 *	@return bool false en cas d'échec et true dans le cas contraire
 */
function stats_origins_refresh( $obj, $class, $ip=null ){
	if( !is_numeric($class) ){
		return false;
	}

	global $config;

	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	} else {
		if( !is_numeric($obj) || $obj<=0 ) return false;

		$obj = array( $obj );
	}

	if( !$ip && isset($_SERVER['REMOTE_ADDR']) ) $ip = $_SERVER['REMOTE_ADDR'];

	$sql = '
		update stats_origins
		set stats_ip = \'' . addslashes($ip) . '\', stats_ip_host = null
		where stats_tnt_id=' . $config['tnt_id'] . '
			and stats_wst_id=' . $config['wst_id'] . '
			and stats_cls_id=' . $class . '
	';

	$i = 0;
	while ($i < sizeof($obj)) {
		$sql .= ' and stats_obj_id_' . $i . ' = ' . $obj[$i];
		$i++;
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer des statistiques de ventes pour un comparateur de prix.
 *
 *    @param $ctr Obligatoire, identifiant d'un comparateur de prix
 *    @param int $cat Optionnel, identifiant d'une catégorie de produit
 *    @param $prd Optionnel, identifiant d'un produit
 *    @param string $date_start Optionnel, date de début de récupération des statistiques
 *    @param string $date_end Optionnel, date de fin de récupération des statistiques
 *    @param $recursive Facultatif, si true (valeur par défaut) alors les statistiques retournées inclues les produits contenant dans les sous-catégories
 *    @param int $wst_id Optionnel, identifiant d'un site internet
 *
 *    @return bool false en cas d'échec de récupération des statistiques, sinon un tableau contenant :
 *                - clicks : nombre de clics
 *                - cost : coût total des différents clics
 *                - sales : nombre de ventes réalisées
 *                - ca : chiffre d'affaires généré HT
 *                - ca-ttc : chiffre d'affaires généré TTC
 *                - transfo : pourcentage de transformation d'un clics en commande
 *                - cost-sales : cout par vente
 */
function stats_comparators_get($ctr, $cat = 0, $prd = 0, $date_start = false, $date_end = false, $recursive = true, $wst_id = 0){
	if (!ctr_comparators_exists($ctr)) return false;
	if (!is_numeric($cat) || $cat < 0) return false;
	if (!is_numeric($prd) || $prd < 0) return false;
	if ($date_start && !isdate($date_start)) return false;
	if ($date_end && !isdate($date_end)) return false;
	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	global $config;

	if (!$wst_id) {
		if (isset($_SESSION['websitepicker'])) {
			if (is_numeric($_SESSION['websitepicker']) && $_SESSION['websitepicker'] > 0) {
				$wst_id = $_SESSION['websitepicker'];
			}
		}

		if (!$wst_id) {
			$wst_id = $config['wst_id'];
		}
	}

	$results = array('clicks' => 0, 'cost' => 0, 'sales' => 0, 'ca' => 0, 'ca-ttc' => 0, 'transfo' => 0, 'cost-sales' => 0, 'margin' => 0, 'roi' => 0);

	$params = ctr_params_get_array($ctr);

	// récupère le nombre de clics et le cout
	$sql = '
		select count( cct_datetime ) as clicks, sum( ifnull( if(old_cpc>0 , old_cpc, cat_cpc), 0 ) ) as cost
		from (
			select
				cct_tnt_id, cct_wst_id, cct_ctr_id, cct_datetime, cct_prd_id, cct_cat_id, cct_usr_id, cct_session, cat_cpc,
				(select prc_cpc from ctr_categories_prices where prc_ctr_id=cct_ctr_id and prc_cat_id=cct_cat_id and cct_datetime>=prc_date_start and cct_datetime<=prc_date_end) as old_cpc
			from ctr_clicks
				join ctr_categories on ( cat_id = cct_cat_id and cat_ctr_id = cct_ctr_id )
				join prd_classify on ( cly_tnt_id = cct_tnt_id and cly_prd_id = cct_prd_id )
	';

	if ($cat > 0 && $recursive)
		$sql .= ' join prd_cat_hierarchy as hry on ( cat_tnt_id = cly_tnt_id and cat_child_id = cly_cat_id )';

	$sql .= '
			where cct_tnt_id=' . $config['tnt_id'] . '
				and cct_ctr_id=' . $ctr . '
				and cct_wst_id=' . $wst_id . '
	';

	if ($date_start)
		$sql .= ' and date(cct_datetime)>=\'' . $date_start . '\'';
	if ($date_end)
		$sql .= ' and date(cct_datetime)<=\'' . $date_end . '\'';

	if ($cat > 0)
		$sql .= ' and ( ' . ($recursive ? 'hry.cat_parent_id =' . $cat . ' or' : '') . ' cly_cat_id = ' . $cat . ' )';
	if ($prd > 0)
		$sql .= ' and cly_prd_id=' . $prd;

	$sql .= '
			group by cct_tnt_id, cct_wst_id, cct_ctr_id, cct_datetime, cct_prd_id, cct_cat_id, cct_usr_id, cct_session
		) as result
	';

	$res = ria_mysql_query($sql);
	if (ria_mysql_errno()) {
		error_log(mysql_error() . ' - ' . $sql);
		return false;
	} elseif (!ria_mysql_num_rows($res)) {
		return false;
	}

	$results['clicks'] = ria_mysql_result($res, 0, 'clicks');
	$results['cost'] = ria_mysql_result($res, 0, 'cost');

	$rctr = ctr_comparators_get($ctr, false, false, null);
	if (!$rctr || !ria_mysql_num_rows($rctr)) return false;
	$c = ria_mysql_fetch_array($rctr);

	// récupère le nombre de vente

	if ($c['marketplace']) {
		$sql = '
			select count(ord_id) as sales, sum(ord_total_ht) as ca, sum(ord_total_ttc) as ca_ttc, sum(purchase) as sum_purchase
			from (
				select ord_id, ord_total_ht, ord_total_ttc,
				(
					select sum(if( ifnull( ifnull( op2.prd_purchase_avg, p2.prd_purchase_avg), 0 )=0, 0, ifnull( op2.prd_purchase_avg, p2.prd_purchase_avg)*prd_qte ))
					from prd_products as p2
						join ord_products as op2 on (op2.prd_tnt_id=p2.prd_tnt_id and op2.prd_id=p2.prd_id)
					where p2.prd_date_deleted is null and p2.prd_tnt_id=p.prd_tnt_id and op2.prd_ord_id=ord_id
				) as purchase
		';
	} else {
		$sql = '
			select
				count(*) as sales,
				sum(ord_total_ht) as ca,
				sum(ord_total_ttc) as ca_ttc,
				(
					select sum(if( ifnull( ifnull( op.prd_purchase_avg, p2.prd_purchase_avg), 0 )=0, 0, ifnull( op.prd_purchase_avg, p2.prd_purchase_avg)*prd_qte ))
					from prd_products as p2
					join ord_products as op on p2.prd_tnt_id=op.prd_tnt_id and p2.prd_id=op.prd_id
					where p2.prd_date_deleted is null and op.prd_ord_id=ord_id and p2.prd_tnt_id=ord_tnt_id
				) as sum_purchase
		';
	}

	$sql .= '
		from ord_orders
			join stats_origins on (stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id)
	';

	if ($c['marketplace']) {
		$sql .= ' join ord_products as p on (prd_tnt_id=stats_tnt_id and prd_ord_id=ord_id)';
	} else {
		$sql .= ' join prd_products as p on (prd_tnt_id=stats_tnt_id and prd_ref=stats_term)';
	}

	$sql .= '
		where ord_tnt_id=' . $config['tnt_id'] . ' and ord_wst_id=' . $wst_id . '
			and stats_source=\'' . ctr_comparators_get_source($ctr) . '\'
			and stats_cls_id=' . CLS_ORDER . '
			and ord_state_id in (' . implode(', ', ord_states_get_ord_valid()) . ')
			and ord_parent_id is null
	';

	if (!$c['marketplace'])
		$sql .= ' and prd_date_deleted is null';

	if ($date_start)
		$sql .= ' and date(ord_date)>=\'' . $date_start . '\'';
	if ($date_end)
		$sql .= ' and date(ord_date)<=\'' . $date_end . '\'';

	if ($cat > 0) {
		$sql .= '
			and (exists(
		';

		if ($recursive) {
			$sql .= '
				select 1
				from prd_classify
					join prd_cat_hierarchy on (cat_tnt_id=cly_tnt_id and cat_child_id=cly_cat_id)
				where (cat_parent_id=' . $cat . ' or cly_cat_id=' . $cat . ')
				and cly_prd_id=prd_id
				and cly_tnt_id = '.$config['tnt_id'].'
				) or exists (
			';
		}

		$sql .= '
				select 1
				from prd_classify
				where cly_prd_id=prd_id
					and cly_tnt_id='.$config['tnt_id'].'
					and cly_cat_id=' . $cat . '
			))
		';
	}

	if ($prd > 0)
		$sql .= ' and prd_id=' . $prd;

	if ($c['marketplace']) {
		$sql .= '
				group by ord_id
			) as results
		';
	}

	$res = ria_mysql_query($sql);
	if (ria_mysql_errno()) {
		error_log(mysql_error() . ' - ' . $sql);
		return false;
	} elseif (!ria_mysql_num_rows($res)) {
		return false;
	}

	$results['sales'] = ria_mysql_result($res, 0, 'sales');
	$results['ca'] = ria_mysql_result($res, 0, 'ca');
	$results['ca-ttc'] = ria_mysql_result($res, 0, 'ca_ttc');
	$results['sum_purchase'] = ria_mysql_result($res, 0, 'sum_purchase');
	if ($results['sales'] > 0) {
		$total_cost = $results['cost'] + $results['sum_purchase'];
		$results['margin'] = $results['ca'] - $total_cost;

		// vérifie si le comparateur / place de marché ne prends pas un taux de commission
		$rrate = ctr_params_get($ctr, 'margin');
		if ($rrate && ria_mysql_num_rows($rrate)) {
			$rate = 1 - (ria_mysql_result($rrate, 0, 'fld') / 100);

			// recalcule la marge
			$results['margin'] = ($results['ca'] * $rate) - $total_cost;
		}

		if ($total_cost <= 0) {
			$results['roi'] = 100;
		} else {
			$results['roi'] = $results['margin'] / $total_cost * 100;
		}
	} elseif ($results['cost'] > 0) {
		$results['margin'] = $results['ca'] - $results['cost'];

		// vérifie si le comparateur / place de marché ne prends pas un taux de commission
		$rrate = ctr_params_get($ctr, 'margin');
		if ($rrate && ria_mysql_num_rows($rrate)) {
			$rate = 1 - (ria_mysql_result($rrate, 0, 'fld') / 100);

			// recalcule la marge
			$results['margin'] = ($results['ca'] * $rate) - $results['cost'];
		}

		$results['roi'] = $results['margin'] / $results['cost'] * 100;
	} else {
		$results['roi'] = 0;
	}

	if ($results['sales'] > 0)
		$results['cost-sales'] = $results['cost'] / $results['sales'];

	if ($results['clicks'] > 0) {
		$results['transfo'] = ($results['sales'] / $results['clicks']) * 100;
	}

	return $results;
}
// \endcond

// \cond onlyria
/** Cette fonction détermine si une action fidélité a déjà été utilisé pour l'utilisateur
 * @param int $usr_id Optionnel, identifiant de l'utilisateur
 * @param $action Obligatoire, identifiant de l'action fidélité
 * @return bool Retourne true si l'action fidélité a déjà été utilisé pour l'utilisateur
 * @return bool Retourne false si l'action fidélité n'a pas été utilisé pour l'utilisateur
 * @return bool Retourne false si les paramètres ne sont pas valides
 */
function stats_rewards_action_is_used( $usr_id, $action ){
	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	if (!is_numeric($action) || $action <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select 1
		from stats_rewards
		where stats_tnt_id = '.$config['tnt_id'].'
			and stats_usr_id ='.$usr_id.'
			and (
				'.( $config['tnt_id'] == 13 && $action == 1 ? ' upper(stats_action) = "ADHESION" or' : '' ).'
				stats_rwa_id = '.$action.'
			)
	';

	$res = ria_mysql_query( $sql );
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le total des points de fidélité
 *    @param int $usr_id Optionnel, identifiant d'un compte client
 *    @return array Un tableau contenant :
 *            - pts : nombre de points total
 *            - amount : montant de remise total
 *            - used_pts : nombre de points utilisés
 *            - used_amount : montant de remise utilisée
 *            - neg_pts : nombre de points non utilisés
 *            - neg_amount : montant de remise non utilisée
 */
function stats_rewards_get_average( $usr_id=0 ){
	global $config;

	$stats_points_sql = ($config['rewards_use_decimal_points'] ? 'stats_points' : 'cast( stats_points as int)');
	$sql = '
		select
			sum(points) as pts,
			sum(points * conv / convert_pts) as amount,
			sum(used) as used_pts,
			sum(used * conv / convert_pts) as used_amount,
			sum(negative_points) as neg_pts,
			sum(negative_points * conv / convert_pts) as neg_amount
		from (
			select
				if( date(stats_date_limited)>=now(), ' .$stats_points_sql. ', 0 ) as points,
				if( stats_points < 0, ' .$stats_points_sql. ', 0) as used,
				if(date(stats_date_limited)<now(), ' .$stats_points_sql. ', 0 ) as negative_points,

				substring( stats_convert, 1, locate(\'/\', stats_convert)-1 ) as conv,
				substring( stats_convert, locate(\'/\', stats_convert)+1 ) as convert_pts
			from stats_rewards
			where stats_tnt_id=' . $config['tnt_id'] . '
				and stats_date_deleted is null
	';

	if ($usr_id > 0) {
		$sql .= '
				and stats_usr_id=' . $usr_id . '
		';
	}

	$sql .= '
		) as results
	';

	$ar_result = array(
		'pts' => 0,
		'amount' => 0,
		'used_pts' => 0,
		'used_amount' => 0,
		'neg_pts' => 0,
		'neg_amount' => 0
	);

	$res = ria_mysql_query($sql);
	if ($res && ria_mysql_num_rows($res)) {
		$r = ria_mysql_fetch_array($res);

		$ar_result = array(
			'pts' => $r['pts'],
			'amount' => $r['amount'],
			'used_pts' => $r['used_pts'] < 0 ? $r['used_pts'] * -1 : $r['used_pts'],
			'used_amount' => $r['used_amount'] < 0 ? $r['used_amount'] * -1 : $r['used_amount'],
			'neg_pts' => $r['neg_pts'],
			'neg_amount' => $r['neg_amount']
		);
	}

	return $ar_result;
}
// \endcond

/** Cette fonction permet de récupérer les informations sur les statistiques d'attribution de points de fidélité
 *
 *	@param int $usr Optionnel, identifiant d'un compte client sur lequel filtrer le résultat
 *	@param int $wst Optionnel, identifiant d'un site internet sur lequel filtrer le résultat
 *	@param $rwa Optionnel, identifiant ou tableau d'identifiants d'actions sur lesquels filtrer le résultat
 *	@param $cls Optionnel, identifiant de classe d'objet sur lequel filtrer le résultat
 *	@param $obj Optionnel, identifiant d'objet sur lequel filtrer le résultat
 *	@param string $date_start Optionnel, date de début sur laquelle filtrer le résultat
 *	@param string $date_end Optionnel, date de fin sur laquelle filtrer le résultat
 *	@param $godchild Optionnel, identifiant du compte filleul sur lequel filtrer le résultat
 *	@param string $date_limited Optionnel, date limite d'utilisation sur laquelle filtrer le resultat
 *	@param $prf_id Optionnel, identifiant du profil de l'utilisateur sur lequel filtrer le resultat
 *	@param $used Optionnel, ne permet de récupérer que les points utilisés
 *	@param $action Optionnel, permet d'inclure/exclure une action du solde de points (selon le nom donné)
 *	@param $is_exclude Optionnel, si l'action est inclue ou exclue du solde
 *	@param $sort Facultatif, tableau associatif contenant le tri souhaité. Utiliser en clé la colonne (ici, seul date est accepté) et en valeur la direction (asc ou desc).
 *	@param $id_reward Optionnel, identifiant d'un points de fidélité
 *	@param $rowstart Optionnel, numéro de la ligne de départ du résultat
 *	@param $maxrows Optionnel, nombre de lignes maximum à retourner
 *	@param $stat_id Optionnel, identifiant de la ligne de statistique
 *	@param $only_ids Optionnel, tableau d'identifiants de ligne de statistique à récupérer uniquement en fonction des autres paramètres
 *	@return resource Un résultat MySQL contenant :
 *		- stats_id : identifiant de la ligne de stat
 *		- rwa : identifiant de l'action rapportant les points de fidélité
 *		- name : intitulé de l'action
 *		- contrary : intitulé inverse de l'action (utile notamment quand le nombre de point est négatif)
 *		- usr : identifiant du compte profitant des points de fidélité
 *		- type_id : type d'adresse de facturation du compte
 *		- title_name : civilité du compte
 *		- firtname : prénom du compte
 *		- lastname : nom du compte
 *		- society : société du compte
 *		- address1 : adresse de facturation du compte
 *		- address2 : complément de l'adresse de facturation du compte
 *		- zipcode : code postal de l'adresse de facturation du compte
 *		- city : ville de l'adresse de facturation du compte
 *		- country : pays de l'adresse de facturation du compte
 *		- email : adresse mail du compte client
 *		- pts : nombre de points de fidélité gagné ou perdu
 *		- ratio : ratio utilisé pour le calculé du gain de point
 *		- convert : ratio de convertion des points
 *		- date_limit_en : format anglais de la date limite d'utilisation des points
 *		- date_limit : date limite d'utilisation des points
 *		- date_en : format anglais de la date d'obtension des points de fidélité
 *		- date : date d'obtension des points de fidélité
 *		- godchild : identifiant du compte filleul qui a permit au compte parrain de gagner ses points
 *		- type_amount : si le calcule de point a été fait selon le montant ht ou ttc de la commande
 *		- cls_id : identifiant de la classe d'objet lié aux points de fidélité
 */
function stats_rewards_get( $usr=0, $wst=0, $rwa=0, $cls=0, $obj=false, $date_start=false, $date_end=false, $godchild=0, $date_limited=false, $prf_id=0, $used=false, $action='', $is_exclude=false, $sort=false, $id_reward=0, $rowstart=0, $maxrows=-1, $stat_id=array(), $only_ids=false ){

	if (!is_numeric($usr) || $usr < 0) return false;
	if (!is_numeric($wst) || $wst < 0) return false;
	if ($date_start && !isdate($date_start)) return false;
	if ($date_end && !isdate($date_end)) return false;
	if ($date_limited && !isdate($date_limited)) return false;
	if (!is_numeric($cls) || $cls < 0) return false;
	if (!is_numeric($godchild) || $godchild < -1) return false;
	if (!is_numeric($prf_id) || $prf_id < -1) return false;

	$stat_id = control_array_integer($stat_id, false);
	if (false === $stat_id) {
		return false;
	}

	global $config;

	if ((is_numeric($rwa) && $rwa > 0) || is_array($rwa)) {
		if (!is_array($rwa)) {
			if (!is_numeric($rwa) || $rwa <= 0) return false;
			$rwa = array($rwa);
		} else {
			if (!sizeof($rwa)) return false;
			foreach ($rwa as $a) {
				if (!is_numeric($a) || $a <= 0)
					return false;
			}
		}
	}

	if ($obj !== false) {
		if (!is_array($obj)) {
			if (!is_numeric($obj) || $obj <= 0) return false;
			$obj = array($obj);
		} else {
			if (!sizeof($obj)) return false;
			foreach ($obj as $o) {
				if (!is_numeric($o) || $o <= 0)
					return false;
			}
		}
	}

	if (is_array($action)) {
		foreach ($action as $k => $a) {
			if (trim($a) == '') {
				return false;
			}

			$action[$k] = strtolower2($a);
		}
	} else {
		if (trim($action) == '') {
			$action = array();
		} else {
			$action = array(strtolower2($action));
		}
	}

	$sql = '
		select
			stats_id, if( ifnull(stats_convert, \'\')=\'\'
				, concat( substr(rwd_convert, locate(\'/\', rwd_convert)+1), \'/\', substr(rwd_convert, 1, locate(\'/\', rwd_convert)-1) )
				, stats_convert
			) as "convert",
			stats_wst_id as wst_id,

			stats_rwa_id as rwa, if(ifnull(stats_action, "")="", rwa_name, stats_action) as name,

			if(ifnull(stats_action, "")="", rwa_name_contrary, stats_action) as contrary, stats_usr_id as usr, adr_type_id as type_id, adr_title_id as title_id, title_name as title_name, adr_firstname as firstname,
			adr_lastname as lastname, adr_society as society, adr_address1 as address1, adr_address2 as address2, adr_postal_code as zipcode, adr_city as city,
			adr_country as country, usr_email as email,' . ($config['rewards_use_decimal_points'] ? 'stats_points' : 'cast( stats_points as int)') . ' as pts, stats_ratio as ratio, stats_date_limited as date_limit_en,

			stats_datetime as date_en,
			date_format(stats_date_limited,"%d/%m/%Y à %H:%i") as date_limit,
			date_format(stats_datetime,"%d/%m/%Y à %H:%i") as date,

			stats_date_created as date_created,

			stats_godchild as godchild, stats_type_amount as type_amount,
			stats_cls_id as cls_id, stats_obj_id_0 as obj_id_0, stats_obj_id_1 as obj_id_1, stats_obj_id_2 as obj_id_2
		from stats_rewards
			left join rwd_actions on (stats_rwa_id=rwa_id)
			join gu_users on ((stats_tnt_id=usr_tnt_id or 0 = usr_tnt_id) and stats_usr_id=usr_id)
			join gu_adresses on (usr_tnt_id=adr_tnt_id and usr_id=adr_usr_id)
			left join gu_titles on (adr_title_id=title_id)
			left join rwd_rewards on ( stats_tnt_id=rwd_tnt_id and usr_prf_id=rwd_prf_id and stats_wst_id=rwd_wst_id and rwd_date_deleted is null )
		where stats_tnt_id=' . $config['tnt_id'] . '
			and stats_date_deleted is null
			and usr_adr_invoices=adr_id
	';

	if (sizeof($stat_id) > 0)
		$sql .= ' and stats_id in(' . implode(',', $stat_id) . ')';
	if ($usr > 0)
		$sql .= ' and stats_usr_id=' . $usr;
	if ($wst > 0)
		$sql .= ' and stats_wst_id=' . $wst;

	if($id_reward > 0 ){
		$sql .=' and stats_id=' . $id_reward;
	}

	if (is_array($rwa) && sizeof($rwa))
		$sql .= ' and stats_rwa_id ' . ($is_exclude ? 'not in' : 'in') . ' (' . implode(',', $rwa) . ')';
	if (sizeof($action)) {
		$sql .= ' and (' . ($is_exclude ? 'ifnull(stats_action, "")="" or' : '') . ' lower(stats_action)' . ($is_exclude ? 'not in' : 'in') . ' ("' . implode('", "', $action) . '"))';
	}

	if ($date_start)
		$sql .= ' and date(stats_datetime)>=\'' . $date_start . '\'';
	if ($date_end)
		$sql .= ' and date(stats_datetime)<=\'' . $date_end . '\'';
	if ($date_limited)
		$sql .= ' and date(stats_date_limited) =\'' . $date_limited . '\'';
	if ($prf_id && $prf_id > 0)
		$sql .= ' and usr_prf_id =\'' . $prf_id . '\'';

	if ($cls)
		$sql .= ' and stats_cls_id=' . $cls;

	if (is_array($obj) && sizeof($obj)) {
		for ($i = 0; $i < sizeof($obj); $i++) {
			$sql .= ' and stats_obj_id_' . $i . ' = ' . $obj[$i];
		}
	}

	if ($godchild > 0)
		$sql .= ' and stats_godchild=' . $godchild;
	elseif ($godchild == -1)
		$sql .= ' and stats_godchild is null';

	if ($used) {
		$sql .= ' and stats_points<0';
	}

	$sort_final = array();

	if( is_array($sort) ){
		foreach ($sort as $col => $dir) {
			if (!in_array($dir, array('asc', 'desc'))) {
				$dir = 'asc';
			}

			switch ($col) {
				case 'date' :
					array_push($sort_final, 'stats_datetime ' . $dir);
					break;
			}
		}
	}


	// Ajoute la clause de tri
	if (sizeof($sort_final) == 0){
		$sort_final = array('stats_datetime desc');
	}

	if (is_array($sort_final) && sizeof($sort_final)) {
		$sql .= ' order by ' . implode(', ', $sort_final) . ' ';
	}

	// Gère la limitation des résultats
	if( !is_numeric($rowstart) || $rowstart < 0 ){
		$rowstart = 0;
	}
	if( !is_numeric($maxrows) ){
		$maxrows = -1;
	}
	$sql .= ' limit '.$rowstart.', '.( $maxrows>0 ? $maxrows : '18446744073709551615' );

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer les informations sur les statistiques d'attribution de points de fidélité
 *
 *	@param int $usr Optionnel, identifiant d'un compte client sur lequel filtrer le résultat
 *	@param int $wst Optionnel, identifiant d'un site internet sur lequel filtrer le résultat
 *	@param $rwa Optionnel, identifiant ou tableau d'identifiants d'actions sur lesquels filtrer le résultat
 *	@param $cls Optionnel, identifiant de classe d'objet sur lequel filtrer le résultat
 *	@param $obj Optionnel, identifiant d'objet sur lequel filtrer le résultat
 *	@param string $date_start Optionnel, date de début sur laquelle filtrer le résultat
 *	@param string $date_end Optionnel, date de fin sur laquelle filtrer le résultat
 *	@param $godchild Optionnel, identifiant du compte filleul sur lequel filtrer le résultat
 *	@param string $date_limited Optionnel, date limite d'utilisation sur laquelle filtrer le resultat
 *	@param $prf_id Optionnel, identifiant du profil de l'utilisateur sur lequel filtrer le resultat
 *	@param $used Optionnel, ne permet de récupérer que les points utilisés
 *	@param $action Optionnel, permet d'inclure/exclure une action du solde de points (selon le nom donné)
 *	@param $is_exclude Optionnel, si l'action est inclue ou exclue du solde
 *	@param $id_reward Optionnel, identifiant d'un points de fidélité
 *	@return Le nombre de lignes de mouvements de points de fidélité
 */
function stats_rewards_get_count( $usr=0, $wst=0, $rwa=0, $cls=0, $obj=false, $date_start=false, $date_end=false, $godchild=0, $date_limited=false, $prf_id=0, $used=false, $action='', $is_exclude=false, $id_reward=0 ){
	if (!is_numeric($usr) || $usr < 0) return false;
	if (!is_numeric($wst) || $wst < 0) return false;
	if ($date_start && !isdate($date_start)) return false;
	if ($date_end && !isdate($date_end)) return false;
	if ($date_limited && !isdate($date_limited)) return false;
	if (!is_numeric($cls) || $cls < 0) return false;
	if (!is_numeric($godchild) || $godchild < -1) return false;
	if (!is_numeric($prf_id) || $prf_id < -1) return false;

	global $config;

	if ((is_numeric($rwa) && $rwa > 0) || is_array($rwa)) {
		if (!is_array($rwa)) {
			if (!is_numeric($rwa) || $rwa <= 0) return false;
			$rwa = array($rwa);
		} else {
			if (!sizeof($rwa)) return false;
			foreach ($rwa as $a) {
				if (!is_numeric($a) || $a <= 0)
					return false;
			}
		}
	}

	if ($obj !== false) {
		if (!is_array($obj)) {
			if (!is_numeric($obj) || $obj <= 0) return false;
			$obj = array($obj);
		} else {
			if (!sizeof($obj)) return false;
			foreach ($obj as $o) {
				if (!is_numeric($o) || $o <= 0)
					return false;
			}
		}
	}

	if (is_array($action)) {
		foreach ($action as $k => $a) {
			if (trim($a) == '') {
				return false;
			}

			$action[$k] = strtolower2($a);
		}
	} else {
		if (trim($action) == '') {
			$action = array();
		} else {
			$action = array(strtolower2($action));
		}
	}

	$sql = '
		select count(*)
		from stats_rewards
		where stats_tnt_id=' . $config['tnt_id'] . '
			and stats_date_deleted is null
	';

	if ($usr > 0)
		$sql .= ' and stats_usr_id=' . $usr;
	if ($wst > 0)
		$sql .= ' and stats_wst_id=' . $wst;

	if($id_reward > 0 ){
		$sql .=' and stats_id=' . $id_reward;
	}

	if (is_array($rwa) && sizeof($rwa))
		$sql .= ' and stats_rwa_id ' . ($is_exclude ? 'not in' : 'in') . ' (' . implode(',', $rwa) . ')';
	if (sizeof($action)) {
		$sql .= ' and (' . ($is_exclude ? 'ifnull(stats_action, "")="" or' : '') . ' lower(stats_action)' . ($is_exclude ? 'not in' : 'in') . ' ("' . implode('", "', $action) . '"))';
	}

	if ($date_start)
		$sql .= ' and date(stats_datetime)>=\'' . $date_start . '\'';
	if ($date_end)
		$sql .= ' and date(stats_datetime)<=\'' . $date_end . '\'';
	if ($date_limited)
		$sql .= ' and date(stats_date_limited) =\'' . $date_limited . '\'';
	if ($prf_id && $prf_id > 0)
		$sql .= ' and usr_prf_id =\'' . $prf_id . '\'';

	if ($cls)
		$sql .= ' and stats_cls_id=' . $cls;

	if (is_array($obj) && sizeof($obj)) {
		for ($i = 0; $i < sizeof($obj); $i++) {
			$sql .= ' and stats_obj_id_' . $i . ' = ' . $obj[$i];
		}
	}

	if ($godchild > 0)
		$sql .= ' and stats_godchild=' . $godchild;
	elseif ($godchild == -1)
		$sql .= ' and stats_godchild is null';

	if ($used) {
		$sql .= ' and stats_points<0';
	}

	$rstats = ria_mysql_query( $sql );
	return mysql_result( $rstats, 0, 0 );
}

// \cond onlyria
/** Cette fonction permet d'enregistrer une action permettant de gagner des points de fidélité. L'enregistrement se fera aussi pour le parrain à ce moment là.
 *    Attention : cette information ne permet pas de sauvegarder directement les points de fidélités pour les commandes.
 *
 *    @param int $usr Optionnel, identifiant de l'utilisateur ayant réalisé l'action
 *    @param int $prf Optionnel, identifiant du profil de l'utilisateur ayant réalisé l'action
 *    @param $rwa Optionnel, identifiant d'une action
 *    @param $cancel Optionnel, par défaut il s'agit de la réalisation d'une action, mettre true pour une annulation d'action (dans ce cas le nombre de point seront multipliés par -1)
 *    @param string $name Optionnel, surcharge le nom de l'action (si aucun identifiant d'action n'est donné ni de nom, la fonction retournera False)
 *    @param $cls Optionnel, identifiant d'une classe d'object
 *    @param $obj Optionnel, identifiant de l'objet qui subit l'action
 *    @param $pts Optionnel, par défaut le système récupère le nombre de points pour l'action donnée en paramètre format accepté entier ou un tableau contenant array( points pour l'internaute, points pour son parrain)
 *    @param $ratio Optionnel, par défaut le système récupère le ratio actuel en place, sinon fournir à tableau array('amount'=>000, 'pts'=>000)
 *    @param $convert Optionnel, par défaut le système récupère le ratio de conversion en place, sinon fournir à tableau array('amount'=>000, 'pts'=>000)
 *    @param $days_limit Optionnel, nombre de jour de validiter des points par défaut on récupère celui en place acutellement (mettre 0 pour ne pas fixer de limite)
 *    @param $sponsor Optionnel, identifiant du sponsor/parrain
 *    @param string $date_created Optionnel, date de création surchargé
 *
 *    @return bool True si l'enregistrement s'est correctement passé (ou si les contraites liées à l'aquisition des points ne sont pas remplies), False dans le cas contraire
 */
function stats_rewards_add($usr = 0, $prf = 0, $rwa = 0, $cancel = false, $name = '', $cls = 0, $obj = false, $pts = false, $ratio = false, $convert = false, $days_limit = -1, $sponsor = 0, $date_created=null){
	global $config;

	// si le module de points de fidélité n'est pas activé, on enregistre aucune statistique
	if (!isset($config['rwd_reward_actived']) || !$config['rwd_reward_actived']) {
		return true;
	}

	if ($rwa > 0 && !rwd_actions_exists($rwa)) return false;
	if (!$rwa && trim($name) == '') return false;
	if ($cls > 0 && !fld_classes_exists($cls)) return false;
	if (!$cls && $obj !== false) return false;
	if( $date_created!==null && !isdateheure($date_created) ) return false;

	$usr = $usr > 0 ? $usr : (isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0);
	$prf = $prf > 0 ? $prf : (isset($_SESSION['usr_prf_id']) ? $_SESSION['usr_prf_id'] : 0);
	if ($prf == 0 && $usr > 0) {
		$prf = gu_users_get_prf($usr);
		if (!is_numeric($prf) || $prf <= 0) {
			return false;
		}
	}

	// vérification qu'une personne est bien connectée
	if (!gu_users_exists($usr)) return false;
	if ($prf > 0 && !gu_profiles_exists($prf)) return false;

	// Récupère la configuration en place
	$rrwd = rwd_rewards_get(0, $prf, $config['wst_id']);
	if (!$rrwd || !ria_mysql_num_rows($rrwd)) {
		return false;
	}

	$rwd = ria_mysql_fetch_assoc($rrwd);

	{ // Contrôle pour n'ajouter qu'une seule fois les points "Première commande"
		if (is_numeric($rwa) && $rwa > 0) {
			$rwa_first_order = rwd_actions_get_id_bycode('RWA_FIRST_ORDER');

			if ($rwa == $rwa_first_order) {
				$r_exists = ria_mysql_query('
					select 1 from stats_rewards
					where stats_tnt_id = '.$config['tnt_id'].'
						and stats_rwa_id = '.$rwa_first_order.'
						and stats_usr_id = '.$usr.'
						and stats_date_deleted is null
				');

				if ($r_exists && ria_mysql_num_rows($r_exists)) {
					return false;
				}
			}
		}
	}

	global $config;

	$ar_cols = array('stats_tnt_id', 'stats_wst_id', 'stats_datetime', 'stats_rwa_id', 'stats_usr_id');
	$ar_vals = array(
		'stats_tnt_id' => $config['tnt_id'],
		'stats_wst_id' => $config['wst_id'],
		'stats_datetime' => isdateheure($date_created) ? '\''.dateheureparse($date_created).'\'': 'now()',
		'stats_rwa_id' => $rwa ? $rwa : '0',
		'stats_usr_id' => $usr
	);

	// vérifie le paramètre contenant l'obj
	if ($obj !== false) {
		if (!is_array($obj)) {
			if (!is_numeric($obj) || $obj <= 0) return false;
			$obj = array($obj);
		} else {
			if (!sizeof($obj)) return false;
			foreach ($obj as $o) {
				if (!is_numeric($o) || $o < 0)
					return false;
			}
		}

		$ar_cols[] = 'stats_cls_id';
		for ($i = 0; $i < sizeof($obj); $i++) {
			$ar_cols[] = 'stats_obj_id_' . $i;
		}

		$ar_vals['stats_cls_id'] = $cls;
		for ($i = 0; $i < sizeof($obj); $i++) {
			$ar_vals['stats_obj_id_' . $i] = $obj[$i];
		}
	}

	// récupère le nombre de points
	if ($pts === false) {
		$pts = rwd_actions_get_points($rwa, $prf);
	}

	// vérifie le paramètre de points
	if (!is_array($pts)) {
		if (!is_numeric($pts) || $pts <= 0) return false;
		$pts = array($pts, 0);
	} else {
		if (sizeof($pts) != 2) return false;
		if (!is_numeric($pts[0]) || $pts[0] <= 0) return false;
		if (!is_numeric($pts[1]) || $pts[1] < 0) return false;
	}

	$ar_cols[] = 'stats_points';
	$ar_vals['stats_points'] = $cancel ? ($pts[0] * -1) : $pts[0];

	// récupère le ratio
	if (!$ratio) {
		$ratio = rwd_rewards_ratio_formated($rwd['ratio'], false);
	}

	// vérifie le ratio
	if (!is_array($ratio) || sizeof($ratio) != 2) return false;
	if (!isset($ratio['amount']) || !is_numeric($ratio['amount']) || $ratio['amount'] <= 0) return false;
	if (!isset($ratio['pts']) || !is_numeric($ratio['pts']) || $ratio['pts'] <= 0) return false;

	$ar_cols[] = 'stats_ratio';
	$ar_vals['stats_ratio'] = '\'' . addslashes($ratio['pts'] . '/' . $ratio['amount']) . '\'';

	if ($rwd['system'] == _RWD_SYSTEM_POINTS) {
		// récupère le ratio de conversion
		if (!$convert) {
			$convert = rwd_rewards_ratio_formated($rwd['convert'], true);
		}

		// vérifie le ratio de conversion
		if (!is_array($convert) || sizeof($convert) != 2) return false;
		if (!isset($convert['amount']) || !is_numeric($convert['amount']) || $convert['amount'] <= 0) return false;
		if (!isset($convert['pts']) || !is_numeric($convert['pts']) || $convert['pts'] <= 0) return false;

		$ar_cols[] = 'stats_convert';
		$ar_vals['stats_convert'] = '\'' . addslashes($convert['amount'] . '/' . $convert['pts']) . '\'';
	}

	if ($days_limit == -1) {
		$days_limit = rwd_rewards_get_days_limit($prf);
	}

	if (!is_numeric($days_limit) || $days_limit < 0) return false;

	if (trim($name)) {
		$ar_cols[] = 'stats_action';
		$ar_vals['stats_action'] = '\'' . addslashes($name) . '\'';
	}

	if ($days_limit != 0) {
		$time_start = isdateheure($date_created) ? strtotime($date_created) : time();
		$date_limit = date('Y-m-d 23:59:59', ($time_start + $days_limit * 86400));

		if ($cancel) {
			// récupère la plus grande date limite pour le compte du client
			$res = ria_mysql_query('
				select max(stats_date_limited) as "limit"
				from stats_rewards
				where stats_tnt_id=' . $config['tnt_id'] . '
					and stats_date_deleted is null
					and stats_usr_id=' . $usr . '
			');

			if (!$res || !ria_mysql_num_rows($res))
				return false;

			$date_limit = ria_mysql_result($res, 0, 'limit');
		}
	}


	$ar_cols[] = 'stats_date_limited';
	$ar_vals['stats_date_limited'] = $days_limit != 0 ? '\'' . $date_limit . '\'' : 'null';

	// enregistre l'information pour savoir si le calcule a été fait sur le montant ht ou ttc
	$type_amount = rwd_rewards_get_type_amount(0, $prf, $config['wst_id']);
	$ar_cols[] = 'stats_type_amount';
	$ar_vals['stats_type_amount'] = '\'' . $type_amount . '\'';

	$ar_cols[] = 'stats_date_created';
	$ar_vals['stats_date_created'] = 'now()';

	$sql = '
		insert into stats_rewards
			( ' . implode(', ', $ar_cols) . ' )
		values
			( ' . implode(', ', $ar_vals) . ' )
	';

	$res = ria_mysql_query($sql);

	if (ria_mysql_errno()) {
		error_log(mysql_error() . ' - ' . $sql);
		return false;
	}

	$id = ria_mysql_insert_id();

	// Contrôle la possibilité de créer un code promotion suite au gain de points de fidélité
	if (!$cancel) {
		rwd_rewards_create_promotion($usr);
	}

	// chargement des points pour le parrain (si le compte en a un)
	if (isset($pts[1]) && $pts[1] > 0) {
		if (!$sponsor) {
			$rsponsor = gu_sponsors_get($usr);
			if ($rsponsor && ria_mysql_num_rows($rsponsor)) {
				$sponsor = ria_mysql_result($rsponsor, 0, 'sponsor');
			}
		}

		if ($sponsor) {
			$ar_vals['stats_usr_id'] = $sponsor;
			$ar_vals['stats_points'] = $cancel ? ($pts[1] * -1) : $pts[1];

			$ar_cols[] = 'stats_godchild';
			$ar_vals['stats_godchild'] = $usr;

			$sql = '
				insert into stats_rewards
					( ' . implode(', ', $ar_cols) . ' )
				values
					( ' . implode(', ', $ar_vals) . ' )
			';

			$res = ria_mysql_query($sql);

			if (ria_mysql_errno())
				error_log(mysql_error() . ' - ' . $sql);
		}
	}

	if ($rwd['system'] == _RWD_SYSTEM_REMISE) {
		$solde = gu_users_get_rewards_balance( $usr );

		if ($solde >= $rwd['nb_pts']) {
			rwd_rewards_system_remise_send( $rwd, ria_mysql_fetch_assoc(gu_users_get($usr)) );
		}
	}

	if( !$res ){
		return false;
	}

	return $id;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre a jour un point de fidélité
 *  @param int $id Obligatoire, identifiant du point de fidélité
 *  @param string $name Optionnel, désignation du point de fidélité
 *  @param $pts Optionnel, nombre de points de fidélité
 *  @param string $date_limit Optionnel, date limite du point de fidélité
 *  @param $cancel Optionnel par defaut à false, si true permet de multiplier les point par moins -1
 *  @return bool True si la mise à jour s'est corretement déroulé, False dans le cas contraire
 */
function stats_rewards_update( $id, $name="", $pts=0, $date_limit=0,$cancel = false ){
	if( !is_numeric($id) || $id<=0 ){
		return false;
	}
	if( !is_numeric($pts) || $pts<0 ){
		return false;
	}
	$pts = $cancel ? ($pts * -1) : $pts;
	if($date_limit == 0){
		$date_limit = 'null';
	}else{
		$date_limit = '"'.$date_limit.'"';
	}
	$sql = '
			update stats_rewards
			set stats_action="'.$name.'",
			stats_points ='.$pts.',
			stats_date_limited ='.$date_limit.'
			where stats_id='.$id;
	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Alias de stats_rewards_update()
 *  @param int $id Obligatoire, identifiant du point de fidélité
 *  @param string $name Optionnel, désignation du point de fidélité
 *  @param $pts Optionnel, nombre de points de fidélité
 *  @param string $date_limit Optionnel, date limite du point de fidélité
 *  @param $cancel Optionnel par defaut à false, si true permet de multiplier les point par moins -1
 *  @return bool True si la mise à jour s'est corretement déroulé, False dans le cas contraire
 */
function stats_reward_update( $id, $name="", $pts=0, $date_limit=0,$cancel=false ){
	return stats_rewards_update( $id, $name, $pts, $date_limit,$cancel);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'un objet a déjà reçu des points de fidélité.
 *    @param $cls Obligatoire, identifiant d'une classe d'objet
 *    @param $obj Obligatoire, identifiant ou tableau d'identifiants d'un objet
 *    @param $rwa Optionnel, identifiant ou tableau d'identifiants d'une action
 *    @param $used Optionnel, permet de recherche une utilisation de points pour un objet
 *    @param $action Optionnel, surcharge le nom de l'action (si aucun identifiant d'action n'est donné ni de nom, la fonction retournera False)
 *    @param $exclude_action Optionnel, permet de'exclure les stats avec une certaine action ex 'REMBOURSEMENT' pour terredeviande
 * 	  @param $get_id Optionnel, par défaut à false, mettre true pour que cette fonction retour l'identifiant de la ligne correspondant au point
 *    @return bool True si l'objet a déjà reçu des points (ou stats_id si $get_id est égal à true), False dans le cas contraire
 */
function stats_rewards_objs_exists( $cls, $obj, $rwa=false, $used=false, $action='', $exclude_action='', $get_id=false ){
	if (!is_numeric($cls) && $cls <= 0) return false;
	if (!is_numeric($obj) && !is_array($obj)) return false;
	global $config;

	if (is_numeric($obj)) {
		if ($obj <= 0) return false;
		$obj = array($obj);
	} else {
		if (!sizeof($obj)) return false;
		foreach ($obj as $o) {
			if (!is_numeric($o) || $o <= 0)
				return false;
		}
	}

	if ($rwa !== false) {
		if (is_numeric($rwa)) {
			if ($rwa <= 0) return false;
			$rwa = array($rwa);
		} else {
			if (!sizeof($rwa)) return false;
			foreach ($rwa as $o) {
				if (!is_numeric($o) || $o <= 0)
					return false;
			}
		}
	}

	$sql = '
		select stats_id
		from stats_rewards
		where stats_tnt_id=' . $config['tnt_id'] . '
			and stats_date_deleted is null
			and stats_wst_id=' . $config['wst_id'] . '
			and stats_cls_id = '.$cls.'
	';

	for ($i = 0; $i < sizeof($obj); $i++) {
		$sql .= ' and stats_obj_id_' . $i . '=' . $obj[$i];
	}

	if ($rwa !== false) {
		$sql .= ' and stats_rwa_id in (' . implode(',', $rwa) . ')';
	}

	if ($used !== false) {
		$sql .= ' and stats_points<0';
	} else {
		$sql .= ' and stats_points>0';
	}

	if (trim($action) != '') {
		$sql .= ' and stats_action=\'' . addslashes($action) . '\'';
	}

	if( trim($exclude_action) != ''){
		$sql .= ' and stats_action!="'.addslashes($exclude_action).'" ';
	}

	$res = ria_mysql_query($sql);
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $get_id ? $r['stats_id'] : true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une ou plusieurs ligne de statistique de points de fidélité dirrectement avec son identifiant de ligne.
 *  @param $ids Obligatoire, identifiant ou tableau d'identifiants de ligne
 *  @return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function stats_rewards_del_by_ids( $ids ){
	$ids = control_array_integer( $ids, true );
	if ($ids === false) {
		return false;
	}

	global $config;

	return ria_mysql_query('
		update stats_rewards
		set stats_date_deleted = now()
		where stats_tnt_id = '.$config['tnt_id'].'
			and stats_id in ('.implode(', ', $ids).')
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une ligne de statistique de points de fidélité
 *    @param int $usr Obligatoire, identifiant du compte client
 *    @param $cls Obligatiore, type d'objet rapportant des points
 *    @param $obj Obligatoire, identifiant de l'objet en question
 *    @param $rwa Optionnel, par défaut on supprimer tous les points gagnés/perdu sur l'objet, mettre identifiant d'une action ou 0 pour une utilisation de points de fidélité
 *    @return bool True si la supperssion s'est correctement déroulée, False dans le cas contraire
 */
function stats_rewards_del($usr, $cls, $obj, $rwa = -1){
	if (!is_numeric($usr) || $usr <= 0) return false;
	if (!is_numeric($cls) && $cls <= 0) return false;
	if (!is_numeric($obj) && !is_array($obj)) return false;
	global $config;

	if (is_numeric($obj)) {
		if ($obj <= 0) return false;
		$obj = array($obj);
	} else {
		if (!sizeof($obj)) return false;
		foreach ($obj as $o) {
			if (!is_numeric($o) || $o <= 0)
				return false;
		}
	}

	$sql = '
		update stats_rewards
		set stats_date_deleted = now()
		where stats_tnt_id=' . $config['tnt_id'] . '
			and stats_wst_id=' . $config['wst_id'] . '
			and stats_usr_id=' . $usr . '
			and stats_cls_id=' . $cls . '
	';

	for ($i = 0; $i < sizeof($obj); $i++) {
		$sql .= ' and stats_obj_id_' . $i . '=' . $obj[$i];
	}

	if (is_numeric($rwa) && $rwa !== -1)
		$sql .= ' and stats_rwa_id=' . $rwa;

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction calcule des statistiques sur le nombre de produit vendus sur une période donnée.
 *    @param string $date_start Facultatif, date de début de la période (la partie horaire est autorisée mais non prise en compte).
 *    @param string $date_end Facultatif, date de fin de la période (la partie horaire est autorisée mais non prise en compte).
 *    @param $users Facultatif, identifiant ou tableau d'identifiants de clients (ne prend pas en compte le système de hiérarchie des utilisateurs).
 *    @param $categs Facultatif, identifiant ou tableau d'identifiants de catégories de produits.
 *    @param $prd_list Facultatif, identifiant ou tableau d'identifiants de produits. Si vide ou non spécifié, le résultat diffère (voir les valeurs possibles de retour).
 *    @param $inc_invoice_no_ord Facultatif, détermine si la requête prend en compte les lignes facturées sans commande d'origine (est ignoré pour la version avec $prd_list vide).
 *    @param $col_list Facultatif, permet de grouper les statistiques par conditionnement, et de filtrer ces conditionnements (tableau d'identifiants). Est ignoré si $prd_list ou $act_like_prd_col non spécifiés. 0 est considéré comme un identifiant (conditionnement unitaire).
 *    @param $sort_prd Facultatif, force un tri par identifiant de produit (si $prd_list activé).
 *    @param $act_like_prd_col Facultatif, si activé, le comportement est identique que $prd_list soit renseigné ou non (on le considère comme renseigné, ainsi que $col_list, et les valeurs de filtrage de $fields_spec).
 *    @param bool $publish Facultatif, permet de filtrer les produits publiés uniquement.
 *    @param $fields_spec Facultatif, liste de champs avancés complémentaires (pouvant être spécifiques au locataire) sur les lignes de commandes et de factures. Le comportement est identique aux conditionnements. Est ignoré si $prd_list ou $act_like_prd_col non spécifiés.
 *    La structure à fournir est la suivante :
 *        Une ligne par champ, chaque ligne est un tableau associatif dont la clé est l'alias SQL de la colonne retournée, al valeur est un tableau de 3 éléments (4 si $inc_invoice_no_ord est activé) :
 *            - Valeur par défaut du champ si NULL
 *            - Valeur de filtrage (NULL ou tableau vide pour ne pas filtrer, mais seulement si $act_like_prd_col activé). Si la valeur du champ est NULL pour la ligne, la comparaison s'effectue sur la valeur par défaut (fournie précédemment).
 *            - Identifiant du champ pour les lignes de commande (classe CLS_ORD_PRODUCT)
 *            - Si $inc_invoice_no_ord : Identifiant du champ pour les lignes de facture (classe CLS_INV_PRODUCT)
 *
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - si $prd_list = true :
 *            prd_id : Identifiant du produit.
 *            total_selled : Nombre de ventes du produit.
 *            col_id : Identifiant du conditionnement (si $col_list spécifié).
 *            [fields_spec_name] : Une colonne par alias de $fields_spec
 *        - si $prd_list = false :
 *            total : Nombre total de produits vendus.
 *            total_distinct : Nombre total de produits vendus distincts.
 *            total_unselled : Nombre total de produits invendus.
 *    @return bool False en cas d'échec.
 */
function stats_products_selled($date_start = false, $date_end = false, $users = array(), $categs = array(), $prd_list = false, $inc_invoice_no_ord = false, $col_list = array(), $sort_prd = false, $act_like_prd_col = false, $publish = false, $fields_spec = array()){

	if ($date_start !== false && !isdateheure($date_start)) {
		return false;
	}

	if ($date_end !== false && !isdateheure($date_end)) {
		return false;
	}

	$users = control_array_integer($users, false);
	if ($users === false) {
		return false;
	}

	$categs = control_array_integer($categs, false);
	if ($categs === false) {
		return false;
	}

	if ($prd_list === false) {
		$prd_list = 0;
	}

	if (is_array($prd_list)) {
		$prd_list = control_array_integer($prd_list, false);
		if ($prd_list === false) {
			return false;
		}
	}

	// paramètres annulés si $prd_list ou $act_like_prd_col non spécifié
	if (is_array($prd_list) && !sizeof($prd_list) && !$act_like_prd_col) {
		$col_list = array();
		$fields_spec = array();
	}

	$col_list = control_array_integer($col_list, false, true);
	if ($col_list === false) {
		return false;
	}

	if (!is_array($fields_spec)) {
		return false;
	}

	$ord_prd_fld = $inv_prd_fld = array();

	foreach ($fields_spec as $alias => $infos_fld) {

		if (!trim($alias)) {
			return false;
		}

		if (!is_array($infos_fld) || sizeof($infos_fld) != ($inc_invoice_no_ord ? 4 : 3)) {
			return false;
		}

		if (fld_fields_get_class($infos_fld[2]) != CLS_ORD_PRODUCT) {
			return false;
		}

		// filtrage obligatoire si $act_like_prd_col n'est pas activé
		if (!$act_like_prd_col && ($infos_fld[1] === null || (is_array($infos_fld[1]) && !sizeof($infos_fld[1])))) {
			return false;
		}

		$ord_prd_fld[$infos_fld[2]] = array(trim($alias), $infos_fld[0], $infos_fld[1]);

		if ($inc_invoice_no_ord) {
			if (fld_fields_get_class($infos_fld[3]) != CLS_INV_PRODUCT) {
				return false;
			}
			$inv_prd_fld[$infos_fld[3]] = array(trim($alias), $infos_fld[0], $infos_fld[1]);
		}

	}

	global $config;

	$dps = prd_deposits_get_main();
	if ($dps === false) {
		return false;
	}

	$states = array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_BL_READY, _STATE_BL_PARTIEL_EXP, _STATE_BL_EXP,
		_STATE_INVOICE, _STATE_PREPARATION, _STATE_BL_STORE, _STATE_PAY_WAIT_CONFIRM, _STATE_INV_STORE, _STATE_CLICK_N_COLLECT);

	$sub_sql = '
		select
			count(p.prd_id)
		from
			prd_products p
			left join prd_stocks as ps on p.prd_id = ps.sto_prd_id and p.prd_tnt_id = ps.sto_tnt_id and ps.sto_dps_id = ' . $dps . ' and sto_is_deleted=0
		where
			p.prd_id not in ( group_concat(prd.prd_id) )
			and p.prd_tnt_id = ' . $config['tnt_id'] . '
			and p.prd_date_deleted is null
			and p.prd_ref not in ("' . implode('", "', $config['dlv_prd_references']) . '")
			and (
				p.prd_sleep = 0 or ( ' . prd_stocks_get_sql('ps'). '-ifnull(ps.sto_prepa, 0) > 0 )
			)
	';

	$sql = '
		from
			ord_products op
			join ord_orders o on op.prd_ord_id = o.ord_id and op.prd_tnt_id = o.ord_tnt_id
			join gu_users usr on o.ord_usr_id = usr.usr_id and o.ord_tnt_id = if(usr.usr_tnt_id=0, o.ord_tnt_id, usr.usr_tnt_id)
			join prd_products as prd on op.prd_id = prd.prd_id and op.prd_tnt_id = prd.prd_tnt_id
	';
	if (sizeof($col_list) || $act_like_prd_col) {
		$sql .= '
			left join fld_object_values as v0 on
				op.prd_tnt_id = v0.pv_tnt_id and op.prd_ord_id = v0.pv_obj_id_0
				and op.prd_id = v0.pv_obj_id_1 and op.prd_line_id = v0.pv_obj_id_2
				and v0.pv_fld_id = ' . _FLD_PRD_COL_ORD_PRODUCT . ' and v0.pv_lng_code = "' . $config['i18n_lng'] . '"
			left join prd_colisage_types on
				v0.pv_tnt_id = col_tnt_id and cast(ifnull(v0.pv_value, "0") as unsigned) = col_id and col_is_deleted = 0
		';
	}
	$i = 0;
	foreach ($ord_prd_fld as $fld_id => $fld_data) {
		$i++;
		$sql .= '
			left join fld_object_values as v' . $i . ' on
				op.prd_tnt_id = v' . $i . '.pv_tnt_id and op.prd_ord_id = v' . $i . '.pv_obj_id_0
				and op.prd_id = v' . $i . '.pv_obj_id_1 and op.prd_line_id = v' . $i . '.pv_obj_id_2
				and v' . $i . '.pv_fld_id = ' . $fld_id . ' and v' . $i . '.pv_lng_code = "' . $config['i18n_lng'] . '"
		';
	}
	$sql .= '
		where
			op.prd_tnt_id = ' . $config['tnt_id'] . '
			and o.ord_state_id in (' . implode(', ', $states) . ')
			and usr.usr_date_deleted is null
			and prd.prd_date_deleted is null
			and prd.prd_ref not in ("' . implode('", "', $config['dlv_prd_references']) . '")
	';
	if ($publish) {
		$sql .= '
			and prd.prd_publish = 1 and prd.prd_publish_cat = 1 and (
				prd.prd_sleep = 0 or ifnull((
					select ' . prd_stocks_get_sql() . ' - sto_prepa from prd_stocks
					where sto_tnt_id = prd.prd_tnt_id and sto_prd_id = prd.prd_id and sto_dps_id = ' . $dps . ' and sto_is_deleted=0
				), 0) > 0
			)
		';
	}

	$sql_inv = '
		from
			ord_inv_products ip
			join ord_invoices i on ip.prd_inv_id = i.inv_id and ip.prd_tnt_id = i.inv_tnt_id
			join gu_users usr on i.inv_usr_id = usr.usr_id and i.inv_tnt_id = if(usr.usr_tnt_id=0, i.inv_tnt_id, usr.usr_tnt_id)
			join prd_products as prd on ip.prd_id = prd.prd_id and ip.prd_tnt_id = prd.prd_tnt_id
	';
	if (sizeof($col_list) || $act_like_prd_col) {
		$sql_inv .= '
			left join fld_object_values as v0 on
				ip.prd_tnt_id = v0.pv_tnt_id and ip.prd_inv_id = v0.pv_obj_id_0
				and ip.prd_id = v0.pv_obj_id_1 and ip.prd_line_id = v0.pv_obj_id_2
				and v0.pv_fld_id = ' . _FLD_PRD_COL_INV_PRODUCT . ' and v0.pv_lng_code = "' . $config['i18n_lng'] . '"
			left join prd_colisage_types on
				v0.pv_tnt_id = col_tnt_id and cast(ifnull(v0.pv_value, "0") as unsigned) = col_id and col_is_deleted = 0
		';
	}
	$i = 0;
	foreach ($inv_prd_fld as $fld_id => $fld_data) {
		$i++;
		$sql_inv .= '
			left join fld_object_values as v' . $i . ' on
				ip.prd_tnt_id = v' . $i . '.pv_tnt_id and ip.prd_inv_id = v' . $i . '.pv_obj_id_0
				and ip.prd_id = v' . $i . '.pv_obj_id_1 and ip.prd_line_id = v' . $i . '.pv_obj_id_2
				and v' . $i . '.pv_fld_id = ' . $fld_id . ' and v' . $i . '.pv_lng_code = "' . $config['i18n_lng'] . '"
		';
	}
	$sql_inv .= '
		where
			ip.prd_tnt_id = ' . $config['tnt_id'] . '
			and usr.usr_date_deleted is null
			and prd.prd_date_deleted is null
			and prd.prd_ref not in ("' . implode('", "', $config['dlv_prd_references']) . '")
			and ip.prd_ord_id is null
			and i.inv_masked = 0
	';
	if ($publish) {
		$sql_inv .= '
			and prd.prd_publish = 1 and prd.prd_publish_cat = 1 and (
				prd.prd_sleep = 0 or ifnull((
					select ' . prd_stocks_get_sql(). ' - sto_prepa from prd_stocks
					where sto_tnt_id = prd.prd_tnt_id and sto_prd_id = prd.prd_id and sto_dps_id = ' . $dps . ' and sto_is_deleted=0
				), 0) > 0
			)
		';
	}

	if (is_array($prd_list) && sizeof($prd_list)) {
		$sql .= ' and prd.prd_id in (' . implode(', ', $prd_list) . ')';
		$sql_inv .= ' and prd.prd_id in (' . implode(', ', $prd_list) . ')';
	}

	if (sizeof($col_list)) {
		$sql .= ' and ifnull(col_id, 0) in (' . implode(', ', $col_list) . ')';
		$sql_inv .= ' and ifnull(col_id, 0) in (' . implode(', ', $col_list) . ')';
	}

	$i = 0;
	foreach ($ord_prd_fld as $fld_id => $fld_data) {
		$i++;
		if ($fld_data[2] !== null && (!is_array($fld_data[2]) || sizeof($fld_data[2]))) {
			$sql .= ' and ifnull(v' . $i . '.pv_value, "' . addslashes($fld_data[1]) . '") ';
			if (is_array($fld_data[2])) {
				$tmp_ar = array();
				foreach ($fld_data[2] as $one_v) {
					$tmp_ar[] = '"' . addslashes($one_v) . '"';
				}
				$sql .= ' in (' . implode(', ', $tmp_ar) . ')';
			} else {
				$sql .= ' = "' . addslashes($fld_data[2]) . '"';
			}
		}
	}
	$i = 0;
	foreach ($inv_prd_fld as $fld_id => $fld_data) {
		$i++;
		if ($fld_data[2] !== null && (!is_array($fld_data[2]) || sizeof($fld_data[2]))) {
			$sql_inv .= ' and ifnull(v' . $i . '.pv_value, "' . addslashes($fld_data[1]) . '") ';
			if (is_array($fld_data[2])) {
				$tmp_ar = array();
				foreach ($fld_data[2] as $one_v) {
					$tmp_ar[] = '"' . addslashes($one_v) . '"';
				}
				$sql_inv .= ' in (' . implode(', ', $tmp_ar) . ')';
			} else {
				$sql_inv .= ' = "' . addslashes($fld_data[2]) . '"';
			}
		}
	}

	if ($date_start !== false) {
		$sql .= ' and date(o.ord_date) >= date("' . dateheureparse($date_start) . '")';
		$sql_inv .= ' and date(i.inv_date) >= date("' . dateheureparse($date_start) . '")';
	}

	if ($date_end !== false) {
		$sql .= ' and date(o.ord_date) <= date("' . dateheureparse($date_end) . '")';
		$sql_inv .= ' and date(i.inv_date) <= date("' . dateheureparse($date_end) . '")';
	}

	if (sizeof($categs)) {
		$tmp = '
			select cly_prd_id
			from prd_classify
			where cly_cat_id in (' . implode(', ', $categs) . ') and cly_tnt_id = ' . $config['tnt_id'] . '

			union all

			select cly_prd_id
			from prd_cat_hierarchy
				join prd_classify on cat_child_id = cly_cat_id and cly_tnt_id = cat_tnt_id
			where cat_parent_id in (' . implode(', ', $categs) . ') and cly_tnt_id = ' . $config['tnt_id'] . '
		';

		$sub_sql .= ' and p.prd_id in (' . $tmp . ')';
		$sql .= '  and op.prd_id in (' . $tmp . ')';
		$sql_inv .= '  and ip.prd_id in (' . $tmp . ')';
	}

	if (sizeof($users)) {
		$sql .= ' and o.ord_usr_id in ( ' . implode(', ', $users) . ' )';
		$sql_inv .= ' and i.inv_usr_id in ( ' . implode(', ', $users) . ' )';
	}

	$columns_ord = $columns_inv = array();

	if ($prd_list === true || (is_array($prd_list) && sizeof($prd_list)) || $act_like_prd_col) {
		$columns_ord['prd_id'] = 'op.prd_id';
		$columns_inv['prd_id'] = 'ip.prd_id';
	}

	if (sizeof($col_list) || $act_like_prd_col) {
		$columns_ord['col_id'] = 'ifnull(col_id, 0)';
		$columns_inv['col_id'] = 'ifnull(col_id, 0)';
	}

	$i = 0;
	foreach ($ord_prd_fld as $fld_id => $fld_data) {
		$i++;
		if ($act_like_prd_col || ($fld_data[2] !== null && (!is_array($fld_data[2]) || sizeof($fld_data[2])))) {
			$columns_ord[$fld_data[0]] = 'ifnull(v' . $i . '.pv_value, "' . addslashes($fld_data[1]) . '")';
		}
	}

	$i = 0;
	foreach ($inv_prd_fld as $fld_id => $fld_data) {
		$i++;
		if ($act_like_prd_col || ($fld_data[2] !== null && (!is_array($fld_data[2]) || sizeof($fld_data[2])))) {
			$columns_inv[$fld_data[0]] = 'ifnull(v' . $i . '.pv_value, "' . addslashes($fld_data[1]) . '")';
		}
	}

	if (sizeof($columns_ord)) {
		$sql .= ' group by ' . implode(', ', $columns_ord);
	}

	if (sizeof($columns_inv)) {
		$sql_inv .= ' group by ' . implode(', ', $columns_inv);
	}

	if ($prd_list === true || (is_array($prd_list) && sizeof($prd_list)) || $act_like_prd_col) {
		$columns_ord['total_selled'] = 'sum(op.prd_qte)';
		$columns_inv['total_selled'] = 'sum(ip.prd_qte)';
		if (sizeof($col_list) || $act_like_prd_col) {
			$columns_ord['total_selled'] = 'sum(op.prd_qte) / ifnull(col_qte, 1)';
			$columns_inv['total_selled'] = 'sum(ip.prd_qte) / ifnull(col_qte, 1)';
		}

		if (!$config['use_decimal_qte']) {
			$columns_ord['total_selled'] = 'cast(' . $columns_ord['total_selled'] . ' as signed)';
			$columns_inv['total_selled'] = 'cast(' . $columns_inv['total_selled'] . ' as signed)';
		}

		// transforme le clé/valeur en valeur
		$ar_select_ord = $ar_select_inv = array();
		foreach ($columns_ord as $cord_k => $cord_v) {
			$ar_select_ord[] = $cord_v . ' as ' . $cord_k;
		}
		foreach ($columns_inv as $cinv_k => $cinv_v) {
			$ar_select_inv[] = $cinv_v . ' as ' . $cinv_k;
		}

		$sql = 'select ' . implode(', ', $ar_select_ord) . ' ' . $sql;
		$sql_inv = 'select ' . implode(', ', $ar_select_inv) . ' ' . $sql_inv;
	} else {
		$sql = 'select ifnull(sum(op.prd_qte),0) as total, count(distinct(op.prd_id)) as total_distinct, (' . $sub_sql . ') as total_unselled ' . $sql;
	}

	// fusion des deux requêtes
	if (($prd_list === true || (is_array($prd_list) && sizeof($prd_list)) || $act_like_prd_col) && $inc_invoice_no_ord) {
		unset($columns_ord['total_selled']);
		$sql = '
			select ' . implode(',', array_keys($columns_ord)) . ', sum(total_selled) as total_selled
			from (
				' . $sql . '
				union all
				' . $sql_inv . '
			) as subquery
			group by ' . implode(',', array_keys($columns_ord)) . '
		';
	}

	if ($prd_list === true || (is_array($prd_list) && sizeof($prd_list)) || $act_like_prd_col) {
		if ($sort_prd) {
			$sql .= ' order by prd_id';
		} else {
			$sql .= ' order by total_selled desc';
		}
	}

	$res = ria_mysql_query($sql);

	if (ria_mysql_errno()) {
		//die('erreur stats_products_selled() : '.mysql_error()."\r\n".$sql);
		error_log('erreur stats_products_selled() : ' . mysql_error() . "\r\n" . $sql);
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction permet de retourner le nombre de produit vendu sur une période par maques
 *    @param string $date_start Facultatif, date de début de la période
 *    @param string $date_end Facultatif, date de fin de la période
 *    @param $users Facultatif, tableau contenant les identifiants des personnes concerné ( ne prend pas en compte automatiquement la hierarchie des utilisateurs )
 *    @param $categs Facultatif, tableau contenant les identifiants des catégories concernés les sous catégories seront incluses
 *
 *    @return resource Un résultat de requete sql de la forme suivante :
 *            id : Identifiant de la marque
 *            name : Nom de la marque
 *            total_selled : Nombre de vente du produit
 */
function stats_brands_selled($date_start = false, $date_end = false, $users = array(), $categs = array()){
	if ($date_start && !isdate($date_start)) return false;
	if ($date_end && !isdate($date_end)) return false;
	global $config;

	$sql = 'select b.brd_id as id, b.brd_name as name, ' . ($config['use_decimal_qte'] ? 'sum( op.prd_qte )' : 'cast(sum( op.prd_qte ) as signed)') . ' as total_selled ';
	$sql .= '
			from ord_products as op
			join ord_orders as o on op.prd_ord_id = o.ord_id and o.ord_tnt_id = op.prd_tnt_id
			join prd_products as p on op.prd_id = p.prd_id and op.prd_tnt_id = p.prd_tnt_id
			join prd_brands as b on p.prd_brd_id = b.brd_id and p.prd_tnt_id = b.brd_tnt_id
			join gu_users usr on o.ord_usr_id = usr.usr_id and usr.usr_tnt_id = o.ord_tnt_id

			where op.prd_tnt_id = ' . $config['tnt_id'] . '
			  and o.ord_state_id in (3, 4, 5, 6, 7, 8 ,11, 24, ' . _STATE_INV_STORE . ')
			  and usr.usr_date_deleted is null
			  and b.brd_date_deleted is null
			  and p.prd_date_deleted is null
			';

	if ($date_start)
		$sql .= ' and date(o.ord_date)>=\'' . $date_start . '\'';

	if ($date_end)
		$sql .= ' and date(o.ord_date)<=\'' . $date_end . '\'';

	if (is_array($categs) && sizeof($categs)) {
		foreach ($categs as $c) if (!is_numeric($c)) return false;

		$sql .= '  and op.prd_id in (
						select cly_prd_id from prd_classify
							where cly_cat_id in ( ' . implode(',', $categs) . ' ) and cly_tnt_id=op.prd_tnt_id
						union all
						select cly_prd_id from prd_cat_hierarchy join prd_classify
							on cat_child_id=cly_cat_id and cly_tnt_id=cat_tnt_id
							where cat_parent_id in ( ' . implode(',', $categs) . ' ) and cly_tnt_id=op.prd_tnt_id
							)';
	}

	if (is_array($users) && sizeof($users)) {
		foreach ($users as $u) if (!is_numeric($u)) return false;

		$sql .= ' and o.ord_usr_id in ( ' . implode(',', $users) . ' )';
	}

	$sql .= 'group by b.brd_id
			 order by total_selled desc';


	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retourner le nombre de produit vendu sur une période par valeur de champs avancés
 *    @param $fld_id Facultatif, identifiant du champ
 *    @param $fld_val Facultatif, valeur du champ
 *    @param string $date_start Facultatif, date de début de la période
 *    @param string $date_end Facultatif, date de fin de la période
 *    @param $users Facultatif, tableau contenant les identifiants des personnes concerné ( ne prend pas en compte automatiquement la hierarchie des utilisateurs )
 *    @param $categs Facultatif, tableau contenant les identifiants des catégories concernés les sous catégories seront incluses
 *
 *    @return resource Un résultat de requête sql de la forme suivante :
 *            id : Identifiant du champ
 *            name : Nom de la v
 *            total_selled : Nombre de vente du produit
 */
function stats_field_selled($fld_id, $fld_val, $date_start = false, $date_end = false, $users = array(), $categs = array()){
	if ($date_start && !isdate($date_start)) return false;
	if ($date_end && !isdate($date_end)) return false;
	if (!$fld_id || !is_numeric($fld_id)) return false;
	if (trim($fld_val) === '') return false;
	global $config;


	$sql = 'select fld.pv_fld_id as id, \'' . addslashes($fld_val) . '\' as name, ' . ($config['use_decimal_qte'] ? 'sum( op.prd_qte )' : 'cast(sum( op.prd_qte ) as signed)') . ' as total_selled ';
	$sql .= '
			from ord_products as op
			join ord_orders as o on op.prd_ord_id = o.ord_id and o.ord_tnt_id = op.prd_tnt_id
			join fld_object_values as fld on op.prd_id = fld.pv_obj_id_0 and op.prd_tnt_id = fld.pv_tnt_id
			join gu_users usr on o.ord_usr_id = usr.usr_id and usr.usr_tnt_id = o.ord_tnt_id
			join prd_products as p on op.prd_id=p.prd_id and op.prd_tnt_id=p.prd_tnt_id

			where op.prd_tnt_id = ' . $config['tnt_id'] . '
			  and o.ord_state_id in (3, 4, 5, 6, 7, 8 ,11, 24, ' . _STATE_INV_STORE . ')
			  and usr.usr_date_deleted is null
			  and p.prd_date_deleted is null
			  and fld.pv_fld_id = ' . $fld_id . '
			  and (
				fld.pv_value = \'' . addslashes($fld_val) . '\'
				or
				fld.pv_value like \'' . addslashes($fld_val) . ', %\'
				or
				fld.pv_value like \'%, ' . addslashes($fld_val) . ', %\'
				or
				fld.pv_value like \'%, ' . addslashes($fld_val) . '\'
			  )
			';

	if ($date_start)
		$sql .= ' and date(o.ord_date)>=\'' . $date_start . '\'';

	if ($date_end)
		$sql .= ' and date(o.ord_date)<=\'' . $date_end . '\'';

	if (is_array($categs) && sizeof($categs)) {
		foreach ($categs as $c) if (!is_numeric($c)) return false;

		$sql .= '  and op.prd_id in (
						select cly_prd_id from prd_classify
							where cly_cat_id in ( ' . implode(',', $categs) . ' ) and cly_tnt_id=op.prd_tnt_id
						union all
						select cly_prd_id from prd_cat_hierarchy join prd_classify
							on cat_child_id=cly_cat_id and cly_tnt_id=cat_tnt_id
							where cat_parent_id in ( ' . implode(',', $categs) . ' ) and cly_tnt_id=op.prd_tnt_id
							)';
	}

	if ($users && sizeof($users)) {
		foreach ($users as $u) if (!is_numeric($u)) return false;

		$sql .= ' and o.ord_usr_id in ( ' . implode(',', $users) . ' )';
	}

	$sql .= '
			 group by fld.pv_fld_id
			 order by total_selled desc';


	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction génère des statistiques sur l'utilisation des moyens de paiement
 *    Ces statistiques ne prennent pas en compte les paniers non terminés
 *    @param string $date_start Optionnel, date de début de prise en compte des statistiques
 *    @param string $date_end Optionnel, date de fin de prise en compte des statistiques
 *    @param $group Optionnel, option(s) de regroupement par type de période. Cet argument peut être un tableau. Les valeurs autorisées sont "hour", "day", "week", "month", "year"
 *    @param $tenant Optionnel, par défaut les statistiques sont pour le locataire courant, mettre False pour retourner des résultats génériques
 *    @param int|array $wst Optionnel, identifiant ou tableau d'identifiants de site (si $tenant = true uniquement)
 *    @param $sort Optionnel, tableau associatif de tri. Les colonnes autorisées sont les alias du résultat MySQL
 *
 *    @return bool False en cas d'échec
 *    @return Un résulat de requête MySQL comprenant les colonnes suivantes :
 *        - pay_id : Identifiant du moyen de paiement
 *        - pay_name : Intitulé du moyen de paiement
 *        - count : Nombre de commandes pour ce moyen de paiement
 *        - total_ht : Chiffre d'affaire HT pour ce moyen de paiement
 *        - total_ttc : Chiffre d'affaire TTC pour ce moyen de paiement
 *        - month, year, ... : valeur de chaque période définie dans $group
 */
function stats_ord_payment_types_get($date_start = false, $date_end = false, $group = '', $tenant = true, $wst = 0, $sort = false){

	{ // contrôle des paramètres
		$allowed = array('hour', 'day', 'week', 'month', 'year');
		if (is_array($group)) {
			$temp = array();
			foreach ($group as $g) {
				if (in_array(strtolower(trim($g)), $allowed)) {
					$temp[] = strtolower(trim($g));
				}
			}
			$group = $temp;
		} else {
			if (in_array(strtolower(trim($group)), $allowed)) {
				$group = array(strtolower(trim($group)));
			} else {
				$group = array();
			}
		}
		$group = array_unique($group);

		$sort_final = array();
		if (is_array($sort)) {
			$base_cols = array('pay_id', 'pay_name', 'count', 'total_ht', 'total_ttc');
			foreach ($sort as $col => $dir) {
				$dir = strtolower(trim($dir)) == 'asc' ? 'asc' : 'desc';
				$col = strtolower(trim($col));
				if (in_array($col, $base_cols) || in_array($col, $group)) {
					if (!array_key_exists($col, $sort_final)) {
						$sort_final[$col] = $dir;
					}
				}
			}
		}

		$wst = control_array_integer($wst, false);
		if ($wst === false) {
			return false;
		}
	}

	$states = array(_STATE_WAIT_PAY, _STATE_PAY_CONFIRM, _STATE_IN_PROCESS, _STATE_BL_READY, _STATE_BL_PARTIEL_EXP, _STATE_BL_EXP, _STATE_INVOICE, _STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND, _STATE_PREPARATION, _STATE_ARCHIVE, _STATE_BL_STORE, _STATE_PAY_WAIT_CONFIRM, _STATE_INV_STORE, _STATE_CLICK_N_COLLECT);

	$sql = '
		select
			pay_id,
			pay_name,
			count(*) as "count",
			sum(ord_total_ht) as "total_ht",
			sum(ord_total_ttc) as "total_ttc"
	';
	foreach ($group as $g) {
		$sql .= ', ' . $g . '(ord_date) as "' . $g . '"';
	}
	$sql .= '
		from ord_orders
			join ord_payment_types on (pay_id=ord_pay_id and pay_tnt_id in (0, ord_tnt_id) and pay_is_deleted=0)
		where ord_state_id in (' . implode(', ', $states) . ')
			and ord_masked=0
			and ord_parent_id is null
	';

	if ($tenant) {
		global $config;
		$sql .= ' and ord_tnt_id=' . $config['tnt_id'];

		if (is_array($wst) && sizeof($wst)) {
			$sql .= ' and ord_wst_id in (' . implode(', ', $wst) . ')';
		}
	}

	if (isdate($date_start)) {
		$sql .= ' and ord_date >= "' . dateparse($date_start) . '"';
	}
	if (isdate($date_end)) {
		$sql .= ' and ord_date <= "' . dateparse($date_end) . ' 23:59:59"';
	}
	$sql .= '
		group by pay_id, pay_name
	';
	foreach ($group as $g) {
		$sql .= ', ' . $g . '(ord_date)';
	}

	if (sizeof($sort_final)) {
		$sql .= ' order by ';
		$first = true;
		foreach ($sort_final as $col => $dir) {
			if ($first) {
				$first = false;
			} else {
				$sql .= ', ';
			}
			$sql .= $col . ' ' . $dir;
		}
	}

	$r = ria_mysql_query($sql);

	if (ria_mysql_errno()) {
		error_log(mysql_error() . ' - ' . $sql);
	}
	return $r;
}
// \endcond

// \cond onlyria
/**	Cette fonction réalise une comparaison entre deux origines de commandes et retourne une valeur utilisée pour trier
 *	le tableau (par la fonction uasort) de façon décroissante en fonction du nombre de commandes.
 *	\ingroup model_origins
 *	@param $a Origine de commande (tableau associatif), tel que retourné par stats_origins_get_stats
 *	@param $b Origine de commande (tableau associatif), tel que retourné par stats_origins_get_stats
 *	@return -1 si la valeur $a doit être classée avant $b, 1 si elle doit être classée après $b et 0 si les deux valeurs sont égales.
 */
function stats_compare_origins( $a, $b ){
	if( $a['count']>$b['count'] ){
		return -1;
	}elseif( $a['count']<$b['count'] ){
		return 1;
	}
	return 0;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer la liste des appareils qui ont été utilisés
 *  @param $date1 Obligatoire, date de début de la recherche
 *  @param $date2 Facultatif, date de fin de la recherche
 *  @param $group Facultatif, indique comment les données retournées doivent être regroupées : par jour (day), par semaine (week), par mois (month) ou par année (year). Par défaut, les données sont retournées groupées par heure.
 *  @param array $filtre Facultatif, origine de commande (tableau associatif), tel que retourné par stats_origins_get_stats
 *  @param $include_all Facultatif, permet d'inclure les tablettes des utilisateurs exclus des statistiques via les paramètres de configuration
 *
 *@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - pay_id : Identifiant du moyen de paiement
 *        - pay_name : Intitulé du moyen de paiement
 *        - count : Nombre de commandes pour ce moyen de paiement
 *        - total_ht : Chiffre d'affaire HT pour ce moyen de paiement
 *        - total_ttc : Chiffre d'affaire TTC pour ce moyen de paiement
 *        - month, year, ... : valeur de chaque période définie dans $groupvaleurs sont égales.valeurs sont égales.
 */
function stats_dev_subscriptions_get( $date1=false, $date2=false, $group='', $filtre=false, $include_all=false ){
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) return false;
	if ( $filtre !== false && !is_array($filtre) ) return false;

	global $config;
	switch( $group ){
		case 'week': // Semaine
			$sql = 'select DATE_SUB( ddu_date, INTERVAL DAYOFWEEK( ddu_date ) - 1 DAY ) as "date",  count(DISTINCT ddu_dev_id) as count';
			break;
		case 'month': // Mois
			$sql = 'select date_format(ddu_date,"%Y-%m") as "date", count(DISTINCT ddu_dev_id) as count';
			break;
		case 'year': // Année
			$sql = 'select year(ddu_date) as "date", count(*) as count';
			break;
		case 'day': // Jour (Valeur par défaut)
		default: // L'heure n'est pas supportée
			$sql = 'select date(ddu_date) as "date", count(DISTINCT ddu_dev_id) as count';
			break;
	}

	$sql .= ' from dev_devices_usages
				left join dev_devices on ddu_dev_id=dev_id
				left join gu_users on dev_usr_id=usr_id
				where ddu_tnt_id='.$config['tnt_id'];


	if (isdate($date1)) {
		$sql .= ' and ddu_date >= "' . dateparse($date1) . '" ';
	}
	if (isdate($date2)) {
		$sql .= ' and ddu_date <= "' . dateparse($date2) . '" ';
	}

	if ( isset($filtre) && $filtre && sizeof($filtre) > 0 ) {
	   foreach ($filtre as $key => $value) {
		   if ( is_numeric($value)) {
			   if ($value > 0) {
				   $sql .= ' and '.$key.' = '.$value.' ';
			   }elseif ($value < 0) {
				   $sql .= ' and '.$key.' != '.$value.' ';
			   }
		   }
	   }
	}

	$sql .= '  and usr_tnt_id != 0 ';

	if ( !$include_all ) {
		if( isset($config['device_usr_stats_exclude']) && is_array($config['device_usr_stats_exclude']) && sizeof($config['device_usr_stats_exclude']) >0  ){
			foreach($config['device_usr_stats_exclude'] as $k => $dev_usr){
				if( !is_numeric($dev_usr) ){
					unset($config['device_usr_stats_exclude'][$k]);
				}
			}
			$sql .= ' and dev_usr_id not in('.implode(",",$config['device_usr_stats_exclude']).') ';
		}
	}

	// Regroupement et tri des données
	switch( $group ){
		case 'week': // Semaine
			$sql .= '
				group by year(ddu_date), week(ddu_date,1)
				order by year(ddu_date), week(ddu_date,1)
			';
			break;
		case 'month': // Mois
			$sql .= '
				group by year(ddu_date), month(ddu_date)
				order by year(ddu_date), month(ddu_date)
			';
			break;
		case 'year': // Année
			$sql .= '
				group by year(ddu_date)
				order by year(ddu_date)
			';
			break;
		case 'day': // Jour (Valeur par défaut)
		default: // L'heure n'est pas supportée
			$sql .= '
				group by date(ddu_date)
				order by date(ddu_date)
			';
			break;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le temps passé par un commercial chez un client entre deux periodes données
 * @param string $date_start Obligatoire, Date date de début
 * @param string $date_end Obligatoire, Date date de fin
 * @param int $seller_id Facultatif, identifiant du commercial (0 pour tous les commerciaux)
 * @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 * - usr_id : Identifizant du client
 * - spent_time : le temps total passé chez le client usr_id
 */
function get_report_spent_time( $date_start, $date_end,  $seller_id=0 ){
	// if ($seller_id < 0) return null;
	global $config;

	$sql = "";
	$sql .= " select rck_usr_id as usr_id, sum(TIME_TO_SEC(TIMEDIFF(rck_date_end, rck_date_start))) as spent_time,";
	$sql .= " group_concat(DISTINCT rck_author_id) as author_ids";
	$sql .= " from rp_checkin";
	$sql .= " where 1";
	$sql .= " and rck_date_deleted is null ";
	$sql .= " and rck_date_start is not null ";
	$sql .= " and rck_date_end is not null ";
	$sql .= " and rck_date_end > rck_date_start ";

	if( $date_start!=null && isdateheure($date_start) ){
		$sql .= " and rck_date_start >= '".dateheureparse($date_start)."' ";
	}

	if( $date_end!=null && isdateheure($date_end) ){
		$sql .= " and rck_date_end <= '".dateheureparse($date_end)."' ";
	}

	if ($seller_id > 0) {
		$sql .= " and rck_author_id = " . $seller_id . " ";
	}

	$sql .= " and TIME_TO_SEC(TIMEDIFF(rck_date_end, rck_date_start)) < 25200 ";
	$sql .= " and TIME_TO_SEC(TIMEDIFF(rck_date_end, rck_date_start)) > 60 ";

	$sql .= " and rck_author_id is not null ";

	$sql .= " and (rck_confirmed in (1, 0) or rck_confirmed is null) ";

	$sql .= " and rck_tnt_id = ".$config["tnt_id"];
	$sql .= " group by rck_usr_id";

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de totaliser le nombre de rapport par groupe ainsi que le temps total passer par groupe de rapport
 * @param string $date_start Obligatoire, Date date de début
 * @param string $date_end Obligatoire, Date date de fin
 * @param $sort_by Facultatif, ordre de tri
 * @param int $seller_id Facultatif, identifiant du commercial (0 pour tous les commerciaux)
 * @param $author_id Facultatif, identifiant de l'auteur du rapport
 * @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *     - rpr_total : Le nombre de rapport par groupe
 *     - total_time : le temps total passé par groupement de rapport
 *     - rtg_id : l'identifiant du groupe de rapports
 */
function stats_get_report_time_grouped( $date_start, $date_end, $sort_by="", $seller_id=0, $author_id=0 ){
	if (!isDate($date_start) || !isDate($date_end)) { return null; }
	if (!is_numeric($seller_id)) { return null; }
	global $config;

	$sql = '
		select
			rtg_id as group_id,
			rtg_name as group_name,
			count(rpr_id) rpr_total,
			sum(TIME_TO_SEC(TIMEDIFF(rck_date_end, rck_date_start))) as total_time
		from rp_reports
			join rp_report_types on rpt_id=rpr_rpt_id and rpt_tnt_id in (0, '.$config['tnt_id'].')
			join rp_report_type_groups on rtg_id = rpt_rtg_id
			join gu_users on rpr_usr_id=usr_id and rpr_tnt_id=usr_tnt_id
			left join rp_report_objects on rpr_id=rpro_rpr_id and rpr_tnt_id=rpro_tnt_id and rpro_cls_id='.CLS_CHECKIN.'
			left join rp_checkin on rck_id=rpro_obj_id_0 and rck_tnt_id=rpro_tnt_id and rck_date_deleted is null and rck_confirmed in (1, 0)
		where rpr_tnt_id='.$config['tnt_id'].'
			and rpt_date_deleted is null
			and rpr_date_deleted is null
	';

	if( $seller_id>0 ){
		$sql .= " and rck_usr_id = ".$seller_id." ";
	}

	if( $author_id>0 ){
		$sql .= " and rpr_author_id = ".$author_id." ";
	}

	if( $date_start!=null && isDate($date_start) ){
		$sql .= " and date(rpr_date_created) >= '".dateparse($date_start)."' ";
	}

	if( $date_end!=null && isDate($date_end) ){
		$sql .= " and date(rpr_date_created) <= '".dateparse($date_end)."' ";
	}

	$sql .= " group by rtg_id ";

	if( trim($sort_by)!='' ){
		$sql .= " order by '".addslashes($sort_by)."'' desc ";
	}else{
		$sql .= 'order by rtg_id asc';
	}
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de générer le tableau contenant les données pour les différents graphiques présents dans RiaShop.
 *    @param $graph Obligatoire, identiant de la ou des courbes devant appraitre sur le graphique, parmis celles-ci :
 *                    - order-carts              : Création de panier
 *                    - order-completed          : Commandes validées
 *                    - order-canceled           : Commandes annulées
 *                    - order-ca-completed       : Commandes validées en euros (CA)
 *                    - order-ca-canceled        : Commandes annulées en euros (CA)
 *                    - product-order            : Commandes validées pour un ou plusieurs produits (paramètre $filtre)
 *                    - product-seen             : Consultations d'un ou plusieurs produits (paramètre $filtre)
 *                    - contacts                 : Messages de contact envoyés
 *                    - origins                  : Origine de commande (ne peut être combiné à un autre)
 *                    - payments                 : Moyens de paiement (ne peut être combiné à un autre)
 *                    - user-product             : Produits les plus consultés par un client
 *                    - user-category            : Catégories les plus consultées par un client
 *                    - price-watching           : Prix d'un produit chez un concurrent
 *                    - dev-subscriptions        : Nombre de tablette utilisées
 *                    - calls-history            : Historique des appels
 *                    - calls-representative     : Appels par représentants
 *                    - calls-customers          : Appels par clients
 *                    - calls-reports             : Appels du nombre de visite global (tous type confondu)
 *    @param string $date_start Obligatoire, date de début de récupération des données
 *    @param string $date_end Obligatoire, date de fin de récupération des données
 *    @param $filtre Optionnel, filtre à appliquer lors de la récupération des données sous forme de tableau, pour :
 * 					  - order-carts         	: Optionnel, array( 'seller_id' => 0000, 'currency' => 'EUR' )
 * 					  - order-completed         : Optionnel, array( 'seller_id' => 0000, 'currency' => 'EUR' )
 *                    - order-canceled          : Optionnel, array( 'seller_id' => 0000, 'currency' => 'EUR' s)
 *                    - order-ca-completed      : Optionnel, array( 'seller_id' => 0000, 'currency' => 'EUR' )
 *                    - order-ca-canceled       : Optionnel, array( 'seller_id' => 0000, 'currency' => 'EUR' )
 *                    - product-order           : Obligatoire, array( 'prd' => array(0000, 0000) ) ou array( 'prd' => 0000 )
 *                    - product-seen            : Obligatoire, array( 'prd' => array(0000, 0000) ) ou array( 'prd' => 0000 )
 *                    - origins                 : Optionnel, array( 'prd|cat' => array(0000, 0000) ) ou array( 'prd|cat' => 00000 )
 *                    - user-product            : Obligatoire, array( 'usr' => 00000 )
 *                    - user-category           : Obligatoire, array( 'usr' => 00000 )
 *                    - price-watching          : Obligatoire, array('prd_id'=>0000,'cpt_id'=>0000)
 *                    - dev-subscriptions       : Obligatoire, array( 'prf_id' => -1 )
 *                                                  --> un key_id negatif exclu celui-ci example array("dev_usr_id" => -970340)
 *                                                  --> un key_id positif filtre sur celui-ci array("usr_prf_id" => 1 )
 *                    - pipeline                : Obligatoire, array( 'seller_id' => 0 )
 *                    - calls-history           : Obligatoire, array( 'author' => 0 )
 *                    - calls-representative    : Obligatoire, array( 'author' => 0 )
 *                    - calls-customers         : Obligatoire, array( 'author' => 0 )
 *                    - calls-reports			: Obligatoire array('author' => 0)
 * 					  -	invoice					: Optionnel, array( 'currency' => 'EUR' )
 *    @param $limit Optionnel, aucune limite par défaut, permet de limiter le nombre de line dans le tableau
 *    @param $websites Facultatif, tableau d'identifiants de sites web sur lesquels filtrer le résultat
 *    @param $force_period Facultatif, Permet de forcer la période ( hour, day, week, month, year ) plutôt qu'elle soit definie sur la différence entre la date de début et date de fin
 *    @param array $origin Facultatif, tableau d'identifiants d'origines sur lesquels filtrer le résultat
 *    @param $pay_id Facultatif, tableau d'identifiants de moyens de paiement sur lequel filtrer les résultats
 *    @param $store Facultatif, tableau d'identifiants de magasins sur lesquels filtrer les résultats
 *    @param bool $gescom Facultatif, correspond à l'origine de commande "gestion commerciale"
 *    @param bool $is_web Facultatif, indique si l'on souhaite les commandes web uniquement (true) ou toutes (false).
 *    @return array Retourne un tableau contenant les données pour les différents graphiques
 */
function stats_graphs_get_datas( $graph, $date_start, $date_end, $filtre=false, $limit=-1, $websites=0, $force_period=false, $origin=false, $pay_id=0, $store=array(), $gescom=null, $is_web=null ){

	$months = array('', _('Jan.'), _('Fév.'), _('Mars'), _('Avril'), _('Mai'), _('Juin'), _('Juil.'), _('Août'), _('Sept.'), _('Oct.'), _('Nov.'), _('Déc.') );
	$days = array('', _('Lundi'), _('Mardi'), _('Mercredi'), _('Jeudi'), _('Vendredi'), _('Samedi'), _('Dimanche'));

	// Récupère le type de période la plus adaptée selon le nombre de jours entre la date de début et de fin
	$diff_days = ceil((strtotime($date_end) - strtotime($date_start)) / 86400);
	if ( $force_period ) {
		$tp = $force_period;
	}else{
		$tp = '';
		if( $diff_days<=1 ){
			$tp = 'hour';
		}elseif( $diff_days<=31 ){
			$tp = 'day';
		}elseif( $diff_days<=93 ){
			$tp = 'week';
		}elseif( $diff_days<=730 ){
			$tp = 'month';
		}else{
			$tp = 'year';
		}
	}

	// Les statistiques d'utilisation des tablettes ne supportent pas l'affichage à l'heure
	if( $graph=='dev-subscriptions' && $tp=='hour' ){
		$tp = 'day';
	}
	if( $graph=='pipeline' && ($tp=='hour' || $tp == "day") ){
		$tp = 'month';
	}

	// Création du tableau des labels de chaque colonne de données
	// Initialisation d'un tableau de données à 0, ou null si les dates sont dans le futur
	$period_of_data = array();
	$data = array();
	switch( $tp ){
		case 'year': // Année
			$first = date('Y', strtotime($date_start));
			$end = date('Y', strtotime($date_end));
			for( $i=$first; $i<=$end; $i++ ){
				$period_of_data[$i] = $i;
				$data[$i] = 0;
			}
			break;
		case 'month': // Mois
			$first = new Datetime($date_start);
			$end = new Datetime($date_end);
			$DatePeriod = new DatePeriod($first, new DateInterval('P1M'), $end);
			foreach( $DatePeriod as $date ){
				$month_number = $date->format('n');
				$year = $date->format('Y');
				$period_of_data[$month_number . $year] = $months[$month_number] . ' ' . $year;
				$data[$month_number . $year] = 0;
			}
			break;
		case 'week': // Semaines
			$first = strtotime($date_start);
			$end = strtotime($date_end);

			$cpt = 0;
			while( $first<$end ){
				if( ++$cpt>1000 ) break;

				$week = str_pad((int)strftime('%V', $first), 2, '0', STR_PAD_LEFT);
				$year = (int)strftime('%Y', $first);

				$period_of_data[(int) $week.$year] = _('Semaine ') . str_pad($week." - ".$year, 6, '0', STR_PAD_LEFT);
				$data[(int) $week.$year] = 0;

				$first = strtotime('+ 1 week', $first);
			}

			break;
		case 'day': // Jours
			for( $i=strtotime($date_start); $i<=strtotime($date_end); $i+=86400 ){
				if( strstr(date('Y-m-d H:i:s', $i), '23:00:00') ){
					$i += 3600;
				}
				$period_of_data[$i] = $days[ date('N', $i) ] . ' ' . date('j', $i);
				if( $i<=time() ){
					$data[$i] = 0;
				}else{
					$data[$i] = 'null';
				}
			}

			break;
		case 'hour': // Heures
			for( $i=0; $i<24; $i++ ){
				$period_of_data[$i] = str_pad($i, 2, '0', STR_PAD_LEFT) . 'h';
				$data[$i] = 0;
			}
			break;
	}

	$first = true;
	$all_data = array();
	$seller_id = 0;
	if( is_array($filtre) && array_key_exists('seller_id', $filtre) ){
		$seller_id = $filtre['seller_id'];
	}

	$currency = false;
	if( is_array($filtre) && array_key_exists('currency', $filtre) ){
		$currency = $filtre['currency'];
	}
	$stats = false;
	$col = false;

	switch( $graph ){
		case 'order-carts': // Paniers
			$col = 'carts';
			$stats = stats_orders_ca($col, $date_start, $date_end, $tp != 'hour' ? 'day' : 'hour', $websites, $origin, $pay_id, $store, true, $seller_id, true, $gescom, 0, true, array(), array(), 0, 0, false, null, $currency);
			$col = 'count';
			break;
		case 'order-completed': // Commandes
			$col = 'completed';
			$stats = stats_orders_ca($col, $date_start, $date_end, $tp != 'hour' ? 'day' : 'hour', $websites, $origin, $pay_id, $store, true, $seller_id, true, $gescom, 0, true, array(), array(), 0, 0, false, null, $currency);
			$col = 'count';
			break;
		case 'order-canceled': // Commandes annulées
			$col = 'canceled';
			$stats = stats_orders_ca($col, $date_start, $date_end, $tp != 'hour' ? 'day' : 'hour', $websites, $origin, $pay_id, $store, true, $seller_id, true, $gescom, 0, true, array(), array(), 0, 0, false, null, $currency);
			$col = 'count';
			break;
		case 'order-ca-completed': // Chiffre d'affaires
			$col = 'completed';
			$stats = stats_orders_ca($col, $date_start, $date_end, $tp != 'hour' ? 'day' : 'hour', $websites, $origin, $pay_id, $store, false, $seller_id, true, $gescom, 0, false, false, ord_states_get_ord_valid(), 0, 0, false, false, $currency);
			break;
		case 'order-ca-canceled': // Chiffre d'affaires annulé
			$col = 'canceled';
			$stats = stats_orders_ca($col, $date_start, $date_end, $tp != 'hour' ? 'day' : 'hour', $websites, $origin, $pay_id, $store, false, $seller_id, true, $gescom, 0, true, array(), array(), 0, 0, false, null, $currency);
			break;
		case 'product-order':
			$col = 'hits';
			$stats = stats_view_products_ordered($filtre['prd'], $date_start, $date_end, $tp != 'hour' ? 'day' : '');
			break;
		case 'product-seen': // Impressions de produits / vues
			$col = 'hits';
			$stats = stats_view_products_seen($filtre['prd'], $date_start, $date_end, $tp != 'hour' ? 'day' : '');
			break;
		case 'contacts': // Contacts
			$col = 'contacts';
			$stats = stats_contacts_count($date_start, $date_end, $tp != 'hour' ? 'day' : '', $websites);
			break;
		case 'origins': // Origines de commandes
			$data = array();
			$stats = stats_origins_get_stats( isset($filtre['prd']) ? $filtre['prd'] : 0, isset($filtre['cat']) ? $filtre['cat'] : 0, $date_start, $date_end, $websites );

			if( $stats ){
				while( $stat = ria_mysql_fetch_array($stats) ){
					if( $stat['count']<=0 ){
						continue;
					}

					$name = view_source_origin( $stat, 'showsource' );
					if( !isset($data[ $name ]) ){
						$data[ $name ] = array(
							'count' => 0,
							'total_ht' => 0,
							'total_ttc' => 0
						);
					}

					$data[ $name ]['count'] += $stat['count'];
					$data[ $name ]['total_ht'] += $stat['total_ht'];
					$data[ $name ]['total_ttc'] += $stat['total_ttc'];
				}
				// Trie les origines par nombre de commandes décroissant
				uasort( $data, 'stats_compare_origins' );
			}
			return array_slice( $data, 0, (is_numeric($limit) && $limit > 0 ? $limit : null) );

		case 'str_views':
			$col = 'hits';
			$website = ($websites == 0) ? false : $websites;
			$stats = stats_stores_search_seen(isset($filtre['str']) ? $filtre['str'] : 0, $date_start, $date_end, $tp,$website );
			break;
		case 'str_contact':
			$col = 'hits';
			$website = ($websites == 0) ? false : $websites;
			$stats = stats_stores_contacts(isset($filtre['str']) ? $filtre['str'] : 0, $date_start, $date_end, $tp, $website,false, isset($filtre['type']) ? $filtre['type'] : 0);
			break;

		case 'user-product':
			$data = array();

			$stats = stats_user_products_get($filtre['user'], false, -1);
			if ($stats) {
				while ($stat = ria_mysql_fetch_array($stats)) {
					$data[$stat['name']] = $stat['views'];
				}
			}

			return array_slice($data, 0, (is_numeric($limit) && $limit > 0 ? $limit : null));
		case 'user-category':
			$data = array();

			$stats = stats_user_categories_get($filtre['user']);
			if ($stats) {
				while ($stat = ria_mysql_fetch_array($stats)) {
					$data[$stat['name']] = $stat['views'];
				}
			}

			return array_slice($data, 0, (is_numeric($limit) && $limit > 0 ? $limit : null));
		case 'payments': // Paiements
			$data = array();

			$stats = stats_ord_payment_types_get($date_start, $date_end, '', true, $websites, array('count' => 'desc'));
			if ($stats) {
				while ($stat = ria_mysql_fetch_array($stats)) {
					if ($stat['count'] <= 0) {
						continue;
					}

					$pay_name = ucfirst($stat['pay_name']);
					if (!isset($data[$pay_name])) {
						$data[$pay_name] = 0;
					}

					$data[$pay_name] += $stat['count'];
				}
			}

			return array_slice($data, 0, (is_numeric($limit) && $limit > 0 ? $limit : null));
		case 'price-watching': // Veille tarifaire
			$stats = stats_price_watching_offers($filtre['prd'], $filtre['cpt'], $date_start, $date_end, $tp != 'hour' ? 'day' : '');
			if ($stats) {
				$array_datas_values = array_values($data);
				$array_datas_keys = array_keys($data);
				if ($array_datas_values[0] == 0 && $array_datas_values[0] !== 0.00) {
					$r = stats_price_watching_offers_past($filtre['prd'], $filtre['cpt'], $date_start, $tp != 'hour' ? 'day' : '');
					$past = ria_mysql_fetch_assoc($r);
					if ($past) {
						$data[$array_datas_keys[0]] = $past['price'].'-'.$past['promo_price'];
					}
				}
				while ($stat = ria_mysql_fetch_assoc($stats)) {
					$period = set_period($tp, $stat);

					if (!$period) {
						continue;
					}
					if (isset($data[(int)$period])) {
						$data[(int)$period] = $stat['price'].'-'.$stat['promo_price'];
					}
				}
				$lenght = count($data);

				if ($array_datas_values[$lenght - 1] == 0 && $array_datas_values[$lenght - 1] !== 0.00){
					$r = stats_price_watching_offers_lastcheck( $filtre['prd'], $filtre['cpt'], $date_end, $tp != 'hour' ? 'day' : '' );
					$lastcheck = ria_mysql_fetch_assoc( $r );
					if( $lastcheck ){
						$p = set_period( $tp, $lastcheck );
						if( isset( $data[(int)$p] ) ){
							$last = $p;
						}
					}
				}

				if (isset($last)) {
					$i = array_search($last, $array_datas_keys) + 1;
					$data = array_slice($data, 0, $i, true);
				}
				$previousValue = null;
				foreach ($data as $key => $ar) {
					if ($previousValue && $ar !== 0.00 && $ar === 0) {
						$data[$key] = $previousValue;
					} else {
						$previousValue = $ar;
					}
				}
			}

			break;
		case 'dev-subscriptions': // Nombre de devices/licences Yuto actifs/actives
			$col = 'count';

			$dev_subs = stats_dev_subscriptions_get( $date_start, $date_end, $tp, $filtre );
			if( $dev_subs && ria_mysql_num_rows($dev_subs) ){
				$stats = $dev_subs;
			}

			break;
		case 'pipeline': // Pipeline commercial


			// Charge les statistiques de chiffre d'affaires réalisé
			$stats = stats_invoices_ca_array(
				'completed',
				$date_start,
				$date_end,
				$tp,
				ria_array_get($filtre, 'with_port', false),
				ria_array_get($filtre, 'usr_seller_id', 0),
				ria_array_get($filtre, 'only_seller_orders', false),
				ria_array_get($filtre, 'user_ids', 0),
				ria_array_get($filtre, 'inv_id', 0),
				ria_array_get($filtre, 'direct', null),
				$is_web,
				$origin
			);
			//$stats = stats_orders_ca('completed', $date_start, $date_end, $tp, $websites, $origin, $pay_id, $store, false, $filtre['usr_seller_id'], $filtre['only_seller_orders']);
			if( $stats && sizeof($stats) ){
				foreach( $stats as $r){
					switch( $tp ){
						case 'year' :
							$index = $r['year'];
							break;
						case 'month' :
							$index = ''.$r['month'].$r['year'];
							break;
						case 'week' :
							$index = ''.$r['week'].$r['year'];
							break;
						default:
							$index = 'month';
							break;
					}

					if( !isset($data[$index]) || !is_array($data[$index]) ){
					   $data[$index] = array();
					}
					$data[$index][0] = round($r['completed'], 2);
				}
			}

			// Charge les statistiques de chiffre d'affaires prévisionnel
			$ord_pipe = stats_get_pipeline( $date_start, $date_end, $filtre, $tp );
			if( $ord_pipe && ria_mysql_num_rows($ord_pipe) ){
				while ($r = ria_mysql_fetch_assoc($ord_pipe)) {
					$index = $r[$tp];

					if( !isset($data[$index]) || !is_array($data[$index]) ){
						$data[$index] = array();
					}
					$data[$index][ 3 ] = round($r['total_ttc'], 2);
				}
			}

			foreach( $period_of_data as $key=>$v ){
				$rcfg = false;
				switch ($tp) {
					case 'month':
						$month_nb = preg_replace('/([0-9]{1,2})[0-9]{4}$/','$1',$key);
						$rcfg = cfg_overrides_get( 0, array(), "turnover_goal_month_".$month_nb, $filtre["usr_seller_id"]);
						break;
					case 'year':
						$rcfg = cfg_overrides_get( 0, array(), "turnover_goal_year", $filtre["usr_seller_id"]);
						break;
				}

				if ($rcfg && ria_mysql_num_rows($rcfg)) {
					$goal_val = ria_mysql_result($rcfg, 0, "value");

					if (!is_array($data[$key])) {
						$data[$key] = array();
					}
					$data[$key][4] = $goal_val != false ? $goal_val : 0 ;
				}
			}
			break;
		case 'report-time':
			$rt = stats_get_report_time_grouped($date_start, $date_end, '', 0, $filtre['usr_seller_id']);

			if( $rt && ria_mysql_num_rows($rt) ){
				while( $r = ria_mysql_fetch_assoc($rt) ){
					$d[] = $r;
				}

				return $d;
			}

			return null;
		case 'invoice':{ // Facturation
			$col = ria_array_get($filtre, 'col', 'completed');
			$stats = stats_invoices_ca(
				'completed',
				$date_start,
				$date_end,
				$tp,
				ria_array_get($filtre, 'with_port', false),
				ria_array_get($filtre, 'seller_id', 0),
				ria_array_get($filtre, 'only_seller_orders', false),
				ria_array_get($filtre, 'user_ids', 0),
				ria_array_get($filtre, 'inv_id', 0),
				ria_array_get($filtre, 'direct', null),
				$is_web,
				$origin
			);
			break;
		}
		case 'calls-history':{ // Historique des appels
			$calls = gcl_calls_get_by_view( "", 0, 0, $filtre['author'], array('date1' => date('Y-m-d H:i:s', strtotime($date_start)),'date2' => date('Y-m-d H:i:s', strtotime($date_end) + 86399)) );
			foreach ($calls as $key => $call) {
				if ($key !== 'total_rows'){
					switch( $tp ){
						case 'year' :
							$index = date('Y', strtotime($call['gcl_date_created']));
							break;
						case 'month' :
							$index = date('nY', strtotime($call['gcl_date_created']));
							break;
						case 'week' :
							$index = (int) date('WY', strtotime($call['gcl_date_created']));
							break;
						case 'day' :
							$index = strtotime(date('Y-m-d', strtotime($call['gcl_date_created'])));
							break;
						case 'hour' :
							$index = intval(date('H', strtotime($call['gcl_date_created'])));
							break;
						default:
							$index = 'month';
							break;
					}
					if( !isset($data[$index]) || !is_array($data[$index])){
						$data[$index] = array("all" => 0, "answered" => 0);
					}
					$data[$index]["all"]++;
					if (isset($call['gcl_answered']) && $call['gcl_answered'] == 1){
						$data[$index]["answered"]++;
					}
				}
			}
			break;
		}
		case 'calls-representative' :{
			$data = array();
			$calls = gcl_calls_get_by_view( "", 0, 0, $filtre['author'], array('date1' => date('Y-m-d H:i:s', strtotime($date_start)),'date2' => date('Y-m-d H:i:s', strtotime($date_end) + 86399)) );
			$answered_calls = 0;
			foreach( $calls as $key => $call ){
				if( $key !== 'total_rows' ){
					$index = $call['gcl_author_id'];

					if( !isset($data[$index]) || !is_array($data[$index]) ){
						$data[$index] = array("all_number" => 0, "answered_number" => 0);
					}
					$data[$index]["all_number"]++;
					if( isset($call['gcl_answered']) && $call['gcl_answered'] == 1 ){
						$data[$index]["answered_number"]++;
						$answered_calls++;
					}
				}
			}
			foreach( $data as $key => $value ){
				if( is_array($value) ){
					$data[$key]['all'] = ($data[$key]['all_number'] / $calls['total_rows'])*100;
					$data[$key]['answered'] = $answered_calls ? ($data[$key]['answered_number'] / $answered_calls)*100 : 0;
				}
			}
			return $data;
		}
		case 'calls-customers' :{
			$data = array();
			$calls = gcl_calls_get_by_view( "", 0, 0, $filtre['author'], array('date1' => date('Y-m-d H:i:s', strtotime($date_start)),'date2' => date('Y-m-d H:i:s', strtotime($date_end) + 86399)) );
			$answered_calls = 0;
			foreach( $calls as $key => $call ){
				if( $key !== 'total_rows' ){
					$index = $call['gcl_usr_dst'];

					if( !isset($data[$index]) || !is_array($data[$index]) ){
						$data[$index] = array("all_number" => 0, "answered_number" => 0);
					}
					$data[$index]["all_number"]++;
					if( isset($call['gcl_answered']) && $call['gcl_answered'] == 1 ){
						$data[$index]["answered_number"]++;
						$answered_calls++;
					}
				}
			}
			foreach( $data as $key => $value ){
				if( is_array($value) ){
					$data[$key]['all'] = ($data[$key]['all_number'] / $calls['total_rows'])*100;
					$data[$key]['answered'] = $answered_calls ? ($data[$key]['answered_number'] / $answered_calls)*100 : 0;
				}
			}
			return $data;
		}
		case 'calls-reports':{
			// on vérifie le filtre par auteur
			$author = isset($filtre['author']) ? $filtre['author'] : 0;
			$usr_id = isset($filtre['usr_id']) ? $filtre['usr_id'] : 0;
			$data = array();
			$listTypesReports = rp_types_get(); // Récuperation des types de rapport
			$count_types = $listTypesReports ? ria_mysql_num_rows( $listTypesReports ) : 0;

			while( $type = ria_mysql_fetch_assoc($listTypesReports) ){ // boucle sur les
				$reports = rp_reports_get( 0, $type['id'], $usr_id, $author, $date_start, $date_end, null, null, null, array('date_created'=>'asc') );

				$count_reports = $reports ? ria_mysql_num_rows( $reports ) : 0;
				if( $count_reports>0 ){
					while( $report = ria_mysql_fetch_assoc($reports) ){
						switch( $tp ){
							case 'year':
								$index = date('Y', strtotime($report['date_created_en']));
								break;
							case 'month':
								$index = date('nY', strtotime($report['date_created_en']));
								break;
							case 'week':
								$index = (int) date('WY', strtotime($report['date_created_en']));
								break;
							case 'day':
								$index = strtotime(date('Y-m-d', strtotime($report['date_created_en'])));
								break;
							case 'hour':
								$index = date('H', strtotime($report['date_created_en']));
								break;
							default:
								$index = 'month';
								break;
						}

						if( !isset($data[$index]) || !is_array($data[$index])){
							$data[$index] = array();
						}

						if( !isset($data[$index][$type['id']])){
							$data[$index][$type['id']] = 0;
						}

						$data[$index][$type['id']]++;
					}
				}
			}
			break;
		}
	}
	if ($stats && $col) {
		while ($stat = ria_mysql_fetch_assoc($stats)) {

			if ( !isset($period) || $period === false ) {
				$period = false;
			}

			switch ($tp) {
				case 'year': // Année
					$period = date('Y', strtotime($stat['date']));
					break;
				case 'month': // Mois
					$period = date('nY', strtotime($stat['date']));
					break;
				case 'week': // Semaine
					$period = date('WY', strtotime($stat['date']));
					break;
				case 'day': // Jour
					$period = strtotime(date('Y-m-d', strtotime($stat['date'])));
					break;
				case 'hour': // Heure
					$period = $stat['hour'];
					break;
				default:
					$period = 'month';
					break;
			}

			if( $period === false ){
				continue;
			}

			if( isset($data[(int)$period]) ){
				$data[(int)$period] += $stat[$col];
			}
		}
	}

	$i = 1;
	$all = array();
	foreach ($period_of_data as $key => $val) {
		if (is_numeric($limit) && $limit > 0 && $i > $limit) {
			break;
		}

		if (isset($data[$key])) {
			$all[$val] = $data[$key];
		}

		$i++;
	}

	return $all;
}
// \endcond


// \cond onlyria
/** Cette fonction permet de retourner le montant total des commandes estimé et retourn les resultat en fonction d'une période
 * @param string $date_start Facultatif, date de départ
 * @param string $date_end Facultatif, date de fin
 * @param $filter Facultatif, Un tableau conprenant les options suivants
 *     - usr_seller_id : permet de filtrer sur un seller_id
 *     - only_seller_orders : permet d'avoir uniquemenet les commandes de ce commercial ou d'inclure les commandes passés par l'adv
 * @param $period Facultatif, Période, accepte les valeurs suivante : day, week, month, year
 * @return resource Un résultat MySQL
 */
function stats_get_pipeline( $date_start=false, $date_end=false, $filter=array(), $period='' ){

	global $config;
	if (!is_string($period)) { return null; }

	$sql = '
		select
			sum(ord_total_ht) as total_ht,
			sum(ord_total_ttc) as total_ttc,
			fld_rate.pv_value as rate,
			ord_date,
			date(fld_delay.pv_value) as date,
	';

	if( trim($period) == '' || !in_array($period, array('day', 'week', 'month', 'year', 'hour'))){
		$period = 'hour';
	}

	switch ($period) {
		case 'day' :
			$sql .= 'dayofmonth(fld_delay.pv_value) as ' . $period . ' ';
			break;
		case 'week' :
			$sql .= 'concat(week(fld_delay.pv_value),year(fld_delay.pv_value)) as ' . $period . ' ';
			break;
		case 'month' :
			$sql .= 'concat(fld_delay.pv_value,year(fld_delay.pv_value)) as ' . $period . ' ';
			break;
		case 'year' :
			$sql .= 'year(fld_delay.pv_value) as ' . $period . ' ';
			break;
		case 'hour' :
		default:
			$sql .= 'hour(fld_delay.pv_value) as ' . $period . ' ';
			break;
	}

	$sql .= '
		from ord_orders
			join gu_users on ord_tnt_id=usr_tnt_id and ord_usr_id = usr_id
			join fld_object_values fld_rate on fld_rate.pv_tnt_id=ord_tnt_id and fld_rate.pv_obj_id_0=ord_orders.ord_id
				and fld_rate.pv_lng_code="'.$config['i18n_lng'].'" and fld_rate.pv_obj_id_1=0 and fld_rate.pv_obj_id_2=0 and fld_rate.pv_fld_id='._FLD_ORD_SIGN_RATE.'
			join fld_object_values fld_delay on fld_delay.pv_tnt_id=ord_tnt_id and fld_delay.pv_obj_id_0=ord_orders.ord_id
				and fld_delay.pv_lng_code="'.$config['i18n_lng'].'" and fld_delay.pv_obj_id_1=0 and fld_delay.pv_obj_id_2=0 and fld_delay.pv_fld_id='._FLD_ORD_PIPE_SIGN_DATE.'
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_state_id = '._STATE_DEVIS;

	if( is_numeric($filter['usr_seller_id']) && $filter['usr_seller_id']>0 ){
		if ($filter['only_seller_orders'] == "true") {
			$sql .= ' and ord_seller_id = '.$filter['usr_seller_id'];
		}else{
			$sql .= ' and (ord_seller_id = '.$filter['usr_seller_id'].' or ( ord_seller_id is null and usr_seller_id = '.$filter['usr_seller_id'].'))';
		}
	}

	if( $date_start && isDate($date_start) && $period != 'all' ){
		$sql .= '    and date(fld_delay.pv_value) >= date( "'.dateparse($date_start).'" ) ';
	}

	if (trim($period) != ''){
		$sql .= ' group by ' . addslashes($period) . ', fld_rate.pv_value ';
		$sql .= ' order by ' . addslashes($period) . ' asc ';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les produits bestsellers.
 *    @param string $date_start Optionnel, date de début pour récupérer les statistiques de ventes
 *    @param string $date_end Optionnel, date de fin pour récupérer les statistiques de ventes
 *    @param int $limit Optionnel, nombre de produits retournés par défaut les 100 premiers sont retournés, mettre 0 pour aucune limite
 *    @param array $origin Optionnel, permet de filtrer les commandes sur son origine pour cela il faut donné un tableau sous la forme array('col'=>'val') avec comme colonne : source, name ou medium
 *    @param bool|null $gescom Optionnel, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 *    @param int $wst_id Optionnel, identifiant d'un site web par défaut on ne tient pas compte des sites web
 *    @param bool $publish Optionnel, par défaut tous les produits sont retournés, mettre true pour n'avoir que ce publié
 *    @param int|array $prd_id Optionnel, identifiant ou tableau d'identifiants de produit
 *    @param int|array $cat_id Optionnel, identifiant d'une catégorie
 *    @param bool $recursive_cat Optionnel, mettre True pour récupèrer les produits des catégories enfants de celle donnée en paramètre ($cat_id), par défaut à False
 *    @param int $brd_id Optionnel, identifiant d'une marque
 *    @param int|array $exclude_prd Optionnel, permet d'exclure un ou plusieurs produits du résultat (donné un identifiant ou tableau d'identifiant, 0 = aucune exclusion)
 *    @param int $seller_id Optionnel, identifiant d'un représentant sur lequel filtrer le résultat
 *    @param array $user_ids Facultatif, ID des utilisateurs à considérer pour le calcul.
 * 		@param bool $in_stock Optionnel, détermine si seul les articles en stocks seront retournées (true par défaut)
 * 		@param bool $state_on_line Optionnel, par défaut à false, mettre true pour utiliser le statut sur la ligne de commande plutôt que sur la commande
 *
 *    @return resource Un résultat MySQL contenant :
 *                - id : identifiant du produit
 *                - ref : référence du produit
 *                - name : désignation du produit
 *                - qte : nombre total de vente du produit
 *                - margin : marge brute totale
 *                - total_ht : total ht des ventes du produit
 *                - orders : nombre de commande contenant le produit
 *                - ord_total_ht : total ht des commandes contenant le produit
 */
function stats_bestsellers_get( $date_start=false, $date_end=false, $limit=100, $origin=false, $gescom=null, $wst_id=0, $publish=false, $prd_id=0, $cat_id=0, $recursive_cat=false, $brd_id=0, $exclude_prd=0, $seller_id=0, $user_ids=array(), $in_stock=true, $state_on_line=false ){
	// Filtre par date de début
	if ($date_start !== false) {
		$date_start = dateparse($date_start);
		if (!isdate($date_start)) {
			return false;
		}
	}

	// Filtre par date de fin
	if ($date_end !== false) {
		$date_end = dateparse($date_end);
		if (!isdate($date_end)) {
			return false;
		}
	}

	// Filtre par nombre de lignes maximum
	if (!is_numeric($limit) || $limit < 0) {
		return false;
	}

	// Préparation du SQL pour la ou les origines de commande
	$sub_origin = ord_orders_get_sql_origin($origin);
	if ($sub_origin === false) {
		return false;
	}

	// Filtre par site d'origine
	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	// Filtre par identifiant de produit
	$prd_id = control_array_integer($prd_id, false);
	if ($prd_id === false) {
		return false;
	}

	// Filtre par identifiant de catégorie
	$cat_id = control_array_integer($cat_id, false);
	if ($cat_id === false) {
		return false;
	}

	// Filtre par identifiant de marque
	$brd_id = control_array_integer($brd_id, false);
	if ($brd_id === false) {
		return false;
	}

	// Produits exclus
	$exclude_prd = control_array_integer($exclude_prd, false);
	if ($exclude_prd === false) {
		return false;
	}

	// Filtre par représentant
	if( !is_numeric($seller_id) || $seller_id<0 ){
		return false;
	}

	global $config;

	if ($in_stock) {
		if ($config['tnt_id'] == 2) {
			$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
		} else {
			$dps = prd_deposits_get_main();
		}
	}

	$sql = '
	select tnt_id, id, ref, name, sum(total_ht) as total_ht, is_sync, sum(qte) as qte, count(distinct ord_id) as orders, sum(gross_margin) as margin, sum(ord_total_ht) as ord_total_ht
	from (
		select
			op.prd_tnt_id as tnt_id, op.prd_id as id, op.prd_ref as ref, op.prd_name as name,
			' . ($config['use_decimal_qte'] ? 'sum(prd_qte)' : 'cast(sum(prd_qte) as signed)') . ' as qte,
			sum(op.prd_qte*op.prd_price_ht) as total_ht, op.prd_qte * (op.prd_price_ht - p.prd_purchase_avg) as gross_margin,
			ord_id, prd_is_sync as is_sync, ord_total_ht
		from ord_products as op
			join ord_orders on (op.prd_tnt_id=ord_tnt_id and prd_ord_id=ord_id)
			join gu_users on (ord_tnt_id=usr_tnt_id and ord_usr_id=usr_id)
			join prd_products as p on (op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id)
	';

	if ($in_stock) {
		$sql .= ' join prd_stocks on (sto_tnt_id = p.prd_tnt_id and sto_prd_id = p.prd_id and sto_dps_id = '.$dps.')';
	}

	if (sizeof($cat_id)) {
		if (!$recursive_cat) {
			$list_cat_ids = implode(', ', $cat_id);
		} else {
			$categories = prd_categories_childs_get_list($cat_id, $publish);
			$list_cat_ids = trim($categories) != '' ? implode(', ', $cat_id) . ',' . $categories : implode(', ', $cat_id);
		}

		$sql .= 'join prd_classify on (p.prd_tnt_id=cly_tnt_id and p.prd_id=cly_prd_id and cly_cat_id in (' . $list_cat_ids . '))';
	}

	if (is_array($origin) && sizeof($origin)) {
		$sql .= '
			join stats_origins on ( stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id )
		';
	} elseif ($origin === -1) {
		$sql .= '
			left join stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )
		';
	}

	$sql .= '
		where op.prd_tnt_id=' . $config['tnt_id'] . '
			and op.prd_ref not in (\'' . implode('\', \'', $config['dlv_prd_references']) . '\')
	';

	if( $state_on_line ){
		$sql .= ' and op.prd_state_line in (' . implode(', ', ord_states_get_ord_valid()) . ')';
	}else{
		$sql .= ' and ord_state_id in (' . implode(', ', ord_states_get_ord_valid()) . ')';
	}

	$sql .= '
			and prd_date_deleted is null
			and usr_prf_id!=' . PRF_ADMIN . '
	';

	// Filtre par date de début
	if ($date_start !== false) {
		$sql .= '
			and date(ord_date)>=\'' . $date_start . '\'
		';
	}

	// Filtre par date de fin
	if ($date_end !== false) {
		$sql .= '
			and date(ord_date)<="' . $date_end . '"
		';
	}

	// Ajout le SQL pour le filtre sur la ou les origines de commande
	$sql .= $sub_origin;

	// Filtre par source (Gestion commerciale ou site web)
	if ($gescom !== null) {
		if ($gescom)
			$sql .= ' and ord_state_id>=3 and ord_pay_id is null';
		else
			$sql .= ' and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) )';
	}

	// Filtre par site d'origine
	if ($wst_id > 0) {
		$sql .= ' and ord_wst_id=' . $wst_id;
	}

	// Filtre par indicateur de publication
	if ($publish) {
		$sql .= '
			and p.prd_publish and p.prd_publish_cat
		';
	}

	// Filtre par indicateur de stock
	if ($in_stock) {
		$sql .= '
			and ( prd_sleep = 0 or (' .prd_stocks_get_sql() . '-sto_prepa'.prd_reservations_get_sql_where_stock('p.prd_id', $dps, true).')>0 )
		';
	}

	// Produits exclus
	if (sizeof($exclude_prd)) {
		$sql .= ' and op.prd_id not in (' . implode(', ', $exclude_prd) . ')';
	}

	// Filtre par produit
	if (sizeof($prd_id)) {
		$sql .= ' and op.prd_id in (' . implode(', ', $prd_id) . ')';
	}

	// Filtre par marque
	if (sizeof($brd_id)) {
		$sql .= ' and p.prd_brd_id in (' . implode(', ', $brd_id) . ')';
	}

	// Filtre par représentant
	if( $seller_id>0 ){
		$sql .= ' and ord_seller_id='.$seller_id;
	}

	// Filtre par identifiant client
	if( is_array($user_ids) && !empty($user_ids) ){
		$sql .= ' and ord_orders.ord_usr_id in (' . implode(',', $user_ids) . ') ';
	}

	$sql .= '
		group by op.prd_ord_id, op.prd_id
		order by sum(prd_qte*prd_price_ht) desc
	';

	$sql .= '
		) as res
		group by tnt_id, id, ref, name, is_sync
		order by total_ht desc
	';

	if ($limit > 0) {
		$sql .= ' limit 0, ' . $limit;
	}

	$res = ria_mysql_query($sql);
	if (!$res) {
		return false;
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction ALIAS permet de récupérer les produits bestsellers en s'appuyant sur les factures.
 *    @param string $date_start Optionnel, date de début pour récupérer les statistiques de ventes
 *    @param string $date_end Optionnel, date de fin pour récupérer les statistiques de ventes
 *    @param int $limit Optionnel, nombre de produits retournés par défaut les 100 premiers sont retournés, mettre 0 pour aucune limite
 *    @param array $origin Optionnel, permet de filtrer les commandes sur son origine pour cela il faut donné un tableau sous la forme array('col'=>'val') avec comme colonne : source, name ou medium
 *    @param bool|null $gescom Optionnel, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 *    @param int $wst_id Optionnel, identifiant d'un site web par défaut on ne tient pas compte des sites web
 *    @param bool $publish Optionnel, par défaut tous les produits sont retournés, mettre true pour n'avoir que ce publié
 *    @param int|array $prd_id Optionnel, identifiant ou tableau d'identifiants de produit
 *    @param int|array $cat_id Optionnel, identifiant d'une catégorie
 *    @param bool $recursive_cat Optionnel, mettre True pour récupèrer les produits des catégories enfants de celle donnée en paramètre ($cat_id), par défaut à False
 *    @param int $brd_id Optionnel, identifiant d'une marque
 *    @param int|array $exclude_prd Optionnel, permet d'exclure un ou plusieurs produits du résultat (donné un identifiant ou tableau d'identifiant, 0 = aucune exclusion)
 *    @param int $seller_id Optionnel, identifiant d'un représentant sur lequel filtrer le résultat
 *    @param array $user_ids Facultatif, ID des utilisateurs à considérer pour le calcul.
 *
 *    @return resource Un tableau contenant :
 *                - id : identifiant du produit
 *                - ref : référence du produit
 *                - name : désignation du produit
 *                - qte : nombre total de vente du produit
 *                - margin : marge brute totale
 *                - total_ht : total ht des ventes du produit
 *                - orders : nombre de commande contenant le produit
 *                - ord_total_ht : total ht des commandes contenant le produit
 */
function stats_bestsellers_invoices_get_array( $date_start=false, $date_end=false, $limit=100, $origin=false, $gescom=null, $wst_id=0, $publish=false, $prd_id=0, $cat_id=0, $recursive_cat=false, $brd_id=0, $exclude_prd=0, $seller_id=0, $user_ids=array() ){

	global $config;
	$stats = array();

	if( isset($config['stats_couchdb_active']) && $config['stats_couchdb_active'] ){
		$query = BigQuery::create(BIGQUERY_DB_INVOICES_STATS)->getInvoiceStats(null, $date_start, $date_end, $user_ids, $prd_id, 0, $seller_id, "prd", null, null, $brd_id);

		if( $query ){
			foreach($query as $s ){
				$s['qte'] = $s['products'];
				$s['total_ht'] = $s['total_ca_ht'];
				$s['inv_total_ht'] = $s['total_ca_ht'];
				$s['margin'] = $s['marge_ht'];
				$s['average_ht'] = 0;
				$s['invoices'] = $s['count'];
				$s['id'] = $s['obj_id_0'];
				$stats[] = $s;
			}
		}
	}else{
		$query = stats_bestsellers_invoices_get($date_start, $date_end, $limit, $origin, $gescom, $wst_id, $publish, $prd_id, $cat_id, $recursive_cat, $brd_id, $exclude_prd, $seller_id, $user_ids );
		if( $query ){
			while( $s = ria_mysql_fetch_assoc($query) ){
				$stats[] = $s;
			}
		}
	}

	return $stats;
}

/** Cette fonction permet de récupérer les produits bestsellers en s'appuyant sur les factures.
 *    @param string $date_start Optionnel, date de début pour récupérer les statistiques de ventes
 *    @param string $date_end Optionnel, date de fin pour récupérer les statistiques de ventes
 *    @param int $limit Optionnel, nombre de produits retournés par défaut les 100 premiers sont retournés, mettre 0 pour aucune limite
 *    @param array $origin Optionnel, permet de filtrer les commandes sur son origine pour cela il faut donné un tableau sous la forme array('col'=>'val') avec comme colonne : source, name ou medium
 *    @param bool|null $gescom Optionnel, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 *    @param int $wst_id Optionnel, identifiant d'un site web par défaut on ne tient pas compte des sites web
 *    @param bool $publish Optionnel, par défaut tous les produits sont retournés, mettre true pour n'avoir que ce publié
 *    @param int|array $prd_id Optionnel, identifiant ou tableau d'identifiants de produit
 *    @param int|array $cat_id Optionnel, identifiant d'une catégorie
 *    @param bool $recursive_cat Optionnel, mettre True pour récupèrer les produits des catégories enfants de celle donnée en paramètre ($cat_id), par défaut à False
 *    @param int $brd_id Optionnel, identifiant d'une marque
 *    @param int|array $exclude_prd Optionnel, permet d'exclure un ou plusieurs produits du résultat (donné un identifiant ou tableau d'identifiant, 0 = aucune exclusion)
 *    @param int $seller_id Optionnel, identifiant d'un représentant sur lequel filtrer le résultat
 *    @param array $user_ids Facultatif, ID des utilisateurs à considérer pour le calcul.
 *
 *    @return resource Un résultat MySQL contenant :
 *                - id : identifiant du produit
 *                - ref : référence du produit
 *                - name : désignation du produit
 *                - qte : nombre total de vente du produit
 *                - margin : marge brute totale
 *                - total_ht : total ht des ventes du produit
 *                - orders : nombre de commande contenant le produit
 *                - ord_total_ht : total ht des commandes contenant le produit
 */
function stats_bestsellers_invoices_get( $date_start=false, $date_end=false, $limit=100, $origin=false, $gescom=null, $wst_id=0, $publish=false, $prd_id=0, $cat_id=0, $recursive_cat=false, $brd_id=0, $exclude_prd=0, $seller_id=0, $user_ids=array() ){
	// Filtre par date de début
	if ($date_start !== false) {
		$date_start = dateparse($date_start);
		if (!isdate($date_start)) {
			return false;
		}
	}

	// Filtre par date de fin
	if ($date_end !== false) {
		$date_end = dateparse($date_end);
		if (!isdate($date_end)) {
			return false;
		}
	}

	// Filtre par nombre de lignes maximum
	if (!is_numeric($limit) || $limit < 0) {
		return false;
	}

	// Préparation du SQL pour la ou les origines de commande
	$sub_origin = ord_orders_get_sql_origin($origin);
	if ($sub_origin === false) {
		return false;
	}

	// Filtre par site d'origine
	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	// Filtre par identifiant de produit
	$prd_id = control_array_integer($prd_id, false);
	if ($prd_id === false) {
		return false;
	}

	// Filtre par identifiant de catégorie
	$cat_id = control_array_integer($cat_id, false);
	if ($cat_id === false) {
		return false;
	}

	// Filtre par identifiant de marque
	$brd_id = control_array_integer($brd_id, false);
	if ($brd_id === false) {
		return false;
	}

	// Produits exclus
	$exclude_prd = control_array_integer($exclude_prd, false);
	if ($exclude_prd === false) {
		return false;
	}

	// Filtre par représentant
	if( !is_numeric($seller_id) || $seller_id<0 ){
		return false;
	}

	global $config;

	if ($publish) {
		if ($config['tnt_id'] == 2) {
			$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
		} else {
			$dps = prd_deposits_get_main();
		}
	}

	$sql = '
	select tnt_id, id, ref, name, sum(total_ht) as total_ht, is_sync, sum(qte) as qte, count(distinct inv_id) as invoices,
	sum(marge) as margin, sum(inv_total_ht) as inv_total_ht
	from (
		select
			ip.prd_tnt_id as tnt_id, ip.prd_id as id, ip.prd_ref as ref, ip.prd_name as name,
			'.($config['use_decimal_qte'] ? 'sum(prd_qte)' : 'cast(sum(prd_qte) as signed)').' as qte,
			sum(ip.prd_qte*(prd_price_ht - ip.prd_ecotaxe)) as total_ht, inv_id, prd_is_sync as is_sync, inv_total_ht,
			( ip.prd_price_ht - ifnull(ip.prd_purchase_avg, ifnull(p.prd_purchase_avg,0)) ) * ip.prd_qte as marge
		from ord_inv_products as ip
			join ord_invoices on ( inv_tnt_id = '.$config['tnt_id'].' and prd_inv_id = inv_id )
			join gu_users on ( usr_tnt_id = '.$config['tnt_id'].' and inv_usr_id = usr_id )
			join prd_products as p on ( p.prd_tnt_id = '.$config['tnt_id'].' and ip.prd_id = p.prd_id )
	';

	if ($publish) {
		$sql .= ' join prd_stocks on (sto_tnt_id = '.$config['tnt_id'].' and sto_prd_id = p.prd_id and sto_dps_id = '.$dps.')';
	}

	if (sizeof($cat_id)) {
		if (!$recursive_cat) {
			$list_cat_ids = implode(', ', $cat_id);
		} else {
			$categories = prd_categories_childs_get_list($cat_id, $publish);
			$list_cat_ids = trim($categories) != '' ? implode(', ', $cat_id).','.$categories : implode(', ', $cat_id);
		}

		$sql .= 'join prd_classify on ( ly_tnt_id = '.$config['tnt_id'].' and p.prd_id = cly_prd_id and cly_cat_id in ('.$list_cat_ids.') )';
	}

	// En cas de filtre sur l'origine alors on active une jointure supplémentaire avec les commandes
	if( $origin !== false || $gescom !== null ){
		$sql .= ' join ord_orders on (ord_tnt_id = '.$config['tnt_id'].' and ord_id = ip.prd_ord_id)';
	}

	if (is_array($origin) && sizeof($origin)) {
		$sql .= '
			join stats_origins on ( stats_tnt_id = '.$config['tnt_id'].' and stats_obj_id_0=ord_id )
		';
	} elseif ($origin === -1) {
		$sql .= '
			left join stats_origins on ( stats_tnt_id = '.$config['tnt_id'].' and ord_id = stats_obj_id_0 )
		';
	}

	$sql .= '
		where ip.prd_tnt_id = '.$config['tnt_id'].'
			and ip.prd_ref not in ( "'.implode('", "', $config['dlv_prd_references']).'" )
			and prd_date_deleted is null
			and usr_date_deleted is null
			and usr_prf_id!='.PRF_ADMIN.'
	';

	// Filtre par date de début
	if ($date_start !== false) {
		$sql .= '
			and date(inv_date)>="'.$date_start.'"
		';
	}

	// Filtre par date de fin
	if ($date_end !== false) {
		$sql .= '
			and date(inv_date)<="'.$date_end.'"
		';
	}

	// Ajout le SQL pour le filtre sur la ou les origines de commande
	$sql .= $sub_origin;

	// Filtre par source (Gestion commerciale ou site web)
	if ($gescom !== null) {
		if ($gescom)
			$sql .= ' and ord_state_id>=3 and ord_pay_id is null';
		else
			$sql .= ' and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) )';
	}

	// Filtre par site d'origine
	if ($wst_id > 0) {
		$sql .= ' and inv_wst_id='.$wst_id;
	}

	// Filtre par indicateur de publication
	if ($publish) {
		$sql .= '
			and p.prd_publish and p.prd_publish_cat
			and ( prd_sleep = 0 or (' .prd_stocks_get_sql().'-sto_prepa'.prd_reservations_get_sql_where_stock('p.prd_id', $dps, true).')>0 )
		';
	}

	// Produits exclus
	if (sizeof($exclude_prd)) {
		$sql .= ' and ip.prd_id not in ('.implode(', ', $exclude_prd).')';
	}

	// Filtre par produit
	if (sizeof($prd_id)) {
		$sql .= ' and ip.prd_id in ('.implode(', ', $prd_id).')';
	}

	// Filtre par marque
	if (sizeof($brd_id)) {
		$sql .= ' and p.prd_brd_id in ('.implode(', ', $brd_id).')';
	}

	// Filtre par représentant
	if( $seller_id>0 ){
		$sql .= ' and (
			inv_seller_id = '.$seller_id.'

			or exists (
				select 1
				from gu_users
				where usr_tnt_id = '.$config['tnt_id'].'
				and usr_id = inv_usr_id
				and usr_seller_id = '.$seller_id.'
			)

			or exists (
				select 1
				from rel_relations_hierarchy
				where rrh_tnt_id = '.$config['tnt_id'].'
					and rrh_rrt_id = 2
					and rrh_src_0 = '.$seller_id.'
					and rrh_dst_0 = inv_usr_id
			)
		)';
	}

	// Filtre par identifiant client
	if( is_array($user_ids) && !empty($user_ids) ){
		$sql .= ' and inv_usr_id in ('.implode(',', $user_ids).') ';
	}

	$sql .= '
		group by ip.prd_inv_id, ip.prd_id, ip.prd_ref, ip.prd_name, p.prd_is_sync
		order by sum(prd_qte*(ip.prd_price_ht - ip.prd_ecotaxe)) desc
	';

	$sql .= '
		) as res
		group by tnt_id, id, ref, name, is_sync
		order by total_ht desc
	';

	if ($limit > 0) {
		$sql .= ' limit 0, '.$limit;
	}

	$res = ria_mysql_query($sql);
	if (!$res) {
		return false;
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les informations de ventes par catégories
 *
 *	@param $cat_id Obligatoire, identifiant d'une catégorie ou tableau d'identifiants de catégories
 *	@param string $date_start Optionnel, date de début pour récupérer les statistiques de ventes
 *	@param string $date_end Optionnel, date de fin pour récupérer les statistiques de ventes
 *	@param $origin Optionnel, permet de filtrer les commandes sur son origine pour cela il faut donné un tableau sous la forme array('col'=>'val') avec comme colonne : source, name ou medium
 *	@param $gescom Optionnel, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 *	@param int $wst_id Optionnel, identifiant d'un site web par défaut on ne tient pas compte des sites web
 *	@param int $seller_id Optionnel, identifiant d'un représentant sur lequel filtrer les statistiques de vente
 *	@param $group Facultatif, période de regroupement du résultat. Les valeurs autorisées sont les suivantes : hour, day, week, month, year
 *	@param $col Facultatif, status de commandes à considérer pour le calcul. Valeurs autorisées : carts, completed, canceled. La valeur par défaut est completed.
 *  @param array $user_ids Facultatif, ID des utilisateurs à considérer pour le calcul.
 * 	@param bool $state_on_line Optionnel, par défaut à false, mettre true pour utiliser le statut sur la ligne de commande plutôt que sur la commande
 *
 *	@return resource Un résultat MySQL contenant :
 *		- qte : nombre total de vente du produit
 *		- margin : marge brute totale
 *		- total_ht : total ht des ventes du produit
 *		- orders : nombre de commande contenant le produit
 *		- ord_total_ht : total ht des commandes contenant le produit
 */
function stats_categories_get_orders($cat_id, $date_start = false, $date_end = false, $origin = false, $gescom = null, $wst_id = 0, $seller_id = 0, $group = '', $col = 'completed', $user_ids = array(), $state_on_line=false)
{
	// Contrôle le paramètre obligatoire $cat_id
	if (!is_array($cat_id)) {
		if (!is_numeric($cat_id) || $cat_id < 0) {
			return false;
		}

		$cat_id = $cat_id > 0 ? array($cat_id) : array();
	} else {
		if (!sizeof($cat_id)) {
			return false;
		}

		foreach ($cat_id as $p) {
			if (!is_numeric($p) || $p <= 0) {
				return false;
			}
		}
	}
	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year', 'hour'))){
		$group = '';
	}

	// Contrôle le paramètre facultatif $date_start
	if ($date_start !== false) {
		$date_start = dateparse($date_start);
		if (!isdate($date_start)) {
			return false;
		}
	}

	// Contrôle le paramètre facultatif $date_end
	if ($date_end !== false) {
		$date_end = dateparse($date_end);
		if (!isdate($date_end)) {
			return false;
		}
	}

	// Contrôle le paramètre facultatif $wst_id
	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	// Contrôle le paramètre facultatif $seller_id
	if( !is_numeric($seller_id) || $seller_id<0 ){
		return false;
	}

	// Préparation du SQL pour la ou les origines de commande
	$sub_origin = ord_orders_get_sql_origin($origin);
	if ($sub_origin === false) {
		return false;
	}

	global $config;

	$sql = '
		select sum(qte_cat) as qte, sum(total_cat) as total_ht, count(ord_id) as orders, sum(ord_margin) as margin, sum(ord_total_ht) as ord_total_ht, sum(ord_total_ht) / count(ord_id) as average_ht
		';
	switch ($group) {
		case 'day' :
			$sql .= ', day ';
			break;
		case 'week' :
			$sql .= ', year, week ';
			break;
		case 'month' :
			$sql .= ', syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= ', year ';
			break;
		case 'hour' :
			$sql .= ', hour ';
			break;
		default:
			$sql .= '';
			break;
	}

	$sql .= "    from ( ";

	switch ($group) {
		case 'day' :
			$sql .= '
				select
					(dayofyear(ord_date) + ((year(ord_date)-year("' . $date_start . '"))*(dayofyear(concat(year(ord_date),\'-12-31\')))) - (dayofyear("' . $date_start . '")))  as "day",

				';
			break;
		case 'week' :
			$sql .= '
				select
					year(ord_date) as year,
					week(ord_date,1)+54*(year(ord_date)-' . substr($date_start, 0, 4) . ')-(select week("' . $date_start . '",1) as first_week) as "week",
				';
			break;
		case 'month' :
			$sql .= '
				select
					year(ord_date) as syear,
					month(ord_date) as smonth,
					((month(ord_date) + ((year(ord_date)-year("' . $date_start . '"))*12)) - (month("' . $date_start . '"))) as "month",
				';
			break;
		case 'year' :
			$sql .= '
				select
					year(ord_date) as year,
				';
			break;
		case 'hour' :
			$sql .= '
				select
					hour(ord_date) as hour,
				';
			break;
		default:
			$sql .= '
				select
			';
			break;
	}
	$sql .= '
		prd_ord_id, prd_tnt_id, ' . ($config['use_decimal_qte'] ? 'sum(prd_qte)' : 'cast(sum(prd_qte) as signed)') . ' as qte_cat, sum(prd_qte * prd_price_ht) as total_cat
			from ord_products as op
				join ord_orders on (op.prd_tnt_id=ord_tnt_id and op.prd_ord_id=ord_id)
				join gu_users on (ord_tnt_id=usr_tnt_id and ord_usr_id=usr_id)
		';

	if (is_array($origin) && sizeof($origin)) {
		$sql .= '
				join stats_origins on ( stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id )
		';
	} elseif ($origin === -1) {
		$sql .= '
				left join stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )
		';
	}

	$sql .= '
			where op.prd_tnt_id = ' . $config['tnt_id'] . '
	';

	if (is_array($user_ids) && !empty($user_ids)) {
		$sql .= ' AND ord_orders.ord_usr_id IN (' . implode(',', $user_ids) . ') ';
	}

	if (sizeof($cat_id)) {
		$sql .= '
				and exists (
					select 1
					from prd_classify
						left join prd_cat_hierarchy on cly_cat_id = cat_child_id and cly_tnt_id = cat_tnt_id
					where cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = op.prd_id
						and (
							cly_cat_id in (' . implode(', ', $cat_id) . ')
							or cat_parent_id in (' . implode(', ', $cat_id) . ')
						)
				)
		';
	}

	$sql .= '
				and op.prd_ref not in (\'' . implode('\', \'', $config['dlv_prd_references']) . '\')
				and usr_prf_id!=' . PRF_ADMIN . '
	';
	switch ($col) {
		case 'carts':
			if( $state_on_line ){
				$sql .= ' and op.prd_state_line in (1,14,21)';
			}else{
				$sql .= ' and ord_state_id in (1,14,21)';
			}
			break;
		case 'completed':
			if( $state_on_line ){
				$sql .= ' and op.prd_state_line in (3,4,5,6,7,8,16,17,18,19,20,24,25)';
			}else{
				$sql .= ' and ord_state_id in (3,4,5,6,7,8,16,17,18,19,20,24,25)';
			}
			break;
		case 'canceled':
			if( $state_on_line ){
				$sql .= ' and op.prd_state_line in (9,10,13)';
			}else{
				$sql .= ' and ord_state_id in (9,10,13)';
			}
			break;
	}

	// Filtre sur la période (date de début)
	if ($date_start !== false) {
		$sql .= '
				and date(ord_date)>=\'' . $date_start . '\'
		';
	}

	// Filtre sur la période (date de fin)
	if ($date_end !== false) {
		$sql .= '
				and date(ord_date)<=\'' . $date_end . '\'
		';
	}

	// Ajout le SQL pour le filtre sur la ou les origines de commande
	$sql .= $sub_origin;

	// Filtre sur l'origine "Gestion commerciale" ou "Web"
	if( $gescom!==null ){
		if( $gescom ){
			$sql .= ' and ord_state_id>=3 and ord_pay_id is null';
		}else{
			$sql .= ' and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) )';
		}
	}

	// Filtre sur le site d'origine
	if( $wst_id>0 ){
		$sql .= ' and ord_wst_id=' . $wst_id;
	}

	// Filtre sur le représentant
	if( $seller_id>0 ){
		$sql .= ' and ord_seller_id='.$seller_id;
	}

	switch ($group) {
		case 'day' :
			$sql .= '
				group by prd_ord_id, day
				order by day
			';
			break;
		case 'week' :
			$sql .= '
				group by prd_ord_id, year(ord_date), week
				order by year(ord_date), week
			';
			break;
		case 'month' :
			$sql .= '
				group by prd_ord_id, year(ord_date), month
				order by year(ord_date), month
			';
			break;
		case 'year' :
			$sql .= '
				group by prd_ord_id, year
				order by year
			';
			break;
		case 'hour' :
			$sql .= '
				group by prd_ord_id, hour(ord_date)
				order by hour(ord_date)
			';
			break;
		default:
			$sql .= '
				group by prd_ord_id, prd_tnt_id
			';
	}
	$sql .='	) as prds
			join ord_orders on (prds.prd_tnt_id=ord_tnt_id and prds.prd_ord_id=ord_id)
	';
	switch ($group) {
		case 'day' :
			$sql .= 'group by day ';
			break;
		case 'week' :
			$sql .= 'group by year, week ';
			break;
		case 'month' :
			$sql .= 'group by syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= 'group by year ';
			break;
		case 'hour' :
			$sql .= 'group by hour ';
			break;
		default:
			$sql .= '';
			break;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les informations de ventes par catégorie (selon les factures)
 *
 *	@param $cat_id Obligatoire, identifiant d'une catégorie ou tableau d'identifiants de catégories
 *	@param string $date_start Optionnel, date de début pour récupérer les statistiques de ventes
 *	@param string $date_end Optionnel, date de fin pour récupérer les statistiques de ventes
 *	@param $origin Optionnel, permet de filtrer les commandes sur son origine pour cela il faut donné un tableau sous la forme array('col'=>'val') avec comme colonne : source, name ou medium
 *	@param $gescom Optionnel, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 *	@param int $wst_id Optionnel, identifiant d'un site web par défaut on ne tient pas compte des sites web
 *	@param int $seller_id Optionnel, identifiant d'un représentant sur lequel filtrer les statistiques de vente
 *	@param $group Facultatif, période de regroupement du résultat. Les valeurs autorisées sont les suivantes : hour, day, week, month, year
 *	@param $col Facultatif, status de commandes à considérer pour le calcul. Valeurs autorisées : carts, completed, canceled. La valeur par défaut est completed.
 *  @param array $user_ids Facultatif, ID des utilisateurs à considérer pour le calcul.
 *
 *	@return resource Un résultat MySQL contenant :
 *		- qte : nombre total de vente du produit
 *		- margin : marge brute totale
 *		- total_ht : total ht des ventes du produit
 *		- invoices : nombre de commande contenant le produit
 *		- inv_total_ht : total ht des commandes contenant le produit
 */
function stats_categories_get_invoices($cat_id, $date_start = false, $date_end = false, $origin = false, $gescom = null, $wst_id = 0, $seller_id = 0, $group = '', $col = 'completed', $user_ids = array())
{
	// Contrôle le paramètre obligatoire $cat_id
	if (!is_array($cat_id)) {
		if (!is_numeric($cat_id) || $cat_id < 0) {
			return false;
		}

		$cat_id = $cat_id > 0 ? array($cat_id) : array();
	} else {
		if (!sizeof($cat_id)) {
			return false;
		}

		foreach ($cat_id as $p) {
			if (!is_numeric($p) || $p <= 0) {
				return false;
			}
		}
	}
	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year', 'hour'))){
		$group = '';
	}

	// Contrôle le paramètre facultatif $date_start
	if ($date_start !== false) {
		$date_start = dateparse($date_start);
		if (!isdate($date_start)) {
			return false;
		}
	}

	// Contrôle le paramètre facultatif $date_end
	if ($date_end !== false) {
		$date_end = dateparse($date_end);
		if (!isdate($date_end)) {
			return false;
		}
	}

	// Contrôle le paramètre facultatif $wst_id
	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	// Contrôle le paramètre facultatif $seller_id
	if( !is_numeric($seller_id) || $seller_id<0 ){
		return false;
	}

	// Préparation du SQL pour la ou les origines de commande
	$sub_origin = ord_orders_get_sql_origin($origin);
	if ($sub_origin === false) {
		return false;
	}

	global $config;

	// sum(inv_margin) as margin,

	$sql = '
		select
			sum(qte_cat) as qte, sum(total_cat) as total_ht, count(inv_id) as invoices, sum(inv_total_ht) as inv_total_ht,
			sum(inv_total_ht) / count(inv_id) as average_ht, sum(marge) as margin
	';

	switch ($group) {
		case 'day' :
			$sql .= ', day ';
			break;
		case 'week' :
			$sql .= ', year, week ';
			break;
		case 'month' :
			$sql .= ', syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= ', year ';
			break;
		case 'hour' :
			$sql .= ', hour ';
			break;
		default:
			$sql .= '';
			break;
	}

	$sql .= "    from ( ";

	switch ($group) {
		case 'day' :
			$sql .= '
				select
					(dayofyear(inv_date) + ((year(inv_date)-year("' . $date_start . '"))*(dayofyear(concat(year(inv_date),\'-12-31\')))) -
					(dayofyear("' . $date_start . '")))  as "day",
				';
			break;
		case 'week' :
			$sql .= '
				select
					year(inv_date) as year,
					week(inv_date,1)+54*(year(inv_date)-' . substr($date_start, 0, 4) . ')-(select week("' . $date_start . '",1) as first_week) as "week",
				';
			break;
		case 'month' :
			$sql .= '
				select
					year(inv_date) as syear,
					month(inv_date) as smonth,
					((month(inv_date) + ((year(inv_date)-year("' . $date_start . '"))*12)) - (month("' . $date_start . '"))) as "month",
				';
			break;
		case 'year' :
			$sql .= '
				select
					year(inv_date) as year,
				';
			break;
		case 'hour' :
			$sql .= '
				select
					hour(inv_date) as hour,
				';
			break;
		default:
			$sql .= '
				select
			';
			break;
	}

	$sql .= '
			prd_inv_id, ip.prd_tnt_id, ' . ($config['use_decimal_qte'] ? 'sum(prd_qte)' : 'cast(sum(prd_qte) as signed)') . ' as qte_cat,
			sum(prd_qte * (ip.prd_price_ht - ip.prd_ecotaxe)) as total_cat,
			( ip.prd_price_ht - ifnull(ip.prd_purchase_avg, ifnull(p.prd_purchase_avg,0)) ) * ip.prd_qte as marge
		from ord_inv_products as ip
			join ord_invoices on (inv_tnt_id = '.$config['tnt_id'].' and ip.prd_inv_id = inv_id)
			join prd_products as p on (p.prd_tnt_id = '.$config['tnt_id'].' and p.prd_id = ip.prd_id)
			join gu_users on (usr_tnt_id = '.$config['tnt_id'].' and inv_usr_id = usr_id)
	';

	// En cas de filtre sur l'origine alors on active une jointure supplémentaire avec les commandes
	if( $origin !== false || $gescom !== null ){
		$sql .= ' join ord_orders on (ord_tnt_id = '.$config['tnt_id'].' and ord_id = ip.prd_ord_id)';
	}

	if (is_array($origin) && sizeof($origin)) {
		$sql .= '
			join stats_origins on ( stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id )
		';
	} elseif ($origin === -1) {
		$sql .= '
			left join stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )
		';
	}

	$sql .= '
		where ip.prd_tnt_id = ' . $config['tnt_id'] . '
			and p.prd_date_deleted is null
			and usr_date_deleted is null
	';

	if (is_array($user_ids) && !empty($user_ids)) {
		$sql .= ' and inv_usr_id in (' . implode(',', $user_ids) . ') ';
	}

	if (sizeof($cat_id)) {
		$sql .= '
				and exists (
					select 1
					from prd_classify
						left join prd_cat_hierarchy on cly_cat_id = cat_child_id and cly_tnt_id = cat_tnt_id
					where cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = ip.prd_id
						and (
							cly_cat_id in (' . implode(', ', $cat_id) . ')
							or cat_parent_id in (' . implode(', ', $cat_id) . ')
						)
				)
		';
	}

	$sql .= '
		and ip.prd_ref not in ("'.implode( '", "', $config['dlv_prd_references'] ).'")
		and usr_prf_id!='.PRF_ADMIN.'
	';

	// Filtre sur la période (date de début)
	if( $date_start !== false ){
		$sql .= '
			and date(inv_date) >= "' . $date_start . '"
		';
	}

	// Filtre sur la période (date de fin)
	if( $date_end !== false ){
		$sql .= '
			and date(inv_date) <= "' . $date_end . '"
		';
	}

	// Ajout le SQL pour le filtre sur la ou les origines de commande
	$sql .= $sub_origin;

	// Filtre sur l'origine "Gestion commerciale" ou "Web"
	if( $gescom!==null ){
		if( $gescom ){
			$sql .= ' and ord_state_id  >= 3 and ord_pay_id is null';
		}else{
			$sql .= ' and ( ord_state_id < 3 or (ord_state_id >= 3 and ord_pay_id is not null) )';
		}
	}

	// Filtre sur le site d'origine
	if( $wst_id>0 ){
		$sql .= ' and inv_wst_id=' . $wst_id;
	}

	// Filtre sur le représentant
	if( $seller_id>0 ){
		$sql .= ' and (
			inv_seller_id = '.$seller_id.'

			or exists (
				select 1
				from gu_users
				where usr_tnt_id = '.$config['tnt_id'].'
				and usr_id = inv_usr_id
				and usr_seller_id = '.$seller_id.'
			)

			or exists (
				select 1
				from rel_relations_hierarchy
				where rrh_tnt_id = '.$config['tnt_id'].'
					and rrh_rrt_id = 2
					and rrh_src_0 = '.$seller_id.'
					and rrh_dst_0 = inv_usr_id
			)
		)';
	}

	switch ($group) {
		case 'day' :
			$sql .= '
				group by prd_inv_id, day
				order by day
			';
			break;
		case 'week' :
			$sql .= '
				group by prd_inv_id, year(inv_date), week
				order by year(inv_date), week
			';
			break;
		case 'month' :
			$sql .= '
				group by prd_inv_id, year(inv_date), month
				order by year(inv_date), month
			';
			break;
		case 'year' :
			$sql .= '
				group by prd_inv_id, year
				order by year
			';
			break;
		case 'hour' :
			$sql .= '
				group by prd_inv_id, hour(inv_date)
				order by hour(inv_date)
			';
			break;
		default:
			$sql .= '
				group by prd_inv_id, ip.prd_tnt_id
			';
	}
	$sql .='	) as prds
			join ord_invoices on (inv_tnt_id = '.$config['tnt_id'].' and prds.prd_inv_id = inv_id)
	';
	switch ($group) {
		case 'day' :
			$sql .= 'group by day ';
			break;
		case 'week' :
			$sql .= 'group by year, week ';
			break;
		case 'month' :
			$sql .= 'group by syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= 'group by year ';
			break;
		case 'hour' :
			$sql .= 'group by hour ';
			break;
		default:
			$sql .= '';
			break;
	}

	return ria_mysql_query($sql);
}
// \endcond


// \cond onlyria
/** Cette fonction permet de récupérer les informations de ventes par marque (selon les commandes).
 *
 *	@param string|int|array $brd_id Optionnel, identifiant d'une catégorie ou tableau d'identifiants de marques (mettre 'nc' pour avoir le CA liés à aucune marque)
 *	@param string $date_start Optionnel, date de début pour récupérer les statistiques de ventes
 *	@param string $date_end Optionnel, date de fin pour récupérer les statistiques de ventes
 *	@param $origin Optionnel, permet de filtrer les commandes sur son origine pour cela il faut donné un tableau sous la forme array('col'=>'val') avec comme colonne : source, name ou medium
 *	@param $gescom Optionnel, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 *	@param int $wst_id Optionnel, identifiant d'un site web par défaut on ne tient pas compte des sites web
 *	@param int $seller_id Optionnel, identifiant d'un représentant sur lequel filtrer les statistiques de vente
 *	@param $group Facultatif, période de regroupement du résultat. Les valeurs autorisées sont les suivantes : hour, day, week, month, year
 *	@param $col Facultatif, status de commandes à considérer pour le calcul. Valeurs autorisées : carts, completed, canceled. La valeur par défaut est completed.
 *  @param array $user_ids Facultatif, ID des utilisateurs à considérer pour le calcul.
 *
 *	@return resource Un résultat MySQL contenant :
 *		- qte : nombre total de vente du produit
 *		- margin : marge brute totale
 *		- total_ht : total ht des ventes du produit
 *		- orders : nombre de commande contenant le produit
 *		- ord_total_ht : total ht des commandes contenant le produit
 */
function stats_brands_get_orders($brd_id, $date_start = false, $date_end = false, $origin = false, $gescom = null, $wst_id = 0, $seller_id = 0,
	$group = '', $col = 'completed', $user_ids = array()
){
	// Contrôle le paramètre obligatoire $brd_id
	if( $brd_id != 'nc' ){
		$brd_id = control_array_integer( $brd_id, false );

		if( $brd_id === false ){
			return false;
		}
	}

	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year', 'hour', 'brand'))){
		$group = '';
	}

	// Contrôle le paramètre facultatif $date_start
	if ($date_start !== false) {
		$date_start = dateparse($date_start);
		if (!isdate($date_start)) {
			return false;
		}
	}

	// Contrôle le paramètre facultatif $date_end
	if ($date_end !== false) {
		$date_end = dateparse($date_end);
		if (!isdate($date_end)) {
			return false;
		}
	}

	// Contrôle le paramètre facultatif $wst_id
	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	// Contrôle le paramètre facultatif $seller_id
	if( !is_numeric($seller_id) || $seller_id<0 ){
		return false;
	}

	// Préparation du SQL pour la ou les origines de commande
	$sub_origin = ord_orders_get_sql_origin($origin);
	if ($sub_origin === false) {
		return false;
	}

	global $config;

	$sql = '
		select
			sum(qte_cat) as qte, sum(total_cat) as total_ht, count(ord_id) as orders, sum(ord_margin) as margin, sum(ord_total_ht) as ord_total_ht,
			sum(ord_total_ht) / count(ord_id) as average_ht, brand
	';

	switch ($group) {
		case 'day' :
			$sql .= ', day ';
			break;
		case 'week' :
			$sql .= ', year, week ';
			break;
		case 'month' :
			$sql .= ', syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= ', year ';
			break;
		case 'hour' :
			$sql .= ', hour ';
			break;
		default:
			$sql .= '';
			break;
	}

	$sql .= "    from ( ";

	switch ($group) {
		case 'day' :
			$sql .= '
				select
					(dayofyear(ord_date) + ((year(ord_date)-year("' . $date_start . '"))*(dayofyear(concat(year(ord_date),\'-12-31\'))))
						- (dayofyear("' . $date_start . '")))  as "day",
				';
			break;
		case 'week' :
			$sql .= '
				select
					year(ord_date) as year,
					week(ord_date,1)+54*(year(ord_date)-' . substr($date_start, 0, 4) . ')-(select week("' . $date_start . '",1) as first_week) as "week",
				';
			break;
		case 'month' :
			$sql .= '
				select
					year(ord_date) as syear,
					month(ord_date) as smonth,
					((month(ord_date) + ((year(ord_date)-year("' . $date_start . '"))*12)) - (month("' . $date_start . '"))) as "month",
				';
			break;
		case 'year' :
			$sql .= '
				select
					year(ord_date) as year,
				';
			break;
		case 'hour' :
			$sql .= '
				select
					hour(ord_date) as hour,
				';
			break;
		default:
			$sql .= '
				select
			';
			break;
	}

	$sql .= '
			prd_ord_id, op.prd_tnt_id, ' . ($config['use_decimal_qte'] ? 'sum(prd_qte)' : 'cast(sum(prd_qte) as signed)') . ' as qte_cat,
			sum(prd_qte * prd_price_ht) as total_cat, p.prd_brd_id as brand
		from ord_products as op
			join prd_products as p on ( p.prd_tnt_id = '.$config['tnt_id'].' and p.prd_id = op.prd_id )
			join ord_orders on (op.prd_tnt_id=ord_tnt_id and op.prd_ord_id=ord_id)
			join gu_users on (ord_tnt_id=usr_tnt_id and ord_usr_id=usr_id)
	';

	if (is_array($origin) && sizeof($origin)) {
		$sql .= '
			join stats_origins on ( stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id )
		';
	} elseif ($origin === -1) {
		$sql .= '
			left join stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )
		';
	}

	$sql .= '
		where op.prd_tnt_id = ' . $config['tnt_id'] . '
	';

	if (is_array($user_ids) && !empty($user_ids)) {
		$sql .= ' and ord_usr_id in (' . implode(',', $user_ids) . ') ';
	}

	$sql .= '
		and op.prd_ref not in (\'' . implode('\', \'', $config['dlv_prd_references']) . '\')
		and usr_prf_id!=' . PRF_ADMIN . '
	';

	switch ($col) {
		case 'carts':
			$sql .= ' and ord_state_id in (1,14,21)';
			break;
		case 'completed':
			$sql .= ' and ord_state_id in (3,4,5,6,7,8,16,17,18,19,20,24,25)';
			break;
		case 'canceled':
			$sql .= ' and ord_state_id in (9,10,13)';
			break;
	}

	// Filtre sur la période (date de début)
	if ($date_start !== false) {
		$sql .= '
			and date(ord_date)>=\'' . $date_start . '\'
		';
	}

	// Filtre sur la période (date de fin)
	if ($date_end !== false) {
		$sql .= '
			and date(ord_date)<=\'' . $date_end . '\'
		';
	}

	// Ajout le SQL pour le filtre sur la ou les origines de commande
	$sql .= $sub_origin;

	// Filtre sur l'origine "Gestion commerciale" ou "Web"
	if( $gescom!==null ){
		if( $gescom ){
			$sql .= ' and ord_state_id>=3 and ord_pay_id is null';
		}else{
			$sql .= ' and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) )';
		}
	}

	// Filtre sur le site d'origine
	if( $wst_id>0 ){
		$sql .= ' and ord_wst_id=' . $wst_id;
	}

	// Filtre sur le représentant
	if( $seller_id>0 ){
		$sql .= ' and ord_seller_id='.$seller_id;
	}

	switch ($group) {
		case 'day' :
			$sql .= '
				group by prd_ord_id, day
				order by day
			';
			break;
		case 'week' :
			$sql .= '
				group by prd_ord_id, year(ord_date), week
				order by year(ord_date), week
			';
			break;
		case 'month' :
			$sql .= '
				group by prd_ord_id, year(ord_date), month
				order by year(ord_date), month
			';
			break;
		case 'year' :
			$sql .= '
				group by prd_ord_id, year
				order by year
			';
			break;
		case 'hour' :
			$sql .= '
				group by prd_ord_id, hour(ord_date)
				order by hour(ord_date)
			';
			break;
		default:
			$sql .= '
				group by prd_ord_id, op.prd_tnt_id, p.prd_brd_id
			';
	}

	$sql .='	) as prds
		join ord_orders on (prds.prd_tnt_id=ord_tnt_id and prds.prd_ord_id=ord_id)
	';

	if( $brd_id === 'nc' ){
		$sql .= ' where brand is null';
	}elseif( is_array($brd_id) && count($brd_id) > 0 ){
		$sql .= ' where brand in ('.implode( ', ', $brd_id ).')';
	}

	switch ($group) {
		case 'day' :
			$sql .= ' group by day ';
			break;
		case 'week' :
			$sql .= ' group by year, week ';
			break;
		case 'month' :
			$sql .= ' group by syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= ' group by year ';
			break;
		case 'hour' :
			$sql .= ' group by hour ';
			break;
		case 'brand' :
			$sql .= ' group by brand ';
			break;
		default:
			$sql .= '';
			break;
	}

	return ria_mysql_query($sql);
}
// \endcond


// \cond onlyria
/** Cette fonction ALIAS permet de récupérer les informations de ventes par marque (selon les factures)
 *
 *	@param string|int|array $brd_id Optionnel, identifiant d'une catégorie ou tableau d'identifiants de marques (mettre 'nc' pour avoir le CA liés à aucune marque)
 *	@param string $date_start Optionnel, date de début pour récupérer les statistiques de ventes
 *	@param string $date_end Optionnel, date de fin pour récupérer les statistiques de ventes
 *	@param $origin Optionnel, permet de filtrer les commandes sur son origine pour cela il faut donné un tableau sous la forme array('col'=>'val') avec comme colonne : source, name ou medium
 *	@param $gescom Optionnel, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 *	@param int $wst_id Optionnel, identifiant d'un site web par défaut on ne tient pas compte des sites web
 *	@param int $seller_id Optionnel, identifiant d'un représentant sur lequel filtrer les statistiques de vente
 *  @param array $user_ids Facultatif, ID des utilisateurs à considérer pour le calcul.
 *	@return resource Un tableau contenant :
 *		- qte : nombre total de vente du produit
 *		- total_ht : total ht des ventes du produit
 *		- invoices : nombre de facture contenant le produit
 *		- inv_total_ht : total ht des commandes contenant le produit
 *		- average_ht : panier moyen en HT
 *		- margin : marge brute totale
 *		- brand : identifiant de la marque (group by) peut-être 'nc' si la marque n'est pas défini sur des produits
 */
function stats_brands_get_invoices_array($brd_id, $date_start = false, $date_end = false, $origin = false, $gescom = null, $wst_id = 0, $seller_id = 0, $user_ids = array()
){
	global $config;
	$stats = array();

	if( isset($config['stats_couchdb_active']) && $config['stats_couchdb_active'] ){
		$query = BigQuery::create(BIGQUERY_DB_INVOICES_STATS)->getInvoiceStats(null, $date_start, $date_end, $user_ids, 0, 0, $seller_id, "brd", null, null, $brd_id);

		if( $query ){
			foreach($query as $s ){
				$s['qte'] = $s['products'];
				$s['total_ht'] = $s['total_ca_ht'];
				$s['inv_total_ht'] = $s['total_ca_ht'];
				$s['margin'] = $s['marge_ht'];
				$s['average_ht'] = 0;
				$s['invoices'] = $s['count'];
				$s['brand'] = $s['obj_id_0'];
				$stats[] = $s;
			}
		}
	}else{
		$query = stats_brands_get_invoices($brd_id, $date_start, $date_end, $origin, $gescom, $wst_id, $seller_id, 'brand', '', $user_ids);
		if( $query ){
			while( $s = ria_mysql_fetch_assoc($query) ){
				$stats[] = $s;
			}
		}
	}

	return $stats;
}
/** Cette fonction permet de récupérer les informations de ventes par marque (selon les factures)
 *
 *	@param string|int|array $brd_id Optionnel, identifiant d'une catégorie ou tableau d'identifiants de marques (mettre 'nc' pour avoir le CA liés à aucune marque)
 *	@param string $date_start Optionnel, date de début pour récupérer les statistiques de ventes
 *	@param string $date_end Optionnel, date de fin pour récupérer les statistiques de ventes
 *	@param $origin Optionnel, permet de filtrer les commandes sur son origine pour cela il faut donné un tableau sous la forme array('col'=>'val') avec comme colonne : source, name ou medium
 *	@param $gescom Optionnel, par défaut on ne tient pas compte de ce paramètre, mettre à true pour ne récupérer que les commandes venant de la gescom ou false pour les commandes web
 *	@param int $wst_id Optionnel, identifiant d'un site web par défaut on ne tient pas compte des sites web
 *	@param int $seller_id Optionnel, identifiant d'un représentant sur lequel filtrer les statistiques de vente
 *	@param $group Facultatif, période de regroupement du résultat. Les valeurs autorisées sont les suivantes : hour, day, week, month, year
 *	@param $col DEPRECATED
 *  @param array $user_ids Facultatif, ID des utilisateurs à considérer pour le calcul.
 *
 *	@return resource Un résultat MySQL contenant :
 *		- qte : nombre total de vente du produit
 *		- total_ht : total ht des ventes du produit
 *		- invoices : nombre de commande contenant le produit
 *		- inv_total_ht : total ht des commandes contenant le produit
 *		- average_ht : panier moyen en HT
 *		- margin : marge brute totale
 *		- brand : identifiant de la marque (group by) peut-être 'nc' si la marque n'est pas défini sur des produits
 */
function stats_brands_get_invoices($brd_id, $date_start = false, $date_end = false, $origin = false, $gescom = null, $wst_id = 0, $seller_id = 0,
	$group = '', $col = 'completed', $user_ids = array()
){
	// Contrôle le paramètre obligatoire $brd_id
	if( $brd_id != 'nc' ){
		$brd_id = control_array_integer( $brd_id, false );

		if( $brd_id === false ){
			return false;
		}
	}

	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year', 'hour', 'brand'))){
		$group = '';
	}

	// Contrôle le paramètre facultatif $date_start
	if ($date_start !== false) {
		$date_start = dateparse($date_start);
		if (!isdate($date_start)) {
			return false;
		}
	}

	// Contrôle le paramètre facultatif $date_end
	if ($date_end !== false) {
		$date_end = dateparse($date_end);
		if (!isdate($date_end)) {
			return false;
		}
	}

	// Contrôle le paramètre facultatif $wst_id
	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	// Contrôle le paramètre facultatif $seller_id
	if( !is_numeric($seller_id) || $seller_id<0 ){
		return false;
	}

	// Préparation du SQL pour la ou les origines de commande
	$sub_origin = ord_orders_get_sql_origin($origin);
	if ($sub_origin === false) {
		return false;
	}

	global $config;

	$sql = '
		select
			sum(qte_cat) as qte, sum(total_cat) as total_ht, count(inv_id) as invoices, sum(inv_total_ht) as inv_total_ht,
			sum(inv_total_ht) / count(inv_id) as average_ht, sum(marge) as margin, brand
	';

	switch ($group) {
		case 'day' :
			$sql .= ', day ';
			break;
		case 'week' :
			$sql .= ', year, week ';
			break;
		case 'month' :
			$sql .= ', syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= ', year ';
			break;
		case 'hour' :
			$sql .= ', hour ';
			break;
		default:
			$sql .= '';
			break;
	}

	$sql .= "    from ( ";

	switch ($group) {
		case 'day' :
			$sql .= '
				select
					(dayofyear(inv_date) + ((year(inv_date)-year("' . $date_start . '"))*(dayofyear(concat(year(inv_date),\'-12-31\'))))
						- (dayofyear("' . $date_start . '")))  as "day",
				';
			break;
		case 'week' :
			$sql .= '
				select
					year(inv_date) as year,
					week(inv_date,1)+54*(year(inv_date)-' . substr($date_start, 0, 4) . ')-(select week("' . $date_start . '",1) as first_week) as "week",
				';
			break;
		case 'month' :
			$sql .= '
				select
					year(inv_date) as syear,
					month(inv_date) as smonth,
					((month(inv_date) + ((year(inv_date)-year("' . $date_start . '"))*12)) - (month("' . $date_start . '"))) as "month",
				';
			break;
		case 'year' :
			$sql .= '
				select
					year(inv_date) as year,
				';
			break;
		case 'hour' :
			$sql .= '
				select
					hour(inv_date) as hour,
				';
			break;
		default:
			$sql .= '
				select
			';
			break;
	}

	$sql .= '
			prd_inv_id, ip.prd_tnt_id, ' . ($config['use_decimal_qte'] ? 'sum(prd_qte)' : 'cast(sum(prd_qte) as signed)') . ' as qte_cat,
			sum(prd_qte * (prd_price_ht - ip.prd_ecotaxe)) as total_cat, p.prd_brd_id as brand,
			( ip.prd_price_ht - ifnull(ip.prd_purchase_avg, ifnull(p.prd_purchase_avg,0)) ) * ip.prd_qte as marge
		from ord_inv_products as ip
			join ord_invoices on (ip.prd_tnt_id=inv_tnt_id and ip.prd_inv_id=inv_id)
			left join prd_products as p on ( p.prd_tnt_id = '.$config['tnt_id'].' and p.prd_id = ip.prd_id )
			left join gu_users on (inv_tnt_id=usr_tnt_id and inv_usr_id=usr_id)
	';

	// En cas de filtre sur l'origine alors on active une jointure supplémentaire avec les commandes
	if( $origin !== false || $gescom !== null ){
		$sql .= ' join ord_orders on (ord_tnt_id = '.$config['tnt_id'].' and ord_id = ip.prd_ord_id)';
	}

	if (is_array($origin) && sizeof($origin)) {
		$sql .= '
			join stats_origins on ( stats_tnt_id=ord_tnt_id and stats_obj_id_0=ord_id )
		';
	} elseif ($origin === -1) {
		$sql .= '
			left join stats_origins on ( ord_tnt_id=stats_tnt_id and ord_id=stats_obj_id_0 )
		';
	}

	$sql .= '
		where ip.prd_tnt_id = ' . $config['tnt_id'] . '
	';

	if (is_array($user_ids) && !empty($user_ids)) {
		$sql .= ' and inv_usr_id in (' . implode(',', $user_ids) . ') ';
	}

	$sql .= '
		and ip.prd_ref not in (\'' . implode('\', \'', $config['dlv_prd_references']) . '\')
		and usr_prf_id!=' . PRF_ADMIN . '
	';

	// Filtre sur la période (date de début)
	if ($date_start !== false) {
		$sql .= '
			and date(inv_date)>=\'' . $date_start . '\'
		';
	}

	// Filtre sur la période (date de fin)
	if ($date_end !== false) {
		$sql .= '
			and date(inv_date)<=\'' . $date_end . '\'
		';
	}

	// Ajout le SQL pour le filtre sur la ou les origines de commande
	$sql .= $sub_origin;

	// Filtre sur l'origine "Gestion commerciale" ou "Web"
	if( $gescom!==null ){
		if( $gescom ){
			$sql .= ' and ord_state_id>=3 and ord_pay_id is null';
		}else{
			$sql .= ' and ( ord_state_id<3 or (ord_state_id>=3 and ord_pay_id is not null) )';
		}
	}

	// Filtre sur le site d'origine
	if( $wst_id>0 ){
		$sql .= ' and inv_wst_id=' . $wst_id;
	}

	// Filtre sur le représentant
	if( $seller_id>0 ){
		$sql .= ' and (
			inv_seller_id = '.$seller_id.'

			or exists (
				select 1
				from gu_users
				where usr_tnt_id = '.$config['tnt_id'].'
				and usr_id = inv_usr_id
				and usr_seller_id = '.$seller_id.'
			)

			or exists (
				select 1
				from rel_relations_hierarchy
				where rrh_tnt_id = '.$config['tnt_id'].'
					and rrh_rrt_id = 2
					and rrh_src_0 = '.$seller_id.'
					and rrh_dst_0 = inv_usr_id
			)
		)';
	}

	switch ($group) {
		case 'day' :
			$sql .= '
				group by prd_inv_id, day
				order by day
			';
			break;
		case 'week' :
			$sql .= '
				group by prd_inv_id, year(inv_date), week
				order by year(inv_date), week
			';
			break;
		case 'month' :
			$sql .= '
				group by prd_inv_id, year(inv_date), month
				order by year(inv_date), month
			';
			break;
		case 'year' :
			$sql .= '
				group by prd_inv_id, year
				order by year
			';
			break;
		case 'hour' :
			$sql .= '
				group by prd_inv_id, hour(inv_date)
				order by hour(inv_date)
			';
			break;
		default:
			$sql .= '
				group by prd_inv_id, ip.prd_tnt_id, p.prd_brd_id
			';
	}

	$sql .='	) as prds
		join ord_invoices on (prds.prd_tnt_id=inv_tnt_id and prds.prd_inv_id=inv_id)
	';

	if( $brd_id === 'nc' ){
		$sql .= ' where brand is null';
	}elseif( is_array($brd_id) && count($brd_id) > 0 ){
		$sql .= ' where brand in ('.implode( ', ', $brd_id ).')';
	}

	switch ($group) {
		case 'day' :
			$sql .= ' group by day ';
			break;
		case 'week' :
			$sql .= ' group by year, week ';
			break;
		case 'month' :
			$sql .= ' group by syear, smonth, month
				';
			break;
		case 'year' :
			$sql .= ' group by year ';
			break;
		case 'hour' :
			$sql .= ' group by hour ';
			break;
		case 'brand' :
			$sql .= ' group by brand ';
			break;
		default:
			$sql .= '';
			break;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'enregistrer une recherche de revendeurs
 *    @param $keyword Obligatoire, recherche effectuée pour trouver les revendeurs
 *    @return bool False Si l'un des paramètres obligatoires est faux ou omis ou en cas d'erreur, sinon l'identifiant attribuée à la recherche
 */
function stats_stores_search_add($keyword){
	if (stats_is_bot()) {
		return true;
	}

	if (trim($keyword) == '') {
		return false;
	}

	global $config;

	$session = session_id();

	$sql = '
		select stat_id
		from stats_stores_search
		where stat_tnt_id = ' . $config['tnt_id'] . '
			and stat_wst_id = ' . $config['wst_id'] . '
			and stat_lng_code = "' . addslashes(i18n::getLang()) . '"
			and stat_keyword = "' . addslashes($keyword) . '"
			and stat_session = "' . addslashes($session) . '"
			and stat_datetime >= "' . date('Y-m-d H:i:s', strtotime('-1 hour')) . '"
	';

	$res = ria_mysql_query($sql);
	if ($res && ria_mysql_num_rows($res)) {
		$r = ria_mysql_fetch_assoc($res);
		return $r['stat_id'];
	}

	$sql = '
		insert into stats_stores_search
			( stat_tnt_id, stat_wst_id, stat_lng_code, stat_datetime, stat_keyword, stat_session )
		values
			( ' . $config['tnt_id'] . ', ' . $config['wst_id'] . ', "' . addslashes(i18n::getLang()) . '", now(), "' . addslashes($keyword) . '", "' . addslashes($session) . '" )
	';

	if (!ria_mysql_query($sql)) {
		return false;
	}

	return ria_mysql_insert_id();
}
// \endcond

// \cond onlyria
/** Cette foncton permet de vérifier qu'un identifiant de recherche de revendeur existe bien
 *    @param $search Obligatoire, identifiant de la recherche
 *    @param int $wst_id Optionnel, identifiant d'un site web, par défaut on utilise l'identifiant du site en cours
 *    @param $lng Optionnel, langue par défaut on utilise la langue en cours d'utilisation
 *    @return bool True si la recherche existe, False dans le cas contraire
 */
function stats_stores_search_exists($search, $wst_id = false, $lng = false){
	global $config;

	$website = $config['wst_id'];
	$language = i18n::getlang();

	if ($wst_id !== false) {
		if (!is_numeric($wst_id) || $wst_id <= 0) {
			return false;
		}

		$website = $wst_id;
	}

	if ($lng !== false) {
		$lng = strtolower2($lng);

		if (!in_array($lng, $config['i18n_lng_used'])) {
			return false;
		}

		$language = $lng;
	}

	$sql = '
		select 1
		from stats_stores_search
		where stat_tnt_id = ' . $config['tnt_id'] . '
			and stat_wst_id = ' . $website . '
			and stat_lng_code = "' . addslashes($language) . '"
			and stat_id = ' . $search . '
	';

	$res = ria_mysql_query($sql);
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** cette fonction permet de récupérer les recherches de revendeurs faites
 *    @param int $wst_id Optionnel, identifiant d'un site internet
 *    @param string $date_start Optionnel, date de début de la récupération
 *    @param string $date_end Optionnel, date de fin de récupération
 *    @param string $lng_code Facultatif, code langue sur lequel filtrer le résultat
 *    @return resource Un résultat MySQL contenant :
 */
function stats_stores_search_get($wst_id = 0, $date_start = false, $date_end = false, $lng_code = ''){
	if (!is_numeric($wst_id) || $wst_id < 0) {
		return false;
	}

	if ($date_start !== false && !isdate($date_start)) {
		return false;
	}

	if ($date_end !== false && !isdate($date_end)) {
		return false;
	}

	global $config;

	$sql = '
		select stat_wst_id as wst_id, stat_keyword as keyword, stat_datetime as date_en
		from stats_stores_search
		where stat_tnt_id=' . $config['tnt_id'] . '
	';

	if ($wst_id > 0) {
		$sql .= ' and stat_wst_id = ' . $wst_id;
	}

	if ($date_start) {
		$sql .= ' and date(stat_datetime) >= "' . addslashes($date_start) . '"';
	}

	if ($date_end) {
		$sql .= ' and date(stat_datetime) <= "' . addslashes($date_end) . '"';
	}

	if (trim($lng_code) != '') {
		$sql .= ' and stat_lng_code = "' . strtolower($lng_code) . '"';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'enregistrer l'affiche d'un revendeur sur le site
 *    @param int|array $str_id Obligatoire, identifiant ou tableau d'identifiants d'un magasin
 *    @param $search Optionnel, identifiant d'une recherche de revendeur (stats_stores_search)
 *    @return bool True si l'enregistrement s'est correctement fait, False dans le cas contraire
 */
function stats_stores_views_add($str_id, $search = 0){
	if (stats_is_bot()) {
		return true;
	}

	$ar_str_id = control_array_integer($str_id);
	if (!$ar_str_id) {
		return false;
	}

	global $config;

	$session = session_id();

	$sql = '
		select 1
		from stats_stores_views
		where stat_tnt_id = ' . $config['tnt_id'] . '
			and stat_wst_id = ' . $config['wst_id'] . '
			and stat_lng_code = "' . addslashes(i18n::getLang()) . '"
			and stat_str_id = "' . $str_id . '"
			and stat_session = "' . addslashes($session) . '"
			and stat_datetime >= "' . date('Y-m-d H:i:s', strtotime('-1 hour')) . '"
			and stat_id ' . (!$search ? 'is null' : '=' . $search) . '
	';

	$res = ria_mysql_query($sql);
	if ($res && ria_mysql_num_rows($res)) {
		return true;
	}

	if (!is_numeric($search) || $search < 0) {
		return false;
	} elseif ($search > 0 && !stats_stores_search_exists($search)) {
		return false;
	}

	$sql = '
		insert into stats_stores_views
			( stat_tnt_id, stat_wst_id, stat_lng_code, stat_str_id, stat_datetime, stat_id, stat_session )
		values
	';

	$first = true;
	foreach ($ar_str_id as $one_str) {
		if (!$first) {
			$sql .= ' , ';
		}

		$sql .= '
			( ' . $config['tnt_id'] . ', ' . $config['wst_id'] . ', "' . addslashes(i18n::getLang()) . '", ' . $str_id . ', now(), ' . ($search ? $search : 'null') . ', "' . addslashes($session) . '" )
		';

		$first = false;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction calcule des statistiques sur le nombre d'affichage d'un magasin suite à une recherche de revendeur
 *    @param int|array $str_id Obligatoire, identifiant ou tableau d'identifiants de magasins
 *    @param $date1 Obligatoire, date de début de prise en compte
 *    @param $date2 Facultatif, date de fin de prise en compte
 *    @param $group Facultatif, type de statistiques (day, hour [par défaut], week, month)
 *    @param int $wst Facultatif, identifiant d'un site particulier pour le locataire courant
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - group : Le nom de la colonne dépend du paramètre $group fourni
 *        - hits : Nombre de pages vues
 */
function stats_stores_search_seen($str_id, $date1, $date2 = '', $group = '', $wst = false){
	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year'))) return false;
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) return false;
	if ($wst && !wst_websites_exists($wst)) return false;

	$ar_str_id = control_array_integer($str_id);
	if (!$ar_str_id) {
		return false;
	}

	global $config;

	switch ($group) {
		case 'day' :
			$sql = 'select stat_datetime as date, (dayofyear(stat_datetime) + ((year(stat_datetime)-year("' . $date1 . '"))*(dayofyear(concat(year(stat_datetime),\'-12-31\')))) - (dayofyear("' . $date1 . '"))) as "day", count(*) as hits ';
			break;
		case 'week' :
			$sql = 'select stat_datetime as date,week(stat_datetime,1)+54*(year(stat_datetime)-' . substr($date1, 0, 4) . ')-(select week("' . $date1 . '",1) as first_week) - 1 as "week",  count(*) as hits';
			break;
		case 'month' :
			$sql = 'select stat_datetime as date,month(stat_datetime) as smonth, year(stat_datetime) as syear,((month(stat_datetime) + ((year(stat_datetime)-year("' . $date1 . '"))*12)) - (month("' . $date1 . '"))) as "month", count(*) as hits';
			break;
		case 'year' :
			$sql = 'select stat_datetime as date,year(stat_datetime) as break, count(*) as hits';
			break;
		default :
			$sql = 'select stat_datetime as date,hour(stat_datetime) as "hour", count(*) as hits';
	}

	$sql .= '
		from stats_stores_views
		where stat_tnt_id=' . $config['tnt_id'] . '
			and stat_str_id in (' . implode(', ', $ar_str_id) . ')
			' . ($wst ? ' and stat_wst_id=' . $wst : '') . '
	';

	if (trim($date2) != '') {
		$sql .= ' and date(stat_datetime)>=\'' . $date1 . '\' and date(stat_datetime)<=\'' . $date2 . '\'';
	} else
		$sql .= 'and date(stat_datetime)=\'' . $date1 . '\'';

	switch ($group) {
		case 'day' :
			$sql .= ' group by dayofyear(stat_datetime)';
			break;
		case 'week' :
			$sql .= ' group by year(stat_datetime), week(stat_datetime,1)';
			break;
		case 'month' :
			$sql .= ' group by year(stat_datetime), month(stat_datetime)';
			break;
		case 'year' :
			$sql .= ' group by year(stat_datetime)';
			break;
		default :
			$sql .= ' group by hour(stat_datetime) order by stat_datetime';
			break;
	}
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction calcule des statistiques sur le nombre de contact pour un magasin
 *    @param int|array $str_id Obligatoire, identifiant ou tableau d'identifiants de magasins
 *    @param $date1 Obligatoire, date de début de prise en compte
 *    @param $date2 Facultatif, date de fin de prise en compte
 *    @param $group Facultatif, type de statistiques (day, hour [par défaut], week, month)
 *    @param int $wst Facultatif, identifiant d'un site particulier pour le locataire courant
 *    @param $groupby_type Facultatif, période de temps sur laquelle les données seront agrégées. Les valeurs acceptées sont les suivantes : hour, day, week, month, year.
 * 		@param $type Optionnel, identifiant d'un type de message (cf. gu_messages_types)
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - group : Le nom de la colonne dépend du paramètre $group fourni
 *        - hits : Nombre de pages vues
 *        - type : type de message
 */
function stats_stores_contacts($str_id, $date1, $date2='', $group='', $wst=false, $groupby_type=false, $type=0){
	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year'))) return false;
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) return false;
	if ($wst && !wst_websites_exists($wst)) return false;

	$ar_str_id = control_array_integer($str_id);
	if (!$ar_str_id) {
		return false;
	}

	global $config;

	switch ($group) {
		case 'day' :
			$sql = 'select cnt_date_created as date, (dayofyear(cnt_date_created) + ((year(cnt_date_created)-year("' . $date1 . '"))*(dayofyear(concat(year(cnt_date_created),\'-12-31\')))) - (dayofyear("' . $date1 . '"))) as "day", count(*) as hits, cnt_type';
			break;
		case 'week' :
			$sql = 'select cnt_date_created as date,week(cnt_date_created,1)+54*(year(cnt_date_created)-' . substr($date1, 0, 4) . ')-(select week("' . $date1 . '",1) as first_week) as "week",  count(*) as hits, cnt_type';
			break;
		case 'month' :
			$sql = 'select cnt_date_created as date,month(cnt_date_created) as smonth, year(cnt_date_created) as syear,((month(cnt_date_created) + ((year(cnt_date_created)-year("' . $date1 . '"))*12)) - (month("' . $date1 . '"))) as "month", count(*) as hits, cnt_type';
			break;
		case 'year' :
			$sql = 'select cnt_date_created as date,year(cnt_date_created) as year, count(*) as hits, cnt_type';
			break;
		default :
			$sql = 'select cnt_date_created as date,hour(cnt_date_created) as "hour", count(*) as hits, cnt_type';
	}

	$sql .= '
		from gu_messages
		where cnt_tnt_id=' . $config['tnt_id'] . '
			and cnt_str_id in (' . implode(', ', $ar_str_id) . ')
			' . ($wst ? ' and cnt_wst_id=' . $wst : '') . '
	';

	if (trim($date2) != '') {
		$sql .= ' and date(cnt_date_created)>=\'' . $date1 . '\' and date(cnt_date_created)<=\'' . $date2 . '\'';
	} else {
		$sql .= 'and date(cnt_date_created)=\'' . $date1 . '\'';
	}

	if($type != 0){
		$sql .= ' and cnt_type = ' . $type;
	}

	switch ($group) {
		case 'day' :
			$sql .= ' group by dayofyear(cnt_date_created)' . ($groupby_type ? ', cnt_type' : '');
			break;
		case 'week' :
			$sql .= ' group by year(cnt_date_created), week(cnt_date_created,1)' . ($groupby_type ? ', cnt_type' : '');
			break;
		case 'month' :
			$sql .= ' group by year(cnt_date_created), month(cnt_date_created)' . ($groupby_type ? ', cnt_type' : '');
			break;
		case 'year' :
			$sql .= ' group by year(cnt_date_created)' . ($groupby_type ? ', cnt_type' : '');
			break;
		default :
			$sql .= ' group by hour(cnt_date_created)' . ($groupby_type ? ', cnt_type' : '') . ' order by cnt_date_created';
			break;
	}
	return ria_mysql_query($sql);
}

/** Cette fonction retourne les type pour les stats
 *    @param int|array $str_id Obligatoire, identifiant ou tableau d'identifiants de magasins
 *    @param $date1 Obligatoire, date de début de prise en compte
 *    @param $date2 Facultatif, date de fin de prise en compte
 *    @param int $wst Facultatif, identifiant d'un site particulier pour le locataire courant
 *    @param $groupby_type --- Ce paramètre n'est plus prit en charge ---
 *
 *    @return bool False en cas d'échec
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - group : Le nom de la colonne dépend du paramètre $group fourni
 *        - hits : Nombre de pages vues
 *        - type : type de message
 */
function stats_stores_contacts_type($str_id, $date1, $date2 = '', $wst = false, $groupby_type = false){
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) return false;
	if ($wst && !wst_websites_exists($wst)) return false;

	$ar_str_id = control_array_integer($str_id);
	if (!$ar_str_id) {
		return false;
	}

	global $config;

	$sql ='select distinct cnt_type';

	$sql .= '
		from gu_messages
		where cnt_tnt_id=' . $config['tnt_id'] . '
			and cnt_str_id in (' . implode(', ', $ar_str_id) . ')
			' . ($wst ? ' and cnt_wst_id=' . $wst : '') . '
	';

	if (trim($date2) != '') {
		$sql .= ' and date(cnt_date_created)>=\'' . $date1 . '\' and date(cnt_date_created)<=\'' . $date2 . '\'';
	} else {
		$sql .= 'and date(cnt_date_created)=\'' . $date1 . '\'';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction calcule des statistiques sur la quantité de chaque produit commandé par un client sur une période donnée.
 *    @param int $usr_id Obligatoire, identifiant du client.
 *    @param string $date_start Optionnel, début de la période à prendre en compte.
 *    @param string $date_end Optionnel, fin de la période à prendre en compte.
 *    @param $colisage_grp Optionnel, détermine si chaque conditionnement différent du produit doit être géré comme un produit différent.
 *    @param $inc_invoices Optionnel, détermine si les factures sans commande d'origine doivent être prises en compte.
 *    @param $fld Optionnel, tableau de champs avancés permettant de répartir les produits plus finement (cf. Sodip : code formule).
 *        Le tableau se compose de sous-tableaux, contenant chacun 3 ou 4 éléments :
 *            - identifiant du champ avancé.
 *            - nom de l'alias des valeurs du champ dans le résultat final (ne peut pas être vide).
 *            - valeur par défaut si le champ n'est pas valorisé.
 *            - identifiant de classe (CLS_ORD_PRODUCT ou CLS_INV_PRODUCT), si $inc_invoices est activé uniquement.
 *    @param $check_col_exist Optionnel, permet de vérifier que le conditionnement sur la ligne de commande (ou de facture) est toujours actif. Sinon, la ligne n'est pas retournée. la variable $colisage_grp doit être activée.
 *
 *    @return bool False en cas d'échec.
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - prd_id : identifiant du produit.
 *        - qte : quantité totale commandée pour ce produit durant la période.
 *        - frequency : nombre de fois où le produit a été commandé durant la période.
 *        - [col_id] : identifiant du conditionnement, si $colisage_grp est activé. 0 indique un produit non conditionné.
 *        - [alias_value] : nom de la colonne issu du champ avancé, tel que défini dans le paramètre $fld, s'il est spécifié.
 */
function stats_user_products_ordered($usr_id, $date_start = null, $date_end = null, $colisage_grp = false, $inc_invoices = false, $fld = array(), $check_col_exist = false){

	if (!is_numeric($usr_id) || $usr_id <= 0) {
		return false;
	}

	if ($date_start !== null) {
		if (!isdateheure($date_start)) {
			return false;
		}
		$date_start = dateheureparse($date_start);
	}

	if ($date_end !== null) {
		if (!isdateheure($date_end)) {
			return false;
		}
		$date_end = dateheureparse($date_end);
	}

	if (!is_array($fld)) {
		$fld = array();
	}

	foreach ($fld as $ar_fld_data) {
		if (sizeof($ar_fld_data) != ($inc_invoices ? 4 : 3)) {
			return false;
		}
		if (!trim($ar_fld_data[1])) {
			return false;
		}
		$fld_cls = fld_fields_get_class($ar_fld_data[0]);
		if (!$inc_invoices && $fld_cls != CLS_ORD_PRODUCT) {
			return false;
		} elseif (!in_array($fld_cls, array(CLS_ORD_PRODUCT, CLS_INV_PRODUCT)) || $fld_cls != $ar_fld_data[3]) {
			return false;
		}
	}

	global $config;

	// colonnes du SELECT interne
	$select_col = array('prd_id', 'sum(prd_qte) as "qte"');
	// colonnes du GROUP BY interne
	$group_col = array('prd_id');
	// colonnes du SELECT externe
	$full_select_col = array('tmp.prd_id', ($config['use_decimal_qte'] ? 'sum(tmp.qte)' : 'cast(sum(tmp.qte) as signed)') . ' as "qte"', 'count(*) as "frequency"');
	// colonnes du GROUP BY externe
	$full_group_col = array('tmp.prd_id');

	// ajoute le conditionnement dans les clauses
	if ($colisage_grp) {
		$select_col[1] = 'sum(prd_qte / ifnull(col_qte, 1)) as "qte"'; // ! prend en compte la quantité conditionnée !
		$select_col[] = 'ifnull(col_id, 0) as col_id';
		$group_col[] = 'ifnull(col_id, 0)';
		$full_select_col[] = 'tmp.col_id';
		$full_group_col[] = 'tmp.col_id';
	}

	// ajoute les champs avancés dans les clauses
	$i = 0;
	foreach ($fld as $ar_fld_data) {
		// on utilise les alias donnés pour la classe CLS_ORD_PRODUCT uniquement
		if ($inc_invoices && $ar_fld_data[3] != CLS_ORD_PRODUCT) {
			continue;
		}
		$i++;
		$select_col[] = 'ifnull(v' . $i . '.pv_value, "' . addslashes($ar_fld_data[2]) . '") as "' . addslashes($ar_fld_data[1]) . '"';
		$group_col[] = 'ifnull(v' . $i . '.pv_value, "' . addslashes($ar_fld_data[2]) . '")';
		$full_select_col[] = 'tmp.`' . addslashes($ar_fld_data[1]) . '`'; // ! quotes spéciales !
		$full_group_col[] = 'tmp.`' . addslashes($ar_fld_data[1]) . '`'; // ! quotes spéciales !
	}

	$sql = '
		select ' . implode(', ', $full_select_col) . '
		from (
			select ' . implode(', ', $select_col) . '
			from ord_orders
			join ord_products on ord_id = prd_ord_id and ord_tnt_id = prd_tnt_id
	';

	if ($colisage_grp) {
		$sql .= '
			left join fld_object_values as vcol
				on prd_tnt_id = vcol.pv_tnt_id and prd_ord_id = vcol.pv_obj_id_0 and prd_id = vcol.pv_obj_id_1 and prd_line_id = vcol.pv_obj_id_2
				and vcol.pv_lng_code = "' . $config['i18n_lng'] . '" and vcol.pv_fld_id = ' . _FLD_PRD_COL_ORD_PRODUCT . '
			left join prd_colisage_types on cast(pv_value as unsigned) = col_id and pv_tnt_id = col_tnt_id and col_is_deleted = 0
		';
	}

	$i = 0;
	foreach ($fld as $ar_fld_data) {
		if ($inc_invoices && $ar_fld_data[3] != CLS_ORD_PRODUCT) {
			continue;
		}
		$i++;
		$sql .= '
			left join fld_object_values as v' . $i . ' on
				prd_tnt_id = v' . $i . '.pv_tnt_id and prd_ord_id = v' . $i . '.pv_obj_id_0
				and prd_id = v' . $i . '.pv_obj_id_1 and prd_line_id = v' . $i . '.pv_obj_id_2
				and v' . $i . '.pv_lng_code = "' . $config['i18n_lng'] . '" and v' . $i . '.pv_fld_id = ' . $ar_fld_data[0] . '
		';
	}

	$sql .= '
		where ord_tnt_id = ' . $config['tnt_id'] . '
		and ord_usr_id = ' . $usr_id . '
		and ord_state_id in (' . implode(', ', ord_states_get_ord_valid()) . ')
	';

	if ($date_start !== null) {
		$sql .= ' and date(ord_date) >= date("' . $date_start . '")';
	}

	if ($date_end !== null) {
		$sql .= ' and date(ord_date) < date("' . $date_end . '")';
	}

	if ($colisage_grp && $check_col_exist) {
		$sql .= '
			and (
				col_id is null or exists (
					select 1 from prd_colisage_classify as colcly
					where colcly.cly_tnt_id = '.$config['tnt_id'].' and colcly.cly_prd_id = prd_id and colcly.cly_col_id = col_id
				)
			)
		';
	}

	$sql .= '
		group by ' . implode(', ', $group_col) . '
	';

	if ($inc_invoices) {

		$sql .= '
				union all

				select ' . implode(', ', $select_col) . '
				from ord_invoices
				join ord_inv_products on inv_id = prd_inv_id and inv_tnt_id = prd_tnt_id
		';

		if ($colisage_grp) {
			$sql .= '
				left join fld_object_values as vcol
					on prd_tnt_id = vcol.pv_tnt_id and prd_inv_id = vcol.pv_obj_id_0 and prd_id = vcol.pv_obj_id_1 and prd_line_id = vcol.pv_obj_id_2
					and vcol.pv_lng_code = "' . $config['i18n_lng'] . '" and vcol.pv_fld_id = ' . _FLD_PRD_COL_INV_PRODUCT . '
				left join prd_colisage_types on cast(pv_value as unsigned) = col_id and pv_tnt_id = col_tnt_id and col_is_deleted = 0
			';
		}

		$i = 0;
		foreach ($fld as $ar_fld_data) {
			if ($ar_fld_data[3] != CLS_INV_PRODUCT) {
				continue;
			}
			$i++;
			$sql .= '
				left join fld_object_values as v' . $i . ' on
					prd_tnt_id = v' . $i . '.pv_tnt_id and prd_inv_id = v' . $i . '.pv_obj_id_0
					and prd_id = v' . $i . '.pv_obj_id_1 and prd_line_id = v' . $i . '.pv_obj_id_2
					and v' . $i . '.pv_lng_code = "' . $config['i18n_lng'] . '" and v' . $i . '.pv_fld_id = ' . $ar_fld_data[0] . '
			';
		}

		$sql .= '
			where inv_tnt_id = ' . $config['tnt_id'] . '
			and inv_usr_id = ' . $usr_id . '
			and inv_masked = 0
		';

		if ($date_start !== null) {
			$sql .= ' and date(inv_date) >= date("' . $date_start . '")';
		}

		if ($date_end !== null) {
			$sql .= ' and date(inv_date) < date("' . $date_end . '")';
		}

		if ($colisage_grp && $check_col_exist) {
			$sql .= '
				and (
					col_id is null or exists (
						select 1 from prd_colisage_classify as colcly
						where colcly.cly_tnt_id = '.$config['tnt_id'].' and colcly.cly_prd_id = prd_id and colcly.cly_col_id = col_id
					)
				)
			';
		}

		$sql .= '
				and prd_ord_id is null
				group by ' . implode(', ', $group_col) . '
		';

	}

	$sql .= '
		) as tmp
		group by ' . implode(', ', $full_group_col) . '
	';

	$res = ria_mysql_query($sql);

	if (ria_mysql_errno()) {
		error_log('stats_user_products_ordered : ' . mysql_error() . "\n" . $sql);
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction génère des statistiques de commandes, par produit et pour un client, suivant un cadencier (période + périodicité).
 *    Par défaut, les résultats sont triés par code article croissant.
 *    @param int $usr_id Obligatoire, identifiant du client.
 *    @param string $date_start Obligatoire, date de début de la période à prendre en compte (Null pour toute la période).
 *    @param string $date_end Obligatoire, date de fin de la période à prendre en compte (Null pour toute la période).
 *    @param $periods Obligatoire, libellé ou tableau des libellés de périodicité pour le regroupement des statistiques. Les valeurs autorisées sont :
 *        - year : par année.
 *        - semester : par semestre.
 *        - trimester : par trimestre.
 *        - month : par mois.
 *        - dayofweek : par jour de la semaine.
 *        - week : par numéro de semaine.
 *        - dayofmonth : par jour du mois.
 *    @param int $cat Optionnel, identifiant d'une catégorie. Seuls les produits de cette catégorie seront retournés.
 *    @param $catchilds Optionnel, détermine si les catégories enfants sont prises en compte si $cat est spécifié.
 *    @param $states Optionnel, tableau de statuts de commande à prendre en compte. Par défaut, les statuts retournés par la fonction ord_states_get_ord_valid sont utilisés.
 *    @param $colisages Optionnel, détermine si les statistiques pour chaque conditionnement sont calculées séparément.
 *    @param $inc_invoices Optionnel, détermine si les factures sans commande à l'origine sont prises en compte dans les calculs.
 *    @param $sort Optionnel, tableau associatif de tri. Les clés doivent être prises dans les colonnes suivantes : id, ref, name, qte, count, total, élements de $periods. La valeur est "asc" ou "desc".
 *
 *    @return bool False en cas d'échec.
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - prd_id : identifiant du produit.
 *        - [col_id] : identifiant du conditionnement (si $colisages activé).
 *        - qte : quantité totale commandée sur la période.
 *        - count : nombre de fois où le produit a été vendu sur la période.
 *        - total_ht : total HT généré par les ventes sur la période.
 *        - total_ttc : total TTC généré par les ventes sur la période.
 *        - [year / month / ...] : périodicités incluses dans $periods (note : pour les semestres, les valeurs sont 1 et 2 ; pour les trimestres, 1, 2, 3 et 4)
 */
function stats_user_products_period($usr_id, $date_start, $date_end, $periods, $cat = 0, $catchilds = false, $states = array(), $colisages = false, $inc_invoices = false, $sort = false){

	if (!gu_users_exists($usr_id)) {
		return false;
	}

	if ($date_start !== null) {
		if (!isdateheure($date_start)) {
			return false;
		}
		$date_start = dateheureparse($date_start);
	}

	if ($date_end !== null) {
		if (!isdateheure($date_end)) {
			return false;
		}
		$date_end = dateheureparse($date_end);
	}

	if (!is_array($periods)) {
		$periods = array($periods);
	}

	// périodes autorisées triées
	$allow_periods = array('year', 'semester', 'trimester', 'month', 'week', 'dayofmonth', 'dayofweek');

	// contrôle les périodes, les dédoublonne, les trie et réassigne le tableau
	// note : le pseudo tri ne fonctionnera plus si sizeof($allow_periods) > 10
	$order_periods = array();
	foreach ($periods as $p) {
		$p = strtolower(trim($p));
		$index_of = array_search($p, $allow_periods);
		if ($index_of === false) {
			return false;
		}
		if (!isset($order_periods['a' . $index_of])) {
			$order_periods['a' . $index_of] = $p;
		}
	}
	ksort($order_periods);
	$periods = array_values($order_periods);

	if (!is_numeric($cat) || $cat < 0) {
		return false;
	} elseif ($cat > 0 && !prd_categories_exists($cat)) {
		return false;
	}

	$states = control_array_integer($states, false);
	if ($states === false) {
		return false;
	}

	if (!sizeof($states)) {
		$states = ord_states_get_ord_valid();
	}

	global $config;

	$select = array(
		'p.prd_id',
		'sum(prd_qte)',
		'count(*) as "count"',
		'sum(prd_price_ht * prd_qte) as total_ht',
		'sum(ifnull(prd_price_ttc, prd_price_ht * prd_tva_rate) * prd_qte) as total_ttc'
	);
	$groupby = array('p.prd_id');
	if ($colisages) {
		$select[1] = 'sum(prd_qte / if(ord_piece != "", if(ifnull(p.prd_sell_weight, 0) = 1, ifnull(col_qte / ' . $config['weight_col_calc_lines'] . ', 1), ifnull(col_qte, 1) ), 1))';
		$select[] = 'ifnull(col_id, 0)';
		$groupby[] = 'ifnull(col_id, 0)';
	}

	if (!$config['use_decimal_qte']) {
		$select[1] = 'cast(' . $select[1] . ' as signed)';
	}
	$select[1] = $select[1] . ' as "qte"';

	foreach ($periods as $p) {
		if ($p == 'semester') {
			$groupby[] = 'if(month(ord_date) > 6, 2, 1)';
		} elseif ($p == 'trimester') {
			$groupby[] = 'if(month(ord_date) < 4, 1, if(month(ord_date) < 7, 2, if(month(ord_date) < 10, 3, 4)))';
		} else {
			// la période a un équivalent exact en nom de fonction SQL
			$groupby[] = $p . '(ord_date)';
		}
		$select[] = $groupby[sizeof($groupby) - 1] . ' "' . $p . '"';
	}

	$sql = '
		select ' . implode(', ', $select) . '
		from
			ord_orders
			join ord_products as op on ord_tnt_id = op.prd_tnt_id and ord_id = op.prd_ord_id
			join prd_products as p on op.prd_tnt_id = p.prd_tnt_id and op.prd_id = p.prd_id
	';
	if ($colisages) {
		$sql .= '
			left join fld_object_values on
				op.prd_tnt_id = pv_tnt_id and op.prd_ord_id = pv_obj_id_0 and op.prd_id = pv_obj_id_1 and op.prd_line_id = pv_obj_id_2
				and pv_fld_id = ' . _FLD_PRD_COL_ORD_PRODUCT . ' and pv_lng_code = "' . $config['i18n_lng'] . '"
			left join prd_colisage_types on pv_tnt_id = col_tnt_id and cast(pv_value as unsigned) = col_id and col_is_deleted = 0
		';
	}
	$sql .= '
		where ord_tnt_id = ' . $config['tnt_id'] . '
			and ord_usr_id = ' . $usr_id . '
			and ord_state_id in (' . implode(', ', $states) . ')
			and p.prd_date_deleted is null
	';
	if ($date_start !== null) {
		$sql .= ' and date(ord_date) >= "' . $date_start . '"';
	}
	if ($date_end !== null) {
		$sql .= ' and date(ord_date) <= "' . $date_end . '"';
	}
	if ($cat) {
		$sql .= ' and (
			exists (
				select 1 from prd_classify
				where cly_tnt_id = '.$config['tnt_id'].'
				and cly_prd_id = p.prd_id
				and cly_cat_id = ' . $cat . '
			)
		';
		if ($catchilds) {
			$sql .= ' or exists (
					select 1 from prd_classify
					join prd_cat_hierarchy on cly_tnt_id = cat_tnt_id and cly_cat_id = cat_child_id
					where cly_tnt_id = '.$config['tnt_id'].'
					and cly_prd_id = p.prd_id
					and cat_parent_id = ' . $cat . '
				)
			';
		}
		$sql .= ')';
	}

	$sql .= '
		group by ' . implode(', ', $groupby) . '
	';

	$sort_final = array();

	if (is_array($sort) && sizeof($sort)) {
		foreach ($sort as $col => $dir) {
			$dir = strtolower(trim($dir)) == 'desc' ? 'desc' : 'asc';
			$col = strtolower(trim($col));
			switch ($col) {
				case 'id':
					$sort_final[] = 'p.prd_id ' . $dir;
					break;
				case 'ref':
					$sort_final[] = 'p.prd_ref ' . $dir;
					break;
				case 'name':
					$sort_final[] = 'p.prd_name ' . $dir;
					break;
				case 'qte':
					$sort_final[] = 'qte ' . $dir;
					break;
				case 'count':
					$sort_final[] = 'count ' . $dir;
					break;
				case 'total':
					$sort_final[] = 'total_ht ' . $dir;
					break;
				default:
					if (in_array($col, $periods)) {
						$sort_final[] = $col . ' ' . $dir;
					}
					break;
			}
		}
	}

	if (!sizeof($sort_final)) {
		$sort_final[] = 'prd_ref asc';
	}

	$sql .= ' order by ' . implode(', ', $sort_final);

	$res = ria_mysql_query($sql);

	if (ria_mysql_errno()) {
		error_log('stats_user_products_period ' . mysql_error() . "\n" . $sql);
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction récupère les offres sur une période, pour un produit et pour un concurrent, suivant un cadencier (période + périodicité).
 *    Par défaut, les résultats sont triés par article plus vendu.
 *    @param $prd_id Obligatoire, identifiant d'un produit.
 *    @param $cpt_id Obligatoire, identifiant d'un concurrent.
 *    @param $date1 Obligatoire, date de début de la période à prendre en compte (Null pour toute la période).
 *    @param $date2 Obligatoire, date de fin de la période à prendre en compte (Null pour toute la période).
 *    @param $group Obligatoire, libellé ou tableau des libellés de périodicité pour le regroupement des statistiques. Les valeurs autorisées sont :
 *        - year : par année.
 *        - semester : par semestre.
 *        - trimester : par trimestre.
 *        - month : par mois.
 *        - dayofweek : par jour de la semaine.
 *        - week : par numéro de semaine.
 *        - dayofmonth : par jour du mois.
 *    @param $last Optionnel, indique si l'on récupère la dernière actualisation, false par default.
 *
 *    @return bool False en cas d'échec.
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - price : le prix de l'offre pour une offre.
 *        - [year / month / ...] : périodicités incluses dans $periods (note : pour les semestres, les valeurs sont 1 et 2 ; pour les trimestres, 1, 2, 3 et 4)
 */
function stats_price_watching_offers($prd_id, $cpt_id, $date1, $date2 = '', $group = '', $last = false){
	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year'))) return false;
	if (!isdate($date1) || ($date2 != '' && !isdate($date2))) return false;
	global $config;
	switch ($group) {
		case 'day' :
			$sql = 'select ofr_date_created as date, day(ofr_date_created) as "day", ofr_landedprice as price, ofr_promo_price as promo_price';
			break;
		case 'week' :
			$sql = 'select week(ofr_date_created) as "week",  ofr_landedprice as price, ofr_promo_price as promo_price';
			break;
		case 'month' :
			$sql = 'select month(ofr_date_created) as "month", month(ofr_date_created) as "smonth", year(ofr_date_created) as "year", year(ofr_date_created) as "syear", ofr_landedprice as price, ofr_promo_price as promo_price';
			break;
		case 'year' :
			$sql = 'select year(ofr_date_created) as "year", year(ofr_date_created) as "syear", ofr_landedprice as price, ofr_promo_price as promo_price';
			break;
		default :
			$sql = 'select hour(ofr_date_created) as "hour", ofr_landedprice as price, ofr_promo_price as promo_price';
	}

	$sql .= ' from prw_offers
		where ofr_tnt_id=' . $config['tnt_id'] . '
		and ofr_prd_id = ' . $prd_id . '
		and ofr_cpt_id = ' . $cpt_id;

	if (trim($date2) != '') {
		$sql .= ' and date(ofr_date_created)>=\'' . $date1 . '\' and date(ofr_date_created)<=\'' . $date2 . '\'';
	} else {
		$sql .= 'and date(ofr_date_created)=\'' . $date1 . '\'';
	}


	switch ($group) {
		case 'day' :
			$sql .= ' group by day(ofr_date_created)';
			break;
		case 'week' :
			$sql .= ' group by year(ofr_date_created), week(ofr_date_created,1)';
			break;
		case 'month' :
			$sql .= ' group by year(ofr_date_created), month(ofr_date_created)';
			break;
		case 'year' :
			$sql .= ' group by year(ofr_date_created)';
			break;
		default :
			$sql .= ' group by hour(ofr_date_created)';
	}
	if ($last) {
		$sql .= 'union (';
		switch ($group) {
			case 'day' :
				$sql .= 'select ofr_date_created as date, day(ofr_date_created) as "day", ofr_landedprice as price, ofr_promo_price as promo_price';
				break;
			case 'week' :
				$sql .= 'select week(ofr_date_created) as "week",  ofr_landedprice as price, ofr_promo_price as promo_price';
				break;
			case 'month' :
				$sql .= 'select month(ofr_date_created) as "month", month(ofr_date_created) as "smonth", year(ofr_date_created) as "year", year(ofr_date_created) as "syear", ofr_landedprice as price, ofr_promo_price as promo_price';
				break;
			case 'year' :
				$sql .= 'select year(ofr_date_created) as "year", year(ofr_date_created) as "syear", ofr_landedprice as price, ofr_promo_price as promo_price';
				break;
			default :
				$sql .= 'select hour(ofr_date_created) as "hour", ofr_landedprice as price, ofr_promo_price as promo_price';
		}
		$sql .= ' from prw_offers
			where ofr_tnt_id=' . $config['tnt_id'] . '
			and ofr_prd_id = ' . $prd_id . '
			and ofr_cpt_id = ' . $cpt_id . '
			and date(ofr_date_created)<\'' . $date1 . '\'
			ORDER BY DATE( ofr_date_created ) DESC
			LIMIT 1)';
	}
	$res = ria_mysql_query($sql);
	if (ria_mysql_errno()) {
		error_log('stats_user_products_period ' . mysql_error() . "\n" . $sql);
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction récupère la première offre disponible juste AVANT la date_entré de la période, pour un produit et pour un concurrent, suivant un cadencier (période + périodicité).
 *    Par défaut, les résultats sont triés par article plus vendu.
 *    @param $prd_id Obligatoire, identifiant d'un produit.
 *    @param $cpt_id Obligatoire, identifiant d'un concurrent.
 *    @param string $date_entre Obligatoire, date de début de la période à prendre en compte (Null pour toute la période).
 *    @param $group Obligatoire, libellé ou tableau des libellés de périodicité pour le regroupement des statistiques. Les valeurs autorisées sont :
 *        - year : par année.
 *        - semester : par semestre.
 *        - trimester : par trimestre.
 *        - month : par mois.
 *        - dayofweek : par jour de la semaine.
 *        - week : par numéro de semaine.
 *        - dayofmonth : par jour du mois.
 *
 *    @return bool False en cas d'échec.
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - price : le prix de l'offre pour une offre.
 *        - [year / month / ...] : périodicités incluses dans $periods (note : pour les semestres, les valeurs sont 1 et 2 ; pour les trimestres, 1, 2, 3 et 4)
 */
function stats_price_watching_offers_past($prd_id, $cpt_id, $date_entre, $group = ''){
	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year'))) return false;
	if (!isdate($date_entre)) return false;
	global $config;
	switch ($group) {
		case 'day' :
			$sql = 'select ofr_date_created as date, day(ofr_date_created) as "day", ofr_landedprice as price, ofr_promo_price as promo_price';
			break;
		case 'week' :
			$sql = 'select week(ofr_date_created) as "week",  ofr_landedprice as price, ofr_promo_price as promo_price';
			break;
		case 'month' :
			$sql = 'select month(ofr_date_created) as "month", month(ofr_date_created) as "smonth", year(ofr_date_created) as "year", year(ofr_date_created) as "syear", ofr_landedprice as price, ofr_promo_price as promo_price';
			break;
		case 'year' :
			$sql = 'select year(ofr_date_created) as "year", year(ofr_date_created) as "syear", ofr_landedprice as price, ofr_promo_price as promo_price';
			break;
		default :
			$sql = 'select hour(ofr_date_created) as "hour", ofr_landedprice as price, ofr_promo_price as promo_price';
	}
	$sql .= ' from prw_offers
			where ofr_tnt_id=' . $config['tnt_id'] . '
			and ofr_prd_id = ' . $prd_id . '
			and ofr_cpt_id = ' . $cpt_id . '
			and date(ofr_date_created)<\'' . $date_entre . '\'
			ORDER BY DATE( ofr_date_created ) DESC
			LIMIT 1';
	$res = ria_mysql_query($sql);
	if (ria_mysql_errno()) {
		error_log('stats_user_products_period ' . mysql_error() . "\n" . $sql);
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction récupère la première offres entrée juste APRES la date_sortie de la période, pour un produit et pour un concurrent, suivant un cadencier (période + périodicité).
 *    Par défaut, les résultats sont triés par article plus vendu.
 *    @param $prd_id Obligatoire, identifiant d'un produit.
 *    @param $cpt_id Obligatoire, identifiant d'un concurrent.
 *    @param string $date_sortie Facultatif, date de fin de la période à prendre en compte (Null pour toute la période).
 *    @param $group Facultatif, libellé ou tableau des libellés de périodicité pour le regroupement des statistiques. Les valeurs autorisées sont :
 *        - year : par année.
 *        - semester : par semestre.
 *        - trimester : par trimestre.
 *        - month : par mois.
 *        - dayofweek : par jour de la semaine.
 *        - week : par numéro de semaine.
 *        - dayofmonth : par jour du mois.
 *
 *    @return bool False en cas d'échec.
 *    @return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *        - price : le prix de l'offre pour une offre.
 *        - [year / month / ...] : périodicités incluses dans $periods (note : pour les semestres, les valeurs sont 1 et 2 ; pour les trimestres, 1, 2, 3 et 4)
 */
function stats_price_watching_offers_lastcheck( $prd_id, $cpt_id, $date_sortie='', $group='' ){
	if ($group != '' && !in_array($group, array('day', 'week', 'month', 'year'))) return false;
	if ($date_sortie != '' && !isdate($date_sortie)) return false;
	global $config;
	switch ($group) {
		case 'day' :
			$sql = 'select pwf_date_lastcheck as date, day(pwf_date_lastcheck) as "day"';
			break;
		case 'week' :
			$sql = 'select week(pwf_date_lastcheck) as "week"';
			break;
		case 'month' :
			$sql = 'select month(pwf_date_lastcheck) as "month", month(pwf_date_lastcheck) as "smonth", year(pwf_date_lastcheck) as "year", year(pwf_date_lastcheck) as "syear"';
			break;
		case 'year' :
			$sql = 'select year(pwf_date_lastcheck) as "year", year(pwf_date_lastcheck) as "syear"';
			break;
		default :
			$sql = 'select hour(pwf_date_lastcheck) as "hour"';
	}
	$sql .= ' from prw_followed_products
			where pwf_tnt_id=' . $config['tnt_id'] . '
			and pwf_prd_id = ' . $prd_id . '
			and pwf_cpt_id = ' . $cpt_id;

	if (trim($date_sortie) != '') {
		$sql .= ' and date(pwf_date_lastcheck)<=\'' . $date_sortie . '\'';
	}

	$sql .= ' ORDER BY DATE( pwf_date_lastcheck ) DESC
			LIMIT 1';
	$res = ria_mysql_query($sql);
	if (ria_mysql_errno()) {
		error_log('stats_user_products_period ' . mysql_error() . "\n" . $sql);
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction récupère le chiffre d'affaire pour un client, une marque, et une période donnée
 *  Les frais de ports sont exclus des calculs !
 *  @param int $usr_id Optionnel, identifiant ou tableau d'identifiants de compte client
 *  @param string $date_start Optionnel, date de début du calcul du CA
 *  @param string $date_end Optionnel date de fin du calcul du CA
 *  @param $brd_id Optionnel, identifiant ou tableau d'identifiants de marque permettant de restreindre le résultat
 *  @param $exclude_prd Optionnel, référence ou tableau de références produits à exclure du résultat
 *
 *  @return bool False en cas d'échec.
 *  @return une requête sql comprenant les colonnes suivantes :
 *      - usr_id : l'identifiant de l'utilisateur
 *      - ca_ht  : le chiffre d'affaire HT
 *      - ca_ttc : le chiffre d'affaire TTC
 */
function stats_get_ca_by_tenant( $usr_id=0, $date_start=false, $date_end=false, $brd_id=0, $exclude_prd=0 ){
	$usr_id = control_array_integer( $usr_id, false );
	if ($usr_id === false) {
		return false;
	}

	if ($date_start !== false) {
		$date_start = dateheureparse($date_start);

		if( !isdateheure($date_start) ){
			return false;
		}
	}

	if ($date_end !== false) {
		$date_end = dateheureparse($date_end);

		if( !isdateheure($date_end) ){
			return false;
		}
	}

	$brd_id = control_array_integer( $brd_id, false );
	if ($brd_id === false) {
		return false;
	}

	global $config;

	$excl_ref = isset($config['dlv_prd_references']) && is_array($config['dlv_prd_references']) ? $config['dlv_prd_references'] : array();
	if (is_array($exclude_prd) && count($exclude_prd)) {
		$excl_ref = array_merge( $excl_ref, $exclude_prd );
	}

	$sql = '';

	switch ($config['tnt_id']) {
		case 39:
			$sql = '
				select
					inv_usr_id as usr_id, sum(prd_price_ht * prd_qte ) as ca_ht, sum(prd_price_ht * prd_tva_rate * prd_qte) as ca_ttc
				from ord_invoices
					join ord_inv_products i on (inv_tnt_id = i.prd_tnt_id and inv_id = i.prd_inv_id)
					left join prd_products p on (i.prd_tnt_id = p.prd_tnt_id and i.prd_id = p.prd_id)
					left join prd_brands on (p.prd_tnt_id = brd_tnt_id and p.prd_brd_id = brd_id)
				where inv_tnt_id = '.$config['tnt_id'].'
			';

			if (is_array($usr_id) && count($usr_id)) {
				$sql .= ' and inv_usr_id in ('.implode(', ', $usr_id).')';
			}

			if ($date_start !== false) {
				$sql .= ' and date(inv_date) >= "'.$date_start.'"';
			}

			if ($date_end !== false) {
				$sql .= ' and date(inv_date) <= "'.$date_end.'"';
			}

			if (is_array($brd_id) && count($brd_id)) {
				$sql .= ' and brd_id in ('.implode(', ', $brd_id).')';
			}

			if (is_array($excl_ref) && count($excl_ref)) {
				$sql .= ' and p.prd_ref not in ("'.implode('", "', $excl_ref).'")';
			}

			$sql .= '
				group by usr_id
			';

			break;
	}

	if( trim($sql) == '' ){
		return false;
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
function set_period($tp, $date){
	$period = false;
	switch ($tp) {
		case 'year' :
			$period = date('Y', strtotime($date['date']));
			break;
		case 'month' :
			$period = date('nY', strtotime($date['date']));
			break;
		case 'week' :
			$period = date('WY', strtotime($date['date']));
			break;
		case 'day' :
			$period = date('j', strtotime($date['date']));
			break;
		case 'hour' :
			$period = $date['hour'];
			break;
	}
	return $period;
}
// \endcond

// \cond onlyria
/** cette fonction permet de récupérer les termes les plus recherché pour une période
 * @param   $date_start Date de début de la recherche
 * @param   $date_end   Optionnel, date de fin de la période de recherche
 * @param   $term 		Optionnel, nom d'un term
 * @param   $wst_id     Optionnel, identifiant du site sur le quel filtrer le résultat
 * @param   $limit      Optionnel, limit de la requête
 * @param   $excluded_user_idss Optionnel tableau avec les identifiants d'utilisateurs
 * @return          	retourne un résultat mysql avec les colonne suivante :
 *                                        - term Le terme recherché
 *                                        - count Le nombre de fois qu'il est ressortie
 * @throws  Si un argument passé en paramètre est faux
 */
function stats_most_searched_terms_get($date_start, $date_end=null, $term='', $wst_id=0, $limit=null, $excluded_user_idss=array() ){

	if( !isdateheure($date_start) ){
		throw new InvalidArgumentException("$date_start doit être une date");
	}

	if( !is_null($date_end) && !isdateheure($date_start) ){
		throw new InvalidArgumentException("$date_end doit être une date");
	}

	if( !is_string($term) ){
		throw new InvalidArgumentException("$term doit être une chaine de caractère");
	}

	if( !is_numeric($wst_id) ){
		throw new InvalidArgumentException("$wst_id doit être un numeric");
	}

	if( !is_null($limit) && !is_numeric($limit) ){
		throw new InvalidArgumentException("$limit doit être un numeric");
	}

	global $config;

	$sql = '
		select sct_name as term,
			count(slg_sct_id) as count
		from search_log
			join search_terms on slg_sct_id=sct_id
		where slg_tnt_id = '.$config['tnt_id'].'
		';
	if( $wst_id > 0 ){
		$sql .= '
			and slg_wst_id = '.$wst_id.'
		';
	}

	$sql .= '
		and date(slg_datetime) >= "'.$date_start.'"
	';

	if( !is_null($date_end) ){
		$sql .= '
			and date(slg_datetime) <= "'.$date_end.'"
		';
	}

	if( is_array($excluded_user_idss) && !empty($excluded_user_idss) ){
		$sql .= ' and slg_usr_id not in ('.implode(', ', $excluded_user_idss).') ';
	}

	if( trim($term) != '' ){
		$sql .= '
			and sct_name = "'.addslashes($term).'"
		';
	}

	$sql .= '
		group by sct_id
		order by count desc
	';

	if( !is_null($limit) ){
		$sql .= '
			limit '.$limit.'
		';
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de retourner le nombre de prospects créés par un représentant pour une période donnée
 * @param int $seller_id Obligatoire, identifiant d'un représentant
 * @param string $date_start Optionnel, date de début (aaaa-mm-jj)
 * @param string $date_end Optionnel, date de fin (aaaa-mm-jj)
 * @param int $wst Optionnel, identifiant d'un site web
 *
 * @return le nombre de prospects créés, false en cas d'erreur
 */
function stats_prospects_created_count( $seller_id, $date_start='', $date_end='', $wst=0 ){

	if( !is_numeric($seller_id) || $seller_id <= 0 ){
		return false;
	}

	if( !is_numeric($wst) ){
		return false;
	}

	global $config;

	$sql = '
		select count(*)
		from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
		and usr_seller_id = '.$seller_id.'
		and usr_date_deleted is null
		and usr_prf_id != '.PRF_SELLER.'
	';

	if( trim($date_start) != '' ){
		$sql .= ' and date(usr_date_created) >= "'.addslashes($date_start).'"';
	}

	if( trim($date_end) != '' ){
		$sql .= ' and date(usr_date_created) <= "'.addslashes($date_end).'"';
	}

	if( $wst ){
		$sql .= ' and usr_wst_id = '.$wst;
	}

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_result($res, 0);
}

// \endcond

// \cond onlyria
/** Cette fonction retourne le nombre de client créés par un représentant pour une période donnée
 * @param int $seller_id Obligatoire, identifiant d'un représentant
 * @param string $date_start Obligatoire, date de début (aaaa-mm-jj)
 * @param string $date_end Obligatoire, date de fin (aaaa-mm-jj)
 * @param int $wst Obligatoire, identifiant d'un site
 *
 * @return le nombre de client créés, false en cas d'erreur
 */
function stats_users_created_count( $seller_id, $date_start, $date_end, $wst ){

	if( !is_numeric($seller_id) || $seller_id <= 0 ){
		return false;
	}

	if( !isdate($date_start) || !isdate($date_end) ){
		return false;
	}

	if( !is_numeric($wst) ){
		return false;
	}

	global $config;


	$sql = '
		select distinct(ord_usr_id) as usr
		from ord_orders, gu_users
		where ord_tnt_id='.$config['tnt_id'].'
			and ord_state_id in ('.implode( ',', ord_states_get_ord_valid() ).')
			and ord_masked=0
			and ord_date >= "'.dateheureparse($date_start).'"
			and ord_date <= "'.dateheureparse($date_end).'"
			and ord_usr_id = usr_id
			and usr_seller_id = '.$seller_id.'
			and usr_wst_id = '.$wst.'
			and (
				select count(*)
				from ord_orders
				where ord_tnt_id='.$config['tnt_id'].'
					and ord_usr_id = gu_users.usr_id
					and ord_state_id in ('.implode( ',', ord_states_get_ord_valid() ).')
					and ord_masked = 0
					and ord_date < "'.dateheureparse($date_start).'") = 0
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de compter le nombre de fiche client mise à jour par un représentant pour une période donnée
 * @param int $seller_id Obligatoire, Identifiant d'un représentant
 * @param string $date_start Obligatoire, Date de début (aaaa-mm-jj)
 * @param string $date_end Obligatoire, Date de fin (aaaa-mm-jj)
 * @param int $wst Optionnel, Identifiant d'un site
 *
 * @return le nombre de fiche client mise à jour, false en cas d'erreur
*/
function stats_users_updated_count( $seller_id, $date_start, $date_end, $wst=0 ){

	if( !is_numeric($seller_id) || $seller_id <= 0 ){
		return false;
	}

	if( !isdate($date_start) || !isdate($date_end) ){
		return false;
	}

	global $config;

	$sql = '
		select count(*)
		from gu_users
		where usr_date_deleted is null
			and	usr_tnt_id = '.$config['tnt_id'].'
			and usr_seller_id = '.$seller_id.'
			and date(usr_date_created) < "'.$date_start.'"
			and date(usr_date_modified) >= "'.$date_start.'"
			and date(usr_date_modified) <= "'.$date_end.'"
	';

	if( $wst ){
		$sql .= ' and usr_wst_id = '.$wst;
	}

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}

	return ria_mysql_result($res, 0);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le nombre de nouveau contact créé par un représentant pour une période donnée
 * @param int $seller_id Obligatoire, Identifiant d'un représentant
 * @param string $date_start Obligatoire, Date de début (aaaa-mm-jj)
 * @param string $date_end Obligatoire, Date de fin (aaaa-mm-jj)
 * @param int $wst Optionnel, Identifiant d'un si
 *
 * @return le nombre de contact créé, false en cas d'erreur
 */
function stats_contact_created_count( $seller_id, $date_start, $date_end, $wst=0 ){

	if( !is_numeric($seller_id) || $seller_id <= 0 ){
		return false;
	}

	if( !isdate($date_start) || !isdate($date_end) ){
		return false;
	}

	global $config;

	$sql = '
		select count(*)
		from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
			and date(usr_date_created) >= "'.$date_start.'"
			and date(usr_date_created) <= "'.$date_end.'"
			and usr_can_login = 0
			and usr_prf_id not in ('.PRF_ADMIN.', '.PRF_SELLER.')
			and usr_seller_id = '.$seller_id.'
	';

	if( $wst ){
		$sql .= ' and usr_wst_id = '.$wst;
	}

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}
	return ria_mysql_result($res, 0);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le nombre de contact mis à jour par un représentant pour une période donnée
 * @param int $seller_id Obligatoire, Identifiant d'un représentant
 * @param string $date_start Obligatoire, Date de début (aaaa-mm-jj)
 * @param string $date_end Obligatoire, Date de fin (aaaa-mm-jj)
 * @param int $wst Optionnel, Identifiant d'un site
 *
 * @return le nombre de contact mis à jour, false en cas d'erreur
 */
function stats_contact_updated_count( $seller_id, $date_start, $date_end, $wst=0 ){

	if( !is_numeric($seller_id) || $seller_id <= 0 ){
		return false;
	}

	if( !isdate($date_start) || !isdate($date_end) ){
		return false;
	}

	global $config;

	$sql = '
		select count(*)
		from gu_users
		where usr_tnt_id = '.$config['tnt_id'].'
			and date(usr_date_created) < "'.$date_start.'"
			and date(usr_date_modified) >= "'.$date_start.'"
			and date(usr_date_modified) <= "'.$date_end.'"
			and usr_date_deleted is null
			and usr_can_login = 0
			and usr_prf_id not in ('.PRF_ADMIN.', '.PRF_SELLER.')
			and usr_seller_id = '.$seller_id.'
	';

	if( $wst ){
		$sql .= ' and usr_wst_id = '.$wst;
	}

	$res = ria_mysql_query($sql);

	if( !$res ){
		return false;
	}
	return ria_mysql_result($res, 0);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de pré-calculer les statistiques de factures.
 * 	Elle permet notamment de lier une facture à toutes les catégories concernés ainsi que tous les représentants.
 * 	@param $inv_id Optionnel, identifiant d'une facture
 * 	@param $inv_usr_id Optionnel, identifiant du client rattaché à la facture
 * 	@return bool true si le pré-calcul s'est correctement déroulé, sinon false dans le cas contraire
 */
function stats_invoices_pre_calculted_exec( $inv_id, $inv_usr_id ){
	global $config;

	if( !is_numeric($inv_id) || $inv_id <= 0 ){
		return false;
	}

	if( !is_numeric($inv_usr_id) || $inv_usr_id <= 0 ){
		return false;
	}

	// Supprime les anciens pré-calcul de stats liés à cette facture
	ria_mysql_query('
		delete from stats_pre_calculated_classify
		where pcc_tnt_id = '.$config['tnt_id'].'
		and pcc_cls_id in ( '.CLS_INVOICE.', '.CLS_INV_PRODUCT.' )
		and pcc_obj_id_0 = '.$inv_id.'
	');

	ria_mysql_query('
		delete from stats_pre_calculated_seller
		where pcs_tnt_id = '.$config['tnt_id'].'
		and pcs_cls_id in ( '.CLS_INVOICE.', '.CLS_INV_PRODUCT.' )
		and pcs_obj_id_0 = '.$inv_id.'
	');

	// Chargement des lignes de factures
	$r_inv_prd = ord_inv_products_get( $inv_id );

	{ // Précalcul pour chaque produit de la facture les catégories liées
		ria_mysql_query('
			insert into stats_pre_calculated_classify
				( pcc_tnt_id, pcc_cls_id, pcc_obj_id_0, pcc_obj_id_1, pcc_obj_id_2, pcc_dst_cls, pcc_dst_obj_0 )
			select '.$config['tnt_id'].', '.CLS_INV_PRODUCT.', prd_inv_id, prd_id, prd_line_id, '.CLS_CATEGORY.', cly_cat_id
			from ord_inv_products
				join prd_classify on (cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = prd_id)
			where prd_tnt_id = '.$config['tnt_id'].' and prd_inv_id = '.$inv_id.'

			union

			select '.$config['tnt_id'].', '.CLS_INV_PRODUCT.', prd_inv_id, prd_id, prd_line_id, '.CLS_CATEGORY.', cat_parent_id
			from ord_inv_products
				join prd_classify on (cly_tnt_id = '.$config['tnt_id'].' and cly_prd_id = prd_id)
				join prd_cat_hierarchy on (cat_tnt_id = '.$config['tnt_id'].' and cat_child_id = cly_cat_id)
			where prd_tnt_id = '.$config['tnt_id'].' and prd_inv_id = '.$inv_id.'
		');
	}

	{ // Précalcul pour chaque produit de la facture les identifiants de représentant
		$results['sellers'] = [];

		// ajout du commercial liée à la factures
		$r_seller = ria_mysql_query('
			select ifnull(inv_seller_id, 0) as seller_id
			from ord_invoices
			where inv_tnt_id = '.$config['tnt_id'].'
				and inv_id = '.$inv_id.'
		');

		$seller_id = 0;
		if( $r_seller && ria_mysql_num_rows($r_seller) ){
			$seller = ria_mysql_fetch_assoc( $r_seller );
			$seller_id = $seller['seller_id'];
		}

		if( isset($seller_id) ){
			$results['sellers'][] = $seller_id;
		}

		// ajout du commercial liée au client
		$usr_seller_id = gu_users_get_seller_id( $inv_usr_id );
		if( is_numeric($usr_seller_id) && $usr_seller_id > 0 ){
			$results['sellers'][] = $usr_seller_id;
		}

		// gestion pour le commercial dans le cas où on utilise les relations suivant le client
		$relation_usr_ids = [];
		$parents = rel_relations_hierarchy_parents_get_ids( REL_SELLER_HIERARCHY, $inv_usr_id );
		if( $parents && count($parents) ){
			$relation_usr_ids = $parents;
		}

		// gestion pour le commercial dans le cas où on utilise les relations suivant le commercial de la facture
		foreach( $results['sellers'] as $slr ){
			$r_seller = gu_users_get( 0, '', '', PRF_SELLER, '', 0, '', false, false, $slr );

			if( $r_seller ){
				while($seller = ria_mysql_fetch_assoc($r_seller) ){
					$parents = rel_relations_hierarchy_parents_get_ids( REL_SELLER_HIERARCHY, $seller['id'] );
					if( $parents && count($parents) ){
						$relation_usr_ids = array_merge( $relation_usr_ids, $parents );
					}
				}
			}
		}

		if( count($relation_usr_ids) ){
			$r_seller = gu_users_get( $relation_usr_ids, '', '', PRF_SELLER );
			if( $r_seller ){
				while($seller = ria_mysql_fetch_assoc($r_seller) ){
					$results['sellers'][] = $seller['seller_id'];
				}
			}
		}

		$results['sellers'] = array_values( array_unique($results['sellers']) );
	}

	if( count($results['sellers']) ){
		foreach( $results['sellers'] as $one_seller ){
			ria_mysql_query('
				insert into stats_pre_calculated_seller
				( pcs_tnt_id, pcs_cls_id, pcs_obj_id_0, pcs_obj_id_1, pcs_obj_id_2, pcs_dst_cls, pcs_dst_obj_0 )
				select '.$config['tnt_id'].', '.CLS_INV_PRODUCT.', prd_inv_id, prd_id, prd_line_id, '.CLS_USER.', '.$one_seller.'
				from ord_inv_products
				where prd_tnt_id = '.$config['tnt_id'].' and prd_inv_id = '.$inv_id.'
			');
		}
	}
}

//\endcond
/// @}