<?php

/**	\file replace_http_urls_from_content.php
 * 	\ingroup https_transition
 *
 */

use Helpers\Https\HttpObject;
set_include_path(dirname(__FILE__) . '/../../include/');

require_once(str_replace('/tools/https_transition', '', getenv('PWD')).'/htdocs/config.inc.php');

require_once 'Helpers/Https/https_transition.php';
require_once 'Helpers/Https/HttpObject.php';

$product = new HttpObject('prd_id', 'prd_products', 'prd_tnt_id', array('prd_desc', 'prd_desc_long'));
$stores = new HttpObject('str_id', 'dlv_stores', 'str_tnt_id', 'str_desc');
$cms = new HttpObject('cat_id', 'cms_categories', 'cat_tnt_id', array('cat_short_desc', 'cat_desc'));
$news = new HttpObject('news_id', 'news', 'news_tnt_id', array('news_desc'));

$objects = array(
	$product,
	$stores,
	$cms,
	$news,
);

foreach ($objects as $obj) {
	replace_http_urls_in_objects_content($obj);
}
