# GetChannelCatalogProductInfoListRequest

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**page_number** | **int** |  | 
**page_size** | **int** |  | 
**criteria** | [**\Swagger\Client\Model\ProductSetVisibilityCriteria**](ProductSetVisibilityCriteria.md) |  | 
**overridden** | **bool** | Search overridden products. If null the filter will not be taken in account. | [optional] 
**product_filters** | [**\Swagger\Client\Model\BeezUPCommonProductFilters**](BeezUPCommonProductFilters.md) |  | [optional] 
**catalog_category_filter** | [**\Swagger\Client\Model\BeezUPCommonCatalogCategoryFilter**](BeezUPCommonCatalogCategoryFilter.md) |  | [optional] 
**channel_category_filter** | [**\Swagger\Client\Model\BeezUPCommonChannelCategoryFilter**](BeezUPCommonChannelCategoryFilter.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


