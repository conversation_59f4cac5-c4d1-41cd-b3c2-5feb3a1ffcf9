/* Popup d'information a la connexion */
$gradient-start-color: #3e58e4;
$gradient-stop-color: #2439b7;

.ria-admin-ui-intro-wrapper {
  display: none;
}
.ria-admin-ui-intro {
  text-align: center;
  color: $dark-color;
  padding: 22px;
  @include media('>=large') {
    padding: 30px;
  }
}
.ria-admin-ui-intro-media {
  border: 0 none;
  width: 150px;
  margin-bottom: 22px;
}
.ria-admin-ui-intro-title {
  font-weight: 700;
  font-size: 20px;
  margin-bottom: 6px;
}
.ria-admin-ui-intro-caption {
  font-size: 13px;
  margin-bottom: 32px;

  &.yuto-alert {
	  margin: 32px 0;
	  font-size: 17px;
  }
}
.ria-admin-ui-intro-button {
  width: 156px;
  height: 42px !important;
  background-image: linear-gradient(to bottom, $gradient-start-color, $gradient-stop-color);
  color: $white !important;
  border: 0 none !important;
  &:hover {
    background-image: linear-gradient(to bottom, darken($gradient-start-color, 5), darken($gradient-stop-color, 5));
  }

  &.yuto-alert {
	  width: 230px;
	  font-size: 1.2em;
  }
}
