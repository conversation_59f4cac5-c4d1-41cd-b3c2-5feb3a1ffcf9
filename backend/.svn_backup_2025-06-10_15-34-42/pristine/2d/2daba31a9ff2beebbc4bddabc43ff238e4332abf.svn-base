<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/metric.proto

namespace Google\Api;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\MetricDescriptor\MetricDescriptorMetadata instead.
     * @deprecated
     */
    class MetricDescriptor_MetricDescriptorMetadata {}
}
class_exists(MetricDescriptor\MetricDescriptorMetadata::class);
@trigger_error('Google\Api\MetricDescriptor_MetricDescriptorMetadata is deprecated and will be removed in the next major release. Use Google\Api\MetricDescriptor\MetricDescriptorMetadata instead', E_USER_DEPRECATED);

