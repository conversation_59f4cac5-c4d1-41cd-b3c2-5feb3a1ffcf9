<?php
/**
 * \defgroup api-profiles-index Profils clients 
 * \ingroup crm
 * @{
 * \page api-profiles-index-get Chargement
 *
 * Cette fonction retourne l'ensemble des profils utilisateurs déclarés sur la plateforme
 *
 *	\code
 *		GET /profiles/
 *	\endcode
 *
 * @param int|array $id Obligatoire, identifiant ou tableau d'identifiants de profils sur lesquelles filtrer le résultat
 *
 * @return json Liste de profils utilisateurs contenant les colonnes :
 *	\code{.json}
 *       {
 *           "id": "Identifiant du profil",
 *           "name": "nom du profil",
 *           "ref": "référence",
 *           "title": "nom du profil",
 *           "publish": "publié ou non (1 ou 0)",
 *           "url": "lien",
 *           "products": numéro du produits,
 *           "img_id": identifiant de l'image,
 *           "is_sync": besoin de synchronisation,
 *           "tag_title": étiquette du titre,
 *           "tag_desc": étiquette de la description,
 *           "keywords": mots-clés,
 *           "url_alias": alias lien url,
 *           "desc": Description,
 *           "pos": position si renseignée,
 *           "date_modified": date de modification
 *       },
 *	\endcode
 * @}
*/
switch( $method ){
	case 'get':

		$ids = 0;
		if( isset($_GET['id']) && is_array($_GET['id']) ){
			foreach( $_GET['id'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants fournis en arguments sont incorrects");
				}
			}
			$ids = $_GET['id'];
		}elseif( isset($_GET['id']) && is_numeric($_GET['id']) ){
			$ids = $_GET['id'];
		}

		$array = array();
		$rbrd = prd_brands_get( $ids );
		while($brd = ria_mysql_fetch_assoc($rbrd)){
			unset( $brd['tenant'] );
			$array[] = $brd;
		}

		$result = true;
		$content = $array;

		break;
}