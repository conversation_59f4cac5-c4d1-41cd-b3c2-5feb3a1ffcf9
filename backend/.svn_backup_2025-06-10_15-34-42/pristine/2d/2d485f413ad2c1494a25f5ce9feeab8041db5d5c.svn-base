<?php
/**
 * GetChannelCatalogProductInfoListRequest
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * GetChannelCatalogProductInfoListRequest Class Doc Comment
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class GetChannelCatalogProductInfoListRequest implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'getChannelCatalogProductInfoListRequest';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'page_number' => 'int',
        'page_size' => 'int',
        'criteria' => '\Swagger\Client\Model\ProductSetVisibilityCriteria',
        'overridden' => 'bool',
        'product_filters' => '\Swagger\Client\Model\BeezUPCommonProductFilters',
        'catalog_category_filter' => '\Swagger\Client\Model\BeezUPCommonCatalogCategoryFilter',
        'channel_category_filter' => '\Swagger\Client\Model\BeezUPCommonChannelCategoryFilter'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'page_number' => 'int32',
        'page_size' => 'int32',
        'criteria' => null,
        'overridden' => null,
        'product_filters' => null,
        'catalog_category_filter' => null,
        'channel_category_filter' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'page_number' => 'pageNumber',
        'page_size' => 'pageSize',
        'criteria' => 'criteria',
        'overridden' => 'overridden',
        'product_filters' => 'productFilters',
        'catalog_category_filter' => 'catalogCategoryFilter',
        'channel_category_filter' => 'channelCategoryFilter'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'page_number' => 'setPageNumber',
        'page_size' => 'setPageSize',
        'criteria' => 'setCriteria',
        'overridden' => 'setOverridden',
        'product_filters' => 'setProductFilters',
        'catalog_category_filter' => 'setCatalogCategoryFilter',
        'channel_category_filter' => 'setChannelCategoryFilter'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'page_number' => 'getPageNumber',
        'page_size' => 'getPageSize',
        'criteria' => 'getCriteria',
        'overridden' => 'getOverridden',
        'product_filters' => 'getProductFilters',
        'catalog_category_filter' => 'getCatalogCategoryFilter',
        'channel_category_filter' => 'getChannelCategoryFilter'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['page_number'] = isset($data['page_number']) ? $data['page_number'] : null;
        $this->container['page_size'] = isset($data['page_size']) ? $data['page_size'] : null;
        $this->container['criteria'] = isset($data['criteria']) ? $data['criteria'] : null;
        $this->container['overridden'] = isset($data['overridden']) ? $data['overridden'] : null;
        $this->container['product_filters'] = isset($data['product_filters']) ? $data['product_filters'] : null;
        $this->container['catalog_category_filter'] = isset($data['catalog_category_filter']) ? $data['catalog_category_filter'] : null;
        $this->container['channel_category_filter'] = isset($data['channel_category_filter']) ? $data['channel_category_filter'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['page_number'] === null) {
            $invalidProperties[] = "'page_number' can't be null";
        }
        if (($this->container['page_number'] < 1)) {
            $invalidProperties[] = "invalid value for 'page_number', must be bigger than or equal to 1.";
        }

        if ($this->container['page_size'] === null) {
            $invalidProperties[] = "'page_size' can't be null";
        }
        if (($this->container['page_size'] < 100)) {
            $invalidProperties[] = "invalid value for 'page_size', must be bigger than or equal to 100.";
        }

        if ($this->container['criteria'] === null) {
            $invalidProperties[] = "'criteria' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['page_number'] === null) {
            return false;
        }
        if ($this->container['page_number'] < 1) {
            return false;
        }
        if ($this->container['page_size'] === null) {
            return false;
        }
        if ($this->container['page_size'] < 100) {
            return false;
        }
        if ($this->container['criteria'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets page_number
     *
     * @return int
     */
    public function getPageNumber()
    {
        return $this->container['page_number'];
    }

    /**
     * Sets page_number
     *
     * @param int $page_number page_number
     *
     * @return $this
     */
    public function setPageNumber($page_number)
    {

        if (($page_number < 1)) {
            throw new \InvalidArgumentException('invalid value for $page_number when calling GetChannelCatalogProductInfoListRequest., must be bigger than or equal to 1.');
        }

        $this->container['page_number'] = $page_number;

        return $this;
    }

    /**
     * Gets page_size
     *
     * @return int
     */
    public function getPageSize()
    {
        return $this->container['page_size'];
    }

    /**
     * Sets page_size
     *
     * @param int $page_size page_size
     *
     * @return $this
     */
    public function setPageSize($page_size)
    {

        if (($page_size < 100)) {
            throw new \InvalidArgumentException('invalid value for $page_size when calling GetChannelCatalogProductInfoListRequest., must be bigger than or equal to 100.');
        }

        $this->container['page_size'] = $page_size;

        return $this;
    }

    /**
     * Gets criteria
     *
     * @return \Swagger\Client\Model\ProductSetVisibilityCriteria
     */
    public function getCriteria()
    {
        return $this->container['criteria'];
    }

    /**
     * Sets criteria
     *
     * @param \Swagger\Client\Model\ProductSetVisibilityCriteria $criteria criteria
     *
     * @return $this
     */
    public function setCriteria($criteria)
    {
        $this->container['criteria'] = $criteria;

        return $this;
    }

    /**
     * Gets overridden
     *
     * @return bool
     */
    public function getOverridden()
    {
        return $this->container['overridden'];
    }

    /**
     * Sets overridden
     *
     * @param bool $overridden Search overridden products. If null the filter will not be taken in account.
     *
     * @return $this
     */
    public function setOverridden($overridden)
    {
        $this->container['overridden'] = $overridden;

        return $this;
    }

    /**
     * Gets product_filters
     *
     * @return \Swagger\Client\Model\BeezUPCommonProductFilters
     */
    public function getProductFilters()
    {
        return $this->container['product_filters'];
    }

    /**
     * Sets product_filters
     *
     * @param \Swagger\Client\Model\BeezUPCommonProductFilters $product_filters product_filters
     *
     * @return $this
     */
    public function setProductFilters($product_filters)
    {
        $this->container['product_filters'] = $product_filters;

        return $this;
    }

    /**
     * Gets catalog_category_filter
     *
     * @return \Swagger\Client\Model\BeezUPCommonCatalogCategoryFilter
     */
    public function getCatalogCategoryFilter()
    {
        return $this->container['catalog_category_filter'];
    }

    /**
     * Sets catalog_category_filter
     *
     * @param \Swagger\Client\Model\BeezUPCommonCatalogCategoryFilter $catalog_category_filter catalog_category_filter
     *
     * @return $this
     */
    public function setCatalogCategoryFilter($catalog_category_filter)
    {
        $this->container['catalog_category_filter'] = $catalog_category_filter;

        return $this;
    }

    /**
     * Gets channel_category_filter
     *
     * @return \Swagger\Client\Model\BeezUPCommonChannelCategoryFilter
     */
    public function getChannelCategoryFilter()
    {
        return $this->container['channel_category_filter'];
    }

    /**
     * Sets channel_category_filter
     *
     * @param \Swagger\Client\Model\BeezUPCommonChannelCategoryFilter $channel_category_filter channel_category_filter
     *
     * @return $this
     */
    public function setChannelCategoryFilter($channel_category_filter)
    {
        $this->container['channel_category_filter'] = $channel_category_filter;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


