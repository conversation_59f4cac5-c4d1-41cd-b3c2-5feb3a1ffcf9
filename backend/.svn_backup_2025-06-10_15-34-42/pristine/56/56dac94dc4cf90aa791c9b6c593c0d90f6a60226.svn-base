<?php

/** \file update-priceandquantity-mirakl.php
 *	\ingroup amazon crontabs
 * 	Ce script est chargé de mettre à jour les tarifs et les stocks disponibles des produits exportés sur la place de marché RDC via mirakl.
 *	Seuls les produits dont l'une de ces deux informations n'est pas à jour seront actualisés.
 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 *
 *	Le script reçoit des arguments :
 *		- int tnt_id : id du tenant (obligatoire)
 *		- string "test"	: activation du mode de verbose/debug (optionnel)
 *
 *	Usage : 
 *		php update-priceandquantity-mirakl.php 16 test
 */

 set_include_path(dirname(__FILE__) . '/../include/');

require_once( 'define.inc.php' );
require_once( 'comparators.inc.php' );
require_once( 'comparators/ctr.mirakl.inc.php' );

unset($config);

if( isset($argv[1]) && (!is_numeric($argv[1]) || $argv[1]<0 || empty($argv[1]) ) ){
	print "Veuillez renseigner un identifiant de tenant valide (numéric supérieur à zéro).\n";
	die();
}

$tnt = isset($argv[1]) && is_numeric($argv[1]) && $argv[1]>0 ? $argv[1] : 0;
$test = isset($argv[2]) && $argv[2] == "test" ? true : false;

// Charge l'ensemble des configurations clients
$configs = cfg_variables_get_all_tenants($tnt);
if( !is_array($configs) || !sizeof($configs) ){
	return false;
}



// Traitement
foreach( $configs as $config ){
	if( !ctr_comparators_actived(CTR_RUEDUCOMMERCE_MIRAKL) ){
		continue;
	}
	$RdC = new RdC_Mirakl( true, $test );

	//	On sette $ignore à true, car on passe par prd_products_get_simple() ( à la place de prd_products_get() )
	$RdC->ctr_mirakl_update_price_and_quantity($ignore = true);

}


