<?php
    /** \file order-product-add-with-colisages.php
     *  Ce fichier permet de gérer l'ajout d'un produit avec du conditionnement. Utiliser pour l'édition de commande.
     */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

    $product_edit_admin = true;

    if ( !isset( $_POST['prd_id'] ) || !is_numeric( $_POST['prd_id'] ) || $_POST['prd_id'] <= 0 ){
        print json_encode(array('code' => '400', 'response' => _('L\'identifiant du produit est invalide ou manquant')));
    } elseif ( !isset( $_POST['ord_id'] ) || !is_numeric( $_POST['ord_id'] ) || $_POST['ord_id'] <= 0 ){
        print json_encode(array('code' => '400', 'response' => _('L\'identifiant de la commande est invalide ou manquant')));
    } elseif ( !isset( $_POST['colisage_ids'] ) || $_POST['colisage_ids'] == "" ){
        print json_encode(array('code' => '400', 'response' => _('Les identifiants de conditionnement sont manquants')));
    } elseif ( !isset( $_POST['colisage_qtes'] ) || $_POST['colisage_qtes'] == "" ){
        print json_encode(array('code' => '400', 'response' => _('Les quantités de conditionnement sont manquants')));
    } elseif (isset($_POST['target']) && (!isset($_POST['target']['id']) || !is_numeric($_POST['target']['id']) || $_POST['target']['id'] < 0 || !isset($_POST['target']['line']) || !is_numeric($_POST['target']['line']) || $_POST['target']['line'] < 0) ){
        print json_encode( array('code' => '400', 'response' => _('Les informations sur la position à laquelle insérer le produit sont invalides ou manquantes') ) );
    } else {
        if (!isset($error)){
            $colisage_ids = explode(',', $_POST['colisage_ids']);
            $colisage_qtes = explode(',', $_POST['colisage_qtes']);
    
            $colisages = array();
            foreach($colisage_ids as $key => $id){
                $col_id = intval( str_replace( "'" , "" , $id ) );
                $col_qte = intval( str_replace( "'" , "" , $colisage_qtes[$key] ) );
                $colisages[$col_id] = $col_qte;
            }
    
            if (sizeof($colisages)){
                foreach($colisages as $col_id => $col_qte) {
                    $line = ord_products_add($_POST['ord_id'], $_POST['prd_id'], $col_qte, '', false, null, $col_id, false, 0, 0, false, false, true, true);
                    if ($line !== false){
                        if (isset($_POST['target'])){
                            ord_products_position_update( $_POST['ord_id'], array('id' => $_POST['prd_id'], 'line' => $line), array('id' => $_POST['target']['id'], 'line' => $_POST['target']['line']), "after" );
                        }
                    } else {
                        $error = _("Une erreur inatendue est survenue lors de l'insertion dans la commande des conditionnements.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
                        break;
                    }
                }
            } else {
                $error = _("Une erreur inatendue est survenue lors de la récupération des conditionnements.")."<br />"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
            }
        }
        if (isset($error)){
            print json_encode(array('code' => '400', 'response' => $error));
        } else {
            print json_encode(array('code' => '100', 'response' => _('L\'ajout du produit et de ses conditionnements s\'est correctement déroulé.')));
        }
    }

