<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AC' => '𑄃𑄳𑄠𑄥𑄴𑄥𑄬𑄚𑄴𑄥𑄧𑄚𑄴 𑄃𑄭𑄣𑄳𑄠𑄚𑄴𑄓𑄴',
  'AD' => '𑄃𑄚𑄴𑄓𑄮𑄢',
  'AE' => '𑄎𑄧𑄙 𑄃𑄢𑄧𑄝𑄴 𑄃𑄟𑄨𑄢𑄖𑄴',
  'AF' => '𑄃𑄛𑄴𑄉𑄚𑄨𑄌𑄴𑄖𑄚𑄴',
  'AG' => '𑄆𑄚𑄴𑄖𑄨𑄉𑄱 𑄃𑄮 𑄝𑄢𑄴𑄟𑄪𑄓',
  'AI' => '𑄄𑄳𑄠𑄋𑄴𑄉𑄪𑄃𑄨𑄣',
  'AL' => '𑄃𑄣𑄴𑄝𑄬𑄚𑄨𑄠',
  'AM' => '𑄃𑄢𑄴𑄟𑄬𑄚𑄨𑄠',
  'AO' => '𑄃𑄳𑄠𑄋𑄴𑄉𑄮𑄣',
  'AQ' => '𑄃𑄳𑄠𑄚𑄴𑄑𑄢𑄴𑄇𑄧𑄑𑄨𑄇',
  'AR' => '𑄃𑄢𑄴𑄎𑄬𑄚𑄴𑄑𑄨𑄚',
  'AS' => '𑄃𑄟𑄬𑄢𑄨𑄇𑄚𑄴 𑄥𑄟𑄮𑄠',
  'AT' => '𑄃𑄧𑄌𑄴𑄑𑄳𑄢𑄨𑄠',
  'AU' => '𑄃𑄌𑄴𑄑𑄳𑄢𑄬𑄣𑄨𑄠',
  'AW' => '𑄃𑄢𑄪𑄝',
  'AX' => '𑄃𑄣𑄚𑄴𑄓𑄧 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'AZ' => '𑄃𑄎𑄢𑄴𑄝𑄭𑄎𑄚𑄴',
  'BA' => '𑄝𑄧𑄥𑄴𑄚𑄨𑄠 𑄃𑄮 𑄦𑄢𑄴𑄎𑄬𑄉𑄮𑄞𑄨𑄚',
  'BB' => '𑄝𑄢𑄴𑄝𑄘𑄮𑄌𑄴',
  'BD' => '𑄝𑄁𑄣𑄘𑄬𑄌𑄴',
  'BE' => '𑄝𑄬𑄣𑄴𑄎𑄨𑄠𑄟𑄴',
  'BF' => '𑄝𑄪𑄢𑄴𑄇𑄨𑄚 𑄜𑄥𑄮',
  'BG' => '𑄝𑄪𑄣𑄴𑄉𑄬𑄢𑄨𑄠',
  'BH' => '𑄝𑄦𑄧𑄢𑄭𑄚𑄴',
  'BI' => '𑄝𑄪𑄢𑄪𑄚𑄴𑄘𑄨',
  'BJ' => '𑄝𑄬𑄚𑄨𑄚𑄴',
  'BL' => '𑄥𑄬𑄚𑄴𑄑𑄴 𑄝𑄢𑄴𑄗𑄬𑄣𑄨𑄟𑄨',
  'BM' => '𑄝𑄢𑄴𑄟𑄪𑄓',
  'BN' => '𑄝𑄳𑄢𑄪𑄚𑄬𑄭',
  'BO' => '𑄝𑄧𑄣𑄨𑄞𑄨𑄠',
  'BQ' => '𑄇𑄳𑄠𑄢𑄨𑄝𑄨𑄠𑄚𑄴 𑄚𑄬𑄘𑄢𑄴𑄣𑄳𑄠𑄚𑄴𑄓𑄧𑄥𑄴',
  'BR' => '𑄝𑄳𑄢𑄎𑄨𑄣𑄴',
  'BS' => '𑄝𑄦𑄟 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'BT' => '𑄞𑄪𑄑𑄚𑄴',
  'BW' => '𑄝𑄧𑄖𑄴𑄥𑄮𑄠𑄚',
  'BY' => '𑄝𑄬𑄣𑄢𑄪𑄌𑄴',
  'BZ' => '𑄝𑄬𑄣𑄨𑄎𑄴',
  'CA' => '𑄇𑄚𑄓',
  'CC' => '𑄇𑄮𑄇𑄮𑄌𑄴 (𑄇𑄨𑄣𑄨𑄁) 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'CD' => '𑄇𑄧𑄋𑄴𑄉𑄮-𑄚𑄨𑄇𑄴𑄥𑄥',
  'CF' => '𑄟𑄧𑄖𑄴𑄙𑄳𑄠𑄧 𑄃𑄜𑄳𑄢𑄨𑄇𑄢𑄴𑄛𑄳𑄢𑄎𑄖𑄧𑄚𑄴𑄖𑄳𑄢𑄧',
  'CG' => '𑄇𑄧𑄋𑄴𑄉𑄮-𑄝𑄳𑄢𑄎𑄞𑄨𑄣𑄴',
  'CH' => '𑄥𑄭𑄪𑄎𑄢𑄴𑄣𑄳𑄠𑄚𑄴𑄓𑄴',
  'CI' => '𑄃𑄭𑄞𑄧𑄢𑄨 𑄇𑄮𑄌𑄴𑄑𑄴',
  'CK' => '𑄇𑄪𑄇𑄪 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'CL' => '𑄌𑄨𑄣𑄨',
  'CM' => '𑄇𑄳𑄠𑄟𑄬𑄢𑄪𑄚𑄴',
  'CN' => '𑄌𑄩𑄚𑄴',
  'CO' => '𑄃𑄣𑄧𑄟𑄴𑄝𑄨𑄠',
  'CR' => '𑄇𑄮𑄥𑄳𑄑𑄢𑄨𑄇',
  'CU' => '𑄇𑄨𑄃𑄪𑄝',
  'CV' => '𑄇𑄬𑄛𑄴𑄞𑄢𑄴𑄘𑄬',
  'CW' => '𑄇𑄨𑄃𑄪𑄢𑄥𑄃𑄮',
  'CX' => '𑄇𑄳𑄢𑄨𑄥𑄴𑄟𑄥𑄴 𑄞𑄨𑄘𑄳𑄠',
  'CY' => '𑄥𑄭𑄛𑄳𑄢𑄥𑄴',
  'CZ' => '𑄌𑄬𑄌𑄨𑄠',
  'DE' => '𑄎𑄢𑄴𑄟𑄚𑄨',
  'DG' => '𑄘𑄨𑄠𑄬𑄉𑄮 𑄉𑄢𑄴𑄥𑄨𑄠',
  'DJ' => '𑄎𑄨𑄝𑄪𑄖𑄨',
  'DK' => '𑄓𑄬𑄚𑄴𑄟𑄢𑄴𑄇𑄧',
  'DM' => '𑄓𑄮𑄟𑄨𑄚𑄨𑄇',
  'DO' => '𑄓𑄮𑄟𑄨𑄚𑄨𑄇𑄚𑄴 𑄛𑄳𑄢𑄧𑄎𑄖𑄧𑄚𑄴𑄖𑄳𑄢𑄧',
  'DZ' => '𑄃𑄢𑄴𑄎𑄬𑄢𑄨𑄠',
  'EA' => '𑄇𑄪𑄃𑄪𑄑 𑄃𑄳𑄃 𑄟𑄬𑄣𑄨𑄣',
  'EC' => '𑄄𑄇𑄪𑄠𑄬𑄓𑄧𑄢𑄴',
  'EE' => '𑄆𑄌𑄴𑄖𑄮𑄚𑄨𑄠',
  'EG' => '𑄟𑄨𑄥𑄧𑄢𑄴',
  'EH' => '𑄛𑄧𑄎𑄨𑄟𑄴 𑄥𑄦𑄢',
  'ER' => '𑄄𑄢𑄨𑄖𑄳𑄢𑄨𑄠',
  'ES' => '𑄥𑄳𑄛𑄬𑄚𑄴',
  'ET' => '𑄃𑄨𑄜𑄨𑄃𑄮𑄛𑄨𑄠',
  'FI' => '𑄜𑄨𑄚𑄴𑄣𑄳𑄠𑄚𑄴𑄓𑄴',
  'FJ' => '𑄜𑄨𑄎𑄨',
  'FK' => '𑄜𑄧𑄇𑄴𑄣𑄳𑄠𑄚𑄴𑄓𑄴 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'FM' => '𑄟𑄭𑄇𑄳𑄢𑄮𑄚𑄬𑄥𑄨𑄠',
  'FO' => '𑄜𑄳𑄠𑄢𑄧𑄃𑄮 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'FR' => '𑄜𑄳𑄢𑄚𑄴𑄥𑄴',
  'GA' => '𑄉𑄳𑄠𑄝𑄧𑄚𑄴',
  'GB' => '𑄎𑄧𑄙𑄢𑄬𑄌𑄴𑄎𑄮',
  'GD' => '𑄉𑄳𑄢𑄬𑄚𑄓',
  'GE' => '𑄎𑄧𑄢𑄴𑄎𑄨𑄠',
  'GF' => '𑄜𑄧𑄢𑄥𑄩 𑄉𑄠𑄚',
  'GG' => '𑄉𑄳𑄢𑄚𑄴𑄏𑄨',
  'GH' => '𑄊𑄚',
  'GI' => '𑄎𑄨𑄝𑄳𑄢𑄣𑄴𑄑𑄢𑄴',
  'GL' => '𑄉𑄳𑄢𑄩𑄚𑄴𑄣𑄳𑄠𑄚𑄴𑄓𑄴',
  'GM' => '𑄉𑄟𑄴𑄝𑄨𑄠',
  'GN' => '𑄉𑄨𑄚𑄨',
  'GP' => '𑄉𑄪𑄠𑄘𑄬𑄣𑄯𑄛𑄴',
  'GQ' => '𑄚𑄨𑄢𑄧𑄇𑄴𑄈𑄩𑄠𑄧 𑄉𑄨𑄚𑄨',
  'GR' => '𑄉𑄳𑄢𑄨𑄌𑄴',
  'GS' => '𑄘𑄧𑄉𑄨𑄚𑄴 𑄎𑄧𑄢𑄴𑄎𑄨𑄠 𑄃𑄮 𑄘𑄧𑄉𑄨𑄚𑄴 𑄥𑄳𑄠𑄚𑄴𑄓𑄃𑄪𑄃𑄨𑄌𑄴 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'GT' => '𑄉𑄪𑄠𑄖𑄬𑄟𑄣',
  'GU' => '𑄉𑄪𑄠𑄟𑄴',
  'GW' => '𑄉𑄨𑄚𑄨-𑄝𑄨𑄥𑄃𑄪',
  'GY' => '𑄉𑄨𑄠𑄚',
  'HK' => '𑄦𑄧𑄁𑄇𑄧𑄁 𑄆𑄌𑄴𑄃𑄬𑄃𑄢𑄴 𑄌𑄩𑄚',
  'HN' => '𑄦𑄪𑄚𑄴𑄓𑄪𑄢𑄥𑄴',
  'HR' => '𑄇𑄳𑄢𑄮𑄠𑄬𑄥𑄨𑄠',
  'HT' => '𑄦𑄭𑄖𑄨',
  'HU' => '𑄦𑄧𑄋𑄴𑄉𑄬𑄢𑄨',
  'IC' => '𑄇𑄳𑄠𑄚𑄢𑄨 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'ID' => '𑄄𑄚𑄴𑄘𑄮𑄚𑄬𑄥𑄨𑄠',
  'IE' => '𑄃𑄠𑄢𑄴𑄣𑄳𑄠𑄚𑄴𑄓𑄴',
  'IL' => '𑄄𑄎𑄴𑄢𑄠𑄬𑄣𑄴',
  'IM' => '𑄃𑄭𑄣𑄴 𑄃𑄧𑄜𑄴 𑄟𑄳𑄠𑄚𑄴',
  'IN' => '𑄞𑄢𑄧𑄖𑄴',
  'IO' => '𑄝𑄳𑄢𑄨𑄑𑄨𑄌𑄴 𑄞𑄢𑄧𑄖𑄴 𑄟𑄧𑄦𑄥𑄉𑄧𑄢𑄨𑄠𑄧 𑄞𑄨𑄘𑄳𑄠',
  'IQ' => '𑄄𑄢𑄇𑄴',
  'IR' => '𑄄𑄢𑄚𑄴',
  'IS' => '𑄃𑄭𑄥𑄴𑄣𑄳𑄠𑄚𑄴𑄓𑄴',
  'IT' => '𑄄𑄖𑄣𑄨',
  'JE' => '𑄎𑄢𑄴𑄥𑄨',
  'JM' => '𑄎𑄟𑄭𑄇',
  'JO' => '𑄎𑄧𑄢𑄴𑄓𑄧𑄚𑄴',
  'JP' => '𑄎𑄛𑄚𑄴',
  'KE' => '𑄇𑄬𑄚𑄨𑄠',
  'KG' => '𑄇𑄨𑄢𑄴𑄉𑄨𑄎𑄨𑄌𑄴𑄖𑄚𑄴',
  'KH' => '𑄇𑄧𑄟𑄴𑄝𑄮𑄓𑄨𑄠',
  'KI' => '𑄇𑄨𑄢𑄨𑄝𑄖𑄨',
  'KM' => '𑄇𑄧𑄟𑄮𑄢𑄮𑄌𑄴',
  'KN' => '𑄥𑄬𑄚𑄴𑄑𑄴 𑄇𑄨𑄑𑄴𑄥𑄴 𑄃𑄮 𑄚𑄬𑄞𑄨𑄌𑄴',
  'KP' => '𑄅𑄪𑄖𑄴𑄖𑄮𑄢𑄴 𑄇𑄮𑄢𑄨𑄠',
  'KR' => '𑄘𑄧𑄉𑄨𑄚𑄴 𑄇𑄮𑄢𑄨𑄠',
  'KW' => '𑄇𑄪𑄠𑄬𑄖𑄴',
  'KY' => '𑄇𑄬𑄟𑄳𑄠𑄚𑄴 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'KZ' => '𑄇𑄎𑄈𑄌𑄴𑄖𑄚𑄴',
  'LA' => '𑄣𑄃𑄮𑄌𑄴',
  'LB' => '𑄣𑄬𑄝𑄚𑄧𑄚𑄴',
  'LC' => '𑄥𑄬𑄚𑄴𑄑𑄴 𑄣𑄪𑄥𑄨𑄠',
  'LI' => '𑄣𑄨𑄌𑄬𑄚𑄴𑄥𑄳𑄑𑄬𑄃𑄨𑄚𑄴',
  'LK' => '𑄥𑄳𑄢𑄨𑄣𑄧𑄁𑄇',
  'LR' => '𑄃𑄭𑄝𑄬𑄢𑄨𑄠',
  'LS' => '𑄣𑄬𑄥𑄮𑄗𑄮',
  'LT' => '𑄣𑄨𑄗𑄪𑄠𑄚𑄨𑄠',
  'LU' => '𑄣𑄪𑄇𑄴𑄥𑄬𑄟𑄴𑄝𑄢𑄴𑄉𑄧',
  'LV' => '𑄣𑄖𑄴𑄞𑄨𑄠',
  'LY' => '𑄣𑄨𑄝𑄨𑄠',
  'MA' => '𑄟𑄮𑄢𑄧𑄇𑄴𑄇𑄮',
  'MC' => '𑄟𑄮𑄚𑄇𑄮',
  'MD' => '𑄟𑄮𑄣𑄴𑄘𑄞𑄨𑄠',
  'ME' => '𑄟𑄧𑄚𑄴𑄑𑄨𑄚𑄨𑄉𑄳𑄢𑄮',
  'MF' => '𑄥𑄬𑄚𑄴𑄑𑄴 𑄟𑄢𑄴𑄑𑄨𑄚𑄴',
  'MG' => '𑄟𑄘𑄉𑄌𑄴𑄇𑄢𑄴',
  'MH' => '𑄟𑄢𑄴𑄥𑄣𑄴 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'MK' => '𑄟𑄳𑄠𑄥𑄓𑄮𑄚𑄨𑄠',
  'ML' => '𑄟𑄣𑄨',
  'MM' => '𑄟𑄠𑄚𑄴𑄟𑄢𑄴 (𑄝𑄢𑄴𑄟)',
  'MN' => '𑄟𑄧𑄋𑄴𑄉𑄮𑄣𑄨𑄠',
  'MO' => '𑄟𑄳𑄠𑄇𑄃𑄮 𑄆𑄌𑄴𑄃𑄬𑄃𑄢𑄴 𑄌𑄩𑄚',
  'MP' => '𑄅𑄪𑄖𑄴𑄖𑄮𑄉𑄎𑄢𑄴 𑄟𑄢𑄨𑄠𑄚 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'MQ' => '𑄟𑄢𑄴𑄑𑄨𑄚𑄨𑄇𑄴',
  'MR' => '𑄟𑄧𑄢𑄨𑄖𑄚𑄨𑄠',
  'MS' => '𑄟𑄧𑄚𑄴𑄑𑄴𑄥𑄬𑄢𑄑𑄴',
  'MT' => '𑄟𑄣𑄴𑄑',
  'MU' => '𑄟𑄧𑄢𑄨𑄥𑄥𑄴',
  'MV' => '𑄟𑄣𑄴𑄘𑄨𑄛𑄴',
  'MW' => '𑄟𑄣𑄃𑄪𑄃𑄨',
  'MX' => '𑄟𑄬𑄇𑄴𑄥𑄨𑄇𑄮',
  'MY' => '𑄟𑄣𑄴𑄠𑄬𑄥𑄨𑄠',
  'MZ' => '𑄟𑄮𑄎𑄟𑄴𑄝𑄨𑄇𑄴',
  'NA' => '𑄚𑄟𑄨𑄝𑄨𑄠',
  'NC' => '𑄚𑄱 𑄇𑄳𑄠𑄣𑄬𑄓𑄮𑄚𑄨𑄠',
  'NE' => '𑄚𑄭𑄎𑄢𑄴',
  'NF' => '𑄚𑄨𑄢𑄴𑄜𑄮𑄇𑄴 𑄞𑄨𑄘𑄳𑄠',
  'NG' => '𑄚𑄭𑄎𑄬𑄢𑄨𑄠',
  'NI' => '𑄚𑄨𑄇𑄢𑄉𑄪𑄠',
  'NL' => '𑄚𑄬𑄘𑄢𑄴𑄣𑄳𑄠𑄚𑄴𑄓𑄴𑄥𑄴',
  'NO' => '𑄚𑄧𑄢𑄴𑄃𑄮𑄠𑄬',
  'NP' => '𑄚𑄬𑄛𑄣𑄴',
  'NR' => '𑄚𑄃𑄪𑄢𑄪',
  'NU' => '𑄚𑄨𑄃𑄪𑄠𑄬',
  'NZ' => '𑄚𑄨𑄃𑄪𑄎𑄨𑄣𑄳𑄠𑄚𑄴𑄓𑄴',
  'OM' => '𑄃𑄮𑄟𑄚𑄴',
  'PA' => '𑄛𑄚𑄟',
  'PE' => '𑄛𑄬𑄢𑄪',
  'PF' => '𑄜𑄧𑄢𑄥𑄩 𑄛𑄧𑄣𑄨𑄚𑄬𑄥𑄨𑄠',
  'PG' => '𑄛𑄛𑄪𑄠 𑄚𑄨𑄃𑄪 𑄉𑄨𑄚𑄨',
  'PH' => '𑄜𑄨𑄣𑄨𑄛𑄭𑄚𑄴',
  'PK' => '𑄛𑄇𑄨𑄌𑄴𑄖𑄚𑄴',
  'PL' => '𑄛𑄮𑄣𑄳𑄠𑄚𑄴𑄓𑄴',
  'PM' => '𑄥𑄬𑄚𑄴𑄑𑄴 𑄛𑄨𑄠𑄬𑄢𑄴 𑄃𑄮 𑄟𑄨𑄢𑄪𑄠𑄬𑄣𑄧𑄚𑄴',
  'PN' => '𑄛𑄨𑄇𑄴𑄇𑄬𑄠𑄢𑄴𑄚𑄴 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'PR' => '𑄛𑄪𑄠𑄬𑄢𑄴𑄖𑄮 𑄢𑄨𑄇𑄮',
  'PS' => '𑄜𑄨𑄣𑄨𑄌𑄴𑄖𑄨𑄚𑄴 𑄎𑄉𑄊𑄚𑄨',
  'PT' => '𑄛𑄧𑄢𑄴𑄖𑄪𑄉𑄣𑄴',
  'PW' => '𑄛𑄣𑄃𑄪',
  'PY' => '𑄛𑄳𑄠𑄢𑄉𑄪𑄠𑄬',
  'QA' => '𑄇𑄖𑄢𑄴',
  'RE' => '𑄢𑄨𑄃𑄨𑄃𑄪𑄚𑄨𑄠𑄧𑄚𑄴',
  'RO' => '𑄢𑄮𑄟𑄚𑄨𑄠',
  'RS' => '𑄥𑄢𑄴𑄝𑄨𑄠',
  'RU' => '𑄢𑄥𑄨𑄠',
  'RW' => '𑄢𑄪𑄠𑄚𑄴𑄓',
  'SA' => '𑄥𑄯𑄘𑄨 𑄃𑄢𑄧𑄝𑄴',
  'SB' => '𑄥𑄧𑄣𑄮𑄟𑄚𑄴 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'SC' => '𑄥𑄨𑄥𑄨𑄣𑄨',
  'SD' => '𑄥𑄪𑄘𑄚𑄴',
  'SE' => '𑄥𑄭𑄪𑄓𑄬𑄚𑄴',
  'SG' => '𑄥𑄨𑄋𑄴𑄉𑄛𑄪𑄢𑄴',
  'SH' => '𑄥𑄬𑄚𑄴𑄑𑄴 𑄦𑄬𑄣𑄬𑄚',
  'SI' => '𑄥𑄳𑄣𑄮𑄞𑄚𑄨𑄠',
  'SJ' => '𑄥𑄣𑄴𑄝𑄢𑄴𑄓𑄴 𑄃𑄮 𑄎𑄚𑄴 𑄟𑄬𑄠𑄬𑄚𑄴',
  'SK' => '𑄥𑄳𑄣𑄮𑄞𑄇𑄨𑄠',
  'SL' => '𑄥𑄨𑄠𑄬𑄢𑄣𑄨𑄃𑄮𑄚𑄴',
  'SM' => '𑄥𑄚𑄴 𑄟𑄢𑄨𑄚𑄮',
  'SN' => '𑄥𑄬𑄚𑄬𑄉𑄣𑄴',
  'SO' => '𑄥𑄮𑄟𑄣𑄨𑄠',
  'SR' => '𑄥𑄪𑄢𑄨𑄚𑄟𑄴',
  'SS' => '𑄘𑄧𑄉𑄨𑄚𑄴 𑄥𑄪𑄘𑄚𑄴',
  'ST' => '𑄥𑄃𑄮𑄑𑄟 𑄃𑄮 𑄛𑄳𑄢𑄨𑄚𑄴𑄥𑄨𑄛𑄨',
  'SV' => '𑄆𑄣𑄴 𑄥𑄣𑄴𑄞𑄬𑄘𑄧𑄢𑄴',
  'SX' => '𑄥𑄨𑄚𑄴𑄑𑄴 𑄟𑄢𑄴𑄑𑄬𑄚𑄴',
  'SY' => '𑄥𑄨𑄢𑄨𑄠',
  'SZ' => '𑄥𑄮𑄠𑄎𑄨𑄣𑄳𑄠𑄚𑄴𑄓𑄴',
  'TA' => '𑄑𑄳𑄢𑄌𑄴𑄑𑄚𑄴 𑄓 𑄇𑄪𑄚𑄴𑄦',
  'TC' => '𑄖𑄪𑄢𑄴𑄇𑄧𑄌𑄴 𑄃𑄮 𑄇𑄭𑄇𑄮𑄌𑄴 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'TD' => '𑄌𑄘𑄴',
  'TF' => '𑄜𑄢𑄥𑄩 𑄘𑄧𑄉𑄨𑄚𑄧 𑄎𑄉',
  'TG' => '𑄑𑄮𑄉𑄮',
  'TH' => '𑄗𑄭𑄣𑄳𑄠𑄚𑄴𑄓𑄴',
  'TJ' => '𑄖𑄎𑄨𑄇𑄴𑄥𑄳𑄗𑄚𑄴',
  'TK' => '𑄑𑄮𑄇𑄬𑄣𑄃𑄪',
  'TL' => '𑄖𑄨𑄟𑄪𑄢𑄴-𑄣𑄬𑄌𑄴𑄖𑄬',
  'TM' => '𑄖𑄪𑄢𑄴𑄇𑄧𑄟𑄬𑄚𑄨𑄌𑄴𑄖𑄚𑄴',
  'TN' => '𑄖𑄨𑄃𑄪𑄚𑄨𑄥𑄨𑄠',
  'TO' => '𑄑𑄮𑄋𑄴𑄉',
  'TR' => '𑄖𑄪𑄢𑄧𑄌𑄴𑄇𑄧',
  'TT' => '𑄖𑄳𑄢𑄨𑄚𑄨𑄚𑄘𑄴 𑄃𑄮 𑄑𑄮𑄝𑄳𑄠𑄉𑄮',
  'TV' => '𑄑𑄪𑄞𑄣𑄪',
  'TW' => '𑄖𑄭𑄤𑄚𑄴',
  'TZ' => '𑄖𑄚𑄴𑄎𑄚𑄨𑄠',
  'UA' => '𑄃𑄨𑄃𑄪𑄇𑄳𑄢𑄬𑄚𑄴',
  'UG' => '𑄅𑄉𑄚𑄴𑄓',
  'UM' => '𑄎𑄧𑄙𑄢𑄬𑄌𑄴𑄎𑄮𑄢𑄴 𑄦𑄭𑄇𑄪𑄢𑄬 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'US' => '𑄟𑄢𑄴𑄇𑄨𑄚𑄴 𑄎𑄧𑄙𑄢𑄬𑄌𑄴𑄎𑄮',
  'UY' => '𑄅𑄪𑄢𑄪𑄉𑄪𑄠𑄬',
  'UZ' => '𑄅𑄪𑄎𑄴𑄝𑄬𑄇𑄨𑄌𑄴𑄖𑄚𑄴',
  'VA' => '𑄞𑄳𑄠𑄑𑄨𑄇𑄚𑄴 𑄥𑄨𑄑𑄨',
  'VC' => '𑄥𑄬𑄚𑄴𑄑𑄴 𑄞𑄨𑄚𑄴𑄥𑄬𑄚𑄴𑄑𑄴 𑄃𑄮 𑄘𑄳𑄠 𑄉𑄳𑄢𑄬𑄚𑄓𑄨𑄚𑄴𑄥𑄴',
  'VE' => '𑄞𑄬𑄚𑄬𑄎𑄪𑄠𑄬𑄣',
  'VG' => '𑄝𑄳𑄢𑄨𑄑𑄨𑄌𑄴 𑄞𑄢𑄴𑄎𑄨𑄚𑄴 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'VI' => '𑄟𑄢𑄴𑄇𑄨𑄚𑄴 𑄎𑄧𑄙𑄢𑄬𑄌𑄴𑄎𑄮𑄢𑄴 𑄞𑄢𑄴𑄎𑄨𑄚𑄴 𑄉𑄭 𑄉𑄭 𑄞𑄨𑄘𑄳𑄠',
  'VN' => '𑄞𑄨𑄠𑄬𑄖𑄴𑄚𑄟𑄴',
  'VU' => '𑄞𑄚𑄪𑄠𑄑𑄪',
  'WF' => '𑄤𑄣𑄨𑄌𑄴 𑄃𑄮 𑄜𑄪𑄑𑄪𑄚',
  'WS' => '𑄥𑄟𑄮𑄠',
  'XK' => '𑄇𑄧𑄥𑄮𑄞𑄮',
  'YE' => '𑄃𑄨𑄠𑄬𑄟𑄬𑄚𑄴',
  'YT' => '𑄟𑄠𑄮𑄖𑄴𑄖𑄬',
  'ZA' => '𑄘𑄧𑄉𑄨𑄚𑄴 𑄃𑄜𑄳𑄢𑄨𑄇',
  'ZM' => '𑄎𑄟𑄴𑄝𑄨𑄠',
  'ZW' => '𑄎𑄨𑄟𑄴𑄝𑄝𑄪𑄠𑄬',
);
