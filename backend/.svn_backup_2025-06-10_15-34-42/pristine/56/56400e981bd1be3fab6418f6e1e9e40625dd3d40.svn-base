<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/servicemanager.proto

namespace Google\Cloud\ServiceManagement\V1\GetServiceConfigRequest;

use UnexpectedValueException;

/**
 * Protobuf type <code>google.api.servicemanagement.v1.GetServiceConfigRequest.ConfigView</code>
 */
class ConfigView
{
    /**
     * Server response includes all fields except SourceInfo.
     *
     * Generated from protobuf enum <code>BASIC = 0;</code>
     */
    const BASIC = 0;
    /**
     * Server response includes all fields including SourceInfo.
     * SourceFiles are of type 'google.api.servicemanagement.v1.ConfigFile'
     * and are only available for configs created using the
     * SubmitConfigSource method.
     *
     * Generated from protobuf enum <code>FULL = 1;</code>
     */
    const FULL = 1;

    private static $valueToName = [
        self::BASIC => 'BASIC',
        self::FULL => 'FULL',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ConfigView::class, \Google\Cloud\ServiceManagement\V1\GetServiceConfigRequest_ConfigView::class);

