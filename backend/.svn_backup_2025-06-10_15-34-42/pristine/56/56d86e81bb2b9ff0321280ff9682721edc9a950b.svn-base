<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\DependencyInjection\Dumper;

/**
 * DumperInterface is the interface implemented by service container dumper classes.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface DumperInterface
{
    /**
     * Dumps the service container.
     *
     * @return string|array The representation of the service container
     */
    public function dump(array $options = []);
}
