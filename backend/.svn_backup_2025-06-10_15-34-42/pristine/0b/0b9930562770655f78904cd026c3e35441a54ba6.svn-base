<?php
    /** \file import-prd.php
	 *
	 * 	Ce script est destiné à importer les articles ajoutés ou modifier d'Harmonia.
	 */

    require_once("imports.inc.php");
    require_once("define.inc.php");


    // Lors de l'éxécution de l'import le fichier est de nouveau téléchargé à partir du ftp les modifications sur celui-ci ne sont pas prise en compte
    // Il faut donc créer un import de type file pour pouvoir sauvegarder l'ajout de l'entête au fichier.
    foreach( $configs as $config ){
        $owner_id = 0;

        // Information de connexion au ftp
        $url = "**************";
        $login = "yuto";
        $password = "Y-F2019_!a!";

        $col_sep = ';';
        $text_separator = '"';

        $error_prd = false;
        $error_stock = false;

        // Import des articles
        {
            $filename = "YUTO-SYNC/X3toYUTO/articles.txt";
            $name = "YUTO-SYNC/X3toYUTO/articles.txt";
            $ext = pathinfo( $filename, PATHINFO_EXTENSION );

            try{
                $file = new ImportFile( $filename, 'ftp', $name );

                $file->connectToFtp($url, $login, $password );
                
                $file->saveFile();

                // Ajoute un entête au fichier
                {
                    $handle = $file->openFile();
                    $file_data = "REFERENCE HML;TITRE;PUBLIE;SOMMEIL;SOUS-TITRE;SUPPORT;EAN13;GENRE;EDITEUR;COLLECTION;PARUTION;DISPONIBILITE;PP TTC;TX TVA;PP HT;INTERVENANTS;STOCK MAS;STOCK CPT;CATALOGUE;LONGUEUR;LARGEUR;EPAISSEUR;POIDS;NB-PAGES;infos\n";
                    $file_data .= $file->getContent();
                    file_put_contents($file->localFilePath, $file_data);

                }

                // Ajoute une colonne pour la catégorie de l'import et créer le fichier d'import de stock
                {
                    $temp = file($file->localFilePath);

                    $first_line = true;
                    foreach($temp as $key => $value){

                        $value = str_replace("\n", "", $value);
                        $value = str_replace("\r", "", $value);
                        
                        $array_value = explode ( ";" , $value );

                        // Ajoute les colonnes catégorie, référence, poids, prix ttc et poids
                        if( $first_line ){
                            $temp[$key] = $value.";categorie;reference;FLD-TVA;FLD-PP-TTC;FLD-POIDS;FLD-INTERVENANTS\r\n";

                        }else{
                            
                            $category = '';
                            
                            if( $array_value[18] === '"F"' ){
                                $category = 235640;
                            }elseif( $array_value[18] === '"G"' ){
                                $category = 235641;
                            }elseif( $array_value[18] === '"N"' ){
                                $category = 235643;
                            }

                            // Modifie la tva pour qu'elle soit en %
                            $array_value[13] = str_replace('"', '', $array_value[13])*100;

                            // Complete le fichier
                            $temp[$key] = implode( ';', $array_value).";".$category.";".$array_value[6].";".$array_value[13].";".$array_value[12].";".$array_value[22].";".$array_value[15]."\r\n";
                        }
                            
                        $first_line = false;
                    }


                    $fp = fopen($file->localFilePath, 'w');
                    foreach($temp as $key => $value){
                        fwrite($fp, $value);
                    }
                    fclose($fp);
                }

                $file->readFile( $col_sep, $text_separator );

                if( !$file->checkColsAndLines() ){
                    error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                    $error_prd = true;
                }
            }
            catch(Exception $e){
                error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                $error_prd = true;
            }


            // Création de l'import des articles
            if( !$error_prd ){
                if( !$imp_id = ipt_imports_add(
                    $ext,
                    $file->original_filename,
                    $file->lines_count,
                    $file->getColumnsCount(),
                    $owner_id,
                    CLS_PRODUCT,
                    'create',
                    'add/upd',
                    $file->localFilePath,
                    $file->getCharset(),
                    $col_sep,
                    $text_separator,
                    $file->getSize(),
                    null,
                    null,
                    null,
                    null,
                    null,
                    0,
                    false,
                    true
                )
                ){
                    unlink( $file->localFilePath );
                    error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import d\'article.');
                    $error_prd = true;
                }
            }

        
            // Création du mapping
            if( !$error_prd ){
                if( !ipt_mapping_add( $imp_id, 0, 'FLD', 'reference-hml', 'reference-hml', null, null, 'fr', '', null, null, 0, 101475 )
                    || !ipt_mapping_add( $imp_id, 1, 'PRD_NAME', 'titre', 'titre', null, null, 'fr' )
                    || !ipt_mapping_add( $imp_id, 2, 'PRD_PUBLISH', 'oublie', 'publie', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}')
                    || !ipt_mapping_add( $imp_id, 3, 'PRD_SLEEP', 'sommeil', 'sommeil', null, null, 'fr', '', null, null, 0, 0, '{"bool":["1"]}')
                    || !ipt_mapping_add( $imp_id, 4, 'FLD', 'sous-titre', 'sous-titre', null, null, 'fr', '', null, null, 0, 101399)
                    || !ipt_mapping_add( $imp_id, 5, 'FLD', 'support', 'support', null, null, 'fr', '', null, null, 0, 101398 )
                    || !ipt_mapping_add( $imp_id, 6, 'PRD_EAN', 'ean13', 'ean13', null, null, 'fr' )
                    || !ipt_mapping_add( $imp_id, 7, 'FLD', 'genre', 'genre', null, null, 'fr', '', null, null, 0, 101400 )
                    || !ipt_mapping_add( $imp_id, 8, 'FLD', 'editeur', 'editeur', null, null, 'fr', '', null, null, 0, 101401 )
                    || !ipt_mapping_add( $imp_id, 9, 'FLD', 'collection', 'collection', null, null, 'fr', '', null, null, 0, 101402 )
                    || !ipt_mapping_add( $imp_id, 10, 'FLD', 'parution', 'parution', null, null, 'fr', '', null, null, 0, 101403 ) 
                    || !ipt_mapping_add( $imp_id, 11, 'FLD', 'disponibilite', 'disponibilite', null, null, 'fr', '', null, null, 0, 101404 )
                    || !ipt_mapping_add( $imp_id, 12, 'PRD_PRICE', 'pp-ttc', 'pp-ttc', null, null, 'fr', '', null, null, 0, 0, '{"gu_catf":"","tarif":"ttc"}' ) 
                    || !ipt_mapping_add( $imp_id, 13, 'PRD_TVA', 'tx-tva', 'tx-tva', null, null, 'fr' )
                    || !ipt_mapping_add( $imp_id, 14, 'PRD_PRICE', 'pp-ht', 'pp-ht', null, null, 'fr', '', null, null, 0, 0, '{"gu_catf":"","tarif":"ht"}' )                 
                    || !ipt_mapping_add( $imp_id, 15, 'PRD_DESC', 'intervenants', 'intervenants', null, null, 'fr' )
                    //|| !ipt_mapping_add( $imp_id, 16, ) stock-mas
                    //|| !ipt_mapping_add( $imp_id, 17, ) stock-cpt
                    || !ipt_mapping_add( $imp_id, 18, 'FLD', 'catalogue', 'catalogue', null, null, 'fr', '', null, null , 0, 101406 )
                    || !ipt_mapping_add( $imp_id, 19, 'PRD_LENGTH', 'longueur', 'longueur', null, null, 'fr', '', null, null, 0, 0, '', 'mm' )
                    || !ipt_mapping_add( $imp_id, 20, 'PRD_WIDTH', 'largeur', 'largeur', null, null, 'fr', '', null, null, 0, 0, '', 'mm' ) 
                    || !ipt_mapping_add( $imp_id, 21, 'PRD_HEIGHT', 'epaisseur', 'epaisseur', null, null, 'fr', '', null, null, 0, 0, '', 'mm' )
                    || !ipt_mapping_add( $imp_id, 22, 'PRD_WEIGHT', 'poids', 'poids', null, null, 'fr', '', null, null, 0, 0, '', 'kg' )
                    || !ipt_mapping_add( $imp_id, 23, 'FLD', 'nb-pages', 'nb-pages', null, null, 'fr', '', null, null, 0, 101412 )
                    || !ipt_mapping_add( $imp_id, 24, 'FLD', 'infos', 'infos', null, null, 'fr', '', null, null , 0, 101483 )
                    || !ipt_mapping_add( $imp_id, 25, 'PRD_CAT', 'categorie', 'categorie', null, null, 'fr', '', 'add', 'id', 0, 0, '', '', 233875, false )
                    || !ipt_mapping_add( $imp_id, 26, 'PRD_REF', 'reference', 'reference', 'ref', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                    || !ipt_mapping_add( $imp_id, 27, 'FLD', 'fld-tva', 'fld-tva', null, null, 'fr', '', null, null , 0, 101481 )
                    || !ipt_mapping_add( $imp_id, 28, 'FLD', 'fld-pp-ttc', 'fld-pp-ttc', null, null, 'fr', '', null, null , 0, 101482 )
                    || !ipt_mapping_add( $imp_id, 29, 'FLD', 'fld-poids', 'fld-poids', null, null, 'fr', '', null, null , 0, 101480 )
                    || !ipt_mapping_add( $imp_id, 30, 'FLD', 'fld-intervenants', 'fld-intervenants', null, null, 'fr', '', null, null , 0, 101405 )

                ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping pour l\'import d\'article.');
                    $error_prd = true;
                }
            }

            
            if( !$error_prd ){
                if( !ipt_imports_exec( $imp_id ) ){
                    ipt_imports_set_state( $imp_id, 'pending' );
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des articles.');
                    $error_prd = true;
                }
            }
        }

        // Attend que l'import d'article soit terminé avant de commencer l'import des stocks
        if( !$error_prd ){
            while( ipt_imports_get_state($imp_id) == 'processing' ){
                sleep(60);
            }
        }

        // import des stocks
        {
            $filename = "YUTO-SYNC/X3toYUTO/stocks.txt";
            $name = "YUTO-SYNC/X3toYUTO/stocks.txt";
            $ext = pathinfo( $filename, PATHINFO_EXTENSION );

            try{
                $file = new ImportFile( $filename, 'ftp', $name );

                $file->connectToFtp($url, $login, $password );
                
                $file->saveFile();

              
                // Ajoute un entête au fichier
                {
                    $handle = $file->openFile();
                    $file_data = "prd-ref;qte;depot-ref\n";
                    $file_data .= $file->getContent();
                    file_put_contents($file->localFilePath, $file_data);

                    $file->closeFile();
                }


                $file->readFile( $col_sep, $text_separator );

                if( !$file->checkColsAndLines() ){
                    error_log(__FILE__.':'.__LINE__.' Votre fichier ne contient aucune lignes ou aucune colonnes.');
                    $error_stock = true;
                }
                
            }
            catch(Exception $e){
                error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
                $error_stock = true;
            }


            // Création de l'import des stocks
            if( !$error_stock ){
                if( !$imp_id = ipt_imports_add(
                    $ext,
                    $file->original_filename,
                    $file->lines_count,
                    $file->getColumnsCount(),
                    $owner_id,
                    CLS_STOCK,
                    'create',
                    'add/upd',
                    $file->localFilePath,
                    $file->getCharset(),
                    $col_sep,
                    $text_separator,
                    $file->getSize(),
                    null,
                    null,
                    null,
                    null,
                    null,
                    0,
                    false,
                    true
                )
                ){
                    unlink( $file->localFilePath );
                    error_log(__FILE__.':'.__LINE__.' Une erreur s\'est produite lors de la création de l\'import des stocks.');
                    $error_stock = true;
                }
            }

            // Création du mapping
            if( !$error_stock ){
                if( !ipt_mapping_add( $imp_id, 0, 'STK_PRD_ID', 'prd-ref', 'prd-ref', 'ref', null, 'fr')
                    || !ipt_mapping_add( $imp_id, 1, 'STK_QTE', 'qte', 'qte', null, null, 'fr' )
                    || !ipt_mapping_add( $imp_id, 2, 'STK_DPS_ID', 'depot-ref', 'depot-ref', 'id', null, 'fr', '', null, null, 0, 0, '', '', 0, 1 )
                
                ){
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de la création du mapping de l\'import des stocks.');
                    $error_stock = true;
                }
            }
            
            if( !$error_stock ){
                if( !ipt_imports_exec( $imp_id ) ){
                    ipt_imports_set_state( $imp_id, 'pending' );
                    error_log(__FILE__.':'.__LINE__.' Une erreur est survenue lors de l\'exécution de l\'import des stocks.');
                    $error_stock = true;
                }
            }
        }
    }