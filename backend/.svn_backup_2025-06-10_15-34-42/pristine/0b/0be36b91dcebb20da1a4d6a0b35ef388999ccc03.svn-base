<?php
	require_once('orders.inc.php');
	require_once('define.inc.php');
	// YesByCash
	/**	\defgroup yesbycash Yes By Cash
	 *	\ingroup payment_external
	 *	Ce module permet les paiement avec YesByCash
	 *	Variables de config obligatoire :
	 *		- yesbycash_merchant_id : identifiant boutique
	 *		- yesbycash_key : clé d'identification - @version : valeur différente en production
	 *		- yesbycash_key_id : identifiant clé boutique - @version : valeur différente en production
	 *
	 * 		- yesbycash_url_nok : Redirection après erreur
	 * 		- yesbycash_url_ok : Redirection après validation
	 * 		- yesbycash_url_actions : URL de validation après paiement
	 * 		- yesbycash_url_s2s : URL action server
	 *	@{
	 */

	/**	\brief Cette classe implémente le paiement avec YesByCash
	 *
	 *	Yes By Cash (https://www.yesbycash.com/) permet de payer en espèces sur un site marchand.
	 */
	class YesByCash {

		/// Contexte d'exécution : Développement
		const CONTEXT_DEV	=	'DEV';
		/// Contexte d'exécution : Production
		const CONTEXT_PROD	=	'PROD';

		const YESBYCASH_STATUS_WAIT			= 1;
		const YESBYCASH_STATUS_OK			= 2;
		const YESBYCASH_STATUS_WAIT_PAY  	= 3;
		const YESBYCASH_STATUS_REFUSED 		= 4;
		const YESBYCASH_STATUS_EXPIRED 		= 5;
		const YESBYCASH_STATUS_CANCELLED 	= 6;
		const YESBYCASH_STATUS_WAIT_AUTH 	= 7;
		const YESBYCASH_STATUS_PAY 			= 8;

		//Tableau des données pour couchDb
		private $data_couchDB = array(
			'date' => '',
			'ord_id' => 0,
			'ord_total_ht' => 0,
			'ord_total_ttc' => 0,
			'user_id' => 0,
			'user_firstname' => '',
			'user_lastname' => '',
			'user_email' => '',
			'data' => '',
		);

		private $soap_client, $server_url = false;
		private $key, $keyID, $merchantID = '';

		/**	Constructeur
		 */
		public function __construct(){
			global $config;

			if( $this->getContext() == YesByCash::CONTEXT_DEV ){
				$this->server_url = 'https://yesbycash.test.tsipayment.net/';
			}else{
				$this->server_url = 'https://yesbycash.tsipayment.net/';
			}

			$this->key 		  = $config['yesbycash_key'];
			$this->keyID 	  = $config['yesbycash_key_id'];
			$this->merchantID = $config['yesbycash_merchant_id'];

			$soap_context = stream_context_create(
				array(
					'ssl' => array(
						'ciphers' => 'DHE-RSA-AES256-SHA:DHE-DSS-AES256-SHA:AES256-SHA:KRB5-DES-CBC3-MD5:KRB5-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:EDH-DSS-DES-CBC3-SHA:DES-CBC3-SHA:DES-CBC3-MD5:DHE-RSA-AES128-SHA:DHE-DSS-AES128-SHA:AES128-SHA:RC2-CBC-MD5:KRB5-RC4-MD5:KRB5-RC4-SHA:RC4-SHA:RC4-MD5:RC4-MD5:KRB5-DES-CBC-MD5:KRB5-DES-CBC-SHA:EDH-RSA-DES-CBC-SHA:EDH-DSS-DES-CBC-SHA:DES-CBC-SHA:DES-CBC-MD5:EXP-KRB5-RC2-CBC-MD5:EXP-KRB5-DES-CBC-MD5:EXP-KRB5-RC2-CBC-SHA:EXP-KRB5-DES-CBC-SHA:EXP-EDH-RSA-DES-CBC-SHA:EXP-EDH-DSS-DES-CBC-SHA:EXP-DES-CBC-SHA:EXP-RC2-CBC-MD5:EXP-RC2-CBC-MD5:EXP-KRB5-RC4-MD5:EXP-KRB5-RC4-SHA:EXP-RC4-MD5:EXP-RC4-MD5',
					)
				)
			);

			$soap_options = array(
				'trace' => 0,
				'soap_version' => SOAP_1_1,
				'stream_context' => $soap_context,
				'location' => $this->server_url
			);

			$this->soap_client = new SoapClient( $this->server_url.'?wsdl', $soap_options );
		}

		/**
		 *	Cette fonction permet de récupérer les partenaires YesByCash
		 *	@param string $zipcode Obligatoire, code postal (5 chiffres)
		 *	@param string $country Optionnel, pays dans lequel rechercher (par défault France)
		 *	@return array Retourne un tableau contenant les points où un paiement YesByCash peut être réalisé
		 */
		public function getOutletsList( $zipcode, $country='France' ){
			$params = array(
				'zipcode' => $zipcode,
				'country' => $this->getCountryCode( $country )
			);

			$ts = time();
			$transactionid = $this->createUID();
			$signature = $this->createHash($ts, $transactionid);

			$params['transactionid'] = $transactionid;
			$params['timestamp'] = $ts;
			$params['signature'] = $signature;

			$result = self::sendRequest( 'getOutletsList', $params );

			$ar_outlets = array();

			if( $result->errorcode == '00000' ){
				foreach( $result->outlets as $outlet ){
					$ar_outlets[] = array(
						'id' 			=> (int) $outlet->outletsid,
						'country_id' 	=> (int) $outlet->outletscountry,
						'name' 			=> (string) $outlet->outletsname,
						'sign' 			=>  (string) $outlet->outletssign,
						'address' 		=> (string) $outlet->outletsaddress,
						'zipcode' 		=> (string) $outlet->outletszipcode,
						'city' 			=> (string) $outlet->outletscity,
						'phone' 		=> (string) $outlet->outletsphone,
						'latitude' 		=> (string) $outlet->outletslatitude,
						'longitude' 	=> (string) $outlet->outletslongitude
					);
				}
			}

			return $ar_outlets;
		}

		/**
		 *	Cette fonction permet de payer via YesByCash.
		 *	Elle réalise une redirection en fonction du résultat de paiement
		 *	@param $ord_id Obligatoire, identifiant de la commande à régler
		 *	@return void
		 */
		public function _doPayment( $ord_id ){
			global $config;

			$result 	= $this->createOrder( $ord_id );
			$error_code = $result->errorcode;

			$error = false;

			switch( $error_code ){
				case '00000': {
					// Validation du paiement OK
					ord_orders_pay_type_set( $ord_id, _PAY_YESBYCASH );
					ord_orders_state_update( $ord_id, _STATE_WAIT_PAY );
					fld_object_values_set( $ord_id, _FLD_ORD_BARCODE, $result->barcode );

					header('Location: '.$this->createURL( $config['yesbycash_url_ok'], '' ));
					exit;
				}
				case '00001': {
					// Validation du paiement OK - En attente d'authentification sur My Emoney Purse
					ord_orders_pay_type_set( $ord_id, _PAY_YESBYCASH );
					ord_orders_state_update( $ord_id, _STATE_WAIT_VALIDATION );
					fld_object_values_set( $ord_id, _FLD_ORD_BARCODE, $result->barcode );
					fld_object_values_set( $ord_id, _FLD_ORD_URl_YESBYCASH, $result->redirectionurl );

					header('Location: '.$result->redirectionurl);
					exit;
				}
				default: {
					// Validation du paiement NOK
					error_log( __FILE__.':'.__LINE__.' Erreur lors du paiement de la commande n°'.$ord_id.' via YesByCash' );
					header('Location: '.$config['yesbycash_url_nok']);
					exit;
				}
			}
		}

		/**
		 *	Cette fonction permet de récupérer les commandes actuellements chez YesByCash
		 *	@param string $barcode Obligatoire, code barre de la commande à récupérer
		 *	@return le résultat d'un appel au WebService YesByCash getOrder
		 */
		public function getOrder( $barcode ){
			if( trim($barcode)=='' ){
				return false;
			}

			$params = array(
				'barcode' => $barcode
			);

			$ts = time();
			$transactionid = $this->createUID();
			$signature = $this->createHash($ts, $transactionid);

			$params['transactionid'] = $transactionid;
			$params['timestamp'] = $ts;
			$params['signature'] = $signature;

			return self::sendRequest( 'getOrder', $params );
		}

		/**
		 *	Cette fonction permet de mettre à jour les informations sur une commande déjà chez YesByCash
		 *	@return Le résultat d'un appel au WebService YesByCash updateOrder
		 */
		public function updateOrder(){
			$params = array(

			);

			$ts = time();
			$transactionid = $this->createUID();
			$signature = $this->createHash($ts, $transactionid);

			$params['transactionid'] = $transactionid;
			$params['timestamp'] = $ts;
			$params['signature'] = $signature;

			return self::sendRequest( 'updateOrder', $params );
		}

		/**
		 *	Cette fonction permet de contrôler le statut d'une commande et de faire les actions en conséquence.
		 *	@param int $ord_id Obligatoire, identifiant d'une commande (statut obligatoire : en attente de paiement via YesByCash)
		 *	@param bool $crontab Facultatif, si les contrôles d'accès IP sont actifs (valeur par défaut) ou bien s'ils doivent être désactivés (indiquer true)
		 *	@return bool True si aucun problème n'est survenue
		 */
		public function controlStatutOrder( $ord_id, $crontab=false ){
			if( !is_numeric($ord_id) || $ord_id<=0 ){
				error_log( __FILE__.':'.__LINE__.' La commande n°'.$ord_id.' n\'existe pas' );
				return false;
			}

			if( !$crontab ){
				if( !$this->isAllowIP('confirmOrderPayment') ){
					return false;
				}
			}

			$rorder = ord_orders_get_with_adresses( 0, $ord_id, 0, '', false, false, false, false, null, false, false, false, false, _PAY_YESBYCASH );
			if( !$rorder || !ria_mysql_num_rows($rorder) ){
				error_log( __FILE__.':'.__LINE__.' La commande n°'.$ord_id.' n\'existe pas ou bien n\'a pas été payée via YesByCash' );
				return false;
			}

			$order = ria_mysql_fetch_assoc( $rorder );

			// Récupère le code barre YesByCash de la commande
			$barcode = fld_object_values_get( $order['id'], _FLD_ORD_BARCODE, '', false, true );
			if( trim($barcode)=='' ){
				error_log( __FILE__.':'.__LINE__.' Code barre YesByCash de la commande n°'.$order['id'].' inconnue.' );
				return false;
			}

			// Récupère les informations de la commande sur YesByCash
			$info_yesbycash = $this->getOrder( $barcode );

			if( !isset($info_yesbycash->errorcode) || $info_yesbycash->errorcode!='00000' ){
				error_log( __FILE__.':'.__LINE__.' Erreur lors de la récupération des informations YesByCash sur la commande n°'.$order['id'] );
				return false;
			}

			global $config;

			$status_yesbycash = (int) $info_yesbycash->orderstatus;
			switch( $status_yesbycash ){
				case YesByCash::YESBYCASH_STATUS_WAIT_AUTH: { // Commande en attente d'authentification, RIA : en attente de validation
					ord_orders_state_update( $order['id'], _STATE_WAIT_VALIDATION );
					break;
				}
				case YesByCash::YESBYCASH_STATUS_WAIT: {
					ord_orders_state_update( $order['id'], _STATE_WAIT_PAY );
					break;
				}
				case YesByCash::YESBYCASH_STATUS_OK:
				case YesByCash::YESBYCASH_STATUS_PAY: { // Commande payée
					if( $order['state_id']==_STATE_WAIT_VALIDATION ){
						ord_orders_state_update( $order['id'], _STATE_WAIT_PAY );
					}

					ord_orders_state_update( $order['id'], _STATE_PAY_CONFIRM );

					$rcfg = cfg_emails_get( 'yesbycash-pay', $config['wst_id'] );
					if( $rcfg && ria_mysql_num_rows($rcfg) ){
						$cfg = ria_mysql_fetch_assoc( $rcfg );

						if( trim($cfg['to']) != '' ){
							require_once('email.inc.php');

							$email = new Email();
							$email->setFrom( 'RiaStudio Suivi Commande YesByCash <<EMAIL>>' );
							$email->addTo( $cfg['to'] );
							$email->addBcc( $cfg['bcc'] );
							$email->setReplyTo( $cfg['reply-to'] );
							$email->setSubject( 'Commande YesByCash payée' );
							$email->addHtml( '<table width="480" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="http://www.riastudio.fr/images/template/header/banner.png"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );
							$email->addParagraph('Bonjour,');
							$email->addParagraph('La commande n°'.$order['id'].' vient d\'être réglée chez un partenaire YesByCash, elle a donc été validée dans RiaShop.');
							$email->addHtml( '</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(0, 47, 59);"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb(255, 255, 255);" href="mailto:<EMAIL>"><EMAIL></a></div></div>' );

							if( !$email->send() ){
								error_log( __FILE__.':'.__LINE__.' Erreur envoi mail de réglement YesByCash pour la commande n°'.$order['id'] );
							}
						}
					}

					break;
				}
				case YesByCash::YESBYCASH_STATUS_EXPIRED:
				case YesByCash::YESBYCASH_STATUS_CANCELLED: { // Commande annulée
					fld_object_values_set( $order['id'], _FLD_ORD_URl_YESBYCASH, '' );
					ord_orders_state_update( $order['id'], _STATE_CANCEL_USER );

					if( isset($config['yesbycash_email_cancel']) && trim($config['yesbycash_email_cancel'])!='' ){
						require_once('email.inc.php');

						$email = new Email();
						$email->setFrom( 'RiaShop Suivi Commande YesByCash <<EMAIL>>' );
						$email->addTo( $config['yesbycash_email_cancel'] );
						$email->addBcc( '<EMAIL>' );
						$email->setSubject( 'Commande YesByCash annulée' );
						$email->addHtml( '<table width="480" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="http://www.riastudio.fr/images/template/header/banner.png"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );
						$email->addParagraph('Bonjour,');
						$email->addParagraph('La commande n°'.$order['id'].' ayant expirée chez YesByCash, elle a donc été annulée dans RiaShop.');
						$email->addHtml( '</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(0, 47, 59);"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb(255, 255, 255);" href="mailto:<EMAIL>"><EMAIL></a></div></div>' );

						if( !$email->send() ){
							error_log( __FILE__.':'.__LINE__.' Erreur envoi mail d\'annulation YesByCash pour la commande n°'.$order['id'] );
						}
					}

					break;
				}
			}

			return true;
		}

		/**
		 *	Cette fonction permet de vérifier qu'une commande existe bien chez YesByCash (via son code barre).
		 *	@param string $barcode Obligatoire, code barre
		 *	@return bool True si la commande existe, False dans le cas contraire
		 */
		public function existsOrder( $barcode ){
			if( trim($barcode)=='' ){
				return false;
			}

			$result = $this->getOrder( $barcode );

			if( !isset($result->errorcode) ){
				return false;
			}

			return ($result->errorcode == '00000' );
		}

		/**
		 *	Cette fonction permet d'envoyer la relance pour le paiement
		 *	@param int $order Obligatoire, identifiant de la commande ou directement le résultat de ria_mysql_fetch_assoc( ord_orders_get_with_adresses() )
		 *	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
		 */
		public static function sendPaymentNotify( $order ){
			if( is_array($order) ){
				if( !ria_array_key_exists(array('id', 'user'), $order) ){
					return false;
				}
			}else{
				$rorder = ord_orders_get_with_adresses( 0, $order );
				if( !$rorder || !ria_mysql_num_rows($rorder) ){
					return false;
				}

				$order = ria_mysql_fetch_assoc( $rorder );
			}

			global $config;

			$rcfg = cfg_emails_get( 'yesbycash-pay', $config['wst_id'] );
			if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
				return false;
			}

			$cfg = ria_mysql_fetch_assoc( $rcfg );

			// Récupère les informations sur le compte client
			$ruser = gu_users_get( $order['user'] );
			if( !$ruser || !ria_mysql_num_rows($ruser) ){
				return false;
			}

			$user = ria_mysql_fetch_assoc( $ruser );

			// Civilité du visiteur
			$title = '';
			if($user['title_id']) {
				$titles = gu_titles_get($user['title_id']);
				if($titles && ria_mysql_num_rows($titles)) {
					$title = ria_mysql_fetch_array($titles);
					$title = ' '.$title['name'];
				}
			}

			$email = new Email();
			$email->setFrom( $cfg['from'] );
			$email->addTo( $user['email'] );
			$email->addBcc( $cfg['bcc'] );
			$email->setReplyTo( $cfg['reply-to'] );

			$compl_url = '?utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=yesbycash_pay';

			$can_send = true;
			switch( $config['tnt_id'] ){
				case 16: {
					$email->setSubject('Animal & Co : En attente de règlement YesByCash.');

					$email->addHtml( $config['email_html_header'] );
					$email->addParagraph('Bonjour '.trim( $title.' '.$user['adr_firstname'].' '.$user['adr_lastname'] ).',');
					$email->addParagraph('Vous avez récemment choisi de régler votre commande n°'.$order['id'].' avec YesByCash. Nous sommes toutefois en attente du règlement d\'un montant de '.number_format( $order['total_ttc'], 2, ',', ' ' ).' € pour pouvoir commencer son traitement.');
					$email->addParagraph('Vous avez la possibilité de régler votre commande dans n\'importe quel point de paiement YesByCash accessible sur <a href="https://www.myemoneypurse.com/outlets">https://www.myemoneypurse.com/outlets</a>. Pour cela munissez-vous du document que vous avez reçu  par e-mail lors de la confirmation de votre commande.');
					$email->addParagraph('Bien entendu, toute l\'équipe d\'Animal & Co se tient à votre entière disposition pour toute question.');
					$email->addParagraph('À très bientôt sur <a href="http://www.animaleco.com/'.$compl_url.'">www.animaleco.com</a> !');
					$email->addParagraph('Cordialement,<br />L\'équipe Animal & Co.');
					$email->addHtml( $config['email_html_footer'] );
					break;
				}
				case 43: {
					$email->setSubject('Purebike : En attente de règlement YesByCash.');

					$email->addHtml( $config['email_html_header'] );
					$email->addParagraph('Bonjour '.trim( $title.' '.$user['adr_firstname'].' '.$user['adr_lastname'] ).',');
					$email->addParagraph('Vous avez récemment choisi de régler votre commande n°'.$order['id'].' avec YesByCash. Nous sommes toutefois en attente du règlement d\'un montant de '.number_format( $order['total_ttc'], 2, ',', ' ' ).' € pour pouvoir commencer son traitement.');
					$email->addParagraph('Vous avez la possibilité de régler votre commande dans n\'importe quel point de paiement YesByCash accessible sur <a href="https://www.myemoneypurse.com/outlets">https://www.myemoneypurse.com/outlets</a>. Pour cela munissez-vous du document que vous avez reçu  par e-mail lors de la confirmation de votre commande.');
					$email->addParagraph('Bien entendu, toute l\'équipe de Purebike se tient à votre entière disposition pour toute question.');
					$email->addParagraph('À très bientôt sur <a href="'.$config['site_url'].$compl_url.'">'.$config['site_url'].'</a> !');
					$email->addParagraph('Cordialement,<br />L\'équipe Purebike.');
					$email->addHtml( $config['email_html_footer'] );
					break;
				}
				default: {
					$can_send = false;
					error_log("Aucune configuration de renseignée \"YesByCash::sendPaymentNotify\" : ".$config['tnt_id']);
					break;
				}
			}

			if( $can_send ){
				return $email->send();
			}

			return true;
		}

		/** Cette fonction permet d'enregistrer dans CouchDb un accès banque lors d'un paiement
		 * Un log sera lancé en cas d'erreur d'insertion dans couchDb
		 */
		protected function savePaymentInCouchDB(){
			$this->data_couchDB['date'] = time();

			$response = CouchDB::create(_COUCHDB_HIST_PAY_DB_NAME)->add(CLS_PAYMENT_ACCESS_HISTO, $this->data_couchDB);
			if (!is_array($response) || !array_key_exists('ok', $response)) {
				error_log('Erreur de l\'insertion dans CouchDb des données suivantes : '.print_r($this->data_couchDB, true) );
			}
		}

		/** Cette fonction permet d'enregistrer dans CouchDb le retour banque lors d'un paiement
		 * Un log sera lancé en cas d'erreur d'insertion dans couchDb
		 */
		protected function saveReturnPaymentInCouchDB(){
			$this->data_couchDB['date'] = time();

			$response = CouchDB::create(_COUCHDB_HIST_PAY_DB_NAME)->add(CLS_PAYMENT_RETURN_HISTO, $this->data_couchDB);
			if (!is_array($response) || !array_key_exists('ok', $response)) {
				error_log('Erreur de l\'insertion dans CouchDb des données suivantes : '.print_r($this->data_couchDB, true) );
			}
		}

		/**
		 *	Cette fonction permet d'envoyer les différentes requêtes via SOAP à YesByCash
		 *	@param $method Obligatoire, le type de requête envoyée à YesByCash
		 *	@param $params Obligatoire, paramètre de requête
		 *
		 *	@return Le résultat retourné par le WebService
		 */
		private function sendRequest( $method, $params ){
			global $config;

			$params['merchantid'] = $this->merchantID;
			$params['keyid'] = $this->keyID;

			if ($method == "createOrder"){
				// Enregistre l'accès à la banque dans CouchDB
				$user = ria_mysql_fetch_assoc( gu_users_get($params['customerid']) );
				$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $params['reference'])));
				$this->data_couchDB['user_id'] = $user['id'];
				$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
				$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
				$this->data_couchDB['user_email'] = $user['email'];

				$this->data_couchDB['ord_id'] = $order['id'];
				$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
				$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];
				$this->data_couchDB['data'] = $params;
				$this->savePaymentInCouchDB();
			}

			$result = null;
			$error = 0;

			try {
				$result = $this->soap_client->__soapCall( $method, array($params) );
			}catch( SoapFault $fault ){
				$error = 1;

				if( !$result ){
					$result = new stdClass();
					$result->errorcode = $fault->faultcode;
					$result->errorMessage = $fault->faultstring;
				}
			}

			if ($method == "createOrder"){
				$this->data_couchDB['data'] = $result;
				$this->data_couchDB['code_id'] = $result->errorcode;
				$this->data_couchDB['code_name'] = isset($result->message) ? $result->message : $result->errorMessage;
				$this->saveReturnPaymentInCouchDB();
			} else {
				mail('<EMAIL>', 'YesByCash - sendRequest ['.$config['tnt_id'].' - '.$method.']', print_r($params, true)."\n\n\n".print_r( $result, true) );
			}

			return $result;
		}

		/**
		 *	Cette fonction permet de créer une nouvelle commande chez YesByCash
		 *	@param int $ord_id Obligatoire, identifiant de la commande RiaShop à créer chez YesByCash
		 *	@return le résultat d'un appel au WebService createOrder de YesByCash
		 */
		private function createOrder( $ord_id ){
			if( !ord_orders_exists($ord_id) ){
				return false;
			}

			global $config;

			$order = ria_mysql_fetch_assoc( ord_orders_get_with_adresses(0, $ord_id) );

			$fname = $lname = $phone = $mobile = $address = $city = $country = $zipcode = '';

			if( is_numeric($order['dlv_id']) && $order['dlv_id']>0 ){
				$fname = $order['dlv_firstname'];
				$lname = $order['dlv_lastname'];

				if( trim($lname)=='' && trim($order['dlv_society'])!='' ){
					$lname = $order['dlv_society'];
				}

				$address = $order['dlv_address1'];
				if( trim($order['dlv_address2'])!='' ){
					$address .= ' '.$order['dlv_address2'];
				}

				$city 	 = $order['dlv_city'];
				$zipcode = $order['dlv_postal_code'];
				$country = $this->getCountryCode( $order['dlv_country'] );
			}else{
				$fname = $order['inv_firstname'];
				$lname = $order['inv_lastname'];

				if( trim($lname)=='' && trim($order['inv_society'])!='' ){
					$lname = $order['inv_society'];
				}

				$address = $order['inv_address1'];
				if( trim($order['inv_address2'])!='' ){
					$address .= ' '.$order['inv_address2'];
				}

				$city 	 = $order['inv_city'];
				$zipcode = $order['inv_postal_code'];
				$country = $this->getCountryCode( $order['inv_country'] );
			}

			$phone = trim($order['dlv_phone'])!='' ? $order['dlv_phone'] : $order['inv_phone'];
			$mobile = trim($order['dlv_mobile'])!='' ? $order['dlv_mobile'] : $order['inv_mobile'];

			if( trim($mobile)=='' ){
				$mobile = $phone;
			}

			$buyerbirthdate = '';
			$usr_birthdate = gu_users_get_date_of_birth($order['user']);
			if (isdateheure($usr_birthdate)) {
				$buyerbirthdate = date('Y-m-d', strtotime($usr_birthdate));
			}

			$params = array(
				'customerid'		=> $order['user'],
				'amount'			=> ( $order['total_ttc'] * 100 ),
				'currency'			=> 'EUR',
				'reference'			=> $order['id'],
				'buyerip'			=> $this->getAddrIP(),
				'buyeremail'		=> gu_users_get_email( $order['user'] ),
				'buyerfname'		=> $fname,
				'buyerlname'		=> $lname,
				'buyerphone'		=> $phone,
				'buyermobile'		=> $mobile,
				'buyeraddress'		=> $address,
				'buyercity'			=> $city,
				'buyercountry'		=> $country,
				'buyerzip'			=> $zipcode,
				'urlok'				=> $this->createURL( $config['yesbycash_url_ok'], '' ),
				'urlnok'			=> $this->createURL( $config['yesbycash_url_nok'], '' ),
				'urls2s'			=> $this->createURL( $config['yesbycash_url_actions'], 'ordid='.$order['id'] ),
				'urlpaymentupdate'	=> $this->createURL( $config['yesbycash_url_actions'], 'ordid='.$order['id'] ),

				'buyerbirthdate' 	=> $buyerbirthdate,
				'buyerbirthcity'	=> '',
				'buyerjob'			=> '',

				'selectedoutlet' 	=> 0,
				'orderTtl'			=> null
			);

			$ts = time();
			$transactionid = $this->createUID();
			$signature = $this->createHash($ts, $transactionid);

			$params['transactionid'] = $transactionid;
			$params['timestamp'] = $ts;
			$params['signature'] = $signature;

			return self::sendRequest( 'createOrder', $params );
		}

		/**	Crée un identifiant unique arbitraire qui sera utilisé pour identifier la transaction
		 *	@return l'UID généré
		 */
		private function createUID(){
			$t 	 = time();
			$uid = md5($t);

			return $uid;
		}

		/**	Crée la signature du marchand permettant de crypter les échanges
		 *	@param $timestamp Obligatoire, timestamp utilisé pour créer la signature
		 *	@param $transactionId Obligatoire, identifiant de la transaction créé à l'aide la méthode createUID
		 *	@return la signature permettant d'authentifier les échanges
		 */
		private function createHash( $timestamp, $transactionId ){
			$str = $this->merchantID.'#'.$transactionId.'#'.$timestamp;
			$signature = hash_hmac('sha1', $str, $this->key);

			return $signature;
		}

		/**	Retourne l'adresse IP du client
		 *	@return l'IP du client. Cette IP est utilisée pour des contrôles d'accès (ou pour déterminer si nous sommes en développement)
		 */
		private function getAddrIP(){
			if( !empty($_SERVER['REMOTE_ADDR']) ){
				$ip = $_SERVER['REMOTE_ADDR'];
			}elseif( !empty($_SERVER['HTTP_CLIENT_IP']) ){
				$ip = $_SERVER['HTTP_CLIENT_IP'];
			}else{
				$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
			}

			if( $this->getContext() == YesByCash::CONTEXT_DEV ){
				$ip = '**************';
			}

			return $ip;
		}

		/**	Facilite la création d'une URL contenant des paramètres
		 *	@param $url Obligatoire, l'url de base64_decode
		 *	@param $params Obligatoire, les paramètres à ajouter à l'url
		 *	@return string L'URL complète à utiliser
		 */
		private function createURL( $url, $params ){
			global $config;

			if( trim($params)!='' ){
				$url = $url.( strstr($url, '?') ? '&' : '?' ).$params;
			}

			return $config['site_url'].$url;
		}

		/** Retourne le code ISO 3166 du pays à partir de son nom
		 *	@param string $country Obligatoire, nom du pays
		 * 	@return le code ISO 3166 alpha 3 du pays
		 */
		private function getCountryCode( $country ){
			$country = strtolower2( $country );

			$one_code = '';
			foreach( $this->_COUNTRY_CODE as $key=>$val ){
				if( strtolower2($key) == $country ){
					$one_code = $val;
				}
			}

			if (trim($one_code) == '') {
				if ($country == 'france metropolitaine (hors-corse)') {
					$one_code = 'FRA';
				}elseif ($country == 'france metropolitaine') {
					$one_code = 'FRA';
				}
			}

			if( trim($one_code)=='' ){
				error_log( __FILE__.':'.__LINE__.' Aucun code ISO3166 alpha 3 trouvé pour '.$country );
				$one_code = 'FRA';
			}

			return $one_code;
		}

		/**
		 *	Cette fonction permet de vérifier que l'accès à une page est bien réalisé depuis l'adresse IP d'un serveur YesByCash.
		 *	@param $func_name Obligatoire, nom de la fonction appellant isAllowIP
		 *	@return bool True si l'IP est autorisée, False dans le cas contraire
		 */
		private function isAllowIP( $func_name ){
			$addr_IP = $this->getAddrIP();

			if( !in_array($addr_IP, $this->_SER_ALLOW) ){
				error_log( __FILE__.':'.__LINE__.' Accès IP '.$addr_IP.' - refusé ('.$func_name.')' );
				return false;
			}else{
				return true;
			}
		}

		/**
		 *	Cette fonction permet de déterminer le contexte (MAQUETTE ou PRODUCTION)
		 *	@return Le contexte
		 */
		private function getContext(){
			$devs = array( '.maquettes.riastudio.fr', '.forge.riastudio.fr' );
			foreach( $devs as $t ){
				if( strpos($_SERVER['HTTP_HOST'], $t ) !== false ){
					return YesByCash::CONTEXT_DEV;
				}
			}

			return YesByCash::CONTEXT_PROD;
		}

		///< Adresses IP des serveurs autorisés
		private $_SER_ALLOW = array(
			'**************', // Serveur test Beijing
			'*************',
			'*************',
			'**************',
			'**************',
			'***********',
			'**************',
			'**************',
			'**************',
			'**************'
		);

		///< Tableau de correspondance Pays / Code ISO 3166
		private $_COUNTRY_CODE = array(
			'Aruba' => 'ABW',
			'Anguilla' => 'AIA',
			'Antilles Néerlandaises' => 'ANT',
			'Arménie' => 'ARM',
			'Terres australes françaises' => 'ATF',
			'Autriche' => 'AUT',
			'Belgique' => 'BEL',
			'Bangladesh' => 'BGD',
			'Bahamas' => 'BHS',
			'Belize' => 'BLZ',
			'Brésil' => 'BRA',
			'Bhoutan' => 'BTN',
			'Centrafricaine, République' => 'CAF',
			'Suisse' => 'CHE',
			'Côte d\'Ivoire' => 'CIV',
			'Cook, îles' => 'COK',
			'Cap-Vert' => 'CPV',
			'Christmas, îles' => 'CXR',
			'Tchèque, république' => 'CZE',
			'Dominique' => 'DMA',
			'Algérie' => 'DZA',
			'Erythrée' => 'ERI',
			'Estonie' => 'EST',
			'Fidji' => 'FJI',
			'Féroé, îles' => 'FRO',
			'Royaume-uni' => 'GBR',
			'Gibraltar' => 'GIB',
			'Gambie' => 'GMB',
			'Grèce' => 'GRC',
			'Guatemala' => 'GTM',
			'Guyana' => 'GUY',
			'Honduras' => 'HND',
			'Hongrie' => 'HUN',
			'Océan indien, Territoire britannique de l\'' => 'IOT',
			'Iraq' => 'IRQ',
			'Italie' => 'ITA',
			'Japon' => 'JPN',
			'Kirghizistan' => 'KGZ',
			'Saint Kitts and Nevis' => 'KNA',
			'Lao, République démocratique populaire' => 'LAO',
			'Libyenne, Jamahiriya arabe' => 'LBY',
			'Sri Lanka' => 'LKA',
			'Luxembourg' => 'LUX',
			'Maroc' => 'MAR',
			'Madagascar' => 'MDG',
			'Marshall, îles' => 'MHL',
			'Malte' => 'MLT',
			'Mariannes du nord, îles' => 'MNP',
			'Montserrat' => 'MSR',
			'Malawi' => 'MWI',
			'Namibie' => 'NAM',
			'Norfolk, île' => 'NFK',
			'Niué' => 'NIU',
			'Népal' => 'NPL',
			'Oman' => 'OMN',
			'Pitcairn' => 'PCN',
			'Palaos' => 'PLW',
			'Porto Rico' => 'PRI',
			'Paraguay' => 'PRY',
			'Réunion' => 'REU',
			'Rwanda' => 'RWA',
			'Sénégal' => 'SEN',
			'Sainte-Hélène' => 'SHN',
			'Sierra leone' => 'SLE',
			'Somalie' => 'SOM',
			'Suriname' => 'SUR',
			'Suède' => 'SWE',
			'Syrienne, république arabe' => 'SYR',
			'Togo' => 'TGO',
			'Tokelau' => 'TKL',
			'Tonga' => 'TON',
			'Turquie' => 'TUR',
			'Tanzanie, République-unie de' => 'TZA',
			'Iles mineures éloignées des Etats-Unis' => 'UMI',
			'Ouzbékistan' => 'UZB',
			'Vénézuéla' => 'VEN',
			'Viet Nam' => 'VNM',
			'Samoa' => 'WSM',
			'Afrique du Sud' => 'ZAF',
			'Zimbabwe' => 'ZWE',
			'Afghanistan' => 'AFG',
			'Albanie' => 'ALB',
			'Emirats arabes unis' => 'ARE',
			'Samoa américaines' => 'ASM',
			'Antigua et Barbuda' => 'ATG',
			'Azerbaïdjan' => 'AZE',
			'Bénin' => 'BEN',
			'Bulgarie' => 'BGR',
			'Bosnie Herzégovine' => 'BIH',
			'Bermudes' => 'BMU',
			'Barbade' => 'BRB',
			'Bouvet, île' => 'BVT',
			'Canada' => 'CAN',
			'Chili' => 'CHL',
			'Cameroun' => 'CMR',
			'Colombie' => 'COL',
			'Costa rica' => 'CRI',
			'Caïmanes, îles' => 'CYM',
			'Allemagne' => 'DEU',
			'Danemark' => 'DNK',
			'Equateur' => 'ECU',
			'Sahara occidental' => 'ESH',
			'Ethiopie' => 'ETH',
			'Falkland, îles (Malvinas)' => 'FLK',
			'Micronésie, Etats fédérés de' => 'FSM',
			'Géorgie' => 'GEO',
			'Guinée' => 'GIN',
			'Guinée-Bissau' => 'GNB',
			'Grenade' => 'GRD',
			'Guyane française' => 'GUF',
			'Hong-kong' => 'HKG',
			'Croatie (nom local: Hrvatska)' => 'HRV',
			'Indonésie' => 'IDN',
			'Irlande' => 'IRL',
			'Islande' => 'ISL',
			'Jamaïque' => 'JAM',
			'Kazakhstan' => 'KAZ',
			'Cambodge' => 'KHM',
			'Corée, République de' => 'KOR',
			'Liban' => 'LBN',
			'Saint Lucia' => 'LCA',
			'Lesotho' => 'LSO',
			'Lettonie' => 'LVA',
			'Monaco' => 'MCO',
			'Maldives' => 'MDV',
			'Macédoine, l\'ex-république yougoslave de' => 'MKD',
			'Myanmar' => 'MMR',
			'Mozambique' => 'MOZ',
			'Martinique' => 'MTQ',
			'Malaisie' => 'MYS',
			'Nouvelle-Calédonie' => 'NCL',
			'Nigéria' => 'NGA',
			'Pays-bas' => 'NLD',
			'Nauru' => 'NRU',
			'Pakistan' => 'PAK',
			'Pérou' => 'PER',
			'Papouasie-Nouvelle-Guinée' => 'PNG',
			'Corée, République populaire démocratique de' => 'PRK',
			'Polynésie française' => 'PYF',
			'Roumanie' => 'ROM',
			'Arabie saoudite' => 'SAU',
			'Singapour' => 'SGP',
			'Svalbard et île Jan Mayen' => 'SJM',
			'El Salvador' => 'SLV',
			'Saint-Pierre-et-Miquelon' => 'SPM',
			'Slovaquie' => 'SVK',
			'Swaziland' => 'SWZ',
			'Turks et Caïques, îles' => 'TCA',
			'Thaïlande' => 'THA',
			'Turkménistan' => 'TKM',
			'Trinité-et-Tobago' => 'TTO',
			'Tuvalu' => 'TUV',
			'Ouganda' => 'UGA',
			'Uruguay' => 'URY',
			'Saint-Siège (Etat de la cité du Vatican)' => 'VAT',
			'Iles Vierges britanniques' => 'VGB',
			'Vanuatu' => 'VUT',
			'Yémen' => 'YEM',
			'Zaïre' => 'ZAR',
			'Angola' => 'AGO',
			'Andorre' => 'AND',
			'Argentine' => 'ARG',
			'Antarctique' => 'ATA',
			'Australie' => 'AUS',
			'Burundi' => 'BDI',
			'Burkina faso' => 'BFA',
			'Bahreïn' => 'BHR',
			'Bélarus' => 'BLR',
			'Bolivie' => 'BOL',
			'Brunéi Darussalam' => 'BRN',
			'Botswana' => 'BWA',
			'Cocos (Keeling), îles' => 'CCK',
			'Chine' => 'CHN',
			'Congo' => 'COG',
			'Comores' => 'COM',
			'Cuba' => 'CUB',
			'Chypre' => 'CYP',
			'Djibouti' => 'DJI',
			'Dominicaine, république' => 'DOM',
			'Egypte' => 'EGY',
			'Espagne' => 'ESP',
			'Finlande' => 'FIN',
			'France' => 'FRA',
			'Gabon' => 'GAB',
			'Ghana' => 'GHA',
			'Guadeloupe' => 'GLP',
			'Guinée équatoriale' => 'GNQ',
			'Groenland' => 'GRL',
			'Guam' => 'GUM',
			'Heard, île et McDonald, îles' => 'HMD',
			'Haïti' => 'HTI',
			'Inde' => 'IND',
			'Iran, république islamique d\'' => 'IRN',
			'Israël' => 'ISR',
			'Jordanie' => 'JOR',
			'Kenya' => 'KEN',
			'Kiribati' => 'KIR',
			'Koweït' => 'KWT',
			'Libéria' => 'LBR',
			'Liechtenstein' => 'LIE',
			'Lituanie' => 'LTU',
			'Macao' => 'MAC',
			'Moldova, République de' => 'MDA',
			'Mexique' => 'MEX',
			'Mali' => 'MLI',
			'Mongolie' => 'MNG',
			'Mauritanie' => 'MRT',
			'Maurice' => 'MUS',
			'Mayotte' => 'MYT',
			'Niger' => 'NER',
			'Nicaragua' => 'NIC',
			'Norvège' => 'NOR',
			'Nouvelle-Zélande' => 'NZL',
			'Panama' => 'PAN',
			'Philippines' => 'PHL',
			'Pologne' => 'POL',
			'Portugal' => 'PRT',
			'Qatar' => 'QAT',
			'Russie, Fédération de' => 'RUS',
			'Soudan' => 'SDN',
			'Géorgie du sud et les îles Sandwich du sud' => 'SGS',
			'Salomon, îles' => 'SLB',
			'Saint-Marin' => 'SMR',
			'Sao Tomé-et-Principe' => 'STP',
			'Slovénie' => 'SVN',
			'Seychelles' => 'SYC',
			'Tchad' => 'TCD',
			'Tadjikistan' => 'TJK',
			'Timor-Leste' => 'TMP',
			'Tunisie' => 'TUN',
			'Taïwan, Province de Chine' => 'TWN',
			'Ukraine' => 'UKR',
			'Etats-Unis' => 'USA',
			'Saint Vincent et les Grenadines' => 'VCT',
			'Iles Vierges des Etats-Unis' => 'VIR',
			'Wallis et Futuna' => 'WLF',
			'Yougoslavie' => 'YUG',
			'Zambia' => 'ZMB'
		);
	}

	 /// @}
