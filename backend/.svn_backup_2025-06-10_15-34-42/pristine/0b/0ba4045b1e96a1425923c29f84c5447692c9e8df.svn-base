var currentAjaxRequest = false;
var wst = prf = rwd = false;
var oldSystem = false;
$(document).ready(function(){
	// charge les actions sur les onglets
	actionOnTabstrip( $('#tab').val() );

	$('input[name=is-highlighting]').click(function(){
    	if($(this).is(':checked'))
   		{
   			if($(this).val() == 0){
				$('.div-pts-highlighting').hide();
   			}else{
   				$('.div-pts-highlighting').show();
   			}
    	}
	});

	$('#system_ratio').change(function(){
		if($(this).val() == 1){
			$(".ratio_value").html("Ratio ");
		}else{
			$(".ratio_value").html("Pallier ");
		}

	});

	// mise en place des sélecteur (site et profil)
	$('.riapicker').click(function(){
		if( $('.selector', this).css('display')=='none' ){
			$('.selector').hide();
			$('.selector', this).show();
		} else {
			$('.selector', this).hide();
		}
	});
	
	oldSystem = $('select#system option:selected').val();
	loadConversionSystem();
	loadFilleulSystem();
	$('select#system').change(
		function(){
			return loadConversionSystem();
		}
	);
	
	$('.selector a').click(function(){
		$('div.error').remove();
		var type = $(this).attr('name').substring(0, 1);
		var info = $(this).attr('name').substring(2);
		
		// mise à jour des informations
		$(this).parents('.riapicker').find('.view').html( $(this).html() );
		$('#' + ( type!='p' ? 'wst_id' : 'prf_id' )).val( info );
		
		// action après sélection
		loadRulesProducts(); loadInfoActions(); loadInfoSponsors(); loadConfigRewards();
		$(this).parent().hide();
		
		// affichage de l'onglet général
		$('.tb_rewards').addClass('none');
		$('#tb-tabConfig').removeClass('none');
		$('.tabstrip .selected').removeClass('selected')
		$('#tabConfig').addClass('selected');
		
		return false;
	});
}).delegate('input[name=save-config]', 'click', function(){
	if( oldSystem==false ){
		return true;
	}
	
	var newSystem = $('select#system option:selected').val();
	if( oldSystem!=newSystem && newSystem==2 ){
		return window.confirm(rewardsConfirmModifSystemeConversion);
	}
	
	return true;
}).on('change', '#sp-system', function(){
	loadInfoSponsors( false, $('#sp-system option:selected').val() );
});

function loadConversionSystem(){
	var type = $('select#system option:selected').val();

	$('.tr-sys-pts, .tr-sys-code, .tr-sys-product').hide();
	
	if (type == 1) {
		$('.tr-sys-pts').show();
	}else if (type == 2) {
		$('.tr-sys-code').show();
		$('.tr-sys-code .help.help-code').attr('title', rewardsTittreGenereCodePromo);
	}else if (type == 3) {
		$('.tr-sys-product').show();
		loadRewardProducts();
	}else if (type == 4) {
		$('.tr-sys-remise').show();
		$('.div-apply-on').hide();
		$('.tr-sys-code .help.help-code').attr('title', rewardsTitreRemisePanier);
	}
}

function loadFilleulSystem(){
	var type = $('select#filleul-system option:selected').val();

	if(type == 1){
		$(".tr-filleul-reduc").show();
		$(".tr-filleul-points").hide();
		$(".tr-filleul-day").show();
	}else if(type == 2){
		$(".tr-filleul-points").show();
		$(".tr-filleul-reduc").hide();
		$(".tr-filleul-day").show();
	}else{
		$(".tr-filleul-points").hide();
		$(".tr-filleul-reduc").hide();
		$(".tr-filleul-day").hide();
	}
}


function message( msg, error, input ){
	$('.success, .error').remove();
	
	if( msg==undefined )
		return false;
	
	input = input!=undefined ? input : 'form-rewards';
	
	$('#' + input).prepend( '<div class="' + ( error ? 'error' : 'success' ) + '">' + msg + '</div>' );
	$(window).scrollTop(0);
	return false;
}

/**
 *	Cette fonction gère la sélection d'un onglet.
 */
function actionOnTabstrip( action ){
	if( action ){
		$('#msg-sponsor').hide();
		$("#reward-products").val('');
		switch( action ){
			case 'general' :
				loadConfigRewards();
				actionsForReward();
				loadRewardProducts();
				break;
			case 'products' :
				actionsForRules();
				loadRulesProducts();
				break;
			case 'actions' :
				actionsForActions();
				loadInfoActions();
				break;
			case 'sponsors' :
				$('#msg-sponsor').show();
				actionsForSponsors();
				loadInfoSponsors( true );
				break;
		}
	}
	
	$('.tabstrip li').click(function(){
		$('#msg-sponsor').hide();
		$('.success, .error').remove();
		var id = $('input',this).attr('name');

		if (id == 'showTabProduct') {
			id = 'tabProducts';
		}
		
		$('.tb_rewards').addClass('none');
		$('#tb-' + id).removeClass('none');
		
		$('.tabstrip .selected').removeClass('selected')
		$('input', this).addClass('selected');
		switch( id ){
			case 'general' :
			case 'tabConfig' : 
				loadConfigRewards();
				actionsForReward();
				loadRewardProducts();
				break;
			case 'products' :
			case 'tabProducts' : 
				actionsForRules();
				loadRulesProducts();
				break;
			case 'actions' :
			case 'tabActions' :
				actionsForActions();
				loadInfoActions();
				break;
			case 'sponsors' :
			case 'tabSponsors' :
				$('#msg-sponsor').show();
				actionsForSponsors();
				loadInfoSponsors( true );
				break;
		}
		return false;
	});
}

/** 
 *	Cette fonction active toutes les actions sur l'onglet "Parrainage".
 */
function actionsForSponsors(){
	$('#save-sponsor').click(function(){
		saveSponsors();
	});
	
	$('#active-sponsor-y').click(function(){
		$('#discount, #points, #remise-day-valid, #sponsor-points').removeAttr('disabled');
		loadInfoSponsors( false );
	});
	
	$('#active-sponsor-n').click(function(){
		$('#discount, #points, #remise-day-valid, #sponsor-points').val('').attr('disabled','disabled');
	});
}

/**
 *	Cette fonction permet de sauvegarder les informations sur le système de parrainage.
 */
function saveSponsors(){
	message();
	rwd = parseInt( $('#rwd_id').val() );
	
	$.getJSON('/admin/ajax/rewards/ajax-rewards.php?savesp=1&rwd=' + rwd + '&' + $('#form-rewards').serialize(), function( result ){
		
		if( result.done ){
			message( rewardsInfosMaj, false );
			loadInfoSponsors(false);
		} else {
			message( rewardsErreurParrainage, true );
		}
		
	});
}

/**
 *	Cette fonction permet de charger les informations de points sur les actions.
 */
function loadInfoSponsors( checked, spSystem ){
	$('tr.sp-type-pourcent, tr.sp-type-pts').hide();
	rwd = parseInt( $('#rwd_id').val() );
	
	$.getJSON('/admin/ajax/rewards/ajax-rewards.php?getinfosp=1&rwd=' + rwd, function( sp ){
		if( typeof spSystem == 'undefined' || !spSystem.length ){
			spSystem = sp.sp_system;
		}

		if( sp.discount!=undefined && sp.discount.length && sp.discount > 0){
			$('#discount').val( sp.discount.replace('.', ',') );
			$('#filleul-system  option[value=1]').prop('selected', true);
			$('#points').val('');
		}else if( sp.points!=undefined && sp.points.length  && sp.points.length > 0){
			$('#points').val( sp.points );
			$('#filleul-system  option[value=2]').prop('selected', true);
			$('#discount').val('');
		}else{
			$('#filleul-system  option[value=0]').prop('selected', true);
		}
		if( sp.days!=undefined && sp.days.length ){
			$('#remise-day-valid').val( sp.days );
		}

		loadFilleulSystem();

		if( spSystem == '1' ){
			$('tr.sp-type-pts').show();

			if( sp.pts!=undefined && sp.pts.length ){
				$('#sponsor-points').val( sp.pts );
			}
			if( sp.limit!=undefined && sp.limit.length ){
				$('#sponsor-limit').val( sp.limit );
			}
			if( sp.cod_before_lost!=undefined && sp.cod_before_lost.length ){
				$('#cod-before-lost').val( sp.cod_before_lost );
			}
		}else{
			$('tr.sp-type-pourcent').show();

			if( sp.sp_ord_pourcent!=undefined && sp.sp_ord_pourcent.length ){
				$('#sp-ord-pourcent').val( sp.sp_ord_pourcent );
			}
			if( sp.sp_cod_days!=undefined && sp.sp_cod_days.length ){
				$('#sp-day-valid').val( sp.sp_cod_days );
			}
		}

		if( checked ){
			$('#active-sponsor-n, #active-sponsor-y').removeAttr( 'checked');
			if( sp.active.length && sp.active.length ){
				if( sp.active=='1' ){
					$('#active-sponsor-y').attr( 'checked', 'checked' );
				} else {
					$('#active-sponsor-n').attr( 'checked', 'checked' );
					$('#discount, #points, #remise-day-valid, #sponsor-points').val('').attr('disabled','disabled');
				}
			}else{
				$('#active-sponsor-n').attr( 'checked', 'checked' );
			}

			$('#sp-system option[value=' + sp.sp_system + ']').attr('selected', 'selected');

			if( sp.first_order == '1' ){
				$('#first_order').attr('checked', 'checked');
			}
		}
	});
	
	return false;
}

/**
 *	Cette fonction active toutes les actions sur l'onglet "Actions".
 */
function actionsForActions(){
	wst = parseInt( $('#wst_id').val() );
	prf = parseInt( $('#prf_id').val() );
	
	$('.avanced').click(function(){
		var rwa = $(this).attr('href').substring(1);
		displayPopup( rewardsParamtragesAvances, '', '/admin/tools/rewards/config/popup-actions.php?rwa=' + rwa + '&prf=' + prf + '&wst=' + wst );
	});
	$('#save-actions').click(function(){
		saveActions();
	});
}

/**
 *	Cette fonction permet de sauvegarder les informations de point sur les actions pour un profil et un site donnée.
 */
function saveActions(){
	message();
	wst = parseInt( $('#wst_id').val() );
	prf = parseInt( $('#prf_id').val() );
	
	var notInt = false;
	$('.raw-params input').each(function(){
		if( $.trim( $(this).val() ) && $(this).val()!='0' && !parseInt( $(this).val() ) ){
			notInt = true;
			$(this).css('border', '1px solid red');
		}
	});
	
	if( notInt ){
		message( "La valeur saisie pour le nombre de points doit être un numerique supérieur à 0.", true);
		return false;
	}
	
	$.getJSON('/admin/ajax/rewards/ajax-rewards.php?saverwa=1&prf=' + prf + '&wst=' + wst + '&' + $('#form-rewards').serialize(), function( result ){
		
		if( result.done ){
			message( rewardsInfosMaj, false );
			loadInfoActions();
		} else {
			message( rewardsErreurEnregistrementActions, true );
		}
		
	});
	
}

/**
 *	Cette fonction permet de charger les informations de points sur les actions.
 */
function loadInfoActions(){
	const wst = parseInt( $('#wst_id').val() );
	const prf = parseInt( $('#prf_id').val() );
	
	$.getJSON('/admin/ajax/rewards/ajax-rewards.php?getinforwa=1&prf=' + prf + '&wst=' + wst, function( result ){
		
		for( var r in result ){
			var pts = result[r]['pts']!=null ? parseInt( result[r]['pts'] ) : '0';
			var spPts = result[r]['sp_pts']!=null ? parseInt( result[r]['sp_pts'] ) : '0';
			
			$('#rwa-usr-' + r).val( pts );
			$('#rwa-sp-' + r).val( spPts );
		}
		
	});
	
	return false;
}

/**
 *	Cette fonction active toutes les actions sur l'onglet "Produits".
 */
function actionsForRules(){
	wst = parseInt( $('#wst_id').val() );
	prf = parseInt( $('#prf_id').val() );
	rwd = parseInt( $('#rwd_id').val() );
	$("#reward-products").val('');
	// mise à jour de l'affichage pour savoir si tout le catalogue est inclu ou exclu
	$.getJSON( '/admin/ajax/rewards/ajax-rewards.php?getallcatalog=1&rwd=' + rwd + '&wst=' + wst + '&prf=' + prf, function( result ){
		$('#rwd-all-catalog-0, #rwd-all-catalog-1').removeAttr('checked');
		// alert( result.all );
		result.all=='1' ? $('#rwd-all-catalog-1').attr('checked', 'checked') : $('#rwd-all-catalog-0').attr('checked', 'checked');
	});
	
	// ouverture de la popup de sélection selon le contenu recherché
	$('#rwd-add-rule input.text, #rwd-add-rule input.ref').focus(function(){
		switch( $(this).attr('name') ){
			case 'rwd-prd-name' :
				displayPopup( rewardsSelectProduit, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=0' );
				break;
			case 'rwd-cat-name' :
				displayPopup( rewardsSelectCategorie, '', '/admin/catalog/popup-categories.php' );
				break;
			case 'rwd-brd-name' : 
				displayPopup( rewardsSelectMarque, '', '/admin/ajax/catalog/popup-brands-select.php' );
				break;
		}
	});
	
	// affecte le focus à la bonne zone selon le contenu recherché
	$('#rwd-brd-select, #rwd-add-rule-brd').click(function(){ $('#rwd-brd-name').focus(); });
	$('#rwd-cat-select, #rwd-add-rule-cat').click(function(){ $('#rwd-cat-name').focus(); });
	$('#rwd-ref-select, #rwd-add-rule-prd').click(function(){ $('#rwd-prd-name').focus(); });
}

/**
 *	Cette fonction permet de gérer la sélection d'un contenu à inclure ou exclure dans le système depuis sa popup.
 *	@param id Obligatoire, identifiant du contenu
 *	@param name Obligatoire, nom du contenu
 *	@param zoneID Obligatoire, identifiant du champ input[type=text] où le nom doit être affiché
 */
function choose_elem( id, name, zoneID ){
	$('#rwd-add-rule .radio').removeAttr('checked');
	$('#rwd-add-rule input[type=text]').val( '' );
	$('#elem-id').val( '' );
	
	var type = false;
	switch( zoneID ){
		case 'rwd-brd-name': type = 'brd'; break;
		case 'rwd-cat-name': type = 'cat'; break;
		case 'rwd-prd-name': type = 'prd'; break;
	}
	$('#elem-type').val( type );
	
	// selection du bouton radio
	$('#' + zoneID).parent().find('input[type=radio]').attr('checked', 'checked');
	
	// gestion de l'affichage + enregistrement de l'identifiant du contenu
	$('#elem-id').val( id );
	$('#' + zoneID).val( name );
	
	hidePopup();
	return false;
}

// AJOUT DE PRODUIT FIDELITE

/**
 *	Cette fonction active toutes les actions pour l'ajout de produit en récompense de fidélité.
 */
function actionsForReward(){
	wst = parseInt( $('#wst_id').val() );
	prf = parseInt( $('#prf_id').val() );

	// ouverture de la popup de sélection selon le contenu recherché
	$('#rwd-product-name, #rwd-product-btn').focus(function(){
		$("#reward-products").val('true');
		displayPopup( rewardsSelectProduit, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0&cnt_publish=0' );
	});
}

/**
 *	Cette fonction permet de gérer la sélection d'un contenu à inclure ou exclure dans le système depuis sa popup.
 *	@param id Obligatoire, identifiant du contenu
 *	@param name Obligatoire, nom du contenu
 *	@param zoneID Obligatoire, identifiant du champ input[type=text] où le nom doit être affiché
 */
function choose_elem_reward( id, name){
	$("#reward-products").val('');
	$('#rwd-add-product input[type=text]').val( '' );
	$('#prd-elem-id').val( '' );
		
	// // gestion de l'affichage + enregistrement de l'identifiant du contenu
	$('#prd-elem-id').val( id );
	$('#rwd-product-name').val( name );
	
	hidePopup();
	return false;
}


/** 
 *	Cette fonction permet d'ajouter une règle d'inclusion ou d'exclusion.
 *	@param include Obligatoire, booléen indiquant s'il s'agit d'une inclusion (True) ou d'une exclusion (False)
 */
function addRewardProducts( include ){
	if( currentAjaxRequest )
		currentAjaxRequest.abort();

	var id = $('#prd-elem-id').val();
	var point = $('#rwd-product-points').val();
	var prf = parseInt( $('#prf_id').val() );
	var is_highlighting = $("input[name='is-highlighting']:checked").val();
	var pts_highlighting = $("input[name='pts-highlighting']").val();
	var error = false;
	var msg = "";

	if(id == ''){
		error = true;
		msg += rewardsChoixProduitAdd + "<br>";

	}

	if(point <= 0){
		error = true;
		msg += rewardsChoixPointFidelite;
	}

	if(!error){
		currentAjaxRequest = $.getJSON( '/admin/ajax/rewards/ajax-rewards.php?addproduct=1&cnt=' + id + '&points=' + point + '&include=' + include + '&prf='+ prf + '&ish=' + is_highlighting + '&ptsh=' + pts_highlighting , function(){
			currentAjaxRequest = false;
			$('#rwd-product-name').val( "" );
			$('#prd-elem-id').val( "" );
			$('#rwd-product-points').val("");
			$("input[name='is-highlighting']").each(function(){
				if($(this).val() == 0){
					$(this).prop('checked', true);
				}else{
					$(this).prop('checked', false);
				}
			});
			$("input[name='pts-highlighting']").val("");
			$('.error-catalog').hide();
			loadRewardProducts();
		});
	}else{
		$('.error-catalog').html(msg);
		$('.error-catalog').show();
	}

	
	return false;
}

/** 
 *	Cette fonction permet de supprimer une règle d'inclusion ou d'exclusion.
 */
function delRewardProducts(){
	var prf = parseInt( $('#prf_id').val() );
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	$('#rwd-products-pt input.checkbox:checked').each(function(){
		
		var id = $(this).val();
				
		currentAjaxRequest = $.getJSON( '/admin/ajax/rewards/ajax-rewards.php?delrewardproduct=1&prd=' + id + '&prf='+ prf, function(){
			currentAjaxRequest = false;
			loadRulesProducts();
		});
		
	});
	
	loadRewardProducts();

	return false;
}

/**	Cette fonction affiche le marqueur de synchronisation pour un produit donné.
 *	@param prd_is_sync la valeur de syncronisation du produit (à 1 si synchro, à 0 si non synchro)
 *	@return la balise HTML IMG permettant d'afficher l'indicateur de synchronisation pour un produit donné
 */
function loadRewardImgIsSyncProduct(prd_is_sync){
	var is_sync;
	var title_sync;
	
	if( prd_is_sync == 1 ){
		is_sync = 1;
		title_sync = 'Ce produit est synchronisé avec votre gestion commerciale';
	}else{
		is_sync 	= 0;
		title_sync	= 'Ce produit n\'existe que dans votre boutique en ligne';
	}
	
	return '<img class="sync" src="/admin/images/sync/' + is_sync + '.svg?1" title="' + title_sync + '" />';
}

/**
 *	Cette fonction permet de charger la zone affichant les produits offert dans l'offre de fidélité
 */
function loadRewardProducts(){
	var prf = parseInt( $('#prf_id').val() );
	currentAjaxRequest = $.getJSON( '/admin/ajax/rewards/ajax-rewards.php?getrewardproducts=1&prf='+prf, function( result ){

		var rules = rows = ''; 

		for( var i =0; i < result.length; i++ ){ 
			var img_is_sync;
			img_is_sync = loadRewardImgIsSyncProduct(result[i].is_sync);
			rows += '<tr>';			
			rows += '	<td headers="inc-check"><input name="del[]" class="checkbox" type="checkbox" id="r-'+ result[i].id + '" value="'+ result[i].id +'"></td>'
			rows += '	<td>' + img_is_sync + '</td>'
			rows += '	<td headers="inc-produit" data-label="' + rewardsReferences + ' : "><label for="ref-'+ result[i].id + '"><a href="/admin/catalog/product.php?cat=0&prd=' + result[i].id + '"> '+ result[i].ref + '</a></label></td>';
			rows += '	<td headers="inc-produit" data-label="' + rewardsProduits + ' : "><label for="id-'+ result[i].id + '"><a href="/admin/catalog/product.php?cat=0&prd=' + result[i].id + '"> '+ result[i].name + '</a></label></td>';
			rows += '	<td headers="inc-point" class="align-right" data-label="' + rewardsPoints + ' : "><label for="point-'+ result[i].id + '"> ' + result[i].points + '</label></td>';
			rows += '	<td headers="inc-point" data-label="' + rewardsAlerteMail + ' : "><label for="point-'+ result[i].id + '"> ' + result[i].is_highlighting + '</label></td>';
			rows += '	<td headers="inc-produit" class="align-right" data-label="' + rewardsRegleAffichage + ' : "><label for="id-'+ result[i].id + '"> '+ result[i].pts_highlighting + ' </label></td>';
			rows += '</tr>';
		}
		
		if( rows != '' ){
			rules += '<table id="rwd-products-pt" class="rwd-products-pt">';
			rules += '	<thead><tr>';
			rules += '	<th id="inc-check" class="col-check" data-label="Tout cocher : "><input class="checkbox" onclick="checkAllClick(this)" name="checkall" type="checkbox"></th>';
			rules += '	<th class="col-check"></th>';
			rules += '	<th id="inc-ref" class="thead-none col100px">' + rewardsReferences + '</th>';
			rules += '	<th id="inc-produit" class="thead-none">' + rewardsProduits + '</th>';
			rules += '	<th id="inc-point" class="align-right col60px thead-none">' + rewardsPoints + '</th>';
			rules += '	<th id="inc-produit" class="col100px thead-none">' + rewardsAlerteMail + '</th>';
			rules += '	<th id="inc-point" class="align-right thead-none" title="' + rewardsPointfideliteMail + '">' + rewardsRegleAffichage + '</th>';
			rules += '	</tr></thead>';
			rules += '	<tbody>' + rows + '</tbody>';
			rules += '	<tfoot><tr><td colspan="7"><sub class="btn-move"></sub><input onclick="return delRewardProducts();" type="submit" value="' + rewardsSupprimer + '" name="pmt-del-rule" class="button float-left" /></td></tr></tfoot>';
			rules += '</table>';
			
			$('#rwd-list-products').html( rules );
		} else {
			$('#rwd-list-products').html( '<div class="notice">' + rewardsAucunProduitEnregistre + '</div>' );
		}
		
		currentAjaxRequest = false;
	});
	
	return false;
}

// AJOUT DE PRODUIT FIDELITE

/** 
 *	Cette fonction permet d'ajouter une règle d'inclusion ou d'exclusion.
 *	@param include Obligatoire, booléen indiquant s'il s'agit d'une inclusion (True) ou d'une exclusion (False)
 */
function addRulesProducts( include ){
	if( currentAjaxRequest )
		currentAjaxRequest.abort();

	rwd = parseInt( $('#rwd_id').val() );
	
	var id = $('#elem-id').val();
	var type = $('#elem-type').val();
	
	if( !$.trim(type) || !$.trim(id) ){
		alert(rewardsChoixProduitCategorieMarque);
		return false;
	}
	
	include = include ? 1 : 0;
	
	currentAjaxRequest = $.getJSON( '/admin/ajax/rewards/ajax-rewards.php?addrule=1&rwd=' + rwd + '&type=' + type + '&cnt=' + id + '&include=' + include, function(){
		currentAjaxRequest = false;
		loadRulesProducts();
	});
	
	return false;
}

/** 
 *	Cette fonction permet de supprimer une règle d'inclusion ou d'exclusion.
 */
function delRulesProducts(){
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	
	$('#rwd-rules option:selected').each(function(){
		
		var attr = $(this).val();
		var cnt = attr.substring( 4 );
		var type = attr.substring( 0, 3 );
		
		currentAjaxRequest = $.getJSON( '/admin/ajax/rewards/ajax-rewards.php?delrule=1&rwd=' + rwd + '&type=' + type + '&cnt=' + cnt, function(){
			currentAjaxRequest = false;
			loadRulesProducts();
		});
		
	});
	
	return false;
}


/**
 *	Cette fonction permet de charger la zone affichant les inclusions ou exclusion.
 */
function loadRulesProducts(){
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	
	rwd = parseInt( $('#rwd_id').val() );
	
	currentAjaxRequest = $.getJSON( '/admin/ajax/rewards/ajax-rewards.php?getincludeprd=1&rwd=' + rwd, function( result ){
		var rules = prds = '';
		var type = cnts = cnt = false;
		
		for( type in result ){
			
			cnts = result[type];
			
			for( cnt in cnts ){
				var c = cnts[ cnt ];
				
				if( type!='prd' ){
					prds = '(' + c.prds + ' ' + rewardsProduit + ( parseInt(c.prds)>1 ? 's' : '' ) + ')';
				}
				
				rules += '<option value="' + type + '-' + c.id + '">(' + c.include + ') ' + htmlspecialchars(c.name) + ' ' + prds + '</option>';
			}
			
		}
		
		if( rules!='' ){
			rules = '	<select multiple="multiple" size="8" id="rwd-rules" name="rwd-rules[]">' + rules + '</select>';
			rules += '	<sub>' + rewardsSubProduitInclusFidelite + '</sub>';
			rules += '	<div class="buttons">';
			// rules += '		<input type="button" style="margin-right: 5px;" value="Tester" class="button" />';
			rules += '		<input onclick="return delRulesProducts();" type="submit" value="Supprimer" name="rwd-del-rule" class="button" />';
			rules += '	</div>';
			
			$('#rwd-list-rules').html( rules );
		} else {
			$('#rwd-list-rules').html( '<div class="notice">' + rewardsAunceException + '</div>' );
		}
		
		currentAjaxRequest = false;
	});
	
	return false;
}

/**
 *	Cette fonction permet de sauvegarder l'information "Tous le catalogue inclu / exclu".
 */
function saveRulesProducts(){
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	
	rwd = parseInt( $('#rwd_id').val() );
	
	var all = $('#rwd-all-catalog-1').is(':checked') ? 1 : 0;
	var in_promo = $('#include_pmt').is(':checked') ? 1 : 0;
	
	currentAjaxRequest = $.getJSON( '/admin/ajax/rewards/ajax-rewards.php?saveallcatalog=1&rwd=' + rwd + '&all=' + all + '&in_promo=' + in_promo, function( result ){
		if( result.done ){
			message( rewardsInfosMaj, false );
		} else {
			message( rewardsErreurEnregistrementInformations, true );
		}
		currentAjaxRequest = false;
	});
	
	return false;
}

/**
 *	Cette fonction charge les informations sur l'onglet "Configuration"
 */
function loadConfigRewards(){
	if ($('div.error').length) {return false;}
	if( currentAjaxRequest )
		currentAjaxRequest.abort();
	
	wst = parseInt( $('#wst_id').val() );
	prf = parseInt( $('#prf_id').val() );
	
	if( !wst || !prf )
		return;
	
	$('div.notice').hide();
	currentAjaxRequest = $.ajax({
		type: 'get',
		url: '/admin/ajax/rewards/ajax-rewards.php',
		data: 'loadinfo=1&wst=' + wst + '&prf=' + prf,
		dataType: 'json',
		async:true,
		success: function( reward ){
			$('#rwd_id').val( reward.id );
			$('#amount').val( reward.ratio.amount );
			$('#pts').val( reward.ratio.pts );
			$('#c-amount').val( reward.convert.amount );
			$('#c-pts').val( reward.convert.pts );
			$('#min-amount').val( reward.minamount );
			$('#days').val( reward.days );
			$('#days_lost, #relance-code').val( reward.dayslost ? reward.dayslost : 0 );
			$('#pmts-y, #pmts-n').removeAttr('checked');
			$('#nb-points').val( reward.nb_pts );
			$('#remise').val( reward.discount );
			
			$('#type-calc option').prop('selected', false);
			$('#type-calc option[value=' + reward.typecalc + ']').prop('selected', true);
			
			$('input[name=discount-type]').removeAttr('checked');
			if( reward.discount_type==0 ){
				$('#discount-type-0').attr('checked', 'checked');
			}else{
				$('#discount-type-1').attr('checked', 'checked');
			}
			
			$('#system_ratio option').removeAttr('selected');
			$('#system_ratio option[value=' + reward.system_ratio + ']').attr('selected', 'selected');
			
			$('#system option').removeAttr('selected');
			$('#system option[value=' + reward.system + ']').attr('selected', 'selected');
			loadConversionSystem();
			
			$('input[name=remise-on]').removeAttr('checked');
			$('#remise-on-' + reward.apply_on).attr('checked', 'checked');
			
			$('#days-limit-code').val( reward.days_code );
			
			reward.exclupromo!='0' ? $('#pmts-y').attr('checked', 'checked') : $('#pmts-n').attr('checked', 'checked');
			reward.cumulpts!='0' ? $('#cumul-pts-y').attr('checked', 'checked') : $('#cumul-pts-n').attr('checked', 'checked');
			
			$('#calcul option').removeAttr('selected');
			$('#calcul option[value=' + reward.type + ']').attr('selected', 'selected');

			if( reward.id!='0' && !$('#tabProducts').length && $('[name="showTabProduct"]').length ) {
				var html= '	<li><input type="button" name="tabConfig" id="tabConfig" value="Général"  class="selected" /></li>';
				html += '	<li><input type="hidden" name="showTabProduct" value="1" /><input type="button" name="tabProducts" id="tabProducts" value="' + rewardsProduits + '" /></li>';
				html += '	<li><input type="button" name="tabActions" id="tabActions" value="' + rewardsActions + '" /></li>';
				html += '	<li><input type="button" name="tabSponsors" id="tabSponsors" value="' + rewardsParrainage + '" /></li>';
				$('.tabstrip').html( html );
				actionOnTabstrip();
			} else if( reward.id=='0' ){
				$('#tabProducts, #tabActions, #tabSponsors').remove();
			}
			// Affichage du bloc après choix des sélecteurs
			$('#tabpanel').removeClass('none').addClass('block');
			$('.tabstrip').removeClass('none');
		},
		complete: function(){ currentAjaxRequest = false; }
	});
}

/** Fonction utilisé par la popup pour la sélection d'un élément à inclure ou exclure du système. */
function parent_select_prd( id, name ){ 
	if($("#reward-products").val()==='true'){
		return choose_elem_reward( id, name); 
	}else{
		return choose_elem( id, name, 'rwd-prd-name' ); 
	}
}

function updateCat( id, idParent, name ){ return choose_elem( id, name, 'rwd-cat-name' ); }
function close_cat_popup(){ hidePopup(); }

// Appel de la pop-up d'export des statistiques de fidélité
$("#export-rewards").click(function(){
	const url = '/admin/tools/rewards/popup-export-rewards.php';
	displayPopup( rewardsExportStat, '', url, undefined, 768, 450 );
});

$('select#filleul-system').change(function(){
	loadFilleulSystem();
});