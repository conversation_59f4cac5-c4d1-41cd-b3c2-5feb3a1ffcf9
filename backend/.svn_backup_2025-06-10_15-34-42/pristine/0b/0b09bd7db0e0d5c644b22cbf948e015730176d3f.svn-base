# DetectedCatalogColumnLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**configure** | [**\Swagger\Client\Model\LinksImportationConfigureCatalogColumnLink**](LinksImportationConfigureCatalogColumnLink.md) |  | [optional] 
**ignore** | [**\Swagger\Client\Model\LinksImportationIgnoreColumnLink**](LinksImportationIgnoreColumnLink.md) |  | [optional] 
**reattend** | [**\Swagger\Client\Model\LinksImportationReattendColumnLink**](LinksImportationReattendColumnLink.md) |  | [optional] 
**map** | [**\Swagger\Client\Model\LinksImportationMapCatalogColumnLink**](LinksImportationMapCatalogColumnLink.md) |  | [optional] 
**unmap** | [**\Swagger\Client\Model\LinksImportationUnmapCatalogColumnLink**](LinksImportationUnmapCatalogColumnLink.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


