<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  389701 => 'T-Mobile',
  389702 => 'T-Mobile',
  389703 => 'T-Mobile',
  389704 => 'T-Mobile',
  389705 => 'T-Mobile',
  389706 => 'T-Mobile',
  389707 => 'T-Mobile',
  389708 => 'T-Mobile',
  389709 => 'T-Mobile',
  389711 => 'T-Mobile',
  389712 => 'T-Mobile',
  389713 => 'T-Mobile',
  389714 => 'T-Mobile',
  389715 => 'T-Mobile',
  389716 => 'T-Mobile',
  389717 => 'T-Mobile',
  389718 => 'T-Mobile',
  389719 => 'T-Mobile',
  389721 => 'T-Mobile',
  389722 => 'T-Mobile',
  389723 => 'T-Mobile',
  389724 => 'T-Mobile',
  389725 => 'T-Mobile',
  389726 => 'T-Mobile',
  389727 => 'T-Mobile',
  389729 => 'T-Mobile',
  389732 => 'Vip',
  389733 => 'Telekom',
  389734 => 'Vip',
  3897421 => 'Mobik',
  389752 => 'Vip',
  389753 => 'Vip',
  389754 => 'Vip',
  389755 => 'Vip',
  389756 => 'Vip',
  389757 => 'Vip',
  389758 => 'Vip',
  389759 => 'Vip',
  389762 => 'Vip',
  389763 => 'Vip',
  389764 => 'Vip',
  389765 => 'Vip',
  389766 => 'Vip',
  389767 => 'Vip',
  389768 => 'Vip',
  389769 => 'Vip',
  389771 => 'Vip',
  389772 => 'Vip',
  389773 => 'Vip',
  389774 => 'Vip',
  389775 => 'Vip',
  389776 => 'Vip',
  389777 => 'Vip',
  389778 => 'Vip',
  389779 => 'Vip',
  389781 => 'Vip',
  389782 => 'Vip',
  389783 => 'Vip',
  389784 => 'Vip',
  389785 => 'Vip',
  389786 => 'Vip',
  389787 => 'Vip',
  389788 => 'Vip',
  389789 => 'Vip',
  389792 => 'Lycamobile',
  389793 => 'Lycamobile',
);
