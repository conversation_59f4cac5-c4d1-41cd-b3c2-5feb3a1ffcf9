<?php

	/**	\file popup-export-customers.php
	 *	Ce fichier est utilisé pour l'exportation des comptes clients. Il est affiché dans le back-office sous forme de popup.
	 * 	Il permet de paramétrer l'export (format, colonnes), l'export effectif étant réalisé par le fichier export.php.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

    define('ADMIN_PAGE_TITLE', 'Export des comptes clients');
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	$default = array( 'ref', 'email', 'title_name', 'adr_firstname', 'adr_lastname', 'address1', 'address2', 'zipcode', 'city', 'country', 'notes' );

	$ar_cols = array(
		'id'		    => _('Identifiant Client'),
		'ref'	        => _('Code client'),
		'title_name' 	=> _('Civilité'),
		'adr_firstname' => _('Prénom'),
		'adr_lastname' 	=> _('Nom de famille'),
		'society' 	    => _('Société'),
		'siret'		    => _('Siret'),
		'naf'		    => _('Code NAF'),
		'email' 	    => _('Email'),
		'can_login' 	=> _('Peut se connecter?'),
		'address1'      => _('Adresse 1'),
		'address2' 	    => _('Adresse 2'),
		'zipcode' 		=> _('Code postal'),
		'city'			=> _('Ville'),
		'country'       => _('Pays'),
		'prf_name'	 	=> _('Profil'),
		'prc_id'	    => _('Catégorie tarifaire'),
		'notes' 		=> _('Commentaires'),
		'parent_id'     => _('Compte parent'),
		'wst_id'		=> _('Créé depuis'),
		'date_created'	=> _('Date d\'inscription'),
		'first_login' 	=> _('Première connexion'),
		'last_login' 	=> _('Dernière connexion'),
		'orders'		=> _('Commandes validées'),
		'orders_canceled'=> _('Commandes annulées'),
		'longitude' 	=> _('Longitude'),
		'latitude' 		=> _('Latitude'),
		'rewards' 		=> _('Points de fidélité'),
		'total_ht' 	    => _('CA HT'),
		'total_ttc'	    => _('CA TTC'),
		'source' 		=> _('Source'),
		'first_visit' 	=> _('Première visite'),
		'nb_visit' 	    => _('Nombre de visites'),
		'ip'		    => _('Adresse ip du client'),
		'phone' 	    => _('Téléphone'),
		'fax' 	        => _('Fax'),
		'mobile' 		=> _('Mobile'),
		'work' 			=> _('Téléphone en journée'),
		'total_marge' 	=> _('Marge HT'),
		'seller_id'		=> _('Représentant'),
		'last_cgv_accepted' => _('Dernière acceptation CGV')
	);

	// On enleve la posibilité d'exporter certaines données pour les Riashop Yuto Essentiel
	if( tnt_tenants_is_yuto_essentiel() ){
		unset( $ar_cols['orders'], $ar_cols['orders_canceled'], $ar_cols['rewards'], $ar_cols['total_ht'], $ar_cols['total_ttc'], $ar_cols['source'], $ar_cols['first_visit'],
			$ar_cols['nb_visit'], $ar_cols['ip'], $ar_cols['total_marge'], $ar_cols['last_cgv_accepted']);
	}
?>
	<link rel="stylesheet" type="text/css" href="/admin/css/export.css?1" />

	<div id="export">
		<form action="export.php" method="post">
			<input id="prf" name="prf" type="hidden" value="<?php print isset($_GET['prf']) ? $_GET['prf'] : 0; ?>">
			<input id="seg" name="seg" type="hidden" value="<?php print isset($_GET['seg']) ? $_GET['seg'] : 0; ?>">
			<input id="seller" name="seller" type="hidden" value="<?php print isset($_GET['seller']) ? $_GET['seller'] : 0; ?>">
			<input id="sort" name="sort" type="hidden" value="<?php print isset($_GET['sort']) ? $_GET['sort'] : 0; ?>">
			<input id="dir" name="dir" type="hidden" value="<?php print isset($_GET['dir']) ? $_GET['dir'] : 0; ?>">
			<?php
			$usr_ids = isset($_GET['usr_id']) ? $_GET['usr_id'] : array();
			if (!is_array($usr_ids)) {
				$usr_ids = array($usr_ids);
			}

			foreach ($usr_ids as $usr_id) { ?>
				<input id="dir" name="usr_id[]" type="hidden" value="<?php print htmlspecialchars($usr_id) ?>">
			<?php } ?>

			<h2>
				<span style="float: left;"><?php print _('Gestion de l\'export')?></span>
				<div class="export-action">
					<input class="btn-action" type="submit" name="export" value="<?php print _('Télécharger')?>" />
				</div>
				<div class="clear"></div>
			</h2>
			<div class="check-catchilds">
				<input type="checkbox" name="for_excel" id="for_excel" value="1" />
				<label for="for_excel"><?php print _('Exporter pour Excel')?></label>
				<div class="clear"></div>
				<input type="checkbox" name="for_mac" id="for_mac" value="1" />
				<label for="for_mac"><?php print _('Exporter pour Excel (Mac)')?></label>
				<div class="clear"></div>
				<input type="checkbox" name="include_childs" id="include_childs" value="1" />
				<label for="include_childs" title="<?php print _('Exporter également les comptes enfants'); ?>"><?php print _('Inclure les comptes enfants')?></label>
				<div class="clear"></div>
			</div>
            <span class="part-export">
				<?php print _('Informations :'); ?>
				<a href="#" class="check-all-col"><?php print _('Cocher tout')?></a> | <a href="#" class="uncheck-all-col"><?php print _('Décocher tout')?></a>
			</span>
			<div class="clear"></div>

			<?php
                print '
					<div class="cols">
				';
				foreach( $ar_cols as $key=>$name ){
					$checked = in_array( $key, $default ) ? 'checked="checked"' : '';
                    print '
                        <div  class="elems">
                            <input type="checkbox" '.$checked.' value="'.$name.'" id="col-'.$key.'" name="col-filter['.$key.']" class="col-filter" />
                            <label for="col-'.$key.'">'.htmlspecialchars( $name ).'</label>
                        </div>
                    ';
				}
				print '
				</div>
				';
            ?>
			<div class="clear"></div><?php
			// On enleve la posibilité d'exporter des informations complémenaires pour les Riashop Yuto Essentiel
			if( !tnt_tenants_is_yuto_essentiel() ){ ?>
				<span class="part-export">
					<?php print _('Informations complémentaires :'); ?>
					<a href="#" class="check-all-fld"><?php print _('Cocher tout')?></a> | <a href="#" class="uncheck-all-fld"><?php print _('Décocher tout')?></a>
				</span>
				<div class="clear"></div>
				<?php
					print '
						<div class="cols">
					';
					$fld = fld_fields_get(0, 0, 0, 0, 0, 0, null, array(), false, array(), null, CLS_USER);
					if( $fld && ria_mysql_num_rows($fld) ){
						while($f = ria_mysql_fetch_array( $fld )){
							$key = 1;
							$checked = in_array( $key, $default ) ? 'checked="checked"' : '';
							print '
							<div  class="elems">
								<input type="checkbox" '.$checked.' value="'.$f['name'].'" id="fld-'.$f['id'].'" name="fld-filter['.$f['id'].']" class="fld-filter" />
								<label for="fld-'.$f['id'].'">'.htmlspecialchars( $f['name'] ).'</label>
							</div>
						';
						}
					}
			} ?>
        </form>
    </div>
    <script src="/admin/js/jquery.min.js"></script>
	<script>
		$(document).ready(
			function(){
				$('#for_excel').click(function(){
					$('#for_mac').prop('checked', false);
				})

				$('#for_mac').click(function(){
					$('#for_excel').prop('checked', false);
				})

				$('.check-all-col').click(function(){
					$('.col-filter').attr('checked', 'checked');
					return false;
                });

				$('.uncheck-all-col').click(function(){
					$('.col-filter').removeAttr('checked');
					return false;
				});

				$('.check-all-fld').click(function(){
					$('.fld-filter').attr('checked', 'checked');
					return false;
                });

				$('.uncheck-all-fld').click(function(){
					$('.fld-filter').removeAttr('checked');
					return false;
                });

                $('input[name=export]').click(function(){
					var cols = '';
					var flds = '';

				$('.col-filter:checked').each(function(){
					var p = $(this).attr('name').replace('col-filter[', '').replace(']', '');
					var v = $(this).val();
					cols += ($.trim(cols) != '' ? '|' : '') + p;
				});

				$('.fld-filter:checked').each(function(){
					var p = $(this).attr('name').replace('fld-filter[', '').replace(']', '');
					var v = $(this).val();
					flds += ($.trim(flds) != '' ? '|' : '') + p;
				});

				var prf = $('input[name=prf]').val();
				var seg = $('input[name=seg]').val();
				var seller = $('input[name=seller]').val();
				var sort = $('input[name=sort]').val();
				var dir = $('input[name=dir]').val();

				var url = '/admin/customers/export.php?prf='+prf+'&seg='+seg+'&seller='+seller+'&sort='+sort+'&dir='+dir+'&cols='+cols+'&flds='+flds;

				if ($('[name=for_excel]:checked').length) {
					url += '&for_excel=1';
				}

				if ($('[name=for_mac]:checked').length) {
					url += '&for_mac=1';
				}

				if ($('[name=include_childs]:checked').length) {
					url += '&include_childs=1';
				}

				var selectedCustomers = [];
				$('[name="usr_id[]"]').each(function(){selectedCustomers.push($(this).val())});
				url += selectedCustomers.length > 0 ? '&usr_id[]=' + selectedCustomers.join('&usr_id[]=') : '';

				$('body').append('<div class="popup_ria_back_load"></div>');
				$('body').append('<div class="popup_ria_back_notice notice"><?php print _('Votre export est en cours de préparation, veuillez patienter...')?></div>');

				$.ajax({
					type 	: 'get',
					url 	: url,
					data 	: '',
					async 	: true,
					success : function(urlDownload){
						$('.popup_ria_back_notice').html('<?php print _("Votre export est prêt à être téléchargé")." : <a href=\"#\" class=\"download-file\">"._("Télécharger")."</a>"?>');
					}
				});
				return false;
				});
			}
        ).delegate(
			'.download-file', 'click', function(){
				var url = parent.window.location.href;
				url = url + (url.match(/\?/) ? '&' : '?') + 'downloadexport=2';
				parent.window.location.href = url;
				parent.hidePopup();
			}
		);
     </script>
<?php
	require_once('admin/skin/footer.inc.php');
?>