<?php
	/**	\file mapping.php
	 *	Cette page permet la configuration des correspondances entre les colonnes du fichier à importer et les champs RiaShop
	*	pour le type de données choisi.
	*/

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT');

	require_once('RegisterGCP.inc.php');
	require_once('imports.inc.php');
	require_once('view.imports.inc.php');

	// L'identifiant de l'import est obligatoire et doit correspondre à un import valide
	if( !isset($_GET['imp']) || !is_numeric($_GET['imp']) ){
		header('Location: /admin/tools/imports/index.php');
		exit;
	}

	if( isset($_GET['execute']) ){
		echo ipt_imports_exec($_GET['imp']);exit;
	}

	// Annulation d'un import
	if( isset($_POST['del_imp']) ){
		ipt_view_import_del( $_GET['imp'] );
		header('location: /admin/tools/imports/index.php');
		exit;
	}

	// Détermine si l'utilisateur a accès au import de type synchronisation
	$access_sync = false;
	if( $config['USER_RIASTUDIO'] ){
		$access_sync = null;
	}

	// Détermine si l'utilisateur a accès aux imports de type système
	$access_system = false;
	if( $config['USER_RIASTUDIO'] ){
		$access_system = null;
	}

	// Détermine s'il l'accès est réalisé pour un import ou pour une backup
	// Les backups ne sont accessible qu'au administrateur RiaStudio
	$is_backup = isset($_GET['backup']) && $_GET['backup'];

	// Récupère les classes accessibles à l'import
	$classes = view_import_get_classes( true );

	// Récupère les informations sur l'import
	$r_import = ipt_imports_get( $_GET['imp'], $is_backup, false, '', 0, $classes, false, false, '', '', [], $access_system, 0, $access_sync );
	if( !$r_import || !ria_mysql_num_rows($r_import) ){
		header('Location: /admin/tools/imports/index.php');
		exit;
	}

	$import = ria_mysql_fetch_assoc( $r_import );

	// Téléchargement du fichier
	// TODO : Pour le moment, cette fonctionnalité est désactivé
	if( isset($_GET['download']) ){
		if( file_exists($import['file_path']) ){
			header('Content-Type: application/x-force-download');
			header('Content-Disposition: attachment; filename="'.str_replace(array('.csv', '.'.$import['format']), '', ipt_filter_import_name($import['name'])).'.csv');
			ob_clean();
			flush();
			readfile($import['file_path']);
			exit;
		}
	}

	$errors = array();

	// Désactive la modification des imports non récursif déja réalisé
	if (!is_null($import['period']) && !is_null($import['period_value'])) {
		$read_only = false;
		$read_only_after_exec = false;
	} elseif ($import['state'] == 'create') {
		$read_only = false;
		$read_only_after_exec = true;
	} else {
		$read_only = true;
		$read_only_after_exec = true;
	}

	// Certaines options d'association ne sont pas valides pour les Riashop Yuto Essentiel
	$exclude_code = [];
	if( RegisterGCP::getPackage($config['tnt_id']) == 'essentiel' ){
		$exclude_code = ['USR_CAT_ID', 'USR_PRC_ID', 'USR_RELATION', 'USR_CAN_LOGIN', 'USR_RELATION_SOCIETY', 'USR_DEFAULT_PRF', 'PRC_DEFAULT_DISCOUNT_TYPE'];
	}

	// Récupération du mapping sur cette import
	$rSchema = ipt_schemas_get( $import['cls_id'] );
	if (!$rSchema) {
		header('location: /admin/tools/imports/index.php');
		exit;
	}

	// Chargement des choix possibles pour le mapping
	$schemas = array();
	$fld_pre_map = array();

	// En premier : le choix de ne pas l'importer
	$schemas['-']['DEFAULT'] = _('Ne pas importer');

	while ($row = ria_mysql_fetch_assoc($rSchema)) {
		if( !in_array($row['code'], $exclude_code) ){
			$schemas[$row['cat_name']][$row['code']] = $row['name'];
		}
	}

	// En troisième : Les champs avancé
	// Les champs avancés ne sont pas accessible sur les imports : Stocks, Tarifs et Promotions sur produits
	if( !in_array($import['cls_id'], array(CLS_STOCK, CLS_PRICE)) ){
		$include_system = $_SESSION['usr_tnt_id']==0;
		$r = fld_fields_get(0, 0, 0, 0, 0, 0, null, array(), $include_system, array(), null, $import['cls_id'], null, false, null, false);
		if ($r && ria_mysql_num_rows($r)) {
			while ($fld = ria_mysql_fetch_assoc($r)) {
				$schemas['Champs personnalisés']['FLD_'.$fld['id']] = $fld['name'];
				$fld_pre_map[ipt_mapping_gen_alias($fld['name'])] = $fld['id'];
			}
			asort($schemas['Champs personnalisés']);
		}
	}

	// En dernier : une ligne spécifique pour la gestion d'imports de sauvegarde
	if ($is_backup) {
		$rfld = ipt_schemas_get(0, 'BACKUP_LINE_ACTION');
		$row = ria_mysql_fetch_assoc($rfld);
		$schemas[$row['cat_name']][$row['code']] = $row['name'];
	}

	$import_method = 'file';
	$filename = $import['file_path'];
	if (isset($import['url'])) {
		$import_method = 'url';
		$filename = $import['url'];
		if (isset($import['login'])) {
			$import_method = 'ftp';
			$filename = $import['file_path'];
		}
	}

	$error_open_file = false;

	// try catch pour toutes les manipulations de fichier car il se peux qu'il y est des problèmes de droit d'accès sur les dossiers ou les fichiers sont inexistant
	try {
		$file = new ImportFile($filename, $import_method, $import['name']);
		if ($import_method == 'ftp') {
			$file->connectToFtp($import['url'], $import['login'], $import['password']);
		}
		if ($import_method == 'ftp' || $import_method == 'url') {
			$file->saveFile();
		}
		$file->readFile($import['separator'], $import['text_separator']);

		// Chargement du mapping correspondant à cet import
		$mapping = array();
		$rMapping = ipt_mapping_get($import['id']);
		$mapping_saved = false;
		if ($rMapping) {
			while ($row = ria_mysql_fetch_assoc($rMapping)) {
				$mapping[$row['pos']] = $row;
			}
			$mapping_saved = true;
		} else {
			foreach ($file->columns as $pos => $col) {
				$alias = ipt_mapping_gen_alias($col);
				$rCode = ipt_mapping_get_by_alias($alias, $import['cls_id']);
				if (!$rCode) {
					$mapping[] = null;
					$rCode = ipt_schemas_aliases_get_by_alias($alias, $import['cls_id']);
					if ($rCode && ria_mysql_num_rows($rCode)) {
						$mapping[$pos] = ria_mysql_fetch_assoc($rCode);
					} elseif (array_key_exists($alias, $fld_pre_map)) {
						$mapping[$pos] = array(
							'code' => 'FLD',
							'fld_id' => $fld_pre_map[$alias],
						);
					}
				} else {
					$mapping[$pos] = ria_mysql_fetch_assoc($rCode);
				}
			}
		}

		// Construit la prévisualisation des données en essayant d'ouvrir le fichier de données
		if ($file->exists()) {
			$preview = array();
			$handle = $file->openFile();
			$first = true;
			while ($line = fgetcsv($handle, null, $import['separator'])) {
				if ($first) {
					$first = false;
					continue;
				}
				foreach ($line as $index => $value) {
					if (strlen($value) > 30) {
						$value = substr($value, 0, 30).'...';
					}
					$preview[$index][] = $value;
				}
				break;
			}
			/*if ($import_method == 'ftp' || $import_method == 'url') {
				$file->deleteFile();
			}*/
		}
	} catch (Exception $e) {
		error_log('[tenant '.$config['tnt_id'].'][exception mapping.php:'.__FILE__.']'.$e->getMessage());
		$errors[] = _("Une erreur s'est produite à l'ouverture du fichier. La configuration n'est pas disponible.");
		$error_open_file = true;
	}

	// différence Yuto/RiaShop
	$mapping_page_title       = _('Faites correspondre vos données avec Yuto');
	if( $read_only ){
		$mapping_page_title = _('Correspondances de vos données avec Yuto');
	}

	$have_website             = 'Yuto';
	$help_message             = _("Consultez [link] à l'import sur le site support Yuto Essentiel.");
	$mapping_destination_text = _('Destination dans Yuto');

	// S'il ne s'agit pas d'une version Yuto Essentiel
	if (RegisterGCP::getPackage($config['tnt_id']) != 'essentiel') {
		$mapping_page_title       = _('Faites correspondre vos données avec RiaShop');
		if( $read_only ){
			$mapping_page_title = _('Correspondances de vos données avec RiaShop');
		}

		$have_website             = 'RiaShop';
		$help_message             = _("Consultez [link] sur l’import de données.");
		$mapping_destination_text = _('Destination dans RiaShop');
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', (tnt_tenants_is_yuto_essentiel() ? _('Imports de comptes clients') : _('Imports')).' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
?>

<div class="tools-import-customers">
	<input type="hidden" name="read_only" id="read_only" value="<?php echo $read_only ?>" />
	<input type="hidden" name="read_only_after_exec" id="read_only_after_exec" value="<?php echo $read_only_after_exec ?>" />

	<h2><?php print $mapping_page_title; ?></h2>

	<?php
		// Affiche l'encart contenant les informations sur l'import
		print
			'<form action="/admin/tools/imports/index.php" method="post">'
				.view_imports_card( $import, 'mapping' )
			.'</form>';

		if( !$read_only ){

			print '<div class="notice" style="margin: 30px 0">';

			if (RegisterGCP::getPackage($config['tnt_id']) == 'essentiel') {
				print _('Effectuez les correspondances ci-dessous entre les colonnes de votre fichier et la destination dans Yuto.').' ';
			}else{
				print _('Effectuez les correspondances ci-dessous entre les colonnes de votre fichier et la destination dans RiaShop.').' ';
			}

			$link = '<a href="https://support.riashop.fr/aide/importer-comptes-clients-dans-riashop/?utm_source=riashop&utm_medium=import-mapping&utm_campaign=support&utm_term=lien_support" target="_blank">'._('la notice dédiée').'</a>';
			if (RegisterGCP::getPackage($config['tnt_id']) != 'essentiel') {
				$link = '<a href="/admin/tools/imports/notice-imports.pdf" download="'._('Notice d\'utilisation riaShop - Imports de données').'">'._("la notice d'utilisation").'</a>';
			}
			print str_replace( '[link]', $link, $help_message );

			print '</div>';
		}
	?>
</div>

	<?php
		if (isset($errors) && !empty($errors)) {
			print '<div class="error">';
			foreach ($errors as $e) {
				print '<p>'.htmlspecialchars( $e ).'</p>';
			}
			print '</div>';
		}

		if ($import['cls_id'] == CLS_PRICE) {
			print '<div class="notice" id="notice-pmt-dates">'._('Les dates de début et de fin doivent obligatoirement être fournies.').'</div>';
		}

		print '<div id="error-all" class="notif" style="max-width: 1200px">';
		if (isset($_SESSION['map-errors']['all'])) {
			foreach ($_SESSION['map-errors']['all'] as $error) {
				print '<div>'.htmlspecialchars( $error ).'</div>';
			}
			unset($_SESSION['map-errors']);
		}
		print '</div>';

		if (!$error_open_file) {
			?><form method="post" action="mapping.php?imp=<?php print $import['id']; ?>" id="map-form" class="map-form">
				<input type="hidden" name="imp_id" id="imp_id" value="<?php echo $import['id']; ?>" />
				<input type="hidden" name="cls_id" id="cls_id" value="<?php echo $import['cls_id']; ?>" />

				<?php if ($import['cls_id'] == CLS_USER && $import['action'] != "upd") { ?>
					<div class="notice-default">
						<label for="default_prf"><?php print _('Profil par défaut :'); ?></label>
						<select <?php echo ($read_only) ? 'disabled' : '' ?> id="default_prf" class="select-client" name="default_prf">
							<option disabled="disabled" value="0"><?php print _('Sélectionnez un profil par défaut'); ?></option>
							<?php
							$r = gu_profiles_get();
							if ($r) {
								while ($s = ria_mysql_fetch_assoc($r)) {
									if ($s['id'] == 1) { // Bloque l'import de compte administrateur
										continue;
									}
									// Bloque l'import de type contact pour les RiaShop Yuto Essentiel
									if (tnt_tenants_is_yuto_essentiel() && $s['id'] == PRF_CONTACT) {
										continue;
									}
									echo '<option value="'.$s['id'].'">'.htmlspecialchars($s['name']).'</option>';
								}
							}
							?>
						</select>
						<p style="margin-bottom: 0;"><?php print _('Ce profil par défaut sera utilisé pour les comptes clients qui n\'ont pas de profil renseigné dans l\'import.'); ?></p>
					</div>
				<?php }

				if ($is_backup) {
					print '<input type="hidden" name="backup" value="'.$import['id'].'" />';
				}
			?>

			<div id="table-map-file" class="map-file list table">
				<div class="table-map-head">
					<div class="table-map-cell"><?php print _('Colonnes de votre fichier'); ?></div>
					<div class="table-map-cell"><?php print _('Aperçu'); ?></div>
					<div class="table-map-cell"><?php print $mapping_destination_text; ?></div>
				</div>

				<?php
					if( !isset($file) ){
						print '
							<div class="table-map-section table-map-link">
								<div class="table-map-row">'._('Une erreur est survenue lors de la lecture de votre fichier.').'</div>
							</div>
						';
					}else{
						// Pré-charge le tableau des correspondances à établir
						print '
							<div id="table-map-link" class="table-map-section table-map-link">
								<div class="table-map-title">
									'._('Correspondances à établir').' <span>- '._('Sélectionnez les destinations correspondantes à vos données').'</span>
								</div>
						';

						// La correspondance n'est pas reconnue, il faut l'enregistrer
						foreach( $file->columns as $key => $col ){
							foreach( $schemas as $code => $name ){
								$isLinked = false;
								if( (isset($mapping[$key]) || $mapping_saved) ){
									$isLinked = true;
									break;
								}
							}

							if (!$isLinked) {
								echo ria_imports_mapping_section($key, $col, $preview, $read_only, $schemas, $import, $mapping, $mapping_saved );
							}
						}

						print '</div>';

						// Pré-charge le tableau des correspondances pour lesquels une action est à effectuer
						print '
							<div id="table-map-action" class="table-map-section table-map-action">
								<div class="table-map-title">
									'._('Actions à effectuer').' <span>- '._('Certaines correspondances nécessitent une précision supplémentaire').'</span>
								</div>
						';

						foreach ($file->columns as $key => $col) {
							foreach ($schemas as $code => $name) {
								$isLinked = false;
								if ((isset($mapping[$key]) || $mapping_saved)) {
									$isLinked = true;
									break;
								}
							}
							if ($isLinked) {
								echo ria_imports_mapping_section($key, $col, $preview, $read_only, $schemas, $import, $mapping, $mapping_saved );
							}
						}

						print '</div>';

						// Création d'un tableau qui contiendra les correspondances trouvées
						// Le chargement de ce tableau est fait en JS après chargement de la page
						print '
							<div id="table-map-valid" class="table-map-section table-map-valid">
								<div class="table-map-title">
						';

						if( !$read_only ){
							print _('Correspondances trouvées').' <span>- '._('Vous pouvez faire des modifications').'</span>';
						}else{
							print _('Correspondances réalisées').' <span>- '._('Retrouvez ci-dessous les colonnes qui ont été importées').'</span>';
						}
						print '
								</div>
						';

						if( !$read_only ){
							print '
								<div class="table-map-row --row-bottom-button">
									<div class="table-map-cell --cell-save">
										<input type="submit" name="save" value="'._('Enregistrer').'" />
										<p>'._('Sauvegardez votre travail pour le terminer plus tard').'</p>
									</div>

									<div class="table-map-cell --cell-cancel">
										<input type="submit" name="del_imp" value="'._('Annuler').'" />
										<p>'._('Supprimer cet import'). '</p>
									</div>

									<div class="table-map-cell --cell-import">
										<input class="button-secondary btn-import-first" type="submit" name="import" value="'._('Importer').'" />
										<p id="js-import-incomplete-notice">
											<span id="incomplete-field-count"></span><br>
											<span id="incomplete-field-compl"></span>
										</p>
									</div>
								</div>
							';
						}

						print '</div>';

						// Si l'import est en lecture seul, un nouveau tableau est créé
						// Celui-ci contiendra les colonnes dont l'utilisateur a choisi de ne rien importé
						if( $read_only ){
							print '
								<div id="table-map-noimp" class="table-map-section table-map-noimp">
									<div class="table-map-title">
										'._('Colonnes ignorées').' <span>- '._('Retrouvez ci-dessous les colonnes qui n\'ont pas été importées').'</span>
									</div>
								</div>
							';
						}
					}
				?>
			</div>
		</form>
	<?php } ?>

<script>
	// objets pour la validation du schema
	var fullSchema = <?php echo json_encode(ria_imports_get_schemas_full($schemas, $import)) ?>;
	var registeredMapping = <?php echo json_encode(ria_imports_get_registered_mapping($schemas, $import, $mapping)) ?>;

	var id = <?php print json_encode( $_GET['imp'] ); ?>

	$(document).ready(function() {
		$('#default_prf').change();

		$('#default_prf').change(function() {
			var prf = $('#default_prf').val();
			var action = '<?php print $import['action']; ?>';
			var val_select = '';

			$('.select_mapping').each(function() {
				val_select = $(this).val();

				if (val_select == 'USR_RELATION_SOCIETY' && prf != 65) {
					$(this).val('');
				}

				if (prf != 65 && action === 'add') {
					$(this).find('option[value=USR_RELATION_SOCIETY]').hide();
					$(this).find('option[value=USR_RELATION_SOCIETY]').prop('disabled', true);
				} else {
					$(this).find('option[value=USR_RELATION_SOCIETY]').show();
					$(this).find('option[value=USR_RELATION_SOCIETY]').prop('disabled', false);
				}
			});
		});
	});
</script>
<?php
	require_once('admin/skin/footer.inc.php');