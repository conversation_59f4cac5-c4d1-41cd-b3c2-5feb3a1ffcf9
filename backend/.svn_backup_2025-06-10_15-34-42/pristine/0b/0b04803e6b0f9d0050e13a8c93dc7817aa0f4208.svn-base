.HelpWidgetType-new-bug-title {
    width: 308px
    float: left;
}

#MetadataAdminScreen-addField-add {
    float: left;
}

.TableWidgetType .recover:hover {
    background-color: #FFF;
}

#clearCache-settings:rootNodes-list_0 {
    border-top: none;
}

.HelpWidgetType-list {
    list-style-image: url();
}

@media (min-width: 320px) and (max-width: 961px) {
    .tooltipsrt:hover span.tltp,
    .tooltipstp:hover span.tltp {
        visibility: hidden;
    }
}

#single-line-multi-statement-no-semicolon {
    padding: 0 border: 20;
}

#multi-line-style-no-semicolon {
    padding: 0 /* phpcs:ignore Standard.Cat.Sniff -- for reasons */
    border:
        20
    margin:
        0px  /* top */
        10px /* right + left */
}

#multi-line-style-whitespace {
    padding: 0; /* phpcs:ignore Standard.Cat.Sniff -- for reasons */
    border:
        20;
    margin:
        10px /* top */
        0px;  /* right + left */
}

.allow-for-star-hack {
    cursor: pointer;
    *cursor: hand;
}

/* Live coding. Has to be the last test in the file. */
.intentional-parse-error {
    float: left
