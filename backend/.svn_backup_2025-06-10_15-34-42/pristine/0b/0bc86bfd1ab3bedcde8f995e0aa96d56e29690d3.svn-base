<?php

require_once('orders.inc.php');
require_once('Services/Customer/Customer.class.php');

/**	\brief Cette classe permet de réaliser les actions liés à l'espace "Mon Compte".
 * 	Toutes les actions lèveront des exceptions avec un message personnalisé et un code unique.
 * 	Les codes suivants sont réservés (et doivent être utilisé dans le cas de nouvelle action) :
 * 			- 97 : Le compte client donné n'existe pas
 * 			- 98 : Aucun client n'est connecté
 * 			- 99 : Une erreur inattendue (quelque soit l'action)
 */
class CustomerActions {

	/** Cette fonction permet d'authentifier un client.
	 *  @param array $data Obligatoire, informations permettant l'authentification
	 *        - sid : identifiant de session
	 *        - email : adresse mail renseigné lors de la connexion
	 *        - password : mot de passe utilisé
	 *  @return bool Une exception est levée en cas d'erreur, true en cas de succès
	 */
	public static function login( $data ){
		global $config;

		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Aucune donnée obligatoire n\'est fournie.', 'ERROR'), 1 );
		}

		if( !ria_array_key_exists(['sid', 'email', 'password'], $data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations nécessaires a votre connexion sont manquantes ou invalides.', 'ERROR'), 2 );
		}

		if( session_id()!=$data['sid'] ){
			throw new Exception( i18n::get('Pour pouvoir vous connecter, votre navigateur doit accepter les cookies. Ils sont indispensables pour maintenir votre connection de manière sécurisée.', 'ERROR'), 3);
		}

		if( !isset($data['email']) || !trim($data['email']) ){
			throw new Exception( i18n::get('Veuillez indiquer votre adresse email.', 'ERROR'), 4);
		}

		if( !isset($data['password']) || !trim($data['password']) ){
			throw new Exception( i18n::get('Veuillez indiquer votre mot de passe.', 'ERROR'), 1 );
		}

		if( !gu_users_login( $data['email'], $data['password'], true, Template::get('myaccount-login-profiles-access') ) ){
			throw new Exception( i18n::get('Login ou mot de passe incorrect', 'ERROR'), 1 );
		}

		// Restore le dernier panier du client
		gu_restore_cart( Template::get('myaccount-login-restore-card-days'), $config['wst_id'], [_STATE_BASKET] );

		return true;
	}

	/** Cette fonction permet de mettre à jour les informations d'un client.
	 *  @param array $data Obligatoire, information sur le client
	 *        - type : obligatoire, type de compte (1 = particulier, 2 = professionel)
	 *        - gender : obligatoire pour les particuliers, civilité (1 = Monsieur, 2 = Madame)
	 *        - firstname : obligatoire pour les particuliers, prénom
	 *        - lastname : obligatoire pour les particuliers, nom de famille
	 *        - society : obligatoire pour les professionels, nom de la société
	 *        - siret : obligatoire pour les professionels, numéro de siret
	 *        - address : obligatoire, adresse postal de facturation
	 *        - address2 : Optionnel, complément d'adresse de facturation
	 *        - zipcode : Obligatoire, code postal de l'adresse de facturation
	 *        - city : Obligatoire, ville de l'adresse de facturation
	 *        - country : Obligatoire, pays de facturation sous ce format (CODE_NOMDUPAYS, exemple FR_FRANCE)
	 *        - email : Obligatoire, adresse mail du client
	 *        - phone : Optionnel, numéro de téléphone fixe (fournir au moins 1 numéro de téléphone)
	 *        - mobile : Optionnel, numéro de téléphone mobile (fournir au moins 1 numéro de téléphone)
	 *
	 *  @return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updatePersonalInformation( $data ){
		global $config;

		$user = CustomerService::getInstance();
		if( !$user->isConnected() ){
			throw new Exception( i18n::get('Aucun client n\'est identifié.', 'ERROR'), 98 );
		}

		$data['phone'] = isset($data['phone']) ? preg_replace( '/[^0-9]/', '', $data['phone'] ) : '';
		$data['mobile'] = isset($data['mobile']) ? preg_replace( '/[^0-9]/', '', $data['mobile'] ) : '';

		if( !isset($data['type']) ){ $data['type'] = 1; }

		if( !isset($data['gender']) ){ $data['gender'] = ''; }
		if( !isset($data['firstname']) ){ $data['firstname'] = ''; }
		if( !isset($data['lastname']) ){ $data['lastname'] = ''; }

		if( !isset($data['society']) ){ $data['society'] = ''; }
		if( !isset($data['siret']) ){ $data['siret'] = ''; }

		$res_country = explode("_", $data['country']);

		// Vérifie que tous les champs demandés ont été envoyés
		if ($data['type'] == 1) {
			if( !isset($data['gender'], $data['firstname'], $data['lastname']) ){
				throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1 );
			}

			$data['society'] = $data['siret'] = '';
		}else{
			if (!isset($data['society'], $data['siret'])) {
				throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1 );
			}
		}

		if( !isset($data['country'], $data['email'], $data['phone'], $data['mobile']) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1 );
		}

		// Vérifie que les informations fournies sont valides
		if ($data['type'] == 1) {
			if( !trim($data['gender']) || !trim($data['firstname']) || !trim($data['lastname']) ){
				throw new Exception(i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.', 'ERROR'), 2 );
			}
		}else{
			if (!trim($data['society']) || !trim($data['siret'])) {
				throw new Exception(i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.', 'ERROR'), 2 );
			}
		}

		if( !trim($data['address']) || !trim($data['zipcode']) || !trim($data['city']) || !trim($data['country']) || !trim($data['email']) || !gu_valid_email($data['email']) ){
			throw new Exception(i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.', 'ERROR'), 2 );
		}

		if( !gu_users_check_email($data['email'], $user->getID()) ){
			throw new Exception( i18n::get('Cette adresse mail est déjà utilisée.', 'ERROR'), 3 );
		}

		if( trim($data['zipcode']) ==  '' || ($res_country[0] == 'FR' && !iszipcode($data['zipcode'])) ){
			throw new Exception( i18n::get('Le code postal saisi ne semble pas correct.', 'ERROR'), 4 );
		}

		if( trim($data['phone']) == '' && trim($data['mobile']) == '' ){
			throw new Exception( i18n::get('Veuillez renseigner un numéro de téléphone.', 'ERROR'), 5 );
		}

		if( $res_country[0] == 'FR' ){
			if( (!isphone($data['phone']) || strlen($data['phone']) != 10) && (!isphone($data['mobile']) || strlen($data['mobile']) != 10 || !in_array(substr($data['mobile'], 0, 2), array('06', '07'))) ){
				throw new Exception( i18n::get('Aucun numéro de téléphone semble valide.', 'ERROR'), 6 );
			}
		}

		// Mise à jour du profil
		if( !gu_users_set_profile( $user->getID(), ($data['type'] == 1 ? PRF_CUSTOMER : PRF_CUST_PRO)) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour de votre profil.'), 99 );
		}

		// Mise à jour de l'adresse de facturation
		$res = gu_adresses_update(
			$user->getID(), $user->getInvoiceID(), ($data['type'] == 1 ? 1 : 3), $data['gender'], $data['firstname'], $data['lastname'], $data['society'], $data['siret'], $data['address'], $data['address2'], $data['zipcode'],
			$data['city'], $res_country[1], $data['phone'], '', $data['mobile'], '', false, null, null, $res_country[0]
		);

		if( !$res || !gu_users_update_email($user->getID(), $data['email']) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour de vos informations personnelles.', 'ERROR'), 99 );
		}

		return true;
	}

	public function updateUserInformations($data){
		global $config;

		$user = CustomerService::getInstance();

		if( !$user->isConnected() ){
			throw new Exception( i18n::get('Aucun client n\'est identifié.', 'ERROR'), 95 );
		}

		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Aucune information n\'a pu être enregistrée.', 'ERROR'), 96);
		}
		$usr_id = $user->getID();
		$usr_inv_id = $user->getInvoiceID();
		$ar_data = [
			'adr_firstname',
			'adr_lastname',
			'adr_society',
			'adr_phone',
			'adr_mobile',
			'adr_phone_work',
			'adr_email',
			'adr_activity_area',
			'adr_function',
			'adr_nb_employees'
		];
		$ar_final = [];

		foreach($data as $kid => $val){
			$key = 'adr_'.$kid;
			if( !in_array($key, $ar_data) ){
				continue;
			}
			$ar_final[] = $key.'="'.addslashes($val).'"';

			if( $kid === 'email' ){
				if( !gu_users_update_email($usr_id, $val) ){
					throw new Exception( i18n::get('La mise à jour de l\'adresse email a échoué. Veuillez réessayer.', 'ERROR'), 98);
				}
			}
		}

		if( !count($ar_final) ){
			throw new Exception( i18n::get('Aucune information n\'a pu être enregistrée.', 'ERROR'), 96);
		}
		$ar_final[] = 'adr_date_modified=CURRENT_TIMESTAMP';

		$sql = 'update gu_adresses set ';
		$sql .= implode(', ', $ar_final);
		$sql .= ' where adr_tnt_id='.$config['tnt_id'].' and adr_usr_id='.$usr_id.' and adr_id='.$usr_inv_id;

		$res = ria_mysql_query($sql);

		if( !$res ){
			throw new Exception( i18n::get('Une erreur est survenue. Aucune information n\'a pu être enregistrée.', 'ERROR'), 97);

		}

		return $res;

	}

	/** Cette fonction permet de créer un compte client
	 * @param array $data Obligatoire, information sur le client
	 *        - type : obligatoire, type de compte (1 = particulier, 2 = entreprise)
	 *        - gender : obligatoire pour les particuliers, civilité (1 = Monsieur, 2 = Madame)
	 *        - firstname : obligatoire pour les particuliers, prénom
	 *        - lastname : obligatoire pour les particuliers, nom de famille
	 *        - company : obligatoire pour les professionels, nom de la société
	 *        - siret : obligatoire pour les professionels, numéro de siret
	 *        - address1 : obligatoire, adresse postal de facturation
	 *        - address2 : Optionnel, complément d'adresse de facturation
	 *        - zipcode : Obligatoire, code postal de l'adresse de facturation
	 *        - city : Obligatoire, ville de l'adresse de facturation
	 *        - country : Obligatoire, pays de facturation sous ce format (CODE_NOMDUPAYS, exemple FR_FRANCE)
	 *        - email : Obligatoire, adresse mail du client
	 * 		  - password1 : Obligatoire, mot de passe du compte
	 * 		  - password2 : Obligatoire, confirmation du mot de passe
	 *        - phone : Optionnel, numéro de téléphone fixe (fournir au moins 1 numéro de téléphone)
	 *        - mobile : Optionnel, numéro de téléphone mobile (fournir au moins 1 numéro de téléphone)
	 * 		  - policy : Obligatoire, acceptation de la politique de confidentialité
	 * @param	bool|array		$add_required Optionnel, Tableau des champs optionnels à vérifier (voir liste des champs optionnels sur le paramètre $data)
	 * @param	mixed			$pay Optionnel, Mode de paiement pour l'utilisateur, false s'il ne faut pas en activer, tableau de type de paiements, null activera ceux par défaut
	 * @param	bool			$login Optionnel, Teste la connexion au compte créé
	 * @return	int|Exception	Une exception sera levée en cas d'erreur, l'identifiant du compte en cas de succès
	 */
	public static function createAccount( $data, $add_required=false, $pay=false, $login=true ){
		global $config;

		$login = is_bool($login) ? $login : true;

		// Champs requis
		$fields_required = [
			'type',
			'address1',
			'zipcode',
			'city',
			'country',
			'email',
			'password1',
			'password2',
			'policy'
		];

		if( !is_array($data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1 );
		}

		$data['birthday'] = ria_array_get( $data, 'birthday', '' );
		$data['notes'] = ria_array_get( $data, 'notes', '' );
		$data['website'] = ria_array_get( $data, 'website', '' );

		// Ajout des champs requis
		if( is_array($add_required) ){
			$fields_required = array_unique( array_merge($fields_required, $add_required) );
		}
		$type = isset($data['type']) && in_array($data['type'], [2, 3, 4]) ? $data['type'] : 1;
		$prf = $type == 1 ? 2 : ($type == 2 ? 3 : $type);

		// Si particulier, on ajoute civilité, nom, prénom comme champs requis
		if( $type == 1 ){
			$fields_required = array_unique( array_merge($fields_required, ['firstname', 'lastname', 'gender']) );

		// Si professionnel, on ajoute société et siret comme champs requis
		}elseif( $type == 2 ){
			$fields_required = array_unique( array_merge($fields_required, ['company', 'siret']) );

		}

		foreach($fields_required as $field){
			if( !isset($data[$field]) ){
				throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1 );
			}
		}

		$res_country = explode("_", $data['country']);

		// Vérification du genre
		if( in_array('gender', $fields_required) ){
			if( !gu_titles_exists($data['gender']) ){
				throw new Exception(i18n::get('L\'information de civilité n\'est pas correct.', 'ERROR'), 2 );
			}
		}else{
			$data['gender'] = ria_array_get( $data, 'gender', '');
		}

		// Vérification du prénom
		if( in_array('firstname', $fields_required) ){
			if( !trim($data['firstname']) ){
				throw new Exception(i18n::get('Veuillez rentrer votre prénom.', 'ERROR'), 3 );
			}
		}else{
			$data['firstname'] = ria_array_get( $data, 'firstname', '');
		}

		// Vérification du nom
		if( in_array('lastname', $fields_required) ){
			if( !trim($data['lastname']) ){
				throw new Exception(i18n::get('Veuillez rentrer votre nom de famille.', 'ERROR'), 4 );
			}
		}else{
			$data['lastname'] = ria_array_get( $data, 'lastname', '');
		}

		// Vérification de la société
		if( in_array('company', $fields_required) ){
			if( !trim($data['company']) ){
				throw new Exception(i18n::get('Veuillez rentrer le nom de votre société.', 'ERROR'), 5 );
			}
		}else{
			$data['company'] = ria_array_get( $data, 'company', '');
		}

		// Vérification du SIRET
		if( in_array('siret', $fields_required) ){
			if( !validSIRET($data['siret']) ){
				throw new Exception(i18n::get('Veuillez vérifier le numéro SIRET de votre société.', 'ERROR'), 6 );
			}
		}else{
			$data['siret'] = ria_array_get( $data, 'siret', '');
		}

		// Vérification du n° de téléphone
		if( in_array('phone', $fields_required) ){
			if( trim($data['phone']) == '' ){
				throw new Exception( i18n::get('Veuillez renseigner un numéro de téléphone.', 'ERROR'), 5 );
			}

			if( $res_country[0] == 'FR' ){
				if( (!isphone($data['phone']) || strlen($data['phone']) != 10) ){
					throw new Exception( i18n::get('Le numéro de téléphone ne semble pas valide.', 'ERROR'), 6 );
				}
			}
		}else{
			$data['phone'] = ria_array_get( $data, 'phone', '');
		}

		// Vérification du n° de téléphone mobile
		if( in_array('mobile', $fields_required) ){
			if( trim($data['mobile']) == '' ){
				throw new Exception( i18n::get('Veuillez renseigner un numéro de téléphone mobile.', 'ERROR'), 5 );
			}

			if( $res_country[0] == 'FR' ){
				if( !isphone($data['mobile']) || strlen($data['mobile']) != 10 || !in_array(substr($data['mobile'], 0, 2), array('06', '07')) ){
					throw new Exception( i18n::get('Le numéro de téléphone mobile ne semble pas valide.', 'ERROR'), 6 );
				}
			}
		}else{
			$data['mobile'] = ria_array_get( $data, 'mobile', '');
		}

		if( !trim($data['address1']) || !trim($data['zipcode']) || !trim($data['city']) || !trim($data['country']) || !trim($data['email']) || !gu_valid_email($data['email']) ){
			throw new Exception(i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.', 'ERROR'), 9 );
		}

		if( !gu_users_check_email($data['email']) ){
			throw new Exception( i18n::get('Cette adresse mail est déjà utilisée.', 'ERROR'), 10 );
		}

		if( !gu_valid_password($data['password1']) ){
			throw new Exception( i18n::get('Le mot de passe que vous avez choisi ne satisfait pas les conditions requises.', 'ERROR'), 13 );
		}

		if( $data['password1'] !== $data['password2'] ){
			throw new Exception( i18n::get('Les mots de passe saisis sont différents. Veuillez les saisir de nouveau.', 'ERROR'), 14 );
		}

		if( trim($data['zipcode']) ==  '' || ($res_country[0] == 'FR' && !iszipcode($data['zipcode'])) ){
			throw new Exception( i18n::get('Le code postal saisi ne semble pas correct.', 'ERROR'), 11 );
		}

		if( !$data['policy'] ){
			throw new Exception( i18n::get('Vous devez accepter notre Politique de confidentialité pour pouvoir continuer.', 'ERROR'), 15 );

		}

		if( !($usr = gu_users_add($data['email'], $data['password1'], $prf)) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
		}

		$data['address2'] = !isset($data['address2']) ? '' : $data['address2'];

		if( $pay === null ){
			$pay = $config['default_usr_payments'];
		}

		if( !($adr = gu_adresses_add($usr, $type, $data['gender'], ucwords($data['firstname']), ucwords($data['lastname']), $data['company'], $data['siret'], $data['address1'], $data['address2'], $data['zipcode'], $data['city'], $res_country[1], $data['phone'], '', $data['mobile'], '', '', $data['email'], $res_country[0])) ){
			gu_users_del($usr);
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
		}

		if( !gu_users_address_set($usr, $adr) ){
			gu_users_del($usr);
			gu_adresses_del($adr);
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
		}

		if( $login ){
			if( !gu_users_login($data['email'], $data['password1'], true) ){
				gu_users_del($usr);
				gu_adresses_del($adr);
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
			}
		}

		if( is_array($pay) && count($pay) ){
			foreach($pay as $pay_type){
				gu_users_payment_types_add($usr, $pay_type, 0, 0, 0);

			}
		}

		if( isdate($data['birthday']) ){
			if( !gu_users_set_date_of_birth($usr, $data['birthday']) ){
				gu_users_del($usr);
				gu_adresses_del($adr);
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
			}
		}

		if( trim($data['website']) != '' ){
			if( !gu_users_set_website($usr, $data['website']) ){
				gu_users_del($usr);
				gu_adresses_del($adr);
				throw new Exception( '...'.i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
			}
		}

		if( trim($data['notes']) != '' ){
			if( !fld_object_notes_add(CLS_USER, $usr, 'Commentaire fiche client', $data['notes'], null) ){
				gu_users_del($usr);
				gu_adresses_del($adr);
				throw new Exception( '----'.i18n::get('Une erreur inattendue s\'est produite lors de la création de votre compte. Veuillez réessayer.', 'ERROR'), 16 );
			}
		}

		// Gestion des notifications personnalisées
		if (isset($config['active_email_perso']) && $config['active_email_perso']) {
			$file_notify_exists = false;

			$file_emails_perso = $config['site_dir'].'/include/view.emails.inc.php';
			if (file_exists($file_emails_perso)) {
				$file_notify_exists = true;
			} else {
				$file_emails_perso = $config['site_dir'].'/../include/view.emails.inc.php';

				if (file_exists($file_emails_perso)) {
					$file_notify_exists = true;
				}
			}

			if ($file_notify_exists) {
				$config_copy = $config;

				require_once( $file_emails_perso );

				if (function_exists('riashop_users_notify_new')) {
					$notify_invoice = riashop_users_notify_new($usr);
					$config = $config_copy;
					return $notify_invoice;
				}
			}
		}

		return $usr;

	}

	/** Cette fonction permet de mettre à jour/ supprimer le magasin associé à un compte client
	 * @param	int|null	$usr_id Obligatoire, Identifiant du compte client, null pour récupérer l'utilisateur en cours
	 * @param	int|bool	$str	Optionnel, Identifiant d'un magasin ou false pour supprimer le magasin du client
	 * @return	bool		Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateStore( $usr_id, $str=0 ){
		global $config;

		if( !is_numeric($usr_id) || $usr_id <= 0 || !gu_users_exists($usr_id) ){
			$user = CustomerService::getInstance();

			if( !$user->isConnected() ){
				throw new Exception( i18n::get('Aucun client n\'est identifié.', 'ERROR'), 95 );
			}
			$usr_id = $user->getID();
		}

		if( !is_numeric($usr_id) || $usr_id <= 0 ){
			throw new Exception( i18n::get('Aucun client n\'est identifié.', 'ERROR'), 95 );
		}

		$is_str_id = is_numeric($str) && $str && dlv_stores_exists($str);
		$is_del = is_bool($str) && !$str;

		if( !$is_str_id && !$is_del ){
			throw new Exception( i18n::get('La mise à jour de votre magasin n\'a pas pu s\'effectuer.', 'ERROR'), 2 );
		}

		// Mise à jour du choix du magasin
		if( !gu_users_set_store_choosed($usr_id, ($str ? $str : 0)) ){
			if( !$is_del ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors du rattachement de votre compte au magasin sélectionné.', 'ERROR'), 3 );
			}

			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors du détachement de votre compte au magasin.', 'ERROR'), 3 );
		}

		return true;
	}

	/** Cette fonction permet d'envoyer le mail de réinitialisation de mot de passe.
	 * 	@param array $data Obligatoire, informations permettant l'envoi
	 * 					- sid : identifiant de session
	 * 					- email : adresse mail renseigné
	 * 	@return bool Une exception est levée en cas d'erreur, true en cas de succès
	 */
	public static function sendLostPassword( $data ){
		if( !isset($data['sid'],$data['email']) ){
			throw new Exception(i18n::get('Une ou plusieurs informations nécessaires a votre connexion sont manquantes ou invalides.', 'ERROR'), 1);
		}

		if( session_id()!=$data['sid'] ){
			throw new Exception( i18n::get('Pour pouvoir réinitialiser votre mot de passe, votre navigateur doit accepter les cookies. Ils sont indispensables pour maintenir votre connection de manière sécurisée.', 'ERROR'), 2 );
		}

		if( !trim($data['email']) ){
			throw new Exception( i18n::get('Veuillez indiquer votre adresse email.', 'ERROR'), 3);
		}

		if( !filter_var($data['email'], FILTER_VALIDATE_EMAIL) ){
			throw new Exception( i18n::get('Veuillez indiquer une adresse email valide.', 'ERROR'), 4 );
		}

		// Envoi du message de récupération de mot de passe.
		if( !gu_users_send_lostpassword( $data['email'], 0, false, true ) ){
			throw new Exception(str_replace('#param[adresse mail]#', $data['email'], i18n::get('Nous n\'avons trouvé aucun compte client enregistré avec l\'adresse email "#param[adresse mail]#". Pour plus d\'informations, n\'hésitez pas à nous contacter.', 'ERROR')), 5);
		}

		return true;
	}

	/** Cette fonction permet de contrôler l'accès à la page de réinitialisatoin de mot de passe.
	 * 	Elle s'appuit sur la présente du token de réinitialisation dans $_GET['p'], doit être une chaîne de 32 caractère.
	 * 	Elle renverra automatiquement vers la page de connexion si l'accès n'est pas autorisé.
	 * 	@return empty Aucun retour
	 */
	public static function controlReinitPassword(){
		if( !isset($_GET['p']) || strlen(trim($_GET['p'])) !== 32 ){
			header('Location: '.Template::getURL('login'));
			exit;
		}
		// Vérifie le token fournit
		$r_user = gu_users_get( 0, '', '', 0, trim($_GET['p']) );
		if( !$r_user || ria_mysql_num_rows($r_user) !=1 ){
			header('Location: '.Template::getURL('login'));
			exit;
		}
	}

	/** Cette fonction permet de réinitialiser le mot de passe de son compte client.
	 * 	@param array $data Obligatoire, information permettant la réinitialisation
	 * 					- sid : identifiant de session
	 * 					- token : token de réinitialisation
	 * 					- email : adresse mail du compte
	 * 					- password : nouveau mot de passe
	 * 					- password2 : confirmation du nouveau mot de passe
	 * 	@return bool Une exception est levée en cas d'erreur, true en cas de succès
	 */
	public static function reinitPassword( $data ){
		global $config;

		if( !isset($data['token'], $data['email'], $data['password'], $data['password2']) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées. Veuillez vérifier. Les champs marqués d\'une * sont obligatoires.', 'ERROR'), 1);
		}

		if( !trim($data['token']) || !trim($data['email']) || !trim($data['password']) || !trim($data['password2']) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées. Veuillez vérifier. Les champs marqués d\'une * sont obligatoires.', 'ERROR'), 2);
		}

		if( $data['password']!=$data['password2'] ){
			throw new Exception( i18n::get('Les deux valeurs saisies pour votre nouveau mot de passe sont différentes. Veuillez les saisir de nouveau.', 'ERROR'), 3);
		}

		$r_user = gu_users_get( 0, $data['email'],'',0,$data['token'] );
		if( !$r_user || ria_mysql_num_rows($r_user) != 1 ){
			throw new Exception( i18n::get('L\'adresse email saisie ne correspond pas à l\'adresse email enregistrée pour votre compte.', 'ERROR'), 4);
		}

		$user = ria_mysql_fetch_assoc( $r_user );

		if( !gu_users_update_password($user['id'], $data['password']) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de l\'enregistrement de votre nouveau mot de passe.', 'ERROR'), 99);
		}

		// loggue l'utilisateur avec son nouveau mot de passe
		if( !gu_users_login( $data['email'], $data['password'], true, Template::get('myaccount-login-profiles-access') ) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de votre connexion avec vos nouveaux identifiants.', 'ERROR'), 99);
		}

		return true;
	}

	/** Cette fonction permet de gérer l'envoi d'une demande d'ouverture de compte
	 * 	@param array $data Obligatoire, informations présentes dans le mail qui sera envoyé
	 * 	@param $schema Obligatoire, schema des données
	 * 	@param $group Optionne, groupe permettant de découper les informations dans le mail
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function sendSignUp( $data, $schema, $group=[] ){
		global $config;
		$http_host_ria = 'riashop-'.$_SERVER['HTTP_HOST'];
		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Un ou plusieurs paramètres obligatoires sont manquants.', 'ERROR'), 1 );
		}

		if( !is_array($schema) || !count($schema) ){
			throw new Exception( i18n::get('La demande d\'ouverture n\'a pas pu aboutir.', 'ERROR'), 2 );
		}

		if( count($group) ){
			foreach( $group as $one_group ){
				// Contrôle que le groupe est correctement paramétré
				if( !ria_array_key_exists(['name', 'fields'], $one_group) || !is_array($one_group['fields']) || !count($one_group['fields']) ){
					throw new Exception( i18n::get('La demande d\'ouverture n\'a pas pu aboutir.', 'ERROR'), 2 );
				}
			}
		}

		// Contrôle des données en fonction du schéma
		foreach( $schema as $key=>$one_schema ){
			if( !ria_array_key_exists(['name', 'mandatory', 'type'], $one_schema) ){
				throw new Exception( i18n::get('Un ou plusieurs paramètres obligatoires sont manquants.'), 1 );
			}

			if( $one_schema['mandatory'] && (!array_key_exists( $key, $data) || trim($data[$key]) == '') ){
				throw new Exception( i18n::get('Un ou plusieurs paramètres obligatoires sont manquants.'), 1 );
			}

			// Contrôle de chaque données en fonction du type défini dans le schéma
			switch( $one_schema['type'] ){
				case 'int':
					if( !is_numeric($data[$key]) ){
						throw new Exception( i18n::get('Un ou plusieurs paramètres obligatoires sont manquants.'), 1 );
					}
				break;
				case 'email':
					if( trim($data[$key]) != '' && !isemail($data[$key]) ){
						throw new Exception( i18n::get('L\'adresse mail est invalide.'), 1 );
					}
				break;
				case 'phone':
					if( trim($data[$key]) != '' && !isphone($data[$key]) ){
						throw new Exception( i18n::get('Le numéro de téléphone.'), 1 );
					}
				break;
				case 'siret':
					if( !validSIRET($data[$key], true) ){
						throw new Exception( i18n::get('Le siret est invalide.', 'ERROR'), 3);
					}
				break;
				case 'naf':
					$res = preg_match('/^[0-9]{4}[a-zA-Z]{1}/', $data[$key]);
					if( $res === 0 ){
						throw new Exception( i18n::get('Le code NAF est invalide.', 'ERROR'), 3);
					}
				break;
				case 'tvaintra':
					$data[$key] = trim($data[$key]);
					$data[$key] = str_replace( ' ', '', $data[$key] );

					$res = preg_match('/^[a-zA-Z]{2}[0-9]{10}/', $data[$key]);
					if( $res === 0 ){
						throw new Exception( i18n::get('La TVA intracommunautaire est invalide.', 'ERROR'), 3);
					}
				break;
			}
		}

		// Recherche la configuration mail défini pour l'envoi des demandes d'ouverture de compte
		$r_cfg = cfg_emails_get( 'dem_compte', $config['wst_id'] );
		if( !$r_cfg || !ria_mysql_num_rows($r_cfg) ){
			throw new Exception( i18n::get('La demande d\'ouverture n\'a pas pu aboutir.', 'ERROR'), 2 );
		}

		// Contrôle que le destinataire principal a bien été configuré
		$cfg = ria_mysql_fetch_assoc( $r_cfg );
		if( trim($cfg['to']) == '' ){
			throw new Exception( i18n::get('La demande d\'ouverture n\'a pas pu aboutir.', 'ERROR'), 2 );
		}

		// Déclenche l'envoi du mail
		$email = new Email();
		$email->setSubject( 'Demande d\'ouverture de compte' );
		$email->setFrom('<EMAIL>');
		$email->addTo( $cfg['to'] );

		if( trim($cfg['cc']) != '' ){
			$email->addCC( $cfg['cc'] );
		}

		if( trim($cfg['bcc']) != '' ){
			$email->addBcc( $cfg['bcc'] );
		}

		if( trim($cfg['reply-to']) != '' ){
			$email->setReplyTo( $cfg['reply-to'] );
		}

		// Gestion des notifications personnalisées
		if (isset($config['active_email_perso']) && $config['active_email_perso']) {
			$file_notify_exists = false;

			$file_emails_perso = $config['site_dir'].'/include/view.emails.inc.php';
			if (file_exists($file_emails_perso)) {
				$file_notify_exists = true;
			} else {
				$file_emails_perso = $config['site_dir'].'/../include/view.emails.inc.php';

				if (file_exists($file_emails_perso)) {
					$file_notify_exists = true;
				}
			}

			if ($file_notify_exists) {
				require_once($file_emails_perso);

				if (function_exists('riashop_signup_notify')) {
					$notify_invoice = riashop_signup_notify($email, $data, $schema, $group);
					$config = $config_copy;
					return $notify_invoice;
				}
			}
		}

		$html = '';

		// Prépare le mail
		if( count($group) ){
			foreach( $group as $one_group ){
				$html .= '
					<p style="margin-top: 30px;margin-bottom: 5px;font-weight:bold;font-size:1.3em">'.htmlspecialchars( $one_group['name'] ).'</p>
					<ul>
				';

				foreach( $one_group['fields'] as $field ){
					if( array_key_exists($field, $schema) && array_key_exists($field, $data) ){
						$html .= '<li>
							<span  style="font-size:1.1em">'.htmlspecialchars( $schema[$field]['name'] ).'</span> :
							'.htmlspecialchars( $data[$field] ).'
						</li>';

						// Supprime la donnée du schéma
						unset($schema[$field]);
					}
				}

				$html .= '</ul>';
			}
		}

		// S'il reste quelque chose dans le schém
		if( count($schema) ){
			if( count($group) ){
				// Tout sera dans un groupe "Autre" (si un envoi par groupe est demandé)
				$html .= '<p style="margin-top: 30px;margin-bottom: 5px;font-weight:bold;font-size:1.3em">'.( count($schema) > 1 ? 'Autres' : 'Autre' ).'</p>';
			}

			$html .= '<ul>';
			foreach( $schema as $field=>$one_schema ){
				$html .= '<li>
					<span  style="font-style:1.1em">'.htmlspecialchars( $one_schema['name'] ).'</span> :
					'.htmlspecialchars( $data[$field] ).'
				</li>';
			}
			$html .= '</ul>';
		}

		$html .= '<p style="margin-top: 30px;margin-bottom: 5px;font-weight:bold;font-size:1.3em">Documents</p>';
		$html .= '<ul>';
		$html .= '<li><span  style="font-style:1.1em">RIB (PDF)</span> : pièce jointe</li>';
		$html .= '<li><span  style="font-style:1.1em">Extrait Kbis (PDF)</span> : pièce jointe</li>';
		$html .= '</ul>';

		$html .= '<p style="margin-top: 30px;">Ces coordonnées vous permettent de prendre contact pour valider ou non cette demande d’ouverture de compte.
			Si la demande est validée, un compte client devra être créé depuis <a href="https://'.$http_host_ria.'/admin/customers/new.php?prf=">
			RiaShop > Comptes > Ajouter un compte</a> (aucun email automatique n’est envoyé suite à la création d’un compte client).</p>';

		$email->addHtml( $config['email_html_header'] );
		$email->addParagraph('Une demande d\'ouverture de compte a été émise, voici les coordonnées de la personne');
		$email->addHtml( $html );
		$email->addHtml( $config['email_html_footer']);

		if( isset($_FILES) && is_array($_FILES) && count($_FILES) ){
			foreach( $_FILES as $key_file=>$one_file ){
				$email->addAttachment( $one_file['tmp_name'], $one_file['name'] );
			}
		}

		if( !$email->send() ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de l\'envoie de votre demande d\'ouverture de compte.', 'ERROR'), 99);
		}

		return true;
	}

	/** Cette fonction permet de mettre à jour le mot de passe.
	 * 	@param array $data Obligatoire, informations permettant de mettre à jour le mot de passe
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updatePassword( $data ){
		global $config;

		$user = CustomerService::getInstance();
		if( !$user->isConnected() ){
			throw new Exception( i18n::get('Aucun client n\'est identifié.', 'ERROR'), 98 );
		}

		if( !isset($data['sid']) ){
			throw new Exception( i18n::get("Une ou plusieurs informations obligatoires ne sont pas renseignées.
				 Veuillez vérifier. Les champs marqués d'une * sont obligatoires.", 'ERROR'), 1);
		}

		if( session_id()!=$data['sid'] ){
			throw new Exception( i18n::get('Pour pouvoir mettre à jour votre mot de passe, votre navigateur doit accepter les cookies.
				Ils sont indispensables pour maintenir votre connection de manière sécurisée.', 'ERROR'), 2);
		}

		if( !isset($data['email'], $data['oldpassword'], $data['newpassword'], $data['confirmpassword']) ){
			throw new Exception( i18n::get("Une ou plusieurs informations obligatoires ne sont pas renseignées.
				 Veuillez vérifier.", 'ERROR'), 1);
		}

		$r_ctrl_password = gu_users_get( 0,$data['email'], $data['oldpassword'] );
		if( !$r_ctrl_password || !ria_mysql_num_rows($r_ctrl_password) ){
			throw new Exception( i18n::get("La valeur saisie dans le champ « Mot de passe actuel » n'est pas correcte.
				 Veuillez essayer de nouveau.", 'ERROR'), 3);
		}

		if( !trim($data['oldpassword']) || !trim($data['newpassword']) || !trim($data['confirmpassword']) ){
			throw new Exception( i18n::get("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.
				 Veuillez vérifier. Les champs marqués d'une * sont obligatoires.", 'ERROR'), 4);
		}

		if( $data['newpassword'] != $data['confirmpassword'] ){
			throw new Exception( i18n::get("Les deux valeurs saisies pour votre nouveau mot de passe diffèrent.
				 Veuillez les saisir de nouveau.", 'ERROR'), 5);
		}

		if( !gu_users_update_password($user->getID(), $data['newpassword']) ){
			throw new Exception( i18n::get("Une erreur inattendue s'est produite lors de l'enregistrement de votre nouveau mot de passe.
				 Veuillez réessayer ou prendre contact avec l'administrateur", 'ERROR'), 99);
		}else{
			if( isset($_COOKIE['remember']) ){
				gu_users_set_remember_cookie( $user->getID() );
			}
		}

		return true;
	}

	/** Cette fonction permet de mettre à jour les notifications mails envoyées suite au changement de statut d'une commande.
	 * 	@param array $data Obligatoire, tableau contenant les identifiants des status choisi (peut être vide = désactive toutes les notifications.)
	 * 	@return Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateAlerts( $data, $copy=false ){
		$user = CustomerService::getInstance();
		if( !$user->isConnected() ){
			throw new Exception( i18n::get('Aucun client n\'est identifié.', 'ERROR'), 98 );
		}

		if( !is_array($data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1);
		}

		if( count($data) ){
			$data = control_array_integer( $data, true );
			if( $data === false  ){
				throw new Exception( i18n::get('Une ou plusieurs informations obligatoires ne sont pas renseignées.', 'ERROR'), 1);
			}
		}

		// Mise à jour des status
		if( !gu_ord_alerts_update($user->getID(), $data) ){
			throw new Exception( i18n::get("Une erreur inattendue s'est produite lors de l'enregistrement de vos alertes emails.", 'ERROR'), 99);
		}

		// Mise à jour de l'adresse mail en copy
		if( $copy !== false ){
			if( !gu_users_set_alert_cc($user->getID(), $copy) ){
				throw new Exception( i18n::get("Une erreur inattendue s'est produite lors de l'enregistrement de l'adresse mail en copie.", 'ERROR'), 99);
			}
		}

		return true;
	}

	/** Cette fonction permet de mettre à jour le mode d'affichage des tarifs (en HT ou en TTC).
	 * 	@param string $mode Obligatoire, le mode choisi (valeurs acceptées : 'ht' ou 'ttc')
	 * 	@return Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateDisplayPrices( $mode ){
		if( !in_array($mode, ['ht', 'ttc']) ){
			throw new Exception( i18n::get('Le mode d\'affichage des tarifs n\'est pas reconnu.', 'ERROR'), 7);
		}

		$user = CustomerService::getInstance();
		if( !$user->isConnected() ){
			throw new Exception( i18n::get('Aucun client n\'est identifié.', 'ERROR'), 98 );
		}

		if( !gu_users_set_display_prices($user->getID(), ($mode == 'ht')) ){
			throw new Exception( i18n::get("Une erreur inattendue s'est produite lors de l'enregistrement du mode d\'affichage des tarifs.", 'ERROR'), 99);
		}

		$_SESSION['usr_display_prices'] = $mode;
		return true;
	}

	/** Cette fonction permet de déclencher le consulter en tant que.
	 * 	Le dernier panier en cours est restauré.
	 * 	@param array $data Obligatoire, tableau contenant les informations permettant d'initialiser le consulter en tant que
	 * 					- usr : identifiant du compte que l'on souhaite consulter
	 * 	@return empty
	 */
	public static function consultAs( $data ){
		global $config;

		// Activation ou désactivation du consulter en tant que
		if( isset($data['usr']) && is_numeric($data['usr']) && $data['usr'] > 0 ){
			// Active le consulter en tant que
			$_SESSION['admin_view_user'] = $data['usr'];
		}elseif( isset($_SESSION['admin_view_user']) ){
			// Désactive le consulter en tant que
			unset( $_SESSION['admin_view_user'] );
		}

		// Restore le dernier panier en cours
		ord_restore_last_cart( (isset($_SESSION['admin_view_user']) ? $_SESSION['admin_view_user'] : 0), $config['wst_id'] );
	}

	/**	Cette méthode permet l'enregistrement de champs avancés, il est possible d'enregistrer plusieurs champs en une fois
	 * @param	array	$fld	Obligatoire, tableau ID CHAMP => Valeur
	 * @return	bool|Exception	Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function setField($fld){
		$user = CustomerService::getInstance();

		if( !$user->isConnected() ){
			throw new Exception( i18n::get('Aucun client n\'est identifié.', 'ERROR'), 98 );
		}

		if( !is_array($fld) || !count($fld) ){
			throw new Exception(i18n::get('Aucune information n\'a pas pu être enregistrée.', 'ERROR'));
		}
		$data = [];

		foreach($fld as $fkey => $fval){

			if( !fld_fields_exists($fkey) ){
				continue;
			}
			$data[] = [
				'value'		=> $fval,
				'cls_id'	=> CLS_USER,
				'fld_id'	=> $fkey,
				'obj_0'		=> $user->getID()
			];
		}

		if( !count($data) ){
			throw new Exception(i18n::get('Aucune information n\'a pas pu être enregistrée.', 'ERROR'));
		}

		if( !fld_object_values_set_multiple($data) ){
			throw new Exception(i18n::get('Aucune information n\'a pas pu être enregistrée.', 'ERROR'));
		}
		return true;
	}
}