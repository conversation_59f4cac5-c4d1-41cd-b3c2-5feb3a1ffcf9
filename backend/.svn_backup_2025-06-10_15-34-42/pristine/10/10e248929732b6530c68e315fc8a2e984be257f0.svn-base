<?php

/// \cond onlyria
/**
 * Actuellement ce fichier permet la syncrhonisation complete de la table "prd_product_positions".
 * Il est possible dans le futur d'avoir d'autre type de donnée qui pourrais passer ici sur le même modèle
 * 
 * Riashop génére un genre de dump des données zip pour que les traitements côté table soit plus rapide que sur la version normal
 *		\code
 *			GET /devices/sync_all/
 *		\endcode
 * 
 * @return zip Le fichier zip généré comprend un fichier texte avec le contenu d'une table en sql sans notion de date de modification 
 *		- prd_product_positions.sql : fichier avec les requetes sql pour l'insertion des positions de produits.
 *
 * Yuto lance périodiquement la tache de synchronisation des positions de produits (toutes les 24 heures). 
 * Yuto supprime toutes les données de la table avant d'executer l'insertion de nouvelle valeur donnée par ce fichier.
 *
 * L'execution de cette tache par Yuto depend de la configuration "prd_product_positions_lvl".
 * La config "prd_product_positions_lvl" doit être supérieur à 0.
 */
switch( $method ){

	// récupère les éléments des restrictions
	case 'get':

		if(!isset($_REQUEST['table']) || !in_array($_REQUEST['table'], array('prd_product_positions')) ){
			exit;
		}

		$zip_url = dirname(__FILE__).'/../upload-temp/table-'.$_REQUEST['table'].'-'.$config['tnt_id'].'-'.$config['dev_id'].'.zip';
		if( !file_exists($zip_url) ){
			$zip = new ZipArchive();
			if($zip->open($zip_url, ZIPARCHIVE::CREATE) == TRUE){

				$pas = 500; 
				$query_separator = '_$$$_';
				$sql_header = array();
				$sql_datas = array();

				// les tablettes avec android 4.0 ( version 15 ) ne supporte pas les insert multiples
				if( $config['dev_os_version'] < 16 && $config['dev_brand']!='Apple' ){
					$pas = 1;
				}

				$sql_final = "";

				switch($_REQUEST['table']) {
					case 'prd_product_positions' : 
						$sql_header = "insert into prd_product_positions (ppp_cat_id, ppp_prd_id, ppp_pos) values ";
						$rpos = prd_product_positions_get( $config['wst_id'] );
						if( $rpos ){
							while( $pos = ria_mysql_fetch_assoc($rpos) ){
								$sql_datas[] = '('.$pos['cat_id'].','.$pos['prd_id'].','.$pos['pos'].')';
							}
						}
						break;
					default : 
						exit;
				}

				$sql_final = '';
				$cpt = 0;
				foreach($sql_datas as $d){
					if( $cpt === 0  || $cpt % $pas === 0 ){
						$sql_final .= ($cpt > 0 ? $query_separator : '').$sql_header;
					}else if( $cpt > 0 ){
						$sql_final .= ',';
					}
					$cpt ++; 

					$sql_final .= $d;
				}

				$zip->addFromString($_REQUEST['table'].'.sql',  $sql_final);
				$zip->close(); 
			}else{
				exit;
			}
		}

		// envoi le zip 
		header("Content-type: application/octet-stream"); 
		header("Content-disposition: attachment; filename=data.zip");  
		ob_clean();
		flush();
		readfile($zip_url);
		@unlink($zip_url);
		exit;

		break;
}

///@}
// \endcond