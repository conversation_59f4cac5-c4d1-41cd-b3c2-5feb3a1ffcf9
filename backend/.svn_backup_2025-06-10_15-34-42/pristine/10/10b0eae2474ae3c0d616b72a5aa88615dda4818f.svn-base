<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  5712 => 'Bogotá',
  5713 => 'Bogotá',
  5714 => 'Bogotá',
  5715 => 'Bogotá',
  5716 => 'Bogotá',
  5717 => 'Bogotá',
  571820 => 'Madrid',
  571821 => 'Funza',
  571822 => 'Funza',
  5718230 => 'Subachoque',
  5718232 => 'Funza',
  5718240 => 'El Rosal',
  5718241 => 'El Rosal',
  57182420 => 'La Pradera',
  57182428 => 'Subachoque',
  57182429 => 'Subachique',
  5718243 => 'Bojaca',
  5718245 => 'Subachoque',
  5718246 => 'Puente Piedra',
  5718247 => 'La Punta',
  5718249 => 'Zipacon',
  5718250 => 'Madrid',
  5718251 => 'Madrid',
  5718252 => 'Madrid',
  5718253 => 'Madrid',
  5718254 => 'Madrid',
  5718255 => 'Madrid',
  5718256 => 'Madrid',
  5718257 => 'Funza',
  571826 => 'Funza',
  571827 => 'Mosquera',
  5718283 => 'Mosquera',
  5718288 => 'Madrid',
  5718289 => 'Madrid',
  571830 => 'Girardot',
  571831 => 'Girardot',
  571832 => 'Girardot',
  571833 => 'Girardot',
  5718370 => 'Jerusalén',
  5718371 => 'Guataqui',
  5718373 => 'Beltrán',
  5718375 => 'Nariño',
  5718376 => 'Tocaima',
  5718381 => 'Agua de Dios',
  5718383 => 'Nilo',
  5718384 => 'Viota',
  5718385 => 'Nariño',
  5718386 => 'Apulo',
  57183925 => 'Nilo',
  57183926 => 'Nilo',
  57183927 => 'Nilo',
  57183928 => 'Nilo',
  57183929 => 'La Esmeralda',
  5718393 => 'Girardot',
  5718397 => 'Apulo',
  5718398 => 'Apulo',
  5718402 => 'San Antonio de Tequendama',
  5718403 => 'Choachi',
  5718404 => 'Fomeque',
  5718412 => 'Santa Inés',
  5718416 => 'Guaduas',
  5718417 => 'Guaduas',
  5718419 => 'Pandi',
  571842 => 'Facatativa',
  5718430 => 'Facatativa',
  5718431 => 'Facatativa',
  57184330 => 'Ninaima',
  57184331 => 'Ninaima',
  57184332 => 'Ninaima',
  57184333 => 'Tobia',
  57184334 => 'Tobia',
  5718434 => 'Cartagenita',
  5718435 => 'Cartagenita',
  5718436 => 'Facatativa',
  5718437 => 'Facatativa',
  5718438 => 'Facatativa',
  5718439 => 'Facatativa',
  5718440 => 'Facatativa',
  5718441 => 'Viani',
  5718442 => 'Cachipay',
  5718443 => 'Cachipay',
  5718444 => 'Villeta',
  5718445 => 'Villeta',
  5718446 => 'Villeta',
  5718447 => 'Villeta',
  5718449 => 'La Peña',
  5718450 => 'San Antonio de Tequendama',
  5718451 => 'Nocaima',
  571845340 => 'La Florida',
  571845341 => 'La Florida',
  571845342 => 'La Florida',
  571845343 => 'La Florida',
  571845344 => 'La Florida',
  571845345 => 'La Florida',
  5718480 => 'Quebradanegra',
  5718481 => 'Quebradanegra',
  5718482 => 'La Magdalena',
  57230 => 'Cali',
  57231 => 'Cali',
  57232 => 'Cali',
  57233 => 'Cali',
  57234 => 'Cali',
  57235 => 'Cali',
  57236 => 'Cali',
  57272 => 'Pasto',
  57273 => 'Pasto',
  57288 => 'Cali',
  57289 => 'Cali',
  57290 => 'Cali',
  57292 => 'Cali',
  5742 => 'Medellín',
  5743 => 'Medellín',
  5744 => 'Medellín',
  5745 => 'Medellín',
  574842 => 'Medellín',
  5748510 => 'Medellín',
  5748511 => 'Medellín',
  5748720 => 'Medellín',
  5748721 => 'Medellín',
  5748722 => 'Medellín',
  5748723 => 'Medellín',
  5748724 => 'Medellín',
  5748725 => 'Medellín',
  5748726 => 'Medellín',
  5749092 => 'Medellín',
  574911 => 'Medellín',
  574913 => 'Medellín',
  574917 => 'Medellín',
  57492 => 'Medellín',
  57532 => 'Barranquilla',
  57533 => 'Barranquilla',
  57534 => 'Barranquilla',
  57535 => 'Barranquilla',
  57536 => 'Barranquilla',
  57537 => 'Barranquilla',
  57538 => 'Barranquilla',
  57557 => 'Valledupar',
  57562951 => 'Cartagena',
  57562956 => 'Cartagena',
  57562957 => 'Cartagena',
  57562958 => 'Cartagena',
  57562959 => 'Cartagena',
  57565 => 'Cartagena',
  57566 => 'Cartagena',
  57567 => 'Cartagena',
  57568 => 'Cartagena',
  57631 => 'Pereira',
  57632 => 'Pereira',
  57633 => 'Pereira',
  57634 => 'Pereira',
  57635 => 'Pereira',
  57687 => 'Manizales',
  57688 => 'Manizales',
  57689 => 'Manizales',
  57757 => 'Cucuta',
  57758 => 'Cucuta',
  57761 => 'Bucaramanga',
  57763 => 'Bucaramanga',
  57764 => 'Bucaramanga',
  57765 => 'Bucaramanga',
  57767 => 'Bucaramanga',
  57768 => 'Bucaramanga',
  57790 => 'Bucaramanga',
  57826 => 'Ibague',
  57827 => 'Ibague',
  57866 => 'Villavicencio',
  57886 => 'Neiva',
  57887 => 'Neiva',
);
