<?php

use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_list_sections;

if (!isset($tab_view, $list)) {
	header('HTTP/1.0 403 Forbidden');
	exit;
}

$r_section = prw_followed_list_sections::get();
$is_published = ria_array_get($list, 'is_published' , 0) == '1';

function GeneralProductTable($id, $name) {
	ob_start();
	?>
		<table id="<?php print $id; ?>" class="pmt-rules ui-sortable" cellpadding="0" cellspacing="0">
			<thead>
				<tr>
					<th>
						<input class="checkbox" onclick="checkAllClick(this);" name="checkall" type="checkbox">
					</th>
					<th><?php print _('Rang'); ?></th>
					<th><?php print _($name); ?></th>
					<th class="align-right"><?php print _('PMC')?></th>
					<th></th>
				</tr>
			</thead>
			<tfoot>
				<tr>
					<td colspan="5">
						<input
							type="submit"
							value="<?php echo _('Supprimer du catalogue')?>"
							name="delete"
							class="button float-left">
						<input
							type="submit"
							value="<?php echo _('Ajout rapide')?>"
							name="fast-add"
							class="button">
						<input
							type="submit"
							value="<?php echo _('Ajout par produit')?>"
							name="prd-add"
							class="button">
					</td>
				</tr>
			</tfoot>
			<tbody>
				<tr class="template" class="none">
					<td class="td-pmt-rules-1 valign-center">
						<input name="del[]" class="checkbox" type="checkbox" value="">
					</td>
					<td class="td-pmt-rules-2 rank valign-center"></td>
					<td class="td-pmt-rules-3 valign-center">
						<a class="url" href=""></a>
					</td>
					<td class="td-pmt-rules-4 align-right valign-center">
						<input type="text" class="pmc">
					</td>
					<td class="td-pmt-rules-5 align-center ria-cell-move valign-center">
						<div class="ria-row-catchable ui-sortable-handle" title="<?php print _('Déplacer'); ?>"></div>
					</td>
				</tr>
				<tr class="no-info">
					<td colspan="5"><?php echo _('Aucun produit sélectionné')?></td>
				</tr>
			</tbody>
		</table>
	<?php
	return ob_get_clean();
}
?>

<form method="post">
	<input type="hidden" name="id" id="id" value="<?php echo $list['id']?>">
	<table id="table-gestion-releves-infos-generales">
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" id="del" name="del" value="<?php echo _('Supprimer l\'assortiment') ?>" class="float-left" />
					<input type="submit" id="save" name="save" data-id="<?php echo $list['id'] ?>" value="<?php echo  (($is_creation)  ? _('Créer l\'assortiment') : _('Enregistrer')); ?>" />
				</td>
			</tr>
		</tfoot>
		<tbody>
			<tr>
				<td id="td-general-1">
					<label for="name"><span class="mandatory">*</span> <?php echo _('Libellé :'); ?></label>
				</td>
				<td id="td-general-2">
					<input type="text" id="name" name="name" value="<?php echo ria_array_get($list, 'name' , '')?>"/>
				</td>
			</tr>
			<tr>
				<td>
					<label for="type"><span class="mandatory">*</span> <?php echo _('Type d\'assortiments :') ?></label>
				</td>
				<td>
					<select name="type" id="type" <?php echo !$is_creation ? 'disabled' : ''?> >
						<?php foreach(array_flip(prw_followed_lists::$types) as $i => $type) {?>
							<option
								value="<?php echo $type?>"
								<?php echo (ria_array_get($list, 'type' , 0)==$i) ? 'selected="selected"' : ''?>
							>
								<?php echo ucfirst($type)?>
							</option>
						<?php } ?>
					</select>
				</td>
			</tr>
			<tr>
				<td><?php echo _("Type de section :"); ?></td>
				<td>
					<?php if (!$r_section || !ria_mysql_num_rows($r_section)) { ?>
						<?php echo _("Aucune section de créé"); ?>.
					<?php }else{ ?>
						<select name="section" id="section">
							<option value="0"></option>
							<?php while ($section = ria_mysql_fetch_assoc($r_section)) { ?>
								<option
									value="<?php echo $section['id'] ?>"
									<?php echo ria_array_get($list, 'fls_id' , '')  == $section['id'] ? 'selected="selected"' : '' ?>
								>
									<?php echo htmlspecialchars($section['title']) ?>
								</option>
							<?php } ?>
						</select>
					<?php } ?>
				</td>
			</tr>
			<tr>
				<td><label for="publish"><?php echo _('Activation :') ?></label></td>
				<td>
					<button
						id="publish"
						data-published="<?php echo $is_published ? 'true' : 'false'?>"
						title="<?php echo $is_published ? _('Clicker pour désactiver'):_('Clicker pour activer');?>"
						class="btn-state <?php echo $is_published ? 'btn-state--actif' : 'btn-state--inactif'?>"
					>
						<?php echo $is_published ? _('Actif'):_('Inactif');?>
					</button>
				</td>
			</tr>
			<tr>
				<td></td>
				<td>
					<div class="notice">
						<strong><?php echo _('Attention')?></strong> : <?php echo _('L\'activation de l\'assortiment donnera accès à ce dernier. Il est conseillé de l\'activer qu\'une fois la saisie des produits et des relations terminée.') ?>
					</div>
				</td>
			</tr>
			<?php if (!$is_creation) { ?>
				<tr>
					<td colspan="2" class="pmt-spe-rules">
						<?php echo GeneralProductTable('catalog', _('Catalogue produits'));?>
					</td>
				</tr>
				<tr>
					<td colspan="2" class="pmt-spe-rules">
						<?php echo GeneralProductTable('competition', _('Catalogue produits concurrent'));?>
					</td>
				</tr>
			<?php } ?>
		</tbody>
	</table>
</form>

<script>
	(function($){
		var state = {
			title: '<?php print ria_array_get($list, 'name' , ''); ?>',
			modified: false,
			section: '<?php print ria_array_get($list, 'fls_id' , ''); ?>',
			catalog: {
				ids : {},
				positions: [],
				items : []
			},
			competition: {
				ids : {},
				positions: [],
				items : []
			},
		};
		var stateHistory = [];

		if ($('div.success').length) {
			setTimeout(function(){
				$('div.success').slideUp('slow', function() {
					$('div.success').remove();
				});
			}, 8000);
		}

		var delay = (function(delay){
			var out = delay;
			return function(callback, delay){
				clearTimeout(out);
				out = setTimeout(callback, delay);
			}
		})(null)

		var StateManager = function(table, stateKey){
			var template = $('.template', table).clone();
			var noResult = $('.no-info', table).clone();

			$('.template', table).remove();
			$('.no-info', table).remove();

			var isCpt = stateKey !== 'catalog';

			init();

			function fixHelper(e, ui) {
				ui.children().each(function() {
					$(this).width($(this).width());
				});

				return ui;
			};

			function init() {
				$('tbody', table).sortable({
					handle: '.ria-row-catchable',
					helper: fixHelper,
					update: updateRank,
				});
				setEvent();
				render();
			}

			function updateRank(e, ui) {
				var ids = $('tbody', table).sortable('toArray');
				var oldCount = state[stateKey].positions.join();
				if (ids[0] === '') {
					state[stateKey].positions = ids.slice(1, ids.length);
				}else{
					state[stateKey].positions = ids;
				}
				$.each(state[stateKey].positions, function(i, id){
					var index = state[stateKey].ids[id];
					if (typeof state[stateKey].items[index] !== "undefined") {
						i++;
						state[stateKey].items[index].rank = i;
						$('#'+id+' .rank').text(i);
					}
				});
				var newCount = state[stateKey].positions.join();
				if (oldCount !== newCount) {
					state.modified = true;
				}
				stateHistory.push($.extend(true, {}, state));
			}

			function updateItems(items) {
				var oldCount = state[stateKey].items.length;
				$.each(items, function(i, item){
					if (typeof state[stateKey].ids[item.id] === "undefined") {
						var index = state[stateKey].items.push(item);
						state[stateKey].ids[item.id] = index-1;
						state[stateKey].positions.push(item.id);
					}
				});
				var newCount = state[stateKey].items.length;

				if (newCount !== oldCount) {
					render();
				}
			}

			function deleteItems(itemsIds) {
				var oldCount = state[stateKey].items.length;
				state[stateKey].items = state[stateKey].items.filter(function(item) {
					return itemsIds.indexOf(item.id) < 0;
				})
				state[stateKey].ids = state[stateKey].items.reduce(function(ids, item, i){
					ids[item.id] = i;
					return ids;
				}, {});
				state[stateKey].positions = state[stateKey].positions.filter(function(id) {
					return itemsIds.indexOf(id) < 0;
				})
				var newCount = state[stateKey].items.length;
				if (newCount !== oldCount) {
					state.modified = true;
					render();
				}
			}

			function render() {
				$('tbody tr', table).remove();

				if (state[stateKey].items.length < 1) {
					$('tbody', table).append(noResult);
					$('[name=delete]', table).hide();
				} else {
					$.each(state[stateKey].positions, function (i, key){
						var tr = template.clone();
						var p = state[stateKey].items[state[stateKey].ids[key]];
						if (typeof p !== 'undefined') {
							i++;
							tr.addClass('ria-row-orderable');
							tr.removeClass('template');
							$('.checkbox', tr).val(p.id);
							$('.url', tr).text(p.name);
							$('.url', tr).attr('href', '/admin/catalog/product.php?cat=0&prd='+p.id);
							$('.pmc', tr).data('id', p.id).val(parseFloat((p.pmc || 0)).toFixed(2));
							$('.rank', tr).text(i);
							tr.show();
							tr.attr('id', p.id);
							$('tbody', table).append(tr);
						}
					});

					$('[name=delete]', table).show();
				}

				$('tbody', table).sortable('refresh');
				updateRank();
			}

			function setEvent () {
				table.on('keyup', '.pmc', function(e){
					var that = $(this);
					delay(function () {
						state[stateKey].items[state[stateKey].ids[that.data('id')]].pmc = that.val();
					}, 500);
					state.modified = true;
				})
				$('input[name="prd-add"]', table).click(function(e){
					e.preventDefault();
					displayPopup( '<?php print _('Sélectionner un produit'); ?>', '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0&multiselect=true&orderable=false' );
					$('.popup_ria_iframe').load(function(){
						var innerDoc = $(this.contentDocument || this.contentWindow.document);
						innerDoc.on( 'click', '.selectprd', function (e) {
							var data = [];
							$('div.success', innerDoc).remove();
							$('div.error', innerDoc).remove();
							$('.select-prd:checked', innerDoc).each(function(){
								data.push({
									id:$(this).val(),
									name:$(this).parent().parent().find('td:nth(2)').text().trim() + ' ' + $(this).parent().parent().find('td:nth(3)').text().trim(),
									pmc:null,
									rank:0,
									is_cpt: isCpt,
								});
							});
							updateItems(data);
							state.modified = true;
							if ($('.select-prd:checked', innerDoc).length) {
								$('#popup-content', innerDoc).prepend(
									$('<div>').addClass('success').text('<?php print _('Les produits ont été ajoutés avec succès.'); ?>')
								);
								setTimeout(function() {
									$('div.success', innerDoc).slideUp('slow', function() {
										$('div.success').remove();
									});
								}, 5000);
							}else{
								$('#popup-content', innerDoc).prepend(
									$('<div>').addClass('error').text('<?php print _('Vous devez sélectionner des produits a ajouté à votre catalogue.'); ?>')
								);
							}
						});
					});
				});

				$('input[name="fast-add"]', table).click(function(e) {
					e.preventDefault();

					displayPopup('<?php print _('Ajout rapide de produit'); ?>', '', '/admin/ajax/catalog/linear-raised/popup-add-fast.php');

					$('.popup_ria_iframe').load(function() {
						var innerDoc = $(this.contentDocument || this.contentWindow.document);

						innerDoc.on('submit', '#validation', function (e) {
							var json = JSON.parse($('#json', innerDoc).val()).sort(function (a, b) {
								if (!a.rank || !b.rank) {
									return a.name < b.name;
								}

								return a.rank - b.rank;
							});

							json.forEach(function (item) {
								state[stateKey].items = state[stateKey].items.filter(function (a) {
									return a.id != item.id;
								});

								delete state[stateKey].ids[item.id];
								delete state[stateKey].positions[state[stateKey].positions.indexOf(item.id)];
							});

							if (json.length) {
								$.each(json, function(i, item) {
									if (item.type === 'error') {
										return;
									}

									item['is_cpt'] = isCpt;

									var index = state[stateKey].items.push(item);

									state[stateKey].ids[item.id] = (index - 1);
									state[stateKey].positions.splice((item.rank - 1), 0, item.id);
								});

								state[stateKey].items.forEach(function (a, i) {
									state[stateKey].ids[a.id] = i;
								});

								state.modified = true;
								render();
							}

							return false;
						})
					});
				});

				$('input[name="delete"]', table).click(function(e){
					e.preventDefault();
					var itemsIds = [];
					$('tbody .checkbox:checked', table).each(function(i, el){
						itemsIds.push(el.value);
					})
					deleteItems(itemsIds);
				})
			}

			return {
				render: render,
				updateItems: updateItems,
			};
		};

		if ( $('#catalog').length && $('#competition').length) {
			var catalog = StateManager($('#catalog'), 'catalog');

			var competition = StateManager($('#competition'), 'competition');
			$('.no-info td').html($('<div>').addClass('notice').text('Chargement...'));
			$.ajax({
				url: '/admin/ajax/catalog/linear-raised/index.php?action=getProducts',
				data: {id: $('#id').val()}
			}).done(function(json){
				if (json.result) {
					if (typeof json.content.catalog === 'object') {
						catalog.updateItems(json.content.catalog);
					}
					if (typeof json.content.competition === 'object') {
						competition.updateItems(json.content.competition);
					}
				}
				$('.no-info td').text('Aucun produit sélectionné');
			})
		}

		$('#name').on('keyup', function(e){
			var that = $(this);
			delay(function(){
				state.title = that.val()
				state.modified = true;
			},600);
		})

		$('#section').on('change', function(e){
			state.section = $(this).val()
			state.modified = true;
		});

		$('#publish').on('click', function(e){
			var is_published = $(this).data('published');
			if(confirm('Êtes-vous sûr de vouloir ' + (!is_published ? 'activer' : 'désactiver') + ' cet assortiment ?')) {
				var action = 'publish';
				if (is_published) {
					action = 'unpublish';
				}
				var that = $(this);
				$.ajax({
					url: '/admin/ajax/catalog/linear-raised/index.php?action='+action,
					type: 'post',
					data: {
						id: $('#id').val(),
					}
				}).done(function(json){
					if (json.result) {
						that.data('published', !is_published);
						if (!is_published) {
							that.removeClass('btn-state--inactif');
							that.addClass('btn-state--actif');
						}else{
							that.removeClass('btn-state--actif');
							that.addClass('btn-state--inactif');
						}
						that.text(json.message);
						that.attr('title', json.content);
					}
				})
			}

			return false;
		})

		$("#del").on('click', function(e) {
			return confirm('<?php print _('Êtes-vous sûr de vouloir supprimer cette assortiment ?'); ?>');
		})

		window.onbeforeunload = function() {
			if (state.modified) {
				return '<?php print _("Vous n\'avez pas enregistré vos modifications. Êtes-vous sûr de vouloir quitter cette page ?"); ?>';
			}
		}

		$("#save").on('click', function(e) {
			var items = [];
			var data = {
				id: $(this).data('id'),
				title: state.title,
				section: state.section,
				items:items.concat(state.catalog.items,state.competition.items),
			};
			$('div.success').remove();
			$.ajax({
				url: '/admin/ajax/catalog/linear-raised/index.php?action=batchProduct',
				type: 'post',
				data: data,
			}).done(function(json){
				if (json.result) {
					state.modified = false;
					$('.tabstrip').before(
						$('<div>').addClass('success').text(json.message)
					);
					setTimeout(function(){
						$('div.success').slideUp("slow", function() { $('div.success').remove();});
					}, 8000);
				}
			})
			return false;
		})
	})($)
</script>