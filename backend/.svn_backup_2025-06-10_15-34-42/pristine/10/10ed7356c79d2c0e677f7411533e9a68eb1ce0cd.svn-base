<?php

	/** \file ajax-set-canonical.php
	 *	Ce fichier permet la mise à jour de l'url canonique d'un produit, en Ajax
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_EDIT');

	if (! isset( $_POST['cat'], $_POST['prd'])) throw new Exception('Erreur post !');
	
	$return = true;
	if( !prd_classify_set_canonical( $_POST['prd'], $_POST['cat'] ) )
		$return = false;
		
	print json_encode(array('result' => $return));
