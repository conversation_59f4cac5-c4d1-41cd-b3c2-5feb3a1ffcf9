<?php

class Filtered
{
	protected $indexes = array();

	protected $filtered = array();

	protected $current_key;

	public function setCurrent<PERSON><PERSON>($key)
	{
		$this->current_key = $key;
	}

	public function addIndex($index, $key = null)
	{
		if (!is_null($key)) {
			$this->setCurrent<PERSON>ey($key);
		}
		$this->indexes[$index] = $this->current_key;
	}

	public function addFiltered($value, $key = null)
	{
		if (!is_null($key)) {
			$this->setCurrentKey($key);
		}
		$this->filtered[$this->current_key] = $value;
	}

	public function getFiltered()
	{
		return $this->filtered;
	}

	public function getFilteredByIndex($index)
	{
		if (array_key_exists($index, $this->indexes)) {
			$key = $this->indexes[$index];
			if (array_key_exists($key, $this->filtered)) {
				return $this->filtered[$key];
			}
		}
		return null;
	}
}