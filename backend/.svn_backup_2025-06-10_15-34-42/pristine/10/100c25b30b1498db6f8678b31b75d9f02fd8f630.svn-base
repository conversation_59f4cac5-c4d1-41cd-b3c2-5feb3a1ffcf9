<?php
/**
 * RuleLinks
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * <PERSON>zUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * RuleLinks Class Doc Comment
 *
 * @category Class
 * @description Links to retrieve/action on other entities
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class RuleLinks implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'ruleLinks';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'self' => '\Swagger\Client\Model\LinksGetRuleLink',
        'update' => '\Swagger\Client\Model\LinksUpdateRuleLink',
        'delete' => '\Swagger\Client\Model\LinksDeleteRuleLink',
        'moveup' => '\Swagger\Client\Model\LinksMoveUpRuleLink',
        'movedown' => '\Swagger\Client\Model\LinksMoveDownRuleLink',
        'enable' => '\Swagger\Client\Model\LinksEnableRuleLink',
        'disable' => '\Swagger\Client\Model\LinksDisableRuleLink',
        'run' => '\Swagger\Client\Model\LinksRunRuleLink',
        'report_filter' => '\Swagger\Client\Model\LinksGetReportFilterLink'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'self' => null,
        'update' => null,
        'delete' => null,
        'moveup' => null,
        'movedown' => null,
        'enable' => null,
        'disable' => null,
        'run' => null,
        'report_filter' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'self' => 'self',
        'update' => 'update',
        'delete' => 'delete',
        'moveup' => 'moveup',
        'movedown' => 'movedown',
        'enable' => 'enable',
        'disable' => 'disable',
        'run' => 'run',
        'report_filter' => 'reportFilter'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'self' => 'setSelf',
        'update' => 'setUpdate',
        'delete' => 'setDelete',
        'moveup' => 'setMoveup',
        'movedown' => 'setMovedown',
        'enable' => 'setEnable',
        'disable' => 'setDisable',
        'run' => 'setRun',
        'report_filter' => 'setReportFilter'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'self' => 'getSelf',
        'update' => 'getUpdate',
        'delete' => 'getDelete',
        'moveup' => 'getMoveup',
        'movedown' => 'getMovedown',
        'enable' => 'getEnable',
        'disable' => 'getDisable',
        'run' => 'getRun',
        'report_filter' => 'getReportFilter'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['self'] = isset($data['self']) ? $data['self'] : null;
        $this->container['update'] = isset($data['update']) ? $data['update'] : null;
        $this->container['delete'] = isset($data['delete']) ? $data['delete'] : null;
        $this->container['moveup'] = isset($data['moveup']) ? $data['moveup'] : null;
        $this->container['movedown'] = isset($data['movedown']) ? $data['movedown'] : null;
        $this->container['enable'] = isset($data['enable']) ? $data['enable'] : null;
        $this->container['disable'] = isset($data['disable']) ? $data['disable'] : null;
        $this->container['run'] = isset($data['run']) ? $data['run'] : null;
        $this->container['report_filter'] = isset($data['report_filter']) ? $data['report_filter'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['self'] === null) {
            $invalidProperties[] = "'self' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['self'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets self
     *
     * @return \Swagger\Client\Model\LinksGetRuleLink
     */
    public function getSelf()
    {
        return $this->container['self'];
    }

    /**
     * Sets self
     *
     * @param \Swagger\Client\Model\LinksGetRuleLink $self self
     *
     * @return $this
     */
    public function setSelf($self)
    {
        $this->container['self'] = $self;

        return $this;
    }

    /**
     * Gets update
     *
     * @return \Swagger\Client\Model\LinksUpdateRuleLink
     */
    public function getUpdate()
    {
        return $this->container['update'];
    }

    /**
     * Sets update
     *
     * @param \Swagger\Client\Model\LinksUpdateRuleLink $update update
     *
     * @return $this
     */
    public function setUpdate($update)
    {
        $this->container['update'] = $update;

        return $this;
    }

    /**
     * Gets delete
     *
     * @return \Swagger\Client\Model\LinksDeleteRuleLink
     */
    public function getDelete()
    {
        return $this->container['delete'];
    }

    /**
     * Sets delete
     *
     * @param \Swagger\Client\Model\LinksDeleteRuleLink $delete delete
     *
     * @return $this
     */
    public function setDelete($delete)
    {
        $this->container['delete'] = $delete;

        return $this;
    }

    /**
     * Gets moveup
     *
     * @return \Swagger\Client\Model\LinksMoveUpRuleLink
     */
    public function getMoveup()
    {
        return $this->container['moveup'];
    }

    /**
     * Sets moveup
     *
     * @param \Swagger\Client\Model\LinksMoveUpRuleLink $moveup moveup
     *
     * @return $this
     */
    public function setMoveup($moveup)
    {
        $this->container['moveup'] = $moveup;

        return $this;
    }

    /**
     * Gets movedown
     *
     * @return \Swagger\Client\Model\LinksMoveDownRuleLink
     */
    public function getMovedown()
    {
        return $this->container['movedown'];
    }

    /**
     * Sets movedown
     *
     * @param \Swagger\Client\Model\LinksMoveDownRuleLink $movedown movedown
     *
     * @return $this
     */
    public function setMovedown($movedown)
    {
        $this->container['movedown'] = $movedown;

        return $this;
    }

    /**
     * Gets enable
     *
     * @return \Swagger\Client\Model\LinksEnableRuleLink
     */
    public function getEnable()
    {
        return $this->container['enable'];
    }

    /**
     * Sets enable
     *
     * @param \Swagger\Client\Model\LinksEnableRuleLink $enable enable
     *
     * @return $this
     */
    public function setEnable($enable)
    {
        $this->container['enable'] = $enable;

        return $this;
    }

    /**
     * Gets disable
     *
     * @return \Swagger\Client\Model\LinksDisableRuleLink
     */
    public function getDisable()
    {
        return $this->container['disable'];
    }

    /**
     * Sets disable
     *
     * @param \Swagger\Client\Model\LinksDisableRuleLink $disable disable
     *
     * @return $this
     */
    public function setDisable($disable)
    {
        $this->container['disable'] = $disable;

        return $this;
    }

    /**
     * Gets run
     *
     * @return \Swagger\Client\Model\LinksRunRuleLink
     */
    public function getRun()
    {
        return $this->container['run'];
    }

    /**
     * Sets run
     *
     * @param \Swagger\Client\Model\LinksRunRuleLink $run run
     *
     * @return $this
     */
    public function setRun($run)
    {
        $this->container['run'] = $run;

        return $this;
    }

    /**
     * Gets report_filter
     *
     * @return \Swagger\Client\Model\LinksGetReportFilterLink
     */
    public function getReportFilter()
    {
        return $this->container['report_filter'];
    }

    /**
     * Sets report_filter
     *
     * @param \Swagger\Client\Model\LinksGetReportFilterLink $report_filter report_filter
     *
     * @return $this
     */
    public function setReportFilter($report_filter)
    {
        $this->container['report_filter'] = $report_filter;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


