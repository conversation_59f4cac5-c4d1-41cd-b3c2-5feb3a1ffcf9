<?php
namespace Riashop\PriceWatching\models\LinearRaised\Features;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_users;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_products;

class FollowedList {

	private $_id = null;

	/**
	 * Constructeur de l'objet
	 *
	 * @param integer $id Identifiant de la liste
	 */
	public function __construct($id)
	{
		if (!is_numeric($id) || $id <=0) {
			throw new \InvalidArgumentException("Error Processing Request");
		}

		$this->_id = $id;
	}
	/**
	 * Cette fonction permet de publié une liste
	 *
	 * @return void
	 */
	public function publish()
	{
		return prw_followed_lists::publish($this->_id);
	}
	/**
	 * Undocumented function
	 *
	 * @param array $users
	 * @return boolean Retourne true si succès, false si erreur
	 */
	public function batchUpdateUsers(array $users)
	{
		try {
			//suppresssion
			$result = prw_followed_users::delete($this->_id);
			//ajout
			foreach ($users as $user) {
				if(!prw_followed_users::add(
					$this->_id,
					$user['prf_id'],
					$user['id']
				)) {
					throw new \Exception('Error '.ria_mysql_error());
				};
			}
			//setdatemodified
			return prw_followed_lists::setDateModified($this->_id);
		}catch(\Exception $e) {
			error_log($e->getMessage());
			return false;
		}
	}
	/**
	 * Cette fonction permet d'ajouté des produits depuis l'interface a un assortiment
	 *
	 * @param array $products Tableau de produit
	 * @return boolean Retourne true si succès, false si erreur
	 */
	public function batchUpdateProducts(array $products)
	{
		try{
			// On supprime d'abord les produits...
			$result = prw_followed_products::delete($this->_id);

			// Pour les rajouter par la suite.
			foreach( $products as $product ){
				// Permet d'accepter le point ou la virgule comme séparateur.
				$product['pmc'] = str_replace(',', '.', trim($product['pmc']));

				if( !prw_followed_products::add(
					$product['id'],
					$this->_id,
					$product['rank'],
					($product['pmc'] ? $product['pmc'] : 0),
					$product['is_cpt'] == 'true'
				) ){
					throw new \Exception('Error '.ria_mysql_error());
				};
			}

			// Met à jour la date de modification.
			return prw_followed_lists::setDateModified($this->_id);
		}catch(\Exception $e) {
			error_log($e->getMessage());
			return false;
		}
	}
}