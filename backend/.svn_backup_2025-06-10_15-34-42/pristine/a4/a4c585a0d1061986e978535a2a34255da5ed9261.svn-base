<?php
/**
 * \defgroup LinearRaised Module de relevé linéaire
 * \ingroup PriceWatching
 * \warning Ce module reprend le schéma de la veille tarifaire mais ne prend pas en compte la notion de concurrent (cpt_id)
 * \namespace Riashop::PriceWatching::models::LinearRaised
 * \brief Ce module permet la gestion des relévés lineaires
 * Ce module contient l'api du module relevé linéaire, cela comporte le modèle et les actions de l'interfaces
 */

/**
 * Autoloader spécifique au module LinearRaised
 * \param  $className Nom de la class
 * \return            true si success, false si erreur (class/fichier n'existe pas ou class qui n'est pas du module)
 */
function linear_raised_autoload($className){
	$filename = str_replace("\\", '/', $className) . '.php';
    if (strstr($filename, 'Riashop/PriceWatching/models/LinearRaised')) {
		$file = str_replace('Riashop/PriceWatching/models/LinearRaised/', '', $filename );
		if (file_exists(__DIR__.'/'.$file)) {
			require_once(__DIR__.'/'.$file);
			if (class_exists($className)) {
				return true;
			}
		}
    }
    return false;
}
spl_autoload_register('linear_raised_autoload');