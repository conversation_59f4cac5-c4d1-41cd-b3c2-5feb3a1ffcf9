<?php
/** SS
 * \defgroup api-devices-need_sync Synchronisation 
 * \ingroup Yuto 
 * @{		
 * \page api-devices-need-sync-del Suppression 
 *
 * Cette fonction supprime les demandes de synchronisation 
 *
 *	\code
 *		DELETE /devices/need_sync/
 *	\endcode
 *	 
 * @param int $tsk_id Obligatoire, identifiant de la tâche.
 *	
 * @return true si la suppression s'est déroulée avec succès 
 * @}
*/

switch( $method ){
	// supprime un élément
	case 'del': 

		if( !isset($_REQUEST['tsk_id']) ){
			throw new Exception("Paramètre invalides");
			
		}
		dev_devices_need_sync_del($config['dev_id'], $_REQUEST['tsk_id']);

		$result = true;
		break;
}
