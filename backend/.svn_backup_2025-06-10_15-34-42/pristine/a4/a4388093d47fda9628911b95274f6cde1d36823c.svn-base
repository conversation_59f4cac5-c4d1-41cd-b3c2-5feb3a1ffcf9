``block``
=========

.. versionadded: 1.28
    Using ``block`` with the ``defined`` test was added in Twig 1.28.

.. versionadded: 1.28
    Support for the template argument was added in Twig 1.28.

When a template uses inheritance and if you want to print a block multiple
times, use the ``block`` function:

.. code-block:: twig

    <title>{% block title %}{% endblock %}</title>

    <h1>{{ block('title') }}</h1>

    {% block body %}{% endblock %}

The ``block`` function can also be used to display one block from another
template:

.. code-block:: twig

    {{ block("title", "common_blocks.twig") }}

Use the ``defined`` test to check if a block exists in the context of the
current template:

.. code-block:: twig

    {% if block("footer") is defined %}
        ...
    {% endif %}

    {% if block("footer", "common_blocks.twig") is defined %}
        ...
    {% endif %}

.. seealso:: :doc:`extends<../tags/extends>`, :doc:`parent<../functions/parent>`
