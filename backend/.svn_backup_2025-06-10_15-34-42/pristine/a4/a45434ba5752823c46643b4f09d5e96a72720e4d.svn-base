<?php

	/**	\file index.php
	 *	Cette page affiche la liste des catégories tarifaires, et permet leur gestion (ajout, modification, suppression)
	 */

	require_once('site.inc.php');
	require_once('products.inc.php');
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_PRICE');

	define( 'QTE_MIN_FLD_ID', -1 );

	unset($error);

	$class_array = array();
	if( $classes = fld_classes_get() ){
		while( $class = ria_mysql_fetch_array($classes) ){
			$class_array[] = $class['id'];
		}
	}

	// Bouton Supprimer la catégorie tarifaire
	if( isset($_POST['del-prc']) ){
		if( !isset($_POST['prc']) ){
			$error = _("Veuillez sélectionner les catégories tarifaires à supprimer");
		}else{
			if( !is_array($_POST['prc']) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression de la catégorie tarifaire.");
			}else{
				foreach( $_POST['prc'] as $p ){
					if( !prd_prices_categories_get_is_sync($p) ){
						if( !prd_prices_categories_del($p) ){
							$error = _("Une erreur inattendue s'est produite lors de la suppression de la catégorie tarifaire.");
							break;
						}
					}
				}
			}
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	// Boutons Ajouter la catégorie tarifaire
	if( isset($_POST['name-prc']) ){
		if( !trim($_POST['name-prc']) ){
			header('Location: edit.php');
			exit;
		}else{
			if( !prd_prices_categories_add($_POST['name-prc']) ){
				$error = "Une erreur inattendue s'est produite lors de la création de la catégorie.\nVeuillez réessayer ou prendre contact avec l'administrateur.";
			}
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	// Boutons supprimer l'exclusion
	if(isset($_POST['del-prd'])){
		if(!isset($_POST['prd'])){
			$error = "Veuillez sélectionner les produits à supprimer de l'exclusion.";
		}else{
			if( !is_array($_POST['prd'])){
				$error = "Une erreur inattendue s'est produite lors de la suppression du produit de l'exclusion.";
			}else{
				foreach($_POST['prd'] as $p){
					pmt_excluded_auto_delete_prd("ref", $p);
				}
			}
		}
		if(!isset($error)){
			header('Location: index.php');
			exit;
		}
	}

	// Boutons ajouter exclusion
	if(isset($_POST['ref-prd'])){
		if( !trim($_POST['ref-prd']) ){
			header('Location: index.php');
			exit;
		}else{
			$prd = prd_products_exists_ref($_POST['ref-prd'], false);
			if($prd){
				pmt_excluded_auto_update('ref', $_POST['ref-prd']);
			}else{
				$error = "Aucun produit ne correspond à cette référence.";
			}
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}



	// Boutons Modifier le mode de calcul
	if( isset($_POST['save-type-calcul'], $_POST['type-calc']) ){
		if( !cfg_overrides_set_value( 'discount_apply_type', $_POST['type-calc']=='sum' ? 0 : ( $_POST['type-calc']=='best' ? 2 : 1 ) ) ){
			$error = "Une erreur inattendue s'est produite lors de la sauvegarde du mode de calcul des remises.\nVeuillez réessayer ou prendre contact avec l'administrateur.";
		}else{
			$config['discount_apply_type'] = $_POST['type-calc']=='sum' ? 0 : ( $_POST['type-calc']=='best' ? 2 : 1 );
			$word = 'En cascade';
			switch( $config['discount_apply_type'] ){
				case 0: $word = 'Additionnées'; break;
				case 2: $word = 'Meilleure remise'; break;
			}
			$error_success = "Vos remises seront désormais calculées selon le type '".$word."'.";
		}
	}

	// Sauvegarde de la catégorie par défaut (mono-site)
	if( isset($_POST['save-prc-default'], $_POST['prc-default']) ){
		if( prd_prices_categories_exists($_POST['prc-default']) ){

			// charge la précédente catégorie tarifaire par défaut
			$old_prc = $_POST['prc-default'];
			$rdefault = cfg_overrides_get( $config['wst_id'], array(), 'default_prc_id' );
			if( $rdefault && ria_mysql_num_rows($rdefault) ){
				$old_prc = ria_mysql_result($rdefault, 0, 'value');
			}else{
				$rdefault = cfg_overrides_get( 0, array(), 'default_prc_id' );
				if( $rdefault && ria_mysql_num_rows($rdefault) ){
					$old_prc = ria_mysql_result($rdefault, 0, 'value');
				}
			}

			// Gère le cas du mono-site. Ne met à jour la catégorie tarifaire par défaut que si celle-ci a été modifiée
			if( $_POST['prc-default']!=$old_prc ){
				if( !cfg_overrides_set_value( 'default_prc_id', $_POST['prc-default'], $config['wst_id'] ) ){
					$error = "Une erreur inattendue s'est produite lors de la sauvegarde de la catégorie tarifaire par défaut.\nVeuillez réessayer ou prendre contact avec l'administrateur.";
				}else{

					// Applique les choix supplémentaires s'il y en a
					if( isset($_POST['usr_action']) ){
						switch( strtolower(trim($_POST['usr_action'])) ){
							case 'rdb-all':
								if( $rusr = gu_users_get( 0, '', '', 0, '', 0, '', false, false, false, -1, '', false, 0, '', 0, false, true ) ){
									while( $u = ria_mysql_fetch_array($rusr) ){
										if( !gu_users_set_prc( $u['id'], $_POST['prc-default'] ) )
											error_log( 'Impossible de mettre à jour la catégorie tarifaire du client '.$u['id'] );
									}
								}
								break;
							case 'rdb-all-old':
								if( $rusr = gu_users_get( 0, '', '', 0, '', 0, '', false, false, false, -1, '', false, 0, '', 0, false, true, null, 0, false, false, null, false, $old_prc ) ){
									while( $u = ria_mysql_fetch_array($rusr) ){
										if( !gu_users_set_prc( $u['id'], $_POST['prc-default'] ) )
											error_log( 'Impossible de mettre à jour la catégorie tarifaire du client '.$u['id'] );
									}
								}
								break;
						}
					}

				}
			}

		}else{
			$error = _("Une erreur inattendue s'est produite lors de la sauvegarde de la catégorie tarifaire par défaut.\nVeuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur.");
		}
	}

	// Gère le cas du multi-site : chaque site peut avoir une catégorie tarifaire par défaut différente
	if( isset($_POST['save-prc-default-wst']) ){
		print 'wst-ok';
		if( $rwst = wst_websites_get() ){
			while( $wst = ria_mysql_fetch_array($rwst) ){
				if( isset($_POST['prc-default-'.$wst['id']]) ){
					if( (int)$_POST['prc-default-'.$wst['id']] < 0 ){
						// La valeur utilisée est celle pour tous les sites
						cfg_overrides_del_value( 'default_prc_id',$wst['id']  );
					}elseif( prd_prices_categories_exists( $_POST['prc-default-'.$wst['id']] ) ){
						// La valeur utilisée est spécifique au site
						cfg_overrides_set_value( 'default_prc_id', $_POST['prc-default-'.$wst['id']], $wst['id'] );
					}
					// Applique les choix supplémentaires s'il y en a
					if( isset($_POST['usr_action-'.$wst['id']]) ){
						switch( strtolower(trim($_POST['usr_action-'.$wst['id']])) ){
							case 'rdb-all':
								if( $rusr = gu_users_get( 0, '', '', 0, '', 0, '', false, false, false, -1, '', false, 0, '', 0, false, true, null, 0, false, false, null, false, 0, $wst['id'] ) ){
									while( $u = ria_mysql_fetch_array($rusr) ){
										if( !gu_users_set_prc( $u['id'], $_POST['prc-default-'.$wst['id']] ) )
											error_log( 'Impossible de mettre à jour la catégorie tarifaire du client '.$u['id'] );
									}
								}
								break;
							case 'rdb-all-old':
								if( $rusr = gu_users_get( 0, '', '', 0, '', 0, '', false, false, false, -1, '', false, 0, '', 0, false, true, null, 0, false, false, null, false, $old_prc, $wst['id'] ) ){
									while( $u = ria_mysql_fetch_array($rusr) ){
										if( !gu_users_set_prc( $u['id'], $_POST['prc-default-'.$wst['id']] ) )
											error_log( 'Impossible de mettre à jour la catégorie tarifaire du client '.$u['id'] );
									}
								}
								break;
						}
					}
				}


				// Vide la config pour chaque site du tenant afin de forcer son rechargement
				$memcached->delete( 'config:'.$config['tnt_id'].':'.$wst['id'] );
			}
		}

		header('Location: index.php?save-prc-wst-ok=');
		exit;
	}
	
	// Sauvegarde du taux de commission
	if( isset($_POST['save_commission']) ){
		
		// Vérification des données saisies dans le formulaire
		if( !isset($_POST['taux_commission']) ){
			$error = _("Un paramètres de configuration est manquant ou invalides.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		} elseif( !is_numeric($_POST['taux_commission']) ){
			$error = _("La valeur saisie pour la valeur est incorrecte. \nCette valeur doit être numérique et comprise entre 1 et 999.");
		} else{
			// on insère la nouvelle valeur
			if( cfg_overrides_set_value('taux_commission',$_POST['taux_commission'], $config['wst_id']) ){
				header('Location: index.php');
				exit;
			}else{
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre configuration.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
			}
		}
	}

	$checkbox = gu_user_is_authorized('_RGH_ADMIN_CONFIG_PRICE_DEL');

	$colspan = $checkbox ? 4 : 3;

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Paramètres de tarification') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

?>
	<?php
		$categories = prd_prices_categories_get();
		if ( !ria_mysql_num_rows($categories) ) {
			$nbrCategories= 0;
		} else {
			$nbrCategories = ria_mysql_num_rows($categories);
		}
	?>
	<h2><?php echo _("Paramètres de tarification"); ?></h2>

	<h3><?php echo _("Catégories tarifaires").' ('.$nbrCategories.')'; ?></h3>

	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<form action="index.php" method="post">
		<table id="table-param-cat-tarifaires" class="checklist">	
			<thead>
				<tr>
					<?php if( $checkbox ){ ?>
						<th id="prc-sel">
							<input type="checkbox" class="checkbox" onclick="checkAllClick(this);">
						</th>
					<?php } ?>
					<th id="prc-name"><?php print _('Désignation'); ?></th>
					<th id="prc-ttc" class="align-right">
						<?php print _('Prix'); ?>
						<abbr title="<?php print _('Toutes Taxes Comprises'); ?>">TTC</abbr>
					</th>
					<th id="prc-users" class="align-right"><?php print _('Utilisateurs'); ?></th>
				</tr>
			</thead>
			<tbody>
				<?php
					if( !ria_mysql_num_rows($categories) ){
						?>
						<tr><td colspan="<?php print $colspan; ?>"><?php echo _("Aucune catégorie tarifaire"); ?></td></tr>
						<?php
					}else{
						while( $r = ria_mysql_fetch_array($categories) ){
							?>
							<tr>
								<?php if( $checkbox ){ ?>
								<td headers="prc-sel"><input type="checkbox" class="checkbox" name="prc[]" value="<?php print $r['id']; ?>" /></td>
								<?php }
								if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_PRICE_EDIT') ){ ?>
								<td headers="prc-name"><?php print view_prc_is_sync($r); ?> <a href="edit.php?prc=<?php print $r['id']; ?>"><?php print htmlspecialchars($r['name']); ?></a></td>
								<?php }else{ ?>
								<td headers="prc-name"><?php print view_prc_is_sync($r); ?> <?php print htmlspecialchars($r['name']); ?></td>
								<?php } ?>
								<td headers="prc-ttc" class="align-right"><?php print $r['ttc'] ? 'Oui' : 'Non'; ?></td>
								<td headers="prc-users" class="align-right"><?php print ria_number_format($r['users']); ?></td>
							</tr>
							<?php
						}
					}
				?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="<?php print $colspan; ?>" class="align-left">
						<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_PRICE_DEL') ){ ?>
						<input type="submit" name="del-prc" class="btn-del" value="<?php echo _("Supprimer"); ?>" onclick="return prcConfirmDelList()" />
						<?php } ?>
						<div class="float-right">
							<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_PRICE_ADD') ){ ?>
							<label for="name"><?php print _('Ajouter une catégorie :'); ?></label> <input type="text" name="name-prc" id="name" maxlength="75" />
							<input type="submit" value="<?php print _('Ajouter'); ?>" />
							<?php } ?>
						</div>
					</td>
				</tr>
			</tfoot>
		</table>

	</form>

	<?php
	$rcat = prd_prices_categories_get();
	if( $rcat && ria_mysql_num_rows($rcat) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_PRICE_DEFAULT') ){

	$rwst = wst_websites_get();
	$multiwst = $rwst && ria_mysql_num_rows($rwst)>1;

	// catégorie tarifaire configurée avec wst_id = 0 (si existante)
	$prc = $config['default_prc_id'];
	$rdefault = cfg_overrides_get( $config['wst_id'], array(), 'default_prc_id' );
	if( $rdefault && ria_mysql_num_rows($rdefault) ){
		$prc = ria_mysql_result($rdefault, 0, 'value');
	}else{
		$rdefault = cfg_overrides_get( 0, array(), 'default_prc_id' );
		if( $rdefault && ria_mysql_num_rows($rdefault) ){
			$prc = ria_mysql_result($rdefault, 0, 'value');
		}
	}
	$cat_array = array();
	$cat_array[] = array( 'id'=>-1, 'name'=>'' );
	while( $cat = ria_mysql_fetch_array($rcat) ){
		$cat_array[] = array( 'id'=>$cat['id'], 'name'=>$cat['name'] );
	}
	?>


	<?php if (!$multiwst) {?>
	<h3><?php echo _("Catégorie tarifaire par défaut"); ?></h3>
	<?php if( !isset($error) && !isset($error_success) && isset($_GET['save-prc-ok']) ){ ?>
		<div class="error-success"><?php echo _("La sauvegarde des modifications s'est correctement déroulée."); ?></div>
	<?php } ?>
	<p><?php echo _("Ce formulaire vous permet de définir la catégorie tarifaire par défaut de"); ?> <?php print $multiwst ? _('vos boutiques') : _('votre boutique'); ?>.</p>
	<p><?php echo _("Cette catégorie est requise dans les cas suivants :"); ?></p>
	<ul>
		<li><?php echo _("Afficher les tarifs de la boutique quand l'utilisateur est déconnecté."); ?></li>
		<li><?php echo _("Déterminer la catégorie d'un utilisateur lors de son inscription."); ?></li>
	</ul>
	<p><?php echo _("Les comptes synchronisés qui proviennent de votre gestion commerciale sont gérés différemment.")."<br/>"._("Leur catégorie tarifaire sera celle qui leur a été attribuée dans l'ERP."); ?></p>

	<form method="post" action="index.php">
		<select class="prc-selector" name="prc-default" data-prc="<?php echo $prc ?>" id="prc-default">
			<?php
			foreach ($cat_array as $cat) {
				print '<option value="'.$cat['id'].'" '.( $cat['id']==$prc ? 'selected="selected"' : '' ).'>'.htmlspecialchars($cat['name']).'</option>';
			}
			?>
		</select>
		<div class="prc-default-options">
			<input type="radio" name="usr_action" value="rdb-all" id="rdb-all" /><label for="rdb-all"><?php echo _("Appliquer ce changement à tous les comptes existants (hors comptes synchronisés)."); ?></label><br />
			<input type="radio" name="usr_action" value="rdb-all-old" id="rdb-all-old" /><label for="rdb-all-old"><?php echo _("Appliquer ce changement à tous les comptes existants dont l'ancienne catégorie était celle par défaut (hors comptes synchronisés)."); ?></label><br />
			<input type="radio" name="usr_action" value="rdb-no" id="rdb-no" checked="checked" /><label for="rdb-no"><?php echo _("Ne modifier aucun compte client."); ?></label>
		</div>
		<input title="Sauvegarder les modifications" type="submit" class="action" value="<?php print _('Enregistrer'); ?>" name="save-prc-default" />
	</form>

	<?php }else{ ?>
		<h3><?php echo _("Catégorie tarifaire par défaut"); ?></h3>

		<?php
			if( !isset($error) && !isset($error_success) && isset($_GET['save-prc-wst-ok']) ){
				print '<div class="error-success">' . _('La sauvegarde des modifications s\'est correctement déroulée.') . '</div>';
			}
		?>
		<p><?php echo _("La catégorie tarifaire par défaut est utilisée dans les cas suivants :"); ?></p>
		<ul>
			<li><?php echo _("Afficher les tarifs quand le client n'est pas encore identifié (tarif général ou par défaut)."); ?></li>
			<li><?php echo _("Déterminer la catégorie tarifaire attribuée à un compte client lors de sa création."); ?></li>
		</ul>

		<form method="post" action="index.php">
			<table id="table-cat-tarifaire-site" class="checklist">
				<tbody>
					<?php
					while( $wst = ria_mysql_fetch_array($rwst) ){
						// catégorie tarifaire configurée avec wst_id = current (si existante)
						$prc = -1;
						$rdefault = cfg_overrides_get( $wst['id'], array(), 'default_prc_id' );
						if( $rdefault && ria_mysql_num_rows($rdefault) ){
							$prc = ria_mysql_result($rdefault, 0, 'value');
						}else{
							$rdefault = cfg_overrides_get( 0, array(), 'default_prc_id' );
							if( $rdefault && ria_mysql_num_rows($rdefault) ){
								$prc = ria_mysql_result($rdefault, 0, 'value');
							}
						}
						?>
						<tr>
							<td><?php echo htmlspecialchars($wst['name'])?></td>
							<td>
								<select name="prc-default-<?php echo $wst['id'] ?>" data-prc="<?php echo $prc ?>" class="prc-selector">
								<?php foreach( $cat_array as $cat ){ ?>
									<option value="<?php echo $cat['id']?>" <?php echo ( $cat['id']==$prc ? 'selected="selected"' : '' )?>><?php echo htmlspecialchars($cat['name']) ?></option>
								<?php }?>
								</select>
								<div class="prc-default-options">
									<input type="radio" name="usr_action-<?php echo $wst['id'] ?>" value="rdb-all" id="rdb-all-<?php echo $wst['id']?>" /><label for="rdb-all-<?php echo $wst['id'] ?>"><?php echo _("Appliquer ce changement à tous les comptes existants (hors comptes synchronisés)."); ?></label><br />
									<input type="radio" name="usr_action-<?php echo $wst['id'] ?>" value="rdb-all-old" id="rdb-all-old-<?php echo $wst['id']?>" /><label for="rdb-all-old-<?php echo $wst['id'] ?>"><?php echo _("Appliquer ce changement à tous les comptes existants dont l'ancienne catégorie était celle par défaut (hors comptes synchronisés)."); ?></label><br />
									<input type="radio" name="usr_action-<?php echo $wst['id'] ?>" value="rdb-no" id="rdb-no-<?php echo $wst['id']?>" checked="checked" /><label for="rdb-no-<?php echo $wst['id'] ?>"><?php echo _("Ne modifier aucun compte client."); ?></label>
								</div>
							</td>
						</tr>
					<?php } ?>
				</tbody>
			</table>
			<p><?php echo _("La catégorie tarifaire des comptes synchronisés sera celle qui leur a été attribuée dans l'ERP."); ?></p>
			<input title="<?php echo _("Sauvegarder les modifications"); ?>" type="submit" class="action" value="<?php echo _("Enregistrer"); ?>" name="save-prc-default-wst" />
		</form>
	<?php } ?>

	<?php
	}

	/*if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_PRICE_DISCOUNT_PRIORITY') ){
	?>

	<h3><?php echo _("Priorité des conditions de remises"); ?></h3>

	<p><?php echo _("Fonctionnalité temporairement désactivé."); ?></p>
	
	<p>Triez ici les champs avancés disponibles afin de définir dans quel ordre vos taux de remises seront prises en compte.</p>

	<table id="tbl-dispo-fld" class="checklist">
		<caption>Champs disponibles pour les catégories tarifaires</caption>
		<col width="400" /><col width="100" />
		<thead>
			<tr>
				<!--th id="prc-apply"><th -->
				<th id="fld-name">Nom</th>
				<th id="prc-priority">Priorité</th>
			</tr>
		</thead>
		<tbody><?php
			$fields = fld_fields_get(0, 0, 0, 0, 0, 0, null, array('prc-priority'), true, array(), null, $class_array, true, true);

			if( $fields!=false && ria_mysql_num_rows($fields)>0 ){
				$count = $fields!=false ? ria_mysql_num_rows($fields)+1 : 1;
				$next = 1;
				$qte_managed = false;
				while( $field = ria_mysql_fetch_array($fields) ){
					if( $field['prc-priority']!=$next && !$qte_managed ){
						$qte_managed = true;
						print '<tr id="tr-fld-'.QTE_MIN_FLD_ID.'" class="ria-row-orderable'.( $count==1 ? ' last' : '' ).( $next==1 ? ' first' : '' ).'">';
						print '		<td headers="fld-name">Commandes => Quantité commandée du produit</td>';
						print '		<td headers="prc-priority" align="center" class="ria-cell-move">';
						print '	<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
						print '		</td>';
						print '</tr>';
						$next++;
						$count--;
					}

					print '<tr id="tr-fld-'.$field['id'].'" class="ria-row-orderable'.( $count==1 ? ' last' : '' ).( $next==1 ? ' first' : '' ).'">';
					print '		<td headers="name">'.htmlspecialchars(fld_classes_get_name($field['cls_id'])).' => '.htmlspecialchars($field['name']).'</td>';
					print '		<td headers="prc-priority" align="center" class="ria-cell-move">';
					print '			<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
					print '		</td>';
					print '</tr>';

					if( $field['prc-priority'] )
						$next = $field['prc-priority'] + 1;
					$count--;
				}

				if( !$qte_managed ){
					print '<tr id="tr-fld-'.QTE_MIN_FLD_ID.'" class="ria-row-orderable'.( $count==1 ? ' last' : '' ).( $next==1 ? ' first' : '' ).'">';
					print '		<td headers="name">Commandes => Quantité commandée du produit</td>';
					print '		<td headers="prc-priority" align="center" class="ria-cell-move">';
					print '			<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
					print '		</td>';
					print '</tr>';
					//$next++;
				}
			}
		?></tbody>
	</table>	

	}*/

	if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_PRICE_DISCOUNT') ){
	?>
	<h3><?php echo _("Calcul des remises"); ?></h3>
	<?php

		if( isset($error_success) ){
			print '<div class="error-success">'.nl2br(htmlspecialchars(_($error_success))).'</div>';
		}
	?>
	<p><?php echo _("Sélectionnez ici comment seront calculées vos remises :"); ?></p>
	<form method="post" action="index.php">

		<input title="<?php echo _("Le pourcentage de chaque remise est additionné pour former un pourcentage de remise globale"); ?>" type="radio" id="type-sum" name="type-calc" value="sum" class="radio" <?php print $config['discount_apply_type']==0 ? 'checked="checked"' : ''; ?> />
		<label title="<?php echo _("Le pourcentage de chaque remise est additionné pour former un pourcentage de remise globale"); ?>" for="type-sum" class="inline"><?php echo _("Additionnées"); ?></label>
		<input title="<?php echo _("Chaque taux de remise est appliqué séparement à partir du tarif de base, du général au particulier"); ?>" type="radio" id="type-cascade" name="type-calc" value="cascade" class="radio" <?php print $config['discount_apply_type']==1 ? 'checked="checked"' : ''; ?> />
		<label title="<?php echo _("Chaque taux de remise est appliqué séparement à partir du tarif de base, du général au particulier"); ?>" for="type-cascade" class="inline"><?php echo _("En cascade"); ?></label>
		<input title="<?php echo _("La meilleure remise, qu'elle soit en valeur ou en porucentage, sera la seule appliquée"); ?>" type="radio" id="type-best" name="type-calc" value="best" class="radio" <?php print $config['discount_apply_type']==2 ? 'checked="checked"' : ''; ?> />
		<label title="<?php echo _("La meilleure remise, qu'elle soit en valeur ou en porucentage, sera la seule appliquée"); ?>" for="type-best" class="inline"><?php echo _("Meilleure remise"); ?></label>
		<br/>
		<input title="<?php echo _("sauvegarder les modifications"); ?>" type="submit" class="action" value="<?php echo _("Enregistrer"); ?>" name="save-type-calcul" />

	</form>

	<?php
	}

	if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_PRICE_EXCLUDE') ){
		// Charge la liste des produits exclus / d'exception
		$rExcluded = cfg_overrides_get( 0, array(), 'pmt_excluded_auto' );

	?>

		
	<?php
		// pour l'affichage du nbr de produit dans le caption
		$countExcluded = 0;
		if( $rExcluded && ria_mysql_num_rows($rExcluded) ){
			$excluded = mysql_fetch_assoc($rExcluded);
			$listPrd = json_decode($excluded['value'],true);
			$countExcluded = count($listPrd['id']);
		}
	;?>
	<h4><?php printf( _("Exclure des produits des promotions (%d)"), $countExcluded ); ?></h4>
	<form method="post" action="index.php">
	
	<table id="table-produits-exclure" class="checklist">
			<thead>
				<tr>
					<th id="prd-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
					<th id="prd-name"><?php echo _("Référence - Désignation"); ?></th>
				</tr>
			</thead>
			<tbody>
			<?php
				$pass = false;
				if( $rExcluded && ria_mysql_num_rows($rExcluded) ){		
					foreach( $listPrd['ref'] as $prd ){
						$pass = true;
						$rPrd = prd_products_get_simple( 0, $prd );
						$prd = mysql_fetch_assoc( $rPrd );

						echo '
							<tr>
								<td headers="prd-sel"><input type="checkbox" class="checkbox" name="prd[]" value="'.$prd['ref'].'" /></td>
								<td headers="prd-name">'.view_prd_is_sync( $prd ).'<a href="/admin/catalog/product.php?cat=0&amp;prd='.$prd['id'].'">'.htmlspecialchars( $prd['ref'] ).' - '.htmlspecialchars( $prd['name'] ).'</a></td>
							</tr>
						';
					}
				}
				if( !$pass ){
				?>
					<tr>
						<td colspan="2" ><?php echo _("Aucun article exclu de toutes promotions"); ?></td>
					</tr>
				<?php
				}

			?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="2" class="align-left">
						<?php if( $rExcluded && ria_mysql_num_rows($rExcluded) ){ ?>
						<input type="submit" name="del-prd" class="btn-del" value="<?php echo _("Supprimer"); ?>" onclick="return prdConfirmDelList()" />
						<?php } ?>
						<div class="float-right">
							<label for="name"><?php echo _("Ajouter un produit :"); ?></label> <input type="text" name="ref-prd" id="ref" maxlength="75" />
							<input type="submit" value="<?php echo _("Ajouter"); ?>" onclick="return document.getElementById('ref').value!='';" />
							<input type="button" value="<?php echo _("Choisir"); ?>" onclick="return displayPopup( 'Sélectionner un produit', '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&amp;publish_search=0' );"/>
						</div>
					</td>
				</tr>
			</tfoot>
		</table>
	</form>

	<?php 
	} 

	if( $config['admin_pipeline_marge_direct'] ){
		$config['taux_commission']	= isset($config['taux_commission']) 	? $config['taux_commission'] 	: 0;
		// Gestion du pourcentage pour le calcul de la commision
	?>
		<h4><?php echo _("Calcul de la commission"); ?></h4>
		<form method="post" action="index.php">
			<?php print _('Veuillez choisir le taux (%) pour le calcul de la commission'); ?> <input type="number" name="taux_commission" step="0.1" value="<?php print $config['taux_commission']; ?>">
			<button name="save_commission"><?php print _('Sauvegarder'); ?></button>
		</form>
	<?php
	}

	require_once('admin/skin/footer.inc.php');
?>
