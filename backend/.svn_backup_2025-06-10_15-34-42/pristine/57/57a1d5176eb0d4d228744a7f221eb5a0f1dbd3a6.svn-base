 # SwaggerClient-php
 ### The REST API of BeezUP system 
 ##### Overview 
 The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  
The main features are: 
 - Register and manage your account 
 - Create and manage and share your stores with your friends/co-workers. 
 - Import your product catalog and schedule the auto importation - Search the channels your want to use 
 - Configure your channels for your catalogs to export your product information:     
 - cost and general settings    
 - category and columns mappings     
 - your will be able to create and manage your custom column    
 - put in place exlusion filters based on simple conditions on your product data     
 - override product values     
 - get product vision for a channel catalog scope 
 - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. 
 - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:
 - Synchronize your orders in an uniformized way     
 -  Get the available actions and update the order status - ...and more!  

### Authentication credentials 
The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com). 

The user API with the base path **_/v2/user** requires a token which is available on this page:
https://go.beezup.com/Account/MyAccount  
### Things to keep in mind
##### API Rate Limits 
The BeezUP REST API is limited to 100 calls/minute. 

#####  Media type 
The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json

##### Required content type 
The required and default encoding for the request and responses is UTF8. 

##### Required date time format
All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.

##### Base URL 
The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)

This PHP package is automatically generated by the [Swagger Codegen](https://github.com/swagger-api/swagger-codegen) project:

- API version: 2.0
- Build package: io.swagger.codegen.languages.PhpClientCodegen

## Requirements

PHP 5.5 and later

## Installation & Usage
### Composer

To install the bindings via [Composer](http://getcomposer.org/), add the following to `composer.json`:

```
{
  "repositories": [
    {
      "type": "git",
      "url": "https://github.com/BeezUP/api-php-client.git"
    }
  ],
  "require": {
    "beezup/api-php-client": "*@dev"
  }
}
```

Then run `composer install`

### Manual Installation

Download the files and include `autoload.php`:

```php
    require_once('/path/to/SwaggerClient-php/vendor/autoload.php');
```

## Tests

To run the unit tests:

```
composer install
./vendor/bin/phpunit
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');

// Configure API key authorization: api_key
$config = Swagger\Client\Configuration::getDefaultConfiguration()->setApiKey('Ocp-Apim-Subscription-Key', 'YOUR_API_KEY');
// Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
// $config = Swagger\Client\Configuration::getDefaultConfiguration()->setApiKeyPrefix('Ocp-Apim-Subscription-Key', 'Bearer');

$apiInstance = new Swagger\Client\Api\CatalogsAutoApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$store_id = "store_id_example"; // string | Your store identifier
$request = new \Swagger\Client\Model\ConfigureAutoImportIntervalRequest(); // \Swagger\Client\Model\ConfigureAutoImportIntervalRequest | 

try {
    $apiInstance->autoConfigureAutoImportInterval($store_id, $request);
} catch (Exception $e) {
    echo 'Exception when calling CatalogsAutoApi->autoConfigureAutoImportInterval: ', $e->getMessage(), PHP_EOL;
}

?>
```

## Documentation for API Endpoints

All URIs are relative to *https://api.beezup.com/v2*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*CatalogsAutoApi* | [**autoConfigureAutoImportInterval**](docs/Api/CatalogsAutoApi.md#autoconfigureautoimportinterval) | **POST** /user/catalogs/{storeId}/autoImport/scheduling/interval | Configure Auto Import Interval
*CatalogsAutoApi* | [**autoDeleteAutoImport**](docs/Api/CatalogsAutoApi.md#autodeleteautoimport) | **DELETE** /user/catalogs/{storeId}/autoImport | Delete Auto Import
*CatalogsAutoApi* | [**autoGetAutoImportConfiguration**](docs/Api/CatalogsAutoApi.md#autogetautoimportconfiguration) | **GET** /user/catalogs/{storeId}/autoImport | Get the auto import configuration
*CatalogsAutoApi* | [**autoPauseAutoImport**](docs/Api/CatalogsAutoApi.md#autopauseautoimport) | **POST** /user/catalogs/{storeId}/autoImport/pause | Pause Auto Import
*CatalogsAutoApi* | [**autoResumeAutoImport**](docs/Api/CatalogsAutoApi.md#autoresumeautoimport) | **POST** /user/catalogs/{storeId}/autoImport/resume | Resume Auto Import
*CatalogsAutoApi* | [**autoScheduleAutoImport**](docs/Api/CatalogsAutoApi.md#autoscheduleautoimport) | **POST** /user/catalogs/{storeId}/autoImport/scheduling/schedules | Configure Auto Import Schedules
*CatalogsAutoApi* | [**autoStartAutoImport**](docs/Api/CatalogsAutoApi.md#autostartautoimport) | **POST** /user/catalogs/{storeId}/autoImport/start | Start Auto Import Manually
*CatalogsAutoApi* | [**importationActivateAutoImport**](docs/Api/CatalogsAutoApi.md#importationactivateautoimport) | **POST** /user/catalogs/{storeId}/autoImport/activate | Activate the auto importation of the last successful manual catalog importation.
*CatalogsCatalogApi* | [**catalogChangeCatalogColumnUserName**](docs/Api/CatalogsCatalogApi.md#catalogchangecatalogcolumnusername) | **POST** /user/catalogs/{storeId}/catalogColumns/{columnId}/rename | Change Catalog Column User Name
*CatalogsCatalogApi* | [**catalogChangeCustomColumnExpression**](docs/Api/CatalogsCatalogApi.md#catalogchangecustomcolumnexpression) | **PUT** /user/catalogs/{storeId}/customColumns/{columnId}/expression | Change custom column expression
*CatalogsCatalogApi* | [**catalogChangeCustomColumnUserName**](docs/Api/CatalogsCatalogApi.md#catalogchangecustomcolumnusername) | **POST** /user/catalogs/{storeId}/customColumns/{columnId}/rename | Change Custom Column User Name
*CatalogsCatalogApi* | [**catalogComputeExpression**](docs/Api/CatalogsCatalogApi.md#catalogcomputeexpression) | **POST** /user/catalogs/{storeId}/customColumns/computeExpression | Compute the expression for this catalog.
*CatalogsCatalogApi* | [**catalogDeleteCustomColumn**](docs/Api/CatalogsCatalogApi.md#catalogdeletecustomcolumn) | **DELETE** /user/catalogs/{storeId}/customColumns/{columnId} | Delete custom column
*CatalogsCatalogApi* | [**catalogGetCatalogColumns**](docs/Api/CatalogsCatalogApi.md#cataloggetcatalogcolumns) | **GET** /user/catalogs/{storeId}/catalogColumns | Get catalog column list
*CatalogsCatalogApi* | [**catalogGetCategories**](docs/Api/CatalogsCatalogApi.md#cataloggetcategories) | **GET** /user/catalogs/{storeId}/categories | Get category list
*CatalogsCatalogApi* | [**catalogGetCustomColumnExpression**](docs/Api/CatalogsCatalogApi.md#cataloggetcustomcolumnexpression) | **GET** /user/catalogs/{storeId}/customColumns/{columnId}/expression | Get the encrypted custom column expression
*CatalogsCatalogApi* | [**catalogGetCustomColumns**](docs/Api/CatalogsCatalogApi.md#cataloggetcustomcolumns) | **GET** /user/catalogs/{storeId}/customColumns | Get custom column list
*CatalogsCatalogApi* | [**catalogGetProductByProductId**](docs/Api/CatalogsCatalogApi.md#cataloggetproductbyproductid) | **GET** /user/catalogs/{storeId}/products/{productId} | Get product by ProductId
*CatalogsCatalogApi* | [**catalogGetProductBySku**](docs/Api/CatalogsCatalogApi.md#cataloggetproductbysku) | **GET** /user/catalogs/{storeId}/products | Get product by Sku
*CatalogsCatalogApi* | [**catalogGetProducts**](docs/Api/CatalogsCatalogApi.md#cataloggetproducts) | **POST** /user/catalogs/{storeId}/products/list | Get product list
*CatalogsCatalogApi* | [**catalogGetRandomProducts**](docs/Api/CatalogsCatalogApi.md#cataloggetrandomproducts) | **GET** /user/catalogs/{storeId}/products/random | Get random product list
*CatalogsCatalogApi* | [**catalogSaveCustomColumn**](docs/Api/CatalogsCatalogApi.md#catalogsavecustomcolumn) | **PUT** /user/catalogs/{storeId}/customColumns/{columnId} | Create or replace a custom column
*CatalogsCatalogApi* | [**catalogStoreIndex**](docs/Api/CatalogsCatalogApi.md#catalogstoreindex) | **GET** /user/catalogs/{storeId} | Get the index of the catalog API for this store
*CatalogsCatalogApi* | [**importationGetManualUpdateLastInputConfig**](docs/Api/CatalogsCatalogApi.md#importationgetmanualupdatelastinputconfig) | **GET** /user/catalogs/{storeId}/inputConfiguration | Get the last input configuration
*CatalogsGlobalApi* | [**catalogGetBeezUPColumns**](docs/Api/CatalogsGlobalApi.md#cataloggetbeezupcolumns) | **GET** /user/catalogs/beezupColumns | Get the BeezUP columns
*CatalogsGlobalApi* | [**catalogIndex**](docs/Api/CatalogsGlobalApi.md#catalogindex) | **GET** /user/catalogs/ | Get the index of the catalog API
*CatalogsImportationCatalogInfoApi* | [**importationConfigureCatalogColumn**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationconfigurecatalogcolumn) | **POST** /user/catalogs/{storeId}/importations/{executionId}/catalogColumns/{columnId} | Configure catalog column
*CatalogsImportationCatalogInfoApi* | [**importationDeleteCustomColumn**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationdeletecustomcolumn) | **DELETE** /user/catalogs/{storeId}/importations/{executionId}/customColumns/{columnId} | Delete Custom Column
*CatalogsImportationCatalogInfoApi* | [**importationGetCustomColumnExpression**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationgetcustomcolumnexpression) | **GET** /user/catalogs/{storeId}/importations/{executionId}/customColumns/{columnId}/expression | Get the encrypted custom column expression in this importation
*CatalogsImportationCatalogInfoApi* | [**importationGetCustomColumns**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationgetcustomcolumns) | **GET** /user/catalogs/{storeId}/importations/{executionId}/customColumns | Get custom columns currently place in this importation
*CatalogsImportationCatalogInfoApi* | [**importationGetDetectedCatalogColumns**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationgetdetectedcatalogcolumns) | **GET** /user/catalogs/{storeId}/importations/{executionId}/catalogColumns | Get detected catalog columns during this importation.
*CatalogsImportationCatalogInfoApi* | [**importationGetProductSample**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationgetproductsample) | **GET** /user/catalogs/{storeId}/importations/{executionId}/productSamples/{productSampleIndex} | Get the product sample related to this importation with all columns (catalog and custom)
*CatalogsImportationCatalogInfoApi* | [**importationGetProductSampleCustomColumnValue**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationgetproductsamplecustomcolumnvalue) | **GET** /user/catalogs/{storeId}/importations/{executionId}/productSamples/{productSampleIndex}/customColumns/{columnId} | Get product sample custom column value related to this importation.
*CatalogsImportationCatalogInfoApi* | [**importationIgnoreColumn**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationignorecolumn) | **POST** /user/catalogs/{storeId}/importations/{executionId}/catalogColumns/{columnId}/ignore | Ignore Column
*CatalogsImportationCatalogInfoApi* | [**importationMapCatalogColumn**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationmapcatalogcolumn) | **POST** /user/catalogs/{storeId}/importations/{executionId}/catalogColumns/{columnId}/map | Map catalog column to a BeezUP column
*CatalogsImportationCatalogInfoApi* | [**importationMapCustomColumn**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationmapcustomcolumn) | **POST** /user/catalogs/{storeId}/importations/{executionId}/customColumns/{columnId}/map | Map custom column to a BeezUP column
*CatalogsImportationCatalogInfoApi* | [**importationReattendColumn**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationreattendcolumn) | **POST** /user/catalogs/{storeId}/importations/{executionId}/catalogColumns/{columnId}/reattend | Reattend Column
*CatalogsImportationCatalogInfoApi* | [**importationSaveCustomColumn**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationsavecustomcolumn) | **PUT** /user/catalogs/{storeId}/importations/{executionId}/customColumns/{columnId} | Create or replace a custom column
*CatalogsImportationCatalogInfoApi* | [**importationUnmapCatalogColumn**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationunmapcatalogcolumn) | **POST** /user/catalogs/{storeId}/importations/{executionId}/catalogColumns/{columnId}/unmap | Unmap catalog column
*CatalogsImportationCatalogInfoApi* | [**importationUnmapCustomColumn**](docs/Api/CatalogsImportationCatalogInfoApi.md#importationunmapcustomcolumn) | **POST** /user/catalogs/{storeId}/importations/{executionId}/customColumns/{columnId}/unmap | Unmap custom column
*CatalogsImportationProcessApi* | [**importationCancel**](docs/Api/CatalogsImportationProcessApi.md#importationcancel) | **POST** /user/catalogs/{storeId}/importations/{executionId}/cancel | Cancel importation
*CatalogsImportationProcessApi* | [**importationCommit**](docs/Api/CatalogsImportationProcessApi.md#importationcommit) | **POST** /user/catalogs/{storeId}/importations/{executionId}/commit | Commit Importation
*CatalogsImportationProcessApi* | [**importationCommitColumns**](docs/Api/CatalogsImportationProcessApi.md#importationcommitcolumns) | **POST** /user/catalogs/{storeId}/importations/{executionId}/commitColumns | Commit columns
*CatalogsImportationProcessApi* | [**importationConfigureRemainingCatalogColumns**](docs/Api/CatalogsImportationProcessApi.md#importationconfigureremainingcatalogcolumns) | **POST** /user/catalogs/{storeId}/importations/{executionId}/configureRemainingCatalogColumns | Configure remaining catalog columns
*CatalogsImportationProcessApi* | [**importationGetImportationMonitoring**](docs/Api/CatalogsImportationProcessApi.md#importationgetimportationmonitoring) | **GET** /user/catalogs/{storeId}/importations/{executionId} | Get the importation status
*CatalogsImportationProcessApi* | [**importationGetReportings**](docs/Api/CatalogsImportationProcessApi.md#importationgetreportings) | **GET** /user/catalogs/{storeId}/importations | Get the latest catalog importation reporting
*CatalogsImportationProcessApi* | [**importationStartManualUpdate**](docs/Api/CatalogsImportationProcessApi.md#importationstartmanualupdate) | **POST** /user/catalogs/{storeId}/importations/start | Start Manual Import
*CatalogsImportationProcessApi* | [**importationTechnicalProgression**](docs/Api/CatalogsImportationProcessApi.md#importationtechnicalprogression) | **GET** /user/catalogs/{storeId}/importations/{executionId}/technicalProgression | Get technical progression
*ChannelCatalogsCategoriesApi* | [**configureChannelCatalogCategory**](docs/Api/ChannelCatalogsCategoriesApi.md#configurechannelcatalogcategory) | **POST** /user/channelCatalogs/{channelCatalogId}/categories/configure | Configure channel catalog category
*ChannelCatalogsCategoriesApi* | [**disableChannelCatalogCategoryMapping**](docs/Api/ChannelCatalogsCategoriesApi.md#disablechannelcatalogcategorymapping) | **POST** /user/channelCatalogs/{channelCatalogId}/categories/disableMapping | Disable a channel catalog category mapping
*ChannelCatalogsCategoriesApi* | [**getChannelCatalogCategories**](docs/Api/ChannelCatalogsCategoriesApi.md#getchannelcatalogcategories) | **GET** /user/channelCatalogs/{channelCatalogId}/categories | Get channel catalog categories
*ChannelCatalogsCategoriesApi* | [**reenableChannelCatalogCategoryMapping**](docs/Api/ChannelCatalogsCategoriesApi.md#reenablechannelcatalogcategorymapping) | **POST** /user/channelCatalogs/{channelCatalogId}/categories/reenableMapping | Reenable a channel catalog category mapping
*ChannelCatalogsColumnMappingsApi* | [**configureChannelCatalogColumnMappings**](docs/Api/ChannelCatalogsColumnMappingsApi.md#configurechannelcatalogcolumnmappings) | **PUT** /user/channelCatalogs/{channelCatalogId}/columnMappings | Configure channel catalog column mappings
*ChannelCatalogsExclusionFiltersApi* | [**configureChannelCatalogExclusionFilters**](docs/Api/ChannelCatalogsExclusionFiltersApi.md#configurechannelcatalogexclusionfilters) | **PUT** /user/channelCatalogs/{channelCatalogId}/exclusionFilters | Configure channel catalog exclusion filters
*ChannelCatalogsExclusionFiltersApi* | [**getChannelCatalogExclusionFilterOperators**](docs/Api/ChannelCatalogsExclusionFiltersApi.md#getchannelcatalogexclusionfilteroperators) | **GET** /user/channelCatalogs/exclusionFilterOperators | Get channel catalog exclusion filter operators
*ChannelCatalogsExclusionFiltersApi* | [**getChannelCatalogExclusionFilters**](docs/Api/ChannelCatalogsExclusionFiltersApi.md#getchannelcatalogexclusionfilters) | **GET** /user/channelCatalogs/{channelCatalogId}/exclusionFilters | Get channel catalog exclusion filters
*ChannelCatalogsExportationsApi* | [**clearChannelCatalogExportationCache**](docs/Api/ChannelCatalogsExportationsApi.md#clearchannelcatalogexportationcache) | **POST** /user/channelCatalogs/{channelCatalogId}/exportations/cache/clear | Clear the exportation cache
*ChannelCatalogsExportationsApi* | [**getChannelCatalogExportationCacheInfo**](docs/Api/ChannelCatalogsExportationsApi.md#getchannelcatalogexportationcacheinfo) | **GET** /user/channelCatalogs/{channelCatalogId}/exportations/cache | Get the exportation cache information
*ChannelCatalogsExportationsApi* | [**getChannelCatalogExportationHistory**](docs/Api/ChannelCatalogsExportationsApi.md#getchannelcatalogexportationhistory) | **GET** /user/channelCatalogs/{channelCatalogId}/exportations/history | Get the exportation history
*ChannelCatalogsGlobalApi* | [**addChannelCatalog**](docs/Api/ChannelCatalogsGlobalApi.md#addchannelcatalog) | **POST** /user/channelCatalogs/ | Add a new channel catalog
*ChannelCatalogsGlobalApi* | [**deleteChannelCatalog**](docs/Api/ChannelCatalogsGlobalApi.md#deletechannelcatalog) | **DELETE** /user/channelCatalogs/{channelCatalogId} | Delete the channel catalog
*ChannelCatalogsGlobalApi* | [**getChannelCatalog**](docs/Api/ChannelCatalogsGlobalApi.md#getchannelcatalog) | **GET** /user/channelCatalogs/{channelCatalogId} | Get the channel catalog information
*ChannelCatalogsGlobalApi* | [**getChannelCatalogs**](docs/Api/ChannelCatalogsGlobalApi.md#getchannelcatalogs) | **GET** /user/channelCatalogs/ | List all your current channel catalogs
*ChannelCatalogsProductsApi* | [**exportChannelCatalogProductInfoList**](docs/Api/ChannelCatalogsProductsApi.md#exportchannelcatalogproductinfolist) | **POST** /user/channelCatalogs/{channelCatalogId}/products/export | Export channel catalog product information list
*ChannelCatalogsProductsApi* | [**getChannelCatalogProductByChannelCatalog**](docs/Api/ChannelCatalogsProductsApi.md#getchannelcatalogproductbychannelcatalog) | **POST** /user/channelCatalogs/products | Get channel catalog products related to these channel catalogs
*ChannelCatalogsProductsApi* | [**getChannelCatalogProductInfo**](docs/Api/ChannelCatalogsProductsApi.md#getchannelcatalogproductinfo) | **GET** /user/channelCatalogs/{channelCatalogId}/products/{productId} | Get channel catalog product information
*ChannelCatalogsProductsApi* | [**getChannelCatalogProductInfoList**](docs/Api/ChannelCatalogsProductsApi.md#getchannelcatalogproductinfolist) | **POST** /user/channelCatalogs/{channelCatalogId}/products | Get channel catalog product information list
*ChannelCatalogsProductsApi* | [**getChannelCatalogProductsCounters**](docs/Api/ChannelCatalogsProductsApi.md#getchannelcatalogproductscounters) | **GET** /user/channelCatalogs/{channelCatalogId}/products/counters | Get channel catalog products&#39; counters
*ChannelCatalogsProductsOptimisationApi* | [**disableChannelCatalogProduct**](docs/Api/ChannelCatalogsProductsOptimisationApi.md#disablechannelcatalogproduct) | **POST** /user/channelCatalogs/{channelCatalogId}/products/{productId}/disable | Disable channel catalog product
*ChannelCatalogsProductsOptimisationApi* | [**reenableChannelCatalogProduct**](docs/Api/ChannelCatalogsProductsOptimisationApi.md#reenablechannelcatalogproduct) | **POST** /user/channelCatalogs/{channelCatalogId}/products/{productId}/reenable | Reenable channel catalog product
*ChannelCatalogsProductsOverridesApi* | [**configureChannelCatalogProductValueOverrideCopy**](docs/Api/ChannelCatalogsProductsOverridesApi.md#configurechannelcatalogproductvalueoverridecopy) | **POST** /user/channelCatalogs/{channelCatalogId}/products/{productId}/overrides/copy | Copy channel catalog product value override
*ChannelCatalogsProductsOverridesApi* | [**deleteChannelCatalogProductValueOverride**](docs/Api/ChannelCatalogsProductsOverridesApi.md#deletechannelcatalogproductvalueoverride) | **DELETE** /user/channelCatalogs/{channelCatalogId}/products/{productId}/overrides/{channelColumnId} | Delete a specific channel catalog product value override
*ChannelCatalogsProductsOverridesApi* | [**getChannelCatalogProductValueOverrideCopy**](docs/Api/ChannelCatalogsProductsOverridesApi.md#getchannelcatalogproductvalueoverridecopy) | **GET** /user/channelCatalogs/{channelCatalogId}/products/{productId}/overrides/copy | Get channel catalog product value override compatibilities status
*ChannelCatalogsProductsOverridesApi* | [**overrideChannelCatalogProductValues**](docs/Api/ChannelCatalogsProductsOverridesApi.md#overridechannelcatalogproductvalues) | **PUT** /user/channelCatalogs/{channelCatalogId}/products/{productId}/overrides | Override channel catalog product values
*ChannelCatalogsSettingsApi* | [**configureChannelCatalogCostSettings**](docs/Api/ChannelCatalogsSettingsApi.md#configurechannelcatalogcostsettings) | **PUT** /user/channelCatalogs/{channelCatalogId}/settings/cost | Configure channel catalog cost settings
*ChannelCatalogsSettingsApi* | [**configureChannelCatalogGeneralSettings**](docs/Api/ChannelCatalogsSettingsApi.md#configurechannelcataloggeneralsettings) | **PUT** /user/channelCatalogs/{channelCatalogId}/settings/general | Configure channel catalog general settings
*ChannelCatalogsSettingsApi* | [**disableChannelCatalog**](docs/Api/ChannelCatalogsSettingsApi.md#disablechannelcatalog) | **POST** /user/channelCatalogs/{channelCatalogId}/disable | Disable a channel catalog
*ChannelCatalogsSettingsApi* | [**enableChannelCatalog**](docs/Api/ChannelCatalogsSettingsApi.md#enablechannelcatalog) | **POST** /user/channelCatalogs/{channelCatalogId}/enable | Enable a channel catalog
*ChannelsChannelsGlobalApi* | [**getAvailableChannels**](docs/Api/ChannelsChannelsGlobalApi.md#getavailablechannels) | **GET** /user/channels/ | List all available channel for this store
*ChannelsChannelsGlobalApi* | [**getChannelCategories**](docs/Api/ChannelsChannelsGlobalApi.md#getchannelcategories) | **GET** /user/channels/{channelId}/categories | Get channel categories
*ChannelsChannelsGlobalApi* | [**getChannelColumns**](docs/Api/ChannelsChannelsGlobalApi.md#getchannelcolumns) | **POST** /user/channels/{channelId}/columns | Get channel columns
*ChannelsChannelsGlobalApi* | [**getChannelInfo**](docs/Api/ChannelsChannelsGlobalApi.md#getchannelinfo) | **GET** /user/channels/{channelId} | Get channel information
*CustomerAccountApi* | [**activateUserAccount**](docs/Api/CustomerAccountApi.md#activateuseraccount) | **POST** /user/customer/account/activate | Activate the user account
*CustomerAccountApi* | [**changePassword**](docs/Api/CustomerAccountApi.md#changepassword) | **POST** /user/customer/account/changePassword | Change user password
*CustomerAccountApi* | [**getCreditCardInfo**](docs/Api/CustomerAccountApi.md#getcreditcardinfo) | **GET** /user/customer/account/creditCardInfo | Get credit card information
*CustomerAccountApi* | [**getProfilePictureInfo**](docs/Api/CustomerAccountApi.md#getprofilepictureinfo) | **GET** /user/customer/account/profilePictureInfo | Get profile picture information
*CustomerAccountApi* | [**getUserAccountInfo**](docs/Api/CustomerAccountApi.md#getuseraccountinfo) | **GET** /user/customer/account | Get user account information
*CustomerAccountApi* | [**resendEmailActivation**](docs/Api/CustomerAccountApi.md#resendemailactivation) | **POST** /user/customer/account/resendEmailActivation | Resend email activation
*CustomerAccountApi* | [**saveCompanyInfo**](docs/Api/CustomerAccountApi.md#savecompanyinfo) | **PUT** /user/customer/account/companyInfo | Change company information
*CustomerAccountApi* | [**saveCreditCardInfo**](docs/Api/CustomerAccountApi.md#savecreditcardinfo) | **PUT** /user/customer/account/creditCardInfo | Save user credit card info
*CustomerAccountApi* | [**savePersonalInfo**](docs/Api/CustomerAccountApi.md#savepersonalinfo) | **PUT** /user/customer/account/personalInfo | Save user personal information
*CustomerAccountApi* | [**saveProfilePictureInfo**](docs/Api/CustomerAccountApi.md#saveprofilepictureinfo) | **PUT** /user/customer/account/profilePictureInfo | Change user picture information
*CustomerAlertsApi* | [**getStoreAlerts**](docs/Api/CustomerAlertsApi.md#getstorealerts) | **GET** /user/customer/stores/{storeId}/alerts | Get store&#39;s alerts
*CustomerAlertsApi* | [**saveStoreAlerts**](docs/Api/CustomerAlertsApi.md#savestorealerts) | **POST** /user/customer/stores/{storeId}/alerts | Save store alerts
*CustomerContractsApi* | [**createContract**](docs/Api/CustomerContractsApi.md#createcontract) | **POST** /user/customer/contracts | Create a new contract
*CustomerContractsApi* | [**deleteNextContract**](docs/Api/CustomerContractsApi.md#deletenextcontract) | **DELETE** /user/customer/contracts/next | Delete your next contract
*CustomerContractsApi* | [**getBillingPeriods**](docs/Api/CustomerContractsApi.md#getbillingperiods) | **GET** /user/customer/billingPeriods | Get billing periods conditions
*CustomerContractsApi* | [**getContracts**](docs/Api/CustomerContractsApi.md#getcontracts) | **GET** /user/customer/contracts | Get contract list
*CustomerContractsApi* | [**getOffer**](docs/Api/CustomerContractsApi.md#getoffer) | **POST** /user/customer/offers | Get offer pricing
*CustomerContractsApi* | [**getStandardOffers**](docs/Api/CustomerContractsApi.md#getstandardoffers) | **GET** /user/customer/offers | Get all standard offers
*CustomerContractsApi* | [**reactivateCurrentContract**](docs/Api/CustomerContractsApi.md#reactivatecurrentcontract) | **POST** /user/customer/contracts/current/reenableAutoRenewal | Reactivate your terminated contract.
*CustomerContractsApi* | [**terminateCurrentContract**](docs/Api/CustomerContractsApi.md#terminatecurrentcontract) | **POST** /user/customer/contracts/current/disableAutoRenewal | Schedule termination of your current contract at the end of the commitment.
*CustomerFriendsApi* | [**getFriendInfo**](docs/Api/CustomerFriendsApi.md#getfriendinfo) | **GET** /user/customer/friends/{userId} | Get friend information
*CustomerGlobalApi* | [**getCustomerIndex**](docs/Api/CustomerGlobalApi.md#getcustomerindex) | **GET** /user/customer/ | The index of all operations and LOV
*CustomerInvoicesApi* | [**getInvoices**](docs/Api/CustomerInvoicesApi.md#getinvoices) | **GET** /user/customer/invoices | Get all your invoices
*CustomerRightsApi* | [**getRights**](docs/Api/CustomerRightsApi.md#getrights) | **GET** /user/customer/stores/{storeId}/rights | Get store&#39;s rights
*CustomerSecurityApi* | [**logout**](docs/Api/CustomerSecurityApi.md#logout) | **POST** /user/customer/security/logout | Log out the current user from go2
*CustomerSharesApi* | [**deleteStoreShare**](docs/Api/CustomerSharesApi.md#deletestoreshare) | **DELETE** /user/customer/stores/{storeId}/shares/{userId} | Delete a share of a store to another user
*CustomerSharesApi* | [**getStoreShares**](docs/Api/CustomerSharesApi.md#getstoreshares) | **GET** /user/customer/stores/{storeId}/shares | Get shares related to this store
*CustomerSharesApi* | [**shareStore**](docs/Api/CustomerSharesApi.md#sharestore) | **POST** /user/customer/stores/{storeId}/shares | Share a store to another user
*CustomerStoresApi* | [**createStore**](docs/Api/CustomerStoresApi.md#createstore) | **POST** /user/customer/stores | Create a new store
*CustomerStoresApi* | [**deleteStore**](docs/Api/CustomerStoresApi.md#deletestore) | **DELETE** /user/customer/stores/{storeId} | Delete a store
*CustomerStoresApi* | [**getStore**](docs/Api/CustomerStoresApi.md#getstore) | **GET** /user/customer/stores/{storeId} | Get store&#39;s information
*CustomerStoresApi* | [**getStores**](docs/Api/CustomerStoresApi.md#getstores) | **GET** /user/customer/stores | Get store list
*CustomerStoresApi* | [**updateStore**](docs/Api/CustomerStoresApi.md#updatestore) | **PATCH** /user/customer/stores/{storeId} | Update some store&#39;s information.
*MarketplacesChannelCatalogsGlobalApi* | [**getMarketplaceChannelCatalogs**](docs/Api/MarketplacesChannelCatalogsGlobalApi.md#getmarketplacechannelcatalogs) | **GET** /user/marketplaces/channelcatalogs/ | Get your marketplace channel catalog list
*MarketplacesChannelCatalogsPublicationsApi* | [**getPublications**](docs/Api/MarketplacesChannelCatalogsPublicationsApi.md#getpublications) | **GET** /user/marketplaces/channelcatalogs/publications/{marketplaceTechnicalCode}/{accountId}/history | Fetch the publication history for an account, sorted by descending start date
*MarketplacesChannelCatalogsSettingsApi* | [**getChannelCatalogMarketplaceProperties**](docs/Api/MarketplacesChannelCatalogsSettingsApi.md#getchannelcatalogmarketplaceproperties) | **GET** /user/marketplaces/channelcatalogs/{channelCatalogId}/properties | Get the marketplace properties for a channel catalog
*MarketplacesChannelCatalogsSettingsApi* | [**getChannelCatalogMarketplaceSettings**](docs/Api/MarketplacesChannelCatalogsSettingsApi.md#getchannelcatalogmarketplacesettings) | **GET** /user/marketplaces/channelcatalogs/{channelCatalogId}/settings | Get the marketplace settings for a channel catalog
*MarketplacesChannelCatalogsSettingsApi* | [**setChannelCatalogMarketplaceSettings**](docs/Api/MarketplacesChannelCatalogsSettingsApi.md#setchannelcatalogmarketplacesettings) | **POST** /user/marketplaces/channelcatalogs/{channelCatalogId}/settings | Save new marketplace settings for a channel catalog
*MarketplacesOrdersAutoTransitionsApi* | [**configureAutomaticTransitions**](docs/Api/MarketplacesOrdersAutoTransitionsApi.md#configureautomatictransitions) | **POST** /user/marketplaces/orders/automaticTransitions | Configure new or existing automatic Order status transition
*MarketplacesOrdersAutoTransitionsApi* | [**getAutomaticTransitions**](docs/Api/MarketplacesOrdersAutoTransitionsApi.md#getautomatictransitions) | **GET** /user/marketplaces/orders/automaticTransitions | Get list of configured automatic Order status transitions
*MarketplacesOrdersBatchesApi* | [**changeOrderList**](docs/Api/MarketplacesOrdersBatchesApi.md#changeorderlist) | **POST** /user/marketplaces/orders/batches/changeOrders/{changeOrderType} | Send a batch of operations to change your marketplace Order information (accept, ship, etc.)
*MarketplacesOrdersBatchesApi* | [**clearMerchantOrderInfoList**](docs/Api/MarketplacesOrdersBatchesApi.md#clearmerchantorderinfolist) | **POST** /user/marketplaces/orders/batches/clearMerchantOrderInfos | Send a batch of operations to clear an Order&#39;s merchant information (max 100 items per call)
*MarketplacesOrdersBatchesApi* | [**setMerchantOrderInfoList**](docs/Api/MarketplacesOrdersBatchesApi.md#setmerchantorderinfolist) | **POST** /user/marketplaces/orders/batches/setMerchantOrderInfos | Send a batch of operations to set an Order&#39;s merchant information  (max 100 items per call)
*MarketplacesOrdersExportsApi* | [**exportOrders**](docs/Api/MarketplacesOrdersExportsApi.md#exportorders) | **POST** /user/marketplaces/orders/exportations | Request a new Order report exportation to be generated
*MarketplacesOrdersExportsApi* | [**getOrderExportations**](docs/Api/MarketplacesOrdersExportsApi.md#getorderexportations) | **GET** /user/marketplaces/orders/exportations | Get a paginated list of Order report exportations
*MarketplacesOrdersGlobalApi* | [**getMarketplaceAccountsSynchronization**](docs/Api/MarketplacesOrdersGlobalApi.md#getmarketplaceaccountssynchronization) | **GET** /user/marketplaces/orders/status | Get current synchronization status between your marketplaces and BeezUP accounts
*MarketplacesOrdersGlobalApi* | [**getOrderIndex**](docs/Api/MarketplacesOrdersGlobalApi.md#getorderindex) | **GET** /user/marketplaces/orders/ | Get all actions you can do on the order API
*MarketplacesOrdersGlobalApi* | [**harvestAll**](docs/Api/MarketplacesOrdersGlobalApi.md#harvestall) | **POST** /user/marketplaces/orders/harvest | Send harvest request to all your marketplaces
*MarketplacesOrdersListApi* | [**getOrderListFull**](docs/Api/MarketplacesOrdersListApi.md#getorderlistfull) | **POST** /user/marketplaces/orders/list/full | Get a paginated list of all Orders with all Order and Order Item(s) properties
*MarketplacesOrdersListApi* | [**getOrderListLight**](docs/Api/MarketplacesOrdersListApi.md#getorderlistlight) | **POST** /user/marketplaces/orders/list/light | Get a paginated list of all Orders without details
*MarketplacesOrdersOrderApi* | [**changeOrder**](docs/Api/MarketplacesOrdersOrderApi.md#changeorder) | **POST** /user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/{changeOrderType} | Change your marketplace Order Information (accept, ship, etc.)
*MarketplacesOrdersOrderApi* | [**clearMerchantOrderInfo**](docs/Api/MarketplacesOrdersOrderApi.md#clearmerchantorderinfo) | **POST** /user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/clearMerchantOrderInfo | Clear an Order&#39;s merchant information
*MarketplacesOrdersOrderApi* | [**getOrder**](docs/Api/MarketplacesOrdersOrderApi.md#getorder) | **GET** /user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId} | Get full Order and Order Item(s) properties
*MarketplacesOrdersOrderApi* | [**getOrderHistory**](docs/Api/MarketplacesOrdersOrderApi.md#getorderhistory) | **GET** /user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/history | Get an Order&#39;s harvest and change history
*MarketplacesOrdersOrderApi* | [**harvestOrder**](docs/Api/MarketplacesOrdersOrderApi.md#harvestorder) | **POST** /user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/harvest | Send harvest request for a single Order
*MarketplacesOrdersOrderApi* | [**headOrder**](docs/Api/MarketplacesOrdersOrderApi.md#headorder) | **HEAD** /user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId} | Get the meta information about the order (ETag, Last-Modified)
*MarketplacesOrdersOrderApi* | [**setMerchantOrderInfo**](docs/Api/MarketplacesOrdersOrderApi.md#setmerchantorderinfo) | **POST** /user/marketplaces/orders/{marketplaceTechnicalCode}/{accountId}/{beezUPOrderId}/setMerchantOrderInfo | Set an Order&#39;s merchant information
*PublicChannelsPublicChannelsApi* | [**getChannels**](docs/Api/PublicChannelsPublicChannelsApi.md#getchannels) | **GET** /public/channels/{countryIsoCode} | The channel list for one country
*PublicChannelsPublicChannelsApi* | [**getChannelsIndex**](docs/Api/PublicChannelsPublicChannelsApi.md#getchannelsindex) | **GET** /public/channels/ | Get public channel index
*PublicListOfValuesLOVApi* | [**getPublicListOfValues**](docs/Api/PublicListOfValuesLOVApi.md#getpubliclistofvalues) | **GET** /public/lov/{listName} | Get the list of values related to this list name
*PublicListOfValuesLOVApi* | [**getPublicLovIndex**](docs/Api/PublicListOfValuesLOVApi.md#getpubliclovindex) | **GET** /public/lov/ | Get all list names
*PublicSecuritySecurityApi* | [**login**](docs/Api/PublicSecuritySecurityApi.md#login) | **POST** /public/security/login | Login
*PublicSecuritySecurityApi* | [**lostPassword**](docs/Api/PublicSecuritySecurityApi.md#lostpassword) | **POST** /public/security/lostpassword | Lost password
*PublicSecuritySecurityApi* | [**register**](docs/Api/PublicSecuritySecurityApi.md#register) | **POST** /public/security/register | User Registration
*UserListOfValuesLOVApi* | [**getUserListOfValues**](docs/Api/UserListOfValuesLOVApi.md#getuserlistofvalues) | **GET** /user/lov/{listName} | Get the list of values related to this list name
*UserListOfValuesLOVApi* | [**getUserLovIndex**](docs/Api/UserListOfValuesLOVApi.md#getuserlovindex) | **GET** /user/lov/ | Get all list names


## Documentation For Models

 - [AccountId](docs/Model/AccountId.md)
 - [AccountInfo](docs/Model/AccountInfo.md)
 - [AccountInfoLinks](docs/Model/AccountInfoLinks.md)
 - [AccountPublications](docs/Model/AccountPublications.md)
 - [AccountPublicationsLinks](docs/Model/AccountPublicationsLinks.md)
 - [AccountStatus](docs/Model/AccountStatus.md)
 - [AccountSynchronization](docs/Model/AccountSynchronization.md)
 - [AccountSynchronizationList](docs/Model/AccountSynchronizationList.md)
 - [AccountingEmails](docs/Model/AccountingEmails.md)
 - [AddChannelCatalogRequest](docs/Model/AddChannelCatalogRequest.md)
 - [AdditionalAnalyticsProductColumnFilters](docs/Model/AdditionalAnalyticsProductColumnFilters.md)
 - [Address](docs/Model/Address.md)
 - [AlertEnabled](docs/Model/AlertEnabled.md)
 - [AlertId](docs/Model/AlertId.md)
 - [AlertName](docs/Model/AlertName.md)
 - [AlertPropertyId](docs/Model/AlertPropertyId.md)
 - [AlertPropertyValue](docs/Model/AlertPropertyValue.md)
 - [AnalyticsIndex](docs/Model/AnalyticsIndex.md)
 - [AnalyticsIndexLinks](docs/Model/AnalyticsIndexLinks.md)
 - [AnalyticsIndexLovLinks](docs/Model/AnalyticsIndexLovLinks.md)
 - [AnalyticsProductColumnFilter](docs/Model/AnalyticsProductColumnFilter.md)
 - [AnalyticsProductColumnFilterOperatorName](docs/Model/AnalyticsProductColumnFilterOperatorName.md)
 - [AnalyticsProductColumnFilters](docs/Model/AnalyticsProductColumnFilters.md)
 - [AnalyticsStoreIndex](docs/Model/AnalyticsStoreIndex.md)
 - [AnalyticsStoreIndexLinks](docs/Model/AnalyticsStoreIndexLinks.md)
 - [ApiCredential](docs/Model/ApiCredential.md)
 - [ApiCredentials](docs/Model/ApiCredentials.md)
 - [AutoImportConfiguration](docs/Model/AutoImportConfiguration.md)
 - [AutoImportConfigurationLinks](docs/Model/AutoImportConfigurationLinks.md)
 - [AutomaticTransition](docs/Model/AutomaticTransition.md)
 - [AutomaticTransitionInfoLinks](docs/Model/AutomaticTransitionInfoLinks.md)
 - [AutomaticTransitionInfoList](docs/Model/AutomaticTransitionInfoList.md)
 - [AutomaticTransitionInfoListLinks](docs/Model/AutomaticTransitionInfoListLinks.md)
 - [BatchOrderOperationResponse](docs/Model/BatchOrderOperationResponse.md)
 - [BeezUPColumnConfiguration](docs/Model/BeezUPColumnConfiguration.md)
 - [BeezUPCommonAdditionalProductFilters](docs/Model/BeezUPCommonAdditionalProductFilters.md)
 - [BeezUPCommonAdditionalProductFiltersValue](docs/Model/BeezUPCommonAdditionalProductFiltersValue.md)
 - [BeezUPCommonApiSettingsStatus](docs/Model/BeezUPCommonApiSettingsStatus.md)
 - [BeezUPCommonBeezUPColumnName](docs/Model/BeezUPCommonBeezUPColumnName.md)
 - [BeezUPCommonCatalogCategoryFilter](docs/Model/BeezUPCommonCatalogCategoryFilter.md)
 - [BeezUPCommonCatalogCategoryId](docs/Model/BeezUPCommonCatalogCategoryId.md)
 - [BeezUPCommonCatalogCategoryPath](docs/Model/BeezUPCommonCatalogCategoryPath.md)
 - [BeezUPCommonCatalogColumnId](docs/Model/BeezUPCommonCatalogColumnId.md)
 - [BeezUPCommonCatalogColumnUserName](docs/Model/BeezUPCommonCatalogColumnUserName.md)
 - [BeezUPCommonChannelBasicInfo](docs/Model/BeezUPCommonChannelBasicInfo.md)
 - [BeezUPCommonChannelCatalogId](docs/Model/BeezUPCommonChannelCatalogId.md)
 - [BeezUPCommonChannelCategoryFilter](docs/Model/BeezUPCommonChannelCategoryFilter.md)
 - [BeezUPCommonChannelCategoryId](docs/Model/BeezUPCommonChannelCategoryId.md)
 - [BeezUPCommonChannelCategoryPath](docs/Model/BeezUPCommonChannelCategoryPath.md)
 - [BeezUPCommonChannelColumnId](docs/Model/BeezUPCommonChannelColumnId.md)
 - [BeezUPCommonChannelColumnName](docs/Model/BeezUPCommonChannelColumnName.md)
 - [BeezUPCommonChannelId](docs/Model/BeezUPCommonChannelId.md)
 - [BeezUPCommonChannelName](docs/Model/BeezUPCommonChannelName.md)
 - [BeezUPCommonColumnDataType](docs/Model/BeezUPCommonColumnDataType.md)
 - [BeezUPCommonColumnImportance](docs/Model/BeezUPCommonColumnImportance.md)
 - [BeezUPCommonCountryIsoCodeAlpha3](docs/Model/BeezUPCommonCountryIsoCodeAlpha3.md)
 - [BeezUPCommonCurrencyCode](docs/Model/BeezUPCommonCurrencyCode.md)
 - [BeezUPCommonDocUrl](docs/Model/BeezUPCommonDocUrl.md)
 - [BeezUPCommonEmail](docs/Model/BeezUPCommonEmail.md)
 - [BeezUPCommonErrorResponseMessage](docs/Model/BeezUPCommonErrorResponseMessage.md)
 - [BeezUPCommonErrorSummary](docs/Model/BeezUPCommonErrorSummary.md)
 - [BeezUPCommonExceptionDetail](docs/Model/BeezUPCommonExceptionDetail.md)
 - [BeezUPCommonHref](docs/Model/BeezUPCommonHref.md)
 - [BeezUPCommonHttpMethod](docs/Model/BeezUPCommonHttpMethod.md)
 - [BeezUPCommonHttpUrl](docs/Model/BeezUPCommonHttpUrl.md)
 - [BeezUPCommonInfoSummaries](docs/Model/BeezUPCommonInfoSummaries.md)
 - [BeezUPCommonInfoSummary](docs/Model/BeezUPCommonInfoSummary.md)
 - [BeezUPCommonLOVLink2](docs/Model/BeezUPCommonLOVLink2.md)
 - [BeezUPCommonLOVLink3](docs/Model/BeezUPCommonLOVLink3.md)
 - [BeezUPCommonLink2](docs/Model/BeezUPCommonLink2.md)
 - [BeezUPCommonLink3](docs/Model/BeezUPCommonLink3.md)
 - [BeezUPCommonLinkParameter2](docs/Model/BeezUPCommonLinkParameter2.md)
 - [BeezUPCommonLinkParameter3](docs/Model/BeezUPCommonLinkParameter3.md)
 - [BeezUPCommonLinkParameterProperty3](docs/Model/BeezUPCommonLinkParameterProperty3.md)
 - [BeezUPCommonListOfValueItem](docs/Model/BeezUPCommonListOfValueItem.md)
 - [BeezUPCommonMarketplaceAccountId](docs/Model/BeezUPCommonMarketplaceAccountId.md)
 - [BeezUPCommonMarketplaceBusinessCode](docs/Model/BeezUPCommonMarketplaceBusinessCode.md)
 - [BeezUPCommonMarketplaceTechnicalCode](docs/Model/BeezUPCommonMarketplaceTechnicalCode.md)
 - [BeezUPCommonOperationId](docs/Model/BeezUPCommonOperationId.md)
 - [BeezUPCommonPageNumber](docs/Model/BeezUPCommonPageNumber.md)
 - [BeezUPCommonPageSize](docs/Model/BeezUPCommonPageSize.md)
 - [BeezUPCommonPaginationRequestParameters](docs/Model/BeezUPCommonPaginationRequestParameters.md)
 - [BeezUPCommonPaginationResult](docs/Model/BeezUPCommonPaginationResult.md)
 - [BeezUPCommonPaginationResultLinks](docs/Model/BeezUPCommonPaginationResultLinks.md)
 - [BeezUPCommonParameterIn](docs/Model/BeezUPCommonParameterIn.md)
 - [BeezUPCommonParameterType](docs/Model/BeezUPCommonParameterType.md)
 - [BeezUPCommonProductBasicInfo](docs/Model/BeezUPCommonProductBasicInfo.md)
 - [BeezUPCommonProductColumnFilterOperatorName](docs/Model/BeezUPCommonProductColumnFilterOperatorName.md)
 - [BeezUPCommonProductFilters](docs/Model/BeezUPCommonProductFilters.md)
 - [BeezUPCommonProductId](docs/Model/BeezUPCommonProductId.md)
 - [BeezUPCommonStoreId](docs/Model/BeezUPCommonStoreId.md)
 - [BeezUPCommonSuccessSummary](docs/Model/BeezUPCommonSuccessSummary.md)
 - [BeezUPCommonUserErrorMessage](docs/Model/BeezUPCommonUserErrorMessage.md)
 - [BeezUPCommonUserErrorMessageArguments](docs/Model/BeezUPCommonUserErrorMessageArguments.md)
 - [BeezUPCommonUserId](docs/Model/BeezUPCommonUserId.md)
 - [BeezUPCommonWarningSummary](docs/Model/BeezUPCommonWarningSummary.md)
 - [BeezUPOrderId](docs/Model/BeezUPOrderId.md)
 - [BeezUPOrderStatus](docs/Model/BeezUPOrderStatus.md)
 - [BeezUPTimeZoneId](docs/Model/BeezUPTimeZoneId.md)
 - [BillingPeriod](docs/Model/BillingPeriod.md)
 - [BillingPeriodInMonth](docs/Model/BillingPeriodInMonth.md)
 - [BillingPeriodList](docs/Model/BillingPeriodList.md)
 - [BillingPeriodListLinks](docs/Model/BillingPeriodListLinks.md)
 - [BonusInfo](docs/Model/BonusInfo.md)
 - [BonusType](docs/Model/BonusType.md)
 - [BusinessOperationType](docs/Model/BusinessOperationType.md)
 - [CanBeTruncated](docs/Model/CanBeTruncated.md)
 - [CardNumber](docs/Model/CardNumber.md)
 - [CardVerificationCode](docs/Model/CardVerificationCode.md)
 - [CatalogColumn](docs/Model/CatalogColumn.md)
 - [CatalogColumnLinks](docs/Model/CatalogColumnLinks.md)
 - [CatalogColumnList](docs/Model/CatalogColumnList.md)
 - [CatalogColumnName](docs/Model/CatalogColumnName.md)
 - [CatalogIndex](docs/Model/CatalogIndex.md)
 - [CatalogIndexLOVLinks](docs/Model/CatalogIndexLOVLinks.md)
 - [CatalogIndexLinks](docs/Model/CatalogIndexLinks.md)
 - [CatalogStoreIndex](docs/Model/CatalogStoreIndex.md)
 - [CatalogStoreIndexLinks](docs/Model/CatalogStoreIndexLinks.md)
 - [CatalogStoreIndexList](docs/Model/CatalogStoreIndexList.md)
 - [CatalogStoreStatus](docs/Model/CatalogStoreStatus.md)
 - [Category](docs/Model/Category.md)
 - [CategoryList](docs/Model/CategoryList.md)
 - [CategoryMappingState](docs/Model/CategoryMappingState.md)
 - [CategoryMappingStatus](docs/Model/CategoryMappingStatus.md)
 - [ChangeCustomColumnExpressionRequest](docs/Model/ChangeCustomColumnExpressionRequest.md)
 - [ChangeCustomColumnRequest](docs/Model/ChangeCustomColumnRequest.md)
 - [ChangeOrderListRequest](docs/Model/ChangeOrderListRequest.md)
 - [ChangeOrderListRequestItem](docs/Model/ChangeOrderListRequestItem.md)
 - [ChangeOrderReporting](docs/Model/ChangeOrderReporting.md)
 - [ChangeOrderRequest](docs/Model/ChangeOrderRequest.md)
 - [ChangePasswordRequest](docs/Model/ChangePasswordRequest.md)
 - [ChangeUserColumnNameRequest](docs/Model/ChangeUserColumnNameRequest.md)
 - [ChannelCatalogCategoryConfiguration](docs/Model/ChannelCatalogCategoryConfiguration.md)
 - [ChannelCatalogCategoryConfigurationInfo](docs/Model/ChannelCatalogCategoryConfigurationInfo.md)
 - [ChannelCatalogCategoryConfigurationList](docs/Model/ChannelCatalogCategoryConfigurationList.md)
 - [ChannelCatalogCategoryCostValue](docs/Model/ChannelCatalogCategoryCostValue.md)
 - [ChannelCatalogCategoryMappingInfoLinks](docs/Model/ChannelCatalogCategoryMappingInfoLinks.md)
 - [ChannelCatalogCategoryMappingSettings](docs/Model/ChannelCatalogCategoryMappingSettings.md)
 - [ChannelCatalogCategoryMappingsLinks](docs/Model/ChannelCatalogCategoryMappingsLinks.md)
 - [ChannelCatalogColumnMapping](docs/Model/ChannelCatalogColumnMapping.md)
 - [ChannelCatalogColumnMappingList](docs/Model/ChannelCatalogColumnMappingList.md)
 - [ChannelCatalogColumnMappingListWithName](docs/Model/ChannelCatalogColumnMappingListWithName.md)
 - [ChannelCatalogExportCacheInfo](docs/Model/ChannelCatalogExportCacheInfo.md)
 - [ChannelCatalogExportCacheInfoResponse](docs/Model/ChannelCatalogExportCacheInfoResponse.md)
 - [ChannelCatalogExportCacheInfoResponseLinks](docs/Model/ChannelCatalogExportCacheInfoResponseLinks.md)
 - [ChannelCatalogExportationHistory](docs/Model/ChannelCatalogExportationHistory.md)
 - [ChannelCatalogExportationHistoryLinks](docs/Model/ChannelCatalogExportationHistoryLinks.md)
 - [ChannelCatalogExportationReporting](docs/Model/ChannelCatalogExportationReporting.md)
 - [ChannelCatalogLinks](docs/Model/ChannelCatalogLinks.md)
 - [ChannelCatalogList](docs/Model/ChannelCatalogList.md)
 - [ChannelCatalogListLinks](docs/Model/ChannelCatalogListLinks.md)
 - [ChannelCatalogListLovLinks](docs/Model/ChannelCatalogListLovLinks.md)
 - [ChannelCatalogMarketplaceProperties](docs/Model/ChannelCatalogMarketplaceProperties.md)
 - [ChannelCatalogMarketplacePropertiesLinks](docs/Model/ChannelCatalogMarketplacePropertiesLinks.md)
 - [ChannelCatalogMarketplaceProperty](docs/Model/ChannelCatalogMarketplaceProperty.md)
 - [ChannelCatalogMarketplacePropertyDiscriminatorType](docs/Model/ChannelCatalogMarketplacePropertyDiscriminatorType.md)
 - [ChannelCatalogMarketplacePropertyGroup](docs/Model/ChannelCatalogMarketplacePropertyGroup.md)
 - [ChannelCatalogMarketplacePropertyName](docs/Model/ChannelCatalogMarketplacePropertyName.md)
 - [ChannelCatalogMarketplaceSetting](docs/Model/ChannelCatalogMarketplaceSetting.md)
 - [ChannelCatalogMarketplaceSettingArray](docs/Model/ChannelCatalogMarketplaceSettingArray.md)
 - [ChannelCatalogMarketplaceSettingDiscriminatorType](docs/Model/ChannelCatalogMarketplaceSettingDiscriminatorType.md)
 - [ChannelCatalogMarketplaceSettings](docs/Model/ChannelCatalogMarketplaceSettings.md)
 - [ChannelCatalogMarketplaceSettingsLinks](docs/Model/ChannelCatalogMarketplaceSettingsLinks.md)
 - [ChannelCatalogProductByChannelCatalogRequest](docs/Model/ChannelCatalogProductByChannelCatalogRequest.md)
 - [ChannelCatalogProductByChannelCatalogResponse](docs/Model/ChannelCatalogProductByChannelCatalogResponse.md)
 - [ChannelCatalogProductInfoLinks](docs/Model/ChannelCatalogProductInfoLinks.md)
 - [ChannelCatalogProductInfoList](docs/Model/ChannelCatalogProductInfoList.md)
 - [ChannelCatalogProductInfoListLinks](docs/Model/ChannelCatalogProductInfoListLinks.md)
 - [ChannelCatalogProductsCounters](docs/Model/ChannelCatalogProductsCounters.md)
 - [ChannelCatalogState](docs/Model/ChannelCatalogState.md)
 - [ChannelCategory](docs/Model/ChannelCategory.md)
 - [ChannelCategoryChannelCode](docs/Model/ChannelCategoryChannelCode.md)
 - [ChannelCategoryColumnOverride](docs/Model/ChannelCategoryColumnOverride.md)
 - [ChannelCategoryColumnOverrides](docs/Model/ChannelCategoryColumnOverrides.md)
 - [ChannelCategoryDefaultCost](docs/Model/ChannelCategoryDefaultCost.md)
 - [ChannelCategoryLevel](docs/Model/ChannelCategoryLevel.md)
 - [ChannelCategorySettings](docs/Model/ChannelCategorySettings.md)
 - [ChannelColumn](docs/Model/ChannelColumn.md)
 - [ChannelColumnConfiguration](docs/Model/ChannelColumnConfiguration.md)
 - [ChannelColumnDescription](docs/Model/ChannelColumnDescription.md)
 - [ChannelColumnRestrictedValues](docs/Model/ChannelColumnRestrictedValues.md)
 - [ChannelColumnShowInMapping](docs/Model/ChannelColumnShowInMapping.md)
 - [ChannelCostSettings](docs/Model/ChannelCostSettings.md)
 - [ChannelFirstLevelCategory](docs/Model/ChannelFirstLevelCategory.md)
 - [ChannelHeader](docs/Model/ChannelHeader.md)
 - [ChannelHeaderLinks](docs/Model/ChannelHeaderLinks.md)
 - [ChannelInfo](docs/Model/ChannelInfo.md)
 - [ChannelInfoList](docs/Model/ChannelInfoList.md)
 - [ChannelInfoListLinks](docs/Model/ChannelInfoListLinks.md)
 - [ChannelRootCategory](docs/Model/ChannelRootCategory.md)
 - [City](docs/Model/City.md)
 - [ClearMerchantOrderInfoListRequest](docs/Model/ClearMerchantOrderInfoListRequest.md)
 - [ClickIncludedAndAdditionalClickPrice](docs/Model/ClickIncludedAndAdditionalClickPrice.md)
 - [ClickIncludedAndVariablePrice](docs/Model/ClickIncludedAndVariablePrice.md)
 - [ColumnConfiguration](docs/Model/ColumnConfiguration.md)
 - [ColumnCultureName](docs/Model/ColumnCultureName.md)
 - [ColumnFormat](docs/Model/ColumnFormat.md)
 - [ColumnId](docs/Model/ColumnId.md)
 - [ColumnMappingStatus](docs/Model/ColumnMappingStatus.md)
 - [Company](docs/Model/Company.md)
 - [CompanyInfo](docs/Model/CompanyInfo.md)
 - [CompareOptions](docs/Model/CompareOptions.md)
 - [CompressionFormatStrategy](docs/Model/CompressionFormatStrategy.md)
 - [ComputeExpressionRequest](docs/Model/ComputeExpressionRequest.md)
 - [ConfigureAutoImportIntervalRequest](docs/Model/ConfigureAutoImportIntervalRequest.md)
 - [ConfigureAutomaticTransitionRequest](docs/Model/ConfigureAutomaticTransitionRequest.md)
 - [ConfigureCatalogColumnCatalogRequest](docs/Model/ConfigureCatalogColumnCatalogRequest.md)
 - [ConfigureCategoryRequest](docs/Model/ConfigureCategoryRequest.md)
 - [ContractBillingPeriodInfo](docs/Model/ContractBillingPeriodInfo.md)
 - [ContractBonusInfo](docs/Model/ContractBonusInfo.md)
 - [ContractClickInfo](docs/Model/ContractClickInfo.md)
 - [ContractCommitmentInfo](docs/Model/ContractCommitmentInfo.md)
 - [ContractDiscountInfo](docs/Model/ContractDiscountInfo.md)
 - [ContractId](docs/Model/ContractId.md)
 - [ContractInfo](docs/Model/ContractInfo.md)
 - [ContractMoneyInfo](docs/Model/ContractMoneyInfo.md)
 - [ContractStoreInfo](docs/Model/ContractStoreInfo.md)
 - [ContractTerminationReason](docs/Model/ContractTerminationReason.md)
 - [ContractTerminationReasonType](docs/Model/ContractTerminationReasonType.md)
 - [Contracts](docs/Model/Contracts.md)
 - [ContractsLinks](docs/Model/ContractsLinks.md)
 - [CopyOptimisationRequest](docs/Model/CopyOptimisationRequest.md)
 - [CopyOptimisationResponse](docs/Model/CopyOptimisationResponse.md)
 - [CostSettings](docs/Model/CostSettings.md)
 - [CostStatus](docs/Model/CostStatus.md)
 - [CostType](docs/Model/CostType.md)
 - [CouponDiscountCode](docs/Model/CouponDiscountCode.md)
 - [CouponOfferCode](docs/Model/CouponOfferCode.md)
 - [CreateContractResponse](docs/Model/CreateContractResponse.md)
 - [CreateContractResponseLinks](docs/Model/CreateContractResponseLinks.md)
 - [CreateCustomColumnRequest](docs/Model/CreateCustomColumnRequest.md)
 - [CreateRuleRequest](docs/Model/CreateRuleRequest.md)
 - [CreateStoreRequest](docs/Model/CreateStoreRequest.md)
 - [Credential](docs/Model/Credential.md)
 - [CreditCardInfo](docs/Model/CreditCardInfo.md)
 - [CreditCardInfoResponse](docs/Model/CreditCardInfoResponse.md)
 - [CreditCardInfoResponseLinks](docs/Model/CreditCardInfoResponseLinks.md)
 - [CreditCardInfoWithCardType](docs/Model/CreditCardInfoWithCardType.md)
 - [CurrentContractInfoLinks](docs/Model/CurrentContractInfoLinks.md)
 - [CustomColumn](docs/Model/CustomColumn.md)
 - [CustomColumnLinks](docs/Model/CustomColumnLinks.md)
 - [CustomColumnList](docs/Model/CustomColumnList.md)
 - [CustomColumnListLinks](docs/Model/CustomColumnListLinks.md)
 - [CustomerIndex](docs/Model/CustomerIndex.md)
 - [CustomerIndexLinks](docs/Model/CustomerIndexLinks.md)
 - [CustomerIndexLovLinks](docs/Model/CustomerIndexLovLinks.md)
 - [DateSearchType](docs/Model/DateSearchType.md)
 - [DetectedCatalogColumnLinks](docs/Model/DetectedCatalogColumnLinks.md)
 - [DetectedCatalogColumnList](docs/Model/DetectedCatalogColumnList.md)
 - [DetectedCatalogColumnListLinks](docs/Model/DetectedCatalogColumnListLinks.md)
 - [DisplayGroupName](docs/Model/DisplayGroupName.md)
 - [DownloadCatalogStrategy](docs/Model/DownloadCatalogStrategy.md)
 - [DuplicateProductValueConfiguration](docs/Model/DuplicateProductValueConfiguration.md)
 - [DuplicateProductValueStrategy](docs/Model/DuplicateProductValueStrategy.md)
 - [EncryptedBlocklyExpression](docs/Model/EncryptedBlocklyExpression.md)
 - [EncryptedExpression](docs/Model/EncryptedExpression.md)
 - [ErrorResponseMessagePaymentRequiredLinks](docs/Model/ErrorResponseMessagePaymentRequiredLinks.md)
 - [Etag](docs/Model/Etag.md)
 - [ExclusionFilter](docs/Model/ExclusionFilter.md)
 - [ExclusionFilterName](docs/Model/ExclusionFilterName.md)
 - [ExclusionFilterOperator](docs/Model/ExclusionFilterOperator.md)
 - [ExclusionFilterOperatorDataType](docs/Model/ExclusionFilterOperatorDataType.md)
 - [ExclusionFilterOperatorName](docs/Model/ExclusionFilterOperatorName.md)
 - [ExclusionFilters](docs/Model/ExclusionFilters.md)
 - [ExclusionFiltersResponse](docs/Model/ExclusionFiltersResponse.md)
 - [ExclusionFiltersResponseLinks](docs/Model/ExclusionFiltersResponseLinks.md)
 - [ExecutionId](docs/Model/ExecutionId.md)
 - [ExecutionUUID](docs/Model/ExecutionUUID.md)
 - [ExpirationMonth](docs/Model/ExpirationMonth.md)
 - [ExpirationYear](docs/Model/ExpirationYear.md)
 - [ExportOrderListFormat](docs/Model/ExportOrderListFormat.md)
 - [ExportOrderListRequest](docs/Model/ExportOrderListRequest.md)
 - [FeedType](docs/Model/FeedType.md)
 - [FileFormatStrategy](docs/Model/FileFormatStrategy.md)
 - [FirstName](docs/Model/FirstName.md)
 - [FixedAndVariableClickModelInfo](docs/Model/FixedAndVariableClickModelInfo.md)
 - [FriendCountryIsoCodeAlpha3](docs/Model/FriendCountryIsoCodeAlpha3.md)
 - [FriendEmail](docs/Model/FriendEmail.md)
 - [FriendProfilePictureUrl](docs/Model/FriendProfilePictureUrl.md)
 - [Functionality](docs/Model/Functionality.md)
 - [FunctionalityRightInfo](docs/Model/FunctionalityRightInfo.md)
 - [GeneralSettings](docs/Model/GeneralSettings.md)
 - [GetChannelCatalogProductInfoListRequest](docs/Model/GetChannelCatalogProductInfoListRequest.md)
 - [GetProductsRequest](docs/Model/GetProductsRequest.md)
 - [GravatarProfilePictureUrl](docs/Model/GravatarProfilePictureUrl.md)
 - [HarvestOrderReporting](docs/Model/HarvestOrderReporting.md)
 - [ImportAlreadyInProgressResponse](docs/Model/ImportAlreadyInProgressResponse.md)
 - [ImportAlreadyInProgressResponseLinks](docs/Model/ImportAlreadyInProgressResponseLinks.md)
 - [ImportationCustomColumn](docs/Model/ImportationCustomColumn.md)
 - [ImportationCustomColumnLinks](docs/Model/ImportationCustomColumnLinks.md)
 - [ImportationCustomColumnList](docs/Model/ImportationCustomColumnList.md)
 - [ImportationCustomColumnListLinks](docs/Model/ImportationCustomColumnListLinks.md)
 - [ImportationMonitoring](docs/Model/ImportationMonitoring.md)
 - [ImportationMonitoringLinks](docs/Model/ImportationMonitoringLinks.md)
 - [ImportationReporting](docs/Model/ImportationReporting.md)
 - [ImportationTechnicalProgression](docs/Model/ImportationTechnicalProgression.md)
 - [ImportationsResponse](docs/Model/ImportationsResponse.md)
 - [ImportationsResponseLinks](docs/Model/ImportationsResponseLinks.md)
 - [InitialsProfilePictureUrl](docs/Model/InitialsProfilePictureUrl.md)
 - [InputConfiguration](docs/Model/InputConfiguration.md)
 - [InputFileConfiguration](docs/Model/InputFileConfiguration.md)
 - [InputFileFetchConfiguration](docs/Model/InputFileFetchConfiguration.md)
 - [InputFileReadConfiguration](docs/Model/InputFileReadConfiguration.md)
 - [InputFileReadCsvConfiguration](docs/Model/InputFileReadCsvConfiguration.md)
 - [InputFileReadXmlConfiguration](docs/Model/InputFileReadXmlConfiguration.md)
 - [Invoice](docs/Model/Invoice.md)
 - [InvoiceList](docs/Model/InvoiceList.md)
 - [InvoiceListLinks](docs/Model/InvoiceListLinks.md)
 - [InvoiceNumber](docs/Model/InvoiceNumber.md)
 - [InvoicePaymentStatus](docs/Model/InvoicePaymentStatus.md)
 - [LastManualImportInputConfiguration](docs/Model/LastManualImportInputConfiguration.md)
 - [LastName](docs/Model/LastName.md)
 - [LinkClickToOrderType](docs/Model/LinkClickToOrderType.md)
 - [LoginRequest](docs/Model/LoginRequest.md)
 - [MapBeezUPColumnRequest](docs/Model/MapBeezUPColumnRequest.md)
 - [MappingStatus](docs/Model/MappingStatus.md)
 - [MarginType](docs/Model/MarginType.md)
 - [MarketplaceChannelCatalog](docs/Model/MarketplaceChannelCatalog.md)
 - [MarketplaceChannelCatalogLinks](docs/Model/MarketplaceChannelCatalogLinks.md)
 - [MarketplaceChannelCatalogList](docs/Model/MarketplaceChannelCatalogList.md)
 - [MarketplaceChannelCatalogListLinks](docs/Model/MarketplaceChannelCatalogListLinks.md)
 - [MarketplaceChannelCatalogLovLinks](docs/Model/MarketplaceChannelCatalogLovLinks.md)
 - [MarketplaceOrderId](docs/Model/MarketplaceOrderId.md)
 - [MarketplaceOrderStatus](docs/Model/MarketplaceOrderStatus.md)
 - [NextContractInfoLinks](docs/Model/NextContractInfoLinks.md)
 - [Offer](docs/Model/Offer.md)
 - [OfferContent](docs/Model/OfferContent.md)
 - [OfferFunctionality](docs/Model/OfferFunctionality.md)
 - [OfferId](docs/Model/OfferId.md)
 - [OfferLinks](docs/Model/OfferLinks.md)
 - [OfferRequest](docs/Model/OfferRequest.md)
 - [OptimisationActionName](docs/Model/OptimisationActionName.md)
 - [OrderBuyerName](docs/Model/OrderBuyerName.md)
 - [OrderByDirection](docs/Model/OrderByDirection.md)
 - [OrderExportationReporting](docs/Model/OrderExportationReporting.md)
 - [OrderExportationReportingProcessingStatus](docs/Model/OrderExportationReportingProcessingStatus.md)
 - [OrderExportations](docs/Model/OrderExportations.md)
 - [OrderExportationsLinks](docs/Model/OrderExportationsLinks.md)
 - [OrderHeader](docs/Model/OrderHeader.md)
 - [OrderHeaderLinks](docs/Model/OrderHeaderLinks.md)
 - [OrderHistory](docs/Model/OrderHistory.md)
 - [OrderIdentifier](docs/Model/OrderIdentifier.md)
 - [OrderIndex](docs/Model/OrderIndex.md)
 - [OrderIndexLinks](docs/Model/OrderIndexLinks.md)
 - [OrderIndexLovLinks](docs/Model/OrderIndexLovLinks.md)
 - [OrderItem](docs/Model/OrderItem.md)
 - [OrderListFull](docs/Model/OrderListFull.md)
 - [OrderListLight](docs/Model/OrderListLight.md)
 - [OrderListLinks](docs/Model/OrderListLinks.md)
 - [OrderListRequestWithoutPagination](docs/Model/OrderListRequestWithoutPagination.md)
 - [OrderMerchantECommerceSoftwareName](docs/Model/OrderMerchantECommerceSoftwareName.md)
 - [OrderMerchantECommerceSoftwareVersion](docs/Model/OrderMerchantECommerceSoftwareVersion.md)
 - [OrderMerchantOrderId](docs/Model/OrderMerchantOrderId.md)
 - [OrderOperationResponse](docs/Model/OrderOperationResponse.md)
 - [OrderTransitionLinks](docs/Model/OrderTransitionLinks.md)
 - [PageNumber](docs/Model/PageNumber.md)
 - [PageSize](docs/Model/PageSize.md)
 - [PaginationRequestParameters](docs/Model/PaginationRequestParameters.md)
 - [PaymentMethod](docs/Model/PaymentMethod.md)
 - [PerformanceIndicatorFilter](docs/Model/PerformanceIndicatorFilter.md)
 - [PerformanceIndicatorFilterOperatorName](docs/Model/PerformanceIndicatorFilterOperatorName.md)
 - [PerformanceIndicatorFormula](docs/Model/PerformanceIndicatorFormula.md)
 - [PerformanceIndicatorFormulaOperatorName](docs/Model/PerformanceIndicatorFormulaOperatorName.md)
 - [PerformanceIndicatorFormulaParameterType](docs/Model/PerformanceIndicatorFormulaParameterType.md)
 - [PerformanceIndicatorType](docs/Model/PerformanceIndicatorType.md)
 - [PersonalInfo](docs/Model/PersonalInfo.md)
 - [PhoneNumber](docs/Model/PhoneNumber.md)
 - [PostalCode](docs/Model/PostalCode.md)
 - [PreviousFixPeriodInvoiceProrataInfo](docs/Model/PreviousFixPeriodInvoiceProrataInfo.md)
 - [Processing](docs/Model/Processing.md)
 - [Product](docs/Model/Product.md)
 - [ProductColumnsToDisplay](docs/Model/ProductColumnsToDisplay.md)
 - [ProductList](docs/Model/ProductList.md)
 - [ProductOverrideWithCatalogValue](docs/Model/ProductOverrideWithCatalogValue.md)
 - [ProductOverrides](docs/Model/ProductOverrides.md)
 - [ProductOverridesCopyResponse](docs/Model/ProductOverridesCopyResponse.md)
 - [ProductOverridesWithCatalogValues](docs/Model/ProductOverridesWithCatalogValues.md)
 - [ProductSample](docs/Model/ProductSample.md)
 - [ProductSetVisibilityCriteria](docs/Model/ProductSetVisibilityCriteria.md)
 - [ProductSetVisibilityCriteriaLogicType](docs/Model/ProductSetVisibilityCriteriaLogicType.md)
 - [ProductStateFilter](docs/Model/ProductStateFilter.md)
 - [ProductValues](docs/Model/ProductValues.md)
 - [ProfilePictureInfo](docs/Model/ProfilePictureInfo.md)
 - [ProfilePictureInfoResponse](docs/Model/ProfilePictureInfoResponse.md)
 - [ProfilePictureInfoResponseLinks](docs/Model/ProfilePictureInfoResponseLinks.md)
 - [ProfilePictureSelected](docs/Model/ProfilePictureSelected.md)
 - [ProfilePictureUrl](docs/Model/ProfilePictureUrl.md)
 - [PromotionalCodeValidity](docs/Model/PromotionalCodeValidity.md)
 - [PublicChannelIndex](docs/Model/PublicChannelIndex.md)
 - [PublicChannelIndexLinks](docs/Model/PublicChannelIndexLinks.md)
 - [PublicListOfValuesResponse](docs/Model/PublicListOfValuesResponse.md)
 - [PublicListOfValuesResponseLinks](docs/Model/PublicListOfValuesResponseLinks.md)
 - [PublicLovIndex](docs/Model/PublicLovIndex.md)
 - [PublicLovIndexLinks](docs/Model/PublicLovIndexLinks.md)
 - [PublicLovLinks](docs/Model/PublicLovLinks.md)
 - [PublicationFeedReporting](docs/Model/PublicationFeedReporting.md)
 - [PublicationReporting](docs/Model/PublicationReporting.md)
 - [PublicationType](docs/Model/PublicationType.md)
 - [RandomProductList](docs/Model/RandomProductList.md)
 - [RegisterRequest](docs/Model/RegisterRequest.md)
 - [ReportAdvancedFilters](docs/Model/ReportAdvancedFilters.md)
 - [ReportByCategory](docs/Model/ReportByCategory.md)
 - [ReportByCategoryLinks](docs/Model/ReportByCategoryLinks.md)
 - [ReportByCategoryResponse](docs/Model/ReportByCategoryResponse.md)
 - [ReportByChannel](docs/Model/ReportByChannel.md)
 - [ReportByChannelLinks](docs/Model/ReportByChannelLinks.md)
 - [ReportByChannelResponse](docs/Model/ReportByChannelResponse.md)
 - [ReportByCommonResponseLinks](docs/Model/ReportByCommonResponseLinks.md)
 - [ReportByDay](docs/Model/ReportByDay.md)
 - [ReportByDayAllChannels](docs/Model/ReportByDayAllChannels.md)
 - [ReportByDayGlobal](docs/Model/ReportByDayGlobal.md)
 - [ReportByDayGlobalAllChannels](docs/Model/ReportByDayGlobalAllChannels.md)
 - [ReportByDayRequest](docs/Model/ReportByDayRequest.md)
 - [ReportByDayResponse](docs/Model/ReportByDayResponse.md)
 - [ReportByProduct](docs/Model/ReportByProduct.md)
 - [ReportByProductAllChannelsLinks](docs/Model/ReportByProductAllChannelsLinks.md)
 - [ReportByProductLinks](docs/Model/ReportByProductLinks.md)
 - [ReportByProductOneChannelLinks](docs/Model/ReportByProductOneChannelLinks.md)
 - [ReportByProductResponse](docs/Model/ReportByProductResponse.md)
 - [ReportFilter](docs/Model/ReportFilter.md)
 - [ReportFilterCommonParameters](docs/Model/ReportFilterCommonParameters.md)
 - [ReportFilterHeader](docs/Model/ReportFilterHeader.md)
 - [ReportFilterHeaderLinks](docs/Model/ReportFilterHeaderLinks.md)
 - [ReportFilterLinks](docs/Model/ReportFilterLinks.md)
 - [ReportFilterList](docs/Model/ReportFilterList.md)
 - [ReportFilterPeriodType](docs/Model/ReportFilterPeriodType.md)
 - [ReportFiltersLinks](docs/Model/ReportFiltersLinks.md)
 - [ReportType](docs/Model/ReportType.md)
 - [Rule](docs/Model/Rule.md)
 - [RuleExecutionReporting](docs/Model/RuleExecutionReporting.md)
 - [RuleExecutionReportingErrorType](docs/Model/RuleExecutionReportingErrorType.md)
 - [RuleExecutionReportingExecutionSource](docs/Model/RuleExecutionReportingExecutionSource.md)
 - [RuleExecutionReportingLinks](docs/Model/RuleExecutionReportingLinks.md)
 - [RuleExecutionReportingStatus](docs/Model/RuleExecutionReportingStatus.md)
 - [RuleExecutionReportings](docs/Model/RuleExecutionReportings.md)
 - [RuleLastExecutionStatus](docs/Model/RuleLastExecutionStatus.md)
 - [RuleLinks](docs/Model/RuleLinks.md)
 - [RuleList](docs/Model/RuleList.md)
 - [RuleListLinks](docs/Model/RuleListLinks.md)
 - [SaveReportFilterRequest](docs/Model/SaveReportFilterRequest.md)
 - [SaveStoreAlertRequest](docs/Model/SaveStoreAlertRequest.md)
 - [SaveStoreAlertsRequest](docs/Model/SaveStoreAlertsRequest.md)
 - [ScheduleAutoImportRequest](docs/Model/ScheduleAutoImportRequest.md)
 - [SchedulingType](docs/Model/SchedulingType.md)
 - [SetChannelCatalogMarketplaceSettingsRequest](docs/Model/SetChannelCatalogMarketplaceSettingsRequest.md)
 - [SetMerchantOrderInfoListRequest](docs/Model/SetMerchantOrderInfoListRequest.md)
 - [SetMerchantOrderInfoRequest](docs/Model/SetMerchantOrderInfoRequest.md)
 - [SourceType](docs/Model/SourceType.md)
 - [StandardOffer](docs/Model/StandardOffer.md)
 - [StandardOfferLinks](docs/Model/StandardOfferLinks.md)
 - [StandardOffers](docs/Model/StandardOffers.md)
 - [StandardOffersLinks](docs/Model/StandardOffersLinks.md)
 - [StartManualImportRequest](docs/Model/StartManualImportRequest.md)
 - [Store](docs/Model/Store.md)
 - [StoreAlert](docs/Model/StoreAlert.md)
 - [StoreAlertLinks](docs/Model/StoreAlertLinks.md)
 - [StoreAlertProperty](docs/Model/StoreAlertProperty.md)
 - [StoreAlerts](docs/Model/StoreAlerts.md)
 - [StoreCount](docs/Model/StoreCount.md)
 - [StoreCountryIsoCodeAlpha3](docs/Model/StoreCountryIsoCodeAlpha3.md)
 - [StoreLinks](docs/Model/StoreLinks.md)
 - [StoreList](docs/Model/StoreList.md)
 - [StoreListLinks](docs/Model/StoreListLinks.md)
 - [StoreName](docs/Model/StoreName.md)
 - [StoreSector](docs/Model/StoreSector.md)
 - [StoreSectors](docs/Model/StoreSectors.md)
 - [StoreShare](docs/Model/StoreShare.md)
 - [StoreShareLinks](docs/Model/StoreShareLinks.md)
 - [StoreShares](docs/Model/StoreShares.md)
 - [StoreSharesLinks](docs/Model/StoreSharesLinks.md)
 - [StoreStatus](docs/Model/StoreStatus.md)
 - [StoreTrackingStatus](docs/Model/StoreTrackingStatus.md)
 - [StoreUrl](docs/Model/StoreUrl.md)
 - [StoreUserRole](docs/Model/StoreUserRole.md)
 - [TerminateContract](docs/Model/TerminateContract.md)
 - [TrackedClick](docs/Model/TrackedClick.md)
 - [TrackedClicks](docs/Model/TrackedClicks.md)
 - [TrackedExternalOrder](docs/Model/TrackedExternalOrder.md)
 - [TrackedExternalOrderProduct](docs/Model/TrackedExternalOrderProduct.md)
 - [TrackedExternalOrders](docs/Model/TrackedExternalOrders.md)
 - [TrackedOrder](docs/Model/TrackedOrder.md)
 - [TrackedOrders](docs/Model/TrackedOrders.md)
 - [TrackingStatus](docs/Model/TrackingStatus.md)
 - [Type](docs/Model/Type.md)
 - [UnmapCategoryRequest](docs/Model/UnmapCategoryRequest.md)
 - [UpdateRuleRequest](docs/Model/UpdateRuleRequest.md)
 - [UpdateStoreRequest](docs/Model/UpdateStoreRequest.md)
 - [UpgradeOfferRequired](docs/Model/UpgradeOfferRequired.md)
 - [UserColumName](docs/Model/UserColumName.md)
 - [UserFriendInfo](docs/Model/UserFriendInfo.md)
 - [UserListOfValuesResponse](docs/Model/UserListOfValuesResponse.md)
 - [UserListOfValuesResponseLinks](docs/Model/UserListOfValuesResponseLinks.md)
 - [UserLovIndex](docs/Model/UserLovIndex.md)
 - [UserLovIndexLinks](docs/Model/UserLovIndexLinks.md)
 - [UserLovLinks](docs/Model/UserLovLinks.md)
 - [VariableModelInfo](docs/Model/VariableModelInfo.md)
 - [VatNumber](docs/Model/VatNumber.md)
 - [WhatIDo](docs/Model/WhatIDo.md)
 - [AutomaticTransitionInfo](docs/Model/AutomaticTransitionInfo.md)
 - [ChannelCatalog](docs/Model/ChannelCatalog.md)
 - [ChannelCatalogColumnMappingWithName](docs/Model/ChannelCatalogColumnMappingWithName.md)
 - [ChannelCatalogMarketplaceBooleanProperty](docs/Model/ChannelCatalogMarketplaceBooleanProperty.md)
 - [ChannelCatalogMarketplaceBooleanSetting](docs/Model/ChannelCatalogMarketplaceBooleanSetting.md)
 - [ChannelCatalogMarketplaceIntegerProperty](docs/Model/ChannelCatalogMarketplaceIntegerProperty.md)
 - [ChannelCatalogMarketplaceIntegerSetting](docs/Model/ChannelCatalogMarketplaceIntegerSetting.md)
 - [ChannelCatalogMarketplaceNumberProperty](docs/Model/ChannelCatalogMarketplaceNumberProperty.md)
 - [ChannelCatalogMarketplaceNumberSetting](docs/Model/ChannelCatalogMarketplaceNumberSetting.md)
 - [ChannelCatalogMarketplaceStringProperty](docs/Model/ChannelCatalogMarketplaceStringProperty.md)
 - [ChannelCatalogMarketplaceStringSetting](docs/Model/ChannelCatalogMarketplaceStringSetting.md)
 - [ChannelCatalogProductInfo](docs/Model/ChannelCatalogProductInfo.md)
 - [CreateContract](docs/Model/CreateContract.md)
 - [CurrentContractInfo](docs/Model/CurrentContractInfo.md)
 - [DetectedCatalogColumn](docs/Model/DetectedCatalogColumn.md)
 - [ErrorResponseMessagePaymentRequired](docs/Model/ErrorResponseMessagePaymentRequired.md)
 - [ExternalLinksCatalogGetBeezUPColumnsLink](docs/Model/ExternalLinksCatalogGetBeezUPColumnsLink.md)
 - [ExternalLinksGetChannelCatalogMarketplaceSettingsLink](docs/Model/ExternalLinksGetChannelCatalogMarketplaceSettingsLink.md)
 - [ExternalLinksGetChannelInfoLink](docs/Model/ExternalLinksGetChannelInfoLink.md)
 - [ExternalLinksGetExternalConfigurationPageLink](docs/Model/ExternalLinksGetExternalConfigurationPageLink.md)
 - [LinksActivateUserAccountLink](docs/Model/LinksActivateUserAccountLink.md)
 - [LinksAddChannelCatalogLink](docs/Model/LinksAddChannelCatalogLink.md)
 - [LinksAnalyticsIndexLink](docs/Model/LinksAnalyticsIndexLink.md)
 - [LinksAutoConfigureAutoImportIntervalLink](docs/Model/LinksAutoConfigureAutoImportIntervalLink.md)
 - [LinksAutoGetAutoImportConfigurationLink](docs/Model/LinksAutoGetAutoImportConfigurationLink.md)
 - [LinksAutoPauseAutoImportLink](docs/Model/LinksAutoPauseAutoImportLink.md)
 - [LinksAutoResumeAutoImportLink](docs/Model/LinksAutoResumeAutoImportLink.md)
 - [LinksAutoScheduleAutoImportLink](docs/Model/LinksAutoScheduleAutoImportLink.md)
 - [LinksAutoStartAutoImportLink](docs/Model/LinksAutoStartAutoImportLink.md)
 - [LinksCatalogChangeCatalogColumnUserNameLink](docs/Model/LinksCatalogChangeCatalogColumnUserNameLink.md)
 - [LinksCatalogChangeCustomColumnExpressionLink](docs/Model/LinksCatalogChangeCustomColumnExpressionLink.md)
 - [LinksCatalogChangeCustomColumnUserNameLink](docs/Model/LinksCatalogChangeCustomColumnUserNameLink.md)
 - [LinksCatalogComputeExpressionLink](docs/Model/LinksCatalogComputeExpressionLink.md)
 - [LinksCatalogDeleteCustomColumnLink](docs/Model/LinksCatalogDeleteCustomColumnLink.md)
 - [LinksCatalogGetBeezUPColumnsLink](docs/Model/LinksCatalogGetBeezUPColumnsLink.md)
 - [LinksCatalogGetCatalogColumnsLink](docs/Model/LinksCatalogGetCatalogColumnsLink.md)
 - [LinksCatalogGetCategoriesLink](docs/Model/LinksCatalogGetCategoriesLink.md)
 - [LinksCatalogGetCustomColumnExpressionLink](docs/Model/LinksCatalogGetCustomColumnExpressionLink.md)
 - [LinksCatalogGetCustomColumnsLink](docs/Model/LinksCatalogGetCustomColumnsLink.md)
 - [LinksCatalogGetProductsLink](docs/Model/LinksCatalogGetProductsLink.md)
 - [LinksCatalogGetRandomProductsLink](docs/Model/LinksCatalogGetRandomProductsLink.md)
 - [LinksCatalogIndexLink](docs/Model/LinksCatalogIndexLink.md)
 - [LinksCatalogSaveCustomColumnLink](docs/Model/LinksCatalogSaveCustomColumnLink.md)
 - [LinksCatalogStoreIndexLink](docs/Model/LinksCatalogStoreIndexLink.md)
 - [LinksChangeOrderLink](docs/Model/LinksChangeOrderLink.md)
 - [LinksChangePasswordLink](docs/Model/LinksChangePasswordLink.md)
 - [LinksClearChannelCatalogExportationCacheLink](docs/Model/LinksClearChannelCatalogExportationCacheLink.md)
 - [LinksClearMerchantOrderInfoLink](docs/Model/LinksClearMerchantOrderInfoLink.md)
 - [LinksClearMerchantOrderInfoListLink](docs/Model/LinksClearMerchantOrderInfoListLink.md)
 - [LinksConfigureAutomaticTransitionsLink](docs/Model/LinksConfigureAutomaticTransitionsLink.md)
 - [LinksConfigureChannelCatalogCategoryLink](docs/Model/LinksConfigureChannelCatalogCategoryLink.md)
 - [LinksConfigureChannelCatalogColumnMappingsLink](docs/Model/LinksConfigureChannelCatalogColumnMappingsLink.md)
 - [LinksConfigureChannelCatalogCostSettingsLink](docs/Model/LinksConfigureChannelCatalogCostSettingsLink.md)
 - [LinksConfigureChannelCatalogExclusionFiltersCopyLink](docs/Model/LinksConfigureChannelCatalogExclusionFiltersCopyLink.md)
 - [LinksConfigureChannelCatalogExclusionFiltersLink](docs/Model/LinksConfigureChannelCatalogExclusionFiltersLink.md)
 - [LinksConfigureChannelCatalogGeneralSettingsLink](docs/Model/LinksConfigureChannelCatalogGeneralSettingsLink.md)
 - [LinksConfigureChannelCatalogProductValueOverrideCopyLink](docs/Model/LinksConfigureChannelCatalogProductValueOverrideCopyLink.md)
 - [LinksCreateContractLink](docs/Model/LinksCreateContractLink.md)
 - [LinksCreateRuleLink](docs/Model/LinksCreateRuleLink.md)
 - [LinksCreateStoreLink](docs/Model/LinksCreateStoreLink.md)
 - [LinksDeleteChannelCatalogLink](docs/Model/LinksDeleteChannelCatalogLink.md)
 - [LinksDeleteNextContractLink](docs/Model/LinksDeleteNextContractLink.md)
 - [LinksDeleteReportFilterLink](docs/Model/LinksDeleteReportFilterLink.md)
 - [LinksDeleteRuleLink](docs/Model/LinksDeleteRuleLink.md)
 - [LinksDeleteStoreLink](docs/Model/LinksDeleteStoreLink.md)
 - [LinksDeleteStoreShareLink](docs/Model/LinksDeleteStoreShareLink.md)
 - [LinksDisableChannelCatalogCategoryMappingLink](docs/Model/LinksDisableChannelCatalogCategoryMappingLink.md)
 - [LinksDisableChannelCatalogLink](docs/Model/LinksDisableChannelCatalogLink.md)
 - [LinksDisableChannelCatalogProductLink](docs/Model/LinksDisableChannelCatalogProductLink.md)
 - [LinksDisableRuleLink](docs/Model/LinksDisableRuleLink.md)
 - [LinksEnableChannelCatalogLink](docs/Model/LinksEnableChannelCatalogLink.md)
 - [LinksEnableRuleLink](docs/Model/LinksEnableRuleLink.md)
 - [LinksExportChannelCatalogProductInfoListLink](docs/Model/LinksExportChannelCatalogProductInfoListLink.md)
 - [LinksExportOrdersLink](docs/Model/LinksExportOrdersLink.md)
 - [LinksGetAutomaticTransitionsLink](docs/Model/LinksGetAutomaticTransitionsLink.md)
 - [LinksGetBillingPeriodsLink](docs/Model/LinksGetBillingPeriodsLink.md)
 - [LinksGetChannelCatalogCategoriesLink](docs/Model/LinksGetChannelCatalogCategoriesLink.md)
 - [LinksGetChannelCatalogExclusionFilterOperatorsLink](docs/Model/LinksGetChannelCatalogExclusionFilterOperatorsLink.md)
 - [LinksGetChannelCatalogExclusionFiltersCopyLink](docs/Model/LinksGetChannelCatalogExclusionFiltersCopyLink.md)
 - [LinksGetChannelCatalogExclusionFiltersLink](docs/Model/LinksGetChannelCatalogExclusionFiltersLink.md)
 - [LinksGetChannelCatalogExportationCacheInfoLink](docs/Model/LinksGetChannelCatalogExportationCacheInfoLink.md)
 - [LinksGetChannelCatalogExportationHistoryLink](docs/Model/LinksGetChannelCatalogExportationHistoryLink.md)
 - [LinksGetChannelCatalogLink](docs/Model/LinksGetChannelCatalogLink.md)
 - [LinksGetChannelCatalogMarketplacePropertiesLink](docs/Model/LinksGetChannelCatalogMarketplacePropertiesLink.md)
 - [LinksGetChannelCatalogMarketplaceSettingsLink](docs/Model/LinksGetChannelCatalogMarketplaceSettingsLink.md)
 - [LinksGetChannelCatalogProductInfoLink](docs/Model/LinksGetChannelCatalogProductInfoLink.md)
 - [LinksGetChannelCatalogProductInfoListLink](docs/Model/LinksGetChannelCatalogProductInfoListLink.md)
 - [LinksGetChannelCatalogProductValueOverrideCopyLink](docs/Model/LinksGetChannelCatalogProductValueOverrideCopyLink.md)
 - [LinksGetChannelCatalogsLink](docs/Model/LinksGetChannelCatalogsLink.md)
 - [LinksGetChannelInfoLink](docs/Model/LinksGetChannelInfoLink.md)
 - [LinksGetChannelsIndexLink](docs/Model/LinksGetChannelsIndexLink.md)
 - [LinksGetChannelsLink](docs/Model/LinksGetChannelsLink.md)
 - [LinksGetContractsLink](docs/Model/LinksGetContractsLink.md)
 - [LinksGetCreditCardInfoLink](docs/Model/LinksGetCreditCardInfoLink.md)
 - [LinksGetCustomerIndexLink](docs/Model/LinksGetCustomerIndexLink.md)
 - [LinksGetFriendInfoLink](docs/Model/LinksGetFriendInfoLink.md)
 - [LinksGetInvoicesLink](docs/Model/LinksGetInvoicesLink.md)
 - [LinksGetMarketplaceAccountsSynchronizationLink](docs/Model/LinksGetMarketplaceAccountsSynchronizationLink.md)
 - [LinksGetMarketplaceChannelCatalogsLink](docs/Model/LinksGetMarketplaceChannelCatalogsLink.md)
 - [LinksGetOfferLink](docs/Model/LinksGetOfferLink.md)
 - [LinksGetOrderExportationsLink](docs/Model/LinksGetOrderExportationsLink.md)
 - [LinksGetOrderHistoryLink](docs/Model/LinksGetOrderHistoryLink.md)
 - [LinksGetOrderIndexLink](docs/Model/LinksGetOrderIndexLink.md)
 - [LinksGetOrderLink](docs/Model/LinksGetOrderLink.md)
 - [LinksGetOrderListFullLink](docs/Model/LinksGetOrderListFullLink.md)
 - [LinksGetOrderListLightLink](docs/Model/LinksGetOrderListLightLink.md)
 - [LinksGetProfilePictureInfoLink](docs/Model/LinksGetProfilePictureInfoLink.md)
 - [LinksGetPublicListOfValuesLink](docs/Model/LinksGetPublicListOfValuesLink.md)
 - [LinksGetPublicLovIndexLink](docs/Model/LinksGetPublicLovIndexLink.md)
 - [LinksGetPublicationsLink](docs/Model/LinksGetPublicationsLink.md)
 - [LinksGetReportFilterLink](docs/Model/LinksGetReportFilterLink.md)
 - [LinksGetReportFiltersLink](docs/Model/LinksGetReportFiltersLink.md)
 - [LinksGetRuleLink](docs/Model/LinksGetRuleLink.md)
 - [LinksGetRulesExecutionsLink](docs/Model/LinksGetRulesExecutionsLink.md)
 - [LinksGetRulesLink](docs/Model/LinksGetRulesLink.md)
 - [LinksGetStandardOffersLink](docs/Model/LinksGetStandardOffersLink.md)
 - [LinksGetStoreLink](docs/Model/LinksGetStoreLink.md)
 - [LinksGetStoreReportByCategoryLink](docs/Model/LinksGetStoreReportByCategoryLink.md)
 - [LinksGetStoreReportByChannelLink](docs/Model/LinksGetStoreReportByChannelLink.md)
 - [LinksGetStoreReportByDayLink](docs/Model/LinksGetStoreReportByDayLink.md)
 - [LinksGetStoreReportByProductLink](docs/Model/LinksGetStoreReportByProductLink.md)
 - [LinksGetStoreSharesLink](docs/Model/LinksGetStoreSharesLink.md)
 - [LinksGetStoreTrackedClicksLink](docs/Model/LinksGetStoreTrackedClicksLink.md)
 - [LinksGetStoreTrackedExternalOrdersLink](docs/Model/LinksGetStoreTrackedExternalOrdersLink.md)
 - [LinksGetStoreTrackedOrdersLink](docs/Model/LinksGetStoreTrackedOrdersLink.md)
 - [LinksGetStoreTrackingStatusLink](docs/Model/LinksGetStoreTrackingStatusLink.md)
 - [LinksGetStoresLink](docs/Model/LinksGetStoresLink.md)
 - [LinksGetTrackingStatusLink](docs/Model/LinksGetTrackingStatusLink.md)
 - [LinksGetUserAccountInfoLink](docs/Model/LinksGetUserAccountInfoLink.md)
 - [LinksGetUserListOfValuesLink](docs/Model/LinksGetUserListOfValuesLink.md)
 - [LinksGetUserLovIndexLink](docs/Model/LinksGetUserLovIndexLink.md)
 - [LinksHarvestAllLink](docs/Model/LinksHarvestAllLink.md)
 - [LinksHarvestOrderLink](docs/Model/LinksHarvestOrderLink.md)
 - [LinksImportationActivateAutoImportLink](docs/Model/LinksImportationActivateAutoImportLink.md)
 - [LinksImportationCancelLink](docs/Model/LinksImportationCancelLink.md)
 - [LinksImportationCommitColumnsLink](docs/Model/LinksImportationCommitColumnsLink.md)
 - [LinksImportationCommitLink](docs/Model/LinksImportationCommitLink.md)
 - [LinksImportationConfigureCatalogColumnLink](docs/Model/LinksImportationConfigureCatalogColumnLink.md)
 - [LinksImportationConfigureRemainingCatalogColumnsLink](docs/Model/LinksImportationConfigureRemainingCatalogColumnsLink.md)
 - [LinksImportationDeleteCustomColumnLink](docs/Model/LinksImportationDeleteCustomColumnLink.md)
 - [LinksImportationGetCustomColumnExpressionLink](docs/Model/LinksImportationGetCustomColumnExpressionLink.md)
 - [LinksImportationGetCustomColumnsLink](docs/Model/LinksImportationGetCustomColumnsLink.md)
 - [LinksImportationGetDetectedCatalogColumnsLink](docs/Model/LinksImportationGetDetectedCatalogColumnsLink.md)
 - [LinksImportationGetImportationMonitoringLink](docs/Model/LinksImportationGetImportationMonitoringLink.md)
 - [LinksImportationGetManualUpdateLastInputConfigLink](docs/Model/LinksImportationGetManualUpdateLastInputConfigLink.md)
 - [LinksImportationGetProductSampleCustomColumnValueLink](docs/Model/LinksImportationGetProductSampleCustomColumnValueLink.md)
 - [LinksImportationGetProductSampleLink](docs/Model/LinksImportationGetProductSampleLink.md)
 - [LinksImportationGetReportingsLink](docs/Model/LinksImportationGetReportingsLink.md)
 - [LinksImportationIgnoreColumnLink](docs/Model/LinksImportationIgnoreColumnLink.md)
 - [LinksImportationMapCatalogColumnLink](docs/Model/LinksImportationMapCatalogColumnLink.md)
 - [LinksImportationMapCustomColumnLink](docs/Model/LinksImportationMapCustomColumnLink.md)
 - [LinksImportationReattendColumnLink](docs/Model/LinksImportationReattendColumnLink.md)
 - [LinksImportationSaveCustomColumnLink](docs/Model/LinksImportationSaveCustomColumnLink.md)
 - [LinksImportationStartManualUpdateLink](docs/Model/LinksImportationStartManualUpdateLink.md)
 - [LinksImportationTechnicalProgressionLink](docs/Model/LinksImportationTechnicalProgressionLink.md)
 - [LinksImportationUnmapCatalogColumnLink](docs/Model/LinksImportationUnmapCatalogColumnLink.md)
 - [LinksImportationUnmapCustomColumnLink](docs/Model/LinksImportationUnmapCustomColumnLink.md)
 - [LinksLogoutLink](docs/Model/LinksLogoutLink.md)
 - [LinksMoveDownRuleLink](docs/Model/LinksMoveDownRuleLink.md)
 - [LinksMoveUpRuleLink](docs/Model/LinksMoveUpRuleLink.md)
 - [LinksOptimiseAllLink](docs/Model/LinksOptimiseAllLink.md)
 - [LinksOptimiseByCategoryLink](docs/Model/LinksOptimiseByCategoryLink.md)
 - [LinksOptimiseByChannelLink](docs/Model/LinksOptimiseByChannelLink.md)
 - [LinksOptimiseByProductLink](docs/Model/LinksOptimiseByProductLink.md)
 - [LinksOptimiseLink](docs/Model/LinksOptimiseLink.md)
 - [LinksOverrideChannelCatalogProductValuesLink](docs/Model/LinksOverrideChannelCatalogProductValuesLink.md)
 - [LinksReactivateCurrentContractLink](docs/Model/LinksReactivateCurrentContractLink.md)
 - [LinksReenableChannelCatalogCategoryMappingLink](docs/Model/LinksReenableChannelCatalogCategoryMappingLink.md)
 - [LinksReenableChannelCatalogProductLink](docs/Model/LinksReenableChannelCatalogProductLink.md)
 - [LinksRunRuleLink](docs/Model/LinksRunRuleLink.md)
 - [LinksRunRulesLink](docs/Model/LinksRunRulesLink.md)
 - [LinksSaveCompanyInfoLink](docs/Model/LinksSaveCompanyInfoLink.md)
 - [LinksSaveCreditCardInfoLink](docs/Model/LinksSaveCreditCardInfoLink.md)
 - [LinksSavePersonalInfoLink](docs/Model/LinksSavePersonalInfoLink.md)
 - [LinksSaveProfilePictureInfoLink](docs/Model/LinksSaveProfilePictureInfoLink.md)
 - [LinksSaveReportFilterLink](docs/Model/LinksSaveReportFilterLink.md)
 - [LinksSaveStoreAlertsLink](docs/Model/LinksSaveStoreAlertsLink.md)
 - [LinksSetChannelCatalogMarketplaceSettingsLink](docs/Model/LinksSetChannelCatalogMarketplaceSettingsLink.md)
 - [LinksSetMerchantOrderInfoLink](docs/Model/LinksSetMerchantOrderInfoLink.md)
 - [LinksSetMerchantOrderInfoListLink](docs/Model/LinksSetMerchantOrderInfoListLink.md)
 - [LinksShareStoreLink](docs/Model/LinksShareStoreLink.md)
 - [LinksTerminateCurrentContractLink](docs/Model/LinksTerminateCurrentContractLink.md)
 - [LinksUpdateRuleLink](docs/Model/LinksUpdateRuleLink.md)
 - [LinksUpdateStoreLink](docs/Model/LinksUpdateStoreLink.md)
 - [NextContractInfo](docs/Model/NextContractInfo.md)
 - [Order](docs/Model/Order.md)
 - [OrderIdentifierWithETag](docs/Model/OrderIdentifierWithETag.md)
 - [OrderLinks](docs/Model/OrderLinks.md)
 - [OrderListFullLinks](docs/Model/OrderListFullLinks.md)
 - [OrderListLightLinks](docs/Model/OrderListLightLinks.md)
 - [OrderListRequest](docs/Model/OrderListRequest.md)
 - [OrderedReportFilterCommonParameters](docs/Model/OrderedReportFilterCommonParameters.md)
 - [ProfilePictureInfoWithDefault](docs/Model/ProfilePictureInfoWithDefault.md)
 - [ReportByCategoryResponseLinks](docs/Model/ReportByCategoryResponseLinks.md)
 - [ReportByChannelResponseLinks](docs/Model/ReportByChannelResponseLinks.md)
 - [ReportByDayByChannel](docs/Model/ReportByDayByChannel.md)
 - [ReportByDayGlobalByChannel](docs/Model/ReportByDayGlobalByChannel.md)
 - [ReportByProductResponseLinks](docs/Model/ReportByProductResponseLinks.md)
 - [ReportFilterParameters](docs/Model/ReportFilterParameters.md)
 - [SetMerchantOrderInfoListRequestItem](docs/Model/SetMerchantOrderInfoListRequestItem.md)
 - [StoreAlertPropertyInfo](docs/Model/StoreAlertPropertyInfo.md)
 - [TrackedOrderProduct](docs/Model/TrackedOrderProduct.md)
 - [OptimiseAllRequest](docs/Model/OptimiseAllRequest.md)
 - [ReportByCategoryRequest](docs/Model/ReportByCategoryRequest.md)
 - [ReportByChannelRequest](docs/Model/ReportByChannelRequest.md)
 - [ReportByProductRequest](docs/Model/ReportByProductRequest.md)
 - [OptimiseRequest](docs/Model/OptimiseRequest.md)


## Documentation For Authorization


## api_key

- **Type**: API key
- **API key parameter name**: Ocp-Apim-Subscription-Key
- **Location**: HTTP header


## Author

<EMAIL>


