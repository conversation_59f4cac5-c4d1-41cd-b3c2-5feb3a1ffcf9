<?php
	require_once('view.translate.inc.php');
	require_once('systems.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_TRANSLATE');

	// Langue de traduction
	$lng = view_selected_language();

	$location = '/admin/config/translate/index.php?lng='.$lng;

	// Contexte de traduction
	$context = $filter = '';
	if( isset($_GET['context']) ){
		$context = $_GET['context'];
		$location .= '&context='.$context;
	}
	if( isset($_GET['filter']) ){
		$filter = $_GET['filter'];
		$location .= '&filter='.$filter;
	}
	if( isset($_GET['page']) ){
		$location .= '&page='.$_GET['page'];
	}
	if( isset($_GET['is_not_tsl']) ){
		$location .= '&is_not_tsl='.$_GET['is_not_tsl'];
	}

	if( isset($_POST['del-tsl']) ){
		if( isset($_POST['del']) && is_array($_POST['del']) && sizeof($_POST['del']) ){
			foreach( $_POST['del'] as $del ){
				$keys = explode( '/#/', $del );
				if( !is_array($keys) || sizeof($keys)!=3 ){
					$error = _("Une ou plusieurs informations sont manquantes empêchant la suppression de ce faire.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
					break;
				}else{
					if( !i18n_translates_del($keys[2], $keys[1], $keys[0]) ){
						$error = _("Une erreur inattendue s'est produite lors de la suppression. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
					}
				}
			}
		}

		if( !isset($error) ){
			header('Location: '.$location);
			exit;
		}
	}

	$rtsl = i18n_translates_get( $lng, $context, '', isset($_GET['is_not_tsl']) && $_GET['is_not_tsl'] ? false : null );

	// Calcule le nombre de pages
	$count_tsl = ria_mysql_num_rows($rtsl);
	$pages = ceil( $count_tsl / 25);

	// Détermine la page en cours de consultation
	$page = 1;
	if( isset($_GET['page']) && is_numeric($_GET['page']) ){
		if( $_GET['page']>0 && $_GET['page']<=$pages )
			$page = $_GET['page'];
	}

	// Détermine les limites inférieures et supérieures pour l'affichage des pages
	$pmin = $page-4;
	if( $pmin<1 )
		$pmin = 1;
	$pmax = $pmin+6;
	if( $pmax>$pages )
		$pmax = $pages;

	define('ADMIN_PAGE_TITLE', _('Traduction') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _('Traductions'); ?></h2>
	<input type="hidden" name="context" id="context" value="<?php print $context; ?>" />
	<input type="hidden" name="lng" id="lng" value="<?php print $lng; ?>" />

	<div id="div-orange">
		<div class="context-menu float-left">
			<div id="riawebsitepicker">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php echo _('Contexte'); ?></span><br/>
						<span class="view"><?php print $context=='' ? _('TOUS') : i18n_contexts_get_name($context); ?></span>
					</div>
					<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" height="8" width="16" alt=""/></a>
					<div class="clear"></div>
				</div>
				<div class="selector"><?php
					print '<a name="p-">TOUS</a>';
					$rcontext = i18n_contexts_get();
					if( $rcontext ){
						while( $cnt = ria_mysql_fetch_array($rcontext) ){
							print '<a name="p-'.$cnt['code'].'">'.$cnt['name'].'</a>';
						}
					}
				?></div>
			</div>
			<div class="clear"></div>
		</div>
		<input type="checkbox" name="is_not_tsl" id="is_not_tsl" value="" <?php print isset($_GET['is_not_tsl']) && $_GET['is_not_tsl'] ? 'checked="checked"' : ''; ?> />
		<label for="is_not_tsl">&nbsp;<?php echo _("Afficher uniquement les chaînes non traduites"); ?></label>
		<br />
		<button onclick="return $('#filter').hide();" value="execute" name="export" class="btn-export" type="submit">
			<?php echo _("Exporter les chaînes à traduire"); ?>
		</button>
		<div class="clear"></div>
	</div>
	<div class="clear"></div>

	<?php
		if( isset($error) ){
			print '
				<div class="error">'.nl2br($error).'</div>
			';
		}
	?>

	<form method="get" action="index.php?context=<?php print $context; ?>&amp;lng=<?php print $lng; ?>" id="frmtranslate">
		<ul class="tabstrip tabstripjax"><?php
			foreach( $config['i18n_lng_used'] as $lang ){
				print '<li><input onclick="return refresh_lst_translate(false, false, \''.$lang.'\');" type="button" value="'.i18n_languages_get_name($lang).'" name="'.$lang.'" '.( $lng==$lang ? 'class="selected"' : '' ).' /></li>';
			}
		?>
		</ul>
	</form>
	<div id="tabpanel">
		<form id="fromtranslate" action="<?php print $location; ?>" method="post">
			<table id="tb-translate" class="checklist">
				<thead>
					<?php if( $count_tsl>15 ){ ?>
					<tr>
						<th colspan="5" class="align-right"><label for="begin"><?php echo _("Filtrer par"); ?>&nbsp;:&nbsp;</label><input type="text" name="begin" id="begin" value="<?php print $filter; ?>" /></th>
					</tr>
					<?php } ?>
					<tr>
						<th id="tsk_checked"><input type="checkbox" name="delalltsl" id="delalltsl" value="" /></th>
						<?php print $context=='' ? '<th id="tsl_context">Contexte</th>' : ''; ?>
						<th id="tsl_original"><?php print i18n_languages_get_name($config['i18n_lng']); ?> <?php echo _("(défaut)"); ?></th>
						<th id="tsl_translate"><?php print i18n_languages_get_name($lng); ?></th>
						<th id="action"></th>
					</tr>
				</thead>
				<tbody><?php
					if( !$rtsl || ria_mysql_num_rows($rtsl)==0 ){
						print '<tr><td colspan="5">' . _("Aucune chaîne de caractère à traduire") . '</td></tr>';
					}else{
						if( $page>1 )
							ria_mysql_data_seek( $rtsl, ($page-1)*25 );

						$count = 0;
						while( $tsl = ria_mysql_fetch_array($rtsl) ){
							if( $count>=25 ){
								break;
							}

							print '
								<tr>
									<td headers="tsk_checked">
										<input type="checkbox" name="del[]" value="'.$tsl['md5'].'/#/'.$tsl['code'].'/#/'.$lng.'" />
									</td>
									'.( $context=='' ? '<td class="uppercase">'.$tsl['context'].'</td>' : '' ).'
									<td headers="tsl_original"><label for="tsl-'.$tsl['code'].$tsl['md5'].'">'.htmlspecialchars( $tsl['original'] ).'</label></td>
									<td headers="tsl_translate">
										<input type="hidden" name="old-tsl" id="old-'.$tsl['code'].$tsl['md5'].'" value="'.htmlspecialchars($tsl['translation']).'" />
										<textarea onblur="return addTranslate(\''.$tsl['code'].'\', \''.$tsl['md5'].'\', '.$count.');" rows="'.ceil(strlen( $tsl['original'])/30).'" name="tsl['.$tsl['md5'].']" id="tsl-'.$tsl['code'].$tsl['md5'].'" cols="100">'.htmlspecialchars($tsl['translation']).'</textarea>
									</td>
									<td headers="action" id="act-'.$count.'"></td>
								</tr>
							';

							$count++;
						}
					}
				?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="5" id="pagination">
							<?php if( ria_mysql_num_rows($rtsl) ){ ?>
								<input type="submit" name="del-tsl" class="float-left" value="<?php echo _("Supprimer"); ?>" />
							<?php } ?>

							<?php
								if( $pages>1 ){
									if( $page>1 )
										print '<a title="Page '.($page-1).'/'.$pages.'" href="#" onclick="refresh_lst_translate('.($page-1).')">&laquo; ' . _("Page précédente") . '</a>';
									for( $i=$pmin; $i<=$pmax; $i++ ){
										if( $i==$page )
											print '<b>'.$i.'</b>&nbsp;|&nbsp;';
										elseif( $i==$pages )
											print '<a title="Page '.$i.'/'.$pages.'" href="#" onclick="refresh_lst_translate('.$i.')">'.$i.'</a>';
										else
											print '<a title="Page '.$i.'/'.$pages.'" href="#" onclick="refresh_lst_translate('.$i.')">'.$i.'</a>&nbsp;|&nbsp;';
									}
									if( $page<$pages )
										print '<a title="Page '.($page+1).'/'.$pages.'" href="#" onclick="refresh_lst_translate('.($page+1).')">' . _("Page suivante") . ' &raquo;</a>';
								}
							?>
						</td>
					</tr>
				</tfoot>
			</table>
		</form>
	</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>