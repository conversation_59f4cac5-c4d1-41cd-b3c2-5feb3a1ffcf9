<?php
/** 
 * \defgroup api-chats-messages Message 
 * \ingroup api-chats-index
 * @{		
 * \page api-chats-messages-add Ajout
 *
 * Cette fonction permet d'ajouter un message sur le chat
 *	
 *	 \code
 *		POST /chats/messages/
 *	 \endcode
 *	
 * @param int $id Obligatoire, identifiant unique pour le message
 * @param int $conv_id Obligatoire, identifiant de la conversation
 * @param int $author_id Obligatoire, identifiant de d l'utilisateur qui emet l'appel
 * @param string $content Obligatoire, contenu du message
 * @param date $date_created Facultatif, date de création du message 
 *	
 * @return true si l'ajout est réalisé avec succés 
 *	@}
*/

switch( $method ){
	case 'add':

		if( !isset($_REQUEST['id']) || !$_REQUEST['id']
			|| !isset($_REQUEST['conv_id']) || !$_REQUEST['conv_id']
			|| !isset($_REQUEST['author_id']) || !is_numeric($_REQUEST['author_id'])
			|| !isset($_REQUEST['content']) || !$_REQUEST['content']
			  ){
			throw new Exception('Paramètres invalide');
		}

		if(!isset($_REQUEST['date_created']) ){
			$_REQUEST['date_created'] = null;
		}

		if( !ch_messages_add($_REQUEST['conv_id'], $_REQUEST['author_id'], $_REQUEST['id'], $_REQUEST['content'], $_REQUEST['date_created']) ){
			throw new Exception('Erreur lors de la création du message.');
		}

		$result = true;

		break;
}
