<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/resources.proto

namespace Google\Cloud\ServiceManagement\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\ServiceManagement\V1\Diagnostic\Kind instead.
     * @deprecated
     */
    class Diagnostic_Kind {}
}
class_exists(Diagnostic\Kind::class);
@trigger_error('Google\Cloud\ServiceManagement\V1\Diagnostic_Kind is deprecated and will be removed in the next major release. Use Google\Cloud\ServiceManagement\V1\Diagnostic\Kind instead', E_USER_DEPRECATED);

