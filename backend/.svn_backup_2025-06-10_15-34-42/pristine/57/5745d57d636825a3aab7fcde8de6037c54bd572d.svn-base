<?php
	require_once('orders.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class ordersGetProductsTest extends PHPUnit_Framework_TestCase {

        /** Cette fonction permet de tester la fonction qui retourne tous les produits contenus dans un panier/commande.
         */
        public function testOrdGetProducts(){

            $rord = ord_products_get(2);
            $this->assertTrue($rord || ria_mysql_num_rows($rord), 'Erreur: Aucun résultat retourné par ord_products_get');
            $this->assertTrue(ria_mysql_num_rows($rord) == 3, 'Erreur: ord_products_get ne retourne pas le bon nombre de résultats');
            
            while($ord = ria_mysql_fetch_assoc($rord)){
                $this->assertTrue(in_array($ord['id'], array(1,2,3)), 'Erreur: ord_products_get ne retourne pas les bon produit');
            }
        }

        /** Fonction permettant de tester la récupération d'un produit d'une commande
         */
        public function testOrdersGetProductsByPrdID(){

            $rord = ord_products_get( 2, false, 1);
            $this->assertTrue($rord || ria_mysql_num_rows($rord), 'Erreur: Aucun résultat retourné par ord_products_get');
			$ord = ria_mysql_fetch_assoc($rord);

            $this->assertEquals(2, $ord['ord_id'], 'Erreur: identifiant de la commande non conforme à la valeur dans la base de donnée');

            $this->assertEquals(1, $ord['id'], 'Erreur: identifiant du produit non conforme à la valeur dans la base de donnée');
        }
		
    }
    
