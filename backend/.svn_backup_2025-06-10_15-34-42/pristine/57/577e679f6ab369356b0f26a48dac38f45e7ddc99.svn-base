Nowdoc and heredoc strings
-----
<?php

// empty strings
<<<'EOS'
EOS;
<<<EOS
EOS;

// constant encapsed strings
<<<'EOS'
Test '" $a \n
EOS;
<<<EOS
Test '" \$a \n
EOS;

// encapsed strings
<<<EOS
Test $a
EOS;
<<<EOS
Test $a and $b->c test
EOS;

b<<<EOS
Binary
EOS;

-----
array(
    0: Scalar_String(
        value:
        comments: array(
            0: // empty strings
        )
    )
    1: Scalar_String(
        value:
    )
    2: Scalar_String(
        value: Test '" $a \n
        comments: array(
            0: // constant encapsed strings
        )
    )
    3: Scalar_String(
        value: Test '" $a

    )
    4: Scalar_Encapsed(
        parts: array(
            0: Scalar_EncapsedStringPart(
                value: Test
            )
            1: Expr_Variable(
                name: a
            )
        )
        comments: array(
            0: // encapsed strings
        )
    )
    5: Scalar_Encapsed(
        parts: array(
            0: Scalar_EncapsedStringPart(
                value: Test
            )
            1: Expr_Variable(
                name: a
            )
            2: Scalar_EncapsedStringPart(
                value:  and
            )
            3: Expr_PropertyFetch(
                var: Expr_Variable(
                    name: b
                )
                name: c
            )
            4: Scalar_EncapsedStringPart(
                value:  test
            )
        )
    )
    6: Scalar_String(
        value: Binary
    )
)