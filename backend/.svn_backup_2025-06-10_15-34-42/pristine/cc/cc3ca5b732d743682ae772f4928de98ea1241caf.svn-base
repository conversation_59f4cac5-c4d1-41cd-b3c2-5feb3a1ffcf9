<?php

/**
 * ADFS PRP IDP protocol support for SimpleSAMLphp.
 *
 * <AUTHOR> SURFnet bv, <<EMAIL>>
 * @package SimpleSAMLphp
 */

namespace SimpleSAML\Module\adfs;

use SimpleSAML\Configuration;
use SimpleSAML\Session;
use Symfony\Component\HttpFoundation\Request;

$config = Configuration::getInstance();
$session = Session::getSessionFromRequest();
$request = Request::createFromGlobals();

$controller = new AdfsController($config, $session);
$t = $controller->prp($request);
$t->send();
