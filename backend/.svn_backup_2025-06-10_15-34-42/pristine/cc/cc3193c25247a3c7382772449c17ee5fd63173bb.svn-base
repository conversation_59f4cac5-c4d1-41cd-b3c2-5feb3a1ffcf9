<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/metric.proto

namespace GPBMetadata\Google\Api;

class Metric
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Label::initOnce();
        \GPBMetadata\Google\Api\LaunchStage::initOnce();
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ac4080a17676f6f676c652f6170692f6d65747269632e70726f746f120a" .
            "676f6f676c652e6170691a1d676f6f676c652f6170692f6c61756e63685f" .
            "73746167652e70726f746f1a1e676f6f676c652f70726f746f6275662f64" .
            "75726174696f6e2e70726f746f22fd050a104d6574726963446573637269" .
            "70746f72120c0a046e616d65180120012809120c0a047479706518082001" .
            "2809122b0a066c6162656c7318022003280b321b2e676f6f676c652e6170" .
            "692e4c6162656c44657363726970746f72123c0a0b6d65747269635f6b69" .
            "6e6418032001280e32272e676f6f676c652e6170692e4d65747269634465" .
            "7363726970746f722e4d65747269634b696e64123a0a0a76616c75655f74" .
            "79706518042001280e32262e676f6f676c652e6170692e4d657472696344" .
            "657363726970746f722e56616c756554797065120c0a04756e6974180520" .
            "01280912130a0b6465736372697074696f6e18062001280912140a0c6469" .
            "73706c61795f6e616d6518072001280912470a086d65746164617461180a" .
            "2001280b32352e676f6f676c652e6170692e4d6574726963446573637269" .
            "70746f722e4d657472696344657363726970746f724d6574616461746112" .
            "2d0a0c6c61756e63685f7374616765180c2001280e32172e676f6f676c65" .
            "2e6170692e4c61756e636853746167651ab0010a184d6574726963446573" .
            "63726970746f724d6574616461746112310a0c6c61756e63685f73746167" .
            "6518012001280e32172e676f6f676c652e6170692e4c61756e6368537461" .
            "67654202180112300a0d73616d706c655f706572696f6418022001280b32" .
            "192e676f6f676c652e70726f746f6275662e4475726174696f6e122f0a0c" .
            "696e676573745f64656c617918032001280b32192e676f6f676c652e7072" .
            "6f746f6275662e4475726174696f6e224f0a0a4d65747269634b696e6412" .
            "1b0a174d45545249435f4b494e445f554e53504543494649454410001209" .
            "0a054741554745100112090a0544454c54411002120e0a0a43554d554c41" .
            "54495645100322710a0956616c756554797065121a0a1656414c55455f54" .
            "5950455f554e535045434946494544100012080a04424f4f4c100112090a" .
            "05494e5436341002120a0a06444f55424c451003120a0a06535452494e47" .
            "100412100a0c444953545249425554494f4e100512090a054d4f4e455910" .
            "0622750a064d6574726963120c0a0474797065180320012809122e0a066c" .
            "6162656c7318022003280b321e2e676f6f676c652e6170692e4d65747269" .
            "632e4c6162656c73456e7472791a2d0a0b4c6162656c73456e747279120b" .
            "0a036b6579180120012809120d0a0576616c75651802200128093a023801" .
            "425f0a0e636f6d2e676f6f676c652e617069420b4d657472696350726f74" .
            "6f50015a37676f6f676c652e676f6c616e672e6f72672f67656e70726f74" .
            "6f2f676f6f676c65617069732f6170692f6d65747269633b6d6574726963" .
            "a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

