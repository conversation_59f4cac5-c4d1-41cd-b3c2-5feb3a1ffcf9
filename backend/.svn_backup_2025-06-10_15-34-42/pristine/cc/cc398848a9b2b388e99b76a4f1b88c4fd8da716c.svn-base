<?php
/**
 * \defgroup brands Marques
 * \ingroup pim 
 * @{
 * \page api-brands-index-get Chargement
 * 
 * Cette fonction retourne l'ensemble des marques de produits
 *
 *		\code
 *			GET /brands/
 *		\endcode
 *
 * @param int|array $id Facultatif, identifiant ou tableau d'identifiants de marques sur lesquelles filtrer le résultat
 * @param int $start Facultatif, pagination : enregistrement de départ à retourner
 * @param int $limit Facultatif, pagination : nombre maximum de comptes à retourner
 *
 * @return Json sous la forme suivante :
 *		\code{.json}
 *			{
 *				"id" : identifiant de la marque
 *				"name" : nom de la marque	
 *				"ref" : reference de la marque
 *				"title" : titre de la marque
 *				"publish" : publication de la marque
 *				"url" : url du site Internet de la marque
 *				"products" : nombre de produits publiés
 *				"img_id" : identifiant de l'image représentant le logo de la société
 *				"is_sync" : booléen indiquant si la marque est synchronisée avec la gestion commerciale
 *				"desc" : description de la marque
 *				"url_alias" : url virtuelle de la marque
 *				"tag_title" : titre utilisé dans le référencement
 *				"tag_desc" : description utilisée dans le référencement
 *				"keywords" : mots clés utilisés dans le référencement
 *				"pos" : position de la marque
 *			}	
 *		 \endcode
 * @}
*/

switch( $method ){
	case 'get':

		// Paramètre $ids
		$ids = 0;
		if( isset($_GET['id']) && is_array($_GET['id']) ){
			foreach( $_GET['id'] as $id ){
				if( !is_numeric($id) ){
					throw new Exception("Les identifiants fournis en arguments sont incorrects");
				}
			}
			$ids = $_GET['id'];
		}elseif( isset($_GET['id']) && is_numeric($_GET['id']) ){
			$ids = $_GET['id'];
		}

		// Paramètre start (non géré par prd_brands_get)
		$start = 0;
		if( isset($_GET['start']) && is_numeric($_GET['start']) ){
			$start = $_GET['start'];
		}

		// Paramètre limit (non géré par prd_brands_get)
		$limit = 0;
		if( isset($_GET['limit']) && is_numeric($_GET['limit']) ){
			$limit = $_GET['limit'];
		}

		$array = array();
		$rbrd = prd_brands_get( $ids, false, '', '', false, null, false, false, false, false, false, false, false, false, $start, $limit );
		while($brd = ria_mysql_fetch_assoc($rbrd)){
			$array[] = $brd;
		}

		$result = true;
		$content = $array;

		break;
}