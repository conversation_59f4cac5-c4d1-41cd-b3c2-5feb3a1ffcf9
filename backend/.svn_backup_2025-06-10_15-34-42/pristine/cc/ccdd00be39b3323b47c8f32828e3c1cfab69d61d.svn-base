HttpKernel Component
====================

The HttpKernel component provides a structured process for converting a Request
into a Response by making use of the EventDispatcher component. It's flexible
enough to create a full-stack framework (Symfony), a micro-framework (Silex) or
an advanced CMS system (Drupal).

Resources
---------

  * [Documentation](https://symfony.com/doc/current/components/http_kernel/index.html)
  * [Contributing](https://symfony.com/doc/current/contributing/index.html)
  * [Report issues](https://github.com/symfony/symfony/issues) and
    [send Pull Requests](https://github.com/symfony/symfony/pulls)
    in the [main Symfony repository](https://github.com/symfony/symfony)
