<?php
require_once('prd/relations.inc.php');

/**
 * \defgroup api-products-relations Relations entre produits 
 * \ingroup Produits
 * @{
*/

switch( $method ){
    /** @{@}
 	 * @{
     * \page api-relations-index-upd Mise à jour 
     *
     * Permet l'ajout ou la modification d'une relation entre produits'
     *
     *      \code
     *          PUT /products/relations/
     *      \endcode
     * 
     * @param raw_data Obligatoire, Donnée en json_decode sous la forme : 
     *		\code{.json}
     *			{
     *   				"src_prd"			Obligatoire	: Identifiant du produit source (parent)
     *   				"dst_prd"			Obligatoire	: Identifiant du produit destination (enfant)
     *   				"rel_type"			Obligatoire	: Identifiant du type de relation entre produits
     *			}
     *		\endcode
     *
     * @return true si la mise à jour s'est déroulée avec succès 
	 * @}
	*/
    case 'upd': 
        // Récupération des valeurs envoyés
        global $method, $config;
        $obj = json_decode($raw_data, true);

        // Vérifie que le contenu est bien un tableau de données
        if( !is_array($obj) ){
            throw new Exception("Les données reçues ne sont pas sous forme de tableau");
        }

        // Récupère l'ensemble des types de relation
        $rreltypes = prd_relations_types_get();
        $arreltypes = array();
        
        while($readreltype = ria_mysql_fetch_array($rreltypes))
        {
            $arreltypes[$readreltype['id']] = $readreltype;
        }

        // Parcours de tout les élléments pour faire le traitement
        foreach ($obj as $rel) {
            // Vérifie que les éléments obligatoires sont bien présents
            if (!isset($rel['src_prd'], $rel['dst_prd'], $rel['rel_type'])) 
            {
                throw new Exception("Les paramètres obligatoires src_prd, dst_prd et rel_type n'ont pas été définis".print_r($rel, true));
            }
            // Vérifie que le type existe
            if (!isset($arreltypes[$rel['rel_type']]))
            {
                throw new Exception("Le type de la relation n'existe pas, type = ".print_r($rel['rel_type'], true));
            }
            // Création de l'association
            if ( !prd_relations_add( $rel['src_prd'], $rel['dst_prd'], $rel['rel_type'] ) ) {
                throw new Exception("Erreur lors de l'association entre 2 produits : ".print_r($rel, true));
            }
        }

        // Renvois que le traitement à bien été fait
        $result = true;
        break;
    /** @{@}
 	 * @{
     * \page api-relations-index-del Suppression 
     *
     * Permet de supprimer une relation entre produits
     *
     *      \code
     *          DELETE /products/relations/
     *      \endcode
     *   
     * @param raw_data Obligatoire, Donnée en json_decode sous la forme :
     *		\code{.json}
     *			{
     *   				"src_prd"			Obligatoire	: Identifiant du produit source (parent)
     *   				"dst_prd"			Obligatoire	: Identifiant du produit destination (enfant)
     *   				"rel_type"			Obligatoire	: Identifiant du type de relation entre produits
     *			}
     *		\endcode
     *
     * @return true si la suppresion s'est déroulée avec succès 
	 * @}
	*/
    case 'del':
        // Récupération des valeurs envoyés
        global $method, $config;
        $obj = json_decode($raw_data, true);

        // Vérifie que le contenu est bien un tableau de données
        if( !is_array($obj) ) {
            throw new Exception("Les données reçues ne sont pas sous forme de tableau");
        }

        // Parcours de tout les élléments pour faire le traitement
        foreach ($obj as $rel) {

            // Vérifie que les éléments obligatoires sont bien présents
            if (!isset($rel['src_prd'], $rel['dst_prd'], $rel['rel_type'])) {
                throw new Exception("Les paramètres obligatoires src_prd, dst_prd et rel_type n'ont pas été définis");
            }
            // Supréssion de l'association
            if ( !prd_relations_del( $rel['src_prd'], $rel['dst_prd'], $rel['rel_type'] ) ) {
                throw new Exception("Erreur lors la suppression de la relation entre 2 produits : ".print_r($rel, true));
            }
        }
      
        // Renvois que le traitement à bien été fait
        $result = true;
        break;
}
///@}