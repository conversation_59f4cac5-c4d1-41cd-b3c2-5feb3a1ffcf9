<documentation title="File Comments">
    <standard>
    <![CDATA[
    Files must have a non-empty doc comment.  The short description must be on the second line of the comment.  Each description must have one blank comment line before and after.  There must be one blank line before the tags in the comments.  There must be a category, package, author, license, and link tag.  There may only be one category, package, subpackage, license, version, since and deprecated tag.  The tags must be in the order category, package, subpackage, author, copyright, license, version, link, see, since, and deprecated.  The php version must be specified.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A file comment is used.">
        <![CDATA[
<?php
<em>/**
 * Short description here.
 *
 * PHP version 5
 *
 * @category Foo
 * @package Foo_Helpers
 * <AUTHOR> <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */</em>
        ]]>
        </code>
        <code title="Invalid: No doc comment for the class.">
        <![CDATA[
<?php
<em></em>
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Short description is the second line of the comment.">
        <![CDATA[
<?php
/**
 * <em>Short description here.</em>
 *
 * PHP version 5
 *
 * @category Foo
 * @package Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
        <code title="Invalid: An extra blank line before the short description.">
        <![CDATA[
<?php
/**
 * <em></em>
 * <em>Short description here.</em>
 *
 * PHP version 5
 *
 * @category Foo
 * @package Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Exactly one blank line around descriptions.">
        <![CDATA[
<?php
/**
 * Short description here.
 * <em></em>
 * PHP version 5
 * <em></em>
 * @category Foo
 * @package Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
        <code title="Invalid: Extra blank lines around the descriptions.">
        <![CDATA[
<?php
/**
 * Short description here.
 * <em></em>
 * <em></em>
 * PHP version 5
 * <em></em>
 * <em></em>
 * @category Foo
 * @package Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Exactly one blank line before the tags.">
        <![CDATA[
<?php
/**
 * Short description here.
 *
 * PHP version 5
 * <em></em>
 * @category Foo
 * @package Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
        <code title="Invalid: Extra blank lines before the tags.">
        <![CDATA[
<?php
/**
 * Short description here.
 *
 * PHP version 5
 * <em></em>
 * <em></em>
 * @category Foo
 * @package Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: All required tags are used.">
        <![CDATA[
<?php
/**
 * Short description here.
 *
 * PHP version 5
 *
 * <em>@category</em> Foo
 * <em>@package</em> Foo_Helpers
 * <em>@author</em> Marty McFly <<EMAIL>>
 * <em>@copyright</em> 2013-2014 Foo Inc.
 * <em>@license</em> MIT License
 * <em>@link</em> http://example.com
 */
        ]]>
        </code>
        <code title="Invalid: Missing an author tag.">
        <![CDATA[
<?php
/**
 * Short description here.
 *
 * PHP version 5
 *
 * @category Foo
 * @package Foo_Helpers
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Tags that should only be used once are only used once.">
        <![CDATA[
<?php
/**
 * Short description here.
 *
 * PHP version 5
 *
 * <em>@category</em> Foo
 * <em>@package</em> Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * <em>@license</em> MIT License
 * @link http://example.com
 */
        ]]>
        </code>
        <code title="Invalid: Multiple category tags.">
        <![CDATA[
<?php
/**
 * Short description here.
 *
 * PHP version 5
 *
 * <em>@category</em> Foo
 * <em>@category</em> Bar
 * @package Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: PHP version specified.">
        <![CDATA[
<?php
/**
 * Short description here.
 *
 * <em>PHP version 5</em>
 *
 * @category Foo
 * @package Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
        <code title="Invalid: Category and package tags are swapped in order.">
        <![CDATA[
<?php
/**
 * Short description here.
 *
 * PHP version 5
 *
 * <em>@package</em> Foo_Helpers
 * <em>@category</em> Foo
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Tags are in the correct order.">
        <![CDATA[
<?php
/**
 * Short description here.
 *
 * PHP version 5
 *
 * @category Foo
 * @package Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
        <code title="Invalid: No php version specified.">
        <![CDATA[
<?php
/**
 * Short description here.
 *
 * @category Foo
 * @package Foo_Helpers
 * <AUTHOR> McFly <<EMAIL>>
 * @copyright 2013-2014 Foo Inc.
 * @license MIT License
 * @link http://example.com
 */
        ]]>
        </code>
    </code_comparison>
</documentation>
