<?php
	/** \file index.php
	 * 	Ce fichier permet d'afficher la liste des différents magasins créés. Il permet d'y accéder et d'en ajouter des nouveaux.
	 */
	
	require_once('delivery.inc.php');
	
	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE');
	
	unset($error);

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	// Publication d'un ou plusieurs magasins
	if( isset($_POST['btn_publish']) && isset($_POST['str']) && is_array($_POST['str']) ){
		if( !dlv_stores_set_publish( $_POST['str'], true ) ){
			$error = _("Une erreur inattendue s'est produite lors de la publication des magasins sélectionnés. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
		}
	}
	
	// Dé-Publication d'un ou plusieurs magasins
	if( isset($_POST['unpublish']) && isset($_POST['str']) && is_array($_POST['str']) ){
		if( !dlv_stores_set_publish( $_POST['str'], false ) ){
			$error = _("Une erreur inattendue s'est produite lors de la dépublication des magasins sélectionnés. \nVeuillez réessayer ou prendre contact pour nous signaler l'erreur");
		}
	}
	
	// Suppression
	if( isset($_POST['del']) ){
		if( !isset($_POST['str']) ){
			$error = _("Veuillez sélectionner un ou plusieurs magasins à supprimer, en cochant la case située devant leur nom.");
		}else{
			if( is_array($_POST['str']) ){
				foreach( $_POST['str'] as $s ){
					dlv_stores_del($s);
				}
			}
		}
	}
	
	$c = isset($_REQUEST['c']) && sys_countries_exists_code($_REQUEST['c']) ? sys_countries_get_name($_REQUEST['c']) : '';
	$pub = isset($_REQUEST['pub']) && in_array(strtolower(trim($_REQUEST['pub'])), array('false', 'true', 'null')) ? strtolower(trim($_REQUEST['pub'])) : 'null';
	$seg = isset($_REQUEST['seg']) && is_numeric($_REQUEST['seg']) && $_REQUEST['seg'] > 0 && seg_segments_exists( $_REQUEST['seg'], CLS_STORE ) ? $_REQUEST['seg'] : 0;
	$reg = isset($_REQUEST['region']) && is_numeric($_REQUEST['region']) ? $_REQUEST['region'] : false;
	
	$sales_types = 0;
	if( isset($_REQUEST['sales_types']) ){
		if( is_numeric($_REQUEST['sales_types']) && $_REQUEST['sales_types'] ){
			$sales_types = $_REQUEST['sales_types'];
		}elseif( $_REQUEST['sales_types'] === 'none' ){
			$sales_types = 'none';
		}
	}
	
	// Bouton d'export
	if( isset($_POST['export']) ){

		$p = $pub=='true' ? true : ( $pub=='false' ? false : null );
		$s = is_numeric( $sales_types ) && $sales_types > 0 ? array($sales_types) : ( $sales_types === 'none' ? 'none' : array() );

		header('Content-disposition: attachment; filename="export-magasins.csv"');
		header('Pragma: no-cache');
		header('Expires: 0');

		$output = fopen('php://output', 'w');

		$sort = $_REQUEST['sort'];
		if( $sort === 'address' ){
			$sort = 'zipcode';
		}
		$stores = dlv_stores_get( 0, null, array($_REQUEST['sort'] => $_REQUEST['dir']), 0, 0, false, 0, $c, 0, 0, false, false, false, $s, false, false, $p, '', $seg, '', array(), $reg ? array(_ZONE_RGN_FRANCE => $reg ) : false);

		$ar = array();
		fputcsv($output, array('Identifiant', 'Nom du magasin', 'Description', 'Adresse', 'Adresse (suite)', 'Code postal', 'Ville', 'Pays', 'Gérant(e)', 'Téléphone', 'Fax', 'Email', 'Site internet', 'Livraison possible', 'Longitude', 'Latitude', 'Synchronisé', 'Publié'), ';');
		while( ($r = ria_mysql_fetch_assoc($stores)) ){
			$r_export = array(
				$r['id'],
				$r['title'],
				html_strip_tags(str_replace(array("\r", "\r\n", "\n", ";"), ' ', $r['desc'])),
				$r['address1'],
				$r['address2'],
				$r['zipcode'],
				$r['city'],
				$r['country'],
				$r['manager'],
				$r['phone'],
				$r['fax'],
				$r['email'],
				$r['website'],
				(bool)$r['allow_delivery'] ? 'Oui' : 'Non',
				$r['longitude'],
				$r['latitude'],
				(bool)$r['is_sync'] ? 'Oui' : 'Non',
				(bool)$r['publish'] ? 'Oui' : 'Non',
			);
			fputcsv($output, $r_export, ';');
		}

		fclose($output);
		exit;
	}

	$checkbox = gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_DEL') || gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_PUBLISH');

	$colspan = $checkbox ? 5 : 4;

	define('ADMIN_PAGE_TITLE', _('Magasins') . ' - '. _('Livraison des commandes') . ' - '. _('Configuration'));
	require_once('admin/skin/header.inc.php');

	if( !isset($_REQUEST['sort']) ) $_REQUEST['sort'] = 'name';
	if( !isset($_REQUEST['dir']) ) $_REQUEST['dir'] = 'asc';
	
	// Charge la liste des magasins
	if( !isset($_GET['mdl']) ){
		$_GET['mdl'] = 0;
	}
	if( !isset($_GET['fld']) ){
		$_GET['fld'] = 0;
	}
	
	$have_mdl_filter = false;
	$have_fld_filter = false;
	
	if( isset($_GET['mdl']) && is_numeric($_GET['mdl']) && $_GET['mdl']>0 ){
		if( $rm = fld_models_get($_GET['mdl']) ){
			$m = ria_mysql_fetch_array($rm);
			if( $m['cls_id']==CLS_STORE ){
				$have_mdl_filter = true;
				$stores = fld_models_get_objects($_GET['mdl']);
			}
		}
	}
	
	if( !$have_mdl_filter ){
		if( isset($_GET['fld']) && is_numeric($_GET['fld']) && $_GET['fld']>0 ){
			if( $rf = fld_fields_get($_GET['fld']) ){
				$f = ria_mysql_fetch_array($rf);
				if( $f['cls_id']==CLS_STORE ){
					$have_fld_filter = true;
					$stores = fld_fields_get_objects($_GET['fld']);
				}
			}
		}
	}
	
	if( !$have_mdl_filter && !$have_fld_filter ){
		$pub = $pub=='true' ? true : ( $pub=='false' ? false : null );
		$s = is_numeric( $sales_types ) && $sales_types > 0 ? array($sales_types) : ( $sales_types === 'none' ? 'none' : array() );
		
		$stores = dlv_stores_get( 0, null, array($_REQUEST['sort'] => $_REQUEST['dir']), 0, 0, false, 0, $c, 0, 0, false, false, false, $s, false, false, $pub, '', $seg, '', array(), $reg ? array(_ZONE_RGN_FRANCE => $reg) : false );
	}
	
	$stores_count = $stores ? ria_mysql_num_rows($stores) : 0;
	$pages = ceil( $stores_count / 25 );
	$page = isset($_GET['page']) && is_numeric($_GET['page']) ? $_GET['page'] : 1;
	if( $page<1 ) $page = 1;
	if( $page>$pages ) $page = $pages;
	if( $page>1 ) ria_mysql_data_seek($stores,($page-1)*25);

	$page_start = $page-3>1 ? $page-3 : 1;
	$page_stop = $page+3<$pages ? $page+3 : $pages;	

?>
	<h2><?php echo _("Magasins"); ?> (<?php print ria_number_format($stores_count) ?>)</h2>
	
	<div class="lng-menu">
		<div id="riacountriespicker" class="riapicker">
			<div class="selectorview">
				<div class="left">
					<span class="function_name"><?php echo _("Sélectionnez un pays"); ?></span><br/>
					<span class="view"><?php print isset($_GET['c']) && sys_countries_exists_code($_GET['c']) ? sys_countries_get_name($_GET['c']) : _('Tous les pays'); ?></span>
				</div>
				<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" class="fleche" alt=""/></a>
				<div class="clear"></div>
			</div>
			<div class="selector">
				<a name="c-"><?php echo _("Tous les pays"); ?></a>
			<?php
				require_once('sys.countries.inc.php');
				$countries = dlv_stores_get_countries();
				if( $countries && ria_mysql_num_rows($countries) ){
					while( $country = ria_mysql_fetch_array($countries) )
						print '<a name="c-'.$country['code'].'">'.htmlspecialchars( $country['name'] ).'</a>';
				}
			?></div>
		</div>
		<?php if( $c === 'France' ){
			$title = _('Toutes les régions');
			if (isset($_GET['region']) && is_numeric($_GET['region']) && $_GET['region']) {
				$r_zone = sys_zones_get(0, $_GET['region'], '', false, 0, '', _ZONE_RGN_FRANCE);
				if ($r_zone && ria_mysql_num_rows($r_zone)) {
					$zone = ria_mysql_fetch_assoc($r_zone);
					$title = $zone['name'];
				}
			}
		?>
		<div id="riacountiespicker" class="riapicker">
			<div class="selectorview">
				<div class="left">
					<span class="function_name"><?php echo _("Régions"); ?></span><br/>
					<span class="view"><?php print htmlspecialchars($title); ?></span>
				</div>
				<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" class="fleche" alt=""/></a>
				<div class="clear"></div>
			</div>
			<div class="selector">
				<a name="c-"><?php echo _("Toutes les régions"); ?></a>
			<?php
			$rcounty = sys_zones_get(0, '', '', false, 0, '', _ZONE_RGN_FRANCE, array('name' => 'ASC'), -1, -1, true, true); 
			if( $rcounty ){
				while( $county = ria_mysql_fetch_array($rcounty) ){
					print '<a name="c-'.$county['code'].'">' .htmlspecialchars( $county['name'] ).'</a>';
				}
			}
			?>
			</div>
		</div>
		<?php } ?>
		<div id="riapublishpicker" class="riapicker">
			<div class="selectorview">
				<div class="left">
					<span class="function_name"><?php echo _("Publication"); ?></span><br/>
					<?php
					$libel = 'Tous les magasins';
					if( isset($_GET['pub']) ){
						if( strtolower(trim($_GET['pub']))=='true' )
							$libel = 'Publiés uniquement';
						elseif( strtolower(trim($_GET['pub']))=='false' )
							$libel = 'Non publiés uniquement';
					}
					?>
					<span class="view"><?php print _($libel); ?></span>
				</div>
				<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" class="fleche" alt=""/></a>
				<div class="clear"></div>
			</div>
			<div class="selector">
				<a name="pub-null"><?php echo _("Tous les magasins"); ?></a>
				<a name="pub-true"><?php echo _("Publiés uniquement"); ?></a>
				<a name="pub-false"><?php echo _("Non publiés uniquement"); ?></a>
			</div>
		</div>
		<?php
		$rsegs = seg_segments_get( 0, CLS_STORE );
		if( $rsegs && ria_mysql_num_rows($rsegs) ){
		?>
		<div id="riasegmentpicker" class="riapicker">
			<div class="selectorview">
				<div class="left">
					<span class="function_name"><?php echo _("Segmentation"); ?></span><br/>
					<?php
					$libel = 'Tous les magasins';
					if( isset($seg) && $seg > 0 ){
						$libel = htmlspecialchars( ria_mysql_result( seg_segments_get( $seg ), 0, 'name' ) );
					}
					?>
					<span class="view"><?php print $libel; ?></span>
				</div>
				<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" class="fleche" alt=""/></a>
				<div class="clear"></div>
			</div>
			<div class="selector">
				<a name="seg-0"><?php echo _("Tous les magasins"); ?></a>
				<?php
				while( $seg_data = ria_mysql_fetch_array($rsegs) ){
					print '	<a name="seg-'.$seg_data['id'].'">'.htmlspecialchars($seg_data['name']).'</a>';
				}
				?>
			</div>
		</div>
		<?php } ?>
		<?php
			$rtype = dlv_sales_types_get();
			if( $rtype && ria_mysql_num_rows($rtype) ){
				$libel = _('Tous les magasins');
				if( isset($sales_types) ){
					if( $sales_types === 'none' ){
						$libel = _("Rattachés à aucun type");
					}elseif( is_numeric($sales_types) && $sales_types > 0 ){
						$libel = htmlspecialchars( ria_mysql_result( dlv_sales_types_get( $sales_types ), 0, 'name' ) );
					}
				}

				print '
					<div id="riatypesalespicker" class="riapicker">
						<div class="selectorview">
							<div class="left">
								<span class="function_name">' . _("Type de ventes") . '</span><br/>
								<span class="view">'.$libel.'</span>
							</div>
							<a class="btn" name="btn"><img src="/admin/images/stats/fleche.gif" class="fleche" alt=""/></a>
							<div class="clear"></div>
						</div>
						<div class="selector">
							<a name="type-0">' . _("Tous les magasins") . '</a>
							<a name="type-none">' . _("Rattachés à aucun type") . '</a>
				';

				while( $stype = ria_mysql_fetch_assoc($rtype) ){
					print '	<a name="type-'.$stype['id'].'">'.htmlspecialchars($stype['name']).'</a>';
				}
				
				print '
						</div>
					</div>
				';
			}
		?>
	</div>

	<?php
		if( isset($error) && trim($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<form action="index.php" method="post">
	<input type="hidden" name="sort" value="<?php print htmlspecialchars($_REQUEST['sort']); ?>" />
	<input type="hidden" name="dir" value="<?php print htmlspecialchars($_REQUEST['dir']); ?>" />

	<input type="hidden" name="pub" id="publish" value="<?php print isset($_REQUEST['pub']) ? $_REQUEST['pub'] : ''; ?>" />
	<input type="hidden" name="c" id="country" value="<?php print isset($_REQUEST['c']) ? $_REQUEST['c'] : ''; ?>" />
	<input type="hidden" name="reg" id="county" value="<?php print isset($_REQUEST['region']) ? $_REQUEST['region'] : ''; ?>" />
	<input type="hidden" name="seg" id="seg" value="<?php print isset($_REQUEST['seg']) ? $_REQUEST['seg'] : ''; ?>" />
	<input type="hidden" name="sales_types" id="sales_types" value="<?php print isset($_REQUEST['sales_types']) ? $_REQUEST['sales_types'] : ''; ?>" />

<?php // champs spécifiques à l'export ?>

	<table id="list-stores" class="checklist tablesorter">
		<thead>
			<tr>
				<?php
					$ar_class = array(
						'ref' => 'header '.( $_REQUEST['sort']=='id' ? ( $_REQUEST['dir']=='asc' ? 'headerSortUp' : 'headerSortDown' ) : '' ),
						'name' => 'header '.( $_REQUEST['sort']=='name' ? ( $_REQUEST['dir']=='asc' ? 'headerSortUp' : 'headerSortDown' ) : '' ),
						'address' => 'header '.( $_REQUEST['sort']=='address' ? ( $_REQUEST['dir']=='asc' ? 'headerSortUp' : 'headerSortDown' ) : '' )
					);
				?>
				<?php if( $checkbox ){ ?>
				<th id="select"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<?php } ?>
				<th id="ref" class="<?php print $ar_class['ref']; ?>">
					<?php print '<a onclick="return strSwitchTablePage( ' . $page . ', '.$pages.', '.( $have_mdl_filter ? $_GET['mdl'] : 0 ).', '.($have_fld_filter ? $_GET['fld'] : 0 ).', \'id\', ' . ($_REQUEST['sort'] === 'id' && $_REQUEST['dir'] === 'asc' ? '\'desc\'' : '\'asc\'' ) . ');" href="index.php?sort=id' . ( $_REQUEST['sort']=='id' && $_REQUEST['dir']=='asc' ? '&amp;dir=desc' : '&amp;dir=asc') . ( $have_mdl_filter ? '&amp;mdl='.$_GET['mdl'] : ($have_fld_filter ? '&amp;fld='.$_GET['fld'] : '') ) . '">'._("Référence"); ?></a>
				</th>
				<th id="name" class="<?php print $ar_class['name']; ?>">
					<?php print '<a onclick="return strSwitchTablePage( ' . $page . ', '.$pages.', '.( $have_mdl_filter ? $_GET['mdl'] : 0 ).', '.($have_fld_filter ? $_GET['fld'] : 0 ).', \'name\', ' . ($_REQUEST['sort'] === 'name' && $_REQUEST['dir'] === 'asc' ? '\'desc\'' : '\'asc\'' ) . ');" href="index.php?sort=name' . ( $_REQUEST['sort']=='name' && $_REQUEST['dir']=='asc' ? '&amp;dir=desc' : '&amp;dir=asc') . ( $have_mdl_filter ? '&amp;mdl='.$_GET['mdl'] : ($have_fld_filter ? '&amp;fld='.$_GET['fld'] : '') ) . '">'._("Nom du magasin"); ?></a>
				</th>
				<th id="address" class="<?php print $ar_class['address']; ?>">
					<?php print '<a onclick="return strSwitchTablePage( ' . $page . ', '.$pages.', '.( $have_mdl_filter ? $_GET['mdl'] : 0 ).', '.($have_fld_filter ? $_GET['fld'] : 0 ).', \'address\', ' . ($_REQUEST['sort'] === 'address' && $_REQUEST['dir'] === 'asc' ? '\'desc\'' : '\'asc\'' ) . ');" href="index.php?sort=address' . ( $_REQUEST['sort']=='address' && $_REQUEST['dir']=='asc' ? '&amp;dir=desc' : '&amp;dir=asc') . ( $have_mdl_filter ? '&amp;mdl='.$_GET['mdl'] : ($have_fld_filter ? '&amp;fld='.$_GET['fld'] : '') ) . '">'._("Adresse").'</a>'; ?>
				</th>
				<th id="contact"><?php echo _("Contact"); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php
				if( !$stores || !ria_mysql_num_rows($stores) ){
					print '<tr><td colspan="'.$colspan.'">' . _("Aucun magasins") . '</td></tr>';
				}else{
					$count = 0;
					while( ($r = ria_mysql_fetch_array($stores)) && $count<25 ){
						$count++;
						if( $have_mdl_filter || $have_fld_filter ){
							if( $rs = dlv_stores_get( $r['obj_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ) )
								$r = ria_mysql_fetch_array($rs);
							else
								$r = null;
						}
						if( $r!=null ){

							print '<tr>';
							if( $checkbox ){
								print '<td headers="select"><input type="checkbox" class="checkbox" name="str[]" value="'.$r['id'].'" /></td>';
							}
							print '<td headers="ref">' . $r['id'] . '</td>';
							if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_VIEW')){
								print '<td headers="name">'.view_str_is_sync($r).' <a href="edit.php?str='.$r['id'].'&amp;page='.$page.'">'.htmlspecialchars($r['name']).'</a></td>';
							}else{
								print '<td headers="name">'.htmlspecialchars($r['name']).'</td>';							
							}
							print '<td headers="address"><address>';
							if( $r['address1'] )
								print htmlspecialchars($r['address1']).'<br />';
							if( $r['address2'] )
								print htmlspecialchars($r['address2']).'<br />';
							if( $r['zipcode'] )
								print htmlspecialchars($r['zipcode']).' ';
							if( $r['city'] )
								print htmlspecialchars($r['city']).'';
							print '</address></td>';
							print '<td>';
							$contact = array();
							if( trim($r['manager']) )
								$contact[] = htmlspecialchars($r['manager']);
							if( trim($r['phone']) )
								$contact[] = htmlspecialchars($r['phone']);
							if( trim($r['fax']) )
								$contact[] = htmlspecialchars($r['fax']);
							if( trim($r['email']) )
								$contact[] = '<a href="mailto:'.htmlspecialchars($r['email']).'">'.htmlspecialchars($r['email']).'</a>';
							if( trim($r['website']) )
								$contact[] = '<a href="'.htmlspecialchars($r['website']).'" target="_blank">'.htmlspecialchars($r['website']).'</a>';
							print implode( '<br />', $contact );
							print '</td>';
							print '</tr>';
						}
					}
				}
			?>
		</tbody>
		<tfoot>
			<?php if( $pages>1 ){ ?>
			<tr id="pagination"><td colspan="<?php print $checkbox ? '3': '2'; ?>" class="align-left">
				<?php print _('Page ').$page.'/'.$pages; ?>
				</td><td colspan="2">
				<?php
					$links = array();
					if( $page>1 )
						$links[] = '<a onclick="return strSwitchTablePage( '.($page-1).', '.$pages.', '.( $have_mdl_filter ? $_GET['mdl'] : 0 ).', '.($have_fld_filter ? $_GET['fld'] : 0 ).', \'' . $_REQUEST['sort'] . '\', \'' . $_REQUEST['dir'] . '\' )" href="index.php?page='.($page-1).''.( $have_mdl_filter ? '&mdl='.$_GET['mdl'] : $have_fld_filter ? '&fld='.$_GET['fld'] : '' ).'&sort=' . $_REQUEST['sort'] . '&dir=' . $_REQUEST['dir'] . '">&laquo; ' . _("Page précédente") . '</a>';

					for( $p=$page_start; $p<=$page_stop; $p++ )
						if( $p==$page )
							$links[] = $p;
						else
							$links[] = '<a onclick="return strSwitchTablePage( '.$p.', '.$pages.', '.( $have_mdl_filter ? $_GET['mdl'] : 0 ).', '.($have_fld_filter ? $_GET['fld'] : 0 ).', \'' . $_REQUEST['sort'] . '\', \'' . $_REQUEST['dir'] . '\' )" href="index.php?page='.$p.''.( $have_mdl_filter ? '&mdl='.$_GET['mdl'] : $have_fld_filter ? '&fld='.$_GET['fld'] : '' ).'">'.$p.'</a>';

					if( $page<$pages )
						$links[] = '<a onclick="return strSwitchTablePage( '.($page+1).', '.$pages.', '.( $have_mdl_filter ? $_GET['mdl'] : 0 ).', '.($have_fld_filter ? $_GET['fld'] : 0 ).', \'' . $_REQUEST['sort'] . '\', \'' . $_REQUEST['dir'] . '\' )" href="index.php?page='.($page+1).''.( $have_mdl_filter ? '&mdl='.$_GET['mdl'] : $have_fld_filter ? '&fld='.$_GET['fld'] : '' ).'">' . _("Page suivante") . ' &raquo;</a>';

					print implode(' | ',$links);
				?>
			</td></tr>
			<?php } ?>
			<tr><td colspan="<?php print $colspan; ?>" class="align-left">
				<?php if( $stores_count ){
					if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_PUBLISH') ){ ?>
					<input type="submit" name="btn_publish" value="Publier" title="<?php echo _("Publier les magasins sélectionnés"); ?>" onclick="return window.confirm('Une fois publiés, les magasins sélectionnés seront visibles sur votre site.\nEtes vous sûr(e) de vouloir continuer ?');" />
					<input type="submit" name="unpublish" value="Dépublier" title="<?php echo _("Ne plus publier les magasins sélectionnés"); ?>" onclick="return window.confirm('Une fois retirés, les magasins sélectionnés ne seront plus visibles sur votre site.\nEtes vous sûr(e) de vouloir continuer ?');"  style="margin-right: 8px" />
					<?php }
					if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_DEL') ){ ?>
					<input type="submit" name="del" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les magasins sélectionnés"); ?>" onclick="return strConfirmDelList()" />
					<?php } ?>
				<input type="submit" name="export" class="btn-export" value="<?php echo _("Exporter"); ?>" title="<?php echo _("Exporter la liste courante"); ?>" />
				<?php }
				if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_ADD') ){ ?>
				<input type="submit" name="add" class="btn-add" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter un magasin"); ?>" onclick="return strAdd()" />
				<?php } ?>
			</td></tr>
		</tfoot>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>