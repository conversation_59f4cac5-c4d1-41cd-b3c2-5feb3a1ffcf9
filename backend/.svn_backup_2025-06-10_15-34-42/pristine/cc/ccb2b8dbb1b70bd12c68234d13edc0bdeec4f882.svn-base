<?php

	// Vérifie que l'utilisateur en cours à bien le droit d'accéder à cette page
	if( isset($_GET['qst']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_FAQ_EDIT');
	}elseif( !isset($_GET['qst']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_FAQ_ADD');
	}

	require_once('tools.faq.inc.php');

	unset($error);

	// Vérifie la validité du paramètre cat (catégorie de classement)
	if( !isset($_GET['cat']) || !faq_categories_exists($_GET['cat']) ){
		header('Location: index.php');
		return false;
    }
    
    // S'assure que la variable $_GET['qst'] est définie
    if( !isset($_GET['qst']) ){
        $_GET['qst'] = 0;
    }
        
	// Gestion des onglets
	if( !isset($_GET['tab']) )
		$_GET['tab'] = 'general';
	$tab = $_GET['tab'];
	if( isset($_POST['tabReferencement']) ){
		$tab = 'ref';
	}
	if( isset($_POST['tabImages']) ){
		$tab = 'images';
	}

	// Vérifie la validité du paramètre qst (identifiant de la question)
	if( isset($_GET['qst']) && $_GET['qst']!=0 && !faq_questions_exists($_GET['qst']) ){
		header('Location: category.php?cat='.$_GET['cat']);
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: category.php?cat='.$_GET['cat']);
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) ){
		if( !faq_questions_del($_GET['qst']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression de la question.\nVeuillez réessayer ou prendre contact avec l'administrateur");
		else{
			header('Location: category.php?cat='.$_GET['cat']);
			exit;
		}
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) ){
		if( !isset($_POST['publish']) ){
            $_POST['publish'] = false;
        }
		if( !isset($_POST['frequent']) ){
            $_POST['frequent'] = false;
        }

        // La catégorie, le nom ou la description n'ont pas été envoyés
		if( !isset($_POST['cat']) || !isset($_POST['name']) || !isset($_POST['desc']) ){
            $error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.\nVeuillez vérifier.");

        // Les paramètres ne sont pas au bon format
		}elseif( !is_numeric($_POST['cat']) || !trim($_POST['name']) || !trim($_POST['desc']) ){
            $error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.\nVeuillez vérifier. Les champs marqués d'une * sont obligatoires.");
            
        // Nouvelle question
        }elseif( $_GET['qst']==0 ){
			$qst = faq_questions_add( $_POST['cat'], $_POST['name'], $_POST['desc'], $_POST['publish'], $_POST['frequent'] );
			if( !$qst ){
				$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la question.\nVeuillez réessayer ou prendre contact avec l'administrateur");
			}else{
				header('Location: question.php?cat='.$_GET['cat'].'&qst='.$qst);
				exit;
            }
        // Mise à jour de la question
		}else{
			if( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
				if( !faq_questions_update($_GET['qst'],$_POST['cat'],$_POST['name'],$_POST['desc'],$_POST['publish'], $_POST['frequent']) ){
                    $error = _("Une erreur inattendue s'est produite lors de l'enregistrement de la question.\nVeuillez réessayer ou prendre contact avec l'administrateur");
                }
			}elseif( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ){
				$values = array(
					_FLD_FAQ_QST_NAME=>$_POST['name'],
					_FLD_FAQ_QST_DESC=>$_POST['desc']
				);

				if( !fld_translates_add($_GET['qst'], $_GET['lng'], $values) ){
                    $error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
                }
			}
		}
    }
	
	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();
	
	// Bouton Enregistrer Référencement
	view_admin_tab_referencement_actions( CLS_FAQ_QST, $_GET['qst'], $lng );

	define('ADMIN_PAGE_TITLE', _('Question').' - '._('Foire Aux Questions').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');

	$qst = array( 'id'=>0, 'cat_id'=>$_GET['cat'], 'name'=>'', 'desc'=>'', 'publish'=>false, 'frequent'=>false );
	if( isset($_GET['name']) )
		$qst['name'] = ucfirst(trim($_GET['name']));
	if( isset($_GET['qst']) && $_GET['qst']>0 ){
		$qst = ria_mysql_fetch_array(faq_questions_get($_GET['qst']));
		// Récupère les informations traduite
		if( $lng!=$config['i18n_lng'] ){
			$tsk_qst = fld_translates_get( CLS_FAQ_CAT, $qst['id'], $lng, $qst, array(_FLD_FAQ_QST_NAME=>'name', _FLD_FAQ_QST_DESC=>'desc' ), true );
			$qst['name'] = $tsk_qst['name'];
			$qst['desc'] = $tsk_qst['desc'];
		}
	}

	$title_quest = _('Foire Aux Questions');
	if( isset($qst['id']) && $qst['id']>0 ) {
		$title_quest = _('Question :').' '.$qst['name'];
	}elseif( $qst['id']==0 ){
		$title_quest = _('Nouvelle question').( isset($qst['name']) && trim($qst['name'])!='' ? _(' :').' '.$qst['name'] : '' );
	}
?>
	<h2><?php print htmlspecialchars($title_quest); ?></h2>

	<?php
		if (isset($_SESSION['referencement_edit_success'])) {
			$success = $_SESSION['referencement_edit_success'];
			unset($_SESSION['referencement_edit_success']);
		}
		if (isset($_SESSION['referencement_edit_error'])) {
			$error = $_SESSION['referencement_edit_error'];
			unset($_SESSION['referencement_edit_error']);
		}

		if( isset($error) )
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';

	
		if( isset($success) ){
			print '<div class="success">'.nl2br(htmlspecialchars($success)).'</div>';
		}

    	// Affiche le menu de langue
		if( isset($_GET['qst']) && $_GET['qst']>0 )
			print view_translate_menu( 'question.php?qst='.$qst['id'].'&cat='.$qst['cat_id'], $lng );
	?>

	<form action="question.php?qst=<?php print $qst['id']; ?>&amp;cat=<?php print $qst['cat_id']; ?>&amp;lng=<?php print $lng; ?>" method="post" onsubmit="return validFaqQuestionForm(this)">
		<ul class="tabstrip">
			<li><input type="submit" name="tabGeneral" value="<?php print _('Général'); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
			<?php if( tnt_tenants_have_websites() ){ ?>
			<li><input type="submit" name="tabReferencement" value="<?php print _('Référencement'); ?>" <?php if( $tab=='ref' ) print 'class="selected"'; ?> /></li>
			<?php } ?>
			<li><input type="submit" name="tabImages" value="<?php print _('Images'); ?>" <?php if( $tab=='images' ) print 'class="selected"'; ?> /></li>
		</ul>
	<div id="tabpanel">
		<?php if( $tab=='general' ){ ?>
		<table width="100%">
			<caption><?php print _('Fiche question'); ?></caption>
			<tbody>
				<tr>
					<td><label for="qstid"><?php print _('Identifiant :'); ?></label></td>
					<td><input type="text" name="qstid" id="qstid" maxlength="75" value="<?php print htmlspecialchars($qst['id']); ?>" disabled="disabled" /></td>
				</tr>
				<tr>
					<td class="col200px"><label for="category"><span class="mandatory">*</span> <?php print _('Catégorie :'); ?></label></td>
					<td>
						<select name="cat" id="category">
							<?php
								$categories = faq_categories_get();
								while( $r = ria_mysql_fetch_array($categories) )
									print '<option value="'.$r['id'].'" '.($qst['cat_id']==$r['id']?'selected="selected"':'').'>'.htmlspecialchars($r['name']).'</option>';
							?>
						</select>
					</td>
				</tr>
				<tr>
					<td><label for="name"><span class="mandatory">*</span> <?php print _('Libellé de la question :'); ?></label></td>
					<td><input type="text" name="name" id="name" maxlength="255" value="<?php print htmlspecialchars($qst['name']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="desc"><span class="mandatory">*</span> <?php print _('Réponse :'); ?></label></td>
					<td><textarea class="tinymce" name="desc" id="desc" rows="15" cols="40"><?php print view_site_format_riawysiwyg($qst['desc'],false,true,false, false); ?></textarea></td>
				</tr>
				<tr>
					<td>&nbsp;</td>
					<td><input type="checkbox" class="checkbox" name="publish" id="publish" <?php if( $qst['publish'] ) print 'checked="checked"'; ?> /> <label for="publish"><?php print _('Publier cette question dans la <abbr title="Foire Aux Questions">FAQ</abbr> de la boutique'); ?></label></td>
				</tr>
				<tr>
					<td>&nbsp;</td>
					<td><input type="checkbox" class="checkbox" name="frequent" id="frequent" <?php if( $qst['frequent'] ) print 'checked="checked"'; ?> /> <label for="frequent"><?php print _('Fait parti des questions les plus fréquemment posées'); ?></label></td>
				</tr>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="2">
						<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
						<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" onclick="return cancelFaqQuestion(this.form)" />
						<?php if( $qst['id']>0 && gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_DEL') ){ ?>
						<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" onclick="return confirmFaqQuestionDel()" />
						<?php } ?>
					</td>
				</tr>
			</tfoot>
		</table>
		<?php }elseif( $tab=='ref' ){ 
			print view_admin_tab_referencement(CLS_FAQ_QST, $_GET['qst'], $lng);
		?>			
		<?php } elseif( $tab=='images' ) {
			view_admin_img_table(CLS_FAQ_QST, $qst['id']);
		}
		?>
	</div>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>