<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/error_details.proto

namespace Google\Rpc;

if (false) {
    /**
     * This class is deprecated. Use Google\Rpc\PreconditionFailure\Violation instead.
     * @deprecated
     */
    class PreconditionFailure_Violation {}
}
class_exists(PreconditionFailure\Violation::class);
@trigger_error('Google\Rpc\PreconditionFailure_Violation is deprecated and will be removed in the next major release. Use Google\Rpc\PreconditionFailure\Violation instead', E_USER_DEPRECATED);

