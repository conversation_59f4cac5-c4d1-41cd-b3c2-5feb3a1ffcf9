<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: example.proto

namespace Google\ApiCore\Tests\Unit;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>google.apicore.tests.unit.MyMessage</code>
 */
class MyMessage extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\ApiCore\Tests\Unit\Example::initOnce();
        parent::__construct($data);
    }

}

