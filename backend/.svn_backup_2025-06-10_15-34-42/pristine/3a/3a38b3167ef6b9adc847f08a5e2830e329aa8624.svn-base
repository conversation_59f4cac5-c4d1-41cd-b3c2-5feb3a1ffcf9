<?php

namespace Basho\Riak\Command\Builder\TimeSeries;

use Basho\Riak;
use Basho\Riak\Command;

/**
 * <AUTHOR> <cmancini at basho d0t com>
 */
class DeleteRow extends Command\Builder implements Command\BuilderInterface
{
    use TableTrait;
    use KeyTrait;

    /**
     * {@inheritdoc}
     *
     * @return Command\TimeSeries\Store
     */
    public function build()
    {
        $this->validate();

        return new Command\TimeSeries\Delete($this);
    }

    /**
     * {@inheritdoc}
     */
    public function validate()
    {
        $this->required('Key');
        $this->required('Table');
    }
}
