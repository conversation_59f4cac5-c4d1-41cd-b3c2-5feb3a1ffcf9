<?php


function func1()
{

}//end func1()


function func1() {

}//end func1()


class MyClass
{


    private function func1()
    {

    }//end func1()


    public function func1() {

    }//end func1()


}//end class


abstract class MyClass
{


    abstract function func1();


    public function func1() {

    }//end func1()


}//end class


interface MyInterface
{


    function func1();


    function func2() ;


}//end interface

function recurseCreateTree(
    array $flatList,
    $markup,
    $lastOffset=0
) {
}


function test ( ){}

function parent() {}
function self() {}
function false() {}
function true() {}
function null() {}
function fn() {}
