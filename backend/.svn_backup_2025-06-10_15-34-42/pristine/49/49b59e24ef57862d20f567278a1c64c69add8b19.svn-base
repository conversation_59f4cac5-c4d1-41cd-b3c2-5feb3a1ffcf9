# ExclusionFilterOperator

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | [**\Swagger\Client\Model\ExclusionFilterOperatorName**](ExclusionFilterOperatorName.md) |  | [optional] 
**expected_channel_column_data_type** | [**\Swagger\Client\Model\ExclusionFilterOperatorDataType**](ExclusionFilterOperatorDataType.md) |  | [optional] 
**value_required** | **bool** | This operator requires a value | [optional] 
**expected_value_data_type** | [**\Swagger\Client\Model\ExclusionFilterOperatorDataType**](ExclusionFilterOperatorDataType.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


