<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  57300 => 'Tigo',
  57301 => 'Tigo',
  57302 => 'Tigo',
  57303 => 'Uff!',
  57304 => 'Une',
  57305 => 'Movil Exito',
  57310 => 'Claro',
  57311 => 'Claro',
  57312 => 'Claro',
  57313 => 'Claro',
  57314 => 'Claro',
  57315 => 'Movistar',
  57316 => 'Movistar',
  57317 => 'Movistar',
  57318 => 'Movistar',
  57319 => 'Virgin Mobile',
  57320 => 'Claro',
  57321 => 'Claro',
  57322 => 'Claro',
  57323 => 'Claro',
  57350 => 'Avantel',
  57351 => 'Avantel',
);
