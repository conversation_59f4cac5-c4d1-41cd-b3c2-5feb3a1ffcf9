<?php

	/** \file ord-update-totals.php
	 *	Ce fichier est chargé de rechercher et d'actualiser les commandes dont le total HT ou TTC serait faux.
	 *	Il affiche en sortie le nombre de commandes erronées. 
	 *	Il n'est lancé par aucune crontab.
	 */
	 
	 set_include_path(dirname(__FILE__) . '/../include/');
	require_once( 'websites.inc.php' );
	require_once( 'orders.inc.php' );
	require_once( 'cfg.variables.inc.php' );
	
	// Récupère toutes les commandes contenues dans la base de données. (On ne tient pas compte du tenant)
	$rord = ria_mysql_query('
		select ord_tnt_id as tnt_id, ord_id as id
		from ord_orders, ord_states
		where ord_state_id=state_id 
			and ord_state_id and ord_state_id not in (13, 26)
			and ord_masked=0 
		order by ord_date desc
	');

	$config = array();
	
	// On met à jour le montant total HT et TTC de chaque commande.
	$ord_affect = 0;
	print 'Vous trouverez ci-dessous les commandes dont leur montant HT ou TTC faux : '."\n";
	print '--------------------------------------------------------------'."\n";
	while( $ord = ria_mysql_fetch_array($rord) ){
		// Récupère l'identifiant du tenant
		$config['tnt_id'] = $ord['tnt_id'];
		
		// Récupère l'identifiant de son site (utilisé pour importer les variables de config)
		$wst = ria_mysql_fetch_array(wst_websites_get());
		$config['wst_id'] = $wst['id'];
		
		// Importe les variables de config
		cfg_variables_load($config);

		// Met à jour les montants totals
		$affected = ord_orders_update_totals($ord['id']);

		
		if( $affected>0 ){
			print 'La commande n° '.$ord['id'].', pour le locataire '.$ord['tnt_id']."\n";
			$ord_affect++;
		}

		// Vide le contenu du tableau
		unset($config);
	}
	print 'Il y avait au total '.($ord_affect>1 ? $ord_affect.' commandes concernées' : $ord_affect. ' commande concernée' ).' par un total HT ou TTC de faux.';


