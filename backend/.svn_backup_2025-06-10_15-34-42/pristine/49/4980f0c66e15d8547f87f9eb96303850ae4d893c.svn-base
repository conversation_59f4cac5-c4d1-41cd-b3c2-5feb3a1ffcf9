--TEST--
phpunit BankAccountTest ../_files/BankAccountTest.php
--FILE--
<?php
$_SERVER['argv'][1] = '--no-configuration';
$_SERVER['argv'][2] = '--my-option=123';
$_SERVER['argv'][3] = '--my-other-option';
$_SERVER['argv'][4] = 'BankAccountTest';
$_SERVER['argv'][5] = __DIR__ . '/../_files/BankAccountTest.php';

require __DIR__ . '/../bootstrap.php';
require __DIR__ . '/../_files/MyCommand.php';
MyCommand::main();
?>
--EXPECTF--
MyCommand::myHandler 123
PHPUnit %s by <PERSON> and contributors.

...                                                                 3 / 3 (100%)

Time: %s, Memory: %s

OK (3 tests, 3 assertions)
