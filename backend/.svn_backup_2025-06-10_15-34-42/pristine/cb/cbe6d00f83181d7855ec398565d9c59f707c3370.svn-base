# ChannelCatalogListLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**self** | [**\Swagger\Client\Model\LinksGetChannelCatalogsLink**](LinksGetChannelCatalogsLink.md) |  | [optional] 
**beez_up_columns** | [**\Swagger\Client\Model\ExternalLinksCatalogGetBeezUPColumnsLink**](ExternalLinksCatalogGetBeezUPColumnsLink.md) |  | [optional] 
**add** | [**\Swagger\Client\Model\LinksAddChannelCatalogLink**](LinksAddChannelCatalogLink.md) |  | [optional] 
**exclusion_filter_operators** | [**\Swagger\Client\Model\LinksGetChannelCatalogExclusionFilterOperatorsLink**](LinksGetChannelCatalogExclusionFilterOperatorsLink.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


