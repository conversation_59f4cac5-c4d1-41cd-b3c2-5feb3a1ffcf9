<?php
	/** \file ncmd-rights.php
	 * 	Ce fichier de visualiser et de gérer les accès à un panier modèle
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_MODEL');

	unset($error);

	$refresh = $error = false;

	$types = array( 'prc' => _('Catégorie tarifaire'),
					'prf' => _('Profil / droit d\'accès') ,
					'cac' => _('Catégorie comptable') ,
					'cnt' => _('Pays d\'origine du client') ,
					'usr' => _('Compte client')  );
					
	if( !isset( $_SESSION['admin_ord_id'] ) ){
		print _('Des paramètres sont manquants.');
		exit;
	}
	
	$order = false;
	$rorder = ord_orders_get( 0, $_SESSION['admin_ord_id'] ); 
	if( $rorder && ria_mysql_num_rows( $rorder ) ){
		$order = ria_mysql_fetch_array( $rorder );
	}

	if (isset($_GET['choose-usr'])) {
		if (!ord_model_users_exists($order['id'], $_GET['choose-usr'], true)) {
			if( !ord_models_set_users($order['id'], $_GET['choose-usr'], 'usr', true) ){
				$error = _('Une erreur est survenue lors de l\'enregistrement de l\'autorisation.');
			}
		}

		$refresh = true;
	}
	
	// pour la gestion des droits 
	if( IS_AJAX && isset($_GET['act']) ){
		$html = '';
		switch( $_GET['act'] ){
			case 'rights_type' : 
				if( isset( $_GET['type'] ) ){
					if(is_numeric($_GET['type'])){
						$fields = fld_fields_get($_GET['type']);
						$field = ria_mysql_fetch_assoc($fields);
						switch ($field['type_id']) {
							case 5:
							case 6:
							case 12:
								$selectClass = '';
								// little_input sur le type de champs avancé Fonction du contact
								$field['type_id'] === '6' ? $selectClass = 'class="little_input"':''; 
								$html .= '<!-- Suivant le type du champ sélectionné -->
											<select name="new_fld" '. $selectClass .'><!-- type 5, 6, 12 (liste de choix) -->';
											if( $rval = fld_restricted_values_get( 0, $field['id'], '', -1 ) ){
												while( $v = ria_mysql_fetch_array($rval) ){
													$select_value = htmlspecialchars($v['name']);
													if ($field['type_id'] == FLD_TYPE_SELECT_HIERARCHY) {
														$select_value = $v['id'];
													}

													$html .= '<option '.(isset( $ru['value'] ) && $select_value == $ru['value'] ? 'selected="selected"':'').' id="'.$v['id'].'" value="'.$select_value.'">'.htmlspecialchars($v['name']).'</option>';
													if( $rchild = fld_restricted_values_get( 0, false, '', $v['id'] ) ){
														while( $child = ria_mysql_fetch_array($rchild) ){
															$select_value = htmlspecialchars($child['name']);
															if ($field['type_id'] == FLD_TYPE_SELECT_HIERARCHY) {
																$select_value = $child['id'];
															}

															$html .= '<option '.(isset( $ru['value'] ) && $select_value == $ru['value']  ? 'selected="selected"':'').' id="'.$child['id'].'" value="'. $select_value .'">'.htmlspecialchars($v['name'].' >> '.$child['name']).'</option>';
															if( $rsschild = fld_restricted_values_get( 0, false, '', $child['id'] ) ){
																while( $sschild = ria_mysql_fetch_array($rsschild) ){
																	$select_value = htmlspecialchars($sschild['name']);
																	if ($field['type_id'] == FLD_TYPE_SELECT_HIERARCHY) {
																		$select_value = $sschild['id'];
																	}

																	$html .= '<option '.(isset( $ru['value'] ) && $select_value == $ru['value']  ? 'selected="selected"':'').' id="'.$sschild['id'].'" value="'.$select_value.'">'.htmlspecialchars($v['name'].' >> '.$child['name'].' >> '.$sschild['name']).'</option>';
																	// on stope à 3 niveaux
																}
															}
														}
													}
												}
											}
								$html .= '	</select>';
								break;
							case 11:
								// implémenter une popup en fonction du champ (fait référence aux produits, aux catégories, etc...)

								$cls = '';
								switch($related_class){
									case CLS_PRODUCT: {
										$cls = 'add-rule-prd';
										break;
									}
									case CLS_CATEGORY: {
										$cls = 'add-rule-cat';
										break;
									}
									case CLS_BRAND: {
										$cls = 'add-rule-brd';
										break;
									}
								}

								$html .= '
									<input type="hidden"  name="rec-new-value" value="'.(isset( $ru['value'] ) ? $ru['value'] : '' ).'" />
									<input type="text" data-input="rec-new-value" class="'.$cls.'" id="rec-new-value" maxlength="16" name="name-rec-new-value" value="'.(isset( $ru['value'] ) ? view_formatted_value( $ru ) : '' ).'"/>
									<input type="button" data-input="rec-new-value" class="'.$cls.'"  name="cls-ref-select" class="button" value="Choisir" />
									';
								
								break;
							case 10: 
								$html .= '<input type="text" class="datepicker" name="new_fld" value="'.(isset($ru['value']) ? dateheureunparse( $ru['value'], false ) : '' ).'"><!-- type 10 (date) -->';
								break;
							case 8: 
								$html .= '<label><input type="radio" name="new_fld" value="1" '.(isset($ru['value']) && $ru['value']==1 ? 'checked="checked"' : '' ).'/> Oui</label>&nbsp;';
								$html .= '<label><input type="radio" name="new_fld" value="0" '.(isset($ru['value']) && $ru['value']==0 ? 'checked="checked"' : '' ).'/> Non</label>';
								break;
							default:
								$html .= '<input type="text" name="new_fld" value="'.(isset($ru['value']) ? $ru['value'] : '' ).'"/><!-- tous les autres types -->';
						}
						
					}else{
						switch( $_GET['type'] ){
							case 'prc':
								$html .= '<select name="new_prc" class="little_input">';
								$rlist = prd_prices_categories_get();
								if( $rlist ) {
									while( $list = ria_mysql_fetch_array( $rlist ) ){

										$html .= '<option value="'.$list['id'].'">'.htmlspecialchars($list['name']).'</option>';
									}
								}
								$html .= '</select>';
								break;
							case 'prf':
								$html .= '<select name="new_prf" class="little_input">';
								$rlist = gu_profiles_get();
								if( $rlist ) {
									while( $list = ria_mysql_fetch_array( $rlist ) ){
										$html .= '<option value="'.$list['id'].'">'.htmlspecialchars($list['name']).'</option>';
									}
								}
								$html .= '</select>';
								break;
							case 'cac':
								$html .= '<select name="new_cac" class="little_input">';
								$rlist = gu_accounting_categories_get();
								if( $rlist ) {
									while( $list = ria_mysql_fetch_array( $rlist ) ){
										$html .= '<option value="'.$list['id'].'">'.htmlspecialchars($list['name']).'</option>';
									}
								}
								$html .= '</select>';
								break;
							case 'cnt':
								$html .= '<select name="new_cnt" class="little_input">';
								$rlist = sys_countries_get();
								if( $rlist ) {
									while( $list = ria_mysql_fetch_array( $rlist ) ){
										$html .= '<option value="'.$list['code'].'">'.htmlspecialchars($list['name']).'</option>';
									}
								}
								$html .= '</select>';
								break;
							case 'usr':
								$html .= '<input type="text" name="new_usr" value="" class="little_input"/>';
								$html .= '<input type="button" name="choose_new_usr" value="'._('Choisir').'" class="little_input"/>';
								break;
						}
					}
				}
				break;
		}	
		print $html;
		exit;
	}

	// Si l'utilisateur n'a pas le droit de créer ou de modifier un panier modèle, on bloque ce qui suit
	if( !gu_user_is_authorized('_RGH_ADMIN_ORDER_MODEL_ADD') && !gu_user_is_authorized('_RGH_ADMIN_ORDER_MODEL_EDIT') ){
		exit;
	}

	// Enregistrement des droits 
	if( isset( $_POST['save-rights'] ) && $order ){
		if( (isset( $_POST['new_type'], $_POST['new_'.$_POST['new_type']] ) && (in_array( $_POST['new_type'], array('cnt','usr','prf','cac','prc')))) || (is_numeric($_POST['new_type']) && isset($_POST['new_fld'])) ){
			if(is_numeric($_POST['new_type'])){
				$fields = fld_fields_get($_POST['new_type']);
				$field = ria_mysql_fetch_assoc($fields);
				switch ($field['type_id']) {
					case 3:
						if(!is_int($_POST['new_fld'])) {$error = "Erreur, la valeur doit être un entier";}
						break;
					case 4:
						if(!is_numeric($_POST['new_fld'])) {$error = "Erreur, la valeur doit être un décimal";}
						break;
					case 10:
						if(!isdate($_POST['new_fld'])) {$error = "Erreur, la valeur doit être un décimal";}
						break;
				}
				if(!isset($error) || $error == ""){
					if( !ord_models_set_users( $order['id'], $_POST['new_fld'], $_POST['new_type'], $_POST['new_allowed'] ? true : false )  ){
						$error = _('Une erreur est survenue lors de l\'enregistrement de l\'autorisation.');
					}
				}
			}else{
			
				if( !ord_models_set_users( $order['id'], $_POST['new_'.$_POST['new_type']], $_POST['new_type'], $_POST['new_allowed'] ? true : false ) ){
					$error = _('Une erreur est survenue lors de l\'enregistrement de l\'autorisation.');
				}
			}
		}

		ord_models_set_all_users( $order['id'],  isset( $_POST['allow-to-all'] ) && $_POST['allow-to-all'] ? true : false  );
			
		$refresh = true;
	}
	
	// Suppression de droit 
	if( isset( $_POST['del-rights'], $_POST['omu_del'] ) && $order  && is_array( $_POST['omu_del'] ) ){
		foreach( $_POST['omu_del'] as $del ){			
			if( !ord_models_users_del( $del ) )
				$error = _('Une erreur est survenue lors de la suppression de l\'autorisation.');
		}
		
		if( !$error ){
			$refresh = true;
		}
	}
	
	
	if( $refresh ){
		$_POST['new_type'] = false;
	}
	
	$rrights = ord_models_users_get( $order['id'] );
	
	define('ADMIN_PAGE_TITLE', _('Ajout par lot'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($error) && $error ){
		print '<div class="error">'.htmlspecialchars($error).'</div>';
	}
?>
	<form action="ncmd-rights.php" method="post">
		<table class="ncmd_model_rights checklist large">
			<caption><?php print _('Autorisations'); ?></caption>
			<tbody>
				<?php
					$tmp_html = '';
					$all = false;
					if( $rrights && ria_mysql_num_rows($rrights) > 0) {
						while( $rights = ria_mysql_fetch_array( $rrights ) ){
							if(  $rights['type'] == 'all' ){
								$all = $rights['is_allowed'] ? true : false;
								continue;
							}
							if($rights['type'] == "fld"){
								$rfld = fld_fields_get( $rights['object'] );
									if( $rfld ){
										$fld = ria_mysql_fetch_array( $rfld );
										$libelle_type = $fld['name'];
									}else{
										$libelle_type =$rights['object'];
									}
								
							}else{
								$libelle_type = $types[ $rights['type'] ];
							}
							$tmp_html .= '<tr>';
							$tmp_html .= '		<td><input type="checkbox" name="omu_del[]" value="'.$rights['id'].'"/></td>';
							$tmp_html .= '		<td data-label="'._('Type :').' ">'.htmlspecialchars($libelle_type).'</td>';
							$tmp_html .= '		<td data-label="'._('Valeur :').' ">';
							switch( $rights['type'] ) {
								case 'usr' : 
									$r_usr = gu_users_get($rights['object']);
								if ($r_usr && ria_mysql_num_rows($r_usr)) {
									$usr = ria_mysql_fetch_assoc( $r_usr );
									
									$usr_name = " ".$usr['ref'];
									if(trim($usr['adr_firstname']) != "" || trim($usr['adr_lastname']) != ""){
										$usr_name .= " - ".trim($usr['adr_firstname'].' '.$usr['adr_lastname']);
									}
									

									if(trim($usr['society']) != ""){
										$usr_name .= " - ".$usr['society'];
									}
									if(trim($usr['email']) != ""){
										$usr_name .= "<br/> &lt".$usr['email']."&gt";
									}

									$tmp_html .= view_usr_is_sync( $usr );

									$tmp_html .= htmlspecialchars($usr_name);
								}
								break;
								case 'prf' : 
									$rprf = gu_profiles_get( $rights['object'] );
									if( $rprf ){
										$prf = ria_mysql_fetch_array( $rprf );
										$tmp_html .= htmlspecialchars($prf['name']);
									}
									break; 
								case 'cnt' : 
									$cnt_name = sys_countries_get_name( $rights['object'] );
									if( $cnt_name ){
										$tmp_html .= htmlspecialchars($cnt_name);
									}
									break; 
								case 'cac' : 
									$rcac = gu_accounting_categories_get( $rights['object'] );
									if( $rcac ){
										$cac = ria_mysql_fetch_array( $rcac );
										$tmp_html .= htmlspecialchars($cac['name']);
									}
									break; 
								case 'prc' : 
									$rprc = prd_prices_categories_get( $rights['object'] );
									if( $rprc ){
										$prc = ria_mysql_fetch_array( $rprc );
										$tmp_html .= htmlspecialchars($prc['name']);
									}
								case 'fld' : 
									$fields = fld_fields_get($rights['object']);
									$field = ria_mysql_fetch_assoc($fields);
									switch($field['type_id']) {
										case FLD_TYPE_BOOLEAN_YES_NO:{
											if($rights['fld_value'] == "1"){
												$tmp_html .= _('Oui');
											}else{
												$tmp_html .= _('Non');
											}
											break;
										}
										case FLD_TYPE_SELECT_HIERARCHY:
											$val = fld_restricted_values_get_name($rights['fld_value']);
											if (trim($val) == '') {
												$val = 'nc.';
											}
											$tmp_html .= htmlspecialchars($val);
											break;
										default: {
											$tmp_html .= htmlspecialchars($rights['fld_value']);
											break;
										}
											
									}
									break; 
							}
							$tmp_html .= '		</td>';
							$tmp_html .= '		<td data-label="'._('Autorisation :').' ">'.($rights['is_allowed'] ? _('Oui') : _('Non')).'</td>';
							$tmp_html .= '</tr>';
						}
					}
					else{
						$tmp_html .= '<tr><td colspan="4">'._('Aucune autorisation').'</td></tr>';
					}
					
				?>
				
				<tr>
					<td colspan="4" class="allow-to-all">
						<input type="checkbox" class="lock" name="allow-to-all" id="allow-to-all" value="1" <?php print $all ? 'checked="checked"':'';?> />
						<label for="allow-to-all"><?php print _('Tous les comptes clients, à l’exception des comptes ci-dessous :'); ?></label>
					</td>
				</tr>
				<tr>
					<th class="col-check" data-label="<?php print _('Tout cocher :'); ?> "><input type="checkbox" onclick="checkAllClick(this)" class="checkbox" /></th>
					<th class="thead-none"><?php print _('Type'); ?></th>
					<th class="thead-none"><?php print _('Valeur'); ?></th>
					<th class="thead-none"><?php print _('Autorisation'); ?></th>
				</tr>
					
				<?php print $tmp_html; ?>
				
				<tr class="newline <?php print isset( $_POST['new_type'] ) && $_POST['new_type'] != '' ? 'open' : '' ?>">
					<td></td>
					<td class="col_first" data-label="<?php print _('Type :'); ?> ">
						<select name="new_type" class="rights_type little_input">
								<option></option>
							<?php foreach( $types as $key => $type ){ ?>
								<option value="<?php print $key; ?>"><?php print $type; ?></option>
							<?php } ?>
							<optgroup label="Champs avancés">
							<?php
							$fields = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), true, array(), null, CLS_USER);
							while( $field = ria_mysql_fetch_assoc($fields)){
								?>
								<option value="<?php print $field['id']; ?>"><?php print htmlspecialchars($field['name']); ?></option>
							<?php
							}
							?>
							</optgroup>
						</select>
					</td>
					<td class="col_second" data-label="<?php print _('Autorisation :'); ?> ">
					</td>
					<td data-label="<?php print _('Disponible :'); ?> ">
						<label><input type="radio" name="new_allowed" value="1" checked="checked" /> <?php print _('Oui'); ?></label>
						<label><input type="radio" name="new_allowed" value="0" /> <?php print _('Non'); ?></label>
					</td>
				</tr>
			</tbody>

			<tfoot>
				<tr>
					<td colspan="2" class="tdleft">
						<?php if( $rrights && ria_mysql_num_rows( $rrights ) ){ ?>
							<input type="submit" onclick="return confirm('<?php print _('Souhaitez vous vraiments supprimer les éléments cochés ?'); ?>');" name="del-rights" value="<?php print _('Supprimer'); ?>"/>
						<?php } ?>
					</td>
					<td colspan="2">
						<input type="submit" name="add-rights" value="<?php print _('Ajouter une condition'); ?>" />
						<input type="submit" name="save-rights" value="<?php print _('Enregistrer'); ?>" />
					</td>
				</tr>
			</tfoot>
		</table>
	</form>

	<div class="pop-form-cancel">
		<input class="btn-action cancel" type="submit" onclick="window.parent.hidePopup();" value="<?php print _('Retour'); ?>"/>
	</div>
	<script>
		$('document').ready(function(){
			<?php if( $refresh ){ ?>
				window.parent.parent_refresh();
			<?php } ?>
			
			$('.rights_type').live('change',function(){
				element = this; 
				$.get( 'ncmd-rights.php?act=rights_type&type='+$(this).val(),function(html){
					$(element).parents('tr').find('.col_second').html(html);
				});
			}).change();	

			$('.newline:not(.open)').hide();
			$('input[name=save-rights]').hide();
			$('input[name=add-rights]').live('click', function(){
				$('input[name=save-rights]').show();
				$(this).hide(); 
				$('.newline').show();
				return false;
			});
			
			$('input[name=allow-to-all]').live('change', function(){
				$('input[name=save-rights]').show();
			});			
		});
	</script>
<?php
	require_once('admin/skin/footer.inc.php');