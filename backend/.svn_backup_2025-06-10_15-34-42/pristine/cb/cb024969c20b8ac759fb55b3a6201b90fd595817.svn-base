<?php
	/** \defgroup registergcp RegisterGCP
	 *  \ingroup system
	 *	Ce module permet détablir la connexion MySQL en fonction du registre des tenants "db_tenants".
	 *
	 *	Exemple : Requête
	 *	\code{.php}
	 *		$result = RegisterGCP::create()->query('select * from [filter] [where]);
	 *	\endcode
	 *
	 *	@{
	 */

	use Google\Cloud\Datastore\DatastoreClient;

	require_once('define.inc.php');
	require_once('ria.mysql.inc.php');
	require_once('strings.inc.php');

	/**	\brief Cette classe permet de récupérer les informations de connexion à la base de données à partir du registre des tenants "db_tenants"
	 * 	stocké dans Google DataStore
	 */
	class RegisterGCP {
		const _projectID = 'riashop-186610'; ///< Identifiant du projet Google

		/** Cette fonction permet de récupérer la liste des tenants selon certains paramètres
		 * 	@param string $mysql_server Optionnel, nom du serveur MySQL sur lequel filtrer le résultat
		 *	@return array Un tableau contenant les identifiant des tenants
		 */
		public function getTenantIDs($mysql_server='') {
			global $memcached;

			// Récupère l'information depuis le cache
			$key_memcached = 'registergcp:getTenantIDs:'.$mysql_server;
			if( $get = $memcached->get($key_memcached) ){
				return $get;
			}

			$datastore = new DatastoreClient();

			$query = $datastore->query()
			    ->kind('db_tenants');

			if (trim($mysql_server) != '') {
				$query->filter('mysql_server', '=', $mysql_server);
			}

			$result = $datastore->runQuery($query);
			if( $result === null ){
				return false;
			}

			$ar_tnt_ids = array();
			foreach( $result as $res ){
				if( $res->getProperty('deleted') ){
					continue;
				}
				$ar_tnt_ids[] = $res->getProperty('tenant_id');
			}

			// Mise en cache du résultat pendant 5 minutes
			$memcached->set( $key_memcached, $ar_tnt_ids, 60 * 60 * 2 );

			return $ar_tnt_ids;
		}

		/** Cette fonction permet de charger un tableau de toutes les connexions possibles
		 * 	@param int $tnt_id Optionnel, par de filtrer le résultat et de ne retourner que la connection d'un tenant
		 * 	@return array Un tableau contenant pour chaque connexion :
		 * 				- mysql_server : serveur MySQL
		 * 				- mysql_base : base de données
		 * 				- mysql_user : compte utilisateur MySQL
		 * 				- mysql_password : mot de passe de connexion
		 */
		public function getConnections($tnt_id=0) {
			if (!is_numeric($tnt_id) || $tnt_id < 0) {
				return array();
			}

			global $memcached;

			// Récupère les informations depuis le cache
			$key_memcached = 'registergcp:getConnections:'.$tnt_id;
			if( $get = $memcached->get($key_memcached) ){
				return $get;
			}

			$datastore = new DatastoreClient();
			$query = $datastore->query()
					->kind('db_tenants');

			if( $tnt_id > 0 ){
				$query->filter('tenant_id', '=', (int) $tnt_id);
			}

			$result = $datastore->runQuery($query);
			if( $result === null ){
				return array();
			}

			$connections = array();
			foreach( $result as $res ){
				if( $res->getProperty('deleted') ){
					continue;
				}

				$connections[md5($res->getProperty('mysql_server').$res->getProperty('mysql_base'))] = array(
					'mysql_server' 		=> $res->getProperty('mysql_server'),
					'mysql_base' 		=> $res->getProperty('mysql_base'),
					'mysql_user'		=> $res->getProperty('mysql_user'),
					'mysql_password' 	=> $res->getProperty('mysql_password'),
				);
			}

			// Mise en cache des informations pendant 5 minutes
			$memcached->set( $key_memcached, $connections, 60 * 60 * 2 );

			return $connections;
		}

		/** Cette fonction permet d'établir la connexion
		 * 	@return object Retourne l'objet courant.
		 */
		public static function create() {
			$datastore = new RegisterGCP();
			return $datastore;
		}

		/** Cette fonction permet de savoir si l'on se trouve sur la GCP ou non.
		 * 	@return bool True si l'on est sur la GCP, False dans le cas contraire
		 */
		public static function onGcloud() {
			return isset($_SERVER['GCLOUD']) && $_SERVER['GCLOUD'] === '1';
		}

		/** Cette fonction permet de créer un nouveau locataire dans le registre, elle va automatiquement la génération de son numéro de tenant
		 *  @param string $society Obligatoire, Nom de la société
		 *  @param string $package Obligatoire, forfait choisi (par défaut 'business')
		 * 	@param array $data Obligatoire, données utilisées lors de la création du locataire
		 * 					- testing : période de test de Yuto (en jours)
		 *					- admin_email : adresse mail du compte administrateur
		 *	@param bool $sub Optionnel, si la gestion de l'abonnement est activé
		 *	@return bool True en cas de succès, sinon une exception sera levée pour signaler toute erreur
		 */
		public static function add($society, $package='business', $data, $sub=false) {
			if( trim($society) == '' ){
				return false;
			}

			if( !in_array($package, array('business', 'legacy')) ){
				return false;
			}

			$datastore = new DatastoreClient();
			$http_host_ria = 'riashop-'.$_SERVER['HTTP_HOST'];
			// Récupère le prochain numéro de locataire
			{
				$next = 0;

				$query = $datastore->query()
					->kind('db_tenants');

				$result = $datastore->runQuery($query);
				if( $result === null ){
					return false;
				}

				foreach( $result as $res ){
					if( $next < $res->getProperty('tenant_id') ){
						$next = $res->getProperty('tenant_id');
					}
				}

				if( $next <= 0 ){
					throw new Exception('Impossible de récupérer le prochain identifiant de locataire');
				}

				$next = $next + 1;
				$data['next'] = $next;
			}

			// Initialise la connexion avec le serveur maria qui va accueillir le nouveu locataire
			{
				$maria_db = $maria_server = $maria_user = $maria_password = null;

				foreach( array('db', 'server', 'user', 'password') as $col ){
					$query = $datastore->query()
							->kind('configs')
							->filter('cfg_code', '=', 'create_tenant_'.$package.'_'.$col);

					$result = $datastore->runQuery($query)->current();
					if( $result === null ){
							return false;
					}

					${'maria_'.$col} = $result->getProperty('cfg_value');
				}

				if( $maria_db === null || $maria_user === null || $maria_password === null ){
					throw new Exception('Impossible d\'établir la connexion avec la base de données pour créer un nouveau locataire');
				}

				RegisterGCPConnection::connect(array(
					'mysql_server' => $maria_server,
					'mysql_base' => $maria_db,
					'mysql_user' => $maria_user,
					'mysql_password' => $maria_password,
				));
			}

			// Ajout du tenant dans le registre
			{
				$tenant = array(
					'crons_activated' => true,
					'mysql_base' => $maria_db,
					'mysql_password' => $maria_password,
					'mysql_server' => $maria_server,
					'mysql_user' => $maria_user,
					'tenant_id' => $next,
					'tenant_name' => $society,
					'package' => $package
				);

				// Création d'une entité datastore + envoi des informations, cela va créer un nouveau document dans le registre.
				$entity = $datastore->entity('db_tenants', $tenant);
				$result = $datastore->insert($entity);

				if( $result === null ){
					throw new Exception('Impossible de créer le nouveau locataire dans le registre');
				}

			}

			// Création du tenant en base + ajout de la configuration de bases
			{
				$res = tnt_tenants_create_poc($data, true, $package, $sub);
				if( !$res['result'] ){
					throw new Exception('Erreur lors de la création du locataire en base : '.implode(' | ', $res['content']['errors']));
				}
			}

			// Mise à jour des tokens synchro et api après création du tenant
			{
				$key = $entity->key()->pathEndIdentifier();

				// Charge l'entité depuis GCloud (obliger pour la mettre à jour)
				$key = $datastore->key( 'db_tenants', $key );
				$entity = $datastore->lookup($key);

				// Récupère le token de synchronisation et celui pour l'API Yuto
				$logtoken = tnt_tenants_get_logtoken($res['content']['tnt_id']);
				$yutotoken = substr($logtoken, 0, 6);

				$entity->setProperty('tokens', array($logtoken, $yutotoken));
				$res = $datastore->update($entity);

				if( $res === null ){
					throw new Exception('Impossible de mettre à jour les informations de tokens dans le registre pour le locataire n°'.$res['content']['tnt_id']);
				}
			}

			// Envoi un mail au nouveau client pour le prévenir que son espace administrateur vient d'être créé
			// Seulement si l'envoi est explicitement demandé et qu'une période d'essai existe
			if( isset($data['sendmail'], $data['testing']) && $data['sendmail'] && is_numeric($data['testing']) && $data['testing'] > 0 ){
				// Récupère la configuration mail à utiliser
				$ar_cfg = cfg_emails_yuto_get();
				if( is_array($ar_cfg) && array_key_exists('vel-yuto-add-tnt', $ar_cfg) && isset($data['admin_email']) && trim($data['admin_email']) != '' ){
					$cfg = $ar_cfg['vel-yuto-add-tnt'];

					$filecontent = $package == 'business' ? 'new-business' : 'newcustomer';
					$content = file_get_contents('/var/www/start.yuto.fr/htdocs/dist/template-email/'.$filecontent.'.html');

					// Date de fin de période d'essaie (aujourd'hui + 14 jours
					$date = new Datetime('+14 days');

					// Remplace certaine variable par des données sur le client ou des urls
					$replace = array(
						'[user_surname]' => 'Bonjour',
						'[date_fin_periode_essai]' => $date->format('d/m/Y'),
						'[url_acceder_a_mon_compte]' => 'https://'.$http_host_ria.'/options/my-account.php',
						'[url_je_me_connecte_a_riashop]' => 'https://'.$http_host_ria.'/',
						'[url_importe_mes_contacts]' => 'https://'.$http_host_ria.'/tools/imports/index.php',
						'[url_cree_mes_representants]' => '',
						'[url_installe_application_yutp]' => '',
						'[url_playstore]' => '',
						'[url_applestore]' => '',
						'[user_code]' => $yutotoken,
						'[user_email]' => $data['admin_email'],
					);

					foreach( $replace as $original=>$new ){
						$content = str_replace($original, $new, $content);
					}

					$email = new Email();
					$email->setFrom($cfg['from']);
					$email->addTo($data['admin_email']);
					$email->addBcc($cfg['bcc']);
					$email->setSubject('Confirmation de votre période d\'essai Yuto '.($package == 'business' ? 'Business' : ''));
					$email->addHtml($content);

					if( !$email->send() ){
						throw new Exception('Impossible d\'envoyer le mail avertissant le nouveau client de l\'ouverture de son espace d\'administration.');
					}
				}
			}

			return true;
		}

		/** Cette fonction est la nouvelle version permettant la création d'un tenant depuis le monitoring.
		 * 	Elle prend en charge aussi bien la création d'un Yuto que d'un site public ou un extranet.
		 * 	Elle peut créer un Yuto et un site e-commerce en même temps.
		 *
		 * 	@param string $config_db Obligatoire, détermine le serveur sur lequel créé le tenant (valeur acceptée : 'business')
		 * 	@param string $society Obligatoire, nom de la société
		 * 	@param array $data Obligatoire, tableau contenant toutes les informations nécessaire à la création
		 *
		 * 	@return int L'identifiant du locataire nouvellement créé, en cas d'erreur, une exception sera levée avec le détail de cette erreur
		 */
		public static function addNewTenant( $config_db, $society, $data ){
			if( trim($society) == '' ){
				throw new Exception('Le nom de la société est obligatoire.');
			}

			$return_ids = [
				'tnt_id' => 0,
				'yuto_id' => 0,
				'web_id' => 0,
			];

			$datastore = new DatastoreClient();

			// Récupère le prochain numéro de locataire
			{
				$next = 0;

				$query = $datastore->query()
					->kind('db_tenants');

				$result = $datastore->runQuery($query);
				if( $result === null ){
					return false;
				}

				foreach( $result as $res ){
					if( $next < $res->getProperty('tenant_id') ){
						$next = $res->getProperty('tenant_id');
					}
				}

				if( $next <= 0 ){
					throw new Exception('Impossible de récupérer le prochain identifiant de locataire');
				}

				$next = $next + 1;
				$data['next'] = $next;
				$return_ids['tnt_id'] = $next;
			}

			// Initialise la connexion avec le serveur maria qui va accueillir le nouveu locataire
			{
				$maria_db = $maria_server = $maria_user = $maria_password = null;

				foreach( array('db', 'server', 'user', 'password') as $col ){
					$query = $datastore->query()
							->kind('configs')
							->filter('cfg_code', '=', 'create_tenant_'.$config_db.'_'.$col);

					$result = $datastore->runQuery($query)->current();
					if( $result === null ){
							return false;
					}

					${'maria_'.$col} = $result->getProperty('cfg_value');
				}

				if( $maria_db === null || $maria_user === null || $maria_password === null ){
					throw new Exception('Impossible d\'établir la connexion avec la base de données pour créer un nouveau locataire');
				}

				RegisterGCPConnection::connect(array(
					'mysql_server' => $maria_server,
					'mysql_base' => $maria_db,
					'mysql_user' => $maria_user,
					'mysql_password' => $maria_password,
				));
			}

			// Ajout du tenant dans le registre
			{
				$tenant = array(
					'crons_activated' => true,
					'mysql_base' => $maria_db,
					'mysql_password' => $maria_password,
					'mysql_server' => $maria_server,
					'mysql_user' => $maria_user,
					'tenant_id' => $next,
					'tenant_name' => $society
				);

				// Création d'une entité datastore + envoi des informations, cela va créer un nouveau document dans le registre.
				$entity = $datastore->entity('db_tenants', $tenant);
				$result = $datastore->insert($entity);

				if( $result === null ){
					throw new Exception('Impossible de créer le nouveau locataire dans le registre');
				}
			}

			$key_id  = $entity->key()->pathEndIdentifier();
			$key_obj = $datastore->key( 'db_tenants', $key_id );

			try{
				// Création du tenant en base + ajout de la configuration de base
				$res_ids = Monitoring::create( $key_id, $next, $data[0], $data[1], $data[2] );
				$return_ids = array_merge( $return_ids, $res_ids );
			}catch( Exception $e ){
				$entity = $datastore->lookup($key_obj);
				$entity->setProperty('tenant_name', 'à_supprimer');
				$datastore->update( $entity );
				tnt_tenant_del( $next );
				throw new Exception( $e->getMessage() );
			}

			// Mise à jour des tokens synchro et api après création du tenant
			{
				// Charge l'entité depuis GCloud (obliger pour la mettre à jour)
				$entity = $datastore->lookup($key_obj);

				// Récupère le token de synchronisation et celui pour l'API Yuto
				$logtoken = tnt_tenants_get_logtoken($next);
				$yutotoken = substr($logtoken, 0, 6);

				$entity->setProperty('tokens', array($logtoken, $yutotoken));
				$res = $datastore->update($entity);

				if( $res === null ){
					throw new Exception('Impossible de mettre à jour les informations de tokens dans le registre pour le locataire n°'.$res['content']['tnt_id']);
				}
			}

			return $return_ids;
		}

		/** Cette fonction permet de récupérer le type d'abonnement Yuto pour un locataire donné.
		 * 	@param int $tnt_id Obligatoire, identifiant d'un tenant
		 * 	@param bool $empty Optionnel, par défaut la fonction retournera "legacy", mettre true pour retourner vide si aucun package appliqué
		 * 	@return string Le type d'abonnement (business ou legacy)
		 */
		public static function getPackage($tnt_id, $empty=false){
			if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
				return false;
			}

			global $memcached;

			// Recherche de l'information en cache avant d'allez la cherche dans le Registre
			$key = 'registergcp:getPackage'.($empty ? ':empty' : '').':'.$tnt_id;
			if( $get = $memcached->get($key) ){
				return $get;
			}

			$datastore = new DatastoreClient();

			$query = $datastore->query()
				->kind('db_tenants')
				->filter('tenant_id', '=', (int) $tnt_id);

			$result = $datastore->runQuery($query)->current();
			if( $result === null || $result->getProperty('deleted') ){
				return false;
			}

			$package = $result->getProperty('package');
			if( trim($package) == '' ){
				$package = $empty ? '' : 'legacy';
			}

			if( !in_array($package, array('business', 'legacy')) ){
				return false;
			}

			// Enregistre en cache le type de forfait pour 24h
			$memcached->set($key, $package, 24 * 60 * 60);

			return $package;
		}

		/** Cette fonction permet de récupérer le type d'abonnement BtoB pour un locataire donné.
		 * 	@param int $tnt_id Obligatoire, identifiant d'un tenant
		 * 	@param bool $empty Optionnel, par défaut la fonction retournera "legacy", mettre true pour retourner vide si aucun package appliqué
		 * 	@return string Le type d'abonnement (business ou legacy)
		 */
		public static function getPackageBtoB($tnt_id, $empty=false){
			if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
				return false;
			}

			global $memcached;

			// Recherche de l'information en cache avant d'allez la cherche dans le Registre
			$key = 'registergcp:getPackageBtoB'.($empty ? ':empty' : '').':'.$tnt_id;
			if( $get = $memcached->get($key) ){
				return $get;
			}

			$datastore = new DatastoreClient();

			$query = $datastore->query()
				->kind('db_tenants')
				->filter('tenant_id', '=', (int) $tnt_id);

			$result = $datastore->runQuery($query)->current();
			if( $result === null || $result->getProperty('deleted') ){
				return false;
			}

			$package = $result->getProperty('package_btob');
			if( trim($package) == '' ){
				$package = $empty ? '' : 'legacy';
			}

			if( !in_array($package, array('business', 'legacy')) ){
				return false;
			}

			// Enregistre en cache le type de forfait pour 24h
			$memcached->set($key, $package, 24 * 60 * 60);

			return $package;
		}

		/** Cette fonction permet de récupérer l'identifiant dans le registre d'un locataire.
		 * 	@param int $tnt_id Obligatoire, identifiant d'un locataire
		 * 	@retunr string La clé dans le registre de ce locataire, false si non trouvée
		 */
		public static function getRegisterKey( $tnt_id ){
			if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
				return false;
			}

			$datastore = new DatastoreClient();
			$query = $datastore->query()
				->kind('db_tenants')
				->filter('tenant_id', '=', (int) $tnt_id);

			$result = $datastore->runQuery($query)->current();
			if( $result === null || $result->getProperty('deleted') ){
				return false;
			}

			return $result->key()->pathEndIdentifier();
		}

		/** Cette fonction permet de récupérer les datas du registre
		 * 	@param int $tnt_id Obligatoire, identifiant d'un tenant
		 * 	@return un tableau avec toutes les datas stocké pour un tenant
		 *		-package : code du package pour un abo yuto
		 *		-package_btob : code du package pour un abo riashop
		 *		-vhost : domaine pour l'extranet
		 */
		public static function getDatas($tnt_id){
			if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
				return false;
			}

			global $memcached;

			// Recherche de l'information en cache avant d'allez la cherche dans le Registre
			$key = 'registergcp:getDatas:'.$tnt_id;
			if( $get = $memcached->get($key) ){
				//return $get;
			}

			$datastore = new DatastoreClient();

			$query = $datastore->query()
				->kind('db_tenants')
				->filter('tenant_id', '=', (int) $tnt_id);

			$result = $datastore->runQuery($query)->current();
			if( $result === null || $result->getProperty('deleted') ){
				return false;
			}

			$return = array(
				'package' => $result->getProperty('package'),
				'package_btob' => $result->getProperty('package_btob'),
				'vhost' => $result->getProperty('vhost'),
			);

			// Enregistre en cache
			$memcached->set($key, $return, 24 * 60 * 60);

			return $return;
		}

		/** Cette fonction permet de mettre à jour le vhost pour le locataire
		 * 	@param int $tnt_id Obligatoire, identifiant d'un tenant
		 * 	@param string $vhost Obligatoire, sous domaine + domaine
		 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		public function setVhost( $tnt_id, $vhost ){
			if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
				return false;
			}

			global $memcached;

			$datastore = new DatastoreClient();

			$query = $datastore->query()
				->kind('db_tenants')
				->filter('tenant_id', '=', (int) $tnt_id);

			$result = $datastore->runQuery($query)->current();
			if( $result === null || $result->getProperty('deleted') ){
				return false;
			}

			$result->setProperty( 'vhost', $vhost );

			$res = $datastore->update( $result );

			if( !$res ){
				return false;
			}

			// vide le cache pour le getdatas
			$removekey = 'registergcp:getDatas:'.$tnt_id;
			if( $memcached->get($removekey) ){
				$memcached->delete($removekey);
			}

			$removekey = 'registergcp:existVhost:'.md5( $vhost );
			if( $memcached->get($removekey) ){
				$memcached->delete($removekey);
			}

			return $res;
		}

		/** Cette fonction permet de tester si un domaine existe
		 * 	@param string $vhost Obligatoire, domaine recherché
		 * 	@return bool|array false en cas d'erreur sinon un tableau contenant :
		 * 										- tnt_id : identifiant du tenant
		 */
		public static function existVhost( $vhost ){
			global $memcached;

			if( trim($vhost) == '' ){
				return false;
			}

			// recherche dans le registre un tenant donc l'attribut "vhost" correspondrait
			$key_memcached = 'registergcp:existVhost:'.md5( $vhost );
			if( ($get = $memcached->get($key_memcached)) ){
				return $get === 'none' ? false : $get;
			}

			$datastore = new DatastoreClient();

			$query = $datastore->query()
				->kind('db_tenants')
				->filter('vhost', '=', $vhost);

			// Si aucun tenant ne correspond, on retourne false
			// Mise en cache pendant 3h
			$result = $datastore->runQuery($query)->current();
			if( $result === null || $result->getProperty('deleted') ){
				$memcached->set( $key_memcached, 'none', 60 * 60 * 3 );
				return false;
			}

			$ar_config = [];

			// Récupère l'identifiant du tenant
			$ar_config['tnt_id'] = $result->getProperty('tenant_id');

			// Stock des informations dans le cache pendant 3h
			$memcached->set( $key_memcached, $ar_config, 60 * 60 * 3);

			return $ar_config;
		}

		/** Cette fonction permet de récupérer l'identifiant d'un tenant à partir du domaine où l'on est.
		 * 	@param string $vhost Obligatoire, domaine recherché
		 * 	@return bool|array false en cas d'erreur sinon un tableau contenant :
		 * 										- tnt_id : identifiant du tenant
		 * 										- wst_id : identifiant du website
		 * 										- lng_code : code de la langue
		 */
		public static function getByVhost( $vhost ){
			global $memcached;

			if( trim($vhost) == '' ){
				return false;
			}

			// recherche dans le registre un tenant donc l'attribut "vhost" correspondrait
			$key_memcached = 'registergcp:getByVhost:'.md5( $vhost );
			if( !isset($_GET['getByVhostForcedCache']) && ($get = $memcached->get($key_memcached)) ){
				return $get === 'none' ? false : $get;
			}

			$datastore = new DatastoreClient();

			$query = $datastore->query()
				->kind('db_tenants')
				->filter('vhost', '=', $vhost);

			// Si aucun tenant ne correspond, on retourne false
			// Mise en cache pendant 3h
			$result = $datastore->runQuery($query)->current();
			if( $result === null ){
				$memcached->set( $key_memcached, 'none', 60 * 60 * 3 );
				return false;
			}

			$ar_config = [];

			if( $result->getProperty('deleted') ){
				return false;
			}

			// Récupère l'identifiant du tenant
			$ar_config['tnt_id'] = $result->getProperty('tenant_id');

			// Connexion à la base de données du tenant
			$res = RegisterGCPConnection::init( $ar_config['tnt_id'] );

			// Si la connexion a échouée on retourne false
			if( $res === false ){
				$memcached->set( $key_memcached, 'none', 60 * 60 * 3 );
				return false;
			}

			// Charge l'identifiant du website + la langue
			// On recherche via le vhost et dans la bdd (twl_url) on remplace 'http://', 'https://' et '/' pas vide
			$res = ria_mysql_query('
				select twl_wst_id as wst_id, twl_lng_code as lng_code
					from tnt_websites_languages
					where twl_tnt_id = '.$ar_config['tnt_id'].'
						and replace(
								replace(
										replace(twl_url, "http://", "")
									, "https://", "")
							, "/", ""
						) = "'.addslashes( $vhost ).'"
			');

			// Les informations sur le site n'ont pas été trouvé
			if( !$res || !ria_mysql_num_rows($res) ){
				$memcached->set( $key_memcached, 'none', 60 * 60 * 3 );
				return false;
			}

			$r = ria_mysql_fetch_assoc( $res );
			$ar_config['wst_id'] = $r['wst_id'];
			$ar_config['lng_code'] = $r['lng_code'];

			// Stock des informations dans le cache pendant 3h
			$memcached->set( $key_memcached, $ar_config, 60 * 60 * 3);

			return $ar_config;
		}
	}

	/**	\brief Cette classe permet d'établir la connexion à la base de données en utilisant une des informations suivantes :
	 * 					- $config['tnt_id'] : identifiant du tenant préciser dans chaque fichier config.inc.php des sites marchands
	 *					- $_GET['logtoken'] : information présente lors de l'utilisation de l'API que ce soit par le logiciel de synchronisation ou les différents appareils ayant installés Yuto
	 */
	class RegisterGCPConnection {
		/** Cette fonction permet d'initialiser la connexion à la base de données.
		 *  Elle s'appuit sur la présence de $config['tnt_id'] ou $_GET['logtoken'] pour savoir quel est le locataire.
		 *  @param int $tnt_id Optionnel, permet d'initialisé la connexion pour un tenant précis
		 *  @param bool $load_conf Optionnel, permet de charger la configuration du tenant (seulement si $tnt_id est renseigné)
		 * 	@param bool $nocache Optionn, permet de ne pas utiliser le cache (par défaut à false)
		 *
		 *  @return bool True si l'initialisation a réussie, False dans le cas contraire
		 */
		public static function init( $tnt_id=0, $load_conf=false, $nocache=false ) {
			global $config, $ria_db_connect, $memcached;

			if( !is_numeric($tnt_id) || $tnt_id < 0 ){
				return false;
			}

			// Force le tnt_id en config en fonction du paramètre donné
			if( $tnt_id > 0 ){
				$config['tnt_id'] = $tnt_id;
			}

			$key_memcached = 'registergcp:init';
			if( isset($config['tnt_id']) && is_numeric($config['tnt_id']) && $config['tnt_id'] > 0 ){
				$key_memcached .= ':filter-tenant-id-'.$config['tnt_id'];
			}

			if( isset($_GET['logtoken']) && trim($_GET['logtoken']) != '' ){
				$key_memcached .= ':filter-logtoken-'.$_GET['logtoken'];
			}

			// Si aucune des informations ($config['tnt_id'] ou $_GET['logtoken']) n'est présente alors la requête échoura
			// Il s'agit d'une protection pour bloquer la connexion à une mauvaise base de données
			if( $key_memcached == 'registergcp:init' ){
				return false;
			}

			if( !$nocache && ($get = $memcached->get($key_memcached)) ){
				$ria_db_connect = $get;
			}else{
				// Initialise la connexion au datastore via l'API de Google
				$datastore = new DatastoreClient();

				// Création du début de la requête
				$query = $datastore->query()
						->kind('db_tenants');

				// Complète la requête selon la présence de $config['tnt_id'] ou $_GET['logtoken']
				{
					$one_where = false;
					if( isset($config['tnt_id']) && is_numeric($config['tnt_id']) && $config['tnt_id'] > 0 ){
						$one_where = true;
						$query->filter('tenant_id', '=', $config['tnt_id']);
					}

					if( $tnt_id <= 0 ){
						if( isset($_GET['logtoken']) && trim($_GET['logtoken']) != '' ){
							$one_where = true;
							$query->filter('tokens', '=', $_GET['logtoken']);
						}
					}
				}

				// Si aucune des informations ($config['tnt_id'] ou $_GET['logtoken']) n'est présente alors la requête échoura
				// Il s'agit d'une protection pour bloquer la connexion à une mauvaise base de données
				if( !$one_where ){
					return false;
				}

				// Réalise la requête et place le pointeur sur le premier résultat
				$result = $datastore->runQuery($query)->current();
				if( $result === null ){
					return false;
				}

				// check si le tenant est supprimé
				if( $result->getProperty('deleted') ){
					$config['deleted'] = true;
					return false;
				}

				// Initialise la variable $ria_db_connect utilisé par la suite pour réaliser toutes les requêtes SQL (cf. ria.mysql.inc.php)
				$ria_db_connect = array(
					_DB_RIASHOP => array(
						'server' 					=> $result->getProperty('mysql_server'),
						'base' 						=> $result->getProperty('mysql_base'),
						'user'						=> $result->getProperty('mysql_user'),
						'password' 				=> $result->getProperty('mysql_password'),
						'link_identifier' => false,
					)
				);


				// Mise en cache du résultat pendant 15 minutes
				$memcached->set( $key_memcached, $ria_db_connect, 60 * 15 );
			}

			// Dans le cas où la connexion est faite pour un tenant en particulier, il est possible possible de charger sa configuration par défaut
			// Attention cela va écrasé un potentiel $config déjà initialisé hors de cette classe
			if( $tnt_id > 0 && $load_conf ){
				$config['wst_id'] = tnt_tenants_get_website_default($config['tnt_id']);
				$config['date_created'] = tnt_tenants_get_date_created($config['tnt_id']);
				cfg_variables_load($config);
			}

			return true;
		}

		/** Cette fonction permet de réinitialiser une connexion.
		 *  @return bool True si la réinitialisation a réussie, False dans le cas contraire
		 */
		public static function reinit() {
			if (!self::init()) {
				return false;
			}

			return ria_mysql_connection(_DB_DEFAULT);
		}

		/** Cette fonction permet d'initialiser la connexion sur une base précise
		 * 	@param int|array $data Obligatoire, identifiant du tenant ou tableau contenant les informations permettant la connexion
		 * 	@return bool False si le paramètre obligatoire est manquant ou faux, True en cas de succès
		 */
		public static function connect($data) {
			global $ria_db_connect;

			if (is_numeric($data) && $data > 0) {
				$ar_connections = RegisterGCP::create()->getConnections($data);
				if (!is_array($ar_connections) || !count($ar_connections)) {
					return false;
				}

				$data = array_shift($ar_connections);
			}

			if (!ria_array_key_exists(array('mysql_server', 'mysql_base', 'mysql_user', 'mysql_password'), $data)) {
				return false;
			}

			$ria_db_connect = array(
				_DB_RIASHOP => array(
					'server' 			=> $data['mysql_server'], // '35.205.253.131',
					'base' 				=> $data['mysql_base'],
					'user' 				=> $data['mysql_user'],
					'password' 			=> $data['mysql_password'],
					'link_identifier'   => false
				)
			);

			return ria_mysql_connection(_DB_DEFAULT);
		}
	}

	/**	\brief Cette classe permet de réaliser des requêtes cross-tenant sur le registre des tenants "db_tenants".
	 */
	class Monitoring {
		private $ar_tenants = array();

		/**	Cette fonction permet de récupérer les données associées à un locataire
		 * 	@return array Un tableau clé/valeurs :
		 * 		- name : Nom du tenant
		 * 		- connect : informations de connexion
		 * 		- etc...
		 */
		public function getTenant( $id ){
			$ar_tenants = $this->getTenants();
			foreach( $ar_tenants as $tnt_id=>$data ){
				if( $tnt_id==$id ){
					return $data;
				}
			}

			return false;
		}

		/** Cette fonction permet de récupérer les informations contenus dans le registre des tenants
		 * 	@param bool $use_cache Optionnel, par défaut à true, détermine si le cache est utilisé ou non
		 * 	@return array Un tableau clé / valeurs avec comme clé l'identifiant du tenant et comme valeur :
		 * 				- name : Nom du tenant
		 * 				- connect : informations de connexion
		 */
		public function getTenants( $use_cache=true ){
			global $memcached;

			if (count($this->ar_tenants)) {
				return $this->ar_tenants;
			}

			$key_memcached = 'Monitoring:getTenants:usr_id:'.( isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0);
			if( $use_cache && ($ar_tenants = $memcached->get($key_memcached))!==false && is_array($ar_tenants) ){
				$this->ar_tenants = $ar_tenants;
				return $ar_tenants;
			}

			// Initialise la connexion au datastore via l'API de Google
			$datastore = new DatastoreClient();

			// Cherche un compte administrateur ayant cette adresse mail
			$query = $datastore->query()
				->kind('db_tenants');

			$result = $datastore->runQuery($query);
			if( $result === null ){
				return false;
			}

			foreach( $result as $res ){
				if( $res->getProperty('tenant_id')== 0 ){ // cas du monitoring
					continue;
				}
				if( $res->getProperty('deleted') ){ // cas des tenants supprimé
					continue;
				}

				$data = array(
					'key' => $res->key()->pathEndIdentifier(),
					'name' => $res->getProperty('tenant_name'),
					'package' => $res->getProperty('package'),
					'package_btob' => $res->getProperty('package_btob'),
					'mysql' => array(
						'mysql_server' 	=> $res->getProperty('mysql_server'),
						'mysql_base' 	=> $res->getProperty('mysql_base'),
						'mysql_user'	=> $res->getProperty('mysql_user'),
						'mysql_password' => $res->getProperty('mysql_password'),
					)
				);

				$this->ar_tenants[$res->getProperty('tenant_id')] = $data;
			}

			$this->ar_tenants = array_msort($this->ar_tenants, array('name'=>SORT_ASC));

			// Mise en cache du résultat pendant une heure
			$memcached->set( $key_memcached, $this->ar_tenants, 15 * 60 );

			return $this->ar_tenants;
		}

		/** Cette fonction permet de récupérer les règles d'activation liés aux crons.
		 * 	@param int $tnt_id Optionnel, identifiant d'un locataire
		 * 	@param bool $cache Optionnel, par défaut le résutat est mis en cache et renvoyé, mettre false pour ne pas utiliser le cache (ou le mettre à jour)
		 * 	@return bool False si les règles n'ont pues être récupérées
		 * 	@return array Un tableau contenant pour chaque locataire (si le locataire est donné en paramètre, les règles pour celui-ci seront retournées) :
		 * 			- actived : si oui ou non les crons sont activés (règle générale)
		 * 			- todo : sera compléter en phase deux avec chaque crons
		 */
		public function getCronRules($tnt_id=0, $cache=true) {
			global $memcached;

			$cache=true; //DELAGE INTERDICTION DE BYPASSER LE CACHE
			if (!is_numeric($tnt_id) || $tnt_id < 0) {
				return false;
			}

			$ar_rules = false;
			$key_memcached = 'Monitoring:getCronRules';
			if( $cache && ($get = $memcached->get($key_memcached)) ){
				if (is_array($get)) {
					$ar_rules = $get;
				}
			}else{
				$datastore = new DatastoreClient();

				$query = $datastore->query()
					->kind('db_tenants');

				$result = $datastore->runQuery($query);
				if( $result !== null ){
					foreach( $result as $res ){
						$ar_rules[$res->getProperty('tenant_id')] = array(
							'activated' => $res->getProperty('crons_activated')
						);
					}
				}

				$memcached->set($key_memcached, (is_array($ar_rules) && count($ar_rules) ? $ar_rules : 'none'), 60*60*5);
			}

			if ($tnt_id) {
				if (array_key_exists($tnt_id, $ar_rules)) {
					$ar_rules = $ar_rules[$tnt_id];
				} else {
					$ar_rules = false;
				}
			}

			return $ar_rules;
		}

		/** Cette fonction permet de recharger le cache sur les règles d'activation des crons
		 * 	Elle est notamment appelée toutes les minutes par le script dans /crontabs : refresh-gcloud-register.php
		 */
		public function reloadCronRules() {
			$this->getCronRules(0, false);
		}

		/** Cette fonction permet de vérifier qu'un script est bien activé pour un locataire.
		 * 	@param int $tnt_id Obligatoire, identifiant d'un locataire
		 * 	@param string $script Obligatoire, nom du script
		 * 	@return bool True si le script est bien active, False dans le cas contraire
		 */
		public function cronIsActive($tnt_id, $script) {
			if (!is_numeric($tnt_id) || $tnt_id <= 0) {
				return false;
			}

			if (trim($script) == '') {
				return false;
			}

			$rules = $this->getCronRules($tnt_id);
			if (!is_array($rules) || !count($rules)) {
				return false;
			}

			if (!$rules['activated']) {
				return false;
			}

			return true;
		}

		/** Cette fonction permet de mettre à jour le nom d'un locataire dans le registre.
		 * 	@param string $key Obligatoire, clé du locataire dans le registre
		 * 	@param string $name Obligatoire, nouveau nom du client
		 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		public function setName( $key, $name ){
			if( trim($key) == '' || trim($name) == '' ){
				return false;
			}

			$datastore = new DatastoreClient();

			$key = $datastore->key( 'db_tenants', $key );
			$entity = $datastore->lookup($key);

			$entity->setProperty( 'tenant_name', $name );

			return $datastore->update( $entity );
		}

		/** Cette fonction permet de mettre à jour le type de formule choisie pour l'application Yuto.
		 * 	@param string $key Obligatoire, clé du locataire dans le registre
		 * 	@param string $package Obligatoire, nouvel formule choisi (valeurs acceptées : business ou entreprise)
		 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		public static function setPackage( $key, $package ){
			if( trim($key) == '' ){
				return false;
			}

			if( !in_array($package, array('business', 'entreprise')) ){
				return false;
			}

			// TODO : supprimer la clé memcached suivant : $key = 'registergcp:getPackage:'.$tnt_id;

			$datastore = new DatastoreClient();

			$key = $datastore->key( 'db_tenants', $key );
			$entity = $datastore->lookup( $key );
			$entity->setProperty( 'package', $package );

			return $datastore->update( $entity );
		}

		/** Cette fonction permet de mettre à jour le type de formule choisie pour le site BtoB.
		 * 	@param string $key Obligatoire, clé du locataire dans le registre
		 * 	@param string $package Obligatoire, nouvel formule choisi (valeurs acceptées : business ou entreprise)
		 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		public static function setPackageBtoB( $key, $package ){
			if( trim($key) == '' ){
				return false;
			}

			if( !in_array($package, array('business', 'entreprise')) ){
				return false;
			}

			$datastore = new DatastoreClient();

			$key = $datastore->key( 'db_tenants', $key );
			$entity = $datastore->lookup( $key );
			$entity->setProperty( 'package_btob', $package );

			return $datastore->update( $entity );
		}

		/** Cette fonction permet d'ajouter une nouvelle clé API.
		 * 	@param string $key Obligatoire, clé du locataire dans le registre
		 * 	@param string $token Obligatoire, clé API
		 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		public function addAPIKey( $key, $token ){
			if( trim($key) == '' || trim($token) == '' ){
				return false;
			}

			$datastore = new DatastoreClient();

			$key = $datastore->key( 'db_tenants', $key );
			$entity = $datastore->lookup($key);

			$tokens = $entity->getProperty('tokens');
			if( !is_array($tokens) ){
				$tokens = [];
			}

			$tokens[] = $token;

			$entity->setProperty( 'tokens', $tokens );
			return $datastore->update( $entity );
		}


		/** Cette fonction permet de supprimer une clé API.
		 * 	@param string $key Obligatoire, clé du locataire dans le registre
		 * 	@param string $token Obligatoire, clé API
		 * 	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
		 */
		public function removeAPIKey( $key, $token ){
			if( trim($key) == '' || trim($token) == '' ){
				return false;
			}

			$datastore = new DatastoreClient();

			$key = $datastore->key( 'db_tenants', $key );
			$entity = $datastore->lookup($key);

			$tokens = $entity->getProperty('tokens');
			if( is_array($tokens) && count($tokens) ){
				$key_token = array_search( $token, $tokens );

				if( $key_token >= 0 ){
					unset( $tokens[ $key_token ]);
				}
			}

			$entity->setProperty( 'tokens', $tokens );
			return $datastore->update( $entity );
		}

		/** Cette fonction permet de supprimer un locataire du registre.
		 * 	ATTENTION : cette action est irréversible.
		 * 	@param string $key Obligatoire, clé du locataire à supprimer dans le registre
		 * 	@param string $date_deleted_date, date à partir de laquelle les données peuvent être supprimé (par défaut 90 jours après la suppression réelle du tenant)
		 * 	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
		 */
		public function delete( $key, $date_deleted_data='' ){
			if( trim($key) == '' ){
				return false;
			}

			$date = new DateTime();

			$datastore = new DatastoreClient();

			$key = $datastore->key( 'db_tenants', $key );
			$entity = $datastore->lookup( $key );
			$entity->setProperty( 'deleted', $date->format('Y-m-d H:i:s') );

			$date_deleted_data = dateheureparse( $date_deleted_data );
			if( !isdateheure($date_deleted_data) ){
				$date->modify( '+ 90 days' );
				$entity->setProperty( 'deleted-data', $date->format('Y-m-d H:i:s') );
			}else{
				$entity->setProperty( 'deleted-data', $date_deleted_data.' '.$date->format('H:i:s') );
			}

			return $datastore->update( $entity );
		}

		/** Cette fonction permet de créer un nouveau locataire RiaShop.
		 *  @param string $gcp_key Obligatoire, clé du locataire dans le registre GCP
		 *  @param int $tnt_id Obligatoire, identifiant du locataire
		 *  @param array $data_tenant Obligatoire, information sur le locataire :
		 *    - name : nom du tenant
		 *    - address1 : adresse postal du tenant
		 *    - address2 : complément d'adresse
		 *    - zipcode : code postal de l'adresse
		 *    - city : ville de l'adresse
		 *    - use_sync : si oui ou non le tenant utilise le logiciel de synchronisation
		 *    - cpt-admin : Information sur le compte administrateur
		 *            - civility : civilité de l'administrateur
		 *            - firstname : prénom de l'administrateur
		 *            - lastname : nom de l'administrateur
		 *            - society : société de l'administrateur
		 *            - email : adresse mail de l'administrateur
		 *            - password : mot de passe choisi
		 *            - address1 : adresse postal de facturation
		 *            - address2 : complément d'adresse de facturation
		 *            - zipcode : code postal de facturation
		 *            - city : ville de facturation
		 *            - phone : numéro de téléphone
		 *
		 *  @param array $data_yuto Optionnel, information sur la formule Yuto à créer
		 *    - package : formule Yuto choisi
		 *          - none : non activé
		 *          - business
		 *          - entreprise
		 * 		- abo-active : si oui ou non l'abonnement est active
		 * 		- abo-amount : montant de l'abonnement (obligatoire si abo-active est à true)
		 * 		- abo-type : si l'abonnement est mensuel ou annuel (obligatoire si abo-active est à true)
		 * 		- start : date de début de l'activation des licences
		 * 		- end : date de fin de l'activation des licences (obligatoire si abo-active est à false)
		 *
		 *  @param array $data_web Optionnel, information sur la formule Web à créer
		 *    - package : formule Web choisi
		 *          - none : non activé
		 *          - entreprise
		 * 		- type : type de site web (_WST_TYPE_SHOP => Boutique en ligne ou _WST_TYPE_EXTRANET => Extranet)
		 *
		 * 	@TODO : Il manque une ligne dans wst_websites_languages()
		 * 	@TODO : Adapter le wst_name en fonction du type 'site public' ou 'extranet'
		 * 	@TODO : Activer la variable dlv_active_port 'true' (pour les site web)
		 * 	@TODO : Activer la variable dlv_active_port_config 'true' (pour les site web)
		 *
		 *  @return array Une exception sera levée dans le cas contraire, sinon un tableau contenant :
		 * 		- yuto_id : identifiant du site Yuto (si demandé dans la création)
		 * 		- web_id : identifiant du site web (si demandé dans la création)
		 */
		public static function create($gcp_key, $tnt_id, $data_tenant, $data_yuto=array(), $data_web=array() ){
			// Contrôle que l'identifiant du nouveau tenant dans le registre est bien fourni
			if( trim($gcp_key) == '' ){
				throw new Exception('L\'identifiant du locataire dans le registre est manquant.');
			}

			// Contrôle que l'identifiant du nouveau tenant est bien donné en paramètre
			if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
				throw new Exception( 'L\'identifiant du tenant est obligatoire' );
			}

			// Contrôle que toutes les données obligatoires sont renseignées
			if( !ria_array_key_exists(['name', 'address1', 'address2', 'zipcode', 'city', 'use_sync'], $data_tenant) ){
				throw new Exception( 'Une ou plusieurs informations obligatoires sur le locataire sont manquantes.' );
			}

			if( trim($data_tenant['name']) == '' ){
				throw new Exception('Le nom de la société est obligatoire.');
			}
			if( trim($data_tenant['address1']) == '' ){
				throw new Exception('L\'adresse de la société est obligatoire.');
			}
			if( trim($data_tenant['zipcode']) == '' ){
				throw new Exception('Le code postal de la société est obligatoire.');
			}
			if( trim($data_tenant['name']) == '' ){
				throw new Exception('La ville de la société est obligatoire.');
			}

			{ // Contrôle que toutes les données obligatoires pour la formule Yuto sont renseignées
				if( !ria_array_key_exists(['package'], $data_yuto) ){
					throw new Exception( 'La formule Yuto, à mettre en place, est manquante.' );
				}
			}

			{ // Contrôle que toutes les données obligatoires pour la formule Web sont renseignées
				if( !ria_array_key_exists(array('package'), $data_web) ){
					throw new Exception( 'La formule Web, à mettre en place, est manquante.' );
				}
			}

			if( $data_yuto['package'] == 'none' && $data_web['package'] == 'none' ){
				throw new Exception( 'Aucune formule Yuto ou Web n\'a été choisie.');
			}

			// Création d'un tenant
			$tenant_add = tnt_tenants_add($data_tenant['name'], $data_tenant['address1'], $data_tenant['address2'], $data_tenant['zipcode'], $data_tenant['city'], '', '', '', $data_tenant['use_sync'], $tnt_id);
			if( !$tenant_add ){
				throw new Exception( 'Une erreur est survenue lors de la création du locataire.' );
			}

			$wst_yuto = $wst_web = 0;

			// Création d'un site Yuto si la formule Yuto est activée
			// Le site sera automatique déterminé comme site par défaut
			if( $data_yuto['package'] != 'none' ){
				// Complément de données pour la création du site
				$data_yuto = array_merge($data_yuto, array(
					'is_default' => true,
				));

				// Lancement de création
				$wst_yuto = Monitoring::addWebsite( $tnt_id, _WST_TYPE_FDV, 'Yuto', $data_yuto);
				$data_yuto['wst_id'] = $wst_yuto;

				if( !is_numeric($wst_yuto) || $wst_yuto <= 0 ){
					throw new Exception( 'Une erreur est survenue lors de la création du site Yuto.' );
				}

				// Attribution des droits sur le site
				Monitoring::setWebsiteRights( $tnt_id, $wst_yuto, _WST_TYPE_FDV, $data_yuto['package'], $data_yuto['abo-active'] );

				// Enregistre dans le registre la formule choisie pour Yuto
				Monitoring::setPackage( $gcp_key, $data_yuto['package'] );
			}

			// Création d'un site Web si la formule Web est activée
			// Le site sera déterminé comme site par défaut si aucun site Yuto n'est demandé
			if( $data_web['package'] != 'none' ){
				// Complément de données pour la création du site
				$data_web = array_merge($data_web, array(
					'is_default' => ($data_yuto['package'] == 'none'),
				));

				// Lancement de création
				$wst_name = $data_web['type'] == _WST_TYPE_EXTRANET ? 'Extranet' : 'Site public';
				$wst_web = Monitoring::addWebsite( $tnt_id, $data_web['type'], $wst_name, $data_web );
				$data_web['wst_id'] = $wst_web;

				if( !is_numeric($wst_web) || $wst_web <= 0 ){
					throw new Exception( 'Une erreur est survenue lors de la création du site Web.' );
				}

				// Attribution des droits sur le site
				$abo_active = isset($data_web['abo-active']) ? $data_web['abo-active'] : false;
				Monitoring::setWebsiteRights( $tnt_id, $wst_web, $data_web['type'], $data_web['package'], $abo_active );

				// Enregistre dans le registre la formule choisi pour le site web
				Monitoring::setPackageBtoB( $gcp_key, $data_web['package'] );
			}

			// Ajout des configurations minimales liées au tenant
			{
				// Miniatures d'images
				$res = ria_mysql_query('
					replace into cfg_images
						( img_tnt_id, img_code, img_width, img_height, img_background, img_clip, img_transparent, img_format, img_old_system )
					values
						( '.$tnt_id.', "high", 260, 260, "#FFFFFF", 0, 0, "jpg", 1 ),
						( '.$tnt_id.', "medium", 150, 150, "#FFFFFF", 0, 0, "jpg", 1 ),
						( '.$tnt_id.', "small", 80, 80, "#FFFFFF", 0, 0, "jpg", 1 ),
						( '.$tnt_id.', "zoom", 1980, 1980, "#FFFFFF", 0, 0, "jpg", 1 ),
						( '.$tnt_id.', "yuto", 1000, 0, "#FFFFFF", 0, 0, "jpg", 0 )
				');

				if( !$res ){
					throw new Exception( 'Une erreur est survenue lors de la configuration des images - étape 1.' );
				}

				// Configuration des miniatures d'images
				$res = ria_mysql_query('
					replace into cfg_images_filters
						( cif_tnt_id, cif_img_code, cif_filter_code, cif_width, cif_height, cif_value, cif_pos )
					values
						( '.$tnt_id.', "high", "fill", 260, 260, "", 0 ),
						( '.$tnt_id.', "medium", "fill", 150, 150, "", 0 ),
						( '.$tnt_id.', "small", "fill", 80, 80, "", 0 ),
						( '.$tnt_id.', "zoom", "fill", 1980, 1980, "", 0 ),
						( '.$tnt_id.', "yuto", "fill", 1000, 0, "", 0 )
				');

				if( !$res ){
					throw new Exception( 'Une erreur est survenue lors de la configuration des images - étape 2.' );
				}

				// Information sur le propriétaire
				$res = ria_mysql_query('
					replace into site_owner
						( owner_tnt_id, owner_type, owner_name, owner_firstname, owner_lastname, owner_address1, owner_address2, owner_zipcode, owner_city, owner_phone, owner_fax, owner_email, owner_naf, owner_taxcode, owner_inscription, owner_capital, owner_publication, owner_redaction )
					values
						( '.$tnt_id.', 0, "", "", "", "", "", "", "", "", "", "", "", "", "", NULL , "", "" )
				');

				if( !$res ){
					throw new Exception( 'Une erreur est survenue lors de la sauvegarde des informations sur le propriétaire.' );
				}

				// Conditions tarifaires accessibles
				$res = ria_mysql_query('
					replace into prc_price_fields
						( ppf_tnt_id, ppf_fld_id, ppf_priority )
					values
						( '.$tnt_id.', 455, 1 ),
						( '.$tnt_id.', 3634, 2 ),
						( '.$tnt_id.', 456, 3 )
				');

				if( !$res ){
					throw new Exception( 'Une erreur est survenue lors de la sauvegarde des conditions tarifaires accessibles.' );
				}

				// Relation produit par défaut
				$res = ria_mysql_query('
					replace into prd_relations_types
						( type_tnt_id, type_name, type_name_plural )
					values
						( '.$tnt_id.', "Produit associé", "Produits associés")
				');

				if( !$res ){
					throw new Exception( 'Une erreur est survenue lors de la création du type de relation produit "Produit associé"' );
				}
			}

			// Configuration par défaut
			$ar_cfg_default = array_merge( $data_tenant['cfg-values'], [
				'orders_update_state_included' => '3, 4, 5, 6, 7, 24, 27, 8, 12, 9, 10, 25', // Défini les statuts qui peuvent être utilisés depuis l'administration
				'allow_orders_update_state' => 'Oui', // Défini les statuts qui peuvent être utilisés depuis l'administration
			] );

			// Création de la catégorie tarifaire par défaut
			$res = ria_mysql_query('
				insert into prd_prices_categories
					( prc_tnt_id, prc_name, prc_ttc, prc_is_sync )
				values
					( '.$tnt_id.', "Par défaut", 0, 0 )
			');

			$ar_cfg_default['default_prc_id'] = ria_mysql_insert_id();
			if( !is_numeric($ar_cfg_default['default_prc_id']) || $ar_cfg_default['default_prc_id'] <= 0 ){
				throw new Exception( 'Une erreur est survenue lors de la création de la catégorie tarifaire par défaut.' );
			}

			// Création d'un dépôt
			$ar_cfg_default['default_dps_id'] = prd_deposits_add( 'Par défaut', true, '', '', '', '', 'FRANCE', '', '', '', false, 0, null, null, $tnt_id );
			if( !is_numeric($ar_cfg_default['default_dps_id']) || $ar_cfg_default['default_dps_id'] <= 0 ){
				throw new Exception( 'Une erreur est survenue lors de la création du dépôt principal.' );
			}

			// Sauvegarde la configuration par défaut
			foreach( $ar_cfg_default as $cfg_code=>$cfg_value ){
				// Pour Yuto
				if( is_numeric($wst_yuto) && $wst_yuto > 0 ){
					if( !cfg_overrides_set_value($cfg_code, $cfg_value, $wst_yuto, 0, $tnt_id) ){
						throw new Exception( 'Une erreur est survenue lors de la configuration de "'.$cfg_code.'" pour Yuto.' );
					}
				}

				// Pour le web
				if( is_numeric($wst_web) && $wst_web > 0 ){
					if( !cfg_overrides_set_value($cfg_code, $cfg_value, $wst_web, 0, $tnt_id) ){
						throw new Exception( 'Une erreur est survenue lors de la configuration de "'.$cfg_code.'" pour le web.' );
					}
				}
			}

			self::finishCreate( $tnt_id, $data_tenant, $data_yuto, $data_web );

			return ['yuto_id' => $wst_yuto, 'web_id' => $wst_web];
		}

		/** Cette fonction permet de créer un nouveau site à un locataire.
		 *  @param int $tnt_id Obligatoire, identifiant du tenant pour lequel créer le site
		 *  @param int $wst_type_id Obligatoire, type de site à créer
		 *  @param string $name, Obligatoire, nom du site à créer
		 *  @param array $data Obligatoire, information sur le site web / Yuto
		 */
		public static function addWebsite( $tnt_id, $wst_type_id, $name, $data ){
			{ // Contrôle que les informations secondaires obligatoire sont fournies
				if( !ria_array_key_exists(array('is_default'), $data) ){
					throw new Exception( 'Une ou plusieurs informations obligatoires à la création du site sont manquantes.' );
				}

				// Contrôle supplémentaire lorsqu'il s'agit d'un Yuto
				if( $wst_type_id == _WST_TYPE_FDV ){
					if( !ria_array_key_exists(['licences', 'abo-active'], $data) ){
						throw new Exception( 'Une ou plusieurs informations obligatoire à la création de la formule Yuto sont manquantes.' );
					}

					if( !is_numeric($data['licences']) || $data['licences'] <= 0 ){
						throw new Exception('Le nombre de licence est manquant.');
					}
				}else{
					if( !in_array($wst_type_id, array(_WST_TYPE_SHOP, _WST_TYPE_EXTRANET)) ){
						throw new Exception( 'Le type de site n\'est pas reconnu.');
					}
				}

				// Contrôle dans le cas où un abonnement est activé
				if( $data['abo-active'] ){
					if( !ria_array_key_exists(['start', 'abo-type', 'abo-amount'], $data) ){
						throw new Exception('Une ou plusieurs informations obligatoires à la gestion de l\'abonnement sont manquantes.');
					}

					if( !isdate($data['start']) ){
						throw new Exception('La date de début d\'abonnement est incorrecte.');
					}

					if( !in_array($data['abo-type'], array('month', 'year')) ){
						throw new Exception('Le type d\'abonnement est incorrecte.');
					}

					if( !is_numeric($data['abo-amount']) || $data['abo-amount'] < 0 ){
						throw new Exception('Le montant de l\'abonnement est incorrect.');
					}
				}elseif( $wst_type_id == _WST_TYPE_FDV ){
					// Aucun abonnement, mais les Yuto nécessite une période de validité pour les licences
					if( !ria_array_key_exists(['start', 'end'], $data) ) {
						throw new Exception('La période de validité des licences n\'est pas définie.');
					}

					if( !isdate($data['start']) || !isdate($data['end']) || strtotime($data['end']) < strtotime($data['start']) ){
						throw new Exception('Les informations de date sur la formule Yuto sont incorrectes.');
					}
				}
			}

			// Création du nouveau site (Yuto ou Web)
			// Les paramètres obligatoires sont contrôlés depuis la fonction de création elle-même
			$wst_id = wst_websites_add( $tnt_id, $wst_type_id, $name );
			if( !is_numeric($wst_id) || $wst_id <= 0 ){
				throw new Exception( 'Une erreur est survenue lors de la création du site web / Yuto.' );
			}

			// Défini ce site comme site par défaut
			if( $data['is_default'] ){
				ria_mysql_query('
					update tnt_websites
					set wst_default = 1
					where wst_tnt_id = '.$tnt_id.'
						and wst_id = '.$wst_id.'
				');
			}

			// Configuration par défaut
			$res = ria_mysql_query('
				replace into cfg_overrides
					( ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value )
				values
					( '.$tnt_id.', '.$wst_id.', 0, "email_alerts_enabled", "0" ),
					( '.$tnt_id.', '.$wst_id.', 0, "faq_enabled", "0" ),
					( '.$tnt_id.', '.$wst_id.', 0, "newsletter_enabled", "0" ),
					( '.$tnt_id.', '.$wst_id.', 0, "site_desc", "" ),
					( '.$tnt_id.', '.$wst_id.', 0, "site_name", "'.addslashes($name).'" ),
					( '.$tnt_id.', '.$wst_id.', 0, "fdv_can_only_edit_user", "1" ),
					( '.$tnt_id.', '.$wst_id.', 0, "sync_sage_price_merge_discount_enable", "1" ),
					( '.$tnt_id.', '.$wst_id.', 0, "sync_sage_price_or_discount_enable", "1" ),
					( '.$tnt_id.', '.$wst_id.', 0, "sync_use_new_api", "1" ),
					( '.$tnt_id.', '.$wst_id.', 0, "sync_sage_contact_empty_email_enable", "1" ),
					( '.$tnt_id.', '.$wst_id.', 0, "sync_sage_price_merge_discount_enable", "1" ),
					( '.$tnt_id.', '.$wst_id.', 0, "sync_sage_order_import_note_as_line_enable", "1" ),
					( '.$tnt_id.', '.$wst_id.', 0, "sync_global_empty_email_parttern", "*@yuto.com" )
			');

			if( !$res ){
				throw new Exception( 'Une erreur est survenue lors de la création des configurations minimales.' );
			}

			// Pour les Yuto, on active la variable "device_subscription_active"
			// Cette variable permet de désactiver automatiquement les appareils en trop par rapport au nombre de licences actives
			if( $wst_type_id == _WST_TYPE_FDV ){
				if( !isset($data['cfg']) || !is_array($data['cfg']) ){
					$data['cfg'] = [];
				}

				$data['cfg']['device_subscription_active'] = 'Oui';
			}

			// Enregistre la configuration personnalisé pour ce site
			if( isset($data['cfg']) && is_array($data['cfg']) ){
				foreach( $data['cfg'] as $cfg_code=>$cfg_value ){
					if( !cfg_overrides_set_value($cfg_code, $cfg_value, $wst_id, 0, $tnt_id) ){
						throw new Exception( 'Une erreur est survenue lors de l\'enregistre de la configuration.' );
					}
				}
			}

			// S'il ne s'agit pas du site par défaut, alors on copie les variables des dossiers documents/images/bannières du site par défaut
			if( !$data['is_default'] ){
				$res = ria_mysql_query('
					replace into cfg_overrides
						( ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value )
					select ovr_tnt_id, '.$wst_id.', ovr_usr_id, ovr_var_code, ovr_value
					from cfg_overrides
					where ovr_tnt_id = '.$tnt_id.'
						and ovr_wst_id = '.tnt_tenants_get_website_default( $tnt_id ).'
				');

				if( !$res ){
					throw new Exception( 'Une erreur est survenue lors de la configuration des dossiers contenants les documents / images.' );
				}
			}else{
				// Définition d'un dossier accueil tous les documents / images
				$md5_dirname_media = md5( $tnt_id.tnt_tenants_get_date_created($tnt_id).'media' );

				// Enregistre dans la configuration du site les accès à ces dossiers
				$res = ria_mysql_query('
					replace into cfg_overrides
						( ovr_tnt_id, ovr_wst_id, ovr_usr_id, ovr_var_code, ovr_value )
					values
						( '.$tnt_id.', '.$wst_id.', 0, "banners_url", "https://media.fr/'.$md5_dirname_media.'/banners" ),
						( '.$tnt_id.', '.$wst_id.', 0, "banners_dir", "/var/www/media.fr/'.$md5_dirname_media.'/banners" ),
						( '.$tnt_id.', '.$wst_id.', 0, "doc_dir", "/var/www/media.fr/'.$md5_dirname_media.'/documents" ),
						( '.$tnt_id.', '.$wst_id.', 0, "img_dir", "/var/www/media.fr/'.$md5_dirname_media.'/images" ),
						( '.$tnt_id.', '.$wst_id.', 0, "img_url", "https://media.fr/'.$md5_dirname_media.'/images" );
				');

				if( !$res ){
					throw new Exception( 'Une erreur est survenue lors de la configuration des dossiers contenants les documents / images.' );
				}

				try {
					if( !file_exists('/var/www/media.fr/' . $md5_dirname_media) ){
						// Création du dossier contenant les documents et les images du client
						mkdir('/var/www/media.fr/'.$md5_dirname_media);
						chown('/var/www/media.fr/'.$md5_dirname_media, 'www-data');
						chgrp('/var/www/media.fr/'.$md5_dirname_media, 'www-data');

						// Création des sous-dossiers
						foreach( array('banners', 'documents', 'documents/pieces_jointes', 'images', 'images/source') as $dir ){
							mkdir('/var/www/media.fr/'.$md5_dirname_media.'/'.$dir);
							chown('/var/www/media.fr/'.$md5_dirname_media.'/'.$dir, 'www-data');
							chgrp('/var/www/media.fr/'.$md5_dirname_media.'/'.$dir, 'www-data');
						}
					}
				}catch( Exception $e ){
					throw new Exception( 'Une erreur est survenue lors de la création des dossiers documents / images.' );
				}
			}

			// Définition du nom de la catégorie principal
			$cat_root_name = '';

			// Action supplémentaire selon le type de site
			switch( $wst_type_id ){
				case _WST_TYPE_FDV :
					$cat_root_name = 'Yuto';
					$type_sub = 'yuto';

					// Configuration des configs mails minimales
					$res = ria_mysql_query('
						replace into cfg_emails
								( email_tnt_id, email_wst_id, email_code, email_name, email_desc, email_allow_from, email_allow_to, email_allow_cc, email_allow_bcc, email_from, email_to, email_cc, email_bcc, email_pos )
							values
								( '.$tnt_id.', '.$wst_id.', "ord-alert", "Suivi des commandes", "Un email de suivi de commande est envoyé à chaque étape du traitement de la commande client (enregistrement, préparation de livraison, livraison, etc...).", 1, 0, 0, 1, "<EMAIL>", "'.$data['admin_email'].'", "", "", 0),
								( '.$tnt_id.', '.$wst_id.', "ord-owner", "Notifications de commande", "Cet email vous est envoyé pour vous avertir d\'&une nouvelle commande dans la boutique. Il contient des informations uniquement destinée à l\'&usage interne (ex: informations bancaires).", 1, 1, 1, 1, "<EMAIL>", "'.$data['admin_email'].'", "", "", 0),
								( '.$tnt_id.', '.$wst_id.', "device-new", "Nouvel appareil", "Envoi une notification dès qu\'&un nouvel appareil associe l\'&application Yuto à votre installation.", 1, 1, 1, 1, "<EMAIL>", "'.$data['admin_email'].'", "", "", 0),
								( '.$tnt_id.', '.$wst_id.', "notify-usr-del", "Suppression contact", "Envoi une notification par email dès qu\'une demande de suppression est formulée depuis Yuto", 1, 1, 1, 1, "<EMAIL>", "'.$data['admin_email'].'", "", "", 0);
					');

					if( !$res ){
						throw new Exception( 'Une erreur est survenue lors de la création des configurations d\'adresse mail.' );
					}

					break;
				case _WST_TYPE_SHOP :
					$cat_root_name = 'Site public';
					$type_sub = 'btoc';

					// Configuration de la génération des URLs (uniquement pour les sites web)
					$res = ria_mysql_query('
						replace into cfg_urls
								( url_tnt_id, url_wst_id, url_cls_id, url_key, url_name, url_used )
							values
								( '.$tnt_id.', '.$wst_id.', 1, "", "/catalog/product.php", 1),
								( '.$tnt_id.', '.$wst_id.', 3, "", "/catalog/index.php", 1),
								( '.$tnt_id.', '.$wst_id.', 6, "", "/stores/index.php", 1),
								( '.$tnt_id.', '.$wst_id.', 11, "", "/cms/view.php", 1),
								( '.$tnt_id.', '.$wst_id.', 13, "", "/faq/category.php", 1),
								( '.$tnt_id.', '.$wst_id.', 14, "", "/news/news.php", 1),
								( '.$tnt_id.', '.$wst_id.', 15, "", "/news/index.php", 1),
								( '.$tnt_id.', '.$wst_id.', 16, "", "/faq/category.php", 1);
					');

					if( !$res ){
						throw new Exception( 'Une erreur est survenue lors de la création des configurations de génération des URLs.' );
					}
					break;
				case _WST_TYPE_EXTRANET :
					$cat_root_name = 'Extranet';
					$type_sub = 'btob';

					// Configuration de la génération des URLs (uniquement pour les sites web)
					$res = ria_mysql_query('
						replace into cfg_urls
								( url_tnt_id, url_wst_id, url_cls_id, url_key, url_name, url_used )
							values
								( '.$tnt_id.', '.$wst_id.', 1, "", "/catalog/product.php", 1),
								( '.$tnt_id.', '.$wst_id.', 3, "", "/catalog/index.php", 1),
								( '.$tnt_id.', '.$wst_id.', 6, "", "/stores/index.php", 1),
								( '.$tnt_id.', '.$wst_id.', 11, "", "/cms/view.php", 1),
								( '.$tnt_id.', '.$wst_id.', 13, "", "/faq/category.php", 1),
								( '.$tnt_id.', '.$wst_id.', 14, "", "/news/news.php", 1),
								( '.$tnt_id.', '.$wst_id.', 15, "", "/news/index.php", 1),
								( '.$tnt_id.', '.$wst_id.', 16, "", "/faq/category.php", 1);
					');

					if( !$res ){
						throw new Exception( 'Une erreur est survenue lors de la création des configurations de génération des URLs.' );
					}
					break;
			}

			// Configuration du nombre de licences active + de l'abonnement si l'option est activé
			$formule_with_abo = false;

			$start = new DateTime( $data['start'] );

			$cost_ht = $cost_ttc = 0;
			if( $data['abo-active'] ){
				$formule_with_abo = true;

				$end = new DateTime( $data['start'] );

				// Détermine la date de fin selon si une période d'essai est renseignée ou selon le type d'abonnement (mensuel / annuel)
				if( is_numeric($data['abo-testing']) && $data['abo-testing'] > 0 ){
					$end->modify('+ '.($data['abo-testing'] - 1).' days');
				}else{
					$end->modify('+ 1'.$data['abo-type']);
				}

				// Défini le montant de l'abonnement
				$cost_ht = $data['abo-amount'] > 0 ? $data['abo-amount'] : 0;
				$cost_ttc = $cost_ht * _TVA_RATE_DEFAULT;
			}elseif( $wst_type_id == _WST_TYPE_FDV ){
				$formule_with_abo = true;
				$end = new DateTime( $data['end'] );
			}

			// Si la formule choisi est soumise à un abonnement
			// Yuto : dans tous les cas
			// BtoC ou BtoB : à la demande (VEL ou monitoring)
			if( $formule_with_abo ){
				if( !isset($data['abo-testing']) || !is_numeric($data['abo-testing']) ){
					$data['abo-testing'] = 0;
				}

				$abo_usr_id = 0;
				if( isset($data['abo-user']) && is_numeric($data['abo-user']) && $data['abo-user'] ){
					$abo_usr_id = $data['abo-user'];
				}

				// Ajout de l'abonnement
				if( !dev_subscribtions_add($data['licences'], $start->format('Y-m-d'), $end->format('Y-m-d'), $data['abo-testing'], 0, $cost_ttc,
					$abo_usr_id, $cost_ht, 0, $tnt_id, $type_sub)
				){
					throw new Exception('Une erreur est survenue lors de l\'enregistrement du nombre de licences et de l\'abonnement.');
				}
			}

			if( !isset($data['no_cat_root']) || !$data['no_cat_root'] ){
				// Création de la catégorie root "Yuto" / "Site public" / "Extranet" selon le type de website
				$cat_root_id = prd_categories_add( $cat_root_name, '', '', 0, true, false, null, false, 0 );
				if( !is_numeric($cat_root_id) || $cat_root_id <= 0 ){
					throw new Exception('Impossible de créer la catégorie principale.');
				}

				// Configure la catégorie principale (cat_root)
				if( !cfg_overrides_set_value('cat_root', $cat_root_id, $wst_id, 0) ){
					throw new Exception('Impossible de configurer la catégorie principale pour Yuto.');
				}
			}

			// Envoi un mail au nouveau client pour le prévenir que son espace administrateur vient d'être créé
			// Seulement si l'envoi est explicitement demandé et qu'une période d'essai existe
			// Seulement pour un site de type Yuto
			if( $wst_type_id == _WST_TYPE_FDV ){
				if( isset($data['sendmail'], $data['abo-testing']) && $data['sendmail'] && is_numeric($data['abo-testing']) && $data['abo-testing'] > 0 ){
					// Récupère la configuration mail à utiliser
					$ar_cfg = cfg_emails_yuto_get();
					if( is_array($ar_cfg) && array_key_exists('vel-yuto-add-tnt', $ar_cfg) && isset($data['admin_email']) && trim($data['admin_email']) != '' ){
						$cfg = $ar_cfg['vel-yuto-add-tnt'];

						$filecontent = $data['package'] == 'business' ? 'new-business' : 'newcustomer';
						$content = file_get_contents('/var/www/start.yuto.fr/htdocs/dist/template-email/'.$filecontent.'.html');

						// Date de fin de période d'essaie (aujourd'hui + 14 jours
						$date = new Datetime('+14 days');

						// Récupère la clé d'utilisation
						$logtoken = tnt_tenants_get_logtoken( $tnt_id );
						$yutotoken = substr( $logtoken, 0, 6 );
						$http_host_ria = 'riashop-'.$_SERVER['HTTP_HOST'];
						// Remplace certaine variable par des données sur le client ou des urls
						$replace = array(
							'[user_surname]' => 'Bonjour',
							'[date_fin_periode_essai]' => $date->format('d/m/Y'),
							'[url_acceder_a_mon_compte]' => 'https://'.$http_host_ria.'/options/my-account.php',
							'[url_je_me_connecte_a_riashop]' => 'https://'.$http_host_ria.'/',
							'[url_importe_mes_contacts]' => 'https://'.$http_host_ria.'/tools/imports/index.php',
							'[url_cree_mes_representants]' => '',
							'[url_installe_application_yutp]' => '',
							'[url_playstore]' => '',
							'[url_applestore]' => '',
							'[user_code]' => $yutotoken,
							'[user_email]' => $data['admin_email'],
						);

						foreach( $replace as $original=>$new ){
							$content = str_replace($original, $new, $content);
						}

						$email = new Email();
						$email->setFrom($cfg['from']);
						$email->addTo($data['admin_email']);
						$email->addBcc($cfg['bcc']);
						$email->setSubject('Confirmation de votre période d\'essai Yuto '.($data['package'] == 'business' ? 'Business' : ''));
						$email->addHtml($content);

						if( !$email->send() ){
							error_log('Impossible d\'envoyer le mail avertissant le nouveau client de l\'ouverture de son espace d\'administration.');
						}
					}
				}
			}

			return $wst_id;
		}

		/** Cette fonction permet d'appliquer les droits à un site web / Yuto.
		 *  @param int $tnt_id Obligatoire, identifiant d'un locataire
		 *  @param int $wst_id Obligatoire identifiant d'un site web / Yuto
		 *  @param int $wst_type_id Obligatoire, type de site web / Yuto
		 *  @param string $package Obligatoire, formule choisi (valeurs acceptées : 'business' ou 'entreprise')
		 *  @param $abo Optionnel, active ou non la gestion d'abonnement
		 *
		 *  @return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
		 */
		public static function setWebsiteRights( $tnt_id, $wst_id, $wst_type_id, $package, $abo=false ){
			if (!is_numeric($tnt_id) || $tnt_id <= 0) {
				return false;
			}

			if( !is_numeric($wst_id) || $wst_id <= 0 ){
				return false;
			}

			if( !is_numeric($wst_type_id) || $wst_type_id <= 0 ){
				return false;
			}

			if( !in_array($package, array('business', 'entreprise')) ){
				return false;
			}

			$ar_rights = [];

			switch( $wst_type_id ){
				case _WST_TYPE_FDV :
					switch( $package ){
						case 'business' :
							// Liste des droits pour un Yuto Business
							$ar_rights = [
									1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1100, 1101, 1102, 1103, 1104, 1105, 1150, 1151, 1152, 1153,
									1154, 1200, 1201, 1202, 1250, 1251, 1252, 1253, 1254, 1255, 2000, 2001, 2002, 2003, 2004, 2005, 2050, 2051, 2052, 2053, 2100, 2101, 2102, 2103, 2104, 2105,
									2150, 2151, 2152, 2153, 3000, 3001, 3004, 3005, 3008, 3050, 3051, 3052, 3053, 4000, 4001, 4002, 4003, 4004, 5000, 5001, 5002, 5003, 5004, 5005, 5008, 5009, 5010,
									5011, 5012, 5013, 5050, 5051, 5052, 5053, 5054, 5055, 5056, 5057, 6600, 6601, 6603, 6604, 6605, 6606, 6607, 6610, 7000, 7100,
									7101, 7102, 7103, 7104, 7105, 7106, 7107, 7108, 7109, 7110, 7111, 7112, 7113, 7122, 7123, 7124, 7125, 7126, 7127, 7128, 7129, 7130, 7131, 7200, 7205, 7206,
									7207, 7208, 7209, 7210, 7211, 7212, 7213, 7214, 7215, 7216, 7217, 7218, 7219, 7220, 7221, 7222, 7223, 7224, 7225, 7250, 7251, 7252, 7253, 7254, 7300, 7301,
									7302, 7303, 7304, 7305, 7306, 7307, 7350, 7450, 7600, 9000, 9001, 9002, 9100, 9150, 9151, 9152, 9300, 9301, 9350, 9400, 9560, 11000, 11001, 11002, 11003,
									11050, 11100, 11101, 11102, 11150, 11151, 11152, 11153, 11154, 11160, 11200, 12050, 13000, 13001, 13002, 13003, 13004, 13005, 13006, 13007, 13008, 13009,
									13010, 13011, 13013, 13014, 13015, 13016, 13017, 13018, 13019, 13020, 13021, 13022, 1000112, 13023, 13024, 13025, 1000113, 13026, 13027, 13028, 13029, 13030, 13031, 13032,
									13033, 13034, 13035, 13037, 13038, 13039, 13040, 13041, 13042, 13043, 13044, 13045, 13046, 13047, 13048, 13049, 13055, 13056, 13058, 13059, 13060, 13061,
									13062, 13012, 6650, 6611, 6612, 11250,
									1000075, 1000076, 1000077, 1000078, 1000079
							];
							break;
						case 'entreprise' :
							// Liste des droits pour un Yuto Entreprise
							$ar_rights = [
									1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1100, 1101, 1102, 1103, 1104, 1105, 1150, 1151, 1152, 1153,
									1154, 1200, 1201, 1202, 1250, 1251, 1252, 1253, 1254, 1255, 2000, 2001, 2002, 2003, 2004, 2005, 2050, 2051, 2052, 2053, 2100, 2101, 2102, 2103, 2104, 2105,
									2150, 2151, 2152, 2153, 3000, 3001, 3004, 3005, 3008, 3050, 3051, 3052, 3053, 4000, 4001, 4002, 4003, 4004, 5000, 5001, 5002, 5003, 5004, 5005, 5008, 5009, 5010,
									5011, 5012, 5013, 5050, 5051, 5052, 5053, 5054, 5055, 5056, 5057, 6600, 6601, 6603, 6604, 6605, 6606, 6607, 6610, 7000, 7100,
									7101, 7102, 7103, 7104, 7105, 7106, 7107, 7108, 7109, 7110, 7111, 7112, 7113, 7122, 7123, 7124, 7125, 7126, 7127, 7128, 7129, 7130, 7131, 7200, 7205, 7206,
									7207, 7208, 7209, 7210, 7211, 7212, 7213, 7214, 7215, 7216, 7217, 7218, 7219, 7220, 7221, 7222, 7223, 7224, 7225, 7250, 7251, 7252, 7253, 7254, 7300, 7301,
									7302, 7303, 7304, 7305, 7306, 7307, 7350, 7450, 7600, 9000, 9001, 9002, 9100, 9150, 9151, 9152, 9300, 9301, 9350, 9400, 9560, 11000, 11001, 11002, 11003,
									11050, 11100, 11101, 11102, 11150, 11151, 11152, 11153, 11154, 11160, 11200, 12050, 13000, 13001, 13002, 13003, 13004, 13005, 13006, 13007, 13008, 13009,
									13010, 13011, 13013, 13014, 13015, 13016, 13017, 13018, 13019, 13020, 13021, 13022, 1000112, 13023, 13024, 13025, 1000113, 13026, 13027, 13028, 13029, 13030, 13031, 13032,
									13033, 13034, 13035, 13037, 13038, 13039, 13040, 13041, 13042, 13043, 13044, 13045, 13046, 13047, 13048, 13049, 13055, 13056, 13058, 13059, 13060, 13061,
									13062, 13012, 6650, 6611, 6612, 11250,
									1000075, 1000076, 1000077, 1000078, 1000079
							];
							break;
					}
					break;
				case _WST_TYPE_SHOP :
				case _WST_TYPE_EXTRANET :
					switch( $package ){
						case 'business' :
						case 'entreprise' :
							// Pour le moment les droits ne sont pas encore défini selon la formule choisi
							$ar_rights = [
								2000, 2001, 2002, 2003, 2005, 3000, 3001, 3004, 3005, 3008, 3050, 3100, 3102, 5000, 5001, 5008, 6000, 6050, 6250, 6400, 7105,
								7109, 7130, 7250, 9560, 13012, 13049, 1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1100, 1101, 1102,
								1103, 1104, 1105, 1150, 1151, 1152, 1153, 1154, 1200, 1201, 1202, 1250, 1251, 1252, 1253, 2004, 2050, 2051, 2052, 2053, 2100, 2101, 2102, 2103, 2104,
								2105, 2150, 2151, 2152, 2153, 3051, 3052, 3053, 4000, 4001, 4002, 4003, 4004, 5002, 5003, 5004, 5005, 5006, 5009, 5010, 5011, 5012, 5013, 5050, 5051,
								5052, 5053, 5054, 5055, 5056, 5057, 5100, 6100, 6101, 6102, 6103, 6104, 6401, 6402, 6403, 6404, 6405, 6600, 6601, 6602, 6603, 6604, 6605, 6606, 6607,
								6610, 7000, 7100, 7101, 7102, 7103, 7104, 7106, 7107, 7108, 7110, 7111, 7112, 7113, 7122, 7123, 7124, 7125, 7126, 7127, 7128, 7129, 7131, 7150, 7200,
								7201, 7202, 7203, 7204, 7205, 7206, 7207, 7208, 7209, 7210, 7211, 7212, 7213, 7214, 7215, 7216, 7217, 7218, 7219, 7220, 7221, 7222, 7223, 7224, 7225,
								7251, 7252, 7253, 7254, 7300, 7301, 7302, 7303, 7304, 7305, 7306, 7307, 7350, 7400, 7450, 7600, 7650, 7700, 7701, 7702, 7800, 7801, 7802, 7803, 9000,
								9001, 9002, 9100, 9150, 9151, 9152, 9300, 9301, 9350, 9400, 12050, 12100, 6650, 6611, 6612, 6000, 6005, 6001, 6002, 6003, 6004, 6050, 6051, 6052, 6053,
								6200, 6201, 6202, 6250, 6251, 6252, 6253, 6259, 6254, 6255, 6256, 6257, 6258, 6300, 6305, 6301, 6302, 6303, 6304, 6500, 6501, 6502, 6503, 6505, 7850,
								7750, 7751, 7752, 7753, 7500, 7501, 7502, 7503, 7504, 7550, 7551, 8000, 8050, 9200, 9250, 9251, 9252, 1000014, 1000015, 1000016, 1000017, 9450, 9500,
								9501, 9502, 9503, 9504, 9505, 9506, 9507, 9550, 7001
							];
							break;
					}
					break;
			}

			// Activation du droit lorsque la formule est activée via abonnement (VEL)
			if( $abo ){
				$ar_rights[] = $wst_type_id == _WST_TYPE_FDV ? 1000029 : 1000068;
			}

			// Evite tout doublon pouvant provoquer une erreur lors de l'attribution des droits
			$ar_rights = array_unique( $ar_rights );

			// Atribution des droits au site
			if( !wst_website_rights_add($tnt_id, $wst_id, $ar_rights, false) ){
				throw new Exception('Une erreur est survenue lors de l\'enregistre des droits d\'accès.');
			}

			return true;
		}

		/** Cette fonction permet d'exécuter toutes les actions lors de la création d'un nouveau locataire, mais qui nécessite que la configuration minimal soit en place.
		 * 	@param int $tnt_id Obligatoire, identifiant d'un locataire
		 * 	@param array $data_tenant Obligatoire, information sur le locataire transmis pour la création
		 * 	@param array $data_yuto Obligatoire, information sur l'application Yuto
		 * 	@param array $data_web Obligatoire, information sur le site web
		 * 	@return bool True si tout s'est correctement déroulé, une exception, contenant l'erreur, sera levée dans le cas contraire
		 */
		private static function finishCreate( $tnt_id, $data_tenant, $data_yuto, $data_web ){

			if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
				throw new Exception('Impossible de finaliser la création du locataire.');
			}

			// Création d'un compte administrateur
			// Le compte administrateur est optionnel, sauf si la gestion d'abonnement est activé
			if( trim($data_tenant['cpt-admin']['email']) != '' ){
				// Connexion à la base de se locataire et chargement de sa configuration minimal par son site par défaut
				RegisterGCPConnection::init( $tnt_id, true, true );

				// Création de l'administrateur dans le registre
				$reg_admin = new Administrator();

				$reg_admin->setEmail($data_tenant['cpt-admin']['email'])
									->setCivility($data_tenant['cpt-admin']['civility'])
									->setFirstname($data_tenant['cpt-admin']['firstname'])
									->setLastname($data_tenant['cpt-admin']['lastname'])
									->setLang('fr_FR')
									->setWelcome('true')
									->setPassword($data_tenant['cpt-admin']['password'])
									->addTenantAccess($tnt_id);

				if( !$reg_admin->save(false) ){
					throw new Exception('Une erreur est survenue lors de l\'ajout du compte administrateur.');
				}

				$usr_admin = gu_users_add_with_adresse( $data_tenant['cpt-admin']['email'], 3, $data_tenant['cpt-admin']['civility'], $data_tenant['cpt-admin']['firstname'], $data_tenant['cpt-admin']['lastname'], null, PRF_ADMIN, '', false, 0, 0, null, false, '', '',$data_tenant['address1'], $data_tenant['address2'], $data_tenant['zipcode'], $data_tenant['city'], '', $data_tenant['cpt-admin']['phone'] );
				if( $usr_admin === false ){
					throw new Exception('Une erreur est survenue lors de l\'ajout du compte administrateur.');
				}

				// Ce compte doit aussi être créé dans RiaStudio si l'abonnement est activé
				// Et que ce dernier n'existe pas encore
				if( $data_yuto['package'] !== 'none' && $data_yuto['abo-active'] ){
					RegisterGCPConnection::init( 52, true );

					$r_user = gu_users_get( 0, $data_tenant['cpt-admin']['email'] );
					if( $r_user && ria_mysql_num_rows($r_user) ){
						$user = ria_mysql_fetch_assoc( $r_user );
						$usr_in_ria = $user['id'];
					}else{
						$usr_in_ria = gu_users_add_with_adresse( $data_tenant['cpt-admin']['email'], 3, $data_tenant['cpt-admin']['civility'], $data_tenant['cpt-admin']['firstname'], $data_tenant['cpt-admin']['lastname'], null, PRF_CUST_PRO, '', false, 0, 0, null, false, '', '',$data_tenant['address1'], $data_tenant['address2'], $data_tenant['zipcode'], $data_tenant['city'], '', $data_tenant['cpt-admin']['phone'] );

						if( !is_numeric($usr_in_ria) || $usr_in_ria <= 0 ){
							throw new Exception('Impossible de créer le compte client dans RiaStudio.');
						}
					}

					// On relie l'abonnement Yuto au compte client juste créé
					$res = ria_mysql_query('
						update dev_subscribtions
						set ds_usr_id = '.$usr_in_ria.'
						where ds_tnt_id = '.$tnt_id.'
							and ds_type_sub = "yuto"
					');

					// Reconnexion à la base du locataire
					RegisterGCPConnection::init( $tnt_id, true, true );

					if( !$res ){
						throw new Exception('Impossible de lier le compte administrateur à l\'abonnement Yuto.');
					}
				}

			}

			return true;
		}
	}

	/// @}
