<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/distribution.proto

namespace Google\Api\Servicecontrol\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Servicecontrol\V1\Distribution\LinearBuckets instead.
     * @deprecated
     */
    class Distribution_LinearBuckets {}
}
class_exists(Distribution\LinearBuckets::class);
@trigger_error('Google\Api\Servicecontrol\V1\Distribution_LinearBuckets is deprecated and will be removed in the next major release. Use Google\Api\Servicecontrol\V1\Distribution\LinearBuckets instead', E_USER_DEPRECATED);

