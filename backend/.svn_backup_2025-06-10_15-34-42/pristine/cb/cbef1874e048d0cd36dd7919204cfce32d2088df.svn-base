var current_ajax_request = false;

$(document).ready(
	function(){
		if( typeof $('#tag_desc') != undefined && $('#tag_desc').length ){
			$('#tag_desc').riametas({ padding: true, type : "desc" });
			$('#tag_title').riametas({ padding: true, type : "title" });
			$('#tag_title_ref').riametas({ type : "title" });
			$('#tag_desc_ref').riametas({ type : "desc" });
		}
		if ($('#faq_categories').length) {
			riaSortable.create({
				'table': $('#faq_categories'),
				'url': '/admin/ajax/tools/ajax-position-update.php'
			});
		}
		if ($('#faq_questions').length) {
			riaSortable.create({
				'table': $('#faq_questions'),
				'url': '/admin/ajax/tools/ajax-position-question-update.php'
			});
		}
	}
);

// Gère la sélection/désélection des images
function previewClick(preview) {
	// Change la couleur de la bordure de l'image
	preview.style.borderColor = preview.style.borderColor == '' ? '#4574BF' : '';
	var chk = preview.getElementsByTagName('input');
	chk[0].checked = preview.style.borderColor != '';
}

function validFaqCategoryForm(frm){
	if($("input[name='name']",frm).length>0) {
		if( !trim(frm.name.value) ){
			alert(faqAlertChampsCategorie);
			frm.name.focus();
			return false;
		}
	}
}
function validFaqQuestionForm(frm){
	if($("input[name='name']",frm).length>0) {
		if( !trim(frm.name.value) ){
			alert(faqAlertChampsIntitule);
			frm.name.focus();
			return false;
		}
		if( !trim(frm.desc.value) ){
			alert(faqAlertChampsReponse);
			frm.desc.focus();
			return false;
		}
	}
}
function confirmFaqCategoryDel(frm){
	if( parseInt(frm.questions.value)>0 ){
		alert(faqAlertSuppressionCategorie);
		return false;
	}
}
function confirmFaqCategoryDelList(){
	return window.confirm(faqConfirmSuppressionCategorieListe);
}
function confirmFaqQuestionDelList(){
	return window.confirm(faqConfirmsuppressionQuestionListe);
}
function confirmFaqQuestionDel(){
	return window.confirm(faqConfirmsuppressionQuestion);
}
function cancelFaqCategory(){
	// Annule les modifications sur une catégorie de question (en retournant à la liste des catégories)
	window.location.href = 'index.php';
	return false;
}
function cancelFaqQuestion(frm){
	// Annule les modifications sur une question (en retournant au formulaire de catégorie)
	var parEx = /cat=([0-9]+)/;
	var result = parEx.exec( frm.action );
	window.location.href = 'category.php?cat=' + result[1];
	return false;
}