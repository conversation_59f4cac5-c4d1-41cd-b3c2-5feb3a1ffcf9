<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/schema.proto

namespace Google\Cloud\PubSub\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\PubSub\V1\Schema\Type instead.
     * @deprecated
     */
    class Schema_Type {}
}
class_exists(Schema\Type::class);
@trigger_error('Google\Cloud\PubSub\V1\Schema_Type is deprecated and will be removed in the next major release. Use Google\Cloud\PubSub\V1\Schema\Type instead', E_USER_DEPRECATED);

