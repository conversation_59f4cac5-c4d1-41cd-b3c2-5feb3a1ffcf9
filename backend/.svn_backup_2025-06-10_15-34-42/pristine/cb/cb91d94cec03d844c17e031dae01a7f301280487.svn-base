<?php 
/**
 * \defgroup orders_signature Signature 
 * \ingroup orders 
 * @{	
 * \page api-orders-signature-add Ajout 
 *
 *	 Cette fonction ajoute une signature à une commande
 *
 *		\code
 *			POST /orders/signature/
 *		\endcode
 *	
 * @param $ord Obligatoire, Identifiant de la commande
 * @param $signature Obligatoire, signature au format text
 * @param int $usr_id Facultatif, identifiant de l'utilisateur signataire
 * @param string $firstname Facultatif, prénom du signataire
 * @param string $lastname Facultatif, nom du signataire
 * @param $function Facultatif, fonction / poste du signataire dans la société
 *	
 * @return true si l'ajout s'est déroulé avec succès 
 * @}
*/ 

switch( $method ){
	case 'add': 
		
		if( !isset($_REQUEST['ord'], $_REQUEST['signature']) ){
			throw new Exception("Paramètre invalide.");
		}

		$usr_id = null;
		if( isset($_REQUEST['usr_id']) && is_numeric($_REQUEST['usr_id']) ){
			$usr_id = $_REQUEST['usr_id'];
		}

		$firstname = null;
		if( isset($_REQUEST['firstname']) && $_REQUEST['firstname'] != "" ){
			$firstname = $_REQUEST['firstname'];
		}

		$lastname = null;
		if( isset($_REQUEST['lastname']) && $_REQUEST['lastname'] != "" ){
			$lastname = $_REQUEST['lastname'];
		}
		$function = null;
		if( isset($_REQUEST['function']) && $_REQUEST['function'] != "" ){
			$function = $_REQUEST['function'];
		}

		if( ord_orders_signature_add( $_REQUEST['ord'], $_REQUEST['signature'], $usr_id, $firstname, $lastname, $function ) ){
			$result = true;
		}

		break;
}
