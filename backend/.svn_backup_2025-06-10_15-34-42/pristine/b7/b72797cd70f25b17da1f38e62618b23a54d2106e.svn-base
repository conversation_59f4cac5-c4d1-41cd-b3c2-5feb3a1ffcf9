<?php

/** \file AmazonMWSRequest.php
 * Cette classe sert d'interface pour créer des requêtes MWS.
 *
 * @see AmazonMWS
 */

require_once __DIR__ . '/AmazonMWS.php';

/** \class AmazonMWSRequest
 *
 * \property AmazonMWS $client
 * \property array     $params           Les paramètres de la requête
 * \property mixed     $request
 * \property string    $merchantIdField  Le nom du champ "Merchant" (Merchant ou SellerId)
 */
abstract class AmazonMWSRequest
{
	protected $amazon;

	protected $params;

	protected $request;

	protected $merchantIdField;

	/** Créer une nouvelle instance de la classe AmazonMWSRequest.
	 *
	 * \param  AmazonMWS $client
	 * \param  array     $params
	 * \return void
	 */
	public function __construct(AmazonMWS $amazon, array $params = array()) {
		if( !$this->merchantIdField ){
			throw new InvalidArgumentException('The $merchantIdField property must be defined.');
		}

		$this->amazon = $amazon;
		$this->params = $params;

		$this->build();

		$this->request->{$this->merchantIdField} = $this->amazon->getMerchantId();
	}

	/** Construit la requête avec les différents paramètres.
	 *
	 * \return void
	 */
	abstract public function build();

	/** Envoie la requête à l'API Amazon MWS.
	 *
	 * \return mixed Le résultat de la requête
	 */
	abstract public function send();
}