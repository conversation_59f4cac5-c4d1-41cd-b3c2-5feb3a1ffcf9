/* Personalisations des champs standards */
/* Inputs */
input[type="color"],
input[type="date"],
input[type="datetime-local"],
input[type="email"],
input[type="month"],
input[type="number"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="text"],
input[type="time"],
input[type="url"],
input[type="week"],
select,
textarea {
	background-color: $white;
	font-family: 'Montserrat', sans-serif;
	font-size: 12px;
	font-weight: 500;
	width: 400px;
	max-width: 400px;
	border: 1px solid $grey-medium-color;
	border-radius: 6px;
	margin-top: 2px;
	margin-bottom: 2px;
	:not(dd select) {
		vertical-align: top;
	}
	padding: 0 4px;

	@include media('<medium') {
		width: 100%;
	}

	thead &, tfoot & {
		width: auto;
	}
	&:hover {
		border: 1px solid $dark-color;
	}
	&:focus:not(.mdc-text-field__search-input) {
		outline: 0 none;
		border: 1px solid $dark-color;
		box-shadow: 0 0 1px 1px $medium-color;
	}
	&.input-edit-success {
		border: 1px solid #7fcc7f;
		box-shadow: 0px 0px 3px #7fcc7f;
	}
	&.input-edit-error {
		border: 1px solid #f99494;
		box-shadow: 0px 0px 3px #f99494;
	}
	&:disabled {
		background-color: $grey-color;
	}
	&:read-only {
		background-color: lighten($grey-color, 4%);
	}
}

// #popup-content {

// 	select{
// 		@include media('<medium') {
// 			width: auto;
// 		}
// 	}
// }

dd {
	input[type="color"],
	input[type="date"],
	input[type="datetime-local"],
	input[type="email"],
	input[type="month"],
	input[type="password"],
	input[type="search"],
	input[type="tel"],
	input[type="text"],
	input[type="time"],
	input[type="url"],
	input[type="week"],
	select,
	textarea {
		width: auto !important;
		max-width: 100%;
	}
}
#devis-logo-disposition {
	width: auto !important;
}
#devis-font-size, #inv-font-size {
	width: 145px;
}

tr > td > input:not([type="submit"]):not([type="button"]):not([type="image"]):only-child ,
div:not(#popup-content) tr > td > select:only-child {
	width: 100%;
}


label + input[type='text'], label + input[type='number'], dd select, input.small {
	vertical-align: middle !important;
}



/* #mdc-input:focus, #mdc-text-field__icon:focus, #ord-ref:focus {
 	border: none !important;
 	box-shadow: none !important;
 } */
input[type="file"]::-ms-value {
	background-color: $white;
	font-family: 'Montserrat', sans-serif;
	font-size: 12px;
	font-weight: 500;
	width: 100%;
	max-width: 400px;
	border: 1px solid $grey-medium-color;
	border-radius: 6px;
	margin-top: 2px;
	margin-bottom: 2px;
	vertical-align: middle;
	padding: 0 4px;
	&:hover {
		border: 1px solid $dark-color;
	}
	&:focus {
		outline: 0 none;
		border: 1px solid $dark-color;
		box-shadow: 0 0 1px 1px $medium-color;
	}
	&.input-edit-success {
		border: 1px solid #7fcc7f;
		box-shadow: 0px 0px 3px #7fcc7f;
	}
	&.input-edit-error {
		border: 1px solid #f99494;
		box-shadow: 0px 0px 3px #f99494;
	}
	&:read-only {
		background-color: lighten($grey-color, 10%);
	}
	&:disabled {
		background-color: $grey-color;
	}
}

input:not([type="checkbox"]):not([type="radio"]):not([type="image"]):not(.mdc-text-field__input), select:not([multiple]) {
	height: 24px;
}
input[type="file"]::-ms-value {
	height: 24px;
	box-sizing: border-box;
}

input[type="text"]{
	&.date {
		min-width: auto;
	}
	// @include media('>large') {
	// 	min-width: 435px
	// }
}

/* Textarea */
textarea {
	padding: 4px;
	max-width: 100%;
	&#desc {
		min-height: 100px;
	}
}

/* Select */
select{
	&:read-only {
		background-color: lighten($grey-color, 10%);
	}
	&:disabled {
		background-color: $grey-color;
	}
	// &[readonly] {
	// 	background-color: $grey-color;
	// }
	&:not([multiple]) {
		background-image: url('/admin/dist/images/input-select.svg');
		background-position: right -1px top -4px;
		background-repeat: no-repeat;
		background-size: 29px 29px;
		appearance: none;
		padding-right: 30px !important;
		&::-ms-expand {
			display: none;
		}
	}
}
/* Boutons */
input[type="button"]:not(.cdt-grp-del),
input[type="reset"],
input[type="submit"],
button,
.button {
	display: inline-block;
	font-family: 'Montserrat', sans-serif;
	font-weight: 500;
	cursor: pointer;
	border: 1px solid $dark-color;
	color: $dark-color;
	background-color: #fff;
	padding: 3px 8px;
	border-radius: 6px;
	text-decoration: none;
	font-style: normal;
	appearance: none;
	margin-top: 2px;
	margin-bottom: 2px;
	vertical-align: middle;
	min-height: 24px;
  	&:hover {
    	border-color: $medium-color;
   		background-color: $medium-color;
		color: $white;
		text-decoration: none !important;
	}
	&:disabled {
		cursor: default;
		background-color: $grey-color !important;
		color: $grey-medium-color !important;
		border-color: $grey-medium-color !important;
		&:hover {
			background-color: $grey-color;
			color: $grey-medium-color;
			border-color: $grey-medium-color;
		}
	}
	&:focus {
		outline: 0 none;
		border: 1px solid $dark-color;
		box-shadow: 0 0 1px 1px $medium-color;
	}
}
input[type="button"]:not(.cdt-grp-del),
input[type="reset"],
input[type="submit"],
button,
.button{

	&.button-secondary {
		border-color: $medium-color;
		background-color: $medium-color;
		color: $white;
		&:hover {

		}
	}
}

/* Exception au focus pour les inputs du sous-menu de commandes */
#site-submenu {
	.ria-admin-ui-searchbar {
		input, button {
			&:focus{
				border: none;
				box-shadow : none;
			}
		}

	}
}

/* Number */
input[type="number"] {
	width: 50px;
}

/* Checkbox, Radio */
input[type="checkbox"],
input[type="radio"] {
	background-color: $white;
	border: 1px solid $grey-medium-color;
	vertical-align: middle;
	appearance: none;
	width: 14px !important;
	height: 14px;
	display: inline-block;
	&:read-only {
		background-color: lighten($grey-color, 10%);
	}
	&:disabled {
		background-color: $grey-color;
	}
	&:hover {
		border: 1px solid $dark-color;
	}
	&:focus {
		outline: 0 none;
		border: 1px solid $dark-color;
		box-shadow: 0 0 1px 1px $medium-color;
	}
}
input[type="checkbox"] {
	border-radius: 3px;
	&:checked {
		background-image: url('/admin/dist/images/checkbox-checked.svg');
	}
	&:indeterminate {
		background-clip: content-box;
		padding: 2px;
		background-color: $medium-color;
	}
}
input[type="radio"] {
	border-radius: 100%;
	&:checked {
		background-image: radial-gradient(ellipse at center, $medium-color 50%, $white 50%);
	}
}
/* == Input file == */
/* Surcharge pour moteur de rendu Webkit, Blink */
input[type="file"] {
	font-family: 'Montserrat', sans-serif;
	&::-webkit-file-upload-button {
		font-family: 'Montserrat', sans-serif;
		font-weight: 500;
		cursor: pointer;
		border: 1px solid $dark-color;
		color: $dark-color;
		background-color: #fff;
		padding: 3px 8px;
		border-radius: 6px;
		text-decoration: none;
		font-style: normal;
		appearance: none;
		vertical-align: middle;
		min-height: 24px;
		&:hover {
			border-color: $medium-color;
			background-color: $medium-color;
			color: $white;
			text-decoration: none;
		}
		&:disabled {
			cursor: default;
			background-color: $grey-color;
			&:hover {
				background-color: $grey-color;
				color: $dark-color;
			}
		}
		&:focus {
			outline: 0 none;
			border: 1px solid $dark-color;
			box-shadow: 0 0 1px 1px $medium-color;
		}
	}
}
/* Surcharge pour moteur de rendu Edge */
input[type="file"] {
	&::-ms-browse {
		font-family: 'Montserrat', sans-serif;
		font-weight: 500;
		cursor: pointer;
		border: 1px solid $dark-color;
		color: $dark-color;
		background-color: #fff;
		padding: 3px 8px;
		border-radius: 6px;
		text-decoration: none;
		font-style: normal;
		appearance: none;
		vertical-align: middle;
		min-height: 24px;
		&:hover {
			border-color: $medium-color;
			background-color: $medium-color;
			color: $white;
			text-decoration: none;
		}
		&[disabled] {
			cursor: default;
			background-color: $grey-color;
			&:hover {
				background-color: $grey-color;
				color: $dark-color;
			}
		}
		&:focus {
			outline: 0 none;
			border: 1px solid $dark-color;
			box-shadow: 0 0 1px 1px $medium-color;
		}
	}
}


#site-content {
	fieldset div input:not(.cdt-grp-del),
	fieldset div textarea,
	#tabpanel-vertical div input,
	#tabpanel-vertical div textarea {
		// width: 345px;
		display: inline-block;
	}

}

label {
	vertical-align: middle;
	cursor: default;
}

/* pour les éléments de type label > input + texteLabel + input */
.nested-inputs {
	display: flex;
	align-items: center;
	@include media('<medium') {
		display: inline-block;
	}
	& > *  {
		margin-right: 5px;
	}
	/* & > input:first-child {
		flex: 1 1 auto;
	} */
	& > input:last-child {
		margin-left: 5px;
	}
}


input.icon-del-cat {
	vertical-align: middle;
	margin-top: -3px;
}

.cdt-grp-legend {
	label{
		display: inline-block;
	}
	input {
		display: inline-block !important;
	}
}

#site-content .links .cnt-infos .del-link {
    float: left;
    height: 23px;
    width: 23px;
    margin-right: 5px;
}

.little_input {
	width: 150px;
}

.auto_input {
	width: auto;
}

.rec-new-fld {
	width: 220px;
}