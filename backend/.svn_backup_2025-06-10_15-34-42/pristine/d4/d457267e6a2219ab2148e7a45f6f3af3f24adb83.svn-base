<?php
/**
 * \defgroup api-rewards Points de fidelité
 * \ingroup pim 
 * \defgroup api-rewards-products Produit
 * \ingroup api-rewards
 * @{
 *
 * \page api-rewards-products-add Ajout/Mise à jour 
 *
 * Cette fonction permet l'ajout ( ou la mise à jour ) de points de fidelité sur un produit
 *
 *		\code
 *			POST /rewards/products/
 *		 ou
 *			PUT /rewards/products/
 *		\endcode
 *
 * @param raw_data Obligatoire, Donnée en json_decode
 *		\code{.json}
 *			{
 *					"prd" : (obligatoire) identifiant du produit
 *					"prf" : (obligatoire) identifiant du profil client
 *					"pts" : nombre de points 
 *			}
 *		\endcode
 *
 * @return json, une liste avec les éléments traité sous la forme suivante :
 *		\code{.json}
 *			{
 *					"prd" : identifiant du produit
 *					"prf" : identifiant du profil client
 *					"pts" : nombre de points 
 *			}
 *		\endcode
 * @}
*/

switch( $method ){
	case 'upd': 
	case 'add': 
		if( !is_array($objs) ){
			throw new Exception("Paramètres invalide");
		}

		$content = array();
		foreach($objs as $stats){

			$prd = isset($stats['prd']) && is_numeric($stats['prd']) ? $stats['prd'] : 0;
			$prf = isset($stats['prf']) && is_numeric($stats['prf']) ? $stats['prf'] : 0;
			$pts = isset($stats['pts']) && is_numeric($stats['pts']) ? $stats['pts'] : 0;

			$data_for_response = array(
					"prd" => $prd,
					"prf" => $prf,
					"pts" => $pts
				);

			if( !$prf ){
				throw new Exception("Paramètres invalide le profile est obligatoire.");
			}
			if( !$prd ){
				throw new Exception("Paramètres invalide le produits est obligatoire.");
			}

			if (!rwd_prd_rewards_exists($prd, $prf)) {
				// ajout
				if( $pts!=0 && !rwd_prd_rewards_add($prd, $prf, $pts, null, null ) ){
					throw new Exception("Erreur lors de l'ajout des pts de fidelité sur le produit pour : ".print_r($data_for_response, true));
				}
			}else{
				// mise à jour
				if( !rwd_prd_rewards_update( $prd, $prf, $pts ) ){
					throw new Exception("Erreur lors de la mise à jours des pts de fidelité sur le produit pour : ".print_r($data_for_response, true));
				}
			}

			$content[] = $data_for_response;

		}
		$result = true;
		break;
}
