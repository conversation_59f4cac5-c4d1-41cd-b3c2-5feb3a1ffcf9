<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  3567210 => 'GO Mobile',
  35677 => 'Melita Mobile',
  35679 => 'GO Mobile',
  3569210 => 'Vodafone',
  3569211 => 'Vodafone',
  3569231 => 'Vodafone',
  3569696 => 'YOM',
  3569697 => 'YOM',
  3569811 => 'Redtouch Fone',
  3569812 => 'Redtouch Fone',
  3569813 => 'Redtouch Fone',
  3569889 => 'GO Mobile',
  3569897 => 'Vodafone',
  35699 => 'Vodafone',
);
