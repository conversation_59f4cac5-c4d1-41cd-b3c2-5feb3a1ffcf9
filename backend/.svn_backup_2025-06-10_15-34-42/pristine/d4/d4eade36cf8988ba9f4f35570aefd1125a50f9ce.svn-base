<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

	if (!isset($_GET['usr']) || !is_numeric($_GET['usr']) || $_GET['usr'] <= 0) {
		$g_error = _("Un ou plusieurs paramètres obligatoires permettant le chargement des informations sont manquants.");
	}

	if(isset($_GET['reward']) && $_GET['reward'] != 0){
		$title=_("Modifier des points de fidélité");
	}else{
		$title=_("Ajouter des points de fidélité");
	}


	if( isset($_POST['save-reward']) ){
		if( !isset($_POST['rwd-name'], $_POST['rwd-pts'], $_POST['rwd-limit']) ){
			$error = _("Une ou plusieurs informations sont manquantes.");
		}elseif( trim($_POST['rwd-name'])=='' ){
			$error = _("Veuillez renseigner une désignation.");
		}elseif( !is_numeric($_POST['rwd-pts']) || $_POST['rwd-pts'] == 0 ){
			$error = _("Veuillez renseigner un nombre de points supérieur/inférieur à zéro.");
		}elseif(isset($_POST['rwd-limit']) && $_POST['rwd-limit'] != "" ) {
			if(isdate($_POST['rwd-limit'])){
				$date = dateparse($_POST['rwd-limit']);
				if( strtotime($date)<time()){
					$error = sprintf(_("Veuillez renseigner une date limite supérieur à aujourd'hui au format JJ/MM/AAAA, exemple : 31/12/%d."), (date('Y')+1) );
				}
			}else{
				$error = _("Le format de la date n'est pas valide");
			}
		}else{
			$date = 0;
		}

		if(!isset($error)){
			if($_POST['rwd-pts'] < 0){
				$_POST['rwd-pts'] = $_POST['rwd-pts']*-1;
				$cancel = true;
			}else{
				$cancel = false;
			}
			if($_GET['reward'] != 0){
				$update = stats_reward_update( $_GET['reward'], $_POST['rwd-name'], $_POST['rwd-pts'], $date,$cancel );
				if( !$update){
					$error = _("Une erreur inattendue s'est produite lors de la mise à jour des points de fidélité.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}else{

				if($date != 0){
					$days = round( (strtotime($date)-time()) / 86400 );
					
				}else{
					$days=0;
				}
				
				if( !stats_rewards_add( $_GET['usr'], gu_users_get_prf($_GET['usr']), 0, $cancel, $_POST['rwd-name'], 0, false, $_POST['rwd-pts'], false, false, $days) ){
					$error = _("Une erreur inattendue s'est produite lors de l'ajout des points de fidélité.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}	
			}
			
		}
	}

	if(isset($_GET['reward']) && $_GET['reward'] != 0){
		$r_reward = stats_rewards_get($_GET['usr'], 0, 0, 0, false, false, false, 0, false, 0, false, '', false, false, $_GET['reward']);
		if( !$r_reward || !ria_mysql_num_rows($r_reward) ){
			$error = _("Une erreur inattendue s'est produite lors de la récupération des points de fidélité");
		}else{
			$reward = ria_mysql_fetch_assoc( $r_reward );		
			
			$_POST['rwd-pts'] = $reward['pts'];
			$_POST['rwd-name'] = $reward['name'];
			if($reward['date_limit_en'] != ""){
				$_POST['rwd-limit'] = date("d/m/Y", strtotime($reward['date_limit_en']));
			}
			
		}
	}

	define('ADMIN_PAGE_TITLE', _('Gestion des points de fidélité'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if (isset($g_error)) {
		?>
		<div class="error"><?php print nl2br( $g_error ); ?></div>
		<?php
	}else{
?>
<form action="/admin/customers/popup-edit-rewards.php?usr=<?php print $_GET['usr']; ?>&amp;reward=<?php print $_GET['reward']; ?>" method="post">
	
	<?php
		if (isset($error)) {
			?>
			<div class="error"><?php print $error; ?></div>
			<?php	
		}
	?>
	
	<table class="checklist" cellspacing="0" cellpadding="0">
		<caption><?php print $title; ?></caption>
		<col width="300" /><col width="200" />
		<tfoot>
			<tr>
				<td colspan="2">
					<input type="submit" name="save-reward" value="<?php print _('Enregistrer')?>" />
				</td>
			</tr>
		</tfoot>
		<tbody>
			<tr>
				<td>
					<span class="mandatory">*</span>
					<label for="rwd-name"><?php print _('Désignation :'); ?></label>
				</td>
				<td>
					<input type="text" name="rwd-name" id="rwd-name" value="<?php print isset($_POST['rwd-name']) ? $_POST['rwd-name'] : ''; ?>" />
				</td>
			</tr>
			<tr>
				<td>
					<label for="rwd-pts"><span class="mandatory">*</span> <?php print _('Nombre de points :'); ?></label>
				</td>
				<td>
					<input type="text" class="date" name="rwd-pts" id="rwd-pts" value="<?php print isset($_POST['rwd-pts']) ? $_POST['rwd-pts'] : ''; ?>" />
				</td>
			</tr>
			<tr>
				<td>
					<label for="rwd-limit"><?php print _('Date limite d\'utilisation :'); ?></label>
				</td>
				<td>
					<input type="text" class="datepicker date" name="rwd-limit" id="rwd-limit" value="<?php print isset($_POST['rwd-limit']) ? $_POST['rwd-limit'] : ''; ?>" />
				</td>
			</tr>
		</tbody>
	</table>
</form>
<?php
		if (isset($_POST['save-reward']) && !isset($error)) {
			?>
				<script>
					parent.window.location.reload();
				</script>
			<?php
		}
	}

	require_once('admin/skin/footer.inc.php');
?>