<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagBase;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag ContactPoint qui complémente TagOrganisation
 */
class TagContactPoint extends TagBase {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	protected $type = "ContactPoint";

	/**
	 * Type de point de contact valid pour google
	 *
	 * @var array $validTypes
	 */
	private $validTypes = array(
		"customer service",
		"technical support",
		"billing support",
		"bill payment",
		"sales",
		"reservations",
		"credit card support",
		"emergency",
		"baggage tracking",
		"roadside assistance",
		"package tracking"
	);

	/**
	 * Constructeur de la classe
	 *
	 * @param string $contactType Type de contact correspondant a validTypes
	 */
	public function __construct($contactType="customer service"){
		$this->setContactType($contactType);
	}

	/**
	 * Permet de retourner le type de tag
	 *
	 * @return string le type de tag
	 */
	public function type(){
		return $this->type;
	}

	/**
	 * Permet d'ajouter un champ au tag
	 *
	 * @param string $name Le nom du champ
	 * @param mixed $value La valeur du champ
	 * @return self retourne l'instance
	 */
	public function addField($name, $value){
		$this->fields[$name] = $value;

		return $this;
	}

	/**
	 * Cette fonction permet de retourner la liste des champs
	 *
	 * @return array le tableau des champs
	 */
	public function getFields(){
		return $this->fields;
	}

	/**
	 * Cette fonction permet d'initialisé le champ telephone de contact
	 *
	 * @param string $phone le telephone du ContactPoint
	 * @return self retourne l'instance
	 */
	public function setPhone($phone){
		$this->addField('telephone', $this->formatPhone($phone));

		return $this;
	}

	/**
	 * Cette fonction permet d'initialisé le champ contactType du ContactPoint
	 *
	 * @param string $contactType le type du ContactPoint
	 * @return self retourne l'instance
	 */
	public function setContactType($contactType){
		if (in_array($contactType, $this->validTypes)) {
			$this->addField('contactType', $contactType);
		}

		return $this;
	}

	/**
	 * Cette fonction permet d'initialisé le champ email du ContactPoint
	 *
	 * @param string $email l'email du ContactPoint
	 * @return self retourne l'instance
	 */
	public function setEmail($email){
		$this->addField('email', $email);

		return $this;
	}

	/**
	 * Cette fonction permet d'initialisé le champ areaServed du ContactPoint
	 *
	 * @param array $areaServed Tableau de cnt code
	 * @return self retourne l'instance
	 */
	public function setAreaServed(array $areaServed){
		$this->addField('areaServed', $areaServed);

		return $this;
	}

	/**
	 * Cette fonction permet d'initialisé le champ availableLanguage du ContactPoint
	 *
	 * @param array $availableLanguage Tableau de code de langue ex : ['FR', 'EN']
	 * @return self retourne l'instance
	 */
	public function setAvailableLanguage(array $availableLanguage){
		$this->addField('availableLanguage', $availableLanguage);

		return $this;
	}
}
/// @}