<?php

	/**	\file json-search.php
	 * 	Ce fichier permet d'effectuer une recherche à l'aide du moteur de recherche interne en Ajax. Le résultat
	 * 	est fourni au format Json.
	 * 
	 * 	Les paramètres de recherche sont les suivants :
	 *  - q : termes de recherche entrés par l'utilisateur
	 * 	- type : types de contenus souhaités
	 *  - seg : identifiant de segment
	 *  - lng : langue des résultats
	 *  - wst : website sur lequel limiter la recherche
	 *  - page : page à retourner (pour les résultats contenants plusieurs pages)
	 *
	 */

	header('Content-type: text/json');
	header('Content-type: application/json');

	$for_page = 25;
	$_REQUEST['type'] = isset($_REQUEST['type']) && $_REQUEST['type']!='-1' ? array($_REQUEST['type']) : (isset($_REQUEST['seg']) && search_engines_exists($_REQUEST['seg']) ? false : array('prd-cat', 'prd', 'faq-cat', 'faq-qst', 'cgv', 'news', 'dlv-str', 'cms', 'type-doc', 'doc'));
	$_REQUEST['seg'] = isset($_REQUEST['seg']) && search_engines_exists($_REQUEST['seg']) ? $_REQUEST['seg'] : 1;
	$_REQUEST['lng'] = isset($_REQUEST['lng']) ? $_REQUEST['lng'] : $config['i18n_lng'];
	$_REQUEST['wst'] = isset($_REQUEST['wst']) ? $_REQUEST['wst'] : $config['wst_id'];
	$_REQUEST['page'] = isset($_REQUEST['page']) && is_numeric($_REQUEST['page']) && $_REQUEST['page']>0 ? $_REQUEST['page'] : 1;
	
	$search = array();
	$search['nombre'] = 0;
	$search['url_site'] = $config['site_url'];
	
	if( isset($_REQUEST['lng']) && $_REQUEST['lng']!=$config['i18n_lng'] ){
		$rurl = wst_websites_languages_get( $_REQUEST['wst'], $_REQUEST['lng'] );
		if( $rurl && ria_mysql_num_rows($rurl) ){
			$search['url_site'] = ria_mysql_result( $rurl, 0, 'url' );
		}
	}
	
	if( isset($_REQUEST['q']) && trim($_REQUEST['q']) ){
		$results = search3( $_REQUEST['seg'], $_REQUEST['q'], 1, 0, true, false, 4, $_REQUEST['type'], false, $_REQUEST['lng'] );
		
		if( $results ){
			
			
			$search['nombre'] = ria_mysql_num_rows( $results );
			
			if( $_REQUEST['page']>1 )
				ria_mysql_data_seek( $results, ($_REQUEST['page']-1)*$for_page );
			
			$count = 0;
			while( $r = ria_mysql_fetch_array($results) ){
				if( ($count++)>($for_page-1) )
					break;
				
				$info = array( 
					'img_id' => $r['img_id'],
					'name' => $r['name'],
					'desc' => $r['desc'],
					'url' => $r['url'],
					'url_alias' => $r['alt_url']
				);
				
				// traduction des contenus
				if( isset($_REQUEST['lng']) && $_REQUEST['lng']!=$config['i18n_lng'] ){
					switch( $r['type_code']	){
						case 'prd' :
							$tsk = fld_translates_get( CLS_PRODUCT, $r['tag'], $_REQUEST['lng'], $r, array(_FLD_PRD_NAME=>'name', _FLD_PRD_TITLE=>'name', _FLD_PRD_DESC=>'desc', _FLD_PRD_URL=>'url'), true ); break;
						case 'prd-cat' ;
							$tsk = fld_translates_get( CLS_CATEGORY, $r['tag'], $_REQUEST['lng'], $r, array(_FLD_CAT_NAME=>'name', _FLD_CAT_TITLE=>'name', _FLD_CAT_URL=>'desc', _FLD_PRD_URL=>'url'), true ); break;
						case 'faq-cat' :
							$tsk = fld_translates_get( CLS_FAQ_CAT, $r['tag'], $_REQUEST['lng'], $r, array(_FLD_FAQ_CAT_NAME=>'name', _FLD_FAQ_CAT_DESC=>'desc', _FLD_FAQ_CAT_URL=>'url'), true ); break;
						case 'faq-qst' :
							$tsk = fld_translates_get( CLS_FAQ_QST, $r['tag'], $_REQUEST['lng'], $r, array(_FLD_FAQ_QST_NAME=>'name', _FLD_FAQ_QST_DESC=>'desc', _FLD_FAQ_QST_URL=>'url'), true ); break;
						case 'news' :
							$tsk = fld_translates_get( CLS_NEWS, $r['tag'], $_REQUEST['lng'], $r, array(_FLD_NEWS_NAME=>'name', _FLD_NEWS_DESC=>'desc', _FLD_NEWS_URL=>'url'), true ); break;
						case 'dlv-str' :
							$tsk = fld_translates_get( CLS_STORE, $r['tag'], $_REQUEST['lng'], $r, array(_FLD_STR_NAME=>'name', _FLD_STR_DESC=>'desc', _FLD_STR_URL=>'url'), true ); break;
						case 'cms' :
							$tsk = fld_translates_get( CLS_CMS, $r['tag'], $_REQUEST['lng'], $r, array(_FLD_CMS_NAME=>'name', _FLD_CMS_DESC=>'desc', _FLD_CMS_URL=>'url'), true ); break;
						case 'brd' :
							$tsk = fld_translates_get( CLS_BRAND, $r['tag'], $_REQUEST['lng'], $r, array(_FLD_BRD_NAME=>'name', _FLD_BRD_TITLE=>'name', _FLD_BRD_DESC=>'desc', _FLD_BRD_URL=>'url'), true ); break;
						default :
							break;
					}
					
					$info['name'] = isset($tsk['name']) ? $tsk['name'] : $info['name'];
					$info['desc'] = isset($tsk['desc']) ? $tsk['desc'] : $info['desc'];
					$info['url'] = isset($tsk['url']) ? $tsk['url'] : $info['url'];
				}
				
				$img_size = $config['img_sizes']['small'];
				$img = $config['img_url'].'/'.$img_size['dir'].'/'.($info['img_id'] ? $info['img_id'] : 'default').'.'.$img_size['format'];
				
				$desc = $info['desc'];
				if( strlen($desc)>105 )
					$desc = mb_substr( $desc, 0, 102, 'UTF8' ).'...';
				
				$search['search'][] = array(
					'img' => $img,
					'name' => htmlspecialchars( $info['name'] ),
					'desc' => htmlspecialchars( $desc ),
					'url' => $info['url'],
					'url_alias' => $info['url_alias'],
					'type_name' => htmlspecialchars($r['type_name'])
				);
			}
			
		}
	}
	
	print json_encode( $search );

