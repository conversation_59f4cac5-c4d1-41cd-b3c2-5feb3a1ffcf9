Scalar type declarations
-----
<?php
function test(bool $a, Int $b, FLOAT $c, StRiNg $d, iterable $e, object $f) : void {}
-----
!!php7
array(
    0: Stmt_Function(
        byRef: false
        name: test
        params: array(
            0: Param(
                type: bool
                byRef: false
                variadic: false
                name: a
                default: null
            )
            1: Param(
                type: int
                byRef: false
                variadic: false
                name: b
                default: null
            )
            2: Param(
                type: float
                byRef: false
                variadic: false
                name: c
                default: null
            )
            3: Param(
                type: string
                byRef: false
                variadic: false
                name: d
                default: null
            )
            4: Param(
                type: iterable
                byRef: false
                variadic: false
                name: e
                default: null
            )
            5: Param(
                type: object
                byRef: false
                variadic: false
                name: f
                default: null
            )
        )
        returnType: void
        stmts: array(
        )
    )
)