# CatalogColumn

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ignored** | **bool** | IF true, the product values of this column will be not taken in account during the importation process | [optional] [default to false]
**duplicate_product_value_configuration** | [**\Swagger\Client\Model\DuplicateProductValueConfiguration**](DuplicateProductValueConfiguration.md) |  | [optional] 
**id** | [**\Swagger\Client\Model\ColumnId**](ColumnId.md) |  | 
**catalog_column_name** | [**\Swagger\Client\Model\CatalogColumnName**](CatalogColumnName.md) |  | 
**user_colum_name** | [**\Swagger\Client\Model\UserColumName**](UserColumName.md) |  | 
**configuration** | [**\Swagger\Client\Model\ColumnConfiguration**](ColumnConfiguration.md) |  | 
**links** | [**\Swagger\Client\Model\CatalogColumnLinks**](CatalogColumnLinks.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


