<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/schema.proto

namespace Google\Cloud\PubSub\V1;

use UnexpectedValueException;

/**
 * Possible encoding types for messages.
 *
 * Protobuf type <code>google.pubsub.v1.Encoding</code>
 */
class Encoding
{
    /**
     * Unspecified
     *
     * Generated from protobuf enum <code>ENCODING_UNSPECIFIED = 0;</code>
     */
    const ENCODING_UNSPECIFIED = 0;
    /**
     * JSON encoding
     *
     * Generated from protobuf enum <code>JSON = 1;</code>
     */
    const JSON = 1;
    /**
     * Binary encoding, as defined by the schema type. For some schema types,
     * binary encoding may not be available.
     *
     * Generated from protobuf enum <code>BINARY = 2;</code>
     */
    const BINARY = 2;

    private static $valueToName = [
        self::ENCODING_UNSPECIFIED => 'ENCODING_UNSPECIFIED',
        self::JSON => 'JSON',
        self::BINARY => 'BINARY',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

