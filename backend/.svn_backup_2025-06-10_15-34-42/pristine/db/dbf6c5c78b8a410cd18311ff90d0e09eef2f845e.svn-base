<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/admin/v1/iam.proto

namespace Google\Iam\Admin\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * The request to get all roles defined under a resource.
 *
 * Generated from protobuf message <code>google.iam.admin.v1.ListRolesRequest</code>
 */
class ListRolesRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * The `parent` parameter's value depends on the target resource for the
     * request, namely
     * [`roles`](/iam/reference/rest/v1/roles),
     * [`projects`](/iam/reference/rest/v1/projects.roles), or
     * [`organizations`](/iam/reference/rest/v1/organizations.roles). Each
     * resource type's `parent` value format is described below:
     * * [`roles.list()`](/iam/reference/rest/v1/roles/list): An empty string.
     *   This method doesn't require a resource; it simply returns all
     *   [predefined roles](/iam/docs/understanding-roles#predefined_roles) in
     *   Cloud IAM. Example request URL:
     *   `https://iam.googleapis.com/v1/roles`
     * * [`projects.roles.list()`](/iam/reference/rest/v1/projects.roles/list):
     *   `projects/{PROJECT_ID}`. This method lists all project-level
     *   [custom roles](/iam/docs/understanding-custom-roles).
     *   Example request URL:
     *   `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles`
     * * [`organizations.roles.list()`](/iam/reference/rest/v1/organizations.roles/list):
     *   `organizations/{ORGANIZATION_ID}`. This method lists all
     *   organization-level [custom roles](/iam/docs/understanding-custom-roles).
     *   Example request URL:
     *   `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
     * Note: Wildcard (*) values are invalid; you must specify a complete project
     * ID or organization ID.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.resource_reference) = {</code>
     */
    private $parent = '';
    /**
     * Optional limit on the number of roles to include in the response.
     *
     * Generated from protobuf field <code>int32 page_size = 2;</code>
     */
    private $page_size = 0;
    /**
     * Optional pagination token returned in an earlier ListRolesResponse.
     *
     * Generated from protobuf field <code>string page_token = 3;</code>
     */
    private $page_token = '';
    /**
     * Optional view for the returned Role objects. When `FULL` is specified,
     * the `includedPermissions` field is returned, which includes a list of all
     * permissions in the role. The default value is `BASIC`, which does not
     * return the `includedPermissions` field.
     *
     * Generated from protobuf field <code>.google.iam.admin.v1.RoleView view = 4;</code>
     */
    private $view = 0;
    /**
     * Include Roles that have been deleted.
     *
     * Generated from protobuf field <code>bool show_deleted = 6;</code>
     */
    private $show_deleted = false;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $parent
     *           The `parent` parameter's value depends on the target resource for the
     *           request, namely
     *           [`roles`](/iam/reference/rest/v1/roles),
     *           [`projects`](/iam/reference/rest/v1/projects.roles), or
     *           [`organizations`](/iam/reference/rest/v1/organizations.roles). Each
     *           resource type's `parent` value format is described below:
     *           * [`roles.list()`](/iam/reference/rest/v1/roles/list): An empty string.
     *             This method doesn't require a resource; it simply returns all
     *             [predefined roles](/iam/docs/understanding-roles#predefined_roles) in
     *             Cloud IAM. Example request URL:
     *             `https://iam.googleapis.com/v1/roles`
     *           * [`projects.roles.list()`](/iam/reference/rest/v1/projects.roles/list):
     *             `projects/{PROJECT_ID}`. This method lists all project-level
     *             [custom roles](/iam/docs/understanding-custom-roles).
     *             Example request URL:
     *             `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles`
     *           * [`organizations.roles.list()`](/iam/reference/rest/v1/organizations.roles/list):
     *             `organizations/{ORGANIZATION_ID}`. This method lists all
     *             organization-level [custom roles](/iam/docs/understanding-custom-roles).
     *             Example request URL:
     *             `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
     *           Note: Wildcard (*) values are invalid; you must specify a complete project
     *           ID or organization ID.
     *     @type int $page_size
     *           Optional limit on the number of roles to include in the response.
     *     @type string $page_token
     *           Optional pagination token returned in an earlier ListRolesResponse.
     *     @type int $view
     *           Optional view for the returned Role objects. When `FULL` is specified,
     *           the `includedPermissions` field is returned, which includes a list of all
     *           permissions in the role. The default value is `BASIC`, which does not
     *           return the `includedPermissions` field.
     *     @type bool $show_deleted
     *           Include Roles that have been deleted.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Iam\Admin\V1\Iam::initOnce();
        parent::__construct($data);
    }

    /**
     * The `parent` parameter's value depends on the target resource for the
     * request, namely
     * [`roles`](/iam/reference/rest/v1/roles),
     * [`projects`](/iam/reference/rest/v1/projects.roles), or
     * [`organizations`](/iam/reference/rest/v1/organizations.roles). Each
     * resource type's `parent` value format is described below:
     * * [`roles.list()`](/iam/reference/rest/v1/roles/list): An empty string.
     *   This method doesn't require a resource; it simply returns all
     *   [predefined roles](/iam/docs/understanding-roles#predefined_roles) in
     *   Cloud IAM. Example request URL:
     *   `https://iam.googleapis.com/v1/roles`
     * * [`projects.roles.list()`](/iam/reference/rest/v1/projects.roles/list):
     *   `projects/{PROJECT_ID}`. This method lists all project-level
     *   [custom roles](/iam/docs/understanding-custom-roles).
     *   Example request URL:
     *   `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles`
     * * [`organizations.roles.list()`](/iam/reference/rest/v1/organizations.roles/list):
     *   `organizations/{ORGANIZATION_ID}`. This method lists all
     *   organization-level [custom roles](/iam/docs/understanding-custom-roles).
     *   Example request URL:
     *   `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
     * Note: Wildcard (*) values are invalid; you must specify a complete project
     * ID or organization ID.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getParent()
    {
        return $this->parent;
    }

    /**
     * The `parent` parameter's value depends on the target resource for the
     * request, namely
     * [`roles`](/iam/reference/rest/v1/roles),
     * [`projects`](/iam/reference/rest/v1/projects.roles), or
     * [`organizations`](/iam/reference/rest/v1/organizations.roles). Each
     * resource type's `parent` value format is described below:
     * * [`roles.list()`](/iam/reference/rest/v1/roles/list): An empty string.
     *   This method doesn't require a resource; it simply returns all
     *   [predefined roles](/iam/docs/understanding-roles#predefined_roles) in
     *   Cloud IAM. Example request URL:
     *   `https://iam.googleapis.com/v1/roles`
     * * [`projects.roles.list()`](/iam/reference/rest/v1/projects.roles/list):
     *   `projects/{PROJECT_ID}`. This method lists all project-level
     *   [custom roles](/iam/docs/understanding-custom-roles).
     *   Example request URL:
     *   `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles`
     * * [`organizations.roles.list()`](/iam/reference/rest/v1/organizations.roles/list):
     *   `organizations/{ORGANIZATION_ID}`. This method lists all
     *   organization-level [custom roles](/iam/docs/understanding-custom-roles).
     *   Example request URL:
     *   `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
     * Note: Wildcard (*) values are invalid; you must specify a complete project
     * ID or organization ID.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setParent($var)
    {
        GPBUtil::checkString($var, True);
        $this->parent = $var;

        return $this;
    }

    /**
     * Optional limit on the number of roles to include in the response.
     *
     * Generated from protobuf field <code>int32 page_size = 2;</code>
     * @return int
     */
    public function getPageSize()
    {
        return $this->page_size;
    }

    /**
     * Optional limit on the number of roles to include in the response.
     *
     * Generated from protobuf field <code>int32 page_size = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setPageSize($var)
    {
        GPBUtil::checkInt32($var);
        $this->page_size = $var;

        return $this;
    }

    /**
     * Optional pagination token returned in an earlier ListRolesResponse.
     *
     * Generated from protobuf field <code>string page_token = 3;</code>
     * @return string
     */
    public function getPageToken()
    {
        return $this->page_token;
    }

    /**
     * Optional pagination token returned in an earlier ListRolesResponse.
     *
     * Generated from protobuf field <code>string page_token = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setPageToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->page_token = $var;

        return $this;
    }

    /**
     * Optional view for the returned Role objects. When `FULL` is specified,
     * the `includedPermissions` field is returned, which includes a list of all
     * permissions in the role. The default value is `BASIC`, which does not
     * return the `includedPermissions` field.
     *
     * Generated from protobuf field <code>.google.iam.admin.v1.RoleView view = 4;</code>
     * @return int
     */
    public function getView()
    {
        return $this->view;
    }

    /**
     * Optional view for the returned Role objects. When `FULL` is specified,
     * the `includedPermissions` field is returned, which includes a list of all
     * permissions in the role. The default value is `BASIC`, which does not
     * return the `includedPermissions` field.
     *
     * Generated from protobuf field <code>.google.iam.admin.v1.RoleView view = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setView($var)
    {
        GPBUtil::checkEnum($var, \Google\Iam\Admin\V1\RoleView::class);
        $this->view = $var;

        return $this;
    }

    /**
     * Include Roles that have been deleted.
     *
     * Generated from protobuf field <code>bool show_deleted = 6;</code>
     * @return bool
     */
    public function getShowDeleted()
    {
        return $this->show_deleted;
    }

    /**
     * Include Roles that have been deleted.
     *
     * Generated from protobuf field <code>bool show_deleted = 6;</code>
     * @param bool $var
     * @return $this
     */
    public function setShowDeleted($var)
    {
        GPBUtil::checkBool($var);
        $this->show_deleted = $var;

        return $this;
    }

}

