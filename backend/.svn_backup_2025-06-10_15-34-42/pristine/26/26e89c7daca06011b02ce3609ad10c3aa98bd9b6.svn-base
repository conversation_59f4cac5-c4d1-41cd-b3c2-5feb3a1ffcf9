<?xml version="1.0" encoding="utf-8"?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd">
  <services>
    <service id="foo" class="Foo" />

    <service id="bar" alias="foo" class="Foo">
        <tag name="foo.bar" />
        <factory service="foobar" method="getBar" />
    </service>
  </services>
</container>
