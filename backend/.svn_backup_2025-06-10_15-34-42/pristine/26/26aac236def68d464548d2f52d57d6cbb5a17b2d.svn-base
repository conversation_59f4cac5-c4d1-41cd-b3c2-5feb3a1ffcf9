<?php

namespace Pdf;

use \DateTime;

require_once('site.inc.php');
require_once('Pdf/PieceDeVente.php');
require_once('Pdf/OrderInstallements.php');

/** \devgroup OrderPdf OrderPdf
 * \ingroup PieceDeVente
 */
class BoplanOrderPdf extends PieceDeVente {

	private $FLD_BOP_SHOW_DISCOUNT_IN_QUOTE = 101951;
	private $FLD_BOP_PAYMENT_TYPE = 101878;


	private $footer_height = 4;

	private $logo_width = 30;

	private $logo_height = 15;

	private $seller_infos = null;

	private $owner_infos = null;

	/**
	 * __construct
	 *
	 * @param array $order
	 *
	 * @return void
	 */
	public function __construct(array $order) {
		parent::__construct();
		$this->data['ord'] = $order;

		$this->owner_infos = site_owner_get();
	}

	public function bootstrap() {
		parent::bootstrap();

		$this->SetSubject(str_replace('#param[ref]#', $this->data['ord']['ref'], $this->requireOption('subject')));
		$this->SetTitle(str_replace('#param[ref]#', $this->data['ord']['ref'], $this->requireOption('name')));
	}

	public function Header() {
		// Do nothing
	}

	/**
	 * Retourne le tableau des options par défaut.
	 *
	 * @return array Tableau des options par défaut
	 */
	protected function getDefaultOptions() {
		global $config;

		$data = $deposit = array();

		// Le client peut "surcharger" la configuration de génération des devis PDF.
		// On doit alors vérifier si c'est le cas et si oui, récupérer la nouvelle configuration.
		if( $deposit_id = $this->data['ord']['dps_id'] ){
			$deposit = ria_mysql_fetch_assoc(
				prd_deposits_get($deposit_id)
			);

			if( ($quote_config = $deposit['quote_config']) ){
				$data = (array) json_decode($quote_config);
			}
		}

		return array_merge(array(
			'subject' => $config['pdf_generation_devis_subject'],
			'name' => $config['pdf_generation_devis_name'],
			'logo' => $config['pdf_generation_devis_logo'],
			'logo_disposition' => $config['pdf_generation_devis_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_devis_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_devis_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_devis_display_dlv_address'],
			'prd_reduce' => $config['pdf_generation_devis_prd_reduce'],
			'display_payment' => isset($config['pdf_generation_devis_display_payment']) ? $config['pdf_generation_devis_display_payment'] : null,
			'header' => $config['pdf_generation_devis_header'],
			'header_content' => $config['pdf_generation_devis_header_content'],
			'footer' => $config['pdf_generation_devis_footer'],
			'footer_content' => $config['pdf_generation_devis_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_devis_prd_reftruncated'],
			'prd_barcode' => $config['pdf_generation_devis_prd_barcode'],
			'prd_img' => $config['pdf_generation_devis_prd_img'],
			'font_size' => $config['pdf_generation_devis_font_size'],
			'ref' => '',
		), $data);
	}

	/**
	 * Contient le corps du pdf dans ce cas le tableau
	 *
	 * @return void
	 */
	public function body() {
		$this->getSellerInformations();
		$this->setupPageOne();
		$this->addPageTwo();
		$this->addPageThree();
		$this->addPageFour();
	}

	public function blocHeader() {
		// Do nothing
	}

	public function userInfoRow() {
		// Do nothing
	}

	public function generateTotalPage() {
		$this->requireData('ord');

		// On calcul la taille de la ligne
		$width_available = $this->w - (2 * 10.00125);

		// La cellule de droite détient la même largeur que la colonne "MONTANT HT"
		$cell_right_width = $this->table()->columns()[count($this->table()->columns()) - 1]->width();

		// La cellule de gauche prend la largeur restante
		$cell_left_width = $width_available - $cell_right_width;

		$cell_height = 10;

		// On ajoute les celulles
		$this->resetX();

		// Premiere cellule de recap
		$this->SetFont("Helvetica", "B", 11);
		$this->Cell($cell_left_width, $cell_height, $this->formatString(mb_strtoupper("total ht")), 1, 0, "R");
		$this->SetFont("Helvetica", "", 10);
		$this->Cell($cell_right_width, $cell_height, $this->formatString($this->price($this->data["ord"]["total_ht"]))." ".$this->currency, 1, 1, "C");

		// Deuxième cellule de recap
		$this->SetFont("Helvetica", "B", 11);
		$this->Cell($cell_left_width, $cell_height, $this->formatString(mb_strtoupper("tva 20%")), 1, 0, "R");
		$this->SetFont("Helvetica", "", 10);
		$this->Cell($cell_right_width, $cell_height, $this->formatString($this->price($this->taxes()->tva()["1.200"]["amount"]))." ".$this->currency, 1, 1, "C");

		// Troisième cellule de recap
		$this->SetFont("Helvetica", "B", 11);
		$this->Cell($cell_left_width, $cell_height, $this->formatString(mb_strtoupper("total ttc")), 1, 0, "R");
		$this->SetFont("Helvetica", "", 10);
		$this->Cell($cell_right_width, $cell_height, $this->formatString($this->price($this->data["ord"]["total_ttc"]))." ".$this->currency, 1, 1, "C");

		// Cellule indiquant une information
		$this->SetFont("Arial", "B", 11);
		$this->Cell($width_available, $cell_height, $this->formatString("Les fixations sont fournies avec les produits de la gamme FLEX IMPACT®"), 1, 1, "C");
	}

	public function blocFooter() {
		$this->resetX();
		$this->SetY($this->h - $this->footer_height);
		$this->SetFont("Arial", '', 6);
		$this->Multicell(0, $this->footer_height, $this->formatString('Boplan France SAS – 617 Avenue de Bayonne – 64210 Bidart – N° TVA FR45 *********** – Tél : 05 35 45 75 35 – E-mail : <EMAIL> www.boplan.com/fr'), 0, "C");
	}

	/**
	* Permet d'ajouter le logo au footer
	**/
	public function addLogoToFooter() {
		$this->SetY($this->h - $this->footer_height - $this->logo_height - 5);
		$this->SetX($this->w - 10.00125 - $this->logo_width);
		$this->Image(dirname(__FILE__)."/images/logo.jpg", $this->GetX(), $this->GetY(), $this->logo_width, $this->logo_height, "JPG");
	}

	/**
	* Permet d'ajouter le logo au footer
	**/
	public function addLogoToHeader() {
		$this->SetY(10.00125 - 1);
		$this->SetX($this->w - 10.00125 - $this->logo_width);
		$this->Image(dirname(__FILE__)."/images/logo.jpg", $this->GetX(), $this->GetY(), $this->logo_width, $this->logo_height, "JPG");
	}

	/**
	* Permet de générer le tableau aux dessus du tableau récap du devis
	*
	* @return void
	**/
	public function generateOrdersInformations() {
		global $config;

		$width_available = $this->w - (2 * 10.00125);

		// Largeur de la celulle gauche de la première ligne (contient Référence, Date et Validité de l'offre)
		// Correspond au 2/3 de la largeur possible de la page
		$cell_left_width = $width_available * (2 / 3);

		// La celulle de droite prend la place restante
		$cell_right_width = $width_available - $cell_left_width;

		// Hauteur des celulles d'en-têtes
		$header_height = 5;

		// Hauteurs des celulles de contenu
		$content_height = 15;

		$this->resetX();
		$this->setY($this->logo_width);

		// Les trois premières cellules auront une largeur de 1/3 de cell_left_width
		$this->SetFont($this->font(), "B", 12);
		if ( in_array("pdf_generation_devis_header_color", $config) && $config['pdf_generation_devis_header_color'] !== "#FFFFFF" ) {
			// On start à 1 pour éviter le #
			$red = hexdec(substr($config['pdf_generation_devis_header_color'], 1, 2));
			$green = hexdec(substr($config['pdf_generation_devis_header_color'], 3, 2));
			$blue = hexdec(substr($config['pdf_generation_devis_header_color'], 5, 2));
			$this->SetFillColor($red, $green, $blue);
		} else {
			$this->SetFillColor(255, 255, 255);
		}
		$this->Cell($cell_left_width / 3, $header_height, $this->formatString("Référence"), 1, 0, "C", true);
		$this->Cell($cell_left_width / 3, $header_height, $this->formatString("Date"), 1, 0, "C", true);
		$this->Cell($cell_left_width / 3, $header_height, $this->formatString("Validité de l'offre"), 1, 0, "C", true);

		// On ajoute maintenant l'en-tête de droite
		$this->Cell($cell_right_width, $header_height, $this->formatString("Votre interlocuteur"), 1, 1, "C", true);
		$this->resetX();

		// On ajoute maintenant les celulles de contenus pour les en-têtes
		$this->SetFont($this->font(), "", 12);
		$piece = $this->data["ord"]["piece"] === "" ? $this->data["ord"]["id"] : $this->data["ord"]["piece"];
		$this->Cell($cell_left_width / 3, $content_height, $this->formatString("DVC20".$piece), 1, 0, "C");
		$this->Cell($cell_left_width / 3, $content_height, $this->formatString(date_format(date_create($this->data["ord"]["date"]), "d/m/Y")), 1, 0, "C");
		$this->Cell($cell_left_width / 3, $content_height, $this->formatString(date_format((date_add(date_create($this->data["ord"]["date"]), date_interval_create_from_date_string('30 days'))), "d/m/Y")), 1, 0, "C");
		$interlocuteur = $this->seller_infos["adr_lastname"]."\n".$this->getSellerPhone();
		$this->MultiCell($cell_right_width, $content_height / $this->NbLines($cell_right_width, $interlocuteur), $this->formatString($interlocuteur), 1, "C");
		$this->resetX();

		// On ajoute maintenant les en-têtes suivantes qui ont chacune une largeur de 1/3 de la largeur de la page
		$this->SetFont($this->font(), "B", 12);
		$payment_conditions_width = $width_available * (2/3);
		$this->Cell($payment_conditions_width, $header_height, $this->formatString("Conditions de réglement"), 1, 0, "C", true);
		$this->Cell($width_available / 3, $header_height, $this->formatString("Code client"), 1, 1, "C", true);
		$this->resetX();

		// On ajoute maintenant les celulles de contenus pour les en-têtes
		$this->SetFont($this->font(), "", 12);
		$payment = gu_users_payment_types_get(array(intval($this->data["ord"]["usr_id"])));
		$pay_view = gu_users_payment_types_view(ria_mysql_fetch_assoc($payment));

		$payment_type = fld_object_values_get($this->data["ord"]["id"], $this->FLD_BOP_PAYMENT_TYPE);
		if ($payment_type) {
			$pay_view = $pay_view."\n".$payment_type;
		}
		$this->MultiCell($payment_conditions_width, $content_height / $this->NbLines($payment_conditions_width, $pay_view), $this->formatString($pay_view), 1, "C");
		$this->resetX();
		$this->SetXY(($payment_conditions_width) + $this->GetX(), $this->GetY() - $content_height);
		$user_ref = $this->data["user"]["ref"];
		if ($user_ref === "") {
			$user_ref = "".$this->data["ord"]["usr_id"];
		}
		$this->Cell($width_available / 3, $content_height, $this->formatString($user_ref), 1, 1, "C");
	}

	/**	Récupére les informations du commercial du devis
	 * @return	OrderExportException|void
	 */
	private function getSellerInformations() {
		$seller_id = false;

		if (isset($this->data['ord']['seller_id'])) {
			$seller_id = $this->data['ord']['seller_id'];
			$r_seller = gu_users_get(0, '', '', PRF_SELLER, '', 0, '', false, false, $seller_id);

			if (!ria_mysql_num_rows($r_seller)) {
				$seller_id = false;
			}
		}

		if (!$seller_id) {
			$seller_id = $this->data['user']['seller_id'];
			$r_seller = gu_users_get(0, '', '', PRF_SELLER, '', 0, '', false, false, $seller_id);

			if (!ria_mysql_num_rows($r_seller)) {
				$seller_id = false;
			}
		}

		if (!$seller_id || !isset($r_seller)) {
			require_once('Export/Exception/OrderExportException.php');
			throw new OrderExportException($this->data['ord']['id'], 'Commercial non trouvé');
		}
		$this->seller_infos = ria_mysql_fetch_assoc($r_seller);
	}

	/**
	 * Configure le tableau pour avoir un comportement par défaut colonne :
	 * - ref
	 * - Désignation
	 * - Quantité
	 * - Prix unitaire brut
	 * - Remise s'il y en a une
	 * - prix unitaire net
	 * - montant total ht
	 *
	 * @return \Pdf\ProductTable
	 */
	public function defaultProductTable() {
		$this->requireData('ord');
		$this->setCurrency();

		$this->table()->withRowSpacing(0)->withDrawAllBorders("T")->withRowWithSameHeight(true);

		$this->table()
			->withTbodyFontSize(10);

		$show_discount = fld_object_values_get($this->data["ord"]["id"], $this->FLD_BOP_SHOW_DISCOUNT_IN_QUOTE);
		if ($show_discount == 'Non') {
			$show_discount = false;
		} else {
			$show_discount = $this->getOption('prd_reduce');
		}

		$column = new Column(mb_strtoupper('code article'), 'C', 15, function($p) {
			return $p['id'] == 0 ? '' : $this->formatString(mb_strtoupper($p['ref']));
		});
		$this->table()->addColumn($column);

		//Column width depends if discount is shown or not
		$column = new Column(mb_strtoupper('Désignation'), 'C', $show_discount ? 40 : 65, function($p){
			if (isset($p['label'])) {
				return $p['label'];
			}

			/**
			 * @deprecated Les appels à Mysql ne doivent plus se faire depuis cette classe
			 */
			$is_colisage = prd_colisage_classify_exists($p['id']);
			$colisage_id = 0;
			if ($is_colisage && $colisage_id = fld_object_values_get(array($p['ord_id'], $p['id'], $p['line']), _FLD_PRD_COL_ORD_PRODUCT) ){
				$r_colisage = prd_colisage_types_get(parseInt($colisage_id));
				$colisage = ria_mysql_fetch_assoc($r_colisage);
			} else {
				$is_colisage = false;
			}

			$comment = '';
			if( $p['id']!=0 && $p['notes'] ){
				$comment = "\n";
			}
			if ($p['notes']) {
				$comment .= $p['notes'];
			}

			// Remplace le retour chariot par un saut ligne suivi d'un retour chariot.
			$comment = str_replace("\r", "\n\r", $comment);

			$product_name = ($p['id'] != 0 ? $p['name']. ($is_colisage ? ' - '.$colisage['name'].' ('.parseInt($colisage['qte']).')' : '') : '') . $comment;

			if(prd_nomenclatures_options_exists($p['id'])){
				$r_nomenclature_products = ord_products_get( $p['ord_id'], array('name' => 'asc'), 0, '', null, false, -1, $p['id'], $p['child-line'] );
				if ($r_nomenclature_products && ria_mysql_num_rows($r_nomenclature_products)){
					$last_name = '';
					$last_comment = '';
					$qte = 0;
					$count = 0;
					while ($product = ria_mysql_fetch_assoc($r_nomenclature_products)){
						if (trim($last_name) != '' && $last_name != $product['name']) {
							$product_name .= "\n- ".$last_name .' x'.$qte;
							if ($product['notes']) {
								$product_name .= "\n   ".$last_comment;
							}
							$count = 0;
							$qte = 0;
						}

						if(!$count){
							$count++;
						}
						$last_name = $product['title'];
						$last_comment = $product['notes'];
						$qte += $product['qte'];

					}
					$product_name .= "\n- ".$last_name.' x'.$qte;
					if ($product['notes']) {
						$product_name .= "\n   ".$last_comment;
					}
				}
			}

			return $product_name;
		});
		$this->table()->addColumn($column);

		// Colonne quantité
		$column = new Column(mb_strtoupper('Qté'), 'C', 10, function($p){
			if ($p['id'] == 0){
				return null;
			} else {
				return floatval( $p['qte'] );
			}
		});
		$this->table()->addColumn($column);

		$column = new Column(mb_strtoupper('Unité'), 'C', 10, function($p){
			if ($p['id'] == 0){
				return null;
			} else {
				return floatval( $p['col_qte'] );
			}
		});
		$this->table()->addColumn($column);

		// Colonne remise (champ avancé _FLD_ORD_LINE_DISCOUNT ou remise supplémentaire sur produit sur la ligne de commande)
		if( $show_discount ){
			// Colonne Prix Unitaire HT du produit
			$column = new Column(mb_strtoupper('P.U. HT'), 'C', 15, function($p){
				if( $p['id'] == 0 ){
					return null;
				}else{
					$fld_discount = fld_object_values_get(array($p['ord_id'], $p['id'], $p['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

					if( is_numeric($fld_discount) && $fld_discount > 0 ){
						$divide_by = (1 - $fld_discount/100);
						if( $divide_by > 0 ){
							$p['price_ht'] = $p['price_ht'] / $divide_by;
						}
					}

					return ria_number_french($p['price_ht']);
				}
			});
			$this->table()->addColumn($column);

			$column = new Column(mb_strtoupper("% rem"), 'C', 10, function ($product) {
				$remise = 0;
				if( isset($product['price_brut_ht']) ){
					$remise = number_format(100 - ($product['price_ht'] * 100 / $product['price_brut_ht']), 0);
				}

				$fld_discount = fld_object_values_get(array($product['ord_id'], $product['id'], $product['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

				// on ajoute le signe lié à la remise (en euro ou pourcentage)
				// floatval permet de retirer les 0 superflux
				// Si une remise a été renseignée avec le champ avancé, celle ci est prioritaire à celle renseignée sur la ligne de commande
				if( is_numeric($fld_discount) && $fld_discount > 0 ){
					$remise = str_replace(',00', '', number_format($fld_discount, 2, ',', ' ')).' %';
				}elseif( $product['discount'] > 0 ){
					if( $product['discount_type'] === "0" ){ // Euros
						$remise = number_format($product['discount'], 2, ',', ' ').' '.$this->currency;
					}else{ // %
						$remise = str_replace(',00', '', number_format($product['discount'], 2, ',', ' ')).' %';
					}
				}

				if( $remise ==  0 ){
					return null;
				}else{
					return $remise;
				}
			});
			$this->table()->addColumn($column);
		}

		//If show discount, we show the price as unit price with discount, otherwise we show the unit price without discount
		$column = new Column(mb_strtoupper($show_discount ? 'P.U. Ht rem' : 'P.U. HT'), 'C', 15, function($p){
			if( $p['id'] == 0 ){
				return null;
			} else {
				return ria_number_french($p['price_ht']);
			}
		});
		$this->table()->addColumn($column);

		// Colonne total HT de la ligne de commande
		$column = new Column(mb_strtoupper('Montant HT'), 'C', 15, function($p){
			$total_ht_displayed = $p['total_ht'];

			$fld_discount = fld_object_values_get(array($p['ord_id'], $p['id'], $p['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

			// Calcul du montant HT de la ligne
			// Si une remise a été renseignée avec le champ avancé, le calcul de la remise est déjà fait sur le total de la ligne de commade, il n'est donc pas nécessaire de l'appliquer.
			// Sinon, on calcule la remise de la ligne de commande si renseignée
			if( (!is_numeric($fld_discount) || $fld_discount <= 0) && $p['discount'] > 0 ){
				if( $p['discount_type'] === "0" ){ // Euros
					$total_ht_displayed = $p['total_ht'] - $p['discount'];
				}else{ // %
					$total_ht_displayed = $p['total_ht'] * (1 - ($p['discount']/100));
				}
			}

			if( $p['id'] == 0 ){
				return null;
			} else {
				return ria_number_french($total_ht_displayed);
			}
		});
		$this->table()->addColumn($column);

		return $this->table();
	}

	/**
	* Ajout de la première page
	**/
	public function setupPageOne() {
		global $config;

		$width_available = $this->w - (2 * 10.00125);
		$height_available = $this->h - (2 * 10.00125);

		$this->Image(dirname(__FILE__)."/images/man.jpg", $this->GetX(), $this->GetY(), $width_available, $height_available / 2, "JPG");
		$this->resetX();
		$this->SetY($this->GetY() + ($height_available / 2) + 10);

		// Il reste la hauteur moins la hauteur de l'image
		$height_available = $height_available / 2;

		$this->SetFont("Arial", "B", 18);
		$this->MultiCell($width_available, 10, $this->formatString($this->data["user"]["society"]), 0, "C");

		$this->SetFont("Arial", "", 18);
		$this->MultiCell($width_available, 10, $this->formatString($this->data["user"]["address1"]." ".$this->data["user"]["address2"]), 0, "C");
		$this->MultiCell($width_available, 10, $this->formatString($this->data["user"]["zipcode"]." ".$this->data["user"]["city"]), 0, "C");

		$for_user_label = "";

		if( isset($this->data["ord"]["contact"]["firstname"], $this->data["ord"]["contact"]["lastname"]) ){
			$for_user_label = $this->data["ord"]["contact"]["firstname"]." ".$this->data["ord"]["contact"]["lastname"];
			if ($for_user_label === " ") {
				$for_user_label = "";
			} else {
				$for_user_label = "À l'attention de ".$for_user_label;
			}
		}

		$this->SetFont("Arial", "B", 18);
		$this->MultiCell($width_available, 20, $this->formatString($for_user_label), 0, "C");

		$this->SetFont("Arial", "", 18);
		$piece = $this->data["ord"]["piece"] === "" ? $this->data["ord"]["id"] : $this->data["ord"]["piece"];
		$this->MultiCell($width_available, 10, $this->formatString(mb_strtoupper("proposition commerciale N°DVC20").$piece), 0, "C");

		$this->Image(dirname(__FILE__)."/images/categories.jpg", (($width_available / 4) / 2)  + 10.00125, $this->GetY() + 10, $width_available * 0.75, round( 340 * 0.0875, 3 ), "JPG");

		$this->blocFooter();
		// $this->addLogoToHeader();
	}

	/**
	* Ajout de la deuxième page
	**/
	public function addPageTwo() {
		$this->addPage();

		$this->setCurrency();

		$width_available = $this->w - (2 * 10.00125);
		// On retire aussi la hauteur du footer
		$height_available = $this->h - (2 * 10.00125) - $this->logo_height;

		$this->resetX();

		$this->SetFont("Arial", "B", 12);
		$this->Cell(20, 5, $this->formatString(mb_strtoupper("Objet : ")), 0, 0);
		$this->SetFont("Arial", "", 12);
		$comment = isset($this->data["ord"]["dlv-notes"]) ? substr(str_replace("\n"," ", $this->data["ord"]["dlv-notes"]), 0 , 150) : "";
		$this->Cell(0, 5, $this->formatString($comment), 0, 1);

		$this->SetFont("Arial", "B", 12);
		$this->Cell(35, 5, $this->formatString(mb_strtoupper("Montant ht : ")), 0, 0);
		$this->SetFont("Arial", "", 12);
		$this->Cell(0, 5, $this->formatString($this->price($this->data["ord"]["total_ht"]))." ".$this->currency, 0, 1);

		$height_available -= 20;

		// Ajout image présentation
		$this->SetY($this->GetY() + 5);
		$this->Image(dirname(__FILE__)."/images/presentation_objets.jpg", $this->GetX(), $this->GetY(), $width_available, 25, "JPG");

		// Pour des questions de simplicités, c'est une image et non du texte
		// On ajoute la hauteur de l'image précédente et on ajopute 5 pour l'espacement
		$this->SetY($this->GetY() + 25 + 5);
		$height_available = $height_available - 25 - 5 - 5;
		$this->Image(dirname(__FILE__)."/images/avantages.jpg", $this->GetX(), $this->GetY(), round( 2319 * 0.0875, 3 ), round( 2340 * 0.0875, 3 ), "JPG");

		$this->blocFooter();
		$this->addLogoToHeader();
	}

	/**
	* Ajout de la troisième page. Elle contient les produits.
	**/
	public function addPageThree() {
		$this->requireData('ord');

		$this->total_page_y = 160;

		$this->addPage();
		$this->addLogoToHeader();
		$this->generateOrdersInformations();
		$this->table()->generateTable();
		$this->generateTotalPage();

		$width_available = $this->w - (2 * 10.00125);

		$this->resetX();
		// Permet de mettre un espace entre le total et le message
		$this->SetY($this->GetY() + 5);

		$this->Image(dirname(__FILE__)."/images/valide_commande_a.jpg", $this->GetX(), $this->GetY(), round( 1966 * 0.0875, 3 ), round( 95 * 0.0875, 3 ), "JPG");

		$this->SetY($this->GetY() + round( 95 * 0.0875, 3 ) + 0.5);

		$piece = $this->data["ord"]["piece"] === "" ? $this->data["ord"]["id"] : $this->data["ord"]["piece"];
		$this->MultiCell($width_available, 5, $this->formatString("DVC20".$piece."."), 0);

		$this->SetY($this->GetY() + 3);

		$this->Image(dirname(__FILE__)."/images/valide_commande.jpg", $this->GetX(), $this->GetY(), round( 1966 * 0.0875, 3 ), round( 319 * 0.0875, 3 ), "JPG");

		$this->blocFooter();
	}

	/**
	* Ajout de la quatrième page. Dans le meilleur des cas c'est la 4e sinon c'est la dernière. Ce sont les CGU.
	**/
	public function addPageFour() {
		$this->addPage();
		$this->Image(dirname(__FILE__)."/images/cgv.jpg", $this->GetX(), $this->GetY(), round( 2155 * 0.0875, 3 ), round( 3045 * 0.0875, 3 ), "JPG");

		$this->SetFont("Arial", "", 4);
		$this->SetXY( round( 1870 * 0.0875, 3 ), round( 2470 * 0.0875, 3 ) );
		$this->Cell( 30, 2, $this->formatString($this->seller_infos["adr_lastname"]), 0, 1, "R" );
		$this->SetX( round( 1870 * 0.0875, 3 ) );
		$this->Cell( 30, 2, trim($this->getSellerPhone()), 0, 1, 'R' );

		$this->SetFont("Arial", "B", 4);
		$this->SetXY( round( 1750 * 0.0875, 3 ), round( 2548 * 0.0875, 3 ) );
		$this->Cell(50, 2, $this->formatString($this->data["user"]["society"]), 0, 1, "L");

		$this->SetFont("Arial", "", 4);
		$this->SetX(round( 1750 * 0.0875, 3 ) );
		$this->Cell(50, 2, $this->formatString($this->data["user"]["address1"]." ".$this->data["user"]["address2"]), 0, 1, "L");

		$this->SetX(round( 1750 * 0.0875, 3 ) );
		$this->Cell(50, 2, $this->formatString($this->data["user"]["zipcode"]." ".$this->data["user"]["city"]), 0, 1, "L");

		$this->SetX(round( 1750 * 0.0875, 3 ) );
		$this->Cell(50, 2, $this->formatString('Tél : '.$this->data["user"]['phone']), 0, 1, "L");

		$for_user_label = "";

		if( isset($this->data["ord"]["contact"]["firstname"], $this->data["ord"]["contact"]["lastname"]) ){
			$for_user_label = $this->data["ord"]["contact"]["firstname"]." ".$this->data["ord"]["contact"]["lastname"];
			if ($for_user_label === " ") {
				$for_user_label = "";
			} else {
				$for_user_label = "Représentée par : ".$for_user_label;
			}
		}

		if( trim($for_user_label) != '' ){
			$this->SetX(round( 1750 * 0.0875, 3 ) );
			$this->Cell(50, 2, $this->formatString($for_user_label), 0, 1, "L");
		}
	}

	/**
	* Permet d'ajouter les CGU
	**/
	public function addCGU() {
		$width_available = $this->w - (2 * 10.00125);

		// Espace entre chaque section
		$space_between_section = 1;

		// hauteur des celulles pour les sections
		$height_cell_section = 4;

		$this->SetFont("Arial", "B", 16);
		$this->Cell($width_available, 5, $this->formatString(mb_strtoupper("CONDITIONS GÉNÉRALES DE VENTE")), 0, 1, "C");

		$this->SetY($this->GetY() + $space_between_section);

		// Section 1
		$this->SetFont("Arial", "B", 10);
		$this->Cell($width_available, 5, $this->formatString(mb_strtoupper("1.prix")), 0, 1);
		$this->SetFont("Arial", "", 10);
		$this->MultiCell($width_available, $height_cell_section, $this->formatString("Hors taxe, départ usine."), 0, "J");

		$this->SetY($this->GetY() + $space_between_section);

		// Section 2
		$this->SetFont("Arial", "B", 10);
		$this->Cell($width_available, $height_cell_section, $this->formatString(mb_strtoupper("2.commandes")), 0, 1);
		$this->SetFont("Arial", "", 10);
		$this->MultiCell($width_available, $height_cell_section, $this->formatString("Tous les engagements pris par nos technico-commerciaux ne sont valables qu'après avoir été acceptés et confirmés par Boplan France. L'annulation d'une commande dûment enregistrée ne peut être faite qu'avec notre consentement.Les articles sur mesures et/ou, fabriqués sur demande, ne peuvent être annulés si l'exécution en est commencée, ou si la matière est spécialement approvisionnée."), 0, "J");

		$this->SetY($this->GetY() + $space_between_section);

		// Section 3
		$this->SetFont("Arial", "B", 10);
		$this->Cell($width_available, $height_cell_section, $this->formatString(mb_strtoupper("3.delai de livraison")), 0, 1);
		$this->SetFont("Arial", "", 10);
		$this->MultiCell($width_available, $height_cell_section, $this->formatString("Nos délais de livraison ne sont donnés qu'à titre indicatif et sont maintenus dans la limite du possible. En aucun cas, ils ne constituent un engagement formel. Un retard à livrer, même important, ne peut constituer une cause acceptable, ni de refus de livraison ni d'action  en dommages et intérêts d'aucune sorte."), 0, "J");

		$this->SetY($this->GetY() + $space_between_section);

		// Section 4
		$this->SetFont("Arial", "B", 10);
		$this->Cell($width_available, 5, $this->formatString(mb_strtoupper("4.conditions de livraison")), 0, 1);
		$this->SetFont("Arial", "", 10);
		$this->MultiCell($width_available, $height_cell_section, $this->formatString("Les marchandises sont vendues départ usine. Elles voyagent toujours aux risques et périls du destinataire, même lorsque les prix sont établis franco destinataire, lequel doit faire toutes réserves, le cas échéant, pour pouvoir exercer recours contre le transporteur, seul responsable des avaries et manquants. Les réclamations, pour être valables, devront nous parvenir dans les quinze jours suivant l'arrivée des marchandises."), 0, "J");

		$this->SetY($this->GetY() + $space_between_section);

		// Section 5
		$this->SetFont("Arial", "B", 10);
		$this->Cell($width_available, 5, $this->formatString(mb_strtoupper("5.conditions de paiement")), 0, 1);
		$this->SetFont("Arial", "", 10);
		$this->MultiCell($width_available, $height_cell_section, $this->formatString("Toutes nos factures sont payables à 30 jours date de facture par virement ou par tout autre moyen à votre convenance.Le défaut de paiement d’une livraison nous autorise à suspendre les expéditions et rend exigible la valeur des produits commandés, disponibles ou en cours de fabrication. Le défaut de paiement provoque également la déchéance du terme et rend immédiatement exigible toutes les autres créances. Tout retard de paiement ou tout report d’échéance est passible de plein droit et sans mise en demeure préalable de pénalités de retard calculés par mensualité en appliquant au montant H.T des créances, un pourcentage égal à trois fois le taux d’intérêt légal en vigueur. Tout mois civil commencé est dû en totalité en ce qui concerne l’intérêt.En sus des indemnités de retard, toute somme, y compris l’acompte, non payée à sa date d’exigibilité produira de plein droit le paiement d’une indemnité forfaitaire de 40 euros due au titre des frais de recouvrement."), 0, "J");

		$this->SetY($this->GetY() + $space_between_section);

		// Section 6
		$this->SetFont("Arial", "B", 10);
		$this->Cell($width_available, 5, $this->formatString(mb_strtoupper("6.garantie")), 0, 1);
		$this->SetFont("Arial", "", 10);
		$this->MultiCell($width_available, $height_cell_section, $this->formatString("Notre garantie est strictement limitée au remplacement dans un délai normal, ou au remboursement, à notre choix, des pièces reconnues défectueuses ou non conformes, après examen par nos soins.Le remplacement éventuel ne pourra, en aucun cas, justifier un retard ou un refus à régler nos factures.Seule pourra être retenue la valeur des pièces reconnues défectueuses."), 0, "J");

		$this->SetY($this->GetY() + $space_between_section);

		// Section 7
		$this->SetFont("Arial", "B", 10);
		$this->Cell($width_available, 5, $this->formatString(mb_strtoupper("7.clause de reserve de propriete")), 0, 1);
		$this->SetFont("Arial", "", 10);
		$this->MultiCell($width_available, $height_cell_section, $this->formatString("Boplan France reste propriétaire de la marchandise livrée à compter du jour de livraison jusqu'à complet paiement de l'intégralité du prix de vente, les risques de la marchandise incombant néanmoins au destinataire, dès la mise à disposition de celle-ci. Ne constitue pas des paiements, la remise de traites ou de tous titres créant une obligation de payer.En conséquence, en cas de non-paiement, la société est en droit d'effectuer ou de faire effectuer la reprise de la marchandise à la charge du destinataire.Cette clause fait partie intégrante de nos conditions générales de vente."), 0, "J");

		$this->SetY($this->GetY() + $space_between_section);

		// Section 8
		$this->SetFont("Arial", "B", 10);
		$this->Cell($width_available, 5, $this->formatString(mb_strtoupper("8.contestations")), 0, 1);
		$this->SetFont("Arial", "", 10);
		$this->MultiCell($width_available, $height_cell_section, $this->formatString("En cas de contestation relative à une fourniture ou à son règlement, le Tribunal de Commerce de Nanterre est seul compétent, la loi applicable sera la loi française."), 0, "J");

		$this->SetY($this->GetY() + $space_between_section);

		// Section 9
		$this->SetFont("Arial", "B", 10);
		$this->Cell($width_available, 5, $this->formatString(mb_strtoupper("9.acceptation")), 0, 1);
		$this->SetFont("Arial", "", 10);
		$this->MultiCell($width_available, $height_cell_section, $this->formatString("Sauf conventions spéciales et écrites, le destinataire agrée et accepte expressément les présentes conditions générales de vente ainsi que les tarifs. Il déclare et reconnaît en avoir une parfaite connaissance, et renonce, de ce fait, à se prévaloir de tout document contradictoire et, notamment, ses propres conditions générales d’achat."), 0, "J");
	}

	/**
	* Permet de convertir un string avec iconv
	*
	* @param $str: String à convertir
	*
	* @return la chaine convertie
	**/
	public function formatString($str) {
		return iconv('utf8', 'windows-1252', $str);
	}

	/**
	* Allows to draw rounded rectangles. Parameters are:
	* @param x, y: top left corner of the rectangle.
	* @param w, h: width and height.
	* @param r: radius of the rounded corners.
	* @param style: same as Rect(): F, D (default value), FD or DF.
	**/
	public function RoundedRect($x, $y, $w, $h, $r, $corners = '1234', $style = '') {
        $k = $this->k;
        $hp = $this->h;
        if($style=='F')
            $op='f';
        elseif($style=='FD' || $style=='DF')
            $op='B';
        else
            $op='S';
        $MyArc = 4/3 * (sqrt(2) - 1);
        $this->_out(sprintf('%.2F %.2F m',($x+$r)*$k,($hp-$y)*$k ));

        $xc = $x+$w-$r;
        $yc = $y+$r;
        $this->_out(sprintf('%.2F %.2F l', $xc*$k,($hp-$y)*$k ));
        if (strpos($corners, '2')===false)
            $this->_out(sprintf('%.2F %.2F l', ($x+$w)*$k,($hp-$y)*$k ));
        else
            $this->_Arc($xc + $r*$MyArc, $yc - $r, $xc + $r, $yc - $r*$MyArc, $xc + $r, $yc);

        $xc = $x+$w-$r;
        $yc = $y+$h-$r;
        $this->_out(sprintf('%.2F %.2F l',($x+$w)*$k,($hp-$yc)*$k));
        if (strpos($corners, '3')===false)
            $this->_out(sprintf('%.2F %.2F l',($x+$w)*$k,($hp-($y+$h))*$k));
        else
            $this->_Arc($xc + $r, $yc + $r*$MyArc, $xc + $r*$MyArc, $yc + $r, $xc, $yc + $r);

        $xc = $x+$r;
        $yc = $y+$h-$r;
        $this->_out(sprintf('%.2F %.2F l',$xc*$k,($hp-($y+$h))*$k));
        if (strpos($corners, '4')===false)
            $this->_out(sprintf('%.2F %.2F l',($x)*$k,($hp-($y+$h))*$k));
        else
            $this->_Arc($xc - $r*$MyArc, $yc + $r, $xc - $r, $yc + $r*$MyArc, $xc - $r, $yc);

        $xc = $x+$r ;
        $yc = $y+$r;
        $this->_out(sprintf('%.2F %.2F l',($x)*$k,($hp-$yc)*$k ));
        if (strpos($corners, '1')===false)
        {
            $this->_out(sprintf('%.2F %.2F l',($x)*$k,($hp-$y)*$k ));
            $this->_out(sprintf('%.2F %.2F l',($x+$r)*$k,($hp-$y)*$k ));
        }
        else
            $this->_Arc($xc - $r, $yc - $r*$MyArc, $xc - $r*$MyArc, $yc - $r, $xc, $yc - $r);
        $this->_out($op);
    }

    function _Arc($x1, $y1, $x2, $y2, $x3, $y3) {
        $h = $this->h;
        $this->_out(sprintf('%.2F %.2F %.2F %.2F %.2F %.2F c ', $x1*$this->k, ($h-$y1)*$this->k,
            $x2*$this->k, ($h-$y2)*$this->k, $x3*$this->k, ($h-$y3)*$this->k));
    }

    /**
	* Calcul le nombre de ligne nécessaire pour un multiCell de largeur w avec le texte txt
	*
	* @param $w: Width du MultiCell
	* @param $txt: le texte dans le MultiCell
	*
	* @return $nl: le nombre de lignes nnécessaire au MultiCell
	* */
	private function NbLines($w, $txt) {
		$txt = trim($txt);

		// Recupère le jeu de caractère
	    $set_of_characters = $this->CurrentFont['cw'];

	    // Calcul la largeur de la cellule pour le PDF avec la dimmension de la police.
	    // On ajoute trois fois espace pour simuler des bords et avoir plus de place.
	    $w_max = ceil(((($w - 10.00125) * 1000) / $this->FontSize) + (3 * $set_of_characters[" "]));
	    $txt = str_replace("\t","",$txt);
	    $txt = str_replace("\r","",$txt);
	    $txt = str_replace("\0","",$txt);
	    $txt = str_replace("\x0B","",$txt);
	    $number_of_characters = strlen($txt);
	    $line_width = 0;

	    // Verifie que le dernier caratère n'est pas \n. Si c'est le cas on réduit nb de 1 car on comptera pas ce caractère.
	    if ($number_of_characters > 0 and $txt[$number_of_characters-1] ===  "\n") {
	        $number_of_characters--;
	    }

	    $number_of_lines = 1;
	    for ($i=0; $i < $number_of_characters; $i++) {
	    	// Récupere le caratere
	    	$character = substr($txt, $i, 1);

	    	switch ($character) {
	    		case "\n":
	    			$number_of_lines += 1;
	    			$line_width = 0;
	    			break;

	    		case " ":
	    			break;

	    		default:
	    			if ($line_width > $w_max) {
	    				$line_width = $set_of_characters[$character];
	    				$number_of_lines += 1;
	    			} else {
	    				$line_width += $set_of_characters[$character];
	    			}
	    			break;
	    	}
	    }

	    return $number_of_lines;
	}

	private function getSellerPhone() {
		$phone_number = $this->seller_infos["mobile"];
		if ($phone_number === "") {
			$phone_number = $this->seller_infos["phone"];
		}
		return $phone_number;
	}
}