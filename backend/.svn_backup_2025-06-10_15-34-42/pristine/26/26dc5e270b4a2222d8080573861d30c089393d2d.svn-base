<?php

// \cond onlyria
/**	\defgroup gu_users_images Images
 *	\ingroup model_users dam
 *	Les fonctions de ce module permettent de gérer des images associées aux comptes clients
 *	@{
 */

/** Permet l'upload d'une image principale à associer à un client.
 *
 *	@param int $usr Identifiant du client.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *
 */
function gu_images_main_upload( $usr, $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return gu_images_main_add( $usr, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );
}

/** Permet l'ajout d'un fichier image à un client.
 *
 *	@param int $usr Identifiant du client.
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source
 *
 *	@return int L'identifiant attribué à l'image.
 *
 */
function gu_images_main_add( $usr, $filename, $srcname='' ){
	if( !is_numeric( $usr ) ) return false;
	global $config;

	// Référencement de l'image dans le système de médiathèque
	if( $id = img_images_add( $filename, $srcname ) ){
		ria_mysql_query('update gu_users set usr_img_id='.$id.' where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr);
		img_images_count_update($id);
	}

	// Mise à jour de l'image dans l'index du moteur de recherche
	$search_contents = ria_mysql_query('select usr_cnt_id as cnt_id from gu_users where usr_tnt_id='.$config['tnt_id'].' and usr_id='.$usr);
	if( $search_contents && ria_mysql_num_rows( $search_contents ) ){
		while( $r = ria_mysql_fetch_array($search_contents) )
			search_contents_image_add( $r['cnt_id'], $id );
	}

	return $id;
}

/** Cette fonction permet de supprimer l'image principale d'un compte.
 * 	@param int $usr_id Obligatoire, identifiant d'un compte
 * 	@return bool true en cas de succès, false en cas d'erreur
 */
function gu_images_main_del( $usr_id ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return false;
	}

	return ria_mysql_query('
		update gu_users
		set usr_img_id = null,
				usr_date_modified = now()
		where usr_tnt_id = '.$config['tnt_id'].'
			and usr_id = '.$usr_id.'
	');
}

/// @}
// \endcond
