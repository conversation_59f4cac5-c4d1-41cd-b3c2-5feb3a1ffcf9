<?php

require_once('users.inc.php');
require_once('view.admin.inc.php');
require_once('admin/menu.inc.php');

/** \defgroup model_rights Gestion des droits
 * 	\ingroup auth
 *	Ce module comprend les fonctions nécessaires à la gestion des droits utilisateurs.
 *
 *	@{
 */

/** Cette fonction permet de récupérer toutes les catégories de droits.
 *	@param int $cat Optionnel, identifiant d'une catégorie de droits
 *  @param bool $admin Optionnel, null pour récupérer toute les catégories, false pour récupérer seulement les catégories sur les sites et yuto, true pour récupérer seulement les catégories admin
 *	@param bool $is_yuto Optionnel, null pour récupérer toutes les catégories, false pour récupérer seulement les catégories sur les sites et admin, true pour récupérer seulement les catégories yuto
 * 	@param bool $linked_rights Optionnel, permet de récupérer les droits qui sont soient utilisés par l'administration soit par Yuto (par défaut à False)
 *	@return bool false si une erreur se produit
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant de la catégorie de droits
 *				- name : nom de la catégorie
 *				- desc : description de la catégorie
 */
function gu_categories_rights_get( $cat=0, $admin=false, $is_yuto=false, $linked_rights=false){
	if( !is_numeric($cat) || $cat<0 ) return false;

	$sql = '
		select cat_id as id, cat_name as name, cat_desc as "desc"
		from gu_categories_rights
		where 1
	';

	if ($linked_rights) {
        $sql .= ' and (cat_admin=1 or cat_is_yuto=1) ';
    } else {
        if($admin === false){
            $sql .= ' and cat_admin = 0 ';
        }elseif($admin){
            $sql .= ' and cat_admin = 1 ';
        }

        if($is_yuto === false){
            $sql .= ' and cat_is_yuto = 0 ';
        }elseif($is_yuto){
            $sql .= ' and cat_is_yuto = 1 ';
        }
    }


	if( $cat>0 )
		$sql .= ' and cat_id='.$cat;

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de tester si une catégorie de droits existe.
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@return bool true si la catégorie existe, false dans le cas contraire
 */
function gu_categories_rights_exists( $cat ){
	if( !is_numeric($cat) || $cat<=0 ) return false;

	$res = ria_mysql_query('
		select 1
		from gu_categories_rights
		where cat_id='.$cat.'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return true;
}

/** Cette fonction permet de récupérer le nom d'une catégorie de droits d'accès à la plateforme.
 *	@param int $cat Obligatoire, identifiant de la catégorie
 *	@return bool false si la catégorie n'est pas trouvée,
 *	@return string le nom de la catégorie
 */
function gu_categories_rights_get_name( $cat ){
	if( !is_numeric($cat) || $cat<=0 ) return false;

	$res = ria_mysql_query('
		select cat_name as name
		from gu_categories_rights
		where cat_id='.$cat.'
	');

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'name' );
}

/** Cette fonction permet de compter le nombre de droits rattachés à une catégorie.
 *	@param int $cat Obligatoire, identifiant d'une catégorie
 *  @param bool $admin Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites et yuto, true pour compter seulement les droits admin
 *  @param bool $usr Optionnel, true pour ne compter que les droits auquels l'administrateur a accès (ignorer si $admin != true)
 * 	@param bool $is_yuto Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites et admin, true pour compter seulement les droits yuto
 * 	@param bool $linked_rights Optionnel, permet de récupérer les droits qui sont soient utilisés par l'administration soit par Yuto (par défaut à False)
 *	@return int|bool le nombre de droits rattachés en cas de succès, false en cas d'erreur
 */
function gu_categories_rights_count_rights( $cat, $admin=false, $usr=false, $is_yuto=false, $linked_rights=false){
	if( !is_numeric($cat) || $cat<=0 ) return false;
	global $config;

	$sql ='
		select count(*) as count_rgh
		from gu_rights
		where (rgh_tnt_id = 0 or rgh_tnt_id = '.$config['tnt_id'].' )
			and rgh_cat_id='.$cat.'
	';

	if ($linked_rights) {
        $sql .= ' and (rgh_admin = 1 or rgh_is_yuto = 1) ';
    } else {
        if($admin === false){
            $sql .= ' and rgh_admin = 0 ';
        }elseif($admin){
            $sql .= ' and rgh_admin = 1 ';
        }

        if($is_yuto === false){
            $sql .= ' and rgh_is_yuto = 0 ';
        }elseif($is_yuto){
            $sql .= ' and rgh_is_yuto = 1 ';
        }
    }


	if( $admin && $usr ){
		$rights = gu_users_load_admin_rights();
		$sql .= ' and rgh_id in ('.implode(',', $rights).')';
	}

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'count_rgh' );
}

/** Cette fonction permet de récupérer la liste des droits.
 *	@param int $rgh Optionnel, identifiant d'un droit
 *	@param string $code Optionnel, code d'un droit
 *	@param int $cat Optionnel, identifiant d'une catégorie
 *	@param array $exclude Optionnel, permet d'exclure des droits du résultat retourner (mettre le code)
 *  @param bool $admin Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites, true pour compter seulement les droits admin
 *  @param int $parent Optionnel, id du parent du droit (mettre null pour récupérer les droits tout en haut de la hiérarchie)
 * 	@param bool $link_tenant Optionnel, par défaut les droits liés au tenant seront aussi récupérés, mettre false pour ne récupérer que les droits globaux
 * 	@param bool $usr Optionnel, true pour retourner seulement les droits auquels l'administrateur à accès (ignorer si $admin != true)
 * 	@param bool $is_yuto Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites et admin, true pour compter seulement les droits yuto
 * 	@param bool $linked_rights Optionnel, permet de récupérer les droits qui sont soient utilisés par l'administration soit par Yuto (par défaut à False)
 *	@return bool false si une erreur s'est produite
 *	@return resource un résultat de requête MySQL contenant :
 *				- id : identifiant du droit
 *				- code : code du droit
 *				- name : nom du droit
 *				- desc : description du droit
 *				- public : détermine si le droit est public
 *				- cat_id : identifiant de catégorie du droit
 * 				- cat_name : nom de la catégorie du droit
 * 				- parent_id : identifiant du parent du droit
 * 				- depend_on : tableau d'identifiants des droits dont dépend le droit au format json
 * 				- depend_on_all : boolean indiquant si le droit peut etre activé seulement si tous les droits dans depend_on sont activés
 */
function gu_rights_get( $rgh=0, $code='', $cat=0, $exclude=array(), $admin=false, $parent=false, $link_tenant=true, $usr=false, $is_yuto=false, $linked_rights=false){
	if( !is_numeric($rgh) || $rgh < 0 ){
		return false;
	}

	if( $cat > 0 && !gu_categories_rights_exists($cat) ){
		return false;
	}

	if( !is_array($exclude) ){
		return false;
	}

	global $config;

	$sql = '
		select rgh_id as id, rgh_code as code, rgh_name as name, rgh_desc as "desc", rgh_public as public, rgh_cat_id as cat_id, cat_name, rgh_parent_id as parent_id, rgh_depend_on as depend_on, rgh_depend_on_all as depend_on_all, rgh_admin as admin, rgh_is_yuto as is_yuto
		from gu_rights
			left join gu_categories_rights on (cat_id = rgh_cat_id)
		where 1
	';

	if ($link_tenant) {
		$sql .= ' and (rgh_tnt_id = 0 or rgh_tnt_id = '.$config['tnt_id'].' )';
	}else{
		$sql .= ' and rgh_tnt_id = 0';
	}

	if( $rgh > 0 ){
		$sql .= ' and rgh_id='.$rgh;
	}

	if( trim($code) != '' ){
		$sql .= ' and rgh_code=\''.addslashes($code).'\'';
	}

	if( $cat > 0 ){
		$sql .= ' and rgh_cat_id='.$cat;
	}

	if( sizeof($exclude) ){
		$sql .= ' and rgh_code not in ("'.implode('", "', $exclude).'")';
	}

	if ($linked_rights) {
        $sql .= ' and (rgh_admin = 1 or rgh_is_yuto = 1)';
    } else {
        if($admin === false){
            $sql .= ' and rgh_admin = 0 ';
        }elseif($admin){
            $sql .= ' and rgh_admin = 1 ';
        }

        if($is_yuto === false){
            $sql .= ' and rgh_is_yuto = 0 ';
        }elseif($is_yuto){
            $sql .= ' and rgh_is_yuto = 1 ';
        }
    }


	if($parent !== false){
		if($parent){
			$sql .= ' and rgh_parent_id = '.$parent;
		}else{
			$sql .= ' and rgh_parent_id is null';
		}
	}

	if( $usr && $admin ){
		$rights = gu_users_load_admin_rights();
		$sql .= ' and rgh_id in ('.implode(',', $rights).')';
	}

	$sql .= ' order by rgh_pos';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer un tableau des identifiant ou codes des droits.
 *	@param int $cat Optionnel, identifiant d'une catégorie
 *	@param bool $is_code Optionnel, par défaut le tableau contient les codes, mettre à false pour retourner un tableau contenant les identifiants
 *  @param bool $admin Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites, true pour compter seulement les droits admin
 *  @param bool $is_yuto Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites et admin, true pour compter seulement les droits yuto
 * 	@param bool $linked_rights Optionnel, permet de récupérer les droits qui sont soient utilisés par l'administration soit par Yuto (par défaut à False)
 *	@return bool false en cas d'échec, sinon un tableau contenant les identifiants ou les codes des droits
 */
function gu_rights_get_array( $cat=0, $is_code=true, $admin=false, $is_yuto=false, $linked_rights=false ){

	if( !is_numeric($cat) || $cat<0 ){
		return false;
	}

	$ar_rights = array();

	$rghs = gu_rights_get( 0, '', $cat, array(), $admin, false, true, false, $is_yuto, $linked_rights );
	if( $rghs ){

		while( $rgh = ria_mysql_fetch_array($rghs) ){
			$ar_rights[] = $is_code ? $rgh['code'] : $rgh['id'];
		}

	}

	return $ar_rights;
}

/** Cette fonction permet de vérifier si un droit existe (soit l'identifiant, soit le code)
 *	@param int $rgh Optionnel, identifiant d'un droit
 *	@param string $code Optionnel, code d'un droit
 *	@return bool false en cas d'erreur ou si aucun des paramètres n'est fourni
 *	@return bool true si le droit existe, false dans le cas contraire
 */
function gu_rights_exists( $rgh=0, $code='' ){
	global $config;

	if( !is_numeric($rgh) || $rgh<0 ) return false;
	if( $rgh<=0 && !trim($code) ) return false;

	$sql = '
		select 1
		from gu_rights
		where (rgh_tnt_id = 0 or rgh_tnt_id = '.$config['tnt_id'].' )
	';

	if( $rgh>0 ) $sql .= ' and rgh_id='.$rgh;
	if( trim($code) ) $sql .= ' and rgh_code=\''.addslashes($code).'\'';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer l'identifiant d'un droit à partir de son code
 *	@param string $code Obligatoire, code d'un droit
 *	@return bool false si aucun droit ne corresponds à ce code
 *	@return int l'identifiant du droit correspondant au code
 */
function gu_rights_get_id_bycode( $code ){
	global $config;

	if( !trim($code) ){
		return false;
	}

	$res = ria_mysql_query('
		select rgh_id as id
		from gu_rights
		where (rgh_tnt_id = 0 or rgh_tnt_id = '.$config['tnt_id'].' )
			and rgh_code=\''.addslashes($code).'\'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'id' );
}

/** Cette fonction permet de récupérer le code d'un droit à partir de son identifiant
 *	@param int $rgh Obligatoire, identifiant d'un droit
 *	@return bool false si aucun droit ne corresponds à cet identifiant
 *	@return string le code du droit correspondant à l'identifiant
 */
function gu_rights_get_code_byid( $rgh ){
	global $config;

	if( !is_numeric($rgh) || $rgh<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select rgh_code as code
		from gu_rights
		where (rgh_tnt_id = 0 or rgh_tnt_id = '.$config['tnt_id'].' )
			and rgh_id='.$rgh.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'code' );
}

/** Cette fonction permet de récupérer le nom d'un droit
 *	@param int $rgh Optionnel, identifiant d'un droit
 *	@param string $code Optionnel, code d'un droit
 *	@return bool false en cas d'erreur ou bien si aucun des paramètres n'est fourni
 *	@return string Le nom du droit correspondant à l'identifiant ou au code
 */
function gu_rights_get_name( $rgh=0, $code='' ){
	if( !is_numeric($rgh) || $rgh<0 ) return false;
	if( !$rgh<=0 && !trim($code) ) return false;

	global $config;

	$sql = '
		select rgh_name as name
		from gu_rights
		where (rgh_tnt_id = 0 or rgh_tnt_id = '.$config['tnt_id'].' )
	';

	if( $rgh>0 ) $sql .= ' and rgh_id='.$rgh;
	if( trim($code) ) $sql .= ' and rgh_code=\''.addslashes($code).'\'';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'name' );
}

/// @}

/** \defgroup model_profiles_rights Gestion des droits sur les profils
 * 	\ingroup auth
 *	Ce module comprend les fonctions nécessaires à la gestion des droits sur des profils de compte.
 *
 *	@{
 */

/** Cette fonction permet de récupérer les droits assignés à un profile utilisateur
 *	@param int $prf Obligatoire, identifiant d'un profile utilisateur
 *  @param bool $admin Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites, true pour compter seulement les droits admin
 *	@param bool $is_yuto Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites et admin, true pour compter seulement les droits yuto
 *  @return bool false en cas d'erreur
 *	@return resource un résultat MySQL contenant :
 *				- id : identifiant du droit
 *				- code : code du droit
 *				- name : nom du droit
 *				- desc : description du droit
 */
function gu_profiles_rights_get( $prf, $admin=false, $is_yuto=false ){
	global $config;

	if( !is_numeric($prf) || $prf<=0 ){
		return false;
	}

	$sql = '
		select rgh_id as id, rgh_code as code, rgh_name as name, rgh_desc as "desc"
		from gu_rights
			join gu_profiles_rights on (prg_tnt_id='.$config['tnt_id'].' and prg_rgh_id=rgh_id)
		where (rgh_tnt_id = 0 or rgh_tnt_id='.$config['tnt_id'].')
			and prg_prf_id='.$prf.'
	';

	if($admin === false){
		$sql .= ' and rgh_admin = 0 ';
	}elseif($admin){
		$sql .= ' and rgh_admin = 1 ';
	}

	if($is_yuto === false){
		$sql .= ' and rgh_is_yuto = 0 ';
	}elseif($is_yuto){
		$sql .= ' and rgh_is_yuto = 1 ';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer les codes ou les identifiants des droits assignés à un profil sous forme de tableau
 *	@param int $prf Obligatoire, identifiant d'un profil
 *	@param bool $is_code Optionnel, par défaut le tableau retourné contient les codes, mettre à false pour récupérer les identifiants
 *  @param bool $admin Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites, true pour compter seulement les droits admin
 *	@param bool $is_yuto Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites et admin, true pour compter seulement les droits yuto
 *	@return array Un tableau contenant les codes des droits d'un profil
 */
function gu_profiles_rights_get_array( $prf, $is_code=true, $admin=false, $is_yuto=false ){

	if( !gu_profiles_exists($prf) ){
		return false;
	}
	$rights = array();

	$prgs = gu_profiles_rights_get( $prf, $admin, $is_yuto );
	if( $prgs ){

		while( $prg = ria_mysql_fetch_array($prgs) ){
			$rights[] = $is_code ? $prg['code'] : $prg['id'];
		}

	}

	return $rights;
}

/** Cette fonction permet de compter le nombre de droits utilisé par uu profile.
 *	@param int $prf Obligatoire, identifiant d'un profile
 *	@param int $cat Optionnel, identifiant d'une catégorie de droit
 *  @param bool $admin Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites, true pour compter seulement les droits admin
 *	@param bool $is_yuto Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites et admin, true pour compter seulement les droits yuto
 *	@return bool false en cas d'erreur, sinon le nombre de droit utilisés
 */
function gu_profiles_rights_count( $prf, $cat=0, $admin=false, $is_yuto=false ){
	global $config;

	// Vérifie les paramètres d'entrée
	if(
		!is_numeric($prf) || $prf<=0
		|| !is_numeric($cat) || $cat<0
	){
		return false;
	}

	$sql = '
		select count(*) as count_rgh
		from gu_rights
			join gu_profiles_rights on (prg_tnt_id='.$config['tnt_id'].' and prg_rgh_id=rgh_id)
		where (rgh_tnt_id = 0 or rgh_tnt_id='.$config['tnt_id'].')
			and prg_prf_id='.$prf.'
	';

	if( $cat>0 ){
		$sql .= ' and rgh_cat_id='.$cat;
	}

	if($admin === false){
		$sql .= ' and rgh_admin = 0 ';
	}elseif($admin){
		$sql .= ' and rgh_admin = 1 ';
	}

	if($is_yuto === false){
		$sql .= ' and rgh_is_yuto = 0 ';
	}elseif($is_yuto){
		$sql .= ' and rgh_is_yuto = 1 ';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'count_rgh' );
}

/** Cette fonction permet de tester si un profile dispose déjà d'un droit.
 *	@param int $prf Obligatoire, identifiant d'un profile
 *	@param int $rgh Obligatoire, identifiant d'un droit
 *	@return bool true si le profil dispose déjà du droit, false dans le cas contraire
 */
function gu_profiles_rights_exists( $prf, $rgh ){
	global $config;

	if( !is_numeric($prf) || $prf<=0 ) return false;
	if( !is_numeric($rgh) || $rgh<=0 ) return false;

	$res = ria_mysql_query('
		select 1
		from gu_profiles_rights
		where prg_tnt_id='.$config['tnt_id'].'
			and prg_prf_id='.$prf.'
			and prg_rgh_id='.$rgh.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet d'ajout un ou plusieurs droits à un profile utilisateur existant
 *	@param int $prf Obligatoire, identifiant d'un profil utilisateur
 *	@param int|array $rights Obligatoire, identifiant ou tableau d'identifiants de droits
 *	@param bool $reset Optionnel, par défaut les comptes liés au profil conservent leur personnalisation des droits, mettre à true pour les réinitialiser
 *	@return bool true si l'ajout s'est correctement déroulé, sinon false dans le cas contraire
 */
function gu_profiles_rights_add( $prf, $rights, $reset=false ){
	if( !gu_profiles_exists($prf) ) return false;
	if( !is_numeric($rights) && !is_array($rights) ) return false;
	if( is_array($rights) && !sizeof($rights) ) return false;
	global $config;

	if( !gu_profiles_rights_del($prf) )
		return false;

	if( is_numeric($rights) ){
		if( $rights<=0 ) return false;
		if( gu_profiles_rights_exists($prf, $rights) ) return true;
		$rights = array($rights);
	} else {
		foreach( $rights as $key=>$rgh ){
			if( !is_numeric($rgh) || $rgh<=0 ) return false;
		}
	}

	if( !sizeof($rights) )
		return true;

	$sql = '
		insert into gu_profiles_rights
			( prg_tnt_id, prg_prf_id, prg_rgh_id )
		values
	';

	$count = 1;
	foreach( $rights as $rgh ){
		$sql .= '( '.$config['tnt_id'].', '.$prf.', '.$rgh.' )'.( $count<sizeof($rights) ? ', ' : '' );
		$count++;
	}

	ria_debug_log('rights', array('sql'=>$sql));

	if( !ria_mysql_query($sql) ){
		return false;
	}

	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return gu_profiles_rigths_update_users( $prf, $reset );
}

/** Cette fonction permet de mettre à jours les droits pour tous les utilisateurs rattachés au profile
 *	@param int $prf Obligatoire, identifiant d'un profile
 *	@param bool $reset Optionnel, par défaut les comptes liés au profile conservent leur personnalisation des droits, mettre à true pour les réinitiliser
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function gu_profiles_rigths_update_users( $prf, $reset=false ){
	global $config;

	if( !gu_profiles_exists($prf) ){
		return false;
	}

	if( $reset ){
		// réinitialise les droits pour tous les utilisateurs liés au profil
		$sql = '
			delete from gu_users_rights
			where urg_tnt_id='.$config['tnt_id'].'
				and urg_usr_id in (
					select usr_id
					from gu_users
					where usr_tnt_id=urg_tnt_id
						and usr_prf_id='.$prf.'
				)
		';

		ria_debug_log('rights', array('sql'=>$sql));

		$reset_usr = ria_mysql_query($sql);

		if( !$reset_usr ){
			return false;
		}
	} else {
		$rgh_prf = gu_profiles_rights_get_array( $prf, false );

		// supprime les presonnalisations à Non des droits qui ne sont plus dans le profil
		$sql = '
			delete from gu_users_rights
			where urg_tnt_id='.$config['tnt_id'].'
				and urg_usr_id in (
					select usr_id
					from gu_users
					where usr_tnt_id=urg_tnt_id
						and usr_prf_id='.$prf.'
				) and (
					(urg_rgh_id not in ('.implode( ',', $rgh_prf ).') and urg_allowed=0)
					or
					(urg_rgh_id in ('.implode( ',', $rgh_prf ).') and urg_allowed=1)
				)
		';
		ria_debug_log('rights', array('sql'=>$sql));

		$dres = ria_mysql_query($sql);


		if( !$dres )
			return false;
	}

	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return true;
}

/** Cette fonction permet de retirer un ou plusieurs droits à un profile.
 *	@param int $prf Obligatoire, identifiant d'un profile existant
 *	@param int|array $rights Obligatoire, identifiant ou tableau d'identifiants de droits
 *	@param bool $reset Optionnel, par défaut les comptes liés au profil conservent leur personnalisation des droits, mettre à true pour les réinitiliser
 *	@return bool true si la suppression s'est correctement déroulée, false dans le cas contraire
 */
function gu_profiles_rights_del( $prf, $rights=false, $reset=null ){
	if( !gu_profiles_exists($prf) ) return false;
	if( is_array($rights) && !sizeof($rights) ) return false;
	global $config;

	if( $rights!==false ){
		if( is_numeric($rights) ){
			if( $rights<=0 ) return false;
			$rights = array($rights);
		} else {
			foreach( $rights as $rgh ){
				if( !is_numeric($rgh) || $rgh<=0 ) return false;
			}
		}
	} elseif( $reset===false ) {
		// conserve les droits sur les comptes utilisateur
		$rusr = ria_mysql_query('select usr_id as id from gu_users where usr_tnt_id='.$config['tnt_id'].' and usr_prf_id='.$prf);
		if( !$rusr )
			return false;

		while( $usr = ria_mysql_fetch_array($rusr) ){
			$tmp = gu_users_rights_get_array($usr['id'], false);
			$ar_rgh = array();
			foreach( $tmp as $rgh )
				$ar_rgh[ $rgh ] = 'Oui';
			gu_users_rights_del( $usr['id'] );
			gu_users_rights_add( $usr['id'], $ar_rgh );
		}
	}

	$sql = '
		delete from gu_profiles_rights
		where prg_tnt_id='.$config['tnt_id'].'
			and prg_prf_id='.$prf.'
	';

	if( $rights!==false ) $sql .= ' and prg_rgh_id in ('.implode(', ', $rights).')';

	ria_debug_log('rights', array('sql'=>$sql, "old" =>  gu_profiles_rights_get_array( $prf, false, null, null)));

	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return ria_mysql_query( $sql );
}

/// @}

/** \defgroup model_users_rights Gestion des droits sur les comptes utilisateur
 * 	\ingroup auth
 *	Ce module comprend les fonctions nécessaires à la gestion des droits sur les comptes utilisateur.
 *
 *	@{
 */

/** Cette fonction permet de récupérer la liste des contraintes de droit propre à un utilisateur
 *	@param int $usr Optionnel, identifiant d'un utiliseteur
 *	@param string $code Optionnel, identifiant d'un code de droit
 *  @param bool $admin Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites, true pour compter seulement les droits admin
 *	@param bool $is_yuto Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites et admin, true pour compter seulement les droits yuto
 *	@return bool false en cas d'erreur
 *	@return resource un résultat MySQL contenant :
 *				- id : identifiant du droit
 *				- code : code du droit
 *				- name : nom du droit
 *				- desc : description du droit
 *
 * 	Droits retournés selon les paramètres admin et yuto :
 *
 *	admin			|0		|1		|0		|1		|null	|0		|null	|1		|null
 *	________________|_______|_______|_______|_______|_______|_______|_______|_______|_____
 *	yuto			|0		|0		|1		|1		|0		|null	|1		|null	|null
 *	________________|_______|_______|_______|_______|_______|_______|_______|_______|_____
 *	delete droits	|site 	|admin	|yuto	|aucun ?|admin	|yuto	|yuto	|admin	|tous
 *					|		|		|		|		|+site	|+site	|		|		|
 */
function gu_users_rights_get( $usr=0, $code='', $admin=false, $is_yuto=false ){
	if( $usr>0 && !gu_users_exists($usr) ) return false;
	global $config;

	$sql = '
		select rgh_id as id, rgh_code as code, rgh_name as name, rgh_desc as "desc"
		from gu_rights
			cross join gu_users
			left join gu_profiles_rights on ( rgh_id=prg_rgh_id and usr_tnt_id=prg_tnt_id and usr_prf_id=prg_prf_id )
			left join gu_users_rights on ( rgh_id=urg_rgh_id and usr_tnt_id=urg_tnt_id and usr_id=urg_usr_id )
		where usr_tnt_id='.$config['tnt_id'].'
			 and (rgh_tnt_id = 0 or rgh_tnt_id=usr_tnt_id)
			and (
				( urg_rgh_id is null and prg_rgh_id is not null ) or urg_allowed=1
			)
	';

	if( $usr>0 ) $sql .= '  and usr_id='.$usr;
	if( trim($code) ) $sql .= ' and rgh_code=\''.addslashes($code).'\'';

	if($admin === false){
		$sql .= ' and rgh_admin = 0 ';
	}elseif($admin){
		$sql .= ' and rgh_admin = 1 ';
	}

	if($is_yuto === false){
		$sql .= ' and rgh_is_yuto = 0 ';
	}elseif($is_yuto){
		$sql .= ' and rgh_is_yuto = 1 ';
	}

	$sql .= ' group by rgh_id';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer la liste des contraintes de droits d'accès propres à un utilisateur, sous forme de tableau.
 *	@param int $usr Obligatoire, identifiant d'un utilisateur existant dont on souhaite récupérer les droits
 *	@param bool $is_code Optionnel, par défaut le tableau contient les codes de droit, mettre à false pour récupérer les identifiants
 *  @param bool $admin Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites, true pour compter seulement les droits admin
 *	@param bool $is_yuto Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites et admin, true pour compter seulement les droits yuto
 *	@return array un tableau contenant soit l'identifiant soit le code des contraintes de droit
 */
function gu_users_rights_get_array( $usr, $is_code=true, $admin=false, $is_yuto=false ){
	if( !gu_users_exists($usr) ) return false;
	$ar_rights = array();

	$rights = gu_users_rights_get( $usr, '', $admin, $is_yuto );
	if( $rights ){

		while( $right = ria_mysql_fetch_array($rights) ){
			$ar_rights[] = $is_code ? $right['code'] : $right['id'];
		}

	}

	return $ar_rights;
}

/** Cette fonction permet de compter le nombre de droit utilisé pour un utilisateur.
 *	@param int $usr Obligatoire, identifiant d'un utilisateur
 *	@param int $cat Optionnel, permet de filtrer par catégorie de droit
 *  @param bool $admin Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites, true pour compter seulement les droits admin
 *	@param bool $is_yuto Optionnel, null pour compter tous les droits, false pour compter seulement les droits sur les sites et admin, true pour compter seulement les droits yuto
 *	@return bool false en cas d'erreur, sinon le nombre de droit utilisé
 */
function gu_users_rights_count( $usr, $cat=0, $admin=false, $is_yuto=false ){
	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !is_numeric($cat) || $cat<0 ) return false;
	global $config;

	$sql = '
		select 1
		from gu_rights
			cross join gu_users
			left join gu_profiles_rights on ( rgh_id=prg_rgh_id and usr_tnt_id=prg_tnt_id and usr_prf_id=prg_prf_id )
			left join gu_users_rights on ( rgh_id=urg_rgh_id and usr_tnt_id=urg_tnt_id and usr_id=urg_usr_id )
		where usr_tnt_id='.$config['tnt_id'].'
			and (rgh_tnt_id = 0 or rgh_tnt_id=usr_tnt_id)
			and usr_id='.$usr.'
			and (
				( urg_rgh_id is null and prg_rgh_id is not null ) or urg_allowed=1
			)
	';

	if( $cat>0 ) $sql .= ' and rgh_cat_id='.$cat;

	if($admin === false){
		$sql .= ' and rgh_admin = 0 ';
	}elseif($admin){
		$sql .= ' and rgh_admin = 1 ';
	}

	if($is_yuto === false){
		$sql .= ' and rgh_is_yuto = 0 ';
	}elseif($is_yuto){
		$sql .= ' and rgh_is_yuto = 1 ';
	}

	$sql .= ' 		group by rgh_id';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows( $res );
}

/** Cette fonction permet de vérifier l'existance d'une contrainte propre à un utilisateur.
 *	@param int $usr Obligatoire, identifiant d'un utilisateur
 *	@param $rgh Obligatoire, identifiant d'un droit
 *	@return bool true si la contrainte existe, false dans le cas contraire
 */
function gu_users_rights_exists( $usr, $rgh ){
	global $config;

	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !is_numeric($rgh) || $rgh<=0 ) return false;

	$res = ria_mysql_query('
		select 1
		from gu_users_rights
		where urg_tnt_id='.$config['tnt_id'].'
			and urg_usr_id='.$usr.'
			and urg_rgh_id='.$rgh.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet d'ajout des droits directement à un utilisateur existant.
 *	@param int $usr Obligatoire, identifiant d'un utilisateur
 *	@param $rights Obligatoire, tableau associatif contruit de cette façon array(id_right=>allowed[Oui|oui|Non|non|0|1],...)
 *	@return bool true si l'ajout s'est correctement passé, false dans le cas contraire
 */
function gu_users_rights_add( $usr, $rights ){
	if( !gu_users_exists($usr) ) return false;
	if( !is_array($rights) ) return false;
	global $config;

	foreach( $rights as $rgh=>$allowed ){
		if( !gu_rights_exists($rgh) ) return false;
		if( !in_array($allowed, array( 'Oui', 'oui', 'Non', 'non', '1', '0' )) ) return false;

		// si la contrainte de droit existe déjà, on met à jour l'information allowed
		if( gu_users_rights_exists($usr, $rgh) ){
			if( !gu_users_rights_set_allowed($usr, $rgh, $allowed) )
				return false;
			unset( $rights[$rgh] );
		}
	}

	if( !sizeof($rights) )
		return true;

	$sql = '
		insert into gu_users_rights
			( urg_tnt_id, urg_usr_id, urg_rgh_id, urg_allowed )
		values
	';

	$count = 1;
	foreach( $rights as $rgh=>$allowed ){
		$sql .= '( '.$config['tnt_id'].', '.$usr.', '.$rgh.', '.( in_array( $allowed, array('Oui', 'oui', '1') ) ? 1 : 0 ).' )'.( $count<sizeof($rights) ? ', ' : '' );
		$count++;
	}

	ria_debug_log('rights', array('sql'=>$sql, "old" => gu_users_rights_get_array($usr, false, null,null)));

	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'ajouter une contrainte à un droit pour un utilisateur existant.
 *	@param int $usr Obligatoire, identifiant d'un utilisateur
 *	@param $rgh Obliatoire, identifiant d'un droit
 *	@param $allowed Optionnel, par défaut il s'agit d'une contrainte à un droit, mettre true pour donner un droit particulier à un utilisateur
 *	@return bool true en cas de succès, false dans le cas contraire
 */
function gu_users_rights_add_constraint( $usr, $rgh, $allowed=false ){
	global $config;

	if( !gu_users_exists($usr) ) return false;
	if( !gu_rights_exists($rgh) ) return false;
	if( !in_array($allowed, array( 'Oui', 'oui', 'Non', 'non', '1', '0' )) ) return false;

	if( gu_users_rights_exists($usr, $rgh) ){
		if( !gu_users_rights_set_allowed($usr, $rgh, $allowed) ){
			return false;
		}
	}

	$res = ria_mysql_query('
		insert into gu_users_rights
			( urg_tnt_id, urg_usr_id, urg_rgh_id, urg_allowed )
		values
			( '.$config['tnt_id'].', '.$usr.', '.$rgh.', '.( in_array($allowed, array('Oui', 'oui', '1')) ? 1 : 0 ).' )
	');

	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return $res;
}

/** Cette fonction permet de mettre à jour l'information allowed d'une contrainte d'un droit pour un utilisateur existant.
 *	@param int $usr Obligatoire, identifiant d'un utilisateur
 *	@param int $rgh Obligatoire, identifiant d'un droit
 *	@param bool $allowed Obligatoire, s'il s'agit d'une contrainte à un droit ou un droit particulier donné à un utilisateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_rights_set_allowed( $usr, $rgh, $allowed ){
	global $config;

	if( !is_numeric($usr) || $usr<=0 ) return false;
	if( !is_numeric($rgh) || $rgh<=0 ) return false;
	if( !in_array($allowed, array( 'Oui', 'oui', 'Non', 'non', '1', '0' )) ) return false;

	$sql = '
		update gu_users_rights
		set urg_allowed='.( in_array($allowed, array('Oui', 'oui', '1')) ? 1 : 0 ).'
		where urg_tnt_id='.$config['tnt_id'].'
			and urg_usr_id='.$usr.'
			and urg_rgh_id='.$rgh.'
	';

	ria_debug_log('rights', array('sql'=>$sql, "old" => gu_users_rights_get_array($usr, false, null,null)));

	$res = ria_mysql_query($sql);


	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return $res;
}

/** Cette fonction permet de vider les contraintes de droits d'accès propres à un compte utilisateur.
 *	@param int $usr Obligatoire, identifiant d'un utilisateur existant
 *	@param int|array $rgh Optionnel, permet de se limiter à une seule ou plusieurs contraintes
 *	@param bool $admin Optionnel, false pour supprimer que les droits site et yuto, true pour supprimer que les droits admin, null pour tout supprimer (surcharge $rgh)
 *	@param bool $is_yuto Optionnel, false pour supprimer que les droits site et admin, true pour supprimer que les droits yuto, null pour tout supprimer (surcharge $rgh)
 * 	@param bool $linked_rights Optionnel, permet de récupérer les droits qui sont soit utilisés par l'administration soit par Yuto (par défaut à False)
 * 	@param bool $multiple_rgh Optionnel, permet de savoir si la variable $rgh passée en argument est un array ou un entier
 *	@return bool true en cas de succès, false dans le cas contraire
 *
 * 	Droits supprimés selon les paramètres admin et yuto :
 *
 *	admin						|0			|1			|0			|1			|null		|0			|null		|1			|null
 *	________________|_______|_______|_______|_______|_______|_______|_______|_______|_____
 *	yuto						|0			|0			|1			|1			|0			|null		|1			|null		|null
 *	________________|_______|_______|_______|_______|_______|_______|_______|_______|_____
 *	delete droits		|site 	|admin	|yuto		|aucun ?|admin	|yuto		|yuto		|admin	|tous
 *									|				|				|				|				|+site	|+site	|				|				|
 */
function gu_users_rights_del( $usr, $rgh=0, $admin=false, $is_yuto=false, $linked_rights=false, $multiple_rgh=false){
	global $config;

	// Vérifie les paramètres d'entrée
	if(
		!is_numeric($usr) || $usr<=0
		|| ((!is_numeric($rgh)  || $rgh<0) && !$multiple_rgh)
		|| (!is_array($rgh) && $multiple_rgh)
	){
		return false;
	}

	$sql = '
		delete from gu_users_rights
		where urg_tnt_id='.$config['tnt_id'].'
			and urg_usr_id='.$usr.'
	';

	if( $admin !== false || $is_yuto !== false ){
		$ar_rgh = gu_rights_get_array( 0, false, $admin, $is_yuto, $linked_rights);
		$sql .= ' and urg_rgh_id in ('.implode(',', $ar_rgh).')';
	}else{
		if ($multiple_rgh == false) {
			if( $rgh>0 ){
				$sql .= ' and urg_rgh_id='.$rgh;
			}
		}
		else {
			if (count($rgh) > 0) {
				$sql .= ' and urg_rgh_id in ('.implode(',', $rgh).')';
			}
		}
	}

	ria_debug_log('rights', array('sql'=>$sql, "old" => gu_users_rights_get_array($usr, false, null,null)));

	// Force la mise à jour des droits sur les applications mobiles
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'affecter des droits à un utilisateur. Si le droit est déjà affecté au profil, aucune ligne n'est ajoutée,
 *	si le droit n'est pas affecté au profil alors une ligne est ajoutée avec allowed à 1, si un droit présent dans le profil n'est
 *	pas affecté à l'utilisateur alors une ligne est ajoutée avec allowed à 0.
 *
 *	@param int $usr Obligatoire, identifiant d'un utilisateur existant
 *	@param array $rights Obligatoire, tableau d'identifiants de droits
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function gu_users_rights_affected( $usr, $rights ){

	// Vérifie les paramètres d'entrée
	if( !gu_users_exists($usr) || !is_array($rights) ){
		return false;
	}

	// Récupérer le profil de l'utilisateur
	$prf = gu_users_get_prf( $usr );
	if( !$prf ){
		return false;
	}

	// Récupérer le tableau des identifiants de droits du profil
	$rprf = gu_profiles_rights_get_array( $prf, false );

	$ar_rights = array();
	foreach( $rprf as $rgh ){
		if( !in_array($rgh, $rights) ){
			$ar_rights[ $rgh ] = 'Non';
		}
	}

	foreach( $rights as $rgh ){
		if( !in_array($rgh, $rprf) ){
			$ar_rights[ $rgh ] = 'Oui';
		}
	}

	// Supprime les contraintes actuelles
	if( !gu_users_rights_del($usr) ){
		return false;
	}

	return gu_users_rights_add( $usr, $ar_rights );
}

/** Cette fonction permet de savoir si un utilisateur peut utiliser ou non un droit.
 *	@param string|array $code Optionnel, code ou tableau de code de droits
 *	@param int $usr Optionnel, identifiant d'un utilisateur existant
 *	@param int $rgh Optionnel, identifiant d'un droit
 *	@param bool $one_true Optionnel, par défaut à False, mettre true pour retourné vrai si au moins un des droits est accessible au compte client (seulement si $code est un tableau, ignoré dans tous les autres cas)
 *	@return bool true si l'utilisateur peut utiliser le ou les droits donnés en paramètre, false dans le cas contraire
 */
function gu_users_rights_used( $code='', $usr=0, $rgh=0, $one_true=false ){
	global $config, $memcached;

	if( !isset($config['rights_is_active']) || !$config['rights_is_active'] ){
		return true;
	}

	if( !is_numeric($rgh) || $rgh<0 ){
		return false;
	}

	if( $rgh<=0 ){
		if( is_array($code) ){
			if( !sizeof($code) ){
				return false;
			}
		}elseif( trim($code) == '' ){
			return false;
		}
	}

	if( !is_numeric($usr) || $usr<0 ){
		return false;
	}

	if( $usr==0 ){
		if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==PRF_SELLER ){
			if( isset($_SESSION['admin_view_user']) && $_SESSION['admin_view_user']!='' ){
				$usr = $_SESSION['admin_view_user'];
			} elseif( isset($_SESSION['usr_id']) ){
				$usr = $_SESSION['usr_id'];
			}
		} elseif( isset($_SESSION['usr_id']) ){
			$usr = $_SESSION['usr_id'];
		}
	}

	if( $usr==0 ) return false;

	$k_code = is_array($code) ? implode(',', $code) : $code;
	$key_memcached = 'rgh-'.$config['tnt_id'].'-'.$config['wst_id'].'-'.$usr.'-'.$k_code.'-'.$rgh.'-'.$one_true;

	$have_right = $memcached->get($key_memcached);
	if( $config['env_sandbox'] || !ria_is_memcached_result_ok($memcached) ){
		if( is_array($code) && sizeof($code) ){
			$res_all = true;
			foreach( $code as $one_code ){
				$res = gu_users_rights_used( $one_code );

				if( $res && $one_true ){
					return true;
				}elseif( !$res && !$one_true ){
					return false;
				}

				if( !$res ){
					$res_all = false;
				}
			}

			return $res_all;
		}

		$sql = '
			select 1
			from gu_rights
				cross join gu_users
				left join gu_profiles_rights on ( rgh_id=prg_rgh_id and usr_tnt_id=prg_tnt_id and usr_prf_id=prg_prf_id )
				left join gu_users_rights on ( rgh_id=urg_rgh_id and usr_tnt_id=urg_tnt_id and usr_id=urg_usr_id )
			where usr_tnt_id='.$config['tnt_id'].'
				and (rgh_tnt_id = 0 or rgh_tnt_id=usr_tnt_id)
				and usr_id='.$usr.'
				and (
					( urg_rgh_id is null and prg_rgh_id is not null ) or urg_allowed=1
				)
		';

		if( trim($code) ) $sql .= ' and rgh_code=\''.addslashes($code).'\'';
		if( $rgh>0 ) $sql .= ' and rgh_id='.$rgh;

		$sql .= ' 		group by rgh_id';

		$res = ria_mysql_query( $sql );
		if( $res && ria_mysql_num_rows($res) ){
			$have_right = true;
		}else{
			$have_right = gu_users_is_tenant_linked($usr) ? false : true;
		}

		$memcached->set($key_memcached, $have_right, 900);
	}

	return $have_right;
}

/** Cette fonction permet de retirer un ou plusieurs droits droits sur le back-office d'un site web
 *	@param int $tnt Obligatoire, tenant du site
 *  @param int $wst Obligatoire, identifiant du site
 *	@param int|array $rights Optionnel, identifiant ou tableau d'identifiants de droits
 *	@return bool true si la suppression s'est correctement déroulée, false dans le cas contraire
 */
function wst_website_rights_del( $tnt, $wst, $rights=false){
	if( !tnt_tenants_exists($tnt)) return false;
	if( !wst_websites_exists($wst, $tnt)) return false;
	if( is_array($rights) && !sizeof($rights) ) return false;

	if( $rights!==false ){
		if( is_numeric($rights) ){
			if( $rights<=0 ){
				return false;
			}

			$rights = array($rights);
		} else {
			foreach( $rights as $rgh ){
				if( !is_numeric($rgh) || $rgh<=0 ){
					return false;
				}
			}
		}
	}

	$sql = '
		delete from wst_rights
		where wrg_tnt_id='.$tnt.'
			and wrg_wst_id='.$wst.'
	';

	if( $rights!==false ) $sql .= ' and wrg_rgh_id in ('.implode(', ', $rights).')';

	ria_debug_log('rights', array('sql'=>$sql, "old" => wst_website_rights_get_array($tnt, $wst, false, null, null, null)));

	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de tester si un site web dispose déja d'un droit .
 *	@param int $tnt Obligatoire, tenant du site web
 *	@param int $wst Obligatoire, identifiant du site web
 *  @param int $rgh Obligatoire, identifiant d'un droit
 *	@return bool true si le profile dispose déjà du droit, false dans le cas contraire
 */
function wst_website_rights_exists( $tnt, $wst, $rgh ){
	if( !is_numeric($tnt) || $tnt<=0 ) return false;
	if( !is_numeric($wst) || $wst<=0 ) return false;
	if( !is_numeric($rgh) || $rgh<=0 ) return false;

	$res = ria_mysql_query('
		select 1
		from wst_rights
		where wrg_tnt_id='.$tnt.'
			and wrg_wst_id='.$wst.'
			and wrg_rgh_id='.$rgh.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return true;
}

/** Cette fonction permet de remplacer les droits éxistant sur le back-office d'un site web.
 *	@param int $tnt Obligatoire, Tenant du site web
 *  @param int $wst Obligatoire, identifiant du site web
 *	@param int|array $rights Obligatoire, identifiant ou tableau d'identifiants de droit
 *	@param bool $is_yuto Optionnel, par défaut à False, mettre true pour dire que ce droit est utilisé sur l'application Yuto
 *	@return bool true si l'ajout s'est correctement déroulé, sinon false dans le cas contraire
 */
function wst_website_rights_add( $tnt, $wst, $rights, $is_yuto=false ){
	if( !tnt_tenants_exists($tnt)) return false;
	if( !wst_websites_exists($wst, $tnt)) return false;
	if( !is_numeric($rights) && !is_array($rights) ) return false;
	if( is_array($rights) && !sizeof($rights) ) return false;

	if( !wst_website_rights_del($tnt, $wst, false, $is_yuto) ){
		return false;
	}
	if( is_numeric($rights) ){
		if( $rights<=0 ) return false;
		if( wst_website_rights_exists($tnt, $wst, $rights) ) return true;
		$rights = array($rights);
	} else {
		foreach( $rights as $key=>$rgh ){
			if( !is_numeric($rgh) || $rgh<=0 ) return false;
		}
	}

	if( !sizeof($rights) ){
		return true;
	}

	$sql = '
		insert into wst_rights
			( wrg_tnt_id, wrg_wst_id, wrg_rgh_id, wrg_rgh_allowed )
		values
	';

	$count = 1;
	foreach( $rights as $rgh ){
		$sql .= '( '.$tnt.', '.$wst.', '.$rgh.', 1 )'.( $count<sizeof($rights) ? ', ' : '' );
		$count++;
	}

	ria_debug_log('rights', array('sql'=>$sql));

	if( !ria_mysql_query($sql) ){
		return false;
	}

	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return true;
}

/** Cette fonction permet de définir les droits par défaut pour un locataire sur l'un de ses sites.
 *  @param int $tnt_id Obligatoire, identifiant d'un locataire
 *  @param int $wst_id Obligatoire, identifiant d'un site
 *  @param string $package Obligatoire, type de formule choisi (valeurs acceptées : 'business' ou 'legacy')
 *  @param bool $sub Obligatoire, s'il faut activé la gestion de l'abonnement (VEL de Yuto)
 *  @return bool True en cas de succès, False dans le cas contraire
 */
function wst_websites_set_default_rights($tnt_id, $wst_id, $package, $sub){
	if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id <= 0 ){
		return false;
	}

	if( !in_array($package, array('business', 'legacy')) ){
		return false;
	}

  // Ci-dessous la liste des droits pour un tenant standard
  $rgh_default = array(
    1254, 1255, 2000, 2001, 2002, 2003, 2005, 3000, 3001, 3004, 3005, 3050, 3100, 3102, 5000, 5001, 5008, 6000, 6050, 6250, 6400, 7050, 7051, 7052,
    7053, 7058, 7105, 7109, 7130, 7250, 9560, 11100, 11150, 11152, 11153, 11154, 11160, 11200, 13000, 13001, 13002, 13003, 13004, 13005, 13006, 13007,
    13008, 13009, 13010, 13011, 13012, 13013, 13014, 13015, 13016, 13017, 13018, 13019, 13020, 13021, 13022, 1000112, 13023, 13024, 13025, 1000113, 13026, 13027, 13028,
    13029, 13030, 13031, 13032, 13033, 13034, 13035, 13036, 13037, 13038, 13039, 13040, 13041, 13042, 13043, 13044, 13045, 13046, 13047, 13048, 13049,
    13055, 13056, 1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1100, 1101, 1102, 1103, 1104, 1105,
    1150, 1151, 1152, 1153, 1154, 1200, 1201, 1202, 1250, 1251, 1252, 1253, 2004, 2050, 2051, 2052, 2053, 2100, 2101, 2102, 2103, 2104, 2105, 2150,
    2151, 2152, 2153, 3051, 3052, 3053, 4000, 4001, 4002, 4003, 4004, 5002, 5003, 5004, 5005, 5006, 5009, 5010, 5011, 5012, 5013, 5050, 5051, 5052,
    5053, 5054, 5055, 5056, 5057, 5100, 6100, 6101, 6102, 6103, 6104, 6401, 6402, 6403, 6404, 6405, 6600, 6601, 6602, 6603, 6604, 6605, 6606, 6607,
    6610, 7000, 7100, 7101, 7102, 7103, 7104, 7106, 7107, 7108, 7110, 7111, 7112, 7113, 7122, 7123, 7124, 7125, 7126, 7127, 7128, 7129, 7131, 7150,
    7200, 7201, 7202, 7203, 7204, 7205, 7206, 7207, 7208, 7209, 7210, 7211, 7212, 7213, 7214, 7215, 7216, 7217, 7218, 7219, 7220, 7221, 7222, 7223,
    7224, 7225, 7251, 7252, 7253, 7254, 7300, 7301, 7302, 7303, 7304, 7305, 7306, 7307, 7350, 7400, 7450, 7600, 7650, 7700, 7701, 7702, 7800, 7801,
    7802, 7803, 9000, 9001, 9002, 9100, 9150, 9151, 9152, 9300, 9301, 9350, 9400, 11000, 11001, 11002, 11003, 11050, 11101, 11102, 11151, 11250, 12050, 12100
  );

  // Ci-dessous la liste des droits pour un tenant avec une gestion d'abonnement pour un Yuto Business
  if( $package == 'business' ){
    $rgh_default = array(
      1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1100, 1101, 1102, 1103, 1104, 1105, 1150, 1151, 1152, 1153,
      1154, 1200, 1201, 1202, 1250, 1251, 1252, 1253, 1254, 1255, 2000, 2001, 2002, 2003, 2004, 2005, 2050, 2051, 2052, 2053, 2100, 2101, 2102, 2103, 2104, 2105,
      2150, 2151, 2152, 2153, 3000, 3001, 3004, 3005, 3050, 3051, 3052, 3053, 4000, 4001, 4002, 4003, 4004, 5000, 5001, 5002, 5003, 5004, 5005, 5008, 5009, 5010,
      5011, 5012, 5013, 5050, 5051, 5052, 5053, 5054, 5055, 5056, 5057, 6600, 6601, 6603, 6604, 6605, 6606, 6607, 6610, 7000, 7050, 7051, 7052, 7053, 7058, 7100,
      7101, 7102, 7103, 7104, 7105, 7106, 7107, 7108, 7109, 7110, 7111, 7112, 7113, 7122, 7123, 7124, 7125, 7126, 7127, 7128, 7129, 7130, 7131, 7200, 7205, 7206,
      7207, 7208, 7209, 7210, 7211, 7212, 7213, 7214, 7215, 7216, 7217, 7218, 7219, 7220, 7221, 7222, 7223, 7224, 7225, 7250, 7251, 7252, 7253, 7254, 7300, 7301,
      7302, 7303, 7304, 7305, 7306, 7307, 7350, 7400, 7450, 7600, 9000, 9001, 9002, 9100, 9150, 9151, 9152, 9300, 9301, 9350, 9400, 9560, 11000, 11001, 11002, 11003, 11050,
      11100, 11101, 11102, 11150, 11151, 11152, 11153, 11154, 11160, 11200, 12050, 13000, 13001, 13002, 13003, 13004, 13005, 13006, 13007, 13008, 13009, 13010,
      13011, 13013, 13014, 13015, 13016, 13017, 13018, 13019, 13020, 13021, 13022, 1000112, 13023, 13024, 13025, 1000113, 13026, 13027, 13028, 13029, 13030, 13031, 13032, 13033, 13034,
	  13035, 13037, 13038, 13039, 13040, 13041, 13042, 13043, 13044, 13045, 13046, 13047, 13048, 13049, 13055, 13056, 13058, 13059, 13060, 13061, 13062, 13012,
	  11250
    );
  }

  // Si la gestion de l'abonnement est activé, alors les droits liés à ces interfaces sont ajoutés
  if( $sub ){
      $rgh_default = array_merge($rgh_default, array(1000029));
  }

  // Suppression des droits existants
  wst_website_rights_del( $tnt_id, $wst_id, false );

	// Ajout des droits par défaut
  return wst_website_rights_add($tnt_id, $wst_id, $rgh_default );
}

/** Cette fonction permet de récupérer les droits sur le back-office assignés à un signe web
 *	@param $tnt Obligatoire, tenant du site
 *  @param int $wst Obligatoire, identifiant du site web
 *	@return bool false en cas d'erreur
 *	@return resource un résultat MySQL contenant :
 *				- id : identifiant du droit
 *				- code : code du droit
 *				- name : nom du droit
 *				- desc : description du droit
 */
function wst_website_rights_get( $tnt, $wst ){
	if( !is_numeric($tnt) || $tnt<=0 ) return false;
	if( !is_numeric($wst) || $wst<=0 ) return false;

	return ria_mysql_query('
		select rgh_id as id, rgh_code as code, rgh_name as name, rgh_desc as "desc"
			from gu_rights, wst_rights
			where wrg_tnt_id = '.$tnt.'
			and wrg_wst_id= '.$wst.'
			and wrg_rgh_id = rgh_id
			and wrg_rgh_allowed = 1
			and (rgh_admin=1 or rgh_is_yuto = 1)
	');

}

/** Cette fonction permet de récupérer les codes ou les identifiants des droits sur le back-office assignés à un site web sous forme de tableau
 *	@param int $tnt Obligatoire, tenant du site
 *  @param int $wst Obligatoire, identifiant du site web
 *	@param bool $is_code Optionnel, par défaut le tableau retourné contient les codes, mettre à false pour récupérer les identifiants
 *  @param int $parent Optionnel, identifiant d'un droit parent (null pour récupérer les droits de premier niveau)
 * 	@param bool $is_admin Optionnel, par défaut seul les droits admin sont récupérer, mettre False pour les ignorés
 * 	@param bool $is_yuto Optionnel, par défaut les droits Yuto seulement sont exclus, mettre True pour les inclures
 * 	@param bool $linked_rights Optionnel, permet de récupérer les droits qui sont soient utilisés par l'administration soit par Yuto (par défaut à False)
 *	@return array Un tableau contenant les codes ou identifiants des droits d'un site web
 */
function wst_website_rights_get_array( $tnt, $wst, $is_code=true, $parent=null, $is_admin=true, $is_yuto=false, $linked_rights=false){

	// Vérifie les paramètres d'entrée
	if(
		!is_numeric($tnt) || $tnt<=0
		|| !is_numeric($wst) || $wst<=0
	){
		return false;
	}

	$rights = array();
	$r_right = gu_rights_get( 0, '', 0, array(), $is_admin, $parent, true, false, $is_yuto, $linked_rights);
	while( $right = ria_mysql_fetch_assoc($r_right) ){
		$sql = 'select wrg_rgh_id as id, rgh_code as code
			from wst_rights, gu_rights
			where wrg_tnt_id = '.$tnt.'
			and wrg_rgh_allowed = 1
			and rgh_id = wrg_rgh_id
			and wrg_rgh_id = '.$right['id'].'';

		if ($wst != -1) {
			$sql .= ' and wrg_wst_id ='.$wst.' ';
		}

		$res = ria_mysql_query($sql);
		if( $res && ria_mysql_num_rows($res) ){
			$r = ria_mysql_fetch_assoc($res);
			$rights[$r['id']] = $is_code ? $r['code'] : $r['id'];
			//Vérifie les droits enfants
			$rights = array_merge($rights, wst_website_rights_get_array($tnt, $wst, $is_code, $r['id'], $is_admin, $is_yuto, $linked_rights));
		}
	}

	return $rights;
}

/**	Cette fonction génère une erreur 403 si l'utilisateur en cours n'a pas l'autorisation passée en argument
 *  @param string|array $code Obligatoire, code ou tableau de codes du droit d'accès à contrôler
 * 	@param bool $all Optionnel, si oui ou non tous les codes doivent être autorisés (par défaut à true)
 * 	@return void
 */
function gu_if_authorized_else_403( $code, $all=true ){
	global $config;

	if( !is_array($code) ){
		$code = array( $code );
	}

	$one_access = false;
	$have_access = true;

	foreach( $code as $c ){
		// Si le code porte sur un module, la fonction de contrôle d'accès est différente de s'il porte sur un droit
		if( substr($c, 0, 4) == '_MDL' ){
			if( !gu_users_admin_rights_used($c) ){
				$have_access = false;
			}else{
				$one_access = true;
			}
		}else{
			// Vérifie que l'utilisateur en cours peut consulter les catégories du catalogue
			if( !gu_user_is_authorized( $c ) ){
				$have_access = false;
			}else{
				$one_access = true;
			}
		}

		// Pas besoin de vérifier tous les codes si tous doivent être accessible et qu'un accès n'est pas autorisé
		if( $all && !$have_access ){
			break;
		}

		// Pas besoin de vérifier tous les codes si au moins un code doit être accessible et qu'un l'est
		if( !$all && $one_access ){
			break;
		}
	}

	// Si au moins un code est validé et qu'il ne les faut pas tous, alors l'accès est autorisé
	if( !$all && $one_access ){
		$have_access = true;
	}

	// Gestion d'une erreur 403 dans le cas où l'utilisateur n'a pas accès à l'argument en paramètre
	if( !$have_access ){
		http_403();
		exit;
	}
}

/** Cette fonction permet de vérifer que l'utilisateur à accès au droit demandé
 *  @param string $code Obligatoire, code du droit d'accès à contrôler
 * 	@return bool retourne vrai si l'utilisateur possède le droit passé en paramètre, false dans le cas contraire
 *	@see gu_users_rights_used
 * 	@see https://riastudio.atlassian.net/browse/RSADMIN-338
 */
function gu_user_is_authorized( $code ){
	global $config;

	// Vérifie les paramètres d'entrée
	if( !$code ){
		return false;
	}

	// Charge les droits d'accès à l'interface d'administration
	$admin_rights = gu_users_load_admin_rights( true );
	if ( !is_array($admin_rights) || !count($admin_rights) ) {
		return false;
	}

	// Vérifie que l'utilisateur en cours à bien le droit d'utiliser la fonction
	return in_array( $code, $admin_rights );

}

/** Cette fonction permet de vérifier que l'utilisateur à accès à un menu donnée
 *  @param $code_menu Obligatoire, code d'un menu
 *  @return bool true si l'utilisateur courant à le droit d'accès, false dans le cas contraire
 * 	@see https://riastudio.atlassian.net/browse/RSADMIN-338
 */
function gu_users_admin_rights_used( $code_menu ){
	global $config;

	if(!$code_menu){
		return false;
	}

	//Le menu option doit toujours être visible pour les utilisateurs ayant le tenant 0 car il comporte la partie de gestion des droits d'accès
	if( $code_menu == '_MDL_OPTIONS' && $config['USER_RIASTUDIO'] ){
		return true;
	}

	// Charge les droits d'accès du compte dans l'administration
	$admin_rights = gu_users_load_admin_rights();

	// Charge les droits nécessaire à chaque entrée de menu
	$menu_rights = adn_menu_get_rights();

	// Si le module en paramètre n'existe pas, la fonction retourne false
	if( !isset($menu_rights[$code_menu]) ){
		return false;
	}

	if( isset($menu_rights[$code_menu]['rights']) && is_array($menu_rights[$code_menu]['rights']) ){
		// Vérification des droits admin
		$diff = array_diff( $menu_rights[$code_menu]['rights'], $admin_rights );
		if( count($diff) ){
			if( !$menu_rights[$code_menu]['one_true'] ){
				return false;
			}else{
				if( count($diff)==count($menu_rights[$code_menu]['rights']) ){
					return false;

				}
			}
		}
	}

	switch($code_menu){
		case '_MENU_OPTIONS_RIGHTS':{
			if ( !isset($config['USER_RIASTUDIO'], $_SESSION['usr_email']) || (!$config['USER_RIASTUDIO'] && $_SESSION['usr_email'] != '<EMAIL>')) {
				return false;
			}
		}
		case '_MENU_TOOLS_MARKETING':{
			if( !isset($config['marketing_is_active']) || !$config['marketing_is_active'] ){
				return false;
			}
		}
	}

	return true;
}

/** Cette fonction permet de vérifier que l'utilisateur à bien le droit d'accéder a un module
 *  Redirige automatiquement l'utilisateur si il n'a pas le droit d'accéder au module
 *  @return void
 */
function gu_rights_admin_accessibility(){

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	switch(true){
		case strstr($_SERVER['PHP_SELF'], '/admin/catalog'):
			gu_if_authorized_else_403('_MDL_CATALOG');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/customers'):
			gu_if_authorized_else_403('_MDL_CUSTOMERS');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/orders'):
			gu_if_authorized_else_403('_MDL_ORDERS');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/promotions'):
			gu_if_authorized_else_403('_MDL_PROMO');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/documents'):
			gu_if_authorized_else_403('_MDL_DOCS');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/tools'):
			gu_if_authorized_else_403('_MDL_TOOLS');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/config'):
			gu_if_authorized_else_403('_MDL_CONFIG');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/sync'):
			gu_if_authorized_else_403('_MDL_SYNC');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/moderation'):
			gu_if_authorized_else_403('_MDL_MOD');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/stats'):
			gu_if_authorized_else_403('_MDL_STATS');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/comparators'):
			gu_if_authorized_else_403('_MDL_COMPARATORS');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/fdv'):
			gu_if_authorized_else_403('_MDL_FDV');
			break;
		case strstr($_SERVER['PHP_SELF'], '/admin/options'):
			gu_if_authorized_else_403('_MDL_OPTIONS');
			break;
	}
}


// \cond onlyria
/**	Cette fonction enregistre une relation parent/enfant dans la table de hiérarchie des droits.
 *
 *	@param int $parent Obligatoire, Identifiant du droit parent
 *	@param int $child Obligatoire, Identifiant du droit enfant
 *
 */
function gu_rights_hierarchy_add( $parent, $child ){

	// Vérifie les paramètres d'entrée
	if(
		!is_numeric($parent) || $parent<=0
		|| !is_numeric($child) || $child<=0
	){
		return false;
	}

	// Ajoute le droit en tant qu'enfant direct de son droit parent
	$depth = gu_rights_depth_get($parent);
	ria_mysql_query('
		insert into gu_rights_hierarchy
			(rgh_tnt_id,rgh_parent_id,rgh_child_id,rgh_parent_depth)
		values
			(0,'.$parent.','.$child.','.$depth.')
	');

	// Maintient à jour la hiérarchie indirecte
	$gparents = gu_rights_parents_get($parent);
	while( $r = ria_mysql_fetch_array($gparents) ){
		ria_mysql_query('
			insert into gu_rights_hierarchy
				(rgh_tnt_id,rgh_parent_id,rgh_child_id,rgh_parent_depth)
			values
				(0,'.$r['id'].','.$child.','.$r['depth'].')
		');
	}

	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return true;
}
// \endcond

// \cond onlyria
/** Permet la suppression d'une relation parent/enfant dans la table de hiérarchie des droits.
 *
 *	@param int $child Obligatoire, Identifiant du droit enfant de la relation
 *
 */
function gu_rights_hierarchy_del( $child ){

	// Vérifie les paramètres d'entrée
	if( !is_numeric($child) || $child<=0 ){
		return false;
	}

	// force la mise à jour des droits sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_RIGHTS);

	return ria_mysql_query('delete from gu_rights_hierarchy where rgh_tnt_id=0 and rgh_child_id='.$child);
}
// \endcond

// \cond onlyria
/**	Reconstruit la hiérarchie complète des droits admin. La reconstruction se fait de manière hiérarchique, en partant
 *	des droits de premier niveau pour descendre vers les droits les plus profondes.
 *
 *	@param int $parent Facultatif, point de départ de la reconstruction. Pour une reconstruction complète, laisser vide.
 *	@param bool $forced Facultatif, booléen indiquant si la recontruction doit être forcée ou non. Indiquer vrai si l'argument $parent est supérieur à 0.
 * 	@param bool $link_tenant Optionnel, par défaut les droits liés au tenant seront aussi récupérés, mettre false pour ne récupérer que les droits globaux
 *
 */
function gu_rights_hierarchy_rebuild( $parent=0, $forced=false, $link_tenant=true ){

	// Vérifie les paramètres d'entrée
	if( !is_numeric($parent) || $parent<0 ){
		return false;
	}

	// Le vidage de la table de hiérarchie ne se fait qu'au premier appel
	if( $parent==0 ){
		ria_mysql_query('delete from gu_rights_hierarchy where rgh_tnt_id=0');
	} elseif( $forced ){
		ria_mysql_query('delete from gu_rights_hierarchy where rgh_tnt_id=0 and rgh_parent_id='.$parent);
	}

	// Reconstruit la hiérarchie pour tous les droits enfants
	$rights = gu_rights_get( 0, '', 0, array(), true, $parent, $link_tenant, false, false, true );

	while( $r = ria_mysql_fetch_array($rights) ){
		gu_rights_hierarchy_add( $r['parent_id'], $r['id'] );
		gu_rights_hierarchy_rebuild( $r['id'], false, $link_tenant );
	}

}
// \endcond

/**	Retourne la profondeur d'un droit dans l'arborescence.
 *	Les droits de plus haut niveau appartiennent au niveau 0, et ainsi de suite.
 *	Les valeurs de profondeur sont absolues.
 *
 *	@param int $id Obligatoire, Identifiant du droit
 *
 *	@return int La profondeur du droit dans l'arborescence
 */
function gu_rights_depth_get( $id ){

	if( !is_numeric($id) || $id<0 ){
		return false;
	}
	if( $id==0 ){
		return 0;
	}

	return ria_mysql_num_rows(gu_rights_parents_get($id));
}

/** Retourne l'ensemble des droits enfant du droit passée en argument
 *
 * @param int $id Obligatoire, identifiant du droit dont on souhaite trouver les parents
 *
 * @return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 * 			- id : l'identifiant du droit
 *			- name : nom du droit
 *			- code : code du droit
 */
function gu_rights_childs_get( $id ){
	if(!is_numeric($id) || $id < 0){
		return false;
	}

	global $config;

	$sql = 'select
				rgh_id as id, rgh_name as name, rgh_code as code
			from gu_rights_hierarchy, gu_rights
			where gu_rights_hierarchy.rgh_tnt_id = 0
				and gu_rights.rgh_tnt_id = 0
				and gu_rights_hierarchy.rgh_child_id = rgh_id
				and gu_rights_hierarchy.rgh_parent_id = '.$id.'
		';

	return ria_mysql_query( $sql );

}

/**	Retourne l'ensemble des droits parent du droit passée en argument, triées par niveau décroissant.
 *	@param int $id Obligatoire, identifiant du droit dont on souhaite trouver les parents.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : l'identifiant du droit
 *			- name : nom du droit
 *			- code : code du droit
 *			- parent_id : id du droit parent
 *			- depth : la profondeur de la catégorie dans l'arborescence
 */
function gu_rights_parents_get( $id ){
	if(!is_numeric($id) || $id < 0){
		return false;
	}

	global $config;

	$sql = 'select
				rgh_id as id, rgh_name as name, rgh_code as code, gu_rights.rgh_parent_id as parent_id, rgh_parent_depth as depth
			from gu_rights_hierarchy, gu_rights
			where gu_rights_hierarchy.rgh_tnt_id = 0
				and gu_rights.rgh_tnt_id = 0
				and gu_rights_hierarchy.rgh_parent_id = rgh_id
				and rgh_child_id = '.$id.'
		';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de savoir combien d'enfants à un droit d'accès
 * @param int $id Obligatoire, Identfiant du droit
 * @return int le nombre de droits enfants, false en cas d'erreur
 */
function gu_rights_count_childs( $id ){
	if(!is_numeric($id) || $id < 0){
		return false;
	}

	$res = ria_mysql_query('select count(*) from gu_rights_hierarchy where rgh_parent_id = '.$id);

	return ria_mysql_result($res, 0, 0);
}

/** Cette fonction permet de savoir la profondeur maximum de la hiérarchie des droits admin
 *
 * @return int la profondeur maximum de la hiérarchie des droits admin
 */
function gu_rights_get_max_depth(){

	$res = ria_mysql_query('select max(rgh_parent_depth) from gu_rights_hierarchy');

	return ria_mysql_result($res, 0, 0);
}

// \cond onlyria
/** Cette fonction permet de récupérer les ids des droits visibles par un utilisateur
 * 	@param int $tnt Obligatoire, tenant du site web
 * 	@param int $wst Obligatoire, identifiant du site web
 * 	@param bool $usr Optionnel, true pour afficher seulement les droits auquels l'administrateur à accès
 * 	@param bool $is_yuto Optionnel, par défaut les droits Yuto seulement sont exclus, mettre True pour les afficher également
 * 	@param array $result Obligatoire, tableau contenant les Ids des droits visibles
 * 	@return void Modifie le contenu de $result passé en argument
 */
function gu_rights_get_all_rights_visible($tnt, $wst, $usr=false, $is_yuto=false, &$result){
	global $config;
	// Vérifie les paramètres d'entrée
	if(
		!is_numeric($tnt) || $tnt<=0
		|| !is_numeric($wst) || $wst<=0
	){
		return false;
	}
	if ($is_yuto) {
		$r_module = gu_categories_rights_get(0, null, true, false);
	} else {
		$r_module = gu_categories_rights_get(0, true, true, true);
	}

	while( $module = ria_mysql_fetch_assoc($r_module) ){
		if ($is_yuto) {
			$nb_rights = gu_categories_rights_count_rights( $module['id'], null, $usr, true, false);
		} else {
			$nb_rights = gu_categories_rights_count_rights( $module['id'], true, $usr, false, true);
		}

		if( $nb_rights == 0 ){
			continue;
		}

		if ($is_yuto) {
			$r_right = gu_rights_get( 0, '', $module['id'], array(), null, null, true, $usr, true, false);
		} else {
			$r_right = gu_rights_get( 0, '', $module['id'], array(), true, null, true, $usr, false, true);
		}
		while( $right = ria_mysql_fetch_assoc($r_right) ){
			if( $right['id'] == 1000029 ){
				// La gestion d'abonnement Yuto n'est accessible que pour les Business
				if( !in_array(getenv('ENVRIA_PACKAGE'), ['business']) ){
					continue;
				}
			}
			if( $right['id'] == 1000068 ){
				// La gestion d'abonnement RiaShop n'est accessible que pour les RiaShop Essentiel ou Business
				if( !in_array(getenv('ENVRIA_PACKAGE'), ['essentiel', 'business']) ){
					continue;
				}
			}
			array_push($result, $right['id']);
			gu_rights_get_all_subrights_visible( $module['name'], $right['id'], 1, $usr, $is_yuto , $result);
		}
	}
}
// \endcond


// \cond onlyria
/** Cette fonction permet de récupérer les ids des droits visibles par un utilisateur
 * @param $module Obligatoire, nom du module auquel appartient le droit
 * @param $parent Obligatoire, parent des droits que l'on veut afficher
 * @param $depth Obligatoire, Profondeur du droit dans la hierarchie
 * @param int $usr Optionnel, true pour afficher seulement les droits auquels l'administrateur à accès
 * @param $is_yuto Optionnel, par défaut les droits Yuto seulement sont exclus, mettre True pour les inclures
 * @param array $result Obligatoire, tableau contenant les Ids des droits visibles
 * @return void Modifie le contenu de $result passé en argument
 */
function gu_rights_get_all_subrights_visible($module, $parent, $depth, $usr=false, $is_yuto=false, &$result){
	if ($is_yuto) {
		$r_right = gu_rights_get( 0, '', $module, array(), null, $parent, true, $usr, true, false);
	} else {
		$r_right = gu_rights_get( 0, '', $module, array(), true, $parent, true, $usr, false, true);
	}
	if( !$r_right || !ria_mysql_num_rows($r_right) ){
        return;
    }

    while( $right = ria_mysql_fetch_assoc($r_right) ){
		array_push($result, $right['id']);
		gu_rights_get_all_subrights_visible($module, $right['id'], $depth+1, $usr, $is_yuto, $result);
    }
}
// \endcond
// \cond onlyria
/** Cette fonction permet d'afficher l'interface de gestion des droits administrateur
 * 	@param int $tnt Obligatoire, tenant du site web
 * 	@param int $wst Obligatoire, identifiant du site web
 * 	@param bool $usr Optionnel, true pour afficher seulement les droits auquels l'administrateur à accès
 * 	@param bool $is_yuto Optionnel, par défaut les droits Yuto seulement sont exclus, mettre True pour les inclures
 * 	@return string Le code HTML de l'interface de gestion des droits administrateur
 */
function view_admin_rights($tnt, $wst, $usr=false, $is_yuto=false){
	global $config;
	if( !is_numeric($tnt) || $tnt<=0 ) return false;
	if( !is_numeric($wst) || $wst<=0 ) return false;
	$submit_name = $is_yuto ? 'save-yuto-rights' : 'save-admin-rights';
	$rgh_name = $is_yuto ? 'rgh-yuto' : 'rgh';
	ob_start();?>

	<table id="rights">
    <caption><?php print _("Liste des droits d'accès ".($is_yuto ? 'Yuto' : 'RiaShop')); ?></caption>
		<thead>
			<tr>
				<th class="align-left th-rights">
					<a class="check_all_rights" href="#"><?php print _('Tout cocher'); ?></a> |
					<a class="uncheck_all_rights" href="#"><?php print _('Tout décocher'); ?></a>
				</th>
				<th class="align-right th-rights">
					<input type="submit" name="<?php echo $submit_name?>" id="save-header" value="<?php print _('Enregistrer'); ?>" class="btn-main" />
					<input type="submit" name="cancel" id="cancel-header" value="<?php print _('Annuler'); ?>" class="btn-cancel" />
				</th>
			</tr>
		</thead>
		<tbody class="head-second"><?php
			if ($is_yuto) {
				$r_module = gu_categories_rights_get(0, null, true, false);
			} else {
				$r_module = gu_categories_rights_get(0, true, true, true);
			}

    	while( $module = ria_mysql_fetch_assoc($r_module) ){
				if ($is_yuto) {
					$nb_rights = gu_categories_rights_count_rights( $module['id'], null, $usr, true, false);
				} else {
					$nb_rights = gu_categories_rights_count_rights( $module['id'], true, $usr, false, true);
				}

				if( $nb_rights == 0 ){
					continue;
				}?>

        <tr>
					<th colspan="2">
						<span title="<?php print htmlspecialchars( sprintf( _('Cette section permet de gérer les droits sur le module %s'), $module['name'] ) ); ?>"><?php print htmlspecialchars(_($module['name'])); ?> </span>
						(<a class="check_all" module="<?php print $module['name']; ?>" href="#"><?php print _('Tout cocher'); ?></a> |
						<a class="uncheck_all" module="<?php print htmlspecialchars($module['name']); ?>" href="#"><?php print _('Tout décocher'); ?></a>)
					</th>
				</tr><?php

				if ($is_yuto) {
					$r_right = gu_rights_get( 0, '', $module['id'], array(), null, null, true, $usr, true, false);
				} else {
					$r_right = gu_rights_get( 0, '', $module['id'], array(), true, null, true, $usr, false, true);
				}

        $first_col = true;
        $a = 0;?>

        <tr>
					<td><?php
						while( $right = ria_mysql_fetch_assoc($r_right) ){
							if( $right['id'] == 1000029 ){
								// La gestion d'abonnement Yuto n'est accessible que pour les Yuto Business
								if( !in_array(getenv('ENVRIA_PACKAGE'), ['business']) ){
									continue;
								}
							}
							if( $right['id'] == 1000068 ){
								// La gestion d'abonnement RiaShop n'est accessible que pour les RiaShop Essentiel ou Business
								if( !in_array(getenv('ENVRIA_PACKAGE'), ['essentiel', 'business']) ){
									continue;
								}
							}
							$a++;
							$has_child = gu_rights_count_childs($right['id']);

							$depend_on = false;
							if (is_array($right['depend_on']) && count($right['depend_on'])) {
								$depend_on = implode(' ', json_decode($right['depend_on']));
							}

							if( $a > $nb_rights/2 && $first_col ){ ?>
								</td>
								<td><?php
								$first_col = false;
							}?>

							<ul>
								<li>
									<input type="checkbox" <?php print ($has_child)? 'class="has_child_0"': ''; ?> id="<?php print $right['id'] ?>" module="<?php print $module['name'] ?>" <?php print ($depend_on)? 'depend-on="'.$depend_on.'" depend-on-all="'.$right['depend_on_all'].'"': '' ;?> name="<?php echo $rgh_name?>[<?php print $right['id']; ?>]" value="<?php print $right['id'] ?>" desc="<?php print $right['desc']; ?>" />
									<label for="<?php print $right['id']; ?>" title="<?php print htmlspecialchars($right['desc']); ?>"><?php print htmlspecialchars(_($right['name'])); print view_admin_rights_label($right['admin'], $right['is_yuto']); ?></label>
								</li>

								<?php print view_admin_subright( $module['name'], $right['id'], 1, $usr, $is_yuto );?>

							</ul><?php
							$a = $a + gu_rights_count_childs( $right['id'] );
						}

						// Force la création de la deuxieme colonne du tableau
						if( $first_col ){ ?>
							</td>
							<td>
						<?php }
					} ?>
				</td>
			</tr>
    </tbody>
    <tfoot>
		<tr>
			<td colspan="2">
				<input type="submit" name="<?php echo $submit_name?>" id="save-footer" value="<?php print _('Enregistrer'); ?>" class="btn-main" />
            	<input type="submit" name="cancel" id="cancel-footer" value="<?php print _('Annuler'); ?>" class="btn-cancel" />
			</td>
		</tr>
	</tfoot>
	</table>

	<input type="hidden" id="max-depth" value="<?php print gu_rights_get_max_depth()?>" />
	<?php

    return ob_get_clean();
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'afficher l'interface de gestion des droits administrateur
 * @param $module Obligatoire, nom du module auquel appartient le droit
 * @param $parent Obligatoire, parent des droits que l'on veut afficher
 * @param $depth Obligatoire, Profondeur du droit dans la hierarchie
 * @param int $usr Optionnel, true pour afficher seulement les droits auquels l'administrateur à accès
 * 	@param $is_yuto Optionnel, par défaut les droits Yuto seulement sont exclus, mettre True pour les inclures
 * @return string Le code HTML de l'interface de gestion des droits administrateur pour le menu passé en paramètre
 */
function view_admin_subright($module, $parent, $depth, $usr=false, $is_yuto=false){
    ob_start();

	if ($is_yuto) {
		$r_right = gu_rights_get( 0, '', $module, array(), null, $parent, true, $usr, true, false);
	} else {
		$r_right = gu_rights_get( 0, '', $module, array(), true, $parent, true, $usr, false, true);
	}
	$rgh_name = $is_yuto ? 'rgh-yuto' : 'rgh';
	if( !$r_right || !ria_mysql_num_rows($r_right) ){
        return ob_get_clean();
    }else{
        ?><ul><?php
    }
    while( $right = ria_mysql_fetch_assoc($r_right) ){
        $has_child = gu_rights_count_childs($right['id']);

        $depend_on = false;
        if( $right['depend_on'] ){
            $depend_on = implode(json_decode($right['depend_on']), ' ');
        }?>

        <li>
						<input type="checkbox" <?php print ($has_child)? 'class="has_child_'.$depth.'"': ''; ?> id="<?php print $right['id']; ?>"
							module="<?php print $module ?>" parent="<?php print $parent ?>"
							name="<?php echo $rgh_name?>[<?php print $right['id']; ?>]"
							value="<?php print $right['id'] ?>"
							desc="<?php print $right['desc']; ?>"
							<?php print ($depend_on)? 'depend-on="'.$depend_on.'" depend-on-all="'.$right['depend_on_all'].'"': '' ;?>
						/>
            <label for="<?php print $right['id']; ?>" title="<?php print $right['desc']; ?>"><?php print $right['name']; print view_admin_rights_label($right['admin'], $right['is_yuto']); ?></label><br/>
        </li>

        <?php print view_admin_subright($module, $right['id'], $depth+1, $usr, $is_yuto);
    }?>

    </ul><?php

    return ob_get_clean();
}
// \endcond

// \cond onlyria
/** Cette fonction retourne une chaine de caractère indiquant le lien du droit avec RiaShop, Yuto ou les deux.
 * 	@param bool $is_admin Si le droit est utilisé dans l'administration
 * 	@param bool $is_yuto Si le droit est utilisé dans l'application Yuto
 * 	@return string Un descriptif pour l'entête des droits :
 * 		- Yuto si yuto
 * 		- RiaShop si RiaShop
 * 		- RiaShop et Yuto si les deux
 */
function view_admin_rights_label($is_admin, $is_yuto) {
	if ($is_admin && !$is_yuto) {
		return "<span class='rgh-admin'> (RiaShop)</span>";
	} elseif (!$is_admin && $is_yuto) {
		return "<span class='rgh-yuto'> (Yuto)</span>";
	} else {
		return " (<span class='rgh-admin'>RiaShop </span><span class='rgh-admin-underline'/>et</span><span class='rgh-yuto'> Yuto</span>)";
	}
}
// \endcond

/// @}
