<?xml version="1.0" encoding="UTF-8"?>
<svg width="46px" height="19px" viewBox="0 0 46 19" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 61.1 (89650) - https://sketch.com -->
    <title>icons / sage</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <polygon id="path-1" points="0.2068 0.4892 11.989 0.4892 11.989 13.3682 0.2068 13.3682"></polygon>
        <polygon id="path-3" points="0 0.4892 11.4907 0.4892 11.4907 13.3682 0 13.3682"></polygon>
        <polygon id="path-5" points="0.6742 0.489 11.5242 0.489 11.5242 13.369 0.6742 13.369"></polygon>
        <polygon id="path-7" points="0.8068 0.489 11.9658 0.489 11.9658 18 0.8068 18"></polygon>
    </defs>
    <g id="icons-/-sage" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icons-/-flottants-/-sage">
            <g id="Group-13">
                <g id="Group-3" transform="translate(34.000000, 0.510800)">
                    <mask id="mask-2" fill="white">
                        <use xlink:href="#path-1"></use>
                    </mask>
                    <g id="Clip-2"></g>
                    <path d="M3.0338,5.9132 C3.0338,3.7792 4.6508,2.7482 6.3938,2.7482 C8.1368,2.7482 9.7088,3.7972 9.7088,5.9132 L3.0338,5.9132 Z M6.0858,0.4892 C2.5328,0.4892 0.2068,2.9262 0.2068,6.9472 C0.2068,11.9372 3.7638,13.3682 6.3048,13.3682 C9.5228,13.3682 11.3568,11.2052 11.3568,11.2052 L9.7198,9.5642 C8.8518,10.3442 7.8448,10.8112 6.3448,10.8112 C4.3648,10.8112 3.0298,9.6532 3.0298,8.0682 L11.8538,8.0682 C11.8538,8.0682 13.3948,0.4892 6.0858,0.4892 L6.0858,0.4892 Z" id="Fill-1" fill="#4D7CFE" mask="url(#mask-2)"></path>
                </g>
                <g id="Group-6" transform="translate(0.000000, 0.510800)">
                    <mask id="mask-4" fill="white">
                        <use xlink:href="#path-3"></use>
                    </mask>
                    <g id="Clip-5"></g>
                    <path d="M7.5867,5.6982 C6.4577,5.5572 3.1877,5.6842 3.1877,4.1942 C3.1877,3.3102 4.5577,2.8352 5.8217,2.8352 C7.8417,2.8352 9.1727,3.6422 9.5597,3.9112 L11.2567,2.2142 C10.7537,1.8092 8.8837,0.4892 5.8217,0.4892 C3.0327,0.4892 0.5227,1.9482 0.5227,4.2872 C0.5227,7.8762 4.8537,7.8752 6.6617,8.0522 C7.7577,8.1592 8.7577,8.2342 8.7577,9.3082 C8.7577,10.3062 7.2277,10.8622 6.0437,10.8622 C4.1137,10.8622 2.9577,10.2282 1.8257,9.2112 L-0.0003,11.0392 C1.6747,12.6562 3.7967,13.3682 5.8867,13.3682 C9.1237,13.3682 11.4907,11.9402 11.4907,9.0652 C11.4907,7.1602 10.2097,6.0262 7.5867,5.6982" id="Fill-4" fill="#4D7CFE" mask="url(#mask-4)"></path>
                </g>
                <g id="Group-9" transform="translate(11.000000, 0.510800)">
                    <mask id="mask-6" fill="white">
                        <use xlink:href="#path-5"></use>
                    </mask>
                    <g id="Clip-8"></g>
                    <path d="M8.7532,8.617 C8.7532,11.002 7.0532,10.874 5.5352,10.874 C3.9982,10.874 3.0172,10.464 3.0172,9.395 C3.0172,8.399 3.8162,8.068 5.4682,8.068 L8.7532,8.068 L8.7532,8.617 Z M5.9122,0.489 C3.0972,0.489 1.2722,1.922 0.9332,2.212 L2.6392,3.918 C2.9792,3.648 4.1372,2.85 5.9342,2.85 C8.0902,2.85 8.7342,3.709 8.7342,4.682 L8.7342,5.893 L4.8902,5.895 C3.4692,5.895 0.6742,6.253 0.6742,9.38 C0.6742,11.678 1.9912,13.369 5.1372,13.369 C6.7162,13.369 7.9102,12.857 8.7342,11.87 L8.7342,13.117 L11.5242,13.117 L11.5242,4.867 C11.5242,1.981 9.7552,0.489 5.9122,0.489 L5.9122,0.489 Z" id="Fill-7" fill="#4D7CFE" mask="url(#mask-6)"></path>
                </g>
                <g id="Group-12" transform="translate(22.000000, 0.510800)">
                    <mask id="mask-8" fill="white">
                        <use xlink:href="#path-7"></use>
                    </mask>
                    <g id="Clip-11"></g>
                    <path d="M9.1378,6.9218 C9.1378,7.8828 9.1218,8.5948 9.0448,8.9578 C8.8728,9.7588 7.9628,10.8118 6.3108,10.8118 C5.8968,10.8118 3.7768,10.5738 3.5398,8.7378 C3.4918,8.3698 3.4548,7.8828 3.4548,6.9218 C3.4548,5.4918 3.5138,5.0378 3.5878,4.6748 C3.7338,3.9538 4.6748,2.8948 6.3108,2.8948 C8.3128,2.8948 8.9558,4.2598 9.0498,4.7868 C9.1158,5.1518 9.1378,5.9608 9.1378,6.9218 L9.1378,6.9218 Z M9.1378,0.7538 L9.1378,2.1388 C8.1878,1.0348 7.0538,0.4888 5.5048,0.4888 C2.8598,0.4888 1.4978,1.9728 1.0368,3.5518 C0.8658,4.1388 0.8068,5.2598 0.8068,6.9158 C0.8068,8.4768 0.8208,9.8968 1.3638,10.9878 C2.1228,12.5158 3.9658,13.3688 5.5048,13.3688 C6.9958,13.3688 8.2618,12.7898 9.1378,11.7868 L9.1378,12.5618 C9.1378,13.4728 8.8258,14.0708 8.2428,14.5438 C7.6598,15.0178 6.9268,15.3078 5.9108,15.3078 C4.9888,15.3078 4.1048,14.8048 3.6018,14.4548 L1.7268,16.3298 C2.9418,17.3158 4.3958,17.9868 5.9638,17.9998 C7.6268,18.0138 8.9638,17.5098 10.1358,16.5888 C11.2578,15.7058 11.9658,14.0468 11.9658,12.2878 L11.9658,0.7538 L9.1378,0.7538 Z" id="Fill-10" fill="#4D7CFE" mask="url(#mask-8)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>