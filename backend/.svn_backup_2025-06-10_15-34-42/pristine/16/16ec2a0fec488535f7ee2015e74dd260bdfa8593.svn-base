<?php
require_once('db.inc.php');

/** \defgroup model_site_owner Pro<PERSON>ri<PERSON><PERSON> de l'instance
 * 	\ingroup model_tenants
 *	Ce module comprend les fonctions nécessaires à la gestion du propriétaire du site.
 *	@{
 */

/** Cet fonction retourne les informations légales sur le propriétaire du site, sous la forme d'un tableau associatif.
 *	@param int $wst_id Optionnel, identifiant d'un site web
 *	@param bool	$default Optionnel, Si wst_id = 0 doit être pris en compte
 *	@return array Les informations directement sur le site, sinon celles sur le tenant sous forme d'un tableau. Les clés de ce tableau sont les suivantes :
 *		- type : 0 pour société, 1 pour personne physique
 *		- name : nom de la société ou de la personne (prénom + nom dans les cas des personnes physiques)
 *		- firstname : prénom de la personne (personnes physiques uniquement)
 *		- lastname : nomn de la personne (personnes physiques uniquement)sss
 *		- address1 : première partie de l'adresse (No et Rue)
 *		- address2 : seconde partie de l'adresse
 *		- zipcode : code postal
 *		- city : Ville du siègle social ou ville de résidence pour les personnes physiques
 *		- inscription : Numéro d'inscription RCS ou numéro d'inscription au registre du commerce
 *		- capital : capital social (sociétés uniquement)
 *		- publication : nom du directeur de la publication
 *		- redaction : nom du responsable de la rédaction
 *		- phone : numéro de téléphone du propriétaire de la boutique
 *		- fax : numéro de fax du propriétaire de la boutique
 *		- email : adresse email du propriétaire de la boutique
 *		- naf : code naf (si possible révision 2) du propriétaire de la boutique
 *		- taxcode : numéro de tva intracommunautaire du propriétaire de la boutique
 *
 */
function site_owner_get( $wst_id=0, $default=false ){
	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	if( is_bool($default) && $default ){
		$wst_id = $wst_id>=0 ? $wst_id : $config['wst_id'];
	}else{
		$wst_id = $wst_id>0 ? $wst_id : $config['wst_id'];
	}

	$sql = '
		select owner_type as type, owner_name as name, owner_firstname as firstname, owner_lastname as lastname,
				owner_address1 as address1, owner_address2 as address2, owner_zipcode as zipcode, owner_city as city,
				owner_inscription as inscription, owner_capital as capital,
				owner_publication as publication, owner_redaction as redaction,
				owner_phone as phone, owner_fax as fax, owner_email as email,
				owner_naf as naf, owner_taxcode as taxcode
		from site_owner
		where owner_tnt_id='.$config['tnt_id'].'
		and (owner_wst_id=0 or owner_wst_id='.$wst_id.')
		order by owner_wst_id '.( $wst_id>0 ? 'desc' : 'asc' ).'
		limit 0, 1
	';

	$r = ria_mysql_query( $sql );
	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_fetch_assoc( $r );
}

// \cond onlyria
/**	Cette fonction permet la modification des informations légales sur le propriétaire du site.
 *	Au lieu d'utiliser cette fonction directement, il est conseillé d'utiliser les wrappers suivants :
 *	site_owner_set_society et site_owner_set_person.
 *
 *	@param int $type Type de propriétaire, 0 pour personne morale, 1 pour personne physique
 *	@param string $name Raison sociale de la société, ou contraction du nom et du prénom pour les personnes physiques
 *	@param string $firstname Prénom de la personne physique
 *	@param string $lastname Nom de la personne physique
 *	@param string $address1 Première partie de l'adresse (No et Rue)
 *	@param string $address2 Seconde partie de l'adresse
 *	@param string $zipcode Code postal
 *	@param string $city Ville
 *	@param string $inscription Numéro d'inscription RCS ou au registre du commerce
 *	@param string $capital Capital social (sociétés uniquement)
 *	@param string $publication Nom du responsable de la publication
 *	@param string $redaction Nom du responsable de la rédaction
 *	@param int $wst_id Optionnel, identifiant d'un site internet, par defaut les informations global sont mises à jour
 *
 */
function site_owner_set( $type, $name, $firstname, $lastname, $address1, $address2, $zipcode, $city, $inscription, $capital, $publication, $redaction, $wst_id=0 ){
	global $config;

	if( $type!=0 && $type!=1 ) return false;

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	$name = ucfirst(trim($name));
	$firstname = ucfirst(trim($firstname));
	$lastname = ucfirst(trim($lastname));
	$address1 = ucfirst(trim($address1));
	$address2 = ucfirst(trim($address2));
	$zipcode = ucfirst(trim($zipcode));
	$city = ucfirst(trim($city));

	if( $capital==0 ) $capital = 'null';

	$inscription = trim($inscription);
	$publication = ucfirst(trim($publication));
	$redaction = ucfirst(trim($redaction));

	return ria_mysql_query('
		replace into site_owner
			( owner_tnt_id, owner_wst_id, owner_type, owner_name, owner_firstname, owner_lastname, owner_address1, owner_address2, owner_zipcode, owner_city, owner_inscription, owner_capital, owner_publication, owner_redaction )
		values
			( '.$config['tnt_id'].', '.( $wst_id>0 ? $wst_id : 0 ).', '.$type.', "'.addslashes($name).'", "'.addslashes($firstname).'", "'.addslashes($lastname).'", "'.addslashes($address1).'", "'.addslashes($address2).'", "'.addslashes($zipcode).'", "'.addslashes($city).'", "'.addslashes($inscription).'", '.$capital.', "'.addslashes($publication).'", "'.addslashes($redaction).'" )
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification du numéro de téléphone du propriétaire de la boutique
 *	@param string $phone Numéro de téléphone
 *	@param int $wst_id Optionnel, identifiant d'un site internet, par defaut les informations global sont mises à jour
 *	@return bool true en cas de succès, false en cas d'échec
 */
function site_owner_set_phone( $phone, $wst_id=0 ){
	global $config;

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	return ria_mysql_query('update site_owner set owner_phone=\''.addslashes($phone).'\' where owner_tnt_id='.$config['tnt_id'].' and owner_wst_id='.( $wst_id>0 ? $wst_id : 0 ));
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification du numéro de fax du propriétaire de la boutique
 *	@param string $fax Numéro de fax
 *	@param int $wst_id Optionnel, identifiant d'un site internet, par defaut les informations global sont mises à jour
 *	@return bool true en cas de succès, false en cas d'échec
 */
function site_owner_set_fax( $fax, $wst_id=0 ){
	global $config;

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	return ria_mysql_query('update site_owner set owner_fax=\''.addslashes($fax).'\' where owner_tnt_id='.$config['tnt_id'].' and owner_wst_id='.( $wst_id>0 ? $wst_id : 0 ));
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de l'adresse email du propriétaire de la boutique
 *	@param string $email Adresse email
 *	@param int $wst_id Optionnel, identifiant d'un site internet, par defaut les informations global sont mises à jour
 *	@return bool true en cas de succès, false en cas d'échec
 */
function site_owner_set_email( $email, $wst_id=0 ){
	global $config;

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	return ria_mysql_query('update site_owner set owner_email=\''.addslashes($email).'\' where owner_tnt_id='.$config['tnt_id'].' and owner_wst_id='.( $wst_id>0 ? $wst_id : 0 ));
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification du code ape/naf du propriétaire de la boutique
 *	@param string $naf code APE/NAF
 *	@param int $wst_id Optionnel, identifiant d'un site internet, par defaut les informations global sont mises à jour
 *	@return bool true en cas de succès, false en cas d'échec
 */
function site_owner_set_naf( $naf, $wst_id=0 ){
	global $config;

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	return ria_mysql_query('update site_owner set owner_naf=\''.addslashes($naf).'\' where owner_tnt_id='.$config['tnt_id'].' and owner_wst_id='.( $wst_id>0 ? $wst_id : 0 ));
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification du numéro de tva intracommunautaire du propriétaire de la boutique
 *	@param string $taxcode Numéro de tva intracommunautaire
 *	@param int $wst_id Optionnel, identifiant d'un site internet, par defaut les informations global sont mises à jour
 *	@return bool true en cas de succès, false en cas d'échec
 */
function site_owner_set_taxcode( $taxcode, $wst_id=0 ){
	global $config;

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	return ria_mysql_query('update site_owner set owner_taxcode=\''.addslashes($taxcode).'\' where owner_tnt_id='.$config['tnt_id'].' and owner_wst_id='.( $wst_id>0 ? $wst_id : 0 ));
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification des informations légales lorsque le propriétaire du site est une société.
 *	Il est conseillé d'utiliser cette fonction plutôt que la fonction site_owner_set qui est générique.
 *
 *	@param string $name Raison sociale de la société, ou contraction du nom et du prénom pour les personnes physiques
 *	@param string $address1 Première partie de l'adresse (No et Rue)
 *	@param string $address2 Seconde partie de l'adresse
 *	@param string $zipcode Code postal
 *	@param string $city Ville
 *	@param string $inscription Numéro d'inscription RCS ou au registre du commerce
 *	@param string $capital Capital social (sociétés uniquement)
 *	@param string $publication Nom du responsable de la publication
 *	@param string $redaction Nom du responsable de la rédaction
 *	@param int $wst_id Optionnel, identifiant d'un site internet, par defaut les informations global sont mises à jour
 *	@return bool True si la modification des informations s'est correctement déroulée, False dans le cas contraire
 *
 */
function site_owner_set_society( $name, $address1, $address2, $zipcode, $city, $inscription, $capital, $publication, $redaction, $wst_id=0 ){
	return site_owner_set( 0, $name, '', '', $address1, $address2, $zipcode, $city, $inscription, $capital, $publication, $redaction, $wst_id );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification des informations légales lorsque le propriétaire du site est une personne physique.
 *	Il est conseillé d'utiliser cette fonction plutôt que la fonction site_owner_set qui est générique.
 *
 *	@param string $firstname Prénom de la personne physique
 *	@param string $lastname Nom de la personne physique
 *	@param string $address1 Première partie de l'adresse (No et Rue)
 *	@param string $address2 Seconde partie de l'adresse
 *	@param string $zipcode Code postal
 *	@param string $city Ville
 *	@param string $inscription Numéro d'inscription RCS ou au registre du commerce
 *	@param int $wst_id Optionnel, identifiant d'un site internet, par defaut les informations global sont mises à jour
 *	@return bool True si la modification des informations s'est correctement déroulée, False dans le cas contraire
 *
 */
function site_owner_set_person( $firstname, $lastname, $address1, $address2, $zipcode, $city, $inscription, $wst_id=0 ){
	return site_owner_set( 1, ucfirst(trim($firstname)).' '.ucfirst(trim($lastname)), $firstname, $lastname, $address1, $address2, $zipcode, $city, $inscription, 0, '', '', $wst_id );
}
// \endcond

/// @}

/// @}

/** \defgroup model_payments_virements Virements bancaires
 * 	\ingroup all_payments
 *	Ce module comprend les fonctions nécessaires à la gestion des informations bancaires (RIB, IBAN et BIC)
 *	@{
 */

// \cond onlyria
/** Cette fonction permet d'ajouter des informations bancaires.
 *	Ces informations peuvent êtres spécifiques ou non à un client donné (paramètre $usr_id).
 *
 *	@param string $name Obligatoire, nom donné aux informations bancaires.
 *	@param $cbank Obligatoire, code de la banque.
 *	@param $counter Obligatoire, code du guichet.
 *	@param $account Obligatoire, numéro du compte.
 *	@param $key Obligatoire, clé du RIB.
 *	@param $iban Obligatoire, International Bank Account Number.
 *	@param $bic Obligatoire, Bank Identifier Code.
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur. Si non spécifié, les informations bancaires sont celles du locataire.
 *	@param $structure_type Optionnel, type de structure bancaire (0 = locale, 1 = autre, 2 = BBAN, 3 = IBAN).
 *	@param $agency_name Optionnel, nom de l'agence.
 *	@param $address_data Optionnel, adresse de l'agence, fournie sous la forme d'un tableau associatif (clés : address1, address2, zipcode, city, country). Le pays doit être un code ISO 2 caractères.
 *	@param $comments Optionnel, commentaires divers.
 *
 *	@return int L'identifiant généré si l'ajout s'est correctement déroulé, False ou un code d'erreur spécifique (cf. ci-dessous) dans le cas contraire.
 *	@return -1 si le RIB fourni est faux.
 *	@return -2 si l'IBAN fourni est faux.
 *	@return -3 si le BIC fourni est faux.
 */
function site_bank_details_add( $name, $cbank, $counter, $account, $key, $iban, $bic, $usr_id=0, $structure_type=0, $agency_name=null, $address_data=null, $comments=null ){

	if( !trim($name) || !trim($cbank) || !trim($counter) || !trim($account) || !trim($key) || !trim($iban) || !trim($bic) ){
		return false;
	}
	if( !is_rib($cbank, $counter, $account, $key) ){
		return -1;
	}
	if( !is_iban($iban) ){
		return -2;
	}
	$len = strlen( $bic );
	if( $len<8 || $len>11 ){
		return -3;
	}
	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}elseif( $usr_id && !gu_users_exists( $usr_id ) ){
		return false;
	}
	if( !is_numeric($structure_type) || !in_array($structure_type, array(0, 1, 2, 3)) ){
		return false;
	}
	if( $address_data !== null ){
		if( !isset($address_data['address1'], $address_data['address2'], $address_data['zipcode'], $address_data['city'], $address_data['country']) ){
			return false;
		}
		if( !sys_countries_exists_code( $address_data['country'] ) ){
			return false;
		}
	}

	global $config;

	$fields = array();
	$values = array();

	$fields[] = 'bnk_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'bnk_name';
	$values[] = '"'.addslashes($name).'"';

	$fields[] = 'bnk_rib_code_bank';
	$values[] = '"'.addslashes($cbank).'"';

	$fields[] = 'bnk_rib_counter';
	$values[] = '"'.addslashes($counter).'"';

	$fields[] = 'bnk_rib_account';
	$values[] = '"'.addslashes($account).'"';

	$fields[] = 'bnk_rib_key';
	$values[] = '"'.addslashes($key).'"';

	$fields[] = 'bnk_iban';
	$values[] = '"'.addslashes($iban).'"';

	$fields[] = 'bnk_bic';
	$values[] = '"'.addslashes($bic).'"';

	$fields[] = 'bnk_date_created';
	$values[] = 'now()';

	if( $usr_id ){
		$fields[] = 'bnk_usr_id';
		$values[] = $usr_id;
	}

	if( $structure_type !== null ){
		$fields[] = 'bnk_structure_type';
		$values[] = $structure_type;
	}

	if( $agency_name !== null ){
		$fields[] = 'bnk_agency_name';
		$values[] = '"'.addslashes($agency_name).'"';
	}

	if( $address_data !== null ){
		$fields[] = 'bnk_address1';
		$values[] = '"'.addslashes($address_data['address1']).'"';
		$fields[] = 'bnk_address2';
		$values[] = '"'.addslashes($address_data['address2']).'"';
		$fields[] = 'bnk_zipcode';
		$values[] = '"'.addslashes($address_data['zipcode']).'"';
		$fields[] = 'bnk_city';
		$values[] = '"'.addslashes($address_data['city']).'"';
		$fields[] = 'bnk_country_code';
		$values[] = '"'.addslashes($address_data['country']).'"';
	}

	if( $comments !== null ){
		$fields[] = 'bnk_comments';
		$values[] = '"'.addslashes($comments).'"';
	}

	$sql = '
		insert into site_bank_details
			('.implode(', ', $fields).')
		values
			('.implode(', ', $values).')
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	return ria_mysql_insert_id();

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour des informations bancaires
 *
 *	@param int $id Obligatoire, identifiant des informations bancaires
 *	@param string $name Obligatoire, nom donné aux informations bancaires
 *	@param $cbank Obligatoire, code de la banque
 *	@param $counter Obligatoire, code du guichet
 *	@param $account Obligatoire, numéro du compte
 *	@param $key Obligatoire, clé du RIB
 *	@param $iban Obligatoire, International Bank Account Number
 *	@param $bic Obligatoire, Bank Identifier Code
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur. Si non spécifié, les informations bancaires sont celles du locataire.
 *	@param $structure_type Optionnel, type de structure bancaire (0 = locale, 1 = autre, 2 = BBAN, 3 = IBAN).
 *	@param $agency_name Optionnel, nom de l'agence.
 *	@param $address_data Optionnel, adresse de l'agence, fournie sous la forme d'un tableau associatif (clés : address1, address2, zipcode, city, country). Le pays doit être un code ISO 2 caractères.
 *	@param $comments Optionnel, commentaires divers.
 *
 *	@return bool True si la mise à jour s'est correctement déroulée, False ou un code d'erreur ci-dessous dans le cas contraire
 *	@return -1 si le RIB fourni est faux
 *	@return -2 si l'IBAN fourni est faux
 *	@return -3 si le BIC fourni est faux
 */
function site_bank_details_update( $id, $name, $cbank, $counter, $account, $key, $iban, $bic, $usr_id=0, $structure_type=0, $agency_name=null, $address_data=null, $comments=null ){

	if( !site_bank_details_exists( $id, -1 ) ){
		return false;
	}
	if( !trim($name) || !trim($cbank) || !trim($counter) || !trim($account) || !trim($key) || !trim($iban) || !trim($bic) ){
		return false;
	}
	if( !is_rib($cbank, $counter, $account, $key) ){
		return -1;
	}
	if( !is_iban($iban) ){
		return -2;
	}
	$len = strlen( $bic );
	if( $len<8 || $len>11 ){
		return -3;
	}
	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return false;
	}elseif( $usr_id && !gu_users_exists( $usr_id ) ){
		return false;
	}
	if( !is_numeric($structure_type) || !in_array($structure_type, array(0, 1, 2, 3)) ){
		return false;
	}
	if( $address_data !== null ){
		if( !isset($address_data['address1'], $address_data['address2'], $address_data['zipcode'], $address_data['city'], $address_data['country']) ){
			return false;
		}
		if( !sys_countries_exists_code( $address_data['country'] ) ){
			return false;
		}
	}

	global $config;

	$setters = array();
	$setters[] = 'bnk_name 				= "'.addslashes($name).'"';
	$setters[] = 'bnk_rib_code_bank 	= "'.addslashes($cbank).'"';
	$setters[] = 'bnk_rib_counter 		= "'.addslashes($counter).'"';
	$setters[] = 'bnk_rib_account 		= "'.addslashes($account).'"';
	$setters[] = 'bnk_rib_key 			= "'.addslashes($key).'"';
	$setters[] = 'bnk_iban 				= "'.addslashes($iban).'"';
	$setters[] = 'bnk_bic 				= "'.addslashes($bic).'"';
	$setters[] = 'bnk_usr_id 			= '.( $usr_id ? $usr_id : 'NULL' );
	$setters[] = 'bnk_structure_type 	= '.( $structure_type !== null ? '"'.addslashes($structure_type).'"' : 'NULL' );
	$setters[] = 'bnk_agency_name 		= '.( $agency_name !== null ? '"'.addslashes($agency_name).'"' : 'NULL' );
	$setters[] = 'bnk_comments 			= '.( $comments !== null ? '"'.addslashes($comments).'"' : 'NULL' );
	if( $address_data !== null ){
		$setters[] = 'bnk_address1		= "'.addslashes($address_data['address1']).'"';
		$setters[] = 'bnk_address2		= "'.addslashes($address_data['address2']).'"';
		$setters[] = 'bnk_zipcode		= "'.addslashes($address_data['zipcode']).'"';
		$setters[] = 'bnk_city			= "'.addslashes($address_data['city']).'"';
		$setters[] = 'bnk_country_code	= "'.addslashes($address_data['country']).'"';
	}else{
		$setters[] = 'bnk_address1		= NULL';
		$setters[] = 'bnk_address2		= NULL';
		$setters[] = 'bnk_zipcode		= NULL';
		$setters[] = 'bnk_city			= NULL';
		$setters[] = 'bnk_country_code	= NULL';
	}

	$sql = '
		update site_bank_details
		set '.implode(', ', $setters).'
		where bnk_tnt_id = '.$config['tnt_id'].'
			and bnk_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer des informations bancaires
 *	@param int $id Obligatoire, identifiant des informations bancaires
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function site_bank_details_del( $id ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	global $config;

	if( !site_bank_profils_del( $id ) ){
		return false;
	}

	// met à jour (Null) les infos bancaires des clients concernés
	ria_mysql_query('update gu_users set usr_bnk_id = NULL where usr_tnt_id = '.$config['tnt_id'].' and usr_bnk_id = '.$id);

	$sql = '
		update site_bank_details
		set bnk_is_deleted = 1, bnk_date_deleted = now()
		where bnk_tnt_id = '.$config['tnt_id'].'
			and bnk_id = '.$id.'
	';

	return ria_mysql_query($sql);

}
// \endcond

/** Cette fonction permet de récupérer des informations bancaires.
 *	@param int $id Obligatoire, identifiant des informations bancaires (ou tableau d'identifiants).
 *	@param int $prf Optionnel, identifiant d'un profil (dans ce cas, le résultat est limité à un résultat.
 *	@param int $usr_id Optionnel, identifiant d'un utilisateur. Par défaut, seules les informations relatives au locataires sont retournées. Pour retourner toutes les informations (client ou locataire), spécifiez -1.
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat MySQL comprenant les colonnes suivantes :
 *		- id : identifiant des informations bancaires
 *		- usr_id : identifiant de l'utilisateur
 *		- name : nom donné aux informations bancaires
 *		- cbank : code de la banque
 *		- counter : code du guichet
 *		- account : numéro de compte
 *		- key : clé RIB
 *		- iban : International Bank Account Number
 *		- bic : Bank Identifier Code
 *		- comments : Commentaires
 *		- structure_type : type de structure
 *		- agency_name : nom de l'agence
 *		- address1 : adresse de l'agence
 *		- address2 : complément d'adresse de l'agence
 *		- zipcode : code postal de l'agence
 *		- city : ville de l'agence
 *		- country_code : code ISO du pays de l'agence
 *		- date_created : date de création
 *		- date_created_en : date de création (format EN)
 *		- date_modified : date de dernière modification
 *		- date_modified_en : date de dernière modification (format EN)
 */
function site_bank_details_get( $id=0, $prf=0, $usr_id=0 ){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}
	if( !is_numeric($prf) || $prf < 0 ){
		return false;
	}
	if( !is_numeric($usr_id) || $usr_id < -1 ){
		return false;
	}

	global $config;

	$sql = '
		select
			bnk_id as id, bnk_usr_id as usr_id, bnk_name as name, bnk_rib_code_bank as cbank, bnk_rib_counter as counter,
			bnk_rib_account as account, bnk_rib_key as "key", bnk_iban as iban, bnk_bic as bic, bnk_comments as "comments",
			bnk_structure_type as structure_type, bnk_agency_name as agency_name, bnk_address1 as address1, bnk_address2 as address2,
			bnk_zipcode as zipcode, bnk_city as city, bnk_country_code as country_code, date_format(bnk_date_created, "%d/%m/%Y à %H:%i") as date_created,
			bnk_date_created as date_created_en, date_format(bnk_date_modified, "%d/%m/%Y à %H:%i") as date_modified, bnk_date_modified as date_modified_en
		from
			site_bank_details
		where bnk_tnt_id = '.$config['tnt_id'].'
			and bnk_is_deleted = 0
	';

	if( sizeof($id) ){
		$sql .= ' and bnk_id in ('.implode(', ', $id).')';
	}

	if( $usr_id > 0 ){
		$sql .= ' and bnk_usr_id = '.$usr_id;
	}elseif( $usr_id > -1 ){
		$sql .= ' and bnk_usr_id is null';
	}

	// condition en dernière car ajout de la clause LIMIT
	if( $prf>0 ){
		$sql .= '
			and exists (
				select 1 from site_bank_profils
				where sbp_prf_id = '.$prf.'
					and sbp_tnt_id = '.$config['tnt_id'].'
					and sbp_bnk_id = bnk_id
			)
		';
		$sql .= ' limit 1';
	}

	return ria_mysql_query($sql);

}

/** Cette fonction permet de décrire les informations bancaires.
 *	@param $details Obligatoire, résultat d'un ria_mysql_fetch_array(site_bank_details_get()).
 *	@param $show_link Facultatif, indique si le lien vers le back-office RiaShop doit être affiché (true, valeur par défaut) ou non (false)
 *	@return string Le code HTML de la description
 */
function site_bank_details_describe( $details, $show_link=true ){
	if( !isset($details['id'], $details['cbank'], $details['counter'], $details['account'], $details['key'], $details['iban'], $details['bic']) ) return '';

	$html = '';

	if( $show_link ){
		$html .= '	<a href="/admin/config/paiements/transfer.php?transfer='.$details['id'].'">'.htmlspecialchars( $details['name'] ).'</a><br />';
	}

	$html .= '		RIB : '.$details['cbank'].' '.$details['counter'].' '.$details['account'].' '.$details['key'].'';
	$html .= '		<br />IBAN : '.chunk_split( $details['iban'], 4, ' ' ).'';
	$html .= '		<br />BIC : '.$details['bic'].'';

	if( $show_link ){
		$html .= '		<br />Visible pour : ';

		$rprf = site_bank_profils_get( $details['id'] );
		if( $rprf && ria_mysql_num_rows($rprf) ){

			$first = true;
			while( $prf = ria_mysql_fetch_array($rprf) ){
				$html .= (!$first ? ', ' : '').$prf['pl_name'];
				$first = false;
			}
		} else {
			$html .= ' Aucun profil de compte client.';
		}
	}

	return $html;
}

// \cond onlyria
/** Cette fonction permet de vérifier si des informations bancaires existe.
 *	@param int $id Obligatoire, identifiant des informations bancaires
 *	@param int $usr_id Optionnel, identifiant de l'utilisateur auquel les informations appartiennent (0 : appartient au tenant, -1 : pas de contrôle sur le usr_id).
 *	@return bool True si elle existe, False dans le cas contraire
 */
function site_bank_details_exists( $id, $usr_id=0 ){

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}
	if( !is_numeric($usr_id) || $usr_id < -1 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from site_bank_details
		where bnk_tnt_id='.$config['tnt_id'].'
			and bnk_id='.$id.'
			and bnk_is_deleted = 0
	';

	if( $usr_id > 0 ){
		$sql .= ' and bnk_usr_id = '.$usr_id;
	}elseif( $usr_id > -1 ){
		$sql .= ' and bnk_usr_id is null';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de rattacher des informations bancaires à un profil
 *	@param int $id Obligatoire, identifiant des informations bancaires
 *	@param int $prf Obligatoire, identifiant d'un profil
 *	@return bool True si le rattachement s'est correctement déroulé, False dans le cas contraire
 */
function site_bank_profils_add( $id, $prf ){
	if( !site_bank_details_exists($id) ) return false;
	if( !gu_profiles_exists($prf) ) return false;
	global $config;

	$sql = '
		insert into site_bank_profils
			( sbp_tnt_id, sbp_bnk_id, sbp_prf_id )
		values
			( '.$config['tnt_id'].', '.$id.', '.$prf.' )
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer un ou plusieurs rattachement de profils à des informations bancaires
 *	@param int $id Obligatoire, identifiant des informations bancaires
 *	@param int $prf Optionnel, ientifiant d'un profil
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function site_bank_profils_del( $id, $prf=0 ){
	if( !is_numeric($id) || $id<=0 ) return false;
	if( !is_numeric($prf) || $prf<0 ) return false;
	global $config;

	$sql = '
		delete from site_bank_profils
		where sbp_tnt_id='.$config['tnt_id'].' and sbp_bnk_id='.$id.'
	';

	if( $prf>0 ){
		$sql .= ' and sbp_prf_id='.$id;
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les profils pour lesquels des informations bancaires sont accessibles.
 *	@param int $id Obligatoire, identifiant des informations bancaires
 *	@return array Un tableau contenant les identifiants des profiles
 */
function site_bank_profils_get( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$sql = '
		select sbp_prf_id as prf, prf_name as name, prf_pl_name as pl_name
		from site_bank_profils
			join gu_profiles on sbp_prf_id=prf_id and prf_tnt_id in (0, sbp_tnt_id) and prf_is_deleted=0
		where sbp_tnt_id='.$config['tnt_id'].'
			and sbp_bnk_id='.$id.'
	';


	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les profils pour lesquels des informations bancaires sont accessibles.
 *	@param int $id Obligatoire, identifiant des informations bancaires
 *	@return array Un tableau contenant les identifiants des profiles
 */
function site_bank_profils_get_array( $id ){
	if( !is_numeric($id) || $id<=0 ) return array();
	global $config;


	$res = site_bank_profils_get($id);
	if( !$res ){
		return array();
	}

	$ar_prfs = array();
	while( $r = ria_mysql_fetch_array($res) ){
		$ar_prfs[] = $r['prf'];
	}

	return $ar_prfs;
}
// \endcond

/// @}


