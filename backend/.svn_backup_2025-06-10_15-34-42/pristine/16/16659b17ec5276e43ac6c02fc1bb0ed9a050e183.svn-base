<?php

/** \file workqueue-rueducommerce.php
 *
 * 	Ce script est destiné à traiter les tâches contenues dans la file d'attente PriceMinister.
 *	Les tâches en question sont des demandes de création de produit, de mise à jour ou de suppression.
 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
 *
 */

set_include_path(dirname(__FILE__) . '/../include/');

require_once( 'define.inc.php' );
require_once( 'comparators.inc.php' );
require_once( 'comparators/ctr.rueducommerce.inc.php' );

// Active ou non le mode test
$mode_test = isset($_GET['test']) && $_GET['test'] || isset($argv) && array_search('test', $argv) !== false;

unset($config);

// Charge l'ensemble des configurations clients
$configs = cfg_variables_get_all_tenants();
if( !is_array($configs) || !sizeof($configs) ){
	return false;
}

// Traitement
foreach( $configs as $config ){

	// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, passe au client suivant.			
	if( !ctr_comparators_actived(CTR_RUEDUCOMMERCE) ){
		continue;
	}
	
	$rcatalog = ctr_catalogs_get( CTR_RUEDUCOMMERCE, 0, 0, true );
	
	$ar_prds = array();
	
	// création d'un tableau contenant les liens parents/enfants
	while( $catalog = ria_mysql_fetch_array($rcatalog) ){
		if( !isset($ar_prds[$catalog['prd_id']]) ){
			$ar_prds[$catalog['prd_id']] = array( 'id'=>$catalog['prd_id'], 'childs'=>'no-child' );
		}
	}
	
	// gestion des attributs communs
	foreach( $ar_prds as $id=>$prd ){
		if( !is_array($prd['childs']) || sizeof($prd['childs'])<=1 ){
			continue;
		}
		
		$first = true; $attr_com = array();
		foreach( $prd['childs'] as $c ){
			
			// famille de Rue du commerce où le produit est exporté
			$cat_ctr = ctr_catalogs_get_categorie( CTR_RUEDUCOMMERCE, $c, false );
			if( !$cat_ctr ){
				continue;
			}
			
			// récupère les attributs de la catégorie
			$attrs = ctr_cat_fields_get( CTR_RUEDUCOMMERCE, $cat_ctr, '', false );
			$params = ctr_catalogs_get_params( CTR_RUEDUCOMMERCE, $c );
			if( $attrs && ria_mysql_num_rows($attrs) ){
				while( $attr = ria_mysql_fetch_array($attrs) ){
					if( isset($params[ $attr['id'] ]) ){
						continue;
					}
					if( !in_array($attr['code'], array('brand', 'weight')) ){
						continue;
					}
					
					if( ($p = ria_mysql_fetch_array( prd_products_get_simple($c) )) ){
						if( $attr['code']=='weight' ){
							$params[ $attr['id'] ] = $p['weight_net']>0 ? $p['weight_net'] : ($p['weight']>0 ? $p['weight'] : 'NC');
						}elseif( $attr['code']=='brand' ){
							$params[ $attr['id'] ] = strtoupper( $p['brd_title'] );
						}
					}
				}
			}
			
			if( is_array($params) && sizeof($params) ){
				foreach( $params as $k=>$v ){
					$code = ctr_cat_fields_get_code( CTR_RUEDUCOMMERCE, $k );
					if( trim($code)=='' ){
						continue;
					}
					
					if( !isset($attr_com[$code]) ){
						if( $first ){
							$attr_com[$code] = $v;
						}
					} else {
						if( $attr_com[$code]!=$v ){
							unset( $attr_com[$code] );
						}
					}
				}
			}
			
			$first = false;
		}
		
		$ar_prds[ $id ]['attr_com'] = $attr_com;
	}
	
	// récupère le XML pour chaque produit
	$xml = '';
	foreach( $ar_prds as $prd ){
		$xml .= ctr_rueducommerce_products_get_xml( $prd );
	}

	if( !$mode_test ){
		$dirname = $config['ctr_dir'].'/'.md5( $config['tnt_id'].$config['date-created'] ).'/';
		$file = $dirname.'rueducommerce.xml';
		$url_file = $config['site_url'].'/shopbots/'.md5( $config['tnt_id'].$config['date-created'] ).'/rueducommerce.xml';
		$f = fopen( $file, 'w' );
		fwrite( $f, '<?xml version="1.0" encoding="UTF-8"?>'."\n" );
		fwrite( $f, '<catalog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" language="FR" country="FR" version="1.0.7" date="'.date('Y-m-d H:i:s').'" GMT="+1">'."\n" );
		fwrite( $f, ctr_rueducommerce_merchand_get_xml() );
		fwrite( $f, '<products>'."\n" );
		fwrite( $f, $xml );
		fwrite( $f, '</products>'."\n" );
		fwrite( $f, '</catalog>'."\n" );
		fclose( $f );
	} else {
		print $xml;
	}
}
