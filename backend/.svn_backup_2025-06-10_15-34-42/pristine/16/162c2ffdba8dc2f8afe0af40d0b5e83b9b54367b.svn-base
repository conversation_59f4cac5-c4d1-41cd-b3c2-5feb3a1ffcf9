<?php

use Riashop\PriceWatching\models\LinearRaised\prw_offers;

/**
 * @group offersTest
 * @backupGlobals disabled
 */
class OffersCRUDTest extends PHPUnit_Framework_TestCase {
	/**
	 * @dataProvider validOffersProvider
	 */
	public function testAddOffers($plr_id, $prd_id, $usr_id, $props) {
		$id = prw_offers::add($plr_id, $prd_id, $usr_id, $props);
		$this->assertTrue(is_numeric($id) && $id > 0, "Erreur insertion échoué");
	}
	/**
	 * @dataProvider invalidOffersProvider
	 */
	public function testFailAddOffers($plr_id, $prd_id, $usr_id, $props) {
		$this->setExpectedException('InvalidArgumentException');
		$id = prw_offers::add($plr_id, $prd_id, $usr_id, $props);
	}

	public function testGetOffers() {
		$result = prw_offers::get(5);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat ne devrait pas être vide");
		$this->assertEquals(3, ria_mysql_num_rows($result), "Erreur il devrait y avoir 2 lignes");
		$one = ria_mysql_fetch_assoc($result);
		$this->assertArrayHasKey('id', $one, "Erreur il manque la clé id");
		$this->assertArrayHasKey('cpt_id', $one, "Erreur il manque la clé cpt_id");
		$this->assertArrayHasKey('landedprice', $one, "Erreur il manque la clé landedprice");
		$this->assertArrayHasKey('shippingprice', $one, "Erreur il manque la clé shippingprice");
		$this->assertArrayHasKey('promo_price', $one, "Erreur il manque la clé promo_price");
		$this->assertArrayHasKey('url', $one, "Erreur il manque la clé url");
		$this->assertArrayHasKey('prd_id', $one, "Erreur il manque la clé prd_id");
		$this->assertArrayHasKey('plr_id', $one, "Erreur il manque la clé plr_id");
		$this->assertArrayHasKey('usr_id', $one, "Erreur il manque la clé usr_id");
		$this->assertArrayHasKey('count', $one, "Erreur il manque la clé count");
		$this->assertArrayHasKey('level', $one, "Erreur il manque la clé level");
		$this->assertArrayHasKey('facings', $one, "Erreur il manque la clé facings");
		$this->assertArrayHasKey('positions', $one, "Erreur il manque la clé positions");
		$this->assertArrayHasKey('pwf_rank', $one, "Erreur il manque la clé pwf_rank");
		$this->assertArrayHasKey('pwf_pmc', $one, "Erreur il manque la clé pwf_pmc");
		$this->assertArrayHasKey('date_created', $one, "Erreur il manque la clé date_created");
		$this->assertArrayHasKey('date_modified', $one, "Erreur il manque la clé date_modified");
	}

	public function testGetOfferById() {
		$id = prw_offers::add(1, 2, 3, array('pwf_pmc' => 8));
		$this->assertTrue(is_numeric($id), "Erreur lors de l'ajout");
		$result = prw_offers::get(1, $id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat ne devrait pas être vide");
		$this->assertEquals(1, ria_mysql_num_rows($result), "Erreur il devrait y avoir 2 lignes");
		$one = ria_mysql_fetch_assoc($result);
		$this->assertEquals(1, $one['plr_id']);
		$this->assertEquals(2, $one['prd_id']);
		$this->assertEquals($id, $one['id']);
		$this->assertEquals(3, $one['usr_id']);
		$this->assertEquals(8, $one['pwf_pmc']);
	}

	public function testGetOfferByPrd() {
		prw_offers::add(1, 80, 3, array('pwf_pmc' => 7, 'is_cpt' => true));
		prw_offers::add(1, 80, 99, array('pwf_pmc' => 7, 'is_cpt' => false));
		$result = prw_offers::get(1, null, 80);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat ne devrait pas être vide");
		$this->assertEquals(2, ria_mysql_num_rows($result), "Erreur il devrait y avoir 2 lignes");
		$usr_id=3;
		$is_cpt = '1';
		while ($one = ria_mysql_fetch_assoc($result)) {
			$this->assertEquals(1, $one['plr_id']);
			$this->assertEquals(80, $one['prd_id']);
			$this->assertEquals($usr_id, $one['usr_id']);
			$this->assertEquals(7, $one['pwf_pmc']);
			$this->assertEquals($is_cpt, $one['is_cpt']);
			$usr_id=99;
			$is_cpt = '0';
		}
	}

	public function testDeleteOffers() {
		$result = prw_offers::delete(5);
		$this->assertTrue($result, "Erreur lors de la suppression");
		$result = prw_offers::get(5);
		$this->assertNotTrue(ria_mysql_control_ressource($result), "Erreur le résultat devrait être vide");
	}

	public function testDeleteOffersById() {
		prw_offers::add(40, 80, 3, array('pwf_pmc' => 7));
		$id = prw_offers::add(40, 80, 99, array('pwf_pmc' => 7));
		$result = prw_offers::delete(40, $id);
		$this->assertTrue($result, "Erreur lors de la suppression");
		$result = prw_offers::get(40);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat devrait être vide");
		$this->assertEquals(1, ria_mysql_num_rows($result), "Erreur il devrait y avoir 2 lignes");
	}

	public function testDeleteOffersByPrdId() {
		prw_offers::add(50, 50, 3, array('pwf_pmc' => 7));
		prw_offers::add(50, 80, 99, array('pwf_pmc' => 7));
		prw_offers::add(50, 80, 99, array('pwf_pmc' => 7));
		prw_offers::add(50, 50, 99, array('pwf_pmc' => 7));
		prw_offers::add(50, 90, 99, array('pwf_pmc' => 7));
		$result = prw_offers::delete(50, null, 80);
		$this->assertTrue($result, "Erreur lors de la suppression");
		$result = prw_offers::get(50);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat devrait être vide");
		$this->assertEquals(3, ria_mysql_num_rows($result), "Erreur il devrait y avoir 3 lignes");
	}

	public function testUpdateOffers() {
		$ofr_id = prw_offers::add(50, 50, 3, array('pwf_pmc' => 7));

		$result = prw_offers::update($ofr_id, null, null, $props = array('pwf_pmc' => 10, 'pwf_rank' => 9, 'usr_id' => 888));
		$this->assertTrue($result);
		$result = prw_offers::get(50, $ofr_id);
		$this->assertTrue(ria_mysql_control_ressource($result));
		$one = ria_mysql_fetch_assoc($result);

		$this->assertEquals(10, $one['pwf_pmc']);
		$this->assertEquals(9, $one['pwf_rank']);
		$this->assertEquals(888, $one['usr_id']);
	}

	public function validOffersProvider() {
		return array(
			array('plr_id' => 1, 'prd_id' => 1, 'usr_id' => 1, 'props' => array(
				'count' => 1, 'level' => 1, 'facings' => 1, 'positions' => 1, 'pwf_rank' => 1, 'pwf_pmc' => 1,
			)),
			array('plr_id' => 5, 'prd_id' => 9, 'usr_id' => 5, 'props' => array(
				'count' => 5, 'pwf_rank' => 1, 'pwf_pmc' => 5,
			)),
			array('plr_id' => 1, 'prd_id' => 8, 'usr_id' => 5, 'props' => array(
				'count' => 8,
			)),
			array('plr_id' => 5, 'prd_id' => 5, 'usr_id' => 1, 'props' => array(
				'count' => 1, 'level' => 1, 'facings' => 1, 'positions' => 1, 'pwf_rank' => 1, 'pwf_pmc' => 1,
			)),
			array('plr_id' => 5, 'prd_id' => 8, 'usr_id' => 1, 'props' => array(
				'count' => 1, 'level' => 1, 'facings' => 5, 'positions' => 3, 'pwf_rank' => 1, 'pwf_pmc' => 1,
			)),
		);
	}

	public function invalidOffersProvider() {
		return array(
			array('plr_id' => null, 'prd_id' => 1, 'usr_id' => 1, 'props' => array(
				'count' => 1, 'level' => 1, 'facings' => 1,'is_cpt' => true, 'positions' => 1, 'pwf_rank' => 1, 'pwf_pmc' => 1, 'ost_id' => 1
			)),
			array('plr_id' => 5, 'prd_id' => 'tests', 'usr_id' => 5, 'props' => array(
				'count' => 5, 'pwf_rank' => 1, 'pwf_pmc' => 5, 'ost_id' => 1
			)),
			array('plr_id' => 1, 'prd_id' => 8, 'usr_id' => 5, 'props' => array(
				'count' => 8, 'ost_id' => 'estsets'
			)),
			array('plr_id' => 5, 'prd_id' => 5, 'usr_id' => 1, 'props' => array(
				'count' => 1, 'level' => 1, 'facings' => 'etstset', 'positions' => 1, 'pwf_rank' => 1, 'pwf_pmc' => 1, 'ost_id' => 1
			)),
			array('plr_id' => 5, 'prd_id' => null, 'usr_id' => 1, 'props' => array(
				'count' => 1, 'level' => 1, 'facings' => 'estests', 'positions' => 3, 'pwf_rank' => 1, 'pwf_pmc' => 1, 'ost_id' => 1
			)),
		);
	}
}