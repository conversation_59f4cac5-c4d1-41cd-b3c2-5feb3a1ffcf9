<?php
/**
 * \defgroup api-return-complete Complets
 * \ingroup api-return
 * @{
*/
require_once('ord.returns.inc.php');


// \cond onlyria
function update_return($raw_data){
	global $method, $config;
	$obj = json_decode($raw_data);
	$obj = json_decode(json_encode($obj), true);

	if( !isset($obj['head']) || !is_array($obj['head'])
		|| !isset($obj['lines']) || !is_array($obj['lines'])
		){
		throw new Exception("Paramètres invalide");
	}

	if( !isset($head['srv_id']) ){
		$head['srv_id'] = 0;
	}

	$head = $obj['head'];
	$lines = $obj['lines'];

	$signature = isset($obj['signature']) && is_array($obj['signature']) ? $obj['signature'] : false;
	$serials = isset($obj['serials']) && is_array($obj['serials']) ? $obj['serials'] : false;

	// controles des paramètres pour le retour
	if( !isset($head['usr'],$head['date'],$head['state'],$head['piece'],$head['ref']) ){
		throw new Exception("Paramètres bon de retour invalide");
	}

	// la synchro va parfois envoyer le real_usr en plus de l'autre, cas des écritures des commandes sous le meme code client
	if( isset($head['real_usr']) && $head['real_usr'] > 0 ){
		$head['usr'] = $head['real_usr'];
	}

	// controles des paramètres pour les lignes
	foreach( $lines as $line ){
		if( !isset($line['prd'],$line['line'],$line['ref'],$line['name'],$line['qte'],$line['price_ht'],$line['tva']) ){
			throw new Exception('Paramètres lignes invalide');
		}
	}
	// création / mise à jour de l'entete de retour
	if( $method == "add" ){
		$head_id = ord_returns_add_sage($head['usr'],$head['piece'],$head['ref'],$head['date'],$head['state']);

		if( !$head_id ){
			throw new Exception("Une erreur est survenue lors de la création de l'entete du retour piece : ".$head['piece']);
		}
	}else{
		$head_id = $head['id'];
	}

	// mise à jour de l'utilisateur
	if( isset($head['usr']) && $head['usr'] > 0 ){
		if( !ord_returns_update_user($head_id, $head['usr']) ){
			throw new Exception("Erreur dans la mise à jour de l'utilisateur du retour ".$head_id);
		}
	}


	// mise à jour de la référence du retour
	if( isset($head['ref']) ){
		if( !ord_returns_ref_update($head_id, $head['ref']) ){
			throw new Exception("Erreur dans la mise à jour de la référence ".$head['ref']." du retour ".$head_id);
		}
	}

	// mise à jour de la date du retour
	if( isset($head['date']) ){
		if( !ord_returns_update_date($head_id, $head['date']) ){
			throw new Exception("Erreur dans la mise à jour de la date du retour ".$head_id);
		}
	}

	// mise à jour du service de livraison
	/*
	if( $head['srv_id']!=0 ){
		if( !ord_returns_update_dlv_service($head_id, $head['srv_id']) ){
			throw new Exception("Erreur dans la mise à jour du service de livraison du bon de retour");
		}
	}
	 */

	// Mise a jour du nombre de produits
	if( isset($head['products']) ){
		if( !ord_returns_update_products($head_id, $head['products']) ){
			throw new Exception("Erreur dans la mise du nombre de produits dans le bon de retour");
		}
	}

	// mise à jours du contact
	if( isset($head['contact_id']) ){
		if( !ord_returns_set_contact_id( $head_id, $head['contact_id']) ){
			throw new Exception("Erreur dans la mise à jour du contact");
		}
	}

	// mise à jour de l'adresse de facturation
	if( isset($head['adr_invoices']) && $head['adr_invoices'] >0){
		if( !ord_returns_adr_invoice_set($head_id, $head['adr_invoices']) ){
			throw new Exception("Erreur dans la mise à jour de l'adresse de facturation");
		}
	}

	// mise à jour de l'adresse de retour
	if( isset($head['adr_delivery']) ){
		if( !ord_returns_adr_delivery_set($head_id, $head['adr_delivery']==0 ? false : $head['adr_delivery']) ){
			throw new Exception("Erreur dans la mise à jour de l'adresse de retour");
		}
	}

	// Mise à jour du need sync
	if( isset($head['need_sync']) && $head['need_sync']==1 ){
		ord_returns_set_need_sync($head_id);
	}

	// mise à jour de la date de retour
	/*
	if( isset($head['date-livr']) ){
		if( !ord_returns_set_date_livr( $head_id, $head['date-livr']) ){
			throw new Exception("Erreur dans la mise à jour de la date de retour");
		}
	} else if( isset($head['date_livr']) ){
		if( !ord_returns_set_date_livr( $head_id, $head['date_livr']) ){
			throw new Exception("Erreur dans la mise à jour de la date de retour");
		}
	}
	 */

	// mise à jour du magasin
	if( isset($head['str_id']) ){
		if( !ord_returns_set_dlv_store( $head_id, $head['str_id']) ){
			throw new Exception("Erreur dans la mise à jour du magasin");
		}
	}

	// mise à jour du type de package
	if( isset($head['pkg_id']) ){
		if( !ord_returns_set_package( $head_id, $head['pkg_id']) ){
			throw new Exception("Erreur dans la mise à jour du type de package");
		}
	}

	// mise à jour des notes
	if( isset($head['dlv_notes']) ){
		if( !ord_returns_dlv_notes_set( $head_id, $head['dlv_notes']) ){
			throw new Exception("Erreur dans la mise à jour d'une note sur le bon de retour");
		}
	}

	// mise à jour du seller id
	if( isset($head['seller_id']) ){
		$seller = $head['seller_id'] > 0 ? $head['seller_id'] : null;
		if( !ord_returns_set_seller_id( $head_id, $seller ) ){
			throw new Exception("Erreur dans la mise à jour du seller id");
		}
	}

	// mise à jour des commentaires
	if( isset($head['comments']) ){
		if( !ord_returns_comments_set( $head_id, $head['comments']) ){
			throw new Exception("Erreur dans la mise à jour du commentaire sur le bon de retour");
		}
	}

	// mise à jour du depot
	if( isset($head['dps_id']) ){
		if( !ord_returns_set_deposit( $head_id, $head['dps_id']) ){
			throw new Exception("Erreur dans la mise à jour du depot");
		}
	}


	// mise à jours du revendeur
	/*
	if( isset($head['reseller_id']) ){
		if( !ord_returns_set_reseller_id( $head_id, $head['reseller_id']) ){
			throw new Exception("Erreur dans la mise à jour du revendeur");
		}
	}
	 */

	// mise à jours du contact du revendeur
	/*
	if( isset($head['reseller_contact_id']) ){
		if( !ord_returns_set_reseller_contact_id( $head_id, $head['reseller_contact_id']) ){
			throw new Exception("Erreur dans la mise à jour du contact du revendeur");
		}
	}
	 */

	// controles de lignes
	$lines_in = array();
	foreach( $lines as $line ){
		$lines_in[] = $line['prd'].'-'.$line['line'];

		$group_id = false;
		if( isset($line['group_id']) && is_numeric($line['group_id']) ){
			$group_id = $line['group_id'];
		}

		$group_parent_id = false;
		if( isset($line['group_parent_id']) && is_numeric($line['group_parent_id']) ){
			$group_parent_id = $line['group_parent_id'];
		}

		// le produit est une interligne
		if( $line['prd'] == 0 ){
			$ordspace = ord_returns_products_get_spacing($head_id, $line['line']);
			if(!($ordspace && ria_mysql_num_rows($ordspace))){
				if( !ord_returns_products_add_spacing($head_id, false, $line['line']) ){
					throw new Exception("Erreur lors de la création de la ligne interligne : ".$line['line']." - ".$line['notes']);
				}
			}
			$position = false;
			if( isset($line['pos']) && is_numeric($line['pos']) && $line['pos'] > -1){
				$position = $line['pos'];
			}
			if( !ord_returns_products_update_spacing($head_id, $line['line'], $line['notes'], $position) ){
				throw new Exception("Erreur lors de l'enregistrement de la ligne interligne : ".$line['line']." - ".$line['notes']);
			}
			if($group_id !== false){
				if(!ord_returns_products_set_group_id($head_id, 0, $line['line'], $group_id, $group_parent_id)){
					throw new Exception("Erreur lors de l'enregistrement du group de la ligne interligne : ".$line['line']." - ".$line['notes']);
				}
			}
			continue;
		}


		$price_ttc = false;
		if( isset($line['price_ttc']) && is_numeric($line['price_ttc']) ){
			$price_ttc = $line['price_ttc'];
		}

		if( !isset($line['ord']) ){
			$line['ord'] = 0;
		}

		$ecotaxe = 0;
		if( isset($line['ecotaxe']) && is_numeric($line['ecotaxe']) ){
			$ecotaxe = $line['ecotaxe'];
		}

		$col_id = false;
		if( isset($line['col']) && is_numeric($line['col']) ){
			$col_id = $line['col'];
		}

		$rprd = ord_returns_products_get($line['prd'], $head_id, 0, $line['line']);

		$position = false;
		if( isset($line['pos']) && is_numeric($line['pos']) && $line['pos'] > -1){
			$position = $line['pos'];
		}

		if( $rprd && ria_mysql_num_rows($rprd) ){
			$res = ord_returns_products_update_sage( $head_id, $line['prd'], $line['line'], $line['ref'], $line['name'], $line['qte'], $line['price_ht'], $line['tva'], $line['ord'], $price_ttc, $ecotaxe, false, $line['notes'], $col_id, $position);
		}else{
			$res = ord_returns_products_add_sage( $head_id, $line['prd'], $line['line'], $line['ref'], $line['name'], $line['qte'], $line['price_ht'], $line['tva'], $line['ord'], $price_ttc, $ecotaxe, false, $line['notes'], $col_id, $position);
		}
		if( !$res ){
			throw new Exception("Erreur lors de l'enregistrement de la ligne produit : ".$line['prd']);
		}

		// insertion du group_id
		if($group_id !== false){
			if(!ord_returns_products_set_group_id($head_id,  $line['prd'], $line['line'], $group_id, $group_parent_id)){
				throw new Exception("Erreur lors de l'enregistrement de la ligne produit : ".$line['prd']);
			}
		}

		// ajout des champs avancés
		if(isset($line['fields'])) {
			$fields_delete_missing = (isset($line['fields_delete_missing']) && !$line['fields_delete_missing']) ? false : true;
			fields_sync(CLS_RETURN_PRODUCTS, $head_id, $line['prd'], $line['line'], $line['fields'], $fields_delete_missing);
		}
	}

	// retire les lignes produits absente du BL
	$rprd = ord_returns_products_get(0, $head_id);
	while( $prd = ria_mysql_fetch_assoc($rprd) ){
		$key = $prd['id'].'-'.$prd['line_id'];
		if( !in_array($key, $lines_in) ){
			if( !ord_returns_products_del($head_id, $prd['line_id'], $prd['id']) ){
				throw new Exception("Erreur lors de la suppression du produit : ".$prd['id']);
			}
		}
	}

	if(isset($head['fields'])) {
		$fields_delete_missing = (isset($head['fields_delete_missing']) && !$head['fields_delete_missing']) ? false : true;
		fields_sync(CLS_RETURN, $head_id, 0, 0, $head['fields'], $fields_delete_missing);
	}

	// ajout des modeles de saisie
	if(isset($head['models'])) {
		$models_delete_missing = (isset($head['models_delete_missing']) && !$head['models_delete_missing']) ? false : true;
		models_sync(CLS_RETURN, $head_id, 0, 0, $head['models'], $models_delete_missing);
	}

	// Mise a jour de totaux
	ord_returns_update_totals($head_id);

	// Ajout de la signature
	if( $signature && isset($signature['signature']) ){

		$usr_id = null;
		if( isset($signature['usr_id']) && is_numeric($signature['usr_id']) ){
			$usr_id = $signature['usr_id'];
		}

		$firstname = null;
		if( isset($signature['firstname']) && $signature['firstname'] != "" ){
			$firstname = $signature['firstname'];
		}

		$lastname = null;
		if( isset($signature['lastname']) && $signature['lastname'] != "" ){
			$lastname = $signature['lastname'];
		}

		$function = null;
		if( isset($signature['function']) && $signature['function'] != "" ){
			$function = $signature['function'];
		}

		obj_signature_add( CLS_RETURN, $head_id, 0, 0, $signature['signature'], $usr_id, $firstname, $lastname, $function );

	}

	// ajout de les serials
	if ($serials) {
		serials_sync(CLS_RETURN_PRODUCTS, $head_id, false, false, $serials);
	}


	return $head_id;
}
// \endcond

switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-return-complete-get Chargement
	 *
	 * Cette fonction permet de récupérer un bon de retour complet
	 *
	 *		\code
	 *			GET /return/complete/
	 *		\endcode
	 *
	 * @param int id obligatoire : identifiant du bon de retour
	 *
	 * @return json sous la forme suivante ( products + signature ):
 	 *		\code{.json}
 	 *			{
     *           "products": [
     *               {
     *                   "data": {
     *                       "ret":
     *                       "line_id": Identifiant de la ligne,
     *                       "id": Identifiant du produit,
     *                       "ref": Référence du produit,
     *                       "name": Nom du produit,
     *                       "price_ht": prix hors taxes du produit,
     *                       "price_ttc": prix tout taxes comprises du produit,
     *                       "qte": quantité,
     *                       "mode": ,
     *                       "details": Détails du produit,
     *                       "state": Etat du produit,
     *                       "lot": ,
     *                       "dlc": ,
     *                       "reason": ,
     *                       "dlc_fr": ,
     *                       "tva_rate": Taux de TVA ,
     *                       "motif": ,
     *                       "garanty": garantie sur le produit,
     *                       "ord_id": ,
     *                       "cod_id": ,
     *                       "ecotaxe": Participation à l'ecotaxe,
     *                       "date_livr": Date de livraison,
     *                       "notes": Notes complémentaires,
     *                       "date_created": Date de création,
     *                       "childs_line_id": Identifiant des lignes "enfants",
     *                      "parent_id": Identifiant de la ligne "parent",
     *                      "purchase_avg": ,
     *                       "pos": ,
     *                      "col_id":
     *                   }
     *               },
     *           ],
     *           "signature": []
     *       },
     *       		"id": Identifiant,
     *       		"date": Date de création,
     *       		"products": Nombre de produits dans le retour,
     *       		"total_ht": Montant total hors taxes,
     *       		"total_ttc": Montant total toutes taxes comprises,
     *       		"user_id": Identifiant de l'utilisateur,
     *       		"ord_id": Identifiant de la commande,
     *       		"states_id": Identifiant de l'état,
     *       		"prepaid_label": ,
     *       		"wst_id_ord": ,
     *       		"need_sync": Besoin d'une synchronisation,
     *       		"adr_invoices": ,
     *       		"piece": ,
     *       		"ref": Référence ,
     *       		"adr_delivery": Identifiant de l'adresse de livraison,
     *       		"dlv_notes": Notes complémentaires sur la livraison,
     *       		"pkg_id": Identifiant du colis,
     *       		"srv_id": ,
     *       		"str_id": ,
     *       		"pay_id": Identifiant du moyen de payement,
     *       		"pmt_id": ,
     *       		"seller_id": Identifiant de,
     *       		"dps_id": Identifiant de ,
     *       		"wst_id": Identifiant de ,
     *       		"contact_id": Identifiant de ,
     *       		"reseller_id": Identifiant de,
     *       		"reseller_contact_id": Identifiant de
     *   }
 	 *			}
 	 *		\endcode
	 *
	 * @}
	*/
	case 'get' :
		$rreturn = ord_returns_get($_REQUEST['id']);

		if( $rreturn && ria_mysql_num_rows($rreturn) ) {
			$content = array();
			$cpt = 0;
			while ($return = ria_mysql_fetch_assoc($rreturn)) {
				if( ++$cpt > 500 ) break; // limite la récupération des données

				$content[] = dev_devices_get_object_simplified(CLS_RETURN, array($return['id']));
			}
		}
		$result = true;
		break;
	/** @{@}
 	 * @{
	 * \page api-return-complete-add Ajout
     *
     * cette fonction permet l'ajout d'un nouveau bon de retour
     *
     * @param raw_data Obligatoire, Donnée en json_decode
     *               - head Obligatoire : Entête du bon de retour
     *                   - usr               Obligatoire : Identifiant de l'utilisateur
     *                   - date              Obligatoire : Date
     *                   - piece             Obligatoire : N° de pièce
     *                   - ref               Obligatoire : Référence
     *                   - state             Obligatoire : Statut du bl
     *                   - srv_id            Obligatoire : Service de livraison
     *               - lines Obligatoire : Tableau de lignes
     *                   - prd               Obligatoire : Identifiant du produit
     *                   - line              Obligatoire : N° de line
     *                   - qte               Obligatoire : Quantité
     *                   - ref               Obligatoire : Référence du produit
     *                   - name              Obligatoire : Nom du produit
     *                   - price_ht          Obligatoire : Prix HT unitaire
     *                   - tva               Obligatoire : tva en coeficient
     *                   - price_ttc         Facultatif  : prix TTC unitaire
     *                   - ecotaxe           Facultatif  : Ecotaxe
     *               - signature Facultatif : Signature de la commande
     *                   - signature : Code de la signature sous une forme moveTo:X:Y..;
     *                   - usr_id    : Identifiant de l'utilisateur qui as signé
     *                   - firstname : Prénom de la personne qui a signé
     *                   - lastname  : Nom de la personne qui as signé
     *                   - function  : Fonction de la personne qui a signé
     *
     * @return Identifiant du bon de retour
	 * @}
	*/
	case 'add':
		$return_id = update_return($raw_data);
		$result = true;
		$content = array('return_id' => $return_id);
		break;

	/** @{@}
 	 * @{
	 * \page api-return-complete-upd Mise à jour
	 *
	 * Cette fonction permet de mettre à jour un bon de retour complet
	 *
	 *		\code
	 *			PUT /return/complete/
	 *		\endcode
	 *
	 * @see ci-dessus ( Ajout )
	 *
	 * @return true si le bon de retour est modifié sans erreur
	 *
	 * @}
	*/
	case 'upd':
		$return_id = update_return($raw_data);
		$result = true;
		break;
}
///@}