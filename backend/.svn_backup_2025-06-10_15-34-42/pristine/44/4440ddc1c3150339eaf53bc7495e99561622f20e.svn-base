<?php
	require_once('orders.inc.php');
	require_once('ord.returns.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_RETURN');

	if( !isset($_GET['state']) || !ord_returns_states_exists($_GET['state']) )
		$_GET['state'] = 0;

	// Variable pour la mise en place des périodes
	if( isset($_GET['date1'], $_GET['date2']) ){
		view_date_in_session($_GET['date1'], $_GET['date2']);
		header('Location: /admin/orders/returns/returns.php?state='.(isset($_GET['state']) ? $_GET['state'] : '0'));
		exit;
	}

	$ord_id = null;
	$date1 = false;
	$date2 = false;
	if( isset($_GET['ref']) && trim($_GET['ref']) != '' ){
		$ord_id = array();
		$r_ord = ord_orders_get_simple(array('piece' => $_GET['ref']));
		if( $r_ord && ria_mysql_num_rows($r_ord)){
			while( $ord = ria_mysql_fetch_assoc($r_ord)){
				$ord_id[] = $ord['id'];
			}
		}
	}else{
		$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
		$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
		$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
		$_SESSION['datepicker_period'] = isset($_SESSION['datepicker_period']) ? $_SESSION['datepicker_period'] : _('Aujourd\'hui');
	}

	// récupère les états
	$query = ord_returns_states_get();
	$states = array();
	while ($dat = ria_mysql_fetch_assoc($query)) $states[$dat['id']] = $dat;

	$rdate1 = $date1.' 00:00';
	$rdate2 = $date2.' 23:59';

	$returns = ord_returns_get(0, $_GET['state'], $date1 ? $rdate1 : null, $date2 ? $rdate2 : null, $ord_id);

	function view_ord_returns_get_uri($param) {
		$state = $_GET['state'];
		$page = (array_key_exists('page', $param)) ? $param['page'] : $_GET['page'];

		$arg = array(
			'state'		=>	$state,
			'page'		=>	$page
		);
		$p = array();
		foreach ($arg as $key => $value) $p[] = $key.'='.$value;
		return 'returns.php?'.implode('&', $p);
	}

	define('ADMIN_PAGE_TITLE', _('Gestion des retours') . ' - ' . _('Commandes'));
	require_once('admin/skin/header.inc.php');

	if( $_GET['state']!=0 ){
		$state = $states[$_GET['state']];
		print '<h2 id="title-state">'._('Retours').' - '.$state['name_plural'].'</h2>';
	}else{
		print '<h2 id="title-state">'._('Retours').'</h2>';
	}

	$returns_count = ria_mysql_num_rows($returns);

	$total_ht = 0;
	$total_ttc = 0;
	$rreturns = array();
	while ($dat = ria_mysql_fetch_assoc($returns)) {
		$rreturns[] = $dat;
		$total_ht += $dat['total_ht'];
		$total_ttc += $dat['total_ttc'];
	}

	// Calcul des paramètres de pagination
	$by_page = 25;
	if( !isset($_GET['page']) || !is_numeric($_GET['page']) )
		$_GET['page'] = 1;
	$pages = ceil( $returns_count / $by_page );
	if( $_GET['page']>$pages )
		$_GET['page'] = $pages;
?>
	<?php
		if(isset($_GET['date1'], $_GET['date2'], $_GET['period'])){
			print '<input type="hidden" name="date1" id="date1" value="'.$_GET['date1'].'"/>';
			print '<input type="hidden" name="date2" id="date2" value="'.$_GET['date2'].'"/>';
			print '<input type="hidden" name="period" id="last" value="'.$_GET['period'].'"/>';
		}
		if(isset($_GET['state'])){
			print '<input type="hidden" name="state" id="state-h" value="'.$_GET['state'].'"/>';
		}
	?>
<div class="stats-menu">
	<div id="riadatepicker"></div>
	<div id="tb-synthese-order">
		<table id="table-synthese-order">
			<caption></caption>
			<thead>
				<tr>
					<th id="hd-order-total"><?php print _('Retours')?></th>
					<th id="hd-order-ht"><?php print _('Total')?> <abbr title="<?php print _('Hors Taxes')?>"><?php print _('HT')?></abbr></th>
					<th id="hd-order-ttc"><?php print _('Total')?> <abbr title="<?php print _('Toutes Taxes Comprises')?>"><?php print _('TTC')?></abbr></th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td headers="hd-order-total">
						<strong><?php print ria_number_format($returns_count); ?></strong>
					</td>
					<td headers="hd-order-ht">
						<strong><?php print ria_number_format($total_ht, NumberFormatter::CURRENCY, 2); ?></strong>
					</td>
					<td headers="hd-order-ttc">
						<strong><?php print ria_number_format($total_ttc, NumberFormatter::CURRENCY, 2); ?></strong>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="clear"></div>
</div>

<?php
	$total_ht = 0;
	$total_ttc = 0;
	$limit = min(count($rreturns), $_GET['page']*$by_page);
	for ($i=max(0, ($_GET['page']-1)*$by_page); $i<$limit; $i++) {
		$r = $rreturns[$i];
		$total_ht += $r['total_ht'];
		$total_ttc += $r['total_ttc'];
	}
?>
<form action="orders.php" method="get">
	<table class="list" cellpadding="0" cellspacing="0">
		<caption><?php print _('Liste des retours')?></caption>
		<thead>
			<tr>
				<th colspan="8"class="align-right">
					<select name="state" id="state" onchange="update_url({state:this.value})">
						<option value=""><?php print _('Tous les retours')?></option>
						<?php
							foreach ($states as $stateID => $state) {
								$count = ord_returns_states_get_count($stateID, $rdate1, $rdate2);
								if( $stateID==$_GET['state'] )
									print '<option value="'.$stateID.'" selected="selected">'.$state['name_plural'].' ('.number_format($count,0,',',' ').')</option>';
								else
									print '<option value="'.$stateID.'">'.$state['name_plural'].' ('.number_format($count,0,',',' ').')</option>';
							}
						?>
					</select>
				</th>
			</tr>
			<tr>
				<th id="ord-id"><?php print _('Numéro')?></th>
				<th id="ord-order"><?php print _('Commande')?></th>
				<th id="ord-user"><?php print _('Client')?></th>
				<th id="ord-date"><?php print _('Date du retour')?></th>
				<th id="ord-state"><?php print _('Etat/Statut')?></th>
				<th id="ord-products"><?php print _('Nombre de retours')?></th>
				<th id="ord-ht"><?php print _('Total H.T.')?></th>
				<th id="ord-ttc"><?php print _('Total T.T.C.')?></th>
			</tr>
		</thead>
		<tfoot>
			<tr id="pagination">
				<td colspan="2" class="align-left">
				<?php
					print 'Page '.$_GET['page'].'/'.$pages;
				?>
				</td>
				<td colspan="6">
				<?php
					if( $pages>1 ){
						$page_start = $_GET['page']>5 ? $_GET['page']-5 : 1;
						$page_stop = $_GET['page']+5<$pages ? $_GET['page']+5 : $pages;

						$links = array();
						if( $_GET['page']>1 )
							$links[] = '<a href="'.view_ord_returns_get_uri(array('page' => $_GET['page']-1)).'">&laquo; '._('Page précédente').'</a>';

						for( $i=$page_start; $i<=$page_stop; $i++ )
							if( $i==$_GET['page'] )
								$links[] = '<b>'.$i.'</b>';
							else
								$links[] = '<a href="'.view_ord_returns_get_uri(array('page' => $i)).'">'.$i.'</a>';

						if( $_GET['page']<$pages )
							$links[] = '<a href="'.view_ord_returns_get_uri(array('page' => $_GET['page']+1)).'">'._('Page suivante').' &raquo;</a>';

						print implode(' | ',$links);
					}
				?>
				</td>
			</tr>
			<tr id="ord-totals">
				<th colspan="6" class="align-right"><?php print _('Total :'); ?></th>
				<td id="orders-total-ht"><?php print ria_number_format($total_ht, NumberFormatter::CURRENCY, 2); ?></td>
				<td id="orders-total-ttc"><?php print ria_number_format($total_ttc, NumberFormatter::CURRENCY, 2); ?></td>
			</tr>
		</tfoot>
		<tbody id="lst_orders">
		<?php
			// Affichage des commandes
			if( !ria_mysql_num_rows($returns) )
				print '<tr><td colspan="8">'._('Aucun retour').'</td></tr>';
			else{
				for ($i=($_GET['page']-1)*$by_page; $i<$limit; $i++) {
					$r = $rreturns[$i];
					$order = ria_mysql_fetch_array(ord_orders_get(0, $r['ord_id']));
					$user = ria_mysql_fetch_array(gu_users_get($r['user_id']));
					print '<tr>';
						if( gu_user_is_authorized('_RGH_ADMIN_ORDER_RETURN_VIEW') ){
							print '<td><a href="return.php?ret='.$r['id'].'">'.str_pad($r['id'], 8, 0, STR_PAD_LEFT).'</a></td>';
						}else{
							print '<td>'.str_pad($r['id'], 8, 0, STR_PAD_LEFT).'</td>';
						}
						if( gu_user_is_authorized('_RGH_ADMIN_ORDER_EDIT') ){
							print '<td>'.view_ord_is_sync($order).' <a href="/admin/orders/order.php?ord='.$r['ord_id'].'" title="'._('Afficher la fiche de cette commande').'" target="_blank">'.str_pad($r['ord_id'], 8, 0, STR_PAD_LEFT).'</a></td>';
						}else{
							print '<td>'.view_ord_is_sync($order).' '.str_pad($r['ord_id'], 8, 0, STR_PAD_LEFT).'</td>';
						}
						if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_VIEW') ){
							print '<td><a href="/admin/customers/edit.php?usr='.$r['user_id'].'" target="_blank">'.htmlspecialchars($user['title_name'].' '.$user['adr_firstname'].' '.$user['adr_lastname']).'</a></td>';
						}else{
							print '<td>'.htmlspecialchars($user['title_name'].' '.$user['adr_firstname'].' '.$user['adr_lastname']).'</td>';
						}
						print '<td>'.ria_date_format($r['date']).'</td>';
						print '<td>'.$states[$r['states_id']]['name'].'</td>';
						print '<td class="align-right">'.ria_number_format($r['products']).'</td>';
						print '<td class="align-right">'.ria_number_format($r['total_ht'], NumberFormatter::CURRENCY, 2).'</td>';
						print '<td class="align-right">'.ria_number_format($r['total_ttc'], NumberFormatter::CURRENCY, 2).'</td>';
					print '</tr>';
				}
			}
		?>
		</tbody>
	</table>
</form>

<input id="date-0" type="hidden" value="<?php print $date1; ?>" />
<input id="date-1" type="hidden" value="<?php print $date2; ?>" />
<input id="period" type="hidden" value="<?php print $_SESSION['datepicker_period']; ?>" />

<script><!--
	<?php view_date_initialized( 0, '' ); ?>
--></script>

<?php
	require_once('admin/skin/footer.inc.php');
?>