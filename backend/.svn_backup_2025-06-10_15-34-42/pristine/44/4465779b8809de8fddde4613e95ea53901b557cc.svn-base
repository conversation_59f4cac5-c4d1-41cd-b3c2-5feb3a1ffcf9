<?xml version="1.0" encoding="utf-8"?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults>
            <bind key="$quz">overridden</bind>
        </defaults>

        <service id="bar" class="Symfony\Component\DependencyInjection\Tests\Fixtures\Bar"/>
    </services>
</container>
