# MarketplaceChannelCatalog

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**api_settings_status** | [**\Swagger\Client\Model\BeezUPCommonApiSettingsStatus**](BeezUPCommonApiSettingsStatus.md) |  | 
**enabled** | **bool** | The enabled status of the Channel Catalog | 
**marketplace_technical_code** | [**\Swagger\Client\Model\BeezUPCommonMarketplaceTechnicalCode**](BeezUPCommonMarketplaceTechnicalCode.md) |  | 
**marketplace_business_code** | [**\Swagger\Client\Model\BeezUPCommonMarketplaceBusinessCode**](BeezUPCommonMarketplaceBusinessCode.md) |  | 
**marketplace_market_place_id** | **string** | The marketplace identifier in the marketplace | 
**marketplace_iso_country_code_alpha2** | **string** | The marketplace country iso code alpha 2 (see http://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Decoding_table for more details) | 
**beez_up_marketplace_name** | [****](.md) | The marketplace name | 
**beez_up_channel_id** | [**\Swagger\Client\Model\BeezUPCommonChannelId**](BeezUPCommonChannelId.md) |  | 
**beez_up_channel_catalog_id** | [**\Swagger\Client\Model\BeezUPCommonChannelCatalogId**](BeezUPCommonChannelCatalogId.md) |  | 
**beez_up_store_id** | [**\Swagger\Client\Model\BeezUPCommonStoreId**](BeezUPCommonStoreId.md) |  | 
**beez_up_store_name** | **string** | The store name | 
**marketplace_merchant_identifiers** | **map[string,string]** | The marketplace merchant identifier list | [optional] 
**marketplace_account_id** | [**\Swagger\Client\Model\BeezUPCommonMarketplaceAccountId**](BeezUPCommonMarketplaceAccountId.md) |  | [optional] 
**lov_links** | [**\Swagger\Client\Model\MarketplaceChannelCatalogLovLinks**](MarketplaceChannelCatalogLovLinks.md) |  | 
**links** | [**\Swagger\Client\Model\MarketplaceChannelCatalogLinks**](MarketplaceChannelCatalogLinks.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


