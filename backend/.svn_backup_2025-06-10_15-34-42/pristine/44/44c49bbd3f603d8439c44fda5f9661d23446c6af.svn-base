<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/control.proto

namespace GPBMetadata\Google\Api;

class Control
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0abe010a18676f6f676c652f6170692f636f6e74726f6c2e70726f746f12" .
            "0a676f6f676c652e617069221e0a07436f6e74726f6c12130a0b656e7669" .
            "726f6e6d656e74180120012809426e0a0e636f6d2e676f6f676c652e6170" .
            "69420c436f6e74726f6c50726f746f50015a45676f6f676c652e676f6c61" .
            "6e672e6f72672f67656e70726f746f2f676f6f676c65617069732f617069" .
            "2f73657276696365636f6e6669673b73657276696365636f6e666967a202" .
            "0447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

