<?php
namespace Stock;

/**
 * \defgroup StockLimited Module de gestion des stocks limités par période
 * \ingroup scm
 * \brief Ce module permet la gestion des stocks limités par période
 * Ce module contient l'api du module gestion des stocks limités par période, cela comporte le modèle et les actions de l'interfaces
 */

 /** \class StockLimited
 * \brief Cette class permet la gestion des opérations CRUD de la table prd_stock_limited
 *
 * Cette class gère les opérations CRUD pour la table prd_stock_limited en donnant plusieurs possiblités pour la suppression des lignes et la récupération.
 */
class StockLimited {
	const TYPE_PERSO = 'perso'; ///< Périod personnalisé (date_start et date_end saisie par le client)
	const TYPE_WEEK = 'week'; ///< Périod avec un numéro de semaine
	const TYPE_MONTH = 'month'; ///< Périod avec un numéro de mois
	const TYPE_TRIMESTER = 'trimester'; ///< Périod avec un numéro de trimestre
	const TYPE_YEAR = 'year'; ///< Périod pour une année

	private $date_types = array(
		self::TYPE_PERSO,
		self::TYPE_WEEK,
		self::TYPE_MONTH,
		self::TYPE_TRIMESTER,
		self::TYPE_YEAR,
	);

	private $select_list = array(
		'stl_tnt_id as tnt_id',
		'stl_id as id',
		'stl_prd_id as prd_id',
		'stl_date_type as date_type',
		'stl_date_start as date_start',
		'stl_date_end as date_end',
		'stl_qte as qte',
		'stl_res as res',
		'stl_date_created as date_created',
		'stl_date_modified as date_modified',
	);

	/**
	 * Cette fonction permet d'insérer un nouveaux stock pour une périod
	 *
	 * @param int $prd_id Identifiant du produit
	 * @param string $date_type Type de période
	 * @param DateTime $date_start Date de début de la période
	 * @param DateTime $date_end Date de fin de la période
	 * @param float $qte La quantité initial
	 * @param float $res La quantité en commandé
	 * @return int|boolean Retourne l'identifiant du stock limité inséré, sinon false
	 *
	 * @throws InvalidArgumentException La fonction lance une excpetion si les arguments sont incorectent
	 */
	public function add($prd_id, $date_type, \DateTime $date_start, \DateTime $date_end, $qte, $res){
		if (!$this->validInteger($prd_id)){
			throw new \InvalidArgumentException("prd_id doit être un entier");
		}

		if (!$this->validDateType($date_type)){
			throw new \InvalidArgumentException("date_type doit être un type valide (".implode($this->date_types).")");
		}

		if (!$this->validFloat($qte)){
			throw new \InvalidArgumentException("qte doit être un décimal valide");
		}

		if (!$this->validFloat($res)){
			throw new \InvalidArgumentException("res doit être un décimal valide");
		}

		if( $date_start > $date_end ){
			throw new \InvalidArgumentException("La date de date_start ne peux pas être suppérieur à la date de fin");
		}

		global $config;

		$fields = array(
			'stl_tnt_id',
			'stl_prd_id',
			'stl_date_type',
			'stl_date_start',
			'stl_date_end',
			'stl_qte',
			'stl_res',
			'stl_date_created',
		);

		$values = array(
			$config['tnt_id'],
			$prd_id,
			$date_type,
			$date_start->format('Y-m-d H:i:s'),
			$date_end->format('Y-m-d H:i:s'),
			$qte,
			$res,
			'now()',
		);

		$insert = '
			insert into prd_stock_limited
				('.implode(', ', $fields).')
			values
				('.implode(', ', $values).')
		';

		$r = ria_mysql_query($insert);

		if (!$r) {
			return false;
		}

		return ria_mysql_insert_id();
	}
	/**
	 * Cette fonction permet de mettre à jour un stock limité
	 *
	 * @param int $id Identifiant du stock limité
	 * @param int $prd_id Identifiant du produit
	 * @param string $date_type Type valide de période
	 * @param DateTime $date_start Nouvelle date de début de la période
	 * @param DateTime $date_end Nouvelle date de fin de la période
	 * @param float $qte Nouveau stock
	 * @param float $res Nouveau stock réservé
	 * @return boolean Retourne true si l'update a bien fonctionné, sinon false
	 */
	public function update($id, $prd_id, $date_type=null, \DateTime $date_start=null, \DateTime $date_end=null, $qte=null, $res=null){

		if (!$this->validInteger($id)){
			throw new \InvalidArgumentException("id doit être un entier");
		}

		if (!$this->validInteger($prd_id)){
			throw new \InvalidArgumentException("prd_id doit être un entier");
		}

		if ($date_type !== null && !$this->validDateType($date_type)){
			throw new \InvalidArgumentException("date_type doit être un type valide (".implode($this->date_types).")");
		}

		if ($qte !== null && !$this->validFloat($qte)){
			throw new \InvalidArgumentException("qte doit être un décimal valide");
		}

		if ($res !== null && !$this->validFloat($res)){
			throw new \InvalidArgumentException("res doit être un décimal valide");
		}

		global $config;

		$to_set = array();

		if( $date_type !== null ){
			$to_set[] = 'stl_date_type='.addslashes($date_type);
		}

		if( $qte !== null ){
			$to_set[] = 'stl_qte='.$qte;
		}

		if( $res !== null ){
			$to_set[] = 'stl_res='.$res;
		}

		if( $date_start !== null ){
			$to_set[] = 'stl_date_start='.$date_start->format('Y-m-d H:i:s');
		}

		if( $date_end !== null ){
			$to_set[] = 'stl_date_end='.$date_end->format('Y-m-d H:i:s');
		}

		if( empty($to_set) ){
			throw new \InvalidArgumentException("Rien a mettre à jours");
		}

		$update = '
			update prd_stock_limited
				set '.implode(', ', $to_set).'
			where stl_tnt_id='.$config['tnt_id'].'
				and stl_id ='.$id.'
				and stl_prd_id ='.$prd_id.'
				and stl_date_deleted is null
		';

		return ria_mysql_query($update);
	}
	/**
	 * Cette fonction permet de récupérer les informations de stock limité pour les produtis
	 *
	 * @param int|array $prd_id Identifiant ou tableau d'identifiant produit
	 * @param string|array $date_type Facultatif, type ou tableau de type de période
	 * @param DateTime $date_start Facultatif, date de début de filtre
	 * @param DateTime $date_end Facultatif, date de fin de filtre
	 * @return resource Retourne une resource mysql avec les informations suivante :
	 * 					- tnt_id : Identifiant du tenant
	 * 					- id : Identifiant du stock limité
	 * 					- prd_id : Identifiant du produit
	 * 					- date_type : Type de période
	 * 					- date_start : Date de début de la périod
	 * 					- date_end : Date de fin de la période
	 * 					- qte : Stock de la période
	 * 					- res : Stock réservé jusqu'a aujourd'hui du stock limité
	 * 					- date_created : Date de création du stock limité
	 * 					- date_modified : Date de dernière modification du stock
	 *
	 * @throws InvalidArgumentException Lance une exeption si les arguements ne sont pas correcte
	 */
	public function get($prd_id, $date_type=null, \DateTime $date_start=null, \DateTime $date_end=null){
		$ids = control_array_integer($prd_id);

		if (!$ids) {
			throw new \InvalidArgumentException("prd_id doit être un entier ou un tableau d'entier");
		}

		if( $date_type !== null && !is_array($date_type) ){
			$date_type = array($date_type);
		}

		if( is_array($date_type) ){
			foreach ($date_type as $value) {
				if( !$this->validDateType($value) ){
					throw new \InvalidArgumentException("date_type doit être un type valide (".implode($this->date_types).")");
				}
			}
		}

		global $config;

		$select = '
			select
				'.implode(' ,', $this->select_list).'
			from prd_stock_limited
				join prd_products on prd_tnt_id=stl_tnt_id and prd_id=stl_prd_id and prd_date_deleted is null
			where stl_tnt_id='.$config['tnt_id'].'
				and stl_prd_id in ('.implode(', ', $ids).')
				and stl_date_deleted is null
		';

		if( !empty($date_type) ){
			$select .= '
				and stl_date_start in ('.implode(', ', $date_type).')';
		}

		if( $date_start !== null ){
			$select .= '
				and date(stl_date_start) >= "'.$date_start->format('Y-m-d H:i:s').'"';
		}

		if( $date_end !== null ){
			$select .= '
				and date(stl_date_end) <= "'.$date_end->format('Y-m-d H:i:s').'"';
		}

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}

	/**
	 * Cette fonction permet de récupérer les informations de stock limité pour les produtis
	 *
	 * @param int|array $id Identifiant ou tableau d'identifiant de tstock limité
	 * @return resource Retourne une resource mysql avec les informations suivante :
	 * 					- tnt_id : Identifiant du tenant
	 * 					- id : Identifiant du stock limité
	 * 					- prd_id : Identifiant du produit
	 * 					- date_type : Type de période
	 * 					- date_start : Date de début de la périod
	 * 					- date_end : Date de fin de la période
	 * 					- qte : Stock de la période
	 * 					- res : Stock réservé jusqu'a aujourd'hui du stock limité
	 * 					- date_created : Date de création du stock limité
	 * 					- date_modified : Date de dernière modification du stock
	 *
	 * @throws InvalidArgumentException Lance une exeption si les arguements ne sont pas correcte
	 */
	public function getById($id){
		$ids = control_array_integer($id);

		if (!$ids) {
			throw new \InvalidArgumentException("prd_id doit être un entier ou un tableau d'entier");
		}

		global $config;

		$select = '
			select
				'.implode(' ,', $this->select_list).'
			from prd_stock_limited
				join prd_products on prd_tnt_id=stl_tnt_id and prd_id=stl_prd_id and prd_date_deleted is null
			where stl_tnt_id='.$config['tnt_id'].'
				and stl_id in ('.implode(', ', $ids).')
				and stl_date_deleted is null
		';

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}
	/**
	 * Cette fonction gère la suppression des stocks limité en fonction de l'identifiant du stock limité.
	 *
	 * @param int|array $id
	 * @return boolean Retourne true si succès, sino false
	 *
	 * @throws InvalidArgumentException Lance une exception si les arguments ne sont pas bon
	 */
	public function deleteById($id){
		$ids = control_array_integer($id);

		if (!$ids) {
			throw new \InvalidArgumentException("id doit être un entier ou un tableau d'entier");
		}

		global $config;

		$delete = '
			update prd_stock_limited
				set stl_date_deleted=now()
			where stl_tnt_id='.$config['tnt_id'].'
				and stl_id	in ('.implode(', ', $ids).')
				and v is null
		';

		$r = ria_mysql_query($delete);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}
	/**
	 * Cette fonction permet la suppression en fonction des produits
	 *
	 * @param int $prd_id Identifiant ou tableau no
	 * @return boolean Retourne true si succès, sinon false
	 *
	 * @throws InvalidArgumentException Lance une exception si les arguments ne sont pas correcte
	 */
	public function deleteByProduct($prd_id){
		$ids = control_array_integer($prd_id);

		if (!$ids) {
			throw new \InvalidArgumentException("prd_id doit être un entier ou un tableau d'entier");
		}

		global $config;

		$delete = '
			update prd_stock_limited
				set stl_date_deleted=now()
			where stl_tnt_id='.$config['tnt_id'].'
				and stl_prd_id	in ('.implode(', ', $ids).')
				and stl_date_deleted is null
		';

		$r = ria_mysql_query($delete);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}

	/**
	 * Cette fonction permet de vérifier si le type de date est valide
	 *
	 * @param string $date_type Type de date
	 * @return boolean Retourne true si correcte, sinon false
	 */
	private function validDateType($date_type){
		return in_array($date_type, $this->date_types);
	}

	/**
	 * permet de validé un entier
	 *
	 * @param integer $int
	 * @return boolean true si valide false si non
	 */
	private function validInteger($int) {
		return is_numeric($int) && $int > 0;
	}
	/**
	 * permet de validé un décimal
	 *
	 * @param float|integer $float
	 * @return boolean true si valide false si non
	 */
	private function validFloat($float) {
		return (is_float($float) || is_numeric($float)) && $float >= 0;
	}
}
