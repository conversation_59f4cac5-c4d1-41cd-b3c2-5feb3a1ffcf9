<?php

	/**	\file ajax-reason-position-update.php
	 *	Ce fichier permet la mise à jour de la position d'un motif de retour produit.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source
	 *	- target : identifiant de l'objet cible
	 *	- action : soit "before" soit "after"
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_RETURN');

	if (! isset($_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception(_("Une erreur inattendue s'est produite lors de l'enregistrement.\nVeuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur."));
	
	$source = $_POST['source'];
	$target = $_POST['target'];
	$action = $_POST['action'];
	
	require_once('ord.returns.inc.php');
	
	$response = array('success' => ord_returns_reason_position_update($source, $target, $action));
	
	print json_encode($response);
	exit;

