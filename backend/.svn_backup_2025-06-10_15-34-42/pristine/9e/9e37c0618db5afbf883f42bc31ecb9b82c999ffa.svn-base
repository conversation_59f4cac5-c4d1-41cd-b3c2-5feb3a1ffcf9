<?php
	require_once('cms.inc.php');

	if( !isset($_GET['cms']) || !cms_categories_exists($_GET['cms']) ){
		$g_error = _("La page de contenu donnée en paramètre n'existe pas.")." "._("Veuillez vérifier cette information.");
	}else{
		$cms = ria_mysql_fetch_array( cms_categories_get($_GET['cms']) );
	}

	define('ADMIN_PAGE_TITLE', _('Page de contenu') . ' - ' . _('Informations'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		print '
			<table class="cheklists">
				<caption>'._('Page de contenu').'</caption>
				<col width="175" /><width="400" />
				<thead>
					<tr>
						<th colspan="2">'._('Informations').'</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>'._('Désignation :').'</td>
						<td>'.htmlspecialchars( $cms['name'] ).'</td>
					</tr>
					<tr>
						<td>'._('Description :').'</td>
						<td>'.$cms['short_desc'].'</td>
					</tr>
					<tr>
						<td>'._('Contenu :').'</td>
						<td class="popup-info-desc">'.str_replace( '<p>&nbsp;</p>', '', $cms['desc'] ).'</td>
					</tr>
					<tr>
						<td>'._('Publiée ?').'</td>
						<td>'.( time()<=strtotime($cms['date']) ? _('Oui') : _('Non') ).'</td>
					</tr>
				</tbody>
			</table>
		';
	}

	require_once('admin/skin/footer.inc.php');
