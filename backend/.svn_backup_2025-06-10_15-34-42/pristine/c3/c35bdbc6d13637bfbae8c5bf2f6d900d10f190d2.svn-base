<?php
/**
 * Store
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * Store Class Doc Comment
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class Store implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'store';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'store_id' => '\Swagger\Client\Model\BeezUPCommonStoreId',
        'name' => '\Swagger\Client\Model\StoreName',
        'url' => '\Swagger\Client\Model\StoreUrl',
        'country_iso_code_alpha3' => '\Swagger\Client\Model\StoreCountryIsoCodeAlpha3',
        'currency_code' => '\Swagger\Client\Model\BeezUPCommonCurrencyCode',
        'sectors' => '\Swagger\Client\Model\StoreSectors',
        'user_role' => '\Swagger\Client\Model\StoreUserRole',
        'status' => '\Swagger\Client\Model\StoreStatus',
        'owner_user_id' => 'string',
        'share_count' => 'int',
        'creation_utc_date' => '\DateTime',
        'links' => '\Swagger\Client\Model\StoreLinks'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'store_id' => null,
        'name' => null,
        'url' => null,
        'country_iso_code_alpha3' => null,
        'currency_code' => null,
        'sectors' => null,
        'user_role' => null,
        'status' => null,
        'owner_user_id' => 'guid',
        'share_count' => null,
        'creation_utc_date' => 'date-time',
        'links' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'store_id' => 'storeId',
        'name' => 'name',
        'url' => 'url',
        'country_iso_code_alpha3' => 'countryIsoCodeAlpha3',
        'currency_code' => 'currencyCode',
        'sectors' => 'sectors',
        'user_role' => 'userRole',
        'status' => 'status',
        'owner_user_id' => 'ownerUserId',
        'share_count' => 'shareCount',
        'creation_utc_date' => 'creationUtcDate',
        'links' => 'links'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'store_id' => 'setStoreId',
        'name' => 'setName',
        'url' => 'setUrl',
        'country_iso_code_alpha3' => 'setCountryIsoCodeAlpha3',
        'currency_code' => 'setCurrencyCode',
        'sectors' => 'setSectors',
        'user_role' => 'setUserRole',
        'status' => 'setStatus',
        'owner_user_id' => 'setOwnerUserId',
        'share_count' => 'setShareCount',
        'creation_utc_date' => 'setCreationUtcDate',
        'links' => 'setLinks'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'store_id' => 'getStoreId',
        'name' => 'getName',
        'url' => 'getUrl',
        'country_iso_code_alpha3' => 'getCountryIsoCodeAlpha3',
        'currency_code' => 'getCurrencyCode',
        'sectors' => 'getSectors',
        'user_role' => 'getUserRole',
        'status' => 'getStatus',
        'owner_user_id' => 'getOwnerUserId',
        'share_count' => 'getShareCount',
        'creation_utc_date' => 'getCreationUtcDate',
        'links' => 'getLinks'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['store_id'] = isset($data['store_id']) ? $data['store_id'] : null;
        $this->container['name'] = isset($data['name']) ? $data['name'] : null;
        $this->container['url'] = isset($data['url']) ? $data['url'] : null;
        $this->container['country_iso_code_alpha3'] = isset($data['country_iso_code_alpha3']) ? $data['country_iso_code_alpha3'] : null;
        $this->container['currency_code'] = isset($data['currency_code']) ? $data['currency_code'] : null;
        $this->container['sectors'] = isset($data['sectors']) ? $data['sectors'] : null;
        $this->container['user_role'] = isset($data['user_role']) ? $data['user_role'] : null;
        $this->container['status'] = isset($data['status']) ? $data['status'] : null;
        $this->container['owner_user_id'] = isset($data['owner_user_id']) ? $data['owner_user_id'] : null;
        $this->container['share_count'] = isset($data['share_count']) ? $data['share_count'] : null;
        $this->container['creation_utc_date'] = isset($data['creation_utc_date']) ? $data['creation_utc_date'] : null;
        $this->container['links'] = isset($data['links']) ? $data['links'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['store_id'] === null) {
            $invalidProperties[] = "'store_id' can't be null";
        }
        if ($this->container['name'] === null) {
            $invalidProperties[] = "'name' can't be null";
        }
        if ($this->container['url'] === null) {
            $invalidProperties[] = "'url' can't be null";
        }
        if ($this->container['country_iso_code_alpha3'] === null) {
            $invalidProperties[] = "'country_iso_code_alpha3' can't be null";
        }
        if ($this->container['currency_code'] === null) {
            $invalidProperties[] = "'currency_code' can't be null";
        }
        if ($this->container['sectors'] === null) {
            $invalidProperties[] = "'sectors' can't be null";
        }
        if ($this->container['user_role'] === null) {
            $invalidProperties[] = "'user_role' can't be null";
        }
        if ($this->container['status'] === null) {
            $invalidProperties[] = "'status' can't be null";
        }
        if ($this->container['owner_user_id'] === null) {
            $invalidProperties[] = "'owner_user_id' can't be null";
        }
        if ($this->container['share_count'] === null) {
            $invalidProperties[] = "'share_count' can't be null";
        }
        if ($this->container['creation_utc_date'] === null) {
            $invalidProperties[] = "'creation_utc_date' can't be null";
        }
        if ($this->container['links'] === null) {
            $invalidProperties[] = "'links' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['store_id'] === null) {
            return false;
        }
        if ($this->container['name'] === null) {
            return false;
        }
        if ($this->container['url'] === null) {
            return false;
        }
        if ($this->container['country_iso_code_alpha3'] === null) {
            return false;
        }
        if ($this->container['currency_code'] === null) {
            return false;
        }
        if ($this->container['sectors'] === null) {
            return false;
        }
        if ($this->container['user_role'] === null) {
            return false;
        }
        if ($this->container['status'] === null) {
            return false;
        }
        if ($this->container['owner_user_id'] === null) {
            return false;
        }
        if ($this->container['share_count'] === null) {
            return false;
        }
        if ($this->container['creation_utc_date'] === null) {
            return false;
        }
        if ($this->container['links'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets store_id
     *
     * @return \Swagger\Client\Model\BeezUPCommonStoreId
     */
    public function getStoreId()
    {
        return $this->container['store_id'];
    }

    /**
     * Sets store_id
     *
     * @param \Swagger\Client\Model\BeezUPCommonStoreId $store_id store_id
     *
     * @return $this
     */
    public function setStoreId($store_id)
    {
        $this->container['store_id'] = $store_id;

        return $this;
    }

    /**
     * Gets name
     *
     * @return \Swagger\Client\Model\StoreName
     */
    public function getName()
    {
        return $this->container['name'];
    }

    /**
     * Sets name
     *
     * @param \Swagger\Client\Model\StoreName $name name
     *
     * @return $this
     */
    public function setName($name)
    {
        $this->container['name'] = $name;

        return $this;
    }

    /**
     * Gets url
     *
     * @return \Swagger\Client\Model\StoreUrl
     */
    public function getUrl()
    {
        return $this->container['url'];
    }

    /**
     * Sets url
     *
     * @param \Swagger\Client\Model\StoreUrl $url url
     *
     * @return $this
     */
    public function setUrl($url)
    {
        $this->container['url'] = $url;

        return $this;
    }

    /**
     * Gets country_iso_code_alpha3
     *
     * @return \Swagger\Client\Model\StoreCountryIsoCodeAlpha3
     */
    public function getCountryIsoCodeAlpha3()
    {
        return $this->container['country_iso_code_alpha3'];
    }

    /**
     * Sets country_iso_code_alpha3
     *
     * @param \Swagger\Client\Model\StoreCountryIsoCodeAlpha3 $country_iso_code_alpha3 country_iso_code_alpha3
     *
     * @return $this
     */
    public function setCountryIsoCodeAlpha3($country_iso_code_alpha3)
    {
        $this->container['country_iso_code_alpha3'] = $country_iso_code_alpha3;

        return $this;
    }

    /**
     * Gets currency_code
     *
     * @return \Swagger\Client\Model\BeezUPCommonCurrencyCode
     */
    public function getCurrencyCode()
    {
        return $this->container['currency_code'];
    }

    /**
     * Sets currency_code
     *
     * @param \Swagger\Client\Model\BeezUPCommonCurrencyCode $currency_code currency_code
     *
     * @return $this
     */
    public function setCurrencyCode($currency_code)
    {
        $this->container['currency_code'] = $currency_code;

        return $this;
    }

    /**
     * Gets sectors
     *
     * @return \Swagger\Client\Model\StoreSectors
     */
    public function getSectors()
    {
        return $this->container['sectors'];
    }

    /**
     * Sets sectors
     *
     * @param \Swagger\Client\Model\StoreSectors $sectors sectors
     *
     * @return $this
     */
    public function setSectors($sectors)
    {
        $this->container['sectors'] = $sectors;

        return $this;
    }

    /**
     * Gets user_role
     *
     * @return \Swagger\Client\Model\StoreUserRole
     */
    public function getUserRole()
    {
        return $this->container['user_role'];
    }

    /**
     * Sets user_role
     *
     * @param \Swagger\Client\Model\StoreUserRole $user_role user_role
     *
     * @return $this
     */
    public function setUserRole($user_role)
    {
        $this->container['user_role'] = $user_role;

        return $this;
    }

    /**
     * Gets status
     *
     * @return \Swagger\Client\Model\StoreStatus
     */
    public function getStatus()
    {
        return $this->container['status'];
    }

    /**
     * Sets status
     *
     * @param \Swagger\Client\Model\StoreStatus $status status
     *
     * @return $this
     */
    public function setStatus($status)
    {
        $this->container['status'] = $status;

        return $this;
    }

    /**
     * Gets owner_user_id
     *
     * @return string
     */
    public function getOwnerUserId()
    {
        return $this->container['owner_user_id'];
    }

    /**
     * Sets owner_user_id
     *
     * @param string $owner_user_id The user id of the owner of the store
     *
     * @return $this
     */
    public function setOwnerUserId($owner_user_id)
    {
        $this->container['owner_user_id'] = $owner_user_id;

        return $this;
    }

    /**
     * Gets share_count
     *
     * @return int
     */
    public function getShareCount()
    {
        return $this->container['share_count'];
    }

    /**
     * Sets share_count
     *
     * @param int $share_count The share count related to this store
     *
     * @return $this
     */
    public function setShareCount($share_count)
    {
        $this->container['share_count'] = $share_count;

        return $this;
    }

    /**
     * Gets creation_utc_date
     *
     * @return \DateTime
     */
    public function getCreationUtcDate()
    {
        return $this->container['creation_utc_date'];
    }

    /**
     * Sets creation_utc_date
     *
     * @param \DateTime $creation_utc_date The creation date of the store
     *
     * @return $this
     */
    public function setCreationUtcDate($creation_utc_date)
    {
        $this->container['creation_utc_date'] = $creation_utc_date;

        return $this;
    }

    /**
     * Gets links
     *
     * @return \Swagger\Client\Model\StoreLinks
     */
    public function getLinks()
    {
        return $this->container['links'];
    }

    /**
     * Sets links
     *
     * @param \Swagger\Client\Model\StoreLinks $links links
     *
     * @return $this
     */
    public function setLinks($links)
    {
        $this->container['links'] = $links;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


