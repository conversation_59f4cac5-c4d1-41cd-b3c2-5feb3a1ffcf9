<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'phpDocumentor\\Reflection\\' => array($vendorDir . '/phpdocumentor/reflection-common/src', $vendorDir . '/phpdocumentor/reflection-docblock/src', $vendorDir . '/phpdocumentor/type-resolver/src'),
    'WhiteHat101\\Crypt\\' => array($vendorDir . '/whitehat101/apr1-md5/src'),
    'Webmozart\\Assert\\' => array($vendorDir . '/webmozart/assert/src'),
    'Twig\\Extensions\\' => array($vendorDir . '/twig/extensions/src'),
    'Twig\\' => array($vendorDir . '/twig/twig/src'),
    'Symfony\\Polyfill\\Util\\' => array($vendorDir . '/symfony/polyfill-util'),
    'Symfony\\Polyfill\\Php70\\' => array($vendorDir . '/symfony/polyfill-php70'),
    'Symfony\\Polyfill\\Php56\\' => array($vendorDir . '/symfony/polyfill-php56'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Component\\Yaml\\' => array($vendorDir . '/symfony/yaml'),
    'Symfony\\Component\\Stopwatch\\' => array($vendorDir . '/symfony/stopwatch'),
    'Symfony\\Component\\Routing\\' => array($vendorDir . '/symfony/routing'),
    'Symfony\\Component\\HttpKernel\\' => array($vendorDir . '/symfony/http-kernel'),
    'Symfony\\Component\\HttpFoundation\\' => array($vendorDir . '/symfony/http-foundation'),
    'Symfony\\Component\\Filesystem\\' => array($vendorDir . '/symfony/filesystem'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\DependencyInjection\\' => array($vendorDir . '/symfony/dependency-injection'),
    'Symfony\\Component\\Debug\\' => array($vendorDir . '/symfony/debug'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Symfony\\Component\\Config\\' => array($vendorDir . '/symfony/config'),
    'SimpleSAML\\modules\\yubikey\\' => array($baseDir . '/modules/authYubikey/lib'),
    'SimpleSAML\\modules\\discopower\\' => array($baseDir . '/modules/discopower/lib'),
    'SimpleSAML\\TwigConfigurableI18n\\' => array($vendorDir . '/simplesamlphp/twig-configurable-i18n/src'),
    'SimpleSAML\\Test\\' => array($baseDir . '/tests', $baseDir . '/tests/lib/SimpleSAML'),
    'SimpleSAML\\TestUtils\\' => array($vendorDir . '/simplesamlphp/simplesamlphp-test-framework/lib'),
    'SimpleSAML\\Module\\statistics\\' => array($baseDir . '/modules/statistics/lib'),
    'SimpleSAML\\Module\\sqlauth\\' => array($baseDir . '/modules/sqlauth/lib'),
    'SimpleSAML\\Module\\smartattributes\\' => array($baseDir . '/modules/smartattributes/lib'),
    'SimpleSAML\\Module\\sanitycheck\\' => array($baseDir . '/modules/sanitycheck/lib'),
    'SimpleSAML\\Module\\riak\\' => array($baseDir . '/modules/riak/lib'),
    'SimpleSAML\\Module\\radius\\' => array($baseDir . '/modules/radius/lib'),
    'SimpleSAML\\Module\\preprodwarning\\' => array($baseDir . '/modules/preprodwarning/lib'),
    'SimpleSAML\\Module\\negotiate\\' => array($baseDir . '/modules/negotiate/lib'),
    'SimpleSAML\\Module\\metarefresh\\' => array($baseDir . '/modules/metarefresh/lib'),
    'SimpleSAML\\Module\\ldap\\' => array($baseDir . '/modules/ldap/lib'),
    'SimpleSAML\\Module\\expirycheck\\' => array($baseDir . '/modules/expirycheck/lib'),
    'SimpleSAML\\Module\\exampleattributeserver\\' => array($baseDir . '/modules/exampleattributeserver/lib'),
    'SimpleSAML\\Module\\consent\\' => array($baseDir . '/modules/consent/lib'),
    'SimpleSAML\\Module\\cdc\\' => array($baseDir . '/modules/cdc/lib'),
    'SimpleSAML\\Module\\cas\\' => array($baseDir . '/modules/cas/lib'),
    'SimpleSAML\\Module\\authwindowslive\\' => array($baseDir . '/modules/authwindowslive/lib'),
    'SimpleSAML\\Module\\authtwitter\\' => array($baseDir . '/modules/authtwitter/lib'),
    'SimpleSAML\\Module\\authorize\\' => array($baseDir . '/modules/authorize/lib'),
    'SimpleSAML\\Module\\authfacebook\\' => array($baseDir . '/modules/authfacebook/lib'),
    'SimpleSAML\\Module\\authcrypt\\' => array($baseDir . '/modules/authcrypt/lib'),
    'SimpleSAML\\Module\\authX509\\' => array($baseDir . '/modules/authX509/lib'),
    'SimpleSAML\\Module\\adfs\\' => array($baseDir . '/modules/adfs/lib'),
    'SimpleSAML\\' => array($baseDir . '/lib/SimpleSAML'),
    'SensioLabs\\Security\\' => array($vendorDir . '/sensiolabs/security-checker/SensioLabs/Security'),
    'RobRichards\\XMLSecLibs\\' => array($vendorDir . '/robrichards/xmlseclibs/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-message/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psalm\\' => array($vendorDir . '/vimeo/psalm/src/Psalm'),
    'Prophecy\\' => array($vendorDir . '/phpspec/prophecy/src/Prophecy'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'PhpCoveralls\\' => array($vendorDir . '/php-coveralls/php-coveralls/src'),
    'PHPMailer\\PHPMailer\\' => array($vendorDir . '/phpmailer/phpmailer/src'),
    'Muglug\\PackageVersions\\' => array($vendorDir . '/muglug/package-versions-56/src/PackageVersions'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'Gettext\\Languages\\' => array($vendorDir . '/gettext/languages/src'),
    'Gettext\\' => array($vendorDir . '/gettext/gettext/src'),
    'Doctrine\\Instantiator\\' => array($vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Composer\\XdebugHandler\\' => array($vendorDir . '/composer/xdebug-handler/src'),
    'Composer\\CaBundle\\' => array($vendorDir . '/composer/ca-bundle/src'),
    'Basho\\' => array($vendorDir . '/phpfastcache/riak-client/src'),
);
