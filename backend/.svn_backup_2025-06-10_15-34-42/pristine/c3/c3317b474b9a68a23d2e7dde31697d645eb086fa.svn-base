/**
 * CSS de TinyMCE
 */

.mceLayout {
	max-width: 960px;
	margin-top: 0 !important;
	width: 100% !important;
}

/* Surcharge le tinymce */
#site-content .defaultSkin .mceIframeContainer {
	padding: 0 !important;
	overflow: hidden;
}
.defaultSkin table.mceToolbar,
.defaultSkin table.mceToolbar *,
.mceSplitButtonMenu,
.mceSplitButtonMenu *,
.mceListBoxMenu ,
.mceListBoxMenu *,
.mceMenu,
.mceMenu *,
.mce_forecolor,
.mce_forecolor *,
[id^="mce"], [id^="mce"] *{
	box-sizing: content-box;
}
.mceToolbar tbody tr {
  @include media('<medium') {
    display: flex;
    flex-wrap: wrap;
  }
}