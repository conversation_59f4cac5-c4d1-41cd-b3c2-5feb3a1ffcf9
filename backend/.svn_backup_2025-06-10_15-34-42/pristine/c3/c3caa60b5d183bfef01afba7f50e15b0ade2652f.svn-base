<?php

/**	\file customers.php
 *	Cet écran est utilisé en popup pour sélectionner une promotion dont les règles "client" seront dupliquées sur une promotion en cours.
 *	Il est par exemple appelé par le fichier /admin/promotions/specials/edit.php pour dupliquer une promotion.
 *	Les paramètres suivants sont attendus en argument :
 *	- type : identifiant du type de promotion
 *	- id : identifiant du code promotion qui va recevoir les données
 */

require_once('promotions.inc.php');

// Vérifie que l'utilisateur à bien accès à la page et qu'il à le droit de modifier une promotion
if( $_GET['type'] == _PMT_TYPE_SOLDES ){
    if( $_GET['id'] == 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_PROMO_SOLDE_ADD');
    }elseif( $_GET['id'] != 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_PROMO_SOLDE_VIEW');
    }
}elseif( $_GET['type'] == _PMT_TYPE_REWARD ){
    if( $_GET['id'] == 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_PROMO_REWARD_ADD');
    }elseif( $_GET['id'] != 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_PROMO_REWARD_VIEW');
    }
}else{
    if( $_GET['id'] == 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD_ADD');
    }elseif( $_GET['id'] != 0 ){
		gu_if_authorized_else_403('_RGH_ADMIN_PROMO_PRD_VIEW');
    }
}

// Vérifie l'identifiant de type de code passé en paramètre
$rtype = null;
$type = null;
if (isset($_GET['type']) && is_numeric($_GET['type']) && $_GET['type'] > 0) {
    $rtype = pmt_types_get($_GET['type'], false);
    if (!$rtype || !ria_mysql_num_rows($rtype)) {
		print _('Identifiant de type de promotion inconnu');
        exit;
    }
    $type = ria_mysql_fetch_assoc($rtype);
    if (!$type) {
        exit;
    }
}

// Vérifie l'identifiant du code promotion, sauf pour les chéquiers cadeaux qui sont un cas à part
$pmt_id = isset($_GET['id']) ? $_GET['id'] : 0;
if( $type['id']!=7 && !pmt_codes_exists($pmt_id) ){
	print _('Identifiant de promotion inconnu');
    exit;
}

$r_promotions = pmt_codes_get(null, null, false, $type['id'], false, null);

if (isset($_POST['pmt']) && is_array($_POST['pmt'])) {
    foreach($_POST['pmt'] as $other_pmt_id) {
        if ($pmt_id == $other_pmt_id) {
            continue;
        }

        $r_usr = pmt_users_get($other_pmt_id);
        if ($r_usr && ria_mysql_num_rows($r_usr)) {
            while ($usr = ria_mysql_fetch_assoc($r_usr)) {
                if (!pmt_users_add($_GET['id'], $usr['id'], intval($usr['include']))) {
                    $usr_error = _('Erreur lors de l\'ajout d\'un client');
                }
            }    
        }

        $r_usr = pmt_segments_get($other_pmt_id);
        if ($r_usr && ria_mysql_num_rows($r_usr)) {
            while ($usr = ria_mysql_fetch_assoc($r_usr)) {
                if (!pmt_segments_add($_GET['id'], $usr['id'], intval($usr['include']))) {
                    $seg_error = _('Erreur lors de l\'ajout d\'un segment de clients');
                }
            }
        }

        $r_usr = pmt_profiles_get($other_pmt_id);
        if ($r_usr && ria_mysql_num_rows($r_usr)) {
            while ($usr = ria_mysql_fetch_assoc($r_usr)) {
                if (!pmt_profiles_add($_GET['id'], $usr['id'], intval($usr['include']))) {
                    $prf_error = _('Erreur lors de l\'ajout d\'un type de compte');
                }
            }
        }
    }

    if (isset($usr_error)) {
        $errors[] = $usr_error;
    }

    if (isset($seg_error)) {
        $errors[] = $seg_error;
    }

    if (isset($prf_error)) {
        $errors[] = $prf_error;
    }
}

// pmt_products_add( $cod, $rule['id'], $rule['include'] )

define('ADMIN_PAGE_TITLE', _('Import de clients') . ' - ' . _('Promotions'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');

require_once('admin/skin/header.inc.php');

if (isset($errors) && is_array($errors)) {
    foreach ($errors as $error) {
        print '<div class="error">' . htmlspecialchars($error) . '</div>';
    }
} elseif (!empty($_POST)) {
    print '<script>
        window.parent.loadRulesCustomers();
        window.parent.hidePopup();
        </script>';
    exit;
}
?>

<form method="post">
    <table id="pmt-specials-customer-import">
        <caption><?php print _('Promotions') ?></caption>
        <thead>
            <tr>
                <th id="pmt-check" class="col20px">
                    <input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
                </th>
                <th id="pmt-name"><?php print _('Désignation') ?></th>
                <th class="col80px"><?php print _('Déplacer') ?></th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <td colspan="3">
                    <input type="submit" name="save" value="<?php print _('Importer les clients') ?>" class="float-right" />
                </td>
            </tr>
        </tfoot>
        <tbody><?php
while ($pmt = ria_mysql_fetch_assoc($r_promotions)) {
	
	// On ne peut pas importer une promotion sur elle-même
    if( intval($pmt['id'])==intval($_GET['id']) ){
        continue;
    }

    ?>
    <tr class="ria-row-orderable" id="line-<?php print intval($pmt['id']) ?>">
        <td>
            <input type="checkbox" name="pmt[]" id="pmt-<?php print $pmt['id']; ?>" value="<?php print intval($pmt['id']) ?>" />
        </td>
        <td><label for="pmt-<?php print $pmt['id']; ?>"><?php print htmlspecialchars( $pmt['code'] ? $pmt['code'] : $pmt['name'] ); ?></label></td>
        <td class="ria-cell-move"></td>
    </tr>
    <?php
}
    ?>
        </tbody>
    </table>
    <br/>
    <div class="notice"><?php echo _("Vous pouvez importer les règles d'inclusions / exclusions depuis une ou plusieurs autres promotions. Si plusieurs promotions disposent de règles s'appliquant sur les mêmes contenus (clients, profils, segments...), vous pouvez, via le glisser / déposer, gérer l'ordre d'import des règles."); ?></div>
</form>
<?php
require_once('admin/skin/footer.inc.php');
?>