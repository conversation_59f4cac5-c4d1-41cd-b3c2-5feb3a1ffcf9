<?php 
// \cond onlyria

/** \defgroup model_origins Origines des commandes et autres actions
 *	Ce module comprend les fonctions nécessaires à la gestion suivi d'origine grâce au marqueur analytics.
 *	La classe GA_Parse est publiquement disponible ici : https://github.com/joaolcorreia/Google-Analytics-PHP-cookie-parser
 *	Elle est distribuée sous licence LGPL
 *	@{
 */

/**
 *	\brief Cette classe analyse le contenu des cookies Google Analytics pour obtenir des informations sur le visiteur et l'origine de sa visite
 */
class GA_Parse {

	/// \publicsection
	
	// informations sur la campagne
	
	/// Source de la visite (Accès direct, Site référent, Référencement naturel, Google Adwords, etc...)
	var $campaign_source = '';
	/// Nom de la campagne (si renseigné)
	var $campaign_name = '';
	/// Medium utilisé (si renseigné). 
	var $campaign_medium = '';
	/// Contenu de la campagne (l'url par exemple dans le cas d'un site référent)
	var $campaign_content = '';
	/// Terme de la campagne (mot clé acheté par exemple, ou référence du produit pour Google Shopping)
	var $campaign_term = '';

	// informations sur la visite
	
	/// Date de la première visite de cet internaute
	var $first_visit;
	/// Date de la précédente visite de cet internaute
	var $previous_visit;
	/// Date de début de la visite actuelle
	var $current_visit_started;
	/// Nombre de visites
	var $times_visited = 0;
	/// Nombre de pages vues depuis le début de la visite en cours
	var $pages_viewed = 0;

	/** Constructeur
	 *	Instance l'objet et l'initialise en lisant les valeurs disponibles dans les cookies Google Analytics suivants :
	 *	__utma, __utmb et __utmz
	 */
	function __construct(){
		$this->parseUtmA(); // Analyse les données disponibles sur le visiteur
		$this->parseUtmB(); // Analyse les données disponibles sur la visite
		$this->parseUtmZ(); // Analyse les données disponibles sur la campagne
	}

	/// \privatesection
	
	
	/**
	 *	Cette fonction permet de récupérer les informations disponibles sur le visiteur dans le cookie __utma de Google Analytics.
	 */
	private function parseUtmA(){

		if( !isset($_COOKIE['__utma']) ){
			return false;
		}
	
		// Récupère les informations contenues dans le cookie __utma
		@list($domain_hash,$random_id,$time_initial_visit,$time_beginning_previous_visit,$time_beginning_current_visit,$session_counter) = preg_split('[\.]', $_COOKIE["__utma"]);
		
		$time_initial_visit = is_numeric($time_initial_visit) && $time_initial_visit>0 ? $time_initial_visit : time();
		$time_beginning_previous_visit = is_numeric($time_beginning_previous_visit) && $time_beginning_previous_visit>0 ? $time_beginning_previous_visit : time();
		$time_beginning_current_visit = is_numeric($time_beginning_current_visit) && $time_beginning_current_visit>0 ? $time_beginning_current_visit : time();
		
		$this->first_visit = date( 'Y-m-d H:i:s', $time_initial_visit );
		$this->previous_visit = date( 'Y-m-d H:i:s', $time_beginning_previous_visit );
		$this->current_visit_started = date( 'Y-m-d H:i:s', $time_beginning_current_visit );
		$this->times_visited = $session_counter;

	}
	
	/**
	 *	Cette fonction permet de récupérer les informations disponibles sur la visite dans le cookie __utmb de Google Analytics.
	 */
	private function parseUtmB(){

		if( !isset($_COOKIE['__utmb']) ){
			return false;
		}

		// Nombre de pages vues depuis le début du cookie
		@list($domain_hash,$pages_viewed,$garbage,$time_beginning_current_session) = preg_split('[\.]', $_COOKIE["__utmb"]);
		$this->pages_viewed = $pages_viewed;

	}

	/**
	 *	Cette fonction permet de récupérer les informations disponibles sur la campagne dans le cookie __utmz de Google Analytics.
	 */
	private function parseUtmZ(){

		if( !isset($_COOKIE['__utmz']) ){
			return false;
		}

		// Recherche les différentes informations contenues dans le cookie __utmz
		@list($domain_hash,$timestamp, $session_number, $campaign_numer, $campaign_data) = preg_split('[\.]', $_COOKIE["__utmz"],5);

		// Traitement sur la chaine pour récupérer les informations de la campagne
		$campaign_data = parse_str(strtr($campaign_data, "|", "&"));

		$this->campaign_source = isset($utmcsr) ? $utmcsr : '';
		$this->campaign_name = isset($utmccn) ? $utmccn : '';
		$this->campaign_medium = isset($utmcmd) ? $utmcmd : '';
		if( isset($utmctr) ){
			$this->campaign_term = $utmctr;
		}
		if( isset($utmcct) ){
			$this->campaign_content = $utmcct;
		}
		
		if( trim($this->campaign_source)!='google_shopping' ){
			// Informations propres aux campagnes Google Adwords
			if( isset($utmgclid) ){
				$this->campaign_source = 'google';
				$this->campaign_name = '';
				$this->campaign_medium = 'cpc';
				$this->campaign_content = '';
				if( isset($utmctr) ){
					$this->campaign_term = $utmctr;
				}
			}
		}

	}
	
}

/// @}

// \endcond