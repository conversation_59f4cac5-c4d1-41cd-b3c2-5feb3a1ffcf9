<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * <PERSON>ull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  642409 => 'Scott Base',
  643 => 'South Island',
  64320 => 'Gore/Edendale',
  64321 => 'Invercargill/Stewart Island/Rakiura',
  64322 => 'Otautau',
  64323 => 'Riverton/Winton',
  64324 => 'Tokanui/Lumsden/Te Anau',
  64330 => 'Ashburton/Akaroa/Chatham Islands',
  64331 => 'Rangiora/Amberley/Culverden/Darfield/Cheviot/Kaikoura',
  64332 => 'Christchurch',
  64333 => 'Christchurch',
  64334 => 'Christchurch/Rolleston',
  64335 => 'Christchurch',
  64337 => 'Christchurch',
  64338 => 'Christchurch',
  643409 => 'Queenstown',
  64341 => 'Balclutha/Milton',
  64343 => 'Oamaru/Mount Cook/Twizel/Kurow',
  64344 => 'Queenstown/Cromwell/Alexandra/Wanaka/Ranfurly/Roxburgh',
  64345 => 'Dunedin/Queenstown',
  64346 => 'Dunedin/Palmerston',
  64347 => 'Dunedin',
  64348 => 'Dunedin/Lawrence/Mosgiel',
  64352 => 'Murchison/Takaka/Motueka',
  64354 => 'Nelson',
  64357 => 'Blenheim',
  64361 => 'Timaru',
  64368 => 'Timaru/Waimate/Fairlie',
  64369 => 'Geraldine',
  64373 => 'Greymouth',
  64375 => 'Hokitika/Franz Josef Glacier/Fox Glacier/Haast',
  64376 => 'Greymouth',
  64378 => 'Westport',
  64390 => 'Ashburton',
  64394 => 'Christchurch/Invercargill',
  64395 => 'Dunedin/Timaru',
  64396 => 'Christchurch',
  64397 => 'Christchurch',
  64398 => 'Christchurch/Blenheim/Nelson',
  64423 => 'Wellington/Porirua/Tawa',
  64429 => 'Paraparaumu',
  6443 => 'Wellington',
  6444 => 'Wellington',
  6445 => 'Wellington/Hutt Valley',
  64480 => 'Wellington',
  6449 => 'Wellington',
  64490 => 'Paraparaumu',
  64627 => 'Hawera',
  64630 => 'Featherston',
  64632 => 'Palmerston North/Marton',
  64634 => 'Wanganui',
  64635 => 'Palmerston North City',
  64636 => 'Levin',
  64637 => 'Masterton/Dannevirke/Pahiatua',
  64638 => 'Taihape/Ohakune/Waiouru',
  64675 => 'New Plymouth/Mokau',
  64676 => 'New Plymouth/Opunake/Stratford',
  64683 => 'Napier/Wairoa',
  64684 => 'Napier City',
  64685 => 'Waipukurau',
  64686 => 'Gisborne/Ruatoria',
  64687 => 'Napier/Hastings',
  64694 => 'Masterton/Levin',
  64695 => 'Palmerston North/New Plymouth',
  64696 => 'Wanganui/New Plymouth',
  64697 => 'Napier',
  64698 => 'Gisborne',
  64730 => 'Whakatane',
  64731 => 'Whakatane/Opotiki',
  64732 => 'Whakatane',
  64733 => 'Rotorua/Taupo',
  64734 => 'Rotorua',
  64735 => 'Rotorua',
  64736 => 'Rotorua',
  64737 => 'Taupo',
  64738 => 'Taupo',
  64754 => 'Tauranga',
  64757 => 'Tauranga',
  64782 => 'Hamilton/Huntly',
  64783 => 'Hamilton',
  64784 => 'Hamilton',
  64785 => 'Hamilton',
  64786 => 'Paeroa/Waihi/Thames/Whangamata',
  64787 => 'Te Awamutu/Otorohanga/Te Kuiti',
  64788 => 'Matamata/Putaruru/Morrinsville',
  64789 => 'Taumarunui',
  64790 => 'Taupo',
  64792 => 'Rotorua/Whakatane/Tauranga',
  64793 => 'Tauranga',
  64795 => 'Hamilton',
  64796 => 'Hamilton',
  6492 => 'Auckland',
  64923 => 'Pukekohe',
  6493 => 'Auckland/Waiheke Island',
  64940 => 'Kaikohe/Kaitaia/Kawakawa',
  64941 => 'Auckland',
  64942 => 'Helensville/Warkworth/Hibiscus Coast/Great Barrier Island',
  64943 => 'Whangarei/Maungaturoto',
  64944 => 'Auckland',
  64947 => 'Auckland',
  64948 => 'Auckland',
  6495 => 'Auckland',
  6496 => 'Auckland',
  6498 => 'Auckland',
  6499 => 'Auckland',
  64990 => 'Warkworth',
  64998 => 'Whangarei',
);
