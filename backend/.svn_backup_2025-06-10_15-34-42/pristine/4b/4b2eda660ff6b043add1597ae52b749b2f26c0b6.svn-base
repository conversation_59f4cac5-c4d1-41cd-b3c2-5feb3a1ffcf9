<?php 

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT_DUPLICATE');

	require_once( 'search.inc.php' );
	require_once( 'products.inc.php' );
	
	if(isset($_POST['search'] )){
		$prd_ref = false;
		$rprd = prd_products_get_simple( 0, $_POST['search'] );
		if( $rprd && ria_mysql_num_rows($rprd) ){
			$prd_ref = ria_mysql_fetch_array( $rprd );
		}

		$html = '
			<table class="cheklists">
				<caption>'._('Produit(s)').'</caption>
				<col width="250" /><col width="25" />
				<thead>
					<tr>
						<th id="col-name">'._('Désignation').'</th>
						<th id="col-select"></th>
					</tr>
				</thead>
				<tbody>
		';

		if( $prd_ref ){
			$html .= '
					<tr>
						<td headers="col-name">'.view_prd_is_sync($prd_ref).' '.htmlspecialchars( $prd_ref['ref'].' - '.$prd_ref['name'] ).'</td>
						<td headers="col-select"><a href="#" data-id="'.$prd_ref['id'].'">'._('Sélectionner').'</a></td>
					</tr>
			';
		}else{
			$results = search( array('seg'=>1, 'keywords'=>$_POST['search'], 'action'=>4, 'type'=>array('prd')), false, 0, 25, false, null, 0, true );
			$max = 10;
			$count = 1;
			if( sizeof($results)>0 ){
				foreach( $results as $r ){
					if( $count >= $max ){
						break;
					}
					$count++;
					$prd = $r['get'];

					$html .= '
						<tr>
							<td headers="col-name">'.view_prd_is_sync($prd).' '.htmlspecialchars( $prd['ref'].' - '.$prd['name'] ).'</td>
							<td headers="col-select"><a href="#" data-id="'.$prd['id'].'">'._('Sélectionner').'</a></td>
						</tr>
					';
				}
			}else{
				$html .= '
						<tr>
							<td colspan="2">'._('Aucun produit').'</td>
						</tr>
				';
			}
		}

		$html .= '
				</tbody>
			</table>
		';
		
		header("Content-Type: application/xml");
		print '<?xml version="1.0" encoding="utf-8"?>
			<result count="'.( isset($results) ? sizeof($results) : 1 ).'"><![CDATA['.$html.']]></result>
		';
	}

	if( isset($_POST['loadPreview'], $_POST['prd']) && prd_products_exists($_POST['prd']) ){
		$prd = ria_mysql_fetch_array( prd_products_get_simple( $_POST['prd'] ) );

		$fields = $parents = $childs = $img_main = $img_oth = $price = $classify = $relation = '';

		if( isset($_POST['info']) ){
			switch( $_POST['info'] ){
				case 'fields': {
					$field_model = fld_fields_get( 0, 0, 0, 0, 0, $prd['id']);
					if( $field_model && ria_mysql_num_rows($field_model) ){
						while( $f = ria_mysql_fetch_array( $field_model ) ){
							$value = '';

							if( $f['type_id']==FLD_TYPE_SELECT){
								$value = fld_restricted_values_get_id($f['id'], $f['obj_value']);
							}elseif( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE ){
								$value = $f['obj_value'];
							}

							if( trim($fields)!='' ){
								$fields .= '<br />';
							}

							$fields .= '<span class="fld-name">'.htmlspecialchars( $f['name'] ).' :</span> '.htmlspecialchars( $value );
						}
					}
					break;
				}
				case 'parents': {
					$rparent = prd_parents_get( $prd['id'], false, false );
					$parents = '
						<table class="cheklists">
							<caption>'._('Produits parents').'</caption>
							<col width="150" />
							<thead>
								<tr>
									<th id="col-name">'._('Désignation').'</th>
								</tr>
							</thead>
							<tbody>
					';

					if( !$rparent || !ria_mysql_num_rows($rparent) ){
						$parents .= '
							<tr>
								<td headers="cal-name">'._('Aucun parent').'</td>
							</tr>
						';
					}else{
						while( $one_parent = ria_mysql_fetch_array($rparent) ){
							$parents .= '
								<tr>
									<td headers="col-name">'.view_prd_is_sync($one_parent).' '.htmlspecialchars( $one_parent['ref'].' - '.$one_parent['name'] ).'</td>
								</tr>
							';
						}
					}

					$parents .= '
							</tbody>
						</table>
					';
					break;
				}
				case 'childs': {
					$rchild = prd_products_get_simple( 0, '', false, 0, false, false, false, false, array('childs'=>true, 'parent'=>$prd['id']) );
					$childs = '
						<table class="cheklists">
							<caption>'._('Produits enfants').'</caption>
							<col width="150" />
							<thead>
								<tr>
									<th id="col-name">'._('Désignation').'</th>
								</tr>
							</thead>
							<tbody>
					';

					if( !$rchild || !ria_mysql_num_rows($rchild) ){
						$childs .= '
							<tr>
								<td headers="cal-name">'._('Aucun parent').'</td>
							</tr>
						';
					}else{
						while( $one_child = ria_mysql_fetch_array($rchild) ){
							$childs .= '
								<tr>
									<td headers="col-name">'.view_prd_is_sync($one_child).' '.htmlspecialchars( $one_child['ref'].' - '.$one_child['name'] ).'</td>
								</tr>
							';
						}
					}

					$childs .= '
							</tbody>
						</table>
					';
					break;
				}
				case 'img-main': {
					if( $prd['img_id'] ){
						$thumb = $config['img_sizes']['high'];
						$img_main = '<img src="'.$config['img_url'].'/'.$thumb['width'].'x'.$thumb['height'].'/'.$prd['img_id'].'.'.$thumb['format'].'" width="'.$thumb['width'].'" height="'.$thumb['height'].'" alt="" />';
					}
					break;
				}
				case 'img-oth': {
					$rimg = prd_images_get( $prd['id'] );
					if( $rimg && ria_mysql_num_rows($rimg) ){
						while( $img = ria_mysql_fetch_array($rimg) ){
							$thumb = $config['img_sizes']['high'];
							$img_oth .= '<img src="'.$config['img_url'].'/'.$thumb['width'].'x'.$thumb['height'].'/'.$img['id'].'.'.$thumb['format'].'" width="'.$thumb['width'].'" height="'.$thumb['height'].'" alt="" />';
						}
					}
					break;
				}
				case 'price': {
					$price = '
						<table class="cheklists">
							<caption>'._('Tarifs').'</caption>
							<col width="150" />
							<tbody>
					';

					$rprices = prc_prices_get( 0, 0, false, false, false, $prd['id'] );
					if( $rprices ){
						$array_prc = array();
						$rprc = prd_prices_categories_get( $config['default_prc_id'] );
						if( $rprc && ria_mysql_num_rows($rprc) ){
							$prc = ria_mysql_fetch_array( $rprc );
							$array_prc[$prc['id']] = array( $prc['name'], 1 );
						}
						$rprc = prd_prices_categories_get();
						if( $rprc && ria_mysql_num_rows($rprc) ){
							while( $prc = ria_mysql_fetch_array( $rprc ) ){
								if( $prc['id']!=$config['default_prc_id'] ){
									$array_prc[$prc['id']] = array( $prc['name'], 0 );
								}
							}
						}
						
						if( sizeof($array_prc) ){
							foreach( $array_prc as $prc_id=>$prc_info ){
								if( $rprice = prd_products_get_price( $prd['id'], 0, $prc_id ) ){
									if( $prc = ria_mysql_fetch_array($rprice) ){
										$price .= '
											<tr>
												<td>
										';

										if( $prc_info[1] ){
											$price .= '
													<b>'.$prc_info[0].' ('._('par défaut').')</b> : 
											';
										}else{
											$price .= '
													'.$prc_info[0].' : 
											';
										}

										if( $prc_info[1] ){
											$price .= '
													<b>'.number_format( $prc['price_ht'], 2, ',', ' ' ).' € '._('HT').'</b> - 
											';
										}else{
											$price .= '
													'.number_format( $prc['price_ht'], 2, ',', ' ' ).' € '._('HT').' - 
											';
										}

										if( $prc_info[1] ){
											$price .= '
													<b class="row_tarif_resume_default">'.number_format( $prc['price_ttc'], 2, ',', ' ' ).' € '._('TTC').'</b>
											';
										}else{
											$price .= '
													'.number_format( $prc['price_ttc'], 2, ',', ' ' ).' € '._('TTC').'
											';
										}

										$price .= '
												</td>
											</tr>
										';
									}
								}
							}
						}else{
							$price .= '
								<tr><td>'._('Aucune information disponible').'</td></tr>
							';
						}
					}
					
					$rprices = prc_prices_get( 0, 0, false, false, false, $prd['id'], false, false, false, null, null, 1, 0, false, null, array(), array(), null, true );
					if( $rprices ){
						$price .= '
							<tr>
								<th>'._('Prix promo').'</th>
							</tr>
						';

						if( ria_mysql_num_rows($rprices) ){
							while( $prc = ria_mysql_fetch_array($rprices) ){
								$price .= '
									<tr>
										<td>'.number_format( $prc['value'], 2 , ',', ' ' ).' € HT</td>
									</tr>
								';
							}
						}else{
							$price .= '
								<tr><td>'._('Aucun tarif promotionel n\'est renseigné').'</td></tr>
							';
						}
					}
					
					$rtva = prc_tvas_get( 0, false, $prd['id'] );
					if( $rtva ){
						$price .= '
							<tr>
								<th>'._('TVA').'</th>
							</tr>
						';

						if( ria_mysql_num_rows($rtva) ){
							while( $tva = ria_mysql_fetch_array($rtva) ){
								$price .= '
									<tr>
										<td> '._('Taux').' : '.number_format( ($tva['rate']-1)*100, 2 , ',', ' ' ).' %</td>
									</tr>
								';
							}
						}else{
							$price .= '
								<tr><td>'._('Aucune TVA n\'est renseignée').'</td></tr>
							';
						}
					}

					$price .= '
							</tbody>
						</table>
					';
					break;
				}
				case 'classify': {
					$classify = '
						<table class="cheklists">
							<caption>'._('Classements').'</caption>
							<col width="150" />
							<tbody>
					';

					$rcly = prd_products_categories_get( $prd['id'] );
					if( !$rcly || !ria_mysql_num_rows($rcly) ){
					    $classify .= '
					    	<tr>
					    		<td>'._('Aucune classification').'</td>
					    	</tr>
					    ';
					}else{
						while( $cly = ria_mysql_fetch_array($rcly) ){
							$ar_categs = array();

							$rparent = prd_categories_parents_get( $cly['cat'] );
							if( $rparent && ria_mysql_num_rows($rparent) ){
							    while( $parent = ria_mysql_fetch_array($rparent) ){
							    	$ar_categs[] = $parent['name'];
							    }
							}

							$ar_categs[] = $cly['name'];

							$classify .= '
								<tr>
									<td>
							';

							// foreach( $ar_categs as $cat ){
								$classify.= htmlspecialchars( implode(' > ', $ar_categs) );
							// }

							$classify .= '
									</td>
								</tr>
							';
						}
					}

					$classify .= '
							</tbody>
						</table>
					';
					break;
				}
				case strstr($_POST['info'], 'rel') : {
					$id = substr( $_POST['info'], 4 );
					
					$rrel = prd_relations_get( $prd['id'], null, $id );
					if( $rrel ){
						$type = ria_mysql_fetch_array( prd_relations_types_get($id) );

						$relation .= '
							<table class="cheklists">
								<caption>'.$type['name_plural'].'</caption>
								<col width="150" />
								<thead>
									<tr>
										<th id="col-rel-name">'._('Désignation').'</th>
									</tr>
								</thead>
								<tbody>
						';

						if( ria_mysql_num_rows($rrel) ){
							while( $rel = ria_mysql_fetch_array($rrel) ){
								$r = ria_mysql_fetch_array( prd_products_get_simple($rel['dst_id']) );

								$relation .= '
									<tr>
										<td headers="col-rel-name">'.view_prd_is_sync($r).' '.htmlspecialchars( $r['ref'].' - '.$r['name'] ).'</td>
									</tr>
								';
							}
						}else{
							$relation .= '
									<tr>
										<td headers="col-rel-name">'._('Aucun produit lié').'</td>
									</tr>
							';
						}

						$relation .= '
								</tbody>
							</table>
						';
					}
					break;
				}
			}
		}

		$infos = array(
			'desc' 		=> $prd['desc'],
			'descLong' 	=> $prd['desc-long'],
			'fields' 	=> $fields,
			'parents'	=> $parents,
			'childs'	=> $childs,
			'img_main' 	=> $img_main,
			'img_oth' 	=> $img_oth,
			'price'		=> $price,
			'classify'	=> $classify,
			'relation'	=> $relation
		);

		header("Content-Type: application/json");
		print json_encode( $infos );
	}
