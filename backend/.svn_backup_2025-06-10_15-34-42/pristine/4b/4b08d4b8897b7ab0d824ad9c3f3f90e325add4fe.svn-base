<?php
/**
 * This file has been @generated by a phing task by {@link BuildMetadataPHPFromXml}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */


namespace libphonenumber;
class ShortNumbersRegionCodeSet {

  // A set of all region codes for which data is available.

   public static $shortNumbersRegionCodeSet = array (
  0 => 'AC',
  1 => 'AD',
  2 => 'AE',
  3 => 'AF',
  4 => 'AG',
  5 => 'AI',
  6 => 'AL',
  7 => 'AM',
  8 => 'AO',
  9 => 'AR',
  10 => 'AS',
  11 => 'AT',
  12 => 'AU',
  13 => 'AW',
  14 => 'AX',
  15 => 'AZ',
  16 => 'BA',
  17 => 'BB',
  18 => 'BD',
  19 => 'BE',
  20 => 'BF',
  21 => 'BG',
  22 => 'BH',
  23 => 'BI',
  24 => 'BJ',
  25 => 'BL',
  26 => 'BM',
  27 => 'BN',
  28 => 'BO',
  29 => 'BQ',
  30 => 'BR',
  31 => 'BS',
  32 => 'BT',
  33 => 'BW',
  34 => 'BY',
  35 => 'BZ',
  36 => 'CA',
  37 => 'CC',
  38 => 'CD',
  39 => 'CF',
  40 => 'CG',
  41 => 'CH',
  42 => 'CI',
  43 => 'CK',
  44 => 'CL',
  45 => 'CM',
  46 => 'CN',
  47 => 'CO',
  48 => 'CR',
  49 => 'CU',
  50 => 'CV',
  51 => 'CW',
  52 => 'CX',
  53 => 'CY',
  54 => 'CZ',
  55 => 'DE',
  56 => 'DJ',
  57 => 'DK',
  58 => 'DM',
  59 => 'DO',
  60 => 'DZ',
  61 => 'EC',
  62 => 'EE',
  63 => 'EG',
  64 => 'EH',
  65 => 'ER',
  66 => 'ES',
  67 => 'ET',
  68 => 'FI',
  69 => 'FJ',
  70 => 'FK',
  71 => 'FM',
  72 => 'FO',
  73 => 'FR',
  74 => 'GA',
  75 => 'GB',
  76 => 'GD',
  77 => 'GE',
  78 => 'GF',
  79 => 'GG',
  80 => 'GH',
  81 => 'GI',
  82 => 'GL',
  83 => 'GM',
  84 => 'GN',
  85 => 'GP',
  86 => 'GR',
  87 => 'GT',
  88 => 'GU',
  89 => 'GW',
  90 => 'GY',
  91 => 'HK',
  92 => 'HN',
  93 => 'HR',
  94 => 'HT',
  95 => 'HU',
  96 => 'ID',
  97 => 'IE',
  98 => 'IL',
  99 => 'IM',
  100 => 'IN',
  101 => 'IQ',
  102 => 'IR',
  103 => 'IS',
  104 => 'IT',
  105 => 'JE',
  106 => 'JM',
  107 => 'JO',
  108 => 'JP',
  109 => 'KE',
  110 => 'KG',
  111 => 'KH',
  112 => 'KI',
  113 => 'KM',
  114 => 'KN',
  115 => 'KP',
  116 => 'KR',
  117 => 'KW',
  118 => 'KY',
  119 => 'KZ',
  120 => 'LA',
  121 => 'LB',
  122 => 'LC',
  123 => 'LI',
  124 => 'LK',
  125 => 'LR',
  126 => 'LS',
  127 => 'LT',
  128 => 'LU',
  129 => 'LV',
  130 => 'LY',
  131 => 'MA',
  132 => 'MC',
  133 => 'MD',
  134 => 'ME',
  135 => 'MF',
  136 => 'MG',
  137 => 'MH',
  138 => 'MK',
  139 => 'ML',
  140 => 'MM',
  141 => 'MN',
  142 => 'MO',
  143 => 'MP',
  144 => 'MQ',
  145 => 'MR',
  146 => 'MS',
  147 => 'MT',
  148 => 'MU',
  149 => 'MV',
  150 => 'MW',
  151 => 'MX',
  152 => 'MY',
  153 => 'MZ',
  154 => 'NA',
  155 => 'NC',
  156 => 'NE',
  157 => 'NF',
  158 => 'NG',
  159 => 'NI',
  160 => 'NL',
  161 => 'NO',
  162 => 'NP',
  163 => 'NR',
  164 => 'NU',
  165 => 'NZ',
  166 => 'OM',
  167 => 'PA',
  168 => 'PE',
  169 => 'PF',
  170 => 'PG',
  171 => 'PH',
  172 => 'PK',
  173 => 'PL',
  174 => 'PM',
  175 => 'PS',
  176 => 'PR',
  177 => 'PT',
  178 => 'PW',
  179 => 'PY',
  180 => 'QA',
  181 => 'RE',
  182 => 'RO',
  183 => 'RS',
  184 => 'RU',
  185 => 'RW',
  186 => 'SA',
  187 => 'SB',
  188 => 'SC',
  189 => 'SD',
  190 => 'SE',
  191 => 'SG',
  192 => 'SH',
  193 => 'SI',
  194 => 'SJ',
  195 => 'SK',
  196 => 'SL',
  197 => 'SM',
  198 => 'SN',
  199 => 'SO',
  200 => 'SR',
  201 => 'ST',
  202 => 'SV',
  203 => 'SX',
  204 => 'SY',
  205 => 'SZ',
  206 => 'TC',
  207 => 'TD',
  208 => 'TG',
  209 => 'TH',
  210 => 'TJ',
  211 => 'TL',
  212 => 'TM',
  213 => 'TN',
  214 => 'TO',
  215 => 'TR',
  216 => 'TT',
  217 => 'TV',
  218 => 'TW',
  219 => 'TZ',
  220 => 'UA',
  221 => 'UG',
  222 => 'US',
  223 => 'UY',
  224 => 'UZ',
  225 => 'VA',
  226 => 'VC',
  227 => 'VE',
  228 => 'VG',
  229 => 'VI',
  230 => 'VN',
  231 => 'VU',
  232 => 'WF',
  233 => 'WS',
  234 => 'XK',
  235 => 'YE',
  236 => 'YT',
  237 => 'ZA',
  238 => 'ZM',
  239 => 'ZW',
);

}
