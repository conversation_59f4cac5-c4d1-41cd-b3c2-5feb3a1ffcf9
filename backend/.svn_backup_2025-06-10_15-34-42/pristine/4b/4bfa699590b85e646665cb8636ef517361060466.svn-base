var currentAjaxRequest = false;

$(document).ready(function(){
	// mise en place du sélecteur de site
	$('#riawebsitepicker .selectorview').click(function(){
		if($('#riawebsitepicker .selector').css('display')=='none'){
			$('#riawebsitepicker .selector').show();
		}else{
			$('#riawebsitepicker .selector').hide();
		}
	});
	$("#riawebsitepicker .selector a").click(function(){
		var w = $(this).attr('name').substring( $(this).attr('name').indexOf('-')+1, $(this).attr('name').length );
		var name = $(this).html();
		
		currentAjaxRequest = $.ajax({
			type: "POST",
			url: '/admin/config/redirections/permanent/js_languages.php',
			data: 'wst=' + w + '&lng=' + $('#lng').val(),
			async:true,
			success: function( html ){
				$('#wst_id').val( w );
				
				if( $('#rialanguagepicker').length )
					$('#rialanguagepicker').remove();
				
				if( html!='' ){
					$('#riawebsitepicker').after( html );
					activeActionLanguage();
				}
				
				// action sur le menu site
				$('#riawebsitepicker .selector').hide();
				$('#riawebsitepicker .view').html( name );
				
				// rechargement ajax de la liste des redirection
				reload_redirections(1);
			},
			complete: function(){ currentAjaxRequest = false; }
		});
	});
	
	// ouvre la popup d'ajout d'une redirection
	$('#add-red').click(function(){
		var url = '/admin/config/redirections/permanent/popup_add.php';
		displayPopup(redirectionAjoutPermanent, '', url, "hidePopupAdd()");	
		$('#popup_riawysiwyg').fadeIn();
		return false;
	});
	
	if( $('#new-dest').length ){
		$('#new-dest').autocomplete({
			source: '/admin/ajax/redirections/search-redirections.php',
			minLength: 2,
		});
	}
	
	// mise en place du sélecteur de langue
	if( $('#rialanguagepicker').length ){
		activeActionLanguage();
	}
	
	// mise en place du filtre sur le début des urls retournant une erreurs 404
	$('#filter-url').keyup(function(){ reload_redirections(1)	});
	
	if( typeof $('#wst') != 'undefined' && $('#wst').length ){
		$('#wst').change(function(){
			$('#langue').hide();
			currentAjaxRequest = $.ajax({
				type: "POST",
				url: '/admin/config/redirections/permanent/js_languages.php',
				data: 'wst=' + $('#wst').val() + '&type=SELECT',
				async: true,
				success: function( html ){
					if( html!='' ){
						$('#langue select').html( html );
						$('#langue').show();
					}
				},
				complete: function(){ currentAjaxRequest = false; }
			});
		});
	}
});

function hidePopupAdd(){
	hidePopup();
	reload_redirections(1);
	return false;
}
function activeActionLanguage(){
	$('#rialanguagepicker .selectorview').click(function(){
		if($('#rialanguagepicker .selector').css('display')=='none'){
			$('#rialanguagepicker .selector').show();
		}else{
			$('#rialanguagepicker .selector').hide();
		}
	});
	$("#rialanguagepicker .selector a").click(function(){
		const l = $(this).attr('name').substring( $(this).attr('name').indexOf('-')+1, $(this).attr('name').length );
		const name = $(this).html();
		
		$('#lng').val( l );
		reload_redirections(1);
		
		// action sur le menu langue
		$('#rialanguagepicker .selector').hide();
		$('#rialanguagepicker .view').html( name );
	});
}

function reload_redirections( page ){
	if( currentAjaxRequest ){
		currentAjaxRequest.abort();
	}
	
	var filter = $('#filter-url').val();
	var wst = $('#wst_id').val();
	var lng = $('#lng').val();
	
	$('#page').val( page );
	$('#filter').val( filter );
	$('#lst-redirections tbody').html('<tr><td colspan="9" style="padding:5px;"><img class="loader" src="/admin/images/stats/loader.gif" alt="loader"/> ' + msgLoading + '</td></tr>');
	
	currentAjaxRequest = $.ajax({
		type: 'GET',
		url: '/admin/ajax/redirections/permanent/json-permanent.php',
		data: 'filter=' + filter + '&wst=' + wst + '&lng=' + lng + '&page=' + page,
		dataType: 'json',
		async:true,
		success: function(json){
			var html = '';
			
			if( json['nombre']==0 )
				html = '<td colspan="4">' + redirectionAucuneRedirection + '</td>';
			else if( json['permanent'].length ){
				
				var colspan = 0;
				
				// gestion de l'affichage du tableau selon les filtres
				if( $('#lst-redirections #url_site').length ){
					if( (wst!='' && wst!='all' && wst!=0) || json['websites']<=1 ){
						$('#lst-redirections #url_site').hide();
						colspan++;
					} else {
						$('#lst-redirections #url_site').show();
					}
				}
				
				if( $('#lst-redirections #url_lng').length ){
					if( (lng!='' && lng!='all') || json['languages']<=1 ){
						$('#lst-redirections #url_lng').hide();
						colspan++;
					} else {
						$('#lst-redirections #url_lng').show();
					}
				}
				
				if( colspan>0 ){
					$('#lst-redirections #url_dest').attr('colspan', colspan+1);
				} else {
					$('#lst-redirections #url_dest').removeAttr('colspan');
				}
				
				for( var c=0 ; c<json['permanent'].length ; c++ ){
					var p = json['permanent'][c];
					
					html += '	<tr>';
					html += '		<td headers="url_source"><a target="_blank" href="' + p.url_site + p.source + '">' + p.source + '</a></td>';
					html += '		<td headers="url_dest"' + ( colspan>0 ? ' colspan="' + (colspan+1) + '"' : '' ) + '><a target="_blank" href="' + p.url_site + p.dest + '">' + p.dest + '</a></td>';
					if( (wst=='' || wst=='all' || wst==0) && json['websites']>1 ){
						html += '		<td headers="url_site" align="center">' + p.website + '</td>';
					}
					if( (lng=='' || lng=='all') && json['languages']>1 ){
						html += '		<td headers="url_lng" align="center">' + p.version + '</td>';
					}
					html += '	</tr>';
				}
				
			}
			
			var links = new Array();
			var nbred = json['nombre'].replace(' ', '');
			var pages = Math.ceil( nbred/30 );
			var minPage = page - 4;
			minPage = minPage>0 ? minPage : 1;
			
			var count = 0;
			if( page>1 )
				links[count++] = '<a onclick="return reload_redirections(' + (page-1) + ')" href="index.php?page=' + (page-1) + '&wst=' + wst + '&filter=' + encodeURIComponent(filter) + '">&laquo; ' + redirectionPrec + '</a>';
			
			for( p=minPage; p<=pages; p++ ){
				if( p==page )
					links[count++] = '<b>'+p+'</b>';
				else
					links[count++] = '<a onclick="return reload_redirections(' + p + ')" href="index.php?page=' + p + '&wst=' + wst + '&filter=' + encodeURIComponent(filter) + '">'+p+'</a>';
				if( count>9 )
					break;
			}
			
			if( page<pages )
				links[count++] = '<a onclick="return reload_redirections(' + (page+1) + ')" href="index.php?page=' + (page+1) + '&wst=' + wst + '&filter=' + encodeURIComponent(filter) + '">' + redirectionSuiv + ' &raquo;</a>';
			
			$('#lst-redirections tbody').html( html );
			$('#pagination').html( links.join(' | ') );
		},
		complete: function(){ currentAjaxRequest = false; }
	});
	return false;
}
