<?php

	/**	\file index.php
	 *	Cette page est destinée à la gestion des rétractions. Elle détermine le délai durant lequel un consommateur
	 *	à la possibilité de retourner un produit commandé sur le site.
	 */
    
	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_RETRACTATION');

    $error = false;
    $success = false;
    if( isset($_POST['save']) ){
        if ( $_POST['retractation_delay'] == '' || !is_numeric($_POST['retractation_delay']) || ($_POST['retractation_delay'] < 14 && $_POST['retractation_delay'] != 0 ) ){
            $error = _('Le nombre de jours n\'est pas valide. Il doit être supérieur à 14 jours, ou égal à 0 pour un délai non limité.');
        } 

        if( !$error ){
            if( !cfg_overrides_set_value( 'retractation_delay', $_POST['retractation_delay'] ) ){
                $error = _('Une erreur inconnue s\'est produite lors de l\'enregistrement du délai maximum de rétractation.');
            }else{
                $config['retractation_delay'] = $_POST['retractation_delay'];
                $success = _('Le délai maximum de rétractation à bien été mis à jour.');
            }
        }
        
    }

	// Défini le titre de la page
    define('ADMIN_PAGE_TITLE', _('Délais de rétraction') . ' - ' . _('Configuration'));
    require_once('admin/skin/header.inc.php');

	// Affichage des messages d'erreur
    if( $error ){
        print '<div class="error">'.htmlspecialchars( $error ).'</div>';
    }

	// Affichage des messages de succès
    if( $success ){
        print '<div class="success">'.htmlspecialchars( $success ).'</div>';
    }
?>

<h2><?php print _('Gestion des rétractations'); ?></h2>

<h3><?php print _('Délai d\'autorisation des rétractations'); ?></h3>

<div class="notice"><?php print _('Saisissez 0 jour pour ne pas tenir compte du délai de rétractation.'); ?></div>

<form method="POST" action="index.php">
    <?php print _('Le consommateur dispose d’un délai de 14 jours après la livraison de ses produits pour changer d’avis sur son achat selon le délai légal.'); ?>
    <br>
    <?php 
        print _('Vous avez la possibilité d’offrir à vos clients un délai plus long pour qu’ils retournent leurs articles :').' ';
        printf(_('vous acceptez les rétractations jusqu’à %s jours après la date de livraison.'), '<input class="small" type="number" min="0" class="rgt" step="1" name="retractation_delay" value="'.$config['retractation_delay'].'"/>');
    ?>
    <p class="mart20"><input type="submit" value="<?php print _('Enregistrer'); ?>" name="save"/></p>
</form>
<?php
    require_once('admin/skin/footer.inc.php');
?>