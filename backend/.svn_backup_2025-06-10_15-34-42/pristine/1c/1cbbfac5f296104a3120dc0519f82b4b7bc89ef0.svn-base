<documentation title="SAPI Usage">
    <standard>
    <![CDATA[
    The PHP_SAPI constant should be used instead of php_sapi_name().
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: PHP_SAPI is used.">
        <![CDATA[
if (<em>PHP_SAPI</em> === 'cli') {
    echo "Hello, CLI user.";
}
        ]]>
        </code>
        <code title="Invalid: php_sapi_name() is used.">
        <![CDATA[
if (<em>php_sapi_name()</em> === 'cli') {
    echo "Hello, CLI user.";
}
        ]]>
        </code>
    </code_comparison>
</documentation>
