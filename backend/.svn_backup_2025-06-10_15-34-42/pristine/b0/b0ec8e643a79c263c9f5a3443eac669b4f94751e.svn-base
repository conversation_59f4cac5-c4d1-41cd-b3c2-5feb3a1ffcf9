.clear {
	clear: both;
}
#export {
	padding-bottom: 10px;
}
#export h2 {
	border-bottom: 1px solid #A9A9A9;
	font-size: 16px;
	height: 30px;
	margin-bottom: 10px;
	padding-bottom: 3px;
}
#export .part-export {
	display: block;
	font-weight: 600;
	margin-top: 15px;
}
.elems {
	float: left0/;
	width: 25%0/;
	*float: left;
	*width: 25%;
}
#export .cols label, #export .check-catchilds label {
	cursor: pointer;
}
#export .size-images {
	margin-left: 24px;
}
#export .check-catchilds {
	margin-top: 15px;
}
#save-form-export {
	margin-top: 15px;
}
#save-form-export ul {
	list-style: disc inside none;
	margin-left: 30px;
}
#save-form-export table {
	margin: 10px auto;
	width: 440px;
}
#save-form-export input[type=text]{
	width: 100%;
}
#save-form-export tr td select {
	width: auto;
}
#save-form-export .period-week, #save-form-export .period-month {
	display: inline-block;
}
#export .block-hide {
	display: none;
}
#export .export-action {
	width: 100%;
	text-align: right;
}
#zone-list-export {
	text-align: center;
	border-bottom: 1px solid #C4C4C4;
	margin-bottom: 15px;
	padding-bottom: 15px;
}
#zone-list-export select {
	width: 400px;
}

tbody .exp-err {
	color: red;
}
 
tbody .exp-processing {
	color: blue;
}

tbody .exp-success {
	color: green;
}

.table-export strong {
    font-weight: 1000;
}