<?php
	require_once('PaymentExternal/Payzen.inc.php');

	/** \defgroup systempay Systempay
	 *	\ingroup payment_external
	 *
	 *	Ce module permet les paiement avec Systempay
	 *	Variables de config obligatoire
	 *			- systempay_site_id : identifiant du site
	 *			- systempay_certicat : certificat à utiliser (permet de calculer la signature de contrôle)
	 *			- systempay_certicat_dev : certificat à utiliser (seulement en mode maquette - permet de calculer la signature de contrôle)
	 *			- systempay_contract : numéro de contract (optionnel, mais fortement conseillé lors d'une gestion de plusieurs contrat - click & collect)
	 *			- systempay_url_error : url lors d'un échec de paiement
	 *			- systempay_url_cancel : url lors de l'annulation d'un paiement
	 *			- systempay_url_return_ok : url lors d'un paiement réussi (surcharge celle renseignée dans l'espace marchand Systempay)
	 *
	 *			- systempay_url_return_register : url de retour dans le cas d'une création ou mise à jour d'un compte carte
	 *			- systempay_url_cancel_register : url lors de l'annulation de création ou mise à jour d'un compte carte
	 *			- systempay_url_error_register : url lors d'un échec de création ou mise à jour d'un compte carte
	 *			- systempay_state_multi : statut final de la commande lors d'un paiement en plusieurs fois (par défaut à 4)
	 *
	 *	Ces infos sont disponibles dans l'inteface SystemPay en ligne (Paramétrages > Boutique > %boutique%}
	 *	La signature est visible dans > Certificats (il faut valider les tests pour obtenir le certificat de production)
	 *
	 *	Exemple : Paiement par identifiant
	 *	\code{.php}
	 *		$systempay = new Systempay();
	 *		$systempay->createSimplePayment();
	 *		$systempay->getIdentifierID( $card_ID ); // -> Optionnel
	 *		$systempay->activePayByIdentifier();
	 *	\endcode
	 *
	 *	Exemple : Paiement en plusieurs fois
	 *	\code{.php}
	 *		$systempay = new Systempay();
	 *		$systempay->createMultiPayment( 1500, 3, 30 );
	 *	\endcode
	 *
	 *	Exemple : Paiement en plusieurs fois (échéancier personnalisé)
	 *	\code{.php}
	 *		$systempay = new Systempay();
	 *		$systempay->createRecurrence( '2014-12-12', 3000, 'RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10', 1750, 3 );
	 *	\endcode
	 *
	 *	Exemple : Mise en place d'une récurrence (abonnement), avec ou sans paiement (@todo : Il reste à brancher cette partie avec prd_subscription / ord_subscription)
	 *	\code{.php}
	 *		$systempay = new Systempay();
	 *		$systempay->createRecurrence( '2014-12-12', 3000, 'RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10', 1750, 3 );
	 *		$systempay->activePayByIdentifier();
	 *	\endcode
	 *
	 *	@{
	 */

	/**	\brief Cette classe est l'implémentation concrète du fournisseur Systempay en tant que prestataire de paiement externe.
	 *
	 */
	class Systempay extends Payzen {
		protected $provider = 'systempay';
		protected $module = 'SYSTEMPAY';
		protected $key = 'SystemPay';
		protected $form_url = 'https://paiement.systempay.fr/vads-payment/';
		protected $form_id = 'form-systempay-access';


		/**	Cette méthode permet d'activer la méthode HMAC pour la génération de la clé de hachage
		 * @todo	Si utilisée, doit être activée le plus tôt possible
		 * @return	object	L'objet en cours
		 */
		public function activateHmac(){
			$this->hmac = true;

			return $this;
		}
	}

/// @}
