<?php
	/**	\file refresh-header-orders.php
	 *	Ce script est actuellement lancé chaque jour pour actualiser la marge réalisée par commande ainsi que le nombre de produits distincts
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('orders.inc.php');

	$all_orders = isset($ar_params['all_orders']) && $ar_params['all_orders'] == 'all' ? true : false;
	
	foreach ($configs as $config) {
		$period = array();
		if (!$all_orders) {
			$period = array(
				'start' => date('Y-m-d', strtotime('-1 day')),
				'end' => date('Y-m-d'),
				'col' => 'ord_date_modified'
			);
		}
		
		$r_order = ord_orders_get_simple(
			array(), 
			$period, 
			array(
				// 'state_id' => ord_states_get_ord_valid()
			)
		);
		
		while ($order = ria_mysql_fetch_assoc($r_order)) {
			ord_orders_update_header($order['id'], $order['piece']);
		}
	}
