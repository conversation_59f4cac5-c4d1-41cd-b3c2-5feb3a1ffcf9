<?php
	/** \file devices-check.php
	 * 	Ce script est destiné à contrôler les abonnements en cours des smartphones et tablettes Yuto et à envoyer une notification
	 *  dans le cas où l'échéance d'un abonnement arrive à expiration.
	 *	4 écheances: 60, 30, 7, 0 jours
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once( 'devices.inc.php' );

	$email_receiver = array('<EMAIL>');

	$delays = array(
					array(	"delay"=>0,
							"color"=>"red",
							"text"=>"Expiré"),
					array(	"delay"=>7,
							"color"=>"orange",
							"text"=>"7 jours"),
					array(	"delay"=>30,
							"color"=>"lightseagreen",
							"text"=>"1 mois"),
					array(	"delay"=>60,
							"color"=>"green",
							"text"=>"2 mois")
					);

	$email = new Email();
	$email->setFrom( '<EMAIL>' );
	$email->addTo( $email_receiver );

	$email->setSubject( 'Echéance d\'abonnements de Yuto' );

	$found = false;

	// Traitement
	foreach( $configs as $config ){
		// force le tnt dans la session pour ne récupérer que les devices liées au tenant et exclure les supers admin
		$_SESSION['usr_tnt_id'] = $config['tnt_id'];

		$first = true;
		$max_activable = dev_subscribtions_get_max() != null ? dev_subscribtions_get_max() : 0;

		foreach ($delays as $delay) {
			$rsubs = dev_subscribtions_get( $delay["delay"] );

			// On récupère le nombre d'abonnements
			if ( $rsubs && ria_mysql_num_rows($rsubs) > 0) {
				$found = true;
				$subs_total = ria_mysql_num_rows($rsubs);


				if ($first) {
					$email->openTable(570);
					$email->addParagraph('<strong>Client : '.tnt_tenants_get_name($config['tnt_id']).'</strong>');
					$email->addParagraph('Nombre de tablette activables : '.$max_activable);
					$email->openTableRow();
					$email->addCell( '<b>N°</b>' );
					$email->addCell( '<b>Nombre de tablettes</b>' );
					$email->addCell( '<b>Date de début</b>' );
					$email->addCell( '<b>Date de fin</b>' );
					$email->addCell( '<b>Echéance</b>' );
					$email->closeTableRow();
				}

				while ($sub = ria_mysql_fetch_assoc($rsubs)) {

					$email->openTableRow();
					$email->addCell( ''.$sub["id"].'' );
					$email->addCell( '<span>'.$sub["qte"].'</span>' );
					$email->addCell( '<span>'.$sub["date_start"].'</span>' );
					$email->addCell( '<span>'.$sub["date_end"].'</span>' );
					$email->addCell( '<span style="color:'.$delay["color"].'"><strong>'.$delay["text"].'</strong></span>' );
					$email->closeTableRow();
				}

				$first = false;
			}
		}
		if (!$first) {
			$email->closeTable();
		}
		$email->addBlankTextLine();

		$actived_devices = dev_devices_get( 0, 0, '', -1, '=', false, false, true, true, true, false, false );
		if ( ria_mysql_num_rows($actived_devices) > $max_activable ) {
			$found = true;

			// Si quantité == 0 alors toutes les tablettes
			$qte_to_deactivate = $max_activable > 0 ? ria_mysql_num_rows($actived_devices) - $max_activable : 0 ;

			$email->addParagraph('<strong>Client : '.tnt_tenants_get_name($config['tnt_id']).'</strong>');
			$email->addParagraph('Nombre de licences actives : '.ria_mysql_num_rows($actived_devices));
			$email->addParagraph('Nombre de licences activables : '.$max_activable);
			$email->addParagraph('Nombre d\'appareils désactivées : '.$qte_to_deactivate);

			$last_devices = dev_devices_get( 0, 0, '', -1, '=', false, false, null, true, $qte_to_deactivate, array('date_created'=>'desc'), false);
			while( $last_device = ria_mysql_fetch_array($last_devices) ){
				dev_devices_deactivate($last_device["id"]);
			}
		}
	}

	if ($found) {
		$email->send();
	}