<?php

	/**	\file popup-stores.php
	 * 
	 * 	Ce fichier fourni une interface permettant la sélection d'un ou plusieurs magasins
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE');

	$is_ajax = false; 
	$limit_stores = 100;

	$select = isset($_GET['select']) && in_array($_GET['select'], array('radio', 'checkbox')) ? $_GET['select'] : 'radio';

	$url_params = '';
	if( isset($_GET['input_id_store_id']) && trim($_GET['input_id_store_id']) != '' ){
		$url_params .= ( trim($url_params) != '' ? '&' : '?').'input_id_store_id='.$_GET['input_id_store_id'];
	}
	if( isset($_GET['callback']) && trim($_GET['callback']) != '' ){
		$url_params .= ( trim($url_params) != '' ? '&' : '?').'callback='.$_GET['callback'];
	}
	if( isset($_GET['select']) && trim($_GET['select']) != '' ){
		$url_params .= ( trim($url_params) != '' ? '&' : '?').'select='.$_GET['select'];
	}

	//si requete ajax on ne va pas plus loin
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}
	
	if( !$is_ajax ) { 
		define('ADMIN_PAGE_TITLE', _('Sélection de magasins'));
		define('ADMIN_ID_BODY', 'popup-content');
		define('ADMIN_HEAD_POPUP', true);
		require_once('admin/skin/header.inc.php');
	}
?>
	<form action="/admin/orders/create/ajax/ncmd-customers-edit.php<?php print $url_params; ?>" method="get">
		<?php 

		// affiche les types de documents
		print '<table class="checklist" cellspacing="0" cellpadding="0">';
		print '	<caption>' . _("Sélection de magasins") . '</caption>';
		print '	<col width="*" />';

		$rcms = dlv_stores_get( 0, null, array('name'=>'asc'));
		if( $rcms && ria_mysql_num_rows($rcms) ){

			$page = isset( $_GET['p'] ) && is_numeric($_GET['p']) ? $_GET['p'] : 1;
			$pages = ceil( ria_mysql_num_rows($rcms) / $limit_stores );

			if( $rcms ){
				print '	<thead>';
				print '		<tr>';
				print '			<th>' . _("Nom") . '</th>';
				print '			<th>' . _("Code postal") . '</th>';
				print '			<th>' . _("Ville") . '</th>';
				print '			<th>' . _("Pays") . '</th>';
				print '			<th></th>';
				print '		</tr>';
				print '	</thead>';
				
				print '	<tfoot>';
				print '		<tr id="pagination">';
				print '			<td class="page">' . _("Page") . ' '.$page.'/'.$pages.'</td>';
				print '			<td colspan="3" class="pages">';
				for( $i= ( $page-5 < 1 ? 1 : $page-5) ; $i<=( $page+5 > $pages ? $pages : $page+5); $i++ ){
					if( $i==$page )
						print '		<b>'.$page.'</b>';
					else
						print '		<a name="selectstore" data-page="'.$i.'" data-url="'.str_replace('?', '', $url_params).'">'.$i.'</a>'; 
					
					if( $i<$pages )
						print ' | ';
				}
				print '			</td>';
				print '		</tr>';
				print '	</tfoot>';
			
				$count = 0;
				print '	<tbody>';
				if( ria_mysql_num_rows($rcms) ){
					ria_mysql_data_seek( $rcms, ($page-1)*$limit_stores );
					
					while( $cms = ria_mysql_fetch_array($rcms) ){
						if( $count >= $limit_stores ) break;
						
						print '	<tr>';
						print '		<td>';

						if( $select == 'radio' ){
							print '			<input class="radio" type="radio" name="store" id="store-'.$cms['id'].'" value="'.$cms['id'].'"  data-name="'.htmlspecialchars($cms['name']).'" />';
						}else{
							print '			<input class="radio" type="checkbox" name="store" id="store-'.$cms['id'].'" value="'.$cms['id'].'"  data-name="'.htmlspecialchars($cms['name']).'" />';
						}

						$cmschild = cms_categories_get( 0, false, false, $cms['id'], false, false, true, null, false, null, false );
						if( $cmschild && ria_mysql_num_rows($cmschild) ){
							print '		<a name="selectstore" data-id="'.$cms['id'].'" data-page="'.$page.'" data-url="'.str_replace('?', '', $url_params).'">'.$cms['name'].'</a>'; 
						} else {
							print '		<label for="store-'.$cms['id'].'">'.htmlspecialchars( $cms['name'] ).'</label>';
						}

						print ' 	</td>';
						print ' 	<td>'.htmlspecialchars( $cms['zipcode'] ).'</td>';
						print ' 	<td>'.htmlspecialchars( $cms['city'] ).'</td>';
						print ' 	<td>'.htmlspecialchars( $cms['country'] ).'</td>';
						print ' 	<td align="center"></td>';
						print ' </tr>';
						$count++;
					}
				}
				
				print '	</tbody>';
			}
		}
		print '	</table>';

		print '	<div class="pop-form-search">';
		print ' 	<input class="btn-action" type="button" name="selectstore" id="selectstore" value="' . _("Sélectionner") . '" />';
		print ' 	<input class="btn-action cancel" onclick="parent.hidePopup();" type="button" name="cancel" id="cancel" value="' . _("Annuler") . '" />';
		print ' </div>';
		
	?></form>

	<?php if( isset($_GET['input_id_store_id']) && trim($_GET['input_id_store_id']) != '' ){ ?>
	<script>
		$(document).ready(function(){
			$('#selectstore').click(function(){
				$('.error').remove();
				if( !$('input[type=radio]:checked, input[type=checkbox]:checked').length ){
					$('.pop-form-search').before( '<div class="error"><?php print _("Veuillez sélectionner un magasin."); ?></div>' );
					return false;
				}

				var arStore = new Array();
				$('input[type=radio]:checked, input[type=checkbox]:checked').each(function(){
					arStore.push({
						id: $(this).val(),
						name: $(this).parent().find('label').html()
					});
				});
				
				window.parent.<?php print $_GET['callback']; ?>( '<?php print $_GET['input_id_store_id']; ?>', arStore );
				return false;
			});
		});
	</script>
	<?php } ?>
<?php 
	if( !$is_ajax ){ 
		require_once('admin/skin/footer.inc.php');
	}
?>