<?php

	/**	\file json-errors.php
	 * 
	 * 	Ce fichier est appelé en Ajax et retourne la liste des erreurs 404 survenues sur le(s) site(s) qui correspondent aux filtres passés en argument :
	 *  - no-redirection : Facultatif, par défaut toutes les erreurs sont retournées, mettre à true pour n'avoir que celles qui n'ont pas de redirection
	 *  - resolved : Facultatif, par défaut seules les erreurs 404 non résolues sont retournées, mettre à true pour avoir que celles qui ont été notées comme résolues
	 *  - lng : Facultatif, code langue sur lequel filtrer le résultat
	 * 	- filter : Facultatif, permet de filtrer les urls retournées sur un mot clé. La recherche est très simple, de type like '%motcle%'.
	 * 		Cette recherche portera aussi bien sur les urls internes que externes.
	 *  - page : Facultatif, numéro de la page de résultat à retourner
	 *  - date1 : Facultatif, date de début de la recherche
	 *  - date2 : Facultatif, date de fin de la recherche
	 *  - sort : Facultatif, colonne de tri et direction de tri, sous la forme colonne-[asc|desc]
	 * 
	 * 	L'utilisateur doit disposer du droit d'accès _RGH_ADMIN_CONFIG_REDIRECTION
	 * 
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REDIRECTION');

$count = 0;
$errors = $websites = array();

if( isset($_GET['filter'], $_GET['filter-type']) ){
	view_date_in_session($_GET['date1'], $_GET['date2']);

	$r_websites = wst_websites_get();
	while( $website = ria_mysql_fetch_array($r_websites) ){
		$websites[$website['id']] = $website['name'];
	}

	$website_id = isset($_GET['wst']) ? $_GET['wst'] : 0;

	list($sortKey, $sortValue) = explode('-', $_GET['sort']);

	$_GET['no-redirection'] = isset($_GET['no-redirection']) && $_GET['no-redirection'];
	$_GET['resolved'] = isset($_GET['resolved']) && $_GET['resolved'];
	$_GET['filter'] = isset($_GET['filter']) ? $_GET['filter'] : '';
	$_GET['page'] = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] ? $_GET['page'] : 1;
	$_GET['lng'] = isset($_GET['lng']) && $_GET['lng'] !== 'all' ? $_GET['lng'] : false;
	$_GET['sort'] = array(
		$sortKey => $sortValue,
	);

	// Récupère les erreurs 404.
	$r_errors = err_errors_get($website_id, '', $_GET['no-redirection'], $_GET['filter'], $_GET['filter-type'], $_GET['resolved'], $_GET['date1'], $_GET['date2'], $_GET['sort'], $_GET['lng']);

	// Création du tableau des résultats.
	$errors['nombre'] = 0;
	$errors['websites'] = count($websites);
	$errors['languages'] = isset($config['i18n_lng_used']) && is_array($config['i18n_lng_used']) ? count($config['i18n_lng_used']) : 1;
	$errors['errors'] = array();

	if( $r_errors ){
		$errors['nombre'] = ria_mysql_num_rows($r_errors);

		if( $_GET['page'] > 1 && $errors['nombre'] > ($_GET['page'] - 1) * 25 ){
			ria_mysql_data_seek($r_errors, ($_GET['page'] - 1) * 25);
		}elseif( $_GET['page'] > 1 ){
			$page = ceil($errors['nombre'] / 25);
			$errors['page'] = $page;

			ria_mysql_data_seek($r_errors, ($page - 1) * 25);
		}

		while( $error = ria_mysql_fetch_assoc($r_errors) ){
			$errors['errors'][] = array(
				'wst' => $error['wst'],
				'url' => $error['url'],
				'hiddenurl' => urlencode($error['url']),
				'lng' => $error['lng'],
				'lng_name' => i18n_languages_get_name( $error['lng'] ),
				'count' => ria_number_format($error['count']),
				'last' => $error['last'],
				'referer' => $error['referer'],
				'first' => $error['first'],
				'first_fr' => $error['first_fr'],
				'last_fr' => $error['last_fr'],
				'redirection' => err_errors_redirection_get($error['wst'], $error['url'], $error['lng']),
				'resolved' => $error['resolved'],
		  	);

			if( $errors['websites'] && $website_id <= 0 ){
				$errors['errors'][$count]['wst_name'] = $websites[$error['wst']];
			}

			if( $count > 24 ){
				break;
			}

			$count++;
		}
	}
}

print json_encode($errors);