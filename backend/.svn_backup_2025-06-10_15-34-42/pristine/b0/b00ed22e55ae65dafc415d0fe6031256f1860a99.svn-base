function getParams(){
	var strparams = window.location.search;
	var i = 0;
	var res = {};
	var params, strparam, param;

	strparams = decodeURI(strparams);
	strparams = strparams.replace('?', '');

	params = strparams.split('&');

	if( params.length < 1 ){
		return res;
	}

	for(i; i < params.length; i++ ){
		strparam = params[i];
		param = strparams.split('=');

		if( param.length !== 2 ){
			continue;
		}
		res[param[0]] = param[1];
	}
	return res;

}

$(document).ready(function(){
	var ordID = 0;
	var params = getParams();

	if( 'ord' in params && (typeof params.ord === 'number' || typeof params.ord === 'string') ){
		ordID = parseInt(params.ord);

	}

	$('input[name=update-rights]').live('click', function(){
		displayPopup(orderModelsAutorisationModele, '', '/admin/ajax/orders/ncmd-rights.php', false, 800, 400);
		return false;
	});

	if( typeof $('#order-models') != 'undefined' && $('#order-models').length ){
		riaSortable.create({
			'table'	: $('#order-models'),
			'url'	: '/admin/ajax/orders/order-models-position-update.php'
		});
	}

	if( typeof $('#order-products') != 'undefined' && $('#order-products').length ){
		riaSortable.create({
			'table'	: $('#order-products'),
			'url'	: '/admin/ajax/orders/order-models-products-position-update.php?ord='+ordID
		});
	}
	var ajaxNotification = false;
	$("#send-notif").click(function(e){
		e.preventDefault();

		$(".notif").html('');
		if( ajaxNotification ){
			return false;
		}
		var rules = $(".ncmd_model_rights input[type=checkbox]:checked");
		var rules_ids = [];
		if( rules.length == 0 ){
			return false;
		}

		rules.each(function(k,el){
			var val = parseInt(el.value);
			if( Number.isInteger(val) ){
				rules_ids.push(el.value);
			}
		});

		if( rules_ids.length == 0 ){
			return false;
		}
		var url = location.pathname+location.search;
		var data = {rights: rules_ids, sendNotif: true};
		$("#popup_ria_shadow").after("<div class=\"popup_ria_back_notice notice\">" + orderModelsEnvoiEnCours + "</div>").show();
		ajaxNotification = $.ajax({
			url: url,
			data: data,
			dataType: 'json',
			method: 'post',
			success: function(json){
				$("#popup_ria_shadow").hide();
				$(".popup_ria_back_notice").remove();
				ajaxNotification = false;
				var success = $('<div>').addClass('success').html(json.success);
				$(".notif").append(success);
			},
			error: function(error){
				ajaxNotification = false;
				var error = $('<div>').addClass('error').text(error);
				$(".notif").append(error);
			}
		})
		return false;
	});
});

$(document).on('focus', '[name=new_usr]', function(){
	window.location.href="/admin/ajax/orders/ncmd-customers-change.php?ncmd-rights=1&no-add=1";
});
$(document).on('click', '[name=choose_new_usr]', function(){
	$('[name=new_usr]').focus();
});