<?php
    // Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_FDV_PALMARES');


    define('ADMIN_PAGE_TITLE', _('Palmarès') . ' - Yuto');
    require_once('goals.inc.php');
    require_once('admin/skin/header.inc.php');

    $seller = isset($_SESSION['ord_seller_id']) && is_numeric($_SESSION['ord_seller_id']) && $_SESSION['ord_seller_id'] ? $_SESSION['ord_seller_id'] : 0;
?>

    <h2><?php print _('Palmarès'); ?></h2>

<?php

    $tenant = ria_mysql_fetch_array(tnt_tenants_get($config['tnt_id']));
    
    $date_start = isset($_SESSION['datepicker_date1']) ? $_SESSION['datepicker_date1'] : date('Y-m-d');
    $date_end = isset($_SESSION['datepicker_date2']) ? $_SESSION['datepicker_date2'] : date('Y-m-d');
   

?>
    <div class="selector-menu">
        <div id="riadatepicker"></div>
        <input type="hidden" id="date1" name="date1" value="<?php print $date_start; ?>">
        <input type="hidden" id="date2" name="date2" value="<?php print $date_end; ?>">
        
        <?php print view_sellers_selector( true, true ); ?>
        <div class="clear"></div>
    </div>

    <div id="notif">
        <?php 
        if (view_palmares( $date_start, $date_end, $seller ) == false ) {
            echo '<p>Aucune donnée à afficher pour la période sélectionnée</p>';
        } ?>
    
    </div>
        
    <div id="palmares">
        <?php  print view_palmares( $date_start, $date_end, $seller ); ?>
    </div>

<?php
    require_once('admin/skin/footer.inc.php');
?>


<script>
    <?php 
        view_date_initialized( 0, '/admin/fdv/prize/index.php', false, array('autorefresh'=>true, 'callback' => 'function(){ update_prize(); }' )); 
    ?>

    var update_prize = function(){
        $('#notif').html('<p>Aucune donnée à afficher pour la période sélectionnée</p>').removeClass('error');
        $.ajax({
            url : '/admin/fdv/prize/ajax-prize.php?date_start='+$('#date1').val()+'&date_end='+$('#date2').val()+'&seller='+($('#seller').length ? $('#seller').val() : 0 ),
            beforeSend : function(){
                $('.load-ajax-opacity').show();
            },
            success : function(html){
                if(html != '') {
                    $('#notif').html('').removeClass('error');
                }
                $('#palmares').html(html);
            }
        }).done(function(){
            $('.load-ajax-opacity').hide();
        }).fail(function(){
            $('.load-ajax-opacity').hide();
            $('#notif').html('Une erreur est survenue lors du chargement du palmarès').addClass('error');
        });
    }


  
</script>