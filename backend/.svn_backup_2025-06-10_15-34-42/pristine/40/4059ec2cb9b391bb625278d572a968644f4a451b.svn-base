language: php
php:
  - 5.5
  - 5.6
  - 7.0
  - 7.1
  - hhvm
  - nightly

matrix:
  allow_failures:
    - php:
      - hhvm
      - nightly

cache:
  directories:
    - $HOME/.composer/cache

script:
  - vendor/bin/phpunit --coverage-clover=coverage.clover -v
  - composer update --no-interaction --prefer-source
  - vendor/bin/phpunit -v

before_script:
 - composer install --no-interaction

after_script:
  - if [ $TRAVIS_PHP_VERSION = '5.6' ]; then wget https://scrutinizer-ci.com/ocular.phar; php ocular.phar code-coverage:upload --format=php-clover coverage.clover; fi

notifications:
  irc: "irc.freenode.org#phpdocumentor"
  email:
    - <EMAIL>
    - <EMAIL>
