<?php 
/**
 *	\defgroup need_sync Information de synchronisation  
 *	\ingroup orders 
 *  @{	 
 *
 *	\page api-orders-need_sync-upd Mise à jour 
 *
 *	cette fonction permet de marquer la commande pour qu'elle se synchronise avec l'erp
 *
 *		\code
 *			PUT /orders/need_sync/
 *		\endcode
 *	
 *	 @param $ord Obligatoire, Identifiant de la commande
 *	
 *	 @return true si la commande se synchronise avec l'erp 
*/
switch( $method ){
	case 'upd': 

		if( !isset($_REQUEST['ord']) || !is_numeric($_REQUEST['ord']) ){
			throw new Exception("Paramètre invalide.");
		}

		if( ord_orders_set_need_sync($_REQUEST['ord']) ){
			$result = true;
		}
	
		break;
}

///@}