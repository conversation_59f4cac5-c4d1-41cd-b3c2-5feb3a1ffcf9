<?php

require_once('db.inc.php');
require_once('define.inc.php');
require_once('obj_position.inc.php');


/** \defgroup model_medias Bibliothèque de médias
 *	\ingroup dam
 *
 *  Ce module comprend les fonctions nécessaires à la gestion d'une bibliothèque de médias stockées à l'extérieur (Youtube, Dailymotion, Wat.tv, Vimeo...).
 *
 * @{
*/

/** \defgroup model_hosts Hébergeurs de médias
 *	Ce module comprend les fonctions nécessaires à la gestion des hébergeurs de médias, il est principalement utilisé pour les hébergeurs de fichiers vidéo (Youtube, Dailymotion, Vimeo, ...)
 * @{
 */

/** Cette fonction permet de récupérer les hébergeurs de médias.
 *
 *	@param int $hst_id Facultatif, identifiant d'un hébergeur
 *
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant de l'hébergeur
 *				- name : nom de l'hébergeur
 *				- desc : description de l'hébergeur
 */
function doc_hosts_get( $hst_id=0 ){
	if( !is_numeric($hst_id) || $hst_id<0 ){
		return false;
	}

	$sql = '
		select hst_id as id, hst_name as name, hst_desc as "desc"
		from doc_hosts
		where 1
	';

	if( $hst_id>0 ){
		$sql .= ' and hst_id = '.$hst_id;
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier si un hébergeur existe
 *	@param int $hst_id Obligatoire, identifiant d'un hébergeur
 *
 *	@return bool True si l'hébergeur existante, False dans le cas contraire
 */
function doc_hosts_exists( $hst_id ){
	if( !is_numeric($hst_id) || $hst_id<=0 ){
		return false;
	}

	$rhost = doc_hosts_get( $hst_id );
	return ( $rhost && ria_mysql_num_rows($rhost) );
}

/** Cette fonction permet de récupérer des statistiques pour un hébergeur.
 *	@param int $hst_id Obligatoire, identifiant d'un hébergeur
 *
 *	@return array Un tableau contenant :
 *			- channels : nombre de chaine
 *			- playlists : nombre de playlists
 *			- medias : nombre de médias
 */
function doc_hosts_get_stats( $hst_id ){
	$stats = array(
		'channels' => 0, 'playlists' => 0, 'medias' => 0
	);

	if( !is_numeric($hst_id) || $hst_id<=0 ){
		return $stats;
	}

	global $config;

	$sql = '
		select sum(chl) as channels, sum(pls) as playlists, sum(med) as medias
		from (
			select count(*) as chl, 0 as pls, 0 as med
			from doc_channels
			where chl_tnt_id = '.$config['tnt_id'].'
				and chl_is_deleted = 0
				and chl_hst_id = '.$hst_id.'

			union

			select 0 as chl, count(*) as pls, 0 as med
			from doc_playlists
			where pls_tnt_id = '.$config['tnt_id'].'
				and pls_is_deleted = 0
				and pls_hst_id = '.$hst_id.'

			union

			select 0 as chl, 0 as pls, count(*) as med
			from doc_medias
			where med_tnt_id = '.$config['tnt_id'].'
				and med_is_deleted = 0
				and med_hst_id = '.$hst_id.'
		) as stats
	';

	$rstats = ria_mysql_query( $sql );
	if( !$rstats || !ria_mysql_num_rows($rstats) ){
		return $stats;
	}

	return ria_mysql_fetch_assoc( $rstats );
}

/** Cette fonction permet de récupérer les paramètres rattachés aux hébergeurs
 *
 *	@param int $hst_id Obligatoire, identifiant d'un hébergeur
 *	@param int $wst_id Facultatif, identifiant du site web client
 *	@param string $key Facultatif, nom du paramètre
 *
 *	@return mixed False si le paramètre obligatoire est omis ou faux, sinon un tableau param => value.
 */
function doc_hosts_params_get( $hst_id, $wst_id=0, $key='' ){
	if( !is_numeric($hst_id) || $hst_id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select hsp_key, hsp_value
		from doc_hosts_params
		where hsp_tnt_id = '.$config['tnt_id'].'
			and hsp_hst_id = '.$hst_id.'
	';

	if( $wst_id>0 ){
		$sql .= ' and hsp_wst_id = '.$wst_id;
	}

	if( trim($key)!='' ){
		$sql .= ' and hsp_key = "'.addslashes( $key ).'"';
	}

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	$ar_params = array();
	while( $r = ria_mysql_fetch_assoc($res) ){
		$ar_params[ $r['hsp_key'] ] = $r['hsp_value'];
	}

	return $ar_params;
}

/** Cette fonction permet de vérifier qu'un paramètre existe bien.
 *	@param int $hst_id Obligatoire, identifiant d'un hébergeur
 *	@param $key Obligatoire, clé du paramètre
 *	@param int $wst_id Optionnel, identifiant d'un site internet
 *
 *	@return bool True si le paramètre existe, False dans le cas contraire
 */
function doc_hosts_params_exists( $hst_id, $key, $wst_id=0 ){
	if( !is_numeric($hst_id) || $hst_id<=0 ){
		return false;
	}

	if( trim($key)=='' ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	if( !$wst_id ){
		$wst_id = $config['wst_id'];
	}

	$sql = '
		select 1
		from doc_hosts_params
		where hsp_tnt_id = '.$config['tnt_id'].'
			and (hsp_wst_id = 0 or hsp_wst_id = '.$wst_id.')
			and hsp_key = "'.addslashes( $key ).'"
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de mettre à jour la valeur d'un paramètre
 *	@param int $hst_id Obligatoire, identifiant d'un hébergeur
 *	@param string $key Obligatoire, clé du paramètre
 *	@param string $value Obligatoire, nouvelle valeur pour le paramètre
 *	@param int $wst_id Optionnel, identifiant d'un site
 *
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function doc_hosts_params_update( $hst_id, $key, $value, $wst_id=0 ){
	if( !doc_hosts_params_exists($hst_id, $key, $wst_id) ){
		return false;
	}

	if( trim($value)=='' ){
		return false;
	}

	global $config;

	$sql = '
		update doc_hosts_params
		set hsp_value = "'.addslashes( $value ).'"
		where hsp_tnt_id = '.$config['tnt_id'].'
			and hsp_wst_id = '.$wst_id.'
			and hsp_key = "'.addslashes( $key ).'"
	';

	return ria_mysql_query( $sql );
}

/// @}

/** \defgroup model_channels Chaînes Youtube
 *	Ce module comprend les fonctions nécessaires à la gestion des chaînes au sein des hébergeurs de média
 * @{
 */

/** Cette fonction permet d'ajouter une chaine
 *
 *	@param int $hst_id Obligatoire, identifiant d'un hébergeur
 *	@param string $name Obligatoire, nom de la chaîne
 *	@param string $desc Facultatif, description de la chaîne
 *	@param string $url Facultatif, url de la chaîne
 *	@param int $import_id Facultatif, identifiant d'import de la chaîne
 *	@param bool $publish Facultatif, publication ou non de la chaîne sur les sites
 *
 *	@return int L'identifiant de chaîne en cas de succès, False dans le cas contraire
 */
function doc_channels_add( $hst_id, $name, $desc='', $url='', $import_id='', $publish=false ){
	if( !doc_hosts_exists($hst_id) ){
		return false;
	}

	if( trim($name)=='' ){
		return false;
	}

	global $config;

	$sql = '
		insert into doc_channels
			( chl_tnt_id, chl_name, chl_desc, chl_hst_id, chl_date_created, chl_date_published, chl_url, chl_import_id )
		values
			( '.$config['tnt_id'].', "'.addslashes( $name ).'", "'.addslashes( $desc ).'", '.$hst_id.', now(), '.( $publish ? 'now()' : 'null' ).',"'.addslashes( $url ).'", "'.addslashes( $import_id ).'" )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	$chl_id = ria_mysql_insert_id();
	if( is_numeric($chl_id) && $chl_id>0 ){
		doc_channels_url_alias_add( $chl_id );
	}

	return $chl_id;
}

/** Cette fonction permet de récupérer les chaines.
 *	@param int $hst_id Optionnel, identifiant d'un hébergeur
 *	@param int $chl_id optionnel, identifiant d'une chaine
 *	@param int $wst_id Optionnel, identifiant d'un site
 *	@param bool $publish Facultatif, si vrai, seules les chaînes publiées seront retournées. La valeur par défaut est false.
 *	@param int $import_id Optionnel, identifiant d'import de la chaine
 *
 *	@return resource Un résultat MySQL contant :
 *				- id : identifiant de la chaine
 *				- name : nom de la chaine
 *				- desc : description de la chaine
 *				- import_id : identifiant de la chaine chez l'hébergeur
 *				- url_alias : url de la chaine dans RiaShop
 *				- url : url de la chaine sur internet
 *				- date_publish : date de publication
 *				- hst_id : identifiant de l'hébergeur
 */
function doc_channels_get( $hst_id=0, $chl_id=0, $wst_id=0, $publish=false, $import_id='' ){
	if( !is_numeric($hst_id) || $hst_id<0 ){
		return false;
	}

	if( !is_numeric($chl_id) || $chl_id<0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select chl_id as id, chl_name as name, chl_desc as "desc", chl_import_id as import_id, chl_url_alias as url_alias, chl_url as url, chl_date_published as date_publish, chl_hst_id as hst_id
		from doc_channels
		where chl_tnt_id = '.$config['tnt_id'].'
			and chl_is_deleted = 0
	';

	if( $hst_id>0 ){
		$sql .= ' and chl_hst_id = '.$hst_id;
	}

	if( $chl_id>0 ){
		$sql .= ' and chl_id = '.$chl_id;
	}

	if( $wst_id>0 ){
		$sql .= '
			and exists (
				select 1
				from doc_channels_websites
				where chw_tnt_id = '.$config['tnt_id'].'
					and chw_chl_id = chl_id
					and chw_wst_id = '.$wst_id.'
			)
		';
	}

	if( $publish ){
		$sql .= ' and ifnull(chl_date_published, "")!=""';
	}

	if( trim($import_id)!='' ){
		$sql .= ' and chl_import_id = "'.addslashes( $import_id ).'"';
	}

	$sql .= '
		order by chl_name asc
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier qu'une chaine existe bien
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *	@param int $hst_id Optionnel, identifiant d'un hébergeur
 *
 *	@return bool True si la chaine existe, False dans le cas contraire
 */
function doc_channels_exists( $chl_id, $hst_id=0 ){
	if( !is_numeric($chl_id) || $chl_id<=0 ){
		return false;
	}

	if( !is_numeric($hst_id) || $hst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from doc_channels
		where chl_tnt_id = '.$config['tnt_id'].'
			and chl_id = '.$chl_id.'
			and chl_is_deleted = 0
	';

	if( $hst_id>0 ){
		$sql .= ' and chl_hst_id = '.$hst_id;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de mettre à jour les informations d'une chaine
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *	@param string $name Obligatoire, nom de la playlist
 *	@param string $desc Optionnel, description de la playlist
 *	@param int $import_id Optionnel, identifiant permetttant l'import de la playlist, laissé null pour ne pas mettre à jour
 *	@param string $url Optionnel, url de la playlist sur internet, laissé null pour ne pas mettre à jour
 *	@param bool $publish Optionnel, publication ou non de la playlist
 *
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function doc_channels_update( $chl_id, $name, $desc='', $import_id=null, $url=null, $publish=false ){
	if( !is_numeric($chl_id) || $chl_id<=0 ){
		return false;
	}

	if( trim($name)=='' ){
		return false;
	}

	global $config;

	$sql = '
		update doc_channels
		set chl_name = "'.addslashes( $name ).'",
			chl_desc = "'.addslashes( $desc ).'",
			'.( $import_id!==null ? 'chl_import_id = "'.addslashes( $import_id ).'",' :'' ).'
			'.( $url!==null ? 'chl_url = "'.addslashes( $url ).'",' : '' ).'
			chl_date_published = '.( $publish ? 'now()' : 'null' ).'
		where chl_tnt_id = '.$config['tnt_id'].'
			and chl_id = '.$chl_id.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de publier une chaine.
 *	@param int|array $chl_id Obligatoire, identifiant ou tableau d'identifiants de chaines
 *
 *	@return bool True si la publication s'est correctement déroulée, False dans le cas contraire
 */
function doc_channels_publish( $chl_id ){
	$ar_chl_id = control_array_integer( $chl_id );
	if( !$ar_chl_id ){
		return false;
	}

	global $config;

	$sql = '
		update doc_channels
		set chl_date_published = now()
		where chl_tnt_id = '.$config['tnt_id'].'
			and chl_id in ( '.implode(', ', $ar_chl_id ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de dépublier une chaine.
 *	@param int|array $chl_id Obligatoire, identifiant ou tableau d'identifiants de chaines
 *
 *	@return bool True si la dépublication s'est correctement déroulée, False dans le cas contraire
 */
function doc_channels_unpublish( $chl_id ){
	$ar_chl_id = control_array_integer( $chl_id );
	if( !$ar_chl_id ){
		return false;
	}

	global $config;

	$sql = '
		update doc_channels
		set chl_date_published = null
		where chl_tnt_id = '.$config['tnt_id'].'
			and chl_id in ( '.implode(', ', $ar_chl_id ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de crééer l'url RiaShop d'une chaine.
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *
 *	@return bool True si la création a correctement fonctionnée, False dans la cas contraire
 */
function doc_channels_url_alias_add( $chl_id ){
	global $config;

	if( !doc_channels_exists($chl_id) ){
		return false;
	}

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) ){
		return false;
	}

	$url = false;

	// Crée les alias
	$alias = rew_rewritemap_generated( array($chl_id), CLS_CHANNEL );

	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_CHANNEL);
		if( $prd_pages  ){
			$added = false;
			while( $page = ria_mysql_fetch_array($prd_pages) ){
				if( !$added ){
					$url = rew_rewritemap_add_specify_class( CLS_CHANNEL, $alias.$page['key'].'/', $page['url'].( strstr($page['url'], '?') ? '&' : '?' ).'chl='.$id, 200, $wst['id'], false, null, $id );
				}else{
					rew_rewritemap_add_specify_class( CLS_CHANNEL, $alias.$page['key'].'/', $page['url'].( strstr($page['url'], '?') ? '&' : '?' ).'chl='.$id, 200, $wst['id'], false, null, $id );
				}
				$added = true;
			}
		}
	}

	$res = false;
	if( $url!==false ){
		$res = ria_mysql_query('
			update doc_channels
			set chl_url_alias="'.addslashes( $url ).'"
			where chl_tnt_id = '.$config['tnt_id'].'
				and chl_id = '.$chl_id.'
		');
	}

	return $res;
}

/** Cette fonction permet de supprimer une chaine
 *	@param int|array $chl_id Obligatoire, identifiant ou tableau d'identifiants de chaines
 *
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire ou si le parmètre obligatoire est omis ou faux
 */
function doc_channels_del( $chl_id ){
	$ar_chl_id = control_array_integer( $chl_id );
	if( !$ar_chl_id ){
		return false;
	}

	global $config;

	$sql = '
		update doc_channels
		set chl_date_deleted = now(), chl_is_deleted = 1
		where chl_tnt_id = '.$config['tnt_id'].'
			and chl_id in ('.implode( ', ', $ar_chl_id ).')
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de rattacher une playlist à une chaine
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function doc_channels_playlists_add( $chl_id, $pls_id ){
	if( !doc_channels_exists($chl_id) ){
		return false;
	}

	if( !doc_playlists_exists($pls_id) ){
		return false;
	}

	if( doc_channels_playlists_exists($chl_id, $pls_id) ){
		return true;
	}

	global $config;

	$res = ria_mysql_query('
		insert into doc_channels_playlists
			( chp_tnt_id, chp_chl_id, chp_pls_id )
		values
			( '.$config['tnt_id'].', '.$chl_id.', '.$pls_id.' )
	');

	if( !$res ){
		return false;
	}

	$rmax_pos = ria_mysql_query( 'select ifnull(max(chp_pos), 0) as max_pos from doc_channels_playlists where chp_tnt_id='.$config['tnt_id'].' and chp_chl_id='.$chl_id );
	if( $rmax_pos && ria_mysql_num_rows($rmax_pos) ){
		$max_pos = ria_mysql_result( $rmax_pos, 0, 'max_pos' );

		$res = ria_mysql_query('
			update doc_channels_playlists
			set chp_pos = '.$max_pos.'
			where chp_tnt_id = '.$config['tnt_id'].'
				and chp_chl_id = '.$chl_id.'
				and chp_pls_id = '.$pls_id.'
		');
	}

	return $res;
}

/** Cette fonction permet de récupérer les liens entre les chaines et les playlists
 *	@param int|array $chl_id Optionnel, identifiant ou tableau d'identifiants de chaines
 *	@param int|array $pls_id Optionnel, identifiant ou tableau d'identifiants de playlists
 *
 *	@return resource Un résultat MySQL contenant :
 *				- chl_id : identifiant de la chaine
 *				- chl_name : nom de la chaine
 *				- pls_id : identifiant de la playlist
 *				- pls_name : nom de la playlist
 */
function doc_channels_playlists_get( $chl_id=0, $pls_id=0 ){
	$ar_chl_id = control_array_integer( $chl_id, false );
	if( !$ar_chl_id ){
		return false;
	}

	$ar_pls_id = control_array_integer( $pls_id, false );
	if( !$ar_pls_id ){
		return false;
	}

	global $config;

	$sql = '
		select chl_id, chl_name, pls_id, pls_name
		from doc_channels_playlists
			join doc_channels on (chp_tnt_id = chl_tnt_id and chp_chl_id = chl_id)
			join doc_playlists on (chp_tnt_id = pls_tnt_id and chp_pls_id = pls_id )
		where chp_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($ar_chl_id) ){
		$sql .= ' and chp_chl_id in ( '.implode( ', ', $ar_chl_id ).' )';
	}

	if( sizeof($ar_pls_id) ){
		$sql .= ' and chp_pls_id in ( '.implode( ', ', $ar_pls_id ).' )';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier qu'un lien entre une chaine et une playlist existe.
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *
 *	@return bool True si le lien existe, False dans le cas contraire
 */
function doc_channels_playlists_exists( $chl_id, $pls_id ){
	if( !is_numeric($chl_id) || $chl_id<=0 ){
		return false;
	}

	if( !is_numeric($pls_id) || $pls_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from doc_channels_playlists
		where chp_tnt_id = '.$config['tnt_id'].'
			and chp_chl_id = '.$chl_id.'
			and chp_pls_id = '.$pls_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de supprimer un lien entre une chaine et ses playlists
 *	Un des deux paramètres doit être fourni.
 *	@param int $chl_id Optionnel, identifiant d'une chaine
 *	@param int|array $pls_id Optionnel, identifiant ou tableau d'identifiants de playlists
 *
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function doc_channels_playlists_del( $chl_id=0, $pls_id=0 ){
	if( !is_numeric($chl_id) || $chl_id<0 ){
		return false;
	}

	$ar_pls_id = control_array_integer( $pls_id, false );
	if( !$ar_pls_id ){
		return false;
	}

	if( !$chl_id && !sizeof($pls_id) ){
		return false;
	}

	global $config;

	$sql = '
		delete from doc_channels_playlists
		where chp_tnt_id = '.$config['tnt_id'].'
	';

	if( $chl_id>0 ){
		$sql .= ' and chp_chl_id = '.$chl_id;
	}

	if( sizeof($ar_pls_id) ){
		$sql .= ' and chp_pls_id in ( '.implode( ', ', $ar_pls_id ).' )';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de rattacher un media à une chaine
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *	@param int $med_id Obligatoire, identifiant d'un media
 *
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function doc_channels_medias_add( $chl_id, $med_id ){
	if( !doc_channels_exists($chl_id) ){
		return false;
	}

	if( !doc_medias_exists($med_id) ){
		return false;
	}

	if( doc_channels_medias_exists($chl_id, $med_id) ){
		return true;
	}

	global $config;

	$res = ria_mysql_query('
		insert into doc_channels_medias
			( chm_tnt_id, chm_chl_id, chm_med_id )
		values
			( '.$config['tnt_id'].', '.$chl_id.', '.$med_id.' )
	');

	if( !$res ){
		return false;
	}

	$rmax_pos = ria_mysql_query( 'select ifnull(max(chm_pos), 0) as max_pos from doc_channels_medias where chm_tnt_id='.$config['tnt_id'].' and chm_chl_id='.$chl_id );
	if( $rmax_pos && ria_mysql_num_rows($rmax_pos) ){
		$max_pos = ria_mysql_result( $rmax_pos, 0, 'max_pos' );

		$res = ria_mysql_query('
			update doc_channels_medias
			set chm_pos = '.$max_pos.'
			where chm_tnt_id = '.$config['tnt_id'].'
				and chm_chl_id = '.$chl_id.'
				and chm_med_id = '.$med_id.'
		');
	}

	return $res;
}

/** Cette fonction permet de récupérer les liens entre les chaines et les médias
 *	@param int|array $chl_id Optionnel, identifiant ou tableau d'identifiants de chaines
 *	@param int|array $med_id Optionnel, identifiant ou tableau d'identifiants de médias
 *
 *	@return resource Un résultat MySQL contenant :
 *				- chl_id : identifiant de la chaine
 *				- chl_name : nom de la chaine
 *				- med_id : identifiant de la playlist
 *				- med_name : nom de la playlist
 */
function doc_channels_medias_get( $chl_id=0, $med_id=0 ){
	if( $chl_id!==0 ){
		$chl_id = control_array_integer( $chl_id, false );
		if( !$chl_id ){
			return false;
		}
	}

	if( $med_id!==0 ){
		$med_id = control_array_integer( $med_id, false );
		if( !$med_id ){
			return false;
		}
	}

	global $config;

	$sql = '
		select chl_id, chl_name, med_id, med_name
		from doc_channels_medias
			join doc_channels on (chm_tnt_id = chl_tnt_id and chm_chl_id = chl_id)
			join doc_medias on (chm_tnt_id = med_tnt_id and chm_med_id = med_id )
		where chm_tnt_id = '.$config['tnt_id'].'
	';

	if( is_array($chl_id) && sizeof($chl_id) ){
		$sql .= ' and chm_chl_id in ( '.implode( ', ', $chl_id ).' )';
	}

	if( is_array($med_id) && sizeof($med_id) ){
		$sql .= ' and chm_med_id in ( '.implode( ', ', $med_id ).' )';
	}

	$sql .= '
		order by chl_name asc
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier qu'un lien entre une chaine et un média existe.
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *	@param int $med_id Obligatoire, identifiant d'un média
 *
 *	@return bool True si le lien existe, False dans le cas contraire
 */
function doc_channels_medias_exists( $chl_id, $med_id ){
	if( !is_numeric($chl_id) || $chl_id<=0 ){
		return false;
	}

	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from doc_channels_medias
		where chm_tnt_id = '.$config['tnt_id'].'
			and chm_chl_id = '.$chl_id.'
			and chm_med_id = '.$med_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de supprimer un lien entre une chaine et ses médias
 *	Un des deux paramètres doit être fourni.
 *	@param int $chl_id Optionnel, identifiant d'une chaine
 *	@param int|array $med_id Optionnel, identifiant ou tableau d'identifiants de playlists
 *
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function doc_channels_medias_del( $chl_id=0, $med_id=0 ){
	if( !is_numeric($chl_id) || $chl_id<0 ){
		return false;
	}

	if( $med_id!==0 ){
		$med_id = control_array_integer( $med_id, false );
		if( !$med_id ){
			return false;
		}
	}

	if( !$chl_id && (!is_array($med_id) || !sizeof($med_id)) ){
		return false;
	}

	global $config;

	$sql = '
		delete from doc_channels_medias
		where chm_tnt_id = '.$config['tnt_id'].'
	';

	if( $chl_id>0 ){
		$sql .= ' and chm_chl_id = '.$chl_id;
	}

	if( is_array($med_id) && sizeof($med_id) ){
		$sql .= ' and chm_med_id in ( '.implode( ', ', $med_id ).' )';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'activer une chaine sur un site web.
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *	@param int $wst_id Obligatoire, identifiant d'un site
 *
 *	@return bool True si le lien a correctement été créé, False dans le cas contaire
 */
function doc_channels_websites_add( $chl_id, $wst_id ){
	if( !doc_channels_exists($chl_id) ){
		return false;
	}

	if( !wst_websites_exists($wst_id) ){
		return false;
	}

	if( doc_channels_websites_exists($chl_id, $wst_id) ){
		return true;
	}

	global $config;

	return ria_mysql_query('
		insert into doc_channels_websites
			( chw_tnt_id, chw_chl_id, chw_wst_id )
		values
			( '.$config['tnt_id'].', '.$chl_id.', '.$wst_id.' )
	');
}

/** Cette fonction permet de vérifier si une chaine est présente sur un site.
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *	@param int $wst_id Optionnel, identifiant d'un site web
 *
 *	@return bool True si c'est bien le cas, False dans le cas contraire
 */
function doc_channels_websites_exists( $chl_id, $wst_id=0 ){
	if( !is_numeric($chl_id) || $chl_id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from doc_channels_websites
		where chw_tnt_id = '.$config['tnt_id'].'
			and chw_chl_id = '.$chl_id.'
	';

	if( $wst_id>0 ){
		$sql .= ' and chw_wst_id = '.$wst_id;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de retirer une chaine sur un ou plusieurs sites.
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *	@param int $wst_id Optionnel, identifiant ou tableau d'identifiants de sites web
 *
 *	@return bool True si c'est bien le cas, False dans le cas contraire
 */
function doc_channels_websites_del( $chl_id, $wst_id=0 ){
	if( !is_numeric($chl_id) || $chl_id<=0 ){
		return false;
	}

	$ar_wst_id = control_array_integer( $wst_id, false );
	if( !$ar_wst_id ){
		return false;
	}

	global $config;

	$sql = '
		delete from doc_channels_websites
		where chw_tnt_id = '.$config['tnt_id'].'
			and chw_chl_id = '.$chl_id.'
	';

	if( sizeof($ar_wst_id) ){
		$sql .= ' and chw_wst_id in ( '.implode( ', ', $ar_wst_id ).' )';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer les sites sur lesquels se trouve une chaine
 *	@param int $chl_id Obligatoire, identifiant d'une chaine
 *	@param int $wst_id Optionnel, identifiant d'un site web
 *
 *	@return resource Un résultat MySQL contenant :
 *			- chl_id : identifiant d'une chaine
 *			- chl_name : nom de la chaine
 *			- wst_id : identifiant du site
 *			- wst_name : nom du site
 */
function doc_channels_websites_get( $chl_id, $wst_id=0 ){
	if( !is_numeric($chl_id) || $chl_id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select chl_id, chl_name, wst_id, wst_name
		from doc_channels_websites
			join wst_websites on (chw_tnt_id = wst_tnt_id and chw_wst_id = wst_id)
		where chw_tnt_id = '.$config['tnt_id'].'
			and chw_chl_id = '.$chl_id.'
	';

	if( $wst_id>0 ){
		$sql .= ' and chw_wst_id = '.$wst_id;
	}

	return ria_mysql_query( $sql );
}

/// @}

/** \defgroup model_playlists Playlists Youtube
 *	Ce module comprend les fonctions nécessaires à la gestion des playlists de médias
 * @{
 */

/** Cette fonction permet d'ajouter une playlist.
 *	@param int $hst_id Obligatoire, identifiant d'un hébergeur
 *	@param string $name Obligatoire, nom de la playlist
 *	@param string $desc Facultatif, description de la playlist
 *	@param int $import_id Facultatif, identifiant permetttant l'import de la playlist
 *	@param string $url Facultatif, url de la playlist sur internet
 *	@param bool $publish Facultatif, publication ou non de la playlist
 *
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function doc_playlists_add( $hst_id, $name, $desc='', $import_id='', $url='', $publish=false ){
	if( !doc_hosts_exists($hst_id) ){
		return false;
	}

	if( trim($name)=='' ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		insert into doc_playlists
			( pls_tnt_id, pls_name, pls_desc, pls_import_id, pls_hst_id, pls_date_created, pls_url, pls_date_published )
		values
			( '.$config['tnt_id'].', "'.addslashes( $name ).'", "'.addslashes( $desc ).'", "'.$import_id.'", '.$hst_id.', now(), "'.addslashes( $url ).'", '.( $publish ? 'now()' : 'null' ).' )
	');

	if( !$res ){
		return false;
	}

	$pls_id = ria_mysql_insert_id();
	if( is_numeric($pls_id) && $pls_id>0 ){
		doc_playlists_url_alias_add( $pls_id );
	}

	return $pls_id;
}

/** Cette fonction permet de récupérer une ou plusieurs playlists.
 *	@param int $hst_id Optionnel, identifiant d'un hébergeur
 *	@param int $chl_id Optionnel, identifiant d'une chaine
 *	@param int $pls_id Optionnel, identifiant d'une playlist
 *	@param bool $active Optionnel, par défaut seules les playlists publiées sont retournées, mettre False pour toutes celles non publiées, null pour toutes les avoir
 *	@param bool $have_medias Optionnel, par défaut seules les playlists avec médias sont retournées, mettre False pour toutes celles sans, null pour toutes les avoir
 *	@param int $import_id Optionnel, identifiant d'import de la playlist
 *
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant de la playlist
 *				- name : nom de la playlist
 *				- desc : description de la playlist
 *				- import_id : identifiant d'import
 *				- url_alias : url générée pour la playlist
 *				- url : url de la playlist sur internet
 *				- date_created : date de création
 *				- date_publish : date de publication
 */
function doc_playlists_get( $hst_id=0, $chl_id=0, $pls_id=0, $active=null, $have_medias=null, $import_id='' ){
	if( !is_numeric($hst_id) || $hst_id<0 ){
		return false;
	}

	if( !is_numeric($chl_id) || $chl_id<0 ){
		return false;
	}

	if( !is_numeric($pls_id) || $pls_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select
			pls_id as id, pls_name as name, pls_desc as "desc", pls_import_id as import_id, pls_url_alias as url_alias,  pls_url as url,
			pls_date_created as date_created, pls_date_published as date_publish
		from doc_playlists
		where pls_tnt_id='.$config['tnt_id'].'
			and pls_is_deleted = 0
	';

	if( $hst_id>0 ){
		$sql .= ' and pls_hst_id = '.$hst_id;
	}

	if( $chl_id>0 ){
		$sql .= '
			and exists(
				select 1
				from doc_channels_playlists
					where chp_tnt_id = '.$config['tnt_id'].'
						and chp_chl_id = '.$chl_id.'
						and chp_pls_id = pls_id
			)
		';
	}

	if( $pls_id>0 ){
		$sql .= ' and pls_id = '.$pls_id;
	}

	if( $active!==null ){
		if( $active ){
			$sql .= ' and ifnull(pls_date_published, "")!=""';
		}else{
			$sql .= ' and ifnull(pls_date_published, "")=""';
		}
	}

	if( $have_medias!==null ){
		$sql .= '
			and pls_medias'.( $have_medias ? '>' : '=' ).' 0
		';
	}

	if( trim($import_id)!='' ){
		$sql .= ' and pls_import_id = "'.addslashes( $import_id ).'"';
	}

	$sql .= '
		order by pls_name asc
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de vérifier qu'une playlist existe.
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *	@return bool True si la playlist existe, False dans le cas contraire
 */
function doc_playlists_exists( $pls_id ){
	if( !is_numeric($pls_id) || $pls_id<=0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from doc_playlists
		where pls_tnt_id='.$config['tnt_id'].'
			and pls_id = '.$pls_id.'
			and pls_is_deleted = 0
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de mettre à jour une playlist
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *	@param string $name Obligatoire, nom de la playlist
 *	@param string $desc Optionnel, description de la playlist
 *	@param int $import_id Optionnel, identifiant permetttant l'import de la playlist, laissé null pour ne pas mettre à jour
 *	@param string $url Optionnel, url de la playlist sur internet, laissé null pour ne pas mettre à jour
 *	@param bool $publish Optionnel, publication ou non de la playlist
 *
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function doc_playlists_update( $pls_id, $name, $desc='', $import_id=null, $url=null, $publish=false ){
	if( !is_numeric($pls_id) || $pls_id<=0 ){
		return false;
	}

	if( trim($name)=='' ){
		return false;
	}

	global $config;

	$sql = '
		update doc_playlists
		set pls_name = "'.addslashes( $name ).'",
			pls_desc = "'.addslashes( $desc ).'",
			'.( $import_id!==null ? 'pls_import_id = "'.addslashes( $import_id ).'",' : '' ).'
			'.( $url!==null ? 'pls_url = "'.addslashes( $url ).'",' : '' ).'
			pls_date_published = '.( $publish ? 'now()' : 'null' ).'
		where pls_tnt_id = '.$config['tnt_id'].'
			and pls_id = '.$pls_id.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	// Mise à jour du nombre de médias contenus dans cette playlist
	return doc_playlists_update_count_medias( $pls_id );
}

/** Cette fonction permet de mettre à jour le nombre de médias contenu dans une playlist
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function doc_playlists_update_count_medias( $pls_id ){
	if( !is_numeric($pls_id) || $pls_id<=0 ){
		return false;
	}

	global $config;

	$sql ='
		select count(*) as nb_medias
		from doc_playlists_medias
			join doc_medias on (plm_tnt_id = med_tnt_id and plm_med_id = med_id)
		where plm_tnt_id='.$config['tnt_id'].'
			and plm_pls_id = '.$pls_id.'
			and med_is_deleted = 0
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );

	return ria_mysql_query('
		update doc_playlists
		set pls_medias = '.$r['nb_medias'].'
		where pls_tnt_id = '.$config['tnt_id'].'
			and pls_id = '.$pls_id.'
	');
}

/** Cette fonction permet de publier une playlist.
 *	@param int|array $pls_id Obligatoire, identifiant ou tableau d'identifiants de playlists
 *
 *	@return bool True si la publication s'est correctement déroulée, False dans le cas contraire
 */
function doc_playlists_publish( $pls_id ){
	$ar_pls_id = control_array_integer( $pls_id );
	if( !$ar_pls_id ){
		return false;
	}

	global $config;

	$sql = '
		update doc_playlists
		set pls_date_published = now()
		where pls_tnt_id = '.$config['tnt_id'].'
			and pls_id in ( '.implode(', ', $ar_pls_id ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de dépublier une playlist.
 *	@param int|array $pls_id Obligatoire, identifiant ou tableau d'identifiants de playlists
 *
 *	@return bool True si la dépublication s'est correctement déroulée, False dans le cas contraire
 */
function doc_playlists_unpublish( $pls_id ){
	$ar_pls_id = control_array_integer( $pls_id );
	if( !$ar_pls_id ){
		return false;
	}

	global $config;

	$sql = '
		update doc_playlists
		set pls_date_published = null
		where pls_tnt_id = '.$config['tnt_id'].'
			and pls_id in ( '.implode(', ', $ar_pls_id ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de crééer l'url RiaShop d'une playlist.
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *
 *	@return bool True si la création a correctement fonctionnée, False dans la cas contraire
 */
function doc_playlists_url_alias_add( $pls_id ){
	if( !doc_playlists_exists($pls_id) ){
		return false;
	}
	global $config;

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) )
		return false;

	$url = false;

	// Crée les alias
	$alias = rew_rewritemap_generated( array($pls_id), CLS_PLAYLIST );

	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_PLAYLIST);
		if( $prd_pages  ){
			$added = false;
			while( $page = ria_mysql_fetch_array($prd_pages) ){
				if( !$added ){
					rew_rewritemap_add_specify_class( CLS_PLAYLIST, $alias.$page['key'], $alias.$page['key'].'/', 301, $wst['id'], false, null, $pls_id );
					$url = rew_rewritemap_add_specify_class( CLS_PLAYLIST, $alias.$page['key'].'/', $page['url'].( strstr($page['url'], '?') ? '&' : '?' ).'pls='.$pls_id, 200, $wst['id'], false, null, $pls_id );
				}else{
					rew_rewritemap_add_specify_class( CLS_PLAYLIST, $alias.$page['key'], $alias.$page['key'].'/', 301, $wst['id'], false, null, $pls_id );
					rew_rewritemap_add_specify_class( CLS_PLAYLIST, $alias.$page['key'].'/', $page['url'].( strstr($page['url'], '?') ? '&' : '?' ).'pls='.$pls_id, 200, $wst['id'], false, null, $pls_id );
				}
				$added = true;
			}
		}
	}

	$res = false;
	if( $url!==false ){
		$res = ria_mysql_query('
			update doc_playlists
			set pls_url_alias="'.addslashes( $url ).'"
			where pls_tnt_id = '.$config['tnt_id'].'
				and pls_id = '.$pls_id.'
		');
	}

	return $res;
}

/** Cette fonction permet de supprimer une playlist
 *	@param int|array $pls_id Optionnel, identifiant ou tableau d'identifiants d'une playlist
 *	@param bool $del_medias Optionnel, par défaut les médias présentes dans cette playlist ne seront pas supprimées, mettre true pour que ce soit le cas
 *
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function doc_playlists_del( $pls_id, $del_medias=false ){
	$ar_pls_id = control_array_integer( $pls_id );
	if( !$ar_pls_id ){
		return false;
	}

	global $config;

	$sql = '
		update doc_playlists
		set pls_date_deleted = now(), pls_is_deleted = 1
		where pls_tnt_id = '.$config['tnt_id'].'
		and pls_id in ( '.implode( ', ', $ar_pls_id ).' )
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	if( $del_medias ){
		return doc_medias_del( 0, $pls_id );
	}

	return true;
}

/** Cette fonction permet de classer un média dans une playlists.
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *	@param int $med_id Obligatoire, identifiant d'un média
 *
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function doc_playlists_medias_add( $pls_id, $med_id ){
	if( !doc_playlists_exists($pls_id) ){
		return false;
	}

	if( !doc_medias_exists($med_id) ){
		return false;
	}

	if( doc_medias_exists($med_id, $pls_id) ){
		return true;
	}

	global $config;

	$sql = '
		insert into doc_playlists_medias
			( plm_tnt_id, plm_pls_id, plm_med_id )
		values
			( '.$config['tnt_id'].', '.$pls_id.', '.$med_id.')
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	$rmax_pos = ria_mysql_query( 'select max(plm_pos) as max_pos from doc_playlists_medias where plm_tnt_id='.$config['tnt_id'].' and plm_pls_id='.$pls_id );
	if( $rmax_pos && ria_mysql_num_rows($rmax_pos) ){
		$max_pos = ria_mysql_result( $rmax_pos, 0);

		$res = ria_mysql_query('
			update doc_playlists_medias
			set plm_pos = '.$max_pos.'
			where plm_tnt_id = '.$config['tnt_id'].'
				and plm_pls_id = '.$pls_id.'
				and plm_med_id = '.$med_id.'
		');
	}

	return $res;
}

/** Cette fonction permet de vérifier si un média est classée (dans une playlist, optionnel)
 *	@param int $med_id Obligatoire, identifiant d'un média
 *	@param int $pls_id Optionnel, identifiant d'une playlist
 *
 *	@return bool True si le classement exists, False dans le cas contaire
 */
function doc_playlists_medias_exists( $med_id, $pls_id=0 ){
	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}

	if( !is_numeric($pls_id) || $pls_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from doc_playlists_medias
		where plm_tnt_id = '.$config['tnt_id'].'
			and plm_med_id = '.$med_id.'
	';

	if( $pls_id>0 ){
		$sql .= ' and plm_pls_id = '.$pls_id;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer les liens entre un ou plusieurs et ses playlists.
 *	@param int $med_id Obligatoire, identifiant d'un média
 *	@param int $pls_id Optionnel, identifiant d'une playlist
 *	@return resource Un résultat MySQL contenant :
 *				- med_id : identifiant du média
 *				- med_name : nom du média
 *				- pls_id : identifiant de la playlist
 *				- pls_name : nom de la playlist
 */
function doc_playlists_medias_get( $med_id, $pls_id=0 ){
	$med_id = control_array_integer( $med_id );
	if( !$med_id ){
		return false;
	}

	if( !is_numeric($pls_id) || $pls_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select med_id, med_name, pls_id, pls_name
		from doc_playlists_medias
			join doc_medias on (med_tnt_id = plm_tnt_id and med_id = plm_med_id)
			join doc_playlists on (pls_tnt_id = pls_tnt_id and pls_id = plm_pls_id)
		where plm_tnt_id = '.$config['tnt_id'].'
			and plm_med_id in ( '.implode( ', ', $med_id ).' )
	';

	if( $pls_id>0 ){
		$sql .= ' and plm_pls_id = '.$pls_id;
	}

	$sql .= '
		order by pls_name asc
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer un tableau des identifiants médias classées dans une ou plusieurs playlists
 *	@param int|array $pls_id Optionnel, identifiant ou tableau d'identifiants de playlists
 *	@param int|array $med_id Optionnel, identifiant ou tableau d'identifiants de médias
 *
 *	@return array Un tableau contenant toutes les médias contenus dans cette(ces) playlist(s)
 */
function doc_playlists_medias_get_ids( $pls_id=0, $med_id=0 ){
	$ar_pls_id = control_array_integer( $pls_id, false );
	if( !$ar_pls_id ){
		return false;
	}

	$ar_med_id = control_array_integer( $med_id, false );
	if( !$ar_med_id ){
		return false;
	}

	global $config;

	$sql = '
		select plm_med_id as med_id
		from doc_playlists_medias
		where plm_tnt_id = '.$config['tnt_id'].'
	';

	if( sizeof($ar_pls_id) ){
		$sql .= ' and plm_pls_id in ( '.implode( ', ', $ar_pls_id ).' )';
	}

	if( sizeof($ar_med_id) ){
		$sql .= ' and plm_med_id in ( '.implode( ', ', $ar_med_id ).' )';
	}

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	$ar_med_id = array();
	while( $r = ria_mysql_fetch_assoc($res) ){
		$ar_med_id[] = $r['med_id'];
	}

	return $ar_med_id;
}

/**	Cette fonction permet la mise à jour de la position arbitraire d'un média au sein d'une playlist. Ces positions permettent d'effectuer un tri personnalisé.
 *	@param int $pls_id Obligatoire, identifiant de la playlist
 *	@param int $med_id Obligatoire, identifiant du média
 *	@param int $pos Obligatoire, position arbitraire de tri
 *	@return bool true en cas de succès, false en cas d'échec
 */
function doc_playlists_medias_set_pos( $pls_id, $med_id, $pos ){
	if( !is_numeric($pls_id) || $pls_id<=0 ){
		return false;
	}

	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}

	if( !is_numeric($pos) || $pos<0 ){
		return false;
	}

	global $config;

	$sql = '
		update doc_playlists_medias
		set plm_pos = '.$pos.'
		where plm_tnt_id = '.$config['tnt_id'].'
			and plm_pls_id = '.$pls_id.'
			and plm_med_id = '.$med_id.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de supprimer un lien entre une playlists et ses médias
 *	@param int $pls_id Obligatoire, identifiant d'une playlists
 *	@param int|array $med_id Optionnel, identifiant ou tableau d'identifiants de médias
 *
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function doc_playlists_medias_del( $pls_id=0, $med_id=0 ){
	if( !is_numeric($pls_id) || $pls_id<0 ){
		return false;
	}

	if( $med_id!==0 ){
		$med_id = control_array_integer( $med_id, false );
		if( !$med_id ){
			return false;
		}
	}

	if( !$pls_id && (!is_array($med_id) || !sizeof($med_id)) ){
		return false;
	}

	global $config;

	$sql = '
		delete from doc_playlists_medias
		where plm_tnt_id = '.$config['tnt_id'].'
	';

	if( $pls_id>0 ){
		$sql .= '
			and plm_pls_id = '.$pls_id.'
		';
	}

	if( is_array($med_id) && sizeof($med_id) ){
		$sql .= ' and plm_med_id in ( '.implode( ', ', $med_id ).' )';
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'activer une playlist sur un site web.
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *	@param int $wst_id Obligatoire, identifiant d'un site
 *
 *	@return bool True si le lien a correctement été créé, False dans le cas contaire
 */
function doc_playlists_websites_add( $pls_id, $wst_id ){
	if( !doc_playlists_exists($pls_id) ){
		return false;
	}

	if( !wst_websites_exists($wst_id) ){
		return false;
	}

	if( doc_playlists_websites_exists($pls_id, $wst_id) ){
		return true;
	}

	global $config;

	return ria_mysql_query('
		insert into doc_playlists_websites
			( plw_tnt_id, plw_pls_id, plw_wst_id )
		values
			( '.$config['tnt_id'].', '.$pls_id.', '.$wst_id.' )
	');
}

/** Cette fonction permet de vérifier si une playlist est présente sur un site.
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *	@param int $wst_id Optionnel, identifiant d'un site web
 *
 *	@return bool True si c'est bien le cas, False dans le cas contraire
 */
function doc_playlists_websites_exists( $pls_id, $wst_id=0 ){
	if( !is_numeric($pls_id) || $pls_id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from doc_playlists_websites
		where plw_tnt_id = '.$config['tnt_id'].'
			and plw_pls_id = '.$pls_id.'
	';

	if( $wst_id>0 ){
		$sql .= ' and plw_wst_id = '.$wst_id;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de retirer une playlist sur un ou plusieurs sites.
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *	@param int $wst_id Optionnel, identifiant ou tableau d'identifiants de sites web
 *
 *	@return bool True si c'est bien le cas, False dans le cas contraire
 */
function doc_playlists_websites_del( $pls_id, $wst_id=0 ){
	if( !is_numeric($pls_id) || $pls_id<=0 ){
		return false;
	}

	$ar_wst_id = control_array_integer( $wst_id, false );
	if( !$ar_wst_id ){
		return false;
	}

	global $config;

	$sql = '
		delete from doc_playlists_websites
		where plw_tnt_id = '.$config['tnt_id'].'
			and plw_pls_id = '.$pls_id.'
	';

	if( sizeof($ar_wst_id) ){
		$sql .= ' and plw_wst_id in ( '.implode( ', ', $ar_wst_id ).' )';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer les sites sur lesquels se trouve une playlist
 *	@param int $pls_id Obligatoire, identifiant d'une playlist
 *	@param int $wst_id Optionnel, identifiant d'un site web
 *
 *	@return resource Un résultat MySQL contenant :
 *			- pls_id : identifiant d'une playlist
 *			- pls_name : nom de la playlist
 *			- wst_id : identifiant du site
 *			- wst_name : nom du site
 */
function doc_playlists_websites_get( $pls_id, $wst_id=0 ){
	if( !is_numeric($pls_id) || $pls_id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select pls_id, pls_name, wst_id, wst_name
		from doc_playlists_websites
			join wst_websites on (plw_tnt_id = wst_tnt_id and plw_wst_id = wst_id)
		where plw_tnt_id = '.$config['tnt_id'].'
			and plw_pls_id = '.$pls_id.'
	';

	if( $wst_id>0 ){
		$sql .= ' and plw_wst_id = '.$wst_id;
	}

	return ria_mysql_query( $sql );
}

/// @}

/** Cette fonction permet d'ajouter un média à la médiathèque
 *
 *	@param int $hst_id Obligatoire, identifiant d'un hébergeur
 *	@param string $name Obligatoire, nom du média
 *	@param string $desc Facultatif, description du média
 *	@param int $import_id Facultatif, identifiant d'import du média
 *	@param string $url Facultatif, url du média sur internet
 *	@param $duration Facultatif, durée de la vidéo
 *	@param bool $publish Facultatif par défaut le média ne sera pas publié, mettre true pour que ce soit le cas, mettre une date au format FR pour spécifier la date de publication
 *
 *	@return bool True si l'ajout s'est correctement déroulée, False dans le cas contraire
 */
function doc_medias_add( $hst_id, $name, $desc='', $import_id='', $url='', $duration=0, $publish=false ){
	if( !doc_hosts_exists($hst_id) ){
		return false;
	}

	if( trim($name)=='' ){
		return false;
	}

	if( $publish === true ){
		$publish = 'now()';
	}elseif( isdateheure($publish) ){
		$publish = '"'.dateheureparse($publish).'"';
	}else{
		$publish = 'null';
	}

	if( !is_numeric($duration) || $duration<0 ){
		return false;
	}
	global $config;

	$ar_cols = array();
	$ar_vals = array();

	$ar_cols[] = 'med_tnt_id';
	$ar_vals[] = $config['tnt_id'];

	$ar_cols[] = 'med_name';
	$ar_vals[] = '"'.addslashes( $name ).'"';

	$ar_cols[] = 'med_desc';
	$ar_vals[] = '"'.addslashes( $desc ).'"';

	$ar_cols[] = 'med_import_id';
	$ar_vals[] = '"'.addslashes( $import_id ).'"';

	$ar_cols[] = 'med_url';
	$ar_vals[] = '"'.addslashes( $url ).'"';

	$ar_cols[] = 'med_date_created';
	$ar_vals[] = 'now()';

	if( $duration>0 ){
		$ar_cols[] = 'med_duration';
		$ar_vals[] = $duration;
	}

	$ar_cols[] = 'med_date_published';
	$ar_vals[] = $publish;

	$ar_cols[] = 'med_hst_id';
	$ar_vals[] = $hst_id;

	$res = ria_mysql_query('
		insert into doc_medias
			( '.implode( ', ', $ar_cols ).' )
		values
			( '.implode( ', ', $ar_vals ).' )
	');

	$med_id = ria_mysql_insert_id();
	if( is_numeric($med_id) && $med_id ){
		doc_medias_add_url_alias( $med_id );
	}

	return $med_id;
}

/** Cette fonction permet de récupérer un ou plusieurs médias
 *
 *	@param int $hst_id Facultatif, identifiant d'un hébergeur
 *	@param int $pls_id Facultatif, identifiant d'une playlist
 *	@param int $chl_id Facultatif, identifiant d'une chaîne
 *	@param int $med_id Facultatif, identifiant d'un média
 *	@param bool $active Facultatif, par défaut seules les médias publiés sont retournés, mettre False pour tous ceux non publiés, null pour tous les avoir
 *	@param int $import_id Facultatif, identifiant d'import du média
 *	@param $sort Facultatif, ordre de tri à appliquer. Tableau associatif contenant les colonnes de tri dans l'ordre à respecter. Utiliser en clé la colonne (publish ou order) et en valeur la direction (asc ou desc).
 *
 *	@return resource Un résultat MySQL contenant :
 *				- id : identifiant du média
 *				- name : nom du média
 *				- desc : description du média
 *				- img_id : identifiant de l'image
 *				- import_id : identifiant d'import du média
 *				- url_alias : url du média dans RiaShop
 *				- url : url du média sur internet
 *				- date_created : date de création du média
 *				- date_publish : date de publication du média
 */
function doc_medias_get( $hst_id=0, $pls_id=null, $chl_id=0, $med_id=0, $active=true, $import_id='', $sort=false ){
	if( !is_numeric($hst_id) || $hst_id<0 ){
		return false;
	}

	if( $pls_id && ( !is_numeric($pls_id) || $pls_id<0 ) ){
		return false;
	}

	if( !is_numeric($chl_id) || $chl_id<0 ){
		return false;
	}

	if( !is_numeric($med_id) || $med_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select
			med_id as id, med_name as name, med_desc as "desc", med_import_id as import_id, med_url_alias as url_alias, med_url as url,
			med_img_id as img_id, med_date_created as date_created, med_date_published as date_publish
		from doc_medias
	';

	if( $pls_id>0 ){
		$sql .= '
			join doc_playlists_medias on (plm_tnt_id = med_tnt_id and plm_med_id = med_id)
		';
	}

	$sql .= '
		where med_tnt_id = '.$config['tnt_id'].'
			and med_is_deleted = 0
	';

	if( $hst_id>0 ){
		$sql .= ' and med_hst_id = '.$hst_id;
	}

	if( $med_id>0 ){
		$sql .= ' and med_id = '.$med_id;
	}

	if( $chl_id>0 && $pls_id===null ){
		$sql .= '
			and (exists (
				select 1
				from doc_channels_medias
				where chm_tnt_id = '.$config['tnt_id'].'
					and chm_med_id = med_id
					and chm_chl_id = '.$chl_id.'
			) or exists (
				select 1
				from doc_channels_playlists
				join doc_playlists_medias on chp_pls_id = plm_pls_id
				where chp_tnt_id = '.$config['tnt_id'].'
					and chp_tnt_id = '.$config['tnt_id'].'
					and plm_med_id = med_id
					and chp_chl_id = '.$chl_id.'
			))
		';
	}else if( $chl_id>0 && $pls_id===false ){
		$sql .= '
			and exists (
				select 1
				from doc_channels_medias
				where chm_tnt_id = '.$config['tnt_id'].'
					and chm_med_id = med_id
					and chm_chl_id = '.$chl_id.'
			)
		';
	}else if( $pls_id>0 ){
		$sql .= '
			and plm_pls_id = '.$pls_id.'
		';
	}


	if( $active!==null ){
		if( $active ){
			$sql .= ' and ifnull(med_date_published, "")!=""';
		}else{
			$sql .= ' and ifnull(med_date_published, "")=""';
		}
	}

	if( trim($import_id)!='' ){
		$sql .= ' and med_import_id = "'.addslashes( $import_id ).'"';
	}

	// Converti le paramètre de tri en SQL
	$sort_final = array();

	// Récupère un éventuel tri par prix
	if( is_array($sort) ){
		foreach( $sort as $col=>$dir ){
			$col = strtolower(trim($col));
			$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';

			switch( $col ){
				case 'publish' :
					array_push( $sort_final, 'med_date_published '.$dir );
					break;
				case 'pos' :
					if( $pls_id>0 ){
						array_push( $sort_final, 'plm_pos '.$dir );
					}
					break;
			}
		}
	}

	// Ajoute la clause de tri
	if( !sizeof($sort_final) ){
		$sort_final = array( 'med_name asc' );
	}

	if( is_array($sort_final) && sizeof($sort_final) )
		$sql .= ' order by '.implode( ', ', $sort_final ).' ';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de récupérer l'identifiant de l'image d'un média
 *
 *	@param int $med_id Obligatoire, identifiant d'un média
 *
 *	@return int L'identifiant de l'image du média
 */
function doc_medias_get_img_id( $med_id ){
	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}
	global $config;

	$res = ria_mysql_query('
		select med_img_id
		from doc_medias
		where med_tnt_id = '.$config['tnt_id'].'
			and med_id = '.$med_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );

	return $r['med_img_id'];
}

/** Cette fonction permet de vérifier si un média existe
 *	@param int $med_id Obligatoire, identifiant d'un média
 *	@param int $pls_id Optionnel, identifiant d'une playlist
 *
 *	@return bool True si le média existe, False dans le cas contraire
 */
function doc_medias_exists( $med_id, $pls_id=0 ){
	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}

	if( !is_numeric($pls_id) || $pls_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from doc_medias
		where med_tnt_id = '.$config['tnt_id'].'
			and med_id = '.$med_id.'
	';

	if( $pls_id>0 ){
		$sql .= '
			and exists (
				select 1
				from doc_playlists_medias
				where plm_tnt_id = '.$config['tnt_id'].'
					and plm_med_id = med_id
					and plm_pls_id = '.$pls_id.'
			)
		';
	}

	$sql .= ' and med_is_deleted = 0';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de mettre à jour les informations relatives à un média
 *
 *	@param int $med_id Obligatoire, identifiant du média
 *	@param string $name Obligatoire, nom du média
 *	@param string $desc Facultatif, description du média
 *	@param int $import_id Facultatif, identifiant d'import du média, laissé null pour ne pas mettre à jour
 *	@param string $url Facultatif, url du média sur internet, laissé null pour ne pas mettre à jour
 *	@param $duration Facultatif, durée du média (en seconde), laissé null pour ne pas mettre à jour
 *	@param bool $publish Facultatif, par défaut le média ne sera pas publiée, mettre true pour que ce soit le cas, mettre une date au format FR pour spécifier la date de publication
 *
 *	@return bool True si l'ajout s'est correctement déroulée, False dans le cas contraire
 */
function doc_medias_update( $med_id, $name, $desc='', $import_id=null, $url=null, $duration=null, $publish=false ){
	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}

	if( trim($name)=='' ){
		return false;
	}

	if( $duration!==null ){
		if( !is_numeric($duration) || $duration<=0 ){
			return false;
		}
	}

	if( $publish === true ){
		$publish = 'now()';
	}elseif( isdateheure($publish) ){
		$publish = '"'.dateheureparse($publish).'"';
	}else{
		$publish = 'null';
	}

	global $config;

	$sql = '
		update doc_medias
		set med_name = "'.addslashes( $name ).'",
			med_desc = "'.addslashes( $desc ).'",
			'.( $import_id!==null ? 'med_import_id = "'.addslashes( $import_id ).'",' : '' ).'
			'.( $url!==null ? 'med_url = "'.addslashes( $url ).'",' : '' ).'
			'.( $duration!==null ? 'med_duration = '.$duration.',' : '' ).'
			med_date_published = '. $publish .'
		where med_tnt_id = '.$config['tnt_id'].'
			and med_id = '.$med_id.'
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de publier un média.
 *	@param int|array $med_id Obligatoire, identifiant ou tableau d'identifiants de médias
 *
 *	@return bool True si la publication s'est correctement déroulée, False dans le cas contraire
 */
function doc_medias_publish( $med_id ){
	$ar_med_id= control_array_integer( $med_id );
	if( !$ar_med_id ){
		return false;
	}

	global $config;

	$sql = '
		update doc_medias
		set med_date_published = now()
		where med_tnt_id = '.$config['tnt_id'].'
			and med_id in ( '.implode(', ', $ar_med_id ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de dépublier un média.
 *	@param int|array $med_id Obligatoire, identifiant ou tableau d'identifiants de médias
 *
 *	@return bool True si la dépublication s'est correctement déroulée, False dans le cas contraire
 */
function doc_medias_unpublish( $med_id ){
	$ar_med_id = control_array_integer( $med_id );
	if( !$ar_med_id ){
		return false;
	}

	global $config;

	$sql = '
		update doc_medias
		set med_date_published = null
		where med_tnt_id = '.$config['tnt_id'].'
			and med_id in ( '.implode(', ', $ar_med_id ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet de crééer l'url RiaShop d'un média.
 *	@param int $med_id Obligatoire, identifiant d'un média
 *
 *	@return bool True si la création a correctement fonctionnée, False dans la cas contraire
 */
function doc_medias_add_url_alias( $med_id ){
	if( !doc_medias_exists($med_id) ){
		return false;
	}
	global $config;

	// Récupère les sites
	$rwst = wst_websites_get();
	if( !$rwst || !ria_mysql_num_rows($rwst) )
		return false;

	$url = false;

	// Crée les alias
	$alias = rew_rewritemap_generated( array($med_id), CLS_VIDEO );

	while( $wst = ria_mysql_fetch_array($rwst) ){
		$prd_pages = cfg_urls_get( $wst['id'], CLS_VIDEO);
		if( $prd_pages  ){
			$added = false;
			while( $page = ria_mysql_fetch_array($prd_pages) ){
				if( !$added ){
					$url = rew_rewritemap_add_specify_class( CLS_VIDEO, $alias.$page['key'].'/', $page['url'].( strstr($page['url'], '?') ? '&' : '?' ).'veo='.$med_id, 200, $wst['id'], false, null, $med_id );
				}else{
					rew_rewritemap_add_specify_class( CLS_VIDEO, $alias.$page['key'].'/', $page['url'].( strstr($page['url'], '?') ? '&' : '?' ).'veo='.$med_id, 200, $wst['id'], false, null, $med_id );
				}
				$added = true;
			}
		}
	}

	$res = false;
	if( $url!==false ){
		$res = ria_mysql_query('
			update doc_medias
			set med_url_alias="'.addslashes( $url ).'"
			where med_tnt_id = '.$config['tnt_id'].'
				and med_id = '.$med_id.'
		');
	}

	return $res;
}

/** Cette fonction permet de supprimer une ou plusieurs médias.
 *	@param int|array $med_id Optionnel, identifiant ou tableau d'identifiants de médias
 *	@param int|array $pls_id Optionnel, identifiant ou tableau d'identifiants de playlist (ignoré si $med_id est renseigné)
 *
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire (ou si aucun des paramètres n'est renseigné)
 */
function doc_medias_del( $med_id=0, $pls_id=0 ){
	$ar_med_id = control_array_integer( $med_id, false );
	if( !$ar_med_id ){
		return false;
	}

	if( $pls_id != 0 ){
		$ar_pls_id = control_array_integer( $pls_id, false );
		if( !$ar_pls_id ){
			return false;
		}
	}

	if( !sizeof($ar_med_id) ){
		if( sizeof($ar_pls_id) ){
			$ar_med_id = doc_playlists_medias_get_ids( $ar_pls_id );
			if( !is_array($ar_med_id) ){
				return false;
			}
		}
	}

	if( !sizeof($ar_med_id) ){
		return true;
	}

	global $config;

	$sql = '
		update doc_medias
		set med_date_deleted = now(), med_is_deleted = 1
		where med_tnt_id = '.$config['tnt_id'].'
			and med_id in ( '.implode( ', ', $ar_med_id ).' )
	';

	return ria_mysql_query( $sql );
}

/** Cette fonction permet l'ajout d'un fichier image à un média.
 *	@param int $med_id Obligatoire, identifiant du produit.
 *	@param string $filename Obligatoire, nom du fichier image.
 *	@param string $srcname Optionnel, nom de l'image source
 *
 *	@return int L'identifiant attribué à l'image, False en cas d'échec.
 */
function doc_medias_image_add( $med_id, $filename, $srcname='' ){
	if( !is_numeric($med_id) || $med_id <= 0 ){
		return false;
	}

	if( trim($filename)=='' ){
		return false;
	}

	// crée l'image, ou récupère l'image existante p/r au md5 de son contenu
	$new_img_id = img_images_add( $filename, $srcname, true );
	if( !$new_img_id ){
		return false;
	}

	global $config;

	$old_img = doc_medias_get_img_id( $med_id );

	// si l'image a changée
	if( $old_img != $new_img_id ){
		$sql = '
			update doc_medias
			set med_img_id = '.$new_img_id.'
			where med_tnt_id = '.$config['tnt_id'].'
				and med_id = '.$med_id.'
		';

		if( ria_mysql_query($sql) ){
			// Mise à jour du nombre d'utilisation de la nouvelle image
			img_images_count_update( $new_img_id );

			// Mise à jour du nombre d'utilisation de l'ancienne image
			if( $old_img ){
				img_images_count_update( $old_img );
			}
		}else{
			$new_img_id = false;
		}
	}

	return $new_img_id;
}

/**	Cette fonction permet la réutilisation d'une image existante pour un média.
 *	@param int $med_id Obligatoire, identifiant d'un média
 *	@param int $img_id Obligatoire, identifiant d'une image
 *
 *	@return bool true en cas de succès, false en cas d'échec
 *	@deprecated Sauf erreur, c'est fonction n'est utilisée nulle part dans le moteur
 */
function doc_medias_image_add_existing( $med_id, $img_id ){
	global $config;

	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}

	if( !is_numeric($img_id) || $img_id<=0 ){
		return false;
	}

	$old_img = doc_medias_get_img_id( $med_id );

	if( $old_img == $img_id ){
		return true;
	}

	$res = ria_mysql_query('update doc_medias set med_img_id='.$img_id.' where prd_tnt_id='.$config['tnt_id'].' and med_id='.$med_id);
	if( !$res ){
		return false;
	}

	img_images_count_update($img_id);
	if( $old_img>0 ){
		img_images_count_update( $old_img );
	}

	return true;
}

/** Cette fonction permet de rattacher un objet à un média.
 *	@param int $med_id Obligatoire, identifiant d'un média
 *	@param int $cls_id Obligatoire, identifiant d'une classe
 *	@param int|array $obj_id Obligatoire, identifiant ou tableau d'identifiants d'un objet
 *
 *	@return bool True si l'ajout s'est correctement déroulé, False dans le cas contraire
 */
function doc_medias_objects_add( $med_id, $cls_id, $obj_id ){
	if( !doc_medias_exists( $med_id ) ){
		return false;
	}

	if( !fld_classes_exists( $cls_id ) ){
		return false;
	}

	$ar_obj_id = control_array_integer( $obj_id, true, false, true );
	if( !$ar_obj_id ){
		return false;
	}

	if( doc_objects_exists($med_id, $cls_id, $obj_id) ){
		return true;
	}

	global $config;

	$ar_cols = array();
	$ar_vals = array();

	$ar_cols[] = 'mob_tnt_id';
	$ar_vals[] = $config['tnt_id'];

	$ar_cols[] = 'mob_med_id';
	$ar_vals[] = $med_id;

	$ar_cols[] = 'mob_cls_id';
	$ar_vals[] = $cls_id;

	for( $i=0 ; $i<sizeof($ar_obj_id) ; $i++ ){
		$ar_cols[] = 'mob_id_'.$i;
		$ar_vals[] = $ar_obj_id[ $i ];
	}

	$sql = '
		insert into doc_medias_objects
			( '.implode( ', ', $ar_cols ).' )
		values
			( '.implode( ', ', $ar_vals ).' )
	';

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	// switch( $cls_id ){
	// 	case CLS_CATEGORY : {
	// 		prd_categories_set_date_modified( $ar_obj_id[0] );
	// 		break;
	// 	}
	// 	case CLS_PRODUCT : {
	// 		prd_products_set_date_modified( $ar_obj_id[0] );
	// 		break;
	// 	}
	// 	case CLS_USER : {
	// 		gu_users_set_date_modified( $ar_obj_id[0] );
	// 		break;
	// 	}
	// }

	return true;
}

/** Cette fonction permet de vérifier si un lient entre un média et un objet existe.
 *	@param int $med_id Obligatoire, identifiant d'un média
 *	@param int $cls_id Obligatoire, identifiant d'une classe
 *	@param int|array $obj_id Obligatoire, identifiant ou tableau d'identifiants de l'objet
 *
 *	@return bool True si le lien existe, False dans le cas contraire
 */
function doc_medias_objects_exists( $med_id, $cls_id, $obj_id ){
	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}

	if( !is_numeric($cls_id) || $cls_id<=0 ){
		return false;
	}

	$ar_obj_id = control_array_integer( $obj_id, true, false, true );
	if( !$ar_obj_id ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from doc_medias_objects
		where mob_tnt_id = '.$config['tnt_id'].'
			and mob_med_id = '.$med_id.'
			and mob_cls_id = '.$cls_id.'
	';

	for( $i=0 ; $i<sizeof($ar_obj_id) ; $i++ ){
		$sql .= ' and mob_id_'.$i.' = '.$ar_obj_id[ $i ];
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de supprimer un lien entre un média et un objet.
 *	Le paramètre $obj_id est obligatoire si $cls_id est spécifié. Si $cls_id n'est pas spécifié, le paramètre $obj_id n'est pas pris en compte.
 *	L'appel sans arguments n'est pas autorisé (cela reviendrait à une vidage de la table)
 *
 * 	@param int $med_id Optionnel, identifiant d'un média
 *	@param int $cls_id Optionnel, identifiant d'une classe
 *	@param int|array $obj_id Optionnel, identifiant ou tableau d'identifiants d'un objet
 *
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function doc_medias_objects_del( $med_id=0, $cls_id=0, $obj_id=0 ){
	$no_med = $no_object = true;

	if( !is_numeric($med_id) || $med_id<0 ){
		return false;
	}elseif( $med_id>0 ){
		$no_med = false;
	}

	if( !is_numeric($cls_id) || $cls_id<0 ){
		return false;
	}elseif( $cls_id>0 ){
		$ar_obj_id = control_array_integer( $obj_id, false, false, true );
		if( !$ar_obj_id ){
			return false;
		}

		$no_object = false;
	}

	if( $no_med && $no_object ){
		return false;
	}

	global $config;

	// Restriction sur la sélection / suppression des lignes concernées par les paramètres
	$sub_sql = '
		where mob_tnt_id = '.$config['tnt_id'].'
	';

	if( $med_id>0 ){
		$sql .= ' and mob_med_id = '.$med_id;
	}

	if( $cls_id>0 ){
		$sql .= ' and mob_cls_id = '.$cls_id;

		if( sizeof($ar_obj_id) ){
			for( $i=0 ; $i<sizeof($ar_obj_id) ; $i++ ){
				$sql .= ' and mob_id_'.$i.' = '.$ar_obj_id[ $i ];
			}
		}
	}

	$sql = '
		delete from doc_medias_objects
		'.$sub_sql.'
	';

	// $rget = false;
	// if( $cls_id>0 && in_array($cls_id, array(CLS_USER, CLS_CATEGORY, CLS_PRODUCT)) ){
	// 	$sql_get = '
	// 		select mob_cls_id as cls_id, mob_id_0 as obj_id_0, mob_id_1 as obj_id_1, mob_id_2 as obj_id_2
	// 		'.$sub_sql.'
	// 	';

	// 	$rget = ria_mysql_query( $sql );
	// }

	if( !ria_mysql_query($sql) ){
		return false;
	}

	// if( $rget ){
	// 	while( $get = ria_mysql_fetch_assoc($rget) ){
	// 		switch( $get['cls_id'] ){
	// 			case CLS_CATEGORY : {
	// 				prd_categories_set_date_modified( $get['obj_id_0'] );
	// 				break;
	// 			}
	// 			case CLS_PRODUCT : {
	// 				prd_products_set_date_modified( $get['obj_id_0'] );
	// 				break;
	// 			}
	// 			case CLS_USER : {
	// 				gu_users_set_date_modified( $get['obj_id_0'] );
	// 				break;
	// 			}
	// 		}
	// 	}
	// }

	return true;
}

/** Cette fonction permet de récupérer les object rattachés à un média.
 *	Si $cls_id n'est pas spécifié, le paramètre $obj_id n'est pas pris en compte.
 *	@param int $med_id Optionnel, identifiant d'un média
 *	@param int $cls_id Optionnel, identifiant d'une classe
 *	@param int|array $obj_id Optionnel, identifiant ou tableau d'identifiants d'un objet
 *
 *	@return resource Un résultat MySQL contenant :
 *		- med_id : Identifiant du média
 *		- med_name : nom du média
 *		- med_desc : description du média
 *		- med_url_alias : url du média
 *		- med_img_id : id de l'image du média
 *		- med_import_id : identifiant d'import
 *		- cls_tnt_id : identifiant du tenant de la classe
 *		- cls_id : Identifiant de la classe
 *		- cls_name : nom de la classe
 *		- obj_id_X : identifiant de l'objet, ou "X" est l'indice de base 0
 */
function doc_medias_objects_get( $med_id=0, $cls_id=0, $obj_id=0 ){
	if( !is_numeric($med_id) || $med_id<0 ){
		return false;
	}

	if( !is_numeric($cls_id) || $cls_id<=0 ){
		return false;
	}

	$ar_obj_id = control_array_integer( $obj_id, false, false, true );
	if( !$ar_obj_id ){
		return false;
	}

	global $config;

	$sql = '
		select
			med_id, med_name, med_desc, med_import_id, med_url_alias, med_img_id,
			cls_tnt_id, cls_id, cls_name,
			mob_id_0 as obj_id_0, mob_id_1 as obj_id_1, mob_id_2 as obj_id_2
		from doc_medias_objects
			join doc_medias on (mob_tnt_id = med_tnt_id and mob_med_id = med_id )
			join fld_classes on (mob_cls_id = cls_id)
		where mob_tnt_id = '.$config['tnt_id'].'
	';

	if( $med_id>0 ){
		$sql .= ' and mob_med_id = '.$med_id;
	}

	if( $cls_id>0 ){
		$sql .= ' and mob_cls_id = '.$cls_id;

		if( sizeof($ar_obj_id) ){
			for( $i=0 ; $i<sizeof($ar_obj_id) ; $i++ ){
				$sql .= ' and mob_id_'.$i.' = '.$ar_obj_id[ $i ];
			}
		}
	}

	return ria_mysql_query( $sql );
}

/** Cette fonction permet d'activer un média sur un site web.
 *	@param int $med_id Obligatoire, identifiant d'un média
 *	@param int $wst_id Obligatoire, identifiant d'un site
 *
 *	@return bool True si le lien a correctement été créé, False dans le cas contaire
 */
function doc_medias_websites_add( $med_id, $wst_id ){
	if( !doc_medias_exists($med_id) ){
		return false;
	}

	if( !wst_websites_exists($wst_id) ){
		return false;
	}

	if( doc_medias_websites_exists($med_id, $wst_id) ){
		return true;
	}

	global $config;

	return ria_mysql_query('
		insert into doc_medias_websites
			( mdw_tnt_id, mdw_med_id, mdw_wst_id )
		values
			( '.$config['tnt_id'].', '.$med_id.', '.$wst_id.' )
	');
}

/** Cette fonction permet de vérifier si un média est présent sur un site.
 *	@param int $med_id Obligatoire, identifiant d'un média
 *	@param int $wst_id Optionnel, identifiant d'un site web
 *
 *	@return bool True si c'est bien le cas, False dans le cas contraire
 */
function doc_medias_websites_exists( $med_id, $wst_id=0 ){
	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select 1
		from doc_medias_websites
		where mdw_tnt_id = '.$config['tnt_id'].'
			and mdw_med_id = '.$med_id.'
	';

	if( $wst_id>0 ){
		$sql .= ' and mdw_wst_id = '.$wst_id;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de retirer un média sur un ou plusieurs sites.
 *	@param int $med_id Obligatoire, identifiant d'un média
 *	@param int $wst_id Optionnel, identifiant ou tableau d'identifiants de sites web
 *
 *	@return bool True si c'est bien le cas, False dans le cas contraire
 */
function doc_medias_websites_del( $med_id, $wst_id=0 ){
	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}

	$ar_wst_id = control_array_integer( $wst_id, false );
	if( !$ar_wst_id ){
		return false;
	}

	global $config;

	$sql = '
		delete from doc_medias_websites
		where mdw_tnt_id = '.$config['tnt_id'].'
			and mdw_med_id = '.$med_id.'
	';

	if( sizeof($ar_wst_id) ){
		$sql .= ' and mdw_wst_id in ( '.implode( ', ', $ar_wst_id ).' )';
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/** Cette fonction permet de récupérer les sites sur lesquels se trouve un média
 *	@param int $med_id Obligatoire, identifiant d'un média
 *	@param int $wst_id Optionnel, identifiant d'un site web
 *
 *	@return resource Un résultat MySQL contenant :
 *			- med_id : identifiant d'un média
 *			- med_name : nom du média
 *			- wst_id : identifiant du site
 *			- wst_name : nom du site
 */
function doc_medias_websites_get( $med_id, $wst_id=0 ){
	if( !is_numeric($med_id) || $med_id<=0 ){
		return false;
	}

	if( !is_numeric($wst_id) || $wst_id<0 ){
		return false;
	}

	global $config;

	$sql = '
		select med_id, med_name, wst_id, wst_name
		from doc_medias_websites
			join wst_websites on (mdw_tnt_id = wst_tnt_id and mdw_wst_id = wst_id)
		where mdw_tnt_id = '.$config['tnt_id'].'
			and mdw_med_id = '.$med_id.'
	';

	if( $wst_id>0 ){
		$sql .= ' and mdw_wst_id = '.$wst_id;
	}

	return ria_mysql_query( $sql );
}

/// @}
/// @}
