<?php 
	require_once('prd/colisage.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class colisageUpdateTest extends PHPUnit_Framework_TestCase {

        /** Fonction permettant de tester la mise à jour d'un conditionnement
         * @dataProvider validColisage
         */
        public function testColisageValidUpdate($id, $name, $qte, $dps_id, $pkg_id){

            $this->assertTrue( prd_colisage_types_update($id, $name, $qte, $dps_id, $pkg_id), 'Erreur lors de la mise à jour du conditionnement');

            // Vérifie que les champs ont bien été mis à jour
            $rcol = prd_colisage_types_get($id);
            $this->assertTrue($rcol && ria_mysql_num_rows($rcol) == 1, 'Erreur lors de la vérification des champs du conditionnement mis à jour');
            $col = ria_mysql_fetch_assoc($rcol);

            if($name){
                $this->assertEquals($name, $col['name'], 'Erreur: nom du conditionnement non mis à jour');
            }

            if($qte){
                $this->assertEquals($qte, $col['qte'], 'Erreur: quantité du conditionnement non mis à jour');
            }

            if($dps_id){
                $this->assertEquals($dps_id, $col['dps_id'], 'Erreur: dépot associé au conditionnement non mis à jour');
            }
            
            if($pkg_id){
                $this->assertEquals($pkg_id, $col['pkg_id'], 'Erreur: package associé au conditionnement non mis à jour');
            }
        }

        /** Fonction permettant de tester la mise à jour d'un conditionnement avec des paramètre invalide
         * @dataProvider invalideColisage
         */
        public function testColisageInvalidUpdate($id, $name, $qte, $dps_id, $pkg_id, $error){

            $this->assertFalse( prd_colisage_types_update($id, $name, $qte, $dps_id, $pkg_id), $error);
        }

        /** Fonction permettant de tester la mise à jour de la quantité d'un conditionnement
         */
        public function testColisageQteSet(){

            $this->assertTrue(prd_colisage_types_set_qte(2, 25), 'Erreur lors de la mise à jour de la quantité du conditionnement');

            // Vérifie que la quantité a bien été mise à jour
            $rcol = prd_colisage_types_get(2);
            $this->assertTrue($rcol && ria_mysql_num_rows($rcol) == 1, 'Erreur lors de la vérification de la mise à jour de la quantité du conditionnement');
            $col = ria_mysql_fetch_assoc($rcol);

            $this->assertEquals(25, $col['qte'], 'Erreur: quantité du conditionnement non mise à jour');
        }

        public static function validColisage(){
            return array(
                //   id          name         qte   dps_id  pkg_id
                array(2, 'Nouveau nom',    false,   false,  false),
                array(2, 'Nouveau nom',       25,   false,  false),
            );
        }

        public static function invalideColisage(){
            return array(
                //   id          name         qte   dps_id  pkg_id  message d'erreur
                array(2,            '',    false,   false,  false, 'Erreur: mise à jour du conditionnement avec un nom invalide'),
                array(2, 'Nouveau nom',       0,   false,  false, 'Erreur: mise à jour du conditionnement avec une quantité invalide'),
            );
        }
    }
