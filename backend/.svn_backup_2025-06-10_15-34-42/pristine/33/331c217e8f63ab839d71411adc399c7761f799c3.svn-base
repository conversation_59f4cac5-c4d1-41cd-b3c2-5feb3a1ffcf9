# Created by .gitignore support plugin (hsz.mobi)
## JetBrains IDE project directory
.idea/

## File-based project format
*.ipr
*.iws

## Additional for IntelliJ
out/

# generated by mpeltonen/sbt-idea plugin
.idea_modules/

# generated by JIRA plugin
atlassian-ide-plugin.xml

# generated by Crashlytics plugin (for Android Studio and Intellij)
com_crashlytics_export_strings.xml

# ignore composer vendor directory and lock file
vendor
composer.lock

# ignore mac files
.DS_Store/

docs

composer
composer.phar
