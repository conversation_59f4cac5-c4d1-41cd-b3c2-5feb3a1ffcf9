<?php

/** 
 * \defgroup api-devices-location Localisation
 * \ingroup Yuto 
 * @{	
 * \page api-devices-locations-add Ajout
 *
 * Cette fonction ajoute une localisation
 *
 *	\code
 *		POST /devices/locations/
 *	\endcode
 *	
 * @param raw_data Obligatoire, Localisation en JSON
 * 
 *	\code{.json}
 *     {
 *				"latitude"  : latitude de la localisation
 *				"longitude" : longitude de la localisation
 *				"accuracy"  : Précision de la localisation
 *				"date"      : date de la localisation
 *     }
 *	\endcode

 * @return true si l'ajout s'est déroulé avec succès. 
 * @}
*/

switch ($method) {

		// permet d'ajouter des positions 
	case 'add':

		if (!trim($raw_data)) {
			throw new Exception("Paramètre invalide.");
		}

		// si la variable de géolocalisation est désactivé alors on coupe l'neregistrement des positions côté administration.
		if (isset($config['fdv_location_active']) && !$config['fdv_location_active']) {
			$result = true;
		} else {

			$objs = json_decode($raw_data);
			if (is_array($objs)) {
				foreach ($objs as $obj) {
					$obj = json_decode(json_encode($obj), true); // convertion en tableau (stdclass)

					if (!is_numeric($obj['latitude']) || !is_numeric($obj['longitude']) || !is_numeric($obj['accuracy']) || !trim($obj['date'])) {
						throw new Exception("Paramètre invalide.");
					}

					if (!dev_devices_locations_add($config['dev_id'], $obj['date'], $obj['latitude'], $obj['longitude'], $obj['accuracy'])) {
						throw new Exception("Erreur lors de l'ajout d'une position.");
					}
				}

				// met à jour la ligne sur devices
				if ($obj) {
					dev_devices_set_location($config['dev_id'], $obj['date'], $obj['latitude'], $obj['longitude'], $obj['accuracy']);
				}
			}
			$result = true;
		}

		break;
}

