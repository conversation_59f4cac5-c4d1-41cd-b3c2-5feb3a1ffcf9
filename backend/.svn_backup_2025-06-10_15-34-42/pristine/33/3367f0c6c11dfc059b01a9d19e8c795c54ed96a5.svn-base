<?php
	// Cyberplus
	/**	\defgroup cyberplus Cyberplus
	 *	\ingroup payment_external
	 *	Ce module permet les paiement avec Cyberplus, module de paiement de la Banque Populaire
	 *
	 *	Variables de config utilisées :
	 *		- cyberplus_test_mode : si le module est en mode TEST ou en mode PRODUCTION
	 *		- cyberplus_site_id : identifiant donné au site
	 *		- cyberplus_certificate : numéro de certificat (information différente entre le mode TEST et PRODUCTION)
	 *	@{
	 */

	require_once('PaymentExternal.inc.php');
	require_once('ord.installments.inc.php');
	require_once('NetAffiliation.inc.php');

	/**	\class Cyberplus
	 *	\brief Cette classe est l'implémentation concrète du fournisseur Cyberplus en tant que prestataire de paiement externe.
	 *
	 */
	class Cyberplus extends PaymentExternal {

		/// Liste des moyens de paiement activés grâce à la méthode setPaymentTypes
		public static $payment = '';

		/// L'instance de classe (Singleton)
		private static $Instance;

		/**
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @return void
		 */
		public static function doPayment(){
			return self::getInstance()->_doPayment();
		}

		/**
		 * Renvoie le singleton
		 * @return object Le singleton
		 */
		public static function getInstance(){
			if( !self::$Instance ){
				self::$Instance = new self;
			}
			return self::$Instance;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@return object L'instance
		 */
		public static function getPaymentResult(){
			return self::getInstance()->_getPaymentResult();
		}

		/**
		 *	Cette méthode permet de définir le moyen de paiement choisi par l'internaute
		 *	@param string $pay_type Obligatoire, moyen de paiement choisi ou un tableau (voir le code pour les valeurs acceptées)
		 *	@return object L'instance
		 */
		public static function setPaymentTypes( $pay_type ){
			$vals_auth = array( 'AMEX', 'AURORE-MULTI', 'BUYSTER', 'CB', 'COFINOGA', 'E-CARTEBLEUE', 'MASTERCARD', 'JCB', 'MAESTRO', 'ONEY', 'ONEY_SANDBOX', 'PAYPAL', 'PAYPAL_SB', 'PAYSAFECARD', 'VISA', 'VISA_ELECTRON', 'COF3XCB', 'COF3XCB_SB' );

			if( !is_array($pay_type) ){
				if( trim($pay_type)=='' ){
					return false;
				}

				if( !in_array($pay_type, $vals_auth) ){
					return false;
				}

				$pay_type = array( $pay_type );
			}else{
				if( !sizeof($pay_type) ){
					return false;
				}

				foreach( $pay_type as $p ){
					if( !in_array($p, $vals_auth) ){
						return false;
					}
				}
			}

			self::$payment = implode( ';', $pay_type );
			return self::$Instance;
		}

		/**
		 * 	Cette fonction permet de récupérer les types de paiement choisi ou à proposer à l'internaute
		 *	@return La valeur à affecter à vads_payment_cards
		 */
		public static function getPaymentTypes(){
			return self::$payment;
		}

		/**
		 * Génération du formulaire affichant les cartes disponibles celui-ci redirigera l'utilisateur vers la banque.
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @return string Le formulaire html
		*/
		public function _doPayment(){
			global $config;

			// Récupération de la commande
			$ord_id = $this->getOrderId();
			if( !is_numeric($ord_id) || $ord_id<0 ){
				return false;
			}

			$rorder = ord_orders_get( 0, $ord_id );
			if( !$rorder || !ria_mysql_num_rows($rorder) ){
				return false;
			}

			// Information sur la commande en cours
			$order = ria_mysql_fetch_array( $rorder );

			// Création du numéro de transaction
			$fp = fopen( $config['doc_dir'].'/compteur/count.txt', 'r+' );
			flock( $fp, LOCK_EX );

			$count = (int)fread($fp, 6);
			$count++;
			if($count < 0 || $count > 899999) {
				$count = 0;
			}

			fseek( $fp, 0 );
			ftruncate( $fp, 0 );

			fwrite( $fp, $count );
			flock( $fp, LOCK_UN );
			fclose( $fp );

			$trans_id = sprintf( "%06d", $count );

			// Initialisation des paramètres
			$params = array(
				// Informations principales
				'vads_site_id'			=> $config['cyberplus_site_id'],
				'vads_amount'			=> 100 * number_format( $order['total_ttc'], 2, '.', '' ),
				'vads_currency'			=> '978',
				'vads_ctx_mode'			=> $config['cyberplus_test_mode'] ? 'TEST' : 'PRODUCTION',
				'vads_page_action'		=> 'PAYMENT',
				'vads_action_mode' 		=> 'INTERACTIVE',
				'vads_payment_config' 	=> 'SINGLE',
				'vads_version'			=> 'V2',
				'vads_payment_cards' 			=> self::getPaymentTypes(),

				// Informations sur le retour du paiement
				'vads_return_mode' 				=> 'GET',
				'vads_redirect_success_timeout' => '5',
				'vads_redirect_success_message' => 'Redirection vers la boutique dans quelques instants',
				'vads_redirect_error_timeout' 	=> '5',
				'vads_redirect_error_message' 	=> 'Redirection vers la boutique dans quelques instants',
				'vads_url_error' 	=> $config['cyberplus_url_error'],
				'vads_url_cancel' 	=> $config['cyberplus_url_success'],

				// Informations sur la transaction unique
				'vads_trans_id' 	=> $trans_id,
				'vads_order_id' 	=> $order['id'],
				'vads_trans_date' 	=> gmdate( "YmdHis" )
			);

			// Génération de la signature
			ksort( $params );
			$contenu_signature = "";
			foreach ($params as $name => $val){
				$contenu_signature .= $val."+";
			}


			$contenu_signature .= $config['cyberplus_certificate'];
			$params['signature'] = sha1( $contenu_signature );

			$url = 'https://systempay.cyberpluspaiement.com/vads-payment/?';
			foreach( $params as $name=>$val ){
				$url .= $name.'='.$val.'&';
			}

			return $url;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	@return object L'instance
		 */
		public function _getPaymentResult(){
			global $config;

			mail( '<EMAIL>', 'Réception infos Cyberplus - Tenant '.$config['tnt_id'], print_r($_REQUEST, true) );

			$contenu_signature = "";
			$params = $_REQUEST;
			ksort($params);

			foreach ($params as $nom => $valeur){
				if(substr($nom,0,5) == 'vads_'){
					$contenu_signature .= $valeur."+";
				}
			}

			$contenu_signature .= $config['cyberplus_certificate'];
			$signature_calculee = sha1($contenu_signature);

			if( isset($_REQUEST['signature']) && $signature_calculee == $_REQUEST['signature'] ){
				if($_REQUEST['vads_result'] == "00"){

					require_once('orders.inc.php');
					ord_orders_pay_type_set( $params['vads_order_id'], 1 );
					ord_orders_update_status( $params['vads_order_id'], 3, '' ); // En attente de règlement
					ord_orders_update_status( $params['vads_order_id'], 4, '' ); // En attente de traitement

					// Confirmation de la commande à NetAffiliation
					require_once('NetAffiliation.inc.php');
					$affi = new NetAffiliation();
					$affi->getOnlinePaymentTag( $params['vads_order_id'] );
				}
			}

			return self::$Instance;
		}

		private static function getUrlAction(){
			global $config;

			if( !isset($config['cyberplus_test_mode']) || $config['cyberplus_test_mode'] ){
				$url = 'https://test.authorize.net/gateway/transact.dll';
			}else{
				$url = 'https://secure.authorize.net/gateway/transact.dll';
			}

			return $url;
		}
	}

/// @}
