<?php

	/**	\file errors-404.php
	 * 	Cette page affiche la liste des erreurs 404 survenues sur le(s) site(s) du client et permet d'intervenir dessus
	 * 	pour enregistrer des redirections.
	 */

	require_once('errors.inc.php');
	
	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REDIRECTION_404');

	// Filtre "Limiter aux URLs n'ayant pas encore de redirection"
	
	if( isset($_REQUEST['no-redirection']) ){
		$no_redirection = $_REQUEST['no-redirection'];
	}else {
		$no_redirection = true;
	}
	
	// Filtre "Afficher les URLs étant notées comme résolues"

	if( isset($_REQUEST['resolved']) ){
		$resolved = $_REQUEST['resolved'];
	}else {
		$resolved = false;
	}
	
	// Website
	$website = wst_websites_get();

	if(isset($_GET['wst'])){
		if($_GET['wst']=='all'){
			$wst_id = 0;
		}elseif( ria_mysql_num_rows($website)==1 ){
			$wst_id = $config['wst_id'];
		}else{
			$wst_id = str_replace('w-','',$_GET['wst']);
		}
	}elseif( ria_mysql_num_rows($website)>1 && (!isset($wst_id) || $wst_id<=0) ){
		$wst_id = 0;
	}else{
		$wst_id = $config['wst_id'];
	}


	$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
	$filterTypes = err_errors_filters_types_get();
	$filterType = isset($_REQUEST['filter-type']) && isset($filterTypes[$_REQUEST['filter-type']]) ? $_REQUEST['filter-type'] : 'pages';

	$filterSort = isset($_REQUEST['sort']) ? $_REQUEST['sort'] : 'date-desc';

	// Langues active(s) pour un site
	$filterLngs = array();
	$r_lng = wst_websites_languages_get( $wst_id );
	if( $r_lng ){
		while( $lng = ria_mysql_fetch_array($r_lng) ){
			$filterLngs[ $lng['lng_code'] ] = $lng['name'];
		}
	}

	// Langue sur laquelle la liste est actuellement filtrée
	$filterLng = isset($_REQUEST['lng']) ? $_REQUEST['lng'] : 'all';

	// Variable pour la mise en place des périodes
	$date1 = isset($_SESSION['datepicker_date1']) ?  $_SESSION['datepicker_date1']  : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ?  $_SESSION['datepicker_date2']  : false;
	
	
	$pmt_id = isset($_GET['pmt']) && is_numeric($_GET['pmt']) && $_GET['pmt'] ? $_GET['pmt'] : 0;
		
	// Enregistre les URL de redirection
	if( isset($_POST['save']) ){
		$error = '';
		
		if( isset($_POST['redirection']) && is_array($_POST['redirection']) && sizeof($_POST['redirection']) ){
			foreach( $_POST['redirection'] as $id => $rdt ){
				$url_site = cfg_overrides_get_value( 'site_url', isset($_POST['wsturl'][$id]) ? $_POST['wsturl'][$id] : $wst_id );
				$rdt = str_replace( $url_site, '', $rdt );
				
				$_POST['oldurl'][$id] = urldecode($_POST['oldurl'][$id]);
				if( rew_strip($_POST['oldurl'][$id])!=$rdt ){
					if( trim($rdt) ){
						$res = err_errors_redirection_add( $_POST['wsturl'][$id], $_POST['oldurl'][$id], $_POST['lngurl'][$id], $rdt );
						if( !$res ){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des redirections. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
							break;
						}elseif( (int) $res == ERROR_REW_404 ){
							$error = ($error!='' ? $error."\n" : '' ). str_replace("#param[redirection]#", $rdt, str_replace("#param[ancienne_url]#", $_POST['oldurl'][$id], _("La redirection '#param[redirection]#' mise en place pour l'url '#param[ancienne_url]#' ne peut être sauvegardée car celle-ci retourne une erreur 404."))) ;
						}
					} elseif( !err_errors_url_redirection_delete($_POST['wsturl'][$id], $_POST['oldurl'][$id], $_POST['lngurl'][$id]) ){
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des redirections. \nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
						break;
					}
				}else{
					$error = str_replace("#param[redirection]#", $rdt, str_replace("#param[ancienne_url]#", $_POST['oldurl'][$id], _("La redirection saisie \"#param[redirection]#\" est identique à l'url \"#param[ancienne_url]#\" ayant générée l'erreur 404.")));
				}
			}
		}
		
		if( $error=='' ){
			$url = '/admin/config/redirections/errors/errors-404.php?wst='.$_POST['wst_id'].'&page='.$_POST['page'].(isset($_POST['filter']) ? '&filter='.$_POST['filter'] : '').'&no-redirection='. (isset($_REQUEST['no-redirection']) ?'1':'0').'&resolved='.$resolved.'&filter-type='.$_POST['filter-type'].'&date1='.$date1.'&date2='.$date2.'&sort='.$_POST['filter-sort'].'&lng='.$filterLng;
			header('Location: '.$url);
			exit;
		}
	}

	$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] ? $_GET['page'] : 1;

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Erreurs 404') . ' - ' . _('Redirections') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo _('Erreurs 404'); ?></h2>

	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<form action="errors-404.php" method="post">
		<input type="hidden" name="wst_id" id="wst_id" value="<?php print $wst_id; ?>" />
        <input type="hidden" name="filter" id="filter" value="<?php print $filter; ?>" />
		<input type="hidden" name="page" id="page" value="<?php print $page; ?>"  />
        <input type="hidden" name="lng" id="lng" value="<?php print $filterLng; ?>" />
		<input type="hidden" name="filterType" id="filterType" value="<?php print $filterType; ?>" />
		<input type="hidden" name="filterSort" id="filterSort" value="<?php print $filterSort; ?>" />
		<?php 
			// Dates de début et de fin
			if(isset($date1, $date2)){
				print '<input type="hidden" name="date1" id="date1" value="'.$date1.'"/>';
				print '<input type="hidden" name="date2" id="date2" value="'.$date2.'"/>';
			}
		?>
		<div class="stats-menu">
			<div id="riadatepicker"></div>
			<?php 
				print view_websites_selector( $wst_id, true, '', true );
			?>
			<?php 
				$filterSortOptions = array('date-desc'=>_('Dernière occurence'),'date-asc'=>_('Première occurence'),'count-desc'=>_('Nombre d\'occurence'));
			?>
			<div id="div-filter-sort">
				<select id="filter-sort" name="filter-sort">
				<?php foreach($filterSortOptions as $key => $value) { 
					echo '<option value="' .$key. '" '.($filterSort === $key ?' selected="selected"' : ''). '>'.$value.'</option>';
				}?>
				</select>
			</div>
			<div id="div-filter-type">
				<?php
					echo '<select id="filter-type" name="filter-type">';
					foreach ($filterTypes as $key => $value) echo '<option value="' . $key . '"' . ($filterType === $key ? ' selected="selected"' : '') . '>' . htmlspecialchars($value['name']) . '</option>';
					echo '</select>';
				?>
			</div>
			<?php if( sizeof($filterLngs)>1 ){?>
				<div id="rialanguagepicker">
            	<div class="selectorview">
                	<div class="left">
                    	<span class="function_name"><?php echo _("Gestion des langues"); ?></span><br />
						<span class="view"><?php print $filterLng!=='all' && trim($filterLng) ? i18n_languages_get_name( $filterLng ) : _('Toutes les langues'); ?></span>
					</div>
                    <a name="btn" class="btn">
                    	<img width="16" height="8" alt="" src="/admin/images/stats/fleche.gif" />
                    </a>
                    <div class="clear"></div>
				</div>
                <div class="selector">
					<a name="p-all"><?php echo _("Toutes les langues"); ?></a><?php
					
                	foreach( $filterLngs as $code=>$name ){
						print '<a name="p-'.$code.'">'.htmlspecialchars( $name ).'</a>';
					}
				?></div>
			</div>
			<?php }?>
			<div class="clear"></div>
			<div id="url-content">
				<label for="filter-url"><?php echo _("URL contenant :"); ?></label>
				<input type="text" name="filter-url" id="filter-url" onkeypress="if( event.keyCode==13 ) return false;" value="<?php print isset($_GET['filter']) ? $_GET['filter'] : ''; ?>" />
			</div>
			<div id="riaothersfilters">
				<input type="checkbox" name="no-redirection" id="no-redirection" value="1" <?php print $no_redirection ? 'checked="checked"' : ''; ?> />
				<label for="no-redirection"><?php echo _("Limiter aux URLs n'ayant pas encore de redirection"); ?></label>
				<div class="clear"></div>
				<input type="checkbox" name="resolved" id="resolved" value="1" <?php print $resolved ? 'checked="checked"' : ''; ?> />
				<label for="resolved"><?php echo _("N’afficher que les URLs marquées comme résolues"); ?></label>
			</div>
			
			
			
		</div>
		
		<?php $colspan = 2; ?>
		<?php
			$head_colspan = $colspan + 1;
			if( ria_mysql_num_rows($website)>1 && (!isset($wst_id) || $wst_id<=0) ){
            	$head_colspan++;
			}
			if( isset($config['i18n_lng_used']) && is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used']) > 1 ){
            	$head_colspan++;
			}
		?>
		<table class="checklist" id="lst-error-404">
			<caption><?php echo _("URLs ayant généré une ou plusieurs erreurs 404"); ?></caption>
			<thead class="thead-none">
				<tr>
					<th id="url"><?php echo _("Adresse URL"); ?></th>
					<th id="count" class="align-right"><?php echo _("Occurences"); ?></th>
					<th id="redirection"><?php echo _("Rediriger vers"); ?></th>
                    <?php 
						if( ria_mysql_num_rows($website)>1 && (!isset($wst_id) || $wst_id<=0) ){
							print '<th id="site">' . _("Site") . '</th>';
							$colspan++;
						}

						if( isset($config['i18n_lng_used']) && is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used']) > 1 ){
							print '<th id="lng">' . _("Version") . '</th>';
							$colspan++;
						}
					?>
				</tr>
			</thead>
			<tbody data-colspans="<?php print $head_colspan ?>"><?php
				print '<tr><td colspan="'.$head_colspan.'" style="padding:5px;"><img class="loader" src="/admin/images/stats/loader.gif" alt="loader"/>&nbsp;' . _("Chargement en cours...") .'</td></tr>';
			?></tbody>
			<tfoot>
				<tr>
					<td id="pagination" colspan="<?php print $head_colspan ; ?>" class="right">
					</td>
				</tr>
				<tr>
					<td colspan="<?php print $head_colspan ; ?>" class="right">
						<input type="submit" name="save" id="save" value="<?php echo _("Enregistrer"); ?>" />
						<?php /*if($config['USER_RIASTUDIO']){ ?>
						<input type="submit" name="control" id="control" value="Contrôler" title="Vérifier que les urls signalées en erreur 404 le sont toujours." />
						<?php }*/ ?>
					</td>

				</tr>

			</tfoot>
		</table>
		<div class="notice">
			<?php echo _("Pour rediriger une URL ayant généré une erreur 404 vers la page d'accueil de votre site, indiquez / dans le champ \"Rediriger vers\""); ?>.
		</div>
	</form>
	<div id="popup_ria" class="maxipopup"><div class="popup_ria_drag"></div><div class="content"></div></div>

	<script><!--
		<?php view_date_initialized( 0, '/admin/config/redirections/errors/errors-404.php', false, array('refresh_in_ajax'=>false, 'autoload'=>true) ); ?>
	//--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>