<?php

// \cond onlyria

/** 
 * \defgroup api-devices-refresh Rafraichissement 
 * \ingroup Yuto 
 * @{		
 * \page api-devices-refresh-get Chargement 
 *
 * Cette fonction récupérer les élements à synchroniser 
 *
 *	\code
 *		GET /devices/refresh/
 *	\endcode
 *
 * @param cls_id obligatoire : identifiant de la classe
 * @param obj_id facultatif : identifiant du 1er élément à vérifier 
 * @param obj_id_1 facultatif : identifiant du 2ème élément à vérifier 
 * @param obj_id_2 facultatif : identifiant du 3ème élément à vérifier 
 * 
 * @return array Les données à synchroniser pour la classe.
 *		Ces données sont données sous forme d'un tableau (une ligne par élément), chaque élément contient les clés suivantes :
 *			- id : identifiant 1
 *			- id_1 : identifiant 2
 *			- id_2 : identifiant 3
 *			- content : Les informations et contenus relatifs à l'objet. Le détail dépend de la classe.
 * @}
*/

	switch( $method ){
		// récupère les élements à synchroniser
		case 'get':

			if( !isset($_GET['cls_id']) ){
				throw new Exception("Le paramètre CLS_ID est manquant ou incorrect");
			}

			$check_id = 0;
			// si des id sont passé en paramètre on parcours les éléments jusq'au bout pour savoir si une modification à eu lieu
			if( isset($_GET['obj_id']) ){
				$check_id = $_GET['obj_id'];

				if( isset($_GET['obj_id_1']) ){
					$check_id = array( $_GET['obj_id'], $_GET['obj_id_1'] );
				}
				if( isset($_GET['obj_id_2']) ){
					$check_id = array( $_GET['obj_id'], $_GET['obj_id_1'], $_GET['obj_id_2'] );
				}
			}

			switch($_GET['cls_id']){
				case CLS_PRODUCT :

					$rprd = prd_products_get($_GET['obj_id']);
					if( $rprd && ria_mysql_num_rows($rprd) ){
						$prd  = ria_mysql_fetch_assoc($rprd);

						// recherche les stocks liée à ce produit
						$data_stock = array();
						$rsto = prd_dps_stocks_get($_GET['obj_id']);
						if( $rsto && ria_mysql_num_rows($rsto) > 0 ){
							while( $sto = ria_mysql_fetch_assoc($rsto) ){
								$data_stock[] = dev_devices_get_object(CLS_STOCK, array($sto['prd'], $sto['dps_id']));
							}
						}

						// recherche les prix liée à ce produit
						$data_prc = array();
						$rprc = prc_prices_get( 0, 0, false, false, false, $prd['id'] );
						if( $rprc && ria_mysql_num_rows($rprc) > 0 ){
							while( $prc = ria_mysql_fetch_assoc($rprc) ){
								$data_prc[] = dev_devices_get_object(CLS_PRICE, $prc['id']);
							}
						}

						// recherche les relations du produits
						$data_rel = array();
						$rrel = prd_relations_get($prd['id']);
						if( $rrel && ria_mysql_num_rows($rrel) ){
							while( $rel = ria_mysql_fetch_assoc($rrel) ){
								$data_rel[] = dev_devices_get_object(CLS_PRD_RELATIONS, array($rel['src_id'], $rel['dst_id'], $rel['type_id']));
							}
						}


						$content = array(
								array('cls' => CLS_PRODUCT, 'content' => array(dev_devices_get_object(CLS_PRODUCT, $check_id))),
								array('cls' => CLS_STOCK, 'content' => $data_stock),
								array('cls' => CLS_PRICE, 'content' => $data_prc),
								array('cls' => CLS_PRD_RELATIONS, 'content' => $data_rel)
							);
								/*
								CLS_TVA => array("data" => ),
								CLS_PRD_RELATIONS => array("data" => )
								CLS_SELL_UNIT => array("data" => )
								CLS_BRAND => array("data" => )
								CLS_DEPOSIT => array("data" => )
								CLS_IMAGE => array("data" => )
								CLS_COLISAGE => array("data" => )
								*/

					}
					break;
				default:
					$content = array(
							array('cls' => $_GET['cls_id'], 'content' => array(dev_devices_get_object($_GET['cls_id'], $check_id) ))
						);
					break;
			}
			$result = true;

			break;
	}

// \endcond