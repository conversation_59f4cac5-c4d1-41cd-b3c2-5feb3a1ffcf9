# OrderListFullLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**harvest** | [**\Swagger\Client\Model\LinksHarvestAllLink**](LinksHarvestAllLink.md) |  | 
**set_merchant_infos** | [**\Swagger\Client\Model\LinksSetMerchantOrderInfoListLink**](LinksSetMerchantOrderInfoListLink.md) |  | 
**clear_merchant_infos** | [**\Swagger\Client\Model\LinksClearMerchantOrderInfoListLink**](LinksClearMerchantOrderInfoListLink.md) |  | 
**export** | [**\Swagger\Client\Model\LinksExportOrdersLink**](LinksExportOrdersLink.md) |  | 
**status** | [**\Swagger\Client\Model\LinksGetMarketplaceAccountsSynchronizationLink**](LinksGetMarketplaceAccountsSynchronizationLink.md) |  | 
**self** | [**\Swagger\Client\Model\LinksGetOrderListFullLink**](LinksGetOrderListFullLink.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


