<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/check_error.proto

namespace Google\Api\Servicecontrol\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Servicecontrol\V1\CheckError\Code instead.
     * @deprecated
     */
    class CheckError_Code {}
}
class_exists(CheckError\Code::class);
@trigger_error('Google\Api\Servicecontrol\V1\CheckError_Code is deprecated and will be removed in the next major release. Use Google\Api\Servicecontrol\V1\CheckError\Code instead', E_USER_DEPRECATED);

