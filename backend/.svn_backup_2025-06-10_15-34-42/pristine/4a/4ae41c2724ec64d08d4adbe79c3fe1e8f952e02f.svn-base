<?php

	/**	\file popup-address.php
	 *	Cette popup permet la modification d'une adresse de facturation / livraison associée à un compte client.
	 *	Elle est accessible depuis l'onglet "Adresses" de la fiche client.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

	// Vérifie que l'utilisateur demandé existe
	if( !isset($_GET['usr']) || !gu_users_exists($_GET['usr']) ){
		define('ADMIN_HEAD_POPUP', true);
        header('HTTP/1.0 404 Not Found');
        include('../errors/404.php');
        exit;
	}

	// Vérifie que l'adresse demandée existe
	if( !isset($_GET['adr']) ){
		$_GET['adr'] = 0;
	}
	if( $_GET['adr'] > 0 && !gu_adresses_exists($_GET['adr']) ){
		header('Location: /admin/views/customers/tabs/adresses.php?carnet=1&usr='.$_GET['usr'].'&popup=1&ord='.$_GET['ord'].(isset($_GET['dlv-adr']) ? '&dlv-adr=1' : ''));
		exit;
	}

	$ord_id = null;
	if (isset($_GET['ord']) && ord_orders_exists($_GET['ord'])) {
		$ord_id = $_GET['ord'];
	}

	$is_dlv_adr = isset($_GET['dlv-adr']);
	
	// Gère le bouton "Enregistrer"
	if( isset($_POST['save-main']) ){

		// Vérifie que tous les champs demandés ont été envoyés
		if( $_POST['type']==1 ){ // Particulier
			if( !isset($_POST['title'],$_POST['firstname'],$_POST['lastname']) ){
				$error = 1;
			}
		}elseif( $_POST['type']==2 ){ // Professionnel
			if( !isset($_POST['society'],$_POST['siret']) ){
				$error = 1;
			}
		}elseif( $_POST['type']==3 ){
			if( !isset($_POST['title'],$_POST['firstname'],$_POST['lastname'],$_POST['society'],$_POST['siret']) ){
				$error = 1;
			}
		}
		if( !isset($error) && !isset($_POST['country']) ){
			$error = 1;
		}
		// Vérifie le SIRET
		if( !isset($error) && isset($_POST['siret']) && trim($_POST['siret']) ){
			$siret = str_replace(' ', '', $_POST['siret']);
			if( !validSIRET($siret) ){
				$error = 10;
			}
		}

		// Vérifie que les informations fournies sont valides
		if( !isset($error) ){
			if( $_POST['type']==1 ){ // Particulier
				if( !gu_titles_exists($_POST['title']) ){
					$error = 2;
				}
			}elseif( $_POST['type']==2 ){ // Professionnel
				if( !trim($_POST['society']) ){
					$error = 2;
				}
			}elseif( $_POST['type']==3 ){ // Société
				if( !gu_titles_exists($_POST['title']) || !trim($_POST['society']) ){
					$error = 2;
				}
			}
		}

		// Enregistre les modifications
		if( !isset($error) ){
			if( $_GET['adr']>0 ){
				if( $_POST['type']==1 ){ // Particulier
					if( !gu_adresses_update($_GET['usr'],$_GET['adr'],$_POST['type'],$_POST['title'],$_POST['firstname'],$_POST['lastname'],'','',$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['tel'],$_POST['fax'],$_POST['mobile'],$_POST['phone-work'], false, $_POST['desc']) ){
						$error = 3;
					}
				}elseif( $_POST['type']==2 ){ // Professionnel
					if( !gu_adresses_update($_GET['usr'],$_GET['adr'],$_POST['type'],'','','',$_POST['society'],$_POST['siret'],$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['tel'],$_POST['fax'],$_POST['mobile'],$_POST['phone-work'], false, $_POST['desc']) ){
						$error = 3;
					}
				}elseif( $_POST['type']==3 ){ // Société
					if( !gu_adresses_update($_GET['usr'],$_GET['adr'],$_POST['type'],$_POST['title'],$_POST['firstname'],$_POST['lastname'],$_POST['society'],$_POST['siret'],$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['tel'],$_POST['fax'],$_POST['mobile'],$_POST['phone-work'], false, $_POST['desc']) ){
						$error = 3;
					}
				}
			}elseif( $_GET['adr']==0 ){
				if( $_POST['type']==1 ){ // Particulier
					$_GET['adr'] = gu_adresses_add($_GET['usr'],$_POST['type'],$_POST['title'],$_POST['firstname'],$_POST['lastname'],'','',$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['tel'],$_POST['fax'],$_POST['mobile'],$_POST['phone-work'], false, $_POST['desc']);
				}elseif( $_POST['type']==2 ){ // Professionnel
					$_GET['adr'] = gu_adresses_add($_GET['usr'],$_POST['type'],'','','',$_POST['society'],$_POST['siret'],$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['tel'],$_POST['fax'],$_POST['mobile'],$_POST['phone-work'], false, $_POST['desc']);
				}elseif( $_POST['type']==3 ){ // Société
					$_GET['adr'] = gu_adresses_add($_GET['usr'],$_POST['type'],$_POST['title'],$_POST['firstname'],$_POST['lastname'],$_POST['society'],$_POST['siret'],$_POST['address1'],$_POST['address2'],$_POST['zipcode'],$_POST['city'],$_POST['country'],$_POST['tel'],$_POST['fax'],$_POST['mobile'],$_POST['phone-work'], false, $_POST['desc']);
				}
				if( !$_GET['adr'] ){
					$error = 3;
				}
			}
		}
		
		// Définit le message d'erreur en fonction du code utilisé
		if( isset($error) ){
			switch( $error ){
				case 1:
					$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées.")."\n"._("Veuillez vérifier.");
					break;
				case 2:
					$error = _("Une ou plusieurs informations obligatoires ne sont pas renseignées ou invalides.")."\n"._("Veuillez vérifier. Les champs marqués d'une <span class=\"mandatory\">*</span> sont obligatoires.");
					break;
				case 3:
					$error = _("Une erreur inattendue s'est produite lors de la modification de l'adresse de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
					break;
				case 4:
					$error = _("Une erreur inattendue s'est produite lors de la modification de l'adresse email de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
					break;
				case 5:
					$error = _("Veuillez confirmer le nouveau mot de passe en le saisissant à nouveau.");
					break;
				case 6:
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de votre nouveau mot de passe.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur");
					break;
				case 7:
					$error = _("Les deux mots de passe saisis sont différents.")."\n"._("Veuillez les saisir à nouveau.");
					break;
				case 8:
					$error = _("Une erreur inattendue s'est produite lors de la modification du code client de l'utilisateur.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
					break;
				/*case 9:
					$error = _("Le champ code client doit être renseigné.");*/
				case 10:
					$error = _("La valeur saisie pour le numéro de SIRET est incorrecte");
					break;
				case 11:
					$error = _('Le mot de passe doit contenir un minimum de 6 caractères pour être accepté');
					break;
				case 12:
					$error = _('Le mot de passe saisi n\'est pas reconnu comme valide. Les caractères autorisés sont les suivants : toutes les lettres, les chiffres, le caractère de soulignement, le tiret et le signe égal.');
					break;
				case 13 :
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des coordonnées géographiques.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous le signaler.");
					break;
				case 14 :
					$error = _("Ce code client est déjà attribué, veuillez en choisir un autre.");
					break;
			}
		}
	}
	
	// Charge le compte client
	$rusr = gu_users_get( $_GET['usr'] );
	$usr = ria_mysql_fetch_array( $rusr );
	
	// Charge l'adresse
	if( isset($_GET['adr']) && $_GET['adr']>0 ){
		$radr = gu_adresses_get( $_GET['usr'], $_GET['adr'] );
		$adr = ria_mysql_fetch_array( $radr );
	}else{
		$adr = array(
			'id' => 0,
			'type_id' => 1,
			'title_id' => 1,
			'society' => '',
			'siret' => '',
			'firstname' => '',
			'lastname' => '',
			'address1' => '',
			'address2' => '',
			'zipcode' => '',
			'city' => '',
			'country' => $usr['country'],
			'phone' => '',
			'fax' => '',
			'mobile' => '',
			'phone_work' => '',
			'description' => ''
		);
	}
	
	// Détermine s'il s'agit de l'adresse de facturation ou bien d'une adresse de livraison
	if( $usr['adr_invoices']==$adr['id'] ){
		$quality = 'facturation';
	}else{
		$quality = 'livraison';
	}
	
	// Gère le bouton "Masquer"
	$masked = false;
	if( isset($_POST['hide-main']) ){
		if( gu_adresses_set_masked( $_GET['adr'] ) ){
			// Fermer la popup
			$masked = true;
		}else{
			if( $quality=='facturation' ){
				$error = _('Il n\'est pas possible de masquer une adresse de facturation, cette fonctionnalité est limitée aux adresses de livraison');
			}else{
				$error = _('Une erreur inattendue s\'est produite lors du masquage de cette adresse. Veuillez réessayer ou prendre contact avec nous pour nous signaler cette erreur.');
			}
		}
	}
	
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	define('ADMIN_PAGE_TITLE', _('Adresse'));
	define('ADMIN_NO_MOBILE_STYLE', true);
	require_once('admin/skin/header.inc.php');

	// Affichage de l'éventuel message d'erreur
	if( isset($error) && $error!='' ){
		echo '<div class="error">'.htmlspecialchars( $error ).'</div>';
	}elseif( isset($_POST['save-main']) || isset($_POST['hide-main']) ){
		if( isset($_GET['popup']) && !isset($_POST['hide-main']) ){
			if (isset($_GET['reloadord'])) {
				print '<script>parent.update_order_user( '.$_GET['usr'].', '.$ord_id.' );</script>';
			}
			print '
				<script>
					document.location.href="/admin/views/customers/tabs/adresses.php?carnet=1&popup=1&usr=' . $_GET['usr'] . ($ord_id ? '&ord=' . $ord_id : '') . ($is_dlv_adr ? '&dlv-adr=1' : '' ) . '";
				</script>
			';
			exit;
		}elseif( isset($_POST['hide-main']) ){
			print '
				<script>
					window.parent.hidePopup();
				</script>
			';
			exit;
		}else{
			print '
				<script>
					window.parent.location.reload();
				</script>
			';
			exit;
		}
	} 
	
	if( !isset($_GET['is-dlv']) || $_GET['is-dlv']!=1 ){ ?>
	<h2><?php print view_usr_is_sync($usr).' '. _('Adresse de ').htmlspecialchars($quality).' : ';  print is_numeric($usr['adr_invoices']) ? htmlspecialchars(gu_users_get_name($_GET['usr'])) : ''; ?></h2>
<?php

	if (isset($_GET['reloadord'])) {
		?><div class="notice cancel-main"><?php print _("Vous pouvez choisir une autre adresse en cliquant sur le lien suivant :"); ?> <a href="#"><?php print _("Voir le carnet d'adresse"); ?></a>.</div><?php
	}
?>
	<form action="/admin/customers/popup-address.php?usr=<?php print $_GET['usr']; ?>&adr=<?php print $_GET['adr']; ?><?php print (isset($_GET['popup'])? '&popup=1' : '') . ($ord_id ? '&ord=' . $ord_id : '') . ($is_dlv_adr ? '&dlv-adr=1' : '') . (isset($_GET['reloadord']) ? '&reloadord=1' : ''); ?>" method="post">
	
		<table id ="table-fiche-client">
			<tbody>
				<tr>
					<td id="td-fiche-client-1"><label for="type"><?php print _('Type d\'adresse :'); ?></label></td>
					<td id="td-fiche-client-2">
						<select name="type" id="type" onchange="switch_adr_type(this.form)" onkeyup="switch_adr_type(this.form)">
						<?php	
							$types = gu_adr_types_get();
							while( $t = ria_mysql_fetch_array($types) ){
								print '<option value="'.$t['id'].'"'.( $t['id']==$adr['type_id'] ? ' selected="selected"':'' ).'>'.htmlspecialchars($t['name']).'</option>';
							}
						?>
						</select>
					</td>
				</tr>
				<tr id="adr-civ"<?php if( $adr['type_id']==2 ) print ' class="none"'; ?>>
					<td><label><span class="mandatory">*</span> <?php print _('Civilité :'); ?></label></td>
					<td>
					<?php
						$titles = gu_titles_get();
						while( $r = ria_mysql_fetch_array($titles) ){
							print '<input type="radio" class="radio" name="title" id="title-'.$r['id'].'" value="'.$r['id'].'" '.( $adr['title_id']==$r['id'] ? ' checked="checked"':'').'/> 
							<label class="inline" for="title-'.$r['id'].'">'.htmlspecialchars($r['name']).'</label> ';
						}
					?>
					</td>
				</tr>
				<tr id="adr-firstname"<?php if( $adr['type_id']==2 ) print ' class="none"'; ?>>
					<td><label for="firstname"><?php print _('Prénom :'); ?></label></td>
					<td><input type="text" name="firstname" id="firstname" value="<?php print htmlspecialchars($adr['firstname']); ?>" maxlength="75" /></td>
				</tr>
				<tr id="adr-lastname"<?php if( $adr['type_id']==2 ) print ' class="none"'; ?>>
					<td><label for="lastname"><?php print _('Nom de famille :'); ?></label></td>
					<td><input type="text" name="lastname" id="lastname" value="<?php print htmlspecialchars($adr['lastname']); ?>" maxlength="75" /></td>
				</tr>
				<tr id="adr-society"<?php if( $adr['type_id']==1 ) print ' class="none"'; ?>>
					<td><label for="society"><span class="mandatory">*</span> <?php print _('Société :'); ?></label></td>
					<td><input type="text" name="society" id="society" value="<?php print htmlspecialchars($adr['society']); ?>" maxlength="75" /></td>
				</tr>
				<tr id="adr-siret"<?php if( $adr['type_id']==1 ) print ' class="none"'; ?>>
					<td><label for="siret"><?php print _('SIRET :'); ?></label></td>
					<td><input type="text" name="siret" id="siret" value="<?php print formatSiret($adr['siret']); ?>" maxlength="17" size="17" />
					</td>
				</tr>
				<tr>
					<td><label for="address1"><?php print _('No et rue :'); ?></label></td>
					<td><input type="text" name="address1" id="address1" maxlength="75" value="<?php print htmlspecialchars($adr['address1']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="address2"><?php print _('Complément :'); ?></label></td>
					<td><input type="text" name="address2" id="address2" maxlength="75" value="<?php print htmlspecialchars($adr['address2']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="zipcode"><?php print _('Code postal :'); ?></label></td>
					<td><input type="text" name="zipcode" id="zipcode" class="zipcode" maxlength="9" value="<?php print htmlspecialchars($adr['zipcode']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="city"><?php print _('Ville :'); ?></label></td>
					<td><input type="text" name="city" id="city" maxlength="75" value="<?php print htmlspecialchars($adr['city']); ?>" /></td>
				</tr>
				<tr>
					<td><label for="country"><?php print _('Pays :'); ?></label></td>
					<td>
						<select name="country" id="country">
							<option value="">&nbsp;</option>
							<?php
								$countries = sys_countries_get();
								while( $c = ria_mysql_fetch_array($countries) ){
									print '<option value="'.htmlspecialchars($c['name']).'"'.( strtoupper($c['name'])==strtoupper($adr['country']) ? ' selected="selected"':'' ).'>'.htmlspecialchars($c['name']).'</option>';
								}
							?>
						</select>				
					</td>
				</tr>		
				<tr><th colspan="2"><?php print _('Contact')?></th></tr>
				<tr><td><label for="tel"><?php print _('Téléphone :'); ?></label></td><td><input type="text" id="tel" name="tel" value="<?php print htmlspecialchars($adr['phone']); ?>" /></td></tr>
				<tr><td><label for="fax"><?php print _('Fax :'); ?></label></td><td><input type="text" id="fax" name="fax" value="<?php print htmlspecialchars($adr['fax']); ?>" /></td></tr>
				<tr><td><label for="mobile"><?php print _('Portable :'); ?></label></td><td><input type="text" id="mobile" name="mobile" value="<?php print htmlspecialchars($adr['mobile']); ?>" /></td></tr>
				<tr><td><label for="phone-work"><?php print _('Téléphone en journée :'); ?></label></td><td><input type="text" id="phone-work" name="phone-work" value="<?php ( $adr['phone_work']!='' ? print htmlspecialchars($adr['phone_work']) : '' ); ?>" /></td></tr>
				<tr><th colspan="2"><?php print _('Description')?></th></tr>
				<tr><td><label for="desc"><?php print _('Commentaires / Notes :'); ?></label></td><td><textarea name="desc" id="desc"><?php print htmlspecialchars( $adr['description'] ); ?></textarea></td></tr>
			<tbody>	
			<tfoot>
				<tr>
				<td colspan="2">
					<?php if( $quality=='livraison' && $_GET['adr']>0 ){ ?>
					<input type="submit" name="hide-main" id="hide-main" value="<?php print _('Masquer')?>" title="<?php print _('Masquer cette adresse')?>" class="float-left" />
					<?php } ?>
					<input type="submit" name="save-main" id="save-main" value="<?php print _('Enregistrer')?>" title="<?php print _('Enregistrer les modifications')?>" />
					<input class="float-none" type="button" name="cancel-main" id="cancel-main" value="<?php print _('Annuler')?>" title="<?php print _('Annuler les modifications')?>" onclick="return parent.hidePopup();" />
				</td></tr>
			</tfoot>
		</table>

	</form>
	<?php } ?>
	<script src="/admin/js/customers/index.js"></script>
	<script><!--
		
		<?php 
		if( isset($_GET['is-dlv']) && $_GET['is-dlv']==1 ){  ?>
			cancel(); <?php
		}
		?>

		$('#cancel-main').click(function(e){
			<?php if (isset($_GET['reloadord'])) {
				?>parent.hidePopup();<?php
			} else {
				?>cancel();<?php
			}?>
		});

		$('.notice.cancel-main a').click(function(){
			cancel();
		});

		function cancel() {
			<?php if( isset($_GET['popup']) ){ ?>
				$.ajax({
					url: '/admin/views/customers/tabs/adresses.php?usr=<?php echo $_GET['usr']; ?>&popup=1<?php print ($ord_id ? '&ord=' . $ord_id : '').($is_dlv_adr ? '&dlv-adr=1' : '').(isset($_GET['reloadord']) && $_GET['reloadord'] ? '&reloadord=1' : '') ?>',
					success: function (response){
						$('body').html(response);
					},
				});
			<?php }else{ ?>
				window.parent.$.fancybox.close();
			<?php } ?>
			
			return false;
		}

		<?php
		if( $masked ){ 
			if( isset($_GET['popup']) ){
				?>
				$.ajax({
					url: '/admin/views/customers/tabs/adresses.php?usr=<?php echo $_GET['usr']; ?>&popup=1',
					success: function (response){
						$('body').html(response);
					},
				});
				<?
			}else{ ?>
				window.parent.$('#address-<?php print $adr['id']; ?>', window.parent.document).hide();
				window.parent.$.fancybox.close();
		<?php } 
		}?>

	--></script>
<?php 
	require_once('admin/skin/footer.inc.php');
?>