<?php

/**	\file img_actions.php
 * 
 *	Ce fichier permet le rafraichissement de la  liste d'images
 *
 */

// Vérifie que l'utilisateur en cours peut accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

$json = array();
if( !isset($_GET['obj']) || !is_array($_GET['obj']) || !control_array_integer($_GET['obj'], false, true)){
	print _('Il manque des paramètres');
	exit;
}else{
	$data_obj = array_pad($_GET['obj'], 3, 0);
}

// Suppression d'une ou plusieurs images
if( isset($_GET['delimg']) ){
	//supprime les images non publiées
	if(isset($_GET['imgs-unpublish']) && is_array($_GET['imgs-unpublish'])){
		foreach( $_GET['imgs-unpublish'] as $img ){
			switch( $_GET['classe'] ){
				case CLS_PRODUCT: 
					prd_images_del( $data_obj[0], $img );
					break;
			}
		}
	}
	//dépublie les images
	if(isset($_GET['imgs']) && is_array($_GET['imgs'])){
		foreach( $_GET['imgs'] as $img ){
			switch( $_GET['classe'] ){
				case CLS_PRODUCT: 
					prd_images_set_publish( $data_obj[0], $img, false );
					break;
				case CLS_NEWS: 
					news_images_del( $img, $data_obj[0] );
					break;
				case CLS_CMS: 
					cms_images_del( $img, $data_obj[0] );
					break;
				case CLS_CATEGORY: 
					prd_cat_images_del( $data_obj[0], $img );
					break;
				case CLS_CTR_MODELS:
					ctr_images_del( $img, $data_obj[0] );
					break;
				case CLS_CTR_MKT:
					ctr_catalogs_images_del( $data_obj[0], $data_obj[1], $img );
					break;
				case CLS_FAQ_CAT: 
					faq_cat_images_del( $data_obj[0], $img );
					break;
				case CLS_FAQ_QST: 
					faq_qst_images_del( $data_obj[0], $img );
					break;
				case CLS_DOCUMENT: 
					doc_images_del( $data_obj[0], $img );
					break;
				case CLS_TYPE_DOCUMENT: 
					doc_types_images_del( $data_obj[0], $img );
					break;
			}
		}
	}

}else if(isset($_GET['delObjImg'] ) && $_GET['delObjImg']){
	foreach( $_GET['objs'] as $obj ){
		if (!img_images_objects_del(0, 0, 0, $obj, $data_obj[0], $data_obj[1], $data_obj[2])) {
			break;
		}
	}

	if (isset($json['error'])) {
		header('HTTP/1.1 400 Bad Request');
		$json['error'] = _('une erreur s\'est produite lors de la suppression d\'une image.');
	}else{
		header('HTTP/1.1 200 OK');
		$json['message'] = _('Les images ont bien été supprimées.');
	}
}


switch( $_GET['classe'] ){
	case CLS_PRODUCT: 
		// Suppression de l'image principale du produit
		if( isset($_GET['delimg']) && isset($_GET['del-img-main']) && $_GET['del-img-main'] == 1){
			prd_images_main_del($data_obj[0]);
		}

		// si le produit n'a pas d'image principal on prend la première des secondaires
		$prd = ria_mysql_fetch_array(prd_products_get($data_obj[0]));
		if( !$prd['img_id'] ){
			// calcul le nombre d'image du produit 
			$rimgs = prd_images_get( $prd['id'], 0, false, true);	
			if( $rimgs ){
				$new_principal = ria_mysql_fetch_array( $rimgs );
				
				if( !prd_images_main_add_existing( $prd['id'], $new_principal['id'] ) ){
					$error = _("Une erreur inattendue s'est produite lors du changement d'image principal.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}
				// retire l'image des secondaires
				prd_images_del($prd['id'], $new_principal['id'] );
			}
		}
		
		break;
}

// Permet d'utiliser le nouveau module d'affichage avec les objets d'images typés
if (empty($json)) {
	if ( isset($_GET['obj_type_id']) && $_GET['obj_type_id'] > 0) {
		print view_admin_img_objects_list( $_GET['classe'], $data_obj[0], $data_obj[1], $data_obj[2], $_GET['obj_type_id'], (isset($_GET['in_popup']) ? true : false) );
	}else if(isset($_GET['classe'])){// Permet de conserver l'ancien système
		if($_GET['classe'] == CLS_PRODUCT){
			$html['publish']= view_admin_img_list( $_GET['classe'], $data_obj[0], false, (isset($_GET['in_popup']) ? true : false), true);
			$html['unpublish']= view_admin_img_list( $_GET['classe'], $data_obj[0], false, (isset($_GET['in_popup']) ? true : false), false);
			print json_encode($html);
		}else{
			print view_admin_img_list( $_GET['classe'], $data_obj[0], false, (isset($_GET['in_popup']) ? true : false));
		}
	}
}else{
	print json_encode($json);
}