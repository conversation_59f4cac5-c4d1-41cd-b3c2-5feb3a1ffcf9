<?php
// \cond onlyria
/** \defgroup model_related Produits ayant également été commandés par les internautes
 *	\ingroup products pieces
 *	Ce module comprend les fonctions nécessaires à la gestion des produits apparentés ayant également été commandés par les internautes.
 *
 *	Il est composé de deux parties :
 *		- des fonctions permettant le précalcul des informations, à exécuter de manière régulière pour les actualiser
 *		- des fonctions permettant leur utilisation dans le cadre d'une boutique en ligne
 *
 *	Les paramètres actuellement pris en compte pour le calcul sont les suivants :
 *		- le produit apparenté doit déjà avoir été commandé avec l'article
 *		- le nombre de commandes regroupant les deux produits
 *		- la quantité commandé
 *
 *	Seuls les produits publiés sont concernés par ces fonctions.
 *
 *	@{
 */
// \endcond

// Nombre maximum de produits à recommander comme "Ayant été commandés également..."
define( 'ORD_RELATED_LIMIT', 10 );

require_once('products.inc.php');
require_once('prd.stocks.inc.php');

/** \ingroup models_cross_selling */
/**	Cette fonction calcule la liste des produits ayant déjà été commandés en même temps qu'un article donné et met à jour
 *	la table utilisée comme cache. La sélection est effectuée sur le nombre d'occurences. La quantité utilisée
 *	à chaque commande n'est actuellement pas prise en compte bien que cette donnée soit également intéressante.
 *
 *	Pour éviter trop de redondance, les articles déjà liés par des relations de type parent/enfant sont exclues du résultat.
 *
 *	@param int $prd Identifiant du produit à actualiser.
 *
 *	@return bool False en cas d'échec.
 *	@return bool True en cas de succès mais si les relations du produit n'ont pas été modifiées.
 *	@return int L'identifiant du produit si ses relations ont été modifiées.
 *
 */
function ord_related_refresh_product( $prd ){

	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	$dps = prd_deposits_get_main();

	global $config;

	// charge les relations actuelles du produit
	$sql = '
		SELECT rel_rel_id AS "rel", rel_weight AS "weight"
		FROM ord_related
		WHERE rel_tnt_id = '.$config['tnt_id'].' AND rel_prd_id = '.$prd.' and rel_is_deleted=0
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	$rel_ar = array();
	while( $data = ria_mysql_fetch_assoc($res) ){
		$rel_ar[ $data['rel'] ] = $data['weight'];
	}

	// charge les relations actualisées
	$sql = '
		SELECT
			p.prd_id AS "rel", count(*) AS "weight"
		FROM
			prd_products AS p
			JOIN ord_products AS p2 ON p.prd_tnt_id = p2.prd_tnt_id AND p.prd_id = p2.prd_id
			JOIN ord_products AS p1 ON p2.prd_tnt_id = p1.prd_tnt_id AND p2.prd_ord_id = p1.prd_ord_id AND p2.prd_id != p1.prd_id
			JOIN ord_orders AS o ON p2.prd_tnt_id = o.ord_tnt_id AND p2.prd_ord_id = o.ord_id
		WHERE
			p.prd_tnt_id = '.$config['tnt_id'].'
			AND p1.prd_id = '.$prd.'
			AND p.prd_date_deleted IS NULL
			AND p.prd_publish = 1 AND p.prd_publish_cat = 1
			AND (
				p.prd_sleep = 0 OR IFNULL((
					SELECT ' . prd_stocks_get_sql() . ' - sto_prepa FROM prd_stocks
					WHERE sto_tnt_id = '.$config['tnt_id'].' AND sto_prd_id = p.prd_id AND sto_dps_id = '.$dps.' and sto_is_deleted=0
				), 0) > 0
			) AND NOT EXISTS (
				SELECT 1 FROM prd_hierarchy AS hry
				WHERE hry.prd_tnt_id = '.$config['tnt_id'].' AND hry.prd_parent_id = p1.prd_id AND hry.prd_child_id = p.prd_id
			) AND EXISTS (
				SELECT 1 FROM gu_users AS u
				WHERE u.usr_tnt_id = '.$config['tnt_id'].' AND u.usr_id = o.ord_usr_id
					AND u.usr_date_deleted IS NULL
					AND u.usr_prf_id NOT IN ('.PRF_ADMIN.', '.PRF_SELLER.', '.PRF_SUPPLIER.')
			) AND DATEDIFF(NOW(), o.ord_date) <= '.floor($config['delay_ord_related'] * (365/12)).'
		GROUP BY
			p.prd_id
		ORDER BY
			COUNT(*) DESC
		LIMIT
			0, '.ORD_RELATED_LIMIT.'
	';
	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	// marqueur de changement général
	$changes = false;

	// liste des produits "rel" à ne pas supprimer
	$p_managed = array();

	// insère ou met à jour les différences
	while( $data = ria_mysql_fetch_assoc($res) ){

		$p_managed[] = $data['rel'];

		// marqueur de changement détecté pour cette ligne
		$change_one = false;

		if( !isset($rel_ar[ $data['rel'] ]) ){
			// ligne à créer
			$change_one = true;
		}elseif( $rel_ar[ $data['rel'] ] != $data['weight'] ){
			// ligne à mettre à jour
			$change_one = true;
		}

		if( $change_one ){
			$sql = '
				REPLACE INTO ord_related
					(rel_tnt_id, rel_prd_id, rel_rel_id, rel_weight, rel_is_deleted)
				VALUES
					('.$config['tnt_id'].', '.$prd.', '.$data['rel'].', '.$data['weight'].', 0)
			';
			if( ria_mysql_query($sql) ){
				$changes = true;
			}
		}

	}

	// nettoie les relations n'ayant plus cours
	$p_to_del = array_diff( array_keys($rel_ar), $p_managed );
	if( sizeof($p_to_del) ){

		$sql = '
			UPDATE ord_related SET rel_is_deleted=1
			WHERE rel_tnt_id = '.$config['tnt_id'].' AND rel_prd_id = '.$prd.'
			AND rel_rel_id IN ('.implode(', ', $p_to_del).')  AND rel_is_deleted=0
		';
		if( ria_mysql_query($sql) ){
			$changes = true;
		}

	}

	return $changes ? $prd : true;

}

// \cond onlyria
/**	Cette fonction actualise la totalité du cache de produits associés de type "Fréquemment achetés ensemble". Elle est destinée à être lancée
 *	régulièrement.
 *
 *	@param bool $publish Optionnel, détermine si tous les produits ou seulement ceux publiés sont à recalculer.
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_related_refresh( $publish=true ){

	global $config;

	$products = prd_products_get_simple( 0, '', $publish, 0, false, false, false, false, array('childs' => true) );
	if( !$products ){
		return false;
	}

	$prd_updated_ar = array();

	while( $p = ria_mysql_fetch_assoc($products) ){
		$res = ord_related_refresh_product( $p['id'] );
		if( $res === false ){
			error_log('Echec de ord_related_refresh_product pour le locataire '.$config['tnt_id'].', produit '.$p['id'].'.');
		}elseif( $res !== true ){
			$prd_updated_ar[] = $res;
		}
	}

	return true;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement des produits associés à un autre sur la base des achats
 *	réalisés par les internautes.
 *	Si $rel_id est spécifié, les lignes sont chargées brutes sans vérifier l'existence du produit source, ou la publication.
 *	@param int $prd Obligatoire, Identifiant du produit dont on souhaite obtenir les produits associés (ou tableaux d'identifant).
 *	@param int $limit Facultatif, nombre maximum de résultats à retourner (-1 pour ne pas limiter).
 *	@param bool $with_price Facultatif, active ou désactive le caul du tarif du produit. Igoré si $rel_id spécifié.
 *	@param $rel_id Facultatif, Identifiant du produit destination
 *	@param $exclude Facultatif, Identifiant ou tableau d'identifiants de produit(s) à exclure du résultat
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : Identifiant du produit
 *		- ref : Référence du produit
 *		- name : Nom du produit
 *		- desc : Description du produit
 *		- desc-long : Description longue du produit
 *		- price_ht : Prix unitaire HT du produit (si $with_price)
 *		- tva_rate : taux de TVA du produit (si $with_price)
 *		- price_ttc : Prix unitaire TTC du produit (si $with_price)
 *		- publish : Produit publié oui / non
 *		- publish_cat : Catégorie du produit publiée oui / non
 *		- orderable : Produit commandable oui / non
 *		- childonly : Produit vendu uniquement en article lié oui / non
 *		- countermark : Produit en contre-marque oui / non
 *		- centralized : Produit centralisé oui / non
 *		- new : Produit nouveau oui / non
 *		- date_published : Date de publication au format FR
 *		- brd_id : Identifiant de la marque du produit
 *		- brd_title : Nom de la marque du produit
 *		- brd_img_id : Identifiant de l'image de la marque du produit
 *		- brd_url : URL interne vers la amrque du produit
 *		- weight : Poids brut du produit
 *		- length : Longueur du produit
 *		- width : Largeur du produit
 *		- height : Hauteur du produit
 *		- stock : Quantité en stock du produit
 *		- stock_res : Quantité réservé du produit
 *		- stock_com : Quantité en commande fournisseur du produit
 *		- stock_prepa : Quantité en préparation du produit
 *		- stock_livr : Date de prochain approvisionnement au format FR
 *		- keywords : Mots-clés rattachés au produit
 *		- sleep : Produit en sommeil oui / non
 *		- img_id : Identifiant de l'image principale du produit
 *		- date_created : Date de création au format FR
 *		- date_modified : Date de modification au format FR
 *		- new_rule : Règle d'apparition en tant que produit nouveau
 *		- is_sync : Produit synchronisé oui / non
 *		- destockage : Produit en déstockage oui / non
 *		- sell_weight : Produit vendu au poids Oui / Non
 *		- rel_weight : Nombre de commandes où les deux produits se retrouvent associés
 */
function ord_related_get( $prd, $limit=ORD_RELATED_LIMIT, $with_price=true, $rel_id=false, $exclude=0) {
	global $config;

	$exclude = control_array_integer( $exclude, false );
	if( $exclude === false ){
		return false;
	}

	if( $with_price ){
		// Paramètres dépendants de l'utilisateur en cours
		$user = isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0;

		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) ){
			// Permet la consultation sous l'identité d'une autre personne (réservé aux administrateurs et représentants)
			$user = $_SESSION['admin_view_user'];
		}elseif( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==4 && $config['tnt_id']==1 ){
			// les revendeurs Bigship ne voient pas leur prix via cette fonction
			$user = 0;
		}
		if( isset($_SESSION['usr_tnt_id']) && !$_SESSION['usr_tnt_id'] ){
			$user = 0;
		}

		$exempt = gu_users_is_tva_exempt( $user );
		$usr_holder = gu_users_get_prices_holder( $user );
		$prc = gu_users_get_prc( $usr_holder, true );
		$usr_ref = gu_users_get_ref( $usr_holder, true );
	}

	$prd = control_array_integer($prd);
	if ($prd === false) {
		return false;
	}

	$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();

	$sql = '
		select
			prd.prd_id as id, prd_ref as ref, prd_name as name,
			prd_desc as "desc", prd_desc_long as "desc-long",
	';
	if( $with_price ){
		$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

		$sql .= '
				get_price_ht( prd.prd_tnt_id, prd.prd_id, '.$usr_holder.', 1, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", prd.prd_brd_id, prd.prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'prd.prd_ecotaxe').' ) as price_ht,
				'.( $exempt ? '1' : 'get_tva( prd.prd_tnt_id, prd.prd_id, '.gu_users_get_accouting_category( $user, true ).', '.gu_users_get_cnt_code($user, true).' )' ).' as tva_rate,
		';
	}
	$sql .= '
			prd_publish as publish, prd_publish_cat as publish_cat,
			prd_orderable as orderable, prd_childonly as childonly,
			prd_countermark as countermark, prd_centralized as centralized,
			prd_new!="-1" and (prd_new="1" or datediff(now(),prd_date_created)<='.$config['prd_new_days'].') as new,
			date_format(prd_date_published,"%d/%m/%Y à %H:%i") as date_published,
			brd_id, if(brd_title!="",brd_title,brd_name) as brd_title, brd_img_id, brd_url,
			prd_weight as weight, prd_length as "length", prd_width as width, prd_height as height,
			'.($config['use_decimal_qte'] ? prd_stocks_get_sql() . '-sto_prepa' : 'cast(' . prd_stocks_get_sql() . '-sto_prepa as signed)' ).' as stock,
			'.($config['use_decimal_qte'] ? prd_stocks_sto_res() : 'cast('.prd_stocks_sto_res().' as signed)' ).' as stock_res,
			'.($config['use_decimal_qte'] ? 'sto_com' : 'cast(sto_com as signed)' ).' as stock_com,
			'.($config['use_decimal_qte'] ? 'sto_prepa' : 'cast(sto_prepa as signed)' ).' as stock_prepa,
			if(prd_stock_livr<ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY),null,date_format(prd_stock_livr,"%d/%m/%Y")) as stock_livr,
			prd_keywords as keywords, prd_sleep as sleep,
			prd_img_id as img_id, date_format(prd_date_created,"%d/%m/%Y à %H:%i") as date_created,
			date_format(prd_date_modified,"%d/%m/%Y à %H:%i") as date_modified,
			prd_new as new_rule, prd_is_sync as is_sync,
			prd_publish and prd_publish_cat and prd_sleep and (' . prd_stocks_get_sql() . '-sto_prepa)>0 as destockage,
			prd_sell_weight as sell_weight, rel_weight
		from ord_related
			left join prd_products as prd on prd.prd_tnt_id='.$config['tnt_id'].' and rel_rel_id=prd.prd_id and prd_date_deleted is null
			left join prd_brands on (brd_tnt_id='.$config['tnt_id'].' and prd_brd_id=brd_id)
			left join prd_stocks on (sto_tnt_id='.$config['tnt_id'].' and prd.prd_id=sto_prd_id and sto_dps_id='.$dps.' and sto_is_deleted=0)
		where rel_tnt_id='.$config['tnt_id'].'
			and rel_prd_id in ('.implode( ', ', $prd ).') and rel_is_deleted=0
	';

	if( sizeof($exclude) ){
		$sql .= ' and prd.prd_id not in ('.implode(',', $exclude).')';
	}

	if( is_numeric($rel_id) && $rel_id>0 ){
		$sql .= ' and rel_rel_id='.$rel_id;
	}else{
		$sql .= ' and prd_publish and prd_publish_cat and prd_publish and prd_publish_cat and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)';
		// sur-requête pour le prix TTC
		if( $with_price ){
			$sql = '
				select d_tbl.*, (d_tbl.tva_rate * d_tbl.price_ht) as price_ttc
				from ('.$sql.') as d_tbl
			';
		}
	}

	$sql .= ' order by rel_weight desc';
	if( is_numeric($limit) && $limit > 0 ){
		$sql .= ' limit 0,'.$limit;
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement des produits associés à un autre sur la base des achats
 *	réalisés par les internautes (Les clients ayant acheté ce produit ont également commandé).
 *
 *	@param int $prd Obligatoire, Identifiant du produit dont on souhaite obtenir les produits associés.
 *	@param int $limit Facultatif, nombre maximum de résultats à retourner (-1 pour ne pas limiter).
 *	@param bool $with_price Facultatif, active ou désactive le caul du tarif du produit. Igoré si $rel_id spécifié.
 *
 *	@return bool False en cas d'échec.
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : Identifiant du produit
 *		- ref : Référence du produit
 *		- name : Nom du produit
 *		- desc : Description du produit
 *		- desc-long : Description longue du produit
 *		- price_ht : Prix unitaire HT du produit (si $with_price)
 *		- tva_rate : taux de TVA du produit (si $with_price)
 *		- price_ttc : Prix unitaire TTC du produit (si $with_price)
 *		- publish : Produit publié oui / non
 *		- publish_cat : Catégorie du produit publiée oui / non
 *		- orderable : Produit commandable oui / non
 *		- childonly : Produit vendu uniquement en article lié oui / non
 *		- countermark : Produit en contre-marque oui / non
 *		- centralized : Produit centralisé oui / non
 *		- new : Produit nouveau oui / non
 *		- date_published : Date de publication au format FR
 *		- brd_id : Identifiant de la marque du produit
 *		- brd_title : Nom de la marque du produit
 *		- brd_img_id : Identifiant de l'image de la marque du produit
 *		- brd_url : URL interne vers la amrque du produit
 *		- weight : Poids brut du produit
 *		- length : Longueur du produit
 *		- width : Largeur du produit
 *		- height : Hauteur du produit
 *		- stock : Quantité en stock du produit
 *		- stock_res : Quantité réservé du produit
 *		- stock_com : Quantité en commande fournisseur du produit
 *		- stock_prepa : Quantité en préparation du produit
 *		- stock_livr : Date de prochain approvisionnement au format FR
 *		- keywords : Mots-clés rattachés au produit
 *		- sleep : Produit en sommeil oui / non
 *		- img_id : Identifiant de l'image principale du produit
 *		- date_created : Date de création au format FR
 *		- date_modified : Date de modification au format FR
 *		- new_rule : Règle d'apparition en tant que produit nouveau
 *		- is_sync : Produit synchronisé oui / non
 *		- destockage : Produit en déstockage oui / non
 *		- sell_weight : Produit vendu au poids Oui / Non
 *		- rel_weight : Nombre de commandes où les deux produits se retrouvent associés
 */
function prd_user_related_get( $prd, $limit=ORD_RELATED_LIMIT, $with_price=true ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}
	if( !is_numeric($limit) || $limit<0 ){
		return false;
	}

	if( $with_price ){
		// Paramètres dépendants de l'utilisateur en cours
		$user = isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0;

		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) ){
			// Permet la consultation sous l'identité d'une autre personne (réservé aux administrateurs et représentants)
			$user = $_SESSION['admin_view_user'];
		}elseif( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==4 && $config['tnt_id']==1 ){
			// les revendeurs Bigship ne voient pas leur prix via cette fonction
			$user = 0;
		}
		if( isset($_SESSION['usr_tnt_id']) && !$_SESSION['usr_tnt_id'] ){
			$user = 0;
		}

		$exempt = gu_users_is_tva_exempt( $user );
		$usr_holder = gu_users_get_prices_holder( $user );
		$prc = gu_users_get_prc( $usr_holder, true );
		$usr_ref = gu_users_get_ref( $usr_holder, true );
	}

	$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();

	// Les utilisateurs "Places de marché" sont exclus de ces calculs statistiques
	require_once('comparators.inc.php');
	$usr_exclude = ctr_marketplaces_get_users();

	// si ya pas de buyer alors
	$buyers = prd_buyers_get( $prd );
	if( !$buyers || !sizeof($buyers) ){
		return false;
	}

	$sql = '
		select
			prd.prd_id as id, prd_ref as ref, prd_name as name,
			prd_desc as "desc", prd_desc_long as "desc-long",
	';
	if( $with_price ){
		$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

		$sql .= '
				get_price_ht( prd.prd_tnt_id, prd.prd_id, '.$usr_holder.', 1, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", prd.prd_brd_id, prd.prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'prd.prd_ecotaxe').' ) as price_ht,
				'.( $exempt ? '1' : 'get_tva( prd.prd_tnt_id, prd.prd_id, '.gu_users_get_accouting_category( $user, true ).', '.gu_users_get_cnt_code($user, true).' )' ).' as tva_rate,
		';
	}
	$sql .= '
			prd_publish as publish, prd_publish_cat as publish_cat,
			prd_orderable as orderable, prd_childonly as childonly,
			prd_countermark as countermark, prd_centralized as centralized,
			prd_new!="-1" and (prd_new="1" or datediff(now(),prd_date_created)<='.$config['prd_new_days'].') as new,
			date_format(prd_date_published,"%d/%m/%Y à %H:%i") as date_published,
			brd_id, if(brd_title!="",brd_title,brd_name) as brd_title, brd_img_id, brd_url,
			prd_weight as weight, prd_length as "length", prd_width as width, prd_height as height,
			'.($config['use_decimal_qte'] ? prd_stocks_get_sql() . '-sto_prepa' : 'cast(' . prd_stocks_get_sql() . '-sto_prepa as signed)' ).' as stock,
			'.($config['use_decimal_qte'] ? prd_stocks_sto_res() : 'cast('.prd_stocks_sto_res().' as signed)' ).' as stock_res,
			'.($config['use_decimal_qte'] ? 'sto_com' : 'cast(sto_com as signed)' ).' as stock_com,
			'.($config['use_decimal_qte'] ? 'sto_prepa' : 'cast(sto_prepa as signed)' ).' as stock_prepa,
			if(prd_stock_livr<ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY),null,date_format(prd_stock_livr,"%d/%m/%Y")) as stock_livr,
			prd_keywords as keywords, prd_sleep as sleep,
			prd_img_id as img_id, date_format(prd_date_created,"%d/%m/%Y à %H:%i") as date_created,
			date_format(prd_date_modified,"%d/%m/%Y à %H:%i") as date_modified,
			prd_new as new_rule, prd_is_sync as is_sync,
			prd_publish and prd_publish_cat and prd_sleep and (' . prd_stocks_get_sql() . '-sto_prepa)>0 as destockage,
			prd_sell_weight as sell_weight, count(*) as rel_weight
		from prd_products as prd
			left join prd_brands on (brd_tnt_id='.$config['tnt_id'].' and prd_brd_id=brd_id)
			left join prd_stocks on (sto_tnt_id='.$config['tnt_id'].' and prd.prd_id=sto_prd_id and sto_dps_id='.$dps.' and sto_is_deleted=0)
		where prd_id!='.$prd.' and prd.prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null and prd_id in (
			select distinct prd_id
			from ord_products, ord_orders
			where prd_ord_id=ord_id
				and prd_tnt_id='.$config['tnt_id'].'
				and ord_tnt_id='.$config['tnt_id'].'
				and ord_usr_id in ('.
					implode( ',', $buyers )
				.')
		)
	';

	$sql .= ' and prd_publish and prd_publish_cat and prd_publish and prd_publish_cat and (not prd_sleep or (' . prd_stocks_get_sql() . '-sto_prepa)>0)';
	$sql .= '
		group by prd_id
	';
	// sur-requête pour le prix TTC
	if( $with_price ){
		$sql = '
			select d_tbl.*, (d_tbl.tva_rate * d_tbl.price_ht) as price_ttc
			from ('.$sql.') as d_tbl
		';
	}
	$sql .= ' order by rel_weight desc';
	if( is_numeric($limit) && $limit > 0 ){
		$sql .= ' limit 0,'.$limit;
	}

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction va retourner la liste des identifiants d'utilisateurs ayant acheté un produit donné (passé en paramètre).
 *	Le résultat de cette fonction est mis en cache pour des questions de performance, durant 24h.
 *	Les utilisateurs correspondant à des places de marché sont automatiquement exclus de la liste.
 *	@param int $prd Obligatoire, identifiant du produit pour lequel on souhaite connaître les acheteurs
 *	@return array un tableau contenant les identifiants utilisateur des personnes ayant acheté le produit demandé.
 */
function prd_buyers_get( $prd ){
	global $config;
	global $memcached;

	if( !is_numeric($prd) || $prd<=0 ){
		return false;
	}

	// Mise en cache memcached
	if( $buyers = $memcached->get( 'prd:buyers:'.$config['tnt_id'].':'.$prd ) ){
		return $buyers;
	}

	// Les utilisateurs "Places de marché" sont exclus de ces calculs statistiques
	require_once('comparators.inc.php');
	$usr_exclude = ctr_marketplaces_get_users();

	// Seules les commandes validées sont prises en compte dans le calcul
	require_once('ord.states.inc.php');
	$ord_states = ord_states_get_ord_valid();

	// Charge la liste des utilisateurs ayant commandé ce produit
	$rusers = ria_mysql_query('
		select distinct ord_usr_id as id
		from ord_products
			join ord_orders on (prd_tnt_id = ord_tnt_id and prd_ord_id=ord_id)
		where prd_ord_id=ord_id
			and prd_id='.$prd.'
			and prd_tnt_id='.$config['tnt_id'].'
			'.( is_array($usr_exclude) && sizeof($usr_exclude) ? ' and ord_usr_id not in ('.implode( ',', $usr_exclude ).')' : '' ).'
			and ord_state_id in ('.implode( ',', $ord_states ).')
	');

	if( !$rusers ){
		error_log('Erreur SQL "prd_buyers_get" : '.$sql);
		return false;
	}

	// Transforme le résultat de requête en tableau
	$buyers = array();
	while( $usr = ria_mysql_fetch_array($rusers) ){
		$buyers[] = $usr['id'];
	}

	// Sauvegarde le résultat pour une utilisation ultérieure du cache
	$memcached->set( 'prd:buyers:'.$config['tnt_id'].':'.$prd, $buyers, 86400 );

	// Retourne le résultat
	return $buyers;
}
// \endcond

/// @}


