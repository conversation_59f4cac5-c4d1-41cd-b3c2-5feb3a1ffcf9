<?php
namespace RGPD\Trackers;
/** \defgroup Trackers Gestion des module js de tracking d'audiance ou réseaux sociaux
 * \ingroup Tarteaucitron
 * Tout tracker qui est voué a être intégré sur un site client doit implémenter l'interface TarteaucitronTrackerInterface et doit être dans ce module
 *
 * Quelque exemple de module js de tracking :
 * - google analytics
 * - adwords
 * - addthis
 *
 * @{
 */
/**
 * \interface TarteaucitronTrackerInterface
 * \brief TarteaucitronTrackerInterface permet de définir la fonctionnalité de base d'un module de js qui ajoute des cookies
 */
interface TarteaucitronTrackerInterface
{
	/** Cette fonction permet de retourné le code de tracking pour ajout au module tarteaucitron.js
	 * \param boolean $with_script_tag détermine si le js retourné est encadré de tag script ou non
	 * \return string Retourne le js d'initialisation du module pour tarteaucitron
	 */
	public function renderTarteaucitronCode($with_script_tag=true);
}
/// @}