<?php

// Dépendances
require_once('orders.inc.php');

/** \defgroup model_ord_bl Gestion des bons de livraison
 *	\ingroup oms
 *	Ce module comprend les fonctions nécessaires à la gestion des bons de livraison (BL)
 *
 *	@{
 */

// \cond onlyria
/** Cette fonction permet la transmission d'un bon de livraison d'un utilisateur à un autre.
 *	Son usage est réservé aux seuls administrateurs et représentants.
 *
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $user Obligatoire, Identifiant du nouveau propriétaire du bon de livraison
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_update_user( $bl, $user ){
	global $config;

	// Contrôles
	if( !is_numeric( $bl ) || $bl<=0 ) return false;
	if( !is_numeric( $user ) || $user<=0 ) return false;
	if( !ord_bl_exists( $bl ) ) return false;
	if( !gu_users_exists( $user ) ) return false;

	// Procède à la mise à jour
	return ria_mysql_query('
		update ord_bl set bl_usr_id='.$user.'
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour la référence d'un bon de livraison.
 *
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param string $ref nouvelle référence du bon de livraison
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_ref_update( $bl, $ref ){
	global $config;

	if( !is_numeric( $bl ) || $bl<=0 ) return false;
	if( !ord_bl_exists( $bl ) ) return false;

	return ria_mysql_query('
		update ord_bl set bl_ref=\''.addslashes($ref).'\'
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour la date d'un bon de livraison.
 *
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param string $date nouvelle date du bon de livraison
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_update_date( $bl, $date ){
	global $config;

	if( !is_numeric( $bl ) || $bl<=0 ) return false;
	if( !ord_bl_exists( $bl ) ) return false;
	if( !isdateheure($date) ){
		return false;
	}

	return ria_mysql_query('
		update ord_bl set bl_date="'.$date.'"
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le service de livraison d'un bon de livraison.
 *
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $srv_id Obligatoire, nouvel identifiant du service de livraison
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_update_dlv_service( $bl, $srv_id ){
	global $config;

	if( !is_numeric( $bl ) || $bl<=0 ) return false;
	if( !ord_bl_exists( $bl ) ) return false;
	if( !is_numeric( $srv_id ) || $srv_id<=0 ) return false;

	return ria_mysql_query('
		update ord_bl set bl_srv_id="'.$srv_id.'"
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour du nombre de produits dans le bon de livraison.
 *
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $products Obligatoire, nombre de produits
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_update_products( $bl, $products ){
	global $config;

	if( !is_numeric( $bl ) || $bl<=0 ) return false;
	if( !ord_bl_exists( $bl ) ) return false;
	if( !is_numeric( $products ) || $products<0 ) return false;

	return ria_mysql_query('
		update ord_bl set bl_products="'.$products.'"
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction est chargée de recalculer les totaux pour un bl.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison à rafraichir
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_update_totals( $bl ){
	global $config;

	if( !is_numeric($bl) || $bl<=0 ) return false;

	$r_bl_data = ord_bl_get( $bl );
	if( !$r_bl_data || !ria_mysql_num_rows($r_bl_data) ){
		return false;
	}

	$bl_data = ria_mysql_fetch_assoc( $r_bl_data );

	ord_update_total_delivred($bl);

	// Charge la catégorie tarifaire à partir du compte client
	$prf = isset($_SESSION['usr_prf_id']) && is_numeric($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] ? $_SESSION['usr_prf_id'] : false;
	$prc = isset($_SESSION['usr_prc_id']) && prd_prices_categories_exists($_SESSION['usr_prc_id']) ? $_SESSION['usr_prc_id'] : $config['default_prc_id'];
	if( $bl_data['usr_id'] ){
		if( $rusr = gu_users_get($bl_data['usr_id']) ){
			if( $usr_data = ria_mysql_fetch_array($rusr) ){
				$prc = $usr_data['prc_id'];
				$prf = $usr_data['prf_id'];
			}
		}
	}

	// Détermine si le tarif TTC doit être arrondi (suivant si le client est HT ou non).
	$round_ttc = prd_prices_categories_get_ttc($prc);
	$ord_update_totals_with_ttc = !isset($config['ord_update_totals_with_ttc']) || $config['ord_update_totals_with_ttc'];

	$ratio = isset($config['weight_col_calc_lines']) && is_numeric($config['weight_col_calc_lines']) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;

	$sub_sql = ord_orders_colisage_sub_sql('o.bl_piece', $ratio, 'op.');

	$round_ttc_count = isset($config['round_digits_count_header']) && $config['round_digits_count_header'] > -1 ? $config['round_digits_count_header'] : $config['round_digits_count'];
	$round_dec = $round_ttc_count;
	if( $bl_data['piece']!='' && $config['tnt_id']==13 ){
		$round_dec = '( if(ifnull(p.prd_sell_weight, 0) = 1, '.$config['round_digits_count'].' + log10('.$ratio.'), '.$round_ttc_count.') )';
	}

	$sum_ht = 'sum( prd_price_ht * ('.$sub_sql.') )';
	$sum_ttc = 'sum( '.($round_ttc ? 'round(' : '' ).'ifnull('.($ord_update_totals_with_ttc ? 'prd_price_ttc' : 'null').', prd_price_ht * prd_tva_rate)'.($round_ttc ? ','.$round_dec.')' : '').' * ('.$sub_sql.') ) ';

	$round_after_qte = isset($config['round_digits_after_qte']) && $config['round_digits_after_qte'];
	if( $round_after_qte ){
		$sum_ht = 'sum( round(prd_price_ht * ('.$sub_sql.'),'.$round_dec.') ) ';
		$sum_ttc = 'sum( round(ifnull('.($ord_update_totals_with_ttc ? 'prd_price_ttc' : 'null').', prd_price_ht * prd_tva_rate) * ('.$sub_sql.'),'.$round_dec.') ) ';
	}

	$sql = '
		select
			('.$sum_ht.') as total_ht,
			('.$sum_ttc.') as total_ttc,
			sum( '.$sub_sql.' ) as products
		from
			ord_bl_products as op
		join ord_bl as o on op.prd_bl_id = o.bl_id and op.prd_tnt_id=o.bl_tnt_id
		left join prd_products as p on ( op.prd_tnt_id=p.prd_tnt_id and op.prd_id=p.prd_id )
		left join prd_colisage_types on ( op.prd_tnt_id=col_tnt_id and ifnull(prd_col_id, 0)=col_id and col_is_deleted = 0 )
		where
			op.prd_tnt_id='.$config['tnt_id'].'
			and op.prd_bl_id='.$bl.'
	';

	$rtotals = ria_mysql_query($sql);
	if( $rtotals && ria_mysql_num_rows($rtotals) ){
		$totals = ria_mysql_fetch_assoc($rtotals);

		return ria_mysql_query('
			update
				ord_bl
			set
				bl_total_ht='.(isset($totals['total_ht']) && $totals['total_ht'] ? $totals['total_ht'] : 0).',
				bl_total_ttc='.(isset($totals['total_ttc']) && $totals['total_ttc']  ? $totals['total_ttc'] : 0).',
				bl_products='.(isset($totals['products']) && $totals['products']  ? $totals['products'] : 0).'
			where
				bl_tnt_id='.$config['tnt_id'].' and
				bl_id='.$bl
		);
	}

	return true;

}
// \endcond

// \cond onlyria
/**	Cette fonction applique le code promotion de la commande à l'origine du BL
 *	S'il y a plus d'une commande pour le BL, rien ne sera effectué
 *	@param int $id Obligatoire, Identifiant du bon de livraison
 *	@return bool True si un code promo était déjà appliqué ou s'il vient d'être appliqué, False dans les autres cas
 */
function ord_bl_apply_pmt( $id ){

	// Existence du BL
	if( !ord_bl_exists($id) ) return false;

	// Code promotion déjà assigné
	$promo_val = fld_object_values_get( $id, FLD_PMT_BL );
	if( $promo_val!==false && trim($promo_val)!='' ) return true;

	// récupère le bon de commande d'origine (doit être unique)
	$rorders = ord_bl_orders_get( $id );
	if( !$rorders ) return false;
	if( ria_mysql_num_rows($rorders)!=1 ) return false;
	$ord = ria_mysql_fetch_array( $rorders );

	// récupère le code promotion éventuel sur la commande
	$rorders = ord_orders_get( 0, $ord['id'] );
	if( !$rorders || !ria_mysql_num_rows($rorders) ) return false;
	$ord = ria_mysql_fetch_array( $rorders );

	// récupère le code promotion
	if( !is_numeric($ord['pmt_id']) || $ord['pmt_id']<=0 ) return false;
	$rpmt = pmt_codes_get($ord['pmt_id']);
	if( !$rpmt || !ria_mysql_num_rows($rpmt) ) return false;
	$pmt = ria_mysql_fetch_array( $rpmt );
	if( !$pmt['discount'] ) return false;

	// récupère l'entête complète du BL
	$rbl = ord_bl_get( $id );
	if( !$rbl || !ria_mysql_num_rows($rbl) ) return false;
	$bl = ria_mysql_fetch_array($rbl);

	// calcule les totaux HT et TTC
	$total_ht = $bl['total_ht'];
	$total_ttc = $bl['total_ttc'];

	// charge le compte client pour récupérer sa catégorie tarifaire
	$rusr = gu_users_get($bl['usr_id']);
	if( !$rusr || !ria_mysql_num_rows($rusr) ) return false;
	$usr = ria_mysql_fetch_array($rusr);

	global $config;

	// toutes les lignes du BL rattachées à la commande d'origine
	$rproducts = ord_bl_products_get( $bl['id'], $ord['id'] );
	if( !$rproducts ) return false;
	if( $ord['piece']=='' || !$config['ord_pmt_sync_update'] ){
		if( $pmt['discount_type']==0 ){
			// Contrôle que la commande ne contient pas que des dons (catégorie 408 du locataire 5)
			$only_dons = false;
			if( $config['tnt_id']==5 ){
				$only_dons = true;
				while( $prd = ria_mysql_fetch_array($rproducts) ){
					if( !prd_products_categories_exists( $prd['id'], 408 ) )
						$only_dons = false;
				}
			}
			if( !$only_dons ){
				// Remise en euro
				$total_ht -= $pmt['discount'];
				$total_ttc -= ( $pmt['discount'] * _TVA_RATE_DEFAULT );
			}
		}else{
			while( $prd = ria_mysql_fetch_array($rproducts) ){
				if( !prd_products_is_port($prd['ref']) ){
					if( pmt_products_is_included( $pmt, $prd['id'] ) && ( $config['tnt_id']!=5 || !prd_products_categories_exists( $prd['id'], 408 ) ) ){
						$total_ht -= ( $prd['price_ht'] * $prd['qte'] * $pmt['discount'] / 100 );
						$ttc_amount = prd_prices_categories_get_ttc($usr['prc_id']) ? round($prd['price_ttc'], $config['round_digits_count']) : $prd['price_ttc'];
						$total_ttc -= ( $ttc_amount * $prd['qte'] * $pmt['discount'] / 100 );
					}
				}
			}
		}
	}

	// met à jour les totaux
	$r = ria_mysql_query('
		update ord_bl
		set bl_total_ht='.round($total_ht, 2).', bl_total_ttc='.round($total_ttc, 2).'
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$id.'
	');

	if( $r ){
		// assigne le code promotion en champ avancé
		$r = fld_object_values_set( $id, FLD_PMT_BL, $pmt['id'] );
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'enregistrement d'un nouveau bon de livraison hors cas de synchronisation (admin, site client)
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur concerné par le bl
 *	@param float $total_ht Total HT
 *	@param float $total_ttc Total TTC
 *	@param string $piece Piece
 *	@param string $ref Référence
 *	@param string|null $date Date/Heure de création du bon
 *	@param int $state Identifiant d'un statut de commande parmi les valeurs 6,7 et 8
 *	@param int|null $srv Identifiant du service de livraison
 *	@param bool $masked Masquer le bon de livraison ?
 *	@param integer $adr_dlv_id Facultatif, identifiant de l'adresse de livraison pour le bl
 *
 *	@return int l'identifiant du nouveau bl en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_add($usr, $total_ht = 0, $total_ttc = 0, $piece = '', $ref = '', $date = null, $state = 6, $srv = null, $masked = false, $adr_dlv_id=0)
{
	$debug = false; // active la trace de débugage

	if (!gu_users_exists($usr)) {
		if ($debug) {
			error_log('[ord_bl_add] client inexistant');
		}
		return false;
	}

	if (!is_string($piece)) {
		if ($debug) {
			error_log('[ord_bl_add] piece invalide');
		}
		return false;
	}

	if (!is_string($ref)) {
		if ($debug) {
			error_log('[ord_bl_add] référence invalide');
		}
		return false;
	}

	if (is_null($date)) {
		$date = date('d/m/Y H:i:s');
	} elseif (!isdateheure($date)) {
		if ($debug) {
			error_log('[ord_bl_add] date invalide');
		}
		return false;
	}
	$date = dateheureparse($date);

	if (!is_numeric($total_ht)) {
		if ($debug) {
			error_log('[ord_bl_add] total HT invalide');
		}
		return false;
	}

	if (!is_numeric($total_ttc)) {
		if ($debug) {
			error_log('[ord_bl_add] total TTC invalide');
		}
		return false;
	}

	if (!ord_states_exists($state)) {
		if ($debug) {
			error_log('[ord_bl_add] état invalide');
		}
		return false;
	}

	if (!is_null($srv) && !dlv_services_exists($srv)) {
		if ($debug) {
			error_log('[ord_bl_add] service inexistant');
		}
		return false;
	}

	$masked = $masked === true ? 1 : 0;

	if (!is_numeric($adr_dlv_id)) {
		if ($debug) {
			error_log('[ord_bl_add] identifiant d\'adresse de livraison invalide');
		}
		return false;
	}

	global $config;

	$sql = '
		insert into ord_bl
			( bl_tnt_id, bl_usr_id, bl_total_ht, bl_total_ttc, bl_piece, bl_ref, bl_date, bl_state_id, bl_srv_id, bl_masked, bl_adr_dlv_id )
		values
			( "'
				. addslashes($config['tnt_id'])
				. '", "' . addslashes($usr)
				. '", ' . $total_ht
				. ', ' . $total_ttc
				. ', "' . addslashes($piece)
				. '", "' . addslashes($ref)
				. '", "' . $date
				. '", ' . $state
				. ', ' . (is_null($srv) ? 'NULL' : $srv)
				. ', ' . $masked
				. ', ' . $adr_dlv_id
		. ' )';

	$res = ria_mysql_query($sql);

	if (!$res) {
		if ($debug) {
			error_log('[ord_bl_add] echec de la requête d\'insertion (erreur : ' . mysql_error() . ')');
		}
		return false;
	}

	return ria_mysql_insert_id();
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'enregistrement d'un nouveau bon de livraison à partir de l'api de synchronisation
 * 	Ne pas utilisé hors cas de synchronisation
 *	@param int $usr Obligatoire, Identifiant de l'utilisateur concerné par le bl
 *	@param string $piece Obligatoire, Numéro de pièce sage
 *	@param string $ref Obligatoire, Référence sage
 *	@param string $date Obligatoire, Date/Heure de création du bon
 *	@param int $state Obligatoire, Identifiant d'un statut de commande parmi les valeurs 6,7 et 8
 *	@param int $srv Obligatoire, Identifiant du service de livraison prenant en charge l'envoi
 *	@param int $ord_srv Facultatif, identifiant de la commande à partir de laquelle charger le service de livraison ($srv doit être mis à zéro)
 *	@param int $adr_dlv_id Facultatif, identifiant de l'adresse de livraison pour le bl
 *	@return int l'identifiant du nouveau bl en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_add_sage( $usr, $piece, $ref, $date, $state, $srv, $ord_srv=0, $adr_dlv_id=0 ){

	$debug = false; // active la trace de débugage

	if( !gu_users_exists( $usr ) ){
		if( $debug ){
			error_log('[ord_bl_add_sage] client inexistant');
		}
		return false;
	}

	if( !ord_states_exists( $state ) ){
		if( $debug ){
			error_log('[ord_bl_add_sage] état invalide');
		}
		return false;
	}
	if( $srv == 0 ){
		$srv = 'null';

		if (is_numeric($ord_srv) && $ord_srv > 0) {
			$temp = ord_orders_get_dlv_service($ord_srv);
			if (is_numeric($temp) && $temp > 0) {
				$srv = $temp;
			}
		}
	}elseif( !dlv_services_exists( $srv ) ){
		if( $debug ){
			error_log('[ord_bl_add_sage] service inexistant');
		}
		return false;
	}
	if( !isdateheure( $date ) ){
		if( $debug ){
			error_log('[ord_bl_add_sage] date invalide');
		}
		return false;
	}

	if (!is_numeric($adr_dlv_id)) {
		if ($debug) {
			error_log('[ord_bl_add] identifiant d\'adresse de livraison invalide');
		}
		return false;
	}

	$date = dateheureparse( $date );

	global $config;

	if( trim($piece) ){
		// si une bon de livraison non masqué existe avec ce numéro de pièce on la supprime
		$rexists = ria_mysql_query('select bl_id from ord_bl where bl_tnt_id='.$config['tnt_id'].' and bl_piece="'.addslashes($piece).'" and bl_masked=0');
		if( $rexists && ria_mysql_num_rows($rexists) ){
			$exists = ria_mysql_fetch_assoc($rexists);
			if( !ord_bl_del($exists['bl_id']) ){
				return false;
			}
		}
	}

	$sql = '
		insert into ord_bl
			( bl_tnt_id, bl_usr_id, bl_piece, bl_ref, bl_date, bl_state_id, bl_srv_id, bl_adr_dlv_id )
		values
			( '.$config['tnt_id'].', '.$usr.', "'.addslashes($piece).'", "'.addslashes($ref).'", "'.$date.'", '.$state.', '.$srv.', '.$adr_dlv_id.' )
	';

	$res = ria_mysql_query($sql);

	if( !$res ){
		if( $debug ){
			error_log('[ord_bl_add_sage] echec de la requête d\'insertion (erreur : '.mysql_error().')');
		}
		return false;
	}

	return ria_mysql_insert_id();

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification de l'existence d'un bon de livraison
 *	@param int $id Obligatoire, Identifiant du bon de livraison
 *	@param int $usr Facultatif, Identifiant du compte client de rattachement
 *	@param bool $include_masked Facultatif, permet de tester un BL masqué
 *	@return bool true si le bon de livraison existe
 *	@return bool false si le bon de livraison n'existe pas où en cas d'erreur
 */
function ord_bl_exists( $id, $usr=0, $include_masked=false ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}
	if( !is_numeric($usr) || $usr < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select bl_id from ord_bl
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_id = '.$id.'
	';

	if( $usr ){
		$sql .= ' and bl_usr_id = '.$usr;
	}

	if( !$include_masked ){
		$sql .= ' and bl_masked = 0';
	}

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification de l'existence d'un bon de livraison, par son numéro de pièce
 *	@param string $piece Obligatoire, Numéro de piece du bon de livraison
 *	@return bool true si le bon de livraison existe
 *	@return bool false si le bon de livraison n'existe pas où en cas d'erreur
 */
function ord_bl_exists_piece( $piece ){

	$piece = trim($piece);
	if( !$piece ){
		return false;
	}

	$rbl = ord_bl_get( 0, 0, false, false, false, array(), $piece );

	return $rbl && ria_mysql_num_rows($rbl);

}
// \endcond

/**	Permet le chargement des informations principales concernant un bon de livraison
 *	@param int $id Optionnel, Identifiant du bon de livraison (ou tableau d'identifiants)
 *	@param int $usr Optionnel, identifiant du compte client de rattachement
 *	@param bool|array $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par date. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : piece, ref, date, total, state. Les valeurs autorisées pour la direction sont : asc, desc.
 *	@param bool $is_sync Optionnel, si true, ne retourne que les bons de livraison synchronisés (ceux ayant un numéro de pièce)
 *	@param bool $usr_exist Optionnel, si true, ne retourne que les bons de livraison associés à des clients non supprimés (n'est pas pris en compte si $usr est précisé)
 *	@param array $state Optionnel, état du bon de livraison sur lequel filtrer la requête
 *	@param string $piece Optionnel, numéro de pièce du bon de livraison (insensible à la casse)
 *	@param int $ord Optionnel, identifiant ou tableau d'identifiants de commande(s) auquel(s) le BL fait référence. Les commandes référencées doivent être valides (non masquées, statut correct).
 *	@param string $date_start Optionnel, date de début de la période
 *	@param string $date_end Optionnel, date de fin de la période
 *	@param array $array_fld Facultatif, tableau de filtres complémentaires
 *	@param int $wst Optionnel, identifiant ou tableau d'identifiants de site (permet de récupérer les BL de commande passées sur un site en particulier)
 *	@param int $seller Optionnel, identifiant du commercial sur lequel filtrer le résultat
 *	@param int $dps Optionnel, identifiant du dépôt de stockage sur lequel filtrer le résultat
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : Identifiant du bon de livraison
 *			- piece : numéro de pièce sage
 *			- ref : référence sage
 *			- date : date/heure de création du BL (dans SAGE)
 *			- date_en : date/heure de création du bon de livraison au format EN/US
 *			- usr_id : Identifiant de l'utilisateur que le BL concerne
 *			- total_ht : Montant total HT du BL
 *			- total_ttc : Montant total TTC du BL
 *			- state_id : Identifiant de l'état du BL
 *			- state_name : Représentation textuelle de l'état du BL
 *			- srv_id : Identifiant du service de livraison pour ce BL
 *			- adr_dlv_id : Identifiant de l'adresse de livraison
 *			- adr_invoices : Identifiant de l'adresse de facturation
 *			- products : nombre de produits dans le bon de livraison
 *			- str_id : identifiant du magasin ?
 *			- pkg_id : ?
 *			- pay_id : identifiant du moyen de paiement
 *			- pmt_id : identifiant du code promotion appliqué au Bon de livraison
 *			- dps_id : identifiant du dépôt de livraison
 *			- wst_id : identifiant du site web
 *			- reseller_id : identifiant du revendeur
 *			- seller_id : identifiant du commercial affecté à la pièce
 *			- age : Age de la pièce, en jours
 *			- date_en : date/heure au format EN
 *			- date_modified : date de dernière modification
 *			- date_modified_en : date de dernière modification au format EN
 *			- need_sync : cette pièce à-t-elle besoin d'être synchronisée ?
 */
function ord_bl_get(
	$id=0, $usr=0, $sort=false, $is_sync=false, $usr_exist=false, $state=array(), $piece=false, $ord=array(), $date_start=false, $date_end=false,
	$array_fld=false, $wst=0, $seller=0, $dps=0
){

	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	if( !is_numeric($usr) || $usr < 0 ){
		return false;
	}

	if( !is_numeric($dps) || $dps < 0 ){
		return false;
	}

	if( !is_numeric($seller) || $seller < 0 ){
		return false;
	}

	$state = control_array_integer( $state, false );
	if( $state === false ){
		return false;
	}

	$ord = control_array_integer( $ord, false );
	if( $ord === false ){
		return false;
	}

	if( $date_start !== false && !isdateheure($date_start) && !isdate($date_start) ){
		return false;
	}

	if( $date_end !== false && !isdateheure($date_end) && !isdate($date_end) ){
		return false;
	}

	if( $array_fld !== false ){
		if( !is_array($array_fld) ){
			return false;
		}

		if( !ria_array_key_exists(array( 'fld' ), $array_fld) ){
			return false;
		}

		if( !isset($array_fld['or_between_val']) ){
			$array_fld['or_between_val'] = false;
		}

		if( !isset($array_fld['or_between_fld']) ){
			$array_fld['or_between_fld'] = false;
		}

		if( !isset($array_fld['lng']) ){
			$array_fld['lng'] = false;
		}else{
			$array_fld['lng'] = strtolower2( $array_fld['lng'] );

			if( !in_array($array_fld['lng'], $config['i18n_lng_code']) ){
				return false;
			}
		}
	}

	$wst = control_array_integer( $wst, false );
	if( $wst === false ){
		return false;
	}

	global $config;

	$sql = '
		select
			bl_id as id,
			bl_piece as piece,
			bl_ref as "ref",
			date_format(bl_date,"%d/%m/%Y à %H:%i") as "date",
			bl_date as date_en,
			bl_usr_id as usr_id,
			bl_total_ht as total_ht,
			bl_total_ttc as total_ttc,
			state_id,
			ifnull(stn_name,state_name) as "state_name",
			bl_srv_id as srv_id,
			bl_adr_dlv_id as adr_dlv_id,
			bl_adr_invoices as adr_invoices,
			bl_products as products,
			bl_str_id as str_id,
			bl_pkg_id as pkg_id,
			bl_pay_id as pay_id,
			bl_pmt_id as pmt_id,
			bl_dps_id as dps_id,
			bl_wst_id as wst_id,
			bl_reseller_id as reseller_id,
			bl_reseller_contact_id as reseller_contact_id,
			bl_contact_id as contact_id,
			bl_seller_id as seller_id,
			bl_date_livr as date_livr,
			bl_dlv_notes as dlv_notes,
			bl_comments as comments,
			to_days(now()) - to_days(bl_date) as age,
			date_format(bl_date_modified, "%d/%m/%Y à %H:%i") as date_modified,
			bl_date_modified as date_modified_en,
			bl_need_sync as need_sync
		from
			ord_bl
			join ord_states on bl_state_id = state_id
			left join ord_states_name on bl_state_id = stn_stt_id and bl_tnt_id = stn_tnt_id and stn_wst_id = '.$config['wst_id'].'
		where
			bl_tnt_id = '.$config['tnt_id'].'
			and bl_masked = 0
	';

	if( sizeof($id) ){
		$sql .= ' and bl_id in ('.implode(', ', $id).')';
	}

	if( $usr > 0 ){
		$sql .= ' and bl_usr_id = '.$usr;
	}

	if( $seller > 0 ){
		$sql .= ' and bl_seller_id = '.$seller;
	}

	if( $dps > 0 ){
		$sql .= ' and bl_dps_id = '.$dps;
	}

	if( $is_sync ){
		$sql .= ' and bl_piece != ""';
	}

	if( $usr_exist && !$usr ){
		$sql .= '
			and bl_usr_id in (
				select usr_id from gu_users
				where usr_tnt_id in (0, '.$config['tnt_id'].') and usr_date_deleted is null
			)
		';
	}

	if( sizeof($state) ){
		$sql .= ' and bl_state_id in ('.implode(', ', $state).')';
	}

	if( trim($piece) ){
		$sql .= ' and upper(bl_piece) = upper("'.addslashes(trim($piece)).'")';
	}

	if( sizeof($ord) || sizeof($wst) ){

		$valid_states = ord_states_get_ord_valid();
		// ajout des statuts fournisseurs valides
		$valid_states = array_merge($valid_states, array(_STATE_SUPP_STUDY, _STATE_SUPP_WAIT_LIVR, _STATE_SUPP_LIVR, _STATE_SUPP_PARTIEL_LIVR, _STATE_SUPP_PARTIEL_INV, _STATE_SUPP_WAIT_CONFIRM));

		$sql .= '
			and exists (
				select 1
				from
					ord_bl_products
					join ord_orders on prd_tnt_id = ord_tnt_id and prd_ord_id = ord_id
				where
					prd_bl_id = bl_id and prd_tnt_id = '.$config['tnt_id'].'
					and ord_state_id in ('.implode(', ', $valid_states).')
					and ord_masked = 0
					'.( sizeof($ord) ? ' and ord_id in ('.implode(', ', $ord).')' : '' ).'
					'.( sizeof($wst) ? ' and ord_wst_id in ('.implode(', ', $wst).')' : '' ).'
			)
		';

	}

	if( $date_start !== false ){
		if( isdate($date_start) ){
			$sql .= ' and date(bl_date) >= "'.dateparse($date_start).'"';
		}else{
			$sql .= ' and bl_date >= "'.dateheureparse($date_start).'"';
		}
	}

	if( $date_end !== false ){
		if( isdate($date_end) ){
			$sql .= ' and date(bl_date) <= "'.dateparse($date_end).'"';
		}else{
			$sql .= ' and bl_date <= "'.dateheureparse($date_end).'"';
		}
	}

	$sql .= fld_classes_sql_get( CLS_BL, $array_fld['fld'], $array_fld['or_between_val'], $array_fld['or_between_fld'], $array_fld['lng'] );

	$sort_final = array();

	// Converti le paramètre de tri en SQL
	if( is_array($sort) ){
		foreach( $sort as $col => $dir ){
			$dir = strtolower(trim($dir)) == 'asc' ? 'asc' : 'desc';
			switch( strtolower(trim($col)) ){
				case 'piece':
					$sort_final[] = 'bl_piece '.$dir;
					break;
				case 'ref':
					$sort_final[] = 'bl_ref '.$dir;
					break;
				case 'date':
					$sort_final[] = 'bl_date '.$dir;
					break;
				case 'total':
					$sort_final[] = 'bl_total_ht '.$dir;
					break;
				case 'state':
					$sort_final[] = 'state_pos '.$dir;
					break;
			}
		}
	}

	if( !sizeof($sort_final) ){
		if( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id'] == PRF_RESELLER ){
			$sort_final[] = 'state_pos asc';
		}
		$sort_final[] = 'bl_date desc';
	}

	$sql .= ' order by '.implode(', ', $sort_final);
	return ria_mysql_query($sql);

}

// \cond onlyria
/** Cette fonction permet de récupérer le nom du service de livraison d'un bl.
 *	@param int $bl Obligatoire, identifiant d'un bon de livraison
 *	@return bool False si le BL n'existe pas, sinon le nom du service rattaché s'il existe
 */
function ord_bl_get_srv_name( $bl ){
	if( !is_numeric($bl) || $bl<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select srv_name
		from dlv_services
			join ord_bl on (srv_tnt_id=bl_tnt_id and srv_id=bl_srv_id)
		where srv_tnt_id='.$config['tnt_id'].'
			and bl_id='.$bl.'
			and bl_masked = 0
			and srv_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'srv_name' );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer l'identifiant du service de livraison utilisé sur un BL.
 *	@param int $bl Obligatoire, identifiant d'un bon de livraison
 *	@return bool False si le BL n'existe pas, sinon le nom du service rattaché s'il existe
 */
function ord_bl_get_srv_id( $bl ){
	global $config;

	if( !is_numeric($bl) || $bl<=0 ){
		return false;
	}

	$res = ria_mysql_query('
		select srv_id
		from dlv_services
			join ord_bl on (srv_tnt_id=bl_tnt_id and srv_id=bl_srv_id)
		where srv_tnt_id='.$config['tnt_id'].'
			and bl_id='.$bl.'
			and bl_masked = 0
			and srv_date_deleted is null
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'srv_id' );
}
// \endcond

/**	Cette fonction retourne les commandes concernées par un bon de livraison donnée.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param bool $smart_sort Facultatif, si True, le résultat est triés sur le nombre de lignes du BL référençant la commande
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : Identifiant de la commande
 *			- piece : numéro de pièce de la commande
 *			- ref : référence de la commande
 *			- date : date/heure de création de la commande (dans SAGE)
 *			- date_en : date/heure au format EN
 *			- usr_id : identifiant du compte client rattaché à la commande
 *			- str_id : identifiant du magasin dans lequel le client souhaite être livré
 *			- str_email : adresse email du magasin dans lequel le client souhaite être livré
 *			- rly_id : identifiant d'un point relais
 *			- state : identifiant de l'état de la commande
 *			- wst_id : identifiant du site de la commande
 *			- pay_id : identifiant du moyen de paiement de la commande
 */
function ord_bl_orders_get( $bl, $smart_sort=false ){
	global $config;

	if( !ord_bl_exists($bl) ){
		return false;
	}

	$sql = 'select
				ord_id as id,
				ord_piece as piece,
				ord_ref as ref,
				date_format(ord_date,"%d/%m/%Y à %H:%i") as date,
				ord_date as date_en,
				ord_usr_id as usr_id,
				str_id,
				str_email,
				ord_rly_id as rly_id,
				ord_state_id as state,
				ord_wst_id as wst_id,
				ord_pay_id as pay_id
			from
				ord_orders
				left join dlv_stores on (
					str_tnt_id='.$config['tnt_id'].' and ord_str_id=str_id
				)
			where ord_tnt_id='.$config['tnt_id'].' and ord_masked = 0 and exists (
					select prd_ord_id from ord_bl_products where prd_tnt_id='.$config['tnt_id'].' and prd_ord_id=ord_id and prd_bl_id='.$bl.'
			)
		';

	if( $smart_sort==true ){
		$sql .= '
			order by (
				select count(*) from ord_bl_products where prd_tnt_id='.$config['tnt_id'].' and prd_bl_id='.$bl.' and prd_ord_id=ord_id
			) desc
		';
	}

	return ria_mysql_query( $sql );
}

// \cond onlyria
/**	Cette fonction permet la modification du statut d'un bl.
 *	Elle entraîne la modification du statut des commandes correspondantes,
 *	ainsi que l'envoi des notifications (si choisies par le client)
 *	Si le statut est inférieur ou égal au précédent, la fonction retournera True mais aucune notification ne sera envoyée
 *	@param int $bl Obligatoire, Identifiant du bl à modifier
 *	@param int $state Obligatoire, Nouvel état du bon de livraison, parmi les valeurs 6, 7, 8, 17, 19, 24, _STATE_INV_STORE
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_bl_state_set( $bl, $state ){

	global $config;

	if( !ord_bl_exists($bl) ) return false;
	if( !ord_states_exists($state) ) return false;

	$old_state = ord_bl_state_get($bl);

	$state_name = '';
	switch( $state ){
		case 6:
			$state_name = 'Bon de livraison, Saisi';
			break;
		case 7:
			$state_name = 'Bon de livraison, Confirmé';
			break;
		case 8:
			$state_name = 'Bon de livraison, A facturer';
			break;
		case 17:
			$state_name = 'Bon de livraison, Réceptionné';
			break;
		case 19:
			$state_name = 'Bon de livraison, Partiellement facturé';
			break;
		case 24:
			$state_name = 'Bon de livraison, Disponible en magasin';
			break;
		case _STATE_INV_STORE:
			$state_name = 'Bon de livraison, Retiré en magasin';
			break;
		default:
			error_log( 'Etat de BL non autorisé : '.$state );
			return false;
	}


	if( $orders = ord_bl_orders_get( $bl ) ){

		if( isset($config['bl_partiel_active']) && $config['bl_partiel_active']){
			$reliquats_ord_id = array();

			$rbl = ord_bl_get( $bl['id'] );
			if( $rbl && ria_mysql_num_rows($rbl) ){
				$bl = ria_mysql_fetch_assoc($rbl);
				$rdelayed = ord_products_get_delayed( $bl['usr_id'], array(), false, 0, 0 );
				if( $rdelayed && ria_mysql_num_rows($rdelayed) ){
					while($delayed = ria_mysql_fetch_assoc($rdelayed)){
						$reliquats_ord_id[] = $delayed['ord_id'];
					}
				}
			}
		}

		while( $r = ria_mysql_fetch_assoc($orders) ){
			// certains statuts ne peuvent pas être modifiés
			if( !in_array($r['state'], array(_STATE_SUPP_PARTIEL_LIVR, _STATE_CANCEL_USER, _STATE_CANCEL_MERCHAND)) ){

				$ord_state = $state;
				// dans le cas ou les bon de livraison partiel sont actif le status de la commande doit être adapté en fonction de l'état de livraison des commandes
				if( isset($config['bl_partiel_active']) && $config['bl_partiel_active']){
					// regarde dans les reliquats, si la commande est encore dedant la livraison est considéré comme partiel
					if( in_array($r['id'], $reliquats_ord_id) ){
						$ord_state = _STATE_BL_PARTIEL_EXP;
					}
				}

				ord_orders_update_status( $r['id'], $ord_state, $state_name );
			}
		}
	}

	// passage direct de 6 à 8
	if( $config['tnt_id']==1 && $state==8 && $old_state == 6 )
		ord_bl_state_set( $bl,7 );

	$sql = 'update ord_bl set bl_state_id='.$state.' where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl;

	$res = ria_mysql_query($sql);

	if( $state > $old_state || ( $state == 8 && $old_state == 19 ) ){
		ord_bl_notify( $bl );
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère le statut d'un bon de livraison.
 *	Au moins un des deux paramètres est obligatoire.
 *	@param int $bl_id Facultatif, identifiant du bon de livraison
 *	@param string $bl_piece Facultatif, numéro de pièce du bon de livraison
 *	@return int|bool L'identifiant du statut associé au bon de livraison, False en cas d'échec.
 */
function ord_bl_state_get( $bl_id=0, $bl_piece='' ){

	if( !is_numeric($bl_id) || $bl_id < 0 ){
		return false;
	}

	$bl_piece = trim($bl_piece);

	if( !$bl_piece && !$bl_id ){
		return false;
	}

	global $config;

	$sql = '
		select bl_state_id
		from ord_bl
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_masked = 0
	';

	if( $bl_piece ){
		$sql .= ' and bl_piece = "'.addslashes($bl_piece).'"';
	}

	if( $bl_id ){
		$sql .= ' and bl_id = '.$bl_id;
	}

	$res = ria_mysql_query($sql);

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result($res, 0, 0);

}
// \endcond

/**	Cette fonction retourne les codes de colis contenus dans un bon de livraison
 *	@param int $bl Obligatoire, Identifiant du bl dont on souhaite extraire les colis
 *	@return array un tableau à une dimension contenant les codes de colis
 */
function ord_bl_colis_get( $bl ){
	global $config;

	if( !ord_bl_exists($bl) ){
		return false;
	}

	$ar_colis = array();
	$colis = ria_mysql_query('
		select distinct prd_colis as colis
		from ord_bl_products
		where prd_tnt_id='.$config['tnt_id'].' and prd_bl_id='.$bl.' and prd_colis!=""
	');
	while( $c = ria_mysql_fetch_array($colis) ){
		$ar_colis[] = $c['colis'];
	}

	return $ar_colis;
}

/**	Cette fonction permet de mettre à jour le  numéro de colis sur une ligne de BL
 *	@param int $bl_id Identifiant du bl
 *	@param int $prd_id Identifiant du produit
 *	@param $line_id Identifiant du numéro de ligne
 *	@param $tracking_number numéro du colis à mettre à jour
 *	@return array un tableau à une dimension contenant les codes de colis
 */
function ord_bl_colis_set( $bl_id, $prd_id, $line_id, $tracking_number="" ){
	global $config;
	if( !is_numeric($bl_id) ) return false;
	if( !is_numeric($prd_id) ) return false;
	if( !is_numeric($line_id) ) return false;

	$sql = '
		update ord_bl_products
		set prd_colis = \''.addslashes($tracking_number).'\'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_id = '.$prd_id.'
			and prd_line_id = '.$line_id.'
			and prd_bl_id = '.$bl_id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	ord_bl_set_date_modified($bl_id);

	return $res;
}

// \cond onlyria
/**	Cette fonction est chargée de l'envoi d'un avis de bon de livraison
 *	@param int $id Identifiant du bon de livraison
 *	@param $from_new_bl Détermine si la notification fait suite à un bon de livraison nouvellement crée
 *	@param $force_age Optionnel. Si activé, la vérification de l'age du bon de livraison ne sera pas faite
 *
 *	@return bool true en cas de succès ou si l'utilisateur a demandé à ne pas en être notifié
 *	@return bool false en cas d'échec
 */
function ord_bl_notify( $id, $from_new_bl=false, $force_age=false ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	// Charge le BL
	$rbl = ord_bl_get( $id );
	if( !$rbl || !ria_mysql_num_rows($rbl) ){
		return false;
	}
	$bl = ria_mysql_fetch_assoc($rbl);

	global $config;

	$wst_id = $config['wst_id'];

	$no_ord = true;

	// Charge les commandes d'origine
	$orders = ord_bl_orders_get( $id );
	if( $orders && ria_mysql_num_rows($orders) ){
		$tmp_wst_id = ria_mysql_result($orders, 0, 'wst_id');
		if( wst_websites_exists( $tmp_wst_id ) ){
			$wst_id = $tmp_wst_id;
			$no_ord = false;
		}
		ria_mysql_data_seek($orders, 0);
	}

	if( $config['tnt_id'] == 19 && $no_ord ){
		return false;
	}

	$config_copy = $config;

	// Charge la configuration issue de la commande, si différente de celle de base
	if( $wst_id != $config['wst_id'] ){
		$ar_to_reload = array(
			'email_html_footer', 'site_url', 'contact_page_url', 'site_have_user_space', 'site_name', 'ord_notify_only_web',
			'site_have_user_histo', 'show_notify_code_discount', 'show_port_in_order_confirmation',
			'show_price_in_weight_unit', 'weight_col_calc_lines', 'show_barcode_in_order_notify', 'days_bl_notify',
			'email_html_header_order', 'email_html_header', 'site_dir', 'email_bcc', 'email_alerts_enabled', 'doc_dir', 'active_email_perso',
		);
		if( $rconf = cfg_overrides_get( $wst_id, array(), $ar_to_reload ) ){
			while( $conf = ria_mysql_fetch_assoc($rconf) ){
				$config[ $conf['code'] ] = $conf['value'];
			}
		}
		$config['wst_id'] = $wst_id;
	}

	if (isset($config['ctr_order_notify']) && !$config['ctr_order_notify']) {
		$mkt_users = ctr_marketplaces_get_users();
		if (is_array($mkt_users)) {
			if (in_array($bl['usr_id'], $mkt_users)) {
				return true;
			}
		}
	}

	// récupère le pay_id, pour déterminer si au moins une commande d'origine est web
	$have_pay_id = false;
	if( $orders && ria_mysql_num_rows($orders) ){
		while( $o = ria_mysql_fetch_assoc($orders) ){
			if( is_numeric($o['pay_id']) && $o['pay_id'] > 0 ){
				$have_pay_id = true;
				break;
			}
		}
		ria_mysql_data_seek($orders, 0);
	}

	// Envoi de la notification que lors de la création
	if (isset($config['bl_notify_only_create']) && $config['bl_notify_only_create']) {
		if (!$from_new_bl) {
			return true;
		}
	}

	// bl issus de commandes web uniquement
	if( $config['ord_notify_only_web'] && !$have_pay_id ){
		$config = $config_copy;
		return true;
	}

	if( in_array($config['tnt_id'], array(8, 24)) ){
		$config = $config_copy;
		return true;
	}

	if( !$config['email_alerts_enabled'] && $bl['state_id'] != 17 ){
		$config = $config_copy;
		return true;
	}

	// Ne notifie pas les bl de plus de $config['days_bl_notify'] jours
	if( $bl['age'] > $config['days_bl_notify'] && !$force_age ){
		$config = $config_copy;
		return true;
	}

	// Guery : la catégorie client doit être Poubelle-Pro ou Mobilier de ville
	if( $config['tnt_id'] == 27 && !$have_pay_id ){
		$cat_cli = strtoupper(trim(fld_object_values_get( $bl['usr_id'], 2922, '', false, true )));
		if( !in_array($cat_cli, array('POUBELLE PRO', 'MOBILIER DE VILLE')) ){
			$config = $config_copy;
			return true;
		}
	}

	// si sodipec on retire la ref de la commande pour la notification
	if( $config['tnt_id'] == 21 ) $bl['ref'] = '';

	$usr = ria_mysql_fetch_array(gu_users_get($bl['usr_id']));
	if( !$usr['email'] ){
		$config = $config_copy;
		return false;
	}

	// hack pour certains locataires (qui ne respectent pas les états de BL Sage)
	$no_state8 = array( 6, 3, 14, 19, 39, 43 );
	if( in_array( $config['tnt_id'], $no_state8 ) && $bl['state_id'] == 8 ){
		$config = $config_copy;
		return true;
	}

	// Revendeurs Proloisirs : envoie d'une notification au client final (que le revendeur soit abonné à la notification ou non)
	if( $config['tnt_id']==4 && isset($config['fld_ord_related']) ){
		$ord_to_notify = array();
		if( $rord = ord_bl_orders_get( $bl['id'] ) ){
			while( $o = ria_mysql_fetch_array($rord) ){
				if( $rv = fld_fields_get_objects( $config['fld_ord_related'], $o['id'] ) ){
					if( ria_mysql_num_rows($rv) ){
						$v = ria_mysql_result($rv, 0, 'obj_id');
						if( is_numeric($v) && $v>0 )
							$ord_to_notify[] = $v;
					}
				}else{
					error_log( __FILE__.':'.__LINE__.' Impossible de charger fld_fields_get_objects( '.$config['fld_ord_related'].', '.$o['id'].' ).' );
				}
			}
		}else{
			error_log( __FILE__.':'.__LINE__.' Impossible de charger les commandes pour le bl '.$bl['id'] );
		}
		if( sizeof($ord_to_notify) ){
			$ord_to_notify = array_unique($ord_to_notify);
			require_once('/var/www/www.proloisirs.fr/htdocs/include/view.emails.inc.php');
			foreach( $ord_to_notify as $otn ){
				if( !proloisirs_ord_expedition_notify( $otn, $bl['id'] ) ){
					error_log( __FILE__.':'.__LINE__.' Echec de l\'envoi de la notification (bl revendeur '.$bl['id'].', commande client '.$otn.')' );
				}
			}
		}
	}

	// Si l'utilisateur à choisi de ne pas recevoir d'alerte pour cet état de commande,
	// arrête l'exécution mais retourne tout de même true
	if( !gu_ord_alerts_exists($bl['usr_id'],$bl['state_id']) ){
		$config = $config_copy;
		return true;
	}

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = false;
	switch( $bl['state_id'] ){
		case 7 :
		case 24 :
			$rcfg = cfg_emails_get('ord-shipped', $wst_id);
			break;
		// case _STATE_INV_STORE
	}

	if( !$rcfg || !ria_mysql_num_rows($rcfg) )
		$rcfg = cfg_emails_get('ord-alert', $wst_id);

	if( !ria_mysql_num_rows($rcfg) ){
		$config = $config_copy;
		return false;
	}
	$cfg = ria_mysql_fetch_array($rcfg);

	$mkt_users = ctr_marketplaces_get_users();
	if (is_array($mkt_users)) {
		if (in_array($bl['usr_id'], $mkt_users)) {
			$cfg['bcc'] = preg_replace("/(.*)@invite.trustpilot.com/", "", $cfg['bcc']);
		}
	}

	// Crée le message
	$usr = ria_mysql_fetch_array(gu_users_get($bl['usr_id']));
	$email = new Email();
	$email->setFrom( $cfg['from'] );

	$ar_users = array();
	if( isset($config['notify_ord_user']) && $config['notify_ord_user'] ){

		$r_ord_bl = ord_bl_orders_get( $bl['id'] );
		if( $r_ord_bl ){
			while( $ord_bl = ria_mysql_fetch_assoc($r_ord_bl) ){
				if( !is_numeric($ord_bl['usr_id']) || $ord_bl['usr_id'] <= 0 ){
					continue;
				}

				$r_tmp_usr = gu_users_get( $ord_bl['usr_id'] );
				if( $r_tmp_usr && ria_mysql_num_rows($r_tmp_usr) ){
					$tmp_usr = ria_mysql_fetch_assoc( $r_tmp_usr );
					$ar_users[] = $tmp_usr['email'];
				}
			}
		}
	}

	if( !sizeof($ar_users) ){
		$ar_users[] = $usr['email'];
	}

	foreach( $ar_users as $one_email ){
		$email->addTo( $one_email );
	}

	if( $cfg['bcc'] ) $email->addBcc( $cfg['bcc'] );
	if( $cfg['reply-to'] ) $email->setReplyTo( $cfg['reply-to'] );
	//if( $config['email_bcc'] ) $email->addBcc( $config['email_bcc'] );
	if( $usr['alert_cc'] ) $email->addCc( $usr['alert_cc'] );

	if( $config['tnt_id'] == 19 ){
		require_once( $config['site_dir'].'/include/view.emails.inc.php' );
		$send_bl_notification = send_bl_notification( $email, $usr, $bl, $orders );
		$config = $config_copy;
		return $send_bl_notification;
	}

	// Définit le sujet du message
	switch( $bl['state_id'] ){
		case 6:
			$email->setSubject( 'Votre commande '.$config['site_name'].' '.$bl['ref'].' est prête à être expédiée' );
			break;
		case 7:
			$email->setSubject( 'Avis d\'expédition de votre commande '.$config['site_name'].' '.$bl['ref'] );
			break;
		case 24:
			$email->setSubject( 'Votre commande '.$config['site_name'].' '.$bl['ref'].' est disponible en magasin' );
			break;
		//case _STATE_INV_STORE
		default:
			$email->setSubject( 'Votre commande '.$config['site_name'].' '.$bl['ref'] );
			break;
	}

	// Gestion des notifications personnalisées
	if (isset($config['active_email_perso']) && $config['active_email_perso']) {
		$file_notify_exists = false;

		$file_emails_perso = $config['site_dir'].'/include/view.emails.inc.php';
		if (file_exists($file_emails_perso)) {
			$file_notify_exists = true;
		} else {
			$file_emails_perso = $config['site_dir'].'/../include/view.emails.inc.php';

			if (file_exists($file_emails_perso)) {
				$file_notify_exists = true;
			}
		}

		if ($file_notify_exists) {
			require_once($file_emails_perso);

			if (function_exists('riashop_bl_notify')) {
				$notify_invoice = riashop_bl_notify($email, $cfg, $bl, $usr, $orders);
				$config = $config_copy;
				return $notify_invoice;
			}
		}
	}

	// Modification du sujet de l'accusé de livraison pour l'Extranet Proloisirs
	if( $config['tnt_id'] == 4 && $config['wst_id'] == 8 ){
		switch($bl['state_id']){
			case 7 : {
				$email->setSubject( 'Avis d\'expédition de votre commande PROLOISIRS '.$bl['ref'] );
				break;
			}
			case 8 : {
				$email->setSubject( 'Accusé de livraison de votre commande PROLOISIRS '.$bl['ref'] );
			}
		}
	}

	// notification spé "On en fait des tonnes"
	if ($config['tnt_id'] == 51) {
		if ( file_exists($config['site_dir'].'/include/view.emails.inc.php') ) {
			require_once($config['site_dir'].'/include/view.emails.inc.php');
			if ( function_exists('idmat_custom_bl_notify') ) {
				$custom_bl_notify = idmat_custom_bl_notify($email, $bl, $usr, $orders);
				$config = $config_copy;
				return $custom_bl_notify;
			}
		}

		return false;
	}

	// notification spé graphicbiz
	if( $config['tnt_id'] == 40 ){
		require_once( $config['site_dir'].'/include/view.emails.inc.php');
		$send = extranet_graphicbiz_notify_bl( $email, $bl, $usr, $orders );
		$config = $config_copy;
		return $send;
	}

	// notification spé bigship site public
	if( $config['tnt_id'] == 1 && $config['wst_id'] == 1 && (isset($config['bigship_prod']) && $config['bigship_prod'] || isset($config['bigship_emails_test']) && in_array($usr['email'], $config['bigship_emails_test'])) ) {
		require_once( $config['site_dir'].'/include/view.emails.inc.php');
		$send = bigship_notify_bl( $email, $bl, $usr, $orders );
		$config = $config_copy;
		return $send;
	}

	// Composition
	$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );
	$email->addParagraph('Cher client, chère cliente,');
	$email->addBlankTextLine();

	$store_id = false; $rly_id=false;
	if( ria_mysql_num_rows($orders)==1 ){
		$ord = ria_mysql_fetch_array($orders);

		// si sodipec on retire la ref de la commande pour la notification
		if( $config['tnt_id'] == 21 ) $ord['ref'] = '';

		if( $config['tnt_id']==4 && $config['wst_id']==8 ){
			if( $bl['state_id'] != 8 ){
				$email->addParagraph('Le statut de votre commande '.( $ord['ref'] ? $ord['ref'] : $ord['id'] ).', enregistrée le '.$ord['date'].', a été modifié par notre service client.'."\n".'Veuillez trouver ci-dessous le détail de l\'opération :');
			}
		}else{
			$email->addParagraph('Le statut de votre commande No '.( $ord['ref'] ? $ord['ref'] : $ord['id'] ).', enregistrée le '.$ord['date'].', a été modifié par notre service client. Veuillez trouver ci-dessous le détail de l\'opération :');
		}
		if( $ord['str_email'] )
			$email->addBcc( $ord['str_email'] );
		if( $ord['str_id'] )
			$store_id = $ord['str_id'];
		if( $ord['rly_id'] ){
			$rly_id = $ord['rly_id'];
		}
	}elseif( ria_mysql_num_rows($orders)>1 ){
		$ar_orders = array();
		while( $ord = ria_mysql_fetch_array($orders) ){
			$ar_orders[] = $ord['ref'];
			if( $ord['str_email'] )
				$email->addBcc( $ord['str_email'] );
			if( $ord['str_id'] )
				$store_id = $ord['str_id'];
			if( $ord['rly_id'] ){
				$rly_id = $ord['rly_id'];
			}
		}
		if( $config['tnt_id']==4 && $config['wst_id']==8 ){
			if( $bl['state_id'] != 8 ){
				$email->addParagraph('Le statut de vos commandes '.implode(', ',$ar_orders).' a été modifié par notre service client.'."\n".'Veuillez trouver ci-dessous le détail de l\'opération :');
			}
		}else{
			$email->addParagraph('Le statut de vos commandes '.implode(', ',$ar_orders).' a été modifié par notre service client. Veuillez trouver ci-dessous le détail de l\'opération');
		}
	}

	switch( $bl['state_id'] ){
		case 6:
			$email->addParagraph('Le traitement de votre commande est terminé.'."\n".'Les articles que vous avez commandé sont désormais prêts à vous être expédiés.');
			break;
		case 7:
			$email->addHtml('
				<!--
					<script type="application/json+trustpilot">
						{
							"recipientEmail":"'.$usr['email'].'",
							"recipientName":"'.$usr['adr_lastname'].' '.$usr['adr_firstname'].'",
							"referenceId":"'.$bl['piece'].'"
						}
					</script>
				-->
			');
			$srv = false;

			if( $bl['srv_id'] ){
				$srv = ria_mysql_fetch_assoc(dlv_services_get($bl['srv_id']));
			}

			if( !($config['tnt_id'] == 4 && $config['wst_id'] == 8) ){
				if( $bl['srv_id'] && is_array($srv) ){
					if( $srv['url-site'] ){
						$srv_name = '<a href="'.$srv['url-site'].'">'.htmlspecialchars($srv['name']).'</a>';
					}else{
						$srv_name = htmlspecialchars($srv['name']);
					}
					$email->addParagraph( str_replace( '%nom%', $srv_name, $srv['alert-msg'] ) );
				}else{
					$email->addParagraph('Votre commande vient d\'être expédiée.'."\n".'Elle a été prise en charge par notre transporteur.');
				}
			}

			// Numéros de colis
			$colis = ord_bl_colis_get($bl['id']);
			$have_links = false;

			for( $i=0; $i<sizeof($colis); $i++ ){

				if( $bl['srv_id'] && is_array($srv) && isset($srv['url-colis']) ){
					$url_suivi = '';

					if( strpos($srv['url-colis'], '#param[colis]#')!==false ){
						if( $rly_id ){
							$rrly = dlv_relays_get_simple( $rly_id );
							if( $rrly && ria_mysql_num_rows($rrly) ){
								$rly = ria_mysql_fetch_assoc( $rrly );

								$tmp = str_replace(
									array('#param[colis]#', '#param[zipcode]#'),
									array($colis[$i], $rly['zipcode']),
									$srv['url-colis']
								);

								$url_suivi = '<a href="'.$tmp.'">'.$colis[$i].'</a>';
							}
						}
					}else{
						$url_suivi = '<a href="'.$srv['url-colis'].$colis[$i].'">'.$colis[$i].'</a>';
					}

					if( trim($url_suivi) ){
						$colis[$i] = $url_suivi;
						$have_links = true;
					}
				}
			}

			switch( sizeof($colis) ){
				case 0:
					break;
				case 1:
					if( $config['tnt_id']==4 && $config['wst_id']==8 ){
						$email->addParagraph('Votre commande porte le n° de colis suivant chez notre transporteur : '.$colis[0]);
					}else{
						$email->addParagraph('Votre commande porte le numéro de colis suivant chez notre transporteur : '.$colis[0]);
					}
					break;
				default:
					$email->addParagraph('Votre commande à été expédiée en plusieurs colis.'."\n".'Voici leur références chez notre transporteur : '.implode(', ',$colis));
					break;
			}
			break;
		case 8:
			if( in_array($config['tnt_id'], array(4, 5, 29)) ){
				$email->addParagraph('Nous avons confié votre colis à notre transporteur.');
			}else{
				$email->addParagraph('Nous avons été informés par notre transporteur que votre commande vous a bien été livrée.');
			}
			break;
		case 24:
			$txt = 'Votre commande est disponible au magasin :'."\n";
			if( $store_id ){

				$str = ria_mysql_fetch_array(dlv_stores_get( $store_id, null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ));
				$txt .= $str['name']."\n";
				$txt .= $str['address1']."\n";
				if( trim($str['address2']) ) $txt .= $str['address2']."\n";
				$txt .= $str['zipcode'].' '.$str['city']."\n";
				$txt .= $str['country']."\n";
				if( trim($str['phone']) ) $txt .= 'Tél. : '.$str['phone']."\n";

			}
			$email->addParagraph( $txt );
			break;
		//case _STATE_INV_STORE
	}

	// Rappel du contenu de la préparation de livraison
	$email->addBlankTextLine();
	switch( $bl['state_id'] ){
		case 6:
			$email->addParagraph('Veuillez trouver ci-dessous le détail des articles prêts à vous être expédiés :');
			break;
		case 7:
			if( $config['tnt_id'] == 4 && $config['wst_id'] == 8 ){
				$email->addParagraph('Veuillez trouver ci-dessous le rappel du contenu de votre commande :');
			}else{
				$email->addParagraph('Veuillez trouver ci-dessous le détail des produits expédiés :');
			}
			break;
		case 24:
			$email->addParagraph('Veuillez trouver ci-dessous le détail des produits disponibles :');
			break;
		//case _STATE_INV_STORE
		default:
			if( $config['tnt_id'] == 4 && $config['wst_id'] == 8 ){
				$email->addParagraph('Veuillez trouver ci-dessous le rappel du contenu de votre commande :');
			}else{
				$email->addParagraph('Veuillez trouver ci-dessous le rappel des produits livrés :');
			}
	}

	$show_ean = isset($config['show_barcode_in_order_notify']) && $config['show_barcode_in_order_notify'];

	$cellpadding = 5;

	if( $show_ean ){
		$cellpadding = $cellpadding + 1;
	}

	// Tableau récapitulatif
	$email->openTable();
	$email->openTableRow();
	$email->addCell( 'Ref' );
	$email->addCell( 'Désignation' );
	if( $show_ean )
		$email->addCell( 'Code EAN' );

	if( $usr['prf_id']==PRF_CUSTOMER ){
		$email->addCell( 'Prix TTC' );
		$email->addCell( 'Qté' );
		$email->addCell( 'Total TTC' );
	}else{
		$email->addCell( 'Prix HT' );
		$email->addCell( 'Qté' );
		$email->addCell( 'Total HT' );
	}

	$email->closeTableRow();

	$ratio = isset( $config['weight_col_calc_lines'] ) && is_numeric( $config['weight_col_calc_lines'] ) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;
	$unit = 'Gr';
	if( $ratio==1000 )
		$unit = 'Kg';
	elseif( $ratio==100000 )
		$unit = 'Qt';
	elseif( $ratio==1000000 )
		$unit = 'Tn';

	$all_prd_for_doc = array();

	// Insère les lignes de commandes, avec une rupture par commande d'origine
	$prd_calc_disc_array = array();
	$port_ht = $port_ttc = 0;
	$total_ecotaxe = 0;

	$colis = ord_bl_colis_get($bl['id']);
	foreach( $colis as $c ){
		$email->openTableRow();
		$email->addCell( '<strong>Colis '.$c.'</strong>', 'left', $cellpadding );
		$email->closeTableRow();

		$products = ord_bl_products_get($bl['id'],0,$c);
		while( $p = ria_mysql_fetch_array($products) ){
			if( prd_products_is_port($p['ref']) ){
				$port_ttc += $p['price_ttc'];
				$port_ht += $p['price_ht'];
			}else{
				$email->openTableRow();
				$email->addCell( $p['ref'], 'right' );
				$email->addCell( $p['name'] );
				if( $show_ean ){
					$barcode = prd_products_get_barcode( $p['id'] );
					$barcode = !$barcode ? '' : $barcode;
					$email->addCell( $barcode );
				}

				$qte_poids = null;
				if( $p['sell_weight'] && $p['weight_net'] && $config['show_price_in_weight_unit'] )
					$qte_poids = $p['weight_net'] / $ratio;

				$suffix = $usr['prf_id']==PRF_CUSTOMER ? 'ttc' : 'ht';

				if( $qte_poids!==null )
					$email->addCell( number_format($p['total_'.$suffix] / $qte_poids,2,',',' '), 'right' );
				else
					$email->addCell( number_format($p['price_'.$suffix],2,',',' '), 'right' );

				if( $qte_poids!==null )
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($qte_poids,3,',',' ') ).'&nbsp;'.$unit, 'right' );
				elseif( $p['sell_weight'] && $p['weight_net'] )
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['weight_net'] / $ratio,3,',',' ') ).'&nbsp;'.$unit, 'right' );
				else
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'],0,',',' ') ), 'right' );

				$email->addCell( number_format($p['total_'.$suffix],2,',',' '), 'right' );

				if( array_key_exists( $p['id'], $prd_calc_disc_array ) ){
					$prd_calc_disc_array[$p['id']] += $p['total_ttc'];
				}else{
					$prd_calc_disc_array[$p['id']] = $p['total_ttc'];
				}

				$email->closeTableRow();
				$all_prd_for_doc[] = $p['id'];

				//ajout du total de l'eco-taxe
				$total_ecotaxe += $p['ecotaxe'] * $p['qte'];
			}
		}
	}

	// Traite les lignes de bl dans la commande d'origine n'est pas disponible
	//$products = ord_bl_products_get($bl['id'],'null');
	$products = ord_bl_products_get($bl['id'],0,'');
	if( $products && ria_mysql_num_rows($products) ){
		while( $p = ria_mysql_fetch_array($products) ){
			if( !prd_products_is_port($p['ref']) ){
				if( sizeof($colis)>0 ){
					$email->openTableRow();
					$email->addCell( '<strong>Autres</strong>', 'left', $cellpadding );
					$email->closeTableRow();
				}

				break;
			}
		}

		ria_mysql_data_seek( $products, 0 );
		while( $p = ria_mysql_fetch_array($products) ){
			if( prd_products_is_port($p['ref']) ){
				$port_ttc += $p['price_ttc'];
				$port_ht += $p['price_ht'];
			}else{
				$email->openTableRow();
				$email->addCell( $p['ref'], 'right' );

				$email->addCell( $p['name'] );
				if( $show_ean ){
					$barcode = prd_products_get_barcode( $p['id'] );
					$barcode = !$barcode ? '' : $barcode;
					$email->addCell( $barcode );
				}

				$qte_poids = null;
				if( $p['sell_weight'] && $p['weight_net'] && $config['show_price_in_weight_unit'] )
					$qte_poids = $p['weight_net'] / $ratio;

				$suffix = $usr['prf_id']==PRF_CUSTOMER ? 'ttc' : 'ht';

				if( $qte_poids!==null )
					$email->addCell( number_format($p['total_'.$suffix] / $qte_poids,2,',',' '), 'right' );
				else
					$email->addCell( number_format($p['price_'.$suffix],2,',',' '), 'right' );

				if( $qte_poids!==null )
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($qte_poids,3,',',' ') ).'&nbsp;'.$unit, 'right' );
				elseif( $p['sell_weight'] && $p['weight_net'] )
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['weight_net'] / $ratio,3,',',' ') ).'&nbsp;'.$unit, 'right' );
				else
					$email->addCell( str_replace( ' ', '&nbsp;', number_format($p['qte'],0,',',' ') ), 'right' );

				$email->addCell( number_format($p['total_'.$suffix],2,',',' '), 'right' );

				if( array_key_exists( $p['id'], $prd_calc_disc_array ) ){
					$prd_calc_disc_array[$p['id']] += $p['total_ttc'];
				}else{
					$prd_calc_disc_array[$p['id']] = $p['total_ttc'];
				}

				$email->closeTableRow();
				$all_prd_for_doc[] = $p['id'];

				//ajout du total de l'eco-taxe
				$total_ecotaxe += $p['ecotaxe'] * $p['qte'];
			}
		}
	}

	$colspan = 4;
	if( $show_ean )
		$colspan += 1;

	if( $config[ 'show_port_in_order_confirmation' ] ){

		$val_promo = fld_object_values_get( $bl['id'], FLD_PMT_BL );
		if( $val_promo!==false && is_numeric($val_promo) && $val_promo>0 ){
			if( $rpmt = pmt_codes_get($val_promo) ){
				if( $pmt = ria_mysql_fetch_array($rpmt) ){
					// Code promotion
					$email->openTableRow();
					$email->addCell( 'Code promotion :', 'right', $colspan );
					$email->addCell( $pmt['code'], 'right', 1, true );
					$email->closeTableRow();

					// Affichage du montant de la promotion [TTC], si activé
					if( $config['show_notify_code_discount'] ){
						// calcule la remise
						$remise = 0;
						if( $pmt['discount_type']==0 ){
							$remise = $pmt['discount']; // remise en montant
						}else{
							// remise en pourcentage
							// on calcule d'abord le montant brut sur lequel la remise s'applique
							$total_brut_ttc = 0;
							foreach( $prd_calc_disc_array as $pid=>$totttc ){
								if( pmt_products_is_included( $pmt, $pid ) ){
									$total_brut_ttc += $totttc;
								}
							}
							$remise = $total_brut_ttc * ($pmt['discount'] / 100);
						}
						// s'il y a bien une remise
						if( $remise>0 ){
							$rem_texte = number_format($remise,2,',',' ');
							if( $pmt['discount_type']==1 )
								$rem_texte = number_format($pmt['discount'],2,',',' ').' %, soit '.$rem_texte; // remise en pourcentage, le texte change légèrement
							$email->openTableRow();
							$email->addCell( 'Remise TTC :', 'right', $colspan );
							$email->addCell( '&nbsp;'.$rem_texte, 'right', 1, true );
							$email->closeTableRow();

							$email->openTableRow();
							$email->addCell( 'Total net TTC :', 'right', $colspan );
							$email->addCell( number_format($bl['total_ttc']-$port_ttc,2,',',' '), 'right', 1, true );
							$email->closeTableRow();
						}
					}
				}
			}
		}

		if( $config['tnt_id'] == 4 && $config['wst_id'] == 8 ){
			// Ecotaxe
			if( $total_ecotaxe > 0 ){
				$email->openTableRow();
				$email->addCell( 'Ecotaxe :', 'right', $colspan );
				$email->addCell( number_format($total_ecotaxe,2,',',' '), 'right', 1, true );
				$email->closeTableRow();
			}
		}

		// Frais de port
		$email->openTableRow();
		$email->addCell( 'Frais de port :', 'right', $colspan );
		$email->addCell( number_format($usr['prf_id']==PRF_CUSTOMER ? $port_ttc : $port_ht,2,',',' '), 'right', 1, true );
		$email->closeTableRow();
	}

	if( $usr['prf_id']!=PRF_CUSTOMER ){
		$price_ht = $bl['total_ht'];
		$tva = $bl['total_ttc'] - $bl['total_ht'];

		if( $config['tnt_id'] == 4 && $config['wst_id'] == 8 ){
			$price_ht += $total_ecotaxe;
			$tva -= $total_ecotaxe;
		}

		// Total HT
		$email->openTableRow();
		$email->addCell( 'Total HT :', 'right', $colspan );
		$email->addCell( number_format($price_ht,2,',',' '), 'right', 1, true );
		$email->closeTableRow();

		// Tva
		$email->openTableRow();
		$email->addCell( 'TVA :', 'right', $colspan );
		$email->addCell( number_format($tva,2,',',' '), 'right', 1, true );
		$email->closeTableRow();
	}

	// Total TTC
	$email->openTableRow();
	$email->addCell( 'Total TTC :', 'right', $colspan );
	$email->addCell( number_format($bl['total_ttc'],2,',',' '), 'right', 1, true );
	$email->closeTableRow();

	$email->closeTable();
	$email->addBlankTextLine();

	if( $config['tnt_id']==4 && $config['wst_id']==8 ){
		// Spécialisation Extranet Proloisirs
		$email->addParagraph('Votre commande porte le n°'.$bl['piece'].' dans notre gestion commerciale.'."\n".'Votre compte client porte le n°'.$usr['ref'].'.'."\n".' Merci d\'y faire référence si vous souhaitez nous interroger sur le contenu de cet email.');
	}else{
		// Numéro de pièce interne
		$email->addParagraph('Cette alerte email porte le numéro de pièce '.$bl['piece'].' dans notre gestion commerciale.'."\n".'Votre compte client porte le numéro '.$usr['ref'].'.'."\n".' Merci d\'y faire référence si vous souhaitez nous interroger sur le contenu de cet email.');
	}

	// Rappel suivi possible
	if( $config['site_have_user_histo'] ){
		$email->addBlankTextLine();

		if( !($config['tnt_id']==4 && $config['wst_id']==8 && $bl['state_id']==8) ){
			$email->addParagraph( 'Vous pouvez également suivre l\'avancement de votre commande depuis votre espace client à l\'adresse suivante : <a href="'.$config['site_url'].'/mon-compte/commandes">'.$config['site_url'].'/mon-compte/commandes</a>.');
		}
	}
	$email->addBlankTextLine();

	$email->addParagraph( "Cordialement,\nL'équipe ".$config['site_name'].'.' );

	if( $config['site_have_user_space'] ){
		// Rappel configuration possible
		$email->addHorizontalRule();
		$email->addParagraph('Cet email vous a été envoyé pour vous tenir informé(e) de l\'avancement de votre commande. Si vous jugez ce message inutile, vous pouvez choisir de ne plus le recevoir en utilisant la fonction \'Mes options\' de votre espace client : <a href="'.$config['site_url'].'/mon-compte/mes-options">'.$config['site_url'].'/mon-compte/mes-options</a>.');
		$email->addBlankTextLine();
	}

	// Rappel contact possible
	$email->addHorizontalRule();
	$email->addBlankTextLine();
	if($config['tnt_id'] == 36){
		$email->addParagraph('Nous sommes en permanence à l\'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n\'hésitez pas à nous contacter : <a href="'.$config['contact_page_url'].'">'.$config['contact_page_url'].'</a>');
	}
	else{
		$email->addParagraph('Nous sommes en permanence à l\'écoute de vos attentes. Pour toutes suggestions sur les emails qui vous sont envoyés ou pour tout autre sujet, n\'hésitez pas à nous contacter : <a href="'.$config['site_url'].$config['contact_page_url'].'">'.$config['site_url'].$config['contact_page_url'].'</a>');
	}

	$email->addHtml( $config['email_html_footer'] );

	// envoi des documents rattachés (FDS, type_id = 1)
	if( $config['tnt_id'] == 2 ){
		$check_doc = array(); // empêche les doublons de documents
		foreach( $all_prd_for_doc as $prd_doc ){
			if( $rdoc = doc_products_get( 0, $prd_doc, 1 ) ){
				while( $d = ria_mysql_fetch_assoc($rdoc) ){
					if( !in_array($d['doc_id'], $check_doc) ){
						$email->addAttachment( $config['doc_dir'].'/'.$d['doc_id'], urlalias($d['doc_name']).'.'.end(explode('.', $d['doc_filename'])) );
						$check_doc[] = $d['doc_id'];
					}
				}
			}
		}
	}

	//ajout du bl en pdf pour Boero
	if( $config['tnt_id']==2 && in_array($bl['state_id'], array(_STATE_BL_EXP, _STATE_INVOICE))){
		$dir = $config['doc_dir'].'/bl-pdf/'.$bl['id'].'.pdf';
		require_once( $config['site_dir'].'/include/bl-pdf.php' );
		$res = create_bl_pdf($bl['id'], $dir);

		if($res!=false && trim($res)!=''){
			$email->addAttachment($dir);
		}
	}

	$email_send = $email->send();

	$config = $config_copy;
	return $email_send;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'un bon de livraison dans la boutique.
 *	@param int $bl Obligatoire, Identifiant de bon de livraison à supprimer.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_bl_del( $bl ){
	global $config;

	if( !is_numeric($bl) || $bl <= 0 ){
		return false;
	}

	ria_mysql_query('
		delete from ord_bl_products
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_bl_id = '.$bl.'
	');

	return ria_mysql_query('
		update ord_bl
		set bl_masked = 1
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_id = '.$bl.'
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un bon de livraison.
 *	@param int $id Identifiant ou tableau d'identifiants de bon de livraison.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_bl_set_date_modified( $id ){

	$id = control_array_integer( $id );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		update ord_bl
		set bl_date_modified = now()
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_id in ('.implode(', ' , $id).')
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le service de livraison du bon.
 *
 *	@param int $id Identifiant ou tableau d'identifiants de bon de livraison.
 *	@param int $srv_id Identifiant de service de livraison.
 *
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function ord_bl_set_srv_id($id, $srv_id)
{
	$id = control_array_integer($id);
	if ($id === false) {
		return false;
	}

	if (!is_numeric($srv_id)) {
		return false;
	}

	global $config;

	$sql = '
		update ord_bl
		set bl_srv_id = ' . intval($srv_id)  . '
		where bl_tnt_id = ' . $config['tnt_id'] . '
			and bl_id in (' . implode(', ', $id) . ')
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**
 * Cette fonction permet de mettre à jours l'adresse de livraison lié a un bon de livraison
 *
 * @param integer $bl_id Identifiant du bon de livraison BL
 * @param integer $adr_dlv_id Identifiant de l'adresse de livraison adr_id
 * @return bool retourne true si succès de la mise à jour, false dans le cas contraire.
 */
function ord_bl_set_adr_dlv_id($bl_id, $adr_dlv_id) {
	if (!is_numeric($bl_id) || $bl_id <= 0) {
		throw new InvalidArgumentException("bl_id doit être un entier supérieur à 0");
	}

	if (!is_null($adr_dlv_id) && (!is_numeric($adr_dlv_id) || $adr_dlv_id <= 0)) {
		throw new InvalidArgumentException("adr_dlv_id doit être un entier supérieur à 0");
	}

	global $config;

	$update = '
		update ord_bl
			set bl_adr_dlv_id = '.(is_null($adr_dlv_id) ? 'null' : $adr_dlv_id).'
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_id='.$bl_id.'
	';

	return ria_mysql_query($update);
}
// \endcond

// \cond onlyria
/**	Permet l'ajout d'une ligne de bl
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $prd Obligatoire, Identifiant interne du produit
 *	@param int $line Obligatoire, Identifiant de ligne permettant d'ajouter plusieurs fois le même produit sur des lignes différentes.
 *	@param string $ref Obligatoire, Référence du produit
 *	@param string $name Obligatoire, Désignation du produit
 *	@param int $qte Obligatoire, Quantité commandée
 *	@param float $price_ht Obligatoire, Prix HT du produit
 *	@param float $tva Obligatoire, taux de tva appliqué au produit, écrit sous la forme 1.000 (ex: 1.196)
 *	@param int $ord Obligatoire, Identifiant de la commande à l'origine de cette préparation de livraison
 *	@param string $colis Obligatoire, Numéro de colis chez le transporteur
 *	@param float $price_ttc Optionnel, Prix TTC du produit
 *	@param float $ecotaxe Optionnel, Eco-participation unitaire
 *	@param bool $prd_deleted Optionnel, Recherche dans les produits supprimés
 *	@param bool $update_totals Optionnel, Permet de ne pas recalculé les totaux
 *	@param int $parent Optionnel, Identifiant d'un produit parent
 *	@param int $child_line Optionnel, Identifiant de ligne pour les produits enfants
 *	@param $position Optionnel, Position de la ligne produit dans le bon de livraison
 *	@param $cod_id Optionnel, Identifiant de promotion
 *  @param $col_id Optionnel, Identifiant de collisage
 *
 *	@return bool True en cas de succès
 *	@return bool False en cas d'échec
 */
function ord_bl_products_add_sage( $bl, $prd, $line, $ref, $name, $qte, $price_ht, $tva, $ord, $colis, $price_ttc=null, $ecotaxe=0, $prd_deleted=false, $update_totals=true, $parent=false, $child_line=false, $position=null, $cod_id=false, $col_id=false ){
	if( !ord_bl_exists( $bl ) ){
		return false;
	}
	if( !prd_products_exists( $prd, false, $prd_deleted ) ){
		return false;
	}
	if( !is_numeric($line) || $line < 0 ){
		return false;
	}
	if( !trim($ref) ){
		return false;
	}
	if( !trim($name) ){
		$name = $ref;
	}

	global $config;

	$qte = str_replace(array(' ', ','), array('', '.'), $qte);
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}

	$price_ht = str_replace(array(' ', ','), array('', '.'), $price_ht);
	$tva = str_replace(array(' ', ','), array('', '.'), $tva);
	$ecotaxe = str_replace(array(' ', ','), array('', '.'), $ecotaxe);


	if( !is_numeric($qte) ){
		return false;
	}
	if( !is_numeric($price_ht) ){
		return false;
	}
	if( !is_numeric($tva) ){
		return false;
	}
	if( !is_numeric($ecotaxe) ){
		return false;
	}

	if( !is_numeric($ord) || !$ord ){
		$ord = 'null';
	}elseif( !ord_orders_exists( $ord ) ){
		return false;
	}

	if( is_null($position) || !is_numeric($position) ){
		$position = 'null';
	}

	if( is_null($cod_id) || !is_numeric($cod_id) || !$cod_id ){
		$cod_id = 'null';
	}

	if( is_null($col_id) || !is_numeric($col_id) || !$col_id ){
		$col_id = 'null';
	}

	$fields = array(
		'prd_tnt_id', 'prd_bl_id', 'prd_id', 'prd_line_id', 'prd_ref', 'prd_name', 'prd_qte', 'prd_price_ht', 'prd_tva_rate',
		'prd_ord_id', 'prd_colis', 'prd_date_created', 'prd_ecotaxe', 'prd_pos', 'prd_cod_id', 'prd_col_id'
	);
	$values = array(
		$config['tnt_id'], $bl, $prd, $line, '"'.addslashes($ref).'"', '"'.addslashes($name).'"', $qte, $price_ht, $tva,
		$ord, '"'.addslashes($colis).'"', 'now()', $ecotaxe, $position, $cod_id, $col_id
	);

	if( $price_ttc !== null ){
		$price_ttc = str_replace(array(' ', ','), array('', '.'), $price_ttc);
		if( is_numeric($price_ttc) ){
			$fields[] = 'prd_price_ttc';
			$values[] = $price_ttc;
		}
	}

	if( $parent!==false ){
		$fields[] = 'prd_parent_id';
		if( $parent===null ){
			$values[] = 'null';
		}else{
			$values[] = $parent;
		}
	}

	if( $child_line!==false ){
		$fields[] = 'prd_childs_line_id';
		if( $child_line===null ){
			$values[] = 'null';
		}else{
			$values[] = $child_line;
		}
	}

	$sql = '
		insert into ord_bl_products
			('.implode(', ', $fields).')
		values
			('.implode(', ', $values).')
	';
	$res = ria_mysql_query( $sql );

	// si échec car ligne déjà existante, supprime et retente
	if( !$res && ord_bl_products_exists( $bl, $prd, $line ) ){
		ord_bl_products_del( $bl, $prd, $line );
		$res = ria_mysql_query( $sql );
	}

	// Met à jour les totaux pour le bl
	if( $res ){
		if( $update_totals ){
			ord_bl_update_totals( $bl );
		}
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Permet la suppression d'une ligne de bl
 *	@param int $bl Obligatoire, Identifiant du bon de livraison à modifier
 *	@param int $prd Facultatif, Identifiant du produit
 *	@param $line Facultatif, Identifiant de la ligne
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_products_del( $bl, $prd=false, $line=false ){
	if( !is_numeric($bl) ) return false;
	if( $prd!==false && !is_numeric($prd) ) return false;
	if( $line!==false && !is_numeric($line) ) return false;
	global $config;

	$sql = 'delete from ord_bl_products where prd_tnt_id='.$config['tnt_id'].' and prd_bl_id='.$bl.' ';

	if( $prd!==false ){
		$sql .= ' and prd_id='.$prd;
	}
	if( $line!==false ){
		$sql .= ' and prd_line_id='.$line;
	}

	$res = ria_mysql_query($sql);
	if( $res ){
		ord_bl_update_totals( $bl );
	}
	return $res;
}
// \endcond

// \cond onlyria
/**	Permet la mise d'une ligne de bl importée depuis la gestion commerciale SAGE.
 *
 *	@param int $bl Obligatoire, Identifiant interne du bon de commande
 *	@param int $prd Obligatoire, Identifiant interne du produit
 *	@param int $line Obligatoire, Numéro de ligne (permet d'avoir plusieurs fois le même produit sur des lignes différentes).
 *	@param string $ref Obligatoire, Référence du produit
 *	@param string $name Obligatoire, Désignation du produit
 *	@param int $qte Obligatoire, Quantité commandée
 *	@param float $price_ht Obligatoire, Prix unitaire HT du produit (remises comprises)
 *	@param float $tva Obligatoire, Taux de tva à appliquer à la ligne
 *	@param int $ord Obligatoire, Identifiant de la commande d'origine
 *	@param string $colis Obligatoire, Numéro de colis pour la livraison
 *	@param float $price_ttc Optionnel, Prix unitaire TTC du produit (remises comprises)
 *	@param float $ecotaxe Optionnel, Eco-participation unitaire
 *	@param bool $update_totals Optionnel, Permet de ne pas recalculé les totaux
 *	@param int $parent Optionnel, Identifiant d'un produit parent
 *	@param int $child_line Optionnel, Identifiant de line pour les produits enfants
 *	@param $position Optionnel, Position de la ligne produit dans le bon de livraison
 *	@param $cod_id Optionnel, Identifiant de promotion
 *  @param $col_id Optionnel, Identifiant de collisage
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function ord_bl_products_update_sage( $bl, $prd, $line, $ref, $name, $qte, $price_ht, $tva, $ord, $colis, $price_ttc=null, $ecotaxe=0, $update_totals=true, $parent=false, $child_line=false, $position=null, $cod_id=false, $col_id=false){
	global $config;

	if( !ord_bl_exists($bl) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( !is_numeric($line) ) return false;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}
	if( !is_numeric($qte) ) return false;

	$price_ht = str_replace( array(',',' '), array('.',''), $price_ht );
	if( !is_numeric($price_ht) ) return false;
	$tva = str_replace( array(',',' '), array('.',''), $tva );
	if( !is_numeric($tva) ) return false;
	$ecotaxe = str_replace( array(',',' '), array('.',''), $ecotaxe );
	if( !is_numeric($ecotaxe) ) return false;
	if( $parent!==false && $parent!==null && !is_numeric($parent) ) return false;
	if( $child_line!==false && $child_line!==null && !is_numeric($child_line) ) return false;
	if( $cod_id!==false && $cod_id!==null && !is_numeric($cod_id) ) return false;
	if( $col_id!==false && $col_id!==null && !is_numeric($col_id) ) return false;

	if( !is_numeric($ord) || $ord==0 ){
		$ord = 'null';
	}elseif( !ord_orders_exists($ord) ){
		return false;
	}

	$str_price_ttc = '';
	if( $price_ttc!=null ){
		$price_ttc = str_replace( array(',',' '), array('.',''), $price_ttc );
		if( is_numeric($price_ttc) ){
			$str_price_ttc = ', prd_price_ttc='.$price_ttc;
		}
	}

	$res = ria_mysql_query('
		update
			ord_bl_products
		set
			prd_ref=\''.addslashes($ref).'\',
			prd_name=\''.addslashes($name).'\',
			prd_qte='.$qte.',
			prd_price_ht='.$price_ht.',
			prd_tva_rate='.$tva.',
			prd_ord_id='.$ord.',
			'.((!is_null($position) && is_numeric($position)) ? 'prd_pos='.$position.',' : '' ).'
			prd_colis=\''.addslashes($colis).'\',
			prd_ecotaxe='.$ecotaxe.'
			'.$str_price_ttc.'
			'.( $parent!==false ? ',prd_parent_id='. ( $parent!==null ? $parent : 'null' ) : '' ).'
			'.( $child_line!==false ? ',prd_childs_line_id='. ( $child_line!==null ? $child_line : 'null' ) : '' ).'
			'.( $cod_id!==false ? ',prd_cod_id='. ( $cod_id!==null ? $cod_id : 'null' ) : '' ).'
			'.( $col_id!==false ? ',prd_col_id='. ( $col_id!==null ? $col_id : 'null' ) : '' ).'
		where
			prd_tnt_id='.$config['tnt_id'].' and
			prd_bl_id='.$bl.' and
			prd_id='.$prd.' and
			prd_line_id='.$line
	);

	if( $res ){
		if( $update_totals ){
			ord_bl_update_totals( $bl );
		}
	}

	return $res;
}
// \endcond

/** Cette fonction récupère des lignes de bons de livraison, suivant des paramètres optionnels.
 *	Les paramètres $bl et $ord sont mutuellement obligatoires
 *
 *	@param int $bl Optionnel, identifiant du bon de livraison (ou tableau)
 *	@param int $ord Optionnel, identifiant de la commande ou null pour les lignes qui ne sont rattachées à aucune commande (possibilité de spécifier "null" en toutes lettres)
 *	@param $colis Optionnel, numéro du colis
 *	@param $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par date de création de la ligne de commande. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : ord-piece, ref, ps-ref, name, date, price, qte, total. Les valeurs autorisées pour la direction sont : asc, desc.
 *	@param $supplier Optionnel, identifiant d'un fournisseur sur lequel le tri par référence fournisseur sera appliqué
 *	@param int $prd Optionnel, permet de filtrer le résultat sur un ou plusieurs produits
 *	@param $line Optionnel, permet de filtrer par numéro de ligne (peut être un tableau)
 *	@param bool $correled Optionnel, si activé et $bl, $prd et $line des tableaux de même taille, un triplet prd_bl_id / prd_id / prd_line_id est testé pour chaque index de tableau
 *
 *	@return resource Un résultat de requête MySQL, comprenant les colonnes suivantes :
 *			- id : Identifiant du produit
 *			- ref : référence du produit
 *			- name : désignation du produit
 *			- qte : quantité commandée
 *			- price_ht : prix ht du produit à l'unité
 *			- price_ttc : prix ttc du produit à l'unité
 *			- total_ht : sous total pour le produit, hors taxes (price_ht*qte)
 *			- total_ttc : sous total pour le produit, ttc (price_ttc*qte)
 *			- ord_id : identifiant de la commande d'origine
 *			- ord_piece : numéro de pièce de la commande d'origine
 *			- ord_ref : référence de la commande d'origine
 *			- ecotaxe : Eco-participation unitaire
 *			- sell_weight : Détermine si la quantité est un poids ou un volume (False par défaut)
 *			- weight_net : Poids net du produit
 *			- line : Numéro de ligne
 * 			- group_id : L'identifiant du groupe.
 * 			- group_parent_id : L'identifiant du parent du groupe.
 *			- orderable : Détermine si le produit est commandable
 *			- colis : numéro de colis
 *			- bl_id : numéro de BL
 *			- tva_rate : taux de TVA
 */
function ord_bl_products_get( $bl = 0, $ord = 0, $colis = false, $sort = false, $supplier = false, $prd = false, $line = false, $correled = false ){

	$bl = control_array_integer( $bl, false );
	if( $bl === false ){
		return false;
	}

	if( $prd === false ){
		$prd = 0;
	}
	$prd = control_array_integer( $prd, false );
	if( $prd === false ){
		return false;
	}

	if( $line === false ){
		$line = array();
	}
	$line = control_array_integer( $line, false, true );
	if( $line === false ){
		return false;
	}

	if( $ord !== 'null' && ( !is_numeric($ord) || $ord < 0 ) ){
		return false;
	}

	if( $correled && ( sizeof($bl) != sizeof($prd) || sizeof($bl) != sizeof($line) ) ){
		return false;
	}

	if( !sizeof($bl) && ( !$ord || $ord === 'null' ) ){
		return false;
	}

	if( !is_array($sort) ){
		$sort = array('ref'=>'asc');
	}

	global $config;

	$sql = '
		select
			bp.prd_bl_id as bl_id, bp.prd_id as id, prd_line_id as line, '.($config['use_decimal_qte'] ? 'prd_qte' : 'cast(prd_qte as signed)' ).' as qte, bp.prd_colis as colis, ord_id, ord_piece, ord_ref,
			bp.prd_ref as ref, bp.prd_name as name, bp.prd_ecotaxe as ecotaxe, ifnull(prd_orderable, 0) as orderable,
			prd_price_ht as price_ht, ifnull(prd_price_ttc, prd_price_ht * prd_tva_rate) as price_ttc, prd_tva_rate as tva_rate,
			prd_price_ht * prd_qte as total_ht, ifnull(prd_price_ttc, prd_price_ht * prd_tva_rate) * prd_qte as total_ttc,
			ifnull(p.prd_sell_weight, 0) as sell_weight, ifnull(pv_value, "0") as weight_net,
			bp.prd_date_livr as date_livr, bp.prd_notes as notes, bp.prd_date_created as date_created, bp.prd_parent_id as parent_id, bp.prd_childs_line_id as childs_line_id, bp.prd_group_id as group_id, bp.prd_group_parent_id as group_parent_id, bp.prd_cod_id as cod_id,
			bp.prd_purchase_avg as purchase_avg, bp.prd_pos as pos, bp.prd_col_id as col_id
		from
			ord_bl_products as bp
			join ord_bl on bp.prd_tnt_id = bl_tnt_id and prd_bl_id = bl_id and bl_masked = 0
			left join ord_orders on
				bp.prd_tnt_id = ord_tnt_id and prd_ord_id = ord_id
			left join prd_products as p on
				bp.prd_tnt_id = p.prd_tnt_id and bp.prd_id = p.prd_id
			left join fld_object_values on
				bp.prd_tnt_id = pv_tnt_id and bp.prd_bl_id = pv_obj_id_0 and bp.prd_id = pv_obj_id_1
				and bp.prd_line_id = pv_obj_id_2 and pv_fld_id = '._FLD_BL_LINE_WEIGHT.'
	';

	if( isset($sort['ps-ref']) ){
		$sql .= '
			left join prd_suppliers on
				bp.prd_tnt_id = ps_tnt_id and bp.prd_id = ps_prd_id and '.( $supplier === false ? 'ps_main = 1' : 'ps_usr_id = '.$supplier ).'
		';
	}

	$sql .= '
		where bp.prd_tnt_id = '.$config['tnt_id'].'
	';

	if( $correled ){
		$cnds = array();
		for( $i = 0; $i < sizeof($bl); $i++ ){
			$cnds[] = 'bp.prd_bl_id = '.$bl[ $i ].' and bp.prd_id = '.$prd[ $i ].' and bp.prd_line_id = '.$line[ $i ];
		}
		if( sizeof($cnds) ){
			$sql .= ' and ( ('.implode(') or (', $cnds).') )';
		}
	}else{
		if( sizeof($bl) ){
			$sql .= ' and bp.prd_bl_id in ('.implode(', ', $bl).')';
		}
		if( sizeof($prd) ){
			$sql .= ' and bp.prd_id in ('.implode(', ', $prd).')';
		}
		if( sizeof($line) ){
			$sql .= ' and bp.prd_line_id in ('.implode(', ', $line).')';
		}
	}

	if( isset($sort['ps-ref']) ){
		$sql .= '
			and (
				ps_usr_id is null or ps_usr_id in (
					select usr_id from gu_users
					where usr_prf_id = '.PRF_SUPPLIER.' and usr_date_deleted is null and usr_tnt_id = '.$config['tnt_id'].'
				)
			)
		';
	}

	if( $ord === 'null' ){
		$sql .= ' and prd_ord_id is null';
	}elseif( $ord ){
		$sql .= ' and prd_ord_id = '.$ord;
	}

	if( $colis !== false ){
		$sql .= ' and prd_colis = "'.addslashes($colis).'"';
	}

	$sort_final = array();
	foreach( $sort as $col => $dir ){
		$dir = strtolower(trim($dir)) == 'asc' ? 'asc' : 'desc';
		switch( strtolower(trim($col)) ){
			case 'ord-piece':
				$sort_final[] = 'ord_piece '.$dir;
				break;
			case 'ref':
				$sort_final[] = 'bp.prd_ref '.$dir;
				break;
			case 'ps-ref':
				$sort_final[] = 'ps_ref '.$dir;
				break;
			case 'name':
				$sort_final[] = 'bp.prd_name '.$dir;
				break;
			case 'qte':
				$sort_final[] = 'prd_qte '.$dir;
				break;
			case 'price':
				$sort_final[] = 'prd_price_ht '.$dir;
				break;
			case 'total':
				$sort_final[] = 'prd_price_ht * prd_qte '.$dir;
				break;
			case 'date':
				$sort_final[] = 'prd_date_created '.$dir;
				break;
			case 'group':
				$sort_final[] = 'prd_group_id '.$dir;
				break;
			case 'group_parent':
				$sort_final[] = 'prd_group_parent_id '.$dir;
				break;
			case 'line_pos':
				$sort_final[] = 'prd_pos '.$dir;
				break;
		}
	}

	if( sizeof($sort_final) ){
		$sql .= ' order by '.implode(', ', $sort_final);
	}

	$r = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log(__FILE__.':'.__LINE__.' - '.mysql_error().' - '.$sql);
	}

	return $r;

}

// \cond onlyria
/**	Vérifie l'existance d'un produit dans un bon de livraison, par son identifiant
 *	@param int $bl Obligatoire, Identifiant du bon de livraison à tester
 *	@param int $prd Identifiant du produit à tester
 *	@param $line Numéro de la ligne à tester. Si false (par défaut), ce paramètre n'est pas pris en compte
 *	@return bool true si le produit est présent dans le bon de livraison
 *	@return bool false si le produit n'existe pas dans le bon de livraison, ou si une erreur s'est produite
 */
function ord_bl_products_exists( $bl, $prd, $line=false ){
	global $config;
	if( !ord_bl_exists($bl) ) return false;
	if( !prd_products_exists($prd) ) return false;
	if( $line!=false && !is_numeric($line) ) return false;

	$sql = 'select prd_id from ord_bl_products where prd_tnt_id='.$config['tnt_id'].' and prd_bl_id='.$bl.' and prd_id='.$prd;
	if( $line!=false )
		$sql .= ' and prd_line_id='.$line;

	return ria_mysql_num_rows(ria_mysql_query( $sql ))>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si un couple produit / quantité existe pour un bon de livraison donné.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $prd Identifiant du produit
 *	@param $qte Quantité
 *
 *	@return bool True si une ou des lignes existent, False sinon
 */
function ord_bl_products_exists_qte( $bl, $prd, $qte ){

	if( !ord_bl_exists( $bl ) ){
		return false;
	}
	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	global $config;

	$qte = str_replace( array(',',' '), array('.',''), $qte );
	if( !$config['use_decimal_qte'] ){
		$qte = floor($qte);
	}
	if( !is_numeric($qte) || !$qte ){
		return false;
	}

	$sql = '
		select 1
		from ord_bl_products
		where
			prd_tnt_id = '.$config['tnt_id'].' and prd_bl_id = '.$bl.'
			and prd_id = '.$prd.' and prd_qte = '.$qte.'
	';

	if( !( $r = ria_mysql_query($sql) ) ){
		return false;
	}

	return ria_mysql_num_rows($r);

}
// \endcond

// \cond onlyria
/** Modifie le group_id et le group_parent_id pour la ligne du bl donnée.
 *
 * @param int $bl Obligatoire, L'identifiant du bon de livraison
 * @param int $prd Identifiant du produit
 * @param int $line L'identifiant de la ligne
 * @param int $group_id L'identifiant du groupe. Si NULL la valeur sera supprimée
 * @param int $group_parent_id  Optionnel. L'identifiant du parent du groupe. Si NULL la valeur sera supprimée
 * @return bool true si succès, false autrement
 */
function ord_bl_products_set_group_id( $bl, $prd, $line, $group_id=false, $group_parent_id=false ){

	if(
		(!is_numeric($bl) || $bl <= 0) ||
		(!is_numeric($prd) || $prd < 0) ||
		(!is_numeric($line) || $line < 0) ||
		($group_id===false || ($group_id!==null && !is_numeric($group_id))) ||
		($group_parent_id!==false && $group_parent_id!==null && !is_numeric($group_parent_id))
	){
		return false;
	}

	global $config;

	$sql = '
		update ord_bl_products
		set	prd_group_id='. ( $group_id!==null ? $group_id : 'null' ).'
		'.( $group_parent_id!==false ? ',prd_group_parent_id='. ( $group_parent_id!==null ? $group_parent_id : 'null' ) : '' ).'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_bl_id = '.$bl.'
			and prd_id = '.$prd.'
			and prd_line_id = '.$line.'
	';

	if( !ria_mysql_query($sql) ){
		return false;
	}

	ord_bl_set_date_modified($bl);

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction envoie une demande pour déposer un avis consommateur sur une commande livrée
 *	@param int|array $bl Obligatoire, identifiant d'un BL ou résultat d'un ria_mysql_fetch_assoc( ord_bl_get() )
 *	@return bool True si l'envoi s'est correctement déroulée, False dans le cas contraire
 */
function ord_bl_product_review_notify( $bl ){
	if( is_array($bl) ){
		if( !ria_array_key_exists(array('id', 'usr_id'), $bl) ){
			return false;
		}
	}else{
		$rbl = ord_bl_get( $bl );
		if( !$rbl || !ria_mysql_num_rows($rbl) ){
			return false;
		}

		$bl = ria_mysql_fetch_assoc( $rbl );
	}

	global $config;

	$cat_root = isset($config['cat_root']) && is_numeric($config['cat_root']) && $config['cat_root'] > 0 ? $config['cat_root'] : 0;

	// Récupère la liste des articles expédiés (hors frais de port) encore publiés sur le site
	$ar_products = array();

	$rproduct = ord_bl_products_get( $bl['id'] );
	if( $rproduct ){
		while( $product = ria_mysql_fetch_assoc($rproduct) ){
			if( prd_products_is_port($product['ref']) ){
				continue;
			}

			$rp = prd_products_get_simple( $product['id'], '', true, $cat_root, true );
			if( $rp && ria_mysql_num_rows($rp) ){
				$p = ria_mysql_fetch_assoc( $rp );

				if( !isset($p['url_alias']) ){
					$p['url_alias'] = prd_products_get_url( $p['id'], true );
				}
				if( isset($p['url_alias']) && trim($p['url_alias']) != '' ){
					$ar_products[] = $p;
				}
			}
		}
	}

	if( !sizeof($ar_products) ){
		return true;
	}

	$rcfg = cfg_emails_get( 'prd-review', $config['wst_id'] );
	if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
		return false;
	}

	$cfg = ria_mysql_fetch_assoc( $rcfg );

	// Récupère les informations sur le compte client
	$ruser = gu_users_get( $bl['usr_id'] );
	if( !$ruser || !ria_mysql_num_rows($ruser) ){
		return false;
	}
	$user = ria_mysql_fetch_assoc( $ruser );

	// Le compte client doit accepter de recevoir les avis d'expéditions
	// TODO déterminer la méthode

	$email = new Email();
	$email->setFrom( $cfg['from'] );
	$email->addTo( $user['email'] );
	$email->addBcc( $cfg['bcc'] );
	$email->setReplyTo( $cfg['reply-to'] );

	$compl_url = '?utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=prd_avis';

	$can_send = true;
	switch( $config['tnt_id'] ){
		case 16: {
			if( $config['wst_id']==23 ){
				$email->setSubject('Vous avez reçu votre dernière commande passée sur Animaleco.com, laissez un avis consommateur.');

				$email->addHtml( $config['email_html_header'] );
				$email->addParagraph('Bonjour '.trim( $user['title_name'].' '.$user['adr_firstname'].' '.$user['adr_lastname'] ).',');
				$email->addParagraph('Vous avez passé commande sur notre site et nous vous remercions pour la confiance que vous nous accordez. Afin de faire partager votre expérience à d’autres internautes et amis des animaux, nous vous invitons à laisser votre avis sur les produits commandés, directement depuis ce mail :');

				$email->addHtml('<ul>');
				foreach( $ar_products as $one_product ){
					$email->addHtml('<li><a href="'.$config['site_url'].$one_product['url_alias'].$compl_url.'&amp;utm_content=page_produit#onglet_avis">'.htmlspecialchars( $one_product['title'] ).'</a></li>');
				}
				$email->addHtml('</ul>');

				$email->addParagraph('Nous serions ravis de pouvoir faire profiter de votre opinion à tous nos futurs visiteurs !');
				$email->addParagraph('Nous vous remercions encore une fois pour votre confiance, au plaisir de vous revoir très bientôt sur <a href="'.$config['site_url'].$compl_url.'&amp;utm_content=page_accueil">www.animaleco.com</a> !');
				$email->addParagraph('Cordialement,<br />L\'équipe Animaleco.com.');
				$email->addHtml( $config['email_html_footer'] );
			}elseif( $config['wst_id']==367 ){
				$email->setSubject('Vous avez reçu votre dernière commande passée sur Animal & Co, laissez un avis consommateur.');

				$email->addHtml( $config['email_html_header'] );
				$email->addParagraph('Bonjour '.trim( $user['title_name'].' '.$user['adr_firstname'].' '.$user['adr_lastname'] ).',');
				$email->addParagraph('Vous avez passé commande sur notre site et nous vous remercions pour la confiance que vous nous accordez. Afin de faire partager votre expérience à d’autres internautes et amis des animaux, nous vous invitons à laisser votre avis sur les produits commandés, directement depuis ce mail :');

				$email->addHtml('<ul>');
				foreach( $ar_products as $one_product ){
					$email->addHtml('<li><a href="'.$config['site_url'].$one_product['url_alias'].$compl_url.'&amp;utm_content=page_produit#onglet_avis">'.htmlspecialchars( $one_product['title'] ).'</a></li>');
				}
				$email->addHtml('</ul>');

				$email->addParagraph('Nous serions ravis de pouvoir faire profiter de votre opinion à tous nos futurs visiteurs !');
				$email->addParagraph('Nous vous remercions encore une fois pour votre confiance, au plaisir de vous revoir très bientôt sur <a href="'.$config['site_url'].$compl_url.'&amp;utm_content=page_accueil">www.animaleco.com</a> !');
				$email->addParagraph('Cordialement,<br />L\'équipe Animal & Co.');
				$email->addHtml( $config['email_html_footer'] );
			}
			break;
		}
		case 43: {
			$email->setSubject('Vous avez reçu votre dernière commande passée sur Purebike, laissez un avis consommateur.');

			$email->addHtml( $config['email_html_header'] );
			$email->addParagraph('Bonjour '.trim( $user['title_name'].' '.$user['adr_firstname'].' '.$user['adr_lastname'] ).',');
			$email->addParagraph('Vous avez passé commande sur notre site et nous vous remercions pour la confiance que vous nous accordez. Afin de faire partager votre expérience à d’autres internautes, nous vous invitons à laisser votre avis sur les produits commandés, directement depuis ce mail :');

			$email->addHtml('<ul>');
			foreach( $ar_products as $one_product ){
				$email->addHtml('<li><a href="'.$config['site_url'].$one_product['url_alias'].$compl_url.'&amp;utm_content=page_produit#onglet_avis">'.htmlspecialchars( $one_product['title'] ).'</a></li>');
			}
			$email->addHtml('</ul>');

			$email->addParagraph('Nous serions ravis de pouvoir faire profiter de votre opinion à tous nos futurs visiteurs !');
			$email->addParagraph('Nous vous remercions encore une fois pour votre confiance, au plaisir de vous revoir très bientôt sur <a href="'.$config['site_url'].$compl_url.'&amp;utm_content=page_accueil">www.purebike.fr</a> !');
			$email->addParagraph('Cordialement,<br />L\'équipe Purebike.');
			$email->addHtml( $config['email_html_footer'] );
			break;
		}
		default: {
			$can_send = false;
			error_log("Aucune configuration de renseigner \"ord_bl_product_review_notify\" : ".$config['tnt_id']);
			break;
		}
	}

	if( $can_send ){
		return $email->send();
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Recherche les BL qui doivent être importés dans la gestion commerciale
 *
 *	@return bool False en cas d'échec
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant du BL
 *		- date : date de création du BL
 *		- sync_past : 0 si le BL à été modifiée il y a moins d'une heure, 1 dans le cas contraire
 */
function ord_bls_get_new_to_import(){
	global $config;

	// maintenir les conditions where à l'identique dans gu_users_toimport_get
	$sql = '
		select 	bl_id as id,
				bl_date as date,
				case when bl_date_modified < now() - INTERVAL 1 HOUR  then 1
				else 0 END as sync_past
		from
			ord_bl
			join gu_users as u1 on bl_usr_id = u1.usr_id and bl_tnt_id = if(u1.usr_tnt_id=0, bl_tnt_id, u1.usr_tnt_id)
			join gu_users as u2 on '.( $config['parent_is_order_holder'] ? 'ifnull(u1.usr_parent_id, u1.usr_id)' : 'u1.usr_id' ).' = u2.usr_id and u1.usr_tnt_id = u2.usr_tnt_id
		where
			bl_tnt_id = '.$config['tnt_id'].'
			and bl_state_id in ('._STATE_BL_EXP.')
			and bl_piece = ""
			and bl_need_sync = 1
			and bl_masked = 0
			and u1.usr_date_deleted is null
			and u2.usr_date_deleted is null
	';

	$exists_usr_sync  = 'exists ( ';
	$exists_usr_sync .= '	select 1 ';
	$exists_usr_sync .= '	from rel_relations_hierarchy ';
	$exists_usr_sync .= '	join gu_users cu2 on rrh_tnt_id=cu2.usr_tnt_id and rrh_src_0=cu2.usr_id ';
	$exists_usr_sync .= '	where rrh_rrt_id='.REL_USR_HIERARCHY.' ';
	$exists_usr_sync .= '		and rrh_tnt_id=bl_tnt_id and rrh_dst_0=bl_usr_id and rrh_dst_1=0 and rrh_dst_2=0 ';
	$exists_usr_sync .= '		and cu2.usr_is_sync=1 ';
	$exists_usr_sync .= '		and cu2.usr_ref != "" ';
	$exists_usr_sync .= '		and cu2.usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.', '.PRF_RESELLER.') ';
	$exists_usr_sync .= ') ';

	if( $config['import_orders_from_sync_usr'] ){
		$sql .= ' and (u2.usr_ref != "" or '.$exists_usr_sync.') ';
	}else{
		$sql .= ' and (u2.usr_prf_id in ('.PRF_CUSTOMER.','.PRF_CUST_PRO.', '.PRF_RESELLER.') or '.$exists_usr_sync.') ';
	}

	$r = ria_mysql_query($sql);

	if( mysql_error() ){
		error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de marquer l'entête d'un bl comme n'étant pas/plus synchronisé avec la gestion commerciale
 *	@param int $bl_id identifiant d'un bl
 *	@return int le nombre de lignes affectées en cas de succès, false en cas d'échec
 */
function ord_bl_set_need_sync( $bl_id ){
	if( !is_numeric($bl_id) ) return false;
	global $config;

	return ria_mysql_query('update ord_bl set bl_need_sync=1 where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl_id);
}
// \endcond

// \cond onlyria
/** Détermine le statut d'un bl
 *	@param int $id Identifiant du bl
 *	@return bool False en cas d'échec, identifiant du statut du bl sinon
 */
function ord_bl_get_state( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select bl_state_id as state_id
		from ord_bl
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res, 0, 'state_id' );
}
// \endcond

/**	Cette fonction met à jour le statut d'un bl
 *
 *	@param int $bl_id Obligatoire, identifiant du bl
 *	@param $status Obligatoire, identifiant du statut
 *
 *	@return int|bool le nombre de lignes affectées en cas de succès, false en cas d'échec
 */
function ord_bl_state_update( $bl_id, $status ){
	if( !is_numeric($bl_id) || $bl_id <= 0 ){
		return false;
	}

	if( !ord_states_exists( $status ) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_bl
		set bl_state_id = '.$status. '
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_id = '.$bl_id . '
	';

	$res = ria_mysql_query($sql);

	// Mis à jour de la commande.
	if( !$res ){
		return false;
	}

	return $res;
}
/// @}

// \cond onlyria
/** Cette fonction permet de mettre à jour le numero de piece d'un BL
 *  @param int $id Obligatoire, Identifiant du bl
 * 	@param string $piece Obligatoire, Nouveau numéro de pièce
 * 	@return bool True en cas de succès, false dans le cas contraire
 */
function ord_bl_set_piece( $id, $piece ){

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	if( !is_string($piece) ){
		return false;
	}

	global $config;

	$sql = '
		update ord_bl
		set bl_piece = "'.addslashes($piece).'", bl_need_sync=0
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_id = '.$id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond


/**	Cette fonction permet la modification de l'adresse de livraison d'un BL.
 *	L'adresse doit au préalable exister dans la base de données.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison à modifier
 *	@param int|bool $adr Facultatif, identifiant de l'adresse de livraison, ou false pour utiliser l'adresse de livraison par défaut
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_adr_delivery_set( $bl, $adr=false ){
	global $config;
	if( !ord_bl_exists($bl) ) return false;
	if( !gu_adresses_exists($adr) ){

		// chargement de l'adresse de livraison par défaut du client
		$radr = ria_mysql_query('
			select ifnull(usr_adr_delivery, 0) as adr_dlv, usr_adr_invoices as adr_inv
			from gu_users join ord_bl on usr_id=bl_usr_id
			where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl.' and ( usr_tnt_id='.$config['tnt_id'].' or usr_tnt_id=0 )
		');

		$adr_defaut = 'bl_adr_invoices'; // ancien comportement
		if( $radr && ria_mysql_num_rows($radr) ){
			// $adr_defaut = ria_mysql_result( $radr, 0, 0 );
			$adr = ria_mysql_fetch_assoc($radr);

			if (gu_adresses_exists($adr['adr_dlv'])) {
				$adr_defaut = $adr['adr_dlv'];
			}else{
				$adr_defaut = $adr['adr_inv'];
			}
		}

		$res = ria_mysql_query('update ord_bl set bl_adr_dlv_id ='.$adr_defaut.' where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl);
	}else{
		$res = ria_mysql_query('update ord_bl set bl_adr_dlv_id='.$adr.' where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl);
	}

	return $res;
}
// \cond onlydev
/// @}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de l'adresse de facturation d'un BL'.
 *	L'adresse doit au préalable exister dans la base de données.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison à modifier
 *	@param int|bool $adr Identifiant de l'adresse de facturation, ou false pour utiliser l'adresse de livraison
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function ord_bl_adr_invoice_set( $bl, $adr=false ){
	global $config;
	if( !ord_bl_exists($bl) ) return false;
	if( !gu_adresses_exists($adr) ){
		error_log( 'ord_bl_adr_invoice_set utilisé avec un $adr faux (tenant '.$config['tnt_id'].').' );
		return false;
	}else{
		$res = ria_mysql_query('update ord_bl set bl_adr_invoices='.$adr.' where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl);
	}

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'association d'un identifiant de vendeur a un bon de livraison donnée.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison à modifier
 *	@param int $seller Identifiant de vendeur a attribuer au bon de livraison (ou NULL pour détacher le représentant)
 *	@return bool true en cas de succès
 *	@return bool false en cas d'erreur
 */
function ord_bl_set_seller_id( $bl, $seller ){
	if( !is_numeric($bl) || $bl<=0 ) return false;
	if( $seller!==null && ( !is_numeric($seller) || $seller<=0 ) ) return false;
	global $config;

	return ria_mysql_query('
		update ord_bl set bl_seller_id='.( $seller!==null ? $seller : 'NULL' ).'
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour du contact qui à pris le bon de livraison, attention il n'y a pas de contrôle sur le fait que ce soit
 * 	un contact ou un utilisateur, il est donc possible d'avoir dans ce champ des comptes qui ne sont pas des contacts.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $contact_id Obligatoire, identifiant du contact qui à pris le bon de livraison
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_bl_set_contact_id( $bl, $contact_id ){
	global $config;

	if( !is_numeric($bl) || $bl <= 0 ){
		return false;
	}
	if( !is_numeric($contact_id) || ($contact_id > 0 && !gu_users_exists($contact_id)) ){
		return false;
	}

	return ria_mysql_query('
		update ord_bl
		set bl_contact_id = '.( $contact_id <= 0 ? 'null' : $contact_id ).'
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_id = '.$bl.'
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour la date de livraison prévue d'un bon de livraison.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param string $date_livr Date de livraison prévue de la commande (format FR ou EN, avec ou sans la partie horaire. Si cette dernière est spécifiée, elle n'est pas prise en compte). Si la date spécifiée est invalide, la date de livraison de la commande est assignée à NULL, aucune erreur n'est retournée.
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_bl_set_date_livr( $bl, $date_livr ){
	global $config;

	if( !is_numeric($bl) || $bl <= 0 ){
		return false;
	}

	$datesql = 'NULL';
	if( isdateheure($date_livr) ){
		$datesql = 'DATE("'.dateheureparse($date_livr).'")';
	}

	return ria_mysql_query('
		update ord_bl
		set bl_date_livr = '.$datesql.'
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_id = '.$bl.'
	');

}
// \endcond


/**	Cette fonction permet la modification des consignes de livraison.
 *	Appellée sans le deuxième argument, cette fonction efface les consignes de livraison.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param $notes Facultatif, Consignes de livraison
 */
function ord_bl_dlv_notes_set( $bl, $notes='' ){
	global $config;

	if( !ord_bl_exists($bl) ){
		return false;
	}
	return ria_mysql_query('
		update ord_bl set bl_dlv_notes=\''.addslashes(trim($notes)).'\'
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);
}

// \cond onlyria
/**	Cette fonction permet de mettre a jour le commentaire du bon de livraison
 *	Appellée sans le deuxième argument, cette fonction efface le commentaire du bon de livraison.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param string $comments Facultatif, Commentaire du bon de livraison
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_bl_comments_set( $bl, $comments='' ){
	global $config;

	if( !ord_bl_exists($bl) ){
		return false;
	}

	return ria_mysql_query('
		update ord_bl set bl_comments=\''.addslashes(trim($comments)).'\'
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le renseignement de l'information 'Magasin de livraison' pour un bon de livraisone.
 *	Appellée sans le second argument, cette fonction efface le champ 'Magasin de livraison'
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $str Facultatif, Identifiant du magasin
 *	@return bool true en cas de succès, false en cas d'échec
 */
function ord_bl_set_dlv_store( $bl, $str=false ){
	global $config;

	if( !is_numeric($bl) || $bl<=0 ){
		return false;
	}
	if( !is_numeric($str) || $str<=0 ){
		$str = 'null';
	}

	return ria_mysql_query('
		update ord_bl set bl_str_id='.$str.'
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le type de package ( Cartons, palette, ... )
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $pkg Obligatoire, Identifiant de l'emballage, 0 permet de valoriser le champ par NULL
 *	@return bool True en cas de succès, False sinon
 */
function ord_bl_set_package( $bl, $pkg ){
	if( !ord_bl_exists( $bl ) ) return false;
	if( !is_numeric( $pkg ) || $pkg<0 ) return false;
	global $config;

	return ria_mysql_query('
		update ord_bl set bl_pkg_id='.( $pkg==0 ? 'NULL' : $pkg ).'
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);
}
// \endcond

// \cond onlyria
/** Cette fonction met à jour le dépôt de livraison
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $dps Obligatoire, Identifiant du dépôt, 0 permet de valoriser le champ par NULL
 *	@return bool True en cas de succès, False sinon
 */
function ord_bl_set_deposit( $bl, $dps ){
	global $config;

	if( !ord_bl_exists( $bl ) ){
		return false;
	}
	if( !is_numeric( $dps ) || $dps<0 ){
		return false;
	}

	return ria_mysql_query('
		update ord_bl set
			bl_dps_id='.( $dps==0 ? 'NULL' : $dps ).'
		where bl_tnt_id='.$config['tnt_id'].' and bl_id='.$bl
	);

}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour le site du locataire sur lequel le bon de livraison a été passée
 *	Note : le website ne peut pas être réinitialisé à NULL par cette fonction, bien que la structure SQL l'autorise
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $website Obligatoire, Identifiant du site
 *	@return bool True en cas de succès, False en cas d'échec
 */
function ord_bl_set_website( $bl, $website ){
	global $config;

	if( !ord_bl_exists($bl) ) return false;
	if( !wst_websites_exists($website) ) return false;

	$rweb = wst_websites_get( $website );
	if( !$rweb || !ria_mysql_num_rows($rweb) ) return false;
	$web = ria_mysql_fetch_array($rweb);

	if( $web['tnt_id'] != $config['tnt_id'] ){
		return false;
	}

	return ria_mysql_query('
		update ord_bl set bl_wst_id='.$website.' where bl_id='.$bl.' and bl_tnt_id='.$config['tnt_id'].'
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour le revendeur qui à pris le bon de livraison. Attention il n'y a pas de contrôle sur le fait que ce soit
 *  un utilisateur ou revendeur.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison.
 *	@param int $reseller_id Obligatoire, identifiant du revendeur qui à pris le bon de livraison
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_bl_set_reseller_id( $bl, $reseller_id ){
	global $config;

	if( !is_numeric($bl) || $bl <= 0 ){
		return false;
	}
	if( !is_numeric($reseller_id) || ($reseller_id > 0 && !gu_users_exists($reseller_id)) ){
		return false;
	}

	return ria_mysql_query('
		update ord_bl
		set bl_reseller_id = '.( $reseller_id <= 0 ? 'null' : $reseller_id ).'
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_id = '.$bl.'
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour le contact revendeur qui à pris le bon de livraison, attention il n'y a pas de contrôle sur le fait que
 * 	ce soit un contact ou un utilisateur, il est donc possible d'avoir dans ce champs des comptes qui ne sont pas des contacts.
 *	@param int $bl Obligatoire, Identifiant du bon de livraison
 *	@param int $reseller_contact_id Obligatoire, identifiant du contact revendeur qui à pris la commande
 *	@return bool True en cas de succès, false en cas d'échec.
 */
function ord_bl_set_reseller_contact_id( $bl, $reseller_contact_id ){
	global $config;

	if( !is_numeric($bl) || $bl <= 0 ){
		return false;
	}
	if( !is_numeric($reseller_contact_id) || ($reseller_contact_id > 0 && !gu_users_exists($reseller_contact_id)) ){
		return false;
	}

	return ria_mysql_query('
		update ord_bl
		set bl_reseller_contact_id = '.( $reseller_contact_id <= 0 ? 'null' : $reseller_contact_id ).'
		where bl_tnt_id = '.$config['tnt_id'].'
			and bl_id = '.$bl.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet l'ajout d'un interligne dans le bon de livraison
 * 	@param int $bl_id Obligatoire, identifiant du bon de livraison
 * 	@param bool $get_line_id Facultatif, variable booléenne permettant de savoir s'il l'on veut renvoyer le line_id de la ligne, ou pas
 * 	@param $line_id Facultatif, permet de définir l'identifiant de ligne
 * 	@param $position Facultatif, permet de définir la position dans le tri des lignes
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function ord_bl_products_add_spacing($bl_id, $get_line_id=false, $line_id=false, $position=false){
	if (!is_numeric($bl_id) || $bl_id <= 0){
		return false;
	}

	if ($line_id !== false){
		if (!is_numeric($line_id) || $line_id < 0){
			return false;
		}
	}

	global $config;

	$line = 0;
	if ($line_id === false){
		$sql_line = '
			select prd_line_id
			from ord_bl_products
			where prd_tnt_id = '.$config['tnt_id'].'
			and prd_bl_id = '.$bl_id.'
			and prd_id = 0
			order by prd_line_id desc
			limit 0,1
		';

		//Recherche la ligne
		$result_line = ria_mysql_query($sql_line);

		if ($result_line && ria_mysql_num_rows($result_line)){
			$line = ria_mysql_result($result_line, 0, 0) + 1;
		}
	} else {
		$line = $line_id;
	}

	$pos = 0;
	if ($position === false){
		$sql_pos = '
		select prd_pos
			from ord_bl_products
			where prd_tnt_id = '.$config['tnt_id'].'
			and prd_bl_id = '.$bl_id.'
			order by prd_pos desc
			limit 0,1
		';

		//Recherche la ligne
		$result_pos = ria_mysql_query($sql_pos);

		if ($result_pos && ria_mysql_num_rows($result_pos)){
			$pos = ria_mysql_result($result_pos, 0, 0) !== null ? ria_mysql_result($result_pos, 0, 0) + 1 : 'null';
		}
	} else {
		$pos = $position !== null ? $position : 'null';
	}

	// Crée la requête
	$fields = array( 'prd_tnt_id', 'prd_bl_id', 'prd_id', 'prd_line_id', 'prd_qte', 'prd_ref', 'prd_name', 'prd_price_ht', 'prd_tva_rate', 'prd_pos' );
	$values = array( $config['tnt_id'], $bl_id, 0, $line, 0, '""', '""', 0 , 0, $pos );

	$fields[] = 'prd_notes';
	$values[] = '""';

	$fields[] = 'prd_date_created';
	$values[] = 'now()';

	// Insère la ligne
	$sql = 'insert into ord_bl_products ( '.implode( ', ', $fields ).' ) values ( '.implode( ', ', $values ).' )';

	$res = ria_mysql_query($sql);

	if (!$res){
		return false;
	} else {
		ord_bl_set_date_modified( $bl_id );
	}

	return $get_line_id ? $line : $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la récupération d'un interligne dans le bon de livraison
 * 	@param int $bl_id Obligatoire, identifiant de bon de livraison
 * 	@param $line Optionnel, ligne de l'interligne dans le bon de livraison
 * 	@return resource un résultat de requête MySQL comprenant les colonnes suivantes en cas de succès :
 * 		- content : contenu de l'interligne
 *
 * 	@return bool false en cas d'échec
 */
function ord_bl_products_get_spacing($bl_id, $line=false){
	if (!is_numeric($bl_id) || $bl_id <= 0){
		return false;
	}
	if ($line !== false){
		if (!is_numeric($line) && $line < 0){
			return false;
		}
	}

	global $config;

	$sql = '
		select prd_notes as content
		from ord_bl_products
		where prd_tnt_id = '.$config['tnt_id'].'
		and prd_bl_id = '.$bl_id.'
		and prd_id = 0
	';

	if ($line !== false){
		$sql .= ' and prd_line_id = '.$line;
	}

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet la suppression d'un interligne dans le bon delivraison
 * 	@param int $bl_id Obligatoire, identifiant du bon de livraison
 * 	@param $line Optionnel, ligne de l'interligne dans le bon de livraison
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function ord_bl_products_del_spacing($bl_id, $line=false){
	if (!is_numeric($bl_id) || $bl_id <= 0){
		return false;
	}
	if ($line !== false){
		if (!is_numeric($line) && $line < 0){
			return false;
		}
	}

	global $config;

	$sql = '
		delete from ord_bl_products
		where prd_tnt_id = '.$config['tnt_id'].'
		and prd_bl_id = '.$bl_id.'
		and prd_id = 0
	';

	if ($line !== false){
		$sql .= ' and prd_line_id = '.$line;
	}

	$res = ria_mysql_query($sql);

	if ($res){
		ord_bl_set_date_modified( $bl_id );
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la mise à jour du contenu d'un interligne dans le bon de livraison
 * 	@param int $bl_id Obligatoire, identifiant du bon de livraison
 * 	@param int $line Obligatoire, ligne de l'interligne dans le bon de livraison
 * 	@param string $content Obligatoire, contenu de l'interligne
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function ord_bl_products_set_spacing($bl_id, $line, $content){
	global $config;

	if (!is_numeric($bl_id) || $bl_id <= 0){
		return false;
	}
	if (!ria_mysql_num_rows(ord_bl_products_get_spacing($bl_id, $line))){
		return false;
	}

	$content = trim($content);

	$res = ria_mysql_query('
		update ord_bl_products
			set prd_notes = \''.addslashes($content).'\'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_bl_id = '.$bl_id.'
			and prd_line_id = '.$line.'
			and prd_id = 0
	');

	if ($res){
		ord_bl_set_date_modified( $bl_id );
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet la mise à jour d'un interligne dans le bon de livraison
 * 	@param int $bl_id Obligatoire, identifiant de la commande
 * 	@param int $line Obligatoire, ligne de l'interligne dans le bon de livraison
 * 	@param string $content Facultatif, contenu de l'interligne
 * 	@param $position Facultatif, position de l'interligne dans le bon de livraison
 * 	@return bool true en cas de succès, false en cas d'échec
 */
function ord_bl_products_update_spacing($bl_id, $line, $content='', $position=false){
	global $config;

	if (!is_numeric($bl_id) || $bl_id <= 0){
		return false;
	}
	if (!is_numeric($line) || $line < 0){
		return false;
	}

	$pos = false;
	if ($position !== false){
		$pos = $position !== null ? $position : 'null';
	}

	$content = trim($content);

	$res = ria_mysql_query('
		update ord_bl_products
			set prd_notes = \''.addslashes($content).'\''.($pos !== false ? ', prd_pos = '.$pos : '').'
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_bl_id = '.$bl_id.'
			and prd_line_id = '.$line.'
			and prd_id = 0
	');

	if ($res){
		ord_bl_set_date_modified( $bl_id );
	}

	return $res;
}
// \endcond

/**	Permet la mise à jour des annotations de lignes du bon de livraison.
 *	@param int $bl_id Obligatoire, Identifiant du bl
 *	@param int $prd Obligatoire, Identifiant du produit
 *	@param int $line Obligatoire, Identifiant de ligne
 *	@param string $notes Obligatoire, Notes à adjoindre à la ligne du bon de livraison
 *
 *	@return bool True en cas de succès, False sinon
 */
function ord_bl_products_notes_update( $bl_id, $prd, $line, $notes ){
	global $config;

	if( !is_numeric($bl_id) || $bl_id <= 0 ){
		return false;
	}
	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}
	if( !is_numeric($line) || $line < 0 ){
		return false;
	}

	$res = ria_mysql_query('
		update ord_bl_products
		set prd_notes = "'.addslashes($notes).'"
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_bl_id = '.$bl_id.'
			and prd_line_id = '.$line.'
			and prd_id = '.$prd.'
	');

	if( $res ){
		ord_bl_set_date_modified( $bl_id );
	}

	return $res;

}

/// @}
