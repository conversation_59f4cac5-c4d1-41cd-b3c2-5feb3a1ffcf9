You can find documentation in the [wiki](https://github.com/mikey179/vfsStream/wiki).

Also you might want to check [vfsStream examples](https://github.com/mikey179/vfsStream-examples).


[![Build Status](https://secure.travis-ci.org/mikey179/vfsStream.png)](http://travis-ci.org/mikey179/vfsStream) [![Build Status Windows](https://ci.appveyor.com/api/projects/status/6whqgluyeggspjp1/branch/master?svg=true)](https://ci.appveyor.com/project/mikey179/vfsstream) [![Coverage Status](https://coveralls.io/repos/github/bovigo/vfsStream/badge.svg?branch=v1.x)](https://coveralls.io/github/bovigo/vfsStream?branch=v1.x)

[![Latest Stable Version](https://poser.pugx.org/mikey179/vfsStream/version.png)](https://packagist.org/packages/mikey179/vfsStream) [![Latest Unstable Version](https://poser.pugx.org/mikey179/vfsStream/v/unstable.png)](//packagist.org/packages/mikey179/vfsStream)
