<?php
$myArray['key']                = $value;
$myArray[/* key start */'key'] = $value;
$myArray[/* key start */'key'/* key end */] = $value;
$myArray  [  'key'  ]            = $value;
if ($array[($index + 1)] === true) {
} else if ($array  [ ($index + 1) ] === null) {
}
$array = [
    'foo' => 'bar',
    'bar' => 'foo',
];

if ($foo) {}
[$a, $b] = $c;

echo foo()[ 1 ];

echo $this->addedCustomFunctions['nonce'];
echo $this->deprecated_functions[ $function_name ]['version'];

echo [ 1,2,3 ][0];
echo [ 1,2,3 ][ 0 ];
echo 'PHP'[ 0 ];

$array = [];
$var = $var[$var[$var]]]; // Syntax error
$var = $var[$var[$var]; // Syntax error

$myArray[ /* key start */'key'] = $value;
$myArray[ /* key start */'key'/* key end */ ] = $value;
