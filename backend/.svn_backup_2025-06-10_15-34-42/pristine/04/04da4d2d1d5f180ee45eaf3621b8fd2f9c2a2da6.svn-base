<?php
/** 
 * \defgroup categories_tarifaires Catégories tarifaires 
 * \ingroup cpq	 
 * @{
 * \page api-prices-categories-get Chargement
 *
 * Cet appel permet la récupération de l'ensemble des catégories tarifaires. Il n'est pas paginé.
 *
 *	\code
 *		GET /prices/categories
 *	\endcode
 *
 *	@return Json Un tableau au format Json comprenant pour chaque item les clés suivantes :
 *	\code{.json}
 *       {
 *           "id": "Identifiant de la catégorie tarifaire",
 *           "name": Nom de la catégorie tarifaire,
 *           "is_sync": "Détermine si la catégorie tarifaire est synchronisée avec la gestion commerciale",
 *           "ttc": "Détermine si le tarif par défaut pour les clients associés à cette catégorie tarifaire est HT ou TTC (impacte le calcul des arrondis)",
 *           "money_code": "code de la monnaie norme conforme à la norme ISO 4217",
 *           "date_created": "Date de création de la catégorie",
 *           "date_modified": "Date de dernière modification",
 *           "date_modified_en": "Date de dernière modification au format EN",
 *           "users": "Nombre de comptes utilisateurs rattachés à cette catégorie tarifaire"          
 *       },
 *	\endcode
 * @}
*/

require_once('gu.categories.inc.php');

switch( $method ){
	case 'get':

		$array = array();
		$rcat = prd_prices_categories_get();
		while( $cat = ria_mysql_fetch_assoc($rcat) ){
			$array[] = $cat;
		}

		$content = $array;
		$result = true;
		break;
}