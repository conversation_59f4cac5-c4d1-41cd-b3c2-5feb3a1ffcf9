{"name": "sebastian/object-enumerator", "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=5.6", "sebastian/recursion-context": "~2.0"}, "require-dev": {"phpunit/phpunit": "~5"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/"]}, "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}}