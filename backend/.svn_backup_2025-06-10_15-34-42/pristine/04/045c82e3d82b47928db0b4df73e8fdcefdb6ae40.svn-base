<?php

/**	\defgroup model_fields_models Modèles de saisie
 * 	\ingroup model_fields
 *	Ce module comprend les fonctions nécessaires à la gestion des modèles de saisie.
 *	@{
 */

// \cond onlyria
/**	Cette fonction permet l'ajout d'un modèle de saisie.
 *	@param string $name Obligatoire, désignation du modèle de saisie
 *	@param string $desc Obligatoire, description du modèle de saisie
 *	@param int $class Facultatif, identifiant de la classe du modèle. La valeur par défaut est 1 (produits)
 *	@param string $tpl_id Optionnel, identifiant du template pour la génération d'un PDF (uniquement pour les commandes)
 *	@param string $ref Optionnel, référence du modèle de saisie
 *	@return int l'identifiant attribué au modèle en cas de succès
 *	@return bool false en cas d'échec
 *	@return ERR_NAME_EXISTS si le nom souhaité est déjà utilisé par un autre modèle de la classe
 */
function fld_models_add( $name, $desc, $class=CLS_PRODUCT, $tpl_id='', $ref='' ){
	global $config;

	if( !trim($name) ) return false;
	if( !fld_classes_exists($class) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	if( $class != CLS_ORDER ){
		$tpl_id = '';
	}

	// Vérifie l'unicité du nom
	$exists = ria_mysql_query('select mdl_id from fld_models where mdl_tnt_id='.$config['tnt_id'].' and mdl_cls_id='.$class.' and mdl_name=\''.addslashes($name).'\' and mdl_date_deleted is null');
	if( ria_mysql_num_rows($exists) ) return ERR_NAME_EXISTS;

	// Calcule la position d'affichage du nouveau modèle
	$pos = 'null';
	$order = fld_models_get_order();
	if( $order ){
		$res_pos = ria_mysql_query('select max(mdl_pos)+1 from fld_models where mdl_tnt_id='.$config['tnt_id'].' and mdl_date_deleted is null');
		if( $res_pos!==false && ria_mysql_num_rows($res_pos) )
			$pos = ria_mysql_result($res_pos,0,0);
	}

	// crée la requête
	$fields = array();
	$values = array();

	$fields[] = 'mdl_tnt_id';
	$values[] = $config['tnt_id'];
	$fields[] = 'mdl_name';
	$values[] = '\''.addslashes($name).'\'';
	$fields[] = 'mdl_desc';
	$values[] = '\''.addslashes($desc).'\'';
	$fields[] = 'mdl_cls_id';
	$values[] = $class;
	$fields[] = 'mdl_pos';
	$values[] = $pos;
	$fields[] = 'mdl_date_created';
	$values[] = 'now()';
	$fields[] = 'mdl_template_id';
	$values[] = trim($tpl_id) != '' ? '"'.addslashes( $tpl_id ).'"' : 'null';
	$fields[] = 'mdl_ref';
	$values[] = '"'.addslashes( $ref ).'"';

	$res = ria_mysql_query('
		insert into fld_models
			('.implode( ',',$fields ).')
		values
			('.implode( ',',$values ).')
	');

	if( $res ){
		$mdl_id = ria_mysql_insert_id();
		// force la mise à jour des configs sur les applications
		dev_devices_need_sync_add(0,_DEV_TASK_MODEL);
		return $mdl_id;
	}else{
		return false;
	}

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification d'un modèle de saisie.
 *	@param int $id Obligatoire, identifiant du modèle de saisie
 *	@param string $name Obligatoire, désignation du modèle de saisie
 *	@param string $desc Obligatoire, description du modèle de saisie
 *	@param string $tpl_id Optionnel, identifiant du template pour la génération d'un PDF (uniquement pour les commandes)
 *	@param string $ref Optionnel, référence du modèle de saisie
 *	@return bool true en cas de succès, false en cas d'échec
 *	@return ERR_NAME_EXISTS si le nom souhaité est déjà utilisé par un autre modèle de la classe
 */
function fld_models_update( $id, $name, $desc, $tpl_id='', $ref='' ){
	global $config;

	$rmdl = fld_models_get( $id );
	if( $rmdl===false || !ria_mysql_num_rows($rmdl) ) return false;
	$mdl = ria_mysql_fetch_array($rmdl);
	if( !trim($name) ) return false;

	$name = ucfirst(trim($name));
	$desc = ucfirst(trim($desc));

	$class = fld_models_get_class( $id );
	if( $class != CLS_ORDER ){
		$tpl_id = '';
	}

	// Vérifie l'unicité du nom
	$exists = ria_mysql_query('select mdl_id from fld_models where mdl_tnt_id='.$config['tnt_id'].' and mdl_cls_id='.$mdl['cls_id'].' and mdl_name=\''.addslashes($name).'\' and mdl_id!='.$id.' and mdl_date_deleted is null');
	if( ria_mysql_num_rows($exists) ) return ERR_NAME_EXISTS;

	$res = ria_mysql_query('
		update fld_models set
			mdl_name=\''.addslashes($name).'\',
			mdl_desc=\''.addslashes($desc).'\',
			mdl_template_id = '.( trim($tpl_id) != '' ? '"'.addslashes( $tpl_id ).'"' : 'null' ).',
			mdl_ref = "'.addslashes( $ref ).'"
		where mdl_tnt_id='.$config['tnt_id'].' and mdl_id='.$id.'
	');

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'un modèle de saisie. La suppression
 *	d'un modèle n'est possible que si aucun objet de la classe correspondante n'y est associé.
 *	@param int $id Obligatoire, identifiant du modèle à supprimer
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_models_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	if( fld_object_models_get_count(0,$id)>0 ) return false;

	// Charge le modèle
	$rmdl = fld_models_get($id);
	if( !ria_mysql_num_rows($rmdl) ) return true;
	$mdl = ria_mysql_fetch_array($rmdl);

	// Actualise les tris personnalisés
	$order = fld_models_get_order();
	if( $order ){
		ria_mysql_query('update fld_models set mdl_pos=mdl_pos-1 where mdl_tnt_id='.$config['tnt_id'].' and mdl_pos>='.$mdl['pos'].' and mdl_date_deleted is null');
	}

	$res = ria_mysql_query('update fld_models set mdl_date_deleted=now() where mdl_tnt_id='.$config['tnt_id'].' and mdl_id='.$id);
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
// \endcond

/**	Cette fonction permet le chargement d'un ou plusieurs modèles de saisie,
 *	éventuellement filtrés en fonction des paramètres optionnels fournis.
 *	@param int $id Facultatif, identifiant d'un modèle sur lequel filtrer le résultat
 *	@param int|array $obj Facultatif, identifiant d'un objet de la classe correspondante au modèle sur lequel filtrer le résultat (ou tableau d'identifiants pour les clés composées)
 *	@param int $class Facultatif, identifiant de classe sur lequel filtrer le résultat. La valeur par défaut est null (pas de filtres).
 *	@param bool $include_fld_generic Facultatif, détermine si, pour les décomptes, les champs libres génériques sont décomptés
 *	@param int $fld_cat Facultatif, filtre sur la catégorie de champs présent dans le modèle
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du modèle de saisie
 *			- ref : référence du modèle de saisie utilisée par la gestion commerciale
 *			- name : désignation du modèle de saisie
 *			- desc : description du modèle de saisie
 *			- fields : nombre de champs contenus dans le modèle
 *			- objects : nombre d'objets associés à ce modèle
 *			- pos : position d'affichage du modèle de saisie (si trié par ordre arbitraire)
 *			- cls_id : identifiant de classe
 *			- as_options : utiliser comme options de déclinaisons
 *			- tpl_id : identifiant du template utilisé pour générer un PDF (Pandadoc)
 *			- is_sync : détermine si le modèle est synchronisé avec la gestion commerciale
 */
function fld_models_get( $id=0, $obj=0, $class=null, $include_fld_generic=true, $fld_cat=0 ){
	global $config;

	if( $id>0 && !fld_models_exists($id) ) return false;
	if( $class!=null && !fld_classes_exists($class) ) return false;

	if( is_array($obj) ){
		if( sizeof($obj)<1 || sizeof($obj)>COUNT_OBJ_ID ) return false;
		foreach( $obj as $o ){
			if( !is_numeric($o) || $o<0 ) return false;
		}
	}else{
		if( !is_numeric($obj) || $obj<0 ) return false;
		if( $obj == 0 )
			$obj = array();
		else
			$obj = array( $obj );
	}

	$sql = '
		select mdl_id as id, mdl_ref as ref,
			mdl_name as name,
			mdl_desc as "desc", (
				select count(fld_id)
				from fld_model_fields join
				fld_fields
					on ( fld_id=mf_fld_id and ( '.( $include_fld_generic ? 'fld_tnt_id=0 or ' : '' ).' fld_tnt_id=mf_tnt_id ) )
				where fld_date_deleted is null and mdl_id=mf_mdl_id and mf_tnt_id='.$config['tnt_id'].'
			) as "fields", (
				select count( * )
				from fld_object_models
				where mdl_id=pm_mdl_id and pm_tnt_id='.$config['tnt_id']. '
			) as "objects",
			mdl_pos as "pos",
			mdl_cls_id as "cls_id",
			ifnull(mdl_as_options, 0) as as_options,
			ifnull(mdl_template_id, "") as tpl_id,
			mdl_is_sync as is_sync
		from fld_models
		where mdl_tnt_id='.$config['tnt_id'].' and mdl_date_deleted is null
	';

	if( sizeof($obj) ){
		$part_sql = '';

		$i = 0;
		while( $i<sizeof($obj) ){
			$part_sql .= ' and pm_obj_id_'.$i.'='.$obj[$i].' ';
			$i++;
		}

		$sql .= ' and exists ( select 1 from fld_object_models where pm_tnt_id='.$config['tnt_id'].' and mdl_id=pm_mdl_id '.$part_sql.' ) ';
	}

	if( $id>0 )
		$sql .= ' and mdl_id='.$id;
	if( $class!=null )
		$sql .= ' and mdl_cls_id='.$class;

	if (is_numeric($fld_cat) && $fld_cat > 0 ) {
		$sql .= '
			and exists (
				select 1
				from fld_fields
				join fld_model_fields on mf_tnt_id=fld_tnt_id and mf_fld_id=fld_id
				where fld_tnt_id='.$config['tnt_id'].'
				and  mdl_id=mf_mdl_id
				and fld_cat_id='.$fld_cat.'
			)
		';
	}

	$sql .= '
		order by mdl_pos, mdl_name
	';

	return ria_mysql_query($sql);
}

// \cond onlyria
/**	Cette fonction permet la modification de la méthode de tri utilisée pour les modèles de saisie.
 *	@param bool $order Mode de tri. 0/false pour un tri alphabétique, 1/true pour un tri numérique défini par l'utilisateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_models_update_order( $order ){
	global $config;

	if( $order ){
		$models = fld_models_get();
		$pos = 0;
		while( $m = ria_mysql_fetch_array($models) ){
			ria_mysql_query('update fld_models set mdl_pos='.$pos.' where mdl_tnt_id='.$config['tnt_id'].' and mdl_id='.$m['id'].' and mdl_date_deleted is null');
			$pos++;
		}
		return true;
	}else{
		return ria_mysql_query('update fld_models set mdl_pos=null where mdl_date_deleted is null and mdl_tnt_id='.$config['tnt_id']);
	}
}
// \endcond

// \cond onlyria
/**	Permet le déplacement vers le haut d'un modèle de saisie.
 *	@param int $id Obligatoire, identifiant du modèle à déplacer vers le haut
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_models_move_up( $id ){
	global $config;

	if( !is_numeric($id) ) return false;

	// Charge le modèle de saisie
	$rmdl = fld_models_get($id);
	if( !ria_mysql_num_rows($rmdl) ) return false;
	$mdl = ria_mysql_fetch_array($rmdl);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( $mdl['pos']==='' ) return false;

	// S'assure que la catégorie n'est pas déjà la première de la liste
	if( $mdl['pos']===0 ) return false;

	// Permute la catégorie avec celle qui se trouve juste au dessus
	ria_mysql_query('update fld_models set mdl_pos='.($mdl['pos']-1).' where mdl_tnt_id='.$config['tnt_id'].' and mdl_id='.$id.' and mdl_date_deleted is null');
	ria_mysql_query('update fld_models set mdl_pos='.($mdl['pos']).' where mdl_tnt_id='.$config['tnt_id'].' and mdl_pos='.($mdl['pos']-1).' and mdl_id!='.$id.' and mdl_date_deleted is null');

	return true;
}
// \endcond

// \cond onlyria
/**	Permet le déplacement vers le bas d'un modèle de saisie.
 *	@param int $id Obligatoire, identifiant du modèle à déplacer vers le bas
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_models_move_down( $id ){
	global $config;

	if( !is_numeric($id) ) return false;

	// Charge la catégorie
	$rmdl = fld_models_get($id);
	if( !ria_mysql_num_rows($rmdl) ) return false;
	$mdl = ria_mysql_fetch_array($rmdl);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( $mdl['pos']==='' ) return false;

	// S'assure que la catégorie n'est pas déjà en fin de la liste
	$rnext = ria_mysql_query('select mdl_id from fld_models where mdl_tnt_id='.$config['tnt_id'].' and mdl_pos='.($mdl['pos']+1).' and mdl_date_deleted is null');
	if( !ria_mysql_num_rows($rnext) ) return false;

	// Permute la catégorie avec celle qui se trouve juste au dessous
	ria_mysql_query('update fld_models set mdl_pos='.($mdl['pos']+1).' where mdl_tnt_id='.$config['tnt_id'].' and mdl_id='.$id.' and mdl_date_deleted is null');
	ria_mysql_query('update fld_models set mdl_pos='.($mdl['pos']).' where mdl_tnt_id='.$config['tnt_id'].' and mdl_pos='.($mdl['pos']+1).' and mdl_id!='.$id.' and mdl_date_deleted is null');

	return true;
}
// \endcond

// \cond onlyria
/**	Déplace le modèle avant ou après un autre modèle
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *	L'utilisateur doit s'assurer que les 2 modèles appartiennent au même parent (sinon ça n'a pas de sens)
 *
 *	@param int $source Identifiant du modèle source
 *	@param int $target Identifiant du modèle cible
 *	@param string $where Chaîne de caractères qui vaut soit "before" soit "after"
 *
 *	@return bool true en cas de succès, false sinon
 */
function fld_models_position_update( $source, $target, $where ) {
	return obj_position_update( DD_FLD_MODEL, $source, $target, $where );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification de la méthode de tri utilisée pour les champs d'un modèle.
 *	@param int $mdl Modèle dont on souhaite modifier l'ordre d'apparition des enfants.
 *	@param bool $order Mode de tri. 0/false pour un tri alphabétique, 1/true pour un tri numérique défini par l'utilisateur
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_models_order_update( $mdl, $order ){
	global $config;

	if( !is_numeric($mdl) ) return false;
	if( !fld_models_exists($mdl) ) return false;

	if( $order ){
		$childs = fld_fields_get(0,0,$mdl);
		$pos = 0;
		while( $c = ria_mysql_fetch_array($childs) ){
			ria_mysql_query('update fld_model_fields set mf_fld_pos='.$pos.' where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl.' and mf_fld_id='.$c['id']);
			$pos++;
		}
		return true;
	}else{
		return ria_mysql_query('update fld_model_fields set mf_fld_pos=null where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl);
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier les champs contenus dans un modèle.
 *	\remarks La fonction ne tient pas compte des champs génériques, sur lesquels le tri n'est pas envisgeable
 *	@param int $mdl Identifiant du modèle
 *	@return bool false si la méthode de tri est alphabétique, true si la méthode de tri est personnalisée
 */
function fld_models_order_get( $mdl ){
	global $config;

	if( !is_numeric($mdl) ) return false;
	if( !fld_models_exists($mdl) ) return false;

	$res = ria_mysql_query('
		select mf_fld_id from fld_model_fields
			join fld_fields on ( fld_id=mf_fld_id and fld_tnt_id=mf_tnt_id )
		where fld_date_deleted is null and mf_tnt_id='.$config['tnt_id'].' and mf_fld_pos is not null
			and mf_mdl_id='.$mdl.'
	');

	if( $res===false ) return false;
	return ria_mysql_num_rows($res);
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le mode utilisé pour trier les modèles de saisie.
 *	@return bool false si la méthode de tri est alphabétique, true si la méthode de tri est personnalisée
 */
function fld_models_get_order(){
	global $config;

	return ria_mysql_num_rows(ria_mysql_query('
		select mdl_id from fld_models
		where mdl_tnt_id='.$config['tnt_id'].' and mdl_pos is not null and mdl_date_deleted is null
	'))>0;

}
// \endcond

// \cond onlyria
/** Détermine la classe d'objet d'un modèle
 *	@param int $id Identifiant du modèle
 *	@return int|bool Identifiant de classe ou False en cas d'échec
 */
function fld_models_get_class( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select mdl_cls_id
		from fld_models
		where mdl_date_deleted is null
			and mdl_tnt_id='.$config['tnt_id'].'
			and mdl_id='.$id.'
	');

	if( $res===false || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res,0,0 );
}
// \endcond

// \cond onlyria
/**	Permet le déplacement vers le haut d'un champ dans un modèle de saisie. S'applique aux modèles dont les enfants
 *	sont triés de manière personnalisée.
 *	\remarks Ne tient aps compte des champs avancés génériques
 *	@param int $mdl Identifiant du modèle de saisie
 *	@param int $fld Identifiant du champ à déplacer à le champ
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_model_fields_move_up( $mdl, $fld ){
	global $config;

	if( !is_numeric($mdl) ) return false;
	if( !is_numeric($fld) ) return false;

	if( !fld_fields_is_tenant_linked($fld) ) return false;

	// Charge la position du champ dans le modèle de saisie
	$rfld = ria_mysql_query('select mf_fld_id as id, mf_fld_pos as "pos" from fld_model_fields where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl.' and mf_fld_id='.$fld);
	if( !ria_mysql_num_rows($rfld) ) return false;
	$fld = ria_mysql_fetch_array($rfld);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( $fld['pos']==='' ) return false;

	// S'assure que la catégorie n'est pas déjà la première de la liste
	if( $fld['pos']===0 ) return false;

	// Permute la catégorie avec celle qui se trouve juste au dessus
	ria_mysql_query('update fld_model_fields set mf_fld_pos='.($fld['pos']-1).' where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl.' and mf_fld_id='.$fld['id']);
	ria_mysql_query('update fld_model_fields set mf_fld_pos='.($fld['pos']).' where mf_tnt_id='.$config['tnt_id'].' and mf_fld_pos='.($fld['pos']-1).' and mf_mdl_id='.$mdl.' and mf_fld_id!='.$fld['id']);

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return true;
}
// \endcond

// \cond onlyria
/**	Déplace le champ avant ou après un autre champ
 *	Utilisé à la place de move_up ou move_down qui ne permet de déplacement que d'une unité (utile pour drag&drop)
 *	L'utilisateur doit s'assurer que les 2 champs appartiennent au même parent (sinon ça n'a pas de sens)
 *
 *	@param int $mdl Obligatoire, identifiant du modèle de saisie dans lequel les champs sont utilisés
 *	@param int $source Obligatoire, Identifiant du champ source qui est à déplacer
 *	@param int $target Obligatoire, Identifiant du champ destination qui va servir de pivot
 *	@param string $where Obligatoire, chaîne de caractères qui vaut soit "before" soit "after". Si \c $where vaut 'before', le champ source sera déplacé avant \c $target. Dans le cas contraire, il sera placé après.
 *
 *	@return bool true en cas de succès, false sinon
 */
function fld_model_fields_position_update( $mdl, $source, $target, $where ) {
	$res = obj_position_update( DD_FLD, array('mdl' => $mdl, 'fld' => $source), array('mdl' => $mdl, 'fld' => $target), $where );
	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
// \endcond

// \cond onlyria
/**	Permet le déplacement vers le bas d'un champ dans un modèle de saisie. S'applique aux modèles dont les enfants
 *	sont triés de manière personnalisée.
 *	\remarks Ne tient aps compte des champs avancés génériques
 *	@param int $mdl Obligatoire, identifiant du modèle au sein duquel le tri est à effectuer
 *	@param int $fld Obligatoire, identifiant du champ à déplacer vers le bas
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_model_fields_move_down( $mdl, $fld ){
	global $config;

	if( !is_numeric($mdl) ) return false;
	if( !is_numeric($fld) ) return false;

	if( !fld_fields_is_tenant_linked($fld) ) return false;

	// Charge la position du champ dans le modèle de saisie
	$rfld = ria_mysql_query('select mf_fld_id as id, mf_fld_pos as "pos" from fld_model_fields where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl.' and mf_fld_id='.$fld);
	if( !ria_mysql_num_rows($rfld) ) return false;
	$fld = ria_mysql_fetch_array($rfld);

	// S'assure que le mode de tri est bien 'Personnalisé'
	if( $fld['pos']==='' ) return false;

	// S'assure que le champ n'est pas déjà en fin de la liste
	$rnext = ria_mysql_query('select mf_fld_id from fld_model_fields where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl.' and mf_fld_pos='.($fld['pos']+1));
	if( !ria_mysql_num_rows($rnext) ) return false;

	// Permute la catégorie avec celle qui se trouve juste au dessous
	ria_mysql_query('update fld_model_fields set mf_fld_pos='.($fld['pos']+1).' where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl.' and mf_fld_id='.$fld['id']);
	ria_mysql_query('update fld_model_fields set mf_fld_pos='.($fld['pos']).' where mf_tnt_id='.$config['tnt_id'].' and mf_fld_pos='.($fld['pos']+1).' and mf_mdl_id='.$mdl.' and mf_fld_id!='.$fld['id']);

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction retourne le nombre de modèles enregistrés
 *	@param int $class Facultatif, identifiant de classe sur lequel filtrer le nombre de résultats
 *	@return int le nombre de modèles enregistrés
 */
function fld_models_get_count( $class=null ){
	global $config;

	if( $class!=null && !fld_classes_exists($class) ) return false;

	$sql = 'select count(*) from fld_models where mdl_date_deleted is null and mdl_tnt_id='.$config['tnt_id'];
	if( $class!=null ){
		$sql .= ' and mdl_cls_id='.$class;
	}

	$res = ria_mysql_query($sql);

	if( $res===false ) return 0;
	return ria_mysql_result($res,0,0);
}
// \endcond

/**	Cette fonction permet le chargement du nom d'un modèle.
 *	@param int $id Obligatoire, identifiant du modèle dont on souhaite charger le nom.
 *	@return string|bool le nom du modèle en cas de succès, false en cas d'échec
 */
function fld_models_get_name( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$rname = ria_mysql_query('select mdl_name from fld_models where mdl_date_deleted is null and mdl_tnt_id='.$config['tnt_id'].' and mdl_id='.$id);
	if( !ria_mysql_num_rows($rname) ) return false;
	return ria_mysql_result($rname,0,0);
}

/**	Cette fonction permet le chargement des champs associés à un modèle. Il s'agit
 *	d'un alias sur fld_fields_get spécialisant son utilisation.
 *	\remarks pas de filtre sur les champs génériques : théoriquement, ils ne sont pas inclus dans les modèles
 *	@param int $id Obligatoire, identifiant du modèle
 *	@return bool false en cas d'erreur
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du champ
 *			- name : désignation du champ
 *			- desc : description du champ
 *			- type_id : identifiant du type de champ
 *			- cls_id : classe du champ (normalement la même que celle du modèle)
 *			- type_name : désignation du type de champ
 *			- unit_id : identifiant de l'unité du champ
 *			- unit_name : désignation de l'unité du champ
 *			- min : valeur minimale acceptée par le champ (dépendant du type)
 *			- max : valeur maximale acceptée par le champ (dépendant du type)
 *			- precision : nombre de chiffres affichés après la virgule (champs de type flottant uniquement)
 *			- cat_id : identifiant de la catégorie de classement du champ
 *			- cat_name : désignation de la catégorie de classement du champ
 */
function fld_models_get_fields( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	return fld_fields_get( 0,0,$id, 0, 0, 0, null, [], true );
}

// \cond onlyria
/**	Cette fonction retourne le nombre de champs contenus dans un modèle donné.
 *	@param int $id Obligatoire, identifiant du modèle concerné
 *	@return int le nombre de champs contenus dans le modèle, ou false en cas d'erreur
 */
function fld_models_get_fields_count( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select count(*)
		from fld_model_fields
		join fld_fields
			on ( fld_id=mf_fld_id and fld_tnt_id=mf_tnt_id )
		where fld_date_deleted is null and mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$id
	);

	if( $res===false ) return false;
	return ria_mysql_result($res,0,0);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification d'un identifiant de modèle de saisie.
 *	@param int $id Obligatoire, identifiant à vérifier
 *	@return bool true si l'identifiant est valide
 *	@return bool false si l'identifiant est invalide ou si une erreur se produit
 */
function fld_models_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('select mdl_id from fld_models where mdl_date_deleted is null and mdl_tnt_id='.$config['tnt_id'].' and mdl_id='.$id);
	if( $res===false ) return false;
	return ria_mysql_num_rows($res);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet l'ajout d'un champ à un modèle de saisie.
 *	@param int $mdl Obligatoire, identifiant du modèle
 *	@param int $fld Obligatoire, identifiant du champ
 *	@param null|int $pos Optionnel, permet de forcer la position du champ dans son modèle
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_models_fields_add( $mdl, $fld, $pos=null ){
	global $config;

	if( !is_numeric($mdl) || $mdl <= 0 ){
		return false;
	}

	if( !is_numeric($fld) || $fld <= 0 ){
		return false;
	}

	if( $pos !== null ){
		if( !is_numeric($pos) || $pos <= 0 ){
			return false;
		}
	}

	// Contrôle que ce champ avancé est ajoutable à un modèle
	$r_field = fld_fields_get( $fld, 0, 0, 0, 0, 0, null, array(), true, array(), null, null, null, false, null, null, false, true, true );
	if( !$r_field || !ria_mysql_num_rows($r_field) ){
		return false;
	}

	$field = ria_mysql_fetch_assoc( $r_field );

	// Contrôle que la classe du champ est bien la même que le modèle de saisie
	// Ex. On ne peut pas mettre un champ lié au produit dans un modèle utilisé pour les catégories de produit
	if( $field['cls_id'] != fld_models_get_class($mdl) ){
		return false;
	}

	$inst_pos = 'null';
	if( $pos === null ){
		if( fld_models_order_get($mdl) ){
			$res = ria_mysql_query('select count(*) from fld_model_fields where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl);
			if( $res!==false && ria_mysql_num_rows($res) ){
				$inst_pos = ria_mysql_result($res,0,0);
			}
		}
	}else{
		$inst_pos = $pos;
	}

	$res = ria_mysql_query('
		insert into fld_model_fields
			(mf_tnt_id,mf_mdl_id,mf_fld_id,mf_fld_pos)
		values
			('.$config['tnt_id'].','.$mdl.','.$fld.','.$inst_pos.')
	');

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'un champ d'un modèle de saisie.
 *	@param int $mdl Obligatoire, identifiant du modèle
 *	@param int $fld Obligatoire, identifiant du champ
 *	@return bool true en cas de succès, false en cas d'échec
 */
function fld_models_fields_del( $mdl, $fld ){
	global $config;

	if( !fld_models_exists($mdl) ) return false;
	if( !fld_fields_exists($fld) ) return false;

	$rpos = ria_mysql_query('select mf_fld_pos from fld_model_fields where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl.' and mf_fld_id='.$fld);
	if( !ria_mysql_num_rows($rpos) )
		return true;
	$pos = ria_mysql_result($rpos,0,0);

	ria_mysql_query('update fld_model_fields set mf_fld_pos=mf_fld_pos-1 where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl.' and mf_fld_pos>'.$pos);

	$res = ria_mysql_query('
		delete from fld_model_fields
		where mf_tnt_id='.$config['tnt_id'].' and mf_mdl_id='.$mdl.' and mf_fld_id='.$fld.'
	');

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère les liaisons entre modèles et champ avancés
 *	@param int $mdl Optionnel, identifiant de modèle ou tableau d'identifiants
 *	@param int $fld Optionnel, identifiant de champ ou tableau d'identifiants
 *	@param bool $as_options Optionnel, si oui ou non le modèle est utilisé comme options de déclinaisons (par défaut à null, donc n'en tient pas compte)
 * 	@param mixed $sort Optionnel, permet d'appliquer un tri (par défaut aucun), ex. ['col' => 'dir'], col = sort et dir = asc|desc
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- mdl_id : identifiant du modèle
 *		- fld_id : identifiant du champ avancé
 *		- fld_pos : position du champ dans le modèle
 */
function fld_models_fields_get( $mdl=0, $fld=0, $as_options=null, $sort=false ){
	global $config;

	$mdl = control_array_integer( $mdl, false );
	if( $mdl === false ){
		return false;
	}

	$fld = control_array_integer( $fld, false );
	if( $fld === false ){
		return false;
	}

	$sql = '
		select
			mf_mdl_id as mdl_id, mf_fld_id as fld_id, mf_fld_pos as fld_pos
		from
			fld_model_fields
			join fld_fields on (mf_tnt_id = fld_tnt_id or fld_tnt_id = 0) and mf_fld_id = fld_id
			join fld_models on mf_tnt_id = mdl_tnt_id and mf_mdl_id = mdl_id
		where
			mf_tnt_id = '.$config['tnt_id'].' and
			fld_date_deleted is null and
			mdl_date_deleted is null
	';

	if( sizeof($mdl) ){
		$sql .= ' and mf_mdl_id in ('.implode(', ', $mdl).')';
	}

	if( sizeof($fld) ){
		$sql .= ' and mf_fld_id in ('.implode(', ', $fld).')';
	}

	if( $as_options !== null ){
		if( $as_options ){
			$sql .= ' and ifnull( mdl_as_options, 0 ) = 1';
		}else{
			$sql .= ' and ifnull( mdl_as_options, 0 ) = 0';
		}
	}

	if( is_array($sort) && count($sort) ){
		$sort_final = [];

		foreach( $sort as $col=>$dir ){
			$dir = !in_array($dir, ['asc', 'desc']) ? 'asc' : $dir;

			switch( $col ){
				case 'pos' :
					array_push($sort_final, 'mf_fld_pos '.$dir);
					break;
			}
		}

		if( count($sort_final) ){
			$sql .= ' order by '.implode(', ', $sort_final);
		}
	}

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la position d'une option dans un modèle de saisie.
 * 	@param int $mdl_id Obligatoire, identifiant d'un model de saisie
 * 	@param int $fld_id Obligatoire, identifiant d'un champ avancé
 * 	@param null|int $opt_pos Optionnel, position de l'option (null est autorisé)
 */
function fld_models_fields_set_opt_position( $mdl_id, $fld_id, $opt_pos=null ){
	global $config;

	if( !is_numeric($mdl_id) || $mdl_id <= 0 ){
		return false;
	}

	if( !is_numeric($fld_id) || $fld_id <= 0 ){
		return false;
	}

	if( $opt_pos !== null ){
		if( !is_numeric($opt_pos) || $opt_pos < 0 ){
			return false;
		}
	}

	return ria_mysql_query('
		update fld_model_fields
		set mf_opt_position = '.( $opt_pos === null ? 'null' : $opt_pos ).'
		where mf_tnt_id = '.$config['tnt_id'].'
			and mf_mdl_id = '.$mdl_id.'
			and mf_fld_id = '.$fld_id.'
	');
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les options de variations liés aux modèles de saisie rattachés à un objet.
 * 	@param int $obj_id Obligatoire, identifiant d'un ou plusieurs objets
 * 	@return resource Un résultat MySQL contenant pour chaque ligne :
 * 		- id : identifiant du champ avancé
 * 		- name : nom du champ avancé
 * 		- obj_ids_0 : liste des identifiants liés à ce champs
 */
function fld_models_fields_get_options( $obj_id ){
	global $config;

	$obj_id = control_array_integer( $obj_id, true );
	if( $obj_id === false ){
		return false;
	}

	return ria_mysql_query('
		select distinct fld_id as id, fld_name as name, group_concat(pm_obj_id_0) as obj_ids_0
		from fld_object_models
			join fld_model_fields on (mf_tnt_id = '.$config['tnt_id'].' and mf_mdl_id = pm_mdl_id)
				join fld_fields on (fld_tnt_id = '.$config['tnt_id'].' and fld_id = mf_fld_id)
		where pm_tnt_id = '.$config['tnt_id'].'
			and pm_obj_id_0 in ('.implode(', ', $obj_id).')
				and ifnull(mf_opt_position, -1) >= 0
		group by fld_id, fld_name
		order by mf_opt_position asc
	');
}
// \endcond

// \cond onlyria
/** Cette fonction récupère les identifiants de tous les objets associés à un modèle de saisie
 *	@param $mdl Identifiant du modèle de saisie
 *	@return bool False en cas d'erreur
 *	@return resource Un résultat de requête MySQL comprenant les champs suivants :
 *		- obj_id : Identifiant de l'objet
 *		- obj_id_2 : Identifiant de l'objet composé (0 si l'objet n'est pas composé)
 *		- obj_id_3 : Identifiant de l'objet composé (0 si l'objet n'est pas composé)
 *		- obj_id_X : Etc...
 */
function fld_models_get_objects( $mdl ){
	global $config;

	if( !is_numeric($mdl) || $mdl<0 ) return false;

	$sql = 'select ';

	for( $i=0; $i<COUNT_OBJ_ID; $i++ ){
		$sql .= ' pm_obj_id_'.$i.' as "obj_id'.( $i==0 ? '' : '_'.($i + 1) ).'" ';
		if( $i < (COUNT_OBJ_ID - 1) )
			$sql .= ',';
	}

	$sql .= '
		from fld_object_models
		where pm_tnt_id='.$config['tnt_id'].'
		and pm_mdl_id='.$mdl.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour l'information pour savoir si le modèle est utiliser pour identifiant les articles enfants.
 * 	@param int $mdl_id Obligatoire, identifiant du modèle de saisie
 * 	@param bool $as_options Optionnel, si oui ou non le modèle est utiliser comme sélecteur d'articles enfants (false par défaut)
 * 	@return bool true en cas de succès, false dans le cas contraire
 */
function fld_models_set_as_options( $mdl_id, $as_options=false ){
	if( !is_numeric($mdl_id) || $mdl_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		update fld_models
		set mdl_as_options = '.( $as_options ? 1 : 0 ).'
		where mdl_tnt_id = '.$config['tnt_id'].'
			and mdl_id = '.$mdl_id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

/// @}
