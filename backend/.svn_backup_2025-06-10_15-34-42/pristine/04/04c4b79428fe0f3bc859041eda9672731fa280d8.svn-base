<?php

	/**	\file ajax-holidays.php
	 * 	Ce fichier permet la mise à jour des jours fériés pour lesquels l'expédition des colis est maintenus.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_EXPEDITIONS');

	header("Content-Type: application/xml");
	$xml = '<?xml version="1.0" encoding="utf-8"?>';

	// Permet de modifier les jours fériés sans rechargement de la page de configuration des expéditions.
	if( isset($_POST['year']) ){
		// Début du résultat sous forme XML
		$xml .= '<result><head-year><![CDATA[';
		
		// Détermine l'entête du tableau, pour la navigation entre les années
		$xml .= '<th style="text-align:left;">';
		if( date('Y')!=$_POST['year'] )
			$xml .= '<img class="hld-nav-year" title="' . _("Année")   .($_POST['year']-1).'" alt="Année '.($_POST['year']-1).'" src="/admin/images/expeditions/feries_active_left.svg" onclick="holidays('.($_POST['year']-1).')" />';
		$xml .= '</th>';
		$xml .= '<th style="text-align:center">' . _("Année ") .$_POST['year'].'</th>';
		$xml .= '<th style="text-align:right">';
		if( $_POST['year']<2037 )
			$xml .= '<img class="hld-nav-year" title="' . _("Année")   .($_POST['year']+1).'" alt="Année '.($_POST['year']+1).'" src="/admin/images/expeditions/feries_active_right.svg" onclick="holidays('.($_POST['year']+1).')" />';
		$xml .= '</th>';
		$xml .= ']]></head-year>\n';
			
		// Récupère toutes les dates de jours fériés (pour l'année en cours de modification) où les expéditions ont lieu
		$rhld = dlv_holidays_get( $_POST['year'], true );
					
		// Construit un tableau de toutes les dates
		$current_holidays = array();
		if( $rhld!=false ){
			while( $hld = ria_mysql_fetch_array($rhld) ){
				$current_holidays[] = $hld['date'];
			}
		}
		
		$holidays = holidays($_POST['year']);
		// 	Construit le corps du tableau selon l'année passée en paramètre
		$xml .= '<body-year><![CDATA[';
		foreach( $holidays as $date=>$name ){
			$xml .= '	<tr>';
			$xml .= '		<td headers="hld-date" class="hld-date">'.dateformatcomplet( strtotime($date) ).'</td>';
			$xml .= '		<td headers="hld-name">'.$name.'</td>';
			$xml .= '		<td headers="hld-exp" class="hld-exp">';
			$xml .= '			<input type="radio" name="date['.$date.']" id="hld-exp-yes-'.$date.'" value="1" '.( in_array($date, $current_holidays) ? 'checked="checked"' : '' ).' /><label for="hld-exp-yes-'.$date.'">' . _("Oui") . '</label>';
			$xml .= '			<input type="radio" name="date['.$date.']" id="hld-exp-no-'.$date.'" value="0" '.( in_array($date, $current_holidays) ? '' : 'checked="checked"' ).' /><label for="hld-exp-no-'.$date.'">' . _("Non") . '</label>';
			$xml .= '		</td>';
			$xml .= '	</tr>';
		}
		$xml .= ']]></body-year>';
		
		// Fin du résultat sous forme XML
		$xml .= '</result>';
	}
	
	// Permet de sauvegarder si oui ou non les expéditions auront lieu les jours fériés d'une année précise
	if( isset($_POST['saveHolidays']) ){
		if( !isset($_POST['date']) ){
			$xml .= "<result type=\"0\">";
			$xml .= "<error>" . _("Une erreur inattendue est survenue lors de l'enregistrement des jours fériés où les expéditions sont maintenues.") .  "<br />" . _("Veuillez réessayer ou prendre contact pour nous signaler l'erreur") . "</error>";
			$xml .= "</result>";
		} else {
			$error = false;
			foreach( $_POST['date'] as $date=>$exp ){
				if( !dlv_holidays_add($date, $exp) ){
					$error = true;
					break;
				}
			}
			if( $error ){
				$xml .= "<result type=\"0\">";
				$xml .= "<error>" . _("Une erreur inattendue est survenue lors de l'enregistrement des jours fériés où les expéditions auront lieu.") .  "<br />" . _("Veuillez réessayer ou prendre contact pour nous signaler l'erreur") . "</error>";
				$xml .= "</result>";
			}else{
				$xml .= "<result type=\"1\">";
				$xml .= "<success>" . _("L'enregistrement des jours fériés où les expéditions auront lieu, s'est correctement déroulé.") . "</success>";
				$xml .= "</result>";
			}
		}
	}
	
	print $xml;
    exit;