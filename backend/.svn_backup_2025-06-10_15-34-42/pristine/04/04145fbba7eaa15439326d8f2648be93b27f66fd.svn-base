<?php

class ArrayOfPartNumberOUT implements \ArrayAccess, \Iterator, \Countable
{

    /**
     * @var partNumberOUT[] $partNumberOUT
     */
    protected $partNumberOUT = null;

    
    public function __construct()
    {
    
    }

    /**
     * @return partNumberOUT[]
     */
    public function getPartNumberOUT()
    {
      return $this->partNumberOUT;
    }

    /**
     * @param partNumberOUT[] $partNumberOUT
     * @return ArrayOfPartNumberOUT
     */
    public function setPartNumberOUT(array $partNumberOUT = null)
    {
      $this->partNumberOUT = $partNumberOUT;
      return $this;
    }

    /**
     * ArrayAccess implementation
     *
     * @param mixed $offset An offset to check for
     * @return boolean true on success or false on failure
     */
    public function offsetExists($offset)
    {
      return isset($this->partNumberOUT[$offset]);
    }

    /**
     * ArrayAccess implementation
     *
     * @param mixed $offset The offset to retrieve
     * @return partNumberOUT
     */
    public function offsetGet($offset)
    {
      return $this->partNumberOUT[$offset];
    }

    /**
     * ArrayAccess implementation
     *
     * @param mixed $offset The offset to assign the value to
     * @param partNumberOUT $value The value to set
     * @return void
     */
    public function offsetSet($offset, $value)
    {
      if (!isset($offset)) {
        $this->partNumberOUT[] = $value;
      } else {
        $this->partNumberOUT[$offset] = $value;
      }
    }

    /**
     * ArrayAccess implementation
     *
     * @param mixed $offset The offset to unset
     * @return void
     */
    public function offsetUnset($offset)
    {
      unset($this->partNumberOUT[$offset]);
    }

    /**
     * Iterator implementation
     *
     * @return partNumberOUT Return the current element
     */
    public function current()
    {
      return current($this->partNumberOUT);
    }

    /**
     * Iterator implementation
     * Move forward to next element
     *
     * @return void
     */
    public function next()
    {
      next($this->partNumberOUT);
    }

    /**
     * Iterator implementation
     *
     * @return string|null Return the key of the current element or null
     */
    public function key()
    {
      return key($this->partNumberOUT);
    }

    /**
     * Iterator implementation
     *
     * @return boolean Return the validity of the current position
     */
    public function valid()
    {
      return $this->key() !== null;
    }

    /**
     * Iterator implementation
     * Rewind the Iterator to the first element
     *
     * @return void
     */
    public function rewind()
    {
      reset($this->partNumberOUT);
    }

    /**
     * Countable implementation
     *
     * @return partNumberOUT Return count of elements
     */
    public function count()
    {
      return count($this->partNumberOUT);
    }

}
