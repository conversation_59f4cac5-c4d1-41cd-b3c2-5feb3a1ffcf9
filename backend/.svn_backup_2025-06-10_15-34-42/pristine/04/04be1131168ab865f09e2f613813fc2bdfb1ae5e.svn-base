/* tablessorter */
table.tablesorter {
  thead {
   tr {
      th {
        vertical-align: middle !important;
        &.header {
          cursor: pointer;
        }
      }
      .header > * {
        display: block;
        &::after {
          content: '';
          cursor: pointer;
          background-image: url('/admin/dist/images/up-down.svg');
          background-repeat: no-repeat;
          background-position: center right;
          background-size: 12px;
          display: inline-block;
          width: 12px;
          height: 12px;
          vertical-align: middle;
          margin-left: 2px;
        }
      }
      .headerSortUp > *::after {
        background-image: url('/admin/dist/images/up.svg');
      }
      .headerSortDown > *::after {
        background-image: url('/admin/dist/images/down.svg');
      }
      .headerSortDown,
      .headerSortUp {
        background-color: darken($light-color, 5%)! important;
      }
    }
  }
}