<?php

namespace Pdf;

use \DateTime;

require_once('Pdf/PieceDeVente.php');
require_once('Pdf/OrderInstallements.php');
require_once('Export/order.inc.php');

/** \devgroup OrderPdf OrderPdf
 * \ingroup PieceDeVente
 */
class CorepOrderPdf extends PieceDeVente
{
	private $decimals = 2;
	/**
	 * __construct
	 *
	 * @param array $order
	 *
	 * @return void
	 */
	public function __construct(array $order)
	{
		global $config;

		parent::__construct();
		$this->data['ord'] = $order;

		if( isset($config['round_digits_count']) && $config['round_digits_count'] == 0 ){
			$this->decimals = 0;
		}
	}

	/**
	 * Function called before PDF generation
	 *
	 * @return void
	 */
	public function bootstrap()
	{
		if (!isset($this->data['user'])) {
			$this->loadUserInfo();
		}

		if (!isset($this->data['addresses'])) {
			$this->loadOrdersAdresses();
        }

		parent::bootstrap();

		$this->SetSubject(str_replace('#param[ref]#', $this->data['ord']['ref'], $this->requireOption('subject')));
		$this->SetTitle(str_replace('#param[ref]#', $this->data['ord']['ref'], $this->requireOption('name')));
	}

	/** Retourne le tableau des options par défaut.
	 *
	 * @return array Tableau des options par défaut
	 */
	protected function getDefaultOptions()
	{
		global $config;

		$data = $deposit = array();

		// Le client peut "surcharger" la configuration de génération des devis PDF.
		// On doit alors vérifier si c'est le cas et si oui, récupérer la nouvelle configuration.
		if( $deposit_id = $this->data['ord']['dps_id'] ){
			$deposit = ria_mysql_fetch_assoc(
				prd_deposits_get($deposit_id)
			);

			if( ($quote_config = $deposit['quote_config']) ){
				$data = (array) json_decode($quote_config);
			}
		}

		return array_merge(array(
			'subject' => $config['pdf_generation_devis_subject'],
			'name' => $config['pdf_generation_devis_name'],
			'logo' => $config['pdf_generation_devis_logo'],
			'logo_disposition' => $config['pdf_generation_devis_logo_disposition'],
			'logo_size_x' => $config['pdf_generation_devis_logo_size_x'],
			'logo_size_y' => $config['pdf_generation_devis_logo_size_y'],
			'display_dlv_address' => $config['pdf_generation_devis_display_dlv_address'],
			'prd_reduce' => $config['pdf_generation_devis_prd_reduce'],
			'display_payment' => isset($config['pdf_generation_devis_display_payment']) ? $config['pdf_generation_devis_display_payment'] : null,
			'header' => $config['pdf_generation_devis_header'],
			'header_content' => $config['pdf_generation_devis_header_content'],
			'footer' => $config['pdf_generation_devis_footer'],
			'footer_content' => $config['pdf_generation_devis_footer_content'],
			'prd_reftruncated' => $config['pdf_generation_devis_prd_reftruncated'],
			'prd_barcode' => $config['pdf_generation_devis_prd_barcode'],
			'prd_img' => $config['pdf_generation_devis_prd_img'],
			'font_size' => $config['pdf_generation_devis_font_size'],
			'ref' => '',
		), $data);
	}

	/**
	 * Contient le corps du pdf dans ce cas le tableau
	 *
	 * @return void
	 */
	public function body()
	{
		$this->table->generateTable();
	}

	/**
	 * Configure le tableau pour avoir un comportement par défaut colonne :
	 * - ref
	 * - Désignation
	 * - Quantité
	 * - Prix unitaire brut
	 * - Remise
	 * - prix unitaire net
	 * - montant total ht
	 *
	 * @return \Pdf\ProductTable
	 */
	public function defaultProductTable()
	{
		global $config;

		$this->requireData('ord');
		$this->setCurrency();

		$this->table()->withTbodyFontSize($this->requireOption('font_size'));

		$this->row_with_same_height = true;

		if( $this->getOption('prd_img') && isset($config['img_sizes']['small']) ){
			$this->table()->withRowHeight($config['img_sizes']['small']['height'] * 0.15);
			require_once('Pdf/Column.php');
			require_once('Pdf/ImagePdf.php');

			$that = &$this;

			$this->table()->addColumn(new \Pdf\Column('Image', 'L', 14, function ($p) use ($that) {
				global $config;

				if (!$p['img_id'] || !file_exists($config['img_dir'] . '/' . $config['img_sizes']['small']['dir'] . '/' . $p['img_id'] . '.' . $config['img_sizes']['small']['format'])) {
					return '';
				}

				$that->Image(
					$config['img_dir'] . '/' . $config['img_sizes']['small']['dir'] . '/' . $p['img_id'] . '.' . $config['img_sizes']['small']['format'],
					$that->getX(),
					$that->getY() + 1,
					($config['img_sizes']['small']['width'] * 0.15),
					($config['img_sizes']['small']['height'] * 0.15)
				);
				return '';
			}));
		}

		$this->table()->addColumn(new Column('Réf.', 'R', 17, function($p){
			return $p['id'] == 0 ? '' : $p['ref'] ;
		}));

		// Colonne code barre du produit
		if( $this->getOption('prd_barcode') ){
			$this->table()->addColumn(new Column('Code EAN', 'L', 25, function ($p) {
				return prd_products_get_barcode($p['id']);
			}));
		}

		// Colonne Désignation/titre/name
		$this->table()->addColumn(new Column('Désignation', 'L', 50, function($p){
			$this->SetFont('', '', 8);
			if (isset($p['title']) && !is_null($p['title'])) {
				return $p['title'];
			}else{
				return null;
			}
		}));

		// Colonne quantité
		$this->table()->addColumn(new Column('Qté', 'C', 10, function($p){
			$this->SetFont('', '', 9);
			if ($p['id'] == 0 || !is_numeric($p['id'])){
				return null;
			}else{
				$this->SetFont('', 'B');
				return floatval( $p['qte'] );
			}
		}));

		// Colonne Prix Unitaire HT du produit
		$this->table()->addColumn(new Column('P.A. HT', 'R', 15, function($p){
			$this->SetFont('', '');
			if( $p['id'] == 0 ){
				return null;
			}else{
				$prc_brut_yuto = $p['price_ht'];

				$fld_discount = fld_object_values_get(array($p['ord_id'], $p['id'], $p['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);
				if ( is_numeric($fld_discount) && $fld_discount > 0 ) {
					if( $fld_discount < 100 ){
						$prc_brut_yuto = $p['price_ht'] / ((100 - $fld_discount) / 100);
					}

					if( $prc_brut_yuto <= 0 ){
						$user_id = ord_orders_get_user( $p['ord_id'] );
						$prc_id = gu_users_get_prc( $user_id );
						$temp = prd_products_get_price( $p['id'], $user_id, $prc_id );
						if( $temp && ria_mysql_num_rows($temp) ){
							$prc_brut_yuto = ria_mysql_result( $temp, 0, 'price_ht' );
						}
					}
				}

				return ria_number_french($prc_brut_yuto, $this->decimals);
			}
		}));

		// Colonne DEEE: demande spécifique Corep
		$this->table()->addColumn(new Column('DEEE', 'R', 12, function($p){
			$deee = preg_replace('/[^0-9.]+([A-Za-z.])/', '', fld_object_values_get($p['id'], 105888, '', false,true));
			if( $deee != null && $deee >= 0 ){
				return ria_number_french($deee, $this->decimals);
			}else{
				return null;
			};
		}));

		// Colonne Eco-particiation
		if( $this->getOption('prd_ecotaxe') ){
			$this->table()->addColumn(new Column('Eco-part.', 'R', 14, function($p){
				if($p['ecotaxe'] > 0 ){
					return ria_number_french($p['ecotaxe'], $this->decimals);
				}else{
					return null;
				}
			}));
		}

		// Colonne remise (champ avancé _FLD_ORD_LINE_DISCOUNT ou remise supplémentaire sur produit sur la ligne de commande)
		if( $this->getOption('prd_reduce') ){
			$this->table()->addColumn(new Column('Remise', 'C', 15, function ($product) {
				$remise = 0;
				$fld_discount = fld_object_values_get(array($product['ord_id'], $product['id'], $product['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

				// on ajoute le signe lié à la remise (en euro ou pourcentage)
				// floatval permet de retirer les 0 superflux
				// Si une remise a été renseignée avec le champ avancé, celle ci est prioritaire à celle renseignée sur la ligne de commande
				if( is_numeric($fld_discount) && $fld_discount > 0 ){
					$remise = str_replace(',00', '', number_format($fld_discount, $this->decimals, ',', ' ')).' %';
				}elseif( $product['discount'] > 0 ){
					if( $product['discount_type'] === "0" ){ // Euros
						$remise = number_format($product['discount'], $this->decimals, ',', ' ').' €';
					}else{ // %
						$remise = str_replace(',00', '', number_format($product['discount'], $this->decimals, ',', ' ')).' %';
					}
				}

				if( $remise ==  0 ){
					return null;
				}else{
					return $remise;
				}
			} ));
			if( $this->getOption('prd_with_discount') ){
				$this->table()->addColumn(new Column('P.U. Rem.', 'R', 10, function ($p) {
					return ria_number_french($p['price_ht'], $this->decimals);
				}));
			}
		}

		// Colonne total HT de la ligne de commande
		$this->table()->addColumn(new Column('Total HT', 'R', 19, function($p){
			$this->SetFont('', 'B');
			$total_ht_displayed = $p['total_ht'];

			$fld_discount = fld_object_values_get(array($p['ord_id'], $p['id'], $p['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);

			// Calcul du montant HT de la ligne
			// Si une remise a été renseignée avec le champ avancé, le calcul de la remise est déjà fait sur le total de la ligne de commade, il n'est donc pas nécessaire de l'appliquer.
			// Sinon, on calcule la remise de la ligne de commande si renseignée
			if( (!is_numeric($fld_discount) || $fld_discount <= 0) && $p['discount'] > 0 ){
				if( $p['discount_type'] === "0" ){ // Euros
					$total_ht_displayed = $p['total_ht'] - $p['discount'];
				}else{ // %
					$total_ht_displayed = $p['total_ht'] * (1 - ($p['discount']/100));
				}
			}

			if( $p['id'] == 0 ){
				return null;
			} else {
				return ria_number_french($total_ht_displayed, $this->decimals);
			}
		}));

		if( $this->getOption('prd_tva') ){
			$this->table()->addColumn(new Column('% TVA', 'C', 8, function ($product) {
				$tva_rate = $product['tva_rate'] - 1;
				$tva_rate = $tva_rate * 100;
				$tva_rate = $tva_rate + 0;
				return $tva_rate;
			}));
		}

		return $this->table();
	}

	/**
	 * Récupère les informations de l'utilisateur
	 *
	 * @return void
	 * @deprecated
	 */
	public function loadUserInfo()
	{
		$this->requireData('ord');

		$r_user = gu_users_get($this->data['ord']['usr_id']);
		if ($r_user && ria_mysql_num_rows($r_user)) {
			$this->withUser(ria_mysql_fetch_assoc($r_user));
		}
	}

	/**
	 * Récupèrer les informations des adresses
	 *
	 * @return void
	 * @deprecated
	 */
	public function loadOrdersAdresses()
	{
		$this->data['addresses'] = ord_orders_address_load($this->requireData('ord'));
		if (false !== $this->data['addresses']){
			$this->withDlvAdress($this->data['addresses']['delivery']);
			$this->withInvAdress($this->data['addresses']['invoice']);
			return;
		}

        $this->data['addresses'] = array(
			'type' => 'postal',
			'invoice' => array(
				'type' => 'facturation',
				'type_id' => 0,
				'title_id' => 0,
				'title_name' => '',
				'id' => '',
				'firstname' => '',
				'lastname' => '',
				'society' => '',
				'address1' => '',
				'address2' => '',
				'address3' => '',
				'postal_code' => '',
				'city' => '',
				'country' => '',
				'cnt_code' => '',
				'country_state' => '',
				'phone' => '',
				'mobile' => '',
				'email' => '',
				'desc' => '',
			),
			'delivery' => array(
				'type' => 'livraison',
				'type_id' => 0,
				'title_id' => 0,
				'title_name' => '',
				'id' => '',
				'firstname' => '',
				'lastname' => '',
				'society' => '',
				'address1' => '',
				'address2' => '',
				'address3' => '',
				'postal_code' => '',
				'city' => '',
				'country' => '',
				'cnt_code' => '',
				'country_state' => '',
				'phone' => '',
				'mobile' => '',
				'email' => '',
				'desc' => '',
			)
		);
	}

	/**
	 * Génèrer la ligne d'inforamtion utilisateur
	 *
	 * @return void
	 */
	protected function userInfoRow()
	{
		$this->requireData('ord');

		$this->SetY($this->getY() + 6);
		$this->resetX();
		$this->SetFont($this->font_family, 'B', 9);
		//header
		if( $this->data['ord']['state_id'] == 28 ){
			$this->Cell(30, 6, utf8_decode('DEVIS N°'), 1, 0, 'C');
		}else{
			$this->Cell(30, 6, utf8_decode('COMMANDE N°'), 1, 0, 'C');
		}
		$this->Cell(30, 6, utf8_decode('DATE'), 1, 0, 'C');
		if( $this->data['ord']['state_id'] == 28 ){
			$this->Cell(47, 6, utf8_decode('REFERENCE DEVIS'), 1, 0, 'C');
		}else{
			$this->Cell(47, 6, utf8_decode('REFERENCE COMMANDE'), 1, 0, 'C');
		}
		$this->Cell(20, 6, utf8_decode('PAGE N°'), 1, trim($this->data['user']['ref']) == '', 'C');

		if( trim($this->data['user']['ref']) != '' ){
			$this->Cell(40, 6, utf8_decode('CLIENT'), 1, 1, 'C');
		}

		//content
		$this->SetFont($this->font_family, '', 9);
		$date = new DateTime(isset($this->data['ord']['date_en']) ? $this->data['ord']['date_en'] : $this->data['ord']['date']);
		$this->Cell(30, 6, utf8_decode($this->data['ord']['piece'] != "" ? $this->data['ord']['piece'] : $this->data['ord']['id']), 1, 0, 'C');
		$this->Cell(30, 6, utf8_decode($date->format('d/m/y à H:i')), 1, 0, 'C');
		$this->Cell(47, 6, utf8_decode($this->data['ord']['ref']), 1, 0, 'C');
		$this->Cell(20, 6, utf8_decode($this->PageNo()), 1, trim($this->data['user']['ref']) == '', 'C');

		if( trim($this->data['user']['ref']) != '' ){
			$this->Cell(40, 6, utf8_decode($this->data['user']['ref']), 1, 1, 'C');
		}

		if ($this->shouldShowTaxcode() && !is_null($this->getData('user')) && trim($this->data['user']['taxcode'])) {
			$this->Cell(50, 6, utf8_decode('N° intracommunautaire : ' . $this->data['user']['taxcode']), 0, 1, 'L');
		} else {
			$this->Cell(0, 6, '', 0, 1, 'L');
		}
	}

	/**
	 * Génèrer le total de page
	 *
	 * @return void
	 */
	protected function PageCount()
	{
		$this->SetXY(20, 30);
		$this->Cell(23, 10, utf8_decode('Page ' . $this->PageNo()), 0, 0, 'C');
	}

	/**
	 * Génère la page des totaux de la commande
	 *
	 * @return void
	 */
	protected function generateTotalPage()
	{
		$this->requireData('ord');
		$remise = $this->getOption('prd_reduce');

		$y = $this->getY();
		$eco = $this->taxes()->ecotaxe();
		$end_y = $y;
		// $this->SetFont($this->font(), 'B', 8);
		// //header
		// $this->Cell(15, 7, utf8_decode('CODE'), 1, 0, 'L');
		// $this->Cell(22, 7, utf8_decode('BASE'), 1, 0, 'C');
		// $this->Cell(13, 7, utf8_decode('TAUX'), 1, 0, 'C');
		// $this->Cell(27, 7, utf8_decode('MONTANT'), 1, 1, 'R');
		$end_y += 7;
		// //row
		// $this->SetFont($this->font(), '', 9);
		// $first = 'Tva';
		// $total = 0;
		// foreach ($this->taxes()->tva() as $rate => $tva) {
		// 	$this->Cell(15, 5, utf8_decode($first), 1, 0, 'L');
		// 	$this->Cell(22, 5, $this->price($tva['base'], $this->decimals).' '.$this->currency, 1, 0, 'R');
		// 	$this->Cell(13, 5, (($rate - 1) * 100) . ' %', 1, 0, 'C');
		// 	$this->Cell(27, 5, $this->price($tva['amount'], $this->decimals).' '.$this->currency, 1, 1, 'R');
		// 	$first = '';
		// 	$total += $tva['amount'];
		$end_y += 5;
		// }
		// //row
		// $rate = 0;
		// if ($eco['base'] > 0) {
		// 	$rate = $eco['amount'] * 100 / $eco['base'];
		// }
		// $this->Cell(15, 5, utf8_decode('Eco'), 1, 0, 'L');
		// $this->Cell(22, 5, $this->price($eco['base'], $this->decimals).' '.$this->currency, 1, 0, 'R');
		// $this->Cell(13, 5, $rate. ' %', 1, 0, 'C');
		// $this->Cell(27, 5, $this->price($eco['amount'], $this->decimals).' '.$this->currency, 1, 1, 'R');
		// $total += $eco['amount'];
		$end_y += 5;
		// //row
		// $this->Cell(15, 7, utf8_decode('TOTAL :'), 1, 0, 'L');
		// $this->Cell(35, 7, utf8_decode('Tva+Eco-participation'), 1, 0, 'R');
		// $this->Cell(27, 7, $this->price($total, $this->decimals).' '.$this->currency, 1, 1, 'R');
		$end_y += 7;

		$this->setXY(90, $y);
		$this->SetFont($this->font(), 'B', 8);
		$this->Cell(37, 7, utf8_decode('Total Remise'), 1, 0, 'C');
		// $this->SetFont($this->font(), 'B', 9);
		$this->Cell(37, 7, utf8_decode('Total Eco-participation'), 1, 0, 'C');
		$this->Cell(37, 7, utf8_decode('Total HT'), 1, 0, 'C');

		$this->setXY(90, $y + 7);
		$this->SetFont($this->font(), '', 9);

		$totalDiscount = 0;
		foreach( $this->data['ord_products'] as $one_prd ){
			$fld_discount = fld_object_values_get(array($one_prd['ord_id'], $one_prd['id'], $one_prd['line']), _FLD_ORD_LINE_DISCOUNT, '', false, true);
			if( is_numeric($fld_discount) && $fld_discount > 0 ){
				$prc_brut_yuto = 0;
				if( $fld_discount < 100 ){
					$prc_brut_yuto = $one_prd['price_ht'] / ((100 - $fld_discount) / 100);
				}

				if( $prc_brut_yuto <= 0 ){
					$user_id = ord_orders_get_user( $one_prd['ord_id'] );
					$prc_id = gu_users_get_prc( $user_id );
					$temp = prd_products_get_price( $one_prd['id'], $user_id, $prc_id );
					if( $temp && ria_mysql_num_rows($temp) ){
						$prc_brut_yuto = ria_mysql_result( $temp, 0, 'price_ht' );
					}
				}
				$totalDiscount += ($prc_brut_yuto - $one_prd['price_ht']) * $one_prd['qte'];
			}
		}

		$this->Cell(37, 7, $this->price($totalDiscount, $this->decimals).' '.$this->currency, 1, 0, 'R');

		$totalDEEE = 0;
		for($e=0;$e<count($this->data['ord_products']);$e++){
			if( fld_object_values_get($this->data['ord_products'][$e]['id'], 105888) != null ){
				$tempDEEE = preg_replace('/[^0-9.]+([A-Za-z.])/', '', fld_object_values_get($this->data['ord_products'][$e]['id'], 105888));
				$totalDEEE += $tempDEEE*$this->data['ord_products'][$e]['qte'];
			}
		};
		$this->Cell(37, 7, $this->price($totalDEEE, $this->decimals).' '.$this->currency, 1, 0, 'R');
		$this->Cell(37, 7, $this->price($this->data['ord']['total_ht'] + $eco['base'], $this->decimals).' '.$this->currency, 1, 0, 'R');

		$this->setXY(90, $y + 14);


		if( $this->getOption('display_payment') ){
			$name = $this->getData('pay_name');
			$ordInstallements = new OrderInstallements($this, $this->data['ord'], $this->getData('installments'), $name);

			if (is_null($name)) {
				$name = $ordInstallements->paymentName();
				if (trim($name) == '') {
					$params = array(0, $this->order['pay_id']);
					if ($this->order['pay_id'] <= 0) {
						$params = array($this->order['usr_id']);
					}
					$r_pay = call_user_func_array('gu_users_payment_types_get', $params);
                    $this->data['pay_name'] = '';
					if ($r_pay && ria_mysql_num_rows($r_pay)) {
						$pay = ria_mysql_fetch_assoc($r_pay);
                        $this->data['pay_name'] = gu_users_payment_types_view($pay);
					}
				}
			}

			if (trim($this->data['pay_name']) != "") {
				$this->SetFont($this->font(), 'B', 7);
				$this->Cell(40, 7, utf8_decode('Conditions de règlement :'), 0, 2, 'C');
				$this->SetFont($this->font(), '', 6);

				$this->Cell(40, 7, utf8_decode($this->data['pay_name']), 0, 1, 'C');

				$ordInstallements->generate($this->getX() + 115, $this->getY() - 5);
			}
		}

		$this->SetY($end_y);
	}

	public function blocHeader()
	{
		global $config;

		$height = 5;
		$this->Cell(50, 4, '', 0, 2);
		if ($this->getOption('header')){
			$this->SetFont($this->font(), '', 9);
			$line_height = 4;
			$w=$this->w-$this->rMargin-$this->x;
			$height = ceil(($this->GetStringWidth(iconv('utf8', 'windows-1252', $this->requireOption('header_content'))) / $w)) * $line_height;

			$this->MultiCell(0, 4, iconv('utf8', 'windows-1252', $this->requireOption('header_content')), 0, 2, '');
		}

		$this->SetY($this->GetY() + $height);
		$this->SetFont($this->font(), 'B', 20);
		if( $this->data['ord']['state_id'] == 28 ){
			$this->MultiCell(0, 4, utf8_decode('Devis'), 0, 2, '');
		}else{
			$this->MultiCell(0, 4, utf8_decode('Commande'), 0, 2, '');
		}
	}

	public function blocFooter()
	{
		$y = $this->getY() + 4;
		$this->SetY($y);

		if ($this->getOption('footer')){
			$this->SetFont($this->font(), '', 9);
			$this->MultiCell(80, 4, iconv('utf8', 'windows-1252', $this->requireOption('footer_content')), 0, 2, '');
			$this->Cell(0, 2, '', 0, 2);
		}

		$this->SetFont($this->font(), 'B', 9);
		$this->Multicell(0, 4, utf8_decode('Pour le client,'."\n".'Signature précédée de la mention'."\n".'"Lu et Approuvé, Bon pour Accord"'), 0, 2, '');

		// Récupère la signature et ses dimensions
		$file_sign = export_order_create_sign( $this->data['ord']['id'] );

		if( file_exists($file_sign) ){
			$img_sign_sizes = getimagesize( $file_sign );

			if( isset($img_sign_sizes[0], $img_sign_sizes[1]) ){
				if( $img_sign_sizes[0] == $img_sign_sizes[1] ){
					// La signature est carrée
					$this->Image( $file_sign, $this->GetX(), $this->GetY(), 20, 20 );
				}elseif( $img_sign_sizes[1] > $img_sign_sizes[0] ){
					// La hauteur est supérieur à la largueur
					$this->Image( $file_sign, $this->GetX(), $this->GetY(), 10, 20 );
				}else{
					// La largueur est supérieure à la hauteur
					$this->Image( $file_sign, $this->GetX(), $this->GetY(), 20, 10 );
				}
			}

			// La signature est tout de suite supprimée après utilisation
			unlink($file_sign);
		}
	}
}