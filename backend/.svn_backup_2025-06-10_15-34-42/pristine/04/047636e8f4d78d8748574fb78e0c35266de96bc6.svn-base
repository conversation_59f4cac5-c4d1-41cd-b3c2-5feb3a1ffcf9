<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagThing;
use SchemaDotOrg\Tags\TagContactPoint;
/**
 * \ingroup SchemaTag
 * @{
 */
/**
 * \brief Cette classe correspond au tag Organistion utile sur la page d'acceuil du site
 */
class TagOrganisation extends TagThing {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	protected $type = "Organization";

	/**
	 * Constructeur permet d'initialisé le nom et l'url de l'organisation
	 *
	 * @param string $name
	 * @param string $url
	 */
	public function __construct($name='', $url=''){
		global $config;

		if (!trim($name)) {
			$name = $config['site_name'];
		}
		if (!trim($url)) {
			$url = $config['site_url'];
		}
		$this->setName($name)
			->setUrl($url);

	}

	/**
	 * Cette fonction permet d'initialisé le champ logo de l'organisation
	 *
	 * @param string $logo l'url de l'organisation
	 * @return self retourne l'instance
	 */
	public function setLogo($logo){
		return $this->addField('logo', $logo);
	}

	/**
	 * Cette fonction permet d'initialisé le champ contactPoint de l'organisation
	 *
	 * @param TagContactPoint $contactPoint Objec
	 * @return self retourne l'instance
	 */
	public function addContactPoint(TagContactPoint $contactPoint){

		$this->fields['contactPoint'][] = $contactPoint->getFields();

		return $this;
	}
}
///@}