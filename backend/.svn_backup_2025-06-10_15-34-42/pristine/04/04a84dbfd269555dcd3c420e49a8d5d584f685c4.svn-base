<?php
	require_once('users.inc.php');

	use Google\Cloud\Datastore\DatastoreClient;

	/** \brief Cette classe représente l'entité administrator du datastore
	 *	\ingroup auth
	 */
	class Administrator {
		private $civility        	= 1; 					///< Civilité
		private $firstname       	= ''; 				///< Prénom
		private $lastname        	= ''; 				///< Nnom
		private $phone           	= ''; 				///< Numéro de téléphone
		private $mobile          	= ''; 				///< Numéro de téléphone mobile
		private $work            	= ''; 				///< Numéro de téléphone en journée
		private $fax             	= ''; 				///< Numéro de fax
		private $birthday        	= ''; 				///< Date anniversaire
		private $lang            	= ''; 				///< Lang par défaut

		private $id             	= ''; 				///< Identifiant de l'administrateur dans le Registre
		private $email          	= ''; 				///< Son adresse e-mail
		private $access         	= array(); 		///< Tableau contenant les identifiants des tenants accessibles par cet administrateur
		private $showWelcome    	= false; 			///< Si oui ou non l'administrateur a déjà vu la popup de bienvenue
		private $selected       	= 0; 					///< Identifiant du tenant en cours de consultation par l'administrateur
		private $password			= ''; 				///< Mot de passe pour la connexion à l'administration (utiliser seulement lors d'une création / maj
		private $exclusion			= array();		///< Tableau contenant les identifiants des tenants inaccessibles par cet administrateur

		private $toReloadUser   	= false; 			///< Permet de savoir si le compte client doit être rechargé

		private $datePassword 		= ''; 				///< Date de dernière demande de ré-initialisation de mot de passe
		private $dateDeleted		= ''; 				///< Date de suppression du compte administrateur

		private $datastore = null;

		/**	Constructeur
		 *
		 */
		public function __construct()
		{
			$this->datastore = new DatastoreClient();
		}

		/** Cette fonction permet de savoir si une adresse mail existe bien dans le registre.
		 * 	@param string $email Obligatoire, adresse mail
		 * 	@return bool True si l'adresse mail a été trouvé dans le registre, False dans le cas contraire
		 */
		public static function inRegister( $email ){
			if( trim($email) == '' ){
				return false;
			}

			$datastore = new DatastoreClient();

			$query = $datastore->query()
				->kind('administrator')
				->filter('adm_email', '=', $email)
				->filter('adm_date_deleted', '=', '');

			$result = $datastore->runQuery($query)->current();
			return $result !== null;
		}

		/** Cette fonction permet de récupérer la liste complète des adminsitrateurs
		 *	@param $include_deleted Optionnel, par défaut seuls les administrateur non supprimés seront retourné, mettre true pour tous les avoir
		 */
		public static function getAll( $include_deleted=false ){
			$datastore = new DatastoreClient();

			$query = $datastore->query()
				->kind('administrator');

			if( !$include_deleted ){
				$query->filter('adm_date_deleted', '=', '');
			}

			$result = $datastore->runQuery($query);

			$ar_admin = [];
			foreach( $result as $one_admin ){
				// print_r($one_admin);
				$admin = new Administrator();
				print_r($admin->initFromEntity( $one_admin ));
				$ar_admin[] = $admin;
			}

			return $ar_admin;
		}

		/** Cette fonction permet de vérifier qu'une connexion à l'administration est élligible
		 *  @param string $email Obligatoire, adresse mail du compte administrateur
		 *  @param string $password Obligatoire, mot de passe (mettre nocontrol pour ne pas contrôler le mot de passe)
		 *  @return bool False si les identifiant de connexion sont faux, sinon toutes les informations du registre sur cet admnistrateur :
		 * 		- firstname : prénom
		 * 		- lastname : nom
		 */
		public function connect($email, $password) {
			if( trim($email) == '' ){
				return false;
			}

			if( trim($password) == '' ){
				return false;
			}

			// Contrôle que l'on arrive bien à récupérer un compte administrateur
			$result = $this->getUserFromDatastore($email);
			if( $result === null ){
				return false;
			}

			// Vérification pour savoir s'il s'agit d'un mot de passe md5 ou hash
			if( $password !== 'nocontrol' ){
				if( substr($result->getProperty('adm_password'), 0, 1) == '$' ){
					// Contrôle du hash du mot de passe
					if( !password_verify($password, $result->getProperty('adm_password')) ){
						return false;
					}
				}else{
					// Contrôle ancienne méthode de mot de passe (md5)
					if( $result->getProperty('adm_password') != md5(strtolower2($password)) ){
						return false;
					}
				}
			}

			// Préparation des informations sur le compte administrateur
			$this->id               = $result->key()->pathEndIdentifier();
			$this->email            = $result->getProperty('adm_email');

			// Information sur la personne
			$this->civility         = $result->getProperty('adm_civility');
			$this->firstname        = $result->getProperty('adm_firstname');
			$this->lastname         = $result->getProperty('adm_lastname');
			$this->phone            = $result->getProperty('adm_phone');
			$this->mobile           = $result->getProperty('adm_mobile');
			$this->work             = $result->getProperty('adm_work');
			$this->fax              = $result->getProperty('adm_fax');
			$this->birthday         = $result->getProperty('adm_birthday');
			$this->lang             = $result->getProperty('adm_lang');

			$this->showWelcome      = $result->getProperty('adm_show_welcome');
			$this->access			= $result->getProperty('adm_tenant_access');
			$this->exclusion		= $result->getProperty('adm_tenant_exclu');

			// Si l'administrateur n'a qu'un seul accès, on le sélectionne automatiquement
			if( is_array($this->access) && count($this->access) == 1 ){
				$this->selected = $this->access[0];
				$this->toReloadUser = true;
			}

			$_SESSION['admin_account_id'] = $this->id;

			return true;
		}

		/** Cette fonction permet de charger les informations d'un compte administrateur à partir de son adresse mail.
		 * 	@param string $email Obligatoire, adresse mail du compte administrateur
		 * 	@return bool False si le compte administrateur n'existe pas, sinon True
		 */
		public function load($email){
			if( trim($email) == '' ){
				return false;
			}

			// Contrôle que l'on arrive bien à récupérer un compte administrateur
			$result = $this->getUserFromDatastore($email);
			if( $result === null ){
				return false;
			}

			$this->initFromEntity($result);

			return true;
		}

		/** Cette fonction permet de charger les informations d'un compte administrateur à partir de son identifiant.
		 * 	@param string $id Identifiant du compte administrateur
		 * 	@return bool False si le compte administrateur n'existe pas, sinon True
		 */
		public function loadFromId($id){
			if( trim($id) == '' ){
				return false;
			}

			$result = $this->getUserByIdFromDatastore($id);
			if( $result === null ){
				return false;
			}

			$this->initFromEntity($result);

			return true;
		}

		/** Cette fonction permet permet de déconnecter un compte administrateur.
		 */
		public function disconnect(){
			unset($_SESSION['admin_account_id']);
			unset($_SESSION['admin_account_tnt']);
		}

    /** Cette fonction permet de vérifier qu'un administrateur connecté a bien accès à un tenant précis.
		 *  @param int $tnt_id Obligatoire, identifiant du tenant
		 *  @return bool True si c'est bien le cas, False dans le cas contraire
		 */
		public function hasAccess($tnt_id) {
			if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
				return false;
			}

			if( !isset($this->id) || trim($this->id) == '' ){
				return false;
			}

			$has_access = false;
			// Les comptes admin avec "all" dans "adm_tenant_access" ont accès à l'ensemble des clients
			if( $this->access == 'all' ){
				$has_access = true;
			}else{
				$has_access = array_search( $tnt_id, $this->access ) !== false;
			}

			// Si l'on a accès au tenant, on regarde si celui-ci ne fait pas partie de la liste de ceux exclus
			if( $has_access ){
				$has_access = array_search( $tnt_id, $this->exclusion ) === false;
			}

			return $has_access;
		}

		/** Cette fonction permet de retourner le premier identifiant de locataire auquel à accès le compte administrateur.
		 * 	@return int Le premier identifiant de locataire auquel le compte à accès (0 si aucun accès)
		 */
		public function getOneAccess(){
			if( $this->access != 'all' && (!is_array($this->access) || !count($this->access)) ){
				return 0;
			}

			$one = 0;
			if( $this->access == 'all' ){
				// Si le compte a accès à tous les locataires, on récupère le premier qui ne serait pas égal à 0
				$query = $this->datastore->query()
					->kind('db_tenants');


				$result = $this->datastore->runQuery($query);
				if( $result !== null ){
					foreach( $result as $res ){
						if( $res->getProperty('tenant_id') <= 0 ){
							continue;
						}

						$one = $res->getProperty('tenant_id');
						break;
					}
				}
			}else{
				$one = $this->access[0];
			}

			return $one;
		}

		/** Cette fonction permet de contrôler qu'un administrateur est bien connecté.
		 *  @return bool True si l'administrateur est connecté, False dans le cas contraire
		 */
		public function isConnected(){
			return isset($this->id) && $this->exists();
		}

		/** Cette fonction permet de récupérer l'identifiant de l'adminsitrateur dans le registre.
		 * 	@return string L'identifiant de l'administrateur
		 */
		public function getID(){
			return $this->id;
		}

		/** Cette fonction permet de récupérer l'adresse mail de l'administrateur.
		 *  @return string L'adresse mail de l'administrateur
		 */
		public function getEmail(){
			return $this->email;
		}

		/** Cette fonction permet de récupérer le mot de passe crypté de l'administrateur.
		 *  @return string L'adresse mail de l'administrateur
		 */
		public function getPassword(){
			return $this->password;
		}

		/** Cette fonction permet de récupérer les tenants accessibles par l'administrateur.
		 *  @return array La liste des identifiants de tenants accessibles
		 */
		public function getAccess(){
			return $this->access;
		}

		/** Cette fonction permet de récupérer l'information permettant de savoir si l'administrateur a déjà vu la popup de bienvenue.
		 *  @return bool True si l'administrateur a bien vu la popup, False dans le cas contraire
		 */
		public function getWelcome(){
			return $this->showWelcome;
		}

		/** Cette fonction permet de récupérer la civilité de l'administrateur.
		 *  @return string La civilité de l'administrateur
		 */
		function getCivility(){
			return $this->civility;
		}

		/** Cette fonction permet de récupérer le prénom de l'administrateur.
		 *  @return string Le prénom de l'administrateur
		 */
		function getFirstname(){
			return $this->firstname;
		}

		/** Cette fonction permet de récupérer le nom de famille de l'administrateur.
		 *  @return string Le nom de famille de l'administrateur
		 */
		function getLastname(){
			return $this->lastname;
		}

		/** Cette fonction permet de récupérer le numéro de téléphone de l'administrateur.
		 *  @return string Le numéro de téléphone de l'administrateur
		 */
		function getPhone(){
			return $this->phone;
		}

		/** Cette fonction permet de récupérer le numéro de téléphone portable de l'administrateur.
		 *  @return string Le numéro de téléphone portable de l'administrateur
		 */
		function getMobile(){
			return $this->mobile;
		}

		/** Cette fonction permet de récupérer le numéro de téléphone en journée de l'administrateur.
		 *  @return string Le numéro de téléphone en journée de l'administrateur
		 */
		function getWork(){
			return $this->work;
		}

		/** Cette fonction permet de récupérer le numéro de fax de l'administrateur.
		 *  @return string Le numéro de fax de l'administrateur
		 */
		function getFax(){
			return $this->fax;
		}

		/** Cette fonction permet de récupérer la langue choisie pour l'interface d'administration.
		 *  @return string La langue choisie pour l'interface d'administration
		 */
		function getLang(){
			return $this->lang;
		}

		/** Cette fonction permet de retourner la date anniversaire au format d'une date yyyy-mm-dd.
		 *  @return string La date anniversaire au format yyyy-mm-dd
		 */
		public function getBirthday(){
			if( trim($this->birthday) == '' ){
				return '';
			}

			$date = new DateTime($this->birthday);
			return $date->format('Y-m-d');
		}

		/** Cette fonction permet de mettre à jour la civilité de l'administrateur.
		 *  @param $civility Obligatoire, la civilité de l'administrateur
		 *  @return object L'objet courant
		 */
		function setCivility($civility){
			$this->civility = $civility;
			return $this;
		}

		/** Cette fonction permet de mettre à jour le prénom de l'administrateur.
		 *  @param string $firstname Obligatoire, le prénom de l'administrateur
		 *  @return object L'objet courant
		 */
		function setFirstname($firstname){
			$this->firstname = $firstname;
			return $this;
		}

		/** Cette fonction permet de mettre à jour le nom de famille de l'administrateur.
		 *  @param string $lastname Obligatoire, le nom de famille de l'administrateur
		 *  @return object L'objet courant
		 */
		function setLastname($lastname){
			$this->lastname = $lastname;
			return $this;
		}

		/** Cette fonction permet de mettre à jour le numéro de téléphone de l'administrateur.
		 *  @param string $phone Obligatoire, le numéro de téléphone de l'administrateur
		 *  @return object L'objet courant
		 */
		function setPhone($phone){
			$this->phone = $phone;
			return $this;
		}

		/** Cette fonction permet de mettre à jour le numéro de téléphone portable de l'administrateur.
		 *  @param string $mobile Obligatoire, le numéro de téléphone portable de l'administrateur
		 *  @return object L'objet courant
		 */
		function setMobile($mobile){
			$this->mobile = $mobile;
			return $this;
		}

		/** Cette fonction permet de mettre à jour le numéro de téléphone en journée de l'administrateur.
		 *  @param string $work Obligatoire, le numéro de téléphone en journée de l'administrateur
		 *  @return object L'objet courant
		 */
		function setWork($work){
			$this->work = $work;
			return $this;
		}

		/** Cette fonction permet de mettre à jour le numéro de fax de l'administrateur.
		 *  @param string $fax Obligatoire, le numéro de fax de l'administrateur
		 *  @return object L'objet courant
		 */
		function setFax($fax){
			$this->fax = $fax;
			return $this;
		}

		/** Cette fonction permet de mettre à jour l'adresse e-mail de l'administrateur.
		 *  @param string $email Obligatoire, l'adresse e-mail de l'administrateur
		 *  @return object L'objet courant
		 */
		function setEmail($email){
			$this->email = $email;
			return $this;
		}

		/** Cette fonction permet de mettre à jour la langue choisie pour l'interface d'administration.
		 *  @param string $lang Obligatoire, la langue choisie pour l'interface d'administration
		 *  @return object L'objet courant
		 */
		function setLang($lang){
			$this->lang = $lang;
			return $this;
		}

		/** Cette fonction permet de mettre à jour l'information permettant de savoir si l'administreur a déjà vu la popup de bienvenu.
		 *  @param $show Obligatoire, si oui ou non la popup a été vue
		 *  @return object L'objet courant après mise à jour
		 */
		public function setWelcome($show){
			$this->showWelcome = $show;
			return $this;
		}

		/** Cette fonction permet de mettre à jour la date anniversaire de l'administrateur
		 *  @param $birthday Obligatoire, la nouvelle date anniversaire
		 *  @return object L'objet courant après mise à jour
		 */
		public function setBirthday($birthday){
			if( trim($birthday) != '' && !isdate($birthday) ){
				return false;
			}

			$this->birthday = $birthday;
			return $this;
		}

		/** Cette fonction permet de mettre à jour le mot de passe de l'administrateur. Celui-ci sera crypté.
		 *  @param string $password Obligatoire, le nouveau mot de passe
		 *  @param bool $is_crypted Optionnel, si le mot de passe donnée est déjà crypté ou non
		 *  @return object L'objet courant après mise à jour
		 */
		public function setPassword($password, $is_crypted=false){
			if( $is_crypted ){
				$this->password = $password;
			}else{
				$this->password = password_hash($password, PASSWORD_DEFAULT);
			}

			return $this;
		}

		/** Cette fonction permet d'ajout un tenant à la liste de ceux accessible à un administrateur
		 * 	@param int $tnt_id Obligatoire, identifiant  d'un locataire
		 * 	@return object L'objet courant après mise à jour
		 */
		public function addTenantAccess( $tnt_id ){
			if( !is_numeric($tnt_id) || $tnt_id <= 0 ){
				return false;
			}

			if( $this->access !== 'all' ){
				$this->access[] = strval( $tnt_id );
			}

			return $this;
		}

		/** Cette fonction permet de récupérer l'identifiant du tenant en cours de consultat par l'administrateur.
		 *  @return int L'identifiant du tenant en cours de consultation
		 */
		public function getTenantSelected(){
			return $this->selected;
		}

		/** Cette fonction permet de récupérer la date de suppression d'un administrateur.
		 * 	@return string La date de suppression (vide si non supprimé)
		 */
		public function getDateDeleted( $format='fr' ){
			$date = false;

			if( is_a($this->dateDeleted, 'DateTimeImmutable') ){
				$date = new DateTime();
				$date->setTimestamp( $this->dateDeleted->getTimestamp());
			}elseif( trim($this->dateDeleted) != '' ){
				$date = new DateTime( $this->dateDeleted );
			}

			return $date ? dateTimeIntlFormat( $date, true ) : '';
		}

		/** Cette fonction permet de renseigner le tenant en cours de consultation par l'administrateur.
		 *  @param int $tnt_selected Optionnel, identifiant du tenant sélectionné
		 *  @return object L'objet courant après mise à jour
		 */
		public function setTenantSelected($tnt_selected=0){
			$this->selected = $tnt_selected;

			// Demande le rechargement du compte client en session
			$this->toReloadUser = true;
			$_SESSION['admin_account_tnt'] = $tnt_selected;

			return $this;
		}

		/** Cette fonction permet de mettre à jour l'information d'accès au tenant
		 * 	@param string|array $access Obligatoire, accès au(x) tenant(s), valeur acceptée : "all" = tous | tableau d'identifiant (sous format string)
		 * 	@return object L'objet courant après mise à jour
		 */
		public function setAccess( $access ){
			$this->access = $access;
			return $this;
		}

		/** Cette fonction permet de recharger le compte client en session en fonction du tenant en cours de consultation.
		 *  @return bool True en cas de succès, false dans le cas contraire
		 */
		public function reloadUser(){
			global $config;

			if( !$this->selected ){
				return false;
			}

			// Aucune action s'il n'y a pas besoin de recharger le compte client en session
			// Ce rechargement n'a lieu que lorsque le tenant en cours de consultat est mis à jour
			if( !$this->toReloadUser ){
				return true;
			}

			// Récupère les informations sur le compte client lié à cet administrateur
			$r_user = gu_users_get(0, $this->email, '', $config['admin_access_profiles']);
			if( $r_user && ria_mysql_num_rows($r_user) ){
				$user = ria_mysql_fetch_assoc($r_user);
				// Connexion du compte client de façon standard dans RiaShop
				gu_users_connect($user);

				// Gestion de la langue
				$_SESSION['lang'] = $this->lang;

				// Demande à ne plus recharger le compte client en session
				$this->toReloadUser = false;
			}else{
				throw new RuntimeException("Requested user doesn't exists");
			}

			return true;
		}

		/** Cette fonction permet d'enregistrer le compte administrateur dans le registre et de mettre à jour l'objet dans la session.
		 *  @param bool $reload Optionnel, par défaut le compte administrateur en session est mis à jour, mettre False pour ne pas y toucher
		 *  @return int -1 si on essaye de créer ou de mettre à jour un compte administrateur avec une adresse mail déjà utilisée par un autre
		 *  @return bool True si l'enregistrement s'est correctement déroulé, False dans le cas contraire
		 */
		public function save( $reload=true ){
			// Vérification pour bloquer le doublon d'adresse mail
			$query = $this->datastore->query()
				->kind('administrator')
				->filter('adm_email', '=', $this->email)
				->filter('adm_date_deleted', '=', '');

			$result = $this->datastore->runQuery($query)->current();

			// Un compte administrateur avec la même adresse mail a été trouvé.
			// On va vérifier que son identifiant est bien celui qu'on essaye de mettre à jour
			if( $result !== null ){
				$key = $result->key()->pathEndIdentifier();

				// Le code de retour sera "-1" si on essaye de créer un nouveau compte ou bien de mettre à jour un compte avec une adresse mail qui est déjà utilisée
				if( trim($this->id) == '' || $this->id != $key ){
					return -1;
				}
			}
			
			// Mise à jour ou Ajout dans le registre en fonction de si l'identifiant est renseigné
			if( $this->id ){
				// Le compte administrateur est déjà présent dans le registre
				$key = $this->datastore->key('administrator', $this->id);
				$entity = $this->datastore->lookup($key);

				$entity['adm_email'] 		 = $this->email;
				$entity['adm_civility']          = $this->civility;
				$entity['adm_firstname']         = $this->firstname;
				$entity['adm_lastname']          = $this->lastname;
				$entity['adm_phone']             = $this->phone;
				$entity['adm_mobile']            = $this->mobile;
				$entity['adm_work']              = $this->work;
				$entity['adm_fax']               = $this->fax;
				$entity['adm_birthday']          = $this->birthday;
				$entity['adm_lang']              = $this->lang;
				$entity['adm_show_welcome']      = $this->showWelcome;
				$entity['adm_tenant_access']     = $this->access;
				$entity['adm_date_deleted'] 	   = $this->dateDeleted;

				if( isset($this->password) && trim($this->password) != '' ){
					$entity['adm_password']	 = $this->password;
				}

				$result = $this->datastore->transaction()
						->update($entity)
						->commit();
			}else{
				global $config;

				// Création d'un mot de passe par défaut si aucun n'est renseigné
				// On utilise la fonction setPassword afin que celui-ci soit toujours crypté
				if( trim($this->password) == '' ){
					$this->setPassword(gu_password_create());
				}

				// Ajout automatique d'une date de création
				$date_created = new DateTime();

				if( $this->access !== 'all' ){
					// Par défaut le compte admin créé ne peut accéder qu'au tenant depuis lequel il est créé
					$this->access = [ strval($config['tnt_id']) ];
				}

				// Création d'un nouveau compte administrateur
				$n_admin = array(
					'adm_email' 		=> $this->email,
					'adm_civility' 		=> $this->civility,
					'adm_firstname' 	=> $this->firstname,
					'adm_lastname' 		=> $this->lastname,
					'adm_phone' 		=> $this->phone,
					'adm_mobile' 		=> $this->mobile,
					'adm_work' 		=> $this->work,
					'adm_fax' 		=> $this->fax,
					'adm_birthday' 		=> $this->birthday,
					'adm_lang' 		=> 'fr_FR',
					'adm_show_welcome' 	=> true,
					'adm_tenant_access' 	=> $this->access,
					'adm_password'		=> $this->password,
					'adm_date_deleted'	=> '',
					'adm_date_created' => $date_created->format('Y-m-d H:i:s')
				);

				// Création d'une entité datastore + envoi des informations, cela va créer un nouveau document dans le registre
				$entity = $this->datastore->entity('administrator', $n_admin);
				$result = $this->datastore->insert($entity);

				return trim($result) != '';
			}

			// Mise à jour de l'objet Administrator en session
			if( $reload ){
				$this->load($this->email);
			}

			return true;
		}

		/** Cette foncton permet de supprimer un compte administrateur.
		 * 	@return bool True si la suppression s'est correctement déroulé, False dans le cas contraire
		 */
		public function delete(){
			$date = new DateTime();
			$this->dateDeleted = $date->format('Y-m-d H:i:s');
			return $this;
		}

		/** Cette fonction permet d'envoyer une demande de ré-initialisation de mot de passe
		 * 	@param string $admin_email Obligatoire, adresse mail du compte administrateur
		 * 	@return bool True si l'envoi s'est correctement déroulé, False dans le cas contraire
		 */
		public static function sendLostPassword($admin_email){
			if( trim($admin_email) == '' ){
				return false;
			}

			if( getenv('email_env_context') == 'production' ){
				$GLOBALS['config']['email_env_context'] = 'production';
			}

			$datastore = new DatastoreClient();

			// Récupère le mot de passe actuel du compte administrateur avec cette adresse mail
			$query = $datastore->query()
		  			->kind('administrator')
					->filter('adm_email', '=', $admin_email)
					->filter('adm_date_deleted', '=', '');

			// Contrôle que l'on arrive bien à récupérer un compte administrateur
			$result = $datastore->runQuery($query)->current();
			if( $result === null ){
				return false;
			}

			// Enregistre la date de la demande de ré-initialisation
			$date_reinit = new Datetime();

			// Création du token de réinitilisation du mot de passe
			$token_reinit_password = md5($admin_email.$result->getProperty('adm_password').$date_reinit->format('Y-m-d H:i:s'));

			// Enregistre la date dans le registre pour l'utiliser lors de la ré-initialisation du mot passe (étape 2)
			$result->setProperty('adm_date_password', $date_reinit->format('Y-m-d H:i:s'));
			$datastore->update($result);

			// Envoi du mail à l'administrateur afin de lui permettre de ré-initialiser son mot de passe
			$email = new Email();
			$email->setFrom( 'Mot de passe RiaShop <<EMAIL>>');
			$email->addTo($admin_email);
			$email->addBcc( '<EMAIL>');
			$email->setSubject( 'Accès à votre compte administrateur' );
			$email->addHtml('<table width="480" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="RiaShop" src="https://start.yuto.fr/dist/images/RS_header.svg"></td></tr><tr><td style="font-size: 0.9em;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );
			$email->addParagraph('Cher client, chère cliente,');
			$email->addParagraph('Une demande de récupération de mot de passe vous concernant a été effectuée sur votre site à '.$date_reinit->format('H:i').' aujourd\'hui. Par mesure de sécurité, aucun mot de passe n\'est envoyé directement par email, afin d\'éviter qu\'une personne mal intentionnée ne l\'intercepte.');
			$email->addParagraph('Pour accéder à votre compte administrateur, vous devez définir un nouveau mot de passe. Pour cela, nous vous prions de bien vouloir vous rendre à l\'adresse ci-dessous :');

			$url =  getenv('site_url').'/login.php?new-pwd=2&p='.$token_reinit_password;
			$email->addParagraph('<a href="'.$url.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=pwd_forget&amp;utm_content=page_pwd">'.$url.'</a>');

			$email->addParagraph('Si la demande de mot de passe a été effectuée par une autre personne que vous même, vous ne devez pas vous inquiéter. Cette personne ne pourra en aucun cas accéder à vos informations personnelles ou usurper votre identité.');
			$email->addParagraph('Si vous avez des questions concernant la procédure de récupération de mot de passe, n\'hésitez pas à nous contacter en répondant simplement à cet email.');
			$email->addHtml( '</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(35,46,99);"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb(255, 255, 255);" href="mailto:<EMAIL>"><EMAIL></a></div></div>' );

			return $email->send();
		}

		/** Cette fonction permet de réinitialiser le mot de passe d'un administrateur.
		 *  @param string $email Obligatoire, Adresse email du compte
		 *  @param string $password Obligatoire, nouveau mot de passe
		 *  @param string $token Obligatoire, token de contrôle de propriété de la demande de ré-initialisation
		 *  @return bool True si la mise à jour s'est correctement déroulée
		 *  @return bool False si l'un des paramètres obligatoire est faux ou omis
		 *  @return int -1 si le token est invalide
		 *	@return bool true en cas de succès
		 */
		public static function reinitPassword($email, $password, $token){
			if( trim($email) == '' ){
				return false;
			}

			if( trim($password) == '' ){
				return false;
			}

			if( trim($token) == '' ){
				return false;
			}

			$datastore = new DatastoreClient();

			// Récupère le mot de passe actuel du compte administrateur avec cette adresse mail
			$query = $datastore->query()
				->kind('administrator')
				->filter('adm_email', '=', $email)
				->filter('adm_date_deleted', '=', '');

			// Contrôle que l'on arrive bien à récupérer un compte administrateur
			$result = $datastore->runQuery($query)->current();
			if( $result === null ){
					return false;
			}

			// Regénère le token ayant normalement été envoyé pour la demande
			$send_token = md5($result->getProperty('adm_email').$result->getProperty('adm_password').$result->getProperty('adm_date_password'));

			// Contrôle que le token est bien celui ayant été envoyé
			if( $token != $send_token ){
				return -1;
			}

			// Mise à jour du mot de passe
			$result->setProperty('adm_password', password_hash($password, PASSWORD_DEFAULT));
			$datastore->update($result);

			return true;
		}

		/** Cette fonction permet de vérifier qu'un administrateur existe bien dans la base via son identifiant.
		 *  @param int $id Obligatoire, identifiant administrateur
		 *  @return bool True si l'administrateur existe, False dans le cas contraire
		 */
		private function exists() {
			if( trim($this->id) == '' ){
				return false;
			}
			$query = $this->datastore->query()
				->kind('administrator')
				->filter('__key__', '=', $this->datastore->key('administrator', $this->id));

			$result = $this->datastore->runQuery($query)->current();
			if( $result === null ){
				return false;
			}

			return trim($result->getProperty('adm_date_deleted')) == '';
		}

		/** Cette fonction récupère l'utilisateur du datasotre
		 *
		 * @param string $email Email de l'utilisateur
		 * @return mixed Retourne Google\Cloud\Datastore\Entity si success, null si error ou pas de résultat
		 */
		private function getUserFromDatastore($email){
			$query = $this->datastore->query()
				->kind('administrator')
				->filter('adm_email', '=', $email)
				->filter('adm_date_deleted', '=', '');

			return $this->datastore->runQuery($query)->current();
		}

		/** Cette fonction récupère l'utilisateur du datasotre
		 *
		 * @param string $email Email de l'utilisateur
		 * @return mixed Retourne Google\Cloud\Datastore\Entity si success, null si error ou pas de résultat
		 */
		private function getUserByIdFromDatastore($id){
			$query = $this->datastore->query()
				->kind('administrator')
				->filter('__key__', '=', $this->datastore->key('administrator', $id));

			return $this->datastore->runQuery($query)->current();
		}

		/** Cette fonction permet de renseigner les informations de la class en fonction des informations du datastore
		 *
		 * @param Google\Cloud\Datastore\Entity $entity Entité du datastore
		 * @return void Ne retourne rien
		 */
		private function initFromEntity(Google\Cloud\Datastore\Entity $entity){
			// Préparation des informations sur le compte administrateur
			$this->id               = $entity->key()->pathEndIdentifier();
			$this->email            = $entity->getProperty('adm_email');

			// Information sur la personne
			$this->civility         = $entity->getProperty('adm_civility');
			$this->firstname        = $entity->getProperty('adm_firstname');
			$this->lastname         = $entity->getProperty('adm_lastname');
			$this->phone            = $entity->getProperty('adm_phone');
			$this->mobile           = $entity->getProperty('adm_mobile');
			$this->work             = $entity->getProperty('adm_work');
			$this->fax              = $entity->getProperty('adm_fax');
			$this->birthday         = $entity->getProperty('adm_birthday');
			$this->lang             = $entity->getProperty('adm_lang');
			$this->password			= $entity->getProperty('adm_password');

			$this->showWelcome      = $entity->getProperty('adm_show_welcome');
			$this->access           = $entity->getProperty('adm_tenant_access');

			$this->datePassword		= $entity->getProperty('adm_date_password');
			$this->dateDeleted		= $entity->getProperty('adm_date_deleted');

			// Si l'administrateur n'a qu'un seul accès, on le sélectionne automatiquement
			if( is_array($this->access) && count($this->access) == 1 ){
				$this->selected = $this->access[0];
				$this->toReloadUser = true;
			}
		}
	}
