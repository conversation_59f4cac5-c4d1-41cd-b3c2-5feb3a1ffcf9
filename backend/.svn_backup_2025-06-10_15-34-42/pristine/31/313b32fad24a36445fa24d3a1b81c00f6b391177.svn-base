<?php
/** 
 * \defgroup api-users-payments Moyens de paiement
 * \ingroup crm
 * @{		
*/
switch( $method ){
	/** @{@}
 	 * @{
	 * \page api-users-payments-add Ajout
	 *	 
	 *	Cette fonction ajoute un moyen de paiement à un utilisateur.
	 *
	 *	 \code
	 *		POST /users/payments/
	 *	 \endcode
	 *	
	 * @param int $usr_id Obligatoire, Identifiant du compte client
	 * @param $pay_id Obligatoire, Identifiant du moyen de paiement
	 * @param $days Obligatoire, Nombre de jours pour les paiements par compte
	 * @param $days_type Obligatoire, Type de délai (0:Immé<PERSON><PERSON>, 1:fin de civil, 2:fin de mois)
	 * @param $day_stop Obligatoire, Jour de remise de la facture après expiration du délai
	 *	
	 * @return true si l'ajout s'est déroulé avec succès 
	 * @}
	*/
	case 'add':
		if( !isset($_POST['usr_id'], $_POST['pay_id'], $_POST['days'], $_POST['days_type'], $_POST['day_stop']) ){
			throw new Exception('Paramètres incomplet');
		}

		if( gu_users_payment_types_add( $_POST['usr_id'], $_POST['pay_id'], $_POST['days'], $_POST['days_type'], $_POST['day_stop']) ){
			$result = true;
		}

		break;
	/** @{@}
 	 * @{
	 * \page api-users-payments-upd Mise à jour 
	 *
	 * Cette fonction modifie le moyen de paiement d'un utilisateur.
	 *
	 *		\code
	 *			PUT /users/payments/
	 *		\endcode
	 *	
	 * @param int $usr_id Obligatoire, Identifiant du compte client
	 * @param $pay_id Obligatoire, Identifiant du moyen de paiement
	 * @param $days Obligatoire, Nombre de jours pour les paiements par compte
	 * @param $days_type Obligatoire, Type de délai (0:Immédiat, 1:fin de civil, 2:fin de mois)
	 * @param $day_stop Obligatoire, Jour de remise de la facture après expiration du délai
	 *	
	 * @return true si la mise à jour s'est déroulée avec succès 
	 *	
	 * @}
	*/
	case 'upd':
		if( !isset($_REQUEST['usr_id'], $_REQUEST['pay_id'], $_REQUEST['days'], $_REQUEST['days_type'], $_REQUEST['day_stop']) ){
			throw new Exception('Paramètres incomplet');
		}

		if( gu_users_payment_types_update( $_REQUEST['usr_id'], $_REQUEST['pay_id'], $_REQUEST['days'], $_REQUEST['days_type'], $_REQUEST['day_stop']) ){
			$result = true;
		}

		break;
	/** @{@}
 	 * @{
	 * \page api-users-payments-del Suppression
	 *
	 * Cette fonction supprime le moyen de paiement d'un utilisateur.	
	 *
	 *	\code
	 *		DELETE /users/payments/
	 *	\endcode
	 *	
	 * @param int $usr_id Obligatoire, Identifiant du compte client
	 * @param $pay_id Obligatoire, Identifiant du moyen de paiement
	 *	
	 * @return true si la suppression s'est déroulée avec succès 
	 * @}
	*/
	case 'del':

		if( !isset($_REQUEST['usr_id']) || !is_numeric($_REQUEST['pay_id']) ){
			throw new Exception('Paramètres incomplet');
		}

		if( !gu_users_payment_types_del($_REQUEST['usr_id'],$_REQUEST['pay_id']) ){
			throw new Exception("Erreur lors de la suppression du moyen de paiement");
		}else{
			$result = true;
		}

		break;
}
///@}