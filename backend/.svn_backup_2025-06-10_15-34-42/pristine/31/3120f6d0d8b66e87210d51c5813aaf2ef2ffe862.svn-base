<?php
require_once 'Services/Abstract.class.php';

/**	Cette classe permet le chargement d'un panier modèle.
 */
class CartModelService extends AbstractService
{
	/**	Identifiant du panier modèle
	 * @var	int
	 */
	protected $id = 0;

	/**	Référence du panier modèle
	 * @var	string|int|null
	 */
	protected $ref = null;

	/**	Description du panier modèle
	 * @var	string|null
	 */
	protected $desc = null;

	/**	Date du panier modèle
	 * @var	string|null
	 */
	protected $date = null;

	/**	Tableau des produits du panier modèle
	 * @var	null|array
	 */
	protected $products = null;

	/**	Nombre de produits dans le panier modèle (sans tenir compte des quantités)
	 * @var	int
	 */
	protected $count = 0;

	/**	Tableau des champs avancés
	 * @var	null|array
	 */
	protected $fields = null;

	/**	Tableau des attributs de requête
	 * @var	array
	 */
	protected $attributes = [
		// 'with_img'			=> false, //<<<< Si vrai, récupére les images des produits
		// 'cfg_img'			=> 'high', //<<<< Nom de la config image à utiliser
		// 'fld'				=> false, //<<<< Identifiant d'un champ avancé ou tableau d'identifiants de champs avancés ou tableau fld_id => valeur
		// 'or_between_fld'	=> false, //<<<< Indique si, pour le tableau $fld, les différents champs sont séparés par des "ET" (vrai par défaut) ou des "OU"
		// 'published'			=> null, //<<<< Si vrai seul les produits publiés sont retournés. Si faux, seul les produits non publiés sont retournés. Valeur par défaut : null (tous les produits)
		// 'orderable'			=> null, //<<<< Si vrai seul les produits commandables sont retournés. Si faux, seul les produits non commandables sont retournés. Valeur par défaut : null (tous les produits)
		'only_prd_ids'		=> false, //<<<< Retourne uniquement les identifiants des produits si à true
		// 'with_price'		=> false,

	];

	/**	Constructeur de la classe
	 * @param	int		$mod	Obligatoire, Identifiant d'un panier modèle
	 * @return	void
	 */
	public function __construct($mod)
	{
		$this->id = is_numeric($mod) && $mod > 0 ? (int)$mod : 0;
		$this->__loadModel();
	}

	/**	Charge quelques informations de base de la commande
	 * @todo	Autorisations
	 * @return	CartModelService	L'instance en cours
	 */
	private function __loadModel()
	{

		if ($this->id < 1) {
			return $this;
		}
		global $config;

		$sql = '
			select
				m.ord_id as id,
				m.ord_ref as ref,
				m.ord_comments as "desc",
				m.ord_date as date
			from
				ord_orders m
			where
					m.ord_tnt_id = ' . $config['tnt_id'] . '
				and m.ord_id = ' . $this->id . '
				and m.ord_state_id = ' . _STATE_MODEL . '
				and m.ord_wst_id = ' . $config['wst_id'] . '
				and m.ord_masked = 0
				and m.ord_date_archived is null
		';

		$rmod = ria_mysql_query($sql);

		if (!ria_mysql_num_rows($rmod)) {
			return $this;
		}
		$mod = ria_mysql_fetch_assoc($rmod);

		$this->ref = (is_string($mod['ref']) && trim($mod['ref']) != '') || is_numeric($mod['ref']) ? $mod['ref'] : null;
		$this->desc = is_string($mod['desc']) && trim($mod['desc']) != '' ? $mod['desc'] : null;
		$this->date = $mod['date'];

		return $this;
	}

	/**	Charge les produits du panier modèle
	 * @return	CartModelService	L'instance en cours
	 */
	public function products()
	{
		if ($this->id < 1) {
			return $this;
		}
		global $config, $hook;

		$sql = '
			select
				prd.prd_id as prd,
				prd.prd_qte as qty
			from
				ord_products prd
			inner join
				ord_orders m
			on
					m.ord_tnt_id = ' . $config['tnt_id'] . '
				and m.ord_id = ' . $this->id . '
				and m.ord_state_id = ' . _STATE_MODEL . '
				and m.ord_wst_id = ' . $config['wst_id'] . '
				and m.ord_masked = 0
				and m.ord_date_archived is null
			inner join
				prd_products p
			on
					p.prd_id = prd.prd_id
				and p.prd_tnt_id = ' . $config['tnt_id'] . '
				and p.prd_publish = 1
				and p.prd_publish_cat = 1
				and p.prd_date_deleted is null
				and p.prd_orderable = 1
			where
					prd.prd_tnt_id = ' . $config['tnt_id'] . '
				and prd.prd_ord_id = ' . $this->id . '
				and prd.prd_qte > 0
			group by prd.prd_id
		';

		$rprds = ria_mysql_query($sql);

		if (!ria_mysql_num_rows($rprds)) {
			return $this;
		}
		$only_prd_ids = $this->getAttribute('only_prd_ids');
		$this->products = [];

		while ($prd = ria_mysql_fetch_assoc($rprds)) {

			if ($only_prd_ids) {
				if (in_array($prd['prd'], $this->products)) {
					continue;
				}
				$this->products[] = $prd['prd'];
				$this->count++;
				continue;
			}
			$obj_prd = $hook->apply_filter('CartModelService_onLoadingProduct', false, ['product' => $prd]);

			if (!$obj_prd) {
				$obj_prd = new ProductService([
					'prd'		=> $prd['prd'],
					'withprice'	=> true
				]);
				$obj_prd->general();
			}

			if ($obj_prd->getStock() < 1) {
				continue;
			}

			$this->products[] = [
				'prd'		=> $obj_prd,
				'line'		=> [
					'qty'	=> $prd['qty'],
				],
			];
		}

		return $this;
	}

	/** Retourne un tableau des produits du panier modèle
	 * @param	bool	$force	Optionnel, Force le chargement des produits
	 * @return	bool|array	Tableau des produits du panier modèle, false sinon
	 */
	public function getProducts($force = false)
	{
		if (is_null($this->products) || (is_bool($force) && $force)) {
			$this->products();
		}

		if (!is_array($this->products) || count($this->products) < 1) {
			return false;
		}
		$ar_prds = [];

		foreach ($this->products as $prd) {
			$prd['prd'] = $prd['prd']->getData();
			$ar_prds[] = $prd;
		}
		return $ar_prds;
	}

	/**	Retourne un tableau de toutes les informations du panier modèle
	 * @return	array	Tableau contenant toutes les informations du panier modèle
	 */
	public function getFullData()
	{
		$ar_model = $this->getData(false);

		$ar_model['products'] = $this->getProducts();

		return $ar_model;
	}

	/**	Permet le chargement des champs avancés du panier modèle
	 * @param	array	$fld	Obligatoire, Tableau des identifiants des champs avancés à récupérer
	 * @return	CartModelService	Instance en cours
	 */
	public function fields($fld)
	{
		if (!is_array($fld) || !count($fld) || $this->id < 1) {
			return $this;
		}
		$ar_fld = [];

		foreach ($fld as $fld_id) {
			if (!is_numeric($fld_id) || $fld_id <= 0) {
				continue;
			}
			$ar_fld[] = $fld_id;
		}

		if (!count($ar_fld)) {
			return $this;
		}
		global $config;

		$rfld = ria_mysql_query('
			select
				fld.fld_id as id,
				fld.fld_name as name,
				val.pv_value as obj_value
			from
				fld_object_values as val
			right join
				fld_fields as fld
			on
					fld.fld_tnt_id = ' . $config['tnt_id'] . '
				and fld.fld_id = val.pv_fld_id
				and fld.fld_cls_id = ' . CLS_ORDER . '
				and fld.fld_date_deleted is null
			where
					val.pv_tnt_id = ' . $config['tnt_id'] . '
				and val.pv_obj_id_0 = ' . $this->id . '
				and val.pv_obj_id_1 = 0
				and val.pv_obj_id_2 = 0
				and val.pv_fld_id in (' . implode(',', $ar_fld) . ')
		');

		if (!ria_mysql_num_rows($rfld)) {
			return $this;
		}
		$this->fields = [];

		while ($field = ria_mysql_fetch_assoc($rfld)) {
			$this->fields['fld' . $field['id']] = [
				'id'		=> $field['id'],
				'name'		=> $field['name'],
				'value'		=> $field['obj_value']
			];
		}

		return $this;
	}

	/**	Permet de controler la valeur d'un attribut et de le mettre à jour
	 * @param	string	$key	Obligatoire, Nom de l'attribut
	 * @param	mixed	$value	Obligatoire, Valeur de l'attribut
	 * @return	bool	True en cas de succes, false sinon
	 */
	protected function __sanitizeAttribute($key, $value)
	{

		if (!is_string($key)) {
			return false;
		}
		global $config;

		$key = trim(strtolower($key));

		switch ($key) {

				// case 'fld':
				// case 'sort':
				// 	$this->attributes[$key] = is_array($value) && count($value) ? $value : false;
				// 	break;

				// case 'or_between_fld':
			case 'only_prd_ids':
				// case 'only_parents':
				// case 'with_img':
				// case 'with_price':
				// case 'children':
				$this->attributes[$key] = is_bool($value) ? $value : false;
				break;

				// case 'published':
				// case 'orderable':
				// 	$this->attributes[$key] = is_bool($value) || is_null($value) ? $value : null;
				// 	break;

			default:
				return false;
		}
		return true;
	}
}
