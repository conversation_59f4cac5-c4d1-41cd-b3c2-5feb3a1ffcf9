<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/admin/v1/iam.proto

namespace Google\Iam\Admin\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Iam\Admin\V1\ListServiceAccountKeysRequest\KeyType instead.
     * @deprecated
     */
    class ListServiceAccountKeysRequest_KeyType {}
}
class_exists(ListServiceAccountKeysRequest\KeyType::class);
@trigger_error('Google\Iam\Admin\V1\ListServiceAccountKeysRequest_KeyType is deprecated and will be removed in the next major release. Use Google\Iam\Admin\V1\ListServiceAccountKeysRequest\KeyType instead', E_USER_DEPRECATED);

