{"name": "muglug/package-versions-56", "description": "A backport of ocramius/package-versions that supports php ^5.6. Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "type": "composer-plugin", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^5.6 || ^7.0", "composer-plugin-api": "^1.0"}, "require-dev": {"phpunit/phpunit": "^5.7.5", "composer/composer": "^1.3", "ext-zip": "*"}, "autoload": {"psr-4": {"Muglug\\PackageVersions\\": "src/PackageVersions"}}, "autoload-dev": {"psr-4": {"Muglug\\PackageVersionsTest\\": "test/PackageVersionsTest"}}, "extra": {"class": "Muglug\\PackageVersions\\Installer", "branch-alias": {"dev-master": "2.0.x-dev"}}, "scripts": {"post-update-cmd": "Muglug\\PackageVersions\\Installer::dumpVersionsClass", "post-install-cmd": "Muglug\\PackageVersions\\Installer::dumpVersionsClass"}}