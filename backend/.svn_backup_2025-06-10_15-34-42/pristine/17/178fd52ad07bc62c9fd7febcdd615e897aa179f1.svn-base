<?php
/**
 * \defgroup orders_products Produits commandés   
 * \ingroup orders
 * @{	 
*/

switch( $method ){
	/** @{@}
 	 * @{	
	 * \page api-orders-products-get Chargement
	 *
	 * Cette fonction récupére des lignes de commande.	
	 *
	 *		\code
	 *			GET /orders/products/
	 *		\endcode
	 *
	 * @param $ord Obligatoire, Identifiant de la commande
	 * @param $prd Facultatif, Identifiant du produit de la ligne de commande
	 * @param $line Facultatif, Identifiant du no de la ligne de commande
	 *	
	 * @return json,  avec les colonnes :
	 *	\code{.json}
	 *       {
     *			"ord_id" : identifiant de la commande
	 *			"id" : Identifiant du produit
	 *			"line" : numéro de ligne
	 *			"ref" : référence du produit
	 *			"name" : désignation du produit
	 *			"title" : titre du produit
	 *			"desc" : description du produit
	 *			"qte" : quantité commandée
	 *			"price_ht" : prix ht du produit à l'unité
	 *			"tva_rate" : taux de TVA de la ligne
	 *			"price_ttc" : prix ttc du produit à l'unité
	 *			"total_ht" : sous total pour le produit, hors taxes (price_ht *qte)
	 *			"total_ttc" : sous total pour le produit, ttc (price_ttc *qte)
	 *			"notes" : annotations clients sur la ligne de commande
	 *			"date-livr" : date de livraison souhaitée
	 *			"date_livr_en" : date de livraison souhaitée au format anglo-saxon
	 *			"publish" : article publié oui/non
	 *			"weight" : poids brut unitaire
	 *			"weight_net" : poids net unitaire
	 *			"weight_net_total" : poids net total de la ligne
	 *			"img_id" : identifiant de l'image associée à l'article
	 *			"nsync" : détermine si la ligne a besoin d'une mise à jour avec la gestion commerciale
	 *			"date_created" : date de création de la ligne
	 *			"date_modified" : date de dernière modification de la ligne
	 *			"barcode" : code barre du produit
	 *			"parent" : identifiant du parent si la ligne est un composant de nomenclature variable
	 *			"child-line" : rang de la nomenclature si plusieurs existent dans la commande
	 *			"ecotaxe" : Eco-participation pour une unité de la ligne
	 *			"is_bookmark" : Détermine si le produit de la ligne est dans les favoris de l'utilisateur
	 *			"col_id" : Identifiant d'un condtionnement éventuel (0 si pas de contionnement)
	 *			"col_qte" : Quantité en conditionnement (1 par défaut)
	 *			"col_name" : Nom du colisage (par défaut chaine vide)
	 *			"orderable" : Détermine si le produit est commandable
	 *			"sell_weight" : Détermine si la quantité pour la ligne est un poids ou un volume (dans la plus petite unité possible)
	 *			"perishable" : Détermine si le produit est périssable ou non
	 *			"opt" : identifiant de l'option pour une ligne de nomenclature variable
	 *			"cod" : identifiant de la promotion ayant ajouté cette ligne
	 *			"type_promo" : type de promotion (produit offert, X acheté Y offert, ...)
	 *			"ord_child_id" : identifiant de la commande enfant à laquelle la ligne est rattachée
	 *			"pos" : position de la ligne dans la commande (pertinent quand il s'agit d'un modèle)
	 *			"prd_date_modified" : date de dernière modification du produit
	 *			"ref_supplier" : référence de l'article chez le fournisseur (uniquement si $sort contient "ps-ref")
	 *			"real_qte" : quantité permettant le passage de price_ht à price_ttc (combinaison de qte, col_qte et sell_weight).
	 *			"line_dps_id" : identifiant du dépôt utilisé sur la ligne de commande
	 *			"brd_name" : Nom de la marque associé au produit
	 *       },
	 *	\endcode
	 *	@}
	*/
	case 'get':
		if( !isset($_REQUEST['ord']) ){
			throw new Exception('Paramètres invalide');
		}

		$prd = 0;
		if( isset($_REQUEST['prd']) && is_numeric($_REQUEST['prd']) ){
			$prd = $_REQUEST['prd'];
		}
		$line = null;
		if( isset($_REQUEST['line']) && is_numeric($_REQUEST['line']) ){
			$line = $_REQUEST['line'];
		}

		$rprd = ord_products_get($_REQUEST['ord'], false, $prd, '', $line, false, 1);

		if( $rprd ){
			$tmp = array();
			while( $prd = ria_mysql_fetch_assoc($rprd) ){
				$tmp[] = $prd;
			}
			$result = true;
			$content = $tmp;
		}

		break;
	/** @{@}
 	 *	@{
	 *	\page api-orders-products-add Ajout / Mise à jour 
	 *
	 *	 Cette fonction ajoute ou met à jour une ligne de commande.
	 *
	 *		\code
	 *			POST /orders/products/
	 *			or
	 *			PUT /orders/products/
	 *		\endcode
	 *	
	 * @param $ord Obligatoire, Identifiant de la commande
	 * @param $prd Obligatoire, Identifiant du produit
	 * @param $line Obligatoire, numéro de ligne
	 * @param string $ref Obligatoire, Référence du produit
	 * @param string $name Obligatoire, Désignation du produit
	 * @param $qte Obligatoire, Quantité commandée
	 * @param $price_ht Obligatoire, Prix HT du produit
	 * @param $tva Obligatoire, taux de tva appliqué au produit, écrit sous la forme 1.000 (ex: 1.196)
	 * @param $datelivr Obligatoire, Date de livraison souhaitée
	 * @param $pos Obligatoire, Position d'une ligne produit dans une commande
	 * @param $price_ttc Facultatif, tarif TTC de l'article
	 * @param $ecotaxe Facultatif, Ecotaxe unitaire
	 * @param $parent Facultatif, Identifiant d'un produit parent
	 * @param $child_line Facultatif, Identifiant de line pour les produits enfants
	 * @param $cod_id Facultatif, Identifiant de promotion
	 * @param $purchase_avg Facultatif, Prix d'achat moyen de la ligne
	 * @param string $date_created Facultatif, Date de d'ajout d'un ligne produit dans une commande
	 * @param $weight Facultatif, ?????
	 * @param $price_brut Facultatif, ?????
	 * @param $free Facultatif, ?????
	 * @param $discount Facultatif, ?????
	 * @param $notes Facultatif, ?????
	 * 
	 * @return true si l'ajout/ la mise à jour s'est déroulé(e) avec succès 
	 * @}
	*/
	case 'upd':
	case 'add':
		if( !isset($_REQUEST['ord'],$_REQUEST['prd'],$_REQUEST['line'],$_REQUEST['ref'],$_REQUEST['name'],$_REQUEST['qte'],$_REQUEST['price_ht'],$_REQUEST['tva'],$_REQUEST['datelivr'],$_REQUEST['pos']) ){
			throw new Exception('Paramètres invalide');
		}

		$price_ttc=null;
		if( isset($_REQUEST['price_ttc']) ) $price_ttc = $_REQUEST['price_ttc'];

		$ecotaxe=0;
		if( isset($_REQUEST['ecotaxe']) ) $ecotaxe = $_REQUEST['ecotaxe'];

		$weight = 0;
		if( isset($_REQUEST['weight']) && is_numeric($_REQUEST['weight']) && $_REQUEST['weight']>0 ) $weight = $_REQUEST['weight'];

		$position = null;
		if( isset($_REQUEST['pos']) && is_numeric($_REQUEST['pos'])) $position = $_REQUEST['pos'];

		$price_brut = null;
		if( isset($_REQUEST['price_brut']) ){
			$_REQUEST['price_brut'] = str_replace(array(',', ' '), array('.', ''), $_REQUEST['price_brut']);
			if( is_numeric($_REQUEST['price_brut']) ){
				$price_brut = $_REQUEST['price_brut'];
			}
		}

		$free = null;
		if( isset($_REQUEST['free']) && $_REQUEST['free']==1 ){
			$free = true;
		}

		$parent = false;
		if( isset($_REQUEST['parent']) && is_numeric($_REQUEST['parent']) ){
			$parent = $_REQUEST['parent'];
		}

		$child_line = false;
		if( isset($_REQUEST['child_line']) && is_numeric($_REQUEST['child_line']) ){
			$child_line = $_REQUEST['child_line'];
		}

		$cod_id = false;
		if( isset($_REQUEST['cod_id']) && is_numeric($_REQUEST['cod_id']) ){
			$cod_id = $_REQUEST['cod_id'];
		}

		$discount = false;
		if( isset($_REQUEST['discount']) && is_numeric($_REQUEST['discount']) ){
			$discount = $_REQUEST['discount'];
		}

		if( $method == "upd"){
			$res = ord_products_update_sage( $_REQUEST['ord'],$_REQUEST['prd'],$_REQUEST['line'],$_REQUEST['ref'],$_REQUEST['name'],$_REQUEST['qte'],$_REQUEST['price_ht'],$_REQUEST['tva'],$_REQUEST['datelivr'],$price_ttc,$ecotaxe,$parent,$child_line,$cod_id, null, null, $position);
		}else{
			$res = ord_products_add_sage( $_REQUEST['ord'],$_REQUEST['prd'],$_REQUEST['line'],$_REQUEST['ref'],$_REQUEST['name'],$_REQUEST['qte'],$_REQUEST['price_ht'],$_REQUEST['tva'],$_REQUEST['datelivr'],$price_ttc,$ecotaxe,$parent,$child_line,$cod_id, null, null, $position );
		}

		if( $res && isset($_REQUEST['col']) && is_numeric($_REQUEST['col']) ){
			fld_object_values_set( array( $_REQUEST['ord'], $_REQUEST['prd'], $_REQUEST['line'] ), _FLD_PRD_COL_ORD_PRODUCT, $_REQUEST['col'] );
		}

		if( $res && $weight ){
			fld_object_values_set( array( $_REQUEST['ord'], $_REQUEST['prd'], $_REQUEST['line'] ), _FLD_ORD_LINE_WEIGHT, $weight );
		}

		if( $res && $discount ){
			fld_object_values_set( array( $_REQUEST['ord'], $_REQUEST['prd'], $_REQUEST['line'] ), _FLD_ORD_LINE_DISCOUNT, $discount );
		}

		if( $res && $price_brut!==null ){
			fld_object_values_set( array( $_REQUEST['ord'], $_REQUEST['prd'], $_REQUEST['line'] ), _FLD_PRD_ORD_PRICE_BRUT, $price_brut );
		}

		if( $res && $free ){
			fld_object_values_set( array( $_REQUEST['ord'], $_REQUEST['prd'], $_REQUEST['line'] ), _FLD_PRD_ORD_FREE, 'Oui' );
		}

		if( isset($_REQUEST['notes']) ){
			ord_products_notes_update($_REQUEST['ord'], $_REQUEST['prd'], $_REQUEST['notes'], "", $_REQUEST['line'] );
		}

		if( $res ){
			$result = true;

			// log les données envoyé par la tablette
			api_log('Commandes '.$config['tnt_id'].' FDV '.$_REQUEST['ord'].' - line : '.print_r($_REQUEST,true), 'debug');

		}else{
			throw new Exception("Erreur lors de l'ajout du produit : ord_products_add_sage");
		}

		break;

	/** @{@}
 	 * @{	
	 * \page api-orders-products-del Suppression
	 *
	 * Cette fonction supprime une ligne de commande.
	 *	
	 *		\code
	 *			DELETE /orders/products/
	 *		\endcode
	 *
	 * @param $ord Obligatoire, Identifiant de la commande
	 * @param $prd Obligatoire, Identifiant du produit
	 * @param $line Obligatoire, numéro de ligne
	 *	
	 * @return true si la suppression s'est déroulée avec succès 
	 * @}
	*/
	case 'del':

		if( !isset($_REQUEST['ord']) || !ord_orders_exists($_REQUEST['ord'])){
			throw new Exception("Paramètres invalide");
		}

		if( isset($_REQUEST['prd']) && $_REQUEST['prd']=='DIVERS' ){
			unset($_REQUEST['line']);
		}

		// log les données envoyé par la tablette
		api_log('Commandes '.$config['tnt_id'].' FDV '.$_REQUEST['ord'].' - line del : '.print_r($_REQUEST,true), 'debug');

		if( isset($_REQUEST['ord'],$_REQUEST['prd'],$_REQUEST['line']) ){
			if( !ord_products_del($_REQUEST['ord'],$_REQUEST['prd'],$_REQUEST['line']) ){
				throw new Exception("Erreur lors de la suppression du produit : ord_products_del");
			}else{
				$result = true;
			}
		}elseif( isset($_REQUEST['ord'],$_REQUEST['prd']) ){
			if( !ord_products_del($_REQUEST['ord'],$_REQUEST['prd']) ){
				throw new Exception("Erreur lors de la suppression du produit : ord_products_del");
			}else{
				$result = true;
			}
		}
		elseif( isset($_REQUEST['ord']) ){
			if( !ord_products_del($_REQUEST['ord']) ){
				throw new Exception("Erreur lors de la suppression du produit : ord_products_del");
			}else{
				$result = true;
			}
		}

		break;
}

///@}