<?php
/**
 * This file has been @generated by a phing task by {@link BuildMetadataPHPFromXml}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */


namespace libphonenumber;
class CountryCodeToRegionCodeMapForTesting {

  // A mapping from a country code to the region codes which denote the
  // country/region represented by that country code. In the case of multiple
  // countries sharing a calling code, such as the NANPA countries, the one
  // indicated with "isMainCountryForCode" in the metadata should be first.

   public static $countryCodeToRegionCodeMapForTesting = array (
  1 => 
  array (
    0 => 'US',
    1 => 'BB',
    2 => 'BS',
    3 => 'CA',
  ),
  33 => 
  array (
    0 => 'FR',
  ),
  36 => 
  array (
    0 => 'HU',
  ),
  39 => 
  array (
    0 => 'IT',
  ),
  44 => 
  array (
    0 => 'GB',
    1 => 'GG',
  ),
  46 => 
  array (
    0 => 'SE',
  ),
  48 => 
  array (
    0 => 'PL',
  ),
  49 => 
  array (
    0 => 'DE',
  ),
  52 => 
  array (
    0 => 'MX',
  ),
  54 => 
  array (
    0 => 'AR',
  ),
  55 => 
  array (
    0 => 'BR',
  ),
  61 => 
  array (
    0 => 'AU',
    1 => 'CC',
    2 => 'CX',
  ),
  64 => 
  array (
    0 => 'NZ',
  ),
  65 => 
  array (
    0 => 'SG',
  ),
  81 => 
  array (
    0 => 'JP',
  ),
  82 => 
  array (
    0 => 'KR',
  ),
  86 => 
  array (
    0 => 'CN',
  ),
  244 => 
  array (
    0 => 'AO',
  ),
  262 => 
  array (
    0 => 'RE',
    1 => 'YT',
  ),
  290 => 
  array (
    0 => 'TA',
  ),
  374 => 
  array (
    0 => 'AM',
  ),
  375 => 
  array (
    0 => 'BY',
  ),
  376 => 
  array (
    0 => 'AD',
  ),
  800 => 
  array (
    0 => '001',
  ),
  882 => 
  array (
    0 => '001',
  ),
  971 => 
  array (
    0 => 'AE',
  ),
  979 => 
  array (
    0 => '001',
  ),
);

}
