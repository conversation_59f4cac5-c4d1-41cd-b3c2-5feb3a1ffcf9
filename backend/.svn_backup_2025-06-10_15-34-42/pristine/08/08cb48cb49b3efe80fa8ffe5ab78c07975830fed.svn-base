<?php
set_include_path(dirname(__FILE__) . '/../include/');
require_once('cfg.variables.inc.php');
require_once('PriceWatching/prw.amazon.inc.php');
require_once('products.inc.php');

$tnt_id = 22;

unset($config);

$configs = cfg_variables_get_all_tenants($tnt_id);

foreach($configs as $config){
	$res = prd_products_get_simple($id=0, $ref='', $published=false, $cat=0, $catchilds=false, $fld=false, $with_price=false, $sort=false, array('childs' => true));
	echo ria_mysql_num_rows($res);
	$prw = new Amazon();
	$file = dirname(__FILE__).'/../locks/export-TPLC-Asin.csv';
	if (file_exists($file)) {
		unlink($file);
	}
	$fp = fopen($file, 'w+');
	fputcsv($fp,array('LIBELLE','REFERENCE','ASIN'),';');
	while($row = ria_mysql_fetch_assoc($res)){
		if($row['barcode']!==''){
			$ean = $prw->getCptRef($row['barcode']);
			if($ean){
				$asin = array($row['name'],$row['ref'],$ean);
				fputcsv($fp,$asin,';');
			}
		}
	}
	fclose($fp);
}
