<?php

set_include_path(dirname(__FILE__) . '/../include/');
require_once(str_replace('/tools', '', getenv('PWD')) . '/htdocs/config.inc.php');
require_once('ord.bl.inc.php');

$bl_id = 0;
$date_start = $date_end = false;

if (isset($argv[1]) && is_numeric($argv[1]) && $argv[1]) {
    $bl_id = intval($argv[1]);
}
if (isset($argv[2]) && isdate($argv[2])) {
    $date_start = $argv[2];
}
if (isset($argv[3]) && isdate($argv[3])) {
    $date_end = $argv[3];
}

$r_bl = ord_bl_get( $bl_id, 0, false, false, false, array(), false, array(), $date_start, $date_end );
if (!$r_bl || !ria_mysql_num_rows($r_bl)) {
    return;
}

$i = 1;
$nb = ria_mysql_num_rows($r_bl);

while ($bl = ria_mysql_fetch_assoc($r_bl)) {
    print ($i++).' / '.$nb."\n";
    ord_bl_update_totals($bl['id']);
}