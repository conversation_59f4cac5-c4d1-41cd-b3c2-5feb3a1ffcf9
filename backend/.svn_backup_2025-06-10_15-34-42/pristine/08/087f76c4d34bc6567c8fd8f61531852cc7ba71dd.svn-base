<?php

require_once('db.inc.php');
require_once('fields.inc.php');

/**	\defgroup model_category_filters Filtres de catégories
 *	\ingroup model_categories
 *	Ce module comprend les fonctions nécessaires à la gestion des filtres pour une catégorie de produits
 * @{
 */

 // \cond onlyria
/**	Permet l'ajout d'un filtre pour une catégorie de produit
 *	@param int $cat_id Obligatoire, Identifiant de la catégorie (ne peut être vide)
 *	@param int $fld_id Obligatoire, Identifiant du champs (ne peut être vide)
 *	@param int $depth Facultatif, Ordre de filtre (spécifique BioAlternatives)
 *	@param array $vals Facultatif, Valeurs de liste à filtrer (spécifique BioAlternatives, format chaîne "val_id_1, val_id2, val_id3...")
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_category_filters_add( $cat_id, $fld_id, $depth=0, $vals=false ){
	global $config;

	if( !is_numeric($cat_id) || $cat_id<0 ) return false;

	$rfld = fld_fields_get( $fld_id );
	if( $rfld===false || !ria_mysql_num_rows($rfld) ) return false;
	$fld = ria_mysql_fetch_array($rfld);

	if( $fld['cls_id']!=CLS_PRODUCT ) return false;

	if( prd_category_filters_exists($cat_id, $fld_id) ){
		if( $depth==0 && $vals==false ) return true;
		else return false;
	}
	if( !is_numeric($depth) || $depth<0 ) return false;

	if( $vals!=false ){

		// Contrôle que les valeurs de restriction sont des identifiants valides pour ce champ
		$val = explode( ', ',$vals );
		if( !sizeof($val) )
			$vals = false;
		else{
			foreach( $val as $v ){
				$rval = fld_restricted_values_get( $v,$fld_id );
				if( $rval==false || !ria_mysql_num_rows($rval) ) return false;
			}
		}

	}

	$fields = array();
	$values = array();

	$fields[] = 'cft_tnt_id';
	$values[] = $config['tnt_id'];
	$fields[] = 'cft_cat_id';
	$values[] = $cat_id;
	$fields[] = 'cft_fld_id';
	$values[] = $fld_id;
	$fields[] = 'cft_depth';
	$values[] = $depth;
	if( $vals!=false){
		$fields[] = 'cft_values';
		$values[] = '\''.addslashes($vals).'\'';
	}

	$sql = 'insert into prd_category_filters ('.implode( ',',$fields ).') values('.implode( ',',$values ).')';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification de l'existance d'un couple catégorie/champ avancé
 *	@param int $cat_id Identifiant de la catégorie (ne peut être vide)
 *	@param int $fld_id Identifiant du champs (ne peut être vide)
 *	@return bool true si l'association existe, false dans le cas contraire
 */
function prd_category_filters_exists( $cat_id, $fld_id ){
	global $config;

	if( !is_numeric($cat_id) || $cat_id<0 ) return false;
	if( !is_numeric($fld_id) || $fld_id<0 ) return false;

	return ria_mysql_num_rows(ria_mysql_query('select 1 from prd_category_filters where cft_tnt_id='.$config['tnt_id'].' and cft_cat_id='.$cat_id.' and cft_fld_id='.$fld_id));
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'une association catégorie/champ avancé
 *	@param int $cat_id Identifiant de la catégorie (ne peut être vide)
 *	@param int $fld_id Identifiant du champs (ne peut être vide)
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_category_filters_del( $cat_id, $fld_id ){
	global $config;

	if( !is_numeric($cat_id) || $cat_id<0 ) return false;
	if( !is_numeric($fld_id) || $fld_id<0 ) return false;

	return ria_mysql_query('delete from prd_category_filters where cft_tnt_id='.$config['tnt_id'].' and cft_cat_id='.$cat_id.' and cft_fld_id='.$fld_id);
}
// \endcond

// \cond onlyria
/** Cette fonction permet le chargement d'un ou plusieurs filtres de catégories de produits
 *	@param int $cat_id Identifiant de la catégorie (ne peut être vide)
 *	@param int $fld_id Identifiant du champs (ne peut être vide)
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- cat_id : identifiant de la catégorie de produits
 *			- cat_name : désignation de la catégorie
 *			- fld_id : identifiant du champ avancé
 *			- fld_name : désignation du champ avancé
 *			- fld_type_id : type de champ
 *			- depth : ordre de filtrage (spécifique BioAlternatives)
 *			- vals : valeurs de filtrage (spécifique BioAlternatives)
 *
 */
function prd_category_filters_get( $cat_id=0, $fld_id=0 ){
	global $config;

	if( !is_numeric($cat_id) || $cat_id<0 ) return false;
	if( !is_numeric($fld_id) || $fld_id<0 ) return false;

	$sql = '
		select
			cat_id, cat_name, fld_id, fld_name, fld_type_id, cft_depth as depth, cft_values as vals, unit_id, unit_symbol, unit_name
		from
			prd_category_filters
			join prd_categories
				on ( cft_cat_id=cat_id and cft_tnt_id=cat_tnt_id )
			join fld_fields
				on ( ( fld_tnt_id=0 or fld_tnt_id='.$config['tnt_id'].' ) and cft_fld_id=fld_id )
			left join fld_units
				on ( fld_unit_id=unit_id and unit_tnt_id='.$config['tnt_id'].' )
		where cft_tnt_id='.$config['tnt_id'].' and cat_date_deleted is null and fld_date_deleted is null and fld_cls_id='.CLS_PRODUCT.'
	';

	if( $cat_id>0 ) $sql .= ' and cat_id='.$cat_id;
	if( $fld_id>0 ) $sql .= ' and fld_id='.$fld_id;

	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer pour un filtre d'une catégorie les valeurs qui lui sont rattachées.
 *
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie
 *	@param int $fld_id Obligatoire, identifiant d'un filtre
 *	@param bool $recursive_cat Optionnel, si oui ou non on récupère les produits des sous catégories
 *	@param bool $published  Optionnel, par défaut seul les valeurs des catégories publiées seront retournées, mettre false pour toutes les avoir
 *	@param $products Optionnel, identifiant ou tableau d'identifiants de produit
 *	@param $field_in_childs Facultatif, permet d'inclure les valeurs contenues sur les articles enfants rattachés, mettre true pour activer cette option
 *	@param bool $exclude_childonly Facultatif, si true, exclue les articles "article lié seulement" du count
 *
 *	@return array Un tableau associatif sous cette forme :  array(valeur => label, valeur => label) où valeur correspond à la donnée à utiliser pour récupérer des produits
 */
function prd_category_filters_get_values( $cat_id, $fld_id, $recursive_cat=false, $published=true, $products=false, $field_in_childs=false, $exclude_childonly=false ){
	global $config;

	$cat_id = control_array_integer( $cat_id, true );
	if( $cat_id === false ){
		return false;
	}

	if( !is_numeric($fld_id) || $fld_id<=0 ){
		return false;
	}

	if( $products!==false ){
		if( !is_array($products) ){
			if( !is_numeric($products) || $products<=0 ){
				return false;
			}else{
				$products = array( $products );
			}
		}else{
			foreach( $products as $p ){
				if( !is_numeric($p) || $p<=0 ){
					return false;
				}
			}
		}
	}

	$ar_cats = array( $cat_id );
	if( $recursive_cat ){
		$catchilds = prd_categories_childs_get_array( $cat_id, $published );
		if( is_array($catchilds) && sizeof($catchilds) ){
			$ar_cats = array_merge( $ar_cats, $catchilds );
		}
	}

	$ar_values = array();
	$type = fld_fields_get_type( $fld_id );

	if (is_array($products) && sizeof($products)) {
		$rvals = fld_fields_get_values( $fld_id, 0, false, false, $exclude_childonly, false, $products, true );
	}else{
		$rvals = fld_fields_get_values( $fld_id, $cat_id, $published, $recursive_cat, $exclude_childonly, true, $products, true );
	}

	if( $rvals ){
		while( $vals = ria_mysql_fetch_array($rvals) ){
			switch( $type ){
				case FLD_TYPE_SELECT:
				case FLD_TYPE_SELECT_MULTIPLE: {
					$values = explode( ', ', $vals['value'] );

					foreach( $values as $one_val ){
						$ar_values[] = array(
							'id' 	=> $one_val,
							'name' 	=> $one_val,
							'pos'	=> 0,
							'count'	=> $vals['count'],
							'ids' => isset($vals['ids']) ? $vals['ids'] : '',
						);
					}
					break;
				}
				case FLD_TYPE_SELECT_HIERARCHY: {
					$values = explode( ', ', $vals['value'] );

					foreach( $values as $one_val ){
						$name = fld_restricted_values_get_name( $one_val );

						if( trim($name)!='' ){
							$ar_values[] = array(
								'id' 	=> $one_val,
								'name' 	=> $name,
								'pos'	=> fld_restricted_values_get_pos( $one_val ),
								'count'	=> $vals['count'],
								'ids' => isset($vals['ids']) ? $vals['ids'] : '',
							);
						}
					}

					break;
				}
				default: {
					$ar_values[] = array(
						'id' 	=> $vals['value'],
						'name' 	=> $vals['value'],
						'pos'	=> 0,
						'count'	=> $vals['count'],
						'ids' => isset($vals['ids']) ? $vals['ids'] : '',
					);
					break;
				}
			}
		}
	}

	if( $field_in_childs ){
		// Récupère les articles enfants seulement des articles classés dans la catégorie donnée
		$ar_prd_ids = array();
		if( is_array($products) && sizeof($products) ){
			$ar_prd_ids = $products;
		}else{
			$r_prd = prd_products_get_simple( 0, '', $published, $cat_id, $recursive_cat, false, false, false, ['only_prd_ids' => true] );
			if( $r_prd ){
				while( $prd = ria_mysql_fetch_assoc($r_prd) ){
					$ar_prd_ids[] = $prd['id'];
				}
			}
		}

		if( sizeof($ar_prd_ids) ){
			$ar_childs = array();

			$r_childs = prd_products_get_simple( 0, '', $published, 0, false, false, false, false, array('parent' => $ar_prd_ids, 'childs' => true, 'only_prd_ids' => true) );
			if( $r_childs ){
				while( $child = ria_mysql_fetch_array($r_childs) ){
					$ar_childs[] = $child['id'];
				}
			}

			if( sizeof($ar_childs) ){
				$rvals = fld_fields_get_values( $fld_id, 0, $published, false, false, true, $ar_childs, true );
				if( $rvals ){
					while( $vals = ria_mysql_fetch_array($rvals) ){
						if( $exclude_childonly && isset($vals['ids']) && $vals['ids'] != '' ){
							$ids = explode( ',', $vals['ids'] );

							$res = ria_mysql_query('
								select prd_parent_id, prd_child_id
								from prd_hierarchy
								where prd_tnt_id = '.$config['tnt_id'].'
									and prd_child_id in ('.implode( ', ', $ids ).')
							');

							if( $res ){
								while( $r = ria_mysql_fetch_assoc($res) ){
									if( !in_array($r['prd_parent_id'], $ids) ){
										$ids[] = $r['prd_parent_id'];
									}

									$key = array_search( $r['prd_child_id'], $ids );
									if( $key !== false ){
										unset($ids[ $key ]);
									}
								}
							}

							$vals['ids'] = implode(',', $ids);
							$vals['count'] = count($ids);
						}

						switch( $type ){
							case FLD_TYPE_SELECT:
							case FLD_TYPE_SELECT_MULTIPLE: {
								$values = explode( ', ', $vals['value'] );
								foreach( $values as $one_val ){
									$ar_values[] = array(
										'id' 	=> $one_val,
										'name' 	=> $one_val,
										'pos'	=> 0,
										'count'	=> $vals['count'],
										'ids' => isset($vals['ids']) ? $vals['ids'] : '',
									);
								}
								break;
							}
							case FLD_TYPE_SELECT_HIERARCHY: {
								$values = explode( ', ', $vals['value'] );

								foreach( $values as $one_val ){
									$name = fld_restricted_values_get_name( $one_val );

									if( trim($name)!='' ){
										$ar_values[] = array(
											'id' 	=> $one_val,
											'name' 	=> $name,
											'pos'	=> fld_restricted_values_get_pos( $one_val ),
											'count'	=> $vals['count'],
											'ids' => isset($vals['ids']) ? $vals['ids'] : '',
										);
									}
								}

								break;
							}
							default: {
								$ar_values[] = array(
									'id' 	=> $vals['value'],
									'name' 	=> $vals['value'],
									'pos'	=> 0,
									'count'	=> $vals['count'],
									'ids' => isset($vals['ids']) ? $vals['ids'] : '',
								);
								break;
							}
						}
					}
				}
			}
		}
	}

	// Création du tableau final des filtres
	$ar_final = [];
	foreach( $ar_values as $key => $data ){
		// On rend unique chaque valeur
		if( !array_key_exists($data['id'], $ar_final) ){
			$ar_final[ $data['id'] ] = $data;
		}else{
			// On complète pour chaque valeur le nombre d'objet lié ainsi que l'identifiant des produits
			$ar_final[ $data['id'] ]['count'] += $data['count'];

			$current_ids = explode( ',', $ar_final[$data['id']]['ids'] );
			$new_ids = explode( ',', $data['ids'] );

			$ar_final[ $data['id'] ]['ids'] = implode( ',', array_merge( $current_ids, $new_ids ) );
		}
	}

	return array_msort( $ar_final, array( 'pos' => SORT_ASC, 'name' => SORT_ASC ) );

	return $ar_values;
}
// \endcond

/** Cette fonction permet de récupérer un tableau contenant les filtres et leurs valeurs pour une catégorie de produits donnée.
 *	Le résultat est retourné sous forme de tableau associatif, en voici un exemple :
 *	\code{.php}
 *			Array (
 *				[brand] => Array (
 *					[name] => Marque
 *					[values] => Array (
 *						[0] => Array (
 *							[id] => BRD_ID
 *							[name] => BRD_TITLE
 *						)
 *					)
 *				)
 *				[fld-FLD_ID] => Array(
 *					[name] => FLD_NAME
 *					[values] => Array (
 *						[0] => Array (
 *							[id] => VAL_ID
 *							[name] => VAL_NAME
 *						)
 *					)
 *				)
 *			)
 *	\endcode
 *	Le résultat de cette fonction est mis en cache pour une heure.
 *
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie
 *	@param bool $val_recursive Optionnel, par défaut on récupérer les valeurs rattachées aux produits des catégories enfant
 *	@param bool $published  Optionnel, par défaut seul les valeurs des catégories publiées seront retournées, mettre false pour toutes les avoir
 *	@param resource|array $products Optionnel, un résulat de prd_products_get() ou directement un tableau d'identifiants produit
 *	@param bool $field_in_childs Facultatif, permet d'inclure les valeurs contenues sur les articles enfants rattachés, mettre true pour activer cette option
 *	@param bool $always_brand Optionnel, permet de toujours activer le filtre par marque (par défaut à False)
 *	@param bool $get_parent Optionnel, permet d'utiliser la hiérarchie des catégories pour récupérer les filtres du parent lors que la catégorie ne dispose pas de filtre (par défaut à False)
 *	@param bool $no_control_prd Optionnel, permet de ne pas restreindre les valeurs d'un filtre en fonction du paramètre $products (passer un tableau contenant l'identifiant du champ ou bien 'brand')
 * 	@param bool $with_ids Optionnel, permet de retourner pour chaque valeur un alias "ids" contenant les identifiants des produits liés à cette valeur séparé par une "," (par défaut désactivé)
 *	@param bool $exclude_childonly Facultatif, si true, exclue les articles "article enfant seulement" du count
 *	@param bool $always_stock Optionnel, permet de toujours activer le filtre par disponibilité (par défaut à False)
 *	@param bool $always_catchilds Optionnel, permet de toujours activer le filtre par famille (par défaut à False)
 *
 *	@return array Un tableau associatif
 */
function prd_category_filters_get_bycat( $cat_id, $val_recursive=true, $published=true, $products=false, $field_in_childs=false, $always_brand=false, $get_parent=false, $no_control_prd=array(), $with_ids=false, $exclude_childonly=false, $always_stock=false, $always_catchilds=false ){
	global $config, $memcached;

	// Contrôle le paramètre $cat_id
	$ar_cats_ids = control_array_integer( $cat_id, true );
	if( $ar_cats_ids === false ){
		return false;
	}

	if( !is_array($no_control_prd) ){
		$no_control_prd = array();
	}

	// Construit le tableau des $ar_prds qui contient la liste des produits à considérer
	$ar_prds = false;
	if( is_array($products) && sizeof($products) ){
		$ar_prds = $products;
	}else{
		if( $products && ria_mysql_num_rows($products) ){
			while( $prd = ria_mysql_fetch_array($products) ){
				$ar_prds[] = $prd['id'];
			}

			ria_mysql_data_seek( $products, 0 );
		}
	}
	if( $ar_prds ){
		sort( $ar_prds );
	}

	// Détermine la langue en cours d'utilisation
	$lng = i18n::getLang();

	// Calcule la clé memcached
	$ar_key = array(
		'ar_cats_ids' => $ar_cats_ids,
		'val_recursive' => $val_recursive,
		'published' => $published,
		'ar_prds' => $ar_prds,
		'field_in_childs' => $field_in_childs,
		'always_brand' => $always_brand,
		'get_parent' => $get_parent,
		'lng' => $lng,
		'no_control_prd' => $no_control_prd,
		'always_catchilds' => $always_catchilds
	);
	$mkey = 'tnt:'.$config['tnt_id'].':v10:prd_category_filters_get_bycat:'.md5( serialize($ar_key) );

	// Si le résultat existe déjà en cache, retourne le résultat en cache
	$mget = $memcached->get( $mkey );
	if( $mget && $mget!='' ){
		return $mget;
	}

	$ar_filters 	= array();
	$ar_unique_brd 	= array();


	$ar_filters_data = array();

	// Filtre sur la marque
	$is_filter_brand = $always_brand ? 'Oui' : 'Non';

	if( !$always_brand ){
		foreach( $ar_cats_ids as $cat_id ){
			$is_filter_brand = fld_object_values_get( $cat_id, _FLD_CAT_FILTER_BRD );

			if( $is_filter_brand == 'Oui' ){
				break;
			}

			if( $get_parent ){
				$ar_parent = prd_categories_parents_get_array( $cat_id, false, 'desc' );
				if( is_array($ar_parent) ){
					foreach( $ar_parent as $one_parent ){
						$is_filter_brand = fld_object_values_get( $one_parent, _FLD_CAT_FILTER_BRD );

						if( $is_filter_brand == 'Oui' ){
							break(2);
						}
					}
				}
			}
		}
	}

	if( $is_filter_brand=='Oui' ){
		$rbrand = prd_brands_get( 0, true, '', '', false, null, false, false, false, false, (in_array('brand', $no_control_prd) ? false : $ar_prds), $ar_cats_ids, true );

		if( $rbrand && ria_mysql_num_rows($rbrand) ){
			$ar_filters['brand'] = array(
				'name' => i18n::get('Marque'),
				'values' => array()
			);

			$key_filter_brands = 0;
			while( $brand = ria_mysql_fetch_assoc($rbrand) ){
				if( $lng != $config['i18n_lng'] ){
					$brand = i18n::getTranslation( CLS_BRAND, $brand );
				}

				$ar_unique_brd[] = $brand['id'];

				$ar_filters['brand']['values'][ $key_filter_brands ] = array(
					'id' => $brand['id'],
					'name' => $brand['title'],
				);

				if( $with_ids ){
					$temp_prd = 0;
					$temp_prd_ids = [];

					if( !in_array('brand', $no_control_prd) ){
						if( is_array($ar_prds) && count($ar_prds) ){
							$temp_prd = $ar_prds;
						}
					}

					$r_product = prd_products_get_simple($temp_prd, '', true, $ar_cats_ids, true, false, false, false, ['brand' => $brand['id'], 'only_prd_ids' => true]);
					if( $r_product ){
						while( $product = ria_mysql_fetch_assoc($r_product) ){
							$temp_prd_ids[] = $product['id'];
						}
					}

					$ar_filters['brand']['values'][ $key_filter_brands ]['count'] = count($temp_prd_ids);
					$ar_filters['brand']['values'][ $key_filter_brands ]['ids'] = implode( ',', $temp_prd_ids );
				}

				$key_filter_brands++;
			}
		}

		if( $field_in_childs ){
			// Récupère les articles enfants seulement des articles classés dans la catégorie donnée
			$ar_prd_ids = array();
			if( is_array($products) && sizeof($products) && !in_array('brand', $no_control_prd) ){
				$ar_prd_ids = $products;
			}else{
				$r_prd = prd_products_get_simple( 0, '', $published, $ar_cats_ids, $val_recursive, false, false, false, ['only_prd_ids' => true] );
				if( $r_prd ){
					while( $prd = ria_mysql_fetch_assoc($r_prd) ){
						$ar_prd_ids[] = $prd['id'];
					}
				}
			}

			if( sizeof($ar_prd_ids) ){
				$ar_childs = array();

				$r_childs = prd_products_get_simple( 0, '', $published, 0, false, false, false, false, array('parent' => $ar_prd_ids, 'childs' => true, 'only_prd_ids' => true) );
				if( $r_childs ){
					while( $child = ria_mysql_fetch_array($r_childs) ){
						$ar_childs[] = $child['id'];
					}
				}

				if( sizeof($ar_childs) ){
					$rbrand = prd_brands_get( 0, true, '', '', false, null, false, false, false, false, $ar_childs );

					if( $rbrand && ria_mysql_num_rows($rbrand) ){
						while( $brand = ria_mysql_fetch_assoc($rbrand) ){
							if( !in_array($brand['id'], $ar_unique_brd) ){
								if( !array_key_exists('brand', $ar_filters) ){
									$ar_filters['brand'] = array(
										'name' => i18n::get('Marque'),
										'values' => array()
									);
								}

								if( $lng != $config['i18n_lng'] ){
									$brand = i18n::getTranslation( CLS_BRAND, $brand );
								}

								$ar_filters['brand']['values'][] = array(
									'id' => $brand['id'],
									'name' => $brand['title']
								);
							}
						}
					}
				}
			}
		}
	}

	// Filtre sur la disponibilité
	$is_filter_stock = $always_stock ? 'Oui' : 'Non';

	if( !$always_stock ){
		foreach( $ar_cats_ids as $cat_id ){
			$is_filter_stock = fld_object_values_get( $cat_id, _FLD_CAT_FILTER_STOCK );

			if( $is_filter_stock == 'Oui' ){
				break;
			}

			if( $get_parent ){
				$ar_parent = prd_categories_parents_get_array( $cat_id, false, 'desc' );
				if( is_array($ar_parent) ){
					foreach( $ar_parent as $one_parent ){
						$is_filter_stock = fld_object_values_get( $one_parent, _FLD_CAT_FILTER_STOCK );

						if( $is_filter_stock == 'Oui' ){
							break(2);
						}
					}
				}
			}
		}
	}

	if( $is_filter_stock=='Oui' ){

		$ar_filters['availability'] = array(
			'name' => i18n::get('Disponibilité'),
			'values' => array(
				array( 'id'=>1, 'name' => i18n::get('En stock') ),
				array( 'id'=>2, 'name' => i18n::get('Prochainement disponible') ),
			)
		);

		if( $with_ids ){
			$r_product = prd_products_get_simple( 0, '', true, $ar_cats_ids, true, false, false, false, ['have_stock' => true, 'only_prd_ids' => true]);
			$ar_filters['availability']['values'][ 0 ]['count'] = ria_mysql_num_rows($r_product);

			$r_product = prd_products_get_simple( 0, '', true, $ar_cats_ids, true, false, false, false, ['ordered' => true, 'have_stock' => false, 'only_prd_ids' => true]);
			$ar_filters['availability']['values'][ 1 ]['count'] = ria_mysql_num_rows($r_product);
		}

	}

	// Filtre sur les sous-catégories
	$is_filter_catchidren = $always_catchilds ? 'Oui' : 'Non';

	if( !$always_catchilds ){
		foreach( $ar_cats_ids as $cat_id ){
			$is_filter_catchidren = fld_object_values_get( $cat_id, _FLD_CAT_FILTER_CATCHILDREN );

			if( $is_filter_catchidren == 'Oui' ){
				break;
			}

			if( $get_parent ){
				$ar_parent = prd_categories_parents_get_array( $cat_id, false, 'desc' );
				if( is_array($ar_parent) ){
					foreach( $ar_parent as $one_parent ){
						$is_filter_catchidren = fld_object_values_get( $one_parent, _FLD_CAT_FILTER_CATCHILDREN );

						if( $is_filter_catchidren == 'Oui' ){
							break(2);
						}
					}
				}
			}
		}
	}


	if( $is_filter_catchidren=='Oui' ){
		$ar_unique_child = [];
		$key_filter_catchilds = 0;

		foreach( $ar_cats_ids as $one_cat ){
			$r_child =  prd_categories_get(
				0, true, $one_cat, '', false, false, null, false, [],
				$recursive_from_parent=false, false, false, false, false, false,
				false, false, false, null, false, false,
				false,  false, 0, false, false, $ar_prds === false ? 0 : $ar_prds
			);

			if( $r_child && ria_mysql_num_rows($r_child) ){
				if( !isset($ar_filters['catchilds']) ){
					$ar_filters['catchilds'] = array(
						'name' => i18n::get('Famille'),
						'values' => array()
					);
				}

				while( $child = ria_mysql_fetch_assoc($r_child) ){
					if( in_array($child['id'], $ar_unique_child) ){
						continue;
					}

					if( $lng != $config['i18n_lng'] ){
						$child = i18n::getTranslation( CLS_CATEGORY, $child );
					}

					$ar_unique_child[] = $child['id'];

					$ar_filters['catchilds']['values'][ $key_filter_catchilds ] = array(
						'id' => $child['id'],
						'name' => $child['title'],
					);

					if( $with_ids ){
						$temp_prd = 0;
						$temp_prd_ids = [];

						if( !in_array('catchilds', $no_control_prd) ){
							if( is_array($ar_prds) && count($ar_prds) ){
								$temp_prd = $ar_prds;
							}
						}

						$r_product = prd_products_get_simple( $temp_prd, '', true, $child['id'], true, false, false, false, ['only_prd_ids' => true] );
						if( $r_product ){
							while( $product = ria_mysql_fetch_assoc($r_product) ){
								$temp_prd_ids[] = $product['id'];
							}
						}

						$ar_filters['catchilds']['values'][ $key_filter_catchilds ]['count'] = count($temp_prd_ids);
						$ar_filters['catchilds']['values'][ $key_filter_catchilds ]['ids'] = implode( ',', $temp_prd_ids );
					}

					$ar_filters2 = [];
					$ar_unique_child2 = [];
					$key_filter_catchilds2 = 0;

					$r_child2 =  prd_categories_get(
						0, true, $child['id'], '', false, false, null, false, [],
						$recursive_from_parent=false, false, false, false, false, false,
						false, false, false, null, false, false,
						false,  false, 0, false, false, $ar_prds === false ? 0 : $ar_prds
					);

					if( $r_child2 && ria_mysql_num_rows($r_child2) ){
						while( $child2 = ria_mysql_fetch_assoc($r_child2) ){
							if( in_array($child2['id'], $ar_unique_child2) ){
								continue;
							}

							if( $lng != $config['i18n_lng'] ){
								$child2 = i18n::getTranslation( CLS_CATEGORY, $child2 );
							}

							$ar_unique_child2[] = $child2['id'];

							$ar_filters2[ $key_filter_catchilds2 ] = array(
								'id' => $child2['id'],
								'name' => $child2['title'],
							);

							if( $with_ids ){
								$temp_prd = 0;
								$temp_prd_ids = [];

								if( !in_array('catchilds', $no_control_prd) ){
									if( is_array($ar_prds) && count($ar_prds) ){
										$temp_prd = $ar_prds;
									}
								}

								$r_product = prd_products_get_simple( $temp_prd, '', true, $child2['id'], true, false, false, false, ['only_prd_ids' => true] );
								if( $r_product ){
									while( $product = ria_mysql_fetch_assoc($r_product) ){
										$temp_prd_ids[] = $product['id'];
									}
								}

								$ar_filters2[ $key_filter_catchilds2 ]['count'] = count($temp_prd_ids);
								$ar_filters2[ $key_filter_catchilds2 ]['ids'] = implode( ',', $temp_prd_ids );
							}

							$ar_filters3 = [];
							$ar_unique_child3 = [];
							$key_filter_catchilds3 = 0;

							$r_child3 =  prd_categories_get(
								0, true, $child2['id'], '', false, false, null, false, [],
								$recursive_from_parent=false, false, false, false, false, false,
								false, false, false, null, false, false,
								false,  false, 0, false, false, $ar_prds === false ? 0 : $ar_prds
							);

							if( $r_child3 && ria_mysql_num_rows($r_child3) ){
								while( $child3 = ria_mysql_fetch_assoc($r_child3) ){
									if( in_array($child3['id'], $ar_unique_child3) ){
										continue;
									}

									if( $lng != $config['i18n_lng'] ){
										$child3 = i18n::getTranslation( CLS_CATEGORY, $child3 );
									}

									$ar_unique_child3[] = $child3['id'];

									$ar_filters3[ $key_filter_catchilds3 ] = array(
										'id' => $child3['id'],
										'name' => $child3['title'],
									);

									if( $with_ids ){
										$temp_prd = 0;
										$temp_prd_ids = [];

										if( !in_array('catchilds', $no_control_prd) ){
											if( is_array($ar_prds) && count($ar_prds) ){
												$temp_prd = $ar_prds;
											}
										}

										$r_product = prd_products_get_simple( $temp_prd, '', true, $child3['id'], true, false, false, false, ['only_prd_ids' => true] );
										if( $r_product ){
											while( $product = ria_mysql_fetch_assoc($r_product) ){
												$temp_prd_ids[] = $product['id'];
											}
										}

										$ar_filters3[ $key_filter_catchilds3 ]['count'] = count($temp_prd_ids);
										$ar_filters3[ $key_filter_catchilds3 ]['ids'] = implode( ',', $temp_prd_ids );
									}

									$key_filter_catchilds3++;
								}

								$ar_filters2[ $key_filter_catchilds2 ]['childs'] = $ar_filters3;
							}

							$key_filter_catchilds2++;
						}
					}

					$ar_filters['catchilds']['values'][ $key_filter_catchilds ]['childs'] = $ar_filters2;

					$key_filter_catchilds++;
				}
			}
		}
	}

	// Tous les autres filtres
	foreach( $ar_cats_ids as $cat_id ){
		$rfilter = prd_category_filters_get( $cat_id );

		if( $get_parent && (!$rfilter || !ria_mysql_num_rows($rfilter)) ){
			$ar_parent = prd_categories_parents_get_array( $cat_id, false, 'desc' );
			if( is_array($ar_parent) ){
				foreach( $ar_parent as $one_parent ){
					$rfilter = prd_category_filters_get( $one_parent );

					if( $rfilter && ria_mysql_num_rows($rfilter) ){
						break;
					}
				}
			}
		}

		if( $rfilter && ria_mysql_num_rows($rfilter) ){
			while( $filter = ria_mysql_fetch_assoc($rfilter) ){
				$ar_filters_data[ $filter['fld_id'] ] = $filter;
			}
		}
	}

	if( sizeof($ar_filters_data) ){

		foreach( $ar_filters_data as $filter ){
			$values = prd_category_filters_get_values( $ar_cats_ids, $filter['fld_id'], $val_recursive, $published, (in_array($filter['fld_id'], $no_control_prd) ? false : $ar_prds), $field_in_childs, $exclude_childonly );

			if( is_array($values) && sizeof($values) ){
				$ar_filters[ 'fld['.$filter['fld_id'].']' ] = array(
					'name' => i18n::get( $filter['fld_name'] ),
					'values' => $values
				);
			}
		}
	}

	// Stocke le résultat dans memcached pour usage ultérieur
	$memcached->set( $mkey, $ar_filters, 60 * 60 );

	return $ar_filters;
}

// \cond onlyria
/** Met à jour un filtre de catégorie
 *	Cette fonction n'est utile que pour BioAlternatives
 *	@param int $cat_id Obligatoire, identifiant de la catégorie
 *	@param int $fld_id Obligatoire, identifiant du champ libre
 *	@param int $depth Obligatoire, ordre de filtrage
 *	@param array $values Facultatif, valeurs de filtrage
 *	@return bool True si succès, False sinon
 */
function prd_category_filters_upd( $cat_id, $fld_id, $depth, $values=false ){
	global $config;

	if( !prd_category_filters_exists($cat_id, $fld_id) ) return false;
	if( !is_numeric($depth) || $depth<0 ) return false;
	if( $values!=false ){

		// Contrôle que les valeurs de restriction sont des identifiants valides pour ce champ
		$val = explode( ', ',$values );
		if( !sizeof($val) )
			$values = false;
		else{
			foreach( $val as $v ){
				$rval = fld_restricted_values_get( $v,$fld_id );
				if( $rval==false || !ria_mysql_num_rows($rval) ) return false;
			}
		}

	}

	return ria_mysql_query('
		update
			prd_category_filters
		set
			cft_depth='.$depth.',
			cft_values='.( $values==false ? 'NULL' : '\''.addslashes( $values ).'\'' ).'
		where
			cft_tnt_id='.$config['tnt_id'].' and
			cft_cat_id='.$cat_id.' and
			cft_fld_id='.$fld_id
	);

}
// \endcond

/** Cette fonction permet de vérifier qu'un ou plusieurs produits données en paramètres utilise la valeur d'un champ avancé
 *	@param int $prd Obligatoire, identifiant ou tableau d'identifiants de produit
 *	@param $fld Obligatoire, identifiant du champ avancé
 *	@param $val Obligatoire, valeur qui serait utilisée
 *	@param string $lng Optionnel, code ISO d'une langue par défaut on utilise la langue par défaut
 *
 *	@return Le nombre de produit utilisant une valeur, False dans le cas contraire
 */
function prd_category_filters_values_get_used( $prd, $fld, $val, $lng='' ){
	global $config, $memcached;

	$prd = control_array_integer( $prd, true );
	if( $prd === false ){
		return false;
	}
	sort( $prd );

	if( !is_numeric($fld) || $fld<=0 ){
	    return false;
	}

	if( trim($val) == '' ){
		return false;
	}

	if( !in_array($lng, $config['i18n_lng_used']) ){
		$lng = $config['i18n_lng'];
	}

	// Calcule la clé memcached
	$ar_key = array(
		'prd' => implode( ',', $prd ),
		'fld' => $fld,
		'val' => $val,
		'lng' => $lng
	);
	$mkey = 'tnt:'.$config['tnt_id'].':prd_category_filters_values_get_used:'.md5( serialize($ar_key) );

	// Si le résultat existe déjà en cache, retourne le résultat en cache
	$mget = $memcached->get( $mkey );
	if( $mget && $mget!='' ){
		return $mget;
	}

	// Interroge la base de données et charge le résultat
	$res = ria_mysql_query('
		select 1 from fld_object_values
		where pv_tnt_id = '.$config['tnt_id'].'
			and pv_fld_id = '.$fld.'
			and pv_obj_id_0 in ('.implode(', ', $prd).')
			and (
				pv_value = "'.addslashes( $val ).'"
				or pv_value like "'.addslashes( $val ).',%"
				or pv_value like "%, '.addslashes( $val ).',%"
				or pv_value like "%, '.addslashes( $val ).'"
			)
			and pv_lng_code = "'.( trim($lng) != '' ? $lng : $config['i18n_lng'] ).'"
	');

	if( !$res ){
		return false;
	}

	$count = ria_mysql_num_rows( $res );

	// Stocke le résultat dans memcached pour usage ultérieur
	$memcached->set( $mkey, $count, 60 * 60 );

	return $count;
}

/// @}

