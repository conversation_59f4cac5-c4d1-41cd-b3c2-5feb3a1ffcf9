<?php

/**	\file popup-add-fast.php
 * 
 * 	Ce fichier permet l'ajout rapide de produits pour constituer un assortiment destiné à faire l'objet d'un relevé de linéaires.
 * 
 */

// Vérifie que l'utilisateur en cours peut accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_LINEAR_RAISED_CONFIG');

// Défini le titre de la page
define('ADMIN_PAGE_TITLE', _('Ajout rapide'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');
require_once('admin/skin/header.inc.php');

?>

<p>
	<?php print _('Indiquez, sur des lignes différentes, les références des articles. Il est possible d\'ajouter un (ou de plusieurs) espace(s) puis le PMC et le rang. Par exemple :'); ?>
</p>
<pre>
Rang Référence PMC
3    6190513   3
1    6430001   0
2    6531713
</pre>

<div id="result" style="display:none;">
	<h3 class="no-border"><?php print _('Résultat de l\'analyse'); ?></h3>
	<table id="result-table">
		<thead>
			<tr>
				<th id="prd-line" class="line align-left"><?php print _('Ligne'); ?></th>
				<th id="prd-ref" class="align-left">
					<?php print _('Référence - Désignation'); ?>
				</th>
				<th id="prd-name" class="align-right"><?php print _('PMC'); ?></th>
				<th id="prd-qte" class="numeric align-right"><?php print _('Rang'); ?></th>
			</tr>
		</thead>
		<tbody>
		</tbody>
	</table>
	<form id="validation" style="display:none;">
		<input type="hidden" name="json" id="json">
		<input type="submit" value="<?php echo _('Valider l\'ajout des produits valides')?>">
	</form>
</div>
<br>
<br>

<form id="batch-product" method="post">
	<textarea name="batch" id="batch" cols="92" rows="10"><?php print isset($_POST['products']) ? htmlspecialchars($_POST['products']): '' ?></textarea>
	<div class="actions">
		<input type="submit" value="<?php print _('Analyser'); ?>" name="analyze" class="btn-action"/>
	</div>
</form>
<p class="tip">
	<?php print _('Vous pouvez préparer la liste des produits dans un tableur comme Microsoft Excel ® ou OpenOffice.org, puis copier-coller ci-dessus la liste désirée.'); ?>
</p>
<script>
	$('#batch-product').on('submit', function(e) {
		$('div.error').remove();
		$('#result').hide();
		$.ajax({
			url: '/admin/ajax/catalog/linear-raised/index.php?action=analyse',
			data: new FormData(e.target),
			processData: false,
  			contentType: false,
			type: 'post',
		}).done(function(json){
			if (json.result) {
				var hasErrors = false;

				$('#result').show();
				$('#result-table tbody tr').remove();
				$.each(json.content, function(i, line){
					var tr = $('<tr>');

					if (line.type === 'error')  {
						tr.addClass('error');
						tr.append($('<td>').text(i + 1));
						tr.append($('<td>').attr('colspan', 3).text(line.message));

						hasErrors = true;
					}else{
						tr.addClass('success');
						tr.append($('<td>').text(i + 1).addClass('line'));
						tr.append($('<td>').text(line.name));
						tr.append($('<td>').text(line.pmc).addClass('align-right'));
						tr.append($('<td>').text(line.rank).addClass('align-right'));
					}

					$('#result-table tbody').append(tr);
				});

				$('#validation').show();
				$('#validation input[type=submit]').prop('disabled', hasErrors);
				$('#json').val(JSON.stringify(json.content));
			} else if (json.message.trim()) {
				$('#batch-product').prepend(
					$('<div>').addClass('error').text(json.message)
				);
			}

		}).fail(function(err) {
			console.log(err);
		});

		return false;
	});
</script>
<?php

require_once('admin/skin/footer.inc.php');