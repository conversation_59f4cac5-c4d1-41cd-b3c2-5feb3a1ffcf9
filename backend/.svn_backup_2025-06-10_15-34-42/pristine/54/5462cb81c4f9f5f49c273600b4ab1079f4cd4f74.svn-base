build: false
version: dev-{build}
shallow_clone: false
clone_folder: C:\projects\php-coveralls

environment:
    matrix:
        - php_ver: 7.1.2

cache:
    - '%APPDATA%\Composer'
    - '%LOCALAPPDATA%\Composer'
    - C:\tools\php -> .appveyor.yml
    - C:\tools\composer.phar -> .appveyor.yml

init:
    - SET PATH=C:\tools\php;%PATH%

install:
    - ps: Set-Service wuauserv -StartupType Manual
    - IF NOT EXIST C:\tools\php (choco install --yes --allow-empty-checksums php --version %php_ver% --params '/InstallDir:C:\tools\php')
    - appveyor DownloadFile http://curl.haxx.se/ca/cacert.pem -FileName C:\cacert.pem
    - cd C:\tools\php
    - copy php.ini-production php.ini
    - echo date.timezone=UTC >> php.ini
    - echo memory_limit=512M >> php.ini
    - echo extension_dir=ext >> php.ini
    - echo extension=php_curl.dll >> php.ini
    - echo extension=php_openssl.dll >> php.ini
    - echo curl.cainfo=C:\cacert.pem >> php.ini
    - IF NOT EXIST C:\tools\composer.phar (cd C:\tools && appveyor DownloadFile https://getcomposer.org/download/1.4.1/composer.phar)
    - php C:\tools\composer.phar global show -ND 2>1 | findstr "hirak/prestissimo" || php C:\tools\composer.phar global require hirak/prestissimo

before_test:
    - cd C:\projects\php-coveralls
    - php C:\tools\composer.phar update --no-interaction --no-progress --prefer-stable --no-ansi

test_script:
    - cd C:\projects\php-coveralls
    - phpdbg -qrr vendor\phpunit\phpunit\phpunit --verbose
