<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  421901 => 'Telekom',
  421902 => 'Telekom',
  421903 => 'Telekom',
  421904 => 'Telekom',
  421905 => 'Orange',
  421906 => 'Orange',
  421907 => 'Orange',
  421908 => 'Orange',
  421909 => 'Juro',
  421910 => 'Telekom',
  421911 => 'Telekom',
  421912 => 'Telekom',
  421914 => 'Telekom',
  421915 => 'Orange',
  421916 => 'Orange',
  421917 => 'Orange',
  421918 => 'Orange',
  421919 => 'Orange',
  421940 => 'O2',
  421944 => 'O2',
  421945 => 'Orange',
  421948 => 'O2',
  421949 => 'O2',
  421950 => '4ka of SWAN',
);
