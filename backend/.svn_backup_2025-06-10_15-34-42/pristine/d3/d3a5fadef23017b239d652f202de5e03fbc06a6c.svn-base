<?php

// \cond onlyria
/**	Retourne les produits parents d'un produit donné.
 *	@param int $id Identifiant du produit dont on souhaite obtenir les parents
 *	@param bool $centralized Booléen indiquant si l'on souhaite obtenir tous les produits ou seulement ceux qui sont centralisés
 *	@param bool $published  Booléen indiquant si l'on ne souhaite que les produits publiés ou tous
 *	@param int|array $cat Optionnel, identifiant ou tableau d'identifiants permetant de ne retourner que les parents appartenant à une ou plusieurs catégories données
 */
function prd_parents_get( $id=0, $centralized=false, $published=true, $cat=0 ){
	if( !is_numeric($id) || $id<0 ) return false;
	if( !is_numeric($cat) && !is_array($cat) ) return false;
	if( is_numeric($cat) && $cat<0 ) return false;
	global $config;

	if( is_numeric($cat) && $cat>0 ){
		$cat = array( $cat );
	} elseif( is_array($cat) ){
		if( sizeof($cat) ){
			foreach( $cat as $c ){
				if( !is_numeric($c) || $c<=0 ){
					return false;
				}
			}
		} else { $cat = 0; }
	}

	$user = isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0;

	if( $config['prd_deposits']=='use-main' ){
		$dps = prd_deposits_get_main();
	}else{
		$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
	}

	if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) ){
		// Permet la consultation sous l'identité d'une autre personne (réservé aux administrateurs et représentants)
		$user = $_SESSION['admin_view_user'];
	}elseif( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==4 && $config['tnt_id']==1 ){
		// les revendeurs Bigship ne voient pas leur prix via cette fonction
		$user = 0;
	}
	if( isset($_SESSION['usr_tnt_id']) && !$_SESSION['usr_tnt_id'] ){
		$user = 0;
	}

	$exempt = gu_users_is_tva_exempt( $user );
	$usr_holder = gu_users_get_prices_holder( $user );
	$usr_prc_id = gu_users_get_prc( $usr_holder, true );
	$usr_ref = gu_users_get_ref( $usr_holder, true );

	$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

	$sql = 	'
	select distinct prd.prd_id as id, prd_ref as ref, prd_name as name, if(ifnull(prd_title, \'\')=\'\', prd_name, prd_title) as title,
	prd_desc as "desc", prd_desc_long as "desc-long",
	get_price_ht( prd.prd_tnt_id, prd.prd_id, '.$usr_holder.', 1, 0, '.$config['discount_apply_type'].', '.$usr_prc_id.', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", prd.prd_brd_id, prd.prd_centralized, '.($apply_remise_ecotaxe ? '0' : 'prd.prd_ecotaxe').' ) as price_ht, '.( $exempt ? '1' : 'get_tva( prd.prd_tnt_id, prd.prd_id, '.gu_users_get_accouting_category( $user, true ).', '.gu_users_get_cnt_code($user, true).' )' ).' as tva_rate,
	prd_publish as publish, prd_publish_cat as publish_cat, prd_orderable as orderable, date_format(prd_date_published,"%d/%m/%Y à %H:%i") as date_published,
	prd_new as new, prd_brd_id as brand, brd_id, brd_name,
	prd_weight as weight, prd_length as "length", prd_width as width, prd_height as height,
	'.($config['use_decimal_qte'] ? prd_stocks_get_sql() . '-sto_prepa' : 'cast(' . prd_stocks_get_sql() . '-sto_prepa as signed)' ).' as stock,
	'.($config['use_decimal_qte'] ? prd_stocks_sto_res() : 'cast('.prd_stocks_sto_res().' as signed)' ).' as stock_res,
	'.($config['use_decimal_qte'] ? 'sto_com' : 'cast(sto_com as signed)' ).' as stock_com,
	'.($config['use_decimal_qte'] ? 'sto_prepa' : 'cast(sto_prepa as signed)' ).' as stock_prepa,
	if(prd_stock_livr<ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY),null,date_format(prd_stock_livr,"%d/%m/%Y")) as stock_livr,
		prd_centralized as centralized, prd_keywords as keywords, prd_img_id as img_id,
	date_format(prd_date_created,"%d/%m/%Y à %H:%i") as date_created,
	date_format(prd_date_modified,"%d/%m/%Y à %H:%i") as date_modified,
	prd_sell_weight as sell_weight, prd_is_sync as is_sync
	';

	if( is_array($cat) )
		$sql .= ', if(ifnull(cly_url_perso, \'\')=\'\', cly_url_alias, cly_url_perso) as url, cly_url_perso as url_perso, cly_url_alias';

	$sql .= '
	from prd_hierarchy as hry
	inner join prd_products as prd on (prd_parent_id=prd.prd_id and prd.prd_tnt_id='.$config['tnt_id'].' and prd_child_id='.$id.')
	left join prd_brands on (prd_brd_id=brd_id and brd_tnt_id='.$config['tnt_id'].')
	left join prd_stocks on (prd.prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
	';

	if( is_array($cat) )
		$sql .= 'join prd_classify on (cly_tnt_id=prd.prd_tnt_id and cly_prd_id=prd.prd_id)';

	$sql .= '
	where hry.prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null
	';

	if( $config['use_catalog_restrictions'] )
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd.prd_id' ).')';
	if( $centralized )
		$sql .= ' and prd_centralized!=0';
	if( $published )
		$sql .= ' and prd_publish and (prd_sleep=0 or ('. prd_stocks_get_sql() . '-sto_prepa)>0) ';
	elseif( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true )
		$sql .= ' and ( (prd_is_sync and prd_publish) or not prd_is_sync ) ';
	if( is_array($cat) )
		$sql .= ' and cly_cat_id in ('.implode(',', $cat).')';

	$sql .= ' order by prd_child_pos, prd_ref ';

	$sql = 'select tmp.*, (tmp.price_ht * tmp.tva_rate) as price_ttc from ('.$sql.') as tmp';

	$r = ria_mysql_query($sql);

	// débugage tva
	if( ria_mysql_errno() ){
		error_log('prd_parents_get - '.mysql_error().' - '.$sql);
	}

	return $r;
}
// \endcond

/** Cette fonction permet de récupérer les identifiants des articles parents du ou des articles passés en paramètre
 *	@param int $prd Obligatoire, identifiant ou tableau d'identifiants produits
 *	@return array Un tableau contenant les identifiants des articles parents
 */
function prd_products_get_parents_ids( $prd ){
	$ar_parents = array();

	$prd_ids = control_array_integer( $prd );
	if( $prd_ids === false ){
		return $ar_parents;
	}

	global $config;

	$res = ria_mysql_query('
		select prd_parent_id
		from prd_hierarchy
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_child_id in ('.implode( ', ', $prd_ids ).')
	');

	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_parents[] = $r['prd_parent_id'];
		}
	}

	return array_unique( $ar_parents );
}

// \cond onlyria
/** Cette fonction permet de vérifier si un produit est parent d'un autre produit
 *	@param int $id Obligatoire, identifiant d'un produit
 *	@return bool Retourne true si le produit est parent, false dans le cas contraire
 */
function prd_products_is_parent( $id ){
	if (!is_numeric($id) || $id <= 0) {
		return false;
	}

	global $config;

	$res = ria_mysql_query( '
		select 1
		from prd_hierarchy as h
		join prd_products as p on ( p.prd_tnt_id=h.prd_tnt_id and prd_id=prd_child_id )
		where h.prd_tnt_id='.$config['tnt_id'].'
		and prd_parent_id='.$id.'
		and prd_date_deleted is null
		' );

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}
	return true;
}
// \endcond

