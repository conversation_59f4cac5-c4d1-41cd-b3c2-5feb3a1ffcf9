<?php

    /** \file index.php
     *  Cette page affiche la liste des rapports d'appels téléphoniques ainsi que des statistiques sur les appels
     */

	require_once('reports.inc.php');
    require_once('strings.inc.php');

    // Téléchargement de l'export Excel des produits
	if( isset($_GET['downloadexportcalls']) ){
		// Contrôle que le fichier est bien disponible
		$file = $config['doc_dir'].'/export-calls-report-'.$_SESSION['usr_id'].'.xls';
		if (file_exists($file)) {
            header('Pragma: no-cache');
            header('Expires: 0');
        	header('Content-Type: application/vnd.ms-excel');
            header('Content-disposition: attachment; filename="export-rapport-appels.xls"');

            ob_clean();
			flush();
			readfile($file);
			exit;
		}else{
            $error = _("Le fichier ne semble plus disponible, veuillez préparer un nouvel export en cliquant sur le bouton \"Exporter\".");
		}
	}

	// Période
    // Variable pour la mise en place des périodes
    $dates = array();
    $date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : false;
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	if( isset($_GET["date1"], $_GET["date2"]) ){
		$date1 = ria_mysql_escape_string( dateheureparse($_GET["date1"] ) );
        $date2 = ria_mysql_escape_string( dateheureparse( $_GET["date2"] ) );
	}
    $dates = array('date1' => date('Y-m-d H:i:s', strtotime($date1)),'date2' => date('Y-m-d H:i:s', strtotime($date2) + 86399));

	$author = isset($_GET['author']) && is_numeric($_GET['author']) && $_GET['author'] > 0 ? $_GET['author'] : 0;

    $limit = 25;

    $total_rows = gcl_calls_get_count($author, $dates);

    //Calcule le nombre de pages
    $pages = ceil($total_rows / $limit);

    // Détermine la page en cours de consultation
    $page = 1;
    if( isset($_GET['page']) && is_numeric($_GET['page']) ){
        if( $_GET['page']>0 && $_GET['page']<=$pages )
            $page = $_GET['page'];
    }

    $offset = $limit * ($page - 1);
    $reports = gcl_calls_get_by_view("", $offset, $limit, $author, $dates);


    if (!is_array($reports)) {
        $reports = array();
    }

    $all_authors = gcl_calls_get_authors();

    $final_reports = array();
    foreach ($reports as $key => $report) {
        if (is_array($report)) {
            $final_reports[] = array(
                '_id' => $report['_id'],
                'is_sync' => !isset($report['is_sync']) ? '0' : $report['is_sync'],
                'gcl_author_id' => $report['gcl_author_id'],
                'gcl_type' => $report['gcl_type'],
                'gcl_usr_dst' => $report['gcl_usr_dst'],
                'date' => $report['gcl_date_created'],
				'gcl_comment' => $report['gcl_comment']
            );
        }
    }

    // Détermine les limites inférieures et supérieures pour l'affichage des pages
    $pmin = $page-5;
    if( $pmin<1 ){
		$pmin = 1;
	}
    $pmax = $pmin+9;
    if( $pmax>$pages ){
		$pmax = $pages;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( 'Yuto', '/admin/fdv/index.php' )
		->push( _('Rapports d\'appels') );

	define('ADMIN_PAGE_TITLE', _('Rapports') . ' - ' . _('Rapports d\'appels') . ' - Yuto');
    require_once('admin/skin/header.inc.php');

    if( isset($error) ){
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
	}

	$colspan = $author ? 3 : 4;
?>

<h2><?php print _('Rapports d\'appels'); ?> (<?php print number_format( $reports['total_rows'], 0, ',', ' ' ); ?>)</h2>

<?php
	// Affiche le sélecteur d'Auteur de rapport
	if( $all_authors && sizeof($all_authors) ){
?>
		<div class="stats-menu">
			<div id="riadatepicker"></div>
			<div class="riapicker" id="select-fdv-calls-report-author">
				<div class="selectorview">
					<div class="left">
						<span class="function_name"><?php print _('Auteurs')?></span>
						<br/><span class="view"><?php
							if( $author <= 0 ){
								print _('Tous les auteurs');
							}else{
								$select_author 	= array( 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 );

								$r_select_author = gu_users_get( $author );
								if( $r_select_author && ria_mysql_num_rows($r_select_author) ){
									$select_author = ria_mysql_fetch_assoc( $r_select_author );
								}

								print trim( $select_author['adr_firstname'].' '.$select_author['adr_lastname'].' '.$select_author['society'] );
							}
						?></span>
					</div>
					<a name="btn" class="btn"><img alt="" src="/admin/images/stats/fleche.gif" height="8" width="16" /></a>
					<div class="clear"></div>
				</div>
				<div style="display: none;" class="selector">
					<a name="author-0"><?php print _('Tous les auteurs')?></a>
					<?php
						foreach( $all_authors as $id => $an_auth ){
							print '<a name="author-'.$id.'">'.htmlspecialchars( trim($an_auth['firstname'].' '.$an_auth['lastname'].' '.$an_auth['society']) ).'</a>';
						}
					?>
				</div>
			</div>
			<div class="clear"></div>
		</div>
<?php } ?>

    <?php
        // Graphiques
        view_import_highcharts();
        require_once( 'admin/highcharts/graph-calls-history.php' );
        if (!$author){
            require_once( 'admin/highcharts/graph-calls-representative.php' );
        }
        require_once( 'admin/highcharts/graph-calls-customers.php' );
    ?>

    <form>
        <input type="hidden" name="date1" id="date1" value="<?php print $date1; ?>" />
	    <input type="hidden" name="date2" id="date2" value="<?php print $date2; ?>" />
	    <input type="hidden" name="author_id" id="author_id" value="<?php print $author; ?>" />
    </form>
<?php
    if ($reports['total_rows'] > 0){
?>
    	<table id="calls-report" class="checklist ui-sortable">
    		<thead><tr>
                <th id="reports-created"><?php print _('Appel')?></th>
    			<?php if( !$author ){ ?>
    				<th id="reports-author"><?php print _('Auteur')?></th>
    			<?php } ?>
    			<th id="reports-dest"><?php print _('Destinataire')?></th>
    			<th id="reports-comments"><?php print _('Commentaires') ?></th>
    		</tr></thead>

            <tfoot>
                <?php if( $pages>1 ){ ?>
                    <tr id="pagination">
                        <td class="page align-left"><?php print _('Page')?> <?php print $page ?>/<?php print $pages; ?></td>
                        <td colspan="<?php print $author ? 2 : 3; ?>" class="pages">
                            <?php
                                if( $pages>1 ){
                                    if( $page>1 )
                                        print '<a href="index.php?page='.($page-1).($author ? '&author='.$author : '').'&date1='.$date1.'&date2='.$date2.'">&laquo; '._('Page précédente').'</a> | ';
                                    for( $i=$pmin; $i<=$pmax; $i++ ){
                                        if( $i==$page )
                                            print '<b>'.$page.'</b>';
                                        else
                                            print '<a href="index.php?page='.$i.($author ? '&author='.$author : '').'&date1='.$date1.'&date2='.$date2.'">'.$i.'</a>';
                                        if( $i<$pmax )
                                            print ' | ';
                                    }
                                    if( $page<$pages )
                                        print ' | <a href="index.php?page='.($page+1).($author ? '&author='.$author : '').'&date1='.$date1.'&date2='.$date2.'">'._('Page suivante').' &raquo;</a>';
                                }
                            ?>
                        </td>
                    </tr>
                <?php } ?>
                <tr id="actions">
                    <td colspan="<?php print $author ? 3 : 4; ?>">
                        <input type="submit" name="export-calls" id="export-calls" value="<?php print _('Exporter')?>" title="<?php print _('Exporter la liste des rapports d\'appels')?>" />
                    </td>
                </tr>
            </tfoot>


    		<tbody><?php
                if (!$reports['total_rows']){
                    print '
    					<tr><td colspan="'.$colspan.'">'._('Aucun rapport d\'appel n\'a été enregistré selon les critères définis.').'</td></tr>
    				';
                }
    			$lcount = 0;

                foreach ($final_reports as $key => $report) {
                    $auth = array( 'id'=>0, 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 );
                    $dest = array( 'id'=>0, 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 );

                    $dest_id = $report['gcl_usr_dst'];
                    if ($report['gcl_type'] != 2){
                        $dest_id = $report['gcl_author_id'];
                    }
                    $r_dest = gu_users_get( $dest_id );
                    if( $r_dest && ria_mysql_num_rows($r_dest) ){
                        $dest = ria_mysql_fetch_assoc( $r_dest );
                    }

                    if (!isset($report['is_sync'])){
                        $report['is_sync'] = 0;
                    }

                    print '
                        <tr>
                            <td headers="reports-created">
                                <img class="sync" src="/admin/images/sync/'.( $report['is_sync'] ? 1 : 0 ).'.svg" title="'.( $report['is_sync'] ? _('Cet appel est synchronisé avec votre gestion commerciale') : _('Cet appel n\'existe que dans votre boutique en ligne') ).'" alt="'.( $report['is_sync'] ? _('Cet appel est synchronisé avec votre gestion commerciale') : _('Cet appel n\'existe que dans votre boutique en ligne') ).'" />
                                <a href="/admin/fdv/reports/calls/view.php?id='.$report['_id'].'">'._('Appel du').' '.date(_('d/m/Y à H:i'), strtotime($report['date'])).'</a>
                            </td>
                    ';

                    if( !$author ){
                        $author_id = $report['gcl_author_id'];
                        if ($report['gcl_type'] != 2){
                            $author_id = $report['gcl_usr_dst'];
                        }
                        $r_author = gu_users_get( $author_id );
                        if( $r_author && ria_mysql_num_rows($r_author) ){
                            $auth = ria_mysql_fetch_assoc( $r_author );
                        }

                        $author_name = gu_users_get_name($author_id);
                        $author_name = $author_name != '' ? $author_name : _('Compte client n°').' '.$author_id;
                        if( $auth == array( 'id'=>0, 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 ) ){
                            print '<td headers="reports-author"><span class="barre" title="'._('Ce compte client a été supprimé').'">'.htmlspecialchars($author_name).'</span></td>';
                        }else{
                            print '<td headers="reports-author"><a href="/admin/customers/edit.php?usr='.$author_id.'">';
                            print view_usr_is_sync( $auth ).'&nbsp;'.htmlspecialchars( $author_name );
                            print '</a></td>';
                        }
                    }

                    $dest_name = gu_users_get_name($dest_id);
                    $dest_name = $dest_name != '' ? $dest_name : _('Compte client n°').' '.$dest_id;
                    if( $dest == array( 'id'=>0, 'adr_firstname'=>'', 'adr_lastname'=>'', 'society'=>'', 'is_sync'=>0 ) ){
                        print '<td headers="reports-dest"><span class="barre" title="'._('Ce compte client a été supprimé').'">'.htmlspecialchars($dest_name).'</span></td>';
                    }else{
                        print '<td headers="reports-dest"><a href="/admin/customers/edit.php?usr='.$dest_id.'">';
                        print view_usr_is_sync( $dest ).'&nbsp;'.htmlspecialchars( $dest_name );
                        print '</a></td>';
                    }

    				print '<td headers="reports-comments">';
    				print isset($report['gcl_comment']) ? htmlspecialchars( $report['gcl_comment'] ) : '';
    				print '</td>';

                    print '
                        </tr>
                    ';
                    $lcount++;
                }
    		?></tbody>
    	</table>
    <?php } ?>
</form>
<script>
<!--
	function load_reports(){
		window.location.href = 'index.php?date1=' + $('[name=date1]').val() + '&author=<?php print $author; ?>&date2=' + $('[name=date2]').val();
	}
	<?php print view_date_initialized( 0, '', false, array('callback'=>'load_reports') ); ?>
-->
</script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
