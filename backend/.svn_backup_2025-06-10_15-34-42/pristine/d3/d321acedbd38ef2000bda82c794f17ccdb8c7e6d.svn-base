<?php

	/** \file confirm-orders-priceminister.php
	 *
	 * 	Ce script est destiné à confirmer les nouvelles commandes réalisées sur la place de marché PriceMinister.
	 *	Il est lancé automatiquement toutes les heures.
	 *
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('orders.inc.php');
	require_once('comparators/ctr.priceminister.inc.php');

	// Active ou non le mode test
	$mode_test = isset($ar_params['test']) && $ar_params['test'] == 'test';

	$errors = array();

	// Traitement
	foreach( $configs as $config ){
		// Vérifie que la place de marché est bien activée pour ce client.
		// Dans le cas contraire, passe au client suivant.
		if( !ctr_comparators_actived(CTR_PRICEMINISTER) ){
			continue;
		}

		$states = array( 6, 7, 8 );

		// récupèrer le compte client, le produit frais de port et le service de livraison
		$pms = ctr_params_get_array( CTR_PRICEMINISTER, array('USR_ID') );
		if( !isset($pms['USR_ID']) || !gu_users_exists($pms['USR_ID']) ){
			continue;
		}

		// récupère les commandes ayant fait l'objet d'une expédition
		$rord = ord_orders_get( $pms['USR_ID'], 0, $states, 0, null, false, false, false, false, false, false, '', true, array(_FLD_ORD_CTR_SHIPPED=>'Non') );

		if( $rord && ria_mysql_num_rows($rord) ){
			while( $ord = ria_mysql_fetch_array($rord) ){

				$result = ctr_priceminister_confirm_order( $ord['id'] );

				if( !$result ){
					$errors[] = '[Commande '.$ord['id'].' - '.$config['site_name'].'] Erreur lors de la confirmation de la commande.';
				} elseif( $result===true && !fld_object_values_set(array($ord['id']), _FLD_ORD_CTR_SHIPPED, 'Oui') ){
					$errors[] = '[Commande '.$ord['id'].' - '.$config['site_name'].'] Erreur lors de la confirmation de la commande, mise à jour du champ avancé notifié : Oui.';
				}

			}
		}
	}

	if( sizeof($errors) ){
		// Module RiaShoppping plus suivi, plus d'envoi de message
	}
