<?php
	if( !isset($usr) ){
		header('Location: /admin/customers/index.php');
		exit;
	}
?>
<table id="table-customers-images">
	<caption><?php print _('Images associées au compte').' '.htmlspecialchars(gu_users_get_name($_GET['usr'])); ?></caption>
	<tbody>
		<tr><th colspan="2"><?php print _('Image principale')?></th></tr>
		<tr>
			<td class="col170px"><label for="main-img"><?php print $usr['img_id'] ? _('Remplacer l\'image par :') : _('Ajouter une image :'); ?></label></td>
			<td><input type="file" name="main-img" class="width-auto" /></td>
		</tr>
		<tr>
			<td><?php print _('Image principale :'); ?></td>
			<td>
				<?php
					if( !$usr['img_id'] ){
						print _('Aucune image principale');
					}else{
						$size = $config['img_sizes']['medium'];
						print '	<div class="preview" onclick="previewClick(this);">
									<input type="checkbox" class="checkbox none" name="del-img-main" value="'.$usr['img_id'].'" />
									<img src="'.$config['img_url'].'/'.$size['dir'].'/'.$usr['img_id'].'.jpg" width="'.$size['width'].'" height="'.$size['height'].'" />
								</div>';
					}
				?>
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td colspan="2">
			<input type="submit" name="saveimg" value="<?php print _('Enregistrer')?>" />
			</td>
		</tr>
	</tfoot>
</table>
<?php print view_admin_img_objects( CLS_USER, $usr['id'], 0, 0, 0, false); ?>
