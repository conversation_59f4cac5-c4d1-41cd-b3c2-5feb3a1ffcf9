<?php
function getVar($varname='var', $var='var')
{
    $var = $var2 = getVar();
    $var = $var2 = $var3 = '';

    if ($varname === 'var' || $var2 === 'varname' || $var = true) {
        $var[($var + 1)] = $var2;
        $var[($var = 1)] = $var2;
    }

    for ($i = $var; (null !== ($key = key($i))); next($i)) {
        while ($i = true) {
            echo $i = getVar();
            echo \A\B\C::$d = getVar();
        }
    }

    while ($row = $query->fetch(PDO::FETCH_NUM)) {
        $result[$row[0]] = array();
        $result[$row[0]][] = $current;

        self::$_inTransaction = TRUE;
        parent::$_inTransaction = TRUE;
        static::$_inTransaction = TRUE;
        $$varName = $varValue;
    }

}//end getVar()

class myClass
{
    private static $_dbh = NULL;
    public $dbh = NULL;
    protected $dbh = NULL;
    var $dbh = NULL; // Old PHP4 compatible code.
}

A::$a = 'b';
\A::$a = 'c';
\A\B\C::$d = 'd';
B\C::$d = 'e';

@$a = 1;

$a = [];
foreach ($a as $b)
    $c = 'd';

$var = $var2;
list ($a, $b) = explode(',', $c);
$var1 ? $var2 = 0 : $var2 = 1;

$obj->$classVar = $prefix.'-'.$type;

$closureWithDefaultParamter = function(array $testArray=array()) {};
?>
<?php $var = false; ?>

<?php

while ( ( $csvdata = fgetcsv( $handle, 2000, $separator ) ) !== false ) {
	// Do something.
}
