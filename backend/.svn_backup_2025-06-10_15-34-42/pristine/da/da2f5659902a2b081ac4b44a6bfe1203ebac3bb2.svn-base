<?php

	/**	\file check-main-img.php
	 * 
	 * 	Ce script essaye de faire en sorte que tous les produits disposent d'une image principale. Pour les
	 * 	produits qui n'en ont pas, il va regarder s'il existe au moins une image secondaire que nous pourrions
	 * 	utiliser en tant qu'image principale.
	 */

	set_include_path(dirname(__FILE__) . '/../include/');

	require_once(str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php');
	require_once('products.inc.php');

	// Charge la liste des produits sans image principale
	$rproducts = prd_products_get_not_images();
	if( $rproducts ){
		while( $prd = ria_mysql_fetch_array($rproducts) ){
			
			// Regarde s'il dispose d'images secondaires
			$rimg = prd_images_get( $prd['id'] ); 
			if( $rimg && ria_mysql_num_rows($rimg) ){
				// Utilise la première image secondaire comme principale
				if( $img = ria_mysql_fetch_array($rimg) ){
					print $prd['name']."\n";
					
					// ajout l'image en tant que principal 
					prd_images_main_add_existing( $prd['id'], $img['id'] ); 
					
					// retire l'image secondaire
					prd_images_del( $prd['id'], $img['id'] );
					
				}
			}
		}
	}
