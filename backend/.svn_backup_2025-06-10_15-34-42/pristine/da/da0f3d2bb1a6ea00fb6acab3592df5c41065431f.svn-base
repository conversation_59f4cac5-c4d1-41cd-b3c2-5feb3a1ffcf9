<?php
    
    /** Ce script permet de mettre à jour les positions des images afin d'éviter les problèmes d'ordre 
     *  Dans ce script, on va mettre à jour, produit par produit, les positions des images qui y sont liées, 
     *  afin de corriger les erreurs de doublons qui ont pu être provoquées
     */
	
     set_include_path(dirname(__FILE__) . '/../include/');

	require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );
	require_once('prd/images.inc.php');

    // On récupère tout les produits
    $r_products = prd_products_get_simple();
    if( $r_products ){
        // Pour chaque produit, on récupère les images associées
        while( $prd = ria_mysql_fetch_assoc($r_products) ){
            $r_img = prd_images_get( $prd['id'], 0, false, true, true ); 
            
            if( $r_img && ria_mysql_num_rows($r_img) ){
                $position = 1;
                // Puis pour chaque image, on va mettre à jour le champ img_pos afin qu'il n'y ai pas de doublons de positions
                while( $img = ria_mysql_fetch_assoc($r_img) ){
                    if( $img['is_main'] ){
                        if( !prd_images_set_pos($prd['id'], $img['id'], 0) ){
                            print 'Erreur lors de la mise à jour de la position de l\'image principale img:'.$img['id'].' prd:'.$prd['id'].PHP_EOL;
                        }
                    } else {
                        if( !prd_images_set_pos($prd['id'], $img['id'], $position) ){
                            print 'Erreur lors de la mise à jour de la position d\'une image secondaire img:'.$img['id'].' prd:'.$prd['id'].' pos:'.$position.PHP_EOL;
                        }
                        $position++;
                    }
                }
            }
        }
    }
