<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/datastore/v1/query.proto

namespace Google\Cloud\Datastore\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * The desired order for a specific property.
 *
 * Generated from protobuf message <code>google.datastore.v1.PropertyOrder</code>
 */
class PropertyOrder extends \Google\Protobuf\Internal\Message
{
    /**
     * The property to order by.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyReference property = 1;</code>
     */
    private $property = null;
    /**
     * The direction to order by. Defaults to `ASCENDING`.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyOrder.Direction direction = 2;</code>
     */
    private $direction = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Cloud\Datastore\V1\PropertyReference $property
     *           The property to order by.
     *     @type int $direction
     *           The direction to order by. Defaults to `ASCENDING`.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Datastore\V1\Query::initOnce();
        parent::__construct($data);
    }

    /**
     * The property to order by.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyReference property = 1;</code>
     * @return \Google\Cloud\Datastore\V1\PropertyReference|null
     */
    public function getProperty()
    {
        return isset($this->property) ? $this->property : null;
    }

    public function hasProperty()
    {
        return isset($this->property);
    }

    public function clearProperty()
    {
        unset($this->property);
    }

    /**
     * The property to order by.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyReference property = 1;</code>
     * @param \Google\Cloud\Datastore\V1\PropertyReference $var
     * @return $this
     */
    public function setProperty($var)
    {
        GPBUtil::checkMessage($var, \Google\Cloud\Datastore\V1\PropertyReference::class);
        $this->property = $var;

        return $this;
    }

    /**
     * The direction to order by. Defaults to `ASCENDING`.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyOrder.Direction direction = 2;</code>
     * @return int
     */
    public function getDirection()
    {
        return $this->direction;
    }

    /**
     * The direction to order by. Defaults to `ASCENDING`.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PropertyOrder.Direction direction = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setDirection($var)
    {
        GPBUtil::checkEnum($var, \Google\Cloud\Datastore\V1\PropertyOrder\Direction::class);
        $this->direction = $var;

        return $this;
    }

}

