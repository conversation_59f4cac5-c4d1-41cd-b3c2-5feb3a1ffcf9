<?php

/**	\file index.php
 * 
 * 	Cette page permet l'intéraction en ajax avec l'écran de configuration des relevés de linéaires.
 * 
 * 	Elle supporte les actions suivantes :
 * 	- publish : Permet la publication d'un assortiment
 *  - unpublish : Permet la dépublication d'un assortiment
 *  - batchProduct : permet l'ajout en mode batch d'une liste de produits
 *  - batchUsers : permet l'association en mode batch d'un assortiment à une liste de clients
 *  - getProducts : retourne la liste des produits contenus dans un assortiment
 *  - getUsers : retourne la liste des clients rattachés à un assortiment
 *  - analyse : permet l'analyse de données à importer
 * 
 */

// Vérifie que l'utilisateur en cours peut accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_LINEAR_RAISED_CONFIG');

use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_users;
use Riashop\PriceWatching\models\LinearRaised\Stats\OffersRaised;
use Riashop\PriceWatching\models\LinearRaised\Features\FollowedList;
use Riashop\PriceWatching\models\LinearRaised\prw_followed_products;

$result = false;
$errors = array();
$content = null;
$message = '';

if( isset($_GET['action']) ){
	switch($_GET['action']) {
		case 'publish':
			if (isset($_POST['id'])) {
				$result = prw_followed_lists::publish($_POST['id']);
				if ($result) {
					$message = 'Actif';
					$content = _('Clicker pour désactiver');
				}
			}
			break;
		case 'unpublish':
			if (isset($_POST['id'])) {
				$result = prw_followed_lists::publish($_POST['id'], false);
				if ($result) {
					$message = 'Inactif';
					$content = _('Clicker pour activer');
				}
			}
			break;
		case 'batchProduct':
			if (isset($_POST['id'])) {
				$up = false;
				$title = null;
				$fls_id = null;
				if (isset($_POST['title']) && trim($_POST['title'])) {
					$up = true;
					$title = $_POST['title'];
				}

				if (isset($_POST['section']) && is_numeric($_POST['section'])) {
					$up = true;
					$fls_id = $_POST['section'];
				}
				if ($up) {
					prw_followed_lists::update($_POST['id'], $title, null, $fls_id);
				}
				$items = array();
				if (isset($_POST['items'])) {
					$items = $_POST['items'];
				}
				try{
					$FollowedList = new FollowedList($_POST['id']);
					$batch = $FollowedList->batchUpdateProducts($items);
					if (!$batch) {
						$message = "Une erreur est survenue lors de l'ajout des produits";
					}else{
						$result = true;
						$message = "Les modifications ont bien été enregistrées.";
					}
				}catch(Exception $e) {
					$message = $e->getMessage();
				}
			}else{
				$message = 'Rien à sauvegarder';
			}
			break;
		case 'batchUsers':
			if (isset($_POST['id'])) {
				$users = array();
				if (isset($_POST['users'])) {
					foreach ($_POST['users'] as $id => $user) {
						$users[] = array(
							'id' => $user['id'],
							'prf_id' => gu_users_get_prf($user['id']),
						);
					}
				}
				try{
					$FollowedList = new FollowedList($_POST['id']);
					$batch = $FollowedList->batchUpdateUsers($users);
					if (!$batch) {
						$message = "Une erreur est survenue lors de l'association des comptes clients";
					}else{
						$result = true;
						$message = "Les comptes clients ont bien été liés à l'assortiment.";
					}
				}catch(Exception $e) {
					$message = $e->getMessage();
				}
			}else{
				$message = 'Rien à sauvegarder';
			}
			break;
		case 'getProducts':
			if (isset($_GET['id'])  && is_numeric($_GET['id'])) {
				$r_prd = prw_followed_products::get($_GET['id']);
				$state = array(
					'catalog' => array(),
					'competition' => array(),
				);
				if ($r_prd) {
					while($prd = ria_mysql_fetch_assoc($r_prd)) {
						$key = 'catalog';
						if ($prd['is_cpt']) {
							$key = 'competition';
						}

						$state[$key][] = array(
							'id' => $prd['prd_id'],
							'name' => prd_products_get_ref($prd['prd_id']).' - '.prd_products_get_name($prd['prd_id']),
							'pmc' => $prd['pmc'],
							'rank' => $prd['rank'],
							'is_cpt' => $prd['is_cpt'] ? true : false,
						);
					}
				}

				$content = $state;
				$result = true;
			}
			break;
		case 'getUsers':
			if (isset($_GET['id']) && is_numeric($_GET['id'])) {
				$r_users = prw_followed_users::get($_GET['id']);
				$state = array(
					'users' => array(),
					'managers' => array(),
				);
				if ($r_users) {
					while($user = ria_mysql_fetch_assoc($r_users)) {
						$key = 'users';
						if (in_array($user['prf_id'], array(PRF_ADMIN, PRF_SELLER))) {
							$key = 'managers';
						}
						$rusr = gu_users_get( $user['usr_id'] );
						if( $rusr && ria_mysql_num_rows($rusr) ){
							$usr = ria_mysql_fetch_assoc($rusr);

							$allname = view_usr_is_sync( $usr ).' '.trim( htmlspecialchars($usr['adr_firstname'].' '.$usr['adr_lastname'].' '.$usr['society']) );

							$state[$key][$user['usr_id']] = array(
								'id' => $user['usr_id'],
								'name' => $allname,
								'email' => $usr['email'],
							);
						}

					}
				}

				$content = $state;
				$result = true;
			}
			break;
		case 'analyse':
			if( isset($_POST['batch']) && trim($_POST['batch']) ){
				$_POST['batch'] = preg_replace('/[ \t]+/', ' ', $_POST['batch']);
				$lines = preg_split('/\r\n|\r|\n/', $_POST['batch'], -1, PREG_SPLIT_NO_EMPTY);
				$matches = $products = $ranks = array();
				$result = true;
				foreach( $lines as $i => $line ){
					$cols = preg_split('/ |\t/', trim($line));
					$cols = array_map('trim', $cols);

					if ( count($cols) < 1 || 3 < count($cols) ) {
						$matches[] = array(
							'type' => 'error',
							'message' => 'La ligne n\'a pas pu être analysé.',
						);

						continue;
					}

					if( !($prd_id = prd_products_get_id($cols[1])) ){
						$matches[] = array(
							'type' => 'error',
							'message' => 'Nous n\'avons pas trouvé de produit pour la référence '.$cols[1],
						);

						continue;
					}

					if( isset($cols[2]) ){
						// Permet d'accepter le point ou la virgule comme séparateur.
						$cols[2] = str_replace(',', '.', $cols[2]);
						if( !is_numeric($cols[2]) || $cols[2] < 0 ){
							$matches[] = array(
								'type' => 'error',
								'message' => 'Erreur lors de l\'analyse du PMC',
							);

							continue;
						}
					}

					if( isset($cols[0]) ){
						if( !is_numeric($cols[0]) || $cols[0] < 0 ){
							$matches[] = array(
								'type' => 'error',
								'message' => 'Erreur lors de l\'analyse du rang',
							);

							continue;
						}

						if( $cols[0] == 0){
							$matches[] = array(
								'type' => 'error',
								'message' => 'Le rang doit être supérieur à 1',
							);

							continue;
						}

						if( in_array($cols[0], $ranks) ){
							$matches[] = array(
								'type' => 'error',
								'message' => 'Le rang "'.$cols[0].'" est déjà utilisé',
							);

							continue;
						}
					}

					if( in_array($prd_id, array_keys($products)) ){
						$matches[] = array(
							'type' => 'error',
							'message' => 'La référence '.$products[$prd_id]['ref'].' est déjà présente au rang '.$products[$prd_id]['rank'],
						);

						continue;
					}

					$products[$prd_id] = array('ref' => $cols[1], 'rank' => $cols[0]);
					$ranks[] = $cols[0];
					$matches[] = array(
						'id' => $prd_id,
						'type' => 'product',
						'name' => $cols[1].' - '.prd_products_get_name($prd_id),
						'pmc' => ria_array_get($cols, '2', 0),
						'rank' => ria_array_get($cols, '0', 0),
					);
				}

				$content = $matches;
			}else{
				$message = 'Vous devez saisir une valeur.';
			}
			break;
	}
}

header('Content-Type: application/json');
echo json_encode(array(
	'result' => $result,
	'time' => date('Y-m-d H:i:s'),
	'message' => _($message),
	'content' => $content,
));
exit;