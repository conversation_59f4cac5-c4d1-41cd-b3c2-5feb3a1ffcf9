# BeezUPCommonUserErrorMessage

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**doc_url** | [**\Swagger\Client\Model\BeezUPCommonDocUrl**](BeezUPCommonDocUrl.md) |  | [optional] 
**code** | **string** | the error code. The error code can be a pattern containing the argument&#39;s name | 
**message** | **string** | The error message | 
**culture_name** | **string** | If the error is translated, the culture name will be indicated | [optional] 
**arguments** | [**\Swagger\Client\Model\BeezUPCommonUserErrorMessageArguments[]**](BeezUPCommonUserErrorMessageArguments.md) | a dictionary string/object | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


