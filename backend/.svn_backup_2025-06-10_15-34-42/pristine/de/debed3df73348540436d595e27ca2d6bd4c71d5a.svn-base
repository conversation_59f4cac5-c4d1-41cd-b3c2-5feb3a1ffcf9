<?php
	/**	\file refresh-orders-seller-comisionned.php
	 *	\ingroup crontabs pieces
	 *	Ce fichier est lancé tous les jours pour mettre à jours le représentant en commission d'une commande en fonction de leurs rapport de visite
	 *	Un rapport de visible ne peut être utilisé qu'une seule fois en indirect de commande.
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}


	// Permet de désactiver la récupération des commandes uniquement celle n'ayant pas encore de représentant indirect
	$disabled_seller = isset($ar_params['disabled_seller']) && $ar_params['disabled_seller'];

	require_once('stats.inc.php');

	foreach( $configs as $config ){
		
		// Si oui ou non un rapport n'est utilisé qu'une seule fois lors de l'attribution d'une vente indirecte
		$ord_com = isset($config['ord_seller_com_report_unique']) && $config['ord_seller_com_report_unique'] ? null : false;

		// Détermine si l'on se base sur la date de facturation de la commande
		$use_inv_date = isset($config['ord_seller_com_from_invoice']) && $config['ord_seller_com_from_invoice'];

		// On récupère la date de premier import
		$date_first_report = false;

		$r_first_report = rp_reports_get( 0, 1, 0, 0, null, null, null, null, null, array('date_created' => 'asc'), $ord_com );
		if( $r_first_report && ria_mysql_num_rows($r_first_report) ){
			$first_report = ria_mysql_fetch_assoc( $r_first_report );
			$date_first_report = new DateTime( $first_report['date_created_en'] );
		}

		if( $date_first_report === false ){
			return;
		}

		$periods = [
			'start' => $date_first_report->format('Y-m-d')
		];

		if (isset($ar_params['date_start']) && isdate($ar_params['date_start'])) {
			$periods['start'] = $ar_params['date_start'];
		}
		if (isset($ar_params['date_end']) && isdate($ar_params['date_end'])) {
			$periods['end'] = $ar_params['date_end'];
		}


		$ord_where = [
			'state_id' => ord_states_get_ord_valid(),
			'pay_id' => null,
			'has_invoice' => $use_inv_date ? true : false, // Seules les commandes avec facture seront récupérées
		];

		if( !$disabled_seller ){
			$ord_where['seller_com'] = null;
		}

		// on récupèrer que les commandes passées directement par le service commercial (gescom) et sans aucun représentant commissioné
		$r_orders = ord_orders_get_simple( [], $periods, $ord_where, [ 'sort' => ['date' => 'asc'] ] );

		if (!$r_orders || !ria_mysql_num_rows($r_orders)) {
			continue;
		}

		// parcour toutes les commandes
		while( $order = ria_mysql_fetch_assoc($r_orders)) {

			// pour chaque commande on récupère les produits
			$r_oproducts = ord_products_get($order['id']);
			if (!$r_oproducts || !ria_mysql_num_rows($r_oproducts)) {
				continue;
			}

			$products = array();

			while( $op = ria_mysql_fetch_assoc($r_oproducts)) {
				$products[] = $op['id'];
			}
			
			if( !$use_inv_date ){
				// Utilisation de la date de commande
				$date_first = new DateTime( $order['date'] );
				$date_end   = new DateTime( $order['date'] );
			}else{
				// On récupère la première facture de la commande
				$inv_list = ord_orders_get_invoices_list( $order['id'] );
				if( !is_array($inv_list) || !count($inv_list) ){
					continue;
				}

				// Utilisation de la date de première facturation
				$date_first = new DateTime( $inv_list[0]['date'] );
				$date_end   = new DateTime( $inv_list[0]['date'] );
			}

			// Les rapports seront récupérés sur les trois mois précédent la date de commande ou de facturation
			$date_first->modify('-3 months');

			// On récupère les raport de visite avec présentation de produit
			$r_reports = rp_reports_get( 0, 1, $order['usr_id'], 0, $date_first->format('Y-m-d'), $date_end->format('Y-m-d'), null, null, null, array('date_created' => 'desc'),$ord_com);

			if (!$r_reports || !ria_mysql_num_rows($r_reports)) {
				continue;
			}

			$seller_com = null;

			while ($report = ria_mysql_fetch_assoc($r_reports)) {
				//Si le représentant de la commande est celui du rapport on passe
				if ($report['author_id'] == $order['seller_id']) {
					continue;
				}

				//on récupèrer les produits et les catégories présenter dans le rapport
				$rp_objects = rp_report_objects_get($report['id']);
				if (!$rp_objects || !ria_mysql_num_rows($rp_objects)) {
					continue;
				}

				// On regarde si le rapport porte sur un des articles de la commande
				$joint = false;

				while ($obj = ria_mysql_fetch_assoc($rp_objects)) {
					if ($obj['cls_id'] == CLS_PRODUCT) {
						//si le produit présenté ce trouve dans la commande on arrête et on nome l'auteur du rapport comme comissionnaire
						if (in_array($obj['obj_id_0'], $products)) {
							// La vente indirecte est attribuée à l'auteur du rapport le plus récent
							if( $seller_com === null ){
								$seller_com = $report['author_id'];
							}

							$joint = true;

							// Le produit dans la commande sera marqué comme vente indirecte lié à ce rapport
							// Seulement si le rapport est emis par le même représentant indirecte que la commande
							if( $seller_com == $report['author_id'] ){
								ord_products_set_report_id( $order['id'], $obj['obj_id_0'], $report['id'] );
							}
						}
					}elseif($obj['cls_id'] == CLS_CATEGORY) {
						foreach($products as $prd_id) {
							//si un produit de la commande ce trouve dans la catégorie présenté on arrête et on nome l'auteur du rapport comme comissionnaire
							if( prd_products_categories_exists($prd_id, $obj["obj_id_0"]) ){
								// La vente indirecte est attribuée à l'auteur du rapport le plus récent
								if( $seller_com === null ){
									$seller_com = $report['author_id'];
								}

								$joint = true;
								
								// Le produit dans la commande sera marqué comme vente indirecte lié à ce rapport
								// Seulement si le rapport est emis par le même représentant indirecte que la commande
								if( $seller_com == $report['author_id'] ){
									ord_products_set_report_id( $order['id'], $prd_id, $report['id'] );
								}
							}
						}
					}
				}

				// Si le rapport porte sur un des articles de la commande, il est rattaché à celle-ci
				if( $ord_com === false && $joint ){
					rp_reports_set_order( $report['id'], $order['id']);
				}
			}

			if (!is_null($seller_com)) {
				ord_orders_set_seller_com(
					$order['id'],
					gu_users_get_seller_id($seller_com)
				);
			}
		}
	}
