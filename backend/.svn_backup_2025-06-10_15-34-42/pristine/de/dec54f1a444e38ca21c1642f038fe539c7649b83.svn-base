<?php
require_once('delivery.inc.php');

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE_JOB');

if( isset($_POST['add-main']) ){
	header('location: edit.php');
	exit;
}
if( isset($_POST['del-main'], $_POST['jobs']) ){
	foreach( $_POST['jobs'] as $jobs ){
		if(!dlv_store_jobs_del($jobs)){
			$error = _('Une erreur est survenue lors de la suppression d\'un poste de travail.');
			break;
		}
	}
	if( !isset($error) ){
		$success = _('Le poste a bien été supprimé.');
	}
}

// Défini le titre de la page
define('ADMIN_PAGE_TITLE', _('Postes').' - '._('Magasins') . ' - ' . _('<PERSON><PERSON><PERSON> des commandes') . ' - ' . _('poste'));
require_once('admin/skin/header.inc.php');

// Charge la liste des postes de travail des magasins
$res = dlv_store_jobs_get();
$jobs_count = ria_mysql_num_rows( $res );
?>
<h2><?php echo _('Postes Magasins'); ?> (<?php print ria_number_format($jobs_count) ?>)</h2>
<?php
	if( isset($_GET['success']) && $_GET['success'] == 'true' ){
		echo '<div class="notice success">' . _("Le poste a bien été enregistré") . '</div>';
	}
	if( isset($_GET['success']) && $_GET['success'] == 'del' ){
		echo '<div class="notice success">' . _("Le poste a bien été supprimé") . '</div>';
	}
	if( isset($error) ){
		echo '<div class="notice error">'.$error.'</div>';
	}
	if( isset($success) ){
		echo '<div class="notice success">'.$success.'</div>';
	}
?>

	<div class="notice"><?php print _('Pour améliorer la présentation de vos points de vente, vous pouvez définir ici ou plusieurs poste (Hôtesse d\'accueil, Conseiller commercial, Technicien, etc...) qui viendront compléter la présentation des membres du personnel.'); ?></div>
	
	<form action="index.php" method="post">
		<table id="table-postes" class="checklist">
			<thead>
				<tr>
					<th id="cat-sel"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
					<th><?php echo _("Nom"); ?></th>
					<th class="align-right"><?php echo _("Nombre d'employés"); ?></th>
				</tr>
			</thead>
			<tbody>
			<?php
				if( !$res || !ria_mysql_num_rows($res) ){
					echo '<tr><td colspan="3">' . _("Aucun poste défini en magasin") . '</td></tr>';
				}else{
					while( $row = ria_mysql_fetch_assoc($res) ){
						?>
						<tr>
							<td headers="cat-sel">
								<input type="checkbox" class="checkbox" name="jobs[]"
									   value="<?php echo $row['id'] ?>" />
							</td>
							<td>
								<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_JOB_EDIT') ){ ?>
									<a href="edit.php?job=<?php echo $row['id']?>"><?php echo htmlspecialchars( $row['name'] ); ?></a>
								<?php }else{
									echo htmlspecialchars( $row['name'] );
								} ?>
							</td>
							<td class="align-right">
								<?php echo ria_number_format(dlv_store_jobs_count($row['id'])); ?>
							</td>
						</tr>
						<?php
					}
				}
			?>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="3">
						<?php if( $res && ria_mysql_num_rows($res) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_JOB_DEL') ){ ?>
						<input type="submit" class="btn-del" name="del-main" value="<?php echo _("Supprimer"); ?>" />
						<?php }
						if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE_JOB_ADD') ){ ?>
						<input type="submit" name="add-main" value="<?php echo _("Ajouter"); ?>" />
						<?php } ?>
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
<?php
require_once('admin/skin/footer.inc.php');
?>