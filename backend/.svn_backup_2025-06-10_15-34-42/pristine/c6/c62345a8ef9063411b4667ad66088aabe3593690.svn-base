<?php
	require('/var/www/simplesamlphp/lib/_autoload.php');


	$as = new SimpleSAML_Auth_Simple('example');

	if( isset($_GET['logout']) ){
		$as->logout('https://app-okta.recette.riashop.fr/simplesaml/okta/test.php');
	}

	if( isset($_GET['login']) ){
		$as->requireAuth();
	}

	if( $as->isAuthenticated() ){
		print '<br/>--------- AUTH ----------<br/>';
		print '<br/><a href="?logout">Logout</a><br/>';

		$attrs = $as->getAttributes();
		print '<pre>';
		print_r($attrs);
		print '</pre>';
	}else{
		print '<br/>--------- NOT AUTH ----------<br/>';
		print '<br/><a href="?login">Login as Okta</a><br />';
	}


	
