<?php

	require_once('stats.inc.php');
	require_once('tenants.inc.php');
	require_once('websites.inc.php');

	if( !gu_user_is_authorized('_RGH_ADMIN_STATS_CUSTOMER_GENDER') && !gu_user_is_authorized('_RGH_ADMIN_STATS_YUTO')  ){
		header('HTTP/1.0 403 Forbidden');
		exit;
	}

	// Par défaut, on arrive sur le jour en cours
	/*$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : date('Y-m-d');
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	$_SESSION['datepicker_period'] = isset($_SESSION['datepicker_period']) ? $_SESSION['datepicker_period'] : 'Aujourd\'hui';*/

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Clients'), '/admin/stats/customers/index.php' )
		->push( _('Genre des clients') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Genre des clients').' - '._('Statistiques'));
	require_once('admin/skin/header.inc.php');

?>

<h2><?php print _('Statistiques du genre des clients'); ?></h2>
<script src="/admin/js/jquery.min.js"></script>
<script src="/admin/js/search-autocomplete.js"></script>
<script src="/admin/js/jquery.autocomplete.js"></script>
<script src="/admin/js/jquery.datepicker.js"></script>
<script>
	<?php view_date_initialized( 0, '/admin/stats/payments.php', array('graph-orders-count','graph-orders-ca'), array('autoload'=>true) ); ?>
</script>
<script src="/admin/js/riadatepicker.js"></script>
<div class="stats-menu">
	<form action="/admin/stats/payments.php" method="get">
		<div id="riadatepicker"></div>
	</form>
	<div class="clear"></div>

</div>
<h3><?php print _('Genre des clients'); ?></h3>
<div id="graph-gender"></div>


<?php
	$date1 = $date2 = false;

	if (isset($_GET['date1'])) {
		$date1 = $_GET['date1'];
	}

	if (isset($_GET['date2'])) {
		$date2 = $_GET['date2'];
	}

	$datestamp1 = strtotime($date1);
	$datestamp2 = strtotime($date2);
	$nbjours = floor(($datestamp2-$datestamp1)/(60*60*24));

	if( $nbjours > 93 ){ // month
		$genders = stats_user_gender('inscription', $date1, $date2, $group='month' );
	}elseif( $nbjours >= 1 && $nbjours < 93 ){ // day
		$genders = stats_user_gender('inscription', $date1, $date2, $group='day' );
	}else{ // hour
		$genders = stats_user_gender('inscription', $date1, $date2, $group='hour' );
	}


	$jgender = array();
	if( $genders ){
		while( $row = ria_mysql_fetch_array($genders) ){
			$jgender[] = $row;
		}
	}

	$src = array();
	$data = array();

	foreach( $jgender as $row ){
		$src[] = $row['name'];
	}

	$src = array_unique($src);
	$data = array_unique($data);
	sort($src);
	sort($data);

	for( $i=0; $i < count($src); $i++ ){
		$data[$i] = 0;
	}

	foreach( $jgender as $row ){
		$k = array_search($row['name'], $src);
		$data[$k] += intval($row['count']);
	}

	$src = json_encode($src);
	$data = json_encode($data);

?>
<?php view_import_highcharts(); ?>
<script>
	var src = '<?php print $src; ?>';
	var data = '<?php print $data; ?>';
</script>
<script src="/admin/js/graph/graph-stats-gender.js?<?php print ADMIN_ASSET; ?>"></script>

<?php
	require_once('admin/skin/footer.inc.php');
?>