<?php

/**	\defgroup model_products_nomenclatures Nomenclatures
 *	\ingroup pim_products
 *	Ce module comprend les fonctions nécessaires à la gestion des nomenclatures
 *	@{
 */

/** \ingroup pim_products_nomenclatures
 *
 * 	Exemple : Afficher la liste des options d'une nomenclature
 *	\code{.php}
 *		$payzen = new Payzen();
 *		$payzen->createSimplePayment();
 *		$payzen->getIdentifierID( $card_ID ); // -> Optionnel
 *		$payzen->activePayByIdentifier();
 *	\endcode
 */

require_once('products.inc.php');
require_once('prd.stocks.inc.php');
require_once('categories.inc.php');

// \cond onlyria
/**	Cette fonction liste tous les types de nomenclatures existantes pour un locataire
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- tenant : identifiant du locataire (pour savoir s'il s'agit d'un type de base ou d'un type personnalisé)
 *		- id : identifiant du type de nomenclature
 *		- name : nom du type de nomenclature
 */
function prd_nomenclatures_types_get(){

	global $config;

	$sql = '
		select type_tnt_id as tenant, type_id as id, type_name as "name"
		from prd_nomenclatures_types
		where type_tnt_id in (0, '.$config['tnt_id'].')
	';

	return ria_mysql_query($sql);

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la verification d'un identifiant de type de nomenclature.
 *	@param $type Obligatoire, identifiant du type de nomenclature à contrôler
 *	@return bool true si l'identifiant est valide, false dans le cas contraire
 */
function prd_nomenclatures_types_exists( $type ){

	if( !is_numeric($type) || $type < 0 ){
		return false;
	}

	global $config;

	$sql = '
		select type_id from prd_nomenclatures_types
		where type_tnt_id in (0, '.$config['tnt_id'].')
			and type_id='.$type.'
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows($res) > 0;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la modification du type de nomenclature d'un produit
 *	Hors personnalisation de la base de données, les nomenclatures acceptées sont les suivantes :
 *	- NM_TYP_NONE : Aucune
 *	- NM_TYP_BUILT : Fabrication
 *	- NM_TYP_COMPOSED : Commerciale / Composé
 *	- NM_TYP_COMPONENT : Commerciale / Composant
 *	- NM_TYP_LINKED : Article lié
 *	- NM_TYP_VARIABLE : Variable
 *	- NM_TYP_COMPOSED_OPT : Composé optionnel
 *	Dans le cas d'un passage aux types suivants : NM_TYP_BUILT, NM_TYP_VARIABLE, la composition de nomenclature existante est supprimée
 *	@param int $prd Obligatoire, identifiant du produit
 *	@param $type Obligatoire, identifiant du type de nomenclature
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_products_set_nomenclature_type( $prd, $type ){
	global $config;
	if( !prd_products_exists($prd) ) return false;
	if( !prd_nomenclatures_types_exists($type) ) return false;

	$r = ria_mysql_query('update prd_products set prd_nomenclature_type='.$type.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd);

	$no_types = array( NM_TYP_BUILT, NM_TYP_VARIABLE );
	if( $config['prd_linked_nomenclature_activate'] ){
		$no_types[] = NM_TYP_LINKED;
	}

	if( $r && in_array( $type, $no_types ) ){
		prd_nomenclatures_products_del( $prd );
	}

	return $r;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet le chargement du type de nomenclature d'un produit donné.
 *	@param int $prd Obligatoire, identifiant du produit à interroger
 *	@return int l'identifiant du type de nomenclature, ou false en cas d'échec
 */
function prd_products_get_nomenclature_type( $prd ){
	global $config;

	if( !is_numeric($prd) || $prd <= 0 ){
		return false;
	}

	if( $rprd = ria_mysql_query('select prd_nomenclature_type from prd_products where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd) ){
		if( ria_mysql_num_rows( $rprd ) ){
			return ria_mysql_result( $rprd,0,0 );
		}
	}

	return false;
}
// \endcond

// \cond onlyria
/** Cette fonction crée une option de nomenclature
 *	@param string $name Obligatoire, nom de l'option
 *	@return bool False en cas d'échec, identifiant de l'option crée en cas de succès
 */
function prd_options_add( $name ){
	global $config;

	$name = trim( $name );
	if( $name=='' ) return false;

	$sql = 'insert into prd_options ( opt_tnt_id, opt_name ) values ( '.$config['tnt_id'].', \''.addslashes( $name ).'\' )';

	if( ria_mysql_query( $sql ) ){
		return ria_mysql_insert_id();
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/** Cette fonction supprime une option de nomenclature
 *	La suppression des clés étrangères doit préalablement réussir pour pouvoir supprimer l'option
 *	@param int $id Obligatoire, identifiant de l'option
 *	@return bool False si l'identifiant n'est pas un entier valide, la suppression a échoué, la mise à jour des clés étrangères a échoué
 *	@return bool True si l'option avait déjà été supprimé ou si la suppression a réussi
 */
function prd_options_del( $id ){

	if( !is_numeric($id) && $id <= 0 ){
		return false;
	}
	if( !prd_options_exists( $id ) ){
		return true;
	}

	global $config;

	// retire les produits de la liste de choix de l'option
	$sql = '
		delete from prd_options_products
		where opt_tnt_id = '.$config['tnt_id'].'
			and opt_id = '.$id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	// charge les produits ayant cette option
	$sql = '
		select distinct prd_id as "id"
		from prd_nomenclatures_options
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_opt_id = '.$id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	$p_up = array();
	while( $p = ria_mysql_fetch_assoc($res) ){
		$p_up[] = $p['id'];
	}

	// retire l'option sur les produits concernés
	$sql = '
		delete from prd_nomenclatures_options
		where prd_tnt_id = '.$config['tnt_id'].'
			and prd_opt_id = '.$id.'
	';

	$res = ria_mysql_query($sql);
	if( !$res ){
		return false;
	}

	// supprime l'option
	$sql = '
		delete from prd_options
		where opt_tnt_id = '.$config['tnt_id'].'
			and opt_id = '.$id.'
	';

	$res = ria_mysql_query($sql);

	// met à jour la date de dernière modification des produits concernés
	if( $res && sizeof($p_up) ){
		prd_products_set_date_modified( $p_up );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction met à jour une option de nomenclature
 *	@param int $id Obligatoire, identifiant de l'option
 *	@param string $name Obligatoire, nouveau nom de l'option
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_options_upd( $id, $name ){

	if( !prd_options_exists( $id ) ){
		return false;
	}

	$name = trim($name);
	if( !$name ){
		return false;
	}

	// charge les produits disposant de cette option
	$rpo = prd_nomenclatures_options_get( 0, $id, null );
	if( !$rpo ){
		return false;
	}

	$p_up = array();
	while( $po = ria_mysql_fetch_assoc($rpo) ){
		$p_up[] = $po['id'];
	}

	global $config;

	$sql = '
		update prd_options
		set opt_name = "'.addslashes($name).'"
		where opt_tnt_id = '.$config['tnt_id'].'
			and opt_id = '.$id.'
	';

	$res = ria_mysql_query($sql);

	if( $res && sizeof($p_up) ){
		prd_products_set_date_modified( $p_up );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction détermine l'existence d'une option de nomenclature
 *	@param int $id Obligatoire, identifiant de l'option
 *	@return bool True si l'option existe, false sinon
 */
function prd_options_exists( $id ){
	global $config;
	if( !is_numeric($id ) || $id<=0 ) return false;

	$sql = 'select opt_id from prd_options where opt_tnt_id='.$config['tnt_id'].' and opt_id='.$id;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}
// \endcond

// \cond onlyria
/** Cette fonction retourne une/des option(s) et son/leur contenu(s)
 *	@param int $id Facultatif, identifiant de l'option
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête mySql, comprenant les champs suivants :
 *		- id, identifiant de l'option
 *		- name, nom de l'option
 */
function prd_options_get( $id=0 ){
	global $config;
	if( $id!==0 && !prd_options_exists( $id ) ) return false;

	$sql = 'select opt_id as id, opt_name as name from prd_options
		where opt_tnt_id='.$config['tnt_id'].'
	';

	if( $id!==0 )
		$sql .= ' and opt_id='.$id;

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction retourne le nom d'une option
 *	@param int $id Obligatoire, identifiant de l'option
 *	@return string Le nom de l'option, False si le paramètre obligatoire est omis ou faux
 */
function prd_options_get_name( $id ){
	if( !is_numeric($id) || $id<=0 ){
	    return false;
	}

	global $config;

	$sql = '
		select opt_name as name
		from prd_options
		where opt_tnt_id='.$config['tnt_id'].'
			and opt_id='.$id;

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['name'];
}
// \endcond

// \cond onlyria
/** Cette fonction crée une/des association(s) entre une option et un/des article(s) enfant(s)
 *	@param $opt Obligatoire, identifiant de l'option
 *	@param int|array $child Obligatoire, identifiant de l'article enfant ou tableau d'identifiants d'articles enfants
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_options_products_add( $opt, $child ){
	global $config;
	if( !prd_options_exists( $opt ) ) return false;

	$no_types = array(NM_TYP_COMPOSED, NM_TYP_VARIABLE);
	if( $config['prd_linked_nomenclature_activate'] ){
		$no_types[] = NM_TYP_LINKED;
	}

	if( is_array( $child ) ){
		foreach( $child as $c ){
			if( !prd_products_exists( $c ) ) return false;

			// refus des frais de port
			if( prd_products_is_port_id( $c ) ) return false;

			// refus des composants qui sont déjà des nomenclatures
			$typenom = prd_products_get_nomenclature_type( $c );
			if( in_array( $typenom, $no_types ) ) return false;
			if( prd_products_is_nomenclature($c) ) return false;
		}
	}else{
		if( !prd_products_exists( $child ) ) return false;

		// refus des frais de port
		if( prd_products_is_port_id( $child ) ) return false;

		// refus des composants qui sont déjà des nomenclatures
		$typenom = prd_products_get_nomenclature_type( $child );
		if( in_array( $typenom, $no_types ) ) return false;
		if( prd_products_is_nomenclature($child) ) return false;

		$child = array( $child );
	}

	$result = true;

	foreach( $child as $prd ){
		if( !prd_options_products_exists( $opt, $prd ) ){
			$sql = 'insert into prd_options_products ( opt_tnt_id, opt_id, prd_id ) values ( '.$config['tnt_id'].', '.$opt.', '.$prd.' )';
			if( !ria_mysql_query( $sql ) ){
				$result = false;
				break;
			}

			prd_products_set_date_modified( $prd );
		}
	}

	if( $result ){
		// met à jour la date de modification des produits concernés par l'option
		if( $rop = prd_nomenclatures_options_get( 0, $opt ) ){
			while( $op = ria_mysql_fetch_assoc($rop) ){
				prd_products_set_date_modified( $op['id'] );
			}
		}
	}

	return $result;
}
// \endcond

// \cond onlyria
/** Cette fonction supprime une/des association(s) entre une option et une/des produit(s)
 *	@param $opt Obligatoire, identifiant de l'option
 *	@param int $prd Facultatif, identifiant de produit, 0 pour supprimer toute les associations
 *	@return bool True en cas de succès, false sinon
 */
function prd_options_products_del( $opt, $prd=0 ){
	global $config;
	if( !is_numeric( $opt ) || $opt<=0 ) return false;
	if( !is_numeric( $prd ) || $prd<0 ) return false;

	$sql = 'delete from prd_options_products where opt_tnt_id='.$config['tnt_id'].' and opt_id='.$opt;
	if( $prd!==0 )
		$sql .= ' and prd_id='.$prd;

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	if( $prd ){
		prd_products_set_date_modified( $prd );
	}

	// met à jour la date de modification des produits concernés par l'option
	if( $rop = prd_nomenclatures_options_get( 0, $opt ) ){
		while( $op = ria_mysql_fetch_assoc($rop) ){
			prd_products_set_date_modified( $op['id'] );
		}
	}

	return true;
}
// \endcond

/** Cette fonction récupère la liste des enfants d'une option
 *	Il est possible d'améliorer cette fonction pour lui ajouter autant de champs que l'on veut
 *	@param $opt Facultatif, identifiant de l'option
 *	@param int $prd Facultatif, identifiant du produit
 *	@return bool False en cas d'erreur
 *	@return resource un résultat de requête mySQL comprenant les champs suivants :
 *		- opt, identifiant de l'option
 *		- name, nom de l'option
 *		- prd, identifiant  de l'article
 *		- prd-ref, référence de l'article
 *		- prd-name, libellé de l'article
 *		- prd-brand-id, identifiant de la marque de l'article
 *		- prd-brand-name, libellé de la marque de l'article
 */
function prd_options_products_get( $opt=0, $prd=0 ){
	global $config;
	if( $opt>0 && !prd_options_exists( $opt ) ) return false;
	if( $prd>0 && !prd_products_exists($prd) ) return false;

	$sql = 'select
		o.opt_id as opt,
		o.opt_name as name,
		p.prd_id as prd,
		p.prd_ref as "prd-ref",
		p.prd_name as "prd-name",
		p.prd_desc as "prd-desc",
		p.prd_brd_id as "prd-brand-id",
		b.brd_name as "prd-brand-name"

		from prd_options_products as oc
			join prd_options as o on ( o.opt_tnt_id=oc.opt_tnt_id and oc.opt_id=o.opt_id )
			join prd_products as p on ( p.prd_tnt_id=oc.opt_tnt_id and oc.prd_id=p.prd_id )
			left join prd_brands as b on ( oc.opt_tnt_id=b.brd_tnt_id and p.prd_brd_id=b.brd_id )

		where
			oc.opt_tnt_id='.$config['tnt_id'].'
	';

	if( $opt>0 ){
		$sql .= ' and oc.opt_id='.$opt;
	}
	if( $prd>0 ){
		$sql .= ' and oc.prd_id='.$prd;
	}
	return ria_mysql_query( $sql );
}

// \cond onlyria
/** Cette fonction détermine si une association produit/option existe
 *	@param $opt Obligatoire, identifiant de l'option
 *	@param int $prd Obligatoire, identifiant de l'article
 *	@return bool True si l'association existe, False sinon
 */
function prd_options_products_exists( $opt, $prd ){
	global $config;
	if( !prd_options_exists( $opt ) ) return false;
	if( !prd_products_exists( $prd ) ) return false;

	$sql = 'select opt_id, prd_id from prd_options_products where opt_tnt_id='.$config['tnt_id'].' and opt_id='.$opt.' and prd_id='.$prd;

	return ria_mysql_num_rows( ria_mysql_query( $sql ) );
}
// \endcond

// \cond onlyria
/** Cette fonction ajoute un composant à une nomenclature variable
 *	@param int $prd Obligatoire, identifiant de la nomenclature
 *	@param $opt Obligatoire, identifiant de l'option
 *	@param $qte Obligatoire, quantité de l'option pour un article nomenclaturé
 *	@param $free Facultatif, détermine si l'option est gratuite (vrai par défaut)
 *	@return bool True en cas de succès, False sinon ( y compris si l'article n'est pas une nomenclature variable )
 */
function prd_nomenclatures_options_add( $prd, $opt, $qte, $free=true ){
	global $config;
	if( !prd_products_exists( $prd ) ) return false;
	if( !prd_options_exists( $opt ) ) return false;
	if( !is_numeric( $qte ) ) return false;
	if( prd_products_get_nomenclature_type( $prd )!=NM_TYP_VARIABLE ) return false;

	if( prd_nomenclatures_options_exists( $prd,$opt ) ){
		return prd_nomenclatures_options_upd( $prd, $opt, $qte );
	}

	$sql = '
		insert into prd_nomenclatures_options (
			prd_tnt_id, prd_id, prd_opt_id, prd_opt_qte, prd_opt_free
		) values (
			'.$config['tnt_id'].','.$prd.','.$opt.','.$qte.', '.( $free ? 1 : 0 ).'
		)
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		prd_products_set_date_modified( $prd );
	}

	return $res;

}
// \endcond

// \cond onlyria
/** Cette fonction met à jour un composant de nomenclature variable
 *	@param int $prd Obligatoire, identifiant de la nomenclature
 *	@param $opt Obligatoire, identifiant de l'option
 *	@param $qte Obligatoire, quantité de l'option pour un article nomenclaturé
 *	@param $free Facultatif, détermine si l'option est gratuite (null par défaut, ne change pas la valeur)
 *	@return bool True en cas de succès, False sinon
 */
function prd_nomenclatures_options_upd( $prd, $opt, $qte, $free=null ){
	global $config;
	if( !is_numeric( $qte ) ) return false;
	if( !prd_nomenclatures_options_exists( $prd,$opt ) ) return false;

	$sql = 'update prd_nomenclatures_options set prd_opt_qte='.$qte.' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd.' and prd_opt_id='.$opt;

	$result = ria_mysql_query( $sql );

	if( $result && $free!==null ){
		$result = ria_mysql_query( 'update prd_nomenclatures_options set prd_opt_free='.( $free ? 1 : 0 ).' where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd.' and prd_opt_id='.$opt );
	}

	prd_products_set_date_modified( $prd );
	return $result;
}
// \endcond

// \cond onlyria
/** Cette fonction supprime un ou tout les composants d'une nomenclature variable
 *	@param int $prd Obligatoire, identifiant de la nomenclature
 *	@param $opt Facultatif, identifiant de l'option
 *	@return bool True si :
 *		- la suppression a réussi
 *		- tout les paramètres, bien que valides, ne composaient pas au préalable une/des ligne(s) dans la table
 *	@return bool False dans les autres cas
 */
function prd_nomenclatures_options_del( $prd, $opt=0 ){
	global $config;
	if( !prd_products_exists( $prd ) ) return false;
	if( $opt!==0 && !prd_options_exists( $opt ) ) return false;
	if( !prd_nomenclatures_options_exists( $prd,$opt ) ) return true;

	$sql = 'delete from prd_nomenclatures_options where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd;
	if( $opt!==0 )
		$sql .= ' and prd_opt_id='.$opt;

	prd_products_set_date_modified( $prd );
	return ria_mysql_query( $sql );
}
// \endcond

/** Cette fonction récupère le contenu d'une ou plusieurs nomenclature(s)
 *	Le résultat est trié par article parent puis par option, il faudrait ajouter un paramètre optionnel de tri ( attention à ne pas changer le tri par défaut )
 *
 *	@param int $prd Facultatif, identifiant de la nomenclature
 *	@param $opt Facultatif, identifiant de l'option
 *	@param $free Par défaut toutes les options sont affichées, mettre à true pour les options gratuites à false pour les options payantes
 *	@param $order Facultatif, permet de trier sur une ou plusieurs colonnes. Exemple : array('col_name1', 'col_name2')
 *	@return bool False en cas d'échec
 *	@return resource un résultat de requête mySQL comprenant les champs suivants :
 *		- id, identifiant de la nomenclature
 *		- name, nom de l'article nomenclaturé
 *		- ref, référence du produit
 *		- opt, identifiant de l'option
 *		- opt-name, nom de l'option
 *		- qte, quantité de l'option pour une unité de nomenclature
 *		- free, Détermine si l'option est gratuite
 */
function prd_nomenclatures_options_get( $prd=0, $opt=0, $free=null, $order=array() ){
	global $config;

	if( !is_numeric($prd) || $prd < 0 ){
		return false;
	}
	if( !is_numeric($opt) || $opt < 0 ){
		return false;
	}
	if( $prd!==0 && $opt!==0 && !prd_nomenclatures_options_exists( $prd,$opt ) ) return false;
	if( $prd===0 && $opt!==0 ){
		if( !prd_options_exists( $opt ) ) return false;
	}


	$sql = '
		select
			p.prd_id as id,
			p.prd_name as name,
			p.prd_ref as ref,
			o.opt_id as opt,
			o.opt_name as "opt-name",
			no.prd_opt_qte as qte,
			no.prd_opt_free as free
		from
			prd_products as p,
			prd_options as o,
			prd_nomenclatures_options as no
		where
			p.prd_tnt_id='.$config['tnt_id'].' and
			o.opt_tnt_id=p.prd_tnt_id and
			no.prd_tnt_id=p.prd_tnt_id and
			no.prd_id=p.prd_id and
			no.prd_opt_id=o.opt_id
	';

	if( $prd!==0 )
		$sql .= ' and p.prd_id='.$prd;
	if( $opt!==0 )
		$sql .= ' and o.opt_id='.$opt;

	if( $free!==null ){
		$sql .= ' and no.prd_opt_free='.( $free ? 1 : 0 );
	}

	if(sizeof($order)){
		$sql .= ' order by';
		$count = 0;
		foreach($order as $key => $name){
			if ($count){
				$sql .= ',';
			}
			$sql .= ' '.$name;
			$count++;
		}
	} else {
		$sql .= ' order by p.prd_id, o.opt_id';
	}

	return ria_mysql_query( $sql );
}

// \cond onlyria
/** Cette fonction détermine l'existence d'une ligne de composant de nomenclature variable
 *	@param int $prd Obligatoire, identifiant de la nomenclature
 *	@param $opt Facultatif, identifiant de l'option
 *	@param $free Facultatif, booléen indiquant si l'emplacement est libre ou non
 *	@return bool True si la ligne existe, False sinon
 */
function prd_nomenclatures_options_exists( $prd, $opt=0, $free=null ){
	global $config;
	if( !prd_products_exists( $prd ) ) return false;
	if( $opt!==0 && !prd_options_exists( $opt ) ) return false;

	$sql = 'select prd_id from prd_nomenclatures_options where prd_tnt_id='.$config['tnt_id'].' and prd_id='.$prd;
	if( $opt!==0 )
		$sql .= ' and prd_opt_id='.$opt;
	if( $free!=null ){
		$sql .= ' and prd_opt_free='.($free ? 1 : 0);
	}

	return ria_mysql_num_rows( ria_mysql_query( $sql ) ) > 0;
}
// \endcond

// \cond onlyria
/**	Détermine si un parent de nomenclature est lié avec un paramètre spécifique d'un enfant
 *	Les trois arguments facultatifs ne peuvent être à 0 en même temps, et sont classés dans l'ordre de priorité de prise en compte
 *	@param int $parent Obligatoire, identifiant du produit parent
 *	@param int $child Facultatif, identifiant d'un produit enfant
 *	@param $cat_child Facultatif, identifiant de la famille d'un produit enfant
 *	@param $brd_child Faculatif, identifiant de la marque d'un produit enfant
 *	@param $free Facultatif, par défaut, on ne tient pas contre de l'option gratuit, mettre true pour que l'option soit gratuite, false pour qu'elle soit payante
 *	@return bool True si la liaison existe, False sinon
 */
function prd_nomenclatures_is_linked( $parent, $child=0, $cat_child=0, $brd_child=0, $free=null ){
	global $config;

	if( $child!==0 && !prd_products_exists( $child ) ) return false;
	if( $cat_child!==0 && !prd_categories_exists( $cat_child ) ) return false;
	if( $brd_child!==0 && !prd_brands_exists( $brd_child ) ) return false;
	if( $child===0 && $cat_child===0 && $brd_child===0 ) return false;
	if( !prd_products_exists( $parent ) ) return false;

	$sql = '
		select no.prd_id
		from prd_nomenclatures_options as no
			join prd_options_products as op on ( opt_tnt_id=prd_tnt_id and prd_opt_id=opt_id )
		where prd_tnt_id='.$config['tnt_id'].' and no.prd_id='.$parent.'
	';

	if( $child!==0 )
		$sql .= ' and op.prd_id='.$child;
	elseif( $cat_child!==0 )
		$sql .= ' and op.prd_id in ( select cly_prd_id from prd_classify where cly_tnt_id=opt_tnt_id and cly_cat_id='.$cat_child.' )';
	elseif( $brd_child!==0 )
		$sql .= ' and op.prd_id in ( select p.prd_id from prd_products as p where p.prd_tnt_id=opt_tnt_id and prd_brd_id='.$brd_child.' )';

	if( $free!=null ){
		$sql .= ' and no.prd_opt_free = '.( $free ? 1 : 0);
	}

	$result = ria_mysql_query( $sql );

	if( !$result ) return false;

	return ria_mysql_num_rows( $result );
}
// \endcond

// \cond onlyria
/**	Retourne le détails sur une nomenclature
 *	Les quatres arguments facultatifs ne peuvent être à 0 en même temps, et sont classés dans l'ordre de priorité de prise en compte
 *	@param int $parent Obligatoire, identifiant du produit parent
 *	@param int $child Facultatif, identifiant d'un produit enfant
 *	@param $cat_child Facultatif, identifiant de la famille d'un produit enfant
 *	@param $brd_child Faculatif, identifiant de la marque d'un produit enfant
 *	@param $cat_exclude Facultatif, identifiant d'une famille de produit à ne pas tenir compte
 *	@return bool True si la liaison existe, False sinon
 */
function prd_nomenclatures_details( $parent, $child=0, $cat_child=0, $brd_child=0, $cat_exclude=0 ){
	global $config;

	if( $child!==0 && !prd_products_exists( $child ) ) return false;
	if( $cat_child!==0 && !prd_categories_exists( $cat_child ) ) return false;
	if( $cat_exclude!==0 && !prd_categories_exists( $cat_exclude ) ) return false;
	if( $brd_child!==0 && !prd_brands_exists( $brd_child ) ) return false;
	if( $child===0 && $cat_child===0 && $brd_child===0 && $cat_exclude===0 ) return false;
	if( !prd_products_exists( $parent ) ) return false;

	$sql = '
		select no.prd_id as prd, no.prd_opt_id as opt, o.opt_name as "opt-name", no.prd_opt_free as free, op.prd_id as opt_prd
		from prd_nomenclatures_options as no
			join prd_options_products as op on ( op.opt_tnt_id=prd_tnt_id and prd_opt_id=op.opt_id )
			join prd_options as o on( o.opt_tnt_id=op.opt_tnt_id and o.opt_id=op.opt_id )
		where prd_tnt_id='.$config['tnt_id'].' and no.prd_id='.$parent.'
	';

	if( $child!==0 )
		$sql .= ' and op.prd_id='.$child;
	elseif( $cat_child!==0 )
		$sql .= ' and op.prd_id in ( select cly_prd_id from prd_classify where cly_tnt_id=op.opt_tnt_id and cly_cat_id='.$cat_child.' '.( $cat_exclude!==0 ? 'and cly_cat_id<>'.$cat_exclude : '' ).')';
	elseif( $cat_child==0 && $cat_exclude!==0 )
		$sql .= ' and op.prd_id not in ( select cly_prd_id from prd_classify where cly_tnt_id=op.opt_tnt_id and cly_cat_id='.$cat_exclude.' )';
	elseif( $brd_child!==0 )
		$sql .= ' and op.prd_id in ( select p.prd_id from prd_products as p where p.prd_tnt_id=op.opt_tnt_id and prd_brd_id='.$brd_child.' )';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Récupère les informations de nomenclature, si elles existent, à partir d'un couple parent / enfant(s)
 *	@param int $parent Identifiant du produit parent
 *	@param int $child Identifiant du produit parent ou tableau d'identifiants d'enfants
 *	@return bool False en cas d'échec
 *	@return Un Résultat de requête MySQL comprenant les champs suivants :
 *		- opt_id : Identifiant de l'option joignant le parent et l'enfant
 *		- qte : Quantité en conditionnement pour une unité du parent
 *		- free : Détermine si l'enfant est gratuit pour l'achat du parent
 */
function prd_nomenclatures_get_childs_details( $parent, $child ){
	global $config;

	if( !is_numeric($parent) || $parent<=0 ) return false;

	if( is_array($child) ){
		foreach( $child as $c ){
			if( !is_numeric($c) || $c<=0 ) return false;
		}
	}else{
		if( !is_numeric($child) || $child<=0 ) return false;
		$child = array( $child );
	}

	$sql = '
		select
			no.prd_opt_id as opt_id, no.prd_opt_qte as qte, no.prd_opt_free as free
		from
			prd_nomenclatures_options as no join
			prd_options_products as op on ( op.opt_tnt_id=no.prd_tnt_id and no.prd_opt_id=op.opt_id )
		where
			no.prd_tnt_id='.$config['tnt_id'].' and
			no.prd_id='.$parent.' and
			op.prd_id in ('.implode( ', ', $child ).')
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/**	Cette fonction crée une nomenclature fixe, ou le cas échéant une relation de type "articles liés".
 *	La nomenclature est propre à RiaShop, elle ne peut pas s'appliquer sur les produits de la gestion dont le type de nomenclature est :
 *		- fabrication
 *		- commerciale composé
 *		- variable
 *	La relation d'articles liés est, elle, synchronisée avec la gestion : le type de nomenclature du parent est alors 4 / NM_TYP_LINKED.
 *	Si le produit parent est une nomenclature (fabrication, commerciale, variable), les produits enfants ne peuvent pas être des nomenclatures.
 *	Le produit parent ne peut pas être un frais de port.
 *	@param int $parent_id Identifiant du produit nomenclaturé ou lié.
 *	@param array $childs Tableau de tableaux associatifs des composants de la nomenclature ou de l'article lié. Chaque tableau associatif est composé de la manière suivante :
 *		- "prd" : obligatoire, identifiant de l'article. S'il est présent plusieurs fois, il ne sera pris en compte qu'une fois.
 *		- "qte" : optionnel, quantité pour un parent (par défaut 1).
 *		- "price_ht" : optionnel, prix indicatif du composant dans la nomenclature. Ne s'applique pas pour les liaisons d'un article lié.
 *	@param bool $is_sync Determine si c'est un ajout par la synchro ou non
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_nomenclatures_products_add( $parent_id, $childs, $is_sync=false ){

	if( !is_array($childs) || !sizeof($childs) ){
		return false;
	}
	if( !prd_products_exists( $parent_id ) ){
		return false;
	}
	if( prd_products_is_port_id( $parent_id ) ){
		return false;
	}

	// les types ci-dessous ne sont pas autorisés pour le parent
	// ils sont autorisés pour les enfants uniquement dans le cas d'articles liés
	$forbidden_types = array(NM_TYP_BUILT, NM_TYP_VARIABLE);

	$parent_type = prd_products_get_nomenclature_type( $parent_id );
	if( in_array( $parent_type, $forbidden_types ) ){
		return false;
	}

	global $config;

	$proper_childs = array();
	foreach( $childs as $child_array ){

		if( !is_array($child_array) || !sizeof($child_array) ){
			return false;
		}

		$prd_id = 0; $qte = 1; $price = null;
		foreach( $child_array as $k => $v ){
			switch( strtolower(trim($k)) ){
				case 'prd':
					if( !prd_products_exists( $v ) ){
						return false;
					}

					// test effectué si le parent n'est pas un article lié ou si ceux-ci ne sont pas activés
					// si le parent est un composé optionnel, tout est autorisé en enfant
					if( $parent_type != NM_TYP_COMPOSED_OPT && ( !$config['prd_linked_nomenclature_activate'] ||  $parent_type != NM_TYP_LINKED ) ){
						$child_type = prd_products_get_nomenclature_type( $v );
						if( in_array($child_type, $forbidden_types) || $child_type == NM_TYP_COMPOSED ){
							return false;
						}
					}

					$prd_id = $v;
					break;
				case 'qte':
					if( !is_numeric($v) || $v <= 0 ){
						return false;
					}
					$qte = $v;
					break;
				case 'price_ht':
					if( $v !== null ){
						$v = str_replace(array(',', ' '), array('.', ''), $v);
						if( !is_numeric($v) ){
							return false;
						}
					}
					$price = $v;
					break;
			}
		}

		if( !$prd_id ){
			return false;
		}

		if( !array_key_exists($prd_id, $proper_childs) ){
			$proper_childs[$prd_id] = array($qte, $price);
		}

	}

	$prd_unique = array();
	foreach( $proper_childs as $child_id => $ar ){

		// recherche et supprime une ligne déjà existante
		$sql = '
			select 1 from prd_nomenclatures_products
			where pnp_tnt_id = '.$config['tnt_id'].' and pnp_parent_id = '.$parent_id.' and pnp_child_id = '.$child_id.' and pnp_is_sync='.($is_sync ? 1 : 0).'
		';

		$r_exist = ria_mysql_query($sql);

		if( $r_exist && ria_mysql_num_rows($r_exist) ){

			$sql = '
				delete from prd_nomenclatures_products
				where pnp_tnt_id = '.$config['tnt_id'].' and pnp_parent_id = '.$parent_id.' and pnp_child_id = '.$child_id.' and pnp_is_sync='.($is_sync ? 1 : 0).'
			';

			ria_mysql_query($sql);

		}

		$sql = '
			insert into prd_nomenclatures_products
				(pnp_tnt_id, pnp_parent_id, pnp_child_id, pnp_qte, pnp_price_ht, pnp_is_sync)
			values
				('.$config['tnt_id'].', '.$parent_id.', '.$child_id.', '.$ar[0].', '.( $ar[1] === null ? 'NULL' : $ar[1] ).', '.($is_sync ? 1 : 0).')
		';

		ria_mysql_query($sql);

		$prd_unique[] = $child_id;

	}

	$prd_unique[] = $parent_id;
	prd_products_set_date_modified( $prd_unique );

	prd_nomenclatures_products_rebuild_stock( $parent_id );

	return true;

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère des informations sur la composition d'une nomenclature
 *	@param int $prd_id Identifiant du produit nomenclaturé
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- prd_id : identifiant du produit
 *		- id : identifiant du produit enfant
 *		- qte : quantité du produit enfant pour une unité du parent
 *		- price_ht : prix unitaire du composant dans la nomenclature
 *		- is_sync : détermine si la liaison est synchro ou non
 */
function prd_nomenclatures_products_get( $prd_id ){
	global $config;

	if( !is_numeric($prd_id) || $prd_id<=0 ){
		return false;
	}

	return ria_mysql_query('
		select pnp_parent_id as prd_id, pnp_child_id as id, pnp_qte as qte, pnp_price_ht as price_ht, pnp_is_sync as is_sync
		from prd_nomenclatures_products
		where pnp_tnt_id='.$config['tnt_id'].'
			and pnp_parent_id='.$prd_id.'
	');

}
// \endcond

// \cond onlyria
/**	Cette fonction récupère les articles parents du nomenclature
 *	@param int $prd_id Identifiant du produit nomenclaturé
 *	@param int $ord_id Optionnel, identifiant d'une commande (seul les parents dans celle-ci seront retournés)
 *	@return bool False en cas d'échec
 *	@return array Un tableau contenant les identifiants des articles parents
 */
function prd_nomenclatures_get_parents( $prd_id, $ord_id=false ){
	if( !is_numeric($prd_id) || $prd_id<=0 ) return false;

	global $config;

	$sql = '
		select pnp_parent_id as prd_id
		from prd_nomenclatures_products
	';

	if ($ord_id > 0) {
		$sql .= ' join ord_products on (pnp_tnt_id = prd_tnt_id and pnp_parent_id = prd_id and prd_ord_id = '.$ord_id.')';
	}

	$sql .= '
		where pnp_tnt_id='.$config['tnt_id'].'
			and pnp_child_id='.$prd_id.'
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$parents = array();
	while( $r = ria_mysql_fetch_assoc($res) ){
		$parents[] = $r['prd_id'];
	}

	return $parents;
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère la quantité unitaire d'un composant dans une nomenclature
 *	@param int $parent_id Identifiant du produit nomenclaturé
 *	@param $child_id Identifiant du produit composant
 *	@return La quantité du composant pour une nomenclature, False en cas d'échec
 */
function prd_nomenclatures_products_get_qte( $parent_id, $child_id ){
	if( !is_numeric($parent_id) || $parent_id <= 0 ){
		return false;
	}
	if( !is_numeric($child_id) || $child_id <= 0 ){
		return false;
	}

	global $config;

	$sql = '
		select pnp_qte
		from prd_nomenclatures_products
		where pnp_tnt_id = '.$config['tnt_id'].' and pnp_parent_id = '.$parent_id.'
		and pnp_child_id = '.$child_id.'
	';

	$r = ria_mysql_query($sql);

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 0);
}
// \endcond

// \cond onlyria
/**	Cette fonction supprime complètement la composition d'une nomenclature. Elle est notamment utile quand la valeur "prd_nomenclature_type" d'un article change pour devenir une composition spécifique à la gestion commerciale (1, 2, 4 ou 5)
 *	@param int $parent Obligatoire, identifiant du produit nomenclaturé
 *	@param int $child Optionnel, identifiant d'un composant
 *	@param bool $is_sync Optionnel, détermine si c'est la sync ou non
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_nomenclatures_products_del( $parent, $child=false, $is_sync=false ){
	if( !is_numeric($parent) || $parent<=0 ) return false;
	if( $child!==false && ( !is_numeric($child) || $child<=0 ) ) return false;

	global $config;

	$sql = '
		delete from prd_nomenclatures_products
		where pnp_tnt_id='.$config['tnt_id'].' and pnp_parent_id='.$parent.' and pnp_is_sync='.($is_sync ? 1 : 0).'
	';
	if( $child!==false )
		$sql .= ' and pnp_child_id='.$child;

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	prd_nomenclatures_products_rebuild_stock( $parent );

	prd_products_set_date_modified( $parent );
	if( $child ){
		prd_products_set_date_modified( $child );
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction met à jour la quantité d'un enfant sur une nomenclature existante
 *	@param int $parent_id Identifiant de l'article nomenclaturé
 *	@param $child_id Identifiant de l'article composant
 *	@param $qte Nouvelle quantité assignée. 0 Permet de supprimer l'article de la composition
 *	@param bool $is_sync Optionnel, détermine si c'est la sync ou non
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_nomenclatures_products_update_qte( $parent_id, $child_id, $qte, $is_sync=false ){
	if( !is_numeric($parent_id) || $parent_id<=0 ) return false;
	if( !is_numeric($child_id) || $child_id<=0 ) return false;
	if( !is_numeric($qte) || $qte<0 ) return false;

	global $config;

	$sql = 'update prd_nomenclatures_products set pnp_qte='.$qte.' where pnp_tnt_id='.$config['tnt_id'].' and pnp_parent_id='.$parent_id.' and pnp_child_id='.$child_id.' and pnp_is_sync='.($is_sync ? 1 : 0);
	if( !$qte ){
		$sql = 'delete from prd_nomenclatures_products where pnp_tnt_id='.$config['tnt_id'].' and pnp_parent_id='.$parent_id.' and pnp_child_id='.$child_id.' and pnp_is_sync='.($is_sync ? 1 : 0);
	}

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	prd_nomenclatures_products_rebuild_stock( $parent_id );

	prd_products_set_date_modified( array($parent_id, $child_id) );
	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour le prix indicatif d'un composant d'une nomenclature
 *	@param int $parent_id Identifiant du produit parent
 *	@param $child_id Identifiant du produit enfant
 *	@param $price Prix indicatif HT du produit enfant dans la nomenclature. La valeur NULL est autorisée
 *	@param bool $is_sync Optionnel, détermine si c'est la sync ou non
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_nomenclatures_products_update_price( $parent_id, $child_id, $price, $is_sync=false ){
	if( !is_numeric($parent_id) || $parent_id <= 0 ){
		return false;
	}
	if( !is_numeric($child_id) || $child_id <= 0 ){
		return false;
	}
	if( $price !== null ){
		$price = str_replace(array(',', ' '), array('.', ''), $price);
		if( !is_numeric($price) ){
			return false;
		}
	}

	global $config;

	$sql = '
		update prd_nomenclatures_products
		set pnp_price_ht = '.( $price === null ? 'NULL' : $price ).'
		where pnp_tnt_id = '.$config['tnt_id'].'
			and pnp_parent_id = '.$parent_id.'
			and pnp_child_id = '.$child_id.'
		 	and pnp_is_sync='.($is_sync ? 1 : 0).'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		prd_products_set_date_modified( array($parent_id, $child_id) );
	}

	return $res;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de mettre à jour le is_sync
 *	@param int $parent_id Identifiant du produit parent
 *	@param $child_id Identifiant du produit enfant
 *	@param $price Prix indicatif HT du produit enfant dans la nomenclature. La valeur NULL est autorisée
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_nomenclatures_products_update_sync( $parent_id, $child_id ){
	if( !is_numeric($parent_id) || $parent_id <= 0 ){
		return false;
	}
	if( !is_numeric($child_id) || $child_id <= 0 ){
		return false;
	}
	global $config;

	$sql = '
		update prd_nomenclatures_products
		set pnp_is_sync = 1
		where pnp_tnt_id = '.$config['tnt_id'].'
			and pnp_parent_id = '.$parent_id.'
			and pnp_child_id = '.$child_id.'
	';

	$res = ria_mysql_query($sql);

	if( $res ){
		prd_products_set_date_modified( array($parent_id, $child_id) );
	}

	return $res;

}
// \endcond

/**	Cette fonction détermine le prix d'une nomenclature fixe dont le parent n'a pas de prix assigné (dans quel cas on fait la somme du prix des enfants)
 *	La fonction ne vérifie pas si le parent à un prix assigné (il faut utiliser prd_products_is_nomenclature() en amont)
 *	@param int $prd Obligatoire, identifiant de l'article parent
 *	@param int $usr Optionnel, identifiant du client
 *	@param int $prc Optionnel, identifiant de la catégorie tarifaire (si usr non spécifié)
 *	@param int $ord Optionnel, identifiant de la commande (peut impacter le taux de TVA)
 *	@param $qte Optionnel, quantité souhaitée pour le produit parent
 *	@param bool $publish Optionnel, mettre true pour ne tenir compte que des articles liés publiés
 *	@return bool False en cas d'échec
 *	@return array Un tableau associatif comprenant les clés suivantes :
 *		- price_ht : Prix HT du produit
 *		- price_ttc : prix TTC du produit
 *	\bug Via cette fonction, il est possible de récupérer le tarif HT et le tarif TTC de l'article parent, mais pas son taux de TVA (qui peut être composite)
 */
function prd_nomenclatures_products_get_price( $prd, $usr=0, $prc=0, $ord=0, $qte=1, $publish=false ){
	global $config;

	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !is_numeric($usr) || $usr<0 ) return false;
	if( !is_numeric($prc) || $prc<0 ) return false;
	if( !is_numeric($ord) || $ord<0 ) return false;
	if( !is_numeric($qte) || $qte<1 ) return false;

	if( $ord>0 ){
		require_once('orders.inc.php');

		$rord = ord_orders_get( 0, $ord );
		if( $rord===false || !ria_mysql_num_rows($rord) ) return false;
		$ord_array = ria_mysql_fetch_array($rord);

		// contrôle que le client de la commande et le client spécifié sont les mêmes
		if( $usr && $ord_array['user'] && $ord_array['user']!=$usr ) return false;
	}

	$usr = !isset($_SESSION['usr_tnt_id']) || (isset($_SESSION['usr_tnt_id']) && $_SESSION['usr_tnt_id']>0) ? $usr : 0;

	$exempt = gu_users_is_tva_exempt( $usr );

	$usr_holder = gu_users_get_prices_holder( $usr );
	if( $usr_holder || !$prc ){
		$prc = gu_users_get_prc( $usr_holder, true );
	}
	$usr_ref = gu_users_get_ref( $usr_holder, true );

	$lng_base = wst_websites_languages_default();

	$apply_remise_ecotaxe = 0;
	if( isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ){
		$apply_remise_ecotaxe = prd_products_get_ecotaxe( $prd );

		if( !is_numeric($apply_remise_ecotaxe) || $apply_remise_ecotaxe <= 0 ){
			$apply_remise_ecotaxe = 0;
		}
	}

	$sql = '
		select
			sum(
				get_price_ht( pnp_tnt_id, pnp_child_id, '.$usr_holder.', '.$qte.', 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.$lng_base.'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", -1, NULL, '.$apply_remise_ecotaxe.' ) * pnp_qte
			) as price_ht, sum(
				get_price_ht( pnp_tnt_id, pnp_child_id, '.$usr_holder.', '.$qte.', 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.$lng_base.'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", -1, NULL, '.$apply_remise_ecotaxe.' ) *
				'.( $exempt ? '1' : 'get_tva( pnp_tnt_id, pnp_child_id, '.gu_users_get_accouting_category( $usr, true ).', '.gu_users_get_cnt_code($usr, true).' )' ).'
				* pnp_qte
			) as price_ttc
		from
			prd_nomenclatures_products
			join prd_products on (prd_tnt_id = pnp_tnt_id and prd_id = pnp_child_id)
		where
			pnp_tnt_id='.$config['tnt_id'].' and pnp_parent_id='.$prd.'
			and prd_date_deleted is null
	';

	if ($publish) {
		$sql .= ' and prd_publish and prd_publish_cat';
	}

	$r = ria_mysql_query($sql);

	// débugage tva
	if( ria_mysql_errno() ){
		error_log('prd_nomenclatures_products_get_price - '.mysql_error().' - '.$sql);
	}

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	$res = ria_mysql_fetch_array($r);

	return array_merge( $res, array('tva_rate' => $res['price_ttc'] / ($res['price_ht'] > 0 ? $res['price_ht'] : 1)) );
}

// \cond onlyria
/**	Cette fonction met à jour le stock d'une nomenclature (non synchronisée, et non variable)
 *	Un des arguments doit être spécifié, mais pas les deux
 *	Ne gère pas le multi-dépôts (Boero)
 *	@param int $parent_id Optionnel, identifiant de la nomenclature à mettre à jour
 *	@param $child_id Optionnel, identifiant d'un produit simple dont on souhaite mettre à jour les nomenclatures qui le contiennent
 *
 *	@return bool True en cas de succès, False en cas d'échec
 */
function prd_nomenclatures_products_rebuild_stock( $parent_id = 0, $child_id = 0 ){
	if( !is_numeric($parent_id) || $parent_id < 0 ){
		return false;
	}
	if( !is_numeric($child_id) || $child_id < 0 ){
		return false;
	}
	if( !$parent_id && !$child_id ){
		return false;
	}elseif( $parent_id && $child_id ){
		return false;
	}

	global $config;

	if( $child_id ){

		// récupération des nomenclatures contenant ce produit, mise à jour de chaque nomenclature
		$sql = '
			select distinct pnp_parent_id as "id"
			from prd_nomenclatures_products
			where pnp_child_id = '.$child_id.' and pnp_tnt_id = '.$config['tnt_id'].'
		';

		$error = false;
		if( $rparent = ria_mysql_query($sql) ){
			while( $parent = ria_mysql_fetch_array($rparent) ){
				if( !prd_nomenclatures_products_rebuild_stock( $parent['id'] ) ){
					$error = true;
				}
			}
		}else{
			$error = true;
		}

		return $error;

	}

	$rprd = prd_products_get_simple( $parent_id );
	if( !$rprd || !ria_mysql_num_rows($rprd) ){
		return false;
	}
	$prd = ria_mysql_fetch_array($rprd);

	// ne prend pas en charge les nomenclatures synchronisées
	if( $prd['is_sync'] ){
		return false;
	}

	// le produit n'est pas une nomenclature simple
	if( !prd_products_is_nomenclature( $prd['id'] ) ){
		return false;
	}

	// le produit n'est pas suivi en stock (return True)
	if( !$prd['follow_stock'] ){
		return true;
	}

	$dps = prd_deposits_get_main();
	if( !is_numeric($dps) || $dps <= 0 ){
		return false;
	}

	$sql = '
		select
			min(floor(' . prd_stocks_get_sql() . ' / pnp_qte)) as "qte", min(floor(ifnull('.prd_stocks_sto_res().', 0) / pnp_qte)) as "res",
			min(floor(ifnull(sto_com, 0) / pnp_qte)) as "com", min(floor(ifnull(sto_prepa, 0) / pnp_qte)) as "prepa"
		from prd_products as par
		join prd_nomenclatures_products
			on par.prd_id = pnp_parent_id and par.prd_tnt_id = pnp_tnt_id
		join prd_products as ch
			on pnp_child_id = ch.prd_id and pnp_tnt_id = ch.prd_tnt_id
		left join prd_stocks
			on ch.prd_id = sto_prd_id and ch.prd_tnt_id = sto_tnt_id and sto_is_deleted=0
		where
			par.prd_tnt_id = '.$config['tnt_id'].' and par.prd_id = '.$prd['id'].'
			and ch.prd_date_deleted is null and ch.prd_follow_stock > 0
			and ifnull(sto_dps_id, '.$dps.') = '.$dps.'
	';

	$rstock = ria_mysql_query($sql);

	if( !$rstock || !ria_mysql_num_rows($rstock) ){
		if( ria_mysql_errno() ){
			error_log(__FILE__.':'.__LINE__.' '.mysql_error().' - '.$sql);
		}
		return false;
	}

	$stock = ria_mysql_fetch_array($rstock);

	if( prd_dps_stocks_exists( $prd['id'], $dps ) ){
		return prd_dps_stocks_update( $prd['id'], $dps, $stock['qte'], $stock['res'], $stock['com'], $stock['prepa'], 0, 0 );
	}

	return prd_dps_stocks_add( $prd['id'], $dps, $stock['qte'], $stock['res'], $stock['com'], $stock['prepa'], 0, 0 );
}
// \endcond

/// @}

