<?php

	/**	\file search-customers.php
	 * 
	 * 	Ce fichier est utilisé en Ajax pour fournir un résultat de recherche de comptes clients, retourné
	 * 	en fonction du paramètre de recherche suivant :
	 * 	- term : requête de recherche
	 * 
	 * 	Son résultat est utilisé pour présenter une liste d'auto-complétion
	 * 
	 * 	Il est utilisé à trois emplacements :
	 * 	- pour faciliter la création d'une nouvelle relation
	 *  - pour choisir un compte parent (sans précédent)
	 *  - pour choisir un nouveau compte parent
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER');

	header('Content-type: text/json');
	header('Content-type: application/json');
	
	$ar_search_customers = search(
		array(
			'seg' => 1,
			'action' => 4,
			'type' => array('usr'),
			'keywords' => $_GET['term'],
			'published' => false
		)
	);
	
	$result = array();
	if (is_array($ar_search_customers)) {
		foreach ($ar_search_customers as $res) {
			$user = $res['get'];
			$name = trim(gu_users_get_name( $user['id'] ));
			
			$result[] = $user['email'].' - '.$name;
		}
	}
	
	print json_encode($result);
