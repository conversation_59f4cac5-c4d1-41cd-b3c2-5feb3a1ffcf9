<?php
	require_once('users.inc.php');
	require_once('define.inc.php');

	/**	\defgroup all_payments Moyens de paiements
	 * 	\ingroup cpq
	 */

	/**	\defgroup payment_external Paiement externalisés
	 * 	\ingroup all_payments marketplace
	 *	Ce module comprend toutes les classes nécessaires à la gestion des moyens de paiement externalisés.
	 *	@{
	 */

	/**
	 * \brief Cette classe abstraite défini une interface commune pour tous les moyens de paiement externalisés
	 * Tous les moyens de paiement (Cyberplus, PayPal, SystemPay, etc.) devraient hériter de cette classe
	 */
	abstract class PaymentExternal {

		/// Contexte d'exécution : Développement
		const CONTEXT_DEV	=	'DEV';
		/// Contexte d'exécution : Production
		const CONTEXT_PROD	=	'PROD';

		//Tableau des données pour couchDb
		protected $data_couchDB = array(
			'date' => '',
			'ord_id' => 0,
			'ord_total_ht' => 0,
			'ord_total_ttc' => 0,
			'user_id' => 0,
			'user_firstname' => '',
			'user_lastname' => '',
			'user_email' => '',
			'data' => '',
		);

		/**
		 *	Redirige l'utilisateur sur la page de paiment du service bancaire
		 *	Si une erreur se produit, la méthode lève une exception contenant une description de l'erreur.
		 *
		 *	\attention Cette méthode doit être obligatoirement surchargée par les classe filles.
		 *
		 *	@todo La portée de cette fonction devrait être \c protected et pas \c public
		 *
		 *	@return void
		 */
		abstract public function _doPayment();

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lève une exception contenant une description de l'erreur
		 *
		 *	\attention Cette méthode doit être obligatoirement surchargée par les classe filles.
		 *
		 *	@todo La portée de cette fonction devrait être \c protected et pas \c public
		 *
		 *	@return object L'instance
		 */
		abstract public function _getPaymentResult();

		/**
		 * Décode les paramètres contenus dans une url (query string) et les retournent sous la forme d'un tableau associatif
		 * Ex : a=b&c=d -> [a => b, c => d]
		 * @param string $params La chaîne à décoder
		 * @return array Tableau de paramètres
		 */
		protected function decodeParams( $params ){
			$t = explode('&', $params);
			$result = array();
			foreach ($t as $v) {
				$v = explode('=', $v);
				if (sizeof($v) !== 2) throw new Exception('decodeParams ne peut pas décoder ' . $params . ' !');
				$result[urldecode($v[0])] = urldecode($v[1]);
			}
			return $result;
		}

		/**
		 * Encode un tableau associatif en query string pouvant être utilisée dans une url
		 * Ex : [a => b, c => d] -> a=b&c=d
		 * @param array $params Tableau de paramètres
		 * @return string La chaîne encodée
		 */
		protected function encodeParams( array $params ){
			$r = array();
			foreach ($params as $key => $v) $r[$key] = urlencode($key) . '=' . urlencode($v);
			return implode('&', $r);
		}

		/**
		 * Renvoie le contexte dev (PaymentExternal::CONTEXT_DEV) ou prod (PaymentExternal::CONTEXT_PROD) en se basant sur le domaine
		 * @return string le contexte d'exécution
		 */
		public static function getContext(){
			// Regarde si le contexte est donné
			global $config;

			if( !empty($config['context']) ){
				$context = $config['context'];
				self::validContext($context);
				return $context;
			}

			// Sinon on le détermine par le domaine
			$devs = array('.maquettes.riastudio.fr', '.lossantos.riastudio.fr', '.forge.riastudio.fr', '.dev.fr', '.recette.fr');
			foreach( $devs as $t ){
				if( strpos($_SERVER['HTTP_HOST'], $t) !== false){
					return PaymentExternal::CONTEXT_DEV;
				}
			}
			return PaymentExternal::CONTEXT_PROD;
		}

		/**
		 *	Renvoie le montant de la commande
		 *	Transforme le montant si nécéssaire pour être sûr de ne pas excéder 2 décimales
		 *	Lance une exception si le montant n'est pas valide
		 *	@return Le montant de la commande
		 */
		protected function getOrderAmount(){
			$amount = ord_orders_get_total_to_pay( $this->getOrderId(), true );
			if (! $amount) throw new Exception('Erreur ord_orders_get_total_to_pay !');
			$amount = round($amount, 2);
			if ($amount < 0.01) throw new Exception('Le montant de la commande est inférieur à 0,01€ (' . number_format($amount, 2, ',', ' ') . '€) !');
			return $amount;
		}

		/**
		 *	Renvoie l'identifiant de la commande
		 *	Lance une exception si n'existe pas ou non valide
		 *	@return int L'identifiant de la commande
		 */
		protected function getOrderId(){
			if (! (isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']))) throw new Exception('Impossible de récupérer la commande ! (tnt_id = ' . $GLOBALS['config']['tnt_id'] . ', class = ' . get_class($this) . ')');
			return $_SESSION['ord_id'];
		}

		/**
		 *	Renvoie l'email du user
		 *	@return L'email du user
		 */
		protected function getUserEmail(){
			if (! isset($_SESSION['usr_email'])) throw new Exception('Impossible de récupérer l\'email !');
			return $_SESSION['usr_email'];
		}

		/**
		 *	Renvoie l'id du user
		 *	@return L'id du user
		 */
		protected function getUserId(){
			if (isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user'])) return $_SESSION['admin_view_user'];
			elseif (isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id'])) return $_SESSION['usr_id'];
			else throw new Exception('Impossible de récupérer userId !');
		}

		/**
		 * 	Renvoi la référence d'un client
		 *	@return La référence d'un client
		 */
		protected function getUserRef(){
			$usr_id = 0;
			if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) )
				$usr_id = $_SESSION['admin_view_user'];
			elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) )
				return $_SESSION['usr_ref'];

			if( !$usr_id ) throw new Exception('Impossible de récupérer la référence du client !');

			return gu_users_get_ref( $usr_id );
		}

		/** Cette fonction permet de récupérer l'url du site en SSL si elle est disponible
		 *	@return La variable de config site_ssl_url si définie et non vide sinon site_url
		 */
		protected function getUrlPaymentExternal(){
			global $config;
			return ( isset($config['site_ssl_url']) && trim($config['site_ssl_url']) != '' ? $config['site_ssl_url'] : $config['site_url'] );
		}

		/**
		 * Permet l'envoi d'une requête HTTP, en prenant en charge le contrôle de certificat SSL
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * @param string $url L'url à charger via HTTP(s)
		 * @return La réponse fournie par le serveur
		 */
		protected function send( $url ){
			$ch = curl_init($url);
			if (! $ch) throw new Exception('Erreur curl_init !');
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			$r = curl_exec($ch);
			if ($r === false) throw new Exception('Erreur curl_exec !');
			curl_close($ch);
			return $r;
		}

		/**
		 * Valide le contexte d'exécution défini dans l'environnement
		 * Si le contexte n'est pas valide, la méthode lance une exception contenant une description de l'erreur
		 * @param $context Le contexte
		 * @return bool true si le contexte est valide
		 * @throws Exception une exception est levée si le contexte n'a pas pu être identifié
		 */
		protected static function validContext( $context ){
			if( $context !== PaymentExternal::CONTEXT_DEV && $context !== PaymentExternal::CONTEXT_PROD ){
				throw new Exception('$config[context] n\'est pas valide ! Utiliser PaymentExternal::CONTEXT_DEV ou PaymentExternal::CONTEXT_PROD !');
			}
			return true;
		}

		/** Cette méthode va permettre d'enregistrer une erreur survenue lors de l'exécution.
		 *	@param $error Obligatoire, message d'erreur à sauvegarder
		 *	@return ne retourne rien
		 */
		public static function logError( $error ){
			if( self::getContext() == PaymentExternal::CONTEXT_DEV ){
				error_log( __FILE__.':'.__LINE__.' '.$error );
			}else{
				throw new Exception( __FILE__.':'.__LINE__.' '.$error );
			}
		}

		/** Cette fonction permet d'enregistrer dans CouchDb un accès banque lors d'un paiement
		 * Un log sera lancé en cas d'erreur d'insertion dans couchDb
		 */
		protected function savePaymentInCouchDB(){
			$this->data_couchDB['date'] = time();

			$response = CouchDB::create(_COUCHDB_HIST_PAY_DB_NAME)->add(CLS_PAYMENT_ACCESS_HISTO, $this->data_couchDB);
			if (!is_array($response) || !array_key_exists('ok', $response)) {
				error_log('Erreur de l\'insertion dans CouchDb des données suivantes : '.print_r($this->data_couchDB, true) );
			}
		}

		/** Cette fonction permet d'enregistrer dans CouchDb le retour banque lors d'un paiement
		 * Un log sera lancé en cas d'erreur d'insertion dans couchDb
		 */
		protected function saveReturnPaymentInCouchDB(){
			$this->data_couchDB['date'] = time();

			$response = CouchDB::create(_COUCHDB_HIST_PAY_DB_NAME)->add(CLS_PAYMENT_RETURN_HISTO, $this->data_couchDB);
			if (!is_array($response) || !array_key_exists('ok', $response)) {
				error_log('Erreur de l\'insertion dans CouchDb des données suivantes : '.print_r($this->data_couchDB, true) );
			}
		}
	}

	/// @}

