<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  5511 => 'São Paulo',
  55112010 => 'São Paulo - SP',
  55112011 => 'São Paulo - SP',
  55112013 => 'São Paulo - SP',
  55112015 => 'São Paulo - SP',
  55112018 => 'São Paulo - SP',
  55112019 => 'São Paulo - SP',
  55112021 => 'São Paulo - SP',
  55112023 => 'São Paulo - SP',
  55112024 => 'São Paulo - SP',
  55112025 => 'São Paulo - SP',
  55112027 => 'São Paulo - SP',
  55112028 => 'São Paulo - SP',
  55112031 => 'São Paulo - SP',
  55112032 => 'São Paulo - SP',
  55112033 => 'São Paulo - SP',
  55112036 => 'São Paulo - SP',
  55112037 => 'São Paulo - SP',
  55112038 => 'São Paulo - SP',
  55112040 => 'São Paulo - SP',
  55112048 => 'São Paulo - SP',
  55112049 => 'São Paulo - SP',
  55112056 => 'São Paulo - SP',
  55112057 => 'São Paulo - SP',
  55112058 => 'São Paulo - SP',
  55112059 => 'São Paulo - SP',
  55112060 => 'São Paulo - SP',
  55112061 => 'São Paulo - SP',
  55112062 => 'São Paulo - SP',
  55112063 => 'São Paulo - SP',
  55112064 => 'São Paulo - SP',
  55112065 => 'São Paulo - SP',
  55112066 => 'São Paulo - SP',
  55112067 => 'São Paulo - SP',
  55112068 => 'São Paulo - SP',
  55112069 => 'São Paulo - SP',
  55112070 => 'São Paulo - SP',
  55112071 => 'São Paulo - SP',
  55112072 => 'São Paulo - SP',
  55112073 => 'São Paulo - SP',
  55112074 => 'São Paulo - SP',
  55112078 => 'Barueri - SP',
  55112079 => 'São Paulo - SP',
  55112081 => 'São Paulo - SP',
  55112084 => 'São Paulo - SP',
  55112085 => 'Guarulhos - SP',
  55112086 => 'Guarulhos - SP',
  55112087 => 'Guarulhos - SP',
  55112088 => 'Guarulhos - SP',
  55112089 => 'São Paulo - SP',
  55112090 => 'São Paulo - SP',
  55112091 => 'São Paulo - SP',
  55112092 => 'São Paulo - SP',
  55112093 => 'São Paulo - SP',
  55112094 => 'São Paulo - SP',
  55112095 => 'São Paulo - SP',
  55112097 => 'São Paulo - SP',
  55112098 => 'São Paulo - SP',
  55112099 => 'São Paulo - SP',
  55112102 => 'São Paulo - SP',
  55112103 => 'São Paulo - SP',
  55112104 => 'São Paulo - SP',
  55112105 => 'São Paulo - SP',
  55112106 => 'São Paulo - SP',
  55112107 => 'São Paulo - SP',
  55112108 => 'São Paulo - SP',
  55112111 => 'São Paulo - SP',
  55112112 => 'São Paulo - SP',
  55112113 => 'São Paulo - SP',
  55112114 => 'São Paulo - SP',
  55112115 => 'São Paulo - SP',
  55112117 => 'São Paulo - SP',
  55112118 => 'Itu - SP',
  55112119 => 'Atibaia - SP',
  55112121 => 'São Paulo - SP',
  55112123 => 'São Paulo - SP',
  55112124 => 'São Paulo - SP',
  55112125 => 'São Paulo - SP',
  55112126 => 'São Paulo - SP',
  55112127 => 'São Paulo - SP',
  55112131 => 'São Paulo - SP',
  55112132 => 'São Paulo - SP',
  55112133 => 'São Paulo - SP',
  55112134 => 'São Paulo - SP',
  55112135 => 'São Paulo - SP',
  55112136 => 'Jundiaí - SP',
  55112137 => 'São Paulo - SP',
  55112138 => 'São Paulo - SP',
  55112139 => 'São Paulo - SP',
  55112141 => 'São Paulo - SP',
  55112142 => 'São Paulo - SP',
  55112144 => 'São Paulo - SP',
  55112145 => 'São Paulo - SP',
  55112146 => 'São Paulo - SP',
  55112147 => 'São Paulo - SP',
  55112148 => 'São Paulo - SP',
  55112149 => 'São Paulo - SP',
  55112151 => 'São Paulo - SP',
  55112152 => 'Jundiaí - SP',
  55112154 => 'São Paulo - SP',
  55112155 => 'São Paulo - SP',
  55112156 => 'São Paulo - SP',
  55112157 => 'São Paulo - SP',
  55112158 => 'São Paulo - SP',
  55112159 => 'São Paulo - SP',
  55112161 => 'São Paulo - SP',
  55112162 => 'São Paulo - SP',
  55112163 => 'São Paulo - SP',
  55112164 => 'São Paulo - SP',
  55112165 => 'São Paulo - SP',
  55112166 => 'Barueri - SP',
  55112167 => 'São Paulo - SP',
  55112168 => 'São Paulo - SP',
  55112169 => 'São Paulo - SP',
  55112171 => 'São Paulo - SP',
  55112172 => 'São Paulo - SP',
  55112173 => 'São Paulo - SP',
  55112174 => 'São Paulo - SP',
  55112175 => 'São Paulo - SP',
  55112176 => 'São Paulo - SP',
  55112177 => 'São Paulo - SP',
  55112178 => 'São Paulo - SP',
  55112179 => 'São Paulo - SP',
  55112182 => 'São Paulo - SP',
  55112183 => 'São Paulo - SP',
  55112184 => 'São Paulo - SP',
  55112185 => 'São Paulo - SP',
  55112186 => 'São Paulo - SP',
  55112187 => 'São Paulo - SP',
  55112188 => 'São Paulo - SP',
  55112189 => 'São Paulo - SP',
  55112192 => 'São Paulo - SP',
  55112193 => 'São Paulo - SP',
  55112195 => 'São Paulo - SP',
  55112196 => 'São Paulo - SP',
  55112197 => 'São Paulo - SP',
  55112198 => 'São Paulo - SP',
  55112201 => 'São Paulo - SP',
  55112203 => 'São Paulo - SP',
  55112204 => 'São Paulo - SP',
  55112205 => 'São Paulo - SP',
  55112206 => 'São Paulo - SP',
  55112207 => 'São Paulo - SP',
  55112211 => 'São Paulo - SP',
  55112213 => 'São Paulo - SP',
  55112214 => 'São Paulo - SP',
  55112215 => 'São Paulo - SP',
  55112216 => 'São Paulo - SP',
  55112217 => 'São Paulo - SP',
  55112218 => 'São Paulo - SP',
  55112219 => 'São Paulo - SP',
  55112221 => 'São Paulo - SP',
  55112223 => 'São Paulo - SP',
  55112224 => 'São Paulo - SP',
  55112225 => 'São Paulo - SP',
  55112226 => 'São Paulo - SP',
  55112227 => 'São Paulo - SP',
  55112228 => 'Santo André - SP',
  55112229 => 'Guarulhos - SP',
  55112231 => 'São Paulo - SP',
  55112232 => 'São Paulo - SP',
  55112233 => 'São Paulo - SP',
  55112234 => 'São Paulo - SP',
  55112235 => 'São Paulo - SP',
  55112236 => 'São Paulo - SP',
  55112238 => 'São Paulo - SP',
  55112239 => 'São Paulo - SP',
  55112240 => 'São Paulo - SP',
  55112241 => 'São Paulo - SP',
  55112242 => 'São Paulo - SP',
  55112243 => 'São Paulo - SP',
  55112245 => 'Guarulhos - SP',
  55112246 => 'São Paulo - SP',
  55112247 => 'São Paulo - SP',
  55112248 => 'São Paulo - SP',
  55112249 => 'São Paulo - SP',
  55112251 => 'São Paulo - SP',
  55112252 => 'São Paulo - SP',
  55112253 => 'São Paulo - SP',
  55112255 => 'São Paulo - SP',
  55112256 => 'São Paulo - SP',
  55112257 => 'São Paulo - SP',
  55112258 => 'São Paulo - SP',
  55112259 => 'São Paulo - SP',
  55112261 => 'São Paulo - SP',
  55112262 => 'São Paulo - SP',
  55112263 => 'São Paulo - SP',
  55112264 => 'São Paulo - SP',
  55112265 => 'São Paulo - SP',
  55112267 => 'São Paulo - SP',
  55112271 => 'São Paulo - SP',
  55112272 => 'São Paulo - SP',
  55112273 => 'São Paulo - SP',
  55112274 => 'São Paulo - SP',
  55112275 => 'São Paulo - SP',
  55112276 => 'São Paulo - SP',
  55112277 => 'Bragança Paulista - SP',
  55112278 => 'São Paulo - SP',
  55112279 => 'Guarulhos - SP',
  55112280 => 'São Paulo - SP',
  55112281 => 'São Paulo - SP',
  55112282 => 'São Paulo - SP',
  55112283 => 'São Paulo - SP',
  55112284 => 'Osasco - SP',
  55112285 => 'São Paulo - SP',
  55112286 => 'São Paulo - SP',
  55112287 => 'São Paulo - SP',
  55112288 => 'São Paulo - SP',
  55112289 => 'São Paulo - SP',
  55112291 => 'São Paulo - SP',
  55112292 => 'São Paulo - SP',
  55112293 => 'São Paulo - SP',
  55112294 => 'São Paulo - SP',
  55112295 => 'São Paulo - SP',
  55112296 => 'São Paulo - SP',
  55112297 => 'São Paulo - SP',
  55112301 => 'São Paulo - SP',
  55112303 => 'Guarulhos - SP',
  55112304 => 'Guarulhos - SP',
  55112312 => 'Mogi das Cruzes - SP',
  55112317 => 'São Paulo - SP',
  55112318 => 'São Paulo - SP',
  55112319 => 'São Paulo - SP',
  55112321 => 'São Paulo - SP',
  55112331 => 'São Paulo - SP',
  55112334 => 'São Paulo - SP',
  55112335 => 'São Paulo - SP',
  55112336 => 'São Paulo - SP',
  55112341 => 'São Paulo - SP',
  55112342 => 'São Paulo - SP',
  55112346 => 'São Paulo - SP',
  55112347 => 'São Paulo - SP',
  55112350 => 'Poá - SP',
  55112352 => 'São Paulo - SP',
  55112354 => 'São Paulo - SP',
  55112378 => 'Mogi das Cruzes - SP',
  55112392 => 'São Paulo - SP',
  55112396 => 'Itu - SP',
  55112401 => 'Guarulhos - SP',
  55112405 => 'Guarulhos - SP',
  55112406 => 'Guarulhos - SP',
  55112407 => 'Guarulhos - SP',
  55112408 => 'Guarulhos - SP',
  55112409 => 'Guarulhos - SP',
  55112411 => 'Guarulhos - SP',
  55112412 => 'Guarulhos - SP',
  55112413 => 'Guarulhos - SP',
  55112414 => 'Guarulhos - SP',
  55112419 => 'Bragança Paulista - SP',
  55112421 => 'Guarulhos - SP',
  55112422 => 'Guarulhos - SP',
  55112425 => 'Guarulhos - SP',
  55112427 => 'Atibaia - SP',
  55112429 => 'Itu - SP',
  55112431 => 'Guarulhos - SP',
  55112432 => 'Guarulhos - SP',
  55112433 => 'Guarulhos - SP',
  55112436 => 'Guarulhos - SP',
  55112437 => 'Guarulhos - SP',
  55112438 => 'Guarulhos - SP',
  55112439 => 'Guarulhos - SP',
  55112440 => 'Guarulhos - SP',
  55112441 => 'Guarulhos - SP',
  55112442 => 'Guarulhos - SP',
  55112443 => 'Guarulhos - SP',
  55112444 => 'Guarulhos - SP',
  55112445 => 'Guarulhos - SP',
  55112446 => 'Guarulhos - SP',
  55112447 => 'Guarulhos - SP',
  55112451 => 'Guarulhos - SP',
  55112452 => 'Guarulhos - SP',
  55112453 => 'Guarulhos - SP',
  55112455 => 'Guarulhos - SP',
  55112456 => 'Guarulhos - SP',
  55112458 => 'Guarulhos - SP',
  55112460 => 'Guarulhos - SP',
  55112461 => 'Guarulhos - SP',
  55112462 => 'Guarulhos - SP',
  55112463 => 'Guarulhos - SP',
  55112464 => 'Guarulhos - SP',
  55112465 => 'Guarulhos - SP',
  55112466 => 'Guarulhos - SP',
  55112467 => 'Guarulhos - SP',
  55112468 => 'Guarulhos - SP',
  55112469 => 'Guarulhos - SP',
  55112471 => 'Guarulhos - SP',
  55112472 => 'Guarulhos - SP',
  55112473 => 'Bragança Paulista - SP',
  55112475 => 'Guarulhos - SP',
  55112479 => 'Guarulhos - SP',
  55112480 => 'Guarulhos - SP',
  55112481 => 'Guarulhos - SP',
  55112482 => 'Guarulhos - SP',
  55112483 => 'Guarulhos - SP',
  55112484 => 'Guarulhos - SP',
  55112485 => 'Guarulhos - SP',
  55112486 => 'Guarulhos - SP',
  55112487 => 'Guarulhos - SP',
  55112488 => 'Guarulhos - SP',
  55112489 => 'Guarulhos - SP',
  55112490 => 'Bragança Paulista - SP',
  55112492 => 'Guarulhos - SP',
  55112493 => 'Atibaia - SP',
  55112496 => 'Guarulhos - SP',
  55112497 => 'Guarulhos - SP',
  55112498 => 'Guarulhos - SP',
  55112499 => 'Guarulhos - SP',
  55112501 => 'São Paulo - SP',
  55112503 => 'São Paulo - SP',
  55112504 => 'São Paulo - SP',
  55112506 => 'São Paulo - SP',
  55112507 => 'São Paulo - SP',
  55112516 => 'São Paulo - SP',
  55112518 => 'São Paulo - SP',
  55112521 => 'São Paulo - SP',
  55112523 => 'São Paulo - SP',
  55112524 => 'São Paulo - SP',
  55112528 => 'São Paulo - SP',
  55112532 => 'São Paulo - SP',
  55112533 => 'São Paulo - SP',
  55112535 => 'São Paulo - SP',
  55112536 => 'São Paulo - SP',
  55112547 => 'São Paulo - SP',
  55112549 => 'São Paulo - SP',
  55112551 => 'São Paulo - SP',
  55112552 => 'São Paulo - SP',
  55112553 => 'São Paulo - SP',
  55112554 => 'São Paulo - SP',
  55112555 => 'São Paulo - SP',
  55112556 => 'São Paulo - SP',
  55112557 => 'São Paulo - SP',
  55112558 => 'São Paulo - SP',
  55112559 => 'São Paulo - SP',
  55112561 => 'São Paulo - SP',
  55112562 => 'São Paulo - SP',
  55112565 => 'São Paulo - SP',
  55112566 => 'São Paulo - SP',
  55112567 => 'São Paulo - SP',
  55112568 => 'São Paulo - SP',
  55112569 => 'São Paulo - SP',
  55112571 => 'São Paulo - SP',
  55112572 => 'São Paulo - SP',
  55112573 => 'São Paulo - SP',
  55112574 => 'São Paulo - SP',
  55112575 => 'São Paulo - SP',
  55112577 => 'São Paulo - SP',
  55112578 => 'São Paulo - SP',
  55112579 => 'São Paulo - SP',
  55112581 => 'São Paulo - SP',
  55112582 => 'São Paulo - SP',
  55112583 => 'São Paulo - SP',
  55112586 => 'São Paulo - SP',
  55112588 => 'São Paulo - SP',
  55112589 => 'São Paulo - SP',
  55112591 => 'São Paulo - SP',
  55112592 => 'São Paulo - SP',
  55112594 => 'São Paulo - SP',
  55112597 => 'São Paulo - SP',
  55112599 => 'São Paulo - SP',
  55112601 => 'São Paulo - SP',
  55112602 => 'São Paulo - SP',
  55112603 => 'São Paulo - SP',
  55112604 => 'São Paulo - SP',
  55112605 => 'São Paulo - SP',
  55112606 => 'São Paulo - SP',
  55112607 => 'São Paulo - SP',
  55112609 => 'São Paulo - SP',
  55112610 => 'Mogi das Cruzes - SP',
  55112612 => 'São Paulo - SP',
  55112618 => 'São Paulo - SP',
  55112621 => 'São Paulo - SP',
  55112622 => 'São Paulo - SP',
  55112623 => 'São Paulo - SP',
  55112625 => 'São Paulo - SP',
  55112628 => 'São Paulo - SP',
  55112631 => 'São Paulo - SP',
  55112632 => 'São Paulo - SP',
  55112633 => 'São Paulo - SP',
  55112634 => 'São Paulo - SP',
  55112635 => 'São Paulo - SP',
  55112636 => 'São Paulo - SP',
  55112641 => 'São Paulo - SP',
  55112642 => 'São Paulo - SP',
  55112643 => 'São Paulo - SP',
  55112646 => 'São Paulo - SP',
  55112647 => 'São Paulo - SP',
  55112651 => 'São Paulo - SP',
  55112652 => 'São Paulo - SP',
  55112653 => 'São Paulo - SP',
  55112654 => 'São Paulo - SP',
  55112655 => 'São Paulo - SP',
  55112657 => 'Guarulhos - SP',
  55112658 => 'Poá - SP',
  55112661 => 'São Paulo - SP',
  55112662 => 'São Paulo - SP',
  55112663 => 'São Paulo - SP',
  55112664 => 'Barueri - SP',
  55112668 => 'Mogi das Cruzes - SP',
  55112671 => 'São Paulo - SP',
  55112672 => 'São Paulo - SP',
  55112673 => 'São Paulo - SP',
  55112674 => 'São Paulo - SP',
  55112675 => 'São Paulo - SP',
  55112676 => 'São Paulo - SP',
  55112678 => 'São Paulo - SP',
  55112679 => 'São Paulo - SP',
  55112682 => 'São Paulo - SP',
  55112683 => 'São Paulo - SP',
  55112684 => 'São Paulo - SP',
  55112685 => 'São Paulo - SP',
  55112687 => 'São Paulo - SP',
  55112688 => 'Guarulhos - SP',
  55112692 => 'São Paulo - SP',
  55112693 => 'São Paulo - SP',
  55112694 => 'São Paulo - SP',
  55112695 => 'São Paulo - SP',
  55112696 => 'São Paulo - SP',
  55112697 => 'São Paulo - SP',
  55112698 => 'São Paulo - SP',
  55112703 => 'São Paulo - SP',
  55112704 => 'São Paulo - SP',
  55112705 => 'São Paulo - SP',
  55112707 => 'São Paulo - SP',
  55112712 => 'São Paulo - SP',
  55112715 => 'Itu - SP',
  55112717 => 'São Paulo - SP',
  55112719 => 'São Paulo - SP',
  55112721 => 'São Paulo - SP',
  55112722 => 'São Paulo - SP',
  55112724 => 'São Paulo - SP',
  55112725 => 'São Paulo - SP',
  55112726 => 'São Paulo - SP',
  55112727 => 'São Paulo - SP',
  55112728 => 'São Paulo - SP',
  55112730 => 'São Paulo - SP',
  55112731 => 'São Paulo - SP',
  55112734 => 'São Paulo - SP',
  55112735 => 'São Paulo - SP',
  55112736 => 'São Paulo - SP',
  55112739 => 'São Paulo - SP',
  55112741 => 'São Paulo - SP',
  55112742 => 'São Paulo - SP',
  55112743 => 'São Paulo - SP',
  55112745 => 'São Paulo - SP',
  55112746 => 'São Paulo - SP',
  55112747 => 'São Paulo - SP',
  55112748 => 'São Paulo - SP',
  55112749 => 'São Paulo - SP',
  55112751 => 'São Paulo - SP',
  55112752 => 'São Paulo - SP',
  55112753 => 'São Paulo - SP',
  55112754 => 'São Paulo - SP',
  55112771 => 'São Paulo - SP',
  55112772 => 'São Paulo - SP',
  55112773 => 'São Paulo - SP',
  55112781 => 'São Paulo - SP',
  55112783 => 'São Paulo - SP',
  55112784 => 'São Paulo - SP',
  55112785 => 'São Paulo - SP',
  55112787 => 'São Paulo - SP',
  55112790 => 'São Paulo - SP',
  55112791 => 'São Paulo - SP',
  55112793 => 'São Paulo - SP',
  55112795 => 'São Paulo - SP',
  55112796 => 'São Paulo - SP',
  55112797 => 'São Paulo - SP',
  55112798 => 'São Paulo - SP',
  55112803 => 'Carapicuíba - SP',
  55112805 => 'Diadema - SP',
  55112806 => 'Diadema - SP',
  55112807 => 'Embu das Artes - SP',
  55112809 => 'Guarulhos - SP',
  55112811 => 'Salto - SP',
  55112812 => 'Guarulhos - SP',
  55112813 => 'São Paulo - SP',
  55112814 => 'Itaquaquecetuba - SP',
  55112818 => 'Mauá - SP',
  55112819 => 'Mogi das Cruzes - SP',
  55112822 => 'Osasco - SP',
  55112827 => 'São Paulo - SP',
  55112828 => 'Santo André - SP',
  55112829 => 'Santo André - SP',
  55112831 => 'São Bernardo do Campo - SP',
  55112834 => 'São Bernardo do Campo - SP',
  55112835 => 'São Caetano do Sul - SP',
  55112836 => 'São Paulo - SP',
  55112837 => 'São Paulo - SP',
  55112838 => 'São Paulo - SP',
  55112839 => 'São Paulo - SP',
  55112840 => 'Salto - SP',
  55112841 => 'São Paulo - SP',
  55112843 => 'São Paulo - SP',
  55112848 => 'São Paulo - SP',
  55112849 => 'São Paulo - SP',
  55112851 => 'São Paulo - SP',
  55112852 => 'São Paulo - SP',
  55112854 => 'São Paulo - SP',
  55112855 => 'São Paulo - SP',
  55112856 => 'São Paulo - SP',
  55112858 => 'São Paulo - SP',
  55112861 => 'São Paulo - SP',
  55112862 => 'São Paulo - SP',
  55112864 => 'São Paulo - SP',
  55112867 => 'São Paulo - SP',
  55112876 => 'Carapicuíba - SP',
  55112883 => 'Mogi das Cruzes - SP',
  55112884 => 'Mogi das Cruzes - SP',
  55112885 => 'Guarulhos - SP',
  55112886 => 'São Paulo - SP',
  55112887 => 'Itaquaquecetuba - SP',
  55112891 => 'Suzano - SP',
  55112892 => 'São Paulo - SP',
  55112893 => 'São Paulo - SP',
  55112894 => 'São Paulo - SP',
  55112895 => 'São Paulo - SP',
  55112896 => 'Santo André - SP',
  55112897 => 'São Bernardo do Campo - SP',
  55112900 => 'São Paulo - SP',
  55112901 => 'São Paulo - SP',
  55112902 => 'São Paulo - SP',
  55112903 => 'São Paulo - SP',
  55112904 => 'São Paulo - SP',
  55112905 => 'São Paulo - SP',
  55112906 => 'São Paulo - SP',
  55112908 => 'São Paulo - SP',
  55112909 => 'São Paulo - SP',
  55112910 => 'São Paulo - SP',
  55112911 => 'São Paulo - SP',
  55112912 => 'São Paulo - SP',
  55112914 => 'São Paulo - SP',
  55112915 => 'São Paulo - SP',
  55112916 => 'São Paulo - SP',
  55112917 => 'São Paulo - SP',
  55112918 => 'São Paulo - SP',
  55112919 => 'São Paulo - SP',
  55112921 => 'São Paulo - SP',
  55112923 => 'Jundiaí - SP',
  55112928 => 'São Paulo - SP',
  55112931 => 'São Paulo - SP',
  55112933 => 'São Paulo - SP',
  55112934 => 'São Paulo - SP',
  55112940 => 'São Paulo - SP',
  55112941 => 'São Paulo - SP',
  55112942 => 'São Paulo - SP',
  55112943 => 'São Paulo - SP',
  55112944 => 'São Paulo - SP',
  55112945 => 'São Paulo - SP',
  55112946 => 'São Paulo - SP',
  55112947 => 'São Paulo - SP',
  55112948 => 'São Paulo - SP',
  55112949 => 'São Paulo - SP',
  55112950 => 'São Paulo - SP',
  55112951 => 'São Paulo - SP',
  55112952 => 'São Paulo - SP',
  55112953 => 'São Paulo - SP',
  55112954 => 'São Paulo - SP',
  55112955 => 'São Paulo - SP',
  55112956 => 'São Paulo - SP',
  55112957 => 'São Paulo - SP',
  55112958 => 'São Paulo - SP',
  55112959 => 'São Paulo - SP',
  55112960 => 'São Paulo - SP',
  55112961 => 'São Paulo - SP',
  55112962 => 'São Paulo - SP',
  55112963 => 'São Paulo - SP',
  55112964 => 'São Paulo - SP',
  55112965 => 'São Paulo - SP',
  55112966 => 'São Paulo - SP',
  55112967 => 'São Paulo - SP',
  55112969 => 'São Paulo - SP',
  55112970 => 'Barueri - SP',
  55112971 => 'São Paulo - SP',
  55112972 => 'São Paulo - SP',
  55112973 => 'São Paulo - SP',
  55112974 => 'São Paulo - SP',
  55112975 => 'São Paulo - SP',
  55112976 => 'São Paulo - SP',
  55112977 => 'São Paulo - SP',
  55112978 => 'São Paulo - SP',
  55112979 => 'São Paulo - SP',
  55112980 => 'São Paulo - SP',
  55112981 => 'São Paulo - SP',
  55112982 => 'São Paulo - SP',
  55112983 => 'São Paulo - SP',
  55112986 => 'São Paulo - SP',
  55112987 => 'São Paulo - SP',
  55112989 => 'São Paulo - SP',
  55112990 => 'São Paulo - SP',
  55112991 => 'São Paulo - SP',
  55112992 => 'São Paulo - SP',
  55112993 => 'São Paulo - SP',
  55112994 => 'São Paulo - SP',
  55112995 => 'São Paulo - SP',
  55112996 => 'São Paulo - SP',
  55112997 => 'São Paulo - SP',
  55112998 => 'São Paulo - SP',
  55113011 => 'São Paulo - SP',
  55113014 => 'São Paulo - SP',
  55113015 => 'São Paulo - SP',
  55113016 => 'São Paulo - SP',
  55113021 => 'São Paulo - SP',
  55113022 => 'São Paulo - SP',
  55113023 => 'São Paulo - SP',
  55113024 => 'São Paulo - SP',
  55113025 => 'São Paulo - SP',
  55113026 => 'São Paulo - SP',
  55113027 => 'São Paulo - SP',
  55113028 => 'São Paulo - SP',
  55113030 => 'São Paulo - SP',
  55113031 => 'São Paulo - SP',
  55113032 => 'São Paulo - SP',
  55113034 => 'São Paulo - SP',
  55113035 => 'São Paulo - SP',
  55113037 => 'São Paulo - SP',
  55113039 => 'São Paulo - SP',
  55113041 => 'São Paulo - SP',
  55113044 => 'São Paulo - SP',
  55113045 => 'São Paulo - SP',
  55113046 => 'São Paulo - SP',
  55113049 => 'São Paulo - SP',
  55113050 => 'São Paulo - SP',
  55113051 => 'São Paulo - SP',
  55113052 => 'São Paulo - SP',
  55113054 => 'São Paulo - SP',
  55113057 => 'São Paulo - SP',
  55113059 => 'São Paulo - SP',
  55113060 => 'São Paulo - SP',
  55113061 => 'São Paulo - SP',
  55113062 => 'São Paulo - SP',
  55113063 => 'São Paulo - SP',
  55113064 => 'São Paulo - SP',
  55113065 => 'São Paulo - SP',
  55113066 => 'São Paulo - SP',
  55113067 => 'São Paulo - SP',
  55113068 => 'São Paulo - SP',
  55113071 => 'São Paulo - SP',
  55113073 => 'São Paulo - SP',
  55113074 => 'São Paulo - SP',
  55113075 => 'São Paulo - SP',
  55113076 => 'São Paulo - SP',
  55113077 => 'São Paulo - SP',
  55113078 => 'São Paulo - SP',
  55113079 => 'São Paulo - SP',
  55113081 => 'São Paulo - SP',
  55113082 => 'São Paulo - SP',
  55113083 => 'São Paulo - SP',
  55113084 => 'São Paulo - SP',
  55113085 => 'São Paulo - SP',
  55113086 => 'São Paulo - SP',
  55113087 => 'São Paulo - SP',
  55113088 => 'São Paulo - SP',
  55113089 => 'São Paulo - SP',
  55113091 => 'São Paulo - SP',
  55113092 => 'São Paulo - SP',
  55113093 => 'São Paulo - SP',
  55113094 => 'São Paulo - SP',
  55113095 => 'São Paulo - SP',
  55113096 => 'São Paulo - SP',
  55113097 => 'São Paulo - SP',
  55113098 => 'São Paulo - SP',
  55113100 => 'São Paulo - SP',
  55113101 => 'São Paulo - SP',
  55113103 => 'São Paulo - SP',
  55113104 => 'São Paulo - SP',
  55113105 => 'São Paulo - SP',
  55113106 => 'São Paulo - SP',
  55113107 => 'São Paulo - SP',
  55113109 => 'Jundiaí - SP',
  55113112 => 'São Paulo - SP',
  55113113 => 'São Paulo - SP',
  55113115 => 'São Paulo - SP',
  55113117 => 'São Paulo - SP',
  55113120 => 'São Paulo - SP',
  55113122 => 'São Paulo - SP',
  55113129 => 'São Paulo - SP',
  55113133 => 'São Paulo - SP',
  55113135 => 'São Bernardo do Campo - SP',
  55113138 => 'São Paulo - SP',
  55113141 => 'São Paulo - SP',
  55113142 => 'São Paulo - SP',
  55113144 => 'São Paulo - SP',
  55113145 => 'São Paulo - SP',
  55113146 => 'São Paulo - SP',
  55113147 => 'São Paulo - SP',
  55113148 => 'São Paulo - SP',
  55113149 => 'São Paulo - SP',
  55113151 => 'São Paulo - SP',
  55113152 => 'São Paulo - SP',
  55113153 => 'São Paulo - SP',
  55113158 => 'São Paulo - SP',
  55113159 => 'São Paulo - SP',
  55113161 => 'São Paulo - SP',
  55113163 => 'São Paulo - SP',
  55113164 => 'São Paulo - SP',
  55113165 => 'São Paulo - SP',
  55113167 => 'São Paulo - SP',
  55113168 => 'São Paulo - SP',
  55113169 => 'São Paulo - SP',
  55113170 => 'São Paulo - SP',
  55113171 => 'São Paulo - SP',
  55113175 => 'São Paulo - SP',
  55113177 => 'São Paulo - SP',
  55113178 => 'São Paulo - SP',
  55113180 => 'São Paulo - SP',
  55113183 => 'Itatiba - SP',
  55113184 => 'Osasco - SP',
  55113188 => 'São Paulo - SP',
  55113191 => 'São Paulo - SP',
  55113192 => 'São Paulo - SP',
  55113198 => 'São Paulo - SP',
  55113201 => 'São Paulo - SP',
  55113202 => 'São Paulo - SP',
  55113203 => 'São Paulo - SP',
  55113205 => 'São Paulo - SP',
  55113206 => 'São Paulo - SP',
  55113207 => 'São Paulo - SP',
  55113208 => 'São Paulo - SP',
  55113209 => 'São Paulo - SP',
  55113210 => 'São Paulo - SP',
  55113211 => 'São Paulo - SP',
  55113212 => 'São Paulo - SP',
  55113213 => 'São Paulo - SP',
  55113214 => 'São Paulo - SP',
  55113215 => 'São Paulo - SP',
  55113219 => 'São Paulo - SP',
  55113220 => 'São Paulo - SP',
  55113221 => 'São Paulo - SP',
  55113222 => 'São Paulo - SP',
  55113223 => 'São Paulo - SP',
  55113225 => 'São Paulo - SP',
  55113226 => 'São Paulo - SP',
  55113227 => 'São Paulo - SP',
  55113228 => 'São Paulo - SP',
  55113229 => 'São Paulo - SP',
  55113231 => 'São Paulo - SP',
  55113234 => 'São Paulo - SP',
  55113235 => 'São Paulo - SP',
  55113237 => 'São Paulo - SP',
  55113240 => 'Salvador - BA',
  55113241 => 'São Paulo - SP',
  55113242 => 'São Paulo - SP',
  55113243 => 'São Paulo - SP',
  55113244 => 'São Paulo - SP',
  55113245 => 'São Paulo - SP',
  55113246 => 'São Paulo - SP',
  55113248 => 'São Paulo - SP',
  55113249 => 'São Paulo - SP',
  55113251 => 'São Paulo - SP',
  55113252 => 'São Paulo - SP',
  55113253 => 'São Paulo - SP',
  55113254 => 'São Paulo - SP',
  55113255 => 'São Paulo - SP',
  55113256 => 'São Paulo - SP',
  55113257 => 'São Paulo - SP',
  55113258 => 'São Paulo - SP',
  55113259 => 'São Paulo - SP',
  55113262 => 'São Paulo - SP',
  55113263 => 'São Paulo - SP',
  55113264 => 'Salvador - BA',
  55113265 => 'São Paulo - SP',
  55113266 => 'São Paulo - SP',
  55113268 => 'São Paulo - SP',
  55113269 => 'São Paulo - SP',
  55113271 => 'São Paulo - SP',
  55113272 => 'São Paulo - SP',
  55113273 => 'São Paulo - SP',
  55113275 => 'São Paulo - SP',
  55113276 => 'São Paulo - SP',
  55113277 => 'São Paulo - SP',
  55113278 => 'São Paulo - SP',
  55113281 => 'São Paulo - SP',
  55113283 => 'São Paulo - SP',
  55113284 => 'São Paulo - SP',
  55113285 => 'São Paulo - SP',
  55113286 => 'São Paulo - SP',
  55113287 => 'São Paulo - SP',
  55113288 => 'São Paulo - SP',
  55113289 => 'São Paulo - SP',
  55113291 => 'São Paulo - SP',
  55113292 => 'São Paulo - SP',
  55113293 => 'São Paulo - SP',
  55113294 => 'São Paulo - SP',
  55113295 => 'São Paulo - SP',
  55113296 => 'São Paulo - SP',
  55113297 => 'São Paulo - SP',
  55113299 => 'São Paulo - SP',
  55113301 => 'São Paulo - SP',
  55113303 => 'São Bernardo do Campo - SP',
  55113304 => 'São Paulo - SP',
  55113305 => 'São Paulo - SP',
  55113307 => 'São Paulo - SP',
  55113308 => 'Jundiaí - SP',
  55113311 => 'São Paulo - SP',
  55113312 => 'São Paulo - SP',
  55113313 => 'São Paulo - SP',
  55113314 => 'São Paulo - SP',
  55113315 => 'São Paulo - SP',
  55113316 => 'São Paulo - SP',
  55113318 => 'São Paulo - SP',
  55113319 => 'São Paulo - SP',
  55113321 => 'São Paulo - SP',
  55113322 => 'São Paulo - SP',
  55113323 => 'São Paulo - SP',
  55113325 => 'São Paulo - SP',
  55113326 => 'São Paulo - SP',
  55113327 => 'São Paulo - SP',
  55113328 => 'São Paulo - SP',
  55113329 => 'São Paulo - SP',
  55113331 => 'São Paulo - SP',
  55113332 => 'São Paulo - SP',
  55113333 => 'São Paulo - SP',
  55113334 => 'São Paulo - SP',
  55113335 => 'São Paulo - SP',
  55113336 => 'São Paulo - SP',
  55113337 => 'São Paulo - SP',
  55113338 => 'São Paulo - SP',
  55113339 => 'São Paulo - SP',
  55113341 => 'São Paulo - SP',
  55113342 => 'São Paulo - SP',
  55113343 => 'São Paulo - SP',
  55113345 => 'São Paulo - SP',
  55113348 => 'São Paulo - SP',
  55113349 => 'São Paulo - SP',
  55113353 => 'São Paulo - SP',
  55113354 => 'São Paulo - SP',
  55113357 => 'São Paulo - SP',
  55113359 => 'São Paulo - SP',
  55113361 => 'São Paulo - SP',
  55113362 => 'São Paulo - SP',
  55113365 => 'São Paulo - SP',
  55113367 => 'São Paulo - SP',
  55113368 => 'São Paulo - SP',
  55113371 => 'São Paulo - SP',
  55113374 => 'Mogi das Cruzes - SP',
  55113375 => 'São Paulo - SP',
  55113376 => 'São Paulo - SP',
  55113377 => 'São Paulo - SP',
  55113378 => 'Jundiaí - SP',
  55113379 => 'Jundiaí - SP',
  55113383 => 'São Paulo - SP',
  55113384 => 'São Paulo - SP',
  55113389 => 'São Paulo - SP',
  55113392 => 'São Paulo - SP',
  55113393 => 'São Paulo - SP',
  55113395 => 'Jundiaí - SP',
  55113399 => 'São Paulo - SP',
  55113402 => 'Atibaia - SP',
  55113403 => 'Bragança Paulista - SP',
  55113404 => 'Bragança Paulista - SP',
  55113408 => 'Itatiba - SP',
  55113409 => 'Suzano - SP',
  55113411 => 'São Paulo - SP',
  55113412 => 'São Bernardo do Campo - SP',
  55113413 => 'Itu - SP',
  55113414 => 'Itu - SP',
  55113416 => 'Taboão da Serra - SP',
  55113418 => 'Taboão da Serra - SP',
  55113421 => 'Mauá - SP',
  55113422 => 'São Caetano do Sul - SP',
  55113423 => 'São Bernardo do Campo - SP',
  55113424 => 'São Bernardo do Campo - SP',
  55113425 => 'Diadema - SP',
  55113426 => 'Diadema - SP',
  55113427 => 'Mogi das Cruzes - SP',
  55113429 => 'São Paulo - SP',
  55113431 => 'Barueri - SP',
  55113432 => 'Carapicuíba - SP',
  55113433 => 'Embu das Artes - SP',
  55113434 => 'São Paulo - SP',
  55113435 => 'Guarulhos - SP',
  55113436 => 'Guarulhos - SP',
  55113437 => 'Guarulhos - SP',
  55113438 => 'Santo André - SP',
  55113439 => 'Santo André - SP',
  55113441 => 'São Paulo - SP',
  55113442 => 'São Paulo - SP',
  55113443 => 'São Paulo - SP',
  55113444 => 'São Paulo - SP',
  55113445 => 'Itaquaquecetuba - SP',
  55113447 => 'Osasco - SP',
  55113448 => 'Osasco - SP',
  55113449 => 'Osasco - SP',
  55113451 => 'São Paulo - SP',
  55113452 => 'São Paulo - SP',
  55113453 => 'São Paulo - SP',
  55113454 => 'São Paulo - SP',
  55113455 => 'São Paulo - SP',
  55113457 => 'São Paulo - SP',
  55113459 => 'São Paulo - SP',
  55113461 => 'São Paulo - SP',
  55113462 => 'São Paulo - SP',
  55113463 => 'São Paulo - SP',
  55113464 => 'São Paulo - SP',
  55113465 => 'São Paulo - SP',
  55113466 => 'São Paulo - SP',
  55113467 => 'São Paulo - SP',
  55113471 => 'São Paulo - SP',
  55113472 => 'São Paulo - SP',
  55113473 => 'São Paulo - SP',
  55113474 => 'São Paulo - SP',
  55113475 => 'São Paulo - SP',
  55113476 => 'São Paulo - SP',
  55113477 => 'São Paulo - SP',
  55113478 => 'São Paulo - SP',
  55113479 => 'São Paulo - SP',
  55113481 => 'São Paulo - SP',
  55113482 => 'São Paulo - SP',
  55113483 => 'São Paulo - SP',
  55113484 => 'São Paulo - SP',
  55113485 => 'São Paulo - SP',
  55113486 => 'São Paulo - SP',
  55113487 => 'São Paulo - SP',
  55113488 => 'São Paulo - SP',
  55113491 => 'São Paulo - SP',
  55113492 => 'São Paulo - SP',
  55113493 => 'São Paulo - SP',
  55113494 => 'São Paulo - SP',
  55113495 => 'São Paulo - SP',
  55113496 => 'São Paulo - SP',
  55113497 => 'São Paulo - SP',
  55113498 => 'São Paulo - SP',
  55113499 => 'São Paulo - SP',
  55113501 => 'São Paulo - SP',
  55113502 => 'São Paulo - SP',
  55113505 => 'São Paulo - SP',
  55113507 => 'São Paulo - SP',
  55113512 => 'São Paulo - SP',
  55113513 => 'São Paulo - SP',
  55113514 => 'São Paulo - SP',
  55113515 => 'São Paulo - SP',
  55113518 => 'São Paulo - SP',
  55113521 => 'São Paulo - SP',
  55113523 => 'São Paulo - SP',
  55113524 => 'São Paulo - SP',
  55113525 => 'São Paulo - SP',
  55113526 => 'São Paulo - SP',
  55113527 => 'São Paulo - SP',
  55113528 => 'São Paulo - SP',
  55113529 => 'São Paulo - SP',
  55113531 => 'São Paulo - SP',
  55113532 => 'São Paulo - SP',
  55113533 => 'São Paulo - SP',
  55113534 => 'São Paulo - SP',
  55113535 => 'São Paulo - SP',
  55113536 => 'São Paulo - SP',
  55113537 => 'São Paulo - SP',
  55113538 => 'São Paulo - SP',
  55113539 => 'São Paulo - SP',
  55113541 => 'São Paulo - SP',
  55113542 => 'São Paulo - SP',
  55113543 => 'São Paulo - SP',
  55113544 => 'São Paulo - SP',
  55113545 => 'São Paulo - SP',
  55113546 => 'São Paulo - SP',
  55113548 => 'São Paulo - SP',
  55113549 => 'São Paulo - SP',
  55113552 => 'São Paulo - SP',
  55113554 => 'São Paulo - SP',
  55113555 => 'São Paulo - SP',
  55113556 => 'São Paulo - SP',
  55113559 => 'São Paulo - SP',
  55113562 => 'São Paulo - SP',
  55113563 => 'São Paulo - SP',
  55113564 => 'São Paulo - SP',
  55113565 => 'São Caetano do Sul - SP',
  55113566 => 'São Paulo - SP',
  55113567 => 'São Paulo - SP',
  55113568 => 'São Paulo - SP',
  55113569 => 'São Paulo - SP',
  55113571 => 'São Paulo - SP',
  55113572 => 'São Paulo - SP',
  55113576 => 'São Paulo - SP',
  55113578 => 'São Paulo - SP',
  55113581 => 'Barueri - SP',
  55113583 => 'São Paulo - SP',
  55113584 => 'São Paulo - SP',
  55113585 => 'São Paulo - SP',
  55113594 => 'São Paulo - SP',
  55113595 => 'São Paulo - SP',
  55113596 => 'São Paulo - SP',
  55113599 => 'Osasco - SP',
  55113601 => 'Osasco - SP',
  55113602 => 'Osasco - SP',
  55113603 => 'Osasco - SP',
  55113607 => 'Osasco - SP',
  55113608 => 'Osasco - SP',
  55113611 => 'São Paulo - SP',
  55113612 => 'São Paulo - SP',
  55113613 => 'São Paulo - SP',
  55113614 => 'São Paulo - SP',
  55113615 => 'São Paulo - SP',
  55113616 => 'São Paulo - SP',
  55113617 => 'São Paulo - SP',
  55113618 => 'São Paulo - SP',
  55113619 => 'São Paulo - SP',
  55113621 => 'São Paulo - SP',
  55113622 => 'São Paulo - SP',
  55113623 => 'São Paulo - SP',
  55113624 => 'São Paulo - SP',
  55113625 => 'São Paulo - SP',
  55113628 => 'São Paulo - SP',
  55113633 => 'São Paulo - SP',
  55113636 => 'São Paulo - SP',
  55113637 => 'São Paulo - SP',
  55113638 => 'São Paulo - SP',
  55113641 => 'São Paulo - SP',
  55113642 => 'São Paulo - SP',
  55113643 => 'São Paulo - SP',
  55113644 => 'São Paulo - SP',
  55113645 => 'São Paulo - SP',
  55113646 => 'São Paulo - SP',
  55113647 => 'São Paulo - SP',
  55113648 => 'São Paulo - SP',
  55113649 => 'São Paulo - SP',
  55113651 => 'Osasco - SP',
  55113652 => 'Osasco - SP',
  55113653 => 'Osasco - SP',
  55113654 => 'Osasco - SP',
  55113659 => 'Osasco - SP',
  55113661 => 'São Paulo - SP',
  55113662 => 'São Paulo - SP',
  55113663 => 'São Paulo - SP',
  55113664 => 'São Paulo - SP',
  55113665 => 'São Paulo - SP',
  55113666 => 'São Paulo - SP',
  55113667 => 'São Paulo - SP',
  55113668 => 'São Paulo - SP',
  55113670 => 'São Paulo - SP',
  55113672 => 'São Paulo - SP',
  55113673 => 'São Paulo - SP',
  55113674 => 'São Paulo - SP',
  55113675 => 'São Paulo - SP',
  55113676 => 'São Paulo - SP',
  55113678 => 'São Paulo - SP',
  55113679 => 'São Paulo - SP',
  55113681 => 'Osasco - SP',
  55113682 => 'Osasco - SP',
  55113683 => 'Osasco - SP',
  55113684 => 'Osasco - SP',
  55113685 => 'Osasco - SP',
  55113686 => 'Osasco - SP',
  55113687 => 'Osasco - SP',
  55113688 => 'Osasco - SP',
  55113689 => 'Osasco - SP',
  55113692 => 'Osasco - SP',
  55113693 => 'Osasco - SP',
  55113694 => 'Osasco - SP',
  55113695 => 'Osasco - SP',
  55113696 => 'Osasco - SP',
  55113697 => 'Osasco - SP',
  55113698 => 'Osasco - SP',
  55113699 => 'Osasco - SP',
  55113701 => 'São Paulo - SP',
  55113704 => 'São Paulo - SP',
  55113705 => 'Santo André - SP',
  55113706 => 'São Paulo - SP',
  55113707 => 'São Paulo - SP',
  55113708 => 'São Paulo - SP',
  55113709 => 'São Paulo - SP',
  55113712 => 'São Paulo - SP',
  55113713 => 'São Paulo - SP',
  55113714 => 'São Paulo - SP',
  55113715 => 'São Paulo - SP',
  55113716 => 'São Paulo - SP',
  55113718 => 'São Paulo - SP',
  55113719 => 'São Paulo - SP',
  55113721 => 'São Paulo - SP',
  55113722 => 'São Paulo - SP',
  55113723 => 'São Paulo - SP',
  55113724 => 'São Paulo - SP',
  55113725 => 'São Paulo - SP',
  55113726 => 'São Paulo - SP',
  55113727 => 'São Paulo - SP',
  55113729 => 'São Paulo - SP',
  55113731 => 'São Paulo - SP',
  55113732 => 'São Paulo - SP',
  55113733 => 'São Paulo - SP',
  55113735 => 'São Paulo - SP',
  55113736 => 'São Paulo - SP',
  55113737 => 'São Paulo - SP',
  55113739 => 'São Paulo - SP',
  55113740 => 'São Paulo - SP',
  55113741 => 'São Paulo - SP',
  55113742 => 'São Paulo - SP',
  55113743 => 'São Paulo - SP',
  55113744 => 'São Paulo - SP',
  55113745 => 'São Paulo - SP',
  55113746 => 'São Paulo - SP',
  55113747 => 'São Paulo - SP',
  55113748 => 'São Paulo - SP',
  55113749 => 'São Paulo - SP',
  55113750 => 'São Paulo - SP',
  55113751 => 'São Paulo - SP',
  55113752 => 'São Paulo - SP',
  55113753 => 'São Paulo - SP',
  55113754 => 'Arujá - SP',
  55113755 => 'São Paulo - SP',
  55113756 => 'São Paulo - SP',
  55113757 => 'São Paulo - SP',
  55113758 => 'São Paulo - SP',
  55113759 => 'São Paulo - SP',
  55113760 => 'São Paulo - SP',
  55113761 => 'São Paulo - SP',
  55113762 => 'São Paulo - SP',
  55113763 => 'São Paulo - SP',
  55113765 => 'São Paulo - SP',
  55113766 => 'São Paulo - SP',
  55113768 => 'São Paulo - SP',
  55113771 => 'São Paulo - SP',
  55113772 => 'São Paulo - SP',
  55113773 => 'São Paulo - SP',
  55113774 => 'São Paulo - SP',
  55113775 => 'São Paulo - SP',
  55113776 => 'São Paulo - SP',
  55113779 => 'São Paulo - SP',
  55113781 => 'São Paulo - SP',
  55113782 => 'São Paulo - SP',
  55113783 => 'São Paulo - SP',
  55113784 => 'São Paulo - SP',
  55113785 => 'São Paulo - SP',
  55113787 => 'São Paulo - SP',
  55113788 => 'São Paulo - SP',
  55113791 => 'São Paulo - SP',
  55113793 => 'São Paulo - SP',
  55113794 => 'São Paulo - SP',
  55113795 => 'São Paulo - SP',
  55113796 => 'São Paulo - SP',
  55113797 => 'São Paulo - SP',
  55113798 => 'São Paulo - SP',
  55113801 => 'São Paulo - SP',
  55113803 => 'São Paulo - SP',
  55113804 => 'São Paulo - SP',
  55113805 => 'São Paulo - SP',
  55113806 => 'São Paulo - SP',
  55113807 => 'São Paulo - SP',
  55113808 => 'São Paulo - SP',
  55113809 => 'São Paulo - SP',
  55113811 => 'São Paulo - SP',
  55113812 => 'São Paulo - SP',
  55113813 => 'São Paulo - SP',
  55113814 => 'São Paulo - SP',
  55113815 => 'São Paulo - SP',
  55113816 => 'São Paulo - SP',
  55113817 => 'São Paulo - SP',
  55113819 => 'São Paulo - SP',
  55113820 => 'Guarulhos - SP',
  55113822 => 'São Paulo - SP',
  55113823 => 'São Paulo - SP',
  55113824 => 'São Paulo - SP',
  55113825 => 'São Paulo - SP',
  55113826 => 'São Paulo - SP',
  55113828 => 'São Paulo - SP',
  55113829 => 'São Paulo - SP',
  55113831 => 'São Paulo - SP',
  55113832 => 'São Paulo - SP',
  55113833 => 'São Paulo - SP',
  55113834 => 'São Paulo - SP',
  55113835 => 'São Paulo - SP',
  55113836 => 'São Paulo - SP',
  55113837 => 'São Paulo - SP',
  55113838 => 'São Paulo - SP',
  55113839 => 'São Paulo - SP',
  55113841 => 'São Paulo - SP',
  55113842 => 'São Paulo - SP',
  55113844 => 'São Paulo - SP',
  55113845 => 'São Paulo - SP',
  55113846 => 'São Paulo - SP',
  55113847 => 'São Paulo - SP',
  55113848 => 'São Paulo - SP',
  55113849 => 'São Paulo - SP',
  55113851 => 'São Paulo - SP',
  55113852 => 'São Paulo - SP',
  55113853 => 'São Paulo - SP',
  55113854 => 'São Paulo - SP',
  55113855 => 'São Paulo - SP',
  55113856 => 'São Paulo - SP',
  55113857 => 'São Paulo - SP',
  55113858 => 'São Paulo - SP',
  55113859 => 'São Paulo - SP',
  55113861 => 'São Paulo - SP',
  55113862 => 'São Paulo - SP',
  55113863 => 'São Paulo - SP',
  55113864 => 'São Paulo - SP',
  55113865 => 'São Paulo - SP',
  55113866 => 'São Paulo - SP',
  55113867 => 'São Paulo - SP',
  55113868 => 'São Paulo - SP',
  55113869 => 'São Paulo - SP',
  55113871 => 'São Paulo - SP',
  55113872 => 'São Paulo - SP',
  55113873 => 'São Paulo - SP',
  55113874 => 'São Paulo - SP',
  55113875 => 'São Paulo - SP',
  55113876 => 'São Paulo - SP',
  55113877 => 'São Paulo - SP',
  55113881 => 'São Paulo - SP',
  55113882 => 'São Paulo - SP',
  55113883 => 'São Paulo - SP',
  55113884 => 'São Paulo - SP',
  55113885 => 'São Paulo - SP',
  55113886 => 'São Paulo - SP',
  55113887 => 'São Paulo - SP',
  55113888 => 'São Paulo - SP',
  55113889 => 'São Paulo - SP',
  55113891 => 'São Paulo - SP',
  55113892 => 'São Paulo - SP',
  55113895 => 'São Paulo - SP',
  55113896 => 'São Paulo - SP',
  55113897 => 'São Paulo - SP',
  55113898 => 'São Paulo - SP',
  55113899 => 'São Paulo - SP',
  55113901 => 'São Paulo - SP',
  55113902 => 'São Paulo - SP',
  55113903 => 'São Paulo - SP',
  55113904 => 'São Paulo - SP',
  55113905 => 'São Paulo - SP',
  55113906 => 'São Paulo - SP',
  55113907 => 'São Bernardo do Campo - SP',
  55113909 => 'São Paulo - SP',
  55113911 => 'São Paulo - SP',
  55113915 => 'São Paulo - SP',
  55113916 => 'São Paulo - SP',
  55113917 => 'São Paulo - SP',
  55113918 => 'São Paulo - SP',
  55113919 => 'São Paulo - SP',
  55113921 => 'São Paulo - SP',
  55113924 => 'São Paulo - SP',
  55113926 => 'São Paulo - SP',
  55113929 => 'São Paulo - SP',
  55113931 => 'São Paulo - SP',
  55113932 => 'São Paulo - SP',
  55113934 => 'São Paulo - SP',
  55113935 => 'São Paulo - SP',
  55113936 => 'São Paulo - SP',
  55113938 => 'São Paulo - SP',
  55113941 => 'São Paulo - SP',
  55113942 => 'São Paulo - SP',
  55113943 => 'São Paulo - SP',
  55113946 => 'São Paulo - SP',
  55113947 => 'São Paulo - SP',
  55113948 => 'São Paulo - SP',
  55113949 => 'São Paulo - SP',
  55113951 => 'São Paulo - SP',
  55113952 => 'São Paulo - SP',
  55113953 => 'São Paulo - SP',
  55113955 => 'São Paulo - SP',
  55113959 => 'São Paulo - SP',
  55113961 => 'São Paulo - SP',
  55113962 => 'São Paulo - SP',
  55113964 => 'Jundiaí - SP',
  55113965 => 'São Paulo - SP',
  55113966 => 'São Paulo - SP',
  55113969 => 'São Paulo - SP',
  55113971 => 'São Paulo - SP',
  55113972 => 'São Paulo - SP',
  55113973 => 'São Paulo - SP',
  55113974 => 'São Paulo - SP',
  55113975 => 'São Paulo - SP',
  55113976 => 'São Paulo - SP',
  55113978 => 'São Paulo - SP',
  55113982 => 'São Paulo - SP',
  55113985 => 'São Paulo - SP',
  55113986 => 'São Paulo - SP',
  55113987 => 'São Paulo - SP',
  55113988 => 'Guarulhos - SP',
  55113989 => 'São Paulo - SP',
  55113990 => 'São Paulo - SP',
  55113991 => 'São Paulo - SP',
  55113992 => 'São Paulo - SP',
  55113993 => 'São Paulo - SP',
  55113994 => 'São Paulo - SP',
  55113997 => 'São Paulo - SP',
  55113998 => 'São Paulo - SP',
  55113999 => 'São Paulo - SP',
  55114000 => 'Arujá - SP',
  55114005 => 'São Paulo - SP',
  55114007 => 'São Paulo - SP',
  55114009 => 'São Paulo - SP',
  55114011 => 'Piracaia - SP',
  55114012 => 'Bom Jesus dos Perdões - SP',
  55114013 => 'Itu - SP',
  55114014 => 'Morungaba - SP',
  55114015 => 'Tuiuti - SP',
  55114016 => 'Jarinu - SP',
  55114017 => 'Jarinu - SP',
  55114018 => 'Pinhalzinho - SP',
  55114019 => 'Itu - SP',
  55114021 => 'Salto - SP',
  55114022 => 'Itu - SP',
  55114023 => 'Itu - SP',
  55114024 => 'Itu - SP',
  55114025 => 'Itu - SP',
  55114026 => 'Itu - SP',
  55114027 => 'Salto - SP',
  55114028 => 'Salto - SP',
  55114029 => 'Salto - SP',
  55114031 => 'Bragança Paulista - SP',
  55114032 => 'Bragança Paulista - SP',
  55114033 => 'Bragança Paulista - SP',
  55114034 => 'Bragança Paulista - SP',
  55114035 => 'Bragança Paulista - SP',
  55114036 => 'Piracaia - SP',
  55114037 => 'Pedra Bela - SP',
  55114038 => 'Campo Limpo Paulista - SP',
  55114039 => 'Campo Limpo Paulista - SP',
  55114043 => 'Diadema - SP',
  55114044 => 'Diadema - SP',
  55114047 => 'Diadema - SP',
  55114048 => 'Diadema - SP',
  55114049 => 'Diadema - SP',
  55114051 => 'Diadema - SP',
  55114052 => 'São Paulo - SP',
  55114053 => 'Diadema - SP',
  55114054 => 'Diadema - SP',
  55114055 => 'Diadema - SP',
  55114056 => 'Diadema - SP',
  55114057 => 'Diadema - SP',
  55114059 => 'Diadema - SP',
  55114061 => 'Diadema - SP',
  55114062 => 'São Paulo - SP',
  55114066 => 'Diadema - SP',
  55114067 => 'Diadema - SP',
  55114069 => 'Diadema - SP',
  55114071 => 'Diadema - SP',
  55114072 => 'Diadema - SP',
  55114075 => 'Diadema - SP',
  55114076 => 'Diadema - SP',
  55114077 => 'Diadema - SP',
  55114079 => 'Diadema - SP',
  55114081 => 'São Paulo - SP',
  55114082 => 'São Paulo - SP',
  55114083 => 'São Paulo - SP',
  55114084 => 'São Paulo - SP',
  55114091 => 'Diadema - SP',
  55114092 => 'Diadema - SP',
  55114093 => 'Diadema - SP',
  55114094 => 'Diadema - SP',
  55114096 => 'São Paulo - SP',
  55114098 => 'Salto - SP',
  55114099 => 'Diadema - SP',
  55114103 => 'São Bernardo do Campo - SP',
  55114104 => 'São Bernardo do Campo - SP',
  55114109 => 'São Bernardo do Campo - SP',
  55114121 => 'São Bernardo do Campo - SP',
  55114122 => 'São Bernardo do Campo - SP',
  55114123 => 'São Bernardo do Campo - SP',
  55114124 => 'São Bernardo do Campo - SP',
  55114125 => 'São Bernardo do Campo - SP',
  55114126 => 'São Bernardo do Campo - SP',
  55114127 => 'São Bernardo do Campo - SP',
  55114128 => 'São Bernardo do Campo - SP',
  55114129 => 'São Bernardo do Campo - SP',
  55114130 => 'São Roque - SP',
  55114131 => 'Pirapora do Bom Jesus - SP',
  55114132 => 'Pirapora do Bom Jesus - SP',
  55114133 => 'Barueri - SP',
  55114134 => 'Barueri - SP',
  55114135 => 'Taboão da Serra - SP',
  55114136 => 'Araçariguama - SP',
  55114137 => 'Taboão da Serra - SP',
  55114138 => 'Taboão da Serra - SP',
  55114141 => 'Itapevi - SP',
  55114142 => 'Itapevi - SP',
  55114143 => 'Itapevi - SP',
  55114144 => 'Itapevi - SP',
  55114145 => 'Itapevi - SP',
  55114147 => 'Itapecerica da Serra - SP',
  55114148 => 'Cotia - SP',
  55114151 => 'Santana de Parnaíba - SP',
  55114152 => 'Santana de Parnaíba - SP',
  55114153 => 'Santana de Parnaíba - SP',
  55114154 => 'Santana de Parnaíba - SP',
  55114155 => 'Santana de Parnaíba - SP',
  55114156 => 'Santana de Parnaíba - SP',
  55114157 => 'Santana de Parnaíba - SP',
  55114158 => 'Vargem Grande Paulista - SP',
  55114159 => 'Vargem Grande Paulista - SP',
  55114161 => 'Barueri - SP',
  55114162 => 'Barueri - SP',
  55114163 => 'Barueri - SP',
  55114164 => 'Carapicuíba - SP',
  55114165 => 'Itapecerica da Serra - SP',
  55114166 => 'Barueri - SP',
  55114168 => 'Barueri - SP',
  55114169 => 'Carapicuíba - SP',
  55114173 => 'São Bernardo do Campo - SP',
  55114174 => 'São Bernardo do Campo - SP',
  55114176 => 'São Bernardo do Campo - SP',
  55114177 => 'São Bernardo do Campo - SP',
  55114178 => 'São Bernardo do Campo - SP',
  55114181 => 'Carapicuíba - SP',
  55114182 => 'Carapicuíba - SP',
  55114183 => 'Carapicuíba - SP',
  55114184 => 'Carapicuíba - SP',
  55114185 => 'Carapicuíba - SP',
  55114186 => 'Carapicuíba - SP',
  55114191 => 'Barueri - SP',
  55114192 => 'Barueri - SP',
  55114193 => 'Barueri - SP',
  55114194 => 'Barueri - SP',
  55114195 => 'Barueri - SP',
  55114196 => 'Barueri - SP',
  55114198 => 'Barueri - SP',
  55114201 => 'Barueri - SP',
  55114202 => 'Carapicuíba - SP',
  55114204 => 'Araçariguama - SP',
  55114205 => 'Itapevi - SP',
  55114206 => 'Jandira - SP',
  55114209 => 'Barueri - SP',
  55114217 => 'Atibaia - SP',
  55114220 => 'São Caetano do Sul - SP',
  55114222 => 'São Caetano do Sul - SP',
  55114223 => 'São Caetano do Sul - SP',
  55114224 => 'São Caetano do Sul - SP',
  55114225 => 'São Caetano do Sul - SP',
  55114226 => 'São Caetano do Sul - SP',
  55114227 => 'São Caetano do Sul - SP',
  55114228 => 'São Caetano do Sul - SP',
  55114229 => 'São Caetano do Sul - SP',
  55114230 => 'Itupeva - SP',
  55114233 => 'São Caetano do Sul - SP',
  55114234 => 'São Caetano do Sul - SP',
  55114241 => 'Embu das Artes - SP',
  55114243 => 'Cotia - SP',
  55114246 => 'Mairinque - SP',
  55114247 => 'Barueri - SP',
  55114255 => 'Mogi das Cruzes - SP',
  55114256 => 'Suzano - SP',
  55114264 => 'Francisco Morato - SP',
  55114265 => 'Itapecerica da Serra - SP',
  55114272 => 'Santana de Parnaíba - SP',
  55114273 => 'Caieiras - SP',
  55114275 => 'Mairiporã - SP',
  55114278 => 'Santa Isabel - SP',
  55114280 => 'Cotia - SP',
  55114282 => 'São Paulo - SP',
  55114284 => 'Mogi das Cruzes - SP',
  55114292 => 'Suzano - SP',
  55114295 => 'Suzano - SP',
  55114302 => 'São Paulo - SP',
  55114308 => 'Diadema - SP',
  55114312 => 'Mogi das Cruzes - SP',
  55114317 => 'São Bernardo do Campo - SP',
  55114330 => 'São Bernardo do Campo - SP',
  55114331 => 'São Bernardo do Campo - SP',
  55114332 => 'São Bernardo do Campo - SP',
  55114334 => 'São Bernardo do Campo - SP',
  55114335 => 'São Bernardo do Campo - SP',
  55114337 => 'São Bernardo do Campo - SP',
  55114338 => 'São Bernardo do Campo - SP',
  55114339 => 'São Bernardo do Campo - SP',
  55114341 => 'São Bernardo do Campo - SP',
  55114342 => 'São Bernardo do Campo - SP',
  55114343 => 'São Bernardo do Campo - SP',
  55114344 => 'São Bernardo do Campo - SP',
  55114345 => 'São Bernardo do Campo - SP',
  55114346 => 'São Bernardo do Campo - SP',
  55114347 => 'São Bernardo do Campo - SP',
  55114348 => 'São Bernardo do Campo - SP',
  55114349 => 'São Paulo - SP',
  55114351 => 'São Bernardo do Campo - SP',
  55114352 => 'São Bernardo do Campo - SP',
  55114354 => 'São Bernardo do Campo - SP',
  55114355 => 'São Bernardo do Campo - SP',
  55114356 => 'São Bernardo do Campo - SP',
  55114357 => 'São Bernardo do Campo - SP',
  55114358 => 'São Bernardo do Campo - SP',
  55114359 => 'São Bernardo do Campo - SP',
  55114360 => 'São Bernardo do Campo - SP',
  55114361 => 'São Bernardo do Campo - SP',
  55114362 => 'São Bernardo do Campo - SP',
  55114363 => 'São Bernardo do Campo - SP',
  55114365 => 'São Bernardo do Campo - SP',
  55114366 => 'São Bernardo do Campo - SP',
  55114367 => 'São Bernardo do Campo - SP',
  55114368 => 'São Bernardo do Campo - SP',
  55114373 => 'São Paulo - SP',
  55114374 => 'Carapicuíba - SP',
  55114390 => 'São Bernardo do Campo - SP',
  55114391 => 'São Bernardo do Campo - SP',
  55114393 => 'São Bernardo do Campo - SP',
  55114394 => 'São Bernardo do Campo - SP',
  55114396 => 'São Bernardo do Campo - SP',
  55114397 => 'São Bernardo do Campo - SP',
  55114398 => 'São Bernardo do Campo - SP',
  55114399 => 'São Bernardo do Campo - SP',
  55114402 => 'Atibaia - SP',
  55114403 => 'Itu - SP',
  55114405 => 'Piracaia - SP',
  55114408 => 'Cajamar - SP',
  55114409 => 'Jacaré - SP',
  55114411 => 'Atibaia - SP',
  55114412 => 'Atibaia - SP',
  55114413 => 'Atibaia - SP',
  55114414 => 'Atibaia - SP',
  55114415 => 'Atibaia - SP',
  55114416 => 'Atibaia - SP',
  55114417 => 'Atibaia - SP',
  55114418 => 'Atibaia - SP',
  55114419 => 'Mairiporã - SP',
  55114421 => 'Santo André - SP',
  55114424 => 'Santo André - SP',
  55114427 => 'Santo André - SP',
  55114428 => 'Santo André - SP',
  55114431 => 'Jundiaí - SP',
  55114432 => 'Santo André - SP',
  55114433 => 'Santo André - SP',
  55114434 => 'Santo André - SP',
  55114435 => 'Santo André - SP',
  55114436 => 'Santo André - SP',
  55114437 => 'Santo André - SP',
  55114438 => 'Santo André - SP',
  55114439 => 'Santo André - SP',
  55114441 => 'Caieiras - SP',
  55114442 => 'Caieiras - SP',
  55114443 => 'Franco da Rocha - SP',
  55114444 => 'Franco da Rocha - SP',
  55114445 => 'Caieiras - SP',
  55114446 => 'Cajamar - SP',
  55114448 => 'Cajamar - SP',
  55114449 => 'Franco da Rocha - SP',
  55114454 => 'Santo André - SP',
  55114456 => 'Salto - SP',
  55114461 => 'Santo André - SP',
  55114462 => 'Santo André - SP',
  55114463 => 'Santo André - SP',
  55114468 => 'Santo André - SP',
  55114469 => 'Santo André - SP',
  55114473 => 'Santo André - SP',
  55114474 => 'Santo André - SP',
  55114477 => 'Santo André - SP',
  55114481 => 'Bragança Paulista - SP',
  55114482 => 'Mairiporã - SP',
  55114483 => 'Mairiporã - SP',
  55114484 => 'Mairiporã - SP',
  55114485 => 'Mairiporã - SP',
  55114486 => 'Terra Preta - SP',
  55114487 => 'Itatiba - SP',
  55114488 => 'Francisco Morato - SP',
  55114489 => 'Francisco Morato - SP',
  55114492 => 'Jundiaí - SP',
  55114493 => 'Várzea Paulista - SP',
  55114494 => 'Atibaia - SP',
  55114495 => 'Itatiba - SP',
  55114496 => 'Itupeva - SP',
  55114497 => 'Jundiaí - SP',
  55114501 => 'São Paulo - SP',
  55114502 => 'São Paulo - SP',
  55114504 => 'São Paulo - SP',
  55114505 => 'São Paulo - SP',
  55114506 => 'São Paulo - SP',
  55114508 => 'São Paulo - SP',
  55114509 => 'Santo André - SP',
  55114511 => 'Mauá - SP',
  55114513 => 'Mauá - SP',
  55114518 => 'Mauá - SP',
  55114519 => 'Mauá - SP',
  55114521 => 'Jundiaí - SP',
  55114522 => 'Jundiaí - SP',
  55114523 => 'Jundiaí - SP',
  55114524 => 'Itatiba - SP',
  55114525 => 'Jundiaí - SP',
  55114526 => 'Jundiaí - SP',
  55114527 => 'Jundiaí - SP',
  55114528 => 'Cabreúva - SP',
  55114529 => 'Jacaré - SP',
  55114531 => 'Jundiaí - SP',
  55114532 => 'Jundiaí - SP',
  55114533 => 'Jundiaí - SP',
  55114534 => 'Itatiba - SP',
  55114535 => 'Jundiaí - SP',
  55114537 => 'Jundiaí - SP',
  55114538 => 'Itatiba - SP',
  55114539 => 'Joanópolis - SP',
  55114543 => 'Mauá - SP',
  55114545 => 'Mauá - SP',
  55114546 => 'Mauá - SP',
  55114547 => 'Mauá - SP',
  55114548 => 'Mauá - SP',
  55114549 => 'Mauá - SP',
  55114555 => 'Mauá - SP',
  55114566 => 'Mogi das Cruzes - SP',
  55114571 => 'São Paulo - SP',
  55114573 => 'Cotia - SP',
  55114576 => 'Mauá - SP',
  55114577 => 'Mauá - SP',
  55114578 => 'Mauá - SP',
  55114581 => 'Jundiaí - SP',
  55114582 => 'Jundiaí - SP',
  55114583 => 'Jundiaí - SP',
  55114584 => 'Jundiaí - SP',
  55114585 => 'Jundiaí - SP',
  55114586 => 'Jundiaí - SP',
  55114587 => 'Jundiaí - SP',
  55114588 => 'Jundiaí - SP',
  55114591 => 'Itupeva - SP',
  55114592 => 'Itupeva - SP',
  55114593 => 'Itupeva - SP',
  55114594 => 'Itatiba - SP',
  55114595 => 'Várzea Paulista - SP',
  55114596 => 'Várzea Paulista - SP',
  55114597 => 'Nazaré Paulista - SP',
  55114598 => 'Vargem - SP',
  55114599 => 'Jundiaí - SP',
  55114601 => 'Jundiaí - SP',
  55114602 => 'Salto - SP',
  55114603 => 'Bragança Paulista - SP',
  55114604 => 'Mairiporã - SP',
  55114605 => 'Caieiras - SP',
  55114606 => 'Várzea Paulista - SP',
  55114607 => 'Jundiaí - SP',
  55114608 => 'Francisco Morato - SP',
  55114609 => 'Francisco Morato - SP',
  55114611 => 'Cotia - SP',
  55114612 => 'Cotia - SP',
  55114614 => 'Cotia - SP',
  55114615 => 'Cotia - SP',
  55114616 => 'Cotia - SP',
  55114617 => 'Cotia - SP',
  55114618 => 'Jandira - SP',
  55114619 => 'Jandira - SP',
  55114620 => 'Osasco - SP',
  55114622 => 'Santana de Parnaíba - SP',
  55114624 => 'Osasco - SP',
  55114634 => 'Poá - SP',
  55114638 => 'Poá - SP',
  55114639 => 'Poá - SP',
  55114641 => 'Itaquaquecetuba - SP',
  55114644 => 'Itaquaquecetuba - SP',
  55114645 => 'Itaquaquecetuba - SP',
  55114646 => 'Itaquaquecetuba - SP',
  55114649 => 'Itaquaquecetuba - SP',
  55114651 => 'Arujá - SP',
  55114652 => 'Arujá - SP',
  55114653 => 'Arujá - SP',
  55114654 => 'Arujá - SP',
  55114655 => 'Arujá - SP',
  55114656 => 'Santa Isabel - SP',
  55114657 => 'Santa Isabel - SP',
  55114658 => 'Igaratá - SP',
  55114661 => 'Embu-Guaçu - SP',
  55114662 => 'Embu-Guaçu - SP',
  55114663 => 'Embu-Guaçu - SP',
  55114664 => 'Embu-Guaçu - SP',
  55114665 => 'Embu-Guaçu - SP',
  55114666 => 'Itapecerica da Serra - SP',
  55114667 => 'Itapecerica da Serra - SP',
  55114668 => 'Itapecerica da Serra - SP',
  55114669 => 'Itapecerica da Serra - SP',
  55114674 => 'Ferraz de Vasconcelos - SP',
  55114675 => 'Ferraz de Vasconcelos - SP',
  55114676 => 'Ferraz de Vasconcelos - SP',
  55114677 => 'Ferraz de Vasconcelos - SP',
  55114678 => 'Ferraz de Vasconcelos - SP',
  55114679 => 'Ferraz de Vasconcelos - SP',
  55114680 => 'Santa Isabel - SP',
  55114681 => 'Juquitiba - SP',
  55114682 => 'Juquitiba - SP',
  55114683 => 'Juquitiba - SP',
  55114684 => 'Juquitiba - SP',
  55114686 => 'São Lourenço da Serra - SP',
  55114687 => 'São Lourenço da Serra - SP',
  55114688 => 'Barueri - SP',
  55114692 => 'Biritiba-Mirim - SP',
  55114693 => 'Guararema - SP',
  55114694 => 'Biritiba-Mirim - SP',
  55114695 => 'Guararema - SP',
  55114696 => 'Salesópolis - SP',
  55114699 => 'Mogi das Cruzes - SP',
  55114701 => 'Taboão da Serra - SP',
  55114702 => 'Cotia - SP',
  55114703 => 'Cotia - SP',
  55114704 => 'Embu das Artes - SP',
  55114705 => 'Santana de Parnaíba - SP',
  55114706 => 'Barueri - SP',
  55114707 => 'Jandira - SP',
  55114708 => 'Mairinque - SP',
  55114711 => 'São Roque - SP',
  55114712 => 'São Roque - SP',
  55114713 => 'São Roque - SP',
  55114714 => 'São Roque - SP',
  55114715 => 'Alumínio - SP',
  55114716 => 'São João Novo - SP',
  55114717 => 'São Roque - SP',
  55114718 => 'Mairinque - SP',
  55114719 => 'São Roque - SP',
  55114722 => 'Mogi das Cruzes - SP',
  55114723 => 'Mogi das Cruzes - SP',
  55114725 => 'Mogi das Cruzes - SP',
  55114726 => 'Mogi das Cruzes - SP',
  55114727 => 'Mogi das Cruzes - SP',
  55114728 => 'Mogi das Cruzes - SP',
  55114729 => 'Mogi das Cruzes - SP',
  55114731 => 'Suzano - SP',
  55114735 => 'Mogi das Cruzes - SP',
  55114736 => 'Mogi das Cruzes - SP',
  55114738 => 'Mogi das Cruzes - SP',
  55114739 => 'Mogi das Cruzes - SP',
  55114741 => 'Suzano - SP',
  55114742 => 'Suzano - SP',
  55114743 => 'Suzano - SP',
  55114744 => 'Suzano - SP',
  55114746 => 'Suzano - SP',
  55114747 => 'Suzano - SP',
  55114748 => 'Suzano - SP',
  55114751 => 'Suzano - SP',
  55114755 => 'Itaquaquecetuba - SP',
  55114759 => 'Suzano - SP',
  55114761 => 'Mogi das Cruzes - SP',
  55114762 => 'Mogi das Cruzes - SP',
  55114772 => 'Jandira - SP',
  55114773 => 'Itapevi - SP',
  55114774 => 'Itapevi - SP',
  55114775 => 'Itapecerica da Serra - SP',
  55114777 => 'Cotia - SP',
  55114779 => 'Itapecerica da Serra - SP',
  55114781 => 'Embu das Artes - SP',
  55114783 => 'Embu das Artes - SP',
  55114784 => 'São Roque - SP',
  55114785 => 'Embu das Artes - SP',
  55114786 => 'Taboão da Serra - SP',
  55114787 => 'Taboão da Serra - SP',
  55114789 => 'Jandira - SP',
  55114790 => 'Mogi das Cruzes - SP',
  55114791 => 'Mogi das Cruzes - SP',
  55114792 => 'Mogi das Cruzes - SP',
  55114794 => 'Mogi das Cruzes - SP',
  55114795 => 'Mogi das Cruzes - SP',
  55114796 => 'Mogi das Cruzes - SP',
  55114797 => 'Mogi das Cruzes - SP',
  55114798 => 'Mogi das Cruzes - SP',
  55114799 => 'Mogi das Cruzes - SP',
  55114805 => 'Jundiaí - SP',
  55114806 => 'Jundiaí - SP',
  55114808 => 'Jundiaí - SP',
  55114811 => 'Franco da Rocha - SP',
  55114812 => 'Campo Limpo Paulista - SP',
  55114813 => 'Itu - SP',
  55114815 => 'Jundiaí - SP',
  55114816 => 'Jundiaí - SP',
  55114817 => 'Jundiaí - SP',
  55114818 => 'Terra Preta - SP',
  55114819 => 'Franco da Rocha - SP',
  55114820 => 'Rio Grande da Serra - SP',
  55114821 => 'Rio Grande da Serra - SP',
  55114823 => 'Ribeirão Pires - SP',
  55114824 => 'Ribeirão Pires - SP',
  55114825 => 'Ribeirão Pires - SP',
  55114826 => 'Rio Grande da Serra - SP',
  55114827 => 'Ribeirão Pires - SP',
  55114828 => 'Ribeirão Pires - SP',
  55114829 => 'Ribeirão Pires - SP',
  55114837 => 'São Paulo - SP',
  55114850 => 'Itu - SP',
  55114852 => 'Santo André - SP',
  55114853 => 'Suzano - SP',
  55114871 => 'São Paulo - SP',
  55114873 => 'São Paulo - SP',
  55114878 => 'Barueri - SP',
  55114881 => 'Francisco Morato - SP',
  55114882 => 'Carapicuíba - SP',
  55114886 => 'Itu - SP',
  55114887 => 'Jarinu - SP',
  55114888 => 'Joanópolis - SP',
  55114891 => 'Bom Jesus dos Perdões - SP',
  55114892 => 'Bragança Paulista - SP',
  55114893 => 'Campo Limpo Paulista - SP',
  55114894 => 'Itatiba - SP',
  55114895 => 'Nazaré Paulista - SP',
  55114897 => 'Itu - SP',
  55114899 => 'Caieiras - SP',
  55114911 => 'São Paulo - SP',
  55114912 => 'São Paulo - SP',
  55114914 => 'São Paulo - SP',
  55114916 => 'São Paulo - SP',
  55114961 => 'Itupeva - SP',
  55114976 => 'Santo André - SP',
  55114978 => 'Santo André - SP',
  55114979 => 'Santo André - SP',
  55114990 => 'Santo André - SP',
  55114991 => 'Santo André - SP',
  55114992 => 'Santo André - SP',
  55114993 => 'Santo André - SP',
  55114994 => 'Santo André - SP',
  55114995 => 'Santo André - SP',
  55114996 => 'Santo André - SP',
  55114998 => 'Santo André - SP',
  55115010 => 'São Paulo - SP',
  55115011 => 'São Paulo - SP',
  55115012 => 'São Paulo - SP',
  55115013 => 'São Paulo - SP',
  55115014 => 'São Paulo - SP',
  55115015 => 'São Paulo - SP',
  55115016 => 'São Paulo - SP',
  55115017 => 'São Paulo - SP',
  55115018 => 'São Paulo - SP',
  55115021 => 'São Paulo - SP',
  55115029 => 'São Paulo - SP',
  55115031 => 'São Paulo - SP',
  55115032 => 'São Paulo - SP',
  55115034 => 'São Paulo - SP',
  55115041 => 'São Paulo - SP',
  55115042 => 'São Paulo - SP',
  55115044 => 'São Paulo - SP',
  55115049 => 'São Paulo - SP',
  55115051 => 'São Paulo - SP',
  55115052 => 'São Paulo - SP',
  55115053 => 'São Paulo - SP',
  55115054 => 'São Paulo - SP',
  55115055 => 'São Paulo - SP',
  55115056 => 'São Paulo - SP',
  55115061 => 'São Paulo - SP',
  55115062 => 'São Paulo - SP',
  55115063 => 'São Paulo - SP',
  55115066 => 'São Paulo - SP',
  55115068 => 'São Paulo - SP',
  55115071 => 'São Paulo - SP',
  55115072 => 'São Paulo - SP',
  55115073 => 'São Paulo - SP',
  55115074 => 'São Paulo - SP',
  55115077 => 'São Paulo - SP',
  55115078 => 'São Paulo - SP',
  55115080 => 'São Paulo - SP',
  55115081 => 'São Paulo - SP',
  55115082 => 'São Paulo - SP',
  55115083 => 'São Paulo - SP',
  55115084 => 'São Paulo - SP',
  55115085 => 'São Paulo - SP',
  55115086 => 'São Paulo - SP',
  55115087 => 'São Paulo - SP',
  55115088 => 'São Paulo - SP',
  55115089 => 'São Paulo - SP',
  55115090 => 'São Paulo - SP',
  55115092 => 'São Paulo - SP',
  55115094 => 'São Paulo - SP',
  55115095 => 'São Paulo - SP',
  55115097 => 'São Paulo - SP',
  55115102 => 'São Paulo - SP',
  55115103 => 'São Paulo - SP',
  55115105 => 'São Paulo - SP',
  55115111 => 'São Paulo - SP',
  55115112 => 'São Paulo - SP',
  55115181 => 'São Paulo - SP',
  55115182 => 'São Paulo - SP',
  55115183 => 'São Paulo - SP',
  55115184 => 'São Paulo - SP',
  55115187 => 'São Paulo - SP',
  55115189 => 'São Paulo - SP',
  55115212 => 'São Paulo - SP',
  55115213 => 'São Paulo - SP',
  55115477 => 'Mogi das Cruzes - SP',
  55115486 => 'São Paulo - SP',
  55115489 => 'São Paulo',
  55115501 => 'São Paulo - SP',
  55115502 => 'São Paulo - SP',
  55115504 => 'São Paulo - SP',
  55115505 => 'São Paulo - SP',
  55115506 => 'São Paulo - SP',
  55115507 => 'São Paulo - SP',
  55115508 => 'São Paulo - SP',
  55115509 => 'São Paulo - SP',
  55115510 => 'São Paulo - SP',
  55115511 => 'São Paulo - SP',
  55115512 => 'São Paulo - SP',
  55115513 => 'São Paulo - SP',
  55115514 => 'São Paulo - SP',
  55115515 => 'São Paulo - SP',
  55115516 => 'São Paulo - SP',
  55115517 => 'São Paulo - SP',
  55115521 => 'São Paulo - SP',
  55115522 => 'São Paulo - SP',
  55115523 => 'São Paulo - SP',
  55115524 => 'São Paulo - SP',
  55115525 => 'São Paulo - SP',
  55115526 => 'São Paulo - SP',
  55115528 => 'São Paulo - SP',
  55115531 => 'São Paulo - SP',
  55115532 => 'São Paulo - SP',
  55115533 => 'São Paulo - SP',
  55115534 => 'São Paulo - SP',
  55115535 => 'São Paulo - SP',
  55115536 => 'São Paulo - SP',
  55115537 => 'São Paulo - SP',
  55115538 => 'São Paulo - SP',
  55115539 => 'São Paulo - SP',
  55115541 => 'São Paulo - SP',
  55115542 => 'São Paulo - SP',
  55115543 => 'São Paulo - SP',
  55115545 => 'São Paulo - SP',
  55115546 => 'São Paulo - SP',
  55115547 => 'São Paulo - SP',
  55115548 => 'São Paulo - SP',
  55115549 => 'São Paulo - SP',
  55115555 => 'São Paulo - SP',
  55115560 => 'São Paulo - SP',
  55115561 => 'São Paulo - SP',
  55115562 => 'São Paulo - SP',
  55115563 => 'São Paulo - SP',
  55115564 => 'São Paulo - SP',
  55115566 => 'São Paulo - SP',
  55115571 => 'São Paulo - SP',
  55115572 => 'São Paulo - SP',
  55115573 => 'São Paulo - SP',
  55115574 => 'São Paulo - SP',
  55115575 => 'São Paulo - SP',
  55115576 => 'São Paulo - SP',
  55115579 => 'São Paulo - SP',
  55115581 => 'São Paulo - SP',
  55115582 => 'São Paulo - SP',
  55115583 => 'São Paulo - SP',
  55115584 => 'São Paulo - SP',
  55115585 => 'São Paulo - SP',
  55115587 => 'São Paulo - SP',
  55115588 => 'São Paulo - SP',
  55115589 => 'São Paulo - SP',
  55115591 => 'São Paulo - SP',
  55115592 => 'São Paulo - SP',
  55115593 => 'São Paulo - SP',
  55115594 => 'São Paulo - SP',
  55115595 => 'São Paulo - SP',
  55115599 => 'São Paulo - SP',
  55115611 => 'São Paulo - SP',
  55115612 => 'São Paulo - SP',
  55115614 => 'São Paulo - SP',
  55115615 => 'São Paulo - SP',
  55115616 => 'São Paulo - SP',
  55115620 => 'São Paulo - SP',
  55115621 => 'São Paulo - SP',
  55115622 => 'São Paulo - SP',
  55115623 => 'São Paulo - SP',
  55115624 => 'São Paulo - SP',
  55115625 => 'São Paulo - SP',
  55115626 => 'São Paulo - SP',
  55115631 => 'São Paulo - SP',
  55115632 => 'São Paulo - SP',
  55115634 => 'São Paulo - SP',
  55115635 => 'São Paulo - SP',
  55115641 => 'São Paulo - SP',
  55115642 => 'São Paulo - SP',
  55115643 => 'São Paulo - SP',
  55115645 => 'São Paulo - SP',
  55115646 => 'São Paulo - SP',
  55115660 => 'São Paulo - SP',
  55115661 => 'São Paulo - SP',
  55115663 => 'São Paulo - SP',
  55115665 => 'São Paulo - SP',
  55115666 => 'São Paulo - SP',
  55115667 => 'São Paulo - SP',
  55115668 => 'São Paulo - SP',
  55115669 => 'São Paulo - SP',
  55115670 => 'São Paulo - SP',
  55115671 => 'São Paulo - SP',
  55115674 => 'São Paulo - SP',
  55115675 => 'São Paulo - SP',
  55115676 => 'São Paulo - SP',
  55115678 => 'São Paulo - SP',
  55115679 => 'São Paulo - SP',
  55115681 => 'São Paulo - SP',
  55115682 => 'São Paulo - SP',
  55115683 => 'São Paulo - SP',
  55115685 => 'São Paulo - SP',
  55115686 => 'São Paulo - SP',
  55115687 => 'São Paulo - SP',
  55115691 => 'São Paulo - SP',
  55115693 => 'São Paulo - SP',
  55115695 => 'São Paulo - SP',
  55115696 => 'São Paulo - SP',
  55115704 => 'Itupeva - SP',
  55115721 => 'Mauá - SP',
  55115787 => 'São Paulo - SP',
  55115812 => 'São Paulo - SP',
  55115816 => 'São Paulo - SP',
  55115817 => 'São Paulo - SP',
  55115818 => 'São Paulo - SP',
  55115819 => 'São Paulo - SP',
  55115833 => 'São Paulo - SP',
  55115839 => 'São Paulo - SP',
  55115845 => 'São Paulo - SP',
  55115851 => 'São Paulo - SP',
  55115852 => 'São Paulo - SP',
  55115853 => 'São Paulo - SP',
  55115854 => 'São Paulo - SP',
  55115855 => 'São Paulo - SP',
  55115870 => 'São Paulo - SP',
  55115871 => 'São Paulo - SP',
  55115872 => 'São Paulo - SP',
  55115873 => 'São Paulo - SP',
  55115874 => 'São Paulo - SP',
  55115875 => 'São Paulo - SP',
  55115891 => 'São Paulo - SP',
  55115892 => 'São Paulo - SP',
  55115895 => 'São Paulo - SP',
  55115896 => 'São Paulo - SP',
  55115897 => 'São Paulo - SP',
  55115899 => 'São Paulo - SP',
  55115904 => 'São Paulo - SP',
  55115906 => 'São Paulo - SP',
  55115907 => 'São Paulo - SP',
  55115908 => 'São Paulo - SP',
  55115920 => 'São Paulo - SP',
  55115921 => 'São Paulo - SP',
  55115922 => 'São Paulo - SP',
  55115924 => 'São Paulo - SP',
  55115926 => 'São Paulo - SP',
  55115927 => 'São Paulo - SP',
  55115928 => 'São Paulo - SP',
  55115929 => 'São Paulo - SP',
  55115931 => 'São Paulo - SP',
  55115932 => 'São Paulo - SP',
  55115933 => 'São Paulo - SP',
  55115934 => 'São Paulo - SP',
  55115971 => 'São Paulo - SP',
  55115972 => 'São Paulo - SP',
  55115973 => 'São Paulo - SP',
  55115975 => 'São Paulo - SP',
  55115976 => 'São Paulo - SP',
  55115978 => 'São Paulo - SP',
  55115979 => 'São Paulo - SP',
  5512 => 'São Paulo',
  55122123 => 'Taubaté - SP',
  55122124 => 'Lorena - SP',
  55122125 => 'Taubaté - SP',
  55122126 => 'Pindamonhangaba - SP',
  55122127 => 'Jacareí - SP',
  55122128 => 'Jacareí - SP',
  55122131 => 'Guaratinguetá - SP',
  55122134 => 'São José dos Campos - SP',
  55122136 => 'São José dos Campos - SP',
  55122139 => 'São José dos Campos - SP',
  55123003 => 'São José dos Campos - SP',
  55123004 => 'São José dos Campos - SP',
  55123011 => 'Taubaté - SP',
  55123013 => 'Guaratinguetá - SP',
  55123014 => 'Jacareí - SP',
  55123018 => 'São José dos Campos - SP',
  55123019 => 'São José dos Campos - SP',
  55123021 => 'São José dos Campos - SP',
  55123022 => 'Taubaté - SP',
  55123023 => 'Jacareí - SP',
  55123025 => 'Taubaté - SP',
  55123026 => 'Taubaté - SP',
  55123027 => 'São José dos Campos - SP',
  55123034 => 'Caraguatatuba - SP',
  55123042 => 'São José dos Campos - SP',
  55123101 => 'Cachoeira Paulista - SP',
  55123102 => 'Silveiras - SP',
  55123103 => 'Cachoeira Paulista - SP',
  55123104 => 'Aparecida - SP',
  55123105 => 'Aparecida - SP',
  55123106 => 'Silveiras - SP',
  55123107 => 'Areias - SP',
  55123108 => 'Aparecida - SP',
  55123111 => 'Cunha - SP',
  55123112 => 'Potim - SP',
  55123115 => 'Arapeí - SP',
  55123116 => 'Bananal - SP',
  55123117 => 'São José do Barreiro - SP',
  55123119 => 'Campos de Cunha - SP',
  55123122 => 'Guaratinguetá - SP',
  55123123 => 'Guaratinguetá - SP',
  55123125 => 'Guaratinguetá - SP',
  55123126 => 'Guaratinguetá - SP',
  55123127 => 'Guaratinguetá - SP',
  55123128 => 'Guaratinguetá - SP',
  55123131 => 'São José dos Campos - SP',
  55123132 => 'Guaratinguetá - SP',
  55123133 => 'Guaratinguetá - SP',
  55123141 => 'Cruzeiro - SP',
  55123143 => 'Cruzeiro - SP',
  55123144 => 'Cruzeiro - SP',
  55123145 => 'Cruzeiro - SP',
  55123146 => 'Lavrinhas - SP',
  55123147 => 'Queluz - SP',
  55123151 => 'Canas - SP',
  55123152 => 'Lorena - SP',
  55123153 => 'Lorena - SP',
  55123156 => 'Piquete - SP',
  55123157 => 'Lorena - SP',
  55123159 => 'Lorena - SP',
  55123184 => 'Cruzeiro - SP',
  55123185 => 'Lorena - SP',
  55123186 => 'Cachoeira Paulista - SP',
  55123201 => 'São José dos Campos - SP',
  55123202 => 'São José dos Campos - SP',
  55123203 => 'São José dos Campos - SP',
  55123204 => 'São José dos Campos - SP',
  55123206 => 'São José dos Campos - SP',
  55123207 => 'São José dos Campos - SP',
  55123211 => 'Cruzeiro - SP',
  55123221 => 'Caçapava - SP',
  55123224 => 'Caçapava - SP',
  55123301 => 'Lorena - SP',
  55123302 => 'São José dos Campos - SP',
  55123311 => 'Aparecida - SP',
  55123321 => 'São José dos Campos - SP',
  55123322 => 'São José dos Campos - SP',
  55123351 => 'Jacareí - SP',
  55123354 => 'Jacareí - SP',
  55123411 => 'Taubaté - SP',
  55123413 => 'Taubaté - SP',
  55123421 => 'Taubaté - SP',
  55123424 => 'Taubaté - SP',
  55123426 => 'Taubaté - SP',
  55123431 => 'São José dos Campos - SP',
  55123432 => 'Taubaté - SP',
  55123512 => 'São José dos Campos - SP',
  55123519 => 'São José dos Campos - SP',
  55123521 => 'Pindamonhangaba - SP',
  55123522 => 'Pindamonhangaba - SP',
  55123527 => 'Pindamonhangaba - SP',
  55123600 => 'São José dos Campos - SP',
  55123601 => 'Taubaté - SP',
  55123602 => 'Taubaté - SP',
  55123604 => 'Pindamonhangaba - SP',
  55123607 => 'Tremembé - SP',
  55123608 => 'Taubaté - SP',
  55123609 => 'Taubaté - SP',
  55123621 => 'Taubaté - SP',
  55123622 => 'Taubaté - SP',
  55123623 => 'Taubaté - SP',
  55123624 => 'Taubaté - SP',
  55123625 => 'Taubaté - SP',
  55123626 => 'Taubaté - SP',
  55123627 => 'Taubaté - SP',
  55123629 => 'Taubaté - SP',
  55123631 => 'Taubaté - SP',
  55123632 => 'Taubaté - SP',
  55123633 => 'Taubaté - SP',
  55123634 => 'Taubaté - SP',
  55123635 => 'Taubaté - SP',
  55123637 => 'Moreira César - SP',
  55123641 => 'Pindamonhangaba - SP',
  55123642 => 'Pindamonhangaba - SP',
  55123643 => 'Pindamonhangaba - SP',
  55123644 => 'Pindamonhangaba - SP',
  55123645 => 'Pindamonhangaba - SP',
  55123646 => 'Roseira - SP',
  55123647 => 'Lagoinha - SP',
  55123648 => 'Pindamonhangaba - SP',
  55123652 => 'Caçapava - SP',
  55123653 => 'Caçapava - SP',
  55123654 => 'Caçapava - SP',
  55123655 => 'Caçapava - SP',
  55123662 => 'Campos do Jordão - SP',
  55123663 => 'Campos do Jordão - SP',
  55123664 => 'Campos do Jordão - SP',
  55123666 => 'Santo Antônio do Pinhal - SP',
  55123668 => 'Campos do Jordão - SP',
  55123669 => 'Campos do Jordão - SP',
  55123671 => 'São Luís do Paraitinga - SP',
  55123672 => 'Tremembé - SP',
  55123674 => 'Tremembé - SP',
  55123676 => 'Redenção da Serra - SP',
  55123677 => 'Natividade da Serra - SP',
  55123678 => 'Natividade da Serra - SP',
  55123681 => 'Taubaté - SP',
  55123682 => 'Taubaté - SP',
  55123686 => 'Taubaté - SP',
  55123687 => 'Quiririm - SP',
  55123832 => 'Ubatuba - SP',
  55123833 => 'Ubatuba - SP',
  55123834 => 'Ubatuba - SP',
  55123835 => 'Ubatuba - SP',
  55123836 => 'Ubatuba - SP',
  55123842 => 'Ubatuba - SP',
  55123843 => 'Ubatuba - SP',
  55123845 => 'Ubatuba - SP',
  55123848 => 'Ubatuba - SP',
  55123849 => 'Ubatuba - SP',
  55123861 => 'São Sebastião - SP',
  55123862 => 'São Sebastião - SP',
  55123863 => 'Maresias - SP',
  55123864 => 'São Sebastião - SP',
  55123867 => 'Maresias - SP',
  55123876 => 'São José dos Campos - SP',
  55123878 => 'São José dos Campos - SP',
  55123881 => 'Caraguatatuba - SP',
  55123882 => 'Caraguatatuba - SP',
  55123883 => 'Caraguatatuba - SP',
  55123884 => 'Caraguatatuba - SP',
  55123885 => 'Caraguatatuba - SP',
  55123886 => 'Caraguatatuba - SP',
  55123889 => 'Caraguatatuba - SP',
  55123891 => 'São Sebastião - SP',
  55123892 => 'São Sebastião - SP',
  55123893 => 'São Sebastião - SP',
  55123894 => 'Ilhabela - SP',
  55123895 => 'Ilhabela - SP',
  55123896 => 'Ilhabela - SP',
  55123897 => 'Caraguatatuba - SP',
  55123902 => 'São José dos Campos - SP',
  55123903 => 'São José dos Campos - SP',
  55123904 => 'São José dos Campos - SP',
  55123905 => 'São José dos Campos - SP',
  55123907 => 'São José dos Campos - SP',
  55123908 => 'São José dos Campos - SP',
  55123911 => 'São José dos Campos - SP',
  55123912 => 'São José dos Campos - SP',
  55123913 => 'São José dos Campos - SP',
  55123916 => 'São José dos Campos - SP',
  55123917 => 'São José dos Campos - SP',
  55123919 => 'São José dos Campos - SP',
  55123921 => 'São José dos Campos - SP',
  55123922 => 'São José dos Campos - SP',
  55123923 => 'São José dos Campos - SP',
  55123924 => 'São José dos Campos - SP',
  55123926 => 'São José dos Campos - SP',
  55123927 => 'São José dos Campos - SP',
  55123928 => 'São José dos Campos - SP',
  55123929 => 'São José dos Campos - SP',
  55123931 => 'São José dos Campos - SP',
  55123933 => 'São José dos Campos - SP',
  55123934 => 'São José dos Campos - SP',
  55123935 => 'São José dos Campos - SP',
  55123936 => 'São José dos Campos - SP',
  55123937 => 'São José dos Campos - SP',
  55123938 => 'São José dos Campos - SP',
  55123939 => 'São José dos Campos - SP',
  55123941 => 'São José dos Campos - SP',
  55123942 => 'São José dos Campos - SP',
  55123943 => 'São José dos Campos - SP',
  55123944 => 'São José dos Campos - SP',
  55123945 => 'São José dos Campos - SP',
  55123946 => 'São José dos Campos - SP',
  55123948 => 'São José dos Campos - SP',
  55123949 => 'São José dos Campos - SP',
  55123951 => 'Jacareí - SP',
  55123952 => 'Jacareí - SP',
  55123953 => 'Jacareí - SP',
  55123954 => 'Jacareí - SP',
  55123955 => 'Jacareí - SP',
  55123956 => 'Jacareí - SP',
  55123957 => 'São Silvestre - SP',
  55123958 => 'Jacareí - SP',
  55123959 => 'Jacareí - SP',
  55123961 => 'Jacareí - SP',
  55123962 => 'Jacareí - SP',
  55123965 => 'Jacareí - SP',
  55123966 => 'São José dos Campos - SP',
  55123971 => 'São Bento do Sapucaí - SP',
  55123972 => 'Santa Branca - SP',
  55123974 => 'Paraibuna - SP',
  55123975 => 'Santa Branca - SP',
  55123978 => 'Jambeiro - SP',
  55123979 => 'Monteiro Lobato - SP',
  55123981 => 'Cedro - SP',
  55124004 => 'São José dos Campos - SP',
  55124104 => 'Taubaté - SP',
  55124109 => 'São José dos Campos - SP',
  55124110 => 'Pindamonhangaba - SP',
  55124158 => 'Vargem Grande Paulista - SP',
  55124159 => 'Vargem Grande Paulista - SP',
  55124242 => 'Cotia - SP',
  55124408 => 'Polvilho - SP',
  55124448 => 'Cajamar - SP',
  55124611 => 'Cotia - SP',
  55124715 => 'Alumínio - SP',
  5513 => 'São Paulo',
  55132101 => 'Santos - SP',
  55132105 => 'Santos - SP',
  55132138 => 'Santos - SP',
  55133034 => 'Praia Grande - SP',
  55133036 => 'Santos - SP',
  55133048 => 'São Vicente - SP',
  55133201 => 'Santos - SP',
  55133202 => 'Santos - SP',
  55133203 => 'Santos - SP',
  55133208 => 'Santos - SP',
  55133209 => 'Santos - SP',
  55133211 => 'Santos - SP',
  55133212 => 'Santos - SP',
  55133213 => 'Santos - SP',
  55133216 => 'Santos - SP',
  55133219 => 'Santos - SP',
  55133224 => 'Santos - SP',
  55133227 => 'Santos - SP',
  55133231 => 'Santos - SP',
  55133232 => 'Santos - SP',
  55133236 => 'Santos - SP',
  55133237 => 'Santos - SP',
  55133238 => 'Santos - SP',
  55133239 => 'Santos - SP',
  55133251 => 'Santos - SP',
  55133258 => 'Santos - SP',
  55133261 => 'Santos - SP',
  55133268 => 'Bertioga - SP',
  55133269 => 'Santos - SP',
  55133271 => 'Santos - SP',
  55133272 => 'Santos - SP',
  55133273 => 'Santos - SP',
  55133278 => 'Santos - SP',
  55133279 => 'Santos - SP',
  55133281 => 'Santos - SP',
  55133284 => 'Santos - SP',
  55133285 => 'Santos - SP',
  55133286 => 'Santos - SP',
  55133288 => 'Santos - SP',
  55133289 => 'Santos - SP',
  55133291 => 'Santos - SP',
  55133295 => 'Santos - SP',
  55133296 => 'Santos - SP',
  55133298 => 'Santos - SP',
  55133299 => 'Santos - SP',
  55133302 => 'Praia Grande - SP',
  55133305 => 'Guarujá - SP',
  55133311 => 'Bertioga - SP',
  55133312 => 'Bertioga - SP',
  55133313 => 'Bertioga - SP',
  55133316 => 'Bertioga - SP',
  55133317 => 'Bertioga - SP',
  55133319 => 'Bertioga - SP',
  55133321 => 'Santos - SP',
  55133323 => 'Santos - SP',
  55133324 => 'Santos - SP',
  55133341 => 'Guarujá - SP',
  55133342 => 'Guarujá - SP',
  55133343 => 'Guarujá - SP',
  55133347 => 'Guarujá - SP',
  55133348 => 'Guarujá - SP',
  55133351 => 'Guarujá - SP',
  55133352 => 'Guarujá - SP',
  55133353 => 'Guarujá - SP',
  55133354 => 'Guarujá - SP',
  55133355 => 'Guarujá - SP',
  55133358 => 'Guarujá - SP',
  55133359 => 'Guarujá - SP',
  55133361 => 'Cubatão - SP',
  55133362 => 'Cubatão - SP',
  55133363 => 'Cubatão - SP',
  55133364 => 'Cubatão - SP',
  55133365 => 'Cubatão - SP',
  55133367 => 'Cubatão - SP',
  55133369 => 'Cubatão - SP',
  55133372 => 'Cubatão - SP',
  55133375 => 'Cubatão - SP',
  55133377 => 'Cubatão - SP',
  55133378 => 'Cubatão - SP',
  55133382 => 'Guarujá - SP',
  55133383 => 'Guarujá - SP',
  55133384 => 'Guarujá - SP',
  55133386 => 'Guarujá - SP',
  55133387 => 'Guarujá - SP',
  55133391 => 'Guarujá - SP',
  55133392 => 'Guarujá - SP',
  55133398 => 'Guarujá - SP',
  55133406 => 'São Vicente - SP',
  55133416 => 'Ana Dias - SP',
  55133418 => 'Itariri - SP',
  55133419 => 'Pedro de Toledo - SP',
  55133421 => 'Itanhaém - SP',
  55133422 => 'Itanhaém - SP',
  55133424 => 'Itanhaém - SP',
  55133425 => 'Itanhaém - SP',
  55133426 => 'Itanhaém - SP',
  55133427 => 'Itanhaém - SP',
  55133429 => 'Itanhaém - SP',
  55133445 => 'Mongaguá - SP',
  55133446 => 'Mongaguá - SP',
  55133448 => 'Mongaguá - SP',
  55133451 => 'Peruíbe - SP',
  55133453 => 'Peruíbe - SP',
  55133454 => 'Peruíbe - SP',
  55133455 => 'Peruíbe - SP',
  55133456 => 'Peruíbe - SP',
  55133457 => 'Peruíbe - SP',
  55133458 => 'Peruíbe - SP',
  55133461 => 'São Vicente - SP',
  55133462 => 'São Vicente - SP',
  55133463 => 'São Vicente - SP',
  55133464 => 'São Vicente - SP',
  55133465 => 'São Vicente - SP',
  55133466 => 'São Vicente - SP',
  55133467 => 'São Vicente - SP',
  55133468 => 'São Vicente - SP',
  55133469 => 'São Vicente - SP',
  55133471 => 'Praia Grande - SP',
  55133472 => 'Praia Grande - SP',
  55133473 => 'Praia Grande - SP',
  55133474 => 'Praia Grande - SP',
  55133477 => 'Praia Grande - SP',
  55133478 => 'Praia Grande - SP',
  55133479 => 'Praia Grande - SP',
  55133481 => 'Praia Grande - SP',
  55133491 => 'Praia Grande - SP',
  55133493 => 'Praia Grande - SP',
  55133494 => 'Praia Grande - SP',
  55133495 => 'Praia Grande - SP',
  55133499 => 'Praia Grande - SP',
  55133500 => 'Santos - SP',
  55133505 => 'Mongaguá - SP',
  55133506 => 'Mongaguá - SP',
  55133507 => 'Mongaguá - SP',
  55133513 => 'Santos - SP',
  55133519 => 'Santos - SP',
  55133561 => 'São Vicente - SP',
  55133563 => 'São Vicente - SP',
  55133564 => 'São Vicente - SP',
  55133565 => 'São Vicente - SP',
  55133566 => 'São Vicente - SP',
  55133567 => 'São Vicente - SP',
  55133568 => 'São Vicente - SP',
  55133569 => 'São Vicente - SP',
  55133576 => 'São Vicente - SP',
  55133579 => 'São Vicente - SP',
  55133591 => 'Praia Grande - SP',
  55133592 => 'Praia Grande - SP',
  55133594 => 'Praia Grande - SP',
  55133596 => 'Praia Grande - SP',
  55133821 => 'Registro - SP',
  55133822 => 'Registro - SP',
  55133828 => 'Registro - SP',
  55133829 => 'Registro - SP',
  55133841 => 'Iguape - SP',
  55133842 => 'Ilha Comprida - SP',
  55133843 => 'Ilha Comprida - SP',
  55133844 => 'Juquiá - SP',
  55133846 => 'Pedro Barros - SP',
  55133847 => 'Miracatu - SP',
  55133848 => 'Iguape - SP',
  55133849 => 'Iguape - SP',
  55133851 => 'Cananéia - SP',
  55133852 => 'Ariri - SP',
  55133854 => 'Cajati - SP',
  55133855 => 'Tatuí - SP',
  55133856 => 'Pariquera-Açu - SP',
  55133862 => 'Colonização - SP',
  55133864 => 'Jacupiranga - SP',
  55133871 => 'Eldorado - SP',
  55133872 => 'Sete Barras - SP',
  55133877 => 'Santos - SP',
  55133879 => 'Barra do Braço - SP',
  55134002 => 'Santos - SP',
  55134003 => 'Praia Grande - SP',
  55134004 => 'Santos - SP',
  55134010 => 'Santos - SP',
  55134102 => 'Guarujá - SP',
  55134104 => 'São Vicente - SP',
  5514 => 'São Paulo',
  55142104 => 'Jaú - SP',
  55142105 => 'Marília - SP',
  55142106 => 'Bauru - SP',
  55142107 => 'Bauru - SP',
  55142108 => 'Bauru - SP',
  55142109 => 'Bauru - SP',
  55142122 => 'Avaré - SP',
  55143003 => 'Bauru - SP',
  55143004 => 'Bauru - SP',
  55143014 => 'Bauru - SP',
  55143022 => 'Avaré - SP',
  55143025 => 'Lins - SP',
  55143026 => 'Ourinhos - SP',
  55143032 => 'Jaú - SP',
  55143102 => 'Bauru - SP',
  55143103 => 'Bauru - SP',
  55143104 => 'Bauru - SP',
  55143106 => 'Bauru - SP',
  55143108 => 'Bauru - SP',
  55143112 => 'Botucatu - SP',
  55143201 => 'Bauru - SP',
  55143202 => 'Bauru - SP',
  55143203 => 'Bauru - SP',
  55143212 => 'Bauru - SP',
  55143214 => 'Bauru - SP',
  55143218 => 'Bauru - SP',
  55143221 => 'Marília - SP',
  55143222 => 'Bauru - SP',
  55143223 => 'Bauru - SP',
  55143224 => 'Bauru - SP',
  55143226 => 'Bauru - SP',
  55143227 => 'Bauru - SP',
  55143231 => 'Bauru - SP',
  55143232 => 'Bauru - SP',
  55143233 => 'Bauru - SP',
  55143234 => 'Bauru - SP',
  55143236 => 'Bauru - SP',
  55143237 => 'Bauru - SP',
  55143238 => 'Bauru - SP',
  55143239 => 'Bauru - SP',
  55143252 => 'Pederneiras - SP',
  55143261 => 'Agudos - SP',
  55143262 => 'Agudos - SP',
  55143263 => 'Lençóis Paulista - SP',
  55143264 => 'Lençóis Paulista - SP',
  55143265 => 'Piratininga - SP',
  55143267 => 'Borebi - SP',
  55143268 => 'Macatuba - SP',
  55143269 => 'Lençóis Paulista - SP',
  55143273 => 'Fernão - SP',
  55143274 => 'Gália - SP',
  55143275 => 'Paulistânia - SP',
  55143276 => 'Bauru - SP',
  55143277 => 'Bauru - SP',
  55143278 => 'Lençóis Paulista - SP',
  55143279 => 'Tibiriçá - SP',
  55143281 => 'Bauru - SP',
  55143282 => 'Duartina - SP',
  55143283 => 'Pederneiras - SP',
  55143284 => 'Pederneiras - SP',
  55143285 => 'Cabrália Paulista - SP',
  55143286 => 'Lucianópolis - SP',
  55143287 => 'Avaí - SP',
  55143288 => 'Bauru - SP',
  55143291 => 'Domélia - SP',
  55143292 => 'Pederneiras - SP',
  55143293 => 'Guaianás - SP',
  55143294 => 'Iacanga - SP',
  55143295 => 'Boracéia - SP',
  55143296 => 'Arealva - SP',
  55143297 => 'Bairro de Santa Izabel - SP',
  55143298 => 'Macatuba - SP',
  55143302 => 'Ourinhos - SP',
  55143303 => 'Marília - SP',
  55143305 => 'Piraju - SP',
  55143307 => 'Ibirarema - SP',
  55143308 => 'Fartura - SP',
  55143311 => 'Marília - SP',
  55143313 => 'Bauru - SP',
  55143316 => 'Marília - SP',
  55143318 => 'Marília - SP',
  55143321 => 'Bauru - SP',
  55143322 => 'Ourinhos - SP',
  55143324 => 'Ourinhos - SP',
  55143325 => 'Ourinhos - SP',
  55143326 => 'Ourinhos - SP',
  55143332 => 'Santa Cruz do Rio Pardo - SP',
  55143335 => 'Ourinhos - SP',
  55143342 => 'Chavantes - SP',
  55143343 => 'Canitar - SP',
  55143344 => 'Ipaussu - SP',
  55143346 => 'Bernardino de Campos - SP',
  55143351 => 'Piraju - SP',
  55143352 => 'Piraju - SP',
  55143354 => 'Botucatu - SP',
  55143355 => 'Manduri - SP',
  55143356 => 'Manduri - SP',
  55143357 => 'Óleo - SP',
  55143361 => 'Botucatu - SP',
  55143366 => 'Bauru - SP',
  55143372 => 'Santa Cruz do Rio Pardo - SP',
  55143373 => 'Santa Cruz do Rio Pardo - SP',
  55143374 => 'Caporanga - SP',
  55143375 => 'Espírito Santo do Turvo - SP',
  55143376 => 'Sodrélia - SP',
  55143377 => 'São Pedro do Turvo - SP',
  55143378 => 'Salto Grande - SP',
  55143379 => 'Ribeirão do Sul - SP',
  55143382 => 'Fartura - SP',
  55143385 => 'Tejupá - SP',
  55143386 => 'Taguaí - SP',
  55143387 => 'Sarutaiá - SP',
  55143389 => 'Timburi - SP',
  55143402 => 'Marília - SP',
  55143404 => 'Tupã - SP',
  55143405 => 'Pompéia - SP',
  55143406 => 'Garça - SP',
  55143407 => 'Garça - SP',
  55143408 => 'Marília - SP',
  55143411 => 'Jaú - SP',
  55143413 => 'Marília - SP',
  55143414 => 'Marília - SP',
  55143415 => 'Marília - SP',
  55143416 => 'Jaú - SP',
  55143417 => 'Marília - SP',
  55143425 => 'Marília - SP',
  55143432 => 'Marília - SP',
  55143433 => 'Marília - SP',
  55143441 => 'Tupã - SP',
  55143451 => 'Marília - SP',
  55143452 => 'Pompéia - SP',
  55143453 => 'Marília - SP',
  55143454 => 'Marília - SP',
  55143456 => 'Oriente - SP',
  55143457 => 'Oscar Bressane - SP',
  55143458 => 'Queiroz - SP',
  55143471 => 'Garça - SP',
  55143472 => 'Ubirajara - SP',
  55143473 => 'Alvinlândia - SP',
  55143474 => 'Lupércio - SP',
  55143475 => 'Ocauçu - SP',
  55143476 => 'Campos Novos Paulista - SP',
  55143477 => 'Arco-Íris - SP',
  55143478 => 'Bastos - SP',
  55143479 => 'Avencas - SP',
  55143481 => 'Marília - SP',
  55143484 => 'Álvaro de Carvalho - SP',
  55143486 => 'Herculândia - SP',
  55143487 => 'Júlio Mesquita - SP',
  55143488 => 'Quintana - SP',
  55143489 => 'Iacri - SP',
  55143491 => 'Tupã - SP',
  55143492 => 'Vera Cruz - SP',
  55143493 => 'Varpa - SP',
  55143495 => 'Tupã - SP',
  55143496 => 'Tupã - SP',
  55143522 => 'Lins - SP',
  55143523 => 'Lins - SP',
  55143529 => 'Lins - SP',
  55143532 => 'Lins - SP',
  55143533 => 'Lins - SP',
  55143535 => 'Pompéia - SP',
  55143541 => 'Promissão - SP',
  55143542 => 'Promissão - SP',
  55143543 => 'Promissão - SP',
  55143546 => 'Sabino - SP',
  55143547 => 'Guaiçara - SP',
  55143552 => 'Getulina - SP',
  55143553 => 'Guaimbê - SP',
  55143554 => 'Cafelândia - SP',
  55143556 => 'Cafelândia - SP',
  55143572 => 'Pirajuí - SP',
  55143581 => 'Pongaí - SP',
  55143582 => 'Uru - SP',
  55143583 => 'Balbinos - SP',
  55143584 => 'Pirajuí - SP',
  55143585 => 'Pirajuí - SP',
  55143586 => 'Guarantã - SP',
  55143587 => 'Presidente Alves - SP',
  55143589 => 'Reginópolis - SP',
  55143597 => 'Presidente Alves - SP',
  55143601 => 'Jaú - SP',
  55143602 => 'Jaú - SP',
  55143604 => 'Barra Bonita - SP',
  55143621 => 'Jaú - SP',
  55143622 => 'Jaú - SP',
  55143623 => 'Jaú - SP',
  55143624 => 'Jaú - SP',
  55143625 => 'Jaú - SP',
  55143626 => 'Jaú - SP',
  55143629 => 'Potunduva - SP',
  55143632 => 'Dois Córregos - SP',
  55143641 => 'Barra Bonita - SP',
  55143642 => 'Barra Bonita - SP',
  55143644 => 'Igaraçu do Tietê - SP',
  55143646 => 'Mineiros do Tietê - SP',
  55143649 => 'Barra Bonita - SC',
  55143652 => 'Dois Córregos - SP',
  55143653 => 'Brotas - SP',
  55143654 => 'Brotas - SP',
  55143656 => 'Torrinha - SP',
  55143662 => 'Bariri - SP',
  55143664 => 'Itapuí - SP',
  55143666 => 'Bocaina - SP',
  55143667 => 'Itaju - SP',
  55143668 => 'Itaju - SP',
  55143711 => 'Avaré - SP',
  55143713 => 'Paranapanema - SP',
  55143714 => 'Cerqueira César - SP',
  55143722 => 'Tupã - SP',
  55143731 => 'Avaré - SP',
  55143732 => 'Avaré - SP',
  55143733 => 'Avaré - SP',
  55143737 => 'Garça - SP',
  55143761 => 'Itaí - SP',
  55143762 => 'Taquarituba - SP',
  55143764 => 'Iaras - SP',
  55143765 => 'Águas de Santa Bárbara - SP',
  55143766 => 'Arandu - SP',
  55143767 => 'Coronel Macedo - SP',
  55143768 => 'Jurumirim - SP',
  55143769 => 'Holambra II - SP',
  55143811 => 'Botucatu - SP',
  55143812 => 'São Manuel - SP',
  55143813 => 'Botucatu - SP',
  55143814 => 'Botucatu - SP',
  55143815 => 'Botucatu - SP',
  55143841 => 'São Manuel - SP',
  55143842 => 'São Manuel - SP',
  55143844 => 'Pratânia - SP',
  55143845 => 'Conchas - SP',
  55143846 => 'Areiópolis - SP',
  55143847 => 'Itatinga - SP',
  55143848 => 'Itatinga - SP',
  55143849 => 'Bairro de Santana - SP',
  55143879 => 'Bauru - SP',
  55143880 => 'Botucatu - SP',
  55143881 => 'Botucatu - SP',
  55143882 => 'Botucatu - SP',
  55143883 => 'Bofete - SP',
  55143884 => 'Anhembi - SP',
  55143885 => 'Pirambóia - SP',
  55143886 => 'Pardinho - SP',
  55143888 => 'Pereiras - SP',
  55144004 => 'Bauru - SP',
  55144009 => 'Bauru - SP',
  55144103 => 'Jaú - SP',
  55144104 => 'Garça - SP',
  5515 => 'São Paulo',
  55152101 => 'Sorocaba - SP',
  55152102 => 'Sorocaba - SP',
  55152104 => 'Sorocaba - SP',
  55152105 => 'Sorocaba - SP',
  55152107 => 'Porto Feliz - SP',
  55152108 => 'Tietê - SP',
  55153003 => 'Sorocaba - SP',
  55153011 => 'Sorocaba - SP',
  55153012 => 'Sorocaba - SP',
  55153013 => 'Sorocaba - SP',
  55153014 => 'Sorocaba - SP',
  55153016 => 'Sorocaba - SP',
  55153017 => 'Sorocaba - SP',
  55153022 => 'Tatuí - SP',
  55153026 => 'Sorocaba - SP',
  55153115 => 'Boituva - SP',
  55153141 => 'Sorocaba - SP',
  55153202 => 'Sorocaba - SP',
  55153205 => 'Tatuí - SP',
  55153207 => 'Gramadinho - SP',
  55153211 => 'Sorocaba - SP',
  55153212 => 'Sorocaba - SP',
  55153213 => 'Sorocaba - SP',
  55153217 => 'Sorocaba - SP',
  55153218 => 'Sorocaba - SP',
  55153219 => 'Sorocaba - SP',
  55153221 => 'Sorocaba - SP',
  55153222 => 'Sorocaba - SP',
  55153223 => 'Sorocaba - SP',
  55153224 => 'Sorocaba - SP',
  55153225 => 'Sorocaba - SP',
  55153226 => 'Sorocaba - SP',
  55153227 => 'Sorocaba - SP',
  55153228 => 'Sorocaba - SP',
  55153229 => 'Sorocaba - SP',
  55153231 => 'Sorocaba - SP',
  55153232 => 'Sorocaba - SP',
  55153233 => 'Sorocaba - SP',
  55153234 => 'Sorocaba - SP',
  55153235 => 'Sorocaba - SP',
  55153236 => 'Sorocaba - SP',
  55153237 => 'Sorocaba - SP',
  55153238 => 'Sorocaba - SP',
  55153239 => 'Sorocaba - SP',
  55153241 => 'Ibiúna - SP',
  55153242 => 'Votorantim - SP',
  55153243 => 'Votorantim - SP',
  55153244 => 'Piedade - SP',
  55153245 => 'Votorantim - SP',
  55153246 => 'Cesário Lange - SP',
  55153247 => 'Votorantim - SP',
  55153248 => 'Ibiúna - SP',
  55153249 => 'Ibiúna - SP',
  55153251 => 'Tatuí - SP',
  55153252 => 'Torre de Pedra - SP',
  55153253 => 'Quadra - SP',
  55153255 => 'Angatuba - SP',
  55153256 => 'Campina do Monte Alegre - SP',
  55153257 => 'Porangaba - SP',
  55153258 => 'Guareí - SP',
  55153259 => 'Tatuí - SP',
  55153261 => 'Porto Feliz - SP',
  55153262 => 'Porto Feliz - SP',
  55153263 => 'Boituva - SP',
  55153264 => 'Águia da Castelo - SP',
  55153266 => 'Iperó - SP',
  55153267 => 'Capela do Alto - SP',
  55153268 => 'Boituva - SP',
  55153271 => 'Itapetininga - SP',
  55153272 => 'Itapetininga - SP',
  55153273 => 'Itapetininga - SP',
  55153274 => 'Alambari - SP',
  55153275 => 'Itapetininga - SP',
  55153276 => 'Sarapuí - SP',
  55153277 => 'Tapiraí - SP',
  55153278 => 'Pilar do Sul - SP',
  55153279 => 'Sao Miguel Arcanjo - SP',
  55153281 => 'Araçoiaba da Serra - SP',
  55153282 => 'Tietê - SP',
  55153283 => 'Laranjal Paulista - SP',
  55153284 => 'Cerquilho - SP',
  55153285 => 'Tietê - SP',
  55153286 => 'Jumirim - SP',
  55153287 => 'Laranjal Paulista - SP',
  55153288 => 'Cerquilho - SP',
  55153289 => 'Ibiúna - SP',
  55153291 => 'Araçoiaba da Serra - SP',
  55153292 => 'Salto de Pirapora - SP',
  55153293 => 'Sorocaba - SP',
  55153294 => 'Ibiúna - SP',
  55153297 => 'Araçoiaba da Serra - SP',
  55153298 => 'Pilar do Sul - SP',
  55153299 => 'São Paulo',
  55153302 => 'Sorocaba - SP',
  55153305 => 'Tatuí - SP',
  55153307 => 'Itapetininga - SP',
  55153311 => 'Sorocaba - SP',
  55153313 => 'Sorocaba - SP',
  55153321 => 'Sorocaba - SP',
  55153324 => 'Tatuí - SP',
  55153325 => 'Sorocaba - SP',
  55153327 => 'Sorocaba - SP',
  55153330 => 'Sorocaba - SP',
  55153334 => 'Sorocaba - SP',
  55153335 => 'Sorocaba - SP',
  55153336 => 'Sorocaba - SP',
  55153339 => 'Sorocaba - SP',
  55153342 => 'Sorocaba - SP',
  55153343 => 'Votorantim - SP',
  55153344 => 'Piedade - SP',
  55153349 => 'Ibiúna - SP',
  55153353 => 'Votorantim - SP',
  55153355 => 'Angatuba - SP',
  55153363 => 'Boituva - SP',
  55153364 => 'Boituva - SP',
  55153372 => 'Itapetininga - SP',
  55153373 => 'Itapetininga - SP',
  55153376 => 'Itapetininga - SP',
  55153378 => 'Pilar do Sul - SP',
  55153379 => 'São Miguel Arcanjo - SP',
  55153383 => 'Laranjal Paulista - SP',
  55153384 => 'Cerquilho - SP',
  55153388 => 'Sorocaba - SP',
  55153392 => 'Itapetininga - SP',
  55153394 => 'Ibiúna - SP',
  55153411 => 'Sorocaba - SP',
  55153412 => 'Sorocaba - SP',
  55153414 => 'Sorocaba - SP',
  55153415 => 'Sorocaba - SP',
  55153416 => 'Votorantim - SP',
  55153417 => 'Sorocaba - SP',
  55153418 => 'Sorocaba - SP',
  55153431 => 'Itararé - SP',
  55153451 => 'Tatuí - SP',
  55153459 => 'Iperó - SP',
  55153461 => 'Porto Feliz - SP',
  55153467 => 'Capela do Alto - SP',
  55153478 => 'Pilar do Sul - SP',
  55153491 => 'Salto de Pirapora - SP',
  55153492 => 'Salto de Pirapora - SP',
  55153494 => 'Ibiúna - SP',
  55153511 => 'Itapetininga - SP',
  55153519 => 'Sorocaba - SP',
  55153521 => 'Itapeva - SP',
  55153522 => 'Itapeva - SP',
  55153523 => 'Caputera - SP',
  55153524 => 'Itapeva - SP',
  55153526 => 'Itapeva - SP',
  55153527 => 'Itapetininga - SP',
  55153531 => 'Itararé - SP',
  55153532 => 'Itararé - SP',
  55153533 => 'Bom Sucesso de Itararé - SP',
  55153534 => 'Taquarivaí - SP',
  55153535 => 'Nova Campina - SP',
  55153537 => 'Itapetininga - SP',
  55153542 => 'Capão Bonito - SP',
  55153543 => 'Capão Bonito - SP',
  55153544 => 'Ribeirão Grande - SP',
  55153546 => 'Buri - SP',
  55153547 => 'Guapiara - SP',
  55153548 => 'Itapirapuã Paulista - SP',
  55153551 => 'Ribeirão Branco - SP',
  55153552 => 'Apiaí - SP',
  55153553 => 'Ribeirão Branco - SP',
  55153554 => 'Barra do Chapéu - SP',
  55153555 => 'Ribeira - SP',
  55153556 => 'Iporanga - SP',
  55153557 => 'Itaóca - SP',
  55153558 => 'Apiaí - SP',
  55153562 => 'Itaberá - SP',
  55153563 => 'Guapiara - SP',
  55153565 => 'Itaporanga - SP',
  55153566 => 'Bairro Palmitalzinho - SP',
  55153571 => 'Riversul - SP',
  55153572 => 'Itaberá - SP',
  55153573 => 'Barão de Antonina - SP',
  55153577 => 'Barra do Turvo - SP',
  55153584 => 'Taquarivaí - SP',
  55153624 => 'Itapeva - SP',
  55153646 => 'Buri - SP',
  55153653 => 'Capão Bonito - SP',
  55154009 => 'Sorocaba - SP',
  5516 => 'São Paulo',
  55162101 => 'Ribeirão Preto - SP',
  55162102 => 'Ribeirão Preto - SP',
  55162105 => 'Sertãozinho - SP',
  55162106 => 'São Carlos - SP',
  55162107 => 'São Carlos - SP',
  55162108 => 'Araraquara - SP',
  55162109 => 'Araraquara - SP',
  55162111 => 'Ribeirão Preto - SP',
  55162132 => 'Ribeirão Preto - SP',
  55162133 => 'Ribeirão Preto - SP',
  55162137 => 'Ribeirão Preto - SP',
  55162138 => 'Ribeirão Preto - SP',
  55163010 => 'Araraquara - SP',
  55163014 => 'Araraquara - SP',
  55163024 => 'Ribeirão Preto - SP',
  55163025 => 'Franca - SP',
  55163026 => 'Franca - SP',
  55163041 => 'Sertãozinho - SP',
  55163042 => 'Sertãozinho - SP',
  55163075 => 'Ribeirão Preto - SP',
  55163101 => 'Ribeirão Preto - SP',
  55163111 => 'Franca - SP',
  55163114 => 'Araraquara - SP',
  55163116 => 'São Carlos - SP',
  55163133 => 'Cristais Paulista - SP',
  55163134 => 'Jeriquara - SP',
  55163135 => 'Rifaina - SP',
  55163142 => 'São José da Bela Vista - SP',
  55163143 => 'Restinga - SP',
  55163145 => 'Patrocínio Paulista - SP',
  55163146 => 'Itirapuã - SP',
  55163171 => 'Pedregulho - SP',
  55163172 => 'Igarapava - SP',
  55163173 => 'Igarapava - SP',
  55163176 => 'Furnas Vila Residencial - SP',
  55163202 => 'Jaboticabal - SP',
  55163203 => 'Jaboticabal - SP',
  55163204 => 'Jaboticabal - SP',
  55163209 => 'Jaboticabal - SP',
  55163214 => 'Araraquara - SP',
  55163221 => 'Matão - SP',
  55163231 => 'Guariba - SP',
  55163234 => 'Ribeirão Preto - SP',
  55163236 => 'Ribeirão Preto - SP',
  55163241 => 'Monte Alto - SP',
  55163242 => 'Monte Alto - SP',
  55163243 => 'Monte Alto - SP',
  55163244 => 'Monte Alto - SP',
  55163246 => 'Taiúva - SP',
  55163251 => 'Guariba - SP',
  55163252 => 'Taquaritinga - SP',
  55163253 => 'Taquaritinga - SP',
  55163254 => 'Guariroba - SP',
  55163256 => 'Santa Ernestina - SP',
  55163257 => 'Cândido Rodrigues - SP',
  55163258 => 'Fernando Prestes - SP',
  55163262 => 'Itápolis - SP',
  55163263 => 'Itápolis - SP',
  55163265 => 'Tapinas - SP',
  55163266 => 'Borborema - SP',
  55163273 => 'Itápolis - SP',
  55163275 => 'Taiaçu - SP',
  55163286 => 'Ribeirão Preto - SP',
  55163287 => 'Vista Alegre do Alto - SP',
  55163301 => 'Araraquara - SP',
  55163303 => 'Araraquara - SP',
  55163304 => 'Araraquara - SP',
  55163305 => 'Araraquara - SP',
  55163306 => 'São Carlos - SP',
  55163307 => 'São Carlos - SP',
  55163308 => 'Gavião Peixoto - SP',
  55163311 => 'Araraquara - SP',
  55163315 => 'Ribeirão Preto - SP',
  55163321 => 'Tabatinga - SP',
  55163322 => 'Araraquara - SP',
  55163323 => 'Ribeirão Preto - SP',
  55163324 => 'Araraquara - SP',
  55163326 => 'Boa Esperança do Sul - SP',
  55163331 => 'Araraquara - SP',
  55163332 => 'Araraquara - SP',
  55163333 => 'Araraquara - SP',
  55163334 => 'Araraquara - SP',
  55163335 => 'Araraquara - SP',
  55163336 => 'Araraquara - SP',
  55163337 => 'Araraquara - SP',
  55163338 => 'Gavião Peixoto - SP',
  55163339 => 'Araraquara - SP',
  55163341 => 'Ibitinga - SP',
  55163342 => 'Ibitinga - SP',
  55163343 => 'Ibaté - SP',
  55163344 => 'Ribeirão Bonito - SP',
  55163345 => 'Dourado - SP',
  55163346 => 'Boa Esperança do Sul - SP',
  55163347 => 'Cambaratiba - SP',
  55163348 => 'Motuca - SP',
  55163349 => 'Trabiju - SP',
  55163351 => 'São Carlos - SP',
  55163352 => 'Ibitinga - SP',
  55163353 => 'Ibaté - SP',
  55163354 => 'Guarapiranga - SP',
  55163357 => 'Araraquara - SP',
  55163358 => 'Araraquara - SP',
  55163361 => 'São Carlos - SP',
  55163362 => 'São Carlos - SP',
  55163363 => 'São Carlos - SP',
  55163364 => 'São Carlos - SP',
  55163366 => 'São Carlos - SP',
  55163367 => 'São Carlos - SP',
  55163368 => 'São Carlos - SP',
  55163371 => 'São Carlos - SP',
  55163372 => 'São Carlos - SP',
  55163373 => 'São Carlos - SP',
  55163374 => 'São Carlos - SP',
  55163375 => 'São Carlos - SP',
  55163376 => 'São Carlos - SP',
  55163377 => 'São Carlos - SP',
  55163378 => 'São Carlos - SP',
  55163379 => 'Fazenda Babilônia - SP',
  55163382 => 'Matão - SP',
  55163383 => 'Matão - SP',
  55163384 => 'Matão - SP',
  55163385 => 'Tabatinga - SP',
  55163386 => 'Dobrada - SP',
  55163387 => 'Nova Europa - SP',
  55163389 => 'São Lourenço do Turvo - SP',
  55163392 => 'Américo Brasiliense - SP',
  55163393 => 'Américo Brasiliense - SP',
  55163394 => 'Matão - SP',
  55163395 => 'Rincão - SP',
  55163396 => 'Santa Lúcia - SP',
  55163397 => 'Araraquara - SP',
  55163398 => 'Fazenda Babilônia - SP',
  55163403 => 'Franca - SP',
  55163405 => 'Franca - SP',
  55163406 => 'Franca - SP',
  55163409 => 'Franca - SP',
  55163411 => 'São Carlos - SP',
  55163412 => 'São Carlos - SP',
  55163413 => 'São Carlos - SP',
  55163415 => 'São Carlos - SP',
  55163421 => 'Ribeirão Preto - SP',
  55163432 => 'Franca - SP',
  55163434 => 'Ribeirão Preto - SP',
  55163456 => 'Ribeirão Preto - SP',
  55163461 => 'Araraquara - SP',
  55163463 => 'Araraquara - SP',
  55163472 => 'Araraquara - SP',
  55163475 => 'Sertãozinho - SP',
  55163482 => 'Cravinhos - SP',
  55163488 => 'São Simão - SP',
  55163489 => 'Serrana - SP',
  55163491 => 'Sertãozinho - SP',
  55163501 => 'São Carlos - SP',
  55163506 => 'Matão - SP',
  55163508 => 'Araraquara - SP',
  55163509 => 'São Carlos - SP',
  55163511 => 'Sertãozinho - SP',
  55163512 => 'Ribeirão Preto - SP',
  55163513 => 'Sertãozinho - SP',
  55163514 => 'Ribeirão Preto - SP',
  55163515 => 'Ribeirão Preto - SP',
  55163518 => 'Cravinhos - SP',
  55163519 => 'Ribeirão Preto - SP',
  55163521 => 'Sertãozinho - SP',
  55163524 => 'Sertãozinho - SP',
  55163567 => 'Cajuru - SP',
  55163601 => 'Ribeirão Preto - SP',
  55163603 => 'Ribeirão Preto - SP',
  55163604 => 'Ribeirão Preto - SP',
  55163605 => 'Ribeirão Preto - SP',
  55163607 => 'Ribeirão Preto - SP',
  55163610 => 'Ribeirão Preto - SP',
  55163611 => 'Ribeirão Preto - SP',
  55163612 => 'Ribeirão Preto - SP',
  55163615 => 'Ribeirão Preto - SP',
  55163617 => 'Ribeirão Preto - SP',
  55163618 => 'Ribeirão Preto - SP',
  55163620 => 'Ribeirão Preto - SP',
  55163621 => 'Ribeirão Preto - SP',
  55163622 => 'Ribeirão Preto - SP',
  55163623 => 'Ribeirão Preto - SP',
  55163624 => 'Ribeirão Preto - SP',
  55163625 => 'Ribeirão Preto - SP',
  55163626 => 'Ribeirão Preto - SP',
  55163627 => 'Ribeirão Preto - SP',
  55163628 => 'Ribeirão Preto - SP',
  55163629 => 'Ribeirão Preto - SP',
  55163630 => 'Ribeirão Preto - SP',
  55163632 => 'Ribeirão Preto - SP',
  55163633 => 'Ribeirão Preto - SP',
  55163635 => 'Ribeirão Preto - SP',
  55163636 => 'Ribeirão Preto - SP',
  55163637 => 'Ribeirão Preto - SP',
  55163638 => 'Ribeirão Preto - SP',
  55163639 => 'Ribeirão Preto - SP',
  55163659 => 'Brodowski - SP',
  55163660 => 'Batatais - SP',
  55163661 => 'Batatais - SP',
  55163662 => 'Batatais - SP',
  55163663 => 'Jardinópolis - SP',
  55163664 => 'Brodowski - SP',
  55163665 => 'Altinópolis - SP',
  55163666 => 'Santa Cruz da Esperança - SP',
  55163667 => 'Cajuru - SP',
  55163668 => 'Santo Antônio da Alegria - SP',
  55163669 => 'Cássia dos Coqueiros - SP',
  55163690 => 'Jardinópolis - SP',
  55163693 => 'Jardinópolis - SP',
  55163704 => 'Franca - SP',
  55163706 => 'Franca - SP',
  55163707 => 'Franca - SP',
  55163708 => 'Franca - SP',
  55163711 => 'Franca - SP',
  55163721 => 'Franca - SP',
  55163722 => 'Franca - SP',
  55163723 => 'Franca - SP',
  55163724 => 'Franca - SP',
  55163725 => 'Franca - SP',
  55163726 => 'Orlândia - SP',
  55163728 => 'São Joaquim da Barra - SP',
  55163729 => 'Ituverava - SP',
  55163749 => 'Ribeirão Corrente - SP',
  55163751 => 'Buritizal - SP',
  55163752 => 'Aramina - SP',
  55163759 => 'Franca - SP',
  55163761 => 'Batatais - SP',
  55163763 => 'Jardinópolis - SP',
  55163797 => 'Ribeirão Preto - SP',
  55163810 => 'São Joaquim da Barra - SP',
  55163811 => 'São Joaquim da Barra - SP',
  55163818 => 'São Joaquim da Barra - SP',
  55163820 => 'Orlândia - SP',
  55163821 => 'Orlândia - SP',
  55163829 => 'Ituverava - SP',
  55163830 => 'Ituverava - SP',
  55163831 => 'Guará - SP',
  55163832 => 'Ipuã - SP',
  55163835 => 'Miguelópolis - SP',
  55163838 => 'Ituverava - SP',
  55163839 => 'Ituverava - SP',
  55163847 => 'Nuporanga - SP',
  55163851 => 'Morro Agudo - SP',
  55163852 => 'Sales Oliveira - SP',
  55163859 => 'Orlândia - SP',
  55163877 => 'Ribeirão Preto - SP',
  55163878 => 'Ribeirão Preto - SP',
  55163902 => 'Ribeirão Preto - SP',
  55163904 => 'Ribeirão Preto - SP',
  55163911 => 'Ribeirão Preto - SP',
  55163913 => 'Ribeirão Preto - SP',
  55163914 => 'Ribeirão Preto - SP',
  55163916 => 'Ribeirão Preto - SP',
  55163917 => 'Ribeirão Preto - SP',
  55163919 => 'Ribeirão Preto - SP',
  55163931 => 'Ribeirão Preto - SP',
  55163934 => 'Ribeirão Preto - SP',
  55163941 => 'Ribeirão Preto - SP',
  55163942 => 'Sertãozinho - SP',
  55163943 => 'Barrinha - SP',
  55163944 => 'Dumont - SP',
  55163945 => 'Sertãozinho - SP',
  55163946 => 'Sertãozinho - SP',
  55163947 => 'Sertãozinho - SP',
  55163949 => 'Cruz das Posses - SP',
  55163951 => 'Cravinhos - SP',
  55163952 => 'Pitangueiras - SP',
  55163953 => 'Pontal - SP',
  55163954 => 'Santa Rosa de Viterbo - SP',
  55163956 => 'Pontal - SP',
  55163957 => 'Ibitiúva - SP',
  55163958 => 'Taquaral - SP',
  55163961 => 'Ribeirão Preto - SP',
  55163962 => 'Ribeirão Preto - SP',
  55163963 => 'Ribeirão Preto - SP',
  55163964 => 'Ribeirão Preto - SP',
  55163965 => 'Ribeirão Preto - SP',
  55163966 => 'Ribeirão Preto - SP',
  55163967 => 'Ribeirão Preto - SP',
  55163968 => 'Ribeirão Preto - SP',
  55163969 => 'Ribeirão Preto - SP',
  55163972 => 'Bonfim Paulista - SP',
  55163973 => 'Guatapará - SP',
  55163974 => 'Ribeirão Preto - SP',
  55163975 => 'Ribeirão Preto - SP',
  55163976 => 'Ribeirão Preto - SP',
  55163977 => 'Ribeirão Preto - SP',
  55163979 => 'Ribeirão Preto - SP',
  55163981 => 'Pradópolis - SP',
  55163982 => 'Serra Azul - SP',
  55163983 => 'Luís Antônio - SP',
  55163984 => 'São Simão - SP',
  55163986 => 'Luís Antônio - SP',
  55163987 => 'Serrana - SP',
  55163993 => 'Ribeirão Preto - SP',
  55163995 => 'Ribeirão Preto - SP',
  55163996 => 'Ribeirão Preto - SP',
  55164003 => 'Ribeirão Preto - SP',
  5517 => 'São Paulo',
  55172136 => 'São José do Rio Preto - SP',
  55172137 => 'São José do Rio Preto - SP',
  55173011 => 'São José do Rio Preto - SP',
  55173012 => 'São José do Rio Preto - SP',
  55173016 => 'São José do Rio Preto - SP',
  55173043 => 'Barretos - SP',
  55173044 => 'Bebedouro - SP',
  55173045 => 'Catanduva - SP',
  55173101 => 'Uchoa - SP',
  55173121 => 'São José do Rio Preto - SP',
  55173122 => 'São José do Rio Preto - SP',
  55173201 => 'São José do Rio Preto - SP',
  55173202 => 'São José do Rio Preto - SP',
  55173203 => 'São José do Rio Preto - SP',
  55173209 => 'São José do Rio Preto - SP',
  55173211 => 'São José do Rio Preto - SP',
  55173212 => 'São José do Rio Preto - SP',
  55173213 => 'São José do Rio Preto - SP',
  55173214 => 'São José do Rio Preto - SP',
  55173215 => 'São José do Rio Preto - SP',
  55173216 => 'São José do Rio Preto - SP',
  55173217 => 'São José do Rio Preto - SP',
  55173218 => 'São José do Rio Preto - SP',
  55173219 => 'São José do Rio Preto - SP',
  55173222 => 'São José do Rio Preto - SP',
  55173223 => 'São José do Rio Preto - SP',
  55173224 => 'São José do Rio Preto - SP',
  55173225 => 'São José do Rio Preto - SP',
  55173226 => 'São José do Rio Preto - SP',
  55173227 => 'São José do Rio Preto - SP',
  55173229 => 'São José do Rio Preto - SP',
  55173231 => 'São José do Rio Preto - SP',
  55173232 => 'São José do Rio Preto - SP',
  55173233 => 'São José do Rio Preto - SP',
  55173234 => 'São José do Rio Preto - SP',
  55173235 => 'São José do Rio Preto - SP',
  55173236 => 'São José do Rio Preto - SP',
  55173237 => 'São José do Rio Preto - SP',
  55173238 => 'São José do Rio Preto - SP',
  55173242 => 'Mirassol - SP',
  55173243 => 'Mirassol - SP',
  55173245 => 'José Bonifácio - SP',
  55173248 => 'Mendonça - SP',
  55173249 => 'Potirendaba - SP',
  55173251 => 'São José do Rio Preto - SP',
  55173253 => 'Mirassol - SP',
  55173254 => 'Mirassol - SP',
  55173256 => 'Santa Luzia - SP',
  55173257 => 'Baguaçu - SP',
  55173258 => 'Bady Bassitt - SP',
  55173261 => 'Nova Granada - SP',
  55173262 => 'Nova Granada - SP',
  55173263 => 'Mirassolândia - SP',
  55173264 => 'Bálsamo - SP',
  55173265 => 'José Bonifácio - SP',
  55173266 => 'Cedral - SP',
  55173267 => 'Guapiaçu - SP',
  55173268 => 'Onda Verde - SP',
  55173269 => 'Ipiguá - SP',
  55173271 => 'Neves Paulista - SP',
  55173272 => 'Tanabi - SP',
  55173274 => 'Tanabi - SP',
  55173275 => 'Monte Aprazível - SP',
  55173276 => 'Engenheiro Balduíno - SP',
  55173277 => 'Nipoã - SP',
  55173278 => 'União Paulista - SP',
  55173279 => 'Olímpia - SP',
  55173280 => 'Olímpia - SP',
  55173281 => 'Olímpia - SP',
  55173282 => 'Icém - SP',
  55173283 => 'Jaci - SP',
  55173284 => 'Ribeiro dos Santos - SP',
  55173291 => 'Riolândia - SP',
  55173292 => 'Paulo de Faria - SP',
  55173293 => 'Palestina - SP',
  55173295 => 'Monte Aprazível - SP',
  55173301 => 'São José do Rio Preto - SP',
  55173302 => 'São José do Rio Preto - SP',
  55173311 => 'Catanduva - SP',
  55173312 => 'Barretos - SP',
  55173321 => 'Barretos - SP',
  55173322 => 'Barretos - SP',
  55173323 => 'Barretos - SP',
  55173324 => 'Barretos - SP',
  55173325 => 'Barretos - SP',
  55173326 => 'Barretos - SP',
  55173328 => 'Barretos - SP',
  55173329 => 'Alberto Moreira - SP',
  55173330 => 'Guaíra - SP',
  55173331 => 'Guaíra - SP',
  55173332 => 'Guaíra - SP',
  55173334 => 'São José do Rio Preto - SP',
  55173335 => 'Colômbia - SP',
  55173341 => 'Colina - SP',
  55173342 => 'Bebedouro - SP',
  55173343 => 'Bebedouro - SP',
  55173344 => 'Bebedouro - SP',
  55173345 => 'Bebedouro - SP',
  55173346 => 'Bebedouro - SP',
  55173347 => 'Jaborandi - SP',
  55173349 => 'Turvínia - SP',
  55173353 => 'São José do Rio Preto - SP',
  55173354 => 'São José do Rio Preto - SP',
  55173355 => 'São José do Rio Preto - SP',
  55173359 => 'Colômbia - SP',
  55173361 => 'Monte Azul Paulista - SP',
  55173362 => 'Marcondésia - SP',
  55173386 => 'Pirangi - SP',
  55173392 => 'Viradouro - SP',
  55173395 => 'Terra Roxa - SP',
  55173405 => 'Votuporanga - SP',
  55173421 => 'Votuporanga - SP',
  55173422 => 'Votuporanga - SP',
  55173423 => 'Votuporanga - SP',
  55173426 => 'Votuporanga - SP',
  55173441 => 'General Salgado - SP',
  55173442 => 'Fernandópolis - SP',
  55173445 => 'Américo de Campos - SP',
  55173453 => 'Cardoso - SP',
  55173461 => 'General Salgado - SP',
  55173462 => 'Fernandópolis - SP',
  55173463 => 'Fernandópolis - SP',
  55173465 => 'Fernandópolis - SP',
  55173466 => 'Cardoso - SP',
  55173467 => 'Nhandeara - SP',
  55173472 => 'Nhandeara - SP',
  55173475 => 'Meridiano - SP',
  55173481 => 'São João das Duas Pontes - SP',
  55173482 => 'Auriflama - SP',
  55173483 => 'Nova Luzitânia - SP',
  55173484 => 'Monções - SP',
  55173485 => 'Valentim Gentil - SP',
  55173486 => 'Álvares Florence - SP',
  55173487 => 'Magda - SP',
  55173489 => 'Brasitânia - SP',
  55173512 => 'São José do Rio Preto - SP',
  55173513 => 'São José do Rio Preto - SP',
  55173521 => 'Catanduva - SP',
  55173522 => 'Catanduva - SP',
  55173523 => 'Catanduva - SP',
  55173524 => 'Catanduva - SP',
  55173525 => 'Catanduva - SP',
  55173529 => 'Elisiário - SP',
  55173531 => 'Catanduva - SP',
  55173542 => 'Novo Horizonte - SP',
  55173543 => 'Novo Horizonte - SP',
  55173546 => 'Itajobi - SP',
  55173547 => 'Itajobi - SP',
  55173548 => 'Marapoama - SP',
  55173551 => 'Ibirá - SP',
  55173552 => 'Urupês - SP',
  55173553 => 'São João de Itaguaçu - SP',
  55173556 => 'Irapuã - SP',
  55173557 => 'Sales - SP',
  55173561 => 'Novais - SP',
  55173562 => 'Tabapuã - SP',
  55173563 => 'Cajobi - SP',
  55173564 => 'Catiguá - SP',
  55173566 => 'Embaúba - SP',
  55173567 => 'Paraíso - SP',
  55173571 => 'Santa Adélia - SP',
  55173572 => 'Pindorama - SP',
  55173573 => 'Roberto - SP',
  55173576 => 'Ariranha - SP',
  55173579 => 'Botelho - SP',
  55173587 => 'Palmares Paulista - SP',
  55173621 => 'Jales - SP',
  55173622 => 'Jales - SP',
  55173624 => 'Jales - SP',
  55173631 => 'Santa Fé do Sul - SP',
  55173632 => 'Jales - SP',
  55173633 => 'Santa Albertina - SP',
  55173634 => 'Urânia - SP',
  55173635 => 'Aparecida D\'Oeste - SP',
  55173636 => 'Dolcinópolis - SP',
  55173637 => 'Guzolândia - SP',
  55173638 => 'Mesópolis - SP',
  55173639 => 'Populina - SP',
  55173641 => 'Santa Fé do Sul - SP',
  55173642 => 'Vitória Brasil - SP',
  55173643 => 'Santa Rita D\'Oeste - SP',
  55173648 => 'Paranapuã - SP',
  55173651 => 'Palmeira D\'Oeste - SP',
  55173661 => 'Rubinéia - SP',
  55173662 => 'Santa Salete - SP',
  55173663 => 'Santa Clara D\'Oeste - SP',
  55173664 => 'Aspásia - SP',
  55173667 => 'Turmalina - SP',
  55173681 => 'Nova Canaã Paulista - SP',
  55173691 => 'Três Fronteiras - SP',
  55173692 => 'Santana da Ponte Pensa - SP',
  55173693 => 'São Francisco - SP',
  55173694 => 'Dirce Reis - SP',
  55173695 => 'Marinópolis - SP',
  55173699 => 'Pontalinda - SP',
  55173801 => 'Riolândia - SP',
  55173802 => 'Paulo de Faria - SP',
  55173807 => 'Ubarana - SP',
  55173808 => 'Engenheiro Schimidt - SP',
  55173809 => 'Mendonça - SP',
  55173811 => 'Nova Aliança - SP',
  55173812 => 'Icém - SP',
  55173813 => 'Jaci - SP',
  55173814 => 'Adolfo - SP',
  55173815 => 'Guaraci - SP',
  55173816 => 'Orindiúva - SP',
  55173817 => 'Severínia - SP',
  55173818 => 'Bady Bassitt - SP',
  55173819 => 'Poloni - SP',
  55173826 => 'Uchoa - SP',
  55173827 => 'Potirendaba - SP',
  55173829 => 'Talhado - SP',
  55173831 => 'Nova Castilho - SP',
  55173832 => 'General Salgado - SP',
  55173833 => 'Estrela D\'Oeste - SP',
  55173834 => 'Guarani D\'Oeste - SP',
  55173836 => 'Cosmorama - SP',
  55173837 => 'Sebastianópolis do Sul - SP',
  55173838 => 'Pedranópolis - SP',
  55173839 => 'Parisi - SP',
  55173841 => 'Arabá - SP',
  55173842 => 'Indiaporã - SP',
  55173843 => 'Ouroeste - SP',
  55173844 => 'Pontes Gestal - SP',
  55173845 => 'Meridiano - SP',
  55173846 => 'Mira Estrela - SP',
  55173847 => 'Floreal - SP',
  55173848 => 'Gastão Vidigal - SP',
  55173849 => 'Macedônia - SP',
  55173874 => 'Macaubal - SP',
  55173875 => 'São João de Iracema - SP',
  55173889 => 'Altair - SP',
  55173893 => 'Palestina - SP',
  55174003 => 'São José do Rio Preto - SP',
  55174004 => 'São José do Rio Preto - SP',
  55174009 => 'São José do Rio Preto - SP',
  5518 => 'São Paulo',
  55182101 => 'Presidente Prudente - SP',
  55182102 => 'Araçatuba - SP',
  55182103 => 'Araçatuba - SP',
  55182104 => 'Presidente Prudente - SP',
  55183021 => 'Birigui - SP',
  55183022 => 'Assis - SP',
  55183117 => 'Araçatuba - SP',
  55183211 => 'Birigui - SP',
  55183217 => 'Presidente Prudente - SP',
  55183221 => 'Presidente Prudente - SP',
  55183222 => 'Presidente Prudente - SP',
  55183223 => 'Presidente Prudente - SP',
  55183229 => 'Presidente Prudente - SP',
  55183251 => 'Presidente Epitácio - SP',
  55183255 => 'Rancharia - SP',
  55183261 => 'Ribeirão dos Índios - SP',
  55183262 => 'Presidente Bernardes - SP',
  55183263 => 'Santo Anastácio - SP',
  55183264 => 'Iepê - SP',
  55183265 => 'Rancharia - SP',
  55183266 => 'Alfredo Marcondes - SP',
  55183267 => 'Santo Expedito - SP',
  55183268 => 'Nantes - SP',
  55183269 => 'Pirapozinho - SP',
  55183271 => 'Presidente Venceslau - SP',
  55183272 => 'Presidente Venceslau - SP',
  55183273 => 'Álvares Machado - SP',
  55183274 => 'Teçaindá - SP',
  55183275 => 'Martinópolis - SP',
  55183276 => 'Piquerobi - SP',
  55183277 => 'Sandovalina - SP',
  55183278 => 'Caiuá - SP',
  55183279 => 'Regente Feijó - SP',
  55183281 => 'Presidente Epitácio - SP',
  55183282 => 'Teodoro Sampaio - SP',
  55183283 => 'Euclides da Cunha Paulista - SP',
  55183284 => 'Rosana - SP',
  55183285 => 'Caiabu - SP',
  55183286 => 'Anhumas - SP',
  55183287 => 'Campinal - SP',
  55183288 => 'Rosana - SP',
  55183289 => 'Tarabai - SP',
  55183301 => 'Araçatuba - SP',
  55183302 => 'Assis - SP',
  55183311 => 'Presidente Prudente - SP',
  55183321 => 'Assis - SP',
  55183322 => 'Assis - SP',
  55183323 => 'Assis - SP',
  55183324 => 'Assis - SP',
  55183325 => 'Assis - SP',
  55183329 => 'Tarumã - SP',
  55183334 => 'Presidente Prudente - SP',
  55183341 => 'Cândido Mota - SP',
  55183344 => 'Presidente Prudente - SP',
  55183345 => 'Presidente Prudente - SP',
  55183349 => 'Frutal do Campo - SP',
  55183351 => 'Palmital - SP',
  55183354 => 'Platina - SP',
  55183355 => 'Presidente Prudente - SP',
  55183356 => 'Echaporã - SP',
  55183361 => 'Paraguaçu Paulista - SP',
  55183362 => 'Paraguaçu Paulista - SP',
  55183366 => 'Quatá - SP',
  55183367 => 'Borá - SP',
  55183368 => 'Lutécia - SP',
  55183371 => 'Maracaí - SP',
  55183373 => 'Tarumã - SP',
  55183375 => 'Pedrinhas Paulista - SP',
  55183376 => 'Cruzália - SP',
  55183377 => 'Florínia - SP',
  55183379 => 'São José Laranjeiras - SP',
  55183401 => 'Valparaíso - SP',
  55183402 => 'Assis - SP',
  55183406 => 'Guararapes - SP',
  55183421 => 'Assis - SP',
  55183422 => 'Assis - SP',
  55183441 => 'Araçatuba - SP',
  55183502 => 'Adamantina - SP',
  55183521 => 'Adamantina - SP',
  55183522 => 'Adamantina - SP',
  55183528 => 'Osvaldo Cruz - SP',
  55183529 => 'Osvaldo Cruz - SP',
  55183551 => 'Lucélia - SP',
  55183552 => 'Pracinha - SP',
  55183556 => 'Inúbia Paulista - SP',
  55183557 => 'Salmourão - SP',
  55183558 => 'Sagres - SP',
  55183571 => 'Flórida Paulista - SP',
  55183581 => 'Flórida Paulista - SP',
  55183582 => 'Parapuã - SP',
  55183583 => 'Rinópolis - SP',
  55183586 => 'Mariápolis - SP',
  55183601 => 'Bento de Abreu - SP',
  55183602 => 'Gabriel Monteiro - SP',
  55183603 => 'Luiziânia - SP',
  55183604 => 'Vicentinópolis - SP',
  55183605 => 'Santópolis do Aguapeí - SP',
  55183606 => 'Guararapes - SP',
  55183607 => 'Araçatuba - SP',
  55183608 => 'Araçatuba - SP',
  55183609 => 'Araçatuba - SP',
  55183621 => 'Araçatuba - SP',
  55183622 => 'Araçatuba - SP',
  55183623 => 'Araçatuba - SP',
  55183624 => 'Araçatuba - SP',
  55183625 => 'Araçatuba - SP',
  55183631 => 'Araçatuba - SP',
  55183634 => 'Birigui - SP',
  55183636 => 'Araçatuba - SP',
  55183637 => 'Araçatuba - SP',
  55183638 => 'Birigui - SP',
  55183639 => 'Santo Antônio do Aracanguá - SP',
  55183641 => 'Birigui - SP',
  55183642 => 'Birigui - SP',
  55183643 => 'Birigui - SP',
  55183644 => 'Birigui - SP',
  55183645 => 'Coroados - SP',
  55183646 => 'Brejo Alegre - SP',
  55183647 => 'Glicério - SP',
  55183648 => 'Juritis - SP',
  55183649 => 'Birigui - SP',
  55183651 => 'Avanhandava - SP',
  55183652 => 'Penápolis - SP',
  55183653 => 'Penápolis - SP',
  55183654 => 'Penápolis - SP',
  55183655 => 'Barbosa - SP',
  55183656 => 'Jatobá - SP',
  55183657 => 'Alto Alegre - SP',
  55183658 => 'Clementina - SP',
  55183659 => 'Bilac - SP',
  55183691 => 'Buritama - SP',
  55183692 => 'Braúna - SP',
  55183693 => 'Piacatu - SP',
  55183694 => 'Zacarias - SP',
  55183695 => 'Planalto - SP',
  55183696 => 'Turiúba - SP',
  55183697 => 'Rubiácea - SP',
  55183698 => 'Lavínia - SP',
  55183699 => 'Lourdes - SP',
  55183701 => 'Mirandópolis - SP',
  55183702 => 'Andradina - SP',
  55183703 => 'Bairro Formosa - SP',
  55183704 => 'Pereira Barreto - SP',
  55183705 => 'Guaraçaí - SP',
  55183706 => 'Suzanápolis - SP',
  55183708 => 'Primeira Aliança - SP',
  55183721 => 'Andradina - SP',
  55183722 => 'Andradina - SP',
  55183723 => 'Andradina - SP',
  55183741 => 'Castilho - SP',
  55183742 => 'Ilha Solteira - SP',
  55183743 => 'Ilha Solteira - SP',
  55183744 => 'Nova Independência - SP',
  55183745 => 'Itapura - SP',
  55183746 => 'Pereira Barreto - SP',
  55183748 => 'Ilha Solteira - SP',
  55183786 => 'Sud Mennucci - SP',
  55183788 => 'Murutinga do Sul - SP',
  55183821 => 'Dracena - SP',
  55183822 => 'Dracena - SP',
  55183823 => 'Dracena - SP',
  55183839 => 'Jamaica - SP',
  55183841 => 'Junqueirópolis - SP',
  55183842 => 'Junqueirópolis - SP',
  55183851 => 'Tupi Paulista - SP',
  55183855 => 'Monte Castelo - SP',
  55183856 => 'Nova Guataporanga - SP',
  55183857 => 'São João do Pau D\'Alho - SP',
  55183861 => 'Irapuru - SP',
  55183862 => 'Pacaembu - SP',
  55183866 => 'Flora Rica - SP',
  55183871 => 'Panorama - SP',
  55183872 => 'Ouro Verde - SP',
  55183875 => 'Santa Mercedes - SP',
  55183876 => 'Paulicéia - SP',
  55183901 => 'Presidente Prudente - SP',
  55183902 => 'Presidente Prudente - SP',
  55183903 => 'Presidente Prudente - SP',
  55183905 => 'Presidente Prudente - SP',
  55183906 => 'Presidente Prudente - SP',
  55183907 => 'Presidente Prudente - SP',
  55183908 => 'Presidente Prudente - SP',
  55183909 => 'Presidente Prudente - SP',
  55183911 => 'Eneida - SP',
  55183913 => 'Montalvão - SP',
  55183916 => 'Presidente Prudente - SP',
  55183917 => 'Presidente Prudente - SP',
  55183918 => 'Presidente Prudente - SP',
  55183928 => 'Presidente Prudente - SP',
  55183941 => 'Espigão - SP',
  55183942 => 'Gardênia - SP',
  55183981 => 'Mirante do Paranapanema - SP',
  55183991 => 'Mirante do Paranapanema - SP',
  55183992 => 'Narandiba - SP',
  55183993 => 'Cuiabá Paulista - SP',
  55183994 => 'Emilianópolis - SP',
  55183995 => 'Indiana - SP',
  55183996 => 'Marabá Paulista - SP',
  55183997 => 'Taciba - SP',
  55183998 => 'João Ramalho - SP',
  55183999 => 'Estrela do Norte - SP',
  55184101 => 'Presidente Prudente - SP',
  55184104 => 'Birigui - SP',
  55185821 => 'Dracena - SP',
  55185841 => 'Junqueirópolis - SP',
  55185871 => 'Panorama - SP',
  5519 => 'São Paulo',
  55192101 => 'Campinas - SP',
  55192102 => 'Campinas - SP',
  55192103 => 'Campinas - SP',
  55192104 => 'Campinas - SP',
  55192105 => 'Piracicaba - SP',
  55192106 => 'Piracicaba - SP',
  55192107 => 'Indaiatuba - SP',
  55192108 => 'Americana - SP',
  55192111 => 'Rio Claro - SP',
  55192112 => 'Rio Claro - SP',
  55192113 => 'Limeira - SP',
  55192114 => 'Limeira - SP',
  55192117 => 'Campinas - SP',
  55192118 => 'Hortolândia - SP',
  55192119 => 'Hortolândia - SP',
  55192121 => 'Campinas - SP',
  55192122 => 'Campinas - SP',
  55192127 => 'Campinas - SP',
  55192129 => 'Campinas - SP',
  55192137 => 'Campinas - SP',
  55192138 => 'Campinas - SP',
  55192146 => 'Capivari - SP',
  55192511 => 'Campinas - SP',
  55192513 => 'Campinas - SP',
  55192516 => 'Indaiatuba - SP',
  55192532 => 'Piracicaba - SP',
  55192533 => 'Piracicaba - SP',
  55193000 => 'Vinhedo - SP',
  55193016 => 'Indaiatuba - SP',
  55193017 => 'Indaiatuba - SP',
  55193019 => 'Mogi-Guaçu - SP',
  55193022 => 'Mogi Mirim - SP',
  55193023 => 'Rio Claro - SP',
  55193024 => 'Rio Claro - SP',
  55193026 => 'Santa Bárbara D\'Oeste - SP',
  55193029 => 'Campinas - SP',
  55193030 => 'Vinhedo - SP',
  55193031 => 'Campinas - SP',
  55193032 => 'Campinas - SP',
  55193042 => 'Piracicaba - SP',
  55193053 => 'Leme - SP',
  55193055 => 'Pirassununga - SP',
  55193056 => 'São João da Boa Vista - SP',
  55193112 => 'Campinas - SP',
  55193123 => 'Campinas - SP',
  55193124 => 'Piracicaba - SP',
  55193129 => 'Vinhedo - SP',
  55193181 => 'Floresta Escura - SP',
  55193186 => 'Charqueada - SP',
  55193187 => 'Santa Maria da Serra - SP',
  55193201 => 'Campinas - SP',
  55193202 => 'Campinas - SP',
  55193203 => 'Campinas - SP',
  55193206 => 'Campinas - SP',
  55193207 => 'Campinas - SP',
  55193208 => 'Campinas - SP',
  55193209 => 'Campinas - SP',
  55193211 => 'Campinas - SP',
  55193212 => 'Campinas - SP',
  55193213 => 'Campinas - SP',
  55193216 => 'Campinas - SP',
  55193221 => 'Campinas - SP',
  55193223 => 'Campinas - SP',
  55193224 => 'Campinas - SP',
  55193225 => 'Campinas - SP',
  55193226 => 'Campinas - SP',
  55193227 => 'Campinas - SP',
  55193228 => 'Campinas - SP',
  55193229 => 'Campinas - SP',
  55193231 => 'Campinas - SP',
  55193232 => 'Campinas - SP',
  55193233 => 'Campinas - SP',
  55193234 => 'Campinas - SP',
  55193235 => 'Campinas - SP',
  55193236 => 'Campinas - SP',
  55193237 => 'Campinas - SP',
  55193238 => 'Campinas - SP',
  55193239 => 'Campinas - SP',
  55193241 => 'Campinas - SP',
  55193243 => 'Campinas - SP',
  55193245 => 'Campinas - SP',
  55193246 => 'Campinas - SP',
  55193249 => 'Campinas - SP',
  55193251 => 'Campinas - SP',
  55193252 => 'Campinas - SP',
  55193253 => 'Campinas - SP',
  55193254 => 'Campinas - SP',
  55193255 => 'Campinas - SP',
  55193256 => 'Campinas - SP',
  55193257 => 'Campinas - SP',
  55193258 => 'Campinas - SP',
  55193259 => 'Campinas - SP',
  55193262 => 'Campinas - SP',
  55193263 => 'Campinas - SP',
  55193265 => 'Campinas - SP',
  55193266 => 'Campinas - SP',
  55193267 => 'Campinas - SP',
  55193268 => 'Campinas - SP',
  55193269 => 'Campinas - SP',
  55193271 => 'Campinas - SP',
  55193272 => 'Campinas - SP',
  55193273 => 'Campinas - SP',
  55193274 => 'Campinas - SP',
  55193275 => 'Campinas - SP',
  55193276 => 'Campinas - SP',
  55193277 => 'Campinas - SP',
  55193278 => 'Campinas - SP',
  55193279 => 'Campinas - SP',
  55193281 => 'Campinas - SP',
  55193282 => 'Campinas - SP',
  55193283 => 'Campinas - SP',
  55193284 => 'Campinas - SP',
  55193285 => 'Campinas - SP',
  55193287 => 'Campinas - SP',
  55193288 => 'Campinas - SP',
  55193289 => 'Campinas - SP',
  55193294 => 'Campinas - SP',
  55193295 => 'Campinas - SP',
  55193296 => 'Campinas - SP',
  55193298 => 'Campinas - SP',
  55193301 => 'Piracicaba - SP',
  55193302 => 'Piracicaba - SP',
  55193304 => 'Campinas - SP',
  55193305 => 'Campinas - SP',
  55193306 => 'Sumaré - SP',
  55193307 => 'Campinas - SP',
  55193308 => 'Campinas - SP',
  55193311 => 'Jaguariúna - SP',
  55193312 => 'Indaiatuba - SP',
  55193321 => 'Araras - SP',
  55193324 => 'Campinas - SP',
  55193325 => 'Campinas - SP',
  55193326 => 'Campinas - SP',
  55193328 => 'Indaiatuba - SP',
  55193336 => 'Rio Claro - SP',
  55193341 => 'Campinas - SP',
  55193345 => 'Campinas - SP',
  55193351 => 'Araras - SP',
  55193352 => 'Araras - SP',
  55193353 => 'Araras - SP',
  55193361 => 'Mogi-Guaçu - SP',
  55193362 => 'Mogi-Guaçu - SP',
  55193363 => 'Paulínia - SP',
  55193366 => 'São João da Boa Vista - SP',
  55193371 => 'Piracicaba - SP',
  55193373 => 'Piracicaba - SP',
  55193374 => 'Piracicaba - SP',
  55193375 => 'Piracicaba - SP',
  55193377 => 'Piracicaba - SP',
  55193384 => 'Campinas - SP',
  55193385 => 'Campinas - SP',
  55193386 => 'Campinas - SP',
  55193387 => 'Campinas - SP',
  55193388 => 'Campinas - SP',
  55193394 => 'Indaiatuba - SP',
  55193396 => 'Sumaré - SP',
  55193399 => 'Sumaré - SP',
  55193401 => 'Piracicaba - SP',
  55193402 => 'Piracicaba - SP',
  55193404 => 'Limeira - SP',
  55193405 => 'Americana - SP',
  55193406 => 'Americana - SP',
  55193407 => 'Americana - SP',
  55193408 => 'Americana - SP',
  55193411 => 'Piracicaba - SP',
  55193412 => 'Piracicaba - SP',
  55193413 => 'Piracicaba - SP',
  55193414 => 'Piracicaba - SP',
  55193415 => 'Piracicaba - SP',
  55193417 => 'Piracicaba - SP',
  55193418 => 'Piracicaba - SP',
  55193421 => 'Piracicaba - SP',
  55193422 => 'Piracicaba - SP',
  55193423 => 'Piracicaba - SP',
  55193424 => 'Piracicaba - SP',
  55193425 => 'Piracicaba - SP',
  55193426 => 'Piracicaba - SP',
  55193427 => 'Piracicaba - SP',
  55193428 => 'Piracicaba - SP',
  55193429 => 'Piracicaba - SP',
  55193430 => 'Piracicaba - SP',
  55193431 => 'Tanquinho - SP',
  55193432 => 'Piracicaba - SP',
  55193433 => 'Piracicaba - SP',
  55193434 => 'Piracicaba - SP',
  55193435 => 'Piracicaba - SP',
  55193436 => 'Piracicaba - SP',
  55193437 => 'Piracicaba - SP',
  55193439 => 'Saltinho - SP',
  55193441 => 'Limeira - SP',
  55193442 => 'Limeira - SP',
  55193443 => 'Limeira - SP',
  55193444 => 'Limeira - SP',
  55193445 => 'Limeira - SP',
  55193446 => 'Limeira - SP',
  55193447 => 'Piracicaba - SP',
  55193448 => 'Ibitiruna - SP',
  55193451 => 'Limeira - SP',
  55193452 => 'Limeira - SP',
  55193453 => 'Limeira - SP',
  55193454 => 'Santa Bárbara D\'Oeste - SP',
  55193455 => 'Santa Bárbara D\'Oeste - SP',
  55193456 => 'Iracemápolis - SP',
  55193457 => 'Santa Bárbara D\'Oeste - SP',
  55193458 => 'Santa Bárbara D\'Oeste - SP',
  55193461 => 'Americana - SP',
  55193462 => 'Americana - SP',
  55193463 => 'Santa Bárbara D\'Oeste - SP',
  55193464 => 'Santa Bárbara D\'Oeste - SP',
  55193465 => 'Americana - SP',
  55193466 => 'Nova Odessa - SP',
  55193467 => 'Americana - SP',
  55193468 => 'Americana - SP',
  55193469 => 'Americana - SP',
  55193473 => 'Santa Bárbara D\'Oeste - SP',
  55193476 => 'Nova Odessa - SP',
  55193477 => 'Americana - SP',
  55193478 => 'Americana - SP',
  55193481 => 'São Pedro - SP',
  55193482 => 'Águas de São Pedro - SP',
  55193483 => 'São Pedro - SP',
  55193484 => 'Nova Odessa - SP',
  55193486 => 'Charqueada - SP',
  55193487 => 'Santa Maria da Serra - SP',
  55193488 => 'Mombuca - SP',
  55193491 => 'Capivari - SP',
  55193492 => 'Capivari - SP',
  55193493 => 'Rio das Pedras - SP',
  55193494 => 'São Paulo',
  55193495 => 'Limeira - SP',
  55193496 => 'Rafard - SP',
  55193497 => 'Limeira - SP',
  55193498 => 'Nova Odessa - SP',
  55193499 => 'Santa Bárbara D\'Oeste - SP',
  55193501 => 'Hortolândia - SP',
  55193502 => 'Rio Claro - SP',
  55193503 => 'Hortolândia - SP',
  55193507 => 'Araras - SP',
  55193508 => 'Araras - SP',
  55193512 => 'Campinas - SP',
  55193513 => 'Limeira - SP',
  55193516 => 'Campinas - SP',
  55193519 => 'Campinas - SP',
  55193521 => 'Campinas - SP',
  55193522 => 'Rio Claro - SP',
  55193523 => 'Rio Claro - SP',
  55193524 => 'Rio Claro - SP',
  55193525 => 'Rio Claro - SP',
  55193526 => 'Rio Claro - SP',
  55193527 => 'Rio Claro - SP',
  55193531 => 'Rio Claro - SP',
  55193532 => 'Rio Claro - SP',
  55193533 => 'Rio Claro - SP',
  55193534 => 'Rio Claro - SP',
  55193535 => 'Rio Claro - SP',
  55193536 => 'Rio Claro - SP',
  55193537 => 'Ipeúna - SP',
  55193538 => 'Ajapi - SP',
  55193539 => 'Ajapi - SP',
  55193541 => 'Araras - SP',
  55193542 => 'Araras - SP',
  55193543 => 'Araras - SP',
  55193544 => 'Araras - SP',
  55193545 => 'Santa Gertrudes - SP',
  55193546 => 'Cordeirópolis - SP',
  55193547 => 'Araras - SP',
  55193549 => 'Mogi Mirim - SP',
  55193551 => 'Araras - SP',
  55193552 => 'Mogi Mirim - SP',
  55193554 => 'Leme - SP',
  55193555 => 'Leme - SP',
  55193556 => 'Cordeirópolis - SP',
  55193557 => 'Rio Claro - SP',
  55193561 => 'Pirassununga - SP',
  55193562 => 'Pirassununga - SP',
  55193563 => 'Pirassununga - SP',
  55193565 => 'Pirassununga - SP',
  55193566 => 'Analândia - SP',
  55193567 => 'Santa Cruz da Conceição - SP',
  55193571 => 'Leme - SP',
  55193572 => 'Leme - SP',
  55193573 => 'Leme - SP',
  55193575 => 'Itirapina - SP',
  55193576 => 'Ipeúna - SP',
  55193577 => 'Corumbataí - SP',
  55193578 => 'Campinas - SP',
  55193579 => 'Campinas - SP',
  55193581 => 'Porto Ferreira - SP',
  55193582 => 'Santa Rita do Passa Quatro - SP',
  55193583 => 'Descalvado - SP',
  55193584 => 'Santa Rita do Passa Quatro - SP',
  55193585 => 'Porto Ferreira - SP',
  55193586 => 'Itirapina - SP',
  55193588 => 'Porto Ferreira - SP',
  55193589 => 'Porto Ferreira - SP',
  55193592 => 'Santa Rita do Passa Quatro - SP',
  55193593 => 'Descalvado - SP',
  55193594 => 'Descalvado - SP',
  55193597 => 'Rio Claro - SP',
  55193601 => 'Americana - SP',
  55193602 => 'São João da Boa Vista - SP',
  55193607 => 'Casa Branca - SP',
  55193608 => 'São José do Rio Pardo - SP',
  55193617 => 'Rio Claro - SP',
  55193621 => 'Americana - SP',
  55193622 => 'São João da Boa Vista - SP',
  55193623 => 'São João da Boa Vista - SP',
  55193624 => 'São João da Boa Vista - SP',
  55193625 => 'São João da Boa Vista - SP',
  55193626 => 'Santa Bárbara D\'Oeste - SP',
  55193628 => 'Santa Bárbara D\'Oeste - SP',
  55193629 => 'Santa Bárbara D\'Oeste - SP',
  55193631 => 'São João da Boa Vista - SP',
  55193632 => 'São João da Boa Vista - SP',
  55193633 => 'São João da Boa Vista - SP',
  55193634 => 'São João da Boa Vista - SP',
  55193635 => 'São João da Boa Vista - SP',
  55193636 => 'São João da Boa Vista - SP',
  55193638 => 'São João da Boa Vista - SP',
  55193641 => 'Vargem Grande do Sul - SP',
  55193642 => 'Águas da Prata - SP',
  55193643 => 'Vargem Grande do Sul - SP',
  55193646 => 'São Sebastião da Grama - SP',
  55193647 => 'Itobi - SP',
  55193649 => 'Águas da Prata - SP',
  55193651 => 'Espírito Santo do Pinhal - SP',
  55193652 => 'Aguaí - SP',
  55193653 => 'Aguaí - SP',
  55193654 => 'Santo Antônio do Jardim - SP',
  55193656 => 'Mococa - SP',
  55193657 => 'Tapiratiba - SP',
  55193661 => 'Espírito Santo do Pinhal - SP',
  55193662 => 'Caconde - SP',
  55193663 => 'Divinolândia - SP',
  55193665 => 'Mococa - SP',
  55193667 => 'Mococa - SP',
  55193669 => 'Divinolândia - SP',
  55193671 => 'Casa Branca - SP',
  55193672 => 'Santa Cruz das Palmeiras - SP',
  55193673 => 'Tambaú - SP',
  55193674 => 'Casa Branca - SP',
  55193675 => 'Tambaú - SP',
  55193678 => 'Canoas - SP',
  55193679 => 'São Paulo',
  55193681 => 'São José do Rio Pardo - SP',
  55193682 => 'São José do Rio Pardo - SP',
  55193683 => 'Santa Cruz das Palmeiras - SP',
  55193684 => 'São José do Rio Pardo - SP',
  55193695 => 'Mococa - SP',
  55193701 => 'Limeira - SP',
  55193702 => 'Limeira - SP',
  55193704 => 'Limeira - SP',
  55193705 => 'Campinas - SP',
  55193706 => 'Campinas - SP',
  55193707 => 'Campinas - SP',
  55193708 => 'Campinas - SP',
  55193709 => 'Campinas - SP',
  55193713 => 'Limeira - SP',
  55193716 => 'Campinas - SP',
  55193717 => 'Limeira - SP',
  55193721 => 'Campinas - SP',
  55193722 => 'Campinas - SP',
  55193723 => 'Campinas - SP',
  55193725 => 'Campinas - SP',
  55193726 => 'Campinas - SP',
  55193728 => 'Campinas - SP',
  55193731 => 'Campinas - SP',
  55193732 => 'Campinas - SP',
  55193733 => 'Campinas - SP',
  55193734 => 'Campinas - SP',
  55193735 => 'Campinas - SP',
  55193736 => 'Campinas - SP',
  55193737 => 'Campinas - SP',
  55193738 => 'Campinas - SP',
  55193739 => 'Campinas - SP',
  55193743 => 'Campinas - SP',
  55193744 => 'Campinas - SP',
  55193746 => 'Campinas - SP',
  55193749 => 'Campinas - SP',
  55193751 => 'Campinas - SP',
  55193753 => 'Campinas - SP',
  55193754 => 'Campinas - SP',
  55193755 => 'Campinas - SP',
  55193756 => 'Campinas - SP',
  55193757 => 'Campinas - SP',
  55193758 => 'Campinas - SP',
  55193761 => 'Campinas - SP',
  55193765 => 'Campinas - SP',
  55193768 => 'Campinas - SP',
  55193769 => 'Campinas - SP',
  55193772 => 'Campinas - SP',
  55193773 => 'Campinas - SP',
  55193775 => 'Campinas - SP',
  55193778 => 'Campinas - SP',
  55193779 => 'Campinas - SP',
  55193781 => 'Campinas - SP',
  55193782 => 'Campinas - SP',
  55193783 => 'Campinas - SP',
  55193785 => 'Campinas - SP',
  55193787 => 'Campinas - SP',
  55193788 => 'Campinas - SP',
  55193789 => 'Campinas - SP',
  55193792 => 'Limeira - SP',
  55193794 => 'Campinas - SP',
  55193795 => 'Campinas - SP',
  55193796 => 'Campinas - SP',
  55193797 => 'Campinas - SP',
  55193798 => 'Campinas - SP',
  55193801 => 'Indaiatuba - SP',
  55193802 => 'Holambra - SP',
  55193803 => 'Sumaré - SP',
  55193804 => 'Mogi Mirim - SP',
  55193805 => 'Mogi Mirim - SP',
  55193806 => 'Mogi Mirim - SP',
  55193807 => 'Amparo - SP',
  55193808 => 'Amparo - SP',
  55193809 => 'Hortolândia - SP',
  55193811 => 'Mogi-Guaçu - SP',
  55193812 => 'Cosmópolis - SP',
  55193813 => 'Itapira - SP',
  55193814 => 'Mogi Mirim - SP',
  55193816 => 'Indaiatuba - SP',
  55193817 => 'Amparo - SP',
  55193818 => 'Mogi-Guaçu - SP',
  55193819 => 'Hortolândia - SP',
  55193821 => 'Elias Fausto - SP',
  55193822 => 'Sumaré - SP',
  55193824 => 'Águas de Lindóia - SP',
  55193825 => 'Indaiatuba - SP',
  55193826 => 'Vinhedo - SP',
  55193827 => 'Artur Nogueira - SP',
  55193828 => 'Sumaré - SP',
  55193829 => 'Valinhos - SP',
  55193831 => 'Mogi-Guaçu - SP',
  55193833 => 'Paulínia - SP',
  55193834 => 'Indaiatuba - SP',
  55193835 => 'Indaiatuba - SP',
  55193836 => 'Vinhedo - SP',
  55193837 => 'Jaguariúna - SP',
  55193839 => 'Amparo - SP',
  55193841 => 'Mogi-Guaçu - SP',
  55193842 => 'Serra Negra - SP',
  55193843 => 'Itapira - SP',
  55193844 => 'Paulínia - SP',
  55193845 => 'Hortolândia - SP',
  55193846 => 'Vinhedo - SP',
  55193847 => 'Jaguariúna - SP',
  55193848 => 'Louveira - SP',
  55193849 => 'Valinhos - SP',
  55193851 => 'Mogi-Guaçu - SP',
  55193852 => 'Pedreira - SP',
  55193853 => 'Pedreira - SP',
  55193855 => 'Socorro - SP',
  55193856 => 'Vinhedo - SP',
  55193857 => 'Engenheiro Coelho - SP',
  55193858 => 'Engenheiro Coelho - SP',
  55193859 => 'Valinhos - SP',
  55193861 => 'Mogi-Guaçu - SP',
  55193862 => 'Mogi Mirim - SP',
  55193863 => 'Itapira - SP',
  55193865 => 'Hortolândia - SP',
  55193866 => 'Conchal - SP',
  55193867 => 'Jaguariúna - SP',
  55193868 => 'Estiva Gerbi - SP',
  55193869 => 'Valinhos - SP',
  55193871 => 'Valinhos - SP',
  55193872 => 'Cosmópolis - SP',
  55193873 => 'Sumaré - SP',
  55193874 => 'Paulínia - SP',
  55193875 => 'Indaiatuba - SP',
  55193876 => 'Vinhedo - SP',
  55193877 => 'Artur Nogueira - SP',
  55193878 => 'Louveira - SP',
  55193879 => 'Monte Mor - SP',
  55193881 => 'Valinhos - SP',
  55193882 => 'Cosmópolis - SP',
  55193883 => 'Sumaré - SP',
  55193884 => 'Paulínia - SP',
  55193885 => 'Indaiatuba - SP',
  55193886 => 'Vinhedo - SP',
  55193887 => 'Hortolândia - SP',
  55193888 => 'Paulínia - SP',
  55193889 => 'Monte Mor - SP',
  55193891 => 'Mogi-Guaçu - SP',
  55193892 => 'Serra Negra - SP',
  55193893 => 'Pedreira - SP',
  55193894 => 'Indaiatuba - SP',
  55193895 => 'Socorro - SP',
  55193896 => 'Santo Antônio de Posse - SP',
  55193897 => 'Hortolândia - SP',
  55193898 => 'Lindóia - SP',
  55193899 => 'Monte Alegre do Sul - SP',
  55193902 => 'Holambra - SP',
  55193903 => 'Sumaré - SP',
  55193904 => 'Mogi Mirim - SP',
  55193907 => 'Amparo - SP',
  55193909 => 'Hortolândia - SP',
  55193911 => 'Mogi-Guaçu - SP',
  55193913 => 'Itapira - SP',
  55193917 => 'Ribeirão Preto - SP',
  55193924 => 'Águas de Lindóia - SP',
  55193927 => 'Piracicaba - SP',
  55193929 => 'Valinhos - SP',
  55193933 => 'Paulínia - SP',
  55193934 => 'Indaiatuba - SP',
  55193935 => 'Indaiatuba - SP',
  55193936 => 'Indaiatuba - SP',
  55193937 => 'Jaguariúna - SP',
  55193938 => 'Indaiatuba - SP',
  55193948 => 'Louveira - SP',
  55193955 => 'Socorro - SP',
  55193965 => 'Hortolândia - SP',
  55193966 => 'Conchal - SP',
  55193977 => 'Artur Nogueira - SP',
  55193979 => 'Monte Mor - SP',
  55193984 => 'Paulínia - SP',
  55194002 => 'Campinas - SP',
  55194003 => 'Campinas - SP',
  55194007 => 'Campinas - SP',
  55194009 => 'Campinas - SP',
  55194113 => 'Sumaré - SP',
  55194126 => 'Itapira - SP',
  55195657 => 'São Paulo - SP',
  5521 => 'Rio de Janeiro',
  55212005 => 'Rio de Janeiro - RJ',
  55212008 => 'Teresópolis - RJ',
  55212016 => 'Rio de Janeiro - RJ',
  55212025 => 'Rio de Janeiro - RJ',
  55212088 => 'São Gonçalo - RJ',
  55212103 => 'Rio de Janeiro - RJ',
  55212108 => 'Rio de Janeiro - RJ',
  55212124 => 'Duque de Caxias - RJ',
  55212132 => 'Rio de Janeiro - RJ',
  55212133 => 'Rio de Janeiro - RJ',
  55212136 => 'Rio de Janeiro - RJ',
  55212152 => 'Teresópolis - RJ',
  55212153 => 'Rio de Janeiro - RJ',
  55212173 => 'Rio de Janeiro - RJ',
  55212186 => 'Nilópolis - RJ',
  55212201 => 'Rio de Janeiro - RJ',
  55212203 => 'Rio de Janeiro - RJ',
  55212204 => 'Rio de Janeiro - RJ',
  55212205 => 'Rio de Janeiro - RJ',
  55212206 => 'Rio de Janeiro - RJ',
  55212208 => 'Rio de Janeiro - RJ',
  55212209 => 'Rio de Janeiro - RJ',
  55212210 => 'Rio de Janeiro - RJ',
  55212211 => 'Rio de Janeiro - RJ',
  55212212 => 'Rio de Janeiro - RJ',
  55212213 => 'Rio de Janeiro - RJ',
  55212214 => 'Rio de Janeiro - RJ',
  55212215 => 'Rio de Janeiro - RJ',
  55212216 => 'Rio de Janeiro - RJ',
  55212217 => 'Rio de Janeiro - RJ',
  55212219 => 'Rio de Janeiro - RJ',
  55212220 => 'Rio de Janeiro - RJ',
  55212221 => 'Rio de Janeiro - RJ',
  55212222 => 'Rio de Janeiro - RJ',
  55212223 => 'Rio de Janeiro - RJ',
  55212224 => 'Rio de Janeiro - RJ',
  55212225 => 'Rio de Janeiro - RJ',
  55212226 => 'Rio de Janeiro - RJ',
  55212227 => 'Rio de Janeiro - RJ',
  55212228 => 'Rio de Janeiro - RJ',
  55212230 => 'Rio de Janeiro - RJ',
  55212231 => 'Rio de Janeiro - RJ',
  55212232 => 'Rio de Janeiro - RJ',
  55212233 => 'Rio de Janeiro - RJ',
  55212234 => 'Rio de Janeiro - RJ',
  55212235 => 'Rio de Janeiro - RJ',
  55212236 => 'Rio de Janeiro - RJ',
  55212237 => 'Rio de Janeiro - RJ',
  55212238 => 'Rio de Janeiro - RJ',
  55212239 => 'Rio de Janeiro - RJ',
  55212240 => 'Rio de Janeiro - RJ',
  55212241 => 'Rio de Janeiro - RJ',
  55212242 => 'Rio de Janeiro - RJ',
  55212243 => 'Rio de Janeiro - RJ',
  55212244 => 'Rio de Janeiro - RJ',
  55212246 => 'Rio de Janeiro - RJ',
  55212247 => 'Rio de Janeiro - RJ',
  55212249 => 'Rio de Janeiro - RJ',
  55212251 => 'São Gonçalo - RJ',
  55212252 => 'Rio de Janeiro - RJ',
  55212253 => 'Rio de Janeiro - RJ',
  55212254 => 'Rio de Janeiro - RJ',
  55212255 => 'Rio de Janeiro - RJ',
  55212256 => 'Rio de Janeiro - RJ',
  55212257 => 'Rio de Janeiro - RJ',
  55212258 => 'Rio de Janeiro - RJ',
  55212259 => 'Rio de Janeiro - RJ',
  55212260 => 'Rio de Janeiro - RJ',
  55212261 => 'Rio de Janeiro - RJ',
  55212262 => 'Rio de Janeiro - RJ',
  55212263 => 'Rio de Janeiro - RJ',
  55212264 => 'Rio de Janeiro - RJ',
  55212265 => 'Rio de Janeiro - RJ',
  55212266 => 'Rio de Janeiro - RJ',
  55212267 => 'Rio de Janeiro - RJ',
  55212268 => 'Rio de Janeiro - RJ',
  55212270 => 'Rio de Janeiro - RJ',
  55212272 => 'Rio de Janeiro - RJ',
  55212273 => 'Rio de Janeiro - RJ',
  55212274 => 'Rio de Janeiro - RJ',
  55212275 => 'Rio de Janeiro - RJ',
  55212276 => 'Rio de Janeiro - RJ',
  55212277 => 'Rio de Janeiro - RJ',
  55212278 => 'Rio de Janeiro - RJ',
  55212280 => 'Rio de Janeiro - RJ',
  55212282 => 'Rio de Janeiro - RJ',
  55212283 => 'Rio de Janeiro - RJ',
  55212284 => 'Rio de Janeiro - RJ',
  55212285 => 'Rio de Janeiro - RJ',
  55212286 => 'Rio de Janeiro - RJ',
  55212287 => 'Rio de Janeiro - RJ',
  55212288 => 'Rio de Janeiro - RJ',
  55212289 => 'Rio de Janeiro - RJ',
  55212290 => 'Rio de Janeiro - RJ',
  55212291 => 'Rio de Janeiro - RJ',
  55212292 => 'Rio de Janeiro - RJ',
  55212293 => 'Rio de Janeiro - RJ',
  55212294 => 'Rio de Janeiro - RJ',
  55212295 => 'Rio de Janeiro - RJ',
  55212296 => 'Rio de Janeiro - RJ',
  55212298 => 'Rio de Janeiro - RJ',
  55212299 => 'Rio de Janeiro - RJ',
  55212301 => 'Rio de Janeiro - RJ',
  55212303 => 'Rio de Janeiro - RJ',
  55212380 => 'Rio de Janeiro - RJ',
  55212391 => 'Rio de Janeiro - RJ',
  55212394 => 'Rio de Janeiro - RJ',
  55212397 => 'Duque de Caxias - RJ',
  55212401 => 'Rio de Janeiro - RJ',
  55212402 => 'Rio de Janeiro - RJ',
  55212403 => 'Rio de Janeiro - RJ',
  55212404 => 'Rio de Janeiro - RJ',
  55212405 => 'Rio de Janeiro - RJ',
  55212406 => 'Rio de Janeiro - RJ',
  55212407 => 'Rio de Janeiro - RJ',
  55212408 => 'Rio de Janeiro - RJ',
  55212409 => 'Rio de Janeiro - RJ',
  55212410 => 'Rio de Janeiro - RJ',
  55212411 => 'Rio de Janeiro - RJ',
  55212412 => 'Rio de Janeiro - RJ',
  55212413 => 'Rio de Janeiro - RJ',
  55212414 => 'Rio de Janeiro - RJ',
  55212415 => 'Rio de Janeiro - RJ',
  55212416 => 'Rio de Janeiro - RJ',
  55212417 => 'Rio de Janeiro - RJ',
  55212418 => 'Rio de Janeiro - RJ',
  55212419 => 'Rio de Janeiro - RJ',
  55212421 => 'Rio de Janeiro - RJ',
  55212422 => 'Rio de Janeiro - RJ',
  55212423 => 'Rio de Janeiro - RJ',
  55212424 => 'Rio de Janeiro - RJ',
  55212425 => 'Rio de Janeiro - RJ',
  55212426 => 'Rio de Janeiro - RJ',
  55212427 => 'Rio de Janeiro - RJ',
  55212428 => 'Rio de Janeiro - RJ',
  55212429 => 'Rio de Janeiro - RJ',
  55212430 => 'Rio de Janeiro - RJ',
  55212431 => 'Rio de Janeiro - RJ',
  55212432 => 'Rio de Janeiro - RJ',
  55212433 => 'Rio de Janeiro - RJ',
  55212434 => 'Rio de Janeiro - RJ',
  55212435 => 'Rio de Janeiro - RJ',
  55212436 => 'Rio de Janeiro - RJ',
  55212437 => 'Rio de Janeiro - RJ',
  55212438 => 'Rio de Janeiro - RJ',
  55212439 => 'Rio de Janeiro - RJ',
  55212440 => 'Rio de Janeiro - RJ',
  55212441 => 'Rio de Janeiro - RJ',
  55212442 => 'Rio de Janeiro - RJ',
  55212444 => 'Rio de Janeiro - RJ',
  55212445 => 'Rio de Janeiro - RJ',
  55212446 => 'Rio de Janeiro - RJ',
  55212447 => 'Rio de Janeiro - RJ',
  55212448 => 'Rio de Janeiro - RJ',
  55212450 => 'Rio de Janeiro - RJ',
  55212452 => 'Rio de Janeiro - RJ',
  55212453 => 'Rio de Janeiro - RJ',
  55212454 => 'Rio de Janeiro - RJ',
  55212455 => 'Rio de Janeiro - RJ',
  55212456 => 'Rio de Janeiro - RJ',
  55212457 => 'Rio de Janeiro - RJ',
  55212458 => 'Rio de Janeiro - RJ',
  55212460 => 'Rio de Janeiro - RJ',
  55212462 => 'Rio de Janeiro - RJ',
  55212463 => 'Rio de Janeiro - RJ',
  55212464 => 'Rio de Janeiro - RJ',
  55212465 => 'Rio de Janeiro - RJ',
  55212466 => 'Rio de Janeiro - RJ',
  55212467 => 'Rio de Janeiro - RJ',
  55212470 => 'Rio de Janeiro - RJ',
  55212471 => 'Rio de Janeiro - RJ',
  55212473 => 'Rio de Janeiro - RJ',
  55212474 => 'Rio de Janeiro - RJ',
  55212475 => 'Rio de Janeiro - RJ',
  55212479 => 'São Gonçalo - RJ',
  55212480 => 'Rio de Janeiro - RJ',
  55212481 => 'Rio de Janeiro - RJ',
  55212482 => 'Rio de Janeiro - RJ',
  55212483 => 'Rio de Janeiro - RJ',
  55212484 => 'Rio de Janeiro - RJ',
  55212486 => 'Rio de Janeiro - RJ',
  55212487 => 'Rio de Janeiro - RJ',
  55212488 => 'Rio de Janeiro - RJ',
  55212489 => 'Rio de Janeiro - RJ',
  55212490 => 'Rio de Janeiro - RJ',
  55212491 => 'Rio de Janeiro - RJ',
  55212492 => 'Rio de Janeiro - RJ',
  55212493 => 'Rio de Janeiro - RJ',
  55212494 => 'Rio de Janeiro - RJ',
  55212495 => 'Rio de Janeiro - RJ',
  55212496 => 'Rio de Janeiro - RJ',
  55212497 => 'Rio de Janeiro - RJ',
  55212498 => 'Rio de Janeiro - RJ',
  55212499 => 'Rio de Janeiro - RJ',
  55212501 => 'Rio de Janeiro - RJ',
  55212502 => 'Rio de Janeiro - RJ',
  55212503 => 'Rio de Janeiro - RJ',
  55212504 => 'Rio de Janeiro - RJ',
  55212505 => 'Rio de Janeiro - RJ',
  55212506 => 'Rio de Janeiro - RJ',
  55212507 => 'Rio de Janeiro - RJ',
  55212508 => 'Rio de Janeiro - RJ',
  55212509 => 'Rio de Janeiro - RJ',
  55212510 => 'Rio de Janeiro - RJ',
  55212511 => 'Rio de Janeiro - RJ',
  55212512 => 'Rio de Janeiro - RJ',
  55212513 => 'Rio de Janeiro - RJ',
  55212514 => 'Rio de Janeiro - RJ',
  55212515 => 'Rio de Janeiro - RJ',
  55212516 => 'Rio de Janeiro - RJ',
  55212517 => 'Rio de Janeiro - RJ',
  55212518 => 'Rio de Janeiro - RJ',
  55212519 => 'Rio de Janeiro - RJ',
  55212520 => 'Rio de Janeiro - RJ',
  55212521 => 'Rio de Janeiro - RJ',
  55212522 => 'Rio de Janeiro - RJ',
  55212523 => 'Rio de Janeiro - RJ',
  55212524 => 'Rio de Janeiro - RJ',
  55212525 => 'Rio de Janeiro - RJ',
  55212526 => 'Rio de Janeiro - RJ',
  55212527 => 'Rio de Janeiro - RJ',
  55212528 => 'Rio de Janeiro - RJ',
  55212529 => 'Rio de Janeiro - RJ',
  55212530 => 'Rio de Janeiro - RJ',
  55212531 => 'Rio de Janeiro - RJ',
  55212532 => 'Rio de Janeiro - RJ',
  55212533 => 'Rio de Janeiro - RJ',
  55212534 => 'Rio de Janeiro - RJ',
  55212535 => 'Rio de Janeiro - RJ',
  55212537 => 'Rio de Janeiro - RJ',
  55212538 => 'Rio de Janeiro - RJ',
  55212539 => 'Rio de Janeiro - RJ',
  55212540 => 'Rio de Janeiro - RJ',
  55212541 => 'Rio de Janeiro - RJ',
  55212542 => 'Rio de Janeiro - RJ',
  55212543 => 'Rio de Janeiro - RJ',
  55212544 => 'Rio de Janeiro - RJ',
  55212545 => 'Rio de Janeiro - RJ',
  55212546 => 'Rio de Janeiro - RJ',
  55212547 => 'Rio de Janeiro - RJ',
  55212548 => 'Rio de Janeiro - RJ',
  55212549 => 'Rio de Janeiro - RJ',
  55212550 => 'Rio de Janeiro - RJ',
  55212551 => 'Rio de Janeiro - RJ',
  55212552 => 'Rio de Janeiro - RJ',
  55212553 => 'Rio de Janeiro - RJ',
  55212554 => 'Rio de Janeiro - RJ',
  55212555 => 'Rio de Janeiro - RJ',
  55212556 => 'Rio de Janeiro - RJ',
  55212557 => 'Rio de Janeiro - RJ',
  55212558 => 'Rio de Janeiro - RJ',
  55212560 => 'Rio de Janeiro - RJ',
  55212561 => 'Rio de Janeiro - RJ',
  55212562 => 'Rio de Janeiro - RJ',
  55212563 => 'Rio de Janeiro - RJ',
  55212564 => 'Rio de Janeiro - RJ',
  55212565 => 'Rio de Janeiro - RJ',
  55212566 => 'Rio de Janeiro - RJ',
  55212567 => 'Rio de Janeiro - RJ',
  55212568 => 'Rio de Janeiro - RJ',
  55212569 => 'Rio de Janeiro - RJ',
  55212570 => 'Rio de Janeiro - RJ',
  55212571 => 'Rio de Janeiro - RJ',
  55212572 => 'Rio de Janeiro - RJ',
  55212573 => 'Rio de Janeiro - RJ',
  55212574 => 'Rio de Janeiro - RJ',
  55212575 => 'Rio de Janeiro - RJ',
  55212576 => 'Rio de Janeiro - RJ',
  55212577 => 'Rio de Janeiro - RJ',
  55212578 => 'Rio de Janeiro - RJ',
  55212579 => 'Rio de Janeiro - RJ',
  55212580 => 'Rio de Janeiro - RJ',
  55212584 => 'Rio de Janeiro - RJ',
  55212585 => 'Rio de Janeiro - RJ',
  55212586 => 'Rio de Janeiro - RJ',
  55212587 => 'Rio de Janeiro - RJ',
  55212588 => 'Rio de Janeiro - RJ',
  55212589 => 'Rio de Janeiro - RJ',
  55212590 => 'Rio de Janeiro - RJ',
  55212591 => 'Rio de Janeiro - RJ',
  55212592 => 'Rio de Janeiro - RJ',
  55212593 => 'Rio de Janeiro - RJ',
  55212594 => 'Rio de Janeiro - RJ',
  55212595 => 'Rio de Janeiro - RJ',
  55212601 => 'São Gonçalo - RJ',
  55212602 => 'São Gonçalo - RJ',
  55212603 => 'São Gonçalo - RJ',
  55212604 => 'São Gonçalo - RJ',
  55212605 => 'São Gonçalo - RJ',
  55212606 => 'São Gonçalo - RJ',
  55212608 => 'Niterói - RJ',
  55212609 => 'Niterói - RJ',
  55212610 => 'Niterói - RJ',
  55212611 => 'Niterói - RJ',
  55212612 => 'Niterói - RJ',
  55212613 => 'Niterói - RJ',
  55212616 => 'Niterói - RJ',
  55212618 => 'Niterói - RJ',
  55212619 => 'Niterói - RJ',
  55212620 => 'Niterói - RJ',
  55212621 => 'Niterói - RJ',
  55212622 => 'Niterói - RJ',
  55212625 => 'Niterói - RJ',
  55212626 => 'Niterói - RJ',
  55212627 => 'Niterói - RJ',
  55212629 => 'Niterói - RJ',
  55212630 => 'Magé - RJ',
  55212631 => 'Magé - RJ',
  55212632 => 'Guapimirim - RJ',
  55212633 => 'Magé - RJ',
  55212634 => 'Maricá - RJ',
  55212637 => 'Maricá - RJ',
  55212638 => 'Maricá - RJ',
  55212641 => 'Teresópolis - RJ',
  55212642 => 'Teresópolis - RJ',
  55212643 => 'Teresópolis - RJ',
  55212645 => 'Itaboraí - RJ',
  55212646 => 'São Gonçalo - RJ',
  55212647 => 'Magé - RJ',
  55212648 => 'Maricá - RJ',
  55212649 => 'Cachoeiras de Macacu - RJ',
  55212651 => 'São João de Meriti - RJ',
  55212653 => 'Duque de Caxias - RJ',
  55212655 => 'São João de Meriti - RJ',
  55212656 => 'São João de Meriti - RJ',
  55212659 => 'Magé - RJ',
  55212664 => 'Japeri - RJ',
  55212665 => 'Queimados - RJ',
  55212666 => 'Nova Iguaçu - RJ',
  55212667 => 'Nova Iguaçu - RJ',
  55212668 => 'Nova Iguaçu - RJ',
  55212669 => 'Nova Iguaçu - RJ',
  55212670 => 'Japeri - RJ',
  55212671 => 'Duque de Caxias - RJ',
  55212673 => 'Duque de Caxias - RJ',
  55212675 => 'Duque de Caxias - RJ',
  55212680 => 'Mangaratiba - RJ',
  55212681 => 'Seropédica - RJ',
  55212682 => 'Seropédica - RJ',
  55212683 => 'Paracambi - RJ',
  55212685 => 'Mangaratiba - RJ',
  55212687 => 'Itaguaí - RJ',
  55212688 => 'Itaguaí - RJ',
  55212689 => 'Porto Belo - RJ',
  55212691 => 'Nilópolis - RJ',
  55212692 => 'Nilópolis - RJ',
  55212693 => 'Nilópolis - RJ',
  55212697 => 'Mesquita - RJ',
  55212700 => 'Itaguaí - RJ',
  55212703 => 'Niterói - RJ',
  55212704 => 'Niterói - RJ',
  55212705 => 'Niterói - RJ',
  55212707 => 'Niterói - RJ',
  55212709 => 'Niterói - RJ',
  55212710 => 'Niterói - RJ',
  55212711 => 'Niterói - RJ',
  55212712 => 'São Gonçalo - RJ',
  55212713 => 'São Gonçalo - RJ',
  55212714 => 'Niterói - RJ',
  55212715 => 'Niterói - RJ',
  55212717 => 'Niterói - RJ',
  55212719 => 'Niterói - RJ',
  55212721 => 'Niterói - RJ',
  55212722 => 'Niterói - RJ',
  55212728 => 'São Gonçalo - RJ',
  55212730 => 'Rio de Janeiro - RJ',
  55212734 => 'Rio Bonito - RJ',
  55212741 => 'Teresópolis - RJ',
  55212742 => 'Teresópolis - RJ',
  55212743 => 'Teresópolis - RJ',
  55212745 => 'Rio de Janeiro',
  55212747 => 'Tanguá - RJ',
  55212751 => 'São João de Meriti - RJ',
  55212752 => 'São João de Meriti - RJ',
  55212753 => 'São João de Meriti - RJ',
  55212755 => 'São João de Meriti - RJ',
  55212756 => 'São João de Meriti - RJ',
  55212760 => 'Rio das Ostras - RJ',
  55212763 => 'Nova Iguaçu - RJ',
  55212765 => 'Nova Iguaçu - RJ',
  55212767 => 'Nova Iguaçu - RJ',
  55212768 => 'Nova Iguaçu - RJ',
  55212769 => 'Nova Iguaçu - RJ',
  55212771 => 'Duque de Caxias - RJ',
  55212772 => 'Duque de Caxias - RJ',
  55212774 => 'São João de Meriti - RJ',
  55212780 => 'Mangaratiba - RJ',
  55212787 => 'Duque de Caxias - RJ',
  55212789 => 'Mangaratiba - RJ',
  55212791 => 'Nilópolis - RJ',
  55212792 => 'Nilópolis - RJ',
  55212796 => 'Mesquita - RJ',
  55212868 => 'Rio de Janeiro - RJ',
  55212882 => 'Nova Iguaçu - RJ',
  55212886 => 'Nova Iguaçu - RJ',
  55213002 => 'Rio de Janeiro - RJ',
  55213010 => 'Rio de Janeiro - RJ',
  55213012 => 'Rio de Janeiro - RJ',
  55213013 => 'Rio de Janeiro - RJ',
  55213014 => 'Rio de Janeiro - RJ',
  55213015 => 'Rio de Janeiro - RJ',
  55213016 => 'Rio de Janeiro - RJ',
  55213017 => 'Rio de Janeiro - RJ',
  55213018 => 'Rio de Janeiro - RJ',
  55213019 => 'Rio de Janeiro - RJ',
  55213020 => 'Rio de Janeiro - RJ',
  55213030 => 'Rio de Janeiro - RJ',
  55213032 => 'Rio de Janeiro - RJ',
  55213035 => 'Rio de Janeiro - RJ',
  55213037 => 'Nova Iguaçu - RJ',
  55213039 => 'Nilópolis - RJ',
  55213077 => 'Rio de Janeiro - RJ',
  55213078 => 'Rio de Janeiro - RJ',
  55213084 => 'Rio de Janeiro - RJ',
  55213089 => 'Rio de Janeiro - RJ',
  55213097 => 'Teresópolis - RJ',
  55213099 => 'Teresópolis - RJ',
  55213103 => 'Nova Iguaçu - RJ',
  55213104 => 'Rio de Janeiro - RJ',
  55213105 => 'Rio de Janeiro - RJ',
  55213106 => 'Rio de Janeiro - RJ',
  55213107 => 'Rio de Janeiro - RJ',
  55213108 => 'Rio de Janeiro - RJ',
  55213109 => 'Rio de Janeiro - RJ',
  55213113 => 'Rio de Janeiro - RJ',
  55213114 => 'Rio de Janeiro - RJ',
  55213116 => 'Rio de Janeiro - RJ',
  55213118 => 'Rio de Janeiro - RJ',
  55213119 => 'São Gonçalo - RJ',
  55213131 => 'Rio de Janeiro - RJ',
  55213133 => 'Rio de Janeiro - RJ',
  55213137 => 'Rio de Janeiro - RJ',
  55213138 => 'Rio de Janeiro - RJ',
  55213139 => 'Rio de Janeiro - RJ',
  55213142 => 'Itaboraí - RJ',
  55213150 => 'Rio de Janeiro - RJ',
  55213151 => 'Rio de Janeiro - RJ',
  55213152 => 'Rio de Janeiro - RJ',
  55213153 => 'Rio de Janeiro - RJ',
  55213154 => 'Rio de Janeiro - RJ',
  55213155 => 'Rio de Janeiro - RJ',
  55213156 => 'Rio de Janeiro - RJ',
  55213157 => 'Rio de Janeiro - RJ',
  55213158 => 'Rio de Janeiro - RJ',
  55213159 => 'Rio de Janeiro - RJ',
  55213162 => 'Nilópolis - RJ',
  55213167 => 'Duque de Caxias - RJ',
  55213171 => 'Rio de Janeiro - RJ',
  55213175 => 'Nilópolis - RJ',
  55213194 => 'Rio de Janeiro - RJ',
  55213198 => 'Rio de Janeiro - RJ',
  55213201 => 'Rio de Janeiro - RJ',
  55213202 => 'Rio de Janeiro - RJ',
  55213207 => 'Rio de Janeiro - RJ',
  55213208 => 'Rio de Janeiro - RJ',
  55213212 => 'Rio de Janeiro - RJ',
  55213213 => 'Rio de Janeiro - RJ',
  55213219 => 'Rio de Janeiro - RJ',
  55213221 => 'Rio de Janeiro - RJ',
  55213222 => 'Rio de Janeiro - RJ',
  55213224 => 'Rio de Janeiro - RJ',
  55213227 => 'Rio de Janeiro - RJ',
  55213229 => 'Rio de Janeiro - RJ',
  55213231 => 'Rio de Janeiro - RJ',
  55213232 => 'Rio de Janeiro - RJ',
  55213233 => 'Rio de Janeiro - RJ',
  55213234 => 'Rio de Janeiro - RJ',
  55213235 => 'Rio de Janeiro - RJ',
  55213236 => 'Nilópolis - RJ',
  55213237 => 'Rio de Janeiro - RJ',
  55213238 => 'Rio de Janeiro - RJ',
  55213239 => 'Rio de Janeiro - RJ',
  55213252 => 'Rio de Janeiro - RJ',
  55213263 => 'Rio de Janeiro - RJ',
  55213267 => 'Rio de Janeiro - RJ',
  55213274 => 'Rio de Janeiro - RJ',
  55213279 => 'Rio de Janeiro - RJ',
  55213288 => 'Rio de Janeiro - RJ',
  55213291 => 'Rio de Janeiro - RJ',
  55213292 => 'Rio de Janeiro - RJ',
  55213293 => 'Rio de Janeiro - RJ',
  55213294 => 'Rio de Janeiro - RJ',
  55213295 => 'Rio de Janeiro - RJ',
  55213296 => 'Rio de Janeiro - RJ',
  55213303 => 'Duque de Caxias - RJ',
  55213304 => 'Belford Roxo - RJ',
  55213305 => 'Rio de Janeiro - RJ',
  55213309 => 'Rio de Janeiro - RJ',
  55213311 => 'Macaé - RJ',
  55213312 => 'Rio de Janeiro - RJ',
  55213313 => 'Rio de Janeiro - RJ',
  55213314 => 'Rio de Janeiro - RJ',
  55213316 => 'Rio de Janeiro - RJ',
  55213317 => 'Rio de Janeiro - RJ',
  55213321 => 'Rio de Janeiro - RJ',
  55213322 => 'Rio de Janeiro - RJ',
  55213323 => 'Rio de Janeiro - RJ',
  55213324 => 'Rio de Janeiro - RJ',
  55213325 => 'Rio de Janeiro - RJ',
  55213326 => 'Rio de Janeiro - RJ',
  55213328 => 'Rio de Janeiro - RJ',
  55213329 => 'Rio de Janeiro - RJ',
  55213331 => 'Rio de Janeiro - RJ',
  55213332 => 'Rio de Janeiro - RJ',
  55213333 => 'Rio de Janeiro - RJ',
  55213335 => 'Rio de Janeiro - RJ',
  55213336 => 'Rio de Janeiro - RJ',
  55213337 => 'Rio de Janeiro - RJ',
  55213338 => 'Rio de Janeiro - RJ',
  55213339 => 'Rio de Janeiro - RJ',
  55213340 => 'Rio de Janeiro - RJ',
  55213341 => 'Rio de Janeiro - RJ',
  55213342 => 'Rio de Janeiro - RJ',
  55213346 => 'Rio de Janeiro - RJ',
  55213347 => 'Rio de Janeiro - RJ',
  55213348 => 'Rio de Janeiro - RJ',
  55213350 => 'Rio de Janeiro - RJ',
  55213351 => 'Rio de Janeiro - RJ',
  55213353 => 'Rio de Janeiro - RJ',
  55213354 => 'Rio de Janeiro - RJ',
  55213355 => 'Rio de Janeiro - RJ',
  55213356 => 'Rio de Janeiro - RJ',
  55213357 => 'Rio de Janeiro - RJ',
  55213358 => 'Rio de Janeiro - RJ',
  55213359 => 'Rio de Janeiro - RJ',
  55213361 => 'Rio de Janeiro - RJ',
  55213362 => 'Rio de Janeiro - RJ',
  55213363 => 'Rio de Janeiro - RJ',
  55213364 => 'Rio de Janeiro - RJ',
  55213365 => 'Rio de Janeiro - RJ',
  55213369 => 'Rio de Janeiro - RJ',
  55213371 => 'Rio de Janeiro - RJ',
  55213372 => 'Rio de Janeiro - RJ',
  55213373 => 'Rio de Janeiro - RJ',
  55213375 => 'Rio de Janeiro - RJ',
  55213376 => 'Rio de Janeiro - RJ',
  55213377 => 'Rio de Janeiro - RJ',
  55213378 => 'Rio de Janeiro - RJ',
  55213380 => 'Rio de Janeiro - RJ',
  55213381 => 'Rio de Janeiro - RJ',
  55213382 => 'Rio de Janeiro - RJ',
  55213383 => 'Rio de Janeiro - RJ',
  55213384 => 'Rio de Janeiro - RJ',
  55213385 => 'Rio de Janeiro - RJ',
  55213386 => 'Rio de Janeiro - RJ',
  55213387 => 'Rio de Janeiro - RJ',
  55213388 => 'Rio de Janeiro - RJ',
  55213389 => 'Rio de Janeiro - RJ',
  55213390 => 'Rio de Janeiro - RJ',
  55213391 => 'Rio de Janeiro - RJ',
  55213392 => 'Rio de Janeiro - RJ',
  55213393 => 'Rio de Janeiro - RJ',
  55213394 => 'Rio de Janeiro - RJ',
  55213395 => 'Rio de Janeiro - RJ',
  55213397 => 'Rio de Janeiro - RJ',
  55213398 => 'Rio de Janeiro - RJ',
  55213400 => 'Rio de Janeiro - RJ',
  55213401 => 'Rio de Janeiro - RJ',
  55213402 => 'Rio de Janeiro - RJ',
  55213403 => 'Rio de Janeiro - RJ',
  55213404 => 'Rio de Janeiro - RJ',
  55213405 => 'Rio de Janeiro - RJ',
  55213406 => 'Rio de Janeiro - RJ',
  55213407 => 'Rio de Janeiro - RJ',
  55213408 => 'Rio de Janeiro - RJ',
  55213409 => 'Rio de Janeiro - RJ',
  55213410 => 'Rio de Janeiro - RJ',
  55213411 => 'Rio de Janeiro - RJ',
  55213412 => 'Rio de Janeiro - RJ',
  55213413 => 'Rio de Janeiro - RJ',
  55213417 => 'Rio de Janeiro - RJ',
  55213418 => 'Rio de Janeiro - RJ',
  55213419 => 'Rio de Janeiro - RJ',
  55213421 => 'Rio de Janeiro - RJ',
  55213422 => 'Rio de Janeiro - RJ',
  55213423 => 'Rio de Janeiro - RJ',
  55213424 => 'Rio de Janeiro - RJ',
  55213425 => 'Rio de Janeiro - RJ',
  55213426 => 'Rio de Janeiro - RJ',
  55213427 => 'Rio de Janeiro - RJ',
  55213431 => 'Rio de Janeiro - RJ',
  55213432 => 'Rio de Janeiro - RJ',
  55213433 => 'Rio de Janeiro - RJ',
  55213434 => 'Rio de Janeiro - RJ',
  55213445 => 'Rio de Janeiro - RJ',
  55213448 => 'Rio de Janeiro - RJ',
  55213450 => 'Rio de Janeiro - RJ',
  55213451 => 'Rio de Janeiro - RJ',
  55213452 => 'Rio de Janeiro - RJ',
  55213453 => 'Rio de Janeiro - RJ',
  55213454 => 'Rio de Janeiro - RJ',
  55213455 => 'Rio de Janeiro - RJ',
  55213458 => 'Rio de Janeiro - RJ',
  55213462 => 'Rio de Janeiro - RJ',
  55213463 => 'Rio de Janeiro - RJ',
  55213464 => 'Rio de Janeiro - RJ',
  55213465 => 'Rio de Janeiro - RJ',
  55213466 => 'Rio de Janeiro - RJ',
  55213467 => 'Rio de Janeiro - RJ',
  55213468 => 'Rio de Janeiro - RJ',
  55213469 => 'Rio de Janeiro - RJ',
  55213481 => 'Rio de Janeiro - RJ',
  55213483 => 'Rio de Janeiro - RJ',
  55213484 => 'Rio de Janeiro - RJ',
  55213485 => 'Rio de Janeiro - RJ',
  55213492 => 'Niterói - RJ',
  55213504 => 'Rio de Janeiro - RJ',
  55213508 => 'Itaboraí - RJ',
  55213512 => 'Rio de Janeiro - RJ',
  55213513 => 'Rio de Janeiro - RJ',
  55213520 => 'Rio de Janeiro - RJ',
  55213525 => 'Rio de Janeiro - RJ',
  55213528 => 'Rio de Janeiro - RJ',
  55213534 => 'Rio de Janeiro - RJ',
  55213540 => 'Nova Iguaçu - RJ',
  55213543 => 'Rio de Janeiro - RJ',
  55213551 => 'Rio de Janeiro - RJ',
  55213552 => 'Duque de Caxias - RJ',
  55213553 => 'Rio de Janeiro - RJ',
  55213554 => 'Rio de Janeiro - RJ',
  55213555 => 'Rio de Janeiro - RJ',
  55213568 => 'Niterói - RJ',
  55213584 => 'Nova Iguaçu - RJ',
  55213589 => 'Mesquita - RJ',
  55213601 => 'Niterói - RJ',
  55213602 => 'Niterói - RJ',
  55213604 => 'Niterói - RJ',
  55213610 => 'Niterói - RJ',
  55213611 => 'São Gonçalo - RJ',
  55213620 => 'Niterói - RJ',
  55213621 => 'Rio de Janeiro - RJ',
  55213626 => 'Rio de Janeiro - RJ',
  55213629 => 'Niterói - RJ',
  55213630 => 'Magé - RJ',
  55213632 => 'Magé - RJ',
  55213633 => 'Guapimirim - RJ',
  55213634 => 'Rio Bonito - RJ',
  55213639 => 'Itaboraí - RJ',
  55213642 => 'Teresópolis - RJ',
  55213643 => 'Teresópolis - RJ',
  55213644 => 'Teresópolis - RJ',
  55213660 => 'Barra Mansa - RJ',
  55213664 => 'Belford Roxo - RJ',
  55213665 => 'Queimados - RJ',
  55213667 => 'Duque de Caxias - RJ',
  55213668 => 'São João de Meriti - RJ',
  55213674 => 'Niterói - RJ',
  55213688 => 'Rio de Janeiro - RJ',
  55213691 => 'Japeri - RJ',
  55213693 => 'Paracambi - RJ',
  55213694 => 'Maricá - RJ',
  55213699 => 'Queimados - RJ',
  55213716 => 'São Gonçalo - RJ',
  55213724 => 'Rio de Janeiro - RJ',
  55213726 => 'Teresópolis - RJ',
  55213731 => 'Maricá - RJ',
  55213733 => 'Rio de Janeiro - RJ',
  55213736 => 'Rio de Janeiro - RJ',
  55213742 => 'Nova Iguaçu - RJ',
  55213743 => 'Nilópolis - RJ',
  55213745 => 'Nova Iguaçu - RJ',
  55213746 => 'Mesquita - RJ',
  55213747 => 'Rio de Janeiro - RJ',
  55213748 => 'Belford Roxo - RJ',
  55213752 => 'São João de Meriti - RJ',
  55213753 => 'São João de Meriti - RJ',
  55213754 => 'São João de Meriti - RJ',
  55213755 => 'São João de Meriti - RJ',
  55213756 => 'São João de Meriti - RJ',
  55213757 => 'São João de Meriti - RJ',
  55213760 => 'Nilópolis - RJ',
  55213761 => 'Nilópolis - RJ',
  55213762 => 'Nilópolis - RJ',
  55213763 => 'Mesquita - RJ',
  55213765 => 'Mesquita - RJ',
  55213766 => 'Nova Iguaçu - RJ',
  55213773 => 'Nova Iguaçu - RJ',
  55213774 => 'Duque de Caxias - RJ',
  55213779 => 'Nova Iguaçu - RJ',
  55213780 => 'Duque de Caxias - RJ',
  55213781 => 'Itaguaí - RJ',
  55213787 => 'Seropédica - RJ',
  55213789 => 'Mangaratiba - RJ',
  55213792 => 'Duque de Caxias - RJ',
  55213803 => 'Niterói - RJ',
  55213806 => 'Rio de Janeiro - RJ',
  55213809 => 'Rio de Janeiro - RJ',
  55213813 => 'Rio de Janeiro - RJ',
  55213814 => 'Rio de Janeiro - RJ',
  55213816 => 'Rio de Janeiro - RJ',
  55213818 => 'Rio de Janeiro - RJ',
  55213820 => 'Rio de Janeiro - RJ',
  55213823 => 'Rio de Janeiro - RJ',
  55213824 => 'Rio de Janeiro - RJ',
  55213826 => 'Rio de Janeiro - RJ',
  55213828 => 'Rio de Janeiro - RJ',
  55213830 => 'Rio de Janeiro - RJ',
  55213833 => 'Rio de Janeiro - RJ',
  55213835 => 'Rio de Janeiro - RJ',
  55213837 => 'Rio de Janeiro - RJ',
  55213839 => 'Rio de Janeiro - RJ',
  55213842 => 'Duque de Caxias - RJ',
  55213844 => 'Nova Iguaçu - RJ',
  55213845 => 'Duque de Caxias - RJ',
  55213846 => 'Duque de Caxias - RJ',
  55213847 => 'Rio de Janeiro - RJ',
  55213849 => 'Rio de Janeiro - RJ',
  55213850 => 'Rio de Janeiro - RJ',
  55213851 => 'Nova Iguaçu - RJ',
  55213852 => 'Rio de Janeiro - RJ',
  55213855 => 'Rio de Janeiro - RJ',
  55213856 => 'São Gonçalo - RJ',
  55213857 => 'São Gonçalo - RJ',
  55213860 => 'Rio de Janeiro - RJ',
  55213861 => 'Rio de Janeiro - RJ',
  55213865 => 'Rio de Janeiro - RJ',
  55213867 => 'Rio de Janeiro - RJ',
  55213868 => 'Rio de Janeiro - RJ',
  55213869 => 'Rio de Janeiro - RJ',
  55213870 => 'Rio de Janeiro - RJ',
  55213872 => 'Rio de Janeiro - RJ',
  55213873 => 'Rio de Janeiro - RJ',
  55213874 => 'Rio de Janeiro - RJ',
  55213875 => 'Rio de Janeiro - RJ',
  55213878 => 'Rio de Janeiro - RJ',
  55213879 => 'Rio de Janeiro - RJ',
  55213882 => 'Rio de Janeiro - RJ',
  55213884 => 'Rio de Janeiro - RJ',
  55213885 => 'Rio de Janeiro - RJ',
  55213886 => 'Rio de Janeiro - RJ',
  55213887 => 'Rio de Janeiro - RJ',
  55213888 => 'Rio de Janeiro - RJ',
  55213889 => 'Rio de Janeiro - RJ',
  55213890 => 'Rio de Janeiro - RJ',
  55213891 => 'Rio de Janeiro - RJ',
  55213895 => 'Rio de Janeiro - RJ',
  55213896 => 'Duque de Caxias - RJ',
  55213899 => 'Rio de Janeiro - RJ',
  55213913 => 'Itaboraí - RJ',
  55213916 => 'Rio de Janeiro - RJ',
  55213917 => 'Maricá - RJ',
  55213923 => 'Rio de Janeiro - RJ',
  55213938 => 'Rio de Janeiro - RJ',
  55213970 => 'Rio de Janeiro - RJ',
  55213974 => 'Rio de Janeiro - RJ',
  55213976 => 'Rio de Janeiro - RJ',
  55213977 => 'Rio de Janeiro - RJ',
  55213978 => 'Rio de Janeiro - RJ',
  55213980 => 'Rio de Janeiro - RJ',
  55213982 => 'Rio de Janeiro - RJ',
  55213987 => 'Rio de Janeiro - RJ',
  55214007 => 'Rio de Janeiro - RJ',
  55214062 => 'Rio de Janeiro - RJ',
  55214117 => 'Nova Iguaçu - RJ',
  55214118 => 'Rio de Janeiro - RJ',
  55214125 => 'Belford Roxo - RJ',
  55214133 => 'Magé - RJ',
  55214137 => 'Nilópolis - RJ',
  55214138 => 'Queimados - RJ',
  55214139 => 'Maricá - RJ',
  55214501 => 'Rio de Janeiro - RJ',
  55214503 => 'Rio de Janeiro - RJ',
  5522 => 'Rio de Janeiro',
  55222009 => 'Campos dos Goytacazes - RJ',
  55222030 => 'Campos dos Goytacazes - RJ',
  55222031 => 'Saquarema - RJ',
  55222101 => 'Campos dos Goytacazes - RJ',
  55222102 => 'Nova Friburgo - RJ',
  55222103 => 'Nova Friburgo - RJ',
  55222105 => 'Macaé - RJ',
  55222106 => 'Macaé - RJ',
  55222123 => 'Macaé - RJ',
  55222134 => 'Iguaba Grande - RJ',
  55222222 => 'Nova Friburgo - RJ',
  55222519 => 'Nova Friburgo - RJ',
  55222520 => 'Nova Friburgo - RJ',
  55222521 => 'Nova Friburgo - RJ',
  55222522 => 'Nova Friburgo - RJ',
  55222523 => 'Nova Friburgo - RJ',
  55222524 => 'Nova Friburgo - RJ',
  55222525 => 'Nova Friburgo - RJ',
  55222526 => 'Nova Friburgo - RJ',
  55222527 => 'Nova Friburgo - RJ',
  55222528 => 'Nova Friburgo - RJ',
  55222529 => 'Nova Friburgo - RJ',
  55222531 => 'Sumidouro - RJ',
  55222533 => 'Nova Friburgo - RJ',
  55222534 => 'Duas Barras - RJ',
  55222537 => 'Carmo - RJ',
  55222540 => 'Nova Friburgo - RJ',
  55222541 => 'Nova Friburgo - RJ',
  55222542 => 'Nova Friburgo - RJ',
  55222543 => 'Nova Friburgo - RJ',
  55222551 => 'Cordeiro - RJ',
  55222552 => 'Santa Rita da Floresta - RJ',
  55222553 => 'Cantagalo - RJ',
  55222554 => 'Macuco - RJ',
  55222555 => 'Cantagalo - RJ',
  55222556 => 'São Sebastião do Alto - RJ',
  55222559 => 'São Sebastião do Alto - RJ',
  55222561 => 'Santa Maria Madalena - RJ',
  55222564 => 'Trajano de Morais - RJ',
  55222565 => 'Bom Jardim - RJ',
  55222566 => 'Bom Jardim - RJ',
  55222580 => 'Nova Friburgo - RJ',
  55222621 => 'São Pedro da Aldeia - RJ',
  55222622 => 'Arraial do Cabo - RJ',
  55222623 => 'Armação dos Búzios - RJ',
  55222624 => 'Iguaba Grande - RJ',
  55222627 => 'São Pedro da Aldeia - RJ',
  55222630 => 'Cabo Frio - RJ',
  55222633 => 'Armação dos Búzios - RJ',
  55222640 => 'Cabo Frio - RJ',
  55222643 => 'Cabo Frio - RJ',
  55222644 => 'Cabo Frio - RJ',
  55222645 => 'Cabo Frio - RJ',
  55222647 => 'Cabo Frio - RJ',
  55222651 => 'Saquarema - RJ',
  55222652 => 'Saquarema - RJ',
  55222653 => 'Saquarema - RJ',
  55222654 => 'Sampaio Correia - RJ',
  55222655 => 'Saquarema - RJ',
  55222661 => 'Araruama - RJ',
  55222662 => 'Arraial do Cabo - RJ',
  55222664 => 'Araruama - RJ',
  55222665 => 'Araruama - RJ',
  55222666 => 'São Vicente de Paula - RJ',
  55222667 => 'Araruama - RJ',
  55222668 => 'Silva Jardim - RJ',
  55222673 => 'Araruama - RJ',
  55222674 => 'Araruama - RJ',
  55222720 => 'Campos dos Goytacazes - RJ',
  55222721 => 'Campos dos Goytacazes - RJ',
  55222722 => 'Campos dos Goytacazes - RJ',
  55222723 => 'Campos dos Goytacazes - RJ',
  55222724 => 'Campos dos Goytacazes - RJ',
  55222726 => 'Campos dos Goytacazes - RJ',
  55222727 => 'São Francisco de Itabapoana - RJ',
  55222732 => 'Campos dos Goytacazes - RJ',
  55222733 => 'Campos dos Goytacazes - RJ',
  55222734 => 'Campos dos Goytacazes - RJ',
  55222735 => 'Campos dos Goytacazes - RJ',
  55222741 => 'São João da Barra - RJ',
  55222747 => 'Farol de São Tomé - RJ',
  55222748 => 'Travessão - RJ',
  55222751 => 'São Fidélis - RJ',
  55222757 => 'Macaé - RJ',
  55222758 => 'São Fidélis - RJ',
  55222759 => 'Macaé - RJ',
  55222760 => 'Rio das Ostras - RJ',
  55222762 => 'Macaé - RJ',
  55222764 => 'Rio das Ostras - RJ',
  55222765 => 'Macaé - RJ',
  55222767 => 'Cambuci - RJ',
  55222768 => 'Quissamã - RJ',
  55222771 => 'Rio das Ostras - RJ',
  55222772 => 'Macaé - RJ',
  55222773 => 'Macaé - RJ',
  55222777 => 'Rio das Ostras - RJ',
  55222778 => 'Casimiro de Abreu - RJ',
  55222779 => 'Conceição de Macabu - RJ',
  55222781 => 'Campos dos Goytacazes - RJ',
  55222783 => 'Italva - RJ',
  55222785 => 'Cardoso Moreira - RJ',
  55222789 => 'São Francisco de Paula - RJ',
  55222791 => 'Macaé - RJ',
  55222793 => 'Macaé - RJ',
  55222796 => 'Macaé - RJ',
  55223012 => 'Campos dos Goytacazes - RJ',
  55223013 => 'Campos dos Goitacazes - RJ',
  55223016 => 'Nova Friburgo - RJ',
  55223021 => 'Araruama - RJ',
  55223022 => 'Itaperuna - RJ',
  55223051 => 'Macaé - RJ',
  55223053 => 'Cabo Frio - RJ',
  55223054 => 'Campos dos Goytacazes - RJ',
  55223056 => 'Campos dos Goytacazes - RJ',
  55223058 => 'Cabo Frio - RJ',
  55223066 => 'Nova Friburgo - RJ',
  55223081 => 'Macaé - RJ',
  55223084 => 'Macaé - RJ',
  55223087 => 'Macaé - RJ',
  55223094 => 'Campos dos Goytacazes - RJ',
  55223201 => 'Araruama - RJ',
  55223205 => 'Saquarema - RJ',
  55223211 => 'Campos dos Goytacazes - RJ',
  55223234 => 'Campos dos Goitacazes - RJ',
  55223308 => 'São Pedro da Aldeia - RJ',
  55223311 => 'Macaé - RJ',
  55223321 => 'Rio das Ostras - RJ',
  55223512 => 'Nova Friburgo - RJ',
  55223533 => 'Vila Velha - ES',
  55223717 => 'Macaé - RJ',
  55223723 => 'Macaé - RJ',
  55223811 => 'Itaperuna - RJ',
  55223820 => 'Itaperuna - RJ',
  55223822 => 'Itaperuna - RJ',
  55223823 => 'Itaperuna - RJ',
  55223824 => 'Itaperuna - RJ',
  55223826 => 'Itaperuna - RJ',
  55223827 => 'Itaperuna - RJ',
  55223828 => 'Boaventura - RJ',
  55223829 => 'Laje do Muriaé - RJ',
  55223831 => 'Bom Jesus do Itabapoana - RJ',
  55223832 => 'Rosal - RJ',
  55223833 => 'Bom Jesus do Itabapoana - RJ',
  55223835 => 'Carabuçu - RJ',
  55223841 => 'Natividade - RJ',
  55223842 => 'Porciúncula - RJ',
  55223843 => 'Varre-Sai - RJ',
  55223844 => 'Porciúncula - RJ',
  55223847 => 'Raposo - RJ',
  55223850 => 'Miracema - RJ',
  55223851 => 'Santo Antônio de Pádua - RJ',
  55223852 => 'Miracema - RJ',
  55223853 => 'Santo Antônio de Pádua - RJ',
  55223854 => 'Santo Antônio de Pádua - RJ',
  55223855 => 'Rio de Janeiro',
  55223861 => 'Itaocara - RJ',
  55223862 => 'Portela - RJ',
  55223863 => 'Jaguarembé - RJ',
  55223864 => 'Aperibé - RJ',
  55223865 => 'São João do Paraíso - RJ',
  55223866 => 'São José de Ubá - RJ',
  55224009 => 'Campos dos Goitacazes - RJ',
  55224104 => 'Macaé - RJ',
  55224105 => 'Nova Friburgo - RJ',
  55224141 => 'Campos dos Goytacazes - RJ',
  5524 => 'Rio de Janeiro',
  55242102 => 'Volta Redonda - RJ',
  55242103 => 'Petrópolis - RJ',
  55242104 => 'Petrópolis - RJ',
  55242106 => 'Barra Mansa - RJ',
  55242107 => 'Volta Redonda - RJ',
  55242109 => 'Resende - RJ',
  55242220 => 'Petrópolis - RJ',
  55242221 => 'Petrópolis - RJ',
  55242222 => 'Petrópolis - RJ',
  55242223 => 'Petrópolis - RJ',
  55242224 => 'São José do Vale do Rio Preto - RJ',
  55242225 => 'Petrópolis - RJ',
  55242228 => 'Secretário - RJ',
  55242231 => 'Petrópolis - RJ',
  55242232 => 'Petrópolis - RJ',
  55242233 => 'Petrópolis - RJ',
  55242235 => 'Petrópolis - RJ',
  55242236 => 'Petrópolis - RJ',
  55242237 => 'Petrópolis - RJ',
  55242242 => 'Petrópolis - RJ',
  55242243 => 'Petrópolis - RJ',
  55242244 => 'Petrópolis - RJ',
  55242245 => 'Petrópolis - RJ',
  55242246 => 'Petrópolis - RJ',
  55242247 => 'Petrópolis - RJ',
  55242248 => 'Petrópolis - RJ',
  55242249 => 'Petrópolis - RJ',
  55242251 => 'Três Rios - RJ',
  55242252 => 'Três Rios - RJ',
  55242254 => 'Comendador Levy Gasparian - RJ',
  55242255 => 'Três Rios - RJ',
  55242257 => 'Areal - RJ',
  55242258 => 'Bemposta - RJ',
  55242259 => 'Petrópolis - RJ',
  55242263 => 'Paraíba do Sul - RJ',
  55242266 => 'Werneck - RJ',
  55242271 => 'Sapucaia - RJ',
  55242280 => 'Petrópolis - RJ',
  55242291 => 'Petrópolis - RJ',
  55242292 => 'Petrópolis - RJ',
  55242401 => 'Barra do Piraí - RJ',
  55242404 => 'Angra dos Reis - RJ',
  55242411 => 'Barra do Piraí - RJ',
  55242430 => 'Barra do Piraí - RJ',
  55242431 => 'Piraí - RJ',
  55242433 => 'Barra do Piraí - RJ',
  55242437 => 'Ipiabas - RJ',
  55242438 => 'Conservatória - RJ',
  55242442 => 'Barra do Piraí - RJ',
  55242443 => 'Barra do Piraí - RJ',
  55242444 => 'Barra do Piraí - RJ',
  55242445 => 'Barra do Piraí - RJ',
  55242447 => 'Barra do Piraí - RJ',
  55242452 => 'Valença - RJ',
  55242453 => 'Valença - RJ',
  55242457 => 'Santa Isabel do Rio Preto - RJ',
  55242458 => 'Rio das Flores - RJ',
  55242463 => 'Engenheiro Paulo de Frontin - RJ',
  55242465 => 'Mendes - RJ',
  55242471 => 'Vassouras - RJ',
  55242483 => 'Miguel Pereira - RJ',
  55242484 => 'Miguel Pereira - RJ',
  55242485 => 'Paty do Alferes - RJ',
  55242487 => 'Avelar - RJ',
  55242491 => 'Vassouras - RJ',
  55242522 => 'Nova Friburgo - RJ',
  55242566 => 'Bom Jardim - RJ',
  55242723 => 'Campos dos Goitacazes - RJ',
  55243064 => 'Petrópolis - RJ',
  55243065 => 'Petrópolis - RJ',
  55243076 => 'Volta Redonda - RJ',
  55243111 => 'Petrópolis - RJ',
  55243302 => 'Petrópolis - RJ',
  55243320 => 'Volta Redonda - RJ',
  55243321 => 'Resende - RJ',
  55243322 => 'Barra Mansa - RJ',
  55243323 => 'Barra Mansa - RJ',
  55243324 => 'Barra Mansa - RJ',
  55243325 => 'Barra Mansa - RJ',
  55243328 => 'Barra Mansa - RJ',
  55243332 => 'Rio Claro - RJ',
  55243333 => 'Arrozal - RJ',
  55243334 => 'Rio Claro - RJ',
  55243335 => 'Rio Claro - RJ',
  55243342 => 'Volta Redonda - RJ',
  55243343 => 'Volta Redonda - RJ',
  55243344 => 'Volta Redonda - RJ',
  55243345 => 'Volta Redonda - RJ',
  55243346 => 'Volta Redonda - RJ',
  55243347 => 'Volta Redonda - RJ',
  55243348 => 'Volta Redonda - RJ',
  55243351 => 'Itatiaia - RJ',
  55243352 => 'Itatiaia - RJ',
  55243353 => 'Porto Real - RJ',
  55243354 => 'Resende - RJ',
  55243355 => 'Resende - RJ',
  55243356 => 'Pinheiral - RJ',
  55243357 => 'Resende - RJ',
  55243358 => 'Resende - RJ',
  55243363 => 'Angra dos Reis - RJ',
  55243364 => 'Angra dos Reis - RJ',
  55243365 => 'Angra dos Reis - RJ',
  55243366 => 'Angra dos Reis - RJ',
  55243367 => 'Angra dos Reis - RJ',
  55243368 => 'Angra dos Reis - RJ',
  55243369 => 'Angra dos Reis - RJ',
  55243370 => 'Angra dos Reis - RJ',
  55243371 => 'Paraty - RJ',
  55243372 => 'Paraty - RJ',
  55243373 => 'Paraty - RJ',
  55243377 => 'Angra dos Reis - RJ',
  55243379 => 'Angra dos Reis - RJ',
  55243381 => 'Resende - RJ',
  55243387 => 'Visconde de Mauá - RJ',
  55243388 => 'Resende - RJ',
  55243389 => 'Barra Mansa - RJ',
  55243401 => 'Barra Mansa - RJ',
  55243421 => 'Angra dos Reis - RJ',
  55244004 => 'Petrópolis - RJ',
  5527 => 'Espirito Santo',
  55272101 => 'Colatina - ES',
  55272102 => 'Colatina - ES',
  55272103 => 'Linhares - ES',
  55272144 => 'Vila Velha - ES',
  55273015 => 'Vitória - ES',
  55273021 => 'Guarapari - ES',
  55273031 => 'Vila Velha - ES',
  55273032 => 'Vila Velha - ES',
  55273033 => 'Vila Velha - ES',
  55273044 => 'Colatina - ES',
  55273047 => 'Linhares - ES',
  55273048 => 'Linhares - ES',
  55273049 => 'Colatina - ES',
  55273051 => 'Serra - ES',
  55273064 => 'Serra - ES',
  55273065 => 'Serra - ES',
  55273071 => 'Vitória - ES',
  55273072 => 'Vila Velha - ES',
  55273076 => 'Cariacica - ES',
  55273079 => 'Serra - ES',
  55273080 => 'Serra - ES',
  55273081 => 'Vitória - ES',
  55273082 => 'Vitória - ES',
  55273084 => 'Vitória - ES',
  55273090 => 'Cariacica - ES',
  55273091 => 'Cariacica - ES',
  55273111 => 'Aracruz - ES',
  55273115 => 'Linhares - ES',
  55273120 => 'Colatina - ES',
  55273125 => 'Guarapari - ES',
  55273129 => 'Guarapari - ES',
  55273132 => 'Vitória - ES',
  55273136 => 'Cariacica - ES',
  55273137 => 'Vitória - ES',
  55273138 => 'Serra - ES',
  55273139 => 'Vila Velha - ES',
  55273145 => 'Vitória - ES',
  55273151 => 'Linhares - ES',
  55273161 => 'Guarapari - ES',
  55273171 => 'Linhares - ES',
  55273177 => 'Colatina - ES',
  55273181 => 'Vitória - ES',
  55273182 => 'Vitória - ES',
  55273194 => 'Vitória - ES',
  55273198 => 'Vitória - ES',
  55273213 => 'Cariacica - ES',
  55273215 => 'Vitória - ES',
  55273222 => 'Vitória - ES',
  55273223 => 'Vitória - ES',
  55273224 => 'Vitória - ES',
  55273225 => 'Vitória - ES',
  55273227 => 'Vitória - ES',
  55273229 => 'Vila Velha - ES',
  55273232 => 'Vitória - ES',
  55273233 => 'Vitória - ES',
  55273235 => 'Vitória - ES',
  55273237 => 'Vitória - ES',
  55273239 => 'Vila Velha - ES',
  55273242 => 'Vila Velha - ES',
  55273245 => 'Serra - ES',
  55273248 => 'Aracê - ES',
  55273249 => 'Paraju - ES',
  55273250 => 'Coqueiral - ES',
  55273251 => 'Serra - ES',
  55273253 => 'Serra - ES',
  55273254 => 'Cariacica - ES',
  55273255 => 'Viana - ES',
  55273256 => 'Aracruz - ES',
  55273257 => 'Ibiraçu - ES',
  55273258 => 'João Neiva - ES',
  55273259 => 'Santa Teresa - ES',
  55273260 => 'Vila Velha - ES',
  55273261 => 'Guarapari - ES',
  55273262 => 'Guarapari - ES',
  55273263 => 'Santa Maria de Jetibá - ES',
  55273264 => 'Linhares - ES',
  55273265 => 'Rio Bananal - ES',
  55273266 => 'Santa Leopoldina - ES',
  55273267 => 'Fundão - ES',
  55273268 => 'Domingos Martins - ES',
  55273269 => 'Alfredo Chaves - ES',
  55273270 => 'Barra do Riacho - ES',
  55273272 => 'Guarapari - ES',
  55273273 => 'Sooretama - ES',
  55273275 => 'Aracruz - ES',
  55273276 => 'Guaraná - ES',
  55273277 => 'Timbuí - ES',
  55273278 => 'Acioli - ES',
  55273287 => 'Fundão - ES',
  55273288 => 'Marechal Floriano - ES',
  55273296 => 'Aracruz - ES',
  55273299 => 'Vila Velha - ES',
  55273302 => 'Aracruz - ES',
  55273311 => 'Vila Velha - ES',
  55273312 => 'São Mateus - ES',
  55273313 => 'São Mateus - ES',
  55273314 => 'Vitória - ES',
  55273315 => 'Vitória - ES',
  55273317 => 'Vitória - ES',
  55273321 => 'Vitória - ES',
  55273322 => 'Vitória - ES',
  55273324 => 'Vitória - ES',
  55273325 => 'Vitória - ES',
  55273326 => 'Vila Velha - ES',
  55273327 => 'Vitória - ES',
  55273331 => 'Vitória - ES',
  55273332 => 'Vitória - ES',
  55273333 => 'Vitória - ES',
  55273335 => 'Vitória - ES',
  55273337 => 'Vitória - ES',
  55273341 => 'Serra - ES',
  55273344 => 'Viana - ES',
  55273345 => 'Vitória - ES',
  55273347 => 'Vitória - ES',
  55273350 => 'Vila Velha - ES',
  55273354 => 'Viana - ES',
  55273355 => 'Vitória - ES',
  55273357 => 'Vitória - ES',
  55273361 => 'Guarapari - ES',
  55273362 => 'Guarapari - ES',
  55273364 => 'Guarapari - ES',
  55273369 => 'Vila Velha - ES',
  55273371 => 'Linhares - ES',
  55273372 => 'Linhares - ES',
  55273373 => 'Linhares - ES',
  55273381 => 'Vitória - ES',
  55273382 => 'Vitória - ES',
  55273385 => 'Serra - ES',
  55273388 => 'Vila Velha - ES',
  55273395 => 'Vitória - ES',
  55273401 => 'Baixo Guandu - ES',
  55273422 => 'Cariacica - ES',
  55273533 => 'Vila Velha - ES',
  55273553 => 'Guaçuí - ES',
  55273555 => 'Mimoso do Sul - ES',
  55273711 => 'Colatina - ES',
  55273717 => 'Colatina - ES',
  55273720 => 'Itarana - ES',
  55273721 => 'Colatina - ES',
  55273722 => 'Colatina - ES',
  55273723 => 'Colatina - ES',
  55273724 => 'Marilândia - ES',
  55273725 => 'Itaguaçu - ES',
  55273726 => 'Pancas - ES',
  55273727 => 'São Gabriel da Palha - ES',
  55273728 => 'Vila Valério - ES',
  55273729 => 'São Roque do Canaã - ES',
  55273732 => 'Baixo Guandu - ES',
  55273733 => 'Brejetuba - ES',
  55273735 => 'Afonso Cláudio - ES',
  55273736 => 'Laranja da Terra - ES',
  55273742 => 'São Domingos do Norte - ES',
  55273743 => 'Colatina - ES',
  55273744 => 'Governador Lindenberg - ES',
  55273745 => 'Águia Branca - ES',
  55273746 => 'Alto Rio Novo - ES',
  55273751 => 'Mucurici - ES',
  55273752 => 'Nova Venécia - ES',
  55273753 => 'Vila Pavão - ES',
  55273754 => 'Montanha - ES',
  55273755 => 'Ecoporanga - ES',
  55273756 => 'Barra de São Francisco - ES',
  55273757 => 'Ponto Belo - ES',
  55273758 => 'Mantenópolis - ES',
  55273759 => 'Água Doce do Norte - ES',
  55273761 => 'São Mateus - ES',
  55273762 => 'Conceição da Barra - ES',
  55273763 => 'São Mateus - ES',
  55273764 => 'Pedro Canário - ES',
  55273765 => 'Pinheiros - ES',
  55273767 => 'São Mateus - ES',
  55273768 => 'Boa Esperança - ES',
  55273769 => 'Jaguaré - ES',
  55273770 => 'Colatina - ES',
  55273771 => 'São Mateus - ES',
  55273772 => 'Nova Venécia - ES',
  55273773 => 'São Mateus - ES',
  55273776 => 'Barra de São Francisco - ES',
  55274002 => 'Vitória - ES',
  55274003 => 'Vitória - ES',
  55274007 => 'Vitória - ES',
  55274102 => 'Serra - ES',
  55274104 => 'Cariacica - ES',
  55274105 => 'Vila Velha - ES',
  5528 => 'Espirito Santo',
  55282101 => 'Cachoeiro de Itapemirim - ES',
  55282102 => 'Cachoeiro de Itapemirim - ES',
  55283036 => 'Cachoeiro de Itapemirim - ES',
  55283037 => 'Cachoeiro de Itapemirim - ES',
  55283155 => 'Cachoeiro de Itapemirim - ES',
  55283310 => 'Castelo - ES',
  55283322 => 'Cachoeiro de Itapemirim - ES',
  55283511 => 'Cachoeiro de Itapemirim - ES',
  55283515 => 'Cachoeiro de Itapemirim - ES',
  55283517 => 'Cachoeiro de Itapemirim - ES',
  55283518 => 'Cachoeiro de Itapemirim - ES',
  55283520 => 'Piúma - ES',
  55283521 => 'Cachoeiro de Itapemirim - ES',
  55283522 => 'Cachoeiro de Itapemirim - ES',
  55283523 => 'Gironda - ES',
  55283524 => 'Vargem Grande do Soturno - ES',
  55283525 => 'Jaciguá - ES',
  55283526 => 'Cachoeiro de Itapemirim - ES',
  55283528 => 'Vargem Alta - ES',
  55283529 => 'Itapemirim - ES',
  55283531 => 'Itapemirim - ES',
  55283532 => 'Marataízes - ES',
  55283533 => 'Rio Novo do Sul - ES',
  55283534 => 'Anchieta - ES',
  55283535 => 'Presidente Kennedy - ES',
  55283536 => 'Anchieta - ES',
  55283537 => 'Iconha - ES',
  55283538 => 'Atilio Vivacqua - ES',
  55283539 => 'Itaoca - ES',
  55283542 => 'Castelo - ES',
  55283543 => 'Ibatiba - ES',
  55283544 => 'Muniz Freire - ES',
  55283545 => 'Iúna - ES',
  55283546 => 'Venda Nova do Imigrante - ES',
  55283547 => 'Conceição do Castelo - ES',
  55283548 => 'Irupi - ES',
  55283551 => 'Divino de São Lourenço - ES',
  55283552 => 'Alegre - ES',
  55283553 => 'Guaçuí - ES',
  55283554 => 'Muqui - ES',
  55283555 => 'Mimoso do Sul - ES',
  55283556 => 'São José do Calçado - ES',
  55283557 => 'Apiacá - ES',
  55283558 => 'Jerônimo Monteiro - ES',
  55283559 => 'Dores do Rio Preto - ES',
  55283560 => 'Alegre - ES',
  55283562 => 'Bom Jesus do Norte - ES',
  55283569 => 'Ibitirama - ES',
  5531 => 'Minas Gerais',
  55312101 => 'Belo Horizonte - MG',
  55312102 => 'Belo Horizonte - MG',
  55312103 => 'Belo Horizonte - MG',
  55312106 => 'Sete Lagoas - MG',
  55312107 => 'Sete Lagoas - MG',
  55312108 => 'Belo Horizonte - MG',
  55312109 => 'Ipatinga - MG',
  55312111 => 'Belo Horizonte - MG',
  55312112 => 'Belo Horizonte - MG',
  55312122 => 'Belo Horizonte - MG',
  55312128 => 'Belo Horizonte - MG',
  55312136 => 'Ipatinga - MG',
  55312138 => 'Belo Horizonte - MG',
  55312146 => 'Sete Lagoas - MG',
  55312191 => 'Contagem - MG',
  55312323 => 'Betim - MG',
  55312524 => 'Contagem - MG',
  55312535 => 'Belo Horizonte - MG',
  55312557 => 'Contagem - MG',
  55312559 => 'Contagem - MG',
  55312564 => 'Contagem - MG',
  55312571 => 'Betim - MG',
  55312572 => 'Betim - MG',
  55312586 => 'Contagem - MG',
  55312591 => 'Betim - MG',
  55313014 => 'Belo Horizonte - MG',
  55313026 => 'Sete Lagoas - MG',
  55313029 => 'Belo Horizonte - MG',
  55313034 => 'Contagem - MG',
  55313039 => 'Timóteo - MG',
  55313045 => 'Belo Horizonte - MG',
  55313048 => 'Belo Horizonte - MG',
  55313049 => 'Contagem - MG',
  55313055 => 'Belo Horizonte - MG',
  55313057 => 'Belo Horizonte - MG',
  55313061 => 'Conselheiro Lafaiete - MG',
  55313064 => 'Belo Horizonte - MG',
  55313067 => 'Itabira - MG',
  55313071 => 'Belo Horizonte - MG',
  55313074 => 'Belo Horizonte - MG',
  55313078 => 'Belo Horizonte - MG',
  55313080 => 'Belo Horizonte - MG',
  55313084 => 'Belo Horizonte - MG',
  55313090 => 'Belo Horizonte - MG',
  55313094 => 'Ipatinga - MG',
  55313096 => 'Ipatinga - MG',
  55313107 => 'Sete Lagoas - MG',
  55313111 => 'São Joaquim de Bicas - MG',
  55313116 => 'Belo Horizonte - MG',
  55313118 => 'Belo Horizonte - MG',
  55313121 => 'Igarapé - MG',
  55313123 => 'Belo Horizonte - MG',
  55313128 => 'Jaboticatubas - MG',
  55313130 => 'Matozinhos - MG',
  55313132 => 'Esmeraldas - MG',
  55313151 => 'Sete Lagoas - MG',
  55313152 => 'Sete Lagoas - MG',
  55313153 => 'Sete Lagoas - MG',
  55313159 => 'Betim - MG',
  55313161 => 'Ribeirão das Neves - MG',
  55313162 => 'Betim - MG',
  55313164 => 'Santa Luzia - MG',
  55313165 => 'Santa Luzia - MG',
  55313166 => 'Belo Horizonte - MG',
  55313184 => 'Itaguara - MG',
  55313194 => 'Belo Horizonte - MG',
  55313201 => 'Belo Horizonte - MG',
  55313202 => 'Belo Horizonte - MG',
  55313207 => 'Belo Horizonte - MG',
  55313212 => 'Belo Horizonte - MG',
  55313213 => 'Belo Horizonte - MG',
  55313214 => 'Belo Horizonte - MG',
  55313217 => 'Belo Horizonte - MG',
  55313218 => 'Belo Horizonte - MG',
  55313219 => 'Belo Horizonte - MG',
  55313221 => 'Belo Horizonte - MG',
  55313222 => 'Belo Horizonte - MG',
  55313223 => 'Belo Horizonte - MG',
  55313224 => 'Belo Horizonte - MG',
  55313225 => 'Belo Horizonte - MG',
  55313226 => 'Belo Horizonte - MG',
  55313227 => 'Belo Horizonte - MG',
  55313228 => 'Belo Horizonte - MG',
  55313235 => 'Belo Horizonte - MG',
  55313236 => 'Belo Horizonte - MG',
  55313237 => 'Belo Horizonte - MG',
  55313238 => 'Belo Horizonte - MG',
  55313239 => 'Belo Horizonte - MG',
  55313240 => 'Perpétuo Socorro - MG',
  55313241 => 'Belo Horizonte - MG',
  55313245 => 'Belo Horizonte - MG',
  55313247 => 'Belo Horizonte - MG',
  55313248 => 'Belo Horizonte - MG',
  55313249 => 'Belo Horizonte - MG',
  55313251 => 'Santana do Paraíso - MG',
  55313253 => 'Belo Horizonte - MG',
  55313254 => 'Belo Horizonte - MG',
  55313261 => 'Belo Horizonte - MG',
  55313262 => 'Belo Horizonte - MG',
  55313263 => 'Belo Horizonte - MG',
  55313267 => 'Belo Horizonte - MG',
  55313269 => 'Belo Horizonte - MG',
  55313270 => 'Belo Horizonte - MG',
  55313271 => 'Belo Horizonte - MG',
  55313272 => 'Belo Horizonte - MG',
  55313273 => 'Belo Horizonte - MG',
  55313274 => 'Belo Horizonte - MG',
  55313275 => 'Belo Horizonte - MG',
  55313278 => 'Belo Horizonte - MG',
  55313279 => 'Belo Horizonte - MG',
  55313280 => 'Belo Horizonte - MG',
  55313281 => 'Belo Horizonte - MG',
  55313282 => 'Belo Horizonte - MG',
  55313283 => 'Belo Horizonte - MG',
  55313284 => 'Belo Horizonte - MG',
  55313285 => 'Belo Horizonte - MG',
  55313286 => 'Belo Horizonte - MG',
  55313287 => 'Belo Horizonte - MG',
  55313288 => 'Belo Horizonte - MG',
  55313291 => 'Belo Horizonte - MG',
  55313292 => 'Belo Horizonte - MG',
  55313295 => 'Belo Horizonte - MG',
  55313303 => 'Belo Horizonte - MG',
  55313305 => 'Belo Horizonte - MG',
  55313309 => 'Belo Horizonte - MG',
  55313312 => 'Belo Horizonte - MG',
  55313318 => 'Belo Horizonte - MG',
  55313320 => 'Ipaba - MG',
  55313323 => 'Belo Horizonte - MG',
  55313324 => 'Belo Horizonte - MG',
  55313326 => 'Belo Horizonte - MG',
  55313327 => 'Belo Horizonte - MG',
  55313330 => 'Belo Horizonte - MG',
  55313335 => 'Belo Horizonte - MG',
  55313337 => 'Belo Horizonte - MG',
  55313338 => 'Nova Lima - MG',
  55313339 => 'Belo Horizonte - MG',
  55313342 => 'Belo Horizonte - MG',
  55313346 => 'Belo Horizonte - MG',
  55313351 => 'Contagem - MG',
  55313356 => 'Contagem - MG',
  55313357 => 'Contagem - MG',
  55313358 => 'Contagem - MG',
  55313373 => 'Belo Horizonte - MG',
  55313375 => 'Belo Horizonte - MG',
  55313376 => 'Belo Horizonte - MG',
  55313377 => 'Belo Horizonte - MG',
  55313378 => 'Belo Horizonte - MG',
  55313381 => 'Belo Horizonte - MG',
  55313383 => 'Belo Horizonte - MG',
  55313384 => 'Belo Horizonte - MG',
  55313388 => 'Belo Horizonte - MG',
  55313389 => 'Belo Horizonte - MG',
  55313391 => 'Contagem - MG',
  55313392 => 'Contagem - MG',
  55313394 => 'Belo Horizonte - MG',
  55313395 => 'Contagem - MG',
  55313397 => 'Contagem - MG',
  55313402 => 'Nova Lima - MG',
  55313408 => 'Belo Horizonte - MG',
  55313409 => 'Belo Horizonte - MG',
  55313412 => 'Belo Horizonte - MG',
  55313413 => 'Belo Horizonte - MG',
  55313415 => 'Belo Horizonte - MG',
  55313416 => 'Belo Horizonte - MG',
  55313417 => 'Belo Horizonte - MG',
  55313418 => 'Belo Horizonte - MG',
  55313424 => 'Belo Horizonte - MG',
  55313426 => 'Belo Horizonte - MG',
  55313428 => 'Belo Horizonte - MG',
  55313431 => 'Belo Horizonte - MG',
  55313432 => 'Belo Horizonte - MG',
  55313433 => 'Belo Horizonte - MG',
  55313434 => 'Belo Horizonte - MG',
  55313435 => 'Belo Horizonte - MG',
  55313444 => 'Belo Horizonte - MG',
  55313445 => 'Belo Horizonte - MG',
  55313446 => 'Belo Horizonte - MG',
  55313447 => 'Belo Horizonte - MG',
  55313449 => 'Belo Horizonte - MG',
  55313450 => 'Belo Horizonte - MG',
  55313451 => 'Belo Horizonte - MG',
  55313452 => 'Belo Horizonte - MG',
  55313453 => 'Belo Horizonte - MG',
  55313454 => 'Belo Horizonte - MG',
  55313455 => 'Belo Horizonte - MG',
  55313457 => 'Belo Horizonte - MG',
  55313458 => 'Belo Horizonte - MG',
  55313462 => 'Belo Horizonte - MG',
  55313464 => 'Belo Horizonte - MG',
  55313466 => 'Belo Horizonte - MG',
  55313471 => 'Belo Horizonte - MG',
  55313473 => 'Belo Horizonte - MG',
  55313474 => 'Belo Horizonte - MG',
  55313475 => 'Belo Horizonte - MG',
  55313476 => 'Belo Horizonte - MG',
  55313477 => 'Belo Horizonte - MG',
  55313478 => 'Belo Horizonte - MG',
  55313481 => 'Belo Horizonte - MG',
  55313483 => 'Belo Horizonte - MG',
  55313484 => 'Belo Horizonte - MG',
  55313485 => 'Belo Horizonte - MG',
  55313486 => 'Belo Horizonte - MG',
  55313487 => 'Belo Horizonte - MG',
  55313492 => 'Belo Horizonte - MG',
  55313493 => 'Belo Horizonte - MG',
  55313494 => 'Belo Horizonte - MG',
  55313495 => 'Belo Horizonte - MG',
  55313496 => 'Belo Horizonte - MG',
  55313497 => 'Belo Horizonte - MG',
  55313498 => 'Belo Horizonte - MG',
  55313504 => 'Belo Horizonte - MG',
  55313507 => 'Belo Horizonte - MG',
  55313508 => 'Belo Horizonte - MG',
  55313511 => 'Betim - MG',
  55313512 => 'Betim - MG',
  55313515 => 'Belo Horizonte - MG',
  55313519 => 'Belo Horizonte - MG',
  55313521 => 'Ibirité - MG',
  55313523 => 'Sítio Novo - MG',
  55313524 => 'Belo Horizonte - MG',
  55313528 => 'Belo Horizonte - MG',
  55313529 => 'Betim - MG',
  55313530 => 'Betim - MG',
  55313531 => 'Betim - MG',
  55313532 => 'Betim - MG',
  55313533 => 'Ibirité - MG',
  55313534 => 'Igarapé - MG',
  55313535 => 'Mateus Leme - MG',
  55313536 => 'Florestal - MG',
  55313537 => 'Serra Azul - MG',
  55313538 => 'Esmeraldas - MG',
  55313539 => 'Betim - MG',
  55313541 => 'Nova Lima - MG',
  55313542 => 'Nova Lima - MG',
  55313543 => 'Raposos - MG',
  55313544 => 'Betim - MG',
  55313545 => 'Rio Acima - MG',
  55313551 => 'Ouro Preto - MG',
  55313552 => 'Ouro Preto - MG',
  55313553 => 'Cachoeira do Campo - MG',
  55313554 => 'Lavras Novas - MG',
  55313556 => 'Mariana - MG',
  55313557 => 'Mariana - MG',
  55313558 => 'Mariana - MG',
  55313559 => 'Ouro Preto - MG',
  55313561 => 'Itabirito - MG',
  55313562 => 'Itabirito - MG',
  55313563 => 'Itabirito - MG',
  55313571 => 'Brumadinho - MG',
  55313572 => 'Itatiaiuçu - MG',
  55313573 => 'Rio Manso - MG',
  55313574 => 'Crucilândia - MG',
  55313575 => 'Moeda - MG',
  55313576 => 'Bonfim - MG',
  55313578 => 'Piedade dos Gerais - MG',
  55313579 => 'Aranha - MG',
  55313581 => 'Nova Lima - MG',
  55313589 => 'Nova Lima - MG',
  55313591 => 'Betim - MG',
  55313592 => 'Betim - MG',
  55313593 => 'Betim - MG',
  55313594 => 'Betim - MG',
  55313595 => 'Betim - MG',
  55313596 => 'Betim - MG',
  55313597 => 'Betim - MG',
  55313599 => 'Ibirité - MG',
  55313611 => 'Viçosa - MG',
  55313615 => 'Belo Horizonte - MG',
  55313621 => 'Vespasiano - MG',
  55313622 => 'Vespasiano - MG',
  55313623 => 'São José da Lapa - MG',
  55313624 => 'Ribeirão das Neves - MG',
  55313625 => 'Ribeirão das Neves - MG',
  55313626 => 'Ribeirão das Neves - MG',
  55313627 => 'Ribeirão das Neves - MG',
  55313628 => 'Ribeirão das Neves - MG',
  55313634 => 'Santa Luzia - MG',
  55313637 => 'Santa Luzia - MG',
  55313641 => 'Santa Luzia - MG',
  55313645 => 'Vespasiano - MG',
  55313649 => 'Santa Luzia - MG',
  55313651 => 'Caeté - MG',
  55313660 => 'Pedro Leopoldo - MG',
  55313661 => 'Pedro Leopoldo - MG',
  55313662 => 'Pedro Leopoldo - MG',
  55313663 => 'Pedro Leopoldo - MG',
  55313665 => 'Pedro Leopoldo - MG',
  55313667 => 'Coronel Fabriciano - MG',
  55313671 => 'Sabará - MG',
  55313672 => 'Sabará - MG',
  55313673 => 'Sabará - MG',
  55313674 => 'Sabará - MG',
  55313675 => 'Sabará - MG',
  55313679 => 'Sabará - MG',
  55313681 => 'Lagoa Santa - MG',
  55313683 => 'Jaboticatubas - MG',
  55313684 => 'Taquaraçu de Minas - MG',
  55313685 => 'Nova União - MG',
  55313686 => 'Confins - MG',
  55313694 => 'Nova Lima - MG',
  55313697 => 'Bairro Eldorado - Sete Lagoas MG',
  55313711 => 'Prudente de Morais - MG',
  55313712 => 'Matozinhos - MG',
  55313713 => 'Capim Branco - MG',
  55313714 => 'Paraopeba - MG',
  55313715 => 'Cordisburgo - MG',
  55313716 => 'Inhaúma - MG',
  55313717 => 'Santana de Pirapama - MG',
  55313718 => 'Baldim - MG',
  55313721 => 'Conselheiro Lafaiete - MG',
  55313722 => 'Queluzito - MG',
  55313723 => 'Casa Grande - MG',
  55313724 => 'Cristiano Otoni - MG',
  55313725 => 'Caranaíba - MG',
  55313726 => 'Santana dos Montes - MG',
  55313727 => 'Capela Nova - MG',
  55313728 => 'Buarque de Macedo - MG',
  55313731 => 'Congonhas - MG',
  55313732 => 'Congonhas - MG',
  55313733 => 'Joaquim Murtinho - MG',
  55313734 => 'Belo Vale - MG',
  55313735 => 'Jeceaba - MG',
  55313736 => 'Desterro de Entre Rios - MG',
  55313738 => 'São Brás do Suaçuí - MG',
  55313741 => 'Ouro Branco - MG',
  55313742 => 'Ouro Branco - MG',
  55313746 => 'Piranga - MG',
  55313749 => 'Ouro Branco - MG',
  55313751 => 'Entre Rios de Minas - MG',
  55313752 => 'Catas Altas da Noruega - MG',
  55313753 => 'Rio Espera - MG',
  55313754 => 'Lamim - MG',
  55313755 => 'Senhora de Oliveira - MG',
  55313757 => 'Itaverava - MG',
  55313761 => 'Conselheiro Lafaiete - MG',
  55313762 => 'Conselheiro Lafaiete - MG',
  55313763 => 'Conselheiro Lafaiete - MG',
  55313764 => 'Conselheiro Lafaiete - MG',
  55313768 => 'Contagem - MG',
  55313769 => 'Conselheiro Lafaiete - MG',
  55313771 => 'Sete Lagoas - MG',
  55313775 => 'Sete Lagoas - MG',
  55313779 => 'Sete Lagoas - MG',
  55313809 => 'Santa Bárbara - MG',
  55313817 => 'Ponte Nova - MG',
  55313819 => 'Ponte Nova - MG',
  55313821 => 'Ipatinga - MG',
  55313822 => 'Ipatinga - MG',
  55313823 => 'Ipatinga - MG',
  55313824 => 'Ipatinga - MG',
  55313825 => 'Ipatinga - MG',
  55313829 => 'Ipatinga - MG',
  55313831 => 'Itabira - MG',
  55313832 => 'Santa Bárbara - MG',
  55313833 => 'São Gonçalo do Rio Abaixo - MG',
  55313834 => 'Itabira - MG',
  55313835 => 'Itabira - MG',
  55313836 => 'Itambé do Mato Dentro - MG',
  55313837 => 'Barão de Cocais - MG',
  55313838 => 'Santa Maria de Itabira - MG',
  55313839 => 'Itabira - MG',
  55313841 => 'Coronel Fabriciano - MG',
  55313842 => 'Coronel Fabriciano - MG',
  55313843 => 'Antônio Dias - MG',
  55313844 => 'Cava Grande - MG',
  55313845 => 'Jaguaraçu - MG',
  55313846 => 'Coronel Fabriciano - MG',
  55313849 => 'Timóteo - MG',
  55313851 => 'João Monlevade - MG',
  55313852 => 'João Monlevade - MG',
  55313853 => 'Bela Vista de Minas - MG',
  55313854 => 'Rio Piracicaba - MG',
  55313855 => 'Alvinópolis - MG',
  55313856 => 'São Domingos do Prata - MG',
  55313857 => 'Dom Silvério - MG',
  55313858 => 'Dionísio - MG',
  55313859 => 'João Monlevade - MG',
  55313861 => 'Nova Era - MG',
  55313862 => 'Alvorada de Minas - MG',
  55313863 => 'Ferros - MG',
  55313864 => 'Carmésia - MG',
  55313865 => 'Coronel Fabriciano - MG',
  55313866 => 'Dom Joaquim - MG',
  55313867 => 'São Sebastião do Rio Preto - MG',
  55313868 => 'Conceição do Mato Dentro - MG',
  55313869 => 'Congonhas do Norte - MG',
  55313871 => 'Rio Casca - MG',
  55313872 => 'Abre Campo - MG',
  55313873 => 'Matipó - MG',
  55313875 => 'Santa Margarida - MG',
  55313876 => 'Urucânia - MG',
  55313877 => 'Jequeri - MG',
  55313878 => 'Contagem - MG',
  55313879 => 'Belo Horizonte - MG',
  55313881 => 'Ponte Nova - MG',
  55313883 => 'Rio Doce - MG',
  55313885 => 'Viçosa - MG',
  55313886 => 'Diogo de Vasconcelos - MG',
  55313887 => 'Acaiaca - MG',
  55313889 => 'Belo Horizonte - MG',
  55313891 => 'Viçosa - MG',
  55313892 => 'Viçosa - MG',
  55313893 => 'Porto Firme - MG',
  55313894 => 'Araponga - MG',
  55313895 => 'Teixeiras - MG',
  55313896 => 'Pedra do Anta - MG',
  55313897 => 'São Miguel do Anta - MG',
  55313898 => 'Cajuri - MG',
  55313899 => 'Viçosa - MG',
  55313911 => 'Contagem - MG',
  55313912 => 'Contagem - MG',
  55313913 => 'Contagem - MG',
  55313915 => 'Belo Horizonte - MG',
  55313916 => 'Belo Horizonte - MG',
  55313939 => 'Conselheiro Lafaiete - MG',
  55313956 => 'Contagem - MG',
  55314002 => 'Belo Horizonte - MG',
  55314003 => 'Belo Horizonte - MG',
  55314004 => 'Belo Horizonte - MG',
  55314007 => 'Belo Horizonte - MG',
  55314009 => 'Belo Horizonte - MG',
  55314062 => 'Belo Horizonte - MG',
  55314111 => 'Belo Horizonte - MG',
  55314113 => 'Sete Lagoas - MG',
  55314114 => 'Conselheiro Lafaiete - MG',
  55314115 => 'Lagoa Santa - MG',
  55314122 => 'Contagem - MG',
  55314133 => 'Belo Horizonte - MG',
  55314136 => 'Belo Horizonte - MG',
  55314138 => 'Vespasiano - MG',
  5532 => 'Minas Gerais',
  55322101 => 'Juiz de Fora - MG',
  55322102 => 'Juiz de Fora - MG',
  55322104 => 'Juiz de Fora - MG',
  55322152 => 'Juiz de Fora - MG',
  55323003 => 'Juiz de Fora - MG',
  55323015 => 'Juiz de Fora - MG',
  55323017 => 'Juiz de Fora - MG',
  55323021 => 'Ubá - MG',
  55323025 => 'Juiz de Fora - MG',
  55323026 => 'Juiz de Fora - MG',
  55323031 => 'Juiz de Fora - MG',
  55323032 => 'Juiz de Fora - MG',
  55323051 => 'Barbacena - MG',
  55323052 => 'Barbacena - MG',
  55323061 => 'Juiz de Fora - MG',
  55323083 => 'Juiz de Fora - MG',
  55323084 => 'Juiz de Fora - MG',
  55323201 => 'Cataguases - MG',
  55323202 => 'Cataguases - MG',
  55323211 => 'Juiz de Fora - MG',
  55323212 => 'Juiz de Fora - MG',
  55323213 => 'Juiz de Fora - MG',
  55323214 => 'Juiz de Fora - MG',
  55323215 => 'Juiz de Fora - MG',
  55323216 => 'Juiz de Fora - MG',
  55323217 => 'Juiz de Fora - MG',
  55323218 => 'Juiz de Fora - MG',
  55323222 => 'Juiz de Fora - MG',
  55323224 => 'Juiz de Fora - MG',
  55323228 => 'Juiz de Fora - MG',
  55323229 => 'Juiz de Fora - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Santos Dumont - MG',
  ******** => 'Santos Dumont - MG',
  ******** => 'Tabuleiro - MG',
  ******** => 'Piau - MG',
  ******** => 'Ewbank da Câmara - MG',
  ******** => 'Aracitaba - MG',
  ******** => 'Juiz de Fora - MG',
  ******** => 'Coronel Pacheco - MG',
  ******** => 'São João Nepomuceno - MG',
  ******** => 'Rochedo de Minas - MG',
  ******** => 'Maripá de Minas - MG',
  ******** => 'Guarará - MG',
  ******** => 'Descoberto - MG',
  ******** => 'São João Nepomuceno - MG',
  ******** => 'Bicas - MG',
  ******** => 'Simão Pereira - MG',
  ******** => 'Matias Barbosa - MG',
  ******** => 'Rio Novo - MG',
  ******** => 'Santana do Deserto - MG',
  ******** => 'Mar de Espanha - MG',
  ******** => 'Chácara - MG',
  ******** => 'Pequeri - MG',
  ******** => 'Lima Duarte - MG',
  ******** => 'Pedro Teixeira - MG',
  ******** => 'Rio Preto - MG',
  ******** => 'Belmiro Braga - MG',
  ******** => 'Chiador - MG',
  ******** => 'Santo Antônio do Aventureiro - MG',
  ******** => 'Senador Cortes - MG',
  ******** => 'Olaria - MG',
  ******** => 'Santa Rita de Jacutinga - MG',
  ******** => 'Bom Jardim de Minas - MG',
  55323293 => 'Liberdade - MG',
  55323294 => 'Bocaina de Minas - MG',
  55323295 => 'Passa-Vinte - MG',
  55323296 => 'Arantina - MG',
  55323311 => 'Juiz de Fora - MG',
  55323312 => 'Juiz de Fora - MG',
  55323313 => 'Juiz de Fora - MG',
  55323314 => 'Juiz de Fora - MG',
  55323322 => 'Nazareno - MG',
  55323323 => 'São João Del Rei - MG',
  55323330 => 'Correia de Almeida - MG',
  55323331 => 'Barbacena - MG',
  55323332 => 'Barbacena - MG',
  55323333 => 'Barbacena - MG',
  55323334 => 'Santana do Garambéu - MG',
  55323335 => 'Piedade do Rio Grande - MG',
  55323336 => 'Desterro do Melo - MG',
  55323337 => 'Mercês - MG',
  55323338 => 'Madre de Deus de Minas - MG',
  55323339 => 'Barbacena - MG',
  55323341 => 'Ressaquinha - MG',
  55323342 => 'Santa Rita de Ibitipoca - MG',
  55323343 => 'Senhora dos Remédios - MG',
  55323344 => 'Bias Fortes - MG',
  55323345 => 'Alto Rio Doce - MG',
  55323346 => 'Antônio Carlos - MG',
  55323347 => 'Ibertioga - MG',
  55323348 => 'Cipotânea - MG',
  55323351 => 'Barroso - MG',
  55323353 => 'Dores de Campos - MG',
  55323354 => 'Resende Costa - MG',
  55323355 => 'Tiradentes - MG',
  55323356 => 'Ritápolis - MG',
  55323357 => 'Coronel Xavier Chaves - MG',
  55323361 => 'Carandaí - MG',
  55323362 => 'Barbacena - MG',
  55323363 => 'Lagoa Dourada - MG',
  55323364 => 'Paiva - MG',
  55323365 => 'Santa Bárbara do Tugúrio - MG',
  55323366 => 'Oliveira Fortes - MG',
  55323367 => 'Alfredo Vasconcelos - MG',
  55323371 => 'São João Del Rei - MG',
  55323372 => 'São João Del Rei - MG',
  55323373 => 'São João Del Rei - MG',
  55323374 => 'Rio das Mortes - MG',
  55323375 => 'Conceição da Barra de Minas - MG',
  55323376 => 'São Tiago - MG',
  55323379 => 'São João Del Rei - MG',
  55323393 => 'Senhora das Dores - MG',
  55323401 => 'Leopoldina - MG',
  55323421 => 'Cataguases - MG',
  55323422 => 'Cataguases - MG',
  55323423 => 'Cataguases - MG',
  55323424 => 'Laranjal - MG',
  55323425 => 'Santana de Cataguases - MG',
  55323426 => 'Miraí - MG',
  55323429 => 'Cataguases - MG',
  55323441 => 'Leopoldina - MG',
  55323442 => 'Leopoldina - MG',
  55323444 => 'Recreio - MG',
  55323445 => 'Argirita - MG',
  55323446 => 'Palma - MG',
  55323447 => 'Leopoldina - MG',
  55323449 => 'Leopoldina - MG',
  55323451 => 'Astolfo Dutra - MG',
  55323452 => 'Itamarati de Minas - MG',
  55323453 => 'Dona Eusébia - MG',
  55323461 => 'Angustura - MG',
  55323462 => 'Além Paraíba - MG',
  55323463 => 'Volta Grande - MG',
  55323464 => 'Estrela Dalva - MG',
  55323465 => 'Pirapetinga - MG',
  55323466 => 'Além Paraíba - MG',
  55323511 => 'Muriaé - MG',
  55323512 => 'Juiz de Fora - MG',
  55323531 => 'Ubá - MG',
  55323532 => 'Ubá - MG',
  55323533 => 'Colônia Padre Damião - MG',
  55323534 => 'Brás Pires - MG',
  55323535 => 'Divinésia - MG',
  55323536 => 'Senador Firmino - MG',
  55323537 => 'Paula Cândido - MG',
  55323538 => 'Presidente Bernardes - MG',
  55323539 => 'Ubá - MG',
  55323541 => 'Ubá - MG',
  55323551 => 'Visconde do Rio Branco - MG',
  55323553 => 'Guiricema - MG',
  55323554 => 'Ervália - MG',
  55323555 => 'Coimbra - MG',
  55323556 => 'São Geraldo - MG',
  55323559 => 'Visconde do Rio Branco - MG',
  55323571 => 'Rio Pomba - MG',
  55323572 => 'Silveirânia - MG',
  55323573 => 'Piraúba - MG',
  55323574 => 'Tocantins - MG',
  55323575 => 'Guarani - MG',
  55323576 => 'Dores do Turvo - MG',
  55323577 => 'Rodeiro - MG',
  55323578 => 'Guidoval - MG',
  55323691 => 'Juiz de Fora - MG',
  55323693 => 'Barbacena - MG',
  55323694 => 'Leopoldina - MG',
  55323696 => 'Muriaé - MG',
  55323711 => 'Vermelho - MG',
  55323721 => 'Muriaé - MG',
  55323722 => 'Muriaé - MG',
  55323723 => 'Rosário da Limeira - MG',
  55323724 => 'Eugenópolis - MG',
  55323725 => 'Antônio Prado de Minas - MG',
  55323726 => 'Patrocínio do Muriaé - MG',
  55323727 => 'Barão de Monte Alto - MG',
  55323728 => 'Muriaé - MG',
  55323729 => 'Muriaé - MG',
  55323741 => 'Carangola - MG',
  55323742 => 'Fervedouro - MG',
  55323743 => 'Divino - MG',
  55323745 => 'Caiana - MG',
  55323746 => 'Espera Feliz - MG',
  55323747 => 'Alto Caparaó - MG',
  55323748 => 'Pedra Dourada - MG',
  55323749 => 'Faria Lemos - MG',
  55323751 => 'Tombos - MG',
  55323753 => 'Miradouro - MG',
  55323754 => 'São Francisco do Glória - MG',
  55323755 => 'Vieiras - MG',
  55324009 => 'Juiz de Fora - MG',
  55324101 => 'Barbacena - MG',
  55324141 => 'Juiz de Fora - MG',
  5533 => 'Minas Gerais',
  55332101 => 'Governador Valadares - MG',
  55332102 => 'Governador Valadares - MG',
  55333014 => 'Governador Valadares - MG',
  55333021 => 'Governador Valadares - MG',
  55333022 => 'Governador Valadares - MG',
  55333025 => 'Governador Valadares - MG',
  55333062 => 'Governador Valadares - MG',
  55333082 => 'Governador Valadares - MG',
  55333084 => 'Governador Valadares - MG',
  55333087 => 'Teófilo Otoni - MG',
  55333089 => 'Governador Valadares - MG',
  55333202 => 'Governador Valadares - MG',
  55333203 => 'Governador Valadares - MG',
  55333212 => 'Governador Valadares - MG',
  55333213 => 'Governador Valadares - MG',
  55333215 => 'Governador Valadares - MG',
  55333221 => 'Governador Valadares - MG',
  55333225 => 'Governador Valadares - MG',
  55333231 => 'Itanhomi - MG',
  55333232 => 'Sobrália - MG',
  55333233 => 'Tarumirim - MG',
  55333234 => 'Engenheiro Caldas - MG',
  55333235 => 'Tumiritinga - MG',
  55333236 => 'Alpercata - MG',
  55333237 => 'Fernandes Tourinho - MG',
  55333238 => 'São Geraldo da Piedade - MG',
  55333241 => 'Mantena - MG',
  55333242 => 'São João do Manteninha - MG',
  55333243 => 'Central de Minas - MG',
  55333244 => 'Galiléia - MG',
  55333245 => 'Divino das Laranjeiras - MG',
  55333246 => 'Mendes Pimentel - MG',
  55333247 => 'Itabirinha - MG',
  55333251 => 'Santana do Paraíso - MG',
  55333252 => 'Joanésia - MG',
  55333253 => 'Belo Oriente - MG',
  55333254 => 'Perpétuo Socorro - MG',
  55333261 => 'Conselheiro Pena - MG',
  55333262 => 'Goiabeira - MG',
  55333263 => 'Resplendor - MG',
  55333265 => 'Santa Rita do Itueto - MG',
  55333266 => 'Quatituba - MG',
  55333267 => 'Aimorés - MG',
  55333268 => 'Aimorés - MG',
  55333271 => 'Governador Valadares - MG',
  55333272 => 'Governador Valadares - MG',
  55333274 => 'Governador Valadares - MG',
  55333275 => 'Governador Valadares - MG',
  55333276 => 'Governador Valadares - MG',
  55333277 => 'Governador Valadares - MG',
  55333278 => 'Governador Valadares - MG',
  55333279 => 'Governador Valadares - MG',
  55333284 => 'Frei Inocêncio - MG',
  55333291 => 'Coroaci - MG',
  55333292 => 'Marilac - MG',
  55333293 => 'São José da Safira - MG',
  55333294 => 'Nacip Raydan - MG',
  55333295 => 'Virgolândia - MG',
  55333296 => 'Sardoá - MG',
  55333297 => 'Santa Efigênia de Minas - MG',
  55333298 => 'Açucena - MG',
  55333299 => 'Açucena - MG',
  55333312 => 'Mutum - MG',
  55333313 => 'Inhapim - MG',
  55333314 => 'Ipanema - MG',
  55333315 => 'Inhapim - MG',
  55333316 => 'Pocrane - MG',
  55333317 => 'Conceição de Ipanema - MG',
  55333318 => 'Caratinga - MG',
  55333321 => 'Caratinga - MG',
  55333322 => 'Caratinga - MG',
  55333323 => 'Ubaporanga - MG',
  55333324 => 'Vargem Alegre - MG',
  55333325 => 'Imbé de Minas - MG',
  55333326 => 'Santa Bárbara do Leste - MG',
  55333327 => 'Ipaba - MG',
  55333328 => 'Alvarenga - MG',
  55333329 => 'Caratinga - MG',
  55333331 => 'Manhuaçu - MG',
  55333332 => 'Manhuaçu - MG',
  55333333 => 'Realeza - MG',
  55333334 => 'Manhuaçu - MG',
  55333335 => 'São José do Mantimento - MG',
  55333336 => 'Simonésia - MG',
  55333339 => 'Manhuaçu - MG',
  55333340 => 'Governador Valadares - MG',
  55333341 => 'Manhumirim - MG',
  55333342 => 'Durandé - MG',
  55333343 => 'Alto Jequitibá - MG',
  55333344 => 'Lajinha - MG',
  55333345 => 'Chalé - MG',
  55333351 => 'Raul Soares - MG',
  55333352 => 'São Pedro dos Ferros - MG',
  55333353 => 'Pingo-D\'Água - MG',
  55333354 => 'Bom Jesus do Galho - MG',
  55333355 => 'Iapu - MG',
  55333356 => 'São João do Oriente - MG',
  55333357 => 'Dom Cavati - MG',
  55333373 => 'Santana do Manhuaçu - MG',
  55333377 => 'São João do Manhuaçu - MG',
  55333411 => 'Peçanha - MG',
  55333412 => 'São João Evangelista - MG',
  55333413 => 'Paulistas - MG',
  55333414 => 'Divinolândia de Minas - MG',
  55333415 => 'Gonzaga - MG',
  55333416 => 'Virginópolis - MG',
  55333421 => 'Guanhães - MG',
  55333423 => 'Sabinópolis - MG',
  55333424 => 'Senhora do Porto - MG',
  55333425 => 'Braúnas - MG',
  55333426 => 'Dores de Guanhães - MG',
  55333427 => 'Materlândia - MG',
  55333428 => 'Santo Antônio do Itambé - MG',
  55333431 => 'Santa Maria do Suaçuí - MG',
  55333432 => 'São Sebastião do Maranhão - MG',
  55333433 => 'São José do Jacuri - MG',
  55333434 => 'São Pedro do Suaçuí - MG',
  55333435 => 'Coluna - MG',
  55333436 => 'Rio Vermelho - MG',
  55333508 => 'Governador Valadares - MG',
  55333511 => 'Itambacuri - MG',
  55333512 => 'Frei Gaspar - MG',
  55333513 => 'Campanário - MG',
  55333514 => 'Malacacheta - MG',
  55333515 => 'Água Boa - MG',
  55333516 => 'Capelinha - MG',
  55333521 => 'Teófilo Otoni - MG',
  55333522 => 'Teófilo Otoni - MG',
  55333523 => 'Teófilo Otoni - MG',
  55333524 => 'Ladainha - MG',
  55333525 => 'Poté - MG',
  55333526 => 'Ataléia - MG',
  55333527 => 'Ouro Verde de Minas - MG',
  55333528 => 'Teófilo Otoni - MG',
  55333529 => 'Teófilo Otoni - MG',
  55333531 => 'Caraí - MG',
  55333532 => 'Itaipé - MG',
  55333533 => 'Novo Cruzeiro - MG',
  55333534 => 'Padre Paraíso - MG',
  55333535 => 'Pavão - MG',
  55333536 => 'Teófilo Otoni - MG',
  55333581 => 'Nova Módica - MG',
  55333582 => 'São José do Divino - MG',
  55333583 => 'Pescador - MG',
  55333611 => 'Águas Formosas - MG',
  55333621 => 'Nanuque - MG',
  55333622 => 'Nanuque - MG',
  55333623 => 'Fronteira dos Vales - MG',
  55333624 => 'Carlos Chagas - MG',
  55333625 => 'Serra dos Aimorés - MG',
  55333626 => 'Santa Helena de Minas - MG',
  55333627 => 'Machacalis - MG',
  55333628 => 'Umburatiba - MG',
  55333721 => 'Almenara - MG',
  55333722 => 'Mata Verde - MG',
  55333723 => 'Jacinto - MG',
  55333724 => 'Divisópolis - MG',
  55333725 => 'Salto da Divisa - MG',
  55333726 => 'Jordânia - MG',
  55333727 => 'Santa Maria do Salto - MG',
  55333728 => 'Bandeira - MG',
  55333731 => 'Araçuaí - MG',
  55333732 => 'Comercinho - MG',
  55333733 => 'Itinga - MG',
  55333734 => 'Itaobim - MG',
  55333735 => 'Coronel Murta - MG',
  55333736 => 'Virgem da Lapa - MG',
  55333737 => 'Berilo - MG',
  55333738 => 'Francisco Badaró - MG',
  55333739 => 'Chapada do Norte - MG',
  55333741 => 'Jequitinhonha - MG',
  55333743 => 'Felisburgo - MG',
  55333744 => 'Rio do Prado - MG',
  55333745 => 'Joaíma - MG',
  55333746 => 'Rubim - MG',
  55333747 => 'Santo Antônio do Jacinto - MG',
  55333751 => 'Pedra Azul - MG',
  55333753 => 'Medina - MG',
  55333754 => 'Cachoeira de Pajeú - MG',
  55333755 => 'Águas Vermelhas - MG',
  55333764 => 'Minas Novas - MG',
  55333825 => 'Belo Oriente - MG',
  55334141 => 'Governador Valadares - MG',
  5534 => 'Minas Gerais',
  55342102 => 'Uberlândia - MG',
  55342106 => 'Patos de Minas - MG',
  55342108 => 'Araguari - MG',
  55342109 => 'Araguari - MG',
  55343003 => 'Uberlândia - MG',
  55343061 => 'Patos de Minas - MG',
  55343071 => 'Uberaba - MG',
  55343074 => 'Uberaba - MG',
  55343088 => 'Uberlândia - MG',
  55343131 => 'Uberlândia - MG',
  55343201 => 'Araxá - MG',
  55343212 => 'Uberlândia - MG',
  55343213 => 'Uberlândia - MG',
  55343215 => 'Uberlândia - MG',
  55343221 => 'Uberlândia - MG',
  55343228 => 'Uberlândia - MG',
  55343232 => 'Uberlândia - MG',
  55343235 => 'Uberlândia - MG',
  55343236 => 'Uberlândia - MG',
  55343241 => 'Araguari - MG',
  55343242 => 'Araguari - MG',
  55343243 => 'Amanhece - MG',
  55343244 => 'Uberlândia - MG',
  55343245 => 'Indianópolis - MG',
  55343246 => 'Araguari - MG',
  55343248 => 'Cascalho Rico - MG',
  55343249 => 'Araguari - MG',
  55343251 => 'Santa Vitória - MG',
  55343252 => 'Ipiaçu - MG',
  55343256 => 'Uberlândia - MG',
  55343257 => 'Uberlândia - MG',
  55343259 => 'Uberlândia - MG',
  55343261 => 'Ituiutaba - MG',
  55343262 => 'Ituiutaba - MG',
  55343263 => 'Capinópolis - MG',
  55343264 => 'Gurinhatã - MG',
  55343265 => 'Cachoeira Dourada - MG',
  55343266 => 'Canápolis - MG',
  55343267 => 'Centralina - MG',
  55343268 => 'Ituiutaba - MG',
  55343269 => 'Ituiutaba - MG',
  55343271 => 'Ituiutaba - MG',
  55343281 => 'Tupaciguara - MG',
  55343283 => 'Monte Alegre de Minas - MG',
  55343284 => 'Araporã - MG',
  55343292 => 'Uberlândia - MG',
  55343301 => 'Uberlândia - MG',
  55343304 => 'Uberlândia - MG',
  55343318 => 'Uberaba - MG',
  55343322 => 'Uberaba - MG',
  55343323 => 'Veríssimo - MG',
  55343324 => 'Água Comprida - MG',
  55343327 => 'Conceição das Alagoas - MG',
  55343328 => 'Campo Florido - MG',
  55343332 => 'Uberaba - MG',
  55343333 => 'Uberaba - MG',
  55343336 => 'Uberaba - MG',
  55343338 => 'Uberaba - MG',
  55343351 => 'Sacramento - MG',
  55343352 => 'Uberaba - MG',
  55343353 => 'Conquista - MG',
  55343354 => 'Santa Juliana - MG',
  55343355 => 'Pedrinópolis - MG',
  55343356 => 'Nova Ponte - MG',
  55343359 => 'Uberaba - MG',
  55343411 => 'Iturama - MG',
  55343412 => 'Campina Verde - MG',
  55343413 => 'São Francisco de Sales - MG',
  55343414 => 'Contagem - MG',
  55343415 => 'Iturama - MG',
  55343421 => 'Frutal - MG',
  55343423 => 'Frutal - MG',
  55343424 => 'Itapagipe - MG',
  55343425 => 'Frutal - MG',
  55343426 => 'Pirajuba - MG',
  55343427 => 'Planura - MG',
  55343428 => 'Fronteira - MG',
  55343429 => 'Frutal - MG',
  55343431 => 'Prata - MG',
  55343453 => 'Limeira do Oeste - MG',
  55343454 => 'Carneirinho - MG',
  55343455 => 'Carneirinho - MG',
  55343456 => 'União de Minas - MG',
  55343457 => 'Carneirinho - MG',
  55343459 => 'Frutal - MG',
  55343511 => 'Patrocínio - MG',
  55343512 => 'Araguari - MG',
  55343513 => 'Araguari - MG',
  55343514 => 'Cachoeira Dourada - MG',
  55343515 => 'Patrocínio - MG',
  55343611 => 'Araxá - MG',
  55343612 => 'Araxá - MG',
  55343614 => 'Perdizes - MG',
  55343631 => 'Ibiá - MG',
  55343633 => 'Tapira - MG',
  55343637 => 'Pratinha - MG',
  55343654 => 'Santa Rosa da Serra - MG',
  55343661 => 'Araxá - MG',
  55343662 => 'Araxá - MG',
  55343663 => 'Perdizes - MG',
  55343664 => 'Araxá - MG',
  55343669 => 'Araxá - MG',
  55343671 => 'São Gotardo - MG',
  55343674 => 'Matutina - MG',
  55343690 => 'Araguari - MG',
  55343691 => 'Araxá - MG',
  55343799 => 'Araguari - MG',
  55343811 => 'Presidente Olegário - MG',
  55343812 => 'Lagamar - MG',
  55343813 => 'Vazante - MG',
  55343814 => 'Patos de Minas - MG',
  55343816 => 'Lagoa Grande - MG',
  55343817 => 'Coromandel - MG',
  55343818 => 'Patos de Minas - MG',
  55343819 => 'Monte Carmelo - MG',
  55343820 => 'Patos de Minas - MG',
  55343821 => 'Patos de Minas - MG',
  55343823 => 'Patos de Minas - MG',
  55343824 => 'Lagoa Formosa - MG',
  55343826 => 'Patos de Minas - MG',
  55343831 => 'Patrocínio - MG',
  55343832 => 'Patrocínio - MG',
  55343833 => 'Serra do Salitre - MG',
  55343834 => 'Guimarânia - MG',
  55343835 => 'Cruzeiro da Fortaleza - MG',
  55343836 => 'São João da Serra Negra - MG',
  55343839 => 'Patrocínio - MG',
  55343841 => 'Coromandel - MG',
  55343842 => 'Monte Carmelo - MG',
  55343843 => 'Estrela do Sul - MG',
  55343844 => 'Grupiara - MG',
  55343845 => 'Iraí de Minas - MG',
  55343846 => 'Douradoquara - MG',
  55343847 => 'Abadia dos Dourados - MG',
  55343848 => 'Romaria - MG',
  55343849 => 'Monte Carmelo - MG',
  55343851 => 'Carmo do Paranaíba - MG',
  55343853 => 'Tiros - MG',
  55343855 => 'Rio Paranaíba - MG',
  55343856 => 'Arapuá - MG',
  55343859 => 'Patos de Minas - MG',
  55343972 => 'Uberaba - MG',
  55344004 => 'Uberlândia - MG',
  55344009 => 'Uberlândia - MG',
  5535 => 'Minas Gerais',
  55352101 => 'Poços de Caldas - MG',
  55352102 => 'Pouso Alegre - MG',
  55352103 => 'Pouso Alegre - MG',
  55352105 => 'Varginha - MG',
  55352106 => 'Varginha - MG',
  55352107 => 'Poços de Caldas - MG',
  55353011 => 'Alfenas - MG',
  55353012 => 'Itajubá - MG',
  55353013 => 'Lavras - MG',
  55353015 => 'Varginha - MG',
  55353021 => 'Passos - MG',
  55353022 => 'Pouso Alegre - MG',
  55353064 => 'Poços de Caldas - MG',
  55353066 => 'Poços de Caldas - MG',
  55353067 => 'Varginha - MG',
  55353068 => 'Varginha - MG',
  55353100 => 'Extrema - MG',
  55353211 => 'Passos - MG',
  55353212 => 'Varginha - MG',
  55353214 => 'Varginha - MG',
  55353221 => 'Varginha - MG',
  55353222 => 'Varginha - MG',
  55353223 => 'Varginha - MG',
  55353225 => 'Carmo da Cachoeira - MG',
  55353226 => 'Luminárias - MG',
  55353229 => 'Varginha - MG',
  55353231 => 'Três Corações - MG',
  55353232 => 'Três Corações - MG',
  55353233 => 'Três Corações - MG',
  55353234 => 'Três Corações - MG',
  55353235 => 'Três Corações - MG',
  55353236 => 'São Bento Abade - MG',
  55353237 => 'São Thomé das Letras - MG',
  55353239 => 'Três Corações - MG',
  55353241 => 'São Gonçalo do Sapucaí - MG',
  55353242 => 'Turvolândia - MG',
  55353244 => 'Cordislândia - MG',
  55353251 => 'Cambuquira - MG',
  55353261 => 'Campanha - MG',
  55353263 => 'Monsenhor Paulo - MG',
  55353264 => 'Elói Mendes - MG',
  55353265 => 'Três Pontas - MG',
  55353266 => 'Três Pontas - MG',
  55353267 => 'Paraguaçu - MG',
  55353271 => 'Lambari - MG',
  55353273 => 'Jesuânia - MG',
  55353274 => 'Olímpio Noronha - MG',
  55353281 => 'Cristina - MG',
  55353282 => 'Carvalhópolis - MG',
  55353283 => 'Poço Fundo - MG',
  55353284 => 'Serrania - MG',
  55353286 => 'Divisa Nova - MG',
  55353291 => 'Alfenas - MG',
  55353292 => 'Alfenas - MG',
  55353293 => 'Areado - MG',
  55353294 => 'Alterosa - MG',
  55353295 => 'Machado - MG',
  55353296 => 'Fama - MG',
  55353297 => 'Alfenas - MG',
  55353298 => 'Machado - MG',
  55353299 => 'Alfenas - MG',
  55353301 => 'Poços de Caldas - MG',
  55353322 => 'Seritinga - MG',
  55353323 => 'São Vicente de Minas - MG',
  55353325 => 'Andrelândia - MG',
  55353326 => 'Minduri - MG',
  55353327 => 'Carrancas - MG',
  55353331 => 'São Lourenço - MG',
  55353332 => 'São Lourenço - MG',
  55353333 => 'Soledade de Minas - MG',
  55353334 => 'Carmo de Minas - MG',
  55353335 => 'Conceição do Rio Verde - MG',
  55353339 => 'São Lourenço - MG',
  55353341 => 'Caxambu - MG',
  55353343 => 'Baependi - MG',
  55353344 => 'Aiuruoca - MG',
  55353345 => 'Carvalhos - MG',
  55353346 => 'Cruzília - MG',
  55353361 => 'Itanhandu - MG',
  55353363 => 'Itamonte - MG',
  55353364 => 'Pouso Alto - MG',
  55353365 => 'São Sebastião do Rio Verde - MG',
  55353366 => 'Alagoa - MG',
  55353371 => 'Passa Quatro - MG',
  55353373 => 'Virgínia - MG',
  55353375 => 'Dom Viçoso - MG',
  55353409 => 'Lavras - MG',
  55353411 => 'São Sebastião do Paraíso - MG',
  55353413 => 'Passos - MG',
  55353421 => 'Pouso Alegre - MG',
  55353422 => 'Pouso Alegre - MG',
  55353423 => 'Pouso Alegre - MG',
  55353424 => 'Congonhal - MG',
  55353425 => 'Pouso Alegre - MG',
  55353426 => 'Senador José Bento - MG',
  55353427 => 'Pouso Alegre - MG',
  55353429 => 'Pouso Alegre - MG',
  55353431 => 'Cambuí - MG',
  55353432 => 'Córrego do Bom Jesus - MG',
  55353433 => 'Camanducaia - MG',
  55353434 => 'Itapeva - MG',
  55353435 => 'Extrema - MG',
  55353436 => 'Toledo - MG',
  55353437 => 'Senador Amaral - MG',
  55353438 => 'Camanducaia - MG',
  55353441 => 'Ouro Fino - MG',
  55353442 => 'Bueno Brandão - MG',
  55353443 => 'Jacutinga - MG',
  55353444 => 'Jacutinga - MG',
  55353445 => 'Borda da Mata - MG',
  55353446 => 'Albertina - MG',
  55353449 => 'Pouso Alegre - MG',
  55353451 => 'Silvianópolis - MG',
  55353452 => 'Careaçu - MG',
  55353453 => 'São Sebastião da Bela Vista - MG',
  55353454 => 'Espírito Santo do Dourado - MG',
  55353455 => 'São João da Mata - MG',
  55353456 => 'Natércia - MG',
  55353457 => 'Heliodora - MG',
  55353461 => 'Bom Repouso - MG',
  55353462 => 'Estiva - MG',
  55353463 => 'Bueno Brandão - MG',
  55353464 => 'Inconfidentes - MG',
  55353465 => 'Monte Sião - MG',
  55353466 => 'Munhoz - MG',
  55353471 => 'Santa Rita do Sapucaí - MG',
  55353472 => 'Cachoeira de Minas - MG',
  55353473 => 'Santa Rita do Sapucaí - MG',
  55353521 => 'Passos - MG',
  55353522 => 'Passos - MG',
  55353523 => 'Alpinópolis - MG',
  55353524 => 'São João Batista do Glória - MG',
  55353525 => 'Delfinópolis - MG',
  55353526 => 'Passos - MG',
  55353527 => 'Bom Jesus dos Campos - MG',
  55353529 => 'Passos - MG',
  55353531 => 'São Sebastião do Paraíso - MG',
  55353532 => 'São Sebastião do Paraíso - MG',
  55353533 => 'Pratápolis - MG',
  55353534 => 'Itamogi - MG',
  55353535 => 'São Tomás de Aquino - MG',
  55353536 => 'Itaú de Minas - MG',
  55353537 => 'Fortaleza de Minas - MG',
  55353539 => 'São Sebastião do Paraíso - MG',
  55353541 => 'Cássia - MG',
  55353543 => 'Capetinga - MG',
  55353544 => 'Ibiraci - MG',
  55353545 => 'Ibiraci - MG',
  55353551 => 'Guaxupé - MG',
  55353552 => 'Guaxupé - MG',
  55353553 => 'Juruaia - MG',
  55353554 => 'São Pedro da União - MG',
  55353555 => 'Guaranésia - MG',
  55353556 => 'Arceburgo - MG',
  55353558 => 'São Sebastião do Paraíso - MG',
  55353559 => 'Guaxupé - MG',
  55353561 => 'Carmo do Rio Claro - MG',
  55353562 => 'Nova Resende - MG',
  55353563 => 'Bom Jesus da Penha - MG',
  55353564 => 'Conceição da Aparecida - MG',
  55353571 => 'Muzambinho - MG',
  55353573 => 'Monte Belo - MG',
  55353591 => 'Monte Santo de Minas - MG',
  55353593 => 'Jacuí - MG',
  55353621 => 'Itajubá - MG',
  55353622 => 'Itajubá - MG',
  55353623 => 'Itajubá - MG',
  55353624 => 'Delfim Moreira - MG',
  55353625 => 'Marmelópolis - MG',
  55353626 => 'Wenceslau Braz - MG',
  55353629 => 'Itajubá - MG',
  55353641 => 'Brasópolis - MG',
  55353643 => 'Piranguçu - MG',
  55353644 => 'Piranguinho - MG',
  55353645 => 'São José do Alegre - MG',
  55353651 => 'Paraisópolis - MG',
  55353653 => 'Conceição dos Ouros - MG',
  55353654 => 'Gonçalves - MG',
  55353655 => 'Sapucaí-Mirim - MG',
  55353656 => 'Consolação - MG',
  55353662 => 'Maria da Fé - MG',
  55353663 => 'Pedralva - MG',
  55353664 => 'Conceição das Pedras - MG',
  55353690 => 'Varginha - MG',
  55353691 => 'Três Corações - MG',
  55353692 => 'Itajubá - MG',
  55353693 => 'Pouso Alegre - MG',
  55353694 => 'Lavras - MG',
  55353695 => 'São Lourenço - MG',
  55353696 => 'Guaxupé - MG',
  55353697 => 'Poços de Caldas - MG',
  55353698 => 'Alfenas - MG',
  55353701 => 'Alfenas - MG',
  55353712 => 'Poços de Caldas - MG',
  55353713 => 'Poços de Caldas - MG',
  55353714 => 'Poços de Caldas - MG',
  55353715 => 'Poços de Caldas - MG',
  55353716 => 'Poços de Caldas - MG',
  55353721 => 'Poços de Caldas - MG',
  55353722 => 'Poços de Caldas - MG',
  55353729 => 'Poços de Caldas - MG',
  55353731 => 'Andradas - MG',
  55353732 => 'Ipuiúna - MG',
  55353733 => 'Ibitiúra de Minas - MG',
  55353734 => 'Santa Rita de Caldas - MG',
  55353735 => 'Caldas - MG',
  55353736 => 'Cabo Verde - MG',
  55353737 => 'São Pedro de Caldas - MG',
  55353739 => 'Andradas - MG',
  55353741 => 'Botelhos - MG',
  55353742 => 'Bandeira do Sul - MG',
  55353743 => 'Campestre - MG',
  55353798 => 'Areado - MG',
  55353799 => 'Caldas - MG',
  55353821 => 'Lavras - MG',
  55353822 => 'Lavras - MG',
  55353823 => 'Itumirim - MG',
  55353824 => 'Ingaí - MG',
  55353825 => 'Itutinga - MG',
  55353826 => 'Lavras - MG',
  55353829 => 'Lavras - MG',
  55353831 => 'Campo Belo - MG',
  55353832 => 'Campo Belo - MG',
  55353833 => 'Candeias - MG',
  55353834 => 'Aguanil - MG',
  55353835 => 'Cristais - MG',
  55353841 => 'Bom Sucesso - MG',
  55353842 => 'Nazareno - MG',
  55353843 => 'Ijaci - MG',
  55353844 => 'Ibituruna - MG',
  55353851 => 'Boa Esperança - MG',
  55353853 => 'Campos Gerais - MG',
  55353854 => 'Ilicínea - MG',
  55353855 => 'Coqueiral - MG',
  55353856 => 'Guapé - MG',
  55353857 => 'Campo do Meio - MG',
  55353858 => 'Santana da Vargem - MG',
  55353861 => 'Nepomuceno - MG',
  55353863 => 'Santo Antônio do Amparo - MG',
  55353864 => 'Perdões - MG',
  55353865 => 'Cana Verde - MG',
  55353866 => 'Santana do Jacaré - MG',
  55353867 => 'Ribeirão Vermelho - MG',
  55354101 => 'Varginha - MG',
  55354102 => 'Pouso Alegre - MG',
  55354103 => 'Passos - MG',
  55354104 => 'Lavras - MG',
  55354141 => 'Poços de Caldas - MG',
  5537 => 'Minas Gerais',
  55372101 => 'Divinópolis - MG',
  55372102 => 'Divinópolis - MG',
  55373015 => 'Divinópolis - MG',
  55373016 => 'Divinópolis - MG',
  55373071 => 'Divinópolis - MG',
  55373073 => 'Itaúna - MG',
  55373201 => 'Itaúna - MG',
  55373212 => 'Divinópolis - MG',
  55373213 => 'Divinópolis - MG',
  55373214 => 'Divinópolis - MG',
  55373215 => 'Divinópolis - MG',
  55373216 => 'Divinópolis - MG',
  55373221 => 'Divinópolis - MG',
  55373222 => 'Divinópolis - MG',
  55373225 => 'Nova Serrana - MG',
  55373226 => 'Nova Serrana - MG',
  55373227 => 'Nova Serrana - MG',
  55373228 => 'Nova Serrana - MG',
  55373229 => 'Divinópolis - MG',
  55373231 => 'Pará de Minas - MG',
  55373232 => 'Pará de Minas - MG',
  55373233 => 'Pará de Minas - MG',
  55373234 => 'São Gonçalo do Pará - MG',
  55373235 => 'Pará de Minas - MG',
  55373238 => 'Pará de Minas - MG',
  55373241 => 'Itaúna - MG',
  55373242 => 'Itaúna - MG',
  55373243 => 'Itaúna - MG',
  55373244 => 'Carmo do Cajuru - MG',
  55373246 => 'Igaratinga - MG',
  55373247 => 'Igaratinga - MG',
  55373249 => 'Itaúna - MG',
  55373258 => 'Pitangui - MG',
  55373259 => 'Pitangui - MG',
  55373261 => 'Lagoa da Prata - MG',
  55373262 => 'Lagoa da Prata - MG',
  55373271 => 'Pitangui - MG',
  55373272 => 'Maravilhas - MG',
  55373273 => 'Onça de Pitangui - MG',
  55373274 => 'Papagaios - MG',
  55373275 => 'São José da Varginha - MG',
  55373276 => 'Conceição do Pará - MG',
  55373277 => 'Leandro Ferreira - MG',
  55373278 => 'Pequi - MG',
  55373281 => 'Santo Antônio do Monte - MG',
  55373286 => 'São Sebastião do Oeste - MG',
  55373287 => 'Perdigão - MG',
  55373288 => 'Araújos - MG',
  55373301 => 'Divinópolis - MG',
  55373321 => 'Formiga - MG',
  55373322 => 'Formiga - MG',
  55373323 => 'Pains - MG',
  55373324 => 'Pimenta - MG',
  55373329 => 'Formiga - MG',
  55373331 => 'Oliveira - MG',
  55373332 => 'São Francisco de Paula - MG',
  55373333 => 'Carmópolis de Minas - MG',
  55373334 => 'Piracema - MG',
  55373335 => 'Passa Tempo - MG',
  55373341 => 'Itapecerica - MG',
  55373343 => 'Camacho - MG',
  55373344 => 'Pedra do Indaiá - MG',
  55373351 => 'Arcos - MG',
  55373352 => 'Arcos - MG',
  55373353 => 'Iguatama - MG',
  55373354 => 'Japaraíba - MG',
  55373355 => 'Doresópolis - MG',
  55373359 => 'Arcos - MG',
  55373361 => 'Oliveira - MG',
  55373371 => 'Piumhi - MG',
  55373373 => 'Capitólio - MG',
  55373381 => 'Cláudio - MG',
  55373383 => 'Carmo da Mata - MG',
  55373384 => 'Itaguara - MG',
  55373402 => 'Itaúna - MG',
  55373405 => 'Arcos - MG',
  55373421 => 'Luz - MG',
  55373423 => 'Tapiraí - MG',
  55373424 => 'Córrego Danta - MG',
  55373425 => 'Luz - MG',
  55373426 => 'Campos Altos - MG',
  55373431 => 'Bambuí - MG',
  55373433 => 'São Roque de Minas - MG',
  55373434 => 'Medeiros - MG',
  55373435 => 'Vargem Bonita - MG',
  55373511 => 'Divinópolis - MG',
  55373512 => 'Divinópolis - MG',
  55373513 => 'Pompéu - MG',
  55373514 => 'Abaeté - MG',
  55373521 => 'Bom Despacho - MG',
  55373522 => 'Bom Despacho - MG',
  55373523 => 'Pompéu - MG',
  55373524 => 'Martinho Campos - MG',
  55373525 => 'Moema - MG',
  55373541 => 'Abaeté - MG',
  55373543 => 'Quartel Geral - MG',
  55373544 => 'Cedro do Abaeté - MG',
  55373545 => 'Paineiras - MG',
  55373546 => 'Biquinhas - MG',
  55373551 => 'Dores do Indaiá - MG',
  55373553 => 'Estrela do Indaiá - MG',
  55373690 => 'Divinópolis - MG',
  55373691 => 'Divinópolis - MG',
  55373755 => 'Morada Nova de Minas - MG',
  55374101 => 'Itaúna - MG',
  55374141 => 'Divinópolis - MG',
  5538 => 'Minas Gerais',
  55382101 => 'Montes Claros - MG',
  55382102 => 'Unaí - MG',
  55382103 => 'Montes Claros - MG',
  55382104 => 'Montes Claros - MG',
  55383014 => 'Montes Claros - MG',
  55383081 => 'Montes Claros - MG',
  55383083 => 'Montes Claros - MG',
  55383084 => 'Montes Claros - MG',
  55383201 => 'Montes Claros - MG',
  55383214 => 'Montes Claros - MG',
  55383216 => 'Montes Claros - MG',
  55383217 => 'Montes Claros - MG',
  55383218 => 'Montes Claros - MG',
  55383221 => 'Montes Claros - MG',
  55383222 => 'Montes Claros - MG',
  55383223 => 'Montes Claros - MG',
  55383226 => 'Coração de Jesus - MG',
  55383227 => 'Brasília de Minas - MG',
  55383228 => 'Coração de Jesus - MG',
  55383231 => 'Brasília de Minas - MG',
  55383232 => 'Cristália - MG',
  55383233 => 'Francisco Sá - MG',
  55383234 => 'São João da Ponte - MG',
  55383235 => 'Capitão Enéas - MG',
  55383236 => 'Juramento - MG',
  55383237 => 'Claro dos Poções - MG',
  55383238 => 'Grão Mogol - MG',
  55383239 => 'Mirabela - MG',
  55383251 => 'Bocaiúva - MG',
  55383252 => 'Engenheiro Dolabela - MG',
  55383253 => 'Engenheiro Navarro - MG',
  55383254 => 'Itacambira - MG',
  55383255 => 'Botumirim - MG',
  55383311 => 'Paracatu - MG',
  55383321 => 'Montes Claros - MG',
  55383504 => 'Paracatu - MG',
  55383505 => 'Unaí - MG',
  55383506 => 'Buritis - MG',
  55383521 => 'Itamarandiba - MG',
  55383523 => 'Felício dos Santos - MG',
  55383525 => 'Senador Modestino Gonçalves - MG',
  55383526 => 'Carbonita - MG',
  55383527 => 'Turmalina - MG',
  55383531 => 'Diamantina - MG',
  55383532 => 'Diamantina - MG',
  55383533 => 'Couto de Magalhães de Minas - MG',
  55383534 => 'Diamantina - MG',
  55383535 => 'Datas - MG',
  55383541 => 'Serro - MG',
  55383543 => 'Gouveia - MG',
  55383545 => 'Presidente Kubitschek - MG',
  55383546 => 'São Gonçalo do Rio Preto - MG',
  55383547 => 'Serra Azul de Minas - MG',
  55383561 => 'João Pinheiro - MG',
  55383562 => 'Brasilândia de Minas - MG',
  55383563 => 'São Gonçalo do Abaeté - MG',
  55383564 => 'Ruralminas I - MG',
  55383567 => 'Varjão de Minas - MG',
  55383612 => 'Montalvânia - MG',
  55383613 => 'Itacarambi - MG',
  55383614 => 'Montalvânia - MG',
  55383615 => 'Manga - MG',
  55383616 => 'Matias Cardoso - MG',
  55383621 => 'Januária - MG',
  55383622 => 'Pedras de Maria da Cruz - MG',
  55383623 => 'Januária - MG',
  55383624 => 'São Romão - MG',
  55383625 => 'Varzelândia - MG',
  55383626 => 'Ibiracatu - MG',
  55383631 => 'São Francisco - MG',
  55383632 => 'Santa Fé de Minas - MG',
  55383633 => 'Ubaí - MG',
  55383634 => 'Chapada Gaúcha - MG',
  55383635 => 'Arinos - MG',
  55383647 => 'Formoso - MG',
  55383662 => 'Buritis - MG',
  55383663 => 'Buritis - MG',
  55383671 => 'Paracatu - MG',
  55383672 => 'Paracatu - MG',
  55383673 => 'Guarda-Mor - MG',
  55383674 => 'Cabeceira Grande - MG',
  55383675 => 'Bonfinópolis de Minas - MG',
  55383676 => 'Unaí - MG',
  55383677 => 'Unaí - MG',
  55383678 => 'Riachinho - MG',
  55383679 => 'Paracatu - MG',
  55383690 => 'Montes Claros - MG',
  55383721 => 'Curvelo - MG',
  55383722 => 'Curvelo - MG',
  55383723 => 'Inimutaba - MG',
  55383724 => 'Presidente Juscelino - MG',
  55383725 => 'Morro da Garça - MG',
  55383726 => 'Santo Hipólito - MG',
  55383727 => 'Monjolos - MG',
  55383728 => 'Angueretá - MG',
  55383729 => 'Curvelo - MG',
  55383731 => 'Várzea da Palma - MG',
  55383733 => 'Francisco Dumont - MG',
  55383740 => 'Pirapora - MG',
  55383741 => 'Pirapora - MG',
  55383742 => 'Buritizeiro - MG',
  55383743 => 'Pirapora - MG',
  55383744 => 'Jequitaí - MG',
  55383745 => 'Lagoa dos Patos - MG',
  55383746 => 'Ibiaí - MG',
  55383747 => 'Paredão de Minas - MG',
  55383749 => 'Pirapora - MG',
  55383751 => 'Corinto - MG',
  55383753 => 'Felixlândia - MG',
  55383754 => 'Três Marias - MG',
  55383755 => 'Morada Nova de Minas - MG',
  55383756 => 'Buenópolis - MG',
  55383757 => 'Joaquim Felício - MG',
  55383758 => 'Augusto de Lima - MG',
  55383759 => 'Lassance - MG',
  55383799 => 'Curvelo - MG',
  55383811 => 'Monte Azul - MG',
  55383812 => 'Espinosa - MG',
  55383813 => 'Mato Verde - MG',
  55383814 => 'Mamonas - MG',
  55383821 => 'Janaúba - MG',
  55383822 => 'Janaúba - MG',
  55383823 => 'Riacho dos Machados - MG',
  55383824 => 'Rio Pardo de Minas - MG',
  55383825 => 'Montezuma - MG',
  55383831 => 'Porteirinha - MG',
  55383832 => 'São João do Paraíso - MG',
  55383833 => 'Jaíba - MG',
  55383834 => 'Nova Porteirinha - MG',
  55383841 => 'Salinas - MG',
  55383842 => 'Salinas - MG',
  55383843 => 'Novorizonte - MG',
  55383845 => 'Taiobeiras - MG',
  55384009 => 'Montes Claros - MG',
  55384141 => 'Montes Claros - MG',
  5541 => 'Paraná',
  55412103 => 'Curitiba - PR',
  55412106 => 'Curitiba - PR',
  55412107 => 'Curitiba - PR',
  55412108 => 'Curitiba - PR',
  55412118 => 'Curitiba - PR',
  55412152 => 'Paranaguá - PR',
  55412626 => 'Curitiba - PR',
  55413003 => 'Curitiba - PR',
  55413012 => 'Curitiba - PR',
  55413020 => 'Curitiba - PR',
  55413025 => 'Curitiba - PR',
  55413031 => 'Araucária - PR',
  55413032 => 'Campo Largo - PR',
  55413033 => 'Pinhais - PR',
  55413034 => 'Piraquara - PR',
  55413035 => 'São José dos Pinhais - PR',
  55413036 => 'Almirante Tamandaré - PR',
  55413041 => 'Curitiba - PR',
  55413047 => 'Almirante Tamandaré - PR',
  55413048 => 'Araucária - PR',
  55413054 => 'Campo Largo - PR',
  55413056 => 'Pinhais - PR',
  55413058 => 'São José dos Pinhais - PR',
  55413059 => 'Pinhais - PR',
  55413060 => 'Fazenda Rio Grande - PR',
  55413061 => 'Curitiba - PR',
  55413063 => 'Curitiba - PR',
  55413070 => 'Fazenda Rio Grande - PR',
  55413073 => 'Belo Horizonte - MG',
  55413081 => 'São José dos Pinhais - PR',
  55413083 => 'Curitiba - PR',
  55413096 => 'São José dos Pinhais - PR',
  55413099 => 'Curitiba - PR',
  55413111 => 'Curitiba - PR',
  55413112 => 'Curitiba - PR',
  55413113 => 'Campo Largo - PR',
  55413116 => 'São José dos Pinhais - PR',
  55413122 => 'Curitiba - PR',
  55413131 => 'Curitiba - PR',
  55413132 => 'Pinhais - PR',
  55413134 => 'São José dos Pinhais - PR',
  55413140 => 'Campo Largo - PR',
  55413146 => 'São José dos Pinhais - PR',
  55413150 => 'Fazenda Rio Grande - PR',
  55413157 => 'Quatro Barras - PR',
  55413158 => 'Campina Grande do Sul - PR',
  55413200 => 'Curitiba - PR',
  55413202 => 'Curitiba - PR',
  55413208 => 'Curitiba - PR',
  55413210 => 'Curitiba - PR',
  55413212 => 'Curitiba - PR',
  55413217 => 'Curitiba - PR',
  55413219 => 'Curitiba - PR',
  55413221 => 'Curitiba - PR',
  55413222 => 'Curitiba - PR',
  55413223 => 'Curitiba - PR',
  55413224 => 'Curitiba - PR',
  55413225 => 'Curitiba - PR',
  55413226 => 'Curitiba - PR',
  55413227 => 'Curitiba - PR',
  55413228 => 'Curitiba - PR',
  55413229 => 'Curitiba - PR',
  55413232 => 'Curitiba - PR',
  55413233 => 'Curitiba - PR',
  55413234 => 'Curitiba - PR',
  55413235 => 'Curitiba - PR',
  55413236 => 'Curitiba - PR',
  55413238 => 'Curitiba - PR',
  55413239 => 'Curitiba - PR',
  55413240 => 'Curitiba - PR',
  55413241 => 'Curitiba - PR',
  55413242 => 'Curitiba - PR',
  55413243 => 'Curitiba - PR',
  55413244 => 'Curitiba - PR',
  55413245 => 'Curitiba - PR',
  55413246 => 'Curitiba - PR',
  55413247 => 'Curitiba - PR',
  55413248 => 'Curitiba - PR',
  55413249 => 'Curitiba - PR',
  55413251 => 'Curitiba - PR',
  55413254 => 'Curitiba - PR',
  55413255 => 'Curitiba - PR',
  55413256 => 'Curitiba - PR',
  55413257 => 'Curitiba - PR',
  55413258 => 'Curitiba - PR',
  55413259 => 'Curitiba - PR',
  55413261 => 'Curitiba - PR',
  55413263 => 'Curitiba - PR',
  55413264 => 'Curitiba - PR',
  55413265 => 'Curitiba - PR',
  55413266 => 'Curitiba - PR',
  55413267 => 'Curitiba - PR',
  55413268 => 'Curitiba - PR',
  55413269 => 'Curitiba - PR',
  55413270 => 'Curitiba - PR',
  55413271 => 'Curitiba - PR',
  55413272 => 'Curitiba - PR',
  55413273 => 'Curitiba - PR',
  55413274 => 'Curitiba - PR',
  55413275 => 'Curitiba - PR',
  55413276 => 'Curitiba - PR',
  55413277 => 'Curitiba - PR',
  55413278 => 'Curitiba - PR',
  55413279 => 'Curitiba - PR',
  55413281 => 'Curitiba - PR',
  55413282 => 'São José dos Pinhais - PR',
  55413283 => 'São José dos Pinhais - PR',
  55413284 => 'Curitiba - PR',
  55413285 => 'Curitiba - PR',
  55413286 => 'Curitiba - PR',
  55413287 => 'Curitiba - PR',
  55413288 => 'Curitiba - PR',
  55413289 => 'Curitiba - PR',
  55413291 => 'Campo Largo - PR',
  55413292 => 'Campo Largo - PR',
  55413296 => 'Curitiba - PR',
  55413297 => 'Curitiba - PR',
  55413298 => 'Curitiba - PR',
  55413304 => 'Curitiba - PR',
  55413310 => 'Curitiba - PR',
  55413312 => 'Curitiba - PR',
  55413314 => 'Curitiba - PR',
  55413315 => 'Curitiba - PR',
  55413316 => 'Curitiba - PR',
  55413317 => 'Curitiba - PR',
  55413320 => 'Curitiba - PR',
  55413321 => 'Curitiba - PR',
  55413322 => 'Curitiba - PR',
  55413323 => 'Curitiba - PR',
  55413324 => 'Curitiba - PR',
  55413326 => 'Curitiba - PR',
  55413327 => 'Curitiba - PR',
  55413329 => 'Curitiba - PR',
  55413330 => 'Curitiba - PR',
  55413332 => 'Curitiba - PR',
  55413333 => 'Curitiba - PR',
  55413334 => 'Curitiba - PR',
  55413335 => 'Curitiba - PR',
  55413336 => 'Curitiba - PR',
  55413337 => 'Curitiba - PR',
  55413338 => 'Curitiba - PR',
  55413339 => 'Curitiba - PR',
  55413340 => 'Curitiba - PR',
  55413342 => 'Curitiba - PR',
  55413343 => 'Curitiba - PR',
  55413344 => 'Curitiba - PR',
  55413345 => 'Curitiba - PR',
  55413346 => 'Curitiba - PR',
  55413347 => 'Curitiba - PR',
  55413348 => 'Curitiba - PR',
  55413349 => 'Curitiba - PR',
  55413354 => 'Curitiba - PR',
  55413355 => 'Curitiba - PR',
  55413356 => 'Curitiba - PR',
  55413357 => 'Curitiba - PR',
  55413358 => 'São José dos Pinhais - PR',
  55413360 => 'Curitiba - PR',
  55413361 => 'Curitiba - PR',
  55413364 => 'Curitiba - PR',
  55413365 => 'Curitiba - PR',
  55413366 => 'Curitiba - PR',
  55413367 => 'Curitiba - PR',
  55413369 => 'Curitiba - PR',
  55413370 => 'Curitiba - PR',
  55413371 => 'Curitiba - PR',
  55413372 => 'Curitiba - PR',
  55413373 => 'Curitiba - PR',
  55413374 => 'Curitiba - PR',
  55413375 => 'São José dos Pinhais - PR',
  55413376 => 'Curitiba - PR',
  55413377 => 'Curitiba - PR',
  55413378 => 'Curitiba - PR',
  55413379 => 'Curitiba - PR',
  55413382 => 'São José dos Pinhais - PR',
  55413383 => 'São José dos Pinhais - PR',
  55413384 => 'São José dos Pinhais - PR',
  55413385 => 'São José dos Pinhais - PR',
  55413386 => 'Curitiba - PR',
  55413389 => 'Curitiba - PR',
  55413391 => 'Campo Largo - PR',
  55413392 => 'Campo Largo - PR',
  55413393 => 'Campo Largo - PR',
  55413395 => 'Curitiba - PR',
  55413396 => 'Curitiba - PR',
  55413398 => 'São José dos Pinhais - PR',
  55413399 => 'Campo Largo - PR',
  55413404 => 'Curitiba - PR',
  55413405 => 'São José dos Pinhais - PR',
  55413414 => 'Tagaçaba - PR',
  55413420 => 'Paranaguá - PR',
  55413422 => 'Paranaguá - PR',
  55413423 => 'Paranaguá - PR',
  55413424 => 'Paranaguá - PR',
  55413425 => 'Paranaguá - PR',
  55413426 => 'Paranaguá - PR',
  55413427 => 'Paranaguá - PR',
  55413432 => 'Antonina - PR',
  55413442 => 'Guaratuba - PR',
  55413443 => 'Guaratuba - PR',
  55413452 => 'Matinhos - PR',
  55413453 => 'Matinhos - PR',
  55413455 => 'Pontal do Paraná - PR',
  55413456 => 'Matinhos - PR',
  55413457 => 'Pontal do Paraná - PR',
  55413462 => 'Morretes - PR',
  55413465 => 'Morretes - PR',
  55413468 => 'Alexandra - PR',
  55413472 => 'Guaratuba - PR',
  55413473 => 'Caiobá - PR',
  55413482 => 'Guaraqueçaba - PR',
  55413517 => 'São José dos Pinhais - PR',
  55413523 => 'Curitiba - PR',
  55413534 => 'São José dos Pinhais - PR',
  55413535 => 'Curitiba - PR',
  55413539 => 'Campo Largo - PR',
  55413543 => 'Rio Negro - PR',
  55413547 => 'Lapa - PR',
  55413552 => 'Araucária - PR',
  55413554 => 'Quatro Barras - PR',
  55413555 => 'Campo Largo - PR',
  55413556 => 'São José dos Pinhais - PR',
  55413562 => 'Colombo - PR',
  55413563 => 'Curitiba - PR',
  55413564 => 'Curitiba - PR',
  55413565 => 'Curitiba - PR',
  55413566 => 'Curitiba - PR',
  55413567 => 'Curitiba - PR',
  55413568 => 'Curitiba - PR',
  55413569 => 'Curitiba - PR',
  55413573 => 'Curitiba - PR',
  55413575 => 'Curitiba - PR',
  55413576 => 'Curitiba - PR',
  55413579 => 'Curitiba - PR',
  55413581 => 'Curitiba - PR',
  55413582 => 'Curitiba - PR',
  55413584 => 'Curitiba - PR',
  55413585 => 'Curitiba - PR',
  55413586 => 'São José dos Pinhais - PR',
  55413587 => 'São José dos Pinhais - PR',
  55413588 => 'São José dos Pinhais - PR',
  55413589 => 'Piraquara - PR',
  55413590 => 'Piraquara - PR',
  55413601 => 'Pinhais - PR',
  55413603 => 'Itaperuçu - PR',
  55413604 => 'Fazenda Rio Grande - PR',
  55413605 => 'Colombo - PR',
  55413606 => 'Colombo - PR',
  55413607 => 'Araucária - PR',
  55413608 => 'Fazenda Rio Grande - PR',
  55413614 => 'Araucária - PR',
  55413621 => 'Colombo - PR',
  55413622 => 'Lapa - PR',
  55413623 => 'Quitandinha - PR',
  55413624 => 'Agudos do Sul - PR',
  55413625 => 'Contenda - PR',
  55413626 => 'Mandirituba - PR',
  55413627 => 'Fazenda Rio Grande - PR',
  55413628 => 'Campo do Tenente - PR',
  55413629 => 'Tijucas do Sul - PR',
  55413632 => 'Piên - PR',
  55413633 => 'Paraná',
  55413634 => 'São José dos Pinhais - PR',
  55413635 => 'São José dos Pinhais - PR',
  55413636 => 'Balsa Nova - PR',
  55413637 => 'Bugre - PR',
  55413639 => 'Lapa - PR',
  55413642 => 'Araucária - PR',
  55413643 => 'Araucária - PR',
  55413648 => 'Bateias - PR',
  55413649 => 'Campo Largo - PR',
  55413651 => 'São Luiz do Purunã - PR',
  55413652 => 'Rio Branco do Sul - PR',
  55413653 => 'Pinhais - PR',
  55413656 => 'Colombo - PR',
  55413657 => 'Almirante Tamandaré - PR',
  55413658 => 'Bocaiúva do Sul - PR',
  55413659 => 'Tunas - PR',
  55413662 => 'Cerro Azul - PR',
  55413663 => 'Colombo - PR',
  55413664 => 'Doutor Ulysses - PR',
  55413665 => 'Pinhais - PR',
  55413666 => 'Colombo - PR',
  55413667 => 'Pinhais - PR',
  55413668 => 'Pinhais - PR',
  55413669 => 'Pinhais - PR',
  55413671 => 'Quatro Barras - PR',
  55413672 => 'Quatro Barras - PR',
  55413673 => 'Piraquara - PR',
  55413674 => 'Tijucas do Sul - PR',
  55413675 => 'Colombo - PR',
  55413676 => 'Campina Grande do Sul - PR',
  55413677 => 'Campo Magro - PR',
  55413678 => 'Adrianópolis - PR',
  55413679 => 'Campina Grande do Sul - PR',
  55413685 => 'Paiol de Baixo - PR',
  55413695 => 'Paraná',
  55413698 => 'Almirante Tamandaré - PR',
  55413699 => 'Almirante Tamandaré - PR',
  55413721 => 'Paranaguá - PR',
  55413873 => 'Campina Grande do Sul - PR',
  55413883 => 'Curitiba - PR',
  55413902 => 'Paranaguá - PR',
  55413906 => 'Curitiba - PR',
  55413907 => 'Curitiba - PR',
  55413908 => 'Curitiba - PR',
  55413941 => 'Curitiba - PR',
  55413971 => 'Matinhos - PR',
  55413972 => 'Pontal do Paraná - PR',
  55413973 => 'Rio Branco do Sul - PR',
  55413978 => 'Antonina - PR',
  55414001 => 'Curitiba - PR',
  55414003 => 'Curitiba - PR',
  55414007 => 'Curitiba - PR',
  55414009 => 'Curitiba - PR',
  55414020 => 'Curitiba - PR',
  55414062 => 'Curitiba - PR',
  55414064 => 'Paranaguá - PR',
  55414107 => 'Curitiba - PR',
  55414113 => 'Pinhais - PR',
  55414114 => 'Curitiba - PR',
  55414116 => 'Curitiba - PR',
  55414121 => 'Fazenda Rio Grande - PR',
  55414122 => 'Curitiba',
  5542 => 'Paraná',
  55422101 => 'Ponta Grossa - PR',
  55422102 => 'Ponta Grossa - PR',
  55422122 => 'Castro - PR',
  55423025 => 'Ponta Grossa - PR',
  55423026 => 'Ponta Grossa - PR',
  55423027 => 'Ponta Grossa - PR',
  55423028 => 'Ponta Grossa - PR',
  55423035 => 'Guarapuava - PR',
  55423036 => 'Guarapuava - PR',
  55423122 => 'Ponta Grossa - PR',
  55423132 => 'Irati - PR',
  55423219 => 'Ponta Grossa - PR',
  55423220 => 'Ponta Grossa - PR',
  55423221 => 'Telêmaco Borba - PR',
  55423222 => 'Ponta Grossa - PR',
  55423223 => 'Ponta Grossa - PR',
  55423224 => 'Ponta Grossa - PR',
  55423225 => 'Ponta Grossa - PR',
  55423226 => 'Ponta Grossa - PR',
  55423227 => 'Ponta Grossa - PR',
  55423228 => 'Ponta Grossa - PR',
  55423229 => 'Ponta Grossa - PR',
  55423231 => 'Carambeí - PR',
  55423232 => 'Castro - PR',
  55423233 => 'Castro - PR',
  55423234 => 'Colônia Castrolanda - PR',
  55423235 => 'Ponta Grossa - PR',
  55423236 => 'Ponta Grossa - PR',
  55423237 => 'Piraí do Sul - PR',
  55423238 => 'Ponta Grossa - PR',
  55423239 => 'Ponta Grossa - PR',
  55423242 => 'Ipiranga - PR',
  55423243 => 'Ponta Grossa - PR',
  55423245 => 'Socavão - PR',
  55423246 => 'Caetano Mendes - PR',
  55423247 => 'Ivaí - PR',
  55423250 => 'Abapã - PR',
  55423251 => 'Papagaios Novos - PR',
  55423252 => 'Palmeira - PR',
  55423254 => 'Colônia Witmarsum - PR',
  55423256 => 'Porto Amazonas - PR',
  55423259 => 'Ventania - PR',
  55423270 => 'Guaragi - PR',
  55423271 => 'Telêmaco Borba - PR',
  55423272 => 'Telêmaco Borba - PR',
  55423273 => 'Telêmaco Borba - PR',
  55423274 => 'Ventania - PR',
  55423275 => 'Tibagi - PR',
  55423276 => 'Reserva - PR',
  55423277 => 'Ortigueira - PR',
  55423278 => 'Imbaú - PR',
  55423301 => 'Ponta Grossa - PR',
  55423302 => 'Ponta Grossa - PR',
  55423303 => 'Guarapuava - PR',
  55423304 => 'Guarapuava - PR',
  55423311 => 'Ponta Grossa - PR',
  55423323 => 'Ponta Grossa - PR',
  55423334 => 'Maceió - AL',
  55423412 => 'Mato Branco de Baixo - PR',
  55423414 => 'Rio da Areia - PR',
  55423421 => 'Irati - PR',
  55423422 => 'Irati - PR',
  55423423 => 'Irati - PR',
  55423434 => 'Guamirim - PR',
  55423435 => 'Pinho de Baixo - PR',
  55423436 => 'Imbituva - PR',
  55423438 => 'Guamiranga - PR',
  55423446 => 'Prudentópolis - PR',
  55423447 => 'São João do Triunfo - PR',
  55423457 => 'Rebouças - PR',
  55423459 => 'Fernandes Pinheiro - PR',
  55423460 => 'Teixeira Soares - PR',
  55423463 => 'Rio Azul - PR',
  55423511 => 'Santo Antônio do Iratim - PR',
  55423516 => 'Rio Claro do Sul - PR',
  55423519 => 'União da Vitória - PR',
  55423520 => 'São Mateus do Sul - PR',
  55423521 => 'União da Vitória - PR',
  55423522 => 'União da Vitória - PR',
  55423523 => 'União da Vitória - PR',
  55423524 => 'União da Vitória - PR',
  55423526 => 'Porto União - SC',
  55423532 => 'São Mateus do Sul - PR',
  55423533 => 'Antônio Olinto - PR',
  55423542 => 'Mallet - PR',
  55423543 => 'Paulo Frontin - PR',
  55423551 => 'Santana - PR',
  55423552 => 'General Carneiro - PR',
  55423553 => 'Bituruna - PR',
  55423554 => 'Cruz Machado - PR',
  55423560 => 'Fluviópolis - PR',
  55423562 => 'Paula Freitas - PR',
  55423573 => 'Porto Vitória - PR',
  55423617 => 'Santa Maria do Oeste - PR',
  55423618 => 'Virmond - PR',
  55423621 => 'Guarapuava - PR',
  55423622 => 'Guarapuava - PR',
  55423623 => 'Guarapuava - PR',
  55423624 => 'Guarapuava - PR',
  55423625 => 'Entre Rios - PR',
  55423626 => 'Guarapuava - PR',
  55423627 => 'Guarapuava - PR',
  55423630 => 'Guarapuava - PR',
  55423631 => 'Guarapuava - PR',
  55423632 => 'Jordãozinho - PR',
  55423633 => 'Mato Rico - PR',
  55423634 => 'Campina do Simão - PR',
  55423635 => 'Laranjeiras do Sul - PR',
  55423636 => 'Cantagalo - PR',
  55423637 => 'Nova Laranjeiras - PR',
  55423638 => 'Candói - PR',
  55423639 => 'Foz do Jordão - PR',
  55423642 => 'Turvo - PR',
  55423643 => 'Nova Tebas - PR',
  55423644 => 'Santa Maria do Oeste - PR',
  55423645 => 'Laranjal - PR',
  55423646 => 'Pitanga - PR',
  55423648 => 'Marquinho - PR',
  55423649 => 'Guará - PR',
  55423651 => 'Reserva do Iguaçu - PR',
  55423652 => 'Boa Ventura de São Roque - PR',
  55423653 => 'Rio Bonito do Iguaçu - PR',
  55423654 => 'Catuporanga - PR',
  55423655 => 'Altamira do Paraná - PR',
  55423656 => 'Goioxim - PR',
  55423657 => 'Palmital - PR',
  55423659 => 'Samambaia - PR',
  55423661 => 'Porto Barreiro - PR',
  55423662 => 'Paz - PR',
  55423663 => 'Palmeirinha - PR',
  55423664 => 'Faxinal do Céu - PR',
  55423667 => 'Inácio Martins - PR',
  55423675 => 'Copel - PR',
  55423676 => 'Faxinal da Boa Vista - PR',
  55423677 => 'Pinhão - PR',
  55423901 => 'Ponta Grossa - PR',
  55423902 => 'Guarapuava - PR',
  55423903 => 'União da Vitória - PR',
  55423904 => 'Telêmaco Borba - PR',
  55423906 => 'Castro - PR',
  55423907 => 'Irati - PR',
  55423909 => 'Palmeira - PR',
  55423912 => 'São Mateus do Sul - PR',
  55423915 => 'Carambeí - PR',
  55423916 => 'Tibagi - PR',
  55424001 => 'Ponta Grossa - PR',
  55424007 => 'Ponta Grossa - PR',
  55424009 => 'Ponta Grossa - PR',
  55424052 => 'Guarapuava - PR',
  55424062 => 'Ponta Grossa - PR',
  55424063 => 'Ponta Grossa - PR',
  55424101 => 'Guarapuava - PR',
  55424141 => 'Ponta Grossa - PR',
  5543 => 'Paraná',
  55432101 => 'Londrina - PR',
  55432102 => 'Apucarana - PR',
  55432103 => 'Londrina - PR',
  55432104 => 'Londrina - PR',
  55432105 => 'Londrina - PR',
  55433011 => 'Arapongas - PR',
  55433015 => 'Rolândia - PR',
  55433016 => 'Rolândia - PR',
  55433017 => 'Londrina - PR',
  55433020 => 'Rolândia - PR',
  55433024 => 'Londrina - PR',
  55433025 => 'Londrina - PR',
  55433026 => 'Londrina - PR',
  55433027 => 'Londrina - PR',
  55433028 => 'Londrina - PR',
  55433029 => 'Londrina - PR',
  55433031 => 'Londrina - PR',
  55433032 => 'Londrina - PR',
  55433033 => 'Apucarana - PR',
  55433035 => 'Cambé - PR',
  55433046 => 'Arapongas - PR',
  55433047 => 'Apucarana - PR',
  55433051 => 'Rolândia - PR',
  55433055 => 'Arapongas - PR',
  55433056 => 'Arapongas - PR',
  55433062 => 'Cambé - PR',
  55433064 => 'Londrina - PR',
  55433066 => 'Londrina - PR',
  55433122 => 'Apucarana - PR',
  55433132 => 'Cornélio Procópio - PR',
  55433133 => 'Cornélio Procópio - PR',
  55433141 => 'Santo Antônio da Platina - PR',
  55433145 => 'Bandeirantes - PR',
  55433151 => 'Sabáudia - PR',
  55433152 => 'Arapongas - PR',
  55433154 => 'Cambé - PR',
  55433156 => 'Rolândia - PR',
  55433158 => 'Ibiporã - PR',
  55433162 => 'Apucarana - PR',
  55433172 => 'Arapongas - PR',
  55433174 => 'Cambé - PR',
  55433176 => 'Rolândia - PR',
  55433202 => 'Apucarana - PR',
  55433223 => 'Cambé - PR',
  55433224 => 'Santo Antônio do Paraíso - PR',
  55433232 => 'Sertanópolis - PR',
  55433235 => 'Primeiro de Maio - PR',
  55433240 => 'São Martinho - PR',
  55433242 => 'Bela Vista do Paraíso - PR',
  55433244 => 'Prado Ferreira - PR',
  55433249 => 'Cambé - PR',
  55433251 => 'Cambé - PR',
  55433252 => 'Arapongas - PR',
  55433253 => 'Cambé - PR',
  55433254 => 'Cambé - PR',
  55433255 => 'Rolândia - PR',
  55433256 => 'Rolândia - PR',
  55433257 => 'Pitangueiras - PR',
  55433258 => 'Ibiporã - PR',
  55433259 => 'Jataizinho - PR',
  55433260 => 'Guaraci - PR',
  55433262 => 'Assaí - PR',
  55433265 => 'São Sebastião da Amoreira - PR',
  55433266 => 'Nova Santa Bárbara - PR',
  55433267 => 'São Jerônimo da Serra - PR',
  55433268 => 'Ibiporã - PR',
  55433270 => 'Santa Cecília do Pavão - PR',
  55433272 => 'Jaguapitã - PR',
  55433273 => 'Miraselva - PR',
  55433274 => 'Arapongas - PR',
  55433275 => 'Arapongas - PR',
  55433276 => 'Arapongas - PR',
  55433301 => 'Londrina - PR',
  55433302 => 'Londrina - PR',
  55433303 => 'Arapongas - PR',
  55433304 => 'Londrina - PR',
  55433305 => 'Londrina - PR',
  55433306 => 'Londrina - PR',
  55433311 => 'Rolândia - PR',
  55433312 => 'Arapongas - PR',
  55433315 => 'Londrina - PR',
  55433316 => 'Arapongas - PR',
  55433321 => 'Londrina - PR',
  55433322 => 'Londrina - PR',
  55433323 => 'Londrina - PR',
  55433324 => 'Londrina - PR',
  55433325 => 'Londrina - PR',
  55433326 => 'Londrina - PR',
  55433329 => 'Londrina - PR',
  55433334 => 'Londrina - PR',
  55433336 => 'Londrina - PR',
  55433337 => 'Londrina - PR',
  55433339 => 'Londrina - PR',
  55433342 => 'Londrina - PR',
  55433343 => 'Londrina - PR',
  55433344 => 'Londrina - PR',
  55433345 => 'Londrina - PR',
  55433351 => 'Londrina - PR',
  55433354 => 'Londrina - PR',
  55433355 => 'Londrina - PR',
  55433356 => 'Londrina - PR',
  55433367 => 'Londrina - PR',
  55433371 => 'Londrina - PR',
  55433372 => 'Londrina - PR',
  55433373 => 'Londrina - PR',
  55433374 => 'Londrina - PR',
  55433375 => 'Londrina - PR',
  55433376 => 'Londrina - PR',
  55433378 => 'Londrina - PR',
  55433379 => 'Londrina - PR',
  55433398 => 'Tamarana - PR',
  55433399 => 'Tamarana - PR',
  55433401 => 'Cornélio Procópio - PR',
  55433417 => 'Ribeirão Bonito - PR',
  55433420 => 'Apucarana - PR',
  55433422 => 'Apucarana - PR',
  55433423 => 'Apucarana - PR',
  55433424 => 'Apucarana - PR',
  55433425 => 'Apucarana - PR',
  55433426 => 'Apucarana - PR',
  55433427 => 'Apucarana - PR',
  55433428 => 'Marilândia do Sul - PR',
  55433429 => 'Califórnia - PR',
  55433432 => 'Jandaia do Sul - PR',
  55433433 => 'Ariranha do Ivaí - PR',
  55433435 => 'Manoel Ribas - PR',
  55433436 => 'Cambira - PR',
  55433437 => 'Novo Itacolomi - PR',
  55433440 => 'Pirapó - PR',
  55433441 => 'Marumbi - PR',
  55433442 => 'Bom Sucesso - PR',
  55433444 => 'Arapuã - PR',
  55433451 => 'São Pedro do Ivaí - PR',
  55433452 => 'Borrazópolis - PR',
  55433453 => 'Kaloré - PR',
  55433454 => 'Cruzmaltina - PR',
  55433456 => 'Apucarana - PR',
  55433461 => 'Faxinal - PR',
  55433463 => 'Godoy Moreira - PR',
  55433464 => 'Mauá da Serra - PR',
  55433465 => 'Rosário do Ivaí - PR',
  55433467 => 'Rio Branco do Ivaí - PR',
  55433468 => 'Rio Bom - PR',
  55433471 => 'Jacutinga - PR',
  55433472 => 'Ivaiporã - PR',
  55433473 => 'Lidianópolis - PR',
  55433474 => 'Grandes Rios - PR',
  55433475 => 'Jardim Alegre - PR',
  55433476 => 'Cândido de Abreu - PR',
  55433477 => 'São João do Ivaí - PR',
  55433478 => 'Lunardelli - PR',
  55433511 => 'Jacarezinho - PR',
  55433512 => 'Arapoti - PR',
  55433520 => 'Cornélio Procópio - PR',
  55433521 => 'Londrina - PR',
  55433523 => 'Cornélio Procópio - PR',
  55433524 => 'Cornélio Procópio - PR',
  55433525 => 'Jacarezinho - PR',
  55433526 => 'Santana do Itararé - PR',
  55433527 => 'Jacarezinho - PR',
  55433528 => 'Wenceslau Braz - PR',
  55433529 => 'Jacarezinho - PR',
  55433531 => 'Santa Mariana - PR',
  55433532 => 'Cambará - PR',
  55433533 => 'Panema - PR',
  55433534 => 'Santo Antônio da Platina - PR',
  55433535 => 'Jaguariaíva - PR',
  55433536 => 'Ribeirão Claro - PR',
  55433537 => 'Barra do Jacaré - PR',
  55433538 => 'Andirá - PR',
  55433540 => 'Rancho Alegre - PR',
  55433541 => 'Uraí - PR',
  55433542 => 'Bandeirantes - PR',
  55433543 => 'Itambaracá - PR',
  55433544 => 'Santa Amélia - PR',
  55433545 => 'Curiúva - PR',
  55433546 => 'Ibaiti - PR',
  55433547 => 'Figueira - PR',
  55433548 => 'Sapopema - PR',
  55433549 => 'Bandeirantes - PR',
  55433551 => 'Ribeirão do Pinhal - PR',
  55433552 => 'Nova Fátima - PR',
  55433553 => 'Nova América da Colina - PR',
  55433554 => 'Congonhinhas - PR',
  55433555 => 'Japira - PR',
  55433556 => 'Abatiá - PR',
  55433557 => 'Arapoti - PR',
  55433558 => 'Santo Antônio da Platina - PR',
  55433559 => 'Joaquim Távora - PR',
  55433560 => 'Cornélio Procópio - PR',
  55433561 => 'Conselheiro Mairinck - PR',
  55433562 => 'Sertaneja - PR',
  55433563 => 'Tomazina - PR',
  55433564 => 'Quatiguá - PR',
  55433565 => 'São José da Boa Vista - PR',
  55433566 => 'Carlópolis - PR',
  55433567 => 'Sengés - PR',
  55433569 => 'Pinhalão - PR',
  55433571 => 'Siqueira Campos - PR',
  55433572 => 'Londrina - PR',
  55433573 => 'Guapirama - PR',
  55433575 => 'Londrina - PR',
  55433579 => 'Salto do Itararé - PR',
  55433616 => 'Sengés - PR',
  55433618 => 'Ibaiti - PR',
  55433619 => 'Ibaiti - PR',
  55433622 => 'Jaboti - PR',
  55433623 => 'Porecatu - PR',
  55433625 => 'Cafeara - PR',
  55433626 => 'Jundiaí do Sul - PR',
  55433627 => 'Leópolis - PR',
  55433660 => 'Lupionópolis - PR',
  55433661 => 'Alvorada do Sul - PR',
  55433662 => 'Florestópolis - PR',
  55433675 => 'Centenário do Sul - PR',
  55433711 => 'Cambé - PR',
  55433717 => 'Londrina - PR',
  55433878 => 'Londrina - PR',
  55433901 => 'Apucarana - PR',
  55433904 => 'Cornélio Procópio - PR',
  55433906 => 'Rolândia - PR',
  55433911 => 'Jacarezinho - PR',
  55434001 => 'Londrina - PR',
  55434004 => 'Londrina - PR',
  55434007 => 'Londrina - PR',
  55434009 => 'Londrina - PR',
  55434052 => 'Londrina - PR',
  55434062 => 'Londrina - PR',
  55434063 => 'Londrina - PR',
  55434101 => 'Apucarana - PR',
  55434104 => 'Apucarana - PR',
  55434141 => 'Londrina - PR',
  5544 => 'Paraná',
  55442031 => 'Umuarama - PR',
  55442033 => 'Palotina - PR',
  55442101 => 'Maringá - PR',
  55442102 => 'Maringá - PR',
  55442103 => 'Maringá - PR',
  55443011 => 'Maringá - PR',
  55443014 => 'Marialva - PR',
  55443015 => 'Marialva - PR',
  55443016 => 'Campo Mourão - PR',
  55443017 => 'Campo Mourão - PR',
  55443018 => 'Cianorte - PR',
  55443019 => 'Cianorte - PR',
  55443038 => 'Umuarama - PR',
  55443039 => 'Cianorte - PR',
  55443043 => 'Paiçandu - PR',
  55443045 => 'Paranavaí - PR',
  55443048 => 'Sarandi - PR',
  55443055 => 'Umuarama - PR',
  55443056 => 'Umuarama - PR',
  55443062 => 'Paranavaí - PR',
  55443068 => 'Campo Mourão - PR',
  55443122 => 'Maringá - PR',
  55443123 => 'Maringá - PR',
  55443125 => 'Marialva - PR',
  55443133 => 'Mandaguari - PR',
  55443201 => 'Campo Mourão - PR',
  55443209 => 'Nova Esperança - PR',
  55443218 => 'Maringá - PR',
  55443219 => 'Maringá - PR',
  55443220 => 'Maringá - PR',
  55443221 => 'Maringá - PR',
  55443222 => 'Maringá - PR',
  55443223 => 'Maringá - PR',
  55443224 => 'Maringá - PR',
  55443225 => 'Maringá - PR',
  55443226 => 'Maringá - PR',
  55443227 => 'Maringá - PR',
  55443228 => 'Maringá - PR',
  55443229 => 'Maringá - PR',
  55443231 => 'Itambé - PR',
  55443232 => 'Marialva - PR',
  55443233 => 'Mandaguari - PR',
  55443234 => 'Astorga - PR',
  55443236 => 'Floresta - PR',
  55443237 => 'Santa Zélia - PR',
  55443238 => 'Doutor Camargo - PR',
  55443242 => 'Floraí - PR',
  55443243 => 'São Jorge do Ivaí - PR',
  55443244 => 'Paiçandu - PR',
  55443245 => 'Mandaguaçu - PR',
  55443246 => 'Maringá - PR',
  55443247 => 'Santa Fé - PR',
  55443248 => 'Iguaraçu - PR',
  55443249 => 'Lobato - PR',
  55443250 => 'Presidente Castelo Branco - PR',
  55443251 => 'Sabáudia - PR',
  55443252 => 'Nova Esperança - PR',
  55443253 => 'Maringá - PR',
  55443254 => 'Atalaia - PR',
  55443255 => 'Maringá - PR',
  55443256 => 'Ângulo - PR',
  55443257 => 'Flórida - PR',
  55443258 => 'Munhoz de Melo - PR',
  55443259 => 'Maringá - PR',
  55443260 => 'Maringá - PR',
  55443261 => 'Maringá - PR',
  55443262 => 'Maringá - PR',
  55443263 => 'Maringá - PR',
  55443264 => 'Sarandi - PR',
  55443265 => 'Maringá - PR',
  55443266 => 'Maringá - PR',
  55443267 => 'Maringá - PR',
  55443268 => 'Maringá - PR',
  55443269 => 'Maringá - PR',
  55443270 => 'Uniflor - PR',
  55443272 => 'Fênix - PR',
  55443273 => 'Ivatuba - PR',
  55443274 => 'Sarandi - PR',
  55443275 => 'Barbosa Ferraz - PR',
  55443276 => 'Maringá - PR',
  55443277 => 'Corumbataí do Sul - PR',
  55443278 => 'Ourizona - PR',
  55443283 => 'Barbosa Ferraz - PR',
  55443288 => 'Sarandi - PR',
  55443302 => 'Maringá - PR',
  55443304 => 'Maringá - PR',
  55443305 => 'Maringá - PR',
  55443311 => 'Jardim Olinda - PR',
  55443312 => 'Nossa Senhora das Graças - PR',
  55443313 => 'Santa Inês - PR',
  55443323 => 'Colorado - PR',
  55443332 => 'Itaguajé - PR',
  55443340 => 'Alto Alegre - PR',
  55443342 => 'Paranapoema - PR',
  55443343 => 'Maringá - PR',
  55443344 => 'Maringá - PR',
  55443351 => 'Cianorte - PR',
  55443352 => 'Santo Inácio - PR',
  55443355 => 'Maringá - PR',
  55443361 => 'Umuarama - PR',
  55443366 => 'Maringá - PR',
  55443401 => 'Cianorte - PR',
  55443421 => 'Paranavaí - PR',
  55443422 => 'Paranavaí - PR',
  55443423 => 'Paranavaí - PR',
  55443424 => 'Paranavaí - PR',
  55443425 => 'Loanda - PR',
  55443427 => 'Porto Rico - PR',
  55443428 => 'Graciosa - PR',
  55443429 => 'Diamante do Norte - PR',
  55443431 => 'Paraíso do Norte - PR',
  55443432 => 'Nova Londrina - PR',
  55443433 => 'Nova Aliança do Ivaí - PR',
  55443435 => 'Planaltina do Paraná - PR',
  55443436 => 'Itaúna do Sul - PR',
  55443437 => 'Amaporã - PR',
  55443438 => 'São Carlos do Ivaí - PR',
  55443440 => 'Inajá - PR',
  55443441 => 'Terra Rica - PR',
  55443442 => 'Guairaçá - PR',
  55443443 => 'Santo Antônio do Caiuá - PR',
  55443444 => 'São Pedro do Paraná - PR',
  55443445 => 'São João do Caiuá - PR',
  55443446 => 'Paranavaí - PR',
  55443447 => 'Alto Paraná - PR',
  55443448 => 'Marilena - PR',
  55443452 => 'Santa Cruz de Monte Castelo - PR',
  55443453 => 'Santa Isabel do Ivaí - PR',
  55443455 => 'Santa Mônica - PR',
  55443460 => 'Tamboara - PR',
  55443462 => 'Querência do Norte - PR',
  55443463 => 'Paranacity - PR',
  55443464 => 'São Pedro do Paraná - PR',
  55443465 => 'Cruzeiro do Sul - PR',
  55443482 => 'Paranavaí - PR',
  55443518 => 'Campo Mourão - PR',
  55443521 => 'Goioerê - PR',
  55443522 => 'Goioerê - PR',
  55443523 => 'Campo Mourão - PR',
  55443524 => 'Campo Mourão - PR',
  55443525 => 'Campo Mourão - PR',
  55443526 => 'Formosa do Oeste - PR',
  55443527 => 'Nova Cantu - PR',
  55443528 => 'Assis Chateaubriand - PR',
  55443529 => 'Campo Mourão - PR',
  55443531 => 'Peabiru - PR',
  55443532 => 'Moreira Sales - PR',
  55443534 => 'Mariluz - PR',
  55443535 => 'Jesuítas - PR',
  55443536 => 'Brasiliana - PR',
  55443537 => 'Engenheiro Beltrão - PR',
  55443538 => 'Engenheiro Beltrão - PR',
  55443540 => 'Bragantina - PR',
  55443541 => 'Moreira Sales - PR',
  55443542 => 'Campina da Lagoa - PR',
  55443543 => 'Ubiratã - PR',
  55443544 => 'Tupãssi - PR',
  55443545 => 'Yolanda - PR',
  55443546 => 'Quarto Centenário - PR',
  55443551 => 'Iracema do Oeste - PR',
  55443552 => 'Boa Esperança - PR',
  55443553 => 'Janiópolis - PR',
  55443554 => 'Assis Chateaubriand - PR',
  55443555 => 'Nice - PR',
  55443556 => 'Rancho Alegre D\'Oeste - PR',
  55443557 => 'Terra Nova do Piquirí - PR',
  55443562 => 'Araruna - PR',
  55443563 => 'Farol - PR',
  55443565 => 'Tuneiras do Oeste - PR',
  55443566 => 'Juranda - PR',
  55443567 => 'Quinta do Sol - PR',
  55443568 => 'Mamborê - PR',
  55443569 => 'Juranda - PR',
  55443571 => 'Luiziana - PR',
  55443572 => 'Piquirivaí - PR',
  55443573 => 'Iretama - PR',
  55443575 => 'Roncador - PR',
  55443576 => 'Águas de Jurema - PR',
  55443582 => 'Guaiporã - PR',
  55443584 => 'Icaraíma - PR',
  55443588 => 'Vidigal - PR',
  55443593 => 'Jota Esse - PR',
  55443599 => 'Campo Mourão - PR',
  55443607 => 'São Tomé - PR',
  55443619 => 'Cianorte - PR',
  55443621 => 'Umuarama - PR',
  55443622 => 'Umuarama - PR',
  55443623 => 'Umuarama - PR',
  55443624 => 'Umuarama - PR',
  55443625 => 'Perobal - PR',
  55443626 => 'Umuarama - PR',
  55443627 => 'São Lourenço - PR',
  55443628 => 'Jussara - PR',
  55443629 => 'Cianorte - PR',
  55443631 => 'Cianorte - PR',
  55443632 => 'Xambrê - PR',
  55443633 => 'São João - PR',
  55443634 => 'São Jorge do Patrocínio - PR',
  55443635 => 'Japurá - PR',
  55443636 => 'Pérola - PR',
  55443637 => 'Cianorte - PR',
  55443639 => 'Umuarama - PR',
  55443640 => 'Esperança Nova - PR',
  55443641 => 'Terra Boa - PR',
  55443642 => 'Guaíra - PR',
  55443643 => 'Francisco Alves - PR',
  55443644 => 'São Manoel do Paraná - PR',
  55443645 => 'Terra Roxa - PR',
  55443646 => 'Pérola Independente - PR',
  55443647 => 'Maripá - PR',
  55443648 => 'Santa Rita do Oeste - PR',
  55443649 => 'Palotina - PR',
  55443652 => 'Iporã - PR',
  55443653 => 'Tuneiras do Oeste - PR',
  55443654 => 'Brasilândia do Sul - PR',
  55443655 => 'Cafezal do Sul - PR',
  55443656 => 'Alto Piquiri - PR',
  55443659 => 'Altônia - PR',
  55443662 => 'Maria Helena - PR',
  55443663 => 'Douradina - PR',
  55443664 => 'Alto Paraíso - PR',
  55443665 => 'Icaraíma - PR',
  55443666 => 'Herculândia - PR',
  55443667 => 'Santa Eliza - PR',
  55443668 => 'Serra dos Dourados - PR',
  55443672 => 'Rondon - PR',
  55443673 => 'Ivaté - PR',
  55443674 => 'Indianópolis - PR',
  55443675 => 'Cidade Gaúcha - PR',
  55443676 => 'Cruzeiro do Oeste - PR',
  55443677 => 'Tapejara - PR',
  55443679 => 'Tapira - PR',
  55443683 => 'Doutor Oliveira Castro - PR',
  55443684 => 'Guaporema - PR',
  55443685 => 'Nova Olímpia - PR',
  55443686 => 'Palotina - PR',
  55443687 => 'Maripá - PR',
  55443688 => 'Xambrê - PR',
  55443810 => 'Campo Mourão - PR',
  55443901 => 'Maringá - PR',
  55443902 => 'Paranavaí - PR',
  55443906 => 'Umuarama - PR',
  55444001 => 'Maringá - PR',
  55444003 => 'Maringá - PR',
  55444007 => 'Maringá - PR',
  55444009 => 'Maringá - PR',
  55444101 => 'Sarandi - PR',
  5545 => 'Paraná',
  55452031 => 'Marechal Cândido Rondon - PR',
  55452101 => 'Cascavel - PR',
  55452102 => 'Foz do Iguaçu - PR',
  55452103 => 'Toledo - PR',
  55452104 => 'Cascavel - PR',
  55452105 => 'Foz do Iguaçu - PR',
  55453015 => 'Cascavel - PR',
  55453017 => 'Foz do Iguaçu - PR',
  55453025 => 'Foz do Iguaçu - PR',
  55453026 => 'Foz do Iguaçu - PR',
  55453027 => 'Foz do Iguaçu - PR',
  55453028 => 'Foz do Iguaçu - PR',
  55453029 => 'Foz do Iguaçu - PR',
  55453030 => 'Foz do Iguaçu - PR',
  55453031 => 'Foz do Iguaçu - PR',
  55453035 => 'Cascavel - PR',
  55453036 => 'Cascavel - PR',
  55453037 => 'Cascavel - PR',
  55453038 => 'Cascavel - PR',
  55453039 => 'Cascavel - PR',
  55453054 => 'Toledo - PR',
  55453055 => 'Toledo - PR',
  55453056 => 'Toledo - PR',
  55453206 => 'Agro Cafeeira - PR',
  55453211 => 'Santa Maria - PR',
  55453218 => 'Cascavel - PR',
  55453219 => 'Cascavel - PR',
  55453220 => 'Cascavel - PR',
  55453222 => 'Cascavel - PR',
  55453223 => 'Cascavel - PR',
  55453224 => 'Cascavel - PR',
  55453225 => 'Cascavel - PR',
  55453226 => 'Cascavel - PR',
  55453227 => 'Cascavel - PR',
  55453228 => 'Cascavel - PR',
  55453229 => 'Cascavel - PR',
  55453230 => 'Diamante do Sul - PR',
  55453231 => 'Santa Tereza do Oeste - PR',
  55453232 => 'Guaraniaçu - PR',
  55453233 => 'Campo Bonito - PR',
  55453234 => 'Catanduvas - PR',
  55453235 => 'Três Barras do Paraná - PR',
  55453236 => 'Serranópolis do Iguaçu - PR',
  55453237 => 'Lindoeste - PR',
  55453238 => 'Ibema - PR',
  55453239 => 'Juvinópolis - PR',
  55453240 => 'Medianeira - PR',
  55453241 => 'Cafelândia - PR',
  55453242 => 'Corbélia - PR',
  55453243 => 'Nova Aurora - PR',
  55453244 => 'Missal - PR',
  55453245 => 'Braganey - PR',
  55453246 => 'Palmitópolis - PR',
  55453247 => 'Penha - PR',
  55453248 => 'Iguatu - PR',
  55453249 => 'Anahy - PR',
  55453251 => 'Ouro Verde do Oeste - PR',
  55453252 => 'Toledo - PR',
  55453253 => 'Nova Santa Rosa - PR',
  55453254 => 'Marechal Cândido Rondon - PR',
  55453255 => 'São Pedro do Iguaçu - PR',
  55453256 => 'Mercedes - PR',
  55453257 => 'Entre Rios do Oeste - PR',
  55453258 => 'Ramilândia - PR',
  55453259 => 'São José das Palmeiras - PR',
  55453260 => 'Missal - PR',
  55453262 => 'Matelândia - PR',
  55453264 => 'Medianeira - PR',
  55453266 => 'Céu Azul - PR',
  55453267 => 'Vera Cruz do Oeste - PR',
  55453268 => 'Santa Helena - PR',
  55453269 => 'Vila Nova - PR',
  55453270 => 'Iguiporã - PR',
  55453271 => 'Sede Alvorada - PR',
  55453272 => 'Diamante D\'Oeste - PR',
  55453273 => 'Toledo - PR',
  55453274 => 'Toledo - PR',
  55453275 => 'São Clemente - PR',
  55453276 => 'Santa Helena - PR',
  55453277 => 'Toledo - PR',
  55453278 => 'Toledo - PR',
  55453279 => 'Quatro Pontes - PR',
  55453280 => 'São Luiz D\'Oeste - PR',
  55453281 => 'Marechal Cândido Rondon - PR',
  55453282 => 'Pato Bragado - PR',
  55453283 => 'Margarida - PR',
  55453284 => 'Marechal Cândido Rondon - PR',
  55453285 => 'Subsede São Francisco - PR',
  55453286 => 'Capitão Leônidas Marques - PR',
  55453287 => 'Boa Vista da Aparecida - PR',
  55453288 => 'Santa Lúcia - PR',
  55453301 => 'Cascavel - PR',
  55453304 => 'Cascavel - PR',
  55453305 => 'Cascavel - PR',
  55453306 => 'Cascavel - PR',
  55453321 => 'Cascavel - PR',
  55453322 => 'Cascavel - PR',
  55453323 => 'Cascavel - PR',
  55453324 => 'Cascavel - PR',
  55453326 => 'Cascavel - PR',
  55453332 => 'Nova Santa Rosa - PR',
  55453333 => 'Cascavel - PR',
  55453336 => 'Luz Marina - PR',
  55453345 => 'Portão Ocoi - PR',
  55453346 => 'São João d\'Oeste - PR',
  55453352 => 'Rio do Salto - PR',
  55453375 => 'Esquina Ipiranga - PR',
  55453376 => 'Nova Concórdia - PR',
  55453377 => 'Foz do Iguaçu - PR',
  55453378 => 'Toledo - PR',
  55453379 => 'Toledo - PR',
  55453411 => 'Cascavel - PR',
  55453421 => 'Toledo - PR',
  55453520 => 'Foz do Iguaçu - PR',
  55453521 => 'Foz do Iguaçu - PR',
  55453522 => 'Foz do Iguaçu - PR',
  55453523 => 'Foz do Iguaçu - PR',
  55453524 => 'Foz do Iguaçu - PR',
  55453527 => 'Foz do Iguaçu - PR',
  55453528 => 'Foz do Iguaçu - PR',
  55453529 => 'Foz do Iguaçu - PR',
  55453540 => 'São Miguel do Iguaçu - PR',
  55453541 => 'Santa Terezinha de Itaipu - PR',
  55453543 => 'Vila Ipiranga - PR',
  55453545 => 'Foz do Iguaçu - PR',
  55453550 => 'São Jorge - PR',
  55453559 => 'Itaipulândia - PR',
  55453565 => 'São Miguel do Iguaçu - PR',
  55453572 => 'Foz do Iguaçu - PR',
  55453573 => 'Foz do Iguaçu - PR',
  55453574 => 'Foz do Iguaçu - PR',
  55453575 => 'Foz do Iguaçu - PR',
  55453576 => 'Foz do Iguaçu - PR',
  55453577 => 'Foz do Iguaçu - PR',
  55453578 => 'Foz do Iguaçu - PR',
  55453902 => 'Cascavel - PR',
  55454001 => 'Cascavel - PR',
  55454003 => 'Foz do Iguaçu - PR',
  55454007 => 'Cascavel - PR',
  55454009 => 'Cascavel - PR',
  55454052 => 'Foz do Iguaçu - PR',
  55454053 => 'Foz do Iguaçu - PR',
  55454062 => 'Cascavel - PR',
  55454063 => 'Cascavel - PR',
  55454100 => 'Cascavel - PR',
  55454101 => 'Cascavel - PR',
  5546 => 'Paraná',
  55462101 => 'Pato Branco - PR',
  55463025 => 'Pato Branco - PR',
  55463055 => 'Francisco Beltrão - PR',
  55463057 => 'Francisco Beltrão - PR',
  55463211 => 'Francisco Beltrão - PR',
  55463213 => 'Pato Branco - PR',
  55463220 => 'Pato Branco - PR',
  55463223 => 'Pato Branco - PR',
  55463224 => 'Pato Branco - PR',
  55463225 => 'Pato Branco - PR',
  55463226 => 'Mariópolis - PR',
  55463227 => 'Vitorino - PR',
  55463232 => 'Coronel Vivida - PR',
  55463233 => 'Coronel Vivida - PR',
  55463234 => 'Bom Sucesso do Sul - PR',
  55463242 => 'Chopinzinho - PR',
  55463243 => 'Mangueirinha - PR',
  55463244 => 'Sulina - PR',
  55463245 => 'Honório Serpa - PR',
  55463246 => 'Saudade do Iguaçu - PR',
  55463252 => 'Clevelândia - PR',
  55463254 => 'Coronel Domingos Soares - PR',
  55463262 => 'Palmas - PR',
  55463263 => 'Palmas - PR',
  55463272 => 'Pato Branco - PR',
  55463311 => 'Pato Branco - PR',
  55463313 => 'Pato Branco - PR',
  55463520 => 'Francisco Beltrão - PR',
  55463523 => 'Francisco Beltrão - PR',
  55463524 => 'Francisco Beltrão - PR',
  55463525 => 'Marmeleiro - PR',
  55463526 => 'Itapejara D\'Oeste - PR',
  55463527 => 'Francisco Beltrão - PR',
  55463532 => 'Quedas do Iguaçu - PR',
  55463533 => 'São João - PR',
  55463534 => 'São Jorge D\'Oeste - PR',
  55463535 => 'Verê - PR',
  55463536 => 'Dois Vizinhos - PR',
  55463537 => 'Boa Esperança do Iguaçu - PR',
  55463538 => 'Salto do Lontra - PR',
  55463539 => 'Doutor Antônio Paranhos - PR',
  55463540 => 'Pranchita - PR',
  55463542 => 'Santa Izabel do Oeste - PR',
  55463543 => 'Realeza - PR',
  55463544 => 'Enéas Marques - PR',
  55463545 => 'Nova Prata do Iguaçu - PR',
  55463546 => 'Nova Esperança do Sudoeste - PR',
  55463547 => 'Ampére - PR',
  55463548 => 'Bom Jesus do Sul - PR',
  55463549 => 'Realeza - PR',
  55463550 => 'Renascença - PR',
  55463552 => 'Capanema - PR',
  55463553 => 'Espigão Alto do Iguaçu - PR',
  55463555 => 'Planalto - PR',
  55463556 => 'Pérola D\'Oeste - PR',
  55463557 => 'Bela Vista da Caroba - PR',
  55463558 => 'Pérola D\'Oeste - PR',
  55463559 => 'Quedas do Iguaçu - PR',
  55463560 => 'Pinhal de São Bento - PR',
  55463562 => 'Manfrinópolis - PR',
  55463563 => 'Santo Antônio do Sudoeste - PR',
  55463564 => 'Salgado Filho - PR',
  55463565 => 'Flor da Serra do Sul - PR',
  55463572 => 'Cruzeiro do Iguaçu - PR',
  55463581 => 'Dois Vizinhos - PR',
  55463902 => 'Pato Branco - PR',
  55463905 => 'Francisco Beltrão - PR',
  55464007 => 'Francisco Beltrão - PR',
  55464054 => 'Francisco Beltrão - PR',
  55464055 => 'Pato Branco - PR',
  5547 => 'Santa Catarina',
  55472033 => 'Balneário Camboriú - SC',
  55472102 => 'Blumenau - SC',
  55472103 => 'Itajaí - SC',
  55472104 => 'Itajaí - SC',
  55472106 => 'Jaraguá do Sul - SC',
  55472107 => 'Jaraguá do Sul - SC',
  55472111 => 'Blumenau - SC',
  55472122 => 'Balneário Camboriú - SC',
  55472123 => 'Blumenau - SC',
  55472125 => 'Balneário Camboriú - SC',
  55473001 => 'Joinville - SC',
  55473018 => 'Gaspar - SC',
  55473021 => 'Balneário Camboriú - SC',
  55473031 => 'Joinville - SC',
  55473035 => 'Blumenau - SC',
  55473039 => 'Blumenau - SC',
  55473041 => 'Blumenau - SC',
  55473044 => 'Brusque - SC',
  55473045 => 'Itajaí - SC',
  55473046 => 'Itajaí - SC',
  55473047 => 'Balneário Camboriú - SC',
  55473048 => 'Itajaí - SC',
  55473050 => 'Camboriú - SC',
  55473052 => 'Indaial - SC',
  55473054 => 'Jaraguá do Sul - SC',
  55473055 => 'Jaraguá do Sul - SC',
  55473056 => 'Balneário Camboriú - SC',
  55473059 => 'São Bento do Sul - SC',
  55473062 => 'Balneário Camboriú - SC',
  55473065 => 'Balneário Camboriú - SC',
  55473080 => 'Blumenau - SC',
  55473081 => 'Balneário Camboriú - SC',
  55473084 => 'Jaraguá do Sul - SC',
  55473087 => 'Brusque - SC',
  55473098 => 'Itapema - SC',
  55473130 => 'Joinville - SC',
  55473135 => 'Rio da Anta - SC',
  55473148 => 'Navegantes - SC',
  55473152 => 'Gaspar Alto - SC',
  55473154 => 'Rio dos Cedros - SC',
  55473156 => 'Braço do Baú - SC',
  55473158 => 'Itajaí - SC',
  55473204 => 'São Francisco do Sul - SC',
  55473205 => 'Joinville - SC',
  55473211 => 'Brusque - SC',
  55473212 => 'Brusque - SC',
  55473221 => 'Blumenau - SC',
  55473222 => 'Blumenau - SC',
  55473228 => 'Balneário Camboriú - SC',
  55473231 => 'Blumenau - SC',
  55473233 => 'São Francisco do Sul - SC',
  55473234 => 'Blumenau - SC',
  55473236 => 'Blumenau - SC',
  55473238 => 'Blumenau - SC',
  55473241 => 'Itajaí - SC',
  55473242 => 'Pomerode - SC',
  55473249 => 'Itajaí - SC',
  55473251 => 'Brusque - SC',
  55473255 => 'Brusque - SC',
  55473258 => 'Vitor Meireles - SC',
  55473261 => 'Balneário Camboriú - SC',
  55473263 => 'Balneário Camboriú - SC',
  55473264 => 'Balneário Camboriú - SC',
  55473270 => 'Jaraguá do Sul - SC',
  55473274 => 'Jaraguá do Sul - SC',
  55473275 => 'Jaraguá do Sul - SC',
  55473300 => 'Rio do Sul - SC',
  55473301 => 'Indaial - SC',
  55473307 => 'Jaraguá do Sul - SC',
  55473309 => 'Blumenau - SC',
  55473312 => 'Timbó - SC',
  55473317 => 'Indaial - SC',
  55473318 => 'Gaspar - SC',
  55473319 => 'Navegantes - SC',
  55473322 => 'Blumenau - SC',
  55473323 => 'Blumenau - SC',
  55473324 => 'Blumenau - SC',
  55473325 => 'Blumenau - SC',
  55473326 => 'Blumenau - SC',
  55473328 => 'Blumenau - SC',
  55473329 => 'Blumenau - SC',
  55473330 => 'Blumenau - SC',
  55473332 => 'Gaspar - SC',
  55473333 => 'Indaial - SC',
  55473334 => 'Blumenau - SC',
  55473336 => 'Blumenau - SC',
  55473337 => 'Blumenau - SC',
  55473339 => 'Blumenau - SC',
  55473340 => 'Blumenau - SC',
  55473341 => 'Itajaí - SC',
  55473342 => 'Navegantes - SC',
  55473343 => 'Ilhota - SC',
  55473344 => 'Itajaí - SC',
  55473346 => 'Itajaí - SC',
  55473347 => 'Piçarras - SC',
  55473348 => 'Itajaí - SC',
  55473349 => 'Itajaí - SC',
  55473350 => 'Brusque - SC',
  55473351 => 'Brusque - SC',
  55473352 => 'Presidente Getúlio - SC',
  55473353 => 'Apiúna - SC',
  55473354 => 'Guabiruba - SC',
  55473355 => 'Brusque - SC',
  55473356 => 'Vidal Ramos - SC',
  55473357 => 'Ibirama - SC',
  55473358 => 'Witmarsum - SC',
  55473359 => 'Botuverá - SC',
  55473360 => 'Balneário Camboriú - SC',
  55473361 => 'Balneário Camboriú - SC',
  55473362 => 'Presidente Nereu - SC',
  55473363 => 'Balneário Camboriú - SC',
  55473364 => 'Dona Emma - SC',
  55473365 => 'Camboriú - SC',
  55473366 => 'Balneário Camboriú - SC',
  55473367 => 'Balneário Camboriú - SC',
  55473371 => 'Jaraguá do Sul - SC',
  55473372 => 'Jaraguá do Sul - SC',
  55473373 => 'Guaramirim - SC',
  55473374 => 'Schroeder - SC',
  55473375 => 'Corupá - SC',
  55473376 => 'Jaraguá do Sul - SC',
  55473377 => 'Luiz Alves - SC',
  55473379 => 'Massaranduba - SC',
  55473382 => 'Timbó - SC',
  55473383 => 'Ascurra - SC',
  55473384 => 'Rodeio - SC',
  55473385 => 'Benedito Novo - SC',
  55473386 => 'Rio dos Cedros - SC',
  55473387 => 'Pomerode - SC',
  55473388 => 'Doutor Pedrinho - SC',
  55473390 => 'Itajaí - SC',
  55473393 => 'Bombinhas - SC',
  55473394 => 'Indaial - SC',
  55473395 => 'Pomerode - SC',
  55473396 => 'Brusque - SC',
  55473397 => 'Gaspar - SC',
  55473399 => 'Timbó - SC',
  55473402 => 'Joinville - SC',
  55473404 => 'Itajaí - SC',
  55473405 => 'Itajaí - SC',
  55473406 => 'Balneário Camboriú - SC',
  55473411 => 'Rio do Sul - SC',
  55473416 => 'Joinville - SC',
  55473418 => 'Joinville - SC',
  55473422 => 'Joinville - SC',
  55473423 => 'Joinville - SC',
  55473424 => 'Pirabeiraba - SC',
  55473426 => 'Joinville - SC',
  55473427 => 'Joinville - SC',
  55473428 => 'Dona Francisca SC 301 - SC',
  55473431 => 'Joinville - SC',
  55473432 => 'Joinville - SC',
  55473433 => 'Joinville - SC',
  55473434 => 'Joinville - SC',
  55473436 => 'Joinville - SC',
  55473437 => 'Joinville - SC',
  55473439 => 'Joinville - SC',
  55473442 => 'São Francisco do Sul - SC',
  55473443 => 'Itapoá - SC',
  55473444 => 'São Francisco do Sul - SC',
  55473445 => 'Garuva - SC',
  55473446 => 'Barra Velha - SC',
  55473447 => 'Araquari - SC',
  55473448 => 'Balneário Barra do Sul - SC',
  55473449 => 'São Francisco do Sul - SC',
  55473452 => 'Araquari - SC',
  55473453 => 'Joinville - SC',
  55473455 => 'Joinville - SC',
  55473456 => 'Barra Velha - SC',
  55473457 => 'Barra Velha - SC',
  55473458 => 'São João do Itaperiú - SC',
  55473459 => 'São Francisco do Sul - SC',
  55473464 => 'Joinville - SC',
  55473465 => 'Joinville - SC',
  55473467 => 'Joinville - SC',
  55473471 => 'São Francisco do Sul - SC',
  55473472 => 'Joinville - SC',
  55473473 => 'Joinville - SC',
  55473488 => 'Blumenau - SC',
  55473492 => 'Santa Cruz - SC',
  55473501 => 'Jaraguá do Sul - SC',
  55473520 => 'Rio do Sul - SC',
  55473521 => 'Rio do Sul - SC',
  55473522 => 'Rio do Sul - SC',
  55473523 => 'Lontras - SC',
  55473524 => 'Aurora - SC',
  55473525 => 'Rio do Sul - SC',
  55473531 => 'Rio do Sul - SC',
  55473533 => 'Ituporanga - SC',
  55473534 => 'Agrolândia - SC',
  55473535 => 'Atalanta - SC',
  55473536 => 'Petrolândia - SC',
  55473537 => 'Chapadão do Lageado - SC',
  55473542 => 'Agronômica - SC',
  55473543 => 'Rio do Oeste - SC',
  55473544 => 'Trombudo Central - SC',
  55473545 => 'Pouso Redondo - SC',
  55473546 => 'Laurentino - SC',
  55473547 => 'Braço do Trombudo - SC',
  55473556 => 'Santa Terezinha - SC',
  55473557 => 'Imbuia - SC',
  55473562 => 'Taió - SC',
  55473563 => 'Salete - SC',
  55473564 => 'Rio do Campo - SC',
  55473565 => 'Mirim Doce - SC',
  55473621 => 'Canoinhas - SC',
  55473622 => 'Canoinhas - SC',
  55473623 => 'Três Barras - SC',
  55473624 => 'Canoinhas - SC',
  55473625 => 'Irineópolis - SC',
  55473626 => 'São Bento do Sul - SC',
  55473627 => 'Canoinhas - SC',
  55473629 => 'Bela Vista do Toldo - SC',
  55473631 => 'São Bento do Sul - SC',
  55473632 => 'Campo Alegre - SC',
  55473633 => 'São Bento do Sul - SC',
  55473634 => 'São Bento do Sul - SC',
  55473635 => 'São Bento do Sul - SC',
  55473641 => 'Mafra - SC',
  55473642 => 'Mafra - SC',
  55473643 => 'Mafra - SC',
  55473644 => 'Rio Negrinho - SC',
  55473647 => 'Mafra - SC',
  55473652 => 'Itaiópolis - SC',
  55473653 => 'Papanduva - SC',
  55473654 => 'Monte Castelo - SC',
  55473655 => 'Major Vieira - SC',
  55473674 => 'São Miguel da Serra - SC',
  55473702 => 'Blumenau - SC',
  55473901 => 'Blumenau - SC',
  55473902 => 'Jaraguá do Sul - SC',
  55473903 => 'Joinville - SC',
  55473904 => 'Itapema - SC',
  55473908 => 'Itajaí - SC',
  55474001 => 'Joinville - SC',
  55474003 => 'Blumenau - SC',
  55474007 => 'Joinville - SC',
  55474052 => 'Blumenau - SC',
  55474053 => 'Blumenau - SC',
  55474062 => 'Joinville - SC',
  55474063 => 'Joinville - SC',
  55474104 => 'São Bento do Sul - SC',
  55474105 => 'Itajaí - SC',
  55474108 => 'Balneário Camboriú - SC',
  55474141 => 'Itajaí - SC',
  5548 => 'Santa Catarina',
  55482101 => 'Criciúma - SC',
  55482102 => 'Criciúma - SC',
  55482106 => 'Florianópolis - SC',
  55482107 => 'Florianópolis - SC',
  55482108 => 'Florianópolis - SC',
  55483003 => 'Florianópolis - SC',
  55483014 => 'São João Batista - SC',
  55483018 => 'Florianópolis - SC',
  55483023 => 'Palhoça - SC',
  55483024 => 'Florianópolis - SC',
  55483025 => 'Florianópolis - SC',
  55483027 => 'Florianópolis - SC',
  55483028 => 'Florianópolis - SC',
  55483029 => 'São José - SC',
  55483030 => 'Florianópolis - SC',
  55483031 => 'Florianópolis - SC',
  55483033 => 'Palhoça - SC',
  55483034 => 'São José - SC',
  55483035 => 'São José - SC',
  55483037 => 'Florianópolis - SC',
  55483039 => 'Florianópolis - SC',
  55483043 => 'Tubarão - SC',
  55483045 => 'Criciúma - SC',
  55483047 => 'São José - SC',
  55483049 => 'São José - SC',
  55483052 => 'Tubarão - SC',
  55483053 => 'Tubarão - SC',
  55483055 => 'Içara - SC',
  55483061 => 'Criciúma - SC',
  55483062 => 'Florianópolis - SC',
  55483065 => 'Florianópolis - SC',
  55483081 => 'Criciúma - SC',
  55483084 => 'Florianópolis - SC',
  55483085 => 'Florianópolis - SC',
  55483086 => 'Palhoça - SC',
  55483089 => 'São José - SC',
  55483090 => 'São José - SC',
  55483091 => 'Florianópolis - SC',
  55483093 => 'Palhoça - SC',
  55483131 => 'Florianópolis - SC',
  55483199 => 'Tubarão - SC',
  55483202 => 'Florianópolis - SC',
  55483203 => 'Florianópolis - SC',
  55483205 => 'Florianópolis - SC',
  55483207 => 'Florianópolis - SC',
  55483208 => 'Florianópolis - SC',
  55483211 => 'Florianópolis - SC',
  55483212 => 'Florianópolis - SC',
  55483214 => 'São José - SC',
  55483215 => 'Florianópolis - SC',
  55483216 => 'Florianópolis - SC',
  55483221 => 'Florianópolis - SC',
  55483222 => 'Florianópolis - SC',
  55483223 => 'Florianópolis - SC',
  55483224 => 'Florianópolis - SC',
  55483225 => 'Florianópolis - SC',
  55483226 => 'Florianópolis - SC',
  55483228 => 'Florianópolis - SC',
  55483229 => 'Florianópolis - SC',
  55483231 => 'Florianópolis - SC',
  55483232 => 'Florianópolis - SC',
  55483236 => 'Florianópolis - SC',
  55483239 => 'Florianópolis - SC',
  55483241 => 'São José - SC',
  55483242 => 'Palhoça - SC',
  55483243 => 'Biguaçu - SC',
  55483244 => 'Florianópolis - SC',
  55483245 => 'Santo Amaro da Imperatriz - SC',
  55483246 => 'São José - SC',
  55483247 => 'São José - SC',
  55483248 => 'Florianópolis - SC',
  55483251 => 'Florianópolis - SC',
  55483252 => 'São Bonifácio - SC',
  55483253 => 'Paulo Lopes - SC',
  55483254 => 'Garopaba - SC',
  55483255 => 'Imbituba - SC',
  55483256 => 'Anitápolis - SC',
  55483257 => 'São José - SC',
  55483258 => 'São José - SC',
  55483259 => 'São José - SC',
  55483262 => 'Governador Celso Ramos - SC',
  55483263 => 'Tijucas - SC',
  55483264 => 'Canelinha - SC',
  55483265 => 'São João Batista - SC',
  55483266 => 'Florianópolis - SC',
  55483267 => 'Nova Trento - SC',
  55483268 => 'Leoberto Leal - SC',
  55483269 => 'Florianópolis - SC',
  55483271 => 'Florianópolis - SC',
  55483272 => 'Antônio Carlos - SC',
  55483273 => 'Major Gercino - SC',
  55483274 => 'Angelina - SC',
  55483275 => 'Rancho Queimado - SC',
  55483276 => 'Alfredo Wagner - SC',
  55483277 => 'São Pedro de Alcântara - SC',
  55483278 => 'São José - SC',
  55483279 => 'Palhoça - SC',
  55483282 => 'Florianópolis - SC',
  55483283 => 'Palhoça - SC',
  55483285 => 'Biguaçu - SC',
  55483286 => 'Palhoça - SC',
  55483287 => 'Florianópolis - SC',
  55483296 => 'Biguaçu - SC',
  55483298 => 'Florianópolis - SC',
  55483301 => 'Tubarão - SC',
  55483303 => 'Florianópolis - SC',
  55483306 => 'Florianópolis - SC',
  55483321 => 'Florianópolis - SC',
  55483322 => 'Florianópolis - SC',
  55483324 => 'Florianópolis - SC',
  55483330 => 'Florianópolis - SC',
  55483332 => 'Florianópolis - SC',
  55483333 => 'Florianópolis - SC',
  55483334 => 'Florianópolis - SC',
  55483341 => 'Palhoça - SC',
  55483342 => 'Palhoça - SC',
  55483343 => 'São José - SC',
  55483344 => 'Palhoça - SC',
  55483345 => 'Tijucas - SC',
  55483346 => 'São José - SC',
  55483354 => 'Garopaba - SC',
  55483355 => 'Imbituba - SC',
  55483356 => 'Imbituba - SC',
  55483357 => 'São José - SC',
  55483369 => 'Florianópolis - SC',
  55483372 => 'São José - SC',
  55483378 => 'Santa Tereza - SC',
  55483381 => 'São José - SC',
  55483411 => 'Criciúma - SC',
  55483413 => 'Criciúma - SC',
  55483430 => 'Criciúma - SC',
  55483431 => 'Quarta Linha - SC',
  55483432 => 'Içara - SC',
  55483433 => 'Criciúma - SC',
  55483434 => 'Morro da Fumaça - SC',
  55483435 => 'Siderópolis - SC',
  55483436 => 'Nova Veneza - SC',
  55483437 => 'Criciúma - SC',
  55483438 => 'Criciúma - SC',
  55483439 => 'Criciúma - SC',
  55483441 => 'Urussanga - SC',
  55483442 => 'Criciúma - SC',
  55483443 => 'Criciúma - SC',
  55483447 => 'Cocal do Sul - SC',
  55483461 => 'Criciúma - SC',
  55483462 => 'Criciúma - SC',
  55483463 => 'Forquilhinha - SC',
  55483464 => 'Lauro Muller - SC',
  55483465 => 'Urussanga - SC',
  55483466 => 'Orleans - SC',
  55483467 => 'Içara - SC',
  55483469 => 'Treviso - SC',
  55483476 => 'Nova Veneza - SC',
  55483478 => 'Criciúma - SC',
  55483491 => 'Orleans - SC',
  55483521 => 'Araranguá - SC',
  55483522 => 'Araranguá - SC',
  55483523 => 'Maracajá - SC',
  55483524 => 'Araranguá - SC',
  55483525 => 'Turvo - SC',
  55483526 => 'Balneário Arroio do Silva - SC',
  55483527 => 'Araranguá - SC',
  55483529 => 'Praia Grande - SC',
  55483531 => 'Morro Grande - SC',
  55483532 => 'Praia Grande - SC',
  55483533 => 'Sombrio - SC',
  55483534 => 'Santa Rosa do Sul - SC',
  55483535 => 'Jacinto Machado - SC',
  55483536 => 'Timbé do Sul - SC',
  55483537 => 'Meleiro - SC',
  55483538 => 'Balneário Bela Torres - SC',
  55483539 => 'São João do Sul - SC',
  55483544 => 'Morro Grande - SC',
  55483546 => 'Ermo - SC',
  55483548 => 'Passo de Torres - SC',
  55483583 => 'Balneário Gaivota - SC',
  55483591 => 'Jacinto Machado - SC',
  55483622 => 'Tubarão - SC',
  55483623 => 'Capivari de Baixo - SC',
  55483624 => 'Jaguaruna - SC',
  55483625 => 'Treze de Maio - SC',
  55483626 => 'Tubarão - SC',
  55483631 => 'Tubarão - SC',
  55483632 => 'Tubarão - SC',
  55483641 => 'Tijucas - SC',
  55483642 => 'Gravatal - SC',
  55483643 => 'Imaruí - SC',
  55483644 => 'Laguna - SC',
  55483645 => 'Armazém - SC',
  55483646 => 'Laguna - SC',
  55483647 => 'Laguna - SC',
  55483648 => 'Termas do Gravatal - SC',
  55483652 => 'Grão Pará - SC',
  55483653 => 'Rio Fortuna - SC',
  55483654 => 'Santa Rosa de Lima - SC',
  55483655 => 'Sangão - SC',
  55483656 => 'Sangão - SC',
  55483657 => 'São Ludgero - SC',
  55483658 => 'Braço do Norte - SC',
  55483659 => 'Pedras Grandes - SC',
  55483664 => 'Florianópolis - SC',
  55483668 => 'Pinheiral - SC',
  55483717 => 'Florianópolis - SC',
  55483721 => 'Florianópolis - SC',
  55483821 => 'Florianópolis - SC',
  55483877 => 'Florianópolis - SC',
  55483878 => 'Florianópolis - SC',
  55483879 => 'Florianópolis - SC',
  55483902 => 'Criciúma - SC',
  55483903 => 'Araranguá - SC',
  55483906 => 'Tubarão - SC',
  55483952 => 'Florianópolis - SC',
  55483953 => 'Florianópolis - SC',
  55484001 => 'Florianópolis - SC',
  55484003 => 'Florianópolis - SC',
  55484004 => 'Florianópolis - SC',
  55484007 => 'Florianópolis - SC',
  55484009 => 'Florianópolis - SC',
  55484020 => 'Florianópolis - SC',
  55484042 => 'Balneário Camboriú - SC',
  55484053 => 'Criciúma - SC',
  55484062 => 'Florianópolis - SC',
  55484106 => 'Florianópolis - SC',
  55484107 => 'Palhoça - SC',
  55484109 => 'Biguaçu - SC',
  55484141 => 'Florianópolis - SC',
  5549 => 'Santa Catarina',
  55492020 => 'Chapecó - SC',
  55492049 => 'Chapecó - SC',
  55492101 => 'Lages - SC',
  55492102 => 'Lages - SC',
  55493015 => 'Lages - SC',
  55493018 => 'Lages - SC',
  55493019 => 'Lages - SC',
  55493021 => 'Lages - SC',
  55493025 => 'Chapecó - SC',
  55493030 => 'Concórdia - SC',
  55493198 => 'Maravilha - SC',
  55493199 => 'Chapecó - SC',
  55493202 => 'Joaçaba - SC',
  55493221 => 'Lages - SC',
  55493222 => 'Lages - SC',
  55493223 => 'Lages - SC',
  55493224 => 'Lages - SC',
  55493225 => 'Lages - SC',
  55493226 => 'Lages - SC',
  55493227 => 'Lages - SC',
  55493228 => 'Bocaina do Sul - SC',
  55493229 => 'Lages - SC',
  55493232 => 'Bom Jardim da Serra - SC',
  55493233 => 'São Joaquim - SC',
  55493235 => 'Painel - SC',
  55493236 => 'Urupema - SC',
  55493237 => 'Capão Alto - SC',
  55493238 => 'Palmeira - SC',
  55493241 => 'Curitibanos - SC',
  55493242 => 'São José do Cerrito - SC',
  55493243 => 'Correia Pinto - SC',
  55493244 => 'Santa Cecília - SC',
  55493245 => 'Curitibanos - SC',
  55493246 => 'Fraiburgo - SC',
  55493247 => 'Lebon Régis - SC',
  55493248 => 'Ponte Alta - SC',
  55493249 => 'Campo Belo do Sul - SC',
  55493251 => 'Lages - SC',
  55493252 => 'Timbó Grande - SC',
  55493253 => 'São Cristovão do Sul - SC',
  55493254 => 'Ponte Alta do Norte - SC',
  55493256 => 'Fraiburgo - SC',
  55493257 => 'Frei Rogério - SC',
  55493258 => 'Cerro Negro - SC',
  55493275 => 'Otacílio Costa - SC',
  55493277 => 'Bom Retiro - SC',
  55493278 => 'Urubici - SC',
  55493279 => 'Rio Rufino - SC',
  55493289 => 'Lages - SC',
  55493301 => 'Concórdia - SC',
  55493304 => 'Chapecó - SC',
  55493311 => 'Chapecó - SC',
  55493312 => 'Chapecó - SC',
  55493313 => 'Chapecó - SC',
  55493316 => 'Chapecó - SC',
  55493319 => 'Chapecó - SC',
  55493321 => 'Chapecó - SC',
  55493322 => 'Chapecó - SC',
  55493323 => 'Chapecó - SC',
  55493325 => 'São Carlos - SC',
  55493326 => 'Caxambu do Sul - SC',
  55493327 => 'Nova Itaberaba - SC',
  55493328 => 'Chapecó - SC',
  55493329 => 'Chapecó - SC',
  55493330 => 'Chapecó - SC',
  55493332 => 'Águas Frias - SC',
  55493333 => 'Nova Erechim - SC',
  55493334 => 'Saudades - SC',
  55493335 => 'Planalto Alegre - SC',
  55493336 => 'Guatambú - SC',
  55493337 => 'Jardinópolis - SC',
  55493338 => 'Cunhataí - SC',
  55493339 => 'Águas de Chapecó - SC',
  55493341 => 'Jupiá - SC',
  55493342 => 'Galvão - SC',
  55493343 => 'Formosa do Sul - SC',
  55493344 => 'São Lourenço do Oeste - SC',
  55493345 => 'Santiago do Sul - SC',
  55493346 => 'Quilombo - SC',
  55493347 => 'Coronel Freitas - SC',
  55493348 => 'União do Oeste - SC',
  55493349 => 'Irati - SC',
  55493351 => 'Entre Rios - SC',
  55493353 => 'Xaxim - SC',
  55493354 => 'Marema - SC',
  55493355 => 'Lajeado Grande - SC',
  55493356 => 'Arvoredo - SC',
  55493358 => 'Cordilheira Alta - SC',
  55493361 => 'Chapecó - SC',
  55493362 => 'Novo Horizonte - SC',
  55493363 => 'Bom Jesus do Oeste - SC',
  55493364 => 'Serra Alta - SC',
  55493365 => 'Modelo - SC',
  55493366 => 'Pinhalzinho - SC',
  55493367 => 'Sul Brasil - SC',
  55493382 => 'Xanxerê - SC',
  55493424 => 'Bom Jesus - SC',
  55493425 => 'Concórdia - SC',
  55493431 => 'Xanxerê - SC',
  55493432 => 'Irani - SC',
  55493433 => 'Xanxerê - SC',
  55493434 => 'Vargeão - SC',
  55493435 => 'Ponte Serrada - SC',
  55493436 => 'Faxinal dos Guedes - SC',
  55493437 => 'Passos Maia - SC',
  55493438 => 'Ipumirim - SC',
  55493439 => 'Linha Planalto - SC',
  55493441 => 'Concórdia - SC',
  55493442 => 'Concórdia - SC',
  55493443 => 'São Domingos - SC',
  55493444 => 'Concórdia - SC',
  55493445 => 'Abelardo Luz - SC',
  55493446 => 'Lindóia do Sul - SC',
  55493447 => 'Ouro Verde - SC',
  55493448 => 'Arabutã - SC',
  55493449 => 'Ipuaçu - SC',
  55493451 => 'Paial - SC',
  55493452 => 'Seara - SC',
  55493453 => 'Peritiba - SC',
  55493454 => 'Xavantina - SC',
  55493455 => 'Alto Bela Vista - SC',
  55493456 => 'Campina da Alegria - SC',
  55493457 => 'Presidente Castelo Branco - SC',
  55493458 => 'Itá - SC',
  55493459 => 'Coronel Martins - SC',
  55493482 => 'Concórdia - SC',
  55493491 => 'Seara - SC',
  55493521 => 'Joaçaba - SC',
  55493522 => 'Joaçaba - SC',
  55493523 => 'Luzerna - SC',
  55493524 => 'Água Doce - SC',
  55493525 => 'Catanduvas - SC',
  55493526 => 'Jaborá - SC',
  55493527 => 'Joaçaba - SC',
  55493531 => 'Videira - SC',
  55493532 => 'Tangará - SC',
  55493533 => 'Videira - SC',
  55493534 => 'Ibiam - SC',
  55493535 => 'Arroio Trinta - SC',
  55493536 => 'Salto Veloso - SC',
  55493537 => 'Treze Tílias - SC',
  55493538 => 'Ibicaré - SC',
  55493539 => 'Iomerê - SC',
  55493541 => 'Campos Novos - SC',
  55493542 => 'Erval Velho - SC',
  55493543 => 'Anita Garibaldi - SC',
  55493544 => 'Campos Novos - SC',
  55493545 => 'Abdon Batista - SC',
  55493546 => 'Monte Carlo - SC',
  55493547 => 'Celso Ramos - SC',
  55493548 => 'Vargem Bonita - SC',
  55493549 => 'Vargem - SC',
  55493551 => 'Joaçaba - SC',
  55493552 => 'Lacerdópolis - SC',
  55493553 => 'Piratuba - SC',
  55493554 => 'Herval D\'Oeste - SC',
  55493555 => 'Capinzal - SC',
  55493556 => 'Brunópolis - SC',
  55493557 => 'Zortéa - SC',
  55493558 => 'Ipira - SC',
  55493561 => 'Caçador - SC',
  55493562 => 'Pinheiro Preto - SC',
  55493563 => 'Caçador - SC',
  55493564 => 'Rio das Antas - SC',
  55493566 => 'Videira - SC',
  55493567 => 'Caçador - SC',
  55493572 => 'Matos Costa - SC',
  55493573 => 'Calmon - SC',
  55493574 => 'Macieira - SC',
  55493592 => 'Tangará - SC',
  55493621 => 'São Miguel do Oeste - SC',
  55493622 => 'São Miguel do Oeste - SC',
  55493623 => 'Descanso - SC',
  55493624 => 'Romelândia - SC',
  55493625 => 'Belmonte - SC',
  55493626 => 'Bandeirante - SC',
  55493627 => 'Paraíso - SC',
  55493631 => 'São Miguel do Oeste - SC',
  55493632 => 'Tunápolis - SC',
  55493633 => 'Santa Helena - SC',
  55493634 => 'Iporã do Oeste - SC',
  55493636 => 'São João do Oeste - SC',
  55493637 => 'Cristo Rei - SC',
  55493641 => 'Princesa - SC',
  55493642 => 'Guarujá do Sul - SC',
  55493643 => 'São José do Cedro - SC',
  55493644 => 'Dionísio Cerqueira - SC',
  55493645 => 'Guaraciaba - SC',
  55493646 => 'Cunha Porã - SC',
  55493647 => 'Palmitos - SC',
  55493648 => 'Caibi - SC',
  55493649 => 'Barra Bonita - SC',
  55493652 => 'Palma Sola - SC',
  55493653 => 'Anchieta - SC',
  55493654 => 'São Bernardino - SC',
  55493655 => 'Campo Erê - SC',
  55493656 => 'Saltinho - SC',
  55493657 => 'Santa Terezinha do Progresso - SC',
  55493658 => 'Tigrinhos - SC',
  55493664 => 'Maravilha - SC',
  55493665 => 'Iraceminha - SC',
  55493667 => 'São Miguel da Boa Vista - SC',
  55493668 => 'Flor do Sertão - SC',
  55493674 => 'Mondaí - SC',
  55493675 => 'Riqueza - SC',
  55493677 => 'Itapiranga - SC',
  55493678 => 'Itapiranga - SC',
  55493700 => 'Chapecó - SC',
  55493719 => 'Capinzal - SC',
  55493735 => 'Fazenda Zandavalli - SC',
  55493802 => 'Lages - SC',
  55493804 => 'Lages - SC',
  55493905 => 'Chapecó - SC',
  55493907 => 'Lages - SC',
  55493908 => 'Fraiburgo - SC',
  55494101 => 'Chapecó - SC',
  5551 => 'Rio Grande do Sul',
  55512101 => 'Porto Alegre - RS',
  55512104 => 'Porto Alegre - RS',
  55512106 => 'Santa Cruz do Sul - RS',
  55512107 => 'Santa Cruz do Sul - RS',
  55512109 => 'Santa Cruz do Sul - RS',
  55512117 => 'Porto Alegre - RS',
  55512121 => 'Porto Alegre - RS',
  55512125 => 'Porto Alegre - RS',
  55512126 => 'Porto Alegre - RS',
  55512131 => 'Porto Alegre - RS',
  55512139 => 'Porto Alegre - RS',
  55513011 => 'Lajeado - RS',
  55513018 => 'Porto Alegre - RS',
  55513031 => 'Canoas - RS',
  55513032 => 'Canoas - RS',
  55513033 => 'Esteio - RS',
  55513034 => 'Sapucaia do Sul - RS',
  55513035 => 'Novo Hamburgo - RS',
  55513036 => 'Novo Hamburgo - RS',
  55513037 => 'São Leopoldo - RS',
  55513038 => 'Campo Bom - RS',
  55513039 => 'Sapiranga - RS',
  55513041 => 'Cachoeirinha - RS',
  55513042 => 'Gravataí - RS',
  55513043 => 'Gravataí - RS',
  55513044 => 'Alvorada - RS',
  55513045 => 'Viamão - RS',
  55513047 => 'Gravataí - RS',
  55513048 => 'Osório - RS',
  55513049 => 'Campo Bom - RS',
  55513051 => 'Canoas - RS',
  55513052 => 'Canoas - RS',
  55513053 => 'Santa Cruz do Sul - RS',
  55513054 => 'Viamão - RS',
  55513055 => 'Guaíba - RS',
  55513056 => 'Santa Cruz do Sul - RS',
  55513057 => 'Montenegro - RS',
  55513059 => 'Canoas - RS',
  55513064 => 'Sapiranga - RS',
  55513065 => 'Novo Hamburgo - RS',
  55513066 => 'Novo Hamburgo - RS',
  55513067 => 'Novo Hamburgo - RS',
  55513075 => 'Canoas - RS',
  55513077 => 'Canoas - RS',
  55513088 => 'Lajeado - RS',
  55513097 => 'Novo Hamburgo - RS',
  55513099 => 'São Leopoldo - RS',
  55513101 => 'Alvorada - RS',
  55513111 => 'Cachoeirinha - RS',
  55513114 => 'Guaíba - RS',
  55513115 => 'Canoas - RS',
  55513123 => 'Porto Alegre - RS',
  55513127 => 'Gravataí - RS',
  55513128 => 'Gravataí - RS',
  55513133 => 'Novo Hamburgo - RS',
  55513134 => 'São Leopoldo - RS',
  55513137 => 'Alvorada - RS',
  55513140 => 'Novo Hamburgo - RS',
  55513151 => 'Porto Alegre - RS',
  55513157 => 'Três Coroas - RS',
  55513170 => 'Estância Velha - RS',
  55513179 => 'Taquara - RS',
  55513191 => 'Portão - RS',
  55513192 => 'Novo Hamburgo - RS',
  55513203 => 'Porto Alegre - RS',
  55513205 => 'Porto Alegre - RS',
  55513206 => 'Porto Alegre - RS',
  55513209 => 'Porto Alegre - RS',
  55513211 => 'Porto Alegre - RS',
  55513212 => 'Porto Alegre - RS',
  55513213 => 'Porto Alegre - RS',
  55513214 => 'Porto Alegre - RS',
  55513216 => 'Porto Alegre - RS',
  55513217 => 'Porto Alegre - RS',
  55513219 => 'Porto Alegre - RS',
  55513220 => 'Porto Alegre - RS',
  55513221 => 'Porto Alegre - RS',
  55513222 => 'Porto Alegre - RS',
  55513223 => 'Porto Alegre - RS',
  55513224 => 'Porto Alegre - RS',
  55513225 => 'Porto Alegre - RS',
  55513226 => 'Porto Alegre - RS',
  55513227 => 'Porto Alegre - RS',
  55513228 => 'Porto Alegre - RS',
  55513230 => 'Porto Alegre - RS',
  55513231 => 'Porto Alegre - RS',
  55513232 => 'Porto Alegre - RS',
  55513233 => 'Porto Alegre - RS',
  55513235 => 'Porto Alegre - RS',
  55513240 => 'Porto Alegre - RS',
  55513241 => 'Porto Alegre - RS',
  55513242 => 'Porto Alegre - RS',
  55513244 => 'Porto Alegre - RS',
  55513245 => 'Porto Alegre - RS',
  55513246 => 'Porto Alegre - RS',
  55513247 => 'Porto Alegre - RS',
  55513248 => 'Porto Alegre - RS',
  55513249 => 'Porto Alegre - RS',
  55513250 => 'Porto Alegre - RS',
  55513251 => 'Porto Alegre - RS',
  55513254 => 'Porto Alegre - RS',
  55513257 => 'Porto Alegre - RS',
  55513258 => 'Porto Alegre - RS',
  55513259 => 'Porto Alegre - RS',
  55513261 => 'Porto Alegre - RS',
  55513262 => 'Porto Alegre - RS',
  55513265 => 'Porto Alegre - RS',
  55513268 => 'Porto Alegre - RS',
  55513269 => 'Porto Alegre - RS',
  55513271 => 'Novo Hamburgo - RS',
  55513272 => 'Porto Alegre - RS',
  55513273 => 'Porto Alegre - RS',
  55513275 => 'Porto Alegre - RS',
  55513276 => 'Porto Alegre - RS',
  55513277 => 'Porto Alegre - RS',
  55513278 => 'Porto Alegre - RS',
  55513280 => 'Porto Alegre - RS',
  55513281 => 'Porto Alegre - RS',
  55513282 => 'Porto Alegre - RS',
  55513283 => 'Novo Hamburgo - RS',
  55513284 => 'Porto Alegre - RS',
  55513286 => 'Porto Alegre - RS',
  55513287 => 'Porto Alegre - RS',
  55513295 => 'Porto Alegre - RS',
  55513307 => 'Porto Alegre - RS',
  55513308 => 'Porto Alegre - RS',
  55513311 => 'Porto Alegre - RS',
  55513312 => 'Porto Alegre - RS',
  55513313 => 'Nova Santa Rita - RS',
  55513314 => 'Porto Alegre - RS',
  55513315 => 'Porto Alegre - RS',
  55513316 => 'Porto Alegre - RS',
  55513317 => 'Porto Alegre - RS',
  55513318 => 'Porto Alegre - RS',
  55513319 => 'Porto Alegre - RS',
  55513320 => 'Porto Alegre - RS',
  55513323 => 'Porto Alegre - RS',
  55513325 => 'Porto Alegre - RS',
  55513329 => 'Porto Alegre - RS',
  55513330 => 'Porto Alegre - RS',
  55513331 => 'Porto Alegre - RS',
  55513332 => 'Porto Alegre - RS',
  55513333 => 'Porto Alegre - RS',
  55513334 => 'Porto Alegre - RS',
  55513335 => 'Porto Alegre - RS',
  55513336 => 'Porto Alegre - RS',
  55513338 => 'Porto Alegre - RS',
  55513339 => 'Porto Alegre - RS',
  55513340 => 'Porto Alegre - RS',
  55513341 => 'Porto Alegre - RS',
  55513342 => 'Porto Alegre - RS',
  55513343 => 'Porto Alegre - RS',
  55513344 => 'Porto Alegre - RS',
  55513346 => 'Porto Alegre - RS',
  55513347 => 'Porto Alegre - RS',
  55513349 => 'Porto Alegre - RS',
  55513350 => 'Porto Alegre - RS',
  55513351 => 'Porto Alegre - RS',
  55513352 => 'Porto Alegre - RS',
  55513353 => 'Porto Alegre - RS',
  55513355 => 'Porto Alegre - RS',
  55513356 => 'Porto Alegre - RS',
  55513357 => 'Porto Alegre - RS',
  55513358 => 'Porto Alegre - RS',
  55513359 => 'Porto Alegre - RS',
  55513360 => 'Porto Alegre - RS',
  55513361 => 'Porto Alegre - RS',
  55513364 => 'Porto Alegre - RS',
  55513365 => 'Porto Alegre - RS',
  55513366 => 'Porto Alegre - RS',
  55513367 => 'Porto Alegre - RS',
  55513369 => 'Porto Alegre - RS',
  55513371 => 'Porto Alegre - RS',
  55513373 => 'Porto Alegre - RS',
  55513374 => 'Porto Alegre - RS',
  55513376 => 'Porto Alegre - RS',
  55513377 => 'Porto Alegre - RS',
  55513378 => 'Porto Alegre - RS',
  55513383 => 'Porto Alegre - RS',
  55513384 => 'Porto Alegre - RS',
  55513385 => 'Porto Alegre - RS',
  55513386 => 'Porto Alegre - RS',
  55513388 => 'Porto Alegre - RS',
  55513390 => 'Porto Alegre - RS',
  55513391 => 'Porto Alegre - RS',
  55513392 => 'Porto Alegre - RS',
  55513393 => 'Porto Alegre - RS',
  55513395 => 'Porto Alegre - RS',
  55513397 => 'Porto Alegre - RS',
  55513398 => 'Porto Alegre - RS',
  55513401 => 'Guaíba - RS',
  55513402 => 'Guaíba - RS',
  55513403 => 'Guaíba - RS',
  55513407 => 'Porto Alegre - RS',
  55513409 => 'Miraguaia - RS',
  55513411 => 'Alvorada - RS',
  55513415 => 'Canoas - RS',
  55513416 => 'Capão da Canoa - RS',
  55513421 => 'Gravataí - RS',
  55513422 => 'Barro Vermelho - RS',
  55513423 => 'Gravataí - RS',
  55513425 => 'Canoas - RS',
  55513426 => 'Canoas - RS',
  55513427 => 'Canoas - RS',
  55513429 => 'Canoas - RS',
  55513430 => 'Gravataí - RS',
  55513431 => 'Gravataí - RS',
  55513432 => 'Gravataí - RS',
  55513434 => 'Viamão - RS',
  55513436 => 'Viamão - RS',
  55513438 => 'Cachoeirinha - RS',
  55513439 => 'Cachoeirinha - RS',
  55513441 => 'Cachoeirinha - RS',
  55513442 => 'Alvorada - RS',
  55513443 => 'Alvorada - RS',
  55513444 => 'Viamão - RS',
  55513445 => 'Presidente Lucena - RS',
  55513446 => 'Viamão - RS',
  55513447 => 'Alvorada - RS',
  55513452 => 'Sapucaia do Sul - RS',
  55513454 => 'Esteio - RS',
  55513456 => 'Canoas - RS',
  55513457 => 'Rio Grande do Sul',
  55513458 => 'Esteio - RS',
  55513459 => 'Esteio - RS',
  55513460 => 'Esteio - RS',
  55513461 => 'Esteio - RS',
  55513462 => 'Canoas - RS',
  55513464 => 'Canoas - RS',
  55513465 => 'Canoas - RS',
  55513466 => 'Canoas - RS',
  55513467 => 'Canoas - RS',
  55513469 => 'Cachoeirinha - RS',
  55513471 => 'Cachoeirinha - RS',
  55513472 => 'Canoas - RS',
  55513473 => 'Esteio - RS',
  55513474 => 'Sapucaia do Sul - RS',
  55513475 => 'Canoas - RS',
  55513476 => 'Canoas - RS',
  55513477 => 'Canoas - RS',
  55513479 => 'Nova Santa Rita - RS',
  55513480 => 'Guaíba - RS',
  55513481 => 'Eldorado do Sul - RS',
  55513482 => 'Barra do Ribeiro - RS',
  55513483 => 'Alvorada - RS',
  55513484 => 'Gravataí - RS',
  55513485 => 'Viamão - RS',
  55513486 => 'Morungava - RS',
  55513487 => 'Glorinha - RS',
  55513488 => 'Gravataí - RS',
  55513489 => 'Gravataí - RS',
  55513490 => 'Gravataí - RS',
  55513491 => 'Guaíba - RS',
  55513492 => 'Viamão - RS',
  55513493 => 'Viamão - RS',
  55513494 => 'Itapuã - RS',
  55513495 => 'Sertão Santana - RS',
  55513496 => 'Gravataí - RS',
  55513497 => 'Gravataí - RS',
  55513499 => 'Eldorado do Sul - RS',
  55513502 => 'Capão da Canoa - RS',
  55513509 => 'São Leopoldo - RS',
  55513515 => 'Porto Alegre - RS',
  55513522 => 'Morro da Pedra - RS',
  55513523 => 'Parobé - RS',
  55513524 => 'Novo Hamburgo - RS',
  55513529 => 'Sapiranga - RS',
  55513534 => 'Rio Grande do Sul',
  55513536 => 'Conceição - RS',
  55513537 => 'Porto Alegre - RS',
  55513538 => 'Canoas - RS',
  55513539 => 'Novo Hamburgo - RS',
  55513541 => 'Taquara - RS',
  55513542 => 'Taquara - RS',
  55513543 => 'Parobé - RS',
  55513544 => 'Taquara - RS',
  55513545 => 'Igrejinha - RS',
  55513546 => 'Três Coroas - RS',
  55513547 => 'Rolante - RS',
  55513548 => 'Riozinho - RS',
  55513549 => 'Igrejinha - RS',
  55513551 => 'Estância Velha - RS',
  55513552 => 'Lindolfo Collor - RS',
  55513553 => 'Novo Hamburgo - RS',
  55513554 => 'São Leopoldo - RS',
  55513559 => 'Sapiranga - RS',
  55513560 => 'Araricá - RS',
  55513561 => 'Estância Velha - RS',
  55513562 => 'Portão - RS',
  55513563 => 'Ivoti - RS',
  55513564 => 'Dois Irmãos - RS',
  55513565 => 'Nova Hartz - RS',
  55513566 => 'São Leopoldo - RS',
  55513567 => 'Santa Maria do Herval - RS',
  55513568 => 'São Leopoldo - RS',
  55513569 => 'Morro Reuter - RS',
  55513571 => 'São José do Hortêncio - RS',
  55513575 => 'São Leopoldo - RS',
  55513579 => 'São Leopoldo - RS',
  55513580 => 'Novo Hamburgo - RS',
  55513581 => 'Novo Hamburgo - RS',
  55513582 => 'Novo Hamburgo - RS',
  55513584 => 'Novo Hamburgo - RS',
  55513585 => 'Campo Bom - RS',
  55513587 => 'Novo Hamburgo - RS',
  55513588 => 'São Leopoldo - RS',
  55513589 => 'São Leopoldo - RS',
  55513590 => 'São Leopoldo - RS',
  55513591 => 'São Leopoldo - RS',
  55513592 => 'São Leopoldo - RS',
  55513593 => 'Novo Hamburgo - RS',
  55513594 => 'Novo Hamburgo - RS',
  55513595 => 'Novo Hamburgo - RS',
  55513596 => 'Novo Hamburgo - RS',
  55513597 => 'Campo Bom - RS',
  55513598 => 'Campo Bom - RS',
  55513599 => 'Sapiranga - RS',
  55513601 => 'Osório - RS',
  55513602 => 'Caraá - RS',
  55513603 => 'Rainha do Mar - RS',
  55513605 => 'Torres - RS',
  55513606 => 'Rondinha Velha - RS',
  55513611 => 'Chuvisca - RS',
  55513612 => 'Doutor Ricardo - RS',
  55513613 => 'Fazenda Vilanova - RS',
  55513614 => 'Maratá - RS',
  55513615 => 'Caraá - RS',
  55513618 => 'Barão - RS',
  55513621 => 'Capão Novo - RS',
  55513622 => 'Arroio Teixeira - RS',
  55513624 => 'Santa Terezinha - RS',
  55513625 => 'Capão da Canoa - RS',
  55513626 => 'Torres - RS',
  55513627 => 'Imbé - RS',
  55513628 => 'Maquiné - RS',
  55513631 => 'Escadinhas - RS',
  55513632 => 'Montenegro - RS',
  55513633 => 'Pareci Novo - RS',
  55513634 => 'Bom Princípio - RS',
  55513635 => 'São Sebastião do Caí - RS',
  55513637 => 'Feliz - RS',
  55513638 => 'Salvador do Sul - RS',
  55513639 => 'São Vendelino - RS',
  55513645 => 'São Pedro da Serra - RS',
  55513647 => 'Vendinha - RS',
  55513649 => 'Montenegro - RS',
  55513650 => 'Barão do Triunfo - RS',
  55513651 => 'São Jerônimo - RS',
  55513652 => 'Butiá - RS',
  55513653 => 'Taquari - RS',
  55513654 => 'Triunfo - RS',
  55513655 => 'General Câmara - RS',
  55513656 => 'Arroio dos Ratos - RS',
  55513657 => 'Vendinha - RS',
  55513658 => 'Charqueadas - RS',
  55513661 => 'Tramandaí - RS',
  55513662 => 'Santo Antônio da Patrulha - RS',
  55513663 => 'Osório - RS',
  55513664 => 'Torres - RS',
  55513665 => 'Capão da Canoa - RS',
  55513666 => 'Terra de Areia - RS',
  55513667 => 'Três Cachoeiras - RS',
  55513668 => 'Palmares do Sul - RS',
  55513669 => 'Nova Tramandaí - RS',
  55513670 => 'Amaral Ferrador - RS',
  55513671 => 'Camaquã - RS',
  55513672 => 'Tapes - RS',
  55513673 => 'Mostardas - RS',
  55513674 => 'Tavares - RS',
  55513675 => 'Cerro Grande do Sul - RS',
  55513676 => 'Arambaré - RS',
  55513677 => 'Dom Feliciano - RS',
  55513678 => 'Cristal - RS',
  55513679 => 'Sentinela do Sul - RS',
  55513680 => 'Quintão - RS',
  55513681 => 'Cidreira - RS',
  55513682 => 'Balneário Pinhal - RS',
  55513684 => 'Tramandaí - RS',
  55513685 => 'Capivari do Sul - RS',
  55513686 => 'Magistério - RS',
  55513687 => 'Arroio do Sal - RS',
  55513689 => 'Xangri-Lá - RS',
  55513692 => 'Camaquã - RS',
  55513694 => 'Minas do Leão - RS',
  55513695 => 'Harmonia - RS',
  55513696 => 'Barão - RS',
  55513697 => 'Brochier - RS',
  55513698 => 'Capela de Santana - RS',
  55513704 => 'Monte Alverne - RS',
  55513705 => 'Marques de Souza - RS',
  55513707 => 'Lajeado - RS',
  55513708 => 'Sinimbu - RS',
  55513709 => 'Lajeado - RS',
  55513710 => 'Lajeado - RS',
  55513711 => 'Santa Cruz do Sul - RS',
  55513712 => 'Estrêla - RS',
  55513713 => 'Santa Cruz do Sul - RS',
  55513714 => 'Lajeado - RS',
  55513715 => 'Santa Cruz do Sul - RS',
  55513716 => 'Arroio do Meio - RS',
  55513717 => 'Santa Cruz do Sul - RS',
  55513718 => 'Vera Cruz - RS',
  55513719 => 'Santa Cruz do Sul - RS',
  55513720 => 'Estrêla - RS',
  55513721 => 'Triunfo - RS',
  55513722 => 'Cachoeira do Sul - RS',
  55513723 => 'Cachoeira do Sul - RS',
  55513724 => 'Cachoeira do Sul - RS',
  55513725 => 'Cerro Branco - RS',
  55513726 => 'Lajeado - RS',
  55513729 => 'Lajeado - RS',
  55513730 => 'Passo do Sobrado - RS',
  55513731 => 'Rio Pardo - RS',
  55513733 => 'Encruzilhada do Sul - RS',
  55513734 => 'Pantano Grande - RS',
  55513735 => 'Várzea do Capivarita - RS',
  55513736 => 'Estrêla - RS',
  55513738 => 'Venâncio Aires - RS',
  55513740 => 'Santa Cruz do Sul - RS',
  55513741 => 'Venâncio Aires - RS',
  55513742 => 'Sobradinho - RS',
  55513743 => 'Candelária - RS',
  55513744 => 'Ibarama - RS',
  55513745 => 'Segredo - RS',
  55513747 => 'Arroio do Tigre - RS',
  55513748 => 'Lajeado - RS',
  55513749 => 'Palanque - RS',
  55513750 => 'Vale do Sol - RS',
  55513751 => 'Encantado - RS',
  55513752 => 'Jacarezinho - RS',
  55513753 => 'Roca Sales - RS',
  55513754 => 'Imigrante - RS',
  55513755 => 'Muçum - RS',
  55513756 => 'Anta Gorda - RS',
  55513757 => 'Nova Bréscia - RS',
  55513758 => 'Capitão - RS',
  55513759 => 'Travesseiro - RS',
  55513760 => 'Colinas - RS',
  55513761 => 'Paverama - RS',
  55513764 => 'Cruzeiro do Sul - RS',
  55513765 => 'Lagoão - RS',
  55513766 => 'Bom Retiro do Sul - RS',
  55513767 => 'Tunas - RS',
  55513770 => 'Sério - RS',
  55513772 => 'Arvorezinha - RS',
  55513773 => 'Poço das Antas - RS',
  55513774 => 'Ilópolis - RS',
  55513775 => 'Pouso Novo - RS',
  55513776 => 'Relvado - RS',
  55513777 => 'Putinga - RS',
  55513782 => 'Santa Clara do Sul - RS',
  55513783 => 'Esteio - RS',
  55513784 => 'Mato Leitão - RS',
  55513787 => 'Pinheiral - RS',
  55513788 => 'Progresso - RS',
  55513789 => 'Boqueirão do Leão - RS',
  55513792 => 'Costão - RS',
  55513793 => 'Venâncio Aires - RS',
  55513798 => 'Rio Grande do Sul',
  55513822 => 'Gravataí - RS',
  55513883 => 'Montenegro - RS',
  55513898 => 'Porto Alegre - RS',
  55513902 => 'Santa Cruz do Sul - RS',
  55513930 => 'Porto Alegre - RS',
  55513931 => 'Porto Alegre - RS',
  55513933 => 'Gravataí - RS',
  55513941 => 'Canoas - RS',
  55513945 => 'Gravataí - RS',
  55513951 => 'Novo Hamburgo - RS',
  55513952 => 'São Leopoldo - RS',
  55513958 => 'Charqueadas - RS',
  55513959 => 'Sapiranga - RS',
  55513982 => 'Lajeado - RS',
  55513983 => 'Venâncio Aires - RS',
  55514001 => 'Porto Alegre - RS',
  55514003 => 'Porto Alegre - RS',
  55514007 => 'Porto Alegre - RS',
  55514009 => 'Porto Alegre - RS',
  55514062 => 'Porto Alegre - RS',
  55514104 => 'Canoas - RS',
  55514109 => 'Gravataí - RS',
  55514112 => 'Novo Hamburgo - RS',
  55514116 => 'Porto Alegre - RS',
  5552 => 'Rio Grande do Sul',
  5553 => 'Rio Grande do Sul',
  55532123 => 'Pelotas - RS',
  55532125 => 'Rio Grande - RS',
  55532126 => 'Rio Grande - RS',
  55532128 => 'Pelotas - RS',
  55533011 => 'Pelotas - RS',
  55533015 => 'Pelotas - RS',
  55533025 => 'Pelotas - RS',
  55533026 => 'Pelotas - RS',
  55533027 => 'Pelotas - RS',
  55533028 => 'Pelotas - RS',
  55533029 => 'Pelotas - RS',
  55533031 => 'Pelotas - RS',
  55533035 => 'Rio Grande - RS',
  55533045 => 'Rio Grande - RS',
  55533201 => 'Rio Grande - RS',
  55533204 => 'Rio Grande - RS',
  55533221 => 'Pelotas - RS',
  55533222 => 'Pelotas - RS',
  55533223 => 'Pelotas - RS',
  55533224 => 'Morro Redondo - RS',
  55533225 => 'Pelotas - RS',
  55533226 => 'Pelotas - RS',
  55533227 => 'Pelotas - RS',
  55533228 => 'Pelotas - RS',
  55533229 => 'Pelotas - RS',
  55533231 => 'Rio Grande - RS',
  55533232 => 'Rio Grande - RS',
  55533233 => 'Rio Grande - RS',
  55533234 => 'Rio Grande - RS',
  55533235 => 'Rio Grande - RS',
  55533236 => 'Rio Grande - RS',
  55533237 => 'Povo Novo - RS',
  55533238 => 'São José do Norte - RS',
  55533239 => 'Rio Grande - RS',
  55533240 => 'Bagé - RS',
  55533241 => 'Bagé - RS',
  55533242 => 'Bagé - RS',
  55533243 => 'Dom Pedrito - RS',
  55533245 => 'Candiota - RS',
  55533246 => 'Aceguá - RS',
  55533247 => 'Bagé - RS',
  55533248 => 'Pinheiro Machado - RS',
  55533249 => 'Hulha Negra - RS',
  55533251 => 'São Lourenço do Sul - RS',
  55533252 => 'Canguçu - RS',
  55533254 => 'Cerrito - RS',
  55533255 => 'Pedro Osório - RS',
  55533256 => 'Bojuru - RS',
  55533257 => 'Piratini - RS',
  55533258 => 'Santana da Boa Vista - RS',
  55533261 => 'Jaguarão - RS',
  55533262 => 'Arroio Grande - RS',
  55533263 => 'Santa Vitória do Palmar - RS',
  55533264 => 'Praia do Hermenegildo - RS',
  55533265 => 'Chuí - RS',
  55533267 => 'Herval - RS',
  55533271 => 'Pelotas - RS',
  55533272 => 'Pelotas - RS',
  55533273 => 'Pelotas - RS',
  55533274 => 'Pelotas - RS',
  55533275 => 'Capão do Leão - RS',
  55533278 => 'Pelotas - RS',
  55533279 => 'Pelotas - RS',
  55533281 => 'Pelotas - RS',
  55533282 => 'Pelotas - RS',
  55533283 => 'Pelotas - RS',
  55533284 => 'Pelotas - RS',
  55533293 => 'Rio Grande - RS',
  55533301 => 'Pelotas - RS',
  55533302 => 'Pelotas - RS',
  55533303 => 'Pelotas - RS',
  55533304 => 'Santa Maria - RS',
  55533305 => 'Pelotas - RS',
  55533306 => 'Pelotas - RS',
  55533307 => 'Pelotas - RS',
  55533309 => 'Pelotas - RS',
  55533310 => 'Pelotas - RS',
  55533311 => 'Bagé - RS',
  55533312 => 'Bagé - RS',
  55533321 => 'Pelotas - RS',
  55533325 => 'Pelotas - RS',
  55533342 => 'Pelotas - RS',
  55533503 => 'Bagé - RS',
  55533517 => 'Pelotas - RS',
  55533611 => 'Portão - RS',
  55533613 => 'Pedras Altas - RS',
  55533717 => 'Rio Grande - RS',
  55533921 => 'Pelotas - RS',
  55533931 => 'Rio Grande - RS',
  55534001 => 'Pelotas - RS',
  55534007 => 'Pelotas - RS',
  55534052 => 'Pelotas - RS',
  55534062 => 'Pelotas - RS',
  55534141 => 'Pelotas - RS',
  5554 => 'Rio Grande do Sul',
  55542101 => 'Caxias do Sul - RS',
  55542102 => 'Bento Gonçalves - RS',
  55542103 => 'Passo Fundo - RS',
  55542104 => 'Passo Fundo - RS',
  55542105 => 'Bento Gonçalves - RS',
  55542106 => 'Erechim - RS',
  55542107 => 'Erechim - RS',
  55542108 => 'Caxias do Sul - RS',
  55542109 => 'Farroupilha - RS',
  55542521 => 'Bento Gonçalves - RS',
  55542621 => 'Bento Gonçalves - RS',
  55542628 => 'Farroupilha - RS',
  55542991 => 'Caxias do Sul - RS',
  55542992 => 'Caxias do Sul - RS',
  55543011 => 'Farroupilha - RS',
  55543015 => 'Erechim - RS',
  55543017 => 'Veranópolis - RS',
  55543021 => 'Caxias do Sul - RS',
  55543025 => 'Caxias do Sul - RS',
  55543026 => 'Caxias do Sul - RS',
  55543027 => 'Caxias do Sul - RS',
  55543028 => 'Caxias do Sul - RS',
  55543029 => 'Caxias do Sul - RS',
  55543031 => 'Canela - RS',
  55543032 => 'Flores da Cunha - RS',
  55543033 => 'Nova Petrópolis - RS',
  55543034 => 'São Marcos - RS',
  55543035 => 'Farroupilha - RS',
  55543036 => 'Gramado - RS',
  55543037 => 'Carlos Barbosa - RS',
  55543038 => 'Garibaldi - RS',
  55543042 => 'Farroupilha - RS',
  55543045 => 'Passo Fundo - RS',
  55543046 => 'Passo Fundo - RS',
  55543052 => 'Bento Gonçalves - RS',
  55543054 => 'Caxias do Sul - RS',
  55543055 => 'Bento Gonçalves - RS',
  55543056 => 'Farroupilha - RS',
  55543057 => 'Bento Gonçalves - RS',
  55543201 => 'Caxias do Sul - RS',
  55543202 => 'Caxias do Sul - RS',
  55543203 => 'Caxias do Sul - RS',
  55543204 => 'Caxias do Sul - RS',
  55543205 => 'Caxias do Sul - RS',
  55543206 => 'Caxias do Sul - RS',
  55543207 => 'São Brás - RS',
  55543208 => 'Caxias do Sul - RS',
  55543209 => 'Caxias do Sul - RS',
  55543210 => 'Passo Fundo - RS',
  55543211 => 'Caxias do Sul - RS',
  55543212 => 'Caxias do Sul - RS',
  55543213 => 'Caxias do Sul - RS',
  55543214 => 'Caxias do Sul - RS',
  55543215 => 'Caxias do Sul - RS',
  55543217 => 'Caxias do Sul - RS',
  55543219 => 'Caxias do Sul - RS',
  55543220 => 'Caxias do Sul - RS',
  55543221 => 'Caxias do Sul - RS',
  55543222 => 'Caxias do Sul - RS',
  55543223 => 'Caxias do Sul - RS',
  55543225 => 'Caxias do Sul - RS',
  55543226 => 'Caxias do Sul - RS',
  55543227 => 'Caxias do Sul - RS',
  55543228 => 'Caxias do Sul - RS',
  55543229 => 'Caxias do Sul - RS',
  55543231 => 'Vacaria - RS',
  55543232 => 'Vacaria - RS',
  55543233 => 'Ipê - RS',
  55543234 => 'São José dos Ausentes - RS',
  55543235 => 'Campestre da Serra - RS',
  55543237 => 'Bom Jesus - RS',
  55543238 => 'Caxias do Sul - RS',
  55543242 => 'Nova Prata - RS',
  55543244 => 'São Francisco de Paula - RS',
  55543251 => 'Cambará do Sul - RS',
  55543253 => 'Jaquirana - RS',
  55543259 => 'Nova Sardenha - RS',
  55543260 => 'Caravaggio - RS',
  55543261 => 'Farroupilha - RS',
  55543266 => 'Santa Lúcia do Piaí - RS',
  55543267 => 'Vila Seca - RS',
  55543268 => 'Farroupilha - RS',
  55543271 => 'São Jorge - RS',
  55543272 => 'Guabiju - RS',
  55543273 => 'Nova Bassano - RS',
  55543275 => 'Nova Araçá - RS',
  55543276 => 'Protásio Alves - RS',
  55543278 => 'Canela - RS',
  55543279 => 'Linha Oitenta - RS',
  55543280 => 'Pedras Brancas - RS',
  55543281 => 'Nova Petrópolis - RS',
  55543282 => 'Canela - RS',
  55543283 => 'Caxias do Sul - RS',
  55543284 => 'Caxias do Sul - RS',
  55543285 => 'Picada Café - RS',
  55543286 => 'Gramado - RS',
  55543287 => 'Vila Cristina - RS',
  55543288 => 'Gramado - RS',
  55543290 => 'Caxias do Sul - RS',
  55543291 => 'São Marcos - RS',
  55543292 => 'Flores da Cunha - RS',
  55543293 => 'Antônio Prado - RS',
  55543294 => 'Nova Roma do Sul - RS',
  55543295 => 'Gramado - RS',
  55543296 => 'Nova Pádua - RS',
  55543297 => 'Flores da Cunha - RS',
  55543311 => 'Passo Fundo - RS',
  55543312 => 'Passo Fundo - RS',
  55543313 => 'Passo Fundo - RS',
  55543314 => 'Passo Fundo - RS',
  55543316 => 'Passo Fundo - RS',
  55543317 => 'Passo Fundo - RS',
  55543318 => 'Passo Fundo - RS',
  55543319 => 'Montauri - RS',
  55543321 => 'Erechim - RS',
  55543322 => 'Quinze de Novembro - RS',
  55543323 => 'Nova Alvorada - RS',
  55543324 => 'Ibirubá - RS',
  55543325 => 'São José do Herval - RS',
  55543326 => 'Campos Borges - RS',
  55543327 => 'Passo Fundo - RS',
  55543329 => 'Carazinho - RS',
  55543330 => 'Carazinho - RS',
  55543331 => 'Carazinho - RS',
  55543332 => 'Não-Me-Toque - RS',
  55543333 => 'Chapada - RS',
  55543334 => 'Colorado - RS',
  55543336 => 'Ipiranga do Sul - RS',
  55543337 => 'Estação - RS',
  55543338 => 'Victor Graeff - RS',
  55543339 => 'Erebango - RS',
  55543340 => 'Vanini - RS',
  55543341 => 'Getúlio Vargas - RS',
  55543342 => 'Marau - RS',
  55543343 => 'Sananduva - RS',
  55543344 => 'Tapejara - RS',
  55543345 => 'Sertão - RS',
  55543346 => 'Ciríaco - RS',
  55543347 => 'Casca - RS',
  55543348 => 'Água Santa - RS',
  55543349 => 'São Domingos do Sul - RS',
  55543351 => 'David Canabarro - RS',
  55543352 => 'São José do Ouro - RS',
  55543353 => 'Caseiros - RS',
  55543354 => 'Esmeralda - RS',
  55543355 => 'Ibiraiaras - RS',
  55543356 => 'Barracão - RS',
  55543357 => 'Camargo - RS',
  55543358 => 'Lagoa Vermelha - RS',
  55543359 => 'Vila Maria - RS',
  55543360 => 'Nova Boa Vista - RS',
  55543361 => 'Sarandi - RS',
  55543362 => 'Nonoai - RS',
  55543363 => 'Constantina - RS',
  55543364 => 'Ronda Alta - RS',
  55543365 => 'Rondinha - RS',
  55543366 => 'Campinas do Sul - RS',
  55543367 => 'Três Palmeiras - RS',
  55543368 => 'Jacutinga - RS',
  55543369 => 'Barra Funda - RS',
  55543371 => 'Marau - RS',
  55543372 => 'Marcelino Ramos - RS',
  55543373 => 'São Valentim - RS',
  55543374 => 'Ibiaçá - RS',
  55543375 => 'Erval Grande - RS',
  55543376 => 'Aratiba - RS',
  55543377 => 'Santo Antônio do Planalto - RS',
  55543378 => 'Ernestina - RS',
  55543379 => 'Coxilha - RS',
  55543380 => 'Ibirapuitã - RS',
  55543381 => 'Soledade - RS',
  55543382 => 'Alto Alegre - RS',
  55543383 => 'Espumoso - RS',
  55543384 => 'Barros Cassal - RS',
  55543385 => 'Tapera - RS',
  55543386 => 'Muliterno - RS',
  55543387 => 'Selbach - RS',
  55543388 => 'Bento Gonçalves - RS',
  55543389 => 'Fontoura Xavier - RS',
  55543391 => 'Gaurama - RS',
  55543392 => 'Lagoa dos Três Cantos - RS',
  55543393 => 'Mormaço - RS',
  55543394 => 'Santo Antônio do Palma - RS',
  55543395 => 'Viadutos - RS',
  55543396 => 'Santo Expedito do Sul - RS',
  55543397 => 'Maximiliano de Almeida - RS',
  55543398 => 'Charrua - RS',
  55543401 => 'Farroupilha - RS',
  55543412 => 'Farroupilha - RS',
  55543421 => 'Gramado - RS',
  55543433 => 'Arco Verde - RS',
  55543434 => 'Silva Jardim - RS',
  55543435 => 'Boa Vista do Sul - RS',
  55543439 => 'Faria Lemos - RS',
  55543441 => 'Veranópolis - RS',
  55543443 => 'Guaporé - RS',
  55543444 => 'Serafina Corrêa - RS',
  55543445 => 'Fagundes Varela - RS',
  55543446 => 'Cotiporã - RS',
  55543447 => 'Vila Flores - RS',
  55543449 => 'Bento Gonçalves - RS',
  55543451 => 'Bento Gonçalves - RS',
  55543452 => 'Bento Gonçalves - RS',
  55543456 => 'Santa Tereza - RS',
  55543457 => 'Monte Belo do Sul - RS',
  55543458 => 'Tuiutí - RS',
  55543459 => 'Bento Gonçalves - RS',
  55543461 => 'Carlos Barbosa - RS',
  55543462 => 'Garibaldi - RS',
  55543463 => 'Rio Grande do Sul',
  55543464 => 'Garibaldi - RS',
  55543468 => 'Pinto Bandeira - RS',
  55543471 => 'Dois Lajeados - RS',
  55543472 => 'São Valentim do Sul - RS',
  55543476 => 'União da Serra - RS',
  55543477 => 'Paraí - RS',
  55543478 => 'Vista Alegre do Prata - RS',
  55543511 => 'Vacaria - RS',
  55543519 => 'Erechim - RS',
  55543520 => 'Erechim - RS',
  55543522 => 'Erechim - RS',
  55543523 => 'Barão de Cotegipe - RS',
  55543524 => 'Mariano Moro - RS',
  55543525 => 'Severiano de Almeida - RS',
  55543526 => 'Três Arroios - RS',
  55543527 => 'Áurea - RS',
  55543528 => 'Itatiba do Sul - RS',
  55543531 => 'Paim Filho - RS',
  55543532 => 'São João da Urtiga - RS',
  55543533 => 'Caxias do Sul - RS',
  55543534 => 'Caxias do Sul - RS',
  55543535 => 'Caxias do Sul - RS',
  55543536 => 'Caxias do Sul - RS',
  55543537 => 'Caxias do Sul - RS',
  55543541 => 'Trindade do Sul - RS',
  55543544 => 'Entre Rios do Sul - RS',
  55543546 => 'Faxinalzinho - RS',
  55543551 => 'Machadinho - RS',
  55543552 => 'Cacique Doble - RS',
  55543568 => 'Ponte Preta - RS',
  55543581 => 'Passo Fundo - RS',
  55543584 => 'Passo Fundo - RS',
  55543601 => 'Passo Fundo - RS',
  55543611 => 'André da Rocha - RS',
  55543612 => 'Muitos Capões - RS',
  55543614 => 'Rio dos Índios - RS',
  55543617 => 'Tapejara - RS',
  55543618 => 'Nonoai - RS',
  55543622 => 'Passo Fundo - RS',
  55543625 => 'Capão Bonito do Sul - RS',
  55543632 => 'Passo Fundo - RS',
  55543701 => 'Bento Gonçalves - RS',
  55543702 => 'Bento Gonçalves - RS',
  55543712 => 'Erechim - RS',
  55543717 => 'Nova Prata - RS',
  55543733 => 'Caxias do Sul - RS',
  55543902 => 'Passo Fundo - RS',
  55543905 => 'Gramado - RS',
  55543906 => 'Farroupilha - RS',
  55543908 => 'Vacaria - RS',
  55544001 => 'Caxias do Sul - RS',
  55544003 => 'Caxias do Sul - RS',
  55544007 => 'Caxias do Sul - RS',
  55544009 => 'Caxias do Sul - RS',
  55544052 => 'Passo Fundo - RS',
  55544062 => 'Caxias do Sul - RS',
  55544141 => 'Caxias do Sul - RS',
  5555 => 'Rio Grande do Sul',
  55552101 => 'Santa Maria - RS',
  55552102 => 'Uruguaiana - RS',
  55552103 => 'Santa Maria - RS',
  55553015 => 'Santa Maria - RS',
  55553025 => 'Santa Maria - RS',
  55553026 => 'Santa Maria - RS',
  55553027 => 'Santa Maria - RS',
  55553028 => 'Santa Maria - RS',
  55553032 => 'Santa Maria - RS',
  55553033 => 'Santa Maria - RS',
  55553201 => 'Santo Ângelo - RS',
  55553211 => 'Santa Maria - RS',
  55553212 => 'Santa Maria - RS',
  55553213 => 'Santa Maria - RS',
  55553214 => 'Santa Maria - RS',
  55553217 => 'Santa Maria - RS',
  55553218 => 'Santa Maria - RS',
  55553219 => 'Santa Maria - RS',
  55553220 => 'Santa Maria - RS',
  55553221 => 'Santa Maria - RS',
  55553222 => 'Santa Maria - RS',
  55553223 => 'Santa Maria - RS',
  55553224 => 'Silveira Martins - RS',
  55553225 => 'Santa Maria - RS',
  55553226 => 'Santa Maria - RS',
  55553227 => 'Itaara - RS',
  55553228 => 'Boca do Monte - RS',
  55553231 => 'Rosário do Sul - RS',
  55553232 => 'São Gabriel - RS',
  55553233 => 'São Sepé - RS',
  55553234 => 'Vila Nova do Sul - RS',
  55553236 => 'Formigueiro - RS',
  55553237 => 'São Gabriel - RS',
  55553241 => 'Santana do Livramento - RS',
  55553242 => 'Santana do Livramento - RS',
  55553243 => 'Santana do Livramento - RS',
  55553244 => 'Santana do Livramento - RS',
  55553249 => 'Santiago - RS',
  55553250 => 'Nova Esperança do Sul - RS',
  55553251 => 'Santiago - RS',
  55553252 => 'São Francisco de Assis - RS',
  55553254 => 'Cacequi - RS',
  55553255 => 'Jaguari - RS',
  55553256 => 'Manoel Viana - RS',
  55553257 => 'São Vicente do Sul - RS',
  55553258 => 'Nova Esperança do Sul - RS',
  55553259 => 'Mata - RS',
  55553261 => 'Restinga Seca - RS',
  55553262 => 'Paraíso do Sul - RS',
  55553263 => 'Faxinal do Soturno - RS',
  55553265 => 'Agudo - RS',
  55553266 => 'Nova Palma - RS',
  55553267 => 'Ivorá - RS',
  55553268 => 'Dona Francisca - RS',
  55553269 => 'São João do Polêsine - RS',
  55553270 => 'São Miguel - RS',
  55553271 => 'Júlio de Castilhos - RS',
  55553272 => 'Tupanciretã - RS',
  55553276 => 'São Pedro do Sul - RS',
  55553277 => 'São Martinho da Serra - RS',
  55553278 => 'Pinhal Grande - RS',
  55553279 => 'Quevedos - RS',
  55553281 => 'Caçapava do Sul - RS',
  55553282 => 'Lavras do Sul - RS',
  55553286 => 'Santa Maria - RS',
  55553289 => 'Vale Vêneto - RS',
  55553290 => 'Santa Maria - RS',
  55553301 => 'Santa Maria - RS',
  55553302 => 'Santa Maria - RS',
  55553303 => 'Cruz Alta - RS',
  55553304 => 'Santa Maria - RS',
  55553305 => 'Ijuí - RS',
  55553307 => 'Santa Maria - RS',
  55553308 => 'Ijuí - RS',
  55553311 => 'Santa Maria - RS',
  55553312 => 'Santo Ângelo - RS',
  55553313 => 'Santo Ângelo - RS',
  55553314 => 'Santo Ângelo - RS',
  55553317 => 'Santa Maria - RS',
  55553318 => 'Jóia - RS',
  55553321 => 'Cruz Alta - RS',
  55553322 => 'Cruz Alta - RS',
  55553324 => 'Cruz Alta - RS',
  55553326 => 'Cruz Alta - RS',
  55553327 => 'Salto do Jacuí - RS',
  55553328 => 'Fortaleza dos Valos - RS',
  55553329 => 'Entre Ijuís - RS',
  55553331 => 'Ijuí - RS',
  55553332 => 'Ijuí - RS',
  55553333 => 'Ijuí - RS',
  55553334 => 'Augusto Pestana - RS',
  55553335 => 'Eugênio de Castro - RS',
  55553336 => 'Catuípe - RS',
  55553338 => 'Nova Ramada - RS',
  55553343 => 'Cruz Alta - RS',
  55553347 => 'Santa Maria - RS',
  55553351 => 'Pirapó - RS',
  55553352 => 'São Luiz Gonzaga - RS',
  55553353 => 'Guarani das Missões - RS',
  55553354 => 'Porto Xavier - RS',
  55553355 => 'Caibaté - RS',
  55553356 => 'Bossoroca - RS',
  55553358 => 'Salvador das Missões - RS',
  55553359 => 'Cerro Largo - RS',
  55553361 => 'Giruá - RS',
  55553362 => 'Dezesseis de Novembro - RS',
  55553363 => 'São Nicolau - RS',
  55553365 => 'Roque Gonzales - RS',
  55553366 => 'Itacurubi - RS',
  55553367 => 'Santo Antônio das Missões - RS',
  55553369 => 'São Pedro do Butiá - RS',
  55553372 => 'Santa Bárbara do Sul - RS',
  55553373 => 'Saldanha Marinho - RS',
  55553375 => 'Panambi - RS',
  55553376 => 'Panambi - RS',
  55553377 => 'Pejuçara - RS',
  55553379 => 'Condor - RS',
  55553381 => 'São Miguel das Missões - RS',
  55553387 => 'Ajuricaba - RS',
  55553401 => 'Uruguaiana - RS',
  55553402 => 'Uruguaiana - RS',
  55553411 => 'Uruguaiana - RS',
  55553412 => 'Uruguaiana - RS',
  55553413 => 'Uruguaiana - RS',
  55553414 => 'Uruguaiana - RS',
  55553419 => 'Barra do Quaraí - RS',
  55553421 => 'Alegrete - RS',
  55553422 => 'Alegrete - RS',
  55553423 => 'Quaraí - RS',
  55553426 => 'Alegrete - RS',
  55553430 => 'São Borja - RS',
  55553431 => 'São Borja - RS',
  55553433 => 'Itaqui - RS',
  55553435 => 'Maçambara - RS',
  55553505 => 'Alegrete - RS',
  55553506 => 'São Francisco de Assis - RS',
  55553511 => 'Santa Rosa - RS',
  55553512 => 'Santa Rosa - RS',
  55553513 => 'Santa Rosa - RS',
  55553522 => 'Três Passos - RS',
  55553523 => 'Padre Gonzales - RS',
  55553524 => 'Crissiumal - RS',
  55553525 => 'Humaitá - RS',
  55553526 => 'Sede Nova - RS',
  55553528 => 'Campo Novo - RS',
  55553533 => 'São Martinho - RS',
  55553534 => 'Doutor Maurício Cardoso - RS',
  55553535 => 'Três de Maio - RS',
  55553536 => 'Alegria - RS',
  55553537 => 'Horizontina - RS',
  55553538 => 'Boa Vista do Buricá - RS',
  55553539 => 'Independência - RS',
  55553541 => 'Santo Cristo - RS',
  55553542 => 'Tucunduva - RS',
  55553543 => 'Tuparendi - RS',
  55553544 => 'Novo Machado - RS',
  55553545 => 'Porto Mauá - RS',
  55553546 => 'Alecrim - RS',
  55553548 => 'Cândido Godói - RS',
  55553551 => 'Tenente Portela - RS',
  55553552 => 'Vista Gaúcha - RS',
  55553554 => 'Miraguaí - RS',
  55553556 => 'Redentora - RS',
  55553557 => 'Coronel Bicaco - RS',
  55553559 => 'Braga - RS',
  55553563 => 'São Paulo das Missões - RS',
  55553565 => 'Porto Lucena - RS',
  55553567 => 'Campina das Missões - RS',
  55553595 => 'Tuparendi - RS',
  55553611 => 'Unistalda - RS',
  55553612 => 'Dilermando de Aguiar - RS',
  55553613 => 'Mato Queimado - RS',
  55553614 => 'Vitória das Missões - RS',
  55553615 => 'Santa Margarida do Sul - RS',
  55553617 => 'Tiradentes do Sul - RS',
  55553621 => 'Santana do Livramento - RS',
  55553629 => 'Jacuizinho - RS',
  55553643 => 'Boa Vista do Cadeado - RS',
  55553649 => 'Sanchuri - RS',
  55553730 => 'Vista Alegre - RS',
  55553737 => 'Vicente Dutra - RS',
  55553738 => 'Caiçara - RS',
  55553739 => 'Taquaruçu do Sul - RS',
  55553742 => 'Palmeira das Missões - RS',
  55553743 => 'Jaboticaba - RS',
  55553744 => 'Frederico Westphalen - RS',
  55553745 => 'Iraí - RS',
  55553746 => 'Seberi - RS',
  55553747 => 'Boa Vista das Missões - RS',
  55553748 => 'Erval Seco - RS',
  55553751 => 'Dois Irmãos das MissõEs - RS',
  55553752 => 'Ametista do Sul - RS',
  55553753 => 'São José das MissõEs - RS',
  55553754 => 'Pinhal - RS',
  55553755 => 'Liberato Salzano - RS',
  55553756 => 'Cerro Grande - RS',
  55553757 => 'Novo Barreiro - RS',
  55553781 => 'Santo Augusto - RS',
  55553784 => 'Chiapetta - RS',
  55553785 => 'Inhacorá - RS',
  55553791 => 'Palmitinho - RS',
  55553792 => 'Pinheirinho do Vale - RS',
  55553794 => 'Planalto - RS',
  55553796 => 'Alpestre - RS',
  55553797 => 'Novo Tiradentes - RS',
  55553798 => 'Rodeio Bonito - RS',
  55553816 => 'Condor - RS',
  55553884 => 'Dona Otília - RS',
  55553921 => 'Santa Maria - RS',
  55553931 => 'Santo Ângelo - RS',
  55554001 => 'Santa Maria - RS',
  55554007 => 'Santa Maria - RS',
  55554052 => 'Santa Maria - RS',
  55554062 => 'Santa Maria - RS',
  5561 => 'Federal District',
  55612020 => 'Brasília - DF',
  55612022 => 'Brasília - DF',
  55612023 => 'Brasília - DF',
  55612024 => 'Brasília - DF',
  55612027 => 'Brasília - DF',
  55612028 => 'Brasília - DF',
  55612029 => 'Brasília - DF',
  55612030 => 'Brasília - DF',
  55612099 => 'Brasília - DF',
  55612101 => 'Brasília - DF',
  55612102 => 'Brasília - DF',
  55612103 => 'Brasília - DF',
  55612104 => 'Brasília - DF',
  55612105 => 'Brasília - DF',
  55612106 => 'Brasília - DF',
  55612107 => 'Brasília - DF',
  55612108 => 'Brasília - DF',
  55612109 => 'Brasília - DF',
  55612141 => 'Brasília - DF',
  55612191 => 'Brasília - DF',
  55612192 => 'Brasília - DF',
  55612193 => 'Brasília - DF',
  55612194 => 'Brasília - DF',
  55612195 => 'Brasília - DF',
  55612196 => 'Brasília - DF',
  55612323 => 'Brasília - DF',
  55612328 => 'Brasília - DF',
  55613003 => 'Brasília - DF',
  55613004 => 'Brasília - DF',
  55613010 => 'Brasília - DF',
  55613012 => 'Brasília - DF',
  55613013 => 'Brasília - DF',
  55613024 => 'Brasília - DF',
  55613026 => 'Brasília - DF',
  55613027 => 'Taguatinga - DF',
  55613031 => 'Brasília - DF',
  55613032 => 'Brasília - DF',
  55613033 => 'Brasília - DF',
  55613034 => 'Brasília - DF',
  55613035 => 'Brasília - DF',
  55613036 => 'Brasília - DF',
  55613037 => 'Brasília - DF',
  55613038 => 'Brasília - DF',
  55613039 => 'Brasília - DF',
  55613041 => 'Brasília - DF',
  55613043 => 'Brasília - DF',
  55613044 => 'Taguatinga - DF',
  55613045 => 'Brasília - DF',
  55613048 => 'Taguatinga - DF',
  55613053 => 'Brasília - DF',
  55613054 => 'Brasília - DF',
  55613055 => 'Brasília - DF',
  55613081 => 'Brasília - DF',
  55613084 => 'Luziânia - GO',
  55613101 => 'Brasília - DF',
  55613107 => 'Brasília - DF',
  55613108 => 'Brasília - DF',
  55613114 => 'Brasília - DF',
  55613190 => 'Brasília - DF',
  55613201 => 'Brasília - DF',
  55613202 => 'Brasília - DF',
  55613203 => 'Brasília - DF',
  55613204 => 'Brasília - DF',
  55613207 => 'Brasília - DF',
  55613208 => 'Brasília - DF',
  55613209 => 'Brasília - DF',
  55613212 => 'Brasília - DF',
  55613213 => 'Brasília - DF',
  55613214 => 'Brasília - DF',
  55613217 => 'Brasília - DF',
  55613218 => 'Brasília - DF',
  55613221 => 'Brasília - DF',
  55613222 => 'Brasília - DF',
  55613223 => 'Brasília - DF',
  55613224 => 'Brasília - DF',
  55613225 => 'Brasília - DF',
  55613226 => 'Brasília - DF',
  55613233 => 'Guará - DF',
  55613234 => 'Guará - DF',
  55613241 => 'Olinda - PE',
  55613242 => 'Brasília - DF',
  55613243 => 'Brasília - DF',
  55613244 => 'Brasília - DF',
  55613245 => 'Brasília - DF',
  55613246 => 'Brasília - DF',
  55613247 => 'Brasília - DF',
  55613248 => 'Brasília - DF',
  55613251 => 'Brasília - DF',
  55613252 => 'Brasília - DF',
  55613253 => 'Brasília - DF',
  55613254 => 'Brasília - DF',
  55613255 => 'Brasília - DF',
  55613257 => 'Brasília - DF',
  55613261 => 'Brasília - DF',
  55613262 => 'Brasília - DF',
  55613263 => 'Brasília - DF',
  55613264 => 'Brasília - DF',
  55613271 => 'Belo Horizonte - MG',
  55613272 => 'Brasília - DF',
  55613273 => 'Brasília - DF',
  55613274 => 'Brasília - DF',
  55613275 => 'Brasília - DF',
  55613297 => 'Brasília - DF',
  55613298 => 'Brasília - DF',
  55613299 => 'Brasília - DF',
  55613301 => 'Brasília - DF',
  55613302 => 'Sobradinho - DF',
  55613303 => 'Brasília - DF',
  55613304 => 'Guará - DF',
  55613306 => 'Brasília - DF',
  55613307 => 'Brasília - DF',
  55613308 => 'Planaltina - DF',
  55613311 => 'Brasília - DF',
  55613312 => 'Brasília - DF',
  55613313 => 'Brasília - DF',
  55613314 => 'Brasília - DF',
  55613315 => 'Brasília - DF',
  55613316 => 'Brasília - DF',
  55613319 => 'Brasília - DF',
  55613321 => 'Brasília - DF',
  55613322 => 'Brasília - DF',
  55613323 => 'Brasília - DF',
  55613325 => 'Brasília - DF',
  55613326 => 'Brasília - DF',
  55613327 => 'Brasília - DF',
  55613328 => 'Brasília - DF',
  55613329 => 'Brasília - DF',
  55613331 => 'Recanto das Emas - DF',
  55613332 => 'Recanto das Emas - DF',
  55613333 => 'Recanto das Emas - DF',
  55613334 => 'Recanto das Emas - DF',
  55613335 => 'São Sebastião - DF',
  55613336 => 'Taguatinga - DF',
  55613338 => 'Brasília - DF',
  55613339 => 'Brasília - DF',
  55613340 => 'Brasília - DF',
  55613341 => 'Cruzeiro - DF',
  55613342 => 'Brasília - DF',
  55613343 => 'Brasília - DF',
  55613344 => 'Brasília - DF',
  55613345 => 'Brasília - DF',
  55613346 => 'Brasília - DF',
  55613347 => 'Brasília - DF',
  55613348 => 'Brasília - DF',
  55613349 => 'Brasília - DF',
  55613351 => 'Taguatinga - DF',
  55613352 => 'Taguatinga - DF',
  55613353 => 'Taguatinga - DF',
  55613354 => 'Brasília - DF',
  55613355 => 'Taguatinga - DF',
  55613356 => 'Taguatinga - DF',
  55613357 => 'Samambaia Sul - DF',
  55613358 => 'Samambaia Sul - DF',
  55613359 => 'Samambaia Sul - DF',
  55613361 => 'Guará - DF',
  55613362 => 'Guará - DF',
  55613363 => 'Guará - DF',
  55613364 => 'Brasília - DF',
  55613365 => 'Brasília - DF',
  55613366 => 'Brasília - DF',
  55613367 => 'Brasília - DF',
  55613368 => 'Brasília - DF',
  55613369 => 'Paranoá - DF',
  55613371 => 'Ceilândia - DF',
  55613372 => 'Ceilândia - DF',
  55613374 => 'Ceilândia - DF',
  55613375 => 'Ceilândia - DF',
  55613376 => 'Ceilândia - DF',
  55613377 => 'Ceilândia - DF',
  55613378 => 'Ceilândia - DF',
  55613379 => 'Ceilândia - DF',
  55613380 => 'Núcleo Bandeirante - DF',
  55613381 => 'Guará - DF',
  55613382 => 'Guará - DF',
  55613383 => 'Brasília - DF',
  55613384 => 'Federal District',
  55613385 => 'Federal District',
  55613386 => 'Núcleo Bandeirante - DF',
  55613387 => 'Sobradinho - DF',
  55613388 => 'Planaltina - DF',
  55613389 => 'Planaltina - DF',
  55613391 => 'Brazlândia - DF',
  55613392 => 'Santa Maria - DF',
  55613393 => 'Santa Maria - DF',
  55613394 => 'Santa Maria - DF',
  55613395 => 'Santa Maria - DF',
  55613397 => 'Taguatinga - DF',
  55613401 => 'Taguatinga - DF',
  55613408 => 'Brasília - DF',
  55613411 => 'Brasília - DF',
  55613415 => 'Brasília - DF',
  55613421 => 'Brasília - DF',
  55613424 => 'Brasília - DF',
  55613425 => 'Brasília - DF',
  55613426 => 'Brasília - DF',
  55613427 => 'Brasília - DF',
  55613429 => 'Brasília - DF',
  55613432 => 'Formosa - GO',
  55613433 => 'Brasília - DF',
  55613434 => 'Recanto das Emas - DF',
  55613435 => 'Brasília - DF',
  55613436 => 'Brasília - DF',
  55613441 => 'Brasília - DF',
  55613442 => 'Brasília - DF',
  55613443 => 'Brasília - DF',
  55613445 => 'Brasília - DF',
  55613447 => 'Brasília - DF',
  55613448 => 'Brasília - DF',
  55613453 => 'Sobradinho - DF',
  55613454 => 'Sobradinho - DF',
  55613456 => 'Taguatinga - DF',
  55613458 => 'Samambaia Sul - DF',
  55613459 => 'Samambaia Sul - DF',
  55613461 => 'Ceilândia - DF',
  55613465 => 'Brasília - DF',
  55613467 => 'Brasília - DF',
  55613468 => 'Brasília - DF',
  55613471 => 'Ceilândia - DF',
  55613475 => 'Brasília - DF',
  55613478 => 'Sobradinho - DF',
  55613479 => 'Brazlândia - DF',
  55613483 => 'Sobradinho - DF',
  55613484 => 'Federal District',
  55613485 => 'Sobradinho - DF',
  55613486 => 'Brasília - DF',
  55613487 => 'Sobradinho - DF',
  55613488 => 'Brasília - DF',
  55613489 => 'Planaltina - DF',
  55613491 => 'Brasília - DF',
  55613500 => 'Brasília - DF',
  55613501 => 'Planaltina - DF',
  55613502 => 'Luziânia - GO',
  55613503 => 'Planaltina - GO',
  55613504 => 'Cristalina - GO',
  55613505 => 'Luziânia - GO',
  55613506 => 'Brasília - DF',
  55613517 => 'Brasília - DF',
  55613521 => 'Brasília - DF',
  55613522 => 'Brasília - DF',
  55613525 => 'Brasília - DF',
  55613526 => 'Brasília - DF',
  55613532 => 'Brasília - DF',
  55613533 => 'Brasília - DF',
  55613535 => 'Brasília - DF',
  55613536 => 'Brasília - DF',
  55613540 => 'Brazlândia - DF',
  55613541 => 'Brasília - DF',
  55613542 => 'Brasília - DF',
  55613543 => 'Brasília - DF',
  55613546 => 'Brasília - DF',
  55613547 => 'Brasília - DF',
  55613548 => 'Brasília - DF',
  55613551 => 'Brasília - DF',
  55613552 => 'Núcleo Bandeirante - DF',
  55613553 => 'Brasília - DF',
  55613554 => 'Brasília - DF',
  55613556 => 'Federal District',
  55613559 => 'Samambaia Sul - DF',
  55613561 => 'Taguatinga - DF',
  55613562 => 'Taguatinga - DF',
  55613563 => 'Taguatinga - DF',
  55613567 => 'Guará - DF',
  55613568 => 'Guará - DF',
  55613573 => 'Brasília - DF',
  55613574 => 'Brasília - DF',
  55613577 => 'Brasília - DF',
  55613578 => 'Brasília - DF',
  55613581 => 'Ceilândia - DF',
  55613585 => 'Ceilândia - DF',
  55613591 => 'Sobradinho - DF',
  55613595 => 'Sobradinho - DF',
  55613597 => 'Taguatinga - DF',
  55613601 => 'Luziânia - GO',
  55613603 => 'Luziânia - GO',
  55613605 => 'Cidade Ocidental - GO',
  55613606 => 'Santo Antônio do Descoberto - GO',
  55613607 => 'Distrito de Campos Lindos - GO',
  55613608 => 'Novo Gama - GO',
  55613612 => 'Cristalina - GO',
  55613613 => 'Águas Lindas de Goiás - GO',
  55613614 => 'Novo Gama - GO',
  55613616 => 'Águas Lindas de Goiás - GO',
  55613617 => 'Águas Lindas de Goiás - GO',
  55613618 => 'Águas Lindas de Goiás - GO',
  55613619 => 'Águas Lindas de Goiás - GO',
  55613620 => 'Luziânia - GO',
  55613621 => 'Luziânia - GO',
  55613622 => 'Luziânia - GO',
  55613623 => 'Luziânia - GO',
  55613624 => 'Valparaíso de Goiás - GO',
  55613625 => 'Cidade Ocidental - GO',
  55613626 => 'Santo Antônio do Descoberto - GO',
  55613627 => 'Valparaíso de Goiás - GO',
  55613628 => 'Novo Gama - GO',
  55613629 => 'Valparaíso de Goiás - GO',
  55613631 => 'Formosa - GO',
  55613632 => 'Formosa - GO',
  55613633 => 'Padre Bernardo - GO',
  55613634 => 'Padre Bernardo - GO',
  55613636 => 'Cabeceiras - GO',
  55613637 => 'Planaltina - GO',
  55613639 => 'Planaltina - GO',
  55613642 => 'Formosa - GO',
  55613669 => 'Valparaíso de Goiás - GO',
  55613677 => 'Planaltina - GO',
  55613679 => 'São Gabriel de Goiás - GO',
  55613689 => 'Formosa - GO',
  55613697 => 'Taboquinha - GO',
  55613701 => 'Brasília - DF',
  55613702 => 'Brasília - DF',
  55613703 => 'Brasília - DF',
  55613704 => 'Brasília - DF',
  55613717 => 'Brasília - DF',
  55613718 => 'Formosa - GO',
  55613797 => 'Brasília - DF',
  55613799 => 'Brasília - DF',
  55613877 => 'Brasília - DF',
  55613878 => 'Brasília - DF',
  55613879 => 'Brasília - DF',
  55613906 => 'Luziânia - GO',
  55613961 => 'Brasília - DF',
  55613962 => 'Brasília - DF',
  55613963 => 'Brasília - DF',
  55613964 => 'Brasília - DF',
  55613966 => 'Brasília - DF',
  55613981 => 'Formosa - GO',
  55614001 => 'Brasília - DF',
  55614003 => 'Brasília - DF',
  55614007 => 'Brasília - DF',
  55614009 => 'Brasília - DF',
  55614020 => 'Brasília - DF',
  55614062 => 'Brasília - DF',
  55614063 => 'Brasília - DF',
  55614101 => 'Brasília - DF',
  55614102 => 'Brasília - DF',
  55614103 => 'Brasília - DF',
  55614141 => 'Brasília - DF',
  55614501 => 'Brasília - DF',
  5562 => 'Goiás',
  55622764 => 'Goiânia - GO',
  55622765 => 'Goiânia - GO',
  55623004 => 'Goiânia - GO',
  55623010 => 'Senador Canedo - GO',
  55623015 => 'Goiânia - GO',
  55623016 => 'Aparecida de Goiânia - GO',
  55623085 => 'Aparecida de Goiânia - GO',
  55623086 => 'Goiânia - GO',
  55623087 => 'Goiânia - GO',
  55623088 => 'Goiânia - GO',
  55623089 => 'Goiânia - GO',
  55623091 => 'Goiânia - GO',
  55623092 => 'Goiânia - GO',
  55623093 => 'Goiânia - GO',
  55623094 => 'Aparecida de Goiânia - GO',
  55623095 => 'Goiânia - GO',
  55623096 => 'Goiânia - GO',
  55623097 => 'Aparecida de Goiânia - GO',
  55623098 => 'Anápolis - GO',
  55623099 => 'Anápolis - GO',
  55623142 => 'Goiânia - GO',
  55623201 => 'Goiânia - GO',
  55623202 => 'Goiânia - GO',
  55623204 => 'Goiânia - GO',
  55623205 => 'Goiânia - GO',
  55623206 => 'Goiânia - GO',
  55623207 => 'Goiânia - GO',
  55623208 => 'Goiânia - GO',
  55623210 => 'Goiânia - GO',
  55623211 => 'Goiânia - GO',
  55623212 => 'Goiânia - GO',
  55623213 => 'Goiânia - GO',
  55623214 => 'Goiânia - GO',
  55623215 => 'Goiânia - GO',
  55623217 => 'Goiânia - GO',
  55623218 => 'Goiânia - GO',
  55623220 => 'Goiânia - GO',
  55623221 => 'Goiânia - GO',
  55623222 => 'Goiânia - GO',
  55623223 => 'Goiânia - GO',
  55623224 => 'Goiânia - GO',
  55623225 => 'Goiânia - GO',
  55623227 => 'Goiânia - GO',
  55623228 => 'Goiânia - GO',
  55623229 => 'Goiânia - GO',
  55623232 => 'Goiânia - GO',
  55623233 => 'Goiânia - GO',
  55623234 => 'Goiânia - GO',
  55623237 => 'Goiânia - GO',
  55623238 => 'Goiânia - GO',
  55623241 => 'Goiânia - GO',
  55623243 => 'Goiânia - GO',
  55623245 => 'Goiânia - GO',
  55623246 => 'Goiânia - GO',
  55623247 => 'Goiânia - GO',
  55623248 => 'Aparecida de Goiânia - GO',
  55623249 => 'Goiânia - GO',
  55623251 => 'Goiânia - GO',
  55623252 => 'Goiânia - GO',
  55623253 => 'Goiânia - GO',
  55623255 => 'Goiânia - GO',
  55623256 => 'Goiânia - GO',
  55623258 => 'Goiânia - GO',
  55623259 => 'Goiânia - GO',
  55623261 => 'Goiânia - GO',
  55623264 => 'Goiânia - GO',
  55623265 => 'Goiânia - GO',
  55623267 => 'Goiânia - GO',
  55623268 => 'Goiânia - GO',
  55623272 => 'Goiânia - GO',
  55623273 => 'Goiânia - GO',
  55623274 => 'Goiânia - GO',
  55623275 => 'Goiânia - GO',
  55623277 => 'Aparecida de Goiânia - GO',
  55623280 => 'Aparecida de Goiânia - GO',
  55623281 => 'Goiânia - GO',
  55623282 => 'Aparecida de Goiânia - GO',
  55623283 => 'Aparecida de Goiânia - GO',
  55623284 => 'Goiânia - GO',
  55623285 => 'Goiânia - GO',
  55623286 => 'Goiânia - GO',
  55623287 => 'Goiânia - GO',
  55623288 => 'Aparecida de Goiânia - GO',
  55623289 => 'Goiânia - GO',
  55623290 => 'Goiânia - GO',
  55623291 => 'Goiânia - GO',
  55623292 => 'Goiânia - GO',
  55623293 => 'Goiânia - GO',
  55623296 => 'Goiânia - GO',
  55623297 => 'Goiânia - GO',
  55623298 => 'Goiânia - GO',
  55623299 => 'Goiânia - GO',
  55623301 => 'Leopoldo de Bulhões - GO',
  55623302 => 'Jussara - GO',
  55623303 => 'Montes Claros de Goiás - GO',
  55623305 => 'São Francisco de Goiás - GO',
  55623307 => 'Ceres - GO',
  55623310 => 'Anápolis - GO',
  55623311 => 'Anápolis - GO',
  55623312 => 'Itapuranga - GO',
  55623314 => 'Anápolis - GO',
  55623316 => 'Anápolis - GO',
  55623317 => 'Anápolis - GO',
  55623319 => 'Anápolis - GO',
  55623320 => 'Anápolis - GO',
  55623321 => 'Anápolis - GO',
  55623323 => 'Ceres - GO',
  55623324 => 'Anápolis - GO',
  55623325 => 'Rubiataba - GO',
  55623326 => 'Jaraguá - GO',
  55623327 => 'Anápolis - GO',
  55623328 => 'Anápolis - GO',
  55623329 => 'Anápolis - GO',
  55623331 => 'Pirenópolis - GO',
  55623332 => 'Silvânia - GO',
  55623334 => 'Petrolina de Goiás - GO',
  55623335 => 'Vianópolis - GO',
  55623336 => 'Alexânia - GO',
  55623338 => 'Corumbá de Goiás - GO',
  55623340 => 'São Patrício - GO',
  55623341 => 'Goianápolis - GO',
  55623342 => 'Ipiranga de Goiás - GO',
  55623343 => 'Abadiânia - GO',
  55623344 => 'Uruana - GO',
  55623347 => 'Campinorte - GO',
  55623348 => 'Mozarlândia - GO',
  55623349 => 'Hidrolina - GO',
  55623351 => 'Campos Verdes - GO',
  55623353 => 'Goianésia - GO',
  55623354 => 'Niquelândia - GO',
  55623355 => 'Itapuranga - GO',
  55623356 => 'Nova Veneza - GO',
  55623357 => 'Uruaçu - GO',
  55623358 => 'Santa Isabel - GO',
  55623359 => 'Jesúpolis - GO',
  55623361 => 'Itapaci - GO',
  55623362 => 'Porangatu - GO',
  55623363 => 'Porangatu - GO',
  55623364 => 'São Miguel do Araguaia - GO',
  55623365 => 'Crixás - GO',
  55623366 => 'Mara Rosa - GO',
  55623367 => 'Porangatu - GO',
  55623370 => 'Montes Claros de Goiás - GO',
  55623371 => 'Goiás - GO',
  55623372 => 'Goiás - GO',
  55623373 => 'Jussara - GO',
  55623374 => 'Itapirapuã - GO',
  55623375 => 'Itaberaí - GO',
  55623376 => 'Aruanã - GO',
  55623377 => 'Formoso - GO',
  55623378 => 'Itauçu - GO',
  55623379 => 'Minaçu - GO',
  55623380 => 'Araguapaz - GO',
  55623381 => 'Estrela do Norte - GO',
  55623382 => 'Fazenda Nova - GO',
  55623383 => 'Britânia - GO',
  55623384 => 'Taquaral de Goiás - GO',
  55623385 => 'Nova Crixás - GO',
  55623386 => 'Faina - GO',
  55623387 => 'Anápolis - GO',
  55623389 => 'Goianésia - GO',
  55623391 => 'Mundo Novo - GO',
  55623393 => 'Bonópolis - GO',
  55623394 => 'Santa Rita do Novo Destino - GO',
  55623396 => 'Itaguari - GO',
  55623397 => 'Rialma - GO',
  55623398 => 'Itaguaru - GO',
  55623404 => 'Assunção de Goiás - GO',
  55623406 => 'Buritinópolis - GO',
  55623407 => 'São Miguel do Passa Quatro - GO',
  55623412 => 'Goiânia - GO',
  55623421 => 'Alvorada do Norte - GO',
  55623425 => 'São Domingos - GO',
  55623429 => 'Posse - GO',
  55623432 => 'Goiânia - GO',
  55623434 => 'Goiânia - GO',
  55623438 => 'São João D\'Aliança - GO',
  55623441 => 'Catalão - GO',
  55623445 => 'Damianópolis - GO',
  55623446 => 'Alto Paraíso de Goiás - GO',
  55623448 => 'Flores de Goiás - GO',
  55623449 => 'Guarani de Goiás - GO',
  55623451 => 'Campos Belos - GO',
  55623455 => 'Povoado de São Jorge - GO',
  55623456 => 'Divinópolis de Goiás - GO',
  55623457 => 'Monte Alegre de Goiás - GO',
  55623459 => 'Alto Paraíso de Goiás - GO',
  55623461 => 'Pires do Rio - GO',
  55623463 => 'Mimoso de Goiás - GO',
  55623464 => 'Água Fria de Goiás - GO',
  55623466 => 'Vila Boa - GO',
  55623467 => 'Teresina de Goiás - GO',
  55623473 => 'Iaciara - GO',
  55623476 => 'Colinas do Tocantins - TO',
  55623481 => 'Posse - GO',
  55623482 => 'Nova Roma - GO',
  55623483 => 'Sítio D\'Abadia - GO',
  55623484 => 'Mambaí - GO',
  55623486 => 'Colinas do Sul - GO',
  55623488 => 'Simolândia - GO',
  55623494 => 'Cavalcante - GO',
  55623502 => 'Bela Vista de Goiás - GO',
  55623503 => 'Abadia de Goiás - GO',
  55623505 => 'Trindade - GO',
  55623506 => 'Trindade - GO',
  55623511 => 'Inhumas - GO',
  55623512 => 'Senador Canedo - GO',
  55623513 => 'Nerópolis - GO',
  55623514 => 'Inhumas - GO',
  55623515 => 'Goiânia - GO',
  55623516 => 'Goianira - GO',
  55623517 => 'Goiânia - GO',
  55623518 => 'Aparecida de Goiânia - GO',
  55623519 => 'Goiânia - GO',
  55623520 => 'Goiânia - GO',
  55623522 => 'Goiânia - GO',
  55623523 => 'Goiânia - GO',
  55623524 => 'Goiânia - GO',
  55623526 => 'Goiânia - GO',
  55623527 => 'Araçu - GO',
  55623528 => 'Caturaí - GO',
  55623529 => 'Brazabrantes - GO',
  55623532 => 'Senador Canedo - GO',
  55623533 => 'Goiânia - GO',
  55623534 => 'Goiânia - GO',
  55623535 => 'Santo Antônio de Goiás - GO',
  55623536 => 'Goiânia - GO',
  55623537 => 'Aparecida de Goiânia - GO',
  55623538 => 'Goiânia - GO',
  55623539 => 'Goiânia - GO',
  55623541 => 'Goiânia - GO',
  55623542 => 'Goiânia - GO',
  55623545 => 'Goiânia - GO',
  55623546 => 'Goiânia - GO',
  55623548 => 'Aparecida de Goiânia - GO',
  55623549 => 'Aparecida de Goiânia - GO',
  55623550 => 'Aragoiânia - GO',
  55623551 => 'Bela Vista de Goiás - GO',
  55623552 => 'Guapó - GO',
  55623553 => 'Hidrolândia - GO',
  55623554 => 'Varjão - GO',
  55623557 => 'Campestre de Goiás - GO',
  55623558 => 'Goiânia - GO',
  55623561 => 'Caldazinha - GO',
  55623565 => 'Goiânia - GO',
  55623567 => 'Goiânia - GO',
  55623572 => 'Goiânia - GO',
  55623573 => 'Goiânia - GO',
  55623575 => 'Goiânia - GO',
  55623578 => 'Aparecida de Goiânia - GO',
  55623579 => 'Goiânia - GO',
  55623581 => 'Goiânia - GO',
  55623582 => 'Goiânia - GO',
  55623583 => 'Goiânia - GO',
  55623584 => 'Aparecida de Goiânia - GO',
  55623586 => 'Goiânia - GO',
  55623587 => 'Aparecida de Goiânia - GO',
  55623588 => 'Aparecida de Goiânia - GO',
  55623589 => 'Goiânia - GO',
  55623591 => 'Goiânia - GO',
  55623592 => 'Goiânia - GO',
  55623593 => 'Goiânia - GO',
  55623594 => 'Aparecida de Goiânia - GO',
  55623595 => 'Goiânia - GO',
  55623596 => 'Aparecida de Goiânia - GO',
  55623597 => 'Goiânia - GO',
  55623598 => 'Aparecida de Goiânia - GO',
  55623605 => 'Goiânia - GO',
  55623607 => 'Goiânia - GO',
  55623608 => 'Goiânia - GO',
  55623609 => 'Goiânia - GO',
  55623611 => 'Aparecida de Goiânia - GO',
  55623612 => 'Goiânia - GO',
  55623621 => 'Goiânia - GO',
  55623622 => 'Goiânia - GO',
  55623624 => 'Goiânia - GO',
  55623625 => 'Aparecida de Goiânia - GO',
  55623626 => 'Goiânia - GO',
  55623628 => 'Novo Gama - GO',
  55623631 => 'Jataí - GO',
  55623636 => 'Goiânia - GO',
  55623637 => 'Goiânia - GO',
  55623639 => 'Goiânia - GO',
  55623642 => 'Goiânia - GO',
  55623643 => 'Goiânia - GO',
  55623645 => 'Goiânia - GO',
  55623661 => 'Goiânia - GO',
  55623683 => 'Santa Bárbara de Goiás - GO',
  55623877 => 'Goiânia - GO',
  55623878 => 'Goiânia - GO',
  55623920 => 'Goiânia - GO',
  55623921 => 'Goiânia - GO',
  55623922 => 'Goiânia - GO',
  55623923 => 'Goiânia - GO',
  55623928 => 'Goiânia - GO',
  55623931 => 'Goiânia - GO',
  55623932 => 'Goiânia - GO',
  55623933 => 'Goiânia - GO',
  55623937 => 'Anápolis - GO',
  55623941 => 'Goiânia - GO',
  55623942 => 'Goiânia - GO',
  55623945 => 'Goiânia - GO',
  55623946 => 'Goiânia - GO',
  55623952 => 'Aparecida de Goiânia - GO',
  55623954 => 'Goiânia - GO',
  55623956 => 'Goiânia - GO',
  55623959 => 'Niquelândia - GO',
  55623978 => 'Anápolis - GO',
  55623979 => 'Anápolis - GO',
  55623981 => 'Aparecida de Goiânia - GO',
  55623983 => 'Aparecida de Goiânia - GO',
  55623988 => 'Goiânia - GO',
  55623995 => 'Goiânia - GO',
  55623997 => 'Goiânia - GO',
  55623998 => 'Goiânia - GO',
  55623999 => 'Goiânia - GO',
  55624001 => 'Goiânia - GO',
  55624003 => 'Goiânia - GO',
  55624005 => 'Goiânia - GO',
  55624006 => 'Goiânia - GO',
  55624007 => 'Goiânia - GO',
  55624008 => 'Goiânia - GO',
  55624009 => 'Goiânia - GO',
  55624011 => 'Goiânia - GO',
  55624012 => 'Goiânia - GO',
  55624014 => 'Anápolis - GO',
  55624015 => 'Anápolis - GO',
  55624017 => 'Aparecida de Goiânia - GO',
  55624051 => 'Anápolis - GO',
  55624052 => 'Goiânia - GO',
  55624053 => 'Goiânia - GO',
  55624101 => 'Goiânia - GO',
  55624103 => 'Aparecida de Goiânia - GO',
  55624104 => 'Goiânia - GO',
  55624105 => 'Senador Canedo - GO',
  55624106 => 'Trindade - GO',
  55624109 => 'Goiânia - GO',
  55624141 => 'Goiânia - GO',
  5563 => 'Tocantins',
  55632111 => 'Palmas - TO',
  55632112 => 'Araguaína - TO',
  55633014 => 'Palmas - TO',
  55633015 => 'Palmas - TO',
  55633025 => 'Palmas - TO',
  55633026 => 'Palmas - TO',
  55633028 => 'Palmas - TO',
  55633212 => 'Palmas - TO',
  55633213 => 'Palmas - TO',
  55633214 => 'Palmas - TO',
  55633215 => 'Palmas - TO',
  55633216 => 'Palmas - TO',
  55633217 => 'Palmas - TO',
  55633218 => 'Palmas - TO',
  55633219 => 'Palmas - TO',
  55633221 => 'Palmas - TO',
  55633223 => 'Palmas - TO',
  55633224 => 'Palmas - TO',
  55633225 => 'Palmas - TO',
  55633228 => 'Palmas - TO',
  55633229 => 'Palmas - TO',
  55633232 => 'Palmas - TO',
  55633233 => 'Palmas - TO',
  55633234 => 'Palmas - TO',
  55633301 => 'Gurupi - TO',
  55633311 => 'Gurupi - TO',
  55633312 => 'Gurupi - TO',
  55633313 => 'Gurupi - TO',
  55633314 => 'Gurupi - TO',
  55633315 => 'Gurupi - TO',
  55633316 => 'Gurupi - TO',
  55633321 => 'Araguaína - TO',
  55633322 => 'Palmas - TO',
  55633344 => 'Carrasco Bonito - TO',
  55633351 => 'Gurupi - TO',
  55633352 => 'Crixás do Tocantins - TO',
  55633353 => 'Alvorada - TO',
  55633354 => 'Cristalândia - TO',
  55633355 => 'Miranorte - TO',
  55633356 => 'Peixe - TO',
  55633357 => 'Formoso do Araguaia - TO',
  55633358 => 'Dueré - TO',
  55633359 => 'São Valério da Natividade - TO',
  55633361 => 'Paraíso do Tocantins - TO',
  55633362 => 'Dois Irmãos do Tocantins - TO',
  55633363 => 'Porto Nacional - TO',
  55633364 => 'Lagoa da Confusão - TO',
  55633365 => 'Fátima - TO',
  55633366 => 'Miracema do Tocantins - TO',
  55633367 => 'Tocantínia - TO',
  55633368 => 'Pium - TO',
  55633369 => 'Novo Acordo - TO',
  55633371 => 'Paranã - TO',
  55633372 => 'Natividade - TO',
  55633373 => 'Almas - TO',
  55633374 => 'Figueirópolis - TO',
  55633375 => 'Pindorama do Tocantins - TO',
  55633376 => 'Barrolândia - TO',
  55633377 => 'Aliança do Tocantins - TO',
  55633378 => 'Ponte Alta do Tocantins - TO',
  55633379 => 'Caseara - TO',
  55633381 => 'Conceição do Tocantins - TO',
  55633383 => 'Cariri do Tocantins - TO',
  55633384 => 'Araguaçu - TO',
  55633385 => 'Talismã - TO',
  55633386 => 'Palmeirópolis - TO',
  55633387 => 'Jaú do Tocantins - TO',
  55633388 => 'Santa Rosa do Tocantins - TO',
  55633389 => 'Abreulândia - TO',
  55633393 => 'Chapada da Natividade - TO',
  55633394 => 'Sandolândia - TO',
  55633396 => 'São Salvador do Tocantins - TO',
  55633397 => 'Pugmil - TO',
  55633399 => 'Sucupira - TO',
  55633402 => 'Araguaína - TO',
  55633411 => 'Araguaína - TO',
  55633412 => 'Araguaína - TO',
  55633413 => 'Araguaína - TO',
  55633414 => 'Araguaína - TO',
  55633415 => 'Araguaína - TO',
  55633416 => 'Araguaína - TO',
  55633421 => 'Araguaína - TO',
  55633422 => 'Bernardo Sayão - TO',
  55633423 => 'Darcinópolis - TO',
  55633424 => 'Goianorte - TO',
  55633425 => 'Pau D\'Arco - TO',
  55633426 => 'São Sebastião do Tocantins - TO',
  55633427 => 'Pequizeiro - TO',
  55633428 => 'Araguanã - TO',
  55633429 => 'Muricilândia - TO',
  55633430 => 'Carmolândia - TO',
  55633431 => 'Angico - TO',
  55633432 => 'Bandeirantes do Tocantins - TO',
  55633433 => 'Palmeiras do Tocantins - TO',
  55633434 => 'Juarina - TO',
  55633435 => 'Arapoema - TO',
  55633437 => 'Cachoeirinha - TO',
  55633438 => 'Recursolândia - TO',
  55633439 => 'Itacajá - TO',
  55633440 => 'Fortaleza do Tabocão - TO',
  55633442 => 'Ananás - TO',
  55633444 => 'Axixá do Tocantins - TO',
  55633446 => 'Sítio Novo do Tocantins - TO',
  55633447 => 'São Miguel do Tocantins - TO',
  55633448 => 'Babaçulândia - TO',
  55633449 => 'Tupiratins - TO',
  55633451 => 'Rio Sono - TO',
  55633452 => 'Nova Olinda - TO',
  55633453 => 'Wanderlândia - TO',
  55633454 => 'Aguiarnópolis - TO',
  55633455 => 'Nazaré - TO',
  55633456 => 'Augustinópolis - TO',
  55633457 => 'Colméia - TO',
  55633459 => 'Buriti do Tocantins - TO',
  55633461 => 'Brasilândia do Tocantins - TO',
  55633463 => 'Aragominas - TO',
  55633464 => 'Guaraí - TO',
  55633465 => 'Itapiratins - TO',
  55633466 => 'Pedro Afonso - TO',
  55633467 => 'Presidente Kennedy - TO',
  55633468 => 'Couto de Magalhães - TO',
  55633469 => 'Goiatins - TO',
  55633470 => 'Santa Fé do Araguaia - TO',
  55633471 => 'Tocantinópolis - TO',
  55633472 => 'Araguacema - TO',
  55633473 => 'Xambioá - TO',
  55633474 => 'Araguatins - TO',
  55633475 => 'Esperantina - TO',
  55633476 => 'Colinas do Tocantins - TO',
  55633477 => 'Itaguatins - TO',
  55633478 => 'Filadélfia - TO',
  55633479 => 'Piraquê - TO',
  55633483 => 'Bom Jesus do Tocantins - TO',
  55633484 => 'Campos Lindos - TO',
  55633487 => 'São Bento do Tocantins - TO',
  55633488 => 'Praia Norte - TO',
  55633491 => 'Luzinópolis - TO',
  55633493 => 'Palmeirante - TO',
  55633494 => 'Barra do Ouro - TO',
  55633497 => 'Tupirama - TO',
  55633509 => 'Dianópolis - TO',
  55633519 => 'Lajeado - TO',
  55633520 => 'Nova Rosalândia - TO',
  55633521 => 'Brejinho de Nazaré - TO',
  55633522 => 'Lagoa do Tocantins - TO',
  55633524 => 'Porto Alegre do Tocantins - TO',
  55633527 => 'Santa Tereza do Tocantins - TO',
  55633530 => 'Rio dos Bois - TO',
  55633531 => 'Divinópolis do Tocantins - TO',
  55633534 => 'Mateiros - TO',
  55633535 => 'Marianópolis do Tocantins - TO',
  55633538 => 'Aparecida do Rio Negro - TO',
  55633539 => 'Lizarda - TO',
  55633540 => 'Monte do Carmo - TO',
  55633542 => 'Silvanópolis - TO',
  55633547 => 'Porto Nacional - TO',
  55633554 => 'Taquarussu do Porto - TO',
  55633571 => 'Palmas - TO',
  55633572 => 'Palmas - TO',
  55633602 => 'Paraíso do Tocantins - TO',
  55633612 => 'Gurupi - TO',
  55633653 => 'Arraias - TO',
  55633654 => 'Taguatinga - TO',
  55633658 => 'Aurora do Tocantins - TO',
  55633659 => 'Ponte Alta do Bom Jesus - TO',
  55633685 => 'Combinado - TO',
  55633691 => 'Rio da Conceição - TO',
  55633692 => 'Dianópolis - TO',
  55633695 => 'Novo Alegre - TO',
  55633696 => 'Novo Jardim - TO',
  55634001 => 'Palmas - TO',
  55634003 => 'Palmas - TO',
  55634007 => 'Palmas - TO',
  55634009 => 'Palmas - TO',
  55634052 => 'Palmas - TO',
  55634101 => 'Palmas - TO',
  55634141 => 'Palmas - TO',
  5564 => 'Goiás',
  55642101 => 'Rio Verde - GO',
  55642102 => 'Jataí - GO',
  55642103 => 'Itumbiara - GO',
  55642104 => 'Rio Verde - GO',
  55643014 => 'Jataí - GO',
  55643018 => 'Itumbiara - GO',
  55643051 => 'Rio Verde - GO',
  55643054 => 'Rio Verde - GO',
  55643071 => 'Rio Verde - GO',
  55643086 => 'Itumbiara - GO',
  55643087 => 'Itumbiara - GO',
  55643088 => 'Itumbiara - GO',
  55643089 => 'Itumbiara - GO',
  55643091 => 'Itumbiara - GO',
  55643092 => 'Itumbiara - GO',
  55643093 => 'Itumbiara - GO',
  55643094 => 'Itumbiara - GO',
  55643095 => 'Itumbiara - GO',
  55643096 => 'Itumbiara - GO',
  55643097 => 'Itumbiara - GO',
  55643202 => 'Itumbiara - GO',
  55643203 => 'Itumbiara - GO',
  55643204 => 'Itumbiara - GO',
  55643205 => 'Itumbiara - GO',
  55643206 => 'Itumbiara - GO',
  55643207 => 'Itumbiara - GO',
  55643208 => 'Itumbiara - GO',
  55643209 => 'Itumbiara - GO',
  55643210 => 'Itumbiara - GO',
  55643211 => 'Itumbiara - GO',
  55643212 => 'Itumbiara - GO',
  55643213 => 'Itumbiara - GO',
  55643214 => 'Itumbiara - GO',
  55643215 => 'Itumbiara - GO',
  55643216 => 'Itumbiara - GO',
  55643217 => 'Itumbiara - GO',
  55643218 => 'Itumbiara - GO',
  55643219 => 'Itumbiara - GO',
  55643221 => 'Itumbiara - GO',
  55643223 => 'Itumbiara - GO',
  55643224 => 'Itumbiara - GO',
  55643225 => 'Itumbiara - GO',
  55643226 => 'Itumbiara - GO',
  55643229 => 'Itumbiara - GO',
  55643231 => 'Itumbiara - GO',
  55643232 => 'Itumbiara - GO',
  55643233 => 'Itumbiara - GO',
  55643234 => 'Itumbiara - GO',
  55643236 => 'Itumbiara - GO',
  55643237 => 'Itumbiara - GO',
  55643238 => 'Itumbiara - GO',
  55643239 => 'Itumbiara - GO',
  55643240 => 'Itumbiara - GO',
  55643241 => 'Itumbiara - GO',
  55643242 => 'Itumbiara - GO',
  55643245 => 'Itumbiara - GO',
  55643246 => 'Itumbiara - GO',
  55643247 => 'Itumbiara - GO',
  55643248 => 'Itumbiara - GO',
  55643249 => 'Itumbiara - GO',
  55643250 => 'Itumbiara - GO',
  55643251 => 'Itumbiara - GO',
  55643252 => 'Itumbiara - GO',
  55643253 => 'Itumbiara - GO',
  55643254 => 'Itumbiara - GO',
  55643255 => 'Itumbiara - GO',
  55643256 => 'Itumbiara - GO',
  55643257 => 'Itumbiara - GO',
  55643258 => 'Itumbiara - GO',
  55643259 => 'Itumbiara - GO',
  55643261 => 'Itumbiara - GO',
  55643264 => 'Itumbiara - GO',
  55643265 => 'Itumbiara - GO',
  55643269 => 'Itumbiara - GO',
  55643271 => 'Itumbiara - GO',
  55643272 => 'Itumbiara - GO',
  55643273 => 'Itumbiara - GO',
  55643274 => 'Itumbiara - GO',
  55643275 => 'Itumbiara - GO',
  55643278 => 'Itumbiara - GO',
  55643280 => 'Itumbiara - GO',
  55643281 => 'Itumbiara - GO',
  55643282 => 'Itumbiara - GO',
  55643284 => 'Itumbiara - GO',
  55643285 => 'Itumbiara - GO',
  55643286 => 'Itumbiara - GO',
  55643287 => 'Itumbiara - GO',
  55643288 => 'Itumbiara - GO',
  55643289 => 'Itumbiara - GO',
  55643290 => 'Itumbiara - GO',
  55643291 => 'Itumbiara - GO',
  55643292 => 'Itumbiara - GO',
  55643293 => 'Itumbiara - GO',
  55643294 => 'Itumbiara - GO',
  55643295 => 'Itumbiara - GO',
  55643296 => 'Itumbiara - GO',
  55643297 => 'Itumbiara - GO',
  55643298 => 'Itumbiara - GO',
  55643299 => 'Itumbiara - GO',
  55643377 => 'Mossâmedes - GO',
  55643404 => 'Itumbiara - GO',
  55643405 => 'Piracanjuba - GO',
  55643408 => 'Joviânia - GO',
  55643411 => 'Catalão - GO',
  55643412 => 'Itumbiara - GO',
  55643413 => 'Morrinhos - GO',
  55643416 => 'Morrinhos - GO',
  55643417 => 'Morrinhos - GO',
  55643419 => 'Cromínia - GO',
  55643430 => 'Itumbiara - GO',
  55643431 => 'Itumbiara - GO',
  55643432 => 'Itumbiara - GO',
  55643433 => 'Itumbiara - GO',
  55643434 => 'Cachoeira Dourada - GO',
  55643438 => 'Domiciano Ribeiro - GO',
  55643440 => 'Cumari - GO',
  55643441 => 'Catalão - GO',
  55643442 => 'Catalão - GO',
  55643443 => 'Catalão - GO',
  55643444 => 'Buriti Alegre - GO',
  55643447 => 'Corumbaíba - GO',
  55643450 => 'Marzagão - GO',
  55643452 => 'Rio Quente - GO',
  55643453 => 'Caldas Novas - GO',
  55643454 => 'Caldas Novas - GO',
  55643455 => 'Caldas Novas - GO',
  55643461 => 'Pires do Rio - GO',
  55643462 => 'Goiandira - GO',
  55643465 => 'Urutaí - GO',
  55643469 => 'Anhanguera - GO',
  55643471 => 'Pontalina - GO',
  55643472 => 'Santa Cruz de Goiás - GO',
  55643474 => 'Orizona - GO',
  55643475 => 'Três Ranchos - GO',
  55643478 => 'Ouvidor - GO',
  55643479 => 'Panamá - GO',
  55643480 => 'Edealina - GO',
  55643489 => 'Água Limpa - GO',
  55643491 => 'Ipameri - GO',
  55643492 => 'Edéia - GO',
  55643495 => 'Goiatuba - GO',
  55643496 => 'Aloândia - GO',
  55643497 => 'Santo Antônio do Rio Verde - GO',
  55643498 => 'Professor Jamil - GO',
  55643504 => 'Americano do Brasil - GO',
  55643512 => 'Rio Quente - GO',
  55643513 => 'Caldas Novas - GO',
  55643515 => 'Itumbiara - GO',
  55643517 => 'Itumbiara - GO',
  55643519 => 'Itumbiara - GO',
  55643520 => 'Itumbiara - GO',
  55643521 => 'Itumbiara - GO',
  55643522 => 'Itumbiara - GO',
  55643526 => 'Itumbiara - GO',
  55643531 => 'Itumbiara - GO',
  55643532 => 'Itumbiara - GO',
  55643533 => 'Itumbiara - GO',
  55643534 => 'Itumbiara - GO',
  55643539 => 'Itumbiara - GO',
  55643541 => 'Itumbiara - GO',
  55643542 => 'Itumbiara - GO',
  55643543 => 'Cezarina - GO',
  55643545 => 'Itumbiara - GO',
  55643546 => 'Itumbiara - GO',
  55643547 => 'Indiara - GO',
  55643548 => 'Itumbiara - GO',
  55643555 => 'Avelinópolis - GO',
  55643556 => 'Paraúna - GO',
  55643558 => 'Itumbiara - GO',
  55643559 => 'Itumbiara - GO',
  55643560 => 'São João da Paraúna - GO',
  55643563 => 'Jandaia - GO',
  55643564 => 'Anicuns - GO',
  55643565 => 'Itumbiara - GO',
  55643567 => 'Itumbiara - GO',
  55643570 => 'Claudinápolis - GO',
  55643571 => 'Palmeiras de Goiás - GO',
  55643573 => 'Itumbiara - GO',
  55643575 => 'Itumbiara - GO',
  55643576 => 'Itumbiara - GO',
  55643579 => 'Itumbiara - GO',
  55643581 => 'Itumbiara - GO',
  55643586 => 'Itumbiara - GO',
  55643588 => 'Itumbiara - GO',
  55643591 => 'Itumbiara - GO',
  55643593 => 'Itumbiara - GO',
  55643594 => 'Itumbiara - GO',
  55643595 => 'Itumbiara - GO',
  55643597 => 'Itumbiara - GO',
  55643601 => 'São Luís de Montes Belos - GO',
  55643602 => 'Rio Verde - GO',
  55643603 => 'Iporá - GO',
  55643604 => 'Mairipotaba - GO',
  55643607 => 'Itumbiara - GO',
  55643608 => 'Bom Jesus de Goiás - GO',
  55643609 => 'Itumbiara - GO',
  55643610 => 'Mineiros - GO',
  55643611 => 'Rio Verde - GO',
  55643612 => 'Rio Verde - GO',
  55643613 => 'Rio Verde - GO',
  55643614 => 'Santa Helena de Goiás - GO',
  55643615 => 'Quirinópolis - GO',
  55643620 => 'Rio Verde - GO',
  55643621 => 'Rio Verde - GO',
  55643622 => 'Rio Verde - GO',
  55643623 => 'Rio Verde - GO',
  55643624 => 'Itumbiara - GO',
  55643626 => 'Santo Antônio da Barra - GO',
  55643627 => 'Riverlândia - GO',
  55643628 => 'Ouroana - GO',
  55643629 => 'Montividiu - GO',
  55643631 => 'Jataí - GO',
  55643632 => 'Jataí - GO',
  55643633 => 'Lagoa do Bauzinho - GO',
  55643634 => 'Chapadão do Céu - GO',
  55643635 => 'Santa Rita do Araguaia - GO',
  55643636 => 'Jataí - GO',
  55643637 => 'Aparecida do Rio Doce - GO',
  55643639 => 'Perolândia - GO',
  55643640 => 'Lagoa Santa - GO',
  55643641 => 'Santa Helena de Goiás - GO',
  55643642 => 'Turvelândia - GO',
  55643643 => 'Porteirão - GO',
  55643644 => 'Aporé - GO',
  55643645 => 'Acreúna - GO',
  55643647 => 'Maurilândia - GO',
  55643648 => 'Itajá - GO',
  55643649 => 'Castelândia - GO',
  55643651 => 'Quirinópolis - GO',
  55643652 => 'Cristianópolis - GO',
  55643653 => 'Gouvelândia - GO',
  55643654 => 'Cachoeira Alta - GO',
  55643655 => 'Paranaiguara - GO',
  55643656 => 'Caçu - GO',
  55643657 => 'Bom Jardim de Goiás - GO',
  55643658 => 'São Simão - GO',
  55643659 => 'Itarumã - GO',
  55643661 => 'Mineiros - GO',
  55643662 => 'Palestina de Goiás - GO',
  55643663 => 'Caiapônia - GO',
  55643664 => 'Doverlândia - GO',
  55643665 => 'Piranhas - GO',
  55643666 => 'Portelândia - GO',
  55643667 => 'Arenópolis - GO',
  55643668 => 'Serranópolis - GO',
  55643671 => 'São Luís de Montes Belos - GO',
  55643672 => 'Mineiros - GO',
  55643674 => 'Iporá - GO',
  55643675 => 'Palminópolis - GO',
  55643676 => 'Cachoeira de Goiás - GO',
  55643677 => 'Amorinópolis - GO',
  55643678 => 'Israelândia - GO',
  55643679 => 'Sanclerlândia - GO',
  55643680 => 'Nazário - GO',
  55643681 => 'Firminópolis - GO',
  55643682 => 'Turvânia - GO',
  55643684 => 'Aurilândia - GO',
  55643685 => 'Ivolândia - GO',
  55643686 => 'Moiporá - GO',
  55643687 => 'Córrego do Ouro - GO',
  55643688 => 'Jaupaci - GO',
  55643689 => 'Diorama - GO',
  55643691 => 'Vicentinópolis - GO',
  55643694 => 'Palmelo - GO',
  55643695 => 'Adelândia - GO',
  55643696 => 'Campo Alegre de Goiás - GO',
  55643697 => 'Davinópolis - GO',
  55643698 => 'Nova Aurora - GO',
  55643699 => 'Buriti de Goiás - GO',
  55643901 => 'Itumbiara - GO',
  55643920 => 'Itumbiara - GO',
  55643921 => 'Itumbiara - GO',
  55643922 => 'Itumbiara - GO',
  55643923 => 'Itumbiara - GO',
  55643924 => 'Itumbiara - GO',
  55643926 => 'Itumbiara - GO',
  55643931 => 'Itumbiara - GO',
  55643932 => 'Itumbiara - GO',
  55643933 => 'Itumbiara - GO',
  55643941 => 'Itumbiara - GO',
  55643942 => 'Itumbiara - GO',
  55643945 => 'Itumbiara - GO',
  55643946 => 'Itumbiara - GO',
  55643954 => 'Itumbiara - GO',
  55643956 => 'Itumbiara - GO',
  55643983 => 'Itumbiara - GO',
  55643996 => 'Itumbiara - GO',
  55643997 => 'Itumbiara - GO',
  55643998 => 'Itumbiara - GO',
  55643999 => 'Itumbiara - GO',
  55644001 => 'Itumbiara - GO',
  55644003 => 'Itumbiara - GO',
  55644005 => 'Itumbiara - GO',
  55644006 => 'Itumbiara - GO',
  55644007 => 'Itumbiara - GO',
  55644008 => 'Itumbiara - GO',
  55644009 => 'Itumbiara - GO',
  55644012 => 'Itumbiara - GO',
  55644052 => 'Itumbiara - GO',
  55644141 => 'Itumbiara - GO',
  5565 => 'Mato Grosso',
  55652121 => 'Cuiabá - MT',
  55652122 => 'Cáceres - MT',
  55652123 => 'Cuiabá - MT',
  55652128 => 'Cuiabá - MT',
  55652137 => 'Várzea Grande - MT',
  55653003 => 'Cuiabá - MT',
  55653023 => 'Cuiabá - MT',
  55653025 => 'Cuiabá - MT',
  55653026 => 'Várzea Grande - MT',
  55653027 => 'Cuiabá - MT',
  55653028 => 'Cuiabá - MT',
  55653029 => 'Várzea Grande - MT',
  55653046 => 'Cuiabá - MT',
  55653052 => 'Cuiabá - MT',
  55653053 => 'Várzea Grande - MT',
  55653054 => 'Cuiabá - MT',
  55653055 => 'Cuiabá - MT',
  55653057 => 'Cuiabá - MT',
  55653102 => 'Nobres - MT',
  55653211 => 'Cáceres - MT',
  55653212 => 'Lucas do Rio Verde - MT',
  55653221 => 'Cáceres - MT',
  55653222 => 'Cáceres - MT',
  55653223 => 'Cáceres - MT',
  55653224 => 'Cáceres - MT',
  55653225 => 'Porto Esperidião - MT',
  55653228 => 'Lambari D\'Oeste - MT',
  55653233 => 'Salto do Céu - MT',
  55653235 => 'Figueirópolis D\'Oeste - MT',
  55653241 => 'Mirassol D\'Oeste - MT',
  55653244 => 'Jauru - MT',
  55653247 => 'Reserva do Cabaçal - MT',
  55653251 => 'São José dos Quatro Marcos - MT',
  55653254 => 'Indiavaí - MT',
  55653257 => 'Rio Branco - MT',
  55653259 => 'Vila Bela da Santíssima Trindade - MT',
  55653261 => 'Araputanga - MT',
  55653265 => 'Conquista D\'Oeste - MT',
  55653266 => 'Pontes e Lacerda - MT',
  55653268 => 'Vale de São Domingos - MT',
  55653273 => 'Curvelândia - MT',
  55653275 => 'Glória D\'Oeste - MT',
  55653277 => 'Caramujo - MT',
  55653283 => 'Comodoro - MT',
  55653291 => 'Cáceres - MT',
  55653301 => 'Chapada dos Guimarães - MT',
  55653308 => 'Nova Mutum - MT',
  55653311 => 'Tangará da Serra - MT',
  55653312 => 'Santo Afonso - MT',
  55653313 => 'Cuiabá - MT',
  55653314 => 'Cuiabá - MT',
  55653318 => 'Cuiabá - MT',
  55653319 => 'Cuiabá - MT',
  55653321 => 'Cuiabá - MT',
  55653322 => 'Cuiabá - MT',
  55653325 => 'Tangará da Serra - MT',
  55653326 => 'Tangará da Serra - MT',
  55653327 => 'Progresso - MT',
  55653329 => 'Tangará da Serra - MT',
  55653331 => 'Barão de Melgaço - MT',
  55653332 => 'Nova Olímpia - MT',
  55653334 => 'Mato Grosso',
  55653335 => 'Agrovila das Palmeiras - MT',
  55653336 => 'Diamantino - MT',
  55653337 => 'Diamantino - MT',
  55653338 => 'Gleba Ranchão - MT',
  55653339 => 'Tangará da Serra - MT',
  55653341 => 'Santo Antônio do Leverger - MT',
  55653342 => 'Denise - MT',
  55653343 => 'Arenápolis - MT',
  55653344 => 'Jangada - MT',
  55653345 => 'Poconé - MT',
  55653346 => 'Nortelândia - MT',
  55653347 => 'Assari - MT',
  55653349 => 'Campo Novo do Parecis - MT',
  55653351 => 'Nossa Senhora do Livramento - MT',
  55653352 => 'Nova Marilândia - MT',
  55653353 => 'Acorizal - MT',
  55653354 => 'Mato Grosso',
  55653356 => 'Rosário Oeste - MT',
  55653361 => 'Barra do Bugres - MT',
  55653362 => 'Várzea Grande - MT',
  55653363 => 'Cuiabá - MT',
  55653364 => 'Várzea Grande - MT',
  55653365 => 'Cuiabá - MT',
  55653366 => 'Nova Mutum - MT',
  55653371 => 'Nova Mutum - MT',
  55653374 => 'Cangas - MT',
  55653376 => 'Nobres - MT',
  55653382 => 'Campo Novo do Parecis - MT',
  55653383 => 'Sapezal - MT',
  55653384 => 'Porto Estrela - MT',
  55653386 => 'São José do Rio Claro - MT',
  55653387 => 'Campos de Júlio - MT',
  55653388 => 'Várzea Grande - MT',
  55653391 => 'Cuiabá - MT',
  55653396 => 'Alto Paraguai - MT',
  55653397 => 'Diamantino - MT',
  55653421 => 'Rondonópolis - MT',
  55653491 => 'Itiquira - MT',
  55653492 => 'Ouro Branco (Antiga Raposolândia) - MT',
  55653513 => 'Lucas do Rio Verde - MT',
  55653529 => 'Santa Rita do Trivelato - MT',
  55653531 => 'Sinop - MT',
  55653541 => 'Colíder - MT',
  55653548 => 'Lucas do Rio Verde - MT',
  55653549 => 'Lucas do Rio Verde - MT',
  55653566 => 'Juína - MT',
  55653611 => 'Cuiabá - MT',
  55653613 => 'Cuiabá - MT',
  55653617 => 'Cuiabá - MT',
  55653619 => 'Cuiabá - MT',
  55653624 => 'Cuiabá - MT',
  55653625 => 'Cuiabá - MT',
  55653626 => 'Cuiabá - MT',
  55653631 => 'Cuiabá - MT',
  55653637 => 'Cuiabá - MT',
  55653641 => 'Cuiabá - MT',
  55653642 => 'Cuiabá - MT',
  55653644 => 'Cuiabá - MT',
  55653645 => 'Cuiabá - MT',
  55653646 => 'Cuiabá - MT',
  55653648 => 'Cuiabá - MT',
  55653649 => 'Cuiabá - MT',
  55653661 => 'Cuiabá - MT',
  55653663 => 'Cuiabá - MT',
  55653664 => 'Cuiabá - MT',
  55653665 => 'Cuiabá - MT',
  55653666 => 'Cuiabá - MT',
  55653667 => 'Cuiabá - MT',
  55653668 => 'Cuiabá - MT',
  55653669 => 'Cuiabá - MT',
  55653681 => 'Várzea Grande - MT',
  55653682 => 'Várzea Grande - MT',
  55653684 => 'Várzea Grande - MT',
  55653685 => 'Várzea Grande - MT',
  55653686 => 'Várzea Grande - MT',
  55653688 => 'Várzea Grande - MT',
  55653691 => 'Várzea Grande - MT',
  55653692 => 'Várzea Grande - MT',
  55653694 => 'Várzea Grande - MT',
  55653695 => 'Várzea Grande - MT',
  55653901 => 'Cuiabá - MT',
  55653904 => 'Campo Novo do Parecis - MT',
  55653925 => 'Cuiabá - MT',
  55653927 => 'Várzea Grande - MT',
  55653928 => 'Cuiabá - MT',
  55654001 => 'Cuiabá - MT',
  55654003 => 'Cuiabá - MT',
  55654004 => 'Várzea Grande - MT',
  55654007 => 'Cuiabá - MT',
  55654009 => 'Cuiabá - MT',
  55654052 => 'Cuiabá - MT',
  55654062 => 'Cuiabá - MT',
  55654104 => 'Várzea Grande - MT',
  55654141 => 'Cuiabá - MT',
  5566 => 'Mato Grosso',
  55662101 => 'Rondonópolis - MT',
  55662103 => 'Rondonópolis - MT',
  55663015 => 'Sinop - MT',
  55663016 => 'Primavera do Leste - MT',
  55663022 => 'Rondonópolis - MT',
  55663023 => 'Rondonópolis - MT',
  55663026 => 'Rondonópolis - MT',
  55663027 => 'Rondonópolis - MT',
  55663211 => 'Sinop - MT',
  55663212 => 'Sorriso - MT',
  55663301 => 'Rondonópolis - MT',
  55663302 => 'Rondonópolis - MT',
  55663321 => 'Rondonópolis - MT',
  55663328 => 'Planalto da Serra - MT',
  55663385 => 'Nova Brasilândia - MT',
  55663386 => 'São José do Rio Claro - MT',
  55663399 => 'Campo Verde - MT',
  55663401 => 'Barra do Garças - MT',
  55663402 => 'Barra do Garças - MT',
  55663405 => 'Barra do Garças - MT',
  55663406 => 'Torixoréu - MT',
  55663407 => 'Barra do Garças - MT',
  55663408 => 'São José do Couto - MT',
  55663410 => 'Rondonópolis - MT',
  55663412 => 'Juscimeira - MT',
  55663415 => 'Ribeirãozinho - MT',
  55663416 => 'General Carneiro - MT',
  55663418 => 'São Pedro da Cipa - MT',
  55663419 => 'Campo Verde - MT',
  55663421 => 'Rondonópolis - MT',
  55663423 => 'Rondonópolis - MT',
  55663424 => 'Rondonópolis - MT',
  55663427 => 'Rondonópolis - MT',
  55663431 => 'Guiratinga - MT',
  55663432 => 'Poxoréo - MT',
  55663433 => 'Primavera do Leste - MT',
  55663435 => 'Tesouro - MT',
  55663436 => 'Poxoréo - MT',
  55663437 => 'Campinápolis - MT',
  55663438 => 'Nova Xavantina - MT',
  55663439 => 'Rondonópolis - MT',
  55663442 => 'Vale dos Sonhos - MT',
  55663451 => 'Dom Aquino - MT',
  55663452 => 'Novo São Joaquim - MT',
  55663455 => 'Santa Elvira - MT',
  55663461 => 'Jaciara - MT',
  55663463 => 'Primavera do Leste - MT',
  55663466 => 'Ponte Branca - MT',
  55663467 => 'Nova Nazaré - MT',
  55663468 => 'Água Boa - MT',
  55663471 => 'Alto Garças - MT',
  55663472 => 'Serra Dourada - MT',
  55663476 => 'Araguainha - MT',
  55663478 => 'Canarana - MT',
  55663479 => 'Novo São Joaquim - MT',
  55663481 => 'Alto Araguaia - MT',
  55663486 => 'Pedra Preta - MT',
  55663488 => 'Santo Antonuio do Leste - MT',
  55663489 => 'Ribeirão Cascalheira - MT',
  55663493 => 'Pedra Preta - MT',
  55663494 => 'São José do Povo - MT',
  55663495 => 'Primavera do Leste - MT',
  55663496 => 'Alto Taquari - MT',
  55663497 => 'Primavera do Leste - MT',
  55663498 => 'Primavera do Leste - MT',
  55663499 => 'Araguaiana - MT',
  55663500 => 'Primavera do Leste - MT',
  55663501 => 'Alta Floresta - MT',
  55663503 => 'Brianorte - MT',
  55663504 => 'União do Norte (Antiga Lenislândia) - MT',
  55663506 => 'Analândia do Norte - MT',
  55663507 => 'Simione - MT',
  55663508 => 'Santo Antônio Fontoura - MT',
  55663510 => 'Juara - MT',
  55663511 => 'Sinop - MT',
  55663512 => 'Alta Floresta - MT',
  55663513 => 'Sorriso - MT',
  55663515 => 'Sinop - MT',
  55663517 => 'Sinop - MT',
  55663521 => 'Alta Floresta - MT',
  55663522 => 'São Félix do Araguaia - MT',
  55663523 => 'Nova Santa Helena - MT',
  55663525 => 'Carlinda - MT',
  55663526 => 'Porto dos Gaúchos - MT',
  55663527 => 'Nova União - MT',
  55663528 => 'Luciára - MT',
  55663529 => 'Querência - MT',
  55663531 => 'Sinop - MT',
  55663532 => 'Sinop - MT',
  55663533 => 'Sinop - MT',
  55663534 => 'Terra Nova do Norte - MT',
  55663535 => 'Sinop - MT',
  55663536 => 'Marcelândia - MT',
  55663537 => 'Nova Maringá - MT',
  55663538 => 'Bom Jesus do Araguaia - MT',
  55663539 => 'Novo Mundo - MT',
  55663540 => 'União do Sul - MT',
  55663541 => 'Colíder - MT',
  55663542 => 'Rondolândia - MT',
  55663544 => 'Sorriso - MT',
  55663545 => 'Sorriso - MT',
  55663546 => 'Cláudia - MT',
  55663547 => 'Tapurah - MT',
  55663551 => 'Nova Canaã do Norte - MT',
  55663552 => 'Guarantã do Norte - MT',
  55663553 => 'Juruena - MT',
  55663554 => 'Vila Rica - MT',
  55663555 => 'Cotriguaçu - MT',
  55663556 => 'Juara - MT',
  55663557 => 'Tabaporã - MT',
  55663558 => 'Santa Terezinha - MT',
  55663559 => 'Novo Horizonte do Norte - MT',
  55663560 => 'Mato Grosso',
  55663561 => 'Itaúba - MT',
  55663562 => 'Santa Carmem - MT',
  55663563 => 'Paranaíta - MT',
  55663564 => 'Confresa - MT',
  55663565 => 'Aripuanã - MT',
  55663566 => 'Juína - MT',
  55663568 => 'São José do Xingu - MT',
  55663569 => 'Porto Alegre do Norte - MT',
  55663571 => 'Colniza - MT',
  55663572 => 'Nova Bandeirantes - MT',
  55663573 => 'Paranatinga - MT',
  55663574 => 'Nova Guarita - MT',
  55663575 => 'Peixoto de Azevedo - MT',
  55663577 => 'Canabrava do Norte - MT',
  55663578 => 'Itanhangá - MT',
  55663579 => 'Nova Ubiratã - MT',
  55663581 => 'Castanheira - MT',
  55663582 => 'Gaúcha do Norte - MT',
  55663583 => 'Vera - MT',
  55663584 => 'Sorriso - MT',
  55663585 => 'Feliz Natal - MT',
  55663586 => 'Cocalinho - MT',
  55663588 => 'Ipiranga do Norte - MT',
  55663592 => 'Brasnorte - MT',
  55663593 => 'Apiacás - MT',
  55663594 => 'Santa Cruz do Xingu - MT',
  55663595 => 'Matupá - MT',
  55663596 => 'Paranorte - MT',
  55663597 => 'Nova Monte Verde - MT',
  55663599 => 'Guariba - MT',
  55663601 => 'Nova Fronteira - MT',
  55663603 => 'Aripuanã - MT',
  55663902 => 'Rondonópolis - MT',
  55663903 => 'Alta Floresta - MT',
  55663904 => 'Barra do Garças - MT',
  55664141 => 'Rondonópolis - MT',
  5567 => 'Mato Grosso do Sul',
  55672105 => 'Três Lagoas - MS',
  55672106 => 'Campo Grande - MS',
  55672108 => 'Dourados - MS',
  55673003 => 'Campo Grande - MS',
  55673016 => 'Dourados - MS',
  55673021 => 'Dourados - MS',
  55673022 => 'Campo Grande - MS',
  55673025 => 'Campo Grande - MS',
  55673027 => 'Campo Grande - MS',
  55673028 => 'Campo Grande - MS',
  55673029 => 'Campo Grande - MS',
  55673033 => 'Dourados - MS',
  55673038 => 'Dourados - MS',
  55673041 => 'Campo Grande - MS',
  55673043 => 'Campo Grande - MS',
  55673044 => 'Campo Grande - MS',
  55673047 => 'Campo Grande - MS',
  55673202 => 'Campo Grande - MS',
  55673213 => 'Campo Grande - MS',
  55673216 => 'Sidrolândia - MS',
  55673221 => 'Três Lagoas - MS',
  55673225 => 'Coxim - MS',
  55673226 => 'Ladário - MS',
  55673227 => 'Anhanduí - MS',
  55673230 => 'Pedro Gomes - MS',
  55673231 => 'Corumbá - MS',
  55673232 => 'Corumbá - MS',
  55673234 => 'Corumbá - MS',
  55673236 => 'Nioaque - MS',
  55673238 => 'Ribas do Rio Pardo - MS',
  55673239 => 'Água Clara - MS',
  55673240 => 'Aquidauana - MS',
  55673241 => 'Aquidauana - MS',
  55673242 => 'Miranda - MS',
  55673243 => 'Dois Irmãos do Buriti - MS',
  55673245 => 'Anastácio - MS',
  55673246 => 'Terenos - MS',
  55673247 => 'Costa Rica - MS',
  55673248 => 'Costa Rica - MS',
  55673250 => 'Corguinho - MS',
  55673251 => 'Jardim - MS',
  55673254 => 'Sonora - MS',
  55673255 => 'Bonito - MS',
  55673258 => 'Taunay - MS',
  55673260 => 'Alcinópolis - MS',
  55673261 => 'Bandeirantes - MS',
  55673268 => 'Bodoquena - MS',
  55673269 => 'Guia Lopes da Laguna - MS',
  55673272 => 'Sidrolândia - MS',
  55673274 => 'Figueirão - MS',
  55673278 => 'Rio Negro - MS',
  55673285 => 'Jaraguari - MS',
  55673286 => 'Camapuã - MS',
  55673287 => 'Porto Murtinho - MS',
  55673289 => 'Rochedo - MS',
  55673291 => 'Coxim - MS',
  55673292 => 'Rio Verde de Mato Grosso - MS',
  55673295 => 'São Gabriel do Oeste - MS',
  55673297 => 'Chapadão do Baús - MS',
  55673302 => 'Campo Grande - MS',
  55673303 => 'Campo Grande - MS',
  55673304 => 'Campo Grande - MS',
  55673306 => 'Campo Grande - MS',
  55673311 => 'Campo Grande - MS',
  55673312 => 'Campo Grande - MS',
  55673313 => 'Campo Grande - MS',
  55673314 => 'Campo Grande - MS',
  55673315 => 'Campo Grande - MS',
  55673316 => 'Campo Grande - MS',
  55673317 => 'Campo Grande - MS',
  55673318 => 'Campo Grande - MS',
  55673320 => 'Campo Grande - MS',
  55673321 => 'Campo Grande - MS',
  55673322 => 'Campo Grande - MS',
  55673323 => 'Campo Grande - MS',
  55673324 => 'Campo Grande - MS',
  55673325 => 'Campo Grande - MS',
  55673331 => 'Campo Grande - MS',
  55673342 => 'Campo Grande - MS',
  55673344 => 'Campo Grande - MS',
  55673345 => 'Campo Grande - MS',
  55673346 => 'Campo Grande - MS',
  55673347 => 'Campo Grande - MS',
  55673349 => 'Campo Grande - MS',
  55673351 => 'Campo Grande - MS',
  55673352 => 'Campo Grande - MS',
  55673354 => 'Campo Grande - MS',
  55673356 => 'Campo Grande - MS',
  55673357 => 'Campo Grande - MS',
  55673358 => 'Campo Grande - MS',
  55673361 => 'Campo Grande - MS',
  55673362 => 'Campo Grande - MS',
  55673363 => 'Campo Grande - MS',
  55673364 => 'Campo Grande - MS',
  55673365 => 'Campo Grande - MS',
  55673366 => 'Campo Grande - MS',
  55673368 => 'Campo Grande - MS',
  55673373 => 'Campo Grande - MS',
  55673378 => 'Campo Grande - MS',
  55673380 => 'Campo Grande - MS',
  55673381 => 'Campo Grande - MS',
  55673382 => 'Campo Grande - MS',
  55673383 => 'Campo Grande - MS',
  55673384 => 'Campo Grande - MS',
  55673385 => 'Campo Grande - MS',
  55673386 => 'Campo Grande - MS',
  55673387 => 'Campo Grande - MS',
  55673388 => 'Campo Grande - MS',
  55673391 => 'Campo Grande - MS',
  55673393 => 'Campo Grande - MS',
  55673397 => 'Campo Grande - MS',
  55673398 => 'Campo Grande - MS',
  55673405 => 'Vista Alegre - MS',
  55673409 => 'Naviraí - MS',
  55673410 => 'Dourados - MS',
  55673411 => 'Dourados - MS',
  55673412 => 'Douradina - MS',
  55673413 => 'Panambi - MS',
  55673414 => 'Vila Vargas - MS',
  55673416 => 'Dourados - MS',
  55673418 => 'Itaum - MS',
  55673419 => 'Angélica - MS',
  55673420 => 'Dourados - MS',
  55673421 => 'Dourados - MS',
  55673422 => 'Dourados - MS',
  55673423 => 'Dourados - MS',
  55673424 => 'Dourados - MS',
  55673425 => 'Dourados - MS',
  55673426 => 'Dourados - MS',
  55673427 => 'Dourados - MS',
  55673428 => 'Dourados - MS',
  55673429 => 'Vila Macaúba - MS',
  55673431 => 'Ponta Porã - MS',
  55673432 => 'Ponta Porã - MS',
  55673433 => 'Ponta Porã - MS',
  55673434 => 'Sanga Puitã - MS',
  55673435 => 'Antônio João - MS',
  55673437 => 'Ponta Porã - MS',
  55673438 => 'Laguna Carapã - MS',
  55673439 => 'Bela Vista - MS',
  55673440 => 'Amandina - MS',
  55673441 => 'Nova Andradina - MS',
  55673442 => 'Ivinhema - MS',
  55673443 => 'Batayporã - MS',
  55673444 => 'Taquarussu - MS',
  55673445 => 'Anaurilândia - MS',
  55673446 => 'Angélica - MS',
  55673447 => 'Novo Horizonte do Sul - MS',
  55673448 => 'Deodápolis - MS',
  55673449 => 'Nova Andradina - MS',
  55673451 => 'Itaporã - MS',
  55673452 => 'Rio Brilhante - MS',
  55673453 => 'Caarapó - MS',
  55673454 => 'Maracaju - MS',
  55673455 => 'Rio Brilhante - MS',
  55673456 => 'Nova Alvorada do Sul - MS',
  55673457 => 'Itaporã - MS',
  55673461 => 'Naviraí - MS',
  55673463 => 'Juti - MS',
  55673465 => 'Jateí - MS',
  55673466 => 'Glória de Dourados - MS',
  55673467 => 'Fátima do Sul - MS',
  55673468 => 'Vicentina - MS',
  55673469 => 'Culturama - MS',
  55673471 => 'Iguatemi - MS',
  55673473 => 'Eldorado - MS',
  55673474 => 'Mundo Novo - MS',
  55673475 => 'Japorã - MS',
  55673476 => 'Itaquiraí - MS',
  55673478 => 'Tacuru - MS',
  55673479 => 'Sete Quedas - MS',
  55673480 => 'Paranhos - MS',
  55673481 => 'Amambaí - MS',
  55673483 => 'Coronel Sapucaia - MS',
  55673484 => 'Caarapó - MS',
  55673487 => 'Vila Marques - MS',
  55673488 => 'Aral Moreira - MS',
  55673489 => 'Indápolis - MS',
  55673495 => 'Caracol - MS',
  55673496 => 'Ponta Porã - MS',
  55673498 => 'Caarapó - MS',
  55673499 => 'Vila Nova Casa Verde - MS',
  55673503 => 'Paranaíba - MS',
  55673509 => 'Três Lagoas - MS',
  55673521 => 'Três Lagoas - MS',
  55673522 => 'Três Lagoas - MS',
  55673524 => 'Três Lagoas - MS',
  55673541 => 'Bataguassu - MS',
  55673546 => 'Brasilândia - MS',
  55673547 => 'Debrasa - MS',
  55673557 => 'Três Lagoas - MS',
  55673559 => 'Paranaíba - MS',
  55673562 => 'Chapadão do Sul - MS',
  55673565 => 'Aparecida do Taboado - MS',
  55673574 => 'Inocência - MS',
  55673579 => 'Selvíria - MS',
  55673591 => 'Santa Rita do Pardo - MS',
  55673596 => 'Cassilândia - MS',
  55673665 => 'Água Clara - MS',
  55673666 => 'Chapadão do Sul - MS',
  55673668 => 'Paranaíba - MS',
  55673669 => 'Paranaíba - MS',
  55673671 => 'Dourados - MS',
  55673672 => 'Rio Brilhante - MS',
  55673673 => 'Jateí - MS',
  55673674 => 'Bela Vista - MS',
  55673675 => 'Tacuru - MS',
  55673676 => 'Nova Andradina - MS',
  55673681 => 'Terenos - MS',
  55673682 => 'Camapuã - MS',
  55673683 => 'Rio Verde de Mato Grosso - MS',
  55673686 => 'Bonito - MS',
  55673687 => 'Miranda - MS',
  55673901 => 'Campo Grande - MS',
  55673902 => 'Dourados - MS',
  55673907 => 'Corumbá - MS',
  55673919 => 'Três Lagoas - MS',
  55673926 => 'Ponta Porã - MS',
  55673929 => 'Três Lagoas - MS',
  55674001 => 'Campo Grande - MS',
  55674002 => 'Campo Grande - MS',
  55674003 => 'Campo Grande - MS',
  55674004 => 'Campo Grande - MS',
  55674007 => 'Campo Grande - MS',
  55674062 => 'Campo Grande - MS',
  5568 => 'Acre',
  55682101 => 'Rio Branco - AC',
  55682102 => 'Rio Branco - AC',
  55682106 => 'Rio Branco - AC',
  55683025 => 'Rio Branco - AC',
  55683026 => 'Rio Branco - AC',
  55683028 => 'Rio Branco - AC',
  55683211 => 'Rio Branco - AC',
  55683212 => 'Rio Branco - AC',
  55683213 => 'Rio Branco - AC',
  55683214 => 'Rio Branco - AC',
  55683216 => 'Rio Branco - AC',
  55683221 => 'Rio Branco - AC',
  55683222 => 'Rio Branco - AC',
  55683223 => 'Rio Branco - AC',
  55683224 => 'Rio Branco - AC',
  55683225 => 'Rio Branco - AC',
  55683226 => 'Rio Branco - AC',
  55683227 => 'Rio Branco - AC',
  55683228 => 'Rio Branco - AC',
  55683229 => 'Rio Branco - AC',
  55683231 => 'Bujari - AC',
  55683232 => 'Senador Guiomard - AC',
  55683233 => 'Porto Acre - AC',
  55683234 => 'Capixaba - AC',
  55683235 => 'Acrelândia - AC',
  55683237 => 'Plácido de Castro - AC',
  55683242 => 'Rio Branco - AC',
  55683244 => 'Rio Branco - AC',
  55683248 => 'Rio Branco - AC',
  55683261 => 'Humaitá (Pad Humaitá) - AC',
  55683262 => 'Vila do V - AC',
  55683267 => 'Vila Campinas (Pad Peixoto) - AC',
  55683301 => 'Rio Branco - AC',
  55683302 => 'Rio Branco - AC',
  55683303 => 'Rio Branco - AC',
  55683311 => 'Cruzeiro do Sul - AC',
  55683322 => 'Cruzeiro do Sul - AC',
  55683325 => 'Marechal Thaumaturgo - AC',
  55683327 => 'Assis Brasil (Vila) - AC',
  55683342 => 'Rodrigues Alves - AC',
  55683343 => 'Mâncio Lima - AC',
  55683462 => 'Tarauacá - AC',
  55683463 => 'Feijó - AC',
  55683464 => 'Jordão - AC',
  55683542 => 'Xapuri - AC',
  55683546 => 'Brasiléia - AC',
  55683548 => 'Assis Brasil - AC',
  55683611 => 'Manoel Urbano - AC',
  55683612 => 'Sena Madureira - AC',
  55683615 => 'Santa Rosa do Purus - AC',
  55683901 => 'Rio Branco - AC',
  55684001 => 'Rio Branco - AC',
  55684003 => 'Rio Branco - AC',
  55684007 => 'Rio Branco - AC',
  55684062 => 'Rio Branco - AC',
  5569 => 'Rondônia',
  55692101 => 'Vilhena - RO',
  55692181 => 'Porto Velho - RO',
  55692183 => 'Ji-Paraná - RO',
  55693025 => 'Porto Velho - RO',
  55693026 => 'Porto Velho - RO',
  55693043 => 'Porto Velho - RO',
  55693216 => 'Porto Velho - RO',
  55693218 => 'Porto Velho - RO',
  55693222 => 'Porto Velho - RO',
  55693230 => 'Candeias do Jamari - RO',
  55693231 => 'Itapuã do Oeste - RO',
  55693233 => 'Triunfo - RO',
  55693235 => 'Porto Velho - RO',
  55693236 => 'Porto Velho - RO',
  55693237 => 'Porto Velho - RO',
  55693238 => 'Buritis - RO',
  55693239 => 'Campo Novo de Rondônia - RO',
  55693251 => 'Vista Alegre do Abunã - RO',
  55693252 => 'Vila Extrema - RO',
  55693253 => 'Vila Nova Califórnia - RO',
  55693302 => 'Porto Velho - RO',
  55693311 => 'Cacoal - RO',
  55693316 => 'Vilhena - RO',
  55693321 => 'Vilhena - RO',
  55693322 => 'Vilhena - RO',
  55693341 => 'Colorado do Oeste - RO',
  55693342 => 'Cerejeiras - RO',
  55693343 => 'Corumbiara - RO',
  55693344 => 'Pimenteiras do Oeste - RO',
  55693345 => 'Cabixi - RO',
  55693346 => 'Chupinguaia - RO',
  55693411 => 'Ji-Paraná - RO',
  55693412 => 'Alvorada do Oeste - RO',
  55693413 => 'Urupá - RO',
  55693416 => 'Ji-Paraná - RO',
  55693418 => 'Nova Brasilândia D\'Oeste - RO',
  55693421 => 'Ji-Paraná - RO',
  55693422 => 'Ji-Paraná - RO',
  55693423 => 'Ji-Paraná - RO',
  55693424 => 'Ji-Paraná - RO',
  55693427 => 'Nova Colina - RO',
  55693428 => 'Nova Londrina - RO',
  55693432 => 'Rolim de Moura - RO',
  55693434 => 'Santa Luzia D\'Oeste - RO',
  55693435 => 'Novo Horizonte do Oeste - RO',
  55693441 => 'Cacoal - RO',
  55693442 => 'Rolim de Moura - RO',
  55693443 => 'Cacoal - RO',
  55693445 => 'São Felipe do Oeste - RO',
  55693446 => 'Primavera de Rondônia - RO',
  55693447 => 'Parecis - RO',
  55693448 => 'Ministro Andreazza - RO',
  55693449 => 'Rolim de Moura - RO',
  55693451 => 'Pimenta Bueno - RO',
  55693459 => 'Ji-Paraná - RO',
  55693461 => 'Ouro Preto do Oeste - RO',
  55693463 => 'Mirante da Serra - RO',
  55693464 => 'Vale do Paraíso - RO',
  55693465 => 'Teixeirópolis - RO',
  55693466 => 'Nova União - RO',
  55693467 => 'Rondominas - RO',
  55693471 => 'Presidente Médici - RO',
  55693474 => 'Castanheiras - RO',
  55693481 => 'Espigão do Oeste - RO',
  55693485 => 'Espigão D\'Oeste - RO',
  55693516 => 'Ariquemes - RO',
  55693521 => 'Jaru - RO',
  55693523 => 'Theobroma - RO',
  55693524 => 'Governador Jorge Teixeira - RO',
  55693525 => 'Vale do Anari - RO',
  55693526 => 'Jaru - RO',
  55693530 => 'Monte Negro - RO',
  55693532 => 'Cacaulândia - RO',
  55693533 => 'Porto Velho - RO',
  55693534 => 'Alto Paraíso - RO',
  55693535 => 'Ariquemes - RO',
  55693536 => 'Ariquemes - RO',
  55693539 => 'Rio Crespo - RO',
  55693541 => 'Guajará-Mirim - RO',
  55693544 => 'Nova Mamoré - RO',
  55693546 => 'Nova Dimensão - RO',
  55693581 => 'Machadinho D\'Oeste - RO',
  55693582 => 'Cujubim - RO',
  55693583 => 'Quinto Bec - RO',
  55693621 => 'São Francisco do Guaporé - RO',
  55693623 => 'Seringueiras - RO',
  55693641 => 'Alta Floresta do Oeste - RO',
  55693642 => 'São Miguel do Guaporé - RO',
  55693643 => 'Alto Alegre dos Parecis - RO',
  55693651 => 'Costa Marques - RO',
  55693654 => 'São Domingos - RO',
  55693733 => 'Porto Velho - RO',
  55693912 => 'Espigão do Oeste - RO',
  55693913 => 'Guajará-Mirim - RO',
  55694001 => 'Porto Velho - RO',
  55694003 => 'Porto Velho - RO',
  55694007 => 'Porto Velho - RO',
  55694009 => 'Porto Velho - RO',
  55694062 => 'Porto Velho - RO',
  5571 => 'Bahia',
  55712101 => 'Salvador - BA',
  55712102 => 'Salvador - BA',
  55712104 => 'Salvador - BA',
  55712105 => 'Salvador - BA',
  55712106 => 'Salvador - BA',
  55712108 => 'Salvador - BA',
  55712109 => 'Salvador - BA',
  55712136 => 'Salvador - BA',
  55712203 => 'Salvador - BA',
  55712223 => 'Salvador - BA',
  55713003 => 'Salvador - BA',
  55713011 => 'Salvador - BA',
  55713012 => 'Salvador - BA',
  55713013 => 'Salvador - BA',
  55713014 => 'Salvador - BA',
  55713015 => 'Salvador - BA',
  55713017 => 'Salvador - BA',
  55713025 => 'Salvador - BA',
  55713026 => 'Lauro de Freitas - BA',
  55713029 => 'Lauro de Freitas - BA',
  55713038 => 'Salvador - BA',
  55713040 => 'Camaçari - BA',
  55713041 => 'Camaçari - BA',
  55713043 => 'Salvador - BA',
  55713051 => 'Lauro de Freitas - BA',
  55713052 => 'Salvador - BA',
  55713054 => 'Camaçari - BA',
  55713083 => 'Salvador - BA',
  55713105 => 'Salvador - BA',
  55713113 => 'Salvador - BA',
  55713115 => 'Salvador - BA',
  55713118 => 'Salvador - BA',
  55713121 => 'Camaçari - BA',
  55713125 => 'Camaçari - BA',
  55713164 => 'Salvador - BA',
  55713167 => 'Salvador - BA',
  55713176 => 'Salvador - BA',
  55713177 => 'Salvador - BA',
  55713183 => 'Salvador - BA',
  55713186 => 'Salvador - BA',
  55713198 => 'Salvador - BA',
  55713203 => 'Salvador - BA',
  55713204 => 'Salvador - BA',
  55713205 => 'Salvador - BA',
  55713212 => 'Salvador - BA',
  55713213 => 'Salvador - BA',
  55713214 => 'Salvador - BA',
  55713215 => 'Salvador - BA',
  55713216 => 'Simões Filho - BA',
  55713217 => 'Salvador - BA',
  55713218 => 'Salvador - BA',
  55713219 => 'Salvador - BA',
  55713221 => 'Salvador - BA',
  55713225 => 'Feira de Santana - BA',
  55713230 => 'Salvador - BA',
  55713231 => 'Salvador - BA',
  55713232 => 'Salvador - BA',
  55713236 => 'Salvador - BA',
  55713238 => 'Salvador - BA',
  55713239 => 'Salvador - BA',
  55713240 => 'Salvador - BA',
  55713241 => 'Salvador - BA',
  55713242 => 'Salvador - BA',
  55713243 => 'Salvador - BA',
  55713248 => 'Salvador - BA',
  55713249 => 'Salvador - BA',
  55713251 => 'Salvador - BA',
  55713252 => 'Salvador - BA',
  55713253 => 'Salvador - BA',
  55713254 => 'Salvador - BA',
  55713257 => 'Salvador - BA',
  55713259 => 'Salvador - BA',
  55713261 => 'Salvador - BA',
  55713264 => 'Salvador - BA',
  55713267 => 'Salvador - BA',
  55713270 => 'Salvador - BA',
  55713271 => 'Salvador - BA',
  55713272 => 'Salvador - BA',
  55713273 => 'Salvador - BA',
  55713276 => 'Salvador - BA',
  55713283 => 'Salvador - BA',
  55713285 => 'Salvador - BA',
  55713286 => 'Salvador - BA',
  55713287 => 'Lauro de Freitas - BA',
  55713288 => 'Lauro de Freitas - BA',
  55713289 => 'Lauro de Freitas - BA',
  55713291 => 'Salvador - BA',
  55713296 => 'Simões Filho - BA',
  55713297 => 'Salvador - BA',
  55713298 => 'Simões Filho - BA',
  55713301 => 'Salvador - BA',
  55713302 => 'Salvador - BA',
  55713303 => 'Salvador - BA',
  55713304 => 'Salvador - BA',
  55713305 => 'Salvador - BA',
  55713306 => 'Salvador - BA',
  55713307 => 'Salvador - BA',
  55713308 => 'Salvador - BA',
  55713309 => 'Salvador - BA',
  55713310 => 'Salvador - BA',
  55713311 => 'Salvador - BA',
  55713315 => 'Salvador - BA',
  55713316 => 'Salvador - BA',
  55713319 => 'Salvador - BA',
  55713321 => 'Salvador - BA',
  55713323 => 'Salvador - BA',
  55713325 => 'Salvador - BA',
  55713326 => 'Salvador - BA',
  55713327 => 'Salvador - BA',
  55713328 => 'Salvador - BA',
  55713329 => 'Salvador - BA',
  55713330 => 'Salvador - BA',
  55713334 => 'Salvador - BA',
  55713335 => 'Salvador - BA',
  55713336 => 'Salvador - BA',
  55713337 => 'Salvador - BA',
  55713340 => 'Salvador - BA',
  55713341 => 'Salvador - BA',
  55713342 => 'Salvador - BA',
  55713344 => 'Salvador - BA',
  55713345 => 'Salvador - BA',
  55713346 => 'Salvador - BA',
  55713347 => 'Salvador - BA',
  55713350 => 'Salvador - BA',
  55713351 => 'Salvador - BA',
  55713352 => 'Salvador - BA',
  55713353 => 'Salvador - BA',
  55713354 => 'Salvador - BA',
  55713355 => 'Salvador - BA',
  55713356 => 'Salvador - BA',
  55713357 => 'Salvador - BA',
  55713358 => 'Salvador - BA',
  55713359 => 'Salvador - BA',
  55713362 => 'Salvador - BA',
  55713363 => 'Salvador - BA',
  55713364 => 'Salvador - BA',
  55713365 => 'Salvador - BA',
  55713367 => 'Salvador - BA',
  55713368 => 'Salvador - BA',
  55713369 => 'Lauro de Freitas - BA',
  55713370 => 'Salvador - BA',
  55713371 => 'Salvador - BA',
  55713374 => 'Salvador - BA',
  55713375 => 'Salvador - BA',
  55713376 => 'Salvador - BA',
  55713377 => 'Salvador - BA',
  55713378 => 'Lauro de Freitas - BA',
  55713379 => 'Lauro de Freitas - BA',
  55713380 => 'Salvador - BA',
  55713384 => 'Salvador - BA',
  55713385 => 'Salvador - BA',
  55713386 => 'Salvador - BA',
  55713387 => 'Salvador - BA',
  55713388 => 'Salvador - BA',
  55713390 => 'Salvador - BA',
  55713393 => 'Salvador - BA',
  55713394 => 'Aratu - BA',
  55713395 => 'Salvador - BA',
  55713396 => 'Simões Filho - BA',
  55713397 => 'Salvador - BA',
  55713398 => 'Salvador - BA',
  55713399 => 'Salvador - BA',
  55713401 => 'Salvador - BA',
  55713402 => 'Salvador - BA',
  55713403 => 'Salvador - BA',
  55713404 => 'Salvador - BA',
  55713405 => 'Salvador - BA',
  55713406 => 'Salvador - BA',
  55713407 => 'Salvador - BA',
  55713408 => 'Salvador - BA',
  55713409 => 'Salvador - BA',
  55713412 => 'Salvador - BA',
  55713414 => 'Salvador - BA',
  55713417 => 'Salvador - BA',
  55713431 => 'Salvador - BA',
  55713433 => 'Salvador - BA',
  55713444 => 'Salvador - BA',
  55713450 => 'Salvador - BA',
  55713451 => 'Salvador - BA',
  55713452 => 'Salvador - BA',
  55713453 => 'Salvador - BA',
  55713460 => 'Salvador - BA',
  55713461 => 'Salvador - BA',
  55713462 => 'Salvador - BA',
  55713472 => 'Salvador - BA',
  55713473 => 'Salvador - BA',
  55713480 => 'Salvador - BA',
  55713481 => 'Salvador - BA',
  55713483 => 'Salvador - BA',
  55713484 => 'Salvador - BA',
  55713486 => 'Salvador - BA',
  55713487 => 'Salvador - BA',
  55713489 => 'Salvador - BA',
  55713491 => 'Salvador - BA',
  55713492 => 'Salvador - BA',
  55713493 => 'Camaçari - BA',
  55713495 => 'Salvador - BA',
  55713496 => 'Salvador - BA',
  55713497 => 'Salvador - BA',
  55713499 => 'Salvador - BA',
  55713500 => 'Salvador - BA',
  55713501 => 'Salvador - BA',
  55713504 => 'Lauro de Freitas - BA',
  55713508 => 'Lauro de Freitas - BA',
  55713521 => 'Salvador - BA',
  55713533 => 'Salvador - BA',
  55713555 => 'Salvador - BA',
  55713594 => 'Simões Filho - BA',
  55713601 => 'Candeias - BA',
  55713602 => 'Candeias - BA',
  55713604 => 'Madre de Deus - BA',
  55713605 => 'Candeias - BA',
  55713612 => 'Salvador - BA',
  55713616 => 'Salvador - BA',
  55713621 => 'Camaçari - BA',
  55713622 => 'Camaçari - BA',
  55713624 => 'Camaçari - BA',
  55713625 => 'Dias d\'Ávila - BA',
  55713626 => 'Camaçari - BA',
  55713627 => 'Camaçari - BA',
  55713628 => 'Arembepe - BA',
  55713631 => 'Itaparica - BA',
  55713632 => 'Pólo Petroquímico Camaçari - BA',
  55713633 => 'Vera Cruz - BA',
  55713634 => 'Pólo Petroquímico Camaçari - BA',
  55713635 => 'Mata de São João - BA',
  55713636 => 'Vera Cruz - BA',
  55713637 => 'Vera Cruz - BA',
  55713641 => 'Catu - BA',
  55713642 => 'Pólo Petroquímico Camaçari - BA',
  55713644 => 'Camaçari - BA',
  55713645 => 'Pojuca - BA',
  55713646 => 'Lauro de Freitas - BA',
  55713647 => 'Catu - BA',
  55713648 => 'Dias d\'Ávila - BA',
  55713649 => 'Camaçari - BA',
  55713651 => 'São Francisco do Conde - BA',
  55713652 => 'São Francisco do Conde - BA',
  55713655 => 'São Sebastião do Passé - BA',
  55713656 => 'São Sebastião do Passé - BA',
  55713667 => 'Mata de São João - BA',
  55713669 => 'Palmares - BA',
  55713671 => 'Camaçari - BA',
  55713674 => 'Bahia',
  55713676 => 'Mata de São João - BA',
  55713677 => 'Mata de São João - BA',
  55713681 => 'Vera Cruz - BA',
  55713682 => 'Bom Despacho - BA',
  55713699 => 'Saubara - BA',
  55713717 => 'Camaçari - BA',
  55713797 => 'Salvador - BA',
  55714003 => 'Salvador - BA',
  55714007 => 'Salvador - BA',
  55714009 => 'Salvador - BA',
  55714062 => 'Salvador - BA',
  55714101 => 'Salvador - BA',
  55714102 => 'Salvador - BA',
  55714109 => 'Salvador - BA',
  55714111 => 'Camaçari - BA',
  55714112 => 'Salvador - BA',
  55714113 => 'Lauro de Freitas - BA',
  55714116 => 'Dias d\'Ávila - BA',
  55714117 => 'Simões Filho - BA',
  55714119 => 'Candeias - BA',
  5573 => 'Bahia',
  55732011 => 'Teixeira de Freitas - BA',
  55732101 => 'Ilhéus - BA',
  55732102 => 'Itabuna - BA',
  55732103 => 'Itabuna - BA',
  55732105 => 'Porto Seguro - BA',
  55733011 => 'Teixeira de Freitas - BA',
  55733012 => 'Porto Seguro - BA',
  55733013 => 'Teixeira de Freitas - BA',
  55733017 => 'Ilhéus - BA',
  55733021 => 'Prado - BA',
  55733031 => 'Itamaraju - BA',
  55733041 => 'Itabuna - BA',
  55733043 => 'Itabuna - BA',
  55733046 => 'Jequié - BA',
  55733047 => 'Jequié - BA',
  55733051 => 'Alcobaça - BA',
  55733084 => 'Ilhéus - BA',
  55733086 => 'Ilhéus - BA',
  55733162 => 'Porto Seguro - BA',
  55733166 => 'Eunápolis - BA',
  55733202 => 'Barra do Rocha - BA',
  55733203 => 'Ibicaraí - BA',
  55733204 => 'Itaju do Colônia - BA',
  55733205 => 'Argolo - BA',
  55733206 => 'Mucuri - BA',
  55733207 => 'Nova Canaã - BA',
  55733208 => 'Nova Viçosa - BA',
  55733209 => 'Posto da Mata - BA',
  55733211 => 'Itabuna - BA',
  55733212 => 'Itabuna - BA',
  55733214 => 'Itabuna - BA',
  55733215 => 'Itabuna - BA',
  55733221 => 'Ilhéus - BA',
  55733222 => 'Ilhéus - BA',
  55733225 => 'Igrapiúna - BA',
  55733230 => 'Ubaitaba - BA',
  55733231 => 'Ilhéus - BA',
  55733234 => 'Ilhéus - BA',
  55733236 => 'Una - BA',
  55733237 => 'Buerarema - BA',
  55733238 => 'Itajuípe - BA',
  55733239 => 'Uruçuca - BA',
  55733240 => 'Gongogi - BA',
  55733241 => 'Coaraci - BA',
  55733242 => 'Ibicaraí - BA',
  55733243 => 'Floresta Azul - BA',
  55733244 => 'Itagibá - BA',
  55733245 => 'Ubatã - BA',
  55733246 => 'Itapitanga - BA',
  55733247 => 'Almadina - BA',
  55733248 => 'Itapé - BA',
  55733249 => 'Barro Preto - BA',
  55733251 => 'Itacaré - BA',
  55733254 => 'Gandu - BA',
  55733255 => 'Camamu - BA',
  55733256 => 'Ituberá - BA',
  55733257 => 'Nilo Peçanha - BA',
  55733258 => 'Maraú - BA',
  55733259 => 'Ibirapitanga - BA',
  55733261 => 'Eunápolis - BA',
  55733262 => 'Eunápolis - BA',
  55733263 => 'Teixeira de Freitas - BA',
  55733265 => 'Itororó - BA',
  55733266 => 'Itarantim - BA',
  55733267 => 'Itororó - BA',
  55733268 => 'Porto Seguro - BA',
  55733269 => 'Ilhéus - BA',
  55733270 => 'Itabela - BA',
  55733271 => 'Iguaí - BA',
  55733272 => 'Ibicuí - BA',
  55733273 => 'Pau Brasil - BA',
  55733274 => 'Vera Cruz - BA',
  55733276 => 'Apuarema - BA',
  55733277 => 'Guaratinga - BA',
  55733278 => 'Wenceslau Guimarães - BA',
  55733279 => 'Teolândia - BA',
  55733281 => 'Eunápolis - BA',
  55733282 => 'Santa Cruz Cabrália - BA',
  55733283 => 'Camacã - BA',
  55733284 => 'Canavieiras - BA',
  55733285 => 'Potiraguá - BA',
  55733286 => 'Itapebi - BA',
  55733287 => 'Belmonte - BA',
  55733288 => 'Porto Seguro - BA',
  55733289 => 'Itagimirim - BA',
  55733290 => 'Ibirapuã - BA',
  55733291 => 'Teixeira de Freitas - BA',
  55733292 => 'Teixeira de Freitas - BA',
  55733293 => 'Alcobaça - BA',
  55733294 => 'Itamaraju - BA',
  55733295 => 'Itanhém - BA',
  55733296 => 'Medeiros Neto - BA',
  55733297 => 'Caravelas - BA',
  55733298 => 'Prado - BA',
  55733299 => 'Lajedão - BA',
  55733301 => 'Itabuna - BA',
  55733311 => 'Teixeira de Freitas - BA',
  55733312 => 'Itamaraju - BA',
  55733313 => 'Ipiaú - BA',
  55733421 => 'Porto Seguro - BA',
  55733511 => 'Eunápolis - BA',
  55733512 => 'Eunápolis - BA',
  55733525 => 'Jequié - BA',
  55733526 => 'Jequié - BA',
  55733527 => 'Jequié - BA',
  55733528 => 'Jequié - BA',
  55733530 => 'Entroncamento de Jaguaquara - BA',
  55733531 => 'Ipiaú - BA',
  55733532 => 'Itamari - BA',
  55733533 => 'Maracas - BA',
  55733534 => 'Jaguaquara - BA',
  55733535 => 'Jitaúna - BA',
  55733536 => 'Santa Inês - BA',
  55733537 => 'Ibirataia - BA',
  55733538 => 'Itiruçu - BA',
  55733539 => 'Itagi - BA',
  55733540 => 'Presidente Tancredo Neves - BA',
  55733542 => 'Km Cem - BA',
  55733543 => 'Itaquara - BA',
  55733544 => 'Planaltino - BA',
  55733546 => 'Nova Itarana - BA',
  55733547 => 'Aiquara - BA',
  55733548 => 'Irajuba - BA',
  55733549 => 'Manoel Vitorino - BA',
  55733551 => 'Aurelino Leal - BA',
  55733552 => 'Córrego de Pedras - BA',
  55733554 => 'Aurelino Leal - BA',
  55733556 => 'Lajedo do Tabocal - BA',
  55733561 => 'Ubaitaba - BA',
  55733573 => 'Prado - BA',
  55733575 => 'Arraial d\'Ajuda - BA',
  55733604 => 'Batinga - BA',
  55733605 => 'Itabatan - BA',
  55733612 => 'Itabuna - BA',
  55733613 => 'Itabuna - BA',
  55733616 => 'Itabuna - BA',
  55733617 => 'Itabuna - BA',
  55733621 => 'Dário Meira - BA',
  55733622 => 'Mucuri - BA',
  55733623 => 'Firmino Alves - BA',
  55733624 => 'Jussari - BA',
  55733625 => 'Mascote - BA',
  55733626 => 'Núcleo Colonial de Una - BA',
  55733627 => 'Santa Cruz da Vitória - BA',
  55733628 => 'Santa Luzia - BA',
  55733629 => 'São João do Paraíso - BA',
  55733632 => 'Ilhéus - BA',
  55733633 => 'Ilhéus - BA',
  55733634 => 'Ilhéus - BA',
  55733639 => 'Ilhéus - BA',
  55733645 => 'Medeiros Neto - BA',
  55733656 => 'Ilhéus - BA',
  55733661 => 'Vereda - BA',
  55733662 => 'Jucuruçu - BA',
  55733665 => 'Teixeira de Freitas - BA',
  55733667 => 'Itamaraju - BA',
  55733668 => 'Trancoso - BA',
  55733671 => 'Santa Cruz Cabrália - BA',
  55733672 => 'Santa Cruz Cabrália - BA',
  55733673 => 'Arataca - BA',
  55733674 => 'Barra de Caravelas - BA',
  55733675 => 'Bahia',
  55733676 => 'Barrolândia - BA',
  55733677 => 'Coroa Vermelha - BA',
  55733678 => 'Monte Pascoal - BA',
  55733679 => 'Porto Seguro - BA',
  55733680 => 'Ilhéus - BA',
  55733682 => 'Teixeira de Freitas - BA',
  55733683 => 'Guarani - BA',
  55733684 => 'Itamaraty - BA',
  55733687 => 'Caravelas - BA',
  55733688 => 'Piraí do Norte - BA',
  55733689 => 'Bahia',
  55733692 => 'Camacan - BA',
  55733694 => 'São José da Vitória - BA',
  55733697 => 'Travessão - BA',
  55734102 => 'Ilhéus - BA',
  55734141 => 'Itabuna - BA',
  5574 => 'Bahia',
  55742102 => 'Juazeiro - BA',
  55743061 => 'Juazeiro - BA',
  55743065 => 'Juazeiro - BA',
  55743162 => 'Juazeiro - BA',
  55743221 => 'Senhor do Bonfim - BA',
  55743258 => 'Baixa Grande - BA',
  55743259 => 'Macajuba - BA',
  55743527 => 'Casa Nova - BA',
  55743528 => 'Umburanas - BA',
  55743529 => 'Andorinha - BA',
  55743531 => 'Curaçá - BA',
  55743532 => 'Pilar - BA',
  55743533 => 'Campo Alegre de Lourdes - BA',
  55743534 => 'Pilão Arcado - BA',
  55743535 => 'Remanso - BA',
  55743536 => 'Casa Nova - BA',
  55743537 => 'Sento Sé - BA',
  55743538 => 'Sobradinho - BA',
  55743541 => 'Senhor do Bonfim - BA',
  55743542 => 'Senhor do Bonfim - BA',
  55743546 => 'Itiúba - BA',
  55743547 => 'Antônio Gonçalves - BA',
  55743548 => 'Pindobaçu - BA',
  55743549 => 'Rômulo Campos - BA',
  55743551 => 'Filadélfia - BA',
  55743552 => 'Campo Formoso - BA',
  55743553 => 'Campo Formoso - BA',
  55743559 => 'Pindobaçu - BA',
  55743611 => 'Juazeiro - BA',
  55743612 => 'Juazeiro - BA',
  55743613 => 'Juazeiro - BA',
  55743614 => 'Juazeiro - BA',
  55743617 => 'Juazeiro - BA',
  55743618 => 'Juazeiro - BA',
  55743619 => 'Jaguarari - BA',
  55743620 => 'São Gabriel - BA',
  55743621 => 'Jacobina - BA',
  55743622 => 'Jacobina - BA',
  55743624 => 'Jacobina - BA',
  55743626 => 'Mundo Novo - BA',
  55743627 => 'Miguel Calmon - BA',
  55743628 => 'Piritiba - BA',
  55743629 => 'Barro Alto - BA',
  55743630 => 'Mirangaba - BA',
  55743631 => 'Serrolândia - BA',
  55743632 => 'Mairi - BA',
  55743633 => 'Saúde - BA',
  55743634 => 'Caldeirão Grande - BA',
  55743635 => 'Tapiramutá - BA',
  55743636 => 'Caém - BA',
  55743637 => 'Gentio do Ouro - BA',
  55743639 => 'Várzea do Poço - BA',
  55743640 => 'Presidente Dutra - BA',
  55743641 => 'Irecê - BA',
  55743642 => 'Irecê - BA',
  55743643 => 'Mulungu do Morro - BA',
  55743644 => 'Itaguaçu da Bahia - BA',
  55743645 => 'Campo Formoso - BA',
  55743646 => 'Cafarnaum - BA',
  55743647 => 'Jussara - BA',
  55743648 => 'Ibipeba - BA',
  55743649 => 'Uibaí - BA',
  55743651 => 'Capim Grosso - BA',
  55743652 => 'Ibititá - BA',
  55743653 => 'Morro do Chapéu - BA',
  55743654 => 'Barra do Mendes - BA',
  55743655 => 'Central - BA',
  55743656 => 'Canarana - BA',
  55743657 => 'Lapão - BA',
  55743658 => 'Canarana - BA',
  55743659 => 'Várzea Nova - BA',
  55743661 => 'Xique-Xique - BA',
  55743662 => 'Barra - BA',
  55743664 => 'Xique-xique - BA',
  55743665 => 'Jacobina - BA',
  55743667 => 'Piritiba - BA',
  55743668 => 'João Dourado - BA',
  55743669 => 'Várzea da Roça - BA',
  55743672 => 'Morro do Chapéu - BA',
  55743673 => 'Uauá - BA',
  55743674 => 'Lajes do Batata - BA',
  55743675 => 'São José do Jacuípe - BA',
  55743676 => 'Quixabeira - BA',
  55743677 => 'Ponto Novo - BA',
  55743681 => 'Ourolândia - BA',
  55743684 => 'Canarana - BA',
  55743685 => 'Mundo Novo - BA',
  55743686 => 'Ibipeba - BA',
  55743692 => 'América Dourada - BA',
  55743699 => 'Lapão - BA',
  55744400 => 'Pilão Arcado - BA',
  5575 => 'Bahia',
  55752101 => 'Feira de Santana - BA',
  55752102 => 'Feira de Santana - BA',
  55753011 => 'Feira de Santana - BA',
  55753015 => 'Feira de Santana - BA',
  55753021 => 'Feira de Santana - BA',
  55753022 => 'Feira de Santana - BA',
  55753023 => 'Feira de Santana - BA',
  55753024 => 'Feira de Santana - BA',
  55753025 => 'Feira de Santana - BA',
  55753030 => 'Feira de Santana - BA',
  55753031 => 'Alagoinhas - BA',
  55753062 => 'Feira de Santana - BA',
  55753161 => 'Feira de Santana - BA',
  55753162 => 'Santo Antônio de Jesus - BA',
  55753181 => 'Alagoinhas - BA',
  55753182 => 'Alagoinhas - BA',
  55753201 => 'Acupe - BA',
  55753202 => 'Retirolândia - BA',
  55753203 => 'Jeremoabo - BA',
  55753204 => 'Feira de Santana - BA',
  55753208 => 'Campinhos - BA',
  55753211 => 'Feira de Santana - BA',
  55753213 => 'Banzaê - BA',
  55753217 => 'Santo Amaro - BA',
  55753218 => 'Salgadália - BA',
  55753221 => 'Feira de Santana - BA',
  55753223 => 'Feira de Santana - BA',
  55753224 => 'Feira de Santana - BA',
  55753225 => 'Feira de Santana - BA',
  55753226 => 'Feira de Santana - BA',
  55753229 => 'Feira de Santana - BA',
  55753230 => 'Antônio Cardoso - BA',
  55753234 => 'Nova Fátima - BA',
  55753235 => 'Candeal - BA',
  55753236 => 'Santa Bárbara - BA',
  55753237 => 'Teodoro Sampaio - BA',
  55753238 => 'Terra Nova - BA',
  55753239 => 'Anguera - BA',
  55753241 => 'Santo Amaro - BA',
  55753242 => 'Amélia Rodrigues - BA',
  55753243 => 'Conceição do Jacuípe - BA',
  55753244 => 'Conceição da Feira - BA',
  55753245 => 'Santo Estêvão - BA',
  55753246 => 'São Gonçalo dos Campos - BA',
  55753247 => 'Irará - BA',
  55753248 => 'Coração de Maria - BA',
  55753249 => 'Tanquinho - BA',
  55753251 => 'Itaberaba - BA',
  55753252 => 'Ruy Barbosa - BA',
  55753254 => 'Ipirá - BA',
  55753256 => 'Tucano - BA',
  55753257 => 'Bessa - BA',
  55753258 => 'Araci - BA',
  55753259 => 'Euclides da Cunha - BA',
  55753261 => 'Serrinha - BA',
  55753262 => 'Conceição do Coité - BA',
  55753263 => 'Valente - BA',
  55753264 => 'Riachão do Jacuípe - BA',
  55753265 => 'Santaluz - BA',
  55753266 => 'Araci - BA',
  55753267 => 'Biritinga - BA',
  55753268 => 'Teofilândia - BA',
  55753269 => 'Riachão do Jacuípe - BA',
  55753271 => 'Euclides da Cunha - BA',
  55753272 => 'Tucano - BA',
  55753274 => 'Cansanção - BA',
  55753275 => 'Monte Santo - BA',
  55753276 => 'Ribeira do Pombal - BA',
  55753277 => 'Antas - BA',
  55753278 => 'Cícero Dantas - BA',
  55753279 => 'Paripiranga - BA',
  55753281 => 'Paulo Afonso - BA',
  55753282 => 'Paulo Afonso - BA',
  55753283 => 'Paulo Afonso - BA',
  55753284 => 'Macururé - BA',
  55753285 => 'Rodelas - BA',
  55753286 => 'Coronel João Sá - BA',
  55753287 => 'Abaré - BA',
  55753289 => 'Pedro Alexandre - BA',
  55753294 => 'Água Fria - BA',
  55753296 => 'Sítio do Quinto - BA',
  55753301 => 'Feira de Santana - BA',
  55753311 => 'Santo Antônio de Jesus - BA',
  55753312 => 'Cruz das Almas - BA',
  55753320 => 'Itaeté - BA',
  55753321 => 'Feira de Santana - BA',
  55753322 => 'Feira de Santana - BA',
  55753325 => 'Iaçu - BA',
  55753326 => 'Boa Vista do Tupim - BA',
  55753327 => 'Lajedinho - BA',
  55753328 => 'Ibiquera - BA',
  55753330 => 'Boninal - BA',
  55753331 => 'Seabra - BA',
  55753332 => 'Palmeiras - BA',
  55753334 => 'Lençóis - BA',
  55753335 => 'Andaraí - BA',
  55753336 => 'Wagner - BA',
  55753337 => 'Utinga - BA',
  55753338 => 'Mucugê - BA',
  55753339 => 'Souto Soares - BA',
  55753340 => 'Marcionílio Souza - BA',
  55753341 => 'João Amaro - BA',
  55753343 => 'Bonito - BA',
  55753344 => 'Palmeiras - BA',
  55753345 => 'Nova Redenção - BA',
  55753353 => 'Santo Antônio de Jesus - BA',
  55753358 => 'Coronel Octaviano Alves - BA',
  55753362 => 'Boa Vista Cananéia - BA',
  55753364 => 'Iraquara - BA',
  55753381 => 'Varzedo - BA',
  55753387 => 'Quijingue - BA',
  55753402 => 'Entre Rios - BA',
  55753413 => 'Esplanada - BA',
  55753414 => 'Cachoeira - BA',
  55753418 => 'Alagoinhas - BA',
  55753420 => 'Entre Rios - BA',
  55753421 => 'Alagoinhas - BA',
  55753422 => 'Alagoinhas - BA',
  55753423 => 'Alagoinhas - BA',
  55753424 => 'Muritiba - BA',
  55753425 => 'Cachoeira - BA',
  55753426 => 'Rio Real - BA',
  55753427 => 'Esplanada - BA',
  55753428 => 'Pedrão - BA',
  55753429 => 'Conde - BA',
  55753430 => 'Itapicuru - BA',
  55753431 => 'Inhambupe - BA',
  55753432 => 'Aramari - BA',
  55753433 => 'Subaúma - BA',
  55753434 => 'Acajutiba - BA',
  55753435 => 'Cipó - BA',
  55753436 => 'Olindina - BA',
  55753437 => 'Nova Soure - BA',
  55753438 => 'São Félix - BA',
  55753439 => 'Ribeira do Amparo - BA',
  55753441 => 'Aporá - BA',
  55753443 => 'Crisópolis - BA',
  55753445 => 'Jandaíra - BA',
  55753446 => 'Sátiro Dias - BA',
  55753447 => 'Ouriçangas - BA',
  55753448 => 'Itamira - BA',
  55753449 => 'Conde - BA',
  55753451 => 'Araças - BA',
  55753452 => 'Itatim - BA',
  55753453 => 'Itanagra - BA',
  55753456 => 'Cardeal da Silva - BA',
  55753462 => 'Itapicuru - BA',
  55753469 => 'Cícero Dantas - BA',
  55753471 => 'Feira de Santana - BA',
  55753475 => 'Porto de Sauipe - BA',
  55753477 => 'Chorrochó - BA',
  55753481 => 'Feira de Santana - BA',
  55753483 => 'Feira de Santana - BA',
  55753484 => 'Feira de Santana - BA',
  55753485 => 'Feira de Santana - BA',
  55753486 => 'Feira de Santana - BA',
  55753487 => 'Feira de Santana - BA',
  55753491 => 'Feira de Santana - BA',
  55753494 => 'Canudos - BA',
  55753496 => 'Adustina - BA',
  55753501 => 'Paulo Afonso - BA',
  55753522 => 'Castro Alves - BA',
  55753526 => 'Maragogipe - BA',
  55753544 => 'Ubaíra - BA',
  55753545 => 'Milagres - BA',
  55753593 => 'Heliópolis - BA',
  55753602 => 'Feira de Santana - BA',
  55753603 => 'Feira de Santana - BA',
  55753604 => 'Feira de Santana - BA',
  55753608 => 'Barrocas - BA',
  55753609 => 'Bravo - BA',
  55753612 => 'Feira de Santana - BA',
  55753614 => 'Feira de Santana - BA',
  55753616 => 'Feira de Santana - BA',
  55753621 => 'Cruz das Almas - BA',
  55753622 => 'Feira de Santana - BA',
  55753623 => 'Feira de Santana - BA',
  55753624 => 'Feira de Santana - BA',
  55753626 => 'Feira de Santana - BA',
  55753627 => 'Sapeaçu - BA',
  55753628 => 'São Felipe - BA',
  55753629 => 'Conceição do Almeida - BA',
  55753631 => 'Santo Antônio de Jesus - BA',
  55753632 => 'Santo Antônio de Jesus - BA',
  55753634 => 'Amargosa - BA',
  55753635 => 'Mutuípe - BA',
  55753636 => 'Nazaré - BA',
  55753638 => 'Governador Mangabeira - BA',
  55753639 => 'Santa Teresinha - BA',
  55753641 => 'Valença - BA',
  55753642 => 'Jaguaripe - BA',
  55753643 => 'Valença - BA',
  55753644 => 'Queimadas - BA',
  55753646 => 'Guaibim - BA',
  55753647 => 'Aratuípe - BA',
  55753648 => 'Dom Macedo Costa - BA',
  55753649 => 'Elísio Medrado - BA',
  55753650 => 'Nordestina - BA',
  55753651 => 'Jiquiriçá - BA',
  55753652 => 'Cairu - BA',
  55753653 => 'Cairu - BA',
  55753654 => 'Brejões - BA',
  55753656 => 'Glória - BA',
  55753658 => 'Fátima - BA',
  55753659 => 'Salinas da Margarida - BA',
  55753660 => 'Pé de Serra - BA',
  55753662 => 'Laje - BA',
  55753664 => 'Taperoá - BA',
  55753667 => 'Nossa Senhora da Ajuda - BA',
  55753669 => 'Ponte 2 de Julho - BA',
  55753674 => 'Cruz das Almas - BA',
  55753676 => 'São Miguel das Matas - BA',
  55753677 => 'Valença - BA',
  55753680 => 'Rafael Jambeiro - BA',
  55753681 => 'Cabaceiras do Paraguaçu - BA',
  55753682 => 'Gavião - BA',
  55753683 => 'Humildes - BA',
  55753684 => 'Ichu - BA',
  55753685 => 'Ipecaetá - BA',
  55753688 => 'Lamarão - BA',
  55753690 => 'Capela do Alto Alegre - BA',
  55753692 => 'Paulo Afonso - BA',
  55753693 => 'Pintadas - BA',
  55753694 => 'Santanópolis - BA',
  55753695 => 'São Domingos - BA',
  55753696 => 'Saubara - BA',
  55753697 => 'Serra Preta - BA',
  55753698 => 'Santa Brígida - BA',
  55753699 => 'Saubara - BA',
  55754009 => 'Feira de Santana - BA',
  55754101 => 'Feira de Santana - BA',
  55754141 => 'Feira de Santana - BA',
  5577 => 'Bahia',
  55772101 => 'Vitória da Conquista - BA',
  55772102 => 'Vitória da Conquista - BA',
  55773021 => 'Barreiras - BA',
  55773081 => 'Vitória da Conquista - BA',
  55773082 => 'Vitória da Conquista - BA',
  55773083 => 'Vitória da Conquista - BA',
  55773084 => 'Vitória da Conquista - BA',
  55773086 => 'Vitória da Conquista - BA',
  55773087 => 'Vitória da Conquista - BA',
  55773088 => 'Vitória da Conquista - BA',
  55773201 => 'Vitória da Conquista - BA',
  55773202 => 'Vitória da Conquista - BA',
  55773221 => 'Itapetinga - BA',
  55773229 => 'Brumado - BA',
  55773261 => 'Itapetinga - BA',
  55773262 => 'Itapetinga - BA',
  55773274 => 'Macarani - BA',
  55773275 => 'Maiquinique - BA',
  55773311 => 'Brumado - BA',
  55773401 => 'Vitória da Conquista - BA',
  55773402 => 'Vitória da Conquista - BA',
  55773409 => 'Iguatemi - BA',
  55773411 => 'Ibitira - BA',
  55773412 => 'Iramaia - BA',
  55773413 => 'Cascavel - BA',
  55773414 => 'Jussiape - BA',
  55773415 => 'Ituaçu - BA',
  55773416 => 'Contendas do Sincorá - BA',
  55773417 => 'Guajeru - BA',
  55773420 => 'Vitória da Conquista - BA',
  55773421 => 'Vitória da Conquista - BA',
  55773422 => 'Vitória da Conquista - BA',
  55773423 => 'Vitória da Conquista - BA',
  55773424 => 'Vitória da Conquista - BA',
  55773425 => 'Vitória da Conquista - BA',
  55773426 => 'Vitória da Conquista - BA',
  55773427 => 'Vitória da Conquista - BA',
  55773429 => 'Vitória da Conquista - BA',
  55773430 => 'Caatiba - BA',
  55773431 => 'Poções - BA',
  55773432 => 'Itambé - BA',
  55773433 => 'Boa Nova - BA',
  55773434 => 'Planalto - BA',
  55773435 => 'Anagé - BA',
  55773436 => 'Barra do Choça - BA',
  55773437 => 'Belo Campo - BA',
  55773438 => 'Cândido Sales - BA',
  55773439 => 'Encruzilhada - BA',
  55773440 => 'Piripá - BA',
  55773441 => 'Brumado - BA',
  55773442 => 'Buritirama - BA',
  55773443 => 'Caraíbas - BA',
  55773444 => 'Livramento de Nossa Senhora - BA',
  55773445 => 'Condeúba - BA',
  55773446 => 'Aracatu - BA',
  55773447 => 'Cordeiros - BA',
  55773448 => 'Dom Basílio - BA',
  55773449 => 'Malhada de Pedras - BA',
  55773450 => 'Barra da Estiva - BA',
  55773451 => 'Guanambi - BA',
  55773452 => 'Guanambi - BA',
  55773453 => 'Brumado - BA',
  55773454 => 'Caetité - BA',
  55773455 => 'Caculé - BA',
  55773456 => 'Urandi - BA',
  55773457 => 'Riacho de Santana - BA',
  55773458 => 'Brumado - BA',
  55773459 => 'Tanhaçu - BA',
  55773460 => 'Igaporã - BA',
  55773461 => 'Bom Jesus da Serra - BA',
  55773462 => 'Caetanos - BA',
  55773463 => 'Licínio de Almeida - BA',
  55773464 => 'Mortugaba - BA',
  55773465 => 'Ibiassucê - BA',
  55773466 => 'Jacaraci - BA',
  55773467 => 'Jacaraci - BA',
  55773468 => 'Mirante - BA',
  55773470 => 'Rio do Antônio - BA',
  55773471 => 'Paramirim - BA',
  55773472 => 'Maetinga - BA',
  55773473 => 'Macaúbas - BA',
  55773474 => 'Feira da Mata - BA',
  55773475 => 'Rio de Contas - BA',
  55773476 => 'Abaíra - BA',
  55773477 => 'Lagoa Real - BA',
  55773478 => 'Ribeirão do Largo - BA',
  55773479 => 'Piatã - BA',
  55773480 => 'Coribe - BA',
  55773481 => 'Bom Jesus da Lapa - BA',
  55773483 => 'Santa Maria da Vitória - BA',
  55773484 => 'Santana - BA',
  55773485 => 'Carinhanha - BA',
  55773488 => 'Correntina - BA',
  55773489 => 'Cocos - BA',
  55773491 => 'São Félix do Coribe - BA',
  55773492 => 'Presidente Jânio Quadros - BA',
  55773493 => 'Guanambi - BA',
  55773494 => 'Tremedal - BA',
  55773495 => 'Caetité - BA',
  55773496 => 'Santa Maria da Vitória - BA',
  55773498 => 'Formoso A - BA',
  55773499 => 'Sussuarana - BA',
  55773611 => 'Barreiras - BA',
  55773612 => 'Barreiras - BA',
  55773613 => 'Barreiras - BA',
  55773614 => 'Barreiras - BA',
  55773616 => 'Formosa do Rio Preto - BA',
  55773617 => 'Baianópolis - BA',
  55773618 => 'Cristópolis - BA',
  55773619 => 'Catolândia - BA',
  55773620 => 'Serra do Ramalho - BA',
  55773621 => 'Cotegipe - BA',
  55773622 => 'Angical - BA',
  55773623 => 'São Desidério - BA',
  55773624 => 'Riachão das Neves - BA',
  55773625 => 'Santa Rita de Cássia - BA',
  55773626 => 'Wanderley - BA',
  55773628 => 'Luis Eduardo Magalhães - BA',
  55773629 => 'Recife - PE',
  55773639 => 'Luis Eduardo Magalhães - BA',
  55773641 => 'Mansidão - BA',
  55773642 => 'Oliveira dos Brejinhos - BA',
  55773643 => 'Matina - BA',
  55773644 => 'Brotas de Macaúbas - BA',
  55773645 => 'Boquira - BA',
  55773646 => 'Ipupiara - BA',
  55773647 => 'Ibitiara - BA',
  55773648 => 'Novo Horizonte - BA',
  55773652 => 'Muquém de São Francisco - BA',
  55773656 => 'Brejolândia - BA',
  55773657 => 'Tabocas do Brejo Velho - BA',
  55773658 => 'Ibitiara - BA',
  55773661 => 'Candiba - BA',
  55773662 => 'Palmas de Monte Alto - BA',
  55773663 => 'Morpará - BA',
  55773664 => 'Paratinga - BA',
  55773667 => 'Pindaí - BA',
  55773668 => 'Sebastião Laranjeiras - BA',
  55773671 => 'Sítio do Mato - BA',
  55773673 => 'Oliveira dos Brejinhos - BA',
  55773674 => 'Ibipitanga - BA',
  55773677 => 'Érico Cardoso - BA',
  55773678 => 'Botuporã - BA',
  55773682 => 'Iuiú - BA',
  55773683 => 'Jaborandi - BA',
  55773684 => 'Roda Velha - BA',
  55773686 => 'Serra Dourada - BA',
  55773687 => 'Canápolis - BA',
  55773688 => 'Novo Paraná - BA',
  55773689 => 'Rosário - BA',
  55773691 => 'Malhada - BA',
  55773693 => 'Rio do Pires - BA',
  55773695 => 'Tanque Novo - BA',
  55773698 => 'Ibotirama - BA',
  55774009 => 'Vitória da Conquista - BA',
  55774141 => 'Vitória da Conquista - BA',
  5579 => 'Sergipe',
  55793014 => 'Nossa Senhora do Socorro - SE',
  55793022 => 'Aracaju - SE',
  55793045 => 'Aracaju - SE',
  55793046 => 'Aracaju - SE',
  55793113 => 'Nossa Senhora do Socorro - SE',
  55793114 => 'Nossa Senhora do Socorro - SE',
  55793194 => 'Aracaju - SE',
  55793198 => 'Aracaju - SE',
  55793205 => 'Aracaju - SE',
  55793211 => 'Aracaju - SE',
  55793213 => 'Aracaju - SE',
  55793214 => 'Aracaju - SE',
  55793215 => 'Aracaju - SE',
  55793217 => 'Aracaju - SE',
  55793221 => 'Aracaju - SE',
  55793222 => 'Aracaju - SE',
  55793223 => 'Aracaju - SE',
  55793224 => 'Aracaju - SE',
  55793227 => 'Aracaju - SE',
  55793236 => 'Aracaju - SE',
  55793241 => 'Aracaju - SE',
  55793243 => 'Aracaju - SE',
  55793245 => 'Aracaju - SE',
  55793246 => 'Aracaju - SE',
  55793248 => 'Aracaju - SE',
  55793249 => 'Aracaju - SE',
  55793251 => 'Aracaju - SE',
  55793252 => 'Aracaju - SE',
  55793254 => 'Nossa Senhora do Socorro - SE',
  55793255 => 'Aracaju - SE',
  55793256 => 'Nossa Senhora do Socorro - SE',
  55793257 => 'São Cristóvão - SE',
  55793259 => 'Aracaju - SE',
  55793260 => 'Barra dos Coqueiros - SE',
  55793261 => 'São Cristóvão - SE',
  55793262 => 'Barra dos Coqueiros - SE',
  55793263 => 'Capela - SE',
  55793264 => 'Itaporanga d\'Ajuda - SE',
  55793265 => 'Nossa Senhora das Dores - SE',
  55793266 => 'Santo Amaro das Brotas - SE',
  55793268 => 'General Maynard - SE',
  55793269 => 'Riachuelo - SE',
  55793271 => 'Divina Pastora - SE',
  55793272 => 'Japaratuba - SE',
  55793274 => 'Rosário do Catete - SE',
  55793275 => 'Maruim - SE',
  55793276 => 'Pirambu - SE',
  55793277 => 'Carmópolis - SE',
  55793279 => 'Nossa Senhora do Socorro - SE',
  55793281 => 'Laranjeiras - SE',
  55793288 => 'Areia Branca - SE',
  55793297 => 'Siriri - SE',
  55793302 => 'Aracaju - SE',
  55793304 => 'Aracaju - SE',
  55793313 => 'Feira Nova - SE',
  55793314 => 'Itabi - SE',
  55793316 => 'Nossa Senhora de Lourdes - SE',
  55793318 => 'Monte Alegre de Sergipe - SE',
  55793319 => 'Gracho Cardoso - SE',
  55793322 => 'Propriá - SE',
  55793337 => 'Poço Redondo - SE',
  55793339 => 'Santana do São Francisco - SE',
  55793341 => 'Aquidabã - SE',
  55793342 => 'Muribeca - SE',
  55793343 => 'Pacatuba - SE',
  55793344 => 'Neópolis - SE',
  55793346 => 'Canindé de São Francisco - SE',
  55793347 => 'Cedro de São João - SE',
  55793348 => 'Japoatã - SE',
  55793349 => 'Porto da Folha - SE',
  55793351 => 'Porto da Folha - SE',
  55793352 => 'Neópolis - SE',
  55793354 => 'Gararu - SE',
  55793361 => 'Amparo de São Francisco - SE',
  55793362 => 'Cumbe - SE',
  55793363 => 'Canhoba - SE',
  55793364 => 'Telha - SE',
  55793365 => 'Malhada dos Bois - SE',
  55793366 => 'Brejo Grande - SE',
  55793377 => 'Ilha das Flores - SE',
  55793411 => 'Nossa Senhora da Glória - SE',
  55793431 => 'Itabaiana - SE',
  55793432 => 'Itabaiana - SE',
  55793436 => 'Itabaiana - SE',
  55793442 => 'Malhador - SE',
  55793443 => 'Campo do Brito - SE',
  55793445 => 'Carira - SE',
  55793447 => 'Frei Paulo - SE',
  55793449 => 'Ribeirópolis - SE',
  55793453 => 'Moita Bonita - SE',
  55793455 => 'São Domingos - SE',
  55793457 => 'Macambira - SE',
  55793459 => 'Pedra Mole - SE',
  55793461 => 'Pinhão - SE',
  55793465 => 'São Miguel do Aleixo - SE',
  55793483 => 'Nossa Senhora Aparecida - SE',
  55793522 => 'Estância - SE',
  55793526 => 'Estância - SE',
  55793530 => 'Estância - SE',
  55793541 => 'Tobias Barreto - SE',
  55793542 => 'Cristinápolis - SE',
  55793543 => 'Indiaroba - SE',
  55793544 => 'Itabaianinha - SE',
  55793545 => 'Tomar do Geru - SE',
  55793546 => 'Umbaúba - SE',
  55793547 => 'Arauá - SE',
  55793548 => 'Santa Luzia do Itanhy - SE',
  55793549 => 'Poço Verde - SE',
  55793611 => 'Simão Dias - SE',
  55793615 => 'Simão Dias - SE',
  55793631 => 'Lagarto - SE',
  55793635 => 'Lagarto - SE',
  55793641 => 'Riachão do Dantas - SE',
  55793642 => 'Colônia Treze - SE',
  55793643 => 'Riachão do Dantas - SE',
  55793644 => 'Lagarto - SE',
  55793645 => 'Boquim - SE',
  55793648 => 'Pedrinhas - SE',
  55793651 => 'Salgado - SE',
  55793711 => 'Aracaju - SE',
  55793712 => 'Aracaju - SE',
  55794002 => 'Aracaju - SE',
  55794003 => 'Aracaju - SE',
  5581 => 'Pernambuco',
  55812101 => 'Recife - PE',
  55812102 => 'Recife - PE',
  55812119 => 'Recife - PE',
  55812126 => 'Recife - PE',
  55812129 => 'Recife - PE',
  55812138 => 'Recife - PE',
  55812626 => 'Recife - PE',
  55813000 => 'Recife - PE',
  55813010 => 'Paulista - PE',
  55813015 => 'Recife - PE',
  55813021 => 'Paulista - PE',
  55813030 => 'Paulista - PE',
  55813031 => 'Recife - PE',
  55813032 => 'Recife - PE',
  55813033 => 'Recife - PE',
  55813046 => 'Caruaru - PE',
  55813078 => 'Recife - PE',
  55813080 => 'Jaboatão dos Guararapes - PE',
  55813086 => 'Recife - PE',
  55813093 => 'Jaboatão dos Guararapes - PE',
  55813094 => 'Jaboatão dos Guararapes - PE',
  55813095 => 'Caruaru - PE',
  55813099 => 'Olinda - PE',
  55813101 => 'Caruaru - PE',
  55813106 => 'Jaboatão dos Guararapes - PE',
  55813114 => 'Vitória de Santo Antão - PE',
  55813127 => 'Recife - PE',
  55813131 => 'Recife - PE',
  55813136 => 'Caruaru - PE',
  55813138 => 'Caruaru - PE',
  55813145 => 'Vitória de Santo Antão - PE',
  55813155 => 'Gravatá - PE',
  55813194 => 'Recife - PE',
  55813198 => 'Recife - PE',
  55813201 => 'Recife - PE',
  55813204 => 'Recife - PE',
  55813212 => 'Recife - PE',
  55813217 => 'Recife - PE',
  55813221 => 'Recife - PE',
  55813222 => 'Recife - PE',
  55813223 => 'Recife - PE',
  55813224 => 'Recife - PE',
  55813225 => 'Recife - PE',
  55813226 => 'Recife - PE',
  55813227 => 'Recife - PE',
  55813228 => 'Recife - PE',
  55813229 => 'Recife - PE',
  55813231 => 'Recife - PE',
  55813236 => 'Recife - PE',
  55813252 => 'Recife - PE',
  55813253 => 'Recife - PE',
  55813255 => 'Jaboatão dos Guararapes - PE',
  55813256 => 'Jaboatão dos Guararapes - PE',
  55813265 => 'Recife - PE',
  55813269 => 'Recife - PE',
  55813271 => 'Recife - PE',
  55813272 => 'Recife - PE',
  55813273 => 'Recife - PE',
  55813274 => 'Recife - PE',
  55813281 => 'Recife - PE',
  55813311 => 'Ipojuca - PE',
  55813314 => 'Recife - PE',
  55813316 => 'Recife - PE',
  55813319 => 'São Lourenço da Mata - PE',
  55813320 => 'Recife - PE',
  55813321 => 'Recife - PE',
  55813323 => 'Recife - PE',
  55813325 => 'Recife - PE',
  55813326 => 'Recife - PE',
  55813327 => 'Recife - PE',
  55813328 => 'Recife - PE',
  55813338 => 'Recife - PE',
  55813339 => 'Recife - PE',
  55813341 => 'Recife - PE',
  55813342 => 'Recife - PE',
  55813343 => 'Recife - PE',
  55813344 => 'Recife - PE',
  55813351 => 'Recife - PE',
  55813361 => 'Jaboatão dos Guararapes - PE',
  55813362 => 'Jaboatão dos Guararapes - PE',
  55813363 => 'Jaboatão dos Guararapes - PE',
  55813372 => 'Paulista - PE',
  55813376 => 'Jaboatão dos Guararapes - PE',
  55813377 => 'Jaboatão dos Guararapes - PE',
  55813378 => 'Jaboatão dos Guararapes - PE',
  55813379 => 'Jaboatão dos Guararapes - PE',
  55813411 => 'Belo Jardim - PE',
  55813412 => 'Recife - PE',
  55813413 => 'Recife - PE',
  55813419 => 'Recife - PE',
  55813421 => 'Recife - PE',
  55813422 => 'Recife - PE',
  55813423 => 'Recife - PE',
  55813424 => 'Recife - PE',
  55813425 => 'Recife - PE',
  55813428 => 'Recife - PE',
  55813431 => 'Olinda - PE',
  55813433 => 'Paulista - PE',
  55813434 => 'Paulista - PE',
  55813435 => 'Paulista - PE',
  55813436 => 'Paulista - PE',
  55813445 => 'Recife - PE',
  55813446 => 'Recife - PE',
  55813447 => 'Recife - PE',
  55813448 => 'Recife - PE',
  55813452 => 'Jaboatão dos Guararapes - PE',
  55813453 => 'Recife - PE',
  55813454 => 'Recife - PE',
  55813457 => 'Paulista - PE',
  55813459 => 'Aldeia - PE',
  55813461 => 'Recife - PE',
  55813462 => 'Recife - PE',
  55813463 => 'Recife - PE',
  55813464 => 'Recife - PE',
  55813465 => 'Recife - PE',
  55813466 => 'Recife - PE',
  55813467 => 'Recife - PE',
  55813468 => 'Jaboatão dos Guararapes - PE',
  55813469 => 'Jaboatão dos Guararapes - PE',
  55813471 => 'Recife - PE',
  55813472 => 'Recife - PE',
  55813473 => 'Jaboatão dos Guararapes - PE',
  55813474 => 'Jaboatão dos Guararapes - PE',
  55813475 => 'Recife - PE',
  55813476 => 'Jaboatão dos Guararapes - PE',
  55813477 => 'Recife - PE',
  55813478 => 'Jaboatão dos Guararapes - PE',
  55813481 => 'Jaboatão dos Guararapes - PE',
  55813482 => 'Jaboatão dos Guararapes - PE',
  55813483 => 'Olinda - PE',
  55813486 => 'Recife - PE',
  55813491 => 'Olinda - PE',
  55813492 => 'Olinda - PE',
  55813495 => 'Olinda - PE',
  55813497 => 'Recife - PE',
  55813501 => 'Ipojuca - PE',
  55813510 => 'Cabo de Santo Agostinho - PE',
  55813512 => 'Cabo de Santo Agostinho - PE',
  55813517 => 'São Lourenço da Mata - PE',
  55813518 => 'Cabo de Santo Agostinho - PE',
  55813519 => 'São Lourenço da Mata - PE',
  55813521 => 'Cabo de Santo Agostinho - PE',
  55813522 => 'Cabo de Santo Agostinho - PE',
  55813523 => 'Vitória de Santo Antão - PE',
  55813524 => 'Cabo de Santo Agostinho - PE',
  55813525 => 'São Lourenço da Mata - PE',
  55813526 => 'Vitória de Santo Antão - PE',
  55813527 => 'Pernambuco',
  55813533 => 'Gravatá - PE',
  55813534 => 'Escada - PE',
  55813535 => 'Moreno - PE',
  55813536 => 'Pombos - PE',
  55813537 => 'Chã Grande - PE',
  55813541 => 'Abreu e Lima - PE',
  55813542 => 'Abreu e Lima - PE',
  55813543 => 'Igarassu - PE',
  55813544 => 'Ilha de Itamaracá - PE',
  55813545 => 'Igarassu - PE',
  55813548 => 'Itapissuma - PE',
  55813551 => 'Ipojuca - PE',
  55813552 => 'Ipojuca - PE',
  55813553 => 'Amaraji - PE',
  55813559 => 'Ipojuca - PE',
  55813561 => 'Ipojuca - PE',
  55813562 => 'Primavera - PE',
  55813563 => 'Gravatá - PE',
  55813576 => 'Sirinhaém - PE',
  55813577 => 'Sirinhaém - PE',
  55813581 => 'Chã de Alegria - PE',
  55813604 => 'Surubim - PE',
  55813607 => 'Orobó - PE',
  55813613 => 'Limoeiro - PE',
  55813616 => 'Goiana - PE',
  55813619 => 'Fernando de Noronha - PE',
  55813621 => 'Carpina - PE',
  55813622 => 'Carpina - PE',
  55813624 => 'Surubim - PE',
  55813625 => 'Goiana - PE',
  55813626 => 'Goiana - PE',
  55813628 => 'Limoeiro - PE',
  55813631 => 'Timbaúba - PE',
  55813633 => 'Nazaré da Mata - PE',
  55813634 => 'Surubim - PE',
  55813635 => 'Itambé - PE',
  55813636 => 'Paudalho - PE',
  55813637 => 'Aliança - PE',
  55813638 => 'Bom Jardim - PE',
  55813639 => 'Macaparana - PE',
  55813641 => 'Vicência - PE',
  55813642 => 'Condado - PE',
  55813643 => 'Itaquitinga - PE',
  55813644 => 'Cumaru - PE',
  55813645 => 'Feira Nova - PE',
  55813646 => 'Tracunhaém - PE',
  55813647 => 'Buenos Aires - PE',
  55813648 => 'João Alfredo - PE',
  55813649 => 'Machados - PE',
  55813651 => 'Passira - PE',
  55813652 => 'Camutanga - PE',
  55813653 => 'Lagoa do Itaenga - PE',
  55813654 => 'Salgadinho - PE',
  55813655 => 'São Vicente Ferrer - PE',
  55813656 => 'Orobó - PE',
  55813657 => 'Ferreiros - PE',
  55813658 => 'Glória do Goitá - PE',
  55813661 => 'Palmares - PE',
  55813662 => 'Palmares - PE',
  55813671 => 'Ribeirão - PE',
  55813673 => 'Catende - PE',
  55813675 => 'Barreiros - PE',
  55813676 => 'Tamandaré - PE',
  55813678 => 'Rio Formoso - PE',
  55813679 => 'Gameleira - PE',
  55813681 => 'Xexéu - PE',
  55813682 => 'Joaquim Nabuco - PE',
  55813683 => 'Maraial - PE',
  55813684 => 'São Benedito do Sul - PE',
  55813685 => 'Quipapá - PE',
  55813686 => 'Belém de Maria - PE',
  55813687 => 'Bonito - PE',
  55813688 => 'São José da Coroa Grande - PE',
  55813689 => 'Jaqueira - PE',
  55813691 => 'Panelas - PE',
  55813692 => 'Lagoa dos Gatos - PE',
  55813693 => 'Xexéu - PE',
  55813699 => 'Panelas - PE',
  55813700 => 'Caruaru - PE',
  55813705 => 'Santa Cruz do Capibaribe - PE',
  55813707 => 'Frei Miguelinho - PE',
  55813708 => 'Bezerros - PE',
  55813709 => 'Belo Jardim - PE',
  55813711 => 'Caruaru - PE',
  55813712 => 'Caruaru - PE',
  55813719 => 'Caruaru - PE',
  55813721 => 'Caruaru - PE',
  55813723 => 'Caruaru - PE',
  55813726 => 'Belo Jardim - PE',
  55813728 => 'Bezerros - PE',
  55813731 => 'Santa Cruz do Capibaribe - PE',
  55813732 => 'Fazenda Nova - PE',
  55813733 => 'Taquaritinga do Norte - PE',
  55813734 => 'Vertentes - PE',
  55813735 => 'São Bento do Una - PE',
  55813736 => 'São Caetano - PE',
  55813737 => 'Bonito - PE',
  55813738 => 'Cupira - PE',
  55813739 => 'Altinho - PE',
  55813741 => 'Toritama - PE',
  55813742 => 'Cachoeirinha - PE',
  55813743 => 'Camocim de São Félix - PE',
  55813744 => 'Agrestina - PE',
  55813745 => 'Riacho das Almas - PE',
  55813746 => 'Jataúba - PE',
  55813747 => 'Brejo da Madre de Deus - PE',
  55813748 => 'Sairé - PE',
  55813751 => 'Frei Miguelinho - PE',
  55813753 => 'São Joaquim do Monte - PE',
  55813755 => 'Tacaimbó - PE',
  55813757 => 'Santa Maria do Cambucá - PE',
  55813758 => 'Barra de Guabiraba - PE',
  55813759 => 'Santa Cruz do Capibaribe - PE',
  55813761 => 'Garanhuns - PE',
  55813762 => 'Garanhuns - PE',
  55813771 => 'Bom Conselho - PE',
  55813821 => 'Recife - PE',
  55813831 => 'Serra Talhada - PE',
  55813846 => 'Santa Cruz da Baixa Verde - PE',
  55813861 => 'Petrolina - PE',
  55813863 => 'Petrolina - PE',
  55813869 => 'Santa Maria da Boa Vista - PE',
  55813873 => 'Araripina - PE',
  55813877 => 'Carnaubeira da Penha - PE',
  55813879 => 'Jaboatão dos Guararapes - PE',
  55813887 => 'Orocó - PE',
  55813915 => 'Aripibu - PE',
  55813972 => 'Recife - PE',
  55813974 => 'Recife - PE',
  55814003 => 'Recife - PE',
  55814007 => 'Recife - PE',
  55814009 => 'Recife - PE',
  55814062 => 'Recife - PE',
  55814102 => 'Recife - PE',
  55814107 => 'Jaboatão dos Guararapes - PE',
  55814109 => 'Paulista - PE',
  55814112 => 'São Lourenço da Mata - PE',
  5582 => 'Alagoas',
  55822121 => 'Maceió - AL',
  55822123 => 'Maceió - AL',
  55823003 => 'Maceió - AL',
  55823013 => 'Maceió - AL',
  55823025 => 'Maceió - AL',
  55823027 => 'Maceió - AL',
  55823028 => 'Maceió - AL',
  55823032 => 'Maceió - AL',
  55823033 => 'Maceió - AL',
  55823035 => 'Maceió - AL',
  55823036 => 'Maceió - AL',
  55823131 => 'Maceió - AL',
  55823177 => 'Maceió - AL',
  55823201 => 'Maceió - AL',
  55823202 => 'Maceió - AL',
  55823203 => 'Ibateguara - AL',
  55823204 => 'Chã Preta - AL',
  55823215 => 'Maceió - AL',
  55823216 => 'Maceió - AL',
  55823218 => 'Maceió - AL',
  55823221 => 'Maceió - AL',
  55823223 => 'Maceió - AL',
  55823231 => 'Maceió - AL',
  55823232 => 'Maceió - AL',
  55823234 => 'Maceió - AL',
  55823235 => 'Maceió - AL',
  55823251 => 'Matriz de Camaragibe - AL',
  55823252 => 'Joaquim Gomes - AL',
  55823253 => 'Novo Lino - AL',
  55823254 => 'São Luís do Quitunde - AL',
  55823255 => 'Colônia Leopoldina - AL',
  55823256 => 'Flexeiras - AL',
  55823257 => 'Campestre - AL',
  55823258 => 'Passo de Camaragibe - AL',
  55823260 => 'Marechal Deodoro - AL',
  55823261 => 'Rio Largo - AL',
  55823262 => 'Messias - AL',
  55823263 => 'Marechal Deodoro - AL',
  55823264 => 'Atalaia - AL',
  55823265 => 'Pilar - AL',
  55823266 => 'Satuba - AL',
  55823267 => 'Coqueiro Seco - AL',
  55823268 => 'Santa Luzia do Norte - AL',
  55823269 => 'Pólo Cloroquímico de Alagoas - AL',
  55823270 => 'Maribondo - AL',
  55823271 => 'São Miguel dos Campos - AL',
  55823272 => 'Barra de São Miguel - AL',
  55823273 => 'Coruripe - AL',
  55823274 => 'Colônia Pindorama - AL',
  55823275 => 'Campo Alegre - AL',
  55823276 => 'Jequiá da Praia - AL',
  55823277 => 'Anadia - AL',
  55823278 => 'Tanque D\'Arca - AL',
  55823279 => 'Boca da Mata - AL',
  55823280 => 'Pindoba - AL',
  55823281 => 'União dos Palmares - AL',
  55823282 => 'Paulo Jacinto - AL',
  55823283 => 'Viçosa - AL',
  55823284 => 'Cajueiro - AL',
  55823285 => 'São José da Laje - AL',
  55823286 => 'Murici - AL',
  55823287 => 'Capela - AL',
  55823288 => 'Quebrangulo - AL',
  55823289 => 'Santana do Mundaú - AL',
  55823291 => 'Barra de Santo Antônio - AL',
  55823292 => 'Porto Calvo - AL',
  55823293 => 'Paripueira - AL',
  55823294 => 'União dos Palmares - AL',
  55823295 => 'São Miguel dos Milagres - AL',
  55823296 => 'Maragogi - AL',
  55823297 => 'Japaratinga - AL',
  55823298 => 'Porto de Pedras - AL',
  55823299 => 'Atalaia - AL',
  55823302 => 'Maceió - AL',
  55823305 => 'Maceió - AL',
  55823311 => 'Maceió - AL',
  55823312 => 'Maceió - AL',
  55823314 => 'Maceió - AL',
  55823320 => 'Maceió - AL',
  55823321 => 'Maceió - AL',
  55823322 => 'Maceió - AL',
  55823323 => 'Maceió - AL',
  55823324 => 'Maceió - AL',
  55823325 => 'Maceió - AL',
  55823326 => 'Maceió - AL',
  55823327 => 'Maceió - AL',
  55823328 => 'Maceió - AL',
  55823332 => 'Maceió - AL',
  55823334 => 'Maceió - AL',
  55823336 => 'Maceió - AL',
  55823337 => 'Maceió - AL',
  55823338 => 'Maceió - AL',
  55823341 => 'Maceió - AL',
  55823342 => 'Maceió - AL',
  55823343 => 'Maceió - AL',
  55823344 => 'Maceió - AL',
  55823346 => 'Maceió - AL',
  55823350 => 'Maceió - AL',
  55823352 => 'Maceió - AL',
  55823353 => 'Maceió - AL',
  55823354 => 'Maceió - AL',
  55823355 => 'Maceió - AL',
  55823356 => 'Maceió - AL',
  55823357 => 'Maceió - AL',
  55823358 => 'Maceió - AL',
  55823359 => 'Maceió - AL',
  55823371 => 'Maceió - AL',
  55823372 => 'Maceió - AL',
  55823373 => 'Maceió - AL',
  55823374 => 'Maceió - AL',
  55823375 => 'Maceió - AL',
  55823376 => 'Maceió - AL',
  55823377 => 'Maceió - AL',
  55823378 => 'Maceió - AL',
  55823420 => 'Palmeira dos Índios - AL',
  55823421 => 'Palmeira dos Índios - AL',
  55823422 => 'Cacimbinhas - AL',
  55823423 => 'Igaci - AL',
  55823424 => 'Major Isidoro - AL',
  55823425 => 'Taquarana - AL',
  55823426 => 'Estrela de Alagoas - AL',
  55823427 => 'Minador do Negrão - AL',
  55823481 => 'Arapiraca - AL',
  55823482 => 'Arapiraca - AL',
  55823520 => 'Girau do Ponciano - AL',
  55823521 => 'Arapiraca - AL',
  55823522 => 'Arapiraca - AL',
  55823523 => 'Limoeiro de Anadia - AL',
  55823524 => 'Feira Grande - AL',
  55823526 => 'Coité do Nóia - AL',
  55823527 => 'Craíbas - AL',
  55823528 => 'Lagoa da Canoa - AL',
  55823529 => 'Arapiraca - AL',
  55823530 => 'Arapiraca - AL',
  55823531 => 'Batalha - AL',
  55823533 => 'Jaramataia - AL',
  55823534 => 'Jacaré dos Homens - AL',
  55823535 => 'Olho d\'Água Grande - AL',
  55823536 => 'Traipu - AL',
  55823537 => 'Campo Grande - AL',
  55823539 => 'Arapiraca - AL',
  55823541 => 'Junqueiro - AL',
  55823542 => 'São Sebastião - AL',
  55823543 => 'Teotônio Vilela - AL',
  55823551 => 'Penedo - AL',
  55823552 => 'Piaçabuçu - AL',
  55823553 => 'Porto Real do Colégio - AL',
  55823554 => 'Igreja Nova - AL',
  55823555 => 'São Brás - AL',
  55823556 => 'Feliz Deserto - AL',
  55823557 => 'Piaçabuçu - AL',
  55823558 => 'Penedo - AL',
  55823597 => 'Maceió - AL',
  55823620 => 'Dois Riachos - AL',
  55823621 => 'Santana do Ipanema - AL',
  55823622 => 'São José da Tapera - AL',
  55823623 => 'Olho d\'Água das Flores - AL',
  55823624 => 'Pão de Açúcar - AL',
  55823625 => 'Maravilha - AL',
  55823626 => 'Poço das Trincheiras - AL',
  55823628 => 'Monteirópolis - AL',
  55823629 => 'Ouro Branco - AL',
  55823632 => 'Olivença - AL',
  55823634 => 'Senador Rui Palmeira - AL',
  55823641 => 'Delmiro Gouveia - AL',
  55823642 => 'Mata Grande - AL',
  55823643 => 'Olho d\'Água do Casado - AL',
  55823644 => 'Água Branca - AL',
  55823645 => 'Inhapi - AL',
  55823646 => 'Canapi - AL',
  55823647 => 'Pariconha - AL',
  55823686 => 'Xingó - AL',
  55823891 => 'Viçosa - MG',
  55824002 => 'Maceió - AL',
  55824004 => 'Maceió - AL',
  55824009 => 'Maceió - AL',
  55824102 => 'Centro - AL',
  5583 => 'Paraiba',
  55832101 => 'Campina Grande - PB',
  55832106 => 'João Pessoa - PB',
  55833015 => 'João Pessoa - PB',
  55833032 => 'Santa Rita - PB',
  55833033 => 'Santa Rita - PB',
  55833035 => 'João Pessoa - PB',
  55833042 => 'João Pessoa - PB',
  55833048 => 'João Pessoa - PB',
  55833051 => 'Campina Grande - PB',
  55833058 => 'Campina Grande - PB',
  55833063 => 'Campina Grande - PB',
  55833065 => 'Campina Grande - PB',
  55833077 => 'Campina Grande - PB',
  55833088 => 'Campina Grande - PB',
  55833099 => 'Campina Grande - PB',
  55833133 => 'João Pessoa - PB',
  55833198 => 'João Pessoa - PB',
  55833201 => 'Campina Grande - PB',
  55833208 => 'João Pessoa - PB',
  55833209 => 'João Pessoa - PB',
  55833213 => 'João Pessoa - PB',
  55833215 => 'João Pessoa - PB',
  55833217 => 'Santa Rita - PB',
  55833220 => 'João Pessoa - PB',
  55833221 => 'João Pessoa - PB',
  55833222 => 'João Pessoa - PB',
  55833223 => 'João Pessoa - PB',
  55833224 => 'João Pessoa - PB',
  55833225 => 'João Pessoa - PB',
  55833226 => 'João Pessoa - PB',
  55833227 => 'João Pessoa - PB',
  55833228 => 'Cabedelo - PB',
  55833229 => 'Santa Rita - PB',
  55833231 => 'João Pessoa - PB',
  55833232 => 'Bayeux - PB',
  55833233 => 'João Pessoa - PB',
  55833235 => 'João Pessoa - PB',
  55833236 => 'João Pessoa - PB',
  55833237 => 'João Pessoa - PB',
  55833238 => 'João Pessoa - PB',
  55833239 => 'João Pessoa - PB',
  55833241 => 'João Pessoa - PB',
  55833242 => 'João Pessoa - PB',
  55833243 => 'João Pessoa - PB',
  55833244 => 'João Pessoa - PB',
  55833245 => 'João Pessoa - PB',
  55833246 => 'João Pessoa - PB',
  55833247 => 'João Pessoa - PB',
  55833248 => 'Cabedelo - PB',
  55833249 => 'João Pessoa - PB',
  55833251 => 'João Pessoa - PB',
  55833252 => 'João Pessoa - PB',
  55833253 => 'Bayeux - PB',
  55833254 => 'Cruz do Espírito Santo - PB',
  55833255 => 'João Pessoa - PB',
  55833256 => 'Alhandra - PB',
  55833257 => 'Mata Redonda - PB',
  55833258 => 'João Pessoa - PB',
  55833259 => 'Pitimbu - PB',
  55833261 => 'Belém - PB',
  55833262 => 'João Pessoa - PB',
  55833263 => 'Lagoa de Dentro - PB',
  55833265 => 'Duas Estradas - PB',
  55833266 => 'Mogeiro - PB',
  55833267 => 'Juarez Távora - PB',
  55833268 => 'João Pessoa - PB',
  55833271 => 'Guarabira - PB',
  55833273 => 'Alagoa Grande - PB',
  55833274 => 'Araçagi - PB',
  55833275 => 'Serraria - PB',
  55833276 => 'Pilões - PB',
  55833277 => 'Pirpirituba - PB',
  55833278 => 'Alagoinha - PB',
  55833279 => 'Cachoeira - PB',
  55833280 => 'Salgado de São Félix - PB',
  55833281 => 'Itabaiana - PB',
  55833282 => 'Pilar - PB',
  55833283 => 'Sapé - PB',
  55833284 => 'Caldas Brandão - PB',
  55833285 => 'Gurinhém - PB',
  55833286 => 'Caaporã - PB',
  55833287 => 'Mari - PB',
  55833288 => 'Mulungu - PB',
  55833289 => 'Juripiranga - PB',
  55833290 => 'Conde - PB',
  55833291 => 'Rio Tinto - PB',
  55833292 => 'Mamanguape - PB',
  55833293 => 'Lucena - PB',
  55833294 => 'Itapororoca - PB',
  55833295 => 'Jacaraú - PB',
  55833296 => 'Baía da Traição - PB',
  55833297 => 'Mataraca - PB',
  55833298 => 'Conde - PB',
  55833299 => 'Pitimbu - PB',
  55833302 => 'Camalaú - PB',
  55833304 => 'São Sebastião do Umbuzeiro - PB',
  55833306 => 'Coxixola - PB',
  55833307 => 'Caraúbas - PB',
  55833308 => 'Santo André - PB',
  55833309 => 'São José dos Cordeiros - PB',
  55833310 => 'Campina Grande - PB',
  55833313 => 'Boa Vista - PB',
  55833314 => 'São José da Mata - PB',
  55833315 => 'Campina Grande - PB',
  55833316 => 'Riachão do Bacamarte - PB',
  55833317 => 'Galante - PB',
  55833321 => 'Campina Grande - PB',
  55833322 => 'Campina Grande - PB',
  55833332 => 'Campina Grande - PB',
  55833333 => 'Campina Grande - PB',
  55833334 => 'Campina Grande - PB',
  55833335 => 'Campina Grande - PB',
  55833336 => 'Campina Grande - PB',
  55833337 => 'Campina Grande - PB',
  55833338 => 'Campina Grande - PB',
  55833339 => 'Campina Grande - PB',
  55833341 => 'Campina Grande - PB',
  55833342 => 'Campina Grande - PB',
  55833343 => 'Campina Grande - PB',
  55833345 => 'Caturité - PB',
  55833346 => 'Barra de Santana - PB',
  55833347 => 'Gado Bravo - PB',
  55833348 => 'Alcantil - PB',
  55833350 => 'Ouro Velho - PB',
  55833351 => 'Monteiro - PB',
  55833352 => 'São João do Tigre - PB',
  55833353 => 'Sumé - PB',
  55833354 => 'Serra Branca - PB',
  55833355 => 'São João do Cariri - PB',
  55833356 => 'Cabaceiras - PB',
  55833357 => 'São Domingos do Cariri - PB',
  55833358 => 'Barra de São Miguel - PB',
  55833359 => 'Congo - PB',
  55833360 => 'Borborema - PB',
  55833361 => 'Esperança - PB',
  55833362 => 'Areia - PB',
  55833363 => 'Solânea - PB',
  55833364 => 'Remígio - PB',
  55833365 => 'Alagoa Nova - PB',
  55833366 => 'Lagoa Seca - PB',
  55833367 => 'Bananeiras - PB',
  55833368 => 'Areial - PB',
  55833369 => 'Arara - PB',
  55833370 => 'Caiçara - PB',
  55833371 => 'Picuí - PB',
  55833372 => 'Cuité - PB',
  55833373 => 'Araruna - PB',
  55833374 => 'Nova Floresta - PB',
  55833375 => 'Pedra Lavrada - PB',
  55833376 => 'Barra de Santa Rosa - PB',
  55833377 => 'Dona Inês - PB',
  55833378 => 'Campo de Santana - PB',
  55833379 => 'Cacimba de Dentro - PB',
  55833380 => 'Puxinanã - PB',
  55833381 => 'Montadas - PB',
  55833382 => 'Juazeirinho - PB',
  55833383 => 'Soledade - PB',
  55833384 => 'Pocinhos - PB',
  55833385 => 'Cubati - PB',
  55833386 => 'Gurjão - PB',
  55833387 => 'São Sebastião de Lagoa de Roça - PB',
  55833388 => 'Seridó - PB',
  55833389 => 'Olivedos - PB',
  55833390 => 'Prata - PB',
  55833391 => 'Boqueirão - PB',
  55833392 => 'Queimadas - PB',
  55833393 => 'Fagundes - PB',
  55833394 => 'Ingá - PB',
  55833395 => 'Umbuzeiro - PB',
  55833396 => 'Aroeiras - PB',
  55833397 => 'Natuba - PB',
  55833398 => 'Itatuba - PB',
  55833399 => 'Massaranduba - PB',
  55833419 => 'Santa Teresinha - PB',
  55833421 => 'Patos - PB',
  55833422 => 'Patos - PB',
  55833423 => 'Patos - PB',
  55833424 => 'Salgadinho - PB',
  55833425 => 'Quixabá - PB',
  55833426 => 'Emas - PB',
  55833427 => 'Catingueira - PB',
  55833428 => 'Mãe d\'Água - PB',
  55833429 => 'São Bentinho - PB',
  55833431 => 'Pombal - PB',
  55833433 => 'Coremas - PB',
  55833434 => 'Guarabira - PB',
  55833435 => 'Jericó - PB',
  55833436 => 'Vista Serrana - PB',
  55833437 => 'Cajazeirinhas - PB',
  55833438 => 'Condado - PB',
  55833439 => 'Lagoa - PB',
  55833440 => 'Brejo dos Santos - PB',
  55833441 => 'Catolé do Rocha - PB',
  55833443 => 'Brejo do Cruz - PB',
  55833444 => 'São Bento - PB',
  55833445 => 'Paulista - PB',
  55833447 => 'Belém do Brejo do Cruz - PB',
  55833448 => 'Bom Sucesso - PB',
  55833449 => 'Riacho dos Cavalos - PB',
  55833450 => 'Tavares - PB',
  55833451 => 'Itaporanga - PB',
  55833452 => 'Piancó - PB',
  55833453 => 'Conceição - PB',
  55833454 => 'Ibiara - PB',
  55833455 => 'Santana de Mangueira - PB',
  55833456 => 'Pedra Branca - PB',
  55833457 => 'Princesa Isabel - PB',
  55833458 => 'Manaíra - PB',
  55833459 => 'Nova Olinda - PB',
  55833461 => 'Santa Luzia - PB',
  55833462 => 'São Mamede - PB',
  55833463 => 'Taperoá - PB',
  55833464 => 'Junco do Seridó - PB',
  55833466 => 'Assunção - PB',
  55833467 => 'São José do Sabugi - PB',
  55833469 => 'Várzea - PB',
  55833471 => 'Malta - PB',
  55833472 => 'Teixeira - PB',
  55833473 => 'Desterro - PB',
  55833474 => 'Maturéia - PB',
  55833477 => 'Livramento - PB',
  55833479 => 'Cacimba de Areia - PB',
  55833480 => 'Igaracy - PB',
  55833481 => 'Água Branca - PB',
  55833482 => 'Imaculada - PB',
  55833483 => 'Olho d\'Água - PB',
  55833484 => 'Juru - PB',
  55833485 => 'Santana dos Garrotes - PB',
  55833488 => 'Santa Inês - PB',
  55833489 => 'São José de Caiana - PB',
  55833490 => 'Bonito de Santa Fé - PB',
  55833492 => 'Monte Horebe - PB',
  55833493 => 'Boa Ventura - PB',
  55833494 => 'Diamante - PB',
  55833498 => 'Serra Grande - PB',
  55833499 => 'Aguiar - PB',
  55833511 => 'Patos - PB',
  55833513 => 'João Pessoa - PB',
  55833521 => 'Sousa - PB',
  55833522 => 'Sousa - PB',
  55833525 => 'Sousa - PB',
  55833531 => 'Cajazeiras - PB',
  55833532 => 'Cajazeiras - PB',
  55833533 => 'João Pessoa - PB',
  55833534 => 'Uiraúna - PB',
  55833535 => 'São João do Rio do Peixe - PB',
  55833536 => 'Santa Cruz - PB',
  55833538 => 'São José da Lagoa Tapada - PB',
  55833539 => 'Triunfo - PB',
  55833542 => 'Santa Helena - PB',
  55833543 => 'Aparecida - PB',
  55833544 => 'Marizópolis - PB',
  55833545 => 'São Francisco - PB',
  55833547 => 'Vieirópolis - PB',
  55833552 => 'São José de Piranhas - PB',
  55833553 => 'Carrapateira - PB',
  55833554 => 'Nazarezinho - PB',
  55833556 => 'São Gonçalo - PB',
  55833558 => 'Cachoeira dos Índios - PB',
  55833559 => 'Bom Jesus - PB',
  55833561 => 'Bernardino Batista - PB',
  55833562 => 'Poço Dantas - PB',
  55833564 => 'Poço de José de Moura - PB',
  55833565 => 'João Pessoa - PB',
  55833567 => 'Cabedelo - PB',
  55833612 => 'João Pessoa - PB',
  55833613 => 'Campina Grande - PB',
  55833622 => 'Capim - PB',
  55833623 => 'Cuité de Mamanguape - PB',
  55833624 => 'João Pessoa - PB',
  55833625 => 'Marcação - PB',
  55833627 => 'Pilõezinhos - PB',
  55833629 => 'São Miguel de Taipu - PB',
  55833633 => 'Baraúna - PB',
  55833634 => 'Casserengue - PB',
  55833635 => 'Damião - PB',
  55833636 => 'Frei Martinho - PB',
  55833638 => 'Nova Palmeira - PB',
  55833639 => 'Riachão - PB',
  55833641 => 'Riacho de Santo Antônio - PB',
  55833642 => 'Santa Cecília - PB',
  55833644 => 'Tenório - PB',
  55833664 => 'Santa Rita - PB',
  55833682 => 'São José dos Ramos - PB',
  55833684 => 'Serra da Raiz - PB',
  55833685 => 'Sertãozinho - PB',
  55834009 => 'João Pessoa - PB',
  55834064 => 'Campina Grande - PB',
  55834101 => 'Campina Grande - PB',
  55834105 => 'Cabedelo - PB',
  5584 => 'Rio Grande do Norte',
  55843003 => 'Natal - RN',
  55843011 => 'Natal - RN',
  55843013 => 'Natal - RN',
  55843034 => 'Natal - RN',
  55843061 => 'Mossoró - RN',
  55843062 => 'Mossoró - RN',
  55843065 => 'Mossoró - RN',
  55843084 => 'Natal - RN',
  55843087 => 'Natal - RN',
  55843133 => 'Natal - RN',
  55843198 => 'Natal - RN',
  55843201 => 'Natal - RN',
  55843202 => 'Natal - RN',
  55843204 => 'Natal - RN',
  55843205 => 'Natal - RN',
  55843206 => 'Natal - RN',
  55843207 => 'Natal - RN',
  55843208 => 'Natal - RN',
  55843211 => 'Natal - RN',
  55843212 => 'Natal - RN',
  55843213 => 'Natal - RN',
  55843214 => 'Natal - RN',
  55843217 => 'Natal - RN',
  55843218 => 'Natal - RN',
  55843219 => 'Natal - RN',
  55843220 => 'Natal - RN',
  55843221 => 'Natal - RN',
  55843222 => 'Natal - RN',
  55843223 => 'Natal - RN',
  55843224 => 'Natal - RN',
  55843225 => 'Genipabu - RN',
  55843227 => 'São Gonçalo do Amarante - RN',
  55843228 => 'Ceará-Mirim - RN',
  55843229 => 'São Gonçalo do Amarante - RN',
  55843230 => 'Nísia Floresta - RN',
  55843231 => 'Natal - RN',
  55843232 => 'Natal - RN',
  55843234 => 'Natal - RN',
  55843235 => 'Natal - RN',
  55843236 => 'Natal - RN',
  55843237 => 'Parnamirim - RN',
  55843238 => 'Parnamirim - RN',
  55843239 => 'Nísia Floresta - RN',
  55843240 => 'Montanhas - RN',
  55843241 => 'Canguaretama - RN',
  55843242 => 'Arês - RN',
  55843243 => 'Goianinha - RN',
  55843244 => 'Baía Formosa - RN',
  55843245 => 'Vila Flor - RN',
  55843246 => 'Pipa - RN',
  55843247 => 'Pedro Velho - RN',
  55843248 => 'Senador Georgino Avelino - RN',
  55843249 => 'Espírito Santo - RN',
  55843251 => 'São Paulo do Potengi - RN',
  55843252 => 'Sítio Novo - RN',
  55843253 => 'Bom Jesus - RN',
  55843254 => 'São Pedro - RN',
  55843255 => 'Senador Elói de Souza - RN',
  55843256 => 'Boa Saúde - RN',
  55843257 => 'Lagoa Salgada - RN',
  55843258 => 'São Tomé - RN',
  55843259 => 'Barcelona - RN',
  55843260 => 'São Bento do Norte - RN',
  55843261 => 'Maxaranguape - RN',
  55843262 => 'João Câmara - RN',
  55843263 => 'Touros - RN',
  55843264 => 'Taipu - RN',
  55843265 => 'Poço Branco - RN',
  55843266 => 'Pureza - RN',
  55843267 => 'Ielmo Marinho - RN',
  55843268 => 'Caiçara do Rio do Vento - RN',
  55843269 => 'Riachuelo - RN',
  55843271 => 'Macaíba - RN',
  55843272 => 'Parnamirim - RN',
  55843273 => 'São José de Mipibu - RN',
  55843274 => 'Ceará-Mirim - RN',
  55843275 => 'Vera Cruz - RN',
  55843276 => 'Monte Alegre - RN',
  55843277 => 'Nísia Floresta - RN',
  55843278 => 'São Gonçalo do Amarante - RN',
  55843279 => 'Extremoz - RN',
  55843281 => 'Nova Cruz - RN',
  55843282 => 'Santo Antônio - RN',
  55843283 => 'Brejinho - RN',
  55843284 => 'Serrinha - RN',
  55843285 => 'Várzea - RN',
  55843286 => 'Passagem - RN',
  55843287 => 'Lagoa D\'Anta - RN',
  55843288 => 'Passa e Fica - RN',
  55843289 => 'Serra de São Bento - RN',
  55843291 => 'Santa Cruz - RN',
  55843292 => 'Tangará - RN',
  55843293 => 'Serra Caiada - RN',
  55843294 => 'São José do Campestre - RN',
  55843295 => 'Jaçanã - RN',
  55843297 => 'Japi - RN',
  55843298 => 'São Bento do Trairí - RN',
  55843299 => 'Coronel Ezequiel - RN',
  55843301 => 'Natal - RN',
  55843312 => 'Mossoró - RN',
  55843314 => 'Mossoró - RN',
  55843315 => 'Mossoró - RN',
  55843316 => 'Mossoró - RN',
  55843317 => 'Mossoró - RN',
  55843318 => 'Mossoró - RN',
  55843320 => 'Baraúna - RN',
  55843321 => 'Mossoró - RN',
  55843323 => 'Mossoró - RN',
  55843325 => 'Upanema - RN',
  55843326 => 'Tibau - RN',
  55843327 => 'Grossos - RN',
  55843328 => 'Governador Dix-Sept Rosado - RN',
  55843329 => 'Felipe Guerra - RN',
  55843330 => 'Itajá - RN',
  55843331 => 'Açu - RN',
  55843332 => 'Areia Branca - RN',
  55843333 => 'Apodi - RN',
  55843334 => 'Serra do Mel - RN',
  55843335 => 'Ipanguaçu - RN',
  55843336 => 'São Rafael - RN',
  55843337 => 'Caraúbas - RN',
  55843338 => 'Carnaubais - RN',
  55843351 => 'Pau dos Ferros - RN',
  55843353 => 'São Miguel - RN',
  55843354 => 'Encanto - RN',
  55843355 => 'Venha-Ver - RN',
  55843356 => 'Doutor Severiano - RN',
  55843357 => 'Coronel João Pessoa - RN',
  55843358 => 'Rafael Fernandes - RN',
  55843359 => 'Água Nova - RN',
  55843361 => 'Patu - RN',
  55843362 => 'Campo Grande - RN',
  55843363 => 'Rafael Godeiro - RN',
  55843364 => 'Olho-D\'Água do Borges - RN',
  55843365 => 'Messias Targino - RN',
  55843366 => 'Janduís - RN',
  55843367 => 'Paraú - RN',
  55843368 => 'Triunfo Potiguar - RN',
  55843371 => 'Itaú - RN',
  55843372 => 'Severiano Melo - RN',
  55843373 => 'Rodolfo Fernandes - RN',
  55843374 => 'Riacho da Cruz - RN',
  55843375 => 'Taboleiro Grande - RN',
  55843376 => 'Viçosa - RN',
  55843377 => 'Portalegre - RN',
  55843378 => 'São Francisco do Oeste - RN',
  55843379 => 'Francisco Dantas - RN',
  55843381 => 'Alexandria - RN',
  55843382 => 'Luís Gomes - RN',
  55843383 => 'José da Penha - RN',
  55843384 => 'Pilões - RN',
  55843385 => 'Marcelino Vieira - RN',
  55843386 => 'Tenente Ananias - RN',
  55843387 => 'Riacho de Santana - RN',
  55843388 => 'Major Sales - RN',
  55843391 => 'Martins - RN',
  55843392 => 'Antônio Martins - RN',
  55843393 => 'João Dias - RN',
  55843394 => 'Frutuoso Gomes - RN',
  55843395 => 'Almino Afonso - RN',
  55843396 => 'Lucrécia - RN',
  55843397 => 'Umarizal - RN',
  55843398 => 'Serrinha dos Pintos - RN',
  55843405 => 'Currais Novos - RN',
  55843412 => 'Currais Novos - RN',
  55843417 => 'Caicó - RN',
  55843421 => 'Caicó - RN',
  55843422 => 'Mossoró - RN',
  55843423 => 'Jardim de Piranhas - RN',
  55843424 => 'Ipueira - RN',
  55843425 => 'São João do Sabugi - RN',
  55843426 => 'Serra Negra do Norte - RN',
  55843427 => 'Timbaúba dos Batistas - RN',
  55843428 => 'São Fernando - RN',
  55843429 => 'Jucurutu - RN',
  55843431 => 'Currais Novos - RN',
  55843432 => 'Campo Redondo - RN',
  55843433 => 'Acari - RN',
  55843434 => 'Santana do Matos - RN',
  55843435 => 'Florânia - RN',
  55843436 => 'São Vicente - RN',
  55843437 => 'Lagoa Nova - RN',
  55843438 => 'Tenente Laurentino Cruz - RN',
  55843439 => 'Bodó - RN',
  55843471 => 'Parelhas - RN',
  55843472 => 'Jardim do Seridó - RN',
  55843473 => 'Cruzeta - RN',
  55843475 => 'Equador - RN',
  55843476 => 'Santana do Seridó - RN',
  55843477 => 'Ouro Branco - RN',
  55843478 => 'São José do Seridó - RN',
  55843479 => 'Carnaúba dos Dantas - RN',
  55843488 => 'Cerro Corá - RN',
  55843504 => 'Caicó - RN',
  55843521 => 'Macau - RN',
  55843522 => 'Pendências - RN',
  55843523 => 'Alto do Rodrigues - RN',
  55843525 => 'Guamaré - RN',
  55843526 => 'Porto do Mangue - RN',
  55843531 => 'Angicos - RN',
  55843532 => 'Lajes - RN',
  55843533 => 'Afonso Bezerra - RN',
  55843534 => 'Pedro Avelino - RN',
  55843535 => 'Jardim de Angicos - RN',
  55843536 => 'Pedra Preta - RN',
  55843538 => 'Fernando Pedroza - RN',
  55843552 => 'Galinhos - RN',
  55843553 => 'Jandaíra - RN',
  55843555 => 'Pedra Grande - RN',
  55843604 => 'Parnamirim - RN',
  55843605 => 'Natal - RN',
  55843606 => 'Natal - RN',
  55843608 => 'Natal - RN',
  55843611 => 'Natal - RN',
  55843613 => 'Natal - RN',
  55843614 => 'Natal - RN',
  55843616 => 'Natal - RN',
  55843618 => 'Natal - RN',
  55843631 => 'Macaíba - RN',
  55843634 => 'Ceará-Mirim - RN',
  55843635 => 'Santa Maria - RN',
  55843636 => 'Ruy Barbosa - RN',
  55843637 => 'Bento Fernandes - RN',
  55843638 => 'Rio do Fogo - RN',
  55843641 => 'Natal - RN',
  55843642 => 'Natal - RN',
  55843643 => 'Parnamirim - RN',
  55843644 => 'Parnamirim - RN',
  55843646 => 'Natal - RN',
  55843647 => 'Parnamirim - RN',
  55843653 => 'Natal - RN',
  55843654 => 'Natal - RN',
  55843661 => 'Natal - RN',
  55843662 => 'Natal - RN',
  55843663 => 'Natal - RN',
  55843664 => 'Natal - RN',
  55843672 => 'Natal - RN',
  55843673 => 'Natal - RN',
  55843674 => 'Natal - RN',
  55843691 => 'Lajes Pintadas - RN',
  55843692 => 'Lagoa de Pedras - RN',
  55843693 => 'Touros - RN',
  55843694 => 'Monte das Gameleiras - RN',
  55843695 => 'Lagoa de Velhos - RN',
  55843696 => 'Caiçara do Norte - RN',
  55843697 => 'Parazinho - RN',
  55843737 => 'Parnamirim - RN',
  55844102 => 'Natal - RN',
  55844109 => 'Mossoró - RN',
  5585 => 'Ceará',
  55853004 => 'Fortaleza - CE',
  55853012 => 'Caucaia - CE',
  55853014 => 'Maracanaú - CE',
  55853022 => 'Fortaleza - CE',
  55853031 => 'Fortaleza - CE',
  55853048 => 'Fortaleza - CE',
  55853051 => 'Fortaleza - CE',
  55853084 => 'Fortaleza - CE',
  55853092 => 'Fortaleza - CE',
  55853104 => 'Fortaleza - CE',
  55853111 => 'Fortaleza - CE',
  55853113 => 'Aquiraz - CE',
  55853123 => 'Caucaia - CE',
  55853133 => 'Fortaleza - CE',
  55853181 => 'Fortaleza - CE',
  55853182 => 'Fortaleza - CE',
  55853198 => 'Fortaleza - CE',
  55853201 => 'Fortaleza - CE',
  55853204 => 'Fortaleza - CE',
  55853205 => 'Fortaleza - CE',
  55853206 => 'Fortaleza - CE',
  55853207 => 'Fortaleza - CE',
  55853208 => 'Fortaleza - CE',
  55853209 => 'Fortaleza - CE',
  55853211 => 'Fortaleza - CE',
  55853212 => 'Fortaleza - CE',
  55853213 => 'Caucaia - CE',
  55853216 => 'Fortaleza - CE',
  55853217 => 'Fortaleza - CE',
  55853218 => 'Fortaleza - CE',
  55853219 => 'Fortaleza - CE',
  55853221 => 'Fortaleza - CE',
  55853224 => 'Fortaleza - CE',
  55853225 => 'Fortaleza - CE',
  55853226 => 'Fortaleza - CE',
  55853228 => 'Fortaleza - CE',
  55853229 => 'Fortaleza - CE',
  55853231 => 'Fortaleza - CE',
  55853233 => 'Fortaleza - CE',
  55853234 => 'Fortaleza - CE',
  55853235 => 'Fortaleza - CE',
  55853236 => 'Fortaleza - CE',
  55853238 => 'Fortaleza - CE',
  55853241 => 'Fortaleza - CE',
  55853242 => 'Fortaleza - CE',
  55853244 => 'Fortaleza - CE',
  55853245 => 'Fortaleza - CE',
  55853246 => 'Fortaleza - CE',
  55853248 => 'Fortaleza - CE',
  55853249 => 'Fortaleza - CE',
  55853250 => 'Fortaleza - CE',
  55853251 => 'Fortaleza - CE',
  55853252 => 'Fortaleza - CE',
  55853253 => 'Fortaleza - CE',
  55853254 => 'Fortaleza - CE',
  55853255 => 'Fortaleza - CE',
  55853256 => 'Fortaleza - CE',
  55853258 => 'Fortaleza - CE',
  55853259 => 'Fortaleza - CE',
  55853260 => 'Eusébio - CE',
  55853261 => 'Fortaleza - CE',
  55853262 => 'Fortaleza - CE',
  55853263 => 'Fortaleza - CE',
  55853264 => 'Fortaleza - CE',
  55853265 => 'Fortaleza - CE',
  55853267 => 'Fortaleza - CE',
  55853268 => 'Fortaleza - CE',
  55853269 => 'Fortaleza - CE',
  55853271 => 'Fortaleza - CE',
  55853273 => 'Fortaleza - CE',
  55853274 => 'Fortaleza - CE',
  55853275 => 'Fortaleza - CE',
  55853276 => 'Fortaleza - CE',
  55853278 => 'Fortaleza - CE',
  55853279 => 'Fortaleza - CE',
  55853282 => 'Fortaleza - CE',
  55853284 => 'Fortaleza - CE',
  55853285 => 'Fortaleza - CE',
  55853286 => 'Fortaleza - CE',
  55853287 => 'Fortaleza - CE',
  55853289 => 'Fortaleza - CE',
  55853290 => 'Fortaleza - CE',
  55853291 => 'Fortaleza - CE',
  55853292 => 'Fortaleza - CE',
  55853293 => 'Fortaleza - CE',
  55853294 => 'Fortaleza - CE',
  55853295 => 'Fortaleza - CE',
  55853297 => 'Fortaleza - CE',
  55853301 => 'Beberibe - CE',
  55853302 => 'Apuiarés - CE',
  55853304 => 'Fortaleza - CE',
  55853305 => 'Fortaleza - CE',
  55853306 => 'Fortaleza - CE',
  55853315 => 'São Gonçalo do Amarante - CE',
  55853319 => 'Chorozinho - CE',
  55853320 => 'Paramoti - CE',
  55853321 => 'Guaramiranga - CE',
  55853322 => 'Ocara - CE',
  55853323 => 'Tejuçuoca - CE',
  55853324 => 'Caridade - CE',
  55853325 => 'Pacoti - CE',
  55853326 => 'Capistrano - CE',
  55853328 => 'Mulungu - CE',
  55853329 => 'Aratuba - CE',
  55853331 => 'Barreira - CE',
  55853332 => 'Redenção - CE',
  55853334 => 'Cascavel - CE',
  55853336 => 'Horizonte - CE',
  55853337 => 'Aracoiaba - CE',
  55853338 => 'Beberibe - CE',
  55853339 => 'Palmácia - CE',
  55853341 => 'Maranguape - CE',
  55853342 => 'Caucaia - CE',
  55853343 => 'Canindé - CE',
  55853344 => 'Paracuru - CE',
  55853345 => 'Pacatuba - CE',
  55853346 => 'Itapagé - CE',
  55853347 => 'Baturité - CE',
  55853348 => 'Pacajus - CE',
  55853351 => 'Trairi - CE',
  55853352 => 'Pentecoste - CE',
  55853353 => 'Uruburetama - CE',
  55853355 => 'São Luís do Curu - CE',
  55853356 => 'Apuiarés - CE',
  55853357 => 'General Sampaio - CE',
  55853358 => 'Tururu - CE',
  55853361 => 'Aquiraz - CE',
  55853362 => 'Aquiraz - CE',
  55853363 => 'Paraipaba - CE',
  55853364 => 'Umirim - CE',
  55853365 => 'Canindé - CE',
  55853366 => 'Fortaleza - CE',
  55853369 => 'Maranguape - CE',
  55853371 => 'Maracanaú - CE',
  55853372 => 'São Gonçalo do Amarante - CE',
  55853373 => 'Acarape - CE',
  55853375 => 'Pindoretama - CE',
  55853376 => 'Guaiúba - CE',
  55853377 => 'Itaitinga - CE',
  55853381 => 'Maracanaú - CE',
  55853382 => 'Maracanaú - CE',
  55853383 => 'Maracanaú - CE',
  55853384 => 'Maracanaú - CE',
  55853387 => 'Caucaia - CE',
  55853403 => 'Fortaleza - CE',
  55853404 => 'Fortaleza - CE',
  55853453 => 'Fortaleza - CE',
  55853454 => 'Fortaleza - CE',
  55853455 => 'Fortaleza - CE',
  55853456 => 'Fortaleza - CE',
  55853457 => 'Fortaleza - CE',
  55853458 => 'Fortaleza - CE',
  55853461 => 'Fortaleza - CE',
  55853462 => 'Fortaleza - CE',
  55853463 => 'Fortaleza - CE',
  55853464 => 'Fortaleza - CE',
  55853468 => 'Fortaleza - CE',
  55853471 => 'Fortaleza - CE',
  55853473 => 'Fortaleza - CE',
  55853474 => 'Fortaleza - CE',
  55853475 => 'Caucaia - CE',
  55853476 => 'Fortaleza - CE',
  55853477 => 'Fortaleza - CE',
  55853478 => 'Fortaleza - CE',
  55853479 => 'Fortaleza - CE',
  55853482 => 'Fortaleza - CE',
  55853483 => 'Fortaleza - CE',
  55853485 => 'Fortaleza - CE',
  55853486 => 'Fortaleza - CE',
  55853488 => 'Fortaleza - CE',
  55853489 => 'Fortaleza - CE',
  55853491 => 'Fortaleza - CE',
  55853492 => 'Fortaleza - CE',
  55853493 => 'Fortaleza - CE',
  55853494 => 'Fortaleza - CE',
  55853495 => 'Fortaleza - CE',
  55853496 => 'Fortaleza - CE',
  55853497 => 'Fortaleza - CE',
  55853498 => 'Fortaleza - CE',
  55853650 => 'Carnaubal - CE',
  55853667 => 'Itarema - CE',
  55853813 => 'São Paulo - SP',
  55853877 => 'Fortaleza - CE',
  55853923 => 'Fortaleza - CE',
  55853924 => 'Fortaleza - CE',
  55854003 => 'Fortaleza - CE',
  55854007 => 'Maracanaú - CE',
  55854042 => 'Fortaleza - CE',
  55854062 => 'Fortaleza - CE',
  55854102 => 'Fortaleza - CE',
  55854117 => 'Maracanaú - CE',
  55855672 => 'São Paulo - SP',
  5586 => 'Piauí',
  55862106 => 'Teresina - PI',
  55862107 => 'Teresina - PI',
  55863081 => 'Teresina - PI',
  55863086 => 'Teresina - PI',
  55863087 => 'Teresina - PI',
  55863089 => 'Teresina - PI',
  55863122 => 'Teresina - PI',
  55863131 => 'Teresina - PI',
  55863133 => 'Teresina - PI',
  55863194 => 'Teresina - PI',
  55863198 => 'Teresina - PI',
  55863212 => 'Timon - MA',
  55863214 => 'Teresina - PI',
  55863216 => 'Teresina - PI',
  55863219 => 'Teresina - PI',
  55863220 => 'Teresina - PI',
  55863221 => 'Teresina - PI',
  55863222 => 'Teresina - PI',
  55863223 => 'Teresina - PI',
  55863224 => 'Teresina - PI',
  55863226 => 'Teresina - PI',
  55863227 => 'Teresina - PI',
  55863228 => 'Teresina - PI',
  55863239 => 'Santo Antônio dos Milagres - PI',
  55863240 => 'Cabeceiras do Piauí - PI',
  55863241 => 'Boa Hora - PI',
  55863242 => 'Barras - PI',
  55863243 => 'Porto - PI',
  55863244 => 'Miguel Alves - PI',
  55863245 => 'Nossa Senhora dos Remédios - PI',
  55863247 => 'Castelo do Piauí - PI',
  55863248 => 'Buriti dos Montes - PI',
  55863249 => 'São Miguel do Tapuio - PI',
  55863250 => 'Prata do Piauí - PI',
  55863251 => 'São João da Serra - PI',
  55863252 => 'Campo Maior - PI',
  55863253 => 'Juazeiro do Piauí - PI',
  55863254 => 'Assunção do Piauí - PI',
  55863256 => 'Alto Longá - PI',
  55863258 => 'Monsenhor Gil - PI',
  55863259 => 'Lagoa do Piauí - PI',
  55863260 => 'Demerval Lobão - PI',
  55863262 => 'Altos - PI',
  55863263 => 'Cocal de Telha - PI',
  55863264 => 'José de Freitas - PI',
  55863265 => 'União - PI',
  55863267 => 'Lagoa Alegre - PI',
  55863269 => 'Beneditinos - PI',
  55863271 => 'Pedro II - PI',
  55863273 => 'Curralinhos - PI',
  55863274 => 'Brasileira - PI',
  55863276 => 'Piripiri - PI',
  55863277 => 'Capitão de Campos - PI',
  55863280 => 'São Pedro do Piauí - PI',
  55863281 => 'Milton Brandão - PI',
  55863282 => 'Água Branca - PI',
  55863284 => 'Barro Duro - PI',
  55863285 => 'Elesbão Veloso - PI',
  55863288 => 'Palmeirais - PI',
  55863289 => 'São Gonçalo do Piauí - PI',
  55863291 => 'Jardim do Mulato - PI',
  55863292 => 'Amarante - PI',
  55863293 => 'Regeneração - PI',
  55863295 => 'São Félix do Piauí - PI',
  55863297 => 'Agricolândia - PI',
  55863298 => 'Angical do Piauí - PI',
  55863299 => 'Hugo Napoleão - PI',
  55863301 => 'Teresina - PI',
  55863302 => 'Teresina - PI',
  55863303 => 'Teresina - PI',
  55863304 => 'Teresina - PI',
  55863315 => 'Parnaíba - PI',
  55863321 => 'Parnaíba - PI',
  55863322 => 'Parnaíba - PI',
  55863323 => 'Parnaíba - PI',
  55863326 => 'Timon - MA',
  55863332 => 'Caxingó - PI',
  55863340 => 'Matias Olímpio - PI',
  55863343 => 'Piracuruca - PI',
  55863345 => 'São João da Fronteira - PI',
  55863346 => 'São José do Divino - PI',
  55863347 => 'Batalha - PI',
  55863360 => 'Joaquim Pires - PI',
  55863362 => 'Cocal - PI',
  55863363 => 'Buriti dos Lopes - PI',
  55863366 => 'Luís Correia - PI',
  55863367 => 'Luís Correia - PI',
  55863369 => 'Cajueiro da Praia - PI',
  55863383 => 'Esperantina - PI',
  55863385 => 'São João do Arraial - PI',
  55863393 => 'Luzilândia - PI',
  55863474 => 'Pimenteiras - PI',
  55863477 => 'Inhuma - PI',
  55863582 => 'São Raimundo Nonato - PI',
  55864009 => 'Teresina - PI',
  55864020 => 'Teresina - PI',
  5587 => 'Pernambuco',
  55872101 => 'Petrolina - PE',
  55873031 => 'Petrolina - PE',
  55873032 => 'Petrolina - PE',
  55873035 => 'Petrolina - PE',
  55873201 => 'Petrolina - PE',
  55873202 => 'Petrolina - PE',
  55873221 => 'Garanhuns - PE',
  55873272 => 'Parnamirim - RN',
  55873761 => 'Garanhuns - PE',
  55873763 => 'Garanhuns - PE',
  55873764 => 'Garanhuns - PE',
  55873771 => 'Bom Conselho - PE',
  55873772 => 'Correntes - PE',
  55873773 => 'Lajedo - PE',
  55873775 => 'Águas Belas - PE',
  55873779 => 'Jupi - PE',
  55873781 => 'Canhotinho - PE',
  55873782 => 'Saloá - PE',
  55873783 => 'Caetés - PE',
  55873784 => 'São João - PE',
  55873785 => 'Lagoa do Ouro - PE',
  55873786 => 'Iati - PE',
  55873787 => 'Paranatama - PE',
  55873788 => 'Angelim - PE',
  55873789 => 'Brejão - PE',
  55873791 => 'Palmeirina - PE',
  55873792 => 'Terezinha - PE',
  55873793 => 'Calçado - PE',
  55873794 => 'Ibirajuba - PE',
  55873795 => 'Jurema - PE',
  55873796 => 'Capoeiras - PE',
  55873798 => 'Bom Conselho - PE',
  55873803 => 'Pesqueira - PE',
  55873809 => 'Iguaraci - PE',
  55873811 => 'Jirau - PE',
  55873816 => 'Buíque - PE',
  55873817 => 'Alagoinha - PE',
  55873821 => 'Arcoverde - PE',
  55873822 => 'Arcoverde - PE',
  55873828 => 'Tuparetama - PE',
  55873829 => 'Ingazeira - PE',
  55873830 => 'Solidão - PE',
  55873831 => 'Serra Talhada - PE',
  55873833 => 'Venturosa - PE',
  55873834 => 'Poção - PE',
  55873835 => 'Pesqueira - PE',
  55873836 => 'Sanharó - PE',
  55873837 => 'Iguaraci - PE',
  55873838 => 'Afogados da Ingazeira - PE',
  55873839 => 'Alagoinha - PE',
  55873840 => 'Inajá - PE',
  55873841 => 'Sertânia - PE',
  55873842 => 'Ibimirim - PE',
  55873843 => 'Tacaratu - PE',
  55873844 => 'São José do Egito - PE',
  55873845 => 'Calumbi - PE',
  55873846 => 'Triunfo - PE',
  55873847 => 'Tabira - PE',
  55873848 => 'Custódia - PE',
  55873849 => 'Itaíba - PE',
  55873850 => 'Brejinho - PE',
  55873851 => 'Petrolândia - PE',
  55873852 => 'Betânia - PE',
  55873853 => 'Itapetim - PE',
  55873854 => 'Carnaíba - PE',
  55873855 => 'Buíque - PE',
  55873856 => 'Tupanatinga - PE',
  55873857 => 'Flores - PE',
  55873858 => 'Pedra - PE',
  55873859 => 'Santa Terezinha - PE',
  55873860 => 'Petrolina - PE',
  55873861 => 'Petrolina - PE',
  55873862 => 'Petrolina - PE',
  55873863 => 'Petrolina - PE',
  55873864 => 'Petrolina - PE',
  55873865 => 'Dormentes - PE',
  55873866 => 'Petrolina - PE',
  55873867 => 'Petrolina - PE',
  55873868 => 'Afrânio - PE',
  55873869 => 'Santa Maria da Boa Vista - PE',
  55873870 => 'Trindade - PE',
  55873871 => 'Salgueiro - PE',
  55873872 => 'Araripina - PE',
  55873873 => 'Araripina - PE',
  55873874 => 'Ouricuri - PE',
  55873875 => 'Cabrobó - PE',
  55873876 => 'Belém de São Francisco - PE',
  55873877 => 'Floresta - PE',
  55873878 => 'Bodocó - PE',
  55873879 => 'Exu - PE',
  55873880 => 'Granito - PE',
  55873881 => 'Ipubi - PE',
  55873882 => 'Serrita - PE',
  55873883 => 'Parnamirim - PE',
  55873884 => 'São José do Belmonte - PE',
  55873885 => 'Mirandiba - PE',
  55873886 => 'Verdejante - PE',
  55873887 => 'Orocó - PE',
  55873889 => 'Cedro - PE',
  55873891 => 'Moreilândia - PE',
  55873892 => 'Terra Nova - PE',
  55873893 => 'Itacuruba - PE',
  55873929 => 'Serra Talhada - PE',
  55873939 => 'Flores - PE',
  55873945 => 'Serra Talhada - PE',
  55873946 => 'Salgueiro - PE',
  55873948 => 'Bom Nome - PE',
  55873964 => 'Serrolândia - PE',
  55873966 => 'Exu - PE',
  55873967 => 'Ouricuri - PE',
  55873983 => 'Petrolina - PE',
  55873991 => 'Vermelho - PE',
  5588 => 'Ceará',
  55883085 => 'Juazeiro do Norte - CE',
  55883111 => 'Sobral - CE',
  55883112 => 'Sobral - CE',
  55883115 => 'Juazeiro do Norte - CE',
  55883221 => 'Juazeiro do Norte - CE',
  55883303 => 'Aracati - CE',
  55883400 => 'Limoeiro do Norte - CE',
  55883401 => 'Limoeiro do Norte - CE',
  55883404 => 'Russas - CE',
  55883409 => 'Russas - CE',
  55883410 => 'Itaiçaba - CE',
  55883411 => 'Russas - CE',
  55883412 => 'Quixadá - CE',
  55883413 => 'Fortim - CE',
  55883414 => 'Quixadá - CE',
  55883415 => 'Palhano - CE',
  55883416 => 'Tauá - CE',
  55883418 => 'Jaguaruana - CE',
  55883419 => 'Arneiroz - CE',
  55883420 => 'São João do Jaguaribe - CE',
  55883421 => 'Aracati - CE',
  55883422 => 'Morada Nova - CE',
  55883423 => 'Limoeiro do Norte - CE',
  55883424 => 'Tabuleiro do Norte - CE',
  55883425 => 'Ibicuitinga - CE',
  55883426 => 'Banabuiú - CE',
  55883427 => 'Boa Viagem - CE',
  55883428 => 'Iracema - CE',
  55883429 => 'Alto Santo - CE',
  55883431 => 'Itapiúna - CE',
  55883432 => 'Icapuí - CE',
  55883433 => 'Aracati - CE',
  55883434 => 'Ererê - CE',
  55883435 => 'Potiretama - CE',
  55883436 => 'Itatira - CE',
  55883437 => 'Tauá - CE',
  55883438 => 'Choró - CE',
  55883439 => 'Ibaretama - CE',
  55883441 => 'Quixeramobim - CE',
  55883442 => 'Madalena - CE',
  55883443 => 'Quixeré - CE',
  55883444 => 'Quixeramobim - CE',
  55883446 => 'Aracati - CE',
  55883447 => 'Limoeiro do Norte - CE',
  55883448 => 'Parambu - CE',
  55883449 => 'Senador Pompeu - CE',
  55883451 => 'Dom Maurício - CE',
  55883501 => 'Juazeiro do Norte - CE',
  55883510 => 'Iguatu - CE',
  55883511 => 'Juazeiro do Norte - CE',
  55883512 => 'Juazeiro do Norte - CE',
  55883513 => 'Crato - CE',
  55883514 => 'Cariús - CE',
  55883515 => 'Pedra Branca - CE',
  55883516 => 'Piquet Carneiro - CE',
  55883517 => 'Jucás - CE',
  55883518 => 'Solonópole - CE',
  55883519 => 'Granjeiro - CE',
  55883521 => 'Crato - CE',
  55883522 => 'Jaguaribe - CE',
  55883523 => 'Crato - CE',
  55883524 => 'Aiuaba - CE',
  55883525 => 'Antonina do Norte - CE',
  55883526 => 'Saboeiro - CE',
  55883527 => 'Pereiro - CE',
  55883529 => 'Milhã - CE',
  55883530 => 'Araripe - CE',
  55883531 => 'Brejo Santo - CE',
  55883532 => 'Barbalha - CE',
  55883533 => 'Campos Sales - CE',
  55883535 => 'Assaré - CE',
  55883536 => 'Lavras da Mangabeira - CE',
  55883537 => 'Salitre - CE',
  55883538 => 'Potengi - CE',
  55883539 => 'Baixio - CE',
  55883541 => 'Várzea Alegre - CE',
  55883542 => 'Missão Velha - CE',
  55883543 => 'Aurora - CE',
  55883544 => 'Farias Brito - CE',
  55883545 => 'Santana do Cariri - CE',
  55883546 => 'Nova Olinda - CE',
  55883547 => 'Caririaçu - CE',
  55883548 => 'Altaneira - CE',
  55883549 => 'Tarrafas - CE',
  55883552 => 'Mauriti - CE',
  55883553 => 'Milagres - CE',
  55883554 => 'Barro - CE',
  55883555 => 'Jardim - CE',
  55883556 => 'Catarina - CE',
  55883557 => 'Porteiras - CE',
  55883558 => 'Abaiara - CE',
  55883559 => 'Penaforte - CE',
  55883561 => 'Icó - CE',
  55883562 => 'Mineirolândia - CE',
  55883563 => 'Icó - CE',
  55883564 => 'Cedro - CE',
  55883565 => 'Acopiara - CE',
  55883566 => 'Juazeiro do Norte - CE',
  55883567 => 'Ipaumirim - CE',
  55883568 => 'Jaguaribara - CE',
  55883569 => 'Deputado Irapuan Pinheiro - CE',
  55883571 => 'Juazeiro do Norte - CE',
  55883574 => 'Barbalha - CE',
  55883575 => 'Jati - CE',
  55883576 => 'Jaguaretama - CE',
  55883578 => 'Umari - CE',
  55883579 => 'Quixelô - CE',
  55883581 => 'Iguatu - CE',
  55883582 => 'Iguatu - CE',
  55883583 => 'Mombaça - CE',
  55883584 => 'Orós - CE',
  55883585 => 'Iguatu - CE',
  55883586 => 'Crato - CE',
  55883587 => 'Juazeiro do Norte - CE',
  55883603 => 'Cruz - CE',
  55883611 => 'Sobral - CE',
  55883613 => 'Sobral - CE',
  55883614 => 'Sobral - CE',
  55883617 => 'Tamboril - CE',
  55883619 => 'Forquilha - CE',
  55883621 => 'Camocim - CE',
  55883623 => 'Barroquinha - CE',
  55883624 => 'Granja - CE',
  55883625 => 'Chaval - CE',
  55883626 => 'São Benedito - CE',
  55883627 => 'Martinópole - CE',
  55883628 => 'Santa Quitéria - CE',
  55883629 => 'Novo Oriente - CE',
  55883630 => 'Miraíma - CE',
  55883631 => 'Itapipoca - CE',
  55883632 => 'Viçosa do Ceará - CE',
  55883633 => 'Ararendá - CE',
  55883634 => 'Ubajara - CE',
  55883635 => 'Irauçuba - CE',
  55883636 => 'Amontada - CE',
  55883637 => 'Reriutaba - CE',
  55883638 => 'Hidrolândia - CE',
  55883639 => 'Varjota - CE',
  55883640 => 'Alcântaras - CE',
  55883641 => 'Pacujá - CE',
  55883642 => 'Moraújo - CE',
  55883643 => 'Massapê - CE',
  55883644 => 'Santana do Acaraú - CE',
  55883645 => 'Coreaú - CE',
  55883646 => 'Cariré - CE',
  55883647 => 'Groaíras - CE',
  55883648 => 'Uruoca - CE',
  55883649 => 'Meruoca - CE',
  55883650 => 'Carnaubal - CE',
  55883652 => 'Guaraciaba do Norte - CE',
  55883653 => 'Ibiapina - CE',
  55883654 => 'Mucambo - CE',
  55883655 => 'Frecheirinha - CE',
  55883656 => 'Graça - CE',
  55883657 => 'Quiterianópolis - CE',
  55883658 => 'Poranga - CE',
  55883659 => 'Croatá - CE',
  55883660 => 'Cruz - CE',
  55883661 => 'Acaraú - CE',
  55883663 => 'Bela Cruz - CE',
  55883664 => 'Marco - CE',
  55883665 => 'Morrinhos - CE',
  55883667 => 'Itarema - CE',
  55883668 => 'Senador Sá - CE',
  55883669 => 'Vila de Jericoacoara - CE',
  55883671 => 'Tianguá - CE',
  55883672 => 'Nova Russas - CE',
  55883673 => 'Itapipoca - CE',
  55883674 => 'Acaraú - CE',
  55883675 => 'Independência - CE',
  55883677 => 'Sobral - CE',
  55883683 => 'Ipu - CE',
  55883684 => 'Ipaporanga - CE',
  55883685 => 'Ipueiras - CE',
  55883686 => 'Catunda - CE',
  55883691 => 'Crateús - CE',
  55883692 => 'Crateús - CE',
  55883695 => 'Sobral - CE',
  55883696 => 'Monsenhor Tabosa - CE',
  55884102 => 'Crato - CE',
  55884141 => 'Juazeiro do Norte - CE',
  5589 => 'Piauí',
  55892101 => 'Picos - PI',
  55893415 => 'Picos - PI',
  55893421 => 'Picos - PI',
  55893422 => 'Picos - PI',
  55893424 => 'Paquetá - PI',
  55893425 => 'Sussuapara - PI',
  55893426 => 'Geminiano - PI',
  55893427 => 'Tanque do Piauí - PI',
  55893428 => 'Santa Rosa do Piauí - PI',
  55893429 => 'São João da Canabrava - PI',
  55893431 => 'Padre Marcos - PI',
  55893432 => 'Cajazeiras do Piauí - PI',
  55893433 => 'Monsenhor Hipólito - PI',
  55893435 => 'Francisco Macedo - PI',
  55893436 => 'Alegrete do Piauí - PI',
  55893438 => 'São Julião - PI',
  55893439 => 'Marcolândia - PI',
  55893440 => 'Ipiranga do Piauí - PI',
  55893442 => 'Alagoinha do Piauí - PI',
  55893443 => 'Santana do Piauí - PI',
  55893444 => 'Dom Expedito Lopes - PI',
  55893445 => 'Santa Cruz do Piauí - PI',
  55893446 => 'Itainópolis - PI',
  55893447 => 'São José do Piauí - PI',
  55893448 => 'Bocaina - PI',
  55893449 => 'Santo Antônio de Lisboa - PI',
  55893450 => 'Francisco Santos - PI',
  55893451 => 'Santo Inácio do Piauí - PI',
  55893452 => 'Wall Ferraz - PI',
  55893453 => 'Pio Ix - PI',
  55893454 => 'Fronteiras - PI',
  55893455 => 'Caldeirão Grande do Piauí - PI',
  55893456 => 'Simões - PI',
  55893457 => 'Jaicós - PI',
  55893459 => 'Patos do Piauí - PI',
  55893461 => 'Colônia do Piauí - PI',
  55893462 => 'Oeiras - PI',
  55893464 => 'Caridade do Piauí - PI',
  55893465 => 'Valença do Piauí - PI',
  55893467 => 'Lagoa do Sítio - PI',
  55893468 => 'Aroazes - PI',
  55893471 => 'Várzea Grande - PI',
  55893472 => 'Francinópolis - PI',
  55893473 => 'Massapê do Piauí - PI',
  55893474 => 'Pimenteiras - PI',
  55893475 => 'Novo Oriente do Piauí - PI',
  55893477 => 'Inhuma - PI',
  55893480 => 'Socorro do Piauí - PI',
  55893482 => 'Simplício Mendes - PI',
  55893483 => 'São João do Piauí - PI',
  55893484 => 'Campinas do Piauí - PI',
  55893485 => 'Isaías Coelho - PI',
  55893487 => 'Paulistana - PI',
  55893488 => 'Jacobina do Piauí - PI',
  55893489 => 'Conceição do Canindé - PI',
  55893492 => 'Campo Alegre do Fidalgo - PI',
  55893493 => 'Acauã - PI',
  55893494 => 'Paes Landim - PI',
  55893495 => 'Queimada Nova - PI',
  55893496 => 'São Francisco de Assis do Piauí - PI',
  55893497 => 'Betânia do Piauí - PI',
  55893498 => 'Lagoa do Barro do Piauí - PI',
  55893499 => 'Bela Vista do Piauí - PI',
  55893515 => 'Floriano - PI',
  55893521 => 'Floriano - PI',
  55893522 => 'Floriano - PI',
  55893523 => 'Floriano - PI',
  55893531 => 'Canto do Buriti - PI',
  55893532 => 'Pajeú do Piauí - PI',
  55893533 => 'Rio Grande do Piauí - PI',
  55893535 => 'Manoel Emídio - PI',
  55893536 => 'Flores do Piauí - PI',
  55893537 => 'Eliseu Martins - PI',
  55893538 => 'Colônia do Gurguéia - PI',
  55893539 => 'Porto Alegre do Piauí - PI',
  55893541 => 'Marcos Parente - PI',
  55893542 => 'Landri Sales - PI',
  55893543 => 'Antônio Almeida - PI',
  55893544 => 'Uruçuí - PI',
  55893546 => 'Bertolínia - PI',
  55893547 => 'São Miguel do Fidalgo - PI',
  55893549 => 'Alvorada do Gurguéia - PI',
  55893550 => 'Jerumenha - PI',
  55893552 => 'Guadalupe - PI',
  55893553 => 'Júlio Borges - PI',
  55893554 => 'São José do Peixe - PI',
  55893555 => 'Arraial - PI',
  55893557 => 'Nazaré do Piauí - PI',
  55893558 => 'São Francisco do Piauí - PI',
  55893559 => 'Itaueira - PI',
  55893560 => 'Francisco Ayres - PI',
  55893562 => 'Bom Jesus - PI',
  55893563 => 'Cristino Castro - PI',
  55893565 => 'Santa Luz - PI',
  55893566 => 'Redenção do Gurguéia - PI',
  55893567 => 'Ribeiro Gonçalves - PI',
  55893568 => 'Palmeira do Piauí - PI',
  55893569 => 'Santa Filomena - PI',
  55893570 => 'Baixa Grande do Ribeiro - PI',
  55893572 => 'Parnaguá - PI',
  55893573 => 'Corrente - PI',
  55893574 => 'Curimatá - PI',
  55893575 => 'Avelino Lopes - PI',
  55893576 => 'Cristalândia do Piauí - PI',
  55893577 => 'Monte Alegre do Piauí - PI',
  55893578 => 'Gilbués - PI',
  55893580 => 'Dom Inocêncio - PI',
  55893582 => 'São Raimundo Nonato - PI',
  55893585 => 'Coronel José Dias - PI',
  55893587 => 'Dirceu Arcoverde - PI',
  55893588 => 'Anísio de Abreu - PI',
  55893589 => 'Caracol - PI',
  55893591 => 'Jurema - PI',
  55894101 => 'Picos - PI',
  5591 => 'Pará',
  55912122 => 'Ananindeua - PA',
  55913011 => 'Paragominas - PA',
  55913014 => 'Ananindeua - PA',
  55913015 => 'Belém - PA',
  55913017 => 'Ananindeua - PA',
  55913031 => 'Ananindeua - PA',
  55913032 => 'Ananindeua - PA',
  55913082 => 'Belém - PA',
  55913087 => 'Belém - PA',
  55913088 => 'Belém - PA',
  55913110 => 'Belém - PA',
  55913116 => 'Belém - PA',
  55913118 => 'Belém - PA',
  55913119 => 'Belém - PA',
  55913120 => 'Belém - PA',
  55913131 => 'Belém - PA',
  55913181 => 'Belém - PA',
  55913182 => 'Belém - PA',
  55913184 => 'Belém - PA',
  55913201 => 'Belém - PA',
  55913202 => 'Belém - PA',
  55913204 => 'Belém - PA',
  55913207 => 'Belém - PA',
  55913210 => 'Belém - PA',
  55913212 => 'Belém - PA',
  55913213 => 'Belém - PA',
  55913214 => 'Belém - PA',
  55913216 => 'Belém - PA',
  55913218 => 'Belém - PA',
  55913221 => 'Belém - PA',
  55913222 => 'Belém - PA',
  55913223 => 'Belém - PA',
  55913224 => 'Belém - PA',
  55913225 => 'Belém - PA',
  55913226 => 'Belém - PA',
  55913227 => 'Belém - PA',
  55913228 => 'Belém - PA',
  55913229 => 'Belém - PA',
  55913230 => 'Belém - PA',
  55913231 => 'Belém - PA',
  55913232 => 'Belém - PA',
  55913233 => 'Belém - PA',
  55913235 => 'Belém - PA',
  55913236 => 'Belém - PA',
  55913237 => 'Belém - PA',
  55913238 => 'Belém - PA',
  55913239 => 'Belém - PA',
  55913241 => 'Belém - PA',
  55913242 => 'Belém - PA',
  55913243 => 'Belém - PA',
  55913244 => 'Belém - PA',
  55913245 => 'Belém - PA',
  55913246 => 'Belém - PA',
  55913248 => 'Belém - PA',
  55913249 => 'Belém - PA',
  55913250 => 'Belém - PA',
  55913251 => 'Belém - PA',
  55913252 => 'Belém - PA',
  55913254 => 'Belém - PA',
  55913255 => 'Belém - PA',
  55913256 => 'Belém - PA',
  55913257 => 'Belém - PA',
  55913258 => 'Belém - PA',
  55913259 => 'Belém - PA',
  55913262 => 'Belém - PA',
  55913263 => 'Belém - PA',
  55913264 => 'Belém - PA',
  55913265 => 'Ananindeua - PA',
  55913266 => 'Belém - PA',
  55913267 => 'Belém - PA',
  55913268 => 'Belém - PA',
  55913269 => 'Belém - PA',
  55913271 => 'Belém - PA',
  55913272 => 'Belém - PA',
  55913273 => 'Belém - PA',
  55913274 => 'Belém - PA',
  55913275 => 'Ananindeua - PA',
  55913276 => 'Belém - PA',
  55913277 => 'Belém - PA',
  55913278 => 'Belém - PA',
  55913279 => 'Belém - PA',
  55913281 => 'Belém - PA',
  55913283 => 'Belém - PA',
  55913284 => 'Ananindeua - PA',
  55913285 => 'Belém - PA',
  55913286 => 'Ananindeua - PA',
  55913287 => 'Ananindeua - PA',
  55913288 => 'Belém - PA',
  55913289 => 'Belém - PA',
  55913292 => 'Belém - PA',
  55913295 => 'Belém - PA',
  55913297 => 'Belém - PA',
  55913298 => 'Belém - PA',
  55913311 => 'Castanhal - PA',
  55913322 => 'Barcarena - PA',
  55913323 => 'Belém - PA',
  55913344 => 'Belém - PA',
  55913346 => 'Ananindeua - PA',
  55913366 => 'Belém - PA',
  55913411 => 'Capanema - PA',
  55913412 => 'Castanhal - PA',
  55913423 => 'Salinópolis - PA',
  55913424 => 'Redenção - PA',
  55913425 => 'Bragança - PA',
  55913429 => 'Viseu - PA',
  55913434 => 'Garrafão do Norte - PA',
  55913441 => 'Igarapé-Açu - PA',
  55913442 => 'Santa Maria do Pará - PA',
  55913443 => 'Irituia - PA',
  55913444 => 'Mãe do Rio - PA',
  55913445 => 'Santa Luzia do Pará - PA',
  55913446 => 'São Miguel do Guamá - PA',
  55913447 => 'Cachoeira do Piriá - PA',
  55913448 => 'Maracanã - PA',
  55913449 => 'São João de Pirabas - PA',
  55913456 => 'Benfica - PA',
  55913461 => 'Colares - PA',
  55913462 => 'Capanema - PA',
  55913464 => 'Atalaia - PA',
  55913466 => 'Marudá - PA',
  55913467 => 'Ourém - PA',
  55913468 => 'Capitão Poço - PA',
  55913469 => 'Nova Timboteua - PA',
  55913481 => 'Primavera - PA',
  55913482 => 'Augusto Corrêa - PA',
  55913483 => 'São Domingos do Capim - PA',
  55913484 => 'Santarém Novo - PA',
  55913485 => 'Tracuateua - PA',
  55913494 => 'Muaná - PA',
  55913521 => 'Paragominas - PA',
  55913528 => 'Novo Progresso - PA',
  55913544 => 'Oriximiná - PA',
  55913556 => 'Senador José Porfírio - PA',
  55913605 => 'Anajás - PA',
  55913606 => 'Bagre - PA',
  55913617 => 'Cotijuba - PA',
  55913621 => 'Caiçava - PA',
  55913633 => 'Curralinho - PA',
  55913636 => 'Limoeiro do Ajuru - PA',
  55913637 => 'Melgaço - PA',
  55913656 => 'Santa Bárbara do Pará - PA',
  55913658 => 'Santa Cruz do Arari - PA',
  55913661 => 'Oeiras do Pará - PA',
  55913662 => 'Terra Alta - PA',
  55913665 => 'Benevides - PA',
  55913692 => 'Gurupá - PA',
  55913694 => 'Anapu - PA',
  55913711 => 'Castanhal - PA',
  55913712 => 'Castanhal - PA',
  55913721 => 'Castanhal - PA',
  55913722 => 'Curuçá - PA',
  55913723 => 'Marapanim - PA',
  55913724 => 'Benevides - PA',
  55913725 => 'Apeú - PA',
  55913726 => 'Ulianópolis - PA',
  55913727 => 'Tomé-Açu - PA',
  55913728 => 'Concórdia do Pará - PA',
  55913729 => 'Paragominas - PA',
  55913731 => 'Vigia - PA',
  55913732 => 'Acará - PA',
  55913733 => 'Murucupi - PA',
  55913734 => 'Quatro Bocas - PA',
  55913738 => 'Km 12 - PA',
  55913739 => 'Paragominas - PA',
  55913741 => 'Soure - PA',
  55913744 => 'Santa Isabel do Pará - PA',
  55913746 => 'Bujaru - PA',
  55913751 => 'Abaetetuba - PA',
  55913752 => 'Tailândia - PA',
  55913753 => 'Barcarena - PA',
  55913754 => 'Barcarena - PA',
  55913755 => 'Igarapé-Miri - PA',
  55913756 => 'Moju - PA',
  55913758 => 'Cachoeira do Arari - PA',
  55913764 => 'São Sebastião da Boa Vista - PA',
  55913765 => 'Salvaterra - PA',
  55913767 => 'São Caetano de Odivelas - PA',
  55913771 => 'Mosqueiro - PA',
  55913772 => 'Mosqueiro - PA',
  55913774 => 'São Francisco do Pará - PA',
  55913775 => 'Santo Antônio do Tauá - PA',
  55913776 => 'Santa Bárbara do Pará - PA',
  55913777 => 'Ponta de Pedras - PA',
  55913781 => 'Cametá - PA',
  55913783 => 'Breves - PA',
  55913784 => 'Portel - PA',
  55913795 => 'Baião - PA',
  55913796 => 'Mocajuba - PA',
  55913798 => 'Pacajá - PA',
  55913802 => 'Mãe do Rio - PA',
  55913803 => 'Bonito - PA',
  55913809 => 'Inhangapi - PA',
  55913811 => 'Ipixuna do Pará - PA',
  55913812 => 'Magalhães Barata - PA',
  55913817 => 'Nova Esperança do Piriá - PA',
  55913821 => 'Peixe-Boi - PA',
  55913822 => 'Quatipuru - PA',
  55913823 => 'Americano - PA',
  55913829 => 'Tomé-Açú - PA',
  55913854 => 'Maracanã - PA',
  55913859 => 'Salinópolis - PA',
  55914003 => 'Belém - PA',
  55914005 => 'Belém - PA',
  55914006 => 'Belém - PA',
  55914104 => 'Ananindeua - PA',
  55914107 => 'Ananindeua - PA',
  5592 => 'Amazonas',
  55922101 => 'Manaus - AM',
  55922121 => 'Manaus - AM',
  55922123 => 'Manaus - AM',
  55922125 => 'Manaus - AM',
  55922127 => 'Manaus - AM',
  55922129 => 'Manaus - AM',
  55923012 => 'Manaus - AM',
  55923016 => 'Manaus - AM',
  55923018 => 'Manaus - AM',
  55923019 => 'Manaus - AM',
  55923020 => 'Manaus - AM',
  55923021 => 'Manaus - AM',
  55923028 => 'Manaus - AM',
  55923030 => 'Manaus - AM',
  55923071 => 'Manaus - AM',
  55923084 => 'Manaus - AM',
  55923131 => 'Manaus - AM',
  55923133 => 'Manaus - AM',
  55923184 => 'Manaus - AM',
  55923194 => 'Manaus - AM',
  55923198 => 'Manaus - AM',
  55923213 => 'Manaus - AM',
  55923215 => 'Manaus - AM',
  55923221 => 'Manaus - AM',
  55923223 => 'Manaus - AM',
  55923228 => 'Manaus - AM',
  55923231 => 'Manaus - AM',
  55923232 => 'Manaus - AM',
  55923233 => 'Manaus - AM',
  55923234 => 'Manaus - AM',
  55923235 => 'Manaus - AM',
  55923236 => 'Manaus - AM',
  55923238 => 'Manaus - AM',
  55923239 => 'Manaus - AM',
  55923245 => 'Manaus - AM',
  55923247 => 'Manaus - AM',
  55923249 => 'Manaus - AM',
  55923301 => 'Manaus - AM',
  55923306 => 'Manaus - AM',
  55923311 => 'Cacau Pirêra - AM',
  55923312 => 'Balbina - AM',
  55923317 => 'Autazes - AM',
  55923318 => 'Nova Olinda do Norte - AM',
  55923323 => 'Pitinga - AM',
  55923324 => 'Presidente Figueiredo - AM',
  55923328 => 'Rio Preto da Eva - AM',
  55923361 => 'Manacapuru - AM',
  55923362 => 'Careiro - AM',
  55923363 => 'Manaquiri - AM',
  55923364 => 'Caapiranga - AM',
  55923365 => 'Novo Airão - AM',
  55923367 => 'Iranduba - AM',
  55923369 => 'Careiro da Várzea - AM',
  55923427 => 'Juruá - AM',
  55923491 => 'Carauari - AM',
  55923512 => 'Borba - AM',
  55923521 => 'Itacoatiara - AM',
  55923524 => 'Urucurituba - AM',
  55923528 => 'Silves - AM',
  55923531 => 'Barreirinha - AM',
  55923533 => 'Parintins - AM',
  55923534 => 'Nhamundá - AM',
  55923542 => 'Maués - AM',
  55923545 => 'Boa Vista do Ramos - AM',
  55923571 => 'Urucará - AM',
  55923572 => 'São Sebastião do Uatumã - AM',
  55923575 => 'Itapiranga - AM',
  55923581 => 'Manaus - AM',
  55923582 => 'Manaus - AM',
  55923584 => 'Manaus - AM',
  55923611 => 'Manaus - AM',
  55923612 => 'Manaus - AM',
  55923613 => 'Manaus - AM',
  55923614 => 'Manaus - AM',
  55923615 => 'Manaus - AM',
  55923616 => 'Manaus - AM',
  55923617 => 'Manaus - AM',
  55923618 => 'Manaus - AM',
  55923621 => 'Manaus - AM',
  55923622 => 'Manaus - AM',
  55923623 => 'Manaus - AM',
  55923624 => 'Manaus - AM',
  55923625 => 'Manaus - AM',
  55923627 => 'Manaus - AM',
  55923629 => 'Manaus - AM',
  55923633 => 'Manaus - AM',
  55923634 => 'Manaus - AM',
  55923635 => 'Manaus - AM',
  55923636 => 'Manaus - AM',
  55923637 => 'Manaus - AM',
  55923639 => 'Manaus - AM',
  55923641 => 'Manaus - AM',
  55923642 => 'Manaus - AM',
  55923643 => 'Manaus - AM',
  55923645 => 'Manaus - AM',
  55923646 => 'Manaus - AM',
  55923648 => 'Manaus - AM',
  55923649 => 'Manaus - AM',
  55923651 => 'Manaus - AM',
  55923652 => 'Manaus - AM',
  55923656 => 'Manaus - AM',
  55923657 => 'Manaus - AM',
  55923658 => 'Manaus - AM',
  55923659 => 'Manaus - AM',
  55923663 => 'Manaus - AM',
  55923664 => 'Manaus - AM',
  55923667 => 'Manaus - AM',
  55923671 => 'Manaus - AM',
  55923672 => 'Manaus - AM',
  55923673 => 'Manaus - AM',
  55923675 => 'Manaus - AM',
  55923681 => 'Manaus - AM',
  55923682 => 'Manaus - AM',
  55923877 => 'Manaus - AM',
  55924002 => 'Manaus - AM',
  55924004 => 'Manaus - AM',
  55924009 => 'Manaus - AM',
  5593 => 'Pará',
  55932101 => 'Santarém - PA',
  55933017 => 'Santarém - PA',
  55933062 => 'Santarém - PA',
  55933063 => 'Santarém - PA',
  55933064 => 'Santarém - PA',
  55933067 => 'Santarém - PA',
  55933222 => 'Santarém - PA',
  55933502 => 'Castelo dos Sonhos - PA',
  55933505 => 'Aveiro - PA',
  55933512 => 'Santarém - PA',
  55933514 => 'Brasil Novo - PA',
  55933515 => 'Altamira - PA',
  55933517 => 'Creporizão - PA',
  55933518 => 'Itaituba - PA',
  55933521 => 'Vila Residencial Belo Monte - PA',
  55933522 => 'Santarém - PA',
  55933523 => 'Santarém - PA',
  55933524 => 'Santarém - PA',
  55933526 => 'Alenquer - PA',
  55933527 => 'Santarém - PA',
  55933528 => 'Novo Progresso - PA',
  55933531 => 'Medicilândia - PA',
  55933532 => 'Uruará - PA',
  55933533 => 'Monte Alegre - PA',
  55933534 => 'Prainha - PA',
  55933536 => 'Juruti - PA',
  55933537 => 'Mujuí dos Campos - PA',
  55933538 => 'Terra Santa - PA',
  55933541 => 'Pará',
  55933542 => 'Jacareacanga - PA',
  55933543 => 'Rurópolis - PA',
  55933544 => 'Oriximiná - PA',
  55933547 => 'Óbidos - PA',
  55933549 => 'Porto Trombetas - PA',
  55933552 => 'Placas - PA',
  55933557 => 'Faro - PA',
  55933558 => 'Belterra - PA',
  55933559 => 'Trairão - PA',
  55933563 => 'Curuá - PA',
  55933582 => 'Santa Maria do Uruará - PA',
  55933589 => 'Santarém - PA',
  55933593 => 'Altamira - PA',
  55933596 => 'São José - PA',
  55933597 => 'Tabocal - PA',
  55933598 => 'Jamanchizinho - PA',
  55933603 => 'Pará',
  55933735 => 'Monte Dourado - PA',
  55933736 => 'Munguba - PA',
  55933737 => 'Almeirim - PA',
  55933793 => 'Porto de Moz - PA',
  5594 => 'Pará',
  55942101 => 'Marabá - PA',
  55942103 => 'Marabá - PA',
  55943012 => 'Marabá - PA',
  55943013 => 'Parauapebas - PA',
  55943222 => 'Marabá - PA',
  55943301 => 'Maracajá - PA',
  55943305 => 'Bannach - PA',
  55943309 => 'Cumaru do Norte - PA',
  55943311 => 'Parauapebas - PA',
  55943312 => 'Marabá - PA',
  55943314 => 'Serra Pelada - PA',
  55943315 => 'PA 275 - PA',
  55943319 => 'Santa Maria das Barreiras - PA',
  55943321 => 'Marabá - PA',
  55943322 => 'Marabá - PA',
  55943323 => 'Marabá - PA',
  55943324 => 'Marabá - PA',
  55943326 => 'Rondon do Pará - PA',
  55943327 => 'Núcleo Carajás - PA',
  55943328 => 'Núcleo Carajás - PA',
  55943331 => 'São Geraldo do Araguaia - PA',
  55943332 => 'São Domingos do Araguaia - PA',
  55943333 => 'Itupiranga - PA',
  55943335 => 'Itinga do Maranhão - PA',
  55943337 => 'Brejo Grande do Araguaia - PA',
  55943341 => 'Bom Jesus do Tocantins - PA',
  55943342 => 'Abel Figueiredo - PA',
  55943344 => 'Nova Ipixuna - PA',
  55943345 => 'Jacundá - PA',
  55943346 => 'Parauapebas - PA',
  55943347 => 'Eldorado dos Carajás - PA',
  55943348 => 'Curionópolis - PA',
  55943351 => 'Palestina do Pará - PA',
  55943352 => 'Parauapebas - PA',
  55943353 => 'Vila Cruzeiro do Sul - PA',
  55943355 => 'Marabá - PA',
  55943356 => 'Parauapebas - PA',
  55943358 => 'Canaã dos Carajás - PA',
  55943364 => 'Vila Mandii - PA',
  55943365 => 'Vila Taboca - PA',
  55943366 => 'Vila Novo Paraíso - PA',
  55943369 => 'Parauapebas - PA',
  55943379 => 'São João do Araguaia - PA',
  55943382 => 'Sapucaia - PA',
  55943385 => 'Vila Santa Fé - PA',
  55943386 => 'Lindoeste - PA',
  55943392 => 'Canaã dos Carajás - PA',
  55943421 => 'Conceição do Araguaia - PA',
  55943422 => 'Piçarras - PA',
  55943424 => 'Redenção - PA',
  55943426 => 'Xinguara - PA',
  55943427 => 'Água Azul do Norte - PA',
  55943428 => 'Rio Maria - PA',
  55943431 => 'Santana do Araguaia - PA',
  55943432 => 'Floresta do Araguaia - PA',
  55943433 => 'Tucumã - PA',
  55943434 => 'Ourilândia do Norte - PA',
  55943435 => 'São Félix do Xingu - PA',
  55943491 => 'Redenção - PA',
  55943778 => 'Vila Residencial de Tucuruí - PA',
  55943779 => 'Goianésia do Pará - PA',
  55943785 => 'Novo Repartimento - PA',
  55943786 => 'Breu Branco - PA',
  55943787 => 'Tucuruí - PA',
  5595 => 'Roraima',
  55952121 => 'Boa Vista - RR',
  55953084 => 'Boa Vista - RR',
  55953086 => 'Boa Vista - RR',
  55953194 => 'Boa Vista - RR',
  55953198 => 'Boa Vista - RR',
  55953212 => 'Boa Vista - RR',
  55953224 => 'Boa Vista - RR',
  55953235 => 'São João da Baliza - RR',
  55953236 => 'Caroebe - RR',
  55953238 => 'Rorainópolis - RR',
  55953262 => 'Normandia - RR',
  55953263 => 'Alto Alegre - RR',
  55953532 => 'Caracaraí - RR',
  55953537 => 'São Luiz - RR',
  55953539 => 'Nova Colina - RR',
  55953542 => 'Mucajaí - RR',
  55953543 => 'Iracema - RR',
  55953552 => 'Bonfim - RR',
  55953553 => 'Cantá - RR',
  55953591 => 'Uiramutã - RR',
  55953592 => 'Pacaraima - RR',
  55953593 => 'Amajari - RR',
  55953621 => 'Boa Vista - RR',
  55953623 => 'Boa Vista - RR',
  55953624 => 'Boa Vista - RR',
  55953625 => 'Boa Vista - RR',
  55953626 => 'Boa Vista - RR',
  55954009 => 'Boa Vista - RR',
  55954400 => 'Caracaraí - RR',
  5596 => 'Amapá',
  55962101 => 'Macapá - AP',
  55963014 => 'Macapá - AP',
  55963081 => 'Macapá - AP',
  55963083 => 'Macapá - AP',
  55963084 => 'Macapá - AP',
  55963116 => 'Santana - AP',
  55963117 => 'Macapá - AP',
  55963118 => 'Macapá - AP',
  55963198 => 'Macapá - AP',
  55963212 => 'Macapá - AP',
  55963214 => 'Macapá - AP',
  55963217 => 'Macapá - AP',
  55963222 => 'Macapá - AP',
  55963223 => 'Macapá - AP',
  55963224 => 'Macapá - AP',
  55963225 => 'Macapá - AP',
  55963227 => 'Macapá - AP',
  55963229 => 'Macapá - AP',
  55963234 => 'Porto Grande - AP',
  55963242 => 'Macapá - AP',
  55963243 => 'Macapá - AP',
  55963244 => 'Macapá - AP',
  55963251 => 'Macapá - AP',
  55963261 => 'Macapá - AP',
  55963271 => 'Mazagão - AP',
  55963281 => 'Santana - AP',
  55963282 => 'Santana - AP',
  55963283 => 'Santana - AP',
  55963312 => 'Macapá - AP',
  55963314 => 'Santana - AP',
  55963321 => 'Serra do Navio - AP',
  55963322 => 'Pedra Branca do Amaparí - AP',
  55963323 => 'Macapá - AP',
  55963324 => 'Itaubal - AP',
  55963325 => 'Cutias - AP',
  55963326 => 'Ferreira Gomes - AP',
  55963332 => 'Macapá - AP',
  55963421 => 'Amapá - AP',
  55963422 => 'Tartarugalzinho - AP',
  55963423 => 'Calçoene - AP',
  55963424 => 'Pracuúba - AP',
  55963426 => 'Lourenço - AP',
  55963521 => 'Oiapoque - AP',
  55963621 => 'Laranjal do Jari - AP',
  55963622 => 'Vitória do Jari - AP',
  55963689 => 'Afuá - PA',
  55963697 => 'Chaves - PA',
  55964009 => 'Macapá - AP',
  55964141 => 'Macapá - AP',
  55964400 => 'Macapá - AP',
  5597 => 'Amazonas',
  55973321 => 'Barcelos - AM',
  55973331 => 'Lábrea - AM',
  55973334 => 'Canutama - AM',
  55973343 => 'Tefé - AM',
  55973345 => 'Alvarães - AM',
  55973346 => 'Uarini - AM',
  55973351 => 'Beruri - AM',
  55973352 => 'Anori - AM',
  55973353 => 'Codajás - AM',
  55973356 => 'Anamã - AM',
  55973373 => 'Humaitá - AM',
  55973379 => 'Novo Aripuanã - AM',
  55973385 => 'Manicoré - AM',
  55973389 => 'Apuí - AM',
  55973391 => 'Tapauá - AM',
  55973412 => 'Tabatinga - AM',
  55973415 => 'Benjamin Constant - AM',
  55973417 => 'Atalaia do Norte - AM',
  55973423 => 'Fonte Boa - AM',
  55973425 => 'Jutaí - AM',
  55973426 => 'Japurá - AM',
  55973427 => 'Juruá - AM',
  55973428 => 'Maraã - AM',
  55973431 => 'São Paulo de Olivença - AM',
  55973441 => 'Santa Isabel do Rio Negro - AM',
  55973451 => 'Boca do Acre - AM',
  55973453 => 'Boca do Acre - AM',
  55973458 => 'Pauini - AM',
  55973461 => 'Santo Antônio do Içá - AM',
  55973463 => 'Amaturá - AM',
  55973464 => 'Tonantins - AM',
  55973471 => 'São Gabriel da Cachoeira - AM',
  55973473 => 'São Gabriel da Cachoeira - AM',
  55973481 => 'Eirunepé - AM',
  55973482 => 'Ipixuna - AM',
  55973483 => 'Envira - AM',
  55973484 => 'Itamarati - AM',
  55973485 => 'Guajará - AM',
  55973491 => 'Carauari - AM',
  55973561 => 'Coari - AM',
  5598 => 'Maranhão',
  55982016 => 'São Luís - MA',
  55982106 => 'São Luís - MA',
  55982109 => 'São Luís - MA',
  55983004 => 'São Luís - MA',
  55983011 => 'São Luís - MA',
  55983012 => 'São Luís - MA',
  55983013 => 'São Luís - MA',
  55983014 => 'São Luís - MA',
  55983015 => 'São Luís - MA',
  55983081 => 'São Luís - MA',
  55983082 => 'São Luís - MA',
  55983083 => 'São Luís - MA',
  55983084 => 'São Luís - MA',
  55983087 => 'São Luís - MA',
  55983088 => 'São Luís - MA',
  55983089 => 'São Luís - MA',
  55983181 => 'São Luís - MA',
  55983182 => 'São Luís - MA',
  55983194 => 'São Luís - MA',
  55983198 => 'São Luís - MA',
  55983212 => 'São Luís - MA',
  55983213 => 'São Luís - MA',
  55983214 => 'São Luís - MA',
  55983217 => 'São Luís - MA',
  55983218 => 'São Luís - MA',
  55983221 => 'São Luís - MA',
  55983222 => 'São Luís - MA',
  55983223 => 'São Luís - MA',
  55983224 => 'São José de Ribamar - MA',
  55983226 => 'São Luís - MA',
  55983227 => 'São Luís - MA',
  55983228 => 'São Luís - MA',
  55983229 => 'Raposa - MA',
  55983231 => 'São Luís - MA',
  55983232 => 'São Luís - MA',
  55983233 => 'São Luís - MA',
  55983234 => 'São Luís - MA',
  55983235 => 'São Luís - MA',
  55983236 => 'São Luís - MA',
  55983237 => 'São Luís - MA',
  55983241 => 'São Luís - MA',
  55983242 => 'São Luís - MA',
  55983243 => 'São Luís - MA',
  55983244 => 'São Luís - MA',
  55983245 => 'São Luís - MA',
  55983246 => 'São Luís - MA',
  55983247 => 'São Luís - MA',
  55983248 => 'São Luís - MA',
  55983249 => 'São Luís - MA',
  55983251 => 'São Luís - MA',
  55983252 => 'São Luís - MA',
  55983253 => 'São Luís - MA',
  55983254 => 'São Luís - MA',
  55983255 => 'São Luís - MA',
  55983256 => 'São Luís - MA',
  55983257 => 'São Luís - MA',
  55983258 => 'São Luís - MA',
  55983259 => 'São Luís - MA',
  55983262 => 'São Luís - MA',
  55983264 => 'São Luís - MA',
  55983268 => 'São Luís - MA',
  55983269 => 'São Luís - MA',
  55983271 => 'São Luís - MA',
  55983272 => 'São Luís - MA',
  55983273 => 'São Luís - MA',
  55983274 => 'Paço do Lumiar - MA',
  55983276 => 'São Luís - MA',
  55983278 => 'São Luís - MA',
  55983302 => 'São Luís - MA',
  55983304 => 'São Luís - MA',
  55983311 => 'São Luís - MA',
  55983312 => 'São Luís - MA',
  55983313 => 'São Luís - MA',
  55983322 => 'Boa Vista do Gurupi - MA',
  55983323 => 'Centro do Guilherme - MA',
  55983324 => 'Centro Novo do Maranhão - MA',
  55983325 => 'Maranhãozinho - MA',
  55983326 => 'Presidente Médici - MA',
  55983337 => 'Alcântara - MA',
  55983345 => 'Rosário - MA',
  55983346 => 'Bacabeira - MA',
  55983349 => 'Barreirinhas - MA',
  55983351 => 'Viana - MA',
  55983352 => 'Vitória do Mearim - MA',
  55983353 => 'Apicum-Açu - MA',
  55983355 => 'Cajapió - MA',
  55983357 => 'Matinha - MA',
  55983358 => 'Penalva - MA',
  55983359 => 'São João Batista - MA',
  55983361 => 'Axixá - MA',
  55983362 => 'Icatu - MA',
  55983363 => 'Morros - MA',
  55983367 => 'Humberto de Campos - MA',
  55983368 => 'Primeira Cruz - MA',
  55983369 => 'Santo Amaro do Maranhão - MA',
  55983371 => 'Governador Nunes Freire - MA',
  55983372 => 'Santo Antônio dos Lopes - MA',
  55983373 => 'Maracaçumé - MA',
  55983374 => 'Santa Luzia do Paruá - MA',
  55983377 => 'Nova Olinda do Maranhão - MA',
  55983378 => 'Pedro do Rosário - MA',
  55983381 => 'Pinheiro - MA',
  55983382 => 'Santa Helena - MA',
  55983383 => 'São Bento - MA',
  55983384 => 'Presidente Sarney - MA',
  55983385 => 'Bequimão - MA',
  55983386 => 'Guimarães - MA',
  55983387 => 'Palmeirândia - MA',
  55983388 => 'Peri Mirim - MA',
  55983391 => 'Cururupu - MA',
  55983392 => 'Bacuri - MA',
  55983393 => 'Luís Domingues - MA',
  55983394 => 'Carutapera - MA',
  55983395 => 'Godofredo Viana - MA',
  55983396 => 'Cândido Mendes - MA',
  55983397 => 'Turiaçu - MA',
  55983398 => 'Cedral - MA',
  55983399 => 'Mirinzal - MA',
  55983451 => 'Santa Rita - MA',
  55983453 => 'Arari - MA',
  55983454 => 'Anajatuba - MA',
  55983455 => 'Matões do Norte - MA',
  55983461 => 'Vargem Grande - MA',
  55983462 => 'Cantanhede - MA',
  55983463 => 'Itapecuru Mirim - MA',
  55983464 => 'Miranda do Norte - MA',
  55983465 => 'Nina Rodrigues - MA',
  55983466 => 'Pirapemas - MA',
  55983468 => 'São Benedito do Rio Preto - MA',
  55983469 => 'Urbano Santos - MA',
  55983471 => 'Chapadinha - MA',
  55983472 => 'Brejo - MA',
  55983473 => 'Coelho Neto - MA',
  55983474 => 'Duque Bacelar - MA',
  55983475 => 'Mata Roma - MA',
  55983476 => 'Santa Quitéria do Maranhão - MA',
  55983477 => 'São Bernardo - MA',
  55983478 => 'Araioses - MA',
  55983479 => 'Tutóia - MA',
  55983481 => 'Anapurus - MA',
  55983482 => 'Buriti - MA',
  55983483 => 'Magalhães de Almeida - MA',
  55983484 => 'Afonso Cunha - MA',
  55983485 => 'Água Doce do Maranhão - MA',
  55983487 => 'Paulino Neves - MA',
  55983488 => 'Santana do Maranhão - MA',
  55983521 => 'Caxias - MA',
  55983523 => 'Imperatriz - MA',
  55983524 => 'Imperatriz - MA',
  55983525 => 'Imperatriz - MA',
  55983538 => 'Açailândia - MA',
  55983621 => 'Bacabal - MA',
  55983622 => 'Bacabal - MA',
  55983651 => 'Araguanã - MA',
  55983652 => 'Bom Jesus das Selvas - MA',
  55983653 => 'Santa Inês - MA',
  55983654 => 'Santa Luzia - MA',
  55983655 => 'Zé Doca - MA',
  55983656 => 'Governador Newton Bello - MA',
  55983658 => 'Alto Alegre do Pindaré - MA',
  55983661 => 'Codó - MA',
  55983664 => 'Buriticupu - MA',
  55983672 => 'Bom Jardim - MA',
  55983673 => 'Brejo de Areia - MA',
  55983678 => 'Pindare Mirim - MA',
  55983681 => 'Santa Inês - MA',
  55983683 => 'Satubinha - MA',
  55983689 => 'Zé Doca - MA',
  55983878 => 'São Luís - MA',
  55984002 => 'São Luís - MA',
  55984009 => 'São Luís - MA',
  55984141 => 'São Luís - MA',
  5599 => 'Maranhão',
  55992101 => 'Imperatriz - MA',
  55993014 => 'Imperatriz - MA',
  55993015 => 'Imperatriz - MA',
  55993017 => 'Imperatriz - MA',
  55993072 => 'Imperatriz - MA',
  55993073 => 'Imperatriz - MA',
  55993075 => 'Imperatriz - MA',
  55993078 => 'Caxias - MA',
  55993117 => 'Timon - MA',
  55993118 => 'Timon - MA',
  55993212 => 'Timon - MA',
  55993221 => 'Imperatriz - MA',
  55993263 => 'Imperatriz - MA',
  55993311 => 'Açailândia - MA',
  55993317 => 'Timon - MA',
  55993321 => 'Imperatriz - MA',
  55993326 => 'Timon - MA',
  55993421 => 'Caxias - MA',
  55993422 => 'Caxias - MA',
  55993425 => 'Jenipapo dos Vieiras - MA',
  55993427 => 'Barra do Corda - MA',
  55993492 => 'Lagoa do Mato - MA',
  55993521 => 'Caxias - MA',
  55993522 => 'Tuntum - MA',
  55993523 => 'Imperatriz - MA',
  55993524 => 'Imperatriz - MA',
  55993525 => 'Imperatriz - MA',
  55993526 => 'Imperatriz - MA',
  55993527 => 'Imperatriz - MA',
  55993528 => 'Imperatriz - MA',
  55993529 => 'Imperatriz - MA',
  55993531 => 'Estreito - MA',
  55993533 => 'Buritirana - MA',
  55993534 => 'Davinópolis - MA',
  55993535 => 'Pequiá - MA',
  55993536 => 'Governador Edison Lobão - MA',
  55993537 => 'Senador La Roque - MA',
  55993538 => 'Açailândia - MA',
  55993539 => 'Vila Nova dos Martírios - MA',
  55993541 => 'Balsas - MA',
  55993542 => 'Balsas - MA',
  55993543 => 'Tasso Fragoso - MA',
  55993544 => 'Loreto - MA',
  55993545 => 'São Domingos do Azeitão - MA',
  55993547 => 'São Raimundo das Mangabeiras - MA',
  55993551 => 'São João dos Patos - MA',
  55993552 => 'Colinas - MA',
  55993553 => 'Sucupira do Riachão - MA',
  55993554 => 'Paraibano - MA',
  55993555 => 'Pastos Bons - MA',
  55993556 => 'Mirador - MA',
  55993557 => 'Nova Iorque - MA',
  55993558 => 'Passagem Franca - MA',
  55993559 => 'Sucupira do Norte - MA',
  55993561 => 'Governador Luiz Rocha - MA',
  55993562 => 'Gonçalves Dias - MA',
  55993563 => 'Aldeias Altas - MA',
  55993564 => 'Governador Eugênio Barros - MA',
  55993565 => 'Formosa da Serra Negra - MA',
  55993567 => 'São João do Soter - MA',
  55993569 => 'Senador Alexandre Costa - MA',
  55993571 => 'Porto Franco - MA',
  55993572 => 'Buriti Bravo - MA',
  55993574 => 'Fortuna - MA',
  55993575 => 'Graça Aranha - MA',
  55993576 => 'Matões - MA',
  55993577 => 'Parnarama - MA',
  55993578 => 'São Domingos do Maranhão - MA',
  55993582 => 'Imperatriz - MA',
  55993584 => 'Lajeado Novo - MA',
  55993586 => 'Ribamar Fiquene - MA',
  55993587 => 'São Francisco do Brejão - MA',
  55993592 => 'Açailândia - MA',
  55993601 => 'Feira Nova do Maranhão - MA',
  55993602 => 'Nova Colinas - MA',
  55993604 => 'São Pedro dos Crentes - MA',
  55993613 => 'Grajaú - MA',
  55993614 => 'Itaipava do Grajaú - MA',
  55993621 => 'Bacabal - MA',
  55993622 => 'Bacabal - MA',
  55993623 => 'Bom Lugar - MA',
  55993626 => 'Pedreiras - MA',
  55993627 => 'Bacabal - MA',
  55993631 => 'São Luís Gonzaga do Maranhão - MA',
  55993632 => 'Lago dos Rodrigues - MA',
  55993633 => 'Lagoa Grande do Maranhão - MA',
  55993634 => 'Lago do Junco - MA',
  55993635 => 'Lago Verde - MA',
  55993636 => 'Poção de Pedras - MA',
  55993637 => 'Joselândia - MA',
  55993638 => 'Alto Alegre do Maranhão - MA',
  55993639 => 'São Mateus do Maranhão - MA',
  55993641 => 'Coroatá - MA',
  55993642 => 'Pedreiras - MA',
  55993643 => 'Barra do Corda - MA',
  55993644 => 'Lago da Pedra - MA',
  55993645 => 'Esperantinópolis - MA',
  55993646 => 'Lima Campos - MA',
  55993647 => 'Igarapé Grande - MA',
  55993648 => 'Bernardo do Mearim - MA',
  55993649 => 'Peritoró - MA',
  55993661 => 'Codó - MA',
  55993662 => 'Dom Pedro - MA',
  55993663 => 'Presidente Dutra - MA',
  55993665 => 'Capinzal do Norte - MA',
  55993666 => 'Santo Antônio dos Lopes - MA',
  55993667 => 'Governador Archer - MA',
  55993668 => 'Timbiras - MA',
  55994102 => 'Imperatriz - MA',
);
