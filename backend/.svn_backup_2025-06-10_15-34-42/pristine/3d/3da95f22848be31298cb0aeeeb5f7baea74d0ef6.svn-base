<?php

set_include_path(dirname(__FILE__) . '/../include/');
	require_once(str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php');
	require_once('documents.inc.php');
	
	die('ATTENTION à ce script');

	// Suppression de toutes les urls liées au catégories
	$count = rew_rewritemap_del_by_objects( CLS_TYPE_DOCUMENT );
	print 'Nombre d\'urls supprimées : '.$count."\n\n";

	// Recréation de toutes les urls catégories
	$types = doc_types_get();
	if( $types ){
		while( $t = mysql_fetch_array( $types ) ){
		
			// supprime l'url alias s'il existe
			if( $t['url_alias'] ){
				
				rew_rewritemap_del( $t['url_alias'] );
				rew_rewritemap_del( '', $t['url_alias'] );
								
			}
			
			// recréer l'url
			$new_url = doc_types_url_alias_add( $t['id'] );
			
			print $new_url."\n";
		}
	}

