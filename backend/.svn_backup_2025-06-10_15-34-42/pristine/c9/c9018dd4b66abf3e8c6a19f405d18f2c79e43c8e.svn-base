<?php

	/** \file update-ord-status-cheque.php
	 *
	 * 	Ce script permet de mettre une ou plusieurs commande à "En attente de traitement" lorsque celle-ci a été payée par chèque.
	 *	Il faut que le système de paiement par chèque sous contrôle soit activé (sys_cheque_actived) et que le nombre de jours
	 *	soit supérieur à zéro (sys_cheque_days).
	 *
	 */
	set_include_path(dirname(__FILE__) . '/../include/');
	require_once( 'db.inc.php' );
	require_once( 'define.inc.php' );
	require_once( 'cfg.variables.inc.php' );
	require_once( 'comparators.inc.php' );
	require_once( 'strings.inc.php' );

	unset($config);

	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_tenants();
	if( !is_array($configs) || !sizeof($configs) ){
		return false;
	}

	// Traitement
	foreach( $configs as $config ){
		if( !isset($config['sys_cheque_actived'], $config['sys_cheque_days']) || !($config['sys_cheque_actived'] && is_numeric($config['sys_cheque_days']) && $config['sys_cheque_days']>0) ){
			continue;
		}

		$ord_error = array();

		// récupère la date d'aujourd'hui avec 7 jours de passés
		$date = date( 'Y-m-d', strtotime('-'.($config['sys_cheque_days']+1).' days') );

		// récupère les commandes
		$rord = ord_orders_get( 0, 0, 25 );

		if( $rord ){
			while( $ord = ria_mysql_fetch_array($rord) ){
				$date_upd = fld_object_values_get( $ord['id'], _FLD_ORD_UPD_STATUT );
				if( trim($date_upd) && $date_upd==$date ){
					if( !ord_orders_update_status($ord['id'], 4) ){
						$ord_error[] = $ord['id'];
					}
				}
			}
		}

		if( is_array($ord_error) && sizeof($ord_error) ){
			mail( '<EMAIL>', '['.$config['site_name'].'] Erreurs commandes chèque', print_r($ord_error, true) );
		}
	}
