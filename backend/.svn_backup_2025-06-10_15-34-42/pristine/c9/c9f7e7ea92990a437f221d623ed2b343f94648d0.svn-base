# RuleLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**self** | [**\Swagger\Client\Model\LinksGetRuleLink**](LinksGetRuleLink.md) |  | 
**update** | [**\Swagger\Client\Model\LinksUpdateRuleLink**](LinksUpdateRuleLink.md) |  | [optional] 
**delete** | [**\Swagger\Client\Model\LinksDeleteRuleLink**](LinksDeleteRuleLink.md) |  | [optional] 
**moveup** | [**\Swagger\Client\Model\LinksMoveUpRuleLink**](LinksMoveUpRuleLink.md) |  | [optional] 
**movedown** | [**\Swagger\Client\Model\LinksMoveDownRuleLink**](LinksMoveDownRuleLink.md) |  | [optional] 
**enable** | [**\Swagger\Client\Model\LinksEnableRuleLink**](LinksEnableRuleLink.md) |  | [optional] 
**disable** | [**\Swagger\Client\Model\LinksDisableRuleLink**](LinksDisableRuleLink.md) |  | [optional] 
**run** | [**\Swagger\Client\Model\LinksRunRuleLink**](LinksRunRuleLink.md) |  | [optional] 
**report_filter** | [**\Swagger\Client\Model\LinksGetReportFilterLink**](LinksGetReportFilterLink.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


