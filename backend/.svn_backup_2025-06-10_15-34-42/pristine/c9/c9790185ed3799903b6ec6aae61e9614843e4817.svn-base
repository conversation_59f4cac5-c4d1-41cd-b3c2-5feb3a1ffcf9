<?php
/**
 * \defgroup api-stocks-deposits-get Dépôts de stockage
 * \ingroup stats
 * @{
 * \page api-stocks-deposits-get Chargement
 *
 * Cet appel retourne les informations suivantes sur les dépôts de stockage.
 * 
 *		\code
 *			GET /stocks/deposits/
 *		\endcode
 *
 * @param int $id Facultatif, Identifiant de dépôts de stockage sur lesquelles filtrer le résultat
 *
 * @return json Un résultat au format Json contenant les clés suivantes :
 *	\code{.json}
 *		{
 *			"id" : identifiant du dépôt
 *			"name" : nom du dépôt
 *			"is_main" : true s'il s'agit du dépôt principal
 *			"address {
 *				"address1" : première partie de l'adresse du dépôt
 *				"address2" : seconde partie de l'adresse
 *				"zipcode" : code postal du dépôt
 *				"city" : ville du dépôt
 *				"country" : pays du dépôt
 *						}
 *			"phone" : numéro de téléphone du dépôt
 *			"fax" : numéro de fax du dépôt
 *			"email" : adresse email du dépôt
 *			"is_sync" : si 1, dépot synchronisé si 0, dépot présent uniquement sur le site
 *			"desc" : description du dépôt
 *			"date_modified" : date de dernière modification
 *			"date_modified_en" : date de dernière modification au format EN
 *			"date_created" : date de création du dépôt (format EN)
 *			"ref" : référence du dépôt
 *			"str_id" : identifiant du magasin
 *		}
 *	\endcode
 * @}
*/

require_once('http.inc.php');
require_once('scm/stats.inc.php');

if( $method=='get' ){

	// Le résultat est mis en cache pour 15 minutes
	http_cache_control( 900 );

	// Filtres sur la période
	/*if(
		!isset($_GET['dateStart']) || !trim($_GET['dateStart']) || !isdate($_GET['dateStart'])
		|| !isset($_GET['dateStop']) || !trim($_GET['dateStop']) || !isdate($_GET['dateStop'])
	){
		throw new Exception("Les dates de début et de fin sont obligatoires (dateStart et dateStop)");
	}*/

	// Filtre sur le dépôt
	$dps_id = null;
	if( isset($_GET['id']) && is_numeric($_GET['id']) ){
		$dps_id = $_GET['id'];
	}

	// Charge la liste des dépôts de stockage et la transforme en tableau associatif
	$deposits = prd_deposits_get( $dps_id );
	while( $dps = ria_mysql_fetch_assoc($deposits) ){
		$dps['address'] = [
			'address1' => $dps['address1'],
			'address2' => $dps['address2'],
			'zipcode' => $dps['zipcode'],
			'city' => $dps['city'],
			'country' => $dps['country'],
		];
		$content[] = $dps;
	}

	// Place le flag de succès / échec
	$result = is_array($content) && $content['count']!==false;

}
