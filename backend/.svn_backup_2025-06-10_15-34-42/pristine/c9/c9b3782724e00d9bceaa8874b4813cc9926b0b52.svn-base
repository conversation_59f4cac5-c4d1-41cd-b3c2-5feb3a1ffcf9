<?php
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_MODEL');

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	unset($error);

	// Modification de la méthode de tri
	if( isset($_POST['orderby'],$_POST['order']) ){
		fld_models_update_order( $_POST['order'] );
		header('Location: index.php');
		exit;
	}

	// Déplacement vers le haut
	if( isset($_GET['up']) ){
		fld_models_move_up( $_GET['up'] );
		header('Location: index.php');
		exit;
	}

	// Déplacement vers le bas
	if( isset($_GET['dw']) ){
		fld_models_move_down( $_GET['dw'] );
		header('Location: index.php');
		exit;
	}

	// Suppression dans la liste
	if( isset($_POST['del']) && isset($_POST['mdl']) ){
		foreach( $_POST['mdl'] as $z )
			if( !fld_models_del($z) ){
				$count = fld_object_models_get_count(0, $z);
				if( $count>0 )
					$error = str_replace("#param[nom_modele]#", fld_models_get_name($z), str_replace("#param[nb_utilisation]#", ria_number_format($count), _("Le modèle #param[nom_modele]# est utilisé par #param[nb_utilisation]# produit(s).\nVeuillez détacher le modèle de ces produits avant de le supprimer.")));
				else
					$error = _("Une erreur inattendue s'est produite lors de la suppression d'un des modèles.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				break;
			}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Modèles de saisie') . ' - ' . _('Structure des données') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	$ordered = fld_models_get_order();
	$classes = fld_classes_get(0, false, true, true, null, true, true);
	$models = fld_models_get(0, 0, null);
	$models_count = ria_mysql_num_rows($models);

?>
	<h2><?php print _('Modèles de saisie'); ?> (<?php print ria_number_format($models_count) ?>)</h2>
	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	?>

	<form action="index.php" method="post">
	<table id="models" class="checklist">
		<thead>
			<tr>
				<th id="select">
					<input type="checkbox" class="checkbox" onclick="checkAllClick(this);">
				</th>
				<th id="name"><?php print _('Nom du modèle'); ?></th>
				<th id="fields" class="align-right"><?php print _('Champs'); ?></th>
				<th id="objects" class="align-right"><?php print _('Utilisation'); ?></th>
				<?php if( $ordered ){ ?>
					<th id="pos"><?php print _('Position'); ?></th>
				<?php } ?>
			</tr>
		</thead>
		<tbody>
			<?php
				if( $classes ){

					$models_count = 0; // Compte le nombre de modèles pour déterminer si le message "Aucun modèle" doit s'afficher ou non
					while( $cls = ria_mysql_fetch_array($classes) ){

						if( $models = fld_models_get( 0, 0, $cls['id'] ) ){

							$count = ria_mysql_num_rows($models);
							if( $count>0 ){
								print '<tr><th colspan="'.( $ordered ? 5 : 4 ).'">'.htmlspecialchars($cls['name']).' ('.ria_number_format($count).')</th></tr>';
								$models_count += $count;
							}

							while( $r = ria_mysql_fetch_array($models) ){
								print '	<tr id="line-' . $r['id'] . '" class="ria-row-orderable fld-' . $cls['id'] . '">
											<td headers="select"><input type="checkbox" class="checkbox" name="mdl[]" value="'.$r['id'].'" /></td>';
								if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_MODEL_EDIT') ){
									print '	<td headers="name"><a href="edit.php?mdl='.$r['id'].'">'.htmlspecialchars($r['name']).'</a></td>';
								}else{
									print '	<td headers="name">'.htmlspecialchars($r['name']).'</td>';
								}
								print '	<td headers="fields" class="align-right"><a href="../fields/index.php?mdl='.$r['id'].'">'.ria_number_format(fld_models_get_fields_count($r['id']))._(' champs') . '</a></td>';
								$link_url = '';
								switch( $cls['id'] ){
									case CLS_PRODUCT:
										if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT') ){
											$link_url = '<a href="../../../catalog/index.php?mdl='.$r['id'].'">'.ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']).'</a>';
										}else{
											$link_url = ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']);
										}
										break;
									case CLS_USER:
										if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER') ){
											$link_url = '<a href="../../../customers/index.php?mdl='.$r['id'].'">'.ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']).'</a>';
										}else{
											$link_url = ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']);
										}

										break;
									case CLS_ORDER:
										if( gu_user_is_authorized('_RGH_ADMIN_ORDER') ){
											$link_url = '<a href="../../../orders/orders.php?mdl='.$r['id'].'">'.ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']).'</a>';
										}else{
											$link_url = ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']);
										}
										break;
									case CLS_BRAND:
										if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_BRAND') ){
											$link_url = '<a href="../../../catalog/brands/index.php?mdl='.$r['id'].'">'.ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']).'</a>';
										}else{
											$link_url = ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']);
										}
										break;
									case CLS_STORE:
										if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_STORE') ){
											$link_url = '<a href="../../livraison/stores/index.php?mdl='.$r['id'].'">'.ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']).'</a>';
										}else{
											$link_url = ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']);
										}
										break;
									default:
										$link_url = ria_number_format(fld_object_models_get_count(0, $r['id'])).' '.htmlspecialchars($cls['name']);
										break;
								}
								print '	<td headers="objects" class="align-right">'.$link_url.'</td>';
								if( $ordered ){
									print '<td headers="pos" class="align-center ria-cell-move">';
									print '		<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
									print '	</td>';
								}
								print '</tr>';
							}

						}
					}
					if( $models_count==0 ){
						print '<tr><td colspan="'.( $ordered ? 5 : 4 ).'">' . _('Aucun modèle de saisie') . '</td></tr>';
					}
				}
			?>
		</tbody>
		<tfoot>
			<tr><td colspan="<?php print $ordered ? 5 : 4; ?>">
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_MODEL_DEL') ){ ?>
				<input type="submit" name="del" class="btn-del float-left" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les modèles sélectionnés"); ?>" onclick="return fldModelConfirmDelList()" />
				<?php } ?>
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_FIELD_MODEL_ADD') ){ ?>
				<input type="submit" name="add" class="btn-add" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter une unité"); ?>" onclick="return fldModelAdd()" />
				<?php } ?>
			</td></tr>
			<tr><td colspan="<?php print $ordered ? 5 : 4; ?>" class="tfoot-grey align-left">
				<label><?php echo _('Trier ces modèles par ordre :'); ?></label>
				<input type="radio" class="radio" name="order" id="order-0" value="0" <?php if( !$ordered ) print 'checked="checked"'; ?> /> <label for="order-0"><?php echo _("Alphabétique"); ?></label>
				<input type="radio" class="radio" name="order" id="order-1" value="1" <?php if( $ordered ) print 'checked="checked"'; ?> /> <label for="order-1"><?php echo _("Personnalisé"); ?></label>
				<input type="submit" name="orderby" value="<?php echo _("Appliquer"); ?>" />
			</td></tr>
		</tfoot>
	</table>
	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>