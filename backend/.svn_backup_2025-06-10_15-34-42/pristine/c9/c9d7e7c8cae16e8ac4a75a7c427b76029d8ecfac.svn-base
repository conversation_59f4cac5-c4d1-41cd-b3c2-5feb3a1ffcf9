<?php

	/** \file clean-search-caches.php
	 *
	 * 	Ce script est destiné à faire le ménage dans les caches de recherche.
	 *	Quatre paramètres peuvent être donnés à l'appel :
	 *				- argv1 : identifiant du tenant
	 *				- argv2 : nombre de jours depuis lesquels les caches non pas été mis à jour (0 = tous les caches)
	 *				- argv3 : mettre true pour activer le mode test (aucune suppression des résultats de recherche ne sera faite)
	 *				- argv4 : mettre true pour activer l'affichage de l'avancement du ménage des caches
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once( 'products.inc.php' );

	$clean_days = isset($ar_params['days']) && is_numeric($ar_params['days']) && $ar_params['days']>=0 ? $ar_params['days'] : 30;
	$mode_test 	= isset($ar_params['test']) && $ar_params['test']=='true' ? true : false;
	$show_progress = isset($ar_params['progress']) && $ar_params['progress']=='true' ? true : false;

	foreach ($configs as $config) {
		print $config['tnt_id'].' - '.$config['wst_id'].PHP_EOL;continue;

		if( $mode_test || $show_progress ){
			print 'Démarrage du ménage dans les caches de recherche'."\n";
			print '	Paramètre : '."\n";
			print '		* Nombre de jours : '.$clean_days."\n";
			print '		* Mode test : '.( $mode_test ? 'Oui' : 'Non' )."\n";
			print '		* Tenant : '.$config['tnt_id']."\n\n";
		}
	
		$result = search_caches_clean( $config['tnt_id'], $clean_days, $mode_test, $show_progress );
	
		if( $mode_test || $show_progress ){
			if( is_array($result) ){
				print "\n";
				print 'Affiché : '.$result['count_show']."\n";
				print 'Masqués : '.$result['count_hide']."\n";
				print 'Doublon : '.$result['count_dbl']."\n";
				print 'Supprimé : '.$result['count_del'].' / '.$result['count_total']."\n";
				print 'Masqué conservé : '.$result['count_hide_save'].' / '.$result['count_hide']."\n";
			}
		}
	}
