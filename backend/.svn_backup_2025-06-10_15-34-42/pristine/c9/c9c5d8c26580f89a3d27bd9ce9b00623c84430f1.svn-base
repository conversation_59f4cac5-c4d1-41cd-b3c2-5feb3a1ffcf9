<?php
/**
 * Unit test class for the UseDeclaration sniff.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2006-2019 Squiz Pty Ltd (ABN **************)
 * @license   https://github.com/squizlabs/PHP_CodeSniffer/blob/master/licence.txt BSD Licence
 */

namespace PHP_CodeSniffer\Standards\PSR12\Tests\Traits;

use PHP_CodeSniffer\Tests\Standards\AbstractSniffUnitTest;

class UseDeclarationUnitTest extends AbstractSniffUnitTest
{


    /**
     * Returns the lines where errors should occur.
     *
     * The key of the array should represent the line number and the value
     * should represent the number of errors that should occur on that line.
     *
     * @return array<int, int>
     */
    public function getErrorList()
    {
        return [
            15  => 1,
            29  => 2,
            30  => 1,
            42  => 1,
            57  => 3,
            59  => 3,
            61  => 1,
            63  => 5,
            65  => 1,
            71  => 1,
            73  => 2,
            76  => 1,
            86  => 2,
            103 => 1,
            112 => 1,
            122 => 1,
            132 => 1,
            157 => 1,
            165 => 1,
            170 => 1,
        ];

    }//end getErrorList()


    /**
     * Returns the lines where warnings should occur.
     *
     * The key of the array should represent the line number and the value
     * should represent the number of warnings that should occur on that line.
     *
     * @return array<int, int>
     */
    public function getWarningList()
    {
        return [];

    }//end getWarningList()


}//end class
