<?php
	require_once('site.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_PAIEMENT_TRANSFER');
	
	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: /admin/config/paiements/transfer.php');
		exit;
	}
	
	$transfer = -1;
	if( isset($_GET['transfer']) ){
		$transfer = $_GET['transfer'];
	}
	
	// Vérifie que l'identifiant fourni correspond à un enregistrement existant
	if( $transfer>0 && !site_bank_details_exists($transfer) ){
		header('Location: /admin/config/paiements/transfer.php');
		exit;
	}
	
	// Bouton Supprimer (depuis la fiche)
	if( isset($_POST['del']) && $transfer>0 ){
		if( !site_bank_details_del($transfer) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression des informations bancaires.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
		}
		
		if( !isset($error) ){
			header('Location: /admin/config/paiements/transfer.php');
			exit;
		}
	}
	
	// Bouton Supprimer (depuis la liste)
	if( isset($_GET['del']) ){
		foreach( $_GET['del'] as $d ){
			if( !site_bank_details_del($d) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression des informations bancaires.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
			}
		}
		
		if( !isset($error) ){
			header('Location: /admin/config/paiements/transfer.php');
			exit;
		}
	}

	if( isset($_GET['show']) ){
		header('Location: /admin/config/paiements/transfer.php?transfer='.$_GET['show']);
		exit;
	}
	
	// Bouton Enregistrer
	if( isset($_POST['save']) ){
		$iban  = '';
		$iban .= isset($_POST['iban1']) ? $_POST['iban1'] : ''; 
		$iban .= isset($_POST['iban2']) ? $_POST['iban2'] : ''; 
		$iban .= isset($_POST['iban3']) ? $_POST['iban3'] : ''; 
		$iban .= isset($_POST['iban4']) ? $_POST['iban4'] : ''; 
		$iban .= isset($_POST['iban5']) ? $_POST['iban5'] : ''; 
		$iban .= isset($_POST['iban6']) ? $_POST['iban6'] : ''; 
		$iban .= isset($_POST['iban7']) ? $_POST['iban7'] : '';
		
		$iban = strtoupper( $iban );
		
		$rib  = '';
		$rib .= isset($_POST['cbank']) 		? $_POST['cbank'] 	: '';
		$rib .= isset($_POST['counter']) 	? $_POST['counter'] : '';
		$rib .= isset($_POST['account']) 	? $_POST['account'] : '';
		$rib .= isset($_POST['key']) 		? $_POST['key'] : '';
		
		$err = array();
		// vérifier si toutes les informations ont été saisi
		if( !isset($_POST['name']) || trim($_POST['name'])=='' ){
			$err[] = _("Veuillez renseigner un libellé permattant d'identifier les informations bancaires.");
		}

		if( strlen($rib)!=23 ){
			$err[] = _("Votre RIB doit être composé de 23 caractères.");
		}
		if( !isset($_POST['cbank']) || trim($_POST['cbank'])=='' ){
			$err[] = _("Veuillez renseigner le code banque.");
		} 
		if( !isset($_POST['counter']) || trim($_POST['counter'])=='' ){
			$err[] = _("Veuillez renseigner le code guichet.");
		} 
		if( !isset($_POST['account']) || trim($_POST['account'])=='' ){
			$err[] = _("Veuillez renseigner votre numéro de compte.");
		}
		
		if( !isset($_POST['key']) || trim($_POST['key'])=='' ){
			$err[] = _("Veuillez renseigner la clé.");
		} elseif( !is_numeric($_POST['key']) || strlen($_POST['key'])!=2 || $_POST['key']<1 || $_POST['key']>97 ){
			$err[] = _("Votre clé RIB n'est pas correcte. Il doit s'agir de 2 chiffres entre 01 et 97.");
		}
		
		if( 
			!isset($_POST['iban1']) || trim($_POST['iban1'])=='' 
			|| !isset($_POST['iban2']) || trim($_POST['iban2'])=='' 
			|| !isset($_POST['iban3']) || trim($_POST['iban3'])=='' 
			|| !isset($_POST['iban4']) || trim($_POST['iban4'])=='' 
			|| !isset($_POST['iban5']) || trim($_POST['iban5'])=='' 
			|| !isset($_POST['iban6']) || trim($_POST['iban6'])=='' 
			|| !isset($_POST['iban7']) || trim($_POST['iban7'])=='' 
		){
			$err[] = _("Veuillez renseigner tous les champs composant votre IBAN (International Bank Account Number).");
		}  elseif( strlen($iban)!=27 ){
			$err[] = _("Votre IBAN (International Bank Account Number) doit être composé de 27 caractères.");
		}
		
		if( !isset($_POST['bic']) || trim($_POST['bic'])=='' ){
			$err[] = _("Veuillez renseigner votre BIC (Bank Identifier Code).");
		} elseif( strlen($_POST['bic'])!=8 && strlen($_POST['bic'])!=11 ){
			$err[] = _("Votre BIC (Bank Identifier Code) doit être constitué de 8 ou 11 caractères."); 
		}
		
		if( is_array($err) && sizeof($err) ){
			$error = implode("\n", $err);
		 } else {
			if( $transfer==0 ){
				$add = site_bank_details_add( $_POST['name'], $_POST['cbank'], $_POST['counter'], $_POST['account'], $_POST['key'], $iban, $_POST['bic'] );
				if( $add===-1 ){
					$error = _("Votre RIB n'est pas reconnue comme valide. Veuillez vérifier votre saisie.");
				} elseif( $add===-2 ){
					$error = _("Votre IBAN (International Bank Account Number) n'est pas reconnue comme valide.");
				} elseif( $add===-3 ){
					$error = _("Votre BIC (Bank Identifier Code) n'est pas reconnue comme valide.");
				} elseif( !$add ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des informations bancaires.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				} else {
					$transfer = $add;
				}
			} else{
				$update = site_bank_details_update( $transfer, $_POST['name'], $_POST['cbank'], $_POST['counter'], $_POST['account'], $_POST['key'], $iban, $_POST['bic'] );
				if( $update===-1 ){
					$error = _("Votre RIB n'est pas reconnue comme valable. Veuillez vérifier votre saisie.");
				} elseif( $update===-2 ){
					$error = _("Votre IBAN (International Bank Account Number) n'est pas reconnue comme valide.");
				} elseif( $update===-3 ){
					$error = _("Votre BIC (Bank Identifier Code) n'est pas reconnue comme valide.");
				} elseif( !$update ){
					$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des informations bancaires.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
				}
			}
			
			if( !isset($error) ){
				if( $transfer>0 && !site_bank_profils_del($transfer) ) {
					$error = "Une erreur inattendue s'est produite lors de la préparation à l'enregistrement des profils.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.";
				} elseif( is_array($_POST['trf-prf']) && sizeof($_POST['trf-prf']) ) {
					foreach( $_POST['trf-prf'] as $prf ){
						if( !site_bank_profils_add($transfer, $prf) ){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des profils.\nVeuillez réessayer ou prendre contact pour nous signaler le problème.");
							break;
						}
					}
				}
			}
		}
		
		if( !isset($error) ){
			header('Location: /admin/config/paiements/transfer.php?transfer='.$transfer.'&success=1');
			exit;
		}
	}

	// Bouton Ajouter (depuis la liste)
	if( isset($_GET['add']) ){
		header('Location: /admin/config/paiements/transfer.php?transfer=0');
		exit;
	}

	define('ADMIN_PAGE_TITLE', _('Règlement par virement') . ' - ' . _('Modes de règlement') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	//Récupération des informations dans la BDD
	if( $transfer>0 && !isset($_POST['save']) ){
		$trf = ria_mysql_fetch_array( site_bank_details_get($transfer) );
		$ar_prfs = site_bank_profils_get_array( $transfer );
	}

	// Affichage du h2 en fonction de la page où on se trouve
	if( isset($_GET['transfer']) && is_numeric($_GET['transfer']) ) {
		if( isset($trf['name']) && trim($trf['name'])!='' ) {
			$title_transfer = _('Edition d\'informations bancaires :').' '.$trf['name'];
		}else{
			$title_transfer = _('Ajout d\'informations bancaires');
		}
	}else{
		$title_transfer = _("Règlement par virement");
	}
?>
	<h2><?php echo htmlspecialchars($title_transfer); ?></h2>
	<?php
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
		if( isset($_GET['success']) ){
			print '<div class="success">' . _('L\'enregistrement des informations bancaires s\'est correctement déroulé.') . '</div>';
		}
		
		if( $transfer>-1 ){
		$ar_prfs = isset($_POST['trf-prf']) && is_array($_POST['trf-prf']) ? $_POST['trf-prf'] 	: array();
		
		$trf = array( 
			'name' 		=> isset($_POST['name']) 	? $_POST['name']	: '',
			'cbank'		=> isset($_POST['cbank']) 	? $_POST['cbank'] 	: '', 
			'counter'	=> isset($_POST['counter']) ? $_POST['counter'] : '', 
			'account'	=> isset($_POST['account']) ? $_POST['account'] : '', 
			'key'		=> isset($_POST['key']) 	? $_POST['key'] 	: '', 
			'iban'		=> isset($_POST['iban']) 	? $_POST['iban'] 	: '', 
			'bic'		=> isset($_POST['bic']) 	? $_POST['bic'] 	: ''
		);
		
		$part_iban = array( 
			isset($_POST['iban1']) ? strtoupper($_POST['iban1']) : '', 
			isset($_POST['iban2']) ? strtoupper($_POST['iban2']) : '', 
			isset($_POST['iban3']) ? strtoupper($_POST['iban3']) : '', 
			isset($_POST['iban4']) ? strtoupper($_POST['iban4']) : '', 
			isset($_POST['iban5']) ? strtoupper($_POST['iban5']) : '', 
			isset($_POST['iban6']) ? strtoupper($_POST['iban6']) : '', 
			isset($_POST['iban7']) ? strtoupper($_POST['iban7']) : '' 
		);
	?>
		<form action="/admin/config/paiements/transfer.php?transfer=<?php print $transfer; ?>" method="post">
			<table id="tb-transfer" class="checklist">
				<caption><?php echo _("Informations de compte"); ?></caption>
				<tbody id="transfert-positif">
					<tr>
						<td>
							<label for="name"><span class="mandatory">*</span> <?php echo _("Libellé :"); ?></label>
						</td>
						<td>
							<div class="elem">
								<input type="text" name="name" id="name" value="<?php print htmlspecialchars($trf['name']); ?>" />
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<label for="cbank"><span class="mandatory">*</span> <?php echo _("RIB :"); ?></label>
						</td>
						<td>
							<div class="elem">
								<input class="cbank" type="text" name="cbank" id="cbank" value="<?php print $trf['cbank']; ?>" maxlength="5" />
								<label for="cbank"><?php echo _("Code banque"); ?></label>
							</div><div class="elem">
								<input class="counter" type="text" name="counter" id="counter" value="<?php print $trf['counter']; ?>" maxlength="5" />
								<label for="cbank"><?php echo _("Code guichet"); ?></label>
							</div><div class="elem elem-account">
								<input class="account" type="text" name="account" id="account" value="<?php print $trf['account']; ?>" maxlength="11" />
								<label for="cbank"><?php echo _("Numéro de compte"); ?></label>
							</div><div class="elem elem-key">
								<input class="key" type="text" name="key" id="key" value="<?php print $trf['key']; ?>" maxlength="2" />
								<label for="cbank"><?php echo _("Clé"); ?></label>
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<label for="iban1"><span class="mandatory">*</span> <?php echo _("IBAN :"); ?></label>
						</td>
						<td>
							<?php
								if( trim($trf['iban'])!='' ){
									$iban = str_replace( array(' ', '-', '.', '_'), '', $trf['iban'] );
									$iban = chunk_split( $iban, 4, '~' );
									$part_iban = explode('~', $iban );
								}
							?>
							<div class="elem elem-iban">
								<input class="cbank" type="text" name="iban1" id="iban1" value="<?php print $part_iban[0]; ?>" />
							</div><div class="elem elem-iban">
								<input class="cbank" type="text" name="iban2" id="iban2" value="<?php print $part_iban[1]; ?>" />
							</div><div class="elem elem-iban">
								<input class="cbank" type="text" name="iban3" id="iban3" value="<?php print $part_iban[2]; ?>" />
							</div><div class="elem elem-iban">
								<input class="cbank" type="text" name="iban4" id="iban4" value="<?php print $part_iban[3]; ?>" />
							</div><div class="elem elem-iban">
								<input class="cbank" type="text" name="iban5" id="iban5" value="<?php print $part_iban[4]; ?>" />
							</div><div class="elem elem-iban">
								<input class="cbank" type="text" name="iban6" id="iban6" value="<?php print $part_iban[5]; ?>" />
							</div><div class="elem elem-iban">
								<input class="cbank" type="text" name="iban7" id="iban7" value="<?php print $part_iban[6]; ?>" />
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<label for="bic"><span class="mandatory">*</span> <?php echo _("BIC :"); ?></label>
						</td>
						<td>
							<input type="text" name="bic" id="bic" value="<?php print $trf['bic']; ?>" />
						</td>
					</tr>
					<tr>
						<th colspan="2"><?php echo _("Visibles par les profils suivants :"); ?></th>
					</tr>
					<tr>
						<td colspan="2" class="bg-white">
							<a class="checkall" href="#"><?php echo _("Tout cocher"); ?></a>/<a class="uncheckall" href="#"><?php echo _("Tout décocher"); ?></a>
							<?php
							$rprf = gu_profiles_get();
							if( $rprf && ria_mysql_num_rows($rprf) ){
								while( $prf = ria_mysql_fetch_array($rprf) ){
									$checked = in_array($prf['id'], $ar_prfs) ? 'checked="checked"' : '';
									
									print '	<div>
												<input class="check-prf" '.$checked.' type="checkbox" name="trf-prf[]" id="trf-prf-'.$prf['id'].'" value="'.$prf['id'].'" />
												<label for="trf-prf-'.$prf['id'].'">'.htmlspecialchars( $prf['name'] ).'</label>
											</div>';
								}
							}
						?></td>
					</tr>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="2">
							<input type="submit" name="save" id="save" value="<?php echo _("Enregistrer"); ?>" />
							<input type="submit" value="<?php echo _("Annuler"); ?>" name="cancel" />
							<?php
								if( isset($trf['id']) && $trf['id']>0 ){
									print '<input type="submit" onclick="return confirmDelTransfer();" value="'._('Supprimer').'" name="del[]" value='.$trf['id'].'" />';
								}
							?>
						</td>
					</tr>
				</tfoot>
			</table>
		</form>
	<?php } else { ?>
		<form action="/admin/config/paiements/transfer.php" method="get">
			<div class="notice">
				<?php echo _("Vous pouvez configurer vos informations bancaires afin d'activer le règlement par virement."); ?>
			</div>
			<table id="tb-transfer" class="checklist">
				<caption><?php echo _("Liste des informations bancaires"); ?></caption>
				<thead>
					<tr>
						<th id="trf-delete">
							<input type="checkbox" name="del-all" id="del-all" value="" />
						</th>
						<th id="trf-describe"><?php echo _("Description"); ?></th>
					</tr>
				</thead>
				<tbody><?php
					$rtrf = site_bank_details_get();
					if( !$rtrf || !ria_mysql_num_rows($rtrf) ){
						print '	<tr>
									<td colspan="2">' . _("Aucune informations bancaires n'est renseignée pour le moment") . '</td>
								</tr>';
					} else {
						while( $trf = ria_mysql_fetch_array($rtrf) ){
							print '	<tr>
										<td headers="trf-delete">
											<input class="del-trf" type="checkbox" name="del[]" id="del-'.$trf['id'].'" value="'.$trf['id'].'" />
										</td>
										<td headers="trf-describe">';
							print '			'.site_bank_details_describe( $trf );
							print '		</td>
									</tr>';
						}
					}
				?>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="2" class="align-left">
							<input onclick="return confirmDelTransfer();" type="submit" name="del-all" id="del-all-action" value="<?php echo _("Supprimer"); ?>" />
							<div class="float-right">
								<input type="submit" value="<?php echo _("Ajouter"); ?>" name="add" />
							</div>
						</td>
					</tr>
				</tfoot>
			</table>
		</form>

<?php 
	} 

	require_once('admin/skin/footer.inc.php');
?>
