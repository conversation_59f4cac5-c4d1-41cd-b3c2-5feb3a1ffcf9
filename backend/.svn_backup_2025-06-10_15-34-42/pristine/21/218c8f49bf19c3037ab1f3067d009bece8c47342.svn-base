<?php

namespace Muglug\PackageVersions;

use <PERSON>\Composer;
use Composer\Config;
use Composer\EventDispatcher\EventSubscriberInterface;
use Composer\IO\IOInterface;
use Composer\Package\AliasPackage;
use Composer\Package\Locker;
use Composer\Package\PackageInterface;
use Composer\Package\RootPackageInterface;
use Composer\Plugin\PluginInterface;
use Composer\Script\Event;
use Composer\Script\ScriptEvents;

final class Installer implements PluginInterface, EventSubscriberInterface
{
    private static $generatedClassTemplate = <<<'PHP'
<?php

namespace Muglug\PackageVersions;

/**
 * This class is generated by muglug/package-versions-56, specifically by
 * @see \Muglug\PackageVersions\Installer
 *
 * This file is overwritten at every run of `composer install` or `composer update`.
 */
%s
{
    public static $VERSIONS = %s;

    private function __construct()
    {
    }

    /**
     * @throws \OutOfBoundsException if a version cannot be located
     */
    public static function getVersion($packageName)
    {
        $selfVersion = self::$VERSIONS;

        if (! isset($selfVersion[$packageName])) {
            throw new \OutOfBoundsException(
                'Required package "' . $packageName . '" is not installed: cannot detect its version'
            );
        }

        return $selfVersion[$packageName];
    }

    /**
     * @throws \OutOfBoundsException if a version cannot be located
     */
    public static function getShortVersion($packageName)
    {
        return explode('@', static::getVersion($packageName))[0];
    }

    /**
     * @throws \OutOfBoundsException if a version cannot be located
     */
    public static function getMajorVersion($packageName)
    {
        return explode('.', static::getShortVersion($packageName))[0];
    }
}

PHP;

    /**
     * {@inheritDoc}
     */
    public function activate(Composer $composer, IOInterface $io)
    {
        // Nothing to do here, as all features are provided through event listeners
    }

    /**
     * {@inheritDoc}
     */
    public static function getSubscribedEvents()
    {
        return [
            ScriptEvents::POST_INSTALL_CMD => 'dumpVersionsClass',
            ScriptEvents::POST_UPDATE_CMD  => 'dumpVersionsClass',
        ];
    }

    /**
     * @param Event $composerEvent
     *
     * @return void
     *
     * @throws \RuntimeException
     */
    public static function dumpVersionsClass(Event $composerEvent)
    {
        $composer = $composerEvent->getComposer();
        $versions = iterator_to_array(self::getVersions($composer->getLocker(), $composer->getPackage()));

        if (!array_key_exists('muglug/package-versions-56', $versions)) {
            //plugin must be globally installed - we only want to generate versions for projects which specifically
            //require muglug/package-versions
            return;
        }

        self::writeVersionClassToFile(self::generateVersionsClass($versions), $composer, $composerEvent->getIO());
    }

    private static function generateVersionsClass(array $versions)
    {
        return sprintf(
            self::$generatedClassTemplate,
            'fin' . 'al ' . 'cla' . 'ss ' . 'Versions', // note: workaround for regex-based code parsers :-(
            var_export($versions, true)
        );
    }

    /**
     * @param string $versionClassSource
     * @param Composer $composer
     * @param IOInterface $io
     *
     * @return void
     *
     * @throws \RuntimeException
     */
    private static function writeVersionClassToFile($versionClassSource, Composer $composer, IOInterface $io)
    {
        $installPath = self::locateRootPackageInstallPath($composer->getConfig(), $composer->getPackage())
            . '/src/PackageVersions/Versions.php';

        if (! file_exists(dirname($installPath))) {
            $io->write('<info>muglug/package-versions-56:</info> Package not found (probably scheduled for removal); generation of version class skipped.');

            return;
        }

        $io->write('<info>muglug/package-versions-56:</info>  Generating version class...');

        file_put_contents($installPath, $versionClassSource);
        chmod($installPath, 0664);

        $io->write('<info>muglug/package-versions-56:</info> ...done generating version class');
    }

    /**
     * @param Config               $composerConfig
     * @param RootPackageInterface $rootPackage
     *
     * @return string
     *
     * @throws \RuntimeException
     */
    private static function locateRootPackageInstallPath(
        Config $composerConfig,
        RootPackageInterface $rootPackage
    ) {
        if ('muglug/package-versions-56' === self::getRootPackageAlias($rootPackage)->getName()) {
            return dirname($composerConfig->get('vendor-dir'));
        }

        return $composerConfig->get('vendor-dir') . '/muglug/package-versions-56';
    }

    private static function getRootPackageAlias(RootPackageInterface $rootPackage)
    {
        $package = $rootPackage;

        while ($package instanceof AliasPackage) {
            $package = $package->getAliasOf();
        }

        return $package;
    }

    /**
     * @param Locker               $locker
     * @param RootPackageInterface $rootPackage
     *
     * @return \Generator|\string[]
     */
    private static function getVersions(Locker $locker, RootPackageInterface $rootPackage)
    {
        $lockData = $locker->getLockData();

        $lockData['packages-dev'] = isset($lockData['packages-dev']) ? $lockData['packages-dev'] : [];

        foreach (array_merge($lockData['packages'], $lockData['packages-dev']) as $package) {
            yield $package['name'] => $package['version'] . '@' . (
                isset($package['source']['reference'])
                    ? $package['source']['reference']
                    : (isset($package['dist']['reference']) ? $package['dist']['reference'] : '')
            );
        }

        yield $rootPackage->getName() => $rootPackage->getVersion() . '@' . $rootPackage->getSourceReference();
    }
}
