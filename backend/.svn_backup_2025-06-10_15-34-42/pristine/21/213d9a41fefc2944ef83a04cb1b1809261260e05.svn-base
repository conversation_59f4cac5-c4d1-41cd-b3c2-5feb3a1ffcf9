<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/resources.proto

namespace Google\Cloud\ServiceManagement\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\ServiceManagement\V1\ConfigFile\FileType instead.
     * @deprecated
     */
    class ConfigFile_FileType {}
}
class_exists(ConfigFile\FileType::class);
@trigger_error('Google\Cloud\ServiceManagement\V1\ConfigFile_FileType is deprecated and will be removed in the next major release. Use Google\Cloud\ServiceManagement\V1\ConfigFile\FileType instead', E_USER_DEPRECATED);

