<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/expr.proto

namespace Google\Type;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Represents an expression text. Example:
 *     title: "User account presence"
 *     description: "Determines whether the request has a user account"
 *     expression: "size(request.user) > 0"
 *
 * Generated from protobuf message <code>google.type.Expr</code>
 */
class Expr extends \Google\Protobuf\Internal\Message
{
    /**
     * Textual representation of an expression in
     * Common Expression Language syntax.
     * The application context of the containing message determines which
     * well-known feature set of CEL is supported.
     *
     * Generated from protobuf field <code>string expression = 1;</code>
     */
    private $expression = '';
    /**
     * An optional title for the expression, i.e. a short string describing
     * its purpose. This can be used e.g. in UIs which allow to enter the
     * expression.
     *
     * Generated from protobuf field <code>string title = 2;</code>
     */
    private $title = '';
    /**
     * An optional description of the expression. This is a longer text which
     * describes the expression, e.g. when hovered over it in a UI.
     *
     * Generated from protobuf field <code>string description = 3;</code>
     */
    private $description = '';
    /**
     * An optional string indicating the location of the expression for error
     * reporting, e.g. a file name and a position in the file.
     *
     * Generated from protobuf field <code>string location = 4;</code>
     */
    private $location = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $expression
     *           Textual representation of an expression in
     *           Common Expression Language syntax.
     *           The application context of the containing message determines which
     *           well-known feature set of CEL is supported.
     *     @type string $title
     *           An optional title for the expression, i.e. a short string describing
     *           its purpose. This can be used e.g. in UIs which allow to enter the
     *           expression.
     *     @type string $description
     *           An optional description of the expression. This is a longer text which
     *           describes the expression, e.g. when hovered over it in a UI.
     *     @type string $location
     *           An optional string indicating the location of the expression for error
     *           reporting, e.g. a file name and a position in the file.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Type\Expr::initOnce();
        parent::__construct($data);
    }

    /**
     * Textual representation of an expression in
     * Common Expression Language syntax.
     * The application context of the containing message determines which
     * well-known feature set of CEL is supported.
     *
     * Generated from protobuf field <code>string expression = 1;</code>
     * @return string
     */
    public function getExpression()
    {
        return $this->expression;
    }

    /**
     * Textual representation of an expression in
     * Common Expression Language syntax.
     * The application context of the containing message determines which
     * well-known feature set of CEL is supported.
     *
     * Generated from protobuf field <code>string expression = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setExpression($var)
    {
        GPBUtil::checkString($var, True);
        $this->expression = $var;

        return $this;
    }

    /**
     * An optional title for the expression, i.e. a short string describing
     * its purpose. This can be used e.g. in UIs which allow to enter the
     * expression.
     *
     * Generated from protobuf field <code>string title = 2;</code>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * An optional title for the expression, i.e. a short string describing
     * its purpose. This can be used e.g. in UIs which allow to enter the
     * expression.
     *
     * Generated from protobuf field <code>string title = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setTitle($var)
    {
        GPBUtil::checkString($var, True);
        $this->title = $var;

        return $this;
    }

    /**
     * An optional description of the expression. This is a longer text which
     * describes the expression, e.g. when hovered over it in a UI.
     *
     * Generated from protobuf field <code>string description = 3;</code>
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * An optional description of the expression. This is a longer text which
     * describes the expression, e.g. when hovered over it in a UI.
     *
     * Generated from protobuf field <code>string description = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setDescription($var)
    {
        GPBUtil::checkString($var, True);
        $this->description = $var;

        return $this;
    }

    /**
     * An optional string indicating the location of the expression for error
     * reporting, e.g. a file name and a position in the file.
     *
     * Generated from protobuf field <code>string location = 4;</code>
     * @return string
     */
    public function getLocation()
    {
        return $this->location;
    }

    /**
     * An optional string indicating the location of the expression for error
     * reporting, e.g. a file name and a position in the file.
     *
     * Generated from protobuf field <code>string location = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setLocation($var)
    {
        GPBUtil::checkString($var, True);
        $this->location = $var;

        return $this;
    }

}

