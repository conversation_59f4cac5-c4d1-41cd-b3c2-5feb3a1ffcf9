<?php
	/**	\file subscriptions.php
	 * 
	 * 	Cette page affiche les statistiques d'utilisation de la solution Yuto :
	 * 	- Nombre de licences actives
	 *  - Nombre de licences maximum autorisées
	 * 
	 */
	require_once('admin/get-filters.php');
	require_once('stats.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	if( !gu_user_is_authorized('_RGH_ADMIN_FDV_STATS_USE') && !gu_user_is_authorized('_RGH_ADMIN_STATS_YUTO') ){
		header('HTTP/1.0 403 Forbidden');
		exit;
	}
	
	define('ADMIN_PAGE_TITLE', _('Utilisation') . ' - ' . _('Statistiques') );
	require_once('admin/skin/header.inc.php');
?>
<h2><?php print _('Nombre de licences actives'); ?></h2>
	<div class="stats-menu">
		<div id="riadatepicker"></div>
		<div class="clear"></div>
	</div>

	<?php view_import_highcharts(); ?>	
	<?php
		$highcharts_show_title = false;
		require_once( 'admin/highcharts/graph-dev-subscriptions.php' );
	?>
	<script><!--
		var urlHighcharts = '/admin/fdv/stats/subscriptions.php';
		var riadatepicker_upd_url = '';
		<?php view_date_initialized( 0, '', false, array() ); ?>
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>
