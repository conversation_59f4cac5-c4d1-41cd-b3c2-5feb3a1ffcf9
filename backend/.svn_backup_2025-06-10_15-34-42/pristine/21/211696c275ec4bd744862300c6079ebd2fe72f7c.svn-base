<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/schema.proto

namespace Google\Cloud\PubSub\V1;

use UnexpectedValueException;

/**
 * View of Schema object fields to be returned by GetSchema and ListSchemas.
 *
 * Protobuf type <code>google.pubsub.v1.SchemaView</code>
 */
class SchemaView
{
    /**
     * The default / unset value.
     * The API will default to the BASIC view.
     *
     * Generated from protobuf enum <code>SCHEMA_VIEW_UNSPECIFIED = 0;</code>
     */
    const SCHEMA_VIEW_UNSPECIFIED = 0;
    /**
     * Include the name and type of the schema, but not the definition.
     *
     * Generated from protobuf enum <code>BASIC = 1;</code>
     */
    const BASIC = 1;
    /**
     * Include all Schema object fields.
     *
     * Generated from protobuf enum <code>FULL = 2;</code>
     */
    const FULL = 2;

    private static $valueToName = [
        self::SCHEMA_VIEW_UNSPECIFIED => 'SCHEMA_VIEW_UNSPECIFIED',
        self::BASIC => 'BASIC',
        self::FULL => 'FULL',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

