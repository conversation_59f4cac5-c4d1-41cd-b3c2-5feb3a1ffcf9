<?php

	/** \file confirm-orders-rueducommerce.php
	 *
	 * 	Ce script est destiné à confirmer les nouvelles commandes réalisées sur la place de marché Rue du Commerce.
	 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
	 *
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('orders.inc.php');
	require_once('comparators/ctr.rueducommerce.inc.php');


	// Active ou non le mode test
	$mode_test = isset($ar_params['test']) && $ar_params['test'] == 'test';

	// Traitement
	foreach( $configs as $config ){
		// Vérifie que la place de marché est bien activée pour ce client.
		// Dans le cas contraire, passe au client suivant.
		if( !ctr_comparators_actived(CTR_RUEDUCOMMERCE) ){
			continue;
		}
		$states = array( 6, 7, 8 );

		// récupèrer le compte client, le produit frais de port et le service de livraison
		$pms = ctr_params_get_array( CTR_RUEDUCOMMERCE, array('USR_ID') );
		if( !isset($pms['USR_ID']) || !gu_users_exists($pms['USR_ID']) ){
			continue;
		}

		// récupère les commandes ayant fait l'objet d'une expédition
		$rord = ord_orders_get( $pms['USR_ID'], 0, $states, 0, null, false, false, false, false, false, false, '', true, array(_FLD_ORD_CTR_SHIPPED=>'Non') );

		if( $rord && ria_mysql_num_rows($rord) ){
			while( $ord = ria_mysql_fetch_array($rord) ){

				// Confirmation de commande sans passer par le WebService Rue du Commerce
				if( isset($config['ctr_confirm_order']) && $config['ctr_confirm_order'] ){
					// mise à jour de l'information de confirmation d'expédition chez Rue du Commerce
					if( !fld_object_values_set($ord['id'], _FLD_ORD_CTR_SHIPPED, 'Oui') ){
						mail('<EMAIL>', '['.$config['tnt_id'].' - RdC] Erreur lors de la mise à jour du champ avancé', 'Confirmation commande '.$ord['id'].' non confirmée');
					}

					continue;
				}

				// récupère le xml de confirmation
				$xml_shipping = ctr_rueducommerce_order_confirm_shipped( $ord['id'], $ord['ref'] );

				if( !$mode_test ){
					if( trim($xml_shipping)=='' ){
						mail('<EMAIL>', '['.$config['tnt_id'].' - RdC] Erreur lors de la confirmation de la commande '.$ord['id'], 'Impossible de génrérer le XML de confirmation');
					} else {
						$xml  = '<?xml version="1.0" encoding="UTF-8"?>'."\n";
						$xml .= '<mmie version="2.0">'."\n";
						$xml .= '	<orders>'."\n";
						$xml .= $xml_shipping."\n";
						$xml .= '	</orders>'."\n";
						$xml .= '</mmie>'."\n";

						$response = ctr_rueducommerce_update_satuts_send( $xml );

						if( !isset($response->message) || $response->message!='OK' ){
							mail( '<EMAIL>', '['.$config['tnt_id'].' - RdC] Erreur lors de la confirmation de la commandes '.$ord['id'], '( '.$xml.' )'.$response );
						} else {
							// mise à jour de l'information de confirmation d'expédition chez Rue du Commerce
							if( !fld_object_values_set($ord['id'], _FLD_ORD_CTR_SHIPPED, 'Oui') ){
								mail('<EMAIL>', '['.$config['tnt_id'].' - RdC] Erreur lors de la mise à jour du champ avancé', 'Confirmation commande '.$ord['id'].' non confirmée');
							}
						}
					}
				} else {
					print $xml_shipping."\n";
				}

			}
		}
	}
