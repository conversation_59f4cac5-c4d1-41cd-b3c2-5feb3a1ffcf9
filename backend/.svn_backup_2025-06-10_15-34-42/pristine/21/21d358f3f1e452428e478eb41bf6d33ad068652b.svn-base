<?php

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_PDF-GENERATION');
gu_if_authorized_else_403('_RGH_ADMIN_ORDER_DL_DEVIS');

require_once 'delivery.inc.php';
require_once 'prd/deposits.inc.php';
define('ADMIN_PAGE_TITLE', _('Devis').' - '._('Génération PDF').' - '._('Configuration'));

$errors = array();

$data = $config;
$deposits = array();
$r_deposits = prd_deposits_get();

if( $r_deposits && ria_mysql_num_rows($r_deposits) ){
	while( $deposit = ria_mysql_fetch_assoc($r_deposits) ){
		$deposits[$deposit['id']] = $deposit;
	}
}

$selected_deposit = !empty($_GET['deposit']) ? $_GET['deposit'] : 'all';

$is_default_config = $selected_deposit === 'all';

// Récupère la configuration surchargée (si présente).
if( !$is_default_config && ($quote_config = $deposits[$selected_deposit]['quote_config']) ){
	$data = (array) json_decode($quote_config);
}

$current_data = $data;

if( isset($_POST['delete-logo']) ){
	if( $is_default_config ){
		cfg_overrides_set_value('pdf_generation_devis_logo', '');
	}else{
		$data['pdf_generation_devis_logo'] = '';

		prd_deposits_store_quote_config($selected_deposit, $data);
	}

	header('Location: /admin/config/pdf_generation/devis.php?deposit='.$selected_deposit);
	exit;
}

// Enregistre les données du formulaire.
if( isset($_POST['save']) ){
	$data = array();

	if( !empty($_POST['devis-name']) ){
		$data['pdf_generation_devis_name'] = trim($_POST['devis-name']);
	}

	if( !empty($_POST['devis-url']) ){
		$data['pdf_generation_devis_url'] = trim($_POST['devis-url']);
	}

	$data['pdf_generation_devis_display_dlv_address'] = !empty($_POST['devis-display-dlv-address']) ? 1 : 0;

	if( $_FILES['devis-logo']['error'] !== UPLOAD_ERR_NO_FILE ){
		if( $_FILES['devis-logo']['error'] === UPLOAD_ERR_OK ){
			if( $img_id = img_images_upload('devis-logo') ){
				$data['pdf_generation_devis_logo'] = $img_id;
			}else{
				$errors[] = _('Erreur lors de l\'upload du logo du devis.');
			}
		}else{
			$data['pdf_generation_devis_logo'] = '';
		}
	}else{
		$data['pdf_generation_devis_logo'] = $current_data['pdf_generation_devis_logo'];
	}

	if( !empty($_POST['devis-logo-size-x']) ){
		$data['pdf_generation_devis_logo_size_x'] = (int) $_POST['devis-logo-size-x'];
	}

	if( !empty($_POST['devis-logo-size-y']) ){
		$data['pdf_generation_devis_logo_size_y'] = (int) $_POST['devis-logo-size-y'];
	}

	$data['pdf_generation_devis_logo_disposition'] = !empty($_POST['devis-logo-disposition']) ? 1 : 0;

	if( !empty($_POST['devis-font-size']) ){
		$data['pdf_generation_devis_font_size'] = (int) $_POST['devis-font-size'];
	}

	$data['pdf_generation_devis_prd_img'] = !empty($_POST['devis-prd-img']) ? 1 : 0;
	$data['pdf_generation_devis_prd_reduce'] = !empty($_POST['devis-prd-reduce']) ? 1 : 0;
	$data['pdf_generation_devis_prd_barcode'] = !empty($_POST['devis-prd-barcode']) ? 1 : 0;
	$data['pdf_generation_devis_prd_reftruncated'] = !empty($_POST['devis-prd-reftruncated']) ? 1 : 0;

	if( !empty($_POST['devis-header']) ){
		$data['pdf_generation_devis_header'] = 1;

		if( !empty($_POST['devis-header-content']) ){
			$data['pdf_generation_devis_header_content'] = trim($_POST['devis-header-content']);
		}
	}else{
		$data['pdf_generation_devis_header'] = 0;
		$data['pdf_generation_devis_header_content'] = '';
	}

	if( !empty($_POST['devis-footer']) ){
		$data['pdf_generation_devis_footer'] = 1;

		if( !empty($_POST['devis-footer-content']) ){
			$data['pdf_generation_devis_footer_content'] = trim($_POST['devis-footer-content']);
		}
	}else{
		$data['pdf_generation_devis_footer'] = 0;
		$data['pdf_generation_devis_footer_content'] = '';
	}

	$data['pdf_generation_devis_display_payment'] = !empty($_POST['devis-display-payment']) ? 1 : 0;

	if( $is_default_config ){
		foreach( $data as $code => $value ){
			if( !cfg_overrides_set_value($code, $value) ){
				$errors[] = _('Erreur lors de l\'enregistrement de la configuration du devis.');
				break;
			}
		}
	}else{
		if( !prd_deposits_store_quote_config($selected_deposit, $data) ){
			$errors[] = _('Erreur lors de l\'enregistrement de la configuration du devis.');
		}
	}

	if( !$errors ){
		header('Location: /admin/config/pdf_generation/devis.php?deposit='.$selected_deposit);
		exit;
	}
}

require_once 'admin/skin/header.inc.php';

?>

	<?php foreach( $errors as $error ){ ?>
		<div class="error"><?php print nl2br($error); ?></div>
	<?php } ?>
	<h2 style="margin: 0;"><?php print _('Configuration du PDF des devis') ?></h2>
	<div id="pdf-quote-deposits-selector" class="riapicker" style="margin: 1rem 0;">
		<div class="selectorview">
			<div class="left">
				<span class="function_name"><?php print _('Dépôts'); ?></span>
				<br>
				<span class="view">
					<?php print $is_default_config ? _('Configuration par défaut') : $deposits[$selected_deposit]['name']; ?>
				</span>
			</div>
			<a class="btn">
				<img src="/admin/images/stats/fleche.gif" alt="" height="8" width="16">
			</a>
			<div class="clear"></div>
		</div>
		<div class="selector">
			<a data-click="all"><?php print _('Configuration par défaut'); ?></a>
			<?php foreach( $deposits as $deposit ) { ?>
				<a data-click="<?php print $deposit['id']; ?>"><?php print $deposit['name']; ?></a>
			<?php } ?>
		</div>
	</div>
	<form method="post" id="config-pdf-quote" class="config-generation-pdf" enctype="multipart/form-data">
		<div class="config-row">
			<h3><?php print _('Entête') ?></h3>
			<div>
				<div class="notice">
					<p><?php print _('La balise #param[ref]# sera remplacée par la référence de la facture') ?></p>
					<p>
						<span><?php print _('Le lien du site par défaut est : '); ?></span>
						<span class="default-site-url"><?php print $config['site_url']; ?></span>
					</p>
				</div>
				<div class="form-group">
					<label for="devis-name"><?php print _('Nom :') ?></label>
					<input type="text" id="devis-name" name="devis-name" value="<?php print htmlspecialchars($data['pdf_generation_devis_name']); ?>" >
				</div>
				<div class="form-group" style="margin: 1rem 0;">
					<label for="devis-url"><?php print _('Lien du site :'); ?></label>
					<input type="text" id="devis-url" name="devis-url" value="<?php print !empty($data['pdf_generation_devis_url']) ? htmlspecialchars($data['pdf_generation_devis_url']) : ''; ?>">
				</div>
			</div>
			<div class="form-group">
				<label><?php print _('Souhaitez-vous afficher l\'adresse de livraison ?'); ?></label>
				<label for="devis-display-dlv-address-yes">
					<input type="radio" id="devis-display-dlv-address-yes" name="devis-display-dlv-address" value="1" <?php print !empty($data['pdf_generation_devis_display_dlv_address']) ? 'checked' : ''; ?>>
					<?php print _('Oui') ?>
				</label>
				<label for="devis-display-dlv-address-no">
					<input type="radio" id="devis-display-dlv-address-no" name="devis-display-dlv-address" value="0" <?php print empty($data['pdf_generation_devis_display_dlv_address']) ? 'checked' : ''; ?>>
					<?php print _('Non') ?>
				</label>
			</div>
			<h3><?php print _('Logo') ?></h3>
			<div class="row flex-column">
				<div class="notice">
					<?php print _('Les images transparentes ne seront pas prises en compte.'); ?>
				</div>
				<?php if( $data['pdf_generation_devis_logo'] ){ ?>
					<?php $size = $config['img_sizes']['medium']; ?>
					<div id="img<?php print $data['pdf_generation_devis_logo']; ?>" class="row">
						<img src="<?php print $config['img_url']; ?>/<?php print $size['dir']; ?>/<?php print $data['pdf_generation_devis_logo'];?>.jpg" height="<?php print $size['height']; ?>" width="<?php print $size['width']; ?>">
					</div>
				<?php } ?>
				<div class="logo-form-group">
					<label for="devis-logo"><?php print $data['pdf_generation_devis_logo'] ? _('Modifier votre logo :') : _('Choisissez votre logo :'); ?></label>
					<input type="file" id="devis-logo" name="devis-logo">
					<br>
					<br>
					<label><?php print _('Taille du logo (en pixels) :') ?></label>
					<input type="number" name="devis-logo-size-x" value="<?php print htmlspecialchars($data['pdf_generation_devis_logo_size_x']) ?>">
					<span>x</span>
					<input type="number" name="devis-logo-size-y" value="<?php print htmlspecialchars($data['pdf_generation_devis_logo_size_y']) ?>">
				</div>
			</div>
			<br>
			<div class="form-group">
				<label for="devis-logo-disposition"><?php print _('Sélectionnez la disposition du logo parmi les choix suivants :') ?></label>
				<select id="devis-logo-disposition" name="devis-logo-disposition">
					<option value="0" <?php print !$data['pdf_generation_devis_logo_disposition'] ? 'selected' : ''; ?>><?php print _('En haut à droite'); ?></option>
					<option value="1" <?php print $data['pdf_generation_devis_logo_disposition'] ? 'selected' : ''; ?>><?php print _('En haut à gauche'); ?></option>
				</select>
			</div>
			<?php if( $data['pdf_generation_devis_logo'] ){ ?>
				<button type="submit" name="delete-logo"><?php print _('Supprimer le logo'); ?></button>
			<?php } ?>
			<h3><?php print _('Contenu') ?></h3>
			<div class="form-group">
				<label for="devis-font-size"><?php print _('Taille de la police :'); ?></label>
				<select id="devis-font-size" name="devis-font-size" style="width: 145px">
					<option value="8" <?php print $data['pdf_generation_devis_font_size'] == 8 ? 'selected' : ''; ?>>8</option>
					<option value="9" <?php print $data['pdf_generation_devis_font_size'] == 9 ? 'selected' : ''; ?>>9</option>
					<option value="10" <?php print $data['pdf_generation_devis_font_size'] == 10 ? 'selected' : ''; ?>>10</option>
					<option value="11" <?php print $data['pdf_generation_devis_font_size'] == 11 ? 'selected' : ''; ?>>11</option>
					<option value="12" <?php print $data['pdf_generation_devis_font_size'] == 12 ? 'selected' : ''; ?>>12</option>
					<option value="14" <?php print $data['pdf_generation_devis_font_size'] == 14 ? 'selected' : ''; ?>>14</option>
					<option value="16" <?php print $data['pdf_generation_devis_font_size'] == 16 ? 'selected' : ''; ?>>16</option>
					<option value="18" <?php print $data['pdf_generation_devis_font_size'] == 18 ? 'selected' : ''; ?>>18</option>
					<option value="20" <?php print $data['pdf_generation_devis_font_size'] == 20 ? 'selected' : ''; ?>>20</option>
					<option value="24" <?php print $data['pdf_generation_devis_font_size'] == 24 ? 'selected' : ''; ?>>24</option>
				</select>
			</div>
			<br>
			<div class="form-group">
				<label><?php print _('Souhaitez-vous afficher l\'image du produit ?') ?></label>
				<label for="devis-prd-img-yes">
					<input type="radio" id="devis-prd-img-yes" name="devis-prd-img" value="1" <?php print $data['pdf_generation_devis_prd_img'] ? 'checked' : ''; ?>>
					<?php print _('Oui') ?>
				</label>
				<label for="devis-prd-img-no">
					<input type="radio" id="devis-prd-img-no" name="devis-prd-img" value="0" <?php print !$data['pdf_generation_devis_prd_img'] ? 'checked' : ''; ?>>
					<?php print _('Non') ?>
				</label>
			</div>
			<br>
			<div class="form-group">
				<label><?php print _('Souhaitez-vous afficher la remise appliquée au produit ?') ?></label>
				<label for="devis-prd-reduce-yes">
					<input type="radio" id="devis-prd-reduce-yes" name="devis-prd-reduce" value="1" <?php print !empty($data['pdf_generation_devis_prd_reduce']) ? 'checked' : ''; ?>>
					<?php print _('Oui') ?>
				</label>
				<label for="devis-prd-reduce-no">
					<input type="radio" id="devis-prd-reduce-no" name="devis-prd-reduce" value="0" <?php print empty($data['pdf_generation_devis_prd_reduce']) ? 'checked' : ''; ?>>
					<?php print _('Non') ?>
				</label>
			</div>
			<br>
			<div class="form-group">
				<label><?php print _('Souhaitez-vous afficher code barre du produit ?') ?></label>
				<label for="devis-prd-barcode-yes">
					<input type="radio" id="devis-prd-barcode-yes" name="devis-prd-barcode" value="1" <?php print !empty($data['pdf_generation_devis_prd_barcode']) ? 'checked' : ''; ?>>
					<?php print _('Oui') ?>
				</label>
				<label for="devis-prd-barcode-no">
					<input type="radio" id="devis-prd-barcode-no" name="devis-prd-barcode" value="0" <?php print empty($data['pdf_generation_devis_prd_barcode']) ? 'checked' : '' ?>>
					<?php print _('Non') ?>
				</label>
			</div>
			<br>
			<div class="form-group">
				<label><?php print _('Souhaitez-vous tronquer la référence du produit, si celle ci est trop longue ?') ?></label>
				<label for="devis-prd-reftruncated-yes">
					<input type="radio" id="devis-prd-reftruncated-yes" name="devis-prd-reftruncated" value="1" <?php print !empty($data['pdf_generation_devis_prd_reftruncated']) ? 'checked' : ''; ?>>
					<?php print _('Oui') ?>
				</label>
				<label for="devis-prd-reftruncated-no">
					<input type="radio" id="devis-prd-reftruncated-no" name="devis-prd-reftruncated" value="0" <?php print empty($data['pdf_generation_devis_prd_reftruncated']) ? 'checked' : '' ?>>
					<?php print _('Non') ?>
				</label>
			</div>
			<br>
			<div>
				<label><?php print _('Souhaitez-vous afficher un texte en début de devis ?') ?></label>
				<label for="devis-header-yes">
					<input type="radio" id="devis-header-yes" name="devis-header" value="1" <?php print !empty($data['pdf_generation_devis_header']) ? 'checked' : ''; ?>>
					<?php print _('Oui') ?>
				</label>
				<label for="devis-header-no">
					<input type="radio" id="devis-header-no" name="devis-header" value="0" <?php print empty($data['pdf_generation_devis_header']) ? 'checked' : '' ?>>
					<?php print _('Non') ?>
				</label>
				<br>
				<textarea id="devis-header-content" name="devis-header-content" rows="5" cols="100" placeholder="Renseignez ici le texte de début du devis..." <?php print !empty($data['pdf_generation_devis_header']) ? '' : 'style="display:none;"'; ?>><?php print !empty($data['pdf_generation_devis_header_content']) ? trim($data['pdf_generation_devis_header_content']) : ''; ?></textarea>
			</div>
			<br>
			<div>
				<label><?php print _('Souhaitez-vous afficher un texte en fin de devis ?') ?></label>
				<label for="devis-footer-yes">
					<input type="radio" id="devis-footer-yes" name="devis-footer" value="1" <?php print !empty($data['pdf_generation_devis_footer']) ? 'checked' : ''; ?>>
					<?php print _('Oui') ?>
				</label>
				<label for="devis-footer-no">
					<input type="radio" id="devis-footer-no" name="devis-footer" value="0" <?php print empty($data['pdf_generation_devis_footer']) ? 'checked' : '' ?>>
					<?php print _('Non') ?>
				</label>
				<br >
				<textarea id="devis-footer-content" name="devis-footer-content" rows="5" cols="100" placeholder="Renseignez ici le texte de fin du devis..." <?php print !empty($data['pdf_generation_devis_footer']) ? '' : 'style="display:none;"'; ?>><?php print !empty($data['pdf_generation_devis_footer_content']) ? trim($data['pdf_generation_devis_footer_content']) : ''; ?></textarea>
			</div>
			<br>
			<div class="form-group">
				<label><?php print _('Souhaitez-vous afficher le mode de paiement utilisé, s\'il est renseigné ?') ?></label>
				<label for="devis-prd-display-payment-yes">
					<input type="radio" id="devis-prd-display-payment-yes" name="devis-prd-display-payment" value="1" <?php print !empty($data['pdf_generation_devis_display_payment']) ? 'checked' : ''; ?>>
					<?php print _('Oui') ?>
				</label>
				<label for="devis-prd-display-payment-no">
					<input type="radio" id="devis-prd-display-payment-no" name="devis-prd-display-payment" value="0" <?php print empty($data['pdf_generation_devis_display_payment']) ? 'checked' : '' ?>>
					<?php print _('Non') ?>
				</label>
			</div>
			<div class="ria-admin-ui-actions">
				<button type="submit" name="save"><?php print _('Enregistrer'); ?></button>
				<a href="/admin/config/pdf_generation/index.php" class="button">
					<?php print _('Retour'); ?>
				</a>
			</div>
		</div>
	</form>
<?php

require_once('admin/skin/footer.inc.php');