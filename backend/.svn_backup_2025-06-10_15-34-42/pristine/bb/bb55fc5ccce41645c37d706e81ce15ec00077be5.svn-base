<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/monitored_resource.proto

namespace GPBMetadata\Google\Api;

class MonitoredResource
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Label::initOnce();
        \GPBMetadata\Google\Protobuf\Struct::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ac1050a23676f6f676c652f6170692f6d6f6e69746f7265645f7265736f" .
            "757263652e70726f746f120a676f6f676c652e6170691a1c676f6f676c65" .
            "2f70726f746f6275662f7374727563742e70726f746f2291010a1b4d6f6e" .
            "69746f7265645265736f7572636544657363726970746f72120c0a046e61" .
            "6d65180520012809120c0a047479706518012001280912140a0c64697370" .
            "6c61795f6e616d6518022001280912130a0b6465736372697074696f6e18" .
            "0320012809122b0a066c6162656c7318042003280b321b2e676f6f676c65" .
            "2e6170692e4c6162656c44657363726970746f72228b010a114d6f6e6974" .
            "6f7265645265736f75726365120c0a047479706518012001280912390a06" .
            "6c6162656c7318022003280b32292e676f6f676c652e6170692e4d6f6e69" .
            "746f7265645265736f757263652e4c6162656c73456e7472791a2d0a0b4c" .
            "6162656c73456e747279120b0a036b6579180120012809120d0a0576616c" .
            "75651802200128093a02380122ca010a194d6f6e69746f7265645265736f" .
            "757263654d65746164617461122e0a0d73797374656d5f6c6162656c7318" .
            "012001280b32172e676f6f676c652e70726f746f6275662e537472756374" .
            "124a0a0b757365725f6c6162656c7318022003280b32352e676f6f676c65" .
            "2e6170692e4d6f6e69746f7265645265736f757263654d65746164617461" .
            "2e557365724c6162656c73456e7472791a310a0f557365724c6162656c73" .
            "456e747279120b0a036b6579180120012809120d0a0576616c7565180220" .
            "0128093a02380142790a0e636f6d2e676f6f676c652e61706942164d6f6e" .
            "69746f7265645265736f7572636550726f746f50015a43676f6f676c652e" .
            "676f6c616e672e6f72672f67656e70726f746f2f676f6f676c6561706973" .
            "2f6170692f6d6f6e69746f7265647265733b6d6f6e69746f726564726573" .
            "f80101a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

