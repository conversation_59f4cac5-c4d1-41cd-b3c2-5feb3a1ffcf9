<?php

	/**	\file ajax-product-select.php
	 *	Cet écran permet la sélection d'un produit provenant du catalogue. Il dispose d'un moteur de recherche permettant
	 *	de trouver plus facilement le produit recherché.
	 *
	 *	Cette popup est utilisée à différent endroits, comme par exemple :
	 *	- /admin/config/prices/index.php : pour exclure des produits des promotions
	 *	- /admin/catalog/product.php
	 *	- /admin/config/campagnes/birthday.php
	 *	- /admin/views/catalog/linear-raised/tabs/general.php
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	$btn_text = _('Sélectionner');
	$is_ajax = false; 
	$root_cat = 0;
	$current_cat = $multiselect = $current_cat_parent = false;
	$limit_prd = 15;
	$show_search = true; 
	$page = isset( $_GET['p'] ) && is_numeric($_GET['p']) ? $_GET['p'] : 1; 
	$publish_search = !isset($_GET['publish_search']) || (isset($_GET['publish_search']) && $_GET['publish_search']) ? true : false;
	$cnt_publish = isset($_GET['cnt_publish']) && $_GET['cnt_publish'] ? true : (!isset($_GET['cnt_publish']) ? null : false);

	$check_orderable = isset($_GET['orderable']) && $_GET['orderable'] === 'false' ? false : true;

	$add_field = isset($_GET['add_field']) && $_GET['add_field'] ? true : false;
	$wishlist = isset($_GET['wishlist']) && $_GET['wishlist'] ? true : false;
	$is_canonical = isset($_GET['is_canonical']) && $_GET['is_canonical'] ? true : false;

	$action = isset($_GET['action']) ? $_GET['action'] : false;

	if( isset( $_GET['multiselect'] ) && $_GET['multiselect'] ){
		$multiselect = true;
	}
	if( isset( $_GET['btn_text'] ) && $_GET['btn_text'] ){
		$btn_text = urldecode($_GET['btn_text']);
	}
	if( isset( $_GET['cat'] ) && is_numeric( $_GET['cat'] ) ){
		$root_cat = $_GET['cat'];
	}
	if( isset( $_GET['show_search'] ) &&  $_GET['show_search'] == '0'){
		$show_search = false;
	}

	if( $root_cat ){
		$rcat = prd_categories_get( $root_cat );
		if( $rcat && ria_mysql_num_rows($rcat) ) {
			$current_cat = ria_mysql_fetch_array($rcat);

			//retourne le parent 
			$rcat = prd_categories_get( $current_cat['parent_id'] );
			if( $rcat && ria_mysql_num_rows($rcat) ) {
				$current_cat_parent = ria_mysql_fetch_array($rcat);
			}
		}
	}

	// dans le cas d'une recherche
	$search = false; 
	if( isset( $_GET['q'] )  && $_GET['q']!='' ){
		$search = true;
		$rprd = $raprd = search3( 1, $_GET['q'], 1, 0, $cnt_publish!==null ? $cnt_publish : $publish_search, false, 6, array('prd') );
	}

	// nom des champs input qui recevront les valeurs en retour
	$url_other_args = '&amp;publish_search='.$publish_search;
	if( isset($_GET['input_id_prd_id']) && $_GET['input_id_prd_id']!='' ){
		$url_other_args .= '&amp;input_id_prd_id='.urlencode($_GET['input_id_prd_id']);
	}
	if( isset($_GET['input_id_name']) && $_GET['input_id_name']!='' ){
		$url_other_args .= '&amp;input_id_name='.urlencode($_GET['input_id_name']);
	}
	if( isset($_GET['input_id_ref']) && $_GET['input_id_ref']!='' ){
		$url_other_args .= '&amp;input_id_ref='.urlencode($_GET['input_id_ref']);
	}
	if( isset($_GET['input_id_cat_id']) && $_GET['input_id_cat_id']!='' ){
		$url_other_args .= '&amp;input_id_cat_id='.urlencode($_GET['input_id_cat_id']);
	}
	if( $cnt_publish!==null ){
		$url_other_args .= '&amp;cnt_publish='.( $_GET['cnt_publish'] ? 1 : 0 );
	}
	if( isset($_GET['add_field']) && $_GET['add_field']!='' ){
		$url_other_args .= '&amp;add_field='.urlencode($_GET['add_field']);
	}
	if( isset($_GET['wishlist']) && $_GET['wishlist']!='' ){
		$url_other_args .= '&amp;wishlist='.urlencode($_GET['wishlist']);
	}
	$url_other_args .= '&amp;orderable='.($check_orderable ? 'true' : 'false');

	//si requete ajax 
	if( array_key_exists('HTTP_X_REQUESTED_WITH', $_SERVER) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest' ){
		$is_ajax = true;
	}

	if( !$is_ajax ) {
		define('ADMIN_PAGE_TITLE', _('Sélection de produit'));
		define('ADMIN_HEAD_POPUP', true);
		define('ADMIN_ID_BODY', 'popup-content');
		require_once('admin/skin/header.inc.php');
	}

	if( $show_search ){ ?>
		<form id="form-search" action="/admin/ajax/catalog/ajax-product-select.php<?php print '?'.$url_other_args; ?>" method="get">
		<div class="pop-form-search"><?php 	
				// affiche le bouton de retour si on est en résultat de recherche ou dans une catégorie
				if( ($root_cat === 0 && isset( $_GET['q'] )) || $root_cat ){ ?>
					<a name="selectcat" rel="'.$current_cat['parent_id'].'" class="float-left">
						<input type="button" name="filter" id="filter" value="<?php print _('Retour'); ?>" class="btn-cancel" />
					</a><?php
				} ?>
				<input type="text" id="search" name="q" value="<?php print isset( $_GET['q'] ) ? htmlspecialchars($_GET['q']) : '';  ?>"/>
				<input type="submit" class="btn-action" name="search" value="<?php print _('Rechercher'); ?>"/>
			</div>
		</form>
		<?php }
		
			print '	<input type="hidden" name="current_cat" id="current_cat" value="'.(isset($current_cat['id']) ? $current_cat['id'] : 0).'" />
					<input type="hidden" name="add_field" id="add_field" value="'.( $add_field ? 1 : 0 ).'" />
					<input type="hidden" name="wishlist" id="wishlist" value="'.( $wishlist ? 1 : 0 ).'" />';
				
			if( !$search ){
				$publish = $cnt_publish!==null ? $cnt_publish : ($publish_search ? true : false); ?>
				
				<?php
				// affiche les catégories
				print '	<table id="tb-popup-catalogue" class="checklist">
							<caption>';
						if( $root_cat ){
							if( $current_cat_parent ){
								print '<a name="selectcat" rel="'.$current_cat['parent_id'].'"">'.$current_cat_parent['title'].'</a> &gt;&gt; ';
							}
							print $current_cat['title'];
						}
						else echo _("Catalogue")." ". ($is_canonical ? _('(ensemble des produits ne possédant pas de liens canoniques)') : '' );
				print'	</caption>
						<thead>
							<tr>
								<th></th>
								<th>'._('Catégories').'</th>
								<th>'._('Produits').'</th>
							</tr>
						</thead>
						<tbody>';

						$rcat = prd_categories_get( 0, $publish, $root_cat)	;
						if( $rcat && ria_mysql_num_rows($rcat) ){

							while( $cat = ria_mysql_fetch_assoc($rcat) ){
								print '	<tr>
											<td>'.view_cat_is_sync( $cat ).'</td>
											<td><a name="selectcat" rel="'.$cat['id'].'">'.htmlspecialchars( $cat['title'] ).'</a></td>
											<td class="align-right" data-label="'._('Produits : ').'">'.$cat['products'].'</td>
										</tr>';
							}
						}
						else print '<tr><td colspan="3">'._('Aucune catégorie').'</td></tr>';

				print '	</tbody>
					</table>';

			}

			// Affiche les produits
			if( $root_cat ){
				if( $is_canonical ){
					$raprd = prd_products_get_simple( 0, '', $publish, $root_cat, false, false, false, false, array('childs'=>true,'can_be_canonical'=>1) );
					$rprd = prd_products_get_simple( 0, '', $publish, $root_cat, false, false, true, false, array( 'childs'=>true,'can_be_canonical'=>1, 'rowstart' => $page > 1 ? ($page-1) * $limit_prd : 0, 'maxrows' => $limit_prd) );
				}else{
					$raprd = prd_products_get_simple( 0, '', $publish, $root_cat, false, false, false, false, array('childs'=>true) );
					$rprd = prd_products_get_simple( 0, '', $publish, $root_cat, false, false, true, false, array( 'childs'=>true, 'rowstart' => $page > 1 ? ($page-1) * $limit_prd : 0, 'maxrows' => $limit_prd) );
				}
				$search = false;
			}

			if( isset( $rprd, $raprd ) && ( $search || ria_mysql_num_rows($rprd) ) ){
				$nb_prd = $raprd ? ria_mysql_num_rows($raprd) : 0;
				$pages = ceil( $nb_prd / $limit_prd ); 
				print '	<table class="lst-prd-table checklist">
							<caption>'.sprintf(_('%d produit(s)'), $nb_prd).'</caption>';
				if( !$multiselect ){
					print '
							<thead>
								<tr>
									<th class="col20px"></th>
									<th class="col80px"><abbr title="'._('Référence').'">'._('Ref').'</abbr></th>
									<th>'._('Désignation').'</th>
									<th class="col100px align-right">'._('Prix HT').'</th>
									<th class="col100px align-right">'._('Prix TTC').'</th>
									<th class="col130px"></th>
								</tr>
							</thead>';
				}else{
					print '
							<thead>
								<tr>
									<th class="col20px"><input type="checkbox" onclick="checkAllClick(this)" class="checkbox"></th>
									<th class="col20px"></th>
									<th class="col80px"><abbr title="'._('Référence').'">'._('Ref').'</abbr></th>
									<th>'._('Désignation').'</th>
									<th class="col100px align-right">'._('Prix HT').'</th>
									<th class="col100px align-right">'._('Prix TTC').'</th>
								</tr>
							</thead>';
				}
				print '
						<tbody>';
						if( $nb_prd <= 0 ){
							print '<tr><td colspan="6">'._('Aucun résultat').'</td></tr>';
						}else{
							$shown_products = array();
							$cpt = 0;
							while( $prd = ria_mysql_fetch_array($rprd) ){
								
								// Evite qu'un même produit apparaisse deux fois dans la liste
								if ($search) {
									if( array_search( $prd['tag'], $shown_products )!==false ){
										continue;
									}
									$shown_products[] = $prd['tag'];
								}else{
									if( array_search( $prd['id'], $shown_products )!==false ){
										continue;
									}
									$shown_products[] = $prd['id'];
								}
								
								$cpt ++;	
								if( $search ){
									// la pagination ne marche pas dans la fct search
									if( $cpt < (($page-1) * $limit_prd) ) continue;
									if( $cpt > ($page * $limit_prd) ) break;
									
									if( $is_canonical ){
										$sprd = prd_products_get_simple( $prd['tag'], '', false, 0, true, false, true, false, array('can_be_canonical'=>1) );
									}else{
										$sprd = prd_products_get_simple( $prd['tag'], '', false, 0, true, false, true, false );
									}
									if( ria_mysql_num_rows($sprd) ){
										$prd = ria_mysql_fetch_array( $sprd );
									}
								}
								
								$small = $config['img_sizes']['small']; 
								if( $prd['img_id'] && trim($prd['img_id'])!='' && is_numeric($prd['img_id']) && $prd['img_id'] > 0 ){
									$url_img = $config['img_url'].'/'.$small['dir'].'/'.$prd['img_id'].'.'.$small['format'];
								}else{
									$url_img = view_admin_get_img_default('small');
								}
								print '	<tr>
											'.( $multiselect  ? '<td>'.($prd['orderable'] || !$check_orderable ?'<input type="checkbox" class="select-prd" name="prd[]" value="'.$prd['id'].'"/>':'').'</td>' : '' ).'
											<td>'.view_prd_is_sync($prd).'</td>
											<td data-label="'._('Référence : ').'">'.htmlspecialchars( $prd['ref'] );
												print '	
												<div class="prd-more">
													<img src="'.$url_img.'" height="'.$small['height'].'" width="'.$small['width'].'" alt="'.htmlspecialchars( $prd['name'] ).'"/>
												</div>
											</td>
											<td>'.htmlspecialchars( $prd['name'] ).'</td>
											<td class="align-right" data-label="'._('Prix HT : ').'">'.( isset($prd['price_ht']) ? number_format($prd['price_ht'],2,',',' ').'€' : '' ).'</td>
											<td class="align-right" data-label="'._('Prix TTC : ').'">'.( isset($prd['price_ht']) ? number_format($prd['price_ttc'],2,',',' ').'€' : '' ).'</td>
											'.( $multiselect ? '' : '<td class="align-center"><a name="selectprd" class="button" data-ref="'.htmlspecialchars($prd['ref']).'" data-name="'.htmlspecialchars($prd['name']).'" rel="'.$prd['id'].'">'.htmlspecialchars($btn_text).'</a></td>').'
										</tr>';
							}
						}
				print '
					</tbody>	
					<tfoot>';
						
			if( $pages>1 ){
				print '<tr id="pagination">
						<td colspan="3" class="page tdleft">Page '.$page.'/'.$pages.'</td>
						<td colspan="3" class="pages">';
						if( $pages > 1 ){
							if( $page-1 > 0 ) print '<a class="prev" name="'.( $search ? 'selectsearch" data-search="'.urlencode($_GET['q']).'"' : 'selectcat" rel="'.$current_cat['id'].'"').'  data-page="'.($page-1).'">« '._('Préc').'</a>&nbsp;|&nbsp;';
							$range = 5;
							for( $i= ( $page-$range < 1 ? 1 : $page-$range) ; $i<=( $page+$range > $pages ? $pages : $page+$range); $i++ ){
								if( $i==$page ){
									print '<b>'.$page.'</b>';
								}else{
									print '<a name="'.( $search ? 'selectsearch" data-search="'.urlencode($_GET['q']).'"' : 'selectcat" rel="'.$current_cat['id'].'"').'  data-page="'.$i.'">'.$i.'</a>'; 
								}
								if( $i<$pages ){
									print ' | ';
								}
							}
							if( $page+1 <= $pages ) print '&nbsp;|&nbsp;<a class="next" name="'.( $search ? 'selectsearch" data-search="'.urlencode($_GET['q']).'"' : 'selectcat" rel="'.$current_cat['id'].'"').'  data-page="'.($page+1).'">'._('Suiv').' »</a>';
						}
					print '</td>
					</tr>';
		}
		if( $multiselect ){
			print '	<tr>
						<td colspan="6" class="tdleft"><input type="button" class="selectprd" name="add" value="'.htmlspecialchars($btn_text).'"/></td>
					</tr>';
		}
		print '
				</tfoot>
						</table>';
			}
		

		?>

		<div class="pop-form-cancel">
			<input class="btn-action cancel" type="submit" onclick="window.parent.hidePopup();" value="<?php print _('Annuler'); ?>" title="<?php print _('Fermer cette fenêtre') ?>" />
		</div>
	
<?php 
	if( !$is_ajax ){
?>
		<script>
			function prd_select_init(){
				$('.lst-prd-table tr').hover(function(){
					$('.prd-more', this ).show();
					$('td', this ).addClass('odd');
				}, function(){
					$('.prd-more', this ).hide();
					$('td', this ).removeClass('odd');
				});			
			}
			
			$('document').ready(function(){
			
				prd_select_init();
				
				var current_ajax_request = false; 
				
				$(document).on('click','a[name=selectcat]', function(){
					page = 1; 
					if( $(this).attr('data-page') ) page = $(this).attr('data-page'); 
	
					$.get( '/admin/ajax/catalog/ajax-product-select.php?show_search=<?php print $show_search ? 1 : 0 ?>&publish_search=<?php print $publish_search ? 1 : 0 ?>&multiselect=<?php print $multiselect ? 1:0 ?>&btn_text=<?php print urlencode($btn_text); ?>&cat='+$(this).attr('rel')+'&p='+page+'<?php print str_replace('&amp;', '&', $url_other_args); ?>&is_canonical=<?php print $is_canonical ? 1 : 0 ?>', function(html){
						$('#popup-content').html(html);
						
						$('.lst-prd-table .prd-more img').bind('error', function(){
							$(this).parent().html("<?php print '<img src=\"/admin/images/default.jpg\" height=\"80\" width=\"80\" alt=\"image indisponible\" />'; ?>");
						});
						prd_select_init();
						
					});
				});

				$('a[name=selectsearch]').live('click', function(){
					page = 1; 
					if( $(this).attr('data-page') ) page = $(this).attr('data-page'); 

					$.get( '/admin/ajax/catalog/ajax-product-select.php?show_search=<?php print $show_search ? 1 : 0 ?>&publish_search=<?php print $publish_search ? 1 : 0 ?>&multiselect=<?php print $multiselect ? 1:0 ?>&btn_text=<?php print urlencode($btn_text); ?>&q='+$(this).attr('data-search')+'&p='+page+'<?php print str_replace('&amp;', '&', $url_other_args); ?>', function(html){
						$('#popup-content').html(html);
						
						prd_select_init();
					});
				});

				$('a[name=selectprd]').live('click', function(){
					var currentCat = $('#current_cat').length && $('#current_cat').val()!=0 ? $('#current_cat').val() : 0;
					<?php
					$return_url_args = '';
					if( isset($_GET['input_id_prd_id']) && $_GET['input_id_prd_id']!='' ){
						$return_url_args .= ', \''.$_GET['input_id_prd_id'].'\'';
					}else{
						$return_url_args .= ', \'\'';
					}
					if( isset($_GET['input_id_name']) && $_GET['input_id_name']!='' ){
						$return_url_args .= ', \''.$_GET['input_id_name'].'\'';
					}else{
						$return_url_args .= ', \'\'';
					}
					if( isset($_GET['input_id_ref']) && $_GET['input_id_ref']!='' ){
						$return_url_args .= ', \''.$_GET['input_id_ref'].'\'';
					}else{
						$return_url_args .= ', \'\'';
					}
					if( isset($_GET['input_id_cat_id']) && $_GET['input_id_cat_id']!='' ){
						$return_url_args .= ', \''.$_GET['input_id_cat_id'].'\'';
					}else{
						$return_url_args .= ', \'\'';
					}
					?>

					if( $('#add_field').val() == 1 ){
						window.parent.addObjectInField($(this).attr('rel'), $(this).attr('data-name')<?php print $return_url_args; ?>);
					}else if( $('#wishlist').val() == 1 ){
						window.parent.wishlist_select_prd( $(this).attr('rel') );
					}else if( typeof window.parent.parent_select_prd !== "undefined" ){
						window.parent.parent_select_prd( $(this).attr('rel'), $(this).attr('data-name'), $(this).attr('data-ref'), currentCat<?php print $return_url_args; ?> );
					}

					<?php if( !$multiselect ){ ?>
						window.parent.hidePopup();
					<?php } ?>
				});
				$('input.selectprd').live('click', function(){
					data = new Array();
					$('.select-prd:checked').each(function(){
						data.push('prd_add[]='+$(this).val() );
					});
					if (typeof window.parent.parent_select_prds !== "undefined") {
						window.parent.parent_select_prds( data.join('&')<?php print ', \''.$action.'\''; ?> );
					}
					<?php if( !$multiselect ){ ?>
						window.parent.hidePopup();
					<?php } ?>
				});

				$('#form-search').live('submit', function(){

					page = 1; 

					$.get( '/admin/ajax/catalog/ajax-product-select.php?show_search=<?php print $show_search ? 1 : 0 ?>&publish_search=<?php print $publish_search ? 1 : 0 ?>&multiselect=<?php print $multiselect ? 1:0 ?>&btn_text=<?php print urlencode($btn_text); ?>&q='+$('#search').val()+'&p='+page+'<?php print str_replace('&amp;', '&', $url_other_args); ?>', function(html){
						$('#popup-content').html(html);
						prd_select_init();
					});

					return false;
				});

				

			});
		</script>
<?php
		require_once('admin/skin/footer.inc.php'); 
	}
?>