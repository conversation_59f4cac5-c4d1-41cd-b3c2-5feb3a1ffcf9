<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/billing.proto

namespace Google\Api;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Billing\BillingDestination instead.
     * @deprecated
     */
    class Billing_BillingDestination {}
}
class_exists(Billing\BillingDestination::class);
@trigger_error('Google\Api\Billing_BillingDestination is deprecated and will be removed in the next major release. Use Google\Api\Billing\BillingDestination instead', E_USER_DEPRECATED);

