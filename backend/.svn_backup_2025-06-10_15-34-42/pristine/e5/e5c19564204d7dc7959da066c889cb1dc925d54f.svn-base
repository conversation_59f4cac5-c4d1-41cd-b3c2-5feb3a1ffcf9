
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: SimpleSAMLphp 1.15\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2016-10-12 09:23+0200\n"
"PO-Revision-Date: 2016-10-14 12:14+0200\n"
"Last-Translator: \n"
"Language: zh_Hant_TW\n"
"Language-Team: \n"
"Plural-Forms: nplurals=1; plural=0\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.3.4\n"

msgid "{memcacheMonitor:memcachestat:total_connections}"
msgstr "總連線數"

msgid "{memcacheMonitor:memcachestat:version}"
msgstr "版本"

msgid "{memcacheMonitor:memcachestat:get_hits}"
msgstr "GET 指令總數(成功)"

msgid "{memcacheMonitor:memcachestat:curr_items}"
msgstr "目前項目的數量"

msgid "{memcacheMonitor:memcachestat:uptime}"
msgstr "已運作時間"

msgid "{memcacheMonitor:memcachestat:limit_maxbytes}"
msgstr "儲存空間總利用數"

msgid "{memcacheMonitor:memcachestat:curr_connections}"
msgstr "目前開啟的連線數"

msgid "{memcacheMonitor:memcachestat:rusage_system}"
msgstr "CPU 使用量(系統)"

msgid "{memcacheMonitor:memcachestat:rusage_user}"
msgstr "CPU 使用量(使用者)"

msgid "{memcacheMonitor:memcachestat:pid}"
msgstr "程序 ID"

msgid "{memcacheMonitor:memcachestat:cmd_get}"
msgstr "GET 指令總數"

msgid "{memcacheMonitor:memcachestat:bytes_read}"
msgstr "傳送至主機總位元組"

msgid "{memcacheMonitor:memcachestat:time}"
msgstr "目前時間"

msgid "{memcacheMonitor:memcachestat:get_misses}"
msgstr "GET 指令總數(成功)"

msgid "{memcacheMonitor:memcachestat:bytes_written}"
msgstr "寫入至主機總位元組"

msgid "{memcacheMonitor:memcachestat:connection_structures}"
msgstr "連線結構"

msgid "{memcacheMonitor:memcachestat:cmd_set}"
msgstr "SET 指令總數"

msgid "{memcacheMonitor:memcachestat:total_items}"
msgstr "所有項目"

msgid "{memcacheMonitor:memcachestat:bytes}"
msgstr "目前使用的總位元組"

msgid "Current time"
msgstr "目前時間"

msgid "Total items ever"
msgstr "所有項目"

msgid "Bytes written by the server"
msgstr "寫入至主機總位元組"

msgid "Uptime"
msgstr "已運作時間"

msgid "Current open connections"
msgstr "目前開啟的連線數"

msgid "Total storage avail"
msgstr "儲存空間總利用數"

msgid "Version"
msgstr "版本"

msgid "Total GET commands (failed)"
msgstr "GET 指令總數(成功)"

msgid "Total SET commands"
msgstr "SET 指令總數"

msgid "Connection structures"
msgstr "連線結構"

msgid "Total GET commands (success)"
msgstr "GET 指令總數(成功)"

msgid "Total bytes in use currently"
msgstr "目前使用的總位元組"

msgid "Total GET commands"
msgstr "GET 指令總數"

msgid "Bytes in to the server"
msgstr "傳送至主機總位元組"

msgid "Process ID"
msgstr "程序 ID"

msgid "Currently number of items"
msgstr "目前項目的數量"

msgid "CPU Seconds (User)"
msgstr "CPU 使用量(使用者)"

msgid "CPU Seconds (System)"
msgstr "CPU 使用量(系統)"

msgid "Total connections"
msgstr "總連線數"

