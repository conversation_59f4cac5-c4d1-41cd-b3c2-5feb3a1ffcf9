<?php
/**
 * \defgroup api-riashopsync-need_sync Nouvelle donnée à synchroniser
 * \ingroup sync
 * @{
 * \page api-riashopsync-need_sync-get Chargement
 *
 *	 Cette fonction récupère les éléments de la configuration ( Ria > Gestion commerciale )
 *
 *		\code
 *			GET /riashopsync/need_sync/
 *		\endcode
 *
 *	 @return Liste de variable de configuration avec les colonnes :
 *	\code{.json}
 *       {
 *               "id": Identifiant ,
 *               "date": Date de la configuration,
 *               "sync_past": est synchronisé ou non SS
 *       },
 *	\endcode
 * @}
*/

require_once('ord.returns.inc.php');

switch( $method ){
	case 'get':

		$result = true;

		$_REQUEST['cls_id'] = isset($_REQUEST['cls_id']) ? $_REQUEST['cls_id'] : array();

		$needed_cls = !is_array($_REQUEST['cls_id']) ? array($_REQUEST['cls_id']) : $_REQUEST['cls_id'];

		$cls_id = array(CLS_ORDER, CLS_BL, CLS_RETURN, CLS_REPORT, CLS_USER, CLS_ADDRESS, CLS_DEVIS);
		if( sizeof($needed_cls)==0 ){
			$needed_cls = $cls_id;
		}
		foreach( $cls_id as $cls){
			if( in_array($cls, $needed_cls) ){
				$content[$cls] = array();
			}
		}

		if( in_array(CLS_ORDER, $needed_cls) ){

			$wst_id = false;
			if( $config['tnt_id'] == 3 ){
				$wst_id = array(7,81);
			}

			if( $orders = ord_orders_get_new_to_import($wst_id) ){
				while( $ord = ria_mysql_fetch_assoc($orders) ){
					$content[CLS_ORDER][] = array('id' => $ord['id'], 'date' => $ord['date'], 'sync_notes' => $ord['sync_notes']);
				}
			}
		}


		if( in_array(CLS_BL, $needed_cls) ){
			if( $bls = ord_bls_get_new_to_import() ){
				while( $bl = ria_mysql_fetch_assoc($bls) ){
					$content[CLS_BL][] = array('id' => $bl['id'], 'date' => $bl['date']);
				}
			}
		}

		if( in_array(CLS_DEVIS, $needed_cls) ){
			if( $quotes = ord_orders_get_need_sync( 'head', _STATE_DEVIS ) ){
				$content[CLS_DEVIS] = $quotes;
			}
		}


		if( in_array(CLS_RETURN, $needed_cls) ){
			if( $returns = ord_returns_get_new_to_import() ){
				while( $return = ria_mysql_fetch_assoc($returns) ){
					$content[CLS_RETURN][] = array('id' => $return['id'], 'date' => $return['date']);
				}
			}
		}

		if( in_array(CLS_REPORT, $needed_cls) ){
			if( $reports = rp_reports_get(0, 0, 0, 0, null, null, null, null, true) ){
				while( $report = ria_mysql_fetch_assoc($reports) ){
					$content[CLS_REPORT][] = array('id' => $report['id'], 'date' => $report['date_created_en']);
				}
			}
		}
		if( in_array(CLS_ADDRESS, $needed_cls) ){
			if( $adrs = gu_adresses_get_updated() ){
				while( $adr = ria_mysql_fetch_assoc($adrs) ){
					$content[CLS_ADDRESS][] = array('id' => $adr['id'], 'date' => $adr['date_modified']);
				}
			}
		}

		if( in_array(CLS_USER, $needed_cls) ){

			$params_for_users = false;
			if( $config['tnt_id'] == 16 ){
				$params_for_users = 1916; // fidélité
			}elseif( $config['tnt_id'] == 13 ){
				$params_for_users = array(2819 => 'Oui'); // import unique
			}

			if( $users = gu_users_toimport_get( $params_for_users ) ){
				while( $user = ria_mysql_fetch_assoc($users) ){
					$content[CLS_USER][] = array('id' => $user['id'], 'date' => $user['date_modified']);
				}
			}
		}

		break;
}
