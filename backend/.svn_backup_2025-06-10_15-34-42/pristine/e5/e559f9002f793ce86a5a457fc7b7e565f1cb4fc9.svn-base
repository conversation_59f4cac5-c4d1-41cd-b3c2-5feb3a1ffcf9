<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/pubsub.proto

namespace Google\Cloud\PubSub\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Response for the `Pull` method.
 *
 * Generated from protobuf message <code>google.pubsub.v1.PullResponse</code>
 */
class PullResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Received Pub/Sub messages. The list will be empty if there are no more
     * messages available in the backlog. For JSON, the response can be entirely
     * empty. The Pub/Sub system may return fewer than the `maxMessages` requested
     * even if there are more messages available in the backlog.
     *
     * Generated from protobuf field <code>repeated .google.pubsub.v1.ReceivedMessage received_messages = 1;</code>
     */
    private $received_messages;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Cloud\PubSub\V1\ReceivedMessage[]|\Google\Protobuf\Internal\RepeatedField $received_messages
     *           Received Pub/Sub messages. The list will be empty if there are no more
     *           messages available in the backlog. For JSON, the response can be entirely
     *           empty. The Pub/Sub system may return fewer than the `maxMessages` requested
     *           even if there are more messages available in the backlog.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Pubsub\V1\Pubsub::initOnce();
        parent::__construct($data);
    }

    /**
     * Received Pub/Sub messages. The list will be empty if there are no more
     * messages available in the backlog. For JSON, the response can be entirely
     * empty. The Pub/Sub system may return fewer than the `maxMessages` requested
     * even if there are more messages available in the backlog.
     *
     * Generated from protobuf field <code>repeated .google.pubsub.v1.ReceivedMessage received_messages = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getReceivedMessages()
    {
        return $this->received_messages;
    }

    /**
     * Received Pub/Sub messages. The list will be empty if there are no more
     * messages available in the backlog. For JSON, the response can be entirely
     * empty. The Pub/Sub system may return fewer than the `maxMessages` requested
     * even if there are more messages available in the backlog.
     *
     * Generated from protobuf field <code>repeated .google.pubsub.v1.ReceivedMessage received_messages = 1;</code>
     * @param \Google\Cloud\PubSub\V1\ReceivedMessage[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setReceivedMessages($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\PubSub\V1\ReceivedMessage::class);
        $this->received_messages = $arr;

        return $this;
    }

}

