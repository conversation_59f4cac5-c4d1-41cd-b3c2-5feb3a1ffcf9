<?php
/**
 * \defgroup ovh Api SMS d'Ovh
 * \ingroup sms_api
 * ce module comporte la gestion de l'Api Ovh
 * @{
*/
require_once('Marketing/partners/SmsInterface.inc.php');
require_once('Marketing/partners/Ovh/Ovh.inc.php');

/**
 * \class SmsOvh
 * \brief Cette classe permet la gestion du service Sms de l'Api Ovh
*/
class SmsOvh implements SmsInterface
{
	private $receiver;
	private $sender;
	private $isMarketing;
	private $deliveryDate;
	private $connection;

	/** Permet générer une connexion avec l'api d'ovh
	 * \param  $api tableau associatif avec les paramètre de l'api
	 * \return une exception si échec
	 */
	public function genConnection( array $api ){
		if( !ria_array_key_exists(array('application_key','application_secret','consumer_key','endpoint'), $api) ){
			throw new Exception('Not all the parameters are entered');
		}
		try{
			$this->connection = new Ovh(
				$api['application_key'],
				$api['application_secret'],
				$api['endpoint'],
				$api['consumer_key']
			);
		}
		catch(Exception $e){
			throw new Exception($e->getMessage());
		}
	}

	public function getConnection(){
		return $this->connection;
	}

	/** Vérifi si le numéro mobile est au format international français
	 * \param  $phoneNumber Numéro mobile a vérifier
	 * \return  true si le numéro est au bon format, false si non.
	 */
	public function checkIsInternational( $phoneNumber ){
		return preg_match("/^(\+|00)[1-9][0-9]{9,16}$/", $phoneNumber) != 1;
	}

	/** Permet d'initialiser un destinataire
	 * \param $receiver un numéro mobile au format international
	 */
	public function setReceiver( $receiver ){
		if ( $this->checkIsInternational($receiver) ) {
			throw new Exception("Receiver parameter must be a valid international phone number");
		}

		$receiver = preg_replace("/^00/", "+", $receiver);

		$this->receiver = array($receiver);
		return true;
	}
	/**
	 * Set the delivery date of the message
	 *
	 * @param dateTime $dateTime date when the message should be sent
	 *
	 * @return void
	 */
	public function setDeliveryDate($dateTime)
	{
		if (!isset($dateTime)) {
			throw new Exception("Date parameter is empty");
		}
		if (!is_a($dateTime, "DateTime")) {
			throw new Exception("Date parameter must be a DateTime object");
		}
		
		$now = new \DateTime('now');
		if ($now > $dateTime) {
			throw new Exception("Date parameter can't be in the past");
		} elseif ($now == $dateTime) {
			$dateTime = null;
		}
		
		$this->deliveryDate = $dateTime;
	}
	/**
	 * Set the marketing information of the message
	 * If the message is flaged as marketing, a "STOP" mention will be added
	 * at the end of the message. Marketing messages will also be delayed
	 * to the next open day if sent by night (22h - 8h) or the weekend.
	 *
	 * @param bool $isMarketing marketing flag of the message
	 *
	 * @return void
	 */
	public function setIsMarketing($isMarketing)
	{
		$this->isMarketing = $isMarketing;
	}

	/** Cette fonction permet de récuper l'uri utilisé pour fair des demande a l'api d'ohv
	 * TODO changer le chargement du compte pour prendre appartir d'un champs avancé ou bien celui avec des credits
	 * \return la chaine de caractère URI
	 */
	public function getUri(){
		/* $account = $this->getAccounts();
		if( !isset($account[0]) ){
			throw new Exception("Aucun compte n'a été retrouver");
		} */
		$uri = '/sms/sms-ms11230-1/';
		return $uri;
	}

	/** Permet d'envoyer le sms en passant un message
	 * \param  $message le message a envoyer
	 * \return Retourne la réponse de l'Api
	 *         si échec retourne des exceptions
	 */
	public function send( $message ){
		if( trim($message) === '' ){
			throw new Exception("You need to pass a message");
		}
		if( !isset($this->receiver) ){
			throw new Exception("You need to set a receiver");
		}
		// Manage differed delivery
		$differedPeriod = 0;
		if (!is_null($this->deliveryDate)) {
			$now = new DateTime('now');
			if ($now > $this->deliveryDate) {
				throw new Exception("Delivery date parameter can't be in the past");
			}
			
			$timeBetween = $this->deliveryDate->diff($now);
			
			$differedPeriod += ($timeBetween->days * 24 * 60) + ($timeBetween->h * 60) + ($timeBetween->i);
		}
		if( !isset($this->isMarketing) ){
			$this->isMarketing = true;
		}
		// Manage coding
		$coding = ($this->is_gsm0338($message) ? '7bit' : '8bit');
		
		// Prepare request parameters
		$parameters = (object) array('message' => $message, 'receivers' => $this->receiver, 'noStopClause' => !$this->isMarketing, 'differedPeriod' => $differedPeriod, 'coding' => $coding);
		
		// Manage sender
		if ($this->sender) {
			$parameters->sender = $this->sender;
		} else {
			$parameters->senderForResponse = true;
		}

		$response = ($this->connection->post($this->getUri() . "jobs", $parameters));

		if( isset($response['message']) ){
			throw new Exception($response['message']);
		}
		elseif( count($response['validReceivers']) == 0 || $response['totalCreditsRemoved'] == 0 || count($response['invalidReceivers']) > 0){
			mail('<EMAIL>', 'response send SMS', print_r( $response, true));
			throw new Exception("invalidReceivers");
		}

		return $response;
	}

	/** Get all SMS accounts
	 *
	 * \param string $details Get accounts details or not
	 *
	 * \return array
	 */
	public function getAccounts( $details = false ){
		$accounts = $this->connection->get("/sms/");

		if ($details) {
			foreach ($accounts as $id => $account) {
				$accounts[$id] = $this->getAccountDetails($account);
			}
		}

		return $accounts;
	}

	/** Get details for an account
	 *
	 * \param string $account Account to get details
	 *
	 * \return mixed
	 */
	public function getAccountDetails( $account ){
		return $this->connection->get("/sms/$account");
	}


	/** Cette fonction permet de supprimer un sms avec un identifiant
	 *	\param $sms_id Identifiant du sms a supprimer
	 *
	 * return null
	 */
	public function delSMS($sms_id){
		return $this->connection->delete($this->getUri().'jobs/'.$sms_id);
	}
	/** Fonction interne pour paramétrer le codage du message 7 ou 8 bit
	 * \param  Le message au format utf-8
	 * \return retourn true pour 7bit et false pour 8bit
	 */
	private function is_gsm0338( $utf8_string ){

		$gsm0338 = array(
			'@','Δ',' ','0','¡','P','¿','p',
			'£','_','!','1','A','Q','a','q',
			'$','Φ','"','2','B','R','b','r',
			'¥','Γ','#','3','C','S','c','s',
			'è','Λ','¤','4','D','T','d','t',
			'é','Ω','%','5','E','U','e','u',
			'ù','Π','&','6','F','V','f','v',
			'ì','Ψ','\'','7','G','W','g','w',
			'ò','Σ','(','8','H','X','h','x',
			'Ç','Θ',')','9','I','Y','i','y',
			"\n",'Ξ','*',':','J','Z','j','z',
			'Ø',"\x1B",'+',';','K','Ä','k','ä',
			'ø','Æ',',','<','L','Ö','l','ö',
			"\r",'æ','-','=','M','Ñ','m','ñ',
			'Å','ß','.','>','N','Ü','n','ü',
			'å','É','/','?','O','§','o','à'
		);

		$len = mb_strlen($utf8_string, 'UTF-8');

		for( $i=0; $i < $len; $i++) {
			if (!in_array(mb_substr($utf8_string,$i,1,'UTF-8'), $gsm0338)) {
				return false;
			}
		}

		return true;
	}
}
/// @}