<?php
	/** \file workqueue-priceminister.php
	 *	\ingroup crontabs priceminister
	 * 	Ce script est destiné à traiter les tâches contenues dans la file d'attente PriceMinister.
	 *	Les tâches en question sont des demandes de création de produit, de mise à jour ou de suppression.
	 *	Il est lancé automatiquement deux fois par heure aux minutes 29 et 59.
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('comparators.inc.php');
	require_once('comparators/ctr.priceminister.inc.php');
	require_once('tsk.comparators.inc.php');
	require_once('prices.inc.php');

	$mode_test = isset($ar_params['test']) && $ar_params['test'];

	foreach( $configs as $config ){
		// Vérifie que la place de marché est bien activée pour ce client.
		// Dans le cas contraire, passe au client suivant.
		if( !ctr_comparators_actived(CTR_PRICEMINISTER) ){
			continue;
		}

		// Récupère les tâches terminées
		$ar_import = array();
		$rimport = tsk_comparators_get( CTR_PRICEMINISTER, true );
		if( $rimport && ria_mysql_num_rows($rimport) ){
			while( $import = ria_mysql_fetch_array($rimport) ){
				$ar_import[] = $import['import_id'];
			}
		}

		// Supprime les tâches terminées si elles se sont correctement déroulées, sinon envoi un mail avec les erreurs
		$import = array_unique( $ar_import );
		if( is_array($import) && sizeof($import) ){
			foreach( $import as $imp ){
				$result = ctr_priceminister_get_result_import( $imp );

				if( is_array($result) && sizeof($result) ){
					// Module RiaShoppping plus suivi, plus d'envoi de message
				}

				tsk_comparators_del_byimport( $imp );
			}
		}

		// Récupère la liste des taches à traiter
		$rtsk = tsk_comparators_get( CTR_PRICEMINISTER );
		if( !$rtsk || !ria_mysql_num_rows($rtsk) ){
			continue;
		}

		$xml = false;
		$message = 1;
		$ar_exec = array(); $ar_no_exec = array();
		while( $tsk = ria_mysql_fetch_array($rtsk) ){
			$temp_xml = '';

			switch( $tsk['action'] ){
				case 'add' : // exporte le produit vers PriceMinister
				case 'update' : // mise à jour d'un produit vers PriceMinister
					$temp_xml = ctr_priceminister_add_xml( $tsk['prd_id'], $mode_test );
					break;
				case 'delete' : // suppression d'un produit vers PriceMinister
					$temp_xml = ctr_priceminister_delete_xml( $tsk['prd_id'] );
					break;
				default :
					$temp_xml = 'noaction';
					break;
			}

			usleep( 250000 );
			if( trim($temp_xml)!='' && $temp_xml!='noaction' ){
				$xml .= $temp_xml;
				$ar_exec[] = $tsk['id'];
			} elseif( $temp_xml!='noaction' ) {
				$ar_no_exec[] = $tsk['id'];
			}
		}

		$xml = ctr_priceminister_get_xml( $xml );

		// envoi d'un mail lors de l'échec d'une ou plusieurs actions
		if( is_array($ar_no_exec) && sizeof($ar_no_exec) ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
		}

		// si on est pas en mode test, on envoi les messages à PriceMinister
		if( !$mode_test ){
			if( trim($xml)!='' ){
				if( !($itemid = ctr_priceminister_xml_send($xml)) ){
					// Module RiaShoppping plus suivi, plus d'envoi de message
				} else {
					// mise à jour de la date d'exécution des tâches
					tsk_comparators_set_completed( $ar_exec );

					// mise à jour de l'identifiant d'import pour toutes les tâches consernées par l'envoi
					tsk_comparators_set_import_id( $ar_exec, $itemid );
				}
			}
		} else {
			print $xml."\n";
		}
	}