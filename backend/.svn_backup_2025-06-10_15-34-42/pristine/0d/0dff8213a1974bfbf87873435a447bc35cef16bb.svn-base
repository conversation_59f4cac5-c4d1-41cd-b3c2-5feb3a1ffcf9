<?php
	/** \file export-google-shopping.php
	 *
	 *
	 * 	Ce script est destiné à mettre à jour les fichiers d'export Google Shopping
	 *	des clients ayant activé cette option. Il est lancé chaque jour par une tâche planifiée.
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once( 'comparators.inc.php' );

	foreach( $configs as $config ){
		Sitemap::resetClassLists();
		if( $config['tnt_id'] == 26 ){
			include($config['site_dir'].'/config.inc.php');
			require_once($config['site_dir'].'/include/view.cart.inc.php');
		}

		if( $config['tnt_id'] == 4 ){
			$config['fld_prd_col_ldd'] = 494;
		}

		$start = microtime(true);

		// lien utilisé pour la création du fichier d'export du catalogue
		$dirname = $config['ctr_dir'].'/'.md5( $config['tnt_id'].$config['date-created'] ).'/';
		$file = $dirname.'tmp-google_shopping.xml';
		$url_file = $config['site_url'].'/shopbots/'.md5( $config['tnt_id'].$config['date-created'] ).'/google_shopping.xml';

		// Vérifie que le comparateur Google Shopping est bien activé pour ce client.
		if( !ctr_comparators_actived(CTR_GOOGLE) ){
			if( file_exists($file) ){
				unlink( $file );
			}

			continue;
		}

		// On supprime les catégories de google shopping qui n'existent plus
		ctr_catalogs_verify(CTR_GOOGLE);

		// si le dossier contenant les fichiers n'existe pas, on le créé avec les droits apache
		if( !file_exists($dirname) ){
			mkdir( $dirname, 0755 );
			chgrp( $dirname, 'apache' );
			chown( $dirname, 'apache' );
		}

		// Le produit doit-il être disponible pour apparaître dans le flux ?
		$ronly = ctr_params_get( CTR_GOOGLE, 'only-stock' );
		if( !$ronly || !ria_mysql_num_rows($ronly) ){
			return false;
		}

		$config['only-stock'] = ria_mysql_result( $ronly, 0, 'fld' );

		// Les produits doivent-ils toujours apparaître comme En stock ? (cas de la contremarque / LDD / Drop Shipping)
		$config['always_in_stock'] = false;
		$rtmp = ctr_params_get( CTR_GOOGLE, 'always_in_stock' );
		if( $rtmp && ria_mysql_num_rows($rtmp) ){
			$tmp = ria_mysql_fetch_assoc( $rtmp );

			if( $tmp['fld'] == 'Oui' ){
				$config['always_in_stock'] = true;
			}
		}

		if( $config['tnt_id'] == 4 ){ // Spécifique Proloisirs
			$config['brands'] = array(2093, 2094, 2116);
			$config['get_prd_params'] = array( 'fld_or' => true, 'reseller_id' => false, 'brand' => $config['brands'], 'publish_reseller' => true );
			$config['dps_ldd']		  = 101325;

			$config['new_port_prices'] = array(
				'ldd' => array(
					0 => 6.666667,
					51 => 15.833333,
					101 => 24.1666666,
					301 => 32.5,
					601 => 40.833333,
					991 => 49.166667,
					1491 => 0,
				),
				'noldd' => array(
					0 => 5.416667,
					51 => 12.5,
					101 => 16.583333,
					301 => 24.916667,
					601 => 33.25,
					991 => 41.583333,
					1491 => 0,
				)
			);
		}

		// création du fichier
		$f = fopen( $file, 'w' );

		// écriture de l'entête
		fwrite( $f, '<?xml version="1.0"?>'."\n" );
		fwrite( $f, '<rss xmlns:g="http://base.google.com/ns/1.0" version="2.0">'."\n" );
		fwrite( $f, '	<channel>'."\n" );
		fwrite( $f, '		<title>'.xmlentities( strtolower($config['site_name']) ).'</title>'."\n" );
		fwrite( $f, '		<link>'.$config['site_url'].'</link>'."\n" );
		fwrite( $f, '		<description>'.xmlentities( $config['site_desc'] ).'</description>'."\n" );

		// récupère le catalogue exporté sur Google Shopping
		$rp = ctr_catalogs_get( CTR_GOOGLE, 0, 0, true );

		// Parcours la liste des produits du catalogue.
		// Le parcours traite les relations parents/enfants automatiquement. Si un produit est sélectionné pour apparaître dans le flux,
		// alors son parent et/ou ses enfants seront automatiquement ajoutés au flux même s'ils n'ont pas été choisis individuellement.
		if( $rp && ria_mysql_num_rows($rp) ){
			$ar_ids = array();
			while( $p = ria_mysql_fetch_assoc($rp) ){
				// Identifiant d'un article parent
				$parent = prd_products_get_parent( $p['prd_id'], true );
				$tmp_prd_id = is_numeric($parent) && $parent > 0 ? $parent : $p['prd_id'];

				// vérifier s'il existe des produits enfants
				$childs = prd_childs_get( $tmp_prd_id, false, false );
				if( $childs && ria_mysql_num_rows($childs) ){
					while( $child = ria_mysql_fetch_array($childs) ){
						if( !in_array($child['id'], $ar_ids) ){
							add_product( $ar_ids, $f, $tmp_prd_id, $config, $child );
						}
					}
					continue;
				}

				if( !in_array($p['prd_id'], $ar_ids) ){
					add_product( $ar_ids, $f, $p['prd_id'], $config );
				}
			}

		}

		// écriture de pied de page
		fwrite( $f, '	</channel>'."\n" );
		fwrite( $f, '</rss>'."\n" );

		// fermeture du fichier
		fclose( $f );

		// création des urls vers le fichier d'export
		ctr_comparators_file_update( CTR_GOOGLE, $url_file );

		rename( $dirname.'tmp-google_shopping.xml', $dirname.'google_shopping.xml' );
		@unlink( $dirname.'tmp-google_shopping.xml' );
	}

	/** Cette fonction permet de générer le XML du catalogue produit pour Google Shopping.
	 * 	\param $ar_ids Obligatoire, tableau contenant les identifiants des articles déjà traités par la fonction
	 * 	\param $file Obligatoire, Objet fichier contenant le XML (via fopen)
	 * 	\param $prd Obligatoire, Identifiant du produit
	 * 	\param $config Obligatoire, configuration ($config) permettant les requêtes et l'export
	 * 	\param $child Optionnel, produit enfant à envoyer (tel que retourné par prd_childs_get)
	 * 	\return false en cas d'erreur
	 */
	function add_product( &$ar_ids, $file, $prd, $config, $child=false ){

		// Charge les informations sur le produit
		$product = ria_mysql_fetch_assoc( prd_products_get_simple($prd) );

		// Seul les produits commandables sont exportés. Dans le cas de produits parents/enfants,
		// la notion de commandable n'est contrôlée que sur l'enfant.
		if( $child!==false ){
			if( !$child['orderable'] ){
				return false;
			}
		} else {
			if( !$product['orderable'] ){
				return false;
			}
		}

		// Poids
		$sell_weight = $product['sell_weight'];

		// url du produit (produit parent s'il s'agit d'un produit enfant)
		if( $config['tnt_id'] == 4 ){ // Spécifique Proloisirs
			$url = prd_products_get_url( $product['id'], true, $config['cat_root'] );
		}else{
			$url = prd_products_get_url( $product['id'], true );
		}

		/*
		// non prodable pour l'instant
		if( $child!==false ){
			$url_enfant_publie = prd_products_get_url( $child['id'], true );
			if( trim($url_enfant_publie) )
				$url = $url_enfant_publie;
		}
		*/

		// intégre le produit seulement s'il existe une url publiée
		if( trim($url) ){
			$url = rew_strip( $url );

			$info = false;
			// information sur le produit
			if( $child!==false ){
				// utilisation du titre de surcharge
				$title = ctr_catalogs_get_prd_title( CTR_GOOGLE, $child['id'], false, true );
				$desc = ctr_catalogs_get_prd_desc( CTR_GOOGLE, $child['id'], false, true, false );
				if( trim($desc)=='' ){
					$desc = ctr_catalogs_get_prd_desc( CTR_GOOGLE, $product['id'], false, true );
				}

				$info = array(
					'id' => $child['id'],
					'ref' => $child['ref'],
					'name' => $child['name'],
					'title' => trim($title) ? $title : $child['title'],
					'desc' => $desc,
					'stock' => $child['stock'],
					'countermark' => $child['countermark'],
					'stock_com' => $child['stock_com'],
					'img_id' => $child['img_id']>0 ? $child['img_id'] : $product['img_id'],
					'brand' => $child['brd_title'],
					'barcode' => $child['barcode'],
					'weight' => $child['weight_net']!='' ? $child['weight_net'] : $child['weight'],
					'follow_stock' => prd_products_is_follow_stock( $child['ref'] ),
					'parent' => $product['id']
				);

				$info['img_id'] = ctr_catalogs_get_prd_images( CTR_GOOGLE, $child['id'], 1, 'main' );
				if( !$info['img_id'] ){
					$info['img_id'] = ctr_catalogs_get_prd_images( CTR_GOOGLE, $product['id'], 1, 'main' );
				}
			} else {
				// utilisation du titre de surcharge
				$title = ctr_catalogs_get_prd_title( CTR_GOOGLE, $product['id'], false );
				$desc = ctr_catalogs_get_prd_desc( CTR_GOOGLE, $product['id'], false );

				$info = array(
					'id' => $product['id'],
					'ref' => $product['ref'],
					'name' => $product['name'],
					'title' => trim($title) ? $title : $product['title'],
					'desc' => $desc,
					'stock' => $product['stock'],
					'countermark' => $product['countermark'],
					'stock_com' => $product['stock_com'],
					'img_id' => ctr_catalogs_get_prd_images( CTR_GOOGLE, $product['id'], 1, 'main' ),
					'brand' => $product['brd_title'],
					'barcode' => $product['barcode'],
					'weight' => $product['weight_net']!='' ? $product['weight_net'] : $product['weight'],
					'follow_stock' => prd_products_is_follow_stock( $product['ref'] ),
					'parent' => '0'
				);

				if( trim($info['desc'])=='' ){
					$info['desc'] = $product['desc'];
				}
			}

			// Traitement spécifique à certaines boutique
			switch( $config['tnt_id'] ){
				case 4 : { // Proloisirs
					$tmp_arg = $config['get_prd_params'];

					if( isset($tmp_arg['prs_wst_id']) ){
						unset($tmp_arg['prs_wst_id']);
					}
					if( isset($tmp_arg['exclude']) ){
						unset($tmp_arg['exclude']);
					}
					if( isset($tmp_arg['reseller_id']) ){
						unset($tmp_arg['reseller_id']);
					}

					$rp = prd_products_get_simple( $product['id'], '', false, 0, false, false, false, false, $tmp_arg );
					if( !$rp || !ria_mysql_num_rows($rp) ){
						return false;
					}

					$p = ria_mysql_fetch_assoc( $rp );

					$product['nomenclature_type'] = $p['nomenclature_type'];
					$product['is_parent'] = prd_products_is_parent( $product['id'] );

					// Récupère la TVA
					$tva_rate = _TVA_RATE_DEFAULT;

					$rtva = prd_products_get_price( $info['id'], 0, 0, 0, 1, 0, true );
					if( $rtva && ria_mysql_num_rows($rtva) ){
						$tva = ria_mysql_fetch_assoc( $rtva );

						if( is_numeric($tva['tva_rate']) && $tva['tva_rate'] > 0 ){
							$tva_rate = $tva['tva_rate'];
						}
					}

					$info['tva_rate'] = $tva_rate;
					$info['price'] = fld_object_values_get( $info['id'], 770, i18n::getLang() );
					$info['price_ht'] = round( ($info['price'] / $tva_rate), 2 );

					$info['price_port'] = 0;

					$is_ldd = fld_object_values_get($info['id'], 503, '', false, true);
					$is_ldd = in_array($is_ldd, array('oui', 'Oui', '1', 1));

					$ar_port = $config['new_port_prices'][$is_ldd ? 'ldd' : 'noldd'];
					foreach ($ar_port as $ht => $port) {
						if ($info['price_ht'] > $ht) {
							$info['price_port'] = $port;
						}
					}

					// montant de port obligatoire
					if( $info['price_port'] <= 0 ){
						return false;
					}

					// Disponible en stock
					require_once( $config['site_dir'].'/include/view.product.inc.php' );
					$res_stock = view_product_stock($product);
					if (is_numeric($res_stock) && $res_stock > 0) {
						$info['stock'] = $res_stock;
					}else{
						$info['stock'] = 0;
					}

					break;
				}
				case 26 : { // Ma Maison est Magnifique
					// récupère le montant du port
					$tmp = get_service_for_products( array($info), false );
					if( is_array($tmp) ){
						$tmp= array_shift( array_shift( $tmp ) );
						$info['price_port'] = $tmp['cost'];
					}

					// Tarif du produit
					$price = prd_products_get_price( $info['id'] );
					if( !$price || !ria_mysql_num_rows($price) ){
						return false;
					}
					$info['price'] = ria_mysql_result( $price, 0, 'price_ttc' );
					break;
				}
				case 43 : { // Purebike
					// Si pas de stock, on récupère le stock fournisseurs
					if( !is_numeric($info['stock']) || $info['stock'] <= 0 ){
						$stock_fourn = fld_object_values_get( $info['id'], 3590 );
						if( is_numeric($stock_fourn) && $stock_fourn > 0 ){
							$info['stock'] = $stock_fourn;
						}
					}

					// Tarif du produit
					$price = prd_products_get_price( $info['id'] );
					if( !$price || !ria_mysql_num_rows($price) ){
						return false;
					}
					$info['price'] = ria_mysql_result( $price, 0, 'price_ttc' );
					break;
				}
				default: {
					// Tarif du produit
					$price = prd_products_get_price( $info['id'] );
					if( !$price || !ria_mysql_num_rows($price) ){
						return false;
					}
					$info['price'] = ria_mysql_result( $price, 0, 'price_ttc' );
					break;
				}
			}

			// Description du produit
			/* Note Google Shopping : Nous vous recommandons de mentionner le terme "multipack" et la quantité ( $info['qte_pack'] ) dans la description. */
			$info['desc'] = trim($info['desc'])=='' ? $info['title'] : html_revert_wysiwyg($info['desc']);

			$add_desc = '';
			$length_add_desc = 0;

			// Récupère la description supplémentaire ajoutée à tous les produits
			$radd_desc = ctr_params_get( CTR_GOOGLE, 'add-desc' );
			if( $radd_desc && ria_mysql_num_rows($radd_desc) ){
				$add_desc = ria_mysql_result( $radd_desc, 0, 'fld' );
				$length_add_desc = strlen( $add_desc ) + 1;
			}

			// Limite de caractère sur différents champs
			$info['title'] = strcut( $info['title'], 150 );
			$info['desc']  = strcut( $info['desc'], (5000-$length_add_desc) ).' '.$add_desc;

			// Vérifie que le produit est en stock. Si seuls les produits en stock doivent
			// être envoyés au comparateur et que celui-ci n'est pas en stock, il sera ignoré.
			if( $info['follow_stock'] && $config['only-stock']=='Oui' && !$info['stock'] ){
				return false;
			}

			// Vérifie que l'url du produit est fonctionnelle (en réalisant une requête HTTP)
			if( $config['tnt_id'] != 1 ){ // désactivation bigship temporaire
				// Seules les urls valides seront inclues
				$infos = Sitemap::checkUrl( $config['site_url'].$url, false, '', true );
				if( !$infos ){
					return false;
				}

				// Récupère l'url directe du produit (sans redirection)
				$url = str_replace( array(str_replace('https', 'http', $config['site_url']), $config['site_url'], '?testadminerror404=1'), '', $infos['url'] );
				if( !trim($url)|| $url=='/' ){
					return false;
				}

				if ($config['tnt_id']==27 && $config['wst_id']==45) {
					if (strstr($url, '/catalogue/mobilierdeville/')) {
						return false;
					}
				}
			}
			// Conditionnement du produit
			//
			// on applique le fonctionnement Animal & Co (qui n'est pas le standard, mais ce sont les seuls qui ont besoin des multipacks pour l'instant)
			// à savoir :
			//	- si un produit est vendu dans un conditionnement, il n'est pas vendu à l'unité
			//	- Il n'y a qu'un seul conditionnement possible
			$info['qte_col'] = 0;
			$info['qte_pack'] = 1;
			if( isset($config['ctr_unity_qty_pack']) && !$config['ctr_unity_qty_pack'] ){
				if( !$sell_weight ){
					$rcol = prd_colisage_classify_get( 0, $info['id'], 0, array('qte'=>'asc') );
					if( $rcol && ria_mysql_num_rows($rcol) ){
						$info['qte_col'] = ria_mysql_result($rcol, 0, 'col_id');
						$info['qte_pack'] = ria_mysql_result($rcol, 0, 'qte');
					}

					if( $rcol && ria_mysql_num_rows($rcol)>1 ){
						error_log( 'Export Google Shopping : il existe plusieurs conditionnements pour le produit '.$info['id'].'. Un seul va être exporté.' );
					}
				}
			}

			if( $sell_weight ){ // Le prix de vente est exprimé au kilo
				$info['price'] = $info['price'] * ($info['weight'] / 1000);
			}

			// url des images des produits
			// utilisation de la nouvelle configuration 800x800 "google-shopping" si existante
			$thumbs = isset($config['img_sizes']['google-shopping']) ? $config['img_sizes']['google-shopping'] : $config['img_sizes']['high'];
			$url_img = $config['img_url'].'/'.$thumbs['width'].'x'.$thumbs['height'];

			// Contrôle que le prix du produit est bien supérieur à 0. Le produit ne pourra pas être exporté
			// si cette condition n'est pas remplie.
			if( $info['price']<=0 ){
				return false;
			}

			// intègre le produit à Google Shopping
			fwrite( $file, '		<item>'."\n" );

			$ctr_cat = ctr_catalogs_get_categorie( CTR_GOOGLE, $info['id'], false, true );
			if( $info['parent'] && !$ctr_cat ){
				$ctr_cat = ctr_catalogs_get_categorie( CTR_GOOGLE, $info['parent'], false, true );
			}

			// informations générales du produit
			fwrite( $file, '			<title><![CDATA['.xmlentities( ucfirst2($info['title']) ).']]></title>'."\n" );
			fwrite( $file, '			<link><![CDATA['.$config['site_url'].$url.'?utm_source=google_shopping&utm_medium=referral&utm_term='.urlencode($info['ref']).'&cid='.$ctr_cat.']]></link>'."\n" );
			fwrite( $file, '			<description><![CDATA['.$info['desc'].']]></description>'."\n" );
			fwrite( $file, '			<g:id>'.$info['id'].'</g:id>'."\n" );
			fwrite( $file, '			<g:condition>new</g:condition>'."\n" );

			// tarif avec promotion (important : multiplié par la quantité conditionnée)
			switch( $config['tnt_id'] ){
				case 4 : { // Proloisirs
					// on ne récupère pas les promotions sur produits pour Proloisirs (réservé à l'extranet).
					$promo = pmt_promotions_get($info['id'], array('price_ht' => $info['price_ht'], 'tva_rate'=>$info['tva_rate'], 'price_ttc'=>$info['price']), 0, false, true);

					if (is_array($promo) && count($promo) && round($promo['price_ttc'], 2) < round($info['price'], 2)) {
						fwrite($file, '            <g:sale_price>'.number_format($promo['price_ttc'], 2, '.', '').' EUR</g:sale_price>'."\n");
						fwrite($file, '            <g:sale_price_effective_date>'.date('Y-m-d', strtotime($promo['date-start-en'])).'T00:00-0800/'.date('Y-m-d', strtotime($promo['datehour-end-en'])).'T23:59-0800</g:sale_price_effective_date>'."\n");
					}
					break;
				}
				case 43 : { // Purebike
					// Récupère le prix avant Soldes
					$p_solde = pmt_soldes_is_active();
					$y_solde = pmt_soldes_get_periods( date('Y') );

					if( $p_solde !== false ){
						$price_solde = fld_object_values_get( $info['id'], 3553 );

						if( is_numeric($price_solde) && $price_solde > $info['price'] ){
							fwrite( $file, '            <g:sale_price>'.number_format($info['price'], 2, '.', '').' EUR</g:sale_price>'."\n" );
							fwrite( $file, '            <g:sale_price_effective_date>'.date( 'Y-m-d', $y_solde[ $p_solde ]['start']['time'] ).'T00:00-0800/'.date( 'Y-m-d', $y_solde[ $p_solde ]['stop']['time'] ).'T23:59-0800</g:sale_price_effective_date>'."\n" );

							$info['price'] = $price_solde;
							break;
						}
					}
				}
				default : {
					// ATTENTION : si pas de solde en champ avancé, le code dans le default est exécuté pour Purebike (à conserver)
					$pmt = prc_promotions_get($info['id'], 0, 0, 1, $info['qte_col']);

					if( is_array($pmt) && sizeof($pmt) && $pmt['price_ttc']<$info['price'] ){
						fwrite( $file, '            <g:sale_price>'.number_format($pmt['price_ttc'] * $info['qte_pack'], 2, '.', '').' EUR</g:sale_price>'."\n" );
						if( trim($pmt['date-start']) && trim($pmt['date-end']) ){
							$datestart = dateparse( $pmt['date-start'] );
							$dateend = dateparse( $pmt['date-end'] );
							fwrite( $file, '            <g:sale_price_effective_date>'.$datestart.'T00:00-0800/'.$dateend.'T23:59-0800</g:sale_price_effective_date>'."\n" );
						}
					}
				}
			}

			// prix du produit (important : multiplié par la quantité conditionnée)
			fwrite( $file, '			<g:price>'.number_format($info['price'] * $info['qte_pack'], 2, '.', '').' EUR</g:price>'."\n" );

			// Information de stock, deux valeurs prédéfinies sont possibles :
			//	- in stock : en stock
			//	- out of stock : non disponible
			//
			$stock = 'in stock';
			if( !$config['always_in_stock'] ){
				if( $info['follow_stock'] ){
					if( $info['stock']>0 || $info['countermark'] ){
						$stock = 'in stock';
					}else{
						$stock = 'out of stock';
					}
				}
			}
			fwrite( $file, '			<g:availability>'.$stock.'</g:availability>'."\n" );

			// Image principale du produit
			//
			// La documentation de Google Shopping indique (https://support.google.com/merchants/answer/188494?hl=fr) :
			//	1. Utilisez l'image la plus grande, en taille réelle, de votre produit (jusqu'à 4 Mo).
			//	2. Nous recommandons l'utilisation d'images d'au moins 800 x 800 pixels.
			//	3. N'agrandissez pas les images. N'envoyez pas non plus de vignettes.
			//	4. Le format minimal obligatoire pour les images de vêtements est 250 x 250 pixels.
			//	5. L'image doit représenter le produit. N'indiquez pas d'espace réservé, tel que "Image non disponible", ou le logo de la marque ou de votre magasin.
			//	6. Les images des produits ne doivent pas comporter de logos ou d'autres formes de promotion.
			//	7. Les formats d'image acceptés sont GIF (.gif) non animés, JPEG (.jpg/.jpeg), PNG (.png), BMP (.bmp) et TIFF (.tif/.tiff). Les extensions des fichiers d'image doivent correspondre au format utilisé.
			//	8. L'URL doit commencer par "http://" ou "https://"
			//
			if( $info['img_id'] ){
				fwrite( $file, '			<g:image_link>'.$url_img.'/'.$info['img_id'].'.'.$thumbs['format'].'</g:image_link>'."\n" );
			}

			// Images secondaires
			//
			// La documentation de Google Shopping indique (https://support.google.com/merchants/answer/188494?hl=fr) :
			// 	1. Vous pouvez insérer jusqu'à 10 images supplémentaires par article en utilisant cet attribut plusieurs fois.
			//	2. Pour les fichiers XML, ajoutez chaque URL en tant qu'attribut <additional_image_link> distinct.
			//	3. Cet attribut est soumis aux mêmes règles que l'attribut 'lien image' [image link].
			//
			$img_second = ctr_catalogs_get_prd_images( CTR_GOOGLE, $product['id'], 10, 'second' );
			if( is_array($img_second) ){
				foreach( $img_second as $img ){
					fwrite( $file, '			<g:additional_image_link>'.$url_img.'/'.$img.'.'.$thumbs['format'].'</g:additional_image_link>'."\n" );
				}
			}

			// liste des paramètres qui permettent de caractériser un produit "avec variantes"
			// dans quel cas, on définit un attribut "item_group_id" qui contiendra l'identifiant du produit parent
			// note : ce fonctionnement est facultatif de dehors des vètements
			// ce système oblige par ailleurs à avoir un produit cartésien de variantes qui soit unique (exemple : pas deux produits différents avec taille et couleur du premier = taille et couleur du deuxième)
			$var_codes = array('size', 'color', 'pattern', 'material');
			$is_var = false;

			// paramètre configuration des comparateurs de prix
			$gtin = false;
			$params = ctr_params_get( CTR_GOOGLE );
			if( $params && ria_mysql_num_rows($params) ){

				while( $param = ria_mysql_fetch_array($params) ){

					if( in_array($param['code'], array('only-stock')) ){
						continue;
					}

					if( $param['fld']>0 ){
						$value = fld_object_values_get( $info['id'], $param['fld'] );
						if( trim($value) ){

							switch( $param['code'] ){
								case 'online_only' :
									// Indique qu'un produit est disponible uniquement en ligne, et n'est pas en vente directement dans votre magasin.
									// - 'o' [y] : lorsqu'un article n'est pas en vente dans votre magasin.
									// - 'n' [n] : lorsqu'un client peut acheter l'article publié dans votre magasin. Il s'agit de la valeur par défaut.
									//
									// Utilisation de l'attribut : obligatoire si vous avez soumis l'emplacement de vos magasins et que vous proposez des
									// articles vendus en ligne, mais pas dans votre magasin.
									if( $value!=='Oui' && $value!=='Non' ){
										error_log( 'Export Google Shopping : valeur non supportée pour l\'attribut online_only : '.$value );
									}
									fwrite( $file, '			<g:online_only>'.( $value == 'Oui' ? 'y' : 'n' ).'</g:online_only>'."\n" );
									break;
								case 'isbn' :
									fwrite( $file, '			<g:gtin>'.$value.'</g:gtin>'."\n" );
									$gtin = true;
									break;
								case 'age_group':
									// restrictions spécifiques aux tranches d'âges
									$value = str_replace(' / ', '&#47;', $value);
									$value = strtolower(trim($value));
									if( $value=='adultes' ){
										$value = 'adult';
									}elseif( $value=='enfants' ){
										$value = 'kids';
									}else{
										error_log('Export Google Shopping : valeur incorrecte pour le paramètre age_group (ID '.$info['id'].').');
									}
									fwrite( $file, '			<g:'.$param['code'].'><![CDATA['.xmlentities( $value ).']]></g:'.$param['code'].'>'."\n" );
									break;
								case 'gender':
									// restrictions spécifiques au sexe
									$value = str_replace(' / ', '&#47;', $value);
									$value = strtolower(trim($value));
									if( $value=='homme' ){
										$value = 'male';
									}elseif( $value=='femme' ){
										$value = 'female';
									}elseif( $value=='unisexe' ){
										$value = 'unisex';
									}else{
										error_log('Export Google Shopping : valeur incorrecte pour le paramètre gender (ID '.$info['id'].').');
									}
									fwrite( $file, '			<g:'.$param['code'].'><![CDATA['.xmlentities( $value ).']]></g:'.$param['code'].'>'."\n" );
									break;
								default :
									$value = str_replace(' / ', '&#47;', $value);
									fwrite( $file, '			<g:'.$param['code'].'><![CDATA['.xmlentities( $value ).']]></g:'.$param['code'].'>'."\n" );
									break;
							}

							// le paramètre fait partie des variantes de produits
							if( in_array($param['code'], $var_codes) ){
								$is_var = true;
							}

						}
					}
				}

			}

			// on est sur un produit enfant qui est une variante
			if( ($is_var || in_array($config['tnt_id'], array(4, 43))) && is_numeric($info['parent']) && $info['parent'] ){
				fwrite( $file, '			<g:item_group_id>'.$info['parent'].'</g:item_group_id>'."\n" );
			}

			// code barre 'EAN'
			if( !$gtin && trim($info['barcode']) ){
				fwrite( $file, '			<g:gtin>'.$info['barcode'].'</g:gtin>'."\n" );
				$gtin = true;
			}

			/*
			Le couple de variables ci-dessous représente un identifiant unique pour le fabriquant
			2 cas :
				- on connait la marque (brd_name) et la référence de son produit (prd_suppliers -> ps_ref)
				- le vendeur fabrique ses produits (Pierre Oteiza, Terre de Viande) : le nom est stocké dans une variable de niveau tenant est le prd_ref est la référence constructeur
			Important : à partir du moment où une boutique gère des marques, il ne faut pas renseigner "comparators_default_brand" (sauf si l'on est CERTAIN que tous les produits sans marque sont les produits fabriqués par le vendeur)
			*/
			$ok_ref_constructeur = false;
			$ok_name_constructeur = false;

			// référence fabriquant
			$sup = prd_suppliers_get( $info['id'], 0, '', true );
			if( $sup && ria_mysql_num_rows($sup) ){
				$ref_sup = ria_mysql_result( $sup, 0, 'ref' );
				if( trim($ref_sup) ){
					fwrite( $file, '		<g:mpn>'.$ref_sup.'</g:mpn>'."\n" );
					$ok_ref_constructeur = true;
				}
			}

			// Si aucune marque n'est renseignée, on récupère la marque par défaut dans les paramètres Google Shopping
			if( trim($info['brand'])=='' ){
				$rbrd = ctr_params_get( CTR_GOOGLE, 'default_brand' );
				if( $rbrd && ria_mysql_num_rows($rbrd) ){
					$info['brand'] = ria_mysql_result( $rbrd, 0, 'fld' );
				}

			}

			// Marque
			if( trim($info['brand']) ){
				fwrite( $file, '			<g:brand><![CDATA['.xmlentities( $info['brand']).']]></g:brand>'."\n" );
				$ok_name_constructeur = true;
			}

			// le vendeur est le fabriquant
			if( !$ok_ref_constructeur && !$ok_name_constructeur && trim($config['comparators_default_brand']) ){
				fwrite( $file, '			<g:mpn>'.$info['ref'].'</g:mpn>'."\n" );
				fwrite( $file, '			<g:brand><![CDATA['.xmlentities($config['comparators_default_brand']).']]></g:brand>'."\n" );
				$ok_ref_constructeur = true;
				$ok_name_constructeur = true;
			}

			// pas de code unique (ni EAN, ni ISBN, ni constructeur)
			if( !$gtin && !($ok_ref_constructeur && $ok_name_constructeur) ){
				/* note Google Shopping : La valeur définie par défaut est TRUE lorsque l'attribut est vide. */
				fwrite( $file, '			<g:identifier_exists>FALSE</g:identifier_exists>'."\n" );
			}

			// poids du produit
			if( is_numeric($info['weight']) && $info['weight']>0 ){
				fwrite( $file, '			<g:shipping_weight>'.$info['weight'].' g</g:shipping_weight>'."\n" );
				// poids net du produit pour la vente au poids
				if( $sell_weight ){
					fwrite( $file, '		<g:unit_pricing_measure>'.$info['weight'].' g</g:unit_pricing_measure>'."\n" );
					// l'attribut ci-dessous indique l'unité de l'expression "Soit xx € le [unité]"
					fwrite( $file, '		<g:unit_pricing_base_measure>1 kg</g:unit_pricing_base_measure>'."\n" );
				}
			}

			if( isset($info['price_port']) && ($config['tnt_id'] == 4 || $config['tnt_id'] == 26) ){
				if( isset($ris_flash) && (!$ris_flash || !ria_mysql_num_rows($ris_flash)) ){
					$info['price_port'] = 0;
				}

				// Calcul des frais de port spécifique à Proloisirs
				fwrite( $file, '			<g:shipping>'."\n" );
				fwrite( $file, '				<g:country>FR</g:country>'."\n" );
				fwrite( $file, '				<g:service>Standard</g:service>'."\n" );
				fwrite( $file, '				<g:price>'.number_format( $info['price_port'], 2 , '.', '' ).' EUR</g:price>'."\n" );
				fwrite( $file, '			</g:shipping>'."\n" );
			}

			// gestion du conditionnement "multipack"
			if( $info['qte_pack'] > 1 ){
				fwrite( $file, '			<g:multipack>'.$info['qte_pack'].'</g:multipack>'."\n" );
			}

			// catégorie de rattachement
			$catname = ctr_categories_export( CTR_GOOGLE, $ctr_cat, 2, '>' );
			if( $catname!='' ){
				fwrite( $file, '			<g:google_product_category><![CDATA['.$catname.']]></g:google_product_category>'."\n" );
			}

			// classification interne du produit
			/* 	Cet attribut indique également la catégorie du produit proposé, mais d'après votre propre classification.
				Contrairement à l'attribut 'catégorie de produits Google' [google product category], vous pouvez inclure plusieurs valeurs d'attribut
				'catégorie' [product type] si les produits appartiennent à plusieurs catégories. Incluez la chaîne de catégorie complète.
				Par exemple, si vos produits appartiennent à la catégorie Réfrigérateurs, indiquez la chaîne complète :
					Maison et jardin > Cuisine et table > Petit électroménager > Réfrigérateurs.
				Vous pouvez utiliser le séparateur > ou /.
			*/
			$rhy = prd_products_categories_get( $info['id'], true, false, null, $config['cat_root'] );
			if( $rhy && ria_mysql_num_rows($rhy) ){

				$i = 1;
				while( $hy = ria_mysql_fetch_array($rhy) ){
					if( $i>10 ){
						break;
					}

					if( !prd_categories_is_published($hy['cat']) ){
						continue;
					}

					// récupère la hiérarchie complète
					$rparent = prd_categories_parents_get( $hy['cat'] );
					if( $rparent && ria_mysql_num_rows($rparent) ){
						$tmp_hy = '';
						while( $parent = ria_mysql_fetch_array($rparent) ){
							if( $parent['id'] == $config['cat_root'] ){
								continue;
							}

							$tmp_hy .= ( $tmp_hy!='' ? ' > ' : '' ).$parent['title'];
						}

						$tmp_hy .= ( $tmp_hy!='' ? ' > ' : '' ).$hy['title'];
						if( $tmp_hy!='' ){
							fwrite( $file, '			<g:product_type><![CDATA['.$tmp_hy.']]></g:product_type>'."\n" );
							$i++;
						}
					} else {
						fwrite( $file, '			<g:product_type><![CDATA['.$hy['title'].']]></g:product_type>'."\n" );
						$i++;
					}

				}
			}

			// si aucune clasification publié pour le produit enfant, on prends celle du produit parent.
			if( $child!==false ){
				$rhy = prd_products_categories_get( $product['id'], true, false, null, $config['cat_root'] );
				if( $rhy && ria_mysql_num_rows($rhy) ){

					while( $hy = ria_mysql_fetch_array($rhy) ){

						if( !prd_categories_is_published($hy['cat']) ){
							continue;
						}

						// récupère la hiérarchie complète
						$rparent = prd_categories_parents_get( $hy['cat'] );
						if( $rparent && ria_mysql_num_rows($rparent) ){
							$tmp_hy = '';
							while( $parent = ria_mysql_fetch_array($rparent) ){
								if( $parent['id'] == $config['cat_root'] ){
									continue;
								}

								$tmp_hy .= ( $tmp_hy!='' ? ' > ' : '' ).$parent['title'];
							}

							$tmp_hy .= ( $tmp_hy!='' ? ' > ' : '' ).$hy['title'];
							if( $tmp_hy!='' ){
								fwrite( $file, '			<g:product_type><![CDATA['.$tmp_hy.']]></g:product_type>'."\n" );
							}
						}

					}
				}
			}

			fwrite( $file, '		</item>'."\n" );
		}

		if ($child!==false) {
			$ar_ids[] = $child['id'];
		} else {
			$ar_ids[] = $prd;
		}
	}