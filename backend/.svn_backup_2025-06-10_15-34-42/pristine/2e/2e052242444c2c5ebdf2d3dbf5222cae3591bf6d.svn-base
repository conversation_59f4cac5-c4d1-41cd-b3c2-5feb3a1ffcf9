# InputFileReadCsvConfiguration

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**has_header_record** | **bool** | Indicate if the csv file contains the column name at the first row | [default to true]
**ignore_header_record** | **bool** | Indicate if the importation should not use the csv column name from the file | [default to false]
**csv_separator** | **string** | Indicate the separator of the values in the CSV file. Generally \&quot;;\&quot; | [default to ';']
**csv_text_qualifier** | **string** | Indicate the text qualifier of the CSV file. Generally the value is \&quot; | [optional] [default to '"']

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


