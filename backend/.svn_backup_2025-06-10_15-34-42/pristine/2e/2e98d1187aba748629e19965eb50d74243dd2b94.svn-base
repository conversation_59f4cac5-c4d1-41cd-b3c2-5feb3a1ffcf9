{"name": "composer/xdebug-handler", "description": "Restarts a process without Xdebug.", "type": "library", "license": "MIT", "keywords": ["xdebug", "performance"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues"}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8"}, "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "autoload-dev": {"psr-4": {"Composer\\XdebugHandler\\": "tests"}}, "scripts": {"test": "phpunit"}}