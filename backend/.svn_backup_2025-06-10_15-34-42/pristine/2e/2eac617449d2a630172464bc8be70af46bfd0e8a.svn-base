<?php
	/**	\file unclassified.php
	 *	Cette page affiche la liste des produits qui ne sont classés dans aucune catégorie.
	 */

	// Vérifie que l'utilisateur a accès à cette catégorie
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_PRODUCT');

	// Déplacement de produits
	if( isset($_POST['classify']) ){
		$move = '';
		
		if( isset($_POST['prd']) ){
			foreach( $_POST['prd'] as $p ){
				if( is_numeric($p) ){
					$move .= '&products[]='.$p;
				}
			}
		}
		
		if( $move=='' ){ // Aucun produit sélectionné
			$error = _('Veuillez sélectionner les produits à déplacer.');
		}else{ // Redirige l'utilisateur vers la page qui permet de choisir la destination de classement
			header('Location: move.php?classify=1&srccat=0'.$move);
			exit;
		}
	}

	// Suppression de produits
	if( isset($_POST['delete']) ){
		if( isset($_POST['prd']) && is_array($_POST['prd']) ){
			foreach( $_POST['prd'] as $p ){
				if( !prd_products_get_is_sync($p) ){
					prd_products_del($p);
				}
			}
		}

		header('Location: unclassified.php');
		exit;
	}

	// Téléchargement de l'export Excel des produits
	if( isset($_GET['downloadexport']) ){
		// Contrôle que le fichier est bien disponible
		$file = $config['doc_dir'] . '/export-catalog-' . $_SESSION['usr_id'] . '.csv';
		if (file_exists($file)) {
			if (!isset($_GET['for_mac'])) {
				header('Pragma: no-cache');
				header('Expires: 0');
				header('Content-disposition: attachment; filename="export-produits.csv"');
			
				echo "\xEF\xBB\xBF"; // BOM UTF-8
			}else{
				header('Content-Description: File Transfer');
				header('Content-Type: application/octet-stream');
				header('Content-Disposition: attachment; filename="export-produits.csv"');
				header('Expires: 0');
				header('Cache-Control: must-revalidate');
				header('Pragma: public');
				header('Content-Length: ' . filesize($file));	
			}
			
			readfile ($file); 
			exit;
		}else{
			$error = _("Le fichier ne semble plus disponible, veuillez préparer un nouvel export en cliquant sur le bouton \"Exporter\".");
		}
	}

	define('ADMIN_PAGE_TITLE', _('Catalogue'));
	require_once('admin/skin/header.inc.php');

	$exclude_cols = array('price_ht', 'tva_rate', 'price_ttc', 'promo', 'cat', 'sort');
	$ref_filter = isset($_GET['ref_filter']) ? $_GET['ref_filter'] : '';

	// Récupère les articles Non classés
	$childonly = isset($_SESSION['usr_admin_show_childs']) ? $_SESSION['usr_admin_show_childs'] : true;
	$r_product = prd_products_get_simple(0, $ref_filter, false, -1, false, false, false, false, array('childs' => $childonly));

	$nb_by_page = 100;
	$count = $r_product ? ria_mysql_num_rows($r_product) : 0;

	$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] ? $_GET['page'] : 1;
	$pages = ceil($count / $nb_by_page); 

	if ($page > $pages) {
		$page = $pages;
	}
?>
	<h2><?php print _('Produits non classés').' ('.number_format( $count, 0, ',', ' ' ).')'; ?></h2>

	<?php
		if (isset($error)) {
			print '<div class="error">'.nl2br( $error ).'</div>';
		}
	?>
	
	<div class="table-top">
	
		<form action="unclassified.php" method="get">
			<label for="ref_filter">
				<?php print _('Rechercher une référence :'); ?> 
				<input type="text" name="ref_filter" id="ref_filter" value="<?php print htmlspecialchars($ref_filter); ?>" style="width: auto" />
			</label>
			<input type="submit" name="search" value="<?php print _('Rechercher'); ?>" />
		</form>
	
		<div class="with-cols-options">
			<?php
				$checked = 'checked="checked"';
				foreach( $config['ar_cols_prd'] as $key=>$col ){
					if( !$col['default'] ){
						$checked = '';
						break;
					}
				}
			?>
			<a href="#" id="display-cols-options" class="edit-cat"><?php print _('Personnaliser colonnes'); ?></a>
			<span class="menu-cols none">
				<span class="cols">
					<span class="col">
						<input <?php print $checked; ?> type="checkbox" name="cols-all" id="cols-all" value="all" />
						<label for="cols-all"><?php print _('Personnaliser colonnes'); ?></label>
					</span>
					<span class="col separate"></span>
			
					<?php
						foreach( $config['ar_cols_prd'] as $col ){
							if (in_array($col['code'], $exclude_cols)) {
								continue;
							}

							$checked = $class = '';
							if( $col['default'] ){
								$checked = 'checked="checked"';
								$class = ' checked';
							}
							
							?><span class="col<?php print $class; ?>">
								<input <?php print $checked; ?> type="checkbox" name="cols-<?php print $col['code']; ?>" id="cols-<?php print $col['code']; ?>" value="<?php print $col['code']; ?>" />
								<label for="cols-<?php print $col['code']; ?>"><?php print htmlspecialchars( _($col['name']) ); ?></label>
							</span><?php
						}
					?>
				</span>
			</span>
		</div>
	</div>

	<form action="unclassified.php?ref_filter=<?php print htmlspecialchars($ref_filter); ?>" method="post">
		<div class="table-cols-changed">
			<table id="products" class="js-unclassified checklist list-cols-changed ui-sortable tablesorter">
				<thead>
					<tr>
						<th id="prd-sel" class="minpadding prd-col-nomove">
							<input type="checkbox" onclick="checkAllClick(this)" />
						</th>
						
						<th id="prd-is-sync" class="minpadding col-sync"></th>
						
						<?php
							// Affichage des entêtes de colonnes
							foreach ($config['ar_cols_prd'] as $col) {
								if (in_array($col['code'], $exclude_cols)) {
									continue;
								}

								?><th class="<?php print $col['default'] ? 'th-col-show' : 'th-col-hide'; ?>" id="prd-<?php print $col['code']; ?>">
									<a href="#"><?php print htmlspecialchars( _($col['name']) ); ?></a>
								</th><?php
							}
						?>
					</tr>
				</thead>
				<tbody><?php 
					if ($page > 1) {
						ria_mysql_data_seek($r_product, ($page - 1) * $nb_by_page);
					}

					print view_products_list($r_product, false, $nb_by_page, $exclude_cols);
				?></tbody>
			</table>
		</div>

		<table id="foot-products" class="checklist">
			<tbody><tr style="display: none;"><td></td></tr></tbody>
			<tfoot>
				<tr>
					<td id="pagination">
					<?php
						// Affichage de la navigation dans les résultats (pagination)
						// La pagination n'est affiche qu'à partir du moment où il y a plus d'une page
						if( $count>0 && $pages > 1 ){
							print '
								<div class="page float-left">'._('Page').' '.$page.' / '.$pages.'</div>
								<div class="nav float-right">
							';

							$links = array();
							if( $page>1 ){
								$links[] = '<a href="/admin/catalog/unclassified.php?page='.($page-1).'">&laquo; '._('Préc').'</a>';
							}
							
							for( $i=$page-5; $i<$page+5; $i++ ){
								if( $i>=1 && $i<=$pages ){
									if( $i==$page )
										$links[] = '<b>'.$i.'</b>';
									else
										$links[] = '<a href="/admin/catalog/unclassified.php?page='.$i.'">'.$i.'</a>';
								}
							}
							
							if( $page<$pages ){
								$links[] = '<a href="/admin/catalog/unclassified.php?page='.($page+1).'">'._('Suiv').' &raquo;</a>';
							}

							print implode(' | ',$links);
							print '
							</div>';
						}
					?>
					</td>
				</tr>
				<tr>
					<td>
						<input class="js-input-disabled none" name="classify" value="<?php print _('Classer')?>" title="<?php print _('Classer les produits sélectionnés dans une autre catégorie (le classement actuel sera conservé).')?>" type="submit" disabled="disabled" />
						<input class="js-input-disabled none" name="delete" value="<?php print _('Supprimer')?>" title="<?php print _('Supprimer les produits sélectionnés')?>" onclick="return window.confirm('<?php print _('Vous êtes sur le point de supprimer les produits sélectionnés.').'\n'._('Cette opération est irréversible et ne pourra pas être annulée.').'\n'._('Etes-vous sûr(e) de vouloir continuer ?')?>');" type="submit" disabled="disabled" />
						<input name="export" id="export-products" value="<?php print _('Exporter')?>" title="<?php print _('Exporter les produits de la catégorie')?>" type="submit">
					</td>
				</tr>
			</tfoot>
		</table>
	</form>
	<script><!--
		var ordered = false;
		var cat = -1;
		var brd = 0;
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>