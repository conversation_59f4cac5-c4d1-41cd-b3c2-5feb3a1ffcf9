<?php
	/**	\file index.php
	 *	Cette page est chargée de l'affichage des fonctionnalitées associées à un hébergeur externe (type Youtube, DailyMotion, Viméo, ...)
	 */

	require_once('medias.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_HOST');

	if( !isset($_GET['hst']) || !doc_hosts_exists($_GET['hst']) ){
		header('Location: /admin/documents/medias/hosts.php');
		exit;
	}

	$host = ria_mysql_fetch_assoc( doc_hosts_get($_GET['hst']) );

	if( isset($_POST['addchl']) ){
		header('Location: /admin/documents/medias/channels/edit.php?hst='.$host['id'].'&chl=0');
		exit;
	}

	if( isset($_POST['addpls']) ){
		header('Location: /admin/documents/medias/playlists/edit.php?hst='.$host['id'].'&pls=0');
		exit;
	}

	if( isset($_POST['addmed']) ){
		header('Location: /admin/documents/medias/medias/edit.php?hst='.$host['id'].'&med=0');
		exit;
	}

	if( isset($_POST['delchl']) ){
		if( isset($_POST['chl']) && is_array($_POST['chl']) && sizeof($_POST['chl']) ){
			if( !doc_channels_del($_POST['chl']) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression de la ou des chaînes séletionnées. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
			}
		}

		if( !isset($error) ){
			header('Location: /admin/documents/medias/index.php?hst='.$host['id']);
			exit;
		}
	}

	if( isset($_POST['delpls']) ){
		if( isset($_POST['pls']) && is_array($_POST['pls']) && sizeof($_POST['pls']) ){
			if( !doc_playlists_del($_POST['pls']) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression de la ou des playlists séletionnées. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
			}
		}

		if( !isset($error) ){
			header('Location: /admin/documents/medias/index.php?hst='.$host['id']);
			exit;
		}
	}

	if( isset($_POST['delmed']) ){
		if( isset($_POST['med']) && is_array($_POST['med']) && sizeof($_POST['med']) ){
			if( !doc_medias_del($_POST['med']) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression du ou des médias séletionnés. \nMerci de réessayer ou prendre contact pour nous signaler le problème.");
			}
		}

		if( !isset($error) ){
			header('Location: /admin/documents/medias/index.php?hst='.$host['id']);
			exit;
		}
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Médiathèque'), '/admin/documents/index.php' )
		->push( _('Hébergeurs externes'), '/admin/documents/medias/hosts.php' )
		->push( $host['name'] );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', htmlspecialchars( $host['name'] ).' - ' . _('Hébergeurs externes'));
	require_once('admin/skin/header.inc.php');

	print '
		<h2>'.htmlspecialchars( $host['name'] ).'</h2>
	';

	if( trim($host['desc'])!='' ){
		print '<p class="notice">'.htmlspecialchars( $host['desc'] ).'</p>';
	}

	if( isset($error) ){
		print '<div class="error">'.nl2br($error).'</div>';
	}

	// Liste des chaînes
	$rchannel = doc_channels_get( $host['id'] );
	print '
		<form id="table-list-hosts" action="/admin/documents/medias/index.php?hst='.$host['id'].'" method="post">
			<table class="checklist">
				<caption>' . _("Liste des chaînes").' ('.( !$rchannel || !ria_mysql_num_rows($rchannel) ? '0' : ria_mysql_num_rows($rchannel)).')</caption>
				<thead>
					<tr>
						<th id="chl-sel" class="col-check">
							<input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
						</th>
						<th id="chl-name">' . _("Désignation") . '</th>
					</tr>
				</thead>
				<tbody>
	';

	if( !$rchannel || !ria_mysql_num_rows($rchannel) ){
		print '
					<tr>
						<td colspan="2">
							' . _("Aucune chaîne n'est rattachée à cet hébergeur") . '
						</td>
					</tr>
		';
	}else{
		while( $channel = ria_mysql_fetch_assoc($rchannel) ){
			print '
					<tr>
						<td headers="chl-sel" class="td-checkbox">
							<input type="checkbox" value="'.$channel['id'].'" name="chl[]" class="checkbox" />
						</td>
						<td headers="chl-name">
							<a title="' . _("Afficher les informations sur cette chaîne") . '" href="/admin/documents/medias/channels/index.php?hst='.$host['id'].'&amp;chl='.$channel['id'].'">'.htmlspecialchars( $channel['name'] ).'</a>
						</td>
					</tr>
			';
		}
	}

	print '
				</tbody>
				<tfoot>
					<tr>
						<td colspan="2">';
	if( ria_mysql_num_rows($rchannel) ){
		print '<input type="submit" value="' . _("Supprimer") . '" name="delchl" />';
	}
	print '
							<input type="submit" value="' . _("Ajouter") . '" name="addchl" />
						</td>
					</tr>
				</tfoot>
			</table>
	';

	// Liste des playlists
	$rplaylist = doc_playlists_get( $host['id'] );
	print '
			<table class="checklist">
				<caption>' . _("Liste des playlists").' ('.( !$rplaylist || !ria_mysql_num_rows($rplaylist) ? '0' : ria_mysql_num_rows($rplaylist)).')</caption>
				<thead>
					<tr>
						<th id="pls-sel" class="col-check">
							<input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
						</th>
						<th id="pls-name">' . _("Désignation") . '</th>

					</tr>
				</thead>
				<tbody>
	';
	if( !$rplaylist || !ria_mysql_num_rows($rplaylist) ){
		print '
					<tr>
						<td colspan="2">
							' . _("Aucune playlist n'est rattachée à cet hébergeur") . '
						</td>
					</tr>
		';
	}else{
		while( $playlist = ria_mysql_fetch_assoc($rplaylist) ){
			print '
					<tr>
						<td headers="pls-sel" class="td-checkbox">
							<input type="checkbox" value="'.$playlist['id'].'" name="pls[]" class="checkbox" />
						</td>
						<td headers="pls-name">
							<a title="' . _("Afficher les informations sur cette playlist") . '" href="/admin/documents/medias/playlists/index.php?hst='.$host['id'].'&amp;pls='.$playlist['id'].'">'.htmlspecialchars( $playlist['name'] ).'</a>
						</td>
					</tr>
			';
		}
	}

	print '
				</tbody>
				<tfoot>
					<tr>
						<td colspan="2">
	';
	if( ria_mysql_num_rows($rplaylist) ){
		print '<input type="submit" value="' . _("Supprimer") . '" name="delpls" />';
	}
	print '
							<input type="submit" value="' . _("Ajouter") . '" name="addpls" />
						</td>
					</tr>
				</tfoot>
			</table>
	';

	// Liste des médias
	$rmedia = doc_medias_get( $host['id'], null, 0, 0, null );
	print '
			<table class="checklist">
				<caption>'._('Liste des médias').' ('.( !$rmedia || !ria_mysql_num_rows($rmedia) ? '0' : ria_mysql_num_rows($rmedia)).')</caption>
				<thead>
					<tr>
						<th id="med-sel" class="col-check">
							<input type="checkbox" onclick="checkAllClick(this)" class="checkbox" />
						</th>
						<th id="med-name">' . _("Désignation") . '</th>

					</tr>
				</thead>
				<tfoot>
					<tr>
						<td colspan="2">
	';
	if( ria_mysql_num_rows($rmedia) ){
		print '<input type="submit" value="' . _("Supprimer") . '" name="delmed" />';
	}
	print '
							<input type="submit" value="' . _("Ajouter") . '" name="addmed" />
						</td>
					</tr>
				</tfoot>
				<tbody>
	';

	if( !$rmedia || !ria_mysql_num_rows($rmedia) ){
		print '
					<tr>
						<td colspan="2">
							'. _("Aucun média n'est rattaché à cet hébergeur") . '
						</td>
					</tr>
		';
	}else{
		while( $media = ria_mysql_fetch_assoc($rmedia) ){
			print '
					<tr>
						<td headers="med-sel" class="td-checkbox">
							<input type="checkbox" value="'.$media['id'].'" name="med[]" class="checkbox" />
						</td>
						<td headers="med-name">
							<a title="' . _("Afficher les informations sur ce média") . '" href="/admin/documents/medias/medias/edit.php?hst='.$host['id'].'&amp;med='.$media['id'].'">'.htmlspecialchars( $media['name'] ).'</a>
						</td>
					</tr>
			';
		}
	}

	print '
				</tbody>
			</table>
		</form>
	';

	require_once('admin/skin/footer.inc.php');
