<?php

// \cond onlyria
/**	Retourne les produits enfants d'un produit donné.
 *	@param int $id Identifiant du produit dont on souhaite obtenir les enfants
 *	@param bool $centralized Booléen indiquant si l'on souhaite obtenir tous les produits ou seulement ceux qui sont centralisés
 *	@param bool $published  Booléen indiquant si l'on ne souhaite que les produits publiés ou tous
 *	@param array $sort Optionnel, tri à appliquer au résultat. Par défaut, le résultat est trié par nom. Ce paramètre doit être fourni sous la forme d'un tableau associatif colonne=>direction. Les valeurs autorisées pour la colonne sont : ref, name, price, brand, publish, completion, date-created, date-promo, random, selled. Les valeurs autorisées pour la direction sont : asc, desc.
 *	@param bool $sleep Optionnel, si false returne les produits qui ne sont pas en sommeil, si true return les produits en sommeil, si null retourne tous
 *
 */
function prd_childs_get( $id, $centralized=false, $published=true, $sort=false, $sleep=null ){
	global $config;

	if( !is_numeric($id) ) return false;

	$user = isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : 0;

	if( $config['prd_deposits']=='use-main' ){
		$dps = prd_deposits_get_main();
	}else{
		$dps = isset($_SESSION['usr_dps_id']) ? $_SESSION['usr_dps_id'] : prd_deposits_get_main();
	}

	if (!$dps) $dps = 0;

	if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) ){
		// Permet la consultation sous l'identité d'une autre personne (réservé aux administrateurs et représentants)
		$user = $_SESSION['admin_view_user'];
	}elseif( isset($_SESSION['usr_prf_id']) && $_SESSION['usr_prf_id']==4 && $config['tnt_id']==1 ){
		// les revendeurs Bigship ne voient pas leur prix via cette fonction
		$user = 0;
	}

	if( isset($_SESSION['usr_tnt_id']) && !$_SESSION['usr_tnt_id'] ){
		$user = 0;
	}

	if( !isset($config['prd_new_days']) || !is_numeric($config['prd_new_days']) || $config['prd_new_days'] < 0 ){
		$config['prd_new_days'] = 60;
	}

	$col_new = isset($config['prd_new_date']) && in_array($config['prd_new_date'], array('prd_date_created', 'prd_first_published', 'prd_date_published')) ? $config['prd_new_date'] : 'prd_date_created';

	$sql = 	'
		select distinct prd.prd_id as id, prd_ref as ref, prd_barcode as barcode,
			prd_name as name, if(prd_title="",prd_name,prd_title) as title, prd_desc as "desc", prd_desc_long as "desc-long",
			prd_publish as publish, prd_orderable as orderable, prd_new!="-1" and if( '.$config['prd_new_days'].' != 0, (prd_new="1" or datediff(now(),'.$col_new.')<='.$config['prd_new_days'].'), prd_new="1") as new,
			prd_countermark as countermark, prd_centralized as centralized, prd_ecotaxe as ecotaxe, prd_sleep as sleep,
			date_format(prd_date_published,"%d/%m/%Y à %H:%i") as date_published,
			prd_brd_id as brand, brd_id, brd_img_id, brd_name, if(brd_title!="",brd_title,brd_name) as brd_title,
			prd_weight as weight, prd_length as "length", prd_width as width, prd_height as height,
			'.($config['use_decimal_qte'] ? prd_stocks_get_sql() . '-sto_prepa' : 'cast(' . prd_stocks_get_sql() .'-sto_prepa as signed)' ).' as stock,
			'.($config['use_decimal_qte'] ? prd_stocks_sto_res() : 'cast('.prd_stocks_sto_res().' as signed)' ).' as stock_res,
			'.($config['use_decimal_qte'] ? 'sto_com' : 'cast(sto_com as signed)' ).' as stock_com,
			'.($config['use_decimal_qte'] ? 'sto_prepa' : 'cast(sto_prepa as signed)' ).' as stock_prepa,
			if(prd_stock_livr<ADDDATE(now(), INTERVAL -'.$config['prd_livr_delay_allowed'].' DAY),null,date_format(prd_stock_livr,"%d/%m/%Y")) as stock_livr,
				prd_keywords as keywords, prd_img_id as img_id, prd_weight_net as weight_net,
			date_format(prd_date_created,"%d/%m/%Y à %H:%i") as date_created,
			date_format(prd_date_modified,"%d/%m/%Y à %H:%i") as date_modified,
			prd_sell_weight as sell_weight, prd_purchase_avg as purchase_avg, prd_child_pos as child_pos, prd_nomenclature_type as nomenclature_type
		from prd_hierarchy as hry
			inner join prd_products as prd on (prd_child_id=prd.prd_id and prd.prd_tnt_id='.$config['tnt_id'].' and prd_parent_id='.$id.')
			left join prd_brands on (prd_brd_id=brd_id and brd_tnt_id='.$config['tnt_id'].')
			left join prd_stocks on (prd.prd_id=sto_prd_id and sto_tnt_id='.$config['tnt_id'].' and sto_dps_id='.$dps.' and sto_is_deleted=0)
	';

	// Filtres
	$sql .= 'where hry.prd_tnt_id='.$config['tnt_id'].' and prd_date_deleted is null ';
	if( $config['use_catalog_restrictions'] )
		$sql .= ' and exists ('.prd_restrictions_get_ids( CLS_PRODUCT, 'prd.prd_id' ).')';
	if( $centralized )
		$sql .= ' and prd_centralized!=0';
	if( $published )
		$sql .= ' and prd_publish and prd_publish_cat and (prd_sleep=0 or (' . prd_stocks_get_sql() .'-sto_prepa)>0) ';
	elseif( isset($config['admin_catalog_hide_source_unpublished']) && $config['admin_catalog_hide_source_unpublished']==true )
		$sql .= ' and ( (prd_is_sync and prd_publish) or not prd_is_sync ) ';
	if( $sleep === true )
		$sql .= ' and prd_sleep = 1 ';
	if( $sleep === false )
		$sql .= ' and prd_sleep = 0 ';

	// Tri du résultat (valeurs par défaut)
	if( $sort==false || !is_array($sort) || sizeof($sort)==0 ){
		$sort = array( 'name'=>'asc' );
	}

	// Converti le paramètre de tri en SQL
	$price_sort_dir = '';
	$sort_final = array();
	foreach( $sort as $col=>$dir ){
		$dir = $dir=='asc' ? 'asc' : 'desc';
		switch( $col ){
			case 'ref':
				array_push( $sort_final, 'prd_ref '.$dir );
				break;
			case 'name':
				array_push( $sort_final, 'prd_name '.$dir );
				break;
			case 'brand':
				array_push( $sort_final, 'if(brd_title!="",brd_title,brd_name) '.$dir );
				break;
			case 'completion':
				array_push( $sort_final, 'prd_completion '.$dir );
				break;
			case 'date-created':
				array_push( $sort_final, 'prd_date_created '.$dir );
				break;
			case 'publish':
				array_push( $sort_final, 'prd_publish '.$dir );
				break;
			case 'publish-cat':
				array_push( $sort_final, 'prd_publish_cat '.$dir );
				break;
			case 'random':
				array_push( $sort_final, 'rand()' );
				break;
			case 'selled':
				array_push( $sort_final, 'prd_selled '.$dir );
				break;
			case 'height' :
				array_push( $sort_final, 'prd_height '.$dir );
				break;
			case 'child_pos':
				array_push( $sort_final, 'child_pos '.$dir );
				break;
		}
		if( $col=='price' ){
			$price_sort_dir = $dir;
			break;
		}
	}

	// Ajoute la clause de tri
	//if( $childs ) array_unshift( $sort_final, 'prd_childonly asc' );
	if( sizeof($sort_final)==0 ) $sort_final = array( 'prd_name asc' );
	$sql .= ' order by '.implode( ', ', $sort_final ).' ';

	$exempt = gu_users_is_tva_exempt( $user );
	$usr_holder = gu_users_get_prices_holder( $user );
	$prc = gu_users_get_prc( $usr_holder, true );
	$usr_ref = gu_users_get_ref( $usr_holder, true );

	$apply_remise_ecotaxe = isset($config['apply_remise_ecotaxe']) && !$config['apply_remise_ecotaxe'] ? false : true;

	$sql = '
		select d_tbl.*, (d_tbl.price_ht * d_tbl.tva_rate) as price_ttc from (
		select tmp.*, get_price_ht( '.$config['tnt_id'].', tmp.id, '.$usr_holder.', 1, 0, '.$config['discount_apply_type'].', '.$prc.', '.$config['use_last_known_price'].', "'.wst_websites_languages_default().'", '.$config['cat_id_prices'].', "'.addslashes($usr_ref).'", tmp.brand, tmp.centralized, tmp.ecotaxe ) as price_ht,
		'.( $exempt ? '1' : 'get_tva( '.$config['tnt_id'].', tmp.id, '.gu_users_get_accouting_category( $user, true ).', '.gu_users_get_cnt_code($user, true).' )' ).' as tva_rate from ('.$sql.') as tmp
		) as d_tbl
	';
	if( $price_sort_dir!='' ){
		$sql .= ' order by price_ht '.$price_sort_dir;
	}

	$r = ria_mysql_query($sql);

	// débugage tva
	if( ria_mysql_errno() ){
		error_log('prd_childs_get - '.mysql_error().' - '.$sql);
	}

	return $r;
}
// \endcond

// \cond onlyria
/** Cette fonction retourne un tableau contenant les identifiant des produits enfants d'un produit passé en paramtère. Elle utilise les mêmes paramètre que prd_childs_get().
 *	@param int $id Identifiant du produit dont on souhaite obtenir les enfants
 *	@param bool $centralized Booléen indiquant si l'on souhaite obtenir tous les produits ou seulement ceux qui sont centralisés
 *	@param bool $published  Booléen indiquant si l'on ne souhaite que les produits publiés ou tous
 *	@param bool $sleep Optionnel, si false returne les produits qui ne sont pas en sommeil, si true return les produits en sommeil, si null retourne tous
 *
 *	@return array un tableau de identifiant des produits enfants
 *	@return bool false si aucun produit enfant n'est trouvé
 */
function prd_childs_get_array( $id, $centralized=false, $published=true, $sleep=null ){
	$childs = array();

	$rchild = prd_childs_get( $id, $centralized, $published, false, $sleep );
	if( !$rchild || !ria_mysql_num_rows($rchild) ){
		return false;
	}

	if( $rchild ){
		while( $child = ria_mysql_fetch_array($rchild) ){
			$childs[] = $child['id'];
		}
	}

	return $childs;
}
// \endcond

/** Cette fonction permet de récupérer les identifiants des articles enfants du ou des articles passés en paramètre.
 *	Le résultat de cette fonction est mis en cache pour 15 minutes
 *	@param int $prd Obligatoire, identifiant ou tableau d'identifiants produits
 *	@param bool $publish Optionnel, par défaut tous les enfants sont retournés, mettre True pour avoir ceux publiés, False pour les autres
 *	@return array Un tableau contenant les identifiants des articles enfants
 */
function prd_products_get_childs_ids( $prd, $publish=null ){
	global $config, $memcached;

	if( is_array($prd) ){
		sort( $prd );
		$prds = md5( implode( ',', $prd ) );
	}else{
		$prds = $prd;
	}

	$mkey = 'tnt:'.$config['tnt_id'].':prd_products_get_childs_ids:'.$prds.':publish-'.($publish!==null ? ($publish ? 'yes' : 'no') : '');

	// Si le résultat existe déjà en cache, retourne le résultat en cache
	$mget = $memcached->get( $mkey );
	if( $mget && $mget!='' ){
		return $mget;
	}

	$ar_childs = array();

	$prd_ids = control_array_integer( $prd );
	if( $prd_ids === false ){
		return $ar_childs;
	}

	global $config;

	$sql = '
		select h.prd_child_id
		from prd_hierarchy as h
	';

	if ($publish !== null) {
		$sql .= ' join prd_products as p on (h.prd_tnt_id = p.prd_tnt_id and h.prd_child_id = p.prd_id)';
	}

	$sql .= '
		where h.prd_tnt_id = '.$config['tnt_id'].'
			and h.prd_parent_id in ('.implode( ', ', $prd_ids ).')
	';

	if ($publish !== null) {
		if ($publish) {
			$sql .= ' and prd_publish = 1 and prd_publish_cat = 1';
		}else{
			$sql .= ' and (prd_publish = 0 or prd_publish_cat = 0)';
		}
	}

	$res = ria_mysql_query( $sql );
	if( $res ){
		while( $r = ria_mysql_fetch_assoc($res) ){
			$ar_childs[] = $r['prd_child_id'];
		}
	}

	$ar_childs = array_unique( $ar_childs );

	// Stocke le résultat dans memcached pour usage ultérieur
	$memcached->set( $mkey, $ar_childs, 60 * 15 );

	return $ar_childs;
}

