<?php

/**	\file edit-team.php
 * 	
 * 	Ce fichier fournit une interface permettant la mise à jour de l'équipe composant un magasin
 * 
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_STORE');

require_once('delivery.inc.php');

$page = !isset($_GET['page']) || !is_numeric($_GET['page']) ? 1 : $_GET['page'];
// Vérification de l'identifiant du magasin
if( isset($_GET['str']) && $_GET['str']!='' ){
	if( !dlv_stores_exists($_GET['str']) ){
		header('Location: index.php?page='.$page);
		exit;
	}
}

// Sauvegarde de l'équipe
do{
	if(isset($_POST['add-employee'])){
		$data = $_POST;
	}elseif( isset($_GET['team']) ){
		$res = dlv_store_teams_get($_GET['str'], $_GET['team']);
		if( !$res ){
			header('location: index.php');
			exit;
		}
		$data = ria_mysql_fetch_assoc($res);
	}else{
		$data = array(
			'firstname' => '',
			'lastname' => '',
			'email' => '',
			'job_id' => 0,
			'srv_id' => 0,
			'img_id' => 0
		);
	}
	if( isset($_POST['add-employee']) ){
		$errors = array();
		if( !is_string($_POST['firstname']) || trim($_POST['firstname']) == '' ){
			$errors[] = _('Il faut un prenom.');
		}
		if( !is_string($_POST['lastname']) || trim($_POST['lastname']) == '' ){
			$errors[] = _('Il faut un nom.');
		}
		if( trim($_POST['email']) != '' && !isemail($_POST['email']) ){
			$errors[] = _('Adresse email invalide.');
		}
		if( !is_numeric($_POST['job_id']) ){
			$errors[] = _('Le poste est incorrect.');
		}
		if( count($errors) ){
			break;
		}
		$firstname = htmlspecialchars($_POST['firstname']);
		$lastname = htmlspecialchars($_POST['lastname']);
		$email = htmlspecialchars($_POST['email']);
		$job_id = $_POST['job_id'];
		$srv_id = isset($_POST['srv_id']) ? $_POST['srv_id'] : 0;
		
		if( isset($_GET['team']) ){
			if( !dlv_store_teams_update($_GET['str'], $_GET['team'], $firstname, $lastname, $email, $job_id, $srv_id ) ){
				$errors[] = _('Une erreur est survenue lors de l\'enregistrement, veuillez réessayer ou contacter l\'administrateur.');
				break;
			}
			if( $_FILES['profile-img']['error']===0 ){
				if( !dlv_store_team_add_img( $_GET['team'], 'profile-img' ) ){
					$errors[] = _('Une erreur est survenue lors de l\'enregistrement.');
					break;
				}
			}
			header('location: edit-team.php?str='.$_GET['str'].'&team='.$_GET['team'].'&success=true');
			exit;
		}
		
		if( !$id = dlv_store_team_add( $_GET['str'], $firstname, $lastname, $email, $job_id, $srv_id ) ){
			$errors[] = _('Une erreur est survenue lors de l\'enregistrement.');
			break;
		}
		if( $_FILES['profile-img']['error']===0 ){
			if( !dlv_store_team_add_img( $id, 'profile-img' ) ){
				dlv_store_teams_del($_GET['str'], $id);
				$errors[] = _('Une erreur est survenue lors de l\'enregistrement.');
				break;
			}
		}
		header('location: edit.php?str='.$_GET['str'].'&tab=team&page='.$page);
		exit;
	}
	
	if( isset($_POST['del-employee'], $_GET['team']) ){
		$res = dlv_store_teams_get($_GET['str'], $_GET['team']);
		$row = ria_mysql_fetch_assoc($res);
		if( !img_images_del($row['img_id'], true) ){
			$error = _('Une erreur est survenue lors de la suppression d\'employé.');
			break;
		}
		if( !dlv_store_teams_del($_GET['str'], $_GET['team']) ){
			$error = _('Une erreur est survenue lors de la suppression d\'employé.');
			break;
		}
		if( !isset($error) ){
			$success = _('La suppression a bien fonctionné.');
		}
	}
	if(isset($_POST['cancel-employee'])){
		header('location: edit.php?str='.$_GET['str'].'&tab=team&page='.$page);
		exit;
	}

	if( isset($_POST['del-profile-img'], $_POST['img-id']) ){
		if( !dlv_store_team_del_img($_GET['team'], $_POST['img-id']) ){
			$error = _('Une erreur est survenue lors de la suppression de la photo de l\'employé.');
			break;
		}
		header('location: edit-team.php?str='.$_GET['str'].'&team='.$_GET['team'].'&success=pic');
			exit;
	}
}while(0);


define('ADMIN_PAGE_TITLE', _('Magasins') . ' - ' . _('Livraison des commandes') . ' - ' . _('Configuration'));
require_once('admin/skin/header.inc.php');
?>
	<h2><?php echo (isset($_GET['team']) ? _('Modifier') : _('Ajouter'))?> <?php echo _("un employé"); ?></h2>
<?php
	if( isset($_GET['success']) && $_GET['success']=='true' ){
		echo '<div class="notice success">' . _('L\'enregistrement c\'est bien déroulé.') . '</div>';
	}elseif( isset($_GET['success']) && $_GET['success']=='pic' ){
		echo '<div class="notice success">' . _('La suppression de la photo c\'est bien déroulé.') . '</div>';
	}elseif( isset($errors) && count($errors) ){
		echo '<div class="error">';
		foreach($errors as $error){
			echo '<p>'.$error.'</p>';
		}
		echo '</div>';
	}
?>
<form action="" method="post" enctype="multipart/form-data">
<table class="edit-employee">
	<tbody>
		<tr>
			<td><span class="mandatory">*</span> <label for="firstname"><?php echo _('Prénom :'); ?></label></td>
			<td><input type="text" name="firstname" id="firstname" value="<?php echo $data['firstname']?>"></td></tr>
		<tr>
			<td><span class="mandatory">*</span> <label for="lastname"><?php echo _('Nom :'); ?></label></td>
			<td><input type="text" name="lastname" id="lastname" value="<?php echo $data['lastname']?>"></td></tr>
		<tr>
			<td><label for="email"><?php echo _('Email :'); ?></label></td>
			<td><input type="email" name="email" id="email" value="<?php echo $data['email']?>"></td></tr>
		<tr>
			<td><label for="srv_id"><?php echo _('Service :'); ?></label></td>
			<td><select type="text" name="srv_id" id="srv_id">
					<?php
					$res = dlv_store_services_get();
					if( !$res ){
						echo '<option value="0">' . _("Aucun service de travail") . '</option>';
					}else{
						while( $row = ria_mysql_fetch_assoc($res) ){
							echo '<option value="'.$row['id'].'" '.($data['srv_id']===$row['id'] ? 'selected="selected"' : '').'>'.htmlspecialchars( $row['name'] ).'</option>';
						}
					}
					?>
				</select></td>
		</tr>
		<tr>
			<td><span class="mandatory">*</span> <label for="job_id"><?php echo _('Poste :'); ?></label></td>
			<td><select type="text" name="job_id" id="job_id">
					<?php
					$res = dlv_store_jobs_get();
					if( !$res ){
						echo '<option value="">' . _("Aucun poste de travail") . '</option>';
					}else{
						while( $row = ria_mysql_fetch_assoc($res) ){
							echo '<option value="'.$row['id'].'" '.($data['job_id']===$row['id'] ? 'selected="selected"' : '').'>'.htmlspecialchars( $row['name'] ).'</option>';
						}
					}
					?>
				</select></td>
		</tr>
		<tr>
			<td><label><?php echo _('Photo :'); ?></label></td>
			<td>
				<?php if( isset($data['img_id']) && $data['img_id']!=0 ) { ?>
					<img src="<?php echo $config['img_url'].'/'.$config['img_sizes']['medium']['dir'].'/'.$data['img_id'].'.jpg'?>" width="<?php echo $config['img_sizes']['medium']['width']?>" height="<?php echo  $config['img_sizes']['medium']['height']?>" style="margin-bottom: 10px;"/>
					<input type="hidden" name="img-id" value="<?php echo $data['img_id']?>">
					<div>
						<input type="submit" name="del-profile-img" value="Supprimer la photo">
					</div>
				<?php } ?>	
				<div>
					<input type="file" name="profile-img" />
				</div>
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td class="tdleft">
				<?php if(isset($_GET['team']) ){ ?><input type="submit" name="del-employee" value="<?php echo _("Supprimer"); ?>"><?php } ?>
				<input type="submit" name="cancel-employee" value="<?php echo _("Annuler"); ?>">
			</td>
			<td><input type="submit" value="<?php echo (isset($_GET['team']) ? _('Enregistrer') : _('Ajouter'))?>" name="add-employee"></td>
		</tr>
	</tfoot>
</table>
</form>

<?php
require_once('admin/skin/footer.inc.php');
?>