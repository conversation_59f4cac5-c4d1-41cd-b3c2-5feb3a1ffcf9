<?php

/**	\defgroup gu_sell_units Gestion de l'unite de vente (PCB - Par combien) des clients
 *	\ingroup model_users
 *  Les règles d'unité de vente permet de définir pour un produit le "par combien" il est possible d'acheter l'article.
 *  Cette règle peut être générique, elle sera alors renseignée un champ avancé générique du produit : _FLD_PRD_SALES_UNIT.
 *
 *  Elle peut être mise en place sur :
 *    - le produit
 *    - le conditionnement d'un produit
 *    - la catégorie d'un produit (la profondeur de classement est prise en compte)
 *    - une marque
 *
 *  L'ordre de priorité est la suivante :
 *     conditionnement > produit > catégorie > marque
 *
 * 	Voici tous les codes erreur qui peuvent être retournés par les fonctions de ce module :
 * 		- -1  : identifiant de la règle incorrect
 * 		- -2  : identifiant client incorrect
 * 		- -3  : identifiant de la classe incorrect
 * 		- -4  : identifiant d'objet incorrect
 * 		- -5  : unité de vente incorrect
 * 		- -6  : date de dernière modification incorrect
 * 		- -7  : information de synchronisation incorrecte
 * 		- -98 : la règle n'existe pas
 * 		- -99 : une erreur inconnue est survenue
 *
 *	Les fonctions de ce module permettent de gérer l'unite de vente (PCB - Par combien) des clients
 *	@{
 */


// \cond onlyria
/** Cette fonction permet de restreindre la mise en place de règles qu'à certaines type d'objets. Voici la liste :
 *    - le produit
 *    - le conditionnement d'un produit
 *    - la catégorie d'un produit
 *    - une marque
 */
function gu_sell_units_rules_get_classes(){
	return [ CLS_PRODUCT, CLS_PRD_COLISAGE, CLS_CATEGORY, CLS_BRAND ];
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajout une nouvelle règle d'unité de vente.
 *  Si une règle est déjà en place, elle sera mise à jour.
 * 	@param int $usr_id Obligatoire, identifiant du client
 * 	@param int $cls_id Obligatoire, classe d'objet sur lequel la règle est appliquée
 * 	@param int|array $obj Obligatoire, tableau d'identifiant de l'objet
 * 	@param float $sell_unit Obligatoire, $sell_unit Obligatoire, unité de vente
 * 	@param int $is_sync Optionnel, si oui (1) ou non (0) la règle est synchronisée (par défaut à 0)
 *
 * 	@return int L'identifiant de la nouvelle règle si l'ajout s'est correcte déroulé, sinon un des codes erreur suivant :
 * 		- -2 : identifiant client incorrect
 * 		- -3 : identifiant de la classe incorrect
 * 		- -4 : identifiant d'objet incorrect
 * 		- -5 : unité de vente incorrect
 * 		- -7  : information de synchronisation incorrecte
 * 		- -99 : erreur lors de l'ajout de la règle
 */
function gu_sell_units_rules_add( $usr_id, $cls_id, $obj, $sell_unit, $is_sync=0 ){
	global $config;

	if( !is_numeric($usr_id) || $usr_id <= 0 ){
		return -2;
	}

	if( !in_array($cls_id, gu_sell_units_rules_get_classes()) ){
		return -3;
	}

	$obj = control_array_integer( $obj, true, true, true );
	if( $obj === false ){
		return -4;
	}

	if( !is_numeric($sell_unit) || $sell_unit <= 0 ){
		return -5;
	}

	if( !is_numeric($is_sync) || $is_sync < 0 ){
		return -7;
	}

	// Si une règle est déjà en place, celle-ci est mise à jour
	// Le contrôle d'existance ne tient pas compte de si la règle est synchronisée ou non, par contre la mise à jour oui.
	// Il n'est possible de mettre à jour une règle synchronise que si l'ajout est fait par la synchronisation.
	// S'il ne s'agit pas d'une synchronisation et que la règle est synchronisé, son identifiant est retourné, mais elle n'est pas mise à jour.
	$exists = gu_sell_units_rules_exists( $usr_id, $cls_id, $obj );
	if( $exists !== false ){
		$res = gu_sell_units_rules_upd( $exists, $sell_unit, ($is_sync === 1) );
		return $res === true ? $exists : $res;
	}

	// Initialise un tableau de colonnes et de valeur
	$fields = $values = [];

	$fields[] = 'sur_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'sur_usr_id';
	$values[] = $usr_id;

	$fields[] = 'sur_cls_id';
	$values[] = $cls_id;

	$i = 0;
	foreach( $obj as $o ){
		$fields[] = 'sur_obj_id_'.$i;
		$values[] = $o;
		$i++;
	}

	$fields[] = 'sur_sell_unit';
	$values[] = str_replace( array(' ',','), array('','.'), $sell_unit );

	$fields[] = 'sur_date_created';
	$values[] = 'now()';

	$fields[] = 'sur_is_sync';
	$values[] = $is_sync;

	// Ajout de la règle en base
	$sql = '
		insert into gu_sell_units_rules
			( '.implode( ', ', $fields ).' )
		values
			( '.implode(', ', $values ).' )
	';

	$res = ria_mysql_query($sql);
	return $res ? ria_mysql_insert_id() : -99;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour une règle d'unité de vente.
 *	@param int $rule_id Obligatoire, identifiant d'une règle
 * 	@param float $sell_unit Obligatoire, unité de vente
 *	@param bool $sync Optionnel, permet de signaler que la mise à jour est faite par la synchronisation (par défaut à false)
 *
 * 	@return bool|int true dans le cas où la règle a bien été mise à jour, sinon un des codes erreur suivant :
 * 		- -1 : identifiant de la règle incorrect
 * 		- -2 : unité de vente incorrect
 * 		- -98 : la règle n'existe pas
 * 		- -99 : erreur lors de la mise à jour
 */
function gu_sell_units_rules_upd( $rule_id, $sell_unit, $sync=false ){
	global $config;

	if( !is_numeric($rule_id) || $rule_id <= 0 ){
		return -1;
	}
	if( !is_numeric($sell_unit) || $sell_unit <= 0 ){
		return -5;
	}

	$r_exists = ria_mysql_query('
		select 1
		from gu_sell_units_rules
		where sur_tnt_id = '.$config['tnt_id'].'
			and sur_date_deleted is null
			and sur_id = '.$rule_id.'
	');

	if( !$r_exists || !ria_mysql_num_rows($r_exists) ){
		return -98;
	}

	$sql = '
		update gu_sell_units_rules
		set sur_sell_unit = '.str_replace( array(' ',','), array('','.'), $sell_unit ).',
			sur_is_sync = '.( $sync ? 1 : 0 ).'
		where sur_tnt_id = '.$config['tnt_id'].'
			and sur_date_deleted is null
			and sur_id = '.$rule_id.'
	';

	if( !$sync ){
		$sql .= ' and sur_is_sync = 0';
	}

	$res = ria_mysql_query( $sql );

	return $res ? true : -99;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer une ou plusieurs règles d'unité de vente.
 *	@param int $rule_id Obligatoire, identifiant ou tableau d'identifiants de règles à supprimer
 *	@param bool $sync Optionnel, permet de signaler que la suppression est faite par la synchronisation (par défaut à false)
 *
 *	@return bool|int true dans le cas où la suppression s'est correctement déroulée, sinon un des codes erreur suivant :
 *		- -1 : identifiant de la règle incorrect
 *		- -99 : erreur lors de la suppression de la règle
 */
function gu_sell_units_rules_del( $rule_id, $sync=false ){
	global $config;

	$rule_id = control_array_integer( $rule_id, true );
	if( $rule_id === false ){
		return -1;
	}

	$sql = '
		update gu_sell_units_rules
		set sur_date_deleted = now()
		where sur_tnt_id = '.$config['tnt_id'].'
			and sur_id in ('.implode( ', ', $rule_id ).')
	';

	// Si la suppression n'est pas faite par la synchronisation
	// Alors on ajoute une condition supplémentaire empêchant la suppression des règles sycnhronisées
	if( !$sync ){
		$sql .= ' and sur_is_sync = 0';
	}

	$res = ria_mysql_query( $sql );

	return $res ? true : -99;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier qu'une règle d'unité de vente existe bien. */
function gu_sell_units_rules_exists( $usr_id, $cls_id, $obj ){
	global $config;

	$rule_id = false;

	$r_rule = gu_sell_units_rules_get( 0, $usr_id, $cls_id, $obj );
	if( $r_rule && ria_mysql_num_rows($r_rule) ){
		$rule = ria_mysql_fetch_assoc( $r_rule );
		$rule_id = $rule['id'];
	}

	return $rule_id;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les règles d'unité de vente selon certains critères.
 *	@param int $rule_id Optionnel, identifiant d'une règle
 * 	@param int $usr_id Optionnel, identifiant du client
 * 	@param int $cls_id Optionnel, classe d'objet sur lequel la règle est appliquée
 * 	@param int|array $obj Optionnel, tableau d'identifiant de l'objet
 * 	@param bool|string $last_modified Optionnel, date de dernière modification (seules les règles créée ou modifié après cette date inclues seront
 * retournées). La date doit être fournie au format suivant AAAA-MM-JJ [HH:MM:SS] (heure optionnel)
 * 	@param array $compl_data Optionnel, permet de compléter les données retournées par la fonction, valeurs acceptées :
 * 		- user : retourne des données supplémentaire sur le client ($compl_data = ['user' => true])
 * 								- firstname : prénom
 * 								- lastname : nom
 * 								- society : société
 * 								- email : adresse mail
 * 	@param array $sort Optionnel, permet de trier le résultat (par défaut, dernière règle créée), le tri se fait sur les colonnes du select (alias)
 * 	@param array $limit Optionnel, permet de retourner un résultat paginé, clé acceptée 'offset' et 'limit', par défault 'offset' sera à 0 et 'limit' au maximum
 *
 * 	@return resource En cas de succès une ressource MySQL contenant pour chaque tuple :
 * 		- id : identifiant de la règle
 * 		- usr_id : identifiant du compte client
 * 		- cls_id : identifiant de la classe
 * 		- obj_id_1 : partie 1 de la clé de l'objet
 * 		- obj_id_2 : partie 2 de la clé de l'objet
 * 		- obj_id_3 : partie 3 de la clé de l'objet
 * 		- sell_unit : unité de vente
 * 		- date_created : date de création
 * 		- date_modified : date de dernière modification
 * 		- is_sync : si oui (1) ou non (0) la règle est synchronisée
 *
 * 	@return int En cas d'ereur, un des codes erreur suivant :
 * 		- -1  : identifiant de la règle incorrect
 * 		- -2  : identifiant client incorrect
 * 		- -3  : identifiant de la classe incorrect
 * 		- -4  : identifiant d'objet incorrect
 * 		- -6  : date de dernière modification incorrect
 * 		- -99 : erreur survenue lors de la récupération des règles
 */
function gu_sell_units_rules_get( $rule_id=0, $usr_id=0, $cls_id=[], $obj=0, $last_modified=false, $compl_data=[], $sort=[], $limit=[] ){
	global $config;

	if( !is_numeric($rule_id) || $rule_id < 0 ){
		return -1;
	}

	if( !is_numeric($usr_id) || $usr_id < 0 ){
		return -2;
	}

	$cls_id = control_array_integer( $cls_id, false );
	if( $cls_id === false ){
		return -3;
	}

	if( count($cls_id) ){
		foreach( $cls_id as $cls ){
			if( !in_array($cls, gu_sell_units_rules_get_classes()) ){
				return -3;
			}
		}
	}

	$obj = control_array_integer( $obj, false, true, true );
	if( $obj === false ){
		return -4;
	}

	if( $last_modified !== false ){
		if( !isdateheure($last_modified) ){
			return -6;
		}

		// Complète avec une heure pour la recherche s'il s'agit juste d'une date
		if( isdate($last_modified) ){
			$last_modified .= ' 00:00:00';
		}
	}

	if( !is_array($compl_data) ){
		$compl_data = [];
	}

	$sql = '
		select
			sur_id as id, sur_usr_id as usr_id, sur_cls_id as cls_id, sur_obj_id_0 as obj_id_0, sur_obj_id_1 as obj_id_1, sur_obj_id_2 as obj_id_2,
			sur_sell_unit as sell_unit, sur_date_created as date_created, sur_date_modified as date_modified, sur_is_sync as is_sync
	';

	// Complète les données avec celle du client
	if( array_key_exists('user', $compl_data) ){
		$sql .= '
			, adr_firstname as firstname, adr_lastname as lastname, adr_society as society, usr_email as email
		';
	}

	$sql .= '
		from gu_sell_units_rules
	';

	// Jointure supplémentaire pour le complément d'information
	if( array_key_exists('user', $compl_data) ){
		// Complément d'information lié au client
		$sql .= '
			join gu_users on (usr_tnt_id = '.$config['tnt_id'].' and usr_id = sur_usr_id)
			join gu_adresses on (adr_tnt_id = '.$config['tnt_id'].' and adr_id = usr_adr_invoices)
		';
	}

	$sql .= '
		where sur_tnt_id = '.$config['tnt_id'].'
			and sur_date_deleted is null
	';

	if( $rule_id > 0 ){
		$sql .= ' and sur_id = '.$rule_id;
	}

	if( count($cls_id) ){
		$sql .= ' and sur_cls_id in ('.implode( ', ', $cls_id ).')';
	}

	if( $usr_id > 0 ){
		$sql .= ' and sur_usr_id = '.$usr_id;
	}

	if( count($obj) > 0 ){
		$i = 0;
		foreach( $obj as $o ){
			$sql .= ' and sur_obj_id_'.$i.' = '.$o;
		}
	}

	if( $last_modified !== false ){
		$sql .= ' and sur_date_modified >= "'.addslashes( $last_modified ).'"';
	}

	$sort_final = [];
	if( is_array($sort) ){
		// Colonne de tri autorisée
		$col_authorized = ['id', 'usr_id', 'cls_id', 'obj_id_0', 'obj_id_1', 'obj_id_2', 'sell_unit', 'date_created', 'date_modified', 'is_sync'];

		// Plus de colonnes de tri selon les informations complémentaires demandées
		if( array_key_exists('user', $compl_data) ){
			$col_authorized = array_merge( $col_authorized, ['firstname', 'name', 'lastname', 'society', 'email'] );
		}

		foreach( $sort as $col=>$dir ){
			$dir = in_array( $dir, ['asc', 'desc'] ) ? $dir : 'asc';

			if( in_array($col, $col_authorized) ){
				array_push( $sort_final, $col.' '.$dir );
			}
		}
	}

	if( !count($sort_final) ){
		$sort_final = ['date_created desc'];
	}

	$sql .= ' order by '.implode( ', ', $sort_final );

	// Pagination
	$offset = 0;
	$max = '18446744073709551615';
	if( array_key_exists('offset', $limit) && is_numeric($limit['offset']) && $limit['offset'] >= 0 ){
		$offset = $limit['offset'];
	}
	if( array_key_exists('limit', $limit) && is_numeric($limit['limit']) && $limit['limit'] >= 0 ){
		$max = $limit['limit'];
	}

	$sql .= ' limit '.$offset.', '.$max;

	$res = ria_mysql_query( $sql );

	return $res ? $res : -99;
}
// \endcond

/** Cette fonction permet de récupérer pour un client et un produit l'unité de vente en vigueur.
 * 	Elle respecte l'ordre de priorité : conditionnement > produit > catégorie (niveau de hiérarchie prit en compte) > marque.
 *
 * 	@param int $usr_id Obligatoire, identifiant d'un compte client
 * 	@param int $prd_id Obligatoire, identifiant d'un produit
 * 	@param int $col_id Optionnel, identifiant d'un conditionnement rattaché au produit
 *
 * 	@return int|null L'unité de vente si celle-ci existe sinon null
 */
function gu_sell_units_rules_get_for_product( $usr_id, $prd_id, $col_id=0 ){
	global $config;

	// Chargement de toutes les catégories dans lesquelles le produit est classé
	// Pour chaque classement, on récupère les catégories parentes
	$ar_cat_ids = prd_products_categories_get_array( $prd_id );
	if( count($ar_cat_ids) ){
		$ar_parent_ids = prd_categories_parents_get_array( $ar_cat_ids, true );
		if( count($ar_parent_ids) ){
			$ar_cat_ids = array_merge( $ar_cat_ids, $ar_parent_ids );
		}

		$ar_cat_ids = array_unique( $ar_cat_ids );
	}

	// Récupère les règles directement sur le produit
	$sql = '
		select 1 as priority, "prd" as type, 99 as depth, sur_sell_unit as sell_unit
		from gu_sell_units_rules
		where sur_tnt_id = '.$config['tnt_id'].'
			and sur_date_deleted is null
			and sur_usr_id = '.$usr_id.'
			and sur_cls_id = '.CLS_PRODUCT.'
			and sur_obj_id_0 = '.$prd_id.'
	';

	// Récupère les règles directement sur le conditionnement d'un produit
	if( $col_id > 0 ){
		$sql .= '
			union

			select 0 as priority, "col" as type, 99 as depth, sur_sell_unit as sell_unit
			from gu_sell_units_rules
			where sur_tnt_id = '.$config['tnt_id'].'
				and sur_date_deleted is null
				and sur_usr_id = '.$usr_id.'
				and sur_cls_id = '.CLS_PRD_COLISAGE.'
				and sur_obj_id_0 = '.$prd_id.'
				and sur_obj_id_1 = '.$col_id.'
		';
	}

	// Récupère les règles sur les catégories où le produit est classé
	// Le niveau de profondeur est récupéré car plus la catégorie est profonte, plus la priorité est haute
	// /!\ Il n'est pas possible de définir une unité de vente sur cat_root
	if( count($ar_cat_ids) ){
		$sql .= '
			union

			select 2 as priority, "cat" as type, ifnull(cat_parent_depth, -1) as depth, sur_sell_unit as sell_unit
			from gu_sell_units_rules
				left join prd_cat_hierarchy on (
						cat_tnt_id = '.$config['tnt_id'].'
		';

		if( is_numeric($config['cat_root']) && $config['cat_root'] > 0 ){
			$sql .= '
						and cat_parent_id = '.$config['cat_root'].'
			';
		}

		$sql .= '
					and cat_child_id = sur_obj_id_0
				)
			where sur_tnt_id = '.$config['tnt_id'].'
				and sur_date_deleted is null
				and sur_usr_id = '.$usr_id.'
				and sur_cls_id = '.CLS_CATEGORY.'
				and sur_obj_id_0 in ('.implode( ', ', $ar_cat_ids ).')
		';
	}

	// Récupère les règles sur la marque du produit
	$sql .= '
		union

		select 3 as priority, "brd" as type, 99 as depth, sur_sell_unit as sell_unit
		from gu_sell_units_rules
			join prd_products on (prd_tnt_id = '.$config['tnt_id'].' and prd_id = '.$prd_id.' and prd_brd_id = sur_obj_id_0)
		where sur_tnt_id = '.$config['tnt_id'].'
			and sur_date_deleted is null
			and sur_usr_id = '.$usr_id.'
			and sur_cls_id = '.CLS_BRAND.'
	';

	// Le tri se fera sur le niveau de priorité (asc), puis sur le niveau de hiérarchie (desc, plus bas niveau > plus haut niveau)
	// Si plusieurs règles s'appliquent sur le produit avec le même niveau de priorité, celle avec la plus petite unité est retournée en première
	$res_sql = ria_mysql_query('
		select sell_unit
		from (
			'.$sql.'
		) as res
		order by priority asc, depth desc, sell_unit asc
		limit 1
	');

	$sell_unit = null;

	if( $res_sql && ria_mysql_num_rows($res_sql) ){
		$res = ria_mysql_fetch_assoc( $res_sql );
		$sell_unit = $res['sell_unit'];
	}

	return $sell_unit;
}

/// @}