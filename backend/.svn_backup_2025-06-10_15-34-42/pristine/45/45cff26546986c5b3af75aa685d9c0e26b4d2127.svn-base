<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/timeofday.proto

namespace GPBMetadata\Google\Type;

class Timeofday
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0aed010a1b676f6f676c652f747970652f74696d656f666461792e70726f" .
            "746f120b676f6f676c652e74797065224b0a0954696d654f66446179120d" .
            "0a05686f757273180120012805120f0a076d696e75746573180220012805" .
            "120f0a077365636f6e6473180320012805120d0a056e616e6f7318042001" .
            "2805426c0a0f636f6d2e676f6f676c652e74797065420e54696d654f6644" .
            "617950726f746f50015a3e676f6f676c652e676f6c616e672e6f72672f67" .
            "656e70726f746f2f676f6f676c65617069732f747970652f74696d656f66" .
            "6461793b74696d656f66646179f80101a20203475450620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

