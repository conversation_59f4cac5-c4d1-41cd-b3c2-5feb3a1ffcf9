<?php
/** 
 * \defgroup api-devices-rights Droits utilisateur Yuto 
 * \ingroup Yuto 
 * @{		
 * \page api-devices-rights-get Chargement
 * 
 *	 Cette fonction récupère les droits de l'utilisateur yuto
 *
 *	\code
 *		GET /devices/rights/
 *	\endcode
 *
 * @return json, Liste de droits avec les colonnes :
 * 		\code{.json}
 *       {
 *           "id": Identifiant,
 *           "code": code,
 *           "name": Nom,
 *           "desc": Description
 *       },
 * 		\endcode
 * 
 * @}
*/
switch( $method ){
	case 'get':

	$result = true;

		// Récupération des droits spécifiques à Yuto
		$rrgh = gu_users_load_yuto_rights( true, $config['usr_id'] ); 
		if( $rrgh ){
			foreach( $rrgh as $id => $code ){
				$content[] = array( 
					'id' => $id,
					'code' => $code,
					'name' => '', 
					'desc' => ''
				);
			}
		}

		break;
}
