<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagThing;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au Tag Faq pour les données de la faq
 */
class TagFaq extends TagThing {
	/**
	 * Le type de tag
	 *
	 * @var string $type
	 */
	protected $type = "FAQPage";

	/**
	 * Cette fonction permet d'initialisé le champ name de l'organisation
	 *
	 * @param string $name le nom de l'organisation
	 * @return self retourne l'instance
	 */
	public function addQuestion(TagQuestion $Question){
		$this->fields['mainEntity'][] = $Question;

		return $this;
	}
}