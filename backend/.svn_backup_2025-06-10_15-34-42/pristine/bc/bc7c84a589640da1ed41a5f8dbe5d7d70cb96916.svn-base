<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd">
  <services>
    <service id="foo" class="FooClass">
      <argument type="service">
        <service class="BarClass">
          <argument type="service">
            <service class="BazClass">
            </service>
          </argument>
        </service>
      </argument>
      <property name="p" type="service">
        <service class="BuzClass" />
      </property>
    </service>
    <service id="bar" parent="foo" />
    <service id="biz" class="BizClass">
        <tag name="biz_tag" />
    </service>
  </services>
</container>
