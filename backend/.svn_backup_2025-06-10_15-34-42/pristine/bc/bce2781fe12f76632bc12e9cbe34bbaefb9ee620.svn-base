<?php
require_once('define.inc.php');
require_once('orders.inc.php');
require_once('ord.bl.inc.php');
require_once('ord.returns.inc.php');


/**	Cette fonction signe un object (bon de livraison, bon de retour, ...)
 *	\param $cls Obligatoire, identifiant du type de l'object
 *	\param $obj_0 Obligatoire, identifiant de l'object
 *	\param $obj_1 Obligatoire, identifiant de l'object
 *	\param $obj_2 Obligatoire, identifiant de l'object
 *	\param $signature Obligatoire, signature au format text
 *	\param $usr_id Optionnel, identifiant de l'utilisateur signataire
 *	\param $firstname Optionnel, prénom du signataire
 *	\param $lastname Optionnel, nom du signataire
 *	\param $function Optionnel, fonction / poste du signataire dans la société
 *
 *	\return True en cas de succès, False en cas d'échec. Si la commande est déjà signée, la fonction retournera False
 */
function obj_signature_add( $cls=0, $obj_0=0, $obj_1=0, $obj_2=0,$signature, $usr_id=null, $firstname=null, $lastname=null, $function=null ){

	global $config;

	if( !is_numeric($cls) ) return false;
	if( !is_numeric($obj_0) ) return false;
	if( !is_numeric($obj_1) ) return false;
	if( !is_numeric($obj_2) ) return false;


	$signature = trim($signature);
	if( $signature == '' ){
		return false;
	}else{
		$signature = '"'.addslashes($signature).'"';
	}

	if( $usr_id === null ){
		$usr_id = 'NULL';
	}elseif( !gu_users_exists( $usr_id ) ){
		return false;
	}

	$firstname = $firstname === null ? 'NULL' : '"'.addslashes($firstname).'"';
	$lastname = $lastname === null ? 'NULL' : '"'.addslashes($lastname).'"';
	$function = $function === null ? 'NULL' : '"'.addslashes($function).'"';

	$sql = '
		insert into obj_signatures
			(sig_tnt_id, sig_cls_id, sig_obj_id_0, sig_obj_id_1, sig_obj_id_2, sig_signature, sig_usr_id, sig_firstname, sig_lastname, sig_function)
		values
			('.$config['tnt_id'].', '.$cls.', '.$obj_0.', '.$obj_1.', '.$obj_2.', '.$signature.', '.$usr_id.', '.$firstname.', '.$lastname.', '.$function.')
	';

	$res = ria_mysql_query($sql);

	if( ria_mysql_errno() ){
		error_log('obj_signature_add '.mysql_error().' - '.$sql);
		return false;
	}


	if( $res ){
		fld_objects_set_date_modified( $cls, $obj_0 );
	}

	return $res;
}

/**	Cette fonction recupèrer les informations concernant un object signé (bon de livraison, bon de retour, ...)
 *	@param int $cls Obligatoire, identifiant du type de l'object
 *	@param mixed $obj Obligatoire, identifiant de l'object
 *	@param bool $multi_key Optionnel, si $obj contient plusieurs objet, par défaut à false
 *	@param mixed $type Optionnel, permet de récupérer la signature du représentant (mettre 1) ou du client (mettre 2), par défaut null
 *
 *	@return ressource|bool false en cas d'erreur, sinon un résultat MySQL contenant :
 *			- id : identifiant de la signature
 *			- cls_id : la classe de l'objet signé
 *			- obj_id_0 : identifiant de l'objet
 *			- obj_id_1 : identifiant de l'objet
 *			- obj_id_2 : identifiant de l'objet
 *			- signature : canvas de la signature
 *			- usr_id : identifiant du compte ayant signé
 *			- firstname : prénom de la personne ayant signée
 *			- lastname : nom de la personne ayant signée
 *			- function : poste occupé par la personne ayant signée
 *			- type : type de compte (null : non communiqué, 1 : représentant ou administrateur, 2 : client)
 */
function obj_signature_get( $cls=0, $obj=null, $multi_key=false, $type=null ){
	global $config;

	if( $multi_key ){
		if( !is_array($obj) ){
			return false;
		}
		foreach( $obj as $k => $o ){
			$obj[$k] = control_array_integer( $o, true, true );
			if( $obj[$k] === false ){
				return false;
			}elseif( !$obj[$k][0] ){
				return false;
			}
		}
	}else{
		if( is_numeric($obj) ){
			$obj = array($obj);
		}

		$obj = control_array_integer( $obj, true, true );
		if( $obj === false ){
			return false;
		}elseif( !$obj[0] ){
			return false;
		}
	}

	if( $type !== null ){
		if( !in_array($type, [1, 2]) ){
			return false;
		}
	}

	$sql = '
		select
			sig_id as id, sig_cls_id as cls_id, sig_obj_id_0 as obj_id_0, sig_obj_id_1 as obj_id_1, sig_obj_id_2 as obj_id_2, sig_signature as signature,
			sig_usr_id as usr_id, sig_firstname as firstname, sig_lastname as lastname, sig_function as function,
			if( usr_prf_id is null, null, if( usr_prf_id = '.PRF_ADMIN.' or usr_prf_id = '.PRF_SELLER.', 1, 2) ) as type
		from obj_signatures
			left join gu_users on ( (usr_tnt_id = 0 or usr_tnt_id = '.$config['tnt_id'].') and usr_id = sig_usr_id )
		where
			sig_tnt_id = '.$config['tnt_id'].'
			and sig_cls_id = '.$cls.'
	';

	if( $multi_key ){
		$wheres = array();
		$wheres_single = array();
		foreach( $obj as $o ){
			if( sizeof( $o ) == 1 ){
				$wheres_single[] = $o[0];
			}else{
				$where = ' sig_obj_id_0 = '.$o[0];
				if( isset($o[1]) ){
					$where .= ' and sig_obj_id_1 = '.$o[1];
				}else{
					$where .= ' and pv_obj_id_1 = 0';
				}
				if( isset($o[2]) ){
					$where .= ' and sig_obj_id_2 = '.$o[2];
				}else{
					$where .= ' and sig_obj_id_2 = 0';
				}
				$wheres[] = $where;
			}
		}
		if( sizeof( $wheres_single ) ){
			$sql .= ' and sig_obj_id_0 in ('.implode(',', $wheres_single).')';
		}else{
			$sql .= ' and (('.implode(') or (', $wheres).'))';
		}
	}else{
		$sql .= ' and sig_obj_id_0 = '.$obj[0];
		if( isset($obj[1]) ){
			$sql .= ' and sig_obj_id_1 = '.$obj[1];
		}
		if( isset($obj[2]) ){
			$sql .= ' and sig_obj_id_2 = '.$obj[2];
		}
	}

	if( $type !== null ){
		$sql .= ' and if( usr_prf_id is null, null, if( usr_prf_id = '.PRF_ADMIN.' or usr_prf_id = '.PRF_SELLER.', 1, 2) ) = '.$type;
	}

	return ria_mysql_query($sql);
}

