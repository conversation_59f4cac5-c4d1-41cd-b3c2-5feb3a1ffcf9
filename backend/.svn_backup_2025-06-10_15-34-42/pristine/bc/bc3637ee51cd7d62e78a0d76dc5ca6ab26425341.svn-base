<?php
    require_once('rights.inc.php');
    global $config;
    
    if( !isset($_GET['usr']) || !$_GET['usr'] ){
        if(isset($_GET['wst']) && isset($_GET['tnt'])){
            $wst_rights = wst_website_rights_get_array($_GET['tnt'], $_GET['wst'], false, null, true, true, true);
            echo json_encode($wst_rights);
            
        }else{
            // Si le params existe alors on l'utilise sinon on met le wst_id de la config
            $paramExists = isset($_GET['wst_id']) && is_numeric($_GET['wst_id']) && $_GET['wst_id'] > 0;
            $wst_rights = wst_website_rights_get_array($config['tnt_id'], $paramExists ? $_GET['wst_id'] : $config['wst_id'], false,  null, true, true, true);
            echo json_encode($wst_rights);
        }
    }else{
        $paramExists = isset($_GET['wst_id']) && is_numeric($_GET['wst_id']);
        
        // Récupère les droits utilisateurs sur l'administration et sur l'application Yuto
        $usr_rights_admin = gu_users_load_admin_rights( false, false, $_GET['usr'] );
        $usr_rights_yuto = gu_users_load_yuto_rights(false, $_GET['usr']);
        
        echo json_encode(array_unique($usr_rights_admin + $usr_rights_yuto));
    }

    exit;
