<?php
$foo = new Foo();
$foo = new Foo();
$foo = new Foo\Bar();
$foo = new Foo\Bar();
$foo = new Foo /* comment */ ();

$foo = new $foo();
$foo = new $foo();
$foo = new $$foo();
$foo = new $$foo();

$foo = new self();
$foo = new self();
$foo = new static();
$foo = new static();

foo(new class {});
echo (new Foo())->bar();
echo (new Foo())->bar();
echo (new Foo((new Bar())->getBaz()))->bar();
$foo = (new Foo())::$bar;

echo (new Foo((new Bar()//comment
)->getBaz(new Baz() /* comment */)))->bar();

$foo = new $bar['a']();
$foo = new $bar['a']['b']();
$foo = new $bar['a'][$baz['a']['b']]['b']();
$foo = new $bar['a'] [$baz['a']/* comment */  ['b']]['b']();

$a = new self::$transport[$cap_string]();
$renderer = new $this->inline_diff_renderer();
$a = new ${$varHoldingClassName}();
