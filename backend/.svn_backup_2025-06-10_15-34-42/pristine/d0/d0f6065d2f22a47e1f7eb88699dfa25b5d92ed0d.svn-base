<?php
	/** \file export-google-shopping.php
	 * 	Ce script est destiné à mettre à jour les fichiers d'export Google Shopping
	 *	des clients ayant activé cette option. Il est lancé chaque jour par une tâche planifiée.
	 *
	 */

	require_once('delivery.inc.php');
	require_once('stats.inc.php');
	require_once('email.inc.php');

	// Récupère la période concernée par le rapport (tous le mois précédent)
	$date_start 	 = date( 'Y-m-01', strtotime('-1 month') );
	$date_end 		 = date( 'Y-m-t', strtotime('-1 month') );

	$year = date( 'Y', strtotime($date_start) );
	$date_start_year = date( $year.'-01-01');
	$date_end_year	 = date( $year.'-12-31');

	if( isset($ar_params['date_start']) && isdate($ar_params['date_start']) ){
		$date_start = $ar_params['date_start'];
	}

	if( isset($ar_params['date_end']) && isdate($ar_params['date_end']) ){
		$date_end 	= $ar_params['date_end'];
	}

	if( strtotime($date_start) > strtotime($date_end) ){
		error_log(__FILE__.':'.__LINE__.' La date de début ne peut être supérieure à la date de fin du rapport');
		return;
	}

	foreach( $configs as $config ){
		$rcfg = cfg_emails_get( 'stats-search-store', $config['wst_id'] );
		if( !$rcfg || !ria_mysql_num_rows($rcfg) ){
			continue;
		}

		$cfg = ria_mysql_fetch_assoc( $rcfg );

		// Récupère les types de messages
		$ar_types = array();
		$rtype = gu_messages_types_get();
		if( $rtype ){
			while( $type = ria_mysql_fetch_assoc($rtype) ){
				$ar_types[ $type['id'] ] = $type;
			}
		}

		if( !sizeof($ar_types) ){
			continue;
		}

		// Initialisation des données
		$ar_stores = array();
		$ar_search = array();
		$ar_totals = array( 'search' => 0, 'seen' => 0 );

		foreach( $ar_types as $id=>$name ){
			$ar_totals[ 'type-'.$id ] = 0;
			$ar_totals[ 'type-year-'.$id ] = 0;
		}

		// Récupération des données
		$rstore = dlv_stores_get( 0, null, false, 0, 0, false, 0, 'FRANCE' );
		if( $rstore ){
			while( $store = ria_mysql_fetch_assoc($rstore) ){
				$ar_stores[ $store['id'] ] = array(
					'name'		=> $store['name'],
					'zipcode' 	=> $store['zipcode'],
					'is_sync' 	=> $store['is_sync'],
					'seen'	  	=> 0,
					'seen-year' => 0
				);

				foreach( $ar_types as $id=>$name ){
					$ar_stores[ $store['id'] ]['type-'.$id] = 0;
					$ar_stores[ $store['id'] ]['type-year-'.$id] = 0;
				}

				$rstat = stats_stores_search_seen( $store['id'], $date_start, $date_end, '', $config['wst_id'] );
				if( $rstat && ria_mysql_num_rows($rstat) ){
					while( $stat = ria_mysql_fetch_assoc($rstat) ){
						$ar_stores[ $store['id'] ]['seen'] += $stat['hits'];
						$ar_totals['seen'] += $stat['hits'];
					}

				}

				$rstat = stats_stores_search_seen( $store['id'], $date_start_year, $date_end_year, '', $config['wst_id'] );
				if( $rstat && ria_mysql_num_rows($rstat) ){
					while( $stat = ria_mysql_fetch_assoc($rstat) ){
						$ar_stores[ $store['id'] ]['seen-year'] += $stat['hits'];
					}

				}

				$rstat = stats_stores_contacts( $store['id'], $date_start, $date_end, '', $config['wst_id'], true );
				if( $rstat && ria_mysql_num_rows($rstat) ){
					while( $stat = ria_mysql_fetch_assoc($rstat) ){
						if( !isset($ar_stores[ $store['id'] ][ 'type-'.$stat['cnt_type'] ]) || !isset($ar_totals[ 'type-'.$stat['cnt_type'] ]) ){
							continue;
						}

						$ar_stores[ $store['id'] ][ 'type-'.$stat['cnt_type'] ] += $stat['hits'];
						$ar_totals[ 'type-'.$stat['cnt_type'] ] += $stat['hits'];
					}
				}

				$rstat = stats_stores_contacts( $store['id'], $date_start_year, $date_end_year, '', $config['wst_id'], true );
				if( $rstat && ria_mysql_num_rows($rstat) ){
					while( $stat = ria_mysql_fetch_assoc($rstat) ){
						if( !isset($ar_stores[ $store['id'] ][ 'type-'.$stat['cnt_type'] ]) || !isset($ar_totals[ 'type-'.$stat['cnt_type'] ]) ){
							continue;
						}

						$ar_stores[ $store['id'] ][ 'type-year-'.$stat['cnt_type'] ] += $stat['hits'];
						$ar_totals[ 'type-year-'.$stat['cnt_type'] ] += $stat['hits'];
					}
				}
			}
		}

		if( !sizeof($ar_stores) ){
			continue;
		}

		$rsearch = stats_stores_search_get( $config['wst_id'], $date_start, $date_end, 'fr' );
		if( $rsearch ){
			while( $search = ria_mysql_fetch_assoc($rsearch) ){
				if( !isset($ar_search[ $search['keyword'] ]['month']) ){
					$ar_search[ $search['keyword'] ] = array(
						'month' => 0,
						'year' 	=> 0
					);
				}

				$ar_search[ $search['keyword'] ]['month'] = $ar_search[ $search['keyword'] ]['month'] + 1;
				$ar_totals['search'] = $ar_totals['search'] + 1;
			}
		}

		$rsearch = stats_stores_search_get( $config['wst_id'], $date_start_year, $date_end_year, 'fr' );
		if( $rsearch ){
			while( $search = ria_mysql_fetch_assoc($rsearch) ){
				if( !isset($ar_search[ $search['keyword'] ]) ){
					continue;
				}

				$ar_search[ $search['keyword'] ]['year'] = $ar_search[ $search['keyword'] ]['year'] + 1;
			}
		}

		$total = 0;
		foreach( $ar_totals as $key=>$nb ){
			if( strstr($key, 'type-year') ){
				continue;
			}

			$total += $nb;
		}

		if( $total<=0 ){
			continue;
		}

		arsort( $ar_search );
		$ar_stores = array_msort( $ar_stores, array('type-3'=>SORT_DESC, 'type-10'=>SORT_DESC, 'seen'=>SORT_DESC) );

		$utm_source = '?utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=stats-search-store&amp;utm_content=page_pwd';
		$email = new Email();
		$email->setSubject( 'Rapport mensuel : Recherches revendeurs sur '.$config['site_name'] );
		$email->setFrom( 'RiaStudio Rapport mensuel <<EMAIL>>');
		$email->addTo( $cfg['to'] );

		$email->addHtml( '<table width="480" border="0"><tbody><tr><td align="center"><img height="110" width="650" alt="Riastudio" src="http://www.riastudio.fr/images/template/header/banner.png"></td></tr><tr><td style="font-size: 12px;"><br><font face="Verdana,Arial,Helvetica,sans-serif">' );

		if( trim($cfg['cc']) ){
			$email->addCC( $cfg['cc'] );
		}

		if( trim($cfg['bcc']) ){
			$email->addBcc( $cfg['bcc'] );
		}

		if( trim($cfg['reply-to']) ){
			$email->setReplyTo( $cfg['reply-to'] );
		}

		$email->addHtml('
			<p>Vous trouverez ci-dessous un rapport sur l\'activité des recherches de revendeurs sur votre site "'.$config['site_name'].'" pour le mois de '.month_french( date('m', strtotime($date_start)) ).' '.date('Y', strtotime($date_start)).'.</p>
			<h3>En général</h3>
			<ul>
		');

		foreach( $ar_totals as $key=>$val ){
			if( strstr($key, 'type-year') ){
				continue;
			}

			switch( $key ){
				case 'search' :
					$email->addHtml('<li>'.$val.' recherche'.( $val > 1 ? 's' : '' ).'</li>');
					break;
				case 'seen' :
					$email->addHtml('<li>'.$val.' affichage'.( $val > 1 ? 's' : '' ).' de revendeurs</li>');
					break;
				default :
					if( is_numeric($val) && $val>0 ){
						$email->addHtml('<li>'.$val.' '.strtolower2( $ar_types[ str_replace('type-', '', $key) ][ $val > 1 ? 'name-pl' : 'name' ] ).'</li>');
					}
					break;

			}
		}

		$email->addHtml('
			</ul>
			<h3>Détails par magasin</h3>
		');

		$ar_csv = array();
		$show_type = array();
		$email->openTable( 570, 1, 'font-size:1em;' );

		$email->openTableRow();
		$email->addCell( '<b>Magasin</b>', 'center' );
		$email->addCell( '<b>Code postal</b>', 'center' );
		$email->addCell( '<b>Affichages</b>', 'center' );
		$email->addCell( '<b>Affichages ('.$year.')</b>', 'center' );

		$tmp_ar_csv = array( 'Magasin', 'Code postal', 'Affichages', 'Affichages ('.$year.')' );
		foreach( $ar_types as $id=>$info ){
			if( isset($ar_totals['type-year-'.$id]) && is_numeric($ar_totals['type-year-'.$id]) && $ar_totals['type-year-'.$id] ){
				$email->addCell( '<b>'.htmlspecialchars( $info['name-pl'] ).'</b>', 'center' );
				$email->addCell( '<b>'.htmlspecialchars( $info['name-pl'] ).' ('.$year.')</b>', 'center' );
				$show_type[] = $id;

				$tmp_ar_csv[] = $info['name-pl'];
				$tmp_ar_csv[] = $info['name-pl'].' ('.$year.')';
			}
		}

		$ar_csv[] = $tmp_ar_csv;

		$email->closeTableRow();

		foreach( $ar_stores as $str_id=>$data ){
			$show_store = true;
			if( !(isset($data['seen']) && is_numeric($data['seen']) && $data['seen']) ){
				$show_store = false;
			}else{
				$have_one_type = false;

				foreach( $ar_types as $id=>$info ){
					if( !in_array($id, $show_type) ){
						continue;
					}

					if( isset($data['type-'.$id]) && is_numeric($data['type-'.$id]) && $data['type-'.$id] ){
						$have_one_type = true;
						break;
					}
				}

				if( $have_one_type ){
					$show_store = true;
				}
			}

			if( !$show_store ){
				continue;
			}

			$tmp_ar_csv = array();

			$total = 0;
			foreach( $data as $key=>$val ){
				if( $key != 'zipcode' && is_numeric($val) ){
					$total += $val;
				}
			}

			if( !$total ){
				continue;
			}

			$email->openTableRow();

			$email->addCell( '<a href="'.$config['site_url'].'/admin/config/livraison/stores/edit.php'.$utm_source.'&amp;str='.$str_id.'&amp;tab=stats">'.$data['name'].'</a>', 'left' );
			$email->addCell( htmlspecialchars($data['zipcode']), 'center' );

			$tmp_ar_csv[] = $data['name'];
			$tmp_ar_csv[] = $data['zipcode'];

			if( isset($data['seen']) && is_numeric($data['seen']) && $data['seen'] ){
				$email->addCell( $data['seen'], 'right' );
				$tmp_ar_csv[] = $data['seen'];
			}else{
				$email->addCell( '&nbsp;0', 'right' );
				$tmp_ar_csv[] = '0';
			}

			if( isset($data['seen-year']) && is_numeric($data['seen-year']) && $data['seen-year'] ){
				$email->addCell( $data['seen-year'], 'right' );
				$tmp_ar_csv[] = $data['seen-year'];
			}else{
				$email->addCell( '&nbsp;0', 'right' );
				$tmp_ar_csv[] = '0';
			}

			foreach( $ar_types as $id=>$info ){
				if( !in_array($id, $show_type) ){
					continue;
				}

				if( isset($data['type-'.$id]) && is_numeric($data['type-'.$id]) && $data['type-'.$id] ){
					$email->addCell( $data['type-'.$id], 'right' );
					$tmp_ar_csv[] = $data['type-'.$id];
				}else{
					$email->addCell( '&nbsp;0', 'right' );
					$tmp_ar_csv[] = '0';
				}

				if( isset($data['type-year-'.$id]) && is_numeric($data['type-year-'.$id]) && $data['type-year-'.$id] ){
					$email->addCell( $data['type-year-'.$id], 'right' );
					$tmp_ar_csv[] = $data['type-year-'.$id];
				}else{
					$email->addCell( '&nbsp;0', 'right' );
					$tmp_ar_csv[] = '0';
				}
			}

			$email->closeTableRow();

			$ar_csv[] = $tmp_ar_csv;
		}

		$email->closeTable();

		$email->addHtml('
			<h3>Liste des recherches</h3>
			<p>Voici la liste complète des recherches de revendeurs réalisées sur le site :</p>
		');

		$email->openTable( 333, 1, 'font-size:1em;' );

		$email->openTableRow();
		$email->addCell( '<b>Recherche</b>', 'center' );
		$email->addCell( '<b>Quantité</b>', 'center' );
		$email->addCell( '<b>Quantité ('.$year.')</b>', 'center' );
		$email->closeTableRow();

		foreach( $ar_search as $keyword=>$data ){
			$email->openTableRow();
			$email->addCell( $keyword, 'right' );
			$email->addCell( $data['month'], 'right' );
			$email->addCell( $data['year'], 'right' );
			$email->closeTableRow();
		}

		$email->addHtml('
			</font></td></tr></table><div style="height: 57px; margin-top : 20px; width: 640px; color: rgb(255, 255, 255); z-index: 9999; font-size: 13px; padding-top: 5px; padding-left: 20px;background-color : rgb(0, 47, 59);"> <div style="float: left;margin-top : 20px;text-align:center;">Solution RiaShop - Nous contacter :&nbsp;05&nbsp;47&nbsp;74&nbsp;81&nbsp;87&nbsp;-&nbsp;<a style="color: rgb(255, 255, 255);" href="mailto:<EMAIL>"><EMAIL></a></div></div>
		');

		// Création du CSV du premier tableau
		$file_create = false;
		if( is_array($ar_csv) && sizeof($ar_csv) ){
			$file_dirname = dirname(__FILE__).'/tmp/recherche-revendeurs.csv';
			$f = fopen( $file_dirname, 'w' );

			foreach( $ar_csv as $line ){
				$tmp = '';
				foreach( $line as $c ){
					$tmp .= ( trim($tmp)!='' ? ';' : '' ).'"'.str_replace('"', '""', $c).'"';
				}

				fwrite( $f, utf8_decode($tmp)."\n" );
			}

			fclose( $f );

			$email->addAttachment( $file_dirname );
			$file_create = true;
		}

		$email->send();

		if( $file_create ){
			unlink( $file_dirname );
		}
	}