<?xml version='1.0'?>
<grammar xmlns="http://relaxng.org/ns/structure/1.0"
  datatypeLibrary="http://www.w3.org/2001/XMLSchema-datatypes"
  ns="http://www.iana.org/assignments">

  <include href="iana-registry.rng"/>

  <start>
    <element name="registry">
      <ref name="registryMeta"/>
      <element name="registry">
        <ref name="registryMeta"/>
        <zeroOrMore>
          <element name="record">
            <optional>
              <attribute name="date"><ref name="genericDate"/></attribute>
            </optional>
            <optional>
              <attribute name="updated"><ref name="genericDate"/></attribute>
            </optional>
            <element name="value"><ref name="genericRange"/></element>
            <element name="description"><text/></element>
            <ref name="references"/>
          </element>
        </zeroOrMore>
      </element>
      <ref name="people"/>
    </element>
  </start>

</grammar>
