<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="%i">
  <project timestamp="%i">
    <file name="%s/source_with_class_and_anonymous_function.php">
      <class name="CoveredClassWithAnonymousFunctionInStaticMethod" namespace="global">
        <metrics complexity="2" methods="2" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="5" coveredstatements="4" elements="7" coveredelements="5"/>
      </class>
      <line num="5" type="method" name="runAnonymous" visibility="public" complexity="1" crap="1.04" count="1"/>
      <line num="7" type="stmt" count="1"/>
      <line num="9" type="stmt" count="1"/>
      <line num="10" type="stmt" count="0"/>
      <line num="11" type="method" name="anonymous function" complexity="1" crap="1" count="1"/>
      <line num="12" type="stmt" count="1"/>
      <line num="13" type="stmt" count="1"/>
      <line num="14" type="stmt" count="1"/>
      <line num="17" type="stmt" count="1"/>
      <line num="18" type="stmt" count="1"/>
      <metrics loc="19" ncloc="17" classes="1" methods="2" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="8" coveredstatements="7" elements="10" coveredelements="8"/>
    </file>
    <metrics files="1" loc="19" ncloc="17" classes="1" methods="2" coveredmethods="1" conditionals="0" coveredconditionals="0" statements="8" coveredstatements="7" elements="10" coveredelements="8"/>
  </project>
</coverage>
