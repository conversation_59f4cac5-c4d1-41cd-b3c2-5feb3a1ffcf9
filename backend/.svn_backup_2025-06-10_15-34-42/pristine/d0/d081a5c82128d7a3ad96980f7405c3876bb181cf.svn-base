<?php
	require_once('fields.inc.php');
	
	// Vérifie que l'utilisateur à bien accès à la page
	if( $_GET['cls_id'] == CLS_PRODUCT ){
		gu_if_authorized_else_403('_RGH_ADMIN_OPTION_MOD_PRD');
	}elseif( $_GET['cls_id'] == CLS_USER ){
		gu_if_authorized_else_403('_RGH_ADMIN_OPTION_MOD_CUSTOMER');
	}elseif( $_GET['cls_id'] == CLS_ADDRESS ){
		gu_if_authorized_else_403('_RGH_ADMIN_OPTION_MOD_ADDRESS');
	}

	// Vérifie le paramètre cls_id
	if ( !isset($_GET["cls_id"]) || !is_numeric($_GET["cls_id"]) || $_GET["cls_id"] <= 0) {
		header('Location: /admin/options/moderation_classes.php');
		exit;
	}

	if( isset($_POST['save']) || isset($_POST['default']) ){
		$fields = fld_fields_get(0,0,-2,0,0,0,null, array(), true, array(), null, $_GET["cls_id"], null, null );

		while( $f = ria_mysql_fetch_assoc($fields) ){
			if( !isset($_POST['default']) && isset($_POST['fld-'.$f['id']]) ){
				gu_moderate_fields_add( $_SESSION['usr_id'], $f['id'] );
			}else{
				gu_moderate_fields_del( $_SESSION['usr_id'], $f['id'] );
			}
		}

		if( !isset($error) ){
			header('Location: moderation.php?cls_id='.urlencode($_GET['cls_id']) );
			exit;
		}
	}
	
	$included = array();
	$fields = gu_moderate_fields_get($_SESSION['usr_id']);
	while( $f = ria_mysql_fetch_array($fields) ){
		$included[] = $f['fld_id'];
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Mes options'), '/admin/options/index.php' )
		->push( _('Modération'), '/admin/options/moderation.php' )
		->push( fld_classes_get_name($_GET['cls_id']) );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Modération') . ' - ' . _('Mes options'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print fld_classes_get_name($_GET["cls_id"]); ?></h2>
	<h3><?php print _('Modération des demandes de modification'); ?></h3>
<?php
	if( isset($error) )
		print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
?>
	<p class="notice"><?php print _('Vous trouverez ci-dessous la liste des champs pouvant être modérés par vos soins. Veuillez sélectionnez ceux que vous souhaitez valider avant mise à jour de la base de données.'); ?></p>

	<form action="moderation.php?cls_id=<?php print $_GET['cls_id']; ?>" method="post">
	<div class="options-tree">
	<?php
		$categories = fld_categories_get( 0, true, $_GET['cls_id'] );

		$exclude_fld_ids = array();
		while( $c = ria_mysql_fetch_array($categories) ){
			$fields = fld_fields_get(0,$c['id'],0,0,0,0,null,array(), true, array(), null, $_GET['cls_id'],null, null);
			
			$fields_html = '';
			$cat_fld_ids = array();
			$check = true;
			while( $f = ria_mysql_fetch_array($fields) ){
				$fld_included = array_search($f['id'],$included)!==false;
				$check = $check && $fld_included;
				$exclude_fld_ids[] = $f['id'];
				$cat_fld_ids[] = $f['id'];
				$fields_html .= '<dd>';
				$fields_html .= '<input type="checkbox" name="fld-'.$f['id'].'" id="fld-'.$f['id'].'" value="'.$f['id'].'" '.( $fld_included ? ' checked="checked"':'' ).' /> ';
				$fields_html .= '<label for="fld-'.$f['id'].'">'.htmlspecialchars($f['name']).'</label><br />';
				$fields_html .= '</dd>';
			}

			if( trim($fields_html) == ''){
				continue;
			}
			$checked = $check ? ' checked="checked' : '';

			print '<dl class="'.($checked ? 'un' : '').'fold">';
			print '<dt>';
			print '<input type="checkbox" name="cat-'.$c['id'].'" id="cat-'.$c['id'].'" value="'.$c['id'].'"'.$checked.' /> ';
			print '<label for="cat-'.$c['id'].'">'.htmlspecialchars($c['name']).'</label><br />';
			print '</dt>';

			print $fields_html;

			print '</dl>';
		}
	?>
	</div>
	<div class="options-tree">
	<?php
		$other_fields = fld_fields_get(0,0,-2,0,0,0,null, array(), true, $exclude_fld_ids, null, $_GET["cls_id"], null, null );
		if ( $other_fields && ria_mysql_num_rows($other_fields) ) {
			$fields_html = '';
			$check = true;
			while( $f = ria_mysql_fetch_array($other_fields) ){
				$fld_included = array_search($f['id'],$included) !== false;
				$check = $check && $fld_included;

				$fields_html .= '<dd>';
				$fields_html .= '<input type="checkbox" name="fld-'.$f['id'].'" id="fld-'.$f['id'].'" value="'.$f['id'].'" '.( $fld_included ? ' checked="checked"':'' ).' /> ';
				$fields_html .= '<label for="fld-'.$f['id'].'">'.htmlspecialchars($f['name']).'</label><br />';
				$fields_html .= '</dd>';
			}

			
			$checked = $check ? ' checked="checked' : '';
			if ($fields_html != '') {
				print '<dl class="'.($check ? 'un' : '').'fold">';
				print '<dt>';
				print '<input type="checkbox" name="cat-0" id="cat-0" value="0"'.$checked.' /> ';
				print '<label for="cat-0">'._('Autres').'</label><br />';
				print '</dt>';
	
				print $fields_html;
	
				print '</dl>';
			}

		}
		
	?>
	</div>
	<script>
		<?php
			if( $categories !== false && ria_mysql_num_rows($categories) ){
				ria_mysql_data_seek($categories, 0);
				while( $c = ria_mysql_fetch_array($categories) ){
					echo 'var optCat'.$c['id'].' = new optionsTree( "cat-'.$c['id'].'", true );', "\n";
					$fields = fld_fields_get(0,$c['id'],0,0,0,0,null,array(), true, array(), null, $_GET['cls_id'],null, null);
					while( $f = ria_mysql_fetch_array($fields) ){
						echo 'var optFld'.$f['id'].' = new optionsTree( "fld-'.$f['id'].'" );', "\n";
						echo 'optCat'.$c['id'].'.addChild( optFld'.$f['id'].' );', "\n";
					}
					echo 'optCat'.$c['id'].'.check();';
				}
			}

			if( $other_fields !== false && ria_mysql_num_rows($other_fields) ){
				ria_mysql_data_seek($other_fields, 0);
				echo 'var optCat0 = new optionsTree( "cat-0", true );', "\n";
				while( $f = mysql_fetch_array($other_fields) ){
					echo 'var optFld'.$f['id'].' = new optionsTree( "fld-'.$f['id'].'" );', "\n";
					echo 'optCat0.addChild( optFld'.$f['id'].' );', "\n";
				}
				echo 'optCat0.check();';
			}
		?>
	</script>
	
	<div class="ria-admin-ui-actions">
		<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
		<input type="submit" name="default" value="<?php print _('Par défaut'); ?>" />
	</div>
	
	<p class="notice"><?php print _('Les champs n\'étant modérés par aucun administrateur resteront en attente de validation et ne seront pas réintégrés dans votre gestion commerciale.'); ?></p>

	</form>
<?php
	require_once('admin/skin/footer.inc.php');
?>