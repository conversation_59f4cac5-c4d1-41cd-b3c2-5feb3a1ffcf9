<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

require_once('orders.inc.php');

$ord_id = isset($_POST['ord']) ? intval($_POST['ord']) : (isset($_GET['ord']) ? intval($_GET['ord']) : 0);

$order = false;
if ($ord_id > 0) {
    $r_order = ord_orders_get_masked($ord_id, true);
    if ($r_order && ria_mysql_num_rows($r_order)) {
        $order = ria_mysql_fetch_array($r_order);
    }
}

$newstate = isset($_POST['newstate']) ? intval($_POST['newstate']) : (isset($_GET['newstate']) ? intval($_GET['newstate']) : 0);
$newstate_not_found = $newstate <= 0 || !ord_states_exists($newstate);

if (!$order) {
    $error = _('La commande demandée n\'a pas été trouvée');
}

if ($newstate_not_found) {
    $error = _('Statut de commande non reconnu');
}

define('ADMIN_PAGE_TITLE', _('Livraison'));
define('ADMIN_HEAD_POPUP', true);
define('ADMIN_ID_BODY', 'popup-content');
require_once('admin/skin/header.inc.php');

// Affichage des messages d'erreur
if (!$order || $newstate_not_found) {
    print '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
    exit;
}

$r_products = ord_products_get($order['id']);

$allow_delivery = gu_user_is_authorized('_RGH_ADMIN_ORDER_ALLOW_DELIVERY');
if (isset($_POST['ord_prd_colis']) && is_array($_POST['ord_prd_colis']) && $r_products && ria_mysql_num_rows($r_products)) {
    $is_valid = true;
    if ($allow_delivery) {
        foreach ($_POST['ord_prd_colis'] as $prd_id => $ord_prd_data) {
            if (!intval($prd_id) > 0 || !is_array($ord_prd_data)) {
                $is_valid = false;
                break;
            }
    
            foreach ($ord_prd_data as $line_id => $colis) {
                if ('' == trim($colis)) {
                    $is_valid = false;
                    break 2;
                }
            }
        }

        if (!$is_valid) {
            $error = _('Veuillez saisir un numéro de colis.');
        }
    }

    if ($is_valid && $allow_delivery) {
        $r_bl = ord_bl_get(0, 0, false, false, false, array(), false, $order['id'], false, false, false, $config['wst_id'] );
        $bl_id = false;
        if ($r_bl && ria_mysql_num_rows($r_bl)) {
            $bl_id = ria_mysql_result($r_bl, 0, 'id');
        }
        
        if (!$bl_id) {
            $bl_id = ord_bl_add($order['user'], 0, 0, '', '', null, _STATE_BL_READY, $order['srv_id'], intval($order['masked']) > 0, $order['dlv_id']);
        }
        
        if (!$bl_id) {
            $error = _('Erreur lors de la création du bon de livraison');
        } else {
            foreach ($_POST['ord_prd_colis'] as $prd_id => $ord_prd_data) {
                foreach ($ord_prd_data as $line_id => $colis) {
                    $r_prd = ord_products_get( $order['id'], false, $prd_id, '', $line_id);
                    if (!$r_prd || !ria_mysql_num_rows($r_prd)) {
                        $error = 'Produit non trouvé dans la commande';
                        break 2;
                    }
        
                    $ord_prd = ria_mysql_fetch_assoc($r_prd);
                    if (prd_products_is_port($ord_prd['ref'])) {
                        continue;
                    }

                    if (ord_bl_products_exists($bl_id, $prd_id, $line_id)) {
                        if (!ord_bl_products_update_sage( $bl_id, $prd_id, $line_id, $ord_prd['ref'], $ord_prd['name'], $ord_prd['qte'], $ord_prd['price_ht'], $ord_prd['tva_rate'], $order['id'], $colis, $ord_prd['price_ttc'], $ord_prd['ecotaxe'])) {
                            $error = _('Erreur lors de la modification d\'une ligne du bon de livraison'); 
                            break 2;
                        }
                    } elseif (!ord_bl_products_add_sage( $bl_id, $prd_id, $line_id, $ord_prd['ref'], $ord_prd['name'], $ord_prd['qte'], $ord_prd['price_ht'], $ord_prd['tva_rate'], $order['id'], $colis, $ord_prd['price_ttc'], $ord_prd['ecotaxe'])) {
                        $error = _('Erreur lors de l\'ajout d\'une ligne au bon de livraison'); 
                        break 2;
                    }
                }
            }
        }
    }

    if (!isset($error) && isset($bl_id) && $bl_id) {
	    // Modification de l'état de la commande
        ord_bl_apply_pmt( $bl_id );
        if( ord_orders_update_status( $order['id'], $newstate) ){
            if ($newstate == 7 || $newstate == 6) {
                $res_bl_upd = ord_bl_state_set( $bl_id, $newstate );
                if ($res_bl_upd) {
                    ord_bl_notify( $bl_id );
                } elseif (false == $res_bl_upd) {
                    ord_bl_del($bl_id);
                    $error = _("Une erreur est survenue pendant la mise à jour du bon de livraison.");
                }
            }
        } else {
            ord_bl_del($bl_id);
            $error = _("Une erreur est survenue pendant la mise à jour du statut de la commande.");
        }

        if (!isset($error)) {
            print '<script>window.parent.location = ' . json_encode('/admin/orders/order.php?' . http_build_query(array('ord' => $order['id']))) . '</script>';
            exit;
        }
    }
}

if (isset($error)) {
    if (isset($bl_id) && $bl_id) {
        ord_bl_del($bl_id);
        ord_bl_products_del($bl_id);
    }

    print '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
}
	
?>
<form method="post">
	<input type="hidden" name="ord_id" value="<?php print htmlspecialchars($order['id']) ?>" />
	<input type="hidden" name="newstate" value="<?php print htmlspecialchars($newstate) ?>" />
    <table>
        <col width="*" /><col width="75" />
        <thead>
            <tr>
                <th><?php print _('Référence')?></th>
                <th><?php print _('Désignation')?></th>
                <th><?php print _('Colis')?></th>
            </tr>
        </thead>
        <tbody>
        <?php
            mysql_data_seek( $r_products, 0 );
            while( $prd = ria_mysql_fetch_array($r_products) ){
                if (prd_products_is_port($prd['ref'])) {
                    continue;
                }

                $r_bl_prd = ord_bl_products_get(0, $order['id'], false, false, false, $prd['id'], $prd['line']);
                $val = '';
                if ($r_bl_prd && ria_mysql_num_rows($r_bl_prd)) {
                    $bl_prd = ria_mysql_fetch_assoc($r_bl_prd);
                    $val = $bl_prd['colis'];
                }

                print '<tr>';
                $categories = prd_products_categories_get($prd['id']);
                if( $cat = ria_mysql_fetch_array($categories) ){
                	print '<td><a href="/admin/catalog/product.php?prd='.$prd['id'].'&amp;cat='.$cat['cat'].'">'.htmlspecialchars($prd['ref']).'</a></td>';
				}else{
					print '<td>'.htmlspecialchars($prd['ref']).'</td>';
				}
                print '<td>'.htmlspecialchars($prd['name']).'</td>';
                print '<td><input type="text" name="ord_prd_colis[' . htmlspecialchars($prd['id']) . '][' . htmlspecialchars($prd['line']) . ']" value="' . htmlspecialchars($val) . '" /></td>';
                print '</tr>';
            }
            ?>
        </tbody>
        <tfoot>
            <tr><td colspan="6">
                <input class="riaButton" type="submit" value="<?php print _('Enregistrer') ?>" />
                <input class="riaCancelButton" type="button" value="<?php print _('Annuler') ?>" onclick="delivery_cancel()" />
            </td>
            </tr>
        </tfoot>
    </table>
</form>
<script>
    var delivery_cancel = function(){
        window.parent.hidePopup();
    };
</script>

<?php
require_once('admin/skin/footer.inc.php');
?>