<?php

if( !isset($usr) ){
	header('Location: /admin/customers/index.php');
	exit;
}

require_once('delayed.php');

$orders_tables = array(
	_('Chiffre d\'affaires potentiel') => array('orders' => ord_orders_get($_GET['usr'], 0, ord_states_get_uncompleted(true)), 'type' => 'uncompleted'),
	_('Chiffre d\'affaires confirmé') => array('orders' => ord_orders_get($_GET['usr'], 0, ord_states_get_ord_valid()), 'type' => 'valid'),
	_('Chiffre d\'affaires annulé') => array('orders' => ord_orders_get($_GET['usr'], 0, ord_states_get_canceled()), 'type' => 'canceled'),
);

foreach( $orders_tables as $title => $orders ){
	$tot_products = $tot_total_ht = $tot_total_ttc = 0; ?>
	<table class="tb-remainders">
		<caption><?php print htmlspecialchars($title); ?></caption>
		<thead class="thead-none">
			<tr>
				<th class="col145px"><?php print _('Référence'); ?></th>
				<th class="col145px"><?php print _('Date'); ?></th>
				<th class="col60px align-right"><?php print _('Produits'); ?></th>
				<th class="col170px"><?php print _('Statut'); ?></th>
				<th class="col110px align-right"><?php print _('Montant HT'); ?></th>
				<th class="col110px align-right"><?php print _('Montant TTC'); ?></th>
			</tr>
		</thead>
		<tbody>
			<?php if( !ria_mysql_num_rows($orders['orders']) ){ ?>
				<tr>
					<td colspan="6"><?php print _('Aucun').' '.mb_strtolower($title, 'UTF-8'); ?></td>
				</tr>
			<?php }else{
				$count = 0;
				$display = true;
				while( $ord = ria_mysql_fetch_array($orders['orders']) ){
					if( $count === 5 ){ ?>
						<tr class="show-orders <?php print $orders['type']; ?>">
							<td colspan="6" class="align-center">
								<a href="#" type="<?php print $orders['type']; ?>" class="show-orders button"><?php print _('Afficher toutes les commandes'); ?></a>
							</td>
						</tr>
						<?php $display = false;
					}
					
						$tot_products += $ord['products'];
						$tot_total_ht += $ord['total_ht'];
						$tot_total_ttc += $ord['total_ttc'];
					?>
					<tr class="order-<?php print $orders['type'].(!$display ? ' none' : '');  ?>">
						<td data-label="<?php print _('Référence :'); ?> ">
							<a href="../orders/order.php?ord=<?php print $ord['id']; ?>"><?php print view_cart_is_sync($ord).' '; print $ord['ref'] ?: str_pad($ord['id'], 8, 0, STR_PAD_LEFT); ?></a>
						</td>
						<td data-label="<?php print _('Date :'); ?> "><?php print ria_date_format($ord['date']); ?></td>
						<td class="numeric" data-label="<?php print _('Produits :'); ?> "><?php print ria_number_format($ord['products'], NumberFormatter::DECIMAL); ?></td>
						<td data-label="<?php print _('Statut :'); ?> "><?php print htmlspecialchars($ord['state_name']); ?></td>
						<td class="numeric" data-label="<?php print _('Montant HT :'); ?> "><?php print ria_number_format($ord['total_ht'], NumberFormatter::CURRENCY, 2); ?></td>
						<td class="numeric" data-label="<?php print _('Montant TTC :'); ?> "><?php print ria_number_format($ord['total_ttc'], NumberFormatter::CURRENCY, 2); ?></td>
					</tr>
					<?php $count++;
				}
			} ?>
		</tbody>
		<tfoot>
			<?php if( ria_mysql_num_rows($orders['orders']) ){ ?>
				<tr>
					<th colspan="2"><?php print _('Total :'); ?></th>
					<th class="numeric" data-label="<?php print _('Produits :'); ?> "><?php print ria_number_format($tot_products, NumberFormatter::DECIMAL); ?></th>
					<th class="thead-none"></th>
					<th class="numeric" data-label="<?php print _('Montant HT :'); ?> "><?php print ria_number_format($tot_total_ht, NumberFormatter::CURRENCY, 2); ?></th>
					<th class="numeric" data-label="<?php print _('Montant TTC :'); ?> "><?php print ria_number_format($tot_total_ttc, NumberFormatter::CURRENCY, 2); ?></th>
				</tr>
			<?php } ?>
			<?php if( $title === _('Chiffre d\'affaires potentiel') ){ ?>
				<tr class="ftoot-border-top">
					<td colspan="6">
						<button type="submit" id="create-order" name="create-order"><?php print _('Créer une commande'); ?></button>
					</td>
				</tr>
			<?php } ?>
		</tfoot>
	</table>
<?php }