<?php
	// MyPDF_Border
	/*
	*/
	
	require_once('mypdf/MyPDF_Color.php');
	
	class MyPDF_Border {
	
		// attributs
		
			private	$_color;
		
		// méthodes
		
			// __construct
			/* Constructor */
			public function __construct($param = array()) {
				$color = (array_key_exists('color', $param)) ? $param['color'] : new MyPDF_Color();
				$this->setColor(array('color' => $color));
			}
			
			// getColor
			/* Renvoie la couleur */
			public function getColor() {
				return $this->_color;
			}
			
			// setColor
			/* Affecte la couleur */
			public function setColor($param) {
				$this->_color = $param['color'];
				return $this;
			}
		
	}

