<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/schema.proto

namespace Google\Cloud\PubSub\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Request for the CreateSchema method.
 *
 * Generated from protobuf message <code>google.pubsub.v1.CreateSchemaRequest</code>
 */
class CreateSchemaRequest extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. The name of the project in which to create the schema.
     * Format is `projects/{project-id}`.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     */
    private $parent = '';
    /**
     * Required. The schema object to create.
     * This schema's `name` parameter is ignored. The schema object returned
     * by CreateSchema will have a `name` made using the given `parent` and
     * `schema_id`.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Schema schema = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    private $schema = null;
    /**
     * The ID to use for the schema, which will become the final component of
     * the schema's resource name.
     * See https://cloud.google.com/pubsub/docs/admin#resource_names for resource
     * name constraints.
     *
     * Generated from protobuf field <code>string schema_id = 3;</code>
     */
    private $schema_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $parent
     *           Required. The name of the project in which to create the schema.
     *           Format is `projects/{project-id}`.
     *     @type \Google\Cloud\PubSub\V1\Schema $schema
     *           Required. The schema object to create.
     *           This schema's `name` parameter is ignored. The schema object returned
     *           by CreateSchema will have a `name` made using the given `parent` and
     *           `schema_id`.
     *     @type string $schema_id
     *           The ID to use for the schema, which will become the final component of
     *           the schema's resource name.
     *           See https://cloud.google.com/pubsub/docs/admin#resource_names for resource
     *           name constraints.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Pubsub\V1\Schema::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. The name of the project in which to create the schema.
     * Format is `projects/{project-id}`.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     * @return string
     */
    public function getParent()
    {
        return $this->parent;
    }

    /**
     * Required. The name of the project in which to create the schema.
     * Format is `projects/{project-id}`.
     *
     * Generated from protobuf field <code>string parent = 1 [(.google.api.field_behavior) = REQUIRED, (.google.api.resource_reference) = {</code>
     * @param string $var
     * @return $this
     */
    public function setParent($var)
    {
        GPBUtil::checkString($var, True);
        $this->parent = $var;

        return $this;
    }

    /**
     * Required. The schema object to create.
     * This schema's `name` parameter is ignored. The schema object returned
     * by CreateSchema will have a `name` made using the given `parent` and
     * `schema_id`.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Schema schema = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return \Google\Cloud\PubSub\V1\Schema|null
     */
    public function getSchema()
    {
        return isset($this->schema) ? $this->schema : null;
    }

    public function hasSchema()
    {
        return isset($this->schema);
    }

    public function clearSchema()
    {
        unset($this->schema);
    }

    /**
     * Required. The schema object to create.
     * This schema's `name` parameter is ignored. The schema object returned
     * by CreateSchema will have a `name` made using the given `parent` and
     * `schema_id`.
     *
     * Generated from protobuf field <code>.google.pubsub.v1.Schema schema = 2 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param \Google\Cloud\PubSub\V1\Schema $var
     * @return $this
     */
    public function setSchema($var)
    {
        GPBUtil::checkMessage($var, \Google\Cloud\PubSub\V1\Schema::class);
        $this->schema = $var;

        return $this;
    }

    /**
     * The ID to use for the schema, which will become the final component of
     * the schema's resource name.
     * See https://cloud.google.com/pubsub/docs/admin#resource_names for resource
     * name constraints.
     *
     * Generated from protobuf field <code>string schema_id = 3;</code>
     * @return string
     */
    public function getSchemaId()
    {
        return $this->schema_id;
    }

    /**
     * The ID to use for the schema, which will become the final component of
     * the schema's resource name.
     * See https://cloud.google.com/pubsub/docs/admin#resource_names for resource
     * name constraints.
     *
     * Generated from protobuf field <code>string schema_id = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setSchemaId($var)
    {
        GPBUtil::checkString($var, True);
        $this->schema_id = $var;

        return $this;
    }

}

