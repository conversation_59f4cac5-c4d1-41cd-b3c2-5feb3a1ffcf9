<?php

	require_once('hotjar.inc.php');

	// Initialise une variable pour dire que l'abonnement Yuto se termine dans 5 jours au plus tard
	$yuto_end_5days = false;

	// Initialise la constante IS_HTTP_ERROR pour simplifier le code qui suit
	if( !defined('IS_HTTP_ERROR') ){
		define( 'IS_HTTP_ERROR', false );
	}
	if( !defined('ADMIN_HEAD_POPUP') ){
		define( 'ADMIN_HEAD_POPUP', false );
	}
	if( !defined('ADMIN_HEAD_LOGIN') ){
		define( 'ADMIN_HEAD_LOGIN', false );
	}

	// Contrôle qu'un administrateur est connecté sauf sur la page de connexion
	if( !ADMIN_HEAD_LOGIN ){
		require_once('users.inc.php');

		if( getenv('oneriashop') !== false ){
			if( !$admin_account->isConnected() ){
				header('Location: /admin/login.php');
				exit;
			}
		}else{
			if (!gu_users_is_connected(false)) {
				header('Location: /admin/login.php');
				exit;
			}
		}
	}

	//Redirige vers la page d'accueil si l'utilisateur tente d'accéder à un module auquel il n'a pas accès
	if( !IS_HTTP_ERROR ){
		gu_rights_admin_accessibility();
	}
	$start_time_page = microtime( true );

	$attr_body = '';
	if( defined('ADMIN_ID_BODY') ){
		$attr_body .= ' id="'.ADMIN_ID_BODY.'"';
	}

	if( defined('ADMIN_CLASS_BODY') ){
		$attr_body .= ' class="'.ADMIN_CLASS_BODY.'"';
	}

	if( isset($_SESSION['lang']) && trim($_SESSION['lang'])!='' ){
		$lang = str_replace('_', '-', htmlspecialchars($_SESSION['lang']));
	}else{
		$lang = 'fr-FR';
	}
?>
<!doctype html>
<html lang="<?php print $lang; ?>">
<head>
	<title><?php print ( defined('ADMIN_PAGE_TITLE') ? ADMIN_PAGE_TITLE.' - ' : '' ).'riaShop - '._('Interface d\'administration'); ?></title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />

	<link href="https://fonts.googleapis.com/css?family=Montserrat:500,600,700%7CRubik&display=swap" rel="stylesheet">
	<?php if( isset($config) ){?>
		<script>
			window.ui_config = {
				img_url: "<?php echo ria_array_get($config, 'img_url', '')?>"
			}
		</script>
	<?php }

		if( getenv('oneriashop') !== false ){
			print '<link rel="icon" href="/images/favicon.ico" />';
		}

		$filename = '';
		if( isset($_SERVER['SCRIPT_URL']) && trim($_SERVER['SCRIPT_URL']) != '' ){
			$filename = $_SERVER['SCRIPT_URL'];
		}elseif( isset($_SERVER['SCRIPT_FILENAME']) && trim($_SERVER['SCRIPT_FILENAME']) != '' ){
			$filename = str_replace( $config['site_dir'].'/admin', '', $_SERVER['SCRIPT_FILENAME'] );
		}

		$file_admin = trim($filename) != '' ? str_replace( '/admin', '', $filename ) : 'none';

		// Déclaration de la variable $file_css
		$file_css = array();
		$popup_ria = 'popup_ria_v1.css';

		// Gestion des fichiers CSS spécifique à l'environnement de maquettes
		// Si on n'est pas sur la page de Login
		// Il est possible de forcer l'environnement de production en passant en paramètre "forced_css_prod"
		if( !ADMIN_HEAD_LOGIN ) {
			$file_css[] = 'layout-v1.css';
		}

		// Importe les fichiers gérant le datepicker et les tris de tableaux
		if( !IS_HTTP_ERROR ){
			switch( $file_admin ){
				case '/customers/popup-export-customers.php':
				case '/documents/images/import/index.php':
					break;
				case '/orders/models.php':
					array_push( $file_css, 'ria-sortable.css' );
					break;
				default:
					$file_css = array_merge( $file_css, array('ria-sortable.css', 'jquery.datepicker.css', 'riadatepicker.css') );
					break;
			}
		}

		if( !ADMIN_HEAD_POPUP ){
			$file_css = array_merge_recursive( $file_css, array($popup_ria) );
		}

		if( ADMIN_HEAD_LOGIN ){
			$file_css = array('login-opti.min.css');
		}

		// CSS spécifiques aux erreurs 403 et 404
		if( IS_HTTP_ERROR ){
			array_push($file_css, 'errors.css');
		}else{ // Autres fichiers CSS

			switch( $file_admin ){
				case '/config/paiements/transfer.php' :
					array_push($file_css, 'paiements.css');
					break;
				case '/config/returns/index.php' :
					array_push($file_css, 'treeview.css');
					break;
				case '/comparators/categories.php' :
				case '/comparators/link-categories.php' :
					array_push($file_css, 'treeview.css', 'comparators.css');
					break;
				case '/tools/banners/edit.php' :
				case '/tools/news/edit.php' :
				case '/customers/segments/segment.php' :
				case '/config/fields/segments/segment.php' :
				case '/documents/edit.php' :
					array_push($file_css, 'segments.css');
					break;
				case '/documents/images/zones.php':
					array_push($file_css, $popup_ria, 'popup-img-zones.css');
					break;
				case '/documents/images/alt.php':
					array_push($file_css, $popup_ria);
					break;
				case '/promotions/specials/edit.php' :
					array_push($file_css, 'segments.css', 'promotions.css');
					break;
				case '/tools/cms/edit.php' :
					array_push($file_css, 'segments.css', 'jquery.fancybox-1.3.4.css', array('file' => 'skin.css', 'dir' => '/admin/js/skin/classic'));
					break;
				case '/documents/popup-add-document.php' :
					array_push($file_css, 'segments.css', 'order-create.css', 'popup-img.css');
					break;
				case '/tools/glossary/edit.php' :
				case '/tools/glossary/index.php' :
					array_push($file_css, 'glossary.css');
					break;
				case '/tools/cms/popup-new-image.php' :
				case '/tools/cms/popup-revision.php' :
				case '/tools/cms/popup_image.php' :
					array_push($file_css, 'riawysiwyg.css');
					break;
				case '/orders/returns/return.php' :
				case '/orders/returns/update.php' :
					array_push($file_css, 'jquery.fancybox-1.3.4.css');
					break;
				case '/stats/prd-no-image.php' :
					array_push($file_css, 'jquery.fancybox-1.3.4.css');
					break;
				case '/stats/popup-image.php' :
					array_push($file_css, 'popup-stats-img.css');
					break;
				case '/tools/rewards/config/index.php' :
					array_push($file_css, 'rewards.css');
					break;
				case '/ajax/orders/ncmd-delivery-edit.php' :
				case '/ajax/orders/ncmd-customers-edit.php' :
				case '/ajax/orders/ncmd-customers-change.php' :
				case '/ajax/orders/ncmd-add-products.php' :
				case '/ajax/orders/ncmd-rights.php' :
					array_push($file_css, 'order-create.css');
					break;
				case '/ajax/media/img_popup.php' :
				case '/catalog/popup-image.php' :
					array_push($file_css, 'order-create.css', 'popup-img.css');
					break;
				case '/customers/index.php' :
					array_push($file_css, 'customers.css');
					break;
				case '/customers/ajax-usr-passwd.php' :
					array_push($file_css, 'order-create.css', 'customers.css');
					break;
				case '/comparators/search/index.php' :
				case '/comparators/stats/index.php' :
					array_push($file_css, 'comparators.css');
					break;
				case '/documents/images/import/index.php' :
					array_push($file_css, 'images.css');
					break;
				case '/stats/prd-conversion.php' :
					array_push($file_css, 'jquery.tooltip.css');
					break;
				case '/customers/edit.php' :
					array_push($file_css, 'jquery.fancybox-1.3.4.css', 'jquery.tooltip.css', 'contact.css');
					break;
				case '/customers/join-file.php' :
				case '/moderation/spam/index.php' :
				case '/moderation/moderation.php' :
				case '/catalog/product.php' :
					array_push($file_css, 'contact.css');
					break;
				case '/comparators/search/popup-multiexport.php' :
				case '/catalog/popup-export-products.php' :
				case '/catalog/popup-export-categories.php' :
				case '/fdv/reports/calls/popup-export-calls.php' :
				case '/catalog/popup-duplicate.php' :
				case '/catalog/popup-duplicate-parent-to-child.php' :
				case '/tools/exports/index.php' :
					array_push($file_css, 'export.css', 'progress-bar.css');
					break;
				case '/comparators/search/popup-save-search.php' :
				case '/comparators/popup-choose-family.php' :
					array_push($file_css, 'popup-ctr.css');
					break;
				case '/comparators/comparators.php' :
				case '/comparators/params.php' :
				case '/comparators/index.php' :
				case '/comparators/mapping-attributs.php' :
					array_push($file_css, 'comparators.css');
					break;
				case '/config/cycles/edit.php' :
					array_push($file_css, 'cycles.css', array('file' => 'skin.css', 'dir' => '/admin/js/skin/classic'));
					break;
				case '/fdv/devices/index.php' :
				case '/fdv/devices/edit.php' :
				case '/fdv/devices/devices-location.php' :
					array_push($file_css, 'fdv.css');
					break;
				case '/config/livraison/stores/edit.php' :
				case '/config/livraison/stores/popup-plage-horaire.php' :
				case '/fdv/reports/edit.php' :
					array_push($file_css, 'jquery-clockpicker.css');
					break;
				case '/config/livraison/zones/edit.php' :
					array_push($file_css, 'delivery.css');
					break;
				case '/config/cross_selling/cross-selling.php' :
					array_push($file_css, 'cross-selling.css');
					break;
				case '/tools/imports/index.php':
					array_push($file_css, 'progress-bar.css', 'imports-v1.css');
					break;
				case '/tools/imports/':
				case '/tools/imports/mapping.php':
					array_push($file_css, 'imports-v1.css');
					break;
			}
		}

		foreach( $file_css as $one_file ){
			if (!is_array($one_file)) {
				$one_file = array('file' => $one_file, 'dir' => '/admin/css');
			}

			print '<link rel="stylesheet" type="text/css" href="'.$one_file['dir'].'/'.$one_file['file'].'?'.ADMIN_ASSET.'" />';
		}
		if( !defined('ADMIN_NO_MOBILE_STYLE') || !ADMIN_NO_MOBILE_STYLE ){
			print '<link rel="stylesheet" type="text/css" media="screen and (max-width: 1023px)" href="/admin/css/mobile.css?'.ADMIN_ASSET.'" />';
		}

		// Import des fichiers JS nécessaires à la page en cours de consultation
		$file_js = array(
			'translates.php?lng='.( isset($_SESSION['lang']) ? $_SESSION['lang'] : '' ),
			'jquery.min.js',
			'jquery-ui-1.8.2.custom.min.js',
			'default.js'
		);

		if( !IS_HTTP_ERROR ){
			switch( $file_admin ){
				// Les fichiers listés ici n'utilisent pas de datepicker, ni de tableau de données, ni d'onglet Médias pouvant être triées
				case '/customers/popup-export-customers.php':
				case '/documents/images/import/index.php':
					break;
				case '/catalog/move.php':
					array_push( $file_js, 'json.js', 'catalog/move.js' );
					break;
				default:
					array_push( $file_js, 'jquery.datepicker.js', 'tab-medias.js', 'ria-sortable.js' );
					break;
			}
		}

		if( !ADMIN_HEAD_POPUP && !IS_HTTP_ERROR ){
			array_push($file_js, 'popup_ria.js');
		}

		if( ADMIN_HEAD_LOGIN ){
			$file_js = array('jquery.min.js');
		}

		if( !IS_HTTP_ERROR ){
			switch( $file_admin ){
				case '/login.php':
					break;
				case '/options/moderation.php' :
					array_push($file_js, 'options-tree.js');
					break;
				case '/orders/orders.php':
				case '/customers/segments/segment.php':
				case '/config/fields/segments/segment.php':
				case '/catalog/product.php':
				case '/documents/images/import/index.php':
				case '/orders/models.php':
					break;
				case '/customers/negotiations/edit.php' :
				case '/customers/negotiations/new.php' :
					array_push($file_js, 'customers/negotiations.js');
					break;
				case '/documents/images/zones.php':
					array_push($file_js, 'interact/interact.min.js', 'documents/images/zones.js');
					break;
				case '/tools/exports/index.php':
					array_push($file_js, 'tools/exports.js');
					break;
					break;
				case '/config/avis_verifie/index.php':
					array_push($file_js, 'config/avis-verifie.js');
					break;
				case '/config/extranet/index.php':
					array_push($file_js, 'config/extranet.js');
				default:
					array_push($file_js, 'riadatepicker.js');
					break;
			}
		}

		foreach ($file_js as $one_file) {
			if (!is_array($one_file)) {
				$one_file = array('file' => $one_file, 'dir' => '/admin/js', 'extern' => false);
			}

			$dir_file_js = $one_file['dir'];
			if (trim($dir_file_js) != '') {
				$dir_file_js .= '/';
			}

			print '
				<script src="'.$dir_file_js.$one_file['file'].( $one_file['extern'] ? '' : '?'.ADMIN_ASSET).'"></script>
			';
		}
	?>
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
	<!-- permet de ne pas activer un zoom sur téléphone (IOS) -->
	<meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">

	<?php if( !ADMIN_HEAD_POPUP && hotjar_include_tenant() ){ ?>
		<script>
			(function(h,o,t,j,a,r){
				h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
				h._hjSettings={hjid:1245476,hjsv:6};
				a=o.getElementsByTagName('head')[0];
				r=o.createElement('script');r.async=1;
				r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
				a.appendChild(r);
			})(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
		</script>
	<?php } ?>
</head>
<body <?php print $attr_body; ?>>
	<?php if( !ADMIN_HEAD_POPUP ){ ?>
		<ul id="site-accessibility-menu" title="Menu d'accessibilité" class="print-none">
			<li><a href="#site-menu">Aller au menu</a></li>
			<li><a href="#site-content">Aller au contenu</a></li>
			<li><a href="#site-search">Aller à la recherche</a></li>
		</ul>
		<h1 class="print-none">
			<a href="/admin/index.php">RiaShop</a>
		</h1>
		<?php if ( !ADMIN_HEAD_LOGIN ) { ?>
			<div class="ria-admin-ui-header print-none">
				<div class="ria-admin-ui-toggle-menu">
					<button class="mdc-icon-button mdc-button--dense js-toggle-menu-mobile "
						aria-label="<?php print _("Ouvrir / Fermer le menu"); ?>"
						aria-hidden="true"
						aria-pressed="true">
						<img src="/admin/dist/images/burger.svg" alt="open menu" class="mdc-icon-button__icon"/>
						<img src="/admin/dist/images/close.svg" alt="close menu" class="mdc-icon-button__icon mdc-icon-button__icon--on"/>
					</button>
				</div>
				<div class="ria-admin-ui-logo-moto">
					<div class="ria-admin-ui-logo"><a href="/admin/index.php"><img class="logo" src="/admin/dist/images/logo-svg" alt="logo RiaShop" /></a></div>
					<div class="ria-admin-ui-moto"><a href="/admin/index.php"><?php print  _("Powerful solutions for successful stories"); ?></a></div>
				</div>
				<?php if( (tnt_tenants_is_yuto_essentiel() && dev_subscribtions_yuto_get()) || !tnt_tenants_is_yuto_essentiel() ){ // Ne pas afficher la barre de recherche si l'abonnement à Yuto Essentiel n'est plus actif ?>
					<div class="ria-admin-ui-searchbar">
						<form id="global-search" method="get" title="Moteur de recherche" action="/admin/search/index.php">
							<div class="mdc-text-field mdc-text-field--fullwidth mdc-text-field--with-trailing-icon">
								<?php
								// Modifie dynamiquement le placeholder du moteur de recherche en fonction des rubriques activées
								$placeholder = _("Rechercher");
								if( gu_users_admin_rights_used('_MDL_CATALOG') ){
									$placeholder .= _(" un produit,");
								}
								if( gu_users_admin_rights_used('_MDL_ORDERS') ){
									$placeholder .= _(" une commande,");

								}
								if( gu_users_admin_rights_used('_MDL_CUSTOMERS') ){
									$placeholder .= _(" un client,");
								}

								$placeholder = substr($placeholder, 0, -1);
								$placeholder .= '...';
								?>
								<input id="mdc-input" class="mdc-text-field__input mdc-text-field__search-input"
											type="text"
											placeholder="<?php print $placeholder; ?>"
											aria-label="Votre recherche" name="q" <?php echo ( isset($_GET['q']) ? 'value="'.htmlspecialchars($_GET['q']).'"' : '' ); ?>>
								<button type="submit" class="mdc-text-field__icon"
									aria-label="<?php print _("Votre recherche"); ?>"
									aria-hidden="true"
									aria-pressed="false" tabindex="1">
									<img src="/admin/dist/images/search.svg" alt="search" width="24" height="24" class="mdc-icon-button__icon"/>
								</button>
							</div>
						</form>
					</div>
				<?php } ?>
				<div class="ria-admin-ui-headactions">
					<?php if( gu_users_admin_rights_used('_MDL_OPTIONS') ){ ?>
						<?php
							$htmltag = 'a';

							$htmltagattrs = ' href="/admin/options/index.php"';
							$htmlbuttonvalue = _('Mes options');

							$class_view_active_menu = strstr($_SERVER['PHP_SELF'],'admin/options/') ? ' ria-button--active' : '';
						?>
						<div class="ria-admin-ui-headaction">
							<<?php echo $htmltag.$htmltagattrs; ?> class="mdc-button ria-button--light ria-button--tti<?php echo $class_view_active_menu; ?>"><?php print $htmlbuttonvalue; ?></<?php echo $htmltag; ?>>
						</div>
					<?php } ?>
					<div class="ria-admin-ui-headaction">
						<a href="/admin/exit.php" class="mdc-button ria-button--outline-light ria-button--tti ria-button--bordered"><?php print htmlspecialchars(_('Déconnexion')); ?></a>
					</div>
				</div>
			</div>

			<div class="ria-admin-ui-menubar print-none">
				<div class="ria-admin-ui-searchbar ria-admin-ui-searchbar--top-menu">
					<form method="get" title="<?php print _("Moteur de recherche"); ?>" action="/admin/search/index.php">
						<div class="mdc-text-field mdc-text-field--fullwidth mdc-text-field--with-trailing-icon">
							<input class="mdc-text-field__input"
										type="text"
										placeholder="Votre recherche"
										aria-label="Votre recherche" name="q" <?php echo ( isset($_GET['q']) ? 'value="'.htmlspecialchars($_GET['q']).'"' : '' ); ?>>
							<button type="submit" class="mdc-text-field__icon"
								aria-label="<?php print _("Lancer la recherche"); ?>"
								aria-hidden="true"
								aria-pressed="false" tabindex="1">
								<img src="/admin/dist/images/search.svg" alt="search" width="24" height="24" class="mdc-icon-button__icon"/>
							</button>
						</div>
					</form>
				</div>
				<?php
					// Affichage d'un avertissement dans le cas où l'on peut accéder à plusieurs instances et que l'on en a choisi une
					if( getenv('oneriashop') !== false ){
						$tnt_access = $admin_account->getAccess();

						if( $tnt_access === 'all' || (is_array($tnt_access) && count($tnt_access) > 1) ){
							if( $admin_account->getTenantSelected() ){
								$essentiel = tnt_tenants_is_yuto_essentiel() ? ' ('._('Yuto Essentiel').')' : '';
								print '<div class="notice header accesstenant">'. _('Vous êtes actuellement sur l\'instance de').' <strong>'.tnt_tenants_get_name($admin_account->getTenantSelected()).$essentiel.'</strong>.<br /><a href="?unset-tenant-admin" onclick="return showPopupSelectTenant(false);">'._('Changer d\'instance').'</a></div>';
							}
						}
					}

					// Affichage d'une notice dans le cas où l'on est sur un abonnement Yuto qui se termine dans 5 jours ou moins
					if( RegisterGCP::onGCloud() && gu_users_rights_used('_RGH_ADMIN_OPTION_SUBSCRIPTION') ){
						$sub = dev_subscribtions_yuto_get();
						if( is_array($sub) && count($sub) && $sub['in_testing'] ){
							// Recherche un abonnement future
							$tmp_sub_fut = dev_subscribtions_yuto_get(false, true);

							if( !is_array($tmp_sub_fut) || !count($tmp_sub_fut) ){
								// Contrôle que la date de fin est bien dans 5 jours au maximum
								$date_end_before = new Datetime(dateparse($sub['date_end']));
								$date_end_before->modify('-5 days');

								if( time() > $date_end_before->getTimestamp() ){
									// Calcul le nombre de jours restant entre aujourd'hui et la date de fin de l'abonnement
									$d1 = new Datetime(dateparse($sub['date_end']).' 23:59:59');
									$d2 = new Datetime();
									$d2 = new Datetime($d2->format('Y-m-d').' 00:00:00');
									$interval = $d1->diff($d2);
									$modulo = (int) $interval->format('%a') + 1;

									// Récupère le type d'abonnement
									$package = ucfirst(RegisterGCP::getPackage($config['tnt_id']));
									print '<div class="notice header align-center accesstenant">';
									if( $modulo > 1 ){
										print str_replace(array('#param[jours restants]#', '#param[package]#', '#param[date de fin]#'), array($modulo, $package, $d1->format('d/m/Y')), _('Votre période d\'essai Yuto #param[package]# se termine dans #param[jours restants]# jours. Après le #param[date de fin]#, votre compte sera fermé.'));
									}else{
										print str_replace(array('#param[package]#'), array($package), _('Votre période d\'essai Yuto #param[package]# se termine aujourd\'hui. Demain, votre compte sera fermé.'));
									}
									print '<br ><a href="#" onclick="displayPopup(\''.str_replace('#param[package]#', $package, _('Activation de mon abonnement Yuto #param[package]#')).'\', \'\', \'/admin/options/popup-reactivate-subscription.php?activation=1\', \'\');return false;"><strong>'.str_replace('#param[package]#', $package, _('J\'active mon abonnement Yuto #param[package]#')).'</strong></a>';
									print '</div>';

									$yuto_end_5days = $modulo;
								}
							}
						}
					}

					require_once('view.admin.inc.php');
					print view_admin_menu();
				?>
			</div>

			<div id="site-content" style="margin-left: <?php print ($config['admin_submenu_state'] == 1 ? '200' : '20'); ?>px;">
				<div id="site-location">
					<?php
					if( Breadcrumbs::count() ){
						print Breadcrumbs::toString();
					}else{
						view_admin_location();
					}
					?>
				</div>
		<?php } else { ?>
			<div id="site-menu"></div>
			<div id="site-location"></div>
		<?php } ?>
	<?php }
