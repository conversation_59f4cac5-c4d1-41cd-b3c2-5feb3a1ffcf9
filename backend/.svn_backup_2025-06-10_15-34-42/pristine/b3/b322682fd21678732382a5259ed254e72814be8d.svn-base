<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagContactPoint;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag PostalAddress utilisé dans store
 */
class TagPostalAddress extends TagContactPoint {
	/**
	 * Le type de tag
	 *
	 * @var string $type
	 */
	protected $type = "PostalAddress";

	public function __construct(){
		
	}

	/**
	 * Ajoute le code postal
	 *
	 * @param string $code
	 * @return self retourne l'instance
	 */
	public function setPostalCode($code){
		return $this->addField('postalCode', $code);
	}

	/**
	 * Ajouter l'adresse
	 *
	 * @param string $address
	 * @return self Retourne l'instnace
	 */
	public function setStreetAddress($address){
		return $this->addField('streetAddress', $address);
	}

	/**
	 * Ajoute le pays
	 *
	 * @param string $cnt_code
	 * @return self retourne l'instance
	 */
	public function setAddressCountry($cnt_code){
		return $this->addField('addressCountry', $cnt_code);
	}

}