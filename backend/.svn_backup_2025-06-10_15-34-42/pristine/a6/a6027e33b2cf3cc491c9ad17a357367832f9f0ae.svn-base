<?xml version="1.0" encoding="UTF-8"?>
<!--
Salesforce.com Enterprise Web Services API Version 27.0
Generated on 2010-11-04 10:49:00 +0000.

Copyright 1999-2010 salesforce.com, inc.
All Rights Reserved
-->
<definitions targetNamespace="urn:enterprise.soap.sforce.com" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="urn:enterprise.soap.sforce.com" xmlns:fns="urn:fault.enterprise.soap.sforce.com" xmlns:ens="urn:sobject.enterprise.soap.sforce.com">
    <types>

        <schema elementFormDefault="qualified" xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:sobject.enterprise.soap.sforce.com">

            <import namespace="urn:enterprise.soap.sforce.com"/>


            <!-- Base sObject (abstract) -->
            <complexType name="sObject">
                <sequence>
                    <element name="fieldsToNull" type="xsd:string" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="Id" type="tns:ID" nillable="true"/>
                </sequence>
            </complexType>

            <complexType name="AggregateResult">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                            <any namespace="##targetNamespace" minOccurs="0" maxOccurs="unbounded" processContents="lax"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Account">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="AccountContactRoles" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="AccountNumber" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="AccountPartnersFrom" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="AccountPartnersTo" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Active__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ActivityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="AnnualRevenue" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Assets" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="BillingCity" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BillingCountry" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BillingPostalCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BillingState" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BillingStreet" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Cases" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Contacts" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Contracts" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="CustomerPriority__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Events" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Fax" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Histories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Industry" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterRecord" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="MasterRecordId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Notes" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="NotesAndAttachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="NumberOfEmployees" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="NumberofLocations__c" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="OpenActivities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Opportunities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpportunityPartnersTo" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Ownership" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="PartnersFrom" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="PartnersTo" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Phone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ProcessInstances" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessSteps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Rating" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SLAExpirationDate__c" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="SLASerialNumber__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SLA__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Shares" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ShippingCity" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ShippingCountry" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ShippingPostalCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ShippingState" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ShippingStreet" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Sic" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Site" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Tasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="TickerSymbol" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UpsellOpportunity__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Website" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="AccountContactRole">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Contact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrimary" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Role" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="AccountHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Field" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="NewValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="OldValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="AccountPartner">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="AccountFrom" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountFromId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="AccountTo" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountToId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrimary" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Opportunity" nillable="true" minOccurs="0" type="ens:Opportunity"/>
                        <element name="OpportunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ReversePartnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Role" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="AccountShare">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountAccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CaseAccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ContactAccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="OpportunityAccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RowCause" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UserOrGroupId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ActivityHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="ActivityType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CallDisposition" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CallDurationInSeconds" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="CallObject" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CallType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DurationInMinutes" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="IsAllDayEvent" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsClosed" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsReminderSet" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsTask" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Location" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Priority" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ReminderDateTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Subject" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="What" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="WhatId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Who" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="WhoId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="AdditionalNumber">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CallCenterId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Phone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ApexClass">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ApiVersion" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Body" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BodyCrc" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsValid" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LengthWithoutComments" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ApexComponent">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ApiVersion" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="ControllerKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ControllerType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Markup" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ApexLog">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Application" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DurationMilliseconds" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Location" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LogLength" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="LogUser" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="LogUserId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Operation" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Request" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="StartTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ApexPage">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ApiVersion" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="ControllerKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ControllerType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Markup" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ApexTrigger">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ApiVersion" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Body" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BodyCrc" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsValid" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LengthWithoutComments" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TableEnumOrId" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UsageAfterDelete" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UsageAfterInsert" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UsageAfterUndelete" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UsageAfterUpdate" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UsageBeforeDelete" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UsageBeforeInsert" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UsageBeforeUpdate" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UsageIsBulk" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Approval">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ApproveComment" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Contract"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="RequestComment" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Asset">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ActivityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Cases" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Contact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Events" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="InstallDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="IsCompetitorProduct" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Notes" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="NotesAndAttachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpenActivities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Price" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="ProcessInstances" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessSteps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Product2" nillable="true" minOccurs="0" type="ens:Product2"/>
                        <element name="Product2Id" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="PurchaseDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="Quantity" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="SerialNumber" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Tasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="UsageEndDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="AssignmentRule">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Active" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SobjectType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="AsyncApexJob">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ApexClass" nillable="true" minOccurs="0" type="ens:ApexClass"/>
                        <element name="ApexClassId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CompletedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="ExtendedStatus" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="JobItemsProcessed" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="JobType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastProcessed" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastProcessedOffset" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="MethodName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NumberOfErrors" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="ParentJobId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="TotalJobItems" nillable="true" minOccurs="0" type="xsd:int"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Attachment">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Body" nillable="true" minOccurs="0" type="xsd:base64Binary"/>
                        <element name="BodyLength" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="ContentType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrivate" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="BrandTemplate">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DeveloperName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Value" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="BusinessHours">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Cases" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="FridayEndTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="FridayStartTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDefault" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MondayEndTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="MondayStartTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SaturdayEndTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="SaturdayStartTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="SundayEndTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="SundayStartTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="ThursdayEndTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="ThursdayStartTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="TimeZoneSidKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="TuesdayEndTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="TuesdayStartTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="WednesdayEndTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        <element name="WednesdayStartTime" nillable="true" minOccurs="0" type="xsd:time"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="BusinessProcess">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TableEnumOrId" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CallCenter">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="InternalName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Version" nillable="true" minOccurs="0" type="xsd:double"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Campaign">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ActivityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ActualCost" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="AmountAllOpportunities" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="AmountWonOpportunities" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="BudgetedCost" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="CampaignMemberRecordTypeId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CampaignMembers" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ChildCampaigns" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EndDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="Events" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ExpectedResponse" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="ExpectedRevenue" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Leads" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NumberOfContacts" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="NumberOfConvertedLeads" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="NumberOfLeads" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="NumberOfOpportunities" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="NumberOfResponses" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="NumberOfWonOpportunities" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="NumberSent" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="OpenActivities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Opportunities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Campaign"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ProcessInstances" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessSteps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Shares" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="StartDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Tasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CampaignMember">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Campaign" nillable="true" minOccurs="0" type="ens:Campaign"/>
                        <element name="CampaignId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Contact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="FirstRespondedDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="HasResponded" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Lead" nillable="true" minOccurs="0" type="ens:Lead"/>
                        <element name="LeadId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CampaignMemberStatus">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CampaignId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="HasResponded" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDefault" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Label" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CampaignShare">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Campaign" nillable="true" minOccurs="0" type="ens:Campaign"/>
                        <element name="CampaignAccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CampaignId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="RowCause" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UserOrGroupId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Case">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ActivityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Asset" nillable="true" minOccurs="0" type="ens:Asset"/>
                        <element name="AssetId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CaseComments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CaseContactRoles" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CaseNumber" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CaseSolutions" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Cases" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ClosedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Contact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EngineeringReqNumber__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Events" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="HasCommentsUnreadByOwner" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="HasSelfServiceComments" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Histories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="IsClosed" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsEscalated" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="OpenActivities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Origin" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Case"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="PotentialLiability__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Priority" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ProcessInstances" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessSteps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Product__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Reason" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SLAViolation__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Shares" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Solutions" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Subject" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SuppliedCompany" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SuppliedEmail" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SuppliedName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SuppliedPhone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Tasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="TeamMembers" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="TeamTemplateRecords" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseComment">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CommentBody" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPublished" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Case"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseContactRole">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Cases" nillable="true" minOccurs="0" type="ens:Case"/>
                        <element name="CasesId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Contact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Role" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Case" nillable="true" minOccurs="0" type="ens:Case"/>
                        <element name="CaseId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Field" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="NewValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="OldValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseShare">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Case" nillable="true" minOccurs="0" type="ens:Case"/>
                        <element name="CaseAccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CaseId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="RowCause" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UserOrGroupId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseSolution">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Case" nillable="true" minOccurs="0" type="ens:Case"/>
                        <element name="CaseId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Solution" nillable="true" minOccurs="0" type="ens:Solution"/>
                        <element name="SolutionId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseStatus">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsClosed" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDefault" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseTeamMember">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MemberId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Case"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TeamRoleId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="TeamTemplateMemberId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseTeamRole">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="AccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="PreferencesVisibleInCSP" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseTeamTemplate">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseTeamTemplateMember">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MemberId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TeamRoleId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="TeamTemplateId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CaseTeamTemplateRecord">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Case"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TeamTemplateId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CategoryData">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CategoryNodeId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="RelatedSobjectId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CategoryNode">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SortStyle" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Community">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Contact">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountContactRoles" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ActivityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Assets" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="AssistantName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="AssistantPhone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Birthdate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="CampaignMembers" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CaseContactRoles" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Cases" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ContractContactRoles" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ContractsSigned" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Department" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Email" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EmailBouncedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="EmailBouncedReason" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EmailStatuses" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Events" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Fax" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FirstName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Histories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="HomePhone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Languages__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="LastCURequestDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastCUUpdateDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LeadSource" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Level__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MailingCity" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MailingCountry" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MailingPostalCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MailingState" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MailingStreet" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MasterRecord" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="MasterRecordId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="MobilePhone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Notes" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="NotesAndAttachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpenActivities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Opportunities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpportunityContactRoles" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OtherCity" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OtherCountry" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OtherPhone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OtherPostalCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OtherState" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OtherStreet" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Phone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ProcessInstances" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessSteps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ReportsTo" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ReportsToId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Salutation" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Shares" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Tasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Title" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ContactHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Contact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Field" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="NewValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="OldValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ContactShare">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Contact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ContactAccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="RowCause" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UserOrGroupId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Contract">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ActivatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="ActivatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ActivatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="ActivityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Approvals" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="BillingCity" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BillingCountry" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BillingPostalCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BillingState" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BillingStreet" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CompanySigned" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CompanySignedDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="CompanySignedId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ContractContactRoles" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ContractNumber" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ContractTerm" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="CustomerSigned" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="CustomerSignedDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="CustomerSignedId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CustomerSignedTitle" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EndDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="Events" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Histories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="LastApprovedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Notes" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="NotesAndAttachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpenActivities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="OwnerExpirationNotice" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ProcessInstances" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessSteps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="SpecialTerms" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="StartDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="StatusCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Tasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ContractContactRole">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Contact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Contract" nillable="true" minOccurs="0" type="ens:Contract"/>
                        <element name="ContractId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrimary" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Role" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ContractHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Contract" nillable="true" minOccurs="0" type="ens:Contract"/>
                        <element name="ContractId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Field" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="NewValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="OldValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ContractStatus">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDefault" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="StatusCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="CronTrigger">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="CronExpression" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EndTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="NextFireTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="PreviousFireTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="StartTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="State" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="TimeZoneSidKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="TimesTriggered" nillable="true" minOccurs="0" type="xsd:int"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Dashboard">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="BackgroundDirection" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BackgroundEnd" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="BackgroundStart" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DeveloperName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FolderId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LeftSize" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MiddleSize" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RightSize" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RunningUserId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TextColor" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="Title" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="TitleColor" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="TitleSize" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Document">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Author" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="AuthorId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Body" nillable="true" minOccurs="0" type="xsd:base64Binary"/>
                        <element name="BodyLength" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="ContentType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DeveloperName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Folder" nillable="true" minOccurs="0" type="ens:Folder"/>
                        <element name="FolderId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="IsBodySearchable" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsInternalUseOnly" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPublic" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Keywords" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Url" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="DocumentAttachmentMap">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="DocumentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="DocumentSequence" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="EmailServicesAddress">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="AuthorizedSenders" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="EmailDomainName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Function" nillable="true" minOccurs="0" type="ens:EmailServicesFunction"/>
                        <element name="FunctionId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LocalPart" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RunAsUserId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="EmailServicesFunction">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="AddressInactiveAction" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Addresses" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ApexClassId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="AttachmentOption" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="AuthenticationFailureAction" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="AuthorizationFailureAction" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="AuthorizedSenders" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="ErrorRoutingAddress" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FunctionInactiveAction" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FunctionName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsAuthenticationRequired" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsErrorRoutingEnabled" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsTextAttachmentsAsBinary" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsTextTruncated" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsTlsRequired" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="OverLimitAction" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="EmailStatus">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="EmailTemplateName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FirstOpenDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastOpenDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Task" nillable="true" minOccurs="0" type="ens:Task"/>
                        <element name="TaskId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="TimesOpened" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="Who" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="WhoId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="EmailTemplate">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ApiVersion" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Body" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="BrandTemplateId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DeveloperName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Encoding" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Folder" nillable="true" minOccurs="0" type="ens:Folder"/>
                        <element name="FolderId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="HtmlValue" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastUsedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Markup" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Subject" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TemplateStyle" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="TemplateType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="TimesUsed" nillable="true" minOccurs="0" type="xsd:int"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Event">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="ActivityDateTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DurationInMinutes" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="EndDateTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="EventAttendees" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="GroupEventType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsAllDayEvent" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsArchived" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsChild" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsGroupEvent" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrivate" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsRecurrence" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsReminderSet" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Location" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="RecurrenceActivityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="RecurrenceDayOfMonth" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="RecurrenceDayOfWeekMask" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="RecurrenceEndDateOnly" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="RecurrenceInstance" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurrenceInterval" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="RecurrenceMonthOfYear" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurrenceStartDateTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="RecurrenceTimeZoneSidKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurrenceType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurringEvents" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ReminderDateTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="ShowAs" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="StartDateTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Subject" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="What" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="WhatId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Who" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="WhoId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="EventAttendee">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Attendee" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="AttendeeId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Event" nillable="true" minOccurs="0" type="ens:Event"/>
                        <element name="EventId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="RespondedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Response" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="FiscalYearSettings">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EndDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="IsStandardYear" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="PeriodId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="PeriodLabelScheme" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="PeriodPrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Periods" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="QuarterLabelScheme" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="QuarterPrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="StartDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="WeekLabelScheme" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="WeekStartDay" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="YearType" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Folder">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="AccessType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="DeveloperName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsReadonly" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ForecastShare">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="AccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CanSubmit" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="RowCause" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UserOrGroupId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="UserRoleId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Group">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="DelegatedUsers" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="DoesIncludeBosses" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="DoesSendEmailToMembers" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Email" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="GroupMembers" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="QueueSobjects" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="RelatedId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="GroupMember">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Group" nillable="true" minOccurs="0" type="ens:Group"/>
                        <element name="GroupId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="UserOrGroupId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Holiday">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EndTimeInMinutes" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="IsAllDay" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsRecurrence" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurrenceDayOfMonth" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="RecurrenceDayOfWeekMask" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="RecurrenceEndDateOnly" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="RecurrenceInstance" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurrenceInterval" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="RecurrenceMonthOfYear" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurrenceStartDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="RecurrenceType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="StartTimeInMinutes" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Idea">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Body" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Categories" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Comments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Community" nillable="true" minOccurs="0" type="ens:Community"/>
                        <element name="CommunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsHtml" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsLocked" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastComment" nillable="true" minOccurs="0" type="ens:IdeaComment"/>
                        <element name="LastCommentDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastCommentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="NumComments" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="ParentIdeaId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Title" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="VoteScore" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="VoteTotal" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Votes" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="IdeaComment">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CommentBody" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Idea" nillable="true" minOccurs="0" type="ens:Idea"/>
                        <element name="IdeaId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsHtml" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Lead">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ActivityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="AnnualRevenue" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Campaign" nillable="true" minOccurs="0" type="ens:Campaign"/>
                        <element name="CampaignMembers" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="City" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Company" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ConvertedAccount" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="ConvertedAccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ConvertedContact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ConvertedContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ConvertedDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="ConvertedOpportunity" nillable="true" minOccurs="0" type="ens:Opportunity"/>
                        <element name="ConvertedOpportunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Country" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="CurrentGenerators__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Email" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EmailBouncedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="EmailBouncedReason" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EmailStatuses" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Events" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Fax" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FirstName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Histories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Industry" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsConverted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsUnreadByOwner" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LeadSource" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MasterRecord" nillable="true" minOccurs="0" type="ens:Lead"/>
                        <element name="MasterRecordId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="MobilePhone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Notes" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="NotesAndAttachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="NumberOfEmployees" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="NumberofLocations__c" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="OpenActivities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Phone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="PostalCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Primary__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ProcessInstances" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessSteps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProductInterest__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Rating" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SICCode__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Salutation" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Shares" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="State" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Street" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Tasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Title" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Website" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="LeadHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Field" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Lead" nillable="true" minOccurs="0" type="ens:Lead"/>
                        <element name="LeadId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="NewValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="OldValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="LeadShare">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Lead" nillable="true" minOccurs="0" type="ens:Lead"/>
                        <element name="LeadAccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LeadId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="RowCause" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UserOrGroupId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="LeadStatus">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsConverted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDefault" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="MailmergeTemplate">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Body" nillable="true" minOccurs="0" type="xsd:base64Binary"/>
                        <element name="BodyLength" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Filename" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastUsedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Name">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Alias" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Email" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FirstName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Phone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Profile" nillable="true" minOccurs="0" type="ens:Profile"/>
                        <element name="ProfileId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Title" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UserRole" nillable="true" minOccurs="0" type="ens:UserRole"/>
                        <element name="UserRoleId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Username" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Note">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Body" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrivate" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Title" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="NoteAndAttachment">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsNote" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrivate" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Title" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="OpenActivity">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="ActivityType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CallDisposition" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CallDurationInSeconds" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="CallObject" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CallType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DurationInMinutes" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="IsAllDayEvent" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsClosed" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsReminderSet" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsTask" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Location" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Priority" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ReminderDateTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Subject" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="What" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="WhatId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Who" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="WhoId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Opportunity">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="AccountPartners" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ActivityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Amount" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Campaign" nillable="true" minOccurs="0" type="ens:Campaign"/>
                        <element name="CampaignId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CloseDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="CurrentGenerators__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DeliveryInstallationStatus__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Events" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ExpectedRevenue" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Fiscal" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FiscalQuarter" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="FiscalYear" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="ForecastCategory" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ForecastCategoryName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="HasOpportunityLineItem" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Histories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="IsClosed" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrivate" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsWon" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LeadSource" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MainCompetitors__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NextStep" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Notes" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="NotesAndAttachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpenActivities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpportunityCompetitors" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpportunityContactRoles" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpportunityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpportunityLineItems" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpportunityPartnersFrom" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OrderNumber__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Partners" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Pricebook2" nillable="true" minOccurs="0" type="ens:Pricebook2"/>
                        <element name="Pricebook2Id" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Probability" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="ProcessInstances" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessSteps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Shares" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="StageName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Tasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="TotalOpportunityQuantity" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="TrackingNumber__c" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="OpportunityCompetitor">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CompetitorName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Opportunity" nillable="true" minOccurs="0" type="ens:Opportunity"/>
                        <element name="OpportunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Strengths" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Weaknesses" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="OpportunityContactRole">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Contact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrimary" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Opportunity" nillable="true" minOccurs="0" type="ens:Opportunity"/>
                        <element name="OpportunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Role" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="OpportunityFieldHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Field" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="NewValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="OldValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="Opportunity" nillable="true" minOccurs="0" type="ens:Opportunity"/>
                        <element name="OpportunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="OpportunityHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Amount" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="CloseDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="ExpectedRevenue" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="ForecastCategory" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Opportunity" nillable="true" minOccurs="0" type="ens:Opportunity"/>
                        <element name="OpportunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Probability" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="StageName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="OpportunityLineItem">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="ListPrice" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Opportunity" nillable="true" minOccurs="0" type="ens:Opportunity"/>
                        <element name="OpportunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="PricebookEntry" nillable="true" minOccurs="0" type="ens:PricebookEntry"/>
                        <element name="PricebookEntryId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Quantity" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="ServiceDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TotalPrice" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="UnitPrice" nillable="true" minOccurs="0" type="xsd:double"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="OpportunityPartner">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="AccountTo" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountToId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrimary" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Opportunity" nillable="true" minOccurs="0" type="ens:Opportunity"/>
                        <element name="OpportunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ReversePartnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Role" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="OpportunityShare">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Opportunity" nillable="true" minOccurs="0" type="ens:Opportunity"/>
                        <element name="OpportunityAccessLevel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OpportunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="RowCause" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UserOrGroupId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="OpportunityStage">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="DefaultProbability" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ForecastCategory" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ForecastCategoryName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsClosed" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsWon" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="OrgWideEmailAddress">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Address" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="DisplayName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsAllowAllProfiles" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Organization">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="City" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ComplianceBccEmail" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Country" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="DefaultAccountAccess" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DefaultCalendarAccess" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DefaultCampaignAccess" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DefaultCaseAccess" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DefaultContactAccess" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DefaultLeadAccess" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DefaultLocaleSidKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DefaultOpportunityAccess" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DefaultPricebookAccess" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Division" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Fax" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FiscalYearStartMonth" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="LanguageLocaleKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MonthlyPageViewsEntitlement" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="MonthlyPageViewsUsed" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OrganizationType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Phone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="PostalCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="PreferencesRequireOpportunityProducts" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PrimaryContact" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ReceivesAdminInfoEmails" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="ReceivesInfoEmails" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="State" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Street" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TrialExpirationDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="UiSkin" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UsesStartDateAsFiscalYearName" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="WebToCaseDefaultOrigin" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Partner">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="AccountFrom" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountFromId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="AccountTo" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountToId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPrimary" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Opportunity" nillable="true" minOccurs="0" type="ens:Opportunity"/>
                        <element name="OpportunityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ReversePartnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Role" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="PartnerRole">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ReverseRole" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Period">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="EndDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="FiscalYearSettings" nillable="true" minOccurs="0" type="ens:FiscalYearSettings"/>
                        <element name="FiscalYearSettingsId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="IsForecastPeriod" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Number" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="PeriodLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="QuarterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="StartDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Pricebook2">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsStandard" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Opportunities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="PricebookEntries" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="PricebookEntry">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OpportunityLineItems" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Pricebook2" nillable="true" minOccurs="0" type="ens:Pricebook2"/>
                        <element name="Pricebook2Id" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Product2" nillable="true" minOccurs="0" type="ens:Product2"/>
                        <element name="Product2Id" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ProductCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="UnitPrice" nillable="true" minOccurs="0" type="xsd:double"/>
                        <element name="UseStandardPrice" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ProcessInstance">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Steps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="StepsAndWorkitems" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TargetObject" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="TargetObjectId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Workitems" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ProcessInstanceHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Actor" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="ActorId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Comments" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPending" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="OriginalActor" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OriginalActorId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ProcessInstance" nillable="true" minOccurs="0" type="ens:ProcessInstance"/>
                        <element name="ProcessInstanceId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="RemindersSent" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="StepStatus" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TargetObject" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="TargetObjectId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ProcessInstanceStep">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Actor" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="ActorId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Comments" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="OriginalActor" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OriginalActorId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ProcessInstance" nillable="true" minOccurs="0" type="ens:ProcessInstance"/>
                        <element name="ProcessInstanceId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="StepStatus" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ProcessInstanceWorkitem">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Actor" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="ActorId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="OriginalActor" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OriginalActorId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ProcessInstance" nillable="true" minOccurs="0" type="ens:ProcessInstance"/>
                        <element name="ProcessInstanceId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Product2">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ActivityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Assets" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Events" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Family" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Notes" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="NotesAndAttachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpenActivities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="OpportunityLineItems" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="PricebookEntries" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessInstances" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessSteps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProductCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Tasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Profile">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="PermissionsApiEnabled" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsAuthorApex" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsBulkApiHardDelete" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsCanUseNewDashboardBuilder" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsConvertLeads" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsCreateMultiforce" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsCustomSidebarOnAllPages" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsCustomizeApplication" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsEditCaseComments" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsEditEvent" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsEditOppLineItemUnitPrice" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsEditPublicDocuments" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsEditReadonlyFields" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsEditReports" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsEditTask" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsEnableNotifications" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsImportLeads" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsInstallMultiforce" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageAnalyticSnapshots" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageBusinessHourHolidays" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageCallCenters" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageCases" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageCategories" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageCssUsers" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageCustomReportTypes" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageDashboards" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageDataCategories" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageDataIntegrations" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageDynamicDashboards" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageEmailClientConfig" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageLeads" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageMobile" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageRemoteAccess" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageSelfService" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageSolutions" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsManageUsers" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsMassInlineEdit" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsModifyAllData" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsNewReportBuilder" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsPasswordNeverExpires" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsPublishMultiforce" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsRunReports" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsScheduleReports" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsSendSitRequests" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsSolutionImport" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsTransferAnyCase" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsTransferAnyEntity" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsTransferAnyLead" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsUseTeamReassignWizards" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsViewAllData" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsViewDataCategories" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsViewMyTeamsDashboards" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="PermissionsViewSetup" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="UserLicense" nillable="true" minOccurs="0" type="ens:UserLicense"/>
                        <element name="UserLicenseId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="UserType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Users" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="QueueSobject">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Queue" nillable="true" minOccurs="0" type="ens:Group"/>
                        <element name="QueueId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SobjectType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="RecordType">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="BusinessProcessId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DeveloperName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SobjectType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Report">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DeveloperName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastRunDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Scontrol">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Binary" nillable="true" minOccurs="0" type="xsd:base64Binary"/>
                        <element name="BodyLength" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="ContentSource" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DeveloperName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EncodingKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Filename" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="HtmlWrapper" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SupportsCaching" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="SelfServiceUser">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Email" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FirstName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LanguageLocaleKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastLoginDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LocaleSidKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SuperUser" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TimeZoneSidKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Username" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Site">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Admin" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="AdminId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="AnalyticsTrackingCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="DailyBandwidthLimit" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="DailyBandwidthUsed" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="DailyRequestTimeLimit" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="DailyRequestTimeUsed" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Histories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MonthlyPageViewsEntitlement" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OptionsAllowHomePage" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="OptionsAllowStandardAnswersPages" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="OptionsAllowStandardIdeasPages" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="OptionsAllowStandardLookups" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="OptionsAllowStandardSearch" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="OptionsEnableFeeds" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Subdomain" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TopLevelDomain" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UrlPathPrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="SiteHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Field" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="NewValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="OldValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="Site" nillable="true" minOccurs="0" type="ens:Site"/>
                        <element name="SiteId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Solution">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="ActivityHistories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CaseSolutions" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Events" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Histories" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsHtml" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPublished" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsPublishedInPublicKb" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsReviewed" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="OpenActivities" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ProcessInstances" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ProcessSteps" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="SolutionName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SolutionNote" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SolutionNumber" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Tasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="TimesUsed" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="Votes" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="SolutionHistory">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Field" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="NewValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="OldValue" nillable="true" minOccurs="0" type="xsd:anyType"/>
                        <element name="Solution" nillable="true" minOccurs="0" type="ens:Solution"/>
                        <element name="SolutionId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="SolutionStatus">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDefault" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsReviewed" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="StaticResource">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Body" nillable="true" minOccurs="0" type="xsd:base64Binary"/>
                        <element name="BodyLength" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="CacheControl" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ContentType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Task">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Account" nillable="true" minOccurs="0" type="ens:Account"/>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ActivityDate" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="Attachments" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="CallDisposition" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CallDurationInSeconds" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="CallObject" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CallType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="IsArchived" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsClosed" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsRecurrence" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsReminderSet" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Owner" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="OwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Priority" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurrenceActivityId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="RecurrenceDayOfMonth" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="RecurrenceDayOfWeekMask" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="RecurrenceEndDateOnly" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="RecurrenceInstance" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurrenceInterval" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="RecurrenceMonthOfYear" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurrenceStartDateOnly" nillable="true" minOccurs="0" type="xsd:date"/>
                        <element name="RecurrenceTimeZoneSidKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurrenceType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RecurringTasks" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="ReminderDateTime" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Status" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Subject" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="What" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="WhatId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Who" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="WhoId" nillable="true" minOccurs="0" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="TaskPriority">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDefault" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsHighPriority" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="TaskStatus">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsClosed" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsDefault" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SortOrder" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="User">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="AccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Alias" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CallCenterId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="City" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CommunityNickname" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CompanyName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Contact" nillable="true" minOccurs="0" type="ens:Contact"/>
                        <element name="ContactId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ContractsSigned" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Country" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="DelegatedApproverId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="DelegatedUsers" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="Department" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Division" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Email" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EmailEncodingKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EmployeeNumber" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Extension" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Fax" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FederationIdentifier" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="FirstName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ForecastEnabled" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsActive" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LanguageLocaleKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastLoginDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LastName" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="LastPasswordChangeDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LocaleSidKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Manager" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="ManagerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="MobilePhone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OfflinePdaTrialExpirationDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="OfflineTrialExpirationDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Phone" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="PostalCode" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Profile" nillable="true" minOccurs="0" type="ens:Profile"/>
                        <element name="ProfileId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ReceivesAdminInfoEmails" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="ReceivesInfoEmails" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="State" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Street" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="TimeZoneSidKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Title" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="UserPermissionsCallCenterAutoLogin" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UserPermissionsMarketingUser" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UserPermissionsMobileUser" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UserPermissionsOfflineUser" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UserPermissionsSupportUser" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UserPreferences" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        <element name="UserPreferencesActivityRemindersPopup" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UserPreferencesApexPagesDeveloperMode" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UserPreferencesEventRemindersCheckboxDefault" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UserPreferencesReminderSoundOff" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UserPreferencesTaskRemindersCheckboxDefault" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="UserRole" nillable="true" minOccurs="0" type="ens:UserRole"/>
                        <element name="UserRoleId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="UserType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Username" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="UserLicense">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="LicenseDefinitionKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MonthlyLoginsEntitlement" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="MonthlyLoginsUsed" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="UserPreference">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="Preference" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="UserId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="Value" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="UserRole">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CaseAccessForAccountOwner" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ContactAccessForAccountOwner" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ForecastUserId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="MayForecastManagerShare" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OpportunityAccessForAccountOwner" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="ParentRoleId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="PortalAccountId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="PortalAccountOwnerId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="PortalType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RollupDescription" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Users" nillable="true" minOccurs="0" type="tns:QueryResult"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="Vote">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="IsDeleted" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Parent" nillable="true" minOccurs="0" type="ens:Name"/>
                        <element name="ParentId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Type" nillable="true" minOccurs="0" type="xsd:string"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="WebLink">
                <complexContent>
                    <extension base="ens:sObject">
                        <sequence>
                        <element name="CreatedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="CreatedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="CreatedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Description" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="DisplayType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="EncodingKey" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="HasMenubar" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="HasScrollbars" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="HasToolbar" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="Height" nillable="true" minOccurs="0" type="xsd:int"/>
                        <element name="IsProtected" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="IsResizable" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="LastModifiedBy" nillable="true" minOccurs="0" type="ens:User"/>
                        <element name="LastModifiedById" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="LastModifiedDate" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="LinkType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="MasterLabel" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Name" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="NamespacePrefix" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="OpenType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="PageOrSobjectType" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Position" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="RequireRowSelection" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="ScontrolId" nillable="true" minOccurs="0" type="tns:ID"/>
                        <element name="ShowsLocation" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="ShowsStatus" nillable="true" minOccurs="0" type="xsd:boolean"/>
                        <element name="SystemModstamp" nillable="true" minOccurs="0" type="xsd:dateTime"/>
                        <element name="Url" nillable="true" minOccurs="0" type="xsd:string"/>
                        <element name="Width" nillable="true" minOccurs="0" type="xsd:int"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>


        </schema>

        <schema elementFormDefault="qualified" xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:enterprise.soap.sforce.com">

            <import namespace="urn:sobject.enterprise.soap.sforce.com"/>

            <!-- Our simple ID Type -->
            <simpleType name="ID">
                <restriction base="xsd:string">
                    <length value="18"/>
                    <pattern value="[a-zA-Z0-9]{18}"/>
                </restriction>
            </simpleType>

            <simpleType name="QueryLocator">
                <restriction base="xsd:string"/>
            </simpleType>

            <!-- Shared Result Types -->
            <complexType name="QueryResult">
                <sequence>
                    <element name="done" type="xsd:boolean"/>
                    <element name="queryLocator" type="tns:QueryLocator" nillable="true"/>
                    <element name="records" type="ens:sObject" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="size" type="xsd:int"/>
                </sequence>
            </complexType>



            <!-- Search Result -->
            <complexType name="SearchResult">
                <sequence>
                    <element name="searchRecords" minOccurs="0" maxOccurs="unbounded" type="tns:SearchRecord"/>
                    <element name="sforceReserved" minOccurs="0" maxOccurs="1" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="SearchRecord">
                <sequence>
                    <element name="record" type="ens:sObject"/>
                </sequence>
            </complexType>

            <!-- GetUpdated Result -->
            <complexType name="GetUpdatedResult">
                <sequence>
                    <element name="ids" minOccurs="0" maxOccurs="unbounded" type="tns:ID"/>
                    <element name="latestDateCovered" type="xsd:dateTime"/>
                    <element name="sforceReserved" minOccurs="0" maxOccurs="1" type="xsd:string"/>
                </sequence>
            </complexType>

            <!-- GetDeleted Result -->
            <complexType name="GetDeletedResult">
                <sequence>
                    <element name="deletedRecords" minOccurs="0" maxOccurs="unbounded" type="tns:DeletedRecord"/>
                    <element name="earliestDateAvailable" type="xsd:dateTime"/>
                    <element name="latestDateCovered" type="xsd:dateTime"/>
                    <element name="sforceReserved" minOccurs="0" maxOccurs="1" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="DeletedRecord">
                <sequence>
                    <element name="deletedDate" type="xsd:dateTime"/>
                    <element name="id" type="tns:ID"/>
                </sequence>
            </complexType>


            <complexType name="GetServerTimestampResult">
                <sequence>
                    <element name="timestamp" type="xsd:dateTime"/>
                </sequence>
            </complexType>


            <!-- InvalidateSessions Result -->
            <complexType name="InvalidateSessionsResult">
                <sequence>
                    <element name="errors" type="tns:Error" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="success" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="SetPasswordResult">
            </complexType>

            <complexType name="ResetPasswordResult">
                <sequence>
                    <element name="password" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="GetUserInfoResult">
                <sequence>
                    <element name="accessibilityMode" type="xsd:boolean"/>
                    <element name="currencySymbol" type="xsd:string" nillable="true"/>
                    <element name="orgDefaultCurrencyIsoCode" type="xsd:string" nillable="true"/>
                    <element name="orgDisallowHtmlAttachments" type="xsd:boolean"/>
                    <element name="orgHasPersonAccounts" type="xsd:boolean"/>
                    <element name="organizationId" type="tns:ID"/>
                    <element name="organizationMultiCurrency" type="xsd:boolean"/>
                    <element name="organizationName" type="xsd:string"/>
                    <element name="profileId" type="tns:ID"/>
                    <element name="roleId" type="tns:ID" nillable="true"/>
                    <element name="userDefaultCurrencyIsoCode" type="xsd:string" nillable="true"/>
                    <element name="userEmail" type="xsd:string"/>
                    <element name="userFullName" type="xsd:string"/>
                    <element name="userId" type="tns:ID"/>
                    <element name="userLanguage" type="xsd:string"/>
                    <element name="userLocale" type="xsd:string"/>
                    <element name="userName" type="xsd:string"/>
                    <element name="userTimeZone" type="xsd:string"/>
                    <element name="userType" type="xsd:string"/>
                    <element name="userUiSkin" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="LoginResult">
                <sequence>
                    <element name="metadataServerUrl" type="xsd:string" nillable="true"/>
                    <element name="passwordExpired" type="xsd:boolean"/>
                    <element name="sandbox" type="xsd:boolean"/>
                    <element name="serverUrl" type="xsd:string" nillable="true"/>
                    <element name="sessionId" type="xsd:string" nillable="true"/>

                    <element name="userId" type="tns:ID" nillable="true"/>
                    <element name="userInfo" type="tns:GetUserInfoResult" minOccurs="0"/>
                </sequence>
            </complexType>

            <simpleType name="StatusCode">
                <restriction base="xsd:string">
                    <enumeration value="ALL_OR_NONE_OPERATION_ROLLED_BACK"/>
                    <enumeration value="ALREADY_IN_PROCESS"/>
                    <enumeration value="ASSIGNEE_TYPE_REQUIRED"/>
                    <enumeration value="BAD_CUSTOM_ENTITY_PARENT_DOMAIN"/>
                    <enumeration value="BCC_NOT_ALLOWED_IF_BCC_COMPLIANCE_ENABLED"/>
                    <enumeration value="CANNOT_CASCADE_PRODUCT_ACTIVE"/>
                    <enumeration value="CANNOT_CHANGE_FIELD_TYPE_OF_APEX_REFERENCED_FIELD"/>
                    <enumeration value="CANNOT_CREATE_ANOTHER_MANAGED_PACKAGE"/>
                    <enumeration value="CANNOT_DEACTIVATE_DIVISION"/>
                    <enumeration value="CANNOT_DELETE_LAST_DATED_CONVERSION_RATE"/>
                    <enumeration value="CANNOT_DELETE_MANAGED_OBJECT"/>
                    <enumeration value="CANNOT_DISABLE_LAST_ADMIN"/>
                    <enumeration value="CANNOT_ENABLE_IP_RESTRICT_REQUESTS"/>
                    <enumeration value="CANNOT_INSERT_UPDATE_ACTIVATE_ENTITY"/>
                    <enumeration value="CANNOT_MODIFY_MANAGED_OBJECT"/>
                    <enumeration value="CANNOT_RENAME_APEX_REFERENCED_FIELD"/>
                    <enumeration value="CANNOT_RENAME_APEX_REFERENCED_OBJECT"/>
                    <enumeration value="CANNOT_REPARENT_RECORD"/>
                    <enumeration value="CANNOT_UPDATE_CONVERTED_LEAD"/>
                    <enumeration value="CANT_DISABLE_CORP_CURRENCY"/>
                    <enumeration value="CANT_UNSET_CORP_CURRENCY"/>
                    <enumeration value="CHILD_SHARE_FAILS_PARENT"/>
                    <enumeration value="CIRCULAR_DEPENDENCY"/>
                    <enumeration value="COMMUNITY_NOT_ACCESSIBLE"/>
                    <enumeration value="CUSTOM_CLOB_FIELD_LIMIT_EXCEEDED"/>
                    <enumeration value="CUSTOM_ENTITY_OR_FIELD_LIMIT"/>
                    <enumeration value="CUSTOM_FIELD_INDEX_LIMIT_EXCEEDED"/>
                    <enumeration value="CUSTOM_INDEX_EXISTS"/>
                    <enumeration value="CUSTOM_LINK_LIMIT_EXCEEDED"/>
                    <enumeration value="CUSTOM_TAB_LIMIT_EXCEEDED"/>
                    <enumeration value="DELETE_FAILED"/>
                    <enumeration value="DELETE_REQUIRED_ON_CASCADE"/>
                    <enumeration value="DEPENDENCY_EXISTS"/>
                    <enumeration value="DUPLICATE_CASE_SOLUTION"/>
                    <enumeration value="DUPLICATE_COMM_NICKNAME"/>
                    <enumeration value="DUPLICATE_CUSTOM_ENTITY_DEFINITION"/>
                    <enumeration value="DUPLICATE_CUSTOM_TAB_MOTIF"/>
                    <enumeration value="DUPLICATE_DEVELOPER_NAME"/>
                    <enumeration value="DUPLICATE_EXTERNAL_ID"/>
                    <enumeration value="DUPLICATE_MASTER_LABEL"/>
                    <enumeration value="DUPLICATE_SENDER_DISPLAY_NAME"/>
                    <enumeration value="DUPLICATE_USERNAME"/>
                    <enumeration value="DUPLICATE_VALUE"/>
                    <enumeration value="EMAIL_NOT_PROCESSED_DUE_TO_PRIOR_ERROR"/>
                    <enumeration value="EMPTY_SCONTROL_FILE_NAME"/>
                    <enumeration value="ENTITY_FAILED_IFLASTMODIFIED_ON_UPDATE"/>
                    <enumeration value="ENTITY_IS_ARCHIVED"/>
                    <enumeration value="ENTITY_IS_DELETED"/>
                    <enumeration value="ENTITY_IS_LOCKED"/>
                    <enumeration value="ERROR_IN_MAILER"/>
                    <enumeration value="FAILED_ACTIVATION"/>
                    <enumeration value="FIELD_CUSTOM_VALIDATION_EXCEPTION"/>
                    <enumeration value="FIELD_FILTER_VALIDATION_EXCEPTION"/>
                    <enumeration value="FIELD_INTEGRITY_EXCEPTION"/>
                    <enumeration value="FILTERED_LOOKUP_LIMIT_EXCEEDED"/>
                    <enumeration value="HTML_FILE_UPLOAD_NOT_ALLOWED"/>
                    <enumeration value="IMAGE_TOO_LARGE"/>
                    <enumeration value="INACTIVE_OWNER_OR_USER"/>
                    <enumeration value="INSUFFICIENT_ACCESS_ON_CROSS_REFERENCE_ENTITY"/>
                    <enumeration value="INSUFFICIENT_ACCESS_OR_READONLY"/>
                    <enumeration value="INVALID_ACCESS_LEVEL"/>
                    <enumeration value="INVALID_ARGUMENT_TYPE"/>
                    <enumeration value="INVALID_ASSIGNEE_TYPE"/>
                    <enumeration value="INVALID_ASSIGNMENT_RULE"/>
                    <enumeration value="INVALID_BATCH_OPERATION"/>
                    <enumeration value="INVALID_CONTENT_TYPE"/>
                    <enumeration value="INVALID_CREDIT_CARD_INFO"/>
                    <enumeration value="INVALID_CROSS_REFERENCE_KEY"/>
                    <enumeration value="INVALID_CROSS_REFERENCE_TYPE_FOR_FIELD"/>
                    <enumeration value="INVALID_CURRENCY_CONV_RATE"/>
                    <enumeration value="INVALID_CURRENCY_CORP_RATE"/>
                    <enumeration value="INVALID_CURRENCY_ISO"/>
                    <enumeration value="INVALID_DATA_URI"/>
                    <enumeration value="INVALID_EMAIL_ADDRESS"/>
                    <enumeration value="INVALID_EMPTY_KEY_OWNER"/>
                    <enumeration value="INVALID_FIELD"/>
                    <enumeration value="INVALID_FIELD_FOR_INSERT_UPDATE"/>
                    <enumeration value="INVALID_FIELD_WHEN_USING_TEMPLATE"/>
                    <enumeration value="INVALID_FILTER_ACTION"/>
                    <enumeration value="INVALID_GOOGLE_DOCS_URL"/>
                    <enumeration value="INVALID_ID_FIELD"/>
                    <enumeration value="INVALID_INET_ADDRESS"/>
                    <enumeration value="INVALID_LINEITEM_CLONE_STATE"/>
                    <enumeration value="INVALID_MASTER_OR_TRANSLATED_SOLUTION"/>
                    <enumeration value="INVALID_MESSAGE_ID_REFERENCE"/>
                    <enumeration value="INVALID_OPERATION"/>
                    <enumeration value="INVALID_OPERATOR"/>
                    <enumeration value="INVALID_OR_NULL_FOR_RESTRICTED_PICKLIST"/>
                    <enumeration value="INVALID_PARTNER_NETWORK_STATUS"/>
                    <enumeration value="INVALID_PERSON_ACCOUNT_OPERATION"/>
                    <enumeration value="INVALID_READ_ONLY_USER_DML"/>
                    <enumeration value="INVALID_SAVE_AS_ACTIVITY_FLAG"/>
                    <enumeration value="INVALID_SESSION_ID"/>
                    <enumeration value="INVALID_SETUP_OWNER"/>
                    <enumeration value="INVALID_STATUS"/>
                    <enumeration value="INVALID_TYPE"/>
                    <enumeration value="INVALID_TYPE_FOR_OPERATION"/>
                    <enumeration value="INVALID_TYPE_ON_FIELD_IN_RECORD"/>
                    <enumeration value="IP_RANGE_LIMIT_EXCEEDED"/>
                    <enumeration value="LICENSE_LIMIT_EXCEEDED"/>
                    <enumeration value="LIGHT_PORTAL_USER_EXCEPTION"/>
                    <enumeration value="LIMIT_EXCEEDED"/>
                    <enumeration value="MALFORMED_ID"/>
                    <enumeration value="MANAGER_NOT_DEFINED"/>
                    <enumeration value="MASSMAIL_RETRY_LIMIT_EXCEEDED"/>
                    <enumeration value="MASS_MAIL_LIMIT_EXCEEDED"/>
                    <enumeration value="MAXIMUM_CCEMAILS_EXCEEDED"/>
                    <enumeration value="MAXIMUM_DASHBOARD_COMPONENTS_EXCEEDED"/>
                    <enumeration value="MAXIMUM_HIERARCHY_LEVELS_REACHED"/>
                    <enumeration value="MAXIMUM_SIZE_OF_ATTACHMENT"/>
                    <enumeration value="MAXIMUM_SIZE_OF_DOCUMENT"/>
                    <enumeration value="MAX_ACTIONS_PER_RULE_EXCEEDED"/>
                    <enumeration value="MAX_ACTIVE_RULES_EXCEEDED"/>
                    <enumeration value="MAX_APPROVAL_STEPS_EXCEEDED"/>
                    <enumeration value="MAX_FORMULAS_PER_RULE_EXCEEDED"/>
                    <enumeration value="MAX_RULES_EXCEEDED"/>
                    <enumeration value="MAX_RULE_ENTRIES_EXCEEDED"/>
                    <enumeration value="MAX_TASK_DESCRIPTION_EXCEEEDED"/>
                    <enumeration value="MAX_TM_RULES_EXCEEDED"/>
                    <enumeration value="MAX_TM_RULE_ITEMS_EXCEEDED"/>
                    <enumeration value="MERGE_FAILED"/>
                    <enumeration value="MISSING_ARGUMENT"/>
                    <enumeration value="MIXED_DML_OPERATION"/>
                    <enumeration value="NONUNIQUE_SHIPPING_ADDRESS"/>
                    <enumeration value="NO_APPLICABLE_PROCESS"/>
                    <enumeration value="NO_ATTACHMENT_PERMISSION"/>
                    <enumeration value="NO_INACTIVE_DIVISION_MEMBERS"/>
                    <enumeration value="NO_MASS_MAIL_PERMISSION"/>
                    <enumeration value="NUMBER_OUTSIDE_VALID_RANGE"/>
                    <enumeration value="NUM_HISTORY_FIELDS_BY_SOBJECT_EXCEEDED"/>
                    <enumeration value="OPTED_OUT_OF_MASS_MAIL"/>
                    <enumeration value="OP_WITH_INVALID_USER_TYPE_EXCEPTION"/>
                    <enumeration value="PACKAGE_LICENSE_REQUIRED"/>
                    <enumeration value="PORTAL_USER_ALREADY_EXISTS_FOR_CONTACT"/>
                    <enumeration value="PRIVATE_CONTACT_ON_ASSET"/>
                    <enumeration value="RECORD_IN_USE_BY_WORKFLOW"/>
                    <enumeration value="REQUEST_RUNNING_TOO_LONG"/>
                    <enumeration value="REQUIRED_FEATURE_MISSING"/>
                    <enumeration value="REQUIRED_FIELD_MISSING"/>
                    <enumeration value="SELF_REFERENCE_FROM_TRIGGER"/>
                    <enumeration value="SHARE_NEEDED_FOR_CHILD_OWNER"/>
                    <enumeration value="SINGLE_EMAIL_LIMIT_EXCEEDED"/>
                    <enumeration value="STANDARD_PRICE_NOT_DEFINED"/>
                    <enumeration value="STORAGE_LIMIT_EXCEEDED"/>
                    <enumeration value="STRING_TOO_LONG"/>
                    <enumeration value="TABSET_LIMIT_EXCEEDED"/>
                    <enumeration value="TEMPLATE_NOT_ACTIVE"/>
                    <enumeration value="TERRITORY_REALIGN_IN_PROGRESS"/>
                    <enumeration value="TEXT_DATA_OUTSIDE_SUPPORTED_CHARSET"/>
                    <enumeration value="TOO_MANY_APEX_REQUESTS"/>
                    <enumeration value="TOO_MANY_ENUM_VALUE"/>
                    <enumeration value="TRANSFER_REQUIRES_READ"/>
                    <enumeration value="UNABLE_TO_LOCK_ROW"/>
                    <enumeration value="UNAVAILABLE_RECORDTYPE_EXCEPTION"/>
                    <enumeration value="UNDELETE_FAILED"/>
                    <enumeration value="UNKNOWN_EXCEPTION"/>
                    <enumeration value="UNSPECIFIED_EMAIL_ADDRESS"/>
                    <enumeration value="UNSUPPORTED_APEX_TRIGGER_OPERATON"/>
                    <enumeration value="UNVERIFIED_SENDER_ADDRESS"/>
                    <enumeration value="USER_OWNS_PORTAL_ACCOUNT_EXCEPTION"/>
                    <enumeration value="USER_WITH_APEX_SHARES_EXCEPTION"/>
                    <enumeration value="WEBLINK_SIZE_LIMIT_EXCEEDED"/>
                    <enumeration value="WRONG_CONTROLLER_TYPE"/>
                </restriction>
            </simpleType>


            <complexType name="Error">
                <sequence>
                    <element name="fields" type="xsd:string" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="message" type="xsd:string"/>
                    <element name="statusCode" type="tns:StatusCode"/>
                </sequence>
            </complexType>

            <complexType name="SendEmailError">
                <sequence>
                    <element name="fields" type="xsd:string" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="message" type="xsd:string"/>
                    <element name="statusCode" type="tns:StatusCode"/>
                    <element name="targetObjectId" type="tns:ID" nillable="true"/>
                </sequence>
            </complexType>

            <complexType name="SaveResult">
                <sequence>
                    <element name="errors" type="tns:Error" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="id" type="tns:ID" nillable="true"/>
                    <element name="success" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="UpsertResult">
                <sequence>
                    <element name="created" type="xsd:boolean"/>
                    <element name="errors" type="tns:Error" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="id" type="tns:ID" nillable="true"/>
                    <element name="success" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="MergeRequest">
                <sequence>
                    <element name="masterRecord" type="ens:sObject"/>
                    <element name="recordToMergeIds" type="tns:ID" minOccurs="1" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <complexType name="MergeResult">
                <sequence>
                    <element name="errors" type="tns:Error" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="id" type="tns:ID" nillable="true"/>
                    <element name="mergedRecordIds" type="tns:ID" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="success" type="xsd:boolean"/>
                    <element name="updatedRelatedIds" type="tns:ID" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <complexType name="ProcessRequest">
                <sequence>
                    <element name="comments" type="xsd:string" nillable="true"/>
                    <element name="nextApproverIds" type="tns:ID" minOccurs="0" maxOccurs="unbounded" nillable="true"/>
                </sequence>
            </complexType>

            <complexType name="ProcessSubmitRequest">
                <complexContent>
                    <extension base="tns:ProcessRequest">
                        <sequence>
                            <element name="objectId" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="ProcessWorkitemRequest">
                <complexContent>
                    <extension base="tns:ProcessRequest">
                        <sequence>
                            <element name="action" type="xsd:string"/>
                            <element name="workitemId" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>



            <complexType name="ProcessResult">
                <sequence>
                    <element name="actorIds" type="tns:ID" nillable="false" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="entityId" type="tns:ID" nillable="true"/>
                    <element name="errors" type="tns:Error" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="instanceId" type="tns:ID" nillable="true"/>
                    <element name="instanceStatus" type="xsd:string" nillable="true"/>
                    <element name="newWorkitemIds" type="tns:ID" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="success" type="xsd:boolean"/>
                </sequence>
            </complexType>





            <complexType name="DeleteResult">
                <sequence>
                    <element name="errors" type="tns:Error" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="id" type="tns:ID" nillable="true"/>
                    <element name="success" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="UndeleteResult">
                <sequence>
                    <element name="errors" type="tns:Error" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="id" type="tns:ID" nillable="true"/>
                    <element name="success" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="EmptyRecycleBinResult">
                <sequence>
                    <element name="errors" type="tns:Error" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="id" type="tns:ID" nillable="true"/>
                    <element name="success" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="LeadConvert">
                <sequence>
                    <element name="accountId" type="tns:ID" nillable="true"/>
                    <element name="contactId" type="tns:ID" nillable="true"/>
                    <element name="convertedStatus" type="xsd:string"/>
                    <element name="doNotCreateOpportunity" type="xsd:boolean"/>
                    <element name="leadId" type="tns:ID"/>
                    <element name="opportunityName" type="xsd:string" nillable="true"/>
                    <element name="overwriteLeadSource" type="xsd:boolean"/>
                    <element name="ownerId" type="tns:ID" nillable="true"/>
                    <element name="sendNotificationEmail" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="LeadConvertResult">
                <sequence>
                    <element name="accountId" type="tns:ID" nillable="true"/>
                    <element name="contactId" type="tns:ID" nillable="true"/>
                    <element name="errors" type="tns:Error" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="leadId" type="tns:ID" nillable="true"/>
                    <element name="opportunityId" type="tns:ID" nillable="true"/>
                    <element name="success" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="DescribeSObjectResult">
                <sequence>
                    <element name="activateable" type="xsd:boolean"/>
                    <element name="childRelationships" type="tns:ChildRelationship" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="createable" type="xsd:boolean"/>
                    <element name="custom" type="xsd:boolean"/>
                    <element name="customSetting" type="xsd:boolean"/>
                    <element name="deletable" type="xsd:boolean"/>
                    <element name="deprecatedAndHidden" type="xsd:boolean"/>
                    <element name="feedEnabled" type="xsd:boolean"/>
                    <element name="fields" type="tns:Field" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="keyPrefix" type="xsd:string" nillable="true"/>
                    <element name="label" type="xsd:string"/>
                    <element name="labelPlural" type="xsd:string"/>
                    <element name="layoutable" type="xsd:boolean"/>
                    <element name="mergeable" type="xsd:boolean"/>
                    <element name="name" type="xsd:string"/>
                    <element name="queryable" type="xsd:boolean"/>
                    <element name="recordTypeInfos" type="tns:RecordTypeInfo" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="replicateable" type="xsd:boolean"/>
                    <element name="retrieveable" type="xsd:boolean"/>
                    <element name="searchable" type="xsd:boolean"/>
                    <element name="triggerable" type="xsd:boolean" minOccurs="0"/>
                    <element name="undeletable" type="xsd:boolean"/>
                    <element name="updateable" type="xsd:boolean"/>
                    <element name="urlDetail" type="xsd:string" nillable="true"/>
                    <element name="urlEdit" type="xsd:string" nillable="true"/>
                    <element name="urlNew" type="xsd:string" nillable="true"/>
                </sequence>
            </complexType>

            <!-- this is a subset of properties for each SObject that is returned by the describeGlobal call -->
            <complexType name="DescribeGlobalSObjectResult">
                <sequence>
                    <element name="activateable" type="xsd:boolean"/>
                    <element name="createable" type="xsd:boolean"/>
                    <element name="custom" type="xsd:boolean"/>
                    <element name="customSetting" type="xsd:boolean"/>
                    <element name="deletable" type="xsd:boolean"/>
                    <element name="deprecatedAndHidden" type="xsd:boolean"/>
                    <element name="feedEnabled" type="xsd:boolean"/>
                    <element name="keyPrefix" type="xsd:string" nillable="true"/>
                    <element name="label" type="xsd:string"/>
                    <element name="labelPlural" type="xsd:string"/>
                    <element name="layoutable" type="xsd:boolean"/>
                    <element name="mergeable" type="xsd:boolean"/>
                    <element name="name" type="xsd:string"/>
                    <element name="queryable" type="xsd:boolean"/>
                    <element name="replicateable" type="xsd:boolean"/>
                    <element name="retrieveable" type="xsd:boolean"/>
                    <element name="searchable" type="xsd:boolean"/>
                    <element name="triggerable" type="xsd:boolean"/>
                    <element name="undeletable" type="xsd:boolean"/>
                    <element name="updateable" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="ChildRelationship">
                <sequence>
                    <element name="cascadeDelete" type="xsd:boolean"/>
                    <element name="childSObject" type="xsd:string"/>
                    <element name="deprecatedAndHidden" type="xsd:boolean"/>
                    <element name="field" type="xsd:string"/>
                    <element name="relationshipName" type="xsd:string" minOccurs="0"/>
                </sequence>
            </complexType>

            <complexType name="DescribeGlobalResult">
                <sequence>
                    <element name="encoding" type="xsd:string" nillable="true"/>
                    <element name="maxBatchSize" type="xsd:int"/>
                    <element name="sobjects" type="tns:DescribeGlobalSObjectResult" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <simpleType name="fieldType">
                <restriction base="xsd:string">
                    <enumeration value="string"/>
                    <enumeration value="picklist"/>
                    <enumeration value="multipicklist"/>
                    <enumeration value="combobox"/>
                    <enumeration value="reference"/>
                    <enumeration value="base64"/>
                    <enumeration value="boolean"/>
                    <enumeration value="currency"/>
                    <enumeration value="textarea"/>
                    <enumeration value="int"/>
                    <enumeration value="double"/>
                    <enumeration value="percent"/>
                    <enumeration value="phone"/>
                    <enumeration value="id"/>
                    <enumeration value="date"/>
                    <enumeration value="datetime"/>
                    <enumeration value="time"/>
                    <enumeration value="url"/>
                    <enumeration value="email"/>
                    <enumeration value="encryptedstring"/>
                    <enumeration value="datacategorygroupreference"/>
                    <enumeration value="anyType"/> <!-- can be string, picklist, reference, boolean, currency, int, double, percent, id, date, datetime, url, email -->
                </restriction>
            </simpleType>

            <simpleType name="soapType">
                <restriction base="xsd:string">
                    <enumeration value="tns:ID"/>
                    <enumeration value="xsd:base64Binary"/>
                    <enumeration value="xsd:boolean"/>
                    <enumeration value="xsd:double"/>
                    <enumeration value="xsd:int"/>
                    <enumeration value="xsd:string"/>
                    <enumeration value="xsd:date"/>
                    <enumeration value="xsd:dateTime"/>
                    <enumeration value="xsd:time"/>
                    <enumeration value="xsd:anyType"/> <!-- can be id, booolean, double, int, string, date, dateTime -->
                </restriction>
            </simpleType>

            <complexType name="Field">
                <sequence>
                    <element name="autoNumber" type="xsd:boolean"/>
                    <element name="byteLength" type="xsd:int"/>
                    <element name="calculated" type="xsd:boolean"/>
                    <element name="calculatedFormula" type="xsd:string" minOccurs="0"/>
                    <element name="caseSensitive" type="xsd:boolean"/>
                    <element name="controllerName" type="xsd:string" minOccurs="0"/>
                    <element name="createable" type="xsd:boolean"/>
                    <element name="custom" type="xsd:boolean"/>
                    <element name="defaultValueFormula" type="xsd:string" minOccurs="0"/>
                    <element name="defaultedOnCreate" type="xsd:boolean"/>
                    <element name="dependentPicklist" type="xsd:boolean" minOccurs="0"/>
                    <element name="deprecatedAndHidden" type="xsd:boolean"/>
                    <element name="digits" type="xsd:int"/>
                    <element name="externalId" type="xsd:boolean" minOccurs="0"/>
                    <element name="filterable" type="xsd:boolean"/>
                    <element name="groupable" type="xsd:boolean"/>
                    <element name="htmlFormatted" type="xsd:boolean" minOccurs="0"/>
                    <element name="idLookup" type="xsd:boolean"/>
                    <element name="inlineHelpText" type="xsd:string" minOccurs="0"/>
                    <element name="label" type="xsd:string"/>
                    <element name="length" type="xsd:int"/>
                    <element name="name" type="xsd:string"/>
                    <element name="nameField" type="xsd:boolean"/>
                    <element name="namePointing" type="xsd:boolean" minOccurs="0"/>
                    <element name="nillable" type="xsd:boolean"/>
                    <element name="picklistValues" type="tns:PicklistEntry" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="precision" type="xsd:int"/>
                    <element name="referenceTo" type="xsd:string" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="relationshipName" type="xsd:string" minOccurs="0"/>
                    <element name="relationshipOrder" type="xsd:int" minOccurs="0"/>
                    <element name="restrictedPicklist" type="xsd:boolean"/>
                    <element name="scale" type="xsd:int"/>
                    <element name="soapType" type="tns:soapType"/>
                    <element name="sortable" type="xsd:boolean" minOccurs="0"/>
                    <element name="type" type="tns:fieldType"/>
                    <element name="unique" type="xsd:boolean"/>
                    <element name="updateable" type="xsd:boolean"/>
                    <element name="writeRequiresMasterRead" type="xsd:boolean" minOccurs="0"/>
                </sequence>
            </complexType>

            <complexType name="PicklistEntry">
                <sequence>
                    <element name="active" type="xsd:boolean"/>
                    <element name="defaultValue" type="xsd:boolean"/>
                    <element name="label" type="xsd:string" nillable="true"/>
                    <element name="validFor" type="xsd:base64Binary" minOccurs="0"/>
                    <element name="value" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="DescribeDataCategoryGroupResult">
                <sequence>
                    <element name="categoryCount" type="xsd:int"/>
                    <element name="description" type="xsd:string"/>
                    <element name="label" type="xsd:string"/>
                    <element name="name" type="xsd:string"/>
                    <element name="sobject" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="DescribeDataCategoryGroupStructureResult">
                <sequence>
                    <element name="description" type="xsd:string"/>
                    <element name="label" type="xsd:string"/>
                    <element name="name" type="xsd:string"/>
                    <element name="sobject" type="xsd:string"/>
                    <element name="topCategories" type="tns:DataCategory" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <complexType name="DataCategoryGroupSobjectTypePair">
                <sequence>
                    <element name="dataCategoryGroupName" type="xsd:string"/>
                    <element name="sobject" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="DataCategory">
                <sequence>
                    <element name="childCategories" type="tns:DataCategory" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="label" type="xsd:string"/>
                    <element name="name" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="DescribeSoftphoneLayoutResult">
                <sequence>
                    <element name="callTypes" type="tns:DescribeSoftphoneLayoutCallType" maxOccurs="unbounded"/>
                    <element name="id" type="tns:ID"/>
                    <element name="name" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="DescribeSoftphoneLayoutCallType">
                <sequence>
                    <element name="infoFields" type="tns:DescribeSoftphoneLayoutInfoField" maxOccurs="unbounded"/>
                    <element name="name" type="xsd:string"/>
                    <element name="screenPopOptions" type="tns:DescribeSoftphoneScreenPopOption" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="screenPopsOpenWithin" type="xsd:string" minOccurs="0"/>
                    <element name="sections" type="tns:DescribeSoftphoneLayoutSection" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <complexType name="DescribeSoftphoneScreenPopOption">
                <sequence>
                    <element name="matchType" type="xsd:string"/>
                    <element name="screenPopData" type="xsd:string"/>
                    <element name="screenPopType" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="DescribeSoftphoneLayoutInfoField">
                <sequence>
                    <element name="name" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="DescribeSoftphoneLayoutSection">
                <sequence>
                    <element name="entityApiName" type="xsd:string"/>
                    <element name="items" type="tns:DescribeSoftphoneLayoutItem" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <complexType name="DescribeSoftphoneLayoutItem">
                <sequence>
                    <element name="itemApiName" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="DescribeLayoutResult">
                <sequence>
                    <element name="layouts" type="tns:DescribeLayout" maxOccurs="unbounded"/>
                    <element name="recordTypeMappings" type="tns:RecordTypeMapping" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="recordTypeSelectorRequired" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="DescribeLayout">
                <sequence>
                    <element name="buttonLayoutSection" type="tns:DescribeLayoutButtonSection" minOccurs="0"/>
                    <element name="detailLayoutSections" type="tns:DescribeLayoutSection" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="editLayoutSections" type="tns:DescribeLayoutSection" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="id" type="tns:ID"/>


                    <element name="relatedLists" type="tns:RelatedList" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <complexType name="DescribeLayoutSection">
                <sequence>
                    <element name="columns" type="xsd:int"/>
                    <element name="heading" type="xsd:string"/>
                    <element name="layoutRows" type="tns:DescribeLayoutRow" maxOccurs="unbounded"/>
                    <element name="rows" type="xsd:int"/>
                    <element name="useCollapsibleSection" type="xsd:boolean"/>
                    <element name="useHeading" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="DescribeLayoutButtonSection">
                <sequence>
                    <element name="detailButtons" type="tns:DescribeLayoutButton" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <complexType name="DescribeLayoutRow">
                <sequence>
                    <element name="layoutItems" type="tns:DescribeLayoutItem" maxOccurs="unbounded"/>
                    <element name="numItems" type="xsd:int"/>
                </sequence>
            </complexType>

            <complexType name="DescribeLayoutItem">
                <sequence>
                    <element name="editable" type="xsd:boolean"/>
                    <element name="label" type="xsd:string" nillable="true"/>
                    <element name="layoutComponents" type="tns:DescribeLayoutComponent" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="placeholder" type="xsd:boolean"/>
                    <element name="required" type="xsd:boolean"/>
                </sequence>
            </complexType>

            <complexType name="DescribeLayoutButton">
                <sequence>
                    <element name="custom" type="xsd:boolean"/>
                    <element name="label" type="xsd:string" nillable="true"/>
                    <element name="name" type="xsd:string" nillable="true"/>
                </sequence>
            </complexType>

            <complexType name="DescribeLayoutComponent">
                <sequence>
                    <element name="displayLines" type="xsd:int"/>
                    <element name="tabOrder" type="xsd:int"/>
                    <element name="type" type="tns:layoutComponentType"/>
                    <element name="value" type="xsd:string"/>
                </sequence>
            </complexType>

            <simpleType name="layoutComponentType">
                <restriction base="xsd:string">
                    <enumeration value="Field"/>
                    <enumeration value="Separator"/>
                    <enumeration value="SControl"/>
                    <enumeration value="EmptySpace"/>
                </restriction>
            </simpleType>

            <complexType name="RecordTypeInfo">
                <sequence>
                    <element name="available" type="xsd:boolean"/>
                    <element name="defaultRecordTypeMapping" type="xsd:boolean"/>
                    <element name="name" type="xsd:string"/>
                    <element name="recordTypeId" type="tns:ID" nillable="true"/>
                </sequence>
            </complexType>

            <complexType name="RecordTypeMapping">
                <sequence>
                    <element name="available" type="xsd:boolean"/>
                    <element name="defaultRecordTypeMapping" type="xsd:boolean"/>
                    <element name="layoutId" type="tns:ID"/>
                    <element name="name" type="xsd:string"/>
                    <element name="picklistsForRecordType" type="tns:PicklistForRecordType" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    <element name="recordTypeId" type="tns:ID" nillable="true"/>
                </sequence>
            </complexType>

            <complexType name="PicklistForRecordType">
                <sequence>
                    <element name="picklistName" type="xsd:string"/>
                    <element name="picklistValues" type="tns:PicklistEntry" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <complexType name="RelatedList">
                <sequence>
                    <element name="columns" type="tns:RelatedListColumn" maxOccurs="unbounded"/>
                    <element name="custom" type="xsd:boolean"/>
                    <element name="field" type="xsd:string" nillable="true"/>
                    <element name="label" type="xsd:string"/>
                    <element name="limitRows" type="xsd:int"/>
                    <element name="name" type="xsd:string"/>
                    <element name="sobject" type="xsd:string" nillable="true"/>
                    <element name="sort" type="tns:RelatedListSort" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <complexType name="RelatedListColumn">
                <sequence>
                    <element name="field" type="xsd:string" nillable="true"/>
                    <element name="format" type="xsd:string" nillable="true"/>
                    <element name="label" type="xsd:string"/>
                    <element name="name" type="xsd:string"/>
                </sequence>
            </complexType>

            <complexType name="RelatedListSort">
                <sequence>
                    <element name="ascending" type="xsd:boolean"/>
                    <element name="column" type="xsd:string"/>
                </sequence>
            </complexType>



            <complexType name="EmailFileAttachment">
                <sequence>
                    <element name="body" nillable="true" minOccurs="0" type="xsd:base64Binary"/>
                    <element name="contentType" nillable="true" minOccurs="0" type="xsd:string"/>
                    <element name="fileName" type="xsd:string"/>
                    <element name="inline" type="xsd:boolean" minOccurs="0"/>
                </sequence>
            </complexType>
            <simpleType name="EmailPriority">
                <restriction base="xsd:string">
                    <enumeration value="Highest"/>
                    <enumeration value="High"/>
                    <enumeration value="Normal"/>
                    <enumeration value="Low"/>
                    <enumeration value="Lowest"/>
                </restriction>
            </simpleType>

            <complexType name="Email">
                <sequence>
                    <element name="bccSender" type="xsd:boolean" nillable="true"/>
                    <element name="emailPriority" type="tns:EmailPriority" nillable="true"/>
                    <element name="replyTo" type="xsd:string" nillable="true"/>
                    <element name="saveAsActivity" type="xsd:boolean" nillable="true"/>
                    <element name="senderDisplayName" type="xsd:string" nillable="true"/>
                    <element name="subject" type="xsd:string" nillable="true"/>
                    <element name="useSignature" type="xsd:boolean" nillable="true"/>
                </sequence>
            </complexType>

            <complexType name="MassEmailMessage">
                <complexContent>
                    <extension base="tns:Email">
                        <sequence>
                            <element name="description" type="xsd:string" nillable="true"/>
                            <element name="targetObjectIds" minOccurs="0" maxOccurs="250" type="tns:ID"/>
                            <element name="templateId" type="tns:ID"/>
                            <element name="whatIds" minOccurs="0" maxOccurs="250" type="tns:ID"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="SingleEmailMessage">
                <complexContent>
                    <extension base="tns:Email">
                        <sequence>
                            <element name="bccAddresses" minOccurs="0" maxOccurs="25" type="xsd:string" nillable="true"/>
                            <element name="ccAddresses" minOccurs="0" maxOccurs="25" type="xsd:string" nillable="true"/>
                            <element name="charset" type="xsd:string" nillable="true"/>
                            <element name="documentAttachments" minOccurs="0" maxOccurs="unbounded" type="tns:ID"/>
                            <element name="htmlBody" type="xsd:string" nillable="true"/>
                            <element name="inReplyTo" minOccurs="0" type="xsd:string" nillable="true"/>
                            <element name="fileAttachments" minOccurs="0" maxOccurs="unbounded" type="tns:EmailFileAttachment"/>
                            <element name="orgWideEmailAddressId" minOccurs="0" maxOccurs="1" type="tns:ID" nillable="true"/>
                            <element name="plainTextBody" type="xsd:string" nillable="true"/>
                            <element name="references" minOccurs="0" type="xsd:string" nillable="true"/>
                            <element name="targetObjectId" type="tns:ID" nillable="true"/>
                            <element name="templateId" type="tns:ID" nillable="true"/>
                            <element name="toAddresses" minOccurs="0" maxOccurs="100" type="xsd:string" nillable="true"/>
                            <element name="whatId" type="tns:ID" nillable="true"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="SendEmailResult">
                <sequence>
                    <element name="errors" minOccurs="0" maxOccurs="unbounded" type="tns:SendEmailError"/>
                    <element name="success" type="xsd:boolean"/>
                </sequence>
            </complexType>



            <complexType name="DescribeTabSetResult">
                <sequence>
                    <element name="label" type="xsd:string"/>
                    <element name="logoUrl" type="xsd:string"/>
                    <element name="namespace" type="xsd:string" minOccurs="0"/>
                    <element name="selected" type="xsd:boolean"/>
                    <element name="tabs" type="tns:DescribeTab" minOccurs="0" maxOccurs="unbounded"/>
                </sequence>
            </complexType>

            <complexType name="DescribeTab">
                <sequence>
                    <element name="custom" type="xsd:boolean"/>
                    <element name="iconUrl" type="xsd:string"/>
                    <element name="label" type="xsd:string"/>
                    <element name="miniIconUrl" type="xsd:string"/>
                    <element name="sobjectName" type="xsd:string" nillable="true"/>
                    <element name="url" type="xsd:string"/>
                </sequence>
            </complexType>





            <!-- Login Message Types -->
            <element name="login">
                <complexType>
                    <sequence>
                        <element name="username" type="xsd:string"/>
                        <element name="password" type="xsd:string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="loginResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:LoginResult"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Describe Message Types -->
            <element name="describeSObject">
                <complexType>
                    <sequence>
                        <element name="sObjectType" type="xsd:string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="describeSObjectResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:DescribeSObjectResult" nillable="true"/>
                    </sequence>
                </complexType>
            </element>

            <!-- DescibeSObjects Message Types -->
            <element name="describeSObjects">
                <complexType>
                    <sequence>
                        <element name="sObjectType" type="xsd:string" minOccurs="0" maxOccurs="100"/>
                    </sequence>
                </complexType>
            </element>
            <element name="describeSObjectsResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:DescribeSObjectResult" nillable="true" minOccurs="0" maxOccurs="100"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Describe Global Message Types -->
            <element name="describeGlobal">
                <complexType>
                    <sequence/>
                </complexType>
            </element>
            <element name="describeGlobalResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:DescribeGlobalResult"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Describe Data Category Groups Message Types -->
            <element name="describeDataCategoryGroups">
                <complexType>
                    <sequence>
                        <element name="sObjectType" type="xsd:string" minOccurs="0" maxOccurs="10"/>
                    </sequence>
                </complexType>
            </element>
            <element name="describeDataCategoryGroupsResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:DescribeDataCategoryGroupResult" minOccurs="0" maxOccurs="100"/>
                    </sequence>
                </complexType>
            </element>
            <element name="describeDataCategoryGroupStructures">
                <complexType>
                    <sequence>
                        <element name="pairs" type="tns:DataCategoryGroupSobjectTypePair" minOccurs="0" maxOccurs="100"/>
                        <element name="topCategoriesOnly" type="xsd:boolean"/>
                    </sequence>
                </complexType>
            </element>
            <element name="describeDataCategoryGroupStructuresResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:DescribeDataCategoryGroupStructureResult" minOccurs="0" maxOccurs="100"/>
                    </sequence>
                </complexType>
            </element>
            <element name="describeLayout">
                <complexType>
                    <sequence>
                        <element name="sObjectType" type="xsd:string"/>
                        <element name="recordTypeIds" type="tns:ID" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="describeLayoutResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:DescribeLayoutResult" nillable="true"/>
                    </sequence>
                </complexType>
            </element>

            <element name="describeSoftphoneLayout">
                <complexType>
                    <sequence/>
                </complexType>
            </element>
            <element name="describeSoftphoneLayoutResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:DescribeSoftphoneLayoutResult" nillable="true"/>
                    </sequence>
                </complexType>
            </element>


            <element name="describeTabs">
                <complexType>
                    <sequence/>
                </complexType>
            </element>
            <element name="describeTabsResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:DescribeTabSetResult" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Create Message Types -->
            <element name="create">
                <complexType>
                    <sequence>
                        <element name="sObjects" type="ens:sObject" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="createResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:SaveResult" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>



            <!-- Send Email Types -->
            <element name="sendEmail">
                <complexType>
                    <sequence>
                        <element name="messages" type="tns:Email" minOccurs="0" maxOccurs="10"/>
                    </sequence>
                </complexType>
            </element>
            <element name="sendEmailResponse">
                <complexType>
                    <sequence>
                        <element name="result" minOccurs="0" maxOccurs="10" type="tns:SendEmailResult"/>
                    </sequence>
                </complexType>
            </element>


            <!-- Update Message Types -->
            <element name="update">
                <complexType>
                    <sequence>
                        <element name="sObjects" type="ens:sObject" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="updateResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:SaveResult" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Upsert Message Types -->
            <element name="upsert">
                <complexType>
                    <sequence>
                        <element name="externalIDFieldName" type="xsd:string"/>
                        <element name="sObjects" type="ens:sObject" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="upsertResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:UpsertResult" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Merge Message Types -->
            <element name="merge">
                <complexType>
                    <sequence>
                        <element name="request" type="tns:MergeRequest" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="mergeResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:MergeResult" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Delete Message Types -->
            <element name="delete">
                <complexType>
                    <sequence>
                        <element name="ids" type="tns:ID" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="deleteResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:DeleteResult" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Undelete Message Types -->
            <element name="undelete">
                <complexType>
                    <sequence>
                        <element name="ids" type="tns:ID" minOccurs="1" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="undeleteResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:UndeleteResult" minOccurs="1" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <!-- EmptyRecycleBin Message Types -->
            <element name="emptyRecycleBin">
                <complexType>
                    <sequence>
                        <element name="ids" type="tns:ID" minOccurs="1" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="emptyRecycleBinResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:EmptyRecycleBinResult" minOccurs="1" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Process Message Types -->
            <element name="process">
                <complexType>
                    <sequence>
                        <element name="actions" type="tns:ProcessRequest" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="processResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:ProcessResult" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>



            <!-- Retrieve (ID List) Message Types -->
            <element name="retrieve">
                <complexType>
                    <sequence>
                        <element name="fieldList" type="xsd:string"/>
                        <element name="sObjectType" type="xsd:string"/>
                        <element name="ids" type="tns:ID" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="retrieveResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="ens:sObject" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Convert Lead Message Types -->
            <element name="convertLead">
                <complexType>
                    <sequence>
                        <element name="leadConverts" type="tns:LeadConvert" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>
            <element name="convertLeadResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:LeadConvertResult" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Get Updated Message Types -->
            <element name="getUpdated">
                <complexType>
                    <sequence>
                        <element name="sObjectType" type="xsd:string"/>
                        <element name="startDate" type="xsd:dateTime"/>
                        <element name="endDate" type="xsd:dateTime"/>
                    </sequence>
                </complexType>
            </element>
            <element name="getUpdatedResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:GetUpdatedResult"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Get Deleted Message Types -->
            <element name="getDeleted">
                <complexType>
                    <sequence>
                        <element name="sObjectType" type="xsd:string"/>
                        <element name="startDate" type="xsd:dateTime"/>
                        <element name="endDate" type="xsd:dateTime"/>
                    </sequence>
                </complexType>
            </element>
            <element name="getDeletedResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:GetDeletedResult"/>
                    </sequence>
                </complexType>
            </element>


            <!-- Logout current session -->
            <element name="logout">
                <complexType>
                </complexType>
            </element>

            <element name="logoutResponse">
                <complexType>
                </complexType>
            </element>

            <!-- Invalidate a list of session ids -->
            <element name="invalidateSessions">
                <complexType>
                    <sequence>
                        <element name="sessionIds" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <element name="invalidateSessionsResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:InvalidateSessionsResult" minOccurs="0" maxOccurs="unbounded"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Create Query -->
            <element name="query">
                <complexType>
                    <sequence>
                        <element name="queryString" type="xsd:string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="queryResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:QueryResult"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Create Query All -->
            <element name="queryAll">
                <complexType>
                    <sequence>
                        <element name="queryString" type="xsd:string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="queryAllResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:QueryResult"/>
                    </sequence>
                </complexType>
            </element>

            <!-- Next Batch of sObjects from a query -->
            <element name="queryMore">
                <complexType>
                    <sequence>
                        <element name="queryLocator" type="tns:QueryLocator"/>
                    </sequence>
                </complexType>
            </element>
            <element name="queryMoreResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:QueryResult"/>
                    </sequence>
                </complexType>
            </element>



            <!-- Create Search -->
            <element name="search">
                <complexType>
                    <sequence>
                        <element name="searchString" type="xsd:string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="searchResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:SearchResult"/>
                    </sequence>
                </complexType>
            </element>



            <element name="getServerTimestamp">
                <complexType>
                    <sequence/>
                </complexType>
            </element>
            <element name="getServerTimestampResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:GetServerTimestampResult"/>
                    </sequence>
                </complexType>
            </element>

            <element name="setPassword">
                <complexType>
                    <sequence>
                        <element name="userId" type="tns:ID"/>
                        <element name="password" type="xsd:string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="setPasswordResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:SetPasswordResult"/>
                    </sequence>
                </complexType>
            </element>

            <element name="resetPassword">
                <complexType>
                    <sequence>
                        <element name="userId" type="tns:ID"/>
                    </sequence>
                </complexType>
            </element>
            <element name="resetPasswordResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:ResetPasswordResult"/>
                    </sequence>
                </complexType>
            </element>

            <element name="getUserInfo">
                <complexType>
                    <sequence/>
                </complexType>
            </element>
            <element name="getUserInfoResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="tns:GetUserInfoResult"/>
                    </sequence>
                </complexType>
            </element>



            <!-- Header Elements -->
            <element name="SessionHeader">
                <complexType>
                    <sequence>
                        <element name="sessionId" type="xsd:string"/>
                    </sequence>
                </complexType>
            </element>

            <element name="LoginScopeHeader">
                <complexType>
                    <sequence>
                        <element name="organizationId" type="tns:ID"/>
                        <element name="portalId" type="tns:ID" minOccurs="0"/>
                    </sequence>
                </complexType>
            </element>



            <element name="QueryOptions">
                <complexType>
                    <sequence>
                        <element name="batchSize" type="xsd:int" minOccurs="0"/>

                    </sequence>
                </complexType>
            </element>


            <simpleType name="DebugLevel">
                <restriction base="xsd:string">
                    <enumeration value="None"/>
                    <enumeration value="DebugOnly"/>
                    <enumeration value="Db"/>
                </restriction>
            </simpleType>
            <element name="DebuggingHeader">
                <complexType>
                    <sequence>
                        <element name="debugLevel" type="tns:DebugLevel"/>
                    </sequence>
                </complexType>
            </element>
            <element name="DebuggingInfo">
                <complexType>
                    <sequence>
                        <element name="debugLog" type="xsd:string"/>
                    </sequence>
                </complexType>
            </element>

   <xsd:complexType name="PackageVersion">
    <xsd:sequence>
     <xsd:element name="majorNumber" type="xsd:int"/>
     <xsd:element name="minorNumber" type="xsd:int"/>
     <xsd:element name="namespace" type="xsd:string"/>
    </xsd:sequence>
   </xsd:complexType>
   <xsd:element name="PackageVersionHeader">
    <xsd:complexType>
     <xsd:sequence>
      <xsd:element name="packageVersions" minOccurs="0" maxOccurs="unbounded" type="tns:PackageVersion"/>
     </xsd:sequence>
    </xsd:complexType>
   </xsd:element>

            <element name="AllowFieldTruncationHeader">
                <complexType>
                    <sequence>
                        <element name="allowFieldTruncation" type="xsd:boolean"/>
                    </sequence>
                </complexType>
            </element>


            <element name="DisableFeedTrackingHeader">
                <complexType>
                    <sequence>
                        <element name="disableFeedTracking" type="xsd:boolean"/>
                    </sequence>
                </complexType>
            </element>



        <element name="AllOrNoneHeader">
            <complexType>
                <sequence>
                    <element name="allOrNone" type="xsd:boolean"/>
                </sequence>
            </complexType>
        </element>


            <!-- ideally this could of just been elem name="..." type="xsd:boolean"
                 but is required to be nested within a complexType for .NET 1.1 compatibility -->
            <element name="MruHeader">
                <complexType>
                    <sequence>
                        <element name="updateMru" type="xsd:boolean"/>
                    </sequence>
                </complexType>
            </element>

            <element name="EmailHeader">
                <complexType>
                    <sequence>
                        <element name="triggerAutoResponseEmail" type="xsd:boolean"/>
                        <element name="triggerOtherEmail" type="xsd:boolean"/>
                        <element name="triggerUserEmail" type="xsd:boolean"/>
                    </sequence>
                </complexType>
            </element>

            <element name="AssignmentRuleHeader">
                <complexType>
                    <sequence>
                        <element name="assignmentRuleId" type="tns:ID" nillable="true"/>
                        <element name="useDefaultRule" type="xsd:boolean" nillable="true"/>
                    </sequence>
                </complexType>
            </element>

            <element name="UserTerritoryDeleteHeader">
                <complexType>
                    <sequence>
                        <element name="transferToUserId" type="tns:ID" nillable="true"/>
                    </sequence>
                </complexType>
            </element>



            <element name="LocaleOptions">
                <complexType>
                    <sequence>
                        <element name="language" type="xsd:string" minOccurs="0"/>
                    </sequence>
                </complexType>
            </element>
        </schema>

        <schema elementFormDefault="qualified" xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:fault.enterprise.soap.sforce.com" xmlns:fns="urn:fault.enterprise.soap.sforce.com">

            <simpleType name="ExceptionCode">
                <restriction base="xsd:string">
                    <enumeration value="API_CURRENTLY_DISABLED"/>
                    <enumeration value="API_DISABLED_FOR_ORG"/>
                    <enumeration value="CANT_ADD_STANDADRD_PORTAL_USER_TO_TERRITORY"/>
                    <enumeration value="CANT_ADD_STANDARD_PORTAL_USER_TO_TERRITORY"/>
                    <enumeration value="CIRCULAR_OBJECT_GRAPH"/>
                    <enumeration value="CLIENT_NOT_ACCESSIBLE_FOR_USER"/>
                    <enumeration value="CLIENT_REQUIRE_UPDATE_FOR_USER"/>
                    <enumeration value="CUSTOM_METADATA_LIMIT_EXCEEDED"/>
                    <enumeration value="DUPLICATE_VALUE"/>
                    <enumeration value="EMAIL_BATCH_SIZE_LIMIT_EXCEEDED"/>
                    <enumeration value="EMAIL_TO_CASE_INVALID_ROUTING"/>
                    <enumeration value="EMAIL_TO_CASE_LIMIT_EXCEEDED"/>
                    <enumeration value="EMAIL_TO_CASE_NOT_ENABLED"/>
                    <enumeration value="EXCEEDED_ID_LIMIT"/>
                    <enumeration value="EXCEEDED_LEAD_CONVERT_LIMIT"/>
                    <enumeration value="EXCEEDED_MAX_SIZE_REQUEST"/>
                    <enumeration value="EXCEEDED_MAX_TYPES_LIMIT"/>
                    <enumeration value="EXCEEDED_QUOTA"/>
                    <enumeration value="FUNCTIONALITY_NOT_ENABLED"/>
                    <enumeration value="FUNCTIONALITY_TEMPORARILY_UNAVAILABLE"/>
                    <enumeration value="INACTIVE_OWNER_OR_USER"/>
                    <enumeration value="INACTIVE_PORTAL"/>
                    <enumeration value="INSUFFICIENT_ACCESS"/>
                    <enumeration value="INVALID_ASSIGNMENT_RULE"/>
                    <enumeration value="INVALID_BATCH_SIZE"/>
                    <enumeration value="INVALID_CLIENT"/>
                    <enumeration value="INVALID_CROSS_REFERENCE_KEY"/>
                    <enumeration value="INVALID_FIELD"/>
                    <enumeration value="INVALID_FILTER_LANGUAGE"/>
                    <enumeration value="INVALID_FILTER_VALUE"/>
                    <enumeration value="INVALID_ID_FIELD"/>
                    <enumeration value="INVALID_LOCALE_LANGUAGE"/>
                    <enumeration value="INVALID_LOCATOR"/>
                    <enumeration value="INVALID_LOGIN"/>
                    <enumeration value="INVALID_NEW_PASSWORD"/>
                    <enumeration value="INVALID_OPERATION"/>
                    <enumeration value="INVALID_OPERATION_WITH_EXPIRED_PASSWORD"/>
                    <enumeration value="INVALID_QUERY_FILTER_OPERATOR"/>
                    <enumeration value="INVALID_QUERY_LOCATOR"/>
                    <enumeration value="INVALID_QUERY_SCOPE"/>
                    <enumeration value="INVALID_REPLICATION_DATE"/>
                    <enumeration value="INVALID_SEARCH"/>
                    <enumeration value="INVALID_SEARCH_SCOPE"/>
                    <enumeration value="INVALID_SESSION_ID"/>
                    <enumeration value="INVALID_SOAP_HEADER"/>
                    <enumeration value="INVALID_SSO_GATEWAY_URL"/>
                    <enumeration value="INVALID_TYPE"/>
                    <enumeration value="INVALID_TYPE_FOR_OPERATION"/>
                    <enumeration value="JSON_PARSER_ERROR"/>
                    <enumeration value="LIMIT_EXCEEDED"/>
                    <enumeration value="LOGIN_CHALLENGE_ISSUED"/>
                    <enumeration value="LOGIN_CHALLENGE_PENDING"/>
                    <enumeration value="LOGIN_DURING_RESTRICTED_DOMAIN"/>
                    <enumeration value="LOGIN_DURING_RESTRICTED_TIME"/>
                    <enumeration value="LOGIN_MUST_USE_SECURITY_TOKEN"/>
                    <enumeration value="MALFORMED_ID"/>
                    <enumeration value="MALFORMED_QUERY"/>
                    <enumeration value="MALFORMED_SEARCH"/>
                    <enumeration value="MISSING_ARGUMENT"/>
                    <enumeration value="NOT_MODIFIED"/>
                    <enumeration value="NO_SOFTPHONE_LAYOUT"/>
                    <enumeration value="NUMBER_OUTSIDE_VALID_RANGE"/>
                    <enumeration value="OPERATION_TOO_LARGE"/>
                    <enumeration value="ORG_IN_MAINTENANCE"/>
                    <enumeration value="ORG_IS_DOT_ORG"/>
                    <enumeration value="ORG_LOCKED"/>
                    <enumeration value="ORG_NOT_OWNED_BY_INSTANCE"/>
                    <enumeration value="PASSWORD_LOCKOUT"/>
                    <enumeration value="PORTAL_NO_ACCESS"/>
                    <enumeration value="QUERY_TIMEOUT"/>
                    <enumeration value="QUERY_TOO_COMPLICATED"/>
                    <enumeration value="REQUEST_LIMIT_EXCEEDED"/>
                    <enumeration value="REQUEST_RUNNING_TOO_LONG"/>
                    <enumeration value="SERVER_UNAVAILABLE"/>
                    <enumeration value="SSO_SERVICE_DOWN"/>
                    <enumeration value="TOO_MANY_APEX_REQUESTS"/>
                    <enumeration value="TRIAL_EXPIRED"/>
                    <enumeration value="UNKNOWN_EXCEPTION"/>
                    <enumeration value="UNSUPPORTED_API_VERSION"/>
                    <enumeration value="UNSUPPORTED_CLIENT"/>
                    <enumeration value="UNSUPPORTED_MEDIA_TYPE"/>
                    <enumeration value="XML_PARSER_ERROR"/>
                </restriction>
            </simpleType>
            <!-- For convenience these QNames are returned in the standard soap faultcode element -->
            <simpleType name="FaultCode">
                <restriction base="xsd:QName">
                    <enumeration value="fns:API_CURRENTLY_DISABLED"/>
                    <enumeration value="fns:API_DISABLED_FOR_ORG"/>
                    <enumeration value="fns:CANT_ADD_STANDADRD_PORTAL_USER_TO_TERRITORY"/>
                    <enumeration value="fns:CANT_ADD_STANDARD_PORTAL_USER_TO_TERRITORY"/>
                    <enumeration value="fns:CIRCULAR_OBJECT_GRAPH"/>
                    <enumeration value="fns:CLIENT_NOT_ACCESSIBLE_FOR_USER"/>
                    <enumeration value="fns:CLIENT_REQUIRE_UPDATE_FOR_USER"/>
                    <enumeration value="fns:CUSTOM_METADATA_LIMIT_EXCEEDED"/>
                    <enumeration value="fns:DUPLICATE_VALUE"/>
                    <enumeration value="fns:EMAIL_BATCH_SIZE_LIMIT_EXCEEDED"/>
                    <enumeration value="fns:EMAIL_TO_CASE_INVALID_ROUTING"/>
                    <enumeration value="fns:EMAIL_TO_CASE_LIMIT_EXCEEDED"/>
                    <enumeration value="fns:EMAIL_TO_CASE_NOT_ENABLED"/>
                    <enumeration value="fns:EXCEEDED_ID_LIMIT"/>
                    <enumeration value="fns:EXCEEDED_LEAD_CONVERT_LIMIT"/>
                    <enumeration value="fns:EXCEEDED_MAX_SIZE_REQUEST"/>
                    <enumeration value="fns:EXCEEDED_MAX_TYPES_LIMIT"/>
                    <enumeration value="fns:EXCEEDED_QUOTA"/>
                    <enumeration value="fns:FUNCTIONALITY_NOT_ENABLED"/>
                    <enumeration value="fns:FUNCTIONALITY_TEMPORARILY_UNAVAILABLE"/>
                    <enumeration value="fns:INACTIVE_OWNER_OR_USER"/>
                    <enumeration value="fns:INACTIVE_PORTAL"/>
                    <enumeration value="fns:INSUFFICIENT_ACCESS"/>
                    <enumeration value="fns:INVALID_ASSIGNMENT_RULE"/>
                    <enumeration value="fns:INVALID_BATCH_SIZE"/>
                    <enumeration value="fns:INVALID_CLIENT"/>
                    <enumeration value="fns:INVALID_CROSS_REFERENCE_KEY"/>
                    <enumeration value="fns:INVALID_FIELD"/>
                    <enumeration value="fns:INVALID_FILTER_LANGUAGE"/>
                    <enumeration value="fns:INVALID_FILTER_VALUE"/>
                    <enumeration value="fns:INVALID_ID_FIELD"/>
                    <enumeration value="fns:INVALID_LOCALE_LANGUAGE"/>
                    <enumeration value="fns:INVALID_LOCATOR"/>
                    <enumeration value="fns:INVALID_LOGIN"/>
                    <enumeration value="fns:INVALID_NEW_PASSWORD"/>
                    <enumeration value="fns:INVALID_OPERATION"/>
                    <enumeration value="fns:INVALID_OPERATION_WITH_EXPIRED_PASSWORD"/>
                    <enumeration value="fns:INVALID_QUERY_FILTER_OPERATOR"/>
                    <enumeration value="fns:INVALID_QUERY_LOCATOR"/>
                    <enumeration value="fns:INVALID_QUERY_SCOPE"/>
                    <enumeration value="fns:INVALID_REPLICATION_DATE"/>
                    <enumeration value="fns:INVALID_SEARCH"/>
                    <enumeration value="fns:INVALID_SEARCH_SCOPE"/>
                    <enumeration value="fns:INVALID_SESSION_ID"/>
                    <enumeration value="fns:INVALID_SOAP_HEADER"/>
                    <enumeration value="fns:INVALID_SSO_GATEWAY_URL"/>
                    <enumeration value="fns:INVALID_TYPE"/>
                    <enumeration value="fns:INVALID_TYPE_FOR_OPERATION"/>
                    <enumeration value="fns:JSON_PARSER_ERROR"/>
                    <enumeration value="fns:LIMIT_EXCEEDED"/>
                    <enumeration value="fns:LOGIN_CHALLENGE_ISSUED"/>
                    <enumeration value="fns:LOGIN_CHALLENGE_PENDING"/>
                    <enumeration value="fns:LOGIN_DURING_RESTRICTED_DOMAIN"/>
                    <enumeration value="fns:LOGIN_DURING_RESTRICTED_TIME"/>
                    <enumeration value="fns:LOGIN_MUST_USE_SECURITY_TOKEN"/>
                    <enumeration value="fns:MALFORMED_ID"/>
                    <enumeration value="fns:MALFORMED_QUERY"/>
                    <enumeration value="fns:MALFORMED_SEARCH"/>
                    <enumeration value="fns:MISSING_ARGUMENT"/>
                    <enumeration value="fns:NOT_MODIFIED"/>
                    <enumeration value="fns:NO_SOFTPHONE_LAYOUT"/>
                    <enumeration value="fns:NUMBER_OUTSIDE_VALID_RANGE"/>
                    <enumeration value="fns:OPERATION_TOO_LARGE"/>
                    <enumeration value="fns:ORG_IN_MAINTENANCE"/>
                    <enumeration value="fns:ORG_IS_DOT_ORG"/>
                    <enumeration value="fns:ORG_LOCKED"/>
                    <enumeration value="fns:ORG_NOT_OWNED_BY_INSTANCE"/>
                    <enumeration value="fns:PASSWORD_LOCKOUT"/>
                    <enumeration value="fns:PORTAL_NO_ACCESS"/>
                    <enumeration value="fns:QUERY_TIMEOUT"/>
                    <enumeration value="fns:QUERY_TOO_COMPLICATED"/>
                    <enumeration value="fns:REQUEST_LIMIT_EXCEEDED"/>
                    <enumeration value="fns:REQUEST_RUNNING_TOO_LONG"/>
                    <enumeration value="fns:SERVER_UNAVAILABLE"/>
                    <enumeration value="fns:SSO_SERVICE_DOWN"/>
                    <enumeration value="fns:TOO_MANY_APEX_REQUESTS"/>
                    <enumeration value="fns:TRIAL_EXPIRED"/>
                    <enumeration value="fns:UNKNOWN_EXCEPTION"/>
                    <enumeration value="fns:UNSUPPORTED_API_VERSION"/>
                    <enumeration value="fns:UNSUPPORTED_CLIENT"/>
                    <enumeration value="fns:UNSUPPORTED_MEDIA_TYPE"/>
                    <enumeration value="fns:XML_PARSER_ERROR"/>
                </restriction>
            </simpleType>


            <!-- Fault -->
            <complexType name="ApiFault">
                <sequence>
                    <element name="exceptionCode" type="fns:ExceptionCode"/>
                    <element name="exceptionMessage" type="xsd:string"/>

                </sequence>
            </complexType>

            <element name="fault" type="fns:ApiFault"/>

            <complexType name="ApiQueryFault">
                <complexContent>
                    <extension base="fns:ApiFault">
                        <sequence>
                        <element name="row" type="xsd:int"/>
                        <element name="column" type="xsd:int"/>
                        </sequence>
                    </extension>
                </complexContent>
            </complexType>

            <complexType name="LoginFault">
                <complexContent>
                    <extension base="fns:ApiFault"/>
                </complexContent>
            </complexType>
            <element name="LoginFault" type="fns:LoginFault"/>

            <complexType name="InvalidQueryLocatorFault">
                <complexContent>
                    <extension base="fns:ApiFault"/>
                </complexContent>
            </complexType>
            <element name="InvalidQueryLocatorFault" type="fns:InvalidQueryLocatorFault"/>

            <complexType name="InvalidNewPasswordFault">
                <complexContent>
                    <extension base="fns:ApiFault"/>
                </complexContent>
            </complexType>
            <element name="InvalidNewPasswordFault" type="fns:InvalidNewPasswordFault"/>

            <complexType name="InvalidIdFault">
                <complexContent>
                    <extension base="fns:ApiFault"/>
                </complexContent>
            </complexType>
            <element name="InvalidIdFault" type="fns:InvalidIdFault"/>

            <complexType name="UnexpectedErrorFault">
                <complexContent>
                    <extension base="fns:ApiFault"/>
                </complexContent>
            </complexType>
            <element name="UnexpectedErrorFault" type="fns:UnexpectedErrorFault"/>

            <complexType name="InvalidFieldFault">
                <complexContent>
                    <extension base="fns:ApiQueryFault"/>
                </complexContent>
            </complexType>
            <element name="InvalidFieldFault" type="fns:InvalidFieldFault"/>

            <complexType name="InvalidSObjectFault">
                <complexContent>
                    <extension base="fns:ApiQueryFault"/>
                </complexContent>
            </complexType>
            <element name="InvalidSObjectFault" type="fns:InvalidSObjectFault"/>

            <complexType name="MalformedQueryFault">
                <complexContent>
                    <extension base="fns:ApiQueryFault"/>
                </complexContent>
            </complexType>
            <element name="MalformedQueryFault" type="fns:MalformedQueryFault"/>

            <complexType name="MalformedSearchFault">
                <complexContent>
                    <extension base="fns:ApiQueryFault"/>
                </complexContent>
            </complexType>
            <element name="MalformedSearchFault" type="fns:MalformedSearchFault"/>


        </schema>
    </types>

    <!-- Header Message -->
    <message name="Header">
        <part element="tns:LoginScopeHeader" name="LoginScopeHeader"/>
        <part element="tns:SessionHeader" name="SessionHeader"/>

        <part element="tns:QueryOptions" name="QueryOptions"/>
        <part element="tns:AssignmentRuleHeader" name="AssignmentRuleHeader"/>
        <part element="tns:AllowFieldTruncationHeader" name="AllowFieldTruncationHeader"/>

        <part element="tns:AllOrNoneHeader" name="AllOrNoneHeader"/>


        <part element="tns:DisableFeedTrackingHeader" name="DisableFeedTrackingHeader"/>

        <part element="tns:MruHeader" name="MruHeader"/>
        <part element="tns:EmailHeader" name="EmailHeader"/>

        <part element="tns:UserTerritoryDeleteHeader" name="UserTerritoryDeleteHeader"/>

        <part element="tns:DebuggingHeader" name="DebuggingHeader"/>
        <part element="tns:PackageVersionHeader" name="PackageVersionHeader"/>
        <part element="tns:DebuggingInfo" name="DebuggingInfo"/>
        <part element="tns:LocaleOptions" name="LocaleOptions"/>
    </message>

    <!-- Fault Messages -->

    <message name="ApiFault">
        <part name="fault" element="fns:fault"/>
    </message>

    <message name="LoginFault">
        <part name="fault" element="fns:LoginFault"/>
    </message>
    <message name="InvalidQueryLocatorFault">
        <part name="fault" element="fns:InvalidQueryLocatorFault"/>
    </message>
    <message name="InvalidNewPasswordFault">
        <part name="fault" element="fns:InvalidNewPasswordFault"/>
    </message>
    <message name="InvalidIdFault">
        <part name="fault" element="fns:InvalidIdFault"/>
    </message>
    <message name="UnexpectedErrorFault">
        <part name="fault" element="fns:UnexpectedErrorFault"/>
    </message>
    <message name="InvalidFieldFault">
        <part name="fault" element="fns:InvalidFieldFault"/>
    </message>
    <message name="InvalidSObjectFault">
        <part name="fault" element="fns:InvalidSObjectFault"/>
    </message>
    <message name="MalformedQueryFault">
        <part name="fault" element="fns:MalformedQueryFault"/>
    </message>
    <message name="MalformedSearchFault">
        <part name="fault" element="fns:MalformedSearchFault"/>
    </message>


    <!-- Method Messages -->
    <message name="loginRequest">
        <part element="tns:login" name="parameters"/>
    </message>
    <message name="loginResponse">
        <part element="tns:loginResponse" name="parameters"/>
    </message>

    <message name="describeSObjectRequest">
        <part element="tns:describeSObject" name="parameters"/>
    </message>
    <message name="describeSObjectResponse">
        <part element="tns:describeSObjectResponse" name="parameters"/>
    </message>

    <message name="describeSObjectsRequest">
        <part element="tns:describeSObjects" name="parameters"/>
    </message>
    <message name="describeSObjectsResponse">
        <part element="tns:describeSObjectsResponse" name="parameters"/>
    </message>

    <message name="describeGlobalRequest">
        <part element="tns:describeGlobal" name="parameters"/>
    </message>
    <message name="describeGlobalResponse">
        <part element="tns:describeGlobalResponse" name="parameters"/>
    </message>

    <message name="describeDataCategoryGroupsRequest">
        <part element="tns:describeDataCategoryGroups" name="parameters"/>
    </message>
    <message name="describeDataCategoryGroupsResponse">
        <part element="tns:describeDataCategoryGroupsResponse" name="parameters"/>
    </message>

    <message name="describeDataCategoryGroupStructuresRequest">
        <part element="tns:describeDataCategoryGroupStructures" name="parameters"/>
    </message>
    <message name="describeDataCategoryGroupStructuresResponse">
        <part element="tns:describeDataCategoryGroupStructuresResponse" name="parameters"/>
    </message>

    <message name="describeLayoutRequest">
        <part element="tns:describeLayout" name="parameters"/>
    </message>
    <message name="describeLayoutResponse">
        <part element="tns:describeLayoutResponse" name="parameters"/>
    </message>

    <message name="describeSoftphoneLayoutRequest">
        <part element="tns:describeSoftphoneLayout" name="parameters"/>
    </message>
    <message name="describeSoftphoneLayoutResponse">
        <part element="tns:describeSoftphoneLayoutResponse" name="parameters"/>
    </message>

    <message name="describeTabsRequest">
        <part element="tns:describeTabs" name="parameters"/>
    </message>
    <message name="describeTabsResponse">
        <part element="tns:describeTabsResponse" name="parameters"/>
    </message>

    <message name="createRequest">
        <part element="tns:create" name="parameters"/>
    </message>
    <message name="createResponse">
        <part element="tns:createResponse" name="parameters"/>
    </message>

    <message name="updateRequest">
        <part element="tns:update" name="parameters"/>
    </message>
    <message name="updateResponse">
        <part element="tns:updateResponse" name="parameters"/>
    </message>

    <message name="upsertRequest">
        <part element="tns:upsert" name="parameters"/>
    </message>
    <message name="upsertResponse">
        <part element="tns:upsertResponse" name="parameters"/>
    </message>

    <message name="mergeRequest">
        <part element="tns:merge" name="parameters"/>
    </message>
    <message name="mergeResponse">
        <part element="tns:mergeResponse" name="parameters"/>
    </message>

    <message name="deleteRequest">
        <part element="tns:delete" name="parameters"/>
    </message>
    <message name="deleteResponse">
        <part element="tns:deleteResponse" name="parameters"/>
    </message>

    <message name="undeleteRequest">
        <part element="tns:undelete" name="parameters"/>
    </message>
    <message name="undeleteResponse">
        <part element="tns:undeleteResponse" name="parameters"/>
    </message>

    <message name="emptyRecycleBinRequest">
        <part element="tns:emptyRecycleBin" name="parameters"/>
    </message>
    <message name="emptyRecycleBinResponse">
        <part element="tns:emptyRecycleBinResponse" name="parameters"/>
    </message>

    <message name="retrieveRequest">
        <part element="tns:retrieve" name="parameters"/>
    </message>
    <message name="retrieveResponse">
        <part element="tns:retrieveResponse" name="parameters"/>
    </message>

    <message name="processRequest">
        <part element="tns:process" name="parameters"/>
    </message>
    <message name="processResponse">
        <part element="tns:processResponse" name="parameters"/>
    </message>

    <message name="convertLeadRequest">
        <part element="tns:convertLead" name="parameters"/>
    </message>
    <message name="convertLeadResponse">
        <part element="tns:convertLeadResponse" name="parameters"/>
    </message>

    <message name="logoutRequest">
        <part element="tns:logout" name="parameters"/>
    </message>
    <message name="logoutResponse">
        <part element="tns:logoutResponse" name="parameters"/>
    </message>

    <message name="invalidateSessionsRequest">
        <part element="tns:invalidateSessions" name="parameters"/>
    </message>
    <message name="invalidateSessionsResponse">
        <part element="tns:invalidateSessionsResponse" name="parameters"/>
    </message>

    <message name="getDeletedRequest">
        <part element="tns:getDeleted" name="parameters"/>
    </message>
    <message name="getDeletedResponse">
        <part element="tns:getDeletedResponse" name="parameters"/>
    </message>

    <message name="getUpdatedRequest">
        <part element="tns:getUpdated" name="parameters"/>
    </message>
    <message name="getUpdatedResponse">
        <part element="tns:getUpdatedResponse" name="parameters"/>
    </message>

    <message name="queryRequest">
        <part element="tns:query" name="parameters"/>
    </message>
    <message name="queryResponse">
        <part element="tns:queryResponse" name="parameters"/>
    </message>

    <message name="queryAllRequest">
        <part element="tns:queryAll" name="parameters"/>
    </message>
    <message name="queryAllResponse">
        <part element="tns:queryAllResponse" name="parameters"/>
    </message>

    <message name="queryMoreRequest">
        <part element="tns:queryMore" name="parameters"/>
    </message>
    <message name="queryMoreResponse">
        <part element="tns:queryMoreResponse" name="parameters"/>
    </message>

    <message name="searchRequest">
        <part element="tns:search" name="parameters"/>
    </message>
    <message name="searchResponse">
        <part element="tns:searchResponse" name="parameters"/>
    </message>

    <message name="getServerTimestampRequest">
        <part element="tns:getServerTimestamp" name="parameters"/>
    </message>
    <message name="getServerTimestampResponse">
        <part element="tns:getServerTimestampResponse" name="parameters"/>
    </message>

    <message name="setPasswordRequest">
        <part element="tns:setPassword" name="parameters"/>
    </message>
    <message name="setPasswordResponse">
        <part element="tns:setPasswordResponse" name="parameters"/>
    </message>

    <message name="resetPasswordRequest">
        <part element="tns:resetPassword" name="parameters"/>
    </message>
    <message name="resetPasswordResponse">
        <part element="tns:resetPasswordResponse" name="parameters"/>
    </message>

    <message name="getUserInfoRequest">
        <part element="tns:getUserInfo" name="parameters"/>
    </message>
    <message name="getUserInfoResponse">
        <part element="tns:getUserInfoResponse" name="parameters"/>
    </message>

    <message name="sendEmailRequest">
        <part element="tns:sendEmail" name="parameters"/>
    </message>
    <message name="sendEmailResponse">
        <part element="tns:sendEmailResponse" name="parameters"/>
    </message>



    <!-- Soap PortType -->
    <portType name="Soap">
        <operation name="login">
            <documentation>Login to the Salesforce.com SOAP Api</documentation>
            <input message="tns:loginRequest"/>
            <output message="tns:loginResponse"/>
            <fault message="tns:LoginFault" name="LoginFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
        </operation>

        <operation name="describeSObject">
            <documentation>Describe an sObject</documentation>
            <input message="tns:describeSObjectRequest"/>
            <output message="tns:describeSObjectResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="describeSObjects">
            <documentation>Describe a number sObjects</documentation>
            <input message="tns:describeSObjectsRequest"/>
            <output message="tns:describeSObjectsResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="describeGlobal">
            <documentation>Describe the Global state</documentation>
            <input message="tns:describeGlobalRequest"/>
            <output message="tns:describeGlobalResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="describeDataCategoryGroups">
            <documentation>Describe all the data category groups available for a given set of types</documentation>
            <input message="tns:describeDataCategoryGroupsRequest"/>
            <output message="tns:describeDataCategoryGroupsResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="describeDataCategoryGroupStructures">
            <documentation>Describe the data category group structures for a given set of pair of types and data category group name</documentation>
            <input message="tns:describeDataCategoryGroupStructuresRequest"/>
            <output message="tns:describeDataCategoryGroupStructuresResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="describeLayout">
            <documentation>Describe the layout of an sObject</documentation>
            <input message="tns:describeLayoutRequest"/>
            <output message="tns:describeLayoutResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
        </operation>

        <operation name="describeSoftphoneLayout">
            <documentation>Describe the layout of the SoftPhone</documentation>
            <input message="tns:describeSoftphoneLayoutRequest"/>
            <output message="tns:describeSoftphoneLayoutResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="describeTabs">
            <documentation>Describe the tabs that appear on a users page</documentation>
            <input message="tns:describeTabsRequest"/>
            <output message="tns:describeTabsResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="create">
            <documentation>Create a set of new sObjects</documentation>
            <input message="tns:createRequest"/>
            <output message="tns:createResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
            <fault message="tns:InvalidFieldFault" name="InvalidFieldFault"/>
        </operation>

        <operation name="update">
            <documentation>Update a set of sObjects</documentation>
            <input message="tns:updateRequest"/>
            <output message="tns:updateResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
            <fault message="tns:InvalidFieldFault" name="InvalidFieldFault"/>
        </operation>

        <operation name="upsert">
            <documentation>Update or insert a set of sObjects based on object id</documentation>
            <input message="tns:upsertRequest"/>
            <output message="tns:upsertResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
            <fault message="tns:InvalidFieldFault" name="InvalidFieldFault"/>
        </operation>

        <operation name="merge">
            <documentation>Merge and update a set of sObjects based on object id</documentation>
            <input message="tns:mergeRequest"/>
            <output message="tns:mergeResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
            <fault message="tns:InvalidFieldFault" name="InvalidFieldFault"/>
        </operation>

        <operation name="delete">
            <documentation>Delete a set of sObjects</documentation>
            <input message="tns:deleteRequest"/>
            <output message="tns:deleteResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="undelete">
            <documentation>Undelete a set of sObjects</documentation>
            <input message="tns:undeleteRequest"/>
            <output message="tns:undeleteResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="emptyRecycleBin">
            <documentation>Empty a set of sObjects from the recycle bin</documentation>
            <input message="tns:emptyRecycleBinRequest"/>
            <output message="tns:emptyRecycleBinResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="retrieve">
            <documentation>Get a set of sObjects</documentation>
            <input message="tns:retrieveRequest"/>
            <output message="tns:retrieveResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:InvalidFieldFault" name="InvalidFieldFault"/>
            <fault message="tns:MalformedQueryFault" name="MalformedQueryFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
        </operation>

        <operation name="process">
            <documentation>Submit an entity to a workflow process or process a workitem</documentation>
            <input message="tns:processRequest"/>
            <output message="tns:processResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
        </operation>

        <operation name="convertLead">
            <documentation>convert a set of leads</documentation>
            <input message="tns:convertLeadRequest"/>
            <output message="tns:convertLeadResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="logout">
            <documentation>Logout the current user, invalidating the current session.</documentation>
            <input message="tns:logoutRequest"/>
            <output message="tns:logoutResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="invalidateSessions">
            <documentation>Logs out and invalidates session ids</documentation>
            <input message="tns:invalidateSessionsRequest"/>
            <output message="tns:invalidateSessionsResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="getDeleted">
            <documentation>Get the IDs for deleted sObjects</documentation>
            <input message="tns:getDeletedRequest"/>
            <output message="tns:getDeletedResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="getUpdated">
            <documentation>Get the IDs for updated sObjects</documentation>
            <input message="tns:getUpdatedRequest"/>
            <output message="tns:getUpdatedResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="query">
            <documentation>Create a Query Cursor</documentation>
            <input message="tns:queryRequest"/>
            <output message="tns:queryResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:InvalidFieldFault" name="InvalidFieldFault"/>
            <fault message="tns:MalformedQueryFault" name="MalformedQueryFault"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidQueryLocatorFault" name="InvalidQueryLocatorFault"/>
        </operation>

        <operation name="queryAll">
            <documentation>Create a Query Cursor, including deleted sObjects</documentation>
            <input message="tns:queryAllRequest"/>
            <output message="tns:queryAllResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:InvalidFieldFault" name="InvalidFieldFault"/>
            <fault message="tns:MalformedQueryFault" name="MalformedQueryFault"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidQueryLocatorFault" name="InvalidQueryLocatorFault"/>
        </operation>

        <operation name="queryMore">
            <documentation>Gets the next batch of sObjects from a query</documentation>
            <input message="tns:queryMoreRequest"/>
            <output message="tns:queryMoreResponse"/>
            <fault message="tns:InvalidQueryLocatorFault" name="InvalidQueryLocatorFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
            <fault message="tns:InvalidFieldFault" name="InvalidFieldFault"/>
        </operation>

        <operation name="search">
            <documentation>Search for sObjects</documentation>
            <input message="tns:searchRequest"/>
            <output message="tns:searchResponse"/>
            <fault message="tns:InvalidSObjectFault" name="InvalidSObjectFault"/>
            <fault message="tns:InvalidFieldFault" name="InvalidFieldFault"/>
            <fault message="tns:MalformedSearchFault" name="MalformedSearchFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="getServerTimestamp">
            <documentation>Gets server timestamp</documentation>
            <input message="tns:getServerTimestampRequest"/>
            <output message="tns:getServerTimestampResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="setPassword">
            <documentation>Set a user's password</documentation>
            <input message="tns:setPasswordRequest"/>
            <output message="tns:setPasswordResponse"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
            <fault message="tns:InvalidNewPasswordFault" name="InvalidNewPasswordFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="resetPassword">
            <documentation>Reset a user's password</documentation>
            <input message="tns:resetPasswordRequest"/>
            <output message="tns:resetPasswordResponse"/>
            <fault message="tns:InvalidIdFault" name="InvalidIdFault"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="getUserInfo">
            <documentation>Returns standard information relevant to the current user</documentation>
            <input message="tns:getUserInfoRequest"/>
            <output message="tns:getUserInfoResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

        <operation name="sendEmail">
            <documentation>Send outbound email</documentation>
            <input message="tns:sendEmailRequest"/>
            <output message="tns:sendEmailResponse"/>
            <fault message="tns:UnexpectedErrorFault" name="UnexpectedErrorFault"/>
        </operation>

    </portType>

    <!-- Soap Binding -->
    <binding name="SoapBinding" type="tns:Soap">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="login">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="LoginScopeHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="LoginFault">
                <soap:fault name="LoginFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
        </operation>
        <operation name="describeSObject">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:header use="literal" message="tns:Header" part="LocaleOptions"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="describeSObjects">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:header use="literal" message="tns:Header" part="LocaleOptions"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="describeGlobal">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="describeDataCategoryGroups">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="describeDataCategoryGroupStructures">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="describeLayout">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
        </operation>
        <operation name="describeSoftphoneLayout">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="describeTabs">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="create">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="AssignmentRuleHeader"/>
                <soap:header use="literal" message="tns:Header" part="MruHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllowFieldTruncationHeader"/>
                <soap:header use="literal" message="tns:Header" part="DisableFeedTrackingHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllOrNoneHeader"/>
                <soap:header use="literal" message="tns:Header" part="DebuggingHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:header use="literal" message="tns:Header" part="EmailHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:header use="literal" message="tns:Header" part="DebuggingInfo"/>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
            <fault name="InvalidFieldFault">
                <soap:fault name="InvalidFieldFault" use="literal"/>
            </fault>
        </operation>
        <operation name="update">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="AssignmentRuleHeader"/>
                <soap:header use="literal" message="tns:Header" part="MruHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllowFieldTruncationHeader"/>
                <soap:header use="literal" message="tns:Header" part="DisableFeedTrackingHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllOrNoneHeader"/>
                <soap:header use="literal" message="tns:Header" part="DebuggingHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:header use="literal" message="tns:Header" part="EmailHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:header use="literal" message="tns:Header" part="DebuggingInfo"/>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
            <fault name="InvalidFieldFault">
                <soap:fault name="InvalidFieldFault" use="literal"/>
            </fault>
        </operation>
        <operation name="upsert">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="AssignmentRuleHeader"/>
                <soap:header use="literal" message="tns:Header" part="MruHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllowFieldTruncationHeader"/>
                <soap:header use="literal" message="tns:Header" part="DisableFeedTrackingHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllOrNoneHeader"/>
                <soap:header use="literal" message="tns:Header" part="DebuggingHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:header use="literal" message="tns:Header" part="EmailHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:header use="literal" message="tns:Header" part="DebuggingInfo"/>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
            <fault name="InvalidFieldFault">
                <soap:fault name="InvalidFieldFault" use="literal"/>
            </fault>
        </operation>
        <operation name="merge">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="AssignmentRuleHeader"/>
                <soap:header use="literal" message="tns:Header" part="MruHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllowFieldTruncationHeader"/>
                <soap:header use="literal" message="tns:Header" part="DisableFeedTrackingHeader"/>
                <soap:header use="literal" message="tns:Header" part="DebuggingHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:header use="literal" message="tns:Header" part="EmailHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:header use="literal" message="tns:Header" part="DebuggingInfo"/>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
            <fault name="InvalidFieldFault">
                <soap:fault name="InvalidFieldFault" use="literal"/>
            </fault>
        </operation>
        <operation name="delete">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:header use="literal" message="tns:Header" part="UserTerritoryDeleteHeader"/>
                <soap:header use="literal" message="tns:Header" part="EmailHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllowFieldTruncationHeader"/>
                <soap:header use="literal" message="tns:Header" part="DisableFeedTrackingHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllOrNoneHeader"/>
                <soap:header use="literal" message="tns:Header" part="DebuggingHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:header use="literal" message="tns:Header" part="DebuggingInfo"/>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="undelete">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllowFieldTruncationHeader"/>
                <soap:header use="literal" message="tns:Header" part="DisableFeedTrackingHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllOrNoneHeader"/>
                <soap:header use="literal" message="tns:Header" part="DebuggingHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:header use="literal" message="tns:Header" part="DebuggingInfo"/>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="emptyRecycleBin">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="retrieve">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="QueryOptions"/>
                <soap:header use="literal" message="tns:Header" part="MruHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="InvalidFieldFault">
                <soap:fault name="InvalidFieldFault" use="literal"/>
            </fault>
            <fault name="MalformedQueryFault">
                <soap:fault name="MalformedQueryFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
        </operation>
        <operation name="process">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllowFieldTruncationHeader"/>
                <soap:header use="literal" message="tns:Header" part="DisableFeedTrackingHeader"/>
                <soap:header use="literal" message="tns:Header" part="DebuggingHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:header use="literal" message="tns:Header" part="DebuggingInfo"/>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
        </operation>
        <operation name="convertLead">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="AllowFieldTruncationHeader"/>
                <soap:header use="literal" message="tns:Header" part="DisableFeedTrackingHeader"/>
                <soap:header use="literal" message="tns:Header" part="DebuggingHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:header use="literal" message="tns:Header" part="DebuggingInfo"/>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="logout">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="invalidateSessions">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="getDeleted">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="getUpdated">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="query">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="QueryOptions"/>
                <soap:header use="literal" message="tns:Header" part="MruHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="InvalidFieldFault">
                <soap:fault name="InvalidFieldFault" use="literal"/>
            </fault>
            <fault name="MalformedQueryFault">
                <soap:fault name="MalformedQueryFault" use="literal"/>
            </fault>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidQueryLocatorFault">
                <soap:fault name="InvalidQueryLocatorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="queryAll">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="QueryOptions"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="InvalidFieldFault">
                <soap:fault name="InvalidFieldFault" use="literal"/>
            </fault>
            <fault name="MalformedQueryFault">
                <soap:fault name="MalformedQueryFault" use="literal"/>
            </fault>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidQueryLocatorFault">
                <soap:fault name="InvalidQueryLocatorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="queryMore">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="QueryOptions"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidQueryLocatorFault">
                <soap:fault name="InvalidQueryLocatorFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
            <fault name="InvalidFieldFault">
                <soap:fault name="InvalidFieldFault" use="literal"/>
            </fault>
        </operation>
        <operation name="search">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="PackageVersionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidSObjectFault">
                <soap:fault name="InvalidSObjectFault" use="literal"/>
            </fault>
            <fault name="InvalidFieldFault">
                <soap:fault name="InvalidFieldFault" use="literal"/>
            </fault>
            <fault name="MalformedSearchFault">
                <soap:fault name="MalformedSearchFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="getServerTimestamp">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="setPassword">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
            <fault name="InvalidNewPasswordFault">
                <soap:fault name="InvalidNewPasswordFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="resetPassword">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:header use="literal" message="tns:Header" part="EmailHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="InvalidIdFault">
                <soap:fault name="InvalidIdFault" use="literal"/>
            </fault>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="getUserInfo">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>
        <operation name="sendEmail">
            <soap:operation soapAction=""/>
            <input>
                <soap:header use="literal" message="tns:Header" part="SessionHeader"/>
                <soap:body parts="parameters" use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="UnexpectedErrorFault">
                <soap:fault name="UnexpectedErrorFault" use="literal"/>
            </fault>
        </operation>

    </binding>

    <!-- Soap Service Endpoint -->
    <service name="SforceService">
        <documentation>Sforce SOAP API</documentation>
        <port binding="tns:SoapBinding" name="Soap">
            <soap:address location="https://login.salesforce.com/services/Soap/c/27.0"/>
        </port>
    </service>
</definitions>