<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/service_controller.proto

namespace Google\Api\Servicecontrol\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Servicecontrol\V1\CheckResponse\ConsumerInfo instead.
     * @deprecated
     */
    class CheckResponse_ConsumerInfo {}
}
class_exists(CheckResponse\ConsumerInfo::class);
@trigger_error('Google\Api\Servicecontrol\V1\CheckResponse_ConsumerInfo is deprecated and will be removed in the next major release. Use Google\Api\Servicecontrol\V1\CheckResponse\ConsumerInfo instead', E_USER_DEPRECATED);

