<?php

	/** \file workqueue-ebay.php
	 *
	 * 	Ce script est destiné à traiter les tâches contenues dans la file d'attente eBay.
	 *	Les tâches en question sont des demandes de création de produit, de mise à jour ou de suppression.
	 *	Il est destiné à être lancé automatiquement deux fois par heure aux minutes 29 et 59 (pas encore en production).
	 *
	 */
	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('tsk.comparators.inc.php');
	require_once('comparators/ctr.ebay.inc.php');
	/* TODO : le numéro d'import est sauvegardé, il faut maintenant gérer le résultat (comme sur les autres marketplace) */

	// active ou non le mode test
	$mode_test = isset($ar_params['test']) && $ar_params['test'] == 'test';

	// Traitement
	foreach( $configs as $config ){

		// Vérifie que la place de marché est bien activée pour ce client, dans le cas contraire, on passe au client suivant.
		if( !ctr_comparators_actived(CTR_EBAY) ){
			continue;
		}

		// Récupère la liste des taches à réaliser. Si aucune tâche, on passe au client suivant.
		$rtsk = tsk_comparators_get( CTR_EBAY );
		if( !$rtsk || !ria_mysql_num_rows($rtsk) ){
			continue;
		}

		// Création de l'objet, si le mode test est activé alors les actions seront réalisées sur la sandbox
		$ebay = new EBay( $mode_test );

		$tsks_exec = array(); $tsks_no_exec = array();
		while( $tsk = ria_mysql_fetch_array($rtsk) ){
			// print $tsk['prd_id']."\n";
			$res = false;
			switch( $tsk['action'] ){
				case 'add' :
					$res = $ebay->worqueueEBayProduct( $tsk['prd_id'], 'add' );
					break;
				case 'update' :
					$res = $ebay->worqueueEBayProduct( $tsk['prd_id'], 'update' );
					break;
				case 'delete' :
					$res = $ebay->worqueueEBayProduct( $tsk['prd_id'], 'delete' );
					break;
			}

			if( !$res ){
				$tsks_no_exec[] = 'Tâche n°'.$tsk['id'].' correspondant au produit n°'.$tsk['prd_id'];
			}else{
				$tsks_exec[] = $tsk['id'];
			}
		}

		if( sizeof($tsks_no_exec) ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
		}

		if( sizeof($tsks_exec) ){
			tsk_comparators_set_completed( $tsks_exec );
		}
	}

