/**	\file import.js
 * 	Ce fichier contient les différentes fonctionnalités d'interaction avec les pages d'importation
 */

// initialisation dès que le dom est chargé
var currentAjax = false;
var noticeSiret;

$(document).ready(function(){

	$('#default_prf').each(function(){
		ajaxGetDefault($(this), 'USR_DEFAULT_PRF');
	});

	// Initialisation du plugin tablesorter et tablesorterpager
	// (page /admin/tools/imports/reports.php)
	if( $( "#imp-form" ).length ){
		$.tablesorter.addParser({
			id: "data-sort-value",
			is: function() {
				return false;
			},
			format: function(s, table, cell) {
				return $.tablesorter.formatFloat(new Date($(cell).attr('data-sort-value')).getTime());
			},
			type: "numeric"
		});
		$( "#imp-form" )
			.tablesorter({
				headers: {
					5: { sorter: "data-sort-value" }
				}
			})
			.tablesorterPager({
				container: $( "#pagination" ),
				output: '{startRow} à {endRow} sur {totalRows} lignes'
			});
	}

	// ajout des écouteur d'évènement
	$(document).on('change', ".promo", function(){
		if($(this).is(':checked')){
			$(this).parent().next().show();
		}else{
			$(this).parent().next().hide();
		}
	});

	$("input[name=imp-auto]").on("click", togglePeriods);
	$("select[name=imp-period]").on("change", renderperiodsValue);
	$('.imp-form input[type=submit],.map-form input[type=submit]').on('click', function(){
		$(this).attr("clicked", true);
	});

	/**
	 * IMPORT
	 */
	// activation du Drag & Drop si on choisi la méthode file
	if ($('#imp-method').val() === 'file') {
		$('.box').addClass('has-advanced-upload');
		initDragAndDrop();
	}

	// Au changement du fichier d'import.
	$(document).on('change','#imp-file', function(){
		// ne doit pas s'exécuter sur les champs avec une url.
		if ($(this).prev().prop('for') !== 'imp-url') {
			$.ajax({
				url: '/admin/ajax/tools/imports/check-file-size.php',
				data: {fileSize:(this.files[0].size  / 1024 /1024)},
				method: 'get',
				dataType: 'json'
			})
			.done(function(json){
				if (json.result && json.content.toBig) {
					$('#imp-file').val(null);
					$( ".notif" ).html(json.message).addClass('error');
				}else{
					$( ".notif" ).html("").removeClass('error');
				}
			});
		}
	});

	// Le contenu des blocs d'information va changer en fonction du type de donnés choisi
	$(document).on('change', '#imp-class', function(){

		var typeId = $(this).val();
		var impData = '';
		// est ce qu'on est sur un modèle de commande ou des remises clients
		var option = $(this).find(':selected');
		if (option.data('info') != undefined) {
			impData = option.data('info');
		}
		$.ajax({
			url: '/admin/ajax/tools/ajax-import-help.php',
			data: {'imp_type': typeId, 'imp_data' : impData},
			method: 'get',
			dataType: 'json'
		})
		.done(function(json){
			if (json.content) {
				$( ".--download-example" ).html(json.content.download);
				$( ".--file-prepare" ).html(json.content.help);
			}
		});

	});

	// Les actions d'importation pouvant être réalisées dépendent des droits de l'utilisateur, raison pour laquelle
	// ils sont traités côté serveur. Actualise les actions en fonction du type de données choisi
	$(document).on('change', '#imp-class', function(){

		var previousValue = $('#imp-action').val();
		$.ajax( "/admin/tools/imports/check-action-import.php?class="+$('#imp-class option:selected').val() )
		.done(function(data) {
			// Supprime les options précédentes
			var actions = JSON.parse(data);
			$('#imp-action').find('option').remove()
			$('#imp-action').append($('<option>', {
					value: '',
					text : importChooseAction,
					disabled: true ,
					selected: true
			}));
			// Crée les options disponibles en fonction du type choisi
			$.each(actions, function (val, text) {
				$('#imp-action').append($('<option>', {
					value: val,
					text : text
				}));
			});
			// Restaure la valeur sélectionnée avant rechargement
			$('#imp-action').val( previousValue );
		});
	});

	// au submit du formulaire d'import
	$('.imp-form').on("submit", formSubmitionValidation);

	// au changement du choix de la méthode
	$('#imp-method').on('change', renderImportInput);

	// vérification de l'affichage du bouton d'import
	$(document).on('change', '.imp-form', blockStartImportValid);

	// affichage des options d'import avancés
	$(".toggle-advance-settings").on("click", function(){
		$(".advance-settings").toggle();
		if( this.dataset.open == 0 ){
			this.value = importsMasquerOption;
			this.dataset.open = 1;
		}else{
			this.dataset.open = 0;
			this.value = importsAfficherOption;
		}
	});

	/**
	 * MAPPING
	 */
	// a la demande d'import ou la sauvegarde du formulaire de mapping.
	$('.map-form').on("click", 'input[name="save"], input[name="import"]',function(e){
		mappingFormValidation(e);
		$('html,body').animate({ scrollTop: 0 }, 'slow');
	});

	// à la modification du select de destination
	$('.js-map-select select').on('change', mappingReloadOptions);

	// vérification de la duplication sur les langues
	$(document).on('change', 'select[data-name=lang]', checkDuplications);

	// vérification de la duplication si l'on choisi une option adresse de livraison / adresse de facturation
	$(document).on('change', 'input[data-name="inv_adr"], input[data-name="dlv_adr"]', function(){
		$(this).parents('.table-map-row').find('.map-error').html('').removeClass('error');
		checkDuplications();
	});

	// Contrôle le nombre d'action à effectuer après avoir saisi une correspondance de valeur
	$(document).on('blur', '.input-client:visible, .map-option [type="text"]', countIncompleteRows);

	$(document).on('change', '.--column-action select, .--column-action input[data-name="vals-bool"], .--column-action input[data-name="sep"]', function(event){
		var select          = $(event.target),
		    type            = select.data('name'),
		    value           = select.val(),
		    select_name     = 'map-'+select.data('pos'),
		    selected_option = $("#"+select_name).val();


		switch( type ){
			case 'rel-action':
				if( value === '0' ){
					$(this).closest('.--column-action-select').find('div[data-name=sep]').hide();
					$(this).closest('.--column-action-select').find('div[data-name=rel-id]').hide();
					$(this).closest('.--column-action-select').find('div[data-name=cat-first]').hide();
				}else if(value ==='del' && (selected_option === 'PRD_CANONICAL_LINK_PRD' ||  selected_option === 'PRD_CANONICAL_LINK_CAT') ){
					$(this).closest('.--column-action-select').find('div[data-name=sep]').hide();
					$(this).closest('.--column-action-select').find('div[data-name=rel-id]').hide();
					$(this).closest('.--column-action-select').find('div[data-name=cat-first]').hide();
				}else{
					$(this).closest('.--column-action-select').find('div[data-name=rel-id]').show();
					$(this).closest('.--column-action-select').find('div[data-name=sep]').show();
					$(this).closest('.--column-action-select').find('div[data-name=cat-first]').show();
				}
				break;
			case 'rel-type' :
				if( $.inArray(value, notMandatorySep) > -1 ){
					$(this).closest('.--column-action-select').find('div[data-name=sep]').find('.mandatory').hide();
				}else{
					$(this).closest('.--column-action-select').find('div[data-name=sep]').find('.mandatory').show();
				}
				break;
		}
	});

	$(document).on('change', ".--column-action .radio", function(event){

		$('.radio').attr('checked',false);
		$(event.target).attr('checked', true);
	});

	$(document).on('change', "select[data-name='rel-id']", function(){
		if($(this).val()=='classmt'){
			$(this).parent().after('<div class="map-option" data-name="sep"><label for="map-sep-'+$(this).attr('data-pos')+'-2" name="label">' + importsSeparateur + '</label><input type="text" class="option-sep" id="map-sep-'+$(this).attr("data-pos")+'-2" name="mapping['+$(this).attr("data-pos")+'][options][sep]" data-name="sep" data-pos="'+$(this).attr("data-pos")+'"></div>');
		}
	});

	var have_id = false; // Si aucun identifiant n'est choisi, affiche un message d'information
	$('.js-map-select select').each(function(){
		if($(this).val() != ""){
			loadOptions($(this), true);
			isSectionVisible();
		}
	});

	if( !have_id ){
		$('#identifiant').show();
	}

	$(document).on('change','.js-map-select .select_mapping', function(e){
		ajaxGetFld(e);
		checkDuplications();
		countIncompleteRows();
	});

	/**
	 * Validation du mapping entre le fichier de données et le schéma RiaShop
	 * @param {*} event
	 */
	var mappingFormValidation = function( event ){
		event.preventDefault();
		if( currentAjax ){
			return true;
		}
		//initialisation des variables
		var $form = $('.map-form'),
			formData = $form.serialize()+'&'+$('input[type=submit][clicked=true]')[0].name+'=""';

		if( $('.table-map-section .map-error.error').length ){
			formData += '&have_error=1';
		}

		$('input[type=submit][clicked=true]').removeAttr("clicked");

		checkDuplications(false);

		currentAjax = $.ajax({
			url: '/admin/tools/imports/check-mapping.php',
			data: formData,
			method: 'post',
			dataType: 'json'
		})
		.done(function(json){
			renderMappingErrors( json, event.toElement.name=='import' );
		})
		.always(function() {
			currentAjax = false;
		});
	};

	// Permet l'actualisation automatique de la progression des imports de données
	switch( window.location.pathname ){
		case '/admin/tools/imports/index.php':
		case '/admin/tools/imports/mapping.php':

			reloadProgressBar();
			setInterval(reloadProgressBar, 5000);

			break;
	}

	$(document).on('hover, click', '.info-click', function(){
		$(this).siblings('.option-notice').toggle();
	});

	$(document).on('mouseleave', '.option-notice', function(){
		$(this).toggle();
	})

	stickyHeader();

	// Une fois que tout est chargé
	// Contrôle du nombre de mapping incomplète
	countIncompleteRows();
	// Contrôle la duplicité des mapping
	checkDuplications();
});

/**
 * Sticky header
 */
function stickyHeader(){
	// position de l'entête au chargement
	var position = $('.table-map-head').offset();
	// position du conteneur au chargement
	var parentPosition = $('#table-map-file').offset();
	// largeur du parent au chargement
	var parentWidth = $('#table-map-file').width();

	$(window).scroll(function(){
		// Lorsque la position du scroll est inférieure ou égale à la position initiale de l'element
		if ( position != undefined  && $(window).scrollTop() >= position.top) {
			// ajout de class + variable css pour le placement
			$('#table-map-file').css({'padding-top':'65px'});
			$('.table-map-head').addClass('fixed-header').css({'left': parentPosition.left, 'width': parentWidth});
		}else{
			// retour en position static
			$('#table-map-file').css({'padding-top':'0'});
			$('.table-map-head').removeClass('fixed-header').css('width', '100%');
		}
	});
}

var droppedFiles = false;

/** Détecte si le navigateur actuellement utilisé supporte l'API Drag & Drop HTML5
 */
function isAdvancedUpload(){
	var div = document.createElement('div');
	return (('draggable' in div) || ('ondragstart' in div && 'ondrop' in div)) && 'FormData' in window && 'FileReader' in window;
}

/** Active la zone de Drag & Drop destinée à accueillir le fichier à importer
 */
function initDragAndDrop(){

	// Custom file input
	// permet de marquer le nom du fichier sélectionné à la place du label "Sélectionnez un fichier"
	$( '.box__file' ).each( function(){
		var $input	 = $( this ),
			$label	 = $input.next( 'label' ),
			labelVal = $label.html();

		$input.on( 'change', function( e )
		{
			var fileName = '';

			if( this.files && this.files.length > 1 )
				fileName = ( this.getAttribute( 'data-multiple-caption' ) || '' ).replace( '{count}', this.files.length );
			else if( e.target.value )
				fileName = e.target.value.split( '\\' ).pop();

			if( fileName )
				$label.find( 'span' ).html( fileName );
			else
				$label.html( labelVal );
		});

		// Firefox bug fix
		$input
		.on( 'focus', function(){ $input.addClass( 'has-focus' ); })
		.on( 'blur', function(){ $input.removeClass( 'has-focus' ); });
	});

	var $box = $('.box');
	var $input  = $box.find('input#imp-file'),
	$label    = $box.find('label'),
	showFiles = function(files) {
		// can support multiple uploads
		if (files.length > 1) {
			$label.text( ($input.attr('data-multiple-caption') || '').replace( '{count}', files.length )) ;
		} else {
			$label.text(files[ 0 ].name);
		}
	};

	if (isAdvancedUpload) {
		// css purpose
		$box.addClass('has-advanced-upload');

		$box.on('drag dragstart dragend dragover dragenter dragleave drop', function(e) {
			e.preventDefault();
			e.stopPropagation();
		})
		.on('dragover dragenter', function() {
			// css purpose
			$box.addClass('is-dragover');
		})
		.on('dragleave dragend drop', function() {
			// css purpose
			$box.removeClass('is-dragover');
		})
		.on('drop', function(e) {
			// display filename in label
			droppedFiles = e.originalEvent.dataTransfer.files; // file that is dropped
			showFiles( droppedFiles );
			blockStartImportValid(); // Active/désactive le bouton d'Importation
		});
	}
}

/**
 * retourne le fichier du drag and drop ou false
 */
function checkFileImport() {
	var $box = $('.box');

	if ($box) {
		if ($box.hasClass('is-uploading')) return false;
		$box.addClass('is-uploading').removeClass('is-error');

		if (isAdvancedUpload) {
			if (droppedFiles) {
				return droppedFiles[0];
			}
		}
		return false;
	}
};

/** Vérifie si on active ou non le bouton d'import.
 * 	Pour être activé, les conditions suivantes doivent être réunies :
 * 	- Le type de données doit être connu (Produits, Clients, Tarifs, etc...)
 *  - La méthode de transfert doit être connue et renseignée de façon complète (fichier, ftp, etc...)
 *  - l'action doit avoir été sélectionnée (Ajout uniquement, Ajout et mise à jour, Mise à jour uniquement, etc...)
 */
function blockStartImportValid(){
	var impClass = false;
	$('#imp-class').val() !== null ? impClass = true : null;
	var impAction = false;
	$('#imp-action').val() !== null ? impAction = true : null;

	// Transmission
	var impMethod = false;
	switch ($('#imp-method').val()) {
		case 'file':
			$('label[for="imp-file"] > span').html() !== importsSelectionFichier ? impMethod = true : null;
			break;
		case 'url':
			$('#imp-file').val() !== "" ? impMethod = true : null;
			break;
		case 'ftp':
			if ($('#imp-url').val() === "" || $('#imp-login').val() === "" || $('#imp-password').val() === "" || $('#imp-file').val() === "" ) {
				impMethod = false;
			} else {
				impMethod = true;
			}
			break;

		default:
				impMethod = false;
			break;
	}
	if (!impClass || !impAction || !impMethod) {
		$('#btn-import').prop('disabled', true);
	} else {
		$('#btn-import').prop('disabled', false);
	}
}

var notMandatorySep = ['parent'];

/** Affiche le formulaire adapté en fonction du mode de transfert de fichier choisi
 * @param {*} event
 */
function renderImportInput(event) {
	var value = event.target.value;
	var container = $(".input-file-container");
	container.html('');

	var html = '';
	switch (value) {
		case 'url':
			html = '<label for="imp-url" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> ' + importsUrlFichier + ' : </label>'
				+ '<input class="input-file" id="imp-file" type="text" name="imp-url" size="75" maxlength="255" value="" autocomplete="off" />'
				+ '<p class="field-helper">' + importMaxSize + '</p>'
			;
			break;
		case 'ftp':
			html = '<div>'
				+ '<div><label for="imp-url" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> ' + importsServeurFTP + ' : </label>'
				+ '<input class="input-file" id="imp-url" type="text" name="imp-url" size="32" maxlength="255" value="" /></div>'
				+ '<div><label for="imp-login" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> ' + importsNomUtilisatreur + ' : </label>'
				+ '<input class="input-login" id="imp-login" type="text" name="imp-login" size="32" maxlength="45" value="" autocomplete="off"></div>'
				+ '<div><label for="imp-password" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> ' + importsMdp + ' : </label>'
				+ '<input class="input-password" id="imp-password" type="password" name="imp-password" size="32" maxlength="45" value="" autocomplete="off" /></div>'
				+ '<label for="imp-file" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> ' + importsCheminFichier + ' : </label>'
				+ '<input class="input-file" id="imp-file" type="text" name="imp-file" value="" size="32" maxlength="255" />'
				+ '<p class="field-helper">' + importMaxSize + '</p></div>'
			;
			break;
		case 'file':
			html = '<label for="imp-file" class="input-file-trigger" tabindex="0"><span class="mandatory">*</span> ' + importsSelectionFichier + ' :</label>'
				+ '<input class="input-file" id="imp-file" type="file"  name="imp-file" value=""><span class="color-red"> (Max : '+container.data('maxupload')+' Mo)</span>';

			html = '<p>' + importDragAndDropText + '</p>'
				+ '<div class="box">'
				+ 	'<input class="box__file" type="file" name="imp-file" id="imp-file" />'
				+ 	'<label for="imp-file"><span>' + importsSelectionFichier + '</span></label>'
				+	'</div>'
				+	'<div class="box__uploading">' + importEnCours + '</div>'
				+	'<div class="box__success">' + importSuccess + '</div>'
				+	'<div class="box__error">' + importError + '<span></span>.</div>'
				+ '<p class="field-helper">' + importMaxSize + '</p>'
			break;
		default:
			break;
	}

	container.html(html);
	if (value === 'file') {
		$('.box').addClass('has-advanced-upload');
		initDragAndDrop();
	}

	if (value == 'ftp' || value == 'url') {
		$('.imp-auto').show();
		$("input[name=imp-auto]").trigger("click");
	} else {
		$('.imp-auto').hide();
		$('.imp-periods').hide();
	}
};

/**
 *
 * @param {*} event
 */
function togglePeriods( event ){
	if( event.target.value == '1' ){
		$('.imp-periods').show();
	}else{
		$('.imp-periods').hide();
	}
};

/**
 * Cette fonction permet de vérifier les données du tableau et d'afficher les erreurs s'il y en a
 * @param {*} event
 */
function formSubmitionValidation( event ){
	event.preventDefault();
	//initialisation des variables
	var $form = $('.imp-form'),
		$notif  = $( ".notif" ),
		error = [],
		ddFile = false,
		formData = $form.serialize().split('&');

	// mise en forme des données du formulaire
	var data = formData.reduce(function(d,v){
		var tmp = v.split('=');
		d[tmp[0]] = tmp[1];
		return d;
	},{});

	if( data['imp-class'] == 0 ){
		error.push(importsErreurContenu);
	}

	if( data['imp-method'] == '' ){
		error.push(importsErreurMethodeRecuperation);
	}

	var $fileInput = false;
	switch( data['imp-method'] ){
		case 'url':
			if( data['imp-url']  == '' ){
				error.push("une url");
			}
			break;
		case 'ftp':
			if( data['imp-file']  == '' ){
				error.push(importsErreurNomFichier);
			}
			if( data['imp-url']  == '' ){
				error.push(importsErreurUrl);
			}
			if( data['imp-login']  == '' ){
				error.push(importsErreurUtilisateur);
			}
			if( data['imp-password']  == '' ){
				error.push(importsErreurMdp);
			}
			break;
		case 'file':
			ddFile = checkFileImport();
			$fileInput = $( "#imp-file" );
			if( $fileInput.val() == '' && ddFile === false ){
				error.push(importsErreurFichierImporter);
			}
			break;
	}

	if( data['imp-method']  == 'url' || data['imp-method']  == 'ftp' ){
		if( data['imp-auto'] == '1' ){
			if( data['imp-period'] == ''){
				error.push(importsErreurPeriodeRecurrence)
			}
		}
	}

	// check si il y a bien un séparateur
	// sinon check si il y a un text séparateur
	if( typeof data['imp-separator'] === 'undefined' ){
		error.push(importsErreurSeprateur);
	}
	else if( data['imp-separator'] == 'other' && data['imp-separator-other-text'] === ''){
		error.push(importsErreurCaracteresSeparateur);
	}
	if( data['imp-action'] == 0 ){
		error.push(importsErreurActionImport);
	}

	// check si il n'y a a pas d'erreur et on envoi le formulaire
	// sinon on affiche les erreurs
	if( error.length === 0 ){
		var formdataObj = new FormData($form[0]);
		if (ddFile) {
			formdataObj.append($fileInput.attr('name'), ddFile )
		}

		// Récupère un complément d'information sur le choix de l'import (ex. distinguer un import de tarifs d'un import de promotion - il y a un data-info sur l'option du select)
		if( typeof $('#imp-class option:selected').data('info') != 'undefined' ){
			formdataObj.append('info', $('#imp-class option:selected').data('info'));
		}

		$('.message-ajax-opacity').html('<div><strong>' + importsTransfertEnCours + '</strong></div><div style="height: 10px;background-color:  #fff;"><div class="progressBar" style="width: 0%;height: 10px;background-color: #2196F3;"></div></div>').show();
		$('.load-ajax-opacity').show();

		$.ajax({
			type: "POST",
			url: '/admin/ajax/tools/imports/import.php',
			processData: false,
			contentType: false,
	    	dataType: 'json',
			data:  formdataObj,
			xhr: function () {
				var xhr = $.ajaxSettings.xhr();
				xhr.upload.onprogress = function (e) {
					// For uploads
					if (e.lengthComputable) {
						$(".progressBar").width((e.loaded / e.total*100) + "%");
					}
				};
				xhr.upload.onload = function(){
					$('.message-ajax-opacity').html("<strong>" + importsTransfertTermine + "</strong><br />La durée de cette étape peut varier en fonction de la taille de votre fichier, merci de patienter.");
				};
				return xhr;
			}
		}).done(function(data) {
			if (data.result) {
				location.href = data.content.redirect + '?' + $.param(data.content.params)
			}else{
				$('.message-ajax-opacity').hide();
				$('.load-ajax-opacity').hide();
				$notif.html('');
				var ul = $('<ul>');
				$.each(data.content, function(i, item){
					var li = $('<li>').html(item);
					ul.append(li);
				});
				$notif.append(ul).addClass('error');
			}
		})
		.fail(function() {
			$('.message-ajax-opacity').hide();
			$('.load-ajax-opacity').hide();
			$notif.html(importsErreurTelechargement).addClass('error');
		});
	}else{
		$notif.html('');
		var ul = $('<ul>');
		for(var i=0; i<error.length; i++) {
			var li = $('<li>').html(error[i]);
			ul.append(li);
		}
		$notif.append(importsManque + ' : ').append(ul).addClass('error');
	}
};

/** Sur l'écran de mapping de l'import, affiche les messages d'erreurs
 *	@param {string} json Obligatoire, réponse du fichier check-mapping.php
 *	@param {bool} isImport Obligatoire, détermine si l'on est dans un contexte de demande d'importation (true) ou d'enregistrement (false)
 */
function renderMappingErrors( json, isImport ){
	if( $.trim(json) == '' ){
		return;
	}

	// Remet à 0 l'affichage des messages d'erreurs et de succès
	$('#error-all .error, #error-all .success').remove();
	
	// Gestion de l'affichage des différents messages d'erreurs ou de succès
	for( var key in json ){
		var keyInt = parseInt(key);

		if( key === 'success' ){
			$('#error-all').prepend('<div class="success">' + json[key] + '</div>');
		} else if( key === 'all' ){
			var json_errors = json[key];
			
			if( Array.isArray(json_errors) ){
				var errors = json_errors.reduce(function(message, error){
					message += error + '<br/>';
					return message;
				}, '');
			}else if( typeof json_errors == 'object' ){
				var errors = "";
				for( var err_k in json_errors ){
					errors += json_errors[err_k] + '<br/>';
				}
			}
			$('#error-all').append('<div class="error">' + errors + '</div>');
		} else if( isImport && !isNaN(keyInt) ){ // Ces messages n'apparaissent qu'en cas d'importation. Ils sont masqués si enregistrement.
			$('#map-error-' + keyInt).html(json[key]).addClass('error');
		}
	}

	// Si aucune erreur, exécute l'importation
	if( typeof json.import != 'undefined' ){
		if( json.import ){
			execImport(json.is_backup);
		}
	}

	countIncompleteRows();
};

/**
 * Réalise l'importation du fichier (bouton Importer)
 * @param {bool} is_backup si oui ou non il s'agit d'une restauration de sauvegarde
 */
function execImport( is_backup ){

	var imp_id = $('#imp_id').val();

	// Désactive tout les champs du mapping durant l'éxécution d'un import non récurrent
	// Bouton Importer inclus
	if( $('#read_only_after_exec').val() ){
		$('#read_only').val( '1' );
		$('#map-form select').attr('disabled', true);
		$('#map-form input').attr('disabled', true);
	}
	$('#incomplete-field-compl').remove();

	$.ajax({
		url: '/admin/tools/imports/check-mapping.php',
		method:'post',
		data: 'imp_id='+imp_id+'&is_backup='+is_backup+'&import=true&exec=true',
		dataType: 'json',
		success: function(json){
			currentAjax = false;
			if (json !== null) {
				renderMappingErrors( json, true );
			}
			$.ajax({
				url: '/admin/ajax/tools/ajax-import-info.php',
				method:'get',
				data: {imp:imp_id, action:'get-state'},
				dataType: 'json',
				success: function(json){
					var state = $('<span>').text(json.content.label).addClass(json.content.className);
					$('.state').html(state);
				}
			});
		},
		error: function(msg){
		}
	});

};

/**
 * Fonction callback pour le changement du sélecteur de mapping
 */
function mappingReloadOptions(){
	var col = $(this).data('col');

	$('#map-error-'+col).html('').removeClass('error');
	loadOptions($(this), false);
	isSectionVisible();
	if ($(this).val() === 'DEFAULT') {
		$('#row-'+col).addClass('--no-import');
	} else {
		$('#row-'+col).removeClass('--no-import');
	}
}

/** Affiche le nombre de champs incomplets dans un message d'information situé sous le bouton Importer.
 */
function countIncompleteRows() {
	// Initalise les variables contenant l'état de l'import actuel
	var not_selected = 0;
	var not_finished = 0;

	// Itère sur chaque champ pour compter le nombre de champs pour lesquels une correspondance reste à faire
	$('.js-map-select select').each(function(){
		// Si le choix "Ne pas importer", on s'arrête là
		if ($(this).val() == 'DEFAULT' ){
			return;
		}

		// Si aucun choix n'est fait, on le comptabilie et on s'arrête là
		if( $(this).val() === null ){
			not_selected++;
			return;
		}

		// Récupère les infos sur le mapping sauf si le "Ne pas importer" est choisi ou aucun choix de fait
		var mapping = getMapping( $(this) );

		// Si les options du mapping sont absence cela veut dire qu'il n'est pas encore défini
		if( typeof mapping.options == 'undefined' ){
			not_finished++;
		}else{
			// On contrôle que toutes les informations obligatoires sur le mapping sont renseignées
			if( !checkIfValid($(this).data('col'), mapping) ){
				not_finished++;
			}
		}
	});

	// Détermine le texte à afficher en fonction de l'état de l'import
	var text = '';
	var impStyle = ''; // Passe le message en rouge si l'import est incomplet
	if( not_selected > 0 || not_finished > 0 ){
		impStyle = 'color: red';
		if( not_selected <= 0 ){
			// Tous les mapping sont faits
			text = importUncomplete.replace('[nb]', not_finished);

			if( not_finished > 1 ){
				text = importUncompletePlural.replace('[nb]', not_finished);
			}
		}else if( not_finished <= 0 ){
			// Tous les mapping choisi on été complété, mais il en reste à faire
			text = importNotSelected.replace('[nb]', not_selected);

			if( not_selected > 1 ){
				text = importNotSelectedPlural.replace('[nb]', not_selected);
			}
		}else{
			// Il reste des mapping à choisir et à compléter
			if( not_selected > 1 && not_finished > 1 ){
				text = importUncompleteAll1.replace('[nb1]', not_selected);
				text = text.replace('[nb2]', not_finished);
			}else if( not_selected > 1 ){
				text = importUncompleteAll2.replace('[nb1]', not_selected);
				text = text.replace('[nb2]', not_finished);
			}else if( not_finished > 1 ){
				text = importUncompleteAll3.replace('[nb1]', not_selected);
				text = text.replace('[nb2]', not_finished);
			}else{
				text = importUncompleteAll4.replace('[nb1]', not_selected);
				text = text.replace('[nb2]', not_finished);
			}
		}
	}

	$('#incomplete-field-count')
		.html( text ).attr( 'style', impStyle );

	// Active / Désactive le bouton "Importer" en fonction du résultat
	var readonly = $('#read_only').val();
	if( readonly || not_finished > 0 || not_selected > 0 ){
		$('#incomplete-field-compl').html('');
		$('[type="submit"][name="import"]').prop( 'disabled', true );
	}else{
		$('#incomplete-field-compl').html(importMappingIsComplete);
		$('[type="submit"][name="import"]').prop( 'disabled', false );
	}
}

/**
 * cache ou affiche les sections vides
 */
function isSectionVisible(){
	$('#table-map-link, #table-map-action').each(function(){
		if ($(this).find('.--table-map-col').length > 0) {
			$(this).show();
		} else {
			$(this).hide();
		}

		return;
	});

	if ($('#table-map-valid').find('.--table-map-col').length > 0 ){
		$('#table-map-valid .table-map-title').show();
	}else{
		$('#table-map-valid .table-map-title').hide();
	}
}

/**
 * récupère le mapping de l'élément
 * @param {*} el
 */
function getMapping(el){
	var col = el.data('col');
	var currentCode = el.val();
	var mapping = false;

	if (currentCode !== null) {
		// on cherche s'il existe un mapping pour la colonne et s'il est identique à la selection, sinon on prend le fullSchema
		if (col in registeredMapping && currentCode === registeredMapping[col]['code']) {
			mapping = registeredMapping[col];
		} else {
			mapping = fullSchema[currentCode];
			mapping['col'] = col;
		}
	}
	if (typeof mapping !== 'object') {
		var tmp = {};
		var msg = mapping;
		tmp['col'] = col;
		tmp['code'] = msg;
		mapping = tmp;
	}
	return mapping;
}

/** Affiche les options de correspondance spécifiques au champ RiaShop sélectionné
 * @param {*} el Champ de mapping à mettre à jour
 * @param {*} moveSection Faut-il changer l'élément de section
 */
function loadOptions( el, moveSection ){

	var col = el.data('col');
	var currentCode = el.val();
	var mapping = false;

	// Si aucun choix alors on déplace la zone dans la section "à établir"
	if( currentCode === null ){
		if( moveSection ){
			moveToSection(col, 'link');
		}
	}else{
		optionsLoading(col,true);
		mapping = getMapping(el);

		// rendu des options
		renderOptions(mapping);
		optionsLoading(col,false);
		renderOptionDesc(col, mapping);

		$("#map-option-"+col).find("select[data-name=fld-id]").trigger('change');
		hideOptionByAction(col);

		if ($("#map-option-"+col+ ' .--column-action-select').html() === '') {
			// il n'y a pas d'options
			$("#map-option-"+col).hide();

			if( moveSection ){
				moveToSection(col, 'valid' );
			}
		} else {
			$("#map-option-"+col).show();

			var readOnly = $('#read_only').val();
			if( moveSection ){
				// si la ligne est valide
				if ( readOnly || checkIfValid(col, mapping)) {
					moveToSection(col, 'valid' );
				} else {
					moveToSection(col, 'action' );
				}
			}
		}
	}
}

/**
 * Contrôle la ligne de correspondance fichier/RiaShop pour déterminer si la ligne est valide
 * @param {*} col
 * @param {*} opt
 */
function checkIfValid( col, opt ) {
	var isValid = true;
	var actionsOptions = opt.options

	// pour chaque options potentielle
	for (var i = 0; i < actionsOptions.length; i++) {
		var el = actionsOptions[i];
		var valid = false;
		// on vérifie si le champ est obligatoire
		if (el.mandatory) {
			// on vérifie si le champ est renseigné
			var field = $('#map-option-'+ col + ' .map-option[data-name="'+ el.name +'"]' );

			switch (el.type) {
				case "checkbox":
					// est ce que c'est coché ?
					field.find('input[type="checkbox"]').prop('ckecked') === true ? valid = true : null;
					break;
				/*case "radio":
					// est ce qu'il y a une valeur de selectionnée ?
					// field.find('input[type="checkbox"]').prop('ckecked') === true ? valid = true : null;
					break;*/

				case "select":
					// est ce que quelque chose est selectionné ?
					valid = field.find('select').val();
					break;

				case "text":
					// est ce que le champ est rempli ?
					valid = field.find('input[type="text"]').val() !== '';
					break;

				case "label":
					var one_noempty = false;
					$('#map-option-' + col + ' [type="text"]:visible').each(function(){
						if( $.trim($(this).val()) != '' ){
							one_noempty = true;
							return;
						}
					});

					if( one_noempty ){
						valid = true;
					}
					break;

				case "radio":
				case "select_input":
				case "href":
				default:
					valid = true;
					break;
			}
		} else {
			valid = true;
		}

		// Pour les champs étant lié à l'adresse de facturation ou de livraison, il faut que le choix déterminant si la valeur est pour l'adresse de
		// facturation et/ou l'adresse de livraison soit renseigné
		if( $('[name="mapping[' + col + '][options][inv_adr]"]').length ){
			if( !$('[name="mapping[' + col + '][options][inv_adr]"]:checked').length && !$('[name="mapping[' + col + '][options][dlv_adr]"]:checked').length ){
				valid = false;
			}
		}

		// si un seul n'est pas valide, la ligne n'est pas valide.
		if( !valid ){
			isValid = false;
		}
	}

	// Modifie le style de la ligne en fonction de son état
	const line = $('#row-'+col);
	if( isValid ){
		line.removeClass('--warning').addClass('--valid');
	}else{
		line.removeClass('--valid').addClass('--warning');
	}

	return isValid;
}

/**
 * permet de récupérer le profil par défaut
 * @param {*} select
 * @param {*} map
 */
function ajaxGetDefault(select, map){

	var imp_id = $('#imp_id').val();
	$.ajax({
		url: '/admin/ajax/tools/ajax-import-mapping.php',
		data: 'map='+map+'&imp_id='+imp_id+'&default=1',
		method: 'get',
		dataType : 'json',
		success: function(data){

			document.getElementById(select.attr("id")).value = data;
		}
	})
}

/**
 * Vérifie s'il y a une duplication dans les champs importés
 */
function checkDuplications(isAsync){
	$('#error-all .error, #error-all .success').remove();
	var schems = {};

	if( typeof isAsync == 'undefined' ){
		isAsync = true;
	}

	$(".js-map-select select").each(function(k,el){
		if( el.value.trim() != '' ){
			var key = el.dataset['col'];
			var val = el.value;

			if( $("#map-option-" + key + " select").children("option:selected").val() != null ){
				val += '-'+$("#map-option-" + key + " select").children("option:selected").val();
			}

			// Les champs correspondant à une adresse client peuvent être présent 2 fois ( adresse de facturation et adresse de livraison)
			if( $("#map-inv_adr-" + key + "-0").is(':checked') ){
				val += '-inv_adr';
			}
			if( $("#map-dlv_adr-" + key + "-1").is(':checked') ){
				val += '-dlv_adr';
			}

			schems[key] = val;
		}
	});

	// Réinitialise les messages d'erreurs liés à la duplication de champs
	$('.temp-error-duplicate').text('').removeClass('error');

	$.ajax({
		url: '/admin/ajax/tools/ajax-import-mapping.php',
		data: {schemas:schems},
		dataType: 'json',
		method: 'POST',
		async: isAsync,
		success: function(data){
			if( data != null ){
				$.each(Object.keys(data),function(k,pos){
					if($('#map-'+pos).find(':selected').prop('disabled')){

					} else {
						if ($('#map-' + pos).val() != 'DEFAULT' ){
							// on affiche pas le message si on est sur "selectionner une correspondance"w
							$("#map-error-"+pos).text(importsErreurAssociationUtilise).css('color', 'red').addClass('error temp-error-duplicate');
						}
					}
				});
			}

			countIncompleteRows();
		},
		error: function(){
		}
	})
}

/**
 * récupère les champs et les ajoute au dom
 * @param {*} event
 */
var ajaxGetFld = function(event){
	var cls_id = $('#cls_id').val();
	var imp_id = $('#imp_id').val();
	var field = $(event.target);
	var fld_id = field.val();

	// Si le choix est de ne pas importer la colonne, on n'exécute pas la requête Ajax
	if( fld_id == 'DEFAULT' ){
		return true;
	}

	var col = field.data('col');
	var label = $('#row-'+col+' input[name="mapping['+col+'][name]"').val();
	var data = 'map='+fld_id+'&col='+col+'&cls_id='+cls_id+'&imp_id='+imp_id+'&label='+label;
	// var data = 'map='+fld_id+'&col='+col+'&cls_id='+cls_id+'&imp_id='+imp_id+'&label='+'&fld_id='+fld_id;
	var $options = $('#map-option-'+col +' .--column-action-select');
	optionsLoading(col,true);
	$.ajax({
		url: '/admin/ajax/tools/ajax-import-mapping.php',
		data: data,
		method: 'get',
		dataType: 'json',
		success: function(data){
			optionsLoading(col,false);
			$options.find('.map-option').remove();
			renderOptions(data);
		},
		error:function(){
			optionsLoading(col,false);
		}
	});
};

/**
 * Cette fonction permet d'afficher ou non une ligne commme en chargement
 * @param {*} col
 * @param {*} is_loading
 */
function optionsLoading( col, is_loading ){
	if(!$('#read_only').val()){ //Laisse les champs en disabled si l'import est en mode read_only
		if( is_loading ){
			$('#row-'+col).addClass('loading');
		}else{
			$('#row-'+col).removeClass('loading');
		}
		$('#map-'+col).prop('disabled', is_loading);
		$('#map-option-'+col).find('select').each(function(k,v){$(v).prop('disabled', is_loading);});
		$('#map-option-'+col).find('input').each(function(k,v){$(v).prop('disabled', is_loading);});
		$('#map-option-'+col).find('textarea').each(function(k,v){$(v).prop('disabled', is_loading);});
	}
};

/**
 *
 * @param {*} col
 */
var hideOptionByAction = function(col){
	var action = $('select[data-name=rel-action][data-pos='+col+']');

	if( action.val() == 0 ){
		action.parent().find('div[data-name=sep]').hide();
		action.parent().find('div[data-name=rel-id]').hide();
		action.parent().find('div[data-name=cat-first]').hide();
	}else{
		action.parent().parent().find('div[data-name=sep]').show();
		action.parent().parent().find('div[data-name=rel-id]').show();
		action.parent().parent().find('div[data-name=cat-first]').show();
	}
};

/**	Cette fonction permet d'afficher les options de correspondance (une ligne de correspondance fichier / riashop),
 * 	telle que le fait d'utiliser ce champ comme identifiant, ou des correspondances entre les valeurs du fichier et RiaShop
 *	@param {*} data Informations sur la ligne
 */
var renderOptions = function(data){

	var $options = $('#map-option-'+data.col +' .--column-action-select');
	$('#map-option-'+data.col +' > div').html('');

	var is_action = false;
	var sep_mandatory = true;

	if ( data.options ){
		$.each(data.options, function(k,option){
			if( !(option.type == 'select_input' && (option.selected == 'inv_adr' || option.selected == 'dlv_adr')) ){

				var name = option.name.split(',').reduce(function(v,b){return v.concat('['+b.trim()+']')},'');

				var divClass = 'map-option fld-option';
				if( option.type == 'select_input' || option.type == 'label' || option.type == 'href' ){
					divClass = 'map-options-client';
				}

				var row = $('<div>')
							.addClass(divClass)
							.attr({'data-name': option.name});
				if( option.name === 'rel-action' ){
					is_action = true;
				}
				if( is_action && (option.name === 'sep' || option.name === 'rel-id') || option.name === 'cat-first' || (option.name === 'include-promo' && option.show !=1 )){
					row.hide();
				}

				var input = '';
				var input2;
				var buttonAddDell;
				var label;
				var matches;
				var endmatches;
				var href;
				var nameInput;

				if(option.label != ''){
					// Création du label
					label = $('<label>')
								.attr('for','map-'+option.name+'-'+data.col+'-'+k)
								.attr('name', 'label')
								.attr('title', option.title)
								.text(option.label.replace(':', ''));


					if( typeof option.selected !== 'undefined' && $.inArray(option.selected, notMandatorySep) > -1 ){
						sep_mandatory = false;
					}

					if(option.name == 'sepPayment'){
						label.addClass('sepPayment')
					}
					label.append(' :')

					// Ajoute le marqueur de champ obligatoire (* rouge)
					if( typeof option.mandatory != 'undefined' && option.mandatory ){
						var span = $('<span>')
										.addClass('mandatory')
										.text(' * ');
						if( option.name == 'sep' && !sep_mandatory){
							span.hide();
						}
						label.prepend(span);
					}

				}

				switch( option.type ){
					case 'select':
						input = $('<select>');

						var firstOpt = true;
						$.each(option.value, function(val, txt){
							var value = $('<option>').val(val).text(txt);
							if( typeof option.selected !== 'undefined' && option.selected === val){
								value.attr('selected', 'selected');
							}
							input.append(value);

							if( firstOpt ){
								value.attr('disabled', 'disabled');
								firstOpt = false;
							}
						});

						if($('#read_only').val()){
							input.attr('disabled', true);
						}

						break;
					case 'radio':
						input = $('<input>')
							.attr('type', 'radio')
							.addClass('radio')
							.val(option.value);
						if( option.value == 1 ){
							have_id = true;
							input.attr('checked', true);
						}
						if($('#read_only').val()){
							input.attr('disabled', true);
						}
						break;
					case 'checkbox':
						var classe = '';
						if(option.name == 'promo'){
							classe = 'promo';
						}
						input = $('<input>')
							.attr('type', 'checkbox')
							.addClass('checkbox ' + classe)
							.val(option.value);
						if( option.value == 1 ){
							input.attr('checked', true);
						}
						if($('#read_only').val()){
							input.attr('disabled', true);
						}
						break;
					case 'select_input':
						// Ce block permet de créer le DOM permettant à l'utilisateur de saisir ses correspondance de valeur
						// Exemple : "Mr" dans le fichier est égal à "Monsieur" dans RiaShop

						label="";

						// Initalise l'élément select du DOM
						input = $('<select>')
							.attr("class" , "select-client")
							.change(changeNameInput);

						// Si l'import est en lecture seul, le select sera en "disabled"
						if($('#read_only').val()){
							input.attr('disabled', true);
						}

						// Création des différentes options du select
						var firstOpt = true;
						$.each(option.value, function(val, txt){
							var value = $('<option>').val(val).text(txt);

							// Sélectionne la valeur précédement choisie
							if( typeof option.selected !== 'undefined' && option.selected == val){
								value.attr('selected', 'selected');

								if(option.selected != 0){
									nameInput = 'mapping['+data.col+'][options][vals]['+val+']';
								}
							}else{
								// Si aucune valeur n'a été précédement choisie, la premier est marquée "selected" car tout de suite après, elle est marquée "disabled"
								// Si "selected" est retiré, l'utilisateur ne verra pas le texte "Choisir ...."
								// En la mettant "disabled" cela guide l'utilisateur sur le fait qu'il doit absolument choisir une valeur
								if( firstOpt ){
									value.attr('selected', 'selected');
								}
							}

							input.append(value);

							// La première valeur est mise en "disabled"
							if( firstOpt ){
								value.attr('disabled', 'disabled');
								firstOpt = false;
							}
						});

						//création de l'input
						input2 = $('<input>')
							.attr('type', 'text')
							.attr('data-name', option.name.replace(',','-'))
							.attr('data-pos',data.col)
							.addClass('input-client')
							.val(option.valueInput);

						if(nameInput != ""){
							input2.attr('name', nameInput);
						}

						if($('#read_only').val()){
							input2.attr('disabled', true);
						}

						// Création du bouton de suppression de la correspondance
						if(!$('#read_only').val()){
							buttonAddDell = $('<input>')
							.attr('type', 'button')
							.attr('value', '-')
							.attr('class', 'buttonAddDell')
							.attr('name', 'buttonDell')
							.attr('title', 'Supprimer une correspondance')
							.click(function(){this.parentNode.remove();});
						}

						matches = "<p>" + importLinkVals + "</p>";
						endmatches = "<p>" + importLinkFile + "</p>";
						break;
					case 'label':
						break;
					case 'href':
						label="";
						if(!$('#read_only').val()){
							href= $("<a />", {
								text : importAddValCorrespondence
								}).click(function(){
									var clone = this.parentNode.previousSibling.cloneNode(true);
									this.parentNode.parentNode.insertBefore(clone, this.parentNode.previousSibling);
									clone.style.display = "block";
									clone.lastChild.onclick=function(){this.parentNode.remove();};
									var x = document.getElementsByClassName("select-client");
									for (var i = 0; i < x.length; i++) {
										x[i].onchange= changeNameInput;
									}
								});
						}
						break;
					case 'input':
						input = $('<input>')
							.attr('type', 'text')
							.addClass(option.class)
							.val(option.value);
						break;
					case 'password':
						input = $('<input>')
							.attr('type', 'password')
							.addClass(option.class)
							.val(option.value);
						break;
					default:
						input = $('<input>')
							.attr('type', 'text')
							.addClass('option-sep')
							.val(option.value);
						if($('#read_only').val()){
							input.attr('disabled', true);
						}
						break;
				}

				if (option.name === 'fetching.type') {
					input.on('change', function() {
						var $options = $(this).parents('.map-option');
						var values = ['url', 'host', 'login', 'path', 'password'];
						$.each(values, function(i, value){
							$('.map-option[data-name="fetching.value.'+value+'"]', $options).hide();
						});
						switch(this.value){
							case 'file':
								break;
							case 'url':
								$('.map-option[data-name="fetching.value.url"]', $options).show();
								break;
							case 'ftp':
								var to_display = ['host', 'login', 'path', 'password'];
								$.each(to_display, function(i, value){
									$('.map-option[data-name="fetching.value.'+value+'"]', $options).show();
								});
								break;
						}
					});
				}
				if( input!=="" ){
					input.attr('id', 'map-'+option.name.replace(',','-')+'-'+data.col+'-'+k)
						.attr('name', 'mapping['+data.col+'][options]'+name)
						.attr({'data-name': option.name.replace(',','-')})
						.attr({'data-pos': data.col})
						.change( countIncompleteRows );
				}

				// Ajout d'un label devant les élements "L'adresse de facturation"
				if( option.name === 'inv_adr' ){
					row.append('<div><span class="mandatory">*</span> ' + importUseTo + '</div>');
				}

				// Ajout d'un "i" notice devant les éléments de type "identifiant d'object"
				if( typeof option.is_obj_id != 'undefined' && option.is_obj_id ){
					if( typeof option.desc_id != 'undefined' && $.trim(option.desc_id) != '' ){
						row.append('<div id="map-option-desc-id-' + data.col + '" class="option-notice"></div><span class="info-click"></span>');
					}
				}

				row.append(label).append(input).append(matches).append(input2).append(endmatches).append(buttonAddDell).append(href);
				$options.append(row);
				if (option.class == 'hidden') {
					row.hide();
				}

				// Créer un clone du select en display:none afin de pouvoir à nouveau cloner le select à chaque ajout de correspondance
				if( option.type == "href" ){
					var clone = row.prev().clone();
					//Si il n'y a aucune correspondance de chargé on supprime la ligne de correspondance
					if(option.remove == true){
						row.prev().remove();
					}
					clone.insertBefore(row);
					clone.find('.input-client').val("");
					clone.find('.input-client').removeAttr("name");
					clone.css("display", "none");

					// Supprime la sélection de valeur sur le clone pour lui mettre la première
					clone.find('select option:selected').removeAttr("selected");
					clone.find('select option:first').attr('selected', 'selected');
				}

				if( option.selected=="classmt" ){
					$('#map-'+option.name.replace(',','-')+'-'+data.col+'-'+k).parent().after(
						'<div class="map-option" data-name="sep">'
							+'<label for="map-sep-'+data.col+'-2" name="label">' + importsSeparateur + '</label>'
							+'<input type="text" class="option-sep" id="map-sep-'+data.col+'-2" name="mapping['+data.col+'][options][sep]" data-name="sep" data-pos="'+data.col+'" value="'+option.separator+'">'
						+'</div>');
				}

				if( option.desc_id && option.desc_id.trim() !== '' ){
					var notice = $("<p>").text(option.desc_id);
					$('#map-option-desc-id-'+data.col).append(notice);
				}
			}
		});
	}

	// Gestion des descriptions sur les éléments du mapping
	$('#map-option-desc-'+data.col).html("");

	if( data.desc && data.desc.trim() !== '' ){
		$('#row-'+data.col + ' .--column-destination').addClass('--has-notice');
		var notice = $("<p>").text(data.desc);
		$('#map-option-desc-'+data.col).append(notice);
	}

	// Dès qu'un identifiant est choisi, le message d'information sur son caractère obligatoire disparaît
	$('input:radio:not([name="imp-separator"])').click(function() {
		$('#identifiant').hide();
		$(noticeSiret).removeClass("notice").html("");
	});

	// Cette fonction est appelée lors que l'on modifie le select de correspondance (ex : Civilités).
	// Elle met a jour le nom de l'input text qui permet la saisie des correspondances.
	// Pour gagner en efficacité, on pré-renseigne la 1ère correspondance automatique dans le champ texte
	// cela limite énormément la saisie
	function changeNameInput() {
		if(this.options[this.selectedIndex].value == "0"){
			this.nextSibling.nextSibling.removeAttribute("name");
		}else{
			this.nextSibling.nextSibling.setAttribute("name",'mapping['+this.getAttribute('data-pos')+'][options][vals]['+this.options[this.selectedIndex].value+']');
			$(this.nextSibling.nextSibling).val( this.options[this.selectedIndex].text )
		}
	}

}

/**
 *
 * @param {*} event
 */
var renderperiodsValue = function(event){
	var type = event.target.value;
	$(".imp-period-values").html('');
	var select = $('<select>').attr({name:"imp-period-values"});
	var span = '';
	switch( type ){
		case 'day':
			span =  " " + importsA + " : ";
			for(var i = 0; i<24;i++ ){
				var option = $("<option>").val(i).text(i+"h00");
				select.append(option);
			}
			break;
		case 'week':
			span =  " " + importsLe +" : ";
			const options = {
				1: importsLundi,
				2: importsMardi,
				3: importsMercredi,
				4: importsJeudi,
				5: importsVendredi,
				6: importsSamedi,
				7: importsDimanche
			};
			$.each(Object.keys(options), function(i,k){
				var option = $("<option>").val(k).text(options[k]);
				select.append(option);
			});
			break;
		case 'month':
			span =  " " + importsLe + " : ";
			for(var i = 0; i<31;i++ ){
				var option = $("<option>").val(i+1).text(i+1);
				select.append(option);
			}
			break;
		default:
			select = '';
			break;
	}
	$(".imp-period-values").append($("<label>").text(span) );
	$(".imp-period-values").append(select);

};

/**
 * affiche ou non la description
 * @param {*} col
 * @param {*} option
 */
var renderOptionDesc = function(col, option){
	if( typeof option.options != 'undefined' && option.options.length > 0 ){
		var temp = option.options[0];

		if (typeof temp.desc_id == 'undefined' || $.trim(temp.desc_id) == '' ){
			$('#map-option-desc-id-' + col + ' + .info-click').hide()
		}else{
			$('#map-option-desc-id-' + col + ' > p').html(temp.desc_id)
			$('#map-option-desc-id-' + col + ' + .info-click').show()
		}
	}

	if (option.desc == undefined || option.desc.trim() === "") {
		$('#map-option-desc-'+col + ' + .info-click' ).hide()
	} else {
		$('#map-option-desc-'+col +' > p').html(option.desc)
		$('#map-option-desc-'+col + ' + .info-click' ).show()
	}
}

/**
 * Déplace une ligne de correspondance d'une section vers une autre
 * @param {*} col
 * @param {*} sectionType (link, action, valid)
 */
function moveToSection(col, sectionType){
	// return true;

	// Détermine si l'import est en lecture seule
	var readonly = $('#read_only').val();

	// Si l'import est en lecture seule, toutes les sections hormis "Correspondances trouvées" sont désactivées.
	// Les colonnes du fichier seront toutes mise dans "Non importer"
	if( readonly && (sectionType !== 'valid' || $('#map-' + col).val() == 'DEFAULT') ){
		sectionType = 'noimp';
	}

	var section = $('#table-map-'+sectionType);
	var row = $('#table-map-'+sectionType + ' .--table-map-col');
	var sectionCol = [];
	var el = $('#row-'+col);

	// Applique une classe supplémentaire si le choix est fait de ne pas import la colonne
	if( !readonly ){
		if( $('#map-' + col).val() == 'DEFAULT' ){
			$('#map-' + col).parents('.table-map-row').addClass('--no-import');
		}
	}

	switch (sectionType) {
		case 'link':
				$('#row-'+col).removeClass('--linked --valid --warning');
			break;
		case 'action':
				$('#row-'+col).removeClass('--linked --valid --warning').addClass('--linked');
			break;
		case 'valid':
				$('#row-'+col).removeClass('--linked --valid --warning').addClass('--linked --valid');
			break;
		default:
			break;
	}


	// on ne va pas plus loin si on est déjà dans la bonne section
	if (el.closest('.table-map-section').attr('id') === 'table-map-' + sectionType ) {
		return false;
	}

	// on récupère les id de colonnes déjà présente dans la section de destination.
	row.each(function(){
		sectionCol.push(Number($(this).attr('data-id')));
	});

	// on cherche l'index ou placer la ligne si le tableau n'est pas vide
	var pos = 0;
	if (sectionCol.length !== 0) {
		for (var i = 0; i < sectionCol.length; i++) {
			if ( sectionCol[i] > Number(col) ){
				break;
			}
			pos = i;
		}
	}

	// on retire de la ligne et on place après l'index récupéré ou après le haut du tableau
	animateCSS('#row-'+col, 'fadeOut')

	if (sectionCol.length === 0) {
		el.insertAfter(section.find('.table-map-title'));
	} else {
		row.each(function(index){
			if (pos === index) {
				el.insertAfter($(this));
			}
		});
	}

	animateCSS('#row-'+col, 'fadeIn', function(){});

	// En lecture seule des informations sont retirés dans le tableau des colonnes non importés
	if( readonly ){
		$('#table-map-noimp .--column-action').remove();
		$('#table-map-noimp .--column-destination').html('');

		if( !$('#table-map-noimp .table-map-row').length ){
			$('#table-map-noimp').hide();
		}else{
			$('#table-map-noimp').show();
		}
	}
}

/**
 * Fonction permettant de recharger les barres de progression des imports en cours d'éxécution
 */
function reloadProgressBar(){

	var $imports = $('[data-imp]');

	// S'il n'y a aucun import en cours de traitement
	if( $imports.length==0 && window.location.href=='/admin/tools/imports/index.php' ){
		// Vérifie si il n'y a pas un nouveau import en cours de traitement
		$.ajax({
			url: '/admin/ajax/tools/ajax-progress-update.php',
			data: 'imp=0',
			method: 'get',
			dataType: 'json',
			success: function(data){
				// Si il y a un nouvel import en cours de traitement, ajoute la barre de progression
				if( data != null && data.imp != null ){
					$('#state-'+data.imp).html(data.html);
					$('#state-'+data.imp).addClass('imp-processing');
					updateProgressBar();
				}
			},
		});
	}

	if( $imports.length ){
		// Identifiant de l'import pour lequel on recharge la barre de progression
		$.each( $imports, function( key, imp ) {

			const imp_id = $(imp).attr('data-imp');

			var data = 'imp='+imp_id;
			$.ajax({
				url: '/admin/ajax/tools/ajax-progress-update.php',
				data: data,
				method: 'get',
				dataType: 'json',
				success: function(data){

					// Le lien de téléchargement du rapport n'est défini que s'il y a un rapport à télécharger
					var report_html = '';
					if( data.report ){
						var report_html = '<a href="reports.php?report=' + data.report + '">Télécharger le rapport</a>';
					}

					if( data.updated_state == 'finished' ){
						$('#state-'+imp_id)
							.addClass('imp-success')
							.removeClass('imp-processing')
							.html('<p><strong>'+data.html+'</strong></p>' + report_html);
							updateImportStatus($('#state-'+imp_id), 'finished', data.title );
					}else if( data.updated_state == 'error' ){
						$('#state-'+imp_id)
							.addClass('imp-err')
							.removeClass('imp-processing')
							.html('<p><strong>'+data.html+'</strong></p>' + report_html);
							updateImportStatus($('#state-'+imp_id), 'error', data.title );
					}else if( data.updated_state == 'warning'){
						$('#state-'+imp_id)
							.addClass('imp-warning')
							.removeClass('imp-processing')
							.html('<p><strong>'+data.html+'</strong></p>' + report_html);
							updateImportStatus($('#state-'+imp_id), 'warning', data.title );
					}else{
						$('#state-'+imp_id).html(data.html);
						updateProgressBar();
						$('#state-'+imp_id).closest('.alert').find('h3').html(data.title);
					}
				},
			});
		});
	}
};

function updateImportStatus(el, status, title){
	el.closest('.alert').attr('class', 'alert')
		.addClass('--'+status);
	el.closest('.alert').find('h3').html(title);
}

/**
 * Calcule de l'affichage de la barre de progression
 */
function updateProgressBar(){
	if( $('.bar-progression').length != 0){
		// Nombre de lignes total de l'import
		var maxProgression = parseInt($('.bar-progression').attr('data-progress-max'));
		// Taille maximale de la barre de progression
		var maxWidth = $('.progress-bar').width() - 4;
		// Nombre de lignes traitées de l'import
		var progression = parseInt($('.bar-progression').attr('data-progress'));

		var width = progression / maxProgression * maxWidth;
		$('.bar-progression').width(width+'px');
	}
}

//  PEUT ÊTRE GLOBAL
/**
 * déclenche les animations ajoutées via animateCSS
 * @param {*} element
 * @param {*} animationName
 * @param {*} callback
 */
function animateCSS(element, animationName, callback) {
	var node = document.querySelector(element)
	node.classList.add('animated', animationName)

	function handleAnimationEnd() {
			node.classList.remove('animated', animationName)
			node.removeEventListener('animationend', handleAnimationEnd)

			if (typeof callback === 'function') callback()
	}

	node.addEventListener('animationend', handleAnimationEnd)
}

// Message de confirmation avant de déclencher la suppression d'un import
$(document).on('click', '.unfinished-file-delete.create .btn-delete', function(){
	return window.confirm('Vous êtes sur le point de supprimer un import sauvegardé. \nConfirmez-vous la suppression de cet import ?');
});
$(document).on('click', '.unfinished-file-delete.processing .btn-delete', function () {
	return window.confirm('Vous êtes sur le point de supprimer un import en cours, cette action annulera l\'import et conservera les données qui ont pu être importées. \nConfirmez-vous la suppression ?');
});
$(document).on('click', '.unfinished-file-delete.finish .btn-delete', function () {
	return window.confirm('Vous êtes sur le point de supprimer un import terminé, cette action retirera l\'import de la liste mais les données importées seront conservées. \nConfirmez-vous la suppression de cet import ?');
});

// Retire le message d'erreur lorsqu'on change un information dans le formulaire de création d'un import
$(document).on('blur', '.--start-import input', function(){
	$('.--start-import .error').html('').removeClass('error');
});