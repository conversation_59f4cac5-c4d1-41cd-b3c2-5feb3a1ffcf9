<?php
	// MyPDF_Element
	/*
	*/
	
	abstract class MyPDF_Element {
	
		// attributs
		
			private	$_bold;
			private	$_font;
			private	$_fontSize;
			private	$_italic;
			private	$_parent;
			private	$_root;
			private	$_underline;
		
		// méthodes
		
			// __construct
			/* Constructeur */
			public function __construct($param = array()) {
				if (array_key_exists('bold', $param)) $this->setBold(array('bold' => $param['bold']));
				if (array_key_exists('font', $param)) $this->setFont(array('font' => $param['font']));
				if (array_key_exists('fontSize', $param)) $this->setFontSize(array('fontSize' => $param['fontSize']));
				if (array_key_exists('italic', $param)) $this->setItalic(array('italic' => $param['italic']));
				if (array_key_exists('parent', $param)) $this->setParent(array('parent' => $param['parent']));
				if (array_key_exists('underline', $param)) $this->setUnderline(array('underline' => $param['underline']));
			}
			
			// build
			/* Build */
			public function build($param = array()) {
				$pdf = $param['pdf'];
				
				$t = '';
				if ($this->isBold()) $t .= 'B';
				if ($this->isItalic()) $t .= 'I';
				if ($this->hasUnderline()) $t .= 'U';
				
				$pdf->setFont($this->getFont(), $t, $this->getFontSize());
			}
			
			// getFont
			/* Renvoie la police */
			public function getFont() {
				$font = $this->_font;
				if ($font !== null) return $font;
				return $this->getParent()->getFont();
			}
			
			// getFontSize
			/* Renvoie la taille de la police */
			public function getFontSize() {
				$size = $this->_fontSize;
				if ($size !== null) return $size;
				return $this->getParent()->getFontSize();
			}
			
			// getHeight
			/* Renvoie height */
			public abstract function getHeight();
			
			// getLeft
			/* Renvoie left */
			public abstract function getLeft();
			
			// getParent
			/* Renvoie le parent */
			public function getParent() {
				return $this->_parent;
			}
			
			// getRoot
			/* Renvoie la racine (document) */
			public function getRoot() {
				if (! $this->_root) {
					$parent = $this->getParent();
					$this->_root = ($parent) ? $parent->getRoot() : $this;
				}
				return $this->_root;
			}
			
			// getTop
			/* Renvoie top */
			public abstract function getTop();
			
			// getWidth
			/* Renvoie width */
			public abstract function getWidth();
			
			// hasUnderline
			/* Renvoi si underline */
			public function hasUnderline() {
				$underline = $this->_underline;
				if ($underline !== null) return $underline;
				return $this->getParent()->hasUnderline();
			}
			
			// isBold
			/* Renvoi si bold */
			public function isBold() {
				$bold = $this->_bold;
				if ($bold !== null) return $bold;
				return $this->getParent()->isBold();
			}
			
			// isItalic
			/* Renvoi si italique */
			public function isItalic() {
				$italic = $this->_italic;
				if ($italic !== null) return $italic;
				return $this->getParent()->isItalic();
			}
			
			// setBold
			/* Affecte bold */
			public function setBold($param) {
				$this->_bold = $param['bold'];
			}
			
			// setFont
			/* Affecte la police */
			public function setFont($param) {
				$this->_font = $param['font'];
			}
			
			// setFontSize
			/* Affecte la taille de la police */
			public function setFontSize($param) {
				$this->_fontSize = $param['fontSize'];
			}
			
			// setItalic
			/* Affecte italic */
			public function setItalic($param) {
				$this->_italic = $param['italic'];
			}
			
			// setParent
			/* Affecte le parent */
			public function setParent($param) {
				$this->_parent = $param['parent'];
				return $this;
			}
			
			// setUnderline
			/* Affecte underline */
			public function setUnderline($param) {
				$this->_underline = $param['underline'];
			}
			
			// setWidth
			/* Affecte la largeur */
			public function setWidth($param) {
			}
		
	}

