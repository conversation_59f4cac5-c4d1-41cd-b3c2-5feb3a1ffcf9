<?php

/** \file index.php
 *	Cette page affiche la liste des règles de négociation et permet leur gestion (ajout, modification, suppression)
 */

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CUSTOMER_NEGOTIATION');

// Bouton Ajouter
if( isset($_GET["add"]) ){
	header('Location: /admin/customers/negotiations/new.php');
	exit;
}

$error = false;
if ( isset($_GET["action"], $_GET["gtr_id"], $_GET["usr_value"]) && $_GET["action"] == "delete" && $_GET["gtr_id"]  > 0 && trim($_GET["usr_value"]) != "" ) {
	if (prd_restrictions_del_by_criterion( $usr_fld_id=false, $usr_value=false, null, null, true )) {

		header('Location: /admin/customers/negotiations/new.php');
	}else{
		$error = true;
	}
}

require_once('devices.inc.php');
require_once('users.inc.php');
require_once('profiles.inc.php');
require_once('fields.inc.php');
require_once('prd/restrictions.admin.inc.php');

Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
	->push( $is_yuto_essentiel ? _('Comptes') : _('Comptes clients'), '/admin/customers/index.php' )
	->push( _('Gestion des négociations'), '/admin/customers/negotiations/index.php' );

// Défini le titre de la page
define('ADMIN_PAGE_TITLE', _('Gestion des négociations') . ' - ' . _('Comptes clients'));
require_once('admin/skin/header.inc.php');

require_once('./functions.php');

?>

<h2><?php print _('Gestion des négociations')?></h2>

<?php
	if ($error) {
		print '<div class="notice error">'._('Une erreur empêche la suppression de cette condition').'</div>';
	}
?>

<form action="index.php" method="get">

	<table class="checklist large">
		<thead>
			<tr>
				<th><?php print _('Contexte')?></th>
				<th><?php print _('Règle')?></th>
				<th><?php print _('Actions')?></th>

			</tr>
		</thead>
		<tfoot>
			<tr>
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_NEGOTIATION_ADD') ){ ?>
				<td colspan="3"><input type="submit" name="add" value="<?php print _('Ajouter')?>" title="<?php print _('Ajouter un règle de négociation'); ?>" /></td>
				<?php } ?>
			</tr>
		</tfoot>
		<tbody>
		<?php
			// Recupération des règles de négociations
			$trading_rules = gu_trading_rules_get( -1, null, null, null, 0, false, null, true, array("usr_id" => 'desc') );
			if ($trading_rules != null && ria_mysql_num_rows($trading_rules) > 0) {

				while ($trading_rule = ria_mysql_fetch_assoc($trading_rules)) {
					print '	<tr>
								<td>';
					if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_NEGOTIATION_EDIT') ){
						if( $trading_rule["usr_id"] == 0 ){
							print '	<a href="/admin/customers/negotiations/edit.php?usr_id=0">'._('Tous les utilisateurs').'</a>';
						}elseif( gu_users_exists($trading_rule["usr_id"]) ){
							print '	<a href="/admin/customers/negotiations/edit.php?usr_id='.$trading_rule["usr_id"].'">'._('L\'utilisateur est').' : '.gu_users_get_name($trading_rule["usr_id"]).'</a>';
						}
					}else{
						if( $trading_rule["usr_id"] == 0 ){
							print '	'._('Tous les utilisateurs');
						}elseif( gu_users_exists($trading_rule["usr_id"]) ){
							print '	'._('L\'utilisateur est').' : '.gu_users_get_name($trading_rule["usr_id"]);
						}
					}

					print '		</td>
								<td>';
					if( $trading_rule["total_user_rules"] > 1 ){
						print $trading_rule["total_user_rules"]._(' Règles actives');
					}else{
						print $trading_rule["total_user_rules"]._(' Règle active');
					}

					print '		</td>
								<td>';
					if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_NEGOTIATION_DEL') ){
						print 	'<a class="del button" href="edit.php?action=del_gtr&usr_id='.$trading_rule["usr_id"].'">'._('Supprimer').'</a>';
					}
					print '		</td>
							</tr>';
				}
			print '</tbody>';
		}else{
			print '<tr>
						<td colspan="3">'._('Aucune règle de négociation.').'</td>
					</tr>';
		}

		 ?>
		</tbody>
	</table>
</form>

<div class="notice">
    <p><?php print _('L\'écran ci-dessus liste les règles de négociation qui peuvent être appliquées en fonction du contexte de connexion.')?></p>
    <p>
        <?php print _('Le système fonctionne via des priorités de gestion : lorsqu\'une ou des conditions d\'accès sont définies pour un utilisateur, les conditions d\'accès plus générales ne s\'appliquent plus. L\'ordre de priorité est le suivant (cet ordre ne peut être modifié) :'); ?>
    </p>
    <ol>
        <li><?php print _('Tout le monde (inclut les utilisateurs non connectés)')?></li>
        <li><?php print _('Utilisateur spécifique (code client ou email)')?></li>
    </ol>
</div>

 <?php
		require_once('admin/skin/footer.inc.php');
	?>