<?php
require_once('db.inc.php');
require_once('images.inc.php');
require_once('search.inc.php');
require_once('categories.inc.php');

/** \defgroup model_cat_images Images des catégories produits
 *	\ingroup pim dam
 *	Ce module comprend les fonctions nécessaires à la gestion des images de catégories de produits.
 *	Une ou plusieurs images peuvent être associées à chacunes des catégories.
 *
 *	Cette fonction exploite les directives de configuration suivantes :
 *		- $config['img_dir'] : dossier d'enregistrement des images.
 *		- $config['img_sizes'] : tableau des dimensions de vignettes. Chaque entrée de ce tableau doit contenir les clés width et height.
 *
 *	Le tableau des tailles de vignettes est utilisé comme ceci :
 *	\code
 *		foreach( $config['img_sizes'] as $size )
 *			print $size['width'].' '.$size['height'];
 *	\endcode
 *
 *	Les tailles de vignettes sont utilisées comme des maximums. La génération de vignette conserve les proportions des images.
 *	Il est possible d'utiliser autant de tailles de vignettes que souhaité, mais celle-ci doivent être triées par ordre croissant.
 *
 *	@{
 */

/** Retourne l'ensemble des fichiers images attachés à une catégorie donnée.
 *	Les paramètres $limit et $offset sont ignoré si un identifiant d'image est donné en paramètre
 *
 *	@param int $cat Optionnel, identifiant de la catégorie
 *	@param int $img Optionnel, identifiant de l'image
 *	@param int $limit Optionnel, nombre maximum de résultats à retourner (profondeur maximum à explorer)
 *	@param int $offset Optionnel, offset à partir duquel démarrer le résultat
 *
 *	@return resource Un résultat de requête MySQL comprenant la colonne suivante :
 *			- id : identifiant de l'image
 *			- pos : position de l'image dans la liste des images de la catégorie
 *			- cat_id : identifiant de la catégorie
 *			- alt : texte alternatif de l'image
 *
 */
function prd_cat_images_get( $cat=0, $img=0, $limit=0, $offset=0 ){
	if( !is_numeric($cat) || $cat<=0 ){
		return false;
	}

	if( !is_numeric($img) ){
		return false;
	}

	if( !is_numeric($limit) ){
		return false;
	}

	if( !is_numeric($offset) ){
		return false;
	}

	global $config;

	$sql = '
		select img_id as id, img_pos as pos, img_cat_id as cat_id, img_alt as alt
		from prd_cat_images
		where img_tnt_id='.$config['tnt_id'].'
	';

	if( $cat>0 ){
		$sql .= ' and img_cat_id='.$cat;
	}

	if( $img > 0 ){
		$sql .= ' and img_id='.$img.' limit 0,1';
	}else{
		$sql .= ' order by img_pos asc';

		if( $limit > 0 ){
			if( $offset < 0 ){
				$offset = 0;
			}

			$sql .= ' limit '.$offset.', '.$limit;
		}
	}

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer l'identifiant de la première image d'une catégorie
 *	@param int $cat_id Obligatoire, identifiant d'une catégorie
 *	@return int L'identifiant de la première image, 0 si aucune image ou si le paramètre obligatoire est omis ou faux
 */
function prd_cat_images_get_main( $cat_id ){
	$img_id = 0;

	if( !is_numeric($cat_id) || $cat_id<=0 ){
		return $img_id;
	}

	global $config;

	$r_img = prd_cat_images_get( $cat_id );
	if( $r_img && ria_mysql_num_rows($r_img) ){
		$img = ria_mysql_fetch_assoc( $r_img );
		$img_id = $img['id'];
	}

	return $img_id;
}

/** Permet l'upload d'une image qui sera ensuite ajoutée aux images de la catégorie
 *
 *	@param int $cat Identifiant de la catégorie.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *
 */
function prd_cat_images_upload( $cat, $fieldname ){

	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;
	return prd_cat_images_add( $cat, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );

}

/** Permet l'ajout d'un fichier image à une catégorie de produit. Cette fonction est similaire à prd_cat_images_upload,
 *	a l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile lors d'importations.
 *
 *	@param int $cat Identifiant de la catégorie.
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, nom de l'image source
 *
 *	@return int L'identifiant attribué à l'image.
 *
 */
function prd_cat_images_add( $cat, $filename, $srcname='' ){
	global $config;
	if( !is_numeric($cat) ) return false;

	if( $id = img_images_add( $filename, $srcname ) ){
		$pos = ria_mysql_result(ria_mysql_query('select count(*) from prd_cat_images where img_tnt_id='.$config['tnt_id'].' and img_cat_id='.$cat),0,0);
		ria_mysql_query('insert into prd_cat_images (img_tnt_id,img_id,img_cat_id,img_pos) values ('.$config['tnt_id'].','.$id.','.$cat.','.$pos.')');
		img_images_count_update($id);
		// Mise à jour de la date de modification de la catégorie
		prd_categories_set_date_modified( $cat );
	}
	return $id;
}

/** Permet de mettre a jout le ALT d'une image associée à une catégorie
 *
 *	@param int $cat Identifiant de la catégorie.
 *	@param int $image identifiant de l'image.
 *	@param string $alt Facultatif, alt a placer sur l'image
 *
 *	@return bool True en cas de réussite, False sinon.
 */
function prd_cat_images_set_alt( $cat, $image, $alt='' ){
	global $config;

	if( !is_numeric($cat) && !is_numeric($image) && !($image > 0) && !is_string($alt) ){
		return false;
	}

	if( prd_cat_images_get($cat, $image) ){
		$sql = 'UPDATE prd_cat_images SET img_alt = "'. addslashes($alt) .'" WHERE img_tnt_id = '. $config['tnt_id'] .' AND img_id = '. $image .' AND img_cat_id = '. $cat;
		if ( !ria_mysql_query($sql) ){
			return false;
		}
		// Mise à jour de la date de modification de la catégorie
		prd_categories_set_date_modified( $cat );
	}
	return true;
}

/**	Cette fonction permet la réutilisation d'une image existante pour une catégorie.
 *	@param int $cat Obligatoire, Identifiant de la catégorie.
 *	@param int $img Obligatoire, Identifiant du fichier image.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function prd_cat_images_add_existing( $cat, $img ){
	global $config;
	if( !prd_categories_exists($cat) ) return false;
	if( !img_images_exists($img) ) return false;

	$pos = ria_mysql_result(ria_mysql_query('select count(*) from prd_cat_images where img_tnt_id='.$config['tnt_id'].' and img_cat_id='.$cat),0,0);

	if( !ria_mysql_query('replace into  prd_cat_images (img_tnt_id,img_id,img_cat_id,img_pos) values ('.$config['tnt_id'].','.$img.','.$cat.','.$pos.')') )
		return false;

	// Mise à jour de la date de modification de la catégorie
	prd_categories_set_date_modified( $cat );

	return img_images_count_update($img);
}

/** Permet la suppression d'une ou plusieurs images de catégories.
 *
 *	@param int $cat Identifiant de la catégorie de produits.
 *	@param int $img Optionnel, identifiant de l'image à supprimer. Si ce paramètre est omis, toutes les images de la catégorie seront supprimées.
 *
 */
function prd_cat_images_del( $cat, $img=0 ){
	global $config;
	if( !is_numeric($cat) ) return false;
	if( !is_numeric($img) ) return false;

	$imgs = prd_cat_images_get($cat,$img);
	while( $r = ria_mysql_fetch_array($imgs) ){

		$r_pos = ria_mysql_query('select img_pos from prd_cat_images where img_tnt_id='.$config['tnt_id'].' and img_cat_id='.$cat.' and img_id='.$r['id']);
		if( ria_mysql_num_rows($r_pos) ){
			$pos = ria_mysql_result($r_pos,0,0);
			ria_mysql_query('update prd_cat_images set img_pos=img_pos-1 where img_tnt_id='.$config['tnt_id'].' and img_cat_id='.$cat.' and img_pos>'.$pos);
			ria_mysql_query('delete from prd_cat_images where img_tnt_id='.$config['tnt_id'].' and img_cat_id='.$cat.' and img_id='.$r['id']);
		}
		img_images_count_update($r['id']);
		search_contents_image_del_from_tag(CLS_CATEGORY, $cat, $r['id']);
	}

	// Mise à jour de la date de modification de la catégorie
	prd_categories_set_date_modified( $cat );

	return true;

}



/// @}