<?php

use Riashop\PriceWatching\models\LinearRaised\prw_offers_states;

/**
 * @group offersStatesTest
 * @backupGlobals disabled
 */
class offersStatesCRUDTest extends PHPUnit_Framework_TestCase {
	/**
	 * @dataProvider validOffersStatesProvider
	 */
	public function testAddOffersStates($ofr_id, $ps_id) {
		$result = prw_offers_states::add($ofr_id, $ps_id);
		$this->assertTrue($result);
	}

	/**
	 * @dataProvider invalidOffersStatesProvider
	 */
	public function testFailAddOffersStates($ofr_id, $ps_id) {
		$this->setExpectedException('InvalidArgumentException');
		prw_offers_states::add($ofr_id, $ps_id);
	}

	public function testGetOffersStates() {
		$result = prw_offers_states::get(5);
		$this->assertTrue(ria_mysql_control_ressource($result));
		$this->assertEquals(3, ria_mysql_num_rows($result));
		$assert = array(1,2,4);
		$i = 0;
		while($r = ria_mysql_fetch_assoc($result)) {
			$this->assertArrayHasKey('ofr_id', $r);
			$this->assertArrayHasKey('ps_id', $r);
			$this->assertArrayHasKey('date_created', $r);
			$this->assertEquals($assert[$i], $r['ps_id']);
			$i++;
		}
	}
	/**
	 * @dataProvider invalidOffersStatesArgsProvider
	 */
	public function testFailArgGetOffersStates($arg) {
		$this->setExpectedException('InvalidArgumentException');
		$result = prw_offers_states::get($arg);
	}

	public function testFailFetchGetOffersStates() {
		$result = prw_offers_states::get(9999);
		$this->assertNotTrue(ria_mysql_control_ressource($result));
	}

	public function testFailFetchGetOffersStatesWithPsId() {
		$result = prw_offers_states::get(5, 2);
		$this->assertTrue(ria_mysql_control_ressource($result));
		$this->assertEquals(1, ria_mysql_control_ressource($result));
	}

	public function testDeleteOffersState() {
		$result = prw_offers_states::delete(5);
		$this->assertTrue($result);
		$result = prw_offers_states::get(5);
		$this->assertNotTrue($result);
	}

	public function testDeleteOffersStateByPsId() {
		$result = prw_offers_states::delete(6, 1);
		$this->assertTrue($result);
		$result = prw_offers_states::get(6);
		$this->assertTrue(ria_mysql_control_ressource($result));
		$this->assertEquals(1, ria_mysql_control_ressource($result));
	}

	public function validOffersStatesProvider() {
		return array(
			array(5, 1),
			array(5, 2),
			array(5, 4),
			array(6, 1),
			array(6, 3),
			array(7, 2),
			array(7, 4),
			array(9, 3),
			array(9, 4),
		);
	}

	public function invalidOffersStatesProvider() {
		return array(
			array('test', 1),
			array(5, null),
			array('test', null),
		);
	}

	public function invalidOffersStatesArgsProvider() {
		return array(
			array('test'),
			array(null),
			array(true),
		);
	}
}