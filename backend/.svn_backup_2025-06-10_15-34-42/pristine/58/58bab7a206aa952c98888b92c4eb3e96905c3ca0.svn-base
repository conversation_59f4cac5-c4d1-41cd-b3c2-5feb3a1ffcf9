<?php

	/**	\file edit.php
	 *	Cette page représente la fiche d'une actualité. Elle permet la création, la modification et la suppression.
	 */

	require_once('news.inc.php');
	require_once('websites.inc.php');
	require_once('site.reviews.inc.php');

	//Vérifie que l'utilisateur à bien accès à cette page
	if( isset($_GET['news']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWS_VIEW');
	}elseif( !isset($_GET['news']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWS_ADD');
	}
	//Vérifie que l'utilisateur à bien le droit de modifier / supprimer une actualité
	if( isset($_POST['del']) || isset($_POST['archive']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWS_DEL');
	}
	if( isset($_GET['news']) && $_GET['news'] != 0 ){
		if( isset($_POST['save']) || isset($_POST['savefields']) || isset($_POST['delimg']) || isset($_POST['save-ref']) ){
			gu_if_authorized_else_403('_RGH_ADMIN_TOOL_NEWS_EDIT');
		}
	}
				
	unset($error);

	if( !isset($_GET['archived']) ){
		$_GET['archived'] = false;
	}
		
	// Gestion des onglets
	if( !isset($_GET['tab']) )
		$_GET['tab'] = 'general';
	$tab = $_GET['tab'];
	if( isset($_POST['tabReferencement']) ){
		$tab = 'ref';
	} elseif( isset($_POST['tabImages']) ){
		$tab = 'images';	
	}elseif( isset($_POST['tabDocuments']) ){
		$tab = 'documents';
	}elseif( isset($_POST['tabFields']) ){
		$tab = 'fields';
	}
	if( $tab=='documents' ){
		$tab = 'images';
	}

	$ajax = false;
	if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] == "XMLHttpRequest") { 
		$ajax = true;
	}
	$xml_status='';
	$xml_id='';
	//publication d'un commentaire
	if(isset($_GET['news'], $_GET['publish-comment'])){
		rvw_reviews_publish($_GET['publish-comment']);
		
		if($ajax){
			$xml_status = 1;
			$xml_id = $_GET['publish-comment'];
		}
	}
	
	// Dépublier un commentaire
	if(isset($_GET['news'], $_GET['unapprove-comment'])){
		rvw_reviews_unpublish($_GET['unapprove-comment']);
		
		if($ajax){
			$xml_status = 0;
			$xml_id = $_GET['unapprove-comment'];
		}
	}
	// Enregistrement Ajax du titre / description du commentaire
	if($ajax && isset($_POST['save_comment'], $_POST['title'], $_POST['desc'])){
		
		header("Content-Type: application/xml");
		$xml = '<?xml version="1.0" encoding="utf-8"?>';
		
		if(rvw_reviews_update( $_POST['save_comment'], $_POST['title'], $_POST['desc'])){
			$xml .= '<success/>';
		}else{
			$xml .= '<error/>';
		}
		
		print $xml;
		exit;
	}
	
	// Enregistrement ajax de la réponse du commmentaire
	if($ajax && isset($_POST['save_comment'], $_POST['resp'])){
	
		header("Content-Type: application/xml");
		$xml = '<?xml version="1.0" encoding="utf-8"?>';
		
		if(rvw_reviews_update( $_POST['save_comment'],null,null,null, $_POST['resp']))
			$xml .= '<success/>';
		else
			$xml .= '<error/>';
		
		//envoie d'un email pour signaler a l'utilisateur qu'une réponse vient d'etre posté
		news_reviews_response_send( $_POST['save_comment'] );
		
		print $xml;
		exit;
	}
	
	

	if($ajax){
		header("Content-Type: application/xml");
		$xml = '<?xml version="1.0" encoding="utf-8"?>';
		$xml .= '<result>';
		$xml .= '<reviews id="'.$xml_id.'" publish="'.$xml_status.'" />';
		$xml .= '</result>';
		print $xml;
		exit;
	}
	
	$_GET['wst'] = (isset($_GET['wst']) ? $_GET['wst'] : '0');

	// Vérifie l'identifiant passé en argument
	if( isset($_GET['news']) && $_GET['news']!=0 && !news_exists($_GET['news']) ){
		header('Location: index.php?archived='.$_GET['archived']);
		exit;
	}

	// Bouton Annuler
	if( isset($_POST['cancel']) ){
		header('Location: index.php?archived='.$_GET['archived']);
		exit;
	}

	// Bouton Supprimer
	if( isset($_POST['del']) && isset($_GET['news']) ){
		if( !news_del($_GET['news']) )
			$error = _("Une erreur inattendue s'est produite lors de la suppression de l'actualité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php?archived='.$_GET['archived']);
			exit;
		}
	}
	
	// Bouton Supprimer pour les commentaires
	if( isset($_POST['del_comment'], $_POST['comment']) ){
		foreach($_POST['comment'] as $c)
			if(!rvw_reviews_del($c))
				$error = _("Une erreur inattendue s'est produite lors de la suppression du commentaire.\nVeuillez réessayer ou prendre contact avec l'administrateur.");	
	}

	// Bouton Archiver
	if( isset($_POST['archive']) && isset($_GET['news']) ){
		if( !news_archive($_GET['news']) )
			$error = _("Une erreur inattendue s'est produite lors de l'archivage de l'actualité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php?archived='.$_GET['archived']);
			exit;
		}
	}

	// Bouton Restaurer
	if( isset($_POST['restore']) && isset($_GET['news']) ){
		if( !news_unarchive($_GET['news']) )
			$error = _("Une erreur inattendue s'est produite lors de la restoration de l'actualité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
		else{
			header('Location: index.php?archived='.$_GET['archived']);
			exit;
		}
	}

	// Bouton Enregistrer
	if( isset($_POST['save']) ){
		$wst = wst_websites_get();
		if( !(($wst && ria_mysql_num_rows($wst)>1) || (is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used'])>1)) ){
			$_POST['language'] = array();
			
			while( $r = ria_mysql_fetch_assoc($wst) ){
				$_POST['language'][ $r['id'] ] = array();

				foreach( $config['i18n_lng_used'] as $one_lng ){
					$_POST['language'][ $r['id'] ][] = $one_lng;
				}
			}
		}

		if( !isset($_POST['name']) || !trim($_POST['name']) ){
			$error = _("Veuillez indiquer le titre de l'actualité.");
		}elseif( !isset($_POST['desc']) || !trim($_POST['desc']) ){
			$error = _("Veuillez indiquer le texte de l'actualité.");
		}elseif( is_array($config['i18n_lng_used']) && sizeof($config['i18n_lng_used'])>1 && (!isset($_POST['language']) || !is_array($_POST['language']) || !sizeof($_POST['language'])) ){
			$error = _("Veuillez sélectionner au moins une version de l'actualité");
		}else{
			if( !isset($_POST['publish-date']) ){
				$_POST['publish-date'] = '';
			}

			if( !isset($_POST['intro']) ){
				$_POST['intro'] = '';
			}

			if( !isset($_POST['hour_from']) || trim($_POST['hour_from']) == '' ){
				$_POST['hour_from'] = '00:00';
			}

			if( !isset($_POST['hour_to']) || trim($_POST['hour_to']) == '' ){
				$_POST['hour_to'] = '23:59';
			}

			$date_from = $date_to = '';

			if( isset($_POST['from-opt']) ){
				if( $_POST['from-opt'] == 2 ){
					$date_from = date('d/m/Y 00:00');
				}elseif( $_POST['from-opt'] == 3 ){
					if( !isdate($_POST['publish_date']) ){
						$error = _("La date de publication doit être saisie au format jj/mm/aaaa.\nVeuillez réessayer.");
					}elseif( !ria_is_time($_POST['hour_from']) ){
						$error = _("L'heure de début de publication doit être saisie au format hh:mm.\nVeuillez réessayer");
					}

					if( !isset($error) ){
						$date_from = $_POST['publish_date'].' '.$_POST['hour_from'];
					}
				}

				if( $_POST['from-opt'] != 1 ){
					if( isset($_POST['to-opt']) ){
						if( $_POST['to-opt'] == 3 ){
							if( !isdate($_POST['publish_date_end']) ){
								$error = _("La date de publication doit être saisie au format jj/mm/aaaa.\nVeuillez réessayer.");
							}elseif( !ria_is_time($_POST['hour_to']) ){
								$error = _("L'heure de début de publication doit être saisie au format hh:mm.\nVeuillez réessayer");
							}

							if( !isset($error) ){
								$date_to = $_POST['publish_date_end'].' '.$_POST['hour_to'];
							}
						}
					}
				}
			}

			if( isset($_POST['delimg']) && is_array($_POST['delimg'])){
				foreach($_POST['delimg'] as $img) {
					if(!news_images_del($img, $_GET['news']))
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'actualité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}
			}
			
			if( !isset($error) ){
				if( isset($_GET['news']) && $_GET['news']==0 ){
					if( !($n_id = news_add( $_POST['name'], $_POST['intro'], $_POST['desc'], $date_from, $_POST['categ'], $_POST['allow_comment'], $date_to ) ) )
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'actualité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}elseif( !isset($_GET['lng']) || $_GET['lng']==$config['i18n_lng'] ){
					if( !news_update( $_GET['news'], $_POST['name'], $_POST['intro'], $_POST['desc'], $date_from, $_POST['categ'], $_POST['allow_comment'], $date_to ) )
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'actualité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}elseif( isset($_GET['lng']) && in_array($_GET['lng'], $config['i18n_lng_used']) ){
					$old = ria_mysql_fetch_array( news_get($_GET['news'], false, null, 0, 0, false, false) );
					$values = array(
						_FLD_NEWS_NAME=>$_POST['name'],
						_FLD_NEWS_INTRO=>$_POST['intro'],
						_FLD_NEWS_DESC=>$_POST['desc']
					);
					
					if( !fld_translates_add($_GET['news'], $_GET['lng'], $values) )
						$error = _("L'enregistrement de vos modifications a échoué pour une raison inconnue.\nVeuillez réessayer ou prendre contact pour nous signaler l'erreur.");
					elseif( !news_update( $_GET['news'], $old['name'], $old['intro'], $old['desc'], $date_from, $_POST['categ'], $_POST['allow_comment'], $date_to ) )
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'actualité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				}
				
				$id = ( isset($n_id) ? $n_id : $_GET['news'] ) ;
				if( !isset($error) ){
					// Gestion des liens entres actualités / sites / langues
					news_website_del( $id );
					if( isset($_POST['language']) && is_array($_POST['language']) && sizeof($_POST['language']) ){
						foreach( $_POST['language'] as $wst=>$langs ){
							foreach( $langs as $one_lng ){
								news_website_add( $id, $wst, $one_lng );
							}
						}
					}
				}

				// retire tous les magasins 
				news_store_del( $id );
				// attachement des magasins 
				if( isset($_POST['str']) ){
					if( is_array( $_POST['str'] ) ){
						foreach( $_POST['str'] as $str_id ){
							if( !is_numeric($str_id) || $str_id<=0 ){
								continue;
							}
							
							if( !news_store_add($id, $str_id) ){
								$error = _("Une erreur inattendue s'est produite lors de l'enregistrement des magasins sur l'actualité.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
							}
						}
					}
				}
			}
			if( !isset($error) ){
				foreach($_FILES as $name => $file){
					if($file['error'] != UPLOAD_ERR_NO_FILE){
					
						$nb = news_image_upload($_GET['news'], $name);
						
					}
				}
				
			}
			
			
			if( !isset($error) ){
				header('Location: edit.php?news='.$id.'&archived='.$_GET['archived']);
				exit;
			}
		}
		
	}

	// Bontou Enregistrer Référencement
	if (isset($_GET['news'])) {
		view_admin_tab_referencement_actions( CLS_NEWS, $_GET['news'], (isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng']) );
	}

	// Action sur l'onglet "Avancés"
	if( isset($_GET['news']) && is_numeric($_GET['news']) && $_GET['news'] > 0 ){
		view_admin_tab_fields_actions( CLS_NEWS, $_GET['news'], (isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng']) );
	}
	
	// Réalisation des actions sur l'onglets Documents
	view_admin_tab_documents_actions( CLS_NEWS );

	// Charge l'actualité
	//if( !isset($_GET['categ']) || !is_numeric($_GET['categ']) ) $_GET['categ'] = 0;
	$news = array('id'=>0,'name'=>'','intro'=>'','desc'=>'','publish_date'=>'', 'publish_date_end'=>'', 'hour_from'=>'', 'hour_to'=>'','archived'=>false,'cat_id'=>'', 'allow_comment'=>0, 'tag_title'=>'', 'tag_desc'=>'', 'keywords'=>'', 'url_alias'=>'');

	// Récupère la langue 
	require_once('view.translate.inc.php');
	$lng = view_selected_language();	
	
	if( isset($_GET['name']) ){
		$news['name'] = ucfirst(trim($_GET['name']));
	}
	
	if( isset($_GET['news']) && is_numeric($_GET['news']) && $_GET['news']>0 ){
		$rnews = news_get($_GET['news'],false,$_GET['archived'], 0, 0, false, false);
		if( !$rnews || !ria_mysql_num_rows($rnews) ){
			header('Location: index.php');
			exit;
		}
		$news = ria_mysql_fetch_array($rnews);
		// Récupère les informations traduite
		if( $lng!=$config['i18n_lng'] ){
			$tsk_news = fld_translates_get( CLS_NEWS, $news['id'], $lng, $news, array(_FLD_NEWS_NAME=>'name', _FLD_NEWS_INTRO=>'intro', _FLD_NEWS_DESC=>'desc' ), true );
			$news['name'] = $tsk_news['name'];
			$news['intro'] = $tsk_news['intro'];
			$news['desc'] = $tsk_news['desc'];
		}
	}
	if( isset($_POST['name']) )
		$news['name'] = $_POST['name'];
	if( isset($_POST['intro']) )
		$news['intro'] = $_POST['intro'];			
	if( isset($_POST['categ']) ){
		$news['cat_id'] = $_POST['categ'];
	}elseif( isset($_GET['cat']) && is_numeric($_GET['cat']) && $_GET['cat'] > 0 ){
		$news['cat_id'] = $_GET['cat'];
	}
	if( isset($_POST['desc']) )
		$news['desc'] = $_POST['desc'];
	if( isset($_POST['allow_comment'] ) )
		$news['allow_comment'] = 0;

	
	// Défini le titre de la page
	if( !isset($_GET['news']) || $_GET['news']<=0 ){
		$page_title = _('Créer une actualité');
	}else{
		$page_title = _('Actualité ').htmlspecialchars( $news['name'] );
	}
	define('ADMIN_PAGE_TITLE', $page_title.' - '._('Actualités').' - '._('Outils'));
	require_once('admin/skin/header.inc.php');
?>
	<h2><?php print htmlspecialchars($page_title); ?></h2>
	
	<?php
	
		// Affichage des messages d'erreur et de succès
		if (isset($_SESSION['referencement_edit_success'])) {
			$success = $_SESSION['referencement_edit_success'];
			unset($_SESSION['referencement_edit_success']);
		}
		if (isset($_SESSION['referencement_edit_error'])) {
			$error = $_SESSION['referencement_edit_error'];
			unset($_SESSION['referencement_edit_error']);
		}
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
	
		if( isset($success) ){
			print '<div class="success">'.nl2br(htmlspecialchars($success)).'</div>';
		}
		
		// Affiche le menu de langue
		if( isset($_GET['news']) && $_GET['news']>0 ) {
			print '	<div class="stats-menu">'.
						view_translate_menu( 'edit.php?news='.$news['id'].'&amp;archived='.$_GET['archived'].'&amp;wst='.$_GET['wst'], $lng ).
					'</div>';
		}
			
	?>


	<form action="edit.php?news=<?php print $news['id']; ?>&amp;archived=<?php print $_GET['archived']; ?>&amp;wst=<?php print $_GET['wst']; ?>&amp;lng=<?php print $lng; ?>" method="post" enctype="multipart/form-data" onsubmit="return newsValidForm(this)">
		<ul class="tabstrip">
			<li><input type="submit" name="tabGeneral" value="<?php print _('Général'); ?>" <?php if( $tab=='general' ) print 'class="selected"'; ?> /></li>
			<?php 
				if( isset($_GET['news']) && $_GET['news']>0 ) {
					if( view_admin_show_tab_fields( CLS_NEWS, $news['id'] ) ){
						print '<li><input type="submit" name="tabFields" value="'._('Avancé').'" ' . ($tab=='fields' ? 'class="selected"' : '').'/></li>';
					}
					print '<li><input type="submit" name="tabImages" value="'._('Médiathèque').'" '.( $tab=='images' ? 'class="selected"' : '').'/></li>';
					if( tnt_tenants_have_websites() ){
						print '<li><input type="submit" name="tabReferencement" value="'._('Référencement').'" '.( $tab=='ref' ? 'class="selected"' : '').'/></li>';
					}
				} 
			?>
		</ul>
	</form>
	
	<div id="tabpanel">
	
	<form action="edit.php?news=<?php print $news['id']; ?>&amp;archived=<?php print $_GET['archived']; ?>&amp;wst=<?php print $_GET['wst']; ?>&amp;lng=<?php print $lng; ?>" method="post" enctype="multipart/form-data" onsubmit="return newsValidForm(this)">
	<?php if( $tab=='general' ){ ?>
		<table>
			<tbody>
				<tr>
					<td class="valign-center"><label for="name"><span class="mandatory">*</span> <?php print _('Titre :'); ?></label></td>
					<td><input type="text" name="name" id="name" value="<?php print htmlspecialchars($news['name']); ?>" maxlength="125" /></td>
				</tr>
				<tr>
					<td class="valign-center"><label for="categ"> <?php print _('Catégorie :'); ?></label></td>
					<td><select name="categ" id="categ">
					<?php
						print '<option value="">'._('Veuillez sélectionner une catégorie').'</option>';
						$cat = news_categories_get();
						while( $c = ria_mysql_fetch_array($cat) ){
							print '<option value="'.$c['id'].'" '.( $c['id']==$news['cat_id'] ? ' selected="selected"' : '' ).'>'.htmlspecialchars( $c['name'] ).'</option>';
						}
					?>
					</select></td>
				</tr>
				<tr>
					<td><label for="intro"><?php print _('Introduction :'); ?></label></td>
					<td><textarea name="intro" id="intro" rows="15" cols="40"><?php print htmlspecialchars($news['intro']); ?></textarea></td>
				</tr>
				<tr>
					<td><label for="desc"><span class="mandatory">*</span> <?php print _('Texte :'); ?></label></td>
					<td><textarea class="tinymce" name="desc" id="desc" rows="15" cols="40"><?php print htmlspecialchars($news['desc']); ?></textarea></td>
				</tr>
				<tr>
					<th colspan="2"><?php print _('Publication'); ?></th>
				</tr>
				<?php if( trim($news['url_alias']) != '' ){ ?>
				<tr>
					<td><label for="from-opt-now"><?php print _('Url :'); ?></label></td>
					<td>
						<a href="<?php print $config['site_url'].$news['url_alias']; ?>" target="_blank"><?php print $news['url_alias']; ?></a>
					</td>
				</tr>
				<?php } ?>
				<tr>
					<td><label for="from-opt-now"><?php print _('Publier le :'); ?></label></td>
					<td>
						<input type="radio" class="radio" name="from-opt" id="from-opt-not" value="1" <?php if( !trim($news['publish_date']) ) print 'checked="checked"'; ?> /> <label for="from-opt-not"><?php print _('Ne pas publier pour l\'instant'); ?></label><br />
						<input type="radio" class="radio" name="from-opt" id="from-opt-now" value="2" />
						<label for="from-opt-now"><?php print _('Publier immédiatement'); ?></label><br />
						<input type="radio" class="radio" name="from-opt" id="from-opt-date" value="3" <?php if( trim($news['publish_date']) ) print 'checked="checked"'; ?> />
						<label for="publish_date" onclick="clickPublishFrom(this.form)" onfocus="clickPublishFrom(this.form)"><?php print _('Publier le :'); ?></label>
						<input type="text" class="date datepicker" name="publish_date" id="publish_date" onfocus="clickPublishFrom(this.form)" value="<?php print htmlspecialchars($news['publish_date']); ?>" maxlength="10" />
						<label for="hour_from"><?php print _('à'); ?></label>
						<input type="text" class="hour" name="hour_from" id="hour_from" value="<?php print htmlspecialchars($news['hour_from']); ?>" maxlength="5" />
					</td>
				</tr>
				<tr>
					<td><label for="to-opt-now"><?php print _('Publier jusqu\'au :'); ?></label></td>
					<td>
						<input type="radio" class="radio" name="to-opt" id="to-opt-now" value="2" <?php if( !trim($news['publish_date_end']) ) print 'checked="checked"'; ?> /> <label for="to-opt-now"><?php print _('Publier indéfiniment'); ?></label><br />
						<input type="radio" class="radio" name="to-opt" id="to-opt-date" value="3" <?php if( trim($news['publish_date_end']) ) print 'checked="checked"'; ?> />
						<label for="publish_date_end" onclick="clickPublishTo(this.form)" onfocus="clickPublishTo(this.form)"><?php print _('Publier jusqu\'au :'); ?></label>
						<input type="text" class="date datepicker" name="publish_date_end" id="publish_date_end" onfocus="clickPublishTo(this.form)" value="<?php print htmlspecialchars($news['publish_date_end']); ?>" maxlength="10" />
						<label for="hour_to"><?php print _('à'); ?></label>
						<input type="text" class="hour" name="hour_to" id="hour_to" value="<?php print htmlspecialchars($news['hour_to']); ?>" maxlength="5" />
					</td>
				</tr>
				<?php
					print view_admin_form_websites_languages( CLS_NEWS, $news['id'] );
				?>
				
				<tr>
					<td><?php print _('Autoriser les commentaires :'); ?></td>
					<td>
						<input type="radio" name="allow_comment" class="radio" id="allow_yes" value="1" <?php $news['allow_comment']==1 ? print 'checked="checked"' : '' ?>/><label for="allow_yes"><?php print _('Oui'); ?></label><br/>
						<input type="radio" name="allow_comment" class="radio" id="allow_none" value="0" <?php $news['allow_comment']==0 ? print 'checked="checked"' : '' ?>/><label for="allow_none"><?php print _('Non'); ?></label>
					</td>
				</tr>
			<?php if( isset($news['id']) && $news['id'] ){ ?>
				<tr>
					<th colspan="2"><?php print _('Segmentation'); ?></th>
				</tr>
				<tr>
					<td><?php print _('Segments'); ?></td>
					<td><?php
						$ar_ex_seg = array();
						
						require_once('segments.inc.php');
						print ' <input type="hidden" name="seg-obj-cls" id="seg-obj-cls" value="'.CLS_NEWS.'" />
								<input type="hidden" name="seg-obj-id-0" id="seg-obj-id-0" value="'.$news['id'].'" />
								
								<div class="seg-obj-infos">';
						$robject = seg_objects_get_segments( CLS_NEWS, array($news['id']) );
						if( $robject &&  ria_mysql_num_rows($robject) ){
							while( $obj = ria_mysql_fetch_array($robject) ){
								$ar_ex_seg[] = $obj['id'];
								print '	<input class="del-obj-seg" type="image" name="del-seg-'.$obj['id'].'" src="/admin/images/del-cat.png" title="'._('Retirer ce segment').'" alt="'._('Supprimer').'" />&nbsp;';
								print htmlspecialchars( $obj['name'] ).'<br />';
							}
						} else { print _('Aucune restriction liée aux segments.'); }
						print '	</div>';
					?></td>
				</tr>
				<tr>
					<td class="valign-center"><label><?php print _('Ajouter :'); ?></label></td>
					<td>
						<select class="select-obj-seg" name="segment" id="segment">
							<option value="-1"><?php print _('Choisir un segment'); ?></option>
							<?php
								$rseg = seg_segments_get( 0, CLS_USER );
								if( $rseg && ria_mysql_num_rows($rseg) ){
									while( $seg = ria_mysql_fetch_array($rseg) ){
										if( in_array($seg['id'], $ar_ex_seg) ) continue;
										print '<option value="'.$seg['id'].'">'.htmlspecialchars( $seg['name'] ).'</option>';
									} 
								}
							?>
						</select>
					</td>
				</tr>
			<?php } ?>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save" value="<?php print _('Enregistrer'); ?>" />
					<input type="submit" name="cancel" value="<?php print _('Annuler'); ?>" onclick="return newsCancelEdit(this.form)" />
					<?php 
					if( $news['id']>0 ){ 
						if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_DEL') ){ ?> 
					<input type="submit" name="del" value="<?php print _('Supprimer'); ?>" onclick="return newsConfirmDel()" />
					<?php 
						} 
						if( $news['archived'] ){ 
							if( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_ADD') ){ ?>
							<input type="submit" name="restore" value="<?php print _('Restaurer'); ?>" />
							<?php 
							} 
						}elseif( gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_DEL') ){ ?>
							<input type="submit" name="archive" value="<?php print _('Archiver'); ?>" />
							<?php 
						} 
					} ?>
				</td></tr>
			</tfoot>
		</table>
		
		
		</form>
		
		<?php if( isset($_GET['news']) && $news['allow_comment'] ){ ?>
		<form action="edit.php?news=<?php print $news['id']; ?>&amp;archived=<?php print $_GET['archived']; ?>&amp;wst=<?php print $_GET['wst']; ?>" method="post">
		<table class="checklist" id="actu_reviews">
			<thead>
				<tr>
					<th class="col-check"><input type="checkbox" name="all" value="all" class="check_all_comment"/></th>
					<th><?php print _('Commentaires'); ?></th>
					<th class="col80px"><?php print _('Etat'); ?></th>
					<th class="col80px"><?php print _('Posté le'); ?></th>
				</tr>
			</thead>
			<tbody>
					<?php 
					// permet l'affichage des commentaires
					function show_comment($comment){
						global $padding_left;
						if($comment['publish']==1)
							$state = _('Publié');
						elseif($comment['publish']==-1)
							$state = _('En attente');
						elseif($comment['publish']==0)
							$state = _('Refusé');
						elseif($comment['publish']==-2)
							$state = _('Spam');
						
						print '<tr class="tr-'.$comment['id'].'">
							<td><input type="checkbox" name="comment[]" class="checkbox scomment" value="'.$comment['id'].'" /></td>
							<td class="rcontent" style="padding-left:'.$padding_left.'px">
								<div class="ctitle"><strong>'.$comment['name'].'</strong></div>
								<div class="cdesc">'.nl2br($comment['desc']).'</div>';
								
							print '<div class="rsp_reviews"><small><strong>'._('Votre réponse').' :</strong></small> <div class="cresp">'.nl2br($comment['ans_desc']).'</div></div>';
								
								
						print '	<div class="coptions">
									<span class="redit"><a class="edit" name="cmt-'.$comment['id'].'">'._('Editer').'</a></span>

									<span class="rresponse"><a class="response" name="cmt-'.$comment['id'].'">'._('Répondre').'</a></span>

									
									<span class="approve'.( $comment['publish'] == 1 ? ' none' : '' ).'">
									<a class="checked" href="edit.php?publish-comment='.$comment['id'].'&amp;news='.$_GET['news'].'">'._('Approuver').'</a></span>
									<span class="unapprove'.( $comment['publish'] == 0 ? ' none' : '' ).'">
									<a class="unchecked" href="edit.php?unapprove-comment='.$comment['id'].'&amp;news='.$_GET['news'].'">'._('Désapprouver').'</a></span>
								</div>
							</td>
							<td class="rstate">'.$state.'</td>
							<td>'.$comment['date_created'].'<br/> par <a href="mailto:'.$comment['usr_email'].'">'.$comment['usr_firstname'].' '.$comment['usr_lastname'].'</a></td>
						</tr>';
					}
					$padding_left = 0;
					//permet l'affichage des commentaires fils
					function show_comment_child($comment){
						global $padding_left;
						if(rvw_reviews_childs_exists($comment)){
							$padding_left += 20;
							$comments = rvw_reviews_get( 0, null, null, null, 7, null, $comment,0,0, array('date_created'=>'asc') );
						
							if(ria_mysql_num_rows($comments)>0){
								while($comment = ria_mysql_fetch_array($comments)){
									show_comment($comment);
									show_comment_child($comment['id']);	
								}
							}
							
							$padding_left -= 20;
						}
					}
					
					//affichage du commentaire principale
					$comments = rvw_reviews_get( 0, null, null, null, 7, null, false, $_GET['news'],0, array('date_created'=>'asc') );
					
					if(ria_mysql_num_rows($comments)>0){
						while($comment = ria_mysql_fetch_array($comments)){
						
							show_comment($comment);
							show_comment_child($comment['id']);	
							
						}
					}else{
						print '<tr><td colspan="4">Aucun commentaire</td><tr>';
					}
					
					?>
			</tbody>
			<?php if(ria_mysql_num_rows($comments)>0){ ?>		
			<tfoot>
				<tr>
					<td colspan="4"><input type="submit" name="del_comment" value="<?php print _('Supprimer'); ?>" class="btn-move" /></td>
				</tr>
			</tfoot>
			<?php } ?>
		</table>
		</form>
		<?php } ?>
		
	<?php }elseif( $tab=='ref' ){ 
		print view_admin_tab_referencement(CLS_NEWS, $_GET['news'], $lng);
	?>
	
	<?php } elseif( $tab=='images' ) { 
		print view_admin_img_table( CLS_NEWS, $_GET['news']);
		print view_admin_tab_documents_show( CLS_NEWS, $_GET['news'] ); 
    }elseif( $tab=='fields'){
    	print view_admin_tab_fields( CLS_NEWS, $_GET['news'], $lng, 'edit.php?news='.$_GET['news'].'&amp;tab=fields' );
    }?>
	</div>

	<script>
		// Disable tous les champs/boutons si on accède à cette page en lecture seul
		<?php if( isset($_GET['news']) && $_GET['news'] != 0 && !gu_user_is_authorized('_RGH_ADMIN_TOOL_NEWS_EDIT') ){ ?>
			$(document).ready(function(){
				$('table').find('input, select, textarea').attr('disabled', 'disabled')
				$('table a.edit').remove();
			});
		<?php } ?>

		<?php if( isset($_GET['news']) && $tab='images' && !gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_NEWS') ){ ?>
			$(document).ready(function(){
				$('#table-images').find('input, select, textarea').attr('disabled', 'disabled')
			});
		<?php } ?>
	</script>
	
	<script><!--
		var segID = false;
		var nbGroups = false;
		var nbGroupCdts = false;
		var segClsID = <?php print CLS_NEWS; ?>;

		var old_title ='';
		var old_desc ='';
		var old_resp ='';
		var comment_id ='';
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>