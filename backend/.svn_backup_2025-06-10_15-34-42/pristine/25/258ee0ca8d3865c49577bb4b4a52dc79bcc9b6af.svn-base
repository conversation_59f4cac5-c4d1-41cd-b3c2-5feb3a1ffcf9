<?php

	/**	\file index.php
	 *	Catalogue > Droits d'accès
	 *  Cette page liste les différents types de droits d'accès
	 * 
	 * Le système fonctionne via des priorités de gestion : lorsqu'une ou des conditions d'accès sont définies pour un utilisateur ou un groupe, les conditions d'accès plus  * générales ne s'appliquent plus.	L'ordre de priorité est le suivant (cet ordre ne peut être modifié) :
	 * 
	 * Tout le monde (inclut les utilisateurs non connectés)
	 * Profil de l'utilisateur
	 * Catégorie comptable de l'utilisateur
	 * Catégorie tarifaire de l'utilisateur
	 * Utilisateur spécifique (code client ou email)
	 */

	// Vérifie que l'utilisateur en cours à accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_RIGHT');
	
	require_once('products.inc.php');
	require_once('fields.inc.php');
	require_once('prd/restrictions.inc.php');
	require_once('prd/restrictions.admin.inc.php');
	
	unset( $error );	
	
	// pour la selection de site si tenant multisite
	if(isset($_GET['wst'])){
		if($_GET['wst']=='all'){
			$_SESSION['websitepicker'] = '0';
		}else{
			$_SESSION['websitepicker'] = str_replace('w-','',$_GET['wst']);
		}
	}
	$wst_id = isset($_SESSION['websitepicker']) && $_SESSION['websitepicker']>0 ? $_SESSION['websitepicker'] : $config['wst_id'];
	
	// Bouton Ajouter
	if( isset($_GET['add']) ){
		header('Location: new.php');
		exit;
	}
	
	// permet l'enregistrement des limitations
	if( isset($_POST['save']) ){
		if( !cfg_overrides_set_value('use_catalog_restrictions_usr_price', $_POST['use_catalog_restrictions_usr_price'] == '1' ? 1 : 0, $wst_id) ){
			$error = _('Une erreur est survenue lors de l\'enregistrement des limitations.');
		}
		if ($_POST['use_catalog_restrictions_usr_price'] == "1"){
			if( !cfg_overrides_set_value('catalog_restrictions_type', $_POST['catalog_restrictions_type'], $wst_id) ){
				$error = _('Une erreur est survenue lors de l\'enregistrement du choix du tarif d\'exception.');
			}
		}

		if (!isset($error)){
			header('Location: index.php');
			exit;
		}
	}
	$config['use_catalog_restrictions_usr_price'] = cfg_overrides_get_value( 'use_catalog_restrictions_usr_price', $wst_id);
	
	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Droits d\'accès') . ' - ' . _('Catalogue'));
	require_once('admin/skin/header.inc.php');
	
?>

	<?php if( isset($_GET['add']) ){ ?>
		<div class="success"><?php print _('Ajout de la condition réussie.');?></div>
	<?php } ?>
	<?php if( isset($error) ){ ?>
		<div class="error"><?php print htmlspecialchars($error); ?></div>
	<?php } 
	
		print '<h2>' . _('Droits d\'accès') . '</h2>';
	?>
		<div class="notice">
			<p><?php print _('L\'écran ci-dessous liste les pages du catalogue (marques, catégories, fiches produits) qui peuvent être vues en fonction du contexte de connexion.');?></p>
			<p>
				<?php print _('Le système fonctionne via des priorités de gestion : lorsqu\'une ou des conditions d\'accès sont définies pour un utilisateur ou un groupe, les conditions d\'accès plus générales ne s\'appliquent plus.'); ?>
				<?php print _('L\'ordre de priorité est le suivant (cet ordre ne peut être modifié)').' :'; ?>
			</p>
			<ol>
				<li><?php print _('Tout le monde (inclut les utilisateurs non connectés)');?></li>
				<li><?php print _('Profil de l\'utilisateur');?></li>
				<li><?php print _('Catégorie comptable de l\'utilisateur');?></li>
				<li><?php print _('Catégorie tarifaire de l\'utilisateur');?></li>
				<li><?php print _('Utilisateur spécifique (code client ou email)');?></li>
			</ol>
		</div>
	<?php
		$website = wst_websites_get();
		print view_websites_selector( $wst_id, false, 'riapicker', true );
	?>
		
	<div>
		<?php
		$page = isset( $_GET['p'] ) && is_numeric($_GET['p']) ? $_GET['p'] : 1; 
		$range = 25;
		
		$r_rec_user = prd_restrictions_get_distinct_usr( false, $wst_id );
		$pages = ceil( ( !$r_rec_user ? 0 : ria_mysql_num_rows($r_rec_user) ) / $range );
		?>
		
		<form action="/admin/catalog/authorizations/index.php" method="get">
			<table class="checklist large" id="table-autorizations">
				<thead>
					<tr>	
						<th colspan="2"><?php print _('Contexte');?></th>
						<?php if(ria_mysql_num_rows($website)>1) { ?><th><?php print _('Site');?></th><?php } ?>
					</tr>
				</thead>
				<tfoot>
					<?php if( $r_rec_user && ria_mysql_num_rows($r_rec_user)>$range ){ ?>
					<tr id="pagination">
					<?php 
						print '	<td class="page tdleft">'._('Page').' '.$page.'/'.$pages.'</td>
								<td colspan="'.( ria_mysql_num_rows($website) > 1 ? 2 : 1 ).'" class="pages">';
								if( $pages > 1 ){
									if( $page-1 > 0 ) print '<a class="prev" href="index.php?p='.($page-1).'">« '._('Page précédente').'</a>&nbsp;|&nbsp;';
									for( $i= ( $page-$range < 1 ? 1 : $page-$range) ; $i<=( $page+$range > $pages ? $pages : $page+$range); $i++ ){
										if( $i==$page )
											print '<b>'.$page.'</b>';
										else
											print '<a href="index.php?p='.$i.'">'.$i.'</a>'; 
										if( $i<$pages )
											print ' | ';
									}
									if( $page+1 <= $pages ) print '&nbsp;|&nbsp;<a class="next" href="index.php?p='.($page+1).'">'._('Page suivante').' »</a>';
								}								
						print '	</td>';
					?>
					</tr>
					<?php } ?>
					<?php if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_RIGHT_ADD') ){ // Le bouton Ajouter n'apparaît que si l'on dispose du droit _RGH_ADMIN_CATALOG_RIGHT_ADD ?>
					<tr>
						<td colspan="<?php print ria_mysql_num_rows($website) > 1 ? 3 : 2; ?>"><input type="submit" name="add" value="<?php print _('Ajouter');?>"/></td>
					</tr>
					<?php } ?>
				</tfoot>
				<tbody>
				<?php
					if( $r_rec_user && ria_mysql_num_rows($r_rec_user) ){
						$cpt = 0;
						ria_mysql_data_seek( $r_rec_user, ($range*($page-1)));
						while( $rec_user = ria_mysql_fetch_array($r_rec_user) ){ if( $cpt >= $range ) break;
							$cpt ++;
							?>
							<tr>
								<td colspan="2">
									<a href="edit.php?wst_id=<?php print $rec_user['wst_id'] ?>&amp;usr_fld_id=<?php print $rec_user['fld_id'] ?>&amp;usr_value=<?php print empty($rec_user['value']) ? -1 : $rec_user['value'] ?>"><?php print htmlspecialchars(view_conditions_name( $rec_user['fld_id'], $rec_user['value'] )); ?></a>
								</td>
								<?php if(ria_mysql_num_rows($website)>1) { ?>
									<td>
										<?php print htmlspecialchars( wst_websites_get_name($rec_user['wst_id']) ); ?>
									</td>
								<?php } ?>
							</tr>
							<?php
						}
					}else{
						?>
						<tr>
							<td colspan="<?php print ria_mysql_num_rows($website) > 1 ? 3 : 2; ?>"><?php print _('Aucun accès n\'a été défini.');?></td>
						</tr>
						<?php
					}
				?>
				</tbody>
			</table>
		</form>
		

		<h3><?php print _('Limitations');?></h3>
		<form action="/admin/catalog/authorizations/index.php" method="post">
			<p id="catalog_restrictions_sentence"><?php echo _('Limiter aux tarifs d\'exception, si cette case est cochée, les utilisateurs auront accès seulement aux tarifs d\'exception en plus des règles définies ci-dessus.'); ?></p>
			<input type="radio" value="1" <?php print $config['use_catalog_restrictions_usr_price'] ? 'checked="checked"' : ''; ?> name="use_catalog_restrictions_usr_price" id="use_catalog_restrictions_usr_price-1">
			<label for="use_catalog_restrictions_usr_price-1"><?php print _('Oui')?></label>
			<input type="radio" value="0" <?php print !$config['use_catalog_restrictions_usr_price'] ? 'checked="checked"' : ''; ?> name="use_catalog_restrictions_usr_price" id="use_catalog_restrictions_usr_price-0">
			<label for="use_catalog_restrictions_usr_price-0"><?php print _('Non')?></label>
			<br />
			<div class="catalog_restrictions_type_block">
				<label for="catalog_restrictions_type"><?php echo _('Sélection du tarif d\'exception :'); ?></label>
				<select name="catalog_restrictions_type" id="catalog_restrictions_type">
					<option <?php echo isset($config['catalog_restrictions_type']) && $config['catalog_restrictions_type'] == _FLD_USR_CENTRAL ? 'selected="selected"' : ''; ?> value="<?php echo _FLD_USR_CENTRAL; ?>"><?php echo _('Tarifs produit/utilisateur et Tarifs produit/centrale'); ?></option>
					<option <?php echo isset($config['catalog_restrictions_type']) && $config['catalog_restrictions_type'] == _FLD_USR_PRC ? 'selected="selected"' : ''; ?>  value="<?php echo _FLD_USR_PRC; ?>"><?php echo _('Tarifs produit/Categorie Tarifaire'); ?></option>
				</select>
			</div>
			<p class="mart20"><input type="submit" name="save" value="<?php print _('Enregistrer');?>" /></p>
		</form>
	</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>