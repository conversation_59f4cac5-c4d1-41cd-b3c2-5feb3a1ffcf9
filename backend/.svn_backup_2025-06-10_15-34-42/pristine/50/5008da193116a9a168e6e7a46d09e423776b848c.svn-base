<?php
/** 
 * \defgroup api-search-rebuild Ré-indexation
 * \ingroup search
 * @{			
 *
 * \page api-search-rebuild-add Ajout
 *
 *	 Cette requête permet de programme la ré-indexation de un ou plusieurs types de contenus. Le paramètre "type" (obligatoire) permet de renseigner quels sont les types de contenu à ré-indexer.
 *	 Il est possible de donner plusieurs types en même temps, exemple : type=prd|cat|cms. Voici la liste de tous les types acceptés : 
 *	 	- cgv : Conditions générales de vente
 *	 	- faq : Foire aux questions
 *	 	- dom : Domaines
 *	 	- cms : Pages de contenu
 *	 	- dtp : Types de documents
 *	 	- doc : Documents
 *	 	- msg : Messages
 *	 	- nws : Actualités
 *	 	- str : Magasins
 *	 	- brd : Marques
 *	 	- cat : Catégories
 *	 	- prd : Produits
 *	 	- usr : Comptes clients
 *	 	- ord : Commandes	
 *
 *		\code
 *			POST /search/rebuild/
 *		\endcode
 *	
 * @param $type Obligatoire, liste des types de contenus à ré-indexer
 *	
 * @return true si l'ajout s'est déroulé avec succès 
*/

require_once('search_fields.inc.php');
require_once('cgv.inc.php');
require_once('tools.faq.inc.php');
require_once('domains.inc.php');
require_once('cms.inc.php');
require_once('documents.inc.php');
require_once('messages.inc.php');
require_once('news.inc.php');
require_once('delivery.inc.php');
require_once('brands.inc.php');
require_once('categories.inc.php');
require_once('products.inc.php');
require_once('users.inc.php');
require_once('orders.inc.php');

switch( $method ){
	case 'add':
		if( !isset($_REQUEST['type']) ){
			throw new BadFunctionCallException('Aucun type de renseigné lors de la demande de ré-indexation');
		}

		// Récupère les types de contenus pour lesquels une demande de ré-indexation est faite
		// Contrôle de chaque type pour vérifier qu'il fait partie de la liste prise en charge
		$type = explode('|', $_REQUEST['type']);
		foreach( $type as $one_type ){
			if( !in_array($one_type, array('cgv', 'faq', 'dom', 'cms', 'dtp', 'doc', 'msg', 'nws', 'str', 'brd', 'cat', 'prd', 'usr', 'ord', 'inv')) ){
				throw new BadFunctionCallException('Le type "'.htmlspecialchars($one_type).'" n\'est pas prit en charge pour une demande de ré-indexation.');
			}
		}

		// Pour chaque type, on réalise une demande de ré-indexation de contenu
		foreach( $type as $one_type ){
			switch( $one_type ){
				case 'cgv' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(6);

					// Récupère les différentes version des conditions générales de ventes
					$r_version = cgv_versions_get();

					if( $r_version ){
						while( $version = ria_mysql_fetch_assoc($r_version) ){
							try {
								// Index la version des CGV dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_CGV_VERSION,
									'obj_id_0' => $version['id'],
								));
							} catch (Exception $e) {
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'faq' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(4);
					search_content_md5_reinit(5);

					// récupère les catégories de la foire aux questions
					$r_cat_faq = faq_categories_get();

					if( $r_cat_faq ){
						while( $cat_faq = ria_mysql_fetch_assoc($r_cat_faq) ){
							try{
								// Index la catégorie dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_FAQ_CAT,
									'obj_id_0' => $cat_faq['id'],
								));
							}catch(Exception $e){
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}

						// Récupère les questions de la foire aux questions
						$r_qst_faq = faq_questions_get();
						if( $r_qst_faq ){
							while( $qst_faq = ria_mysql_fetch_assoc($r_qst_faq) ){
								try{
									// Index la question dans le moteur de recherche.
									RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
										'cls_id' => CLS_FAQ_QST,
										'obj_id_0' => $qst_faq['id'],
									));
								}catch(Exception $e){
									throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
								}
							}
						}
					}

					break;
				}
				case 'dom' : {
					// Demande la ré-indexation des domaines dans le moteur de recherche
					prd_domains_index_rebuild();
					break;
				}
				case 'cms' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(10);

					// Récupère les différentes pages de contenu
					$r_cms = cms_categories_get();

					if( $r_cms ){
						while( $cms = ria_mysql_fetch_assoc($r_cms) ){
							try {
								// Index la page de contenu dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_CMS,
									'obj_id_0' => $cms['id'],
								));
							} catch (Exception $e) {
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'dtp' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(13);

					// Récupère les types de documents
					$r_type = doc_types_get();

					if( $r_type ){
						while( $type = ria_mysql_fetch_assoc($r_type) ){
							try {
								// Index le type de document dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_TYPE_DOCUMENT,
									'obj_id_0' => $type['id'],
								));
							} catch (Exception $e) {
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'doc' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(14);

					// Récupère les documents
					$r_document = doc_documents_get();

					if( $r_document ){
						while( $document = ria_mysql_fetch_assoc($r_document) ){
							try {
								// Index le document dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_DOCUMENT,
									'obj_id_0' => $document['id'],
								));
							} catch (Exception $e) {
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'msg' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(16);

					// Récupère les messages à ré-indexer
					$r_message = messages_get();

					if( $r_message ){
						while( $message = ria_mysql_fetch_assoc($r_message) ){
							try{
								// Index le message dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_MESSAGE,
									'obj_id_0' => $message['id'],
								));
							}catch(Exception $e){
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'nws' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(7);

					// Récupère les actualités à ré-indexer
					$r_news = news_get();

					if( $r_news ){
						while( $news = ria_mysql_fetch_assoc($r_news) ){
							try{
								// Index l'actualité dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_NEWS,
									'obj_id_0' => $news['id'],
								));
							}catch(Exception $e){
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'str' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(9);

					// Récupère les magasins à ré-indexer
					$r_store = dlv_stores_get();

					if( $r_store ){
						while( $store = ria_mysql_fetch_assoc($r_store) ){
							try {
								// Index le magasin dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_STORE,
									'obj_id_0' => $store['id'],
								));
							} catch (Exception $e) {
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'brd' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(12);

					// Récupère les marques à ré-indexer
					$r_brand = prd_brands_get();

					if( $r_brand ){
						while( $brand = ria_mysql_fetch_assoc($r_brand) ){
							try{
								// Index la marque dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_BRAND,
									'obj_id_0' => $brand['id'],
								));
							}catch(Exception $e){
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'cat' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(1);

					// Récupère les catégories à ré-indexer
					$r_category = prd_categories_get_all();

					if( $r_category ){
						while( $category = ria_mysql_fetch_assoc($r_category) ){
							try{
								// Index la catégorie dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_CATEGORY,
									'obj_id_0' => $category['id'],
								));
							}catch(Exception $e){
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'prd' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(2);

					// Récupère les produits à ré-indexer
					$r_product = prd_products_get_all();

					if( $r_product ){
						while( $product = ria_mysql_fetch_assoc($r_product) ){
							try{
								// Index le produit dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_PRODUCT,
									'obj_id_0' => $product['id'],
								));
							}catch(Exception $e){
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'usr' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(3);

					// Récupère les comptes à ré-indexer
					$r_user = gu_users_get();

					if( $r_user ){
						while( $user = ria_mysql_fetch_assoc($r_user) ){
							try{
								// Index le produit dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_USER,
									'obj_id_0' => $user['id'],
								));
							}catch(Exception $e){
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'ord' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(8);

					// Récupère les comptes à ré-indexer
					$r_order = ord_orders_get_simple(
						array(), 
						array(), 
						array(), 
						array(), 
						array(
							'type' => 'replace', 
							'columns' => array(
								'ord_id' => 'id', 
								'ord_parent_id' => 'parent_id',
								'ord_piece' => 'piece',
							)
						)
					);

					if( $r_order ){
						while( $order = ria_mysql_fetch_assoc($r_order) ){
							// Seules les commandes validées sont indexées
							{
								if( trim($order['piece']) == '' ){
									continue;
								}

								if( is_numeric($order['parent_id']) && $order['parent_id'] > 0 ){
									continue;
								}
							}

							try{
								// Index le produit dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_ORDER,
									'obj_id_0' => $order['id'],
								));
							}catch(Exception $e){
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
				case 'inv' : {
					// Réinitialise le md5 de ré-indexation
					search_content_md5_reinit(18);

					// Récupère les factures à indexer
					$r_invoices = ord_invoices_get();

					if( $r_invoices ){
						while( $invoice = ria_mysql_fetch_assoc($r_invoices) ){
							try{
								// Index la facture dans le moteur de recherche.
								RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
									'cls_id' => CLS_INVOICE,
									'obj_id_0' => $invoice['id'],
								));
							}catch(Exception $e){
								throw new BadFunctionCallException(__FILE__.':'.__LINE__.' '.$e->getMessage());
							}
						}
					}

					break;
				}
			}
		}
		
		$result = true;

		break;
}

///@}