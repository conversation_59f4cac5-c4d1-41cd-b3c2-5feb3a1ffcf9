<?php
	require_once('delivery.inc.php');
	
	if( !isset($_GET['str']) || !dlv_stores_exists($_GET['str']) ){
		$g_error = _("Le magasin donné en paramètre n'existe pas.")." "._("Veuillez vérifier cette information.");
	}else{
		$str = ria_mysql_fetch_array( dlv_stores_get($_GET['str']) );
	}

	define('ADMIN_PAGE_TITLE', _('Magasin') . ' - ' . _('Informations'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		print '
			<table class="cheklists">
				<caption>'._('Informations sur le magasin').'</caption>
				<col width="175" /><width="400" />
				<thead>
					<tr>
						<th colspan="2">'._('Informations').'</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>'._('Désignation :').'</td>
						<td>'.htmlspecialchars($str['name']).'</td>
					</tr>
					<tr>
						<td>'._('Adresse :').'</td>
						<td>'.htmlspecialchars($str['address1']).'</td>
					</tr>
					<tr>
						<td>'._('Complément d\'adresse :').'</td>
						<td>'.htmlspecialchars($str['address2']).'</td>
					</tr>
					<tr>
						<td>'._('Code postal :').'</td>
						<td>'.htmlspecialchars($str['zipcode']).'</td>
					</tr>
					<tr>
						<td>'._('Ville :').'</td>
						<td>'.htmlspecialchars($str['city']).'</td>
					</tr>
					<tr>
						<td>'._('Pays :').'</td>
						<td>'.htmlspecialchars($str['country']).'</td>
					</tr>
					<tr>
						<td>'._('Description :').'</td>
						<td class="popup-info-desc">'.str_replace( '<p>&nbsp;</p>', '', $str['desc'] ).'</td>
					</tr>
					<tr>
						<td>'._('Publiée ?').'</td>
						<td>'.( $str['publish'] ? _('Oui') : _('Non') ).'</td>
					</tr>
				</tbody>
			</table>
		';
	}

	require_once('admin/skin/footer.inc.php');
