<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators.inc.php');
	
	$marketplace = isset($_GET['marketplace']) ? $_GET['marketplace'] : false;
	
	// Vérifier si le client à accès aux comparateurs
	if( !$config['ctr_active'] ){
		header('HTTP/1.0 403 Forbidden');
		exit;
	}
	
	// Vérifie la validité du paramètre ctr si celui-ci est présent
	if( isset($_GET['ctr']) && !ctr_comparators_exists($_GET['ctr']) ){
		header('Location: index.php');
		exit;
	}elseif( isset($_GET['ctr']) ){
		$rctr = ctr_comparators_get( $_GET['ctr'], true, false, null );
		if( !ria_mysql_num_rows($rctr) ){
			header('Location: index.php');
			exit;
		}
		$ctr = ria_mysql_fetch_array( $rctr );
		$marketplace = $ctr['marketplace'];
	}

	// Activation / Désactivation d'un comparateur de prix
	if( isset($_POST['active'],$_GET['ctr']) ){
		ctr_comparators_activation($_GET['ctr']);
		header('Location: index.php');
		exit;
	}
	
	//Demande de rattachement des catégories de produits aux catégories d'un comparateur de prix
	if( isset($_POST['links-cat'], $_GET['ctr']) ){
		header('Location: link-categories.php?ctr='.$_GET['ctr']);
		exit;
	}
	
	//Demande de rattachement des produits aux catégories d'un comparateur de prix
	if( isset($_POST['links-prd'], $_GET['ctr']) ){
		header('Location: link-products.php?ctr='.$_GET['ctr']);
		exit;
	}
	
	//Filtrer les familles d'un comparateur de prix
	if( isset($_POST['filters-cat'], $_GET['ctr']) ){
		header('Location: categories.php?ctr='.$_GET['ctr']);
		exit;
	}
	
	//Filtrer les familles d'un comparateur de prix
	if( isset($_POST['stats-ctr'], $_GET['ctr']) ){
		header('Location: /admin/comparators/stats/index.php?'.($marketplace ? 'marketplace=1&' : '').'ctr='.$_GET['ctr']);
		exit;
	}
	
	//Rechercher un produit pour un comparateur de prix
	if( isset($_POST['search-ctr'], $_GET['ctr']) ){
		header('Location: /admin/comparators/search/index.php?'.($marketplace ? 'marketplace=1&' : '').'ctr='.$_GET['ctr']);
		exit;
	}
	
	//Rechercher un produit pour un comparateur de prix
	if( isset($_POST['mapping-attr'], $_GET['ctr']) ){
		header('Location: /admin/comparators/mapping-attributs.php?'.($marketplace ? 'marketplace=1&' : '').'ctr='.$_GET['ctr']);
		exit;
	}
	
	// Paramétrer le comparateur de prix
	if( isset($_POST['params'], $_GET['ctr']) ){
		header('Location: params.php?ctr='.$_GET['ctr']);
		exit;
	}

	if( !isset($_SESSION['websitepicker']) || !is_numeric($_SESSION['websitepicker']) || !$_SESSION['websitepicker'] ){
		$_SESSION['websitepicker'] = $config['wst_id'];
	}

	if( isset($_GET['wst']) && is_numeric($_GET['wst']) && $_GET['wst'] ){
		$_SESSION['websitepicker'] = $_GET['wst'];
	}

	define('ADMIN_PAGE_TITLE', ( isset($ctr['name']) ? $ctr['name'].' - ' : '' ).( $marketplace ? _('Places de marché') : _('Comparateurs de prix') ));
	require_once('admin/skin/header.inc.php');
?>
			<h2><?php echo $marketplace ? _('Places de marché') : _('Comparateurs de prix'); ?></h2>
			<?php 
				if( !$marketplace ){
					print view_websites_selector( $_SESSION['websitepicker'], false );
				}
			?>
			<div id="exp" class="notice">
				<?php print $marketplace ? _('A partir d\'ici, vous pouvez gérer l\'exportation de vos produits pour les places de marché ci-dessous.') : _('A partir d\'ici, vous pouvez gérer l\'exportation de vos produits pour les comparateurs de prix ci-dessous.').' :'; ?>
			</div>
			<div id="comparators" class="all">
				<?php 
					require_once('view.ctr.inc.php');
					if( isset($_GET['ctr']) && ctr_comparators_exists($_GET['ctr']) ){
						print view_ctr_header($_GET['ctr'], $marketplace);
					} else {
						print view_ctr_header(0, $marketplace);
					}
				?>
			</div>
<?php
	require_once('admin/skin/footer.inc.php');
?>