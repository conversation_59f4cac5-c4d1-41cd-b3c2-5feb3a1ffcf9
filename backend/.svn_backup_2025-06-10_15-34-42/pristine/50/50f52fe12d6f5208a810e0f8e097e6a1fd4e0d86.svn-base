<?php
require_once 'Services/Service.class.php';
require_once 'Services/Customer/Customer.class.php';

/**	Cette classe permet le chargement de produits de façon allégée.
 * Le but étant d'optimiser les temps de chargement des sites en appliquant la méthode du lazy loading
 */
class ProductsService extends Service
{

	/**	Tableau des informations des produits
	 * @var	null|array
	 */
	protected $products = null;

	/**	Nombre de produits
	 * @var	int
	 */
	protected $count = 0;

	/**	Tableau d'identifiants de champs avancés à charger
	 * @var	null|array
	 */
	private $fields = null;

	/**	Tableau des attributs de requête
	 * @var	array
	 */
	private $attributes = [
		'prds'				=> 0, //<<<< Identifiant d'un produit ou tableau d'identifiants de produits
		'cats'				=> 0, //<<<< Identifiant d'une catégorie ou tableau d'identifiants de catégories
		'cat_children'		=> false,
		'with_img'			=> false, //<<<< Si vrai, récupére les images des produits
		'cfg_img'			=> 'high', //<<<< Nom de la config image à utiliser
		'fld'				=> false, //<<<< Identifiant d'un champ avancé ou tableau d'identifiants de champs avancés ou tableau fld_id => valeur
		'or_between_fld'	=> false, //<<<< Indique si, pour le tableau $fld, les différents champs sont séparés par des "ET" (vrai par défaut) ou des "OU"
		'published'			=> null, //<<<< Si vrai seul les produits publiés sont retournés. Si faux, seul les produits non publiés sont retournés. Valeur par défaut : null (tous les produits)
		'orderable'			=> null, //<<<< Si vrai seul les produits commandables sont retournés. Si faux, seul les produits non commandables sont retournés. Valeur par défaut : null (tous les produits)
		'only_prd_ids'		=> false, //<<<< Retourne uniquement les identifiants des produits si à true
		'only_parents'		=> false, //<<<< Retourne les produits parents uniquement si à true
		'offset'			=> 0,
		'limit'				=> -1,
		'price_min'			=> null,
		'price_max'			=> null,
		'with_price'		=> false,
		'brds'				=> 0,
		'sort'				=> false,
		'children'			=> false, //<<<< Par défaut, les produits 'enfant seulement' ne sont pas retournés. Mettre true pour les inclure au résultat
		'parent'			=> 0, //<<<< Identifiant d'un produit parent

	];

	/**	Constructeur de la classe
	 * @param	int|array	$prd	Optionnel, Identifiant de produit ou tableau d'identifiants de produits
	 * @param	int|array	$cat	Optionnel, Identifiant de catégorie ou tableau d'identifiants de catégories
	 */
	public function __construct($prd = 0, $cat = 0)
	{
		$this->setAttribute('prds', $prd)
			->setAttribute('cats', $cat);
	}

	/**	Permet de définir une valeur à un attribut
	 * @param	string	$key	Obligatoire, Nom de l'attribut
	 * @param	mixed	$value	Obligatoire, Valeur de l'attribut
	 * @return	object	L'objet en cours
	 */
	public function setAttribute($key, $value)
	{
		$this->__sanitizeAttribute($key, $value);
		return $this;
	}

	/**	La valeur d'un attribut
	 * @param	string		$key	Obligatoire, Nom de l'attribut
	 * @return	mixed|null	Valeur de l'attribut, null en cas d'erreur
	 */
	public function getAttribute($key)
	{
		if (!is_string($key)) {
			return null;
		}
		$key = trim(strtolower($key));

		if (!$key) {
			return null;
		}
		return array_key_exists($key, $this->attributes) ? $this->attributes[$key] : null;
	}

	/**	Retourne tous les attributs
	 * @return	array	Tableau attribut => valeur
	 */
	public function getAttributes()
	{
		return $this->attributes;
	}

	/**	Retourne les produits
	 * @param	bool		$force	Optionnel, True pour recharger la liste des produits
	 * @return	array|bool	Tableau des produits, false sinon
	 * @todo	restrictions
	 * @todo	orderable
	 * @todo	filters
	 * @todo	sort
	 * @todo	image + alt + custom size
	 */
	public function getProducts($force = false)
	{

		$force = is_bool($force) ? $force : false;

		if (is_array($this->products) && count($this->products) && !$force) {
			return $this->products;
		}
		$prds = $this->attributes['prds'];
		$cats = $this->attributes['cats'];
		$brds = $this->attributes['brds'];

		if ($prds === false || $cats === false || $brds === false) {
			return false;
		}
		global $config;

		$fld = $this->attributes['fld'];
		$or_between_fld = $this->attributes['or_between_fld'];
		$published = $this->attributes['published'];
		$orderable = $this->attributes['orderable'];
		$only_prd_ids = $this->attributes['only_prd_ids'];
		$only_parents = $this->attributes['only_parents'];
		$cat_children = $this->attributes['cat_children'];
		$with_price = $this->attributes['with_price'];
		$offset = $this->attributes['offset'];
		$limit = $this->attributes['limit'];
		$limit = $limit > 0 ? $limit : '18446744073709551615';
		$sort = $this->attributes['sort'];
		$load_children = $this->attributes['children'];
		$parent = $this->attributes['parent'];

		if (is_array($sort) && sizeof($sort)) {
			// Récupère un éventuel tri par prix
			foreach ($sort as $col => $dir) {
				$dir = is_string($dir) && strtolower(trim($dir)) == 'desc' ? 'desc' : 'asc';
				switch ($col) {
					case 'ref':
						$sort_final = 'prd.prd_ref ' . $dir;
						break;
						// case 'hits':
						// 	$sort_final = 'prd.prd_hits '.$dir;
						// 	break;
					case 'title':
						$sort_final = 'title ' . $dir;
						break;
					case 'name':
						$sort_final = 'name ' . $dir;
						break;
					case 'brand':
						$sort_final = 'brd_title ' . $dir;
						break;
						// case 'completion':
						// 	$sort_final = 'prd.prd_completion '.$dir;
						// 	break;
						// case 'date-created':
						// 	$sort_final = 'prd.prd_date_created '.$dir;
						// 	if( !$cumul_sort || $cumul_sort=='after' ){
						// 		array_push( $sort_final, 'prd_date_created '.$dir );
						// 	}else{
						// 		array_unshift( $sort_final, 'prd_date_created '.$dir );
						// 	}
						// 	break;
						// case 'date-published':
						// 	$sort_final = 'prd.prd_date_published '.$dir;
						// 	break;
					case 'random':
						$sort_final = 'rand()';
						break;
					case 'new':
						$sort_final = 'new ' . $dir;
						break;
					case 'prcprices':
						$with_price = true;
						$offset = 0;
						$limit = '18446744073709551615';

						$sort_final = 'price_ht ' . $dir;
						break;
				}
			}
		}

		$cols = [];
		$cols['id'] = 'prd.prd_id';

		if (!$only_prd_ids) {
			$with_price = true;
			$col_new = isset($config['prd_new_date']) && in_array($config['prd_new_date'], ['prd_date_created', 'prd_first_published', 'prd_date_published']) ? $config['prd_new_date'] : 'prd.prd_date_created';

			$cols['ref'] = 'prd.prd_ref';
			$cols['name'] = 'prd.prd_name';
			$cols['title'] = 'if(prd.prd_title="",prd.prd_name,prd.prd_title)';
			$cols['"desc"'] = 'prd.prd_desc';
			$cols['"desc-long"'] = 'prd.prd_desc_long';
			$cols['barcode'] = 'prd.prd_barcode';
			// $cols['price_ht'] = 'price.price_val';
			// $cols['price_ttc'] = 'ptv.ptv_tva_rate * price.price_val';
			$cols['price_ht'] = 'prc.prc_value';
			$cols['price_ttc'] = 'ptv.ptv_tva_rate * prc.prc_value';
			$cols['tax_rate'] = 'ptv.ptv_tva_rate';
			// $cols['publish'] = 'prd.prd_publish';
			// $cols['publish_cat'] = 'prd.prd_publish_cat';
			// $cols['warranty'] = 'prd.prd_garantie';
			$cols['tag_title'] = 'prd.prd_tag_title';
			$cols['tag_desc'] = 'prd.prd_tag_desc';
			$cols['ecotaxe'] = 'prd.prd_ecotaxe';
			$cols['new'] = 'prd.prd_new!="-1" and if( ' . $config['prd_new_days'] . ' != 0, (prd.prd_new="1" or datediff(now(),' . $col_new . ')<=' . $config['prd_new_days'] . '), prd.prd_new="1")';
			$cols['brd_id'] = 'brd.brd_id';
			$cols['brd_title'] = 'if(brd.brd_title!="", brd.brd_title, brd.brd_name)';
			$cols['brd_url'] = 'brd.brd_url';
			$cols['taxcode'] = 'prd.prd_taxcode';
			$cols['canonical_id'] = 'prd.prd_canonical_id';
			// $cols['supplier_ref'] = 'ps.ps_ref';
			// $cols['supplier_price_ht'] = 'ps.ps_price';
			// $cols['supplier_barcode'] = 'ps.ps_barcode';
			// $cols['supplier_id'] = 'ps.ps_usr_id';
			$cols['url_alias'] = 'if(ifnull(cly.cly_url_perso, \'\')=\'\', cly.cly_url_alias, cly.cly_url_perso)';
			$cols['url_perso'] = 'cly.cly_url_perso';
			$cols['cat_id'] = 'cat.cat_id';
			$cols['cat_name'] = 'cat.cat_name';
			$cols['cat_title'] = 'if(ifnull(cat.cat_title, "") != "", cat.cat_title, cat.cat_name)';
		}

		$where = 'prd.prd_tnt_id = ' . $config['tnt_id'];


		if (!is_null($orderable)) {
			$where .= ' and prd.prd_orderable=' . ($orderable ? '1' : '0');
		}

		if (!is_null($published)) {
			$where .= '
				and prd.prd_publish=' . ($published ? '1' : '0') . '
				and prd.prd_publish_cat=' . ($published ? '1' : '0') . '
			';
		}

		if (is_array($prds) && count($prds)) {
			$where .= ' and prd.prd_id in(' . implode(',', $prds) . ')';
		}

		if (is_array($brds) && count($brds)) {
			$where .= ' and prd.prd_brd_id in (' . implode(',', $brds) . ')';
		}

		if ($with_price) {
			$price_min = $this->getAttribute('price_min');
			$price_max = $this->getAttribute('price_max');

			if ($price_min !== null) {
				$price_min = str_replace(array(',', ' '), array('.', ''), $price_min);

				if (is_numeric($price_min)) {
					$where .= ' and prc.prc_value >= ' . $price_min;
				}
			}

			if ($price_max !== null) {
				$price_max = str_replace(array(',', ' '), array('.', ''), $price_max);

				if (is_numeric($price_max)) {
					$where .= ' and prc.prc_value <= ' . $price_max;
				}
			}
		}

		if (!$load_children) {
			$where .= ' and prd.prd_childonly=0';
		}

		$join = '';

		if ($only_parents) {
			$join .= '
				inner join
					prd_hierarchy as prd_hry
				on
					prd_hry.prd_tnt_id=' . $config['tnt_id'] . '
				and	prd_hry.prd_parent_id=prd.prd_id
			';
		} elseif ($parent) {
			$join .= '
				inner join
					prd_hierarchy as prd_hry
				on
					prd.prd_tnt_id = prd_hry.prd_tnt_id
				and prd.prd_id = prd_hry.prd_child_id
				and prd_hry.prd_parent_id=' . $parent . '
			';
		}

		if (count($cats) && $cat_children) {
			$join .= '
				inner join
					prd_cat_hierarchy as cat_hry
				on
					cat_hry.cat_parent_id in(' . implode(',', $cats) . ')
				and cat_hry.cat_tnt_id=' . $config['tnt_id'] . '
			';
		}

		$join .= '
			inner join
				prd_classify as cly
			on
				prd.prd_id=cly.cly_prd_id
			and cly.cly_tnt_id=' . $config['tnt_id'] . '
		';

		if (count($cats)) {

			if (!$cat_children) {
				$join .= ' and cly.cly_cat_id in (' . implode(',', $cats) . ')';
			} else {
				$join .= '
					and (
						cly.cly_cat_id=cat_hry.cat_child_id
					or
						cly.cly_cat_id in(' . implode(',', $cats) . ')
					)
				';
			}
		}

		$join .= '
			inner join
				prd_categories as cat
			on
				cly.cly_cat_id=cat.cat_id
			and cat.cat_tnt_id=' . $config['tnt_id'];

		$join .= '
			left join
				prd_brands as brd
			on
				prd.prd_brd_id=brd.brd_id
			and brd.brd_tnt_id=' . $config['tnt_id'];

		if ($with_price) {
			$User = CustomerService::getInstance();
			$prc_id = $User->getPriceCategory();

			$join .= '
				left join
					prc_tvas as ptv
				on
						ptv.ptv_date_deleted is null
					and ptv.ptv_tnt_id=' . $config['tnt_id'] . '
					and ptv.ptv_prd_id=prd.prd_id
					and ptv.ptv_cnt_code is null
					and ptv.ptv_cac_id is null
				left join
					prc_prices as prc
				on
						prc.prc_type_id=1
					and prc.prc_prd_id = prd.prd_id
					and prc.prc_is_deleted=0
					and prc.prc_is_promotion=0
					and prc.prc_grp_id is null
					and ( prc.prc_date_start<=now() ) and ( prc.prc_date_end>now() )
					and prc.prc_qte_min=1
					and prc.prc_tnt_id=' . $config['tnt_id'] . '
					and exists (
						select
							1
						from
							prc_price_conditions as ppc
						where
								ppc.ppc_tnt_id=' . $config['tnt_id'] . '
							and ppc.ppc_value=' . $prc_id . '
							and ppc.ppc_prc_id=prc.prc_id
					)
			';

		}

		if (is_array($fld) && count($fld)) {
			foreach ($fld as $fld_id => $fld_val) {
				$join .= '
					right join
						fld_object_values as fld
					on
						fld.pv_tnt_id=' . $config['tnt_id'] . '
					and fld.pv_obj_id_0=prd.prd_id
					and fld.pv_fld_id=' . $fld_id . '
				';
				if (is_array($fld_val) && count($fld_val)) {
					$operator = $or_between_fld ? 'or' : 'and';
					// $join .= 'and (fld.pv_value REGEXP "'.implode('" '.$operator.' fld.pv_value REGEXP "', $fld_val).'")';
					$join .= 'and (fld.pv_value LIKE "%' . implode('%" ' . $operator . ' fld.pv_value LIKE "%', $fld_val) . '%")';
				} else {
					$join .= 'and fld.pv_value="' . $fld_val . '"';
				}
			}
		}

		$ar_cols = [];
		foreach ($cols as $as => $col) {
			$ar_cols[] = $col . ' as ' . $as;
		}

		$sql = '
			select ' . implode(', ', $ar_cols) . '
			from
				prd_products as prd
			' . $join . '
			where
			' . $where . '
			group by prd.prd_id
			' . (isset($sort_final) ? 'order by ' . $sort_final : '') . '
			limit ' . $offset . ', ' . $limit . '

		';

		$r = ria_mysql_query($sql);

		if (!ria_mysql_num_rows($r)) {
			return false;
		}
		$this->products = [];
		$this->count = 0;

		while ($p = ria_mysql_fetch_assoc($r)) {
			$this->products[] = $p;
			$this->count++;
		}

		// Charge les images des produits
		$this->__loadImages();
		// Charge les champs avancés des produits
		$this->__loadFields();

		return $this->products;
	}

	/**	Permet de définir les champs avancés à récupérer pour chaque produit
	 * @param
	 */
	public function fields($fld)
	{
		if (!is_array($fld) || !count($fld)) {
			return $this;
		}
		$ar_fld = [];

		foreach($fld as $fld_id){
			if( !is_numeric($fld_id) || $fld_id <= 0){
				continue;
			}
			$ar_fld[] = $fld_id;
		}

		if( count($ar_fld) ){
			$this->fields = $ar_fld;
		}
		return $this;
	}

	/**	Charge les champs avancés des produits
	 * @return	ProductsService	L'objet en cours
	 */
	private function __loadFields()
	{
		global $config;

		if (is_null($this->fields) || is_null($this->products)) {
			return $this;
		}

		foreach ($this->products as $k => $prd) {

			$r_fields = ria_mysql_query('
				select
					fld.fld_id as id,
					fld.fld_name as name,
					val.pv_value as obj_value
				from
					fld_object_values as val
				right join
					fld_fields as fld
				on
					fld.fld_tnt_id = ' . $config['tnt_id'] . '
				and fld.fld_id = val.pv_fld_id
				and fld.fld_cls_id = ' . CLS_PRODUCT . '
				and fld.fld_date_deleted is null
				where
					val.pv_tnt_id = ' . $config['tnt_id'] . '
				and val.pv_obj_id_0 = ' . $prd['id'] . '
				and val.pv_obj_id_1 = 0
				and val.pv_obj_id_2 = 0
				and val.pv_fld_id in (' . implode(',', $this->fields) . ')
			');

			if (!ria_mysql_num_rows($r_fields)) {
				continue;
			}
			$this->products[$k]['fields'] = [];

			while ($field = ria_mysql_fetch_assoc($r_fields)) {
				$this->products[$k]['fields']['field' . $field['id']] = [
					'id'		=> $field['id'],
					'name'		=> $field['name'],
					'value'		=> $field['obj_value']
				];
			}
		}

		return $this;
	}

	/**	Charge les images des produits si nécessaire
	 * @return	object	L'objet en cours
	 */
	private function __loadImages()
	{

		if (!$this->attributes['with_img'] || is_null($this->products)) {
			return $this;
		}
		global $config;

		$thumb = $config['img_sizes'][$this->attributes['cfg_img']];
		$img_default = isset($config['default_image']) ? $config['default_image'] : 0;
		$ar_tmp = [];

		foreach ($this->products as $p) {
			$rimg = prd_images_get($p['id'], 0, false, true, true);
			$p['images'] = [];

			if (ria_mysql_num_rows($rimg)) {

				while ($img = ria_mysql_fetch_assoc($rimg)) {
					$p['images'][] = [
						'id'		=> $img['id'],
						'alt'		=> $img['alt'],
						'url'		=> $config['img_url'] . '/' . $thumb['dir'] . '/' . $img['id'] . '.' . $thumb['format'],
						'width'		=> $thumb['width'],
						'height'	=> $thumb['height'],
						'is_main'	=> $img['is_main']
					];
				}
			} else {
				$p['images'][] = [
					'id'		=> $img_default,
					'alt'		=> $p['title'],
					'url'		=> $config['img_url'] . '/' . $thumb['dir'] . '/' . $img_default . '.' . $thumb['format'],
					'width'		=> $thumb['width'],
					'height'	=> $thumb['height'],
					'is_main'	=> true
				];
			}
			$ar_tmp[] = $p;
		}
		$this->products = $ar_tmp;

		return $this;
	}

	/**	Permet de controler la valeur d'un attribut et de le mettre à jour
	 * @param	string	$key	Obligatoire, Nom de l'attribut
	 * @param	mixed	$value	Obligatoire, Valeur de l'attribut
	 * @return	bool	True en cas de succes, false sinon
	 */
	private function __sanitizeAttribute($key, $value)
	{

		if (!is_string($key)) {
			return false;
		}
		global $config;

		$key = trim(strtolower($key));

		switch ($key) {

			case 'prds':
			case 'cats':
			case 'brds':
				$this->attributes[$key] = control_array_integer($value, false);
				break;

			case 'fld':
			case 'sort':
				$this->attributes[$key] = is_array($value) && count($value) ? $value : false;
				break;

			case 'cat_children':
			case 'or_between_fld':
			case 'only_prd_ids':
			case 'only_parents':
			case 'with_img':
			case 'with_price':
			case 'children':
				$this->attributes[$key] = is_bool($value) ? $value : false;
				break;

			case 'published':
			case 'orderable':
				$this->attributes[$key] = is_bool($value) || is_null($value) ? $value : null;
				break;

			case 'offset':
			case 'parent':
				$this->attributes[$key] = is_numeric($value) && $value > 0 ? $value : 0;
				break;

			case 'limit':
				$this->attributes[$key] = is_numeric($value) && $value > 0 ? $value : -1;
				break;

			case 'cfg_img':
				$this->attributes[$key] = is_string($value) && trim($value) != '' && array_key_exists($value, $config['img_sizes']) ? $value : 'high';
				break;

			case 'price_min':
			case 'price_max':
				$this->attributes[$key] = is_string($value) && trim($value) != '' ? $value : null;
				break;

			default:
				return false;
		}
		return true;
	}
}
