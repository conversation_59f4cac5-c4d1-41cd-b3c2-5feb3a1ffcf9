<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/pubsub.proto

namespace Google\Cloud\PubSub\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Response for the `ListSnapshots` method.
 *
 * Generated from protobuf message <code>google.pubsub.v1.ListSnapshotsResponse</code>
 */
class ListSnapshotsResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * The resulting snapshots.
     *
     * Generated from protobuf field <code>repeated .google.pubsub.v1.Snapshot snapshots = 1;</code>
     */
    private $snapshots;
    /**
     * If not empty, indicates that there may be more snapshot that match the
     * request; this value should be passed in a new `ListSnapshotsRequest`.
     *
     * Generated from protobuf field <code>string next_page_token = 2;</code>
     */
    private $next_page_token = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Cloud\PubSub\V1\Snapshot[]|\Google\Protobuf\Internal\RepeatedField $snapshots
     *           The resulting snapshots.
     *     @type string $next_page_token
     *           If not empty, indicates that there may be more snapshot that match the
     *           request; this value should be passed in a new `ListSnapshotsRequest`.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Pubsub\V1\Pubsub::initOnce();
        parent::__construct($data);
    }

    /**
     * The resulting snapshots.
     *
     * Generated from protobuf field <code>repeated .google.pubsub.v1.Snapshot snapshots = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getSnapshots()
    {
        return $this->snapshots;
    }

    /**
     * The resulting snapshots.
     *
     * Generated from protobuf field <code>repeated .google.pubsub.v1.Snapshot snapshots = 1;</code>
     * @param \Google\Cloud\PubSub\V1\Snapshot[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSnapshots($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\PubSub\V1\Snapshot::class);
        $this->snapshots = $arr;

        return $this;
    }

    /**
     * If not empty, indicates that there may be more snapshot that match the
     * request; this value should be passed in a new `ListSnapshotsRequest`.
     *
     * Generated from protobuf field <code>string next_page_token = 2;</code>
     * @return string
     */
    public function getNextPageToken()
    {
        return $this->next_page_token;
    }

    /**
     * If not empty, indicates that there may be more snapshot that match the
     * request; this value should be passed in a new `ListSnapshotsRequest`.
     *
     * Generated from protobuf field <code>string next_page_token = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setNextPageToken($var)
    {
        GPBUtil::checkString($var, True);
        $this->next_page_token = $var;

        return $this;
    }

}

