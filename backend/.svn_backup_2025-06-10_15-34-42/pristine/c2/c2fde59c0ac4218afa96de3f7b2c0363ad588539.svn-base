<?php
/** \file yuto_to_pay_subscription.php
 *  Ce script permet de débiter les cartes de crédit des clients si nécessaire dans le cadre de l'abonnement Yuto.
 *	Il peut être lancé à n'importe quel moment car un abonnement ne sera jamais préléve plusieurs fois.
 *
 *	Un prélèvement est réalisé le lendemain de la fin de la période de l'abonnement pour reconduire sur la période suivante (mensuel ou annuel)
 *
 * 	Ce script est lancé une fois par jour.
 */
if (!isset($ar_params)) {
	error_log("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	exit;
}

require_once('devices.inc.php');
require_once('orders.inc.php');
require_once('users.inc.php');
require_once('PaymentExternal/Payplug_old.inc.php');
require_once('strings.inc.php');
require_once('ord.invoices.inc.php');

// Récupération des différentes configurations mails pour Yuto vente en ligne
$cfg_emails = cfg_emails_yuto_get();

// Initialisation du module de paiement PayPlug
$payplug = new Payplug();


// Parcours chaque tenant pour réaliser le prélévement des abonnements
foreach( $configs as $config ){
	global $ria_db_connect;

	// Chargement des informations sur le produit correspondant à l'abonnement choisi (depuis RiaStudio)
	$package = RegisterGCP::getPackage($config['tnt_id']);

	// Charge les différents articles Yuto
	$prd_packages = dev_package_get_products($package);

	// Récupère les abonnements en cours
	$r_sub = dev_subscribtions_get();
	if( !$r_sub ){
		continue;
	}

	while( $sub = ria_mysql_fetch_assoc($r_sub) ){
		// Si l'abonnement a été annulé, aucun prélèvement n'est réalisé
		if( trim($sub['date_unsubscribe']) != '' ){
			continue;
		}

		// Seul les abonnements issu d'une commande Yuto VEL sont à prendre en compte
		if( !is_numeric($sub['ord_id']) || $sub['ord_id'] <= 0 ){
			continue;
		}

		// Contrôle que le prélèvement n'a pas déjà été réalisé
		$pay = fld_object_values_get($sub['id'], _FLD_YUTO_PAY_ABO, '', false, true);
		if( $pay == 'Oui' ){
				continue;
		}

		$date_start = new DateTime(dateparse($sub['date_start']).' 00:00:00');
		$date_end = new DateTime(dateparse($sub['date_end']).' 23:59:59');

		// Charge le type d'abonnement
		if( $date_end->diff($date_start)->format("%a") > 300 ){
			$sub_type = 'yearly';
		}else{
			$sub_type = 'monthly';
		}

		// Période de l'abonnement qui sera affiché dans les metadata de l'abonnement
		$date = new DateTime();
		if( $sub_type == 'monthly' ){
			$sub_date = $date->format('F Y');
		}else{
			$sub_date = 'year '.$date->format('Y');
		}

		// Contrôle que l'abonnement est arrivé à échance pour prélevé l'abonnement pour la période suivante
		if( $date_end->getTimestamp() < time() ){
			$old_config = $config;
			$old_ria_db_connect = $ria_db_connect;

			// Réalise la connexion à RiaStudio
			if( !RegisterGCPConnection::init(52, true) ){
				error_log(__FILE__.':'.__LINE__.' => Erreur lors de la connexion à RiaStudio');
				break;
			}

			$licences = is_numeric($sub['qte_next']) && $sub['qte_next'] ? $sub['qte_next'] : $sub['qte'];

			// Création d'un panier pour le nouvel abonnement
			$ord_id = ord_orders_add_sage($sub['usr_id'], date('d/m/Y'), _STATE_BASKET, '', '', '', 0, 227);

			if( is_numeric($ord_id) && $ord_id > 0 ){
				$ord_prd = $prd_packages[$sub_type == 'monthly' ? 'mensuel' : 'annuel'];

				// Le panier est vidé à chaque fois pour prendre en compte tout changement fait entre deux rechargements de pages
				ord_products_del($ord_id);

				// Ajoute le bon produit à la commande en fonction du type d'abonnement
				if( !ord_products_add($ord_id, $ord_prd['id'], $licences, '', false, null, 0, false, 0, $ord_prd['sub_id']) ){
					error_log(__FILE__.':'.__LINE__.' => Erreur lors l\'ajout du produit de licence');
					ord_orders_del_sage($ord_id);
					break;
				}

				// Ajout du coût de l'hébergement pour la version Business
				if( $package == 'business' ){
					$hosting = false;
					foreach( $prd_packages['hosting'] as $qty => $data ){
						if( $licences >= $qty ){
							$hosting = $data;
						}
					}

					if( !ord_products_add($ord_id, $hosting['id'], ($sub_type == 'monthly' ? 1 : 12), '', false, null, 0, false, 0) ){
						error_log(__FILE__.':'.__LINE__.' => Erreur lors l\'ajout du produit pour le coût du BO');
						ord_orders_del_sage($ord_id);
						break;
					}
				}

				// Date de fin de l'abonnement
				fld_object_values_set($ord_id, 100073, $date_end->format('d/m/Y'));

				// Paiement de l'abonnement avec enregistrement de l'empreinte bancaire pour la récursivité
				$pay_id = '';

				// Charge les informations du panier dans une variable
				$cart_abo = ria_mysql_fetch_assoc(ord_orders_get_simple(
					array('id' => $ord_id),
					array(),
					array(),
					array(),
					array(
						'type' => 'replace',
						'columns' => array(
							'ord_id' => 'id',
							'ord_usr_id' => 'user_id',
							'ord_total_ht' => 'total_ht',
							'ord_total_ttc' => 'total_ttc',
						)
					)
				));

				// Réalise le paiement
				try{
					$payplug = new Payplug();
					$payplug->createSimplePayment( $cart_abo['id'], false, false, true );

					// Contrôle du paiement, si celui-ci est OK, aucune confirmation de commande n'est envoyée (second paramètre)
					$payplug->_getPaymentResult( $_SESSION['payplug_payment_id'], false );
				}catch( Exception $e ){
					error_log(__FILE__.':'.__LINE__.' => Erreur lors du paiement avec l\'empreinte CB pour le tenant '.$config['tnt_id']);
					ord_orders_del_sage($ord_id);
					break;
				}
			}

			$config = $old_config;
			$ria_db_connect = $old_ria_db_connect;

			// Détermine les nouvelles dates d'abonnement
			if( $sub_type == 'monthly' ){
				$date_start->modify('+1 month');
				$date_end->modify('+1 month');
			}else{
				$date_start->modify('+1 year');
				$date_end->modify('+1 year');
			}

			// Création du nouvel abonnement une fois le paiement réalisé
			if( !dev_subscribtions_add($licences, $date_start->format('Y-m-d'), $date_end->format('Y-m-d'), 0, $ord_id, $cart_abo['total_ttc'], $sub['usr_id'], $cart_abo['total_ht']) ){
				error_log(__FILE__.':'.__LINE__.' => Erreur lors l\'enregistrement du renouvellement de l\'abonnement');
				ord_orders_del_sage($ord_id);
				break;
			}

			// Enregistre le fait que l'abonnement a été renouvellé
			fld_object_values_set($sub['id'], _FLD_YUTO_PAY_ABO, 'Oui');

			continue;
		}
	}
}