tinyMCE.addI18n('fr.table_dlg',{"rules_border":"bordure","rules_box":"bo\u00eete","rules_vsides":"verticales","rules_rhs":"\u00e0 droite","rules_lhs":"\u00e0 gauche","rules_hsides":"horizontales","rules_below":"au-dessous","rules_above":"au-dessus","rules_void":"aucune",rules:"R\u00e8gles","frame_all":"tous","frame_cols":"colonnes","frame_rows":"lignes","frame_groups":"groupe","frame_none":"aucun",frame:"Cadre",caption:"Afficher la l\u00e9gende du tableau","missing_scope":"\u00cates-vous s\u00fbr de vouloir continuer sans sp\u00e9cifier de port\u00e9e pour cette cellule de titre ? Sans port\u00e9e, cela peut \u00eatre difficile pour certains utilisateurs de comprendre le contenu ou les donn\u00e9es affich\u00e9es dans le tableau.","cell_limit":"Vous avez d\u00e9pass\u00e9 le nombre maximum de cellules ({$cells}).","row_limit":"Vous avez d\u00e9pass\u00e9 le nombre maximum de lignes ({$rows}).","col_limit":"Vous avez d\u00e9pass\u00e9 le nombre maximum de colonnes ({$cols}).",colgroup:"Groupe de colonnes",rowgroup:"Groupe de lignes",scope:"Port\u00e9e",tfoot:"Pied de tableau",tbody:"Corps de tableau",thead:"En-t\u00eates de tableau","row_all":"Mettre \u00e0 jour toutes les lignes du tableau","row_even":"Mettre \u00e0 jour les lignes paires","row_odd":"Mettre \u00e0 jour les lignes impaires","row_row":"Mettre \u00e0 jour la ligne courante","cell_all":"Mettre \u00e0 jour toutes les cellules du tableau","cell_row":"Mettre \u00e0 jour toutes les cellules de la ligne","cell_cell":"Mettre \u00e0 jour la cellule courante",th:"Titre",td:"Donn\u00e9es",summary:"R\u00e9sum\u00e9",bgimage:"Image de fond",rtl:"de droite \u00e0 gauche",ltr:"De gauche \u00e0 droite",mime:"Type MIME de la cible",langcode:"Code de la langue",langdir:"Sens de lecture",style:"Style",id:"Id","merge_cells_title":"Fusionner les cellules",bgcolor:"Couleur du fond",bordercolor:"Couleur de la bordure","align_bottom":"Bas","align_top":"Haut",valign:"Alignement vertical","cell_type":"Type de cellule","cell_title":"Propri\u00e9t\u00e9s de la cellule","row_title":"Propri\u00e9t\u00e9s de la ligne","align_middle":"Centr\u00e9","align_right":"Droite","align_left":"Gauche","align_default":"Par d\u00e9faut",align:"Alignement",border:"Bordure",cellpadding:"Espacement dans les cellules",cellspacing:"Espacement entre les cellules",rows:"Lignes",cols:"Colonnes",height:"Hauteur",width:"Largeur",title:"Ins\u00e9rer / modifier un tableau",rowtype:"Type de ligne","advanced_props":"Propri\u00e9t\u00e9s avanc\u00e9es","general_props":"Propri\u00e9t\u00e9s g\u00e9n\u00e9rales","advanced_tab":"Avanc\u00e9","general_tab":"G\u00e9n\u00e9ral","cell_col":"Mettre \u00e0 jour toutes les cellules de la colonne"});