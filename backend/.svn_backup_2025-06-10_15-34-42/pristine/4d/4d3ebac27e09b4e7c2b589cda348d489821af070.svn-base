<?php

    /** \ajax-order-duplicate.php
     *  Ce fichier gère la duplication d'une commande et la réponse pour une requête AJAX. Les paramètres sont les suivants :
     *  - ord_id : obligatoire, identifiant de la commande à dupliquer
     *  - ref : facultatif, référence à utiliser pour la nouvelle commande
     *  - user : facultatif, identifiant du compte client auquel rattacher la nouvelle commande
     */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

    if (!isset( $_POST['ord_id'] ) || !is_numeric( $_POST['ord_id'] ) || $_POST['ord_id'] <= 0) {
        print json_encode(array('code' => '400', 'response' => _('L\'identifiant de la commande est invalide ou manquant.')));
    } else {
        $order_id = $_POST['ord_id'];
        $new_id = false;
        $ref = isset($_POST['ref']) ? $_POST['ref'] : '';
        $user_id = isset($_POST['user']) ? $_POST['user'] : 0;

        if (!$new_id = ord_orders_copy($order_id, $ref, $user_id, isset($_POST['admin']))) {
            $error = _('Une erreur est survenue lors de la duplication de la commande.')."\n"._('Veuillez réessayer ou prendre contact pour signaler l\'erreur.');
        }

        if (isset($error)){
            print json_encode(array('code' => '400', 'response' => $error));
        } else {
            print json_encode(array('code' => '100', 'new_ord' => $new_id, 'response' => _('La duplication s\'est correctement déroulée.')));
        }
    }

