<?php
    /** \file reports.php
     *  Ce fichier contient l'onglet Rapports de la fiche client. Il affiche la liste des rapports d'appels et de visite
     *  associés à ce compte client.
     */

    // Ce fichier peut uniquement être utilisé comme include de la fiche client, il ne peut pas être ouvert directement.
	if( !isset($usr) ){
		header('Location: /admin/customers/index.php');
		exit;
    }

    // Charge les rapports de visite
	$visits = rp_reports_get( 0, 0, $usr['id'], 0, null, null );
    $count_visits = $visits ? ria_mysql_num_rows( $visits ) : 0;
    
    // Charge les rapports d'appels
    $calls = gcl_calls_get_by_view( "", 0, 0, 0, array(), $usr['id']);
    if( !$calls ) $calls = array();
    $count_calls = sizeof( $calls );

    // Agrège les rapports d'appels et de visite dans un seul et même tableau, trié par date décroissante
    $reports = array();

    // Agrège les rapports de visite
    while( $visit = ria_mysql_fetch_array($visits) ){
        $reports[] = array(
            'id' => $visit['id'],
            'type_id' => $visit['type_id'],
            'created' => $visit['date_created'],
            'author' => array(
                'id' => $visit['author_id'],
                'name' => $visit['author_name'],
                'is_sync' => $visit['author_is_sync'],
                'is_deleted' => $visit['author_is_deleted']
            ),
            'comments' => $visit['comments']
        );
    }

    // Agrège les rapports d'appels
    foreach( $calls as $key => $call ){
        if( is_array($call) ){

            $author = array(
                'id' => 0,
                'name' => '',
                'is_sync' => false,
                'is_deleted' => false
            );

            if( isset($call['gcl_author_id']) && is_numeric( $call['gcl_author_id'] ) && $call['gcl_author_id']>0 ){
                $r_author = gu_users_get( $call['gcl_author_id'] );
                if( $r_author && ria_mysql_num_rows($r_author) ){
                    $a = ria_mysql_fetch_assoc( $r_author );

                    $author = array(
                        'id' => $a['id'],
                        'name' => ( $a['adr_firstname'].' '.$a['adr_lastname'] ?: '' ).( ($a['adr_firstname'] || $a['adr_lastname']) && $a['society'] ? ', ' : '' ).( $a['society'] ?: '' ),
                        'is_sync' => $a['is_sync'],
                        'is_deleted' => false
                    );
        
                }
            }

            $reports[] = array(
                'id' => $call['_id'],
                'created' => $call['gcl_date_created'],
                'author' => $author,
				'comments' => $call['gcl_comment']
            );
        }
    }

    // Cette fonction assure un tri personnalisé des éléments du tableau reports en fonction de la date de création
    // décroissante du rapport. Les arguments sont standardisés par la fonction uasort et contiennent les valeurs à comparer
    function sortReports( $a, $b ){
        $dta = new DateTime( dateheureparse( $a['created'] ) );
        $dtb = new DateTime( dateheureparse( $b['created'] ) );
        
        if( $dta>$dtb ){
            return -1;
        }elseif( $dta<$dtb ){
            return 1;
        }
        return 0;
    }
    uasort( $reports, 'sortReports' );
    $count_reports = sizeof( $reports );

?>

<h3><?php print _('Rapports de visite et d\'appels téléphoniques') ?> (<?php print ria_number_format( $count_reports ); ?>)</h3>

<table id="tb-reports-pres-produits" class="checklist ui-sortable">
    <thead>
        <tr>
            <th id="reports-desc"><?php print _('Commentaires'); ?></th>
            <th id="reports-created"><?php print _('Créé le')?></th>
            <th id="reports-author"><?php print _('Auteur')?></th>
            <th id="reports-id"><?php print _('Rapport n°')?></th>
        </tr>
    </thead>
    <tbody>
        <?php if( !$count_reports ){ ?>
            <tr><td colspan="4"><?php print _('Aucun rapport de visite ou d\'appel n\'a encore été saisi'); ?></td></tr>
        <?php }else{ ?>
            <?php foreach( $reports as $key => $report ){ ?>
            <tr>
                <td headers="report-desc"><?php print nl2br( htmlspecialchars($report['comments']) ); ?></td>
                <td headers="reports-created"><?php print ria_date_format($report['created']); ?></td>
                <td headers="reports-author">
                <?php
                    $a = $report['author'];
                    print '
                        '.( $a['id'] > 0 && !$a['is_deleted'] ? '<a href="/admin/customers/edit.php?usr='.$a['id'].'">' : '' ).'
                        '.view_usr_is_sync( $a ).' '.htmlspecialchars( $a['name'] ).'
                        '.( $a['id'] > 0 && !$a['is_deleted'] ? '</a>' : '' ).'
                    ';
                ?>
                </td>
                <td headers="reports-id">
                    <?php 
                    $ref = str_pad( $report['id'], '6', '0', STR_PAD_LEFT );
                    if( !isset( $report['type_id'] ) ){
                        $url = "/admin/fdv/reports/calls/view.php?id={$report['id']}";
                        print '<a href="'.$url.'">'.$ref.'</a>';

                    }else{
                        print $ref;

                    }
                    ?>
                </td>
            </tr>
            <?php } ?>
        <?php } ?>
    </tbody>
</table>