<?php
/**
 * InputFileReadConfiguration
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * InputFileReadConfiguration Class Doc Comment
 *
 * @category Class
 * @description Describe how to read the file. If FileFormatStrategy is CSV, csvFileReadProperties is required. Otherwise the xmlFileReadProperties is required.
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class InputFileReadConfiguration implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'inputFileReadConfiguration';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'format' => '\Swagger\Client\Model\FileFormatStrategy',
        'encoding_type_name' => 'string',
        'culture_name' => 'string',
        'csv_file_read_properties' => '\Swagger\Client\Model\InputFileReadCsvConfiguration',
        'xml_file_read_properties' => '\Swagger\Client\Model\InputFileReadXmlConfiguration'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'format' => null,
        'encoding_type_name' => null,
        'culture_name' => null,
        'csv_file_read_properties' => null,
        'xml_file_read_properties' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'format' => 'format',
        'encoding_type_name' => 'encodingTypeName',
        'culture_name' => 'cultureName',
        'csv_file_read_properties' => 'csvFileReadProperties',
        'xml_file_read_properties' => 'xmlFileReadProperties'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'format' => 'setFormat',
        'encoding_type_name' => 'setEncodingTypeName',
        'culture_name' => 'setCultureName',
        'csv_file_read_properties' => 'setCsvFileReadProperties',
        'xml_file_read_properties' => 'setXmlFileReadProperties'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'format' => 'getFormat',
        'encoding_type_name' => 'getEncodingTypeName',
        'culture_name' => 'getCultureName',
        'csv_file_read_properties' => 'getCsvFileReadProperties',
        'xml_file_read_properties' => 'getXmlFileReadProperties'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['format'] = isset($data['format']) ? $data['format'] : null;
        $this->container['encoding_type_name'] = isset($data['encoding_type_name']) ? $data['encoding_type_name'] : 'UTF-8';
        $this->container['culture_name'] = isset($data['culture_name']) ? $data['culture_name'] : '';
        $this->container['csv_file_read_properties'] = isset($data['csv_file_read_properties']) ? $data['csv_file_read_properties'] : null;
        $this->container['xml_file_read_properties'] = isset($data['xml_file_read_properties']) ? $data['xml_file_read_properties'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['format'] === null) {
            $invalidProperties[] = "'format' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['format'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets format
     *
     * @return \Swagger\Client\Model\FileFormatStrategy
     */
    public function getFormat()
    {
        return $this->container['format'];
    }

    /**
     * Sets format
     *
     * @param \Swagger\Client\Model\FileFormatStrategy $format format
     *
     * @return $this
     */
    public function setFormat($format)
    {
        $this->container['format'] = $format;

        return $this;
    }

    /**
     * Gets encoding_type_name
     *
     * @return string
     */
    public function getEncodingTypeName()
    {
        return $this->container['encoding_type_name'];
    }

    /**
     * Sets encoding_type_name
     *
     * @param string $encoding_type_name The encoding type. UTF-8 by default.
     *
     * @return $this
     */
    public function setEncodingTypeName($encoding_type_name)
    {
        $this->container['encoding_type_name'] = $encoding_type_name;

        return $this;
    }

    /**
     * Gets culture_name
     *
     * @return string
     */
    public function getCultureName()
    {
        return $this->container['culture_name'];
    }

    /**
     * Sets culture_name
     *
     * @param string $culture_name The culture name of the file.  (i.e. fr-FR). If null then Invariant culture will be used.
     *
     * @return $this
     */
    public function setCultureName($culture_name)
    {
        $this->container['culture_name'] = $culture_name;

        return $this;
    }

    /**
     * Gets csv_file_read_properties
     *
     * @return \Swagger\Client\Model\InputFileReadCsvConfiguration
     */
    public function getCsvFileReadProperties()
    {
        return $this->container['csv_file_read_properties'];
    }

    /**
     * Sets csv_file_read_properties
     *
     * @param \Swagger\Client\Model\InputFileReadCsvConfiguration $csv_file_read_properties csv_file_read_properties
     *
     * @return $this
     */
    public function setCsvFileReadProperties($csv_file_read_properties)
    {
        $this->container['csv_file_read_properties'] = $csv_file_read_properties;

        return $this;
    }

    /**
     * Gets xml_file_read_properties
     *
     * @return \Swagger\Client\Model\InputFileReadXmlConfiguration
     */
    public function getXmlFileReadProperties()
    {
        return $this->container['xml_file_read_properties'];
    }

    /**
     * Sets xml_file_read_properties
     *
     * @param \Swagger\Client\Model\InputFileReadXmlConfiguration $xml_file_read_properties xml_file_read_properties
     *
     * @return $this
     */
    public function setXmlFileReadProperties($xml_file_read_properties)
    {
        $this->container['xml_file_read_properties'] = $xml_file_read_properties;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


