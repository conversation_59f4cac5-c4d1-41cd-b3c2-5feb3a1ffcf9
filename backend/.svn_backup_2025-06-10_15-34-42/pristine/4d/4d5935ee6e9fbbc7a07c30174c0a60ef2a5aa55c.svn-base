<?php
namespace Login;

use InvalidArgumentException;

class Password
{
	/**
	 * Mot de passe
	 *
	 * @var string $password
	 */
	protected $password;
	/**
	 * Longueur du mot de passe
	 *
	 * @var integer $length
	 */
	protected $length;
	/**
	 * Regex pour vérifier le mot de passe
	 *
	 * @var string $regex
	 */
	protected $regex;
	/**
	 * Si on dois vérifier si le regex valid la forme correcte ou la forme incorrecte du mot de passe
	 *
	 * @var boolean $match_valid
	 */
	protected $match_valid = true;
	/**
	 * Message d'erreur a afficher si le mot de passe est invalid
	 *
	 * @var string $error_message
	 */
	protected $error_message;
	/**
	 * constructeur initialisation du mot de passe
	 *
	 * @param string $password Mot de passe
	 */
	public function __construct($password)
	{
		global $config;
		$this->setPassword($password);

		if( !isset($config['password_regex']) ){
			$config['password_regex'] = '^[a-zA-Z0-9\-\_\=\.]{6,}$';
		}

		if( !isset($config['password_regex_match_valid']) ){
			$config['password_regex_match_valid'] = true;
		}

		if( !isset($config['password_error_message']) ){
			$config['password_error_message'] = 'Le mot de passe saisi n\'est pas reconnu comme valide. Le minimum est de 6 caractères. Les caractères autorisés sont les suivants : toutes les lettres, les chiffres, le caractère de soulignement, le tiret et le signe égal.';
		}

		$this->regex = $config['password_regex'];
		$this->match_valid = $config['password_regex_match_valid'];
		$this->error_message = $config['password_error_message'];
	}
	/**
	 * Vérifie si le mot de passe est valide
	 *
	 * @param integer $max_length Taille maximum du mot de passe
	 * @return boolean
	 */
	public function isValid($max_length=32)
	{
		if ( $this->length > 32 && $this->length > $max_length) {
			return false;
		}

		$is_valid = preg_match( "/".$this->regex."/", $this->password )>0;

		if ($this->match_valid) {
			return $is_valid;
		}
		return !$is_valid;
	}
	/**
	 * Récupère le mot de passe
	 *
	 * @return string
	 */
	public function getPassword()
	{
		return $this->password;
	}
	/**
	 * Récupère la longueur du mot de passe
	 *
	 * @return integer
	 */
	public function getLength()
	{
		return $this->length;
	}
	/**
	 * Vérifie si 2 mot de passe sont identique
	 *
	 * @param Password $Password
	 * @return boolean
	 */
	public function isSameAs(Password $Password)
	{
		return $this->password === $Password->getPassword();
	}
	/**
	 * Retourne le message d'erreur
	 *
	 * @return string
	 */
	public function getErreurMessage()
	{
		return $this->error_message;
	}
	/**
	 * Permet d'initialisé le mot de passe
	 *
	 * @param string $password Mot de passe
	 * @return void
	 * @throws InvalidArgumentException si le mot de passe n'est pas une chaine de caractère
	 */
	private function setPassword($password)
	{
		if (!is_string($password)) {
			throw new InvalidArgumentException("password must be a string");
		}

		$this->password = trim($password);
		$this->length = strlen($this->password);
	}
}
