<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/datetime.proto

namespace GPBMetadata\Google\Type;

class Datetime
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0aa8030a1a676f6f676c652f747970652f6461746574696d652e70726f74" .
            "6f120b676f6f676c652e7479706522e0010a084461746554696d65120c0a" .
            "0479656172180120012805120d0a056d6f6e7468180220012805120b0a03" .
            "646179180320012805120d0a05686f757273180420012805120f0a076d69" .
            "6e75746573180520012805120f0a077365636f6e6473180620012805120d" .
            "0a056e616e6f73180720012805122f0a0a7574635f6f6666736574180820" .
            "01280b32192e676f6f676c652e70726f746f6275662e4475726174696f6e" .
            "4800122a0a0974696d655f7a6f6e6518092001280b32152e676f6f676c65" .
            "2e747970652e54696d655a6f6e654800420d0a0b74696d655f6f66667365" .
            "7422270a0854696d655a6f6e65120a0a026964180120012809120f0a0776" .
            "657273696f6e18022001280942690a0f636f6d2e676f6f676c652e747970" .
            "65420d4461746554696d6550726f746f50015a3c676f6f676c652e676f6c" .
            "616e672e6f72672f67656e70726f746f2f676f6f676c65617069732f7479" .
            "70652f6461746574696d653b6461746574696d65f80101a2020347545062" .
            "0670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

