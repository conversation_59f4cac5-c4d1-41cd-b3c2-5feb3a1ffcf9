INSERT INTO img_images_objects 
    (imo_tnt_id, imo_cls_id, imo_img_id, imo_type_id, imo_obj_id_0, imo_obj_id_1, imo_obj_id_2, imo_pos, imo_date_created, imo_date_modified, imo_date_deleted, imo_alt, imo_publish, imo_is_main)
SELECT img_tnt_id, 1, img_id, NULL, img_prd_id, 0, 0, img_pos, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, NULL, NULL, img_publish, IF(prd_img_id = img_id, 1, 0)
FROM prd_images
    LEFT JOIN prd_products on prd_tnt_id = img_tnt_id and prd_id = img_prd_id;

INSERT INTO img_images_objects
    (imo_tnt_id, imo_cls_id, imo_img_id, imo_type_id, imo_obj_id_0, imo_obj_id_1, imo_obj_id_2, imo_pos, imo_date_created, imo_date_modified, imo_date_deleted, imo_alt, imo_publish, imo_is_main)
SELECT prd_tnt_id, 1, prd_img_id, NULL, prd_id, 0, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, NULL, NULL, 1, 1
FROM prd_products
    left join img_images_objects on imo_tnt_id = prd_tnt_id and imo_cls_id = 1 and imo_obj_id_0 = prd_id and prd_img_id = imo_img_id
where imo_id is null and prd_img_id is not null;