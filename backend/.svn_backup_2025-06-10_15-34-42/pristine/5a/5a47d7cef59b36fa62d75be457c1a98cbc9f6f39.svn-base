<documentation title="Doc Comment Alignment">
    <standard>
    <![CDATA[
    The asterisks in a doc comment should align, and there should be one space between the asterisk and tags.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Asterisks are aligned.">
        <![CDATA[
/**
<em> </em>* @see foo()
<em> </em>*/
]]>
        </code>
        <code title="Invalid: Asterisks are not aligned.">
        <![CDATA[
/**
<em>  </em>* @see foo()
<em></em>*/
]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: One space between asterisk and tag.">
        <![CDATA[
/**
 *<em> </em>@see foo()
 */
]]>
        </code>
        <code title="Invalid: Incorrect spacing used.">
        <![CDATA[
/**
 *<em>  </em>@see foo()
 */
]]>
        </code>
    </code_comparison>
</documentation>
