<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators/ctr.priceminister.inc.php');
	
	$html = '';
	if( isset($_GET['alias']) && trim($_GET['alias'])!='' ){
		$params = ctr_catalogs_get_params( CTR_PRICEMINISTER, $_GET['prd'] );
	
		$attr = ctr_priceminister_product_type_template_get( $_GET['alias'] );
		if( is_array($attr) && sizeof($attr) ){
			$html .= '<p>'._('Afin que nous puissions exporter votre produit vers PriceMinister, vous devez renseigner toutes les informations ci-dessous avec une <span class="mandatory">*</span>.').'</p>';
			$html .= '<hr />';
			
			$count = 0;
			foreach( $attr['attribute'] as $k=>$a ){
				$type = isset($params['typeproduct']) ? $params['typeproduct'] : '';
				
				$valdef = isset($_GET['hiddenattr-'.$k]) ? $_GET['hiddenattr-'.$k] : (isset($params['template'][$k]) && $type==$_GET['alias'] ? $params['template'][$k] : '');
				
				if( $k=='codebarres' ){
					$valdef = prd_products_get_barcode( $_GET['prd'] );
				}
				
				$html .= '	<div class="elem">';
				$html .= '		<label for="attr-'.$count.'">';
				if( $a['mandatory'] ){
					$html .= '		<span class="mandatory">*</span>';
				}
				$html .= '		'.$a['label'].' :</label>';
				
				if( is_array($a['values']) && sizeof($a['values']) ){
					
					$html .= '	<select name="attr['.$k.']" id="attr-'.$count.'">';
					$html .= '		<option value="-1">&nbsp;</option>';
					foreach( $a['values'] as $val ){
						$html .= '	<option '.( $valdef==$val ? 'selected="selected"' : '' ).' value="'.htmlspecialchars($val).'">'.htmlspecialchars($val).'</option>';
					}
					$html .= '	</select>';
					
				} else {
					switch( $a['type'] ){
						case 'Boolean' :
							$html .= '<input '.( $valdef ? 'checked="checked"' : '' ).' type="radio" name="attr['.$k.']" id="attr-'.$count.'-y" value="1" />';
							$html .= '<label class="radio" for="attr-'.$count.'-y">'._('Oui').'</label>';
							$html .= '<input '.( !$valdef ? 'checked="checked"' : '' ).' type="radio" name="attr['.$k.']" id="attr-'.$count.'-n" value="0" />';
							$html .= '<label class="radio" for="attr-'.$count.'-n">'._('Non').'</label>';
							break;
						default :
							$html .= '<input class="text" type="text" name="attr['.$k.']" id="attr-'.$count.'" value="'.$valdef.'" />';
							break;
					}
				}
				
				$html .= '		<input type="hidden" name="label['.$k.']" id ="label-'.$count.'" value="'.$a['label'].'" />';
				$html .= '		<input type="hidden" name="mandatory['.$k.']" id ="mandatory-'.$count.'" value="'.$a['mandatory'].'" />';
				$html .= '		<div class="clear"></div>';
				$html .= '	</div>';
				
				$count++;
			}
		}
	}
	
	print $html;
