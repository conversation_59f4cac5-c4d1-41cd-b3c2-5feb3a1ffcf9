# CatalogStoreIndexLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**self** | [**\Swagger\Client\Model\LinksCatalogStoreIndexLink**](LinksCatalogStoreIndexLink.md) |  | 
**input_configuration** | [**\Swagger\Client\Model\LinksImportationGetManualUpdateLastInputConfigLink**](LinksImportationGetManualUpdateLastInputConfigLink.md) |  | [optional] 
**auto_import_info** | [**\Swagger\Client\Model\LinksAutoGetAutoImportConfigurationLink**](LinksAutoGetAutoImportConfigurationLink.md) |  | [optional] 
**importations** | [**\Swagger\Client\Model\LinksImportationGetReportingsLink**](LinksImportationGetReportingsLink.md) |  | [optional] 
**start_importation** | [**\Swagger\Client\Model\LinksImportationStartManualUpdateLink**](LinksImportationStartManualUpdateLink.md) |  | [optional] 
**catalog_columns** | [**\Swagger\Client\Model\LinksCatalogGetCatalogColumnsLink**](LinksCatalogGetCatalogColumnsLink.md) |  | [optional] 
**custom_columns** | [**\Swagger\Client\Model\LinksCatalogGetCustomColumnsLink**](LinksCatalogGetCustomColumnsLink.md) |  | [optional] 
**categories** | [**\Swagger\Client\Model\LinksCatalogGetCategoriesLink**](LinksCatalogGetCategoriesLink.md) |  | [optional] 
**products** | [**\Swagger\Client\Model\LinksCatalogGetProductsLink**](LinksCatalogGetProductsLink.md) |  | [optional] 
**random_products** | [**\Swagger\Client\Model\LinksCatalogGetRandomProductsLink**](LinksCatalogGetRandomProductsLink.md) |  | [optional] 
**compute_expression** | [**\Swagger\Client\Model\LinksCatalogComputeExpressionLink**](LinksCatalogComputeExpressionLink.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


