<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AD' => 'Andorra',
  'AE' => 'Veräinigti Arabischi Emirate',
  'AF' => 'Afganischtan',
  'AG' => 'Antigua und Barbuda',
  'AI' => 'Anguilla',
  'AL' => 'Albaanie',
  'AM' => 'Armeenie',
  'AO' => 'Angoola',
  'AQ' => 'Antarktis',
  'AR' => 'Argentiinie',
  'AS' => 'Amerikaanisch-Samoa',
  'AT' => 'Ööschtriich',
  'AU' => 'Auschtraalie',
  'AW' => 'Aruba',
  'AX' => 'Aaland-Insle',
  'AZ' => 'Aserbäidschan',
  'BA' => 'Bosnie und Herzegowina',
  'BB' => 'Barbados',
  'BD' => 'Bangladesch',
  'BE' => 'Belgie',
  'BF' => 'Burkina Faaso',
  'BG' => 'Bulgaarie',
  'BH' => 'Bachräin',
  'BI' => 'Burundi',
  'BJ' => 'Benin',
  'BL' => 'St. Barthelemi',
  'BM' => 'Bermuuda',
  'BN' => 'Brunäi Tarussalam',
  'BO' => 'Boliivie',
  'BR' => 'Brasilie',
  'BS' => 'Bahaamas',
  'BT' => 'Bhutan',
  'BW' => 'Botswana',
  'BY' => 'Wiissrussland',
  'BZ' => 'Belize',
  'CA' => 'Kanada',
  'CC' => 'Kokos-Insle',
  'CD' => 'Temokraatischi Republik Kongo',
  'CF' => 'Zentraalafrikaanischi Republik',
  'CG' => 'Kongo',
  'CH' => 'Schwiiz',
  'CI' => 'Elfebäiküschte',
  'CK' => 'Cook-Insle',
  'CL' => 'Tschile',
  'CM' => 'Kamerun',
  'CN' => 'Chiina',
  'CO' => 'Kolumbie',
  'CR' => 'Coschta Rica',
  'CU' => 'Kuba',
  'CV' => 'Kap Verde',
  'CX' => 'Wienachts-Insle',
  'CY' => 'Zypere',
  'CZ' => 'Tschechischi Republik',
  'DE' => 'Tüütschland',
  'DJ' => 'Tschibuuti',
  'DK' => 'Tänemark',
  'DM' => 'Tominica',
  'DO' => 'Tominikaanischi Republik',
  'DZ' => 'Algeerie',
  'EC' => 'Ecuador',
  'EE' => 'Eestland',
  'EG' => 'Ägüpte',
  'EH' => 'Weschtsahara',
  'ER' => 'Äritreea',
  'ES' => 'Schpanie',
  'ET' => 'Äthiopie',
  'FI' => 'Finnland',
  'FJ' => 'Fitschi',
  'FK' => 'Falkland-Insle',
  'FM' => 'Mikroneesie',
  'FO' => 'Färöer',
  'FR' => 'Frankriich',
  'GA' => 'Gabun',
  'GB' => 'Veräinigts Chönigriich',
  'GD' => 'Grenada',
  'GE' => 'Geoorgie',
  'GF' => 'Französisch-Guäjaana',
  'GG' => 'Gäärnsi',
  'GH' => 'Gaana',
  'GI' => 'Gibraltar',
  'GL' => 'Gröönland',
  'GM' => 'Gambia',
  'GN' => 'Gineea',
  'GP' => 'Guadälup',
  'GQ' => 'Äquatoriaalgineea',
  'GR' => 'Griecheland',
  'GS' => 'Süüdgeorgie und d’süüdlichi Sändwitsch-Insle',
  'GT' => 'Guatemaala',
  'GU' => 'Guam',
  'GW' => 'Gineea-Bissau',
  'GY' => 'Guäjaana',
  'HK' => 'Sonderverwaltigszone Hongkong',
  'HN' => 'Honduras',
  'HR' => 'Kroaazie',
  'HT' => 'Haiti',
  'HU' => 'Ungarn',
  'ID' => 'Indoneesie',
  'IE' => 'Irland',
  'IL' => 'Israel',
  'IM' => 'Insle vo Män',
  'IN' => 'Indie',
  'IO' => 'Britischs Territoorium im Indische Oozean',
  'IQ' => 'Iraak',
  'IR' => 'Iraan',
  'IS' => 'Iisland',
  'IT' => 'Itaalie',
  'JE' => 'Dschörsi',
  'JM' => 'Dschamäika',
  'JO' => 'Jordaanie',
  'JP' => 'Japan',
  'KE' => 'Keenia',
  'KG' => 'Kirgiisischtan',
  'KH' => 'Kambodscha',
  'KI' => 'Kiribaati',
  'KM' => 'Komoore',
  'KN' => 'St. Kitts und Niuwis',
  'KP' => 'Demokraatischi Volksrepublik Koreea',
  'KR' => 'Republik Koreea',
  'KW' => 'Kuwäit',
  'KY' => 'Käimän-Insle',
  'KZ' => 'Kasachschtan',
  'LA' => 'Laaos',
  'LB' => 'Libanon',
  'LC' => 'St. Lutschiia',
  'LI' => 'Liächteschtäi',
  'LK' => 'Schri Lanka',
  'LR' => 'Libeeria',
  'LS' => 'Lesooto',
  'LT' => 'Littaue',
  'LU' => 'Luxemburg',
  'LV' => 'Lettland',
  'LY' => 'Lüübie',
  'MA' => 'Marokko',
  'MC' => 'Monaco',
  'MD' => 'Republik Moldau',
  'ME' => 'Monteneegro',
  'MF' => 'St. Martin',
  'MG' => 'Madagaschkar',
  'MH' => 'Marshallinsle',
  'MK' => 'Mazedoonie',
  'ML' => 'Maali',
  'MM' => 'Myanmar (Burma)',
  'MN' => 'Mongolei',
  'MO' => 'Sonderverwaltigszone Macao',
  'MP' => 'Nördlichi Mariaane',
  'MQ' => 'Martinigg',
  'MR' => 'Mauretaanie',
  'MS' => 'Moosörrat',
  'MT' => 'Malta',
  'MU' => 'Mauriizius',
  'MV' => 'Malediiwe',
  'MW' => 'Malaawi',
  'MX' => 'Mexiko',
  'MY' => 'Maläisia',
  'MZ' => 'Mosambik',
  'NA' => 'Namiibia',
  'NC' => 'Nöikaledoonie',
  'NE' => 'Niger',
  'NF' => 'Norfolk-Insle',
  'NG' => 'Nigeeria',
  'NI' => 'Nicaraagua',
  'NL' => 'Holland',
  'NO' => 'Norweege',
  'NP' => 'Neepal',
  'NR' => 'Nauru',
  'NU' => 'Niue',
  'NZ' => 'Nöiseeland',
  'OM' => 'Omaan',
  'PA' => 'Panama',
  'PE' => 'Peru',
  'PF' => 'Französisch-Polineesie',
  'PG' => 'Papua-Neuguinea',
  'PH' => 'Philippiine',
  'PK' => 'Pakischtan',
  'PL' => 'Poole',
  'PM' => 'St. Pierr und Miggelo',
  'PN' => 'Pitggäärn',
  'PR' => 'Puerto Riggo',
  'PS' => 'Paläschtinänsischi Gebiet',
  'PT' => 'Portugal',
  'PW' => 'Palau',
  'PY' => 'Paraguai',
  'QA' => 'Ggatar',
  'RE' => 'Reünioon',
  'RO' => 'Rumäänie',
  'RS' => 'Särbie',
  'RU' => 'Russland',
  'RW' => 'Ruanda',
  'SA' => 'Saudi-Araabie',
  'SB' => 'Salomoone',
  'SC' => 'Seischälle',
  'SD' => 'Sudan',
  'SE' => 'Schweede',
  'SG' => 'Singapuur',
  'SH' => 'St. Helena',
  'SI' => 'Sloweenie',
  'SJ' => 'Svalbard und Jaan Määie',
  'SK' => 'Slowakäi',
  'SL' => 'Sierra Leoone',
  'SM' => 'San Mariino',
  'SN' => 'Senegal',
  'SO' => 'Somaalie',
  'SR' => 'Surinam',
  'ST' => 'Sao Tome und Prinssipe',
  'SV' => 'El Salvador',
  'SY' => 'Süürie',
  'SZ' => 'Swasiland',
  'TC' => 'Törks- und Gaiggos-Insle',
  'TD' => 'Tschad',
  'TF' => 'Französischi Süüd- und Antarktisgebiet',
  'TG' => 'Toogo',
  'TH' => 'Thailand',
  'TJ' => 'Tadschikischtan',
  'TK' => 'Tokelau',
  'TL' => 'Oschttimor',
  'TM' => 'Turkmeenischtan',
  'TN' => 'Tuneesie',
  'TO' => 'Tonga',
  'TR' => 'Türggei',
  'TT' => 'Trinidad und Tobaago',
  'TV' => 'Tuvalu',
  'TW' => 'Taiwan',
  'TZ' => 'Tansaniia',
  'UA' => 'Ukraiine',
  'UG' => 'Uganda',
  'UM' => 'Amerikanisch-Ozeaanie',
  'US' => 'Veräinigti Schtaate',
  'UY' => 'Uruguay',
  'UZ' => 'Uschbeekischtan',
  'VA' => 'Vatikanstadt',
  'VC' => 'St. Vincent und d’Grönadiine',
  'VE' => 'Venezueela',
  'VG' => 'Britischi Jungfere-Insle',
  'VI' => 'Amerikaanischi Jungfere-Insle',
  'VN' => 'Wietnam',
  'VU' => 'Wanuatu',
  'WF' => 'Wallis und Futuuna',
  'WS' => 'Samooa',
  'YE' => 'Jeeme',
  'YT' => 'Majott',
  'ZA' => 'Süüdafrika',
  'ZM' => 'Sambia',
  'ZW' => 'Simbabwe',
);
