<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/5.7/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         backupGlobals="false"
         beStrictAboutCoversAnnotation="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutTestsThatDoNotTestAnything="false"
         beStrictAboutTodoAnnotatedTests="true"
         verbose="true">
    <testsuite name="default">
        <directory>tests</directory>
    </testsuite>

    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">src</directory>
            <exclude>
                <directory suffix=".php">src/Psalm/Issue/</directory>
                <directory suffix=".php">src/Psalm/Stubs/</directory>
                <file>src/command_functions.php</file>
                <file>src/psalm.php</file>
                <file>src/psalter.php</file>
                <file>src/Psalm/CallMap.php</file>
                <file>src/Psalm/Fork/Pool.php</file>
                <file>src/Psalm/PropertyMap.php</file>
                <file>src/Psalm/Provider/Cache/NoParserCacheProvider.php</file>
                <file>src/Psalm/Provider/ParserCacheProvider.php</file>
            </exclude>
        </whitelist>
    </filter>

    <logging>
        <log type="coverage-clover" target="build/logs/clover.xml"/>
        <log type="coverage-html" target="build/logs/phpunit-html/"/>
    </logging>
</phpunit>

