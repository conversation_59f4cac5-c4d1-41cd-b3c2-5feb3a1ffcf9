<?php

	/**	\file ajax-position-update.php
	 *	Ce fichier permet la mise à jour de la position d'un article dans la gestion de contenus.
	 *	Il est appelé en Ajax avec les paramètres obligatoires suivants :
	 *	- source : identifiant de l'objet source
	 *	- target : identifiant de l'objet cible
	 *	- action : soit "before" soit "after"
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_CMS_MOVE');

	if (! isset($_POST['source'], $_POST['target'], $_POST['action'])) throw new Exception('Erreur post !');
	
	$source = $_POST['source'];
	$target = $_POST['target'];
	$action = $_POST['action'];
	
	require_once('cms.inc.php');
	
	$response = array('success' => cms_categories_position_update($source, $target, $action));
	
	print json_encode($response);
	exit;

