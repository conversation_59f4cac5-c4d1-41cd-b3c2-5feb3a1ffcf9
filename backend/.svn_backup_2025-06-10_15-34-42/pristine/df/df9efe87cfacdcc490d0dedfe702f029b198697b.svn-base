<?php

set_include_path(dirname(__FILE__) . '/../include/');

print "L'utilisation de ce fichier n'est pas recommandé. Si vraiment nécessaire, décommentez le code ci-dessous.\n";

/*
require_once('db.inc.php');

// liste des taux de TVA ayant des doublons
$sql = '
	select ptv_tnt_id as tnt, ptv_prd_id as prd, ptv_cac_id as cac
	from riashop.prc_tvas
	where ptv_date_deleted is null
	group by ptv_tnt_id, ptv_prd_id, ptv_cac_id
	having count(*) > 1
';

$rtva = ria_mysql_query($sql);

if( ria_mysql_errno() ){
	print "Erreur au chargement : ".mysql_error()."\n\n".$sql."\n";
	exit;
}

print ria_mysql_num_rows($rtva)." taux de TVA en doublon\n";

while( $tva = ria_mysql_fetch_array($rtva) ){
	
	// pour chaque taux, on n'en conserve qu'un
	$sql2 = '
		select ptv_tnt_id as tnt, ptv_id as id
		from riashop.prc_tvas
		where ptv_date_deleted is null
	';
	$sql2 .= ' and ptv_prd_id = '.$tva['prd'];
	$sql2 .= ' and ptv_tnt_id = '.$tva['tnt'];
	$sql2 .= ' and ifnull(ptv_cac_id, 0) = '.( is_numeric($tva['cac']) && $tva['cac'] >= 0 ? $tva['cac'] : '0' );
	$sql2 .= ' order by ptv_id desc';
	
	$rtva2 = ria_mysql_query($sql2);
	
	if( ria_mysql_errno() ){
		print "Erreur au chargement d'une ligne : ".mysql_error()."\n\n".$sql2."\n";
		continue;
	}
	
	print (ria_mysql_num_rows($rtva2) - 1)." taux à supprimer [TNT ".$tva['tnt'].", PRD ".$tva['prd'].", CAC ".$tva['cac']."]\n";
	
	$first_row = true;
	while( $tva2 = ria_mysql_fetch_array($rtva2) ){
		if( !$first_row ){
			
			if( !ria_mysql_query('update riashop.prc_tvas set ptv_date_deleted = now() where ptv_tnt_id = '.$tva2['tnt'].' and ptv_id = '.$tva2['id']) ){
				print "Erreur lors de la suppression (".$tva2['tnt'].", ".$tva2['id'].")\n";
			}
			
		}
		
		$first_row = false;
	}
}
*/

print "Fin de traitement.\n";

