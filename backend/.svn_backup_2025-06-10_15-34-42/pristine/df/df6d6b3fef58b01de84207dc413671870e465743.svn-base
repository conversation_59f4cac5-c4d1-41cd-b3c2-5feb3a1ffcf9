<?php
/**
 * \defgroup delivery_holiday Jours fériés d'expédition 
 * \ingroup delivery
 * @{
 * \page api-delivery-holiday-get Chargement 
 *
 * Cette fonction permet de récupérer les jours fériés
 *
 *	 \code
 *		GET /delivery/holiday/
 *	 \endcode
 *	
 * @return json un tableau des jours fériés sous la forme suivante pour chaque élément :
 * \code{.json}
 *	 {
 *		"date": date du jour fériés au format YYYY-MM-DD,
 *		"exp": 0 si les expédition n'ont pas lieu, 1 dans le cas contraire
 *	 },
 * \endcode
 * @}
*/
switch( $method ){
	case 'get':
		$result = true;

		$rholiday = dlv_holidays_get();
		if( $rholiday && ria_mysql_num_rows($rholiday) ){
			while( $holiday = ria_mysql_fetch_assoc($rholiday) ){
				$content[] = $holiday;
			}
		}
		break;
}
