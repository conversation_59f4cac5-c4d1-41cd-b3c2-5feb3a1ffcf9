<?php
namespace SchemaDotOrg\Tags;
use SchemaDotOrg\Tags\TagInterface;
/**
 * \ingroup SchemaTag
 *
 * @{
 */
/**
 * \brief Cette classe correspond au tag de base don tous les tag doivent hérité
 */
class TagBase implements TagInterface {
	/**
	 * Type de tag
	 *
	 * @var string $type
	 */
	protected $type;

	/**
	 * Tableau avec les champs pour ce tag
	 *
	 * @var array $fields
	 */
	protected $fields = array();

	/**
	 * Permet de retourner le type de tag
	 *
	 * @return string le type de tag
	 */
	public function type(){
		return $this->type;
	}

	/**
	 * Permet d'ajouter un champ au tag
	 *
	 * @param string $name Le nom du champ
	 * @param mixed $value La valeur du champ
	 * @return self retourne l'instance
	 */
	public function addField($name, $value){
		$this->fields[$name] = $value;

		return $this;
	}

	/**
	 * Cette fonction permet de retourner la liste des champs
	 *
	 * @return array le tableau des champs
	 */
	public function getFields(){
		$fields = $this->serializeField($this->fields);
        return array_merge(
			array('@type' => $this->type()),
			$fields
		);
	}

	/**
	 * Permet de formaté le champ si c'est un tableau ou un tag
	 *
	 * @param array|string|TagInterface $field un champ du tag
	 * @return array|string Retourne soit la valeur au format texte ou un tableau si le champ est un tag
	 */
	protected function serializeField($field)
    {
        if (is_array($field)) {
            return array_map(array($this, 'serializeField'), $field);
        }
        if ($field instanceof TagInterface) {
            $field = $field->getFields();
        }
        if ($field instanceof DateTime) {
            $field = $field->format('Y-m-d');
        }

        return $field;
	}
	

	protected function formatPhone($phone){

		// élimination de tout caractère non numéric
		$phone = preg_replace('/[^0-9]+/', '', $phone);

		//Garder les 9 derniers chiffres
		$phone = substr($phone, -9);

		$motif = '+33\1\2\3\4\5';

		$phone = preg_replace(
			'/(\d{1})(\d{2})(\d{2})(\d{2})(\d{2})/',
			$motif,
			$phone
		);

		return $phone;
	}
}
// @}
