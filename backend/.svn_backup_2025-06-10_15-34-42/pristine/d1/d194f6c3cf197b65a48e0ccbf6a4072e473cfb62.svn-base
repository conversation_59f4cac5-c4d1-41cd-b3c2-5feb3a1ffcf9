<?xml version="1.0" encoding="utf-8"?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://symfony.com/schema/dic/services https://symfony.com/schema/dic/services/services-1.0.xsd">
  <parameters>
    <parameter key="baz_class">BazClass</parameter>
    <parameter key="foo_class">Bar\FooClass</parameter>
    <parameter key="foo">bar</parameter>
  </parameters>
  <services>
    <service id="service_container" class="Symfony\Component\DependencyInjection\ContainerInterface" public="true" synthetic="true"/>
    <service id="foo" class="Bar\FooClass" public="true">
      <tag name="foo" foo="foo"/>
      <tag name="foo" bar="bar" baz="baz"/>
      <argument>foo</argument>
      <argument type="service" id="foo.baz"/>
      <argument type="collection">
        <argument key="%foo%">foo is %foo%</argument>
        <argument key="foobar">%foo%</argument>
      </argument>
      <argument>true</argument>
      <argument type="service" id="service_container"/>
      <property name="foo">bar</property>
      <property name="moo" type="service" id="foo.baz"/>
      <property name="qux" type="collection">
        <property key="%foo%">foo is %foo%</property>
        <property key="foobar">%foo%</property>
      </property>
      <call method="setBar">
        <argument type="service" id="bar"/>
      </call>
      <call method="initialize"/>
      <factory class="Bar\FooClass" method="getInstance"/>
      <configurator function="sc_configure"/>
    </service>
    <service id="foo.baz" class="%baz_class%" public="true">
      <factory class="%baz_class%" method="getInstance"/>
      <configurator class="%baz_class%" method="configureStatic1"/>
    </service>
    <service id="bar" class="Bar\FooClass" public="true">
      <argument>foo</argument>
      <argument type="service" id="foo.baz"/>
      <argument>%foo_bar%</argument>
      <configurator service="foo.baz" method="configure"/>
    </service>
    <service id="foo_bar" class="%foo_class%" shared="false" public="true">
      <argument type="service" id="deprecated_service"/>
    </service>
    <service id="method_call1" class="Bar\FooClass" public="true">
      <file>%path%foo.php</file>
      <call method="setBar">
        <argument type="service" id="foo"/>
      </call>
      <call method="setBar">
        <argument type="service" id="foo2" on-invalid="null"/>
      </call>
      <call method="setBar">
        <argument type="service" id="foo3" on-invalid="ignore"/>
      </call>
      <call method="setBar">
        <argument type="service" id="foobaz" on-invalid="ignore"/>
      </call>
      <call method="setBar">
        <argument type="expression">service("foo").foo() ~ (container.hasParameter("foo") ? parameter("foo") : "default")</argument>
      </call>
    </service>
    <service id="foo_with_inline" class="Foo" public="true">
      <call method="setBar">
        <argument type="service" id="inlined"/>
      </call>
    </service>
    <service id="inlined" class="Bar" public="false">
      <property name="pub">pub</property>
      <call method="setBaz">
        <argument type="service" id="baz"/>
      </call>
    </service>
    <service id="baz" class="Baz" public="true">
      <call method="setFoo">
        <argument type="service" id="foo_with_inline"/>
      </call>
    </service>
    <service id="request" class="Request" public="true" synthetic="true"/>
    <service id="configurator_service" class="ConfClass" public="false">
      <call method="setFoo">
        <argument type="service" id="baz"/>
      </call>
    </service>
    <service id="configured_service" class="stdClass" public="true">
      <configurator service="configurator_service" method="configureStdClass"/>
    </service>
    <service id="configurator_service_simple" class="ConfClass" public="false">
      <argument>bar</argument>
    </service>
    <service id="configured_service_simple" class="stdClass" public="true">
      <configurator service="configurator_service_simple" method="configureStdClass"/>
    </service>
    <service id="decorated" class="stdClass" public="true"/>
    <service id="decorator_service" class="stdClass" public="true" decorates="decorated"/>
    <service id="decorator_service_with_name" class="stdClass" public="true" decorates="decorated" decoration-inner-name="decorated.pif-pouf"/>
    <service id="deprecated_service" class="stdClass" public="true">
      <deprecated>The "%service_id%" service is deprecated. You should stop using it, as it will soon be removed.</deprecated>
    </service>
    <service id="new_factory" class="FactoryClass" public="false">
      <property name="foo">bar</property>
    </service>
    <service id="factory_service" class="Bar" public="true">
      <factory service="foo.baz" method="getInstance"/>
    </service>
    <service id="new_factory_service" class="FooBarBaz" public="true">
      <property name="foo">bar</property>
      <factory service="new_factory" method="getInstance"/>
    </service>
    <service id="service_from_static_method" class="Bar\FooClass" public="true">
      <factory class="Bar\FooClass" method="getInstance"/>
    </service>
    <service id="factory_simple" class="SimpleFactoryClass" public="false">
      <argument>foo</argument>
      <deprecated>The "%service_id%" service is deprecated. You should stop using it, as it will soon be removed.</deprecated>
    </service>
    <service id="factory_service_simple" class="Bar" public="true">
      <factory service="factory_simple" method="getInstance"/>
    </service>
    <service id="lazy_context" class="LazyContext" public="true">
      <argument type="iterator">
        <argument key="k1" type="service" id="foo.baz"/>
        <argument key="k2" type="service" id="service_container"/>
      </argument>
      <argument type="iterator"/>
    </service>
    <service id="lazy_context_ignore_invalid_ref" class="LazyContext" public="true">
      <argument type="iterator">
        <argument type="service" id="foo.baz"/>
        <argument type="service" id="invalid" on-invalid="ignore"/>
      </argument>
      <argument type="iterator"/>
    </service>
    <service id="tagged_iterator_foo" class="Bar" public="false">
      <tag name="foo"/>
    </service>
    <service id="tagged_iterator" class="Bar" public="true">
      <argument type="tagged" tag="foo"/>
    </service>
    <service id="Psr\Container\ContainerInterface" alias="service_container" public="false"/>
    <service id="Symfony\Component\DependencyInjection\ContainerInterface" alias="service_container" public="false"/>
    <service id="alias_for_foo" alias="foo" public="true"/>
    <service id="alias_for_alias" alias="foo" public="true"/>
  </services>
</container>
