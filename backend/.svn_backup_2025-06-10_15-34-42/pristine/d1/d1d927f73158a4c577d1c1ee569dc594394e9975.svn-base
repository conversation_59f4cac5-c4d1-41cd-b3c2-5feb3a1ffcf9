<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/service_controller.proto

namespace Google\Api\Servicecontrol\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Servicecontrol\V1\CheckResponse\CheckInfo instead.
     * @deprecated
     */
    class CheckResponse_CheckInfo {}
}
class_exists(CheckResponse\CheckInfo::class);
@trigger_error('Google\Api\Servicecontrol\V1\CheckResponse_CheckInfo is deprecated and will be removed in the next major release. Use Google\Api\Servicecontrol\V1\CheckResponse\CheckInfo instead', E_USER_DEPRECATED);

