<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/v1/policy.proto

namespace Google\Cloud\Iam\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * The difference delta between two policies.
 *
 * Generated from protobuf message <code>google.iam.v1.PolicyDelta</code>
 */
class PolicyDelta extends \Google\Protobuf\Internal\Message
{
    /**
     * The delta for Bindings between two policies.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.BindingDelta binding_deltas = 1;</code>
     */
    private $binding_deltas;
    /**
     * The delta for AuditConfigs between two policies.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.AuditConfigDelta audit_config_deltas = 2;</code>
     */
    private $audit_config_deltas;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Cloud\Iam\V1\BindingDelta[]|\Google\Protobuf\Internal\RepeatedField $binding_deltas
     *           The delta for Bindings between two policies.
     *     @type \Google\Cloud\Iam\V1\AuditConfigDelta[]|\Google\Protobuf\Internal\RepeatedField $audit_config_deltas
     *           The delta for AuditConfigs between two policies.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Iam\V1\Policy::initOnce();
        parent::__construct($data);
    }

    /**
     * The delta for Bindings between two policies.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.BindingDelta binding_deltas = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getBindingDeltas()
    {
        return $this->binding_deltas;
    }

    /**
     * The delta for Bindings between two policies.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.BindingDelta binding_deltas = 1;</code>
     * @param \Google\Cloud\Iam\V1\BindingDelta[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBindingDeltas($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\Iam\V1\BindingDelta::class);
        $this->binding_deltas = $arr;

        return $this;
    }

    /**
     * The delta for AuditConfigs between two policies.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.AuditConfigDelta audit_config_deltas = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getAuditConfigDeltas()
    {
        return $this->audit_config_deltas;
    }

    /**
     * The delta for AuditConfigs between two policies.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.AuditConfigDelta audit_config_deltas = 2;</code>
     * @param \Google\Cloud\Iam\V1\AuditConfigDelta[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setAuditConfigDeltas($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\Iam\V1\AuditConfigDelta::class);
        $this->audit_config_deltas = $arr;

        return $this;
    }

}

