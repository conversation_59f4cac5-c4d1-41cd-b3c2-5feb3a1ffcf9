<documentation title="Self Member Reference">
    <standard>
    <![CDATA[
    The self keyword should be used instead of the current class name, should be lowercase, and should not have spaces before or after it.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Lowercase self used.">
        <![CDATA[
<em>self</em>::foo();
]]>
        </code>
        <code title="Invalid: Uppercase self used.">
        <![CDATA[
<em>SELF</em>::foo();
]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Correct spacing used.">
        <![CDATA[
self<em></em>::<em></em>foo();
]]>
        </code>
        <code title="Invalid: Incorrect spacing used.">
        <![CDATA[
self<em> </em>::<em> </em>foo();
]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Self used as reference.">
        <![CDATA[
class Foo
{
    public static function bar()
    {
    }

    public static function baz()
    {
        <em>self</em>::bar();
    }
}
]]>
        </code>
        <code title="Invalid: Local class name used as reference.">
        <![CDATA[
class <em>Foo</em>
{
    public static function bar()
    {
    }

    public static function baz()
    {
        <em>Foo</em>::bar();
    }
}
]]>
        </code>
    </code_comparison>
</documentation>
