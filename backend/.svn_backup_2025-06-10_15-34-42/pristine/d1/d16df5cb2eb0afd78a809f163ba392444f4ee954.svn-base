# ChannelCatalogColumnMappingWithName

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**channel_column_id** | [**\Swagger\Client\Model\BeezUPCommonChannelColumnId**](BeezUPCommonChannelColumnId.md) |  | 
**channel_category_path** | [**\Swagger\Client\Model\BeezUPCommonChannelCategoryPath**](BeezUPCommonChannelCategoryPath.md) |  | [optional] 
**catalog_column_id** | [**\Swagger\Client\Model\BeezUPCommonCatalogColumnId**](BeezUPCommonCatalogColumnId.md) |  | [optional] 
**channel_column_name** | [**\Swagger\Client\Model\BeezUPCommonChannelColumnName**](BeezUPCommonChannelColumnName.md) |  | 
**channel_beez_up_column_name** | [**\Swagger\Client\Model\BeezUPCommonBeezUPColumnName**](BeezUPCommonBeezUPColumnName.md) |  | [optional] 
**catalog_column_name** | [**\Swagger\Client\Model\BeezUPCommonCatalogColumnUserName**](BeezUPCommonCatalogColumnUserName.md) |  | [optional] 
**catalog_beez_up_column_name** | [**\Swagger\Client\Model\BeezUPCommonBeezUPColumnName**](BeezUPCommonBeezUPColumnName.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


