<?php

require_once('db.inc.php');
require_once('strings.inc.php');
require_once('images.inc.php');
require_once('users.inc.php');

/** \defgroup model_advertising Bannières publicitaires
 *	\ingroup mkt
 *	Ce module comprend les fonctions nécessaires à la gestion des annonces publicitaires
 *	@{
 */

// \cond onlyria
/**	Cette fonction permet le chargement d'un ou plusieurs emplacements
 *	publicitaires. Ces emplacements sont définis par l'administrateur
 *	technique. Elle ne sont pas gérées par l'administrateur du client.
 *
 *	@param int $id Optionnel, identifiant d'un emplacement sur lequel filtrer le résultat
 *	@param int $plc_type Optionnel, type d'emplacement par défaut des bannières
 *	@param string $page Optionnel, sur quelle page se trouve cet emplacement (non prit en compte par défaut, valeur acceptées : 'home', 'category', 'product', 'cms', 'store', 'search')
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : identifiant de l'emplacement
 *		- name : désignation de l'emplacement
 *		- desc : description de l'emplacement
 *		- width : largeur de l'emplacement
 *		- height : hauteur de l'emplacement
 *		- wst_id : identifiant du site de l'emplacement
 *		- wst : désignation du site de l'emplacement
 *		- img_code : config image utilisé pour la génération des bannières
 *		- type : type d'emplacement (bannière, zone de clique ...)
 *	@return bool false si le paramètre optionnel \c $id est invalide ou si une erreur est survenue
 *
 */
function adv_places_get( $id=0, $plc_type=_ADV_PLC_BANNERS, $page='' ){
	if( !is_numeric($id) || $id<0 ){
		return false;
	}

	$plc_type = control_array_integer( $plc_type, false );
	if( $plc_type === false ){
		return false;
	}

	if( trim($page) != '' && !in_array($page, array('home', 'category', 'product', 'cms', 'store', 'search')) ){
		return false;
	}

	global $config;

	$sql = '
		select plc_id as id, plc_name as name, plc_desc as "desc",
			plc_width as width, plc_height as height,
			ifnull(wst_id, 0) as wst_id, ifnull(wst_name, "") as wst,
			plc_img_code as img_code, plc_type as type
		from adv_places
			left join tnt_websites on ( plc_tnt_id=wst_tnt_id and (0=wst_id or plc_wst_id=wst_id) )
		where plc_tnt_id='.$config['tnt_id'].'
			and plc_date_deleted is null
			and plc_type in ('.implode(', ', $plc_type).')
	';

	if( $id>0 ){
		$sql .= ' and plc_id='.$id;
	}

	if( trim($page) != '' ){
		$sql .= ' and plc_page = "'.addslashes($page).'"';
		$sql .= ' and plc_wst_id = '.( isset($config['wst_id']) ? $config['wst_id'] : 0 );
	}

	$sql .= ' order by plc_wst_id, plc_name';

	return ria_mysql_query( $sql );

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de déterminer si un identifiant d'emplacement
 *	est valide. Elle est chargée de contrôler que l'identifiant est bien
 *	formé, mais également qu'il correspond à un emplacement définit
 *	dans la base de données.
 *	@param int $id Obligatoire, identifiant d'emplacement à contrôler
 *	@return bool true si l'identifiant est valide
 *	@return bool false dans le cas contraire
 */
function adv_places_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	return ria_mysql_num_rows(ria_mysql_query('
		select plc_id from adv_places where plc_tnt_id='.$config['tnt_id'].' and plc_id='.$id.' and plc_date_deleted is null
	'))>0;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le site où se trouve un emplacement
 *	@param int $plc_id Obligatoire, identifiant d'un emplacement
 *	@return int L'identifiant du site où il se trouve
 */
function adv_places_get_website( $plc_id ){
	if( !is_numeric($plc_id) || $plc_id<=0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select plc_wst_id as wst_id
		from adv_places
		where plc_tnt_id='.$config['tnt_id'].'
			and plc_id='.$plc_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['wst_id'];
}
// \endcond

// \cond onlyria
/**	Cette fonction est un raccourci à utiliser dans les templates de la
 *	boutique en ligne. Elle retourne une bannière à afficher dans un
 *	emplacement donné.
 *
 *	Si plusieurs bannières sont actives au moment de l'appel, l'une de ces
 *	bannières est sélectionnée de manière aléatoire.
 *
 *	@param int $plc Obligatoire. Identifiant de l'emplacement.
 *
 *	@return array La bannière est retournée sous la forme d'un tableau associatif
 *	comprenant les clés suivantes :
 *	- id : identifiant de la bannière
 *	- name : désignation de la bannière
 *	- alt : texte alternatif à utiliser dans les attributs alt et title
 *	- url : url de destination de la bannière
 *	- image : nom du fichier image associé
 *
 */
function adv_places_get_banner( $plc ){
	if( !adv_places_exists($plc) ){
		return false;
	}

	$banners = adv_banners_get( $plc, 0, true );
	$count = $banners ? ria_mysql_num_rows($banners) : 0;
	if( $count>0 ){
		$row = mt_rand(0,$count-1);
		if( $row>0 ){
			ria_mysql_data_seek( $banners, $row );
		}
		return ria_mysql_fetch_assoc($banners);
	}
	return false;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les zones d'actions pour un emplacement de manière aléatoire.
 *	@param int $plc Obligatoire, identifiant de l'emplacement
 *	@return array|bool une zone d'action de façon aléatoire en cas de succès, false en cas d'échec
 */
function adv_places_get_action_zones( $plc ){
	if( !adv_places_exists($plc) ) return false;

	$action_zones = adv_action_zones_get( $plc, 0, true );
	$count = $action_zones ? ria_mysql_num_rows($action_zones) : 0;
	if( $count>0 ){
		$row = mt_rand(0,$count-1);
		if( $row>0 ) ria_mysql_data_seek($action_zones,$row);
		return ria_mysql_fetch_array($action_zones);
	}
	return false;
}
// \endcond

// \cond onlyria
/**	Cette fonction est chargée de l'affichage du bannière publiée dans un emplacement donné.
 *
 *	@param int $plc Obligatoire. Identifiant de l'emplacement.
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function adv_places_show_banner( $plc ){
	$banner = adv_places_get_banner($plc);
	if( !$banner ){
		return false;
	}
	return adv_banners_show( $banner['id'] );

}
// \endcond

// \cond onlyria
/**	Cette fonction est chargée de l'affichage html d'une bannière.
 *
 *	@param int $id Identifiant de la bannière à afficher
 *	@param string $name Optionnel, contenu de l'attribut name/id de la balise html
 *	@param bool $check_segments Optionnel, si activé verifie que la bannière doit être affichée pour le compte connecté. Annulé si $id spécifié
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function adv_banners_show( $id, $name='', $check_segments=true ){
	global $config;

	$r_banner = adv_banners_get( 0, $id, false, false, false, $check_segments );

	if( !ria_mysql_num_rows($r_banner) ){
		return false;
	}
	$banner = ria_mysql_fetch_assoc($r_banner);
	$banner_dir = rtrim($config['banners_dir'],'/');
	$filename = trim($banner['image'], '/');
	$file = $banner_dir.'/'.$filename;

	if( !file_exists($file) ){
		return false;
	}

	list($width,$height,$type) = getimagesize($file);

	switch( $type ){
		case IMAGETYPE_JPEG:
		case IMAGETYPE_GIF:
		case IMAGETYPE_PNG:
			if( $banner['url'] ){

				$target = "_self";
				if( substr($banner['url'],0,1)!='/' ){
					$target = "_blank";
				}
				print '<a href="'.$banner['url'].'"'.( $target!='_self' ? ' target="'.$target.'"':'' ).'>';
			}
			$src = rtrim($config['banners_url'], '/').'/'.$filename;
			$alt = str_replace('"','&quote;', $banner['alt']);

			print '<img '.( $name ? 'id="'.$name.'"' : '' ).' src="'.$src.'" width="'.$width.'" height="'.$height.'" alt="'.$alt.'" title="'.$alt.'" />';

			if( $banner['url'] ){
				print '</a>';
			}
			break;
		default:
			return false;
	}
	return true;

}
// \endcond

/**	Cette fonction permet le chargement d'une ou plusieurs bannières,
 *	filtrées en fonction des paramètres optionnels fournis.
 *
 *	@param int|array $plc Optionnel, identifiant ou tableau d'identifiants d'un emplacement sur lequel filtrer le résultat
 *	@param int $id Optionnel, identifiant d'une bannière sur laquelle filtrer le résultat (ou tableau d'identifiants)
 *	@param bool $active Optionnel, indique si seul les bannières utilisables en affichage sont retournés (contrôle les dates de parution et la présence d'une image)
 *	@param string $lng Optionnel, code d'une langue sur laquelle filtrer le résultat
 *	@param int $wst Optionnel, identifiant d'un site sur lequel filtrer le résultat
 *	@param bool $check_segments Optionnel, si activé verifie que la bannière doit être affichée pour le compte connecté
 *	@param array $array_fld Optionnel, tableau de filtres supplémentaires
 *	@param bool $with_img Optionnel, si true, seules les bannières possédant une image sont retournées (valeur par défaut).
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la bannière
 *			- plc_id : identifiant de l'emplacement
 *			- name : désignation de la bannière
 *			- alt : texte alternatif de l'image
 *			- url : url de destination de la bannière
 *			- image : nom du fichier image associé
 *			- date_from : date de début d'affichage de la bannière, au format jj/mm/aaaa
 *			- hour_from : heure de début d'affichage de la bannière, au format hh:mm
 *			- date_to : date de fin d'affichage de la bannière, au format jj/mm/aaaa
 *			- hour_to : heure de fin de la bannière, au format hh:mm
 *			- pos : position de la bannière
 *			- active : Bannières disposant d'une image et d'une date de parution actuelle
 *          - prd_id : identifiant d'un produit qui sera en destination de la bannière
 * 			- desc : description de la bannière
 * 			- btn_label : label du bouton
 */
function adv_banners_get( $plc=0, $id=0, $active=true, $lng=false, $wst=false, $check_segments=true, $array_fld=false, $with_img=true ){
	global $config;

	if( is_array($id) ){
		foreach( $id as $one_id ){
			if( !is_numeric($one_id) || $one_id<=0 ){
				return false;
			}
		}
	}elseif( is_numeric($id) && $id>0 ){
		$id = array($id);
	}else{
		$id = array();
	}

	if( $lng!=null ){
		$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : $config['i18n_lng'];
	}

	if( $array_fld !== false ){
		if( !is_array($array_fld) ){
			return false;
		}

		if( !ria_array_key_exists(array( 'fld' ), $array_fld) ){
			return false;
		}

		if( !isset($array_fld['or_between_val']) ){
			$array_fld['or_between_val'] = false;
		}

		if( !isset($array_fld['or_between_fld']) ){
			$array_fld['or_between_fld'] = false;
		}

		if( !isset($array_fld['lng']) ){
			$array_fld['lng'] = false;
		}else{
			$array_fld['lng'] = strtolower2( $array_fld['lng'] );

			if( !in_array($array_fld['lng'], $config['i18n_lng_code']) ){
				return false;
			}
		}
	}

	$plc = control_array_integer( $plc, false );
	if( $plc === false ){
		return false;
	}

	$sql = '
		select bnr_id as id, bnr_plc_id as plc_id, bnr_name as name,
			bnr_alt as alt, bnr_image as image, bnr_pos as pos,
			date_format(bnr_date_from,"%d/%m/%Y") as "date_from", time_format(bnr_date_from,"%H:%i") as "hour_from",
			date_format(bnr_date_to,"%d/%m/%Y") as "date_to", time_format(bnr_date_to,"%H:%i") as "hour_to",
			if(bnr_image'.($with_img ? '!=""' : '=""').' and bnr_date_from<=now() and (bnr_date_to is null or bnr_date_to>now()),1,if(bnr_date_from>now(),2,0)) as active,
            if(ifnull(bnr_url, \'\')=\'\', cly_url_alias, bnr_url) as url, bnr_prd_id as prd_id, bnr_desc as "desc", bnr_btn_label as btn_label
		from adv_banners
			left join adv_websites on (bnw_tnt_id=bnr_tnt_id and bnw_bnr_id=bnr_id)
            left join prd_classify on (bnr_tnt_id=cly_tnt_id and bnr_prd_id=cly_prd_id)
		where bnr_tnt_id='.$config['tnt_id'].'
			and bnr_date_deleted is null'.( $lng!=null ? '
			and ('.( $wst==false ? 'ifnull(bnw_lng_code, "")="" or ' : '' ).'bnw_lng_code=\''.$lng.'\')' : '' );

	if( is_array($plc) && sizeof($plc) ){
		$sql .= ' and bnr_plc_id in ('.implode(', ', $plc).')';
	}

	if( sizeof($id) ) $sql .= ' and bnr_id in ('.implode(', ', $id).')';
	if( $wst!=false ) $sql .= ' and bnw_wst_id='.$wst;

    $sql .= '
        and not exists (
            select cat_id
            from prd_categories
            where cat_tnt_id='.$config['tnt_id'].' and cat_id=cly_cat_id and (not cat_publish or cat_date_deleted is not null)
        )
        and not exists (
            select cat_id
            from prd_cat_hierarchy as hry, prd_categories as cat
            where hry.cat_tnt_id='.$config['tnt_id'].'
                and cat.cat_tnt_id='.$config['tnt_id'].'
                and hry.cat_parent_id=cat_id
                and hry.cat_child_id=cly_cat_id
                and not cat_publish
        )
    ';

	$sql .= fld_classes_sql_get( CLS_BANNER, $array_fld['fld'], $array_fld['or_between_val'], $array_fld['or_between_fld'], $array_fld['lng'] );

	//	Filtres d'état
	if( $active === 1){
		//	En cours
		$sql .= ' and bnr_image'.($with_img ? '!=""' : '=""').'
			and (bnr_date_from<=now() )
			and (bnr_date_to>=now() or bnr_date_to is null)';
	}
	elseif( $active === 2){
		//	Passés
		$sql .= ' and bnr_image'.($with_img ? '!=""' : '=""').'
			and (bnr_date_from<=now() )
			and (bnr_date_to<=now() and bnr_date_to is not null)';
	}
	elseif( $active === 3){
		//	Futurs
		$sql .= ' and bnr_image'.($with_img ? '!=""' : '=""').'
			and (bnr_date_from>now() ) ';
	}
	elseif( $active === 4){
		//	Passés ou actuels
		$sql .= ' and bnr_image'.($with_img ? '!=""' : '=""').'
			and (
				(
					(bnr_date_from<=now() or bnr_date_from is null)
					and (bnr_date_to<=now() or bnr_date_to is null)
				)
				or (bnr_date_to>=now() and bnr_date_to is not null)
			)
				';
	}
	elseif( $active === 5){
		//	Actuels ou futurs
		$sql .= ' and bnr_image'.($with_img ? '!=""' : '=""').'
			and (
				(
					(bnr_date_from>=now() or bnr_date_from is null)
					and (bnr_date_to>=now() or bnr_date_to is null)
				)
				or (bnr_date_to>=now() or bnr_date_to is null)
			)

				';
	}
	elseif( $active === 6){
		//	Actuels ou futurs ou terminés récemment
		if(!isset($config['recent_interval'])){
			$config['recent_interval'] = 60;
		}
		$interval = intval($config['recent_interval']);
		$sql .= ' and bnr_image'.($with_img ? '!=""' : '=""').'
			and (
				(
					(bnr_date_from>=now() or bnr_date_from is null)
					and (bnr_date_to>=now() or bnr_date_to is null)
				)
				or (bnr_date_to>=now() or bnr_date_to is null)
				or (
					bnr_date_to<=now() and bnr_date_to is not null
					and (bnr_date_to>=DATE_SUB( now(), INTERVAL '.$interval.' day) and bnr_date_to is not null)
				)
			)

				';
	}
	elseif( $active === 7){
		//	Terminés récemment
		if(!isset($config['recent_interval'])){
			$config['recent_interval'] = 60;
		}
		$interval = intval($config['recent_interval']);
		$sql .= ' and bnr_image'.($with_img ? '!=""' : '=""').'
			and (
					bnr_date_to<=now() and bnr_date_to is not null
					and (bnr_date_to>=DATE_SUB( now(), INTERVAL '.$interval.' day) and bnr_date_to is not null)
				)
				';
	}
	//	Traitement de base
	elseif( $active ) {
		$sql .= ' and bnr_image'.($with_img ? '!=""' : '=""').' and bnr_date_from<=now() and (bnr_date_to is null or bnr_date_to>now())';
		$sql .= ' group by bnr_id';
		$sql .= ' order by ifnull(bnr_pos,18446744073709551615) asc, rand() ';
	} else {
		$sql .= ' group by bnr_id';
		$sql .= ' order by ifnull(bnr_pos,18446744073709551615) asc, bnr_date_from desc, ifnull(bnr_date_to,\'9999-12-31 23:59:59\') desc ';
	}

	if( is_numeric($active) && in_array($active, array(1,2,3,4,5,6,7), true) ){
		$sql .= ' group by bnr_id';
		$sql .= ' order by ifnull(bnr_pos,18446744073709551615) asc, bnr_date_to desc, bnr_date_from desc ';
	}
	$r = ria_mysql_query($sql);
	if( $check_segments && $r && ria_mysql_num_rows($r) ){
		$id_ar = array();

		$usr_id = 0; // non connecté
		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] ){
			$usr_id = $_SESSION['admin_view_user'];
		}elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] ){
			$usr_id = $_SESSION['usr_id'];
		}

		while( $b = ria_mysql_fetch_assoc($r) ){
			if( seg_objects_check_segment( CLS_BANNER, $b['id'], CLS_USER, $usr_id, true ) ){
				$id_ar[] = $b['id'];
			}
		}

		if( !sizeof($id_ar) ){
			return false;
		}

		return adv_banners_get( 0, $id_ar, $active, false, false, false, false, $with_img );
	}

	return $r;
}

// \cond onlyria
/**	Cette fonction est un alias pour la fonction adv_banners_get qui permet de récupérer les zones d'action
 *
 *	@param int|array $plc Optionnel, identifiant ou tableau d'identifiants d'un emplacement sur lequel filtrer le résultat
 *	@param int $id Optionnel, identifiant d'une bannière sur laquelle filtrer le résultat (ou tableau d'identifiants)
 *	@param bool $active Optionnel, indique si seul les bannières utilisables en affichage sont retournés (contrôle les dates de parution et la présence d'une image)
 *	@param string $lng Optionnel, code d'une langue sur laquelle filtrer le résultat
 *	@param int $wst Optionnel, identifiant d'un site sur lequel filtrer le résultat
 *	@param bool $check_segments Optionnel, si activé verifie que la bannière doit être affichée pour le compte connecté
 *	@param array $array_fld Optionnel, tableau de filtres supplémentaires
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant de la bannière
 *			- plc_id : identifiant de l'emplacement
 *			- name : désignation de la bannière
 *			- alt : texte alternatif de l'image
 *			- url : url de destination de la bannière
 *			- image : nom du fichier image associé
 *			- date_from : date de début d'affichage de la bannière, au format jj/mm/aaaa
 *			- hour_from : heure de début d'affichage de la bannière, au format hh:mm
 *			- date_to : date de fin d'affichage de la bannière, au format jj/mm/aaaa
 *			- hour_to : heure de fin de la bannière, au format hh:mm
 *			- pos : position de la bannière
 *			- active : Bannières disposant d'une image et d'une date de parution actuelle
 *          - prd_id : identifiant d'un produit qui sera en destination de la bannière
 */
function adv_action_zones_get( $plc=0, $id=0, $active=true, $lng=false, $wst=false, $check_segments=true, $array_fld=false ){
	return adv_banners_get( $plc, $id, $active, $lng, $wst, $check_segments, $array_fld, false );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la création d'une nouvelle bannière
 *	@param int $plc Identifiant de l'emplacement dans lequel la bannière doit s'afficher
 *	@param string $name Désignation de la bannière (utilisé uniquement dans l'interface d'administration).
 *	@param string $alt Texte alternatif à l'image
 *	@param string $url Url de destination de la bannière (interne ou externe)
 *	@param string $from Date et heure de début d'affichage de la bannière
 *	@param string $to Date et heure de fin d'affichage de la bannière
 *  @param int $prd Optionnel, identifiant d'un produit
 * 	@param string $desc Optionnel, description de la bannière
 * 	@param string $btn_label Optionnel, label du bouton
 *
 *	@return	int l'identifiant attribué à la nouvelle bannière en cas de succès
 *	@return bool false en cas d'échec
 *
 */
function adv_banners_add( $plc, $name, $alt='', $url='', $from='', $to='', $prd=0, $desc='', $btn_label='' ){
	global $config;

	if( !adv_places_exists($plc) ){
		return false;
	}
	if( !trim($name) ){
		return false;
	}

	if( trim($from) ){
		if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4} [0-9]{1,2}:[0-9]{2}$/',$from) ){
			return false;
		}
		$from = preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+) ([0-9]{1,2}):([0-9]{2})$/', '\3-\2-\1 \4:\5:00', $from );
	}else{
		$from = 'null';
	}

	if( trim($to) ){
		if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4} [0-9]{1,2}:[0-9]{2}$/',$to) ){
			return false;
		}
		$to = "'".preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+) ([0-9]{1,2}):([0-9]{2})$/', '\3-\2-\1 \4:\5:00', $to )."'";
	}else{
		$to = 'null';
	}

	$name = ucfirst(trim($name));
	$alt = ucfirst(trim($alt));
	$prd_id = is_numeric($prd) && $prd>0 ? $prd : 'null';

	$fields = array();
	$values = array();

	$fields[] = 'bnr_tnt_id';
	$values[] = $config['tnt_id'];

	$fields[] = 'bnr_plc_id';
	$values[] = $plc;

	$fields[] = 'bnr_name';
	$values[] = '"'.addslashes($name).'"';

	if( trim($alt) != '' ){
		$fields[] = 'bnr_alt';
		$values[] = '"'.addslashes($alt).'"';
	}

	if( trim($url) != '' ){
		$fields[] = 'bnr_url';
		$values[] = '"'.addslashes($url).'"';
	}

	if( trim($from) != '' ){
		$fields[] = 'bnr_date_from';
		$values[] = '"'.$from.'"';
	}

	if( trim($to) != '' ){
		$fields[] = 'bnr_date_to';
		$values[] = $to;
	}

	if( trim($desc) != '' ){
		$fields[] = 'bnr_desc';
		$values[] = '"'.addslashes($desc).'"';
	}

	if( trim($btn_label) != '' ){
		$fields[] = 'bnr_btn_label';
		$values[] = '"'.addslashes($btn_label).'"';
	}

	$fields[] = 'bnr_prd_id';
	$values[] = $prd_id;

	$res = ria_mysql_query('
		insert into adv_banners
			( '.implode(',', $fields).' )
		values
			( '.implode(',',$values).' )
	');
	if( $res ){
		return ria_mysql_insert_id();
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Cette fonction est un alias de adv_banners_add qui permet la création d'une nouvelle zone action
 *	@param int $plc Identifiant de l'emplacement dans lequel la bannière doit s'afficher
 *	@param string $name Désignation la zone d'action (utilisé uniquement dans l'interface d'administration).
 *	@param string $alt Texte alternatif à l'image
 *	@param string $url Url de destination la zone d'action (interne ou externe)
 *	@param string $from Date et heure de début d'affichage la zone d'action
 *	@param string $to Date et heure de fin d'affichage la zone d'action
 *  @param int $prd Optionnel, identifiant d'un produit
 * 	@param string $desc Optionnel, description de la zone d'action
 * 	@param string $btn_label Optionnel, label du bouton
 *
 *	@return	int l'identifiant attribué à la nouvelle bannière en cas de succès
 *	@return bool false en cas d'échec
 */
function adv_action_zone_add( $plc, $name, $alt='', $url='', $from='', $to='', $prd=0, $desc='', $btn_label='' ){
	return adv_banners_add( $plc, $name, $alt, $url, $from, $to, $prd, $desc, $btn_label );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour des propriétés d'une bannière.
 *
 *	@param int $id Identifiant de la bannière à mettre à jour
 *	@param int $plc Identifiant de l'emplacement dans lequel la bannière doit s'afficher
 *	@param string $name Désignation de la bannière (utilisé uniquement dans l'interface d'administration).
 *	@param string $alt Texte alternatif à l'image
 *	@param string $url Url de destination de la bannière (interne ou externe)
 *	@param string $from Date et heure de début d'affichage de la bannière
 *	@param string $to Date et heure de fin d'affichage de la bannière
 *  @param int $prd Optionnel, identifiant d'un produit
 * 	@param string $desc Optionnel, description de la bannière
 * 	@param string $btn_label Optionnel, label du bouton
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function adv_banners_update( $id, $plc, $name, $alt='', $url='', $from='', $to='', $prd=0, $desc='', $btn_label='' ){
	global $config;

	if( !adv_banners_exists($id) ){
		return false;
	}
	if( !adv_places_exists($plc) ){
		return false;
	}
	if( !trim($name) ){
		return false;
	}

	if( trim($from) ){
		if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4} [0-9]{1,2}:[0-9]{2}$/',$from) ){
			return false;
		}
		$from = preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+) ([0-9]{1,2}):([0-9]{2})$/', '\3-\2-\1 \4:\5:00', $from );
	}

	if( trim($to) ){
		if( !preg_match('/^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{4} [0-9]{1,2}:[0-9]{2}$/',$to) ){
			return false;
		}
		$to = "'".preg_replace( '/^([0-9]+)\/([0-9]+)\/([0-9]+) ([0-9]{1,2}):([0-9]{2})$/', '\3-\2-\1 \4:\5:00', $to )."'";
	}else{
		$to = 'null';
	}

	$name = ucfirst(trim($name));
	$alt = ucfirst(trim($alt));
	$prd_id = is_numeric($prd) && $prd>0 ? $prd : 'null';

	$fields = array();

	$fields[] = 'bnr_plc_id='.$plc;
	$fields[] = 'bnr_name="'.addslashes($name).'"';

	if( trim($alt) != '' ){
		$fields[] = 'bnr_alt="'.addslashes($alt).'"';
	}

	if( trim($from) != '' ){
		$fields[] = 'bnr_date_from="'.$from.'"';
	}

	if( trim($to) != '' ){
		$fields[] = 'bnr_date_to='.$to;
	}

	if( trim($desc) != '' ){
		$fields[] = 'bnr_desc = "'.addslashes($desc).'"';
	}else{
		$fields[] = 'bnr_desc = null';
	}

	if( trim($btn_label) != '' ){
		$fields[] = 'bnr_btn_label = "'.addslashes($btn_label).'"';
	}else{
		$fields[] = 'bnr_btn_label = null';
	}

	$fields[] = 'bnr_url="'.addslashes($url).'"';
	$fields[] = 'bnr_prd_id='.$prd_id;

	return ria_mysql_query('
		update adv_banners set
		'.implode(',', $fields ).'
		where bnr_tnt_id='.$config['tnt_id'].'
			and bnr_id='.$id.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction est un alias de adv_banners_update qui permet d'update les zones d'actions
 *	@param int $id Identifiant de la zone d'action à mettre à jour
 *	@param int $plc Identifiant de l'emplacement dans lequel la zone d'action doit s'afficher
 *	@param string $name Désignation de la zone d'action (utilisé uniquement dans l'interface d'administration).
 *	@param string $alt Texte alternatif à l'image
 *	@param string $url Url de destination de la zone d'action (interne ou externe)
 *	@param string $from Date et heure de début d'affichage de la zone d'action
 *	@param string $to Date et heure de fin d'affichage de la zone d'action
 *  @param int $prd Optionnel, identifiant d'un produit
 * 	@param string $desc Optionnel, description de la zone d'action
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function adv_action_zone_update( $id, $plc, $name, $alt='', $url='', $from='', $to='', $prd=0, $desc='' ){
	return adv_banners_update( $id, $plc, $name, $alt, $url, $from, $to, $prd=0, $desc );
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour des propriétés d'une bannière.
 *
 *	@param int $id Identifiant de la bannière à mettre à jour
 *	@param int $pos Position de la bannière
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function adv_banners_update_pos( $id, $pos ){
	global $config;

	if( !adv_banners_exists($id) ) return false;
	$pos = is_numeric($pos) ? $pos : 'null';

	return ria_mysql_query('
		update adv_banners set
			bnr_pos='.$pos.'
		where bnr_tnt_id='.$config['tnt_id'].' and bnr_id='.$id.'
	');
}
// \endcond

// \cond onlyria
/** Cette fonction est un alias de adv_banners_update_pos qui permet de mettre à jour les zones d'actions
 *
 *	@param int $id Identifiant de la bannière à mettre à jour
 *	@param int $pos Position de la bannière
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function adv_action_zone_update_pos( $id, $pos ){
	return adv_banners_update_pos( $id, $pos );
}
// \endcond

// \cond onlyria
/** Permet l'association d'un fichier image à une bannière.
 *
 *	@param int $id Identifiant de la bannière à actualiser
 *	@param string $fieldname Nom du fichier image à ajouter (le fichier doit être local)
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 *
 *	@todo L'ancien fichier si il existe est laissé sur le disque. Un 301 / Moved permanently serait plus approprié.
 */
function adv_banners_image_upload( $id, $fieldname ){
	global $config;

	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ){
		return false;
	}

	// Charge les données enregistrées sur la bannière
	$r_banner = adv_banners_get( 0, $id, false, false, false, false );
	if( !$r_banner || !ria_mysql_num_rows($r_banner) ){
		return false;
	}

	$banner = ria_mysql_fetch_assoc($r_banner);

	$rplace = adv_places_get( $banner['plc_id'], array(_ADV_PLC_BANNERS, _ADV_PLC_VISIO) );
	if( !$rplace || !ria_mysql_num_rows($rplace) ){
		return false;
	}

	$place = ria_mysql_fetch_assoc( $rplace );

	// Chargement des configurations pour l'upload des bannières en fonction du site internet lié à l'emplacement de bannière
	$banners_dir = cfg_overrides_get_value( 'banners_dir', $place['wst_id'] );
	if( trim($banners_dir) == '' ){
		$banners_dir = $config['banners_dir'];
	}

	// Extrait l'extension du fichier
	$last_pos_point = strrpos( basename($_FILES[$fieldname]['name']), '.' );
	if( $last_pos_point === false ){
		return false;
	}

	if( strlen(basename($_FILES[$fieldname]['name'])) <= ($last_pos_point + 1) ){
		return false;
	}

	$ext = substr( basename($_FILES[$fieldname]['name']), $last_pos_point + 1 );

	if( trim($place['img_code'])=='' || !isset($config['img_sizes'][$place['img_code']]) ){
		$size 			= array( 'width'=>$place['width'], 'height'=>$place['height'], 'transparent'=>0, 'border'=>0, 'border-color'=>'', 'format'=>$ext, 'corners'=>0, 'background'=>'#FFFFFF' );
	} else {
		$size = $config['img_sizes'][$place['img_code']];
	}

	// Le nom de la bannière n'est pas utilisé car il est réservé à un usage interne
	// L'attribut alt est quant à lui public
	$name = urlalias( $banner['alt'] );
	if( strlen($name)>32 ){
		$name = substr($name,0,32);
		$name = preg_replace( '/-[a-z0-9]+$/', '', $name );
	}

	// Vérifie que le nom du fichier est bien unique avant de le déplacer
	$counter = 0;
	$destination = $banners_dir.'/'.$name.'.'.$size['format'];
	while( file_exists($destination) ){
		$destination = $banners_dir.'/'.$name.'-'.($counter++).'.'.$size['format'];
	}

	if( is_gif_animated($_FILES[$fieldname]['tmp_name']) ){
		if( !move_uploaded_file( $_FILES[$fieldname]['tmp_name'], $destination ) ){
			return false;
		}
	} else {
		$img = new Imagick( $_FILES[$fieldname]['tmp_name'] );
		$img->setResourceLimit(6, 2);

		if( $size['width']<=0 ){
			$size['width'] = $img->getImageWidth();
		}
		if( $size['height']<=0 ){
			$size['height'] = $img->getImageHeight();
		}
		// Redimensionnement (en conservant les proportions)
		$img->thumbnailImage( $size['width'], $size['height'], false );

		// réduit la taille de l'image selon la largeur de la bordure x2
		if( is_numeric($size['border']) && $size['border']>0 ){
			$size['width'] -= $size['border'] * 2;
			$size['height'] -= $size['border'] * 2;
		}

		// Création de l'image "support" qui va constituer l'image finale
		$canvas = new Imagick();
		$canvas->newImage( $size['width'], $size['height'], 'none' );
		$canvas->setImageColorSpace( imagick::COLORSPACE_SRGB );

		// Définition du format de sortie
		$canvas->setImageFormat( $size['format'] );

		// Composition de l'image finale par empilement des deux images (le fond + la miniature)
		$tsize = $img->getImageGeometry();
		$pos_x = $size['width'] / 2 - $tsize['width'] / 2;
		$pos_y = $size['height'] / 2 - $tsize['height'] / 2;
		$canvas->compositeImage( $img, imagick::COMPOSITE_OVER, $pos_x, $pos_y );

		if( is_numeric($size['corners']) && $size['corners']>0 ){
			$canvas->roundCorners( $size['corners'], $size['corners']);
		}

		if( is_numeric($size['border']) && $size['border']>0 ){
			$canvas->borderImage( $size['border-color'], $size['border'], $size['border'] );
			if( is_numeric($size['corners']) && $size['corners']>0 ){
				$canvas->roundCorners( $size['corners'], $size['corners']);
			}
		}

		$canvas->writeImage( $destination );

		// Effectue une passe supplémentaire d'optimisation sur les images (inspirée par Yahoo Smush.it)
		img_images_optimize( $destination );
	}

	return ria_mysql_query('update adv_banners set bnr_image=\''.basename($destination).'\' where bnr_tnt_id='.$config['tnt_id'].' and bnr_id='.$id);
}
// \endcond

// \cond onlyria
/**	Cette fonction est chargée de vérifier qu'une bannière respecte les dimensions imposées par un emplacement.
 *	@param string $filename Nom du fichier de bannière
 *	@param int $plc Identifiant de l'emplacement
 */
function adv_banners_image_match_place( $filename, $plc ){

	$r_place = adv_places_get($plc);
	if( !ria_mysql_num_rows($r_place) ) return false;
	$place = ria_mysql_fetch_assoc($r_place);

	list($width,$height,$type) = getimagesize( $filename );

	if( $place['width'] && $place['width']!=$width ) return false;
	if( $place['height'] && $place['height']!=$height ) return false;

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'une bannière.
 *	@param int $id Obligatoire, identifiant de la bannière à supprimer.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function adv_banners_del( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;
	return ria_mysql_query('delete from adv_banners where bnr_tnt_id='.$config['tnt_id'].' and bnr_id='.$id);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la vérification d'un identifiant de bannière.
 *	@param int $id Obligatoire, identifiant de bannière à vérifier
 *	@return bool true si l'identifiant est valide
 *	@return bool false dans le cas contraire
 */
function adv_banners_exists( $id ){
	global $config;
	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select bnr_id
		from adv_banners
		where bnr_tnt_id='.$config['tnt_id'].'
			and bnr_date_deleted is null
			and bnr_id='.$id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'ajouter un lien entre une bannière, un site et une langue
 *	@param int $id Obligatoire, identifiant d'une bannière
 *	@param int $wst Obligatoire, identifiant d'une site web
 *	@param string $lng Optionnel, code ISO d'une langue
 */
function adv_websites_add( $id, $wst, $lng=false ){
	global $config;

	if( $lng===false ){
		$lng = $config['i18n_lng'];
	}else{
		$lng = strtolower( $lng );
	}

	if( !adv_banners_exists($id) || !wst_websites_exists($wst) || adv_websites_exists($id,$wst,$lng) ){
		return false;
	}else{
		return ria_mysql_query('
			insert into adv_websites
				( bnw_tnt_id, bnw_wst_id, bnw_lng_code, bnw_bnr_id )
			values
				( '.$config['tnt_id'].', '.$wst.', \''.$lng.'\', '.$id.' )
		');
	}
}
// \endcond

// \cond onlyria
/** Cette fonction permet de vérifier l'existance d'une bannière sur un site
 *	@param int $id Obligatoire, identifiant d'une banière
 *	@param int $wst Optionnel, identifiant d'un site web
 *	@param string $lng Optionnel, code ISO de la langue
 */
function adv_websites_exists( $id, $wst=0, $lng=false ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	if( $lng!=false ){
		$lng = strtolower($lng);
	}

	$sql = 'select 1 from adv_websites where bnw_tnt_id='.$config['tnt_id'].' and bnw_bnr_id='.$id.( $lng!=false ? ' and bnw_lng_code=\''.$lng.'\'' : '');
	if( $wst>0 ){
		$sql .= ' and bnw_wst_id='.$wst;
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de supprimer un lien entre une bannière et sa présence sur un site.
 *	@param int $id Obligatoire, identifiant d'une bannière
 *	@param int $wst Facultatif, identifiant d'un site
 *	@param string $lng Facultatif, code langue
 *
 *	@return bool Retourne true si la suppression s'est correctement déroulée, false dans le cas contraire
 */
function adv_website_del( $id, $wst=0, $lng=null ){
	global $config;

	if( !is_numeric($id) || $id<0 ){
		return false;
	}
	if( $wst>0 && !wst_websites_exists($wst) ){
		return false;
	}

	if( $lng!==null ){
		$lng = strtolower($lng);
	}

	return ria_mysql_query('
		delete from adv_websites
		where bnw_tnt_id='.$config['tnt_id'].'
			and bnw_bnr_id='.$id.( $wst>0 ? '
			and bnw_wst_id='.$wst : '' ).
			( $lng!=null ? ' and bnw_lng_code=\''.$lng.'\'' : '' )
	);
}
// \endcond

/// @}
