<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  959250 => 'Huawei',
  959251 => 'Huawei',
  959252 => 'Huawei',
  959253 => 'Huawei',
  959254 => 'Huawei',
  9592557 => 'ASB',
  9592558 => 'ASB',
  9592559 => 'ASB',
  959256 => 'ZTE',
  959257 => 'ZTE',
  959258 => 'ZTE',
  959259 => 'ZTE',
  959260 => 'Huawei',
  959261 => 'Huawei',
  959262 => 'Huawei',
  959263 => 'Huawei',
  959264 => 'Huawei',
  959265 => 'ZTE',
  95930 => 'MECTel',
  95931 => 'MECTel',
  95932 => 'MECTel',
  95933 => 'MECTel',
  959340 => 'MECTel',
  959346 => 'MECTel',
  959349 => 'MECTel',
  95936 => 'MECTel',
  959426 => 'ZTE',
  959427 => 'ZTE',
  95951 => 'ZTE',
  95955 => 'ZTE',
  95969 => 'MNTC',
  959750 => 'Telenor',
  959751 => 'Telenor',
  959752 => 'Telenor',
  95976 => 'Telenor',
  95977 => 'Telenor',
  95978 => 'Telenor',
  95979 => 'Telenor',
  95989 => 'MPT',
  95995 => 'Ooredoo',
  95996 => 'Ooredoo',
  95997 => 'Ooredoo',
);
