probe {
  type: EXTERNAL
  name: "spanner"
  interval_msec: 1800000
  timeout_msec: 30000
  targets { dummy_targets {} }  # No targets for external probe
  external_probe {
    mode: ONCE
    command: "php grpc_gpc_prober/prober.php --api=spanner"
  }
}

probe {
  type: EXTERNAL
  name: "firestore"
  interval_msec: 1800000
  timeout_msec: 30000
  targets { dummy_targets {} }  # No targets for external probe
  external_probe {
    mode: ONCE
    command: "php grpc_gpc_prober/prober.php --api=firestore"
  }
}

surfacer {
  type: STACKDRIVER
  name: "stackdriver"
  stackdriver_surfacer {
    monitoring_url: "custom.googleapis.com/cloudprober/"
  }
}
