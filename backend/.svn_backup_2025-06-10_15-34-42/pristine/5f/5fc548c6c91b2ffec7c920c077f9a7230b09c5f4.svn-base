Arguments
-----
<?php

f();
f($a);
f($a, $b);
f(&$a);
f($a, ...$b);
-----
array(
    0: Expr_FuncCall(
        name: Name(
            parts: array(
                0: f
            )
        )
        args: array(
        )
    )
    1: Expr_FuncCall(
        name: Name(
            parts: array(
                0: f
            )
        )
        args: array(
            0: Arg(
                value: Expr_Variable(
                    name: a
                )
                byRef: false
                unpack: false
            )
        )
    )
    2: Expr_FuncCall(
        name: Name(
            parts: array(
                0: f
            )
        )
        args: array(
            0: Arg(
                value: Expr_Variable(
                    name: a
                )
                byRef: false
                unpack: false
            )
            1: Arg(
                value: Expr_Variable(
                    name: b
                )
                byRef: false
                unpack: false
            )
        )
    )
    3: Expr_FuncCall(
        name: Name(
            parts: array(
                0: f
            )
        )
        args: array(
            0: Arg(
                value: Expr_Variable(
                    name: a
                )
                byRef: true
                unpack: false
            )
        )
    )
    4: Expr_FuncCall(
        name: Name(
            parts: array(
                0: f
            )
        )
        args: array(
            0: Arg(
                value: Expr_Variable(
                    name: a
                )
                byRef: false
                unpack: false
            )
            1: Arg(
                value: Expr_Variable(
                    name: b
                )
                byRef: false
                unpack: true
            )
        )
    )
)