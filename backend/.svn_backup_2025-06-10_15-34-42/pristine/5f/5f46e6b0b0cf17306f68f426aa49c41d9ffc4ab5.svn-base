<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  59170 => 'Nuevatel',
  591710 => 'Entel',
  591711 => 'Entel',
  591712 => 'Entel',
  591714 => 'Entel',
  591715 => 'Entel',
  591716 => 'Entel',
  591717 => 'Entel',
  591718 => 'Entel',
  591719 => 'Entel',
  59172 => 'Entel',
  591730 => 'Entel',
  591731 => 'Entel',
  591732 => 'Entel',
  591733 => 'Entel',
  5917342 => 'Entel',
  5917343 => 'Entel',
  5917344 => 'Entel',
  5917346 => 'Entel',
  5917347 => 'Entel',
  5917348 => 'Entel',
  5917349 => 'Entel',
  5917353 => 'Entel',
  591736 => 'Entel',
  5917370 => 'Entel',
  5917371 => 'Entel',
  5917372 => 'Entel',
  5917373 => 'Entel',
  5917374 => 'Entel',
  5917375 => 'Entel',
  5917377 => 'Entel',
  591738 => 'Entel',
  59174 => 'Entel',
  59175 => 'Tigo',
  59176 => 'Tigo',
  59177 => 'Tigo',
  59179 => 'Nuevatel',
);
