<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  'ar' => 
  array (
    0 => 82,
    1 => 966,
  ),
  'be' => 
  array (
    0 => 375,
  ),
  'bg' => 
  array (
    0 => 359,
    1 => 82,
  ),
  'bs' => 
  array (
    0 => 387,
  ),
  'ca' => 
  array (
    0 => 82,
  ),
  'cs' => 
  array (
    0 => 82,
  ),
  'de' => 
  array (
    0 => 32,
    1 => 352,
    2 => 41,
    3 => 43,
    4 => 49,
  ),
  'el' => 
  array (
    0 => 30,
    1 => 82,
  ),
  'en' => 
  array (
    0 => 12,
    1 => 13,
    2 => 14,
    3 => 15,
    4 => 16,
    5 => 17,
    6 => 18,
    7 => 19,
    8 => 20,
    9 => 212,
    10 => 213,
    11 => 216,
    12 => 218,
    13 => 220,
    14 => 221,
    15 => 222,
    16 => 223,
    17 => 224,
    18 => 225,
    19 => 226,
    20 => 227,
    21 => 228,
    22 => 229,
    23 => 230,
    24 => 232,
    25 => 233,
    26 => 234,
    27 => 237,
    28 => 238,
    29 => 239,
    30 => 240,
    31 => 241,
    32 => 242,
    33 => 243,
    34 => 244,
    35 => 245,
    36 => 247,
    37 => 249,
    38 => 251,
    39 => 252,
    40 => 254,
    41 => 255,
    42 => 256,
    43 => 257,
    44 => 258,
    45 => 260,
    46 => 261,
    47 => 263,
    48 => 264,
    49 => 266,
    50 => 267,
    51 => 268,
    52 => 269,
    53 => 27,
    54 => 290,
    55 => 299,
    56 => 30,
    57 => 31,
    58 => 32,
    59 => 33,
    60 => 34,
    61 => 351,
    62 => 352,
    63 => 353,
    64 => 354,
    65 => 355,
    66 => 358,
    67 => 359,
    68 => 36,
    69 => 370,
    70 => 371,
    71 => 372,
    72 => 373,
    73 => 374,
    74 => 375,
    75 => 380,
    76 => 381,
    77 => 383,
    78 => 387,
    79 => 389,
    80 => 39,
    81 => 40,
    82 => 41,
    83 => 420,
    84 => 421,
    85 => 43,
    86 => 44,
    87 => 46,
    88 => 47,
    89 => 48,
    90 => 49,
    91 => 501,
    92 => 504,
    93 => 51,
    94 => 52,
    95 => 53,
    96 => 54,
    97 => 55,
    98 => 56,
    99 => 57,
    100 => 58,
    101 => 592,
    102 => 599,
    103 => 61,
    104 => 62,
    105 => 64,
    106 => 66,
    107 => 675,
    108 => 680,
    109 => 686,
    110 => 688,
    111 => 7,
    112 => 81,
    113 => 82,
    114 => 84,
    115 => 8610,
    116 => 86130,
    117 => 86131,
    118 => 86132,
    119 => 86133,
    120 => 86134,
    121 => 86135,
    122 => 86136,
    123 => 86137,
    124 => 86138,
    125 => 86139,
    126 => 86145,
    127 => 86147,
    128 => 86150,
    129 => 86151,
    130 => 86152,
    131 => 86153,
    132 => 86155,
    133 => 86156,
    134 => 86157,
    135 => 86158,
    136 => 86159,
    137 => 86170,
    138 => 86176,
    139 => 86177,
    140 => 86178,
    141 => 86180,
    142 => 86181,
    143 => 86182,
    144 => 86183,
    145 => 86184,
    146 => 86185,
    147 => 86186,
    148 => 86187,
    149 => 86188,
    150 => 86189,
    151 => 86,
    152 => 886,
    153 => 90,
    154 => 91,
    155 => 93,
    156 => 94,
    157 => 95,
    158 => 960,
    159 => 962,
    160 => 966,
    161 => 968,
    162 => 972,
    163 => 975,
    164 => 976,
    165 => 98,
    166 => 992,
    167 => 994,
  ),
  'es' => 
  array (
    0 => 228,
    1 => 230,
    2 => 34,
    3 => 52,
    4 => 54,
    5 => 56,
    6 => 58,
    7 => 82,
  ),
  'fa' => 
  array (
    0 => 93,
    1 => 98,
  ),
  'fi' => 
  array (
    0 => 358,
    1 => 82,
  ),
  'fr' => 
  array (
    0 => 212,
    1 => 222,
    2 => 225,
    3 => 228,
    4 => 229,
    5 => 230,
    6 => 243,
    7 => 290,
    8 => 32,
    9 => 33,
    10 => 352,
    11 => 41,
    12 => 82,
  ),
  'hi' => 
  array (
    0 => 82,
  ),
  'hr' => 
  array (
    0 => 387,
  ),
  'hu' => 
  array (
    0 => 36,
    1 => 82,
  ),
  'hy' => 
  array (
    0 => 374,
  ),
  'id' => 
  array (
    0 => 62,
  ),
  'it' => 
  array (
    0 => 39,
    1 => 41,
  ),
  'iw' => 
  array (
    0 => 82,
    1 => 972,
  ),
  'ja' => 
  array (
    0 => 81,
    1 => 82,
  ),
  'ko' => 
  array (
    0 => 82,
  ),
  'nl' => 
  array (
    0 => 31,
    1 => 32,
  ),
  'pl' => 
  array (
    0 => 48,
    1 => 82,
  ),
  'pt' => 
  array (
    0 => 238,
    1 => 239,
    2 => 244,
    3 => 245,
    4 => 258,
    5 => 351,
    6 => 55,
    7 => 82,
  ),
  'ro' => 
  array (
    0 => 373,
    1 => 40,
    2 => 82,
  ),
  'ru' => 
  array (
    0 => 373,
    1 => 374,
    2 => 375,
    3 => 7,
    4 => 82,
  ),
  'sl' => 
  array (
    0 => 82,
  ),
  'sq' => 
  array (
    0 => 383,
  ),
  'sr' => 
  array (
    0 => 381,
    1 => 383,
    2 => 387,
    3 => 82,
  ),
  'sv' => 
  array (
    0 => 358,
    1 => 46,
  ),
  'th' => 
  array (
    0 => 66,
  ),
  'tr' => 
  array (
    0 => 82,
    1 => 90,
  ),
  'uk' => 
  array (
    0 => 82,
  ),
  'vi' => 
  array (
    0 => 82,
    1 => 84,
  ),
  'zh' => 
  array (
    0 => 82,
    1 => 8610,
    2 => 86130,
    3 => 86131,
    4 => 86132,
    5 => 86133,
    6 => 86134,
    7 => 86135,
    8 => 86136,
    9 => 86137,
    10 => 86138,
    11 => 86139,
    12 => 86145,
    13 => 86147,
    14 => 86150,
    15 => 86151,
    16 => 86152,
    17 => 86153,
    18 => 86155,
    19 => 86156,
    20 => 86157,
    21 => 86158,
    22 => 86159,
    23 => 86170,
    24 => 86176,
    25 => 86177,
    26 => 86178,
    27 => 86180,
    28 => 86181,
    29 => 86182,
    30 => 86183,
    31 => 86184,
    32 => 86185,
    33 => 86186,
    34 => 86187,
    35 => 86188,
    36 => 86189,
    37 => 86,
    38 => 886,
  ),
  'zh_Hant' => 
  array (
    0 => 82,
    1 => 886,
  ),
);
