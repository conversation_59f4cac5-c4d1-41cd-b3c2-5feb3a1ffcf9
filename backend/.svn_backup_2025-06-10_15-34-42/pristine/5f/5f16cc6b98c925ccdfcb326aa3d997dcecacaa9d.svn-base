<?php
	require_once('advertising.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class bannersGetTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la récupération des données d'une bannière par son id
		 */
		public function testBannersGetById() {

            $rbnr = adv_banners_get(0, 1, false);
			$this->assertTrue($rbnr && ria_mysql_num_rows($rbnr) == 1, 'Erreur lors de la récupération d\'une bannière par son id');
			$bnr = ria_mysql_fetch_assoc($rbnr);
            $this->assertEquals(1, $bnr['id'], 'Erreur lors de la récupération d\'une bannière par son id');         
        }

        /** Fonction permettant de tester la récupération de bannières par l'id de son emplacement
         */
        public function testBannersGetByPlc(){

            $rbnr = adv_banners_get(1, 0, false);
			$this->assertTrue($rbnr && ria_mysql_num_rows($rbnr) == 2, 'Erreur lors de la récupération de bannières par id de leur emplacement');
            while( $bnr = ria_mysql_fetch_assoc($rbnr) ){
                $this->assertTrue( in_array($bnr['id'], array(1,100)), 'Erreur lors de la récupération de bannières par id de leur emplacement');
            }
        }

	
	}
