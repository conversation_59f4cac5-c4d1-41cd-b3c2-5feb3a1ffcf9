<?php
    /* Ce script permet de renseigner les nouveaux droits en fonction des anciennes variables de config
     * argv[1] correspond au tenant
     * argv[2] correspond a l'identifiant du site web
     */


     set_include_path(dirname(__FILE__) . '/../include/');
    require_once('cfg.variables.inc.php');
    require_once('rights.inc.php');
    require_once('define.inc.php');

    unset($config);
	$tnt_id = isset($argv[1]) && is_numeric($argv[1]) && $argv[1] > 0 ? $argv[1] : 0;

	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_websites($tnt_id);
	if( !is_array($configs) || !sizeof($configs) ){
		return false;
	}

	// Traitement
	foreach( $configs as $config ){
        unset($authorized_rights);
        global $authorized_rights;


        $r_module = gu_categories_rights_get(0, true, null);
        while( $module = ria_mysql_fetch_assoc($r_module) ){
            switch( $module['name'] ){
                case 'Catalogue':{
                    if(!view_admin_menu_authorized(_MDL_CATALOG)){
                        continue 2;
                    }
                    break;
                }
                case 'Clients':{
                    if(!view_admin_menu_authorized(_MDL_CUSTOMER)){
                        continue 2;
                    }
                    break;
                }
                case 'Commandes':{
                    if(!view_admin_menu_authorized(_MDL_ORDER)){
                        continue 2;
                    }
                    break;
                }
                case 'Promotions':{
                    if(!view_admin_menu_authorized(_MDL_PROMOTION)){
                        continue 2;
                    }
                    break;
                }
                case 'Médiathèque':{
                    if(!view_admin_menu_authorized(_MDL_MEDIA)){
                        continue 2;
                    }
                    break;
                }
                case 'Outils':{
                    if(!view_admin_menu_authorized(_MDL_TOOLS)){
                        continue 2;
                    }
                    break;
                }
                case 'Configuration':{
                    if(!view_admin_menu_authorized(_MDL_CONFIG)){
                        continue 2;
                    }
                    break;
                }
                case 'Modération':{
                    if(!view_admin_menu_authorized(_MDL_MODERATION)){
                        continue 2;
                    }
                    if(!tnt_tenants_have_websites()){
                        continue 2;
                    }
                    break;
                }
                case 'Statistique':{
                    if(!view_admin_menu_authorized(_MDL_STATS)){
                        continue 2;
                    }
                    break;
                }
                case 'Comparateurs':{
                    if(!(isset($config['ctr_active']) && $config['ctr_active'])){
                        continue 2;
                    }
                    
                    $r_ctr = ctr_comparators_get( 0, true, false, false );
                    $r_mtk = ctr_comparators_get( 0, true, false, true );
                    if( !($r_ctr && ria_mysql_num_rows($r_ctr)) && !($r_mtk && ria_mysql_num_rows($r_mtk)) ){
                        continue 2;
                    }
        
                    break;
                }
                case 'Force de ventes':{
                    if(!view_admin_menu_authorized(_MDL_FDV)){
                        continue 2;
                    }
                    break;
                }
                case 'Mes options':{
                    if(!view_admin_menu_authorized(_MDL_OPTION)){
                        continue 2;
                    }
                    break;
                }
            } 
            check_child_rights(null, $module['id']);
        }
        
        wst_website_rights_add($config['tnt_id'], $config['wst_id'], $authorized_rights);
    }


    function check_child_rights($parent_id, $cat_id = null){
        global $authorized_rights, $config;


        if($cat_id){
            $r_right = gu_rights_get( 0, '', $cat_id, array(), true, null );
        }else{
            $r_right = gu_rights_get( 0, '', 0, array(), true, $parent_id ); 
        }
        if( $r_right ){
            while( $right = ria_mysql_fetch_assoc($r_right) ){                
                switch( $right['code'] ){
                    case '_RGH_ADMIN_CATALOG_RIGHT':{
                        if(!$config['use_catalog_restrictions'] ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_ORDER_CLICK_COLLECT':{
                        if(!(isset($config['click_collect_enabled']) && $config['click_collect_enabled'])){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_ORDER_PANIER' :{
                        if (!(isset($config['rights_is_active']) && $config['rights_is_active'])){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_ORDER_MODEL':{
                        if( !$config['ord_models_actived'] ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_ORDER_RETURN':{
                        if( !$config['returns_allowed'] ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_PROMO_SOLDE':{
                        if (!isset($config['module_soldes_is_actived']) || !$config['module_soldes_is_actived']){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_PROMO_REWARD':{
                        if( !(isset($config['rwd_reward_actived']) && $config['rwd_reward_actived'] && view_functionnality_access('VIEW_REWARDS')) ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_TOOL_NEWS':
                    case '_RGH_ADMIN_TOOL_NEWS_CATEG':
                    case '_RGH_ADMIN_TOOL_ALERT':
                    case '_RGH_ADMIN_TOOL_BANNER':
                    case '_RGH_ADMIN_TOOL_ERRATUM':
                    case '_RGH_ADMIN_TOOL_GLOSSARY':{
                        if( !tnt_tenants_have_websites() ){ 
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_TOOL_SMS':{
                        if( !(isset($config['marketing_is_active']) && $config['marketing_is_active'] )){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_TOOL_FAQ_CATEG':{
                        if( !$config['faq_enabled'] ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_TOOL_ZONE':{
                        if( !view_functionnality_access('ACTION_ZONES') ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_TOOL_REWARD':{
                        if( !(isset($config['rwd_reward_actived']) && $config['rwd_reward_actived'] && view_functionnality_access('VIEW_REWARDS') )){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_TOOL_IMPORT':{
                        if (!(isset($config['import_actived']) && $config['import_actived'])) {
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_CONFIG_CONTACT':
                    case '_RGH_ADMIN_CONFIG_REF':
                    case '_RGH_ADMIN_CONFIG_REDIRECTION':
                    case '_RGH_ADMIN_CONFIG_FILTER':{
                        if( !tnt_tenants_have_websites() ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_CONFIG_DLV_STORE_SRV':
                    case '_RGH_ADMIN_CONFIG_DLV_STORE_JOB':{
                        if( !ria_mysql_num_rows(dlv_stores_get()) ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_CONFIG_ORDER':{
                        if( !$config['allow_orders_update_state'] ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_CONFIG_TRANSLATE':{
                        if(!(isset($config['i18n_lng_used']) && sizeof($config['i18n_lng_used']))){ 
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_CONFIG_PAIEMENT_YESBYCASH':{
                        if(!( isset($config['yesbycash_merchant_id']) && trim($config['yesbycash_merchant_id'])!='' )){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_CONFIG_IMG':{
                        if( !(isset($config['active_default_image']) && $config['active_default_image']) ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_CONFIG_CAMPAGNE':{
                        if(!(isset($config['active_campain']) && $config['active_campain']) ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_CONFIG_INSTAGRAM':{
                        if( !(isset($config['instagram_is_active']) && $config['instagram_is_active']) ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_MOD_PRD':{
                        if( !$config['prd_moderation'] ){ 
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_STATS_ORIGIN':
                    case '_RGH_ADMIN_STATS_CONTACT':
                    case '_RGH_ADMIN_STATS_SEARCH':
                    case '_RGH_ADMIN_STATS_PRD_CONVERSION':
                    case '_RGH_ADMIN_STATS_REF':{
                        if( !tnt_tenants_have_websites() ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_STATS_PRICE_WATCHING':{
                        if(!(isset($config['price_watching_active']) && $config['price_watching_active']) ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_COMPARATOR':{
                        if( !view_admin_menu_authorized(_MDL_COMPARATOR) ){
                            continue 2;
                        }
                        $r_ctr = ctr_comparators_get( 0, true, false, false );
                        if( !($r_ctr && ria_mysql_num_rows($r_ctr)) ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_MARKET_PLACE':{
                        if( !view_admin_menu_authorized(_MDL_MARKETPLACE) ){
                            continue 2;
                        }
                        $r_mtk = ctr_comparators_get( 0, true, false, true );
                        if( !($r_mtk && ria_mysql_num_rows($r_mtk)) ){
                            continue 2;
                        }			
                        break;
                    }
                    case '_RGH_ADMIN_FDV_STATS_PIPELINE':
                    case '_RGH_ADMIN_FDV_STATS_REPORT_TIME':
                    case '_RGH_ADMIN_FDV_STATS_SPENT_TIME':{
                        break;
                    }
                    case '_RGH_ADMIN_OPTION_MOD':{
                        if( !$config['prd_moderation'] ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_OPTION_CACHE':{
                        if(!tnt_tenants_have_websites() ){
                            continue 2;
                        }
                        break;
                    }
                    case '_RGH_ADMIN_OPTION_ACCOUNT':{
                        if( $config['prd_moderation'] ){
                            continue 2;
                        }
                        break;
                    }
                }

                $authorized_rights[$right['id']] = $right['id'];
                check_child_rights($right['id']);
            }
        }
    }

    


