<?php
	require_once('PaymentExternal/Payzen.inc.php');

	/** \defgroup lyra Lyra
	 *	\ingroup payment_external
	 *
	 *	Ce module permet les paiement avec Lyra
	 *	Variables de config obligatoire
	 *			- systempay_site_id : identifiant du site
	 *			- systempay_certicat : certificat à utiliser (permet de calculer la signature de contrôle)
	 *			- systempay_certicat_dev : certificat à utiliser (seulement en mode maquette - permet de calculer la signature de contrôle)
	 *			- systempay_contract : numéro de contract (optionnel, mais fortement conseillé lors d'une gestion de plusieurs contrat - click & collect)
	 *			- systempay_url_error : url lors d'un échec de paiement
	 *			- systempay_url_cancel : url lors de l'annulation d'un paiement
	 *			- systempay_url_return_ok : url lors d'un paiement réussi (surcharge celle renseignée dans l'espace marchand Lyra)
	 *
	 *			- systempay_url_return_register : url de retour dans le cas d'une création ou mise à jour d'un compte carte
	 *			- systempay_url_cancel_register : url lors de l'annulation de création ou mise à jour d'un compte carte
	 *			- systempay_url_error_register : url lors d'un échec de création ou mise à jour d'un compte carte
	 *			- systempay_state_multi : statut final de la commande lors d'un paiement en plusieurs fois (par défaut à 4)
	 *
	 *	Ces infos sont disponibles dans l'inteface SystemPay en ligne (Paramétrages > Boutique > %boutique%}
	 *	La signature est visible dans > Certificats (il faut valider les tests pour obtenir le certificat de production)
	 *
	 *	Exemple : Paiement par identifiant
	 *	\code{.php}
	 *		$lyra = new Lyra();
	 *		$lyra->createSimplePayment();
	 *		$lyra->getIdentifierID( $card_ID ); // -> Optionnel
	 *		$lyra->activePayByIdentifier();
	 *	\endcode
	 *
	 *	Exemple : Paiement en plusieurs fois
	 *	\code{.php}
	 *		$lyra = new Lyra();
	 *		$lyra->createMultiPayment( 1500, 3, 30 );
	 *	\endcode
	 *
	 *	Exemple : Paiement en plusieurs fois (échéancier personnalisé)
	 *	\code{.php}
	 *		$lyra = new Lyra();
	 *		$lyra->createRecurrence( '2014-12-12', 3000, 'RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10', 1750, 3 );
	 *	\endcode
	 *
	 *	Exemple : Mise en place d'une récurrence (abonnement), avec ou sans paiement (@todo : Il reste à brancher cette partie avec prd_subscription / ord_subscription)
	 *	\code{.php}
	 *		$lyra = new Lyra();
	 *		$lyra->createRecurrence( '2014-12-12', 3000, 'RRULE:FREQ=MONTHLY;COUNT=12;BYMONTHDAY=10', 1750, 3 );
	 *		$lyra->activePayByIdentifier();
	 *	\endcode
	 *
	 *	@{
	 */

	/**	\brief Cette classe est l'implémentation concrète du fournisseur Lyra en tant que prestataire de paiement externe.
	 *
	 */
	class Lyra extends Payzen {
		protected $provider = 'payzen'; ///< Utilisation des mêmes variables de configuration que payzen
		protected $module = 'LYRA';
		protected $key = 'Lyra';
		protected $form_url = 'https://secure.lyra.com/vads-payment/';
		protected $form_id = 'form-lyra-access';

		private static $rest_client = null; ///< Objet permettant les requêtes à l'API REST
		private static $trans = null;

		/** Cette fonction permet de récupérer le montant déjà remboursé pour une commande.
		 * 	@param int $ord_id Obligatoire, identifiant d'une commande
		 * 	@return float Le montant déjà remboursé (0 par défaut)
		 */
		public static function getOrderRefund( $ord_id ){
			$ar_trans = self::getOrderTransactions( $ord_id );

			$refund = 0;
			foreach( $ar_trans as $one_trans ){
				if( $one_trans['status']['code'] == 'REFUND' ){
					$refund += $one_trans['amount'] * -1;
				}
			}

			return $refund;
		}

		/** Cette fonction permet de charger un tableau des transactions liées à une commande.
		 * 	@param int $ord_id Obligatoire, identifiant d'une commande
		 * 	@return array Une liste des transaction et pour chacune :
		 * 		- status : information sur le statut
		 * 			- code : code du statut
		 * 			- name : texte associé au code
		 * 		- amount : montant de la transaction
		 * 		- currency : devise utilisée lors de la transaction
		 * 		- timestamp : timestamp de la transaction (utilisé surtout pour le tri - la plus récente en premier)
		 * 		- date : Objet DateTime de la date de transaction
		 * 		- error : information sur l'erreur
		 * 			- code : code erreur (vide si aucune erreur)
		 * 			- msg : message associé au code erreur (vide si aucune erreur)
		 */
		public static function getOrderTransactions( $ord_id ){
			if( !is_numeric($ord_id) || $ord_id <= 0 ){
				return [];
			}

			if( self::$trans === null ){
				self::$trans = [];
				self::restInit();
			}

			$res = self::$rest_client->post( '/V4/Order/Get', [
				'orderId' => $ord_id,
			]);

			if( isset($res['answer']['transactions']) && is_array($res['answer']['transactions']) ){
				foreach( $res['answer']['transactions'] as $one_trans ){
					$date = new DateTime( $one_trans['creationDate'] );

					if( isset($one_trans['transactionDetails']['creationContext']) && $one_trans['transactionDetails']['creationContext'] == 'REFUND' ){
						$one_trans['detailedStatus'] = 'REFUND';
						$one_trans['amount'] = $one_trans['amount'] * -1;
					}

					self::$trans[ $one_trans['uuid'] ] = [
						'status' => [
							'code' => $one_trans['detailedStatus'],
							'name' => self::statusLabel( $one_trans['detailedStatus'] )
						],
						'amount' => $one_trans['amount'] / 100,
						'currency' => $one_trans['currency'],
						'timestamp' => $date->getTimestamp(),
						'date' => $date,
						'error' => [
							'code' => $one_trans['detailedErrorCode'],
							'msg' => self::errorMessage( $one_trans['detailedErrorCode'] )
						]
					];
				}
			}

			self::$trans = array_msort( self::$trans, ['timestamp' => SORT_DESC] );

			return self::$trans;
		}

		/** Cette fonction permet de réaliser un remboursement partiel ou total d'une transaction.
		 * 	@param string $uuid Obligatoire, identifiant d'une transaction
		 * 	@param float $amount Obligatoire, montant du remboursement
		 * 	@param bool true si le remboursement s'est correctement déroulé, sinon une exception est levée
		 */
		public function refund( $uuid, $amount ){
			self::restInit();

			// Contrôle que la transaction existe et permet de faire un remboursement
			$result = self::transactionExist( $uuid, 'CAPTURED' );
			if( $result === -1 ){
				throw new Exception( _('Le statut de la transaction ne permet pas de réaliser un remboursement.') );
			}elseif( $result === false ){
				throw new Exception( _('La transaction n\'existe pas ou plus.') );
			}

			if( !is_numeric($amount) || $amount <= 0 ){
				throw new Exception( _('Le montant du remboursement n\'est pas valide. Celui-ci doit-être inférieur ou égal au montant de la transaction.') );
			}

			// Réalisation du remboursement
			$result = self::$rest_client->post( '/V4/Transaction/CancelOrRefund', [
				'uuid' => $uuid,
				'amount' => ($amount * 100)
			] );

			if( !isset($result['status']) || $result['status'] != 'SUCCESS' ){
				throw new Exception( _('Une erreur inattendue s\'est produite lors du remboursement. Veuillez réessayer.') );
			}

			return true;
		}

		/** Cette fonction permet de mettre à jour le montant pour une transaction.
		 * 	Elle n'est utilisable que si la transaction n'a pas encore été présenté à la banque de l'internaute.
		 * 	@param string $uuid Obligatoire, identifiant d'une transaction
		 * 	@param float $amount Obligatoire, montant du remboursement
		 * 	@param string $currency Obligatoire, devise du montant (ex. EUR)
		 * 	@param bool true si le mise à jour s'est correctement déroulée, sinon une exception est levée
		 */
		public function update( $uuid, $amount, $currency ){
			self::restInit();

			// Contrôle que la transaction existe et permet de faire un remboursement
			$result = self::transactionExist( $uuid, 'AUTHORISED' );
			if( $result === -1 ){
				throw new Exception( _('Le statut de la transaction ne permet pas de réaliser un remboursement.') );
			}elseif( $result === false ){
				throw new Exception( _('La transaction n\'existe pas ou plus.') );
			}

			if( !is_numeric($amount) || $amount <= 0 ){
				throw new Exception( _('Le montant n\'est pas valide. Celui-ci doit-être inférieur ou égal au montant initial de la transaction.') );
			}

			$result = self::$rest_client->post( '/V4/Transaction/Update', [
				'uuid' => $uuid,
				'cardUpdate' => [
					'amount' => intval( $amount * 100 ),
					'currency' => $currency
				]
			]);

			if( !isset($result['status']) || $result['status'] != 'SUCCESS' ){
				throw new Exception( _('Une erreur inattendue s\'est produite lors de la mise à jour. Veuillez réessayer.') );
			}

			return true;
		}

		/** Cette fonction permet de mettre à jour le montant pour une transaction.
		 * 	Elle n'est utilisable que si la transaction n'a pas encore été présenté à la banque de l'internaute.
		 * 	@param string $uuid Obligatoire, identifiant d'une transaction
		 * 	@param float $amount Obligatoire, montant du remboursement
		 * 	@param bool true si le mise à jour s'est correctement déroulée, sinon une exception est levée
		 */
		public function cancel( $uuid ){
			self::restInit();

			// Contrôle que la transaction existe et permet de faire un remboursement
			$result = self::transactionExist( $uuid );
			if( $result !== true ){
				throw new Exception( _('La transaction n\'existe pas ou plus.') );
			}

			$result = self::$rest_client->post( '/V4/Transaction/CancelOrRefund', [
				'uuid' => $uuid
			]);

			if( !isset($result['status']) || $result['status'] != 'SUCCESS' ){
				throw new Exception( _('Une erreur inattendue s\'est produite lors de la mise à jour. Veuillez réessayer.') );
			}

			return true;
		}

		/** Cette fonction permet de vérifier l'existance d'une transaction.
		 * 	@param string $uuid Obligatoire, identifiant d'une transaction
		 * 	@param string $status Optionnel, permet de vérifier en même temps le statut
		 * 	@return bool true si la transaction est bien existante, false dans le cas contraire
		 * 	@return int -1 si le statut n'est pas conforme à la demande
		 */
		private static function transactionExist( $uuid, $status='' ){
			if( trim($uuid) == '' ){
				return false;
			}

			self::restInit();

			$result = self::$rest_client->post( '/V4/Transaction/Get', [
				'uuid' => $uuid
			]);

			if( !isset($result['status']) || $result['status'] != 'SUCCESS' ){
				return false;
			}

			// Vérifie le statut
			if( trim($status) != '' ){
				if( $result['answer']['detailedStatus'] != trim($status) ){
					return -1;
				}
			}

			return true;
		}

		/** Cette fonction permet d'initialiser la connexion à l'API REST de Lyra
		 * 	@return empty
		 */
		private static function restInit(){
			global $config;

			if( self::$rest_client === null ){
				if( !isset($config['payzen_api_rest']) || trim($config['payzen_api_rest']) == '' ){
					throw new Exception('Connexion Lyra API impossible : clés d\'accès non renseignées.');
				}

				$ar_keys_api = json_decode( $config['payzen_api_rest'], true );
				if( !ria_array_key_exists(['username', 'password', 'publickey', 'endpoint', 'sha256key'], $ar_keys_api) ){
					throw new Exception('Connexion Lyra API impossible : clés d\'accès incomplètes.');
				}

				self::$rest_client = new Lyra\Client();

				self::$rest_client->setUsername( $ar_keys_api['username'] );
				self::$rest_client->setPassword( $ar_keys_api['password'] );
				self::$rest_client->setPublicKey( $ar_keys_api['publickey'] );
				self::$rest_client->setEndpoint( $ar_keys_api['endpoint'] );
				self::$rest_client->setSHA256Key( $ar_keys_api['sha256key'] );
			}
		}

		/** Cette fonction retourne le texte associé à un code statut d'une transaction.
		 * 	@param string $status Obligatoire, code statut de la transaction
		 * 	@return string Le texte associé au code, "Non défini" si le statut n'est pas prit en compte
		 */
		private static function statusLabel( $status ){
			$array = [
				'ABANDONED' => _('Abandonné'),
				'ACCEPTED' => _(' Accepté'),
				'AUTHORISED' => _(' En attente de remise'),
				'AUTHORISED_TO_VALIDATE' => _(' À valider'),
				'CANCELLED' => _('Annulé'),
				'CAPTURED' => _(' Présenté'),
				'CAPTURE_FAILED' => _(' Echec remise en banque'),
				'EXPIRED' => _('Expiré'),
				'REFUSED' => _('Refusé'),
				'SUSPENDED' => _('Suspendu'),
				'UNDER_VERIFICATION' => _(' Vérification en cours'),
				'WAITING_AUTHORISATION' => _('En attente d\'autorisation'),
				'WAITING_AUTHORISATION_TO_VALIDATE' => _('A valider et autoriser'),
				'REFUND' => _('Remboursement'),
			];

			$tmp = _('Non défini');
			if( array_key_exists($status, $array) ){
				$tmp = $array[ $status ];
			}

			return $tmp;
		}

		/** Cette fonction retourne le texte associé à un code erreur.
		 * 	@param string $code Obligatoire, code erreur
		 * 	@return string Le texte associée au code erreur, vide si aucun code n'est donné et "Non défini" si le code n'est pas prit en compte
		 */
		private static function errorMessage( $code ){
			if( trim($code) == '' ){
				return '';
			}

			$array = [
				1 => _('La transaction n\'a pas été trouvée.'),
				2 => _('La transaction n\'a pas été trouvée.'),
				3 => _('Cette action n\'est pas autorisée sur une transaction ayant ce statut {0}.'),
				4 => _('Cette transaction n\'est pas autorisée dans ce contexte.'),
				5 => _('La transaction existe déjà.'),
				6 => _('Montant de transaction invalide.'),
				7 => _('Cette action n\'est plus possible pour une transaction créée à cette date.'),
				8 => _('La date d\'expiration du moyen de paiement ne permet pas cette action.'),
				9 => _('Cryptogramme visuel obligatoire.'),
				10 => _('Le montant de remboursement est supérieur au montant initial.'),
				11 => _('La somme des remboursements effectués est supérieure au montant initial.'),
				12 => _('La duplication d\'un crédit (remboursement) n\'est pas autorisée.'),
				13 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				14 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				15 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				16 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				19 => _('Devise inconnue.'),
				20 => _('Moyen de paiement invalide.'),
				21 => _('Aucun contrat marchand trouvé pour ce paiement. Veuillez modifier les données ou joindre votre contact commercial en cas d\'échecs répétés.'),
				22 => _('Boutique non trouvée.'),
				23 => _('Contrat marchand ambigu.'),
				24 => _('Contrat marchand invalide.'),
				25 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				26 => _('Numéro de carte invalide'),
				27 => _('Numéro de carte invalide.'),
				28 => _('Numéro de carte invalide.'),
				29 => _('Numéro de carte invalide.'),
				30 => _('Numéro de carte invalide (Luhn).'),
				31 => _('Numéro de carte invalide (longueur).'),
				32 => _('Le numéro de carte ne correspond pas au moyen de paiement sélectionné.'),
				33 => _('Le numéro de carte ne correspond pas au moyen de paiement sélectionné.'),
				34 => _('Contrôle carte à autorisation systématique en échec.'),
				35 => _('Contrôle e-Carte Bleue en échec.'),
				36 => _('Le contrôle des risques a provoqué le refus de la transaction.'),
				37 => _('Interruption non gérée lors du processus de paiement.'),
				38 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				39 => _('Refus 3D Secure pour la transaction.'),
				40 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				41 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				42 => _('Une erreur interne est survenue lors de la consultation du numéro de carte.'),
				43 => _('Une erreur interne est survenue lors de la consultation du numéro de carte.'),
				44 => _('Cette action n\'est pas autorisée pour les transactions de proximité.'),
				45 => _('Devise invalide pour la modification.'),
				46 => _('Le montant est supérieur au montant autorisé.'),
				47 => _('La date de remise souhaitée est postérieure à la date de validité de l\'autorisation.'),
				48 => _('La modification requise est invalide.'),
				49 => _('Définition du paiement en N fois invalide.'),
				50 => _('Boutique inconnue.'),
				51 => _('Cours inconnu.'),
				52 => _('Le contrat est clos depuis le {0}.'),
				53 => _('La boutique {0} est close depuis le {1}.'),
				54 => _('Paramètre rejeté pouvant contenir des données sensibles {0}.'),
				55 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				56 => _('Le montant est inférieur au montant minimum autorisé.'),
				57 => _('Erreur lors de la récupération de l\'alias.'),
				58 => _('L\'état de cet alias n\'est pas compatible avec cette opération.'),
				59 => _('Erreur lors de la récupération de l\'alias.'),
				60 => _('Alias existant.'),
				61 => _('Alias invalide.'),
				62 => _('Création d\'un alias refusée.'),
				63 => _('Abonnement déjà existant.'),
				64 => _('Cet abonnement est déjà résilié.'),
				65 => _('Cet abonnement est invalide.'),
				66 => _('La règle d\'abonnement n\'est pas valide.'),
				67 => _('Création de l\'abonnement refusée.'),
				68 => _('Annulation refusée.'),
				69 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				70 => _('Code pays invalide.'),
				71 => _('Paramètre du service web invalide.'),
				72 => _('Refus d\'autorisation par Cofinoga.'),
				73 => _('Refus de l\'autorisation à 1 EUR (ou demande de renseignement sur le réseau CB si l\'acquéreur le supporte).'),
				74 => _('Configuration de paiement invalide.'),
				75 => _('L\'opération a été refusée par PayPal.'),
				76 => _('Le nom du porteur est absent.'),
				77 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				78 => _('Identifiant de transaction non défini.'),
				79 => _('Identifiant de transaction déjà utilisé.'),
				80 => _('Identifiant de transaction expiré.'),
				81 => _('Contenu du thème config invalide.'),
				82 => _('Le remboursement n\'est pas autorisé pour ce contrat.'),
				83 => _('Montant de transaction en dehors des valeurs permises.'),
				84 => _('Remise non autorisée pour la transaction {0} avec le numéro de commande {1} car non encore enregistrée dans un fichier CNAB/Remessa.'),
				85 => _('Commission absente lors de la remise de boleto.'),
				86 => _('Remise(s) non autorisée(s) pour la (les) transaction(s) {0} car non encore enregistrée(s) dans un fichier CNAB/Remessa.'),
				87 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				88 => _('Remboursement impossible : le remboursement des transactions est interdit par PayPal au-delà de 60 jours.'),
				89 => _('La modification n\'est pas autorisée.'),
				90 => _('Une erreur est apparue lors du remboursement de cette transaction.'),
				91 => _('Aucune option de paiement activée pour ce contrat.'),
				92 => _('Une erreur est survenue lors du calcul du canal de paiement.'),
				93 => _('Une erreur est survenue lors du retour de l\'acheteur sur la page de finalisation de paiement.'),
				94 => _('Une erreur technique est survenue lors de l\'appel au service RSP.'),
				96 => _('Une erreur est apparue lors de la remise de cette transaction.'),
				97 => _('La date de remise demandée est trop éloignée.'),
				98 => _('Date de transaction invalide.'),
				99 => _('Une erreur est survenue lors du calcul de l\'origine du paiement.'),
				100 => _('Contrôle carte commerciale en échec.'),
				101 => _('Refusé car première échéance refusée.'),
				103 => _('Le statut de la transaction n\'a pas pu être synchronisé avec le système externe.'),
				104 => _('Une erreur est apparue lors de la remise de cette transaction.'),
				105 => _('3D Secure - Signature du message d’authentification invalide (Pares).'),
				106 => _('Devise non supportée pour ce contrat et/ou cette boutique.'),
				107 => _('Le moyen de paiement associé à l\'alias n\'est plus valide.'),
				108 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				109 => _('Timeout lors de la redirection de l\'acheteur.'),
				110 => _('Moyen de paiement non supportée par le contrat.'),
				111 => _('Refus des transactions sans Transfert de responsabilité.'),
				112 => _('L\'annulation n\'est pas autorisée.'),
				113 => _('La duplication n\'est pas autorisée.'),
				115 => _('Le remboursement n\'est pas autorisé.'),
				116 => _('Paiement manuel non autorisé pour ce moyen de paiement.'),
				118 => _('Paiement en N fois non autorisé pour ce moyen de paiement.'),
				119 => _('La date soumise est invalide.'),
				120 => _('L\'option de paiement de la transaction initiale n\'est pas applicable.'),
				124 => _('Moyen de paiement inactif.'),
				125 => _('Paiement refusé par l\'acquéreur.'),
				126 => _('Cette action n\'est pas possible car la séquence de paiement n\'est pas terminée.'),
				128 => _('Moyen de paiement invalide.'),
				129 => _('Code PIN invalide.'),
				130 => _('Solde épuisé'),
				131 => _('Solde insuffisant'),
				136 => _('Refus des transactions dérivées, sans sur la transaction primaire.'),
				137 => _('La transaction est un doublon.'),
				138 => _('Le remboursement partiel n\'est pas possible sur cette transaction.'),
				139 => _('Remboursement refusé.'),
				140 => _('Un problème technique est survenu lors du paiement.'),
				141 => _('L\'analyseur de risque a rejeté cette transaction.'),
				142 => _('Le moyen de paiement utilisé n\'est pas valide pour le mode de paiement demandé.'),
				143 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				144 => _('Une transaction en mode production a été marquée en mode test chez l\'acquéreur.'),
				145 => _('Une transaction en mode test a été marquée en mode production chez l\'acquéreur.'),
				146 => _('Code SMS invalide.'),
				147 => _('Le module de gestion de fraudes a demandé le refus de cette transaction.'),
				148 => _('Aucun contrat compatible trouvé.'),
				149 => _('La durée de la session de paiement a expiré (cas de l\'acheteur qui est redirigé vers l\'ACS et qui ne finalise pas l\'authentification 3D Secure).'),
				150 => _('Aucun contrat compatible trouvé.'),
				151 => _('Une transaction Facily Pay ne peut pas être annulée/modifiée/remboursée entre 23h30 et 5h30.'),
				152 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				153 => _('Une erreur technique est survenue lors de l\'appel au service Banque Accord.'),
				155 => _('La transaction Facily Pay n\'a pu être annulée/modifiée/remboursée : l\'état de la transaction ne permet pas de réaliser l\'action demandée. Rappel concernant une transaction Facily Pay : un remboursement doit respecter un délai de deux jours après la remise, le délai entre deux remboursements est d\'un jour, un remboursement partiel est limité à 20 jours, un remboursement total est limité à 6 mois.'),
				156 => _('Opération non supportée.'),
				158 => _('Suite à un incident technique, nous ne sommes pas en mesure de traiter votre demande.'),
				159 => _('Le montant est inférieur au montant minimum autorisé (minimum={0} {1}).'),
				160 => _('Il est impossible de rembourser la transaction {0} car elle a fait l\'objet d\'un impayé.'),
				161 => _('La modification a échoué car l\'option de paiement choisie n\'est pas disponible.'),
				162 => _('La modification a échoué car l\'option de paiement choisie n\'est plus valide.'),
				163 => _('La modification a échoué car l\'option de paiement choisie n\'existe pas.'),
				164 => _('Option de paiement invalide.'),
				165 => _('Le type de document d\'identité est présent, mais son numéro est absent.'),
				166 => _('Le numéro de document d\'identité est présent, mais son type est absent.'),
				167 => _('Le type du document d\'identité est inconnu.'),
				168 => _('Le numéro du document d\'identité est invalide.'),
				169 => _('Les données spécifiques devant être transmises à l\'acquéreur sont invalides.'),
				170 => _('Le paiement différé n\'est pas autorisé.'),
				171 => _('Le nombre de mois pour le paiement différé n\'est pas autorisé.'),
				172 => _('La cinématique de paiement sélectionnée est invalide.'),
				173 => _('Erreur sur le service Express Checkout de PayPal.'),
				174 => _('Emetteur de carte non disponible.'),
				175 => _('Annulation impossible, veuillez tenter un remboursement.'),
				176 => _('Remboursement impossible, veuillez tenter une annulation.'),
				177 => _('Aucune réponse à la demande d\'autorisation n\'a été reçue dans le délai imparti.'),
				178 => _('Annulation impossible, la transaction a déjà été annulée.'),
				179 => _('Le statut de la transaction est inconnue.'),
				182 => _('L\'identifiant national du client est absent.'),
				183 => _('Le format de l\'identifiant national du client est incorrect.'),
				184 => _('L\'e-mail est absent.'),
				186 => _('Le montant minimum autorisé ne peut être inférieur à 80% du montant initial.'),
				187 => _('Afin de rembourser la transaction, veuillez contacter RBM à l\'adresse e-mail <EMAIL>.'),
				188 => _('Afin de rembourser la transaction, veuillez contacter Credibanco à l\'adresse e-mail <EMAIL>.'),
				189 => _('Afin de rembourser la transaction, veuillez contacter Davivienda à l\'adresse e-mail <EMAIL>.'),
				190 => _('Le motif de refus ne permet pas la duplication de la transaction.'),
				191 => _('L\'adresse de facturation est absente ou incomplète.'),
				192 => _('La capture manuelle n\'est pas autorisée pour ce type de contrat.'),
				193 => _('Ampliation refusée par l\'émetteur. Cet échec d’autorisation d’ampliation n’a pas de conséquence sur l’autorisation initiale qui reste valable.'),
				194 => _('Un crédit n\'est pas autorisé sur ce type de transaction.'),
				195 => _('Le montant éligible en TRD est invalide.'),
				196 => _('Le montant éligible en TRD est négatif.'),
				197 => _('Le montant éligible en TRD est supérieur au montant de la commande.'),
				198 => _('Les données transmises pour le réseau CONECS dans le champ vads_acquirer_transient_data ne contiennent pas la clé eligibleAmount.'),
				199 => _('Le montant éligible en TRD est inférieur à 1.50€'),
				200 => _('Les données spécifiques devant être transmises à l\'acquéreur sont invalides.'),
				201 => _('Le nom de l\'acheteur est absent ou incomplet.'),
				202 => _('Identifiant de paiement annulé.'),
				203 => _('Vérification du moyen de paiement refusée.'),
				204 => _('Une erreur est apparue lors de l\'annulation de cette transaction.'),
				205 => _('3D Secure - DS ou ACS Injoignable'),
				206 => _('3D Secure - Une erreur technique est survenue lors du processus'),
				207 => _('3D Secure - Refus de l’authentification par l’émetteur'),
				208 => _('3D Secure - Refus car authentification par l\'émetteur impossible'),
				210 => _('Duplication de transaction de vérification interdite.'),
				211 => _('Afin de rembourser la transaction, veuillez contacter Tuya.'),
				212 => _('Afin de rembourser la transaction, veuillez contacter BigPass Edenred Colombie à l\'adresse e-mail <EMAIL>.'),
				213 => _('3D Secure - Session altérée par l\'ACS.'),
				214 => _('Le numéro de carte n\'est pas éligible pour ce paiement.'),
				215 => _('Erreur interne de l\'acquéreur.'),
			];

			$tmp = _('Non défini');
			if( array_key_exists($code, $array) ){
				$tmp = $array[ $code ];
			}

			return $tmp;
		}
	}

/// @}
