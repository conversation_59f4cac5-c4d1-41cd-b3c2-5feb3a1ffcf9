# BeezUPCommonLinkParameter2

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** | The name of the parameter | 
**value** | **string** | The value of the parameter | [optional] 
**required** | **bool** |  | [optional] 
**in** | [**\Swagger\Client\Model\BeezUPCommonParameterIn**](BeezUPCommonParameterIn.md) |  | [optional] 
**type** | [**\Swagger\Client\Model\BeezUPCommonParameterType**](BeezUPCommonParameterType.md) |  | [optional] 
**lov_link** | [**\Swagger\Client\Model\BeezUPCommonLOVLink2**](BeezUPCommonLOVLink2.md) | This parameter expect the values indicated in this list of values. | [optional] 
**lov_required** | **bool** | If true, you MUST use indicate a value from the list of values otherwise it&#39;s a freetext | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


