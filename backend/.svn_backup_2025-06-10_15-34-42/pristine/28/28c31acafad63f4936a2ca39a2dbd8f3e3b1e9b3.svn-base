<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  4915020 => 'Interactive digital media',
  4915050 => 'NAKA AG',
  4915080 => 'Easy World',
  49151 => 'T-Mobile',
  491520 => 'Vodafone',
  491521 => 'Vodafone/Lycamobile',
  491522 => 'Vodafone',
  491523 => 'Vodafone',
  491525 => 'Vodafone',
  491526 => 'Vodafone',
  491529 => 'Vodafone/Truphone',
  4915555 => 'Tismi BV',
  4915630 => 'Multiconnect',
  4915678 => 'Argon Networks',
  491570 => 'Eplus/Telogic',
  491573 => 'Eplus',
  491575 => 'Eplus',
  491577 => 'Eplus',
  491578 => 'Eplus',
  491579 => 'Eplus/Sipgate',
  4915888 => 'TelcoVillage',
  491590 => 'O2',
  49160 => 'T-Mobile',
  49162 => 'Vodafone',
  49163 => 'Eplus',
  49170 => 'T-Mobile',
  49171 => 'T-Mobile',
  49172 => 'Vodafone',
  49173 => 'Vodafone',
  49174 => 'Vodafone',
  49175 => 'T-Mobile',
  49176 => 'O2',
  49177 => 'Eplus',
  49178 => 'Eplus',
  49179 => 'O2',
);
