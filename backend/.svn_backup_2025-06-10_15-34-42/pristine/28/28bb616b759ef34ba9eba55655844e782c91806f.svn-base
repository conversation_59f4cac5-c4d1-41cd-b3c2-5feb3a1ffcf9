//#################################################################//
//						Gestion des popup RIA		   			   //
//																   //
// 			<PERSON><PERSON> fonctionner, ce script à besoin de :			   //
//					- +jquery.min.js							   //
//					- jquery.datepicker.js						   //
// 					- +jquery-ui-1.8.2.custom.min.js			   //
//																   //
//#################################################################//
	
	// Gestion du déplacement
	$(document).ready(function () {
		if( !$('#popup_ria').length ){
			$('body').append('<div class="maxipopup" id="popup_ria"><div class="popup_ria_drag"></div><div class="content"></div></div>');
		}

		$('body').append('<div id="popup_ria_shadow"></div>');
		$('#popup_ria').draggable({ 
			handle: '.popup_ria_drag',
			containment: 'document',
			cursorAt: { top: 5 },
			refreshPositions: true,
			start: function(){
				window.onresize = false;
				window.onscroll = false;
			},
			stop: function(){
				window.onresize = positionPopup;
				window.onscroll = positionPopup;
			}
		});

		window.onresize = positionPopup;
		window.onscroll = positionPopup;

		window.addEventListener('resize', function() {
			if( window.matchMedia(devices.mobile).matches ){
				mobileHeightFix($('#popup_ria'));
			}
		})
		

	}).delegate(
		'#popup_ria_shadow', 'click', function(){
			if( $('#popup_ria').hasClass('noclose') ){
				return true;
			}

			$('.popup_ria_drag .close').parent().click();
		}
	);
	
	/*
	 *	Cette fonction permet de rechercher la position du scroll
	*/
	function getScrollPositionPopup()
	{
			return Array((document.documentElement && document.documentElement.scrollLeft) || window.pageXOffset || self.pageXOffset || document.body.scrollLeft,(document.documentElement && document.documentElement.scrollTop) || window.pageYOffset || self.pageYOffset || document.body.scrollTop);
	}

	/**
     * Cette fonction permet de mettre à jour la position de la popup ainsi que de la redimensionner à la taille de la fenêtre de l'utilisateur
	 */
	function positionPopup(){
		if( $('#popup_ria').is(':visible') )
		{
			const currentScroll = getScrollPositionPopup();
			$('#popup_ria').css('top', ( $(window).height() / 2 - $('#popup_ria').height() / 2 + currentScroll[1] )+ 'px');
			$('#popup_ria').css('left', ( $(window).width() / 2 - $('#popup_ria').width() / 2 )  + 'px');
		}
	}
	
	/** Cette fonction est chargé de l'affichage de la popup avec son contenu passé en paramètre
	 *	@param title Titre de la popup
	 *	@param content Contenu de la popup
	 *	@param iframe Url de l'iframe
	 *	@param action Action réalisé lors de la fermeture de la popup
	 *	@param width Largeur de la popup
	 *	@param height Hauteur de la popup
	 *	@param percent Booléan indiquant si les dimensions (width/height) sont exprimées en pourcentage (true) ou en pixels (false, valeur par défaut)
	 */
	function displayPopup(title, content, iframe, action, width, height, percent, noclose){
		// On réinitialise tout le CSS sur la popup pour gérer l'affichage de plusieurs popup à la suite
		$('#popup_ria').removeAttr('style');

		action = action==undefined || !action ? "hidePopup()"  : action;
		noclose = noclose == undefined || !noclose ? false : true;
		
		$("#popup_ria").css('opacity', '1');
		if( percent ){
			if( width ){
				$('#popup_ria').css('width', width + '%');
				$('#popup_ria').css('min-width', width + '%');
			}
			if( height ){
				$('#popup_ria').css('height', height + '%');
			}
		}else{
			if( width ){
				$('#popup_ria').css('width', width + 'px');
				$('#popup_ria').css('min-width', width + 'px');
			}
			if( height ){
				$('#popup_ria').css('height', height + 'px');
			}
		}
		// pour le mobile uniquement: 
		if( window.matchMedia(devices.mobile).matches ){
			mobileHeightFix($('#popup_ria'));
		}

		var popuphtml = '<div class="drag"></div><div class="text">' + escapeHtml( title ) + '</div>';
		if( !noclose ){
			popuphtml += '<a onclick="return '+action+';"><div class="close"></div></a>';
		}
		
		// Titre
		$('#popup_ria .popup_ria_drag').html(popuphtml);
		
		// Corps
		$('#popup_ria .content').html(content);

		if( noclose ){
			$('#popup_ria').addClass('noclose');
		}

		if( iframe!=='' ){
			// Ajout d'une iframe
			$('#popup_ria .content').html(
				  '<div class="popup_ria_back_load abs"></div>'
				+ '<div class="popup_ria_back_notice notice">' + msgLoading + '</div><iframe class="popup_ria_iframe" name="join-file" src="'+iframe+'"></iframe>'
			);
			$('.popup_ria_iframe').load(function(){$('.popup_ria_back_load, .popup_ria_back_notice').remove();});
		}
		
		// Gestion de la taille de la popup selon la résolution de l'écran
		if( $('#popup_ria .content').html() != "" ){
			currentScroll = getScrollPositionPopup();
			$('#popup_ria').css('top', ( $(window).height() / 2 - $('#popup_ria').height() / 2 + currentScroll[1] )+ 'px');
			$('#popup_ria').css('left', ( $(window).width() / 2 - $('#popup_ria').width() / 2 )  + 'px');
			$('#popup_ria, #popup_ria_shadow').show();
		} else {
			$('#popup_ria, #popup_ria_shadow').hide();
		}
		
		$(document).keyup(function () {
		}).keydown(function (e) {
			if( e.which==27 ){
				$('.popup_ria_drag .close').parent().click();
			}
		});
	}
	
	/*
	 *	Cette fonction permet de changer l'action qui se réalisera lors de la fermeture
	 */
	function changePopupActionOnClick( funct){
		$('#popup_ria .close').parent().removeAttr('onclick');
		$('#popup_ria .close').click( funct );	
	}
	
	/*
	 * Cette fonction permet de fermer une popup
	 */
	function hidePopup(reload = false){
		$('#popup_ria, #popup_ria_shadow').hide(function(){
			if(reload){
				window.location.reload();
			}
 		});
	}
	
	/** Fonction pour l'affichage de la popup de recherche, uniquement valable pour la page stat / recherche actuellement 
	 */
	function show_search(chaine,seg,scc,section,cnt,active){

		var minichaine;

		if( scc==0 && parseInt(chaine)>=0 ){
			chaine = $("#replace-"+chaine).val();
		}
		if( chaine.length>20 ){
			minichaine = chaine.substr(0,20);
			minichaine += "...";
		}else{
			minichaine = chaine;
		}
		
		active = active!=undefined && active==0 ? 0 : 1;
		
		var url = '../stats/js_search.php?seg='+seg+'&amp;scc='+scc+'&amp;search='+encodeURIComponent(chaine)+'&amp;section='+section+'&amp;cnt='+cnt+'&amp;active='+active;
		displayPopup(popupRiaRechercheDe + ' : ' +minichaine, '', url);	
		$('#popup_riawysiwyg').fadeIn();
		return false;
		
	}
	

