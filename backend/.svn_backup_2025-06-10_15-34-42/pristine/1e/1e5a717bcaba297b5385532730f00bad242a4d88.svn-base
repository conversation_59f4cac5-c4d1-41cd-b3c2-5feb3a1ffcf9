<?php
/**
 *  \defgroup Hierarchy Hiérarchie 
 *  \ingroup OnBoardingYuto
 *  @{
 * 
 *	\page api-actions-hierarchy-get Chargement
 *
 *	Cette fonction renvoie la hiérarchie des actions à effectuer
 *
 *	\code
 *		GET /actions/hierarchy/
 *  \endcode
 *	
 * @return Liste de hiérarchie des actions contenant les colonnes :
 *		    - parent_id : identifiant de l'action parent
 *			- child_id : identifiant de l'action enfant
 *
*/
	require_once('act_actions.inc.php');

	switch( $method ){
		case 'get':

			$rstates = act_actions_hierarchy_get()  ;
			if( $rstates && ria_mysql_num_rows($rstates) ){
				while($state = ria_mysql_fetch_assoc($rstates)){
					$content[] = $state;
				}
			}
			$result = true;


			break;
	}
/// @}