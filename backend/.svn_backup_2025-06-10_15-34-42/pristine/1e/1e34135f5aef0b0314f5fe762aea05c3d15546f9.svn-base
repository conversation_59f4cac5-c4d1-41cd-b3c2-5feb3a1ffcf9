<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/resources.proto

namespace Google\Cloud\ServiceManagement\V1\Rollout;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Strategy that specifies how clients of Google Service Controller want to
 * send traffic to use different config versions. This is generally
 * used by API proxy to split traffic based on your configured percentage for
 * each config version.
 * One example of how to gradually rollout a new service configuration using
 * this
 * strategy:
 * Day 1
 *     Rollout {
 *       id: "example.googleapis.com/rollout_20160206"
 *       traffic_percent_strategy {
 *         percentages: {
 *           "example.googleapis.com/20160201": 70.00
 *           "example.googleapis.com/20160206": 30.00
 *         }
 *       }
 *     }
 * Day 2
 *     Rollout {
 *       id: "example.googleapis.com/rollout_20160207"
 *       traffic_percent_strategy: {
 *         percentages: {
 *           "example.googleapis.com/20160206": 100.00
 *         }
 *       }
 *     }
 *
 * Generated from protobuf message <code>google.api.servicemanagement.v1.Rollout.TrafficPercentStrategy</code>
 */
class TrafficPercentStrategy extends \Google\Protobuf\Internal\Message
{
    /**
     * Maps service configuration IDs to their corresponding traffic percentage.
     * Key is the service configuration ID, Value is the traffic percentage
     * which must be greater than 0.0 and the sum must equal to 100.0.
     *
     * Generated from protobuf field <code>map<string, double> percentages = 1;</code>
     */
    private $percentages;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array|\Google\Protobuf\Internal\MapField $percentages
     *           Maps service configuration IDs to their corresponding traffic percentage.
     *           Key is the service configuration ID, Value is the traffic percentage
     *           which must be greater than 0.0 and the sum must equal to 100.0.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Servicemanagement\V1\Resources::initOnce();
        parent::__construct($data);
    }

    /**
     * Maps service configuration IDs to their corresponding traffic percentage.
     * Key is the service configuration ID, Value is the traffic percentage
     * which must be greater than 0.0 and the sum must equal to 100.0.
     *
     * Generated from protobuf field <code>map<string, double> percentages = 1;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getPercentages()
    {
        return $this->percentages;
    }

    /**
     * Maps service configuration IDs to their corresponding traffic percentage.
     * Key is the service configuration ID, Value is the traffic percentage
     * which must be greater than 0.0 and the sum must equal to 100.0.
     *
     * Generated from protobuf field <code>map<string, double> percentages = 1;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setPercentages($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::DOUBLE);
        $this->percentages = $arr;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(TrafficPercentStrategy::class, \Google\Cloud\ServiceManagement\V1\Rollout_TrafficPercentStrategy::class);

