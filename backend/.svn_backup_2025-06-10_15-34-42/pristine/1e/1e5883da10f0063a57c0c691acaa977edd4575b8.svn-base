<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="%i">
  <project timestamp="%i" name="BankAccount">
    <file name="%s/BankAccount.php">
      <class name="BankAccount" namespace="global">
        <metrics complexity="5" methods="4" coveredmethods="3" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="5" elements="14" coveredelements="8"/>
      </class>
      <line num="6" type="method" name="getBalance" visibility="public" complexity="1" crap="1" count="2"/>
      <line num="8" type="stmt" count="2"/>
      <line num="11" type="method" name="setBalance" visibility="protected" complexity="2" crap="6" count="0"/>
      <line num="13" type="stmt" count="0"/>
      <line num="14" type="stmt" count="0"/>
      <line num="15" type="stmt" count="0"/>
      <line num="16" type="stmt" count="0"/>
      <line num="18" type="stmt" count="0"/>
      <line num="20" type="method" name="depositMoney" visibility="public" complexity="1" crap="1" count="2"/>
      <line num="22" type="stmt" count="2"/>
      <line num="24" type="stmt" count="1"/>
      <line num="27" type="method" name="withdrawMoney" visibility="public" complexity="1" crap="1" count="2"/>
      <line num="29" type="stmt" count="2"/>
      <line num="31" type="stmt" count="1"/>
      <metrics loc="33" ncloc="33" classes="1" methods="4" coveredmethods="3" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="5" elements="14" coveredelements="8"/>
    </file>
    <metrics files="1" loc="33" ncloc="33" classes="1" methods="4" coveredmethods="3" conditionals="0" coveredconditionals="0" statements="10" coveredstatements="5" elements="14" coveredelements="8"/>
  </project>
</coverage>
