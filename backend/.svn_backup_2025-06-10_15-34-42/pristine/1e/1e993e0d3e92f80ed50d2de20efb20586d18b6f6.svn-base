# CreateStoreRequest

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | [**\Swagger\Client\Model\BeezUPCommonStoreId**](BeezUPCommonStoreId.md) |  | [optional] 
**name** | [**\Swagger\Client\Model\StoreName**](StoreName.md) |  | 
**url** | [**\Swagger\Client\Model\StoreUrl**](StoreUrl.md) |  | 
**country_iso_code_alpha3** | [**\Swagger\Client\Model\StoreCountryIsoCodeAlpha3**](StoreCountryIsoCodeAlpha3.md) |  | 
**sectors** | [**\Swagger\Client\Model\StoreSectors**](StoreSectors.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


