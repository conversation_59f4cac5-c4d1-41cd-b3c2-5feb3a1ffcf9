
services:
    service_container:
        class: Symfony\Component\DependencyInjection\ContainerInterface
        synthetic: true
    foo:
        autoconfigure: true
        abstract: true
        arguments: ['@!bar']
    Psr\Container\ContainerInterface:
        alias: service_container
        public: false
    Symfony\Component\DependencyInjection\ContainerInterface:
        alias: service_container
        public: false
