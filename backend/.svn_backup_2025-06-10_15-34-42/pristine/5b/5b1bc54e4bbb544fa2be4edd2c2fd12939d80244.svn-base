<?php

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_COMPARATOR');

	require_once('comparators.inc.php');
	require_once('products.inc.php');

	if( !isset($_GET['ctr'], $_GET['attr']) || !ctr_comparators_exists($_GET['ctr']) ){
		$error_load = _("Une ou plusieurs informations obligatoires sont manquantes.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
	}

	if (!isset($error_load)){
		// $r_attr = ctr_cat_fields_get( $_GET['ctr'], $_GET['attr'] );
		$r_attr = ctr_cat_fields_get( $_GET['ctr'], 0, '', true, false, null, false, $_GET['attr'] );
		if( !$r_attr || !ria_mysql_num_rows($r_attr) ){
			$error_load = _("Une ou plusieurs informations obligatoires sont manquantes.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		}
	}

	define('ADMIN_PAGE_TITLE', _('Historique des accès - Comparateurs'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($error_load) ){
		print '<div class="error">'.nl2br( $error_load ).'</div>';
	}else{
		$ctr  = ria_mysql_fetch_assoc( ctr_comparators_get($_GET['ctr']) );
		$attr = ria_mysql_fetch_assoc( $r_attr );

		$ar_prds = ctr_cat_fields_get_products( $_GET['ctr'], $attr['id'] );

		if( IS_AJAX ){
			$can_reload = false;
			
			if( isset($_POST['val']) && is_array($_POST['val']) ){
				foreach( $_POST['val'] as $prd=>$val ){
					if( trim($val) == '' ){
						continue;
					}

					$params = ctr_catalogs_get_params( $_GET['ctr'], $prd );
					if( !is_array($params) ){
						$params = array();
					}

					if( $attr['type_id'] == FLD_TYPE_SELECT ){
						$val = ctr_cat_fields_get_value( $attr['id'], $val );

						if( trim($val) == '' ){
							$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'information.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
							break;	
						}
					}

					$params[ $attr['id'] ] = $val;

					if( !ctr_catalogs_update_params( $_GET['ctr'], $prd, json_encode($params)) ){
						$error = _("Une erreur inattendue s'est produite lors de l'enregistrement de l'information.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
						break;
					}

					$can_reload = true;
				}
			}

			if( !isset($error) ){
				if( $can_reload ){
					$_SESSION['save_popup_edit_prd_attr'] = true;
					$memcached->delete( 'ctr_cat_fields_get_products:ctrid-'.$_GET['ctr'] );
				}
			}

			exit;
		}

		if( isset($error) ){
			print '<div class="error">'.nl2br( $error ).'</div>';
		}elseif( isset($_SESSION['save_popup_edit_prd_attr']) ){
			print '<input type="hidden" name="can_reload_mapping" id="can_reload_mapping" value="1" />';
			print '<script>parent.canReloadMapping = true;</script>';
			unset( $_SESSION['save_popup_edit_prd_attr'] );
		}
?>
	<form action="/admin/comparators/popup-edit-prd-attr.php?ctr=<?php print $_GET['ctr']; ?>&amp;attr=<?php print $_GET['attr']; ?>" method="post">
		<p class="notice"><?php printf(_('Vous pouvez saisir ici l\'information "%s" pour chaque produit rattaché à cet attribut.'), htmlspecialchars( $attr['name'] ) ); ?></p><br />

		<input type="hidden" name="ctr" id="ctr" value="<?php print $_GET['ctr']; ?>" />
		<input type="hidden" name="attr" id="attr" value="<?php print $_GET['attr']; ?>" />

		<table id="list-attributs">
		<col width="300" /><col width="300" />
			<thead>
				<tr>
					<th id="prd"><?php print _('Produit'); ?></th>
					<th id="val"></th>
				</tr>
			</thead>
			<tfoot>
				<tr>
					<!-- td colspan="2" class="align-right">
						<input type="submit" name="save" value="Enregistrer" onclick="return saveMappingAttrProducts();" />
					</td -->
				</tr>
			</tfoot>
			<tbody>
				<?php if( !sizeof($ar_prds) ){ ?>
					<tr><td colspan="2"><?php print _('Tous les produits ont une valeur pour cet attribut'); ?></td></tr>
				<?php 
					}else{
						$ar_vals = array();
						if( $attr['type_id'] == FLD_TYPE_SELECT ){
							$r_val = ctr_cat_field_values_get( $attr['id'] );
							if( $r_val && ria_mysql_num_rows($r_val) ){
								while( $val = ria_mysql_fetch_assoc($r_val) ){
									$ar_vals[] = $val;
								}
							}
						}

						foreach( $ar_prds as $one_prd ){
				?>
				<tr>
					<td headers="prd">
						<a target="_blank" href="/admin/catalog/product.php?cat=0&amp;prd=<?php print $one_prd; ?>"><?php print htmlspecialchars( prd_products_get_name( $one_prd ) ); ?></a>
					</td>
					<td headers="val"><?php
						if( $attr['type_id'] == FLD_TYPE_SELECT ){
							if( count($ar_vals) > 0 ){
								print '<select name="val['.$one_prd.']" style="width:230px;">
									<option value="">Sélectionnez une valeur</option>';

								foreach( $ar_vals as $val ){
									$selected = isset($_POST['val'][ $one_prd ]) && $_POST['val'][ $one_prd ] == $val['id'] ? ' selected="selected"' : '';
								
									print '<option value="'.$val['id'].'"'.$selected.'>'.htmlspecialchars( $val['val'] ).'</option>';
								}

								print '</select>';
							}
						}else{
							print '<input type="text" name="val['.$one_prd.']" value="'.( isset($_POST['val'][ $one_prd ]) ? $_POST['val'][ $one_prd ] : '' ).'" />';
						}
					?></td>
				</tr>
				<?php 
						}
					}
				?>
			</tbody>
		</table>
	</form>
<?php
	}

	require_once('admin/skin/footer.inc.php');
?>