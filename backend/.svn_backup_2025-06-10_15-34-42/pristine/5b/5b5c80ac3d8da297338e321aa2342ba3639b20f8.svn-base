<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/quota_controller.proto

namespace GPBMetadata\Google\Api\Servicecontrol\V1;

class QuotaController
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Api\Servicecontrol\V1\MetricValue::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ae70b0a33676f6f676c652f6170692f73657276696365636f6e74726f6c" .
            "2f76312f71756f74615f636f6e74726f6c6c65722e70726f746f121c676f" .
            "6f676c652e6170692e73657276696365636f6e74726f6c2e76311a2f676f" .
            "6f676c652f6170692f73657276696365636f6e74726f6c2f76312f6d6574" .
            "7269635f76616c75652e70726f746f2291010a14416c6c6f636174655175" .
            "6f74615265717565737412140a0c736572766963655f6e616d6518012001" .
            "280912480a12616c6c6f636174655f6f7065726174696f6e18022001280b" .
            "322c2e676f6f676c652e6170692e73657276696365636f6e74726f6c2e76" .
            "312e51756f74614f7065726174696f6e12190a11736572766963655f636f" .
            "6e6669675f696418042001280922a5030a0e51756f74614f706572617469" .
            "6f6e12140a0c6f7065726174696f6e5f696418012001280912130a0b6d65" .
            "74686f645f6e616d6518022001280912130a0b636f6e73756d65725f6964" .
            "18032001280912480a066c6162656c7318042003280b32382e676f6f676c" .
            "652e6170692e73657276696365636f6e74726f6c2e76312e51756f74614f" .
            "7065726174696f6e2e4c6162656c73456e74727912430a0d71756f74615f" .
            "6d65747269637318052003280b322c2e676f6f676c652e6170692e736572" .
            "76696365636f6e74726f6c2e76312e4d657472696356616c756553657412" .
            "4a0a0a71756f74615f6d6f646518062001280e32362e676f6f676c652e61" .
            "70692e73657276696365636f6e74726f6c2e76312e51756f74614f706572" .
            "6174696f6e2e51756f74614d6f64651a2d0a0b4c6162656c73456e747279" .
            "120b0a036b6579180120012809120d0a0576616c75651802200128093a02" .
            "380122490a0951756f74614d6f6465120f0a0b554e535045434946494544" .
            "1000120a0a064e4f524d414c1001120f0a0b424553545f4546464f525410" .
            "02120e0a0a434845434b5f4f4e4c59100322d0010a15416c6c6f63617465" .
            "51756f7461526573706f6e736512140a0c6f7065726174696f6e5f696418" .
            "012001280912410a0f616c6c6f636174655f6572726f727318022003280b" .
            "32282e676f6f676c652e6170692e73657276696365636f6e74726f6c2e76" .
            "312e51756f74614572726f7212430a0d71756f74615f6d65747269637318" .
            "032003280b322c2e676f6f676c652e6170692e73657276696365636f6e74" .
            "726f6c2e76312e4d657472696356616c756553657412190a117365727669" .
            "63655f636f6e6669675f696418042001280922f8010a0a51756f74614572" .
            "726f72123b0a04636f646518012001280e322d2e676f6f676c652e617069" .
            "2e73657276696365636f6e74726f6c2e76312e51756f74614572726f722e" .
            "436f6465120f0a077375626a65637418022001280912130a0b6465736372" .
            "697074696f6e1803200128092286010a04436f6465120f0a0b554e535045" .
            "434946494544100012160a125245534f555243455f455848415553544544" .
            "100812160a1242494c4c494e475f4e4f545f414354495645106b12130a0f" .
            "50524f4a4543545f44454c45544544106c12130a0f4150495f4b45595f49" .
            "4e56414c4944106912130a0f4150495f4b45595f45585049524544107032" .
            "c2010a0f51756f7461436f6e74726f6c6c657212ae010a0d416c6c6f6361" .
            "746551756f746112322e676f6f676c652e6170692e73657276696365636f" .
            "6e74726f6c2e76312e416c6c6f6361746551756f7461526571756573741a" .
            "332e676f6f676c652e6170692e73657276696365636f6e74726f6c2e7631" .
            "2e416c6c6f6361746551756f7461526573706f6e7365223482d3e493022e" .
            "22292f76312f73657276696365732f7b736572766963655f6e616d657d3a" .
            "616c6c6f6361746551756f74613a012a4289010a20636f6d2e676f6f676c" .
            "652e6170692e73657276696365636f6e74726f6c2e7631421451756f7461" .
            "436f6e74726f6c6c657250726f746f50015a4a676f6f676c652e676f6c61" .
            "6e672e6f72672f67656e70726f746f2f676f6f676c65617069732f617069" .
            "2f73657276696365636f6e74726f6c2f76313b73657276696365636f6e74" .
            "726f6cf80101620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

