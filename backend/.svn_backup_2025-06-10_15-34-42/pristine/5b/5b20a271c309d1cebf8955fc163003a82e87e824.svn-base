<?php
	require_once('advertising.inc.php');

	/**
	 *@backupGlobals disabled
	 */
	class bannersDelTest extends PHPUnit_Framework_TestCase {

		/** Fonction permettant de tester la suppression d'une bannière
		 */
		public function testBannersDel(){

            $this->assertTrue(adv_banners_del(1), 'Erreur : la bannière n\'a pas été supprimé');
		}

		/** Fonction permettant de vérifier la suppression d'une bannière
		 */
		public function testBannersVerifyDel(){

			$rbnr = adv_banners_get(1);
			$this->assertTrue( $rbnr && ria_mysql_num_rows($rbnr) == 0, 'Erreur lors de la vérification de la suppression de la bannière');
		}
	}
