/**
 * CSS de la catégirie Comparateurs
 */

#tb-ctr-cat, #tb-ctr-prd {
    thead tr th {
        width: 100px;
        text-align: right;
        &#cat {
            width: 200px;
            text-align: left;
        }
        &#cat-margin, &#prd-margin {
            width: 104px;
        }
        &#cat-roi, &#prd-roi {
            width: 84px;
        }
        &#prd-check {
            width: 25px;
        }
        &#prd {
            width : auto;
            text-align: left;
        }
        &#prd-export {
            width: 75px;
        }
    }
}

/**
 * Rechercher des produits
 */

 #zone-filters {
     label + span {
         display: inline-block;
         vertical-align: middle;
     }
 }

 #zone-conditions {
     margin-top: 5px;
    .options {
        margin-top: 0;
        a.del {
            display: inline-block;
            vertical-align: middle;
            width: 15px;
            height: 15px;
            background: url(../../images/del-cat.svg) center no-repeat;
            background-size: contain;
            border-radius: 50%;
        }
    }
 }

 .checklist {
    input[name="save-export"] + * {
        margin-left: 10px;
    }
 }

/**
 * Place de marché
 */
 #site-content {
     .form-notice-container {
        width: 100%;
        @include media('>medium') {
            width: 720px;
        }
        & > form table,
        & > .notice {
            width: 100%;
        }
     }
 }