<?php

namespace Basho\Riak\Command\Builder\Search;

use Basho\Riak\Command;

/**
 * Used to increment counter objects in Riak by the provided positive / negative integer
 *
 * <code>
 * $command = (new Command\Builder\StoreObject($riak))
 *   ->buildObject('{"firstName":"<PERSON>","lastName":"<PERSON><PERSON>","email":"<EMAIL>"}')
 *   ->buildBucket('users')
 *   ->build();
 *
 * $response = $command->execute();
 *
 * $user_location = $response->getLocation();
 * </code>
 *
 * <AUTHOR> <cman<PERSON> at basho d0t com>
 */
class AssociateIndex extends Command\Builder\SetBucketProperties implements Command\BuilderInterface
{
    /**
     * @param $name
     *
     * @return $this
     */
    public function withName($name)
    {
        $this->set('search_index', $name);

        return $this;
    }
}
