<?php

	/**	\file index.php
	 *	Cette page affiche le glossaire de définitions et permet sa gestion
	 */

	require_once('glossary.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_GLOSSARY');

	// Détection de requete ajax
	$ajax = false;
	$lst_rvw = array();
	$xml_status = '';
	$xml_body = '';
	if (isset($_SERVER['HTTP_X_REQUESTED_WITH'])  
        && $_SERVER['HTTP_X_REQUESTED_WITH'] == "XMLHttpRequest") { 
		$ajax = true;
	}
	
	$_GET['comments-type'] = (isset($_GET['comments-type']) &&  is_numeric($_GET['comments-type'])) ? $_GET['comments-type'] : null;
	
	
	// Vérifie les paramètres de suivi pour la liste des définitions
	if( !isset($_GET['comments-type']) )
		$_GET['comments-type'] = '';
	if( !isset($_GET['filter']) )
		$_GET['filter'] = 'all';
	if( !isset($_GET['page']) || !is_numeric($_GET['page']) )
		$_GET['page'] = 1;
	if( !isset($_GET['c']) )
		$_GET['c'] = '';
	
	// Action refresh count
	if( isset($_GET['refresh-count']) ){
		require_once('prd/reviews.inc.php');
		html_count_reviews();
		exit;
	}
	
	
	// Bouton Ajouter une définition
	elseif( isset($_POST['add-review-site']) ){
		header('Location: add.php?page='.$_GET['page'].'&comments-type='.$_GET['comments-type'].'&filter='.$_GET['filter']);
	}
	
	// Bouton Supprimer une définition
	elseif( isset($_POST['delete-word']) ){
		if( is_array($_REQUEST['word']) )
			foreach( $_REQUEST['word'] as $word )
				gsr_words_del($word);
		else
			gsr_words_del($_REQUEST['word']);
		// La suppression ajaxienne n'est pas implémenter sur la page
		if(!$ajax) {
			header('Location: index.php?page='.$_GET['page'].'&comments-type='.$_GET['comments-type'].'&filter='.$_GET['filter'].'&c='.$_GET['c']);
			exit;
		}
	}

	// Bouton Publier une définition
	elseif( isset($_REQUEST['publish-word']) && isset($_REQUEST['word']) ){
		foreach( $_REQUEST['word'] as $word )
		{
			gsr_words_publish($word);
		}
		if(!$ajax) {
			header('Location: index.php?page='.$_GET['page'].'&comments-type=1&filter='.$_GET['filter'].'&c='.$_GET['c']);
			exit;
		}
	}
	
	// Bouton Desapprouver une définition
	elseif( isset($_REQUEST['unapprove-word']) && isset($_REQUEST['word']) ){
		foreach( $_REQUEST['word'] as $word )
		{
			gsr_words_unpublish($word);
		}
		if(!$ajax) {
			header('Location: index.php?page='.$_GET['page'].'&comments-type='.$_GET['comments-type'].'&filter='.$_GET['filter'].'&c='.$_GET['c']);
			exit;
		}
	}
	
	// Bouton Enregistrer une définition
	if( isset($_POST['save-word']) || isset($_POST['action']) && $_POST['action']=='add' ){
		if( !isset($_POST['wrd-publish']) ) $_POST['wrd-publish'] = 0; else $_POST['wrd-publish'] = 1;
		if( !isset($_POST['wrd-name']) ) $_POST['wrd-name'] = '';
		if( !isset($_POST['wrd-desc']) ) $_POST['wrd-desc'] = '';
		if(!gsr_words_add($_POST['wrd-name'],$_POST['wrd-desc'],$_POST['wrd-publish'],isset($_POST['wrd-name-pl']) ? $_POST['wrd-name-pl'] :''))
			header('Location: index.php?state-add=ko&page='.$_GET['page'].'&c='.$_GET['c']);
		else
			header('Location: index.php?state-add=ok&page='.$_GET['page'].'&c='.$_GET['c']);
		exit;
	}
	
	if(isset($_GET['state-add'])){
		if($_GET['state-add'] == 'ko')
			$error = _('L\'enregistrement de la définition a échoué.');
		elseif($_GET['state-add'] == 'ok')
			$success = _('Définition ajouté au glossaire.');
	}
	
	if(!$ajax){
		$filter = null;
		$caption_etat = '';
		if(isset($_GET['filter']))
		{
			switch($_GET['filter'])
			{
				case 'waiting' :
					$filter = -1;
					$caption_etat = _('en attentes');
					break;
				case 'unchecked' :
					$filter = 0;
					$caption_etat = _('non validés');
					break;
				case 'checked' :
					$filter = 1;
					$caption_etat = _('validés');
					break;
			}
		}
		
		if( isset($_GET['c']) )
			$rwords = gsr_words_get_array(0,null,$_GET['c']); 
		else
			$rwords = gsr_words_get_array(); 
		$words_count = is_array($rwords) ? sizeof($rwords) : 0;
		define('ADMIN_PAGE_TITLE', _('Glossaire').' - '._('Outils'));
		require_once('admin/skin/header.inc.php');
?>
	<h2>Glossaire</h2>
	<?php 
		if(isset($error))
			print '<div class="error">'.$error.'</div>';
		elseif(isset($success))
			print '<div class="error-success">'.$success.'</div>';
			
	if( gu_user_is_authorized('_RGH_ADMIN_TOOL_GLOSSARY_ADD') ){ ?>
	<form id="form-add" action="index.php?action=add" method="post">
		<table id="table-glossary-def">
			<caption><?php print _('Ajouter une définition'); ?></caption>
			<tbody>
				<tr>
					<td class="tdw-150"><label for="wrd-name"><?php print _('Nom :'); ?></label></td>
					<td><input type="text" name="wrd-name" id="wrd-name" value="" maxlength="255" /></td>
				</tr>
				<tr>
					<td><label for="wrd-name-pl"><?php print _('Nom :'); ?> (<?php print _('au pluriel'); ?>)</label></td>
					<td><input type="text" name="wrd-name-pl" id="wrd-name-pl" value="" maxlength="255" /></td>
				</tr>
				<tr>
					<td><label for="wrd-desc"><?php print _('Définition :'); ?></label></td>
					<td><textarea class="wdr-desc" name="wrd-desc" id="wrd-desc" cols="25" rows="5"></textarea></td>
				</tr>
			</tbody>
			<tfoot>
				<tr><td colspan="2">
					<input type="submit" name="save-word" value="<?php print _('Enregistrer'); ?>" />
				</td></tr>
			</tfoot>
		</table>		
	</form>
	<?php } 
	
		$rw = gsr_words_get(); 
		$w_count = ria_mysql_num_rows($rw);
		$char_links = array();
		if($w_count > 0) {
		
			$letters['0-9'] = 0;
			for( $c=65; $c<=90; $c++ ){
				$letters[chr($c)] = 0;
			}

			while( $w = ria_mysql_fetch_array($rw) ){
				$letter = substr( str_remove_accents($w['name']), 0, 1 );
				
				if( is_numeric($letter) )
					$letters['0-9']++;
				else
					$letters[ strtoupper( $letter ) ]++;
			}

			if( $letters['0-9']>0 )
				$char_links[] = '<a class="letter" href="'.preg_replace('/\?.*/','',$_SERVER['REQUEST_URI']).'?c=09">0-9</a>';
			else
				$char_links[] = '<span class="noprd">0-9</span>';

			for( $c=65; $c<=90; $c++ ){
				$char = chr($c);
				if( $char==$_GET['c'] ){
					$char_links[] = '<span class="letterbold">'.$char.'</span>';
				}else{
					if( $letters[$char]> 0 ){
						$char_links[] = '<a class="letter" href="'.preg_replace('/\?.*/','',$_SERVER['REQUEST_URI']).'?c='.$char.'">'.$char.'</a>';
					}else{
						$char_links[] = '<span class="noprd">'.$char.'</span>';
					}	
				}
			}
		}
	?>
	<form action="index.php<?php echo '?page='.$_GET['page'].'&amp;comments-type='.$_GET['comments-type'].'&amp;filter='.$_GET['filter'].'&amp;c='.$_GET['c'] ?>" method="post">
		<table id="table-glossaire" class="checklist reviewslist">
			<caption>
				<?php print _('Glossaire'); ?> 
				- <span class="alphabet-list"><a href="index.php<?php echo '?page='.$_GET['page'].'&amp;comments-type='.$_GET['comments-type'].'&amp;filter='.$_GET['filter']; ?>"><?php print _('Tous'); ?></a>
				<?php print implode( ' ', $char_links ); ?>
			</caption>
			<thead>
				<tr>
					<th><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
					<th id="name"><?php print _('Nom'); ?></th>
					<th id="name_pl"><?php print _('Nom'); ?> (<?php print _('au pluriel'); ?>)</th>
					<th id="desc"><?php print _('Description'); ?></th>
					<th id="etat"><?php print _('Publié'); ?></th>
					<th id="date"><?php print _('Date d\'ajout'); ?></th>
				</tr>
			</thead>
			<?php
				// Calcule le nombre de pages
				$pages = ceil($words_count / 25);

				// Détermine la page en cours de consultation
				$page = 1;
				if( isset($_GET['page']) && is_numeric($_GET['page']) ){
					if( $_GET['page']>0 && $_GET['page']<=$pages )
						$page = $_GET['page'];
				}

				// Détermine les limites inférieures et supérieures pour l'affichage des pages
				$pmin = $page-5;
				if( $pmin<1 )
					$pmin = 1;
				$pmax = $pmin+9;
				if( $pmax>$pages )
					$pmax = $pages;
			?>
			<tbody>
				<?php
					if( $words_count>0 ) {
						$i = ($page-1)*25;
						$count = 0;
						while( $count<=25 )
						{
							if( !isset($rwords[$i]) ){
								break;
							}
							
							$w = $rwords[ $i ];
							print '	<tr>'."\n".'
										<td><input type="checkbox" class="checkbox" name="word[]" value="'.$w['id'].'" /></td>'."\n";
							if( gu_user_is_authorized('_RGH_ADMIN_TOOL_GLOSSARY_EDIT') ){
								print '	<td headers="name" data-label="'._('Nom :').' "><a href="edit.php?wrd='.$w['id'].'&amp;c='.$_GET['c'].'">'.htmlspecialchars($w['name']).'</a></td>'."\n";
							}else{
								print '	<td headers="name" data-label="'._('Nom :').' ">'.htmlspecialchars($w['name']).'</td>'."\n";								
							}
								print '		<td headers="name_pl" data-label="'._('Nom (au pluriel) :').' ">'.htmlspecialchars($w['name_pl']).'</td>'."\n".'
											<td headers="desc" data-label="'._('Description :').' ">'.htmlspecialchars($w['desc']).'</td>'."\n".'
											<td headers="etat" data-label="'._('Publié :').' ">'.( $w['publish'] ? 'Oui' : 'Non' ).'</td>'."\n".'
											<td headers="date" data-label="'._('Date d\'ajout :').' ">'.ria_date_format($w['date']).'</td>'."\n".'
										</tr>'."\n";
							
							$i++;
							$count++;
						}
					}
					else {
						print '<tr><td colspan="6">'._('Aucune définition à ce jour').'</td></tr>';
					}
					
				?>
			</tbody>
			<?php if( $pages>1 || $words_count ){ ?>
			<tfoot>
				<?php if( $pages>1 ){ ?>
				<tr>
					<td colspan="2" class="page">
						<?php print _('Page').' '.$page.'/'.$pages; ?>
					</td>
					<td colspan="4" class="pages">
						<?php
							if( $page>1 ){
								print '<a href="index.php?page='.($page-1).( $filter !== null ? '&amp;comments-type='.$_GET['comments-type'].'&amp;filter='.$_GET['filter'] : '' ).'&amp;c='.$_GET['c'].'">&laquo; '._('Page précédente').'</a> | ';
							}								
							for( $i=$pmin; $i<=$pmax; $i++ ){
								if( $i==$page ){
									print '<b>'.$page.'</b>';
								}else{
									print '<a href="index.php?page='.$i.( $filter !== null ? '&amp;comments-type='.$_GET['comments-type'].'&amp;filter='.$_GET['filter'] : '' ).'&amp;c='.$_GET['c'].'">'.$i.'</a>';
								}	
								if( $i<$pmax ){
									print ' | ';
								}
							}
							if( $page<$pages ){
								print ' | <a href="index.php?page='.($page+1).( $filter !== null ? '&amp;comments-type='.$_GET['comments-type'].'&amp;filter='.$_GET['filter'] : '' ).'&amp;c='.$_GET['c'].'">'._('Page suivante').' &raquo;</a>';
							} 
						?>
					</td>
				</tr>
				<?php } ?>
				<tr><td colspan="4">
					<?php if( $words_count ){
						if( gu_user_is_authorized('_RGH_ADMIN_TOOL_GLOSSARY_PUBLISH') ){ ?>
						<input type="submit" name="publish-word" value="<?php print _('Publier'); ?>" class="float-left" />
						<input type="submit" name="unapprove-word" value="<?php print _('Retirer la publication'); ?>" class="float-left" />
						<?php }
						if( gu_user_is_authorized('_RGH_ADMIN_TOOL_GLOSSARY_DEL') ){ ?>						
						<input type="submit" name="delete-word" value="<?php print _('Supprimer'); ?>" class="float-left" />
						<?php } ?>
					<!--<input type="submit" name="cancel-reviews" value="<?php //print _('Annuler'); ?>" />-->
					<?php } ?>
				</td><td colspan="2">
				</td></tr>
			</tfoot>
			<?php } ?>
		</table>
	</form>
<?php 
	require_once('admin/skin/footer.inc.php');
}else{
	if($ajax)
	{
		header("Content-Type: application/xml");
		$xml = '';
		$xml .= '<?xml version="1.0" encoding="utf-8"?>';
		$xml .= '<result>';
		$xml .= $xml_status;
		$xml .= '<reviews>';
		$xml .= $xml_body;
		$xml .= '</reviews>';
		$xml .= '</result>';
		print $xml;
	}
} ?>