/**
 * Classes d'aide g<PERSON><PERSON>le
 */

.none {
	display: none !important;
}
.align-center {
	text-align: center !important;
}
.align-right {
	text-align: right !important;
}
.align-left {
	text-align: left !important;
}
.valign-center {
	vertical-align: middle !important;
}
.width-auto {
	width: auto !important;
}
.float-right {
  float: right;
}
.float-left {
  float: left;
}
.float-none {
  float: none !important;
}
.no-border {
  border: 0 !important;
}
.color-red {
  color: $red !important;
}
.large{
  width: 100%;
}
.clear-right {
  clear: right;
}
.clear-left {
  clear: left;
}
.clear-both {
  clear: both;
}
.display-flex {
  display: flex;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.uppercase {
  text-transform: uppercase;
}
.bg-white {
  background-color: #FFF !important;
}
.border-none {
	border: medium none;
}
.margin-top-10 {
    margin-top: 10px;
}
.margin-bottom-10 {
  margin-bottom: 10px;
}
.padding-bottom-10 {
  padding-bottom: 10px !important;
}
.spacing-top{
  margin-top: 15px;
}
.div-padding-15 {
  padding: 15px;
}
.bold, .bold tr td, .bold td {
	font-weight: 600;
}
.hide {
  display: none;
}
.nonSelectionnable {
  user-select: none;
}
.info-fld {
  color:#707070;
  font-size: 10px;
  padding-left: 5px;
}
td.number{
  text-align: right !important;
  padding-right: 5px !important;
}

.hide-mobile {
  display: block;
  @include media('<large') {
    display: none !important;
  }
}