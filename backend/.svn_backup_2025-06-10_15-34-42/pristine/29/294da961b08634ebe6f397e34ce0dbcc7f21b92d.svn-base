<?php

	/**	\file ajax-rewards.php
	 *
	 *	Ce fichier est appelé par la page de configuration des points de fidélité pour charger les données
	 *
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_REWARD_CONFIG');

	require_once('rewards.inc.php');

	header('Content-type: text/json');
	header('Content-type: application/json');

	$result = array( 'error' => '' );

	// Chargement des informations sur une configuration site-profil
	if( isset($_GET['loadinfo'], $_GET['prf'], $_GET['wst']) ){

		$_SESSION['admin_rewards_prf'] = $_GET['prf'];
		$_SESSION['admin_rewards_wst'] = $_GET['wst'];
		$_SESSION['admin_rewards_tab'] = 'general';

		$reward = array( 'id'=>0, 'ratio'=>array('amount'=>0, 'pts'=>0), 'convert'=>array('amount'=>0, 'pts'=>0), 'min-amount'=>0, 'days'=>0, 'exclu-promo'=>0, 'cumul-pts'=>1, 'all-catalog'=>1 , 'days_lost'=>0, 'type-calc'=>_RWD_SYSTEM_POINTS );

		$rrwd = rwd_rewards_get( 0, $_GET['prf'], $_GET['wst'] );
		if( $rrwd && ria_mysql_num_rows($rrwd) ){
			$reward = ria_mysql_fetch_array( $rrwd );

			$reward['ratio'] = rwd_rewards_ratio_formated( $reward['ratio'] );
			$reward['convert'] = rwd_rewards_ratio_formated( $reward['convert'], true );
		}

		$reward['min-amount'] = number_format( $reward['min-amount'], 2, ',', ' ' );
		$reward['ratio']['amount'] = number_format( $reward['ratio']['amount'], 4, ',', ' ' );
		$reward['ratio']['pts'] = number_format( $reward['ratio']['pts'], 0, '', ' ' );
		$reward['convert']['amount'] = isset($reward['convert']['amount']) ? number_format( $reward['convert']['amount'], 4, ',', ' ' ) : 0;
		$reward['convert']['pts'] = isset($reward['convert']['pts']) ? number_format( $reward['convert']['pts'], 0, '', ' ' ) : 0;

		$reward['typecalc'] = $reward['type-calc'];
		$reward['minamount'] = $reward['min-amount'];
		$reward['exclupromo'] = $reward['exclu-promo'];
		$reward['cumulpts'] = $reward['cumul-pts'];
		$reward['allcatalog'] = $reward['all-catalog'];
		$reward['dayslost'] = $reward['days_lost'];

		$result = $reward;
	}
	// Récupère l'information pour savoir si tout le catalogue est inclu ou exclu
	elseif( isset($_GET['getallcatalog'], $_GET['rwd'], $_GET['prf'], $_GET['wst']) ){
		$_SESSION['admin_rewards_tab'] = 'products';

		$all_catalog = rwd_rewards_get_all_catalog( $_GET['rwd'], $_GET['prf'], $_GET['wst'] );
		$result = array( 'all' => $all_catalog );

	}
	// Charge les différentes règles en place sur l'inclusion / l'exclusion de produit, catégorie ou marque
	elseif( isset($_GET['getincludeprd'], $_GET['rwd']) ){
		$_SESSION['admin_rewards_tab'] = 'products';

		$ar_rules = array();

		// charge les règles pour les produits
		$rprd = rwd_products_get( $_GET['rwd'] );
		if( $rprd && ria_mysql_num_rows($rprd) ){

			while( $prd = ria_mysql_fetch_array($rprd) ){
				$ar_rules['prd'][] = array( 'id'=>$prd['prd'], 'include'=>($prd['include'] ? '+' : '-'), 'name'=>$prd['title'] );
			}

		}

		// charge les règles pour les catégories
		$rcat = rwd_categories_get( $_GET['rwd'] );
		if( $rcat && ria_mysql_num_rows($rcat) ){

			while( $cat = ria_mysql_fetch_array($rcat) ){
				$ar_rules['cat'][] = array( 'id'=>$cat['cat'], 'include'=>($cat['include'] ? '+' : '-'), 'name'=>$cat['title'], 'prds'=>$cat['products'] );
			}

		}

		// charge les règles pour les marques
		$rbrd = rwd_brands_get( $_GET['rwd'] );
		if( $rbrd && ria_mysql_num_rows($rbrd) ){

			while( $brd = ria_mysql_fetch_array($rbrd) ){
				$ar_rules['brd'][] = array( 'id'=>$brd['brd'], 'include'=>($brd['include'] ? '+' : '-'), 'name'=>$brd['title'], 'prds'=>$brd['products'] );
			}

		}

		$result = $ar_rules;

	}
	// Ajout d'une règle d'inclusion / d'exclusion sur un produit, une catégorie ou une marque
	elseif( isset($_GET['addrule'], $_GET['rwd'], $_GET['type'], $_GET['cnt'], $_GET['include']) ){
		$_SESSION['admin_rewards_tab'] = 'products';

		switch( $_GET['type'] ){
			case 'prd' :
				$res = rwd_products_add( $_GET['rwd'], $_GET['cnt'], $_GET['include'] );
				break;
			case 'cat' :
				$res = rwd_categories_add( $_GET['rwd'], $_GET['cnt'], $_GET['include'] );
				break;
			case 'brd' :
				$res = rwd_brands_add( $_GET['rwd'], $_GET['cnt'], $_GET['include'] );
				break;
		}

		$result = array( 'done' => $res );

	}
	// Supprime une règle d'inclusion / d'exclusion sur un produit, une catégorie ou une marque
	elseif( isset($_GET['delrule'], $_GET['rwd'], $_GET['type'], $_GET['cnt']) ){
		$_SESSION['admin_rewards_tab'] = 'products';

		switch( $_GET['type'] ){
			case 'prd' :
				$res = rwd_products_del( $_GET['rwd'], $_GET['cnt'] );
				break;
			case 'cat' :
				$res = rwd_categories_del( $_GET['rwd'], $_GET['cnt'] );
				break;
			case 'brd' :
				$res = rwd_brands_del( $_GET['rwd'], $_GET['cnt'] );
				break;
		}

		$result = array( 'done' => $res );

	}
	// Mise à jour de l'information pour savoir si tout le catalogue est inclu ou exclu
	elseif( isset($_GET['saveallcatalog'], $_GET['rwd'], $_GET['all'], $_GET['in_promo']) ){
		$_SESSION['admin_rewards_tab'] = 'products';

		$res = rwd_rewards_set_all_catalog( $_GET['rwd'], $_GET['all'] );
		$res = $res && rwd_rewards_set_exclu_promo( $_GET['rwd'], $_GET['in_promo'] ? false : true );
		$result = array( 'done' => $res );

	}
	// Charge les configurations des actions pour un site-profil
	elseif( isset($_GET['getinforwa'], $_GET['prf'], $_GET['wst']) ){
		$_SESSION['admin_rewards_tab'] = 'actions';
		$res = array();

		$rrwa = rwd_actions_get();
		if( $rrwa ){
			while( $rwa = ria_mysql_fetch_array($rrwa) ){
				$res[ $rwa['id'] ] = array( 'pts'=>0, 'sp_pts'=>0 );
			}
		}

		$rcfg = rwd_actions_configs_get( $_GET['prf'], $_GET['wst'] );
		if( $rcfg ){
			while( $cfg = ria_mysql_fetch_array($rcfg) ){
				$res[ $cfg['rwa'] ] = array('pts'=>$cfg['pts'], 'sp_pts'=>$cfg['sp_pts']);
			}
		}

		$result = $res;
	}
	// Enregistre les configurations des actions pour un site-profil
	elseif( isset($_GET['saverwa'], $_GET['prf'], $_GET['wst'], $_GET['rwa-usr'], $_GET['rwa-sp']) ){
		$_SESSION['admin_rewards_tab'] = 'actions';

		$rrwa = rwd_actions_get();
		if( $rrwa ){

			while( $rwa = ria_mysql_fetch_array($rrwa) ){
				$pts = isset($_GET['rwa-usr'][$rwa['id']]) && is_numeric($_GET['rwa-usr'][$rwa['id']]) && $_GET['rwa-usr'][$rwa['id']]>0 ? $_GET['rwa-usr'][$rwa['id']] : 0;
				$sp_pts = isset($_GET['rwa-sp'][$rwa['id']]) && is_numeric($_GET['rwa-sp'][$rwa['id']]) && $_GET['rwa-sp'][$rwa['id']]>0 ? $_GET['rwa-sp'][$rwa['id']] : 0;

				if( !rwd_actions_configs_add( $rwa['id'], $_GET['prf'], $_GET['wst'], $pts, $sp_pts) ) {
					$result = array( 'done'=>false );
				} else {
					$result = array( 'done'=>true );
				}
			}

		}

	}
	// Enregistre les informations pour le système de parrainage
	elseif( isset($_GET['savesp'], $_GET['rwd'], $_GET['active-sponsor']) ){
		$_SESSION['admin_rewards_tab'] = 'sponsors';

		$active 		= $_GET['active-sponsor'] ? true : false;
		$filleul_system = !$active ? 0 : $_GET['filleul-system'];
		$discount 		= !$active ? 0 : str_replace( ',', '.', $_GET['discount']);
		$points 		= !$active ? 0 : trim($_GET['points']);
		$days 			= !$active ? 0 : $_GET['remise-day-valid'];
		$pts 			= !$active ? 0 : (is_numeric($_GET['sponsor-points']) && $_GET['sponsor-points'] > 0 ? $_GET['sponsor-points'] : 0 );
		$limit 			= !$active ? 0 : (is_numeric($_GET['sponsor-limit']) && $_GET['sponsor-limit']>0 ? $_GET['sponsor-limit'] : 0);
		$before_lost 	= !$active ? 0 : (is_numeric($_GET['cod-before-lost']) && $_GET['cod-before-lost']>0 ? $_GET['cod-before-lost'] : 0);
		$first_order	= !$active ? 0 : (isset($_GET['first_order']) ? true : false );
		$sp_system		= !$active ? _RWD_SP_SYS_POINTS : $_GET['sp-system'];
		$sp_pourcent	= !$active ? 0 : (is_numeric($_GET['sp-ord-pourcent']) && $_GET['sp-ord-pourcent'] > 0 ? $_GET['sp-ord-pourcent'] : 0);
		$sp_days		= !$active ? 0 : (is_numeric($_GET['sp-day-valid']) && $_GET['sp-day-valid'] > 0 ? $_GET['sp-day-valid'] : 0);

		if($filleul_system == "1" ){
			$points = 0;
		}else if($filleul_system == "2"){
			$discount = 0;
		}else{
			$discount = 0;
			$points = 0;
			$days = 0;
		}

		if( !rwd_rewards_update_sponsors($_GET['rwd'], $active, $days, $discount, $pts, $limit, $before_lost, $first_order, $sp_system, $sp_pourcent, $sp_days, $points) ){
			$result = array( 'done'=>false );
		} else {
			$result = array( 'done'=>true );
		}
	}
	// Charge les informations sur le système de parraiange
	elseif( isset($_GET['getinfosp'], $_GET['rwd']) ){
		$_SESSION['admin_rewards_tab'] = 'sponsors';

		$sponsor = rwd_rewards_get_sponsor( $_GET['rwd'] );
		if( is_array($sponsor) && sizeof($sponsor) ){
			$result = $sponsor;
		} else {
			$result = array( 'done'=>false );
		}
	}
	// Charge les informations des conditions pour une action
	elseif( isset($_GET['getinforwc'], $_GET['rwa'], $_GET['prf'], $_GET['wst']) ){
		$_SESSION['admin_rewards_tab'] = 'actions';

		$res = array();
		$rcfg = rwd_action_conditions_configs_get( 0, 0, '', $_GET['rwa'], $_GET['prf'], $_GET['wst'] );
		if( $rcfg ){

			while( $cfg = ria_mysql_fetch_array($rcfg) ){
				$res[ $cfg['rwc'] ] = array('type'=>$cfg['type'], 'val'=>$cfg['val']);
			}

		}

		$result = $res;
	}
	// enregistre les information des conditions pour une action
	elseif( isset($_GET['saverwc'], $_GET['prf'], $_GET['wst'], $_GET['config']) ){
		$_SESSION['admin_rewards_tab'] = 'actions';

		$res = true;
		foreach( $_GET['config'] as $key=>$val ){
			if( trim($val) || $val==0 ){
				if( !rwd_action_conditions_configs_add($key, $_GET['prf'], $val, $_GET['wst']) )
					$res = false;
			} elseif( ($id = rwd_action_conditions_configs_exists( 0, $_GET['prf'], $_GET['wst'], $key) ) ){
				if( !rwd_action_conditions_configs_del($id) )
					$res = false;
			}
		}

		$result = array( 'done' => $res );
	}
	// charge les palier pour l'action "Passage de la Nème commande"
	elseif( isset($_GET['getlanding'], $_GET['prf'], $_GET['wst']) ){
		$_SESSION['admin_rewards_tab'] = 'actions';

		$res = array();

		$cfg = isset($_GET['cfg']) ? $_GET['cfg'] : 0;
		$rrwc = rwd_action_conditions_configs_get( $cfg, 10, '', 0, $_GET['prf'], $_GET['wst'], array('value'=>'asc') );
		if( $rrwc && ria_mysql_num_rows($rrwc) ){

			while( $rwc = ria_mysql_fetch_array($rrwc) ){
				$res[] = array(
					'id' => $rwc['id'],
					'val' => $rwc['val'],
					'pts' => $rwc['pts'],
					'sp_pts' => $rwc['sp_pts']
				);
			}

		}

		$result = $res;

	}
	// enregistre un nouveau palier pour l'action "Passage de la Nème commande"
	elseif( isset($_GET['addnewlanding'], $_GET['rwa'], $_GET['prf'], $_GET['wst'], $_GET['landing'], $_GET['pts'], $_GET['sp_pts'])  ) {
		$_SESSION['admin_rewards_tab'] = 'actions';
		$res = rwd_action_conditions_configs_add( 10, $_GET['prf'], $_GET['landing'], $_GET['wst'], $_GET['pts'], $_GET['sp_pts'] );
		$result = array( 'done' => $res );
	}
	// suppression d'un palier
	elseif( isset($_GET['dellanding'], $_GET['cfg']) ){
		$_SESSION['admin_rewards_tab'] = 'actions';
		$res = rwd_action_conditions_configs_del( $_GET['cfg'] );
		$result = array( 'done' => $res );
	}
	// mise à jour d'un palier
	elseif( isset($_GET['updatelanding'], $_GET['val'], $_GET['pts'], $_GET['sp_pts'], $_GET['cfg']) ){
		$_SESSION['admin_rewards_tab'] = 'actions';
		$res = rwd_action_conditions_configs_update( $_GET['cfg'], $_GET['val'], $_GET['pts'], $_GET['sp_pts'] );
		$result = array( 'done' => $res );
	}
	// suppression d'une définition de points de fidélité pour un produit
	elseif(isset($_GET['delprdreward'],$_GET['id'])){
		$res = rwd_prd_rewards_del($_GET['id']);
		$result = array( 'done' => $res );
	}
	// suppression d'une définition de points de fidélité pour une catégorie
	elseif(isset($_GET['delcatreward'],$_GET['id'])){
		$res = rwd_cat_rewards_del($_GET['id']);
		$result = array( 'done' => $res );
	}
	// ajout d'un produit offert dans l'offre
	elseif(isset($_GET['addproduct'],$_GET['cnt'],$_GET['points'],$_GET['prf'])){
		if(isset($_GET['ish']) && $_GET['ish'] == 1){
			$res = rwd_catalogs_add($_GET['cnt'],$_GET['points'],$_GET['prf'],$_GET['ish'],$_GET['ptsh']);
		}else{
			$res = rwd_catalogs_add($_GET['cnt'],$_GET['points'],$_GET['prf']);
		}
		$result = array( 'done' => $res );
	}
	// suppression d'un produit de l'offre fidélité
	elseif(isset($_GET['delrewardproduct'],$_GET['prd'],$_GET['prf'])){
		$res = rwd_catalogs_del($_GET['prd'],$_GET['prf']);
		$result = array( 'done' => $res );
	}
	// charge les produits présent dans le catalog des produits vendu avec des points de fidélités
	elseif( isset($_GET['getrewardproducts'],$_GET['prf']) ){

		$ar_prd = array();

		// charge les règles pour les produits
		$res = rwd_catalogs_get($_GET['prf']);
		if( $res && ria_mysql_num_rows($res) ){
			while( $prd = ria_mysql_fetch_array($res) ){
				$prd['is_sync'] = prd_products_get_is_sync($prd['rwc_prd_id']);

				$is_highlighting = (isset($prd['rwc_is_highlighting']) && $prd['rwc_is_highlighting'] != 0 ? "Oui" : "Non" );
				if($is_highlighting == "Non" ){
					$pts_highlighting = "";
				}else{
					$pts_highlighting = (isset($prd['rwc_pts_highlighting']) && $prd['rwc_pts_highlighting'] && (isset($prd['rwc_is_highlighting']) && $prd['rwc_is_highlighting'] != 0) ? $prd['rwc_pts_highlighting'] : "non défini" );
				}
				$ar_prd[] = array( 'id'=>$prd['rwc_prd_id'], 'is_sync'=>$prd['is_sync'], 'ref'=>$prd['prd_ref'], 'name'=>$prd['prd_name'], 'points'=>$prd['rwc_sell_points'],'is_highlighting'=>$is_highlighting,'pts_highlighting'=> $pts_highlighting );
			}
		}
		$result = $ar_prd;
	}

	print json_encode( $result );
	exit;