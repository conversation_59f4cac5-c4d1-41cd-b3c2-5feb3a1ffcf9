# TrackedOrderProduct

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**product_id** | **string** | The product identifier | 
**product_sku** | **string** | The product SKU | 
**product_title** | **string** | The product tile | 
**product_image_url** | **string** | The product image Url | [optional] 
**product_exists** | **bool** | Indicates if the product still exists in your catalog | 
**unit_price** | **float** | The product&#39;s unit price for the order | 
**quantity** | **float** | The quantity of this product for the order | 
**margin** | **float** | The product&#39;s margin for the order | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


