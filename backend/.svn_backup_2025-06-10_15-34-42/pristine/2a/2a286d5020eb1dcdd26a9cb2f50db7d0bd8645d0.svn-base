<?php

set_include_path(dirname(__FILE__) . '/../include/');
require_once('db.inc.php');
require_once('define.inc.php');
require_once('sys.zones.inc.php');

$r_region = sys_zones_get(0, '', '', false, 0, '', _ZONE_RGN_FRANCE, array(), -1, -1, true, true);

$ar_zones = array();

while ($region = ria_mysql_fetch_assoc($r_region)) {
	$r_dept = sys_zones_get(0, '', '', false, $region['id'], '', _ZONE_DPT_FRANCE, array(), -1, -1, true, true);
	
	while ($dept = ria_mysql_fetch_assoc($r_dept)) {
		$r_zipcode = sys_zones_get(0, '', '', false, $dept['id'], '', _ZONE_ZIPCODES, array(), -1, -1, true, true);
		
		while ($zipcode = ria_mysql_fetch_assoc($r_zipcode)) {
			$r_city = sys_zones_get(0, '', '', false, $zipcode['id'], '', _ZONE_INSEE, array(), -1, -1, true, true);

			while ($city = ria_mysql_fetch_assoc($r_city)) {
				$ar_zones[] = array(
					$region['name'],
					$dept['name'],
					$zipcode['name'],
					$city['name']
				);
			}
		}
	}
}

$outputBuffer = fopen("php://output", 'w');
foreach ($ar_zones as $zone) {
	fputcsv($outputBuffer, $zone, ';', '"');
}
fclose($outputBuffer);
