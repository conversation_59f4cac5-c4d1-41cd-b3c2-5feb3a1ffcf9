<?php
/**
 * \defgroup delivery_periodes Périodes d'expédition 
 * \ingroup delivery
 * @{
 * \page api-delivery-expedition-period-get Chargement
 *
 * Cette fonction permet de récupérer toutes les périodes d'horaires d'expédition
 *	\code
 *		GET /delivery/expedition_period/
 *	\endcode 
 *
 * @return json un tableau dont chaque période contient les propriétés suivantes :
 * \code{.json}
 *	{
 *		"day": identifiant du jour de la semaine
 *		"start": heure de début
 *		"end": heure de fin
 *		"int-start": heure de début sous la forme d'entier (1, 2, 13, 14 ...)
 *		"int-end": heure de fin sous la forme d'entier (1, 2, 13, 14 ...)
 *	}	
 * \endcode
 * @}
*/
switch( $method ){
	case 'get':
		$result = true;

		$rexp = dlv_expedition_periods_get($config['wst_id']);
		if( $rexp && ria_mysql_num_rows($rexp) ){
			while( $exp = ria_mysql_fetch_assoc($rexp) ){
				$content[] = $exp;
			}
		}
		break;
}