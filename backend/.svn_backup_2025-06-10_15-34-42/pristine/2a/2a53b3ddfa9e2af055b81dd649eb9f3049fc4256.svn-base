<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/pubsub/v1/schema.proto

namespace Google\Cloud\PubSub\V1\Schema;

use UnexpectedValueException;

/**
 * Possible schema definition types.
 *
 * Protobuf type <code>google.pubsub.v1.Schema.Type</code>
 */
class Type
{
    /**
     * Default value. This value is unused.
     *
     * Generated from protobuf enum <code>TYPE_UNSPECIFIED = 0;</code>
     */
    const TYPE_UNSPECIFIED = 0;
    /**
     * A Protocol Buffer schema definition.
     *
     * Generated from protobuf enum <code>PROTOCOL_BUFFER = 1;</code>
     */
    const PROTOCOL_BUFFER = 1;
    /**
     * An Avro schema definition.
     *
     * Generated from protobuf enum <code>AVRO = 2;</code>
     */
    const AVRO = 2;

    private static $valueToName = [
        self::TYPE_UNSPECIFIED => 'TYPE_UNSPECIFIED',
        self::PROTOCOL_BUFFER => 'PROTOCOL_BUFFER',
        self::AVRO => 'AVRO',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Type::class, \Google\Cloud\PubSub\V1\Schema_Type::class);

