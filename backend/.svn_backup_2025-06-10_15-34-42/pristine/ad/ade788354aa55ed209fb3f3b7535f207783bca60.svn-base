<?php

	/**	\file ajax-prd-no-image.php
	 * 	Ce fichier est appelé en ajax par la page prd-no-image, qui affiche la liste des produits sans image (principale ou secondaire).
	 * 	Cette page est chargée de fournir les données, alors que le fichieri prd-no-image.php fourni le cadre d'affichage et la navigation.
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_PRD_NO_IMG');

	$response = array();

	try {
		ob_start();

		require_once('products.inc.php');

		$tab = isset($_GET['export']) ? $_GET['tab'] : $_POST['tab'];

		$orderable = $published = null;

		$filter = isset($_GET['export'] ) ? $_GET['filter'] : $_POST['filter'];

		switch ($filter) {
			case 'published':
				$published = true;
				break;
			case 'unpublished':
				$published = false;
				break;
			case 'orderable':
				$orderable = true;
				break;
		}

		$products = prd_products_get_not_images( ($tab == 'tabMain' ? true : false), $published, $orderable );

		$prd_count = $products ? ria_mysql_num_rows( $products ) : 0;

		// Partie réservé à la demande d'exportation
		if( isset($_GET['export']) ){
			require_once('export-prd-no-image.php');
			exit;
		}

		$page = isset($_POST['page']) && is_numeric($_POST['page']) && $_POST['page'] > 0 ? $_POST['page'] : 1;
		$pages = $prd_count > 1 ? ceil( $prd_count / 100 ) : 1;

		if( $page > $pages ){
			$page = $pages;
		}

		// On affiche les produits
		if ($prd_count) {
			ria_mysql_data_seek( $products, ($page - 1) * 100 );

			$i = 1;
			while( $prd = ria_mysql_fetch_assoc($products) ){
				if( $i > 100 ){
					break;
				}

				print '<tr>';
				if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ){
					print '
						<td headers="prd-ref">'.view_prd_is_sync($prd).' <a href="/admin/catalog/product.php?cat=0&prd='.$prd['id'].'" target="_bank">'.htmlspecialchars( $prd['ref'] ).'</a></td>
						<td headers="prd-name"><a href="/admin/catalog/product.php?cat=0&prd='.$prd['id'].'" target="_bank">'.htmlspecialchars( $prd['name'] ).'</a></td>';
				}else{
					print '	
						<td headers="prd-ref">'.view_prd_is_sync($prd).' '.htmlspecialchars( $prd['ref'] ).'</td>
						<td headers="prd-name">'.htmlspecialchars( $prd['name'] ).'</td>';
				}
					print '
						<td class="align-right" headers="prd-hits" data-label="'._('Impressions :').' ">'.ria_number_format($prd['hits']).'</td>
						<td class="align-right" headers="prd-sells" data-label="'._('Ventes :').' ">'.ria_number_format($prd['selled_web']).'</td>
						<td class="align-right" headers="prd-taux" data-label="'._('Taux de conversion :').' ">'.ria_number_format($prd['taux'], NumberFormatter::PERCENT, 2).'</td>
						<td headers="prd-link" class="right">';
				if( gu_user_is_authorized('_RGH_ADMIN_DOCS_IMAGE_ASSOC_PRD') ){
				print '<a class="popup button" href="/admin/catalog/product.php?cat=0&prd=' . $prd['id'] . '&lng=fr&tab=images" target="_blank">'._('Ajouter des images').'</a>';
				}
				print '</td>
					</tr>';

				$i++;
			}
		}else{
			print '<tr><td colspan="6">'._(sprintf('Aucun produit sans image %s', ($tab === 'tabMain' ? 'principale' : 'secondaire'))).'</td></tr>';
		} 

		$content = ob_get_contents();
		ob_clean();

		if( $prd_count>0 ){
			$links = array();
			if( $page>1 )
				$links[] = '<a href="#" data-page="'.($page-1).'">&laquo; '._('Page précédente').'</a>';
			for( $i=$page-5; $i<$page+5; $i++ )
				if( $i>=1 && $i<=$pages ){
					if( $i==$page )
						$links[] = '<b>'.$i.'</b>';
					else
						$links[] = '<a href="#" data-page="'.$i.'">'.$i.'</a>';
				}
			if( $page<$pages )
				$links[] = '<a href="#" data-page="'.($page+1).'">'._('Page suivante').' &raquo;</a>';
		}

		ob_start();

		print '
			<td class="align-left">'._('Page').' '.$page.' / '.$pages.'</td>
			<td colspan="5">';
			print implode( ' | ',$links );
		print '</td>';

		$pager = ob_get_contents();
		ob_clean();

		$response['nb_prds']	= $prd_count;
		$response['count'] 		= ' (' . ($prd_count ? number_format($prd_count, 0, ',', ' ') : _('Aucun')) . ' '._('produit' . ($prd_count > 1 ? 's' : '')) . ')';
		$response['content'] 	= $content;
		$response['pager'] 		= $pager;
	}
	catch (Exception $e) {
		$response['error'] = $e->getMessage();
	}

	print json_encode($response);
	exit;

