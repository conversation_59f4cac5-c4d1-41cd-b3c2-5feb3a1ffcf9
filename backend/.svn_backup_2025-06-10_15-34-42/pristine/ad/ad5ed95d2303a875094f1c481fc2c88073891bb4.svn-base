<?php

	/**	\file export-categories.php
	 *
	 *	Ce fichier exporte une liste de catégories de produits.
	 *
	 */

	// Vérifie que l'utilisateur à bien accès à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_CATEG_VIEW');

	require_once('categories.inc.php');
	
	// Lignes du document CSV
	$rows = array();
	
	$cols = isset($_GET['cols']) && trim($_GET['cols']) != '' ? explode('|', $_GET['cols']) : false; 
	$heads = isset($_GET['heads']) && trim($_GET['heads']) != '' ? explode('|', $_GET['heads']) : false;
	$exclude_cat_ids = isset($_GET['exclude_cat_ids']) && trim($_GET['exclude_cat_ids']) != '' ? explode('|', $_GET['exclude_cat_ids']) : array();
	$recursive = isset($_GET['catchilds']) && $_GET['catchilds'] ? true : false;

	$for_excel = isset($_GET['for_excel']) && $_GET['for_excel'] ? true : false;
	$for_mac = isset($_GET['for_mac']) && $_GET['for_mac'] ? true : false;

	$r_fields = fld_fields_get(0,0,0,FLD_TYPE_IMAGE,0,0,null,array(),false,array(),null,CLS_CATEGORY);
	$ar_image_sizes = array();

	while($fld = ria_mysql_fetch_assoc($r_fields)){
		if (isset($_GET[$fld['id']])){
			$ar_image_sizes[$fld['id']] = $_GET[$fld['id']];
		}
	}

	$fields = array();
	for($i = 0; $i < sizeof($cols); $i++){
		$fields[$heads[$i]] = $cols[$i];
	}
	
	// ligne d'entête
	$header_row = array();
	foreach( $fields as $field=>$name ){
		$header_row[] = str_replace(array(';', "\r", "\n"), array(',', ' ', ' '), $name);
	}
	
	$rows[] = implode(';', $header_row);
	
	$cat = isset($_GET['cat']) ? $_GET['cat'] : 0;

	$file_name = 'export-categories-'.$_SESSION['usr_id'].'.csv';
	$file_csv = $config['doc_dir'].'/'.$file_name;

	$exp_id = exp_exports_add( CLS_CATEGORY, $file_csv, $file_name );

	try{
		// Ajoute l'import dans la file d'attente
		RiaQueue::getInstance()->addJob(RiaQueue::WORKER_EXPORT_GENERATE, array(
			'wst_id' => $config['wst_id'],
			'cls_id' => CLS_CATEGORY,
			'exp_id' => $exp_id,
			'heads' => $heads,
			'cat' => $cat,
			'recursive' => $recursive,
			'ar_image_sizes' => $ar_image_sizes,
			'exclude_cat_ids' => $exclude_cat_ids,
			'header_row' => $header_row,
			'for_excel' => $for_excel,
			'for_mac' => $for_mac
		));
	}catch(Exception $e){
		error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
	}

