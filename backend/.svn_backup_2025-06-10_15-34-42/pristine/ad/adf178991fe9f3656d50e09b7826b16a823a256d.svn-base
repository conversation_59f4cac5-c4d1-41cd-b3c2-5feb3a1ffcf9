#foot-products {
	width: 100%;
}
#editproduct {
	width: 100%;
	#icon-del-cat {
		width: 16px;
	}
	#title {
		width: 100% !important;
		max-width: 100%;
	}
}

/* Catégories */ 
#categories {
	width: 100%;
	max-width: 1100px; 
	@include media('>=large') {
		#cat-sel {
			width: 25px;
		}
		#cat-publish{
			width: 100px;
		}
		#cat-products {
			width: 135px;
		}
	}
}
/* Une catégorie */ 
.table-top + .items-list-filters {
	padding-right: 180px;
	@include media('<medium') {
		padding-right: 0;
	}
}
#form-catalog-produits {
	position: relative;
	height: auto;
	min-height: 300px;
	.with-cols-options {
		position: absolute;
		right: 0;
		top: -25px;
		@include media('<=large') {
			display: inline-block;
			top: auto;
			position: relative;
			margin-bottom: 5px;
		}		
	}
}

/* Une catégorie, tableau de produits */ 
.list-cols-changed {
	border: 0 none !important;
	border-bottom: 1px solid $grey-medium-color !important;
    margin-bottom: 0 !important;
	width: 100%;
	tr {
		position: relative;
	}
	#prd-selled, .number {
		padding-right: 5px !important;
		text-align: right;
	}
	.th-col-hide {
		display: none;
	}
}
*+html .list-cols-changed {
	tr, .prd-col-nomove {
		position: static !important;
	}
}
/* Catégories > un produit > "edition" > onglet "Général" */ 
#product {
    #desc {
        width: 100%;
    }
}
/* Une catégorie > "edition" > onglet "place de marché" */ 
#tbl-export {
	thead tr td {
		&:first-child {
			width: 180px;
		}
		&:last-child {
			width: 650px;
			max-width: 100%;
		}
	}
}


/* Catalogue > une catégorie > un produit > edition, onglet "Général" */ 
#table-nomenclature, #table-nomenclature-composition {
	width: 600px;
	#nomenclature_ref{
		width: 300px;
		@include media('<medium') {
			width: 100%;
		}
	}
}

/* Catalogue > une catégorie > un produit > edition, onglet "Articles liés" */ 
.list-prd-relations {
	margin-top: 10px !important;
	max-width: 1247px;
	@include media('<large') {
		display: flex;
		flex-direction: column;
	}

}
/* Catalogue > une catégorie > un produit > edition, onglet "Stocks" */ 
#table-reapprovisionnement {
	input.datepicker, input.schedule-qte {
		width: 100% !important;
	}
}
.tb-stock-all {
	width: 100%;
	max-width: 700px;
}

.tab-stock {
	input {
		width: 50px;
		text-align: right;
	}
	@include media('<large') {
		td, th {
			&.align-right {
				padding-right: 5px !important;
			}
		}
	}
}

.schedule-table {
	text-align: center;
	width: 100%;
	.schedule-date, 
	.schedule-qte {
		width: 100px;
	}
	@include media('<medium') {
		thead {
			display: none;
		}
		tbody td {
			text-align: left;
		}
	}
}

/* #products td.is-loading {
	background-image: url('/admin/images/ajax.gif');
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
} */

// Linear-raised
.search-customers-results {
	tbody {
		tr + tr {
			border-top: 1px solid $grey-medium-color;
		}
	}
}

.list-to-move {
	margin-bottom: 10px;
}

/* Une catégorie > "edition" > onglet "Comparateurs" */ 
.mapping {
	td {
		padding: 5px;
		&.link-ctr label {
			color: #000000;
			font-weight: 500;
			margin-top: 5px;
			display: inline-block;
		}
	}
	input[type=radio] {
		margin-top: 5px;
		width: 15px !important;
	}
	.info-compl {
		margin-top: 5px;
		.underline {
			text-decoration: underline;
		}
	}
	.diffctr {
		display: none;
	}
	.del, input.title, textarea.desc {
		display: none;
	}
	textarea.desc {
		width: 100%;
		height: 150px;
	}
	span.srg_desc {
		display: block;
		width: 95%;
		float: left;
	}
}

/* Une catégorie > "edition" > onglet "Tarification" */ 

#pamp, #taxes {
	label{
		width: 107px;
		display: inline-block;
	}
	input:not([type="checkbox"]):not([type="radio"]):not([type="image"]):not([type="button"]):not([type="submit"]):not([type="reset"]), select {
		text-align: right;
		width: 110px;
	}
	input[type=button] {
		margin-left: 5px;
	}
}
/* Une catégorie > "edition" > onglet "Fidèlité" */ 
#site-content input.hour {
	width: 44px !important;
}

/*  Une catégorie > un produit, onglet "Avis" */ 
#cntcontact{
	margin-top: 15px;
	.td-author{
		border-bottom: 1px solid #C4C4C4;
		border-right: 1px solid #C4C4C4;
		padding: 20px;
	}
	.td-message{
		border-bottom:1px solid #C4C4C4;
		padding:20px;
		max-width: 610px;
		word-wrap: break-word;
	}
	.infos-compl{
		border-top: 1px solid #C3C3C3;
		margin-top: 20px;
		padding-top: 5px;
	}
	.content-action{
		border: 1px solid #C3C3C3;
		float: left;
		margin: 5px;
		padding: 5px;
		width: 370px;
	}
	.content-action-moderate{
		width: 195px;
	}
	.info-value{
		margin-left: 20px;
	}
	.name-action{
		display: block;
		margin-top: 5px;
	}
	textarea{
		height: 150px !important;
		margin: 5px 0 !important;
		width: 524px !important;
	}
}

/*  Une catégorie > un produit, onglet "Référencement" */ 
#table-recherches-internes {
	width: 100%;
	max-width: 820px;
	#volume, #website{
		width: 100px;
	}
	#ctr, #section{
		width: 65px;
	}
	#types {
		width: 130px;
	}
}

/* Catégories > "une catégorie" > "un produit", onglet "Avancé" */ 

/* Tableau des champs avancés */ 
.tb-champs-avances {
	width: 100%;
    .tr-champs { 
        &:not(:first-child) {
            border-top: 1px solid $grey-medium-color;
        }
        .modele-saisie {
            margin-top: 4px;
        }
        .del-link {
            margin-right: 5px;
        }
        @include media('<medium') {
            th {
                display: table-cell !important;
            }
        }
    }
    .bg-ghostwhite {
        background-color: $ghostwhite !important;
    }
	.list-fields {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		div {
			word-break: break-all;
		}
	}
}