$(document).ready(function () {
    // évènement qui crée et télécharge le fichier CSV
    $( "#export" ).on( 'click', function () {
        var div = $('<div>')
                    .addClass('notice')
                    .text(priceWatchingTelechargement);
        
        $(this).parent().before(div);
        var get_data = '';
        if( $('#mask-unupdated').is(':checked') ){
            get_data += '?mask-unupdated=1';
        }
        
        if( $('#mask-unavailable').is(':checked') ){
            if( get_data == "" ){
                  get_data += '?mask-unavailable=1';
            }else{
                  get_data += '&mask-unavailable=1';
            }
           
        }

       $.getJSON('/admin/stats/export-price-watching.php' + get_data, function(){
            div.remove();
           window.location.href = '/admin/stats/price-watching.php?downloadexport=true';
       });
    });

    $('#stats-price-watching thead tr th').click(function(){
        if ($(this).find('a').length) {
            // $(this).find('a').trigger('click');
            window.location.href = $(this).find('a').attr('href');
        }
    });

    $('#mask-unavailable, #mask-unupdated').click(function(){
        $(this).parents('form').submit();
    });
});
