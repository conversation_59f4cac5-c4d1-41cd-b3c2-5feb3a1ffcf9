<?php

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_ZONE');

if( !IS_AJAX ){
	header( 'location: /admin' );
	exit;
}

require_once("sys.zones.inc.php");

if(isset($_GET['id'])){

	$typeZone=array(1 => _("Choisir une région *"),
					2 => _("Choisir un département"),
					3 => _("choisir un état Americain"),
					4 => _("Choisir un land Allemand"),
					5 => _("Choisir un code postal"),
					6 => _("Choisir une ville"),
					8 => _("Choisir une région *"),
					9 => _("Choisir un département"));

	$data = array(
		'type' => 0,
		'zone' => array()
	);

	$child = sys_zones_get( 0, '', '', false, $_GET['id'], '', 0, array(), -1, -1, true, false);
	if(!ria_mysql_num_rows($child)){
		echo _("Aucune zone fille");
		exit;
	}
	while( $c = ria_mysql_fetch_assoc($child)){
		$data['zone'][$c['id']] =  sys_zone_get_label($c);
		$data['type'] = $typeZone[$c['type_id']];
	}
	echo json_encode($data);
	exit;
}
