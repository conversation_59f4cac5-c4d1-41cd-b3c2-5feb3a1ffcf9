{"name": "phpunit/phpunit-mock-objects", "description": "Mock Object library for PHPUnit", "type": "library", "keywords": ["xunit", "mock"], "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/issues", "irc": "irc://irc.freenode.net/phpunit"}, "require": {"php": "^5.6 || ^7.0", "phpunit/php-text-template": "^1.2", "doctrine/instantiator": "^1.0.2", "sebastian/exporter": "^1.2 || ^2.0"}, "require-dev": {"phpunit/phpunit": "^5.4"}, "conflict": {"phpunit/phpunit": "<5.4.0"}, "suggest": {"ext-soap": "*"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture/"]}, "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}}