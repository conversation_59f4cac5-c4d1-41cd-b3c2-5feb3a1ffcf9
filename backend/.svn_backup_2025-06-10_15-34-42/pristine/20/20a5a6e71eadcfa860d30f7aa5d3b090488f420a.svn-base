/**	\file extranet.js
 * 	Ce fichier contient les différentes fonctionnalités d'interaction avec les pages d'importation
 */

$(document).ready(function(){

	// activation du Drag & Drop si on choisi la méthode file
	$('.box').addClass('has-advanced-upload');
	initDragAndDrop();

	// event pour gérer l'enregistrement des changements sur le formulaire
	$('form.extranet-form input').on('change', function(){
		formSubmition($('form.extranet-form'));
	});

	//pour le changement de couleur on affiche le bon nuancier 
	$('#color-picker').on('change', function(){
		$('.color-group').removeClass('color-selected');
		$('.color-group.color-'+$(this).val()+'').addClass('color-selected');
		formSubmition($('form.extranet-form'));
	});
	

	$('#domain, #subdomain').on('change', function(){
		checkDomain();
	});

	checkDomain();

})
.on('click', '#domain-validate-btn', function(e){//pour le changement de nom de domaine
	if(confirm('Confirmez-vous le choix de l’adresse de votre site : '+getDomain()+' ? Celle-ci ne pourra être modifiée après validation.')){
		formSubmition($('form.domain-form'));
	}
	return false;
});

function getDomain(){
	return $('#subdomain').val()+'.'+$('#domain').val();
}
function checkDomain(){
	$('.domain-text').hide();
	$('.domain-button').hide();

	if( $('#subdomain').val() != "" ){
		var url = getDomain();

		$('.domain-text').show();
		$('.domain-text span').html(url);

		if($('#subdomain').attr('disabled') == 'disabled'){
			$('#domain-see-btn').show();
			$('#domain-see-btn').attr("href", "http://"+url);
		}else{
			$('#domain-validate-btn').show();
		}
	}
}


/** Détecte si le navigateur actuellement utilisé supporte l'API Drag & Drop HTML5
 */
function isAdvancedUpload(){
	var div = document.createElement('div');
	return (('draggable' in div) || ('ondragstart' in div && 'ondrop' in div)) && 'FormData' in window && 'FileReader' in window;
}

/** Active la zone de Drag & Drop destinée à accueillir le fichier à importer
 */
function initDragAndDrop(){

	// Custom file input
	// permet de marquer le nom du fichier sélectionné à la place du label "Sélectionnez un fichier"
	$( '.box__file' ).each( function(){

		$(this).on( 'change', function( e )
		{
			var fileName = '';

			if( this.files && this.files.length > 1 )
				fileName = ( this.getAttribute( 'data-multiple-caption' ) || '' ).replace( '{count}', this.files.length );
			else if( e.target.value )
				fileName = e.target.value.split( '\\' ).pop();

			if( fileName )
				$(this).find( 'label' ).find( 'span' ).html( fileName );
			else
				$(this).find( 'label' ).html( "Selectionner un fichier" );

		})
		.on( 'focus', function(){ $(this).addClass( 'has-focus' ); })
		.on( 'blur', function(){ $(this).removeClass( 'has-focus' ); });
	});


	if (isAdvancedUpload) {
		// css purpose
		$('.box').addClass('has-advanced-upload');
		$('.box').on('drag dragstart dragend dragover dragenter dragleave drop', function(e) {
			e.preventDefault();
			e.stopPropagation();
		})
		.on('dragover dragenter', function() {
			// css purpose
			$(this).addClass('is-dragover');
		})
		.on('dragleave dragend drop', function() {
			// css purpose
			$(this).removeClass('is-dragover');
		})
		.on('drop', function(e) {
			// display filename in label
			var droppedFiles = e.originalEvent.dataTransfer.files; // file that is dropped
			$(this).find('label').html(droppedFiles[0].name);
			formSubmition($(this), droppedFiles[0]);
		});
	}
}

function hasValue(elem) {
    return $(elem).filter(function() { return $(this).val(); }).length > 0;
}
function formSubmition($form, input_box, dropped){

	//initialisation des variables
	var $notif  = $( ".notif" ),
		error = [],
		formData = $form.serialize().split('&');

	$notif.html('');

	// mise en forme des données du formulaire
	var data = formData.reduce(function(d,v){
		var tmp = v.split('=');
		d[tmp[0]] = tmp[1];
		return d;
	},{});


	var msg_loader = negociationsEnregistrement;
	var msg_progress = false;

	var formdataObj = new FormData($form[0]);
	if (input_box!=null) {
		formdataObj.append($( "input[type=file]", input_box ).attr('name'), dropped );
		msg_loader = importsTransfertEnCours;
		msg_progress = true;
	}
	 $("input[type=file]").each(function(el){
		if(hasValue(this)){
			msg_loader = importsTransfertEnCours;
			msg_progress = true;
		}
	});


	$('.message-ajax-opacity').html('<div><strong>' + msg_loader + '</strong></div>'+ (msg_progress ? '<div style="height: 10px;background-color:  #fff;"><div class="progressBar" style="width: 0%;height: 10px;background-color: #2196F3;"></div>':'')+'</div>').show();
	$('.load-ajax-opacity').show();

	$.ajax({
		type: "POST",
		url: '/admin/config/extranet/index.php',
		processData: false,
		contentType: false,
    	dataType: 'json',
		data:  formdataObj,
		xhr: function () {
			var xhr = $.ajaxSettings.xhr();
			xhr.upload.onprogress = function (e) {
				// For uploads
				if (e.lengthComputable) {
					$(".progressBar").width((e.loaded / e.total*100) + "%");
				}
			};
			xhr.upload.onload = function(){
			};
			return xhr;
		}
	}).done(function(data) {
		if (data.result) {
			if( data.redirect ){
				location.href = data.redirect
			}else{
				$('.message-ajax-opacity').hide();
				$('.load-ajax-opacity').hide();

				if( $form.hasClass('domain-form') ){
					$('#domain, #subdomain').attr('disabled','disabled');
					checkDomain();
				}
			}
		}else{
			$('.message-ajax-opacity').hide();
			$('.load-ajax-opacity').hide();
			$notif.html('');
			var tmp = '<div class="error">';
			$.each(data.content, function(i, item){
				tmp += item+'<br/>';
			});
			tmp+='</div>';
			$notif.html(tmp);
		}
	})
	.error(function(err) {
		$('.message-ajax-opacity').hide();
		$('.load-ajax-opacity').hide();
		$notif.html(submitError).addClass('error');
	});
};