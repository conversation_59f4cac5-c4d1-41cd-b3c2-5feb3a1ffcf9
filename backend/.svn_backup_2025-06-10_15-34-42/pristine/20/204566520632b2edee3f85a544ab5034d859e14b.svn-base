<?php
	/**
	*	Ce script permet de mettre � jour toutes les positions des services de livraison en fonction de leur srv_id.
	*/
	
	set_include_path(dirname(__FILE__) . '/../include/');
	require_once( 'db.inc.php' );
	
	$rline = ria_mysql_query('
		select srv_tnt_id
		from dlv_services
		group by srv_tnt_id
		
	');
	if( $rline && ria_mysql_num_rows($rline) ){
		while( $line = ria_mysql_fetch_array( $rline ) ){
			$rsrv = ria_mysql_query('select srv_tnt_id, srv_id from dlv_services where srv_tnt_id = '.$line['srv_tnt_id'].' order by srv_is_active desc, srv_name asc');
			
			print 'Service '.$line['srv_tnt_id']."\n";
			if( $rsrv ){
				$pos = 1;
				while( $srv = ria_mysql_fetch_array( $rsrv ) ){
					ria_mysql_query('update dlv_services set srv_pos = '.$pos.' where srv_tnt_id = '.$srv['srv_tnt_id'].' and srv_id = '.$srv['srv_id']);
					$pos++;
				}
			}
		}
	}
	