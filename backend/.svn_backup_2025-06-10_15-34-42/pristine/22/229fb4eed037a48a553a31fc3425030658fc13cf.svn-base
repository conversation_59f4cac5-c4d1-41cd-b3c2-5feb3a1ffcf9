# ContractStoreInfo

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**store_count** | [**\Swagger\Client\Model\StoreCount**](StoreCount.md) |  | [optional] 
**max_store_count** | **int** | The maximum store count related to the offer. | [optional] 
**min_store_count** | **int** | The minimum store count related to the offer. | [optional] 
**owned_store_count** | **int** | The owned store count. | [optional] 
**additional_store_price** | **double** | The additional store price. | [optional] 
**store_included** | **int** | The store count included in the offer. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


