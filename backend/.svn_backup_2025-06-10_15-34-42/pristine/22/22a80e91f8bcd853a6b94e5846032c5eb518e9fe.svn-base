<?php
/**
 * GetProductsRequest
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * GetProductsRequest Class Doc Comment
 *
 * @category Class
 * @description The request message to get products based on these filters
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class GetProductsRequest implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'getProductsRequest';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'page_number' => '\Swagger\Client\Model\BeezUPCommonPageNumber',
        'page_size' => '\Swagger\Client\Model\BeezUPCommonPageSize',
        'column_id_list' => '\Swagger\Client\Model\BeezUPCommonCatalogColumnId[]',
        'exists' => 'bool',
        'product_id_list' => '\Swagger\Client\Model\BeezUPCommonProductId[]',
        'sku' => 'string',
        'title' => 'string',
        'category_path' => '\Swagger\Client\Model\BeezUPCommonCatalogCategoryPath',
        'orderby_catalog_column_id' => '\Swagger\Client\Model\BeezUPCommonCatalogColumnId',
        'without_sub_categories' => 'bool'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'page_number' => null,
        'page_size' => null,
        'column_id_list' => null,
        'exists' => null,
        'product_id_list' => null,
        'sku' => null,
        'title' => null,
        'category_path' => null,
        'orderby_catalog_column_id' => null,
        'without_sub_categories' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'page_number' => 'pageNumber',
        'page_size' => 'pageSize',
        'column_id_list' => 'columnIdList',
        'exists' => 'exists',
        'product_id_list' => 'productIdList',
        'sku' => 'sku',
        'title' => 'title',
        'category_path' => 'categoryPath',
        'orderby_catalog_column_id' => 'orderbyCatalogColumnId',
        'without_sub_categories' => 'withoutSubCategories'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'page_number' => 'setPageNumber',
        'page_size' => 'setPageSize',
        'column_id_list' => 'setColumnIdList',
        'exists' => 'setExists',
        'product_id_list' => 'setProductIdList',
        'sku' => 'setSku',
        'title' => 'setTitle',
        'category_path' => 'setCategoryPath',
        'orderby_catalog_column_id' => 'setOrderbyCatalogColumnId',
        'without_sub_categories' => 'setWithoutSubCategories'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'page_number' => 'getPageNumber',
        'page_size' => 'getPageSize',
        'column_id_list' => 'getColumnIdList',
        'exists' => 'getExists',
        'product_id_list' => 'getProductIdList',
        'sku' => 'getSku',
        'title' => 'getTitle',
        'category_path' => 'getCategoryPath',
        'orderby_catalog_column_id' => 'getOrderbyCatalogColumnId',
        'without_sub_categories' => 'getWithoutSubCategories'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['page_number'] = isset($data['page_number']) ? $data['page_number'] : null;
        $this->container['page_size'] = isset($data['page_size']) ? $data['page_size'] : null;
        $this->container['column_id_list'] = isset($data['column_id_list']) ? $data['column_id_list'] : null;
        $this->container['exists'] = isset($data['exists']) ? $data['exists'] : null;
        $this->container['product_id_list'] = isset($data['product_id_list']) ? $data['product_id_list'] : null;
        $this->container['sku'] = isset($data['sku']) ? $data['sku'] : null;
        $this->container['title'] = isset($data['title']) ? $data['title'] : null;
        $this->container['category_path'] = isset($data['category_path']) ? $data['category_path'] : null;
        $this->container['orderby_catalog_column_id'] = isset($data['orderby_catalog_column_id']) ? $data['orderby_catalog_column_id'] : null;
        $this->container['without_sub_categories'] = isset($data['without_sub_categories']) ? $data['without_sub_categories'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['page_number'] === null) {
            $invalidProperties[] = "'page_number' can't be null";
        }
        if ($this->container['page_size'] === null) {
            $invalidProperties[] = "'page_size' can't be null";
        }
        if ($this->container['column_id_list'] === null) {
            $invalidProperties[] = "'column_id_list' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['page_number'] === null) {
            return false;
        }
        if ($this->container['page_size'] === null) {
            return false;
        }
        if ($this->container['column_id_list'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets page_number
     *
     * @return \Swagger\Client\Model\BeezUPCommonPageNumber
     */
    public function getPageNumber()
    {
        return $this->container['page_number'];
    }

    /**
     * Sets page_number
     *
     * @param \Swagger\Client\Model\BeezUPCommonPageNumber $page_number page_number
     *
     * @return $this
     */
    public function setPageNumber($page_number)
    {
        $this->container['page_number'] = $page_number;

        return $this;
    }

    /**
     * Gets page_size
     *
     * @return \Swagger\Client\Model\BeezUPCommonPageSize
     */
    public function getPageSize()
    {
        return $this->container['page_size'];
    }

    /**
     * Sets page_size
     *
     * @param \Swagger\Client\Model\BeezUPCommonPageSize $page_size page_size
     *
     * @return $this
     */
    public function setPageSize($page_size)
    {
        $this->container['page_size'] = $page_size;

        return $this;
    }

    /**
     * Gets column_id_list
     *
     * @return \Swagger\Client\Model\BeezUPCommonCatalogColumnId[]
     */
    public function getColumnIdList()
    {
        return $this->container['column_id_list'];
    }

    /**
     * Sets column_id_list
     *
     * @param \Swagger\Client\Model\BeezUPCommonCatalogColumnId[] $column_id_list column_id_list
     *
     * @return $this
     */
    public function setColumnIdList($column_id_list)
    {
        $this->container['column_id_list'] = $column_id_list;

        return $this;
    }

    /**
     * Gets exists
     *
     * @return bool
     */
    public function getExists()
    {
        return $this->container['exists'];
    }

    /**
     * Sets exists
     *
     * @param bool $exists Search for existing products or not. If null you will received both.
     *
     * @return $this
     */
    public function setExists($exists)
    {
        $this->container['exists'] = $exists;

        return $this;
    }

    /**
     * Gets product_id_list
     *
     * @return \Swagger\Client\Model\BeezUPCommonProductId[]
     */
    public function getProductIdList()
    {
        return $this->container['product_id_list'];
    }

    /**
     * Sets product_id_list
     *
     * @param \Swagger\Client\Model\BeezUPCommonProductId[] $product_id_list Filter with a list of product identifier
     *
     * @return $this
     */
    public function setProductIdList($product_id_list)
    {
        $this->container['product_id_list'] = $product_id_list;

        return $this;
    }

    /**
     * Gets sku
     *
     * @return string
     */
    public function getSku()
    {
        return $this->container['sku'];
    }

    /**
     * Sets sku
     *
     * @param string $sku Search for products containing this SKU (merchant product dentifier).
     *
     * @return $this
     */
    public function setSku($sku)
    {
        $this->container['sku'] = $sku;

        return $this;
    }

    /**
     * Gets title
     *
     * @return string
     */
    public function getTitle()
    {
        return $this->container['title'];
    }

    /**
     * Sets title
     *
     * @param string $title Search for products containing this title
     *
     * @return $this
     */
    public function setTitle($title)
    {
        $this->container['title'] = $title;

        return $this;
    }

    /**
     * Gets category_path
     *
     * @return \Swagger\Client\Model\BeezUPCommonCatalogCategoryPath
     */
    public function getCategoryPath()
    {
        return $this->container['category_path'];
    }

    /**
     * Sets category_path
     *
     * @param \Swagger\Client\Model\BeezUPCommonCatalogCategoryPath $category_path category_path
     *
     * @return $this
     */
    public function setCategoryPath($category_path)
    {
        $this->container['category_path'] = $category_path;

        return $this;
    }

    /**
     * Gets orderby_catalog_column_id
     *
     * @return \Swagger\Client\Model\BeezUPCommonCatalogColumnId
     */
    public function getOrderbyCatalogColumnId()
    {
        return $this->container['orderby_catalog_column_id'];
    }

    /**
     * Sets orderby_catalog_column_id
     *
     * @param \Swagger\Client\Model\BeezUPCommonCatalogColumnId $orderby_catalog_column_id orderby_catalog_column_id
     *
     * @return $this
     */
    public function setOrderbyCatalogColumnId($orderby_catalog_column_id)
    {
        $this->container['orderby_catalog_column_id'] = $orderby_catalog_column_id;

        return $this;
    }

    /**
     * Gets without_sub_categories
     *
     * @return bool
     */
    public function getWithoutSubCategories()
    {
        return $this->container['without_sub_categories'];
    }

    /**
     * Sets without_sub_categories
     *
     * @param bool $without_sub_categories Do not retrieve sub categories. By default, this value is set to false
     *
     * @return $this
     */
    public function setWithoutSubCategories($without_sub_categories)
    {
        $this->container['without_sub_categories'] = $without_sub_categories;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


