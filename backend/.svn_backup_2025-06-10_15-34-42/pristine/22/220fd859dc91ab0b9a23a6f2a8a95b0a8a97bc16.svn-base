<?php
require_once('Services/Service.class.php');
require_once('Services/Documents/Document.class.php');
require_once('Services/Catalog/Brand.class.php');
require_once('Services/Catalog/Promotions.class.php');

require_once('documents.inc.php');
require_once('prd/colisage.inc.php');

/**	\brief Cette classe permet de charger les informations sur un produit en fonction de son contexte.
 *
 */
class ProductService extends Service {
	protected $ref					= ''; ///< Référence du produit
	protected $barcode 				= ''; ///< Code à barres du produit
	protected $title				= ''; ///< Titre du produit
	protected $desc					= ''; ///< Description courte du produit
	protected $longdesc				= ''; ///< Description longue du produit
	protected $new					= ''; ///< Si le produit est nouveau (yes | no)
	protected $isparent     		= null; ///< Si le produit est un parent (yes | no)
	protected $mainimage 			= ''; ///< URL de l'image principale du produit
	protected $stock				= null; ///< Quantité en stock
	protected $stocklivr 			= null; ///< Date de la prochaine remise en stock
	protected $url					= ''; ///< URL du produit
	protected $typenomenclature 	= null; ///< Type de nomenclature (null|0 = ce n'est pas une nomenclature | 5 = variable)
	protected $followstock 			= true; ///< Le produit est-il suivi en stock ?
	protected $alertstock 			= false; ///< Permet de savoir si le client est déjà inscript à l'alerte de disponibilité
	protected $bookmark 			= 'no'; ///< Si oui ou non le produit est présent dans les favoris (yes | no)
	protected $weight 				= null; ///< Poids brut (en gramme)
	protected $weightnet 			= null; ///< Poids net (en gramme)
	protected $brand 				= null; ///< Information sur la marque lié à ce produit
	protected $orderable 			= 'no'; ///< si oui ou non le produit est commandable

	protected $publicht 			= null; ///< Prix public HT
	protected $publicttc 			= null; ///< Prix public TTC
	protected $priceht      		= null; ///< Prix HT du produit
	protected $tvarate      		= null; ///< Taux de TVA (ex. 1.200 pour une TVA de 20 %)
	protected $pricettc     		= null; ///< Prix TTC du produit
	protected $ecotaxe     			= null; ///< Ecotaxe du produit
	protected $sellunit 			= 1; ///< Unité de vente (ex. vendu par 3)
	protected $colisages			= []; ///< Colisages
	protected $promotions 			= []; ///< Promotions spéciales s'appliquant au produit
	protected $promotionsDetails = []; ///< Détails de promotions spéciales s'appliquant au produit

	protected $ispromo 				= false; ///< Si oui ou non le produit est en promotion
	protected $promovalue 			= ''; ///< Texte de promotion (ex. -5€ ou  -5%)
	protected $promovaluettc 			= ''; ///< Texte de promotion (ex. -5€ ou  -5%) TTC
	protected $origpriceht 			= null; ///< Tarif originial du produit (si en promotion)
	protected $origpricettc 		= null; ///< Prix TTC original du produit (si en promotion)

	protected $images 				= null; ///< Tableau contenant l'URL des images
	protected $relations    		= null; ///< Tableau contenant toutes les relations produits
	protected $children				= null; ///< Tableau contenant les enfants correspondants aux options de champs avancés
	protected $fields       		= null; ///< Tableau contenant les informations des champs avancés liés au produit
	protected $displayFields       	= null; ///< Tableau contenant les informations des champs avancés liés au produit et qu'il est possible d'afficher
	protected $mainclassify 		= []; ///< Tableau contenant le classement principal du produit
	protected $documents 			= null; ///< Documents rattachés au produit
	protected $delivery 			= null; ///< Informations sur les dates de livraison estimées
	protected $options 				= null; ///< Options de déclinaison
	protected $nomenclature 		= null; ///< Nomenclature du produit
	protected $breadcrumbs 			= null; ///< Fil d'Ariane du produit
	protected $livralert 			= false; ///< Si oui ou non le client connecté reçoit l'alerte de disponibilité pour ce produit
	protected $date_available		= null; ///< Date de disponibilité du produit (format EN)

	protected $reviews = null; ///< Liste des avis produits

	protected $id = 0; ///< Identifiant du produit
	private	$with_price = false; ///< Si le tarif est chargé par défaut
	private $brandid = 0; ///< Identifiant de la marque lié à cette article
	private $nopromo = false; ///< Ne pas récupérer les promotions spéciales
	private $use_childs_price = true; ///< Par défaut les tarifs enfants détermine le tarif du parent

	private $with_related_images = true; ///< Récupére les images des produits des nomenclatures, enfants, options
	private $cfg_img = '';
	private $loadgeneralinfo = false;

	private $cat_id = 0;
	private $cat_recursive = true;

	/** Cette fonction permet de créer un objet permettant le chargement des informations du produit.
	 *  @param $data Optionnel, permet de transmettre les informations suivantes :
	 *                - prd : identifiant du produit à charger
	 *                - withprice : charge le tarif du produit
	 */
	public function __construct( $data=[] ){
		global $config;
		// Information privée sur le produit permettant le chargement de ces informations
		$this->id 							= ria_array_get($data, 'prd', 0);
		$this->with_price 					= ria_array_get($data, 'withprice', false);
		$this->nopromo 						= ria_array_get($data, 'nopromo', false);
		$this->with_related_images			= ria_array_get($data, 'relatedimages', true);
		$this->cfg_img						= ria_array_get($data, 'cfgimg', '');
		$this->use_childs_price 			= ria_array_get($data, 'usechildsprice', true) === true;

		// Informations générales sur le produits
		$this->ref 					= ria_array_get($data, 'ref', '');
		$this->barcode 				= ria_array_get($data, 'barcode', '');
		$this->ecotaxe 				= ria_array_get($data, 'ecotaxe', '');
		$this->title 				= ria_array_get($data, 'title', '');
		$this->desc 				= ria_array_get($data, 'desc', '');
		$this->longdesc 			= ria_array_get($data, 'longdesc', '');
		$this->new 					= ria_array_get($data, 'new', '');
		$this->stock 				= ria_array_get($data, 'stock', '');
		$this->stocklivr 			= ria_array_get($data, 'stocklivr', '');
		$this->url 					= ria_array_get($data, 'url', '');
		$this->url 					= ria_array_get($data, 'url_alias', $this->url);
		$this->followstock 			= isset($data['followstock']) && $data['followstock'];
		$this->orderable 			= ria_array_get($data, 'orderable', '');
		$this->weight 				= ria_array_get($data, 'weight', '');
		$this->weightnet 			= ria_array_get($data, 'weightnet', '');

		// Information de tarifs
		$this->priceht 				= ria_array_get($data, 'priceht', '');
		$this->pricettc 			= ria_array_get($data, 'pricettc', '');
		$this->tvarate 				= ria_array_get($data, 'tvarate', '');

		$this->cat_id = ria_array_get($data, 'cat', $config['cat_root']);
		$this->cat_recursive = ria_array_get($data, 'cat_recursive', true);
	}

	/** Cette fonction permet de charger les informations principales sur le produit à partir de sa référence.
	 * 	@return ProductService L'objet courant
	 */
	public function loadByRef(){
		if( trim($this->ref) == '' ){
			throw new Exception('La référence saisie n\'a pas été trouvée dans le catalogue.', 1);
		}

		// Recherche l'identifiant du produit correspond à la référence
		$prd_id = prd_products_get_id( $this->ref );
		if( !is_numeric($prd_id) || $prd_id <= 0 ){
			throw new Exception('La référence saisie n\'a pas été trouvée dans le catalogue.', 1);
		}

		$this->ref = '';
		$this->id = $prd_id;
		return $this->general();
	}

	/** Cette fonction permet de charger les informations principales sur le produit.
	 */
	public function general(){
		global $config, $hook;

		if( in_array($config['tnt_id'], [171, 977, 998, 1043, 1024]) ){
			$this->with_price = false;
		}

		// Désactive la récupération des tarifs quand l'utilisateur n'est pas connecté
		if( Template::get('catalog-show-price-connected') ){
			if( !CustomerService::getInstance()->isConnected() ){
				$this->with_price = false;
			}
		}

		if( $this->loadgeneralinfo ){
			return $this;
		}
		$this->loadgeneralinfo = true;
		// Chargement des informations sur le produit si celles-ci sont manquantes
		if( trim($this->ref) == '' ){
			$params = [];

			if($this->orderable){
				$params['orderable'] = true;
			}

			$r_product = prd_products_get_simple($this->id, '', true, $this->cat_id, $this->cat_recursive, false, $this->with_price, false, $params);

			if (!$r_product || !ria_mysql_num_rows($r_product)) {
				throw new Exception('Le produit n\'existe pas ou plus.');
			}

			// Traduction du produit
			$product = i18n::getTranslation( CLS_PRODUCT, ria_mysql_fetch_assoc($r_product) );

			// Informations générales présentes sur le produit
			$this->ref 			= $product['ref'];
			$this->barcode 		= $product['barcode'];
			$this->ecotaxe 		= $product['ecotaxe'];
			$this->title 		= $product['title'];
			$this->desc 		= view_site_format_description($product['desc']);
			$this->longdesc 	= view_site_format_riawysiwyg($product['desc-long']);
			$this->new 			= $product['new'] ? 'yes' : 'no';
			$this->stock 		= $product['stock'];
			$this->stocklivr 	= isdate($product['stock_livr']) ? dateparse( $product['stock_livr'] ) : null;
			$this->url 			= isset($product['url_alias']) ? rew_strip($product['url_alias']) : '';
			$this->followstock 	= $product['follow_stock'];
			$this->orderable 	= $product['orderable'] ? 'yes' : 'no';
			$this->weight 		= $product['weight'];
			$this->weightnet 	= $product['weight_net'];
			$this->brandid 		= $product['brd_id'];
		}

        if( in_array($config['tnt_id'], [135]) ){
            $this->loadAllStock();
        }

        if( trim($this->url) == '' ){
			$this->url = prd_products_get_url( $this->id, true, $config['cat_root'] );
		}

		// récupération de l'id du premier article parent pour essayer de récupérer une url
		if( trim($this->url) == '' ){
			$parent = prd_products_get_parent( $this->id, true );
			if( is_numeric($parent) && $parent > 0 ){
				$this->url = prd_products_get_url( $parent, true, $config['cat_root'] );
			}
		}

		// S'il s'agit d'un article parent, les informations suivante sont chargées depuis les enfants :
		//		- stock
		//		- nouveauté
		if( $this->getIsParent() ){
			$this->stock = prd_products_get_childs_stock( $this->id );

			if( prd_products_get_childs_new($this->id) ){
				$this->new = 'yes';
			}
		}

		// S'il s'agit d'un article de type nomenclature variable, l'information de stock est chargé selon les articles présents dans les options
		if( $this->getTypeNomenclature() == NM_TYP_VARIABLE ){
			$dps = prd_deposits_get_main();

			// On recherche la quantité minimale de stock pour chaque option
			$result = ria_mysql_query('
				select min(total_stock) as min_total, min(min_stock) as stock
				from (
						select
							pop.opt_id as opt,
							sum(sto_qte - sto_prepa) as total_stock,
							min(if( ifnull(sto_qte, 0) - ifnull(sto_prepa, 0) <= 0, 999999, (ifnull(sto_qte, 0) - ifnull(sto_prepa, 0)))) as min_stock
						from prd_nomenclatures_options as pno
							join prd_options_products as pop on (pop.opt_tnt_id = '.$config['tnt_id'].' and pop.opt_id = pno.prd_opt_id)
							left join prd_stocks on (sto_tnt_id = '.$config['tnt_id'].' and sto_prd_id = pop.prd_id and sto_dps_id = '.$dps.')
					where pno.prd_tnt_id = '.$config['tnt_id'].'
					group by opt_id
				) as result
			');

			if( $result && ria_mysql_num_rows($result) ){
				$r = ria_mysql_fetch_assoc( $result );
				$this->stock = is_numeric($r['min_total']) && $r['min_total'] > 0 ? $r['stock'] : 0;
			}
		}

		// Charge l'unité de vente
		$temp_udv = fld_object_values_get( $this->id, _FLD_PRD_SALES_UNIT );
		if( is_numeric($temp_udv) && $temp_udv > 0 ){
			$this->sellunit = $temp_udv;
		}

		$this->loadColisage();

		// On regarde si le produit fait parti des articles en favoris du compte connecté
		$user = CustomerService::getInstance();
		if( $user->isConnected() ){
			$this->bookmark = gu_bookmarks_exists( $user->getID(), $this->id ) ? 'yes' : 'no';

			// S'il n'y a pas de stock, on regarde si le compte connecté est inscrit à l'alert de disponibilité
			if( $this->orderable == 'no' || $this->stock <= 0 ){
				$this->livralert = gu_livr_alerts_exists( $user->getEmail(), $this->id, $config['wst_id'] );
			}
		}

		// Chargemement du tarif en même temps si cela est demandé
		if( $this->with_price && !Template::get('catalog-only-price-colisage') && (is_null($this->priceht) || $this->priceht <= 0) ){
			if( isset($product) && ria_array_key_exists(array('price_ht', 'tva_rate', 'price_ttc'), $product) ){
				$this->priceht = $product['price_ht'];
				$this->tvarate = $product['tva_rate'];
				$this->pricettc = $product['price_ttc'];
			}
			$this->loadPrice();
		}

		return $this;
	}

    /** Cette fonction permet d'ajouter le stock des depots secondaires.
     */
    public function loadAllStock() {
        if( trim($this->ref) == '' ){
            $params = [];

            if($this->orderable){
                $params['orderable'] = true;
            }

            try {
                $ar_deposits = array();
                $r_deposits = prd_deposits_get(null, 0);
                if( $r_deposits ){
                    while( $deposit = ria_mysql_fetch_assoc($r_deposits) ){
                        $ar_deposits[] = $deposit['id'];
                    }
                }
                foreach ($ar_deposits as $dps_id) {
                    $params['dps'] = $dps_id;
                    $r_product = prd_products_get_simple($this->id, '', true, $this->cat_id, $this->cat_recursive, false, $this->with_price, false, $params);
                    if (!$r_product || !ria_mysql_num_rows($r_product)) {
                        continue;
                    }

                    $product = ria_mysql_fetch_assoc($r_product);
                    $this->stock += $product['stock'];
                }
            }
            catch(\Exception $e) {}
        }
    }

	/** Cette fonction permet de charger les avis sur un article.
	 * @param	bool	$options	Permet d'activer ou non la récupération des avis des produits enfants
	 * @return ProductService L'objet courant
	 */
	public function reviews($options=true){

		if( $this->reviews !== null ){
			return $this;
		}
		global $config;

		$this->reviews = [
			'note'			=> 0,
			'count'			=> 0,
			'countbynote'	=> [
				5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0
			],
			'list' => new Collection()
		];

		// Si activer, on récupére les avis déposés sur Avis Vérifiés
		// Les avis doivent être importé dans RiaShop via le script /crontabs/avis-verifie-get-notice.php
		// afin de limiter les temps de chargement sur le site.

		$r_reviews = prd_reviews_get( 0, $this->id );

		if( !ria_mysql_num_rows($r_reviews) ){
			return $this;
		}
		while( $reviews = ria_mysql_fetch_assoc($r_reviews) ){
			if( !isset($reviews['note'], $this->reviews['countbynote']) ){
				continue;
			}

			// Compte le nombre d'avis par note (de 1 à 5)
			$this->reviews['countbynote'][ $reviews['note'] ]++;

			// Additionne les notes et le nombre de message
			$this->reviews['note'] += $reviews['note'];
			$this->reviews['count']++;

			// Ajoute l'avis au tableau final
			$this->reviews['list']->addItem([
				'title' => $reviews['name'],
				'desc' => $reviews['desc'],
				'date' => $reviews['date-en'],
				'note' => $reviews['note'],
				'answer' => $reviews['ans_desc'],
				'on_avis_verifie' => trim($reviews['id_verifie']) != '',
				'author' => [
					'firstname' => $reviews['usr_firstname']
				]
			]);
		}

		// Réalise une moyenne des notes
		if( $this->reviews['count'] > 0 ){
			$this->reviews['note'] = round( $this->reviews['note'] / $this->reviews['count'], 1 );
		}

		return $this;
	}

	/** Charge les colisages supportés par ce produit (si supporté par ce produit).
	 *	Cette méthode remplit la propriété $this->colisages avec un tableau associatif. Chaque item contient les propriétés suivantes :
	 *	- id : identifiant du colisage
	 *	- name : désignation du colisage
	 *	- qte : nombre d'unités de ventes contenues dans le colis
	 *	- price_ht : tarif HT du colis
	 *	- tva_rate : taux de TVA applicable, au format 1.XX
	 *	- price_ttc : tarif TTC du colis
	 *	@return	object	L'objet en cours
	 */
	private function loadColisage(){
		global $config;

		// Charge l'utilisateur actuellement connecté pour appliquer le bon tarif
		$global_user = CustomerService::getInstance();


		$this->loadPrice();

		$colisage = prd_colisage_classify_get( 0, $this->id, 0, ['qte' => 'asc'] );

		if( !ria_mysql_num_rows($colisage) ){
			return $this;
		}

		while( $col = ria_mysql_fetch_assoc($colisage) ){

			// Pour Benjamin, on ignore pour l'instant les conditionnements à la paire
			if( $config['tnt_id']==135 && stristr($col['col_name'],'PA')!==false ){
				continue;
			}

			// Charge le tarif du produit pour ce colisage
			$price = prd_products_get_price_array($this->id, $global_user->getID(), $global_user->getPriceCategory(), 0, 1, $col['col_id']);
			if( !ria_array_key_exists(['price_ht', 'tva_rate', 'price_ttc'], $price) ){
				// Aucun tarif pour ce colisage n'a été trouvé
				// Le tarif de base du produit est alors utilisé
				$price = array(
					'price_ht' => $this->priceht,
					'tva_rate' => $this->tvarate,
					'price_ttc' => $this->pricettc
				);
			}

			// Enregistre le colisage et son tarif dans la propriété $this->colisages
			$this->colisages[] = [
				'id' => $col['col_id'],
				'name' => $col['col_name'],
				'qte' => $col['qte'],
				'price_ht' => $price['price_ht'],
				'tva_rate' => $price['tva_rate'],
				'price_ttc' => $price['price_ttc']
			];

		}
		return $this;

	}

	/** Cette fonction permet de charger les données du produit pour afficher son encart dans un listing.
	 *  @return L'objet après chargement des informations
	 */
	public function card(){
		global $config, $hook;

		// Active la récupération des tarifs dans l'encard
		if( Template::get('productcard-show-price') ){
			$this->with_price = true;
		}

		// Désactive la récupération des tarifs quand l'utilisateur n'est pas connecté
		if( Template::get('catalog-show-price-connected') ){
			if( !CustomerService::getInstance()->isConnected() ){
				$this->with_price = false;
			}
		}

		// Chargement des informations principales
		$res = $this->general();
		if( $res === false ){
			throw new Exception('Impossible de charger les informations du produit.');
		}

		$thumb = $config['img_sizes']['high'];

		if( trim(Template::get('productcard-cfg-image')) && array_key_exists(Template::get('productcard-cfg-image'), $config['img_sizes']) ){
			$thumb = $config['img_sizes'][ Template::get('productcard-cfg-image') ];
		}

		// Chargement de l'image principale
		$img_main = prd_images_main_get($this->id);
		if( !is_numeric($img_main) || $img_main <= 0 ){
			$img_main = isset($config['default_image']) ? $config['default_image'] : 0;

			$this->mainimage = [
				'id'		=> $img_main,
				'url'		=> $config['img_url'].'/'.$thumb['dir'].'/'.$img_main.'.'.$thumb['format'],
				'alt'		=> '',
				'width'		=> $thumb['width'],
				'height'	=> $thumb['height'],

			];
		}else{
			$rimg = prd_images_get($this->id, $img_main, false, true, true);

			if( ria_mysql_num_rows($rimg) ){
				$img = ria_mysql_fetch_assoc($rimg);

				$this->mainimage = [
					'id'		=> $img_main,
					'url'		=> $config['img_url'].'/'.$thumb['dir'].'/'.$img_main.'.'.$thumb['format'],
					'alt'		=> trim($img['alt']) != '' ? $img['alt'] : '',
					'width'		=> $thumb['width'],
					'height'	=> $thumb['height'],
				];

			}
		}

		// Active la récupération des avis sur le produit dans l'encard
		if( Template::get('productcard-with-reviews') ){
			$this->reviews();
		}

		// Hook permettant de compléter les informations générales d'un produit
		$hook->do_action( 'ProductService_cardCompleted', ['prd' => $this] );

		return $this;
	}

	/**	Cette méthode permet de charger la prochaine date de disponibilité du produit
	 * @return	ProductService	L'objet en cours
	 */
	public function availability(){

		if( $this->stock > 0 || !$this->id ){
			return $this;
		}
		$date = prd_products_date_available($this->id, true);

		if( $date && strtotime($date) ){
			$this->date_available = $date;
		}
		return $this;


	}

	/** Cette fonction permet de charger les informations nécessaire à l'affichage d'une fiche produit.
	 * 	@param bool $all_child_data Optionnel, permet de retourner toutes les informations sur les articles enfants (par défaut seul l'identiant + le nom)
	 * 	@return object L'objet courant
	 */
	public function index( $all_child_data=false ){
		global $hook;

		// Charge les informations générales
		$this->general();

		// Charge les informations avancés
		$this->fields();

		// Charge les informations avancés et destinées à l'affichage
		$this->displayFields();

		// Récupère les images du produit
		$this->images($this->cfg_img);

		// Récupère les dates estimées de livraison
		$this->delivery();

		// Récupère les relations personnalisés
		if( Template::get('product-relations-active') ){
			$this->relations();
		}

		// Récupère les documents rattachés au produit (seulement si l'option de téléchargement est activée)
		if( Template::get('product-documents-download-active') ){
			$this->documents();
		}

		// Charge les options de déclinaisons s'il en existe
		$this->optionsChart( false, false, $all_child_data );

		// Charge les nomenclatures variables s'il en existe
		$this->nomenclature();

		// Charge la date de prochaine disponibilité du produit s'il n'est pas en stock
		$this->availability();

		// Hook permettant de compléter les informations générales d'un produit
		$hook->do_action( 'ProductService_indexCompleted', ['prd' => $this] );

		return $this;
	}

	/** Cette fonction permet de charger les images du produit.
	 * 	@return object L'objet courant
	 */
	public function images( $cfg='' ){
		global $config;

		$this->images = new Collection();

		// Charge la configuration des miniatures (par défaut "high" est utilisé)
		$thumb = $config['img_sizes']['high'];
		if( trim($cfg) != '' && array_key_exists($cfg, $config['img_sizes']) ){
			$this->cfg_img = $cfg;
			$thumb = $config['img_sizes'][$cfg];

		}elseif( trim($this->cfg_img) != '' && array_key_exists($this->cfg_img, $config['img_sizes']) ){
			$thumb = $config['img_sizes'][$this->cfg_img];

		}

		// Récupère toutes les images du produits
		$ar_img = prd_images_get_all( $this->id, $this->with_related_images, true );

		if( Template::get('index-product-image-noempty') && (!is_array($ar_img) || !count($ar_img)) ){
			$img_default = isset($config['default_image']) ? $config['default_image'] : 0;
			if( $img_default ){
				$ar_img = [ $img_default ];
			}

			$this->images->addItem([
				'prd'		=> $this->id,
				'id'		=> $img_default,
				'url'		=> $config['img_url'].'/'.$thumb['dir'].'/'.$img_default.'.'.$thumb['format'],
				'alt'		=> trim( $this->title ),
				'width'		=> $thumb['width'],
				'height'	=> $thumb['height'],
			], 'img'.$img_default);
		}

		if( is_array($ar_img) ){
			foreach( $ar_img as $prd_id => $imgs ){

				if( !is_array($imgs) || !count($imgs) ){
					continue;
				}

				foreach($imgs as $img_id){
					$rimg = prd_images_get($prd_id, $img_id, false, true, true);

					if( !ria_mysql_num_rows($rimg) ){
						continue;
					}
					$img = ria_mysql_fetch_assoc($rimg);

					if( $this->images->exitsKey('img'.$img_id) ){
						continue;
					}

					$this->images->addItem([
						'prd'		=> $prd_id,
						'id'		=> $img_id,
						'url'		=> $config['img_url'].'/'.$thumb['dir'].'/'.$img_id.'.'.$thumb['format'],
						'alt'		=> trim($img['alt']) != '' ? $img['alt'] : '',
						'width'		=> $thumb['width'],
						'height'	=> $thumb['height'],
					], 'img'.$img_id);
				}
			}
		}

		// Renseigne l'image principale du produit
		if( $this->images->length() ){
			$this->mainimage = $this->images->getFirstItem();
		}

		return $this;
	}

	/** Cette fonction permet de charger les relations personnalisées d'un article
	 * @param	string	$cfg	optionnel, configuration images
	 * @param	string	$relation_type	optionnel, type de relation à charger, peut être un id ou un tableau d'id
	 * @return	ProductService L'object courant
	 */
	public function relations($cfg='', $relation_type=null){
		$this->relations = new Collection();

		// Récupère les différents type de relations
		if( $relation_type !== null && control_array_integer($relation_type) ){
			$r_type = prd_relations_types_get( $relation_type );
		}else{
			$r_type = prd_relations_types_get();
		}

		if( $r_type ){
			while( $type = ria_mysql_fetch_assoc($r_type) ){
				$temp = [
					'name' => $type['name'],
					'name_plural' => $type['name_plural'],
					'products' => new Collection()
				];

				// Récupère les articles présents dans cette relation
				$r_prd_rel = prd_relations_get( $this->id, null, $type['id'], true, 0, false, false );
				if( $r_prd_rel ){
					while( $prd_rel = ria_mysql_fetch_assoc($r_prd_rel) ){
						try{
							$prd_relation = new ProductService([
								'prd'			=> $prd_rel['dst_id'],
								'withprice'		=> true,
								'cfgimg'		=> $cfg,
								'relatedimages'	=> false
							]);
							$prd_relation->general()->images($cfg);

							$temp['products']->addItem( $prd_relation );
						}catch(Exception $e){
							// pas d'erreur le produit lié n'existe pas ou plus
						}
					}
				}

				if( $temp['products']->length() ){
					$this->relations->addItem( $temp, 'rel'.$type['id'] );
				}
			}
		}

		return $this;
	}

	/** Cette fonction permet de récupérer la propriété "options" du produit.
	 * 	@return array|null La propriété "options"
	 */
	public function getOptionsChart(){
		return $this->options;
	}

	/** Cette fonction permet de charger les options de déclinaisons.
	 *  Utiliser pour la gestion d'article parent / enfant (ex.  article existant en taill 36 / 37 et en couleur Rouge / Vert / Bleu)
	 *
	 * 	Pour charger les options, le système récupère les modèles de saisie ayant l'information "Utiliser comme options de déclinaison" à "Oui.
	 * 	Si aucun modèle de saisie n'existe, alors le système chargera les articles enfants en tant qu'options.
	 *
	 * 	Exemple : Options sans modèle de saisie
	 * 	[
	 * 		[type] => childs
	 * 		[options] => [
	 * 			[childs] => [
	 * 				[id] => childs
	 * 				[name] => Déclinaisons
	 * 				[selected] =>
	 * 				[values] => [
	 * 					[0] => [
	 * 						[id] => 2133562
	 * 						[name] => première déclinaison - 36 Bleu
	 * 					]
	 * 					[1] => [
	 * 						[id] => 2133563
	 * 						[name] => deuxième déclinaison - 36 Noir
	 * 					]
	 * 					[2] => [
	 * 						[id] => 2133565
	 * 						[name] => troisième déclinaison - 39 Gris
	 * 					]
	 * 				]
	 * 			]
	 * 		]
	 * 	]
	 *
	 * 	Exemple : Options avec modèle de saisie
	 * 	[
	 *		['type'] => 'fields'
	 *		['options'] => [
	 *			[101710] => [
	 *				['id'] => 101710
	 *				['name'] => 'Taille'
	 *				['selected'] =>
	 *				['values'] => [
	 *					[36] => [
	 *						['id'] => 36
	 *						['name'] => 36
	 *					]
	 *					[39] => [
	 *						['id'] => 39
	 *						['name'] => 39
	 *					]
	 *				]
	 *			]
	 *
	 *			[101709] => [
	 *				['id'] => 101709
	 *				['name'] => 'Couleur'
	 *				['selected'] =>
	 *				['values'] => [
	 *					['Bleu'] => [
	 *						['id'] => 'Bleu'
	 *						['name'] => 'Bleu'
	 *					]
	 *					['Noir'] => [
	 *						['id'] => 'Noir'
	 *						['name'] => 'Noir'
	 *					]
	 *					['Gris'] => [
	 *						['id'] => 'Gris'
	 *						['name'] => 'Gris'
	 *					]
	 *				]
	 *			]
	 *		]
	 *	]
	 *
	 *	@param bool|array $options Optionnel, tableau contenant les options déjà sélectionné
	 *	@param bool|int $select_fld Optionnel, identifiant de la dernière option sélectionnée
	 * 	@param bool $all_child_data Optionnel, permet de retourner toutes les informations sur les articles enfants (par défaut seul l'identiant + le nom)
	 * 	@param array $multisort Optionnel, tableau contenant les champs de tri direction (ex. ['order' => SORT_ASC]) et type de tri (ex. ['type' => SORT_NATURAL]).
	 *										Voir toutes les options pour la fonction array_multisort() de PHP
	 * 	@return object L'objet courant
	 */
	public function optionsChart( $options=false, $select_fld=false, $all_child_data=false, $multisort=false ){
		global $config, $hook;

		if( !$this->id ){
			throw new Exception( i18n::get('Aucun produit n\'est chargé.', 'ERROR', i18n::getLang()), 98 );
		}

		// Hook : remplace la fonction optionsChart
		if( $hook->do_action('ProductService_replaceOptionsChart', ['prd' => $this, 'options' => $options, 'select_fld' => $select_fld, 'all_child_data' => $all_child_data]) ){
			return $this;
		}

		// Seuls les articles parent disposent de déclinaison
		if( !$this->getIsParent() ){
			return $this;
		}

		// Application du dernier filtre choisi
		$val_fld = $fld = false;
		if( is_array($options) && count($options) ){
			if( array_key_exists($select_fld, $options) ){
				$val_fld = $options[ $select_fld ];
				$fld[ $select_fld ] = $val_fld;
			}
		}

		$opt_type = 'childs';
		$ar_options = [];

		$ar_all_child = [];
		$ar_selected_child = [];

		// Récupère tous les articles enfants
		$orderby = prd_relations_order_get('childs');
		$sort = [ 'ref' => 'asc' ];
		if( $orderby !== false ){
			$sort = [ 'child_pos' => 'asc' ];
		}

		$r_child = prd_products_get_simple( 0, '', true, $config['cat_root'], true, false, false, $sort, array('childs' => true, 'parent' => $this->id) );
		if( $r_child ){
			while( $child = ria_mysql_fetch_assoc($r_child) ){
				$ar_all_child[] = $child['id'];
			}
		}

		// Récupère les champs avancés utilisés comme filtre (trié par position)
		$r_field = fld_models_fields_get( 0, 0, true, ['pos' => 'asc'] );
		$has_fld_options = false;

		if( ria_mysql_num_rows($r_field) ){
			$opt_type = 'fields';

			// Récupère les articles enfants seulement liés à la dernière option choisie
			if(is_array($fld) ){
				$r_child = prd_products_get_simple( 0, '', false, 0, false, $fld, false, false, array('childs' => true, 'parent' => $this->id, 'prd_publish' => true, 'fld_use_hierarchy_ids' => true) );
				if($r_child ){
					while( $child = ria_mysql_fetch_assoc($r_child) ){
						$ar_selected_child[] = $child['id'];
					}
				}
			}

			while( $field = ria_mysql_fetch_assoc($r_field) ){
				// Limite le chargement à tous les articles enfants ou bien à liés aux options déjà choisi
				// En fonction de si l'option en cours de chargement correspond à la dernière options sélectionnée
				$use_table_child = $select_fld != $field['fld_id'] && count($ar_selected_child) ? $ar_selected_child : $ar_all_child;

				foreach( $use_table_child as $child ){
					$r_vals = fld_fields_get( $field['fld_id'], 0, 0, 0, 0, $child, null, [] );

					if( !ria_mysql_num_rows($r_vals) ){
						continue;
					}

					while( $vals = ria_mysql_fetch_assoc($r_vals) ){
						if( !isset($ar_options[ $vals['id'] ]) ){
							$ar_options[ $vals['id'] ] = [
								'id' 				=> $vals['id'],
								'name' 	 		=> $vals['name'],
								'selected' 	=> false,
								'values' 		=> []
							];
						}

						// Sélection par défaut la dernière valeur choisie
						if( $field['fld_id'] == $select_fld && $vals['obj_value'] == $val_fld ){
							$ar_options[ $vals['id'] ]['selected'] = $vals['obj_value'];
						}else{
							// Conserve la sélection des autres options si bien liés
							$link = fld_object_values_filter_objects( $ar_selected_child, $field['fld_id'], $vals['obj_value'], $multi_key=true );
							if( is_array($link) && count($link) ){
								if( isset($options[ $field['fld_id'] ]) && $options[ $field['fld_id'] ] == $vals['obj_value'] ){
									$ar_options[ $vals['id'] ]['selected'] = $vals['obj_value'];
								}
							}
						}

						if( $vals['obj_value'] != null ){

							if( $vals['type_id'] == FLD_TYPE_SELECT_HIERARCHY ){
								$ar_options[ $vals['id'] ]['values'][ $vals['obj_value'] ] = [
									'id' 		=> $vals['obj_value'],
									'name' 	=> fld_restricted_values_get_name( $vals['obj_value'] )
								];
							}else{
								$ar_options[ $vals['id'] ]['values'][ $vals['obj_value'] ] = [
									'id' 		=> $vals['obj_value'],
									'name' 	=> $vals['obj_value']
								];
							}
						}
					}
				}
			}

			// Vérifie qu'il y a des options
			if( count($ar_options) ){
				foreach($ar_options as $option){
					if( isset($option['values']) && is_array($option['values']) && count($option['values']) ){
						$has_fld_options = true;
						break;
					}
				}
			}
		}

		// Si pas d'options avec modèle de saisie
		if( !$has_fld_options ){
			$opt_type = 'childs';
			$ar_options = [];

			if( $r_child && ria_mysql_num_rows($r_child) ){
				$ar_options['childs'] = [
					'id' 				=> 'childs',
					'name' 			=> i18n::get( 'Déclinaisons', 'PRODUCT', i18n::getLang() ),
					'selected' 	=> false,
					'values' 		=> []
				];

				ria_mysql_data_seek( $r_child, 0 );

				while( $child = ria_mysql_fetch_assoc($r_child) ){
					if( $all_child_data ){
						$obj_child = new ProductService( ['prd' => $child['id']] );
						$obj_child->card();
						$data = $obj_child->getData();
					}else{
						$data = [
							'id' => $child['id'],
							'name' => $child['title']
						];
					}

					$ar_options['childs']['values'][] = $data;
				}
			}
		}

		if( count($ar_options) <= 0 ){
			return $this;
		}
		$this->options = ['type' => $opt_type, 'options' => $ar_options];

		// contrôle pour le tri des options
		if ( !is_array($multisort) || !isset($multisort['order']) || !isset($multisort['type']) ) {
			return $this;
		}

		// tri des options
		foreach ( $this->options['options'] as $key_type_option => $option ) {
			$name = array_column($option['values'], 'name'); // récupère les noms des options

			// tri les options sur les noms et associe les valeurs
			array_multisort($name, $multisort['order'], $multisort['type'], $option['values']);

			$this->options['options'][$key_type_option] = $option;
		}

		return $this;
	}

	/** Cette fonction permet de mettre à jour les options de déclinaisons.
	 * 	@param array $options Obligatoire, nouvelles options de déclinaisons
	 */
	public function setOptionsChart( $options ){
		if( !is_array($options) ){
			$options = [];
		}

		$this->options = $options;
	}

	/** Cette méthode permet de charger les produits enfants suivant les options sélectionnées
	 * @param	array	$options	Obligatoire, Tableau de.s option.s sélectionnée.s
	 * @param	bool	$bestprice	Optionnel, True pour récupérer le meilleur prix, false par défaut
	 * @param	array	$others		Optionnel, Tableau de.s paramétre.s complémentaire.s
	 * @return	object	L'objet en cours
	 */
	public function getChildren($options, $bestprice=false, $others=[]){
		global $hook;

		if( !$this->id ){
			throw new Exception( i18n::get('Aucun produit n\'est chargé.', 'ERROR', i18n::getLang()), 98 );
		}

		// Seuls les articles parent disposent de déclinaison
		if( !$this->getIsParent() ){
			return $this;
		}
		$fld = false;

		if( is_array($options) && count($options)){
			$fld = [];
			foreach($options as $fld_id => $fld_value){
				if( !is_numeric($fld_id) || !fld_fields_exists($fld_id) || $fld_value == ''){
					continue;
				}
				$fld[$fld_id] = $fld_value;

			}
			$fld = count($fld) ? $fld : false;
		}

		$params = [
			'childs'		=> true,
			'parent'		=> $this->id,
			'publish_prd'	=> true,
		];

		if( is_array($others) && isset($others['params']) && is_array($others['params']) ){
			$params = array_merge($params, $others['params']);
		}

		/** /!\ Le paramètre only_prd_ids à true ne gère pas le tri appliqué aux articles enfants
		 * @see https://riastudio.atlassian.net/browse/CHADMT-23
		 * @todo Remettre only_prd_ids à true par défaut et appliquer un filtre (hook) sur $params pour pouvoir surcharger only_prd_ids si le tri est important
		 */

		// Charge le tri appliqué aux articles enfants
		$ordered = prd_relations_order_get('childs');
		if( $ordered ){
			$sort_child = [ 'child_pos' => 'asc' ];
		}else{
			$sort_child = [ 'ref' => 'asc' ];
		}

		$children = prd_products_get_simple( 0, '', false, 0, false, $fld, false, $sort_child, $params );

		if( !ria_mysql_num_rows($children) ){
			return $this;
		}
		$this->children = new Collection();
		$load_images = is_array($others) && isset($others['images']) && is_bool($others['images']) ? $others['images'] : true;

		while($child = ria_mysql_fetch_assoc($children)){
			// Récupére les données du produit enfant via un hook
			$prd_data = $hook->apply_filter('ProductService_loadingOneChild', false, ['Product' => $this, 'child' => $child]);

			// Si le hook n'existe pas, n'a pas fonctionné ou n'a pas retourné de données
			if( !$prd_data ){

				try{

					// Instancie le produit
					$prd = new ProductService([
						'prd'			=> $child['id'],
						'withprice'		=> true,
						'relatedimages'	=> false
					]);

					// Charge le produit
					$prd->general();

					// Charge les informations avancés
					$prd->fields();

					// Récuère les images sur le produits
					if( $load_images ){
						$prd->images($this->cfg_img);
					}

					// Récupère les dates estimées de livraison
					$prd->delivery();

					if( Template::get('product-documents-download-active') ){
						$prd->documents();
					}
					$prd_data = $prd->getData();

				}catch( Exception $e ){}
			}

			// Ajout du produit à la collection
			if( $prd_data ){
				$this->children->addItem( $prd_data );
			}
		}

		if( !is_bool($bestprice) || !$bestprice ){
			return $this;
		}

		if( $this->children->length() <= 0 ){
			return $this;
		}

		// Remonte le meilleur prix
		foreach($this->children->getAll() as $prd){

			if( (float)$prd['priceht'] >= (float)$this->priceht ){
				continue;
			}
			$this->publicht		= $prd['publicht'];
			$this->publicttc	= $prd['publicttc'];
			$this->tvarate		= $prd['tvarate'];
			$this->ecotaxe		= $prd['ecotaxe'];

			$this->ispromo		= $prd['ispromo'];
			$this->promovalue	= $prd['promovalue'];
			$this->promovaluettc	= $prd['promovaluettc'];
			$this->priceht		= $prd['priceht'];
			$this->pricettc		= $prd['pricettc'];
			$this->origpriceht	= $prd['origpriceht'];
			$this->origpricettc	= $prd['origpricettc'];

		}

		return $this;

	}

	/** Cette fonction charge la nomenclature d'un produit.
	 * 	@return object L'objet courant
	 */
	public function nomenclature(){
		if( !$this->id ){
			throw new Exception( i18n::get('Aucun produit n\'est chargé.', 'ERROR', i18n::getLang()), 98 );
		}

		// Récupère le type de nomenclature
		$type = $this->getTypeNomenclature();
		if( !is_numeric($type) || $type <= 0 ){
			return $this;
		}

		$ar_nomenclature = new Collection();

		// Charge les options de nomenclature lié au produit
		$r_option = prd_nomenclatures_options_get( $this->id );

		if( $r_option ){
			while( $option = ria_mysql_fetch_assoc($r_option) ){
				// Pour chaque option, charge les articles composant cette option
				$r_opt_prd = prd_options_products_get( $option['opt'] );
				if( $r_opt_prd && ria_mysql_num_rows($r_opt_prd) ){
					$temp = new Collection();

					while( $opt_prd = ria_mysql_fetch_assoc($r_opt_prd) ){
						$opt = new ProductService(['prd' => $opt_prd['prd'], 'relatedimages' => false]);
						$opt->general();

						if( $this->with_related_images ){
							$opt->images($this->cfg_img);
							if( !($this->images instanceof Collection) || $this->images === null ){
								$this->images = new Collection();
							}
							$ar_img = $opt->getImages();

							if( count($ar_img) ){
								foreach( $ar_img as $img){
									$this->images->addItem($img);
								}
							}

						}
						$temp->addItem( $opt );
					}

					if( $temp->length() ){
							$ar_nomenclature->addItem( [
									'id'		=> $option['opt'],
									'name'		=> $option['opt-name'],
									'prds'		=> $temp
								], 'opt'.$option['opt']
							);
					}
				}
			}
		}

		if( $ar_nomenclature->length() ){
			$this->nomenclature = $ar_nomenclature;
		}

		return $this;
	}

	/** Cette fonction permet de charger les documents liés à un articles
	 * 	@param bool $group_by_type Optionnel, permet de regrouper les documents par type
	 * 	@return object L'objet courant
	 */
	public function documents( $group_by_type=false ){
		global $config;

		if( !gu_users_rights_used('_RGH_DOC_DWL') ){
			return $this;
		}

		// On récupère les documents
		$r_doc = doc_products_get( 0, $this->id, 0, ['doc_name' => 'asc'], $config['wst_id'], i18n::getLang() );

		if( !ria_mysql_num_rows($r_doc) ){
			return $this;
		}
		$this->documents = new Collection();

		while( $doc = ria_mysql_fetch_assoc($r_doc) ){
			try{
				$obj_doc = new Document( ['doc' => $doc['doc_id']] );
				$obj_doc->general();

				if( $group_by_type ){
					if( !$this->documents->exitsKey($doc['doc_type_id']) ){
						$this->documents->addItem( [
							'id'	=> $doc['doc_type_id'],
							'name'	=> $doc['doc_type_name'],
							'docs'	=> new Collection()
						], $doc['doc_type_id']);
					}
					$type = $this->documents->getItem( $doc['doc_type_id'] );
					$type['docs']->addItem( $obj_doc );
					continue;
				}

				$this->documents->addItem( $obj_doc );
			}catch( Exception $e ){
				// Exception non bloquante, un document qui n'existe pas ou plus ne bloque pas le chargement du produit
			}
		}
		return $this;
	}

	/** Cette fonction permet de charger la marque qui est lié à l'article.
	 * 	@return ProductService L'objet courant
	 */
	public function brand(){
		if( is_numeric($this->brandid) && $this->brandid > 0 ){
			$obj_brand = new BrandService([
				'brd'		=> $this->brandid,
				'published'	=> Template::get('product-published-brand')
			]);
			$this->brand = $obj_brand->general();
		}

		return $this;
	}

	/** Cette fonction permet de charger les informations de livraison du produit.
	 * 	Elle s'appuit sur le panier en cours.
	 * 	@return object L'instance courant
	 */
	public function delivery(){
		if( !$this->id || $this->delivery !== null){
			return $this;
		}
		// Récupération des informations de livraison pour ce produit
		$delivery = DeliveryService::getInstance();
		$delivery->setProduct( $this->id )->getDates();

		$dates = [];
		foreach( $delivery->getData() as $one_service ){
			// On recherche le service ayant la date de livraison la plus tôt
			if( !count($dates) || strtotime($dates['min']) > strtotime($one_service['dates']['min']) ){
				$dates = $one_service['dates'];
			}
		}

		$this->delivery = array_merge( $dates, ['franco' => $delivery->getFranco($this)] );

		return $this;
	}

	/** Cette fonction permet de charger les champs avancés liés à une page de contenu.
	 *  @return object L'instance en cours
	 */
	public function fields(){
		global $config, $hook;

		if( !$this->id || $this->fields !== null ){
			return $this;
		}
		$fld_ids = [];
		$fld_ids = $hook->apply_filter('ProductService_filterFields', $fld_ids);

		$this->fields = [];

		$sql = '
			select
				fld_id as id,
				fld_name as name,
				fld_pos,
				pv_value as obj_value,
				unit_symbol
			from fld_object_values
			join fld_fields
			on
				fld_tnt_id = '.$config['tnt_id'].'
			and fld_id = pv_fld_id
			and fld_cls_id = '.CLS_PRODUCT.'
			left join fld_units
			on
				unit_tnt_id='.$config['tnt_id'].'
			and fld_unit_id = unit_id
			where
				pv_tnt_id = '.$config['tnt_id'].'
			and pv_obj_id_0 = '.$this->id.'
			and pv_obj_id_1 = 0
			and pv_obj_id_2 = 0
			and fld_date_deleted is null
		';

		if( is_array($fld_ids) && count($fld_ids) ){
			$sql .= '
				and pv_fld_id in ('.implode(',', $fld_ids).')
			';
		}

		$r_fields = ria_mysql_query($sql);

		if( !ria_mysql_num_rows($r_fields) ){
			return $this;
		}

		while( $field = ria_mysql_fetch_assoc($r_fields) ){
			$this->fields[ 'field'.$field['id'] ] = [
				'id'		=> $field['id'],
				'name'		=> $field['name'],
				'value'		=> $field['obj_value'],
				'fldpos'	=> $field['fld_pos'],
				'unit'		=> $field['unit_symbol']
			];
		}

		return $this;
	}

	/** Cette fonction permet de charger les champs avancés liés à une fiche produit et destinés à l'affichage.
	 * 	Elle est pilotée par la variable de configuration fdv_prd_show_models comme pour Yuto
	 *  @return object L'instance en cours
	 */
	public function displayFields(){
		global $config;

		if( $this->displayFields !== null ){
			return $this;
		}

		$this->displayFields = [];

		if( $this->id && isset($config['fdv_prd_show_models']) && is_array($config['fdv_prd_show_models']) && count($config['fdv_prd_show_models']) > 0 ){

			$r_fields = ria_mysql_query('
				select fld_id as id, fld_name as name, fld_pos, pv_value as obj_value
				from fld_object_values
					join fld_fields on (fld_tnt_id = '.$config['tnt_id'].' and fld_id = pv_fld_id and fld_cls_id = '.CLS_PRODUCT.')
					join fld_model_fields on (mf_tnt_id = '.$config['tnt_id'].' and mf_fld_id = fld_id and mf_mdl_id in ('.implode( ', ', $config['fdv_prd_show_models'] ).'))
				where pv_tnt_id = '.$config['tnt_id'].'
					and pv_obj_id_0 = '.$this->id.' and pv_obj_id_1 = 0 and pv_obj_id_2 = 0
					and fld_date_deleted is null
			');

			if( $r_fields ){
				while( $field = ria_mysql_fetch_assoc($r_fields) ){
					$this->displayFields[ 'field'.$field['id'] ] = [
						'id' => $field['id'],
						'name' => $field['name'],
						'value' => $field['obj_value'],
					];
				}
			}
		}

		return $this;
	}

	/** Cette fonction permet de charger l'insccription à l'alerte de disponibilité.
	 * 	@return object L'objet courant
	 */
	public function alert(){
		global $config;

		$user = CustomerService::getInstance();
		if( !$user->isConnected() ){
			return $this;
		}

		$this->alertstock = gu_livr_alerts_exists( $user->getEmail(), $this->id, $config['wst_id'] );
		return $this;
	}

	/** Cette fonction permet de récupérer la propriété "id" du produit.
	 * 	@return float La proriété "id"
	 */
	public function getID(){
		return $this->id;
	}

	/** Cette fonction permet de récupérer la propriété "bookmark" du produit.
	 * 	@return float La proriété "bookmark"
	 */
	public function getBookmark(){
		return $this->bookmark;
	}

	/** Cette fonction permet de récupérer la propriété "priceht" du produit.
	 * 	@return float La proriété "priceht"
	 */
	public function getPriceHT(){
		return $this->priceht;
	}

	/** Cette fonction permet de récupérer la propriété "followstock" du produit.
	 * 	@return float La proriété "followstock"
	 */
	public function getFollowStock(){
		return $this->followstock;
	}

	/** Cette fonction permet de récupérer la propriété "stock" du produit.
	 * 	@return float La proriété "stock"
	 */
	public function getStock(){
		return $this->stock;
	}

	/** Cette fonction permet de modifier la propriété "stock" du produit.
	 *	@param float|int $stock Optionnel, nouvelle quantité en stock (peut-être égale à null)
	 *	@return ProductService L'objet courant
	 */
	public function setStock( $stock=null ){
		if( $stock === null || is_numeric($stock) ){
			$this->stock = $stock;
		}

		return $this;
	}

	/** Cette fonction permet de récupérer la propriété "pricettc" du produit.
	 * 	@return float La proriété "pricettc"
	 */
	public function getPriceTTC(){
		return $this->pricettc;
	}

	/** Cette fonction permet de récupérer la propriété "origpriceht" du produit.
	 * 	@return float La proriété "origpriceht"
	 */
	public function getOriginalPriceHT(){
		return $this->origpriceht;
	}

	/** Cette fonction permet de récupérer la propriété "origpricettc" du produit.
	 * 	@return float La proriété "origpricettc"
	 */
	public function getOriginalPriceTTC(){
		return $this->origpricettc;
	}

	/** Cette fonction permet de récupérer la propriété "sellunit" du produit.
	 * 	@return float La proriété "sellunit"
	 */
	public function getSellUnit(){
		return $this->sellunit;
	}

	/** Cette fonction permet de récupérer la propriété "weight" du produit.
	 * 	@return float La proriété "weight"
	 */
	public function getWeight(){
		return $this->weight;
	}

	/** Cette fonction permet de récupérer la propriété "weightnet" du produit.
	 * 	@return float La proriété "weightnet"
	 */
	public function getWeightNet(){
		return $this->weightnet;
	}

	/** Cette fonction permet de connaitre le type de nomenclature du produit
	 * 	@return int L'attribut "typenomenclature" du produit
	 */
	public function getTypeNomenclature(){
		if( is_null($this->typenomenclature) ){
			$type = prd_products_get_nomenclature_type( $this->id );
			$this->typenomenclature = in_array( $type, [NM_TYP_VARIABLE] ) ? $type : 0;
		}

		return $this->typenomenclature;
	}

	/** Cette fonction permet de récupérer la propriété "url" du produit.
	 * 	@return int L'attribut "url" du produit
	 */
	public function getURL(){
		return $this->url;
	}

	/** Cette méthode permet de récupérer la propriété "images" du produit.
	 * 	@return array Un tableau contenant les images du produit
	 */
	public function getImages(){

		if( $this->images instanceof Collection ){
			return $this->images->getAll();
		}

		if( is_array($this->images) ){
			return $this->images;
		}
		return [];
	}

	/** Cette fonction permet de récupérer la propriété "fields" du produit au format tableau.
	 * 	@return array Un tableau des champs avancés du produit.
	 */
	public function getFields(){
		return $this->fields;
	}

	/** Cette fonction permet de charger le fil d'Ariane du produit.
	 * 	@param $cat_id Optionnel, permet de préciser dans quelle catégorie l'on se trouve
	 * 	@return ProductService L'objet courant
	 */
	public function breadcrumbs( $cat_id=0 ){
		global $config;

		if( is_numeric($this->id) && $this->id > 0 && $this->breadcrumbs === null ){
			$this->breadcrumbs = new Collection();

			// Si la catégorie n'est pas renseigné, on charge la première publiée où se trouve le produit (celle canonique en prioritée)
			if( !is_numeric($cat_id) || $cat_id <= 0 || $cat_id == $config['cat_root'] ){
				$r_category = prd_products_categories_get( $this->id, true, true, null, $config['cat_root'] );

				if( $r_category && ria_mysql_num_rows($r_category) ){
					while( $category = ria_mysql_fetch_assoc($r_category) ){
						if( $category['cat'] == $config['cat_root'] ){
							continue;
						}

						if( $category['url_is_canonical'] ){
							$cat_id = $category['cat'];
							break;
						}elseif( !$cat_id || $cat_id == $config['cat_root'] ){
							$cat_id = $category['cat'];
						}
					}
				}
			}

			if( is_numeric($cat_id) && $cat_id > 0 ){
				// Charge les inforamtions de la catégorie + son fil d'Ariane
				$category = new CategoryService( [], ['cat' => $cat_id] );
				$category->general()->breadcrumbs();

				$cat_breadcrumbs = $category->getBreadcrumbs();
				if( count($cat_breadcrumbs) ){
					foreach( $cat_breadcrumbs as $one_breadcrumbs ){
						$this->breadcrumbs->addItem( $one_breadcrumbs );
					}
				}

				// Ajoute à la fin la catégorie sur laquelle on se trouve
				$this->breadcrumbs->addItem([
					'id' => $category->getID(),
					'url' => $category->getUrl(),
					'text' => $category->getTitle()
				]);
			}
		}

		return $this;
	}

	/** Cette fonction permet de savoir si un produit est un article parent.
	 *  @return bool True si s'est le cas, False dans le cas contraire
	 */
	public function getIsParent(){
		global $hook;

		if( is_null($this->isparent) ){
			$this->isparent = prd_products_is_parent($this->id) ? 'yes' : 'no';
		}

		// Hook : surcharge l'information pour savoir s'il s'agit d'un parent ou non
		$hook->do_action('ProductService_getIsParent', ['prd' => $this] );

		return $this->isparent === 'yes';
	}

	/** Cette fonction permt de mettre à jour l'information déterminant si un produit est un article parent.
	 * 	@param string $isparent Obligatoire, 'yes' lors qu'il s'agit d'un parent, 'no' dans le cas contraire
	 */
	public function setIsParent( $isparent ){
		if( !in_array($isparent, ['yes', 'no']) ){
			$isparent = 'no';
		}

		$this->isparent = $isparent;
	}

	/** Cette fonction permet de mettre à jour la propriété 'stocklivr' du produit.
	 * 	@param string $date Obligatoire, date de réapprovionnement (peut-être vide)
	 */
	public function setStockLivr( $date ){
		$this->stocklivr = $date;
	}

	/** Cette fonction permet de charger le tarif public.
	 * 	@return ProductService L'objet courant
	 */
	public function loadPublicPrice(){

		if( $this->id ){
			$colisage = [ 'col_id' => 0 ];

			// Désactive la récupération des tarifs quand l'utilisateur n'est pas connecté
			if( Template::get('catalog-show-price-connected') ){
				if( !CustomerService::getInstance()->isConnected() ){
					return $this;
				}
			}

			if( Template::get('catalog-only-price-colisage') ){
				$r_colisage = prd_colisage_classify_get( 0, $this->id, 0, ['qte' => 'asc'] );
				if( $r_colisage && ria_mysql_num_rows($r_colisage) ){
					$colisage = ria_mysql_fetch_assoc( $r_colisage );
				}
			}

			// Charge le tarif des articles enfants
			if( $this->options['type'] == 'childs' ){
				foreach( $this->options['options']['childs']['values'] as $key => $val ){
					$price = prd_products_get_price_array( $val['id'], 0, 0, 0, 1, $colisage['col_id'], false, false );
					if( ria_array_key_exists(['price_ht', 'tva_rate', 'price_ttc'], $price) ){
						$this->options['options']['childs']['values'][ $key ]['publicht'] = $price['price_ht'];
						$this->options['options']['childs']['values'][ $key ]['publicttc'] = $price['price_ttc'];
					}
				}
			}else{
				// Charge le tarif de l'article
				$price = prd_products_get_price_array( $this->id, 0, 0, 0, 1, $colisage['col_id'], false, false );
				if( ria_array_key_exists(['price_ht', 'tva_rate', 'price_ttc'], $price) ){
					$this->publicht = $price['price_ht'];
					$this->publicttc = $price['price_ttc'];
				}
			}
		}

		return $this;
	}

	/** Cette fonction permet de mettre à jour le tarif HT d'un produit.
	 * 	Elle accepte un numérique ou un tableau contenant 'value' et 'currency'.
	 */
	public function setPriceHT( $price_ht ){
		if( !is_numeric($price_ht) ){
			if( !is_array($price_ht) || !ria_array_key_exists(['value', 'currency'], $price_ht) ){
				return false;
			}

			if( !is_numeric($price_ht['value']) ){
				return false;
			}
		}

		$this->priceht = $price_ht;
	}

	/** Permet de surcharger ou définir le prix ht, la tva et le prix ttc du produit
	 * @param	float		$price_ht	Obligatoire, valeur hors taxe du produit
	 * @param	float		$tva_rate	Obligatoire, taux de tva
	 * @param	bool|float	$price_ttc	Optionnel, valeur toutes taxes comprises du produit, si la valeur n'est pas numérique, le prix ttc se calculera grâce au prix ht et la tva
	 * @return	bool		True en cas de succès, false sinon
	 */
	public function setPrice($price_ht, $tva_rate, $price_ttc=false){

		if( !is_numeric($tva_rate) || !is_numeric($price_ht) ){
			return false;
		}
		$this->priceht = $price_ht;
		$this->tvarate = $tva_rate;
		$this->pricettc = is_numeric($price_ttc) ? $price_ttc : ($price_ht * $tva_rate);

		return true;

	}

	/**	Permet de savoir si les tarifs sont chargés pour le produit
	 * @return	bool	True si les tarifs sont chargés, false sinon
	 */
	public function isLoadingPrices(){
		return is_bool($this->with_price) ? $this->with_price : false;
	}

	/** Cette fonction permet de charger le tarif complet d'un article (promotion compris).
	 *	@return ProductService L'objet courant
	 */
	private function loadPrice(){
		global $config, $hook;

		// Charge les informations du compte client connecté
		$global_user = CustomerService::getInstance();

		// Désactive la récupération des tarifs quand l'utilisateur n'est pas connecté
		if( Template::get('catalog-show-price-connected') ){
			if( !$global_user->isConnected() ){
				return $this;
			}
		}

		// Hook : remplace la fonction loadPrice
		if( $hook->do_action('ProductService_replaceLoadPrice', ['prd' => $this, 'user' => $global_user]) ){
			return $this;
		}

		$colisage = [ 'col_id' => 0 ];

		if( Template::get('catalog-only-price-colisage') ){
			$r_colisage = prd_colisage_classify_get( 0, $this->id, 0, ['qte' => 'asc'] );
			if( $r_colisage && ria_mysql_num_rows($r_colisage) ){
				$colisage = ria_mysql_fetch_assoc( $r_colisage );
			}
		}

		$hook->do_action('ProductService_loadPrice', ['prd'	=> $this, 'user' => $global_user]);

		$prd_id = [$this->id];

		$load_childs_prices = $this->getIsParent();
		if( $this->orderable == 'yes' && $this->use_childs_price === false ){
			$load_childs_prices = false;
		}

		// Calcul du tarif de base du produit (si pas déjà calculé)
		if( is_null($this->priceht) || $this->priceht <= 0 ){
			if( !$load_childs_prices ){
				// Calcul du tarif s'il s'agit d'un article normal (non parent)
				$price = prd_products_get_price_array( $this->id, $global_user->getID(), $global_user->getPriceCategory(), 0, 1, $colisage['col_id'] );
				if( !ria_array_key_exists(['price_ht', 'tva_rate', 'price_ttc'], $price) ){
					throw new Exception('Le tarif du produit n\'est pas défini.');
				}
			}else{
				// Calcul du tarif s'il s'agit d'un article parent
				$price = prd_products_get_parent_price( $this->id, $global_user->getID(), $global_user->getPriceCategory(), 0, 1, $colisage['col_id'], false, Template::get('catalog-only-price-child-published') );
				if( !is_array($price) || !count($price) ){
					throw new Exception('Le tarif du produit n\'est pas défini.');
				}

				// Utilise l'identifiant de l'article enfant pour calcul s'il existe une remise sur ce produit
				$r_rel = prd_hierarchy_get( $this->id );
				if( $r_rel ){
					while( $rel = ria_mysql_fetch_assoc($r_rel) ){
						$prd_id[ $rel['child'] ] = $rel['child'];
					}
				}
			}

			$this->priceht = $price['price_ht'];
			$this->tvarate = $price['tva_rate'];
			$this->pricettc = $price['price_ttc'];
		}

		// Si le prix en promotion est déjà calculé
		if( !is_null($this->origpriceht) ){
			return $this;
		}

		// Prépare un tableau contenant le prix du produit afin de ne pas le recalcul lors de la recherche de promotion
		$ar_price = [
			'price_ht' => $this->priceht,
			'tva_rate' => $this->tvarate,
			'price_ttc' => $this->pricettc,
		];

		// On recherche une promotion sur le produit (il peut s'agit de l'article enfant ayant le plus petit tarif)
		$last_price = $this->priceht;
		foreach( $prd_id as $one_prd_id ){
			$user_id = 0;

			$user = CustomerService::getInstance();
			if( $user->isConnected() ){
				$user_id = $user->getID();
			}

			$info_promo = prc_promotions_get( $one_prd_id, $user_id, 0, 1, 0, (!$load_childs_prices ? $ar_price : null), true, true );
			if( trim($info_promo['price_ht']) == '' ){
				continue;
			}


			if( $info_promo['price_ht'] > $last_price ){
				continue;
			}

			$last_price = $info_promo['price_ht'];

			if( is_array($info_promo) ){
				// On bloque la promotion s'il s'agit d'une promotion spéciale accessible uniquement à certains clients
				if( !gu_users_is_connected() && array_key_exists('cod_id', $info_promo) ){
					if( !pmt_codes_get_all_customers($info_promo['cod_id']) ){
						$info_promo = false;
					}
				}

				// Si la promotion est toujours applicable après le contrôle sur le compte client
				if( is_array($info_promo) ){
					// Hack obligatoire dans le cas d'une remise spéciale
					// Les identifiant de type de remise ne sont pas les mêmes que pour les promotions sur produits
					if( array_key_exists('cod_id', $info_promo) ){
						switch( $info_promo['type'] ){
							case 0 : $info_promo['type'] = 3; break;
							case 1 : $info_promo['type'] = 2; break;
							case 2 : $info_promo['type'] = 1; break;
						}
					}

					// Charge les informations de promotion sur le produit
					switch ($info_promo['type']) {
						case 1:
						case 3:
							$promo_value = number_format( $info_promo['origin_price_ht'] - $info_promo['price_ht'], 2, ',', ' ');
							$promo_value = str_replace( ',00', '', $promo_value );

							$promo_value_ttc = number_format( $info_promo['origin_price_ttc'] - $info_promo['price_ttc'], 2, ',', ' ');
							$promo_value_ttc = str_replace( ',00', '', $promo_value_ttc );

							$this->ispromo 				= true;
							$this->promovalue 		= "- ".$promo_value." €";
							$this->promovaluettc 		= "- ".$promo_value_ttc." €";
							$this->priceht 				= $info_promo['price_ht'];
							$this->pricettc 			= $info_promo['price_ttc'];
							$this->origpriceht 		= $info_promo['origin_price_ht'];
							$this->origpricettc 	= $info_promo['origin_price_ttc'];
							break;
						case 2:
							$val_remise = number_format( $info_promo['value'], 2, ',', ' ' );
							$val_remise = str_replace( ',00', '', $val_remise );

							$this->ispromo 				= true;
							$this->promovalue 		= "- ".$val_remise." %";
							$this->promovaluettc 		= "- ".$val_remise." %";
							$this->priceht 				= $info_promo['price_ht'];
							$this->pricettc 			= $info_promo['price_ttc'];
							$this->origpriceht 		= $info_promo['origin_price_ht'];
							$this->origpricettc 	= $info_promo['origin_price_ttc'];
					}
				}

				if( !count($this->promotions) ){
					$this->promotions( $one_prd_id );
				}

				break;
			}

			if( !count($this->promotions) ){
				$this->promotions();
			}
		}

		return $this;
	}

	/** Cette fonction permet de charger les promotions spéciales.
	 * 	Uniquement accessible si le tarif est chargé
	 * 	@return ProductService L'objet courant
	 */
	private function promotions( $child_id=0 ){
		if( trim($this->promovalue) != '' ){
			$this->promotions[] = $this->promovalue;
		}

		// Recherche des promotions spéciales
		if( !$this->nopromo ){
			$promotions = PromotionsService::getInstance();
			$promotions->fields();

			$get_prd = $this->id;
			if( is_numeric($child_id) && $child_id > 0 ){
				$get_prd = $child_id;
			}

			$this->promotions = $this->promotions + $promotions->labelsProduct( $get_prd );
			$this->promotionsDetails = $promotions->get( $get_prd );
		}

		if( count($this->promotions) > 0 ){
			$this->ispromo = true;
		}
	}
}