Array
(
    [Container%s/removed-ids.php] => <?php

return [
    'Psr\\Container\\ContainerInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ContainerInterface' => true,
    'configurator_service' => true,
    'configurator_service_simple' => true,
    'decorated.pif-pouf' => true,
    'decorator_service.inner' => true,
    'factory_simple' => true,
    'inlined' => true,
    'new_factory' => true,
    'tagged_iterator_foo' => true,
];

    [Container%s/getBazService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'baz' shared service.

$this->services['baz'] = $instance = new \Baz();

$instance->setFoo(${($_ = isset($this->services['foo_with_inline']) ? $this->services['foo_with_inline'] : $this->load('getFooWithInlineService.php')) && false ?: '_'});

return $instance;

    [Container%s/getConfiguredServiceService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'configured_service' shared service.

$this->services['configured_service'] = $instance = new \stdClass();

$a = new \ConfClass();
$a->setFoo(${($_ = isset($this->services['baz']) ? $this->services['baz'] : $this->load('getBazService.php')) && false ?: '_'});

$a->configureStdClass($instance);

return $instance;

    [Container%s/getConfiguredServiceSimpleService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'configured_service_simple' shared service.

$this->services['configured_service_simple'] = $instance = new \stdClass();

(new \ConfClass('bar'))->configureStdClass($instance);

return $instance;

    [Container%s/getDecoratorServiceService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'decorator_service' shared service.

return $this->services['decorator_service'] = new \stdClass();

    [Container%s/getDecoratorServiceWithNameService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'decorator_service_with_name' shared service.

return $this->services['decorator_service_with_name'] = new \stdClass();

    [Container%s/getDeprecatedServiceService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'deprecated_service' shared service.

@trigger_error('The "deprecated_service" service is deprecated. You should stop using it, as it will soon be removed.', E_USER_DEPRECATED);

return $this->services['deprecated_service'] = new \stdClass();

    [Container%s/getFactoryServiceService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'factory_service' shared service.

return $this->services['factory_service'] = ${($_ = isset($this->services['foo.baz']) ? $this->services['foo.baz'] : $this->load('getFoo_BazService.php')) && false ?: '_'}->getInstance();

    [Container%s/getFactoryServiceSimpleService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'factory_service_simple' shared service.

return $this->services['factory_service_simple'] = ${($_ = isset($this->services['factory_simple']) ? $this->services['factory_simple'] : $this->load('getFactorySimpleService.php')) && false ?: '_'}->getInstance();

    [Container%s/getFactorySimpleService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'factory_simple' shared service.

@trigger_error('The "factory_simple" service is deprecated. You should stop using it, as it will soon be removed.', E_USER_DEPRECATED);

return $this->services['factory_simple'] = new \SimpleFactoryClass('foo');

    [Container%s/getFooService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'foo' shared service.

$a = ${($_ = isset($this->services['foo.baz']) ? $this->services['foo.baz'] : $this->load('getFoo_BazService.php')) && false ?: '_'};

$this->services['foo'] = $instance = \Bar\FooClass::getInstance('foo', $a, ['bar' => 'foo is bar', 'foobar' => 'bar'], true, $this);

$instance->foo = 'bar';
$instance->moo = $a;
$instance->qux = ['bar' => 'foo is bar', 'foobar' => 'bar'];
$instance->setBar(${($_ = isset($this->services['bar']) ? $this->services['bar'] : $this->getBarService()) && false ?: '_'});
$instance->initialize();
sc_configure($instance);

return $instance;

    [Container%s/getFoo_BazService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'foo.baz' shared service.

$this->services['foo.baz'] = $instance = \BazClass::getInstance();

\BazClass::configureStatic1($instance);

return $instance;

    [Container%s/getFooWithInlineService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'foo_with_inline' shared service.

$this->services['foo_with_inline'] = $instance = new \Foo();

$a = new \Bar();
$a->pub = 'pub';
$a->setBaz(${($_ = isset($this->services['baz']) ? $this->services['baz'] : $this->load('getBazService.php')) && false ?: '_'});

$instance->setBar($a);

return $instance;

    [Container%s/getLazyContextService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'lazy_context' shared service.

return $this->services['lazy_context'] = new \LazyContext(new RewindableGenerator(function () {
    yield 'k1' => ${($_ = isset($this->services['foo.baz']) ? $this->services['foo.baz'] : $this->load('getFoo_BazService.php')) && false ?: '_'};
    yield 'k2' => $this;
}, 2), new RewindableGenerator(function () {
    return new \EmptyIterator();
}, 0));

    [Container%s/getLazyContextIgnoreInvalidRefService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'lazy_context_ignore_invalid_ref' shared service.

return $this->services['lazy_context_ignore_invalid_ref'] = new \LazyContext(new RewindableGenerator(function () {
    yield 0 => ${($_ = isset($this->services['foo.baz']) ? $this->services['foo.baz'] : $this->load('getFoo_BazService.php')) && false ?: '_'};
}, 1), new RewindableGenerator(function () {
    return new \EmptyIterator();
}, 0));

    [Container%s/getMethodCall1Service.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'method_call1' shared service.

include_once ($this->targetDirs[0].'/Fixtures/includes/foo.php');

$this->services['method_call1'] = $instance = new \Bar\FooClass();

$instance->setBar(${($_ = isset($this->services['foo']) ? $this->services['foo'] : $this->load('getFooService.php')) && false ?: '_'});
$instance->setBar(NULL);
$instance->setBar((${($_ = isset($this->services['foo']) ? $this->services['foo'] : $this->load('getFooService.php')) && false ?: '_'}->foo() . (($this->hasParameter("foo")) ? ($this->getParameter("foo")) : ("default"))));

return $instance;

    [Container%s/getNewFactoryServiceService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'new_factory_service' shared service.

$a = new \FactoryClass();
$a->foo = 'bar';

$this->services['new_factory_service'] = $instance = $a->getInstance();

$instance->foo = 'bar';

return $instance;

    [Container%s/getServiceFromStaticMethodService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'service_from_static_method' shared service.

return $this->services['service_from_static_method'] = \Bar\FooClass::getInstance();

    [Container%s/getTaggedIteratorService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'tagged_iterator' shared service.

return $this->services['tagged_iterator'] = new \Bar(new RewindableGenerator(function () {
    yield 0 => ${($_ = isset($this->services['foo']) ? $this->services['foo'] : $this->load('getFooService.php')) && false ?: '_'};
    yield 1 => ${($_ = isset($this->services['tagged_iterator_foo']) ? $this->services['tagged_iterator_foo'] : ($this->services['tagged_iterator_foo'] = new \Bar())) && false ?: '_'};
}, 2));

    [Container%s/getTaggedIteratorFooService.php] => <?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'tagged_iterator_foo' shared service.

return $this->services['tagged_iterator_foo'] = new \Bar();

    [Container%s/ProjectServiceContainer.php] => <?php

namespace Container%s;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\DependencyInjection\Exception\InvalidArgumentException;
use Symfony\Component\DependencyInjection\Exception\LogicException;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;
use Symfony\Component\DependencyInjection\ParameterBag\FrozenParameterBag;

/**
 * This class has been auto-generated
 * by the Symfony Dependency Injection Component.
 *
 * @final since Symfony 3.3
 */
class ProjectServiceContainer extends Container
{
    private $buildParameters;
    private $containerDir;
    private $parameters = [];
    private $targetDirs = [];

    public function __construct(array $buildParameters = [], $containerDir = __DIR__)
    {
        $dir = $this->targetDirs[0] = \dirname($containerDir);
        for ($i = 1; $i <= 5; ++$i) {
            $this->targetDirs[$i] = $dir = \dirname($dir);
        }
        $this->buildParameters = $buildParameters;
        $this->containerDir = $containerDir;
        $this->parameters = $this->getDefaultParameters();

        $this->services = [];
        $this->syntheticIds = [
            'request' => true,
        ];
        $this->methodMap = [
            'bar' => 'getBarService',
            'foo_bar' => 'getFooBarService',
        ];
        $this->fileMap = [
            'baz' => 'getBazService.php',
            'configured_service' => 'getConfiguredServiceService.php',
            'configured_service_simple' => 'getConfiguredServiceSimpleService.php',
            'decorator_service' => 'getDecoratorServiceService.php',
            'decorator_service_with_name' => 'getDecoratorServiceWithNameService.php',
            'deprecated_service' => 'getDeprecatedServiceService.php',
            'factory_service' => 'getFactoryServiceService.php',
            'factory_service_simple' => 'getFactoryServiceSimpleService.php',
            'factory_simple' => 'getFactorySimpleService.php',
            'foo' => 'getFooService.php',
            'foo.baz' => 'getFoo_BazService.php',
            'foo_with_inline' => 'getFooWithInlineService.php',
            'lazy_context' => 'getLazyContextService.php',
            'lazy_context_ignore_invalid_ref' => 'getLazyContextIgnoreInvalidRefService.php',
            'method_call1' => 'getMethodCall1Service.php',
            'new_factory_service' => 'getNewFactoryServiceService.php',
            'service_from_static_method' => 'getServiceFromStaticMethodService.php',
            'tagged_iterator' => 'getTaggedIteratorService.php',
            'tagged_iterator_foo' => 'getTaggedIteratorFooService.php',
        ];
        $this->privates = [
            'factory_simple' => true,
            'tagged_iterator_foo' => true,
        ];
        $this->aliases = [
            'alias_for_alias' => 'foo',
            'alias_for_foo' => 'foo',
            'decorated' => 'decorator_service_with_name',
        ];
    }

    public function getRemovedIds()
    {
        return require $this->containerDir.\DIRECTORY_SEPARATOR.'removed-ids.php';
    }

    public function compile()
    {
        throw new LogicException('You cannot compile a dumped container that was already compiled.');
    }

    public function isCompiled()
    {
        return true;
    }

    public function isFrozen()
    {
        @trigger_error(sprintf('The %s() method is deprecated since Symfony 3.3 and will be removed in 4.0. Use the isCompiled() method instead.', __METHOD__), E_USER_DEPRECATED);

        return true;
    }

    protected function load($file, $lazyLoad = true)
    {
        return require $this->containerDir.\DIRECTORY_SEPARATOR.$file;
    }

    /**
     * Gets the public 'bar' shared service.
     *
     * @return \Bar\FooClass
     */
    protected function getBarService()
    {
        $a = ${($_ = isset($this->services['foo.baz']) ? $this->services['foo.baz'] : $this->load('getFoo_BazService.php')) && false ?: '_'};

        $this->services['bar'] = $instance = new \Bar\FooClass('foo', $a, $this->getParameter('foo_bar'));

        $a->configure($instance);

        return $instance;
    }

    /**
     * Gets the public 'foo_bar' service.
     *
     * @return \Bar\FooClass
     */
    protected function getFooBarService()
    {
        return new \Bar\FooClass(${($_ = isset($this->services['deprecated_service']) ? $this->services['deprecated_service'] : $this->load('getDeprecatedServiceService.php')) && false ?: '_'});
    }

    public function getParameter($name)
    {
        $name = (string) $name;
        if (isset($this->buildParameters[$name])) {
            return $this->buildParameters[$name];
        }
        if (!(isset($this->parameters[$name]) || isset($this->loadedDynamicParameters[$name]) || array_key_exists($name, $this->parameters))) {
            $name = $this->normalizeParameterName($name);

            if (!(isset($this->parameters[$name]) || isset($this->loadedDynamicParameters[$name]) || array_key_exists($name, $this->parameters))) {
                throw new InvalidArgumentException(sprintf('The parameter "%s" must be defined.', $name));
            }
        }
        if (isset($this->loadedDynamicParameters[$name])) {
            return $this->loadedDynamicParameters[$name] ? $this->dynamicParameters[$name] : $this->getDynamicParameter($name);
        }

        return $this->parameters[$name];
    }

    public function hasParameter($name)
    {
        $name = (string) $name;
        if (isset($this->buildParameters[$name])) {
            return true;
        }
        $name = $this->normalizeParameterName($name);

        return isset($this->parameters[$name]) || isset($this->loadedDynamicParameters[$name]) || array_key_exists($name, $this->parameters);
    }

    public function setParameter($name, $value)
    {
        throw new LogicException('Impossible to call set() on a frozen ParameterBag.');
    }

    public function getParameterBag()
    {
        if (null === $this->parameterBag) {
            $parameters = $this->parameters;
            foreach ($this->loadedDynamicParameters as $name => $loaded) {
                $parameters[$name] = $loaded ? $this->dynamicParameters[$name] : $this->getDynamicParameter($name);
            }
            foreach ($this->buildParameters as $name => $value) {
                $parameters[$name] = $value;
            }
            $this->parameterBag = new FrozenParameterBag($parameters);
        }

        return $this->parameterBag;
    }

    private $loadedDynamicParameters = [];
    private $dynamicParameters = [];

    /**
     * Computes a dynamic parameter.
     *
     * @param string $name The name of the dynamic parameter to load
     *
     * @return mixed The value of the dynamic parameter
     *
     * @throws InvalidArgumentException When the dynamic parameter does not exist
     */
    private function getDynamicParameter($name)
    {
        throw new InvalidArgumentException(sprintf('The dynamic parameter "%s" must be defined.', $name));
    }

    private $normalizedParameterNames = [];

    private function normalizeParameterName($name)
    {
        if (isset($this->normalizedParameterNames[$normalizedName = strtolower($name)]) || isset($this->parameters[$normalizedName]) || array_key_exists($normalizedName, $this->parameters)) {
            $normalizedName = isset($this->normalizedParameterNames[$normalizedName]) ? $this->normalizedParameterNames[$normalizedName] : $normalizedName;
            if ((string) $name !== $normalizedName) {
                @trigger_error(sprintf('Parameter names will be made case sensitive in Symfony 4.0. Using "%s" instead of "%s" is deprecated since Symfony 3.4.', $name, $normalizedName), E_USER_DEPRECATED);
            }
        } else {
            $normalizedName = $this->normalizedParameterNames[$normalizedName] = (string) $name;
        }

        return $normalizedName;
    }

    /**
     * Gets the default parameters.
     *
     * @return array An array of the default parameters
     */
    protected function getDefaultParameters()
    {
        return [
            'baz_class' => 'BazClass',
            'foo_class' => 'Bar\\FooClass',
            'foo' => 'bar',
        ];
    }
}

    [ProjectServiceContainer.php] => <?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\Container%s\ProjectServiceContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/Container%s/ProjectServiceContainer.php') {
    touch(__DIR__.'/Container%s.legacy');

    return;
}

if (!\class_exists(ProjectServiceContainer::class, false)) {
    \class_alias(\Container%s\ProjectServiceContainer::class, ProjectServiceContainer::class, false);
}

return new \Container%s\ProjectServiceContainer([
    'container.build_hash' => '%s',
    'container.build_id' => '%s',
    'container.build_time' => %d,
], __DIR__.\DIRECTORY_SEPARATOR.'Container%s');

)
