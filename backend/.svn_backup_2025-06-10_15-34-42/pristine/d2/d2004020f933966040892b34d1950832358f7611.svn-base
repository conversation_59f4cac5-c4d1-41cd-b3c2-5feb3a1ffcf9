<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/service_controller.proto

namespace Google\Api\Servicecontrol\V1\CheckResponse;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>google.api.servicecontrol.v1.CheckResponse.CheckInfo</code>
 */
class CheckInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * Consumer info of this check.
     *
     * Generated from protobuf field <code>.google.api.servicecontrol.v1.CheckResponse.ConsumerInfo consumer_info = 2;</code>
     */
    private $consumer_info = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Api\Servicecontrol\V1\CheckResponse\ConsumerInfo $consumer_info
     *           Consumer info of this check.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Servicecontrol\V1\ServiceController::initOnce();
        parent::__construct($data);
    }

    /**
     * Consumer info of this check.
     *
     * Generated from protobuf field <code>.google.api.servicecontrol.v1.CheckResponse.ConsumerInfo consumer_info = 2;</code>
     * @return \Google\Api\Servicecontrol\V1\CheckResponse\ConsumerInfo
     */
    public function getConsumerInfo()
    {
        return $this->consumer_info;
    }

    /**
     * Consumer info of this check.
     *
     * Generated from protobuf field <code>.google.api.servicecontrol.v1.CheckResponse.ConsumerInfo consumer_info = 2;</code>
     * @param \Google\Api\Servicecontrol\V1\CheckResponse\ConsumerInfo $var
     * @return $this
     */
    public function setConsumerInfo($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Servicecontrol\V1\CheckResponse_ConsumerInfo::class);
        $this->consumer_info = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(CheckInfo::class, \Google\Api\Servicecontrol\V1\CheckResponse_CheckInfo::class);

