<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 * (c) <PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Twig\Node\Expression;

use Twig\Node\Node;

/**
 * Abstract class for all nodes that represents an expression.
 *
 * <AUTHOR> <<EMAIL>>
 */
abstract class AbstractExpression extends Node
{
}

class_alias('Twig\Node\Expression\AbstractExpression', 'Twig_Node_Expression');
