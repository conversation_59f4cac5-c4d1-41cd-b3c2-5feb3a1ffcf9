{"name": "phpdocumentor/reflection-docblock", "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^5.6 || ^7.0", "phpdocumentor/reflection-common": "^1.0.0", "phpdocumentor/type-resolver": "^0.4.0", "webmozart/assert": "^1.0"}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "autoload-dev": {"psr-4": {"phpDocumentor\\Reflection\\": ["tests/unit"]}}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.4"}}