<?php
	
	$script_start = microtime(true);
	
	// Spécifier "PRODUCTION" en premier argument
	$test = isset($argv[1]) && strtoupper($argv[1]) == 'PRODUCTION' ? false : true;
	
	// Nombre de commandes traitées à chaque exécution (0 = pas de limite)
	$limit = 1000;
	if( $test ){
		$limit = 0;
	}
	
	set_include_path(dirname(__FILE__) . '/../include/');
	require_once('orders.inc.php');
	
	unset($config);
	
	// Gestion d'un tenant spécifique
	$tnt_id = isset($argv[2]) && is_numeric($argv[2]) && $argv[2] > 0 ? $argv[2] : 0;
	
	// Charge l'ensemble des configurations clients
	$configs = cfg_variables_get_all_tenants();
	if( !is_array($configs) || !sizeof($configs) ){
		return false;
	}
	
	// L'incrément est global
	$i = 0;
	
	foreach( $configs as $config ){
		
		if( $tnt_id > 0 && $config['tnt_id'] != $tnt_id ){
			continue;
		}
		
		// Commandes synchronisées puis virtuellement supprimées de plus de 3 mois
		// Pour lesquelles il existe une commande équivalente (même N° de pièce) non annulée
		$orders = ord_orders_get_sync_replaced( 90, true );
		
		if( !is_array($orders) ){
			error_log(__FILE__.' - Echec lors de la récupération des commandes du tenant '.$config['tnt_id']);
			continue;
		}elseif( $test ){
			print sizeof($orders).' commndes à supprimer (tenant '.$config['tnt_id'].').'."\n";
		}
		
		foreach( $orders as $ord_id ){
			
			if( $limit > 0 && $i >= $limit ){
				break;
			}
			
			if( $test ){
				print 'Commande '.$ord_id.' (tenant '.$config['tnt_id'].') à supprimer.'."\n";
			}else{
				// Suppression physique de la commande (et des lignes et relations)
				if( !ord_orders_del_sage( $ord_id, true ) ){
					error_log(__FILE__.' - Echec lors de la suppression de la commande '.$ord_id.' du tenant '.$config['tnt_id']);
				}else{
					print 'Commande '.$ord_id.' (tenant '.$config['tnt_id'].') supprimée.'."\n";
				}
			}
			
			$i++;
		}
	
	}
	
	print 'Fin du script (exécution en '.round(microtime(true) - $script_start, 3).' secondes)'."\n";
	
