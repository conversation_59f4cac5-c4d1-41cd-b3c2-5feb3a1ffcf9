{"canonicalName": "Pubsub", "documentationLink": "https://cloud.google.com/pubsub/docs", "icons": {"x32": "http://www.google.com/images/icons/product/search-32.gif", "x16": "http://www.google.com/images/icons/product/search-16.gif"}, "revision": "20200909", "kind": "discovery#restDescription", "version": "v1", "ownerDomain": "google.com", "resources": {"projects": {"resources": {"subscriptions": {"methods": {"create": {"request": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "response": {"$ref": "Subscription"}, "description": "Creates a subscription to a given topic. See the [resource name rules] (https://cloud.google.com/pubsub/docs/admin#resource_names). If the subscription already exists, returns `ALREADY_EXISTS`. If the corresponding topic doesn't exist, returns `NOT_FOUND`. If the name is not provided in the request, the server will assign a random name for this subscription on the same project as the topic, conforming to the [resource name format] (https://cloud.google.com/pubsub/docs/admin#resource_names). The generated name is populated in the returned Subscription object. Note that for REST API requests, you must specify a name in the request.", "httpMethod": "PUT", "parameters": {"name": {"pattern": "^projects/[^/]+/subscriptions/[^/]+$", "description": "Required. The name of the subscription. It must have the format `\"projects/{project}/subscriptions/{subscription}\"`. `{subscription}` must start with a letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters in length, and it must not start with `\"goog\"`.", "location": "path", "required": true, "type": "string"}}, "id": "pubsub.projects.subscriptions.create", "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}", "parameterOrder": ["name"]}, "delete": {"path": "v1/{+subscription}", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "httpMethod": "DELETE", "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}", "parameters": {"subscription": {"pattern": "^projects/[^/]+/subscriptions/[^/]+$", "location": "path", "description": "Required. The subscription to delete. Format is `projects/{project}/subscriptions/{sub}`.", "type": "string", "required": true}}, "response": {"$ref": "Empty"}, "id": "pubsub.projects.subscriptions.delete", "parameterOrder": ["subscription"], "description": "Deletes an existing subscription. All messages retained in the subscription are immediately dropped. Calls to `Pull` after deletion will return `NOT_FOUND`. After a subscription is deleted, a new one may be created with the same name, but the new one has no association with the old subscription or its topic unless the same topic is specified."}, "setIamPolicy": {"request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "httpMethod": "POST", "path": "v1/{+resource}:setIamPolicy", "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}:setIamPolicy", "parameterOrder": ["resource"], "description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "parameters": {"resource": {"pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "location": "path", "description": "REQUIRED: The resource for which the policy is being specified. See the operation documentation for the appropriate value for this field.", "type": "string"}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "id": "pubsub.projects.subscriptions.setIamPolicy"}, "seek": {"httpMethod": "POST", "description": "Seeks an existing subscription to a point in time or to a given snapshot, whichever is provided in the request. Snapshots are used in [Seek]( https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot. Note that both the subscription and the snapshot must be on the same topic.", "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}:seek", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "request": {"$ref": "SeekRequest"}, "parameters": {"subscription": {"location": "path", "description": "Required. The subscription to affect.", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "parameterOrder": ["subscription"], "path": "v1/{+subscription}:seek", "response": {"$ref": "SeekResponse"}, "id": "pubsub.projects.subscriptions.seek"}, "modifyAckDeadline": {"flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}:modifyAckDeadline", "httpMethod": "POST", "id": "pubsub.projects.subscriptions.modifyAckDeadline", "request": {"$ref": "ModifyAckDeadlineRequest"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "response": {"$ref": "Empty"}, "description": "Modifies the ack deadline for a specific message. This method is useful to indicate that more time is needed to process a message by the subscriber, or to make the message available for redelivery if the processing was interrupted. Note that this does not modify the subscription-level `ackDeadlineSeconds` used for subsequent messages.", "path": "v1/{+subscription}:modifyAckDeadline", "parameters": {"subscription": {"description": "Required. The name of the subscription. Format is `projects/{project}/subscriptions/{sub}`.", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string", "location": "path"}}, "parameterOrder": ["subscription"]}, "get": {"httpMethod": "GET", "description": "Gets the configuration details of a subscription.", "parameterOrder": ["subscription"], "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}", "id": "pubsub.projects.subscriptions.get", "response": {"$ref": "Subscription"}, "path": "v1/{+subscription}", "parameters": {"subscription": {"description": "Required. The name of the subscription to get. Format is `projects/{project}/subscriptions/{sub}`.", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "required": true, "location": "path", "type": "string"}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "testIamPermissions": {"scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "parameters": {"resource": {"type": "string", "location": "path", "required": true, "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "description": "REQUIRED: The resource for which the policy detail is being requested. See the operation documentation for the appropriate value for this field."}}, "httpMethod": "POST", "description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "id": "pubsub.projects.subscriptions.testIamPermissions", "response": {"$ref": "TestIamPermissionsResponse"}, "request": {"$ref": "TestIamPermissionsRequest"}, "path": "v1/{+resource}:testIamPermissions", "parameterOrder": ["resource"], "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}:testIamPermissions"}, "detach": {"response": {"$ref": "DetachSubscriptionResponse"}, "id": "pubsub.projects.subscriptions.detach", "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}:detach", "parameterOrder": ["subscription"], "path": "v1/{+subscription}:detach", "httpMethod": "POST", "parameters": {"subscription": {"required": true, "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "type": "string", "description": "Required. The subscription to detach. Format is `projects/{project}/subscriptions/{subscription}`."}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "description": "Detaches a subscription from this topic. All messages retained in the subscription are dropped. Subsequent `Pull` and `StreamingPull` requests will return FAILED_PRECONDITION. If the subscription is a push subscription, pushes to the endpoint will stop."}, "list": {"scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "parameters": {"pageSize": {"format": "int32", "type": "integer", "location": "query", "description": "Maximum number of subscriptions to return."}, "project": {"required": true, "type": "string", "location": "path", "description": "Required. The name of the project in which to list subscriptions. Format is `projects/{project-id}`.", "pattern": "^projects/[^/]+$"}, "pageToken": {"location": "query", "description": "The value returned by the last `ListSubscriptionsResponse`; indicates that this is a continuation of a prior `ListSubscriptions` call, and that the system should return the next page of data.", "type": "string"}}, "description": "Lists matching subscriptions.", "path": "v1/{+project}/subscriptions", "flatPath": "v1/projects/{projectsId}/subscriptions", "parameterOrder": ["project"], "id": "pubsub.projects.subscriptions.list", "response": {"$ref": "ListSubscriptionsResponse"}, "httpMethod": "GET"}, "getIamPolicy": {"path": "v1/{+resource}:getIamPolicy", "id": "pubsub.projects.subscriptions.getIamPolicy", "parameters": {"resource": {"location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "description": "REQUIRED: The resource for which the policy is being requested. See the operation documentation for the appropriate value for this field.", "required": true, "type": "string"}, "options.requestedPolicyVersion": {"format": "int32", "description": "Optional. The policy format version to be returned. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional bindings must specify version 3. Policies without any conditional bindings may specify any valid value or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "type": "integer", "location": "query"}}, "httpMethod": "GET", "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}:getIamPolicy", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "response": {"$ref": "Policy"}, "parameterOrder": ["resource"], "description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set."}, "acknowledge": {"httpMethod": "POST", "parameters": {"subscription": {"type": "string", "required": true, "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "location": "path", "description": "Required. The subscription whose message is being acknowledged. Format is `projects/{project}/subscriptions/{sub}`."}}, "request": {"$ref": "AcknowledgeRequest"}, "parameterOrder": ["subscription"], "response": {"$ref": "Empty"}, "path": "v1/{+subscription}:acknowledge", "id": "pubsub.projects.subscriptions.acknowledge", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "description": "Acknowledges the messages associated with the `ack_ids` in the `AcknowledgeRequest`. The Pub/Sub system can remove the relevant messages from the subscription. Acknowledging a message whose ack deadline has expired may succeed, but such a message may be redelivered later. Acknowledging a message more than once will not result in an error.", "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}:acknowledge"}, "pull": {"scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "parameterOrder": ["subscription"], "httpMethod": "POST", "path": "v1/{+subscription}:pull", "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}:pull", "id": "pubsub.projects.subscriptions.pull", "request": {"$ref": "PullRequest"}, "response": {"$ref": "PullResponse"}, "description": "Pulls messages from the server. The server may return `UNAVAILABLE` if there are too many concurrent pull requests pending for the given subscription.", "parameters": {"subscription": {"required": true, "type": "string", "location": "path", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "description": "Required. The subscription from which messages should be pulled. Format is `projects/{project}/subscriptions/{sub}`."}}}, "patch": {"description": "Updates an existing subscription. Note that certain properties of a subscription, such as its topic, are not modifiable.", "parameters": {"name": {"location": "path", "description": "Required. The name of the subscription. It must have the format `\"projects/{project}/subscriptions/{subscription}\"`. `{subscription}` must start with a letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters in length, and it must not start with `\"goog\"`.", "required": true, "type": "string", "pattern": "^projects/[^/]+/subscriptions/[^/]+$"}}, "path": "v1/{+name}", "parameterOrder": ["name"], "httpMethod": "PATCH", "id": "pubsub.projects.subscriptions.patch", "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}", "response": {"$ref": "Subscription"}, "request": {"$ref": "UpdateSubscriptionRequest"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "modifyPushConfig": {"parameterOrder": ["subscription"], "id": "pubsub.projects.subscriptions.modifyPushConfig", "description": "Modifies the `PushConfig` for a specified subscription. This may be used to change a push subscription to a pull one (signified by an empty `PushConfig`) or vice versa, or change the endpoint URL and other attributes of a push subscription. Messages will accumulate for delivery continuously through the call regardless of changes to the `PushConfig`.", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "path": "v1/{+subscription}:modifyPushConfig", "parameters": {"subscription": {"type": "string", "required": true, "description": "Required. The name of the subscription. Format is `projects/{project}/subscriptions/{sub}`.", "pattern": "^projects/[^/]+/subscriptions/[^/]+$", "location": "path"}}, "httpMethod": "POST", "request": {"$ref": "ModifyPushConfigRequest"}, "flatPath": "v1/projects/{projectsId}/subscriptions/{subscriptionsId}:modifyPushConfig"}}}, "topics": {"resources": {"snapshots": {"methods": {"list": {"scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "id": "pubsub.projects.topics.snapshots.list", "response": {"$ref": "ListTopicSnapshotsResponse"}, "parameterOrder": ["topic"], "flatPath": "v1/projects/{projectsId}/topics/{topicsId}/snapshots", "parameters": {"topic": {"description": "Required. The name of the topic that snapshots are attached to. Format is `projects/{project}/topics/{topic}`.", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "location": "path", "type": "string"}, "pageSize": {"type": "integer", "location": "query", "description": "Maximum number of snapshot names to return.", "format": "int32"}, "pageToken": {"location": "query", "type": "string", "description": "The value returned by the last `ListTopicSnapshotsResponse`; indicates that this is a continuation of a prior `ListTopicSnapshots` call, and that the system should return the next page of data."}}, "description": "Lists the names of the snapshots on this topic. Snapshots are used in [Seek](https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot.", "path": "v1/{+topic}/snapshots", "httpMethod": "GET"}}}, "subscriptions": {"methods": {"list": {"httpMethod": "GET", "response": {"$ref": "ListTopicSubscriptionsResponse"}, "parameters": {"pageSize": {"type": "integer", "description": "Maximum number of subscription names to return.", "format": "int32", "location": "query"}, "topic": {"type": "string", "description": "Required. The name of the topic that subscriptions are attached to. Format is `projects/{project}/topics/{topic}`.", "location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true}, "pageToken": {"type": "string", "location": "query", "description": "The value returned by the last `ListTopicSubscriptionsResponse`; indicates that this is a continuation of a prior `ListTopicSubscriptions` call, and that the system should return the next page of data."}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "parameterOrder": ["topic"], "id": "pubsub.projects.topics.subscriptions.list", "path": "v1/{+topic}/subscriptions", "description": "Lists the names of the attached subscriptions on this topic.", "flatPath": "v1/projects/{projectsId}/topics/{topicsId}/subscriptions"}}}}, "methods": {"setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "parameterOrder": ["resource"], "flatPath": "v1/projects/{projectsId}/topics/{topicsId}:setIamPolicy", "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "parameters": {"resource": {"required": true, "type": "string", "description": "REQUIRED: The resource for which the policy is being specified. See the operation documentation for the appropriate value for this field.", "pattern": "^projects/[^/]+/topics/[^/]+$", "location": "path"}}, "id": "pubsub.projects.topics.setIamPolicy", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "response": {"$ref": "Policy"}, "httpMethod": "POST"}, "publish": {"parameters": {"topic": {"pattern": "^projects/[^/]+/topics/[^/]+$", "location": "path", "required": true, "description": "Required. The messages in the request will be published on this topic. Format is `projects/{project}/topics/{topic}`.", "type": "string"}}, "parameterOrder": ["topic"], "flatPath": "v1/projects/{projectsId}/topics/{topicsId}:publish", "request": {"$ref": "PublishRequest"}, "id": "pubsub.projects.topics.publish", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "description": "Adds one or more messages to the topic. Returns `NOT_FOUND` if the topic does not exist.", "httpMethod": "POST", "path": "v1/{+topic}:publish", "response": {"$ref": "PublishResponse"}}, "list": {"description": "Lists matching topics.", "flatPath": "v1/projects/{projectsId}/topics", "response": {"$ref": "ListTopicsResponse"}, "id": "pubsub.projects.topics.list", "parameters": {"pageSize": {"type": "integer", "format": "int32", "location": "query", "description": "Maximum number of topics to return."}, "project": {"required": true, "description": "Required. The name of the project in which to list topics. Format is `projects/{project-id}`.", "pattern": "^projects/[^/]+$", "location": "path", "type": "string"}, "pageToken": {"description": "The value returned by the last `ListTopicsResponse`; indicates that this is a continuation of a prior `ListTopics` call, and that the system should return the next page of data.", "location": "query", "type": "string"}}, "parameterOrder": ["project"], "httpMethod": "GET", "path": "v1/{+project}/topics", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "patch": {"flatPath": "v1/projects/{projectsId}/topics/{topicsId}", "path": "v1/{+name}", "parameters": {"name": {"location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "type": "string", "description": "Required. The name of the topic. It must have the format `\"projects/{project}/topics/{topic}\"`. `{topic}` must start with a letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters in length, and it must not start with `\"goog\"`."}}, "description": "Updates an existing topic. Note that certain properties of a topic are not modifiable.", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "parameterOrder": ["name"], "request": {"$ref": "UpdateTopicRequest"}, "httpMethod": "PATCH", "id": "pubsub.projects.topics.patch", "response": {"$ref": "Topic"}}, "get": {"parameters": {"topic": {"type": "string", "location": "path", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "description": "Required. The name of the topic to get. Format is `projects/{project}/topics/{topic}`."}}, "description": "Gets the configuration of a topic.", "flatPath": "v1/projects/{projectsId}/topics/{topicsId}", "id": "pubsub.projects.topics.get", "parameterOrder": ["topic"], "httpMethod": "GET", "response": {"$ref": "Topic"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "path": "v1/{+topic}"}, "delete": {"httpMethod": "DELETE", "parameterOrder": ["topic"], "id": "pubsub.projects.topics.delete", "description": "Deletes the topic with the given name. Returns `NOT_FOUND` if the topic does not exist. After a topic is deleted, a new topic may be created with the same name; this is an entirely new topic with none of the old configuration or subscriptions. Existing subscriptions to this topic are not deleted, but their `topic` field is set to `_deleted-topic_`.", "parameters": {"topic": {"description": "Required. Name of the topic to delete. Format is `projects/{project}/topics/{topic}`.", "type": "string", "pattern": "^projects/[^/]+/topics/[^/]+$", "required": true, "location": "path"}}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "path": "v1/{+topic}", "flatPath": "v1/projects/{projectsId}/topics/{topicsId}"}, "getIamPolicy": {"path": "v1/{+resource}:getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"pattern": "^projects/[^/]+/topics/[^/]+$", "type": "string", "location": "path", "required": true, "description": "REQUIRED: The resource for which the policy is being requested. See the operation documentation for the appropriate value for this field."}, "options.requestedPolicyVersion": {"format": "int32", "location": "query", "description": "Optional. The policy format version to be returned. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional bindings must specify version 3. Policies without any conditional bindings may specify any valid value or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "type": "integer"}}, "id": "pubsub.projects.topics.getIamPolicy", "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "response": {"$ref": "Policy"}, "description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/topics/{topicsId}:getIamPolicy"}, "create": {"response": {"$ref": "Topic"}, "path": "v1/{+name}", "flatPath": "v1/projects/{projectsId}/topics/{topicsId}", "parameterOrder": ["name"], "description": "Creates the given topic with the given name. See the [resource name rules]( https://cloud.google.com/pubsub/docs/admin#resource_names).", "httpMethod": "PUT", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "id": "pubsub.projects.topics.create", "parameters": {"name": {"required": true, "pattern": "^projects/[^/]+/topics/[^/]+$", "location": "path", "description": "Required. The name of the topic. It must have the format `\"projects/{project}/topics/{topic}\"`. `{topic}` must start with a letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters in length, and it must not start with `\"goog\"`.", "type": "string"}}, "request": {"$ref": "Topic"}}, "testIamPermissions": {"path": "v1/{+resource}:testIamPermissions", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See the operation documentation for the appropriate value for this field.", "location": "path", "required": true, "pattern": "^projects/[^/]+/topics/[^/]+$", "type": "string"}}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "response": {"$ref": "TestIamPermissionsResponse"}, "request": {"$ref": "TestIamPermissionsRequest"}, "httpMethod": "POST", "flatPath": "v1/projects/{projectsId}/topics/{topicsId}:testIamPermissions", "parameterOrder": ["resource"], "description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "id": "pubsub.projects.topics.testIamPermissions"}}}, "snapshots": {"methods": {"patch": {"parameters": {"name": {"pattern": "^projects/[^/]+/snapshots/[^/]+$", "description": "The name of the snapshot.", "location": "path", "type": "string", "required": true}}, "response": {"$ref": "Snapshot"}, "path": "v1/{+name}", "id": "pubsub.projects.snapshots.patch", "description": "Updates an existing snapshot. Snapshots are used in Seek operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot.", "httpMethod": "PATCH", "flatPath": "v1/projects/{projectsId}/snapshots/{snapshotsId}", "request": {"$ref": "UpdateSnapshotRequest"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "parameterOrder": ["name"]}, "setIamPolicy": {"id": "pubsub.projects.snapshots.setIamPolicy", "path": "v1/{+resource}:setIamPolicy", "response": {"$ref": "Policy"}, "httpMethod": "POST", "request": {"$ref": "SetIamPolicyRequest"}, "parameterOrder": ["resource"], "parameters": {"resource": {"required": true, "location": "path", "type": "string", "description": "REQUIRED: The resource for which the policy is being specified. See the operation documentation for the appropriate value for this field.", "pattern": "^projects/[^/]+/snapshots/[^/]+$"}}, "flatPath": "v1/projects/{projectsId}/snapshots/{snapshotsId}:setIamPolicy", "description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"]}, "get": {"parameterOrder": ["snapshot"], "parameters": {"snapshot": {"pattern": "^projects/[^/]+/snapshots/[^/]+$", "required": true, "type": "string", "description": "Required. The name of the snapshot to get. Format is `projects/{project}/snapshots/{snap}`.", "location": "path"}}, "flatPath": "v1/projects/{projectsId}/snapshots/{snapshotsId}", "response": {"$ref": "Snapshot"}, "id": "pubsub.projects.snapshots.get", "description": "Gets the configuration details of a snapshot. Snapshots are used in Seek operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot.", "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "path": "v1/{+snapshot}"}, "testIamPermissions": {"request": {"$ref": "TestIamPermissionsRequest"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "parameters": {"resource": {"pattern": "^projects/[^/]+/snapshots/[^/]+$", "description": "REQUIRED: The resource for which the policy detail is being requested. See the operation documentation for the appropriate value for this field.", "location": "path", "type": "string", "required": true}}, "id": "pubsub.projects.snapshots.testIamPermissions", "flatPath": "v1/projects/{projectsId}/snapshots/{snapshotsId}:testIamPermissions", "response": {"$ref": "TestIamPermissionsResponse"}, "parameterOrder": ["resource"], "path": "v1/{+resource}:testIamPermissions", "httpMethod": "POST"}, "getIamPolicy": {"flatPath": "v1/projects/{projectsId}/snapshots/{snapshotsId}:getIamPolicy", "response": {"$ref": "Policy"}, "parameters": {"options.requestedPolicyVersion": {"type": "integer", "location": "query", "description": "Optional. The policy format version to be returned. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional bindings must specify version 3. Policies without any conditional bindings may specify any valid value or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32"}, "resource": {"location": "path", "type": "string", "description": "REQUIRED: The resource for which the policy is being requested. See the operation documentation for the appropriate value for this field.", "required": true, "pattern": "^projects/[^/]+/snapshots/[^/]+$"}}, "parameterOrder": ["resource"], "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "id": "pubsub.projects.snapshots.getIamPolicy", "path": "v1/{+resource}:getIamPolicy", "httpMethod": "GET"}, "create": {"request": {"$ref": "CreateSnapshotRequest"}, "httpMethod": "PUT", "flatPath": "v1/projects/{projectsId}/snapshots/{snapshotsId}", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "id": "pubsub.projects.snapshots.create", "path": "v1/{+name}", "parameters": {"name": {"location": "path", "type": "string", "description": "Required. User-provided name for this snapshot. If the name is not provided in the request, the server will assign a random name for this snapshot on the same project as the subscription. Note that for REST API requests, you must specify a name. See the resource name rules. Format is `projects/{project}/snapshots/{snap}`.", "required": true, "pattern": "^projects/[^/]+/snapshots/[^/]+$"}}, "description": "Creates a snapshot from the requested subscription. Snapshots are used in [Seek](https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot. If the snapshot already exists, returns `ALREADY_EXISTS`. If the requested subscription doesn't exist, returns `NOT_FOUND`. If the backlog in the subscription is too old -- and the resulting snapshot would expire in less than 1 hour -- then `FAILED_PRECONDITION` is returned. See also the `Snapshot.expire_time` field. If the name is not provided in the request, the server will assign a random name for this snapshot on the same project as the subscription, conforming to the [resource name format] (https://cloud.google.com/pubsub/docs/admin#resource_names). The generated name is populated in the returned Snapshot object. Note that for REST API requests, you must specify a name in the request.", "parameterOrder": ["name"], "response": {"$ref": "Snapshot"}}, "delete": {"parameters": {"snapshot": {"pattern": "^projects/[^/]+/snapshots/[^/]+$", "description": "Required. The name of the snapshot to delete. Format is `projects/{project}/snapshots/{snap}`.", "location": "path", "required": true, "type": "string"}}, "flatPath": "v1/projects/{projectsId}/snapshots/{snapshotsId}", "response": {"$ref": "Empty"}, "parameterOrder": ["snapshot"], "description": "Removes an existing snapshot. Snapshots are used in [Seek] (https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot. When the snapshot is deleted, all messages retained in the snapshot are immediately dropped. After a snapshot is deleted, a new one may be created with the same name, but the new one has no association with the old snapshot or its subscription, unless the same subscription is specified.", "httpMethod": "DELETE", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "id": "pubsub.projects.snapshots.delete", "path": "v1/{+snapshot}"}, "list": {"description": "Lists the existing snapshots. Snapshots are used in [Seek]( https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot.", "parameterOrder": ["project"], "response": {"$ref": "ListSnapshotsResponse"}, "httpMethod": "GET", "flatPath": "v1/projects/{projectsId}/snapshots", "path": "v1/{+project}/snapshots", "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/pubsub"], "id": "pubsub.projects.snapshots.list", "parameters": {"pageSize": {"format": "int32", "description": "Maximum number of snapshots to return.", "type": "integer", "location": "query"}, "pageToken": {"location": "query", "type": "string", "description": "The value returned by the last `ListSnapshotsResponse`; indicates that this is a continuation of a prior `ListSnapshots` call, and that the system should return the next page of data."}, "project": {"pattern": "^projects/[^/]+$", "location": "path", "description": "Required. The name of the project in which to list snapshots. Format is `projects/{project-id}`.", "type": "string", "required": true}}}}}}}}, "baseUrl": "https://pubsub.googleapis.com/", "name": "pubsub", "id": "pubsub:v1", "basePath": "", "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/pubsub": {"description": "View and manage Pub/Sub topics and subscriptions"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "View and manage your data across Google Cloud Platform services"}}}}, "protocol": "rest", "discoveryVersion": "v1", "servicePath": "", "description": "Provides reliable, many-to-many, asynchronous messaging between applications. ", "title": "Cloud Pub/Sub API", "parameters": {"fields": {"type": "string", "location": "query", "description": "Selector specifying which fields to include in a partial response."}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "type": "string", "location": "query"}, "oauth_token": {"type": "string", "location": "query", "description": "OAuth 2.0 token for the current user."}, "alt": {"type": "string", "enum": ["json", "media", "proto"], "location": "query", "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "description": "Data format for response.", "default": "json"}, "prettyPrint": {"location": "query", "type": "boolean", "description": "Returns response with indentations and line breaks.", "default": "true"}, "access_token": {"type": "string", "description": "OAuth access token.", "location": "query"}, "callback": {"type": "string", "location": "query", "description": "JSONP"}, "quotaUser": {"type": "string", "description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query"}, "upload_protocol": {"type": "string", "description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query"}, "$.xgafv": {"type": "string", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "description": "V1 error format.", "location": "query"}, "key": {"location": "query", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "type": "string"}}, "rootUrl": "https://pubsub.googleapis.com/", "batchPath": "batch", "schemas": {"ListSubscriptionsResponse": {"type": "object", "id": "ListSubscriptionsResponse", "properties": {"subscriptions": {"items": {"$ref": "Subscription"}, "description": "The subscriptions that match the request.", "type": "array"}, "nextPageToken": {"type": "string", "description": "If not empty, indicates that there may be more subscriptions that match the request; this value should be passed in a new `ListSubscriptionsRequest` to get more subscriptions."}}, "description": "Response for the `ListSubscriptions` method."}, "PullResponse": {"description": "Response for the `Pull` method.", "type": "object", "id": "PullResponse", "properties": {"receivedMessages": {"description": "Received Pub/Sub messages. The list will be empty if there are no more messages available in the backlog. For JSON, the response can be entirely empty. The Pub/Sub system may return fewer than the `maxMessages` requested even if there are more messages available in the backlog.", "items": {"$ref": "ReceivedMessage"}, "type": "array"}}}, "Snapshot": {"type": "object", "properties": {"expireTime": {"description": "The snapshot is guaranteed to exist up until this time. A newly-created snapshot expires no later than 7 days from the time of its creation. Its exact lifetime is determined at creation by the existing backlog in the source subscription. Specifically, the lifetime of the snapshot is `7 days - (age of oldest unacked message in the subscription)`. For example, consider a subscription whose oldest unacked message is 3 days old. If a snapshot is created from this subscription, the snapshot -- which will always capture this 3-day-old backlog as long as the snapshot exists -- will expire in 4 days. The service will refuse to create a snapshot that would expire in less than 1 hour after creation.", "format": "google-datetime", "type": "string"}, "name": {"type": "string", "description": "The name of the snapshot."}, "topic": {"description": "The name of the topic from which this snapshot is retaining messages.", "type": "string"}, "labels": {"type": "object", "description": "See [Creating and managing labels] (https://cloud.google.com/pubsub/docs/labels).", "additionalProperties": {"type": "string"}}}, "id": "Snapshot", "description": "A snapshot resource. Snapshots are used in [Seek](https://cloud.google.com/pubsub/docs/replay-overview) operations, which allow you to manage message acknowledgments in bulk. That is, you can set the acknowledgment state of messages in an existing subscription to the state captured by a snapshot."}, "CreateSnapshotRequest": {"id": "CreateSnapshotRequest", "type": "object", "properties": {"subscription": {"description": "Required. The subscription whose backlog the snapshot retains. Specifically, the created snapshot is guaranteed to retain: (a) The existing backlog on the subscription. More precisely, this is defined as the messages in the subscription's backlog that are unacknowledged upon the successful completion of the `CreateSnapshot` request; as well as: (b) Any messages published to the subscription's topic following the successful completion of the CreateSnapshot request. Format is `projects/{project}/subscriptions/{sub}`.", "type": "string"}, "labels": {"description": "See Creating and managing labels.", "type": "object", "additionalProperties": {"type": "string"}}}, "description": "Request for the `CreateSnapshot` method."}, "RetryPolicy": {"properties": {"minimumBackoff": {"format": "google-duration", "description": "The minimum delay between consecutive deliveries of a given message. Value should be between 0 and 600 seconds. Defaults to 10 seconds.", "type": "string"}, "maximumBackoff": {"format": "google-duration", "type": "string", "description": "The maximum delay between consecutive deliveries of a given message. Value should be between 0 and 600 seconds. Defaults to 600 seconds."}}, "type": "object", "id": "RetryPolicy", "description": "A policy that specifies how Cloud Pub/Sub retries message delivery. Retry delay will be exponential based on provided minimum and maximum backoffs. https://en.wikipedia.org/wiki/Exponential_backoff. RetryPolicy will be triggered on NACKs or acknowledgement deadline exceeded events for a given message. Retry Policy is implemented on a best effort basis. At times, the delay between consecutive deliveries may not match the configuration. That is, delay can be more or less than configured backoff."}, "ListTopicSnapshotsResponse": {"description": "Response for the `ListTopicSnapshots` method.", "properties": {"nextPageToken": {"description": "If not empty, indicates that there may be more snapshots that match the request; this value should be passed in a new `ListTopicSnapshotsRequest` to get more snapshots.", "type": "string"}, "snapshots": {"type": "array", "items": {"type": "string"}, "description": "The names of the snapshots that match the request."}}, "type": "object", "id": "ListTopicSnapshotsResponse"}, "SeekResponse": {"id": "SeekResponse", "properties": {}, "type": "object", "description": "Response for the `Seek` method (this response is empty)."}, "Policy": {"id": "Policy", "type": "object", "properties": {"etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "type": "string", "format": "byte"}, "bindings": {"items": {"$ref": "Binding"}, "description": "Associates a list of `members` to a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one member.", "type": "array"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members` to a single `role`. Members can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } **YAML example:** bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') - etag: BwWWja0YfJA= - version: 3 For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/)."}, "ModifyPushConfigRequest": {"type": "object", "id": "ModifyPushConfigRequest", "properties": {"pushConfig": {"$ref": "PushConfig", "description": "Required. The push configuration for future deliveries. An empty `pushConfig` indicates that the Pub/Sub system should stop pushing messages from the given subscription and allow messages to be pulled and acknowledged - effectively pausing the subscription if `Pull` or `StreamingPull` is not called."}}, "description": "Request for the ModifyPushConfig method."}, "ReceivedMessage": {"description": "A message and its corresponding acknowledgment ID.", "id": "ReceivedMessage", "type": "object", "properties": {"message": {"$ref": "PubsubMessage", "description": "The message."}, "deliveryAttempt": {"type": "integer", "description": "The approximate number of times that Cloud Pub/Sub has attempted to deliver the associated message to a subscriber. More precisely, this is 1 + (number of NACKs) + (number of ack_deadline exceeds) for this message. A NACK is any call to ModifyAckDeadline with a 0 deadline. An ack_deadline exceeds event is whenever a message is not acknowledged within ack_deadline. Note that ack_deadline is initially Subscription.ackDeadlineSeconds, but may get extended automatically by the client library. Upon the first delivery of a given message, `delivery_attempt` will have a value of 1. The value is calculated at best effort and is approximate. If a DeadLetterPolicy is not set on the subscription, this will be 0.", "format": "int32"}, "ackId": {"description": "This ID can be used to acknowledge the received message.", "type": "string"}}}, "DetachSubscriptionResponse": {"type": "object", "properties": {}, "id": "DetachSubscriptionResponse", "description": "Response for the DetachSubscription method. Reserved for future use."}, "PubsubMessage": {"properties": {"messageId": {"type": "string", "description": "ID of this message, assigned by the server when the message is published. Guaranteed to be unique within the topic. This value may be read by a subscriber that receives a `PubsubMessage` via a `Pull` call or a push delivery. It must not be populated by the publisher in a `Publish` call."}, "attributes": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Attributes for this message. If this field is empty, the message must contain non-empty data. This can be used to filter messages on the subscription."}, "publishTime": {"type": "string", "description": "The time at which the message was published, populated by the server when it receives the `Publish` call. It must not be populated by the publisher in a `Publish` call.", "format": "google-datetime"}, "data": {"type": "string", "description": "The message data field. If this field is empty, the message must contain at least one attribute.", "format": "byte"}, "orderingKey": {"type": "string", "description": "If non-empty, identifies related messages for which publish order should be respected. If a `Subscription` has `enable_message_ordering` set to `true`, messages published with the same non-empty `ordering_key` value will be delivered to subscribers in the order in which they are received by the Pub/Sub system. All `PubsubMessage`s published in a given `PublishRequest` must specify the same `ordering_key` value."}}, "description": "A message that is published by publishers and consumed by subscribers. The message must contain either a non-empty data field or at least one attribute. Note that client libraries represent this object differently depending on the language. See the corresponding [client library documentation](https://cloud.google.com/pubsub/docs/reference/libraries) for more information. See [quotas and limits] (https://cloud.google.com/pubsub/quotas) for more information about message limits.", "type": "object", "id": "PubsubMessage"}, "Subscription": {"id": "Subscription", "type": "object", "properties": {"expirationPolicy": {"$ref": "ExpirationPolicy", "description": "A policy that specifies the conditions for this subscription's expiration. A subscription is considered active as long as any connected subscriber is successfully consuming messages from the subscription or is issuing operations on the subscription. If `expiration_policy` is not set, a *default policy* with `ttl` of 31 days will be used. The minimum allowed value for `expiration_policy.ttl` is 1 day."}, "name": {"type": "string", "description": "Required. The name of the subscription. It must have the format `\"projects/{project}/subscriptions/{subscription}\"`. `{subscription}` must start with a letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters in length, and it must not start with `\"goog\"`."}, "messageRetentionDuration": {"type": "string", "description": "How long to retain unacknowledged messages in the subscription's backlog, from the moment a message is published. If `retain_acked_messages` is true, then this also configures the retention of acknowledged messages, and thus configures how far back in time a `Seek` can be done. Defaults to 7 days. Cannot be more than 7 days or less than 10 minutes.", "format": "google-duration"}, "labels": {"type": "object", "description": "See Creating and managing labels.", "additionalProperties": {"type": "string"}}, "retryPolicy": {"$ref": "RetryPolicy", "description": "A policy that specifies how Pub/Sub retries message delivery for this subscription. If not set, the default retry policy is applied. This generally implies that messages will be retried as soon as possible for healthy subscribers. RetryPolicy will be triggered on NACKs or acknowledgement deadline exceeded events for a given message."}, "topic": {"description": "Required. The name of the topic from which this subscription is receiving messages. Format is `projects/{project}/topics/{topic}`. The value of this field will be `_deleted-topic_` if the topic has been deleted.", "type": "string"}, "ackDeadlineSeconds": {"format": "int32", "description": "The approximate amount of time (on a best-effort basis) Pub/Sub waits for the subscriber to acknowledge receipt before resending the message. In the interval after the message is delivered and before it is acknowledged, it is considered to be *outstanding*. During that time period, the message will not be redelivered (on a best-effort basis). For pull subscriptions, this value is used as the initial value for the ack deadline. To override this value for a given message, call `ModifyAckDeadline` with the corresponding `ack_id` if using non-streaming pull or send the `ack_id` in a `StreamingModifyAckDeadlineRequest` if using streaming pull. The minimum custom deadline you can specify is 10 seconds. The maximum custom deadline you can specify is 600 seconds (10 minutes). If this parameter is 0, a default value of 10 seconds is used. For push delivery, this value is also used to set the request timeout for the call to the push endpoint. If the subscriber never acknowledges the message, the Pub/Sub system will eventually redeliver the message.", "type": "integer"}, "filter": {"description": "An expression written in the Pub/Sub [filter language](https://cloud.google.com/pubsub/docs/filtering). If non-empty, then only `PubsubMessage`s whose `attributes` field matches the filter are delivered on this subscription. If empty, then no messages are filtered out.", "type": "string"}, "detached": {"type": "boolean", "description": "Indicates whether the subscription is detached from its topic. Detached subscriptions don't receive messages from their topic and don't retain any backlog. `Pull` and `StreamingPull` requests will return FAILED_PRECONDITION. If the subscription is a push subscription, pushes to the endpoint will not be made."}, "retainAckedMessages": {"type": "boolean", "description": "Indicates whether to retain acknowledged messages. If true, then messages are not expunged from the subscription's backlog, even if they are acknowledged, until they fall out of the `message_retention_duration` window. This must be true if you would like to [Seek to a timestamp] (https://cloud.google.com/pubsub/docs/replay-overview#seek_to_a_time)."}, "enableMessageOrdering": {"description": "If true, messages published with the same `ordering_key` in `PubsubMessage` will be delivered to the subscribers in the order in which they are received by the Pub/Sub system. Otherwise, they may be delivered in any order.", "type": "boolean"}, "deadLetterPolicy": {"description": "A policy that specifies the conditions for dead lettering messages in this subscription. If dead_letter_policy is not set, dead lettering is disabled. The Cloud Pub/Sub service account associated with this subscriptions's parent project (i.e., service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com) must have permission to Acknowledge() messages on this subscription.", "$ref": "DeadLetterPolicy"}, "pushConfig": {"description": "If push delivery is used with this subscription, this field is used to configure it. An empty `pushConfig` signifies that the subscriber will pull and ack messages using API methods.", "$ref": "PushConfig"}}, "description": "A subscription resource."}, "PublishResponse": {"properties": {"messageIds": {"items": {"type": "string"}, "type": "array", "description": "The server-assigned ID of each published message, in the same order as the messages in the request. IDs are guaranteed to be unique within the topic."}}, "description": "Response for the `Publish` method.", "id": "PublishResponse", "type": "object"}, "PushConfig": {"properties": {"attributes": {"type": "object", "description": "Endpoint configuration attributes that can be used to control different aspects of the message delivery. The only currently supported attribute is `x-goog-version`, which you can use to change the format of the pushed message. This attribute indicates the version of the data expected by the endpoint. This controls the shape of the pushed message (i.e., its fields and metadata). If not present during the `CreateSubscription` call, it will default to the version of the Pub/Sub API used to make such call. If not present in a `ModifyPushConfig` call, its value will not be changed. `GetSubscription` calls will always return a valid version, even if the subscription was created without this attribute. The only supported values for the `x-goog-version` attribute are: * `v1beta1`: uses the push format defined in the v1beta1 Pub/Sub API. * `v1` or `v1beta2`: uses the push format defined in the v1 Pub/Sub API. For example: attributes { \"x-goog-version\": \"v1\" } ", "additionalProperties": {"type": "string"}}, "oidcToken": {"description": "If specified, Pub/Sub will generate and attach an OIDC JWT token as an `Authorization` header in the HTTP request for every pushed message.", "$ref": "OidcToken"}, "pushEndpoint": {"description": "A URL locating the endpoint to which messages should be pushed. For example, a Webhook endpoint might use `https://example.com/push`.", "type": "string"}}, "type": "object", "description": "Configuration for a push delivery endpoint.", "id": "PushConfig"}, "AcknowledgeRequest": {"id": "AcknowledgeRequest", "description": "Request for the Acknowledge method.", "type": "object", "properties": {"ackIds": {"items": {"type": "string"}, "type": "array", "description": "Required. The acknowledgment ID for the messages being acknowledged that was returned by the Pub/Sub system in the `Pull` response. Must not be empty."}}}, "SeekRequest": {"id": "SeekRequest", "type": "object", "description": "Request for the `Seek` method.", "properties": {"snapshot": {"type": "string", "description": "The snapshot to seek to. The snapshot's topic must be the same as that of the provided subscription. Format is `projects/{project}/snapshots/{snap}`."}, "time": {"description": "The time to seek to. Messages retained in the subscription that were published before this time are marked as acknowledged, and messages retained in the subscription that were published after this time are marked as unacknowledged. Note that this operation affects only those messages retained in the subscription (configured by the combination of `message_retention_duration` and `retain_acked_messages`). For example, if `time` corresponds to a point before the message retention window (or to a point before the system's notion of the subscription creation time), only retained messages will be marked as unacknowledged, and already-expunged messages will not be restored.", "format": "google-datetime", "type": "string"}}}, "ListSnapshotsResponse": {"type": "object", "id": "ListSnapshotsResponse", "properties": {"snapshots": {"description": "The resulting snapshots.", "items": {"$ref": "Snapshot"}, "type": "array"}, "nextPageToken": {"type": "string", "description": "If not empty, indicates that there may be more snapshot that match the request; this value should be passed in a new `ListSnapshotsRequest`."}}, "description": "Response for the `ListSnapshots` method."}, "MessageStoragePolicy": {"description": "A policy constraining the storage of messages published to the topic.", "properties": {"allowedPersistenceRegions": {"type": "array", "items": {"type": "string"}, "description": "A list of IDs of GCP regions where messages that are published to the topic may be persisted in storage. Messages published by publishers running in non-allowed GCP regions (or running outside of GCP altogether) will be routed for storage in one of the allowed regions. An empty list means that no regions are allowed, and is not a valid configuration."}}, "id": "MessageStoragePolicy", "type": "object"}, "SetIamPolicyRequest": {"id": "SetIamPolicyRequest", "description": "Request message for `SetIamPolicy` method.", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Cloud Platform services (such as Projects) might reject them."}}, "type": "object"}, "OidcToken": {"id": "OidcToken", "description": "Contains information needed for generating an [OpenID Connect token](https://developers.google.com/identity/protocols/OpenIDConnect).", "type": "object", "properties": {"audience": {"description": "Audience to be used when generating OIDC token. The audience claim identifies the recipients that the JWT is intended for. The audience value is a single case-sensitive string. Having multiple values (array) for the audience field is not supported. More info about the OIDC JWT token audience here: https://tools.ietf.org/html/rfc7519#section-4.1.3 Note: if not specified, the Push endpoint URL will be used.", "type": "string"}, "serviceAccountEmail": {"description": "[Service account email](https://cloud.google.com/iam/docs/service-accounts) to be used for generating the OIDC token. The caller (for CreateSubscription, UpdateSubscription, and ModifyPushConfig RPCs) must have the iam.serviceAccounts.actAs permission for the service account.", "type": "string"}}}, "TestIamPermissionsResponse": {"properties": {"permissions": {"type": "array", "items": {"type": "string"}, "description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed."}}, "type": "object", "description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse"}, "PublishRequest": {"description": "Request for the Publish method.", "type": "object", "id": "PublishRequest", "properties": {"messages": {"description": "Required. The messages to publish.", "type": "array", "items": {"$ref": "PubsubMessage"}}}}, "UpdateSubscriptionRequest": {"type": "object", "properties": {"subscription": {"description": "Required. The updated subscription object.", "$ref": "Subscription"}, "updateMask": {"type": "string", "description": "Required. Indicates which fields in the provided subscription to update. Must be specified and non-empty.", "format": "google-fieldmask"}}, "id": "UpdateSubscriptionRequest", "description": "Request for the UpdateSubscription method."}, "PullRequest": {"description": "Request for the `Pull` method.", "properties": {"maxMessages": {"format": "int32", "description": "Required. The maximum number of messages to return for this request. Must be a positive integer. The Pub/Sub system may return fewer than the number specified.", "type": "integer"}, "returnImmediately": {"type": "boolean", "description": "Optional. If this field set to true, the system will respond immediately even if it there are no messages available to return in the `Pull` response. Otherwise, the system may wait (for a bounded amount of time) until at least one message is available, rather than returning no messages. Warning: setting this field to `true` is discouraged because it adversely impacts the performance of `Pull` operations. We recommend that users do not set this field."}}, "id": "PullRequest", "type": "object"}, "DeadLetterPolicy": {"id": "DeadLetterPolicy", "description": "Dead lettering is done on a best effort basis. The same message might be dead lettered multiple times. If validation on any of the fields fails at subscription creation/updation, the create/update subscription request will fail.", "properties": {"maxDeliveryAttempts": {"type": "integer", "description": "The maximum number of delivery attempts for any message. The value must be between 5 and 100. The number of delivery attempts is defined as 1 + (the sum of number of NACKs and number of times the acknowledgement deadline has been exceeded for the message). A NACK is any call to ModifyAckDeadline with a 0 deadline. Note that client libraries may automatically extend ack_deadlines. This field will be honored on a best effort basis. If this parameter is 0, a default value of 5 is used.", "format": "int32"}, "deadLetterTopic": {"type": "string", "description": "The name of the topic to which dead letter messages should be published. Format is `projects/{project}/topics/{topic}`.The Cloud Pub/Sub service account associated with the enclosing subscription's parent project (i.e., service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com) must have permission to Publish() to this topic. The operation will fail if the topic does not exist. Users should ensure that there is a subscription attached to this topic since messages published to a topic with no subscriptions are lost."}}, "type": "object"}, "Empty": {"type": "object", "properties": {}, "description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } The JSON representation for `Empty` is empty JSON object `{}`.", "id": "Empty"}, "Binding": {"type": "object", "description": "Associates `members` with a `role`.", "id": "Binding", "properties": {"bindingId": {"description": "A client-specified ID for this binding. Expected to be globally unique to support the internal bindings-by-ID API.", "type": "string"}, "condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the members in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the identities requesting access for a Cloud Platform resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a service account. For example, `<EMAIL>`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. ", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to `members`. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.", "type": "string"}}}, "UpdateTopicRequest": {"type": "object", "id": "UpdateTopicRequest", "properties": {"updateMask": {"type": "string", "description": "Required. Indicates which fields in the provided topic to update. Must be specified and non-empty. Note that if `update_mask` contains \"message_storage_policy\" but the `message_storage_policy` is not set in the `topic` provided above, then the updated value is determined by the policy configured at the project or organization level.", "format": "google-fieldmask"}, "topic": {"$ref": "Topic", "description": "Required. The updated topic object."}}, "description": "Request for the UpdateTopic method."}, "ExpirationPolicy": {"id": "ExpirationPolicy", "properties": {"ttl": {"description": "Specifies the \"time-to-live\" duration for an associated resource. The resource expires if it is not active for a period of `ttl`. The definition of \"activity\" depends on the type of the associated resource. The minimum and maximum allowed values for `ttl` depend on the type of the associated resource, as well. If `ttl` is not set, the associated resource never expires.", "type": "string", "format": "google-duration"}}, "description": "A policy that specifies the conditions for resource expiration (i.e., automatic resource deletion).", "type": "object"}, "UpdateSnapshotRequest": {"properties": {"snapshot": {"description": "Required. The updated snapshot object.", "$ref": "Snapshot"}, "updateMask": {"description": "Required. Indicates which fields in the provided snapshot to update. Must be specified and non-empty.", "format": "google-fieldmask", "type": "string"}}, "id": "UpdateSnapshotRequest", "type": "object", "description": "Request for the UpdateSnapshot method."}, "ModifyAckDeadlineRequest": {"description": "Request for the ModifyAckDeadline method.", "properties": {"ackDeadlineSeconds": {"format": "int32", "type": "integer", "description": "Required. The new ack deadline with respect to the time this request was sent to the Pub/Sub system. For example, if the value is 10, the new ack deadline will expire 10 seconds after the `ModifyAckDeadline` call was made. Specifying zero might immediately make the message available for delivery to another subscriber client. This typically results in an increase in the rate of message redeliveries (that is, duplicates). The minimum deadline you can specify is 0 seconds. The maximum deadline you can specify is 600 seconds (10 minutes)."}, "ackIds": {"type": "array", "description": "Required. List of acknowledgment IDs.", "items": {"type": "string"}}}, "id": "ModifyAckDeadlineRequest", "type": "object"}, "TestIamPermissionsRequest": {"properties": {"permissions": {"items": {"type": "string"}, "type": "array", "description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as '*' or 'storage.*') are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions)."}}, "type": "object", "description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest"}, "ListTopicSubscriptionsResponse": {"id": "ListTopicSubscriptionsResponse", "description": "Response for the `ListTopicSubscriptions` method.", "properties": {"subscriptions": {"items": {"type": "string"}, "type": "array", "description": "The names of subscriptions attached to the topic specified in the request."}, "nextPageToken": {"description": "If not empty, indicates that there may be more subscriptions that match the request; this value should be passed in a new `ListTopicSubscriptionsRequest` to get more subscriptions.", "type": "string"}}, "type": "object"}, "ListTopicsResponse": {"type": "object", "description": "Response for the `ListTopics` method.", "properties": {"topics": {"type": "array", "items": {"$ref": "Topic"}, "description": "The resulting topics."}, "nextPageToken": {"type": "string", "description": "If not empty, indicates that there may be more topics that match the request; this value should be passed in a new `ListTopicsRequest`."}}, "id": "ListTopicsResponse"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "properties": {"title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}, "expression": {"type": "string", "description": "Textual representation of an expression in Common Expression Language syntax."}, "description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "location": {"type": "string", "description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file."}}, "id": "Expr", "type": "object"}, "Topic": {"type": "object", "properties": {"labels": {"description": "See [Creating and managing labels] (https://cloud.google.com/pubsub/docs/labels).", "additionalProperties": {"type": "string"}, "type": "object"}, "kmsKeyName": {"type": "string", "description": "The resource name of the Cloud KMS CryptoKey to be used to protect access to messages published on this topic. The expected format is `projects/*/locations/*/keyRings/*/cryptoKeys/*`."}, "name": {"description": "Required. The name of the topic. It must have the format `\"projects/{project}/topics/{topic}\"`. `{topic}` must start with a letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters in length, and it must not start with `\"goog\"`.", "type": "string"}, "messageStoragePolicy": {"description": "Policy constraining the set of Google Cloud Platform regions where messages published to the topic may be stored. If not present, then no constraints are in effect.", "$ref": "MessageStoragePolicy"}}, "description": "A topic resource.", "id": "Topic"}}, "ownerName": "Google", "mtlsRootUrl": "https://pubsub.mtls.googleapis.com/"}