<?php

set_include_path(dirname(__FILE__) . '/../include/');

print "L'utilisation de ce fichier n'est pas recommandé. Si vraiment nécessaire, décommentez le code ci-dessous.\n";
/*
require_once('db.inc.php');
require_once('users.inc.php');

require_once( str_replace('/tools', '', getenv('PWD')).'/htdocs/config.inc.php' );

$rusr = gu_users_get(); 
if( $rusr && ria_mysql_num_rows($rusr) ){
	while( $usr = ria_mysql_fetch_assoc($rusr) ){

		$data = array();

		// récupère toutes les adresse pour ce compte client 
		$radr = gu_adresses_get( $usr['id'], 0, '', array(), false, '', '', '', '', '', '', '', false, false, false, false );
		if( $radr && ria_mysql_num_rows($radr)> 1 ){

			// première boucle pour ajouter l'adresse de facturation et livraison par défaut de l'utilisateur , adresse non maskable
			while( $adr = ria_mysql_fetch_assoc($radr) ){

				if( (is_numeric($usr['adr_invoices']) && $usr['adr_invoices'] == $adr['id']) 
				 || (is_numeric($usr['adr_delivery']) && $usr['adr_delivery'] == $adr['id'] ) ){
					$data[] = get_key_from_adr($adr);
				}
			}

			ria_mysql_data_seek($radr, 0);

			// deuxième boucle pour détecter les doublons
			while( $adr = ria_mysql_fetch_assoc($radr) ){

				if( (is_numeric($usr['adr_invoices']) && $usr['adr_invoices'] == $adr['id']) 
				 || (is_numeric($usr['adr_delivery']) && $usr['adr_delivery'] == $adr['id'] ) ){
					continue;
				}

				if( array_search(get_key_from_adr($adr), $data) !== false ){

					// doublons trouvé masque l'adresse 
					print 'Masque:'.$adr['id'].':'.get_key_from_adr($adr)."\n";
					gu_adresses_set_masked( $adr['id'], true );

				}else{
					$data[] = get_key_from_adr($adr);
				}
			}
		}

	}
}

function get_key_from_adr($adr){
	return urlalias($adr['lastname'].$adr['firstname'].$adr['society'].$adr['address1'].$adr['address2'].$adr['postal_code'].$adr['city'].$adr['country']);
}
*/