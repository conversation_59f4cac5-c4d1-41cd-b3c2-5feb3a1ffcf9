<?php

namespace PhpCoveralls\Bundle\CoverallsBundle\Api;

use GuzzleHttp\Client;
use PhpCoveralls\Bundle\CoverallsBundle\Config\Configuration;

/**
 * Coveralls API client.
 *
 * <AUTHOR> <<EMAIL>>
 */
abstract class CoverallsApi
{
    /**
     * Configuration.
     *
     * @var Configuration
     */
    protected $config;

    /**
     * HTTP client.
     *
     * @var \GuzzleHttp\Client
     */
    protected $client;

    /**
     * Constructor.
     *
     * @param Configuration      $config configuration
     * @param \GuzzleHttp\Client $client hTTP client
     */
    public function __construct(Configuration $config, Client $client = null)
    {
        $this->config = $config;
        $this->client = $client;
    }

    // accessor

    /**
     * Return configuration.
     *
     * @return Configuration
     */
    public function getConfiguration()
    {
        return $this->config;
    }

    /**
     * Set HTTP client.
     *
     * @param \GuzzleHttp\Client $client hTTP client
     *
     * @return $this
     */
    public function setHttpClient(Client $client)
    {
        $this->client = $client;

        return $this;
    }

    /**
     * Return HTTP client.
     *
     * @return \GuzzleHttp\Client
     */
    public function getHttpClient()
    {
        return $this->client;
    }
}
