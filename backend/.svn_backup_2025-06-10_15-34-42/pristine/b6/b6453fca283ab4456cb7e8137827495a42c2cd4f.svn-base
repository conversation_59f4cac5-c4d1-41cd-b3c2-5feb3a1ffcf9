<?php
/// \cond onlyria

require_once( 'comparators.inc.php' );
require_once( 'products.inc.php' );

/** \defgroup tsk_comparators File de tâches comparateur
 *	\ingroup ctr_comparators
 *	Ce module comprend les fonctions nécessaires à la gestion d'une file de tâches pour les comparateurs
 *	@{
 */

/**	Cette fonction permet d'ajouter une tâche
 *	@param int $ctr_id			Identifiant du comparateur
 *	@param int $prd_id			Identifiant du produit
 *	@param $action			Action à réaliser
 *	@return	true en cas de succès, false sinon
 */
function tsk_comparators_add( $ctr_id, $prd_id, $action ){
	if( !ctr_comparators_exists($ctr_id) ) return false;
	if( !prd_products_exists($prd_id) ) return false;
	if( !tsk_actions_exists($action) ) return false;

	global $config;

	$sql = '
		insert into tsk_comparators
			( tsk_tnt_id, tsk_ctr_id, tsk_prd_id, tsk_action, tsk_date_created, tsk_date_completed, tsk_date_deleted )
		values
			( '.$config['tnt_id'].', '.($ctr_id === null ? 'null' : $ctr_id).', '.$prd_id.', \''.addslashes($action).'\', now(), null, null )
	';

	$rtsk = ria_mysql_query($sql);

	if( !$rtsk ) return false;

	return ria_mysql_insert_id();
}

/**	Cette fonction supprime des tâches
 *	@param $id Identifiant (ou tableau d'identifiants) de la tâche à supprimer
 *	@return bool true en cas de succès, false sinon
 */
function tsk_comparators_del( $id ) {
	if( !is_array($id) ) $id = array($id);
	if( !sizeof($id) ) return false;
	foreach ($id as $value) {
		if( !is_numeric($value) ) return false;
	}

	global $config;

	$sql = '
		update tsk_comparators
		set tsk_date_deleted = now()
		where tsk_id in ( '.implode(', ', $id).' )
			and tsk_tnt_id = '.$config['tnt_id'].'
	';
	return ria_mysql_query($sql);
}

/** Cette fonction permet de supprimer des tâches selons l'identifiant de l'import.
 *	@param int $import_id Obligatoire, identifiant d'un import
 *	@param int|array $prds Optionnel, identifiant ou tableau d'identifiants de produit
 *	@return bool True si la suppression s'est correctement déroulée, False dans le cas contraire
 */
function tsk_comparators_del_byimport( $import_id, $prds=false ){
	if( trim($import_id)=='' ) return false;
	if( $prds!==false && !is_numeric($prds) && !is_array($prds) ) return false;
	global $config;

	if( $prds!==false ){
		if( is_numeric($prds) ){
			if( $prds<=0 ) return false;
			$prds = array( $prds );
		} else {
			if( !sizeof($prds) ) return false;
			foreach( $prds as $p ){
				if( !is_numeric($p) || $p<=0 ){
					return false;
				}
			}
		}
	}

	$sql = '
		update tsk_comparators
		set tsk_date_deleted=now()
		where tsk_tnt_id='.$config['tnt_id'].'
			and tsk_import_id=\''.addslashes( $import_id ).'\'
	';

	if( is_array($prds) ){
		$sql .= '
			and tsk_prd_id in ('.implode(', ', $prds).')
		';
	}

	return ria_mysql_query( $sql );
}

/**	Cette fonction renvoie si la tâche existe
 *	@param int $id	Identifiant de la tâche
 *	@return bool true si la tâche existe, false sinon
 */
function tsk_comparators_exists( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;

	global $config;

	$rtsk = ria_mysql_query('
		select 1
		from tsk_comparators
		where tsk_id = '.$id.'
			and tsk_tnt_id = '.$config['tnt_id'].'
			and tsk_date_deleted is null
	');

	if( !($rtsk && ria_mysql_num_rows($rtsk)) ) return false;

	return true;
}

/** Cette fonction permet de vérifier qu'une action sur un produit existe bien pour un comparateur donné.
 *	@param int $ctr Obligatoire, identifiant d'un comparateur de prix
 *	@param int $prd Obligatoire, identifiant d'un produit
 *	@param $action Obligatoire, action réalisée sur ce produit
 *	@param bool $completed facultatif, par defaut on récupère toutes les tâches,
 * 		si true que les tâches complété, si false que les tâches non complété
 *	@return bool True si l'action existe bien, False dans le cas contraire
 */
function tsk_comparators_action_exists( $ctr, $prd, $action, $completed=null ){
	if( !is_numeric($ctr) || $ctr<=0 ) return false;
	if( !is_numeric($prd) || $prd<=0 ) return false;
	if( !tsk_actions_exists($action) ) return false;
	global $config;

	$sql = '
		select 1
		from tsk_comparators
		where tsk_tnt_id='.$config['tnt_id'].'
			and tsk_ctr_id='.$ctr.'
			and tsk_prd_id='.$prd.'
			and tsk_action=\''.$action.'\'
			and tsk_date_deleted is null
	';

	if( null !== $completed ){
		if( $completed ){
			$sql .= '
				and tsk_date_completed is not null
			';
		}else{
			$sql .= '
				and tsk_date_completed is null
			';
		}
	}

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return true;
}

/**	Cette fonction renvoie une liste de tâches
 *	@param int $ctr_id	Identifiant du comparateur
 *	@param bool $complete Optionnel, par défaut seule les tâches non faites sont retourné, mettre true pour ne retourner quel celles qui sont finie
 *	@param $action Optionnel, action des tâches à récupérer
 *
 *	@return	resource Un résultat de requête MySQL avec les champs suivants :
 *		-	id				Identifiant de la tâche
 *		-	tnt_id			Tenant
 *		-	prd_id			Identifiant du produit
 *		-	action			Action à réaliser
 *		-	date_created	Date de création
 *		-	date_completed	Date de fin de traitement
 *		-	import_id : identifiant de l'import
 */
function tsk_comparators_get( $ctr_id, $complete=false, $action='' ) {
	if( !ctr_comparators_exists($ctr_id) ) return false;

	$action = trim( $action );
	if( $action!='' && !tsk_actions_exists($action) ){
		return false;
	}

	global $config;

	$sql = '
		select tsk_tnt_id as tnt_id, tsk_id as id, tsk_ctr_id as ctr_id, tsk_prd_id as prd_id, tsk_action as action, tsk_date_created as date_created,
		tsk_date_completed as date_completed, tsk_date_deleted as date_deleted, tsk_import_id as import_id
		from tsk_comparators
		where tsk_tnt_id = '.$config['tnt_id'].'
			and tsk_ctr_id = '.$ctr_id.'
			and tsk_date_deleted is null
	';

	if( $complete ){
		$sql .= ' and tsk_date_completed is not null';
	} else {
		$sql .= ' and tsk_date_completed is null';
	}

	if( $action!='' ){
		$sql .= ' and tsk_action="'.$action.'"';
	}

	$sql .= '
		order by tsk_date_created asc, tsk_id asc
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de récupérer l'identifiant de l'import pour une tâche.
 *	@param int $tsk Obligatoire, identifiant d'une tâche
 *	@return int L'identifiant de l'import, False si la tâche n'existe pas ou que l'information n'est pas renseignée
 */
function tsk_comparators_get_import_id( $tsk ){
	if( !is_numeric($tsk) || $tsk<=0 ) return false;
	global $config;

	$sql = '
		select tsk_import_id as import_id
		from tsk_comparators
		where tsk_tnt_id='.$config['tnt_id'].'
			and tsk_id='.$tsk.'
			and tsk_import_id is not null
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'import_id' );
}

/**	Marqué la tâche comme traitée
 *	@param int|array $id	Identifiant ou tableau d'identifiants de la tâche
 *	@return bool true en cas de succès, false sinon
 */
function tsk_comparators_set_completed( $id ){
	if( !is_numeric($id) && !is_array($id) ) return false;

	if( !is_array($id) ){
		if( $id<=0 ) return false;
		$id = array( $id );
	} else {
		if( !sizeof($id) ) return false;
		foreach( $id as $i ){
			if( !is_numeric($i) || $i<=0 )
				return false;
		}
	}

	global $config;

	$sql = '
		update tsk_comparators
		set tsk_date_completed = now()
		where tsk_id in ('.implode( ',', $id ).')
			and tsk_tnt_id = '.$config['tnt_id'].'
	';

	return ria_mysql_query($sql);
}

/** Cette fonction permet de mettre à jour l'identifiant de l'import pour une tâche.
 *	@param int|array $tsk Obligatoire, identifiant ou tableau d'identifiants d'une tâche
 *	@param int $importid Obligatoire, identifiant de l'import
 *	@return bool True si tout s'est correctement déroulé, False dans le cas contaire
 */
function tsk_comparators_set_import_id( $tsk, $importid ){
	if( !is_numeric($tsk) && !is_array($tsk) ) return false;
	global $config;

	if( is_numeric($tsk) ){
		if( $tsk<=0 ) return false;
		$tsk = array( $tsk );
	} else {
		if( !sizeof($tsk) ) return false;
		foreach( $tsk as $t ){
			if( !is_numeric($t) || $t<=0 )
				return false;
		}
	}

	$sql = '
		update tsk_comparators
		set tsk_import_id=\''.addslashes( $importid ).'\'
		where tsk_tnt_id='.$config['tnt_id'].'
			and tsk_id in ('.implode( ',', $tsk ).')
	';

	return ria_mysql_query( $sql );
}


/** Cette fonction permet de vérifier si une action existe
 *	@param $action Obligatoire, code de l'action
 *	@return bool True si l'action existe, False dans le cas contraire
 */
function tsk_actions_exists( $action ){
	return in_array( $action, array('add', 'add-prd', 'update', 'update-price', 'update-qte', 'update-priceqte', 'set-image', 'delete') );
}

/** Cette fonction permet de faire le ménage dans la table des tâches
 *	@param $days Optionnel, nombre de jours par défaut les tâches complétées il y a plus de 30 jours sont définitivement supprimées
 *	@return bool True si le ménage s'est correctement déroulé, False dans le cas contraire
 */
function tsk_comparators_cleanup( $days=30 ){
	if( !is_numeric($days) || $days<0 ){
		return false;
	}

	$res = ria_mysql_query('
		update tsk_comparators
		set tsk_date_deleted = now()
		where tsk_date_deleted is null
			and tsk_date_completed is not null
	');

	if( !$res ){
		return false;
	}

	$res = ria_mysql_query('
		update tsk_comparators
		set tsk_date_completed = now(),
			tsk_date_completed = now()
		where tsk_date_deleted is null
			and tsk_ctr_id=11
	');

	if( !$res ){
		return false;
	}

	$date_limited = $days>0 ? date( 'Y-m-d', strtotime('-'.$days.' days') ) : date('Y-m-d');

	$res = ria_mysql_query('
		select tsk_tnt_id as tnt_id, tsk_id as id from tsk_comparators
		where (tsk_date_deleted is not null and date(tsk_date_deleted)<="'.$date_limited.'")
			or (tsk_date_deleted is null and tsk_action="update-priceqte" and date(tsk_date_completed)<="'.$date_limited.'");
	');

	if( !$res ){
		return false;
	}

	$i = 1;
	while( $r = ria_mysql_fetch_array($res) ){
		if( $i>=1000 ){
			sleep(2);
			$i = 1;
		}

		if( !ria_mysql_query('delete from tsk_comparators where tsk_tnt_id='.$r['tnt_id'].' and tsk_id='.$r['id']) ){
			error_log( __FILE__.':'.__LINE__.' Erreur lors de la suppresion tsk : delete from tsk_comparators where tsk_tnt_id='.$r['tnt_id'].' and tsk_id='.$r['id'] );
			return false;
		}
	}

	return true;
}

/// @}

/// \endcond