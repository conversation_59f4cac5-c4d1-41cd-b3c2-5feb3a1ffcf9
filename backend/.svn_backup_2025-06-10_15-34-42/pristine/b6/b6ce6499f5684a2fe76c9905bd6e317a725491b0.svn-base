{"name": "phpunit/php-token-stream", "description": "Wrapper around PHP's tokenizer extension.", "type": "library", "keywords": ["tokenizer"], "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues"}, "require": {"php": ">=5.3.3", "ext-tokenizer": "*"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "1.4-dev"}}}