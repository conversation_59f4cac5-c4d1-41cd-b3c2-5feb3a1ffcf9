<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/servicemanager.proto

namespace Google\Cloud\ServiceManagement\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\ServiceManagement\V1\GetServiceConfigRequest\ConfigView instead.
     * @deprecated
     */
    class GetServiceConfigRequest_ConfigView {}
}
class_exists(GetServiceConfigRequest\ConfigView::class);
@trigger_error('Google\Cloud\ServiceManagement\V1\GetServiceConfigRequest_ConfigView is deprecated and will be removed in the next major release. Use Google\Cloud\ServiceManagement\V1\GetServiceConfigRequest\ConfigView instead', E_USER_DEPRECATED);

