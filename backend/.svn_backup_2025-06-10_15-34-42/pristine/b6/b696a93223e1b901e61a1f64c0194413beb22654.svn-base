<?php
/**
 * \defgroup api-fields-restricted-values Valeur restrictive  
 * \ingroup api-fields
 * @{	
 * \page api-fields-restricted-values-get Chargement
 *
 * Cette fonction permet de récupérer des valeurs de listes de choix
 *
 *		\code
 *			GET /fields/restricted_values/
 *		\endcode
 *	
 * @param id Facultatif, identifiant d'une valeur sur laquelle filtrer le résultat
 * @param fld Facultatif, identifiant d'un champ sur lequel filtrer le résultat
 * @param alias Facultatif, alias du libellé sur lequel filtrer le résultat
 *			
 * @return Json sous la forme suivante :
 *		\code{.json}
 *			{
 *				"tenant": identifiant du locataire,
 *			 	"id": identifiant de la valeur,
 *			 	"name": désignation de la valeur,
 *			 	"fld_id": identifiantdu champ de rattachement,
 *			 	"alias": alias de la valeur (suppression des accents par exemple),
 *			 	"parent": identifiant de tri personnalisé,
 *			 	"pos": Position de tri personnalisé,
 *				"parent_pos" : Position de tri personnalisé du parent (si $sort_by_parent spécifié),
 *				"in_restrict" : uniquement si $val_restrict et $restrict_inline, détermine si la valeur est incluse via le moteur de restrictions
 *			}	
 *		\endcode
 * @}
*/
switch( $method ){
	case 'get':

		$id = 0;
		if( isset($_GET['id']) && is_numeric($_GET['id']) ){
			$id = $_GET['id'];
		}
		$fld = 0;
		if( isset($_GET['fld']) && is_numeric($_GET['fld']) ){
			$fld = $_GET['fld'];
		}
		$alias = '';
		if( isset($_GET['alias']) ){
			$alias = $_GET['alias'];
		}

		$res = fld_restricted_values_get( $id, $fld, $alias );
		if( !$res ){
			throw new Exception("Erreur de la requete fld_restricted_values_get");
		}

		$array = array();

		while($val = ria_mysql_fetch_assoc($res)){
			$array[] = $val;
		}

		$result = true;
		$content = $array;

		break;
}
