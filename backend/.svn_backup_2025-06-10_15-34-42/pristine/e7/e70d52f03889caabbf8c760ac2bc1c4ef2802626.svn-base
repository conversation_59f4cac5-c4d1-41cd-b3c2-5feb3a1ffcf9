<?php
// This is a normal block comment that might
// talk about functions like echo and print and if/else but
// is not commented out code.

// if (empty($this)) {echo 'This is will not work';}

/*
function myFunction()
{

}//end myFunction()
*/

//eval('$var = 4;');
//$string = '$var = 4;';
//eval($string);

#eval('$var = 4;');
#$string = '$var = 4;';
#eval($string);

if ($blah) {
    // This is a multi-line comment that goes over two indented
    // lines.

    // If it's null, then there must be no parameters for this
    // method.
}

// short.

// Continue, as we're done with this token.

/*
 * The listeners array.
 *
 * @var array(PHP_CodeSniffer_Sniff)
 */

//

/*
*/

function myFunction()
{
}//end myFunction()

// ----------------------------
// | A comment block           |
// ----------------------------

// =============================
// | A comment block           |
// =============================

// *****************************
// | A comment block           |
// *****************************

/*
 * List of panels.
 *
 * XML structure:
 * <panels>
 *     <panel_one_id>
 *         <title>Title</title>
 *         <panelContent>Contents</panelContent>
 *         <panels>
 *             <child_panel_id>...</child_panel_id>
 *         </panels>
 *     </panel_one_id>
 * </panels>
 *
 * @return void
 */

/*
    [^\'"]
*/

// http://www.google.com

// Base config function.

// function ()

// T_STRING is not a function call or not listed in _getFunctionListWithCallableArgument().

	// function myFunction( $param )
	// {
	//    do_something();
	// }//end myFunction()
	//

/*
function myFunction( $param )
{
	// phpcs:disable Standard.Category.Sniff -- for reasons.
	if ( preg_match( '`[abc]`', $param ) > 0 ) {
		do_something();
	}
	// phpcs:enable Standard.Category.Sniff -- for reasons.

}//end myFunction()
*/

	/*
	 * function myFunction( $param ) // @phpcs:ignore Standard.Category.Sniff -- for reasons.
	 * {
	 *
	 * }//end myFunction()
	 */

    /*
     * function myFunction( $param )
     * {
     *  // phpcs:disable Standard.Category.Sniff -- for reasons.
     *  if ( preg_match( '`[abc]`', $param ) > 0 ) {
     *      do_something();
     *  }
     *  // phpcs:enable Standard.Category.Sniff -- for reasons.
     *
     * }//end myFunction()
     */

    // function myFunction( $param )
    // {
          // phpcs:disable Standard.Category.Sniff -- for reasons.
    //    do_something();
          // phpcs:enable Standard.Category.Sniff -- for reasons.
    // }//end myFunction()
    //

echo 'something'; // @codeCoverageIgnore
echo 'something'; // @codeCoverageIgnoreStart
echo 'something'; // @SuppressWarnings(PHPMD.UnusedLocalVariable)

// Ok!

/* Go! */

// ISO-639-3

			// But override with a different text if any.
			/*
			$id = intval( str_replace( 'hook_name', '', $order_method['method_id'] ) );
			if ( ! empty( $id ) ) {
				$info_text = get_post_meta( $location_id, 'meta_name' );

				if ( ! empty( $info_text ) ) {
					$text = $info_text;
				}

			}
			*/
			// function() { $a = $b; };
