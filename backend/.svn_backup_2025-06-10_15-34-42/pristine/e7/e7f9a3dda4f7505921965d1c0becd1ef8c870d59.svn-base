<?php

require_once('tools.faq.inc.php');

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_TOOL_FAQ_CATEG');

if( isset($_POST['add'], $_POST['name']) ){
	header('Location: category.php?name='.urlencode($_POST['name']));
	exit;
}

if( !empty($_POST['orderby']) ){
	faq_categories_order_update($_POST['order']);

	header('Location: index.php');
	exit;
}

// Bouton Supprimer.
if( isset($_POST['del'], $_POST['cat']) ){
	foreach( $_POST['cat'] as $category_id ){
		faq_categories_del($category_id);
	}

	header('Location: index.php');
	exit;
}

define('ADMIN_PAGE_TITLE', _('Foire Aux Questions').' - '._('Outils'));

// Récupère les catégories.
$categories = faq_categories_get();

$nb_categories = ria_mysql_num_rows($categories);

// Détermine si les catégories sont triées alphabétiquement ou non.
$ordered = faq_categories_get_order();

require_once('admin/skin/header.inc.php');

?>

<h2><?php print _('Foire Aux Questions'); ?></h2>

<form action="index.php" method="post">
	<table id="faq_categories" class="checklist">
		<caption><?php print _('Liste des catégories').' ('.($categories===false || !ria_mysql_num_rows($categories) ? '0' : ria_mysql_num_rows($categories)).')'; ?></caption>
		<thead class="thead-none">
			<tr>
				<th id="faq-sel">
					<input type="checkbox" class="checkbox" onclick="checkAllClick(this);">
				</th>
				<th id="faq-name"><?php print _('Nom de la catégorie'); ?></th>
				<th id="faq-qst" class="align-right"><?php print _('Questions'); ?></th>
				<th id="faq-qst-pub" class="align-right"><?php print _('Questions publiées'); ?></th>
				<?php if( $ordered && gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_CATEG_MOVE') ){ ?>
					<th id="faq-cat-pos"><?php print _('Déplacer'); ?></th>
				<?php } ?>
			</tr>
		</thead>
		<tbody>
			<?php
				if($categories===false || !ria_mysql_num_rows($categories) )
					print '<tr><td colspan="'.( $ordered && gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_CATEG_MOVE') ? 5 : 4 ).'">'._('Aucune catégorie').'</td></tr>';
				else {
					$count = ria_mysql_num_rows($categories); $current = 0;
					while( $r = ria_mysql_fetch_array($categories) ){
						print '
							<tr id="line-' . $r['id'] . '" class="ria-row-orderable">
								<td headers="faq-sel"><input type="checkbox" class="checkbox" name="cat[]" value="'.$r['id'].'" /></td>
						';
						if( gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_CATEG_EDIT') ){
							print '<td headers="faq-name" data-label="'._('Nom de la catégorie :').' "><a href="category.php?cat='.$r['id'].'">'.htmlspecialchars($r['name']).'</a></td>';
						}else{
							print '<td headers="faq-name" data-label="'._('Nom de la catégorie :').' ">'.htmlspecialchars($r['name']).'</td>';
						}
						print '
							<td headers="faq-qst" class="col-numeric" data-label="'._('Questions :').' ">'.ria_number_format($r['questions']).'</td>
							<td headers="faq-qst-pub" class="col-numeric" data-label="'._('Questions publiées :').' ">'.ria_number_format($r['questions_published']).'</td>
						';
						if( $ordered && gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_CATEG_MOVE') ){
							print '<td headers="faq-cat-pos" class="align-center ria-cell-move">';
							print '	<div class="ria-row-catchable" title="'._('Déplacer').'"></div>';
							print '</td>';
						}
						print '</tr>';
						$current++;
					}
				}
			?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="<?php print $ordered && gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_CATEG_MOVE') ? 5 : 4; ?>">
					<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_CATEG_DEL') && $nb_categories ){ ?>
						<button type="submit" class="btn-del" name="del" title="<?php print _('Supprimer les catégories sélectionnées'); ?>" onclick="return confirmFaqCategoryDelList();"><?php print _('Supprimer'); ?></button>
					<?php }
					if( gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_CATEG_ADD') ){ ?>
						<div class="float-right">
							<label for="name"><?php print _('Ajouter une catégorie :'); ?></label>
							<input type="text" id="name" name="name" maxlength="75">
							<input type="submit" name="add" value="<?php print _('Ajouter'); ?>">
						</div>
					<?php } ?>
				</td>
			</tr>
			<?php if( gu_user_is_authorized('_RGH_ADMIN_TOOL_FAQ_CATEG_MOVE') && $nb_categories > 1 ){ ?>
				<tr>
					<td class="tfoot-grey" colspan="<?php print $ordered ? 5 : 4; ?>">
						<label><?php print _('Trier les catégories par ordre :'); ?></label>
						<input type="radio" id="order-0" class="radio" name="order" value="0" <?php print !$ordered ? 'checked' : ''; ?>>
						<label for="order-0"><?php print _('Alphabétique'); ?></label>
						<input type="radio" id="order-1" class="radio" name="order" value="1" <?php print $ordered ? 'checked' : ''; ?>>
						<label for="order-1"><?php print _('Personnalisé'); ?></label>
						<input type="submit" name="orderby" value="<?php print _('Appliquer'); ?>">
					</td>
				</tr>
			<?php } ?>
		</tfoot>
	</table>
</form>

<?php

require_once('admin/skin/footer.inc.php');