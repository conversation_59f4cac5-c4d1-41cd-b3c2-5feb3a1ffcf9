# Changes in PHP_CodeCoverage 3.0

All notable changes of the PHP_CodeCoverage 3.0 release series are documented in this file using the [Keep a CHANGELOG](http://keepachangelog.com/) principles.

## [3.0.2] - 2015-11-12

### Changed

* It is now optional that `@deprecated` code is ignored

## [3.0.1] - 2015-10-06

### Fixed

* Fixed [#391](https://github.com/sebastian<PERSON>mann/php-code-coverage/pull/391): Missing `</abbr>` tag

## [3.0.0] - 2015-10-02

### Changed

* It is now mandatory to configure a whitelist

### Removed

* The blacklist functionality has been removed
* PHP_CodeCoverage is no longer supported on PHP 5.3, PHP 5.4, and PHP 5.5

[3.0.2]: https://github.com/sebastian<PERSON>mann/php-code-coverage/compare/3.0.1...3.0.2
[3.0.1]: https://github.com/sebastian<PERSON>mann/php-code-coverage/compare/3.0.0...3.0.1
[3.0.0]: https://github.com/sebastian<PERSON>mann/php-code-coverage/compare/2.2...3.0.0

