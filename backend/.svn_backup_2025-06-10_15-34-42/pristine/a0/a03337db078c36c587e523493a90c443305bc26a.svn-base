<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/monitoring.proto

namespace GPBMetadata\Google\Api;

class Monitoring
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0a93030a1b676f6f676c652f6170692f6d6f6e69746f72696e672e70726f" .
            "746f120a676f6f676c652e61706922ec010a0a4d6f6e69746f72696e6712" .
            "4b0a1570726f64756365725f64657374696e6174696f6e7318012003280b" .
            "322c2e676f6f676c652e6170692e4d6f6e69746f72696e672e4d6f6e6974" .
            "6f72696e6744657374696e6174696f6e124b0a15636f6e73756d65725f64" .
            "657374696e6174696f6e7318022003280b322c2e676f6f676c652e617069" .
            "2e4d6f6e69746f72696e672e4d6f6e69746f72696e6744657374696e6174" .
            "696f6e1a440a154d6f6e69746f72696e6744657374696e6174696f6e121a" .
            "0a126d6f6e69746f7265645f7265736f75726365180120012809120f0a07" .
            "6d65747269637318022003280942710a0e636f6d2e676f6f676c652e6170" .
            "69420f4d6f6e69746f72696e6750726f746f50015a45676f6f676c652e67" .
            "6f6c616e672e6f72672f67656e70726f746f2f676f6f676c65617069732f" .
            "6170692f73657276696365636f6e6669673b73657276696365636f6e6669" .
            "67a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

