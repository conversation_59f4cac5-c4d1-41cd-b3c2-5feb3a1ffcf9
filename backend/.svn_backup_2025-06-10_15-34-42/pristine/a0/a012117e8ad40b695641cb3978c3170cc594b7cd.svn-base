<?php

/**	\defgroup oms_ord_alerts Alertes emails
 * 	\ingroup model_orders
 * 	Ce groupe continent les fonctions envoyants des alertes emails liées aux commandes
 * 	@{
 */

/** Retourne les documents ANFR des produits d'une commande
 * @param	int		$ord_id	Obligatoire, Identifiant d'une commande
 * @return	array|bool		Tableau des documents, false sinon
 */
function navicom_order_get_products_documents($ord_id){

	if( !is_numeric($ord_id) || !$ord_id ){
		return false;
	}
	$robjects = doc_objects_get(0, CLS_ORDER, $ord_id, 46350);

	if( !ria_mysql_num_rows($robjects) ){
		return false;
	}
	$docs = [];

	while($obj = ria_mysql_fetch_assoc($robjects) ){
		$prd_id = fld_object_values_get($obj['doc_id'], 101778);
		$no_mmsi = fld_object_values_get($obj['doc_id'], 101779);
		if( !is_numeric($prd_id) || !$prd_id || !is_string($no_mmsi) || trim($no_mmsi) == '' ){
			continue;
		}
		$prd_id = (int)$prd_id;
		$obj['prd_id'] = $prd_id;
		$obj['mmsi_number'] = trim($no_mmsi);
		$docs[$prd_id][] = $obj;
	}

	return count($docs) ? $docs : false;
}

/**	Si le produit est un produit MMSI
 * @param	int		$prd	Obligatoire, Identifiant d'un produit
 * @return	bool	True si c'est un produit MMSI, false sinon
 */
function navicom_product_is_mmsi($prd){

	if( !is_numeric($prd) || !$prd || !prd_products_exists($prd) ){
		return false;
	}

	$r = fld_object_values_get($prd, 101777);

	return $r && in_array($r, ['Oui', 'OUI', 'oui', '1', 'Yes', 'YES', 'yes']);

}

// \cond onlyria
/**	Envoi un email au propriétaire du site pour l'informer d'une nouvelle commande.
 *
 * @param  int $ord Obligatoire, Identifiant de la commande
 * @return bool true en cas de succès, false autrement
 */
function ord_alert_shop_owner( $ord ){
	global $config;

	if( !ord_orders_exists($ord) ) return false;

	$r_order = ord_orders_get_with_adresses( 0, $ord );
	if( !$r_order || !ria_mysql_num_rows($r_order) ){
		return false;
	}
    if( !empty($config['bo_url']) ){
        $http_host_ria = $config['bo_url'];
    } else {
        $http_host_ria = 'riashop-'.$_SERVER['HTTP_HOST'];
    }	
	$order = ria_mysql_fetch_array( $r_order );

	// hack Chazelles configuration magasin
	$cfg_email_name = 'ord-owner';
	if( $config['tnt_id']==8 ){
		if( substr(gu_users_get_parent_ref($order['user']),0,2)=='US' ){
			$cfg_email_name = 'ord-owner-store';
		}
	}

	// Charge la configuration de l'alerte email (expéditeur, destinataire, copies, etc...)
	$rcfg = cfg_emails_get($cfg_email_name, $order['wst_id']);
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);

	$ord_pmts = ord_orders_promotions_get($order['id']);

	$email = new Email();

	if( $config['tnt_id'] == 3 && $config['wst_id'] == 7 ){
		// require_once $config['site_dir'].'/include/view.site.inc.php';
		$docs = navicom_order_get_products_documents($order['id']);
	}

	if( $config['tnt_id'] == 3 && $config['wst_id']==6 && $order['piece'] == '' ){
		$email->setSubject( 'Nouvelle commande Goodies '.str_pad($ord,8,'0',STR_PAD_LEFT) );
	}else{

		if( !is_numeric($order['wst_id']) || $order['wst_id'] <= 0 ){
			$order['wst_id'] = $config['wst_id'];
		}

		$tmp_wst_id = $config['wst_id'];
		$config['wst_id'] = $order['wst_id'];
		cfg_variables_load($config, array('alert_owner_subject','notify-seller-owner'));
		$config['wst_id'] = $tmp_wst_id;

		$subject = 'Nouvelle commande';
		if( isset($config['alert_owner_subject']) ){
			$subject = $config['alert_owner_subject'];
			$subject = str_replace('#PARAM[ord_id]#', str_pad($ord,8,'0',STR_PAD_LEFT), $subject);
			$subject = str_replace('#PARAM[ord_piece]#', $order['piece'], $subject);
			if( $config['tnt_id'] == 4 && $config['wst_id'] == 8 && strpos($order['piece'], 'WBC') === 0 ) {
				$subject = str_replace('Extranet', 'Internet', $subject);
			}
			//ajout de la référence
			if( isset($config['ord_notify_supplier_subject_ref']) && $config['ord_notify_supplier_subject_ref'] ){
				$subject .= " ".$order['ref'];
			}
		}

		if( $order['state_id'] == _STATE_DEVIS ){
			$subject = "Nouveau devis ".str_pad($ord,8,'0',STR_PAD_LEFT).'/'.$order['piece'];
		}

		// Ajout du département pour la notification envoyée à Chadog
		if( $config['tnt_id'] == 171 && $order['wst_id'] == 232 ){
			$dept = substr( str_pad($order['inv_postal_code'], 5, '0', STR_PAD_LEFT), 0, 2 );
			$subject = '['.htmlspecialchars( $dept ).'] '.$subject;
		}

		$email->setSubject( $subject );
	}

	// Configuration spécifique pour Proloisirs concernant les destinataires du mail (en fonction des ADV)
	if( $config['tnt_id'] == 4 ){

		// Associations des ADV aux codes de configuration mail
		// Ce tableau de configuration est aussi utilisé dans l'extranet : /users/sav/sav.php et /users/sav/edit.php
		$ar_adv_emails = [];
		$cat_root = $order['wst_id'] == 30 ? 3158 : 16096;

		$data_adv_email = explode("\n", fld_object_values_get( $cat_root, 101826 ) );
		if( is_array($data_adv_email) ){
			foreach( $data_adv_email as $one_adv_email ){
				$temp = explode( ':', $one_adv_email );

				if( is_array($temp) && count($temp) == 2 ){
					$ar_adv_emails[ trim($temp[0]) ] = trim( $temp[1] );
				}
			}
		}

		$ADV_name = '';

		// Bloc de récupération des configurations mails spécifique en fonction du contenu du champ avancé "ADV commercial bureau" renseigné sur la fiche client du magasin rattaché à la commande.
		// Cette information est récupérée différement en fonction du site sur lequel la commande est passée
		switch( $order['wst_id'] ){
			case 8 : { // Extranet Proloisirs
				// Vérification de l'existance dans la config des identifiants de champ avancé
				// if( !isset($config['fld_usr_adv']) ){
				// 	break;
				// }

				// L'utilisateur associé à la commande est un revendeur, donc le champ avancé "ADV commercial bureau" se trouve dans sa fiche client
				if( !is_numeric($order['user']) || $order['user'] <= 0 ){
					break;
				}
				// Récupère le nom de l'ADV via le champ avancé fld_usr_adv
				$ADV_name = fld_object_values_get($order['user'], 4230, '', false, true);
				break;
			}
			case 30 : { // Site public
				// Vérification de l'existance dans la config des identifiants de champ avancé
				if( !isset($config['fld_ord_str'], $config['fld_ref_revendeur'], $config['fld_usr_adv']) ){
					break;
				}

				$store_id = fld_object_values_get($order['id'], $config['fld_ord_str'], '', false, true);
				if( is_numeric($store_id) && $store_id > 0 ){
					// Récupère la référence du revendeur via le champ avancé ref_revendeur
					$reseller_ref = fld_object_values_get($store_id, $config['fld_ref_revendeur'], '', false, true);
					$reseller_id = gu_users_by_ref( $reseller_ref );

					if( is_numeric($reseller_id) && $reseller_id > 0 ){
						// Récupère le nom de l'ADV via le champ avancé fld_usr_adv
						$ADV_name = fld_object_values_get($reseller_id, $config['fld_usr_adv'], '', false, true);
					}
				}
				break;
			}
		}

		// Si on a bien récupéré un ADV sur la fiche client du revendeur, et que celui-ci est associé à une notification de commande spécifique, alors on charge la bonne configuration mail
		// Sinon on garde la configuration mail par défaut des notifications de commande
		if( trim($ADV_name) && array_key_exists($ADV_name, $ar_adv_emails) ){
			// Récupère la configuration mail
			$rcfg = cfg_emails_get($ar_adv_emails[$ADV_name], $order['wst_id']);
			if( ria_mysql_num_rows($rcfg) ) {
				$from = $cfg['from'];
				$cfg = ria_mysql_fetch_assoc($rcfg);

				// Si l'expéditeur n'est pas renseigné dans la configuration, on garde celui de la notification par défaut
				if( !trim($cfg['from']) ){
					$cfg['from'] = $from;
				}
			}
		}
	}

	$email->setFrom( $cfg['from'] );
	$email->addTo( $cfg['to'] );
	$email->addCC( $cfg['cc'] );
	$email->addBcc( $cfg['bcc'] );
	$email->setReplyTo( $cfg['reply-to'] );

	// T&T FOODS Expéditeur du mail spécifique si seller_id renseigné
	if ($config['tnt_id'] == 66){
		$r_states = ord_orders_states_get( $ord, _STATE_WAIT_PAY );
		if ($r_states && ria_mysql_num_rows($r_states) && $author_id = ria_mysql_result($r_states, 0, 'usr_id')){ // Si on a un résultat et si l'auteur est renseigné
			$author_email = gu_users_get_email( $author_id );
			if (isemail($author_email)){
				$email->setFrom( $author_email );
			}
		} elseif ($order['seller_id']){
			$seller_email = gu_users_get_email( $order['seller_id'] );
			if(isemail($seller_email)){
				$email->setFrom( $seller_email );
			}
		}
	}

	// Freevox (paiement par CB, copie CC supplémentaire)
	if( $config['tnt_id'] == 39 ){
		if( $order['state_id'] != _STATE_DEVIS &&  $order['pay_id'] == _PAY_CB ){
			$email->addCC('<EMAIL>');
		}
	}

	// Si le paiement à été effectué par carte bleue, inclut la pièce jointe contenant les infos
	if( $order['pay_id']==1 ){
		// Ajoute les infos de paiement en pièce jointe
		$email->addAttachment( $config['ord-xml-dir'].'/'.str_pad($ord,8,'0',STR_PAD_LEFT).'.rcb' );
	}

	if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
		$config['email_html_header'] = str_replace('%site%', $config['site_url'], $config['email_html_header']);
		$config['email_html_footer'] = str_replace('%site%', $config['site_url'], $config['email_html_footer']);
	}

	// Composition du message
	$email->addHtml( isset($config['email_html_header_order']) && $config['email_html_header_order']!='' ? $config['email_html_header_order'] : $config['email_html_header'] );

	$email->openTable(570);

	if( $order['state_id'] == _STATE_DEVIS ){
		$email->openTableRow(); $email->addCell( '<b>Devis</b>', 'left', 2 ); $email->closeTableRow();
	}else{
		$email->openTableRow(); $email->addCell( '<b>Commande</b>', 'left', 2 ); $email->closeTableRow();
	}
	$email->openTableRow(); $email->addCell( 'Date :' ); $email->addCell( $order['date'] ); $email->closeTableRow();

	$email->openTableRow(); $email->addCell( 'Référence Internet :' ); $email->addCell( str_pad($ord,8,'0',STR_PAD_LEFT) ); $email->closeTableRow();

	$email->openTableRow(); $email->addCell( 'Référence Gescom :' ); $email->addCell( $order['piece'] ); $email->closeTableRow();

	if( trim($order['ref']) ){
		$email->openTableRow(); $email->addCell( 'Référence client :' ); $email->addCell( $order['ref'] ); $email->closeTableRow();
	}

	$store = $is_order_store = false;
	if( $config['tnt_id'] == 1 && $config['wst_id'] == 1 ){
		$is_order_store = fld_object_values_get($order['id'], $config['fld_ord_code_societe']);
		$store_id = fld_object_values_get($order['id'], $config['fld_ord_str_starred']);
		$price_group = fld_object_values_get($order['id'], $config['fld_price_group']);

		// Affichage de la notif en HT ou TTC selon le groupe de prix
		if( trim($price_group) == '' || trim($price_group) == 'BS-PUB TTC' ){
			$config['alert_owner_ttc'] = true;
		}else{
			$config['alert_owner_ttc'] = false;
		}

		if( $store_id ){
			$stores = dlv_stores_get($store_id);
			if ($stores && mysql_num_rows($stores)) {
				$store = mysql_fetch_assoc($stores);
			}
		}
		if ( $is_order_store && $store ) {
			$email->openTableRow();
			$email->addCell( 'Magasin C&C :' );
			$email->addCell( $store['name'].' - '.$is_order_store );
			$email->closeTableRow();
		}
	}

	$rusr = gu_users_get( $order['user'] );
	if( $rusr!=false ){
		if( ria_mysql_num_rows($rusr) ){
			$usr = ria_mysql_fetch_array($rusr);

			// Notifie également les dépots de stockage
			if( isset( $config['notify-deposit-owner'] ) && $config['notify-deposit-owner'] ){
				$rdps = prd_deposits_get( $order['dps'] );
				if( $rdps && ria_mysql_num_rows($rdps) ){
					$dps = ria_mysql_fetch_assoc($rdps);
					if( trim($dps['email']) != '' ){
						$email->addBcc($dps['email']);
					}
				}
			}

			// Notifie également le propriétaire (si activé)
			if( isset( $config['notify-seller-owner'] ) ){

				if( $config['notify-seller-owner'] ){
					$seller_id = array();
					if( !$usr['seller_id'] ){

						$rusr = gu_users_get( $usr['parent_id'] );
						if( $rusr && ria_mysql_num_rows($rusr) ){
							$parent = ria_mysql_fetch_array( $rusr );
							if( $parent['seller_id'] ){
								$seller_id[] = $parent['seller_id'];
							}
						}

					}else{
						$seller_id[] = $usr['seller_id'];
					}

					// récupère la hiérachie des comptes parentes
					$rparents = rel_relations_hierarchy_parents_get_ids(REL_USR_HIERARCHY, $usr['id']);
					if( $rparents && sizeof($parents) ){
						$rusr = gu_users_get( $parents );
						if( $rusr && ria_mysql_num_rows($rusr) ){
							$parent = ria_mysql_fetch_array( $rusr );
							if( $parent['seller_id'] ){
								$seller_id[] = $parent['seller_id'];
							}
						}
					}

					if( sizeof($seller_id) ){
						foreach( $seller_id as $id ){
							$rseller = gu_users_get( 0, '', '', PRF_SELLER, '', 0, '', false, false, $id );
							if( $rseller && ria_mysql_num_rows( $rseller )==1 ){
								if( $seller = ria_mysql_fetch_assoc( $rseller ) ){
									if( $seller['email'] ){
										$email->addBcc( $seller['email'] );
									}

									if( trim($seller['alert_cc'])!='' ){
										$email->addBcc( $seller['alert_cc'] );
									}
								}
							}
						}
					}
				}
			}
		
		
		$url_user = 'https://'.$http_host_ria.'/admin/customers/edit.php?usr='.$usr['id'];
		$email->openTableRow();
		$email->addCell( 'Compte client :' );
		$cellContent = '<a href="'.$url_user.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=user-general">';
		$cellContent .= '<img border="0" align="absmiddle" src="https://'.$http_host_ria.'/admin/images/sync/'.( $usr['is_sync'] ? 1 : 0 ).'.svg" width="16" height="16" title="'.( $usr['is_sync'] ? 'Ce compte client est synchronisé avec votre gestion commerciale' : 'Ce compte client n\'existe que dans votre boutique en ligne' ).'" />';
		$cellContent .= ( $usr['ref'] ? $usr['ref'] : $usr['id'] ).'</a>';
		$cellContent .= '<br />';
		$cellContent .= '
			<small>
				<a href="'.$url_user.'&amp;tab=orders&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=user-orders">Commandes</a>
				| <a href="'.$url_user.'&amp;tab=reviews&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=user-reviews">Avis</a>
				| <a href="'.$url_user.'&amp;tab=delayed&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=user-delayed">Reliquats</a>
				| <a href="'.$url_user.'&amp;tab=stats&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=user-stats">Statistiques</a>
			</small>
		';
		$email->addCell( $cellContent );
		$email->closeTableRow();

		$email->openTableRow();
		$email->addCell( 'Adresse email :' );
		$email->addCell( '<a href="mailto:'.$usr['email'].'">'.$usr['email'].'</a>' );
		$email->closeTableRow();

		if( trim($order['inv_phone']) ){
			$email->openTableRow();
				$email->addCell( 'Téléphone :' );
				$email->addCell( $order['inv_phone'] );
				$email->closeTableRow();
			}

			// numéro de portable si renseigné
			if( trim($order['inv_mobile']) ){
				$email->openTableRow();
				$email->addCell( 'Mobile :' );
				$email->addCell( $order['inv_mobile'] );
				$email->closeTableRow();
			}

			// affiche le commercial
			if( is_numeric($order['seller_id']) && $order['seller_id'] > 0 ){
				$rseller = gu_users_get(0, "", "", PRF_SELLER, "", 0, "", false, false, $order['seller_id']);
				if( $rseller && ria_mysql_num_rows($rseller) ){
					$seller = ria_mysql_fetch_assoc($rseller);

					$email->openTableRow();
					$email->addCell( 'Représentant :' );

					$cellContent = "";
					$url_seller = 'https://'.$http_host_ria.'/admin/customers/edit.php?usr='.$seller['id'];
					$cellContent = '<a href="'.$url_seller.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=seller-general">';
					$cellContent .= '<img border="0" align="absmiddle" src="https://'.$http_host_ria.'/admin/images/sync/'.( $seller['is_sync'] ? 1 : 0 ).'.svg" width="16" height="16" title="'.( $seller['is_sync'] ? 'Ce compte client est synchronisé avec votre gestion commerciale' : 'Ce compte client n\'existe que dans votre boutique en ligne' ).'" />';
					$cellContent .= $seller['title_name']." ".$seller['adr_firstname']." ".$seller['adr_lastname'] .'</a>';

					$email->addCell( $cellContent );
					$email->closeTableRow();
				}
			}

			// Affichage de la date de livraison
			if (trim($order['date_livr_en']) != '' && wst_websites_is_fdv_app($config['tnt_id'], $order['wst_id'])) {
				$email->openTableRow();
				$email->addCell('Date de livraison :');
				$email->addCell(date('d/m/Y', strtotime($order['date_livr_en'])));
				$email->closeTableRow();
			}
		}
	}
	if( isset($config['inv_adr_parent_used']) && $config['inv_adr_parent_used'] == 1 ){
		$parent_id = gu_users_get_parent_id( $order['user'] );
		if( $parent_id ){
			$r_parent_adress = gu_adresses_get( $parent_id, 0, '', array(), false, '', '', '', '', '', '', '', false, false, false, null, true);
			if( $r_parent_adress && ria_mysql_num_rows($r_parent_adress) ){
				$parent_adress = ria_mysql_fetch_assoc($r_parent_adress);

				$order['dlv_lastname']		 = $parent_adress['lastname'];
				$order['dlv_firstname']		 = $parent_adress['firstname'];
				$order['dlv_society']		 = $parent_adress['society'];
				$order['inv_address1']		 = $parent_adress['address1'];
				$order['inv_address2']		 = $parent_adress['address2'];
				$order['inv_address3']		 = $parent_adress['address3'];
				$order['inv_postal_code']	 = $parent_adress['postal_code'];
				$order['inv_city']		 	 = $parent_adress['city'];
				$order['inv_country']		 = $parent_adress['country'];
			}
		}
	}
	// Adresse de facturation
	$email->openTableRow(); $email->addCell( '<b>Adresse de facturation</b>', 'left', 2 ); $email->closeTableRow();
	if( trim($order['inv_lastname']) ){
		$email->openTableRow(); $email->addCell( _('Nom :') ); $email->addCell( $order['inv_lastname'] ); $email->closeTableRow();
	}
	if( trim($order['inv_firstname']) ){
		$email->openTableRow(); $email->addCell( _('Prénom :') ); $email->addCell( $order['inv_firstname'] ); $email->closeTableRow();
	}
	if( trim($order['inv_society']) ){
		$email->openTableRow(); $email->addCell( _('Société :') ); $email->addCell( $order['inv_society'] ); $email->closeTableRow();
	}
	$email->openTableRow(); $email->addCell( _('Adresse :') );
	$inv_adress = array(
		$order['inv_address1'],
		$order['inv_address2'],
		$order['inv_address3'],
		$order['inv_postal_code'].' '.$order['inv_city'],
		$order['inv_country']
	);

	$inv_adress = array_filter($inv_adress,function($item){
		return !(is_null($item) || trim($item)=='');
	});

	$email->addCell( implode('<br>', $inv_adress) );
	$email->closeTableRow();

	if (trim($order['inv_phone']) != '') {
		$email->openTableRow();
		$email->addCell('Téléphone :');
		$email->addCell($order['inv_phone']);
		$email->closeTableRow();
	}

	if (trim($order['inv_mobile']) != '') {
		$email->openTableRow();
		$email->addCell('Téléphone :');
		$email->addCell($order['inv_mobile']);
		$email->closeTableRow();
	}

	$srv = null;
	if( $config['tnt_id']!=26 ){
		// Options de livraison
		if ($order['rly_id'] || is_numeric($order['str_id']) || is_numeric($order['srv_id']) ) {
			$email->openTableRow();
			$email->addCell('<b>Options de livraison</b>', 'left', 2);
			$email->closeTableRow();
			$email->openTableRow();
			$email->addCell('Type :');

			$dlv_ponton = dsp_plage_get_plage_for_order($order['id']);

			// Ajout de la partie "Livraison sur ponton" pour Bigship.
			if( $config['tnt_id'] == 1 && $dlv_ponton ){
				$email->addCell('Livraison sur ponton');
				$email->closeTableRow();

				$email->openTableRow();
				$email->addCell('Date');
				$email->addCell(dateformatcomplet($dlv_ponton['date']->getTimestamp()));
				$email->closeTableRow();

				$email->openTableRow();
				$email->addCell('Plage horraire');
				$email->addCell($dlv_ponton['dsp_debut'].' - '.$dlv_ponton['dsp_fin']);
				$email->closeTableRow();

				$email->openTableRow();
				$email->addCell('Numéro de ponton');
				$email->addCell($dlv_ponton['ponton_number']);
				$email->closeTableRow();

				$email->openTableRow();
				$email->addCell('Numéro de bateau');
				$email->addCell($dlv_ponton['boat_number']);
				$email->closeTableRow();
			}else{
				if( $order['rly_id'] ){
					$rly = ria_mysql_fetch_array(dlv_relays_get_simple($order['rly_id']));

					$email->addCell('Livraison au point-relais '.$rly['ref'].' - '.$rly['name'].' ('.$rly['zipcode'].')');
				}elseif( is_numeric($order['str_id']) ){
					$str = ria_mysql_fetch_array(dlv_stores_get( $order['str_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ));
					$email->addCell( 'Livraison au magasin '.$str['name'].' ('.$str['zipcode'].')' );

					if ((isset($config['notify_owner_always_delivery']) && $config['notify_owner_always_delivery'])) {
						$order['dlv_lastname'] = '';
						$order['dlv_firstname'] = '';
						$order['dlv_society'] = $str['name'];
						$order['dlv_address1'] = $str['address1'];
						$order['dlv_address2'] = $str['address2'];
						$order['dlv_address3'] = '';
						$order['dlv_postal_code'] = $str['zipcode'];
						$order['dlv_city'] = $str['city'];
						$order['dlv_country'] = $str['country'];
						$order['dlv_phone'] = $str['phone'];
						$order['dlv_mobile'] = '';
					}
				}elseif( is_numeric($order['srv_id']) ){
					$srv = ria_mysql_fetch_array(dlv_services_get($order['srv_id']));
					$email->addCell( 'Livraison '.str_replace('Livraison','',$srv['name']) );
				}else{
					$email->addCell('Non sélectionné');
				}
			}

			$email->closeTableRow();
		}

		if( isset($config['alert_owner_show_deposit']) && $config['alert_owner_show_deposit'] && $order['dps'] ){
			$rdps = prd_deposits_get($order['dps']);
			if( $rdps && ria_mysql_num_rows($rdps) ){
				$dps = ria_mysql_fetch_assoc($rdps);

				$email->openTableRow();
				$email->addCell( 'Dépôt :' );
				$email->addCell( $dps['name'] );
				$email->closeTableRow();
			}
		}
		if( isset($config['alert_owner_show_package']) && $config['alert_owner_show_package'] && $order['pkg_id']){
			$rpkg = dlv_packages_get($order['pkg_id']);
			if( $rpkg && ria_mysql_num_rows($rpkg) ){
				$pkg = ria_mysql_fetch_assoc($rpkg);

				$email->openTableRow();
				$email->addCell( 'Emballage :' );
				$email->addCell( $pkg['name'] );
				$email->closeTableRow();
			}
		}
		if( isset($config['alert_owner_show_date_livr']) && $config['alert_owner_show_date_livr'] ){
			$email->openTableRow();
			$email->addCell( 'Date souhaitée :' );
			$email->addCell( trim($order['date_livr']) ? $order['date_livr'] : 'N.C.' );
			$email->closeTableRow();
		}
		if( trim($order['dlv-notes']) ){
			$email->openTableRow(); $email->addCell( 'Consignes de livraison :' ); $email->addCell( nl2br($order['dlv-notes']) ); $email->closeTableRow();
		}

		// Action spécifique pour Proloisirs. On récupère la référence du magasin afin de l'afficher sous le type de livraison
		if( $config['tnt_id'] == 4 && $order['wst_id'] == 30 ) {
			$ord_ref_user = false;
			// On récupère le champ avancé "Référence Client" de la commande
			if( isset($config['fld_ord_ref_user']) ){
				$ord_ref_user = fld_object_values_get($order['id'], $config['fld_ord_ref_user'], '', false, true);
			}
			//Si on a pas réussi à récupérer le champ "Référence Client", on récupère le champ avancé "Ref revendeur" (la valeur est la même)
			if( !$ord_ref_user && isset($config['fld_ref_revendeur']) ){
				$ord_ref_user = fld_object_values_get($order['id'], $config['fld_ref_revendeur'], '', false, true);
			}
			//Si on a encore pas pu récupérer la référence, on la récupère via le champ avancé "Ref revendeur" associé au magasin
			if( !$ord_ref_user && isset($config['fld_ord_str'], $config['fld_ref_revendeur']) ){
				$store_id = fld_object_values_get($order['id'], $config['fld_ord_str'], '', false, true);
				if( is_numeric($store_id) && $store_id > 0 ){
					// Récupère la référence du revendeur via le champ avancé ref_revendeur
					$ord_ref_user = fld_object_values_get($store_id, $config['fld_ref_revendeur'], '', false, true);
				}
			}

			//Ajout dans le tableau "Options de livraison" de la référence client du revendeur, si renseignée, et dans le cas d'une livraison magasin
			if( $ord_ref_user && is_numeric($order['str_id']) ){
				$email->openTableRow(); $email->addCell( 'Référence Client :' ); $email->addCell( $ord_ref_user ); $email->closeTableRow();
			}elseif( $ord_ref_user && !is_numeric($order['str_id']) ){ // Sinon si livraison domicile, on ajoute les informations du magasin associé à la commande
				$email->openTableRow(); $email->addCell( '<b>Informations sur le magasin référent</b>', 'left', 2 ); $email->closeTableRow();
				$email->openTableRow(); $email->addCell( 'Référence Client :' ); $email->addCell( $ord_ref_user ); $email->closeTableRow();

				// Récupération de l'id RiaShop du compte client
				$reseller_id = gu_users_by_ref( $ord_ref_user );
				$reseller_name = '';
				// Puis récupération du nom
				if( is_numeric($reseller_id) && $reseller_id > 0 ){
					$reseller_name = gu_users_get_name($reseller_id);
				}

				// Affichage de la raison sociale du revendeur, si renseignée
				if( trim($reseller_name) != '' ){
					$email->openTableRow(); $email->addCell( 'Raison sociale :' ); $email->addCell( $reseller_name ); $email->closeTableRow();
				}
			}
		}

		// Adresse de livraison
		if( ((isset($config['notify_owner_always_delivery']) && $config['notify_owner_always_delivery']) || $order['inv_id']!=$order['dlv_id']) && !$order['rly_id'] ){
			$email->openTableRow(); $email->addCell( '<b>Adresse de livraison</b>', 'left', 2 ); $email->closeTableRow();
			if( trim($order['dlv_lastname']) ){
				$email->openTableRow(); $email->addCell( 'Nom :' ); $email->addCell( $order['dlv_lastname'] ); $email->closeTableRow();
			}
			if( trim($order['dlv_firstname']) ){
				$email->openTableRow(); $email->addCell( 'Prénom :' ); $email->addCell( $order['dlv_firstname'] ); $email->closeTableRow();
			}
			if( trim($order['dlv_society']) ){
				$email->openTableRow(); $email->addCell( 'Société :' ); $email->addCell( $order['dlv_society'] ); $email->closeTableRow();
			}
			$email->openTableRow(); $email->addCell( 'Adresse :' );

			$dlv_adress = array(
				$order['dlv_address1'],
				$order['dlv_address2'],
				$order['dlv_address3'],
				$order['dlv_postal_code'].' '.$order['dlv_city'],
				$order['dlv_country']
			);

			$dlv_adress = array_filter($dlv_adress,function($item){
				return !(is_null($item) || trim($item)=='');
			});

			$email->addCell( implode('<br>', $dlv_adress) );
			$email->closeTableRow();

			if (trim($order['dlv_phone']) != '') {
				$email->openTableRow();
				$email->addCell( 'Téléphone :' );
				$email->addCell( $order['dlv_phone'] );
				$email->closeTableRow();
			}

			if (trim($order['dlv_mobile']) != '') {
				$email->openTableRow();
				$email->addCell( 'Téléphone :' );
				$email->addCell( $order['dlv_mobile'] );
				$email->closeTableRow();
			}
		}
	}

	if( $order['opt-gift']){
		$email->openTableRow(); $email->addCell( '<b>Option cadeau</b>', 'left', 2 );
		$email->openTableRow(); $email->addCell( 'Option cadeau :' ); $email->addCell( $order['opt-gift'] ? 'Oui':'Non' ); $email->closeTableRow();
		$email->openTableRow(); $email->addCell( 'Message :' ); $email->addCell( nl2br($order['opt-gift-msg']) ); $email->closeTableRow();
	}

	$pay_name = ord_payment_types_get_name($order['pay_id']);
	if( $config['tnt_id'] == 40 ){
		if( !empty($ord_pmts) ){
			foreach($ord_pmts as $pmt ){
				if ($config['tnt_id'] == 40 && $pmt['pmt_type'] == _PMT_TYPE_GIFTS) {
					$pay_name .= ' + Carte Cadeau';
				}
			}
		}
	}
	if ($pay_name) {
		$email->openTableRow(); $email->addCell( '<b>Règlement</b>', 'left', 2 );
		$email->openTableRow(); $email->addCell( 'Mode de règlement :' ); $email->addCell( $pay_name ); $email->closeTableRow();
	}

	if( trim($order['comments']) ){
		$email->openTableRow(); $email->addCell( '<b>Commentaires</b>', 'left', 2 );
		$email->openTableRow(); $email->addCell( 'Commentaire commande :' ); $email->addCell( $order['comments'] ); $email->closeTableRow();
	}

	if( isset($config['alert_owner_show_sign']) && $config['alert_owner_show_sign'] ){
		if( $rsign = ord_orders_signature_get( $order['id'] ) ){
			if( $sign = ria_mysql_fetch_assoc($rsign) ){

				$sign_name = $sign['firstname'] .' - '. $sign['lastname'] ;
				if( $sign['usr_id'] > 0 ){
					$rusr = gu_users_get($sign['usr_id']);
					if( $rusr && ria_mysql_num_rows($rusr) ){
						$usr = ria_mysql_fetch_assoc($rusr);
						$sign_name = $usr['adr_firstname'] .' - '. $usr['adr_lastname'] ;
					}
				}

				$email->openTableRow();
				$email->addCell( 'Signataire :' );
				$email->addCell( $sign_name );
				$email->closeTableRow();
			}
		}
	}

	//origine de la commande
	if( isset($config['email_ord_origin']) && $config['email_ord_origin'] ){
		$stats = stats_origins_get( $order['id'], CLS_ORDER );
		if( $stats && ria_mysql_num_rows($stats) ){
			$stat = ria_mysql_fetch_array( $stats );

			$source = $stat['source'];
			$term = $medium = $prd = '';
			if ($stat['source'] === 'amazon') {
				$source = 'Amazon';
			} elseif( $stat['source']=='priceminister' ) {
				$source = 'PriceMinister';
			} elseif( $stat['source']=='(direct)' ) {
				$source = 'Accès direct';
			} elseif( $stat['medium']=='organic' ) {
				$source = 'Référencement naturel';
				$term = $stat['term'];
			} elseif( $stat['name']=='(referral)' && $stat['medium']=='referral' ) {
				$source = 'Site Référent';
				$term = $stat['source'].$stat['content'];
			} elseif( $stat['source']=='google' && $stat['medium']=='cpc' ) {
				$source = 'Google Adwords';
				$term = $stat['term'];
				if( trim($stat['term']) ){
					$rprd = prd_products_get_byref( $stat['term'] );
				}
			} else {
				$rctr = ctr_comparators_get_bysource( $stat['source'] );
				if( $rctr && ria_mysql_num_rows($rctr) ){
					$source = ria_mysql_result( $rctr, 0, 'name' );
				}
				if( trim($stat['term']) ){
					$rprd = prd_products_get_byref( $stat['term'] );
				}
			}

			// détermine les parties de "stats_origin" qui déclenchent l'affichage de la rubrique
			$show_src = trim($source);
			$show_prd = isset($rprd) && ria_mysql_num_rows($rprd);
			$show_term = trim($term) && !$show_prd; // mutuellement exclusif avec $show_prd
			$show_ip = trim($stat['ip']);

			if( $show_src || $show_prd || $show_term || $show_ip ){

				// intitulé de la rubrique
				$email->openTableRow();
				$email->addCell( '<b>Origine de la commande</b>', 'left', 2 );
				$email->closeTableRow();

				// source
				if( $show_src ){
					$email->openTableRow();
					$email->addCell( 'Source :' );
					$email->addCell( $source );
					$email->closeTableRow();
				}

				// produit exporté
				if( $show_prd ){
					$p = ria_mysql_fetch_assoc($rprd);
					$p['title'] = trim($p['title']) ? $p['title'] : $p['name'];
					$product_info = view_prd_is_sync( $p ).$p['ref'].' - '.$p['title'];
					if( $rcat = prd_products_categories_get( $p['id'] ) ){
						if( $cat = ria_mysql_fetch_assoc($rcat) ){
							$product_info = view_prd_is_sync( $p ).' <a href="https://'.$http_host_ria.'/admin/catalog/product.php?cat='.$cat['cat'].'&prd='.$p['id'].'" target="_bank">'.$p['ref'].' - '.$p['title'].'</a>';
						}
					}
					$email->openTableRow();
					$email->addCell( 'Produit exporté :' );
					$email->addCell( $product_info );
					$email->closeTableRow();
				}

				// terme de recherche
				if( $show_term ){
					$email->openTableRow();
					if( $stat['name'] == '(referral)' ){
						$link = trim($stat['source'])!='' && strstr($stat['content'], $stat['source']) ? $stat['content']  : 'http://'.$stat['source'].$stat['content'];
						$email->addCell( 'Page d\'origine :' );
						$email->addCell( '<a target="_blank" href="'.htmlspecialchars($link).'">'.htmlspecialchars($link).'</a>' );
					}else{
						$email->addCell( 'Recherche effectuée :' );
						$email->addCell( $term );
					}
					$email->closeTableRow();
				}

				// première visite o/n (ne déclenche pas l'affichage de la rubrique à elle seule)
				if( trim($stat['first_visit']) ){
					$first_visit = strtotime( $stat['first_visit'] );
					$email->openTableRow();
					$email->addCell( 'Première visite : ' );
					$email->addCell( date( 'd/m/Y à H:i', $first_visit ) );
					$email->closeTableRow();
				}

				// nombre de visites (ne déclenche pas l'affichage de la rubrique à elle seule)
				if( is_numeric($stat['times_visited']) ){
					$email->openTableRow();
					$email->addCell( 'Nombre de visites</span> :' );
					$email->addCell( number_format( $stat['times_visited'], 0, ',', ' ' ) );
					$email->closeTableRow();
				}

				// adresse IP
				if( $show_ip ){
					$email->openTableRow();
					$email->addCell( 'Adresse ip du client :' );
					$email->addCell( $stat['ip'].' - '.$stat['ip_host'] );
					$email->closeTableRow();
				}

			}
		}
	}

	// ajout d'un pdf pour nipahut
	if ($config['tnt_id'] == 89) {
		require_once($config['site_dir'].'/include/'.'view.pdf.inc.php');

		$file = $config['site_dir'].'/../pdf/'.$order['id'].'.pdf';

		$error = false;
		$error = !generate_pdf($order, $file );

		if ($error) {
			return false;
		}

		$email->setSubject('Bon de commande');
		$email->addAttachment( $file, 'Bon_commande_'.$order['id'].'.pdf' );
	}

	$email->closeTable();

	$email->addHorizontalRule();

	$email->openTable(570);

	$alert_owner_ecotaxe = isset($config['alert_owner_ecotaxe']) && $config['alert_owner_ecotaxe'];
	$show_discount_shop_owner = isset($config['show_discount_shop_owner']) && $config['show_discount_shop_owner'];

	$email->openTableRow(); $email->addCell( '<b>Commande</b>', 'left', ($alert_owner_ecotaxe && $show_discount_shop_owner ? 7 : ($alert_owner_ecotaxe || $show_discount_shop_owner ? 6 : 5) )); $email->closeTableRow();
	$email->openTableRow();
	$email->addCell( '<b>Ref</b>' );
	$email->addCell( '<b>Désignation</b>' );
	if( $config['alert_owner_ttc'] )
		$email->addCell( '<b>Prix&nbsp;TTC</b>' );
	else
		$email->addCell( '<b>Prix&nbsp;HT</b>' );

	if( $alert_owner_ecotaxe ){
		$email->addCell( '<b>Ecotaxe</b>' );
	}
	$email->addCell( '<b>Qte</b>' );

	if ($show_discount_shop_owner) {
		$email->addCell( '<b>Remise en %</b>' );
	}

	if( $config['alert_owner_ttc'] ){
		$email->addCell( '<b>Total&nbsp;TTC</b>' );
	}else{
		$email->addCell( '<b>Total&nbsp;HT</b>' );
	}
	$email->closeTableRow();

	$ratio = isset( $config['weight_col_calc_lines'] ) && is_numeric( $config['weight_col_calc_lines'] ) && $config['weight_col_calc_lines']>0 ? $config['weight_col_calc_lines'] : 1000;
	$unit = 'Gr';
	if( $ratio==1000 )
		$unit = 'Kg';
	elseif( $ratio==100000 )
		$unit = 'Qt';
	elseif( $ratio==1000000 )
		$unit = 'Tn';

	$sort = $config['tnt_id']==21 ? array('line'=>'asc') : array('pos'=>'asc');
	$products = ord_products_get( $order['id'], $sort, 0, '', null, false, 1 );
	$port_total_ht = $port_total_ttc = 0;
	$colspan = $alert_owner_ecotaxe && $show_discount_shop_owner ? 6 : ($alert_owner_ecotaxe || $show_discount_shop_owner ? 5 : 4);
	$order_ecotaxe = 0;

	while( $prd = ria_mysql_fetch_array($products) ){

		// ajout des interlignes
		if ($prd['id'] === '0') {
			if (isset($config['show_notes_shop_owner']) && $config['show_notes_shop_owner'] && trim($prd['notes'])) {
				$email->openTableRow();
				$email->addCell( '<b>Interligne :</b>' );
				$email->addCell( $prd['notes'], 'left', $colspan );
				$email->closeTableRow();
			}
			continue;
		}

		$prd_name = $prd['name'];
		if( $config['tnt_id'] == 40 ){
			$prd_name = $prd['title'];
		}
		if( isset($config['alert_owner_show_colisage']) && $config['alert_owner_show_colisage'] && $prd['col_id'] && $prd['col_qte'] && !$prd['sell_weight'] ){
			// recherche le nom du conditionnement
			$rcol = prd_colisage_types_get($prd['col_id']);
			if( $rcol && ria_mysql_num_rows($rcol) ){
				$prd_name .= ' ('.ria_mysql_result( $rcol, 0, 'name' ).')';
			}
		}

		if(isset($docs) && $config['tnt_id'] == 3 && $config['wst_id'] == 7){
			if( navicom_product_is_mmsi($prd['id']) && is_array($docs) && array_key_exists($prd['id'], $docs) && is_array($docs[$prd['id']]) && count($docs[$prd['id']]) ){
				$ar_docs = $docs[$prd['id']];

				foreach($ar_docs as $doc){
					$email->addAttachment($config['doc_dir'].'/'.$doc['doc_id'], $doc['doc_filename']);
				}
			}
		}

		// Ajout la mention "Soldes" ou "produit de confiance" pour Freevox.
		if( $config['tnt_id'] == 39 ){
			$is_included_in_promotion = false;

			if( $r_promotions = pmt_codes_get(null, null, true, _PMT_TYPE_REMISE) ){
				while( $promotion = ria_mysql_fetch_assoc($r_promotions) ){
					// Passe à la promotion suivante si non soldes.
					if( strpos(mb_strtolower($promotion['name']), 'solde') === false ){
						continue;
					}

					$r_product = pmt_products_get($promotion['id'], $prd['id']);
					if( $is_included_in_promotion || !$r_product || !ria_mysql_num_rows($r_product) ){
						continue;
					}

					if( ria_mysql_num_rows($r_product) && ria_mysql_result($r_product, 0, 'include') ){
						$prd_name .= " - {$promotion['name']}";
						$is_included_in_promotion = true;
					}
				}
			}

			if( !$is_included_in_promotion && isset($config['fld_prd_confiance'], $config['fld_usr_confiance'], $config['fld_contract_confiance']) ){
				$prd_confiance = fld_object_values_get($prd['id'], $config['fld_prd_confiance']);
				$usr_confiance = fld_object_values_get($order['user'], $config['fld_usr_confiance']);
				$contract_confiance = fld_object_values_get($order['user'], $config['fld_usr_confiance']);

				if( $prd_confiance === 'Oui' && $usr_confiance === 'Oui' && $contract_confiance === 'Oui' ){
					$prd_name .= ' - produit de confiance';
				}
			}
		}

		$ref = $prd['ref']; $sync = ''; $name = $prd_name;
		$rcat = prd_products_categories_get($prd['id'], true);
		if( $rcat && ($cat = ria_mysql_fetch_array( $rcat )) ){
			$url_prd = 'https://'.$http_host_ria.'/admin/catalog/product.php?cat='.$cat['cat'].'&prd='.$prd['id'];
			$ref = '<a href="'.$url_prd.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=prd-general" target="_bank">'.htmlspecialchars($prd['ref']).'</a>';
			$name = '<a href="'.$url_prd.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=prd-general" target="_bank">'.htmlspecialchars($prd_name).'</a>';
		}

		// Affichage du pictogramme de synchro
		if( !isset($config['show_picto_email_owner']) || $config['show_picto_email_owner'] ){
			$is_sync = prd_products_get_is_sync( $prd['id'] );
			$sync = '
			<img border="0" align="absmiddle" src="https://'.$http_host_ria.'/admin/images/sync/'.( $is_sync ? 1 : 0 ).'.svg" width="16" height="16" title="'.( $is_sync ? 'Ce produit est synchronisé avec votre gestion commerciale' : 'Ce produit n\'existe que dans votre boutique en ligne' ).'" />';
		}

		if( prd_products_is_port($prd['ref']) ){
			$port_total_ht += $prd['total_ht'];
			$port_total_ttc += $prd['total_ttc'];
		}

		if( is_array($config['alert_owner_show_models']) ){
			foreach( $config['alert_owner_show_models'] as $model ){
				if( !is_numeric( $model ) || $model<=0 ) continue;

				$model_string = array();
				if( $prd_models = fld_models_get($model,$prd['id'],CLS_PRODUCT) ){
					while( $m = ria_mysql_fetch_array($prd_models) ){
						if( $fields = fld_fields_get(0,0,$m['id'],0,0,$prd['id'],null,array(),false,array(),null,CLS_PRODUCT) ){
							while( $f = ria_mysql_fetch_array($fields) ){
								$model_string[] = '<strong>'.$f['name'].'</strong> : '.$f['obj_value'];
							}
						}
					}
				}
				if( $models = fld_models_get($model,0,CLS_ORD_PRODUCT) ){
					while( $m = ria_mysql_fetch_array($models) ){
						if( $fields = fld_fields_get(0,0,$m['id'],0,0,0,null,array(),false,array(),null,CLS_ORD_PRODUCT) ){
							while( $f = ria_mysql_fetch_array($fields) ){
								$value = fld_object_values_get(array($prd['ord_id'],$prd['id'],$prd['line']), $f['id']);
								if( $value != '' ){
									$model_string[] = '<strong>'.$f['name'].'</strong> : '.$value;
								}
							}
						}
					}
				}

				if( sizeof($model_string) ){
					$name .= '<p style="font-size: 0.9em;">';
					$name .= implode( '<br/>', $model_string );
					$name .= '</p>';
				}
			}
		}

		if( $config['alert_owner_show_dps'] ){
			if( $prd['line_dps_id'] ){
				$depots = prd_deposits_get($prd['line_dps_id']);
				if( $depots && ria_mysql_num_rows($depots) ) {
					$depot = ria_mysql_fetch_assoc($depots);
					$name .= '<p style="font-size: 0.9em;">';
					$name .= '<strong>Dépôt</strong> : '.$depot['name'];
					$name .= '</p>';
				}
			}
		}

		$email->openTableRow();
		$email->addCell( $sync.$ref );
		$email->addCell( $name );

		if( isset($config['alert_owner_ecotaxe']) && $config['alert_owner_ecotaxe'] ){
			$prd['price_ht'] = $prd['price_ht'] - $prd['ecotaxe'];
			$prd['price_ttc'] = $prd['price_ttc'] - ($prd['ecotaxe'] * _TVA_RATE_DEFAULT);
		}

		// Si cette variable est activée, l'écotaxe est calculée en + du prix du produit, et on l'affiche en ligne complémentaire de la commande. Spécifique à l'Extranet Proloisirs
		if( isset($config['order_products_calculate_price_ttc']) && $config['order_products_calculate_price_ttc'] ){
			$order_ecotaxe += $prd['ecotaxe'];
		}

		$suffix = $config['alert_owner_ttc'] ? 'ttc' : 'ht';

		// seul Pierre Oteiza enregistre le poids dans le champ quantité
		$is_real_weight_sell = $prd['sell_weight'] && $config['tnt_id'] == 13;

		if( $is_real_weight_sell ){
			$email->addCell( number_format($prd['price_'.$suffix] * $ratio,2,',',' '), 'right', 1, 'nowrap' );
		}else{
			$email->addCell( number_format($prd['price_'.$suffix],2,',',' '), 'right', 1, 'nowrap' );
		}

		if( $alert_owner_ecotaxe ){
			$email->addCell( number_format($prd['ecotaxe'],2,',',' '), 'right', 1, 'nowrap' );
		}

		if( $is_real_weight_sell || ( $prd['sell_weight'] && $prd['weight_net'] ) ){
			$unit_qte = fld_object_values_get( array($order['id'], $prd['id'], $prd['line']), _FLD_PRD_ORD_QTE_UNIT, '', false, true );
			if( !is_numeric($unit_qte) || !$unit_qte ){
				$unit_qte = $is_real_weight_sell ? 1 : $prd['qte'];
			}
			if( $is_real_weight_sell ){
				$email->addCell( str_replace( ' ', '&nbsp;', number_format($prd['qte'] / $ratio, 3,',',' ') ).'&nbsp;'.$unit.'&nbsp;('.$unit_qte.')', 'right' );
			}else{
				$email->addCell( str_replace( ' ', '&nbsp;', number_format($prd['weight_net'] / $ratio, 3,',',' ') ).'&nbsp;'.$unit.'&nbsp;('.$unit_qte.')', 'right' );
			}
		}else{
			$qte = $prd['qte'];
			$is_int =  floor($qte)==$qte;
			$email->addCell( str_replace( ' ', '&nbsp;', number_format($qte, $is_int ? 0 : 4,',',' ') ), 'right' );
		}

		// ajout de la colonne remise en % (donnée de yuto)
		$remise_yuto = false;
		if( $show_discount_shop_owner ){
			$remise_yuto = fld_object_values_get([$prd['ord_id'], $prd['id'], $prd['line']], 3257);

			if ($remise_yuto !== false && $remise_yuto !== null && $remise_yuto > 0) {
				$email->addCell( str_replace( ' ', '&nbsp;', number_format($remise_yuto, 2,',',' ') ), 'right' );
			} else {
				$email->addCell( '0', 'right' );
			}
		}

		if( $is_real_weight_sell ){
			$email->addCell( number_format($prd['price_'.$suffix] * $prd['qte'],2,',',' '), 'right', 1, 'nowrap' );
		}else{
			$email->addCell( number_format($prd['total_'.$suffix],2,',',' '), 'right', 1, 'nowrap' );
		}

		$email->closeTableRow();

		// notes sur la ligne (si activé)
		if( isset($config['show_notes_shop_owner']) && $config['show_notes_shop_owner'] && trim($prd['notes']) ){
			$email->openTableRow();
			$email->addCell( '<b>Notes :</b>' );
			$email->addCell( $prd['notes'], 'left', $colspan );
			$email->closeTableRow();
		}

	}

	$free_shipping = false;
	if( !empty($ord_pmts) ){
		foreach($ord_pmts as $ord_pmt){
			$pmt = ria_mysql_fetch_assoc(pmt_codes_get($ord_pmt['pmt_id']));
			if( $pmt['free_shipping'] ){
				$services = pmt_codes_get_services( $pmt['parent'] ? $pmt['parent'] : $pmt['id'] );
				if( !$order['srv_id'] && !$order['str_id'] )
					$free_shipping = true;
				elseif( in_array($order['srv_id'], $services) )
					$free_shipping = true;
				elseif( $order['str_id'] && in_array(-1, $services) )
					$free_shipping = true;
			}

			// Affichage code promotion
			$email->openTableRow();
			if ($config['tnt_id'] == 13 && $pmt['type'] == _PMT_TYPE_GIFTS) {
				$email->addCell( 'Code promotion :', 'right', $colspan-2 );
				$email->addCell( '<a href="https://'.$http_host_ria.'/admin/promotions/codes/edit.php?pmt='.($pmt['parent'] ? $pmt['parent'] : $pmt['id']).'">'.$pmt['code'].'</a>', 'right', 2, true );
				$discount = $pmt['discount'];
				if ($config['alert_owner_ttc']) {
					$discount = $discount * (is_numeric($pmt['tva_rate']) && $pmt['tva_rate'] > 1 ? $pmt['tva_rate'] : _TVA_RATE_DEFAULT);
				}
				$email->addCell( '-'.ria_number_french($discount), 'right', 1 );
			}else{
				$email->addCell( 'Code promotion :', 'right', $colspan );
				$email->addCell( '<a href="https://'.$http_host_ria.'/admin/promotions/codes/edit.php?pmt='.($pmt['parent'] ? $pmt['parent'] : $pmt['id']).'">'.$pmt['code'].'</a>', 'right', 1, true );
			}
			$email->closeTableRow();
		}
	}

	$remise_reward = fld_object_values_get( $order['id'], _FLD_ORD_RWD_OPT4_HT );
	if (is_numeric($remise_reward) && $remise_reward > 0) {
		$email->openTableRow();
		$email->addCell( 'Remise points de fidélité :', 'right', $colspan );
		$email->addCell( '-'.number_format( $remise_reward, 2, ',', ' '), 'right', 1, true );
		$email->closeTableRow();
	}

	if( $order['srv_id'] && ( !isset($config['notify_owner_show_srv_port']) || $config['notify_owner_show_srv_port'] ) ){
		if( $srv===null )
			$srv = ria_mysql_fetch_array(dlv_services_get($order['srv_id']));
		$email->openTableRow();
		$email->addCell( 'Livraison '.$srv['name'].' :', 'right', $colspan );
		if( $free_shipping ){
			$email->addCell( 'Offert', 'right' );
		}else{
			if( $port_total_ht<=0 )
				$email->addCell( 'Offert', 'right' );
			else{
				if( $config['alert_owner_ttc'] ){
					$email->addCell( number_format($port_total_ttc, 2, ',', ' '), 'right' );
				}else{
					$email->addCell( number_format($port_total_ht, 2, ',', ' '), 'right' );
				}
			}
		}
		$email->closeTableRow();
	}

	$email->openTableRow();
	$email->addCell( 'Total HT :', 'right', $colspan );
	$email->addCell( number_format($order['total_ht'],2,',',' '), 'right' );
	$email->closeTableRow();

	// Cette variable est calculée uniquement si la config order_products_calculate_price_tcc est activée
	if( $order_ecotaxe > 0 ){
		$email->openTableRow();
		$email->addCell( 'Ecotaxe :', 'right', $colspan );
		$email->addCell( number_format($order_ecotaxe,2,',',' '), 'right' );
		$email->closeTableRow();
	}
	// Bigship: Si le pays n'est pas éxonéré de TVA, affiche la TVA et le montant TTC dans la notif de commande.
	if( $config['tnt_id']==1 && $config['wst_id']==1 ){
		if( sys_zones_exlude_tva($order['dlv_country'],$order['dlv_postal_code']) == true ){
		$email->openTableRow();
		$email->addCell( 'TVA :', 'right', $colspan );
		$email->addCell( number_format($order['total_ttc'] - ($order['total_ht'] + $order_ecotaxe),2,',',' '), 'right' );
		$email->closeTableRow();

		$email->openTableRow();
		$email->addCell( 'Total TTC :', 'right', $colspan );
		$email->addCell( number_format($order['total_ttc'],2,',',' '), 'right' );
		$email->closeTableRow();

		$email->closeTable();
		}

	} else { // Pour tous les autres sites

		$email->openTableRow();
		$email->addCell( 'TVA :', 'right', $colspan );
		$email->addCell( number_format($order['total_ttc'] - ($order['total_ht'] + $order_ecotaxe),2,',',' '), 'right' );
		$email->closeTableRow();

		$email->openTableRow();
		$email->addCell( 'Total TTC :', 'right', $colspan );
		$email->addCell( number_format($order['total_ttc'],2,',',' '), 'right' );
		$email->closeTableRow();

		$email->closeTable();
	}

	// Affichage des commandes enfants
	if( isset($config['notify_ord_childs']) && $config['notify_ord_childs'] ){
		$ord_p_id = $order['id'];

		$child = true;

		$rochild = ord_orders_get_childs( $order['id'] );
		if( !$rochild || !ria_mysql_num_rows($rochild) ){
			$child = false;
			$rochild = ord_orders_get( 0, $order['id'] );
		}

		if( $rochild ){
			$cpt = 0;
			while( $ochild = ria_mysql_fetch_array($rochild) ){
				$cpt++;

				$order = ria_mysql_fetch_array( ord_orders_get_with_adresses(0, $ochild['id']) );

				$email->addHorizontalRule();
				$email->openTable(570);

				$email->openTableRow();
				$email->addCell( '<b>Colis n°'.$cpt.'</b>', 'left', 2 );
				$email->closeTableRow();

				$email->openTableRow();
				$email->addCell( 'Référence Internet :' ); $email->addCell( str_pad($ord,8,'0',STR_PAD_LEFT) );
				$email->closeTableRow();

				// Options de livraison
				$srv = null;
				$email->openTableRow(); $email->addCell( '<b>Options de livraison</b>', 'left', 2 ); $email->closeTableRow();

				$email->openTableRow(); $email->addCell( 'Type :' );
				if( $order['rly_id'] ){
					$rly = ria_mysql_fetch_array( dlv_relays_get_simple($order['rly_id']) );
					$email->addCell( 'Livraison au point-relais '.$rly['type_name'].'<br />'.$rly['ref'].' - '.$rly['name'] );
				}elseif( is_numeric($order['str_id']) ){
					$str = ria_mysql_fetch_array(dlv_stores_get( $order['str_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null ));
					$email->addCell( 'Livraison au magasin '.$str['name'].' ('.$str['zipcode'].')' );
				}elseif( is_numeric($order['srv_id']) ){
					$srv = ria_mysql_fetch_array(dlv_services_get($order['srv_id']));
					$email->addCell( 'Livraison '.str_replace('Livraison','',$srv['name']) );
				}else{
					$email->addCell( 'Non sélectionné' );
				}
				$email->closeTableRow();

				if( $order['rly_id'] ){
					$email->openTableRow();
					$email->addCell( 'Adresse :' );
					$email->addCell( $rly['name'].'<br />'.$rly['address1'].( trim($rly['address2'])!='' ? '<br />'.$rly['address2'] : '' ).'<br />'.$rly['zipcode'].'<br />'.$rly['city'].'<br />'.$rly['country'] );
					$email->closeTableRow();
				}
				if( trim($order['dlv-notes']) ){
					$email->openTableRow(); $email->addCell( 'Consignes de livraison :' ); $email->addCell( nl2br($order['dlv-notes']) ); $email->closeTableRow();
				}

				// Adresse de livraison
				if( $order['inv_id']!=$order['dlv_id'] && !$order['rly_id'] ){
					$email->openTableRow(); $email->addCell( '<b>Adresse de livraison</b>', 'left', 2 ); $email->closeTableRow();
					if( trim($order['dlv_lastname']) ){
						$email->openTableRow(); $email->addCell( 'Nom :' ); $email->addCell( $order['dlv_lastname'] ); $email->closeTableRow();
					}
					if( trim($order['dlv_firstname']) ){
						$email->openTableRow(); $email->addCell( 'Prénom :' ); $email->addCell( $order['dlv_firstname'] ); $email->closeTableRow();
					}
					if( trim($order['dlv_society']) ){
						$email->openTableRow(); $email->addCell( 'Société :' ); $email->addCell( $order['dlv_society'] ); $email->closeTableRow();
					}
					$email->openTableRow(); $email->addCell( 'Adresse :' ); $email->addCell( $order['dlv_address1'].'<br>'.$order['dlv_address2'].'<br>'.$order['dlv_address3'].'<br>'.$order['dlv_postal_code'].' '.$order['dlv_city'].'<br>'.$order['dlv_country'] ); $email->closeTableRow();

					$email->openTableRow();
					$email->addCell( 'Téléphone :' );
					$email->addCell( $order['dlv_phone'] );
					$email->closeTableRow();
				}

				$email->closeTable();

				$email->addHtml('<br /><b>Article(s) dans ce colis :</b>' );

				$email->openTable(570);

				$email->openTableRow();
				$email->addCell( '<b>Ref</b>' );
				$email->addCell( '<b>Désignation</b>' );
				$email->addCell( '<b>Qté</b>' );
				$email->closeTableRow();

				// Article(s) contenu(s) dans ce colis
				if( $child ){
					$rprd = ord_products_get_from_child( $ord_p_id, $order['id'] );
				}else{
					$rprd = ord_products_get( $ord_p_id );
				}

				if( $rprd ){
					while($prd = ria_mysql_fetch_array($rprd) ){
						$ref = $prd['ref']; $sync = ''; $name = $prd['name'];
						$rcat = prd_products_categories_get($prd['id'], true);
						if( $rcat && ($cat = ria_mysql_fetch_array( $rcat )) ){
							$url_prd = 'https://'.$http_host_ria.'/admin/catalog/product.php?cat='.$cat['cat'].'&prd='.$prd['id'];
							$ref = '<a href="'.$url_prd.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=prd-general" target="_bank">'.htmlspecialchars($prd['ref']).'</a>';
							$name = '<a href="'.$url_prd.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=order-new&amp;utm_content=prd-general" target="_bank">'.htmlspecialchars($name).'</a>';
						}

						// affichage du pictogramme de synchro
						if( !isset($config['show_picto_email_owner']) || $config['show_picto_email_owner'] ){
							$is_sync = prd_products_get_is_sync( $prd['id'] );
							$sync = '
							<img border="0" align="absmiddle" src="https://'.$http_host_ria.'/admin/images/sync/'.( $is_sync ? 1 : 0 ).'.svg" width="16" height="16" title="'.( $is_sync ? 'Ce produit est synchronisé avec votre gestion commerciale' : 'Ce produit n\'existe que dans votre boutique en ligne' ).'" />';
						}

						$email->openTableRow();
						$email->addCell( $sync.$ref );
						$email->addCell( $name );
						$email->addCell( $prd['qte'] );
						$email->closeTableRow();
					}
				}
				$email->closeTable();
			}
		}
	}

	$email->addHtml( $config['email_html_footer'] );

	// Copie sans la pièce jointe pour bigship site public pour le magasin client
	if( $config['tnt_id'] == 1 && $config['wst_id'] == 1 ){
		if ( isset($is_order_store, $store) && $is_order_store && $store ) {
			$email2 = clone $email;
			$email2->attachments = array();
			$email2->cc = array();
			$email2->to = array();
			$email2->addTo($store['email']);
			$email2->send();
		}
	}

	return $email->send();
}

// \endcond

/// @}