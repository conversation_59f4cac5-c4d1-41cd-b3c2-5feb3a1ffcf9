<?php

/**	\defgroup fld_classes Classes personnalisées
 * 	\ingroup model_fields
 *	Ce module comprend les fonctions nécessaires à la gestion des classes personnalisées.
 *	@{
 */

// \cond onlyria
/** Détermine l'existence d'une classe de champs avancés.
 *	Pour des questions de performances, le résultat de cette fonction est mis en cache durant 15 secondes.
 *	@param int $id Obligatoire, Identifiant de la classe
 *	@param bool $control_tenant Optionnel, par défaut la classe peut être une classe système, mettre true pour les exclures du contrôle
 *	@return bool True si la classe existe, False dans le cas contraire.
 */
function fld_classes_exists( $id, $control_tenant=false ){
	global $config;
	global $memcached;

	// Mise en cache durant 15s
	$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':fld_classes_exists:'.$id.':'.$control_tenant;
	if( $exists = $memcached->get($key_memcached) ){
		return $exists;
	}

	if( !is_numeric($id) || $id<=0 ) return false;

	$sql = '
		select cls_id
		from fld_classes
		where 1
	';

	if( !$control_tenant ){
		$sql .= '
			and (cls_tnt_id=0 or cls_tnt_id='.$config['tnt_id'].')
		';
	}else{
		$sql .= '
			and cls_tnt_id='.$config['tnt_id'].'
		';
	}

	$sql .= '
		and cls_id='.$id.'
		and cls_date_deleted is null
	';

	$result = ria_mysql_query($sql);
	if( !$result ) return false;

	$exists = ria_mysql_num_rows($result);
	$memcached->set( $key_memcached, $exists, 15 );

	return $exists;
}
// \endcond

// \cond onlyria
/** Détermine le nom physique d'une classe, si et seulement si celle-ci est marquée comme physique
 *	@param int $id identifiant de la classe
 *	@return string|bool Nom physique de la classe, False en cas d'erreur
 */
function fld_classes_get_physical_name( $id ){
	if( !is_numeric($id) || $id<=0 ) return false;
	global $config;

	$res = ria_mysql_query('
		select cls_physical_name
		from fld_classes
		where (cls_tnt_id=0 or cls_tnt_id='.$config['tnt_id'].') and cls_id='.$id.' and cls_date_deleted is null and cls_is_physical=1
	');

	if( !$res || !ria_mysql_num_rows($res) ) return false;

	return ria_mysql_result( $res,0,0 );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de créer une nouvelle classe de champs libres
 *	@param string $name Obligatoire, nom de la classe
 *	@param string $desc Optionnel, description de la classe
 *	@param bool $segmentable Optionnel, détermine si la classe est segmentable
 *	@return int L'identifiant de la nouvelle classe, False si une erreur s'est prouite lors de la création
 */
function fld_classes_add( $name, $desc='', $segmentable=false ){
	global $config;

	if( trim($name)=='' ){
		return false;
	}

	$res = ria_mysql_query('
		insert into fld_classes
			( cls_tnt_id, cls_name, cls_desc, cls_is_segmentable )
		values
			( '.$config['tnt_id'].', \''.addslashes( $name ).'\', '.( trim($desc)!='' ? '\''.addslashes($desc).'\'' : 'null' ).', '.( $segmentable ? '1' : '0' ).' )
	');

	if( !$res ){
		return false;
	}
	$cls_id = ria_mysql_insert_id();

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $cls_id;
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère une ou plusieurs classes selon des paramètres optionnels.
 *	@param int $id Identifiant d'une classe ou tableau d'identifiants de classes. Si spécifié, les autres paramètres de filtrage sont ignorés.
 *	@param bool $global Si activé, les informations de type "count" retournées sont transversales à tous les locataires.
 *	@param bool $include_physical Détermine si les classes physiques sont ou non incluses. C'est le cas par défaut.
 *	@param bool $include_generic Détermine si les classes sans locataire sont retournées, ou uniquement celles du locataire courant. Par défaut tout est retourné.
 *	@param bool $segmentable Permet de filtrer les classes segmentables (True) ou non segmentables (False). Par défaut tout est retourné.
 *	@param bool $customizable Permet de filtrer les classes personnalisables (True) ou non (False). Par défaut tout est retourné.
 *	@param bool $get_infos Optionnel, si désactivé, fields, models et categories ne sont pas calculés (0).
 *
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- tenant : identifiant de locataire
 *		- id : identifiant de la classe
 *		- name : nom de la classe
 *		- desc : description de la classe
 *		- fields : nombre de champs avancés liés à la classe
 *		- models : nombre de modèles liés à la classe
 *		- categories : nombre de catégories de champs liés à la classe
 *		- physical : classe physique oui / non
 *		- physical-name : nom de la classe physique (table SQL)
 *		- is-segmentable : détermine si les objets de la classe sont segmentables
 *		- customizable : détermine si la classe peut être étendue par les utilisateurs (création de champs, de modèles, etc...)
 */
function fld_classes_get( $id=0, $global=false, $include_physical=true, $include_generic=true, $segmentable=null, $customizable=null, $get_infos=false ){
	global $config;

	if( is_array($id) ){
		foreach( $id as $one_id ){
			if( !is_numeric($one_id) || $one_id <= 0 ){
				return false;
			}
		}
	}elseif( !is_numeric($id) || $id < 0 ){
		return false;
	}elseif( $id ){
		$id = array($id);
	}else{
		$id = array();
	}

	$sql = '
		select
			cls_tnt_id as "tenant", cls_id as "id", cls_name as "name", cls_desc as "desc", '.( $get_infos ? '(
				select count(*) from fld_fields
				where fld_date_deleted is null and fld_cls_id = cls_id '.( $global ? '' : 'and ( '.( $include_generic ? 'fld_tnt_id = 0 or ' : '' ).'fld_tnt_id = '.$config['tnt_id'].' )' ).'
			)' : 'null' ).' as "fields", '.( $get_infos ? '(
				select count(*) from fld_models
				where mdl_date_deleted is null and mdl_cls_id = cls_id '.( $global ? '' : 'and mdl_tnt_id = '.$config['tnt_id'] ).'
			)' : 'null' ).' as "models", '.( $get_infos ? '(
				select count(*) from fld_categories
				where cat_date_deleted is null and cat_cls_id = cls_id '.( $global ? '' : 'and cat_tnt_id = '.$config['tnt_id'] ).'
			)' : 'null' ).' as "categories",
			cls_is_physical as "physical", cls_physical_name as "physical-name", cls_is_segmentable as "is-segmentable",
			cls_is_customizable as "customizable"
		from
			fld_classes
		where
			cls_date_deleted is null
	';

	if( sizeof($id) ){
		$sql .= ' and cls_id in ('.implode(', ', $id).')';
	}else{
		if( !$include_physical ){
			$sql .= ' and cls_is_physical = 0';
		}
		if( $include_generic ){
			$sql .= ' and cls_tnt_id in (0, '.$config['tnt_id'].')';
		}else{
			$sql .= ' and cls_tnt_id = '.$config['tnt_id'];
		}
		if( $segmentable !== null ){
			$sql .= ' and cls_is_segmentable = '.( $segmentable ? 1 : 0 );
		}
		if( $customizable !== null ){
			$sql .= ' and cls_is_customizable = '.( $customizable ? 1 : 0 );
		}
	}

	return ria_mysql_query($sql);

}
/// \endcond

// \cond onlyria
/** Cette fonction permet de charger un tableau associatif simple des classes.
 * 	@return array Un tableau contenant (id => classe)
 */
function fld_classes_get_array(){
	global $config;

	$r_class = ria_mysql_query('
		select cls_id, cls_name
		from fld_classes
		where (cls_tnt_id = 0 or cls_tnt_id = '.$config['tnt_id'].')
			and cls_date_deleted is null
	');

	$ar_class = [];

	if( $r_class ){
		while( $class = ria_mysql_fetch_assoc($r_class) ){
			$ar_class[ $class['cls_id'] ] = $class['cls_name'];
		}
	}

	return $ar_class;
}
/// \endcond

// \cond onlyria
/** Détermine le nom d'une classe de champs libres à partir de son identifiant
 *	@param int $id identifiant du champ libre
 *	@return string|bool Le nom de la classe, False en cas d'erreur
 */
function fld_classes_get_name( $id ){
	global $config;

	if( !fld_classes_exists($id) ){
		return false;
	}

	$result = ria_mysql_query('
		select cls_name
		from fld_classes
		where (
			cls_tnt_id=0 or cls_tnt_id='.$config['tnt_id'].'
		) and
		cls_id='.$id.' and
		cls_date_deleted is null
	');

	if( $result===false || !ria_mysql_num_rows($result) ) return false;

	return ria_mysql_result( $result,0,0 );
}
// \endcond

// \cond onlyria
/** Détermine si une classe de champ libre a une représentation physique
 *	@param int $id Identifiant de la classe de champ libre
 *	@return bool True si la classe est physique, False sinon
 */
function fld_classes_is_physical( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;

	$res = ria_mysql_query('
		select cls_is_physical as "is-physical" from fld_classes
		where cls_id='.$id.' and ( cls_tnt_id=0 or cls_tnt_id='.$config['tnt_id'].' )
	');
	if( $res===false || !ria_mysql_num_rows($res) ) return false;
	$c = ria_mysql_fetch_array( $res );

	return $c['is-physical'];
}
// \endcond

// \cond onlyria
/** Détermine le nombre de champs libres assignés à cette classe
 * 	@param int $id Identifiant de la classe
 *	@param bool $include_fld_generic Facultatif, si False, les champs non spécifiques
 *	@return int|bool Le nombre de champs libres, False en cas d'erreur
 */
function fld_classes_get_fields_count( $id, $include_fld_generic=true ){
	global $config;

	if( !fld_classes_exists($id) ){
		return false;
	}

	$result = ria_mysql_query('
		select count(*)
		from fld_fields
		where( '.( $include_fld_generic ? 'fld_tnt_id=0 or ' : '' ).' fld_tnt_id='.$config['tnt_id'].' )
			and fld_cls_id='.$id.'
			and fld_date_deleted is null
	');

	if( !$result ){
		return false;
	}

	return ria_mysql_result( $result, 0, 0 );
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère des objets de classes physiques, en appellant la méthode "get()" appropriée suivant la classe.
 *	Note : pour la classe produit, les tarifs ne sont pas retournés. Pour les classes restriction de catalogue, 0 est retourné pour les valeurs NULL.
 *	@param int $cls_id Identifiant de la classe
 *	@param $objs Liste des objets souhaités (tableau vide pour tout récupérer). Pour les classes à clés multiples, il faut spécifier un tableau pour chaque clé.
 *	@param $cols Par référence : tableau des intitulés des colonnes identifiants dans le résultat. Par exemple, pour la classe "Images secondaires de produits", le résultat sera : array('id', 'prd_id')
 *	@param int $wst Optionnel, identifiant d'un site spécifique (si requis par la fonction get()), prend par défaut le site de la configuration
 *
 *	@return bool False en cas d'échec ou si la classe n'est pas implémentée
 *	@return Le résultat de la fonction "get()" associée à la classe (par exemple, pour les clients, le résultat de gu_users_get())
 */
function fld_classes_get_objects( $cls_id, $objs, &$cols, $wst = false ){
	global $config;

	if( !fld_classes_exists( $cls_id ) ){
		return false;
	}

	if( !is_array($objs) || sizeof($objs) > COUNT_OBJ_ID ){
		return false;
	}elseif( !sizeof($objs) ){
		$objs[] = 0; // typiquement, la valeur par défaut des méthodes get() est "0"
	}

	$wst_id = $config['wst_id'];
	if( is_numeric($wst) && $wst > 0 ){
		$wst_id = $wst;
	}

	$cols = array('id');

	// le switch est dans l'ordre des IDs, les classes non gérées actuellement sont marquées en commentaires
	switch( $cls_id ){
		case CLS_PRODUCT: // sans les prix
			return prd_products_get_simple( $objs[0], '', false, 0, false, false, false, false, array('childs'=>true) );
		case CLS_USER:
			return gu_users_get( $objs[0] );
		case CLS_CATEGORY:
			return prd_categories_get( $objs[0] );
		case CLS_ORDER:
			return ord_orders_get( 0, $objs[0] );
		case CLS_BRAND:
			return prd_brands_get( $objs[0] );
		case CLS_STORE:
			return dlv_stores_get( $objs[0], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
		// CLS_MESSAGE
		case CLS_ORD_PRODUCT:
			if( !isset($objs[1], $objs[2]) ){
				return false;
			}
			$cols = array('ord_id', 'id', 'line');
			return ord_products_get( $objs[0], false, $objs[1], '', $objs[2], false, 1, 0, -1, false, false, 0, false, false, false, false, true );
		case CLS_PRD_COLISAGE:
			if( !isset($objs[1]) ){
				return false;
			}
			$cols = array('col_id', 'prd_id');
			return prd_colisage_classify_get( $objs[0], $objs[1], 0, false, true );
		// CLS_SECTOR
		// CLS_CMS
		case CLS_TYPE_DOCUMENT:
			return doc_types_get( $objs[0], true, true, false, false, false, -1 );
		// CLS_FAQ_CAT
		// CLS_NEWS
		// CLS_NEWS_CAT
		// CLS_FAQ_QST
		case CLS_DOCUMENT:
			return doc_documents_get( $objs[0], 0, null, '', false );
		// CLS_BANNER
		// CLS_CGV_ARTICLE
		case CLS_PRD_RELATIONS:
			if( !isset($objs[1], $objs[2]) ){
				return false;
			}
			$cols = array('src_id', 'dst_id', 'type_id');
			return prd_relations_get( $objs[0], $objs[1], $objs[2], false, 0, true );
		// CLS_ERRATUM
		// CLS_WEBSITE
		case CLS_INV_PRODUCT:
			if( !isset($objs[1], $objs[2]) ){
				return false;
			}
			$cols = array('inv_id', 'id', 'line');
			return ord_inv_products_get( $objs[0], false, false, null, null, null, null, $objs[1], $objs[2], true );
		case CLS_BL_PRODUCT:
			if( !isset($objs[1], $objs[2]) ){
				return false;
			}
			$cols = array('bl_id', 'id', 'line');
			return ord_bl_products_get( $objs[0], 0, false, false, false, $objs[1], $objs[2], true );
		case CLS_PL_PRODUCT:
			if( !isset($objs[1], $objs[2]) ){
				return false;
			}
			$cols = array('pl_id', 'id', 'line');
			return ord_pl_products_get( $objs[0], false, $objs[1], $objs[2], true );
		case CLS_INVOICE:
			return ord_invoices_get( $objs[0] );
		case CLS_BL:
			return ord_bl_get( $objs[0] );
		case CLS_CLASSIFY:
			if( !isset($objs[1]) ){
				return false;
			}
			$cols = array('cat', 'prd');
			return prd_classify_get( false, $objs[1], $objs[0], 0, false, false, true );
		case CLS_ACCOUNTING_CATEGORY:
			return gu_accounting_categories_get( $objs[0] );
		case CLS_PRICE_CATEGORY:
			return prd_prices_categories_get( $objs[0] );
		case CLS_PROFIL:
			return gu_profiles_get( $objs[0] );
		// CLS_NLR_SUBSCRIBERS
		case CLS_PMT_CODE:
			return pmt_codes_get( $objs[0], null, false, false, false, null, null, null, null, false, false, false, $wst_id );
		// CLS_NLR_CATEGORY
		case CLS_DLV_SERVICE:
			return dlv_services_get( $objs[0] );
		case CLS_ORD_PAYMENT:
			return ord_payment_types_get( $objs[0] );
		case CLS_IMAGE:
			return img_images_get( $objs[0] );
		case CLS_PRICE:
			return prc_prices_get( $objs[0] );
		case CLS_NOTE:
			require_once('obj.notes.inc.php');
			return fld_object_notes_get( $objs[0] );
		// CLS_CTR_CATALOGS
		// CLS_WISHLISTS
		// CLS_RETURN
		case CLS_RETURN:
			return ord_returns_get( $objs[0] );
		case CLS_RETURN_PRODUCTS:
			return ord_returns_products_get($objs[1], $objs[0], 0,  $objs[2]); // ordre de la clé return_id,  prd, line_id
		case CLS_ADDRESS:
			return gu_adresses_get( 0, $objs[0] );
		// CLS_STR_SALE_TYPE
		case CLS_PRD_HIERARCHY:
			if( !isset($objs[1]) ){
				return false;
			}
			$cols = array('parent', 'child');
			return prd_hierarchy_get( $objs[0], $objs[1], true );
		case CLS_PRD_IMAGE:
			if( !isset($objs[1]) ){
				return false;
			}
			$cols = array('id', 'prd_id');
			return prd_images_get( $objs[1], $objs[0], true );
		case CLS_USR_PAYMENT:
			if( !isset($objs[1]) ){
				return false;
			}
			$cols[] = 'usr';
			return gu_users_payment_types_get( $objs[1], $objs[0], true );
		case CLS_PL:
			return ord_pl_get( $objs[0] );
		case CLS_TVA:
			return prc_tvas_get( $objs[0] );
		// CLS_CGV_VERSION
		case CLS_EXEMPT_TVA:
			return prc_tva_exempt_groups_get( $objs[0] );
		case CLS_COLISAGE:
			return prd_colisage_types_get( $objs[0] );
		case CLS_PRICE_CONDITION:
			$cols = array('price', 'fld');
			return prc_price_conditions_get( $objs[0] );
		case CLS_EXEMPT_TVA_CONDITION:
			$cols = array('group', 'fld');
			return prc_tva_exempt_conditions_get( $objs[0] );
		case CLS_RESTRICT_PRODUCT:
		case CLS_RESTRICT_CATEGORY:
		case CLS_RESTRICT_BRAND:
			$correled = isset($objs[1], $objs[2]);
			$cols = array('obj_id', 'fld_id', 'val_id');
			// remplace les valeurs NULL par des 0
			return prd_restrictions_cache_get( $cls_id, isset($objs[1]) ? $objs[1] : -1, isset($objs[2]) ? $objs[2] : -2, $objs[0], $wst_id, $correled, true );
		case CLS_DEPOSIT:
			return prd_deposits_get( $objs[0] );
		case CLS_STOCK:
			if( !isset($objs[1]) ){
				return false;
			}
			$cols = array('dps_id', 'prd');
			return prd_dps_stocks_get( $objs[1], $objs[0], 0, 0, true );
		case CLS_ORDER_SIGNATURE:
			$cols = array('ord_id');
			return ord_orders_signature_get( $objs[0] );
		case CLS_RELATION_TYPE:
			return prd_relations_types_get( $objs[0] );
		case CLS_FIELD_PRICE:
			$cols = array('fld_id');
			return prc_fields_get( $objs[0] );
		case CLS_FIELD_TVA:
			$cols = array('fld_id');
			return prc_fields_get( $objs[0], false, true );
		case CLS_SELL_UNIT:
			$cols = array('id');
			return prd_sell_units_get( $objs[0] );
		case CLS_ORD_STATE_NAME:
			$cols = array('id');
			return ord_states_name_get( $objs[0], 0, $wst_id );
		default:
			error_log(__FILE__.':'.__LINE__.' - classe non implémentée.');
			break;
	}

	return false;
}
/// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les informations sur la structure des classes physiques
 * 	@param int $cls_id Obligatoire, identifiant d'une classe
 * 	@return array Un tableau contenant la structure
 *	@return bool false si la classe donnée en paramètre n'existe pas
 */
function fld_classes_get_physical_structure( $cls_id ){
	global $config;

	if( !is_numeric($cls_id) || $cls_id<=0 ){
		return false;
	}

	$ar_physical = false;
	switch( $cls_id ){
		case CLS_PRODUCT :
			$ar_physical = ['name'=>'prd_products', 'tnt_id'=>'prd_tnt_id', 'delete'=> 'prd_date_deleted', 'col_id' => 'prd_id' ];
			break;
		case CLS_CATEGORY :
			$ar_physical = ['name'=>'prd_categories', 'tnt_id'=>'cat_tnt_id', 'delete'=> 'cat_date_deleted', 'col_id' => 'cat_id' ];
			break;
		case CLS_USER :
			$ar_physical = ['name'=>'gu_users', 'tnt_id'=>'usr_tnt_id', 'delete'=> 'usr_date_deleted', 'col_id' => 'usr_id' ];
			break;
		case CLS_ORDER :
			$ar_physical = ['name'=>'ord_orders', 'tnt_id'=>'ord_tnt_id', 'delete'=> 'ord_masked', 'col_id' => 'ord_id' ];
			break;
		case CLS_BRAND :
			$ar_physical = ['name'=>'prd_brands', 'tnt_id'=>'brd_tnt_id', 'delete'=> 'brd_date_deleted', 'col_id' => 'brd_id' ];
			break;
		case CLS_STORE :
			$ar_physical = ['name'=>'dlv_stores', 'tnt_id'=>'str_tnt_id', 'delete'=> 'str_date_deleted', 'col_id' => 'str_id' ];
			break;
		case CLS_MESSAGE :
			$ar_physical = ['name'=>'gu_messages', 'tnt_id'=>'cnt_tnt_id', 'delete'=> 'cnt_date_delete', 'col_id' => 'cnt_id' ];
			break;
		case CLS_ORD_PRODUCT :
			$ar_physical = ['name'=>'ord_products', 'tnt_id'=>'prd_tnt_id', 'delete'=> '', 'col_id' => ['prd_ord_id', 'prd_id', 'prd_line_id'] ];
			break;
		case CLS_PRD_COLISAGE :
			$ar_physical = ['name'=>'prd_colisage_classify', 'tnt_id'=>'cly_tnt_id', 'delete'=> '', 'col_id' => ['cly_col_id','cly_prd_id'] ];
			break;
		case CLS_SECTOR :
			$ar_physical = ['name'=>'dlv_sectors', 'tnt_id'=>'sct_tnt_id', 'delete'=> '', 'col_id' => 'sct_id' ];
			break;
		case CLS_CMS :
			$ar_physical = ['name'=>'cms_categories', 'tnt_id'=>'cat_tnt_id', 'delete'=> 'date_deleted', 'col_id' => 'cat_id' ];
			break;
		case CLS_TYPE_DOCUMENT :
			$ar_physical = ['name'=>'doc_types', 'tnt_id'=>'type_tnt_id', 'delete'=> 'type_date_deleted', 'col_id' => 'type_id' ];
			break;
		case CLS_FAQ_CAT :
			$ar_physical = ['name'=>'faq_categories', 'tnt_id'=>'cat_tnt_id', 'delete'=> '', 'col_id' => 'cat_id' ];
			break;
		case CLS_NEWS :
			$ar_physical = ['name'=>'news', 'tnt_id'=>'news_tnt_id', 'delete'=> '', 'col_id' => 'news_id' ];
			break;
		case CLS_NEWS_CAT :
			$ar_physical = ['name'=>'news_categories', 'tnt_id'=>'cat_tnt_id', 'delete'=> '', 'col_id' => 'cat_id' ];
			break;
		case CLS_FAQ_QST :
			$ar_physical = ['name'=>'faq_questions', 'tnt_id'=>'qst_tnt_id', 'delete'=> '', 'col_id' => 'qst_id' ];
			break;
		case CLS_DOCUMENT :
			$ar_physical = ['name'=>'doc_documents', 'tnt_id'=>'doc_tnt_id', 'delete'=> 'doc_is_deleted', 'col_id' => 'doc_id' ];
			break;
		case CLS_BANNER :
			$ar_physical = ['name'=>'adv_banners', 'tnt_id'=>'bnr_tnt_id', 'delete'=> 'bnr_date_deleted', 'col_id' => 'bnr_id' ];
			break;
		case CLS_CGV_ARTICLE :
			$ar_physical = ['name'=>'cgv_articles', 'tnt_id'=>'art_tnt_id', 'delete'=> '', 'col_id' => 'art_id' ];
			break;
		case CLS_PRD_RELATIONS :
			$ar_physical = ['name'=>'prd_relations', 'tnt_id'=>'rel_tnt_id', 'delete'=> '', 'col_id' => ['rel_src_id', 'rel_dst_id', 'rel_type_id'] ];
			break;
		case CLS_ERRATUM :
			$ar_physical = ['name'=>'cat_erratums', 'tnt_id'=>'err_tnt_id', 'delete'=> 'err_date_deleted', 'col_id' => 'err_id' ];
			break;
		case CLS_WEBSITE :
			$ar_physical = ['name'=>'tnt_websites', 'tnt_id'=>'wst_tnt_id', 'delete'=> 'wst_date_deleted', 'col_id' => 'wst_id' ];
			break;
		case CLS_INV_PRODUCT :
			$ar_physical = ['name'=>'ord_inv_products', 'tnt_id'=>'prd_tnt_id', 'delete'=> '', 'col_id' => ['prd_inv_id', 'prd_id', 'prd_line_id'] ];
			break;
		case CLS_BL_PRODUCT :
			$ar_physical = ['name'=>'ord_bl_products', 'tnt_id'=>'prd_tnt_id', 'delete'=> '', 'col_id' => ['prd_bl_id', 'prd_id', 'prd_line_id'] ];
			break;
		case CLS_PL_PRODUCT :
			$ar_physical = ['name'=>'ord_pl_products', 'tnt_id'=>'prd_tnt_id', 'delete'=> '', 'col_id' => ['prd_pl_id', 'prd_id', 'prd_line_id'] ];
			break;
		case CLS_INVOICE :
			$ar_physical = ['name'=>'ord_invoices', 'tnt_id'=>'inv_tnt_id', 'delete'=> 'inv_masked', 'col_id' => 'inv_id' ];
			break;
		case CLS_BL :
			$ar_physical = ['name'=>'ord_bl', 'tnt_id'=>'bl_tnt_id', 'delete'=> 'bl_masked', 'col_id' => 'bl_id' ];
			break;
		case CLS_CLASSIFY :
			$ar_physical = ['name'=>'prd_classify', 'tnt_id'=>'cly_tnt_id', 'delete'=> '', 'col_id' => ['cly_cat_id', 'cly_prd_id'] ];
			break;
		case CLS_ACCOUNTING_CATEGORY :
			$ar_physical = ['name'=>'gu_accounting_categories', 'tnt_id'=>'cac_tnt_id', 'delete'=> '', 'col_id' => 'cac_id' ];
			break;
		case CLS_PRICE_CATEGORY :
			$ar_physical = ['name'=>'prd_prices_categories', 'tnt_id'=>'prc_tnt_id', 'delete'=> 'prc_is_deleted', 'col_id' => 'prc_id' ];
			break;
		case CLS_PROFIL :
			$ar_physical = ['name'=>'gu_profiles', 'tnt_id'=>'prf_tnt_id', 'delete'=> 'prf_is_deleted', 'col_id' => 'prf_id' ];
			break;
		case CLS_NLR_SUBSCRIBERS :
			$ar_physical = ['name'=>'nlr_subscribers', 'tnt_id'=>'sub_tnt_id', 'delete'=> 'sub_date_deleted', 'col_id' => 'sub_id' ];
			break;
		case CLS_PMT_CODE :
			$ar_physical = ['name'=>'pmt_codes', 'tnt_id'=>'cod_tnt_id', 'delete'=> 'cod_date_deleted', 'col_id' => 'cod_id' ];
			break;
		case CLS_NLR_CATEGORY :
			$ar_physical = ['name'=>'nlr_cat', 'tnt_id'=>'cnt_tnt_id', 'delete'=> 'cnt_date_deleted', 'col_id' => 'cnt_id' ];
			break;
		case CLS_DLV_SERVICE :
			$ar_physical = ['name'=>'dlv_services', 'tnt_id'=>'srv_tnt_id', 'delete'=> 'srv_date_deleted', 'col_id' => 'srv_id' ];
			break;
		case CLS_ORD_PAYMENT :
			$ar_physical = ['name'=>'ord_payment_types', 'tnt_id'=>'pay_tnt_id', 'delete'=> 'pay_is_deleted', 'col_id' => 'pay_id' ];
			break;
		case CLS_IMAGE :
			$ar_physical = ['name'=>'img_images', 'tnt_id'=>'img_tnt_id', 'delete'=> 'img_date_deleted', 'col_id' => 'img_id' ];
			break;
		case CLS_PRICE:
			$ar_physical = ['name'=>'prc_prices', 'tnt_id'=>'prc_tnt_id', 'delete'=>'prc_date_deleted', 'col_id'=>'prc_id' ];
			break;
		case CLS_CTR_CATALOGS:
			$ar_physical = ['name'=>'ctr_catalogs', 'tnt_id'=>'ctl_tnt_id', 'delete'=>'', 'col_id'=>['ctl_ctr_id', 'ctl_prd_id'] ];
			break;
		case CLS_DEPOSIT:
			$ar_physical = ['name'=>'prd_deposits', 'tnt_id'=>'dps_tnt_id', 'delete'=>'', 'col_id'=>'dps_id'];
			break;
		case CLS_CTR_MKT:
			$ar_physical = ['name'=>'ctr_comparators', 'tnt_id'=>'', 'delete'=>'', 'col_id'=>'ctr_id'];
			break;
		case CLS_REPORT:
			$ar_physical = ['name'=>'rp_reports', 'tnt_id'=>'rpr_tnt_id', 'delete'=>'rpr_date_deleted', 'col_id'=>'rpr_id'];
			break;
		case CLS_IMAGES_OBJECT:
			$ar_physical = ['name'=>'img_images_objects', 'tnt_id'=>'imo_tnt_id', 'delete'=>'imo_date_deleted', 'col_id'=>'imo_id'];
			break;
		case CLS_DEV_SUB:
			$ar_physical = ['name'=>'dev_subscribtions', 'tnt_id'=>'ds_tnt_id', 'delete'=>'ds_date_deleted', 'col_id'=>'ds_id'];
			break;
		case CLS_RETURN_PRODUCTS:
			$ar_physical = ['name'=>'ord_returns_products', 'tnt_id'=>'prd_tnt_id', 'delete'=>'', 'col_id'=>['prd_return_id','prd_line_id'] ];
			break;
		default :
			if( fld_classes_is_physical($cls_id) ){
				error_log( __FILE__.':'.__LINE__.' Classe physique non definie (tenant : '.$config['tnt_id'].', website : '.$config['wst_id'].') : '. $cls_id );
				return false;
			}
			break;
	}

	return $ar_physical;
}
/// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le nombre d'objet rattaché à une classe.
 *	@param int $id Obligatoire, identifiant d'une classe
 *	@return int|bool Le nombre d'objet de la classe, False dans le cas où le paramètre est omis ou faux.
 */
function fld_classes_get_objects_count( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	if( !fld_classes_is_physical($id) ){
		return false;
	}

	$ar_physical = fld_classes_get_physical_structure( $id );
	if( !is_array($ar_physical) ){
		return false;
	}

	if( is_array($ar_physical) ){
		$sql = '
			select 1
			from '.$ar_physical['name'].'
			where '.$ar_physical['tnt_id'].'='.$config['tnt_id'].'
		';

		if( trim($ar_physical['delete'])!='' ){
			if( strpos($ar_physical['delete'], '_is_') !== false || strpos($ar_physical['delete'], 'masked') !== false ){
				$sql .= ' and '.$ar_physical['delete'].' = 0';
			}else{
				$sql .= ' and '.$ar_physical['delete'].' is null';
			}
		}
	}else{
		$sql = '
			select 1
			from fld_objects
			where obj_tnt_id='.$config['tnt_id'].'
				and obj_cls_id='.$id.'
				and obj_date_deleted is null
		';
	}

	$res = ria_mysql_query( $sql );
	if( !$res ){
		return false;
	}

	return ria_mysql_num_rows( $res );
}
// \endcond

// \cond onlyria
/** Détermine le nombre de modèles assignés à cette classe
 * 	@param int $id Identifiant de la classe
 *	@return int|bool Le nombre de modèles, False en cas d'erreur
 */
function fld_classes_get_models_count( $id ){
	global $config;

	if( !fld_classes_exists($id) ) return false;

	$result = ria_mysql_query('
		select count(*)
		from fld_models
		where mdl_tnt_id='.$config['tnt_id'].'
			and mdl_cls_id='.$id.'
			and mdl_date_deleted is null
	');

	if( !$result ) return false;

	return ria_mysql_result( $result, 0, 0 );
}
// \endcond

// \cond onlyria
/** Détermine le nombre de catégories assignés à cette classe
 * 	@param int $id Identifiant de la classe
 *	@return int|bool Le nombre de catégories, False en cas d'erreur
 */
function fld_classes_get_categories_count( $id ){
	global $config;

	if( !fld_classes_exists($id) ) return false;

	$result = ria_mysql_query('
		select count(*)
		from fld_categories
		where cat_tnt_id='.$config['tnt_id'].'
		and cat_cls_id='.$id.'
		and cat_date_deleted is null
	');

	if( !$result ) return false;

	return ria_mysql_result( $result, 0, 0 );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer les patterns
 *	@param int $cls Optionnel, identifiant d'une classe
 *	@param string $lng Optionnel, code ISO 639-1 d'une langue
 *	@return string Retourne le pattern (par défaut celui de la langue du site)
 *	@return bool Retourne false si l'un des paramètres est faux
 */
function fld_classes_get_pattern( $cls=0, $lng=false ){
	global $config;

	if( $cls>0 && !fld_classes_exists($cls) ){
		return false;
	}

	$lng_used = isset($config['i18n_lng_used']) ? $config['i18n_lng_used'] : ( isset($config['i18n_lng']) ? $config['i18n_lng'] : 'fr' );
	$lng = $lng && in_array( strtolower($lng), $lng_used ) ? $lng : $config['i18n_lng'];

	$sql = '
		select ptn_pattern as pattern
		from fld_classes_patterns
		where (ptn_tnt_id=0 or ptn_tnt_id='.$config['tnt_id'].')
			and ptn_lng_code=\''.strtolower($lng).'\'
	';

	if( $cls>0 ){
		$sql .= ' and ptn_cls_id='.$cls;
	}

	$sql .= '
		order by ptn_tnt_id desc
	';

	$res = ria_mysql_query( $sql );
	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	return ria_mysql_result( $res, 0, 'pattern' );
}
// \endcond

// \cond onlyria
/**	Cette fonction détermine si une classe est spécifique à un locataire ou si elle est générique
 *	@param int $cls_id Identifiant de la classe
 *	@return bool False en cas d'erreur
 *	@return int L'identifiant du locataire (donc 0 pour générique)
 */
function fld_classes_is_tenant_linked( $cls_id ){
	global $config;

	if( !is_numeric($cls_id) || $cls_id<=0 )return false;

	$r = ria_mysql_query('
		select cls_tnt_id from fld_classes
		where cls_id='.$cls_id.' and cls_date_deleted is null and cls_tnt_id in (0, '.$config['tnt_id'].')
	');

	if( !ria_mysql_num_rows($r) ) return false;

	return ria_mysql_result( $r, 0, 0 );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour les informations d'une classe.
 *	@param int $id Obligatoire, identifiant d'une classe
 *	@param string $name Obligatoire, nom de la classe
 *	@param string $desc Optionnel, description de la classe
 *	@return bool True si la mise à jour s'est correctement passée, False dans le cas contraire
 */
function fld_classes_update( $id, $name, $desc='' ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	if( trim($name)=='' ){
		return false;
	}

	$res = ria_mysql_query('
		update fld_classes
		set cls_name=\''.addslashes( $name ).'\',
			cls_desc='.( trim($desc)!='' ? '\''.addslashes( $desc ).'\'' : 'null' ).'
		where cls_tnt_id='.$config['tnt_id'].'
			and cls_id='.$id.'
	');

	if( !$res ){
		return false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la date de modification de la classe
 * @param int $id Obligatoire, identifiant de la classe
 *
 * @return bool true si la mise à jour s'est correctement passés, false dans le cas contraire
 */
function fld_classes_set_date_modified( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		update fld_classes
		set cls_date_modified = now()
		where cls_tnt_id = '.$config['tnt_id'].'
			and cls_id = '.$id.'
	');

	if( !$res ){
		return false;
	}

	return true;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de supprimer une classe personnalisée
 *	@param int $cls_id Identifiant de la classe
 *	@return bool False en cas d'échec, True en cas de succès
 */
function fld_classes_del( $cls_id ){
	global $config;

	if( !fld_classes_is_tenant_linked( $cls_id ) ) return false;

	// Date de suppression
	$date_deleted = date( 'Y-m-d H:i:s' );

	// Supprime la classe
	$res = ria_mysql_query('
		update fld_classes
		set cls_date_deleted=\''.$date_deleted.'\'
		where cls_id='.$cls_id.'
			and cls_tnt_id='.$config['tnt_id'].'
	');

	if( !$res ){
		error_log( __FILE__.':'.__LINE__.' '.mysql_error() );
		return false;
	}

	$res_del_all = true;

	// Supprime les objets rattachés à la classe
	$res = ria_mysql_query('
		update fld_objects
		set obj_date_deleted=\''.$date_deleted.'\'
		where obj_tnt_id='.$config['tnt_id'].'
			and obj_cls_id='.$cls_id.'
	');

	if( !$res ){
		$res_del_all = false;
		error_log( __FILE__.':'.__LINE__.' '.mysql_error() );
	}

	// Supprime les champs avancés rattachés à la classe
	$res = ria_mysql_query('
		update fld_fields
		set fld_date_deleted=\''.$date_deleted.'\'
		where fld_tnt_id='.$config['tnt_id'].'
			and fld_cls_id='.$cls_id.'
	');

	if( !$res ){
		$res_del_all = false;
		error_log( __FILE__.':'.__LINE__.' '.mysql_error() );
	}

	// Supprime les catégories rattachés à la classe
	$res = ria_mysql_query('
		update fld_categories
		set cat_date_deleted=\''.$date_deleted.'\'
		where cat_tnt_id='.$config['tnt_id'].'
			and cat_cls_id='.$cls_id.'
	');

	if( !$res ){
		$res_del_all = false;
	}

	// Supprime les modèles de saisie rattaché à la classe
	$res = ria_mysql_query('
		update fld_models
		set mdl_date_deleted=\''.$date_deleted.'\'
		where mdl_tnt_id='.$config['tnt_id'].'
			and mdl_cls_id='.$cls_id.'
	');

	if( !$res ){
		$res_del_all = false;
	}

	// force la mise à jour des configs sur les applications
	dev_devices_need_sync_add(0,_DEV_TASK_MODEL);

	return $res_del_all;
}
// \endcond

// \cond onlyria
/**	Cette fonction génère la sous-requête SQL permettant de filtrer les éléments d'une classe selon la ou les valeurs d'un ou plusieurs champs avancés.
 *	@param int $cls_id Obligatoire, identifiant de la classe
 *	@param int|array $fld Optionnel, champ avancé sur lequel filtrer le résultat. Ce paramètre peut être un identifiant, un tableau d'identifiants ou un tableau associatif identifiant => valeur
 *	@param $or_between_val Optionnel, dans un contexte où $fld est un tableau associatif, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque valeur possible d'un même champ (si non spécifié, la valeur logique est ET)
 *	@param $or_between_fld Optionnel, dans un contexte où $fld est un tableau, ce paramètre permet de déterminer si des OU logique sont spécifiés entre chaque champs (si non spécifié, la valeur logique est ET)
 *	@param string $lng Optionnel, permet de spécifier une autre langue que celle de la configuration actuelle. Utilse s'il existe des valeurs de champs avancés multilingues
 *	@param $alias_tbl Optionnel, permet de spécifier un alias pour la table de la classe (si c'est une classe physique)
 *	@param $check_on_childs Optionnel, spécifique à la classe produit, permet d'appliquer les filtres sur les produits enfants. Ce paramètre doit être le nom de la colonne de jointure sur le produit enfant, par exemple prdh.prd_child_id (prdh étant l'alias de la table prd_hierarchy)
 *	@param $like_type Optionnel, permet de spécifier un type de like : true pour '%valeur%', false pour tester tous les types, -1 pour un égfal strict.
 *	@param $case_insensitive Par défaut les valeurs sont sensible à la casse, mettre true pour quelle ne le soit pas
 *	@param bool $use_hierarchy_ids Optionnel, par défaut à true, mettre false pour dire d'utilisé un tableau de nom des valeurs de restriction pour recupérer les ids correspondant.
 *
 *	@return string Une sous-requête SQL de type " and ( condition(s) ) "
 */
function fld_classes_sql_get( $cls_id, $fld=false, $or_between_val=false, $or_between_fld=false, $lng=false, $alias_tbl='', $check_on_childs='', $like_type=false, $case_insensitive=false, $use_hierarchy_ids=true ){
	if( !is_numeric($cls_id) || $cls_id <= 0 ){
		return false;
	}

	global $config, $memcached;

	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : i18n::getLang();

	$key_memcached = 'fld_classes_sql_get:'.$config['tnt_id'].':'.$cls_id;
	if (($get = $memcached->get($key_memcached))) {
		if ($get == 'none') {
			return false;
		}

		$class = $get;
	} else {
		$rclass = fld_classes_get( $cls_id );
		if( !$rclass || !ria_mysql_num_rows($rclass) ){
			$memcached->set($key_memcached, 'none', 60 * 60);
			return false;
		}

		$class = ria_mysql_fetch_array($rclass);
		$memcached->set($key_memcached, $class, 60 * 60);
	}

	if( $class['physical'] ){

		if( trim($alias_tbl) ){
			$class['physical-name'] = trim($alias_tbl);
		}

		$columns = array();
		switch( $cls_id ){
			case CLS_PRODUCT: $columns[] = 'prd_id'; break;
			case CLS_USER: $columns[] = 'usr_id'; break;
			case CLS_CATEGORY: $columns[] = 'cat_id'; break;
			case CLS_ORDER: $columns[] = 'ord_id'; break;
			case CLS_BRAND: $columns[] = 'brd_id'; break;
			case CLS_STORE: $columns[] = 'str_id'; break;
			case CLS_MESSAGE: $columns[] = 'cnt_id'; break;
			case CLS_ORD_PRODUCT: $columns[] = 'prd_ord_id'; $columns[] = 'prd_id'; $columns[] = 'prd_line_id'; break;
			case CLS_PRD_COLISAGE: $columns[] = 'cly_col_id'; $columns[] = 'cly_prd_id'; break;
			case CLS_SECTOR: $columns[] = 'srv_id'; break;
			case CLS_CMS: $columns[] = 'cat_id'; break;
			case CLS_TYPE_DOCUMENT: $columns[] = 'type_id'; break;
			case CLS_FAQ_CAT: $columns[] = 'cat_id'; break;
			case CLS_NEWS: $columns[] = 'news_id'; break;
			case CLS_NEWS_CAT: $columns[] = 'cat_id'; break;
			case CLS_FAQ_QST: $columns[] = 'qst_id'; break;
			case CLS_DOCUMENT: $columns[] = 'doc_id'; break;
			case CLS_BANNER: $columns[] = 'bnr_id'; break;
			case CLS_CGV_ARTICLE: $columns[] = 'art_wst_id'; $columns[] = 'art_id'; break;
			case CLS_PRD_RELATIONS: $columns[] = 'rel_src_id'; $columns[] = 'rel_dst_id'; $columns[] = 'rel_type_id'; break;
			case CLS_ERRATUM: $columns[] = 'err_wst_id'; $columns[] = 'err_id'; break;
			case CLS_WEBSITE: $columns[] = 'wst_id'; break;
			case CLS_INV_PRODUCT: $columns[] = 'prd_inv_id'; $columns[] = 'prd_id'; $columns[] = 'prd_line_id'; break;
			case CLS_BL_PRODUCT: $columns[] = 'prd_bl_id'; $columns[] = 'prd_id'; $columns[] = 'prd_line_id'; break;
			case CLS_PL_PRODUCT: $columns[] = 'prd_pl_id'; $columns[] = 'prd_id'; $columns[] = 'prd_line_id'; break;
			case CLS_INVOICE: $columns[] = 'inv_id'; break;
			case CLS_BL: $columns[] = 'bl_id'; break;
			case CLS_CLASSIFY: $columns[] = 'cly_cat_id'; $columns[] = 'cly_prd_id'; break;
			case CLS_PMT_CODE: $columns[] = 'cod_id'; break;
			case CLS_PRD_RESELLER: $columns[] = 'prs_wst_id'; $columns[] = 'prs_prd_id'; $columns[] = 'prs_usr_id'; break;
			default: error_log( __FILE__.':'.__LINE__.' classe non gérée : '.$cls_id ); break;
		}

		if( !sizeof($columns) || trim($class['physical-name'])=='' ){
			return '';
		}

	}

	$sql = '';

	if( !is_array($fld) ){
		$fldar = array();
		if( is_numeric($fld) && $fld>0 ){
			$fldar[ $fld ] = '';
		}
		$fld = $fldar;
	}

	if( sizeof($fld) ){
		$tab = array();
		foreach( $fld as $key=>$val ){
			$subvar = array();
			if( fld_fields_get_class( $key ) == $cls_id ){
				if( !is_array($val) ){
					$val = array( $val );
				}
				foreach( $val as $v ){
					// $i = 0 : parent, $i = 1 : enfant (si activé)
					$subv_ch_or_not = array();
					for( $i = 0; $i < 2; $i++ ){
						if( $i > 0 && trim($check_on_childs) == '' ){
							continue;
						}
						$subv = '
							select 1 from fld_object_values
							where pv_tnt_id='.$config['tnt_id'].' and pv_lng_code="'.strtolower($lng).'"
							and pv_fld_id='.$key.'
						';
						if( $class['physical'] ){
							if( $cls_id == CLS_PRODUCT ){
								$subv .= ' and pv_obj_id_0 = '.( $i > 0 ? $check_on_childs : $class['physical-name'].'.'.$columns[0] );
								$subv .= " and pv_obj_id_1=0 ";
								$subv .= " and pv_obj_id_2=0 ";
							}else{
								for( $i=0; $i<sizeof($columns); $i++ ){
									$subv .= ' and pv_obj_id_'.$i.' = '.$class['physical-name'].'.'.$columns[$i];
								}

								if (count($columns) < 3) {
									for ($i=count($columns); $i < 3; $i++) {
										$subv .= ' and pv_obj_id_'.$i.'=0 ';
									}
								}
							}
						}else{
							$subv .= ' and pv_obj_id_0 = obj_id';
							$subv .= " and pv_obj_id_1=0 ";
							$subv .= " and pv_obj_id_2=0 ";
						}

						if( !is_array($v) && trim($v) ){
							$fld_type = fld_fields_get_type( $key );

							if( $fld_type == FLD_TYPE_SELECT_HIERARCHY && !$use_hierarchy_ids ){
								$v = fld_restricted_values_get_id( $key, trim($v), strtolower($lng) );
							}

							$subv .= '
								and (
							';
							if( $like_type === -1 ){
								if( !$case_insensitive ){
									$subv .= 'pv_value = "'.addslashes($v).'"';
								}else{
									$subv .= 'lower(pv_value) = lower("'.addslashes($v).'")';
								}
							}elseif( $like_type ){
								if( !$case_insensitive ){
									$subv .= 'pv_value like "%'.addslashes($v).'%"';
								}else{
									$subv .= 'lower(pv_value) like lower("%'.addslashes($v).'%")';
								}
							}else{
								if( !$case_insensitive ){
									$subv .= '
										pv_value = "'.addslashes($v).'"
										or pv_value like "'.addslashes($v).',%"
										or pv_value like "%, '.addslashes($v).',%"
										or pv_value like "%, '.addslashes($v).'"
									';
								}else{
									$subv .= '
										lower(pv_value) = lower("'.addslashes($v).'")
										or lower(pv_value) like lower("'.addslashes($v).',%")
										or lower(pv_value) like lower("%, '.addslashes($v).',%")
										or lower(pv_value) like lower("%, '.addslashes($v).'")
									';
								}
							}
							$subv .= '
								)
							';
						}
						$subv_ch_or_not[] = $subv;
					}
					if( !empty($subv_ch_or_not)){
						if( sizeof($subv_ch_or_not) > 1 ){
							$subvar[] = 'exists ('.$subv_ch_or_not[0].') or exists ('.$subv_ch_or_not[1].')';
						}else{
							$subvar[] = 'exists ('.$subv_ch_or_not[0].')';
						}
					}
				}
			}

			if( sizeof($subvar) ){
				if( !$or_between_fld && $or_between_val ){
					$tab[$key] = array( 'size'=>sizeof($subvar), 'sql' => implode( ($or_between_val ? ' or ' : ' and '), $subvar ) );
				}else{
					$tab[$key] = implode( ($or_between_val ? ' or ' : ' and '), $subvar );
				}
			}
		}

		if( sizeof($tab) ){
			if( !$or_between_fld && $or_between_val ){
				$temp_tab = array_msort( $tab, array('size'=>SORT_ASC) );

				$tab = array();
				foreach( $temp_tab as $k=>$t ){
					$tab[ $k ] = $t['sql'];
				}
			}

			$sql = ' and (
				('.implode(') '.( $or_between_fld ? 'or' : 'and' ).' (', $tab).')
			) ';
		}
	}

	return $sql;
}
// \endcond

// \cond onlyria
/**	Détermine si la classe est segmentable
 *	@param int $id Identifiant de la classe
 *	@return bool True si la classe est segmentable, False sinon
 */
function fld_classes_is_segmentable( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	$r = ria_mysql_query('
		select cls_is_segmentable as "seg"
		from fld_classes
		where cls_id = '.$id.' and cls_tnt_id in (0, '.$config['tnt_id'].')
	');

	if( !$r || !ria_mysql_num_rows($r) ){
		return false;
	}

	return ria_mysql_result($r, 0, 'seg');
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère les classes segmentables et les transforment en un tableau d'identifiants.
 * 	Pour l'heure, seules les classes CLS_USER et CLS_STORE sont actuellement supportées.
 *	@return array Un tableau des identifiants des classes segmentables. Le tableau est vide en cas d'erreur.
 */
function fld_classes_get_segmentable_array(){

	return array( CLS_USER, CLS_STORE );

	/*global $config, $memcached;

	if( $res = $memcached->get('fld_classes_get_segmentable_array') ){
		return $res;
	}

	$ar = array();
	if( $rcls = fld_classes_get( 0, false, true, true, true ) ){
		while( $c = ria_mysql_fetch_array($rcls) ){
			$ar[] = $c['id'];
		}
	}

	$time_cache = isset($config['time_cache_fields']) && is_numeric($config['time_cache_fields']) && $config['time_cache_fields'] > 0 ? $config['time_cache_fields'] : 3600;
	$memcached->set( 'fld_classes_get_segmentable_array', $ar, $time_cache );
	return $ar;*/
}
// \endcond

// \cond onlyria
/**	Cette fonction récupère les classes segmentables et les transforment en un tableau d'identifiants
 *	@return array Un tableau des identifiants des classes segmentables. Le tableau est vide en cas d'erreur.
 */
function fld_classes_get_triggerable_array(){
	global $memcached;

	$ar = array();
	if( $rcls = fld_classes_get( 0, false, true, true ) ){
		while( $c = ria_mysql_fetch_array($rcls) ){
			$ar[] = $c['id'];
		}
	}

	return $ar;
}
// \endcond

/**
 * Cette fonction permet de paramétrer un objet Form en fonction d'un formulaire dynamique de riashop
 *
 * @param integer $cls_id Identifiant du formulaire dynamique
 * @return bool|array Reourne false si échec du chargement des champs, sinon retourne le tableau avec les champs du formulaire
 */
function fld_classes_get_form_fields($cls_id) {
	if( !is_numeric($cls_id) || $cls_id <= 0 ) {
		return false;
	}

	$fields = array();

	$rfields = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array('fld-pos'=>'asc'), false, array(), null, $cls_id );

	if( !$rfields || !mysql_num_rows($rfields) ){
		return false;
	}

	while( $field = mysql_fetch_assoc($rfields) ){

		$type = false;
		$cls_css = false;
		$data = false;
		$display = false;
		$mandatory = ( is_numeric($field['min']) && $field['min'] > 0 );
		$key = $field['id'];

		switch( $field['type_id'] ){
			case FLD_TYPE_TEXT: {
				$type = 'text';
				$cls_css = 'in-text';
				break;
			}
			case FLD_TYPE_TEXTAREA: {
				$type = 'textarea';
				$cls_css = 'in-textarea';
				break;
			}
			case FLD_TYPE_DATE: {
				$type = 'text';
				$cls_css = 'in-text';
				break;
			}
			case FLD_TYPE_INT:
			case FLD_TYPE_FLOAT: {
				$type = 'number';
				$cls_css = 'in-text';
				break;
			}
			case FLD_TYPE_SELECT: {
				$type = 'select';
				$cls_css = 'in-select';

				$data = array();

				$values = fld_restricted_values_get( 0, $field['id'] );
				while( $r = mysql_fetch_array($values) ){
					$data[ $r['id'] ] = $r['name'];
				}
				break;
			}
			case FLD_TYPE_SELECT_MULTIPLE: {
				$type = 'select';
				$cls_css = 'in-select';
				$display = 'checkbox';
				$key .= '[]';

				$data = array();

				$values = fld_restricted_values_get( 0, $field['id'] );
				while( $r = mysql_fetch_array($values) ){
					$data[ $r['id'] ] = array('title' => $r['name']);
				}
				break;
			}
			case FLD_TYPE_BOOLEAN_YES_NO: {
				$type = 'bool';
				$cls_css = 'in-text';
				break;
			}
			case FLD_TYPE_SELECT_HIERARCHY: {
				$type = 'select';
				$cls_css = 'in-select';

				$data = array();

				$rvalue = fld_restricted_values_get( 0, $field['id'] );
				while( $values = mysql_fetch_assoc($rvalue) ){
					$data[ $values['id'] ] = $values['name'];
				}
				break;
			}
			case FLD_TYPE_IMAGE : {
				break;
			}
		}

		$fields[$key]= array(
			'name' => $field['name'],
			'type' => $type,
			'mandatory' => $mandatory,
			'class_css' => $cls_css,
			'data' => $data ? $data : false,
			'display' => $display ? $display : false,
		);
	}

	return $fields;
}

/// @}
