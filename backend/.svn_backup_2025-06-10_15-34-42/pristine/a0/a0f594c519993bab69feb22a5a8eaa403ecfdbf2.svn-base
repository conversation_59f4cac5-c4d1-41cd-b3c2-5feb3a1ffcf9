<?php

// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_DOCS_HOST');

// Fil d'ariane
Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
	->push( _('Médiathèque'), '/admin/documents/index.php' )
	->push( _('Hébergeurs externes') );

// Défini le titre de la page
define('ADMIN_PAGE_TITLE', _('Hébergeurs externes'));
require_once('admin/skin/header.inc.php');

$r_hosts = doc_hosts_get();

?>
<h2><?php print _("Hébergeurs externes"); ?></h2>
<p class="notice"><?php print _('Vous trouverez ci-dessous la liste des plateformes tierces sur lesquelles vous pouvez également héberger des contenus multimédias (vidéo, photos, playlists, ...).'); ?></p>
<table id="table-hosts" class="checklist">
	<thead class="thead-none">
		<tr>
			<th id="hst-name"><?php print _('Désignation'); ?></th>
			<th id="hst-channels" class="align-right"><?php print _('Chaines'); ?></th>
			<th id="hst-playlists" class="align-right"><?php print _('Playlists'); ?></th>
			<th id="hst-medias" class="align-right"><?php print _('Médias'); ?></th>
		</tr>
	</thead>
	<tbody>
		<?php if( $r_hosts && ria_mysql_num_rows($r_hosts) ){
			while( $host = ria_mysql_fetch_assoc($r_hosts) ){
				$stats = doc_hosts_get_stats($host['id']); ?>
				<tr>
					<td headers="hst-name" data-label="<?php print _('Désignation :'); ?> ">
						<a href="/admin/documents/medias/index.php?hst=<?php print $host['id']; ?>" title="<?php print _('Afficher les informations sur cet hébergeur'); ?>"><?php print htmlspecialchars($host['name']); ?></a>
					</td>
					<td headers="hst-channels" class="align-right" data-label="<?php print _('Chaines :'); ?> "><?php print ria_number_format($stats['channels']); ?></td>
					<td headers="hst-playlists" class="align-right" data-label="<?php print _('Playlists :'); ?> "><?php print ria_number_format($stats['playlists']); ?></td>
					<td headers="hst-medias" class="align-right" data-label="<?php print _('Médias :'); ?> "><?php print ria_number_format($stats['medias']); ?></td>
				</tr>
			<?php }
		}else{ ?>
			<tr>
				<td colspan="2"><?php print _('Aucune hébergeur de disponibles pour le moment'); ?></td>
			</tr>
		<?php } ?>
	</tbody>
</table>

<?php

require_once('admin/skin/footer.inc.php');