<?php
	
	/**	\file export-wo-result.php
	 * 
	 * 	Ce fichier exporte les données de l'écran "Recherches sans résultat" (search-wo-results.php) au format CSV
	 * 
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_SEARCH_NO_RESULT');

	require_once('websites.inc.php');

	$website = wst_websites_get();

	$date1 = isset($_SESSION['datepicker_date1']) ? $_SESSION['datepicker_date1'] : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? $_SESSION['datepicker_date2'] : false;
	$date1 = dateparse($date1); $date2 = dateparse($date2);
	
	$_GET['wst'] = isset($_GET['wst']) && $_GET['wst']>0 ? $_GET['wst'] : false;
	$stats = search_log_get( $_GET['wst'], $date1, $date2, true, false, array(), 0, '', '', false );
	
	$file = _('recherches-sans-resultats-');
	$file .= $date1 ? date('d-m-Y', strtotime($date1) ) : '';
	$file .= $date2 && $date1!=$date2 ? '-'.date('d-m-Y', strtotime($date2) ) : '';
	
	header('Content-Type: application/csv');
	header('Content-disposition: filename='.$file.'.csv');
	
	$csv = '';
	
	// Entête du fichier CSV
	$csv .= _("Recherche").';'._("Sections").';'._("Filtres").';'._("Volume").';'.( $_GET['wst']>0 || ria_mysql_num_rows($website)==1 ? '' : _("Site Web").';' )."\n";
	
	while( $stat = ria_mysql_fetch_array($stats) ){
		$section = _('Toutes');
		$types = search_log_get_types($stat['scc']);

		$csv .= '"'.$stat['search'].'";';
		$csv .= '"'.$section.'";';
		$csv .= '"'.$types.'";';
		$csv .= ''.$stat['volume'].';';
		if( !$_GET['wst'] && ria_mysql_num_rows($website)>1 ){
			$wst = ria_mysql_fetch_array( wst_websites_get($stat['wst_id']) );
			$csv .= ( !$_GET['wst'] ? '"'.$wst['name'].'";' : '' );
		}
		$csv .= "\n";
	}

	print utf8_decode($csv);

