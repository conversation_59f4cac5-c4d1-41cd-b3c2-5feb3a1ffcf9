<?php
require_once 'Services/Service.class.php';

require_once('search.inc.php');

/**	\brief Cette classe permet une recherche sur différents moteurs.
 * 	Une seule instance de cette classe peut existe à la fois
 *	La recherche porte obligatoirement sur un type de contenu.
 *
 * 	@warning /!\ ATTENTION /!\
 * 	@warning Le moteur de recherche (search_engines) n°1 est réservé à une recherche exclusive de produits
 */
final class SearchService extends Service {
	const ENGINE_CATALOG = 1; ///< Constante contenant l'identifiant du moteur pour la recherche d'article

	private static $instance = null;

	private $count = 0; ///< Nombre de résultats retournés
	private $results = []; ///< Tableau contenant les résultats retourné par le moteur RiaShop
	private $engine = 0; ///< Identifiant du moteur utilisé par la recherche
	private $section = false; ///< Identifiant d'une section de recherche

	private $ar_prd_ids = null; ///< Tableau contenant les identifiants de produits présents dans la recherche
	private $univers = null; ///< Tableau contenant les catégories dans lesquelles se trouvent les produits

	/** Cette fonction permet d'initialiser une recherche.
	 * 	@param int $engine Obligatoire, moteur de recherche utilisé
	 * 	@param int $univers Optionnel, univers de recherche (ex. identifiant d'une catégorie de produits)
	 * 	@param bool $forced Optionnel, permet de forcer un nouveau moteur de recherche (utile dans le cas d'une recherche sur plusieurs onglets)
	 * 	@return SearchService L'objet courant
	 */
	public static function getInstance( $engine, $univers=0, $forced=false ){
		if( is_null(self::$instance) || $forced ){
			self::$instance = new SearchService( $engine, $univers );
		}

		return self::$instance;
	}

	/** Cette fonction permet de lancer une recherche.
	 * 	@param string $keyword Obligatoire, recherche a effectuer
	 * 	@return SearchService L'objet courant
	 */
	public function search( $keywords ){
		global $config;

		$this->results = new Collection();

		$maxlength = 0;
		if( isset($config['prd_search_length']) && is_numeric($config['prd_search_length']) && $config['prd_search_length'] > 0 ){
			$maxlength = $config['prd_search_length'];
		}

		// Lance la recherche
		$r_result = search3( $this->engine, $keywords, 1, $maxlength, true, false, 6 );
		if( $r_result ){
			while( $result = ria_mysql_fetch_assoc($r_result) ){
				$key_result = $result['type_code'].$result['tag'];

				if( !$this->results->exitsKey($key_result) ){
					$this->results->addItem( $result, $key_result );
				}
			}

			$this->count = $this->results->length();
		}

		return $this;
	}

	/** Cette fonction permet de lancer une recherche produit et d'avoir le résultat avec la liste des univers.
	 * 	@param string $keyword Obligatoire, recherche a effectuer
	 * 	@param int $univers Optionnel, permet de filtrer le résultat sur un univers
	 * 	@param int $page Optionnel, page sur laquelle on se trouve
	 * 	@return array Un tableau du résultat final contenant :
	 * 		- engine  : identifiant du moteur de recherche
	 * 		- filters : filtres applicables
	 * 		- univers_selected : Nom de l'univers utilisé
	 * 		- univers : liste des univers
	 * 		- results : liste des articles
	 * 		- resultscount : nombre de résutlats
	 */
	public function searchProductsWithUnivers( $keyword, $univers=0, $page=1 ){
		global $config;

		// Lance la recherche initiale
		$this->search( $keyword );

		// Charge la liste des univers correspondant au résultat
		$list_univers = $this->getUnivers( 2 );

		// Applique le filtre sur l'univers
		$univers_selected = '';
		if( $univers > 0 ){
			$univers_selected = $this->applyUnivers( $univers );
		}

		return [
			'engine' => $this->engine,
			'filters' => $this->filters(),
			'univers' => $list_univers,
			'univers_selected' => $univers_selected,
			'results' => $this->results( ($config['prd_list_length'] * ($page - 1)), $config['prd_list_length'] ),
			'resultscount' => $this->count(),
		];
	}

	/** Cette fonction permet de récupérer les filtres applicables à un résultat de recherche.
	 * 	Ces filtres ne sont que dans le cadre d'une recherche de produits.
	 * 	@return array Un tableau contenant les filtres
	 */
	public function filters(){
		global $config;

		// Un tableau vide est retourné
		// 	- soit car il ne s'agit pas d'une recherche de produit (moteur de recherches = self::ENGINE_CATALOG)
		// 	- soit car il n'y a aucun résultat
		if( $this->engine !== self::ENGINE_CATALOG || !($this->results instanceof Collection) || !$this->results->length() ){
			return [];
		}

		// Charge un tableau des identifiants de produits retournés par le moteur de recherche
		$ar_prd_ids = $this->loadProductIDs();

		// Récupère les filtres liés aux articles retournés par le moteur de recherche
		$this->section->setPrdListID( $ar_prd_ids )->filters()->setFilters();

		// Transforme l'objet en tableau pour renvoyer le tableau des filtres
		$temp = $this->transformObjectToArray( $this->section );
		return $temp['filters'];
	}

	/** Cette fonction permet de récupérer le resultat de la recherche sous forme de tableau.
	 * 	@param int $start Optionnel, permet de préciser à partir de quel curseur on récupère les résultats
	 * 	@param bool|int $limit Optionnel, permet de préciser combien de résultats sont récupérées (par défaut 20)
	 *
	 * 	@return array Un tableau contenant les résultats, chaque résultat sera retourné en fonction de sa classe (ex ProductService pour les produits),
	 * 	toujours sous forme de tableau
	 */
	public function results( $start=0, $limit=false, $sort=false ){
		global $config;

		if( !($this->results instanceof Collection)){
			return [];
		}

		if( !is_numeric($start) || $start < 0 ){
			$start = 0;
		}

		if( !is_numeric($limit) || $limit <= 0 ){
			$limit = 20;
		}

		$temp = $this->results->getAll();

		// Recherche de produit
		if( $this->engine == self::ENGINE_CATALOG ){
			// Charge un tableau des identifiants de produits
			// Les produits seront unique dans le résultat final et non pas classement indexé

			if( count($temp) ){
				$ar_compl_url = [];
				$ar_prd_ids = [];

				foreach( $temp as $one_res ){
					$ar_prd_ids[$one_res['tag']] = $one_res['tag'];

					if( isset($one_res['id'], $one_res['seg'], $one_res['scc']) ){
						$ar_compl_url[ $one_res['tag'] ] = '?seg='.$one_res['seg'].'&scc='.$one_res['scc'].'&cnt='.$one_res['id'];
					}
				}

				// Si aucun tri n'est appliqué alors le résultat du moteur de recherche servira de tri
				if( $sort === false ){
					$sort = 'set';
				}

				// Chargement des produits à partir de la cat_root
				// Cela permet d'appliquer filtre et tri
				$this->section->setPrdListID( $ar_prd_ids )->setFilters()->products( $start, $limit, $sort, false, ['fields'] );
				if( count($ar_compl_url) ){
					$this->section->complSearchUrl( $ar_compl_url );
				}
			}

			$ar_results = $this->section->getPrds();
			$this->count = $this->section->getCountProducts();
		}else{
			$ar_results = new Collection();
			if( count($temp) > $start ){
				$temp = array_slice( $temp, $start, $limit );

				foreach( $temp as $one_res ){
					$ar_obj = false;

					switch( $one_res['type_code'] ){
						case 'prd':
							$obj = new ProductService( ['prd' => $one_res['tag']] );
							$obj->card();

							$ar_obj = $obj->getData();
							if( isset($one_res['id'], $one_res['seg'], $one_res['scc']) ){
								$ar_obj['url'] .= '?seg='.$one_res['seg'].'&scc='.$one_res['scc'].'&cnt='.$one_res['id'];
							}
							break;
						default:
							$ar_obj = $one_res;
							if( isset($one_res['id'], $one_res['seg'], $one_res['scc']) ){
								$ar_obj['url'] .= '?seg='.$one_res['seg'].'&scc='.$one_res['scc'].'&cnt='.$one_res['id'];
							}

							if( !is_numeric($ar_obj['img_id']) || $ar_obj['img_id'] <= 0 ){
								$ar_obj['img_id'] = $config['default_image'];
							}
							break;
					}

					if( $ar_obj !== false ){
						$ar_results->addItem( $ar_obj );
					}
				}
			}
		}

		return $this->transformObjectToArray( $ar_results );
	}

	/** Cette fonction retourne le nombre de résutlats pour une recherche.
	 * 	@return int Le nombre de résultats
	 */
	public function count(){
		return $this->count;
	}

	/** Cette fonction permet de récupérer le tableau des univers liés à la recherche.
	 * 	Ces univers sont ceux où sont classés les produits retournés par la recherche.
	 *	@param null|int $depth Optionnel, niveau de profondeurs où les catégories seront récupérées
	 * 	@return array Un tableau contenant les univers
	 */
	public function getUnivers( $depth=null ){
		return $this->loadUnivers( $depth );
	}

	/** Cette fonction permet de charger le tableau des identifiants produits présents dans le résultat d'une recherche.
	 * 	@return array Le tableau des identifiant produit
	 */
	public function loadProductIDs(){
		if( $this->ar_prd_ids === null ){
			$this->ar_prd_ids = [];

			$temp = $this->results->getAll();
			foreach( $temp as $one_prd ){
				if( $one_prd['type_code'] != 'prd' ){
					continue;
				}

				$this->ar_prd_ids[] = $one_prd['tag'];
			}
		}

		return $this->ar_prd_ids;
	}

	/** Cette fonction permet d'initialiser une recherche.
	 * 	@param int $engine Obligatoire, moteur de recherche utilisé
	 * 	@param int $univers Optionnel, univers de recherche (ex. identifiant d'une catégorie de produits)
	 * 	@return SearchService L'objet courant
	 */
	private function __construct($engine, $univers=0 ){
		global $config;

		// Paramètre le moteur de recherche sur lequel la recherche sera lancé
		$this->engine = $engine;

		// Paramètre la section de recherche
		// Pour une recherche de produit, par défaut il s'agira de la cat_root
		if( $this->engine == self::ENGINE_CATALOG ){
			if( !$univers ){
				$univers = $config['cat_root'];
			}

			$this->section = new CategoryService( [], ['cat' => $univers] );
		}

		return $this;
	}

	/** Cette fonction permet de charger les univers liés à une recherche de produit.
	 *	@param null|int $depth Optionnel, niveau de profondeurs où les catégories seront récupérées
	 *	@return arrray Le tableau des catégories
	 */
	private function loadUnivers( $depth=null ){
		global $config;

		// Le tableau des univers n'est chargé qu'une seule fois
		if( $this->univers === null ){
			// Initialise le tableau conteant les catégories liés aux produits retournés par le moteur de recherche
			$this->univers = [];

			// Charge un tableau des identifiants de produits retournés par le moteur de recherche
			$ar_prd_ids = $this->loadProductIDs();
			if( count($ar_prd_ids) ){
				// Recherche les catégories de seconds niveau
				$r_category = prd_products_categories_get( $this->ar_prd_ids, true, true, null, $config['cat_root'], $depth );

				if( $r_category ){
					while( $category = ria_mysql_fetch_assoc($r_category) ){
						if( !array_key_exists($category['cat'], $this->univers) ){
							$this->univers[ $category['cat'] ] = [
								'id' => $category['cat'],
								'title' => $category['title'],
								'products' => [],
							];
						}

						$this->univers[ $category['cat'] ]['products'][ $category['prd_id'] ] = $category['prd_id'];
					}
				}
			}
		}

		return $this->univers;
	}

	/** Cette fonction permet d'appliquer un univers sur le résultats
	 * 	/!\ Ne fonctionne que sur des résultats de type "Produit" /!\
	 * 	@param int $univers Obligatoire, identifiant d'un univers
	 * 	@return array Un tableau contenant l'univers utilisé avec :
	 * 		- id : identifiant de l'univers
	 * 		- title : nom de l'univers
	 * 	@return array Un tableau vide si l'univers est faux ou inexistant
	 */
	private function applyUnivers( $univers ){
		// Si l'univers n'est pas défini, le résultat de la recherche sera vide
		if( !is_numeric($univers) || $univers <= 0 ){
			return [];
		}

		// Charge les univers, le chargement est unique et réalisé au premier appel
		$this->loadUnivers();

		// Charge les produits de l'univers
		$prds_in_univers = [];
		$univers_selected = [];
		if( isset($this->univers[ $univers ]) ){
			$prds_in_univers = $this->univers[ $univers ]['products'];
			$univers_selected = [
				'id' => $this->univers[ $univers ]['id'],
				'title' => $this->univers[ $univers ]['title'],
			];
		}

		$new_results = new Collection();

		$temp = $this->results->getAll();
		foreach( $temp as $one_prd ){
			// Si ce n'est pas un produit on ne vas pas plus loin
			if( $one_prd['type_code'] != 'prd' ){
				continue;
			}

			// Contrôle que le produit est bien présent dans l'univers choisi
			if( !array_key_exists($one_prd['tag'], $prds_in_univers) ){
				continue;
			}
			$key_result = $one_prd['type_code'].$one_prd['tag'];
			if( !$new_results->exitsKey($key_result) ){
				$new_results->addItem( $one_prd, $key_result );
			}
		}

		// Mise à jour du résultat
		$this->results = $new_results;

		return $univers_selected;
	}

}