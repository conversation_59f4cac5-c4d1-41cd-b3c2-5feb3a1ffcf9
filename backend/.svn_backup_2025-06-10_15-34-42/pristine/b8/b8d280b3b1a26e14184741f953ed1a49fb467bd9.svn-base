<?php
namespace RGPD;

use RGPD\Trackers\TarteaucitronTrackerInterface;

/** \defgroup Tarteaucitron
 * Ce Module inclue le service Tarteaucitron et les différents type de trackeur d'audiance et de publicité gérer par tarteaucitron.
 * Le service Tarteaucitron permet d'initialisé le JS, tarteaucitron.js, toutes en configurant les trackeurs comme analytics en php en fonction des actions réalisé sur le sites (passage de commande, création de compte, ...).
 * Cella permet de rapidement proposer une gestion de cookie sur les site clients.
 * Pour plus d'information sur tarteaucitron.js voir https://github.com/AmauriC/tarteaucitron.js
 * \ingroup RGPD
 * @{
 */
/**
 * \class Tarteaucitron
 * \brief Tarteaucitron gère le module js de tarteaucitron pour plus d'information sur la configuration voir https://github.com/AmauriC/tarteaucitron.js
 *	Initialisation
 *	\code{.php}
 *		use RGPD\Tarteaucitron;
 *		require_once 'RGPD/autoload.php';
 *		$Tarteaucitron = new Tarteaucitron();
 *	\endcode
 *	Ajout du code dans le <head> de la page
 *	\code{.php}
 *		<head>
 *			...
 *			echo $Tarteaucitron->init();
 *			...
 *		</head>
 *	\endcode
 *	Ajout de trackeur qui implémente TarteaucitronTrackerInterface
 *	\code{.php}
 *		$Tarteaucitron->addTracker(new GA(00-000000-00));
 *	\endcode
 *
 *	Ajout de css de surcharge
 *	\code{.php}
 *		$Tarteaucitron->withOverrideCss('/css/surcharge.css');
 *	\endcode
 */
class Tarteaucitron {

	public $privacyUrl = ""; ///< string Privacy policy url

	public $hashTag = "#tarteaucitron"; ///< string Open the panel with this hashtag

	public $cookieName = "tartaucitron"; ///< string Cookie name

	public $orientation = "top"; ///< string Banner position (top - bottom)

	public $showAlertSmall = true; ///< boolean Show the small banner on bottom right

	public $cookieslist = true; ///< boolean Show the cookie list

	public $adblocker = false; ///< boolean Show a Warning if an adblocker is detected

	public $AcceptAllCta = true; ///< boolean Show the accept all button when highPrivacy on

	public $highPrivacy = false; ///< boolean Disable auto consent

	public $handleBrowserDNTRequest = false; ///< boolean If Do Not Track == 1; disallow all

	public $removeCredit = false; ///< boolean Remove credit link

	public $moreInfoLink = true; ///< boolean Show more info link

	public $useExternalCss = false; ///< boolean If false; the tarteaucitron.css file will be loaded

	public $cookieDomain = ""; ///< string Shared cookie for multisite

	public $readmoreLink = "/cookiespolicy"; ///< string Change the default readmore link

	private $js_public_url = '/js/tarteaucitron/tarteaucitron.js'; ///< string Url pour le js de tarteaucitron

	private $lang = 'fr'; ///< string Langue a utiliser pour les texte de tarteaucitron

	private $override_css_url = null; ///< string Url pour le css de surchargement du style par defaut

	private $custom_text = null; ///< array Tableau des textes de surcharge

	private $trackers = array(); ///< array Liste des trackeur

	/** Cette fonction permet d'initialisation de la configuration de tarteaucitron
	 * pour plus d'information sur les paramètres voir https://github.com/AmauriC/tarteaucitron.js
	 *
	 * \param mixed $privacyUrl Privacy policy url
	 * \param mixed $hashTag Open the panel with this hashtag
	 * \param mixed $cookieName Cookie name
	 * \param mixed $orientation Banner position (top - bottom)
	 * \param mixed $showAlertSmall Show the small banner on bottom right
	 * \param mixed $cookieslist Show the cookie list
	 * \param mixed $adblocker Show a Warning if an adblocker is detected
	 * \param mixed $AcceptAllCta Show the cookie list
	 * \param mixed $highPrivacy Disable auto consent
	 * \param mixed $handleBrowserDNTRequest If Do Not Track == 1; disallow all
	 * \param mixed $removeCredit Remove credit link
	 * \param mixed $moreInfoLink Show more info link
	 * \param mixed $useExternalCss If false; the tarteaucitron.css file will be loaded
	 * \param mixed $cookieDomain Shared cookie for multisite
	 * \param mixed $readmoreLink Change the default readmore link
	 * \return void
	 */
	public function __construct($privacyUrl="", $hashTag="#tarteaucitron", $cookieName="tartaucitron", $orientation="top", $showAlertSmall=true, $cookieslist=true, $adblocker=false, $AcceptAllCta=true, $highPrivacy=false, $handleBrowserDNTRequest=false, $removeCredit=true, $moreInfoLink=true, $useExternalCss=false, $cookieDomain="", $readmoreLink="/cookiespolicy") {
		$this->privacyUrl = $privacyUrl;
		$this->hashTag = $hashTag;
		$this->cookieName = $cookieName;
		$this->orientation = $orientation;
		$this->showAlertSmall = $showAlertSmall;
		$this->cookieslist = $cookieslist;
		$this->adblocker = $adblocker;
		$this->AcceptAllCta = $AcceptAllCta;
		$this->highPrivacy = $highPrivacy;
		$this->handleBrowserDNTRequest = $handleBrowserDNTRequest;
		$this->removeCredit = $removeCredit;
		$this->moreInfoLink = $moreInfoLink;
		$this->useExternalCss = $useExternalCss;
		$this->cookieDomain = $cookieDomain;
		$this->readmoreLink = $readmoreLink;
	}

	/** Cette fonction permet de définir l'url du js de tarteaucitron
	 *
	 * 	\param string $js_url
	 * 	\return Tarteaucitron l'instance
	 */
	public function withJsUrl($js_url) {
		$this->js_public_url = $js_url;
		return $this;
	}

	/** Cette fonction permet de définir le ccs de surcharge de style de tarteaucitron
	 *
	 * \param mixed $css_url L'url du css
	 * \return Tarteaucitron l'instance
	 */
	public function withOverrideCss($css_url) {
		$this->override_css_url = $css_url;
		return $this;
	}

	/** Cette fonction permet définir le tableau de surcharge des textes voir les fichiers tarteaucitron.fr.js par exemple pour avoir les clés.
	 *
	 * \param array $custom_text Tableau des textes
	 * \return Tarteaucitron l'instance
	 */
	public function withCustomText(array $custom_text) {
		$this->custom_text = $custom_text;
		return $this;
	}

	/** Cette fonction retourne la liste des trackeur.
	 * 	\return array Liste des trackeur TarteaucitronTrackerInterface
	 */
	public function getTrackers() {
		return $this->trackers;
	}
	/** Cette fonction permet l'ajoute un trackeur
	 *
	 * \param TarteaucitronTrackerInterface $tracker Instance qui implémente TarteaucitronTrackerInterface
	 * \return Tarteaucitron l'instance
	 */
	public function addTracker(TarteaucitronTrackerInterface $tracker)
	{
		$this->trackers[] = $tracker;

		return $this;
	}
	/** Cette fonction permet de construire un json pour la configuration de tarteaucitron
	 *
	 * \return string
	 */
	public function serializeConfig()
	{
		return json_encode($this);
	}
	/** Cette fonction permet de retourner le script tag qui initialise tarteaucitron
	 *
	 * \return string Retourne le tag ou une chaine vide si pas de trackeur
	 */
	public function init()
	{
		if (empty($this->trackers)) {
			return '';
		}

		$js_script = '
			<script src="'.$this->js_public_url.'"></script>
			<script>
				tarteaucitronForceLanguage = "'.$this->lang.'";
		';

		if (is_array($this->custom_text) && count($this->custom_text)) {
			$js_script .= '
				tarteaucitronCustomText = ' . json_encode($this->custom_text) . ';
			';
		}

		$js_script .= '
			tarteaucitron.init(' . $this->serializeConfig() . ');
		';

		ob_start();
		foreach($this->trackers as $tracker) {
			echo $tracker->renderTarteaucitronCode(false);
		}
		$js_script .= ob_get_clean();

		if ($this->override_css_url !== null) {
			$js_script .= '
				window.addEventListener("load", function () {
					linkElement = document.createElement("link");
					linkElement.rel = "stylesheet";
					linkElement.type = "text/css";
					linkElement.href = "'.htmlspecialchars($this->override_css_url).'";
					document.getElementsByTagName("head")[0].appendChild(linkElement);
				});
			';
		}
		$js_script .= '</script>';

		return $js_script;
	}
}
/// @}