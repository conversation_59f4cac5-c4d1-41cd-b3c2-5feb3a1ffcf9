<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/datastore/v1/entity.proto

namespace Google\Cloud\Datastore\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A partition ID identifies a grouping of entities. The grouping is always
 * by project and namespace, however the namespace ID may be empty.
 * A partition ID contains several dimensions:
 * project ID and namespace ID.
 * Partition dimensions:
 * - May be `""`.
 * - Must be valid UTF-8 bytes.
 * - Must have values that match regex `[A-Za-z\d\.\-_]{1,100}`
 * If the value of any dimension matches regex `__.*__`, the partition is
 * reserved/read-only.
 * A reserved/read-only partition ID is forbidden in certain documented
 * contexts.
 * Foreign partition IDs (in which the project ID does
 * not match the context project ID ) are discouraged.
 * Reads and writes of foreign partition IDs may fail if the project is not in
 * an active state.
 *
 * Generated from protobuf message <code>google.datastore.v1.PartitionId</code>
 */
class PartitionId extends \Google\Protobuf\Internal\Message
{
    /**
     * The ID of the project to which the entities belong.
     *
     * Generated from protobuf field <code>string project_id = 2;</code>
     */
    private $project_id = '';
    /**
     * If not empty, the ID of the namespace to which the entities belong.
     *
     * Generated from protobuf field <code>string namespace_id = 4;</code>
     */
    private $namespace_id = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $project_id
     *           The ID of the project to which the entities belong.
     *     @type string $namespace_id
     *           If not empty, the ID of the namespace to which the entities belong.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Datastore\V1\Entity::initOnce();
        parent::__construct($data);
    }

    /**
     * The ID of the project to which the entities belong.
     *
     * Generated from protobuf field <code>string project_id = 2;</code>
     * @return string
     */
    public function getProjectId()
    {
        return $this->project_id;
    }

    /**
     * The ID of the project to which the entities belong.
     *
     * Generated from protobuf field <code>string project_id = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setProjectId($var)
    {
        GPBUtil::checkString($var, True);
        $this->project_id = $var;

        return $this;
    }

    /**
     * If not empty, the ID of the namespace to which the entities belong.
     *
     * Generated from protobuf field <code>string namespace_id = 4;</code>
     * @return string
     */
    public function getNamespaceId()
    {
        return $this->namespace_id;
    }

    /**
     * If not empty, the ID of the namespace to which the entities belong.
     *
     * Generated from protobuf field <code>string namespace_id = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setNamespaceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->namespace_id = $var;

        return $this;
    }

}

