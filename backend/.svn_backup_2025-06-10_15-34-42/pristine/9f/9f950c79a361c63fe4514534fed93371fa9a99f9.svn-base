<?php

	$results =  array('error'=>'', 'html'=>'');

	// tente de charger le représentant
	if( !isset($_GET['usr']) || !is_numeric($_GET['usr']) || $_GET['usr']<=0 || !( $ruser = gu_users_get($_GET['usr']) ) || !( $usr = ria_mysql_fetch_array($ruser) ) ){
		$results['error'] = _('Impossible de charger le compte client.');
	}else{
		if( $usr['seller_id'] ){
			$rseller = gu_users_get( 0, '', '', PRF_SELLER, '', 0, '', false, false, $usr['seller_id'] );
			if( $rseller && ria_mysql_num_rows($rseller) ){
				$sll_data = ria_mysql_fetch_array($rseller);
				if( $usr['is_sync'] && $sll_data['is_sync'] ){
					$results['error'] = _("Vous ne pouvez pas détacher le représentant de ce compte client, car le lien est synchronisé.");
				}elseif( !gu_users_set_seller_id( $usr['id'], null ) ){
					$results['error'] = _("Une erreur inattendue s'est produite lors de la suppression du lien entre le client et son représentant.")."\n"._("Veuillez réessayer ou prendre contact avec l'administrateur.");
				}else{
					$results['html'] = '<a onclick="return showSelectSeller(this, '.$usr['id'].')">'._('Assigner un représentant').'</a>';
				}
			}
		}
	}

	print json_encode( $results );
    exit;