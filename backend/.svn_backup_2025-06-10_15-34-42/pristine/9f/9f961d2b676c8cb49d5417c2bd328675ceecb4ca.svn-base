<?php
	// MyPDF_Color
	/*
	*/
	
	class MyPDF_Color {
	
		// attributs
		
			private	$_red;
			private	$_green;
			private	$_blue;
		
		// méthodes
		
			// __construct
			/* Constructeur */
			public function __construct($param = array()) {
				$red = (array_key_exists('red', $param)) ? $param['red'] : 0;
				$green = (array_key_exists('green', $param)) ? $param['green'] : 0;
				$blue = (array_key_exists('blue', $param)) ? $param['blue'] : 0;
				
				$this->setRed(array('red' => $red));
				$this->setGreen(array('green' => $green));
				$this->setBlue(array('blue' => $blue));
			}
			
			// getBlue
			/* Renvoie bleu */
			public function getBlue() {
				return $this->_blue;
			}
			
			// getGreen
			/* Renvoie vert */
			public function getGreen() {
				return $this->_green;
			}
			
			// getRed
			/* Renvoie rouge */
			public function getRed() {
				return $this->_red;
			}
			
			// setBlue
			/* Affecte bleu */
			public function setBlue($param) {
				$this->_blue = $param['blue'];
				return $this;
			}
			
			// setGreen
			/* Affecte vert */
			public function setGreen($param) {
				$this->_green = $param['green'];
				return $this;
			}
			
			// setRed
			/* Affecte rouge */
			public function setRed($param) {
				$this->_red = $param['red'];
				return $this;
			}
		
	}

