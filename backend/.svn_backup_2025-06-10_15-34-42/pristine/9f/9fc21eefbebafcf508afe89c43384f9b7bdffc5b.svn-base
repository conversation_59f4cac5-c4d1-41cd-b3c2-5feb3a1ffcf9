<?php

/** \file check-origin-orders.php
 *	\ingroup crontabs
 *
 * 	Ce script permet de récupérer l'origine d'une commande depuis un évènement enregistré sur Analytics
 * 	L'évènement porte l'action "pre-order-save" et fait partie de la catégorie "cart".
 * 	Son libellé contient le numéro de la commande, ex. "ord:000000".
 * 
 * 	Ce script est exécuté tous les jours et traitera les commandes sans origine sur les 3 derniers jours
 * 	permettant ainsi de pallier à un problème de lancement sur une journée.
 * 
 * Ce script s'appuyer sur :
 * 		- la variable de configuration "api_analytics_check" contenant les identifiants des vues sur Analytics
 * 		- l'accès à l'analytics pour cette URL : "<EMAIL>"
 */

set_include_path(dirname(__FILE__) . '/../include/');

require_once('cfg.variables.inc.php');
require_once('orders.inc.php');
require_once('stats.inc.php');

require_once('Google/Client.php');
require_once('Google/Service/Analytics.php');

unset($config);

$tnt_id = isset($argv[1]) && is_numeric($argv[1]) && $argv[1] ? $argv[1] : 0;

$date_start = date('Y-m-d', strtotime('-3 days'));
$date_end = date('Y-m-d', strtotime('-1 days'));
if (isset($argv[2], $argv[3]) && isdate($argv[2]) && isdate($argv[3]) && strtotime($argv[2]) <= strtotime($argv[3])) {
	$date_start = $argv[2];
	$date_end 	= $argv[3];
}

// Charge l'ensemble des configurations clients
$configs = cfg_variables_get_all_tenants($tnt_id);
if (!is_array($configs) || !sizeof($configs)) {
	return false;
}

// Fichier contenant la clé API
$email_ria_api		= '<EMAIL>';
$key_file_location 	= '/var/www/riashop.riastudio.fr/crontabs/p/key_api_riaaccount.p12';

{ // Initialise la connexion à Google Analytics
	$client = new Google_Client();
	$client->setApplicationName("Ria_API_Analytics_Origins");

	$cred = new Google_Auth_AssertionCredentials($email_ria_api, array('https://www.googleapis.com/auth/analytics.readonly'), file_get_contents($key_file_location));

	$client->setAssertionCredentials($cred);
	if ($client->getAuth()->isAccessTokenExpired()) {
		$client->getAuth()->refreshTokenWithAssertion($cred);
	}

	$service = new Google_Service_Analytics($client);
}

// Traitement
foreach ($configs as $config) {
	if (!is_array($config['api_analytics_check']) || !count($config['api_analytics_check'])) {
		continue;
	}

	$params = array(
		'dimensions' => 'ga:eventLabel, ga:source, ga:medium, ga:keyword, ga:adContent, ga:campaign',
		'filters' => 'ga:eventAction==pre-order-save',
		'max-results' => 2500
	);

	$r_order = ord_orders_get_simple(array(), array('start'=>$date_start, 'end'=>$date_end), array('origin'=>-1));
	if (!$r_order || !ria_mysql_num_rows($r_order)) {
		continue;
	}

	$ar_ord_ids = array();
	while ($order = ria_mysql_fetch_assoc($r_order)) {
		$ar_ord_ids[] = $order['id'];
	}

	if (!count($ar_ord_ids)) {
		continue;
	}
	
	foreach ($config['api_analytics_check'] as $gid) {
		try{
			$data = $service->data_ga->get('ga:'.$gid, $date_start, $date_end, "ga:eventValue", $params);
	
			foreach ($data->rows as $key => $data) {
				$ord_id = str_replace('ord:', '', $data[0]);
				if (!in_array($ord_id, $ar_ord_ids)) {
					continue;
				}

				$source = $data[1];
				$name = $data[5];
				$medium = $data[2];
				$content = $data[4];
				$term = $data[3];

				if ($source == 'google' && $medium == 'organic') {
					$name = '(organic)';
				}

				if ($content == '(not set)') {
					$content = '';
				}

				$temp_cfg_wst = $config['wst_id'];
				$ord_wst = ord_orders_get_website($ord_id);
				if (is_numeric($ord_wst) && $ord_wst) {
					$config['wst_id'] = $ord_wst;
				}

				if (stats_origins_exists($ord_id, CLS_ORDER)) {
					stats_origins_update($ord_id, CLS_ORDER, null, $source, $name, $medium, $content, $term, null, null, null, null, null);
				}else{
					stats_origins_add($ord_id, CLS_ORDER, null, $source, $name, $medium, $content, $term, null, null, null, null, null);
				}

				$config['wst_id'] = $temp_cfg_wst;
			}
		}catch(Exception $e){}
	}
}
