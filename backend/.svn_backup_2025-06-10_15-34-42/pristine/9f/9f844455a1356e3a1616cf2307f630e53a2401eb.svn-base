<?php
namespace EventService\Order\Events;

/**
 * \class AddFreePromo
 * Événement lorsqu'un produit offert est ajouté automatiquement au panier par riashop
 */
class AddFreePromo {
	/**
	 * Identifiant de la commande
	 *
	 * @var int $order
	 */
	public $order;
	/**
	 * Identifiant du produit offert
	 *
	 * @var int $prd_id
	 */
	public $prd_id;
	/**
	 * Identifiant de la ligne du produit offert sur la commande
	 *
	 * @var int $line
	 */
	public $line;
	/**
	 * Identifiant du code promo qui ajoute le produit offert
	 *
	 * @var int $cod_id
	 */
	public $cod_id;
	/**
	 * La quantité de produit offert ajouté
	 *
	 * @var int $qty
	 */
	public $qty;
	/**
	 * constructeur de la class
	 *
	 * @param int $order Identifiant de la commande
	 * @param int $prd_id Identifiant du produit offert
	 * @param int $line Ligne du produit offert sur la commande
	 * @param int $cod_id Idendifiant de la promo qui ajoute le produit offert
	 * @param int $qty Quantité de produit offert ajouté
	 */
	public function __construct( $order, $prd_id, $line, $cod_id, $qty ){
		$this->order = $order;
		$this->prd_id = $prd_id;
		$this->line = $line;
		$this->cod_id = $cod_id;
		$this->qty = $qty;
	}
}