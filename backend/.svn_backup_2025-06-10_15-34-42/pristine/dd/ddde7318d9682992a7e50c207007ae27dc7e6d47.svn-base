<?php

	/**	\file fields.php
	 *	Cette page permet l'ajout de champs libres à un modèle de saisie
	 */

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_FIELD_MODEL');

	require_once('fields.inc.php');

	unset( $error );

	// Vérifie le paramètre mdl
	if( isset($_GET['mdl']) ){
		if( $rmodel=fld_models_get($_GET['mdl']) ){
			$model = ria_mysql_fetch_array($rmodel);
			$fields = fld_fields_get( 0, 0, 0, 0, 0, 0, null, array(), false, array(), null, $model['cls_id'], null, false, null, false );
		}else{
			$error = _("Le modèle demandé n'a pas été trouvé.\nVeuillez fermer cette fenêtre.");
		}
	}else{
		$error = _("Le modèle demandé n'a pas été trouvé.\nVeuillez fermer cette fenêtre.");
	} 

	// Ajoute les champs sélectionnés
	$add = false;
	if( isset($_POST['fld'])  ){
		if( !is_array($_POST['fld']) ){
			if( is_numeric($_POST['fld']) ){
				$_POST['fld'] = array( $_POST['fld'] );
			}
		}
		if( is_array($_POST['fld']) ){
			foreach( $_POST['fld'] as $f ){
				$res = fld_models_fields_add( $_GET['mdl'], $f );
				if( $res ){
					$add = true;
				}else{
					$error = _('Une erreur inattendue s\'est produite lors de l\'ajout des champs sélectionnés au modèle de saisie');
				}
			}
		}
	}

	define( 'ADMIN_PAGE_TITLE', _('Choisir un ou plusieurs champs') );
	define( 'ADMIN_HEAD_POPUP', true );
	define( 'ADMIN_ID_BODY', 'popup-content' );
	define( 'ADMIN_NO_MOBILE_STYLE', true );
	
	require_once('admin/skin/header.inc.php');
	
	if( isset($error) ){
		print '
			<div class="error">'.nl2br( htmlspecialchars($error)).'</div>
		';
	}else{
?>

<form action="fields.php?mdl=<?php print $_GET['mdl']; ?>" method="post">
<table id="typing-template-tab" class="checklist">
	<caption><?php echo _("Liste des champs"); ?></caption>
	<thead>
		<tr>
			<th id="select"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
			<th id="name"><?php echo _("Désignation"); ?></th>
		</tr>
	</thead>
	<tbody class="head-second">
	<?php
		// Charge la liste des champs déjà sélectionnés, pour empêcher qu'un champ soit ajouté plusieurs fois au modèle
		$arr_selected = array();
		$selected = fld_models_get_fields($_GET['mdl']);
		while( $s = ria_mysql_fetch_array($selected) ){
			$arr_selected[] = $s['id'];
		}

		// Affichage des champs, avec une rupture par catégorie
		$last_cat = -1;
		while( $f = ria_mysql_fetch_array($fields) ){
			
			// Affichage de la catégorie du champ
			if( $f['cat_id']!=$last_cat ){
				if( $f['cat_id']==0 ){
					print '<tr><th colspan="2">' . _("Autres") . '</th></tr>';
				}else{
					print '<tr><th colspan="2">'.htmlspecialchars($f['cat_name']).'</th></tr>';
				}
				$last_cat = $f['cat_id'];
			}
			
			// Affichage du champ
			print '	<tr>
						<td headers="select"><input type="checkbox" class="checkbox" name="fld[]" id="fld-'.$f['id'].'" value="'.$f['id'].'" '.( array_search($f['id'],$arr_selected)!==false ? 'checked="checked" disabled="disabled"':'' ).' /></td>
						<td headers="name"><label for="fld-'.$f['id'].'">'.htmlspecialchars($f['name']).'</a></td>
					</tr>';
		}
	?>
	</tbody>
	<tfoot>
		<tr><td colspan="2">
			<input type="submit" value="<?php echo _("Ajouter"); ?>" />
			<input type="button" value="<?php echo _("Fermer"); ?>" onclick="parent.closeShadowbox();" />
		</td></tr>
	</tfoot>
</table>
</form>

<?php 
	}

	require_once('admin/skin/footer.inc.php');
?>