<?php

	/** \file refresh-prd-cat-pos.php
	 *	\ingroup crontabs model_products
	 * 	Ce script est destiné à mettre à jour la position des articles dans les niveaux supérieurs de l'arborescence
	 *	Il est lancé chaque jour par une tâche planifiée.
	 *
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}
	
	foreach( $configs as $config ){
		if( isset($config['prd_product_positions_lvl']) && $config['prd_product_positions_lvl'] > 0 ){

			$parent = $config['cat_root']; 
			$final_sort = array(); // cat_id => array(prd_ids)
			$final_sort[$parent] = array();

			recursive_search($final_sort, $parent, 0);

			foreach( $final_sort as $cat => $prds ){
				prd_product_positions_set($config['wst_id'], $cat, $prds);
			}
		}
	}


	/**	Effectue une recherche récursive des produits dans les catégories enfants.
	 * 	Le résultat est aggrégé dans $data et contient
	 * 	- en clé l'identifiant de la catégorie
	 * 	- en valeur un tableau contenant la liste des identifiants de produits contenus dans la catégorie
	 *	@param array $data Obligatoire, données accumulées par la fonction. Passé par référence.
	 *	@param int $parent Obligatoire, identifiant de la catégorie parent.
	 *	@param int $lvl Obligatoire, niveau de profondeur. Au dela de 10, une erreur fatale sera générée.
	 */
	function recursive_search(&$data, $parent, $lvl){
		global $config;

		if( $lvl > 10 ) die('boucle recursive');

		// récupère les catégories du niveau en cours de consultation 
		$rcat = prd_categories_get(0, false, $parent);
		if( $rcat ){
			while( $cat = ria_mysql_fetch_assoc($rcat) ){

				$data[$cat['id']] = array();

				$cat_childs =  $lvl == $config['prd_product_positions_lvl']; 

				// récupère les produits classés dans cette catégorie
				$rprd = prd_products_get_simple(0, '', false, $cat['id'], $cat_childs);
				if( $rprd ){
					while( $prd = ria_mysql_fetch_assoc($rprd) ){
						$data[$cat['id']][] = $prd['id'];
					}
				}

				// récursive pour l'arborescence descendante
				if( $lvl < $config['prd_product_positions_lvl'] ){
					recursive_search($data, $cat['id'], $lvl+1);
				}

				// ajoute le résultat des sous éléments à la catégorie en cours 
				$data[$parent] = array_merge($data[$parent],$data[$cat['id']]);
			}
		}
	}