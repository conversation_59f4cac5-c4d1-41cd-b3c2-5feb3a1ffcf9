<?php
	require_once('rewrite.inc.php');
	
	header("Content-Type: application/xml");
	$error = false;
	$xml = '<?xml version="1.0" encoding="utf-8"?>';
	
	$lng = isset($_POST['lng']) && in_array($_POST['lng'], $config['i18n_lng_used']) ? $_POST['lng'] : $config['i18n_lng'];
		
	// Enregistre les modifications sur les redirections
	if( isset($_POST['save-maj-url'], $_POST['url_id']) && $_POST['url_id']>0 ){
		$id = $_POST['url_id'];
		if( substr($_POST['url-'.$id], 0, 1)!='/' ){
				$xml .= '<result type="0"><error>';
				$xml .= '<![CDATA['._('Une erreur est survenue lors de la mise à jour d\'une url de redirection.').'<br />'._('Le premier caractère ne peut être qu\'un \'/\'.').']]>';
				$xml .= '</error>\n</result>';
				$error = true;
		} elseif( $_POST['url-'.$id]!=$_POST['url_old-'.$id] && rew_rewritemap_exists($_POST['url-'.$id], '', $lng) ){ // On vérifie que la nouvelle url n'est pas déjà utilisée
			$xml .= '<result type="0"><error>';
			$xml .= '<![CDATA['._('Une erreur est survenue lors de la mise à jour d\'une url de redirection.').'<br />'._('L\'url saisie est déjà utilisée.').']]>';
			$xml .= '</error>\n</result>';
			$error = true;
		} elseif( trim($_POST['url-'.$id]=='' || $_POST['url-'.$id]=='/') ){
			$xml .= '<result type="0"><error>';
			$xml .= '<![CDATA['._('Une erreur est survenue lors de la mise à jour d\'une url de redirection.').'<br />'._('La nouvelle URL ne doit pas être vide ni contenir un seul \'/\'.').']]>';
			$xml .= '</error>\n</result>';
			$error = true;
		} else {
			if( !rew_rewritemap_extern_update($_POST['url_old-'.$id], $_POST['url-'.$id], $lng) ){
				$xml .= '<result type="0"><error>';
				$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la mise à jour de l\'url de redirection.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
				$xml .= '</error>\n</result>';
				$error = true;
			}
		}
		
		if( !$error ){
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}
	}
	
	// Ajout d'une URL de redirection
	if( isset($_POST['add-url'], $_POST['url-0']) && trim($_POST['url-0'])!='' ){
		if( substr($_POST['url-0'], 0, 1)!='/' ){
				$xml .= '<result type="0"><error>';
				$xml .= '<![CDATA['._('Le premier caractère de la nouvelle redirection ne peut être qu\'un \'/\'.').']]>';
				$xml .= '</error>\n</result>';
				$error = true;
		} elseif( rew_rewritemap_exists($_POST['url-0'], '', $lng) ){ // On vérifie que la nouvelle url n'est pas déjà utilisée
				$xml .= '<result type="0"><error>';
				$xml .= '<![CDATA['._('Une erreur est survenue lors de la mise à jour d\'une url de redirection.').'<br />'._('La nouvelle url est déjà utilisée.').']]>';
				$xml .= '</error>\n</result>';
				$error = true;
		} else if( !rew_rewritemap_redirection_add($_POST['url-0'], $_POST['urlcat'], 0, $lng, CLS_CATEGORY, $_POST['cat']) ){	
			$xml .= '<result type="0"><error>';
			$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de l\'ajout de l\'url de redirection.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
			$xml .= '</error>\n</result>';
			$error = true;
		}
		
		if( !$error ){
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}
	}

	if( isset($_POST['del'], $_POST['url']) ){
		if( !rew_rewritemap_redirection_del($_POST['url']) ){
			$xml .= '<result type="0"><error>';
			$xml .= '<![CDATA['._('Une erreur inattendue s\'est produite lors de la suppression de l\'url de redirection.').'<br />'._('Veuillez réessayer ou prendre contact avec l\'administrateur.').']]>';
			$xml .= '</error>\n</result>';
			$error = true;
		} else {
			$xml .= '<result type="1">';
			$xml .= '<cdt-line><![CDATA[';
			$xml .= ']]></cdt-line>';
			$xml .= '</result>';
		}
	}

	print $xml;

