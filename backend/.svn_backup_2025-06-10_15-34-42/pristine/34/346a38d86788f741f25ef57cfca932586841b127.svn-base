<?php

require_once('db.inc.php');
require_once('rewrite.inc.php');
require_once('strings.inc.php');
require_once('news.categories.inc.php');
require_once('websites.inc.php');
require_once('ria.queue.inc.php');

/// \addtogroup tools
/// @{

/// \defgroup tools_news Actualités
///	Ce module comprend les fonctions nécessaires à la gestion des actualités.
/// @{

// \cond onlyria
/**	Permet l'ajout d'une actualité. Cette actualité sera publiée dès que la date publish_date sera atteinte.
 *	@param string $name Obligatoire, Titre de l'actualité
 *	@param string $intro Obligatoire, Introduction de l'actualité
 *	@param string $desc Obligatoire, Contenu de l'actualité
 *	@param $publish_date Obligatoire, Date de publication de l'actualité, au format jj/mm/aaaa ou jj/mm/aaaa hh:mm:ss
 *  @param int $cat Facultatif, Identifiant de la catégorie de l'actualité à ajouter
 *	@param string $comment Facultatif, détermine si l'actualité autorise les commentaires (non par défaut)
 *	@param $publish_date_end Optionnel, date de fin de publication de l'actualité au format jj/mm/aaaa ou jj/mm/aaaa hh:mm:ss
 *	@return int l'identifiant de l'actualité en cas de succès, false en cas d'échec
 */
function news_add( $name, $intro, $desc, $publish_date, $cat=0, $comment=0, $publish_date_end='' ){
	global $config;

	if( !is_numeric($comment) ) return false;
	if( !trim($name) ) return false;
	if( !trim($desc) ) return false;

	if( trim($publish_date) != '' ){
		if( !isdateheure($publish_date) ){
			return false;
		}

		if( isdate($publish_date) ){
			$publish_date = '"'.dateparse( $publish_date ).' 00:00:00'.'"';
		}else{
			$publish_date = '"'.dateheureparse( $publish_date ).'"';
		}
	}else{
		$publish_date = 'null';
	}

	if( trim($publish_date_end) != '' ){
		if( !isdateheure($publish_date_end) ){
			return false;
		}

		if( isdate($publish_date_end) ){
			$publish_date_end = '"'.dateparse( $publish_date_end ).' 00:00:00'.'"';
		}else{
			$publish_date_end = '"'.dateheureparse( $publish_date_end ).'"';
		}
	}else{
		$publish_date_end = 'null';
	}

	$name = ucfirst(trim($name));
	$intro = ucfirst(trim($intro));
	$desc = ucfirst(trim($desc));
	if( !news_categories_exists($cat) ){
		$cat = 'null';
	}

	$res = ria_mysql_query("
		insert into news
			( news_tnt_id, news_name, news_intro, news_desc, news_publish_date, news_publish_date_end, news_cat_id, news_allow_comment )
		values
			( ".$config['tnt_id'].", '".addslashes($name)."','".addslashes($intro)."','".addslashes($desc)."',".$publish_date.",".$publish_date_end.",".$cat.", ".$comment." );
	");

	if( $res ){
		$id = ria_mysql_insert_id();

		// On met à jour le nombre d'utilisation des images
		img_images_update_from_riawysiwyg( $desc );

		// Récupère les sites
		$rwst = wst_websites_get();
		if( !$rwst || !ria_mysql_num_rows($rwst) ){
			return false;
		}

		// Crée les alias
		$alias = rew_rewritemap_generated( array($id), CLS_NEWS );
		while( $wst = ria_mysql_fetch_array($rwst) ){
			$prd_pages = cfg_urls_get( $wst['id'], CLS_NEWS);
			if( $prd_pages ){
				$first = true;
				while( $page = ria_mysql_fetch_array($prd_pages) ){
					if( $first ){
						$url = rew_rewritemap_add_specify_class( CLS_NEWS, $alias.$page['key'], $page['url'].'?n='.$id, 200, $wst['id'], false, null, $id );
					}else{
						rew_rewritemap_add_specify_class( CLS_NEWS, $alias.$page['key'], $page['url'].'?n='.$id, 200, $wst['id'], false, null, $id );
					}
					$first = false;
				}
			}
		}

		ria_mysql_query('
			update news
			set news_url_alias=\''.$url.'\'
			where news_tnt_id='.$config['tnt_id'].'
				and news_id='.$id
		);

		try{
			// Index l'actualité dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_NEWS,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}

		return $id;
	}else{
		return false;
	}
}
// \endcond

// \cond onlyria
/**	Permet la mise à jour d'une actualitée. Cette actualité sera publiée dès que la date publish_date sera atteinte.
 *	@param int $id Obligatoire, Identifiant de l'actualité à mettre à jour
 *	@param string $name Obligatoire, Titre de l'actualité
 *	@param string $intro Obligatoire, Introduction de l'actualité
 *	@param string $desc Obligatoire, Contenu de l'actualité
 *	@param $publish_date Obligatoire, Date de publication de l'actualité, au format jj/mm/aaaa
 *  @param int $cat Facultatif, Identifiant de la catégorie de l'actualité à mettre à jour
 *	@param string $comment Facultatif, détermine si l'actualité autorise les commentaires
 *	@param $publish_date_end Optionnel, date de fin de publication de l'actualité au format jj/mm/aaaa ou jj/mm/aaaa hh:mm:ss
 *
 *	@return bool true en cas de succès, false en cas d'échec
 */
function news_update( $id, $name, $intro, $desc, $publish_date, $cat=0, $comment=false, $publish_date_end='' ){
	global $config;

	if( $comment!=false && !is_numeric($comment) ) return false;
	if( !news_exists($id) ) return false;
	if( !trim($name) ) return false;
	if( !trim($desc) ) return false;

	if( trim($publish_date) != '' ){
		if( !isdateheure($publish_date) ){
			return false;
		}

		if( isdate($publish_date) ){
			$publish_date = '"'.dateparse( $publish_date ).' 00:00:00'.'"';
		}else{
			$publish_date = '"'.dateheureparse( $publish_date ).'"';
		}
	}else{
		$publish_date = 'null';
	}

	if( trim($publish_date_end) != '' ){
		if( !isdateheure($publish_date_end) ){
			return false;
		}

		if( isdate($publish_date_end) ){
			$publish_date_end = '"'.dateparse( $publish_date_end ).' 00:00:00'.'"';
		}else{
			$publish_date_end = '"'.dateheureparse( $publish_date_end ).'"';
		}
	}else{
		$publish_date_end = 'null';
	}

	$name = ucfirst(trim($name));
	$intro = ucfirst(trim($intro));
	$desc = ucfirst(trim($desc));
	if( !news_categories_exists($cat) ){
		$cat = 'null';
	}

	$old_desc = '';
	$r = ria_mysql_query( 'select news_desc from news where news_tnt_id='.$config['tnt_id'].' and news_id='.$id );
	if( $r && ria_mysql_num_rows($r) ){
		$old_desc = ria_mysql_result( $r, 0, 'news_desc' );
	}

	$sql = "
		update news
		set news_name='".addslashes($name)."',
			news_intro='".addslashes($intro)."',
			news_desc='".addslashes($desc)."',
			news_publish_date=".$publish_date.",
			news_publish_date_end=".$publish_date_end.",
			news_cat_id=".$cat."
			".( is_numeric($comment) ? ", news_allow_comment=".$comment : "" )."
		where news_tnt_id=".$config['tnt_id']."
			and news_id=".$id."
	";

	$res = ria_mysql_query($sql);
	if( $res ){
		// On met à jour le nombre d'utilisation des images
		img_images_update_from_riawysiwyg( $old_desc.' '.$desc );

		// Récupère les sites
		$rwst = wst_websites_get();
		if( !$rwst || !ria_mysql_num_rows($rwst) )
			return false;

		$alias = rew_rewritemap_generated( array($id), CLS_NEWS );
		while( $wst = ria_mysql_fetch_array($rwst) ){
			$prd_pages = cfg_urls_get( $wst['id'], CLS_NEWS);
			if( $prd_pages ){
				$first = true;
				while( $page = ria_mysql_fetch_array($prd_pages) ){
					if( $first )
						$url = rew_rewritemap_add_specify_class( CLS_NEWS, $alias.$page['key'], $page['url'].'?n='.$id, 200, $wst['id'], false, null, $id );
					else
						rew_rewritemap_add_specify_class( CLS_NEWS, $alias.$page['key'], $page['url'].'?n='.$id, 200, $wst['id'], false, null, $id );
					$first = false;
				}
			}
		}

		ria_mysql_query('
			update news
			set news_url_alias=\''.$url.'\'
			where news_tnt_id='.$config['tnt_id'].'
			and news_id='.$id
		);

		try{
			// Index l'actualité dans le moteur de recherche.
			RiaQueue::getInstance()->addJob(RiaQueue::WORKER_SEARCH_LOW, array(
				'cls_id' => CLS_NEWS,
				'obj_id_0' => $id,
			));
		}catch(Exception $e){
			error_log(__FILE__.':'.__LINE__.' '.$e->getMessage());
		}
	}

	return $res;
}
// \endcond

// \cond onlyria
/** Cette fonction permet d'indexer une actualité
 *	@param int $id Obligatoire, identifiant d'une actualité
 *	@param string $lng Optionnel, code ISO 639-1 de la langue, par défaut, on prends celle du site
 *	@return bool Retourne true si tout s'est correctement déroulé
 *	@return bool Retourne false si le paramètre $id est omis ou bien si l'actualité n'existe pas
 */
function news_index_add( $id, $lng=false ){
	if( !news_exists($id) ) return false;
	global $config;

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? $lng : $config['i18n_lng'];

	$rnews = news_get( $id, false, null, 0, 0, false, false );

	if( !$rnews || !ria_mysql_num_rows($rnews) )
		return false;

	$news = ria_mysql_fetch_array( $rnews );
	if( strtolower($lng)!=strtolower($config['i18n_lng']) ){
		$tsk_news = fld_translates_get( CLS_NEWS, $news['id'], $lng, $news, array(_FLD_NEWS_NAME=>'name', _FLD_NEWS_INTRO=>'intro', _FLD_NEWS_DESC=>'desc', _FLD_NEWS_URL=>'url_alias') );
		$news['name'] = $tsk_news['name'];
		$news['intro'] = $tsk_news['intro'];
		$news['desc'] = $tsk_news['desc'];
		$news['url_alias'] = $tsk_news['url_alias'];
	}

	$cid = search_index_content( $news['url_alias'], 'news', $news['name'], $news['intro'], $news['desc'], '/admin/tools/news/edit.php?news='.$id, $news['published'], $id, false, '', '', $lng );
	if( strtolower($lng)==strtolower($config['i18n_lng']) && $cid )
		ria_mysql_query('update news set news_cnt_id='.$cid.' where news_id='.$id.' and news_tnt_id='.$config['tnt_id']);

	return $cid;
}
// \endcond

// \cond onlyria
/**	Cette fonction va reconstruire l'index du moteur de recherche pour toutes les actualités.
 *	@param string $lng Optionnel, si le paramètre est précisé, alors les actualités seront réindéxées seulement dans cette langue
 */
function news_index_rebuild( $lng=false ){
	global $config;

	// Reconstruit l'index
	$news = ria_mysql_query('
		select news_id as id
		from news
		where news_tnt_id='.$config['tnt_id'].'
	');

	$lng = $lng && in_array(strtolower($lng), $config['i18n_lng_used']) ? array($lng) : $config['i18n_lng_used'];

	foreach( $lng as $l ){
		ria_mysql_data_seek( $news, 0 );
		while( $n = ria_mysql_fetch_array($news) ){
			news_index_add( $n['id'], strtolower($l) );
		}
	}

	return true;

}
// \endcond

// \cond onlyria
/**	Cette fonction permet la suppression d'une actualité. Si l'actualité à déjà été supprimée,
 *	cette fonction ne génère pas d'erreur.
 *
 *	@param int $id Identifiant de la news que l'on souhaite supprimer
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function news_del( $id ){
	global $config;

	if( !is_numeric($id) ) return false;

	// Suppression des urls
	rew_rewritemap_del_multilingue( _FLD_NEWS_URL, array($id) );
	$url = news_get_url( $id );
	if( trim($url)!='' )
		rew_rewritemap_del( $url );

	$imgs = news_images_get($id);

	while( $img = ria_mysql_fetch_array($imgs) ){
		news_images_del($img['id'], $id);
	}
	if( $res = ria_mysql_fetch_array(news_get($id, false, null, 0, 0, false, false)) )
		search_index_clean('news',$res['cnt_id']);
	return ria_mysql_query('delete from news where news_id='.$id .' and news_tnt_id='.$config[ 'tnt_id' ]);
}
// \endcond

/** Cette fonction permet le chargement d'une ou plusieurs actualités.
 *	Les actualités sont retournées triées par date de publication descendante.
 *
 *	@param int $id Optionnel, permet de filtrer le résultat sur un identifiant donné (ou tableau d'identifiants)
 *	@param bool $published  Optionnel, indique si toutes les actualités doivent être retournées, ou seulement celles actuellement publiées
 *	@param $archived Optionnel, permet d'obtenir soit les actualités en cours, soit les archives
 *  @param int $cat Optionnel, Identifiant de la catégorie de l'actualité à charger (-1 pour charger les actualités sans catégorie)
 *	@param int $wst Optionnel, identifiant d'un site du locataire
 *	@param string $lng Optionnel, code ISO d'une langue spécifique
 *	@param bool $check_segments Optionnel, si activé verifie que l'actualité doit être affichée pour le compte connecté.
 *	@param int|array $store Deprecated, identifiant ou tableau d'identifiants de magasins
 *	@param $cat_recursive Optionnel, détermine si le paramètre $cat, quand il est spécifié, doit entrainer une recherche sur les catégories enfants également.
 *	@param $sort Optionnel, par défaut les actualités sont retournés par date de publication décroissante, permet de personnalisé le tri, valeurs accepté : publish_date
 *	@param array $array_fld Optionnel, paramètre permettant de récupérer des actualités selon un ou plusieurs champs avancés (englobe habituellement : $fld, $fld_or et $or_between_val)
 *	@param $exclude Optionnel, identifiant ou tableau d'identifiants d'actualités à exclure
 *	@param string $date_start Optionnel, date de début de la période
 *	@param string $date_end Optionnel, date de fin de la période
 *	@param int $offset Optionnel, numéro de ligne de départ
 *	@param bool|int	$limit	Optionnel, permet de limiter le résultat, false pour ne pas limiter le résultat
 *
 *	@return resource Un résultat de requête MySQL comprenant les colonnes suivantes :
 *		- id : Identifiant de l'actualité
 *		- name : Titre de l'actualité
 *		- intro : Introduction de l'actualité
 *		- desc : Contenu de l'actualité
 *		- publish_date : Date de publication de l'actualité
 * 		- publish_date_en : date de publication, au format anglais
 *		- publish_date_end : Date de fin publication de l'actualité
 * 		- publish_date_end_en : date de fin publication, au format anglais
 *		- hour_from : heure de début de publication
 *		- hour_to : heure de fin de publication
 *		- url_alias : adresse simplifiée de cette actualité
 *		- cnt_id : identifiant d'indexation
 *		- archived : détermine si l'actualité est archivée
 *		- published : Détermine si l'actualité est actuellement publiée
 *		- cat_id : Identifiant de la catégorie d'actualité
 *		- img_id : Image rattachée à l'actualité
 *		- old_img : ancien identifiant de l'image rattachée à l'actualité
 *		- allow_comment : Détermine si l'actualité accepte les commentaires
 *		- keywords : Mots-clés rattachés au CMS
 *		- tag_title : Balise "title" pôur le référencement
 *		- tag_desc : Balise "description" pôur le référencement
 *		- pos : position dans le classement
 *
 *	@todo Il faudrait que le paramètre $wst soit renseigné automatiquement losque l'appel de la fonction se fait depuis un frontoffice. Actuellement impossible car les interfaces d'admin hérite du fichier config de leur site public.
 *
 */
function news_get( $id=0, $published=false, $archived=null, $cat=0, $wst=0, $lng=false, $check_segments=true, $store=false, $cat_recursive=false, $sort=false, $array_fld=false, $exclude=false, $date_start=false, $date_end=false, $offset=0, $limit=false ){
	global $config;

	if( $store !== false ){
		mail('<EMAIL>', 'function news_get() : utilisation du paramètre $store', 'config : '.$config['tnt_id'].' - '.$config['wst_id'] );
	}

	if( is_array($id) ){
		foreach( $id as $one_id ){
			if( !is_numeric($one_id) || $one_id<=0 ){
				return false;
			}
		}
	}elseif( is_numeric($id) && $id>0 ){
		$id = array($id);
	}else{
		$id = array();
	}

	if( $store ){
		if( is_numeric( $store ) ){
			$store = array( $store );
		}else if( is_array( $store ) ){
			foreach( $store as $key => $str_id ){
				if( !is_numeric( $str_id ) || $str_id <= 0 ){
					unset( $store[$key] );
				}
			}
		}
	}

	if( $array_fld !== false ){
		if( !is_array($array_fld) ){
			return false;
		}

		if( !ria_array_key_exists(array( 'fld' ), $array_fld) ){
			return false;
		}

		if( !array_key_exists('or_between_val', $array_fld) ){
			$array_fld['or_between_val'] = false;
		}

		if( !array_key_exists('or_between_fld', $array_fld) ){
			$array_fld['or_between_fld'] = false;
		}

		if( !array_key_exists('lng', $array_fld) ){
			$array_fld['lng'] = false;
		}else{
			$array_fld['lng'] = strtolower2( $array_fld['lng'] );

			if( !in_array($array_fld['lng'], $config['i18n_lng_code']) ){
				return false;
			}
		}
	}

	if( $exclude !== false ){
		$exclude = control_array_integer( $exclude, false );
		if( $exclude === false ){
			return false;
		}
	}

	$lng = $lng!=false && in_array(strtolower($lng),$config['i18n_lng_used']) ? strtolower($lng) : false;

	if( !is_numeric($cat) || $cat<-1 ) return false;
	if( !is_numeric($wst) || $wst<0 ) return false;
	$sql = '
		select news_id as id, news_name as name, news_intro as intro, news_desc as "desc", news_url_alias as url_alias,
			date_format(news_publish_date,"%d/%m/%Y") as publish_date, news_publish_date as publish_date_en, news_cnt_id as cnt_id,
			date_format(news_publish_date_end,"%d/%m/%Y") as publish_date_end, news_publish_date_end as publish_date_end_en,
			time_format(news_publish_date,"%H:%i") as "hour_from", time_format(news_publish_date_end,"%H:%i") as "hour_to",
			(
				(news_publish_date is not null and news_publish_date <= now() )
				and (news_publish_date_end is null or news_publish_date_end > now() )
			) as published, news_archived as archived, news_cat_id as cat_id, ifnull(news_img_id,(select ni_img_id from news_img where ni_news_id=news_id and ni_tnt_id ='.$config['tnt_id'].' order by ni_pos limit 0, 1)) as img_id, news_img_id as old_img, news_allow_comment as allow_comment, news_keywords as keywords, news_tag_title as tag_title, news_tag_desc as tag_desc,
			news_pos as pos
		from news';
	if( $wst > 0 )
		$sql .= ' join news_websites ns on ( news_id=nw_news_id and nw_wst_id='.$wst.( $lng!=false ? ' and nw_lng_code=\''.$lng.'\'' : '' ).' and nw_tnt_id='.$config['tnt_id'].' )';
	$sql .= ' where 1';
	if( sizeof($id) ) $sql .= ' and news_id in ('.implode(', ', $id).')';

	if( $published ){
		$sql .= '
			and (news_publish_date is not null and news_publish_date <= now() )
			and (news_publish_date_end is null or news_publish_date_end > now() )
		';
	}

	if( $archived!==null ){
		if( $archived ){
			$sql .= ' and news_archived!=0';
		}else{
			$sql .= ' and news_archived=0';
		}
	}
	if( $store && sizeof($store) > 0 ){
		$sql .= ' and exists( select 1 from news_stores where ns_tnt_id = '.$config['tnt_id'].' and ns_news_id = news_id and ns_str_id in ('.implode(',', $store).') )';
	}
	if( $cat > 0 ){
		if( $cat_recursive ){
			$sql .= '
				and (
					news_cat_id = '.$cat.' or
					news_cat_id in (
						select cah_child_id from news_cat_hierarchy
						where cah_tnt_id = '.$config['tnt_id'].' and cah_parent_id = '.$cat.'
					)
				)
			';
		}else{
			$sql .= ' and news_cat_id='.$cat;
		}
	}elseif( $cat === -1 ){
		$sql .= ' and news_cat_id is null';
	}
	if( isset($config[ 'tnt_id' ]) ){
		$sql .=  ' and news_tnt_id='.$config[ 'tnt_id' ];
	}

	if( $date_start !== false ){
		if( isdate($date_start) ){
			$sql .= ' and date(news_publish_date) >= "'.dateparse($date_start).'"';
		}else{
			$sql .= ' and news_publish_date >= "'.dateheureparse($date_start).'"';
		}
	}

	if( $date_end !== false ){
		if( isdate($date_end) ){
			$sql .= ' and date(news_publish_date) <= "'.dateparse($date_end).'"';
		}else{
			$sql .= ' and news_publish_date <= "'.dateheureparse($date_end).'"';
		}
	}

	$sql .= fld_classes_sql_get( CLS_NEWS, $array_fld['fld'], $array_fld['or_between_val'], $array_fld['or_between_fld'], $array_fld['lng'] );



	if( is_array($exclude) && sizeof($exclude) ){
		$sql .= ' and news_id not in ('.implode( ', ', $exclude ).')';
	}
	if( sizeof($id)==1 )
		$sql .= ' limit 0,1';
	else{
		$sort_final = array();
		if( $sort==false || !is_array($sort) || sizeof($sort)==0 ){
			$sort = array();
		}
		foreach( $sort as $col=>$dir ){
			$col = strtolower(trim($col));
			$dir = strtolower(trim($dir))=='desc' ? 'desc' : 'asc';

			if( substr($col, 0, 4)=='fld-' ){
				$fld_id = substr( $col, 4, strlen($col)-4 );
				if( is_numeric($fld_id) && $fld_id>0 ){
					$type_fld = fld_fields_get_type( $fld_id );
					$numeric_types = array( FLD_TYPE_INT, FLD_TYPE_FLOAT, FLD_TYPE_REFERENCES_ID );
					$sub_sql = '
						(
							select
								'.( in_array( $type_fld, $numeric_types ) ? 'cast(pv_value as decimal)' : 'pv_value' ).'
							from
								fld_object_values
							where
								pv_tnt_id='.$config['tnt_id'].' and pv_lng_code=\''.$config['i18n_lng'].'\' and pv_obj_id_0=news_id and pv_fld_id='.$fld_id.'
						)
					';
					if( $type_fld == FLD_TYPE_DATE ){
						$sub_sql = 'STR_TO_DATE('.$sub_sql.', "%d/%m/%Y") ' .$dir;
					}else{
						$sub_sql .= ' ' . $dir;
					}
					array_push( $sort_final, $sub_sql );
				}
			}else{
				switch( $col ){
					case 'publish_date' :
						array_push( $sort_final, 'news_publish_date '.$dir );
						break;
					case 'random' :
						array_push( $sort_final, 'rand()' );
						break;
					case 'id' :
						array_push( $sort_final, 'news_id '.$dir );
						break;
				}
			}
		}

		if( sizeof($sort_final)==0 ){
			array_push( $sort_final, 'ifnull(news_pos, 18446744073709551615) asc, news_publish_date desc' );
		}

		$sql .= ' order by '.implode( ', ', $sort_final );

		if( is_numeric($offset) && $offset > 0 ){
			$sql .= ' offset '.$offset;
		}

		if( is_numeric($limit) && $limit > 0 ){
			$sql .= ' limit '.$limit;
		}
	}

	$r = ria_mysql_query($sql);
	if( !$r && ria_mysql_errno() )
		error_log( __FILE__.':'.__LINE__.' '.mysql_error().' '.$sql );

	if( $r && ria_mysql_num_rows($r) && $check_segments ){
		$id_ar = array();

		$usr_id = 0; // non connecté
		if( isset($_SESSION['admin_view_user']) && is_numeric($_SESSION['admin_view_user']) && $_SESSION['admin_view_user'] )
			$usr_id = $_SESSION['admin_view_user'];
		elseif( isset($_SESSION['usr_id']) && is_numeric($_SESSION['usr_id']) && $_SESSION['usr_id'] )
			$usr_id = $_SESSION['usr_id'];

		while( $n = ria_mysql_fetch_array($r) ){
			if( seg_objects_check_segment( CLS_NEWS, $n['id'], CLS_USER, $usr_id ) ){
				$id_ar[] = $n['id'];
			}
		}

		if( !sizeof($id_ar) ){
			return false;
		}

		return news_get( $id_ar, false, null, 0, 0, $lng, false, false, false, $sort );
	}

	return $r;
}

// \cond onlyria
/** Cette fonction permet de récupérer l'url d'une actualité
 *	@param int $id Obligatoire, identifiant d'une actualité
 *	@return string L'URL de l'actualité en cas de succès, False en cas d'échec
 */
function news_get_url( $id ){
	if( !news_exists($id) )return false;
	global $config;

	$res = ria_mysql_query( 'select news_url_alias as alias from news where news_tnt_id='.$config['tnt_id'].' and news_id='.$id );

	if( !$res || !ria_mysql_num_rows($res) )
		return false;

	return ria_mysql_result( $res, 0, 'alias' );
}
// \endcond

/** Cette fonction permet de récupérer l'identifiant de la catégorie laquelle l'actualité apartient
 *	@param int $news_id Obligatoire, identifiant d'une actualité
 *	@return int L'identifiant de la catégorie, ou 0 si l'actualité n'apartient à aucune catégorie
 *	@return bool False si le paramètre est omis ou faux
 */
function news_get_category( $news_id ){
	if( !is_numeric($news_id) || $news_id <= 0 ){
		return false;
	}

	global $config;

	$res = ria_mysql_query('
		select ifnull(news_cat_id, 0) as cat_id
		from news
		where news_tnt_id='.$config['tnt_id'].'
			and news_id='.$news_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['cat_id'];
}

/**	Cette fonction permet de récupérer les images d'une actualités.
 *	@param int $news_id Optionnel, Identifiant de l'actualité pour laquelle on veut les images.
 *	@param int $img_id Optionnel, Identifiant d'une image
 *	@return bool False en cas d'échec
 *	@return resource Un résultat de requête MySQL comprenant les colonnes :
 *		- news_id : identifiant de l'actualité
 *		- id : identifiant de l'image
 *		- ni_pos : position relative de l'image
 */
function news_images_get( $news_id=0, $img_id=0 ){
	global $config;

	$sql = 'select ni_img_id as id, news_id, ni_pos
			from news, news_img
			where news_tnt_id = ni_tnt_id
	';
	if( is_numeric($news_id) && $news_id>0 )
		$sql .= ' and news_id = '.$news_id;
	if( is_numeric($img_id) && $img_id>0 )
		$sql .= ' and ni_img_id = '.$img_id;
	$sql .= '
				and ni_news_id = news_id
				and ni_tnt_id = '.$config['tnt_id'].'
			order by ni_pos asc';

	return ria_mysql_query($sql);
}

// \cond onlyria
/**	Cette fonction permet la suppression d'une image relié à une actualité
 *	@param int $id Identifiant de l'image à supprimer
 *	@param int $news Identifiant de l'actualité pour laquelle on veut les images.
 *	@return resource la liste des images attachés à l'actualité
 *	@return bool false en cas d'échec
 */
function news_images_del( $id, $news ){
	if (!is_numeric($id) || $id <= 0) {
		return false;
	}
	global $config;

	$sql = 'delete from news_img where ni_tnt_id='.$config['tnt_id'].' and ni_news_id='.$news.' and ni_img_id='.$id;

	if( img_images_exists( $id ) ){
		img_images_count_update( $id );
	}
	search_contents_image_del_from_tag(CLS_NEWS, $news, $id);

	return ria_mysql_query($sql);
}
// \endcond

/**	Cette fonction permet de déterminer si une actualité existe dans la base de données.
 *	@param int $id Identifiant de l'actualité à tester.
 *	@return bool true si l'actualité existe, false si elle n'existe pas.
 */
function news_exists( $id ){
	global $config;

	$sql = 'select news_id from news where news_id='.$id;
	if(isset($config[ 'tnt_id' ]))
		$sql .=  ' and news_tnt_id='.$config[ 'tnt_id' ];
	$sql .= ' limit 0,1';
	return ria_mysql_num_rows(ria_mysql_query($sql));
}

// \cond onlyria
/** Cette fonction permet l'archivage d'une actualité. Une fois archivée, l'actualité n'est plus publiée sur le site,
 *	même si sa date de publication est antérieure à la date du jour.
 *	@param int $id Identifiant de l'actualité à publier.
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function news_archive( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$sql = 'update news set news_archived=1 where news_id='.$id;
	if(isset($config[ 'tnt_id' ]))
		$sql .=  ' and news_tnt_id='.$config[ 'tnt_id' ];
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de ressortir une actualité des archives.
 *	@param int $id Identifiant de l'actualité à publier.
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function news_unarchive( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ) return false;
	$sql = 'update news set news_archived=0 where news_id='.$id;
	if(isset($config[ 'tnt_id' ]))
		$sql .=  ' and news_tnt_id='.$config[ 'tnt_id' ];
	return ria_mysql_query($sql);
}
// \endcond

// \cond onlyria
/** Permet l'association d'une image à une actualité.
 *
 *	@param int $news Identifiant de l'actualité.
 *	@param string $fieldname Nom du champ de formulaire contenant l'image à uploader.
 *
 *	@return int L'identifiant attribué à l'image.
 *	@return bool false en cas d'erreur
 *
 */
function news_image_upload( $news, $fieldname ){
	if( !is_uploaded_file($_FILES[$fieldname]['tmp_name']) ) return false;

	return news_image_add( $news, $_FILES[$fieldname]['tmp_name'], $_FILES[$fieldname]['name'] );
}
// \endcond

// \cond onlyria
/** Permet l'ajout d'un fichier image à une actualité. Cette fonction est similaire à \c news_image_upload,
 *	à l'exception que le fichier doit déjà se trouver sur disque. Cette fonction est notamment utile
 *	lors d'importations.
 *
 *	@param int $news Identifiant de l'actualité.
 *	@param string $filename Nom du fichier image.
 *	@param string $srcname Facultatif, Nom de l'image source
 *
 *	@return int L'identifiant attribué à l'image.
 *	@return bool false en cas d'erreur
 *
 */
function news_image_add( $news, $filename, $srcname='' ){
	global $config;

	$temp = ria_mysql_fetch_array(ria_mysql_query('select max(ni_pos)+1 as pos from news_img where ni_news_id='.$news.' and ni_tnt_id='.$config['tnt_id']));
	if( $temp['pos'] == null ){
		$temp['pos'] = 1;
	}

	$sql = '';
	if( $id = img_images_add( $filename, $srcname ) ){
		$sql = '
			insert into news_img
				( ni_tnt_id, ni_news_id, ni_img_id, ni_pos )
			values
				( '.$config['tnt_id'].','.$news.','.$id.','.$temp['pos'].' )
		';
	}

	if( !trim($sql) || !ria_mysql_query($sql) ){
		return false;
	}

	return $id;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la réutilisation d'une image existante pour une actualité.
 *	@param int $news Obligatoire, Identifiant de l'actualité.
 *	@param int $img Obligatoire, Identifiant du fichier image.
 *	@return bool true en cas de succès, false en cas d'échec
 */
function news_image_add_existing( $news, $img ){
	global $config;
	if( !news_exists($news) ) return false;
	if( !img_images_exists($img) ) return false;

	$temp = ria_mysql_fetch_array(ria_mysql_query('select max(ni_pos)+1 as pos from news_img where ni_news_id='.$news.' and ni_tnt_id='.$config['tnt_id']));
	if($temp['pos'] == null)
		$temp['pos'] = 1;

	if( !ria_mysql_query('insert into news_img (ni_tnt_id, ni_news_id, ni_img_id, ni_pos) values('.$config['tnt_id'].','.$news.','.$img.','.$temp['pos'].')') )
		return false;

	return img_images_count_update($img);
}
// \endcond

// \cond onlyria
/**	Cette fonction permet de déterminer si une actualité est associée à un site et/ou à une langue dans la base de données.
 *	@param int $news_id Obligatoire, Identifiant de l'actualité.
 *	@param int $wst_id Facultatif, Identifiant du site.
 *	@param string $lng Facultatif, code ISO d'une langue
 *	@return bool True si l'association existe, False sinon
 */
function news_websites_exists( $news_id , $wst_id=0, $lng=false ){
	global $config;

	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : false;

	return ria_mysql_num_rows(ria_mysql_query('
		select nw_news_id as news_id, nw_wst_id as wst_id from news_websites
		where nw_news_id='.$news_id.' '.( $wst_id>0 ? ' and nw_wst_id='.$wst_id : '' ).'
			and nw_tnt_id='.$config['tnt_id'].( $lng!=false ? ' and nw_lng_code=\''.$lng.'\'' : '
			and nw_lng_code in (\''.implode('\',\'', $config['i18n_lng_used']).'\')' ).'
		limit 0,1
	'));
}
// \endcond

// \cond onlyria
/**	Cette fonction permet d'associer une actualité à un site
 *	@param int $news_id Obligatoire, identifiant d'une actualité
 *	@param int $wst_id Obligatoire, identifiant d'un site
 *	@param string $lng Facultatif, code ISO d'une langue pour laquelle l'actualité est active
 *	@return bool True en cas de succès, False en cas d'échec
 */
function news_website_add( $news_id, $wst_id, $lng=false ){
	global $config;

	$lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : $config['i18n_lng'];

	if( !news_exists($news_id) || !wst_websites_exists($wst_id) || news_websites_exists($news_id,$wst_id,$lng) )
		return false;
	else
		return ria_mysql_query('insert into news_websites( nw_news_id, nw_wst_id, nw_lng_code, nw_tnt_id ) values ('.$news_id.','.$wst_id.',\''.$lng.'\','.$config['tnt_id'].')');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de dissocier une actualité d'un site et/ou d'une langue
 *
 *	@param int $news_id Obligatoire, Identifiant de l'actualité.
 *	@param int $wst_id Facultatif, Identifiant du site.
 *	@param string $lng Facultatif, code ISO d'une langue
 *	@return bool True en cas de succès, False sinon
 */
function news_website_del( $news_id, $wst_id=0, $lng=false ){
	if( $wst_id>0 && !wst_websites_exists($wst_id) ) return false;
	global $config;

	$lng = $lng!=false && in_array(strtolower($lng), $config['i18n_lng_used']) ? strtolower($lng) : false;

	if( !news_exists($news_id) )
		return false;
	else
		return ria_mysql_query('delete from news_websites where nw_news_id='.$news_id.' '.( $wst_id>0 ? ' and nw_wst_id='.$wst_id : '' ).' '.( $lng!=false ? ' and nw_lng_code=\''.$lng.'\'' : '').' and nw_tnt_id='.$config['tnt_id']);
}
// \endcond

// \cond onlyria
/** Permet de dissocier une actualité à tous les sites
 *
 *	@param int $news_id Identifiant de l'actualité.
 *	@return bool True en cas de succès, False sinon
 *
 */
function news_website_empty( $news_id ){
	global $config;

	if( !news_exists($news_id) ){
		return false;
	}else{
		return ria_mysql_query('delete from news_websites where nw_news_id='.$news_id.' and nw_tnt_id='.$config['tnt_id']);
	}
}
// \endcond

/** Permet l'envoi d'un email pour les commentaires poster sur les actualités
 *	@param int $id Identifiant de l'actu pour laquelle on envoi le message
 *	@param string $subject Titre du commentaire
 *	@param string $body Contenu du commentaire
 *	@param string $email Adresse email de la personne qui a posté le commentaire
 *	@return bool true si l'envoi du message à reussi , false dans le cas contraire
 */
function news_reviews_send( $id, $subject='', $body='', $email='' ){
	global $config;
	$http_host_ria = 'riashop-'.$_SERVER['HTTP_HOST'];
	$rcfg = cfg_emails_get('news-review');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);


	$semail = new Email();
	$semail->setSubject( 'Nouveau commentaire sur une actualité' );
	$semail->setFrom( $email );


	$semail->addTo( $cfg['to'] );
	if( $cfg['cc'] )
			$semail->addCc( $cfg['cc'] );
	if( $cfg['bcc'] )
			$semail->addBcc( $cfg['bcc'] );
	if( $cfg['reply-to'] )
			$semail->setReplyTo( $cfg['reply-to'] );

	$semail->addHtml( $config['email_html_header'] );
	// Construction du corps du message
	$semail->addParagraph( 'Bonjour,' );
	$semail->addParagraph('Un nouveau commentaire vient d\'être enregistré sur le site <a href="'.$config['site_url'].'">'.$config['site_url'].'</a> . Ce commentaire a été déposé par '.$email.' à '.date('H:i').' aujourd\'hui. Vous trouverez le commentaire ci-dessous :');

	$semail->openTable();

	$semail->openTableRow();
	$semail->addCell('Actualité concerné : ');

	$rnews = ria_mysql_fetch_array(news_get($id, false, null, 0, 0, false, false));

	$semail->addCell( '<a href="https://'.$http_host_ria.'/admin/tools/news/edit.php?news='.$id.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=news-comment&amp;utm_content=news-cmt">'.$rnews['name'].'</a>' );
	$semail->closeTableRow();

	$semail->openTableRow();
	$semail->addCell( 'Titre du commentaire :' );
	$semail->addCell( $subject );
	$semail->closeTableRow();

	$semail->openTableRow();
	$semail->addCell( 'Contenu du commentaire :' );
	$semail->addCell( nl2br($body) );
	$semail->closeTableRow();

	$semail->closeTable();


	// Indique si l'avis est publié ou non.
	if( $config['prd_reviews_moderation']=='before' ){
		$semail->addParagraph('Conformément au mode de modération choisi, ce commentaire attend votre approbation pour être affiché dans la boutique. Pour le publier, veuillez vous rendre dans <a href="https://'.$http_host_ria.'/admin/tools/news/edit.php?news='.$id.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=news-comment&amp;utm_content=news-cmt">votre interface d\'administration</a>.');
	}else{
		$semail->addParagraph('Conformément au mode de modération choisi, ce commentaire est déjà publié dans la boutique. Si vous souhaitez le retirer, vous pouvez le faire en vous rendant <a href="https://'.$http_host_ria.'/admin/tools/news/edit.php?news='.$id.'&amp;utm_source=alerts&amp;utm_medium=email&amp;utm_campaign=news-comment&amp;utm_content=news-cmt">dans votre interface d\'administration</a>.');
	}

	// Indique comment contacter l'auteur de l'avis consommateur.
	$semail->addParagraph( "Pour contacter l'auteur de ce commentaire, répondez simplement à cet email." );

	$semail->addHtml( $config['email_html_footer'] );

	return $semail->send();
}

// \cond onlyria
/** Permet l'envoi d'un email à l'auteur du commentaire lorsque celui ci à recu une réponse.
 *	@param int $id Obligatoire, Identifiant du commentaire
 *
 *	@return bool true si l'envoi du message à réussi, false dans le cas contraire
 */
function news_reviews_response_send( $id ){
	global $config;

	if( !is_numeric($id) ){
		return false;
	}

	if( !$config['email_reviews_answer'] ){
		return false;
	}

	$rvw = ria_mysql_fetch_array(rvw_reviews_get($id, null, null, null, 7));
	$rcfg = cfg_emails_get('news-review');
	if( !ria_mysql_num_rows($rcfg) ) return false;
	$cfg = ria_mysql_fetch_array($rcfg);


	$semail = new Email();
	$semail->setSubject( 'Vous venez de recevoir une réponse' );
	$semail->setFrom( $cfg['from'] );

	$semail->addTo( $rvw['usr_email'] );
	if( $cfg['cc'] )
			$semail->addCc( $cfg['cc'] );
	if( $cfg['bcc'] )
			$semail->addBcc( $cfg['bcc'] );
	if( $cfg['reply-to'] )
			$semail->setReplyTo( $cfg['reply-to'] );

	$semail->addHtml( $config['email_new_html_header'] );

	$semail->addParagraph( 'Bonjour,' );
	$semail->addParagraph( 'Vous venez de recevoir une réponse au message laissé sur notre site' );

	$semail->addParagraph( 'Votre message : <br/> <div style="padding-left: 30px;">'.$rvw['desc'].'</div>');

	$semail->addParagraph( 'Réponse '.$config['site_name'].': <br/> <div style="padding-left: 30px;">'.$rvw['ans_desc'].'</div>');

	$semail->addParagraph( '<div style="padding-left:300px">Merci, <br/>L\'équipe '.$config['site_name'].'<br/><br/><br/>' );

	$semail->addHtml( $config['email_new_html_footer'] );

	return $semail->send();
}
// \endcond

// \cond onlyria
/** Permet la modification des champs de référencement pour les actualités
 *	@param int $news Obligatoire, Identifiant de l'actualité
 *	@param string $tag_title Facultatif, Titre de la catégorie
 *	@param string $tag_desc Facultatif, Meta-description de la catégorie
 *	@param string $keywords Facultatif, Mots clés de la catégorie
 *	@return bool True en cas de succès, false en cas d'échec
 */
function news_update_referencing( $news, $tag_title='', $tag_desc='', $keywords='' ){
	global $config;
	if(!is_numeric($news) || !news_exists($news)) return false;

	// Ré-indexe la catégorie
	news_index_rebuild($news);

	//on ajout l'url dans la table
	return 	ria_mysql_query('update news set news_keywords="'.addslashes($keywords).'",news_tag_title="'.addslashes($tag_title).'", news_tag_desc="'.addslashes($tag_desc).'" where news_tnt_id='.$config['tnt_id'].' and news_id='.$news);

}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise TITLE d'une actualité.
 *	@param int $news Obligatoire, Identifiant de l'actualité
 *	@param string $tag_title Facultatif, Titre de la catégorie
 *	@return bool True en cas de succès, false en cas d'échec
 */
function news_update_referencing_tag_title( $news, $tag_title='' ){
	if( !is_numeric($news) || $news<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update news
		set news_tag_title='.( trim($tag_title)!='' ? '\''.addslashes( $tag_title ).'\'' : 'null' ).'
		where news_tnt_id='.$config['tnt_id'].'
			and news_id='.$news.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la personnalisation de la balise META DESCRIPTION d'une actualité.
 *	@param int $news Obligatoire, Identifiant de l'actualité
 *	@param string $tag_desc Facultatif, Meta-description de la catégorie
 *	@return bool True en cas de succès, false en cas d'échec
 */
function news_update_referencing_tag_desc( $news, $tag_desc='' ){
	if( !is_numeric($news) || $news<=0 ){
		return false;
	}

	global $config;

	$sql = '
		update news
		set news_tag_desc='.( trim($tag_desc)!='' ? '\''.addslashes( $tag_desc ).'\'' : 'null' ).'
		where news_tnt_id='.$config['tnt_id'].'
			and news_id='.$news.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet de mettre à jour la position d'une actualité
 *	@param int $news_id Obligatoire, identifiant d'une actualité
 *	@param int $pos Optionnel, position pour cette actualité
 *	@return bool True si la mise à jour s'est correctement déroulée, False dans le cas contraire
 */
function news_update_pos( $news_id, $pos=null ){
	if( !is_numeric($news_id) || $news_id <= 0 ){
		return false;
	}

	if( !is_numeric($pos) || $pos < 0 ){
		$pos = null;
	}

	global $config;

	return ria_mysql_query('
		update news
		set news_pos = '.( $pos !== null ? $pos : 'null' ).'
		where news_tnt_id = '.$config['tnt_id'].'
			and news_id = '.$news_id.'
	');
}
// \endcond

// \cond onlyria
/**	Cette fonction permet d'associer une actualité à un magasin
 *	@param int $news_id Obligatoire, identifiant d'une actualité
 *	@param int $str_id Obligatoire, identifiant du magasin
 *	@return bool True en cas de succès, False en cas d'échec
 */
function news_store_add( $news_id, $str_id ){
	global $config;

	if( !news_exists($news_id) || !dlv_stores_exists($str_id) )
		return false;
	else
		return ria_mysql_query('insert into news_stores( ns_tnt_id, ns_news_id, ns_str_id ) values ('.$config['tnt_id'].','.$news_id.','.$str_id.')');

}
// \endcond

// \cond onlyria
/**	Cette fonction permet de supprimer l'association une actualité à un magasin
 *	@param int $news_id Obligatoire, identifiant d'une actualité
 *	@param int $str_id Facultatif, identifiant du magasin
 *	@return bool True en cas de succès, False en cas d'échec
 */
function news_store_del( $news_id, $str_id=false ){
	global $config;

	if( !news_exists($news_id) ) return false;
	if( $str_id && !dlv_stores_exists($str_id) ) return false;

	$sql = 'delete from news_stores where ns_tnt_id = '.$config['tnt_id'].' and ns_news_id = '.$news_id;

	if( $str_id ) $sql .= ' and ns_str_id = '.$str_id;

	return ria_mysql_query($sql);
}
// \endcond

/**	Cette fonction permet de retourner les magasins associés
 *	@param int $news_id Obligatoire, identifiant d'une actualité
 *	@param int $str_id Facultatif, identifiant du magasin
 *	@return bool True en cas de succès, False en cas d'échec
 */
function news_stores_get( $news_id, $str_id=false ){
	global $config;

	if( !news_exists($news_id) ) return false;
	if( $str_id && !dlv_stores_exists($str_id) ) return false;

	$sql = 'select ns_news_id as news_id, ns_str_id as str_id
			from news_stores
			where ns_tnt_id = '.$config['tnt_id'].'
			  and ns_news_id = '.$news_id;

	if( $str_id ) $sql .= ' and ns_str_id = '.$str_id;

	return ria_mysql_query($sql);
}

// \cond onlyria
/**	Récupération des nouveautés sans image (pour l'affichage dans stats/content-no-image.php)
 *	@param bool $published  Optionnel toutes les actualités sont prises en compte, mettre True pour avoir les actualités publiée, false pour avoir celle non publiée
 *	@param bool $secondary_only Optionnel, toutes les actualités n'ayant aucune image sont retournée, mettre True pour n'avoir que celle ayant aucune image secondaire
 *	@return resource un résultat mysql contenant les informations de la table news ou false si rien n'a été trouvé
 *
 */
function news_get_imageless($published = null, $secondary_only = null){
	global $config;
	$sql = 'select news_id as id, news_name as name, news_intro as intro, news_desc as "desc", news_url_alias as url_alias,
			date_format(news_publish_date,"%d/%m/%Y") as publish_date, news_publish_date as publish_date_en, news_cnt_id as cnt_id,
			date_format(news_publish_date_end,"%d/%m/%Y") as publish_date_end, news_publish_date_end as publish_date_end_en,
			time_format(news_publish_date,"%H:%i") as "hour_from", time_format(news_publish_date_end,"%H:%i") as "hour_to",
			news_publish_date<=now() as published, news_archived as archived, news_cat_id as cat_id,
			news_pos as pos
		from news as a
		WHERE news_tnt_id = '.$config['tnt_id'].' ';

	if($secondary_only === true){
		$sql .= ' AND (select count(ni_news_id) from news_img as b WHERE a.news_id = b.ni_news_id AND b.ni_tnt_id = '.$config['tnt_id'].') = 1 ';
	}
	else{
		$sql .= ' AND NOT EXISTS (select ni_news_id from news_img as b WHERE a.news_id = b.ni_news_id AND b.ni_tnt_id = '.$config['tnt_id'].') ';
	}

	if( $published === true ){
		$sql .= '
			and (news_publish_date is not null and news_publish_date <= now() )
			and (news_publish_date_end is null or news_publish_date_end > now() )
		';
	}
	elseif( $published === false ){
		$sql .= '
			and (news_publish_date is null or news_publish_date >= now() )
			and (news_publish_date_end is null or news_publish_date_end < now() )
		';
	}

	$r = mysql_query($sql);
	if( !$r || !mysql_num_rows($r) ){
		return false;
	}

	return $r;
}
// \endcond

/**	Cette fonction permet de retourner le nom d'une actualité.
 *	@param int $news_id Obligatoire, identifiant d'une actualité
 *	@return string Le nom de l'actualité
 *	@return bool False si le paramètre obligatoire est omis ou faux
 */
function news_get_name( $news_id ){
	if (!is_numeric($news_id) || $news_id <= 0) {
		return false;
	}

	global $config;

	$sql = '
		select news_name
		from news
		where news_tnt_id = '.$config['tnt_id'].'
			and news_id = '.$news_id.'
	';

	$res = ria_mysql_query($sql);
	if (!$res || !ria_mysql_num_rows($res)) {
		return false;
	}

	$r = ria_mysql_fetch_assoc($res);
	return $r['news_name'];
}
/// @}
/// @}


