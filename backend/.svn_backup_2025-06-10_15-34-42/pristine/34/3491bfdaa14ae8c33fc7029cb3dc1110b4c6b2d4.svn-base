<?php
/**
 * CatalogsImportationCatalogInfoApi
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Api;

use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\MultipartStream;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\RequestOptions;
use Swagger\Client\ApiException;
use Swagger\Client\Configuration;
use Swagger\Client\HeaderSelector;
use Swagger\Client\ObjectSerializer;

/**
 * CatalogsImportationCatalogInfoApi Class Doc Comment
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class CatalogsImportationCatalogInfoApi
{
    /**
     * @var ClientInterface
     */
    protected $client;

    /**
     * @var Configuration
     */
    protected $config;

    /**
     * @var HeaderSelector
     */
    protected $headerSelector;

    /**
     * @param ClientInterface $client
     * @param Configuration   $config
     * @param HeaderSelector  $selector
     */
    public function __construct(
        ClientInterface $client = null,
        Configuration $config = null,
        HeaderSelector $selector = null
    ) {
        $this->client = $client ?: new Client();
        $this->config = $config ?: new Configuration();
        $this->headerSelector = $selector ?: new HeaderSelector();
    }

    /**
     * @return Configuration
     */
    public function getConfig()
    {
        return $this->config;
    }

    /**
     * Operation importationConfigureCatalogColumn
     *
     * Configure catalog column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\ConfigureCatalogColumnCatalogRequest $request request (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function importationConfigureCatalogColumn($store_id, $execution_id, $column_id, $request)
    {
        $this->importationConfigureCatalogColumnWithHttpInfo($store_id, $execution_id, $column_id, $request);
    }

    /**
     * Operation importationConfigureCatalogColumnWithHttpInfo
     *
     * Configure catalog column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\ConfigureCatalogColumnCatalogRequest $request (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationConfigureCatalogColumnWithHttpInfo($store_id, $execution_id, $column_id, $request)
    {
        $returnType = '';
        $request = $this->importationConfigureCatalogColumnRequest($store_id, $execution_id, $column_id, $request);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 400:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationConfigureCatalogColumnAsync
     *
     * Configure catalog column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\ConfigureCatalogColumnCatalogRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationConfigureCatalogColumnAsync($store_id, $execution_id, $column_id, $request)
    {
        return $this->importationConfigureCatalogColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id, $request)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationConfigureCatalogColumnAsyncWithHttpInfo
     *
     * Configure catalog column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\ConfigureCatalogColumnCatalogRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationConfigureCatalogColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id, $request)
    {
        $returnType = '';
        $request = $this->importationConfigureCatalogColumnRequest($store_id, $execution_id, $column_id, $request);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationConfigureCatalogColumn'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\ConfigureCatalogColumnCatalogRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationConfigureCatalogColumnRequest($store_id, $execution_id, $column_id, $request)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationConfigureCatalogColumn'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationConfigureCatalogColumn'
            );
        }
        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationConfigureCatalogColumn'
            );
        }
        // verify the required parameter 'request' is set
        if ($request === null || (is_array($request) && count($request) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $request when calling importationConfigureCatalogColumn'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/catalogColumns/{columnId}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($request)) {
            $_tempBody = $request;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationDeleteCustomColumn
     *
     * Delete Custom Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function importationDeleteCustomColumn($store_id, $execution_id, $column_id)
    {
        $this->importationDeleteCustomColumnWithHttpInfo($store_id, $execution_id, $column_id);
    }

    /**
     * Operation importationDeleteCustomColumnWithHttpInfo
     *
     * Delete Custom Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationDeleteCustomColumnWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = '';
        $request = $this->importationDeleteCustomColumnRequest($store_id, $execution_id, $column_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationDeleteCustomColumnAsync
     *
     * Delete Custom Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationDeleteCustomColumnAsync($store_id, $execution_id, $column_id)
    {
        return $this->importationDeleteCustomColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationDeleteCustomColumnAsyncWithHttpInfo
     *
     * Delete Custom Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationDeleteCustomColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = '';
        $request = $this->importationDeleteCustomColumnRequest($store_id, $execution_id, $column_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationDeleteCustomColumn'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationDeleteCustomColumnRequest($store_id, $execution_id, $column_id)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationDeleteCustomColumn'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationDeleteCustomColumn'
            );
        }
        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationDeleteCustomColumn'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/customColumns/{columnId}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'DELETE',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationGetCustomColumnExpression
     *
     * Get the encrypted custom column expression in this importation
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return string
     */
    public function importationGetCustomColumnExpression($store_id, $execution_id, $column_id)
    {
        list($response) = $this->importationGetCustomColumnExpressionWithHttpInfo($store_id, $execution_id, $column_id);
        return $response;
    }

    /**
     * Operation importationGetCustomColumnExpressionWithHttpInfo
     *
     * Get the encrypted custom column expression in this importation
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of string, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationGetCustomColumnExpressionWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = 'string';
        $request = $this->importationGetCustomColumnExpressionRequest($store_id, $execution_id, $column_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        'string',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationGetCustomColumnExpressionAsync
     *
     * Get the encrypted custom column expression in this importation
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationGetCustomColumnExpressionAsync($store_id, $execution_id, $column_id)
    {
        return $this->importationGetCustomColumnExpressionAsyncWithHttpInfo($store_id, $execution_id, $column_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationGetCustomColumnExpressionAsyncWithHttpInfo
     *
     * Get the encrypted custom column expression in this importation
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationGetCustomColumnExpressionAsyncWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = 'string';
        $request = $this->importationGetCustomColumnExpressionRequest($store_id, $execution_id, $column_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationGetCustomColumnExpression'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationGetCustomColumnExpressionRequest($store_id, $execution_id, $column_id)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationGetCustomColumnExpression'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationGetCustomColumnExpression'
            );
        }
        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationGetCustomColumnExpression'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/customColumns/{columnId}/expression';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationGetCustomColumns
     *
     * Get custom columns currently place in this importation
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Swagger\Client\Model\ImportationCustomColumnList
     */
    public function importationGetCustomColumns($store_id, $execution_id)
    {
        list($response) = $this->importationGetCustomColumnsWithHttpInfo($store_id, $execution_id);
        return $response;
    }

    /**
     * Operation importationGetCustomColumnsWithHttpInfo
     *
     * Get custom columns currently place in this importation
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Swagger\Client\Model\ImportationCustomColumnList, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationGetCustomColumnsWithHttpInfo($store_id, $execution_id)
    {
        $returnType = '\Swagger\Client\Model\ImportationCustomColumnList';
        $request = $this->importationGetCustomColumnsRequest($store_id, $execution_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\ImportationCustomColumnList',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationGetCustomColumnsAsync
     *
     * Get custom columns currently place in this importation
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationGetCustomColumnsAsync($store_id, $execution_id)
    {
        return $this->importationGetCustomColumnsAsyncWithHttpInfo($store_id, $execution_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationGetCustomColumnsAsyncWithHttpInfo
     *
     * Get custom columns currently place in this importation
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationGetCustomColumnsAsyncWithHttpInfo($store_id, $execution_id)
    {
        $returnType = '\Swagger\Client\Model\ImportationCustomColumnList';
        $request = $this->importationGetCustomColumnsRequest($store_id, $execution_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationGetCustomColumns'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationGetCustomColumnsRequest($store_id, $execution_id)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationGetCustomColumns'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationGetCustomColumns'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/customColumns';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationGetDetectedCatalogColumns
     *
     * Get detected catalog columns during this importation.
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Swagger\Client\Model\DetectedCatalogColumnList
     */
    public function importationGetDetectedCatalogColumns($store_id, $execution_id)
    {
        list($response) = $this->importationGetDetectedCatalogColumnsWithHttpInfo($store_id, $execution_id);
        return $response;
    }

    /**
     * Operation importationGetDetectedCatalogColumnsWithHttpInfo
     *
     * Get detected catalog columns during this importation.
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Swagger\Client\Model\DetectedCatalogColumnList, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationGetDetectedCatalogColumnsWithHttpInfo($store_id, $execution_id)
    {
        $returnType = '\Swagger\Client\Model\DetectedCatalogColumnList';
        $request = $this->importationGetDetectedCatalogColumnsRequest($store_id, $execution_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\DetectedCatalogColumnList',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationGetDetectedCatalogColumnsAsync
     *
     * Get detected catalog columns during this importation.
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationGetDetectedCatalogColumnsAsync($store_id, $execution_id)
    {
        return $this->importationGetDetectedCatalogColumnsAsyncWithHttpInfo($store_id, $execution_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationGetDetectedCatalogColumnsAsyncWithHttpInfo
     *
     * Get detected catalog columns during this importation.
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationGetDetectedCatalogColumnsAsyncWithHttpInfo($store_id, $execution_id)
    {
        $returnType = '\Swagger\Client\Model\DetectedCatalogColumnList';
        $request = $this->importationGetDetectedCatalogColumnsRequest($store_id, $execution_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationGetDetectedCatalogColumns'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationGetDetectedCatalogColumnsRequest($store_id, $execution_id)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationGetDetectedCatalogColumns'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationGetDetectedCatalogColumns'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/catalogColumns';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationGetProductSample
     *
     * Get the product sample related to this importation with all columns (catalog and custom)
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  int $product_sample_index Index of the product sample. Starting from 0 to 99. (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return \Swagger\Client\Model\ProductSample
     */
    public function importationGetProductSample($store_id, $execution_id, $product_sample_index)
    {
        list($response) = $this->importationGetProductSampleWithHttpInfo($store_id, $execution_id, $product_sample_index);
        return $response;
    }

    /**
     * Operation importationGetProductSampleWithHttpInfo
     *
     * Get the product sample related to this importation with all columns (catalog and custom)
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  int $product_sample_index Index of the product sample. Starting from 0 to 99. (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of \Swagger\Client\Model\ProductSample, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationGetProductSampleWithHttpInfo($store_id, $execution_id, $product_sample_index)
    {
        $returnType = '\Swagger\Client\Model\ProductSample';
        $request = $this->importationGetProductSampleRequest($store_id, $execution_id, $product_sample_index);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\ProductSample',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationGetProductSampleAsync
     *
     * Get the product sample related to this importation with all columns (catalog and custom)
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  int $product_sample_index Index of the product sample. Starting from 0 to 99. (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationGetProductSampleAsync($store_id, $execution_id, $product_sample_index)
    {
        return $this->importationGetProductSampleAsyncWithHttpInfo($store_id, $execution_id, $product_sample_index)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationGetProductSampleAsyncWithHttpInfo
     *
     * Get the product sample related to this importation with all columns (catalog and custom)
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  int $product_sample_index Index of the product sample. Starting from 0 to 99. (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationGetProductSampleAsyncWithHttpInfo($store_id, $execution_id, $product_sample_index)
    {
        $returnType = '\Swagger\Client\Model\ProductSample';
        $request = $this->importationGetProductSampleRequest($store_id, $execution_id, $product_sample_index);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationGetProductSample'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  int $product_sample_index Index of the product sample. Starting from 0 to 99. (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationGetProductSampleRequest($store_id, $execution_id, $product_sample_index)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationGetProductSample'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationGetProductSample'
            );
        }
        // verify the required parameter 'product_sample_index' is set
        if ($product_sample_index === null || (is_array($product_sample_index) && count($product_sample_index) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $product_sample_index when calling importationGetProductSample'
            );
        }
        if ($product_sample_index > 99) {
            throw new \InvalidArgumentException('invalid value for "$product_sample_index" when calling CatalogsImportationCatalogInfoApi.importationGetProductSample, must be smaller than or equal to 99.');
        }
        if ($product_sample_index < 0) {
            throw new \InvalidArgumentException('invalid value for "$product_sample_index" when calling CatalogsImportationCatalogInfoApi.importationGetProductSample, must be bigger than or equal to 0.');
        }


        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/productSamples/{productSampleIndex}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($product_sample_index !== null) {
            $resourcePath = str_replace(
                '{' . 'productSampleIndex' . '}',
                ObjectSerializer::toPathValue($product_sample_index),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationGetProductSampleCustomColumnValue
     *
     * Get product sample custom column value related to this importation.
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  int $product_sample_index Index of the product sample. Starting from 0 to 99. (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return string
     */
    public function importationGetProductSampleCustomColumnValue($store_id, $execution_id, $product_sample_index, $column_id)
    {
        list($response) = $this->importationGetProductSampleCustomColumnValueWithHttpInfo($store_id, $execution_id, $product_sample_index, $column_id);
        return $response;
    }

    /**
     * Operation importationGetProductSampleCustomColumnValueWithHttpInfo
     *
     * Get product sample custom column value related to this importation.
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  int $product_sample_index Index of the product sample. Starting from 0 to 99. (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of string, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationGetProductSampleCustomColumnValueWithHttpInfo($store_id, $execution_id, $product_sample_index, $column_id)
    {
        $returnType = 'string';
        $request = $this->importationGetProductSampleCustomColumnValueRequest($store_id, $execution_id, $product_sample_index, $column_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            $responseBody = $response->getBody();
            if ($returnType === '\SplFileObject') {
                $content = $responseBody; //stream goes to serializer
            } else {
                $content = $responseBody->getContents();
                if ($returnType !== 'string') {
                    $content = json_decode($content);
                }
            }

            return [
                ObjectSerializer::deserialize($content, $returnType, []),
                $response->getStatusCode(),
                $response->getHeaders()
            ];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 200:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        'string',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationGetProductSampleCustomColumnValueAsync
     *
     * Get product sample custom column value related to this importation.
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  int $product_sample_index Index of the product sample. Starting from 0 to 99. (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationGetProductSampleCustomColumnValueAsync($store_id, $execution_id, $product_sample_index, $column_id)
    {
        return $this->importationGetProductSampleCustomColumnValueAsyncWithHttpInfo($store_id, $execution_id, $product_sample_index, $column_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationGetProductSampleCustomColumnValueAsyncWithHttpInfo
     *
     * Get product sample custom column value related to this importation.
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  int $product_sample_index Index of the product sample. Starting from 0 to 99. (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationGetProductSampleCustomColumnValueAsyncWithHttpInfo($store_id, $execution_id, $product_sample_index, $column_id)
    {
        $returnType = 'string';
        $request = $this->importationGetProductSampleCustomColumnValueRequest($store_id, $execution_id, $product_sample_index, $column_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    $responseBody = $response->getBody();
                    if ($returnType === '\SplFileObject') {
                        $content = $responseBody; //stream goes to serializer
                    } else {
                        $content = $responseBody->getContents();
                        if ($returnType !== 'string') {
                            $content = json_decode($content);
                        }
                    }

                    return [
                        ObjectSerializer::deserialize($content, $returnType, []),
                        $response->getStatusCode(),
                        $response->getHeaders()
                    ];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationGetProductSampleCustomColumnValue'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  int $product_sample_index Index of the product sample. Starting from 0 to 99. (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationGetProductSampleCustomColumnValueRequest($store_id, $execution_id, $product_sample_index, $column_id)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationGetProductSampleCustomColumnValue'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationGetProductSampleCustomColumnValue'
            );
        }
        // verify the required parameter 'product_sample_index' is set
        if ($product_sample_index === null || (is_array($product_sample_index) && count($product_sample_index) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $product_sample_index when calling importationGetProductSampleCustomColumnValue'
            );
        }
        if ($product_sample_index > 99) {
            throw new \InvalidArgumentException('invalid value for "$product_sample_index" when calling CatalogsImportationCatalogInfoApi.importationGetProductSampleCustomColumnValue, must be smaller than or equal to 99.');
        }
        if ($product_sample_index < 0) {
            throw new \InvalidArgumentException('invalid value for "$product_sample_index" when calling CatalogsImportationCatalogInfoApi.importationGetProductSampleCustomColumnValue, must be bigger than or equal to 0.');
        }

        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationGetProductSampleCustomColumnValue'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/productSamples/{productSampleIndex}/customColumns/{columnId}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($product_sample_index !== null) {
            $resourcePath = str_replace(
                '{' . 'productSampleIndex' . '}',
                ObjectSerializer::toPathValue($product_sample_index),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'GET',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationIgnoreColumn
     *
     * Ignore Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function importationIgnoreColumn($store_id, $execution_id, $column_id)
    {
        $this->importationIgnoreColumnWithHttpInfo($store_id, $execution_id, $column_id);
    }

    /**
     * Operation importationIgnoreColumnWithHttpInfo
     *
     * Ignore Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationIgnoreColumnWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = '';
        $request = $this->importationIgnoreColumnRequest($store_id, $execution_id, $column_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationIgnoreColumnAsync
     *
     * Ignore Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationIgnoreColumnAsync($store_id, $execution_id, $column_id)
    {
        return $this->importationIgnoreColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationIgnoreColumnAsyncWithHttpInfo
     *
     * Ignore Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationIgnoreColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = '';
        $request = $this->importationIgnoreColumnRequest($store_id, $execution_id, $column_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationIgnoreColumn'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationIgnoreColumnRequest($store_id, $execution_id, $column_id)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationIgnoreColumn'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationIgnoreColumn'
            );
        }
        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationIgnoreColumn'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/catalogColumns/{columnId}/ignore';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationMapCatalogColumn
     *
     * Map catalog column to a BeezUP column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The catalog column identifier (required)
     * @param  \Swagger\Client\Model\MapBeezUPColumnRequest $request request (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function importationMapCatalogColumn($store_id, $execution_id, $column_id, $request)
    {
        $this->importationMapCatalogColumnWithHttpInfo($store_id, $execution_id, $column_id, $request);
    }

    /**
     * Operation importationMapCatalogColumnWithHttpInfo
     *
     * Map catalog column to a BeezUP column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The catalog column identifier (required)
     * @param  \Swagger\Client\Model\MapBeezUPColumnRequest $request (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationMapCatalogColumnWithHttpInfo($store_id, $execution_id, $column_id, $request)
    {
        $returnType = '';
        $request = $this->importationMapCatalogColumnRequest($store_id, $execution_id, $column_id, $request);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationMapCatalogColumnAsync
     *
     * Map catalog column to a BeezUP column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The catalog column identifier (required)
     * @param  \Swagger\Client\Model\MapBeezUPColumnRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationMapCatalogColumnAsync($store_id, $execution_id, $column_id, $request)
    {
        return $this->importationMapCatalogColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id, $request)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationMapCatalogColumnAsyncWithHttpInfo
     *
     * Map catalog column to a BeezUP column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The catalog column identifier (required)
     * @param  \Swagger\Client\Model\MapBeezUPColumnRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationMapCatalogColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id, $request)
    {
        $returnType = '';
        $request = $this->importationMapCatalogColumnRequest($store_id, $execution_id, $column_id, $request);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationMapCatalogColumn'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The catalog column identifier (required)
     * @param  \Swagger\Client\Model\MapBeezUPColumnRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationMapCatalogColumnRequest($store_id, $execution_id, $column_id, $request)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationMapCatalogColumn'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationMapCatalogColumn'
            );
        }
        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationMapCatalogColumn'
            );
        }
        // verify the required parameter 'request' is set
        if ($request === null || (is_array($request) && count($request) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $request when calling importationMapCatalogColumn'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/catalogColumns/{columnId}/map';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($request)) {
            $_tempBody = $request;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationMapCustomColumn
     *
     * Map custom column to a BeezUP column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\MapBeezUPColumnRequest $request request (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function importationMapCustomColumn($store_id, $execution_id, $column_id, $request)
    {
        $this->importationMapCustomColumnWithHttpInfo($store_id, $execution_id, $column_id, $request);
    }

    /**
     * Operation importationMapCustomColumnWithHttpInfo
     *
     * Map custom column to a BeezUP column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\MapBeezUPColumnRequest $request (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationMapCustomColumnWithHttpInfo($store_id, $execution_id, $column_id, $request)
    {
        $returnType = '';
        $request = $this->importationMapCustomColumnRequest($store_id, $execution_id, $column_id, $request);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationMapCustomColumnAsync
     *
     * Map custom column to a BeezUP column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\MapBeezUPColumnRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationMapCustomColumnAsync($store_id, $execution_id, $column_id, $request)
    {
        return $this->importationMapCustomColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id, $request)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationMapCustomColumnAsyncWithHttpInfo
     *
     * Map custom column to a BeezUP column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\MapBeezUPColumnRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationMapCustomColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id, $request)
    {
        $returnType = '';
        $request = $this->importationMapCustomColumnRequest($store_id, $execution_id, $column_id, $request);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationMapCustomColumn'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\MapBeezUPColumnRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationMapCustomColumnRequest($store_id, $execution_id, $column_id, $request)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationMapCustomColumn'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationMapCustomColumn'
            );
        }
        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationMapCustomColumn'
            );
        }
        // verify the required parameter 'request' is set
        if ($request === null || (is_array($request) && count($request) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $request when calling importationMapCustomColumn'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/customColumns/{columnId}/map';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($request)) {
            $_tempBody = $request;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationReattendColumn
     *
     * Reattend Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function importationReattendColumn($store_id, $execution_id, $column_id)
    {
        $this->importationReattendColumnWithHttpInfo($store_id, $execution_id, $column_id);
    }

    /**
     * Operation importationReattendColumnWithHttpInfo
     *
     * Reattend Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationReattendColumnWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = '';
        $request = $this->importationReattendColumnRequest($store_id, $execution_id, $column_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationReattendColumnAsync
     *
     * Reattend Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationReattendColumnAsync($store_id, $execution_id, $column_id)
    {
        return $this->importationReattendColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationReattendColumnAsyncWithHttpInfo
     *
     * Reattend Column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationReattendColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = '';
        $request = $this->importationReattendColumnRequest($store_id, $execution_id, $column_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationReattendColumn'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationReattendColumnRequest($store_id, $execution_id, $column_id)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationReattendColumn'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationReattendColumn'
            );
        }
        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationReattendColumn'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/catalogColumns/{columnId}/reattend';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationSaveCustomColumn
     *
     * Create or replace a custom column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\ChangeCustomColumnRequest $request request (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function importationSaveCustomColumn($store_id, $execution_id, $column_id, $request)
    {
        $this->importationSaveCustomColumnWithHttpInfo($store_id, $execution_id, $column_id, $request);
    }

    /**
     * Operation importationSaveCustomColumnWithHttpInfo
     *
     * Create or replace a custom column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\ChangeCustomColumnRequest $request (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationSaveCustomColumnWithHttpInfo($store_id, $execution_id, $column_id, $request)
    {
        $returnType = '';
        $request = $this->importationSaveCustomColumnRequest($store_id, $execution_id, $column_id, $request);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationSaveCustomColumnAsync
     *
     * Create or replace a custom column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\ChangeCustomColumnRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationSaveCustomColumnAsync($store_id, $execution_id, $column_id, $request)
    {
        return $this->importationSaveCustomColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id, $request)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationSaveCustomColumnAsyncWithHttpInfo
     *
     * Create or replace a custom column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\ChangeCustomColumnRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationSaveCustomColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id, $request)
    {
        $returnType = '';
        $request = $this->importationSaveCustomColumnRequest($store_id, $execution_id, $column_id, $request);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationSaveCustomColumn'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     * @param  \Swagger\Client\Model\ChangeCustomColumnRequest $request (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationSaveCustomColumnRequest($store_id, $execution_id, $column_id, $request)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationSaveCustomColumn'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationSaveCustomColumn'
            );
        }
        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationSaveCustomColumn'
            );
        }
        // verify the required parameter 'request' is set
        if ($request === null || (is_array($request) && count($request) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $request when calling importationSaveCustomColumn'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/customColumns/{columnId}';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;
        if (isset($request)) {
            $_tempBody = $request;
        }

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'PUT',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationUnmapCatalogColumn
     *
     * Unmap catalog column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The catalog column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function importationUnmapCatalogColumn($store_id, $execution_id, $column_id)
    {
        $this->importationUnmapCatalogColumnWithHttpInfo($store_id, $execution_id, $column_id);
    }

    /**
     * Operation importationUnmapCatalogColumnWithHttpInfo
     *
     * Unmap catalog column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The catalog column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationUnmapCatalogColumnWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = '';
        $request = $this->importationUnmapCatalogColumnRequest($store_id, $execution_id, $column_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationUnmapCatalogColumnAsync
     *
     * Unmap catalog column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The catalog column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationUnmapCatalogColumnAsync($store_id, $execution_id, $column_id)
    {
        return $this->importationUnmapCatalogColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationUnmapCatalogColumnAsyncWithHttpInfo
     *
     * Unmap catalog column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The catalog column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationUnmapCatalogColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = '';
        $request = $this->importationUnmapCatalogColumnRequest($store_id, $execution_id, $column_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationUnmapCatalogColumn'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The catalog column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationUnmapCatalogColumnRequest($store_id, $execution_id, $column_id)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationUnmapCatalogColumn'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationUnmapCatalogColumn'
            );
        }
        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationUnmapCatalogColumn'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/catalogColumns/{columnId}/unmap';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Operation importationUnmapCustomColumn
     *
     * Unmap custom column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return void
     */
    public function importationUnmapCustomColumn($store_id, $execution_id, $column_id)
    {
        $this->importationUnmapCustomColumnWithHttpInfo($store_id, $execution_id, $column_id);
    }

    /**
     * Operation importationUnmapCustomColumnWithHttpInfo
     *
     * Unmap custom column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \Swagger\Client\ApiException on non-2xx response
     * @throws \InvalidArgumentException
     * @return array of null, HTTP status code, HTTP response headers (array of strings)
     */
    public function importationUnmapCustomColumnWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = '';
        $request = $this->importationUnmapCustomColumnRequest($store_id, $execution_id, $column_id);

        try {
            $options = $this->createHttpClientOption();
            try {
                $response = $this->client->send($request, $options);
            } catch (RequestException $e) {
                throw new ApiException(
                    "[{$e->getCode()}] {$e->getMessage()}",
                    $e->getCode(),
                    $e->getResponse() ? $e->getResponse()->getHeaders() : null,
                    $e->getResponse() ? $e->getResponse()->getBody()->getContents() : null
                );
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode < 200 || $statusCode > 299) {
                throw new ApiException(
                    sprintf(
                        '[%d] Error connecting to the API (%s)',
                        $statusCode,
                        $request->getUri()
                    ),
                    $statusCode,
                    $response->getHeaders(),
                    $response->getBody()
                );
            }

            return [null, $statusCode, $response->getHeaders()];

        } catch (ApiException $e) {
            switch ($e->getCode()) {
                case 404:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                case 409:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
                default:
                    $data = ObjectSerializer::deserialize(
                        $e->getResponseBody(),
                        '\Swagger\Client\Model\BeezUPCommonErrorResponseMessage',
                        $e->getResponseHeaders()
                    );
                    $e->setResponseObject($data);
                    break;
            }
            throw $e;
        }
    }

    /**
     * Operation importationUnmapCustomColumnAsync
     *
     * Unmap custom column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationUnmapCustomColumnAsync($store_id, $execution_id, $column_id)
    {
        return $this->importationUnmapCustomColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id)
            ->then(
                function ($response) {
                    return $response[0];
                }
            );
    }

    /**
     * Operation importationUnmapCustomColumnAsyncWithHttpInfo
     *
     * Unmap custom column
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function importationUnmapCustomColumnAsyncWithHttpInfo($store_id, $execution_id, $column_id)
    {
        $returnType = '';
        $request = $this->importationUnmapCustomColumnRequest($store_id, $execution_id, $column_id);

        return $this->client
            ->sendAsync($request, $this->createHttpClientOption())
            ->then(
                function ($response) use ($returnType) {
                    return [null, $response->getStatusCode(), $response->getHeaders()];
                },
                function ($exception) {
                    $response = $exception->getResponse();
                    $statusCode = $response->getStatusCode();
                    throw new ApiException(
                        sprintf(
                            '[%d] Error connecting to the API (%s)',
                            $statusCode,
                            $exception->getRequest()->getUri()
                        ),
                        $statusCode,
                        $response->getHeaders(),
                        $response->getBody()
                    );
                }
            );
    }

    /**
     * Create request for operation 'importationUnmapCustomColumn'
     *
     * @param  string $store_id Your store identifier (required)
     * @param  string $execution_id The execution identifier of you catalog importation (required)
     * @param  string $column_id The custom column identifier (required)
     *
     * @throws \InvalidArgumentException
     * @return \GuzzleHttp\Psr7\Request
     */
    protected function importationUnmapCustomColumnRequest($store_id, $execution_id, $column_id)
    {
        // verify the required parameter 'store_id' is set
        if ($store_id === null || (is_array($store_id) && count($store_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $store_id when calling importationUnmapCustomColumn'
            );
        }
        // verify the required parameter 'execution_id' is set
        if ($execution_id === null || (is_array($execution_id) && count($execution_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $execution_id when calling importationUnmapCustomColumn'
            );
        }
        // verify the required parameter 'column_id' is set
        if ($column_id === null || (is_array($column_id) && count($column_id) === 0)) {
            throw new \InvalidArgumentException(
                'Missing the required parameter $column_id when calling importationUnmapCustomColumn'
            );
        }

        $resourcePath = '/user/catalogs/{storeId}/importations/{executionId}/customColumns/{columnId}/unmap';
        $formParams = [];
        $queryParams = [];
        $headerParams = [];
        $httpBody = '';
        $multipart = false;


        // path params
        if ($store_id !== null) {
            $resourcePath = str_replace(
                '{' . 'storeId' . '}',
                ObjectSerializer::toPathValue($store_id),
                $resourcePath
            );
        }
        // path params
        if ($execution_id !== null) {
            $resourcePath = str_replace(
                '{' . 'executionId' . '}',
                ObjectSerializer::toPathValue($execution_id),
                $resourcePath
            );
        }
        // path params
        if ($column_id !== null) {
            $resourcePath = str_replace(
                '{' . 'columnId' . '}',
                ObjectSerializer::toPathValue($column_id),
                $resourcePath
            );
        }

        // body params
        $_tempBody = null;

        if ($multipart) {
            $headers = $this->headerSelector->selectHeadersForMultipart(
                ['application/json']
            );
        } else {
            $headers = $this->headerSelector->selectHeaders(
                ['application/json'],
                ['application/json']
            );
        }

        // for model (json/xml)
        if (isset($_tempBody)) {
            // $_tempBody is the method argument, if present
            $httpBody = $_tempBody;
            // \stdClass has no __toString(), so we should encode it manually
            if ($httpBody instanceof \stdClass && $headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($httpBody);
            }
        } elseif (count($formParams) > 0) {
            if ($multipart) {
                $multipartContents = [];
                foreach ($formParams as $formParamName => $formParamValue) {
                    $multipartContents[] = [
                        'name' => $formParamName,
                        'contents' => $formParamValue
                    ];
                }
                // for HTTP post (form)
                $httpBody = new MultipartStream($multipartContents);

            } elseif ($headers['Content-Type'] === 'application/json') {
                $httpBody = \GuzzleHttp\json_encode($formParams);

            } else {
                // for HTTP post (form)
                $httpBody = \GuzzleHttp\Psr7\build_query($formParams);
            }
        }

        // this endpoint requires API key authentication
        $apiKey = $this->config->getApiKeyWithPrefix('Ocp-Apim-Subscription-Key');
        if ($apiKey !== null) {
            $headers['Ocp-Apim-Subscription-Key'] = $apiKey;
        }

        $defaultHeaders = [];
        if ($this->config->getUserAgent()) {
            $defaultHeaders['User-Agent'] = $this->config->getUserAgent();
        }

        $headers = array_merge(
            $defaultHeaders,
            $headerParams,
            $headers
        );

        $query = \GuzzleHttp\Psr7\build_query($queryParams);
        return new Request(
            'POST',
            $this->config->getHost() . $resourcePath . ($query ? "?{$query}" : ''),
            $headers,
            $httpBody
        );
    }

    /**
     * Create http client option
     *
     * @throws \RuntimeException on file opening failure
     * @return array of http client options
     */
    protected function createHttpClientOption()
    {
        $options = [];
        if ($this->config->getDebug()) {
            $options[RequestOptions::DEBUG] = fopen($this->config->getDebugFile(), 'a');
            if (!$options[RequestOptions::DEBUG]) {
                throw new \RuntimeException('Failed to open the debug file: ' . $this->config->getDebugFile());
            }
        }

        return $options;
    }
}
