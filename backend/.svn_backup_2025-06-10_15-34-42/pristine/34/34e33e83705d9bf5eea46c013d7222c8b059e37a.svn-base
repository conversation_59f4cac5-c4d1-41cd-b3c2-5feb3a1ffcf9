<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/decl.proto

namespace Google\Api\Expr\V1beta1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A declaration.
 *
 * Generated from protobuf message <code>google.api.expr.v1beta1.Decl</code>
 */
class Decl extends \Google\Protobuf\Internal\Message
{
    /**
     * The id of the declaration.
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     */
    private $id = 0;
    /**
     * The name of the declaration.
     *
     * Generated from protobuf field <code>string name = 2;</code>
     */
    private $name = '';
    /**
     * The documentation string for the declaration.
     *
     * Generated from protobuf field <code>string doc = 3;</code>
     */
    private $doc = '';
    protected $kind;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $id
     *           The id of the declaration.
     *     @type string $name
     *           The name of the declaration.
     *     @type string $doc
     *           The documentation string for the declaration.
     *     @type \Google\Api\Expr\V1beta1\IdentDecl $ident
     *           An identifier declaration.
     *     @type \Google\Api\Expr\V1beta1\FunctionDecl $function
     *           A function declaration.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Expr\V1Beta1\Decl::initOnce();
        parent::__construct($data);
    }

    /**
     * The id of the declaration.
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * The id of the declaration.
     *
     * Generated from protobuf field <code>int32 id = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt32($var);
        $this->id = $var;

        return $this;
    }

    /**
     * The name of the declaration.
     *
     * Generated from protobuf field <code>string name = 2;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * The name of the declaration.
     *
     * Generated from protobuf field <code>string name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * The documentation string for the declaration.
     *
     * Generated from protobuf field <code>string doc = 3;</code>
     * @return string
     */
    public function getDoc()
    {
        return $this->doc;
    }

    /**
     * The documentation string for the declaration.
     *
     * Generated from protobuf field <code>string doc = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setDoc($var)
    {
        GPBUtil::checkString($var, True);
        $this->doc = $var;

        return $this;
    }

    /**
     * An identifier declaration.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.IdentDecl ident = 4;</code>
     * @return \Google\Api\Expr\V1beta1\IdentDecl
     */
    public function getIdent()
    {
        return $this->readOneof(4);
    }

    /**
     * An identifier declaration.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.IdentDecl ident = 4;</code>
     * @param \Google\Api\Expr\V1beta1\IdentDecl $var
     * @return $this
     */
    public function setIdent($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Expr\V1beta1\IdentDecl::class);
        $this->writeOneof(4, $var);

        return $this;
    }

    /**
     * A function declaration.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.FunctionDecl function = 5;</code>
     * @return \Google\Api\Expr\V1beta1\FunctionDecl
     */
    public function getFunction()
    {
        return $this->readOneof(5);
    }

    /**
     * A function declaration.
     *
     * Generated from protobuf field <code>.google.api.expr.v1beta1.FunctionDecl function = 5;</code>
     * @param \Google\Api\Expr\V1beta1\FunctionDecl $var
     * @return $this
     */
    public function setFunction($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Expr\V1beta1\FunctionDecl::class);
        $this->writeOneof(5, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getKind()
    {
        return $this->whichOneof("kind");
    }

}

