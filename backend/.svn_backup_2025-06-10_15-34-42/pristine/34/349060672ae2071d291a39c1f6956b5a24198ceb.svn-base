<?php

require_once 'Services/Stores/AbstractStores.class.php';

class StoreService extends AbstractStores
{

	/**	Identifiant du magasin
	 * @var	int
	 */
	protected $id = 0;

	/**	Nom du magasin
	 * @var	string
	 */
	protected $name = '';

	/**	URL du magasin
	 * @var	string
	 */
	protected $url = '';

	/**	Description du magasin
	 * @var	string
	 */
	protected $desc = '';

	/**	Description du magasin sans html
	 * @var	string
	 */
	protected $desc_strip_tags = '';

	/**	Ligne 1 de l'adresse du magasin
	 * @var	string
	 */
	protected $address1 = '';

	/**	Ligne 2 de l'adresse du magasin
	 * @var	string
	 */
	protected $address2 = '';

	/**	Code postal du magasin
	 * @var	string|int
	 */
	protected $zipcode = '';

	/**	Ville du magasin
	 * @var	string
	 */
	protected $city = '';

	/**	Pays du magasin
	 * @var	string
	 */
	protected $country = '';

	/**	Téléphone du magasin
	 * @var	string|int
	 */
	protected $phone = '';

	/**	Email du magasin
	 * @var	string
	 */
	protected $email = '';

	/**	Site web du magasin
	 * @var	string
	 */
	protected $website = '';

	/**	Longitude du magasin
	 * @var	float
	 */
	protected $longitude = '';

	/**	Latitude du magasin
	 * @var	float
	 */
	protected $latitude = '';

	/**	Adresse compléte (formatée) du magasin
	 * @var	string
	 */
	protected $formatted_address = '';

	/**	Détermine si c'est un magasin ou non
	 * @var	bool
	 */
	protected $is_store = false;

	/**	Tableau des données de l'image principale associée au magasin
	 * @var	array
	 */
	protected $main_image = [];

	/**	Tableau des images associées au magasin
	 * @var	array
	 */
	protected $images = [];

	/**	Nombre d'images associées au magasin
	 * @var	int
	 */
	protected $images_length = 0;

	/**	Distance calculée par rapport aux coordonnées magasin (lat/ long) et d'autres coordonnées (lat/ long)
	 * @var	float
	 */
	protected $distance = 0;

	/**	Tableau contenant les horaires d'ouverture du magasin
	 * @var	array
	 */
	protected $timetable = [];

	/**	Texte court du créneau horaire du magasin
	 * @todo champ avancé
	 * @var	string
	 */
	protected $time_slot = '';

	/**	URL google map du magasin
	 * @var	string
	 */
	protected $route_url = '';

	/**	URL google map du magasin (embed) (pour iframe)
	 * @var	string
	 */
	protected $embed_url = '';

	/**	Tableau contenant des données du magasin utilisables lors de $this->buildAddress()
	 * @var	array
	 */
	private $store = [];

	/**	Valeur comprise entre 1 (dézoomé) et 20 (zoomé)
	 * @var	int
	 */
	private $embed_zoom = 7;

	/**
	 * Initialise les données du magasin
	 * @param array $data Optionnel, données du magasin
	 */
	public function __construct($data = [])
	{

		if (!is_array($data)) {
			$data = [];
		}
		// Controle et initialise l'identifiant du magasin
		$this->id = !isset($data['id']) || !is_numeric($data['id']) ? 0 : (int)$data['id'];
		$this->id = !dlv_stores_exists($this->id) ? 0 : $this->id;
		$this->store['id'] = $this->id;
		$this->embed_zoom = isset($data['ezoom']) && is_int($data['ezoom']) && $data['ezoom'] > 0 && $data['ezoom'] < 21 ? $data['ezoom'] : 7;

		// Nous n'avons plus besoin de l'identifiant magasin
		unset($data['id']);

		// Initiliase les données du magasin
		$this->setData($data)
			->loadGoogleMapRouteURL()
			->loadEmbedURL();

		// Obligatoire
		$this->resetParameters();
	}

	/**
	 * Permet de surcharger $this->store et les variables
	 * @param array $data Obligatoire, Tableau contenant les données à corréler
	 */
	private function setData($data = [])
	{

		if (!is_array($data)) {
			throw new Exception('$data doit être un tableau');
		}
		$this->name = ria_array_get($data, 'name', '');
		$this->url = ria_array_get($data, 'url_alias', '');
		$this->desc = ria_array_get($data, 'desc', '');
		$this->desc_strip_tags = strip_tags($this->desc);
		$this->address1 = ria_array_get($data, 'address1', '');
		$this->address2 = ria_array_get($data, 'address2', '');
		$this->zipcode = ria_array_get($data, 'zipcode', '');
		$this->city = ria_array_get($data, 'city', '');
		$this->country = ria_array_get($data, 'country', '');
		$this->phone = ria_array_get($data, 'phone', '');
		$this->email = ria_array_get($data, 'email', '');
		$this->website = ria_array_get($data, 'website', '');
		$this->longitude = ria_array_get($data, 'longitude', '');
		$this->latitude = ria_array_get($data, 'latitude', '');
		$this->distance = ria_array_get($data, 'distance', 0);

		$this->store = [
			'name'		=> $this->name,
			'address1'	=> $this->address1,
			'address2'	=> $this->address2,
			'zipcode'	=> $this->zipcode,
			'city'		=> $this->city,
			'country'	=> $this->country,
			'email'		=> $this->email,
			'phone'		=> $this->phone,
			'website'	=> $this->website,
			'longitude'	=> $this->longitude,
			'latitude'	=> $this->latitude
		];

		return $this;
	}

	/**
	 * Récupére les données du magasin
	 * @param bool $active True pour récupérer les données du magasin uniquement s'il est publié, false sinon
	 */
	public function loadStore()
	{

		// Si le magasin souhaité a un identifiant valide
		if (!$this->id) {
			return $this;
		}
		// Construit les variables à passer en paramètre de dlv_stores_get()
		extract(parent::getParameters());

		$r_store = dlv_stores_get($this->id, $allow_delivery, $sort, $sector, $dept, $coordinates, $zipcode, $country, $start_row, $limit, $fld, $or_between_val, $or_between_fld, $sales_types, $name, $is_like, $publish, $email, $seg_id, $continent, $brands, $zones, $website, $city, $sales_types_or, $is_clickandcollect, $like_type_fld, $lng, $alias_tbl, $check_on_childs);

		if (!ria_mysql_num_rows($r_store)) {
			throw new Exception('Le magasin n\'existe pas ou plus.');
		}
		$with_all_images = isset($images) && is_bool($images) && $images;
		$with_main_img_only = isset($only_main_img) && is_bool($only_main_img) && $only_main_img;
		$with_images = $with_all_images || $with_main_img_only;
		$store = i18n::getTranslation(CLS_STORE, ria_mysql_fetch_assoc($r_store));
		$this->is_store = true;

		if ($with_images) {
			$this->getImages($with_main_img_only);
		}
		$this->setData($store)
			->loadGoogleMapRouteURL()
			->loadEmbedURL();

		return $this;
	}

	/**
	 * Permet de calculer la distance entre 2 points par rapport aux latitudes et longitudes
	 * La méthode loadStore() doit être appelée avant
	 * @param	float	$lat Obligatoire, Latitude de départ
	 * @param	float	$lng Obligatoire, Longitude de départ
	 * @return	object	L'object en cours
	 */
	public function calculateDistance($lat, $lng)
	{

		if (!$this->is_store || !is_numeric($lat) || !is_numeric($lng)) {
			return $this;
		}
		$lat1 = deg2rad($this->latitude);
		$long1 = deg2rad($this->longitude);

		$lat2 = deg2rad($lat);
		$long2 = deg2rad($lng);

		$dlong = $long2 - $long1;
		$dlati = $lat2 - $lat1;

		$val = pow(sin($dlati / 2), 2) + cos($lat1) * cos($lat2) * pow(sin($dlong / 2), 2);

		$res = 2 * asin(sqrt($val));

		$radius = 3958.756;

		$this->distance = round($res * $radius, 2);

		return $this;
	}

	/**
	 * Permet de charger les horaires d'ouverture du magasin
	 * Surcharge $this->timetable
	 * @return	object	L'object en cours
	 */
	public function loadTimePeriod()
	{

		if (!$this->id) {
			return $this;
		}
		$type_period = [
			'morning_summer'	=> 1,
			'afternoon_summer'	=> 2,
			'morning_winter'	=> 3,
			'afternoon_winter'	=> 4
		];

		$days = [
			1 => i18n::get('Lundi', 'STORE'),
			2 => i18n::get('Mardi', 'STORE'),
			3 => i18n::get('Mercredi', 'STORE'),
			4 => i18n::get('Jeudi', 'STORE'),
			5 => i18n::get('Vendredi', 'STORE'),
			6 => i18n::get('Samedi', 'STORE'),
			7 => i18n::get('Dimanche', 'STORE')
		];
		$per = [];
		$r_object = per_objects_get(CLS_STORE, $this->id, 0, 0, 0, array($type_period['morning_summer'], $type_period['afternoon_summer']));

		if (!ria_mysql_num_rows($r_object)) {
			return $this;
		}

		while ($object = ria_mysql_fetch_assoc($r_object)) {
			$r_period = per_periods_get($object['id'], 0, true);

			if (!ria_mysql_num_rows($r_period)) {
				break;
			}

			while ($period = ria_mysql_fetch_assoc($r_period)) {
				$per[$period['day_id']]['day'] = $days[$period['day_id']];

				if ($object['type_id'] == 1) {
					$per[$period['day_id']]['morning']['start'] = $period['start'];
					$per[$period['day_id']]['morning']['end'] = $period['end'];
					continue;
				}

				if ($object['type_id'] == 2) {
					$per[$period['day_id']]['afternoon']['start'] = $period['start'];
					$per[$period['day_id']]['afternoon']['end'] = $period['end'];
				}
			}
		}
		$this->timetable = $per;

		// Complète le tableau des jours
		if (count($per) < 7) {
			foreach ($days as $d_id => $day) {
				if (isset($per[$d_id])) {
					continue;
				}
				$per[$d_id] = [
					'day'	=> $day
				];
			}
			$this->timetable = $per;
		}
		return $this;
	}


	/**
	 * Permet de charger les images associées au magasin
	 * @param	bool	$only_main Optionnel, True pour récupérer uniquement l'image principale
	 * @return	object	L'object en cours
	 */
	public function getImages($only_main = false)
	{
		global $config;

		$r_imgs = dlv_stores_images_get($this->id);

		if (!ria_mysql_num_rows($r_imgs)) {
			return $this;
		}
		$i = 0;
		$only_main = is_bool($only_main) && $only_main;
		$thumb_code = 'high';
		$cfg_img = $this->getParameter('cfg_img');

		if ($cfg_img !== $thumb_code && array_key_exists($cfg_img, $config['img_sizes'])) {
			$thumb_code = $cfg_img;
		} elseif (array_key_exists(Template::get('productcard-cfg-image'), $config['img_sizes'])) {
			$thumb_code = Template::get('productcard-cfg-image');
		}
		$thumb = $config['img_sizes'][$thumb_code];

		while ($img = ria_mysql_fetch_assoc($r_imgs)) {
			$ar_image = [
				'id'		=> $img['id'],
				'url'		=> $config['img_url'] . '/' . $thumb['dir'] . '/' . $img['id'] . '.' . $thumb['format'],
				'width'		=> $thumb['width'],
				'height'	=> $thumb['height'],
				'alt'		=> $this->name
			];

			if ($i === 0) {
				$this->main_image = $ar_image;
			}
			$this->images[] = $ar_image;
			$i++;

			if ($only_main) {
				break;
			}
		}
		$this->images_length = $i;

		return $this;
	}

	/**
	 * Charge toutes les valeurs des champs avancés du magasin
	 * @return object l'object courant
	 */
	private function loadFldValues()
	{
	}

	/**
	 * Charge la valeur d'un champ avancé du magasin
	 * @return object l'object courant
	 */
	private function loadFldValue($fld_id)
	{
	}

	/**
	 * Retourne la valeur d'un champ avancé du magasin
	 * @param int $fld_id Obligatoire, Identifiant du champ avancé
	 * @return mixed Valeur du champ avancé, sinon false
	 */
	public function getFldValue($fld_id)
	{
	}

	/**
	 * Retourne les valeurs des champs avancés du magasin
	 * @return array Valeurs des champs avancés (fld_id => valeur), false sinon
	 */
	public function getFldValues()
	{
	}

	/**
	 * Construit l'adresse du magasin
	 * @param	array	$lines		Optionnel, lignes constituantes l'adresse, l'ordre est important (exemple: ['address1', 'zipcode city', 'country'])
	 * @param	string	$separator	Optionnel, séparateur des différentes lignes
	 * @param	object	L'object en cours
	 */
	public function buildAddress($lines = [], $separator = '-')
	{

		if (!is_array($lines) || !count($lines)) {
			$lines = ['name', 'address1', 'address2', 'zipcode city', 'country', 'phone', 'email'];
		}
		$separator = !is_string($separator) ? '-' : $separator;

		$this->formatted_address = $this->buildLines($lines, $separator);

		return $this;
	}

	/**
	 * Construit une ligne à l'adresse du magasin
	 * @param	array	$lines		Optionnel, lignes constituants l'adresse, l'ordre est important (exemple: ['address1', 'zipcode city', 'country'])
	 * @param	string	$separator	Optionnel, séparateur des différentes lignes
	 * @return	string	L'adresse du magasin
	 */
	private function buildLines($lines = [], $separator = '-')
	{

		if (!is_array($lines) || !count($lines)) {
			return '';
		}
		$adr = '';

		foreach ($lines as $line) {
			$line = trim($line);

			if (strpos($line, ' ')) {
				$adr .= $this->buildLines(explode(' ', $line), ' ') . $separator;
				continue;
			}

			if (!array_key_exists($line, $this->store) || $this->store[$line] === '') {
				continue;
			}
			$adr .= $this->store[$line] . $separator;
		}
		return trim($adr, $separator);
	}

	/**
	 * Construit l'url google map du magasin
	 * @return	object	L'objet en cours
	 */
	private function loadGoogleMapRouteURL()
	{
		$base_url = 'https://www.google.com/maps/dir/';

		$queries = http_build_query([
			'api'			=> 1,
			'destination'	=> $this->latitude . ',' . $this->longitude
		]);
		$this->route_url = urldecode($base_url . '?' . $queries);

		return $this;
	}

	/**
	 * Construit l'url google map du magasin (embed version) (pour iframe)
	 * @return	object	L'objet en cours
	 */
	private function loadEmbedURL()
	{
		$base_url = 'https://maps.google.com/maps';

		$queries = http_build_query([
			'q'			=> $this->latitude . ',' . $this->longitude,
			'hl'		=> i18n::getLang(),
			'z'			=> $this->embed_zoom,
			'output'	=> 'embed'
		]);
		$this->embed_url = urldecode($base_url . '?' . $queries);

		return $this;
	}

	/**
	 * Retourne l'identifiant du magasin
	 * @return	int	L'identifiant du magasin
	 */
	public function getID()
	{
		return $this->id;
	}

	/**	Permet de savoir si le magasin existe ou non
	 * @return	bool	True si le magasin existe, false sinon
	 */
	public function exists()
	{
		return $this->id > 0;
	}
}
