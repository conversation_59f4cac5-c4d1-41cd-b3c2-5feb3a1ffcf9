{"name": "mikey179/vfsstream", "type": "library", "homepage": "http://vfs.bovigo.org/", "description": "Virtual file system to mock the real file system in unit tests.", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "homepage": "http://frankkleine.de/", "role": "Developer"}], "support": {"issues": "https://github.com/bovigo/vfsStream/issues", "source": "https://github.com/bovigo/vfsStream/tree/master", "wiki": "https://github.com/bovigo/vfsStream/wiki"}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.5|^5.0"}, "autoload": {"psr-0": {"org\\bovigo\\vfs\\": "src/main/php"}}, "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}}