<?php 

/** 
 * \defgroup api-devices-request_sync IOS - Demande de synchronisation 
 * \ingroup Yuto 
 * @{		
 * \page api-devices-request_sync-upd Mise à jour
 *
 * Cette fonction permet l'envoi d'une demande de synchronisation sur le device si nécessaire
 *
 *	\code
 *		PUT /devices/request_sync/
 *	\endcode
 *	
 * @param int $id Obligatoire, Identifiant du device
 *	
 * @return true si la mise à jour s'est déroulée avec succès 
 * @}
*/

switch( $method ){
	case 'upd': 

		if( !isset($_REQUEST['id']) ){
			throw new Exception("Il manque l'id du device");
		}

		$rdev = dev_devices_get($_REQUEST['id']);
		if( !$rdev || !ria_mysql_num_rows($rdev) ){
			throw new Exception("Device introuvable dans la base");
		}
		$dev = ria_mysql_fetch_assoc($rdev);

		// check la date de la dernière sync, si pas de retour de l'appareil depuis 1h on retente une notif 
		$go_check = !$dev['is_waiting_sync'];

		if( !$dev['is_active'] ){
			$go_check = false;
		}

		if( !$go_check && strtotime($dev['date_last_notify']) < strtotime("-1 HOUR") ){
			$go_check = true;
		}

		if( $go_check ){

			$res = dev_send_notifications($dev['id'], '', '', array());
			if( !$res || ( isset($res['success']) && $res['success']==0 ) ){
				throw new Exception("Erreur d'envoi:".print_r($res, true));
			}

			// met à jour la date de notification de l'appareil 
			dev_devices_set_notify_date($dev['id'], date('Y-m-d H:i:s'));

			// marque l'apareil en attente de sync 
			dev_devices_set_is_waiting_sync($dev['id'], true);

		}
		$result = true;

		break;
}
