<?php

	/**	\file export-bestsellers-cat.php
	 *
	 * 	Ce fichier réalise l'export des données de l'écran bestsellers-cat.php, au format Microsoft Excel.
	 * 
	 */

	require_once('products.inc.php');
	require_once('fields.inc.php');

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_STATS_BESTSELLER_CAT');
	
	$date1 = isset($_SESSION['datepicker_date1']) ? dateparse( $_SESSION['datepicker_date1'] ) : date('Y-m-d');
	$date2 = isset($_SESSION['datepicker_date2']) ? dateparse( $_SESSION['datepicker_date2'] ) : date('Y-m-d');
	$date2 = strtotime($date2)<strtotime($date1) ? $date1 : $date2;
	
	// Filtre sur l'origine de la commande
	$parent = isset($_GET['parent']) && is_numeric($_GET['parent']) && $_GET['parent']>0 ? $_GET['parent'] : 0;
	$params = view_origins_get_params();
	$origin = $params['origin'];
	$gescom = $params['gescom'];
	$is_web = $params['is_web'];

	$wst_id = 0;
	if( isset($_SESSION['websitepicker']) ){
		$wst_id = str_replace('w-', '', $_SESSION['websitepicker']);
	}
	
	// Gère le filtre par représentant
	$seller_id = isset($_SESSION['ord_seller_id']) ? $_SESSION['ord_seller_id'] : 0;

	require_once( 'excel/PHPExcel.php' );

	// Création de l'objet PHPExcel
	$objPHPExcel = new PHPExcel();

	// Déterminé les propriétés de la feuille Excel
	$objPHPExcel->getProperties()->setCreator( "riaStudio" )
								 ->setLastModifiedBy( "riaStudio" )
								 ->setTitle( _("Meilleurs Ventes / Categorie") )
								 ->setSubject( _("Meilleurs Ventes / Categorie") )
								 ->setDescription( _("Meilleurs Ventes / Categorie") )
								 ->setKeywords( _("Meilleurs Ventes / Categorie") )
								 ->setCategory( "" );
	
	// Création du fichier Excel
	$objWorksheet1 = $objPHPExcel->getActiveSheet();
	$objWorksheet1->setTitle( _("Catégories") );

	$line = 3;

	$title_cat = '';
	if( $parent<=0 ){
		$title_cat .= _('Catalogue');
	}else{
		$name = _('Catalogue').' » ';
		$last = false;
		
		$parents = prd_categories_parents_get($parent);
		$cat = ria_mysql_fetch_array(prd_categories_get($parent));
		while( $p = ria_mysql_fetch_array($parents) ){
			$name .= htmlspecialchars($p['title']).' » ';
			$last = $p;
		}

		$title_cat .= $name.htmlspecialchars($cat['title']);
	}

	// Entête du tableau des catégories
	$objWorksheet1->mergeCells( 'A1:F1' );
	$objWorksheet1->setCellValue( 'A1', $title_cat );
	$objWorksheet1->setCellValue( 'A2', _('Désignation') );
	$objWorksheet1->setCellValue( 'B2', _('Ventes') );
	$objWorksheet1->setCellValue( 'C2', _('Par commandes') );
	$objWorksheet1->setCellValue( 'D2', _('Marge brute') );
	$objWorksheet1->setCellValue( 'E2', _('CA') );
	$objWorksheet1->setCellValue( 'F2', _('CA Total') );

	// Style sur l'entête du tableau
	$objWorksheet1->getStyle('A1:F2')->getFont()->setBold(true);
	$objWorksheet1->getStyle('A1:F2')->getFont()->setSize(11);
	$objWorksheet1->getStyle('A1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
	$objWorksheet1->getStyle('A1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('000099');
	$objWorksheet1->getStyle('A2:F2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('BBBBBB');
	$objWorksheet1->getStyle('A2:F2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
	
	// Cords du tableau des catégories
	$total = array( 'orders'=>0, 'margin'=>0, 'by-ord'=>0, 'ca'=>0, 'ca-ord'=>0 );
	$rtotal = stats_categories_get_orders( $parent, $date1, $date2, $origin, $gescom, $wst_id, $seller_id );
	if( $rtotal && ria_mysql_num_rows($rtotal) ){
		$r = ria_mysql_fetch_array( $rtotal );
		
		$total['orders'] += $r['orders'];
		$total['by-ord'] += $r['orders']>0 ? $r['qte']/$r['orders'] : 0;
		$total['margin'] += $r['margin'];
		$total['ca'] += $r['total_ht'];
		$total['ca-ord'] += $r['ord_total_ht'];
	}

	$rcat = prd_categories_get( 0, false, $parent );
	if( $rcat && ria_mysql_num_rows($rcat) ){
		while( $cat = ria_mysql_fetch_array($rcat) ){
			$stat = array( 'qte' => 0, 'margin'=>0, 'total_ht'=> 0, 'orders'=>0, 'ord_total_ht'=>0 );
			$rstat = stats_categories_get_orders( $cat['id'], $date1, $date2, $origin, $gescom, $wst_id, $seller_id );
			if( $rstat && ria_mysql_num_rows($rstat) ){
				$stat = ria_mysql_fetch_array( $rstat );
			}

			$objWorksheet1->setCellValue( 'A'.$line, htmlspecialchars( $cat['name'] ) );
			$objWorksheet1->setCellValue( 'B'.$line, number_format( $stat['orders'], 0, ',', ' ' ) );
			$objWorksheet1->setCellValue( 'C'.$line, number_format( $stat['orders']>0 ? $stat['qte']/$stat['orders'] : 0, 0, ',', ' ' ) );
			$objWorksheet1->setCellValue( 'D'.$line, number_format( $stat['margin'], 2, ',', ' ' ) );
			$objWorksheet1->setCellValue( 'E'.$line, number_format( $stat['total_ht'], 2, ',', ' ' ) );
			$objWorksheet1->setCellValue( 'F'.$line, number_format( $stat['ord_total_ht'], 2, ',', ' ' ) );
			
			$line ++;
		}
	}else{
		$cat = ria_mysql_fetch_array( prd_categories_get($parent) );

		$stat = array( 'qte' => 0, 'margin'=>0, 'total_ht'=> 0, 'orders'=>0, 'ord_total_ht'=>0 );
		$rstat = stats_categories_get_orders( $cat['id'], $date1, $date2, $origin, $gescom, $wst_id, $seller_id );
		if( $rstat && ria_mysql_num_rows($rstat) ){
			$stat = ria_mysql_fetch_array( $rstat );
		}

		$objWorksheet1->setCellValue( 'A'.$line, htmlspecialchars($cat['name']) );
		$objWorksheet1->setCellValue( 'B'.$line, number_format($stat['orders'], 0, ',', ' ') );
		$objWorksheet1->setCellValue( 'C'.$line, number_format($stat['orders']>0 ? $stat['qte']/$stat['orders'] : 0, 0, ',', ' ') );
		$objWorksheet1->setCellValue( 'D'.$line, number_format($stat['margin'], 2, ',', ' ') );
		$objWorksheet1->setCellValue( 'E'.$line, number_format($stat['total_ht'], 2, ',', ' ') );
		$objWorksheet1->setCellValue( 'F'.$line, number_format($stat['ord_total_ht'], 2, ',', ' ') );

		$line ++;
	}

	// Pieds du tableau des catégories
	$objWorksheet1->setCellValue( 'A'.$line, _('Totaux').' :' );
	$objWorksheet1->setCellValue( 'B'.$line, number_format($total['orders'], 0, ',', ' ') );
	$objWorksheet1->setCellValue( 'C'.$line, number_format($total['by-ord'], 0, ',', ' ') );
	$objWorksheet1->setCellValue( 'D'.$line, number_format($total['margin'], 2, ',', ' ') );
	$objWorksheet1->setCellValue( 'E'.$line, number_format($total['ca'], 2, ',', ' ') );
	$objWorksheet1->setCellValue( 'F'.$line, number_format($total['ca-ord'], 2, ',', ' ') );

	$objWorksheet1->getStyle('A'.$line.':F'.$line)->getFont()->setBold(true);
	$objWorksheet1->getStyle('A'.$line.':F'.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('BBBBBB');
	$objWorksheet1->getStyle('A'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
	$objWorksheet1->getStyle('B2:F'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);

	// Applique des formats aux cellules C5:G7
	$objWorksheet1->getStyle('D3:F'.$line)->getNumberFormat()->applyFromArray( array('code' => '# ##0') );	
	
	// Bordure du tableau
	$objWorksheet1->getStyle('A2:F'.$line)->getBorders()->applyFromArray(array(
		'allborders' => array( 
			'style' => PHPExcel_Style_Border::BORDER_THIN, 
			'color' => array( 'rgb' => '808080') 
		)
	));

	// Largueur des cellules
	$objWorksheet1->getColumnDimension('A')->setWidth(45);
	$objWorksheet1->getColumnDimension('B')->setWidth(15);
	$objWorksheet1->getColumnDimension('C')->setWidth(15);
	$objWorksheet1->getColumnDimension('D')->setWidth(15);
	$objWorksheet1->getColumnDimension('E')->setWidth(15);
	$objWorksheet1->getColumnDimension('F')->setWidth(15);

	if( $parent>0 ){
		$line = 1;

		$objWorksheet2 = $objPHPExcel->createSheet();
		$objWorksheet2->setTitle( _("Produits") );

		$rprd = prd_products_get_simple( 0, '', false, $parent );
		if( $rprd && ria_mysql_num_rows($rprd) ){

			// Entête du tableau des produits
			$objWorksheet2->mergeCells( 'A'.$line.':F'.$line );
			$objWorksheet2->setCellValue( 'A'.$line, _('Produits') );
			$line++;
			
			$objWorksheet2->setCellValue( 'A'.$line, _('Référence') );
			$objWorksheet2->setCellValue( 'B'.$line, _('Désignation') );
			$objWorksheet2->setCellValue( 'C'.$line, _('Ventes') );
			$objWorksheet2->setCellValue( 'D'.$line, _('Par commandes') );
			$objWorksheet2->setCellValue( 'E'.$line, _('CA') );
			$objWorksheet2->setCellValue( 'F'.$line, _('CA Total') );
			$line++;
	
			// Style sur l'entête du tableau
			$objWorksheet2->getStyle('A1:F2')->getFont()->setBold(true);
			$objWorksheet2->getStyle('A1:F2')->getFont()->setSize(11);
			$objWorksheet2->getStyle('A1')->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
			$objWorksheet2->getStyle('A1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('000099');
			$objWorksheet2->getStyle('A2:F2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('BBBBBB');
			$objWorksheet2->getStyle('A2:F2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
			
			$total = array( 'orders'=>0, 'by-ord'=>0, 'ca'=>0, 'ca-ord'=>0 );
			while( $prd = ria_mysql_fetch_array($rprd) ){
				$link = '/admin/catalog/product.php?cat='.$parent.'&amp;prd='.$prd['id'];

					$stat = array( 'qte'=>0, 'total_ht'=>0, 'orders'=>0, 'ord_total_ht'=>0 );
				$rstat = stats_bestsellers_get( $date1, $date2, 1, $origin, $gescom, $wst_id, false, $prd['id'], 0, false, 0, 0, $seller_id );
				if( $rstat && ria_mysql_num_rows($rstat) ){
					$stat = ria_mysql_fetch_array( $rstat );
				}

				$objWorksheet2->setCellValue( 'A'.$line, htmlspecialchars($prd['ref']) );
				$objWorksheet2->setCellValue( 'B'.$line, htmlspecialchars($prd['name']) );
				$objWorksheet2->setCellValue( 'C'.$line, number_format($stat['orders'], 0, ',', ' ') );
				$objWorksheet2->setCellValue( 'D'.$line, number_format($stat['orders']>0 ? $stat['qte']/$stat['orders'] : 0, 0, ',', ' ') );
				$objWorksheet2->setCellValue( 'E'.$line, number_format($stat['total_ht'], 2, ',', ' ') );
				$objWorksheet2->setCellValue( 'F'.$line, number_format($stat['ord_total_ht'], 2, ',', ' ') );

				$total['orders'] += $stat['orders'];
				$total['by-ord'] += $stat['orders']>0 ? $stat['qte']/$stat['orders'] : 0;
				$total['ca'] += $stat['total_ht'];
				$total['ca-ord'] += $stat['ord_total_ht'];

				$line ++;
			}

			// Pieds du tableau des produits
			$objWorksheet2->mergeCells( 'A'.$line.':B'.$line );
			$objWorksheet2->setCellValue( 'A'.$line, 'Totaux :' );
			$objWorksheet2->setCellValue( 'C'.$line, number_format($total['orders'], 0, ',', ' ') );
			$objWorksheet2->setCellValue( 'D'.$line, number_format($total['by-ord'], 0, ',', ' ') );
			$objWorksheet2->setCellValue( 'E'.$line, number_format($total['ca'], 2, ',', ' ') );
			$objWorksheet2->setCellValue( 'F'.$line, number_format($total['ca-ord'], 2, ',', ' ') );
		}
		// Applique des formats aux cellules C5:G7
		$objWorksheet2->getStyle('E3:F'.$line)->getNumberFormat()->applyFromArray( array('code' => '# ##0') );	
		
		// Bordure du tableau
		$objWorksheet2->getStyle('A2:F'.$line)->getBorders()->applyFromArray(array(
			'allborders' => array( 
				'style' => PHPExcel_Style_Border::BORDER_THIN, 
				'color' => array( 'rgb' => '808080') 
			)
		));

		// Largueur des cellules
		$objWorksheet2->getColumnDimension('A')->setWidth(20);
		$objWorksheet2->getColumnDimension('B')->setWidth(45);
		$objWorksheet2->getColumnDimension('C')->setWidth(15);
		$objWorksheet2->getColumnDimension('D')->setWidth(15);
		$objWorksheet2->getColumnDimension('E')->setWidth(15);
		$objWorksheet2->getColumnDimension('F')->setWidth(15);

		$objWorksheet2->getStyle('A'.$line.':F'.$line)->getFont()->setBold(true);
		$objWorksheet2->getStyle('A'.$line.':F'.$line)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID  )->getStartColor()->setRGB('BBBBBB');
		$objWorksheet2->getStyle('A'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
		$objWorksheet2->getStyle('C2:F'.$line)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT);
		
		$objPHPExcel->setActiveSheetIndex(0);
	}

	// Redirect output to a client web browser (Excel5)
	header('Content-Type: application/vnd.ms-excel');
	header('Content-Disposition: attachment;filename="'._('meilleures-ventes-categories').'.xls"');
	header('Cache-Control: max-age=0');

	// Ecrit le fichier et le sauvegarde
	$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
	$objWriter->save('php://output');
	exit;
