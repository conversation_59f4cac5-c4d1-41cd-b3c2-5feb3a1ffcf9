<?php
	require_once('categories.inc.php');
	require_once('stats.inc.php');

	print '
		<h3>Commandes et consultations</h3>
		<div id="graph-cat-views-ordered"></div>
	';

	// Paramètre de recherche
	$prd_id = isset($_GET['prd']) ? $_GET['prd'] : 0;
	$cat_id = isset($_GET['cat']) ? $_GET['cat'] : 0;
	
	$ar_prd = array();
	$rprd = prd_products_get_simple( $prd_id, '', false, $cat_id, true, false, false, false, array('childs'=>true) );
	while( $p = ria_mysql_fetch_array($rprd) ){
		$ar_prd[] = $p['id'];
	}

	// Récupération des statistiques
	$cmds = stats_graphs_get_datas( 'product-order', $date1, $date2, array( 'prd' =>$ar_prd) );
	$views = stats_graphs_get_datas( 'product-seen', $date1, $date2, array( 'prd' =>$ar_prd) );
?>
<script>
	$(function () {
		$('#graph-cat-views-ordered').highcharts({
			chart: {
				type: "spline",
				plotBorderWidth: 0,
				animation: false,
				events: {
					load: function (event) {
						var extremes = this.yAxis[0].getExtremes();
						if (extremes.dataMax == 0) {
							this.yAxis[0].setExtremes(0, 5);
						}
					}
				}
			},
			credits: {
				enabled: false
			},
			exporting: {
				filename: 'statistiques-commandes-consultations'
			},
			title: {
				text: '',
				x: -20
			},
			xAxis: {
				categories: [<?php print '\''.implode('\', \'', array_keys($cmds)).'\''; ?>]
			},
			yAxis: {
				title: {
					text: ''
				},
				min: 0,
				plotLines: [{
					value: 0,
					width: 1,
					color: '#808080'
				}]
			},
			legend: {
				layout: 'horizontal',
				align: 'center',
				verticalAlign: 'top',
				borderWidth: 0
			},
			tooltip: {
				shared: true,
				crosshairs: true,
				followTouchMove: true,
				style: {
					fontSize: "13px"
				},
				formatter: function() {
					var str = '<span style="font-size: 12px;">'+ this.x +'</span>';

					$.each(this.points, function(i, point) {
						str += '<br/><span style="color:'+point.series.color+'">'+ point.series.name +'</span>: <b>'+
						point.y+'</b>';
					});

					return str;
				},
			},
			series: [
				{
					name: 'Consultations',
					data: [<?php print implode( ', ', array_values($views) ); ?>],
					color: '#89A54E'
				},
				{
					name: 'Commandes',
					data: [<?php print implode( ', ', array_values($cmds) ); ?>],
					color: '#4572A7'
				}
			]
		});
	});
</script>