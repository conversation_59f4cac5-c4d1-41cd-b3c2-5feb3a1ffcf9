<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AC' => 'Ascension-sziget',
  'AD' => 'Andorra',
  'AE' => 'Egyesült Arab Emírségek',
  'AF' => 'Afganisztán',
  'AG' => 'Antigua és Barbuda',
  'AI' => 'Anguilla',
  'AL' => 'Albánia',
  'AM' => 'Örményország',
  'AO' => 'Angola',
  'AQ' => 'Antarktisz',
  'AR' => 'Argentína',
  'AS' => 'Amerikai Szamoa',
  'AT' => 'Ausztria',
  'AU' => 'Ausztrália',
  'AW' => 'Aruba',
  'AX' => 'Åland-szigetek',
  'AZ' => 'Azerbajdzsán',
  'BA' => 'Bosznia-Hercegovina',
  'BB' => 'Barbados',
  'BD' => 'Banglades',
  'BE' => 'Belgium',
  'BF' => 'Burkina Faso',
  'BG' => 'Bulgária',
  'BH' => 'Bahrein',
  'BI' => 'Burundi',
  'BJ' => 'Benin',
  'BL' => 'Saint-Barthélemy',
  'BM' => 'Bermuda',
  'BN' => 'Brunei',
  'BO' => 'Bolívia',
  'BQ' => 'Holland Karib-térség',
  'BR' => 'Brazília',
  'BS' => 'Bahama-szigetek',
  'BT' => 'Bhután',
  'BW' => 'Botswana',
  'BY' => 'Belarusz',
  'BZ' => 'Belize',
  'CA' => 'Kanada',
  'CC' => 'Kókusz (Keeling)-szigetek',
  'CD' => 'Kongó - Kinshasa',
  'CF' => 'Közép-afrikai Köztársaság',
  'CG' => 'Kongó - Brazzaville',
  'CH' => 'Svájc',
  'CI' => 'Elefántcsontpart',
  'CK' => 'Cook-szigetek',
  'CL' => 'Chile',
  'CM' => 'Kamerun',
  'CN' => 'Kína',
  'CO' => 'Kolumbia',
  'CR' => 'Costa Rica',
  'CU' => 'Kuba',
  'CV' => 'Zöld-foki Köztársaság',
  'CW' => 'Curaçao',
  'CX' => 'Karácsony-sziget',
  'CY' => 'Ciprus',
  'CZ' => 'Csehország',
  'DE' => 'Németország',
  'DG' => 'Diego Garcia',
  'DJ' => 'Dzsibuti',
  'DK' => 'Dánia',
  'DM' => 'Dominika',
  'DO' => 'Dominikai Köztársaság',
  'DZ' => 'Algéria',
  'EA' => 'Ceuta és Melilla',
  'EC' => 'Ecuador',
  'EE' => 'Észtország',
  'EG' => 'Egyiptom',
  'EH' => 'Nyugat-Szahara',
  'ER' => 'Eritrea',
  'ES' => 'Spanyolország',
  'ET' => 'Etiópia',
  'FI' => 'Finnország',
  'FJ' => 'Fidzsi',
  'FK' => 'Falkland-szigetek',
  'FM' => 'Mikronézia',
  'FO' => 'Feröer-szigetek',
  'FR' => 'Franciaország',
  'GA' => 'Gabon',
  'GB' => 'Egyesült Királyság',
  'GD' => 'Grenada',
  'GE' => 'Grúzia',
  'GF' => 'Francia Guyana',
  'GG' => 'Guernsey',
  'GH' => 'Ghána',
  'GI' => 'Gibraltár',
  'GL' => 'Grönland',
  'GM' => 'Gambia',
  'GN' => 'Guinea',
  'GP' => 'Guadeloupe',
  'GQ' => 'Egyenlítői-Guinea',
  'GR' => 'Görögország',
  'GS' => 'Déli-Georgia és Déli-Sandwich-szigetek',
  'GT' => 'Guatemala',
  'GU' => 'Guam',
  'GW' => 'Bissau-Guinea',
  'GY' => 'Guyana',
  'HK' => 'Hongkong KKT',
  'HN' => 'Honduras',
  'HR' => 'Horvátország',
  'HT' => 'Haiti',
  'HU' => 'Magyarország',
  'IC' => 'Kanári-szigetek',
  'ID' => 'Indonézia',
  'IE' => 'Írország',
  'IL' => 'Izrael',
  'IM' => 'Man-sziget',
  'IN' => 'India',
  'IO' => 'Brit Indiai-óceáni Terület',
  'IQ' => 'Irak',
  'IR' => 'Irán',
  'IS' => 'Izland',
  'IT' => 'Olaszország',
  'JE' => 'Jersey',
  'JM' => 'Jamaica',
  'JO' => 'Jordánia',
  'JP' => 'Japán',
  'KE' => 'Kenya',
  'KG' => 'Kirgizisztán',
  'KH' => 'Kambodzsa',
  'KI' => 'Kiribati',
  'KM' => 'Comore-szigetek',
  'KN' => 'Saint Kitts és Nevis',
  'KP' => 'Észak-Korea',
  'KR' => 'Dél-Korea',
  'KW' => 'Kuvait',
  'KY' => 'Kajmán-szigetek',
  'KZ' => 'Kazahsztán',
  'LA' => 'Laosz',
  'LB' => 'Libanon',
  'LC' => 'Saint Lucia',
  'LI' => 'Liechtenstein',
  'LK' => 'Srí Lanka',
  'LR' => 'Libéria',
  'LS' => 'Lesotho',
  'LT' => 'Litvánia',
  'LU' => 'Luxemburg',
  'LV' => 'Lettország',
  'LY' => 'Líbia',
  'MA' => 'Marokkó',
  'MC' => 'Monaco',
  'MD' => 'Moldova',
  'ME' => 'Montenegró',
  'MF' => 'Saint Martin',
  'MG' => 'Madagaszkár',
  'MH' => 'Marshall-szigetek',
  'MK' => 'Macedónia',
  'ML' => 'Mali',
  'MM' => 'Mianmar (Burma)',
  'MN' => 'Mongólia',
  'MO' => 'Makaó KKT',
  'MP' => 'Északi Mariana-szigetek',
  'MQ' => 'Martinique',
  'MR' => 'Mauritánia',
  'MS' => 'Montserrat',
  'MT' => 'Málta',
  'MU' => 'Mauritius',
  'MV' => 'Maldív-szigetek',
  'MW' => 'Malawi',
  'MX' => 'Mexikó',
  'MY' => 'Malajzia',
  'MZ' => 'Mozambik',
  'NA' => 'Namíbia',
  'NC' => 'Új-Kaledónia',
  'NE' => 'Niger',
  'NF' => 'Norfolk-sziget',
  'NG' => 'Nigéria',
  'NI' => 'Nicaragua',
  'NL' => 'Hollandia',
  'NO' => 'Norvégia',
  'NP' => 'Nepál',
  'NR' => 'Nauru',
  'NU' => 'Niue',
  'NZ' => 'Új-Zéland',
  'OM' => 'Omán',
  'PA' => 'Panama',
  'PE' => 'Peru',
  'PF' => 'Francia Polinézia',
  'PG' => 'Pápua Új-Guinea',
  'PH' => 'Fülöp-szigetek',
  'PK' => 'Pakisztán',
  'PL' => 'Lengyelország',
  'PM' => 'Saint-Pierre és Miquelon',
  'PN' => 'Pitcairn-szigetek',
  'PR' => 'Puerto Rico',
  'PS' => 'Palesztin Terület',
  'PT' => 'Portugália',
  'PW' => 'Palau',
  'PY' => 'Paraguay',
  'QA' => 'Katar',
  'RE' => 'Réunion',
  'RO' => 'Románia',
  'RS' => 'Szerbia',
  'RU' => 'Oroszország',
  'RW' => 'Ruanda',
  'SA' => 'Szaúd-Arábia',
  'SB' => 'Salamon-szigetek',
  'SC' => 'Seychelle-szigetek',
  'SD' => 'Szudán',
  'SE' => 'Svédország',
  'SG' => 'Szingapúr',
  'SH' => 'Szent Ilona',
  'SI' => 'Szlovénia',
  'SJ' => 'Svalbard és Jan Mayen',
  'SK' => 'Szlovákia',
  'SL' => 'Sierra Leone',
  'SM' => 'San Marino',
  'SN' => 'Szenegál',
  'SO' => 'Szomália',
  'SR' => 'Suriname',
  'SS' => 'Dél-Szudán',
  'ST' => 'São Tomé és Príncipe',
  'SV' => 'Salvador',
  'SX' => 'Sint Maarten',
  'SY' => 'Szíria',
  'SZ' => 'Szváziföld',
  'TA' => 'Tristan da Cunha',
  'TC' => 'Turks- és Caicos-szigetek',
  'TD' => 'Csád',
  'TF' => 'Francia Déli Területek',
  'TG' => 'Togo',
  'TH' => 'Thaiföld',
  'TJ' => 'Tádzsikisztán',
  'TK' => 'Tokelau',
  'TL' => 'Kelet-Timor',
  'TM' => 'Türkmenisztán',
  'TN' => 'Tunézia',
  'TO' => 'Tonga',
  'TR' => 'Törökország',
  'TT' => 'Trinidad és Tobago',
  'TV' => 'Tuvalu',
  'TW' => 'Tajvan',
  'TZ' => 'Tanzánia',
  'UA' => 'Ukrajna',
  'UG' => 'Uganda',
  'UM' => 'Az USA lakatlan külbirtokai',
  'US' => 'Egyesült Államok',
  'UY' => 'Uruguay',
  'UZ' => 'Üzbegisztán',
  'VA' => 'Vatikán',
  'VC' => 'Saint Vincent és a Grenadine-szigetek',
  'VE' => 'Venezuela',
  'VG' => 'Brit Virgin-szigetek',
  'VI' => 'Amerikai Virgin-szigetek',
  'VN' => 'Vietnam',
  'VU' => 'Vanuatu',
  'WF' => 'Wallis és Futuna',
  'WS' => 'Szamoa',
  'XK' => 'Koszovó',
  'YE' => 'Jemen',
  'YT' => 'Mayotte',
  'ZA' => 'Dél-afrikai Köztársaság',
  'ZM' => 'Zambia',
  'ZW' => 'Zimbabwe',
);
