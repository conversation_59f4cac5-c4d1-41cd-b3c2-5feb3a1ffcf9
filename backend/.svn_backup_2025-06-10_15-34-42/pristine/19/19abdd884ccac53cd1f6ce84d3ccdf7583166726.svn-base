# OrderItem

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**beez_up_order_item_id** | **string** | The BeezUP identifier of the order Item | 
**order_item_order_item_type** | **string** | The order item type of the order item | 
**order_item_merchant_imported_product_id** | **string** | The product id indicated in the catalog importation in BeezUP system for this order item. This property will help you to match the order to the inventory system. | [optional] 
**order_item_merchant_imported_product_id_column_name** | **string** | The column name for the product id indicated in the catalog importation in BeezUP system related to this order item. This property will help you to match the order to the inventory system. | [optional] 
**order_item_merchant_imported_product_url** | **string** | The product url indicated in the catalog importation in BeezUP system related to this order item | [optional] 
**order_item_merchant_product_id_column_name** | **string** | The column name indicate in the mapping for the product id related to the order item | [optional] 
**order_item_beez_up_store_id** | **string** | The store id in the beezup system related to the order item. This property will help you to match the order to the inventory system. | [optional] 
**order_item_item_tax** | **float** | The tax of the order item | [optional] 
**order_item_title** | **string** | The title of the order item | [optional] 
**order_item_image_url** | **string** | The URL of the image of the order item | [optional] 
**order_item_merchant_product_id** | **string** | The merchant product id of the order item | [optional] 
**order_item_market_place_product_id** | **string** | The marketplace product identifier of the order item | [optional] 
**order_item_item_price** | **float** | The price of the order item | [optional] 
**order_item_quantity** | **float** | The quantity of the order item | [optional] 
**order_item_total_price** | **float** | The total price of the order item | [optional] 
**order_item_shipping_price** | **float** | The shipping price of the order item | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


