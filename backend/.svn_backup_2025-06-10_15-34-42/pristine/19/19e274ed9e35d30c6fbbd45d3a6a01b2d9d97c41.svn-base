<?php
/** 
 * \defgroup api-devices-index Appareil
 * \ingroup Yuto  
 * @{	
 *	
 *	Ce fichier permet création ou la modification d'un appareil, il permet aussi de gérer la connexion de l'utilisateur.
 *	
 *	Dans le cas de Salesforce (sso_active=true) on va controler que l'utilisateur et son token son valide auprès de SF
 *	dans le cas contraire on va renvoyer une exception et refuser la connexion et le déclenchement de la synchronisation
 *	Pour Legrand : les paramètres suivant sont obligatoire : 
 *	@param sf_usr_id : Obligatoire identifiant de l'utilisateur dans SF
 *	@param sf_auth_token : Obligatoire token donnée par SF, permet un double controle par riashop de la validité de celui ci
 *	
 *	
 */


//cas de la connexion Salesforce (legrand) avec passage de token pour controlle
if( $config['sso_active'] && isset($_REQUEST['sf_usr_id'], $_REQUEST['sf_auth_token']) && $_REQUEST['sf_usr_id'] != '' && $_REQUEST['sf_auth_token'] != '' && $_REQUEST['sf_usr_id'] != 'null' && $_REQUEST['sf_auth_token'] != 'null' ){

	// vérifier la validité du token.
	// récupére le compte client dans la base ria avec le usr_id
	$rusr = gu_users_get(0, '', '', 0, '', 0, $_REQUEST['sf_usr_id']);
	if( !$rusr || !ria_mysql_num_rows($rusr) ){
		throw new RiaShopException("Compte client introuvable.", "ERROR_DEVICE_NO_ACCOUNT");
	}else{
		$usr = ria_mysql_fetch_assoc($rusr);

		try{
			$ch = curl_init();
            $ch_url = $config['sso_userendpoint'];
            $ch_url .= "?format=json";
            $ch_url .= "&oauth_token=".urlencode($_REQUEST['sf_auth_token']);
			curl_setopt($ch, CURLOPT_URL, $ch_url);
			// curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_HTTPHEADER, array(
				'Content-type: application/json',
				'Accept: */*',
				'Authorization: Bearer '.$_REQUEST['sf_auth_token']
			));
			$response = curl_exec($ch);

			curl_close($ch);
			$obj = json_decode($response);
			if( $obj->user_id == $_REQUEST['sf_usr_id'] ){
				$_REQUEST['email'] = $usr['email'];
				$_REQUEST['password'] = md5($usr['ref']);
			}
		}catch(Exception $e){
			throw new RiaShopException("Validation du token échoué.", "ERROR_DEVICE_INVALID_TOKEN");
		}

	}
}

switch( $method ){
	/** @{@}
	 *
 	 * @{
	 * \page api-devices-index-get Chargement	
	 * 
	 * Cette fonction récupère des informations sur un appareil
	 *
	 *		\code
	 *			GET /devices/
	 *		\endcode
	 *	
	 * @param is_biometrie Facultatif, si true permet de ne pas valider le mot de passe pour la connexion 
	 * @param email Obligatoire, Email du compte utilisateur
	 * @param password Obligatoire, Le mot de passe
	 * @param usr_id Facultatif, Identifiant du compte, permet de faire une vérification voir si le compte à été supprimé ou non
	 * @param device Obligatoire, Identifiant de l'appareil
	 * @param token Obligatoire, Token de connexion de l'appareil
	 * @param reset_account Facultatif, Si true, on va considéré que c'est une réinstallation d'appareil et donc on va forcer les taches de base ( config, restriction à ce recharger)
	 *	
	 *	
	 *	@return json sous la forme d'une liste ayant les éléments sous la forme suivant : 
	 *	\code{.json}
	 *		{
	 *			"token": string, token de connexion, nécessaire pour l'identification sur l'api 
	 *			"usr_id": integer, identifiant de l'utilisateur connecté
	 *			"need_sync_screen": bool, permet de forcer l'affichage de l'écran de synchronisation de yuto (utile dans le cas de grosse mise à jour de data)
	 *			...
	 *			(tous le contenu du retour de la fonction gu_users_get)
	 *		},
	 * 	\endcode 
	 * @}
	 */
	case 'get':
		if( !isset($_REQUEST['email']) ){
			throw new RiaShopException("Veuillez saisir votre email.", "ERROR_DEVICE_NO_EMAIL", "email");
		}else {

			// dans le cas de l'usage de la biometrie alors pas de password mais controle de l'utilisateur
			if( isset($_REQUEST['is_biometrie']) && $_REQUEST['is_biometrie'] ){

				$rusr = gu_users_get(0, $_REQUEST['email'], '', array(PRF_ADMIN,PRF_SELLER));
				if( !$rusr || !ria_mysql_num_rows($rusr) ){
					$content = array('deleted' => true);
					throw new RiaShopException("Votre compte à été supprimé.", "ERROR_DEVICE_DELETED_ACCOUNT");
				}else{
					$usr = ria_mysql_fetch_assoc($rusr);
					gu_users_connect($usr, false);
				}

			}
			// connexion standard avec mdp
			else{
				/*
				 * Côté Yuto, on envoie "password" ou "hashed_password" en paramètre selon la version :
				 *  - Si < 326.4.0, on envoie "password" en paramètre.
				 *  - Si >= 326.4.0, on envoie "hashed_password".
				 * On vérifie donc si "password" est set :
				 * 	- Si non, on vérifie si "hashed_password" est set :
				 * 		- Si oui, on applique le traitement avec les fonctions gu_users_hp_[get | login].
				 * 		- Si non, les deux paramètres sont inexistants, on envoie l'erreur.
				 *  - Si oui, on utilise le traitement habituel.
				 */

				if( !isset($_REQUEST['password']) ){
					if (isset($_REQUEST['hashed_password'])) {
						$rusr = gu_users_hp_get(0, $_REQUEST['email'], '', array(PRF_ADMIN, PRF_SELLER));
						if (!$rusr || !ria_mysql_num_rows($rusr)) {
							throw new RiaShopException("L'adresse email saisie est inconnue, veuillez vérifier.", "ERROR_DEVICE_NO_ACCOUNT", "email");
						}

						if (!gu_users_hp_login($_REQUEST['email'], $_REQUEST['hashed_password'], true, array(PRF_ADMIN, PRF_SELLER))) {
							// Check si le compte n'a pas été supprimé. Dans ce cas, on le notifie à l'appareil, celui-ci devrait vider la base.
							if (isset($_REQUEST['usr_id']) && is_numeric($_REQUEST['usr_id']) && $_REQUEST['usr_id']) {
								$rusr = gu_users_hp_get($_REQUEST['usr_id']);
								if (!$rusr || !ria_mysql_num_rows($rusr)) {
									$content = array('deleted' => true);
									throw new RiaShopException("Votre compte a été supprimé.", "ERROR_DEVICE_DELETED_ACCOUNT");
								}
							}
						}
					} else {
						throw new RiaShopException("Veuillez saisir votre mot de passe.", "ERROR_DEVICE_NO_PASSWORD", "password");
					}
				}else{

					$rusr = gu_users_get(0,  $_REQUEST['email'], '', array(PRF_ADMIN,PRF_SELLER));
					if( !$rusr || !ria_mysql_num_rows($rusr) ){
						throw new RiaShopException("L'adresse email saisie est inconnue, veuillez vérifier.", "ERROR_DEVICE_NO_ACCOUNT", "email");
					}

					if( !gu_users_login( $_REQUEST['email'], $_REQUEST['password'], true, array(PRF_ADMIN,PRF_SELLER) ) ){

						// check si le compte n'a pas été supprimé dans ce cas on le notifi à l'appareil, celui-ci devrait vider la base
						if( isset($_REQUEST['usr_id']) && is_numeric($_REQUEST['usr_id']) && $_REQUEST['usr_id'] ){
							$rusr = gu_users_get($_REQUEST['usr_id']);
							if( !$rusr || !ria_mysql_num_rows($rusr) ){
								$content = array('deleted' => true);
								throw new RiaShopException("Votre compte à été supprimé.", "ERROR_DEVICE_DELETED_ACCOUNT");
							}
						}

						throw new RiaShopException("La connexion a échoué, veuillez vérifier votre mot de passe.", "ERROR_DEVICE_INVALID_PASSWORD");
					}
				}

			}
		}

		$result = true;

		$rdev = dev_devices_get( 0, $_SESSION['usr_id'], $_REQUEST['device']);
		if( !$rdev || !ria_mysql_num_rows($rdev) ){
			// si le token est passé en paramètre alors on le retourne cas particulier ios qui générer des dev_key pas correcte
			$content = array('token'=> isset($_REQUEST['token']) && dev_devices_is_authorized($_REQUEST['token']) ? $_REQUEST['token'] : '');
		}else{
			$dev = ria_mysql_fetch_assoc($rdev);

			// lors de la désinstallations et reinstall il faut forcer la réactualisation des variables
			if( isset($_REQUEST['reset_account']) && $_REQUEST['reset_account']==1){
				foreach( dev_devices_need_sync_tsk() as $tsk ){
					dev_devices_need_sync_add($dev['id'], $tsk);
				}
			}

			// récupère le compte pour envoyer toutes les données à la tablette
			$rusr = gu_users_get($_SESSION['usr_id']);
			if(!$rusr || !ria_mysql_num_rows($rusr)){
				throw new Exception("Utilisateur introuvable");
			}

			$usr = ria_mysql_fetch_assoc($rusr);

			// détermine si l'appareil doit avoir l'écran de synchronisation à sa prochaine reconnexion
			$need_sync_screen = false;
			$rneedsync = dev_devices_need_sync_get( $dev['id'], _DEV_TASK_NEED_SYNC );
			if( $rneedsync && ria_mysql_num_rows($rneedsync)){
				$need_sync_screen = true;
			}

			$content = array_merge( array(
				'token' => md5($config['tnt_id'].$dev['id'].$dev['date_created_en']), 
				'usr_id' => $_SESSION['usr_id'], 
				'need_sync_screen' => $need_sync_screen
			), $usr );
		}

		break;
	// ajout d'un device
	/** @{@}
	 *
 	 *	@{
	 *	 \page api-devices-index-add Ajout
	 *
	 *	 Cette fonction ajoute un appareil
	 *
	 *	 	\code
	 *			POST /devices/
	 *	 	\endcode
	 *	
	 *	 @param email Obligatoire, Email de l'utilisateur
	 *	 @param password Obligatoire, Mot de passe de l'utilisateur
	 * 	 @param device Obligatoire, clé d'un appareil
	 *	 @param version Obligatoire, Numéro de version de l'appareil
	 *	 @param os_version Obligatoire, Numéro de version de l'os
	 *	 @param brand Obligatoire, Marque de l'appareil
	 *	 @param model Obligatoire, Model de l'appareil
	 *	 @param notify_token : Facultatif, clé pour l'envoi de notification
	 *	 @param is_debug : Facultatif, détermine si l'appareil est installer avec une version de dév
	 *	
	 *	
	 *	@return json sous la forme d'une liste ayant les éléments sous la forme suivant : 
	 *	\code{.json}
	 *		{
	 *			"token": string, token de connexion, nécessaire pour l'identification sur l'api 
	 *			"usr_id": integer, identifiant de l'utilisateur connecté
	 *		},
	 * 	\endcode 
	 *	@}
	 */
	case 'add':
		if( !isset($_REQUEST['version']) ){
			throw new RiaShopException("La version de l'application n'est pas renseigné.", null, "version");
		}else if( !isset($_REQUEST['device']) ){
			throw new RiaShopException("Le numéro de l'appareil n'est pas renseigné.", null, "device");
		}else if( !isset($_REQUEST['email']) ){
			throw new RiaShopException("Veuillez saisir votre email.", "ERROR_DEVICE_NO_EMAIL", "email");
		}else if( !isset($_REQUEST['password']) ){
			throw new RiaShopException("Veuillez saisir votre mot de passe.", "ERROR_DEVICE_NO_PASSWORD", "password");
		}else {
			$rusr = gu_users_get(0,  $_REQUEST['email'], '', array(PRF_ADMIN,PRF_SELLER));
			if( !$rusr || !ria_mysql_num_rows($rusr) ){
				throw new RiaShopException("L'adresse email saisie est inconnue, veuillez vérifier.", "ERROR_DEVICE_NO_ACCOUNT");
			}
			if( !gu_users_login($_REQUEST['email'], $_REQUEST['password'], true, array(PRF_ADMIN,PRF_SELLER) ) ){
				throw new RiaShopException("La connexion a échoué, veuillez vérifier votre mot de passe.", "ERROR_DEVICE_INVALID_PASSWORD" );
			}

			if( !isset($_REQUEST['os_version']) ) $_REQUEST['os_version'] = false;
			if( !isset($_REQUEST['model']) ) $_REQUEST['model'] = false;
			if( !isset($_REQUEST['brand']) ) $_REQUEST['brand'] = false;
			if( !isset($_REQUEST['notify_token']) ) $_REQUEST['notify_token'] = false;
			if( !isset($_REQUEST['is_debug']) ) $_REQUEST['is_debug'] = false;

			// ajout du nouvel appareil, le trim est important sur la version parfois ... j'ai un retour à la ligne ??
			if( !($dev_id = dev_devices_add($_SESSION['usr_id'], $_REQUEST['device'], trim($_REQUEST['version']), $_REQUEST['os_version'], $_REQUEST['brand'], $_REQUEST['model'])) ){
				throw new Exception("Une erreur est survenue lors de l'ajout de l'appareil.");
			}

			$rdev = dev_devices_get( $dev_id );
			if( !$rdev || !ria_mysql_num_rows($rdev) ){
				throw new Exception("L'appareil n'est pas présent dans la base");
			}

			$dev = ria_mysql_fetch_assoc($rdev);

			// activation automatique de la tablette dans le cas de la démo apple pour la validation de l'app
			if( $_REQUEST['brand'] == 'Apple' && $_REQUEST['logtoken'] == '924a3c' ){
				dev_devices_activate($dev['id']);
			}

			// dans le cas d'une auth SSO on considére que les devices sont forcement actif, a vérifier sur d'autre installation dans ce cas passer cette activation spé à legrand
			if( $config['sso_active'] || !$config['dev_need_activation']){
				// Récupère le nombre d'appareils maximum autorisés
				$max_devices = dev_subscribtions_get_max();

				// Calcule le nombre d'appareils actuellement actifs
				$total_devices = 0;
				$devices = dev_devices_get( 0, 0, '', -1, '=', false, false, true, true, false, false, false );

				if( !$devices ){
					throw new Exception("Erreur lors de la récupération des devices actif.");
				}

				while( $device = ria_mysql_fetch_assoc($devices) ){
					// Ne prend pas en compte les appareils rattachés à un client super-admin
					if( $device['usr_tnt_id'] == $config['tnt_id'] ){
						$total_devices++;
					}
				}
				if( $max_devices>$total_devices ){
					dev_devices_activate($dev['id']);
				}else{
					throw new RiaShopException("Impossible d'activer une nouvelle licence. Nombre maximum de licence atteint.", "ERROR_DEVICE_NO_MORE_LICENCE");
				}
			}

			// met à jour le token de notification
			dev_devices_set_notify_token($dev['id'], $_REQUEST['notify_token']);

			// met à jour la version version de l'app debug ou non
			dev_devices_set_is_debug($dev['id'], $_REQUEST['is_debug']);

			// récupère le compte pour envoyer toutes les données à la tablette
			$rusr = gu_users_get($_SESSION['usr_id']);
			if(!$rusr || !ria_mysql_num_rows($rusr)){
				throw new Exception("Utilisateur introuvable");
			}

			$usr = ria_mysql_fetch_assoc($rusr);

			$result = true;
			$content = array_merge( array(
				'token' => md5($config['tnt_id'].$dev['id'].$dev['date_created_en']), 
				'usr_id' => $_SESSION['usr_id']
			), $usr );
		}

		break;
	/** @{@}
	* 
 	*	@{
	*	\page api-devices-index-upd Mise à jour 
	*
	*	Cette fonction permet de mettre à jour les informations de base de l'appareil c'est le premier appel fait par yuto lors du lancement de la synchronisation
	*
	*	\code
	*		PUT /devices/
	*	\endcode
	*	
	*	@param dev_id Obligatoire, clé d'un appareil
	*	@param version Obligatoire, Numéro de version de l'appareil
	*	@param os_version : Facultatif, Numéro de version de l'os
	*	@param brand : Facultatif, Marque de l'appareil
	*	@param notify_token : Facultatif, clé pour l'envoi de notification
	*	@param notif_is_active : Facultatif, détermine si le notification sont activé sur l'appareil (push)
	*	@param is_debug : Facultatif, détermine si l'appareil est installer avec une version de dév
	*	@param model : Facultatif, Model de l'appareil
	*	
	*	
	*	@return json sous la forme d'une liste ayant les éléments sous la forme suivant : 
	*	\code{.json}
	*		{
	*			"is_active": bool, détermine si l'appareil est actif ou non, si non alors le processus de synchro yuto sera coupé
	*			"need_sync": liste des taches basique à synchroniser ( config, restrictions, modèles de saisie, ... )
	*				Cette liste de taches vient de la table dev_devices_need_sync qui est rempli par les fonctions du moteur riashop
	*				Par exemple, lors du recacul du cache des restrictions on va mettre une ligne pour demander à tous les appareils de recharger le resultat
	*				ou encore lors d'un ajout de champs avancé on va demander à tous les appareils de recharger le modèles des champs 
	*				(modèles de saisies / catégorie / champs ... )
	*		},
	* 	\endcode 
	*	@}
	*/
	//mise à jour d'un appareil
	case 'upd':

		if( !isset($_REQUEST['version']) ){
			throw new RiaShopException("La version de l'application n'est pas renseigné.", null, "version");
		}elseif( !isset($config['dev_id'] ) ){
			throw new RiaShopException("L'appareil n'est pas authentifié.", null, "dev_id");
		}


		$rdev = dev_devices_get( $config['dev_id'] );
		if( !$rdev || !ria_mysql_num_rows($rdev) ){
			throw new Exception("L'appareil n'est pas présent dans la base");
		}

		if( !isset($_REQUEST['os_version']) ) $_REQUEST['os_version'] = false;
		if( !isset($_REQUEST['model']) ) $_REQUEST['model'] = false;
		if( !isset($_REQUEST['brand']) ) $_REQUEST['brand'] = false;
		if( !isset($_REQUEST['notify_token']) ) $_REQUEST['notify_token'] = false;
		if( !isset($_REQUEST['notif_is_active']) ) $_REQUEST['notif_is_active'] = true; //True par defaut
		if( !isset($_REQUEST['is_debug']) ) $_REQUEST['is_debug'] = false;

		$dev = ria_mysql_fetch_assoc($rdev);
		if( !dev_devices_upd( $config['dev_id'], $dev['usr_id'], $dev['key'], $_REQUEST['version'], $_REQUEST['os_version'], $_REQUEST['brand'], $_REQUEST['model'] ) ){
			throw new Exception("La mise à jour de l'appareil a échoué.");
		}
		else{
			$content = array(
				'is_active' => $dev['is_active'], 
				'need_sync' => array()
			);

			// met à jour le token de notification
			dev_devices_set_notify_token($config['dev_id'], $_REQUEST['notify_token']);

			// met à jour le status des notifications
			dev_devices_set_notif_is_active($config['dev_id'], $_REQUEST['notif_is_active']);

			// si nous sommes passé ici c'est que le device n'est plus en attente de sync
			dev_devices_set_is_waiting_sync($config['dev_id'], false);

			// met à jour la version version de l'app debug ou non
			dev_devices_set_is_debug($config['dev_id'], $_REQUEST['is_debug']);

			// récupère les potentiels mise à jour nécessaire de la config et des modèles de champs par exemple
			$rneed_sync = dev_devices_need_sync_get($dev['id']);
			if( $rneed_sync && ria_mysql_num_rows($rneed_sync) ){
				while( $need_sync = ria_mysql_fetch_assoc($rneed_sync) ){
					$content['need_sync'][] = $need_sync['tsk_id'];
				}
			}

			$result = true;
		}

		break;
}

///@}