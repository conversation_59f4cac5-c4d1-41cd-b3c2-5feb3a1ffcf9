<?php
// \cond onlyria
/**
 *	\defgroup orders_unsmask Commandes masquées 
 *	\ingroup orders 
 *  	@{	
*/
	switch( $method ){
		/** @{@}
 		 *	@{
		 *
		 *   \page api-orders-unmask-get Chargement
         *
         *    Cette fonction récupére une commande masquée.
         *
         *       \code
         *           GET /orders/unmask/
         *       \endcode
         *
         *    @param $ord Obligatoire, Identifiant de la commande
         *   
         *    @return json avec les propriétées suivantes :
	     *      \code{.json}
	     *           {
         *           "user" : identifiant du client ayant passé la commande
         *           "id" : Identifiant de la commande
         *           "ref" : Réf<PERSON><PERSON><PERSON> de la commande (identifiant complété par des 0, 8 caractères)
         *           "piece" : numéro de pièce dans la gestion commerciale
         *           "date" : date/heure de la commande
         *           "date_en" : date au format US
         *           "state_id" : identifiant du statut de commande
         *           "state_name" : libellé du statut de commande
         *           "products" : nombre d'articles contenus dans la commande
         *           "total_ht" : montant total de la commande, hors taxes
         *           "total_ttc" : montant total de la commande, ttc
         *           "adr_invoices" : identifiant de l'adresse de facturation
         *           "adr_delivery" : identifiant de l'adresse de livraison
         *           "srv_id" : identifiant du service de livraison demandé par le client
         *           "str_id" : identifiant du magasin dans lequel le client souhaite être livré
         *           "dlv-notes" : consignes de livraison
         *           "pmt_id" : identifiant d'un code promotion appliqué à la commande
         *           "seller_id" : identifiant du représentant ayant enregistré la commande
         *           "comments" : commentaires du représentant ayant enregistré la commande
         *           "cnt_id" : identifiant de la commande dans le moteur de recherche
         *           "ord_livr" : date de livraison prévue de la commande
         *           "nsync" : détermine si la commande nécessite une synchronisation avec la gestion commerciale
         *           "need_sync" : détermine si la commande nécessite une synchronisation avec la gestion commerciale
         *           "total_ht_delivered" : montant total des articles non livrés de la commande
         *           "dps_id" : dépôt de livraison de la commande (commandes fournisseurs)
         *           "age" : Age de la commande
         *           "relanced" : Détermine si le client de la commande doit être relancé pour paiement complémentaire (Atos / Bigship uniquement)
         *           "alert-livr" : Détermine si une alerte email pour les produits en rupture de la commande doit être envoyée (Bigship uniquement)
         *           "rly_id" : Identifiant du point-relais de livraison
         *           "parent" : Identifiant du compte parent, si la commande est liée à un compte enfant (à ignorer si la variable de configuration parent_is_order_holder n'est pas activée)
         *           "wst_id" : Identifiant du site pour lequel la commande a été passée
         *           "date_archived" : Date d'archivation
         *           "date_modified" : Date de modification
         *           "state_sage" : non utilisé
         *           "opt-gift" : option cadeau activée oui / non
         *           "opt-gift-message" : message de l'option cadeau
         *           "pay_id" : identifiant du moyen de paiement de la commande
         *           "card_id" : identifiant du type de CB de la commande
         *           "reliquats" : est un reliquat oui / non
         *           "ord_livr_fr" : date de livraison prévue au format FR
         *           "date_modified_fr" : date de dernière modification au format FR
         *           "masked" : commande masquée oui / non
         *           "parent_id" : identifiant de la commande parent
         *	         },
	     *      \endcode
		 *	@}
		*/
		case 'get':

			if( !isset($_REQUEST['ord']) ){
				throw new Exception( "Paramètre invalide.");
			}

			$rord = ord_orders_get_masked($_REQUEST['ord']);
			if( $rord && ria_mysql_num_rows($rord) ){
				$result = true;
				$content = ria_mysql_fetch_assoc($rord);
			}

			break;
		/** @{@}
 		 * @{
		 * \page api-orders-unmask-upd Mise à jour 
		 *
		 * Cette fonction masque/démasque une commande
		 *
		 *		\code
		 *			GET /orders/unmask/
		 *		\endcode
		 *	
		 * @param $ord Obligatoire, Identifiant de la commande
		 * @param $reverse Facultatif, 1 pour masquer, 0 pour démasquer la commande
         * @param $nocheck Facultatif, 1 pour ne pas reacalculer les totaux de la commande
         *
         * @return true si la mise à jour s'est déroulée avec succès 
		 * @}
		*/
		case 'upd':

			if( !isset($_REQUEST['ord']) ){
				throw new Exception( "Paramètre invalide.");
			}

			// log les données envoyé par la tablette
			api_log('Commandes '.$config['tnt_id'].' FDV '.$_REQUEST['ord'].' - unmask : '.print_r($_REQUEST,true), 'debug');

			if( ord_orders_unmask($_REQUEST['ord'], isset($_REQUEST['reverse']) && $_REQUEST['reverse'] == 1, isset($_REQUEST['nocheck']) && $_REQUEST['nocheck'] == 1) ){
				$result = true;
			}

			break;
	}
///@}
// \endcond 