<?php

namespace SAML2\Utilities;

/**
 * Collection of Utility functions specifically for certificates
 */
class Certificate
{
    /**
     * The pattern that the contents of a certificate should adhere to
     */
    const CERTIFICATE_PATTERN = '/^-----BEGIN CERTIFICATE-----([^-]*)^-----<PERSON>ND CERTIFICATE-----/m';


    /**
     * @param  $certificate
     *
     * @return bool
     */
    public static function hasValidStructure($certificate)
    {
        return !!preg_match(self::CERTIFICATE_PATTERN, $certificate);
    }


    /**
     * @param string $X509CertificateContents
     *
     * @return string
     */
    public static function convertToCertificate($X509CertificateContents)
    {
        return "-----B<PERSON>IN CERTIFICATE-----\n"
                . chunk_split($X509CertificateContents, 64)
                . "-----<PERSON>ND CERTIFICATE-----\n";
    }
}
