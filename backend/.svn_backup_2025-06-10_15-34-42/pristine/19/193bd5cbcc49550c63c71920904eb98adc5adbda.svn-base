<?php 
	/** \file refresh-newsletters-mailjet.php
	 *
	 * 	Ce script est destiné à mettre à jour les inscriptions à la newsletter présentes dans RiaShop sur MailJet.
	 *	L'identifiant du tenant peut-être donné en premier paramètre à ce script.
	 *
	 *	Si une inscription ou une désinscription intervient après la dernière mise à jour de MailJet, elle sera envoyée sur MailJet
	 *	Si une désincription a été enregistré sur MailJet, elle sera enregistrée sur RiaShop (si elle a lieu après la dernière mise à jour MailJet)
	 */

	if (!isset($ar_params)) {
		die("L'exécution de ce script nécessite l'appel de execute-script.php." . PHP_EOL);
	}

	require_once('MailJet.inc.php');
	require_once('newsletter.inc.php');

	foreach( $configs as $config ){
		$r_nlr_mailjet = nlr_mailjet_get();
		if( !$r_nlr_mailjet || !ria_mysql_num_rows($r_nlr_mailjet) ){
			continue;
		}

		$nlr_mailjet = ria_mysql_fetch_assoc( $r_nlr_mailjet );

		$ar_cat_newsletter = array();
		if( trim($nlr_mailjet['cat_id']) != '' ){
			$ar_cat_newsletter = explode( ', ', $nlr_mailjet['cat_id'] );
		}
		
		$mailjet = new MailJet( $nlr_mailjet['api_key'], $nlr_mailjet['secret_key'] );

		$r_nlr_cat = nlr_categorie_get();
		if( !$r_nlr_cat || !ria_mysql_num_rows($r_nlr_cat) ){
			continue;
		}

		// Charge un tableau de tous les contacts sur MailJet pour ne pas les créer deux fois
		$ar_mailjet_contact = array();

		while( $nlr_cat = ria_mysql_fetch_assoc($r_nlr_cat) ){
			if( sizeof($ar_cat_newsletter) && !in_array($nlr_cat['id'], $ar_cat_newsletter) ){
				continue;
			}

			// Création de la catégorie de newsletter sur MailJet (Préfixé par "RiaShop - ") si elle n'existe pas déjà
			if( !is_numeric($nlr_cat['mailjet_id']) || $nlr_cat['mailjet_id']<=0 ){
				$mailjet_id = $mailjet->AddList('RiaShop - '.$nlr_cat['cat']);

				if( !is_numeric($mailjet_id) || $mailjet_id<=0 ){
					error_log( '['.$config['tnt_id'].'] Erreur lors de la création de la liste de contact "RiaShop"' );
					break;
				}

				if( !nlr_mailjet_set_cat_id($nlr_cat['id'], $mailjet_id) ){
					error_log( '['.$config['tnt_id'].'] Erreur lors de la mise à jour de l\'identifiant MailJet pour la catéogrie de newsletter : '.$nlr_cat['id'].', '.$mailjet_id );
					break;
				}

				$nlr_cat['mailjet_id'] = $mailjet_id;
			}

			// Récupère les inscripts à la newsletter
			$r_subscriber = nlr_subscribers_get( NEWSLETTER_TYPE_ALL, 0, '', $nlr_cat['id'], 0, null, null, 0, 0, array('inscript-requested'=>'desc') );
			if( !$r_subscriber || !ria_mysql_num_rows($r_subscriber) ){
				continue;
			}

			// Pour chaque inscription validée
			$ar_email_unique = array();
			while( $subscriber = ria_mysql_fetch_assoc($r_subscriber) ){
				if( trim($subscriber['inscript-confirmed']) == '' ){
					continue;
				}

				if( in_array($subscriber['email'], $ar_email_unique) ){
					continue;
				}

				$ar_email_unique[] = $subscriber['email'];

				// Création du contact s'il n'existe pas déjà sur MailJet
				if( !is_numeric($subscriber['mailjet_id']) || $subscriber['mailjet_id'] <= 0 ){
					$mailjet_id = $mailjet->existsContact( $subscriber['email'] );

					if( !is_numeric($mailjet_id) || $mailjet_id <= 0 ){
						$mailjet_id = $mailjet->addContact( $subscriber['email'] );
					}

					if( !is_numeric($mailjet_id) || $mailjet_id<=0 ){
					    continue;
					}

					if( !nlr_subscribers_update_mailjet_id($subscriber['email'], $mailjet_id) ){
						continue;
					}

					$ar_mailjet_contact[ $subscriber['email'] ] = $mailjet_id;
				}

				if( trim($nlr_mailjet['last_update']) == '' ){
					$nlr_mailjet['last_update'] = '1970-01-01 00:00:00';
				}

				// Mise à jour de MailJet si l'inscription / la désinscription est intervenue après la dernière mise à jour de MailJet
				if( strtotime($nlr_mailjet['last_update']) < strtotime($subscriber['inscript-confirmed-en']) || strtotime($nlr_mailjet['last_update']) < strtotime($subscriber['uninscript-confirmed-en']) ){
					if( trim($subscriber['uninscript-confirmed']) != '' ){
						if( !$mailjet->unsubscribedContactInList($subscriber['email'], $nlr_cat['mailjet_id']) ){
							continue;
						}
					}else{
						if( !$mailjet->addContactInList($subscriber['email'], $nlr_cat['mailjet_id']) ){
							continue;
						}
					}
				}
			}

			// Gestion des désinscriptions faites sur MailJet
			$r_subscriber = nlr_subscribers_get(NEWSLETTER_TYPE_ALL, 0, '', $nlr_cat['id']);
			if ($r_subscriber) {
				while ($subscriber = ria_mysql_fetch_assoc($r_subscriber)) {
					if (trim($subscriber['uninscript-confirmed-en']) != '') {
						continue;
					}

					if (trim($subscriber['mailjet_id']) == '') {
						continue;
					}

					$data = $mailjet->getContactAllLists($subscriber['mailjet_id']);
					if (is_array($data) && !in_array($nlr_cat['mailjet_id'], $data)) {
						if (!nlr_subscribers_del($subscriber['email'], $nlr_cat['id'], 0, true)) {
							error_log( '['.$config['tnt_id'].'] Erreur lors de la désinscription à la newsletter : '.$subscriber['email'].', '.$nlr_cat['id'] );
						}
					}
				}
			}
		}

		// Mise à jour de la date de dernière mise à jour MailJet
		if( !nlr_mailjet_set_last_update(date('Y-m-d H:i:s')) ){
			error_log( '['.$config['tnt_id'].'] Erreur lors de la mise à jour de la date de dernière mise à jour MailJet : '.date('Y-m-d H:i:s') );
		}
	}