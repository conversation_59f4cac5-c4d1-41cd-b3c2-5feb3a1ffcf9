<?php

	/**	\file check-file-size.php
	 * 	Cette page est utilisée en ajax pour contrôler que la taille du fichier à importer est compatible avec nos restrictions.
	 * 	Aujourd'hui un document ne peut pas exéder 64 Mo (cf $upload_mb)
	 */

	// Vérifie que l'utilisateur en cours à le droit d'importer des données
	gu_if_authorized_else_403('_RGH_ADMIN_TOOL_IMPORT_ADD');
	
	$result = false;
	$content = null;
	$message = '';
	if (isset($_GET['fileSize']) && is_numeric($_GET['fileSize'])) {
		$max_upload = (int)(ini_get('upload_max_filesize'));
		$max_post = (int)(ini_get('post_max_size'));
		$memory_limit = (int)(ini_get('memory_limit'));
		$upload_mb = 64;

		if ($_GET['fileSize'] > $upload_mb) {
			$result = true;
			$message = str_replace("#param[taille]#", $upload_mb, _("Vous venez de choisir un fichier trop volumineux, celui-ci ne peux excéder #param[taille]# Mo."));
			$content = array(
				'toBig' => true
			);
		}else{
			$result = true;
			$content = array(
				'toBig' => false
			);
		}
	}


	header('Content-Type: application/json');
	echo json_encode(array(
		'result' => $result,
		'time' => date('Y-m-d H:i:s'),
		'message' => $message,
		'content' => $content,
	));