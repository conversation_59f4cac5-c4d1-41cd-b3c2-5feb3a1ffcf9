<?php

	/**	\file search-redirections.php
	 * 
	 * 	Ce fichier est appelé en Ajax et retourne la liste des redirections qui correspondent aux filtres passés en argument :
	 * 	- wst : Facultatif, identififiant d'un website sur lequel filtrer le résultat
	 *  - lng : Facultatif, code langue sur lequel filtrer le résultat
	 *  - term : Obligatoire, terme de recherche
	 * 
	 * 	L'utilisateur doit disposer du droit d'accès _RGH_ADMIN_CONFIG_REDIRECTION
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_REDIRECTION');

	require_once('rewrite.inc.php');

	header('Content-type: text/json');
	header('Content-type: application/json');
	
	$wst = isset($_GET['wst']) ? $_GET['wst'] : $config['wst_id'];
	$lng = isset($_GET['lng']) ? $_GET['lng'] : $config['i18n_lng'];

	if( !isset($_GET['term']) || !trim($_GET['term']) ){
		exit;
	}

	$ar_types = array();
	$rtype = search_content_types_get();
	if( $rtype ){
		while( $type = ria_mysql_fetch_array($rtype) ){
			$ar_type[ $type['code'] ] = $type['name'];
		}
	}
	
	$rsearch = search3( 1, $_GET['term'], 1, 10, true, false, 4, array('prd', 'prd-cat','cms', 'news', 'faq-cat', 'faq-qst', 'cgv', 'dlv-str') );
	
	$result = array();
	if( $rsearch && ria_mysql_num_rows($rsearch) ){
		$tmp = '';
		while( $res = ria_mysql_fetch_array($rsearch) ){
			$result[] = $res['url'].' | '.$res['name'].' '.( isset($ar_type[ $res['type_code'] ]) ? '('.$ar_type[ $res['type_code'] ].')' : '' );
		}
	}
	
	print json_encode($result);
