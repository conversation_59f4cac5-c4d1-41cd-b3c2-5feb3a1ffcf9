<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AD' => 'ⴰⵏⴷⵓⵔⴰ',
  'AE' => 'ⵍⵉⵎⴰⵔⴰⵜ',
  'AF' => 'ⴰⴼⵖⴰⵏⵉⵙⵜⴰⵏ',
  'AG' => 'ⴰⵏⵜⵉⴳⴰ ⴷ ⴱⵔⴱⵓⴷⴰ',
  'AI' => 'ⴰⵏⴳⵉⵍⴰ',
  'AL' => 'ⴰⵍⴱⴰⵏⵢⴰ',
  'AM' => 'ⴰⵔⵎⵉⵏⵢⴰ',
  'AO' => 'ⴰⵏⴳⵓⵍⴰ',
  'AR' => 'ⴰⵔⵊⴰⵏⵜⵉⵏ',
  'AS' => 'ⵙⴰⵎⵡⴰ ⵜⴰⵎⵉⵔⵉⴽⴰⵏⵉⵜ',
  'AT' => 'ⵏⵏⵎⵙⴰ',
  'AU' => 'ⵓⵙⵜⵔⴰⵍⵢⴰ',
  'AW' => 'ⴰⵔⵓⴱⴰ',
  'AZ' => 'ⴰⴷⵔⴰⴱⵉⵊⴰⵏ',
  'BA' => 'ⴱⵓⵙⵏⴰ ⴷ ⵀⵉⵔⵙⵉⴽ',
  'BB' => 'ⴱⴰⵔⴱⴰⴷ',
  'BD' => 'ⴱⴰⵏⴳⵍⴰⴷⵉⵛ',
  'BE' => 'ⴱⵍⵊⵉⴽⴰ',
  'BF' => 'ⴱⵓⵔⴽⵉⵏⴰ ⴼⴰⵙⵓ',
  'BG' => 'ⴱⵍⵖⴰⵔⵢⴰ',
  'BH' => 'ⴱⵃⵔⴰⵢⵏ',
  'BI' => 'ⴱⵓⵔⵓⵏⴷⵉ',
  'BJ' => 'ⴱⵉⵏⵉⵏ',
  'BM' => 'ⴱⵔⵎⵓⴷⴰ',
  'BN' => 'ⴱⵔⵓⵏⵉ',
  'BO' => 'ⴱⵓⵍⵉⴼⵢⴰ',
  'BR' => 'ⴱⵔⴰⵣⵉⵍ',
  'BS' => 'ⴱⴰⵀⴰⵎⴰⵙ',
  'BT' => 'ⴱⵀⵓⵜⴰⵏ',
  'BW' => 'ⴱⵓⵜⵙⵡⴰⵏⴰ',
  'BY' => 'ⴱⵉⵍⴰⵔⵓⵙⵢⴰ',
  'BZ' => 'ⴱⵉⵍⵉⵣ',
  'CA' => 'ⴽⴰⵏⴰⴷⴰ',
  'CD' => 'ⵜⴰⴳⴷⵓⴷⴰⵏⵜ ⵜⴰⴷⵉⵎⵓⵇⵔⴰⵜⵉⵜ ⵏ ⴽⵓⵏⴳⵓ',
  'CF' => 'ⵜⴰⴳⴷⵓⴷⴰⵏⵜ ⵜⴰⵏⴰⵎⵎⴰⵙⵜ ⵏ ⵉⴼⵔⵉⵇⵢⴰ',
  'CG' => 'ⴽⵓⵏⴳⵓ',
  'CH' => 'ⵙⵡⵉⵙⵔⴰ',
  'CI' => 'ⴽⵓⵜ ⴷⵉⴼⵡⴰⵔ',
  'CK' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵏ ⴽⵓⴽ',
  'CL' => 'ⵛⵛⵉⵍⵉ',
  'CM' => 'ⴽⴰⵎⵉⵔⵓⵏ',
  'CN' => 'ⵛⵛⵉⵏⵡⴰ',
  'CO' => 'ⴽⵓⵍⵓⵎⴱⵢⴰ',
  'CR' => 'ⴽⵓⵙⵜⴰ ⵔⵉⴽⴰ',
  'CU' => 'ⴽⵓⴱⴰ',
  'CV' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵏ ⴽⴰⴱⴱⵉⵔⴷⵉ',
  'CY' => 'ⵇⵓⴱⵔⵓⵙ',
  'CZ' => 'ⵜⴰⴳⴷⵓⴷⴰⵏⵜ ⵜⴰⵜⵛⵉⴽⵉⵜ',
  'DE' => 'ⴰⵍⵎⴰⵏⵢⴰ',
  'DJ' => 'ⴷⵊⵉⴱⵓⵜⵉ',
  'DK' => 'ⴷⴰⵏⵎⴰⵔⴽ',
  'DM' => 'ⴷⵓⵎⵉⵏⵉⴽ',
  'DO' => 'ⵜⴰⴳⴷⵓⴷⴰⵏⵜ ⵜⴰⴷⵓⵎⵉⵏⵉⴽⵜ',
  'DZ' => 'ⴷⵣⴰⵢⵔ',
  'EC' => 'ⵉⴽⵡⴰⴷⵓⵔ',
  'EE' => 'ⵉⵙⵜⵓⵏⵢⴰ',
  'EG' => 'ⵎⵉⵚⵕ',
  'ER' => 'ⵉⵔⵉⵜⵉⵔⵢⴰ',
  'ES' => 'ⵙⴱⴰⵏⵢⴰ',
  'ET' => 'ⵉⵜⵢⵓⴱⵢⴰ',
  'FI' => 'ⴼⵉⵍⵍⴰⵏⴷⴰ',
  'FJ' => 'ⴼⵉⴷⵊⵉ',
  'FK' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵏ ⵎⴰⵍⴰⵡⵉ',
  'FM' => 'ⵎⵉⴽⵔⵓⵏⵉⵣⵢⴰ',
  'FR' => 'ⴼⵔⴰⵏⵙⴰ',
  'GA' => 'ⴳⴰⴱⵓⵏ',
  'GB' => 'ⵜⴰⴳⵍⴷⵉⵜ ⵉⵎⵓⵏⵏ',
  'GD' => 'ⵖⵔⵏⴰⵟⴰ',
  'GE' => 'ⵊⵓⵔⵊⵢⴰ',
  'GF' => 'ⴳⵡⵉⵢⴰⵏ ⵜⴰⴼⵔⴰⵏⵙⵉⵙⵜ',
  'GH' => 'ⵖⴰⵏⴰ',
  'GI' => 'ⴰⴷⵔⴰⵔ ⵏ ⵟⴰⵕⵉⵇ',
  'GL' => 'ⴳⵔⵉⵍⴰⵏⴷ',
  'GM' => 'ⴳⴰⵎⴱⵢⴰ',
  'GN' => 'ⵖⵉⵏⵢⴰ',
  'GP' => 'ⴳⵡⴰⴷⴰⵍⵓⴱ',
  'GQ' => 'ⵖⵉⵏⵢⴰ ⵏ ⵉⴽⵡⴰⴷⵓⵔ',
  'GR' => 'ⵍⵢⵓⵏⴰⵏ',
  'GT' => 'ⴳⵡⴰⵜⵉⵎⴰⵍⴰ',
  'GU' => 'ⴳⵡⴰⵎ',
  'GW' => 'ⵖⵉⵏⵢⴰ ⴱⵉⵙⴰⵡ',
  'GY' => 'ⴳⵡⵉⵢⴰⵏⴰ',
  'HN' => 'ⵀⵓⵏⴷⵓⵔⴰⵙ',
  'HR' => 'ⴽⵔⵡⴰⵜⵢⴰ',
  'HT' => 'ⵀⴰⵢⵜⵉ',
  'HU' => 'ⵀⵏⵖⴰⵔⵢⴰ',
  'ID' => 'ⴰⵏⴷⵓⵏⵉⵙⵢⴰ',
  'IE' => 'ⵉⵔⵍⴰⵏⴷⴰ',
  'IL' => 'ⵉⵙⵔⴰⵢⵉⵍ',
  'IN' => 'ⵍⵀⵉⵏⴷ',
  'IO' => 'ⵜⴰⵎⵏⴰⴹⵜ ⵜⴰⵏⴳⵍⵉⵣⵉⵜ ⵏ ⵓⴳⴰⵔⵓ ⴰⵀⵉⵏⴷⵉ',
  'IQ' => 'ⵍⵄⵉⵔⴰⵇ',
  'IR' => 'ⵉⵔⴰⵏ',
  'IS' => 'ⵉⵙⵍⴰⵏⴷ',
  'IT' => 'ⵉⵟⴰⵍⵢⴰ',
  'JM' => 'ⵊⴰⵎⴰⵢⴽⴰ',
  'JO' => 'ⵍⵓⵔⴷⵓⵏ',
  'JP' => 'ⵍⵢⴰⴱⴰⵏ',
  'KE' => 'ⴽⵉⵏⵢⴰ',
  'KG' => 'ⴽⵉⵔⵖⵉⵣⵉⵙⵜⴰⵏ',
  'KH' => 'ⴽⴰⵎⴱⵓⴷⵢⴰ',
  'KI' => 'ⴽⵉⵔⵉⴱⴰⵜⵉ',
  'KM' => 'ⵇⵓⵎⵓⵔ',
  'KN' => 'ⵙⴰⵏⴽⵔⵉⵙ ⴷ ⵏⵉⴼⵉⵙ',
  'KP' => 'ⴽⵓⵔⵢⴰ ⵏ ⵉⵥⵥⵍⵎⴹ',
  'KR' => 'ⴽⵓⵔⵢⴰ ⵏ ⵉⴼⴼⵓⵙ',
  'KW' => 'ⵍⴽⵡⵉⵜ',
  'KY' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵏ ⴽⴰⵢⵎⴰⵏ',
  'KZ' => 'ⴽⴰⵣⴰⵅⵙⵜⴰⵏ',
  'LA' => 'ⵍⴰⵡⵙ',
  'LB' => 'ⵍⵓⴱⵏⴰⵏ',
  'LC' => 'ⵙⴰⵏⵜⵍⵓⵙⵉ',
  'LI' => 'ⵍⵉⴽⵉⵏⵛⵜⴰⵢⵏ',
  'LK' => 'ⵙⵔⵉⵍⴰⵏⴽⴰ',
  'LR' => 'ⵍⵉⴱⵉⵔⵢⴰ',
  'LS' => 'ⵍⵉⵚⵓⵟⵓ',
  'LT' => 'ⵍⵉⵜⵡⴰⵏⵢⴰ',
  'LU' => 'ⵍⵓⴽⵙⴰⵏⴱⵓⵔⴳ',
  'LV' => 'ⵍⴰⵜⴼⵢⴰ',
  'LY' => 'ⵍⵉⴱⵢⴰ',
  'MA' => 'ⵍⵎⵖⵔⵉⴱ',
  'MC' => 'ⵎⵓⵏⴰⴽⵓ',
  'MD' => 'ⵎⵓⵍⴷⵓⴼⵢⴰ',
  'MG' => 'ⵎⴰⴷⴰⵖⴰⵛⵇⴰⵔ',
  'MH' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵏ ⵎⴰⵔⵛⴰⵍ',
  'MK' => 'ⵎⴰⵙⵉⴷⵓⵏⵢⴰ',
  'ML' => 'ⵎⴰⵍⵉ',
  'MM' => 'ⵎⵢⴰⵏⵎⴰⵔ',
  'MN' => 'ⵎⵏⵖⵓⵍⵢⴰ',
  'MP' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵏ ⵎⴰⵔⵢⴰⵏ ⵏ ⵉⵥⵥⵍⵎⴹ',
  'MQ' => 'ⵎⴰⵔⵜⵉⵏⵉⴽ',
  'MR' => 'ⵎⵓⵕⵉⵟⴰⵏⵢⴰ',
  'MS' => 'ⵎⵓⵏⵙⵉⵔⴰⵜ',
  'MT' => 'ⵎⴰⵍⵟⴰ',
  'MU' => 'ⵎⵓⵔⵉⵙ',
  'MV' => 'ⵎⴰⵍⴷⵉⴼ',
  'MW' => 'ⵎⴰⵍⴰⵡⵉ',
  'MX' => 'ⵎⵉⴽⵙⵉⴽ',
  'MY' => 'ⵎⴰⵍⵉⵣⵢⴰ',
  'MZ' => 'ⵎⵓⵣⵏⴱⵉⵇ',
  'NA' => 'ⵏⴰⵎⵉⴱⵢⴰ',
  'NC' => 'ⴽⴰⵍⵉⴷⵓⵏⵢⴰ ⵜⴰⵎⴰⵢⵏⵓⵜ',
  'NE' => 'ⵏⵏⵉⵊⵉⵔ',
  'NF' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵏ ⵏⵓⵔⴼⵓⵍⴽ',
  'NG' => 'ⵏⵉⵊⵉⵔⵢⴰ',
  'NI' => 'ⵏⵉⴽⴰⵔⴰⴳⵡⴰ',
  'NL' => 'ⵀⵓⵍⴰⵏⴷⴰ',
  'NO' => 'ⵏⵏⵔⵡⵉⵊ',
  'NP' => 'ⵏⵉⴱⴰⵍ',
  'NR' => 'ⵏⴰⵡⵔⵓ',
  'NU' => 'ⵏⵉⵡⵉ',
  'NZ' => 'ⵏⵢⵓⵣⵉⵍⴰⵏⴷⴰ',
  'OM' => 'ⵄⵓⵎⴰⵏ',
  'PA' => 'ⴱⴰⵏⴰⵎⴰ',
  'PE' => 'ⴱⵉⵔⵓ',
  'PF' => 'ⴱⵓⵍⵉⵏⵉⵣⵢⴰ ⵜⴰⴼⵔⴰⵏⵙⵉⵙⵜ',
  'PG' => 'ⴱⴰⴱⵡⴰ ⵖⵉⵏⵢⴰ ⵜⴰⵎⴰⵢⵏⵓⵜ',
  'PH' => 'ⴼⵉⵍⵉⴱⴱⵉⵏ',
  'PK' => 'ⴱⴰⴽⵉⵙⵜⴰⵏ',
  'PL' => 'ⴱⵓⵍⵓⵏⵢⴰ',
  'PM' => 'ⵙⴰⵏⴱⵢⵉⵔ ⴷ ⵎⵉⴽⵍⵓⵏ',
  'PN' => 'ⴱⵉⵜⴽⴰⵢⵔⵏ',
  'PR' => 'ⴱⵓⵔⵜⵓ ⵔⵉⴽⵓ',
  'PS' => 'ⴰⴳⵎⵎⴰⴹ ⵏ ⵜⴰⴳⵓⵜ ⴷ ⵖⵣⵣⴰ',
  'PT' => 'ⴱⵕⵟⵇⵉⵣ',
  'PW' => 'ⴱⴰⵍⴰⵡ',
  'PY' => 'ⴱⴰⵔⴰⴳⵡⴰⵢ',
  'QA' => 'ⵇⴰⵜⴰⵔ',
  'RE' => 'ⵔⵉⵢⵓⵏⵢⵓⵏ',
  'RO' => 'ⵔⵓⵎⴰⵏⵢⴰ',
  'RU' => 'ⵔⵓⵙⵢⴰ',
  'RW' => 'ⵔⵡⴰⵏⴷⴰ',
  'SA' => 'ⵙⵙⴰⵄⵓⴷⵉⵢⴰ',
  'SB' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵏ ⵙⴰⵍⵓⵎⴰⵏ',
  'SC' => 'ⵙⵙⵉⵛⵉⵍ',
  'SD' => 'ⵙⵙⵓⴷⴰⵏ',
  'SE' => 'ⵙⵙⵡⵉⴷ',
  'SG' => 'ⵙⵏⵖⴰⴼⵓⵔⴰ',
  'SH' => 'ⵙⴰⵏⵜⵉⵍⵉⵏ',
  'SI' => 'ⵙⵍⵓⴼⵉⵏⵢⴰ',
  'SK' => 'ⵙⵍⵓⴼⴰⴽⵢⴰ',
  'SL' => 'ⵙⵙⵉⵔⴰⵍⵢⵓⵏ',
  'SM' => 'ⵙⴰⵏⵎⴰⵔⵉⵏⵓ',
  'SN' => 'ⵙⵙⵉⵏⵉⴳⴰⵍ',
  'SO' => 'ⵚⵚⵓⵎⴰⵍ',
  'SR' => 'ⵙⵓⵔⵉⵏⴰⵎ',
  'ST' => 'ⵙⴰⵡⵟⵓⵎⵉ ⴷ ⴱⵔⴰⵏⵙⵉⴱ',
  'SV' => 'ⵙⴰⵍⴼⴰⴷⵓⵔ',
  'SY' => 'ⵙⵓⵔⵢⴰ',
  'SZ' => 'ⵙⵡⴰⵣⵉⵍⴰⵏⴷⴰ',
  'TC' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵏ ⵜⵓⵔⴽⵢⴰ ⴷ ⴽⴰⵢⴽ',
  'TD' => 'ⵜⵛⴰⴷ',
  'TG' => 'ⵟⵓⴳⵓ',
  'TH' => 'ⵟⴰⵢⵍⴰⵏⴷ',
  'TJ' => 'ⵜⴰⴷⵊⴰⴽⵉⵙⵜⴰⵏ',
  'TK' => 'ⵟⵓⴽⵍⴰⵡ',
  'TL' => 'ⵜⵉⵎⵓⵔ ⵏ ⵍⵇⴱⵍⵜ',
  'TM' => 'ⵜⵓⵔⴽⵎⴰⵏⵙⵜⴰⵏ',
  'TN' => 'ⵜⵓⵏⵙ',
  'TO' => 'ⵟⵓⵏⴳⴰ',
  'TR' => 'ⵜⵓⵔⴽⵢⴰ',
  'TT' => 'ⵜⵔⵉⵏⵉⴷⴰⴷ ⴷ ⵟⵓⴱⴰⴳⵓ',
  'TV' => 'ⵜⵓⴼⴰⵍⵓ',
  'TW' => 'ⵟⴰⵢⵡⴰⵏ',
  'TZ' => 'ⵟⴰⵏⵥⴰⵏⵢⴰ',
  'UA' => 'ⵓⴽⵔⴰⵏⵢⴰ',
  'UG' => 'ⵓⵖⴰⵏⴷⴰ',
  'US' => 'ⵉⵡⵓⵏⴰⴽ ⵎⵓⵏⵏⵉⵏ ⵏ ⵎⵉⵔⵉⴽⴰⵏ',
  'UY' => 'ⵓⵔⵓⴳⵡⴰⵢ',
  'UZ' => 'ⵓⵣⴱⴰⴽⵉⵙⵜⴰⵏ',
  'VA' => 'ⴰⵡⴰⵏⴽ ⵏ ⴼⴰⵜⵉⴽⴰⵏ',
  'VC' => 'ⵙⴰⵏⴼⴰⵏⵙⴰⵏ ⴷ ⴳⵔⵉⵏⴰⴷⵉⵏ',
  'VE' => 'ⴼⵉⵏⵣⵡⵉⵍⴰ',
  'VG' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵜⵉⵎⴳⴰⴷ ⵏ ⵏⵏⴳⵍⵉⵣ',
  'VI' => 'ⵜⵉⴳⵣⵉⵔⵉⵏ ⵜⵉⵎⴳⴰⴷ ⵏ ⵉⵡⵓⵏⴰⴽ ⵎⵓⵏⵏⵉⵏ',
  'VN' => 'ⴼⵉⵜⵏⴰⵎ',
  'VU' => 'ⴼⴰⵏⵡⴰⵟⵓ',
  'WF' => 'ⵡⴰⵍⵉⵙ ⴷ ⴼⵓⵜⵓⵏⴰ',
  'WS' => 'ⵙⴰⵎⵡⴰ',
  'YE' => 'ⵢⴰⵎⴰⵏ',
  'YT' => 'ⵎⴰⵢⵓⵟ',
  'ZA' => 'ⴰⴼⵔⵉⵇⵢⴰ ⵏ ⵉⴼⴼⵓⵙ',
  'ZM' => 'ⵣⴰⵎⴱⵢⴰ',
  'ZW' => 'ⵣⵉⵎⴱⴰⴱⵡⵉ',
);
