<?php
require_once('orders.inc.php');
require_once('Services/Cart/Cart.class.php');
require_once('ServicesActions/AddressActions.class.php');

/**	\brief Cette classe permet de réaliser les actions sur le panier en cours.
 * 	Toutes les actions lèveront des exceptions avec un message personnalisé et un code unique.
 * 	Les codes suivants sont réservés (et doivent être utilisé dans le cas de nouvelle action) :
 * 			- 98 : Aucun panier en cours
 * 			- 99 : Une erreur inattendue (quelque soit l'action)
 */
class CartActions {

	/** Cette fonction permet d'ajouter un ou plusieurs articles au panier en cours.
	 *
	 *	Exemple : Ajout d'un ou plusieurs produits simples
	 *	\code{.php}
	 *				$data = [
	 *					'prd' => [ '5e8204959bb08' => 2341, '5e8204c0c8a47' => 7685 ],
	 *					'qty' => [ '5e8204959bb08' => 4, '5e8204c0c8a47' => 1 ],
	 *					'dps' => [ '5e8204959bb08' => 45154, '5e8204c0c8a47' => 1451 ],
	 *					'col' => [ '5e8204c0c8a47' => 78 ],
	 *				];
	 *	\endcode
	 *
	 *	Exemple : Ajout d'une déclinaison par son identifiant
	 *	\code{.php}
	 *				$data = [
	 *					'prd' => [ '5e8204959bb08' => 2341 ],
	 *					'qty' => [ '5e8204959bb08' => 4 ],
	 *					'dps' => [ '5e8204959bb08' => 45154 ],
	 *					'choose-child' => [ '5e8204959bb08' => 9078 ],
	 *				];
	 *	\endcode
	 *
	 *	Exemple : Ajout d'une déclinaison par le choix d'option
	 *	\code{.php}
	 *				$data = [
	 *					'prd' => [ '5e8204959bb08' => 2341 ],
	 *					'qty' => [ '5e8204959bb08' => 4 ],
	 *					'dps' => [ '5e8204959bb08' => 45154 ],
	 *					'opt-fld' => [ '5e8204959bb08' => [ 890 => 'Rouge', 788 => 'XL' ] ],
	 *				];
	 *	\endcode
	 *
	 * @param array $data Obligatoire, information nécessaire à l'ajout au panier :
	 * 				- prd (obligatoire) : tableau des identifiants de produit [ 'clé unique' => 'identifiant du produit' ]
	 * 				- qty (obligatoire) : tableau de la quantité par produit [ 'clé unique' => 'quantité à ajouter' ]
	 * 				- dps (optionnel) : tableau du dépôt par produit [ 'clé unique' => ' identifiant du dépôt appliqué]
	 * 				- col (optionnel) : tableau du conditionnement par produit [ 'clé unique' => ' identifiant du conditionnement appliqué]
	 * 				- choose-child (optionnel) : identifiant du produit déclinaison choisie
	 * 				- opt-fld (optionnel) : champ avancé permettant d'identifier la déclinaison choisie [ 'identifiant champ' => 'valeur choisie' ]
	 * @param	mixed	$ord_id		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * @return array Une exception sera levée en cas d'erreur sinon un tableau contenant les produits ajoutés :
	 * 				- 0 : identifiant de la commande
	 * 				- 1 : identifiant du produit
	 * 				- 2 : quantité réellement ajoutée (tient compte des limites de stocks quand celles-ci sont appliquées)
	 */
	public static function addProduct( $data, $ord_id=null ){
		global $hook;
		global $config;
		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Aucune donnée obligatoire n\'est fournie.', 'ERROR'), 1 );
		}

		if( !isset($data['qty']) ){
			throw new Exception( i18n::get('Un ou plusieurs paramètres obligatoires sont manquants.', 'ERROR'), 2 );
		}
		$custom = true;

		if( !is_numeric($ord_id) || $ord_id <= 0 ){

			// Création d'un panier si aucun n'existe encore
			if( !ord_carts_add_if_not_exists() ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre panier.', 'ERROR'), 99);
			}
			$ord_id = self::getOrdID();
			$custom = false;

		}
		$User = CustomerService::getInstance();

		if( !ord_orders_exists($ord_id, $User->getID()) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite de la récupération de votre panier.', 'ERROR'), 98);
		}

		$obj_add_to_cart = array();

		foreach( $data['qty'] as $key=>$qty ){
			if( !is_numeric($qty) || $qty <= 0 ){
				continue;
			}

			// Dans le cas d'un ajout d'un article enfant par liste déroulante
			if( isset($data['choose-child'][$key]) && is_numeric($data['choose-child'][$key]) && $data['choose-child'][$key] > 0 ){
				$data['prd'][ $key ] = $data['choose-child'][$key];
			}

			if( !isset($data['prd']) || !array_key_exists($key, $data['prd']) ){
				throw new Exception( i18n::get('La référence saisie n\'a pas été trouvée dans le catalogue.', 'ERROR'), 3 );
			}

			if( !isset($data['dps']) ){
				$data['dps'] = [];
			}

			$colisage = array_key_exists($key, $data['col']) && is_numeric($data['col'][$key]) && $data['col'][$key] > 0 ? $data['col'][$key] : false;
			$deposit = array_key_exists($key, $data['dps']) && is_numeric($data['dps'][$key]) && $data['dps'][$key] > 0 ? $data['dps'][$key] : false;

			// Chargement du produit selon les options choisie
			if( isset($data['opt-fld'][$key]) ){
				if( !is_array($data['opt-fld'][$key]) || !count($data['opt-fld'][$key]) ){
					throw new Exception( i18n::get('Les options de déclinaison sont fausses.', 'ERROR'), 4 );
				}

				$r_product = prd_products_get_simple( 0, '', false, 0, false, $data['opt-fld'][$key], false, false, array('childs'=>true, 'parent' => $data['prd'][ $key ], 'prd_publish' => true, 'fld_use_hierarchy_ids' => true) );
			}else{
				$r_product = prd_products_get_simple( $data['prd'][ $key ], '', true, 0, false, false, false, false, array('childs'=>true) );
			}

			if( !$r_product || !ria_mysql_num_rows($r_product) ){
				throw new Exception( i18n::get('La référence saisie n\'a pas été trouvée dans le catalogue.', 'ERROR'), 5 );
			}

			$product = ria_mysql_fetch_assoc( $r_product );

			if($config['tnt_id'] == 588 || $config['tnt_id'] == 1279){
				$sum_stock_berton = prd_dps_stocks_get_sum( $product['id'] );
				$stock_berton = ria_mysql_fetch_assoc( $sum_stock_berton );
				$product['stock'] = $stock_berton['qte'];
				if($stock_berton['qte'] > 0){
					$product['orderable'] = true;
				}
			}

			if( !$product['orderable']  ){
				throw new Exception( str_replace('%produit%', $product['title'], i18n::get('L\'article %produit% n\'est plus disponible.', 'ERROR')), 4 );
			}
			$prd_stock = $hook->apply_filter('CartActions_addProductOverrideProductStock', $product['stock'], $product);

			if( !Template::get('ord-product-nostock') && $product['follow_stock'] && $prd_stock <= 0  ){
				throw new Exception( str_replace('%produit%', $product['title'], i18n::get('L\'article %produit% n\'est plus disponible.', 'ERROR')), 6 );
			}

			$r_line = ord_products_get(
				$ord_id, false, $product['id'], '', null, false, 0, 0, -1, false, false, 0, false, false, $colisage,
				false, false, false, $deposit
			);

			if( $r_line && ria_mysql_num_rows($r_line) ){
				$line_id = false;
				while( $line = ria_mysql_fetch_assoc($r_line) ){
					if( !$line['cod'] ){
						$qty += $line['qte'];
						$line_id = $line['line'];
						break;
					}
				}

				if( $line_id !== false && !ord_products_del($ord_id, $product['id'], $line_id) ){
					throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de l\'ajout au panier.', 'ERROR'), 7 );
				}
			}

			//Spé Berton : MAINT_BO #55268 - Bouton Ajout au panier non limitant selon le stock
			if($config['tnt_id'] == 588 || $config['tnt_id'] == 1279) {
				if( !Template::get('ord-product-nostock') ){
				if( Template::get('use-sales-unit') ){
					$unit_sell = fld_object_values_get( $product['id'], _FLD_PRD_SALES_UNIT );
	
					if( is_numeric($unit_sell) && $unit_sell > 0 ){
						$prd_stock = floor( $prd_stock / $unit_sell ) * $unit_sell;
					}
				}

					// produit sur devis uniquement
					$quotation_only = fld_object_values_get( $product['id'], 114454);
					if( !in_array($quotation_only,["Oui","oui","yes","Yes",1,"1"]) ){
				if( $qty >= $prd_stock ){
					$_SESSION['cart_qty_max'] = array( 'prd_id' => $product['id'], 'stock' => $prd_stock);
					$qty = $prd_stock;
				}
			}
				}
			}
			else{
				if( !Template::get('ord-product-nostock') && $product['follow_stock'] ){
					if( Template::get('use-sales-unit') ){
						$unit_sell = fld_object_values_get( $product['id'], _FLD_PRD_SALES_UNIT );
	
						if( is_numeric($unit_sell) && $unit_sell > 0 ){
							$prd_stock = floor( $prd_stock / $unit_sell ) * $unit_sell;
						}
					}
	
					if( $qty >= $prd_stock ){
						$_SESSION['cart_qty_max'] = array( 'prd_id' => $product['id'], 'stock' => $prd_stock);
						$qty = $prd_stock;
					}
				}
			}

			$qty_is_add = $qty;

			$line = ord_products_add(
				$ord_id, $product['id'], $qty, '', true, null, ($colisage ? $colisage : 0), false, 0, 0, false,
				false, false, true, false, false, $deposit
			);
			if( $line === false ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de l\'ajout au panier.', 'ERROR'), 8 );
			}else{
				$obj_add_to_cart[] = array($ord_id, $product['id'], $line, $qty_is_add );
			}
		}

		if( count($obj_add_to_cart) ){
			// Mise à jour des totaux du panier
			ord_orders_refresh($ord_id);
		}

		if( !$custom ){
			self::postActionLineOrder();
		}

		return $obj_add_to_cart;
	}

	/** Cette fonction permet de gérer l'ajout au panier d'une ou plusieurs nomenclatures variables.
	 *
	 *	Exemple : Ajout d'une nomenclature variable
	 *	\code{.php}
	 *				$data = [
	 *					'prd' => [ '5e8204959bb08' => 2341, '5e8204c0c8a47' => 7685 ],
	 *					'qty' => [ '5e8204959bb08' => 4, '5e8204c0c8a47' => 1 ],
	 *					'col' => [ '5e8204c0c8a47' => 78 ],
	 *				];
	 *	\endcode
	 *
	 * 	@param array $data Obligatoire, information nécessaire à l'ajout au panier :
	 * 				- prd (obligatoire) : tableau des identifiants de produit [ 'clé unique' => 'identifiant du produit' ]
	 * 				- qty (obligatoire) : tableau de la quantité par produit [ 'clé unique' => 'quantité à ajouter' ]
	 * 				- opt-[identifiant de l'option] : tableau de chaque valeur de nomenclature choisi
	 * 	@return array Une exception sera levée en cas d'erreur sinon un tableau contenant les produits ajoutés :
	 * 				- 0 : identifiant de la commande
	 * 				- 1 : identifiant du produit
	 * 				- 2 : quantité réellement ajoutée (tient compte des limites de stocks quand celles-ci sont appliquées)
	 */
	public static function addNomenclature( $data ){
		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Aucune donnée obligatoire n\'est fournie.', 'ERROR'), 1 );
		}

		if( !isset($data['qty']) ){
			throw new Exception( i18n::get('Un ou plusieurs paramètres obligatoires sont manquants.', 'ERROR'), 2 );
		}

		// Création d'un panier si aucun n'existe encore
		if( !ord_carts_add_if_not_exists() ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la création de votre panier.', 'ERROR'), 99);
		}

		$obj_add_to_cart = array();

		foreach( $data['qty'] as $key => $qty ){
			$prd_id = $data['prd'][$key];
			$roptions = prd_nomenclatures_options_get($prd_id);

			$pack_data = array();

			// Charge les options choisies de la nomenclature tout en contrôlant que toutes soient bien choisi
			while( $o = ria_mysql_fetch_array($roptions) ){
				if( isset($data['opt-'.$o['opt']][$key]) && is_numeric($data['opt-'.$o['opt']][$key]) && $data['opt-'.$o['opt']][$key] > 0 ){
					$pack_data[$o['opt']] = $data['opt-'.$o['opt']][$key];
				}else{
					// Si une option est manquante, une exception est levée
					throw new Exception( i18n::get('Merci de choisir un article pour chaqu\'une des options proposées.', 'ERROR'), 2);
				}
			}

			// Comportements différents en cas de mise à jour ou d'ajout
			$line_id = ord_products_add( self::getOrdID(), $prd_id, $qty, '', false, $pack_data, 0, false, 0, 0, false, false, true, true );
			if( $line_id === false ){
				throw new Exception( i18n::get('Une erreur inattendue est survenue lors de l\'ajout au panier de l\'article.', 'ERROR'), 99);
			}

			$obj_add_to_cart[] = array( self::getOrdID(), $prd_id, $line_id, $qty );
		}

		self::postActionLineOrder();
		return $obj_add_to_cart;
	}

	/** Cette fonction permet de gérer l'ajout au panier par référence.
	 *
	 *	Exemple : Ajout d'un produit par référence
	 *	\code{.php}
	 *				$data = [
	 *					'ref' => 'REFERENCEDUPRODUIT',
	 * 					'qty' => 1
	 *				];
	 *	\endcode
	 *
	 * 	@param array $data Obligatoire, information nécessaire à l'ajout au panier :
	 * 				- ref (obligatoire) : référence du produit
	 * 				- qty (obligatoire) : quantité choisie
	 *
	 * 	@return array Une exception sera levée en cas d'erreur sinon un tableau contenant les produits ajoutés :
	 * 				- 0 : identifiant de la commande
	 * 				- 1 : identifiant du produit
	 * 				- 2 : quantité réellement ajoutée (tient compte des limites de stocks quand celles-ci sont appliquées)
	 */
	public static function addByReference( $data ){
		global $config;

		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Aucune donnée obligatoire n\'est fournie.', 'ERROR'), 1 );
		}

		if( !isset($data['ref'], $data['qty']) ){
			throw new Exception( i18n::get('Un ou plusieurs paramètres obligatoires sont manquants.', 'ERROR'), 2 );
		}

		// Recherche l'identifiant du produit par sa référence
		// Insensible à la casse
		$prd_id = prd_products_get_id( $data['ref'], $config['cat_root'] );
		if( !is_numeric($prd_id) || $prd_id <= 0 ){
			throw new Exception( i18n::get('La référence saisie n\'a pas été trouvée dans le catalogue.', 'ERROR'), 3 );
		}

		$col = [];
		$uniqid= uniqid();

		// Si le conditionnement est obligatoire, on ajoutera le produit avec son premier conditionnement
		if( Template::get('catalog-only-price-colisage') ){
			$r_colisage = prd_colisage_classify_get( 0, $prd_id );
			if( $r_colisage && ria_mysql_num_rows($r_colisage) ){
				$colisage = ria_mysql_fetch_assoc( $r_colisage );
				$col = [ $uniqid => $colisage['col_id'] ];
			}
		}

		return self::addProduct([
			'prd' => [ $uniqid => $prd_id ],
			'qty' => [ $uniqid => $data['qty'] ],
			'col' => $col
		]);
	}

	/** Cette méthode permet de dupliquer une ligne produit d'un panier.
	 *
	 *	Exemple : Mise à jour d'une ou plusieurs lignes du panier en cours
	 *	\code{.php}
	 *				$data = [
	 *					'prd' => [ '5e8204959bb08' => 2341, '5e8204c0c8a47' => 7685 ],
	 *					'line' => [ '5e8204959bb08' => 0, '5e8204c0c8a47' => 0 ],
	 *					'col' => [ '5e8204c0c8a47' => 78 ],
	 *				];
	 *	\endcode
	 *
	 * 	@param array $data Obligatoire information sur la ligne du panier
	 * 				- prd (obligatoire) : tableau des identifiants de produit [ 'clé unique' => 'identifiant du produit' ]
	 * 				- line (obligatoire) : tableau des identifiants de ligne de panier [ 'clé unique' => 'identifiant de la ligne' ]
	 * 				- col (optionnel) : tableau du conditionnement par produit [ 'clé unique' => ' identifiant du conditionnement appliqué]
	 * @param	mixed	$ord_id		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * @return array Une exception sera levée en cas d'erreur sinon un tableau contenant les produits ajoutés :
	 * 				- 0 : identifiant de la commande
	 * 				- 1 : identifiant du produit
	 * 				- 2 : quantité réellement ajoutée (tient compte des limites de stocks quand celles-ci sont appliquées)
	 */
	public static function duplicateLine( $data, $ord_id=null ){

		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Aucune donnée obligatoire n\'est fournie.', 'ERROR'), 1 );
		}

		if( !array_key_exists('prd', $data) || !is_array($data['prd']) || !count($data['prd']) ){
			throw new Exception( i18n::get('L\'identifiant du produit est manquant, empêchant la mise à jour du panier.', 'ERROR'), 2 );
		}

		if( !array_key_exists('line', $data) || !is_array($data['line']) || !count($data['line']) ){
			throw new Exception( i18n::get('L\'identifiant de la ligne est manquant, empêchant la mise à jour du panier.', 'ERROR'), 3 );
		}
		$cart_id = self::getOrdID();
		$custom = false;

		if( is_numeric($ord_id) && $ord_id > 0 ){

			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE]) ){
				throw new Exception( i18n::get('Le panier n\'existe pas.', 'ERROR'), 99);
			}
			$cart_id = $ord_id;
			$custom = true;

		}

		$obj_add_to_cart = array();

		foreach( $data['prd'] as $key=>$id ){

			$colisage = array_key_exists($key, $data['col']) && is_numeric($data['col'][$key]) && $data['col'][$key] > 0 ? $data['col'][$key] : false;
			$r_oprd = ord_products_get( $cart_id, false, $id, '', ($data['line'][ $key ] != "0" ? $data['line'][ $key ] : null), false, 0, 0, -1, false, false, 0, false, false, $colisage );

			if( !ria_mysql_num_rows($r_oprd) ){
				throw new Exception( i18n::get('La ligne du panier a dupliquer n\'a pas été trouvée.', 'ERROR'), 5 );
			}

			$oprd = ria_mysql_fetch_assoc( $r_oprd );
			$qty = $oprd['qte'];

			// Charge les information sur le produit
			$product = new ProductService( ['prd' => $oprd['id']] );
			$product->general();

			if( !Template::get('ord-product-nostock') && $product->getFollowStock() ){
				$stock = $product->getStock();

				// Si le produit n'est commendable qu'avec une unité de vente (ex.)
				if( Template::get('use-sales-unit') ){
					$unit_sell = fld_object_values_get( $product->getID(), _FLD_PRD_SALES_UNIT );

					if( is_numeric($unit_sell) && $unit_sell > 0 ){
						$stock = floor( $product->getStock() / $unit_sell ) * $unit_sell;
					}
				}

				if( $qty >= $stock){
					$qty = $stock;
				}
			}
			$line = ord_products_add($ord_id, $product->getID(), $qty, '', true, null, ($colisage ? $colisage : 0), false, 0, 0, false, false, false, true, false, true);

			if( $line === false ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la duplication du produit.', 'ERROR'), 8 );
			}
			$obj_add_to_cart[] = array($ord_id, $product->getID(), $line, $qty );
			break;
		}

		if( count($obj_add_to_cart) ){
			// Mise à jour des totaux du panier
			ord_orders_refresh($ord_id);
		}

		if( !$custom ){
			self::postActionLineOrder();
		}
		return $obj_add_to_cart;
	}

	/** Cette fonction permet de retirer un article du panier en cours.
	 * 	@param array $data Obligatoire information sur la ligne du panier à supprimer
	 * 				- prd : identifiant du produit
	 * 				- line : identifiant de la ligne du panier
	 * 	@param	mixed	$ord_id		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * 	@return bool Une exception sera levée en cas d'erreur sinon true en cas de succès
	 */
	public static function deleteProduct( $data, $ord_id=null ){
		if( !is_array($data) || !count($data) ){
			throw new Exception( i18n::get('Aucune donnée obligatoire n\'est fournie.', 'ERROR'), 1 );
		}

		if( !array_key_exists('prd', $data) || !is_numeric($data['prd']) || $data['prd'] <= 0 ){
			throw new Exception( i18n::get('L\'identifiant du produit est manquant empêchant sa suppression.', 'ERROR'), 2 );
		}

		if( !array_key_exists('line', $data) || !is_numeric($data['line']) || $data['line'] < 0 ){
			throw new Exception( i18n::get('L\'identifiant de la ligne de commande est manquante empêchant sa suppression.', 'ERROR'), 3 );
		}
		$cart_id = self::getOrdID();

		$custom = false;

		if( is_numeric($ord_id) && $ord_id > 0 ){

			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE]) ){
				throw new Exception( i18n::get('Le panier n\'existe pas.', 'ERROR'), 99);
			}
			$cart_id = $ord_id;
			$custom = true;

		}

		$r_oprd = ord_products_get( $cart_id, false, $data['prd'], '', $data['line'] );

		if( !$r_oprd || !ria_mysql_num_rows($r_oprd) ){
			throw new Exception( i18n::get('La ligne de commande n\'existe pas ou plus.', 'ERROR'), 4 );
		}
		$oprd = ria_mysql_fetch_assoc( $r_oprd );
		$chl = is_numeric( $oprd['child-line'] ) && $oprd['child-line'] >= 0 ? $oprd['child-line'] : -1;

		if( !ord_products_del($cart_id, $data['prd'], $data['line'], $chl) ){
			throw new Exception( i18n::get('Une erreur inattendue est survenue lors de la suppression de l\'article de votre panier', 'ERROR'), 99 );
		}

		if( !$custom ){
			self::postActionLineOrder();
			ord_orders_promotions_verified( $cart_id );
		}
	}

	/** Cette fonction permet de vider entièrement un panier en cours.
	 * 	@return bool Une exception sera levée en cas d'erreur sinon true en cas de succès
	 */
	public static function emptyCart(){
		// Charge le panier en cours
		$is_ok = ord_products_del( self::getOrdID() );

		if( !$is_ok ){
			throw new Exception( i18n::get('Une erreur est survenue lors du vidage de votre panier', 'ERROR'), 4 );
		}else{
			self::postActionLineOrder();
			ord_orders_promotions_verified( self::getOrdID() );
		}
	}

	/** Cette fonction permet de mettre à jour un article du panier en cours.
	 *
	 *	Exemple : Mise à jour d'une ou plusieurs lignes du panier en cours
	 *	\code{.php}
	 *				$data = [
	 *					'prd' => [ '5e8204959bb08' => 2341, '5e8204c0c8a47' => 7685 ],
	 *					'qty' => [ '5e8204959bb08' => 4, '5e8204c0c8a47' => 1 ],
	 *					'line' => [ '5e8204959bb08' => 0, '5e8204c0c8a47' => 0 ],
	 *					'col' => [ '5e8204c0c8a47' => 78 ],
	 *				];
	 *	\endcode
	 *
	 * 	@param array $data Obligatoire information sur la ligne du panier
	 * 				- prd (obligatoire) : tableau des identifiants de produit [ 'clé unique' => 'identifiant du produit' ]
	 * 				- qty (obligatoire) : tableau des quantités par produit [ 'clé unique' => 'nouvelle quantité' ]
	 * 				- line (obligatoire) : tableau des identifiants de ligne de panier [ 'clé unique' => 'identifiant de la ligne' ]
	 * 				- col (optionnel) : tableau du conditionnement par produit [ 'clé unique' => ' identifiant du conditionnement appliqué]
	 * 	@param	mixed	$ord_id		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * 	@return bool Une exception sera levée en cas d'erreur sinon true en cas de succès
	 */
	public static function updateProduct( $data, $ord_id=null ){
		global $hook;

		if( !array_key_exists('qty', $data) || !is_array($data['qty']) || !count($data['qty']) ){
			throw new Exception( i18n::get('L\'information de quantité est manquante, empêchant la mise à jour du panier.', 'ERROR'), 1 );
		}

		if( !array_key_exists('prd', $data) || !is_array($data['prd']) || !count($data['prd']) ){
			throw new Exception( i18n::get('L\'identifiant du produit est manquant, empêchant la mise à jour du panier.', 'ERROR'), 2 );
		}

		if( !array_key_exists('line', $data) || !is_array($data['line']) || !count($data['line']) ){
			throw new Exception( i18n::get('L\'identifiant de la ligne est manquant, empêchant la mise à jour du panier.', 'ERROR'), 3 );
		}
		$cart_id = self::getOrdID();
		$custom = false;

		// Détection du cas où la mise à jour n'est pas réalisé sur le panier en session, mais un autre
		if( is_numeric($ord_id) && $ord_id > 0 ){
			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE]) ){
				throw new Exception( i18n::get('Le panier n\'existe pas.', 'ERROR'), 99);
			}

			$cart_id = $ord_id;
			$custom = true;
		}

		foreach( $data['qty'] as $key=>$qty ){
			if( !is_numeric($qty) || $qty <= 0 ){
				continue;
			}

			if( !array_key_exists($key, $data['prd']) || !array_key_exists($key, $data['line']) ){
				throw new Exception( i18n::get('Un ou plusieurs paramètres obligatoires sont manquants.', 'ERROR'), 4 );
			}
			$colisage = array_key_exists($key, $data['col']) && is_numeric($data['col'][$key]) && $data['col'][$key] > 0 ? $data['col'][$key] : false;
			$r_oprd = ord_products_get( $cart_id, false, $data['prd'][ $key ], '', ($data['line'][ $key ] != "0" ? $data['line'][ $key ] : null), false, 0, 0, -1, false, false, 0, false, false, $colisage );

			if( !$r_oprd || !ria_mysql_num_rows($r_oprd) ){
				throw new Exception( i18n::get('La ligne du panier a mettre à jour n\'a pas été trouvée.', 'ERROR'), 5 );
			}

			$oprd = ria_mysql_fetch_assoc( $r_oprd );

			// Initialise des variables pour permettre la mise à jour des nomenclatures
			$ar_childs = null;
			$childsline = -1;

			// Charge les information sur le produit
			$product = new ProductService( ['prd' => $oprd['id']] );
			$product->general();

			if( $product->getTypeNomenclature() == NM_TYP_VARIABLE ){
				$r_child = ord_products_get( $cart_id, false, 0, '', null, false, -1, $oprd['id'], $oprd['child-line'] );
				if( $r_child && ria_mysql_num_rows($r_child) ){
					$ar_childs = [];

					while ($c = ria_mysql_fetch_array($r_child)) {
						$ar_childs[$c['opt']] = $c['id'];
					}
				}

				$childsline = $oprd['child-line'];
			}

			if( !Template::get('ord-product-nostock') && $product->getFollowStock() ){
				$stock = $product->getStock();
				$prd_stock = $hook->apply_filter('CartActions_updateProductOverrideProductStock', $stock, $product->getData());

				// Si le produit n'est commendable qu'avec une unité de vente (ex.)
				if( Template::get('use-sales-unit') ){
					$unit_sell = fld_object_values_get( $product->getID(), _FLD_PRD_SALES_UNIT );

					if( is_numeric($unit_sell) && $unit_sell > 0 ){
						$prd_stock = floor( $prd_stock / $unit_sell ) * $unit_sell;
					}
				}

				if( $qty >= $prd_stock){
					$qty = $prd_stock;
				}
			}

			if( !ord_products_update($cart_id, $oprd['id'], $qty, $childsline, ($colisage ? $colisage : 0), $oprd['line'], false, $ar_childs) ){
				throw new Exception( i18n::get('Une erreur inattendue est survenue lors de la mise à jour de votre panier.', 'ERROR'), 99 );
			}
		}

		if( !$custom ){
			self::postActionLineOrder();
		}

		return true;
	}

	/** Cette fonction permet d'appliquer un code promotion au panier en cours.
	 *	@param array $data Obligatoire, information nécessaire à l'application du code promotion
	 *				- cod : code promotion saisie par l'internaute
	 *	@return bool Une exception sera levée en cas d'erreur sinon true en cas de succès
	 */
	public static function applyPromotionCode( $data, $post_action=true ){
		if( !array_key_exists('cod', $data) || trim($data['cod']) == '' ){
			throw new Exception( i18n::get('Le code promotion est manquant.', 'ERROR'), 1 );
		}

		// Contrôle que le code est applicable sur le panier en cours
		$is_applicable = pmt_codes_is_applicable( $data['cod'], self::getOrdID() );

		if( $is_applicable === true ){
			if( !pmt_codes_apply($data['cod'], self::getOrdID()) ){
				throw new Exception( i18n::get('Une erreur est survenue lors de l\'application du code promotion.', 'ERROR'), 99 );
			}
		}else{
			$desc_error = pmt_err_describe( $is_applicable, $data['cod'] );
			$desc_error = html_revert_wysiwyg( $desc_error );

			throw new Exception( $desc_error, $is_applicable );
		}

		if( $post_action ){
			self::postActionLineOrder( ['update-content' => false] );
		}

		return true;
	}

	/** Cette fonction permet de supprimer le code promotion appliqué au panier.
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function cancelPromotionCode( $data, $post_action=true ){
		if( array_key_exists('id', $data) ){
			if( !is_numeric($data['id']) || $data['id'] <= 0 ){
				throw new Exception( i18n::get('L\'identifiant du code promotion est faux.', 'ERROR'), 1);
			}
		}else{
			$data['id'] = 0;
		}

		if( !pmt_codes_cancel(self::getOrdID(), true, $data['id']) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors du retrait du code promotion.', 'ERROR'), 99 );
		}

		if( $post_action ){
			self::postActionLineOrder( ['update-content' => false] );
		}

		return true;
	}
	/** Cett fonction permet de recharger la commande en cours, elle est utilisée pour mettre à jour le panier en cours, ou pour recharger le panier après une modification de la commande.
	 *
	 *@param array $data Les paramètres de la fonction ord_orders_refresh (ord_id, update, refresh), sont dipsonible dans le tableau
	 *@return bool Une exception sera levée en cas d'erreur, true en cas de succès, si le paramètre get_deleted est à true, la fonction retournera un tableau contenant les id des produits supprimés
	 */
	public static function verifyPromotions(){

		if( !ord_orders_promotions_verified(self::getOrdID()) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour du panier.', 'ERROR'), 99 );
		}

		return true;
	}

	/** Cette fonction permet d'appliquer un service de livraison à un panier en cours.
	 * 	@param	array	$data	Informations sur le service de livraison choisi et la commande
	 * 						- srv : Obligatoire, identifiant du service de livraison
	 * 						- ord : Optionnel, identifiant d'une commande à mettre à jour (par défaut panier en cours)
	 * 	@return	bool	Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function chooseDeliveryService( $data ){
		global $config;

		// Contrôle que le service de livraison est bien renseigné
		if( !array_key_exists('srv', $data) || !is_numeric($data['srv']) || !$data['srv'] ){
			throw new Exception( i18n::get('Le service de livraison n\'est pas renseigné.', 'ERROR'), 1);
		}

		// Charge le panier en cours
		$ord_id = self::getOrdID();
		$custom = false;

		// Détection du cas où la mise à jour n'est pas réalisé sur le panier en session, mais un autre
		if( isset($data['ord']) && is_numeric($data['ord']) && $data['ord'] > 0 ){
			if( !ord_orders_exists( $data['ord'], 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la sélection du service de livraison', 'ERROR'), 99);
			}
			$ord_id = $data['ord'];
			$custom = true;
		}

		// Applique le service de livraison sur la commande
		if( !ord_orders_set_dlv_service($ord_id, $data['srv']) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la sélection du service de livraison', 'ERROR'), 99);
		}

		// Remise à zéro du magasin si nécessaire
		if( !isset($data['unset_store']) || (isset($data['unset_store']) && is_bool($data['unset_store']) && $data['unset_store']) ){
			ord_orders_set_dlv_store($ord_id, false);
		}

		// Remise à zéro du relai si nécessaire
		if( !isset($data['unset_relay']) || (isset($data['unset_relay']) && is_bool($data['unset_relay']) && $data['unset_relay']) ){
			ord_orders_set_relay($ord_id, null);
		}


		if( !isset($data['reset_delivery_adr']) || (isset($data['reset_delivery_adr']) && is_bool($data['reset_delivery_adr']) && $data['reset_delivery_adr']) ){
			// Si le service de livraison est de type point relai,
			// alors l'adresse postal de livraison liée au panier est mise par défaut à l'identique de l'adresse de facturation
			$r_presta = ria_mysql_query('
				select srv_presta_id
				from dlv_services
				where srv_tnt_id = '.$config['tnt_id'].'
					and srv_id = '.$data['srv'].'
			');

			if( ria_mysql_num_rows($r_presta) ){
				$presta = ria_mysql_fetch_assoc( $r_presta );

				if( is_numeric($presta['srv_presta_id']) && $presta['srv_presta_id'] > 0 ){
					ria_mysql_query('
						update ord_orders set ord_adr_delivery = ord_adr_invoices
						where ord_tnt_id = '.$config['tnt_id'].' and ord_id = '.$ord_id.'
					');
				}
			}
		}

		if( !$custom ){
			self::applyPort();
		}

		return true;
	}

	/**	Cette méthode permet de supprimer le service de livraison sur le panier en cours
	 * @param	array	$data	Tableau d'informations pouvant contenir :
	 * 						- ord			int		Optionnel, identifiant d'une commande (par défaut panier en cours)
	 * @return	bool	Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function deleteDeliveryService($data=[]){

		// Charge le panier en cours
		$ord_id = self::getOrdID();
		$custom = false;

		// Détection du cas où la mise à jour n'est pas réalisé sur le panier en session, mais un autre
		if( isset($data['ord']) && is_numeric($data['ord']) && $data['ord'] > 0 ){
			if( !ord_orders_exists( $data['ord'], 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la suppression du service de livraison', 'ERROR'), 99);
			}
			$ord_id = $data['ord'];
			$custom = true;
		}

		// Supprime le service de livraison
		if( !ord_orders_set_dlv_service($ord_id, null) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la suppression du service de livraison', 'ERROR'), 99);
		}

		if( !$custom ){
			// Charge le panier en cours
			$Cart = CartService::getInstance();
			if( !$Cart->exists() ){
				return true;
			}
			self::applyPort();

			// Si l'action est réalisée en Ajax alors l'objet du panier en cours est mis à jour
			$Cart->reload();
		}

		return true;
	}

	/** Cette fonction permet de mettre à jour les commentaires sur le panier en cours.
	 * 	@param array $data Obligatoire, information permettant la mise à jour
	 * 				- comments : commentaires sur le panier
	 * 	@param	mixed	$ord_id		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateComments( $data, $ord_id=null ){
		// Contrôle que les commentaires sont bien donnés en paramêtre
		if( !array_key_exists('comments', $data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes.', 'ERROR'), 1 );
		}

		// Charge le panier en cours
		$cart_id = self::getOrdID();

		if( is_numeric($ord_id) && $ord_id > 0 ){

			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Le panier n\'existe pas.', 'ERROR'), 99);
			}
			$cart_id = $ord_id;

		}

		// Mise à jour du commentaire
		if( !ord_orders_comments_update( $cart_id, $data['comments']) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour du commentaire.', 'ERROR'), 2 );
		}

		return true;
	}

	/** Cette fonction permet de mettre à jour le commentaire une ligne du panier en cours.
	 * 	@param array $data Obligatoire, information permettant la mise à jour
	 * 				- prd : identifiant du produit
	 * 				- line : identifiant de la ligne
	 * 				- comments : commentaires sur la ligne
	 * 	@param	mixed	$ord_id		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateLineComments( $data, $ord_id=null ){
		// Contrôle que les commentaires sont bien donnés en paramêtre
		if( !ria_array_key_exists(['prd', 'line', 'comments'], $data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes.', 'ERROR'), 1 );
		}

		if( !is_numeric($data['prd']) || $data['prd'] <= 0 ){
			throw new Exception( i18n::get('L\'identifiant du produit est faux.'), 2);
		}

		if( !is_numeric($data['line']) || $data['line'] < 0 ){
			throw new Exception( i18n::get('L\'identifiant de la ligne est faux.'), 3);
		}
		$cart_id = self::getOrdID();

		if( is_numeric($ord_id) && $ord_id > 0 ){

			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Le panier n\'existe pas.', 'ERROR'), 99);
			}
			$cart_id = $ord_id;
		}

		// Mise à jour du commentaire
		if( !ord_products_notes_update( $cart_id, $data['prd'], $data['comments'], false, $data['line']) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour du commentaire.', 'ERROR'), 2 );
		}

		return true;
	}

	/** Cette méthode permet de mettre à jour un champ avancé d'une ligne d'un panier.
	 * 	@param array $data Obligatoire, informations permettant la mise à jour
	 * 				- prd : identifiant du produit
	 * 				- line : identifiant de la ligne
	 * 				- value : valeur du champ
	 * 	@param	int		$fld		Obligatoire, identifiant du champ avancé à mettre à jour
	 * 	@param	mixed	$ord_id		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateLineField( $data, $fld, $ord_id=null ){
		// Contrôle que les commentaires sont bien donnés en paramêtre
		if( !ria_array_key_exists(['prd', 'line', 'value'], $data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes.', 'ERROR'), 1 );
		}

		if( !is_numeric($data['prd']) || $data['prd'] <= 0 ){
			throw new Exception( i18n::get('L\'identifiant du produit est faux.'), 2);
		}

		if( !is_numeric($data['line']) || $data['line'] < 0 ){
			throw new Exception( i18n::get('L\'identifiant de la ligne est faux.'), 3);
		}

		if( !is_numeric($fld) || $fld <= 0 || !fld_fields_exists($fld) ){
			throw new Exception( i18n::get('L\'information n\'a pas pu être enregistrée.'), 4);
		}
		$cart_id = self::getOrdID();

		if( is_numeric($ord_id) && $ord_id > 0 ){

			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Le panier n\'existe pas.', 'ERROR'), 99);
			}
			$cart_id = $ord_id;
		}

		$r_oprd = ord_products_get( $cart_id, false, $data['prd'], '', $data['line'] );

		if( !ria_mysql_num_rows($r_oprd) ){
			throw new Exception( i18n::get('La ligne de commande n\'existe pas ou plus.', 'ERROR'), 5 );
		}

		if( !fld_object_values_set([$cart_id, $data['prd'], $data['line']], $fld, $data['value']) ){
			throw new Exception( i18n::get('L\'information n\'a pas pu être enregistrée.'), 4);
		}
		return true;

	}

	/** Cette fonction permet de mettre à jour les consignes de livraison sur un panier ou devis.
	 * 	@param	array	$data	Tableau d'informations permettant la mise à jour
	 * 					- notes		string		Obligatoire, consignes de livraison sur le panier ou devis
	 * 					- ord		int			Optionnel, identifiant d'un panier ou devis
	 * 	@return	bool	Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateDeliveryNotes( $data ){
		// Contrôle que les consignes de livraison sont bien donnés en paramêtre
		if( !array_key_exists('notes', $data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes.', 'ERROR'), 1 );
		}

		$ord_id = self::getOrdID();

		if( isset($data['ord']) && is_numeric($data['ord']) && $data['ord'] > 0 ){

			if( !ord_orders_exists( $data['ord'], 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour des consignes de livraison.', 'ERROR'), 2 );
			}
			$ord_id = $data['ord'];
		}

		// Mise à jour des consignes de livraison
		if( !ord_orders_dlv_notes_set( $ord_id, $data['notes']) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour des consignes de livraison.', 'ERROR'), 2 );
		}

		return true;
	}


	/** Cette fonction permet de mettre à jour les interlignes sur le panier.
	 *  @param array $data Obligatoire, Tableau des informations permettant la mise à jour
	 * 				- ord	int		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * 				- line		int		Obligatoire, identifiant de l'interligne sur le panier
	 * 				- content	string	Obligatoire, contenu de l'interligne
	 */
	public static function updateSpacing( $data ){

		// Contrôle qu'il y ai bien du contenu à mettre a jour
		if( !array_key_exists('content', $data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes.', 'ERROR'), 1 );
		}

		// Controle que l'identifiant de la ligne est bien donné
		if( !array_key_exists('line', $data) || !is_numeric($data['line']) || $data['line'] < 0 ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes.', 'ERROR'), 1 );
		}

		$ord_id = self::getOrdID();

		if( isset($data['ord']) && is_numeric($data['ord']) && $data['ord'] > 0 ){

			if( !ord_orders_exists( $data['ord'], 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour de l\'interligne.', 'ERROR'), 2 );
			}
			$ord_id = $data['ord'];
		}

		try {
			$r_line_id = ord_products_get_spacing($ord_id, $data['line']);

			if ( $r_line_id && ria_mysql_num_rows($r_line_id) && isset($data['content']) && trim($data['content']) != '' ){
				ord_products_set_spacing($ord_id, $data['line'], $data['content']);

			}elseif( isset($data['content']) && trim($data['content']) != '' ) {
				ord_products_add_spacing($ord_id, false, $data['line'], false, $data['content']);

			}elseif( $r_line_id && ria_mysql_num_rows($r_line_id) && isset($data['content']) && trim($data['content']) == '' ){
				ord_products_del_spacing($ord_id, $data['line']);
			}
		}catch( Exception $e ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour de l\'interligne.', 'ERROR'), 3 );
		}

		return true;

	}

	/** Cette fonction permet de mettre à jour la référence sur le panier en cours.
	 * 	@param array $data Obligatoire, information permettant la mise à jour
	 * 				- ref : référence sur le panier
	 * 	@param	mixed	$ord_id		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * 	@param bool $only_cart Optionnel, par défaut la mise à jour ne se fait que sur un panier, mettre false pour ne pas tenir compte du statut
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateReference( $data, $ord_id=null, $only_cart=true ){
		// Contrôle que la référence est bien donnée en paramêtre
		if( !array_key_exists('ref', $data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes.', 'ERROR'), 1 );
		}

		$cart_id = self::getOrdID();

		if( is_numeric($ord_id) && $ord_id > 0 ){
			$state_exists = [ _STATE_BASKET, _STATE_BASKET_SAVE ];
			if( !$only_cart ){
				$state_exists = 0;
			}

			if( !ord_orders_exists( $ord_id, 0, $state_exists) ){
				throw new Exception( i18n::get('Le panier n\'existe pas.', 'ERROR'), 99);
			}
			$cart_id = $ord_id;
		}

		// Mise à jour de la référence
		if( !ord_orders_ref_update( $cart_id, $data['ref']) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour de la référence.', 'ERROR'), 2 );
		}

		return true;
	}

	/** Cette fonction permet de mettre à jour l'adresse de facturation d'un panier en cours.
	 * @param array $data Obligatoire, information permettant la mise à jour
	 * 				- adr : identifiant de l'adresse de facturation
	 * @param	int|null	$ord_id		Optionnel, identifiant d'un panier (panier en cours, sauvegardé ou devis lié à l'utilisateur en cours)
	 * @return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateInvoiceAddress( $data, $ord_id=null ){
		if( !array_key_exists('adr', $data) || !is_numeric($data['adr']) || !$data['adr'] ){
			throw new Exception( i18n::get('L\'adresse de facturation n\'existe pas.', 'ERROR'), 1);
		}
		$id = self::getOrdID();

		if( is_numeric($ord_id) && $ord_id > 0 ){

			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Le panier ou le devis n\'existe pas ou plus.', 'ERROR'), 99);
			}
			$id = $ord_id;

		}

		if( !ord_orders_adr_invoices_set($id, $data['adr']) ){
			throw new Exception( i18n::get('Une erreur est survenue lors de la sélection de l\'adresse de facturation.', 'ERROR'), 99);
		}

		return true;
	}

	/** Cette fonction permet de mettre à jour l'adresse de livraison d'un panier en cours.
	 * @param array $data Obligatoire, information permettant la mise à jour
	 * 				- adr : identifiant de l'adresse de livraison
	 * 				- is_store: true|false	si l'identifiant est un magasin, défaut: false
	 * @param	int|null	$ord_id		Optionnel, identifiant d'un panier (panier en cours, sauvegardé ou devis lié à l'utilisateur en cours)
	 * @return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateDeliveryAddress( $data, $ord_id=null ){

		if( !array_key_exists('adr', $data) || !is_numeric($data['adr']) || !$data['adr'] ){
			throw new Exception( i18n::get('L\'adresse de livraison n\'est pas renseignée.', 'ERROR'), 1);
		}
		$id = self::getOrdID();
		$custom = false;

		if( is_numeric($ord_id) && $ord_id > 0 ){

			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Le panier ou le devis n\'existe pas ou plus.', 'ERROR'), 99);
			}
			$id = $ord_id;
			$custom = true;

		}

		if( isset($data['is_store']) && is_bool($data['is_store']) && $data['is_store'] ){

			if( !dlv_stores_exists($data['adr'], true) ){
				throw new Exception( i18n::get('La livraison n\'est pas possible à cette adresse.', 'ERROR'), 100);
			}

			if( !ord_orders_adr_delivery_set($id) || !ord_orders_set_relay($id, null) ){
				throw new Exception( i18n::get('Une erreur est survenue lors de la mise à jour de l\'adresse de livraison.', 'ERROR'), 99);
			}

			if( !ord_orders_set_dlv_store($id, $data['adr']) ){
				throw new Exception( i18n::get('Une erreur est survenue lors de la mise à jour de l\'adresse de livraison.', 'ERROR'), 99);
			}

			return true;
		}

		if( !ord_orders_set_dlv_store($id) || !ord_orders_set_relay($id, null) ){
			throw new Exception( i18n::get('Une erreur est survenue lors de la sélection de l\'adresse de livraison.', 'ERROR'), 99);
		}

		if( !ord_orders_adr_delivery_set($id, $data['adr']) ){
			throw new Exception( i18n::get('Une erreur est survenue lors de la sélection de l\'adresse de livraison.', 'ERROR'), 99);
		}

		if( !$custom ){
			self::applyPort();
		}

		return true;
	}

	/** Cette fonction permet de mettre à jour la 3ieme adresse d'un panier en cours.
	 * @param array $data Obligatoire, information permettant la mise à jour
	 * 				- adr : identifiant de l'adresse
	 * @param	int|null	$ord_id		Optionnel, identifiant d'un panier (panier en cours, sauvegardé ou devis lié à l'utilisateur en cours)
	 * @return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateThirdAddress( $data, $ord_id=null ){
		if( !array_key_exists('adr', $data) || !is_numeric($data['adr']) || !$data['adr'] ){
			throw new Exception( i18n::get('L\'adresse n\'existe pas ou plus.', 'ERROR'), 1);
		}
		$id = self::getOrdID();

		if( is_numeric($ord_id) && $ord_id > 0 ){

			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Le panier ou le devis n\'existe pas ou plus.', 'ERROR'), 99);
			}
			$id = $ord_id;
		}

		if( !ord_orders_set_adr_final($id, $data['adr']) ){
			throw new Exception( i18n::get('Une erreur est survenue lors de la sélection de l\'adresse.', 'ERROR'), 99);
		}

		return true;
	}

	/** Cette fonction permet de définir un point relais comme lieu de livraison
	 * @param	array	$data	Obligatoire, Tableau pouvant contenir
	 * 									- code	Obligatoire, Code du point relais
	 * 									- ord	Optionnel, Identifiant de commande à mettre à jour
	 * @return	bool	Une exception est levée en cas d'erreur, true en cas de succès
	 */
	public static function updatetDeliveryRelay( $data ){
		if( !array_key_exists('code', $data) ){
			throw new Exception(i18n::get('Le code du point relais n\'est pas défini.', 'ERROR'), 1);
		}

		// Charge le panier en cours
		$ord_id = self::getOrdID();
		$custom = false;

		if( isset($data['ord']) && is_numeric($data['ord']) && $data['ord'] > 0 ){

			if( !ord_orders_exists( $data['ord'], 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite.', 'ERROR'), 2 );
			}
			$custom = true;
			$ord_id = $data['ord'];
		}

		$r_relay = dlv_relays_get( 0, $data['code'] );
		if( !ria_mysql_num_rows($r_relay) ){
			throw new Exception(i18n::get('Une erreur inattendue s\'est produite lors de la sélection du point relais.', 'ERROR'), 99);
		}

		$relay = ria_mysql_fetch_assoc( $r_relay );

		if(  !ord_orders_set_dlv_store($ord_id, false)
			|| !ord_orders_adr_delivery_set($ord_id, false )
			|| !ord_orders_set_relay($ord_id, $relay['id'])
		){
			throw new Exception(i18n::get('Une erreur inattendue s\'est produite lors de la sélection du point relais.', 'ERROR'), 99);
		}

		if(!$custom){
			self::applyPort();
		}

		return true;
	}

	/** Cette fonction permet de définir un magasin comme lieu de livraison
	 * 	@param array $data Obligatoire, information permettant la mise à jour
	 * 	@param int $ord_id Optionnel, permet de forcer l'action sur un panier qui ne serait pas celui en cours
	 * 	@return bool Une exception est levée en cas d'erreur, true en cas de succès
	 */
	public static function updatetDeliveryStore( $data, $ord_id=0 ){
		if( !array_key_exists('id', $data) ){
			throw new Exception(i18n::get('Le magasin n\'est pas défini.', 'ERROR'), 1);
		}

		// Charge le panier en cours
		if( !is_numeric($ord_id) || $ord_id <= 0 ){
			$cart = CartService::getInstance();
			if( !$cart->exists() ){
				throw new Exception( i18n::get('Aucun panier en cours.', 'ERROR'), 98 );
			}

			$ord_id = $cart->getID();
		}

		if( !dlv_stores_exists($data['id']) ){
			throw new Exception(i18n::get('Le magasin n\'a pas été identifié', 'ERROR'), 2);
		}

		if(  !ord_orders_set_dlv_store($ord_id, $data['id'])
			|| !ord_orders_adr_delivery_set( $ord_id, false )
			|| !ord_orders_set_relay($ord_id, null)
		){
			throw new Exception(i18n::get('Une erreur inattendue s\'est produite lors de la sélection du magasin.', 'ERROR'), 99);
		}

		return true;
	}

	/** Cette fonction permet de mettre à jour l'adresse de livraison identique à l'adresse de facturation.
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function defaultDeliveryAddress($ord_id=null){
		$User = CustomerService::getInstance();

		if( !$User->isConnected() ){
			throw new Exception(i18n::get('L\'adresse de livraison n\'a pas pu être mise à jour.'), 98);
		}
		$custom = true;

		if(!is_numeric($ord_id) || $ord_id <= 0){
			$custom = false;
			$ord_id = self::getOrdID();
		}

		if( !ord_orders_exists($ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite.', 'ERROR'), 2 );
		}

		if( !ord_orders_set_dlv_store($ord_id) || !ord_orders_set_relay($ord_id, null) ){
			throw new Exception(i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour de l\'adresse de livraison.', 'ERROR'), 99);
		}

		if( !ord_orders_adr_delivery_set($ord_id, $User->getInvoiceID()) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la mise à jour de l\'adresse de livraison.', 'ERROR'), 99);
		}

		if(!$custom){
			self::applyPort();
		}

		return true;
	}

	/** Cette fonction permet de mettre à jour la date de livraison souhaité.
	 * 	@param array $data Obligatoire, tableau contenant les informations nécessaire à la mise à jour
	 * 			- date : date de livraison souhaitée (peut-être vide)
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function updateDateDelivery( $data ){
		if( !array_key_exists('date', $data) ){
			throw new Exception( i18n::get('Une ou plusieurs informations obligatoires sont manquantes. La date de livraison n\'a pas été mise à jour.'), 1);
		}

		if( !ord_orders_set_date_livr(self::getOrdID(), $data['date']) ){
			throw new Exception( i18n::get('Une erreur est survenue lors de la sélection de l\'adresse de livraison.', 'ERROR'), 99);
		}

		return true;
	}

	/** Cette fonction permet de créer une nouvelle adresse de facturation.
	 * 	@param array $data Obligatoire, information nécessaire à son ajout
	 *
	 * 	@return bool Une exeception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function addAddressInvoice( $data ){
		// Création de la nouvelle adresse
		$adr_id = AddressActions::add( $data );

		if( !$adr_id ){
			throw new Exception( i18n::get('Une erreur inattendue est survenue lors de la création de l\'adresse.'), 99 );
		}

		self::updateInvoiceAddress( ['adr' => $adr_id] );

		return true;
	}

	/** Cette fonction permet de créer une nouvelle adresse de livraison.
	 * @param array $data	Obligatoire, information nécessaire à son ajout
	 * @param bool	$set	Optionnel, True pour mettre à jour l'adresse de livraison de la commande, false sinon
	 * @return bool Une exeception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function addAddressDelivery( $data, $set=true ){
		// Création de la nouvelle adresse
		$adr_id = AddressActions::add( $data );
		$set = is_bool($set) ? $set : true;

		if( !$adr_id ){
			throw new Exception( i18n::get('Une erreur inattendue est survenue lors de la création de l\'adresse.'), 99 );
		}

		if( $set ){
			self::updateDeliveryAddress( ['adr' => $adr_id] );
		}

		return true;
	}

	/** Cette fonction permet de sauvegarder le panier en cours
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function save(){
		// Charge le panier en cours
		$cart = CartService::getInstance();
		if( $cart->exists() ){
			if( !ord_orders_update_status( self::getOrdID(), _STATE_BASKET_SAVE) ){
				throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la sauvegarde de votre panier.', 'ERROR'), 99);
			}

			unset($_SESSION['ord_id']);
			$cart->destroy();
		}
		return true;
	}

	/** Cette fonction permet de reprendre un panier sauvegarder.
	 * 	@param $cart_id Obligatoire, identifiant du panier sauvegardé
	 * 	@param bool $refresh_cart Optionnel, par défaut à false, permet de mettre à jour le panier (tarif, libellé, référence)
	 * 	@return bool Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function resumeCart( $cart_id, $refresh_cart=false ){
		$user = CustomerService::getInstance();
		if( !ord_orders_exists($cart_id, $user->getID(), _STATE_BASKET_SAVE) ){
			throw new Exception( i18n::get('Le panier enregistré n\'a pas été identifié.'), 98);
		}

		// Sauvegarde le panier en cours (s'il y en a un)
		if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] ){
			self::save();
		}

		if( !ord_orders_update_status($cart_id, _STATE_BASKET) ){
			throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors de la reprise de votre panier sauvegardé.', 'ERROR'), 99);
		}

		$_SESSION['ord_id'] = $cart_id;
		return $refresh_cart ? ord_orders_refresh($cart_id, false, true, true, true) : true;
	}

	/** Cette fonction permet de gérer le paiement d'une commande.
	 * 	@param $data Obligatoire, information permettant le paiement de la commande
	 * 	@return bool Une exception sera levée en cas d'erreur, true si le paiement s'est corretement déroulée
	 */
	public static function payment( $data ){
		global $config;

		if( !array_key_exists('mode', $data) || !is_numeric($data['mode']) || $data['mode'] <= 0 ){
			throw new Exception( i18n::get('Le mode de paiement est absent.', 'ERROR'), 1);
		}

		// Hack Chadog - Uniquement pour le paiement par prélèvement
		if( $config['tnt_id'] == 171 && $config['wst_id'] == 232 ){
			if( (int) $data['mode'] == 7 ){
				if(
					!ord_orders_pay_type_set( self::getOrdID(), $data['mode'] )
					|| !ord_orders_state_update( self::getOrdID(), _STATE_WAIT_PAY )
					|| !ord_orders_state_update( self::getOrdID(), _STATE_PAY_CONFIRM )
				){
					throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors du règlement de votre commande.', 'ERROR'), 99);
				}

				return true;
			}
		}

		switch( (int) $data['mode'] ){
			case _PAY_CB:
				// Chaque module bancaire gèrera la validation de la commande
				// Par défaut celle-ci passera au statut "En attente de traitement"
				switch( Template::get('module-cb-payement') ){
					case _PAY_MODULE_PAYPLUG: // Module bancaire : PayPlug
						if( !array_key_exists('token', $data) ){
							throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors du règlement de votre abonnement', 'ERROR'), 99 );
						}

						require_once('PaymentExternal/Payplug.inc.php');
						// Réalise le paiement
						try{
							$payplug = new Payplug();
							$pay_id = $payplug->createSimplePayment(self::getOrdID(), false, $data['token']);
						}catch( Exception $e ){
							mail('<EMAIL>', 'error payplug extranet template 1', print_r($e, true));
							throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors du règlement de votre abonnement', 'ERROR'), 99 );
						}

						if( trim($pay_id) != '' ){
							if( $payplug->_getPaymentResult($pay_id) === false ){
								throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors du règlement de votre abonnement', 'ERROR'), 99 );
							}
						}

					break;
				}
				break;
			case _PAY_PAYPAL:
				require_once('PayPal.inc.php');
				try {
					PayPal::doPayment();
					exit;
				}
				catch (exception $e) {
					error_log('[ERROR] CartActions - payment() - paypal : '.$e->getMessage());
					throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors du règlement de votre commande.', 'ERROR'), 99);
				}
				break;
			case _PAY_CHEQUE:
			case _PAY_COMPTE:
			case _PAY_VIREMENT:
			default:
				// Règlement par chèque / virement / en compte
				// La commande passe au statut "En attente de règlement"
				if(
					!ord_orders_pay_type_set( self::getOrdID(), $data['mode'] )
					|| !ord_orders_state_update( self::getOrdID(), _STATE_WAIT_PAY )
				){
					throw new Exception( i18n::get('Une erreur inattendue s\'est produite lors du règlement de votre commande.', 'ERROR'), 99);
				}

			break;
		}

		return true;
	}

	/** Cette fonction permet d'enregistrer une valeur pour un champ avancé
	 * @param	int		$fld	Obligatoire, Identifiant du champ avancé
	 * @param	mixed	$value	Obligatoire, Valeur du champ avancé
	 * @return	bool	Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function setFieldValue($fld, $value){
		if( !fld_object_values_set(self::getOrdID(), $fld, $value, i18n::getLang()) ){
			throw new Exception( i18n::get('Une erreur est survenue lors de la mise à jour du panier', 'ERROR'), 1);
		}
		return true;

	}

	/** Cette fonction permet de définir le réprésentant de la commande ou de le mettre a jour
	 * @param	int 	$id	Identifiant du représentant
	 * @return	bool	Une exception sera levée en cas d'erreur, true en cas de succès
	 */
	public static function setSeller($id){
		global $config;

		if( !is_numeric($id) || $id <= 0 ){
			throw new Exception( i18n::get('Le représentant n\'existe pas', 'ERROR'), 1);
		}

		$res = ria_mysql_query('
			SELECT 1 FROM gu_users
			WHERE usr_tnt_id = '. $config['tnt_id'] .'
			AND usr_seller_id = '. $id .'
			AND usr_prf_id = '. PRF_SELLER .'
			AND usr_date_deleted IS NULL
		');

		if( !ria_mysql_num_rows($res) ){
			throw new Exception( i18n::get('Le représentant n\'existe pas', 'ERROR'), 1);
		}

		if( !ord_orders_set_seller_id(self::getOrdID(), $id) ){
			throw new Exception( i18n::get('Une erreur est survenue lors de la mise à jour du panier', 'ERROR'), 2);
		}
		return true;
	}

	/** Vérifie que le panier en cours est valide + contrôle le code promo s'il y en a un
	 * 	@return	void	Une exception sera levée en cas d'erreur
	 * 	@return int Un code en fonction des actions réalisés (1 : contrôle OK, 2 : contrôle OK + code promotion désactivé)
	 */
	public static function controlCart(){
		global $config;

		if( !isset($_SESSION['ord_id']) ){
			return;
		}

		// L'identifiant doit être un numérique supérieure à zéro
		if( !is_numeric($_SESSION['ord_id']) || $_SESSION['ord_id'] <= 0 ){
			unset($_SESSION['ord_id']);
			return;
		}

		// Récupère le compte client
		$user = CustomerService::getInstance();
		$user_id = $user->isConnected() ? $user->getID() : 0;

		if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] ){
			if( !ord_orders_exists($_SESSION['ord_id'], $user_id) ){
				unset($_SESSION['ord_id']);
				return;
			}
		}

		$res = ria_mysql_query('
			select oop_pmt_id
			from ord_orders_promotions
			where oop_tnt_id = '.$config['tnt_id'].'
				and oop_ord_id = '.$_SESSION['ord_id'].'
		');

		if( $res ){
			$cancel_promo = false;

			while( $r = ria_mysql_fetch_array($res) ){
				if( pmt_codes_is_applicable(null, $_SESSION['ord_id'], $r['oop_pmt_id']) !== true ){
					pmt_codes_cancel( $_SESSION['ord_id'], false, $r['oop_pmt_id'] );
					$cancel_promo = true;
				}
			}

			if( $cancel_promo ){
				ord_orders_update_totals( $_SESSION['ord_id'] );
			}
		}

		return !$cancel_promo ? '1' : '2';
	}

	/** Cette fonction permet de reprendre un panier via un code MD5.
	 * 	@param string $md5 Obligatoire, code MD5 de récupération du panier
	 * 	@return void Une exception sera levée en cas d'erreur
	 */
	public static function restoreCartFromMD5( $md5 ){
		if( !ord_restore_cart_from_md5($md5) ){
			throw new Exception( i18n::get('Le code de reprise du panier est erroné.') );
		}
	}

	/** Cette fonction permet de contrôler qu'un panier est valide pour l'étape de paiement.
	 * 	Il faut qu'un service de livraison soit renseigner ainsi qu'une adresse.
	 */
	public static function controlCartBeforePayment(){
		$cart = new CartService();
		if( !$cart->exists() ){
			// Aucun panier en cours
			return false;
		}

		if( !$cart->getServiceID() ){
			// Aucun service de livraison choisi
			return false;
		}

		return true;
	}

	/**	Cette méthode permet de raffraichir les frais de port de la commande
	 * @return	void
	 */
	public static function reloadPort(){
		self::applyPort();
	}

	/** Cette fonction permet de supprimer la ligne concernant les frais de port sur le panier en cours.
	 * 	@return empty
	 */
	public static function removePort(){
		// Charge le panier en cours
		$cart = CartService::getInstance();

		if( !$cart->exists() ){
			throw new Exception( i18n::get('Aucun panier en cours.', 'ERROR'), 98 );
		}

		if( !ord_orders_port_del( self::getOrdID() ) ){
			throw new Exception( i18n::get('Une erreur est survenue sur les frais de port.', 'ERROR'), 99 );
		}

		// S'assure que l'objet "Panier en cours" est bien à jour
		$cart = $cart->reload();
	}

	/**	Permet de supprimé virtuellement (masquer) un panier ou le panier en cours si non spécifié
	 * @param	int		$ord_id		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * @return	bool	True si le panier a été supprimé (masqué), une exception sera levée en cas d'erreur
	 */
	public static function deleteCart($ord_id=null){
		$cart_id = self::getOrdID();

		if( is_numeric($ord_id) && $ord_id > 0 ){

			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE]) ){
				throw new Exception( i18n::get('Le panier n\'existe pas.', 'ERROR'), 99);
			}

			$cart_id = $ord_id;
		}

		$User = CustomerService::getInstance();
		$user_id = $User->getID();

		if( !ord_orders_set_masked($cart_id, $user_id) ){
			throw new Exception( i18n::get('Le panier n\'a pas pu être supprimé.', 'ERROR'), 1);
		}

		return true;
	}

	/**	Permet d'archiver une commande, un panier ou le panier en cours si non spécifié
	 * @param	int		$ord_id		Optionnel, identifiant d'un panier (panier en cours ou sauvegardé lié à l'utilisateur en cours)
	 * @return	bool	True si la commande a été archivée, une exception sera levée en cas d'erreur
	 */
	public static function archive($ord_id=null){
		$cart_id = self::getOrdID();

		if( is_numeric($ord_id) && $ord_id > 0 ){

			if( !ord_orders_exists( $ord_id, 0, [_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_DEVIS]) ){
				throw new Exception( i18n::get('La commande n\'existe pas ou plus.', 'ERROR'), 99);
			}
			$cart_id = $ord_id;
		}

		if( !ord_orders_state_update($cart_id, _STATE_ARCHIVE, '', false) ){
			throw new Exception( i18n::get('Une erreur est survenue.', 'ERROR'), 1);
		}
		return true;
	}

	/** Cette classe n'est pas instanciable. */
	private function __construct(){}

	/** Cette fonction permet de récupérer l'identifiant du panier en cours qui est stocké en session.
	 * 	@return int L'identifiant du panier en cours, false si aucun panier en cours
	 */
	private static function getOrdID(){
		$ord_id = false;

		if( isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']) && $_SESSION['ord_id'] > 0 ){
			$ord_id = $_SESSION['ord_id'];
		}

		return $ord_id;
	}

	/** Cette fonction permet de réaliser des actions après une modification sur le contenu du panier.
	 * 	Ex. Ajout d'un produit, application d'un code promotion, modification de la quantité commandée, suppression d'une nomenclature, etc...
	 *
	 * 	@return empty
	 */
	private static function postActionLineOrder( $params=[] ){
		global $hook;

		$hook->do_action( 'CartActions_postActionLineOrder', [] );

		// Option déterminant si l'action est réaliser après la mise à jour d'une ligne
		$updateContent = ria_array_get( $params, 'update-content', true );

		self::applyPort( $updateContent );
	}

	/** Cette fonction applique les frais de port à la commande
	 * 	@param bool $change_content_cart Optionnel, permet de déterminer si l'action est réalisé suite à la mise à jour du contenu du panier (ex. ajout d'un produit, mise à jour d'une quantité...)
	 * 	@return bool true en cas de succès, false dans le cas contraire
	 */
	private static function applyPort( $change_content_cart=false ){
		global $config, $hook;

		if( $hook->do_action('CartActions_replaceApplyPort') ){
			return;
		}

		// Réalisation d'action pré-calcul de frais de port suite à la mise à jour du contenu du panier
		if( $change_content_cart ){
			// Si l'estimation de frais de port est activé, alors toute estimation est réinitialisé
			// Suppression de toutes les estimations pouvant avoir été fais sur la commande
			if( Template::get('order-specific-shipping-charges') ){
				ria_mysql_query('
					delete from fld_object_values
					where pv_tnt_id = '.$config['tnt_id'].'
						and pv_obj_id_0 = '.self::getOrdID().'
						and pv_fld_id in ('._FLD_ORD_SPECIFIC_SHIPPING_CHARGES_1.', '._FLD_ORD_SPECIFIC_SHIPPING_CHARGES_2.', '._FLD_ORD_SPECIFIC_SHIPPING_CHARGES_3.', '._FLD_ORD_SPECIFIC_SHIPPING_CHARGES_4.', '._FLD_ORD_SPECIFIC_SHIPPING_CHARGES_5.', '._FLD_ORD_SPECIFIC_SHIPPING_CHARGES_6.', '._FLD_ORD_SPECIFIC_SHIPPING_CHARGES_7.', '._FLD_ORD_SPECIFIC_SHIPPING_CHARGES_8.', '._FLD_ORD_SPECIFIC_SHIPPING_CHARGES_9.')
				');
			}
		}

		// Charge le panier en cours
		$cart = CartService::getInstance()->reload();
		if( !$cart->exists() ){
			throw new Exception( i18n::get('Aucun panier en cours.', 'ERROR'), 98 );
		}

		// Charge la dernière version des services de livraison
		$delivery = DeliveryService::getInstance();
		$res = $delivery->reload()->getPort( [], [], false )->getData();

		if( isset($res['srv'.$cart->getServiceID()]) ){
			$port = $res[ 'srv'.$cart->getServiceID() ];

			foreach( $config['dlv_prd_references'] as $one_port_ref ){
				ord_products_del_ref( self::getOrdID(), $one_port_ref, false, -1, false );
			}

			ord_products_add_free( self::getOrdID(), $port['prd'], 'Frais de port', $port['port'], 1, null, '', _TVA_RATE_DEFAULT );

			// S'assure que l'objet "Panier en cours" est bien à jour
			$cart = $cart->reload();
		}
	}
}