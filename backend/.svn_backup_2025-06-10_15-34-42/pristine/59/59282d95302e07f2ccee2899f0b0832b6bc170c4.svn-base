<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  67773 => 'Solomon Telekom',
  67774 => 'Solomon Telekom',
  67775 => 'Solomon Telekom',
  67776 => 'Solomon Telekom',
  67777 => 'Solomon Telekom',
  67778 => 'Solomon Telekom',
  677790 => 'Solomon Telekom',
  677791 => 'Solomon Telekom',
  677792 => 'Solomon Telekom',
  677793 => 'Solomon Telekom',
  677794 => 'Solomon Telekom',
  67784 => 'BMobile',
  67785 => 'BMobile',
  67786 => 'BMobile',
  67787 => 'BMobile',
  67788 => 'BMobile',
  67789 => 'BMobile',
  67791 => 'Satsol',
  67792 => 'Satsol',
  677930 => 'Satsol',
  677931 => 'Satsol',
  677932 => 'Satsol',
  67794 => 'Smile',
  67795 => 'Smile',
  67796 => 'Smile',
  67797 => 'Smile',
  67798 => 'Smile',
  67799 => 'Smile',
);
