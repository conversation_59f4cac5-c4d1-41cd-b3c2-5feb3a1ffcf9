<?php
namespace Riashop\PriceWatching\models\LinearRaised;
/**
 * \ingroup LinearRaisedModel
 */

  /** \class prw_followed_users
  * \brief Cette class permet la gestion des opérations CRUD de la table prw_followed_users
  */
class prw_followed_users {

	private static $managers = array(PRF_ADMIN, PRF_SELLER);

	private static function validInteger($int) {
		return is_numeric($int) && $int > 0;
	}

	/**
	 * Cette fonction permet d'ajouté un utilisateur à un assortiment
	 *
	 * @param integer $pfl_id Identifiant de l'assortiment
	 * @param integer $prf_id Identifiant du profil utilisateur
	 * @param integer $usr_id Identifiant de l'utilisateur
	 * @return boolean Retourne True si pas d'erreur, sinon false
	 */
	public static function add($pfl_id, $prf_id, $usr_id) {
		if (!self::validInteger($pfl_id)) {
			throw new \InvalidArgumentException("pfl_id doit être un entier", 1);
		}
		if (!self::validInteger($prf_id)) {
			throw new \InvalidArgumentException("prf_id doit être un entier", 1);
		}
		if (!self::validInteger($usr_id)) {
			throw new \InvalidArgumentException("usr_id doit être un entier", 1);
		}

		global $config;

		$values = array(
			$config['tnt_id'],
			$pfl_id,
			$prf_id,
			$usr_id,
			'now()',
		);

		$insert = '
			insert into prw_followed_users
				(pfu_tnt_id, pfu_pfl_id, pfu_prf_id, pfu_usr_id, pfu_date_created)
			values
				('.implode(', ', $values).')
		';

		return ria_mysql_query($insert);
	}

	/**
	 * Cette fonction permet de récupérer les utilisateurs lié a un assortiement
	 *
	 * @param integer $pfl_id Identifiant de l'assortiment
	 * @return array|boolean Retourne un tableau avec les clé suivante
	 * 							- plf_id : identifiant de la l'assortiment
	 * 							- prf_id : identifiant du profile du client
	 * 							- usr_id : identifiant du client
	 * 							- usr_ref : référence du client
	 * 							- date_created : date de création du client
	 * 							- date_modified : date de modification du client
	 */
	public static function get($pfl_id, $prf_id=null) {
		if (!self::validInteger($pfl_id)) {
			return false;
		}

		$prf_ids = null;

		if (!is_null($prf_id)) {
			$prf_ids = control_array_integer($prf_id);

			if (!$prf_ids) {
				return false;
			}
		}

		global $config;

		$select = '
			select pfu_pfl_id as pfl_id,
				pfu_id as id,
				pfu_prf_id as prf_id,
				pfu_usr_id as usr_id,
				usr_ref as usr_ref,
				pfu_date_created as date_created,
				pfu_date_modified as date_modified
			from prw_followed_users
			join gu_users on pfu_tnt_id=usr_tnt_id and pfu_usr_id=usr_id
			where pfu_tnt_id = '.$config['tnt_id'].'
				and pfu_pfl_id='.$pfl_id.'
				and pfu_date_deleted is null
				and usr_date_deleted is null
		';

		if (!is_null($prf_ids)) {
			$select .= ' and pfu_prf_id in ('.implode(', ', $prf_ids).')';
		}

		$r = ria_mysql_query($select);

		if (!$r || !ria_mysql_num_rows($r)) {
			return false;
		}

		return $r;
	}

	/**
	 * Cette fonction permet de récupérer que les responssables lié à un assortiement
	 *
	 * @param integer $pfl_id Identifiant de l'assortiment
	 * @return array|boolean Retourne un tableau avec les clé suivante
	 * 							- plf_id : identifiant de la l'assortiment
	 * 							- prf_id : identifiant du profile du client
	 * 							- usr_id : identifiant du client
	 * 							- date_created : date de création du client
	 * 							- date_modified : date de modification du client
	 */
	public static function getOnlyManagers($pfl_id) {
		return self::get($pfl_id, self::$managers);
	}
	/**
	 * Cette fonction permet de supprimer les utilisateur lié a un assortiment
	 *
	 * @param integer $pfl_id Identifiant de l'assortiment
	 * @param integer $usr_id Facultatif, Identifiant de l'utilisateur
	 * @return boolean Retourne true si bien connecté sinon false.
	 */
	public static function delete($pfl_id, $usr_id=null) {
		if (!self::validInteger($pfl_id)) {
			return false;
		}

		global $config;

		$delete = '
			update prw_followed_users
				set pfu_date_deleted = now()
			where pfu_tnt_id = '.$config['tnt_id'].'
				and pfu_pfl_id='.$pfl_id.'
				and pfu_date_deleted is null
		';

		if( is_numeric($usr_id) && $usr_id > 0 ){
			$delete .= ' and pfu_usr_id='.$usr_id;
		}

		return ria_mysql_query($delete);
	}
}