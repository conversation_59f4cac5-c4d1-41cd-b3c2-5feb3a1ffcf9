# LinksGetChannelCatalogCategoriesLink

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**label** | **string** | The label corresponding to the link. This label is automatically translated based on the Accept-Language http header. | [optional] 
**doc_url** | [**\Swagger\Client\Model\BeezUPCommonDocUrl**](BeezUPCommonDocUrl.md) |  | [optional] 
**description** | **string** | The description of the link | [optional] 
**href** | [**\Swagger\Client\Model\BeezUPCommonHref**](BeezUPCommonHref.md) |  | 
**operation_id** | [**\Swagger\Client\Model\BeezUPCommonOperationId**](BeezUPCommonOperationId.md) |  | [optional] 
**method** | [**\Swagger\Client\Model\BeezUPCommonHttpMethod**](BeezUPCommonHttpMethod.md) |  | [optional] 
**parameters** | [**map[string,\Swagger\Client\Model\BeezUPCommonLinkParameter3]**](BeezUPCommonLinkParameter3.md) |  | [optional] 
**url_templated** | **bool** | indicates whether the href is templated or not | [optional] 
**all_required_params_provided** | **bool** | indicates whether all required params have been provided | [optional] 
**all_optional_params_provided** | **bool** | indicates whether all optionals params have been provided | [optional] 
**info** | [**\Swagger\Client\Model\BeezUPCommonInfoSummaries**](BeezUPCommonInfoSummaries.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


