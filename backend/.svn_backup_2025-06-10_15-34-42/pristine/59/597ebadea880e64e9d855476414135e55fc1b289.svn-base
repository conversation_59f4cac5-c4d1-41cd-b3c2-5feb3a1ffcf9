<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  97250 => 'Pelephone',
  97251 => 'Xphone',
  97252 => 'Cellcom',
  97253 => 'Hot Mobile',
  97254 => 'Orange',
  9725501 => 'Beezz',
  9725522 => 'Home Cellular',
  9725523 => 'Home Cellular',
  9725532 => 'Free Telecom',
  9725533 => 'Free Telecom',
  9725566 => '<PERSON><PERSON>',
  9725567 => '<PERSON><PERSON>',
  9725568 => '<PERSON><PERSON> Levy',
  9725570 => 'Cellact',
  9725571 => 'Cellact',
  9725587 => 'Alon',
  9725588 => 'Alon',
  9725589 => 'Alon',
  9725592 => 'Telzar',
  9725593 => 'Telzar',
  9725594 => 'Telzar',
  9725595 => 'Telzar',
  9725596 => 'Telzar',
  9725597 => 'Telzar',
  9725598 => 'Telzar',
  9725599 => 'Telzar',
  97256 => 'Wataniya',
  97257 => 'Hot Mobile',
  97258 => 'Golan Telecom',
  97259 => 'Jawwal',
);
