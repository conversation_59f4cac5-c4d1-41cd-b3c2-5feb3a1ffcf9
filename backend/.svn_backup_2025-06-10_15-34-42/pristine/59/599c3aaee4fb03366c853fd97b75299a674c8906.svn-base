<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/datastore/v1/entity.proto

namespace Google\Cloud\Datastore\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A unique identifier for an entity.
 * If a key's partition ID or any of its path kinds or names are
 * reserved/read-only, the key is reserved/read-only.
 * A reserved/read-only key is forbidden in certain documented contexts.
 *
 * Generated from protobuf message <code>google.datastore.v1.Key</code>
 */
class Key extends \Google\Protobuf\Internal\Message
{
    /**
     * Entities are partitioned into subsets, currently identified by a project
     * ID and namespace ID.
     * Queries are scoped to a single partition.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PartitionId partition_id = 1;</code>
     */
    private $partition_id = null;
    /**
     * The entity path.
     * An entity path consists of one or more elements composed of a kind and a
     * string or numerical identifier, which identify entities. The first
     * element identifies a _root entity_, the second element identifies
     * a _child_ of the root entity, the third element identifies a child of the
     * second entity, and so forth. The entities identified by all prefixes of
     * the path are called the element's _ancestors_.
     * An entity path is always fully complete: *all* of the entity's ancestors
     * are required to be in the path along with the entity identifier itself.
     * The only exception is that in some documented cases, the identifier in the
     * last path element (for the entity) itself may be omitted. For example,
     * the last path element of the key of `Mutation.insert` may have no
     * identifier.
     * A path can never be empty, and a path can have at most 100 elements.
     *
     * Generated from protobuf field <code>repeated .google.datastore.v1.Key.PathElement path = 2;</code>
     */
    private $path;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Cloud\Datastore\V1\PartitionId $partition_id
     *           Entities are partitioned into subsets, currently identified by a project
     *           ID and namespace ID.
     *           Queries are scoped to a single partition.
     *     @type \Google\Cloud\Datastore\V1\Key\PathElement[]|\Google\Protobuf\Internal\RepeatedField $path
     *           The entity path.
     *           An entity path consists of one or more elements composed of a kind and a
     *           string or numerical identifier, which identify entities. The first
     *           element identifies a _root entity_, the second element identifies
     *           a _child_ of the root entity, the third element identifies a child of the
     *           second entity, and so forth. The entities identified by all prefixes of
     *           the path are called the element's _ancestors_.
     *           An entity path is always fully complete: *all* of the entity's ancestors
     *           are required to be in the path along with the entity identifier itself.
     *           The only exception is that in some documented cases, the identifier in the
     *           last path element (for the entity) itself may be omitted. For example,
     *           the last path element of the key of `Mutation.insert` may have no
     *           identifier.
     *           A path can never be empty, and a path can have at most 100 elements.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Datastore\V1\Entity::initOnce();
        parent::__construct($data);
    }

    /**
     * Entities are partitioned into subsets, currently identified by a project
     * ID and namespace ID.
     * Queries are scoped to a single partition.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PartitionId partition_id = 1;</code>
     * @return \Google\Cloud\Datastore\V1\PartitionId|null
     */
    public function getPartitionId()
    {
        return isset($this->partition_id) ? $this->partition_id : null;
    }

    public function hasPartitionId()
    {
        return isset($this->partition_id);
    }

    public function clearPartitionId()
    {
        unset($this->partition_id);
    }

    /**
     * Entities are partitioned into subsets, currently identified by a project
     * ID and namespace ID.
     * Queries are scoped to a single partition.
     *
     * Generated from protobuf field <code>.google.datastore.v1.PartitionId partition_id = 1;</code>
     * @param \Google\Cloud\Datastore\V1\PartitionId $var
     * @return $this
     */
    public function setPartitionId($var)
    {
        GPBUtil::checkMessage($var, \Google\Cloud\Datastore\V1\PartitionId::class);
        $this->partition_id = $var;

        return $this;
    }

    /**
     * The entity path.
     * An entity path consists of one or more elements composed of a kind and a
     * string or numerical identifier, which identify entities. The first
     * element identifies a _root entity_, the second element identifies
     * a _child_ of the root entity, the third element identifies a child of the
     * second entity, and so forth. The entities identified by all prefixes of
     * the path are called the element's _ancestors_.
     * An entity path is always fully complete: *all* of the entity's ancestors
     * are required to be in the path along with the entity identifier itself.
     * The only exception is that in some documented cases, the identifier in the
     * last path element (for the entity) itself may be omitted. For example,
     * the last path element of the key of `Mutation.insert` may have no
     * identifier.
     * A path can never be empty, and a path can have at most 100 elements.
     *
     * Generated from protobuf field <code>repeated .google.datastore.v1.Key.PathElement path = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getPath()
    {
        return $this->path;
    }

    /**
     * The entity path.
     * An entity path consists of one or more elements composed of a kind and a
     * string or numerical identifier, which identify entities. The first
     * element identifies a _root entity_, the second element identifies
     * a _child_ of the root entity, the third element identifies a child of the
     * second entity, and so forth. The entities identified by all prefixes of
     * the path are called the element's _ancestors_.
     * An entity path is always fully complete: *all* of the entity's ancestors
     * are required to be in the path along with the entity identifier itself.
     * The only exception is that in some documented cases, the identifier in the
     * last path element (for the entity) itself may be omitted. For example,
     * the last path element of the key of `Mutation.insert` may have no
     * identifier.
     * A path can never be empty, and a path can have at most 100 elements.
     *
     * Generated from protobuf field <code>repeated .google.datastore.v1.Key.PathElement path = 2;</code>
     * @param \Google\Cloud\Datastore\V1\Key\PathElement[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setPath($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\Datastore\V1\Key\PathElement::class);
        $this->path = $arr;

        return $this;
    }

}

