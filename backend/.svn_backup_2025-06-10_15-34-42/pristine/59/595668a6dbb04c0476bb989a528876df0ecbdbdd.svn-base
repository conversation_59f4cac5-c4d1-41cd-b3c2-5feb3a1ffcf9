# CreateRuleRequest

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**optimisation_action_name** | [**\Swagger\Client\Model\OptimisationActionName**](OptimisationActionName.md) |  | 
**rule_name** | **string** | The name of the rule | 
**report_filter_id** | **string** | The report filter to use for the rule | 
**start_utc_date** | [**\DateTime**](\DateTime.md) | The start validity utc date of the rule | [optional] 
**end_utc_date** | [**\DateTime**](\DateTime.md) | The end validity utc date of the rule | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


