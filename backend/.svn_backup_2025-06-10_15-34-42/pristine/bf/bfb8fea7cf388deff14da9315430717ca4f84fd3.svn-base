<?php

	/**	\file fidelity.php
	 *	Cette page affiche des statistiques sur le nombre de comptes ayant réalisé de 0 à N achats.
	 */

	// Vérifie que l'utilisateur en cours a bien accès à cette page
	if( !gu_user_is_authorized('_RGH_ADMIN_STATS_CUSTOMER_FIDELITY') && !gu_user_is_authorized('_RGH_ADMIN_STATS_YUTO')  ){
		header('HTTP/1.0 403 Forbidden');
		exit;
	}

	// Fil d'ariane
	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Statistiques'), '/admin/stats/index.php' )
		->push( _('Clients'), '/admin/stats/customers/index.php' )
		->push( _('Fidélité des clients') );

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', _('Fidélité des clients').' - '._('Statistiques'));
	require_once('admin/get-filters.php');
	require_once('admin/skin/header.inc.php');

?>
	<h2><?php print _('Fidélité des clients'); ?></h2>

	<div class="stats-menu">
		<form method="get" action="" id="riapicker-form">
			<div id="riadatepicker" class="riapicker"></div>
			<div class="clear"></div>
		</form>
	</div>
	<?php view_import_highcharts(); ?>
	<?php
		require_once( 'admin/highcharts/graph-fidelity.php' );

		if(strtotime(dateparse($date1LastYear)) != strtotime(dateparse($date2LastYear))){
			$libelle_last_year = _('Du') . ' ' . ria_date_format($date1LastYear) . ' ' . _('au') . ' ' . ria_date_format($date2LastYear);
		}else{
			$libelle_last_year = _('Du') . ' ' . ria_date_format($date1LastYear);
		}

		if(strtotime(dateparse( $_GET['date1'])) != strtotime(dateparse($_GET['date2']))){
			$libelle_current_year = _('Du') . ' ' . ria_date_format($_GET['date1']) . ' ' . _('au') . ' ' . ria_date_format($_GET['date2']);
		}else{
			$libelle_current_year = _('Du') . ' ' . ria_date_format($_GET['date1']);
		}
	?>
	<table class="fidelity ui-sortable">
		<caption><?php print _('Nombre de comptes par nombre de commandes')?></caption>
		<thead>
			<tr>
				<th id="customers"><?php echo _('Comptes clients'); ?></th>
				<th id="val_last_year" class="align-right"><?php echo $libelle_last_year; ?></th>
				<th id="val_current_year" class="align-right"><?php echo $libelle_current_year; ?></th>
			</tr>
		</thead>
		<tbody>
			<tr>
				<td id="customers" ><?php echo _('Nombre de comptes créés dans la période'); ?></td>
				<td id="val_last_year" data-label="<?php echo $libelle_last_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_last_year_total); ?></td>
				<td id="val_current_year" data-label="<?php echo $libelle_current_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_total); ?></td>
			</tr>
			<tr>
				<td id="customers" ><?php echo _('Avec une commande ou plus'); ?></td>
				<td id="val_last_year" data-label="<?php echo $libelle_last_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_last_year_1_orders); ?></td>
				<td id="val_current_year" data-label="<?php echo $libelle_current_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_1_orders); ?></td>
			</tr>
			<tr>
				<td id="customers" ><?php echo _('Avec deux commandes ou plus'); ?></td>
				<td id="val_last_year" data-label="<?php echo $libelle_last_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_last_year_2_orders); ?></td>
				<td id="val_current_year" data-label="<?php echo $libelle_current_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_2_orders); ?></td>
			</tr>
			<tr>
				<td id="customers" ><?php echo _('Avec trois commandes ou plus'); ?></td>
				<td id="val_last_year" data-label="<?php echo $libelle_last_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_last_year_3_orders); ?></td>
				<td id="val_current_year" data-label="<?php echo $libelle_current_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_3_orders); ?></td>
			</tr>
			<tr>
				<td id="customers" ><?php echo _('Avec quatre commandes ou plus'); ?></td>
				<td id="val_last_year" data-label="<?php echo $libelle_last_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_last_year_4_orders); ?></td>
				<td id="val_current_year" data-label="<?php echo $libelle_current_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_4_orders); ?></td>
			</tr>
			<tr>
				<td id="customers" ><?php echo _('Avec cinq commandes ou plus'); ?></td>
				<td id="val_last_year" data-label="<?php echo $libelle_last_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_last_year_5_orders); ?></td>
				<td id="val_current_year" data-label="<?php echo $libelle_current_year; ?> : " class="align-right"><?php echo ria_number_format($nb_users_5_orders); ?></td>
			</tr>
		</tbody>
	</table>
	<script src="/admin/js/riaSelectors.js?1"></script>
	<script>
		var urlHighcharts = '/admin/stats/fidelity.php';
		var riadatepicker_upd_url = '';
		<?php view_date_initialized( 0, '', false, array() ); ?>
	</script>

<?php
	require_once('admin/skin/footer.inc.php');
?>