<?php

    /** \file ncmd-add-products.php
     *  Ce fichier intervient dans le processus de création de commande, et permet l'ajout de produits à une commande en cours de création
     */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_CREATE');
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

	unset($error);

	$refresh = false;
	if( isset($_POST['add']) ){
		if( !isset($_POST['products']) || !trim($_POST['products']) ){
			$_POST['batch'] = '';
			$error = _("Votre commande ne contient aucun produit");
		}elseif( !isset($_SESSION['admin_ord_id']) ){
			$_POST['batch'] = $_POST['products'];
			$error = _("Une erreur inattendue est survenue lors de la création de la commande.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur.");
		}else{
			$have_rows = false;
			// Ajout des produits
			$lines = preg_split( '/\r\n|\r|\n/', $_POST['products'] );
			foreach( $lines as $key_batch=>$l ){
				$v = preg_split( '/ |\t/', $l );
				if (sizeof($v) < 2){
					continue;
				}
				list($prd_ref, $qty) = $v;

				$qte_min = $qte_steps = 1;

				$prd_id = prd_products_get_id($prd_ref);
				if (!$prd_id){
					continue;
				}

				$rcolisage = prd_colisage_classify_get( 0, $prd_id );

				if( $rcolisage && mysql_num_rows($rcolisage) ){
					$colisage = mysql_fetch_assoc( $rcolisage );
					$qte_steps = $colisage['qte'];
				}

				if( $qte_min < $qte_steps ){
					$qte_min = $qte_steps;
				}

				if( $qty < $qte_min ){
					$qty = $qte_min;
				}

				// Coontrôle sur l'unité de vente
				$qty = ceil( $qty / $qte_steps ) * $qte_steps;

				if( !ord_products_add_ref( $_SESSION['admin_ord_id'], $prd_ref, $qty, '', '', false ) ){
					$error = _("Une erreur inattendue est survenue lors de l'ajout au panier d'un produit.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour nous signaler l'erreur.");
				}else{
					unset($lines[$key_batch]);
					
					if( !$have_rows ){
						$have_rows = true;
					}
				}
			}

			if (count($lines)) {
				$_POST['batch'] = implode("\n", $lines);
			}
			
			if( !isset($error) && $have_rows ){
				$refresh = true;
			}
		}
	}

	define('ADMIN_PAGE_TITLE', _('Ajout par lot'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');
?>

	<?php if( !isset( $_POST['batch']) ){ ?>
	
	<p><?php print _('Indiquez, sur des lignes différentes, les références des articles suivis d\'un (ou de plusieurs) espace(s) puis de la quantité souhaitée. Par exemple :'); ?></p>
	<pre>6190513 3 
6430001 5 
6531713 4</pre>

	<form id="batch-order" action="/admin/ajax/orders/ncmd-add-products.php" method="post">
		<?php 
		if (isset($error)) {
			print '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
		}
		?>
		<textarea name="batch" id="batch" cols="92" rows="10"><?php print isset($_POST['products']) ? htmlspecialchars($_POST['products']): '' ?></textarea>
		<div class="actions"><input type="submit" value="<?php print _('Analyser'); ?>" name="analyze" class="btn-action"/></div>
	</form>

	<p class="tip"><?php print _('Vous pouvez préparer la liste des produits dans un tableur comme Microsoft Excel ® ou OpenOffice.org, puis copier-coller ci-dessus la liste désirée.'); ?></p>
	
	<?php } else {  ?>
		<?php
			if (isset($error)) {
				print '<div class="error">'.nl2br($error).'</div>';
			}
		?>
	<table>
		<thead>
			<tr>
				<th id="prd-line" class="line"><?php print _('Ligne'); ?></th>
				<th id="prd-ref"><?php print _('Référence'); ?></th>
				<th id="prd-name"><?php print _('Désignation'); ?></th>
				<th id="prd-qte" class="numeric"><?php print _('Quantité'); ?></th>
			</tr>
		</thead>
		<tbody>
		<?php
			$erroranalyze = false; $onerowmin = false;
			$_POST['batch'] = preg_replace( '/[ \t]+/', ' ', $_POST['batch'] );
			$lines = preg_split( '/\r\n|\r|\n/', $_POST['batch'], -1, PREG_SPLIT_NO_EMPTY );
			$count = 0;

			{ // Récupère les références données dans l'ajout rapide
				$ar_ref_prd = array();
				foreach( $lines as $l ){
					$v = preg_split( '/ |\t/', $l );
					$ar_ref_prd[] = trim(strtoupper2($v[0]));
				}
			}

			{ // Contruit à tableau des articles selon les références données en ne réalisant qu'une seule requête
				$ar_products = array();
				
				if (count($ar_ref_prd)) {
					$r_product = prd_products_get_simple( 0, $ar_ref_prd, false, 0, false, false, false, false, array('childs'=>true, 'orderable'=>true) );
					if ($r_product) {
						while ($product = ria_mysql_fetch_assoc($r_product)) {
							$ar_products[$product['ref']] = $product;
						}
					}
				}
			}
			
			// Parcours chaque ligne de la commande rapide
			foreach( $lines as $l ){
				$count++;
				$v = preg_split( '/ |\t/', $l );
				$rowv = str_pad( $count, 5, '0', STR_PAD_LEFT );
				
				if( sizeof($v)<2 ){
					$erroranalyze = true;
					?>
					<tr class="error">
						<td headers="prd-line" class="line"><?php print $rowv; ?></td>
						<td colspan="3"><strong><?php print htmlspecialchars($l); ?></strong> : <?php printf(_('La valeur <strong>%s</strong> a provoqué une erreur d\'analyse'), htmlspecialchars($l)); ?></td>
					</tr>
					<?php
				}else{
					$ref = trim(strtoupper2($v[0]));
					
					if( !is_numeric($v[1]) || $v[1]<=0 ){
						$erroranalyze = true;
						?>
						<tr class="error">
							<td headers="prd-line" class="line"><?php print $rowv; ?></td>
							<td colspan="3"><strong><?php print htmlspecialchars($l); ?></strong> : <?php printf(_('La quantité <strong>%s</strong> a provoqué une erreur d\'analyse.'), htmlspecialchars($v[1])); ?></td>
						</tr>
						<?php
					}else{
						// Si la référence n'esst pas dans le tableau des articles, on génère une erreur
						if (!array_key_exists($ref, $ar_products)) {
							$erroranalyze = true;
							?>
							<tr class="error">
								<td headers="prd-line" class="line"><?php print $rowv; ?></td>
								<td colspan="3"><strong><?php print htmlspecialchars($l); ?></strong> : <?php printf(_('La référence <strong>%s</strong> n\'a pas été trouvée parmis les produits commandables de votre catalogue.'), htmlspecialchars($v[0])); ?></td>


							</tr>
							<?php
						}else{
							$onerowmin = true;
							$qty = $v[1];
							
							$product = $ar_products[$ref];
							if( !ria_array_key_exists( array('qte-min', 'qte-max', 'qte-step'), $product ) ){
								$product['qte-min'] = $product['qte-step'] = 1;

								$rcolisage = prd_colisage_classify_get( 0, $product['id'] );

								if( $rcolisage && mysql_num_rows($rcolisage) ){
									$colisage = mysql_fetch_assoc( $rcolisage );
									$product['qte-step'] = $colisage['qte'];
								}

								if( $product['qte-min'] < $product['qte-step'] ){
									$product['qte-min'] = $product['qte-step'];
								}
							}

							if( $qty < $product['qte-min'] ){
								$qty = $product['qte-min'];
							}

							// Coontrôle sur l'unité de vente
							$qty = ceil( $qty / $product['qte-step'] ) * $product['qte-step'];

							?>
							<tr>
								<td headers="prd-line" class="line"><?php print $rowv; ?></td>
								<td headers="prd-ref"><?php print htmlspecialchars($product['ref']); ?></td>
								<td headers="prd-name"><?php print htmlspecialchars($product['name']); ?></td>
								<td headers="prd-qte" class="qte"><?php print $qty; ?></td>
							</tr>
							<?php
						}
					}
					
				}
			}
		?>
		</tbody>
		</table>
		<form id="batch-order" action="/admin/ajax/orders/ncmd-add-products.php" method="post">
			<?php 
			if (isset($error)) {
				print '<div class="error">' . nl2br(htmlspecialchars($error)) . '</div>';
			}
			?>
			<input type="hidden" name="products" value="<?php print htmlspecialchars($_POST['batch']); ?>" />
			<div class="actions">
				<input type="submit" value="<?php print _('Retour'); ?>" name="cancel" class="btn-action"/>
				<?php if( $onerowmin ){ ?>
				<input type="submit" value="<?php print _('Ajouter au panier'); ?>" name="add" class="btn-action"/>
				<?php } ?>
			</div>
		</form>
	<?php
	}

	?>
	<script><!--
		$('document').ready(function(){
			<?php if( $refresh ){ ?>
				window.parent.parent_refresh();
				window.parent.hidePopup();
			<?php } ?>
		});
	--></script>
<?php
	require_once('admin/skin/footer.inc.php');
?>