<?php
/**
 * CustomerIndexLovLinks
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * CustomerIndexLovLinks Class Doc Comment
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class CustomerIndexLovLinks implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'customerIndexLovLinks';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'offer_lov' => '\Swagger\Client\Model\BeezUPCommonLOVLink3',
        'active_offer_lov' => '\Swagger\Client\Model\BeezUPCommonLOVLink3',
        'customer_status_lov' => '\Swagger\Client\Model\BeezUPCommonLOVLink3',
        'beez_up_time_zone_lov' => '\Swagger\Client\Model\BeezUPCommonLOVLink3',
        'store_country_lov' => '\Swagger\Client\Model\BeezUPCommonLOVLink3',
        'store_sector_lov' => '\Swagger\Client\Model\BeezUPCommonLOVLink3',
        'country_lov' => '\Swagger\Client\Model\BeezUPCommonLOVLink3',
        'contract_termination_reason_lov' => '\Swagger\Client\Model\BeezUPCommonLOVLink3',
        'invoice_payment_status_lov' => '\Swagger\Client\Model\BeezUPCommonLOVLink3'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'offer_lov' => null,
        'active_offer_lov' => null,
        'customer_status_lov' => null,
        'beez_up_time_zone_lov' => null,
        'store_country_lov' => null,
        'store_sector_lov' => null,
        'country_lov' => null,
        'contract_termination_reason_lov' => null,
        'invoice_payment_status_lov' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'offer_lov' => 'offerLov',
        'active_offer_lov' => 'activeOfferLov',
        'customer_status_lov' => 'customerStatusLov',
        'beez_up_time_zone_lov' => 'beezUPTimeZoneLov',
        'store_country_lov' => 'storeCountryLov',
        'store_sector_lov' => 'storeSectorLov',
        'country_lov' => 'countryLov',
        'contract_termination_reason_lov' => 'contractTerminationReasonLov',
        'invoice_payment_status_lov' => 'invoicePaymentStatusLov'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'offer_lov' => 'setOfferLov',
        'active_offer_lov' => 'setActiveOfferLov',
        'customer_status_lov' => 'setCustomerStatusLov',
        'beez_up_time_zone_lov' => 'setBeezUpTimeZoneLov',
        'store_country_lov' => 'setStoreCountryLov',
        'store_sector_lov' => 'setStoreSectorLov',
        'country_lov' => 'setCountryLov',
        'contract_termination_reason_lov' => 'setContractTerminationReasonLov',
        'invoice_payment_status_lov' => 'setInvoicePaymentStatusLov'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'offer_lov' => 'getOfferLov',
        'active_offer_lov' => 'getActiveOfferLov',
        'customer_status_lov' => 'getCustomerStatusLov',
        'beez_up_time_zone_lov' => 'getBeezUpTimeZoneLov',
        'store_country_lov' => 'getStoreCountryLov',
        'store_sector_lov' => 'getStoreSectorLov',
        'country_lov' => 'getCountryLov',
        'contract_termination_reason_lov' => 'getContractTerminationReasonLov',
        'invoice_payment_status_lov' => 'getInvoicePaymentStatusLov'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['offer_lov'] = isset($data['offer_lov']) ? $data['offer_lov'] : null;
        $this->container['active_offer_lov'] = isset($data['active_offer_lov']) ? $data['active_offer_lov'] : null;
        $this->container['customer_status_lov'] = isset($data['customer_status_lov']) ? $data['customer_status_lov'] : null;
        $this->container['beez_up_time_zone_lov'] = isset($data['beez_up_time_zone_lov']) ? $data['beez_up_time_zone_lov'] : null;
        $this->container['store_country_lov'] = isset($data['store_country_lov']) ? $data['store_country_lov'] : null;
        $this->container['store_sector_lov'] = isset($data['store_sector_lov']) ? $data['store_sector_lov'] : null;
        $this->container['country_lov'] = isset($data['country_lov']) ? $data['country_lov'] : null;
        $this->container['contract_termination_reason_lov'] = isset($data['contract_termination_reason_lov']) ? $data['contract_termination_reason_lov'] : null;
        $this->container['invoice_payment_status_lov'] = isset($data['invoice_payment_status_lov']) ? $data['invoice_payment_status_lov'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        return true;
    }


    /**
     * Gets offer_lov
     *
     * @return \Swagger\Client\Model\BeezUPCommonLOVLink3
     */
    public function getOfferLov()
    {
        return $this->container['offer_lov'];
    }

    /**
     * Sets offer_lov
     *
     * @param \Swagger\Client\Model\BeezUPCommonLOVLink3 $offer_lov offer_lov
     *
     * @return $this
     */
    public function setOfferLov($offer_lov)
    {
        $this->container['offer_lov'] = $offer_lov;

        return $this;
    }

    /**
     * Gets active_offer_lov
     *
     * @return \Swagger\Client\Model\BeezUPCommonLOVLink3
     */
    public function getActiveOfferLov()
    {
        return $this->container['active_offer_lov'];
    }

    /**
     * Sets active_offer_lov
     *
     * @param \Swagger\Client\Model\BeezUPCommonLOVLink3 $active_offer_lov active_offer_lov
     *
     * @return $this
     */
    public function setActiveOfferLov($active_offer_lov)
    {
        $this->container['active_offer_lov'] = $active_offer_lov;

        return $this;
    }

    /**
     * Gets customer_status_lov
     *
     * @return \Swagger\Client\Model\BeezUPCommonLOVLink3
     */
    public function getCustomerStatusLov()
    {
        return $this->container['customer_status_lov'];
    }

    /**
     * Sets customer_status_lov
     *
     * @param \Swagger\Client\Model\BeezUPCommonLOVLink3 $customer_status_lov customer_status_lov
     *
     * @return $this
     */
    public function setCustomerStatusLov($customer_status_lov)
    {
        $this->container['customer_status_lov'] = $customer_status_lov;

        return $this;
    }

    /**
     * Gets beez_up_time_zone_lov
     *
     * @return \Swagger\Client\Model\BeezUPCommonLOVLink3
     */
    public function getBeezUpTimeZoneLov()
    {
        return $this->container['beez_up_time_zone_lov'];
    }

    /**
     * Sets beez_up_time_zone_lov
     *
     * @param \Swagger\Client\Model\BeezUPCommonLOVLink3 $beez_up_time_zone_lov beez_up_time_zone_lov
     *
     * @return $this
     */
    public function setBeezUpTimeZoneLov($beez_up_time_zone_lov)
    {
        $this->container['beez_up_time_zone_lov'] = $beez_up_time_zone_lov;

        return $this;
    }

    /**
     * Gets store_country_lov
     *
     * @return \Swagger\Client\Model\BeezUPCommonLOVLink3
     */
    public function getStoreCountryLov()
    {
        return $this->container['store_country_lov'];
    }

    /**
     * Sets store_country_lov
     *
     * @param \Swagger\Client\Model\BeezUPCommonLOVLink3 $store_country_lov store_country_lov
     *
     * @return $this
     */
    public function setStoreCountryLov($store_country_lov)
    {
        $this->container['store_country_lov'] = $store_country_lov;

        return $this;
    }

    /**
     * Gets store_sector_lov
     *
     * @return \Swagger\Client\Model\BeezUPCommonLOVLink3
     */
    public function getStoreSectorLov()
    {
        return $this->container['store_sector_lov'];
    }

    /**
     * Sets store_sector_lov
     *
     * @param \Swagger\Client\Model\BeezUPCommonLOVLink3 $store_sector_lov store_sector_lov
     *
     * @return $this
     */
    public function setStoreSectorLov($store_sector_lov)
    {
        $this->container['store_sector_lov'] = $store_sector_lov;

        return $this;
    }

    /**
     * Gets country_lov
     *
     * @return \Swagger\Client\Model\BeezUPCommonLOVLink3
     */
    public function getCountryLov()
    {
        return $this->container['country_lov'];
    }

    /**
     * Sets country_lov
     *
     * @param \Swagger\Client\Model\BeezUPCommonLOVLink3 $country_lov country_lov
     *
     * @return $this
     */
    public function setCountryLov($country_lov)
    {
        $this->container['country_lov'] = $country_lov;

        return $this;
    }

    /**
     * Gets contract_termination_reason_lov
     *
     * @return \Swagger\Client\Model\BeezUPCommonLOVLink3
     */
    public function getContractTerminationReasonLov()
    {
        return $this->container['contract_termination_reason_lov'];
    }

    /**
     * Sets contract_termination_reason_lov
     *
     * @param \Swagger\Client\Model\BeezUPCommonLOVLink3 $contract_termination_reason_lov contract_termination_reason_lov
     *
     * @return $this
     */
    public function setContractTerminationReasonLov($contract_termination_reason_lov)
    {
        $this->container['contract_termination_reason_lov'] = $contract_termination_reason_lov;

        return $this;
    }

    /**
     * Gets invoice_payment_status_lov
     *
     * @return \Swagger\Client\Model\BeezUPCommonLOVLink3
     */
    public function getInvoicePaymentStatusLov()
    {
        return $this->container['invoice_payment_status_lov'];
    }

    /**
     * Sets invoice_payment_status_lov
     *
     * @param \Swagger\Client\Model\BeezUPCommonLOVLink3 $invoice_payment_status_lov invoice_payment_status_lov
     *
     * @return $this
     */
    public function setInvoicePaymentStatusLov($invoice_payment_status_lov)
    {
        $this->container['invoice_payment_status_lov'] = $invoice_payment_status_lov;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


