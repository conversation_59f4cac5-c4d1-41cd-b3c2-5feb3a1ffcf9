{"name": "nikic/php-parser", "description": "A PHP parser written in PHP", "keywords": ["php", "parser"], "type": "library", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON><PERSON>"}], "require": {"php": ">=5.5", "ext-tokenizer": "*"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "bin": ["bin/php-parse"], "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}