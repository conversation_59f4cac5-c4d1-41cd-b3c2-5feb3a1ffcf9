<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/distribution.proto

namespace Google\Api\Servicecontrol\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Servicecontrol\V1\Distribution\ExplicitBuckets instead.
     * @deprecated
     */
    class Distribution_ExplicitBuckets {}
}
class_exists(Distribution\ExplicitBuckets::class);
@trigger_error('Google\Api\Servicecontrol\V1\Distribution_ExplicitBuckets is deprecated and will be removed in the next major release. Use Google\Api\Servicecontrol\V1\Distribution\ExplicitBuckets instead', E_USER_DEPRECATED);

