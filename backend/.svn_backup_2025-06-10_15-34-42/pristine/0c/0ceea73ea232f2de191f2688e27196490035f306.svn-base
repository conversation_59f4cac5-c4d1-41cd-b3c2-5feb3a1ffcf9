<?php
	/*
	 * 	Ce script permet d'encoder une chaine via Google KMS
	 */

// curl -s -X POST "" \
// -d "{\"plaintext\":\"U29tZSB0ZXh0IHRvIGJlIGVuY3J5cHRlZA==\"}" \
//   -H "Authorization:Bearer $(gcloud auth print-access-token)" \
//   -H "Content-Type:application/json"

$projectId = "riashop-186610";
$locationId = "europe-west1";
$keyRingId = "Legrand-Salesforce";
$cryptoKeyId = "Salesforce_api_password";

$bearer=exec("gcloud auth print-access-token");

$header = array(
	'Authorization:Bearer '.$bearer,
	'Content-Type:application/json',
);
$data = array('plaintext' => base64_encode('test'));

$ch = curl_init(); 
curl_setopt($ch, CURLOPT_URL, "https://cloudkms.googleapis.com/v1/projects/".$projectId."/locations/".$locationId."/keyRings/".$keyRingId."/cryptoKeys/".$cryptoKeyId.":encrypt"); 
curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true );
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false );
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0 );
curl_setopt($ch, CURLOPT_TIMEOUT, 60);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data)); 
curl_setopt($ch, CURLOPT_POST, 1); 

$result = curl_exec($ch); 

