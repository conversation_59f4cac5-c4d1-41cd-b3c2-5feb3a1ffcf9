<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"> 
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>{#advanced_dlg.about_title}</title>
	<script src="../../tiny_mce_popup.js"></script>
	<script src="../../utils/mctabs.js"></script>
	<script src="js/about.js"></script>
</head>
<body id="about" style="display: none">
		<div class="tabs">
			<ul>
				<li id="general_tab" class="current" aria-controls="general_panel"><span><a href="javascript:mcTabs.displayTab('general_tab','general_panel');" onmousedown="return false;">{#advanced_dlg.about_general}</a></span></li>
				<li id="help_tab" style="display:none" aria-hidden="true" aria-controls="help_panel"><span><a href="javascript:mcTabs.displayTab('help_tab','help_panel');" onmousedown="return false;">{#advanced_dlg.about_help}</a></span></li>
				<li id="plugins_tab" aria-controls="plugins_panel"><span><a href="javascript:mcTabs.displayTab('plugins_tab','plugins_panel');" onmousedown="return false;">{#advanced_dlg.about_plugins}</a></span></li>
			</ul>
		</div>

		<div class="panel_wrapper">
			<div id="general_panel" class="panel current">
				<h3>{#advanced_dlg.about_title}</h3>
				<p>Version: <span id="version"></span> (<span id="date"></span>)</p>
				<p>TinyMCE is a platform independent web based Javascript HTML WYSIWYG editor control released as Open Source under <a href="../../license.txt" target="_blank">LGPL</a>
				by Moxiecode Systems AB. It has the ability to convert HTML TEXTAREA fields or other HTML elements to editor instances.</p>
				<p>Copyright &copy; 2003-2008, <a href="http://www.moxiecode.com" target="_blank">Moxiecode Systems AB</a>, All rights reserved.</p>
				<p>For more information about this software visit the <a href="http://tinymce.moxiecode.com" target="_blank">TinyMCE website</a>.</p>

				<div id="buttoncontainer">
					<a href="http://www.moxiecode.com" target="_blank"><img src="http://tinymce.moxiecode.com/images/gotmoxie.png" alt="Got Moxie?" border="0" /></a>
				</div>
			</div>

			<div id="plugins_panel" class="panel">
				<div id="pluginscontainer">
					<h3>{#advanced_dlg.about_loaded}</h3>

					<div id="plugintablecontainer">
					</div>

					<p>&nbsp;</p>
				</div>
			</div>

			<div id="help_panel" class="panel noscroll" style="overflow: visible;">
				<div id="iframecontainer"></div>
			</div>
		</div>

		<div class="mceActionPanel">
			<input type="button" id="cancel" name="cancel" value="{#close}" onclick="tinyMCEPopup.close();" />
		</div>
</body>
</html>
