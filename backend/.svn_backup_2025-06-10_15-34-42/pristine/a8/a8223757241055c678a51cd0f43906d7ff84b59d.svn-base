<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/resources.proto

namespace Google\Cloud\ServiceManagement\V1\OperationMetadata;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Represents the status of one operation step.
 *
 * Generated from protobuf message <code>google.api.servicemanagement.v1.OperationMetadata.Step</code>
 */
class Step extends \Google\Protobuf\Internal\Message
{
    /**
     * The short description of the step.
     *
     * Generated from protobuf field <code>string description = 2;</code>
     */
    private $description = '';
    /**
     * The status code.
     *
     * Generated from protobuf field <code>.google.api.servicemanagement.v1.OperationMetadata.Status status = 4;</code>
     */
    private $status = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $description
     *           The short description of the step.
     *     @type int $status
     *           The status code.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Servicemanagement\V1\Resources::initOnce();
        parent::__construct($data);
    }

    /**
     * The short description of the step.
     *
     * Generated from protobuf field <code>string description = 2;</code>
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * The short description of the step.
     *
     * Generated from protobuf field <code>string description = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDescription($var)
    {
        GPBUtil::checkString($var, True);
        $this->description = $var;

        return $this;
    }

    /**
     * The status code.
     *
     * Generated from protobuf field <code>.google.api.servicemanagement.v1.OperationMetadata.Status status = 4;</code>
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * The status code.
     *
     * Generated from protobuf field <code>.google.api.servicemanagement.v1.OperationMetadata.Status status = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setStatus($var)
    {
        GPBUtil::checkEnum($var, \Google\Cloud\ServiceManagement\V1\OperationMetadata_Status::class);
        $this->status = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Step::class, \Google\Cloud\ServiceManagement\V1\OperationMetadata_Step::class);

