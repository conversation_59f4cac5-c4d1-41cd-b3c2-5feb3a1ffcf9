<?php
require_once('Cadencier/CadencierProduct.inc.php');
require_once('Periods/Periods.inc.php');
require_once('Periods/ObjectPeriods.inc.php');
require_once('Periods/Filters/ArrayFilter.inc.php');
require_once('Periods/Filtered.inc.php');
require_once('orders.inc.php');

/**
 * Class Cadencier
 */
class Cadencier
{
	/**
	 *
	 */
	const PUBLIC_PRICE = 'public_price';

	/**
	 *
	 */
	const USER_PRICE = 'user_price';

	/**
	 *
	 */
	const LAST_INVOICE_PRICE = 'last_invoice_price';

	/**
	 *
	 */
	const USER_AND_LAST_INVOICE_PRICE = 'both';

	/**
	 * Objet Period
	 *
	 * @var Periods $periods
	 */
	protected $periods;
	/**
	 * Identifiant de l'utilisateur
	 *
	 * @var integer $user_id
	 */
	protected $user_id;
	/**
	 * Tableau des produits commandé
	 *
	 * @var array $ordProducts
	 */
	protected $ordProducts = array();
	/**
	 * Tableau avec les closure pour filtrer
	 *
	 * @var array $filters
	 */
	protected $filters = array();

	/**
	 * Configuration du prix a afficher sur le cadencier
	 *
	 * @var string $price_to_display
	 */
	protected $price_to_display = self::PUBLIC_PRICE;

	/**
	 * Cadencier constructor.
	 *
	 * @param Periods $periods Object Period
	 * @param integer $user_id Identifiant de l'utilisateur
	 * @param null|string    $price_to_display Facultatif, optiondd'affichage des tarifs
	 *
	 * @return void
	 */
	public function __construct( Periods $periods, $user_id, $price_to_display=null)
	{
		$this->periods = $periods;

		$this->user_id = $user_id;

		$this->setPriceToDisplay($price_to_display);
	}

	/**
	 * Cette fonction permet de définir quel prix il faudra afficher
	 *
	 * @param string $option Option de tarif à afficher
	 *
	 * @return void
	 */
	public function setPriceToDisplay( $option)
	{
		if (!is_null($option) && in_array($option, array(self::PUBLIC_PRICE, self::USER_PRICE, self::LAST_INVOICE_PRICE, self::USER_AND_LAST_INVOICE_PRICE))) {
			$this->price_to_display = $option;
		}
	}

	/**
	 * Permet  d'ajouter des fonctions filtres qui permettrons de filtrer le résultat
	 *
	 * @param array|closure $closure Une closure ou un tableau de closure
	 * @return self
	 */
	public function addFilters($closure)
	{
		if ($closure instanceof Closure ){
			$this->filters[] = $closure;
		}elseif (is_array($closure)){
			$this->filters = $closure;
		}

		return $this;
	}

	/**
	 * Cette fonction permet de retourner les produits
	 *
	 * @return array Retourne un tableau avec les produits filtrés
	 */
	public function getProducts()
	{
		$this->loadProducts();
		return $this->sortProducts();
	}

	/**
	 * Cette fonction permet de trier et de filtrer les produits
	 *
	 * @return array Retourne un tableau de produit
	 */
	protected function sortProducts()
	{
		$products = array();
		foreach($this->ordProducts as $id => $product) {
			if (!$this->execFilters($product)) {
				continue;
			}
			$products[$id] = $product;
		}

		return $products;
	}

	/**
	 * Cette fonction permet d'exécuter les filtres définies pour les produits
	 *
	 * @param CadencierProduct $product Un produit du cadencier
	 * @return bool Retourne true si il doit être inclue, false si exclue
	 */
	protected function execFilters(CadencierProduct $product)
	{
		foreach($this->filters as $filter) {
			if (!$filter($product)){
				return false;
			}
		}

		return true;
	}


	/**
	 * Cette classe permet de charger les produits de la base de donnée
	 * @return void
	 */
	protected function loadProducts()
	{
		global $config;
		$format = 'Y-m-d';

		foreach( $this->periods->DatePeriod() as $key => $begin) {
			/**  @var DateTime $begin */
			$end = clone $begin;
			$end->add($this->periods->getDateInterval());

			$date_end = $end->format($format);
			$date_start = $begin->format($format);

			$r = stats_products_selled($date_start, $date_end, array($this->user_id), array(), true);

			if (!$r || !ria_mysql_num_rows($r)) {
				continue;
			}

			while ($p = ria_mysql_fetch_assoc($r)) {
				$exclude = fld_object_values_get($p['prd_id'], _FLD_PRD_EXCLUDE_FROM_STATS);
				if (in_array($exclude, $config['fld_vals_yes'])) {
					continue;
				}
				if (!array_key_exists($p['prd_id'], $this->ordProducts)) {

					$r_p = prd_products_get_simple($p['prd_id']);

					if (!$r_p || !ria_mysql_num_rows($r_p)) {
						continue;
					}

					$prd = ria_mysql_fetch_assoc($r_p);
					$prd['url'] = prd_products_get_url($p['prd_id'], true);

					$prd['prices'] = $this->getPrice($prd);

					$cp = new CadencierProduct($prd, $this->periods);

					$this->ordProducts[$p['prd_id']] = $cp;
				}

				$this->ordProducts[$p['prd_id']]->addQuantityForPeriod($begin, $p['total_selled']);
			}
		}
	}

	/**
	 * Cette fonction permet de retourner le prix pour un produit en fonction de la configuration du cadencier
	 *
	 * @param array $prd Tableau représentant le produit prd_products_get
	 *
	 * @return array|bool retourne un tableau avec les clés suivantes : price_ht, tva_rate, price_ttc, price_brut_ht. Reetourne false si erreur
	 */
	private function getPrice($prd)
	{
		$price = array(
			'price_ht' => 0,
			'tva_rate' => _TVA_RATE_DEFAULT,
			'price_ttc' => 0,
			'price_brut_ht' => 0,
		);

		switch($this->price_to_display) {
			case self::PUBLIC_PRICE:{
				$price = $this->getProductPrice($prd);
				break;
			}
			case self::USER_PRICE:{
				$price = $this->getProductPrice($prd, false);
				break;
			}
			case self::LAST_INVOICE_PRICE:{
				$return = ord_inv_products_get_last_user_sold_price($this->user_id, $prd['id']);
				if ($return){
					$price = $return;
				}
				break;
			}
			case self::USER_AND_LAST_INVOICE_PRICE: {
				try{
					$is_negotiated = prd_has_negotiated_price(
						$this->user_id, $prd['id']
					);
				}catch(InvalidArgumentException $e){
					return $price;
				}
				if ($is_negotiated) {
					$price = $this->getProductPrice($prd, false);
				}else{
					$return = ord_inv_products_get_last_user_sold_price($this->user_id, $prd['id']);
					if ($return){
						$price = $return;
					}
				}
				break;
			}
		}

		return $price;
	}


	/**
	 * Permet de récupérer le prix pour un produit en fonction du tarif public ou du client
	 *
	 * @param array $prd Tableau représentant le produit prd_products_get
	 * @param bool $public Détermine si c'est le prix public ou le prix négocier qu'il faut retourner
	 *
	 * @return array Retourne un tableau avec les informations suivantes : price_ttc, price_ht, tva_rate
	 */
	private function getProductPrice($prd, $public=true)
	{
		$price = array('price_ttc' => 0, 'price_ht' => 0, 'tva_rate' => _TVA_RATE_DEFAULT);
		$r_price = prd_products_get_price($prd["id"], $public ? 0 : $this->user_id);
		if($r_price && ria_mysql_num_rows($r_price)){
			$prc = ria_mysql_fetch_assoc($r_price);
			$price['price_ttc'] = $prc['price_ttc'];
			$price['price_ht'] = $prc['price_ht'];
			$price['tva_rate'] = $prc['tva_rate'];
		}

		return $price;
	}
}