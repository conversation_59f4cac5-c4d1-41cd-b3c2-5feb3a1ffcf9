<?php

	/**	\file edit.php
	 *	Cette page permet la création, la modification et la suppression d'un type de rapport.
	 */

	require_once('reports.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	if( isset($_GET['type']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_FDV_REPORT_EDIT');
	}elseif( !isset($_GET['type']) ){
		gu_if_authorized_else_403('_RGH_ADMIN_FDV_REPORT_ADD');
	}

	$current_type = isset( $_GET['type'] ) ? $_GET['type'] : 0;
	$type_models = array();
	$type = array();
	if( $current_type > 0 ){
		$rtype = rp_types_get($current_type);
		
		if( !$rtype ){
			header('Location: index.php');
			exit;
		}else{
			$type = ria_mysql_fetch_array($rtype);
		}
	
		$rmodels = rp_types_models_get($type['id']);
		if ($rmodels && count($rmodels)) {
			while ($model = ria_mysql_fetch_array($rmodels)) {
				$type_models[] = $model['mdl_id'];
			}
		}
	}else{
		$type = array('id' => 0, 'name' => '', 'rtg_id' => 0, 'tnt_id' => -1);
		$group = array();
	}

	// Bouton Annuler
	if (isset($_POST['cancel'])) {
		header('Location: index.php');
		exit;
	}

	// Bouton Supprimer
	if (isset($_POST['del'], $_POST['type_id']) && $_POST['type_id'] > 0 && $type['tnt_id'] > 0) {
		if (rp_types_del($_POST['type_id'])) {
			header('Location: index.php');
			exit;
		}
	}

	$error = null;

	// Bouton Enregistrer
	if (isset($_POST['save'])) {
		// Ajout un mise à jour du type en fonction de son existance
		$model = isset($_POST['models']) ? $_POST['models'] : array();

		if( !isset($error) && ( !isset($_POST['name']) || trim($_POST['name']) == '' ) ){
			$error = _("Veuillez renseigner le nom du type.");
		}

		if( !isset($error) && ( !isset($_POST['group']) || !is_numeric($_POST['group']) || intval($_POST['group']) == 0 ) ){
			$error = _("Veuillez renseigner le groupe du type.");
		}

		if( !isset($error) ){
			if ($type['id'] <= 0 && isset($_POST['name'], $_POST['group'])) {
				$r_type = rp_types_get(0, $_POST['name'], false);
				if( $r_type && ria_mysql_num_rows($r_type) > 0 ){
					$error = _('Ce type de rapports de visite existe déjà.');
				}else{
					$type_id = rp_types_add($_POST['name'], $_POST['group']);
	
					if ($type_id && $type_id > 0) {
						$r = rp_type_models_update($type_id, $model);
						
						if ($r) {
							header('Location: index.php');
							exit;
						}
					} else {
						$error = _('Une erreur s\'est produite lors de l\'ajout du type de rapport. Veuillez réessayer ou prendre contact avec nous pour signaler l\'erreur.');
					}
				}
			}else{
				$r_existing_type = rp_types_get(0, $_POST['name'], false);
				if( $r_existing_type && ria_mysql_num_rows($r_existing_type) > 0 ){
					while( $existing_type = ria_mysql_fetch_assoc($r_existing_type) ){
						if( $existing_type['id'] != $type['id']){
							$error = _('Ce type de rapports de visite existe déjà.');
							break;
						}
					}
				}
	
				if( !isset($error) ){
					if (isset($type['tnt_id']) && $type['tnt_id'] > 0) {
						$res = rp_types_update($type['id'] , $_POST['name'], $_POST['group'] );
					}else{
						$res = true;
					}
					
					if ($res) {
						$r = rp_type_models_update($type['id'], $model);
						if ($r) {
							header('Location: index.php');
							exit;
						}else{
							$error = _('Une erreur s\'est produite lors de l\'enregistrement du type de rapport. Veuillez réessayer ou prendre contact avec nous pour signaler l\'erreur.');
						}
					}else{
						$error = _('Une erreur s\'est produite lors de l\'enregistrement du type de rapport. Veuillez réessayer ou prendre contact avec nous pour signaler l\'erreur.');
					}
				}
			}
		}
	}

	// Défini le titre de la page
	if( $type['id']<=0 ){
		$page_title = _('Ajout d\'un type de rapport');
	}else{
		$page_title = 'Type de rapport '.$type['name'];
	}
	define('ADMIN_PAGE_TITLE', $page_title . ' - ' . _('Rapports') . ' - Yuto');
	require_once('admin/skin/header.inc.php');

?>

<h2><?php print htmlspecialchars($page_title) ;?></h2>

<?php
	// Affichage des messages d'erreur
	if ($error != null) {
		print '<div class="error">'.htmlspecialchars( $error ).'</div>';
	}
	
	// Affiche un message d'avertissement pour les champs système
	if( $type['tnt_id']==0 ){
		print '<div class="notice">'._('Ce type de rapport est requis par le système, il ne peut donc pas être modifié ou supprimé.').'</div>';
	}
?>

<form action="edit.php?type=<?php print $type['id']; ?>" method="post">
	<table>
	
	<tbody>
		<tr>
			<td><span class="mandatory">*</span></label> <label for="name"><?php print _('Nom :'); ?></td>
			<td>
			<?php if ($type['tnt_id']==0){ ?>
				<input type="text" name="name" id="name" disabled="disabled" maxlength="75" value="<?php print htmlspecialchars(isset($_POST['name']) ? $_POST['name'] : $type['name']); ?>" />
			<?php }else{ ?>
				<input type="text" name="name" id="name" maxlength="75" value="<?php print htmlspecialchars(isset($_POST['name']) ? $_POST['name'] : $type['name']); ?>" />
			<?php } ?>
			</td>
		</tr>
		<tr>
			<td><span class="mandatory">*</span> <label for="group"><?php print _('Groupe de rapports :');?></label></td>
			<td><?php
				$groups = rp_type_groups_get();
				if( $groups && ria_mysql_num_rows($groups) ){
					if ($type['tnt_id']==0) {
						print '	<select disabled="disabled" name="group" id="group">';
					}else{
						print '	<select name="group" id="group">';
					}
					while( $group = ria_mysql_fetch_array($groups) )
						print '	<option value="'.strtoupper2($group['id']).'" '.((mb_strtoupper($group['id'])==(isset($_POST['group']) ? $_POST['group'] : $type['rtg_id'])) ? 'selected="selected"' : '' ).'>'.htmlspecialchars($group['name']).'</option>';
					print '</select>';
				}
			?></td>
		</tr>
		<tr>
			<td><?php print _('Modèles de saisie :')?></td>
			<td><?php

				$models = fld_models_get( 0, 0, CLS_REPORT);
				if( $models && ria_mysql_num_rows($models) ){
					while( $model = ria_mysql_fetch_array($models) ){
						print '<input type="checkbox" id="'.$model["id"].'" name="models[]" ';
						if (in_array($model["id"], $type_models)) {
							print ' checked="checked" ';
						}

						print 'value="'.$model["id"].'"> <label for ="'.$model["id"].'">'.htmlspecialchars( $model['name'] ).'</label></br>';
					}
				}else{
					print _('Aucun modèle de saisie');
				}
			?></td>
		</tr>
	</tbody>
	<tfoot>
		<tr><td colspan="2">

			<?php if( $type['id']>0 && $type['tnt_id']>0 ){ ?>
				<input type="submit" name="save" value="<?php print _('Enregistrer')?>" />
			<?php }elseif( $type['id']<=0 ){ ?>
				<input type="submit" name="save" value="<?php print _('Ajouter')?>" />
			<?php } ?>

				<input type="submit" name="cancel" value="<?php print _('Annuler')?>"/>

			<?php if( $type['id']>0 && $type['tnt_id']>0){ ?>
			
				<input type="submit" name="del" value="<?php print _('Supprimer')?>" onclick="return confirm('<?php print _('Voulez-vous vraiment effectuer cette suppression ?')?>')" />
				<input type="hidden" name="type_id" value="<?php print $type['id'] ?>"/>
			
			<?php } ?>
		</td></tr>
	</tfoot>
	</table>
</form>

<?php
	require_once('admin/skin/footer.inc.php');
?>