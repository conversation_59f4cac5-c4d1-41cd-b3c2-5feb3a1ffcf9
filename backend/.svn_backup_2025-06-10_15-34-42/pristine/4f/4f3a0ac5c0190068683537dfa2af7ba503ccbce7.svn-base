<?php
// \cond onlyria

require_once('fields.inc.php');

/** \defgroup model_revisions Révisions
 *	\ingroup tools_cms
 *
 *	Ce module comprend les fonctions nécessaires à la gestion des révisions.
 *	Le verioning est générique et peut être appliqué à n'importe quel module
 *	Lorsqu'un module veut implémenter le versioning, il peut s'appuyer sur ce qui a été fait pour les CMS (onglet révision).
 *	Pour permettre la générécité, une version est définie par sa classe (ex : CLS_CMS), la clé primaire (l'objet qui contient les versions) (ex : une page de contenu)
 *	et une clé de révision (ex : Version du 01/01/2011). Les clés sont composées de COUNT_OBJ_ID champs pour permettre les clés composées mais un champ suffira peut-être à identifier chaque objet.
 *	Les champs doivent avoir une valeur numérique. Si des champs ne sont pas utilisés, il doivent être complétés par des 0.
 *	Chaque clé identifie un objet du module.
 *
 *	Avant de modifier un objet, l'utilisateur doit créer une copie de son objet dans la version actuelle (avant de recevoir les modifications).
 *	L'utilisateur peut ensuite modifier son objet (update). La vérification qu'une modification a bien été apportée est à la charge de l'utilisateur
 *	Il faut ensuite appeler la fonction rev_revisions_add
 *
 *	Pour restaurer une version, il faut récupérer l'objet correspondant à la version concernée grâce à sa clé.
 *	Il suffit ensuite de modifier l'objet courant avec les données de l'ancienne version et les informations de révision.
 *	La fonction rev_revisions_restore_fields permet de restaurer les modèles de saisie et les champs avancés.
 *
 *	Note : Tous vos objets et versions sont stockés dans la même table. Afin de ne pas récupérer toutes les versions quand vous cherchez un objet,
 *	créez un champ current qui indique s'il s'agit de la version courante ou non (1 pour les objets déjà présents et les nouveaux objets créés, 0 sinon).
 *	La version courante aura toujours la même clé (celle de l'objet qui contient les versions).
 *
 *	@{
 */

/** Cette fonction crée une nouvelle révision
 *	Avant d'appeler cette fonction, l'utilisateur doit créer une copie de la version courante avant modification.
 *
 *	@param $class Olbigatoire, classe du module concerné
 *	@param $obj Obligatoire, clé de l'objet contenant les versions
 *	@param int $id Obligatoire, clé de l'objet à versionner
 *	@param $major Obligatoire, indique si la révision est majeure ou non
 *	@param string $comment Facultatif, modifications apportées à la nouvelle version
 *
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function rev_revisions_add( $class, $obj, $id, $major, $comment='' ){
	if( !(is_numeric($class) && fld_classes_exists($class)) )
		return false;

	// primary key
	if( !is_array($obj) )
		$obj = array($obj);
	if( count($obj)==0 || count($obj)>COUNT_OBJ_ID )
		return false;
	for( $i=0; $i<COUNT_OBJ_ID; $i++) {
		if( !isset($obj[$i]) )
			$obj[$i] = 0;
		elseif( !is_numeric($obj[$i]) )
			return false;
	}

	// id
	if( !is_array($id) )
		$id = array($id);
	if( count($id)==0 || count($id)>COUNT_OBJ_ID )
		return false;
	for( $i=0; $i<COUNT_OBJ_ID; $i++) {
		if( !isset($id[$i]) )
			$id[$i] = 0;
		elseif( !is_numeric($id[$i]) )
			return false;
	}

	global $config;
	$lng_code = $config['i18n_lng'];

	$time = time();

	// récupère les infos de la version courante qui vont s'appliquer à cette révision
	$rev_id = array();
	for ($i=0; $i<COUNT_OBJ_ID; $i++)
		$rev_id[] = 'rev_id_'.$i;
	$sql = '
		select rev_user_id as user_id, rev_major as major, rev_date_created as date_created, rev_comment as comment, rev_lng_code as lng_code
		from rev_revisions
		where rev_tnt_id = '.$config['tnt_id'].'
			and rev_class_id = '.$class.'
			and ('.implode(',', $rev_id).') in (('.implode(',', $obj).'))
	';

	if( !($cur = ria_mysql_query($sql)) )
		return false;
	$cur = ria_mysql_fetch_array($cur);
	if( !$cur )
		$cur = array('user_id' => null, 'major' => 0, 'date_created' => date('Y-m-d H:i:s', $time), 'comment' => 'Version initiale', 'lng_code' => $lng_code);

	// création de la révision avant modifications
	// enregistrement dans la table des révisions
	$tmp = array(
		'rev_tnt_id'		=>	$config['tnt_id'],
		'rev_class_id'		=>	$class,
		'rev_user_id'		=>	$cur['user_id'] ? $cur['user_id'] : 'null',
		'rev_date_created'	=>	'\''.$cur['date_created'].'\'',
		'rev_major'			=>	$cur['major'],
		'rev_comment'		=>	'\''.addslashes($cur['comment']).'\'',
		'rev_lng_code'		=>	'\''.addslashes($cur['lng_code']).'\''
	);

	foreach( $obj as $i => $v )
		$tmp['rev_obj_id_'.$i] = $v;
	foreach( $id as $i => $v )
		$tmp['rev_id_'.$i] = $v;
	$fields = array();
	$values = array();
	foreach( $tmp as $key => $value ){
		$fields[] = $key;
		$values[] = $value;
	}

	$sql  =	'
		insert
		into rev_revisions ('.implode(',', $fields).')
		values ('.implode(',', $values).');
	';

	if( !ria_mysql_query($sql) )
		return false;

	// version courante
	$user_id = isset($_SESSION['usr_id']) ? $_SESSION['usr_id'] : null;

	$major = $major ? 1 : 0;

	// enregistrement dans la table des révisions
	$tmp = array(
		'rev_tnt_id'		=>	$config['tnt_id'],
		'rev_class_id'		=>	$class,
		'rev_user_id'		=>	$user_id ? $user_id : 'null',
		'rev_date_created'	=>	'\''.date('Y-m-d H:i:s', $time+1).'\'',
		'rev_major'			=>	$major,
		'rev_comment'		=>	'\''.addslashes($comment).'\'',
		'rev_lng_code'		=>	'\''.addslashes($lng_code).'\''
	);

	foreach( $obj as $i => $v ){
		$tmp['rev_id_'.$i] = $v;
		$tmp['rev_obj_id_'.$i] = $v;
	}
	$fields = array();
	$values = array();
	foreach( $tmp as $key => $value ){
		$fields[] = $key;
		$values[] = $value;
	}

	$sql =	'
		replace
		into rev_revisions ('.implode(',', $fields).')
		values ('.implode(',', $values).');
	';

	if( !ria_mysql_query($sql) )
		return false;

	// associe les modèles de saisie
	$rmdl = fld_object_models_get($obj);
	if( $rmdl ){
		while( $mdl = ria_mysql_fetch_array($rmdl) ) {
			fld_object_models_add($id, $mdl['mdl_id']);
		}
	}

	global $config;

	// recopie les champs avancés
	foreach( $config['i18n_lng_used'] as $lng ){
		$fields = fld_fields_get(0, 0, -1, 0, 0, $obj, null, array(), true, array(), null, CLS_CMS, null, null, null, null, $lng);
		if( $fields ){
			while ($f = ria_mysql_fetch_array($fields)) {
				if( $f['type_id'] == FLD_TYPE_SELECT ) {
					$val = ria_mysql_fetch_array(fld_restricted_values_get(0, $f['id'], '', 0, $f['obj_value']));
					$f['obj_value'] = $val['id'];
				}
				fld_object_values_set($id, $f['id'], $f['obj_value'], $lng);
			}
		}
	}

	return true;
}

/** Cette fonction recopie les modèles de saisie et champs avancée de l'objet id vers l'objet obj
 *	@param $class Obligatoire, classe du module concerné
 *	@param $obj Obligatoire, clé de l'objet à modifier
 *	@param int $id Obligatoire, clé de l'objet servant de module
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function rev_revisions_restore( $class, $obj, $id ){
	// on supprime tous les modèles de saisie liés
	fld_object_models_del($obj, 0, FLD_TYPE_REFERENCES_ID);

	// on associe les modèles de saisie de la révision
	$rmdl = fld_object_models_get($id);
	if ($rmdl) {
		while( $mdl = ria_mysql_fetch_array($rmdl) ) {
			fld_object_models_add($obj, $mdl['mdl_id']);
		}
	}

	// applique les champs avancés de la version à restaurer à la nouvelle version
	global $config;

	foreach( $config['i18n_lng_used'] as $lng ){
		if ($fields = fld_fields_get(0, 0, -1, 0, 0, $id, null, array(), true, array(), null, $class, null, null, null, null, $lng)) {
			while( $f = ria_mysql_fetch_array($fields) ) {
				if( $f['type_id'] == FLD_TYPE_SELECT ){
					$val = ria_mysql_fetch_array(fld_restricted_values_get(0, $f['id'], '', 0, $f['obj_value']));
					$f['obj_value'] = $val['id'];
				}
				fld_object_values_set($obj, $f['id'], $f['obj_value'], $lng);
			}
		}
	}
	return true;
}

/** Cette fonction supprime des révisions
 *	Les révisions sont supprimées virtuellement avec une date de suppression.
 *	@param $class Obligaoire, classe du module concerné
 *	@param $obj Obligatoire, clé de l'objet contenant les versions
 *	@param $ids Obligatoire, liste des clés à supprimer
 *		Si l'utilisateur fournit array(1,2,3), on ne sait pas très bien s'il faut supprimer la version (1,2,3)
 *		ou les versions 1, 2 et 3. Afin de lever toute ambiguité, on impose que le paramètre fourni soit la liste des versions à supprimer.
 *		Ainsi, array(1,2,3) sera interprêté comme "supprimer les 3 versions : 1, 2 et 3"
 *		Utiliser array(array(1,2,3)) pour supprimer la version de clé composée (1,2,3)
 *	@return bool true en cas de succès, false en cas d'échec.
 */
function rev_revisions_del( $class, $obj, $ids ){
	if( !(is_numeric($class) && fld_classes_exists($class)) )
		return false;

	// id
	if( !is_array($ids) )
		$ids = array($revisions_key);

	// primary key
	if( !is_array($obj) )
		$obj = array($obj);
	if( count($obj)==0 || count($obj)>COUNT_OBJ_ID )
		return false;
	for( $i=0; $i<COUNT_OBJ_ID; $i++) {
		if( !isset($obj[$i]) )
			$obj[$i] = 0;
		elseif( !is_numeric($obj[$i]) )
			return false;
	}

	$sql = '
		update rev_revisions
		set rev_date_deleted = now()
	';

	$where = '';
	$where .= 'rev_class_id = '.$class;

	$tmp = array();
	foreach( $ids as $key ){
		// key
		if( !is_array($key) )
			$key = array($key);
		if( count($key)==0 || count($key)>COUNT_OBJ_ID )
			return false;
		for( $i=0; $i<COUNT_OBJ_ID; $i++) {
			if( !isset($key[$i]) )
				$key[$i] = 0;
			elseif( !is_numeric($key[$i]) )
				return false;
		}

		if( $key==$obj )
			return false;		// on ne peut pas supprimer la version actuelle
		$tmp[] = '('.implode(',', $key).')';
	}

	$rev_id = array();
	for( $i=0; $i<COUNT_OBJ_ID; $i++)
		$rev_id[] = 'rev_id_'.$i;
	$where .= ' and ('.implode(',', $rev_id).') in ('.implode(',', $tmp).')';

	$sql .= '
		where '.$where.'
	';
	return ria_mysql_query($sql);
}

/** Cette fonction renvoie les révisions disponibles
 *
 *	@param $class Obligatoire, classe du module concerné
 *	@param $obj Facultatif, clé de l'objet contenant les versions
 *	@param int $id Facultatif, clé de la version
 *	@param $major Facultatif, indique si les modifications doivent etre majeures (true), mineures (false) ou n'importe (null).
 *
 *	@return array un tableau MySQL avec les champs suivants :
 *				- obj_id_X : Champ X de l'objet contenant les versions
 *				- user_id :	Identifiant de l'auteur de la version
 *				- date_created : Date de création
 *				- major : Type de modification
 *				- comment : Commentaires de modification
 *				- lng_code : Langue
 */
function rev_revisions_get( $class, $obj=null, $id=null, $major=null ){
	if( !(is_numeric($class) && fld_classes_exists($class)) ){
		return false;
	}
	global $config;

	$rev_id = array();
	for( $i=0; $i<COUNT_OBJ_ID; $i++ )
		$rev_id[] = 'rev_id_'.$i.' as id_X_'.$i;
	$sql = '
		select '.implode(',', $rev_id).', rev_user_id as user_id, rev_date_created as date_created, rev_major as major, rev_comment as comment, rev_lng_code as lng_code
		from rev_revisions
	';

	$where = '';
	$where .= 'rev_tnt_id = '.$config['tnt_id'];
	$where .= ' and rev_class_id = '.$class;
	$where .= ' and (rev_date_deleted is null or rev_date_deleted > now())';

	if( $major !== null ){
		if( !is_numeric($major) )
			return false;
		$where .= ' and rev_major = '.$major;
	}

	if( $obj === null && $id === null)
		return false;

	if( $obj ){
		// obj
		if( !is_array($obj) )
			$obj = array($obj);
		if( count($obj)==0 || count($obj)>COUNT_OBJ_ID )
			return false;
		for( $i=0; $i<COUNT_OBJ_ID; $i++) {
			if( !isset($obj[$i]) )
				$obj[$i] = 0;
			elseif( !is_numeric($obj[$i]) )
				return false;
		}

		foreach( $obj as $i => $value )
			$where .= ' and rev_obj_id_'.$i.' = '.$value;
	}

	if( $id ){
		// id
		if( !is_array($id) )
			$id = array($id);
		if( count($id)==0 || count($id)>COUNT_OBJ_ID )
			return false;
		for( $i=0; $i<COUNT_OBJ_ID; $i++) {
			if( !isset($id[$i]) )
				$id[$i] = 0;
			elseif( !is_numeric($id[$i]) )
				return false;
		}

		foreach( $id as $i => $value )
			$where .= ' and rev_id_'.$i.' = '.$value;
	}

	$sql .= '
		where '.$where.'
		order by rev_date_created desc
	';
	$res = ria_mysql_query($sql);
	return $res;
}

/// @}

// \endcond
