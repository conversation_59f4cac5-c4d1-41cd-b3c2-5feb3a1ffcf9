<?php
	/** \file index.php
	 * 	Ce fichier permet d'afficher la liste des différents services de livraison ayant été définis. Il permet d'y accéder et d'en ajouter des nouveaux.
	 */
	require_once('delivery.inc.php');

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_CONFIG_DLV_SRV');

	// Bouton Ajouter
	if( isset($_POST['add']) ){
		header('Location: edit.php');
		exit;
	}

	unset($error);

	// Suppression dans la liste
	if( isset($_POST['del']) && isset($_POST['srv']) ){
		foreach( $_POST['srv'] as $z ){
			if( !dlv_services_del($z) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression d'un des services.\nVeuillez réessayer ou prendre contact avec l'administrateur.");
				break;
			}
		}
		if( !isset($error) ){
			header('Location: index.php');
			exit;
		}
	}

	$can_move = gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_SRV_MOVE');
	$checkbox = gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_SRV_DEL');

	if( $can_move && $checkbox ){
		$colspan = 4;
	}elseif( $can_move || $checkbox ){
		$colspan = 3;
	}else{
		$colspan = 2;
	}

	define('ADMIN_PAGE_TITLE', _('Services de livraison') . ' - ' . _('Livraison des commandes') . ' - ' . _('Configuration'));
	require_once('admin/skin/header.inc.php');

	// Charge la liste des services de livraison
	$services = dlv_services_get();
	if( !$services ){
		$error = _('Une erreur s\'est produite lors du chargement des services de livraison disponibles');
		$services_count = 0;
	}else{
		$services_count = ria_mysql_num_rows($services);
	}

?>
	<form action="index.php" method="post">

	<h2>
		<?php echo _('Services de livraison'); ?> (<?php print ria_number_format($services_count, 0) ?>)

		<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_SRV_ADD') ){ ?>
			<input type="submit" name="add" class="btn-add float-right" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter un service de livraison"); ?>" onclick="return srvAdd()" />
		<?php } ?>
	</h2>

	<?php
	
		if( isset($error) ){
			print '<div class="error">'.nl2br(htmlspecialchars($error)).'</div>';
		}
		
		if( $services ){
	?>

	<table id="table-config-services" class="checklist srv-table">
		<thead>
			<tr>
				<?php if( $checkbox ){ ?>
				<th id="select"><input type="checkbox" class="checkbox" onclick="checkAllClick(this)" /></th>
				<?php } ?>
				<th id="name"><?php echo _("Nom du service"); ?></th>
				<th id="is-active"><?php echo _("Activé"); ?> ?</th>
				<?php print $can_move ? '<th></th>' : ''; ?>
			</tr>
		</thead>
		<tbody>
			<?php
				if( !ria_mysql_num_rows($services) ){
					print '<tr><td colspan="'.$colspan.'">' . _('Aucun service de livraison') . '</td></tr>';
				}else{
					while( $r = ria_mysql_fetch_array($services) ){
						print '<tr id="line-' . $r['id'] . '" class="ria-row-orderable">';
						if( $checkbox ){
							print 	'<td headers="select"><input type="checkbox" class="checkbox" name="srv[]" value="'.$r['id'].'" /></td>';
						}
						if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_SRV_VIEW') ){
							print 	'<td headers="name"><a href="edit.php?srv='.$r['id'].'">'.htmlspecialchars($r['name']).'</a></td>';
						}else{
							print 	'<td headers="name">'.htmlspecialchars($r['name']).'</td>';
						}
						print 		'<td headers="is-active">'.( $r['is_active'] ? 'Oui' : 'Non' ).'</td>';
						if( $can_move ){
							print	'<td class="ria-cell-move"></td>';
						}
						print '</tr>';
					}
				}
			?>
		</tbody>
		<tfoot>
			<tr><td colspan="<?php print $colspan; ?>">
				<?php if( ria_mysql_num_rows($services) && gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_SRV_DEL') ){ ?>
				<input type="submit" name="del" class="btn-del float-left" value="<?php echo _("Supprimer"); ?>" title="<?php echo _("Supprimer les services de livraison sélectionnés"); ?>" onclick="return srvConfirmDelList()" />
				<?php } ?>
				<?php if( gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_SRV_ADD') ){ ?>
				<input type="submit" name="add" class="btn-add" value="<?php echo _("Ajouter"); ?>" title="<?php echo _("Ajouter un service de livraison"); ?>" onclick="return srvAdd()" />
				<?php } ?>
			</td></tr>
		</tfoot>
	</table>
	</form>
<?php
		} // if( $services )
	
	require_once('admin/skin/footer.inc.php');
?>