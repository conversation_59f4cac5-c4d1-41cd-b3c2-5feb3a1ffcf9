<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/metric.proto

namespace Google\Api;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\MetricDescriptor\ValueType instead.
     * @deprecated
     */
    class MetricDescriptor_ValueType {}
}
class_exists(MetricDescriptor\ValueType::class);
@trigger_error('Google\Api\MetricDescriptor_ValueType is deprecated and will be removed in the next major release. Use Google\Api\MetricDescriptor\ValueType instead', E_USER_DEPRECATED);

