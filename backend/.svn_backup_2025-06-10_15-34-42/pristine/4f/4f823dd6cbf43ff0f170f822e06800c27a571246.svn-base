<?php

/**	\file export.php
 *	Ce fichier exporte au format Excel les rapports de visite réalisés avec Yuto.
 *	Il utilise pour cela la librairie PHPExcel.
 *	Les paramètres acceptés sont les suivants :
 *	- type : Optionnel, identifiant du type de rapport
 *	- author : Optionnel, identifiant de l'auteur du rapport (commercial)
 *	- date1 : Optionnel, date de début à partir de laquelle filtrer les rapports
 *	- date2 : Optionnel, date de fin à partir de laquelle filtrer les rapports
 */

use Reports\VisitReport;

require_once('Reports/VisitReport.inc.php');
require_once('fields.inc.php');

require_once('products.inc.php');
require_once('users.inc.php');
$alphabet = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');
	// Récupération des rapport

$type_id = isset($_REQUEST["type"]) ? $_REQUEST["type"] : 0;
$author = isset($_REQUEST["author"]) ? $_REQUEST["author"] : 0;
$date1 = isset($_REQUEST["date1"]) ? $_REQUEST["date1"] : null;
$date2 = isset($_REQUEST["date2"]) ? $_REQUEST["date2"] : null;

try {
	$Report = new VisitReport($type_id, $author, $date1, $date2);
	$Report->generate();
} catch (Exception $e) {
	echo $e->getMessage();
}

exit;