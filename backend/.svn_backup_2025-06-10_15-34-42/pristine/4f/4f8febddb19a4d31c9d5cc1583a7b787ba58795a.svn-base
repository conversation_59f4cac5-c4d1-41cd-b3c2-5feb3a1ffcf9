<?php
/**
 * ReportAdvancedFilters
 *
 * PHP version 5
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */

/**
 * BeezUP API
 *
 * # The REST API of BeezUP system ## Overview The REST APIs provide programmatic access to read and write BeezUP data.  Basically, with this API you will be able to do everything like you were with your browser on https://go.beezup.com !  The main features are: - Register and manage your account - Create and manage and share your stores with your friends/co-workers. - Import your product catalog and schedule the auto importation - Search the channels your want to use - Configure your channels for your catalogs to export your product information:     - cost and general settings     - category and columns mappings     - your will be able to create and manage your custom column     - put in place exlusion filters based on simple conditions on your product data     - override product values     - get product vision for a channel catalog scope - Analyze and optimize your performance of your catalogs on all yours channels with different type of reportings by day, channel, category and by product. - Automatize your optimisation by using rules! - And of course... Manage your orders harvested from all your marketplaces:     - Synchronize your orders in an uniformized way     - Get the available actions and update the order status - ...and more!  ## Authentication credentials The public API with the base path **_/v2/public** have been put in place to give you an entry point to our system for the user registration, login and lost password. The public API does not require any credentials. We give you the some public list of values and public channels for our public commercial web site [www.beezup.com](http://www.beezup.com).  The user API with the base path **_/v2/user** requires a token which is available on this page: https://go.beezup.com/Account/MyAccount  ## Things to keep in mind ### API Rate Limits - The BeezUP REST API is limited to 100 calls/minute.  ### Media type The default media type for requests and responses is application/json. Where noted, some operations support other content types. If no additional content type is mentioned for a specific operation, then the media type is application/json.  ### Required content type The required and default encoding for the request and responses is UTF8.  ### Required date time format All our date time are formatted in ISO 8601 format: 2014-06-24T16:25:00Z.  ### Base URL The Base URL of the BeezUP API Order Management REST API conforms to the following template.  https://api.beezup.com  All URLs returned by the BeezUP API are relative to this base URL, and all requests to the REST API must use this base URL template.  You can test our API on https://api-docs.beezup.com/swagger-ui\\ You can contact us on [gitter, #BeezUP/API](https://gitter.im/BeezUP/API)
 *
 * OpenAPI spec version: 2.0
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 * Swagger Codegen version: 2.4.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */

namespace Swagger\Client\Model;

use \ArrayAccess;
use \Swagger\Client\ObjectSerializer;

/**
 * ReportAdvancedFilters Class Doc Comment
 *
 * @category Class
 * @package  Swagger\Client
 * <AUTHOR> Codegen team
 * @link     https://github.com/swagger-api/swagger-codegen
 */
class ReportAdvancedFilters implements ModelInterface, ArrayAccess
{
    const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $swaggerModelName = 'reportAdvancedFilters';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerTypes = [
        'margin_type' => '\Swagger\Client\Model\MarginType',
        'global_margin_percent' => 'int',
        'link_click_to_order_type' => '\Swagger\Client\Model\LinkClickToOrderType',
        'link_click_to_order_max_day' => 'int',
        'only_payment_validated_orders' => 'bool',
        'only_direct_sales' => 'bool',
        'performance_indicator_formula' => '\Swagger\Client\Model\PerformanceIndicatorFormula'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $swaggerFormats = [
        'margin_type' => null,
        'global_margin_percent' => null,
        'link_click_to_order_type' => null,
        'link_click_to_order_max_day' => null,
        'only_payment_validated_orders' => null,
        'only_direct_sales' => null,
        'performance_indicator_formula' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerTypes()
    {
        return self::$swaggerTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function swaggerFormats()
    {
        return self::$swaggerFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'margin_type' => 'marginType',
        'global_margin_percent' => 'globalMarginPercent',
        'link_click_to_order_type' => 'linkClickToOrderType',
        'link_click_to_order_max_day' => 'linkClickToOrderMaxDay',
        'only_payment_validated_orders' => 'onlyPaymentValidatedOrders',
        'only_direct_sales' => 'onlyDirectSales',
        'performance_indicator_formula' => 'performanceIndicatorFormula'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'margin_type' => 'setMarginType',
        'global_margin_percent' => 'setGlobalMarginPercent',
        'link_click_to_order_type' => 'setLinkClickToOrderType',
        'link_click_to_order_max_day' => 'setLinkClickToOrderMaxDay',
        'only_payment_validated_orders' => 'setOnlyPaymentValidatedOrders',
        'only_direct_sales' => 'setOnlyDirectSales',
        'performance_indicator_formula' => 'setPerformanceIndicatorFormula'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'margin_type' => 'getMarginType',
        'global_margin_percent' => 'getGlobalMarginPercent',
        'link_click_to_order_type' => 'getLinkClickToOrderType',
        'link_click_to_order_max_day' => 'getLinkClickToOrderMaxDay',
        'only_payment_validated_orders' => 'getOnlyPaymentValidatedOrders',
        'only_direct_sales' => 'getOnlyDirectSales',
        'performance_indicator_formula' => 'getPerformanceIndicatorFormula'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$swaggerModelName;
    }

    

    

    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['margin_type'] = isset($data['margin_type']) ? $data['margin_type'] : null;
        $this->container['global_margin_percent'] = isset($data['global_margin_percent']) ? $data['global_margin_percent'] : null;
        $this->container['link_click_to_order_type'] = isset($data['link_click_to_order_type']) ? $data['link_click_to_order_type'] : null;
        $this->container['link_click_to_order_max_day'] = isset($data['link_click_to_order_max_day']) ? $data['link_click_to_order_max_day'] : null;
        $this->container['only_payment_validated_orders'] = isset($data['only_payment_validated_orders']) ? $data['only_payment_validated_orders'] : false;
        $this->container['only_direct_sales'] = isset($data['only_direct_sales']) ? $data['only_direct_sales'] : false;
        $this->container['performance_indicator_formula'] = isset($data['performance_indicator_formula']) ? $data['performance_indicator_formula'] : null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['margin_type'] === null) {
            $invalidProperties[] = "'margin_type' can't be null";
        }
        if (!is_null($this->container['global_margin_percent']) && ($this->container['global_margin_percent'] > 100)) {
            $invalidProperties[] = "invalid value for 'global_margin_percent', must be smaller than or equal to 100.";
        }

        if (!is_null($this->container['global_margin_percent']) && ($this->container['global_margin_percent'] < 0)) {
            $invalidProperties[] = "invalid value for 'global_margin_percent', must be bigger than or equal to 0.";
        }

        if ($this->container['link_click_to_order_type'] === null) {
            $invalidProperties[] = "'link_click_to_order_type' can't be null";
        }
        if (!is_null($this->container['link_click_to_order_max_day']) && ($this->container['link_click_to_order_max_day'] > 180)) {
            $invalidProperties[] = "invalid value for 'link_click_to_order_max_day', must be smaller than or equal to 180.";
        }

        if (!is_null($this->container['link_click_to_order_max_day']) && ($this->container['link_click_to_order_max_day'] < 0)) {
            $invalidProperties[] = "invalid value for 'link_click_to_order_max_day', must be bigger than or equal to 0.";
        }

        if ($this->container['only_payment_validated_orders'] === null) {
            $invalidProperties[] = "'only_payment_validated_orders' can't be null";
        }
        if ($this->container['only_direct_sales'] === null) {
            $invalidProperties[] = "'only_direct_sales' can't be null";
        }
        if ($this->container['performance_indicator_formula'] === null) {
            $invalidProperties[] = "'performance_indicator_formula' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {

        if ($this->container['margin_type'] === null) {
            return false;
        }
        if ($this->container['global_margin_percent'] > 100) {
            return false;
        }
        if ($this->container['global_margin_percent'] < 0) {
            return false;
        }
        if ($this->container['link_click_to_order_type'] === null) {
            return false;
        }
        if ($this->container['link_click_to_order_max_day'] > 180) {
            return false;
        }
        if ($this->container['link_click_to_order_max_day'] < 0) {
            return false;
        }
        if ($this->container['only_payment_validated_orders'] === null) {
            return false;
        }
        if ($this->container['only_direct_sales'] === null) {
            return false;
        }
        if ($this->container['performance_indicator_formula'] === null) {
            return false;
        }
        return true;
    }


    /**
     * Gets margin_type
     *
     * @return \Swagger\Client\Model\MarginType
     */
    public function getMarginType()
    {
        return $this->container['margin_type'];
    }

    /**
     * Sets margin_type
     *
     * @param \Swagger\Client\Model\MarginType $margin_type margin_type
     *
     * @return $this
     */
    public function setMarginType($margin_type)
    {
        $this->container['margin_type'] = $margin_type;

        return $this;
    }

    /**
     * Gets global_margin_percent
     *
     * @return int
     */
    public function getGlobalMarginPercent()
    {
        return $this->container['global_margin_percent'];
    }

    /**
     * Sets global_margin_percent
     *
     * @param int $global_margin_percent If the margin type is 'Global', indicate the percentage of sale price.
     *
     * @return $this
     */
    public function setGlobalMarginPercent($global_margin_percent)
    {

        if (!is_null($global_margin_percent) && ($global_margin_percent > 100)) {
            throw new \InvalidArgumentException('invalid value for $global_margin_percent when calling ReportAdvancedFilters., must be smaller than or equal to 100.');
        }
        if (!is_null($global_margin_percent) && ($global_margin_percent < 0)) {
            throw new \InvalidArgumentException('invalid value for $global_margin_percent when calling ReportAdvancedFilters., must be bigger than or equal to 0.');
        }

        $this->container['global_margin_percent'] = $global_margin_percent;

        return $this;
    }

    /**
     * Gets link_click_to_order_type
     *
     * @return \Swagger\Client\Model\LinkClickToOrderType
     */
    public function getLinkClickToOrderType()
    {
        return $this->container['link_click_to_order_type'];
    }

    /**
     * Sets link_click_to_order_type
     *
     * @param \Swagger\Client\Model\LinkClickToOrderType $link_click_to_order_type link_click_to_order_type
     *
     * @return $this
     */
    public function setLinkClickToOrderType($link_click_to_order_type)
    {
        $this->container['link_click_to_order_type'] = $link_click_to_order_type;

        return $this;
    }

    /**
     * Gets link_click_to_order_max_day
     *
     * @return int
     */
    public function getLinkClickToOrderMaxDay()
    {
        return $this->container['link_click_to_order_max_day'];
    }

    /**
     * Sets link_click_to_order_max_day
     *
     * @param int $link_click_to_order_max_day If the linkOrderType is OnClickDate, indicate the max day to search the click from the order
     *
     * @return $this
     */
    public function setLinkClickToOrderMaxDay($link_click_to_order_max_day)
    {

        if (!is_null($link_click_to_order_max_day) && ($link_click_to_order_max_day > 180)) {
            throw new \InvalidArgumentException('invalid value for $link_click_to_order_max_day when calling ReportAdvancedFilters., must be smaller than or equal to 180.');
        }
        if (!is_null($link_click_to_order_max_day) && ($link_click_to_order_max_day < 0)) {
            throw new \InvalidArgumentException('invalid value for $link_click_to_order_max_day when calling ReportAdvancedFilters., must be bigger than or equal to 0.');
        }

        $this->container['link_click_to_order_max_day'] = $link_click_to_order_max_day;

        return $this;
    }

    /**
     * Gets only_payment_validated_orders
     *
     * @return bool
     */
    public function getOnlyPaymentValidatedOrders()
    {
        return $this->container['only_payment_validated_orders'];
    }

    /**
     * Sets only_payment_validated_orders
     *
     * @param bool $only_payment_validated_orders If true, you will get the only the orders with payment validated. Otherwise, you will get all orders validated or not.
     *
     * @return $this
     */
    public function setOnlyPaymentValidatedOrders($only_payment_validated_orders)
    {
        $this->container['only_payment_validated_orders'] = $only_payment_validated_orders;

        return $this;
    }

    /**
     * Gets only_direct_sales
     *
     * @return bool
     */
    public function getOnlyDirectSales()
    {
        return $this->container['only_direct_sales'];
    }

    /**
     * Sets only_direct_sales
     *
     * @param bool $only_direct_sales If true, you will get only direct sales. Otherwise the indirect sales will be included.
     *
     * @return $this
     */
    public function setOnlyDirectSales($only_direct_sales)
    {
        $this->container['only_direct_sales'] = $only_direct_sales;

        return $this;
    }

    /**
     * Gets performance_indicator_formula
     *
     * @return \Swagger\Client\Model\PerformanceIndicatorFormula
     */
    public function getPerformanceIndicatorFormula()
    {
        return $this->container['performance_indicator_formula'];
    }

    /**
     * Sets performance_indicator_formula
     *
     * @param \Swagger\Client\Model\PerformanceIndicatorFormula $performance_indicator_formula performance_indicator_formula
     *
     * @return $this
     */
    public function setPerformanceIndicatorFormula($performance_indicator_formula)
    {
        $this->container['performance_indicator_formula'] = $performance_indicator_formula;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed
     */
    public function offsetGet($offset)
    {
        return isset($this->container[$offset]) ? $this->container[$offset] : null;
    }

    /**
     * Sets value based on offset.
     *
     * @param integer $offset Offset
     * @param mixed   $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        if (defined('JSON_PRETTY_PRINT')) { // use JSON pretty print
            return json_encode(
                ObjectSerializer::sanitizeForSerialization($this),
                JSON_PRETTY_PRINT
            );
        }

        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


