<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS_IMAGE');

	header('Content-Type: application/csv');
	header('Content-disposition: filename=images-produits.csv');
	
	function lineCSVImage( $p, $img ){
		global $config;

		$csv = '';
		$rimg = img_images_get( $img );
		if( $rimg && ria_mysql_num_rows($rimg) ){
			$img = ria_mysql_fetch_array($rimg);
			
			$csv = $p['ref'].';';
			$csv .= $p['name'].';';
			$csv .= $img['src_name']!='' ? $img['src_name'] : $p['ref'];
			$csv .= $img['type']!='' ? '.'.$img['type'].';' : ';';
			$csv .= $config['site_url'].'/admin/documents/images/download.php?image='.$img['id'].'&filename='.($img['src_name']!='' ? $img['src_name'] : $p['ref']).'.'.$img['type'];
			$csv .= "\n";
		}
		
		return $csv;
	}
	
	// header du fichier csv
	$csv = _('Référence du produit').';'._('Désignation du produit').';'._('Nom d\'origine du fichier').';'._('Url de l\'image')."\n";
	
	$rp = prd_products_get_simple( 0, '', false, 0, false, false, false, false );
	if( $rp ){
		while( $p = ria_mysql_fetch_array($rp) ){
			
			// image principale
			if( $p['img_id'] )
				$csv .= lineCSVImage( $p, $p['img_id'] );
			
			// images secondaire
			$rimg = prd_images_get( $p['id'] );
			if( $rimg ){
				while( $img = ria_mysql_fetch_array($rimg) )
					$csv .= lineCSVImage( $p, $img['id'] );
			}
			
		}
	}
	
	print utf8_decode( $csv );
