<?php

use Riashop\PriceWatching\models\LinearRaised\prw_followed_lists;

// Vérifie que l'utilisateur en cours peut accéder à cette page
gu_if_authorized_else_403('_RGH_ADMIN_CATALOG_LINEAR_RAISED_CONFIG');

$is_creation = true;
$list = array();
$error = null;
$url_param = array();

// Bouton Enregistrer
if (isset($_POST['save'])) {
	if (!isset($_POST['name']) || !trim($_POST['name'])) {
		$error = _("Vous devez saisir un libellé");
	}
	if (!isset($_POST['type']) || !trim($_POST['type'])) {
		$error = _("Vous devez choisir un type d'assortiment");
	}

	if (isset($_POST['name'], $_POST['type'])) {
		$list['name'] = $_POST['name'];
		$list['type'] = $_POST['type'];
	}

	if (is_null($error)) {
		try{
			$id = prw_followed_lists::add($list['name'], $list['type']);
			if (!$id) {
				throw new Exception(_("Une erreur est survenue lors de la création de votre assortiment."));
			}else{
				$_SESSION['flash-success'] = str_replace("#param[nom_assortiment]#", htmlspecialchars($list['name']), _("L'assortiment #param[nom_assortiment]# a été créer avec succès."));
				header('Location: /admin/catalog/linear-raised/edit.php?id='.$id);
				exit;
			}
		}catch(Exception $e) {
			$error = $e->getMessage();
		}
	}

}

if (isset($_GET['id']) && is_numeric($_GET['id'])) {
	$result = prw_followed_lists::get($_GET['id']);
	if (!$result) {
		header('Location: /admin/catalog/linear-raised/');
		exit;
	}
	$list = ria_mysql_fetch_assoc($result);
	$title = $list['name'];
	$url_param['id'] = $list['id'];
	$is_creation = false;
	if (isset($_POST['del'])) {
		if(!prw_followed_lists::delete($list['id'])) {
			$error = _('Une erreur est survenue lors de la suppression de l\'assortiment.');
		}else{
			$_SESSION['flash-success'] = str_replace("#param[nom_assortiment]#", " <strong>".htmlspecialchars($list['name'])."</strong> ",  _("L'assortiment #param[nom_assortiment]# a été supprimé avec succès."));
			header('Location: /admin/catalog/linear-raised/');
			exit;
		}
	}
}else{
	$title = _('Nouvel assortiment de produits');
}

$tab_path = './../../views/catalog/linear-raised/tabs/';
$allowed_tabs = array(
	'general' => $tab_path.'general.php',
	'relations' => $tab_path.'relations.php',
);
$tab = ria_array_get($_GET, 'tab', 'general');

if (!in_array($tab, array_keys($allowed_tabs))) {
	$tab = 'general';
}
$tab_view = $allowed_tabs[$tab];

define('ADMIN_PAGE_TITLE', $title);
require_once('admin/skin/header.inc.php');
?>
<h2><?php echo htmlspecialchars( $title ); ?></h2>
<?php if (!is_null($error)) { ?>
	<div class="error"><?php echo $error?></div>
<?php } ?>
<?php if (isset($_SESSION['flash-success'])) { ?>
	<div class="success">
		<?php
			echo $_SESSION['flash-success'];
			unset($_SESSION['flash-success']);
		?>
	</div>
<?php } ?>
<ul class="tabstrip">
	<li>
		<a
			href="<?php echo viewParsedUrl('/admin/catalog/linear-raised/edit.php?tab=general', $url_param)?>"
			class="tab <?php if( $tab == 'general' ) {print 'selected';} ?>"
		>
			<?php echo _('Général') ?>
		</a>
	</li>
	<?php if (!$is_creation) {?>
		<li>
			<a
				href="<?php echo viewParsedUrl('/admin/catalog/linear-raised/edit.php?tab=relations', $url_param)?>"
				class="tab <?php if( $tab == 'relations' ) {print 'selected';} ?>"
			>
				<?php echo _('Relations') ?>
			</a>
		</li>
	<?php } ?>
</ul>
<div id="tabpanel">
	<?php 
		include($allowed_tabs[$tab]);
	?>
</div>

<?php
require_once('admin/skin/footer.inc.php');

function viewParsedUrl($url, $params=array()) {
	$parts = parse_url($url);
	parse_str($parts['query'], $query);
	$params = array_merge($query, $params);

	return $parts['path'].'?'.http_build_query($params);
}