<?php

/*
 * This file is part of Twig.
 *
 * (c) <PERSON><PERSON><PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Twig\Node;

/**
 * Represents a displayable node in the AST.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface NodeOutputInterface
{
}

class_alias('Twig\Node\NodeOutputInterface', 'Twig_NodeOutputInterface');
