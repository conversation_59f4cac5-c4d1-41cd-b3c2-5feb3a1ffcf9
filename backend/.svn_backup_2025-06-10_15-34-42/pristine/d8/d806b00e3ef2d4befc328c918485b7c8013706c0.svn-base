{"icons": {"x32": "https://www.google.com/images/icons/product/search-32.gif", "x16": "https://www.google.com/images/icons/product/search-16.gif"}, "revision": "20210120", "documentationLink": "https://cloud.google.com/bigquery/", "ownerDomain": "google.com", "discoveryVersion": "v1", "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/devstorage.read_only": {"description": "View your data in Google Cloud Storage"}, "https://www.googleapis.com/auth/bigquery.readonly": {"description": "View your data in Google BigQuery"}, "https://www.googleapis.com/auth/devstorage.full_control": {"description": "Manage your data and permissions in Google Cloud Storage"}, "https://www.googleapis.com/auth/devstorage.read_write": {"description": "Manage your data in Google Cloud Storage"}, "https://www.googleapis.com/auth/bigquery.insertdata": {"description": "Insert data into Google BigQuery"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "View and manage your data across Google Cloud Platform services"}, "https://www.googleapis.com/auth/cloud-platform.read-only": {"description": "View your data across Google Cloud Platform services"}, "https://www.googleapis.com/auth/bigquery": {"description": "View and manage your data in Google BigQuery"}}}}, "description": "A data platform for customers to create, manage, share and query data.", "schemas": {"Binding": {"type": "object", "id": "Binding", "description": "Associates `members` with a `role`.", "properties": {"role": {"type": "string", "description": "Role that is assigned to `members`. For example, `roles/viewer`, `roles/editor`, or `roles/owner`."}, "condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the members in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"items": {"type": "string"}, "description": "Specifies the identities requesting access for a Cloud Platform resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a service account. For example, `<EMAIL>`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. ", "type": "array"}}}, "Entry": {"type": "object", "description": "A single entry in the confusion matrix.", "id": "Entry", "properties": {"predictedLabel": {"description": "The predicted label. For confidence_threshold > 0, we will also add an entry indicating the number of items under the confidence threshold.", "type": "string"}, "itemCount": {"description": "Number of items being predicted as this label.", "format": "int64", "type": "string"}}}, "StandardSqlDataType": {"id": "StandardSqlDataType", "description": "The type of a variable, e.g., a function argument. Examples: INT64: {type_kind=\"INT64\"} ARRAY: {type_kind=\"ARRAY\", array_element_type=\"STRING\"} STRUCT>: {type_kind=\"STRUCT\", struct_type={fields=[ {name=\"x\", type={type_kind=\"STRING\"}}, {name=\"y\", type={type_kind=\"ARRAY\", array_element_type=\"DATE\"}} ]}}", "type": "object", "properties": {"arrayElementType": {"description": "The type of the array's elements, if type_kind = \"ARRAY\".", "$ref": "StandardSqlDataType"}, "structType": {"$ref": "StandardSqlStructType", "description": "The fields of this struct, in order, if type_kind = \"STRUCT\"."}, "typeKind": {"enumDescriptions": ["Invalid type.", "Encoded as a string in decimal format.", "Encoded as a boolean \"false\" or \"true\".", "Encoded as a number, or string \"NaN\", \"Infinity\" or \"-Infinity\".", "Encoded as a string value.", "Encoded as a base64 string per RFC 4648, section 4.", "Encoded as an RFC 3339 timestamp with mandatory \"Z\" time zone string: 1985-04-12T23:20:50.52Z", "Encoded as RFC 3339 full-date format string: 1985-04-12", "Encoded as RFC 3339 partial-time format string: 23:20:50.52", "Encoded as RFC 3339 full-date \"T\" partial-time: 1985-04-12T23:20:50.52", "Encoded as WKT", "Encoded as a decimal string.", "Encoded as a decimal string.", "Encoded as a list with types matching Type.array_type.", "Encoded as a list with fields of type Type.struct_type[i]. List is used because a JSON object cannot have duplicate field names."], "description": "Required. The top level type of this field. Can be any standard SQL data type (e.g., \"INT64\", \"DATE\", \"ARRAY\").", "enum": ["TYPE_KIND_UNSPECIFIED", "INT64", "BOOL", "FLOAT64", "STRING", "BYTES", "TIMESTAMP", "DATE", "TIME", "DATETIME", "GEOGRAPHY", "NUMERIC", "BIGNUMERIC", "ARRAY", "STRUCT"], "type": "string"}}}, "ExplainQueryStep": {"type": "object", "id": "ExplainQueryStep", "properties": {"kind": {"description": "Machine-readable operation type.", "type": "string"}, "substeps": {"description": "Human-readable stage descriptions.", "type": "array", "items": {"type": "string"}}}}, "CsvOptions": {"type": "object", "id": "CsvOptions", "properties": {"allowQuotedNewlines": {"type": "boolean", "description": "[Optional] Indicates if BigQuery should allow quoted data sections that contain newline characters in a CSV file. The default value is false."}, "encoding": {"type": "string", "description": "[Optional] The character encoding of the data. The supported values are UTF-8 or ISO-8859-1. The default value is UTF-8. BigQuery decodes the data after the raw, binary data has been split using the values of the quote and fieldDelimiter properties."}, "allowJaggedRows": {"type": "boolean", "description": "[Optional] Indicates if BigQuery should accept rows that are missing trailing optional columns. If true, BigQuery treats missing trailing columns as null values. If false, records with missing trailing columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false."}, "fieldDelimiter": {"type": "string", "description": "[Optional] The separator for fields in a CSV file. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. BigQuery also supports the escape sequence \"\\t\" to specify a tab separator. The default value is a comma (',')."}, "quote": {"default": "\"", "pattern": ".?", "type": "string", "description": "[Optional] The value that is used to quote data sections in a CSV file. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. The default value is a double-quote ('\"'). If your data does not contain quoted sections, set the property value to an empty string. If your data contains quoted newline characters, you must also set the allowQuotedNewlines property to true."}, "skipLeadingRows": {"type": "string", "description": "[Optional] The number of rows at the top of a CSV file that BigQuery will skip when reading the data. The default value is 0. This property is useful if you have header rows in the file that should be skipped. When autodetect is on, the behavior is the following: * skipLeadingRows unspecified - Autodetect tries to detect headers in the first row. If they are not detected, the row is read as data. Otherwise data is read starting from the second row. * skipLeadingRows is 0 - Instructs autodetect that there are no headers and data should be read starting from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1 rows and tries to detect headers in row N. If headers are not detected, row N is just skipped. Otherwise row N is used to extract column names for the detected schema.", "format": "int64"}}}, "ModelReference": {"id": "ModelReference", "type": "object", "properties": {"datasetId": {"description": "[Required] The ID of the dataset containing this model.", "type": "string"}, "modelId": {"type": "string", "description": "[Required] The ID of the model. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters."}, "projectId": {"type": "string", "description": "[Required] The ID of the project containing this model."}}}, "GlobalExplanation": {"id": "GlobalExplanation", "type": "object", "description": "Global explanations containing the top most important features after training.", "properties": {"explanations": {"description": "A list of the top global explanations. Sorted by absolute value of attribution in descending order.", "items": {"$ref": "Explanation"}, "type": "array"}, "classLabel": {"description": "Class label for this set of global explanations. Will be empty/null for binary logistic and linear regression models. Sorted alphabetically in descending order.", "type": "string"}}}, "ProjectList": {"type": "object", "id": "ProjectList", "properties": {"totalItems": {"format": "int32", "type": "integer", "description": "The total number of projects in the list."}, "etag": {"description": "A hash of the page of results", "type": "string"}, "kind": {"type": "string", "description": "The type of list.", "default": "bigquery#projectList"}, "nextPageToken": {"type": "string", "description": "A token to request the next page of results."}, "projects": {"description": "Projects to which you have at least READ access.", "type": "array", "items": {"properties": {"numericId": {"type": "string", "format": "uint64", "description": "The numeric ID of this project."}, "id": {"description": "An opaque ID of this project.", "type": "string"}, "friendlyName": {"description": "A descriptive name for this project.", "type": "string"}, "projectReference": {"description": "A unique reference to this project.", "$ref": "ProjectReference"}, "kind": {"type": "string", "description": "The resource type.", "default": "bigquery#project"}}, "type": "object"}}}}, "TrainingOptions": {"id": "TrainingOptions", "description": "Options used in model training.", "properties": {"distanceType": {"enum": ["DISTANCE_TYPE_UNSPECIFIED", "EUCLIDEAN", "COSINE"], "description": "Distance type for clustering models.", "type": "string", "enumDescriptions": ["", "Eculidean distance.", "Cosine distance."]}, "userColumn": {"description": "User column specified for matrix factorization models.", "type": "string"}, "timeSeriesTimestampColumn": {"type": "string", "description": "Column to be designated as time series timestamp for ARIMA model."}, "labelClassWeights": {"additionalProperties": {"type": "number", "format": "double"}, "description": "Weights associated with each label class, for rebalancing the training data. Only applicable for classification models.", "type": "object"}, "timeSeriesDataColumn": {"description": "Column to be designated as time series data for ARIMA model.", "type": "string"}, "hiddenUnits": {"description": "Hidden units for dnn models.", "type": "array", "items": {"format": "int64", "type": "string"}}, "dataSplitEvalFraction": {"format": "double", "type": "number", "description": "The fraction of evaluation data over the whole input data. The rest of data will be used as training data. The format should be double. Accurate to two decimal places. Default value is 0.2."}, "timeSeriesIdColumn": {"description": "The id column that will be used to indicate different time series to forecast in parallel.", "type": "string"}, "modelUri": {"type": "string", "description": "Google Cloud Storage URI from which the model was imported. Only applicable for imported models."}, "learnRateStrategy": {"enum": ["LEARN_RATE_STRATEGY_UNSPECIFIED", "LINE_SEARCH", "CONSTANT"], "description": "The strategy to determine learn rate for the current iteration.", "enumDescriptions": ["", "Use line search to determine learning rate.", "Use a constant learning rate."], "type": "string"}, "kmeansInitializationMethod": {"description": "The method used to initialize the centroids for kmeans algorithm.", "enumDescriptions": ["Unspecified initialization method.", "Initializes the centroids randomly.", "Initializes the centroids using data specified in kmeans_initialization_column.", "Initializes with kmeans++."], "type": "string", "enum": ["KMEANS_INITIALIZATION_METHOD_UNSPECIFIED", "RANDOM", "CUSTOM", "KMEANS_PLUS_PLUS"]}, "maxIterations": {"format": "int64", "description": "The maximum number of iterations in training. Used only for iterative training algorithms.", "type": "string"}, "includeDrift": {"type": "boolean", "description": "Include drift when fitting an ARIMA model."}, "batchSize": {"description": "Batch size for dnn models.", "format": "int64", "type": "string"}, "earlyStop": {"description": "Whether to stop early when the loss doesn't improve significantly any more (compared to min_relative_progress). Used only for iterative training algorithms.", "type": "boolean"}, "dataFrequency": {"enumDescriptions": ["", "Automatically inferred from timestamps.", "Yearly data.", "Quarterly data.", "Monthly data.", "Weekly data.", "Daily data.", "Hourly data.", "Per-minute data."], "enum": ["DATA_FREQUENCY_UNSPECIFIED", "AUTO_FREQUENCY", "YEARLY", "QUARTERLY", "MONTHLY", "WEEKLY", "DAILY", "HOURLY", "PER_MINUTE"], "type": "string", "description": "The data frequency of a time series."}, "optimizationStrategy": {"enum": ["OPTIMIZATION_STRATEGY_UNSPECIFIED", "BATCH_GRADIENT_DESCENT", "NORMAL_EQUATION"], "enumDescriptions": ["", "Uses an iterative batch gradient descent algorithm.", "Uses a normal equation to solve linear regression problem."], "type": "string", "description": "Optimization strategy for training linear regression models."}, "kmeansInitializationColumn": {"description": "The column used to provide the initial centroids for kmeans algorithm when kmeans_initialization_method is CUSTOM.", "type": "string"}, "minRelativeProgress": {"format": "double", "description": "When early_stop is true, stops training when accuracy improvement is less than 'min_relative_progress'. Used only for iterative training algorithms.", "type": "number"}, "warmStart": {"type": "boolean", "description": "Whether to train a model from the last checkpoint."}, "numClusters": {"description": "Number of clusters for clustering models.", "format": "int64", "type": "string"}, "subsample": {"description": "Subsample fraction of the training data to grow tree to prevent overfitting for boosted tree models.", "format": "double", "type": "number"}, "autoArima": {"type": "boolean", "description": "Whether to enable auto ARIMA or not."}, "horizon": {"type": "string", "format": "int64", "description": "The number of periods ahead that need to be forecasted."}, "walsAlpha": {"type": "number", "description": "Hyperparameter for matrix factoration when implicit feedback type is specified.", "format": "double"}, "nonSeasonalOrder": {"$ref": "ArimaOrder", "description": "A specification of the non-seasonal part of the ARIMA model: the three components (p, d, q) are the AR order, the degree of differencing, and the MA order."}, "dataSplitMethod": {"enum": ["DATA_SPLIT_METHOD_UNSPECIFIED", "RANDOM", "CUSTOM", "SEQUENTIAL", "NO_SPLIT", "AUTO_SPLIT"], "description": "The data split type for training and evaluation, e.g. RANDOM.", "enumDescriptions": ["", "Splits data randomly.", "Splits data with the user provided tags.", "Splits data sequentially.", "Data split will be skipped.", "Splits data automatically: Uses NO_SPLIT if the data size is small. Otherwise uses RANDOM."], "type": "string"}, "dataSplitColumn": {"type": "string", "description": "The column to split data with. This column won't be used as a feature. 1. When data_split_method is CUSTOM, the corresponding column should be boolean. The rows with true value tag are eval data, and the false are training data. 2. When data_split_method is SEQ, the first DATA_SPLIT_EVAL_FRACTION rows (from smallest to largest) in the corresponding column are used as training data, and the rest are eval data. It respects the order in Orderable data types: https://cloud.google.com/bigquery/docs/reference/standard-sql/data-types#data-type-properties"}, "holidayRegion": {"enum": ["HOLIDAY_REGION_UNSPECIFIED", "GLOBAL", "NA", "JAPAC", "EMEA", "LAC", "AE", "AR", "AT", "AU", "BE", "BR", "CA", "CH", "CL", "CN", "CO", "CS", "CZ", "DE", "DK", "DZ", "EC", "EE", "EG", "ES", "FI", "FR", "GB", "GR", "HK", "HU", "ID", "IE", "IL", "IN", "IR", "IT", "JP", "KR", "LV", "MA", "MX", "MY", "NG", "NL", "NO", "NZ", "PE", "PH", "PK", "PL", "PT", "RO", "RS", "RU", "SA", "SE", "SG", "SI", "SK", "TH", "TR", "TW", "UA", "US", "VE", "VN", "ZA"], "description": "The geographical region based on which the holidays are considered in time series modeling. If a valid value is specified, then holiday effects modeling is enabled.", "type": "string", "enumDescriptions": ["Holiday region unspecified.", "Global.", "North America.", "Japan and Asia Pacific: Korea, Greater China, India, Australia, and New Zealand.", "Europe, the Middle East and Africa.", "Latin America and the Caribbean.", "United Arab Emirates", "Argentina", "Austria", "Australia", "Belgium", "Brazil", "Canada", "Switzerland", "Chile", "China", "Colombia", "Czechoslovakia", "Czech Republic", "Germany", "Denmark", "Algeria", "Ecuador", "Estonia", "Egypt", "Spain", "Finland", "France", "Great Britain (United Kingdom)", "Greece", "Hong Kong", "Hungary", "Indonesia", "Ireland", "Israel", "India", "Iran", "Italy", "Japan", "Korea (South)", "Latvia", "Morocco", "Mexico", "Malaysia", "Nigeria", "Netherlands", "Norway", "New Zealand", "Peru", "Philippines", "Pakistan", "Poland", "Portugal", "Romania", "Serbia", "Russian Federation", "Saudi Arabia", "Sweden", "Singapore", "Slovenia", "Slovakia", "Thailand", "Turkey", "Taiwan", "Ukraine", "United States", "Venezuela", "Viet Nam", "South Africa"]}, "learnRate": {"description": "Learning rate in training. Used only for iterative training algorithms.", "type": "number", "format": "double"}, "preserveInputStructs": {"type": "boolean", "description": "Whether to preserve the input structs in output feature names. Suppose there is a struct A with field b. When false (default), the output feature name is A_b. When true, the output feature name is A.b."}, "maxTreeDepth": {"format": "int64", "description": "Maximum depth of a tree for boosted tree models.", "type": "string"}, "minSplitLoss": {"description": "Minimum split loss for boosted tree models.", "format": "double", "type": "number"}, "feedbackType": {"type": "string", "description": "Feedback type that specifies which algorithm to run for matrix factorization.", "enumDescriptions": ["", "Use weighted-als for implicit feedback problems.", "Use nonweighted-als for explicit feedback problems."], "enum": ["FEEDBACK_TYPE_UNSPECIFIED", "IMPLICIT", "EXPLICIT"]}, "dropout": {"type": "number", "format": "double", "description": "Dropout probability for dnn models."}, "lossType": {"description": "Type of loss function used during training run.", "enumDescriptions": ["", "Mean squared loss, used for linear regression.", "Mean log loss, used for logistic regression."], "type": "string", "enum": ["LOSS_TYPE_UNSPECIFIED", "MEAN_SQUARED_LOSS", "MEAN_LOG_LOSS"]}, "numFactors": {"format": "int64", "type": "string", "description": "Num factors specified for matrix factorization models."}, "inputLabelColumns": {"description": "Name of input label columns in training data.", "type": "array", "items": {"type": "string"}}, "autoArimaMaxOrder": {"description": "The max value of non-seasonal p and q.", "format": "int64", "type": "string"}, "initialLearnRate": {"type": "number", "format": "double", "description": "Specifies the initial learning rate for the line search learn rate strategy."}, "l1Regularization": {"description": "L1 regularization coefficient.", "type": "number", "format": "double"}, "itemColumn": {"type": "string", "description": "Item column specified for matrix factorization models."}, "l2Regularization": {"type": "number", "format": "double", "description": "L2 regularization coefficient."}}, "type": "object"}, "GetServiceAccountResponse": {"id": "GetServiceAccountResponse", "properties": {"kind": {"default": "bigquery#getServiceAccountResponse", "type": "string", "description": "The resource type of the response."}, "email": {"type": "string", "description": "The service account email address."}}, "type": "object"}, "TableDataList": {"properties": {"rows": {"type": "array", "items": {"$ref": "TableRow"}, "description": "Rows of results."}, "pageToken": {"description": "A token used for paging results. Providing this token instead of the startIndex parameter can help you retrieve stable results when an underlying table is changing.", "type": "string"}, "kind": {"type": "string", "default": "bigquery#tableDataList", "description": "The resource type of the response."}, "etag": {"type": "string", "description": "A hash of this page of results."}, "totalRows": {"format": "int64", "description": "The total number of rows in the complete table.", "type": "string"}}, "type": "object", "id": "TableDataList"}, "QueryParameterType": {"type": "object", "id": "QueryParameterType", "properties": {"structTypes": {"items": {"type": "object", "properties": {"description": {"type": "string", "description": "[Optional] Human-oriented description of the field."}, "type": {"description": "[Required] The type of this field.", "$ref": "QueryParameterType"}, "name": {"description": "[Optional] The name of this field.", "type": "string"}}}, "description": "[Optional] The types of the fields of this struct, in order, if this is a struct.", "type": "array"}, "arrayType": {"description": "[Optional] The type of the array's elements, if this is an array.", "$ref": "QueryParameterType"}, "type": {"description": "[Required] The top level type of this field.", "type": "string"}}}, "JobStatistics": {"id": "JobStatistics", "type": "object", "properties": {"quotaDeferments": {"items": {"type": "string"}, "description": "[Output-only] Quotas which delayed this job's start time.", "type": "array"}, "creationTime": {"format": "int64", "description": "[Output-only] Creation time of this job, in milliseconds since the epoch. This field will be present on all jobs.", "type": "string"}, "endTime": {"format": "int64", "type": "string", "description": "[Output-only] End time of this job, in milliseconds since the epoch. This field will be present whenever a job is in the DONE state."}, "extract": {"description": "[Output-only] Statistics for an extract job.", "$ref": "JobStatistics4"}, "totalSlotMs": {"type": "string", "format": "int64", "description": "[Output-only] Slot-milliseconds for the job."}, "rowLevelSecurityStatistics": {"$ref": "RowLevelSecurityStatistics", "description": "[Output-only] [Preview] Statistics for row-level security. Present only for query and extract jobs."}, "reservationUsage": {"type": "array", "description": "[Output-only] Job resource usage breakdown by reservation.", "items": {"properties": {"name": {"description": "[Output-only] Reservation name or \"unreserved\" for on-demand resources usage.", "type": "string"}, "slotMs": {"format": "int64", "description": "[Output-only] Slot-milliseconds the job spent in the given reservation.", "type": "string"}}, "type": "object"}}, "parentJobId": {"description": "[Output-only] If this is a child job, the id of the parent.", "type": "string"}, "totalBytesProcessed": {"description": "[Output-only] [Deprecated] Use the bytes processed in the query statistics instead.", "type": "string", "format": "int64"}, "scriptStatistics": {"description": "[Output-only] Statistics for a child job of a script.", "$ref": "ScriptStatistics"}, "startTime": {"format": "int64", "description": "[Output-only] Start time of this job, in milliseconds since the epoch. This field will be present when the job transitions from the PENDING state to either RUNNING or DONE.", "type": "string"}, "completionRatio": {"format": "double", "type": "number", "description": "[TrustedTester] [Output-only] Job progress (0.0 -> 1.0) for LOAD and EXTRACT jobs."}, "reservation_id": {"type": "string", "description": "[Output-only] Name of the primary reservation assigned to this job. Note that this could be different than reservations reported in the reservation usage field if parent reservations were used to execute this job."}, "numChildJobs": {"format": "int64", "description": "[Output-only] Number of child jobs executed.", "type": "string"}, "load": {"$ref": "JobStatistics3", "description": "[Output-only] Statistics for a load job."}, "transactionInfoTemplate": {"description": "[Output-only] [Alpha] Information of the multi-statement transaction if this job is part of one.", "$ref": "TransactionInfo"}, "query": {"description": "[Output-only] Statistics for a query job.", "$ref": "JobStatistics2"}}}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "properties": {"requestedPolicyVersion": {"description": "Optional. The policy format version to be returned. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional bindings must specify version 3. Policies without any conditional bindings may specify any valid value or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object", "id": "GetPolicyOptions"}, "ListRoutinesResponse": {"id": "ListRoutinesResponse", "type": "object", "properties": {"routines": {"items": {"$ref": "Routine"}, "type": "array", "description": "Routines in the requested dataset. Unless read_mask is set in the request, only the following fields are populated: etag, project_id, dataset_id, routine_id, routine_type, creation_time, last_modified_time, and language."}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}}, "Explanation": {"properties": {"featureName": {"description": "Full name of the feature. For non-numerical features, will be formatted like .. Overall size of feature name will always be truncated to first 120 characters.", "type": "string"}, "attribution": {"description": "Attribution of feature.", "type": "number", "format": "double"}}, "id": "Explanation", "description": "Explanation for a single feature.", "type": "object"}, "StandardSqlStructType": {"type": "object", "id": "StandardSqlStructType", "properties": {"fields": {"items": {"$ref": "StandardSqlField"}, "type": "array"}}}, "JobConfigurationExtract": {"id": "JobConfigurationExtract", "properties": {"sourceTable": {"description": "A reference to the table being exported.", "$ref": "TableReference"}, "useAvroLogicalTypes": {"description": "[Optional] If destinationFormat is set to \"AVRO\", this flag indicates whether to enable extracting applicable column types (such as TIMESTAMP) to their corresponding AVRO logical types (timestamp-micros), instead of only using their raw types (avro-long). Not applicable when extracting models.", "type": "boolean"}, "destinationUris": {"type": "array", "items": {"type": "string"}, "description": "[Pick one] A list of fully-qualified Google Cloud Storage URIs where the extracted table should be written."}, "compression": {"type": "string", "description": "[Optional] The compression type to use for exported files. Possible values include GZIP, DEFLATE, SNAPPY, and NONE. The default value is NONE. DEFLATE and SNAPPY are only supported for Avro. Not applicable when extracting models."}, "destinationFormat": {"type": "string", "description": "[Optional] The exported file format. Possible values include CSV, NEWLINE_DELIMITED_JSON, PARQUET or AVRO for tables and ML_TF_SAVED_MODEL or ML_XGBOOST_BOOSTER for models. The default value for tables is CSV. Tables with nested or repeated fields cannot be exported as CSV. The default value for models is ML_TF_SAVED_MODEL."}, "printHeader": {"description": "[Optional] Whether to print out a header row in the results. Default is true. Not applicable when extracting models.", "default": "true", "type": "boolean"}, "sourceModel": {"$ref": "ModelReference", "description": "A reference to the model being exported."}, "destinationUri": {"type": "string", "description": "[Pick one] DEPRECATED: Use destinationUris instead, passing only one URI as necessary. The fully-qualified Google Cloud Storage URI where the extracted table should be written."}, "fieldDelimiter": {"description": "[Optional] Delimiter to use between fields in the exported data. Default is ','. Not applicable when extracting models.", "type": "string"}}, "type": "object"}, "TrainingRun": {"type": "object", "id": "TrainingRun", "properties": {"dataSplitResult": {"description": "Data split result of the training run. Only set when the input data is actually split.", "$ref": "DataSplitResult"}, "evaluationMetrics": {"description": "The evaluation metrics over training/eval data that were computed at the end of training.", "$ref": "EvaluationMetrics"}, "trainingOptions": {"description": "Options that were used for this training run, includes user specified and default options that were used.", "$ref": "TrainingOptions"}, "startTime": {"type": "string", "description": "The start time of this training run.", "format": "google-datetime"}, "globalExplanations": {"description": "Global explanations for important features of the model. For multi-class models, there is one entry for each label class. For other models, there is only one entry in the list.", "type": "array", "items": {"$ref": "GlobalExplanation"}}, "results": {"description": "Output of each iteration run, results.size() <= max_iterations.", "type": "array", "items": {"$ref": "IterationResult"}}}, "description": "Information about a single training query run for the model."}, "CategoryCount": {"type": "object", "properties": {"count": {"description": "The count of training samples matching the category within the cluster.", "type": "string", "format": "int64"}, "category": {"description": "The name of category.", "type": "string"}}, "description": "Represents the count of a single category within the cluster.", "id": "CategoryCount"}, "ExternalDataConfiguration": {"properties": {"csvOptions": {"description": "Additional properties to set if sourceFormat is set to CSV.", "$ref": "CsvOptions"}, "connectionId": {"description": "[Optional, Trusted Tester] Connection for external data source.", "type": "string"}, "sourceUris": {"items": {"type": "string"}, "type": "array", "description": "[Required] The fully-qualified URIs that point to your data in Google Cloud. For Google Cloud Storage URIs: Each URI can contain one '*' wildcard character and it must come after the 'bucket' name. Size limits related to load jobs apply to external data sources. For Google Cloud Bigtable URIs: Exactly one URI can be specified and it has be a fully specified and valid HTTPS URL for a Google Cloud Bigtable table. For Google Cloud Datastore backups, exactly one URI can be specified. Also, the '*' wildcard character is not allowed."}, "ignoreUnknownValues": {"description": "[Optional] Indicates if BigQuery should allow extra values that are not represented in the table schema. If true, the extra values are ignored. If false, records with extra columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. The sourceFormat property determines what BigQuery treats as an extra value: CSV: Trailing columns JSON: Named values that don't match any column names Google Cloud Bigtable: This setting is ignored. Google Cloud Datastore backups: This setting is ignored. Avro: This setting is ignored.", "type": "boolean"}, "sourceFormat": {"type": "string", "description": "[Required] The data format. For CSV files, specify \"CSV\". For Google sheets, specify \"GOOGLE_SHEETS\". For newline-delimited JSON, specify \"NEWLINE_DELIMITED_JSON\". For Avro files, specify \"AVRO\". For Google Cloud Datastore backups, specify \"DATASTORE_BACKUP\". [Beta] For Google Cloud Bigtable, specify \"BIGTABLE\"."}, "schema": {"$ref": "TableSchema", "description": "[Optional] The schema for the data. Schema is required for CSV and JSON formats. Schema is disallowed for Google Cloud Bigtable, Cloud Datastore backups, and Avro formats."}, "googleSheetsOptions": {"$ref": "GoogleSheetsOptions", "description": "[Optional] Additional options if sourceFormat is set to GOOGLE_SHEETS."}, "autodetect": {"type": "boolean", "description": "Try to detect schema and format options automatically. Any option specified explicitly will be honored."}, "compression": {"description": "[Optional] The compression type of the data source. Possible values include GZIP and NONE. The default value is NONE. This setting is ignored for Google Cloud Bigtable, Google Cloud Datastore backups and Avro formats.", "type": "string"}, "hivePartitioningOptions": {"$ref": "HivePartitioningOptions", "description": "[Optional] Options to configure hive partitioning support."}, "maxBadRecords": {"type": "integer", "description": "[Optional] The maximum number of bad records that BigQuery can ignore when reading data. If the number of bad records exceeds this value, an invalid error is returned in the job result. This is only valid for CSV, JSON, and Google Sheets. The default value is 0, which requires that all records are valid. This setting is ignored for Google Cloud Bigtable, Google Cloud Datastore backups and Avro formats.", "format": "int32"}, "bigtableOptions": {"$ref": "BigtableOptions", "description": "[Optional] Additional options if sourceFormat is set to BIGTABLE."}}, "id": "ExternalDataConfiguration", "type": "object"}, "SetIamPolicyRequest": {"properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Cloud Platform services (such as Projects) might reject them."}, "updateMask": {"format": "google-fieldmask", "description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "type": "string"}}, "type": "object", "id": "SetIamPolicyRequest", "description": "Request message for `SetIamPolicy` method."}, "Routine": {"type": "object", "id": "Routine", "properties": {"definitionBody": {"description": "Required. The body of the routine. For functions, this is the expression in the AS clause. If language=SQL, it is the substring inside (but excluding) the parentheses. For example, for the function created with the following statement: `CREATE FUNCTION JoinLines(x string, y string) as (concat(x, \"\\n\", y))` The definition_body is `concat(x, \"\\n\", y)` (\\n is not replaced with linebreak). If language=JAVASCRIPT, it is the evaluated string in the AS clause. For example, for the function created with the following statement: `CREATE FUNCTION f() RETURNS STRING LANGUAGE js AS 'return \"\\n\";\\n'` The definition_body is `return \"\\n\";\\n` Note that both \\n are replaced with linebreaks.", "type": "string"}, "language": {"type": "string", "enumDescriptions": ["", "SQL language.", "JavaScript language."], "enum": ["LANGUAGE_UNSPECIFIED", "SQL", "JAVASCRIPT"], "description": "Optional. Defaults to \"SQL\"."}, "routineReference": {"$ref": "RoutineReference", "description": "Required. Reference describing the ID of this routine."}, "importedLibraries": {"description": "Optional. If language = \"JAVASCRIPT\", this field stores the path of the imported JAVASCRIPT libraries.", "type": "array", "items": {"type": "string"}}, "description": {"description": "Optional. [Experimental] The description of the routine if defined.", "type": "string"}, "arguments": {"description": "Optional.", "type": "array", "items": {"$ref": "Argument"}}, "etag": {"type": "string", "description": "Output only. A hash of this resource.", "readOnly": true}, "determinismLevel": {"description": "Optional. [Experimental] The determinism level of the JavaScript UDF if defined.", "enumDescriptions": ["The determinism of the UDF is unspecified.", "The UDF is deterministic, meaning that 2 function calls with the same inputs always produce the same result, even across 2 query runs.", "The UDF is not deterministic."], "enum": ["DETERMINISM_LEVEL_UNSPECIFIED", "DETERMINISTIC", "NOT_DETERMINISTIC"], "type": "string"}, "returnType": {"$ref": "StandardSqlDataType", "description": "Optional if language = \"SQL\"; required otherwise. If absent, the return type is inferred from definition_body at query time in each query that references this routine. If present, then the evaluated result will be cast to the specified returned type at query time. For example, for the functions created with the following statements: * `CREATE FUNCTION Add(x FLOAT64, y FLOAT64) RETURNS FLOAT64 AS (x + y);` * `CREATE FUNCTION Increment(x FLOAT64) AS (Add(x, 1));` * `CREATE FUNCTION Decrement(x FLOAT64) RETURNS FLOAT64 AS (Add(x, -1));` The return_type is `{type_kind: \"FLOAT64\"}` for `Add` and `Decrement`, and is absent for `Increment` (inferred as FLOAT64 at query time). Suppose the function `Add` is replaced by `CREATE OR REPLACE FUNCTION Add(x INT64, y INT64) AS (x + y);` Then the inferred return type of `Increment` is automatically changed to INT64 at query time, while the return type of `Decrement` remains FLOAT64."}, "lastModifiedTime": {"readOnly": true, "description": "Output only. The time when this routine was last modified, in milliseconds since the epoch.", "format": "int64", "type": "string"}, "creationTime": {"type": "string", "format": "int64", "description": "Output only. The time when this routine was created, in milliseconds since the epoch.", "readOnly": true}, "routineType": {"enum": ["ROUTINE_TYPE_UNSPECIFIED", "SCALAR_FUNCTION", "PROCEDURE"], "enumDescriptions": ["", "Non-builtin permanent scalar function.", "Stored procedure."], "description": "Required. The type of routine.", "type": "string"}}, "description": "A user-defined function or a stored procedure."}, "JobConfigurationQuery": {"type": "object", "id": "JobConfigurationQuery", "properties": {"query": {"description": "[Required] SQL query text to execute. The useLegacySql field can be used to indicate whether the query uses legacy SQL or standard SQL.", "type": "string"}, "allowLargeResults": {"description": "[Optional] If true and query uses legacy SQL dialect, allows the query to produce arbitrarily large result tables at a slight cost in performance. Requires destinationTable to be set. For standard SQL queries, this flag is ignored and large results are always allowed. However, you must still set destinationTable when result size exceeds the allowed maximum response size.", "type": "boolean", "default": "false"}, "tableDefinitions": {"description": "[Optional] If querying an external data source outside of BigQuery, describes the data format, location and other properties of the data source. By defining these properties, the data source can then be queried as if it were a standard BigQuery table.", "type": "object", "additionalProperties": {"$ref": "ExternalDataConfiguration"}}, "destinationTable": {"description": "[Optional] Describes the table where the query results should be stored. If not present, a new table will be created to store the results. This property must be set for large results that exceed the maximum response size.", "$ref": "TableReference"}, "preserveNulls": {"description": "[Deprecated] This property is deprecated.", "type": "boolean"}, "useQueryCache": {"type": "boolean", "default": "true", "description": "[Optional] Whether to look for the result in the query cache. The query cache is a best-effort cache that will be flushed whenever tables in the query are modified. Moreover, the query cache is only available when a query does not have a destination table specified. The default value is true."}, "destinationEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)."}, "parameterMode": {"type": "string", "description": "Standard SQL only. Set to POSITIONAL to use positional (?) query parameters or to NAMED to use named (@myparam) query parameters in this query."}, "queryParameters": {"items": {"$ref": "QueryParameter"}, "type": "array", "description": "Query parameters for standard SQL queries."}, "writeDisposition": {"type": "string", "description": "[Optional] Specifies the action that occurs if the destination table already exists. The following values are supported: WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON>uery overwrites the table data and uses the schema from the query result. WRITE_APPEND: If the table already exists, <PERSON>Query appends the data to the table. WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_EMPTY. Each action is atomic and only occurs if <PERSON>Quer<PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion."}, "defaultDataset": {"description": "[Optional] Specifies the default dataset to use for unqualified table names in the query. Note that this does not alter behavior of unqualified dataset names.", "$ref": "DatasetReference"}, "connectionProperties": {"items": {"$ref": "ConnectionProperty"}, "description": "Connection properties.", "type": "array"}, "timePartitioning": {"$ref": "TimePartitioning", "description": "Time-based partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified."}, "flattenResults": {"description": "[Optional] If true and query uses legacy SQL dialect, flattens all nested and repeated fields in the query results. allowLargeResults must be true if this is set to false. For standard SQL queries, this flag is ignored and results are never flattened.", "type": "boolean", "default": "true"}, "createDisposition": {"description": "[Optional] Specifies whether the job is allowed to create new tables. The following values are supported: CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "schemaUpdateOptions": {"type": "array", "items": {"type": "string"}, "description": "Allows the schema of the destination table to be updated as a side effect of the query job. Schema update options are supported in two cases: when writeDisposition is WRITE_APPEND; when writeDisposition is WRITE_TRUNCATE and the destination table is a partition of a table, specified by partition decorators. For normal tables, WRITE_TRUNCATE will always overwrite the schema. One or more of the following values are specified: ALLOW_FIELD_ADDITION: allow adding a nullable field to the schema. ALLOW_FIELD_RELAXATION: allow relaxing a required field in the original schema to nullable."}, "maximumBytesBilled": {"type": "string", "format": "int64", "description": "[Optional] Limits the bytes billed for this job. Queries that will have bytes billed beyond this limit will fail (without incurring a charge). If unspecified, this will be set to your project default."}, "useLegacySql": {"default": "true", "type": "boolean", "description": "Specifies whether to use BigQuery's legacy SQL dialect for this query. The default value is true. If set to false, the query will use BigQuery's standard SQL: https://cloud.google.com/bigquery/sql-reference/ When useLegacySql is set to false, the value of flattenResults is ignored; query will be run as if flattenResults is false."}, "clustering": {"$ref": "Clustering", "description": "[Beta] Clustering specification for the destination table. Must be specified with time-based partitioning, data in the table will be first partitioned and subsequently clustered."}, "maximumBillingTier": {"type": "integer", "format": "int32", "description": "[Optional] Limits the billing tier for this job. Queries that have resource usage beyond this tier will fail (without incurring a charge). If unspecified, this will be set to your project default.", "default": "1"}, "priority": {"type": "string", "description": "[Optional] Specifies a priority for the query. Possible values include INTERACTIVE and BATCH. The default value is INTERACTIVE."}, "rangePartitioning": {"$ref": "RangePartitioning", "description": "[TrustedTester] Range partitioning specification for this table. Only one of timePartitioning and rangePartitioning should be specified."}, "userDefinedFunctionResources": {"items": {"$ref": "UserDefinedFunctionResource"}, "type": "array", "description": "Describes user-defined function resources used in the query."}}}, "JobStatistics3": {"properties": {"outputBytes": {"format": "int64", "type": "string", "description": "[Output-only] Size of the loaded data in bytes. Note that while a load job is in the running state, this value may change."}, "outputRows": {"format": "int64", "description": "[Output-only] Number of rows imported in a load job. Note that while an import job is in the running state, this value may change.", "type": "string"}, "badRecords": {"description": "[Output-only] The number of bad records encountered. Note that if the job has failed because of more bad records encountered than the maximum allowed in the load job configuration, then this number can be less than the total number of bad records present in the input data.", "format": "int64", "type": "string"}, "inputFiles": {"description": "[Output-only] Number of source files in a load job.", "format": "int64", "type": "string"}, "inputFileBytes": {"format": "int64", "description": "[Output-only] Number of bytes of source data in a load job.", "type": "string"}}, "id": "JobStatistics3", "type": "object"}, "RankingMetrics": {"type": "object", "properties": {"meanAveragePrecision": {"type": "number", "format": "double", "description": "Calculates a precision per user for all the items by ranking them and then averages all the precisions across all the users."}, "meanSquaredError": {"description": "Similar to the mean squared error computed in regression and explicit recommendation models except instead of computing the rating directly, the output from evaluate is computed against a preference which is 1 or 0 depending on if the rating exists or not.", "format": "double", "type": "number"}, "averageRank": {"description": "Determines the goodness of a ranking by computing the percentile rank from the predicted confidence and dividing it by the original rank.", "type": "number", "format": "double"}, "normalizedDiscountedCumulativeGain": {"type": "number", "format": "double", "description": "A metric to determine the goodness of a ranking calculated from the predicted confidence by comparing it to an ideal rank measured by the original ratings."}}, "id": "RankingMetrics", "description": "Evaluation metrics used by weighted-ALS models specified by feedback_type=implicit."}, "BigtableColumnFamily": {"properties": {"type": {"type": "string", "description": "[Optional] The type to convert the value in cells of this column family. The values are expected to be encoded using HBase Bytes.toBytes function when using the BINARY encoding value. Following BigQuery types are allowed (case-sensitive) - BYTES STRING INTEGER FLOAT BOOLEAN Default type is BYTES. This can be overridden for a specific column by listing that column in 'columns' and specifying a type for it."}, "columns": {"type": "array", "description": "[Optional] Lists of columns that should be exposed as individual fields as opposed to a list of (column name, value) pairs. All columns whose qualifier matches a qualifier in this list can be accessed as .. Other columns can be accessed as a list through .Column field.", "items": {"$ref": "BigtableColumn"}}, "familyId": {"type": "string", "description": "Identifier of the column family."}, "onlyReadLatest": {"type": "boolean", "description": "[Optional] If this is set only the latest version of value are exposed for all columns in this column family. This can be overridden for a specific column by listing that column in 'columns' and specifying a different setting for that column."}, "encoding": {"type": "string", "description": "[Optional] The encoding of the values when the type is not STRING. Acceptable encoding values are: TEXT - indicates values are alphanumeric text strings. BINARY - indicates values are encoded using HBase Bytes.toBytes family of functions. This can be overridden for a specific column by listing that column in 'columns' and specifying an encoding for it."}}, "type": "object", "id": "BigtableColumnFamily"}, "ExplainQueryStage": {"properties": {"readRatioMax": {"format": "double", "type": "number", "description": "Relative amount of time the slowest shard spent reading input."}, "id": {"format": "int64", "type": "string", "description": "Unique ID for stage within plan."}, "waitRatioMax": {"description": "Relative amount of time the slowest shard spent waiting to be scheduled.", "format": "double", "type": "number"}, "computeRatioMax": {"format": "double", "description": "Relative amount of time the slowest shard spent on CPU-bound tasks.", "type": "number"}, "recordsWritten": {"description": "Number of records written by the stage.", "format": "int64", "type": "string"}, "readRatioAvg": {"type": "number", "format": "double", "description": "Relative amount of time the average shard spent reading input."}, "waitMsMax": {"description": "Milliseconds the slowest shard spent waiting to be scheduled.", "type": "string", "format": "int64"}, "writeRatioMax": {"format": "double", "description": "Relative amount of time the slowest shard spent on writing output.", "type": "number"}, "startMs": {"format": "int64", "type": "string", "description": "Stage start time represented as milliseconds since epoch."}, "shuffleOutputBytesSpilled": {"type": "string", "format": "int64", "description": "Total number of bytes written to shuffle and spilled to disk."}, "shuffleOutputBytes": {"description": "Total number of bytes written to shuffle.", "type": "string", "format": "int64"}, "readMsAvg": {"type": "string", "format": "int64", "description": "Milliseconds the average shard spent reading input."}, "waitMsAvg": {"description": "Milliseconds the average shard spent waiting to be scheduled.", "type": "string", "format": "int64"}, "writeMsAvg": {"type": "string", "format": "int64", "description": "Milliseconds the average shard spent on writing output."}, "completedParallelInputs": {"description": "Number of parallel input segments completed.", "type": "string", "format": "int64"}, "status": {"type": "string", "description": "Current status for the stage."}, "writeMsMax": {"format": "int64", "description": "Milliseconds the slowest shard spent on writing output.", "type": "string"}, "writeRatioAvg": {"format": "double", "description": "Relative amount of time the average shard spent on writing output.", "type": "number"}, "inputStages": {"type": "array", "items": {"type": "string", "format": "int64"}, "description": "IDs for stages that are inputs to this stage."}, "waitRatioAvg": {"format": "double", "type": "number", "description": "Relative amount of time the average shard spent waiting to be scheduled."}, "name": {"type": "string", "description": "Human-readable name for stage."}, "computeMsAvg": {"description": "Milliseconds the average shard spent on CPU-bound tasks.", "type": "string", "format": "int64"}, "endMs": {"format": "int64", "description": "Stage end time represented as milliseconds since epoch.", "type": "string"}, "steps": {"type": "array", "description": "List of operations within the stage in dependency order (approximately chronological).", "items": {"$ref": "ExplainQueryStep"}}, "recordsRead": {"description": "Number of records read into the stage.", "type": "string", "format": "int64"}, "parallelInputs": {"description": "Number of parallel input segments to be processed.", "type": "string", "format": "int64"}, "slotMs": {"type": "string", "format": "int64", "description": "Slot-milliseconds used by the stage."}, "readMsMax": {"type": "string", "description": "Milliseconds the slowest shard spent reading input.", "format": "int64"}, "computeMsMax": {"description": "Milliseconds the slowest shard spent on CPU-bound tasks.", "type": "string", "format": "int64"}, "computeRatioAvg": {"description": "Relative amount of time the average shard spent on CPU-bound tasks.", "type": "number", "format": "double"}}, "id": "ExplainQueryStage", "type": "object"}, "Dataset": {"properties": {"datasetReference": {"$ref": "DatasetReference", "description": "[Required] A reference that identifies the dataset."}, "description": {"type": "string", "description": "[Optional] A user-friendly description of the dataset."}, "etag": {"description": "[Output-only] A hash of the resource.", "type": "string"}, "friendlyName": {"type": "string", "description": "[Optional] A descriptive name for the dataset."}, "access": {"items": {"properties": {"domain": {"description": "[Pick one] A domain to grant access to. Any users signed in with the domain specified will be granted the specified access. Example: \"example.com\". Maps to IAM policy member \"domain:DOMAIN\".", "type": "string"}, "routine": {"description": "[Pick one] A routine from a different dataset to grant access to. Queries executed against that routine will have read access to views/tables/routines in this dataset. Only UDF is supported for now. The role field is not required when this field is set. If that routine is updated by any user, access to the routine needs to be granted again via an update operation.", "$ref": "RoutineReference"}, "iamMember": {"type": "string", "description": "[Pick one] Some other type of member that appears in the IAM Policy but isn't a user, group, domain, or special group."}, "specialGroup": {"type": "string", "description": "[Pick one] A special group to grant access to. Possible values include: projectOwners: Owners of the enclosing project. projectReaders: Readers of the enclosing project. projectWriters: Writers of the enclosing project. allAuthenticatedUsers: All authenticated BigQuery users. Maps to similarly-named IAM members."}, "groupByEmail": {"type": "string", "description": "[Pick one] An email address of a Google Group to grant access to. Maps to IAM policy member \"group:GROUP\"."}, "role": {"description": "[Required] An IAM role ID that should be granted to the user, group, or domain specified in this access entry. The following legacy mappings will be applied: OWNER  roles/bigquery.dataOwner WRITER  roles/bigquery.dataEditor READER  roles/bigquery.dataViewer This field will accept any of the above formats, but will return only the legacy format. For example, if you set this field to \"roles/bigquery.dataOwner\", it will be returned back as \"OWNER\".", "type": "string"}, "view": {"$ref": "TableReference", "description": "[Pick one] A view from a different dataset to grant access to. Queries executed against that view will have read access to tables in this dataset. The role field is not required when this field is set. If that view is updated by any user, access to the view needs to be granted again via an update operation."}, "userByEmail": {"description": "[Pick one] An email address of a user to grant access to. For example: <EMAIL>. Maps to IAM policy member \"user:EMAIL\" or \"serviceAccount:EMAIL\".", "type": "string"}}, "type": "object"}, "description": "[Optional] An array of objects that define dataset access for one or more entities. You can set this property when inserting or updating a dataset in order to control who is allowed to access the data. If unspecified at dataset creation time, BigQuery adds default dataset access for the following entities: access.specialGroup: projectReaders; access.role: READER; access.specialGroup: projectWriters; access.role: WRITER; access.specialGroup: projectOwners; access.role: OWNER; access.userByEmail: [dataset creator email]; access.role: OWNER;", "type": "array"}, "defaultTableExpirationMs": {"type": "string", "description": "[Optional] The default lifetime of all tables in the dataset, in milliseconds. The minimum value is 3600000 milliseconds (one hour). Once this property is set, all newly-created tables in the dataset will have an expirationTime property set to the creation time plus the value in this property, and changing the value will only affect new tables, not existing ones. When the expirationTime for a given table is reached, that table will be deleted automatically. If a table's expirationTime is modified or removed before the table expires, or if you provide an explicit expirationTime when creating a table, that value takes precedence over the default expiration time indicated by this property.", "format": "int64"}, "kind": {"default": "bigquery#dataset", "type": "string", "description": "[Output-only] The resource type."}, "labels": {"type": "object", "description": "The labels associated with this dataset. You can use these to organize and group your datasets. You can set this property when inserting or updating a dataset. See Creating and Updating Dataset Labels for more information.", "additionalProperties": {"type": "string"}}, "satisfiesPZS": {"description": "[Output-only] Reserved for future use.", "type": "boolean"}, "selfLink": {"description": "[Output-only] A URL that can be used to access the resource again. You can use this URL in Get or Update requests to the resource.", "type": "string"}, "defaultPartitionExpirationMs": {"description": "[Optional] The default partition expiration for all partitioned tables in the dataset, in milliseconds. Once this property is set, all newly-created partitioned tables in the dataset will have an expirationMs property in the timePartitioning settings set to this value, and changing the value will only affect new tables, not existing ones. The storage in a partition will have an expiration time of its partition time plus this value. Setting this property overrides the use of defaultTableExpirationMs for partitioned tables: only one of defaultTableExpirationMs and defaultPartitionExpirationMs will be used for any new partitioned table. If you provide an explicit timePartitioning.expirationMs when creating or updating a partitioned table, that value takes precedence over the default partition expiration time indicated by this property.", "type": "string", "format": "int64"}, "lastModifiedTime": {"type": "string", "description": "[Output-only] The date when this dataset or any of its tables was last modified, in milliseconds since the epoch.", "format": "int64"}, "id": {"description": "[Output-only] The fully-qualified unique name of the dataset in the format projectId:datasetId. The dataset name without the project name is given in the datasetId field. When creating a new dataset, leave this field blank, and instead specify the datasetId field.", "type": "string"}, "location": {"description": "The geographic location where the dataset should reside. The default value is US. See details at https://cloud.google.com/bigquery/docs/locations.", "type": "string"}, "creationTime": {"type": "string", "description": "[Output-only] The time when this dataset was created, in milliseconds since the epoch.", "format": "int64"}, "defaultEncryptionConfiguration": {"$ref": "EncryptionConfiguration"}}, "id": "Dataset", "type": "object"}, "ArimaModelInfo": {"id": "ArimaModelInfo", "properties": {"timeSeriesId": {"type": "string", "description": "The id to indicate different time series."}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "type": "array", "items": {"enumDescriptions": ["", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "type": "string", "enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]}}, "hasDrift": {"type": "boolean", "description": "Whether Arima model fitted with drift or not. It is always false when d is not 1."}, "nonSeasonalOrder": {"$ref": "ArimaOrder", "description": "Non-seasonal order."}, "arimaFittingMetrics": {"description": "Arima fitting metrics.", "$ref": "ArimaFittingMetrics"}, "arimaCoefficients": {"description": "Arima coefficients.", "$ref": "ArimaCoefficients"}}, "description": "Arima model information.", "type": "object"}, "JobConfigurationTableCopy": {"id": "JobConfigurationTableCopy", "properties": {"destinationEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)."}, "destinationExpirationTime": {"description": "[Optional] The time when the destination table expires. Expired tables will be deleted and their storage reclaimed.", "type": "any"}, "operationType": {"description": "[Optional] Supported operation types in table copy job.", "type": "string"}, "sourceTables": {"description": "[Pick one] Source tables to copy.", "type": "array", "items": {"$ref": "TableReference"}}, "sourceTable": {"description": "[Pick one] Source table to copy.", "$ref": "TableReference"}, "writeDisposition": {"description": "[Optional] Specifies the action that occurs if the destination table already exists. The following values are supported: WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the table data. WRITE_APPEND: If the table already exists, <PERSON>Query appends the data to the table. WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_EMPTY. Each action is atomic and only occurs if <PERSON><PERSON><PERSON><PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "destinationTable": {"description": "[Required] The destination table", "$ref": "TableReference"}, "createDisposition": {"description": "[Optional] Specifies whether the job is allowed to create new tables. The following values are supported: CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}}, "type": "object"}, "ScriptStackFrame": {"properties": {"startLine": {"type": "integer", "format": "int32", "description": "[Output-only] One-based start line."}, "procedureId": {"description": "[Output-only] Name of the active procedure, empty if in a top-level script.", "type": "string"}, "endColumn": {"format": "int32", "description": "[Output-only] One-based end column.", "type": "integer"}, "text": {"type": "string", "description": "[Output-only] Text of the current statement/expression."}, "startColumn": {"format": "int32", "type": "integer", "description": "[Output-only] One-based start column."}, "endLine": {"type": "integer", "format": "int32", "description": "[Output-only] One-based end line."}}, "type": "object", "id": "ScriptStackFrame"}, "JobCancelResponse": {"type": "object", "id": "JobCancelResponse", "properties": {"job": {"$ref": "Job", "description": "The final state of the job."}, "kind": {"description": "The resource type of the response.", "default": "bigquery#jobCancelResponse", "type": "string"}}}, "TableSchema": {"id": "TableSchema", "type": "object", "properties": {"fields": {"type": "array", "description": "Describes the fields in a table.", "items": {"$ref": "TableFieldSchema"}}}}, "Streamingbuffer": {"properties": {"estimatedRows": {"format": "uint64", "type": "string", "description": "[Output-only] A lower-bound estimate of the number of rows currently in the streaming buffer."}, "estimatedBytes": {"format": "uint64", "type": "string", "description": "[Output-only] A lower-bound estimate of the number of bytes currently in the streaming buffer."}, "oldestEntryTime": {"description": "[Output-only] Contains the timestamp of the oldest entry in the streaming buffer, in milliseconds since the epoch, if the streaming buffer is available.", "format": "uint64", "type": "string"}}, "id": "Streamingbuffer", "type": "object"}, "RangePartitioning": {"properties": {"range": {"properties": {"end": {"format": "int64", "description": "[TrustedTester] [Required] The end of range partitioning, exclusive.", "type": "string"}, "interval": {"description": "[TrustedTester] [Required] The width of each interval.", "type": "string", "format": "int64"}, "start": {"description": "[TrustedTester] [Required] The start of range partitioning, inclusive.", "type": "string", "format": "int64"}}, "description": "[TrustedTester] [Required] Defines the ranges for range partitioning.", "type": "object"}, "field": {"type": "string", "description": "[TrustedTester] [Required] The table is partitioned by this field. The field must be a top-level NULLABLE/REQUIRED field. The only supported type is INTEGER/INT64."}}, "id": "RangePartitioning", "type": "object"}, "Job": {"properties": {"id": {"description": "[Output-only] Opaque ID field of the job", "type": "string"}, "kind": {"default": "bigquery#job", "description": "[Output-only] The type of the resource.", "type": "string"}, "selfLink": {"description": "[Output-only] A URL that can be used to access this resource again.", "type": "string"}, "etag": {"type": "string", "description": "[Output-only] A hash of this resource."}, "configuration": {"$ref": "JobConfiguration", "description": "[Required] Describes the job configuration."}, "user_email": {"type": "string", "description": "[Output-only] Email address of the user who ran the job."}, "status": {"description": "[Output-only] The status of this job. Examine this value when polling an asynchronous job to see if the job is complete.", "$ref": "JobStatus"}, "statistics": {"description": "[Output-only] Information about the job, including starting time and ending time of the job.", "$ref": "JobStatistics"}, "jobReference": {"$ref": "JobReference", "description": "[Optional] Reference describing the unique-per-user name of the job."}}, "type": "object", "id": "Job"}, "MaterializedViewDefinition": {"properties": {"query": {"type": "string", "description": "[Required] A query whose result is persisted."}, "lastRefreshTime": {"type": "string", "format": "int64", "description": "[Output-only] [TrustedTester] The time when this materialized view was last modified, in milliseconds since the epoch."}, "enableRefresh": {"type": "boolean", "description": "[Optional] [TrustedTester] Enable automatic refresh of the materialized view when the base table is updated. The default value is \"true\"."}, "refreshIntervalMs": {"format": "int64", "type": "string", "description": "[Optional] [TrustedTester] The maximum frequency at which this materialized view will be refreshed. The default value is \"1800000\" (30 minutes)."}}, "type": "object", "id": "MaterializedViewDefinition"}, "ProjectReference": {"properties": {"projectId": {"description": "[Required] ID of the project. Can be either the numeric ID or the assigned ID of the project.", "type": "string"}}, "id": "ProjectReference", "type": "object"}, "ConnectionProperty": {"type": "object", "id": "ConnectionProperty", "properties": {"value": {"description": "[Required] Value of the connection property.", "type": "string"}, "key": {"description": "[Required] Name of the connection property to set.", "type": "string"}}}, "GetIamPolicyRequest": {"id": "GetIamPolicyRequest", "type": "object", "description": "Request message for `GetIamPolicy` method.", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`."}}}, "RoutineReference": {"properties": {"routineId": {"description": "[Required] The ID of the routine. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 256 characters.", "type": "string"}, "datasetId": {"type": "string", "description": "[Required] The ID of the dataset containing this routine."}, "projectId": {"description": "[Required] The ID of the project containing this routine.", "type": "string"}}, "type": "object", "id": "RoutineReference"}, "EncryptionConfiguration": {"id": "EncryptionConfiguration", "type": "object", "properties": {"kmsKeyName": {"description": "[Optional] Describes the Cloud KMS encryption key that will be used to protect destination BigQuery table. The BigQuery Service Account associated with your project requires access to this encryption key.", "type": "string"}}}, "DatasetList": {"type": "object", "id": "DatasetList", "properties": {"kind": {"description": "The list type. This property always returns the value \"bigquery#datasetList\".", "default": "bigquery#datasetList", "type": "string"}, "etag": {"description": "A hash value of the results page. You can use this property to determine if the page has changed since the last request.", "type": "string"}, "datasets": {"description": "An array of the dataset resources in the project. Each resource contains basic information. For full information about a particular dataset resource, use the Datasets: get method. This property is omitted when there are no datasets in the project.", "items": {"properties": {"datasetReference": {"$ref": "DatasetReference", "description": "The dataset reference. Use this property to access specific parts of the dataset's ID, such as project ID or dataset ID."}, "id": {"description": "The fully-qualified, unique, opaque ID of the dataset.", "type": "string"}, "labels": {"description": "The labels associated with this dataset. You can use these to organize and group your datasets.", "type": "object", "additionalProperties": {"type": "string"}}, "location": {"type": "string", "description": "The geographic location where the data resides."}, "kind": {"type": "string", "description": "The resource type. This property always returns the value \"bigquery#dataset\".", "default": "bigquery#dataset"}, "friendlyName": {"type": "string", "description": "A descriptive name for the dataset, if one exists."}}, "type": "object"}, "type": "array"}, "nextPageToken": {"description": "A token that can be used to request the next results page. This property is omitted on the final results page.", "type": "string"}}}, "QueryParameterValue": {"properties": {"arrayValues": {"description": "[Optional] The array values, if this is an array type.", "items": {"$ref": "QueryParameterValue"}, "type": "array"}, "value": {"type": "string", "description": "[Optional] The value of this value, if a simple scalar type."}, "structValues": {"additionalProperties": {"$ref": "QueryParameterValue"}, "description": "[Optional] The struct field values, in order of the struct type's declaration.", "type": "object"}}, "type": "object", "id": "QueryParameterValue"}, "QueryRequest": {"type": "object", "id": "QueryRequest", "properties": {"preserveNulls": {"description": "[Deprecated] This property is deprecated.", "type": "boolean"}, "parameterMode": {"type": "string", "description": "Standard SQL only. Set to POSITIONAL to use positional (?) query parameters or to NAMED to use named (@myparam) query parameters in this query."}, "labels": {"type": "object", "description": "The labels associated with this job. You can use these to organize and group your jobs. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "additionalProperties": {"type": "string"}}, "defaultDataset": {"description": "[Optional] Specifies the default datasetId and projectId to assume for any unqualified table names in the query. If not set, all table names in the query string must be qualified in the format 'datasetId.tableId'.", "$ref": "DatasetReference"}, "query": {"type": "string", "description": "[Required] A query string, following the BigQuery query syntax, of the query to execute. Example: \"SELECT count(f1) FROM [myProjectId:myDatasetId.myTableId]\".", "annotations": {"required": ["bigquery.jobs.query"]}}, "maxResults": {"description": "[Optional] The maximum number of rows of data to return per page of results. Setting this flag to a small value such as 1000 and then paging through results might improve reliability when the query result set is large. In addition to this limit, responses are also limited to 10 MB. By default, there is no maximum row count, and only the byte limit applies.", "format": "uint32", "type": "integer"}, "maximumBytesBilled": {"type": "string", "description": "[Optional] Limits the bytes billed for this job. Queries that will have bytes billed beyond this limit will fail (without incurring a charge). If unspecified, this will be set to your project default.", "format": "int64"}, "kind": {"default": "bigquery#queryRequest", "type": "string", "description": "The resource type of the request."}, "location": {"type": "string", "description": "The geographic location where the job should run. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location."}, "timeoutMs": {"type": "integer", "description": "[Optional] How long to wait for the query to complete, in milliseconds, before the request times out and returns. Note that this is only a timeout for the request, not the query. If the query takes longer to run than the timeout value, the call returns without any results and with the 'jobComplete' flag set to false. You can call GetQueryResults() to wait for the query to complete and read the results. The default value is 10000 milliseconds (10 seconds).", "format": "uint32"}, "queryParameters": {"items": {"$ref": "QueryParameter"}, "type": "array", "description": "Query parameters for Standard SQL queries."}, "dryRun": {"type": "boolean", "description": "[Optional] If set to true, BigQuery doesn't run the job. Instead, if the query is valid, BigQuery returns statistics about the job such as how many bytes would be processed. If the query is invalid, an error returns. The default value is false."}, "useQueryCache": {"description": "[Optional] Whether to look for the result in the query cache. The query cache is a best-effort cache that will be flushed whenever tables in the query are modified. The default value is true.", "type": "boolean", "default": "true"}, "connectionProperties": {"type": "array", "description": "Connection properties.", "items": {"$ref": "ConnectionProperty"}}, "requestId": {"type": "string", "description": "A unique user provided identifier to ensure idempotent behavior for queries. Note that this is different from the job_id. It has the following properties: 1. It is case-sensitive, limited to up to 36 ASCII characters. A UUID is recommended. 2. Read only queries can ignore this token since they are nullipotent by definition. 3. For the purposes of idempotency ensured by the request_id, a request is considered duplicate of another only if they have the same request_id and are actually duplicates. When determining whether a request is a duplicate of the previous request, all parameters in the request that may affect the behavior are considered. For example, query, connection_properties, query_parameters, use_legacy_sql are parameters that affect the result and are considered when determining whether a request is a duplicate, but properties like timeout_ms don't affect the result and are thus not considered. Dry run query requests are never considered duplicate of another request. 4. When a duplicate mutating query request is detected, it returns: a. the results of the mutation if it completes successfully within the timeout. b. the running operation if it is still in progress at the end of the timeout. 5. Its lifetime is limited to 15 minutes. In other words, if two requests are sent with the same request_id, but more than 15 minutes apart, idempotency is not guaranteed."}, "useLegacySql": {"default": "true", "type": "boolean", "description": "Specifies whether to use BigQuery's legacy SQL dialect for this query. The default value is true. If set to false, the query will use BigQuery's standard SQL: https://cloud.google.com/bigquery/sql-reference/ When useLegacySql is set to false, the value of flattenResults is ignored; query will be run as if flattenResults is false."}}}, "IterationResult": {"id": "IterationResult", "description": "Information about a single iteration of the training run.", "type": "object", "properties": {"principalComponentInfos": {"description": "The information of the principal components.", "items": {"$ref": "PrincipalComponentInfo"}, "type": "array"}, "durationMs": {"description": "Time taken to run the iteration in milliseconds.", "format": "int64", "type": "string"}, "arimaResult": {"$ref": "ArimaResult"}, "evalLoss": {"format": "double", "type": "number", "description": "Loss computed on the eval data at the end of iteration."}, "trainingLoss": {"type": "number", "format": "double", "description": "Loss computed on the training data at the end of iteration."}, "index": {"description": "Index of the iteration, 0 based.", "format": "int32", "type": "integer"}, "learnRate": {"description": "Learn rate used for this iteration.", "format": "double", "type": "number"}, "clusterInfos": {"description": "Information about top clusters for clustering models.", "type": "array", "items": {"$ref": "ClusterInfo"}}}}, "Clustering": {"id": "Clustering", "properties": {"fields": {"description": "[Repeated] One or more fields on which data should be clustered. Only top-level, non-repeated, simple-type fields are supported. When you cluster a table using multiple columns, the order of columns you specify is important. The order of the specified columns determines the sort order of the data.", "type": "array", "items": {"type": "string"}}}, "type": "object"}, "StandardSqlField": {"id": "StandardSqlField", "type": "object", "description": "A field or a column.", "properties": {"name": {"type": "string", "description": "Optional. The name of this field. Can be absent for struct fields."}, "type": {"description": "Optional. The type of this parameter. Absent if not explicitly specified (e.g., CREATE FUNCTION statement can omit the return type; in this case the output parameter does not have this \"type\" field).", "$ref": "StandardSqlDataType"}}}, "TableDataInsertAllRequest": {"id": "TableDataInsertAllRequest", "type": "object", "properties": {"skipInvalidRows": {"description": "[Optional] Insert all valid rows of a request, even if invalid rows exist. The default value is false, which causes the entire request to fail if any invalid rows exist.", "type": "boolean"}, "ignoreUnknownValues": {"description": "[Optional] Accept rows that contain values that do not match the schema. The unknown values are ignored. Default is false, which treats unknown values as errors.", "type": "boolean"}, "kind": {"default": "bigquery#tableDataInsertAllRequest", "description": "The resource type of the response.", "type": "string"}, "templateSuffix": {"description": "If specified, treats the destination table as a base template, and inserts the rows into an instance table named \"{destination}{templateSuffix}\". BigQuery will manage creation of the instance table, using the schema of the base template table. See https://cloud.google.com/bigquery/streaming-data-into-bigquery#template-tables for considerations when working with templates tables.", "type": "string"}, "rows": {"items": {"type": "object", "properties": {"json": {"description": "[Required] A JSON object that contains a row of data. The object's properties and values must match the destination table's schema.", "$ref": "JsonObject"}, "insertId": {"type": "string", "description": "[Optional] A unique ID for each row. BigQuery uses this property to detect duplicate insertion requests on a best-effort basis."}}}, "description": "The rows to insert.", "type": "array"}}}, "Cluster": {"properties": {"count": {"type": "string", "description": "Count of training data rows that were assigned to this cluster.", "format": "int64"}, "featureValues": {"type": "array", "items": {"$ref": "FeatureValue"}, "description": "Values of highly variant features for this cluster."}, "centroidId": {"format": "int64", "description": "Centroid id.", "type": "string"}}, "id": "Cluster", "type": "object", "description": "Message containing the information about one cluster."}, "ArimaSingleModelForecastingMetrics": {"properties": {"seasonalPeriods": {"type": "array", "description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "items": {"enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"], "enumDescriptions": ["", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "type": "string"}}, "arimaFittingMetrics": {"description": "Arima fitting metrics.", "$ref": "ArimaFittingMetrics"}, "nonSeasonalOrder": {"$ref": "ArimaOrder", "description": "Non-seasonal order."}, "hasDrift": {"description": "Is arima model fitted with drift or not. It is always false when d is not 1.", "type": "boolean"}, "timeSeriesId": {"type": "string", "description": "The id to indicate different time series."}}, "description": "Model evaluation metrics for a single ARIMA forecasting model.", "type": "object", "id": "ArimaSingleModelForecastingMetrics"}, "RowAccessPolicyReference": {"id": "RowAccessPolicyReference", "properties": {"policyId": {"type": "string", "description": "[Required] The ID of the row access policy. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 256 characters."}, "projectId": {"description": "[Required] The ID of the project containing this row access policy.", "type": "string"}, "datasetId": {"type": "string", "description": "[Required] The ID of the dataset containing this row access policy."}, "tableId": {"description": "[Required] The ID of the table containing this row access policy.", "type": "string"}}, "type": "object"}, "BigtableColumn": {"type": "object", "properties": {"onlyReadLatest": {"type": "boolean", "description": "[Optional] If this is set, only the latest version of value in this column are exposed. 'onlyReadLatest' can also be set at the column family level. However, the setting at this level takes precedence if 'onlyReadLatest' is set at both levels."}, "encoding": {"type": "string", "description": "[Optional] The encoding of the values when the type is not STRING. Acceptable encoding values are: TEXT - indicates values are alphanumeric text strings. BINARY - indicates values are encoded using HBase Bytes.toBytes family of functions. 'encoding' can also be set at the column family level. However, the setting at this level takes precedence if 'encoding' is set at both levels."}, "fieldName": {"description": "[Optional] If the qualifier is not a valid BigQuery field identifier i.e. does not match [a-zA-Z][a-zA-Z0-9_]*, a valid identifier must be provided as the column field name and is used as field name in queries.", "type": "string"}, "type": {"type": "string", "description": "[Optional] The type to convert the value in cells of this column. The values are expected to be encoded using HBase Bytes.toBytes function when using the BINARY encoding value. Following BigQuery types are allowed (case-sensitive) - BYTES STRING INTEGER FLOAT BOOLEAN Default type is BYTES. 'type' can also be set at the column family level. However, the setting at this level takes precedence if 'type' is set at both levels."}, "qualifierEncoded": {"description": "[Required] Qualifier of the column. Columns in the parent column family that has this exact qualifier are exposed as . field. If the qualifier is valid UTF-8 string, it can be specified in the qualifier_string field. Otherwise, a base-64 encoded value must be set to qualifier_encoded. The column field name is the same as the column qualifier. However, if the qualifier is not a valid BigQuery field identifier i.e. does not match [a-zA-Z][a-zA-Z0-9_]*, a valid identifier must be provided as field_name.", "type": "string", "format": "byte"}, "qualifierString": {"type": "string"}}, "id": "BigtableColumn"}, "SnapshotDefinition": {"type": "object", "id": "SnapshotDefinition", "properties": {"snapshotTime": {"format": "date-time", "description": "[Required] The time at which the base table was snapshot.", "type": "string"}, "baseTableReference": {"description": "[Required] Reference describing the ID of the table that is snapshotted.", "$ref": "TableReference"}}}, "AuditConfig": {"type": "object", "id": "AuditConfig", "properties": {"auditLogConfigs": {"type": "array", "description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It <NAME_EMAIL> from DATA_READ logging, and <EMAIL> from DATA_WRITE logging."}, "ConfusionMatrix": {"id": "ConfusionMatrix", "properties": {"confidenceThreshold": {"type": "number", "format": "double", "description": "Confidence threshold used when computing the entries of the confusion matrix."}, "rows": {"items": {"$ref": "Row"}, "type": "array", "description": "One row per actual label."}}, "description": "Confusion matrix for multi-class classification models.", "type": "object"}, "JobStatus": {"properties": {"state": {"description": "[Output-only] Running state of the job.", "type": "string"}, "errorResult": {"description": "[Output-only] Final error result of the job. If present, indicates that the job has completed and was unsuccessful.", "$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "errors": {"type": "array", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "description": "[Output-only] The first errors encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has completed or was unsuccessful."}}, "id": "JobStatus", "type": "object"}, "ErrorProto": {"properties": {"reason": {"description": "A short error code that summarizes the error.", "type": "string"}, "message": {"type": "string", "description": "A human-readable description of the error."}, "debugInfo": {"type": "string", "description": "Debugging information. This property is internal to Google and should not be used."}, "location": {"type": "string", "description": "Specifies where the error occurred, if present."}}, "id": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "type": "object"}, "BqmlIterationResult": {"type": "object", "properties": {"evalLoss": {"type": "number", "format": "double", "description": "[Output-only, Beta] Eval loss computed on the eval data at the end of the iteration. The eval loss is used for early stopping to avoid overfitting. No eval loss if eval_split_method option is specified as no_split or auto_split with input data size less than 500 rows."}, "learnRate": {"description": "[Output-only, Beta] Learning rate used for this iteration, it varies for different training iterations if learn_rate_strategy option is not constant.", "format": "double", "type": "number"}, "trainingLoss": {"description": "[Output-only, Beta] Training loss computed on the training data at the end of the iteration. The training loss function is defined by model type.", "type": "number", "format": "double"}, "index": {"type": "integer", "description": "[Output-only, Beta] Index of the ML training iteration, starting from zero for each training run.", "format": "int32"}, "durationMs": {"type": "string", "format": "int64", "description": "[Output-only, Beta] Time taken to run the training iteration in milliseconds."}}, "id": "BqmlIterationResult"}, "MultiClassClassificationMetrics": {"properties": {"confusionMatrixList": {"description": "Confusion matrix at different thresholds.", "items": {"$ref": "ConfusionMatrix"}, "type": "array"}, "aggregateClassificationMetrics": {"$ref": "AggregateClassificationMetrics", "description": "Aggregate classification metrics."}}, "description": "Evaluation metrics for multi-class classification/classifier models.", "type": "object", "id": "MultiClassClassificationMetrics"}, "Row": {"description": "A single row in the confusion matrix.", "properties": {"actualLabel": {"description": "The original label of this row.", "type": "string"}, "entries": {"description": "Info describing predicted label distribution.", "items": {"$ref": "Entry"}, "type": "array"}}, "type": "object", "id": "Row"}, "AuditLogConfig": {"type": "object", "id": "AuditLogConfig", "properties": {"logType": {"enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "type": "string", "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "description": "The log type that this config enables."}, "exemptedMembers": {"items": {"type": "string"}, "description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "type": "array"}}, "description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging."}, "FeatureValue": {"properties": {"categoricalValue": {"description": "The categorical feature value.", "$ref": "CategoricalValue"}, "numericalValue": {"description": "The numerical feature value. This is the centroid value for this feature.", "type": "number", "format": "double"}, "featureColumn": {"description": "The feature column name.", "type": "string"}}, "description": "Representative value of a single feature within the cluster.", "id": "FeatureValue", "type": "object"}, "AggregateClassificationMetrics": {"description": "Aggregate metrics for classification/classifier models. For multi-class models, the metrics are either macro-averaged or micro-averaged. When macro-averaged, the metrics are calculated for each label and then an unweighted average is taken of those values. When micro-averaged, the metric is calculated globally by counting the total number of correctly predicted rows.", "type": "object", "id": "AggregateClassificationMetrics", "properties": {"accuracy": {"type": "number", "description": "Accuracy is the fraction of predictions given the correct label. For multiclass this is a micro-averaged metric.", "format": "double"}, "f1Score": {"type": "number", "description": "The F1 score is an average of recall and precision. For multiclass this is a macro-averaged metric.", "format": "double"}, "threshold": {"format": "double", "type": "number", "description": "Threshold at which the metrics are computed. For binary classification models this is the positive class threshold. For multi-class classfication models this is the confidence threshold."}, "precision": {"description": "Precision is the fraction of actual positive predictions that had positive actual labels. For multiclass this is a macro-averaged metric treating each class as a binary classifier.", "format": "double", "type": "number"}, "recall": {"format": "double", "type": "number", "description": "Recall is the fraction of actual positive labels that were given a positive prediction. For multiclass this is a macro-averaged metric."}, "rocAuc": {"format": "double", "type": "number", "description": "Area Under a ROC Curve. For multiclass this is a macro-averaged metric."}, "logLoss": {"description": "Logarithmic Loss. For multiclass this is a macro-averaged metric.", "type": "number", "format": "double"}}}, "JobStatistics2": {"type": "object", "properties": {"reservationUsage": {"items": {"type": "object", "properties": {"name": {"type": "string", "description": "[Output-only] Reservation name or \"unreserved\" for on-demand resources usage."}, "slotMs": {"format": "int64", "type": "string", "description": "[Output-only] Slot-milliseconds the job spent in the given reservation."}}}, "type": "array", "description": "[Output-only] Job resource usage breakdown by reservation."}, "queryPlan": {"description": "[Output-only] Describes execution plan for the query.", "type": "array", "items": {"$ref": "ExplainQueryStage"}}, "totalBytesProcessedAccuracy": {"type": "string", "description": "[Output-only] For dry-run jobs, totalBytesProcessed is an estimate and this field specifies the accuracy of the estimate. Possible values can be: UNKNOWN: accuracy of the estimate is unknown. PRECISE: estimate is precise. LOWER_BOUND: estimate is lower bound of what the query would cost. UPPER_BOUND: estimate is upper bound of what the query would cost."}, "ddlTargetTable": {"description": "[Output-only] The DDL target table. Present only for CREATE/DROP TABLE/VIEW and DROP ALL ROW ACCESS POLICIES queries.", "$ref": "TableReference"}, "timeline": {"items": {"$ref": "QueryTimelineSample"}, "description": "[Output-only] [Beta] Describes a timeline of job execution.", "type": "array"}, "statementType": {"description": "The type of query statement, if valid. Possible values (new values might be added in the future): \"SELECT\": SELECT query. \"INSERT\": INSERT query; see https://cloud.google.com/bigquery/docs/reference/standard-sql/data-manipulation-language. \"UPDATE\": UPDATE query; see https://cloud.google.com/bigquery/docs/reference/standard-sql/data-manipulation-language. \"DELETE\": DELETE query; see https://cloud.google.com/bigquery/docs/reference/standard-sql/data-manipulation-language. \"MERGE\": MERGE query; see https://cloud.google.com/bigquery/docs/reference/standard-sql/data-manipulation-language. \"ALTER_TABLE\": ALTER TABLE query. \"ALTER_VIEW\": ALTER VIEW query. \"ASSERT\": ASSERT condition AS 'description'. \"CREATE_FUNCTION\": CREATE FUNCTION query. \"CREATE_MODEL\": CREATE [OR REPLACE] MODEL ... AS SELECT ... . \"CREATE_PROCEDURE\": CREATE PROCEDURE query. \"CREATE_TABLE\": CREATE [OR REPLACE] TABLE without AS SELECT. \"CREATE_TABLE_AS_SELECT\": CREATE [OR REPLACE] TABLE ... AS SELECT ... . \"CREATE_VIEW\": CREATE [OR REPLACE] VIEW ... AS SELECT ... . \"DROP_FUNCTION\" : DROP FUNCTION query. \"DROP_PROCEDURE\": DROP PROCEDURE query. \"DROP_TABLE\": DROP TABLE query. \"DROP_VIEW\": DROP VIEW query.", "type": "string"}, "ddlOperationPerformed": {"description": "The DDL operation performed, possibly dependent on the pre-existence of the DDL target. Possible values (new values might be added in the future): \"CREATE\": The query created the DDL target. \"SKIP\": No-op. Example cases: the query is CREATE TABLE IF NOT EXISTS while the table already exists, or the query is DROP TABLE IF EXISTS while the table does not exist. \"REPLACE\": The query replaced the DDL target. Example case: the query is CREATE OR REPLACE TABLE, and the table already exists. \"DROP\": The query deleted the DDL target.", "type": "string"}, "undeclaredQueryParameters": {"items": {"$ref": "QueryParameter"}, "description": "Standard SQL only: list of undeclared query parameters detected during a dry run validation.", "type": "array"}, "schema": {"description": "[Output-only] The schema of the results. Present only for successful dry run of non-legacy SQL queries.", "$ref": "TableSchema"}, "modelTrainingCurrentIteration": {"type": "integer", "format": "int32", "description": "[Output-only, Beta] Deprecated; do not use."}, "estimatedBytesProcessed": {"type": "string", "description": "[Output-only] The original estimate of bytes processed for the job.", "format": "int64"}, "totalBytesBilled": {"description": "[Output-only] Total bytes billed for the job.", "type": "string", "format": "int64"}, "referencedTables": {"items": {"$ref": "TableReference"}, "type": "array", "description": "[Output-only] Referenced tables for the job. Queries that reference more than 50 tables will not have a complete list."}, "numDmlAffectedRows": {"description": "[Output-only] The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "type": "string", "format": "int64"}, "modelTrainingExpectedTotalIteration": {"format": "int64", "type": "string", "description": "[Output-only, Beta] Deprecated; do not use."}, "totalBytesProcessed": {"type": "string", "format": "int64", "description": "[Output-only] Total bytes processed for the job."}, "cacheHit": {"description": "[Output-only] Whether the query result was fetched from the query cache.", "type": "boolean"}, "ddlTargetRowAccessPolicy": {"$ref": "RowAccessPolicyReference", "description": "[Output-only] [Preview] The DDL target row access policy. Present only for CREATE/DROP ROW ACCESS POLICY queries."}, "totalSlotMs": {"description": "[Output-only] Slot-milliseconds for the job.", "type": "string", "format": "int64"}, "ddlTargetRoutine": {"$ref": "RoutineReference", "description": "The DDL target routine. Present only for CREATE/DROP FUNCTION/PROCEDURE queries."}, "modelTraining": {"$ref": "BigQueryModelTraining", "description": "[Output-only, Beta] Information about create model query job progress."}, "billingTier": {"format": "int32", "type": "integer", "description": "[Output-only] Billing tier for the job."}, "ddlAffectedRowAccessPolicyCount": {"type": "string", "description": "[Output-only] [Preview] The number of row access policies affected by a DDL statement. Present only for DROP ALL ROW ACCESS POLICIES queries.", "format": "int64"}, "referencedRoutines": {"type": "array", "description": "[Output-only] Referenced routines (persistent user-defined functions and stored procedures) for the job.", "items": {"$ref": "RoutineReference"}}, "totalPartitionsProcessed": {"type": "string", "description": "[Output-only] Total number of partitions processed from all partitioned tables referenced in the job.", "format": "int64"}}, "id": "JobStatistics2"}, "QueryResponse": {"type": "object", "properties": {"cacheHit": {"description": "Whether the query result was fetched from the query cache.", "type": "boolean"}, "totalRows": {"format": "uint64", "description": "The total number of rows in the complete query result set, which can be more than the number of rows in this single page of results.", "type": "string"}, "jobComplete": {"description": "Whether the query has completed or not. If rows or totalRows are present, this will always be true. If this is false, totalRows will not be available.", "type": "boolean"}, "kind": {"type": "string", "default": "bigquery#queryResponse", "description": "The resource type."}, "schema": {"$ref": "TableSchema", "description": "The schema of the results. Present only when the query completes successfully."}, "pageToken": {"type": "string", "description": "A token used for paging results."}, "totalBytesProcessed": {"format": "int64", "type": "string", "description": "The total number of bytes processed for this query. If this query was a dry run, this is the number of bytes that would be processed if the query were run."}, "rows": {"type": "array", "items": {"$ref": "TableRow"}, "description": "An object with as many results as can be contained within the maximum permitted reply size. To get any additional rows, you can call GetQueryResults and specify the jobReference returned above."}, "numDmlAffectedRows": {"type": "string", "description": "[Output-only] The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "format": "int64"}, "jobReference": {"description": "Reference to the Job that was created to run the query. This field will be present even if the original request timed out, in which case GetQueryResults can be used to read the results once the query has completed. Since this API only returns the first page of results, subsequent pages can be fetched via the same mechanism (GetQueryResults).", "$ref": "JobReference"}, "errors": {"items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "type": "array", "description": "[Output-only] The first errors or warnings encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has completed or was unsuccessful."}}, "id": "QueryResponse"}, "UserDefinedFunctionResource": {"properties": {"inlineCode": {"description": "[Pick one] An inline resource that contains code for a user-defined function (UDF). Providing a inline code resource is equivalent to providing a URI for a file containing the same code.", "type": "string"}, "resourceUri": {"type": "string", "description": "[Pick one] A code resource to load from a Google Cloud Storage URI (gs://bucket/path)."}}, "id": "UserDefinedFunctionResource", "description": "This is used for defining User Defined Function (UDF) resources only when using legacy SQL. Users of Standard SQL should leverage either DDL (e.g. CREATE [TEMPORARY] FUNCTION ... ) or the Routines API to define UDF resources. For additional information on migrating, see: https://cloud.google.com/bigquery/docs/reference/standard-sql/migrating-from-legacy-sql#differences_in_user-defined_javascript_functions", "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "properties": {"permissions": {"items": {"type": "string"}, "description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as '*' or 'storage.*') are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "type": "array"}}, "type": "object", "id": "TestIamPermissionsRequest"}, "BigtableOptions": {"id": "BigtableOptions", "properties": {"columnFamilies": {"description": "[Optional] List of column families to expose in the table schema along with their types. This list restricts the column families that can be referenced in queries and specifies their value types. You can use this list to do type conversions - see the 'type' field for more details. If you leave this list empty, all column families are present in the table schema and their values are read as BYTES. During a query only the column families referenced in that query are read from Bigtable.", "type": "array", "items": {"$ref": "BigtableColumnFamily"}}, "readRowkeyAsString": {"type": "boolean", "description": "[Optional] If field is true, then the rowkey column families will be read and converted to string. Otherwise they are read with BYTES type values and users need to manually cast them with CAST if necessary. The default value is false."}, "ignoreUnspecifiedColumnFamilies": {"description": "[Optional] If field is true, then the column families that are not specified in columnFamilies list are not exposed in the table schema. Otherwise, they are read with BYTES type values. The default value is false.", "type": "boolean"}}, "type": "object"}, "CategoricalValue": {"description": "Representative value of a categorical feature.", "properties": {"categoryCounts": {"description": "Counts of all categories for the categorical feature. If there are more than ten categories, we return top ten (by count) and return one more CategoryCount with category \"_OTHER_\" and count as aggregate counts of remaining categories.", "type": "array", "items": {"$ref": "CategoryCount"}}}, "type": "object", "id": "CategoricalValue"}, "QueryParameter": {"id": "QueryParameter", "type": "object", "properties": {"name": {"type": "string", "description": "[Optional] If unset, this is a positional parameter. Otherwise, should be unique within a query."}, "parameterType": {"$ref": "QueryParameterType", "description": "[Required] The type of this parameter."}, "parameterValue": {"description": "[Required] The value of this parameter.", "$ref": "QueryParameterValue"}}}, "HivePartitioningOptions": {"id": "HivePartitioningOptions", "properties": {"requirePartitionFilter": {"type": "boolean", "description": "[Optional] If set to true, queries over this table require a partition filter that can be used for partition elimination to be specified. Note that this field should only be true when creating a permanent external table or querying a temporary external table. Hive-partitioned loads with requirePartitionFilter explicitly set to true will fail."}, "sourceUriPrefix": {"description": "[Optional] When hive partition detection is requested, a common prefix for all source uris should be supplied. The prefix must end immediately before the partition key encoding begins. For example, consider files following this data layout. gs://bucket/path_to_table/dt=2019-01-01/country=BR/id=7/file.avro gs://bucket/path_to_table/dt=2018-12-31/country=CA/id=3/file.avro When hive partitioning is requested with either AUTO or STRINGS detection, the common prefix can be either of gs://bucket/path_to_table or gs://bucket/path_to_table/ (trailing slash does not matter).", "type": "string"}, "mode": {"description": "[Optional] When set, what mode of hive partitioning to use when reading data. The following modes are supported. (1) AUTO: automatically infer partition key name(s) and type(s). (2) STRINGS: automatically infer partition key name(s). All types are interpreted as strings. (3) CUSTOM: partition key schema is encoded in the source URI prefix. Not all storage formats support hive partitioning. Requesting hive partitioning on an unsupported format will lead to an error. Currently supported types include: AVRO, CSV, JSON, ORC and Parquet.", "type": "string"}}, "type": "object"}, "TableCell": {"properties": {"v": {"type": "any"}}, "id": "TableCell", "type": "object"}, "ModelDefinition": {"properties": {"trainingRuns": {"type": "array", "items": {"$ref": "BqmlTrainingRun"}, "description": "[Output-only, Beta] Information about ml training runs, each training run comprises of multiple iterations and there may be multiple training runs for the model if warm start is used or if a user decides to continue a previously cancelled query."}, "modelOptions": {"type": "object", "description": "[Output-only, Beta] Model options used for the first training run. These options are immutable for subsequent training runs. Default values are used for any options not specified in the input query.", "properties": {"lossType": {"type": "string"}, "modelType": {"type": "string"}, "labels": {"items": {"type": "string"}, "type": "array"}}}}, "type": "object", "id": "ModelDefinition"}, "TransactionInfo": {"type": "object", "id": "TransactionInfo", "properties": {"transactionId": {"description": "[Output-only] // [Alpha] Id of the transaction.", "type": "string"}}}, "LocationMetadata": {"id": "LocationMetadata", "type": "object", "description": "BigQuery-specific metadata about a location. This will be set on google.cloud.location.Location.metadata in Cloud Location API responses.", "properties": {"legacyLocationId": {"type": "string", "description": "The legacy BigQuery location ID, e.g. “EU” for the “europe” location. This is for any API consumers that need the legacy “US” and “EU” locations."}}}, "Model": {"type": "object", "properties": {"lastModifiedTime": {"type": "string", "format": "int64", "description": "Output only. The time when this model was last modified, in millisecs since the epoch.", "readOnly": true}, "location": {"type": "string", "readOnly": true, "description": "Output only. The geographic location where the model resides. This value is inherited from the dataset."}, "creationTime": {"description": "Output only. The time when this model was created, in millisecs since the epoch.", "type": "string", "format": "int64", "readOnly": true}, "modelType": {"description": "Output only. Type of the model resource.", "enumDescriptions": ["", "Linear regression model.", "Logistic regression based classification model.", "K-means clustering model.", "Matrix factorization model.", "DNN classifier model.", "An imported TensorFlow model.", "DNN regressor model.", "Boosted tree regressor model.", "Boosted tree classifier model.", "ARIMA model.", "[Beta] AutoML Tables regression model.", "[Beta] AutoML Tables classification model."], "enum": ["MODEL_TYPE_UNSPECIFIED", "LINEAR_REGRESSION", "LOGISTIC_REGRESSION", "KMEANS", "MATRIX_FACTORIZATION", "DNN_CLASSIFIER", "TENSORFLOW", "DNN_REGRESSOR", "BOOSTED_TREE_REGRESSOR", "BOOSTED_TREE_CLASSIFIER", "ARIMA", "AUTOML_REGRESSOR", "AUTOML_CLASSIFIER"], "readOnly": true, "type": "string"}, "modelReference": {"description": "Required. Unique identifier for this model.", "$ref": "ModelReference"}, "etag": {"type": "string", "description": "Output only. A hash of this resource.", "readOnly": true}, "description": {"description": "Optional. A user-friendly description of this model.", "type": "string"}, "encryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys). This shows the encryption configuration of the model data while stored in BigQuery storage. This field can be used with PatchModel to update encryption key for an already encrypted model."}, "trainingRuns": {"items": {"$ref": "TrainingRun"}, "type": "array", "readOnly": true, "description": "Output only. Information for all training runs in increasing order of start_time."}, "friendlyName": {"type": "string", "description": "Optional. A descriptive name for this model."}, "featureColumns": {"type": "array", "description": "Output only. Input feature columns that were used to train this model.", "items": {"$ref": "StandardSqlField"}, "readOnly": true}, "labels": {"additionalProperties": {"type": "string"}, "type": "object", "description": "The labels associated with this model. You can use these to organize and group your models. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key."}, "expirationTime": {"description": "Optional. The time when this model expires, in milliseconds since the epoch. If not present, the model will persist indefinitely. Expired models will be deleted and their storage reclaimed. The defaultTableExpirationMs property of the encapsulating dataset can be used to set a default expirationTime on newly created models.", "format": "int64", "type": "string"}, "labelColumns": {"readOnly": true, "items": {"$ref": "StandardSqlField"}, "type": "array", "description": "Output only. Label columns that were used to train this model. The output of the model will have a \"predicted_\" prefix to these columns."}}, "id": "Model"}, "ScriptStatistics": {"type": "object", "id": "ScriptStatistics", "properties": {"stackFrames": {"items": {"$ref": "ScriptStackFrame"}, "type": "array", "description": "Stack trace showing the line/column/procedure name of each frame on the stack at the point where the current evaluation happened. The leaf frame is first, the primary script is last. Never empty."}, "evaluationKind": {"description": "[Output-only] Whether this child job was a statement or expression.", "type": "string"}}}, "BigQueryModelTraining": {"properties": {"expectedTotalIterations": {"format": "int64", "description": "[Output-only, Beta] Expected number of iterations for the create model query job specified as num_iterations in the input query. The actual total number of iterations may be less than this number due to early stop.", "type": "string"}, "currentIteration": {"description": "[Output-only, Beta] Index of current ML training iteration. Updated during create model query job to show job progress.", "type": "integer", "format": "int32"}}, "type": "object", "id": "BigQueryModelTraining"}, "Argument": {"description": "Input/output argument of a function or a stored procedure.", "id": "Argument", "type": "object", "properties": {"mode": {"enum": ["MODE_UNSPECIFIED", "IN", "OUT", "INOUT"], "description": "Optional. Specifies whether the argument is input or output. Can be set for procedures only.", "enumDescriptions": ["", "The argument is input-only.", "The argument is output-only.", "The argument is both an input and an output."], "type": "string"}, "dataType": {"$ref": "StandardSqlDataType", "description": "Required unless argument_kind = ANY_TYPE."}, "name": {"type": "string", "description": "Optional. The name of this argument. Can be absent for function return argument."}, "argumentKind": {"enum": ["ARGUMENT_KIND_UNSPECIFIED", "FIXED_TYPE", "ANY_TYPE"], "description": "Optional. Defaults to FIXED_TYPE.", "enumDescriptions": ["", "The argument is a variable with fully specified type, which can be a struct or an array, but not a table.", "The argument is any type, including struct or array, but not a table. To be added: FIXED_TABLE, ANY_TABLE"], "type": "string"}}}, "DatasetReference": {"properties": {"datasetId": {"description": "[Required] A unique ID for this dataset, without the project name. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters.", "type": "string", "annotations": {"required": ["bigquery.datasets.update"]}}, "projectId": {"description": "[Optional] The ID of the project containing this dataset.", "annotations": {"required": ["bigquery.datasets.update"]}, "type": "string"}}, "type": "object", "id": "DatasetReference"}, "ListRowAccessPoliciesResponse": {"type": "object", "description": "Response message for the ListRowAccessPolicies method.", "id": "ListRowAccessPoliciesResponse", "properties": {"rowAccessPolicies": {"type": "array", "description": "Row access policies on the requested table.", "items": {"$ref": "RowAccessPolicy"}}, "nextPageToken": {"type": "string", "description": "A token to request the next page of results."}}}, "ViewDefinition": {"type": "object", "properties": {"query": {"type": "string", "description": "[Required] A query that BigQuery executes when the view is referenced."}, "userDefinedFunctionResources": {"type": "array", "description": "Describes user-defined function resources used in the query.", "items": {"$ref": "UserDefinedFunctionResource"}}, "useLegacySql": {"description": "Specifies whether to use BigQuery's legacy SQL for this view. The default value is true. If set to false, the view will use BigQuery's standard SQL: https://cloud.google.com/bigquery/sql-reference/ Queries and views that reference this view must use the same flag value.", "type": "boolean"}}, "id": "ViewDefinition"}, "TableFieldSchema": {"id": "TableFieldSchema", "properties": {"fields": {"items": {"$ref": "TableFieldSchema"}, "description": "[Optional] Describes the nested schema fields if the type property is set to RECORD.", "type": "array"}, "type": {"description": "[Required] The field data type. Possible values include STRING, BYTES, INTEGER, INT64 (same as INTEGER), FLOAT, FLOAT64 (same as FLOAT), NUMERIC, BIGNUMERIC, BOOLEAN, BOOL (same as BOOLEAN), TIM<PERSON><PERSON>MP, DATE, TIME, DATETIM<PERSON>, RECORD (where RECORD indicates that the field contains a nested schema) or STRUCT (same as RECORD).", "type": "string"}, "name": {"type": "string", "description": "[Required] The field name. The name must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_), and must start with a letter or underscore. The maximum length is 128 characters."}, "mode": {"description": "[Optional] The field mode. Possible values include NULL<PERSON>LE, REQUIRED and REPEATED. The default value is NULLABLE.", "type": "string"}, "categories": {"type": "object", "description": "[Optional] The categories attached to this field, used for field-level access control.", "properties": {"names": {"description": "A list of category resource names. For example, \"projects/1/taxonomies/2/categories/3\". At most 5 categories are allowed.", "type": "array", "items": {"type": "string"}}}}, "description": {"description": "[Optional] The field description. The maximum length is 1,024 characters.", "type": "string"}, "policyTags": {"properties": {"names": {"type": "array", "items": {"type": "string"}, "description": "A list of category resource names. For example, \"projects/1/location/eu/taxonomies/2/policyTags/3\". At most 1 policy tag is allowed."}}, "type": "object"}}, "type": "object"}, "ArimaResult": {"type": "object", "properties": {"seasonalPeriods": {"items": {"enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"], "enumDescriptions": ["", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "type": "string"}, "description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "type": "array"}, "arimaModelInfo": {"type": "array", "items": {"$ref": "ArimaModelInfo"}, "description": "This message is repeated because there are multiple arima models fitted in auto-arima. For non-auto-arima model, its size is one."}}, "description": "(Auto-)arima fitting result. Wrap everything in ArimaResult for easier refactoring if we want to use model-specific iteration results.", "id": "ArimaResult"}, "GetQueryResultsResponse": {"type": "object", "id": "GetQueryResultsResponse", "properties": {"numDmlAffectedRows": {"format": "int64", "description": "[Output-only] The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "type": "string"}, "etag": {"type": "string", "description": "A hash of this response."}, "pageToken": {"description": "A token used for paging results.", "type": "string"}, "kind": {"default": "bigquery#getQueryResultsResponse", "type": "string", "description": "The resource type of the response."}, "jobComplete": {"type": "boolean", "description": "Whether the query has completed or not. If rows or totalRows are present, this will always be true. If this is false, totalRows will not be available."}, "schema": {"description": "The schema of the results. Present only when the query completes successfully.", "$ref": "TableSchema"}, "errors": {"items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "type": "array", "description": "[Output-only] The first errors or warnings encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has completed or was unsuccessful."}, "totalRows": {"format": "uint64", "description": "The total number of rows in the complete query result set, which can be more than the number of rows in this single page of results. Present only when the query completes successfully.", "type": "string"}, "cacheHit": {"type": "boolean", "description": "Whether the query result was fetched from the query cache."}, "totalBytesProcessed": {"type": "string", "description": "The total number of bytes processed for this query.", "format": "int64"}, "jobReference": {"$ref": "JobReference", "description": "Reference to the BigQuery Job that was created to run the query. This field will be present even if the original request timed out, in which case GetQueryResults can be used to read the results once the query has completed. Since this API only returns the first page of results, subsequent pages can be fetched via the same mechanism (GetQueryResults)."}, "rows": {"type": "array", "description": "An object with as many results as can be contained within the maximum permitted reply size. To get any additional rows, you can call GetQueryResults and specify the jobReference returned above. Present only when the query completes successfully.", "items": {"$ref": "TableRow"}}}}, "RowAccessPolicy": {"description": "Represents access on a subset of rows on the specified table, defined by its filter predicate. Access to the subset of rows is controlled by its IAM policy.", "type": "object", "properties": {"creationTime": {"description": "Output only. The time when this row access policy was created, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "google-datetime"}, "etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "filterPredicate": {"type": "string", "description": "Required. A SQL boolean expression that represents the rows defined by this row access policy, similar to the boolean expression in a WHERE clause of a SELECT query on a table. References to other tables, routines, and temporary functions are not supported. Examples: region=\"EU\" date_field = CAST('2019-9-27' as DATE) nullable_field is not NULL numeric_field BETWEEN 1.0 AND 5.0"}, "rowAccessPolicyReference": {"description": "Required. Reference describing the ID of this row access policy.", "$ref": "RowAccessPolicyReference"}, "lastModifiedTime": {"format": "google-datetime", "description": "Output only. The time when this row access policy was last modified, in milliseconds since the epoch.", "readOnly": true, "type": "string"}}, "id": "RowAccessPolicy"}, "JobReference": {"type": "object", "id": "JobReference", "properties": {"projectId": {"type": "string", "annotations": {"required": ["bigquery.jobs.getQueryResults"]}, "description": "[Required] The ID of the project containing this job."}, "jobId": {"description": "[Required] The ID of the job. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-). The maximum length is 1,024 characters.", "annotations": {"required": ["bigquery.jobs.getQueryResults"]}, "type": "string"}, "location": {"type": "string", "description": "The geographic location of the job. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location."}}}, "BqmlTrainingRun": {"id": "BqmlTrainingRun", "properties": {"startTime": {"format": "date-time", "description": "[Output-only, Beta] Training run start time in milliseconds since the epoch.", "type": "string"}, "iterationResults": {"items": {"$ref": "BqmlIterationResult"}, "description": "[Output-only, Beta] List of each iteration results.", "type": "array"}, "trainingOptions": {"properties": {"learnRateStrategy": {"type": "string"}, "l1Reg": {"type": "number", "format": "double"}, "minRelProgress": {"type": "number", "format": "double"}, "l2Reg": {"format": "double", "type": "number"}, "lineSearchInitLearnRate": {"type": "number", "format": "double"}, "maxIteration": {"format": "int64", "type": "string"}, "warmStart": {"type": "boolean"}, "earlyStop": {"type": "boolean"}, "learnRate": {"format": "double", "type": "number"}}, "type": "object", "description": "[Output-only, Beta] Training options used by this training run. These options are mutable for subsequent training runs. Default values are explicitly stored for options not specified in the input query of the first training run. For subsequent training runs, any option not explicitly specified in the input query will be copied from the previous training run."}, "state": {"description": "[Output-only, Beta] Different state applicable for a training run. IN PROGRESS: Training run is in progress. FAILED: Training run ended due to a non-retryable failure. SUCCEEDED: Training run successfully completed. CANCELLED: Training run cancelled by the user.", "type": "string"}}, "type": "object"}, "TableDataInsertAllResponse": {"type": "object", "id": "TableDataInsertAllResponse", "properties": {"kind": {"description": "The resource type of the response.", "default": "bigquery#tableDataInsertAllResponse", "type": "string"}, "insertErrors": {"description": "An array of errors for rows that were not inserted.", "items": {"type": "object", "properties": {"errors": {"type": "array", "description": "Error information for the row indicated by the index property.", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}, "index": {"type": "integer", "description": "The index of the row that error applies to.", "format": "uint32"}}}, "type": "array"}}}, "BinaryClassificationMetrics": {"description": "Evaluation metrics for binary classification/classifier models.", "id": "BinaryClassificationMetrics", "properties": {"binaryConfusionMatrixList": {"items": {"$ref": "BinaryConfusionMatrix"}, "type": "array", "description": "Binary confusion matrix at multiple thresholds."}, "positiveLabel": {"type": "string", "description": "Label representing the positive class."}, "aggregateClassificationMetrics": {"$ref": "AggregateClassificationMetrics", "description": "Aggregate classification metrics."}, "negativeLabel": {"type": "string", "description": "Label representing the negative class."}}, "type": "object"}, "ClusteringMetrics": {"id": "ClusteringMetrics", "type": "object", "description": "Evaluation metrics for clustering models.", "properties": {"meanSquaredDistance": {"format": "double", "type": "number", "description": "Mean of squared distances between each sample to its cluster centroid."}, "clusters": {"description": "Information for all clusters.", "items": {"$ref": "Cluster"}, "type": "array"}, "daviesBouldinIndex": {"format": "double", "type": "number", "description": "Davies-Bouldin index."}}}, "ListModelsResponse": {"id": "ListModelsResponse", "type": "object", "properties": {"nextPageToken": {"type": "string", "description": "A token to request the next page of results."}, "models": {"items": {"$ref": "Model"}, "type": "array", "description": "Models in the requested dataset. Only the following fields are populated: model_reference, model_type, creation_time, last_modified_time and labels."}}}, "ArimaCoefficients": {"type": "object", "properties": {"movingAverageCoefficients": {"type": "array", "description": "Moving-average coefficients, an array of double.", "items": {"type": "number", "format": "double"}}, "autoRegressiveCoefficients": {"items": {"type": "number", "format": "double"}, "type": "array", "description": "Auto-regressive coefficients, an array of double."}, "interceptCoefficient": {"format": "double", "description": "Intercept coefficient, just a double not an array.", "type": "number"}}, "id": "ArimaCoefficients", "description": "Arima coefficients."}, "Table": {"id": "Table", "properties": {"materializedView": {"description": "[Optional] Materialized view definition.", "$ref": "MaterializedViewDefinition"}, "timePartitioning": {"$ref": "TimePartitioning", "description": "Time-based partitioning specification for this table. Only one of timePartitioning and rangePartitioning should be specified."}, "schema": {"description": "[Optional] Describes the schema of this table.", "$ref": "TableSchema"}, "selfLink": {"description": "[Output-only] A URL that can be used to access this resource again.", "type": "string"}, "snapshotDefinition": {"$ref": "SnapshotDefinition", "description": "[Output-only] Snapshot definition."}, "etag": {"type": "string", "description": "[Output-only] A hash of the table metadata. Used to ensure there were no concurrent modifications to the resource when attempting an update. Not guaranteed to change when the table contents or the fields numRows, numBytes, numLongTermBytes or lastModifiedTime change."}, "description": {"description": "[Optional] A user-friendly description of this table.", "type": "string"}, "tableReference": {"description": "[Required] Reference describing the ID of this table.", "$ref": "TableReference"}, "view": {"description": "[Optional] The view definition.", "$ref": "ViewDefinition"}, "kind": {"description": "[Output-only] The type of the resource.", "type": "string", "default": "bigquery#table"}, "friendlyName": {"description": "[Optional] A descriptive name for this table.", "type": "string"}, "numPhysicalBytes": {"type": "string", "format": "int64", "description": "[Output-only] [TrustedTester] The physical size of this table in bytes, excluding any data in the streaming buffer. This includes compression and storage used for time travel."}, "lastModifiedTime": {"format": "uint64", "type": "string", "description": "[Output-only] The time when this table was last modified, in milliseconds since the epoch."}, "expirationTime": {"type": "string", "description": "[Optional] The time when this table expires, in milliseconds since the epoch. If not present, the table will persist indefinitely. Expired tables will be deleted and their storage reclaimed. The defaultTableExpirationMs property of the encapsulating dataset can be used to set a default expirationTime on newly created tables.", "format": "int64"}, "externalDataConfiguration": {"$ref": "ExternalDataConfiguration", "description": "[Optional] Describes the data format, location, and other properties of a table stored outside of BigQuery. By defining these properties, the data source can then be queried as if it were a standard BigQuery table."}, "streamingBuffer": {"$ref": "Streamingbuffer", "description": "[Output-only] Contains information regarding this table's streaming buffer, if one is present. This field will be absent if the table is not being streamed to or if there is no data in the streaming buffer."}, "numRows": {"type": "string", "description": "[Output-only] The number of rows of data in this table, excluding any data in the streaming buffer.", "format": "uint64"}, "location": {"description": "[Output-only] The geographic location where the table resides. This value is inherited from the dataset.", "type": "string"}, "numLongTermBytes": {"type": "string", "format": "int64", "description": "[Output-only] The number of bytes in the table that are considered \"long-term storage\"."}, "clustering": {"$ref": "Clustering", "description": "[Beta] Clustering specification for the table. Must be specified with partitioning, data in the table will be first partitioned and subsequently clustered."}, "creationTime": {"format": "int64", "type": "string", "description": "[Output-only] The time when this table was created, in milliseconds since the epoch."}, "type": {"type": "string", "description": "[Output-only] Describes the table type. The following values are supported: TABLE: A normal BigQuery table. VIEW: A virtual table defined by a SQL query. SNAPSHOT: An immutable, read-only table that is a copy of another table. [TrustedTester] MATERIALIZED_VIEW: SQL query whose result is persisted. EXTERNAL: A table that references data stored in an external storage system, such as Google Cloud Storage. The default value is TABLE."}, "id": {"description": "[Output-only] An opaque ID uniquely identifying the table.", "type": "string"}, "model": {"description": "[Output-only, Beta] Present iff this table represents a ML model. Describes the training information for the model, and it is required to run 'PREDICT' queries.", "$ref": "ModelDefinition"}, "requirePartitionFilter": {"default": "false", "type": "boolean", "description": "[Optional] If set to true, queries over this table require a partition filter that can be used for partition elimination to be specified."}, "numBytes": {"format": "int64", "description": "[Output-only] The size of this table in bytes, excluding any data in the streaming buffer.", "type": "string"}, "labels": {"type": "object", "additionalProperties": {"type": "string"}, "description": "The labels associated with this table. You can use these to organize and group your tables. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key."}, "rangePartitioning": {"description": "[TrustedTester] Range partitioning specification for this table. Only one of timePartitioning and rangePartitioning should be specified.", "$ref": "RangePartitioning"}, "encryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)."}}, "type": "object"}, "ArimaOrder": {"type": "object", "description": "Arima order, can be used for both non-seasonal and seasonal parts.", "id": "ArimaOrder", "properties": {"d": {"description": "Order of the differencing part.", "format": "int64", "type": "string"}, "q": {"format": "int64", "description": "Order of the moving-average part.", "type": "string"}, "p": {"type": "string", "format": "int64", "description": "Order of the autoregressive part."}}}, "BinaryConfusionMatrix": {"type": "object", "properties": {"recall": {"description": "The fraction of actual positive labels that were given a positive prediction.", "type": "number", "format": "double"}, "f1Score": {"type": "number", "format": "double", "description": "The equally weighted average of recall and precision."}, "falseNegatives": {"description": "Number of false samples predicted as false.", "format": "int64", "type": "string"}, "positiveClassThreshold": {"format": "double", "description": "Threshold value used when computing each of the following metric.", "type": "number"}, "precision": {"type": "number", "format": "double", "description": "The fraction of actual positive predictions that had positive actual labels."}, "trueNegatives": {"type": "string", "format": "int64", "description": "Number of true samples predicted as false."}, "truePositives": {"type": "string", "description": "Number of true samples predicted as true.", "format": "int64"}, "falsePositives": {"description": "Number of false samples predicted as true.", "type": "string", "format": "int64"}, "accuracy": {"description": "The fraction of predictions given the correct label.", "format": "double", "type": "number"}}, "id": "BinaryConfusionMatrix", "description": "Confusion matrix for binary classification models."}, "GoogleSheetsOptions": {"type": "object", "properties": {"skipLeadingRows": {"format": "int64", "type": "string", "description": "[Optional] The number of rows at the top of a sheet that BigQuery will skip when reading the data. The default value is 0. This property is useful if you have header rows that should be skipped. When autodetect is on, behavior is the following: * skipLeadingRows unspecified - Autodetect tries to detect headers in the first row. If they are not detected, the row is read as data. Otherwise data is read starting from the second row. * skipLeadingRows is 0 - Instructs autodetect that there are no headers and data should be read starting from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1 rows and tries to detect headers in row N. If headers are not detected, row N is just skipped. Otherwise row N is used to extract column names for the detected schema."}, "range": {"description": "[Optional] Range of a sheet to query from. Only used when non-empty. Typical format: sheet_name!top_left_cell_id:bottom_right_cell_id For example: sheet1!A1:B20", "type": "string"}}, "id": "GoogleSheetsOptions"}, "JobConfigurationLoad": {"id": "JobConfigurationLoad", "type": "object", "properties": {"createDisposition": {"description": "[Optional] Specifies whether the job is allowed to create new tables. The following values are supported: CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "clustering": {"$ref": "Clustering", "description": "[Beta] Clustering specification for the destination table. Must be specified with time-based partitioning, data in the table will be first partitioned and subsequently clustered."}, "decimalTargetTypes": {"type": "array", "description": "Defines the list of possible SQL data types to which the source decimal values are converted. This list and the precision and the scale parameters of the decimal field determine the target type. In the order of NUMERIC, BIGNUMERIC ([Preview](/products/#product-launch-stages)), and STRING, a type is picked if it is in the specified list and if it supports the precision and the scale. STRING supports all precision and scale values. If none of the listed types supports the precision and the scale, the type supporting the widest range in the specified list is picked, and if a value exceeds the supported range when reading the data, an error will be thrown. Example: Suppose the value of this field is [\"NUMERIC\", \"BIGNUMERIC\"]. If (precision,scale) is: * (38,9) -> NUMERIC; * (39,9) -> BIGNUMERIC (NUMERIC cannot hold 30 integer digits); * (38,10) -> <PERSON>IGNUMERI<PERSON> (NUMERIC cannot hold 10 fractional digits); * (76,38) -> <PERSON>IGNUMERIC; * (77,38) -> BIGNUMERIC (error if value exeeds supported range). This field cannot contain duplicate types. The order of the types in this field is ignored. For example, [\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>ER<PERSON>\"] is the same as [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"] and NUMERIC always takes precedence over <PERSON>IGNUMERI<PERSON>. Defaults to [\"NUMERIC\", \"STRING\"] for ORC and [\"NUMERIC\"] for the other file formats.", "items": {"type": "string"}}, "destinationEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)."}, "allowQuotedNewlines": {"description": "Indicates if BigQuery should allow quoted data sections that contain newline characters in a CSV file. The default value is false.", "type": "boolean"}, "rangePartitioning": {"description": "[TrustedTester] Range partitioning specification for this table. Only one of timePartitioning and rangePartitioning should be specified.", "$ref": "RangePartitioning"}, "sourceFormat": {"type": "string", "description": "[Optional] The format of the data files. For CSV files, specify \"CSV\". For datastore backups, specify \"DATASTORE_BACKUP\". For newline-delimited JSON, specify \"NEWLINE_DELIMITED_JSON\". For Avro, specify \"AVRO\". For parquet, specify \"PARQUET\". For orc, specify \"ORC\". The default value is CSV."}, "schemaUpdateOptions": {"items": {"type": "string"}, "type": "array", "description": "Allows the schema of the destination table to be updated as a side effect of the load job if a schema is autodetected or supplied in the job configuration. Schema update options are supported in two cases: when writeDisposition is WRITE_APPEND; when writeDisposition is WRITE_TRUNCATE and the destination table is a partition of a table, specified by partition decorators. For normal tables, WRITE_TRUNCATE will always overwrite the schema. One or more of the following values are specified: ALLOW_FIELD_ADDITION: allow adding a nullable field to the schema. ALLOW_FIELD_RELAXATION: allow relaxing a required field in the original schema to nullable."}, "quote": {"pattern": ".?", "description": "[Optional] The value that is used to quote data sections in a CSV file. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. The default value is a double-quote ('\"'). If your data does not contain quoted sections, set the property value to an empty string. If your data contains quoted newline characters, you must also set the allowQuotedNewlines property to true.", "default": "\"", "type": "string"}, "skipLeadingRows": {"format": "int32", "type": "integer", "description": "[Optional] The number of rows at the top of a CSV file that BigQuery will skip when loading the data. The default value is 0. This property is useful if you have header rows in the file that should be skipped."}, "destinationTableProperties": {"description": "[Beta] [Optional] Properties with which to create the destination table if it is new.", "$ref": "DestinationTableProperties"}, "encoding": {"description": "[Optional] The character encoding of the data. The supported values are UTF-8 or ISO-8859-1. The default value is UTF-8. BigQuery decodes the data after the raw, binary data has been split using the values of the quote and fieldDelimiter properties.", "type": "string"}, "hivePartitioningOptions": {"$ref": "HivePartitioningOptions", "description": "[Optional] Options to configure hive partitioning support."}, "jsonExtension": {"description": "[Optional] If sourceFormat is set to newline-delimited JSON, indicates whether it should be processed as a JSON variant such as GeoJSON. For a sourceFormat other than JSON, omit this field. If the sourceFormat is newline-delimited JSON: - for newline-delimited GeoJSON: set to GEOJSON.", "type": "string"}, "sourceUris": {"type": "array", "description": "[Required] The fully-qualified URIs that point to your data in Google Cloud. For Google Cloud Storage URIs: Each URI can contain one '*' wildcard character and it must come after the 'bucket' name. Size limits related to load jobs apply to external data sources. For Google Cloud Bigtable URIs: Exactly one URI can be specified and it has be a fully specified and valid HTTPS URL for a Google Cloud Bigtable table. For Google Cloud Datastore backups: Exactly one URI can be specified. Also, the '*' wildcard character is not allowed.", "items": {"type": "string"}}, "allowJaggedRows": {"type": "boolean", "description": "[Optional] Accept rows that are missing trailing optional columns. The missing values are treated as nulls. If false, records with missing trailing columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. Only applicable to CSV, ignored for other formats."}, "fieldDelimiter": {"description": "[Optional] The separator for fields in a CSV file. The separator can be any ISO-8859-1 single-byte character. To use a character in the range 128-255, you must encode the character as UTF8. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. BigQuery also supports the escape sequence \"\\t\" to specify a tab separator. The default value is a comma (',').", "type": "string"}, "autodetect": {"description": "[Optional] Indicates if we should automatically infer the options and schema for CSV and JSON sources.", "type": "boolean"}, "writeDisposition": {"description": "[Optional] Specifies the action that occurs if the destination table already exists. The following values are supported: WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the table data. WRITE_APPEND: If the table already exists, <PERSON>Query appends the data to the table. WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_APPEND. Each action is atomic and only occurs if <PERSON><PERSON><PERSON><PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "maxBadRecords": {"type": "integer", "description": "[Optional] The maximum number of bad records that BigQuery can ignore when running the job. If the number of bad records exceeds this value, an invalid error is returned in the job result. This is only valid for CSV and JSON. The default value is 0, which requires that all records are valid.", "format": "int32"}, "schemaInline": {"type": "string", "description": "[Deprecated] The inline schema. For CSV schemas, specify as \"Field1:Type1[,Field2:Type2]*\". For example, \"foo:STRING, bar:INTEGER, baz:FLOAT\"."}, "schema": {"$ref": "TableSchema", "description": "[Optional] The schema for the destination table. The schema can be omitted if the destination table already exists, or if you're loading data from Google Cloud Datastore."}, "schemaInlineFormat": {"type": "string", "description": "[Deprecated] The format of the schemaInline property."}, "projectionFields": {"type": "array", "items": {"type": "string"}, "description": "If sourceFormat is set to \"DATASTORE_BACKUP\", indicates which entity properties to load into BigQuery from a Cloud Datastore backup. Property names are case sensitive and must be top-level properties. If no properties are specified, BigQuery loads all properties. If any named property isn't found in the Cloud Datastore backup, an invalid error is returned in the job result."}, "nullMarker": {"description": "[Optional] Specifies a string that represents a null value in a CSV file. For example, if you specify \"\\N\", BigQuery interprets \"\\N\" as a null value when loading a CSV file. The default value is the empty string. If you set this property to a custom value, BigQuery throws an error if an empty string is present for all data types except for STRING and BYTE. For STRING and BYTE columns, BigQuery interprets the empty string as an empty value.", "type": "string"}, "ignoreUnknownValues": {"type": "boolean", "description": "[Optional] Indicates if BigQuery should allow extra values that are not represented in the table schema. If true, the extra values are ignored. If false, records with extra columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. The sourceFormat property determines what BigQuery treats as an extra value: CSV: Trailing columns JSON: Named values that don't match any column names"}, "destinationTable": {"description": "[Required] The destination table to load the data into.", "$ref": "TableReference"}, "useAvroLogicalTypes": {"description": "[Optional] If sourceFormat is set to \"AVRO\", indicates whether to enable interpreting logical types into their corresponding types (ie. TIMESTAMP), instead of only using their raw types (ie. INTEGER).", "type": "boolean"}, "timePartitioning": {"description": "Time-based partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified.", "$ref": "TimePartitioning"}}}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "type": "object", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "title": {"type": "string", "description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression."}}}, "JobConfiguration": {"properties": {"load": {"description": "[Pick one] Configures a load job.", "$ref": "JobConfigurationLoad"}, "labels": {"description": "The labels associated with this job. You can use these to organize and group your jobs. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "additionalProperties": {"type": "string"}, "type": "object"}, "jobTimeoutMs": {"description": "[Optional] Job timeout in milliseconds. If this time limit is exceeded, <PERSON><PERSON><PERSON><PERSON> may attempt to terminate the job.", "type": "string", "format": "int64"}, "query": {"description": "[Pick one] Configures a query job.", "$ref": "JobConfigurationQuery"}, "jobType": {"type": "string", "description": "[Output-only] The type of the job. Can be QUERY, LOAD, EXTRACT, COPY or UNKNOWN."}, "copy": {"$ref": "JobConfigurationTableCopy", "description": "[Pick one] Copies a table."}, "extract": {"description": "[Pick one] Configures an extract job.", "$ref": "JobConfigurationExtract"}, "dryRun": {"type": "boolean", "description": "[Optional] If set, don't actually run this job. A valid query will return a mostly empty response with some processing statistics, while an invalid query will return the same error it would if it wasn't a dry run. Behavior of non-query jobs is undefined."}}, "id": "JobConfiguration", "type": "object"}, "ClusterInfo": {"type": "object", "id": "ClusterInfo", "properties": {"clusterRadius": {"type": "number", "format": "double", "description": "Cluster radius, the average distance from centroid to each point assigned to the cluster."}, "centroidId": {"format": "int64", "description": "Centroid id.", "type": "string"}, "clusterSize": {"description": "Cluster size, the total number of points assigned to the cluster.", "type": "string", "format": "int64"}}, "description": "Information about a single cluster for clustering model."}, "DestinationTableProperties": {"properties": {"description": {"type": "string", "description": "[Optional] The description for the destination table. This will only be used if the destination table is newly created. If the table already exists and a value different than the current description is provided, the job will fail."}, "friendlyName": {"description": "[Optional] The friendly name for the destination table. This will only be used if the destination table is newly created. If the table already exists and a value different than the current friendly name is provided, the job will fail.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "[Optional] The labels associated with this table. You can use these to organize and group your tables. This will only be used if the destination table is newly created. If the table already exists and labels are different than the current labels are provided, the job will fail.", "type": "object"}}, "id": "DestinationTableProperties", "type": "object"}, "JobList": {"properties": {"nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}, "kind": {"type": "string", "default": "bigquery#jobList", "description": "The resource type of the response."}, "etag": {"type": "string", "description": "A hash of this page of results."}, "jobs": {"items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique opaque ID of the job."}, "kind": {"default": "bigquery#job", "type": "string", "description": "The resource type."}, "configuration": {"description": "[Full-projection-only] Specifies the job configuration.", "$ref": "JobConfiguration"}, "jobReference": {"$ref": "JobReference", "description": "Job reference uniquely identifying the job."}, "status": {"description": "[Full-projection-only] Describes the state of the job.", "$ref": "JobStatus"}, "user_email": {"description": "[Full-projection-only] Email address of the user who ran the job.", "type": "string"}, "errorResult": {"description": "A result object that will be present only if the job has failed.", "$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "statistics": {"description": "[Output-only] Information about the job, including starting time and ending time of the job.", "$ref": "JobStatistics"}, "state": {"description": "Running state of the job. When the state is DONE, errorResult can be checked to determine whether the job succeeded or failed.", "type": "string"}}}, "description": "List of jobs that were requested.", "type": "array"}}, "type": "object", "id": "JobList"}, "QueryTimelineSample": {"type": "object", "properties": {"completedUnits": {"type": "string", "description": "Total parallel units of work completed by this query.", "format": "int64"}, "totalSlotMs": {"description": "Cumulative slot-ms consumed by the query.", "type": "string", "format": "int64"}, "pendingUnits": {"type": "string", "description": "Total parallel units of work remaining for the active stages.", "format": "int64"}, "elapsedMs": {"format": "int64", "type": "string", "description": "Milliseconds elapsed since the start of query execution."}, "activeUnits": {"type": "string", "format": "int64", "description": "Total number of units currently being processed by workers. This does not correspond directly to slot usage. This is the largest value observed since the last sample."}}, "id": "QueryTimelineSample"}, "JsonValue": {"type": "any", "id": "JsonValue"}, "TableReference": {"type": "object", "id": "TableReference", "properties": {"datasetId": {"description": "[Required] The ID of the dataset containing this table.", "annotations": {"required": ["bigquery.tables.update"]}, "type": "string"}, "tableId": {"annotations": {"required": ["bigquery.tables.update"]}, "description": "[Required] The ID of the table. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters.", "type": "string"}, "projectId": {"annotations": {"required": ["bigquery.tables.update"]}, "type": "string", "description": "[Required] The ID of the project containing this table."}}}, "Policy": {"properties": {"bindings": {"description": "Associates a list of `members` to a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one member.", "items": {"$ref": "Binding"}, "type": "array"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}, "auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "etag": {"format": "byte", "type": "string", "description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost."}}, "description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members` to a single `role`. Members can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } **YAML example:** bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') - etag: BwWWja0YfJA= - version: 3 For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "type": "object", "id": "Policy"}, "RowLevelSecurityStatistics": {"type": "object", "id": "RowLevelSecurityStatistics", "properties": {"rowLevelSecurityApplied": {"description": "[Output-only] [Preview] Whether any accessed data was protected by row access policies.", "type": "boolean"}}}, "TestIamPermissionsResponse": {"properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "type": "object"}, "ArimaFittingMetrics": {"properties": {"logLikelihood": {"description": "Log-likelihood.", "format": "double", "type": "number"}, "variance": {"type": "number", "description": "Varian<PERSON>.", "format": "double"}, "aic": {"format": "double", "type": "number", "description": "AIC."}}, "type": "object", "description": "ARIMA model fitting metrics.", "id": "ArimaFittingMetrics"}, "TableList": {"properties": {"tables": {"type": "array", "items": {"properties": {"labels": {"description": "The labels associated with this table. You can use these to organize and group your tables.", "type": "object", "additionalProperties": {"type": "string"}}, "expirationTime": {"format": "int64", "type": "string", "description": "[Optional] The time when this table expires, in milliseconds since the epoch. If not present, the table will persist indefinitely. Expired tables will be deleted and their storage reclaimed."}, "kind": {"default": "bigquery#table", "type": "string", "description": "The resource type."}, "id": {"type": "string", "description": "An opaque ID of the table"}, "clustering": {"description": "[Beta] Clustering specification for this table, if configured.", "$ref": "Clustering"}, "rangePartitioning": {"description": "The range partitioning specification for this table, if configured.", "$ref": "RangePartitioning"}, "creationTime": {"format": "int64", "description": "The time when this table was created, in milliseconds since the epoch.", "type": "string"}, "tableReference": {"description": "A reference uniquely identifying the table.", "$ref": "TableReference"}, "friendlyName": {"description": "The user-friendly name for this table.", "type": "string"}, "type": {"type": "string", "description": "The type of table. Possible values are: TABLE, VIEW."}, "timePartitioning": {"description": "The time-based partitioning specification for this table, if configured.", "$ref": "TimePartitioning"}, "view": {"type": "object", "description": "Additional details for a view.", "properties": {"useLegacySql": {"description": "True if view is defined in legacy SQL dialect, false if in standard SQL.", "type": "boolean"}}}}, "type": "object"}, "description": "Tables in the requested dataset."}, "totalItems": {"description": "The total number of tables in the dataset.", "format": "int32", "type": "integer"}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}, "kind": {"default": "bigquery#tableList", "type": "string", "description": "The type of list."}, "etag": {"type": "string", "description": "A hash of this page of results."}}, "type": "object", "id": "TableList"}, "TimePartitioning": {"properties": {"expirationMs": {"format": "int64", "type": "string", "description": "[Optional] Number of milliseconds for which to keep the storage for partitions in the table. The storage in a partition will have an expiration time of its partition time plus this value."}, "field": {"type": "string", "description": "[Beta] [Optional] If not set, the table is partitioned by pseudo column, referenced via either '_PARTITIONTIME' as TIMESTAMP type, or '_PARTITIONDATE' as DATE type. If field is specified, the table is instead partitioned by this field. The field must be a top-level TIMESTAMP or DATE field. Its mode must be NULLABLE or REQUIRED."}, "type": {"description": "[Required] The supported types are DAY, HOUR, MONTH, and YEAR, which will generate one partition per day, hour, month, and year, respectively. When the type is not specified, the default behavior is DAY.", "type": "string"}, "requirePartitionFilter": {"type": "boolean"}}, "type": "object", "id": "TimePartitioning"}, "ArimaForecastingMetrics": {"type": "object", "properties": {"arimaSingleModelForecastingMetrics": {"items": {"$ref": "ArimaSingleModelForecastingMetrics"}, "description": "Repeated as there can be many metric sets (one for each model) in auto-arima and the large-scale case.", "type": "array"}, "arimaFittingMetrics": {"items": {"$ref": "ArimaFittingMetrics"}, "description": "Arima model fitting metrics.", "type": "array"}, "seasonalPeriods": {"type": "array", "description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "items": {"enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"], "enumDescriptions": ["", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "type": "string"}}, "timeSeriesId": {"items": {"type": "string"}, "description": "Id to differentiate different time series for the large-scale case.", "type": "array"}, "nonSeasonalOrder": {"description": "Non-seasonal order.", "type": "array", "items": {"$ref": "ArimaOrder"}}, "hasDrift": {"items": {"type": "boolean"}, "type": "array", "description": "Whether Arima model fitted with drift or not. It is always false when d is not 1."}}, "id": "ArimaForecastingMetrics", "description": "Model evaluation metrics for ARIMA forecasting models."}, "PrincipalComponentInfo": {"description": "Principal component infos, used only for eigen decomposition based models, e.g., PCA. Ordered by explained_variance in the descending order.", "id": "PrincipalComponentInfo", "type": "object", "properties": {"explainedVarianceRatio": {"description": "Explained_variance over the total explained variance.", "format": "double", "type": "number"}, "cumulativeExplainedVarianceRatio": {"type": "number", "format": "double", "description": "The explained_variance is pre-ordered in the descending order to compute the cumulative explained variance ratio."}, "principalComponentId": {"type": "string", "description": "Id of the principal component.", "format": "int64"}, "explainedVariance": {"description": "Explained variance by this principal component, which is simply the eigenvalue.", "format": "double", "type": "number"}}}, "EvaluationMetrics": {"properties": {"rankingMetrics": {"description": "Populated for implicit feedback type matrix factorization models.", "$ref": "RankingMetrics"}, "binaryClassificationMetrics": {"description": "Populated for binary classification/classifier models.", "$ref": "BinaryClassificationMetrics"}, "clusteringMetrics": {"description": "Populated for clustering models.", "$ref": "ClusteringMetrics"}, "regressionMetrics": {"description": "Populated for regression models and explicit feedback type matrix factorization models.", "$ref": "RegressionMetrics"}, "arimaForecastingMetrics": {"description": "Populated for ARIMA models.", "$ref": "ArimaForecastingMetrics"}, "dimensionalityReductionMetrics": {"$ref": "DimensionalityReductionMetrics", "description": "Evaluation metrics when the model is a dimensionality reduction model, which currently includes PCA."}, "multiClassClassificationMetrics": {"description": "Populated for multi-class classification/classifier models.", "$ref": "MultiClassClassificationMetrics"}}, "description": "Evaluation metrics of a model. These are either computed on all training data or just the eval data based on whether eval data was used during training. These are not present for imported models.", "type": "object", "id": "EvaluationMetrics"}, "JobStatistics4": {"properties": {"inputBytes": {"type": "string", "format": "int64", "description": "[Output-only] Number of user bytes extracted into the result. This is the byte count as computed by BigQuery for billing purposes."}, "destinationUriFileCounts": {"description": "[Output-only] Number of files per destination URI or URI pattern specified in the extract configuration. These values will be in the same order as the URIs specified in the 'destinationUris' field.", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object", "id": "JobStatistics4"}, "TableRow": {"properties": {"f": {"description": "Represents a single row in the result set, consisting of one or more fields.", "items": {"$ref": "TableCell"}, "type": "array"}}, "type": "object", "id": "TableRow"}, "RegressionMetrics": {"id": "RegressionMetrics", "properties": {"rSquared": {"format": "double", "description": "R^2 score. This corresponds to r2_score in ML.EVALUATE.", "type": "number"}, "meanSquaredError": {"description": "Mean squared error.", "format": "double", "type": "number"}, "meanSquaredLogError": {"type": "number", "description": "Mean squared log error.", "format": "double"}, "meanAbsoluteError": {"description": "Mean absolute error.", "type": "number", "format": "double"}, "medianAbsoluteError": {"format": "double", "description": "Median absolute error.", "type": "number"}}, "description": "Evaluation metrics for regression and explicit feedback type matrix factorization models.", "type": "object"}, "DataSplitResult": {"id": "DataSplitResult", "type": "object", "description": "Data split result. This contains references to the training and evaluation data tables that were used to train the model.", "properties": {"evaluationTable": {"$ref": "TableReference", "description": "Table reference of the evaluation data after split."}, "trainingTable": {"description": "Table reference of the training data after split.", "$ref": "TableReference"}}}, "JsonObject": {"type": "object", "id": "JsonObject", "description": "Represents a single JSON object.", "additionalProperties": {"$ref": "JsonValue"}}, "DimensionalityReductionMetrics": {"type": "object", "id": "DimensionalityReductionMetrics", "properties": {"totalExplainedVarianceRatio": {"format": "double", "description": "Total percentage of variance explained by the selected principal components.", "type": "number"}}, "description": "Model evaluation metrics for dimensionality reduction models."}}, "batchPath": "batch/bigquery/v2", "ownerName": "Google", "kind": "discovery#restDescription", "servicePath": "bigquery/v2/", "resources": {"tables": {"methods": {"insert": {"response": {"$ref": "Table"}, "path": "projects/{projectId}/datasets/{datasetId}/tables", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "parameterOrder": ["projectId", "datasetId"], "httpMethod": "POST", "description": "Creates a new, empty table in the dataset.", "parameters": {"projectId": {"required": true, "type": "string", "description": "Project ID of the new table", "location": "path"}, "datasetId": {"type": "string", "required": true, "description": "Dataset ID of the new table", "location": "path"}}, "id": "bigquery.tables.insert", "request": {"$ref": "Table"}}, "update": {"scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "parameters": {"datasetId": {"required": true, "description": "Dataset ID of the table to update", "location": "path", "type": "string"}, "projectId": {"type": "string", "description": "Project ID of the table to update", "location": "path", "required": true}, "tableId": {"description": "Table ID of the table to update", "location": "path", "type": "string", "required": true}}, "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}", "parameterOrder": ["projectId", "datasetId", "tableId"], "response": {"$ref": "Table"}, "id": "bigquery.tables.update", "httpMethod": "PUT", "request": {"$ref": "Table"}, "description": "Updates information in an existing table. The update method replaces the entire table resource, whereas the patch method only replaces fields that are provided in the submitted table resource."}, "setIamPolicy": {"path": "{+resource}:setIamPolicy", "response": {"$ref": "Policy"}, "parameters": {"resource": {"required": true, "description": "REQUIRED: The resource for which the policy is being specified. See the operation documentation for the appropriate value for this field.", "type": "string", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "location": "path"}}, "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "parameterOrder": ["resource"], "description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "id": "bigquery.tables.setIamPolicy", "httpMethod": "POST"}, "list": {"response": {"$ref": "TableList"}, "id": "bigquery.tables.list", "parameterOrder": ["projectId", "datasetId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "path": "projects/{projectId}/datasets/{datasetId}/tables", "description": "Lists all tables in the specified dataset. Requires the READER dataset role.", "parameters": {"projectId": {"required": true, "description": "Project ID of the tables to list", "location": "path", "type": "string"}, "datasetId": {"required": true, "description": "Dataset ID of the tables to list", "type": "string", "location": "path"}, "maxResults": {"type": "integer", "format": "uint32", "description": "Maximum number of results to return", "location": "query"}, "pageToken": {"location": "query", "type": "string", "description": "Page token, returned by a previous call, to request the next page of results"}}, "httpMethod": "GET"}, "getIamPolicy": {"flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:getIamPolicy", "id": "bigquery.tables.getIamPolicy", "description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "httpMethod": "POST", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "parameterOrder": ["resource"], "request": {"$ref": "GetIamPolicyRequest"}, "parameters": {"resource": {"type": "string", "required": true, "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "description": "REQUIRED: The resource for which the policy is being requested. See the operation documentation for the appropriate value for this field."}}, "path": "{+resource}:getIamPolicy"}, "patch": {"parameters": {"tableId": {"location": "path", "required": true, "type": "string", "description": "Table ID of the table to update"}, "datasetId": {"required": true, "type": "string", "description": "Dataset ID of the table to update", "location": "path"}, "projectId": {"description": "Project ID of the table to update", "location": "path", "type": "string", "required": true}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "httpMethod": "PATCH", "response": {"$ref": "Table"}, "id": "bigquery.tables.patch", "request": {"$ref": "Table"}, "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}", "description": "Updates information in an existing table. The update method replaces the entire table resource, whereas the patch method only replaces fields that are provided in the submitted table resource. This method supports patch semantics."}, "delete": {"description": "Deletes the table specified by tableId from the dataset. If the table contains data, all the data will be deleted.", "httpMethod": "DELETE", "parameters": {"tableId": {"type": "string", "location": "path", "required": true, "description": "Table ID of the table to delete"}, "datasetId": {"description": "Dataset ID of the table to delete", "required": true, "location": "path", "type": "string"}, "projectId": {"description": "Project ID of the table to delete", "required": true, "type": "string", "location": "path"}}, "id": "bigquery.tables.delete", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}", "parameterOrder": ["projectId", "datasetId", "tableId"]}, "get": {"parameterOrder": ["projectId", "datasetId", "tableId"], "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "httpMethod": "GET", "response": {"$ref": "Table"}, "parameters": {"tableId": {"description": "Table ID of the requested table", "required": true, "type": "string", "location": "path"}, "selectedFields": {"location": "query", "type": "string", "description": "List of fields to return (comma-separated). If unspecified, all fields are returned"}, "projectId": {"description": "Project ID of the requested table", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Dataset ID of the requested table", "required": true, "type": "string", "location": "path"}}, "description": "Gets the specified table resource by table ID. This method does not return the data in the table, it only returns the table resource, which describes the structure of this table.", "id": "bigquery.tables.get"}, "testIamPermissions": {"flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:testIamPermissions", "path": "{+resource}:testIamPermissions", "httpMethod": "POST", "response": {"$ref": "TestIamPermissionsResponse"}, "parameters": {"resource": {"type": "string", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "required": true, "description": "REQUIRED: The resource for which the policy detail is being requested. See the operation documentation for the appropriate value for this field."}}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "id": "bigquery.tables.testIamPermissions", "description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "request": {"$ref": "TestIamPermissionsRequest"}, "parameterOrder": ["resource"]}}}, "datasets": {"methods": {"update": {"httpMethod": "PUT", "id": "bigquery.datasets.update", "parameters": {"datasetId": {"required": true, "description": "Dataset ID of the dataset being updated", "type": "string", "location": "path"}, "projectId": {"type": "string", "location": "path", "description": "Project ID of the dataset being updated", "required": true}}, "response": {"$ref": "Dataset"}, "request": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "path": "projects/{projectId}/datasets/{datasetId}", "parameterOrder": ["projectId", "datasetId"], "description": "Updates information in an existing dataset. The update method replaces the entire dataset resource, whereas the patch method only replaces fields that are provided in the submitted dataset resource."}, "list": {"id": "bigquery.datasets.list", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "response": {"$ref": "DatasetList"}, "parameters": {"projectId": {"location": "path", "required": true, "description": "Project ID of the datasets to be listed", "type": "string"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results", "location": "query", "type": "string"}, "filter": {"type": "string", "description": "An expression for filtering the results of the request by label. The syntax is \"labels.<name>[:<value>]\". Multiple filters can be ANDed together by connecting with a space. Example: \"labels.department:receiving labels.active\". See Filtering datasets using labels for details.", "location": "query"}, "maxResults": {"type": "integer", "location": "query", "format": "uint32", "description": "The maximum number of results to return"}, "all": {"description": "Whether to list all datasets, including hidden ones", "type": "boolean", "location": "query"}}, "parameterOrder": ["projectId"], "description": "Lists all datasets in the specified project to which you have been granted the READER dataset role.", "httpMethod": "GET", "path": "projects/{projectId}/datasets"}, "insert": {"path": "projects/{projectId}/datasets", "parameterOrder": ["projectId"], "httpMethod": "POST", "parameters": {"projectId": {"description": "Project ID of the new dataset", "location": "path", "type": "string", "required": true}}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "id": "bigquery.datasets.insert", "request": {"$ref": "Dataset"}, "response": {"$ref": "Dataset"}, "description": "Creates a new empty dataset."}, "patch": {"id": "bigquery.datasets.patch", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "parameters": {"projectId": {"required": true, "location": "path", "description": "Project ID of the dataset being updated", "type": "string"}, "datasetId": {"required": true, "type": "string", "location": "path", "description": "Dataset ID of the dataset being updated"}}, "response": {"$ref": "Dataset"}, "parameterOrder": ["projectId", "datasetId"], "httpMethod": "PATCH", "description": "Updates information in an existing dataset. The update method replaces the entire dataset resource, whereas the patch method only replaces fields that are provided in the submitted dataset resource. This method supports patch semantics.", "request": {"$ref": "Dataset"}, "path": "projects/{projectId}/datasets/{datasetId}"}, "delete": {"id": "bigquery.datasets.delete", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "parameterOrder": ["projectId", "datasetId"], "parameters": {"deleteContents": {"location": "query", "description": "If True, delete all the tables in the dataset. If False and the dataset contains tables, the request will fail. Default is False", "type": "boolean"}, "projectId": {"required": true, "description": "Project ID of the dataset being deleted", "type": "string", "location": "path"}, "datasetId": {"location": "path", "required": true, "type": "string", "description": "Dataset ID of dataset being deleted"}}, "path": "projects/{projectId}/datasets/{datasetId}", "httpMethod": "DELETE", "description": "Deletes the dataset specified by the datasetId value. Before you can delete a dataset, you must delete all its tables, either manually or by specifying deleteContents. Immediately after deletion, you can create another dataset with the same name."}, "get": {"response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "id": "bigquery.datasets.get", "description": "Returns the dataset specified by datasetID.", "path": "projects/{projectId}/datasets/{datasetId}", "parameterOrder": ["projectId", "datasetId"], "httpMethod": "GET", "parameters": {"datasetId": {"description": "Dataset ID of the requested dataset", "location": "path", "type": "string", "required": true}, "projectId": {"description": "Project ID of the requested dataset", "location": "path", "required": true, "type": "string"}}}}}, "jobs": {"methods": {"list": {"description": "Lists all jobs that you started in the specified project. Job information is available for a six month period after creation. The job list is sorted in reverse chronological order, by job creation time. Requires the Can View project role, or the Is Owner project role if you set the allUsers property.", "httpMethod": "GET", "parameterOrder": ["projectId"], "path": "projects/{projectId}/jobs", "id": "bigquery.jobs.list", "parameters": {"projection": {"location": "query", "type": "string", "enum": ["full", "minimal"], "enumDescriptions": ["Includes all job data", "Does not include the job configuration"], "description": "Restrict information returned to a set of selected fields"}, "allUsers": {"description": "Whether to display jobs owned by all users in the project. Default false", "location": "query", "type": "boolean"}, "maxResults": {"description": "Maximum number of results to return", "format": "uint32", "type": "integer", "location": "query"}, "parentJobId": {"location": "query", "description": "If set, retrieves only jobs whose parent is this job. Otherwise, retrieves only jobs which have no parent", "type": "string"}, "minCreationTime": {"type": "string", "format": "uint64", "location": "query", "description": "Min value for job creation time, in milliseconds since the POSIX epoch. If set, only jobs created after or at this timestamp are returned"}, "projectId": {"type": "string", "required": true, "location": "path", "description": "Project ID of the jobs to list"}, "pageToken": {"location": "query", "description": "Page token, returned by a previous call, to request the next page of results", "type": "string"}, "stateFilter": {"location": "query", "description": "Filter for job state", "enum": ["done", "pending", "running"], "enumDescriptions": ["Finished jobs", "Pending jobs", "Running jobs"], "repeated": true, "type": "string"}, "maxCreationTime": {"type": "string", "format": "uint64", "location": "query", "description": "Max value for job creation time, in milliseconds since the POSIX epoch. If set, only jobs created before or at this timestamp are returned"}}, "response": {"$ref": "JobList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "cancel": {"httpMethod": "POST", "parameterOrder": ["projectId", "jobId"], "id": "bigquery.jobs.cancel", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "response": {"$ref": "JobCancelResponse"}, "path": "projects/{projectId}/jobs/{jobId}/cancel", "parameters": {"projectId": {"location": "path", "type": "string", "description": "[Required] Project ID of the job to cancel", "required": true}, "jobId": {"type": "string", "description": "[Required] Job ID of the job to cancel", "required": true, "location": "path"}, "location": {"description": "The geographic location of the job. Required except for US and EU. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "type": "string", "location": "query"}}, "description": "Requests that a job be cancelled. This call will return immediately, and the client will need to poll for the job status to see if the cancel completed successfully. Cancelled jobs may still incur costs."}, "insert": {"scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"], "supportsMediaUpload": true, "description": "Starts a new asynchronous job. Requires the Can View project role.", "httpMethod": "POST", "request": {"$ref": "Job"}, "id": "bigquery.jobs.insert", "mediaUpload": {"protocols": {"simple": {"multipart": true, "path": "/upload/bigquery/v2/projects/{projectId}/jobs"}, "resumable": {"path": "/resumable/upload/bigquery/v2/projects/{projectId}/jobs", "multipart": true}}, "accept": ["*/*"]}, "response": {"$ref": "Job"}, "parameters": {"projectId": {"location": "path", "description": "Project ID of the project that will be billed for the job", "type": "string", "required": true}}, "path": "projects/{projectId}/jobs", "parameterOrder": ["projectId"]}, "get": {"description": "Returns information about a specific job. Job information is available for a six month period after creation. Requires that you're the person who ran the job, or have the Is Owner project role.", "path": "projects/{projectId}/jobs/{jobId}", "parameters": {"jobId": {"description": "[Required] Job ID of the requested job", "location": "path", "type": "string", "required": true}, "location": {"description": "The geographic location of the job. Required except for US and EU. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "type": "string", "location": "query"}, "projectId": {"required": true, "description": "[Required] Project ID of the requested job", "location": "path", "type": "string"}}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "response": {"$ref": "Job"}, "id": "bigquery.jobs.get", "parameterOrder": ["projectId", "jobId"], "httpMethod": "GET"}, "getQueryResults": {"parameters": {"pageToken": {"description": "Page token, returned by a previous call, to request the next page of results", "location": "query", "type": "string"}, "timeoutMs": {"type": "integer", "location": "query", "description": "How long to wait for the query to complete, in milliseconds, before returning. Default is 10 seconds. If the timeout passes before the job completes, the 'jobComplete' field in the response will be false", "format": "uint32"}, "location": {"location": "query", "type": "string", "description": "The geographic location where the job should run. Required except for US and EU. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location."}, "projectId": {"location": "path", "description": "[Required] Project ID of the query job", "required": true, "type": "string"}, "startIndex": {"type": "string", "description": "Zero-based index of the starting row", "location": "query", "format": "uint64"}, "jobId": {"description": "[Required] Job ID of the query job", "type": "string", "required": true, "location": "path"}, "maxResults": {"description": "Maximum number of results to read", "format": "uint32", "location": "query", "type": "integer"}}, "parameterOrder": ["projectId", "jobId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "path": "projects/{projectId}/queries/{jobId}", "description": "Retrieves the results of a query job.", "response": {"$ref": "GetQueryResultsResponse"}, "httpMethod": "GET", "id": "bigquery.jobs.getQueryResults"}, "query": {"request": {"$ref": "QueryRequest"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "id": "bigquery.jobs.query", "path": "projects/{projectId}/queries", "parameterOrder": ["projectId"], "parameters": {"projectId": {"required": true, "location": "path", "description": "Project ID of the project billed for the query", "type": "string"}}, "httpMethod": "POST", "description": "Runs a BigQuery SQL query synchronously and returns query results if the query completes within a specified timeout.", "response": {"$ref": "QueryResponse"}}}}, "projects": {"methods": {"list": {"httpMethod": "GET", "response": {"$ref": "ProjectList"}, "path": "projects", "id": "bigquery.projects.list", "description": "Lists all projects to which you have been granted any project role.", "parameters": {"maxResults": {"location": "query", "description": "Maximum number of results to return", "format": "uint32", "type": "integer"}, "pageToken": {"location": "query", "description": "Page token, returned by a previous call, to request the next page of results", "type": "string"}}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "getServiceAccount": {"parameterOrder": ["projectId"], "httpMethod": "GET", "id": "bigquery.projects.getServiceAccount", "path": "projects/{projectId}/serviceAccount", "response": {"$ref": "GetServiceAccountResponse"}, "parameters": {"projectId": {"description": "Project ID for which the service account is requested.", "type": "string", "required": true, "location": "path"}}, "description": "Returns the email address of the service account for your project used for interactions with Google Cloud KMS.", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}, "rowAccessPolicies": {"methods": {"setIamPolicy": {"parameters": {"resource": {"pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+/rowAccessPolicies/[^/]+$", "location": "path", "required": true, "description": "REQUIRED: The resource for which the policy is being specified. See the operation documentation for the appropriate value for this field.", "type": "string"}}, "httpMethod": "POST", "description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "id": "bigquery.rowAccessPolicies.setIamPolicy", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "request": {"$ref": "SetIamPolicyRequest"}, "path": "{+resource}:setIamPolicy", "response": {"$ref": "Policy"}, "parameterOrder": ["resource"], "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:setIamPolicy"}, "testIamPermissions": {"parameters": {"resource": {"location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+/rowAccessPolicies/[^/]+$", "description": "REQUIRED: The resource for which the policy detail is being requested. See the operation documentation for the appropriate value for this field.", "required": true, "type": "string"}}, "id": "bigquery.rowAccessPolicies.testIamPermissions", "path": "{+resource}:testIamPermissions", "description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "response": {"$ref": "TestIamPermissionsResponse"}, "parameterOrder": ["resource"], "httpMethod": "POST", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}}, "getIamPolicy": {"response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "parameterOrder": ["resource"], "id": "bigquery.rowAccessPolicies.getIamPolicy", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:getIamPolicy", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See the operation documentation for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+/rowAccessPolicies/[^/]+$", "location": "path", "type": "string", "required": true}}, "description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "httpMethod": "POST", "path": "{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}}, "list": {"path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies", "parameters": {"pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "type": "string", "location": "query"}, "pageSize": {"type": "integer", "format": "int32", "location": "query", "description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection."}, "tableId": {"description": "Required. Table ID of the table to list row access policies.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the row access policies to list.", "required": true, "pattern": "^[^/]+$", "location": "path", "type": "string"}, "datasetId": {"required": true, "pattern": "^[^/]+$", "description": "Required. Dataset ID of row access policies to list.", "location": "path", "type": "string"}}, "id": "bigquery.rowAccessPolicies.list", "response": {"$ref": "ListRowAccessPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "parameterOrder": ["projectId", "datasetId", "tableId"], "httpMethod": "GET", "description": "Lists all row access policies on the specified table.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies"}}}, "tabledata": {"methods": {"list": {"id": "bigquery.tabledata.list", "parameterOrder": ["projectId", "datasetId", "tableId"], "response": {"$ref": "TableDataList"}, "description": "Retrieves table data from a specified set of rows. Requires the READER dataset role.", "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}/data", "parameters": {"selectedFields": {"description": "List of fields to return (comma-separated). If unspecified, all fields are returned", "type": "string", "location": "query"}, "pageToken": {"description": "Page token, returned by a previous call, identifying the result set", "location": "query", "type": "string"}, "maxResults": {"location": "query", "type": "integer", "format": "uint32", "description": "Maximum number of results to return"}, "tableId": {"description": "Table ID of the table to read", "location": "path", "type": "string", "required": true}, "datasetId": {"type": "string", "location": "path", "required": true, "description": "Dataset ID of the table to read"}, "startIndex": {"description": "Zero-based index of the starting row to read", "format": "uint64", "type": "string", "location": "query"}, "projectId": {"location": "path", "required": true, "description": "Project ID of the table to read", "type": "string"}}}, "insertAll": {"description": "Streams data into BigQuery one record at a time without needing to run a load job. Requires the WRITER dataset role.", "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}/insertAll", "httpMethod": "POST", "parameterOrder": ["projectId", "datasetId", "tableId"], "id": "bigquery.tabledata.insertAll", "request": {"$ref": "TableDataInsertAllRequest"}, "parameters": {"datasetId": {"location": "path", "description": "Dataset ID of the destination table.", "type": "string", "required": true}, "projectId": {"description": "Project ID of the destination table.", "type": "string", "location": "path", "required": true}, "tableId": {"type": "string", "description": "Table ID of the destination table.", "location": "path", "required": true}}, "response": {"$ref": "TableDataInsertAllResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.insertdata", "https://www.googleapis.com/auth/cloud-platform"]}}}, "routines": {"methods": {"update": {"description": "Updates information in an existing routine. The update method replaces the entire Routine resource.", "parameters": {"projectId": {"location": "path", "required": true, "pattern": "^[^/]+$", "type": "string", "description": "Required. Project ID of the routine to update"}, "routineId": {"location": "path", "pattern": "^[^/]+$", "description": "Required. Routine ID of the routine to update", "required": true, "type": "string"}, "datasetId": {"pattern": "^[^/]+$", "location": "path", "type": "string", "description": "Required. Dataset ID of the routine to update", "required": true}}, "httpMethod": "PUT", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "request": {"$ref": "Routine"}, "id": "bigquery.routines.update", "response": {"$ref": "Routine"}, "path": "projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "parameterOrder": ["projectId", "datasetId", "routineId"]}, "insert": {"description": "Creates a new routine in the dataset.", "request": {"$ref": "Routine"}, "id": "bigquery.routines.insert", "parameterOrder": ["projectId", "datasetId"], "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines", "path": "projects/{+projectId}/datasets/{+datasetId}/routines", "response": {"$ref": "Routine"}, "httpMethod": "POST", "parameters": {"projectId": {"location": "path", "description": "Required. Project ID of the new routine", "required": true, "type": "string", "pattern": "^[^/]+$"}, "datasetId": {"required": true, "pattern": "^[^/]+$", "location": "path", "description": "Required. Dataset ID of the new routine", "type": "string"}}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "httpMethod": "GET", "parameterOrder": ["projectId", "datasetId", "routineId"], "response": {"$ref": "Routine"}, "path": "projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "id": "bigquery.routines.get", "description": "Gets the specified routine resource by routine ID.", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "parameters": {"routineId": {"description": "Required. Routine ID of the requested routine", "type": "string", "location": "path", "pattern": "^[^/]+$", "required": true}, "projectId": {"description": "Required. Project ID of the requested routine", "type": "string", "pattern": "^[^/]+$", "required": true, "location": "path"}, "readMask": {"format": "google-fieldmask", "description": "If set, only the Routine fields in the field mask are returned in the response. If unset, all Routine fields are returned.", "location": "query", "type": "string"}, "datasetId": {"pattern": "^[^/]+$", "location": "path", "required": true, "type": "string", "description": "Required. Dataset ID of the requested routine"}}}, "list": {"flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines", "path": "projects/{+projectId}/datasets/{+datasetId}/routines", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "id": "bigquery.routines.list", "httpMethod": "GET", "parameters": {"filter": {"type": "string", "description": "If set, then only the Routines matching this filter are returned. The current supported form is either \"routine_type:\" or \"routineType:\", where is a RoutineType enum. Example: \"routineType:SCALAR_FUNCTION\".", "location": "query"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results", "location": "query", "type": "string"}, "projectId": {"location": "path", "description": "Required. Project ID of the routines to list", "type": "string", "pattern": "^[^/]+$", "required": true}, "readMask": {"location": "query", "description": "If set, then only the Routine fields in the field mask, as well as project_id, dataset_id and routine_id, are returned in the response. If unset, then the following Routine fields are returned: etag, project_id, dataset_id, routine_id, routine_type, creation_time, last_modified_time, and language.", "format": "google-fieldmask", "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "type": "integer", "format": "uint32", "location": "query"}, "datasetId": {"description": "Required. Dataset ID of the routines to list", "type": "string", "location": "path", "pattern": "^[^/]+$", "required": true}}, "parameterOrder": ["projectId", "datasetId"], "description": "Lists all routines in the specified dataset. Requires the READER dataset role.", "response": {"$ref": "ListRoutinesResponse"}}, "delete": {"description": "Deletes the routine specified by routineId from the dataset.", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "httpMethod": "DELETE", "parameters": {"datasetId": {"required": true, "description": "Required. Dataset ID of the routine to delete", "location": "path", "pattern": "^[^/]+$", "type": "string"}, "projectId": {"pattern": "^[^/]+$", "location": "path", "type": "string", "required": true, "description": "Required. Project ID of the routine to delete"}, "routineId": {"type": "string", "pattern": "^[^/]+$", "description": "Required. Routine ID of the routine to delete", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId", "routineId"], "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "id": "bigquery.routines.delete", "path": "projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}"}}}, "models": {"methods": {"delete": {"description": "Deletes the model specified by modelId from the dataset.", "parameterOrder": ["projectId", "datasetId", "modelId"], "path": "projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "id": "bigquery.models.delete", "parameters": {"modelId": {"description": "Required. Model ID of the model to delete.", "type": "string", "pattern": "^[^/]+$", "required": true, "location": "path"}, "datasetId": {"location": "path", "required": true, "description": "Required. Dataset ID of the model to delete.", "type": "string", "pattern": "^[^/]+$"}, "projectId": {"pattern": "^[^/]+$", "required": true, "type": "string", "description": "Required. Project ID of the model to delete.", "location": "path"}}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "httpMethod": "DELETE"}, "list": {"flatPath": "projects/{projectsId}/datasets/{datasetsId}/models", "response": {"$ref": "ListModelsResponse"}, "parameterOrder": ["projectId", "datasetId"], "parameters": {"datasetId": {"type": "string", "location": "path", "description": "Required. Dataset ID of the models to list.", "required": true, "pattern": "^[^/]+$"}, "maxResults": {"type": "integer", "format": "uint32", "location": "query", "description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection."}, "projectId": {"required": true, "type": "string", "pattern": "^[^/]+$", "description": "Required. Project ID of the models to list.", "location": "path"}, "pageToken": {"location": "query", "type": "string", "description": "Page token, returned by a previous call to request the next page of results"}}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "id": "bigquery.models.list", "httpMethod": "GET", "path": "projects/{+projectId}/datasets/{+datasetId}/models", "description": "Lists all models in the specified dataset. Requires the READER dataset role."}, "patch": {"response": {"$ref": "Model"}, "path": "projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "description": "Patch specific fields in the specified model.", "parameterOrder": ["projectId", "datasetId", "modelId"], "httpMethod": "PATCH", "id": "bigquery.models.patch", "parameters": {"datasetId": {"required": true, "pattern": "^[^/]+$", "type": "string", "description": "Required. Dataset ID of the model to patch.", "location": "path"}, "projectId": {"location": "path", "pattern": "^[^/]+$", "type": "string", "description": "Required. Project ID of the model to patch.", "required": true}, "modelId": {"description": "Required. Model ID of the model to patch.", "required": true, "type": "string", "location": "path", "pattern": "^[^/]+$"}}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "request": {"$ref": "Model"}}, "get": {"flatPath": "projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "httpMethod": "GET", "parameters": {"datasetId": {"pattern": "^[^/]+$", "required": true, "type": "string", "location": "path", "description": "Required. Dataset ID of the requested model."}, "projectId": {"description": "Required. Project ID of the requested model.", "required": true, "location": "path", "type": "string", "pattern": "^[^/]+$"}, "modelId": {"required": true, "pattern": "^[^/]+$", "description": "Required. Model ID of the requested model.", "location": "path", "type": "string"}}, "id": "bigquery.models.get", "path": "projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "description": "Gets the specified model resource by model ID.", "parameterOrder": ["projectId", "datasetId", "modelId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.readonly", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "response": {"$ref": "Model"}}}}}, "id": "bigquery:v2", "parameters": {"userIp": {"description": "Deprecated. Please use quotaUser instead.", "location": "query", "type": "string"}, "oauth_token": {"type": "string", "description": "OAuth 2.0 token for the current user.", "location": "query"}, "prettyPrint": {"location": "query", "default": "true", "description": "Returns response with indentations and line breaks.", "type": "boolean"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "type": "string", "location": "query"}, "alt": {"location": "query", "type": "string", "enum": ["json"], "default": "json", "enumDescriptions": ["Responses with Content-Type of application/json"], "description": "Data format for the response."}, "quotaUser": {"type": "string", "description": "An opaque string that represents a user for quota purposes. Must not exceed 40 characters.", "location": "query"}, "key": {"location": "query", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "type": "string"}}, "baseUrl": "https://bigquery.googleapis.com/bigquery/v2/", "mtlsRootUrl": "https://bigquery.mtls.googleapis.com/", "protocol": "rest", "name": "big<PERSON>y", "title": "BigQuery API", "rootUrl": "https://bigquery.googleapis.com/", "basePath": "/bigquery/v2/", "version": "v2"}