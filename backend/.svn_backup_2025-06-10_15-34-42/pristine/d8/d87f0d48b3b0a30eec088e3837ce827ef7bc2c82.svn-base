<?php
/**
 * \defgroup Bl Bon de Livraison 
 * \ingroup oms 
 * @{	 
*/
	switch( $method ){
		/** @{@}
 		 * @{	
		 * \page api-bl-get Chargement
		 *
		 * Cette fonction récupére un bon de livraison
		 *
		 *		\code
		 *			GET /bl/
		 *		\endcode
		 *
		 *	@param id obligatoire, identifiant du bon de livraison
		 *
		 * 	@return JSON sous la forme suivante : 
		 *	\code{.json}
		 *		{
		 *		"id": identifiant du bon de livraison,
		 *		"piece": référence du bon de livraison dans la gestion commerciale,
		 *		"ref": référence du bon de livraison,
		 *		"date": date format fr,
		 *		"date_en": date format en,
		 *		"usr_id": identifiant de l'utilisateur,
		 *		"total_ht": prix hors taxes,
		 *		"total_ttc": prix toutes taxes comprises,
		 *		"state_id": identifiant du statut,
		 *		"state_name": nom du statut,
		 *		"srv_id": identifiant du service de livraison,
		 *		"adr_dlv_id": identifiant de l'adresse de livraison,
		 *		"adr_invoices": identifiant de l'adresse de facturation,
		 *		"products": nombre de produits dans le bon de livraison,
		 *		"str_id": identifiant du magasin,
		 *		"pkg_id": identifiant du packaging,
		 *		"pay_id": identifiant du moyen de payement,
		 *		"pmt_id": identifiant de la promotion,
		 *		"dps_id": identifiant du dépot de stockage,
		 *		"wst_id": identifiant du site,
		 *		"reseller_id": identifiant du revendeur,
		 *		"reseller_contact_id": identifiant du contact du revendeur,
		 *		"contact_id": identifiant du conctact qui a passé le bon de livraison,
		 *		"seller_id": identifiant du commercial,
		 *		"date_livr": date de livraison,
		 *		"dlv_notes": consigne de livraison,
		 *		"comments": commentaires,
		 *		"age": nombre de jour depuis la création du bon de livraison,
		 *		"date_modified": date de modification format fr,
		 *		"date_modified_en": date de modification format en,
		 *		"need_sync": détermine si le bon de livraison va être synchronisé
		 *		}	
		 * 	\endcode
		 * @}
		*/
		case 'get':

			if( !isset($_REQUEST['id']) ){
				throw new Exception( "Paramètre invalide.");
			}

			$rbl = ord_bl_get($_REQUEST['id'] );
			if( $rbl && ria_mysql_num_rows($rbl) ){
				$result = true;
				$content = ria_mysql_fetch_assoc($rbl);
			}
			break;


		/**
 		 *	@{	
		 * 	\page api-bl-del Suppression 
		 *
		 *	Cette fonction permet la suppression d'un bon de livraison	
		 *	
		 * 	\code		
		 * 		DELETE /bl/
		 * 	\endcode
		 *
		 *	@param raw_data Obligatoire : Liste d'objet JSON sous la forme suivante :
		 *	\code
		 *		{
		 *			"bl_id": Identifiant du bon de livraison
		 *		}
		 *	\endcode
		 *	
		 *	@return true si le bon est bien supprimé sinon indique l'erreur 
		 * @}
		*/		
		case 'del':
			if( !is_array($objs) ){
				throw new Exception("Paramètres invalide");
			}

			foreach($objs as $bl){
				if(!isset($bl["bl_id"])){
					throw new Exception("Paramètres invalide");
				}
				if(!ord_bl_del($bl['bl_id'])){
					throw new Exception("La suppression du bl n°:".$bl['bl_id']." a échoué");
				}
			}
			$result = true;
			break;
	}
///@}