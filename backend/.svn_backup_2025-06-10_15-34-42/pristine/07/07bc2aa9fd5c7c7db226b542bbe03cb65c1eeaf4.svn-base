<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/distribution.proto

namespace Google\Api;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Distribution\BucketOptions\Exponential instead.
     * @deprecated
     */
    class Distribution_BucketOptions_Exponential {}
}
class_exists(Distribution\BucketOptions\Exponential::class);
@trigger_error('Google\Api\Distribution_BucketOptions_Exponential is deprecated and will be removed in the next major release. Use Google\Api\Distribution\BucketOptions\Exponential instead', E_USER_DEPRECATED);

