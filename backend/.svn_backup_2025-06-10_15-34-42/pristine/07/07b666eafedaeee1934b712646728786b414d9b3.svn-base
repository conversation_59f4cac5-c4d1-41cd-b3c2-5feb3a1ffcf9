<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/client.proto

namespace GPBMetadata\Google\Api;

class Client
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0aba010a17676f6f676c652f6170692f636c69656e742e70726f746f120a" .
            "676f6f676c652e6170691a20676f6f676c652f70726f746f6275662f6465" .
            "7363726970746f722e70726f746f42690a0e636f6d2e676f6f676c652e61" .
            "7069420b436c69656e7450726f746f50015a41676f6f676c652e676f6c61" .
            "6e672e6f72672f67656e70726f746f2f676f6f676c65617069732f617069" .
            "2f616e6e6f746174696f6e733b616e6e6f746174696f6e73a20204474150" .
            "49620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

