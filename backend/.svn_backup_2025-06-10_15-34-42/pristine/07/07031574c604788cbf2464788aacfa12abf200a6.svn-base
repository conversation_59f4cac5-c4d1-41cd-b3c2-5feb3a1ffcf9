<?php

use Riashop\PriceWatching\models\LinearRaised\prw_followed_users;

/**
 * @group followedUsersTest
 * @backupGlobals disabled
 */
class followedUsersCRUDTest extends PHPUnit_Framework_TestCase {
	private $id_counts = array(
		1 => 5,
		2 => 4,
	);
	/**
	 * @dataProvider validFollowedUsersProvider
	 */
	public function testAddFollowedUsers($id, $prf_id, $usr_id) {
		$result = prw_followed_users::add($id, $prf_id, $usr_id);
		$this->assertTrue($result, "Erreur lors de l'ajout");
	}

	/**
	 * @dataProvider invalidFollowedUsersProvider
	 */
	public function testFAilAddFollowedUsers($id, $prf_id, $usr_id) {
		$this->setExpectedException('InvalidArgumentException');
		$result = prw_followed_users::add($id, $prf_id, $usr_id);
	}
	/**
	 * @dataProvider validPflIds
	 */
	public function testGetFollowedUsers($id) {
		$result = prw_followed_users::get($id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat ne dois pas être vide");
		$expected_count = $this->id_counts[$id];
		$this->assertEquals($expected_count, ria_mysql_num_rows($result),  "Erreur il devrais avoir ".$expected_count." ligne");

		$user = ria_mysql_fetch_assoc($result);
		$this->assertArrayHasKey('pfl_id', $user, "Error il manque la clé pfl_id");
		$this->assertArrayHasKey('id', $user, "Error il manque la clé id");
		$this->assertArrayHasKey('prf_id', $user, "Error il manque la clé prf_id");
		$this->assertArrayHasKey('usr_id', $user, "Error il manque la clé usr_id");
		$this->assertArrayHasKey('date_created', $user, "Error il manque la clé date_created");
		$this->assertArrayHasKey('date_modified', $user, "Error il manque la clé date_modified");
	}
	/**
	 * @dataProvider validPflIds
	 */
	public function testGetOnlyManagersFollowedUsers($id) {
		$result = prw_followed_users::getOnlyManagers($id);
		$this->assertTrue(ria_mysql_control_ressource($result), "Erreur le résultat ne dois pas être vide");
		$expected_count = 1;
		$this->assertEquals($expected_count, ria_mysql_num_rows($result), "Erreur il devrais avoir ".$expected_count." ligne");

		$user = ria_mysql_fetch_assoc($result);
		$this->assertArrayHasKey('pfl_id', $user, "Error il manque la clé pfl_id");
		$this->assertArrayHasKey('prf_id', $user, "Error il manque la clé prf_id");
		$this->assertArrayHasKey('usr_id', $user, "Error il manque la clé usr_id");
		$this->assertArrayHasKey('date_created', $user, "Error il manque la clé date_created");
		$this->assertArrayHasKey('date_modified', $user, "Error il manque la clé date_modified");
	}
	/**
	 * @dataProvider validPflIds
	 */
	public function testDeleteFollowedUsers($id) {
		$result = prw_followed_users::delete($id);
		$this->assertTrue($result, "Erreur de suppression de client");
		$result = prw_followed_users::get($id);
		$this->assertEmpty($result, "Erreur le devrais être vide du a la suppression");
	}

	public function validFollowedUsersProvider() {
		return array(
			array('id' => 1, 'prf_id' => 3, 'usr_id' => 34),
			array('id' => 1, 'prf_id' => 2, 'usr_id' => 35),
			array('id' => 1, 'prf_id' => 3, 'usr_id' => 36),
			array('id' => 1, 'prf_id' => 5, 'usr_id' => 37),
			array('id' => 1, 'prf_id' => 3, 'usr_id' => 38),
			array('id' => 2, 'prf_id' => 3, 'usr_id' => 39),
			array('id' => 2, 'prf_id' => 3, 'usr_id' => 40),
			array('id' => 2, 'prf_id' => 1, 'usr_id' => 41),
			array('id' => 2, 'prf_id' => 3, 'usr_id' => 42),
		);
	}

	public function invalidFollowedUsersProvider() {
		return array(
			array('id' => 0, 'prf_id' => 3, 'usr_id' => 34),
			array('id' => 1, 'prf_id' => 0, 'usr_id' => 35),
			array('id' => 1, 'prf_id' => 3, 'usr_id' => 'test'),
			array('id' => 'test', 'prf_id' => 5, 'usr_id' => null),
		);
	}

	public function validPflIds() {
		return array(
			array(1),
			array(2),
		);
	}
}