<?php

class PostsService
{
	/**	Identifiant ou tableau d'identifiants de catégorie.s
	 * @var int|array
	 */
	private $cat = 0;

	/**	Permet de récupérer uniquement les identifiants des articles du blog
	 * @var	bool
	 */
	private $only_ids = false;

	/**	Permet de préciser à partir de quel curseur les articles de blog devront être récupères
	 * @var	int
	 */
	private $offset = 0;

	/**	Nombre d'articles de blog qui seront récupérés (par défaut $config['prd_list_length'])
	 * Défaut : $config['prd_list_length']
	 * @var	bool|int
	 */
	private $limit = false;

	/**	Tri à appliquer (valeurs acceptées : 'date|asc', 'date|desc', 'alpha|asc', 'alpha|desc')
	 * Défaut : date|desc
	 * @var	bool|string
	 */
	private $sort = false;

	/**	Articles du blog
	 * @var	null|array
	 */
	private $posts = null;

	/**	Nombre total d'articles du blog récupérés
	 * @var	int
	 */
	private $count = 0;

	/**	Constructeur de la classe
	 * @param	int|array	$cat	Optionnel, Identifiant ou tableaux d'identifiants de catégorie.s du blog
	 * @return	void
	 */
	public function __construct($cat = 0)
	{

		if (is_array($cat) && count($cat) > 0) {
			$ar_cat = [];
			foreach ($cat as $id) {
				if (!is_numeric($id) || $id <= 0) {
					continue;
				}
				$ar_cat[] = $id;
			}

			if (count($ar_cat) > 0) {
				$this->cat = $ar_cat;
			}
		} elseif (is_numeric($cat) && $cat > 0) {
			$this->cat = $cat;
		}
	}

	/**	Met à jour le paramètre permettant la récupération des identifiants des articles du blog
	 * @param	bool		$only_ids	Obligatoire, True pour récupérer les identifiants uniquement
	 * @return	object		L'instance en cours
	 */
	public function setOnlyIds($only_ids)
	{
		$this->only_ids = is_bool($only_ids) ? $only_ids : false;

		return $this;
	}

	/**	Met à jour le paramètre du curseur
	 * @param	int		$offset	Obligatoire, offset à appliquer
	 * @return	object	L'instance en cours
	 */
	public function setOffset($offset)
	{
		$this->offset = is_numeric($offset) && $offset > 0 ? (int)$offset : 0;

		return $this;
	}

	/**	Met à jour le paramètre de limite d'articles à récupérer
	 * @param	bool|int	$limit	Obligatoire, Limite à appliquer
	 * @return	object		L'instance en cours
	 */
	public function setLimit($limit)
	{
		$this->limit = is_numeric($limit) && $limit > 0 ? (int)$limit : false;

		return $this;
	}

	/**	Met à jour le paramètre de tri (valeurs acceptées : 'date|asc', 'date|desc', 'alpha|asc', 'alpha|desc')
	 * @param	bool|string	$sort	Obligatoire, Tri à appliquer
	 * @return	object		L'instance en cours
	 */
	public function setSort($sort)
	{
		$this->sort = $sort;

		return $this;
	}

	/**	Retourne les articles du blog
	 * @param	bool	$force	Optionnel, True pour recharger les articles du blog
	 * @return	bool|array	Tableau contenant les articles du blog, false si aucun article n'a pu être récupéré
	 */
	public function getPosts($force = false)
	{
		$this->__loadPosts($force);

		return is_array($this->posts) && count($this->posts) > 0 ? $this->posts : false;
	}

	/**	Retourne le nombre total des articles du blog suivant les paramètres
	 * @return	int	Nombre total d'articles
	 */
	public function getCount()
	{
		return $this->count;
	}

	// public function getCount()
	// {
	// 	if ($this->count > 0) {
	// 		return $this->count;
	// 	}
	// 	$Posts = new PostsService($this->cat);
	// 	$Posts->setOnlyIds(true);
	// 	$Posts->getPosts();
	// 	return $this->count;
	// }

	/**	Permet le chargement des articles du blog
	 * @param	bool	$force	Optionnel, True pour recharger les articles du blog
	 * @return	object	L'instance en cours
	 */
	private function __loadPosts($force = false)
	{
		if (!is_null($this->posts) && is_bool($force) && !$force) {
			return $this;
		}
		global $config;

		$sql =
			'select
				n.news_id as id,
				n.news_name as name
			from
				news n

			inner join
				news_websites ns
			on
					n.news_id = ns.nw_news_id
				and ns.nw_wst_id = ' . $config['wst_id'] . '
				and ns.nw_tnt_id=' . $config['tnt_id'] . '
				and ns.nw_lng_code="' . i18n::getLang() . '"
			where
					n.news_tnt_id = ' . $config['tnt_id'] . '
				and (n.news_publish_date is not null and n.news_publish_date <= now() )
				and (n.news_publish_date_end is null or n.news_publish_date_end > now() )
		';


		if ($this->cat !== 0) {
			$ar_cat = $this->cat;

			if (is_numeric($this->cat)) {
				$ar_cat = [$this->cat];
			}
			$sql .= 'and n.news_cat_id in (' . implode(',', $ar_cat) . ')';
		}

		switch ($this->sort) {
			case 'title|asc':
				$sort = 'n.news_name asc';

				break;
			case 'title|desc':
				$sort = 'n.news_name desc';

				break;
			case 'date|asc':
				$sort = 'n.news_publish_date asc';
				break;

			case 'date|desc':
			default:
				$sort = 'n.news_publish_date desc';
		}

		$sql .= ' order by ' . $sort;

		$r_sql = ria_mysql_query($sql);
		$r_num = ria_mysql_num_rows($r_sql);
		$this->count = is_numeric($r_num) && $r_num > 0 ? $r_num : 0;

		if (!$r_num) {
			return $this;
		}

		if ($this->offset >= $this->count) {
			return $this;
		}
		ria_mysql_data_seek($r_sql, $this->offset);

		$i = 1;
		$this->posts = [];

		while ($post = ria_mysql_fetch_assoc($r_sql)) {
			if ($this->limit && ($i++) > $this->limit) {
				break;
			}
			$this->posts[] = $post['id'];
		}

		$sql_img = '
			select
				ni.ni_img_id
			from
				news_img ni
			where
					ni.ni_news_id = n.news_id
				and ni.ni_tnt_id =' . $config['tnt_id'] . '
			order by ni.ni_pos
			limit 0, 1
		';

		$sql =
			'select
				n.news_id as id,
				n.news_name as name,
				n.news_intro as intro,
				n.news_url_alias as url_alias,
				date_format(n.news_publish_date,"%d/%m/%Y") as publish_date,
				n.news_publish_date as publish_date_en,
				time_format(n.news_publish_date,"%H:%i") as hour,
				ifnull(n.news_img_id, (' . $sql_img . ')) as img_id,
				n.news_cat_id as cat_id,
				c.cat_name as cat_name

			from
				news n
			left join
				news_categories c
			on
					c.cat_tnt_id = ' . $config['tnt_id'] . '
				and c.cat_id = n.news_cat_id
				and c.cat_date_deleted is null
			where
					n.news_tnt_id = ' . $config['tnt_id'] . '
				and n.news_id in(' . implode(',', $this->posts) . ')
			order by ' . $sort;

		$r_sql = ria_mysql_query($sql);

		if (!ria_mysql_num_rows($r_sql)) {
			return $this;
		}
		$this->posts = [];

		while ($post = ria_mysql_fetch_assoc($r_sql)) {
			$post = i18n::getTranslation(CLS_NEWS, $post);

			$this->posts[] = [
				'news'		=> $post['id'],
				'name'		=> $post['name'],
				'intro'		=> $post['intro'],
				'date'		=> $post['publish_date_en'],
				'url'		=> $post['url_alias'],
				'catid'		=> $post['cat_id'],
				'catname'	=> $post['cat_name'],
				'images'	=> is_numeric($post['img_id']) && $post['img_id'] > 0 ? [$post['img_id']] : false
			];
		}
		return $this;
	}
}
