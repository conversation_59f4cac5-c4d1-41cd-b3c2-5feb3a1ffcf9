# OrderExportationReporting

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**abortion_utc_date** | [**\DateTime**](\DateTime.md) |  | [optional] 
**begin_utc_date** | [**\DateTime**](\DateTime.md) |  | [optional] 
**blob_name_uri** | **string** |  | [optional] 
**end_utc_date** | [**\DateTime**](\DateTime.md) |  | [optional] 
**enqueued_utc_date** | [**\DateTime**](\DateTime.md) |  | 
**error_message** | **string** |  | [optional] 
**execution_uuid** | **string** |  | 
**expiration_utc_date** | [**\DateTime**](\DateTime.md) |  | [optional] 
**failure_utc_date** | [**\DateTime**](\DateTime.md) |  | [optional] 
**ip_address** | **string** |  | 
**json_criteria** | **string** | Raw representation of the JSON | [optional] 
**last_update_utc_date** | [**\DateTime**](\DateTime.md) |  | [optional] 
**order_count** | **int** |  | [optional] 
**processing_status** | [**\Swagger\Client\Model\OrderExportationReportingProcessingStatus**](OrderExportationReportingProcessingStatus.md) |  | 
**remaining_order_count** | **int** |  | [optional] 
**resumed_utc_date** | [**\DateTime**](\DateTime.md) |  | [optional] 
**source_type** | [**\Swagger\Client\Model\SourceType**](SourceType.md) |  | 
**source_user_id** | [**\Swagger\Client\Model\BeezUPCommonUserId**](BeezUPCommonUserId.md) |  | [optional] 
**source_user_name** | **string** |  | 
**suspended_utc_date** | [**\DateTime**](\DateTime.md) |  | [optional] 
**timeout_duration** | **string** |  | [optional] 
**warning_message** | **string** |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


