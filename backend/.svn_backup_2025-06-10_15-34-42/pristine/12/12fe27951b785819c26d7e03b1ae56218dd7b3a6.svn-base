<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\DependencyInjection\Compiler;

/**
 * Interface that must be implemented by passes that are run as part of an
 * RepeatedPass.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface RepeatablePassInterface extends CompilerPassInterface
{
    public function setRepeatedPass(RepeatedPass $repeatedPass);
}
