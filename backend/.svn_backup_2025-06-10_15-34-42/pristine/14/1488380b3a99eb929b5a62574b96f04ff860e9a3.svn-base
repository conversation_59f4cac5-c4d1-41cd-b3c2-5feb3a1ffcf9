<?php

// \cond onlyria

/**
*\defgroup api-riashopsync-logs Logs DEPRECIER (envoi sur google)
*\ingroup sync
*@{
*	 \page api-riashopsync-logs-add Ajout
*
*	 cette fonction envoie les logs de synchro par e-mail
*
*		\code
*			POST /riashopsync/logs/
*		\endcode
*
*	 @param raw_data Obligatoire, ????
*/

switch( $method ){
    case 'add':

		$header = "MIME-Version: 1.0".PHP_EOL;
		$header .= "Content-Type: text/html; charset=UTF-8".PHP_EOL;

		$message = '<html>';
		$title = 'Tnt : '.tnt_tenants_get_name($config['tnt_id']).' - Gescom : '.$config['sync_global_gescom_type'].' - DataBase : '.$config['sync_sqlserver_gescom_db'].'- Cache : '.$config['sync_sqlserver_cache_db'];
		$message .= $title .'<br/><br/>';

		$final_array = array();
		$final_array["date"] = date('Y-m-d H:i:s');
		$final_array["tnt_id"] = $config['tnt_id'];
		$final_array["logtoken"] = $_REQUEST['logtoken'];
		$final_array["gescom"] = $config['sync_global_gescom_type'];
		$final_array["database"] = $config['sync_sqlserver_gescom_db'];
		$final_array["cache"] = $config['sync_sqlserver_cache_db'];

		$tmp = '';
		$objs = json_decode($raw_data, true);
		if( is_array($objs) ){
			foreach( $objs as $obj ){
				$color = 'black';
				if(isset($obj['gravity']) && $obj['gravity'] == 'error' ){
					$color = 'red';

					$final_array["log"] = $obj;
					api_log(json_encode($final_array), 'api-sync');
				}

				if( !isset($obj['processName']) ){
					$obj['processName'] = "";
				}
				if( !isset($obj['branch']) ){
					$obj['branch'] = "";
				}
				if( !isset($obj['date']) ){
					$obj['date'] = "";
				}
				if( !isset($obj['version']) ){
					$obj['version'] = "";
				}

				$tmp .= '<div style="color:'.$color.'; padding-bottom:20px;">';
				$tmp .= $obj['date'].' '.$obj['version'].' '.$obj['branch'].' '.$obj['processName'];
				$tmp .= '<div style="padding-left:30px">'.nl2br($obj['message']).'</div>';
				$tmp .= '</div>';
			}
			$message .= $tmp.'</body></html>';

			if( mail('<EMAIL>','Log : '.$config['tnt_id'].' - '.tnt_tenants_get_name($config['tnt_id']), $message, $header) ){
				$result = true;
			}
		}
		break;
}

///@}

// \endcond