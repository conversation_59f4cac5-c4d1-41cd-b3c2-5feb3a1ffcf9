<?php

	/**	\file json-documents.php
	 * 
	 * 	Ce fichier fourni une api json pour récupérer une liste de documents. Les paramètres à utiliser sont les suivants :
	 * 	- type : Optionnel, identifiant d'un type de document sur lequel filtrer le résultat
	 *  - p : Optionnel, page de résultat sur lequel filtrer le résultat. Ne fonctionnera que si le paramètre limit est renseigné.
	 *  - limit : Optionnel, nombre maximum de résultat à retourner. Par défaut, tous les résultats sont retournés.
	 * 
	 */

	// Vérifie que l'utilisateur en cours peut accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_DOCS');

	// Inclut les fonctions pour les documents
	require_once('documents.inc.php');
	
	// Détermine le filtre sur le type de documents
	$type = isset($_POST['type']) && doc_types_exists($_POST['type']) ? $_POST['type'] : 0;
	
	// Défini si la recherche fait partie d'une pagination
	$page = isset($_POST['p']) && is_numeric($_POST['p']) && $_POST['p']>0 ? $_POST['p'] : 1;
	
	// Détermine le nombre limite de document retourné
	$limit = isset($_POST['limit']) && is_numeric($_POST['limit']) && $_POST['limit']>0 ? $_POST['limit'] : 18446744073709551615;
	
	// Récupère les documents
	$rdoc = doc_documents_get( 0, $type, null, '', false );
	
	// Tableau contenant tous les documents
	$docs = array();
	
	if( $rdoc!=false && ria_mysql_num_rows($rdoc)>=($page-1)*$limit ){
		
		// On se place sur la position de départ de lecture des résultats
		if( $page>1 )
			ria_mysql_data_seek( $rdoc, ($page-1)*$limit );
		
		$count = 0;
		while( $doc = ria_mysql_fetch_array($rdoc) ){
			
			// Intégration d'un tableau contenant les informations sur un document
			$docs[] = array(
				'id'				=> $doc['id'],
				'filename'			=> $doc['filename'],
				'size'				=> $doc['size'],
				'name'				=> $doc['name'],
				'desc'				=> $doc['desc'],
				'date_created'		=> $doc['date_created'],
				'date_created_en'	=> $doc['date_created_en'],
				'type_id'			=> $doc['type_id'],
				'type_name'			=> $doc['type_name']
			);
			
			// Comptabilise le nombre de document ajouté dans la liste
			$count++;
			
			// Si le nombre de document retourné est fixé et atteint, on arrête la lecture du résultats
			if( $count>=$limit )
				break;
		}
	}
	
	// Encode le tableau sous format JSON
	print json_encode( $docs );
