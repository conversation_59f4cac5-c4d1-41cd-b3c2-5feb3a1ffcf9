var current_ajax = false;
var loaded = false;
$(document).ready(function(){
	titleReplace();	
	
	// Permet de cacher les champs pour le select usr_fld_id
	$('select[name=usr_fld_id]').change(function(){ 	
		$('.rec-lbl').hide();
		$('.rec-value').hide();
		$('.rec-action').hide();

		var element = '.value-'+$('option[value='+$(this).val()+']').attr('id'); 
		if( $(element).length > 0 ){
			$(element).show();
			$('.rec-lbl').show();
		} 

		var element = '.action-'+$('option[value='+$(this).val()+']').attr('id'); 
		if( $(element).length > 0 ){
			$(element).show();
		} 
	}).change();

	$('.value-rec-usr, #choose-rec-usr').click(function(){
		displayPopup( negociationsSelectClient, '', '/admin/ajax/orders/ncmd-customers-change.php?no-add=1&callback=authorizations_select_user' );
	});

	// permet de recharger les valeurs possible supérieur / inférieur / .. 
	$('.rec-new-fld').live('change',function(){
		
		if (!loaded) { 
			init_autocomplete();
			return;
		}

		if( current_ajax ) current_ajax.abort();
		var element = this;

		const fld_id		= $(element).val();
		const usr_id		= $(element).data('usr_id');
		const gtr_id		= $('input[name=gtr_id]').val();
		const price_fld_id	= $('input[name=price_fld_id]:checked').val();
		const price_type	= $('input[name=price_type]:checked').val();
		const price_value	= $('input[name=price_value]').val();

		current_ajax = $.get('/admin/customers/negotiations/new.php?fld_id='+fld_id+'&usr_id='+usr_id+'&gtr_id='+gtr_id+'&price_fld_id='+price_fld_id+'&price_type='+price_type+'&price_value='+price_value, function(retour){
			$(element).parents('tr').replaceWith( retour );
			init_autocomplete();
		});

	}).change();
	
	loaded = true;

	// autocomplétion pour la sélection de l'utilisateur
	$('input[name=usr_value_txt]').autocomplete({
		source: "/admin/ajax/search/search-class.php",
		minLength: 2,
		select: function( event, ui ) {	
			$(this).parents('tr').find('.val-autocomplete-usr').val(ui.item.id); 
		}
	});
});

function authorizations_select_user( id, email ){
	$('.value-rec-usr').val( email );
	$('[name=usr_id').val( id );
}

var input_id = false;
function init_autocomplete(){
	// init datepicker
	$('.datepicker').each(function(){
		var temp = this;
		$(this).DatePicker({
			format:'d/m/Y',
			date: $(this).val(),
			current: $(this).val(),
			starts: 1,
			onChange: function(formated, dates){
				if(dates != 'Invalid Date'){
					$(temp).val(formated);
					$(temp).DatePickerHide();
				}
			}
		});
	});
	// autocomplétion sur le type marques 
	$('.add-rule-prd').click(function(){
		input_id=$('.add-rule-prd').data('input');
		return displayPopup( negociationsSelectProduit, '', '/admin/ajax/catalog/ajax-product-select.php?show_search=1&publish_search=0&input_id_prd_id='+input_id ); 
	});
	$('.add-rule-cat').click(function(){ 
		input_id=$('.add-rule-cat').data('input');
		return displayPopup( negociationsSelectCategorie, '', '/admin/catalog/popup-categories.php?show_search=1&publish_search=0&input_id_cat_id='+input_id ); 
	});
	$('.add-rule-brd').click(function(){
		input_id=$('.add-rule-brd').data('input');
		return displayPopup( negociationsSelectMarque, '', '/admin/ajax/catalog/popup-brands-select.php?show_search=1&publish_search=0&input_id_brd_id='+input_id ); 
	});
	
	titleReplace();
}
function replace_element(element,retour){
	$('.error,.success').remove(); // supprime les précédents message d'erreur / succès
	
	if( retour.match('error') ){
		$('tr[data-id='+element.attr('data-id')+']:first').before( retour );
	}else{
		$('tr[data-id='+element.attr('data-id')+']').addClass('toremove');
		$(element).after( retour );
		$('tr.toremove').remove();
	}
}

/**
 *	Cette fonction permet de sélectionner le produit (retour de la popup).
 */
function parent_select_prd( id, name, ref, cat, input_id ){
	$('#'+input_id).val(id);
	$('input[name=name-'+input_id+']').val(ref);
}
function parent_select_brd( id, name ){ 
	$('#'+input_id).val(id);
	$('input[name=name-'+input_id+']').val(name);
	input_id=false;
}
function updateCat( id, idParent, name ){ 
	$('#'+input_id).val(id);
	$('input[name=name-'+input_id+']').val(name);
	input_id=false;
}


//effectue un line action 
function do_line_action( action, melement ){
	messageDuringSave();
	if( current_ajax ) current_ajax.abort();
	
	var form = $(melement).parents('form');
	var element = $(melement).parents('tr');
	var split = element.attr('data-id').split('-');
	
	if( split.length == 4 ){
		current_ajax = $.ajax({
			url: '/admin/catalog/authorizations/new.php',
			data: 'act='+action+'&wst_id='+split[0]+'&usr_fld_id='+split[1]+'&usr_value='+split[2]+'&grp_id='+split[3]+'&'+form.serialize(),
			type: 'POST',
			success:function(retour){
				replace_element(element,retour);
				init_autocomplete();
				messageDuringSave();
			}
		});
	}
}

function titleReplace(){

	$('tr[title].dataID').each(function(){
		if( $(this).attr('title') != '' ){
			$(this).attr('data-id',$(this).attr('title'));
			$(this).attr('title','');
		}
	});
}
function messageDuringSave(){
	if( typeof $('.notice') != 'undefined' && $('.notice').length ){
		$('.notice').remove();
		$('.unclickable').removeClass('unclickable');
	}else{
		$('h3').after('<div class="notice">Enregistrement en cours...</div>');
		$('[type=submit], a').addClass('unclickable');
	}
}