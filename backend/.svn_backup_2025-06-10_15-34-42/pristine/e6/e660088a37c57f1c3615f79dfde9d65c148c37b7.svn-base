<?php

namespace Php<PERSON>arser\Node\Expr;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Expr;
use Php<PERSON><PERSON>er\Node\Name;

class ClassConstFetch extends Expr
{
    /** @var Name|Expr Class name */
    public $class;
    /** @var string|Error Constant name */
    public $name;

    /**
     * Constructs a class const fetch node.
     *
     * @param Name|Expr    $class      Class name
     * @param string|Error $name       Constant name
     * @param array        $attributes Additional attributes
     */
    public function __construct($class, $name, array $attributes = array()) {
        parent::__construct($attributes);
        $this->class = $class;
        $this->name = $name;
    }

    public function getSubNodeNames() {
        return array('class', 'name');
    }
}
