<?php

require_once('prd.stocks.inc.php');

/** \defgroup scm_deposits Dépôts de stockage
 *	\ingroup scm
 *	Les fonctions contenues dans ce module permettent la gestion des dépôts de stockage.
 *	La gestion des stocks s'appuie sur un ou plusieurs dépôts de stockage. Parmi ces dépôts,
 *	l'un est considéré comme principal.
 *	@{
 */

// \cond onlyria
/**	Cette fonction permet la création d'un dépôt de stockage dans la base de données.
 *
 *	@param string $name Obligatoire, nom du dépôt de stockage, ne peut pas être nul.
 *	@param bool $is_main Obligatoire, booléen indiquant s'il s'agit du dépôt principal, ou non. Il ne peut exister qu'un seul dépôt principal.
 *	@param string $address1 Obligatoire, première partie de l'adresse du dépôt
 *	@param string $address2 Obligatoire, seconde partie de l'adresse du dépôt
 *	@param string $zipcode Obligatoire, code postal du dépôt
 *	@param string $city Obligatoire, ville du dépôt
 *	@param string $country Obligatoire, pays du dépôt
 *	@param string $phone Obligatoire, numéro de téléphone du dépôt
 *	@param string $fax Obligatoire, numéro de fax du dépôt
 *	@param string $email Obligatoire, adresse email du dépôt
 *	@param string $desc Optionnel, description du dépôt
 *	@param bool $is_sync Optionnel, si oui ou non le dépot est synchronisé
 *	@param string $ref Optionnel, référence du dépôt
 *	@param int $str_id Optionnel, Identifiant d'un magasin
 *	@param int $tnt_id Optionnel, identifiant d'un tenant
 *
 *	@return int l'identifiant attribué au dépôt en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_deposits_add( $name, $is_main, $address1, $address2, $zipcode, $city, $country, $phone, $fax, $email, $desc=false, $is_sync=1, $ref=null, $str_id=null, $tnt_id=0 ){
	global $config;

	if( !is_numeric( $is_sync ) ){
		return false;
	}
	if( $str_id!=null && !dlv_stores_exists( $str_id ) ){
		return false;
	}

	$name = ucfirst(trim($name));
	if( $name=='' ){
		return false;
	}


	if( !is_numeric($tnt_id) || $tnt_id < 0 ){
		return false;
	}

	$is_main = $is_main ? 1 : 0;
	$address1 = ucfirst(trim($address1));
	$address2 = ucfirst(trim($address2));
	$zipcode = trim($zipcode);
	$city = ucfirst(trim($city));
	$country = strtoupper(trim($country));
	$phone = trim($phone);
	$fax = trim($fax);
	$email = strtolower($email);
	$desc = $desc ? $desc : '';

	if( $is_main=='False' ){
		$is_main = false;
	}elseif( $is_main=='True' ){
		$is_main = true;
	}

	// S'assure qu'il ne peut y avoir qu'un seul dépôt principal
	if( $is_main ){
		ria_mysql_query('update prd_deposits set dps_is_main=0 where dps_tnt_id='.($tnt_id ? $tnt_id : $config['tnt_id']));
	}

	// Procède à la création du dépôt dans la base de données
	$res = ria_mysql_query('
		insert into prd_deposits
			(dps_tnt_id,dps_name,dps_is_main,dps_address1,dps_address2,dps_zipcode,dps_city,dps_country,dps_phone,dps_fax,dps_email,dps_desc,
			dps_is_sync, dps_ref, dps_str_id, dps_date_created )
		values
			('.($tnt_id ? $tnt_id : $config['tnt_id']).',\''.addslashes($name).'\','.( $is_main ? '1':'0' ).',\''.addslashes($address1).'\',
			\''.addslashes($address2).'\',\''.addslashes($zipcode).'\',\''.addslashes($city).'\',\''.addslashes($country).'\',
			\''.addslashes($phone).'\',\''.addslashes($fax).'\',\''.addslashes($email).'\', \''.addslashes($desc).'\', '.$is_sync.',
			'.( $ref !== null ? '\''.addslashes($ref).'\'' : 'null').','.( $str_id !== null ? $str_id : 'null').', now() )
	');

	if( $res ){
		return ria_mysql_insert_id();
	}

	return false;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la validation d'un identifiant de dépôt. Elle va s'assurer que le format de l'identifiant
 *	passé en paramètre et valide, et qu'il correspond à un dépôt enregistré dans la base de données.
 *
 *	@param int $id Identifiant de dépôt à valider
 *
 *	@return bool true si l'identifiant est valide
 *	@return bool false dans le cas contraire
 *
 */
function prd_deposits_exists( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	return ria_mysql_num_rows(ria_mysql_query('
		select dps_id from prd_deposits
		where dps_tnt_id='.$config['tnt_id'].' and dps_is_deleted=0 and dps_id='.$id
	))>0;
}
// \endcond

// \cond onlyria
/**	Cette fonction permet la mise à jour d'un dépôt de stockage déjà présent dans la base de données.
 *
 *	@param int $id Identifiant du dépôt de stockage à modifier
 *	@param string $name Nom du dépôt de stockage, ne peut pas être nul.
 *	@param bool $is_main Booléen indiquant s'il s'agit du dépôt principal, ou non. Il ne peut exister qu'un seul dépôt principal.
 *	@param string $address1 Première partie de l'adresse du dépôt
 *	@param string $address2 Seconde partie de l'adresse du dépôt
 *	@param string $zipcode Code postal du dépôt
 *	@param string $city Ville du dépôt
 *	@param string $country Pays du dépôt
 *	@param string $phone Numéro de téléphone du dépôt
 *	@param string $fax Numéro de fax du dépôt
 *	@param string $email Adresse email du dépôt
 *	@param string $desc Description du dépôt
 *	@param null|string $ref Optionnel, référence du dépôt (null par défaut, aucune modification n'est apportée)
 *
 *	@return bool true en cas de succès
 *	@return bool false en cas d'échec
 */
function prd_deposits_update( $id, $name, $is_main, $address1, $address2, $zipcode, $city, $country, $phone, $fax, $email, $desc, $ref=null ){
	global $config;

	if( !prd_deposits_exists($id) ){
		return false;
	}
	$name = ucfirst(trim($name));
	if( $name=='' ){
		return false;
	}

	$is_main = $is_main ? 1 : 0;
	$address1 = ucfirst(trim($address1));
	$address2 = ucfirst(trim($address2));
	$zipcode = trim($zipcode);
	$city = ucfirst(trim($city));
	$country = strtoupper(trim($country));
	$phone = trim($phone);
	$fax = trim($fax);
	$email = strtolower($email);
	$desc = $desc ? $desc : '';

	if( $is_main=='False' ){
		$is_main = false;
	}elseif( $is_main=='True' ){
		$is_main = true;
	}

	// S'assure qu'il ne peut y avoir qu'un seul dépôt principal
	if( $is_main ){
		ria_mysql_query('update prd_deposits set dps_is_main=0 where dps_tnt_id='.$config['tnt_id'].' and dps_id!='.$id);
	}

	$sql = '
		update prd_deposits set
			dps_name=\''.addslashes($name).'\',
			dps_address1=\''.addslashes($address1).'\',
			dps_address2=\''.addslashes($address2).'\',
			dps_zipcode=\''.addslashes($zipcode).'\',
			dps_city=\''.addslashes($city).'\',
			dps_country=\''.addslashes($country).'\',
			dps_phone=\''.addslashes($phone).'\',
			dps_fax=\''.addslashes($fax).'\',
			dps_email=\''.addslashes($email).'\',
			dps_is_main='.( $is_main ? '1':'0' ).',
			dps_desc=\''.addslashes($desc).'\',
	';

	if( $ref !== null ){
		$sql .= 'dps_ref = "'.addslashes( $ref ).'",';
	}

	$sql .= '
			dps_date_modified = now()
		where dps_tnt_id='.$config['tnt_id'].' and dps_id='.$id.'
	';

	return ria_mysql_query( $sql );
}
// \endcond

// \cond onlyria
/** Cette fonction permet la suppression d'un dépôt de stockage
 *
 *	@todo La suppression virtuelle est actuellement réalisée avec un booléen. Ce serait plus performant si elle était réalisée avec une date de suppression.
 *
 *	@param int $id Identifiant du dépôt de stockage à supprimer
 *
 *	@return bool true si la suppression du dépôt à réussi
 *	@return bool false si la suppression du dépôt a échoué
 */
function prd_deposits_del( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}
	if( !prd_deposits_exists( $id ) ){
		return true;
	}

	return ria_mysql_query('
		update prd_deposits
		set dps_is_deleted = 1,
			dps_date_modified = now()
		where dps_tnt_id = '.$config['tnt_id'].' and dps_id = '.$id.'
	');

}
// \endcond

/**	Cette fonction permet le chargement d'un ou plusieurs dépôts, éventuellement filtrés
 *	en fonction des paramètres optionnels fournis.
 *
 *	@param int $id Facultatif, identifiant d'un dépôt sur lequel filtrer le résultat (ou tableau d'identifiants)
 *	@param bool $is_main Facultatif, filtrer sur le dépôt principal si true, filtrer sur les dépôts secondaires si false (note : prend en compte la variable $config['default_dps_id'] si elle est définie)
 *	@param string $ref Facultatif, référence du dépot
 *	@param int $str_id Facultatif, identifiant d'un magasin
 *	@param string $name Facultatif, Nom du dépots
 *
 *	@return resource un résultat de requête MySQL comprenant les colonnes suivantes :
 *			- id : identifiant du dépôt
 *			- name : nom du dépôt
 *			- is_main : true s'il s'agit du dépôt principal
 *			- address1 : première partie de l'adresse du dépôt
 *			- address2 : seconde partie de l'adresse
 *			- zipcode : code postal du dépôt
 *			- city : ville du dépôt
 *			- country : pays du dépôt
 *			- phone : numéro de téléphone du dépôt
 *			- fax : numéro de fax du dépôt
 *			- email : adresse email du dépôt
 *			- is_sync : si 1, dépot synchronisé si 0, dépot présent uniquement sur le site
 *			- desc : description du dépôt
 *			- date_modified : date de dernière modification
 *			- date_modified_en : date de dernière modification au format EN
 *			- date_created : date de création (format EN)
 *			- ref : référence du dépôt
 *			- str_id : identifiant du magasin
 */
function prd_deposits_get( $id=null, $is_main=null, $ref=null, $str_id=null, $name=null ){

	if( $id === null ){
		$id = 0;
	}
	$id = control_array_integer( $id, false );
	if( $id === false ){
		return false;
	}

	global $config;

	$sql = '
		select
			dps_id as id, dps_name as name, dps_is_main as is_main,
			dps_address1 as address1, dps_address2 as address2,
			dps_zipcode as zipcode, dps_city as city, dps_country as country,
			dps_phone as phone, dps_fax as fax, dps_email as email, dps_is_sync as is_sync, dps_desc as "desc",
			date_format(dps_date_modified, "%d/%m/%Y à %H:%i") as date_modified,
			dps_date_modified as date_modified_en, dps_date_created as date_created,
			dps_ref as "ref",
			dps_str_id as str_id,
			dps_quote_config as quote_config
		from
			prd_deposits
		where
			dps_tnt_id = '.$config['tnt_id'].'
			and dps_is_deleted = 0
	';

	if( sizeof($id) ){
		$sql .= ' and dps_id in ('.implode(', ', $id).')';
	}

	if( $is_main !== null ){
		if( $config['default_dps_id'] > 0 && prd_deposits_exists($config['default_dps_id']) ){
			$sql .= ' and dps_id '.( $is_main ? ' = '.$config['default_dps_id'] : ' != '.$config['default_dps_id'] );
		}else{
			$sql .= ' and dps_is_main = '.( $is_main ? 1 : 0 );
		}
	}

	if( trim($ref) != '' ){
		$sql .= ' and dps_ref = "'.addslashes($ref).'"';
	}

	if( trim($name) != '' ){
		$sql .= ' and dps_name = "'.addslashes($name).'"';
	}

	if( $str_id!=null && is_numeric($str_id) ){
		$sql .= ' and dps_str_id = '.$str_id;
	}

	$sql .= '
		order by dps_is_main desc, dps_name
	';

	return ria_mysql_query($sql);

}

/**	Cette fonction retourne le nom d'un dépôt de stockage à partir de son identifiant
 * 	@param int $id Obligatoire, identifiant du dépôt dont on souhaite charger le nom
 * 	@return string La désignation du dépôt dont l'identifiant est passé en paramètre, ou false en cas d'erreur
 */
function prd_deposits_get_name( $id ){
	global $config;

	if( !is_numeric($id) || $id<=0 ){
		return false;
	}

	$rname = ria_mysql_query('
		select dps_name from prd_deposits where dps_tnt_id='.$config['tnt_id'].' and dps_id='.$id.'
	');
	if( ria_mysql_num_rows($rname) ){
		return ria_mysql_result( $rname, 0, 0 );
	}

	return false;
}

/**	Cette fonction retourne l'identifiant du dépôt principal
 *	@return int l'identifiant du dépôt principal
 *	@return bool false en cas d'erreur (ou si aucun dépôt principal n'est définit)
 */
function prd_deposits_get_main(){
	global $config, $memcached;

	$key_memcached = $config['tnt_id'].':'.$config['wst_id'].':prd_deposits_get_main';
	if ($get = $memcached->get($key_memcached)) {
		return ($get == 'none' ? false : $get);
	}

	// dépôt principal propre à une configuration de site
	if( isset($config['default_dps_id']) && $config['default_dps_id'] > 0 && prd_deposits_exists($config['default_dps_id']) ){
		$memcached->set( $key_memcached, $config['default_dps_id'], 5 * 60 );
		return $config['default_dps_id'];
	}

	$res = ria_mysql_query('
		select dps_id
		from prd_deposits
		where dps_tnt_id = '.$config['tnt_id'].' and dps_is_main and dps_is_deleted = 0
		limit 0, 1
	');

	$dps_main = false;
	if ($res && ria_mysql_num_rows($res)) {
		$r = ria_mysql_fetch_assoc( $res );
		$dps_main = $r['dps_id'];
	}

	$memcached->set( $key_memcached, ($dps_main === false ? 'none' : $dps_main), 5 * 60 );
	return $dps_main;
}

// \cond onlyria
/**	Cette fonction force la mise à jour de la date de dernière modification d'un dépôt de stockage.
 *	@param int $id Identifiant du dépôt.
 *	@return bool True en cas de succès, False en cas d'échec.
 */
function prd_deposits_set_date_modified( $id ){
	global $config;

	if( !is_numeric($id) || $id <= 0 ){
		return false;
	}

	return ria_mysql_query('
		update prd_deposits
		set dps_date_modified = now()
		where dps_tnt_id = '.$config['tnt_id'].' and dps_id = '.$id.'
	');

}
// \endcond

// \cond onlyria
/** Cette fonction permet de récupérer le délai de réservation pour un dépôt.
 *	@param int $dps_id Identifiant d'un dépôt
 *	@return Le délai de réservation, False si le paramètre est omis ou faux
 */
function prd_deposits_get_res_time( $dps_id ){
	global $config;

	if( !is_numeric($dps_id) || $dps_id <= 0 ){
		return false;
	}

	$res = ria_mysql_query('
		select ifnull(dps_res_time, 0) as res_time
		from prd_deposits
		where dps_tnt_id = '.$config['tnt_id'].'
			and dps_id = '.$dps_id.'
	');

	if( !$res || !ria_mysql_num_rows($res) ){
		return false;
	}

	$r = ria_mysql_fetch_assoc( $res );
	return $r['res_time'];
}
// \endcond

// \cond onlyria
/** Sauvegarde la configuration PDF devis surchargée pour un dépôt particulier.
 *
 * @param int $id Obligatoire, Identifiant du dépôt
 * @param array $options Obligatoire, Configuration surchargée
 * @return bool true si succès, false autrement
 */
function prd_deposits_store_quote_config( $id, array $options ){
	global $config;

	return ria_mysql_query('
		update prd_deposits
		set dps_quote_config = \''.json_encode($options).'\',
			dps_date_modified = now()
		where dps_tnt_id = '.$config['tnt_id'].'
			and dps_id = '.$id
	);
}
// \endcond

// \cond onlyria
/** Cette fonction permet de charger un tableau des dépôts de stockage.
 * 	@return array Un tableau contenant la liste des dépôts et comprenant les clés suivantes :
 * 		- id : identifiant du dépôt
 * 		- name : nom du dépôt
 */
function prd_deposits_get_array( $id ){
	$deposits = array();
	$res = prd_deposits_get();

	if(  !$res || !ria_mysql_num_rows($res) ){
		return;
	} else {
		while ($dep = ria_mysql_fetch_assoc($res)) {
			$deposits[] = array(
				'id'	=> $dep['id'],
				'name'	=> $dep['name'],
			);
		}
	}

	return $deposits;
}
// \endcond

/// @}
