<?php

require_once('Colissimo/ClientException.php');

class ColissimoClient
{
    protected $httpClient;

    protected function requireHttpClient()
    {
        if (!$this->httpClient) {
            throw new Exception(_('Client HTTP manquant'));
        }

        return $this->httpClient;
    }

    public function setHttpClient($httpClient)
    {
        $this->httpClient = $httpClient;

        return $this;
    }

    /**
     * Generer une étiquette d'après un bon de livraison (et un numéro de colis)
     * 
     * Ordre de priorité des paramètres : $parameters puis $config['colissimo_defaults'] (json ou tableau)
     *
     * @param int $bl_id Identifiant de bon de livraison
     * @param int $ord_id Facultatif, identifiant de commande
     * @param $colis Facultatif, numéro de colis
     * @param $sort Facultatif, ordre de tri des produits du bon de livraison
     * @param $supplier Optionnel, identifiant d'un fournisseur sur lequel le tri par référence fournisseur sera appliqué
     * @param int|array $prd Optionnel, permet de filtrer le résultat sur un ou plusieurs produits
     * @param int $line Optionnel, permet de filtrer par numéro de ligne (peut être un tableau)
     * @param $validateParameters
     * @param $throwsException
     * @param array $pmt Tableau de paramètres supplémentaires à passer au service d'étiquette
     * 
     * @return void
     */
    public function generateLabelFromBl(
        $bl_id, 
        $ord_id = null, 
        $colis = false,
        $sort = false,
        $supplier = false,
        $prd = false,
        $line = false,
        $validateParameters = false,
        $throwsException = true,
        array $parameters = array()
    ) {
        require_once('ord.bl.inc.php');

        $r_bl = ord_bl_get($bl_id);
        if (!$r_bl || !ria_mysql_num_rows($r_bl)) {
            $message = _('Bon de livraison non trouvé');

            if ($throwsException) {
                throw new ColissimoClientException($message);
            }
            
            error_log($message, E_USER_ERROR);
            return false;
        }

        $bl = ria_mysql_fetch_assoc($r_bl);
        unset($r_bl);

        $r_bl_prd = ord_bl_products_get(
            $bl['id'],
            is_null($ord_id) ? 0 : $ord_id,
            $colis, 
            $sort, 
            $supplier, 
            $prd, 
            $line, 
            false
        );

        if (!$r_bl_prd || !ria_mysql_num_rows($r_bl_prd)) {
            $message = _('Aucun produit ne correspond au bon de livraison');

            if ($throwsException) {
                throw new ColissimoClientException($message);
            }

            error_log($message, E_USER_ERROR);
            return false;
        }

        if (intval($bl['adr_dlv_id']) <= 0) {
            $message = _('Adresse de livraison manquante sur le bon de livraison');

            if ($throwsException) {
                throw new ColissimoClientException($message);
            }

            error_log($message, E_USER_ERROR);
            return false;
        }

        $r_bl_dlv_adr = gu_adresses_get(0, $bl['adr_dlv_id']);
        if (!$r_bl_dlv_adr || !ria_mysql_num_rows($r_bl_dlv_adr)) {
            $message = _('L\'adresse de livraison n\'existe pas sur le bon de livraison');

            if ($throwsException) {
                throw new ColissimoClientException($message);
            }

            error_log($message, E_USER_ERROR);
            return false;
        }

        $r_srv = isset($bl['srv_id']) && intval($bl['srv_id']) > 0 ? dlv_services_get($bl['srv_id']) : false;
        if (!$r_srv || !ria_mysql_num_rows($r_srv)) {
            $message = _('Service de livraison non trouvé depuis le bon de livraison');

            if ($throwsException) {
                throw new ColissimoClientException($message);
            }

            error_log($message, E_USER_ERROR);
            return false;
        }

        $srv = ria_mysql_fetch_assoc($r_srv);
        unset($r_srv);

        $r_srv_type = isset($srv['type_id']) && intval($srv['type_id']) > 0 ? dlv_services_types_get($srv['type_id']) : false;
        if (!$r_srv_type || !ria_mysql_num_rows($r_srv_type)) {
            $message = _('Type de service de livraison non trouvé depuis le bon de livraison');

            if ($throwsException) {
                throw new ColissimoClientException($message);
            }

            error_log($message, E_USER_ERROR);
            return false;
        }

        $srv_type = ria_mysql_fetch_assoc($r_srv_type);
        unset($r_srv_type);

        if (!isset($srv_type['ref']) || !trim($srv_type['ref'])) {
            $message = _('Référence externe du type de service de livraison non trouvé depuis le bon de livraison');

            if ($throwsException) {
                throw new ColissimoClientException($message);
            }

            error_log($message, E_USER_ERROR);
            return false;
        }

        global $config;

        $depositDate = \DateTime::createFromFormat('Y-m-d H:i:s', $bl['date_en']);
        $now = new \DateTime();
        if (!($depositDate instanceof \DateTime) || $depositDate->diff($now)->invert == 0/*date bl dans le passe*/) {
            $depositDate = new \DateTime();
        }

        $defaultParameters = array();
        if (isset($config['colissimo_generate_label_defaults'])) {
            if (is_array($config['colissimo_generate_label_defaults'])) {
                $defaultParameters = $config['colissimo_generate_label_defaults'];
            } elseif (is_string($config['colissimo_generate_label_defaults'])) {
                $defaultParameters = json_decode($config['colissimo_generate_label_defaults'], JSON_OBJECT_AS_ARRAY);
                if (!is_array($defaultParameters)) {
                    $defaultParameters = array();
                }
            }
        }

        $parameters = array_merge_recursive(
            $defaultParameters,
            $parameters,
            array(
                'letter' => array(
                    'service' => array(
                        'productCode' => $srv_type['ref'],
                        'depositDate' => $depositDate->format('Y-m-d'),
                        'commercialName' => $config['site_name']
                    )
                )
            )
        );

        unset($srv_type);

        $order = false;
        $weight = 0;
        while ($bl_prd = ria_mysql_fetch_assoc($r_bl_prd)) {
            if (false == $order && isset($bl_prd['ord_id']) && intval($bl_prd['ord_id']) > 0) {
                $r_ord = ord_orders_get(0, $bl_prd['ord_id']);
                if ($r_ord && ria_mysql_num_rows($r_ord)) {
                    $order = ria_mysql_fetch_assoc($r_ord);
                }
            }

            $prd_weight = floatval($bl_prd['weight_net']);

            if ($prd_weight <= 0) {
                $prd_weight = floatval(prd_products_get_weight_net($bl_prd['id'])) / 1000;
            }

            if ($prd_weight <= 0) {
                $message = sprintf(
                    _('Le poids de l\'article "%s" n\'est pas un entier positif supérieur à zéro'),
                    $bl_prd['ref']
                );

                if ($throwsException) {
                    throw new ColissimoClientException($message);
                }

                error_log($message, E_USER_ERROR);
                return false;
            }

            $weight += $prd_weight;
        }
        unset($bl_prd);

        if ($weight <= 0) {
            $message = _('Le poids total des articles n\'est pas un entier positif supérieur à zéro');

            if ($throwsException) {
                throw new ColissimoClientException($message);
            }

            error_log($message, E_USER_ERROR);
            return false;
        }

        $parameters['letter']['parcel']['weight'] = number_format($weight, 2, '.', '');

        $site_owner = site_owner_get();

        $parameters['letter']['sender'] = array('address' => array('companyName' => $site_owner['name']));

        $r_dps = false;
        if ($order) {
            $parameters['letter']['service']['orderNumber'] = $order['id'];
            if (trim($order['ref'])) {
                $parameters['letter']['sender']['senderParcelRef'] = $order['ref'];
            }

            if (intval($order['dps_id']) > 0) {
                $r_dps = prd_deposits_get($order['dps_id']);
            }
            unset($order);
        }

        if (false === $r_dps) {
            $r_dps = prd_deposits_get(null, true);
        }

        if ($r_dps && ria_mysql_num_rows($r_dps)) {
            $dps = ria_mysql_fetch_assoc($r_dps);
            $this->setAddressParameter($parameters['letter']['sender']['address'], $dps);
            unset($dps);
        }

        if (!isset($parameters['letter']['sender']['address']['zipCode'])) {
            $parameters['letter']['sender']['address'] = array('companyName' => $site_owner['name']);
            $this->setAddressParameter($parameters['letter']['sender']['address'], $site_owner);
        }

        if (!isset($parameters['letter']['sender']['address']['zipCode'])) {
            $r_store = dlv_stores_get();
            if ($r_store && ria_mysql_num_rows($r_store) === 1) {
                $parameters['letter']['sender']['address'] = array('companyName' => $site_owner['name']);
                $this->setAddressParameter($parameters['letter']['sender']['address'], ria_mysql_fetch_assoc($r_store));
            }
        } else {
            $message = sprintf(
                _(
                    'Adresse du site d\'envoi non trouvé. Veuillez renseigner le dépot sur le bon de livraison'
                        . ', ou l\'adresse du dépôt principal, ou l\'adresse du propriétaire du site web : %s'
                ),
                json_encode(func_get_args())
            );

            if ($throwsException) {
                throw new Exception($message);
            }

            error_log($message, E_USER_ERROR);
            return false;
        }

        $parameters['letter']['addressee'] = array('address' => array());
        $this->setAddressParameter($parameters['letter']['addressee']['address'], ria_mysql_fetch_assoc($r_bl_dlv_adr));

        $data = $this->requireHttpClient()->generateLabel($parameters, $validateParameters, $throwsException);

        if (!isset($data['file'])) {
            $message = sprintf(
                _('Etiquette non trouvée dans la réponse : %s'),
                json_encode(func_get_args())
            );

            if ($throwsException) {
                throw new Exception($message);
            }

            error_log($message, E_USER_ERROR);
            return false;
        }
        
        $r_doc_type = doc_types_get(0, false, true, false, false, false, null, false, 'Etiquette Colissimo');
        if (!$r_doc_type || !ria_mysql_num_rows($r_doc_type)) {
            $doc_type_id = doc_types_add('Etiquette Colissimo', 'Etiquette générée pour l\'envoi d\'un colis Colissimo', 0);
        } else {
            $doc_type_id = ria_mysql_result($r_doc_type, 0, 'id');
        }

        $prefix = 'etiquette-colissimo';
        $doc_filename = 'etiquette-colissimo-' 
            . (isset($data['parcelNumber']) ? $data['parcelNumber'] : uniqid($prefix)) 
            . '.' . strtolower(substr($parameters['outputFormat']['outputPrintingType'], 0, 3));

        $doc_name = 'Etiquette Colissimo' . (isset($data['parcelNumber']) ? 'N°' . $data['parcelNumber'] : '');
            
        $tempname = tempnam(sys_get_temp_dir(), $prefix);
        if (!@file_put_contents($tempname, $data['file'])) {
            throw new Exception(
                sprintf(
                    _('Impossible d\'écrire dans le fichier de l\'étiquette: %s'),
                    json_encode(func_get_args())
                )
            );
        }

        $doc_id = doc_documents_add($doc_type_id, $tempname, $doc_filename, $doc_name, '', array(), array());
        if (!$doc_id) {
            $message = sprintf(
                _('Erreur lors de la création du document étiquette: %s'),
                json_encode(func_get_args())
            );

            if ($throwsException) {
                throw new Exception($message);
            }

            error_log($message, E_USER_ERROR);
            return false;
        }

        ria_mysql_data_seek($r_bl_prd, 0);
        while ($bl_prd = ria_mysql_fetch_assoc($r_bl_prd)) {
            if (isset($data['labelResponse']) 
                && isset($data['labelResponse']['parcelNumber'])
                && trim($data['labelResponse']['parcelNumber'])
            ) {
                ord_bl_products_update_sage(
                    $bl_prd['bl_id'],
                    $bl_prd['id'], 
                    $bl_prd['line'], 
                    $bl_prd['ref'], 
                    $bl_prd['name'], 
                    $bl_prd['qte'], 
                    $bl_prd['price_ht'], 
                    $bl_prd['tva_rate'], 
                    $bl_prd['ord_id'],
                    $data['labelResponse']['parcelNumber'],
                    $bl_prd['price_ttc'], 
                    $bl_prd['ecotaxe']
                );
            }

            $r_bl_prd_docs = doc_objects_get(0, CLS_BL_PRODUCT, $bl_prd['id'], $doc_type_id);
            if ($r_bl_prd_docs && ria_mysql_num_rows($r_bl_prd_docs)) {
                while ($bl_prd_doc = ria_mysql_fetch_assoc($r_bl_prd_docs)) {
                    doc_objects_del($bl_prd_doc['doc_id'], CLS_BL_PRODUCT, $bl_prd['id']);
                }
            }

            doc_objects_add($doc_id, CLS_BL_PRODUCT, $bl_prd['id']);
        }

        return array(
            'doc_id' => $doc_id
        );
    }

    protected function setAddressParameter(array & $parameter, array & $address)
    {
        if (isset($address['lastname']) && trim($address['lastname'])) {
            $parameter['lastName'] = $address['lastname'];
        }

        if (isset($address['firstname']) && trim($address['firstname'])) {
            $parameter['firstName'] = $address['firstname'];
        }

        if (isset($address['address2']) && trim($address['address2'])) {
            $parameter['line1'] = $address['address2'];
        }

        if (isset($address['address1']) && trim($address['address1'])) {
            $parameter['line2'] = $address['address1'];
        }

        $parameter['countryCode'] = 'FR';
        if (isset($address['country']) && trim($address['country'])) {
            $r_country = sys_zones_get(0, '', $address['country'], false, 0, '', _ZONE_PAYS, array(), 0, 1, false, true);
            if ($r_country && ria_mysql_num_rows($r_country)) {
                $country = ria_mysql_fetch_assoc($r_country);
                if (trim($country['code'])) {
                    $parameter['countryCode'] = $country['code'];
                }
            }
        }

        if (isset($address['city']) && trim($address['city'])) {
            $parameter['city'] = $address['city'];
        }

        if (isset($address['zipcode']) && trim($address['zipcode'])) {
            $parameter['zipCode'] = $address['zipcode'];
        }

        if (isset($address['phone']) && trim($address['phone'])) {
            $parameter['phoneNumber'] = preg_replace('/[^\d\+]/', '', $address['phone']);
        }

        if (isset($address['email']) && trim($address['email'])) {
            $parameter['email'] = $address['email'];
        }

        $parameter['language'] = 'FR';

        return $this;
    }
}