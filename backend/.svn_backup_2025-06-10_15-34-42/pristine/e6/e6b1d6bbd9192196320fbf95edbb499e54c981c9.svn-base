# ChannelCatalogLinks

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**self** | [**\Swagger\Client\Model\LinksGetChannelCatalogLink**](LinksGetChannelCatalogLink.md) |  | 
**delete** | [**\Swagger\Client\Model\LinksDeleteChannelCatalogLink**](LinksDeleteChannelCatalogLink.md) |  | [optional] 
**enable** | [**\Swagger\Client\Model\LinksEnableChannelCatalogLink**](LinksEnableChannelCatalogLink.md) |  | [optional] 
**disable** | [**\Swagger\Client\Model\LinksDisableChannelCatalogLink**](LinksDisableChannelCatalogLink.md) |  | [optional] 
**configure_general_settings** | [**\Swagger\Client\Model\LinksConfigureChannelCatalogGeneralSettingsLink**](LinksConfigureChannelCatalogGeneralSettingsLink.md) |  | [optional] 
**configure_cost_settings** | [**\Swagger\Client\Model\LinksConfigureChannelCatalogCostSettingsLink**](LinksConfigureChannelCatalogCostSettingsLink.md) |  | [optional] 
**configure_column_mappings** | [**\Swagger\Client\Model\LinksConfigureChannelCatalogColumnMappingsLink**](LinksConfigureChannelCatalogColumnMappingsLink.md) |  | [optional] 
**reenable_category_mappings** | [**\Swagger\Client\Model\LinksReenableChannelCatalogCategoryMappingLink**](LinksReenableChannelCatalogCategoryMappingLink.md) |  | [optional] 
**disable_category_mappings** | [**\Swagger\Client\Model\LinksDisableChannelCatalogCategoryMappingLink**](LinksDisableChannelCatalogCategoryMappingLink.md) |  | [optional] 
**category_mappings** | [**\Swagger\Client\Model\LinksGetChannelCatalogCategoriesLink**](LinksGetChannelCatalogCategoriesLink.md) |  | [optional] 
**exclusion_filters** | [**\Swagger\Client\Model\LinksGetChannelCatalogExclusionFiltersLink**](LinksGetChannelCatalogExclusionFiltersLink.md) |  | [optional] 
**products** | [**\Swagger\Client\Model\LinksGetChannelCatalogProductInfoListLink**](LinksGetChannelCatalogProductInfoListLink.md) |  | [optional] 
**exportation_cache_info** | [**\Swagger\Client\Model\LinksGetChannelCatalogExportationCacheInfoLink**](LinksGetChannelCatalogExportationCacheInfoLink.md) |  | [optional] 
**marketplace_settings** | [**\Swagger\Client\Model\ExternalLinksGetChannelCatalogMarketplaceSettingsLink**](ExternalLinksGetChannelCatalogMarketplaceSettingsLink.md) |  | [optional] 
**channel_info** | [**\Swagger\Client\Model\ExternalLinksGetChannelInfoLink**](ExternalLinksGetChannelInfoLink.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


