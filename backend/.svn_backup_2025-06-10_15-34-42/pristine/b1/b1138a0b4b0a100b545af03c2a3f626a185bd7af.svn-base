<?php
for ($i = 0; $i < count($array); $i++) {
}

$num = count($array);

while ($i < count($array)) {
}

do {
} while ($i < count($array));

for ($i = 0; $i < count($this->children); $i++) {
}



for ($i = 0; $i < sizeof($array); $i++) {
}

$num = sizeof($array);

while ($i < sizeof($array)) {
}

do {
} while ($i < sizeof($array));

for ($i = 0; $i < sizeof($this->children); $i++) {
}




for ($i = 0; $i < strlen($string); $i++) {
}

$num = strlen($string);

while ($i < strlen($string)) {
}

do {
} while ($i < strlen($string));

for ($i = 0; $i < strlen($this->string); $i++) {
}

for ($i = sizeof($array); $i > 0; $i--) {
}

do {
    echo $a->count;
    $a->count--;
} while($a->count);

for ($i = 0; $i < $a->count; $i++) {}
?>
