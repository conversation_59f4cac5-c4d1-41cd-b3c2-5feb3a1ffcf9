<?php

use FtpClient\FtpClient;
use FtpClient\FtpException;

require_once('products.inc.php');
require_once('prd/images.inc.php');
require_once('FTP/FtpClient.php');
require_once('CorepMapping.php');
// require_once('db.inc.php');
// require_once('tenants.inc.php');
// require_once('RegisterGCP.inc.php');
// print_r($config);

class CorepImport
{
    // Constantes FTP : serveur, identifiant, mot de passe et repertoire contenant les fichiers zip à importer
    const _COREP_FTP_HOST_ = 'ftp.corep.com';
    const _COREP_FTP_LOGIN_ = 'crp_prod';
    const _COREP_FTP_PASSWORD_ = 'N^9p4og7';
    const _COREP_FTP_FOLDER_ = '/YUTO';

    // Autres constantes :

    // Repertoires de stockage des fichiers
    // Processus : repertoire download puis repertoire imported si traitement OK ou repertoire invalid si probleme
    const _COREP_DOWNLOAD_FOLDER_ = __DIR__ . '/files/temp';
    const _COREP_PROCESSED_FOLDER_ = __DIR__ . '/files/processed';
    const _COREP_INVALID_FOLDER_ = __DIR__ . '/files/invalid';

    // Re-traitement des fichiers
    const _SKIP_ALREADY_DOWNLOADED_ = true;
    const _SKIP_ALREADY_PROCESSED_ = true;

    // Log
    const _COREP_ENABLE_LOG_ = true;
    const _COREP_LOG_FOLDER_ = __DIR__ . '/logs';

    // Mode Test ### TRES IMPORTANT !!! A activer pour ne pas saturer l'herbergement et accelerer les tests mais à passer sur false pour lancer l'import complet
    const _COREP_IMPORT_TEST_MODE_ = true;

    // Option pour creer les emballages (en tant que conditionnements)
    const _COREP_IMPORT_CREATE_CONDITIONING_ = true;

    // Type d'operation (telechargement, traitement...). Determine aussi le nom du log à utiliser
    public $operation;

    // Objet FtpClient
    public $connector;

    // Tableau - Liste des fichiers zip à traiter (utilisés pour le telechargement et l'integration)
    public $files = [];

    // Tableau - Liste des categories avec lesquelles on va associer un produit
    public $product_categories = [];

    // Tableau pour stocker les erreurs
    public $errors = [];

    // Compteur de telechargements
    public $counters = [
        'directories' => 0,
        'ftp_files' => 0,
        'downloaded_files' => 0,
        'processed_files' => 0
    ];

    private $cat_root = 238487;

    public function __construct($operation)
    {
        $this->operation = $operation;
        $this->connector = new \FtpClient\FtpClient();
        $this->prepareLog();
    }

    /*** INTEGRATION ***/

    /**
     * Recuperation de la liste des fichiers presents dans le repertoire temp
     * @return false si probleme (repertoire absent ou vide de fichiers zip), sinon remplit la propriete $files de la classe
     */
    public function getFilesToProcess()
    {
        if (!file_exists(self::_COREP_DOWNLOAD_FOLDER_)) {
            return false;
        } else {
            // Recuperation des fichiers dans le repertoire racine
            $root_files = $zip_files = glob(self::_COREP_DOWNLOAD_FOLDER_.'/*.zip');
            if (!empty($root_files)) {
                if (!file_exists(self::_COREP_PROCESSED_FOLDER_.'/root')) {
                    mkdir(self::_COREP_PROCESSED_FOLDER_.'/root');
                }
                $this->getFilesFromDirectory(self::_COREP_DOWNLOAD_FOLDER_, true);
            }
            // Recuperation des sous-repertoires
            $subdirs = array_filter(glob(self::_COREP_DOWNLOAD_FOLDER_.'/*'), 'is_dir');
            // Recuperation des fichiers dans les sous-repertoires
            foreach ($subdirs as $sd) {
                $this->getFilesFromDirectory(self::_COREP_DOWNLOAD_FOLDER_);
            }
            return !empty($this->files);
        }
    }

    /**
     * Recuperation des fichiers zip d'un repertoire
     * @param $dir : le chemin absolu du repertoire
     */
    public function getFilesFromDirectory($dir, $is_root = false)
    {
        if (!$is_root) {
            $folder = str_replace(self::_COREP_DOWNLOAD_FOLDER_.'/', '', $dir);
        }
        $zip_files = glob($dir.'/*.zip');
        // Le nom de fichier doit etre formaté sur le mode sku_local_LOCALE.zip (ex : 163300_fr_FR.zip)
        foreach ($zip_files as $zf) {
            $file_info = pathinfo($zf);
            $file_data = null;
            $file_data = explode('_', $file_info['filename']);
            if ((count($file_data) !== 3) || (strlen($file_data[1]) !== 2) || (strlen($file_data[2]) !== 2)){
                if (self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage(sprintf('Fichier %s non traité dans le répertoire %s car il ne respecte pas le format', $file_info['filename'], $dir));
                }
                $this->moveFileToInvalid($folder, $file_info['basename']);
                continue;
            }
            $this->files[] = [
                'name' => $file_info['filename'],
                'path' => $zf,
                'folder' => !$is_root ? $folder : 'root',
                'sku' => $file_data[0],
                'lang' => $file_data[1]
            ];
        }
    }

    /**
     * Traitement des fichiers : à partir de la liste des fichiers zip trouvés dans le répertoire de téléchargement, oneffectue les traitements suivant :
     * 1- Dezipper les archives dans le repertoire de traitement ("processed")
     * 2- Verifier que les archives sont conformes (doivent contenir 2 fichiers JSON)
     * 3- Lire le fichier sku_data.json pour y récupérer les données du produit
     */
    public function processFiles()
    {
        global $config;
        $ZipArchive = new ZipArchive;
        $CorepMapping = new CorepMapping;
        RegisterGCPConnection::init($CorepMapping->id_tenant, true);
        $config['tnt_id'] = $CorepMapping->id_tenant;
        foreach ($this->files as $k => $file) {
            $current_dir = self::_COREP_PROCESSED_FOLDER_.'/'.$file['folder'].'/'.$file['name'];
            if (self::_SKIP_ALREADY_PROCESSED_ && $this->fileAlreadyProcessed($file['name'], $file['folder'])) {
                if (self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage(sprintf('Le fichier %1$s (répertoire %2$s) a déjà été traité pour intégration.', $file['name'], $file['folder']));
                }
                continue;
            }
            // Ouverture du fichier
            $zip_open = $ZipArchive->open($file['path']);
            if ($zip_open) {
                // Extraction du fichier
                $zip_extract = $ZipArchive->extractTo($current_dir);// to do : decommenter pour reactiver l'extraction
                if (!$zip_extract) {
                    // Si erreur d'extraction on retire le fichier de la liste et on supprime son répertoire de processed (il peut ne pas avoir été créé)
                    $error = sprintf('L\'extraction du fichier %1$s (répertoire %2$s) a échoué.', $file['name'], $file['folder']);
                    $this->errors[] = $error;
                    if (self::_COREP_ENABLE_LOG_ === true) {
                        $this->logMessage($error);
                    }
                    unset($this->files[$k]);
                    $this->moveFileToInvalid($file['folder'], $file['name'].'.zip', true, $current_dir);
                } else {
                    // Si extraction ok on vérifie que les 2 fichiers JSON sont bien présents, si non on retire le fichier de la liste et on supprime son répertoire de processed
                    if (!file_exists($current_dir.'/sku_data.json') || !file_exists($current_dir.'/assets_data.json')) {
                        $error = sprintf('Contenu de l\'archive %1$s invalide (répertoire %2$s). Au moins 1 des 2 fichiers JSON est manquant', $file['name'], $file['folder']);
                        $this->errors[] = $error;
                        if (self::_COREP_ENABLE_LOG_ === true) {
                            $this->logMessage($error);
                        }
                        unset($this->files[$k]);
                        $this->moveFileToInvalid($file['folder'], $file['name'].'.zip', true, $current_dir);
                    } else {
                        // On verifie que le fichier JSON principal est lisible, et si non on passe à l'archive suivante
                        $sku_data = json_decode(file_get_contents($current_dir.'/sku_data.json'), true);
                        if (!$sku_data || is_null($sku_data)) {
                            $error = sprintf('Contenu du fichier sku_data.json de l\'archive %1$s illisible (répertoire %2$s).', $file['name'], $file['folder']);
                            if (self::_COREP_ENABLE_LOG_ === true) {
                                $this->logMessage($error);
                            }
                            unset($this->files[$k]);
                            $this->moveFileToInvalid($file['folder'], $file['name'].'.zip', true, $current_dir);
                            continue;
                        }
                        // On recupere les donnees dans le fichier et en fonction du mapping on les ajoute en tant que valeur de champ natif ou custom
                        $product_data = [];
                        $product_custom_fields = [];
                        // On commence par récupérer la ref car elle va nous permettre de vérifier si le produit existe (et doit être mis à jour) ou s'il doit être créé
                        $ref = isset($sku_data['id_famille_produit']) ? $sku_data['id_famille_produit'] : $sku_data['sku']; // si propriété id_famille_produit on l'utilise, sinon on se rabat sur le SKU vide
                        $id_product = prd_products_get_id($ref);
                        $operation = $id_product === false ? 'create' : 'update';
                        foreach ($sku_data as $k => $v) {
                            if (in_array($k, ['i18n', 'associations', 'categories'])) { // Liste des groupes de propriete qu'on ne traite pas ou pas tout de suite
                                continue;
                            }
                            // Dans le mapping des champs, 'id' contient le nom de la colonne SQL si c'est un champ natif ou le "numero" du champ si c'est un champ custom
                            if (isset($CorepMapping->fields[$k])) {
                                if (!is_numeric($CorepMapping->fields[$k]['id'])) { // Champ natif
                                    $callback = $CorepMapping->fields[$k]['callback'];
                                    $id_field = $CorepMapping->fields[$k]['id'];
                                    if (!is_null($v)) {
                                        // Callback
                                        if (!is_null($callback)) {
                                            $v = $this->getValueFromCallback($v, $sku_data, $CorepMapping, $callback);
                                        }
                                        // Casts
                                        if ($CorepMapping->fields[$k]['type'] == 'float') {
                                            $v = floatval($v);
                                        }
                                        if ($CorepMapping->fields[$k]['type'] == 'int') {
                                            $v = intval($v);
                                        }
                                    }
                                    $product_data[$id_field] = $v;
                                } else if (!is_null($v)) {
                                    if (is_numeric($CorepMapping->fields[$k]['id'])) { // Champ custom : on enregistre une paire 'id_du_champ' => valeur de la propiété (castée)
                                        // Si la propriete necessite un formatage particulier on execute son callback
                                        if (!is_null($CorepMapping->fields[$k]['callback'])) {
                                            $callback = $CorepMapping->fields[$k]['callback'];
                                            $v = $this->getValueFromCallback($v, $sku_data, $CorepMapping, $callback);
                                        }
                                        // Casts
                                        if ($CorepMapping->fields[$k]['type'] == 'float') {
                                            $v = floatval($v);
                                        }
                                        if ($CorepMapping->fields[$k]['type'] == 'int') {
                                            $v = intval($v);
                                        }
                                        // Fin des traitements - Ajout à la liste des champs custom à traiter
                                        $product_custom_fields[$CorepMapping->fields[$k]['id']] = $v;
                                    } else { // Champ natif
                                        $callback = $CorepMapping->fields[$k]['callback'];
                                        $id_field = $CorepMapping->fields[$k]['id'];
                                        if (!is_null($callback)) {
                                            $v = $this->getValueFromCallback($v, $sku_data, $CorepMapping, $callback);
                                        }
                                        // Casts
                                        if ($CorepMapping->fields[$k]['type'] == 'float') {
                                            $v = floatval($v);
                                        }
                                        if ($CorepMapping->fields[$k]['type'] == 'int') {
                                            $v = intval($v);
                                        }
                                        $product_data[$id_field] = $v;
                                    }
                                } // Fin de la recuperation des donnees et de leur classement natif / custom
                            }
                        }
                        // Corrections en cas de donnee indispensable absente du fichier
                        if (empty(trim($product_data['prd_name']))) {
                            $product_data['prd_name'] = 'Désignation inconnue';
                        }
                        if (!is_numeric($product_data['prd_brd_id'])) {
                            $product_data['prd_brd_id'] = 0;
                        }
                        // Creation du produit
                        if ($operation === 'create') {
                            $id_product = prd_products_add( $ref, $product_data['prd_name'], $product_data['prd_desc'], $product_data['prd_brd_id'], true, $product_data['prd_weight'], $product_data['prd_length'], $product_data['prd_width'], $product_data['prd_height'] );
                            // Si erreur de creation on enregistre l'erreur et on passe au produit suivant
                            if (!$id_product) {
                                $error = sprintf('Erreur de création pour le produit %1$s (répertoire %2$s).', $file['name'], $file['folder']);
                                if (self::_COREP_ENABLE_LOG_ === true) {
                                    $this->logMessage($error);
                                }
                                unset($this->files[$k]);
                                $this->moveFileToInvalid($file['folder'], $file['name'].'.zip', true, $current_dir);
                                continue;
                            } else if (self::_COREP_ENABLE_LOG_ === true) {
                                $this->counters['processed_files']++;
                                $this->logMessage(sprintf('Création du produit %1$s réussie (répertoire %2$s).', $file['name'], $file['folder']));
                            }
                        }
                        // MaJ du produit
                        if ($operation === 'update') {
                            $update_product = prd_products_update( $id_product, $ref, $product_data['prd_name'], $product_data['prd_desc'], $product_data['prd_brd_id'], true, $product_data['prd_weight'], $product_data['prd_length'], $product_data['prd_width'], $product_data['prd_height'] );
                            // Si erreur de creation on enregistre l'erreur et on passe au produit suivant
                            if (!$update_product) {
                                $error = sprintf('Erreur de mise-à-jour pour le produit %1$s (répertoire %2$s).', $file['name'], $file['folder']);
                                if (self::_COREP_ENABLE_LOG_ === true) {
                                    $this->logMessage($error);
                                }
                                unset($this->files[$k]);
                                $this->moveFileToInvalid($file['folder'], $file['name'].'.zip', true, $current_dir);
                                continue;
                            } else if (self::_COREP_ENABLE_LOG_ === true) {
                                $this->counters['processed_files']++;
                                $this->logMessage(sprintf('Mise-à-jour des informations principales du produit %1$s réussie (répertoire %2$s).', $file['name'], $file['folder']));
                            }
                        }
                        // Les setters ne permettent pas d'enregistrer le poids net, on execute donc une fonction supplementaire
                        if ($product_data['prd_weight_net'] > 0) {
                            $udpate_weight_net = prd_products_update_weight_net($id_product, $product_data['prd_weight_net']);
                        }
                        // Les champs natifs du produit sont traités, on passe aux champs custom (pas d'action apres enregistrement)
                        foreach ($product_custom_fields as $id_field => $field_value) {
                            $update_field = fld_object_values_set( $id_product, $id_field, $field_value, $file['lang'], true );
                            if (!$update_field) {
                                $error = sprintf('Erreur de mise-à-jour du champ %1$s pour le produit %2$s (répertoire %3$s).', $id_field, $file['name'], $file['folder']);
                                if (self::_COREP_ENABLE_LOG_ === true) {
                                    $this->logMessage($error);
                                }
                            }
                        }
                        // Integration des images
                        $import_images = $this->integrateImages($file['folder'].'/'.$file['name'], json_decode(file_get_contents($current_dir.'/assets_data.json'), true), $id_product, $ref);
                        // Creation des emballages (en tant que conditionnement) si l'option est activée
                        if (self::_COREP_IMPORT_CREATE_CONDITIONING_ && !empty($sku_data['emballages'])) {
                            $this->integrateConditionings($sku_data['emballages'], $id_product, $file['name']);
                        }
                    }
                }
            } else {
                $error = sprintf('L\'ouverture du fichier %1$s (répertoire %2$s) a échoué. Erreur ZipArchive  %3$s', $file['name'], $file['folder'], $zip_open);
                $this->errors[] = $error;
                if (self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage($error);
                }
            }
            // En mode test on n'integre que 10 produits maximum pour  reduire le temps du processus
            if (self::_COREP_IMPORT_TEST_MODE_ === true && $this->counters['processed_files'] >= 10) {
                break;
            }
        }
    }

    /**
     * Import des images d'un produit à partir des donnees contenues dans le fichier assets_data.json du repertoire
     * @param string $folder - le repertoire en cours de traitement (dans "processed", chemin relatif du type /IMPORT_JJ_MM_AAAA/sku_fr_FR )
     * @param array $json_data - les donnees JSON du fichier assets_data.json converties en tableau PHP
     * @param int $id_product - l'id du produit à traiter
     * @param string $ref - la reference du produit
     */
    public function integrateImages($folder, $json_data, $id_product, $ref)
    {
        global $config;
        $images_integration_errors = false;
        $is_first_image = true;
        if (empty($folder) || (!$json_data || is_null($json_data)) || !$id_product) {
            $error = sprintf('Impossible d\'importer les images pour le produit %1$s (répertoire %2$s). Paramètres incomplets', $ref, $folder);
            $this->errors[] = $error;
            $images_integration_errors = true;
            if (self::_COREP_ENABLE_LOG_ === true) {
                $this->logMessage($error);
            }
            return false;
        } else {
            foreach ($json_data['assets'] as $asset) {
                $msg = null;
                switch ($asset['kind']) {
                    case 'img': // Images
                        if (!in_array(strtolower($asset['extension']), ['jpg', 'jpeg', 'png', 'gif'])) {
                            $msg = sprintf('Le fichier asset %s du répertoire %s n\'a pas été traité car son extention (%s) n\'est pas prise en charge', $asset['filename'], $folder, $asset['extension']);
                        } else {
                            $filename = $asset['filename'];
                            // Le fichier est il présent dans l'archive ?
                            if (!file_exists(self::_COREP_PROCESSED_FOLDER_.'/'.$folder.'/'.$filename)) {
                                $msg = sprintf('Impossible de traiter le fichier asset %s car il est absent du répertoire %s', $asset['filename'], $folder);
                                $this->errors[] = $msg;
                                $images_integration_errors = true;
                            } else {
                                if ($is_first_image) {
                                    $image_add = prd_images_main_add($id_product, self::_COREP_PROCESSED_FOLDER_.'/'.$folder.'/'.$filename, $filename, true);
                                } else {
                                    $image_add = prd_images_add($id_product, self::_COREP_PROCESSED_FOLDER_.'/'.$folder.'/'.$filename, $filename, true);
                                }
                                $is_first_image = false;
                                if ($image_add) {
                                    $msg = sprintf('Intégration du fichier asset %s du répertoire %s réussie', $asset['filename'], $folder);
                                } else {
                                    @unlink($config['img_dir'].'/'.$filename);
                                    $msg = sprintf('L\'intégration du fichier asset %s du répertoire %s a échoué', $asset['filename'], $folder);
                                    $this->errors[] = $msg;
                                    $images_integration_errors = true;
                                }
                            }
                        }
                        break;
                    default: // A ce jour pas d'autre type de fichier a traiter en dehors des images
                        $msg = sprintf('Le fichier asset %s du répertoire %s n\'a pas été traité car il ne correspond pas à un type de fichier pris en charge', $asset['filename'], $current_dir);
                        break;
                }
                if (!empty($msg) && self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage($msg);
                }
            }
        }
        return $images_integration_errors === false ? true : false;
    }

    /**
     * Import des conditionnements (emballages) d'un produit
     * @param array $json_data - un tableau PHP avec les emballages, correspond aux donnees de la propriété "emabllages" dans le fichier sku_data.json du repertoire
     * @param int $id_product - l'id du produit à traiter
     * @param int $filename - le nom du fichier à traiter
     */
    public function integrateConditionings(array $json_data, $id_product, $filename)
    {
        global $config;
        $conditioning_integration_errors = false;
        $conditioning_types_array = [];
        // On commence par verifier si le type de conditionnement existe ou non, si non on le cree
        foreach ($json_data as $conditioning) {
            $conditioning_type = ria_mysql_fetch_assoc(prd_colisage_types_get(0, false, false, $conditioning['libelle']));
            if (isset($conditioning_type['id'])) {
                // S'il n'existe pas  dejà une association entre ce produit et ce type de conditionnement on l'ajoute à la liste de celels à créer
                if (!prd_colisage_classify_exists($id_product, (int)$conditioning_type['id'])) {
                    $conditioning_types_array[] = (int)$conditioning_type['id'];
                }
            } else { // Creation du type de conitionnement (avec quantité 1 car pas d'info de quantité dans le fichier JSON)
                $id_conditioning_type = prd_colisage_types_add(trim($conditioning['libelle']), 1);
                if (!$id_conditioning_type) {
                    $conditioning_integration_errors = true;
                    $msg = sprintf('Impossible de créer le type de conditionnement %s pour le fichier %s', $conditioning['libelle'], $filename);
                    $this->errors[] = $msg;
                    if (self::_COREP_ENABLE_LOG_ === true) {
                        $this->logMessage($msg);
                    }
                    continue;
                } else {
                    if (self::_COREP_ENABLE_LOG_ === true) {
                        $this->logMessage(sprintf('Création réussie du type de conditionnement %s pour le fichier %s', $conditioning['libelle'], $filename));
                    }
                    // On empile l'id du nouveau type de conditionnement dans la liste pour la créer
                    $conditioning_types_array[] = $id_conditioning_type;
                }
            }
            if (!empty($conditioning_types_array)) {
                // Ajout des nouvelles associations produit / types de conditionnement
                if (prd_colisage_classify_add($conditioning_types_array, $id_product)) {
                    $msg = sprintf('Création des conditonnements réussie pour le fichier %s', $filename);
                } else {
                    $conditioning_integration_errors = true;
                    $msg = sprintf('Echec de la création des conditonnements pour le fichier %s', $filename);
                    $this->errors[] = $msg;
                }
                if (isset($msg) &&  self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage($msg);
                }
            }

        }
        return $conditioning_integration_errors === false ? true : false;
    }

        /**
     * Correction de la reference
     * @param string $before - la propriété du fichier JSON qui a servi de valeur pour la reference
     * @param string $before - la propriété du fichier JSON qui a utlisé comme nouvelle valeur pour la refernce
     * @return true/false selon resultat de l'update
     */
    public function updateReference($before, $after)
    {
        global $config;
        $CorepMapping = new CorepMapping;
        RegisterGCPConnection::init($CorepMapping->id_tenant, true);
        foreach ($this->files as $k => $file) {
            $current_dir = self::_COREP_PROCESSED_FOLDER_.'/'.$file['folder'].'/'.$file['name'];
            if (!file_exists($current_dir.'/sku_data.json')) {
                $error = sprintf('Contenu de l\'archive %1$s invalide (répertoire %2$s). Fichier JSON manquant', $file['name'], $file['folder']);
                $this->errors[] = $error;
                if (self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage($error);
                }
                unset($this->files[$k]);
                $this->moveFileToInvalid($file['folder'], $file['name'].'.zip', true, $current_dir);
            } else {
                $sku_data = json_decode(file_get_contents($current_dir.'/sku_data.json'), true);
                if (!$sku_data || is_null($sku_data)) {
                    $error = sprintf('Contenu du fichier sku_data.json de l\'archive %1$s illisible (répertoire %2$s).', $file['name'], $file['folder']);
                    if (self::_COREP_ENABLE_LOG_ === true) {
                        $this->logMessage($error);
                    }
                    $this->errors[] = $error;
                    unset($this->files[$k]);
                    $this->moveFileToInvalid($file['folder'], $file['name'].'.zip', true, $current_dir);
                    continue;
                } else {
                    $old_reference = $sku_data[$before];
                    $product = ria_mysql_fetch_array(prd_products_get_simple(0, $old_reference));
                    if (!$product['id']) {
                        $error = sprintf('Aucun produit trouvé avec la référence %1$s (propriété %2$s - fichier %3$s - fichier %4$s).', $old_reference, $before, $file['name'], $file['folder']);
                        if (self::_COREP_ENABLE_LOG_ === true) {
                            $this->logMessage($error);
                        }
                    } else {
                        $new_reference = $sku_data[$after];
                        if (!prd_products_update($product['id'], $new_reference, $product['name'])) {
                            $error = sprintf('Echec de la mise-à-jour du produit id %1$s (référence %2$s - fichier %3$s - fichier %4$s).', $product['id'], $new_reference, $file['name'], $file['folder']);
                            if (self::_COREP_ENABLE_LOG_ === true) {
                                $this->logMessage($error);
                            }
                            $this->errors[] = $error;
                        } else {
                            $update_field = fld_object_values_set( $product['id'], $CorepMapping->fields[$before]['id'], $sku_data[$before], $file['lang'], true );
                        }
                    }
                }
            }
        }
    }

    /*** ASSOCIATION PRODUITS/CATEGORIES ***/

    /**
     * Traitement des fichiers : à partir de la liste des fichiers zip trouvés dans le répertoire de téléchargement, on effectue le traitement de l'etape 3 :
     * Associer les produits aux categories (que l'on créée à la volée si elles n'existent pas)
     */
    public function processCategories()
    {
        global $config;
        $CorepMapping = new CorepMapping;
        RegisterGCPConnection::init($CorepMapping->id_tenant, true);
        foreach ($this->files as $k => $file) {
            $current_dir = self::_COREP_PROCESSED_FOLDER_.'/'.$file['folder'].'/'.$file['name'];
            // L'archive a t'elle été dézippée ?
            if (!file_exists($current_dir)) {
                $error = sprintf('Impossible de créer les associations pour le fichier %1$s (répertoire %2$s) car il n\'a pas été dézippé.', $file['name'], $file['folder']);
                $this->errors[] = $error;
                if (self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage($error);
                }
                continue;
            }
            $this->product_categories = []; // On vide la liste des categories a chaque tour de boucle
            // Si le fichier a déjà été traité et que le re-traitement est désactivé on passe au suivant
            if (self::_SKIP_ALREADY_PROCESSED_ && $this->fileAlreadyProcessed($file['name'], $file['folder'])) {
                if (self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage(sprintf('Le fichier %1$s (répertoire %2$s) a déjà été traité pour association.', $file['name'], $file['folder']));
                }
                continue;
            }
            // On verifie qu'on disponse bien d'un fichier json et qu'il est valide
            if (file_exists($current_dir.'/sku_data.json') && ($sku_data = json_decode(file_get_contents($current_dir.'/sku_data.json'), true))) {
                if (!empty($sku_data['categories'])) {
                    foreach ($sku_data['categories'] as $root_cat) {
                        if (!empty($root_cat['children'])) { // On ne traite pas la catégorie racine
                            foreach ($root_cat['children'] as $cat) {
                                $create_categories = $this->createCategoryIfNotExists($cat, $this->cat_root, $file['name'], $file['folder']);
                            }
                        }
                    }
                    if (!empty($this->product_categories)) {
                        $id_product = prd_products_get_id($sku_data['sku']);
                        $associate = prd_products_categories_set($id_product, $this->product_categories);
                    }
                } else {
                    $error = sprintf('Le produit %1$s invalide (répertoire %2$s) n\'a été associé à aucune catégorie car il n\'existe pas de données dans le fichier JSON', $file['name'], $file['folder']);
                    $this->errors[] = $error;
                    if (self::_COREP_ENABLE_LOG_ === true) {
                        $this->logMessage($error);
                    }
                }
            } else {
                $error = sprintf('Contenu de l\'archive %1$s invalide (répertoire %2$s). Au moins 1 des 2 fichiers JSON est manquant', $file['name'], $file['folder']);
                $this->errors[] = $error;
                if (self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage($error);
                }
                unset($this->files[$k]);
                $this->moveFileToInvalid($file['folder'], $file['name'].'.zip', true, $current_dir);
            }
        }
    }

    /**
     * Creation d'une categorie (recursif)
     * @param array $category : un tableau PHP contenant les donnees de la categorie recuperees dans le fichier JSON
     * @param int id_parent : la categorie parente
     * @return true ou false selon si resultat du traitement
     * !!! Important : ici $cat['id'] est l'id provenant des donnees JSON, pas l'id de Riashop
     */
    protected function createCategoryIfNotExists($category, $id_parent, $filename, $folder)
    {
        global $config;
        $existing_category = ria_mysql_fetch_assoc(prd_categories_get(0, false, $id_parent, trim($category['name'])));
        // On commence par verifier si la categorie existe (au niveau actuel), si non on la cree
        if (!$existing_category['id']) {
            $id_new_category = prd_categories_add($category['name'], '', '', $id_parent, true, true, $category['id']);
            if ($id_new_category) {
                $this->product_categories[] = $id_new_category;
                if (self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage(sprintf('Création réussie de la catégorie %1$s de niveau %2$s (fichier %3$s - dossier %4$s)', $category['name'], $category['level'], $filename, $folder));
                }
                if (isset($category['children'])) {
                    foreach ($category['children'] as $child) {
                        $this->createCategoryIfNotExists($child, $id_new_category, $filename, $folder);
                    }
                }
            } else {
                $error = sprintf('Erreur de création de la catégorie %1$s de niveau %2$s (fichier %3$s - dossier %4$s)', $category['name'], $category['level'], $filename, $folder);
                $this->errors[] = $error;
                if (self::_COREP_ENABLE_LOG_ === true) {
                    $this->logMessage($error);
                }
            }
        } else {
            $this->product_categories[] = $existing_category['id'];
            if (isset($category['children'])) {
                foreach ($category['children'] as $child) {
                    $this->createCategoryIfNotExists($child, $existing_category['id'], $filename, $folder);
                }
            }
        }
    }

    /*** FTP ***/

    /*
        Effectue les 2 etapes de connexion de FTPClient : connexion et authentification, puis accede au repertoire contenant les fichiers
    */
    public function connect()
    {
        $this->connector->connect(self::_COREP_FTP_HOST_);
        $this->connector->login(self::_COREP_FTP_LOGIN_, self::_COREP_FTP_PASSWORD_);
        $this->connector->pasv(true);
        $this->connector->chdir(self::_COREP_FTP_FOLDER_);
        return $this;
    }

    /*
        Le repertoire cible existe t'il et contient il des fichiers ?
        @return false si les tests echouent, sinon peuple la propriete "files" (array)
    */
    public function ftpDirectoryExistsAndContainsFiles()
    {
        if (!$this->connector->isDir(self::_COREP_FTP_FOLDER_) || $this->connector->isEmpty(self::_COREP_FTP_FOLDER_)) {
            return false;
        } else {
            return true;
        }
    }

    // Parcours du repertoire FTP pour recuperer les fichiers
    public function getFilesFromFtp()
    {
        $dir_contents = $this->connector->scanDir('.', false);
        // Filtrage du contenu : on ne prend que les fichiers zip et on parcoure tous les sous-répertories "IMPORT_" pour y recuperer les zip
        foreach ($dir_contents as &$content) {
            // On parcoure d'abord la racine...
            if ($content['type'] == 'file' && (substr(strtolower($content['name']), -4) == '.zip')) {
                $this->files['root'][] = $content['name'];
                $this->counters['ftp_files']++;
                // En mode test on ne recupere qu'un seul repertoire pour eviter de saturer l'hebergement et reduire le temps du processus
                if (self::_COREP_IMPORT_TEST_MODE_ === true && $this->counters['ftp_files'] >= 10) {
                    break;
                }
            } else if ($content['type'] == 'directory' && (substr(strtoupper($content['name']), 0, 7) == 'IMPORT_')) {
                // ...puis tous les sous-repertoires (on cree des indexs avec leur nom pour y stocker les noms de fichiers)
                $subdir_files = [];
                $this->connector->chdir($content['name']);
                $subdir_contents = $this->connector->scanDir('.', false);
                foreach ($subdir_contents as $subdir_content) {
                    if ($subdir_content['type'] == 'file' && (substr(strtolower($subdir_content['name']), -4) == '.zip')) {
                        $subdir_files[] = $subdir_content['name'];
                        $this->counters['ftp_files']++;
                    }
                    if (!empty($subdir_files)) {
                        $this->files[$content['name']] = $subdir_files;
                    }
                    // En mode test on ne recupere qu'un seul repertoire pour eviter de saturer l'hebergement et reduire le temps du processus
                    if (self::_COREP_IMPORT_TEST_MODE_ === true) {
                        break;
                    }
                }
                $this->connector->chdir('..');
            }

        }
        if (empty($this->files)) { // Si on n'a collecté aucun fichier on doit stopper le traitement
            return false;
        } else {
            return true;
        }
    }

    // Telechargement de la liste des fichiers identifiés sur le FTP
    // NB : la propriété $files est un tableau avec les fichiers indexés par répertoire
    public function downloadFiles()
    {
        if (!empty($this->files) && ($this->connector instanceof FtpClient) ) {
            foreach($this->files as $dir => $docs) {
                // On remet les fichiers récupérés à la racine à la base de notre répertoire d'import (dossier temporaire)...
                if ($dir == 'root') {
                    foreach ($docs as $doc) {
                        $this->downloadFile($dir, $doc);
                    }
                } else { //... et pour les sous-répertoires on recrée les équivalents pour conserver la meme arborescence (pour avoir un "historique" dans le cas où il faudrait faire du support)
                    $this->connector->chdir($dir);
                    if (!is_dir(self::_COREP_DOWNLOAD_FOLDER_.'/'.$dir)) {
                        mkdir(self::_COREP_DOWNLOAD_FOLDER_.'/'.$dir);
                    }
                    foreach ($docs as $doc) {
                        $this->downloadFile($dir, $doc);
                    }
                    $this->connector->chdir('..');
                }
            }
        } else {
            return false;
        }
    }

    /*
        Telechargement individuel d'un fichier
        @dir string : nom du repertoire
        @doc string : nom du fichier
        @return null (on incremente le compteur de telechargements ou enregistre une erreur si echec)
    */
    public function downloadFile($dir, $doc)
    {
        $path = ($dir == 'root') ? '/' : '/'.$dir.'/';
        if (self::_SKIP_ALREADY_DOWNLOADED_ === true && (file_exists(self::_COREP_DOWNLOAD_FOLDER_.$path.$doc))) {
            if (self::_COREP_ENABLE_LOG_ === true) {
                $this->logMessage(sprintf('Le fichier %1$s (répertoire %2$s) existe déjà.', $doc, $dir));
            }
            $this->counters['downloaded_files']++;
            return null;
        }
        if (self::_SKIP_ALREADY_PROCESSED_ === true && file_exists(self::_COREP_PROCESSED_FOLDER_.$path.$doc)) {
            if (self::_COREP_ENABLE_LOG_ === true) {
                $this->logMessage(sprintf('Le fichier %1$s (répertoire %2$s) a déjà été traité.', $doc, $dir));
            }
            $this->counters['downloaded_files']++;
            return null;
        }
        $download = $this->connector->get(self::_COREP_DOWNLOAD_FOLDER_.$path.$doc, $doc, FTP_BINARY);
        if ($download == FTP_FINISHED) {
            if (self::_COREP_ENABLE_LOG_ === true) {
                $this->logMessage(sprintf('Téléchargement du ficher %1$s (répertoire %2$s) réussi', $doc, $dir));
            }
            $this->counters['downloaded_files']++;
        } else if ($download == FTP_FAILED) {
            $error = sprintf('Le téléchargement du ficher %1$s (répertoire %2$s) a échoué', $doc, $dir);
            if (self::_COREP_ENABLE_LOG_ === true) {
                $this->logMessage($error);
            }
            $this->errors[] = $error;
        }
    }

    /*** DIVERS ***/

    /**
     * Deplacer un fichier zip vers le repertoire "invalide"
     * @param string $folder : le sous-repertoire
     * @param string $file : le nom du fichier (avec l'extension)
     * @param bool Optionnel $remove_dir : suppression du sous-repertoire
     * @param string Optionnel $dir_to_remove : le sous-repertoire à supprimer
     * @return true / false selon resultat
     */
    protected function moveFileToInvalid($folder, $file, $remove_dir = false, $dir_to_remove = null)
    {
        if (!file_exists(self::_COREP_INVALID_FOLDER_.'/'.$folder)) {
            mkdir(self::_COREP_INVALID_FOLDER_.'/'.$folder);
        }
        if ($remove_dir && $dir_to_remove) {
            @rmdir($dir_to_remove);
        }
        return rename(self::_COREP_DOWNLOAD_FOLDER_.'/'.$folder.'/'.$file, self::_COREP_INVALID_FOLDER_.'/'.$folder.'/'.$file);
    }

    /**
     * Verifie si un fichier a deja été traité (par sa presence dans le repotoire "processed")
     * @param $filename : le nom du fichier
     * @param $folder : le sous-dossier
     * @return true/false selon si fichier trouvé ou non
     */
    public function fileAlreadyProcessed($filename, $folder)
    {
        return file_exists(self::_COREP_PROCESSED_FOLDER_.'/'.$folder.'/'.$filename);
    }

    /**
     * Recuperation d'une valeur apres le traitement par un callback du modele qui gere le mapping
     * @param string $value - la valeur à traiter
     * @param string $data - les donnees du fichier JSON sous forme d'array PHP
     * @param object CorepMapping
     * @param string ou array $callback selon si traitement simple ou avec parametres
     * @return string la valeur apres traitement
     */
    public function getValueFromCallback($value, $json_data, CorepMapping $CorepMapping, $callback)
    {
        if (!is_array($callback)) { // callback simple
            $v = $CorepMapping->$callback($value);
        } else { // callback complexe (recuperation d'une ou plusieurs valeurs via le chemin i18n > options)
            $method = $callback[0];
            $params = $callback[1];
            $v = $CorepMapping->$method($json_data, $value, $params);
        }
        return $v;
    }

    /*** LOGS ***/

    // Enregistrement d'un message dans le log
    public function logMessage($msg)
    {
        if (empty($msg) || (self::_COREP_ENABLE_LOG_ === false)) {
            return;
        }
        $line = date('d/m/Y  H:i').' - '.$msg.PHP_EOL;
        if (file_put_contents(self::_COREP_LOG_FOLDER_.'/'.$this->operation.'.log', $line, FILE_APPEND) !== false) {
            return true;
        } else {
            throw new Exception('Impossible d\'écrire dans le fichier log.');
        }
    }

    // Preparation du log (si la fonctionnalité est activée) :
    // 1- Création du répertoire s'il n'existe pas
    // 2- Création du fichier s'il n'existe pas
    // 3- Archivage du log courant s'il est trop volumineux
    protected function prepareLog()
    {
        if (self::_COREP_ENABLE_LOG_ !== true) {
            return;
        }
        if (!file_exists(self::_COREP_LOG_FOLDER_)) {
            mkdir(self::_COREP_LOG_FOLDER_, 0755);
        }
        if (!file_exists(self::_COREP_LOG_FOLDER_.'/'.$this->operation.'.log')) {
            $this->createLogFile();
        } else {
            if (filesize(self::_COREP_LOG_FOLDER_.'/'.$this->operation.'.log') > 2000000) {
                $this->archiveLogFile();
            }
        }
    }

    // Creation d'un nouveau log vierge
    protected function createLogFile()
    {
        if ($new_file = fopen(self::_COREP_LOG_FOLDER_.'/'.$this->operation.'.log', "w")) {
            fwrite($new_file, "");
            fclose($new_file);
            return true;
        } else {
            throw new Exception('Impossible de créer le nouveau fichier log');
        }
    }

    // Archivage du fichier log actuel, puis creation d'un nouveau log vierge
    protected function archiveLogFile()
    {
        if (rename(self::_COREP_LOG_FOLDER_.'/'.$this->operation.'.log', self::_COREP_LOG_FOLDER_.'/import'.date("Y-m-d_His").'.log')) {
            return $this->createLogFile();
        } else {
            throw new Exception('Impossible d\'archiver le fichier log');
        }
    }

}
