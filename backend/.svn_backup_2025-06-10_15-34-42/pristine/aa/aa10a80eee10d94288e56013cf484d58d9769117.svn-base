<?php
	require_once('brands.inc.php');
	
	if( !isset($_GET['brd']) || !prd_brands_exists($_GET['brd']) ){
		$g_error = _("La marque donnée en paramètre n'existe pas.")." "._("Veuillez vérifier cette information.");
	}else{
		$brd = ria_mysql_fetch_array( prd_brands_get($_GET['brd']) );
	}

	define('ADMIN_PAGE_TITLE', _('Marque') . ' - ' . _('Informations'));
	define('ADMIN_HEAD_POPUP', true);
	define('ADMIN_ID_BODY', 'popup-content');
	require_once('admin/skin/header.inc.php');

	if( isset($g_error) ){
		print '<div class="error">'.nl2br( $g_error ).'</div>';
	}else{
		print '
			<table class="cheklists">
				<caption>'._('Informations sur la marque').'</caption>
				<col width="150" /><width="400" />
				<thead>
					<tr>
						<th colspan="2">'._('Informations').'</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>'._('Désignation :').'</td>
						<td>'.htmlspecialchars( $brd['name'] ).'</td>
					</tr>
					<tr>
						<td>'._('Titre :').'</td>
						<td>'.htmlspecialchars( $brd['title'] ).'</td>
					</tr>
					<tr>
						<td>'._('Description :').'</td>
						<td class="popup-info-desc">'.str_replace( '<p>&nbsp;</p>', '', $brd['desc'] ).'</td>
					</tr>
					<tr>
						<td>'._('Produit publiés :').'</td>
						<td>'.number_format( $brd['products'], 0 , '.', ' ' ).'</td>
					</tr>
					<tr>
						<td>'._('Publiée ?').'</td>
						<td>'.( $brd['publish'] ? _('Oui') : _('Non') ).'</td>
					</tr>
				</tbody>
			</table>
		';
	}

	require_once('admin/skin/footer.inc.php');	
