<?php

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_RETURN_VIEW');
	
	require_once('orders.inc.php');
	require_once('ord.returns.inc.php');
	
	$_GET['ret'] = isset($_GET['ret']) ? $_GET['ret'] : 0;
	
	// s'il s'agit du formulaire de consultation, on vérifie que l'identifiant de retour existe
	if( $_GET['ret'] > 0 && !ord_retuns_exists($_GET['ret']) ){
		header('Location: /admin/orders/index.php');
		exit;
	}

	// s'il s'agit du formulaire de création, on vérifie l'identifiant de commande
	if( $_GET['ret'] == 0 && (!isset($_GET['ord']) || !ord_orders_exists($_GET['ord'])) ){
		header('Location: /admin/orders/index.php');
		exit;
	}

	// information sur le retour
	$ret = array( 'id'=>'0', 'date'=>'', 'states_id'=>'1', 'user_id' => 0 );
	if( $_GET['ret']>0 ){
		$rret = ord_returns_get( $_GET['ret'] );
		if( !$rret || !ria_mysql_num_rows($rret) ){
			header('Location: /admin/orders/index.php');
			exit;
		}
		
		$ret = ria_mysql_fetch_array( $rret );
	}
	
	// Détermine si le tarif TTC doit être arrondi (suivant si le client est HT ou non).
	$prc = $config['default_prc_id'];
	if( $ret['user_id'] ){
		if( $rusr = gu_users_get($ret['user_id']) ){
			if( $usr_data = ria_mysql_fetch_array($rusr) ){
				$prc = $usr_data['prc_id'];
			}
		}
	}

	$round_ttc = prd_prices_categories_get_ttc($prc);

	// Détermine l'arrondi à appliquer au TTC
	$round_dec = isset($config['round_digits_count_header']) && $config['round_digits_count_header'] > -1 ? $config['round_digits_count_header'] : $config['round_digits_count'];

	$current = $ret['states_id'];
	// information sur la commande
	$ord_id = $_GET['ret']==0 ? $_GET['ord'] : $ret['ord_id'];
	$order = ria_mysql_fetch_array( ord_orders_get(0, $ord_id) );
	
	// information sur le compte client
	$ruser = gu_users_get( $order['user'] );
	if( !$ruser || !ria_mysql_num_rows($ruser) ){
		header('Location: /admin/orders/index.php');
		exit;
	}
	$user = ria_mysql_fetch_array( $ruser );
	
	// vérifie que l'état de la commande permet bien de créer un retour
	if( $_GET['ret']==0 && !in_array($order['state_id'], ord_returns_orders_states_allowed_get()) ){
		header('Location: /admin/orders/index.php');
		exit;
	}
	
	// Modification retour
	if (isset($_POST['return-submit'], $_POST['next-state'])) {
		// vérifications étape
		if ($_POST['next-state'] == ORD_RETURNS_STATE_QUERY) {
			if (!isset($ret['products']) || $ret['products'] == 0) $error = _('Vous devez ajouter au moins un produit à votre retour.');
		}
		if (! isset($error)) {
			$files = null;
			if (isset($_FILES['prepaid-label'])) {
				if ($_FILES['prepaid-label']['error'] == UPLOAD_ERR_NO_FILE) $error = _('Une erreur s\'est produite lors du téléchargement de l\'image.');
				else $files = $_FILES['prepaid-label']['tmp_name'];
			}
			if( !ord_returns_update($ret['id'], $_POST['next-state']) ){
				$error = _('Une erreur s\'est produite lors de la modification.');
				if ($_POST['next-state'] == ORD_RETURNS_STATE_RETURNED) $error .= _(' Vérifiez que tous les produits sont bien réceptionnés.');
				else if ($_POST['next-state'] == ORD_RETURNS_STATE_COMPLETE) $error .= _(' Vérifiez que tous les produits ont été traités.');
			}
			else {
				ord_returns_update_status( $ret['id'], $_POST['next-state'], $files, false );
				header('Location: return.php?ret='.$ret['id']);
				exit;
			}
		}
	}
	
	// récupère les raisons
	$rreasons = ord_returns_reasons_get();
	$reasons = array();
	if ($rreasons && ria_mysql_num_rows($rreasons) > 0) {
		while ($dat = ria_mysql_fetch_array($rreasons)) {
			$reasons[ $dat['id'] ] = $dat;
		}
	}
	
	// récupère les modes
	$rmodes = ord_returns_modes_allowed_get();
	$modes = array();
	if ($rmodes && ria_mysql_num_rows($rmodes) > 0) {
		while ($dat = ria_mysql_fetch_array($rmodes)) {
			$modes[ $dat['id'] ] = $dat;
		}
	}
	
	// récupère les états
	$rstates = ord_returns_states_get();
	$states = array();
	if ($rstates && ria_mysql_num_rows($rstates) > 0) {
		while ($dat = ria_mysql_fetch_array($rstates)) {
			$states[ $dat['id'] ] = $dat;
		}
	}
	
	// récupère les états produit
	$rstates = ord_returns_products_states_get();
	$prdstates = array();
	if ($rstates && ria_mysql_num_rows($rstates)) {
		while ($dat = ria_mysql_fetch_array($rstates)) {
			$prdstates[ $dat['id'] ] = $dat;
		}
	}
	
	// Ajouter des produits au retour
	if (isset($_POST['return-keeped-add'])) {
		$id_ret = $ret['id'];
		if( !isset($_POST['qte']) || !isset($_POST['reason']) || !isset($_POST['mode']) || !isset($_POST['ord_id']) ){
			$error = _("Une ou plusieurs informations obligatoires sont manquantes.");
		} elseif( $_POST['reason']=='other' && (!isset($_POST['message']) || !trim($_POST['message'])) ){
			$error = _("Veuillez préciser la raison de votre retour.");
		} else {
			$sum_qte = 0;
			// vérifie si la quantité saisie est juste
			foreach( $_POST['qte'] as $prd=>$qte ){
				if( !trim($qte) || !is_numeric($qte) || $qte<=0 ) continue;
				$title = prd_products_get_name( $prd, true );
				if( !ord_returns_products_verify_qte($_POST['ord_id'], $prd, $qte) ){
					$error = (isset($error) ? $error.'<br />' : '').sprintf(_(' La quantité saisie, pour le produit "%s", ne correspond pas à la quantité livrée moins la quantité faisant déjà l\'objet d\'un retour.'), $title);
				}
				$sum_qte += $qte;
			}
			
			if( !isset($error) && $sum_qte ){
				// création du bon de retour s'il s'agit d'un nouveau
				if( $id_ret == 0 )
					$id_ret = ord_returns_add( $_POST['ord_id'] );
				if( !$id_ret ){
					$error = _("Une erreur inattendue s'est produite lors de la création de votre bon de retour.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
				} else {
					// insertion des produits dans le bon de retour
					$first = ord_returns_products_get_qte_returned( $_POST['ord_id'] ) ? false : true;
					foreach( $_POST['qte'] as $prd=>$qte ){
						if( !trim($qte) || !is_numeric($qte) || $qte<=0 ) continue;
						
						$reason = $_POST['reason']=='other' ? null : $_POST['reason'];
						$message = $_POST['reason']=='other' ? trim($_POST['message']) : '';
						$lot = isset($_POST['number-lot']) && trim($_POST['number-lot']) ? $_POST['number-lot'] : null;
						$dlc = isset($_POST['dlc']) && trim($_POST['dlc']) ? $_POST['dlc'] : null;
						
						if( !ord_returns_products_add($id_ret, $prd, $_POST['mode'], $qte, $reason, $message, $lot, $dlc) ){
							$error = _("Une erreur inattendue s'est produite lors de la création de votre bon de retour.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
							
							// si une erreur s'est produite lors de l'ajout du produit, on supprime le bon de retour
							if( $first )
								ord_returns_del( $id_ret );
						}
					}
				}
			}
		}
		
		if( !isset($error) ){
			header('Location: /admin/orders/returns/return.php?ret='.$id_ret);
			exit;
		}
	}
	
	// Modification de l'état des produits
	if (isset($_GET['return-submit-state'], $_GET['liste'], $_GET['state'])) {
		$liste = explode(',', $_GET['liste']);
		foreach( $liste as $line ){
			$rqte = ord_returns_products_get( 0, $ret['id'], 0, $line );
			if( !$rqte || !ria_mysql_num_rows($rqte) )
				$error = _("Une erreur inattendue s'est produite lors de la mise à jour du statut des produits retournés.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
			else {
				$qte = ria_mysql_result($rqte, 0, 'qte');
				$name = ria_mysql_result($rqte, 0, 'name');
				if( $qte<$_GET['qte'] )
					$error = sprintf(_('La quantité %s saisie, pour le produit %s, ne peut être supérieure à la quantité retournée.'), ( $_GET['state']==ORD_RETURNS_PRD_STATE_FAILURE ? _('refusée') : ($_GET['state']==ORD_RETURNS_PRD_STATE_SUCCESS ? _('acceptée') : '') ), $name);
				// elseif( $qte>$_GET['qte'] )
					
			}
		}
		
		if( !isset($error) ){
			if (! ord_returns_products_update($ret['id'], $liste, false, false, false, $_GET['state'], $_GET['motif']))
				$error = _("Une erreur inattendue s'est produite lors de la mise à jour du statut des produits retournés.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
			else {
				header('Location: return.php?ret='.$ret['id']);
				exit;
			}
		}
	}
	
	// Retirer des produits du retour
	if (isset($_POST['return-returned-del'])) {
		$products = isset($_POST['return-returned-check']) ? $_POST['return-returned-check'] : array();
		foreach( $products as $line=>$val ){
			// vérifie si l'état d'avancement du retour sur le produit permet sa suppression
			$state_prd = ord_returns_products_get_state( $_GET['ret'], $line );
			if( $state_prd!=ORD_RETURNS_PRD_STATE_CREATE )
				$error = _('Seuls les produits qui sont au statut "En cours de création" peuvent être supprimés.');
			elseif( !ord_returns_products_del($_GET['ret'], $line) ) {
				$error = sprintf(_("Une erreur inattendue s'est produite lors de la suppression %s.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur."), ( sizeof($products)>1 ? _('des produits') : _('du produit') ));
			}
		}
		
		if( !isset($error) ) {
			// s'il n'y a plus aucun produit dans le retour, on supprime alors le retour et redirection sur le formulaire d'ajout
			if( !ord_returns_products_get_qte_returned($_POST['ord_id']) ){
				ord_returns_del( $_GET['ret'] );
				header('Location: /admin/orders/returns/return.php?ret=0&ord='.$_POST['ord_id']);
				exit;			
			} else {
				header('Location: /admin/orders/returns/return.php?ret='.$_GET['ret']);
				exit;
			}
		}
	}
	
	if( isset($_GET['valid-prd'], $_POST['line-valid'], $_POST['qte-valid'], $_POST['state-valid'], $_POST['motif-valid']) ){
		
		$_POST['date-valid'] = isset($_POST['date-valid']) ? $_POST['date-valid'] : '';
		
		if( trim($_POST['date-valid'])!='' && !isdate($_POST['date-valid']) )
			$error = _("La date limité de validité de l'avoir n'est pas au format JJ/MM/AAAA.");
		elseif( !ord_returns_products_update_line($_GET['ret'], $_POST['line-valid'], $_POST['qte-valid'], $_POST['state-valid'], $_POST['motif-valid'], $_POST['date-valid']!='' ? $_POST['date-valid'] : false) ){
			$error = _("Une erreur inattendue s'est produite lors de la mise à jour de l'état.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler l'erreur.");
		}
		
		if( !isset($error) ){
			header('Location: /admin/orders/returns/return.php?ret='.$_GET['ret']);
			exit;
		}
	}
	
	if( isset($_POST['cancel']) ){
		if( !ord_returns_update_status($_GET['ret'], ORD_RETURNS_STATE_CANCEL) ){
			$error = _("Une erreur inattendue s'est produite lors de l'annulation du retour.")."\n"._("Veuillez réessayer ou prendre contact pour nous signaler le problème.");
		} else {
			header('Location: /admin/orders/returns/return.php?ret='.$_GET['ret']);
			exit;
		}
	}

	define('ADMIN_PAGE_TITLE', _('Fiche retour').' - '._('Gestion des retours').' - '._('Commandes'));
	require_once('admin/skin/header.inc.php');

	if (isset($error)) print '<div class="error">'.nl2br( $error ).'</div>';
	if (isset($success)) print '<div class="success">'.nl2br( $success ).'</div>';
?>

<h2><?php print _('Fiche retour')?></h2>

<form id="form-return" action="/admin/orders/returns/return.php<?php print isset($_GET['ret']) ? '?ret='.$_GET['ret'].( isset($_GET['ord']) ? '&amp;ord='.$_GET['ord'] : '') : ''; ?>" method="post" enctype="multipart/form-data">
<table class="checklist flol">
	<caption><?php print _('Fiche retour')?></caption>
<?php
	$submits = array();
	switch ($current) {
		case ORD_RETURNS_STATE_CREATE : {
			$submits = array('return-submit' => _('Créer le retour et passer à l\'étape suivante').' »');
			$nextStep = ORD_RETURNS_STATE_QUERY;
			break;
		}
		case ORD_RETURNS_STATE_QUERY : {
			$submits = array('return-submit' => _('Passer à l\'étape &quot;En attente des produits&quot;').' »');
			$nextStep = ORD_RETURNS_STATE_WAIT;
			break;
		}
	}
?>
<tbody>
	<tr>
		<td><?php print _('Numéro de commande :'); ?></td>
		<?php if( gu_user_is_authorized('_RGH_ADMIN_ORDER_EDIT') ){ ?>
		<td><?php print view_ord_is_sync($order).' <a href="/admin/orders/order.php?ord='.$ord_id.'" title="'._('Afficher la fiche de cette commande').'" target="_blank">'.str_pad($ord_id, 8, 0, STR_PAD_LEFT).'</a>'; ?></td>
		<?php }else{ ?>
		<td><?php print view_ord_is_sync($order).' '.str_pad($ord_id, 8, 0, STR_PAD_LEFT); ?></td>		
		<?php } ?>
	</tr>
	<tr>
		<td><?php print _('Date de commande :'); ?></td>
		<td><?php print $order['date']; ?></td>
	</tr>
	<tr>
    	<td><?php print _('Montant des frais de port :'); ?></td>
        <td><?php 
			$port = ord_orders_port_get($order['id'], false); 
			print $port>0 ? number_format($port, 2, ',', ' ').' € '._('HT').' ('.number_format(ord_orders_port_get($order['id'], true), 2, ',', ' ').' € '._('TTC').')' : _('Offert') ;
		?></td>
    </tr>
	<tr>
		<td><?php print _('Numéro de retour :'); ?></td>
		<td><?php print str_pad($ret['id'], 8, 0, STR_PAD_LEFT); ?></td>
	</tr>
    <tr>
		<td><?php print _('Client :'); ?></td>
		<?php if( gu_user_is_authorized('_RGH_ADMIN_CUSTOMER_VIEW') ){ ?>
		<td><?php print view_usr_is_sync($user).' <a href="/admin/customers/edit.php?usr='.$user['id'].'" target="_blank">'.htmlspecialchars($user['title_name'].' '.$user['adr_firstname'].' '.$user['adr_lastname']).'</a>'; ?></td>
		<?php }else { ?>
			<td><?php print view_usr_is_sync($user).' '.htmlspecialchars($user['title_name'].' '.$user['adr_firstname'].' '.$user['adr_lastname']); ?></td>		
		<?php } ?>
	</tr>
	<tr>
		<td><?php print _('Nombre de produits du retour :'); ?></td>
		<td><?php print $_GET['ret']>0 ? $ret['products'] : _('En cours de création'); ?></td>
	</tr>
	<tr>
		<td><?php print _('État du retour :'); ?></td>
		<td><?php
			if( $current == ORD_RETURNS_STATE_COMPLETE ){
				print $states[$current]['name'];
			} else {
				$astates = ord_returns_states_update_get($ret['id']);
				if( !sizeof($astates) ){
					if( isset($states[ $current ]) ){
						print $states[ $current ]['name'];
					}
				}else{
					print _('Actuellement').' : '.ord_returns_states_get_name( $current );
					print '	<select name="next-state" id="next-state">';
					
					foreach( $astates as $tmp ){
						print '	<option value="'.$tmp.'"'.(($current == $tmp) ? ' selected="selected"' : '').'>'.$states[$tmp]['name'].'</option>';
					}

					print '	</select>';
					print '	<input name="return-submit" type="submit" value="'._('Modifier').'" style="font-size: 7.5pt; width: auto" />';
				}
			}
		?></td>
	</tr>
	<?php if ($current == ORD_RETURNS_STATE_QUERY) { ?>
		<tr>
			<td><?php print _('Etiquette prépayée :'); ?></td>
			<td>
				<input type="file" name="prepaid-label" onchange="$('#return-state').val(3)" />
			</td>
		</tr>
	<?php } ?>
</tbody>
<tfoot>
	<tr>
		<td class="align-left"><?php
			if( $_GET['ret']>0 && !in_array($current, array(ORD_RETURNS_STATE_PARTIAL, ORD_RETURNS_STATE_COMPLETE, ORD_RETURNS_STATE_CANCEL)) ){
				print '	<input type="submit" name="cancel" id="cancel" value="'._('Annuler le retour').'" title="'._('Annuler le retour').'" />';
			}
		?></td>
		<td><?php 
			if (count($submits)) {
				foreach ($submits as $name => $value) print '<input type="submit" name="'.$name.'" value="'.$value.'" />';
				if (isset($nextStep)) print '<input name="next-state" type="hidden" value="'.$nextStep.'" />';
			}
		?></td>
	</tr>
</tfoot>
</table>
<?php
	if( $current >= ORD_RETURNS_STATE_WAIT && !in_array($current, array(ORD_RETURNS_STATE_COMPLETE,ORD_RETURNS_STATE_CANCEL)) ) 
		print '<div class="flol" style="font-size: 8pt; padding-left: 20px; width: 400px">'._('Afin de passer aux étapes suivantes, vous devez confirmer que tous les produits concernés par ce bon de retour ont bien été réceptionnés.').'</div>';
?>
<div class="clr"></div>
<input id="return-id" name="return-id" type="hidden" value="<?php print $ret['id']; ?>" />
</form>

<?php
	if ($current == ORD_RETURNS_STATE_CREATE) {
		$rpord = ord_products_get( $ord_id );
		$products = array();
		if( $rpord ){
			while( $pord = ria_mysql_fetch_array($rpord) ){
				if( in_array($pord['ref'], $config['dlv_prd_references']) ) continue;
				$products[] = $pord;
			}
		}
	?>
	<form action="/admin/orders/returns/return.php<?php print isset($_GET['ret']) ? '?ret='.$_GET['ret'].( isset($_GET['ord']) ? '&amp;ord='.$_GET['ord'] : '') : ''; ?>" method="post">
	<input name="ord_id" type="hidden" value="<?php print $ord_id; ?>" />
	<input id="current-state" type="hidden" value="<?php print $current; ?>" />
	<h3><?php print _('Produits de la commande')?></h3>
	<p class="notice">
		<?php print _('Vous trouverez dans cette liste tous les produits de la commande pouvant être retournés et n\'ayant pas déjà fait l\'objet d’un retour.')?>
	</p>
	<table class="checklist large">
		<caption><?php printf(_('Produits de la commande (%d)'), sizeof( $products ))?></caption>
	<?php
		if( sizeof($products) ){
			$total_ttc = 0;
			$price_promo = ord_products_get_with_promotion($ord_id);
			foreach ($products as $dat) {
				$dat['price_ttc'] = isset($price_promo[ $dat['id'].'-'.$dat['line'] ]) ? $price_promo[ $dat['id'].'-'.$dat['line'] ]['price_ttc'] : $dat['price_ttc'];
				
				if( $round_ttc ){
					$dat['price_ttc'] = round( $dat['price_ttc'], $round_dec);
				}

				$total = $dat['price_ttc'] * $dat['qte'];
				$total_ttc += $total;
			}
	?>
		<thead>
			<tr>
				<th><?php print _('Référence')?></th>
				<th><?php print _('Désignation')?></th>
				<th><?php print _('Délai restant')?></th>
				<th><?php print _('Prix Unitaire')?></th>
				<th><?php print _('Quantité')?></th>
				<th><?php print _('Total')?></th>
			</tr>
		</thead>
	<?php
		}
	?>
	<tbody>
	<?php
		if( !sizeof($products) )
			print '<tr><td colspan="6">'._('Aucun produit').'</td></tr>';
		else {
			foreach ($products as $p) {
				$days = ord_returns_can_create_for_product( $order['id'], $p['id'], ORD_RETURNS_STATE_CANCEL );
				// quantité du produit
				$qte_returned = ord_returns_products_get_qte_returned( $order['id'], $p['id'], ORD_RETURNS_STATE_CANCEL );
				$qte_delivred = ord_returns_products_get_qte_delivred( $order['id'], $p['id'] );
				
				$p['price_ttc'] = isset($price_promo[ $p['id'].'-'.$p['line'] ]) ? $price_promo[ $p['id'].'-'.$p['line'] ]['price_ttc'] : $p['price_ttc'];
				
				if( $round_ttc ){
					$p['price_ttc'] = round( $p['price_ttc'], $round_dec);
				}

				$cat = ria_mysql_fetch_array(prd_products_categories_get($p['id']));
				$total = $p['price_ttc'] * $p['qte'];
				
				print '<tr>';
					if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ){
						print '<td><a href="/admin/catalog/product.php?prd='.$p['id'].'&amp;cat='.$cat['cat'].'" target="_blank">'.htmlspecialchars($p['ref']).'</a></td>';
					}else{
						print '<td>'.htmlspecialchars($p['ref']).'</td>';						
					}
					print '<td>'.htmlspecialchars($p['name']).'</td>';
					print '<td>';
					$autorized = true;
					switch( intval($days) ){
						case -1 :
							printf(_('Le délai de %d jour%s, permettant de retourner ce produit, est dépassé'), $config['returns_delay'], ( $config['returns_delay']>1 ? 's' : '' ));
							break;
						case -2 :
							print _('Les retours ne sont pas autorisés sur ce produit.');
							break;
						case -3 :
							$autorized = false;
							print _('La totalité des produits livrés ont déjà tous été retournés.');
							break;
						default :
							printf(_('Il reste normalement <b>%d</b> jour%s pour effectuer un retour sur ce produit.'), $days, ( $days>1 ? 's' : '' ));
							break;														
					}
					print '	</td>
							<td class="align-center">'.number_format($p['price_ttc'], 2, ',', ' ').'&euro;</td>
							<td class="align-center">';
					print _('Quantité livrée').' :'.( $qte_delivred ? $qte_delivred : '0' );
					print '<br />'._('Quantité retournée').' :'.( $qte_returned ? $qte_returned : '0' );
					if( $autorized && ($qte_delivred-$qte_returned)>0 ){
						print '<br /><input id="amount-box-'.$p['id'].'" name="qte['.$p['id'].']" type="text" style="width: 50px; text-align: right" value="'.( isset($_POST['qte'][$p['id']]) ? $_POST['qte'][$p['id']] : '0' ).'" />';
					}
					print '	</td>
							<td class="align-center">'.number_format($total, 2, ',', ' ').'&euro;</td>
						</tr>';
			}
		}
	?>
	<?php
		if( sizeof($products) ){
	?>
		<tr>
			<td colspan="6">
				<table class="checklist large">
				<tbody>
					<tr>
						<td class="col200px">
							<?php print _('Raison du retour')?><br />
							<a href="/admin/config/returns/index.php" target="_blank" style="font-size: 7.5pt">[<?php print _('Éditer la liste')?>]</a>
						</td>
						<td><?php 
							foreach( $reasons as $id=>$reason ){
								print '	<div>
											<input id="reason-keeped-'.$id.'" type="radio" name="reason" value="'.$id.'" style="width: auto" '.( isset($_POST['reason']) && $_POST['reason']==$id ? 'checked="checked"' : '' ).' />
											<label for="reason-keeped-'.$id.'">'.htmlspecialchars($reason['name']).'</label>
										</div>';
							} ?>
							<div><input id="reason-keeped-other" type="radio" name="reason" value="other" <?php print isset($_POST['reason']) && $_POST['reason']=='other' ? 'checked="checked"' : ''; ?> style="width: auto" /> <label for="reason-keeped-other"><?php print _('Autre'); ?></label></div>
						</td>
					</tr>
					<tr>
						<td><label for="message-keeped"><?php print _('Message')?></label></td>
						<td>
							<textarea id="message-keeped" name="message" cols="20" rows="5" style="height: auto"><?php print isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
						</td>
					</tr>
					<tr>
						<td>
							<?php print _('Mode de retour')?><br />
							<a href="/admin/config/returns/index.php" target="_blank" style="font-size: 7.5pt">[<?php print _('Éditer la liste')?>]</a>
						</td>
						<td>
							<?php
								foreach ($modes as $id => $mode) {
									print '
									<div>
										<input id="mode-keeped-'.$id.'" type="radio" name="mode" value="'.$id.'"'.(isset($_POST['mode']) && $_POST['mode']==$id ? ' checked="checked"' : '').' style="width: auto" />
										<label for="mode-keeped-'.$id.'">'.htmlspecialchars($mode['name']).'</label>
									</div>';
								}
								if( !sizeof($modes) ){
									print '<span class="color-failure">'._('Vous n\'avez autorisé aucun mode de retour.').' <a href="/admin/config/returns/index.php" target="_blank" style="font-size: 7.5pt">['._('Éditer la liste').']</a></span>';
								}
							?>
						</td>
					</tr>
					<tr>
						<td><label for="reason-keeped-lot"><?php print _('Numéro de lot :'); ?></label></td>
						<td><input id="reason-keeped-lot" type="text" name="lot" style="width: auto" value="<?php print isset($_POST['lot']) ? htmlspecialchars($_POST['lot']) : ''; ?>" /></td>
					</tr>
					<?php
						if ($ret['states_id'] == ORD_RETURNS_STATE_CREATE) {
					?>
						<tr>
							<td><label for="reason-keeped-dlc"><?php print _('Date limite de consommation :'); ?></label></td>
							<td><input id="reason-keeped-dlc" type="text" name="dlc" class="date datepicker" style="width: auto" value="<?php print isset($_POST['dlc']) ? htmlspecialchars($_POST['dlc']) : ''; ?>" /></td>
						</tr>
					<?php
						}
					?>
				</tbody>
				</table>
			</td>
		</tr>
	<?php
		}
	?>
	</tbody>
	
	<tfoot>
			<tr>
				<td colspan="6">
					<input type="submit" name="return-keeped-add" value="<?php print _('Ajouter ces produits au retour')?>" style="float: left" />
					<div class="float-right"><b><?php print number_format(round($total_ttc, 2), 2, ',', ' '); ?>&euro;</b></div>
				</td>
			</tr>
		</tfoot>
	</table>
	</form>
<?php
	}
?>

<?php
	// Produits retournés
	$products = ord_returns_returned_products_get($ret['id']);
	$tproducts = array();
	while ($dat = ria_mysql_fetch_array($products)) $tproducts[$dat['line_id']] = $dat;
	$hasProducts = $count = count($tproducts);
	
	if( $current<ORD_RETURNS_STATE_RETURNED ){
?>
<form id="form-returned" action="/admin/orders/returns/return.php<?php print isset($_GET['ret']) ? '?ret='.$_GET['ret'].( isset($_GET['ord']) ? '&amp;ord='.$_GET['ord'] : '') : ''; ?>" method="post">
<?php } ?>
<input name="ord_id" type="hidden" value="<?php print $ord_id; ?>" />
<h3><?php print _('Produits du retour')?></h3>
<p class="notice">
	<?php print _('Cette liste contient tous les articles concernés par le retour.')?>
</p>
<table id="table-return-returned" class="checklist large">
	<caption><?php print _('Produits du retour'); ?> (<?php print $count; ?>)</caption>
<?php
	$total_ttc = 0;
	if ($hasProducts) {
		foreach( $tproducts as $dat ){
			if( $dat['state_id'] != 5 ){
				if( $round_ttc ){
					$dat['price_ttc'] = round( $dat['price_ttc'], $round_dec);
				}

				$total_ttc += $dat['price_ttc'] * $dat['qte'];
			}
		}
?>
	<thead>
		<tr>
			<th id="ret-prd-check"><input id="return-returned-check-all" type="checkbox" style="width: auto" /></th>
			<th id="ret-prd-ref"><?php print _('Référence')?></th>
			<th id="ret-prd-desc"><?php print _('Désignation')?></th>
			<th id="ret-prd-ttc"><?php print _('Prix unitaire')?> <abbr title="<?php print _('Toutes Taxes Comprises')?>"><?php print _('TTC')?></abbr></th>
			<th id="ret-prd-qte"><?php print _('Quantité')?></th>
			<th id="ret-prd-comp"><?php print _('Compléments')?></th>
			<th id="ret-prd-etat"><?php print _('État du retour')?></th>
			<th id="ret-prd-total"><?php print _('Total')?> <abbr title="<?php print _('Toutes Taxes Comprises')?>"><?php print _('TTC')?></abbr></th>
		</tr>
	</thead>
<?php
	}
?>
	<tbody>
	<?php
		if (! $hasProducts) print '<tr><td colspan="9">'._('Aucun produit retourné').'</td></tr>';
		else {
			foreach ($tproducts as $dat) {
				$cat = ria_mysql_fetch_array(prd_products_categories_get($dat['id']));
				
				if( $round_ttc ){
					$dat['price_ttc'] = round( $dat['price_ttc'], $round_dec);
				}

				print '	<tr class="prd-row">
							<td headers="ret-prd-check">';
				if( in_array($current, array( ORD_RETURNS_STATE_PARTIAL, ORD_RETURNS_STATE_COMPLETE )) ){
				} else {
					print '		<input name="return-returned-check['.$dat['line_id'].']" id="return-returned-check-'.$dat['line_id'].'" type="checkbox" style="width: auto" />';
				}
				print '		</td>';
				if( gu_user_is_authorized('_RGH_ADMIN_CATALOG_PRODUCT_VIEW') ){
					print '	<td headers="ret-prd-ref"><a href="/admin/catalog/product.php?prd='.$dat['id'].'&amp;cat='.$cat['cat'].'" target="_blank">'.htmlspecialchars($dat['ref']).'</a></td>';
				}else{
					print '	<td headers="ret-prd-ref">'.htmlspecialchars($dat['ref']).'</td>';				
				}
				print '		<td headers="ret-prd-desc">'.htmlspecialchars($dat['name']).'</td>
							<td headers="ret-prd-ttc" align="center">'.number_format($dat['price_ttc'], 2, ',', ' ').'</td>
							<td headers="ret-prd-qte" align="center">'.$dat['qte'].'</td>
							<td headers="ret-prd-comp">
								'._('Raison').' : '.( $dat['reason_id']==0 ? htmlspecialchars($dat['reason_detail']) : htmlspecialchars($reasons[$dat['reason_id']]['name']) );
				print '			<br />'._('Mode de retour').' : '.htmlspecialchars($modes[$dat['mode_id']]['name']);
				print '			<br />'._('Numéro de lot').' : '.htmlspecialchars($dat['lot']);
				print '			<br /><abbr title="'._('Date limite de consommation').'">'._('DLC').' : </abbr>'.($dat['dlc'] ? dateheureunparse($dat['dlc'], false) : '');
				print '		</td>
							<td headers="ret-prd-etat">';
				$state = ''._('État actuel').' : '.$prdstates[$dat['state_id']]['name'].'<br /><br />';
				if( $dat['state_id']==ORD_RETURNS_PRD_STATE_RETURNED && in_array($current, array( ORD_RETURNS_STATE_RETURNED, ORD_RETURNS_STATE_PARTIAL )) ){
					$state .= '	<div class="valid-prd">
									<form action="/admin/orders/returns/return.php?ret='.$_GET['ret'].'&amp;valid-prd=1" method="post">
										<input type="hidden" name="line-valid" id="line-valid-'.$dat['line_id'].'" value="'.$dat['line_id'].'" />
										<div class="state-valid">
											<label for="state-valid-'.$dat['line_id'].'">'._('Accepter/Refuser le produit').' :</label>
											<select style="width: auto;" name="state-valid" id="state-valid-'.$dat['line_id'].'" onchange="$(this).val()==5 ? $(\'#motif-valid-'.$dat['line_id'].'\').show() : $(\'#motif-valid-'.$dat['line_id'].'\').hide();">
												<option value="'.ORD_RETURNS_PRD_STATE_SUCCESS.'">'._('Accepté').'</option>
												<option value="'.ORD_RETURNS_PRD_STATE_FAILURE.'">'._('Refusé').'</option>
											</select>
										</div>
										<div class="qte-valid">
											<label for="qte-valid-'.$dat['line_id'].'">'._('Quantiée acceptée/refusée').' :</label>
											<input class="qte" type="text" name="qte-valid" id="qte-valid-'.$dat['line_id'].'" value="'.$dat['qte'].'" />
										</div>';
					if( $dat['mode_id']==ORD_RETURNS_MODE_AVOIR ){
						$state .= '		<div class="qte-valid">
											<label for="date-valid-'.$dat['line_id'].'">'._('Fin de validité de l\'avoir').' :</label>
											<input class="date datepicker" type="text" name="date-valid" id="date-valid-'.$dat['line_id'].'" value="" />
										</div>';
					}
					$state .= '			<div class="motif-valid" id="motif-valid-'.$dat['line_id'].'" style="display:none;">
											<label for="motif-valid-'.$dat['line_id'].'">'._('Motifs').' :</label>
											<textarea name="motif-valid" id="motif-valid-'.$dat['line_id'].'" cols="50" rows="3"></textarea>
										</div>
										<input type="submit" name="save-valid-prd" id="save-valid-'.$dat['line_id'].'" value="'._('Enregistrer').'" />
									</form>
								</div>';
				} elseif( $dat['state_id'] == ORD_RETURNS_PRD_STATE_SUCCESS ){
					$state = '<span style="color: green">'.$state.'</span>';
				} elseif( $dat['state_id'] == ORD_RETURNS_PRD_STATE_FAILURE ){
					$state = '<span style="color: red">'.$state.'</span>';
					if ($dat['motif']) {
						$state .= '<br /><br /><strong>'._('Motif&nbsp;').':</strong><br /><span id="return-returned-motif-'.$dat['line_id'].'">'.str_replace("\n", '<br />', htmlspecialchars($dat['motif'])).'</span>';
					}
				}
				print $state;
				print '			<input id="return-returned-state-'.$dat['line_id'].'" type="hidden" value="'.$dat['state_id'].'" />
							</td>
							<td align="right" headers="ret-prd-total">';
				$total = number_format($dat['price_ttc']*$dat['qte'], 2, ',', ' ').'&euro;';
				if( $dat['state_id'] == ORD_RETURNS_PRD_STATE_FAILURE ){
					$total = '<span style="text-decoration: line-through">'.$total.'</span>';
				}
				print '			'.$total;
				print '		</td>
						</tr>';
			}
		}
	?>
	</tbody>
	<tfoot>
		<tr>
			<td colspan="4">
				<div class="align-left">
					<?php 
						if (in_array($current, array( ORD_RETURNS_STATE_CREATE )) ) 
							print '	<input id="return-returned-del" type="submit" name="return-returned-del" value="'._('Retirer ces produits du retour').'" style="float: left" />';
					?>
				</div>
				<?php
					if( !in_array($current, array( ORD_RETURNS_STATE_CREATE, ORD_RETURNS_STATE_QUERY, ORD_RETURNS_STATE_COMPLETE, ORD_RETURNS_STATE_RETURNED )) ){
						$tstates = array();
						switch( $current ){
							case ORD_RETURNS_PRD_STATE_CREATE		:	break;
							case ORD_RETURNS_PRD_STATE_QUERY		:	break;
							case ORD_RETURNS_PRD_STATE_WAIT			:	$tstates = array(ORD_RETURNS_PRD_STATE_RETURNED, ORD_RETURNS_PRD_STATE_WAIT); break;
							case ORD_RETURNS_PRD_STATE_RETURNED		:
							case ORD_RETURNS_PRD_STATE_FAILURE		:
							case ORD_RETURNS_PRD_STATE_SUCCESS		:	$tstates = array(ORD_RETURNS_PRD_STATE_FAILURE, ORD_RETURNS_PRD_STATE_SUCCESS); break;
						}
						
						if( is_array($tstates) && sizeof($tstates) ){
							print '	<label for="return-returned-state" style="width: 71px; float: left;">Action :</label>
									<select id="return-returned-state" name="state" style="width: auto; float: left; margin-left: 5px; margin-right: 5px;">';
							foreach( $tstates as $stateID ){
								print '	<option value="'.$stateID.'" '.( $stateID==ORD_RETURNS_PRD_STATE_SUCCESS ? 'selected="selected"' : '' ).'>'.$prdstates[$stateID]['name'].'</option>';
							} 
							print '	</select>
									<input id="return-returned-submit-state" type="button" value="'._('Modifier').'" style="float: left" />';
						}
					}
				?>
			</td>
			<td colspan="5">
				<div class="float-right"><b><?php print number_format($total_ttc, 2, ',', ' '); ?>&euro;</b></div>
			</td>
		</tr>
	</tfoot>
</table>
<?php 
	if( $current<ORD_RETURNS_STATE_RETURNED ){
		print '
			</form>
		';
	}

	require_once('admin/skin/footer.inc.php');
?>