<?php
require_once('comparators.inc.php');
require_once('ctr.rueducommerce.inc.php');
require_once('tsk.comparators.inc.php');

/**	\brief Cette classe facile l'utilisation de l'API Mirakl utilisée par Rue du Commerce
 */
class RdC_Mirakl {

	public $sandbox 		= false; ///< S'agit-il de conditions de développement (false) ou de production (true)

	private $api_url 		= 'https://mirakl-api-preprod.groupe-rueducommerce.fr/api';
	private $api_key		= '';

	private $user 			= 0; ///< Identifiant du compte utilisateur Rue du Commerce dans RiaShop
	private $dlvExpMax 		= 3; ///< Délai maximum que peut prendre l'expédition d'une commande (ne pas tenir compte du délai de livraison).
	private $errors 		= array(); ///< Tableau d'erreurs rencontrées durant l'éxécution
	private $sizesImage		= array(); ///< Tailles des images
	private $tmp_attr		= array(); ///< Attributs renseignés sur la fiche produit
	private $tmp_cat_attr 	= array(); ///< Attributs renseignés sur les catégories de produits
	private $export_prds	= array(); ///< Produits exportés
	/*	Debug vars	*/
	public $iteration_count = 0;
	public $prd_count		= 0;
	public $xml_success_count= 0;
	public $xml_error_count	= 0;
	public $no_stock		= 0;


	/**	Constructeur
	 *	@param $load_user Facultatif, ce paramètre n'est pas utilisé
	 *	@param $sandbox Facultatif, détermine s'il s'agit de conditions de production (false, valeur par défaut) ou de développement (true)
	 *	@warning Le paramètre $load_user n'est pas utilisé, il est à supprimer
	 */
	public function __construct( $load_user=true, $sandbox=false ){
		global $config;

		$this->sandbox = $sandbox;
		$this->errors = array();

		$this->sizesImage= $config['img_sizes']['rueducommerce'];

		$params = ctr_params_get_array( CTR_RUEDUCOMMERCE_MIRAKL, array('USR_ID', 'dlv_max_exp') );
		if( is_array($params) && sizeof($params) ){
			if( isset($params['USR_ID']) ){
				$this->user = $params['USR_ID'];
			}

			if( isset($params['dlv_max_exp']) ){
				$this->dlvExpMax = $params['dlv_max_exp'];
			}
		}

		if( !isset($config['mirakl_preprod']) || $config['mirakl_preprod'] ){
			$this->api_url = 'https://mirakl-api-preprod.groupe-rueducommerce.fr/api';
		}else{
			$this->api_url = 'https://mirakl-api.groupe-rueducommerce.fr/api';
		}

		$this->api_key = $config['mirakl_api_key'];
	}

	public function __destruct(){}

	/** Cette fonction est chargé d'afficher les erreurs qui se seraient produite dans l'utilisation d'un objet de la classe.
	 */
	public function showAllErrors(){
		if( $this->sandbox ){
			print_r( $this->errors );
		}else{
			if( is_array($this->errors) && sizeof($this->errors) ){
				global $config;
				// Module RiaShoppping plus suivi, plus d'envoi de message
			}
		}
	}

	/** Cette fonction génère le XML du catalogue produit export sur Rue du Commerce
	 *	@param $ignore_stock_error Facultatif, ignore les erreurs de stock, permet l'envoi même si le stock est vide
	 *	@param $ignore_mandatory_errors Facultatif, paramètres passés à generatedProductXML()
	 *	@param $xml_name string, détermine le nom du fichier XML généré
	 *	@return bool True en cas de succès, False dans le cas contraire
	 */
	public function generatedCatalogProduts( $ignore_stock_error=false, $ignore_mandatory_errors=false, $xml_name='products' ){
		$xml  = '<?xml version="1.0" encoding="UTF-8"?>'."\n";
		$xml .= '<import>'."\n";
		$xml .= '	<products>'."\n";

		$ar_products = RdC_Mirakl::getAllProducts( false );

		$this->prd_count = count($ar_products);

		if( is_array($ar_products) ){
			foreach( $ar_products as $one_product ){

				$this->iteration_count++;

				$tmp_prd = RdC_Mirakl::generatedProductXML( $one_product , $ignore_stock_error, $ignore_mandatory_errors );
				if($tmp_prd === false){
					$this->xml_error_count++;
				}
				elseif($tmp_prd === -1){
					$this->no_stock++;
				}
				if( trim($tmp_prd) != '-1' && trim($tmp_prd) != '' ){
					$xml .= $tmp_prd;
					$this->export_prds[] = $one_product['product']['id'];
				}
			}
		}

		$xml .= '	</products>'."\n";
		$xml .= '</import>'."\n";

		//	Debug vars
		if( $this->sandbox ){
			echo "iterations ".$this->iteration_count;
			echo "\nprd count ".$this->prd_count;
			echo "\nxml success count ".$this->xml_success_count;
			echo "\nxml error count ".$this->xml_error_count;
			echo "\npas de stock ".$this->no_stock;
		}
		return RdC_Mirakl::createCatalogFile( $xml, $xml_name );
	}

	/** Cette fonction génère le XML du catalogue produit export sur Rue du Commerce
	 *	@return bool True en cas de succès, False dans le cas contraire
	 */
	public function generatedCatalogOffers(){
		$ar_products = RdC_Mirakl::getAllProducts( true );

		$xml = '';

		if( is_array($ar_products) ){
			foreach( $ar_products as $one_product ){
				$tmp = RdC_Mirakl::generatedOfferXML( $one_product );

				if( $tmp === false || trim($tmp) == '' || $tmp == '-1' ){
					continue;
				}

				$xml .= $tmp;
			}
		}

		return $xml;
	}

	/** Cette fonction est chargée de générer le XML d'export d'un article
	 *	@param $data Obligatoire, tableau contenant toutes les informations sur un article permettant son export
	 *	@param $ignore_stock_error optionnel, ignore les erreurs de stock, permet l'envoi même si le stock est vide
	 *	@param $ignore_mandatory_errors optionnel, ignore les champs obligatoires, permet l'envoi même s'ils sont incomplets
	 *	@return Le code XML si aucune erreur
	 *	@return bool False dans le cas d'une erreur
	 *	@return -1 dans le cas d'une erreur ne pouvant être corrigé que par l'administrateur
	 */
	public function generatedProductXML( $data , $ignore_stock_error=false, $ignore_mandatory_errors=false ){

		if( $ignore_mandatory_errors === false ){
			if( !ria_array_key_exists(array('product', 'images', 'family-id', 'family-name'), $data) ){
				return false;
			}
		}

		$d_product 	= $data['product'];
		$d_images	= $data['images'];

		if( $ignore_stock_error === false ){
			if( !is_numeric($d_product['stock']) || $d_product['stock'] <= 0 ){
				return -1;
			}
		}

		if( $ignore_mandatory_errors === false ){
			if( trim($d_product['brd_title']) == '' ){
				$this->errors[] = "Marque inconnue pour l'article : ".$d_product['ref']." - ".$d_product['name'];
				return false;
			}

			if( trim($d_product['barcode']) == '' ){
				$this->errors[] = "Le code barre est vide pour l'article : ".$d_product['ref']." - ".$d_product['name'];
				return false;
			}

			if( !is_numeric($d_product['img_id']) || $d_product['img_id'] <= 0 ){
				$this->errors[] = "Aucune image pour l'article : ".$d_product['ref']." - ".$d_product['name'];
				return false;
			}

			if( trim($data['family-code']) == '' ){
				$this->errors[] = "Aucune famille de RdC n'a été définie : ".$d_product['ref']." - ".$d_product['name'];
				return false;
			}
		}
		global $config;

		$image_url = $config['img_url'].'/'.$this->sizesImage['width'].'x'.$this->sizesImage['height'].'/#param[image id]#.'.$this->sizesImage['format'];

		if( !is_numeric($d_product['garantie']) || $d_product['garantie'] <= 0 ){
			$d_product['garantie'] = '';
		}

		$all_attr = array();
		if( !array_key_exists($data['family-id'], $this->tmp_cat_attr) ){
			$r_params = ctr_cat_fields_get( CTR_RUEDUCOMMERCE_MIRAKL, $data['family-id'] );
			if( $r_params && ria_mysql_num_rows($r_params) ){
				while( $param = ria_mysql_fetch_assoc($r_params) ){
					$all_attr[] = $param;
				}
			}

			$this->tmp_cat_attr[ $data['family-id'] ] = $all_attr;
		}else{
			$all_attr = $this->tmp_cat_attr[ $data['family-id'] ];
		}

		global $config;

		$xml  = '<product>'."\n";
		$xml .= '	<article_sku><![CDATA['.$d_product['ref'].']]></article_sku>'."\n";
		$xml .= '	<brand><![CDATA['.strtoupper2( $d_product['brd_title'] ).']]></brand>'."\n";
		$xml .= '	<brand_reference></brand_reference>'."\n";
		$xml .= '	<category><![CDATA['.$data['family-code'].']]></category>'."\n";
		$xml .= '	<ean13><![CDATA['.$d_product['barcode'].']]></ean13>'."\n";
		$xml .= '	<short_title><![CDATA['.$d_product['title'].']]></short_title>'."\n";
		$xml .= '	<long_title><![CDATA['.$d_product['title'].']]></long_title>'."\n";
		$xml .= '	<short_description><![CDATA['.$d_product['desc'].']]></short_description>'."\n";
		$xml .= '	<full_description><![CDATA['.$d_product['desc-long'].']]></full_description>'."\n";
		$xml .= '	<group_code></group_code>'."\n";
		$xml .= '	<media_notice_url></media_notice_url>'."\n";
		$xml .= '	<media_url_1><![CDATA['.str_replace( '#param[image id]#', $d_product['img_id'], $image_url ).']]></media_url_1>'."\n";

		$i = 2;
		foreach( $d_images as $one_img ){
			if( $i > 5 ){
				break;
			}

			$xml .= '			<media_url_'.$i.'><![CDATA['.$config['img_url'].'/'.$this->sizesImage['width'].'x'.$this->sizesImage['height'].'/'.str_replace( '#param[image id]#', $d_product['img_id'], $one_img ).'.'.$this->sizesImage['format'].']]></media_url_'.$i.'>'."\n";
			$i++;
		}

		$xml .= '	<warranty>'.$d_product['garantie'].'</warranty>'."\n";

		if( sizeof($all_attr) ){
			foreach( $all_attr as $one_attr ){
				$val = array_key_exists( $one_attr['code'], $data['attr'] ) ? $data['attr'][ $one_attr['code'] ] : '';
				if( trim($val) == '' ){
					$val = ctr_cat_field_values_get_default( CTR_RUEDUCOMMERCE_MIRAKL, $one_attr['id'], $one_attr['code'], $d_product );
				}

				// 3 	Nombre entier
				// 4 	Nombre à virgule flottante
				// 5 	Liste de choix à sélection unique
				// 6 	Liste de choix à sélection multiple
				// 12 	Liste de choix hiérarchique multiple

				if( $ignore_mandatory_errors === false ){
					if( $one_attr['mandatory'] && trim($val) == '' ){
						return false;
					}
				}

				// Traitement spécial pour certains attributs (par exemple lié aux dimensions ou aux poids)
				switch ($one_attr['code']) {
					case 'dimensions_length_cm':
						if( trim($val) == '' ){
							$val = $d_product['length'];
						}
						break;
					case 'dimensions_height_cm':
						if( trim($val) == '' ){
							$val = $d_product['height'];
						}
						break;
					case 'dimensions_width_cm':
						if( trim($val) == '' ){
							$val = $d_product['width'];
						}
						break;
					case 'weight_g':
						if( trim($val) == '' ){
							$val = is_numeric($d_product['weight']) && $d_product['weight'] > 0 ? $d_product['weight'] : $d_product['weight_net'];
						}
						break;
				}

				$error = false;
				// Contrôle le type de données attendu
				switch ($one_attr['type_id']) {
					case FLD_TYPE_INT: // Nombre entier
						if( !is_int($val) ){
							$error = true;
							$this->errors[] = "Le type de donné INT, pour l'attribut ".$one_attr['code']." est faux (".$val.") : ".$d_product['ref']." - ".$d_product['name'];
						}
						break;
					case FLD_TYPE_FLOAT: // Nombre à virgule flottante
						$val = str_replace( array(',', ' '), array('.', ''), $val );
						if( !is_numeric($val) ){
							$error = true;
							$this->errors[] = "Le type de donné FLOAT, pour l'attribut ".$one_attr['code']." est faux (".$val.") : ".$d_product['ref']." - ".$d_product['name'];
						}
						break;
				}

				$xml .= '			<'.$one_attr['code'].'>'.$val.'</'.$one_attr['code'].'>'."\n";
			}
		}
		if( $error ){
			return false;
		}

		$this->xml_success_count++;

		$xml .= '</product>'."\n";
		return $xml;
	}

	/** Lit les erreurs dans la génération du XML et renvoie la chaîne d'erreur appropriée
	 *	@param $retour le retour de generatedOfferXML ou generatedProductXML
	 *	@return "" si erreur, $retour si tout est ok
	 */
	public function processXMLGeneratorErrors($retour){
		if($retour == false || $retour == -1){
			//	Il y a eu une erreur de génération du XML
			if($retour == false){
				return "erreur de champs obligatoires";
			}
			elseif($retour == -1){
				return "pas de stock";
			}
		}
		return "";
	}


	/** Permet d'envoyer le catalogue de produits
	 *	@param $xml_name le suffixe du nom du fichier xml à lire, doit être le même que pour generatedCatalogProducts()
	 *	@return L'ID de l'import reçu en retour de la requête d'envoi
	 */
	public function sendCatalogProducts( $xml_name = "products"){
		global  $config;
		$dirname = $config['ctr_dir'].'/'.md5( $config['tnt_id'].$config['date-created'] ).'/';
		$file = $dirname.'rdc-mirakl-'.$xml_name.'.xml';

		//$data = array( 'file'=>'@/var/www/refonte-aetc.maquettes.riastudio.fr/htdocs/pages/shopbots/340d2679f94d8b3b4f6ebb44a663957c/rdc-mirakl-'.$xml_name.'.xml' );
		$data = array( 'file'=> '@'.$file, 'filename' => 'rdc-mirakl-'.$xml_name.'.xml' );

		$handle = curl_init( $this->api_url.'/products/imports?api_key='.$this->api_key );
		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_SAFE_UPLOAD, false);
		curl_setopt($handle, CURLOPT_POSTFIELDS, $data);
		curl_setopt($handle, CURLINFO_HEADER_OUT, true);
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, 1);
		$response =  curl_exec($handle);
		curl_close($handle);

		if( $this->sandbox ){
			print $response;
		}
		$ar = json_decode( $response );
		$result = array();
		foreach ($ar as $key => $value) {
			$result[ $key ] = $value;
		}

		if( !isset($result['import_id']) ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
			return false;
		}

		return $result["import_id"];

	}

	/** Permet d'envoyer le catalogue d'offres (add offer)
	 *	@param $xml_name Facultatif, le suffixe du nom du fichier xml à lire, doit être le même que pour generatedCatalogProducts() ou createCatalogFile()
	 *	@return L'ID de l'import reçu en retour de la requête d'envoi
	 */
	public function sendCatalogOffers( $xml_name='products' ){
		//	Envoi via l'API mirakl
		global  $config;
		$dirname = $config['ctr_dir'].'/'.md5( $config['tnt_id'].$config['date-created'] ).'/';
		$file = $dirname.'rdc-mirakl-'.$xml_name.'.xml';

		$data = array( 'file'=> '@'.$file, "import_mode" => "REPLACE", 'filename' => 'rdc-mirakl-'.$xml_name.'.xml' );
		$handle = curl_init( $this->api_url.'/offers/imports?api_key='.$this->api_key );
		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_SAFE_UPLOAD, false);
		curl_setopt($handle, CURLOPT_POSTFIELDS, $data);
		curl_setopt($handle, CURLINFO_HEADER_OUT, true);
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, 1);
		$response =  curl_exec($handle);
		curl_close($handle);

		if( $this->sandbox ){
			print $response;
		}
		$ar = json_decode( $response );
		$result = array();
		foreach ($ar as $key => $value) {
			$result[ $key ] = $value;
		}

		if( !isset($result['import_id']) ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
			return false;
		}

		return $result["import_id"];
	}

	/** Génère un bundle d'erreur unique
	 *	@param $error Obligatoire, l'entrée d'erreur à lire
	 *	@param $product Obligatoire, le produit à ajouter dans le bundle d'erreur
	 *	@param string $type Obligatoire, peut être "product" ou "offer"
	 *	@return retourne l'entrée d'erreur unique, avec le produit
	 */
	public function generateErrorCollection( $error, $product, $type ){
		$errors_bundle = array(
			'error_code' => $error['code'],
			'error_desc' =>	$error['desc'],
			'object'	=> $type,
			'products' => array()
		);
		$errors_bundle['products'][] = $product;
		return $errors_bundle;
	}


	/** Récupère le rapport d'import de l'import dont l'id est en paramètre
	 *	@param int $import_id l'id de l'import
	 *	@param string $type peut être "product" ou "offer"
	 *	@return le json du retour parsé ou false si rien n'a été trouvé pour le $import_id courant
	 */
	public function getImportStatus( $import_id, $type ){
		$url = $this->api_url.'/'.$type.'s/imports/'.$import_id.'?api_key='.$this->api_key;
		$handle = curl_init( $url );
		curl_setopt($handle, CURLOPT_HTTPGET, true);
		curl_setopt($handle, CURLINFO_HEADER_OUT, true);
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($handle, CURLOPT_HTTPHEADER, array(
		    'Accept: ' . ' application/json'
		));

		$response =  curl_exec($handle);
		curl_close($handle);
		if( $this->sandbox ){
			print $response;
		}
		$ar = json_decode( $response );

		if( empty($ar) ){
			print "\njson parsing failed";
			return false;
		}
		$result = array();
		foreach ($ar as $key => $value) {
			$result[ $key ] = $value;
		}

		//	Le statut des produits et des offres ne portent pas la même clé, mais peuvent avoir les même valeurs
		//	On settera la clé manquante pour éviter les warnings
		if( !isset($result['import_status']) && isset($result['status']) ){
			$result['import_status'] = $result['status'];
		}
		elseif( isset($result['import_status']) && !isset($result['status']) ){
			$result['status'] = $result['import_status'];
		}

		return $result;
	}


	/** Génère un tableau permettant de déterminer quels rapports sont disponibles pour l'import de l'offre ou du produit
	 *	@param $import_status la sortie de getImportStatus()
	 *	@param string $type peut être "product" ou "offer"
	 *	@return renvoie un tableau avec 3 bool, représentant, dans l'ordre la disponibilité du : rapport d'erreur, rapport de transformation, fichier transformé
	 */
	public function createErrorReportStatus( $import_status, $type ){
		$error_status = array(0=>false, 1=>false, 2=>false);

		//	On traite les états de rapport dans cet ordre
		//	error_report, transformation_error_report, transformed_file
		if( $import_status['has_error_report'] ){
			//	traitement error report
			$error_status[0] = true;
		}
		//	Ces deux rapports n'existent pas pour les offers
		if( $type == "product" ){
			if( $import_status['has_transformation_error_report'] ){
				//	traitement transformation error report
				$error_status[1] = true;
			}
			if( $import_status['has_transformed_file'] ){
				//	traitement fichier transformation (contient les imports réussis)
				$error_status[2] = true;
			}
		}
		return $error_status;
	}

	/** Créé et renvoie le tableau de résultats des rapports d'erreur
	 *	@param int $import_id Obligatoire, l'id d'import
	 *	@param string $type Obligatoire, soit "product" soit "offer"
	 *	@param array $error_status Informations sur les erreurs rencontrées
	 *	@return renvoie un tableau contenant toutes les informations concernant les erreurs à ajouter au tableau de produits $tasked
	 */
	public function createErrorReport( $import_id, $type, $error_status=array(false, false, false) ){
		$return = array();
		switch ($type) {
			case 'product':
				//	Il est impossible que deux rapports différents contiennent le même produit, il n'y aura pas d'écrasement
				if($error_status[0] === true){
					$report = $this->processProductImportReport($import_id);
					foreach($report as $key => $single_error){
						$return[$key] = $single_error;
					}
				}
				if($error_status[1] === true){

					$report = $this->processProductTransformationReport($import_id);
					foreach($report as $key => $single_error){
						$return[$key] = $single_error;
					}
				}
				if($error_status[2] === true){
					$report = $this->processProductTransformationFile($import_id);
					foreach($report as $key => $single_error){
						$return[$key] = $single_error;
					}
				}
				break;

			default:
				//	Traitement offre
				if($error_status[0] === true){
					$report = $this->processOfferImportReport($import_id);
					foreach($report as $key => $single_error){
						$return[$key] = $single_error;
					}
				}
				break;
		}
		return $return;
	}

	/** Récupère un XML d'une ressource accédée par son URL, puis renvoie le XML
	 *	Notamment utilisé pour récupérer les error_report et transformation_error_report
	 *	@param $url l'url de la ressource XML
	 *	@return object un objet de type SimpleXMLElement formé à partir du XML reçu depuis l'URL ou bool false si le retour est une erreur
	 */
	private function parseXMLFromUrl($url){
		$handle = curl_init($url);
		curl_setopt($handle, CURLOPT_HTTPGET, true);
		curl_setopt($handle, CURLINFO_HEADER_OUT, true);
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($handle, CURLOPT_HTTPHEADER, array(
		    'Accept: ' . ' application/xml,text/xml'
		));

		$response =  curl_exec($handle);
		curl_close($handle);
		$import = new SimpleXMLElement( $response );
		if( $this->isXMLReportInError($import) === false){
			return $import;
		}
		else{
			return false;
		}
	}

	/** Prend un SimpleXMLElement en entrée (sortie de parseXMLFromUrl()) et détermine si le retour est une erreur Mirakl
	 *	@param $xmlelement l'élément à analyser
	 *	@return bool true ou false
	 */
	private function isXMLReportInError($xmlelement){
		if( ($xmlelement->getName() == "error") ){
			/*$error_desc = $import->message->__toString();
			$error_status = $import->status->__toString();*/
			return true;
		}
		else{
			return false;
		}
	}

	/** Traite les rapports d'erreur offre en fonction de leur $import_id
	 *	Contrairement à processProductImportReport(), l'API renvoie un XML (au lieu d'un CSV)
	 * 	Le 31/08/2016, l'API renvoie un CSV, le résultat originellement attendu
	 *
	 *	@param int $import_id l'id d'import
	 *	@return renvoie un tableau contenant toutes les informations concernant les erreurs à ajouter au tableau de produits $tasked
	 *	Peut retourner des lignes en erreur ou en réussite
	 */
	public function processOfferImportReport( $import_id ){

		$csv = $this->parseCSVFromUrl($this->api_url.'/offers/imports/'.$import_id.'/error_report?api_key='.$this->api_key);

		$return = array();
		//	$csv est false si le XML récupéré est une erreur renvoyée par l'API
		//	Si $return est renvoyé vide, il n'y aura pas d'erreur ajoutée au tableau d'erreurs
		if($csv !== false){
			foreach($csv as $line){
				//	Les rapports d'import ne rapportent qu'une seule erreur et ne possèdent pas de code erreur
				$error_lines = array( array(
						'code' 	=> 	"NOCODE",
						'desc'	=>	$line['error-message']
					)
				);

				$return[ $line['sku'] ] = array(
					"in_error"	=>	true,
					"error"		=>	$error_lines
				);
			}
		}
		return $return;
	}


	/** Traite le rapport d'erreur de transformation de l'import sélectionné par son ID
	 *	@param int $import_id Obligatoire, l'id de l'import dont il faut récupérer le rapport de transformation
	 *	@return renvoie un tableau contenant les erreurs des produits, trié par prd_ref
	 */
	public function processProductTransformationReport( $import_id ){

		$import = $this->parseXMLFromUrl( $this->api_url.'/products/imports/'.$import_id.'/transformation_error_report?api_key='.$this->api_key );

		$return = array();
		//	$import est false si le XML récupéré est une erreur renvoyée par l'API
		//	Si $import est renvoyé vide, il n'y aura pas d'erreur ajoutée au tableau d'erreurs
		if($import !== false){
			foreach($import as $product){
				//	On génère le tableau de codes d'erreur à renvoyer
				$error_lines = explode(",", $product->{'errors'}->__toString() );
				for($j = 0; $j < count($error_lines); $j++){
					$temp = explode("|", $error_lines[$j]);
					unset($error_lines[$j]);
					$error_lines[$j]['code'] = $temp[0];
					$error_lines[$j]['desc'] = $temp[1];
				}

				$return[ $product->{'article_sku'}->__toString() ] = array(
					"in_error"	=>	true,
					"error"		=>	$error_lines
				);
			}
		}
		return $return;
	}


	/** Traite les rapports d'erreur produit en fonction de leur $import_id
	 *	@param int $import_id l'id d'import
	 *	@return renvoie un tableau contenant toutes les informations concernant les erreurs à ajouter au tableau de produits $tasked
	 */
	public function processProductImportReport($import_id){

		$url = $this->api_url.'/products/imports/'.$import_id.'/error_report?api_key='.$this->api_key;

		$csv = $this->parseCSVFromUrl($url);

		$return = array();
		//	$csv est false si le XML récupéré est une erreur renvoyée par l'API
		//	Si $return est renvoyé vide, il n'y aura pas d'erreur ajoutée au tableau d'erreurs
		if($csv !== false){
			foreach($csv as $line){
				//	Les rapports d'import ne rapportent qu'une seule erreur et ne possèdent pas de code erreur
				$error_lines = array( array(
						'code' 	=> 	$line['error_code'],
						'desc'	=>	$line['error_message']
					)
				);

				$return[ $line['sku'] ] = array(
					"in_error"	=>	true,
					"error"		=>	$error_lines
				);
			}
		}
		return $return;
	}


	/** Traite le rapport de transformation, contient tous les imports réussis
	 *	@param int $import_id l'id de l'import dont il faut récupérer le fichier de transformation
	 *	@return renvoie un tableau contenant toutes les informations à ajouter au tableau de produits $tasked (seulement "in_error" => false dans ce cas)
	 */
	public function processProductTransformationFile($import_id){
		$url = $this->api_url.'/products/imports/'.$import_id.'/transformed_file?api_key='.$this->api_key;

		$csv = $this->parseCSVFromUrl($url);
		$return = array();
		foreach($csv as $line){
			$return[ $line['article_sku'] ] = array(
				"in_error"	=>	false
			);
		}
		return $return;
	}

	/** Parse les CSV envoyés depuis une URL, traite et ignore les CRLF à l'intérieur des champs
	 *	@param $url l'url de la ressource
	 *	@return array $csv le csv parsé depuis la ressource en ligne
	 *	Le CSV récupéré peut être un XML s'il y a eu une erreur retournée par l'API
	 */
	public function parseCSVFromUrl($url){
		$csvData = file_get_contents($url);

		//	Si le fichier est un XML, c'est une erreur de l'API, on retournera false
		if(substr($csvData, 0, 5) == "<?xml"){
			return false;
		}

		//	Regex de gestion des CRLF à l'intérieur du texte
		$lines = preg_split('/[\r\n]{1,2}(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))/',$csvData);

		//	On extrait le header du csv
		$header = array_shift($lines);
		$header = str_getcsv($header, ";", '"', "\n");


		foreach ($lines as $line) {
			if( !empty($line) ){
				$temp = str_getcsv($line, ";", '"', "\n");
				$temp = $this->rename_keys($temp, $header);
				$csv[] = $temp;
			}
		}
		return $csv;
	}

	/** Permet de renommer les clés d'un tableau à partir des valeurs d'un autre tableau
	 *	Notamment utilisé pour réindexer les valeurs d'un CSV dans parseCSVFromUrl()
	 *	Un warning est issued si les deux arguments n'ont pas la même taille
	 * @param array $array le tableau contenant les valeurs à réindexer
	 * @param array $replacement_keys le tableau contenant les nouveaux indexes
	 * @return array le tableau avec ses indexes renommés
	 */
	private function rename_keys($array, $replacement_keys)  {
		return array_combine($replacement_keys, array_values($array));
	}

	/** Cette fonction est chargée de générer les offres de ventes sur un article
	 * 	La documentation de l'API ne l'indique pas mais l'ajout d'offre par fichier (OF01) accepte les XML
	 *	@param $data Obligatoire, tableau contenant toutes les informations sur un article permettant son export
	 * 	@param $ignore permet d'ignorer les erreurs (de stock notamment), pour les tests
	 * 	@param $update_delete permet de paramètrer l'entrée d'offre, en "update" ou "delete"
	 *	@return Le code XML si aucune erreur
	 *	@return bool False dans le cas d'une erreur
	 *	@return -1 dans le cas d'une erreur ne pouvant être corrigé que par l'administrateur
	 */
	public function generatedOfferXML( $data , $ignore = false, $update_delete = "update"){
		if( !ria_array_key_exists(array('product', 'images', 'family-id', 'family-name'), $data) && !$ignore){
			return false;
		}

		global $config;

		$d_product 	= $data['product'];
		$d_promo	= $data['promo'];

		if (trim($d_product['barcode']) == '') {
			return false;
		}

		if( !is_numeric($d_product['stock']) || $d_product['stock'] <= 0 ){
			$d_product['stock'] = 0;
		}

		$xml  = '		<offer>'."\n";
		$xml .= '			<sku><![CDATA['.$d_product['ref'].']]></sku>'."\n";
		$xml .= '			<product-id><![CDATA['.$d_product['barcode'].']]></product-id>'."\n";
		$xml .= '			<product-id-type>EAN</product-id-type>'."\n";
		$xml .= '			<internal-description><![CDATA['.$d_product['name'].']]></internal-description>'."\n";
		$xml .= '			<price>'.number_format( $d_product['price_ttc'], 2 , '.', '' ).'</price>'."\n";
		$xml .= '			<quantity>'.$d_product['stock'].'</quantity>'."\n";
		$xml .= '			<state>11</state>'."\n";

		if( is_array($d_promo) && sizeof($d_promo) && $d_promo['price_ttc'] < $d_product['price_ttc'] ){
			$xml .= '			<available-start-date>'.$d_promo['date-start-en'].'</available-start-date>'."\n";
			$xml .= '			<available-end-date>'.$d_promo['date-end-en'].'</available-end-date>'."\n";
			$xml .= '			<discount-price>'.number_format( $d_promo['price_ttc'], 2 , '.', '' ).'</discount-price>'."\n";
		}

		if( isset($config['mirakl_logistic_class']) && trim($config['mirakl_logistic_class']) != '' ){
			$xml .= '				<logistic-class>'.$config['mirakl_logistic_class'].'</logistic-class>'."\n";
		}

		if($update_delete == "delete"){
			$xml .= '			<update-delete>delete</update-delete>'."\n";
		}
		else{
			$xml .= '			<update-delete></update-delete>'."\n";
		}
		$xml .= '			<leadtime-to-ship>'.$this->dlvExpMax.'</leadtime-to-ship>'."\n";

		$xml .= '		</offer>'."\n";

		return $xml;
		/* Balise dans l'export des articles au format XML
			O 	 	<sku></sku>
			O 	 	<product-id></product-id>
			O 	 	<product-id-type>EAN / SHOP_SKU / SKU</product-id-type>
			N 	 	<internal-description></internal-description>
			O 	 	<price>En TTC</price>
			N 	 	<quantity></quantity>
			N 	* 	<min-quantity-alert></min-quantity-alert>
			O 	 	<state>11</state>
			N 	* 	<available-start-date></available-start-date>
			N 	* 	<available-end-date></available-end-date>
			N 	* 	<logistic-class></logistic-class>
			N 	 	<discount-start-date></discount-start-date>
			N 	 	<discount-end-date></discount-end-date>
			N 	 	<discount-price>Prix remisé en TTX</discount-price>
			N 	 	<update-delete>update / delete</update-delete>
			O 	* 	<leadtime-to-ship>Délai expédition</leadtime-to-ship>
			N 	* 	<offer-additional-fields></offer-additional-fields>
				N 	* 	<offer-additional-field></offer-additional-field>
		 */
	}

	/** Cette fonction génère les entrées d'offre en JSON (le XML n'est pas accepté)
	 *	@param $data Obligatoire, tableau contenant toutes les informations sur un article permettant son export
	 * 	@param $ignore permet d'ignorer les erreurs (de stock notamment), pour les tests
	 * 	@param $update_delete permet de paramètrer l'entrée d'offre, en "update" ou "delete"
	 *	@return Le tableau que l'on peut passer à json_encode() pour obtenir le JSON d'une offre
	 */
	public function generatedOfferJSONArray( $data , $ignore = false, $update_delete = ""){
		if( !ria_array_key_exists(array('product', 'images', 'family-id', 'family-name'), $data) && !$ignore){
			return false;
		}

		//	Il est possible qu'un $data provienne de prd_products_get_simple, qui ne renvoie pas de tableau product, mais directement le produit
		//	Son $d_promo est donc vide
		$d_product 	= isset($data['product']) ? $data['product'] : $data;
		$d_promo 	= isset($data['promo']) ? $data['promo'] : array();

		if( !$ignore && (!is_numeric($d_product['stock']) || $d_product['stock'] <= 0) ){
			return -1;
		}

		if( is_array($d_promo) && count($d_promo) ){
			$d_product['price_ht'] = $d_promo['price_ht'];
			$d_product['price_ttc'] = $d_promo['price_ttc'];
		}

		global $config;

		$array_offer_additional_fields = array();
		$array_offer_additional_fields[] = array(
			"code"	=>	"code",
			"value"	=>	"blah"
		);

		$json_array = array(
			"available_ended"			=>	null,
			"available_started"			=>	null,
			"description"				=>	$d_product['desc-long'],
			"discount"					=>	array(
				"end_date"					=>	null,
				"price"						=>	null,
				"start_date"				=>	null
			),
			"internal_description"		=>	$d_product['desc'],
			//"logistic_class"			=>	"A",
			"min_quantity_alert"		=>	null,
			"offer_additional_fields"	=>	array(),
			"price"						=>	$d_product['price_ttc'],
			"price_additional_info"		=>	null,
			"product_id"				=>	null,
			"product_id_type"			=>	null,
			"quantity"					=>	$d_product['stock'],
			"shop_sku"					=>	$d_product['ref'],
			//"state_code"				=>	"state_code",
			"update_delete"				=>	$update_delete
		);

		return $json_array;
	}

	/** Envoie un string xml correctement formé à l'API mirakl, pour un ajout d'offres (OF1)
	 *	On créé un fichier .xml dans htdocs/pages/shopbots/-md5-/rdc-mirakl-offers-add.xml
	 *	On passera par l'autre API pour les update/delete (OF24) avec sendOfferJSON()
	 *	@param $xml_content Obligatoire, xml d'import d'offre contenant les offres
	 *	@return $import_id l'import ID des nouvelles offres
	 */
	public function sendOfferXML( $xml_content ){
		//	Attention, pour ajouter une offre, le XML doit contenir la balise <import> (au singulier)
		$xml  = '<?xml version="1.0" encoding="UTF-8"?>'."\n";
		$xml_add =			'<import>'."\n".
							'	<offers>'."\n";
		$xml_add_close =	'	</offers>'."\n".
							'</import>'."\n";

		$this->createCatalogFile($xml.$xml_add.$xml_content.$xml_add_close, "offers-add");
		//	L'envoi d'offres par fichier XML doit se dérouler comme un envoi/création de catalogue
		return $this->sendCatalogOffers("offers-add");
	}

	/** Envoie un JSON à l'API pour update/delete seulement (OF24)
	 *	@param $json le JSON correctement formé à envoyer
	 *	@return $import_id l'import ID des update/delete envoyés à mirakl
	 */
	public function sendOfferJSON($json){
		//	Permet la reconversion des caractères unicode ( ils ont été html_entities() par json_encode() )
		$unescaped = preg_replace_callback('/\\\u(\w{4})/', function ($matches) {
		    return html_entity_decode('&#x' . $matches[1] . ';', ENT_COMPAT, 'UTF-8');
		}, $json);
		$json = $unescaped;
		if( $this->sandbox ){
			print $json;
		}

		$handle = curl_init( $this->api_url.'/offers?api_key='.$this->api_key );
		curl_setopt($handle, CURLOPT_HTTPHEADER, array(
		    'Content-Type: application/json'
		));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($handle, CURLINFO_HEADER_OUT, true);
		curl_setopt($handle, CURLOPT_CUSTOMREQUEST, "POST");
		curl_setopt($handle, CURLOPT_POSTFIELDS, $json);
		//	Setter le CURLOPT_CONTENT_TYPE ne fait rien ici
		curl_setopt($handle, CURLINFO_CONTENT_TYPE, "application/json");

		$response = curl_exec($handle);

		curl_close($handle);
		if( $this->sandbox ){
			print_r($response);
		}

		$ar = json_decode( $response );
		$result = array();
		foreach ($ar as $key => $value) {
			$result[ $key ] = $value;
		}

		if( !isset($result['import_id']) ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
			return false;
		}

		return $result["import_id"];
	}


	/** Envoie un fichier xml correctement formé à l'API mirakl, pour un ajout de produits (OF24)
	 *	@param $xml_content contient le contenu du xml les produits
	 *	@return $import_id l'import ID de la nouvelle offre ajoutée à mirakl
	 */
	public function sendProductXML($xml_content){
		//	Attention, pour ajouter un produit le XML doit contenir la balise <imports> (au pluriel)
		$xml  = '<?xml version="1.0" encoding="UTF-8"?>'."\n";
		$xml_add_prd =			'<imports>'."\n";
		$xml_add_prd .=			'	<products>'."\n";

		$xml_add_prd_close =	'	</products>'."\n";
		$xml_add_prd_close .=	'</imports>'."\n";

		$this->createCatalogFile($xml.$xml_add_prd.$xml_content.$xml_add_prd_close, "add_prd");
		//	L'envoi d'offres doit se dérouler comme un envoi/création de catalogue
		return $this->sendCatalogProducts("add_prd");
	}

	/** Cette fonction créé un tableau contenant tous les articles exportés sur Rue du Commerce avec toutes les informations nécessaire à leur export
	 *	@param $with_price Optionnel, par défaut les informations de prix ne sont pas incluses dans les données, mettre True pour que ce soit le cas
	 *	@param $prd_id Optionnel, permet de sélectionner un seul produit
	 *	@return array Un tableau contenant les articles et toutes leurs informations
	 */
	public function getAllProducts( $with_price=false, $prd_id = 0){
		if( !is_numeric($prd_id) ){
			$prd_id = 0;
		}
		$ar_products = array();

		$rcatalog = ctr_catalogs_get( CTR_RUEDUCOMMERCE_MIRAKL, $prd_id, 0, true );
		if( $rcatalog && ria_mysql_num_rows($rcatalog) ){
			while( $catalog = ria_mysql_fetch_assoc($rcatalog) ){

				$cat_id = ctr_catalogs_get_categorie( CTR_RUEDUCOMMERCE_MIRAKL, $catalog['prd_id'], false );
				if( !is_numeric($cat_id) || $cat_id <= 0 ){
					continue;
				}

				$rcategory = ctr_categories_get( CTR_RUEDUCOMMERCE_MIRAKL, $cat_id );
				if( !$rcategory || !ria_mysql_num_rows($rcategory) ){
					continue;
				}

				$category = ria_mysql_fetch_assoc( $rcategory );

				$ar_products[ $catalog['prd_id'] ] = array(
					'product' 		=> array(),
					'images'		=> array(),
					'promo'			=> false,
					'family-id'		=> $category['id'],
					'family-code'	=> $category['ref'],
					'family-name'	=> $category['name'],
					'attr'			=> array()
				);

				// Charge les attributs renseignés
				if( trim($catalog['params']) != '' ){
					$ar_attr = json_decode( $catalog['params'], true );

					if( is_array($ar_attr) && count($ar_attr) ){
						foreach( $ar_attr as $key=>$val ){
							$attr = isset($this->tmp_attr[ $key ]) ? $this->tmp_attr[ $key ] : '';
							if( trim($attr) == '' ){
								$attr = ctr_cat_fields_get_code( CTR_RUEDUCOMMERCE_MIRAKL, $key );
								if( trim($attr) != '' ){
									$this->tmp_attr[ $key ] = $attr;
								}
							}

							if( trim($attr) != '' ){
								$ar_products[ $catalog['prd_id'] ]['attr'][ $attr ] = $val;
							}
						}
					}
				}
			}

			if( sizeof($ar_products) ){
				$rproduct = prd_products_get_simple( array_keys($ar_products), '', false, 0, false, false, $with_price, false, array('childs'=>true) );
				if( $rproduct && ria_mysql_num_rows($rproduct) ){
					while( $product = ria_mysql_fetch_assoc($rproduct) ){
						$product['title'] 		= ctr_catalogs_get_prd_title( CTR_RUEDUCOMMERCE_MIRAKL, $product['id'], false, true );
						$product['desc'] 		= ctr_catalogs_get_prd_desc(  CTR_RUEDUCOMMERCE_MIRAKL, $product['id'], false, 100 );
						$product['desc-long']	= ctr_catalogs_get_prd_desc(  CTR_RUEDUCOMMERCE_MIRAKL, $product['id'], false, true );

						$rimg = prd_images_get( $product['id'] );
						if( $rimg && ria_mysql_num_rows($rimg) ){
							while( $img = ria_mysql_fetch_assoc($rimg) ){
								if( !is_numeric($product['img_id']) || $product['img_id'] <= 0 ){
									$product['img_id'] = $img['id'];
									continue;
								}

								$ar_products[ $product['id'] ]['images'][] = $img['id'];
							}
						}

						$ar_products[ $product['id'] ]['product'] = $product;

						if( $with_price ){
							$price_ar = array(
								'price_ht'	=> $product['price_ht'],
								'tva_rate'	=> $product['tva_rate'],
								'price_ttc'	=> $product['price_ttc']
							);

							$promo = prc_promotions_get( $product['id'], $this->user, 0, 1, 0, $price_ar );
							if( is_array($promo) && sizeof($promo) ){
								$ar_products[ $product['id'] ]['promo'] = $promo;
							}
						}
					}
				}

				foreach( $ar_products as $key=>$one_product ){
					if( !sizeof($one_product['product']) ){
						unset( $ar_products[ $key ] );
					}
				}
			}
		}

		return $ar_products;
	}

	/** Cette fonction genère le fichier contenant le XML pour l'export des articles ou de leur offre.
	 *	Si le mode de test est activé alors aucun fichier n'est créé, mais le contenu est affiché.
	 *	@param $content Obligatoire, contenu du fichier
	 *	@return $file le chemin du fichier ajouté, sinon false
	 */
	private function createCatalogFile( $content, $type ){
		if( trim($content) == '' ){
			return false;
		}

		global  $config;


		$dirname = $config['ctr_dir'].'/'.md5( $config['tnt_id'].$config['date-created'] ).'/';
		$file = $dirname.'rdc-mirakl-'.$type.'.xml';
		//	Création du répertoire
		if( !is_dir($dirname) ){
			if ( !mkdir($dirname, 0777, true) ) {
			    die("Erreur dans l'écriture du répertoire ".$dirname);
			}
		}

		$f = fopen( $file, 'w' );
		fwrite( $f, $content );
		fclose( $f );

		return $file;
	}

	public function ctr_mirakl_update_price_and_quantity($ignore = false){
		global $config;

		// change le user_id en session pour récupérer les prix dans prd_products_get_simple()
		$old_session_id = null;
		if( isset($_SESSION['usr_id']) ){
			$old_session_id = $_SESSION['usr_id'];
		}
		if( $this->user ){
			$_SESSION['usr_id'] = $this->user;
		}

		$rctl = ctr_catalogs_get( CTR_RUEDUCOMMERCE_MIRAKL, 0, 0, true );

		$ar_products = array();
		if( $rctl ){
			while( $ctl = ria_mysql_fetch_assoc($rctl) ){
				$ar_products[ $ctl['prd_id'] ] = $ctl;
			}
		}

		if( !sizeof($ar_products) ){
			return true;
		}

		$rproduct = prd_products_get_simple( array_keys($ar_products), '', false, 0, false, false, true, false, array('childs'=>true) );
		if( $rproduct ){
			$ar_upd = array();
			$msg_price = $msg_inventory = '';
			$count_price = $count_inventory = 1;
			$json_array_offers = array();

			while( $product = ria_mysql_fetch_assoc($rproduct) ){
				$ctl_prd = $ar_products[ $product['id'] ];

				$price = array( 'price_ht'=>$product['price_ht'], 'tva_rate'=>$product['tva_rate'], 'price_ttc'=>$product['price_ttc'] );
				if( $price['price_ttc']<=0 ){
					continue;
				}

				$promo = prc_promotions_get( $product['id'], $this->user, 0, 1, 0, array('price_ht' => $product['price_ht'], 'tva_rate' => $product['tva_rate']) );

				if( is_array($promo) && sizeof($promo) ){
					$product['price_ht'] = $promo['price_ht'];
					$product['price_ttc'] = $promo['price_ttc'];
				}

				if( $product['price_ht'] != $ctl_prd['price_ht'] || $product['stock'] != $ctl_prd['qte'] ){
					if($this->sandbox){
						print "\nprd_id :".$product['id']." - offer added for updating\n";
					}

					$json_array_offers[] = $this->generatedOfferJSONArray($product, $ignore, "update");

					$ar_upd[] = $product['id'];
					$count_price++;

				}
			}

			if( count($json_array_offers) ){
				$json = array();
				$json['offers'] = $json_array_offers;
				$new_import_id_offers = $this->sendOfferJSON(json_encode($json));

				if( trim($new_import_id_offers) != '' ){
					$ar_exec = array();
					foreach( $ar_upd as $p ){
						if( ($tsk = tsk_comparators_add( CTR_RUEDUCOMMERCE_MIRAKL, $p, 'update-priceqte' )) ){
							$ar_exec[] = $tsk;
							ctr_catalogs_update_price( CTR_RUEDUCOMMERCE_MIRAKL, $product['id'], $product['price_ht'], $price['tva_rate'] );
							ctr_catalogs_update_quantity( CTR_RUEDUCOMMERCE_MIRAKL, $product['id'], $product['stock'] );
						}
					}

					tsk_comparators_set_completed( $ar_exec );
					tsk_comparators_set_import_id( $ar_exec, $new_import_id_offers );
				}
			}
			else{
				if($this->sandbox){
					print "no offers to update";
				}
			}
		}

		return true;
	}
}

abstract class Mirakl {

	protected $sandbox = false;
	protected $errors;
	protected $api_url;
	protected $ctr_id;
	protected $file_prefixe;
	protected $dirname;
	protected $user 			= 0; ///< Identifiant du compte utilisateur Rue du Commerce dans RiaShop
	protected $dlvExpMax 		= 3; ///< Délai maximum que peut prendre l'expédition d'une commande (ne pas tenir compte du délai de livraison).
	protected $tmp_attr		= array(); ///< Attributs renseignés sur la fiche produit
	protected $tmp_cat_attr 	= array(); ///< Attributs renseignés sur les catégories de produits
	protected $httpcode;
	protected $ctr_name;
	protected $catAttributesFields = array();
	protected $useHierarchyAttributs = false;

	/** Constructeur permet d'initialiser certaine variable */
	public function __construct(){
		global $config;
		$this->dirname = $config['ctr_dir'].'/'.md5( $config['tnt_id'].$config['date-created'] ).'/';
		$params = ctr_params_get_array( $this->ctr_id, array('USR_ID', 'dlv_max_exp') );
		if( is_array($params) && sizeof($params) ){
			if( isset($params['USR_ID']) ){
				$this->user = $params['USR_ID'];
			}

			if( isset($params['dlv_max_exp']) ){
				$this->dlvExpMax = $params['dlv_max_exp'];
			}
		}
		$this->useHierarchyAttributs = ctr_comparators_get_fields_use_hierarchy($this->ctr_id);
	}

	public function getCtrID(){
		return $this->ctr_id;
	}

	/** Cette méthode retourne le code http de la dernière requête réalisé */
	public function getLastHttpCode(){
		return $this->httpcode;
	}

	/** Cette fonction permet de retourner l'identifiant utilisateur associé a la place de marché */
	public function getUserID(){
		return $this->user;
	}
	/** Cette fonction est chargé d'afficher les erreurs qui se seraient produite dans l'utilisation d'un objet de la classe.
	 */
	public function showAllErrors(){
		if( $this->sandbox ){
			print_r( $this->errors );
		}else{
			if( is_array($this->errors) && sizeof($this->errors) ){
				global $config;
				// Module RiaShoppping plus suivi, plus d'envoi de message
			}
		}
	}

	protected function getCatAttributeField($cat_id){

		if( array_key_exists($cat_id, $this->catAttributesFields)){
			return $this->catAttributesFields[$cat_id];
		}

		$cats_id = array($cat_id);
		if ($this->useHierarchyAttributs) {
			$r_cats_id = ctr_categories_parents_get($cat_id);
			if ($r_cats_id && ria_mysql_num_rows($r_cats_id)) {
				while($p_id = ria_mysql_fetch_assoc($r_cats_id)) {
					$cats_id[] = $p_id['id'];
				}
			}
		}
		$r_cat_fields = ctr_cat_fields_get( $this->ctr_id, $cats_id);

		if( !$r_cat_fields || !ria_mysql_num_rows($r_cat_fields) ){
			return array();
		}

		while( $cat_field = ria_mysql_fetch_assoc($r_cat_fields)){
			$this->catAttributesFields[$cat_id][] = $cat_field;
		}

		return $this->catAttributesFields[$cat_id];
	}

	/** Cette fonction permet de créer une requète mirakl
	 *	@param $url Obligatoire, le chemin de l'api à appeler
	 *	@param $method Facultatif, méthode de la requète
	 *	@param $contentType Facultatif, le contenu envoyer
	 *	@param $param Facultatif, les données à envoyer (json, xml, tableau de paramètre)
	 *	@param $headers Facultatif, tableau avec les headers à envoyer
	 *
	 *	@return la réponse de la requête
	 */
	public function miraklRequest( $url, $method='get', $contentType='', $params=array(), $headers=array() ){

		if( !is_string($url) ){
			throw new Exception("Erreur url n'est pas une chaine de caractères.", 1);
		}
		$method = strtolower($method);

		if( !in_array($method, array('get', 'post', 'put')) ){
			throw new Exception("Erreur la methode est incorrect.", 1);
		}

		$handle = curl_init();
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, 1);
		// curl_setopt($handle, CURLOPT_HEADER, 1);
		curl_setopt($handle, CURLINFO_HEADER_OUT, true);
		curl_setopt($handle, CURLOPT_BINARYTRANSFER, 1);

		switch($method){
			case 'post':
				curl_setopt($handle, CURLOPT_POST, true);
				curl_setopt($handle, CURLOPT_SAFE_UPLOAD, false);
				curl_setopt($handle, CURLOPT_POSTFIELDS, $params);
				break;
			case 'get':
				curl_setopt($handle, CURLOPT_HTTPGET, true);
				if( is_array($params) ){
					$query = http_build_query($params);
					$url .= (parse_url($url, PHP_URL_QUERY) ? '&' : '?').$query;
				}
				break;
			case 'put':
				curl_setopt($handle, CURLOPT_CUSTOMREQUEST, 'PUT');
				curl_setopt($handle, CURLOPT_POSTFIELDS, $params);
				break;
		}

		curl_setopt($handle, CURLOPT_URL, $this->api_url.$url);

		$headers = array_merge($headers, array('Authorization: '.$this->api_key));

		if( trim($contentType) != '' ){
			$headers[] = 'Content-Type: '.$contentType;
		}

		if( !empty($headers) ){
			curl_setopt($handle, CURLOPT_HTTPHEADER, $headers);
		}

		$response =  curl_exec($handle);
		$this->httpcode = curl_getinfo ( $handle , CURLINFO_HTTP_CODE );
		curl_close($handle);

		return $response;
	}

	/** Cette fonction génère le XML du catalogue produit export sur Rue du Commerce
	 *	@return bool True en cas de succès, False dans le cas contraire
	 */
	public function generatedCatalogOffers(){
		$ar_products = $this->getAllProducts( true );

		$xml = '';

		if( is_array($ar_products) ){
			foreach( $ar_products as $one_product ){
				$tmp = $this->generatedOfferXML( $one_product );

				if( $tmp === false || trim($tmp) == '' || $tmp == '-1' ){
					continue;
				}

				$xml .= $tmp;
			}
		}
		if( $this->sandbox ){
			var_dump($xml);
		}
		return $xml;
	}

	/** Cette fonction créé un tableau contenant tous les articles exportés sur Rue du Commerce avec toutes les informations nécessaire à leur export
	 *	@param $with_price Optionnel, par défaut les informations de prix ne sont pas incluses dans les données, mettre True pour que ce soit le cas
	 *	@param $prd_id Optionnel, permet de sélectionner un seul produit
	 *	@return array Un tableau contenant les articles et toutes leurs informations
	 */
	public function getAllProducts( $with_price=false, $prd_id = 0){
		if( !is_numeric($prd_id) ){
			$prd_id = 0;
		}
		$ar_products = array();

		$rcatalog = ctr_catalogs_get( $this->ctr_id, $prd_id, 0, true );
		if( $rcatalog && ria_mysql_num_rows($rcatalog) ){
			while( $catalog = ria_mysql_fetch_assoc($rcatalog) ){

				/*$cat_id = ctr_catalogs_get_categorie( $this->ctr_id, $catalog['prd_id'], false );

				if( !is_numeric($cat_id) || $cat_id <= 0 ){
					continue;
				}

				$rcategory = ctr_categories_get( $this->ctr_id, $cat_id );
				if( !$rcategory || !ria_mysql_num_rows($rcategory) ){
					continue;
				}
				$category = ria_mysql_fetch_assoc( $rcategory );*/

				$ar_products[ $catalog['prd_id'] ] = array(
					'product' 		=> array(),
					'images'		=> array(),
					'promo'			=> false,
					'attr'			=> array()
				);

				// Charge les attributs renseignés
				if( trim($catalog['params']) != '' ){
					$ar_attr = json_decode( $catalog['params'], true );

					if( is_array($ar_attr) && count($ar_attr) ){
						foreach( $ar_attr as $key=>$val ){
							$attr = isset($this->tmp_attr[ $key ]) ? $this->tmp_attr[ $key ] : '';
							if( trim($attr) == '' ){
								$attr = ctr_cat_fields_get_code( $this->ctr_id, $key );
								if( trim($attr) != '' ){
									$this->tmp_attr[ $key ] = $attr;
								}
							}

							if( trim($attr) != '' ){
								$ar_products[ $catalog['prd_id'] ]['attr'][ $attr ] = $val;
							}
						}
					}
				}
			}

			if( sizeof($ar_products) ){
				$rproduct = prd_products_get_simple( array_keys($ar_products), '', false, 0, false, false, $with_price, false, array('childs'=>true) );
				if( $rproduct && ria_mysql_num_rows($rproduct) ){
					while( $product = ria_mysql_fetch_assoc($rproduct) ){
						$product['title'] 		= ctr_catalogs_get_prd_title( $this->ctr_id, $product['id'], false, true );
						$product['desc'] 		= ctr_catalogs_get_prd_desc(  $this->ctr_id, $product['id'], false, 100 );
						$product['desc-long']	= ctr_catalogs_get_prd_desc(  $this->ctr_id, $product['id'], false, true );
						$product['cat_id']		= $catalog['cat_id'];

						$rimg = prd_images_get( $product['id'] );
						if( $rimg && ria_mysql_num_rows($rimg) ){
							while( $img = ria_mysql_fetch_assoc($rimg) ){
								if( !is_numeric($product['img_id']) || $product['img_id'] <= 0 ){
									$product['img_id'] = $img['id'];
									continue;
								}

								$ar_products[ $product['id'] ]['images'][] = $img['id'];
							}
						}

						$ar_products[ $product['id'] ]['product'] = $product;

						if( $with_price ){
							$price_ar = array(
								'price_ht'	=> $product['price_ht'],
								'tva_rate'	=> $product['tva_rate'],
								'price_ttc'	=> $product['price_ttc']
							);

							$promo = prc_promotions_get( $product['id'], $this->user, 0, 1, 0, $price_ar );
							if( is_array($promo) && sizeof($promo) ){
								$ar_products[ $product['id'] ]['promo'] = $promo;
							}
						}
					}
				}

				foreach( $ar_products as $key=>$one_product ){
					if( !sizeof($one_product['product']) ){
						unset( $ar_products[ $key ] );
					}
				}
			}
		}
		if( $this->sandbox ){
			var_dump($ar_products);
		}
		return $ar_products;
	}


	/** Cette fonction est chargée de générer les offres de ventes sur un article
	 * 	La documentation de l'API ne l'indique pas mais l'ajout d'offre par fichier (OF01) accepte les XML
	 *	@param $data Obligatoire, tableau contenant toutes les informations sur un article permettant son export
	 * 	@param $ignore permet d'ignorer les erreurs (de stock notamment), pour les tests
	 * 	@param $update_delete permet de paramètrer l'entrée d'offre, en "update" ou "delete"
	 *	@return Le code XML si aucune erreur
	 *	@return bool False dans le cas d'une erreur
	 *	@return -1 dans le cas d'une erreur ne pouvant être corrigé que par l'administrateur
	 */
	public function generatedOfferXML( $data , $ignore = false, $update_delete = "update"){
		if( !ria_array_key_exists(array('product', 'images'), $data) && !$ignore){
			return false;
		}

		global $config;

		$d_product 	= $data['product'];
		$d_promo	= $data['promo'];

		if (trim($d_product['barcode']) == '') {
			return false;
		}

		if( !is_numeric($d_product['stock']) || $d_product['stock'] <= 0 ){
			$d_product['stock'] = 0;
		}

		if( !$d_product['follow_stock'] ){
			$d_product['stock'] = 99;
		}
		$mirakl_prd_id = $d_product['barcode'];
		$id_type = 'EAN';
		if (isset($config['mirakl_offer_id_type']) && in_array($config['mirakl_offer_id_type'], array('EAN', 'SHOP_SKU'))) {
			$id_type = $config['mirakl_offer_id_type'];
			switch($id_type) {
				case 'SHOP_SKU' :
					$mirakl_prd_id = $d_product['ref'];
					break;
				case 'EAN':
				default:
					$mirakl_prd_id = $d_product['barcode'];
					break;
			}
		}

		if ($d_product['sell_weight']) {
			$d_product['price_ttc'] = ($d_product['price_ttc']*$d_product['weight_net']) / 1000;
		}

		$xml  = '		<offer>'."\n";
		$xml .= '			<sku><![CDATA['.$d_product['ref'].']]></sku>'."\n";
		$xml .= '			<product-id><![CDATA['.$mirakl_prd_id.']]></product-id>'."\n";
		$xml .= '			<product-id-type>'.$id_type.'</product-id-type>'."\n";
		$xml .= '			<internal-description><![CDATA['.$d_product['name'].']]></internal-description>'."\n";
		$xml .= '			<price>'.number_format( $d_product['price_ttc'], 2 , '.', '' ).'</price>'."\n";
		$xml .= '			<quantity>'.$d_product['stock'].'</quantity>'."\n";
		$xml .= '			<state>11</state>'."\n";

		if( is_array($d_promo) && sizeof($d_promo) && $d_promo['price_ttc'] < $d_product['price_ttc'] ){
			$xml .= '			<available-start-date>'.$d_promo['date-start-en'].'</available-start-date>'."\n";
			$xml .= '			<available-end-date>'.$d_promo['date-end-en'].'</available-end-date>'."\n";
			$xml .= '			<discount-price>'.number_format( $d_promo['price_ttc'], 2 , '.', '' ).'</discount-price>'."\n";
		}

		if( isset($config['mirakl_logistic_class']) && trim($config['mirakl_logistic_class']) != '' ){
			$xml .= '				<logistic-class>'.$config['mirakl_logistic_class'].'</logistic-class>'."\n";
		}

		if($update_delete == "delete"){
			$xml .= '			<update-delete>delete</update-delete>'."\n";
		}
		else{
			$xml .= '			<update-delete></update-delete>'."\n";
		}
		$xml .= '			<leadtime-to-ship>'.$this->dlvExpMax.'</leadtime-to-ship>'."\n";

		$xml .= '		</offer>'."\n";

		return $xml;
		/* Balise dans l'export des articles au format XML
			O 	 	<sku></sku>
			O 	 	<product-id></product-id>
			O 	 	<product-id-type>EAN / SHOP_SKU / SKU</product-id-type>
			N 	 	<internal-description></internal-description>
			O 	 	<price>En TTC</price>
			N 	 	<quantity></quantity>
			N 	* 	<min-quantity-alert></min-quantity-alert>
			O 	 	<state>11</state>
			N 	* 	<available-start-date></available-start-date>
			N 	* 	<available-end-date></available-end-date>
			N 	* 	<logistic-class></logistic-class>
			N 	 	<discount-start-date></discount-start-date>
			N 	 	<discount-end-date></discount-end-date>
			N 	 	<discount-price>Prix remisé en TTX</discount-price>
			N 	 	<update-delete>update / delete</update-delete>
			O 	* 	<leadtime-to-ship>Délai expédition</leadtime-to-ship>
			N 	* 	<offer-additional-fields></offer-additional-fields>
			N 	* 	<offer-additional-field></offer-additional-field>
		 */
	}

	/** Envoie un string xml correctement formé à l'API mirakl, pour un ajout d'offres (OF1)
	 *	On créé un fichier .xml dans htdocs/pages/shopbots/-md5-/rdc-mirakl-offers-add.xml
	 *	On passera par l'autre API pour les update/delete (OF24) avec sendOfferJSON()
	 *	@param $xml_content Obligatoire, xml d'import d'offre contenant les <c><offer></offer><offer></offer></c>...
	 *	@return $import_id l'import ID des nouvelles offres
	 */
	public function sendOfferXML($xml_content){
		//	Attention, pour ajouter une offre, le XML doit contenir la balise <import> (au singulier)
		$xml  = '<?xml version="1.0" encoding="UTF-8"?>'."\n";
		$xml_add =			'<import>'."\n".
							'	<offers>'."\n";
		$xml_add_close =	'	</offers>'."\n".
							'</import>'."\n";

		$this->createCatalogFile($xml.$xml_add.$xml_content.$xml_add_close, "offers-add");
		//	L'envoi d'offres par fichier XML doit se dérouler comme un envoi/création de catalogue
		$import_id = $this->sendCatalogOffers("offers-add");

		if( $this->sandbox ){
			echo 'setting '.$import_id;
		}
		$this->saveImportID($import_id);

		return $import_id;
	}

	/** Cette fonction permet de sauvegarder l'identifiant d'un import mirakl */
	protected function saveImportID($import_id){
		return fld_object_values_set($this->ctr_id, _FLD_CTR_IMP_ID, $import_id);
	}

	/** Cette fonction permet de récupérer l'identifiant du dernier import mirakl réalisé */
	protected function getLastImportID(){
		return fld_object_values_get( $this->ctr_id, _FLD_CTR_IMP_ID);
	}

	/** Cette fonction genère le fichier contenant le XML pour l'export des articles ou de leur offre.
	 *	Si le mode de test est activé alors aucun fichier n'est créé, mais le contenu est affiché.
	 *	@param $content Obligatoire, contenu du fichier
	 *	@return $file le chemin du fichier ajouté, sinon false
	 */
	protected function createCatalogFile( $content, $type ){
		if( trim($content) == '' ){
			return false;
		}

		global  $config;


		$file = $this->dirname.$this->ctr_name.'-mirakl-'.$type.'.xml';
		//	Création du répertoire
		if( !is_dir($this->dirname) ){
			if ( !mkdir($this->dirname, 0777, true) ) {
			    die("Erreur dans l'écriture du répertoire ".$this->dirname);
			}
		}

		$f = fopen( $file, 'w' );
		fwrite( $f, $content );
		fclose( $f );

		return $file;
	}

	/** Permet d'envoyer le catalogue d'offres (add offer)
	 *	@param $xml_name, le suffixe du nom du fichier xml à lire, doit être le même que pour generatedCatalogProducts() ou createCatalogFile()
	 *	@return L'ID de l'import reçu en retour de la requête d'envoi
	 */
	public function sendCatalogOffers( $xml_name = "products"){
		//	Envoi via l'API mirakl
		global  $config;
		$dirname = $config['ctr_dir'].'/'.md5( $config['tnt_id'].$config['date-created'] ).'/';
		$file = $this->dirname.$this->ctr_name.'-mirakl-'.$xml_name.'.xml';

		$data = array( 'file'=> '@'.$file, "import_mode" => "REPLACE", 'filename' => 'rdc-mirakl-'.$xml_name.'.xml' );

		$response = $this->miraklRequest('/offers/imports','post', '', $data);

		$response = json_decode($response, true);

		if( !isset($response['import_id']) ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
			return false;
		}

		return $response["import_id"];
	}

	/** Permet d'envoyer le catalogue de produit (add offer)
	 *	@param $xml_name, le suffixe du nom du fichier xml à lire, doit être le même que pour generatedCatalogProducts() ou createCatalogFile()
	 *	@return L'ID de l'import reçu en retour de la requête d'envoi
	 */
	public function sendCatalogProducts( $file ){
		//	Envoi via l'API mirakl
		global  $config;

		$data = array( 'file'=> '@'.$file, "import_mode" => "REPLACE", 'filename' => str_replace($this->dirname, '', $file) );

		$response = $this->miraklRequest('/products/imports','post', '', $data);

		$response = json_decode($response, true);
		if( !isset($response['import_id']) ){
			// Module RiaShoppping plus suivi, plus d'envoi de message
			return false;
		}

		return $response["import_id"];
	}

	/** Cette fonction permet de récupérer les commandes réalisé sur la place de marché
	*	@return array un tableau avec les commandes
	*/
	public function getOrders($params=array()){
		$response = $this->miraklRequest('/orders', 'get', '', $params);
		$response = json_decode($response, true);

		return $response['orders'];
	}

	/** Cette fonction permet de valider une commande mirakl
	 *	@param $mirakl_order un tableau contenant une commande mirakl récupérer avec getOrders
	 */
	public function acceptOrder($mirakl_order){

		$data = array('order_lines' => array() );

		foreach( $mirakl_order['order_lines'] as $mirakl_product ){
			$data['order_lines'][] = array(
				'accepted'=> true,
				'id'=>$mirakl_product['order_line_id']
			);
		}
		$json = json_encode($data);

		return $this->miraklRequest('/orders/'.$mirakl_order['order_id'].'/accept', 'put', 'application/json', $json);
	}

	/** Cette fonction permet d'idiquer a mirakl qu'une commande est en cour de livraison
	 *	@param $mirakl_order_id identifiant de la commande j'ai mirakl
	 */
	public function shipOrder($mirakl_order_id){
		return $this->miraklRequest('/orders/'.$mirakl_order_id.'/ship', 'put');
	}

	/** Cette fonction permet d'envoyer les informations de tracking de commande
	 *	@param $mirakl_order_id Identifiant de la commande chez mirakl
	 *	@param $carrier_info tableau associatifs avec les clé :
	 *					- carrier_code => le code du service de livraison
	 *					- carrier_name => le nom du service de livraison
	 *					- carrier_url => l'url de tracking du service de livraison
	 *					- tracking_number => le code de tracking du colis
	 */
	public function trackingOrder($mirakl_order_id, $carrier_info){

		if( !ria_array_key_exists(array('carrier_code', 'carrier_name', 'carrier_url', 'tracking_number'), $carrier_info )){
			return false;
		}
		$json = json_encode($carrier_info);

		return $this->miraklRequest('/orders/'.$mirakl_order_id.'/tracking', 'put', '', $json);
	}
	/** Cette fonction permet de confirmer l'expédition d'une commande
	 *	@param array $order Obligatoire, résultat tel que retourné par ria_mysql_fetch_assoc( ord_orders_get($ord_id) )
	 *	@return bool True si la confirmation s'est correctement déroulée, False dans le cas contraire
	 *	@deprecated sauf erreur, cette méthode n'est pas du tout utilisée
	 */
	public function confirmDeliveryOrder($order){
		global $config;

		if( !is_array($order) || !sizeof($order) ){
			error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Aucune information sur la commande.');
			return false;
		}

		if( !isset($order['id'], $order['state_id']) ){
			error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Toutes les informations sur la commande ne sont pas fournies.');
			return false;
		}

		if( !in_array($order['state_id'], array(6, 7, 8)) ){
			error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le statut "'.$order['state_id'].'" de la commande "'.$order['id'].'" ne permet pas d\'informer eBay de son expédition.');
			return true;
		}

		$shipping = $this->getProductShipping();
		if( !is_array($shipping) || !sizeof($shipping) ){
			error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de récupérer les informations sur le produit frais de port.');
			return false;
		}

		// Récupère le bl
		$r_bl = ord_bl_get( 0, $params['USR_ID'], false, false, false, array(), false, array($order['id']) );
        if( $r_bl && ria_mysql_num_rows($r_bl) ){
            while( $bl = ria_mysql_fetch_assoc($r_bl) ){
	            $r_bl_product = ord_bl_products_get($bl['id'], $order['id']);

	            if( $r_bl_product && ria_mysql_num_rows($r_bl_product) ){

	                while( $bl_product = ria_mysql_fetch_assoc($r_bl_product) ){
	                    if( $bl_product['colis'] != '' ){

	                        $colis = $bl_product['colis'];
	                        break(2);
	                    }
	                }
	            }
	        }
        }

        if( !isset($bl) ){
			error_log('Impossible de confirmer la commande "'.$order['ref'].'" sur truffaut car le BL n\'existe pas.');
			return false;
		}

		if( trim($colis)=='' ){
			return false;
		}

		// Service de livraison
		$carrier['carrier_name'] = 'Colissimo';
		$carrier['carrier_url'] = '';
		$carrier['carrier_code'] = '';
		$carrier['tracking_number'] = $colis;
		if( $bl['srv_id'] ){
			$rsrv = dlv_services_get( $bl['srv_id'] );
			if( $rsrv && ria_mysql_num_rows($rsrv) ){
				$srv = ria_mysql_fetch_array( $rsrv );
				$carrier['carrier_name'] = $srv['name'];
				$carrier['carrier_url'] = $srv['url-colis'];
				$carrier['carrier_code'] = $srv['id'];
			}
		}

		$this->shipOrder($order['ref']);
		$this->trackingOrder($order['ref'], $carrier);

		if( !fld_object_values_set( $order['id'], _FLD_ORD_CTR_SHIPPED, 'Oui') ){
			error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Impossible de noter la commande comme expédiée ("'.$order['id'].'").');
		}
	}

	/** Cette fonction permet de récupérer les informations sur le produit frais de port paramétré.
	 *	Un cache de 30 minutes est mis en place sur cette fonction
	 *	@return bool False si le produit n'est pas paramètré, sinon les informations sur le produit contenant dans un tableau :
	 *				- id : identifiant du produit frais de port
	 *				- ref : référence du produit frais de port
	 *				- name : nom du produit frais de port
	 */
	public function getProductShipping(){
		global $config;
		global $memcached;

		if($shipping = $memcached->get( $this->ctr_name.':getproductshipping:'.$config['tnt_id'] ) ){
			return $shipping;
		}

		$port = '';

		// Récupère la référence dans la configuration de truffaut
		$rport = ctr_params_get( $this->ctr_id, 'port_ref' );
		if( $rport && ria_mysql_num_rows($rport) ){
			$port = ria_mysql_result( $rport, 0, 'fld' );
		}

		if( trim($port)=='' ){
			error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le paramètre des frais de port n\'est pas défini.');
			return false;
		}

		// Récupère les informations sur le produit
		$rp = prd_products_get_simple( 0, $port );
		if( !$rp || !ria_mysql_num_rows($rp) ){
			error_log( __FILE__.':'.__LINE__.' [Tenant '.$config['tnt_id'].'] Le produit frais de port n\'existe pas.');
			return false;
		}

		$p = ria_mysql_fetch_array( $rp );

		$shipping = array(
			'id' => $p['id'],
			'ref' => $p['ref'],
			'name' => $p['name']
		);

		$memcached->set( $this->ctr_name.':getproductshipping:'.$config['tnt_id'], $shipping, 900 );
		return $shipping;
	}

	/** Cette fonction permet de récupérer le rapport de l'import précédent et d'envoyer un mail avec les erreur */
	public function getErrorReport(){

		global $config;
		$import_id = $this->getLastImportID();

		if( !$import_id ){
			return false;
		}

		$tmp_report = $this->dirname.'/tmp-report-'.$import_id.'.xml';

		$response = $this->miraklRequest('/offers/imports/'.$import_id.'/error_report','get');

		if( $this->sandbox ){
			var_dump($response);
		}
		file_put_contents($tmp_report, $response);

		$errors = array();
		if (substr($response, 0, 1) == '<') {
			$report = new SimpleXMLElement($response);


			foreach ($report->offers->offer as $offer) {
				$ref = (string)$offer->{'sku'};
				$errors[(string)$offer->{'error-message'}][] = $ref;
				$r_product = prd_products_get_byref( $ref );

				if( $r_product && ria_mysql_num_rows($r_product) ){
					$product = ria_mysql_fetch_assoc($r_product);
					if (!tsk_comparators_action_exists( $this->ctr_id, $product['id'], 'add', false )) {
						tsk_comparators_add($this->ctr_id, $product['id'], 'add');
					}

					fld_object_values_set(array($this->ctr_id, $product['id']), _FLD_CTR_PRODUCT_EXIST, '0' );
				}
			}
		}


		$html = '';

		foreach( $errors as $msg_error => $sku_list ) {
			$html .= '<h2>Erreur : '.$msg_error.'</h2>';
			$html .= '<ul>';
			foreach ($sku_list as $sku) {
				$html .= '<li>'.$sku.'</li>';
			}
			$html .= '</ul>';

		}

		unlink($tmp_report);
		return $response;
	}

	/**
	 * Récupère les catégorie de mirakl
	 * @return  un tableau avec les catégories
	 */
	public function getHierarchies(){
		$response = $this->miraklRequest('/hierarchies', 'get', 'application/json', array(), array('Accept: application/json'));

		$json = json_decode($response, true);

		return $json['hierarchies'];
	}

	/**
	 * Permet de récupérer les attributs
	 * @param   $category Facultatif, Tableau avec le code de la catégorie mirakl et le level niveaux de la catégorie
	 * @return            un tableau avec les attributs
	 */
	public function getAttributes($category=null){

		$params = array();

		if( !is_null($category) ){
			$params['hierarchy'] = $category['code'];

			if( isset($category['level']) ){
				$params['max_level'] = $category['level'];
			}
		}
		$url = '/products/attributes';

		if( !empty($params) ){
			$url .= '?'.http_build_query($params);
		}

		$response = $this->miraklRequest($url, 'get', 'application/json', array(), array('Accept: application/json'));

		$json = json_decode($response, true);

		return $json['attributes'];
	}

	/**
	 * Récupère le rapport de transformation du xml en csv chez mirakl, permet de savoir pour quel produit il manque des informations (attribut)
	 * @param   $import_id Identifiant de l'import mirakl
	 * @return             Retourne le contenu du rapport au format xml
	 */
	public function getTransformationErrorReport($import_id){
		if( !is_numeric($import_id) || $import_id < 0){
			throw new InvalidArgumentException("$import_id doit être un numéric supérieur a 0");
		}
		$response = $this->miraklRequest('/products/imports/'.$import_id.'/transformation_error_report', 'get');

		return $response;
	}

	/**
	 * Cette fonction permet de récupérer le raport de création de nouveau produit
	 * @param   $import_id Identifiant de l'import mirakl
	 * @return             Retourne le contenu du rapport au format xml si http code 200, si code 404 un xml déclarant aucun raport
	 */
	public function getNewProductReport($import_id){
		if( !is_numeric($import_id) || $import_id < 0){
			throw new InvalidArgumentException("$import_id doit être un numéric supérieur a 0");
		}
		$response = $this->miraklRequest('/products/imports/'.$import_id.'/new_product_report', 'get');

		return $response;
	}

	/**
	 * Cette fonction permet de récupérer les valeurs possible our un attribut type list
	 * @param   $code Code de l'attribut mirakl
	 * @return        retourne un tableau avec la liste des valuer voir la doc mirakl pour plus d'info
	 */
	public function getAttributesValues($code){
		$response = $this->miraklRequest('/values_lists?code='.$code, 'get', 'application/json', array(), array('Accept: application/json'));
		$json = json_decode($response, true);
		return $json['values_lists'];
	}

}


