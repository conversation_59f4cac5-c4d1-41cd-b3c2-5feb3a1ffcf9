<?php

use EventService\EventProvider;
use EventService\Products\Events\PriceChanges;
use EventService\Products\Events\ProductPriceChanges;
require_once( 'PriceWatching/models/prw_followed_products.inc.php' );
require_once( 'PriceWatching/models/prw_offers.inc.php' );
require_once( 'products.inc.php' );
require_once( 'PriceWatching/prw.iWatching.inc.php' );
/**
 * \ingroup PriceWatchingCron
 */
/**
 * \class ClientWatch
 * \brief Cette class permet la veille tarifiare sur le client riashop
 */
class ClientWatch implements iWatching {

	private static $event;
	/**
	 * Cette fontion a pour but de récupérer les tarifs des produit direcetement depuis riashop
	 * pour en suite créer une ligne dans la table prw_offers
	 * \param int $cat Optionnel, identifiant d'une catégorie
	 * \param int $prd Optionnel, identifiant d'un produit
	 * \param int $recursive Optionnel, si un identifiant de catégorie est saisi, ce paramètre indique l'option de
	 * récursivité. pour la catégorie
	 * \return false si erreur
	 */
	public static function watch( $cat = 0, $prd = 0, $recursive = 0 ){

		if( !is_numeric( $cat ) || !is_numeric( $prd ) || !is_numeric( $recursive ) ){
			return false;
		}

		static::$event = new PriceChanges;

		$oFollowed = new prw_followed_products();

		$rPrd = prd_products_get_simple( $prd, '', false, $cat, ($recursive == true ? true : false), false, true, false, array('childs'=>true) );

		if( !$rPrd && !ria_mysql_num_rows($rPrd) ){
			return false;
		}

		while( $followedProduct = ria_mysql_fetch_assoc( $rPrd ) ) {
			if( !$oFollowed->prw_followed_products_get( $followedProduct['id'], PRW_CLIENT, 0 ) ){
				$oFollowed->prw_followed_products_add( $followedProduct['id'], PRW_CLIENT, $followedProduct['ref'], 0 );
			}

			$productPriceChanges = new ProductPriceChanges(
				$followedProduct
			);

			$ListingPrice = number_format( $followedProduct['price_ttc'], 2 );

			$oOffers = prw_offers::getInstance();

			$promoPrice = null;
			$prdPromo = prc_promotions_get( $followedProduct['id'], 0, 0, 1,0, array( 'price_ht'=> $followedProduct['price_ht'], 'tva_rate'=> $followedProduct['tva_rate']), true );

			if( $prdPromo && is_array($prdPromo) ){
				$promoPrice = number_format( $prdPromo['price_ttc'], 2 );
			}
			$productPriceChanges->withCurrentPrice(
				$ListingPrice,
				0,
				$promoPrice
			);

			$last_offer = $oOffers->get_offer_getLast( $followedProduct['id'], PRW_CLIENT );

			if( !$last_offer ){
				$ShippingPrice = 0;
				$url = '';
				$prd_id = $followedProduct['id'];
				$id = $oOffers->prw_offers_add( $ListingPrice, $ShippingPrice, $url, PRW_CLIENT, $prd_id, $promoPrice );
				$oFollowed->prw_followed_products_update_lastcheck( $followedProduct['id'], PRW_CLIENT );
			} else{

				if( $last_offer['ListingPrice'] == $ListingPrice && $last_offer['promo_price'] == $promoPrice ){
					$oFollowed->prw_followed_products_update_lastcheck( $followedProduct['id'], PRW_CLIENT );
				} else{
					$ShippingPrice = 0;
					$url = '';
					$prd_id = $followedProduct['id'];
					$oOffers->prw_offers_add( $ListingPrice, $ShippingPrice, $url, PRW_CLIENT, $prd_id, $promoPrice );
					$oFollowed->prw_followed_products_update_lastcheck( $followedProduct['id'], PRW_CLIENT );

					$productPriceChanges->withLastPrice(
						$last_offer['ListingPrice'],
						$last_offer['shippingprice'],
						$last_offer['promo_price']
					);
					static::$event->priceChanged($productPriceChanges);
				}
			}
		}
		if (static::$event->hasChanges()) {
			EventProvider::emit(static::$event);
		}
	}
}
