# ColumnConfiguration

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**beez_up_column_name** | [**\Swagger\Client\Model\BeezUPCommonBeezUPColumnName**](BeezUPCommonBeezUPColumnName.md) |  | [optional] 
**column_importance** | [**\Swagger\Client\Model\BeezUPCommonColumnImportance**](BeezUPCommonColumnImportance.md) |  | 
**column_data_type** | [**\Swagger\Client\Model\BeezUPCommonColumnDataType**](BeezUPCommonColumnDataType.md) |  | 
**column_culture_name** | [**\Swagger\Client\Model\ColumnCultureName**](ColumnCultureName.md) |  | [optional] 
**column_format** | [**\Swagger\Client\Model\ColumnFormat**](ColumnFormat.md) |  | [optional] 
**can_be_truncated** | [**\Swagger\Client\Model\CanBeTruncated**](CanBeTruncated.md) |  | [optional] 
**display_group_name** | [**\Swagger\Client\Model\DisplayGroupName**](DisplayGroupName.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


