<?php

	/**	\file index.php
	 *
	 *	Page de résultat du moteur de recherche interne au back-office RiaShop.
	 *	Cette page est chargée d'afficher le résultat d'une recherche. Elle propose différentes fonctionnalités :
	 *	- Affichage des résultats
	 *	- Possibilité de lancer une nouvelle recherche
	 *	- Navigation à facettes
	 *
	 */

	// Si aucun terme de recherche n'a été défini, redirige vers la page d'accueil du back-office
	if( !isset($_GET['q']) ){
		header('Location: ../index.php');
		exit;
	}

	// Traitement spéciaux liés aux noms de domaines ?
	$q_search = str_replace( array('.net', '.fr', '.com', '.coop'), '', $_GET['q'] );
	switch( $config['tnt_id'] ){
		case 5: // Traitement spécifique à la LPO
			$q_search = str_replace('@lpo', '', $q_search);
			$q_search = str_replace('lpo', '', $q_search);
			break;
		case 39: // Traitement spécifique à Freevox
			$q_search = str_replace('@freevox', '', $q_search);
			$q_search = str_replace('freevox', '', $q_search);
			break;
	}

	// Si aucun terme de recherche n'a été défini, redirige vers la page d'accueil du back-office
	if( trim($q_search)=='' ){
		header('Location: /admin/index.php');
		exit;
	}

	require_once('search.inc.php');

	// Facette Type de résultat (classe)
	// Valeur par défaut : indifférent
	if( !isset($_GET['type-result']) || !$_GET['type-result'] ){
		$_GET['type-result'] = '';
	}

	// Facette Statut de publication.
	// Valeur par défaut : indifférent
    if( !isset($_GET['publish']) ){
		$_GET['publish'] = '0';
	}

	// Facette Section (catégorie de premier niveau).
	// Valeur par défaut : indifférent
	if( !isset($_GET['section']) || !$_GET['section'] ) $_GET['section'] = false;

	Breadcrumbs::root( _('Accueil'), '/admin/index.php' )
		->push( _('Rechercher') )
		->push( $_GET['q'], '/admin/search/index.php?q='.urlencode($_GET['q']) );

	if( $_GET['type-result']!='' ){
		$rtype = search_content_types_get( $_GET['type-result'] );
		if( $rtype && ria_mysql_num_rows($rtype) ){
			$type = ria_mysql_fetch_array($rtype);
			Breadcrumbs::add( $type['name'] );
		}
	}

	// Définit le titre de la page
	define('ADMIN_PAGE_TITLE', htmlspecialchars( $_GET['q'] ).' - ' . _('Rechercher'));
	require_once('admin/skin/header.inc.php');
?>
	<form method="get" title="<?php echo _("Moteur de recherche"); ?>" action="index.php">
		<h2 id="search-results-title"><label for="search"><?php echo _("Rechercher :"); ?></label>
		<input type="text" name="q" id="search" <?php if( isset($_GET['q']) ) print 'value="'.htmlspecialchars($_GET['q']).'"'; ?> />
		<input type="submit" value="<?php echo _("Chercher"); ?>" /></h2>
	</form>
<?php
	// Facette Statut de publication
	$publish = false;
	switch( $_GET['publish'] ){
		case -1:
		case '-1':
			$publish = null;
			break;
		case 1:
		case '1':
			$publish = true;
			break;
	}

	// Facettes du type de contenu (classe)
	$tab = search_known_types();
	if( $_GET['type-result']!='' ){
		$tab = array( $_GET['type-result'] );
	}

	// Gère la pagination
	if( !isset($_GET['page']) ){
		$page = 1;
	}else{
		if( !is_numeric($_GET['page']) || $_GET['page']<0 ){
			$page = 1;
		}else{
			$page = $_GET['page'];
		}
	}

	// Lance la recherche
	$rowstart = $page > 1 ? ($page - 1) * $config['prd_list_length'] : 0;
	$results = search(
		array('seg'=>1, 'keywords'=>$q_search, 'published'=>$publish, 'section'=>$_GET['section'], 'action'=>2, 'type'=>$tab),
		false, $rowstart, $config['prd_list_length'], false, null, 0, true
	);

	// Nombre total de résultats
	$results_count = sizeof($results) ? $results[0]['search']['nb_results'] : 0;

	// Pagination
	$pages = ceil($results_count / $config['prd_list_length']);
	if( isset($_GET['page']) && $_GET['page']>$pages ){
		$page = $pages;
	}

	// Affichage du nombre de résultats
	print '<span id="search-results-infos">';

	if ( $results_count == 0 ) {
		print _('Aucun résultat pour <b>') . htmlspecialchars($_GET['q']) . '</b>';
	} elseif ( $results_count == 1 ) {
		print _('1 résultat pour <b>') . htmlspecialchars($_GET['q']) . '</b>';
	} else {
		if (count($results) > 20) {
			$results = array_slice($results, ($page - 1) * 20);
		}

		print number_format($results_count, 0, ',', ' ') . ' résultats pour <b>' . htmlspecialchars($_GET['q']) . '</b>';
	}

	print '</span>';

	// Affichage des résultats
	print '<div id="search-results">';
	if( $results_count==0 ){
		print '<p>' . _("Votre recherche n'a ramené aucun résultat.") . '</p>';

		// On regarde s'il existe des articles non classés pour compléter le texte dans le cas ou le moteur de retour aucun produit
		// Récupère les articles Non classés
		$childonly = isset($_SESSION['usr_admin_show_childs']) ? $_SESSION['usr_admin_show_childs'] : true;
		$r_product = prd_products_get_simple(0, '', false, -1, false, false, false, false, array('childs' => $childonly));

		// Des produits non classés ont été trouvés
		if( $r_product && ria_mysql_num_rows($r_product) ){
			print '<div class="notice">'._('Seuls les articles classés dans votre catalogue sont visibles dans les résultats du moteur de recherche.').'<br />'.str_replace('#param[url]#', '/admin/catalog/unclassified.php', _('Retrouvez tous vos articles non classés en vous rendant dans <a href="#param[url]#">Catalogue > Non classés</a>.')).'</div>';
		}
	}else{
		$tnt_have_website = tnt_tenants_have_websites();
		$i = 0;
		foreach( $results as $sr ){
			if (($i++) >= 20) {
				break;
			}

			$r = $sr['search'];
			$g = $sr['get'];

			// S'il s'agit d'un produit, l'alt_url est construit en priorité avec la catégorie canonique > catégorie publiée > reste telle-quelle
			if( $r['type_code'] == 'prd' ){
				// Récupère la catégorie en fonction de l'url canonique (ou de sa première catégorie publié).
				$r_category = prd_products_categories_get( $g['id'], true, true, null, $config['cat_root'] );

				if( $r_category ){
					$cat_publish = $cat_canonical = false;
					while( $category = ria_mysql_fetch_assoc($r_category) ){
						if( $category['url_is_canonical'] ){
							$cat_canonical = $category['cat'];
							break;
						}elseif( $cat_publish === false ){
							$cat_publish = $category['cat'];
						}
					}

					if( is_numeric($cat_canonical) && $cat_canonical > 0 ){
						$r['alt_url'] = '/admin/catalog/product.php?cat='.$cat_canonical.'&amp;prd='.$g['id'];
					}elseif( is_numeric($cat_publish) && $cat_publish > 0 ){
						$r['alt_url'] = '/admin/catalog/product.php?cat='.$cat_publish.'&amp;prd='.$g['id'];
					}
				}
			}

			// Calcul du titre du résultat en fonction de son type
			switch( $r['type_code'] ){
				case 'prd':
					$name = view_prd_is_sync( $sr['get'] ).' '.htmlspecialchars( $sr['get']['ref'].' - '.$r['name'] );
					break;
				case 'prd-cat':
					$name = view_cat_is_sync( $sr['get'] ).' '.htmlspecialchars( $r['name'] );
					break;
				case 'usr':
					$name = view_usr_is_sync( $sr['get'] ).(trim($sr['get']['ref']) != '' ? $sr['get']['ref'] . ' - ' : '').' '.htmlspecialchars( $r['name'] );
					break;
				case 'ord':
					$name = view_ord_is_sync( $sr['get'] ).' '.htmlspecialchars('Commande '.ord_orders_name( $g['ref'], $g['piece'], $g['id'] ).', '.$g['state_name']);
					$r['desc'] = 'Commande '.ord_orders_name( $g['ref'], $g['piece'], $g['id'] ).' du '.$g['date'].', '.$g['state_name'].', Compte client '.gu_users_get_ref($g['user'], true);
					break;
				case 'brd':
					$name = view_brd_is_sync( $sr['get'] ).' '.htmlspecialchars( $r['name'] );
					break;
				case 'dlv-str':
					$name = view_str_is_sync( $sr['get'] ).' '.htmlspecialchars( $r['name'] );
					break;
				default:
					$name = htmlspecialchars( $r['name'] );
					break;
			}

			// Affichage du résultat
			// On vérifie s'il y a une image, sinon on affiche une image par défaut
			if( is_numeric($r['img_id']) && $r['img_id'] > 0 ){
				$url_img = $config['img_url'].'/'.$config['img_sizes']['small']['dir'].'/'.$r['img_id'].'.'.$config['img_sizes']['small']['format'];
				if( !ria_file_exists_byurl($url_img) ){
					$url_img = view_admin_get_img_default('small');
				}
			}else{
				$url_img = view_admin_get_img_default('small');
				if( !ria_file_exists_byurl($url_img) ){
					$url_img = '/admin/images/default.jpg';
				}
			}

			print ' <dl class="search-result">
					<dd class="search-result-img"><a href="'.$r['alt_url'].'"><img src="'.$url_img.'" width="80" height="80" alt="'.htmlspecialchars($r['name']).'" title="'.htmlspecialchars($r['name']).'" /></a></dd>
					<dt><a href="'.$r['alt_url'].'">'.$name.'</a></dt>
					<dd>'.strcut( html_revert_wysiwyg($r['desc']), 130, ' ...' ).'</dd>';

			if ($r['type_code'] == 'msg'){
				$r_msg = messages_get(0,'',0,$g['id']);
				if ($msg = ria_mysql_fetch_assoc($r_msg)){
					$date_reception = $msg['date_created'];
				}
				print '<dd>Reçu le : '.$date_reception.'</dd>';
			}

			// Fil d'ariane du contenu dans l'arborescence du back-office
			print '<dd class="search-result-url"><a href="'.$r['alt_url'].'">';
			switch( $r['type_code'] ){
				case 'prd': // Produit
					print 'Catalogue &raquo; ';
					preg_match( '/cat=([0-9]+)/', $r['alt_url'], $matches );
					$cat = $matches[1];
					$parents = prd_categories_parents_get( $cat );
					while( $p = ria_mysql_fetch_array($parents) ){
						print $p['title'].' &raquo; ';
					}
					$rcat = prd_categories_get( $cat );
					if( ria_mysql_num_rows($rcat) ){
						$cat = ria_mysql_fetch_array($rcat);
						print $cat['title'].' &raquo; ';
					}
					print htmlspecialchars( $sr['get']['name'] );
					break;
				case 'usr': // Compte client
					print _('Clients &raquo; ');
					print htmlspecialchars( $r['name'] );
					break;
				case 'dlv-str': // Magasin
					print _('Magasins &raquo; ');
					print htmlspecialchars( $r['name'] );
					break;
				case 'ord': // Commande
					print _( 'Commandes &raquo; ');
					print _('Commande ').htmlspecialchars( ord_orders_name( $g['ref'], $g['piece'], $g['id'] ).', '.$g['state_name'] );
					break;
				default: // Autres
					print $r['alt_url'].' - '.htmlspecialchars( $r['type_name'] );
			}
			print '</a></dd>';

			// Liens d'accès direct aux onglets de la fiche (dépend du type de contenu)
			print '<dd class="search-result-tabs">';
			switch( $r['type_code'] ){
				case 'prd': // Produits
					print '	<ul>
								<li><a href="'.$r['alt_url'].'">' . _("Général") . '</a></li>
								<li><a href="'.$r['alt_url'].'&amp;tab=fields">' . _("Avancé") . '</a></li>
								<li><a href="'.$r['alt_url'].'&amp;tab=prices">' . _("Tarifications") . '</a></li>
								<li><a href="'.$r['alt_url'].'&amp;tab=nomenclature">' . _("Nomenclature") . '</a></li>
								<li><a href="'.$r['alt_url'].'&amp;tab=linked">' . _("Articles liés") . '</a></li>
								<li><a href="'.$r['alt_url'].'&amp;tab=images">' . _("Médiathèque") . '</a></li>
								<li><a href="'.$r['alt_url'].'&amp;tab=stocks">' . _("Stocks") . '</a></li>
								<li><a href="'.$r['alt_url'].'&amp;tab=delivery">L' . _("ivraison") . '</a></li>';
					if( $tnt_have_website){
						print '	<li><a href="'.$r['alt_url'].'&amp;tab=reviews">' . _("Avis") . '</a></li>
								<li><a href="'.$r['alt_url'].'&amp;tab=ref">' . _("Référencement") . '</a></li>';
					}
					print '<li><a href="'.$r['alt_url'].'&amp;tab=stats">' . _("Statistiques") . '</a></li>';

					if( isset($config['ctr_active']) && $config['ctr_active'] ){
						if( gu_user_is_authorized('_RGH_ADMIN_COMPARATOR') ){
							$r_ctr = ctr_comparators_get( 0, true, false, false );

							if( $r_ctr && ria_mysql_num_rows($r_ctr) ){
								print '<li><a href="'.$r['alt_url'].'&amp;tab=comparators">' . _("Comparateurs") . '</a></li>';
							}
						}

						if( gu_user_is_authorized('_RGH_ADMIN_MARKET_PLACE') ){
							$r_mtk = ctr_comparators_get(0, true, false, true);

							if( $r_mtk && ria_mysql_num_rows($r_mtk) ){
								print '<li><a href="'.$r['alt_url'].'&amp;tab=marketplace">' . _("Places de marché") . '</a></li>';
							}
						}
					}

					print '</ul>';
					break;
				case 'prd-cat': // Catégories de produits
					$alt_url = $config['site_url'].'/admin/catalog/edit.php?cat='.$r['tag'];
					print '	<ul>
								<li><a href="'.$alt_url.'">' . _("Général") . '</a></li>
								<li><a href="'.$alt_url.'&amp;tab=fields">' . _("Avancé") . '</a></li>
								<li><a href="'.$alt_url.'&amp;tab=prices">' . _("Tarifications") . '</a></li>
								<li><a href="'.$alt_url.'&amp;tab=images">' . _("Images") . '</a></li>
								<li><a href="'.$alt_url.'&amp;tab=filters">' . _("Filtres") . '</a></li>';
					if( $tnt_have_website ){
						print '<li><a href="'.$alt_url.'&amp;tab=ref">' . _("Référencement") . '</a></li>';
					}
					if (isset($config['ctr_active']) && $config['ctr_active']) {
						if (gu_user_is_authorized('_RGH_ADMIN_COMPARATOR')) {
							if(!isset($r_ctr) ){
								$r_ctr = ctr_comparators_get( 0, true, false, false );
							}

							if ($r_ctr && ria_mysql_num_rows($r_ctr)) {
								print '<li><a href="'.$alt_url.'&amp;tab=comparators">' . _("Comparateurs") . '</a></li>';
							}
						}

						if (gu_user_is_authorized('_RGH_ADMIN_MARKET_PLACE')) {
							if(!isset($r_mtk)){
								$r_mtk = ctr_comparators_get(0, true, false, true);
							}

							if ($r_mtk && ria_mysql_num_rows($r_mtk)) {
								print '<li><a href="'.$alt_url.'&amp;tab=marketplace">' . _("Places de marché") . '</a></li>';
							}
						}
					}

					print '</ul>';
					break;
				case 'usr': // Comptes clients
					if( !tnt_tenants_is_yuto_essentiel() ){
						print '	<ul>
									<li><a href="'.$r['alt_url'].'">' . _("Général") . '</a></li>
									<li><a href="'.$r['alt_url'].'&amp;tab=orders">' . _("Commandes") . '</a></li>
									<li><a href="'.$r['alt_url'].'&amp;tab=delayed">' . _("Reliquats") . '</a></li>';
						if( $tnt_have_website ){
							print '<li><a href="'.$r['alt_url'].'&amp;tab=contacts">' . _("Messages") . '</a></li>';
						}
						print '</ul>';
					}
					break;
				case 'ord': // Commandes
					print '	<ul class="ord-actions">
							<li>
								<a id="ord-duplicate" class="button">'._('Dupliquer').'</a>
								<input type="hidden" name="ord-id" value="'.$r['tag'].'" />
							</li>
						</ul>';
					break;
			}
			print '	</dd>
				</dl>';
		}
	}
	print '</div>';

	// Affichage de la navigation dans les résultats (pagination)
	if( $results_count>0 ){
		print '	<div class="search-paginator">
					<div class="search-page">Page '.$page.' / '.$pages.'</div>
					<div class="search-nav">';

		$links = array();
		if( $page>1 ){
			$links[] = '<a href="index.php?q='.urlencode($_GET['q']).'&amp;page='.($page-1).'&amp;type-result='.$_GET['type-result'].'&amp;publish='.$_GET['publish'].'&amp;section='.$_GET['section'].'">&laquo; ' . _("Page précédente") . '</a>';
		}
		for( $i=$page-5; $i<$page+5; $i++ )
			if( $i>=1 && $i<=$pages ){
				if( $i==$page )
					$links[] = '<b>'.$i.'</b>';
				else
					$links[] = '<a href="index.php?q='.urlencode($_GET['q']).'&amp;page='.$i.'&amp;type-result='.$_GET['type-result'].'&amp;publish='.$_GET['publish'].'&amp;section='.$_GET['section'].'">'.$i.'</a>';
			}
		if( $page<$pages ){
			$links[] = '<a href="index.php?q='.urlencode($_GET['q']).'&amp;page='.($page+1).'&amp;type-result='.$_GET['type-result'].'&amp;publish='.$_GET['publish'].'&amp;section='.$_GET['section'].'">' . _("Page suivante") . ' &raquo;</a>';
		}
		print implode(' | ',$links);
		print '	</div>
			</div>';
	}

	require_once('admin/skin/footer.inc.php');
?>