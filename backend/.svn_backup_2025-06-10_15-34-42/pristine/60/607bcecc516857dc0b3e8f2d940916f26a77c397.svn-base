<documentation title="Constant Names">
    <standard>
    <![CDATA[
     Constants should always be all-uppercase, with underscores to separate words.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: all uppercase">
        <![CDATA[
define('<em>FOO_CONSTANT</em>', 'foo');

class FooClass
{
    const <em>FOO_CONSTANT</em> = 'foo';
}
        ]]>
        </code>
        <code title="Invalid: mixed case">
        <![CDATA[
define('<em>Foo_Constant</em>', 'foo');

class FooClass
{
    const <em>foo_constant</em> = 'foo';
}
        ]]>
        </code>
    </code_comparison>
</documentation>
