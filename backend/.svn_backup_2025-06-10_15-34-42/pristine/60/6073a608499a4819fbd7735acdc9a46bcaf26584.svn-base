language: php

dist: trusty

sudo: false

cache:
    directories:
        - vendor
        - $HOME/.composer/cache/files


env:
    global:
        - TWIG_EXT=no
        - SYMFONY_PHPUNIT_REMOVE_RETURN_TYPEHINT=1

before_install:
    - phpenv config-rm xdebug.ini || return 0

install:
    - travis_retry composer install

before_script:
    - if [ "$TWIG_EXT" == "yes" ]; then sh -c "cd ext/twig && phpize && ./configure --enable-twig && make && make install"; fi
    - if [ "$TWIG_EXT" == "yes" ]; then echo "extension=twig.so" >> `php --ini | grep "Loaded Configuration" | sed -e "s|.*:\s*||"`; fi

script: ./vendor/bin/simple-phpunit

jobs:
    fast_finish: true
    include:
        - php: 5.5
        - php: 5.5
          env: TWIG_EXT=yes
        - php: 5.6
        - php: 5.6
          env: TWIG_EXT=yes
        - php: 7.0
        - php: 7.1
        - php: 7.2
        - php: 7.3
        - php: 7.4snapshot
        - stage: integration tests
          php: 7.3
          script: ./drupal_test.sh
