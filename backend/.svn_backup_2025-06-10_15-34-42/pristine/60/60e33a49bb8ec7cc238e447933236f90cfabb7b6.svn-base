<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/error_details.proto

namespace Google\Rpc;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Describes the cause of the error with structured details.
 * Example of an error when contacting the "pubsub.googleapis.com" API when it
 * is not enabled:
 *     { "type":   "API_DISABLED"
 *       "domain": "googleapis.com"
 *       "metadata": {
 *         "resource": "projects/123",
 *         "service": "pubsub.googleapis.com"
 *       }
 *     }
 * This response indicates that the pubsub.googleapis.com API is not enabled.
 * Example of an error that is returned when attempting to create a Spanner
 * instance in a region that is out of stock:
 *     { "type":   "STOCKOUT"
 *       "domain": "spanner.googleapis.com",
 *       "metadata": {
 *         "availableRegions": ""us-central1,us-east2"
 *       }
 *     }
 *
 * Generated from protobuf message <code>google.rpc.ErrorInfo</code>
 */
class ErrorInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * The type of the error. This is a constant value that identifies the
     * proximate cause of the error. Error types are unique within a particular
     * source of errors. This should be at most 63 characters and match
     * /[A-Z0-9_]+/.
     *
     * Generated from protobuf field <code>string type = 1;</code>
     */
    private $type = '';
    /**
     * The logical grouping to which the "type" belongs.  Often "domain" will
     * contain the registered service name of the tool or product that is the
     * source of the error. Example: "pubsub.googleapis.com". If the error is
     * common across many APIs, the first segment of the example above will be
     * omitted.  The value will be, "googleapis.com".
     *
     * Generated from protobuf field <code>string domain = 2;</code>
     */
    private $domain = '';
    /**
     * Additional structured details about this error.
     * Keys should match /[a-zA-Z0-9-_]/ and be limited to 64 characters in
     * length. When identifying the current value of an exceeded limit, the units
     * should be contained in the key, not the value.  For example, rather than
     * {"instanceLimit": "100/request"}, should be returned as,
     * {"instanceLimitPerRequest": "100"}, if the client exceeds the number of
     * instances that can be created in a single (batch) request.
     *
     * Generated from protobuf field <code>map<string, string> metadata = 3;</code>
     */
    private $metadata;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $type
     *           The type of the error. This is a constant value that identifies the
     *           proximate cause of the error. Error types are unique within a particular
     *           source of errors. This should be at most 63 characters and match
     *           /[A-Z0-9_]+/.
     *     @type string $domain
     *           The logical grouping to which the "type" belongs.  Often "domain" will
     *           contain the registered service name of the tool or product that is the
     *           source of the error. Example: "pubsub.googleapis.com". If the error is
     *           common across many APIs, the first segment of the example above will be
     *           omitted.  The value will be, "googleapis.com".
     *     @type array|\Google\Protobuf\Internal\MapField $metadata
     *           Additional structured details about this error.
     *           Keys should match /[a-zA-Z0-9-_]/ and be limited to 64 characters in
     *           length. When identifying the current value of an exceeded limit, the units
     *           should be contained in the key, not the value.  For example, rather than
     *           {"instanceLimit": "100/request"}, should be returned as,
     *           {"instanceLimitPerRequest": "100"}, if the client exceeds the number of
     *           instances that can be created in a single (batch) request.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Rpc\ErrorDetails::initOnce();
        parent::__construct($data);
    }

    /**
     * The type of the error. This is a constant value that identifies the
     * proximate cause of the error. Error types are unique within a particular
     * source of errors. This should be at most 63 characters and match
     * /[A-Z0-9_]+/.
     *
     * Generated from protobuf field <code>string type = 1;</code>
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * The type of the error. This is a constant value that identifies the
     * proximate cause of the error. Error types are unique within a particular
     * source of errors. This should be at most 63 characters and match
     * /[A-Z0-9_]+/.
     *
     * Generated from protobuf field <code>string type = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkString($var, True);
        $this->type = $var;

        return $this;
    }

    /**
     * The logical grouping to which the "type" belongs.  Often "domain" will
     * contain the registered service name of the tool or product that is the
     * source of the error. Example: "pubsub.googleapis.com". If the error is
     * common across many APIs, the first segment of the example above will be
     * omitted.  The value will be, "googleapis.com".
     *
     * Generated from protobuf field <code>string domain = 2;</code>
     * @return string
     */
    public function getDomain()
    {
        return $this->domain;
    }

    /**
     * The logical grouping to which the "type" belongs.  Often "domain" will
     * contain the registered service name of the tool or product that is the
     * source of the error. Example: "pubsub.googleapis.com". If the error is
     * common across many APIs, the first segment of the example above will be
     * omitted.  The value will be, "googleapis.com".
     *
     * Generated from protobuf field <code>string domain = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setDomain($var)
    {
        GPBUtil::checkString($var, True);
        $this->domain = $var;

        return $this;
    }

    /**
     * Additional structured details about this error.
     * Keys should match /[a-zA-Z0-9-_]/ and be limited to 64 characters in
     * length. When identifying the current value of an exceeded limit, the units
     * should be contained in the key, not the value.  For example, rather than
     * {"instanceLimit": "100/request"}, should be returned as,
     * {"instanceLimitPerRequest": "100"}, if the client exceeds the number of
     * instances that can be created in a single (batch) request.
     *
     * Generated from protobuf field <code>map<string, string> metadata = 3;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getMetadata()
    {
        return $this->metadata;
    }

    /**
     * Additional structured details about this error.
     * Keys should match /[a-zA-Z0-9-_]/ and be limited to 64 characters in
     * length. When identifying the current value of an exceeded limit, the units
     * should be contained in the key, not the value.  For example, rather than
     * {"instanceLimit": "100/request"}, should be returned as,
     * {"instanceLimitPerRequest": "100"}, if the client exceeds the number of
     * instances that can be created in a single (batch) request.
     *
     * Generated from protobuf field <code>map<string, string> metadata = 3;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setMetadata($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::STRING);
        $this->metadata = $arr;

        return $this;
    }

}

