name: CI

on:
  - pull_request
  - push

jobs:
  tests:
    runs-on: ubuntu-16.04

    strategy:
      fail-fast: true
      matrix:
        php-versions: ['5.6', '7.0', '7.1', '7.2', '7.3', '7.4'] 

    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-versions }}

    - name: Install dependencies
      run: composer install --optimize-autoloader --prefer-dist

    - name: Run tests
      run: composer run-script test

  tests-php8:
    runs-on: ubuntu-16.04

    strategy:
      fail-fast: true
      matrix:
        php-versions: ['8.0'] 

    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-versions }}

    - name: Install dependencies
      run: composer require -W phpunit/phpunit

    - name: Run tests
      run: composer run-script test