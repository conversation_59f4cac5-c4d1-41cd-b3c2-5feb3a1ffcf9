<?php
	/**
	 *	Ce module permet les paiement avec Payline
	 *	
	 */

	require_once('PaymentExternal.inc.php');
	require_once('ord.installments.inc.php');
	require_once('NetAffiliation.inc.php');
	require_once('Payline/paylineSDK.php');

	/**	\brief Cette classe facilite l'intégration du prestataire de paiement Paybox
	 */
	class Payline extends PaymentExternal {
		private static $Instance; ///< Instance

		private $contract = false; ///< Numéro de contrat Payline
		private $selected_contracts = false; ///< Numéro de contrat sélection (ceux qui seront visible sur le choix du mode de paiement)
		private $ord_id = false; ///< Identifiant de la commande à régler
		private $payline = false; ///< Instance de la classe fournie par Payline
		private $with_billing = false; ///< Si oui ou non l'adresse de facturation sera incluse dans les informations de paiement

		//Code de retours et noms associés
		protected $returns = array(
			"00000" => "Transaction acceptée",
			"01100" => "Transaction refusée",
			"01101" => "Carte expirée",
			"01102" => "Date d'expiration invalide",
			"01103" => "Renvoi - Contactez votre banque pour autorisation",
			"01109" => "Marchant invalide",
			"01110" => "Montant invalide",
			"01111" => "Numéro de carte invalide",
			"01113" => "Transaction refusée",
			"01114" => "Ce compte n'existe pas",
			"01115" => "Cette fonction n'existe pas",
			"01116" => "Limite de montant",
			"01117" => "Code PIN invalide",
			"01118" => "Carte non enregistrée",
			"01119" => "Transaction non autorisée",
			"01120" => "Transaction refusée par le terminal",
			"01121" => "Limite de débit dépassée",
			"01122" => "Violation de sécurité",
			"01123" => "Fréquence de transaction de débit dépassée",
			"01125" => "Carte inactive",
			"01126" => "Format du code PIN invalide",
			"01128" => "Code PIN de contrôle invalide",
			"01129" => "Contrefaçon suspectée",
			"01130" => "Numéro CVV invalide",
			"01180" => "Banque invalide",
			"01181" => "Devise invalide",
			"01182" => "Taux de conversion non trouvé",
			"01183" => "Montant maximum dépassé",
			"01184" => "Nombre d'utilisation maximum dépassé",
			"01185" => "Commande utilisée",
			"01197" => "Erreur de communication de l'acquéreur",
			"01198" => "Erreur de configuration de l'acquéreur",
			"01199" => "Erreur système interne (backend)",
			"01200" => "Transaction refusée",
			"01201" => "Carte expirée",
			"01202" => "Fraude suspectée par le partenaire de paiement",
			"01206" => "Nombre maximal de tentatives atteint",
			"01207" => "Condition spéciale",
			"01208" => "Carte perdue",
			"01209" => "Carte volée",
			"01280" => "Code BIN de la carte non autorisé",
			"01401" => "Opposition sur le compte (temporaire)",
			"01402" => "Chèque irrégulier",
			"01403" => "Chèque non référencé",
			"01404" => "Mauvais numéro d'abonné (IDCF)",
			"01405" => "Erreur serveur FNCI",
			"01406" => "CMC7 incorrect",
			"01407" => "Code IDC incorrect",
			"01430" => "Numéro de chèque incorrect",
			"01902" => "Transaction invalide de l'acquéreur",
			"01904" => "Mauvais format de requête de l'acquéreur",
			"01907" => "Erreur du serveur émetteur",
			"01909" => "Erreur interne du serveur de banque",
			"01912" => "Erreur du serveur émetteur",
			"01913" => "Transaction dupliquée",
			"01914" => "Transaction non trouvée",
			"01915" => "Annulation de l'opération non autorisée",
			"01917" => "Cette transaction n'est pas annulable",
			"01940" => "Serveur de banque indisponible",
			"01941" => "Erreur de communication au serveur de banque",
			"01942" => "Erreur interne du serveur de banque",
			"01943" => "Erreur interne du serveur de banque",
			"02000" => "Transaction en cours, veuillez attendre le statut du paiement.",
			"02001" => "Transaction dupliquée",
			"02002" => "Transaction dupliquée",
			"02003" => "Erreur de communication avec l'émetteur. La transaction devrait être annulée.",
			"02004" => "Erreur de communication avec l'émetteur. Transaction annulée.",
			"02005" => "Transaction en cours, veuillez attendre le statut du paiement.",
			"02006" => "Nouvel essai en cours, veuillez attendre le statut du paiement.",
			"02008" => "Transaction annulée par l'utilisateur",
			"02009" => "Inversion, processus de secours Payline",
			"02010" => "Fonction demandée non disponible",
			"02011" => "Inversion en mode de secours",
			"02012" => "Code de retour du partenaire sans correspondance",
			"02013" => "Erreur de communication avec l'émetteur. Transaction annulée.",
			"02014" => "Transaction gérée par le marchand",
			"02015" => "Transaction en cours, veuillez attendre le statut du paiement.",
			"02016" => "Attente de transaction sur le partenaire, veuillez attendre le retour du mode de paiement",
			"02020" => "Transaction refusée par le parenaire",
			"02101" => "Erreur système interne (frontend)",
			"02102" => "Erreur de communication du serveur acquéreur",
			"02103" => "Expiration de la connexion, veuillez réessayer ultérieurement",
			"02104" => "Erreur interne du Tokenizer",
			"02105" => "Capture envoyée pour règlement avant le processus d'annulation",
			"02106" => "Erreur du partenaire de paiement",
			"02110" => "Le montant est invalide",
			"02201" => "La clé de chiffrement recherchée n'existe pas",
			"02202" => "La clé de chiffrement utilisée est périmée",
			"02301" => "L'ID de transaction est invalide.",
			"02302" => "La transaction est invalide.",
			"02303" => "Numéro de contrat invalide.",
			"02304" => "Aucune transaction trouvée pour ce token",
			"02305" => "Format du champ invalide.",
			"02305" => "Format du champ invalide.",
			"02306" => "Le consommateur doit renseigner les détails de sa carte de paiement.",
			"02307" => "Code de page personnalisé invalide",
			"02308" => "Valeur invalide pour ",
			"02309" => "Le code de page de paiement personnalisé est inactif",
			"02310" => "Aucune transaction correspondant aux critères de recherche",
			"02311" => "Trop de transactions correspondent aux critères de recherche",
			"02312" => "Les critères de recherches sont invalides",
			"02313" => "L'indicateur de transaction capturable est désactivé",
			"02314" => "L'indicateur de transaction remboursable est désactivé",
			"02315" => "Un commerçant est déjà connecté avec ce nom de société. S'il y a un problème, contactez le support",
			"02316" => "L'offre commerciale n'existe pas pour ce distributeur. S'il y a un problème, contactez le support",
			"02317" => "Ce token n'existe pas",
			"02318" => "Ce token n'existe pas",
			"02319" => "Transaction annulée par l'utilisateur",
			"02320" => "L'intervalle de temps recherché est trop long",
			"02321" => "Valeur invalide pour l'action de paiement",
			"02322" => "Nom du titulaire de carte invalide",
			"02323" => "La date d'expiration en doit pas être fournie en utilisant l'authentification 3D Secure",
			"02324" => "La session a expiré avant que le consommateur ait terminé la transaction",
			"02325" => "La transaction doit être débloquée avant la capture",
			"02400" => "Transaction de masse réussie",
			"02401" => "Une transaction a échoué",
			"02402" => "En attente de traitement",
			"02403" => "Aucun traitement de masse trouvé",
			"02500" => "Opération réussie",
			"02501" => "Opération réussie mais le portefeuille expirera",
			"02502" => "Un portefeuille avec le même identifiant existe",
			"02503" => "Le portefeuille n'existe pas",
			"02504" => "Mise à jour du nom et du prénom impossible",
			"02505" => "Le portefeuille est désactivé",
			"02506" => "Le portefeuille ne peut pas fonctionner à la date prévue",
			"02507" => "Enregistrement de paiement introuvable",
			"02508" => "Payment record is disabled",
			"02509" => "payment.amount doit être égal à recurring.firstAmount + (recurring.billingLeft - 1) * recurring.amount",
			"02510" => "Enregistrement de paiement introuvable",
			"02511" => "Le portefeuille n'est pas supporté pour cette carte",
			"02512" => "Nom et prénom requis pour ce portefeuille",
			"02513" => "L'identifiant du portefeuille est requis pour ce portefeuille",
			"02514" => "Le numéro de données privé est limité à 99",
			"02515" => "Doit choisir les données à mettre à jour",
			"02516" => "Désactivation du ou des portefeuille(s) impossible",
			"02517" => "Désactivation d'un ou plusieurs portefeuille(s) impossible",
			"02518" => "Index de la carte invalide",
			"02519" => "Impossible d'activer le ou les portefeuilles",
			"02520" => "Impossible d'activer certains portefeuilles",
			"02521" => "Une carte existe déjà dans ce portefeuille",
			"02522" => "Champ invalide Amex Recurring",
			"02523" => "Amex Recurring non autorisé",
			"02524" => "Champ invalide Amex One Click",
			"02525" => "Amex one click non autorisé",
			"02526" => "Amex one click saisie du montant invalide",
			"02527" => "Option de contrôle invalide",
			"02528" => "La liste de contrats sélectionnés doit être rempli avec un seul contrat",
			"02529" => "Les informations doivent référencées au même contrat",
			"02530" => "eMoneo non autorisé",
			"02531" => "Leetchi non autorisé",
			"02532" => "La gestion de portefeuille n'est pas autorisée avec votre compte",
			"02533" => "Le consommateur n'est pas redirigé sur les pages Web de paiement",
			"02534" => "Le consommateur n'est pas redirigé sur les pages Web de paiement et la session est expirée",
			"02535" => "La session a expiré avant l'opération sur le portefeuille",
			"02536" => "Transaction annulée par l'utilisateur",
			"02538" => "Remplir soit card.number soit card.token (pas les deux)",
			"02539" => "La date d'expiration est obligatoire pour ce format de token.",
			"02540" => "Aucune carte trouvée pour ce token.",
			"02541" => "La carte n'est pas éligible",
			"02542" => "La carte expire avant la dernière date de facturation.",
			"02543" => "Enregistrement de la facturation non trouvé",
			"02544" => "La transaction ne doit pas être REC",
			"02545" => "La transaction ne doit pas être NX",
			"02546" => "L'état de facturation n'autorise pas l'action.",
			"02550" => "Date de fin attendue non valide",
			"02551" => "Le nouveau montant et la date de modification du montant sont tous deux présents ou vides.",
			"02552" => "La mise à jour de l'enregistrement de paiement doit être utilisé uniquement pour les paiements REC.",
			"02553" => "Nombre de jour de facturation entre 1 et 28 pour un cycle de facturation supérieur ou égal à 1 mois.",
			"02554" => "La facture restante doit être supérieure ou égale à la facturation déjà présente dans la base de données.",
			"02555" => "La date de fin, le nouveau montant et la date de modification du montant ne peuvent être utilisés que pour des paiements récurrents",
			"02556" => "L'identifiant d'enregistrement de facturation non valide",
			"02557" => "La date de modification du montant invalide",
			"02600" => "La réinitialisation n'est pas prise en charge pour le type de transaction",
			"02601" => "Réinitialisation déjà effectuée",
			"02602" => "L'autorisation est déjà expirée",
			"02603" => "L'autorisation n'est pas réinitialisable",
			"02604" => "Cette transaction n'existe pas",
			"02605" => "Identifiant de profil fonctionnel non valide",
			"02606" => "Identifiant de séquence invalide",
			"02607" => "Identifiant de profil monétique invalide",
			"02608" => "Identifiant des paramètres monétiques invalide",
			"02609" => "Les paramètres monétiques ne sont pas initialisés",
			"02610" => "Le montant maximum est dépassé",
			"02611" => "Le montant minimum n'est pas atteint",
			"02612" => "Le montant fourni est différent de la transaction initiale",
			"02613" => "La somme maximale de la capture / remboursement est dépassée",
			"02614" => "Clé Luhn invalide",
			"02615" => "Carte virtuelle refusée",
			"02616" => "Erreur lors de la création du portefeuille",
			"02617" => "La transaction est déjà capturée",
			"02618" => "La transaction n'est pas encore capturée",
			"02619" => "Vous n'avez pas l'option de réautorisation",
			"02620" => "La devise doit être la même que l'autorisation initiale",
			"02621" => "Opération non autorisée sur ce site",
			"02622" => "Réautorisation non autorisé sur la carte virtuelle",
			"02623" => "Nombre maximal de tentatives atteint",
			"02624" => "Carte expirée",
			"02625" => "Format du numéro de carte invalide",
			"02626" => "Date d'expiration invalide",
			"02627" => "CVV invalide",
			"02628" => "URL de retour invalide",
			"02629" => "Impossible de décoder le CVV",
			"02630" => "CVV non autorisé pour cette fonctionnalité",
			"02631" => "Délais dépassé",
			"02632" => "Méthode GET non disponible",
			"02633" => "Carte perdue ou volée",
			"02634" => "Code d'option de crédit invalide",
			"02635" => "Date d'expiration invalide",
			"02636" => "CVV invalide",
			"02637" => "Date d'expiration manquant",
			"02638" => "CVV manquant",
			"02639" => "Date d'expiration ou CVV manquant",
			"02640" => "Crédit refusé par le consommateur",
			"02701" => "Montant non disponible",
			"02702" => "Devise non disponible",
			"02703" => "Action non disponible",
			"02712" => "Une donnée privée valide avec la clé \"voucherValue\" est requise",
			"02713" => "Le champ du token n'est pas valide. Il doit être d'être alphanumérique, entre 13 et 19 caractères",
			"02715" => "L'Authentification 3DSecure est obligatoire",
			"02716" => "La liste de contrats sélectionnée doit être remplie avec un seul contrat par mode de paiement",
			"02717" => "La version du service Web est incompatible avec un portefeuille multi-contrats",
			"02718" => "La liste des contrats sélectionnés doit être remplie avec au moins un contrat",
			"02719" => "Le type des contrats est incompatible avec le type de carte",
			"02720" => "Mode de livraison non trouvé pour cette valeur",
			"02721" => "Délai de livraison non trouvé pour cette valeur",
			"02724" => "Le montant doit être vide pour une demande d'informations",
			"02725" => "La valeur de contract.enrolment3DS n'est pas valide. Doit être Y ou N.",
			"02726" => "Soit card.number soit walletId doit être rempli (pas les deux)",
			"02727" => "Identifiant de la carte du portefeuille invalide",
			"02728" => "La réinitialisation est impossible car l'autorisation a été approuvée il y a plus de {0} jours",
			"02729" => "Le montant du remboursement dépasse la limite",
			"02730" => "Le pays de remboursement est hors zone euro",
			"02731" => "La période de remboursement dépasse la limite",
			"02732" => "La devise de remboursement est seulement l'euro",
			"02733" => "Format de champ non valide: {0}: ne peut pas être vide.",
			"02734" => "Le montant du crédit dépasse la limite",
			"02735" => "Le format BIC est incorrect",
			"02736" => "Le format IBAN est incorrect",
			"02737" => "Cette banque n'est pas autorisée à virer",
			"02738" => "Ce compte n'est pas autorisé pour virement",
			"02739" => "Ce virement n'est pas autorisé pour des raisons de sécurité",
			"02740" => "Monnaie de crédit seulement euro",
			"02741" => "Cette commande n'existe pas",
			"02742" => "Requête incohérente: contrat non conforme",
			"02840" => "Le numéro de contrat doit être rempli",
			"02841" => "Trop de numéro de contrat",
			"02842" => "Contrat non associé à la carte",
			"02843" => "La date d'action différée doit être vide si le mode de paiement n'est pas DIF",
			"02998" => "Transaction refusée",
			"03000" => "Transaction acceptée",
			"03001" => "Non inscrit",
			"03002" => "Non participant",
			"03003" => "Authentification echouée",
			"03004" => "Impossible de vérifier un appel d'inscription",
			"03005" => "La transaction existe déjà",
			"03006" => "PARES invalide",
			"03021" => "La vérification de l'inscription a échoué",
			"03022" => "La vérification de l'authentification a échoué",
			"04000" => "Transaction acceptée",
			"04001" => "Transaction acceptée",
			"04002" => "Fraude detectée",
			"04002" => "Fraude detectée",
			"04003" => "Fraude suspectée",
			"04101" => "Champ manquant",
			"04102" => "Nom de règle incorrect",
			"04103" => "Données dupliquées",
			"04104" => "La valeur du champ doit être nulle",
			"04301" => "Données non trouvées",
			"04302" => "Liste vide",
			"04303" => "Configuration non trouvée",
			"04401" => "Faites une authentification 3DSecure",
			"04901" => "Erreur système",
			"04902" => "Accès au service non autorisé",
			"others" => "Transaction échouée, code de retour non géré",
		);

		/**
		 * Constructeur
		 */
		public function __construct(){
			global $config;

			$this->payline = new paylineSDK($this->getMerchantId(), $config['payline_access_key'], '', '', '', '', $this->getContext()=='DEV' ? false:true );

			$this->contract = $config['payline_contract'];
		}

		/**
		 * Redirige l'utilisateur sur la page de paiment du service bancaire
		 * Si une erreur se produit, la méthode lève une exception contenant une description de l'erreur
		 * \return Rien car il y a une redirection à la fin
		 */
		public static function doPayment() {
			return self::getInstance()->_doPayment();
		}

		/**
		 * Renvoie le singleton
		 * \return Le singleton
		 */
		public static function getInstance() {
			if (! self::$Instance) self::$Instance = new self;
			return self::$Instance;
		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lève une exception contenant une description de l'erreur
		 *	@return L'instance
		 */
		public static function getPaymentResult() {
			return self::getInstance()->_getPaymentResult();
		}

		/** Cette fonction permet de charger les informations de livraison.
		 */
		public function widthBilling(){
			$this->with_billing = true;
		}

		/**
		 * Génération du formulaire affichant les cartes disponibles celui-ci redirigera l'utilisateur vers la banque.
		 * Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 * \return Le formulaire html
		 */
		public function _doPayment() {
			global $config;

			$orderId = $this->ord_id ? $this->ord_id : $this->getOrderId();
			$amount = number_format($this->getOrderAmount(), 2, '', '');

			// INITIALIZE
			$array = array();
			$this->payline->returnURL = $this->getUrlPaymentExternal().$config['payline_return_url'];
			$this->payline->cancelURL = $this->getUrlPaymentExternal().$config['payline_cancel_url'];
			$this->payline->notificationURL = $this->getUrlPaymentExternal().$config['payline_notification_url'];

			// PAYMENT
			DEFINE( 'PAYMENT_ACTION', 101 ); // Default payment method
			DEFINE( 'PAYMENT_MODE', 'CPT' ); // Default payment mode

			$array['payment']['amount'] = $amount;
			$array['payment']['currency'] = $config['payline_currency_code'];
			$array['payment']['action'] = PAYMENT_ACTION;
			$array['payment']['mode'] = PAYMENT_MODE;

			// ORDER
			$array['order']['ref'] = $orderId;
			$array['order']['amount'] = $amount;
			$array['order']['currency'] = $config['payline_currency_code'];

			// CONTRACT NUMBERS
			$array['payment']['contractNumber'] = $this->contract;

			// Limite l'accès à certains mode de paiement (via le numéro de contrat)
			if( is_array($this->selected_contracts) && count($this->selected_contracts) ){
				$array['selected_contracts'] = $this->selected_contracts;
			}

			// Enregistre l'accès à la banque dans CouchDB
			$user = ria_mysql_fetch_assoc( gu_users_get($_SESSION['usr_id']) );
			$order = ria_mysql_fetch_assoc(ord_orders_get_simple(array('id' => $array['order']['ref'])));

			// Inclu les informations de facturation
			if( $this->with_billing ){
				$address = ord_orders_address_load( $order, true );

				$array['buyer']['lastName'] = $address['invoice']['lastname'];
				$array['buyer']['firstName'] = $address['invoice']['firstname'];
				$array['buyer']['email'] = $user['email'];
				$array['buyer']['customerId'] = $user['id'];

				$array['billingAddress']['name'] = '';
				$array['billingAddress']['street1'] = $address['invoice']['address1'];
				$array['billingAddress']['street2'] = $address['invoice']['address2'];
				$array['billingAddress']['cityName'] = strtoupper2($address['invoice']['city']);
				$array['billingAddress']['zipCode'] = $address['invoice']['postal_code'];
				$array['billingAddress']['country'] = $address['invoice']['country'];
				$array['billingAddress']['phone'] = $address['invoice']['phone'];
			}

			$this->data_couchDB['user_id'] = $user['id'];
			$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
			$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
			$this->data_couchDB['user_email'] = $user['email'];

			$this->data_couchDB['ord_id'] = $order['id'];
			$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
			$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];
			$this->data_couchDB['data'] = $array;
			$this->savePaymentInCouchDB();

			// EXECUTE
			$result = $this->payline->doWebPayment($array);
			if( $result && !is_array($result) ){
				throw new Exception('ERROR : '.$result->shortMessage.' '.$result->longMessage);
			}else if( $result && $result['result']['code'] == '00000'){
				header('Location: '.$result['redirectURL']);
				exit;
			}
			else{
				throw new Exception('ERROR : '.$result['result']['code']. ' '.$result['result']['longMessage']);
			}

		}

		/**
		 *	Cette méthode traite la réponse du service bancaire pour mettre à jour la commande
		 *	Si une erreur se produit, la méthode lance une exception contenant une description de l'erreur
		 *	\return L'instance
		 */
		public function _getPaymentResult() {
			global $config;

			if( !isset($_GET['token']) ){
				throw new Exception( "Payline : Le token est absent" );
			}

			// questionne payline pour avoir le détail avec le token fourni
			$array = array();
			$array['version'] = 3;
			$array['token'] = $_GET['token'];

			$result = $this->payline->getWebPaymentDetails($array);

			if( !isset($result['result']['code']) ){
				return false;
			}

			if( !isset($result['order']['ref']) ){
				throw new Exception( "Payline : L'identifiant de la transaction est invalide." );
			}

			$orderId = $result['order']['ref'];

			$r_order = ord_orders_get_simple(array('id' => $orderId) );
			if( !$r_order || !ria_mysql_num_rows($r_order) ){
				return false;
			}

			$order = ria_mysql_fetch_assoc( $r_order );
			{ // Enregistre le retour de la banque dans CouchDB
				$name = $this->returns[(array_key_exists($result['result']['code'], $this->returns) ? $result['result']['code'] : "others")];
				$user = ria_mysql_fetch_assoc(gu_users_get($order['usr_id']));

				$this->data_couchDB['ord_id'] = $order['id'];
				$this->data_couchDB['ord_total_ht'] = $order['total_ht'];
				$this->data_couchDB['ord_total_ttc'] = $order['total_ttc'];

				$this->data_couchDB['user_id'] = $user['id'];
				$this->data_couchDB['user_firstname'] = $user['adr_firstname'];
				$this->data_couchDB['user_lastname'] = $user['adr_lastname'];
				$this->data_couchDB['user_email'] = $user['email'];

				$this->data_couchDB['data'] = $result;
				$this->data_couchDB['code_id'] = $result['result']['code'];
				$this->data_couchDB['code_name'] = $name;
				$this->saveReturnPaymentInCouchDB();
			}

			if ( !in_array($result['result']['code'], array('00000', '10000', '02017'))) {
				return false;
			}

			if (!in_array($order['state_id'], array(_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_BASKET_PAY_CB))) {
				return false;
			}

			if( !is_numeric($order['total_ht']) || $order['total_ht'] <= 0 ){
				error_log('[Payline] Montant de la commande null : '.$order['id']);
				return false;
			}

			if( $result['payment']['contractNumber'] == $config['payline_contract_x3'] ){
				ord_orders_pay_type_set($orderId, _PAY_COFIDIS);
			}elseif ($result['result']['code'] == '10000') {
				ord_orders_pay_type_set($orderId, _PAY_PAYPAL);
			}else{
				ord_orders_pay_type_set($orderId, _PAY_CB);
			}

			if (in_array($order['state_id'], array(_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_BASKET_PAY_CB))) {
				if ($result['result']['code'] == '02017') {
					mail('<EMAIL>', 'Payline code 02017', 'Commande : '.$orderId);
				}

				ord_orders_update_status($orderId, _STATE_WAIT_PAY, '');
			}

			if ($result['result']['code'] != '02017') {
				ord_orders_update_status($orderId, _STATE_PAY_CONFIRM, '');
			}

			// Confirmation de la commande à NetAffiliation
			$affi = new NetAffiliation();
			$affi->getOnlinePaymentTag( $orderId );

			return $orderId;
		}


		/**
		 *	Cette fonction returne le merchant_id à utiliser en fonction de si c'est un paiement 3dsecure ou non
		 */
		public function getMerchantId(){
			global $config;
			return $config['payline_merchant_id'];
		}

		/**
		 *	Permet de surcharger le ord_id
		 *	\param $ord_id Obligatoire, identifiant de commande à utiliser
		 */
		public function setOrdId( $ord_id ){
			$this->ord_id = $ord_id;
		}

		/**
		 *	(surcharge) Renvoie l'identifiant de la commande
		 *	Lance une exception si n'existe pas ou non valide
		 *	\return L'identifiant de la commande
		 */
		protected function getOrderId() {
			if( is_numeric($this->ord_id) && $this->ord_id > 0 ) return $this->ord_id;

			if (! (isset($_SESSION['ord_id']) && is_numeric($_SESSION['ord_id']))) throw new exception('Impossible de récupérer la commande ! (tnt_id = ' . $GLOBALS['config']['tnt_id'] . ', class = ' . get_class($this) . ')');
			return $_SESSION['ord_id'];
		}

		/**
		 *	Permet de surcharger le contrat
		 *	\param $contract_id Obligatoire, identifiant de du contrat
		 */
		public function setContract($contract_id){
			$this->contract = $contract_id;
		}

		/**
		 *	Permet de limiter la page de paiement à certains choix de paiement
		 *	@param $selected_contract Obligatoire, identifiant de du contrat
		 */
		public function setSelectedContract($selected_contract){
			if( trim($selected_contract) == '' ){
				return false;
			}

			if( $this->selected_contracts === false ){
				$this->selected_contracts = array();
			}

			$this->selected_contracts[] = $selected_contract;
		}
	}
