<?php
/**
 * This file has been @generated by a phing task from CLDR version 32.0.0.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AD' => 'Andorra',
  'AE' => 'Emirâts araps unîts',
  'AF' => 'Afghanistan',
  'AG' => 'Antigua e Barbuda',
  'AI' => 'Anguilla',
  'AL' => 'Albanie',
  'AM' => 'Armenie',
  'AO' => 'Angola',
  'AQ' => 'Antartic',
  'AR' => 'Argjentine',
  'AS' => 'Samoa merecanis',
  'AT' => 'Austrie',
  'AU' => 'Australie',
  'AW' => 'Aruba',
  'AX' => 'Isulis Aland',
  'AZ' => 'Azerbaigian',
  'BA' => 'Bosnie e Ercegovine',
  'BB' => 'Barbados',
  'BD' => 'Bangladesh',
  'BE' => 'Belgjiche',
  'BF' => 'Burkina Faso',
  'BG' => 'Bulgarie',
  'BH' => 'Bahrain',
  'BI' => 'Burundi',
  'BJ' => 'Benin',
  'BL' => 'Sant Barthélemy',
  'BM' => 'Bermuda',
  'BN' => 'Brunei',
  'BO' => 'Bolivie',
  'BR' => 'Brasîl',
  'BS' => 'Bahamas',
  'BT' => 'Bhutan',
  'BW' => 'Botswana',
  'BY' => 'Bielorussie',
  'BZ' => 'Belize',
  'CA' => 'Canade',
  'CC' => 'Isulis Cocos',
  'CD' => 'Republiche Democratiche dal Congo',
  'CF' => 'Republiche centri africane',
  'CG' => 'Congo - Brazzaville',
  'CH' => 'Svuizare',
  'CI' => 'Cueste di Avoli',
  'CK' => 'Isulis Cook',
  'CL' => 'Cile',
  'CM' => 'Camerun',
  'CN' => 'Cine',
  'CO' => 'Colombie',
  'CR' => 'Costa Rica',
  'CU' => 'Cuba',
  'CV' => 'Cjâf vert',
  'CX' => 'Isule Christmas',
  'CY' => 'Cipri',
  'CZ' => 'Republiche ceche',
  'DE' => 'Gjermanie',
  'DG' => 'Diego Garcia',
  'DJ' => 'Gibuti',
  'DK' => 'Danimarcje',
  'DM' => 'Dominiche',
  'DO' => 'Republiche dominicane',
  'DZ' => 'Alzerie',
  'EA' => 'Ceuta e Melilla',
  'EC' => 'Ecuador',
  'EE' => 'Estonie',
  'EG' => 'Egjit',
  'EH' => 'Sahara ocidentâl',
  'ER' => 'Eritree',
  'ES' => 'Spagne',
  'ET' => 'Etiopie',
  'FI' => 'Finlandie',
  'FJ' => 'Fizi',
  'FK' => 'Isulis Falkland',
  'FM' => 'Micronesie',
  'FO' => 'Isulis Faroe',
  'FR' => 'France',
  'GA' => 'Gabon',
  'GB' => 'Ream unît',
  'GD' => 'Grenada',
  'GE' => 'Gjeorgjie',
  'GF' => 'Guiana francês',
  'GG' => 'Guernsey',
  'GH' => 'Ghana',
  'GI' => 'Gjibraltar',
  'GL' => 'Groenlande',
  'GM' => 'Gambia',
  'GN' => 'Guinee',
  'GP' => 'Guadalupe',
  'GQ' => 'Guinee ecuatoriâl',
  'GR' => 'Grecie',
  'GS' => 'Georgia dal Sud e Isulis Sandwich dal Sud',
  'GT' => 'Guatemala',
  'GU' => 'Guam',
  'GW' => 'Guinea-Bissau',
  'GY' => 'Guyana',
  'HK' => 'Regjon aministrative speciâl de Cine di Hong Kong',
  'HN' => 'Honduras',
  'HR' => 'Cravuazie',
  'HT' => 'Haiti',
  'HU' => 'Ongjarie',
  'IC' => 'Isulis Canariis',
  'ID' => 'Indonesie',
  'IE' => 'Irlande',
  'IL' => 'Israêl',
  'IM' => 'Isule di Man',
  'IN' => 'India',
  'IO' => 'Teritori britanic dal Ocean Indian',
  'IQ' => 'Iraq',
  'IR' => 'Iran',
  'IS' => 'Islande',
  'IT' => 'Italie',
  'JE' => 'Jersey',
  'JM' => 'Gjamaiche',
  'JO' => 'Jordanie',
  'JP' => 'Gjapon',
  'KE' => 'Kenya',
  'KG' => 'Kirghizstan',
  'KH' => 'Camboze',
  'KI' => 'Kiribati',
  'KM' => 'Comoris',
  'KN' => 'San Kitts e Nevis',
  'KP' => 'Coree dal nord',
  'KR' => 'Coree dal sud',
  'KW' => 'Kuwait',
  'KY' => 'Isulis Cayman',
  'KZ' => 'Kazachistan',
  'LA' => 'Laos',
  'LB' => 'Liban',
  'LC' => 'Sante Lusie',
  'LI' => 'Liechtenstein',
  'LK' => 'Sri Lanka',
  'LR' => 'Liberie',
  'LS' => 'Lesotho',
  'LT' => 'Lituanie',
  'LU' => 'Lussemburc',
  'LV' => 'Letonie',
  'LY' => 'Libie',
  'MA' => 'Maroc',
  'MC' => 'Monaco',
  'MD' => 'Moldavie',
  'ME' => 'Montenegro',
  'MF' => 'Sant Martin',
  'MG' => 'Madagascar',
  'MH' => 'Isulis Marshall',
  'MK' => 'Macedonie',
  'ML' => 'Mali',
  'MM' => 'Birmanie',
  'MN' => 'Mongolie',
  'MO' => 'Regjon aministrative speciâl de Cine di Macao',
  'MP' => 'Isulis Mariana dal Nord',
  'MQ' => 'Martiniche',
  'MR' => 'Mauritanie',
  'MS' => 'Montserrat',
  'MT' => 'Malta',
  'MU' => 'Maurizi',
  'MV' => 'Maldivis',
  'MW' => 'Malawi',
  'MX' => 'Messic',
  'MY' => 'Malaysia',
  'MZ' => 'Mozambic',
  'NA' => 'Namibie',
  'NC' => 'Gnove Caledonie',
  'NE' => 'Niger',
  'NF' => 'Isole Norfolk',
  'NG' => 'Nigerie',
  'NI' => 'Nicaragua',
  'NL' => 'Paîs bas',
  'NO' => 'Norvegje',
  'NP' => 'Nepal',
  'NR' => 'Nauru',
  'NU' => 'Niue',
  'NZ' => 'Gnove Zelande',
  'OM' => 'Oman',
  'PA' => 'Panamà',
  'PE' => 'Perù',
  'PF' => 'Polinesie francês',
  'PG' => 'Papue Gnove Guinee',
  'PH' => 'Filipinis',
  'PK' => 'Pakistan',
  'PL' => 'Polonie',
  'PM' => 'San Pierre e Miquelon',
  'PN' => 'Pitcairn',
  'PR' => 'Porto Rico',
  'PS' => 'Teritoris palestinês',
  'PT' => 'Portugal',
  'PW' => 'Palau',
  'PY' => 'Paraguay',
  'QA' => 'Qatar',
  'RE' => 'Reunion',
  'RO' => 'Romanie',
  'RS' => 'Serbie',
  'RU' => 'Russie',
  'RW' => 'Ruande',
  'SA' => 'Arabie Saudide',
  'SB' => 'Isulis Salomon',
  'SC' => 'Seychelles',
  'SD' => 'Sudan',
  'SE' => 'Svezie',
  'SG' => 'Singapore',
  'SH' => 'Sante Eline',
  'SI' => 'Slovenie',
  'SJ' => 'Svalbard e Jan Mayen',
  'SK' => 'Slovachie',
  'SL' => 'Sierra Leone',
  'SM' => 'San Marin',
  'SN' => 'Senegal',
  'SO' => 'Somalie',
  'SR' => 'Suriname',
  'ST' => 'Sao Tomè e Principe',
  'SV' => 'El Salvador',
  'SY' => 'Sirie',
  'SZ' => 'Swaziland',
  'TA' => 'Tristan da Cunha',
  'TC' => 'Isulis Turks e Caicos',
  'TD' => 'Çad',
  'TF' => 'Teritoris meridionâi francês',
  'TG' => 'Togo',
  'TH' => 'Tailandie',
  'TJ' => 'Tazikistan',
  'TK' => 'Tokelau',
  'TL' => 'Timor orientâl',
  'TM' => 'Turkmenistan',
  'TN' => 'Tunisie',
  'TO' => 'Tonga',
  'TR' => 'Turchie',
  'TT' => 'Trinidad e Tobago',
  'TV' => 'Tuvalu',
  'TW' => 'Taiwan',
  'TZ' => 'Tanzanie',
  'UA' => 'Ucraine',
  'UG' => 'Uganda',
  'UM' => 'Isulis periferichis minôrs dai Stâts Unîts',
  'US' => 'Stâts Unîts',
  'UY' => 'Uruguay',
  'UZ' => 'Uzbechistan',
  'VA' => 'Vatican',
  'VC' => 'San Vincent e lis Grenadinis',
  'VE' => 'Venezuela',
  'VG' => 'Isulis vergjinis britanichis',
  'VI' => 'Isulis vergjinis americanis',
  'VN' => 'Vietnam',
  'VU' => 'Vanuatu',
  'WF' => 'Wallis e Futuna',
  'WS' => 'Samoa',
  'YE' => 'Yemen',
  'YT' => 'Mayotte',
  'ZA' => 'Sud Afriche',
  'ZM' => 'Zambia',
  'ZW' => 'Zimbabwe',
);
