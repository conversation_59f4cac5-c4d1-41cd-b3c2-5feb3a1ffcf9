<?php

	/**	\file order.php
	 *	Cette page est chargée d'afficher une pièce de vente
	 */

	require_once('orders.inc.php');
	require_once('ord.returns.inc.php');
	require_once('delivery.inc.php');
	require_once('promotions.inc.php');
	require_once('fields.inc.php');
	require_once('strings.inc.php');
	require_once 'admin/view.order.inc.php';

	/** Détermine quelle date est sélectionnée par défault pour le select "select-sign-date".
	 *
	 * \param  string|null $value Date sauvegardé en BDD, null si pas pas de valeur
	 * \param  array       $dates Tableau de dates à comparer avec le paramètre $date
	 * \param  string      $date  Date à comparer
	 * \return string
	 */
	function select_default_date_option( $value, array $dates, $date ){
		// Si $value est null, cela signifie que le délai de signature n'a pas encore été renseigné.
		// Nous selectionnons donc la valeur par défaut qui est de 1 mois.
		if( is_null($value) && $date === date('Y-m-d', strtotime('1 month')) ){
			return 'selected';
		}

		// Gère le cas "délai dépassé" quand la date de signature est dans le passé.
		if( strtotime($value) <= time() ){
			return strtotime($date) <= time() ? 'selected' : '';
		}

		// Sélectionne par défault l'option de date la plus proche.
		// Il faut exclure l'option qui est strictement égale à $value car c'est celle qui correspond au "délai dépassé".
		return find_nearest_date_in_array(array_filter($dates, function ($d) use ($value) {
			return $d !== $value;
		}), $value) === $date ? 'selected' : '';
	}

	// L'identifiant de la pièce est obligatoire et il faut disposer d'un accès à cette page
	if( isset($_GET['ord']) && $_GET['ord']=='new' ){
		gu_if_authorized_else_403('_RGH_ADMIN_ORDER_CREATE');
	}

	// Vérifie que l'utilisateur en cours à le droit d'accéder à cette page
	gu_if_authorized_else_403('_RGH_ADMIN_ORDER_EDIT');

	unset( $error, $no_error );
	if( !isset($_GET['state']) ) $_GET['state'] = '';

	// Indique une commande Place de marché en édition pour validation
	$ctr_validation = false;
	// Si impossible de charger la commande
	$no_ord = false;

	// Vérifie les paramètres d'entrée
	// On récupère la commande via $_GET['ord'] (commande valide) ou via $_GET['token'] + $_GET['ctr'] (validation d'une commande masquée de place de marché)
	if( isset($_GET['token'], $_GET['ctr']) ){
		$ord_id = ord_orders_get_from_ctr_token( $_GET['token'], $_GET['ctr'] );
		if( !is_numeric($ord_id) || $ord_id <= 0 ){
			$no_ord = true;
		}else{
			$ctr_validation = true;
			$_GET['ord'] = $ord_id;
		}
	}elseif( !isset($_GET['ord']) || !ord_orders_exists( $_GET['ord'], 0, 0, true ) ){
		$no_ord = true;
	}

	//
	if( isset($_GET['ord']) && $_GET['ord']=='new' ){
		if( isset($_GET['usr']) ){
			if( ord_carts_add_if_not_exists(true) ){
				ord_orders_attach($_SESSION['ord_id'], $_GET['usr']);
				header('Location: order.php?ord='.$_SESSION['ord_id']);
				unset($_SESSION['ord_id']);
				exit;
			}
		}else{
			if( ord_carts_add_if_not_exists(true) ){
				header('Location: order.php?ord='.$_SESSION['ord_id']);
				unset($_SESSION['ord_id']);
				exit;
			}
		}
	}

	// redirection vers la liste des commandes
	if( $no_ord ){
		if( isset($_GET['state']) && is_numeric($_GET['state']) )
			header('Location: orders.php?state='.$_GET['state']);
		else
			header('Location: orders.php');
		exit;
	}

	// Signalement de la commande comme frauduleuse
	if( isset($_GET['action']) && $_GET['action']=='signal-spam' ){
		$r1 = $r2 = false;
		// Passe la commande à l'état 10 - Annulé par le marchand
		$r1 = ord_orders_state_update( $_GET['ord'], _STATE_CANCEL_MERCHAND );
		// Charge l'adresse IP associée à la commande
		$r_origin = stats_origins_get( $_GET['ord'], CLS_ORDER );
		if( $r_origin ){
			$origin = ria_mysql_fetch_array( $r_origin );
		}
		if( isset($origin['ip']) ){
			// Marque l'adresse IP du spammeur comme blacklistée
			$r2 = ats_ips_add( $origin['ip'] );
		}
		if( $r1 && $r2 ){
			$no_error = _('Suite à votre signalement, la commande a été annulée et l\'adresse IP de l\'internaute a été bloquée. Il ne pourra plus effectuer de commande par carte sur le site.');
		}elseif( $r1 ){
			$no_error = _('Suite à votre signalement, la commande de cet internaute a été annulée.');
		}elseif( !$r1 && !$r2 ){
			$error = _('Une erreur s\'est produite lors de l\'annulation de la commande. Veuillez réessayer ou prendre contact avec l\'administrateur.');
		}
	}

	// création d'un retour
	if( !$ctr_validation && isset($_POST['createReturn']) ){
		header('Location: /admin/orders/returns/return.php?ret=0&ord='.$_GET['ord']);
		exit;
	}

	// Codes promotions
	if( !$ctr_validation && isset($_POST['pmt-code'], $_POST['attach']) && trim($_POST['pmt-code']) ){

		$applicable = pmt_codes_is_applicable($_POST['pmt-code'],$_GET['ord']);
		if( $applicable===true ){
			// Le code est valide et peut s'appliquer à la commande
			if( !pmt_codes_apply($_POST['pmt-code'],$_GET['ord']) ){
				$error = _('Une erreur inconnue est survenue lors de l\'application de votre code promotion sur votre commande.');
			}
		}else{
			switch( $applicable ){
				case ERR_PMT_CODE_NOT_FOUND:
					$error = sprintf(_('Le code %s ne correspond à aucun code promotion en cours de validité'), pmt_codes_format($_POST['pmt-code']));
					break;
				case ERR_ORD_ORDER_NOT_FOUND:
					$error = _('Votre panier n\'a pas été trouvé. Ceci est une erreur grave ne devant pas survenir lors d\'une utilisation normale. Nous vous remercions de nous contacter au plus vite pour nous signaler la survenue de cette erreur.');
					break;
				case ERR_GU_USER_NOT_FOUND:
					$error = _('Votre compte client n\'a pas été trouvé. Ceci est une erreur grave ne devant pas survenir lors d\'une utilisation normale. Nous vous remercions de nous contacter au plus vite pour nous signaler la survenue de cette erreur.');
					break;
				case ERR_PMT_ORD_INSUFFICIENT:
					$error = _('Le montant total de votre commande n\'est pas suffisant pour profiter de ce code promotion.');
					break;
				case ERR_PMT_CODE_FULL:
					$error = _('Ce code promotion a malheureusement atteint son nombre maximal d\'utilisations.');
					break;
				case ERR_PMT_CODE_ALREADY_USED:
					$error = _('Ce code promotion est limité à une seule utilisation par client. Il semble que vous ayez déjà profité de ce code.');
					break;
				case ERR_PMT_ORD_NO_PRODUCTS:
					$error = sprintf(_('Le code promotion %s ne concerne aucun des produits de votre commande. Il ne peut donc s\'y appliquer.'), pmt_codes_format($_POST['pmt-code']));
					break;
				case ERR_PMT_CODE_INCOMING:
					$code = ria_mysql_fetch_array(pmt_codes_get(NULL,$_POST['pmt-code']));
					$error = sprintf(_('Le code promotion %s ne sera pas utilisable avant le '.$code['date_start'].' à '.$code['hour_start'].'.'), pmt_codes_format($_POST['pmt-code']));
					break;
				case ERR_PMT_CODE_CLOSED:
					$code = ria_mysql_fetch_array(pmt_codes_get(NULL,$_POST['pmt-code']));
					$error = sprintf(_('Le code promotion %s n\'est plus utilisable depuis le '.$code['date_start'].' à '.$code['hour_start'].'.'), pmt_codes_format($_POST['pmt-code']));
					break;
				case ERR_PMT_USER_EXCLUDED:
					$error = _('Votre compte client ne vous permet de profiter de ce code promotion.');
					break;
				case ERR_FIRST_ORDER_ONLY:
					$error = sprintf(_('Le code promotion %s est applicable seulement à la première commande.'), pmt_codes_format($_POST['pmt-code']));
					break;
				default:
					$error = _('Une erreur inconnue est survenue lors de la vérification de validité de votre code promotion. Veuillez nous excuser pour la gêne occasionnée.');
					break;
			}
		}
	}

	// Permet le détachement du code promotion
	if( !$ctr_validation && isset($_POST['detach']) && !empty($_POST['detach'])){
		foreach($_POST['detach'] as $promotion_id => $v){
			pmt_codes_cancel($_GET['ord'], true, $promotion_id);
		}
	}

	// champ avancé spécial Animal & Co - non configurable (LDD)
	if( $config['tnt_id'] == 16 ){
		$config['fld_order_ldd'] = 1024;
		$config['srv_ldd'] = 139;
	}
	$allow_delivery_ldd = isset($config['fld_order_ldd'], $config['srv_ldd']);
	$allow_delivery = gu_user_is_authorized('_RGH_ADMIN_ORDER_ALLOW_DELIVERY') || $allow_delivery_ldd;

	$delivery_previous_states = isset($config['delivery_previous_states']) && is_array($config['delivery_previous_states'])
		? $config['delivery_previous_states']
		: array();

	$delivery_states = isset($config['delivery_states']) && is_array($config['delivery_states'])
		? $config['delivery_states']
		: array();

	$allow_update_state_delivery = $allow_delivery && !$allow_delivery_ldd
		&& isset($config['allow_orders_update_state'])
		&& $config['allow_orders_update_state'];

	// Modification de l'état de la commande
	if( !$ctr_validation && isset($_POST['save-newstate'], $_POST['newstate']) && is_numeric($_POST['newstate']) ){
		if( $allow_delivery_ldd && $_POST['newstate']==7 ){
			// traitement spécial si LDD activée
			$rorder = ord_orders_get( 0, $_GET['ord'] );
			if( !$rorder || !ria_mysql_num_rows($rorder) ){
				$error = _("Le chargement de la commande a échoué pour une raison inconnue.");
			}else{
				$order = ria_mysql_fetch_array( $rorder );
				if( $order['state_id'] >= 7 ){
					$error = _("La commande a déjà été transformée en bon de livraison.");
				}else{
					$bl_id = ord_bl_add_sage( $order['user'], 'LD'.$order['id'], '', date('d/m/Y H:i:s'), 6, $config['srv_ldd'] );
					if( $bl_id ){
						$have_rows = false;
						$have_rows_invalid = false;
						if( $rproducts = ord_products_get($order['id']) ){
							while( $p = ria_mysql_fetch_array($rproducts) ){
								if( !prd_products_is_port($p['ref']) && prd_products_get_countermark($p['id']) ){
									$have_rows = true;
									if( !ord_bl_products_add_sage(
										$bl_id, $p['id'], $p['line'], $p['ref'], $p['name'], $p['qte'], $p['price_ht'], $p['tva_rate'], $order['id'], ''
									) ){
										$have_rows_invalid = true;
									}
								}
							}
						}
						if( $have_rows && !$have_rows_invalid ){
							ord_bl_update_totals( $bl_id );
							ord_bl_apply_pmt( $bl_id );
							if( ord_orders_update_status( $order['id'], 7 ) ){
								if( ord_bl_state_set( $bl_id, 7 ) ){
									ord_bl_notify( $bl_id );
								}else{
									ord_bl_del($bl_id);
									$error = _("Une erreur est survenue pendant la mise à jour du bon de livraison.");
								}
							}else{
								ord_bl_del($bl_id);
								$error = _("Une erreur est survenue pendant la mise à jour du statut de la commande.");
							}
						}else{
							ord_bl_del($bl_id);
							$error = _("Le bon de commande ne contient aucune ligne permettant sa transformation en bon de livraison.");
						}
					}else{
						$error = _("Le bon de livraison n'a pas pu être crée pour une raison inconnue.");
					}
				}
			}

		}else{
			$rorder = ord_orders_get( 0, $_GET['ord'] );
			if( !$rorder || !ria_mysql_num_rows($rorder) ){
				$error = _('Le chargement de la commande a échoué pour une raison inconnue.');
			}else{
				$order = ria_mysql_fetch_array($rorder);

				if(isset($config['active_popup_products_invoice']) && $config['active_popup_products_invoice'] && $_POST['newstate'] == _STATE_INVOICE ){
					if( $order['state_id'] != _STATE_INVOICE ){ // Si la commande est déja en état facturée, ne fait rien
						$inv = ord_invoices_add( $order['user'], date("d/m/Y") );

						if( !$inv ){
							$error = _("La création de la facture a échoué pour une raison inconnue.");
						}else{

							$qte_inv = array();

							//Récupère les factures lié à la commande
							$r_inv = ord_invoices_get( 0, 0, 0, false, false, false, false, $_GET['ord'], false, false, true );
							if( $r_inv ){
								while( $ord_inv = ria_mysql_fetch_assoc($r_inv)){
									//Récupère la quantité des produits déja facturés
									$r_inv_product = ord_inv_products_get( $ord_inv['id']);
									if( $r_inv_product ){
										while( $inv_product = ria_mysql_fetch_assoc($r_inv_product) ){
											if( isset($qte_inv[$inv_product['id']]) ){
												$qte_inv[$inv_product['id']] = $qte_inv[$inv_product['id']] + $inv_product['qte'];
											}else{
												$qte_inv[$inv_product['id']] = $inv_product['qte'];
											}
										}
									}
								}
							}


							$r_product = ord_products_get( $order['id'] );
							if( !$r_product || !ria_mysql_num_rows($r_product) ){
								$error = _("Le chargement des produits de la commande a échoué pour une raison inconnue.");
							}else{
								while( $product = ria_mysql_fetch_assoc($r_product) ){
									$qte = isset($qte_inv[$product['id']])? $product['qte'] - $qte_inv[$product['id']] : $product['qte'];
									if( $qte > 0){
									    ord_inv_products_add( $inv, $product['id'], $product['line'], $product['ref'], $product['name'], $product['price_ht'], $qte, $product['tva_rate'], $order['id']);
									}
								}
							}
						}
					}
				}

				/**
				 * Applique les valeurs par défaut pour les champs "_FLD_ORD_SIGN_RATE" et "_FLD_ORD_PIPE_SIGN_DATE" si non renseigné.
				 *
				 * Valeurs par défaut:
				 * 	   - _FLD_ORD_SIGN_RATE       --> 3 (30% de chance de signature)
				 *     - _FLD_ORD_PIPE_SIGN_DATE  --> 1 mois
				 */
				if( in_array($_POST['newstate'], array(_STATE_DEVIS)) ){
					if( !fld_object_values_get($_GET['ord'], _FLD_ORD_PIPE_SIGN_DATE) ){
						fld_object_values_set($_GET['ord'], _FLD_ORD_PIPE_SIGN_DATE, date('Y-m-d 00:00:00', strtotime('+1 month')));
					}

					if( !fld_object_values_get($_GET['ord'], _FLD_ORD_SIGN_RATE) ){
						fld_object_values_set($_GET['ord'], _FLD_ORD_SIGN_RATE, 3);
					}
				}

				if( $_POST['newstate'] == 25 && (!isset($config['sys_cheque_actived']) || !$config['sys_cheque_actived'] || !in_array($order['pay_id'], array(_PAY_CHEQUE, _PAY_VIREMENT, _PAY_CHEQUE_X))) ){
					$error = _('La modification du statut de la commande a échoué car celle-ci n\'a pas été payée par chèque ou virement.');
				}elseif( !ord_orders_state_update($_GET['ord'], $_POST['newstate'], '', true, $_SESSION['usr_id'], false) ){
					$error = _('La modification du statut de la commande a échoué pour une raison inconnue.');
				}elseif( $_POST['newstate'] == _STATE_DEVIS && isset($_POST['need_sync']) && $_POST['need_sync'] ){
					if( !ord_orders_set_need_sync($_GET['ord']) ){
						$error = _('Une erreur est survenue lors de la modification du statut de commande.');
					}
				}else{
					if( isset($_GET['state']) && is_numeric($_GET['state']) ){
						header('Location: order.php?ord='.$_GET['ord'].'&state='.$_GET['state']);
					}else{
						header('Location: order.php?ord='.$_GET['ord']);
					}
					exit;
				}
			}
		}
	}

	// Rattachement à un modèle de saisie
	if( !$ctr_validation && isset($_POST['addmdl']) && isset($_POST['model-pick']) && is_numeric($_POST['model-pick']) && $_POST['model-pick']>0 ){
		$res = fld_object_models_add( $_GET['ord'],$_POST['model-pick'] );
		if( $res===false ){
			$error = sprintf(_("L'ajout du modèle de saisie à la commande %d a échoué pour une raison inconnue."), $_GET['ord']);
		}else{
			header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
			exit;
		}
	}

	// Détachement d'un modèle de saisie
	if( !$ctr_validation && isset($_GET['delmdl']) && is_numeric($_GET['delmdl']) && $_GET['delmdl']>0 ){
		$res = fld_object_models_del( $_GET['ord'],$_GET['delmdl'] );
		if( $res===false ){
			$error = sprintf(_("La suppression du modèle de saisie pour la commande %d a échoué pour une raison inconnue."), $_GET['ord']);
		}else{
			header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
			exit;
		}
	}

	// Sauvegarde des champs avancés
	if( !$ctr_validation && isset($_POST['savefields']) ){
		$fields = fld_fields_get( 0,0,-2,0,0,0,null,array(),false,array(),null,CLS_ORDER );
		$notify_port = false; // Par défaut aucune notification est envoyé
		while( $f = ria_mysql_fetch_array($fields) ){
			if( $f['type_id']==FLD_TYPE_SELECT_MULTIPLE && !isset($_POST['fld'.$f['id']]) ){
				$_POST['fld'.$f['id']] = '';
			}
			if( isset($_POST['fld'.$f['id']]) ){
				$value = $_POST['fld'.$f['id']];
				fld_object_values_set( $_GET['ord'], $f['id'], $value );

				//Verification si un champs avancé lié au frais de port est modifier
				if( $f['id'] == FLD_PRODUCT_FDP_1|| $f['id'] == FLD_PRODUCT_FDP_2 ){
					$notify_port = true;
				}
			}
		}
		if(gu_user_is_authorized('_RGH_ADMIN_CONFIG_DLV_PORT') && $notify_port){
			$res = ord_order_notify_port($_GET['ord']);
			if(!$res){
				$error = sprintf(_("Erreur lors de l'envoi de la notification."), $_GET['ord']);
			}

		}
		header('Location: /admin/orders/order.php?ord='.$_GET['ord']);
		exit;
	}

	/**
	 * Création d'un acompte ou échéance
	 */
	if( !$ctr_validation && !isset($_POST['add-deposit']) ){

		if( isset($_POST['new-deposit'], $_POST['new-deposit-date'], $_POST['new-deposit-amount']) && $_POST['new-deposit'] == $_GET['ord'] ){ // Enregistrement d'un acompte
			$is_deadline = (isset($_POST['new-deposit-type']) && $_POST['new-deposit-type'] == 'deadline');

			if( !is_numeric($_POST['new-deposit-amount']) || !isdate($_POST['new-deposit-date']) ){
				$error_deposit = true;
				if( $is_deadline ){
					$error = _('Veuillez saisir un montant et une date pour la création de l\'échéance.');
				}else{
					$error = _('Veuillez saisir un montant et une date pour la création de l\'acompte.');
				}

			}else{
				$current_timestamp = time();
				$exp_date = dateheureparse($_POST['new-deposit-date']);

				if( strtotime($exp_date) > $current_timestamp ){
					$deposit_amount = (float)$_POST['new-deposit-amount'];

					if( $deposit_amount > 0 ){
						$rord = ord_orders_get(0, $_GET['ord']);

						if( ria_mysql_num_rows($rord) ){
							$or = ria_mysql_fetch_assoc($rord);

							if( is_numeric($or['state_id']) && $or['state_id'] != _STATE_INVOICE ){

								if( $deposit_amount > $or['total_ttc'] ){
									$error_deposit = true;
									if( $is_deadline ){
										$error = _('Le montant de la nouvelle échéance ne peut pas excédé le montant total TTC de la commande.');
									}else{
										$error = _('Le montant du nouvel acompte ne peut pas excédé le montant total TTC de la commande.');
									}
								}else{
									$deposit_rest = $or['total_ttc'] - $deposit_amount;

									if( $deposit_rest > $deposit_amount ){
										$deposit_rest = 0;
									}

									if( ord_installments_add(($or['pay_id']>0 ? $or['pay_id'] : 1), 1, $or['id'], $deposit_amount, $deposit_rest, false, $exp_date, null, false, 0, $is_deadline ) ){
										if( $is_deadline ){
											$no_error = _('L\'échéance a bien été enregistrée.');
										}else{
											$no_error = _('L\'acompte a bien été enregistré.');
										}
									}else{
										$error_deposit = true;
										if( $is_deadline ){
											$error = _('L\'échéance n\'a pu être enregistrée.');
										}else{
											$error = _('L\'acompte n\'a pu être enregistré.');
										}
									}
								}

							}else{
								$error = _('Commande invalide.');
							}

						}else{
							$error = _('Commande invalide.');
						}
					}else{
						$error_deposit = true;
						if( $is_deadline ){
							$error = _('Le montant de l\'échéance ne peut être inférieur ou égal à 0.');
						}else{
							$error = _('Le montant de l\'acompte ne peut être inférieur ou égal à 0.');
						}
					}
				}else{
					$error_deposit = true;
					if( $is_deadline ){
						$error = _('Vous ne pouvez pas enregistrer une échéance ayant une date d\'expiration invalide.');
					}else{
						$error = _('Vous ne pouvez pas enregistrer un acompte ayant une date d\'expiration invalide.');
					}
				}
			}
		}elseif( isset($_POST['relaunch']) ){ // Relance

			$rord = ord_orders_get( 0,$_GET['ord'] );
			if( ria_mysql_num_rows($rord) ){
				$or = ria_mysql_fetch_assoc($rord);
				$ritm = ord_installments_get(0, 1, $_GET['ord'], true);
				$solde = 0;
				if( ria_mysql_num_rows($ritm) ){
					$itm = ria_mysql_fetch_assoc($ritm);
					$solde = $or['total_ttc'] - $itm['total'];
				}

				// Ecart significatif : la relance est envoyée immédiatement
				if( $solde > 0.1 ){
					if( $or['relanced']===true )
						ord_orders_set_relanced( $or['id'],false );

					if( ord_installment_notify_order( $or['id'] ) ){
						$no_error = sprintf(_('Le client de la commande %s a été relancé.'), $or['piece']);
					}else{
						$error = _('La relance n\'a pas pu être envoyée pour une raison inconnue.');
					}
				}else{
					// la relance est envoyée en différé
					if( $or['relanced']===true ){
						$no_error = _('Une relance différée a déjà été prise en compte pour cette commande.');
					}else{
						if( ord_orders_set_relanced( $or['id'],true ) ){
							$no_error = sprintf(_('Une relance sera envoyée pour la commande %s dès que le ou les article(s) en complément seront synchronisés.'), $or['piece']);
						}else{
							$error = sprintf(_('La relance différée n\'a pas pu être programmée pour la commande %s.'), $or['piece']);
						}
					}
				}
			}else{
				$error = _('Commande invalide.');
			}
		}
	}

	// Charge la commande (on peut se permettre de charger les commandes masquées à ce stade).
	$ord = ria_mysql_fetch_array(
		ord_orders_get_masked($_GET['ord'], true)
	);

	$is_invoice = $ord['state_id'] == _STATE_INVOICE;
	$is_sync = $ord['piece'] !== '';
	$can_modify_order = !$is_sync && in_array($ord['state_id'], array(_STATE_BASKET, _STATE_BASKET_SAVE, _STATE_WAIT_VALIDATION, _STATE_PAY_WAIT_CONFIRM, _STATE_DEVIS, _STATE_BASKET_PAY_CB));

	// Sauvegarde les champs avancés "Chance de signature" et "Délai de signature".
	if( isset($_POST['sign_date']) && empty($ord['ord_piece']) ){
		$date = null;

		switch( $_POST['sign_date'] ){
			case 0:
				$date = date('Y-m-d', strtotime('1 week'));
				break;
			case 1:
				$date = date('Y-m-d', strtotime('2 weeks'));
				break;
			case 2:
				$date = date('Y-m-d', strtotime('1 month'));
				break;
			case 3:
				$date = date('Y-m-d', strtotime('2 months'));
				break;
			case 4:
				$date = date('Y-m-d', strtotime('6 months'));
				break;
			case 5:
				$date = date('Y-m-d', strtotime('1 year'));
				break;
		}

		fld_object_values_set($ord['id'], _FLD_ORD_PIPE_SIGN_DATE, $date.' 00:00:00');
	}

	if( isset($_POST['sign_rate']) && empty($ord['ord_piece']) ){
		fld_object_values_set($ord['id'], _FLD_ORD_SIGN_RATE, $_POST['sign_rate']);
	}

    $ord_has_child = false;
    $ord_is_empty = true;

    $r_childs = ord_orders_get_with_adresses( 0, 0, 0, '', false, false, false, false, null, false, false, false, false, 0, 0, false, false, $ord["id"] );
    if($r_childs && ria_mysql_num_rows($r_childs)){
        $ord_has_child =true;
    }

	// Charge la liste des produits de la commande
    $r_products = ord_products_get( $ord['id'], array('line_pos'=>'asc') );
    if( $r_products && ria_mysql_num_rows($r_products) ){
    	while( $prd_try = ria_mysql_fetch_assoc($r_products) ){
    		if( !$prd_try['ord_child_id'] ){
    			$ord_is_empty = false;
    			break;
    		}
    	}
    }

	// Retire la remise fidélité d'une commande
	if( !$ctr_validation && isset($_POST['detach-reward'], $_GET['ord']) ){
		if( !fld_object_values_set($_GET['ord'], _FLD_ORD_PTS, '') ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression des points de fidélité de cette commande.")."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème.");
		} elseif( !stats_rewards_del($ord['user'], CLS_ORDER, $_GET['ord'], 0) ){
			$error = _("Une erreur inattendue s'est produite lors de la suppression des points de fidélité de cette commande.")."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème.");
		} elseif( isset($config['rwd_prd_ref']) && trim($config['rwd_prd_ref']) ){
			if( !ord_products_del_ref( $_GET['ord'], $config['rwd_prd_ref']) ){
				$error = _("Une erreur inattendue s'est produite lors de la suppression des points de fidélité de cette commande.")."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème.");
			}
		}

		if( !isset($error) ){
			header('Location: /admin/orders/order.php?state='.$_GET['state'].'&ord='.$_GET['ord']);
			exit;
		}
	}

	// Attache des points de fidélité
	if( !$ctr_validation && isset($_POST['pts-reward'], $_POST['attach-reward']) ){
		$prf = gu_users_get_prf( $ord['user'] );
		$balance = gu_users_get_rewards_balance( $ord['user'] );

		if( !is_numeric($_POST['pts-reward']) || $_POST['pts-reward']<=0 ){
			$error = _('Veuillez renseigner un nombre de points supérieur à 0.');
		} elseif( !is_numeric($prf) || $prf<=0 ){
			$error = _("Une erreur inattendue s'est produite lors de la récupération du profil client de cette commande.")."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème.");
		} elseif( $balance<=0 ) {
			$error = _("Le compte client de cette commande ne dispose d'aucun point de fidélité.");
		} elseif( $balance<$_POST['pts-reward'] ){
			$error = sprintf(_('Le compte client de cette commande ne dispose que de %d point%s de fidélité.'), $balance, ($balance>1 ? 's' : ''));
		} elseif( isset($config['rwd_prd_ref']) && trim($config['rwd_prd_ref']) ) {

			$rp = prd_products_get_simple( 0, $config['rwd_prd_ref'], false, 0, false, false, true );
			if( $rp && ria_mysql_num_rows($rp) ){
				// supprime le produit de points de fidélité afin de ne pas les cumuler
				ord_products_del_ref( $ord['id'], $config['rwd_prd_ref'] );

				$p = ria_mysql_fetch_array( $rp );
				$reduc = rwd_rewards_using_points_simulated( $ord['user'], $_POST['pts-reward'] );

				// limite la réduction au total de la commande hors frais de port
				$total = ord_orders_get_total_without_port( $ord['id'], true );
				if( $reduc['reduc']>$total ){
					$_REQUEST['pts-used'] = floor( $reduc['points'] * $total / $reduc['reduc'] );
					$reduc['reduc'] = $reduc['reduc'] * $_REQUEST['pts-used'] / $reduc['points'];
				}

				$price = $reduc['reduc'] / $p['tva_rate'] * -1;
				if( !ord_products_add_free( $_GET['ord'], $p['ref'], $p['name'], $price, 1, null, '', $p['tva_rate']) ){
					$error = _("Une erreur inattendue s'est produite lors de l'ajout des points de fidélité de cette commande.")."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème.");
				}
			}

		}

		if( !isset($error) ){
			if( !fld_object_values_set($_GET['ord'], _FLD_ORD_PTS, $_POST['pts-reward']) ) {
				$error = _("Une erreur inattendue s'est produite lors de l'ajout des points de fidélité de cette commande.")."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème.");
			} elseif( !stats_rewards_add($ord['user'], $prf, 0, true, 'Utilisation de points de fidélité', CLS_ORDER, $_GET['ord'], $_POST['pts-reward']) ){
				$error = _("Une erreur inattendue s'est produite lors de l'ajout des points de fidélité de cette commande.")."\n"._("Veuillez réessayer ou prendre contact pour signaler le problème.");
			}
		}

		if( !isset($error) ){
			header('Location: /admin/orders/order.php?state='.$_GET['state'].'&ord='.$_GET['ord']);
			exit;
		} else {
			fld_object_values_set( $_GET['ord'], _FLD_ORD_PTS, '' );
			stats_rewards_del( $ord['user'], CLS_ORDER, $_GET['ord'], 0 );
			ord_products_del_ref( $_GET['ord'], $config['rwd_prd_ref'] );
		}
	}

	// Sauvegarde d'un code postal pour une commande place de marché
	if( $ctr_validation && isset($_POST['save-dlv-zipcode'], $_POST['dlv-zipcode']) ){
		$_POST['dlv-zipcode'] = trim($_POST['dlv-zipcode']);
		if( $_POST['dlv-zipcode'] == '' ){
			$error = _("Le code postal saisi n'est pas valide.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour signaler le problème.");
		}elseif( strlen($_POST['dlv-zipcode']) > 9 ){
			$error = _("Le code postal saisi est trop long.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour signaler le problème.");
		}elseif( !gu_adresses_set_zipcode( $ord['user'], $ord['dlv_id'], $_POST['dlv-zipcode'] ) ){
			$error = _("Une erreur inattendue est survenue pendant la sauvegarde du code postal.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour signaler le problème.");
		}elseif( !ord_orders_unmask( $_GET['ord'] ) ){
			$error = _("Une erreur inattendue est survenue pendant l'étape de validation de la commande, faisant suite à la saisie du code postal.")."\n"._("Veuillez réessayer ou prendre contact avec nous pour signaler le problème.");
		}else{
			// le code postal a été mis à jour, la commande peut être validée
			header('Location: order.php?ord='.$_GET['ord'].'&ctr-zip-success=OK');
			exit;
		}
	}

	// Le paramètre ctr-zip-success permet de spécifier que l'enregistrement du code postal s'est bien déroulé
	if( !isset($no_error) && isset($_GET['ctr-zip-success']) ){
		$no_error = _("Le code postal a bien été enregistré, la commande est désormais en attente de traitement.");
	}

	$num = $ord['piece']!='' ? $ord['piece'] : str_pad( $ord['id'], 8, '0', STR_PAD_LEFT );
	$state = '';
	if( isset($_GET['state']) && $_GET['state']>0 ){
		$rst = ord_states_get( $_GET['state'] );
		if( $rst && ria_mysql_num_rows($rst) )
			$state = ria_mysql_result( $rst, 0, 'name_plural' );
	}elseif( isset($_GET['state']) ){
		$state = _('Toutes les commandes');
	}

	$seller = false;
	if($ord['seller_id']){
		$r_seller = gu_users_get(0, '', '', PRF_SELLER, '', 0, '', false, false, $ord['seller_id']);
		if ($r_seller && ria_mysql_num_rows($r_seller)) {
			$seller = ria_mysql_fetch_assoc($r_seller);
		}
	}

	// Déplacement vers le haut
	if( isset($_GET['up']) ){
		ord_products_move_up( $_GET['ord'], $_GET['up'], $_GET['line'] );
		header('Location: order.php?ord='.$_GET['ord']);
		exit;
	}

	// Déplacement vers le bas
	if( isset($_GET['dw']) ){
		ord_products_move_down( $_GET['ord'], $_GET['dw'], $_GET['line'] );
		header('Location: order.php?ord='.$_GET['ord']);
		exit;
	}

	// Défini le titre de la page
	define('ADMIN_PAGE_TITLE', sprintf(_('Commande %s%s - Commandes'), $num, ($state!='' ? ' - '.$state : '' )));
	require_once('admin/skin/header.inc.php');

?>
<h2><?php print ( $_GET['state']==1 ? view_cart_is_sync($ord) : view_ord_is_sync($ord)); printf( ' '._('Pièce n°%d du %s'), str_pad( $ord['id'], 8, '0', STR_PAD_LEFT ), ria_date_format($ord['date'])); ?></h2>

<?php
	if( isset($error) ){
		print '<div class="error">'.htmlspecialchars($error).'</div>';
	}
	if( isset($no_error) ){
		print '<div class="error-success">'.htmlspecialchars($no_error).'</div>';
	}

	$usr = array();
	$prc = array('money_code' => '');
	$rusr = gu_users_get($ord['user']);
	if( $rusr && ria_mysql_num_rows($rusr) ){
		$usr = ria_mysql_fetch_assoc($rusr);

		$prc = ria_mysql_fetch_assoc(
			prd_prices_categories_get($usr['prc_id'])
		);
	}

	// Récupération de l'acompte en cours s'il y'en a un
	$ritm = ord_installments_get(0, 1, $_GET['ord'], true);
	$solde = 0;
	if( ria_mysql_num_rows($ritm) ){
		$itm = ria_mysql_fetch_assoc($ritm);
		$solde = $ord['total_ttc'] - $itm['total'];
	}
	// Récupération de tous les acomptes
	$ritms = ord_installments_get(0, 1, $_GET['ord']);

	// gestion memcached, test "isset" obligatoire
	$allow_cancel = isset($config['allow_orders_cancellation']) && $config['allow_orders_cancellation'];

if( $ctr_validation ){
	?><form action="order.php?state=<?php print $_GET['state']; ?>&amp;ctr=<?php print $_GET['ctr']; ?>&amp;token=<?php print $_GET['token']; ?>" method="post"><?php
}else{
	?><form action="order.php?state=<?php print $_GET['state']; ?>&amp;ord=<?php print $_GET['ord']; ?>" method="post"><?php
}
?>
<table id="table-une-commande">
<thead>
	<tr>
		<th colspan="2"><?php print _('Propriétés générales')?></th>
	</tr>
</thead>
<tbody>
	<?php
		if( !trim($ord['piece']) ){
			// Affichage de la référence en mode édition si la commande n'est pas synchronisé
			print '
				<tr>
					<td id="td-order-ref">
						<div class="bloc-ghost"></div>
						'._('Référence :').'
					</td>
					<td class="large">
						<input type="text" name="ord-reference" class="ord-mini-action" id="ord-reference" value="'.( trim($ord['ref']) ? htmlspecialchars($ord['ref']) : '' ).'"/>
					</td>
				</tr>
			';
		}elseif( trim($ord['ref']) != '' ){
			// Affichage de la référence en mode lecture seule si la commande est synchronisé et que la référence est saisie
			print '
				<tr>
					<td id="td-order-ref">
						<div class="bloc-ghost"></div>
						'._('Référence :').'
					</td>
					<td class="large">'.htmlspecialchars($ord['ref']).'</td>
				</tr>
			';
		}

		if( $config['sync_type']=='sage' ){
			if( trim($ord['piece']) ){ ?>
		<tr>
			<td id="td-order-ref">
				<div class="bloc-ghost"></div>
				<?php print _('Bon de commande ERP :'); ?>
			</td>
			<td class="large"><?php print htmlspecialchars($ord['piece']); ?></td>
		</tr>
		<?php }

			// Affichage des PL, seulement s'il y en a
			$ar_pl = array();
			if( $pl = ord_orders_pl_get($ord['id']) ){
				while( $p = ria_mysql_fetch_array($pl) ){
					$ar_pl[] = trim($p['piece']);
				}
			}
			if( sizeof($ar_pl) ){
				print '<tr><td>'._('Préparation(s) de livraison Gescom').' :</td><td>'.implode( ', ', $ar_pl ).'</td></tr>';
			}

			// Affichage des BL, seulement s'il y en a
			if ($ord['state_id'] > _STATE_IN_PROCESS) {
				$ar_bl = array();
				if( $bl = ord_orders_bl_get($ord['id']) ){
					while( $p = ria_mysql_fetch_array($bl) ){
						$ar_bl[] = trim($p['piece']);
					}
				}
				if( sizeof($ar_bl) ){
					print '<tr><td>'._('Bon(s) de livraison Gescom').' :</td><td>'.implode( ', ', $ar_bl ).'</td></tr>';
				}
			}

			// Affichage des Factures, seulement s'il y en a
			$ar_invoices = array();
			$invoices = ord_orders_invoices_get($ord['id']);
			if ($invoices && ria_mysql_num_rows($invoices)) {
				print '<tr><td>'._('Facture(s) Gescom'). ' :</td><td>';
				$can_dl_invoice = gu_user_is_authorized('_RGH_ADMIN_ORDER_DL_INVOICE')
					&& in_array($ord['state_id'], array(_STATE_INVOICE, _STATE_SUPP_PARTIEL_INV, _STATE_BL_EXP));
				while ($inv = ria_mysql_fetch_array($invoices)) {
					if ($can_dl_invoice) {
						print '<a data-type="invoice" data-ord="'.intval($ord['id']).'" data-inv="' . intval($inv['id']) . '" class="download-invoice-pdf print-pdf-btn">';
					}

					print ('' != trim($inv['piece']) ? trim($inv['piece']) : trim($inv['id']));

					if ($can_dl_invoice) {
						print '</a>';
					}
				}
				print '</td></tr>';
			}
		}
		if( tnt_tenants_have_websites() ){ ?>
		<tr>
			<td><?php print _('Source :'); ?></td><td><?php print wst_websites_get_name($ord['wst_id']); ?></td>
		</tr>
	<?php }
		if( gu_user_is_authorized('_RGH_ADMIN_TOOL_REWARD') ){ ?>
		<tr>
			<td><?php print _('Points de fidélité :'); ?></td>
			<td><?php
				$rpts = stats_rewards_get( 0, 0, 0, CLS_ORDER, $ord['id'], false, false, 0, false, 0, true );
				$balance = gu_users_get_rewards_balance( $ord['user'] );

				if( $rpts && ria_mysql_num_rows($rpts) ){
					$pts = ria_mysql_fetch_array( $rpts );
					print '	<input disabled="disabled" type="text" class="text ord-mini-action ord-action-reward" name="pts-reward" maxlength="16" value="'.($pts['pts']*-1).'" />
							<input class="auto_input" type="submit" name="detach-reward" value="'._('Retirer').'" '.( $ctr_validation ? 'disabled="disabled"' : '' ).' />';
				} elseif( $balance<=0 ){
					print _('Le compte client de cette commande ne dispose actuellement d\'aucun point de fidélité.');
				} else {
					print '	<input type="text" class="text ord-mini-action ord-action-reward" name="pts-reward" maxlength="16" value="'.( isset($_POST['pts-reward']) ? $_POST['pts-reward'] : '' ).'" '.( $ctr_validation ? 'disabled="disabled"' : '' ).' />
							<input class="auto_input" type="submit" name="attach-reward" value="'._('Utiliser').'" '.( $ctr_validation ? 'disabled="disabled"' : '' ).' />
							<sub>'.sprintf(_('Le compte client de cette commande dispose actuellement de %d point%s de fidélité.'),$balance, ($balance>0 ? 's' : '') ).'</sub>';
				}
			?></td>
		</tr>
	<?php }
		if( $seller ){
		?>
		<tr>
			<td><?php print _('Représentant :'); ?></td>
			<td><?php
				print '<a href="/admin/customers/edit.php?usr='.$seller['id'].'">';
					print view_usr_is_sync( $seller ).'&nbsp;'.htmlspecialchars( $seller['adr_firstname'].' '.$seller['adr_lastname'].' '.$seller['society'] );
				print '</a>';
			?></td>
		</tr>
	<?php } ?>
	<?php
		$sign_rate_field = fld_object_values_get($ord['id'], _FLD_ORD_SIGN_RATE, '', false, true);
		$sign_date_field = fld_object_values_get($ord['id'], _FLD_ORD_PIPE_SIGN_DATE, '', false, true);

		$sign_rate_options = array(
			'0% - Devis perdu',
			'10% - Opportunité identifiée',
			'20% - 1er RDV effectué',
			'30% - Offre préparée',
			'40% - Offre acceptée',
			'50% - Signature à planifier',
			'60% - Vérification',
			'70% - Discussion sur le prix',
			'80% - Accord oral',
			'90% - Bon pour accord',
			'100% - Devis gagné',
		);

		$sign_date_options = array(
			array('date' => date('Y-m-d', strtotime('1 week')), 'label' => '1 semaine'),
			array('date' => date('Y-m-d', strtotime('2 weeks')), 'label' => '2 semaines'),
			array('date' => date('Y-m-d', strtotime('1 month')), 'label' => '1 mois'),
			array('date' => date('Y-m-d', strtotime('3 months')), 'label' => '3 mois'),
			array('date' => date('Y-m-d', strtotime('6 months')), 'label' => '6 mois'),
			array('date' => date('Y-m-d', strtotime('1 year')), 'label' => '1 an'),
			array('date' => $sign_date_field, 'label' => 'Délai dépassé'),
		);

		if( strtotime($sign_date_field) > time() ){
			array_pop($sign_date_options);
		}

		$sign_dates = array_map(function ($date) {
			return $date['date'];
		}, $sign_date_options);
	?>
	<?php if( $ord['state_id'] == _STATE_DEVIS ){ ?>
		<tr>
			<th colspan="2"><?php print _('Pipeline commercial'); ?></th>
		</tr>
		<tr>
			<td>
				<label for="select-sign-rate"><?php print _('Chance de signature'); ?></label>
			</td>
			<td>
				<div class="sign-rate-block">
					<select id="select-sign-rate">
						<?php foreach( $sign_rate_options as $value => $option ){ ?>
							<option value="<?php print $value; ?>" <?php print $sign_rate_field == $value ? 'selected' : ''; ?>><?php print $option; ?></option>
						<?php } ?>
					</select>
					<input type="range" id="slider-sign-rate" min="0" max="<?php print count($sign_rate_options) - 1; ?>" <?php print !empty($ord['piece']) ? 'disabled' : ''; ?>>
					<p id="text-sign-rate"></p>
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<label for="sign-date-block"><?php print _('Délai de signature'); ?></label>
			</td>
			<td>
				<div class="sign-date-block">
					<select id="select-sign-date" <?php print !empty($ord['piece']) ? 'disabled' : ''; ?>>
						<?php foreach( $sign_date_options as $i => $option ){ ?>
							<option value="<?php print $i; ?>" <?php print select_default_date_option(($sign_date_field ?: null), $sign_dates, $option['date']); ?>><?php print $option['label']; ?></option>
						<?php } ?>
					</select>
					<input type="range" id="slider-sign-date" min="0" max="<?php print count($sign_date_options) - 1; ?>">
					<p id="text-sign-date"></p>
				</div>
			</td>
		</tr>
	<?php } ?>
	<?php
		$date_start = $ord['date_en'];
		$date_end = false;
		$r_next_order = ord_orders_get_simple(array(), array('start'=>$ord['date_en']), array('usr_id' => $ord['user']), array('sort' => array('date' => 'asc')));
		if( $r_next_order && ria_mysql_num_rows($r_next_order) >= 2){
			//Prend la deuxieme commande retourné car la première correspond à la commande en cour de visualisation
			$next_order = ria_mysql_fetch_assoc($r_next_order);
			$next_order = ria_mysql_fetch_assoc($r_next_order);
			$date_end = $next_order['date'];
		}
		$messages = array();
		if( $ord['user'] ){
			$r_message_usr = messages_get($ord['user'], '', 0, 0, 0, false, true, false, 0, 0, $date_start, $date_end);
			while( $message_usr = ria_mysql_fetch_assoc($r_message_usr) ){
				if( $message_usr['ord_id'] == null || $message_usr['ord_id'] == $ord['id']){
					$messages[$message_usr['id']] = $message_usr['id'];
				}
			}
		}
		$r_message_ord = messages_get(0 , '', 0, 0, 0, false, false, false, 0, 0, false, false, null, null, false, false, null, false, '', false, false, false, false, false, $ord['id'] );

		while( $message_ord = ria_mysql_fetch_assoc($r_message_ord) ){
			$messages[$message_ord['id']] = $message_ord['id'];
		}

		$nb_msg = sizeof($messages);

		if( $nb_msg == 1 ){
			print '<tr><td colspan="2" class="multi-colspan"><div class="notice notice-msg">'._('Un message a été envoyé par ce client après le passage de la commande.').' <a href="/admin/customers/edit.php?usr='.$ord['user'].'&tab=contacts&start='.$date_start.'&end='.$date_end.'&ord='.$ord['id'].'">'._('Voir le message').'</a></div></td></tr>';
		}elseif( $nb_msg >=1 ){
			print '<tr><td colspan="2" class="multi-colspan"><div class="notice notice-msg">'._('Plusieurs messages ont été envoyés par ce client après le passage de la commande.').' <a href="/admin/customers/edit.php?usr='.$ord['user'].'&tab=contacts&start='.$date_start.'&end='.$date_end.'&ord='.$ord['id'].'">'.sprintf(_('Voir les %d messages'), $nb_msg).'</a></div></td></tr>';
		}

		$exclude_codes = array();
		$promotions = pmt_codes_get(null, null, true);
		$order_promotions = ord_orders_promotions_get($ord['id']); ?>
	<?php if( ($can_modify_order && ria_mysql_num_rows($promotions)) || !empty($order_promotions) ){ ?>
		<tr>
			<th colspan="2"><?php print _('Codes promotion')?></th>
		</tr>
	<?php } ?>
	<?php if( !empty($order_promotions) ){ ?>
		<tr>
			<td><?php print _('Codes appliqués :'); ?></td>
			<td>
				<?php foreach( $order_promotions as $promotion ){
					$rpmt = pmt_codes_get($promotion['pmt_id']);
					if( ria_mysql_num_rows($rpmt) ){
						$pmt = ria_mysql_fetch_assoc($rpmt);
						$exclude_codes[] = $pmt['code'];
				?>
						<p>
							<a href="/admin/promotions/codes/edit.php?pmt=<?php print $pmt['id']; ?>" target="_blank"><?php print htmlspecialchars((trim($pmt['name']) ? $pmt['name'].' - ' : '' ).$pmt['code']); ?></a>
							<?php if( $can_modify_order ){ ?>
								<input type="submit" class="auto_input" name="detach[<?php print $pmt['id']; ?>]" value="<?php print _('Détacher'); ?>" <?php print $ctr_validation ? 'disabled' : ''; ?>>
							<?php } ?>
						</p>
					<?php }
				} ?>
			</td>
		</tr>
	<?php } if( ria_mysql_num_rows($promotions) && $can_modify_order ){ ?>
	<tr>
		<td>
			<label for="pmt-code"><?php print _('Code promotion :'); ?></label>
		</td>
		<td>
			<p>
				<select class="text" id="pmt-code" name="pmt-code" <?php print $ctr_validation ? 'disabled' : ''; ?>>
					<option value="">--- <?php print _('Sélectionnez un code promotion'); ?> ---</option>
					<?php
						while( $row = ria_mysql_fetch_assoc($promotions) ){
							if( !is_null($row['code']) && !in_array($row['code'], $exclude_codes) ){
								print '<option value="'.$row['code'].'">'.htmlspecialchars((trim($row['name']) ? $row['name'].' - ' : '').$row['code']).'</option>' ;
							}
						}
					?>
				</select>
				<?php if( !$ctr_validation ){ ?>
					<input class="auto_input" type="submit" name="attach" id="attach" value="<?php print _('Attacher')?>" title="<?php print _('Attacher le code promotion')?>" />
				<?php } ?>
			</p>
		</td>
	</tr>
	<?php }

		// Affiche des informations sur l'origine de la commande
		$stats = stats_origins_get( $ord['id'], CLS_ORDER );
		if( $stats && ria_mysql_num_rows($stats) ){
			$stat = ria_mysql_fetch_array( $stats );
			$html = view_source_origin($stat, 'table');
			if( trim($html) != '' ){
				print '<tr><th colspan="2">'._('Origine de la commande').'</th></tr>';
				print $html;
			}
		}

		// Le bloc Règlement n'apparaîtra que s'il contient des informations à afficher
		$payment_name = ord_payment_types_get_name($ord['pay_id']);

		$nb_couchdb_payments = ord_payment_couchdb_get_count($_GET['ord']);
		if( $payment_name || $ord['comments'] || $ord['card_id'] || $nb_couchdb_payments ){
	?>
	<tr><th colspan="2"><?php print _('Règlement')?></th></tr>
	<?php }
		if( $payment_name || $nb_couchdb_payments ){ ?>
	<tr>
		<td><?php print _('Mode de règlement :'); ?></td>
		<td><?php print $payment_name ? htmlspecialchars( $payment_name ) : ''; ?></td>
	</tr>
	<tr>
		<td></td>
		<td><a href="" id="payment_attempts_link"><?php print _('Afficher l\'historique des paiements'); ?></a></td>
	</tr>
	<?php }
		if( $ord['comments'] ){ ?>
		<tr><th colspan="2"><?php print _('Commentaire')?></th></tr>
		<tr><td colspan="2"><?php print nl2br($ord['comments']); ?></td></tr>
	<?php }

		$is_deadline = ord_installments_is_deadline($ord['id']);
		$is_expired_or_missing = true;

		if( ria_mysql_num_rows($ritms) ){
			$current_timestamp = time();
			if( $is_deadline ){
				$html_deadline = '';
				$has_one_installment_not_expired = false;
				while( $item = ria_mysql_fetch_assoc($ritms) ){
					$expired_timestamp = strtotime($item['expire']);

					if( $current_timestamp < $expired_timestamp ){
						$has_one_installment_not_expired = true;

					}
					$html_deadline .= '
						<tr>
							<td>'.date('d/m/Y', $expired_timestamp).'</td>
							<td class="align-right">'.ria_number_format($item['rest'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</td>
						</tr>
					';
				}
				if( $has_one_installment_not_expired ){
					$is_expired_or_missing = false;
				}
				?>
				<tr><th colspan="2"><?php print _('Échéancier');?></th></tr>
				<tr>
					<td colspan="2" class="multi-colspan">
						<?php print view_admin_deposit_form($ord, $is_expired_or_missing, true, (isset($error_deposit) && $error_deposit));?>
						<table id="table-date-montant">
							<thead class="thead-none">
								<tr>
									<th><?php print _('Date');?></th>
									<th><?php print _('Montant');?></th>
								</tr>
							</thead>
							<tbody>
								<?php print $html_deadline;?>
							</tbody>
						</table>
					</td>
				</tr>
		<?php }else{
			$html_deposit = '';
			$has_one_installment_not_expired = false;
			while( $itm = ria_mysql_fetch_assoc( $ritms ) ){
				$expired_timestamp = strtotime($itm['expire']);

				if( $current_timestamp < $expired_timestamp ){
					$has_one_installment_not_expired = true;

				}
				$dpt_class = $expired_timestamp > $current_timestamp ? '' : ' class="show_amount_diff"';
				$itm_rest = $ord['total_ttc'] - $itm['total'];
				$html_deposit .= '
					<tr>
						<td'.$dpt_class.'>'.$itm['transaction'].'</td>
						<td'.$dpt_class.'>'.$itm['form_date_created'].'</td>
						<td'.$dpt_class.'>'.$itm['form_date_expired'].'</td>
						<td'.$dpt_class.'>'.ria_number_format($itm_rest, NumberFormatter::CURRENCY, 2, $prc['money_code']).'</td>
						<td'.$dpt_class.'>'.ria_number_format($itm['total'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</td>
					</tr>
				';
			}

			if( $has_one_installment_not_expired ){
				$is_expired_or_missing = false;
			}
		?>
			<tr>
				<th colspan="2"><?php print _('Acomptes')?></th>
			</tr>
			<tr>
				<td colspan="2" class="multi-colspan">
					<?php print view_admin_deposit_form($ord, $is_expired_or_missing, false, (isset($error_deposit) && $error_deposit));?>
					<table id="table-acompte">
						<thead>
							<tr>
								<th class="col150px"><?php print _('Transaction')?></th>
								<th><?php print _('Date de l\'acompte')?></th>
								<th><?php print _('Date d\'expiration')?></th>
								<th class="col150px"><?php print _('Montant restant')?></th>
								<th class="col150px"><?php print _('Montant total')?></th>
							</tr>
						</thead>
						<tbody>
							<?php print $html_deposit;?>
						</tbody>
						<?php if( !$is_expired_or_missing && isset($solde) ){ ?>
							<tfoot>
								<tr>
									<th colspan="4" class="align-right">
										<?php print _('Solde actuel:'); ?>
									</th>
									<th class="align-right">
										<?php print ria_number_format($solde, NumberFormatter::CURRENCY, 2, $prc['money_code']);?>
									</th>
								</tr>
								<?php if( is_numeric($ord['state_id']) && $ord['state_id'] != _STATE_INVOICE ){ ?>
									<tr>
										<th colspan="5" class="align-right">
											<input class="auto_input" title="<?php print _('Notifier le client pour un paiement complémentaire.')?>" type="submit" name="relaunch" value="<?php print _('Relancer')?>" <?php print $ctr_validation ? 'disabled="disabled"' : ''; ?> />
										</th>
									</tr>
									<tr>
										<th colspan="5" class="align-right">
											<?php print _('Seul l\'acompte en cours de validité est pris en compte lors de la relance.')?>
										</th>
									</tr>
								<?php } ?>
							</tfoot>
						<?php } ?>
					</table>
				</td>
			</tr>
		<?php }
		}elseif( gu_user_is_authorized('_RGH_ADMIN_ORDER_ADD_DEPOSIT') && $is_expired_or_missing && is_numeric($ord['state_id']) && $ord['state_id'] != _STATE_INVOICE ){ ?>
			<tr>
				<th colspan="2"><?php print _('Acomptes')?></th>
			</tr>
			<tr>
				<td colspan="2" class="multi-colspan">
					<?php print view_admin_deposit_form($ord, $is_expired_or_missing, false, (isset($error_deposit) && $error_deposit));?>
				</td>
			</tr>
		<?php } ?>

	<tr id="ord-adresses-row">
		<td colspan="2" class="multi-colspan" id="adresses">
			<?php include('./order/update-user.php'); ?>
		</td>
	</tr>

	<?php
		include("./order/delivery.php");
	?>
	<tr>
		<th colspan="2" class="multi-colspan"><?php print _('Articles')?></th>
	</tr>
	<tr class="order-products-row" data-ord="<?php print htmlspecialchars($ord['id']) ?>">
		<?php include('./order/products.php'); ?>
	</tr>
	<?php if( ria_mysql_num_rows(ord_orders_signature_get($ord['id']))>0 ){
			$script_js = "";
			$sign_row = ria_mysql_fetch_assoc(ord_orders_signature_get($ord['id']));
			$sign = $sign_row['signature'];
		?>
	<script>
		$('document').ready(function(){
			var canvas =  $('#ord_signature').get(0);
			var path = '<?php print $sign; ?>';

			var ctx = canvas.getContext('2d');
			path = path.split(',');
			ctx.beginPath();

			path.forEach( function(element, index) {

				if (element.indexOf("lineTo:") != -1) {
					var p = element.replace("lineTo:","").split(':');
					ctx.lineTo(p[0]/2,p[1]/2);

				}else if (element.indexOf("moveTo:") != -1) {

					var p = element.replace("moveTo:","").split(':');
					ctx.moveTo(p[0]/2,p[1]/2);

				}
			});

			ctx.stroke();
		});
	</script>
			<tr>
				<th colspan="2"><?php print _('Signature')?></th>
			</tr>
			<tr>
				<td colspan="2" class="multi-colspan"><?php print _('Bon pour accord par')?> <?php print $sign_row["lastname"] != NULL ? htmlspecialchars( $sign_row["lastname"] )." ": "";
					print $sign_row["firstname"] != NULL ? htmlspecialchars( $sign_row["firstname"] ) : "" ;
					print $sign_row["function"] != NULL ? ", (".htmlspecialchars( $sign_row["function"] ).")" : "" ;
					 ?>
				</td>
			</tr>
			<tr>
				<td class="align-center multi-colspan" colspan="2"><canvas id="ord_signature" width="800" height="850">
					<?php print _('Désolé, votre navigateur ne permet pas d\'afficher ce canvas HTML5.')?>
				</canvas></td>
			</tr>
	<?php
} ?>
</tbody>
<tfoot>
	<tr>
		<td colspan="2" class="multi-colspan">
			<div class="ord-prd-actions">
			<?php
				if( gu_user_is_authorized('_RGH_ADMIN_ORDER_PRD_ADD') ){
					if (trim($ord['piece']) == '') {
						print '<input type="button" class="del-ord-prd" name="del-ord-prd" data-ord="'.htmlspecialchars($ord['id']).'" value="'._('Supprimer la sélection').'" />';
					}
				} ?>
			</div>
			<div class="ord-state-actions float-right">
				<?php if( $ord['state_id']==_STATE_DEVIS && gu_user_is_authorized('_RGH_ADMIN_ORDER_DL_DEVIS') ){ ?>
					<input type="button" class="download-devis-pdf print-pdf-btn" name="generate-devis"
						data-type="devis"
						data-ord="<?php print $ord['id']; ?>"
						value="<?php print _('Imprimer le devis'); ?>"/>
				<?php } ?>
				<?php
					// active ou non la mise à jour de l'état d'une commande
					$actived = isset($config['sys_cheque_actived']) && $config['sys_cheque_actived'];
					$actived = $actived || $config['allow_orders_update_state'] || $allow_cancel || $allow_delivery;

					$states_array = array();
					if( isset($config['sys_cheque_actived']) && $config['sys_cheque_actived'] && in_array($ord['pay_id'], array(_PAY_CHEQUE, _PAY_VIREMENT, _PAY_CHEQUE_X)) ){
						if( $ord['state_id']==25 ){
							$states_array[] = 4;
						} else {
							$states_array[] = 25;
						}
					}

					if( !$config['allow_orders_update_state'] || $config['allow_orders_update_state'] && $config['orders_update_state_included'] ){
						// si annulation autorisée seulement, tous les états ne sont pas dans la liste
						$states_array = array_merge($states_array, $config['orders_update_state_included']);
						$states_array[] = 9;
						$states_array[] = 10;
						$states_array[] = $ord['state_id'];

						if( $allow_delivery_ldd){
							$is_sync_ldd = fld_object_values_get( $_GET['ord'], $config['fld_order_ldd'] );
							if (strtolower(trim($is_sync_ldd))==='oui' ) $states_array[] = 7;
						}
					}

					if ($allow_update_state_delivery && isset($ord) && in_array($ord['state_id'], $delivery_previous_states)) {
						if (isset($config['srv_port_store']) && $config['srv_port_store']) {
							$states_array[] = _STATE_BL_STORE;
						}
						$states_array = array_merge($states_array, $delivery_states);
					}

					if( in_array($ord['state_id'], array( _STATE_WAIT_PAY, _STATE_PAY_WAIT_CONFIRM )) && in_array($ord['pay_id'], array(_PAY_CB, _PAY_SOFINCO)) ){
						$states_array[] = _STATE_PAY_CONFIRM;
						$actived = true;
					}

					if( $ord['pay_id']==_PAY_PAYPAL && $ord['state_id']==_STATE_WAIT_PAY ){
						$states_array[] = _STATE_PAY_CONFIRM;
						$actived = true;
					}

					if( $config['tnt_id'] == 14 ){
						if( $ord['state_id'] == _STATE_BASKET && !in_array(_STATE_WAIT_PAY, $states_array) ){
							$states_array[] = _STATE_WAIT_PAY;
						}
						if( $ord['state_id'] == _STATE_WAIT_PAY && !in_array(_STATE_PAY_CONFIRM, $states_array) ){
							$states_array[] = _STATE_PAY_CONFIRM;
						}
					}

					$states_array[] = _STATE_WAIT_PAY;
					$states_array[] = _STATE_PAY_CONFIRM;
					if (isset($config['ord_products_devis']) && $config['ord_products_devis']){
						$states_array[] = _STATE_DEVIS;
					}

					if (!in_array($ord['state_id'], $states_array)) {
						$states_array[] = $ord['state_id'];
					}

					$states = ord_states_get( $states_array );
					if( $states && ria_mysql_num_rows($states) ){
				?>

				<label for="newstate"><?php print _('Etat :'); ?></label>
				<select name="newstate" id="newstate" <?php print !$actived || $ctr_validation ? 'disabled="disabled"' : ''; ?>>
				<?php
					$print_need_sync = false;
					while( $r = ria_mysql_fetch_array($states) ){
						if( $ord['state_id'] == _STATE_INVOICE ){
							switch ($r['id']){
								case _STATE_SUPP_PARTIEL_INV:
								case _STATE_BASKET:
								case _STATE_BASKET_CANCEL:
								case _STATE_BASKET_SAVE:
								case _STATE_BASKET_PAY_CB:
								case _STATE_CANCEL_MERCHAND:
								case _STATE_CANCEL_USER:
								case _STATE_WAIT_PAY:
								case _STATE_PAY_CONFIRM:
								case _STATE_DEVIS:
									continue 2;
									break;
							}
						}


						if( $r['id']==$ord['state_id'] ){
							if ($r['id'] == _STATE_DEVIS){
								if ($ord['nsync'] == 0 && !$ord['piece']){
									print '	<option id="ord_status_devis" value="'.$r['id'].'">'.htmlspecialchars( _($r['name']) ).'</option>
											<option id="ord_status_devis_in_progress" value="'.$r['id'].'" selected="selected">'._('Devis en cours').'</option>';
									$print_need_sync = true;
								} else {
									print '<option value="'.$r['id'].'" selected="selected">'.htmlspecialchars( _($r['name']) ).'</option>';
								}
							} else {
								print '<option value="'.$r['id'].'" selected="selected">'.htmlspecialchars( _($r['name']) ).'</option>';
							}
						} else {
							if ($r['id'] == _STATE_DEVIS){
								if ($ord['state_id'] == _STATE_BASKET || $ord['state_id'] == _STATE_BASKET_SAVE){
									print '	<option id="ord_status_devis" value="'.$r['id'].'">'.htmlspecialchars( _($r['name']) ).'</option>
											<option id="ord_status_devis_in_progress" value="'._STATE_DEVIS.'">'._('Devis en cours').'</option>';
									$print_need_sync = true;
								} else {
									print '<option value="'.$r['id'].'">'.htmlspecialchars( _($r['name']) ).'</option>';
								}
							} else {
								print '	<option value="'.$r['id'].'">'.htmlspecialchars( _($r['name']) ).'</option>';
							}
						}

					}
				?>
				</select>
				<?php
					if( $print_need_sync ){
						print '<input type="hidden" name="need_sync" value="0" />';
					}

					if( $actived && !$ctr_validation ){ ?>
					<input type="submit" name="save-newstate" id="save-newstate" value="<?php print _('Modifier'); ?>" title="<?php print _('Mettre à jour le statut de cette commande'); ?>" />
				<?php }
					}
				?>
			</div>
		</td>
	</tr>
	<tr>
		<td colspan="2" class="multi-colspan">
			<div class="ord-actions" style="display: block;">
				<?php // Duplication
					if( gu_user_is_authorized('_RGH_ADMIN_ORDER_CREATE') ){
						print '	<input type="button" id="ord-duplicate" name="ord-duplicate" value="'._('Dupliquer').'" title="'._('Créer une copie de cette commande').'" />
								<input type="hidden" name="ord-id" value="'.$_GET['ord'].'" />';
					}
				?>
				<?php // Commande rapide - limitee au panier et devis
					if( gu_user_is_authorized('_RGH_ADMIN_ORDER_CREATE') && in_array($ord['state_id'], ['1', '28'], false)){
						print '	<input type="button" id="quick-order" name="quick-order" value="'._('Commande rapide').'" title="'._('Créer une commande rapide').'" />
								<input type="hidden" name="ord-id" value="'.$_GET['ord'].'" />';
					}
				?>
			</div>
			<div class="ord-history align-right">
				<?php
				print '<a class="button order-states-history-link" href="/admin/orders/popup-order-state-history.php?ord=' . intval($ord['id']) . '" title="'
					. _('Historique des états de la commande')
					. '">'
						. _('Voir l\'historique')
					. '</a>';
				?>
			</div>
		</td>
	</tr>
</tfoot>
</table>
<?php
$count = 1;
if($ord_has_child){
     while($ord_child = ria_mysql_fetch_assoc($r_childs)){
	?>
	<table id="table-adresse-livraison">
		<tbody>
			<tr>
				<th colspan="2" class="multi-colspan"><?php print _('Adresse de livraison')?></th>
			</tr>
			<?php
				if( is_numeric($ord_child['str_id']) ){
					$rstore = dlv_stores_get( $ord_child['str_id'], null, false, 0, 0, false, 0, '', 0, 0, false, false, false, array(), false, false, null );
					if( ria_mysql_num_rows($rstore) ){
						$store = ria_mysql_fetch_array($rstore);
			?>
			<tr>
				<td class="th-150"><?php print _('Adresse :'); ?></td>
				<td>
					<?php
						print htmlspecialchars( $store['name'] ).'<br />';
						print htmlspecialchars( $store['address1'] ).'<br />';
						if( $store['address2'] )
							print htmlspecialchars($store['address2']).'<br />';
						print htmlspecialchars( $store['zipcode'].' '.$store['city'] ).'<br />';
						print htmlspecialchars( $store['country'] ).'<br />';
					?>
				</td>
			</tr>
			<?php if( trim($store['phone']) ){ ?>
			<tr><td><?php print _('Téléphone :'); ?></td><td><?php print htmlspecialchars($store['phone']); ?></td></tr>
			<?php }
					if( trim($store['fax']) ){ ?>
			<tr><td><?php print _('Fax :'); ?></td><td><?php print htmlspecialchars($store['fax']); ?></td></tr>
			<?php 	}
				}
			}elseif( is_numeric($ord_child['rly_id']) ){
				require_once('relays.inc.php');
				$rrelay = dlv_relays_get_simple($ord_child['rly_id']);
				if( $rrelay && ria_mysql_num_rows($rrelay) ){
					$relay = ria_mysql_fetch_array($rrelay);
			?>
			<tr>
				<td><?php print _('Adresse :'); ?></td><td>
					<?php
						print htmlspecialchars( $relay['name'] ).'<br />';
						print htmlspecialchars( $relay['address1'] ).'<br />';
						if( $relay['address2'] )
							print htmlspecialchars($relay['address2']).'<br />';
						print htmlspecialchars( $relay['zipcode'].' '.$relay['city'] ).'<br />';
						print htmlspecialchars( $relay['country'] ).'<br />';
					?>
				</td>
			</tr>
			<tr><td><?php print _('Téléphone :')?></td><td><?php print htmlspecialchars($relay['phone']); ?></td></tr>
			<?php
				}
			}else{ ?>
			<tr>
				<td><?php print _('Adresse :'); ?></td><td>
					<?php
						if( $ord_child['dlv_society'] ) print htmlspecialchars($ord_child['dlv_society']).'<br />';

						if( $ord_child['dlv_title_name'] )  print $ord_child['dlv_title_name'].' ';
						if( $ord_child['dlv_lastname'] ) print htmlspecialchars($ord_child['dlv_lastname']);
						if( $ord_child['dlv_firstname'] ) print ', '.htmlspecialchars($ord_child['dlv_firstname']).'<br />';
						else print '<br />';

						if( $ord_child['dlv_address1'] ) print htmlspecialchars($ord_child['dlv_address1']).'<br />';
						if( $ord_child['dlv_address2'] ) print htmlspecialchars($ord_child['dlv_address2']).'<br />';
						if( $ord_child['dlv_address3'] ) print htmlspecialchars($ord_child['dlv_address3']).'<br />';
						if( !$ctr_validation ){
							print htmlspecialchars($ord['dlv_postal_code']).' '.htmlspecialchars($ord_child['dlv_city']).'<br />';
						}else{
							?>
							<input type="text" name="dlv-zipcode" maxlength="9" placeholder="<?php print _('Code postal...')?>" class="text ord-mini-action" value="<?php print isset($_POST['dlv-zipcode']) ? htmlspecialchars($_POST['dlv-zipcode']) : ''; ?>" />
							<input type="submit" name="save-dlv-zipcode" value="<?php print _('Enregistrer')?>" class="auto_input" title="<?php print _('Valider la commande après avoir saisi le code postal de livraison.')?>" />
							<br />
							<?php
							print htmlspecialchars($ord_child['dlv_city']).'<br />';
						}
						print htmlspecialchars($ord_child['dlv_country']);
					?>
				</td>
			</tr>
			<tr><td><?php print _('Téléphone :'); ?></td><td><?php print htmlspecialchars($ord_child['dlv_phone']); ?></td></tr>
			<tr><td><?php print _('Fax :'); ?></td><td><?php print htmlspecialchars($ord_child['dlv_fax']); ?></td></tr>
			<tr><td><?php print _('Portable :'); ?></td><td><?php print htmlspecialchars($ord_child['dlv_mobile']); ?></td></tr>
			<tr><td><?php print _('En journée :'); ?></td><td><?php print htmlspecialchars($ord_child['dlv_phone_work']); ?></td></tr>
			<?php } ?>
			<tr><th colspan="2"><?php print _('Livraison')?></th></tr>
			<tr>
				<?php if( $ord_child['srv_id'] && ( $rsrv = dlv_services_get( $ord_child['srv_id'] ) ) && ( $srv = ria_mysql_fetch_array($rsrv) ) ){ ?>
				<td><label><?php print _('Livraison assurée par :'); ?></label></td>
				<td>
					<a href="/admin/config/livraison/services/edit.php?srv=<?php print $ord['srv_id']; ?>" target="_blank"><?php print htmlspecialchars($srv['name']); ?></a>
				</td>
				<?php } ?>
			</tr>
			<tr>
				<td><?php print _('Colis :'); ?></td>
				<td>
				<?php

					$ar_colis = $colis = array();
					if( $bl = ord_orders_bl_get($ord['id']) ){
						while( $b = ria_mysql_fetch_array($bl) ){
							if( $products = ord_products_get($ord['id'],false, 0, '', null, false, 0, 0, -1, false, false,0, false, false, false,$ord_child['id'])){
								while( $prd = ria_mysql_fetch_array($products)){
									$result = ord_bl_colis_get($b['id']);
									if (is_array($result) && count($result)) {
										$colis = array_merge($colis, $result);
									}

								}
								if( $b['srv_id'] ){
									$srv = ria_mysql_fetch_array(dlv_services_get($b['srv_id']));
									if( $srv['url-colis'] || true ){
										foreach( $colis as $c ){
											$ar_colis[] = '<a href="'.$srv['url-colis'].$c.'" target="_blank">'.$c.'</a>';
										}

									}
								}
							}
						}
					}
					print implode(', ',$ar_colis);

				?>
				</td>
			</tr>
			<tr>
				<th colspan="2"><?php print _('Articles')?></th>
			</tr>
			<tr class="order-products-row" data-ord="<?php print htmlspecialchars($ord['id']) ?>" data-ord-child="<?php print htmlspecialchars($ord_child['id']) ?>">
				<?php include('./order/products.php') ?>
			</tr>
		</tbody>
		<tfoot>
		</tfoot>
	</table>
<?php
	$count++;
	}
}?>



<?php
// Affichage des retours
if( $config['returns_allowed'] ){
	if( ord_returns_keeped_products_get_count($ord['id']) && in_array($ord['state_id'], ord_returns_orders_states_allowed_get()) ){
	$returns = ord_returns_get( 0, 0, null, null, $ord['id']);
?>
<table  class="ncmd_cart checklist" id="table-retours">
	<caption><?php print _('Retours')?></caption>
	<thead>
		<tr>
			<th id="ord-return-id"><?php print _('Numéro')?></th>
			<th id="ord-date"><?php print _('Date du retour')?></th>
			<th id="ord-state"><?php print _('Etat/Statut')?></th>
			<th id="ord-products"><?php print _('Nombre de produits')?></th>
			<th id="ord-ht"><?php print _('Total H.T.')?></th>
			<th id="ord-ttc"><?php print _('Total T.T.C.')?></th>
		</tr>
	</thead>
	<tbody>
	<?php
		if( ria_mysql_num_rows($returns) ){
			while($r = ria_mysql_fetch_array($returns)){

				print '<tr>
							<td><a href="/admin/orders/returns/return.php?ret='.$r['id'].'">'.str_pad($r['id'], 8, 0, STR_PAD_LEFT).'</a></td>
							<td class="align-center"">'.substr(dateheureunparse($r['date']), 0, 17).'</td>
							<td class="align-center">'.ord_returns_states_get_name($r['states_id']).'</td>
							<td class="align-right">'.ria_number_format($r['products']).'</td>
							<td class="align-right">'.ria_number_format($r['total_ht'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</td>
							<td class="align-right">'.ria_number_format($r['total_ttc'], NumberFormatter::CURRENCY, 2, $prc['money_code']).'</td>
						</tr>';
			}
		}else{
			print '	<tr><td colspan="6" class="multi-colspan">'._('Il n\'y a pas de retour pour cette commande').'</td></tr>';
		}
	?>
	</tbody>
	<tfoot>
		<tr>
			<th colspan="6" class="align-left">
				<input type="submit" <?php print $ctr_validation ? 'disabled="disabled"' : ''; ?> id="ncmd_new_return" name="createReturn" value="<?php print _('Créer un retour')?>" />
			</th>
		</tr>
	</tfoot>
</table>

<?php
	}
}

	// Charge la liste des modèles de saisie
	$models = fld_models_get(0, 0, CLS_ORDER);
	$ord_models = fld_models_get(0, $_GET['ord'], CLS_ORDER);
	$fields = fld_fields_get(0, 0, -2, 0, 0, $_GET['ord'], null, array(), false, array(_FLD_ORD_SIGN_RATE, _FLD_ORD_PIPE_SIGN_DATE), null, CLS_ORDER);

	// Affiche le tableau "Champs personnalisés" uniquement s'il y a des modèles de saisie ou bien des champs orphelins
	if( ria_mysql_num_rows($models) || ria_mysql_num_rows($ord_models) || ria_mysql_num_rows($fields) ){
?>
<table id="table-champs-personnalises">
	<caption><?php print _('Champs personnalisés')?></caption>
	<tbody>
		<?php
		$ar_fld_exclude = array();

		if( ria_mysql_num_rows($ord_models) ){
			ria_mysql_data_seek($ord_models, 0);

			// Si un ou plusieurs modèles sont affectés à la commande, affiche leur liste ainsi que les champs qu'ils contiennent
			while( $m = ria_mysql_fetch_array($ord_models) ){
				print '<tr><th colspan="2" class="tfoot-grey">';
				if( !$ctr_validation ){
					print '<a href="order.php?ord='.$_GET['ord'].'&amp;delmdl='.$m['id'].'" style="float: right;">'._('Supprimer').'</a>';
				}
				print _('Modèle de saisie').' : '.htmlspecialchars($m['name']);
				print '</th></tr>';

				$ord_fields = fld_fields_get(0,0,$m['id'],0,0,$_GET['ord'],null,array(),false,array(),null,CLS_ORDER);
				$last_cat = '';
				while( $f = ria_mysql_fetch_array($ord_fields) ){
					$ar_fld_exclude[] = $f['id'];

					if( $f['cat_name']!=$last_cat ){
						print '
							<tr>
								<th colspan="2">'.htmlspecialchars($f['cat_name']).'</th>
							</tr>';
						$last_cat = $f['cat_name'];
					}
					print '	<tr>
								<td class="th-150"><label for="fld'.$f['id'].'" '.( $f['is-sync'] ? 'title="'._('Ce champ est synchronisé avec votre gestion commerciale.').'"' : '' ).' >'.htmlspecialchars( $f['name'] ).' :</label>';
					/*if( ($f['type_id']==5 || $f['type_id']==6) && !$f['is-sync'] ){
						print '<sub><a href="#" onclick="return fldFieldModifyValues('.$f['id'].')">Editer la liste</a></sub>';
					}*/
					print '		</td>
								<td>';
					print fld_fields_edit( $f );
					print '		</td>
							</tr>';
				}

			}
		}else{
			// Affiche le message d'information uniquement si des modèles peuvent être ajoutés
			if( ria_mysql_num_rows($models)>ria_mysql_num_rows($ord_models) ){
				print '<tr><td colspan="2" class="multi-colspan">';
				print _('Aucun modèle de saisie n\'a été associé à cette commande. Avant de pouvoir continuer, veuillez sélectionner un modèle ci-dessous.');
				print '</td></tr>';
			}
		}

		// Affiche la liste des champs orphelins (n'étant rattachés à aucun modèle affecté à la commande)
		// $fields = fld_fields_get_orphans($_GET['ord'],CLS_ORDER);
		if( $fields && ria_mysql_num_rows($fields) ){
			ria_mysql_data_seek($fields, 0);

			ob_start();

			$last_cat = '';
			while ($f = ria_mysql_fetch_array($fields)) {
				if (in_array($f['id'], $ar_fld_exclude)) {
					continue;
				}

				if ($f['cat_name'] != $last_cat) {
					print '<tr><th colspan="2">' . htmlspecialchars($f['cat_name']) . '</th></tr>';
					$last_cat = $f['cat_name'];
				}

				print '<tr><td class="th-150"><label for="fld' . $f['id'] . '" ' . ($f['is-sync'] ? 'title="'._('Ce champ est synchronisé avec votre gestion commerciale.').'"' : '') . '>' . $f['name'] . ' :</label></td><td>';
				print fld_fields_edit($f);
				print '</td></tr>';
			}

			$orphans = ob_get_clean();

			if (trim($orphans)) {
				print '<tr><th colspan="2" class="tfoot-grey color-red">';
				print _('Champs orphelins');
				print '</th></tr>';
				print $orphans;
			}
		}

		?>
	</tbody>
	<tfoot class="print-none">
		<?php if( !$ord_viewed['piece'] ){ ?>
		<tr>
			<td colspan="2" class="multi-colspan">
				<input type="submit" name="savefields" value="<?php print _('Enregistrer')?>" title="<?php print _('Enregistrer les modifications'); ?>" <?php print $ctr_validation ? 'disabled="disabled"' : ''; ?> />
				<input type="submit" name="cancel" value="<?php print _('Annuler')?>" title="<?php print _('Annuler les modifications'); ?>" <?php print $ctr_validation ? 'disabled="disabled"' : ''; ?> />
			</td>
		</tr>
		<?php }

			// Construit la liste des modèles déjà appliqués à cette commande
			$exclude = array();
			while( $m = ria_mysql_fetch_array($ord_models) ){
				$exclude[] = $m['id'];
			}

			// Si des modèles supplémentaires peuvent être ajoutés, affiche l'interface correspondante
			if( ria_mysql_num_rows($models)>ria_mysql_num_rows($ord_models) ){
				print '<tr><td colspan="2" class="tfoot-grey multi-colspan">';
				// Affiche la liste des modèles pouvant être ajoutés à cette commande
				print '
					<label for="model-pick">'._('Modèle de saisie :').'</label>
					<select name="model-pick" id="model-pick">
				';
				while( $m = ria_mysql_fetch_array($models) )
					if( array_search( $m['id'], $exclude )===false ){
						print '<option value="'.$m['id'].'">'.htmlspecialchars( $m['name'] ).'</option>';
					}
				print '
					</select>
					<input type="submit" name="addmdl" class="button" value="'._('Ajouter').'" '.( $ctr_validation ? 'disabled="disabled"' : '' ).' />
				';
				print '</td></tr>';
			}
		?>
	</tfoot>
</table>
<?php
	}
?>

<input type="hidden" id="ord-id" value=<?php print htmlspecialchars($ord['id']); ?> />
<?php

if( isset($config['active_popup_products_invoice']) && $config['active_popup_products_invoice'] ){ ?>
	<input type="hidden" id="popup-invoice" value="1" /><?php
}

?>

</form>
<script>
	var signRateOptions = <?php print json_encode($sign_rate_options); ?>,
		signDateOptions = <?php print json_encode($sign_date_options); ?>;

	$('#attach').click(function(e){
		if( !$.trim($('#pmt-code').val()) ){
			e.preventDefault();
		}
	});

		<?php if (gu_user_is_authorized('_RGH_ADMIN_ORDER_POPUP_DELIVERY')) { ?>
			var delivery_states = <?php print json_encode($delivery_states) ?>;
			var delivery_previous_states = <?php print json_encode($delivery_previous_states) ?>;
			$('#save-newstate').click(function(event){
				var newstate = ''+$('#newstate').val();

				if (-1 == delivery_states.indexOf(newstate)) {
					return;
				}
				event.preventDefault();

				displayPopup(<?php print json_encode(_('Livraison')) ?>, '', <?php print json_encode('/admin/orders/popup-delivery.php?ord=' . urlencode($ord['id'])) ?> + '&newstate=' + encodeURIComponent(newstate));
			})
		<?php } ?>
</script>
<?php

require_once('admin/skin/footer.inc.php');
